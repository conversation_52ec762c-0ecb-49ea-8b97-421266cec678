��         9              �q     �q     r     #r     ?r     Wr     or     �r     �r     �r     �r     �r     s     #s     ;s     Ts     ms     �s     �s     �s     �s     �s     �s     t     -t     Et     \t     xt     �t  "   �t     �t      �t     u  '   2u  ,   Zu  (   �u  $   �u  (   �u  -   �u  '   ,v  )   Tv  $   ~v  $   �v  $   �v  $   �v  )   w     <w  %   [w  "   �w  %   �w  !   �w  !   �w  $   x     3x  !   Qx  "   sx      �x     �x  #   �x     �x     y  "   9y     \y  #   zy  #   �y  #   �y      �y     z     $z     Az     ^z     {z  #   �z     �z  #   �z     �z  #   {     A{  #   _{     �{     �{     �{     �{     �{     |     6|     V|     l|     �|     �|     �|     �|     �|     }     7}     T}     q}      �}      �}      �}     �}     
~     %~     @~     [~     v~     �~     �~     �~     �~               7     L     a     v     �     �      �      �  (   �  0   %�  .   V�  .   ��  -   ��  +   �  +   �  +   :�  +   f�  +   ��  +   ��  +   �  )   �  *   @�  )   k�  )   ��  )   ��  )   �  )   �  )   =�  )   g�  )   ��  +   ��  ,   �  ,   �  ,   A�  )   n�  )   ��  )     *   �  ,   �  2   D�  7   w�  :   ��  4   �  6   �  -   V�  6   ��  2   ��  1   �  1    �  .   R�  4   ��  @   ��  ;   ��  =   3�  >   q�  =   ��  ;   �  4   *�  ,   _�  /   ��  #   ��  #   ��  '   �  '   ,�  '   T�  #   |�  #   ��  #   Ċ  "   �     �  #   )�  #   M�  #   q�     ��  "   ��  "   ҋ  "   ��  "   �     ;�     [�     {�  "   ��  (   ��  (   �  (   �  (   9�  (   b�     ��     ��     ō  !   �  '   �  '   +�  '   S�  '   {�  '   ��  '   ˎ  &   �  ,   �  ,   G�  ,   t�  #   ��  )   ŏ  )   �  )   �  )   C�  )   m�     ��     ��  $   Ԑ  $   ��  #   �  )   B�  )   l�  )   ��  )   ��  )   �  )   �  )   >�  )   h�     ��     ��  #   ϒ  #   �  #   �  #   ;�  "   _�     ��     ��     ��     ߓ     ��     �     4�  !   P�  !   r�     ��     ��     Δ  &   �  ,   �  ,   =�     j�     ��  *   ��  -   Ε  0   ��  +   -�  2   Y�  .   ��  6   ��  6   �  <   )�  -   f�  ,   ��  6   ��  1   ��  .   *�  +   Y�  3   ��  %   ��  %   ߘ  %   �  %   +�  %   Q�  %   w�  #   ��     ��  -   ��  /   �  -   >�  $   l�  $   ��  $   ��  $   ۚ  $    �     %�     D�  $   a�  *   ��  *   ��  *   ܛ  *   �  *   2�  *   ]�  "   ��  "   ��  "   Μ  "   �  "   �     7�     R�  %   r�  %   ��  %   ��  %   �  %   
�  %   0�     V�     q�     ��  %   ��  %   ў  %   ��  %   �  %   C�  "   i�  (   ��  (   ��     ޟ     ��     �     4�     O�     j�     ��     ��     ��  "   נ  "   ��  "   �  "   @�  "   c�  "   ��     ��  !   š  !   �  !   	�  '   +�  '   S�  '   {�  '   ��  '   ˢ  '   �     �     5�     K�     a�     w�     ��     ��     ��     ң      �     
�     )�     E�     a�     }�     ��     ��     Ѥ     �     �     �     ;�     W�     s�     ��     ��     å     إ     �     �     2�     R�  !   r�  '   ��  '   ��  '   �  '   �  '   4�  '   \�  '   ��     ��  !   ȧ  !   �  !   �  &   .�  &   U�  (   |�  $   ��  #   ʨ     �  #   �  "   0�      S�  &   t�  #   ��  "   ��  #   �  #   �  #   *�  #   N�  #   r�  #   ��  #   ��     ު     ��     �     2�     N�     j�     ��  #   ��  #   ȫ  #   �  #   �  #   4�     X�  &   t�  #   ��  !   ��  !   �  !   �  !   %�  "   G�     j�     ��     ��     ��     ȭ     ڭ     �     ��     �     #�     =�     W�     j�     {�     ��     ��     ��     ڮ     ��     �     -�     D�     [�     r�     ��     ��     ��     ί     �     �     �     =�      ]�     ~�     ��     ��     ��     ۰     ��     �     5�     P�     g�     �     ��     ��     Ǳ     ߱     ��     �     '�     ?�     V�     m�     ��     ��     ��     ɲ     �     ��     �     $�  !   =�     _�      y�     ��     ��     ѳ     �     �     "�     =�     X�     s�     ��     ��     Ĵ     ޴     ��     �  '   1�  $   Y�  +   ~�  9   ��  -   �  (   �  .   ;�     j�  '   v�  0   ��  0   ϶  0    �  0   1�  0   b�  0   ��     ķ     ط     �     ��     �     �  1   (�     Z�  ,   z�  '   ��  *   ϸ  7   ��  #   2�     V�  )   v�  *   ��  ,   ˹  /   ��  ,   (�  /   U�  (   ��  -   ��  %   ܺ  2   �  6   5�  +   l�  ,   ��  /   Ż  .   ��  6   $�  -   [�  ,   ��  )   ��  %   �  (   �  '   /�  '   W�  $   �  $   ��  #   ɽ  1   �  '   �  0   G�     x�     ��     ��     Ҿ  !   �  %   �  ,   8�  #   e�  $   ��  $   ��  '   ӿ  #   ��      �  #   @�  $   d�  %   ��  "   ��  3   ��  "   �  9   )�  #   c�  $   ��  &   ��  "   ��      ��  !   �  $   9�  4   ^�  +   ��  %   ��  %   ��  "   �  $   .�     S�     m�  $   ��  2   ��  +   ��  /   �  '   6�  $   ^�  #   ��  "   ��     ��      ��  "   ��      "�     C�  +   a�     ��      ��  (   ��      ��      �      8�  #   Y�  +   }�  *   ��  )   ��  .   ��  0   -�  8   ^�  '   ��  6   ��  1   ��  "   (�  .   K�      z�  #   ��  )   ��     ��     �      (�      I�  !   j�  %   ��     ��     ��      ��     �  (   -�     V�     r�  (   ��      ��  &   ��  $    �  &   %�     L�      k�      ��  !   ��  $   ��  $   ��     �     5�  $   U�  !   z�     ��  "   ��      ��      ��  $    �  $   E�  '   j�  #   ��  "   ��  %   ��  !   ��      !�     B�     \�     y�     ��     ��     ��     ��     ��     �     &�     =�     S�     i�     �     ��     ��     ��  !   ��  $   ��  %   �  %   >�  5   d�  *   ��  *   ��  &   ��  )   �  %   A�  ,   g�  &   ��  *   ��  -   ��  -   �     B�  &   _�  '   ��  .   ��  ,   ��  ,   
�  %   7�  (   ]�  '   ��  '   ��  (   ��  (   ��  (   (�     Q�     o�     ��     ��     ��     ��     ��     �     �     :�     Q�     g�     ~�     ��     ��     ��     ��     ��  @   ��     ?�     X�  6   r�     ��     ��     ��     ��     �     %�  )   B�     l�     ��     ��     ��  &   ��  !   ��  )   !�     K�     i�     ��     ��     ��     ��      ��     �     #�     C�  !   ]�     �     ��     ��     ��      ��     �     +�      I�     j�     �  &   ��      ��     ��  #   ��     �  !   4�  !   V�  "   x�  (   ��     ��      ��  #   �     (�     F�      f�      ��  #   ��  !   ��     ��  &   �     /�     L�     j�     ��     ��     ��     ��     ��     �     "�  '   <�  &   d�  &   ��  #   ��     ��     ��  #   �  #   :�  #   ^�  #   ��  $   ��  +   ��  '   ��  $   �  $   D�  $   i�  &   ��  0   ��  5   ��  .   �  *   K�  -   v�  $   ��  $   ��  #   ��  ,   �  (   ?�     h�  %   ��  )   ��  *   ��  *   �     ,�  *   L�     w�  !   ��     ��  +   ��  )   �  ,   +�  0   X�  (   ��  $   ��  "   ��  %   ��  ,    �  '   M�  +   u�  (   ��  &   ��  +   ��  &   �  &   D�     k�  '   ��  %   ��  $   ��  $   ��  (   !�  "   J�     m�  )   ��  )   ��  )   ��  !   	�     +�  +   G�  *   s�  -   ��  &   ��  "   ��  $   �     ;�     X�     u�     ��     ��      ��  !   ��  !   �  !   1�  !   S�  ,   u�  ,   ��  "   ��      ��  !   �  #   5�     Y�     t�     ��  $   ��  $   ��  $   ��  "   �  "   B�  "   e�  %   ��  %   ��  %   ��  "   ��     �     =�  "   ]�  "   ��  "   ��  )   ��  &   ��  &   �  )   >�  &   h�  &   ��  )   ��  %   ��  '   �  #   .�  #   R�  &   v�  &   ��  #   ��  0   ��  (   �  (   B�  -   k�     ��  $   ��  $   ��  $   ��      �  "   =�  "   `�      ��     ��  #   ��     ��  '   �  '   .�  %   V�  '   |�  (   ��  )   ��  &   ��  0   �  %   O�  !   u�  &   ��  &   ��  $   ��  "   
�  "   -�  "   P�  (   s�  !   ��  *   ��  -   ��  "   �  &   :�      a�  +   ��  )   ��  &   ��  "   ��  !   "�  *   D�  #   o�  (   ��  %   ��     ��  )   �  (   ,�  #   U�  !   y�     ��  '   ��  &   ��     
�  (   %�  "   N�  #   q�  )   ��     ��     ��  -   ��  *   *�     U�      t�      ��  *   ��  0   ��  $   �  *   7�  0   b�  1   ��  @   ��  3   �  3   :�  3   n�  3   ��  3   ��  3   
�  ,   >�  <   k�  5   ��  3   ��  3   �  3   F�  3   z�  3   ��  ;   ��  ;   �  6   Z�  ;   ��  8   ��  ,   �  #   3�     W�     u�     ��     ��  #   ��      ��  "        3     N     g  #   �     �     �     �     �         )    < #   M )   q    �    �    �    �             5    O    j    � %   � +   � *   � +    *   K +   v *   � +   � )   �    #    A &   `    � #   �    � #   �            0 #   F #   j    � +   � 4   � "    *   (    S    p    �    �     �    � )   � )    "   > )   a "   � %   � %   � )   � #   $    H #   h (   � )   � /   � "   	 &   2	 "   Y	 (   |	 %   �	    �	 #   �	 3   
 "   C
 !   f
    �
 "   �
 #   �
 '   �
 )    %   A &   g 2   � 2   � 2   � 3   ' 2   [ 2   � 3   � #   � #   
    =
     Z
 %   {
 $   �
 %   �
 ,   �
 /       I     i 0   � 2   � )   � #    %   < $   b    � %   � "   �     � %    "   7 "   Z %   } &   � $   � !   �        )    C $   b     � *   �    �    �    	    )    A    Y    t    �    �    � %   � $   � %   $ $   J %   o $   � %   �    �    �        9    T    o    �    �    �    � &   �         3 &   P    w    � "   � "   �    � &    (   9 (   b (   � (   �    �     � %    &   <     c .   � )   � -   � -    -   9 -   g +   � ,   � (   �     !   7 )   Y "   �    � !   �    � "   �         ;    \    y #   �    �    �    � !    "   . *   Q '   | #   � (   � #   �        /    G    d -   � +   �    �    �        .    J    f "   � -   � )   � *   �    ( "   C $   f &   � '   � $   � "   � '   " "   J "   m !   �    � $   � $   � "     '   ?     g  +   �  *   �  $   �  (   ! %   ,! (   R! (   {! '   �! &   �! &   �! )   "     D"    e"    ~"    �"    �" &   �"    �"     # !   6#    X# )   t# $   �#    �#    �#    �#    $     $ "   ?$ "   b$     �$    �$    �$     �$ .   �$ *   )%    T% "   l% !   �%    �%    �%    �%    &    &    3&    I&    \&    p&    �&    �&    �&    �&    �&    �&    �&    '    /'    G'    _'    s'    �'    �'    �'    �'    �'     (    (    -(    A(    W(    m(    �(    �(    �( !   �(    �(    )    +)    D)    a)    ~)    �)    �)    �)    �)    �)    *    (*    <*    P*    d*    x*    �*    �*    �*    �* .   �* &   	+ &   0+ !   W+    y+    �+    �+    �+    �+    �+    �+    ,    ),    >,    S,    h,    },    �,    �,    �,    �,    �,    -    !-    A-    a-    y-    �-    �-    �-    �-    �-     .    5.    Q.    g.    }.    �.    �.     �.    �.    �.    /    7/ $   T/    y/    �/    �/    �/    �/     0    0    /0    E0 !   ]0    0    �0    �0    �0    �0    1    ,1    B1    Y1    p1    �1    �1    �1    �1 &   �1 #   �1    "2    ?2    ^2    }2    �2    �2    �2    �2    �2    3    *3    F3    b3    v3    �3    �3    �3    �3    �3    �3    �3    4 $   44     Y4 "   z4 !   �4 "   �4    �4 $   5    &5    C5    \5    {5    �5    �5    �5     �5    6 $   56 '   Z6 !   �6 #   �6     �6     �6 &   
7 !   17 '   S7    {7 "   �7 *   �7 %   �7 %   8 (   28 9   [8 2   �8 +   �8 &   �8 &   9    B9 "   b9 (   �9    �9    �9    �9     : )   %: -   O:    }:     �:    �: )   �:    ; &   #; !   J; (   l; $   �;    �; $   �; $   �; %   !< #   G< &   k<     �< )   �<    �<    �< #   =    <=    X= "   u= "   �= (   �=    �=    > %   !> '   G> -   o>    �> &   �>    �>    �> $   ?    A?    a?    {? $   �?    �?    �?    �?    @    "@    5@    L@    l@    @     �@    �@    �@    �@    A    ,A !   KA    mA    �A    �A    �A    �A    �A !   B !   (B !   JB    lB    �B    �B #   �B    �B    	C    (C %   @C "   fC !   �C    �C    �C    �C    D    D    >D    ^D    }D    �D    �D    �D    �D    �D    E    'E    EE    aE %   E    �E    �E    �E #   �E     F    <F    UF    uF    �F    �F    �F    �F    �F    G    $G    ;G    SG    mG    �G    �G    �G    �G    �G    H    .H    MH    lH    �H    �H    �H    �H    �H    I    *I    FI    bI "   ~I "   �I $   �I $   �I &   J &   5J &   \J $   �J "   �J "   �J $   �J "   K    6K $   RK .   wK .   �K    �K    �K "   
L "   0L "   SL "   vL "   �L    �L "   �L     �L    M    3M '   NM    vM    �M    �M    �M    �M    �M $   N -   3N &   aN    �N    �N    �N    �N    �N    
O /   -O -   ]O    �O    �O !   �O ,   �O -   P '   =P (   eP (   �P %   �P    �P    �P    Q    *Q    FQ    bQ    yQ    �Q    �Q    �Q    �Q    �Q    �Q    R    !R    5R    OR    hR    �R    �R !   �R )   �R )   �R    "S !   >S "   `S "   �S     �S !   �S #   �S #   
T #   1T #   UT )   yT "   �T )   �T -   �T 1   U "   PU "   sU $   �U !   �U "   �U )    V "   *V    MV "   lV !   �V    �V    �V    �V    W    "W    3W &   OW    vW (   �W -   �W    �W !   X     $X !   EX !   gX !   �X $   �X    �X &   �X !   Y &   4Y '   [Y &   �Y    �Y !   �Y %   �Y    Z     'Z *   HZ $   sZ "   �Z #   �Z #   �Z    [ %   ![    G[    c[     v[    �[    �[    �[    �[    �[    \     0\    Q\ '   h\    �\    �\    �\    �\    �\    	]    ']    E]    c] *   �] (   �] (   �] (   �] (   '^ (   P^ (   y^ (   �^ '   �^ &   �^ %   _ %   @_ 8   f_ -   �_ (   �_ #   �_ &   ` !   A` "   c` 2   �` 4   �` 4   �` )   #a ?   Ma 7   �a 1   �a +   �a &   #b +   Jb &   vb    �b �  �b   Ed "  Gh 5   jj �  �j `  �l   �o ]  �r   Iu �   Zw �   �w O   �x    �x �  [y 7  J| �  �~ F  !� �  h� e  � �  �� �  |� �  � s  �� �  � �   � �  o� �   �� ,  �� &   �� d  ٜ O   >� @   �� #   Ϡ ,   � X    � "   y�    �� [   �� =   � :   U�    �� d   �� �  � �  �� �   ˦ U   f�    ��    Χ -   ڧ    � @   �    U� 	   g�    q�    �� @   ��    ը ,   ۨ 	   � 
   � 0    � 9   Q� #   �� �  �� U  ;� �   �� 5   �� I  ��   
� �  � K  �� &   @� �   g� &   \� ;  �� +   ��   � &   �� �  � r   �� �   �    �� J  �� '  &� �  N� �  �    �� �   � �   �� D  .� Q   s� J   �� �   � o   �� }   f� F   ��    +� �   2� �  � ]    � �   ^� \  %� }   �� �   � g   �� �  6� �   �� $  �� �   �� p  j�   �� O   �� U  B� H  �� �   �� a   |� �   ��    ��   ��   �� O   ��    =� .  R� �  ��     � *  -� �   X� 1  <� ;  n� �   �� D  �� �  ��    �� �   '� �   �� �   N� �   
� �   �� N   \� �   �� �   `� k   �    r� �   �� �   ]� 5  ��    ,     M �  g �  1 �  � �  I �   G
 �     � �   � �   �   � �  �   � �  �    ] �   r >  m   � �  � �   � �   R �   C �  5 �   
! �  �! �  �# 9   �% �   �% �   �& �   w' h  #( *  �* �   �+ �   u, 2   \- �  �- F  61 �  }2 +   5 �   15   �5 �  �6 �  p8 |  : �  �; �  E> G   �@ g  !A f  �C �  �G n  �I   SN �  rP �   BR �   �R    �S �  �S   ?U |  FW �  �Y �  �[   d]    m^ v  �^ =  a �  ?c    �d ;  �d   f y  #h �  �i g  %m �   �n "   #o ~  Fo i  �q <   /t u  lt �  �u ]  �w `  
{ �  k| �   �~ �   � �  � ;   ��    �   
� �  � �  ߅ S   ˇ O   � ?  o�    �� �   �� �   �� �  
� 4   �� (   � �  � e  �� �   � �   ٕ �   s� <   � �  R� �  �    ��    �� >   Κ g   
� ]   u� j   ӛ �   >� �   ܜ '   ͝ W   �� �   M� �   Ҟ C   j� X   �� Y   � �   a� 
   =� '   H� �  p�   5� n  P� �  ��   a� f  h� 6  Ϫ "   � }  )�   ��   �� #  ϰ �  � �  �� �  %� �  � O   ھ s   *� J   �� <  � M  &� �  t� 6  2� B  i� �  �� �  s� �  �� F   �� �  ��   �� `   � !   a� �  �� �  �   �� ;  �� �  ��    �� Q   �� �   �    �� �  �� w  ��   � #  � o  5� !   �� �  �� }  S� O   �� �   !� �  �� �  d�   <� �  O� �   �� �  �� .   ]� {  ��     +  ) s  U i  �
   3    I �  ^ �  � "   � C   I  U   � �  � �  �!   ]# O   `% �  �% �  [( �   �) v   �* �   #+    �+ R   �+ -   4, @   b, 9   �, #   �, #  - C  %.   i2 �  {5 a  9    z: B  �: �  �? �  �C �  WF   2I *  ?M    jQ �  Q    KS    ZS �  xS �  kV �  /Z $  �] !   �a b  b   ve F  �h �  �k   ~o    �t �  �x 4   X{ a  �{ �   �| �  �} �  6� %  � �   <�   7� -  M� #   {� 9  �� o   ً }   I� 3   ǌ T   �� {   P�   ̍ �  ܑ �  Ô _   p� �  Й '  �    �� C  �� Y   �� �   V� o  =� b   �� �   � %   �� ,  �   � #  � �  ?� �   �    �� �   � �  ò    �� �   �� q  �� 
  � \   ,� N   ��   ػ 5   ��   -� �  3� H  � <   W� �   �� �   � �  L� �  ��    �� �  �� <   �� 1  �� )  �� �  � |  �� |   X� 5   �� �  � _   �� =  � "  O� �  r� �   � �   �� n   S� &  �� e  �� �  O�   ��   �� \   � �  d� �  ��   �� 3  �� 7  �� �  6� 5   �� �    >   	 �  H �   % %   � �  � �  x
 �   �  � 	  � �  � R  r   �   � �  �  �   �# �  �$   �) U   �+ �   �+ �   �, "   �- 
   �- �  �-   �/ a   �0 �  &1 #  �2 �  6 ]  �7 �  ; =  �= �  �>   �A     �C Q  �C    *E �  3E Q   �G ;   .H    jH    pH F   �H    �H -   �H %   I 
   AI    LI    cI    tI    �I    �I )   �I    �I <   �I 
   J    (J    9J    FJ 9  WJ �   �K I   L    gL H   �L A   �L W   M `   iM K   �M    N    +N %   8N )   ^N    �N    �N    �N    �N 
   �N     O ]   O ;   qO !   �O O   �O S   P R   sP @   �P    Q 9   !Q b   [Q '   �Q �   �Q �   �R 
   S �   S �   �S    JT    fT $   zT ,   �T %   �T 5   �T %   (U    NU    nU    vU 6   �U    �U    �U 	   �U 
   �U    V 9   V   EV    WW 	   `W #   jW 	   �W M   �W    �W    �W 	   �W �   �W �   �X 0   DY �   uY    Z    >Z    \Z    dZ i   {Z    �Z [   �Z 
   S[ 
   ^[ >   i[    �[ /   �[    �[ /   �[    &\ (   7\ N   `\ �   �\   �] �   �^ 4  _ �  G` �   �b    c 4   �c !   �c    �c    �c    d    )d L   <d ~   �d y   e �   �e 6   f n   Cf I   �f �   �f �   �g X  �h �   �i e   tj G   �j    "k    Ak    Nk    ak    tk $   �k    �k *   �k    �k    �k !   l    &l    -l )   Ml    wl !   �l    �l    �l 
   �l    �l    �l    
m -   &m &   Tm �  {m    o 	   	o w   o H   �o s   �o ^   Hp x   �p >    q Q   _q O   �q �   r �   �r    s �   s 5   �s    �s ?   t �   Yt ,   u    8u �   Uu L   +v �  xv �   B{ �   �{   �| O  �} Q    �  d �   B�   � U  5� $   ��    �� w   �� v   9� �   �� o   4� �   �� Y   S� �   �� /   \� 2   ��    �� 3   ҉ �   � 9   ��    Њ    ݊    �� "   � �   &�    Ջ \   � ~   D� .   Ì !   � h   � �   }� ^  � #   t� &   �� ?  ��    ��    �    � "   ,�    O� 9   X� �  �� +   �� 
   �� *   Ŕ    �   � 4   � 	   F�    P� �   _�    �    � R   �� ,   P�    }� 1   �� �   �� 3   ��    ߙ �   �� ,   ��    ��    ݚ 0   � X   � �   v� "   0� U   S� ,  �� �   ֝ }   ��    � �  .� �   � %   k� [   �� %   � 1  � 1  E� .   w� w   ��   � <  +� 8   h� ?   ��    � �  �� J   �� 9   � .   � J   J� F   �� 	  ܫ +   � "   � P   5� ]   ��    � "   � 5   � H   L� K  �� e  � �  G� g   ڷ \   B�    �� +   �� �  ظ `  Ǻ <  (� 7   e� |  �� �  � =   �� O   �� �  ;� �  ��    ��    �� C   ��    � �  )� 4   )� �  ^� �  1� �  ��    b� �   �� \   p� @   �� �   � �  �� �  �� '  O� w  w� �   �� �  �� �   �� J  5� �   �� �   y� 3  �� j   /� _   ��     ��    �    .� B   N� F   �� �   �� Y   r� 	   �� M   �� �  $� U   �� �  H� �   
� �   �� _   �� P   � /   i�    �� U   �� T   � c   a� 5   �� �  �� 9   �� F  ��    � B  1� �   t� �   "� �   � n  �� :   M� �   �� 	   [�   e� �   �� 1   Z� �  ��    @�    M� !   g� 6   �� ;   ��    ��    	�    � /  �� �  ��    X     g  ,   p    �  �  �   � �   �    �    � $   � "   �        "    @ `   H A   � 5  � �   !
 3   �
 �    �   � y   c �   � i   �
 }   �
    y �   � s   : �   � h   r w   � !   S ^  u �   � Y   � �   � �   � b   3    � \   � �    �   � J   w �   �    � �  � �   � �    �   �    | 7  � i  � &   '! .   N!    }!    �! �  �! C   �# ~   �# A  W$ �   �% �   �&    �' �   �( �   �) *  T* �   + �  , �   �- �  d. ,  �0 �  2 
   �3 
   �3 
   �3 �   �3 
   �4 
   �4 ~   �4 �   ;5 X  �5 
   $7 �   27 u   �7 �   Y8 c   9 �   k9    /: 
   �: �   �: 
   �; 
   �; 
   �; ?  �; �   
?   �?     C    "C    1C   HC .   cD H   �D �  �D �   `F C   RG    �G .   �G a   �G X   7H Z   �H '   �H '   I t  ;I 9   �J �  �J �  �M �   xO    gP   �P �  �Q P  �S �  �V t  �Z �   ;\ �   ,]    ^ \  (^ 0   �` w  �` z  .c �  �d �   Bf +  g   7i �   Oj �   8k D   l a   Ql    �l U   �l �   %m    �m #   �m i  �m    So �   fo E   p    Zp    gp    �p &   �p �   �p �   q q   r �  �r N   Dt    �t    �t 3   �t H   �t    3u    Eu    Uu    bu    ru    �u    �u    �u V   �u �   v    �v    �v    �v    �v    �v    w    &w    :w    Ow    mw 5   �w �   �w    zx R   �x �   �x �   �y [  yz D  �{ �  }   �~ �  � >   �� �   � Q   Ƃ m   � q   �� $  �� h  � �   �� �   
�    �� �   Ƈ �   ��   Z�    b�    o� %   � (   ��    Ί 9   �    "� 2   /�    b� *   ~� ,   �� =   ֋    �    2�    N�    f�    m� X   ��    ތ '   � V   � T   e� ;   �� �   �� �   �� :   q� 4   �� �   � �   �� �   � L   �� J   �� P   @� E   �� f   ג B   >� Z  �� �  ܔ �  ��    -� [   M� �   �� B   m� �  �� l  [� �   Ȝ u   �� ,   �   J�   Z� �   s� >   %� �   d� W  �    w� D   ��    ڣ Z   �� �   T� �   �    �    �    �� :   �� �   :� �   %� 4   �� R   � 6   9� (   p� &   �� �   �� /   D� �   t� Z   � �  c� �   � �   �� f  B� E   �� (   � %   � (   >� '   g� (   �� '   �� (   � j   	� u   t� �  � m   ٴ E   G� W   �� O   �    5� �   D� �   �� �  ط N  `� 
   �� �   �� F   �� *   � c  � -   z� I   �� �   � Y   t� �   ξ �   v� �   	� +   �� &   ��   �� I   �� 9   ?� |   y� �  �� n   �� �   � �   �� �   �� �   A� D   � a   ]� �   �� "   �� �   �� >   [� L  �� E   �� c   -� &   �� @   �� �  �� w   �� j   "� �   �� �   s� �   "� �   �� �   �� 
   }�    �� �   �� l   9� *   �� 3   ��    � "   �    ;� 3   T� �   �� v   8� *   �� >   �� K   � G   e� Y   �� �   � e   �� �   @� �   /� K   �� �   %� �  �� :   �� l   ��    *� p   F� [   �� !   � �   5� �   �� 7   �� {   �    �� :   �� l   ��     E� �   f� 9   0�    j� I   �� �   �� \  g�    ��    ��     � e   �    �� �   �� d   6�   �� �   �� 6   �� a   �� 9  7� %   q�   �� g   �� @   � {   U� �  �� 
   k�    y�    {� 5   }� S   �� h   � {   p� ;   ��    (�    E� 4   T� w   �� u   � 
   w� {   �� W   �     Y� 9   z� R   �� (   � !   0� �   R� �  �� u   ��    �    ,� �   @� ;  5� �   q�    
� y  � �  �� \   h  �   �  1   � �  � ?   � �   � $   l #   �   � )   �	 �   �	 �   �
 �   Q �    4   �    � X   [
 "   �
 �   �
 Y   o H   � k    H   ~ y   � �   A R  8 M   � 7  � i   h  { N   � <  3 �  p C    =  D �  � L   
! 0   W! ?  �! K   �" c  # �   x& d   &'    �' 0   �' �  �'    �* y  �* �  U, �  N.   �/ }   �1    q2 �   �2 a   �3 O    4 �   P4 �   �4    �5 )   �5 L   �5 ]   6 +   r6 j   �6 �   	7 C   �7    �7 �   �7 p  �8   (: !   1;    S;    r; |   �; |   �; �   {< 6  �< �   3> $   �> =   
? w  K?    �@ �   �@ ;  �A �  �E n   hG 7   �G u   H    �H �   �H X   8I N   �I �   �I �  �J �   TL    �L    M ,   1M &   ^M �   �M    N R   +N    ~N :   �N G   �N !   O    0O �   GO    �O '   P �   *P 8   �P    �P �   Q `  �Q �    S �   �S �   �T s  *U    �V +   �V ;  �V @  X P  [Y    �Z �   �Z �   �[ x   
\ �   �\ �   @] �   �] !   r^ �   �^ #   _ )   @_ '   j_ -   �_ -   �_ '   �_ )   ` #   @`    d`    �` K   �` G   �` G   &a 8   na R   �a C   �a E   >b -   �b    �b c   �b 5   3c    ic s   zc V   �c �   Ed L   e    Te     je 0   �e '   �e !   �e h   f L   of �   �f 6  Ig $   �h $   �h >   �h    	i [   $i 	   �i "   �i &   �i �   �i    �j #   �j    k 
   k 	   $k k   .k /   �k �  �k !   gm 3   �m .   �m )   �m ;   n "   Rn @   un <   �n F   �n >   :o K   yo    �o �   �o Q   �p    �p t   �p �   pq �   �q 8   vr >   �r A   �r �   0s �   t �   �t    7u )   Du 	   nu 
   xu #   �u .   �u }  �u �   Wx *   8y    cy @   gy +   �y /   �y    z 	   	z �   z r   �z U  8{ .   �| /   �| �   �| /   �} 0   �} .   ~ 1   N~ D   �~ 6   �~    �~     U   3 -   � C   � \   �   X� s   h� m   ܂ �   J� :   Ѓ �   � ;    �     <� {   ]� &   م @    � %   A� {   g� s   � ?   W� '  �� !   �� !   � 3   � &   7�    ^�     }� >   ��    ݉     �� �   � |  �� 1   ,� 9   ^� �  �� !  � ;   ?� (   {�    ��    �� %   Ǐ    �     �    �    .�    @�    S�    b� 
   s�    ��    ��    ��    ��    �� O   ː }  � �  �� t  #� !  �� �  �� �  �� C  =�    �� �  �� .   l� �  �� j  ;� �  �� &  v� m  �� G   � #  S� w   w� ,   � l   �    �� ~   	� �   �� 7  M�    �� s  �� �  �� "   �� I  � e  f� �   ̺   �� "   �� v  �� �  -� c  �� ]  I�    �� �   �� �   m� �  '� �   �� �   �� �   d�    ?� 8   V� )   �� m   �� _   '�    �� �   �� h   j� �   �� 9   �� $  ��   �    #� �   �� `   (� �   �� �   � 6   �� �   �� �   �� �   O� �   ,�    )� :   6� H   q� �   �� M   L�    ��    �� }   �� %   >� (   d�    �� a   �� L   �� l   I� (   �� D   ��    $� d   3� 	   �� �   �� �   +� �   �� e   o� ;   �� G   � R   Y� (   ��    ��    ��    ��    
�    �    *�    9�    M�    V�    n�    ��    ��    ��    �� (   ��    �    � 
   .�    9� 6   J� 5   �� .   �� �   ��    �� �   �� 3  U�    ��    ��    ��    ��    ��    ��    �� .  � �   3� \   �� "   ,� '   O� �   w�    3� �   Q� �   -� <   #�    `� �   }� D  p� �   �� I  J� �   �� 7   C� �   {� -   T� .   �� �   �� �   7� ;   !� �   ]� �   C� �   � {   � &   ��    �� )   ��    �� "   ��    �    '�    ?� 	  ]� �   g� �   9� B  �� �  9� r   �� f   S� �  �� s  Y� �  �� �   v  �  v 9  .    h V  w �  � 9  � �  �	 <  l �   � \  o %   � W   �   J �  ] r   � �  S    �    � #   � �    w   � �   s O   1 �   � q   $ �   � :   9 >  t l   �  4    ! �   U! q  �! -   a#  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: bg
Language-Team: bg <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library е популярна (и незаконна) библиотека. Те са взели колекцията на Library Genesis и са я направили лесно търсима. Освен това, те са станали много ефективни в привличането на нови книжни приноси, като стимулират потребителите, които допринасят, с различни предимства. В момента те не връщат тези нови книги обратно на Library Genesis. И за разлика от Library Genesis, те не правят колекцията си лесно огледална, което предотвратява широко съхранение. Това е важно за техния бизнес модел, тъй като те таксуват за достъп до тяхната колекция в големи количества (повече от 10 книги на ден). Ние не правим морални преценки относно таксуването за масов достъп до незаконна колекция от книги. Без съмнение, Z-Library е успешна в разширяването на достъпа до знания и в намирането на повече книги. Ние сме тук, за да изпълним нашата част: да осигурим дългосрочното съхранение на тази частна колекция. - Анна и екипът (<a %(reddit)s>Reddit</a>) В оригиналното издание на Огледалото на пиратската библиотека (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>), направихме огледало на Z-Library, голяма незаконна колекция от книги. Като напомняне, това е, което написахме в оригиналната публикация в блога: Тази колекция датира от средата на 2021 г. Междувременно, Z-Library расте с поразителна скорост: те са добавили около 3.8 милиона нови книги. Разбира се, има някои дубликати, но по-голямата част от тях изглежда са наистина нови книги или по-качествени сканирания на вече подадени книги. Това е в голяма степен благодарение на увеличения брой доброволни модератори в Z-Library и тяхната система за масово качване с премахване на дубликати. Бихме искали да ги поздравим за тези постижения. С удоволствие съобщаваме, че сме получили всички книги, които бяха добавени към Z-Library между последното ни огледало и август 2022 г. Също така се върнахме и събрахме някои книги, които пропуснахме първия път. Всичко на всичко, тази нова колекция е около 24TB, което е много по-голямо от предишната (7TB). Нашето огледало сега е общо 31TB. Отново, премахнахме дубликатите спрямо Library Genesis, тъй като вече има налични торенти за тази колекция. Моля, посетете Pirate Library Mirror, за да разгледате новата колекция (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>). Там има повече информация за това как са структурирани файловете и какво се е променило от последния път. Няма да го свързваме оттук, тъй като това е просто блог сайт, който не хоства никакви незаконни материали. Разбира се, споделянето също е чудесен начин да ни помогнете. Благодарим на всички, които споделят нашия предишен набор от торенти. Благодарни сме за положителния отговор и сме щастливи, че има толкова много хора, които се грижат за съхранението на знания и култура по този необичаен начин. 3x нови книги добавени към Огледалото на пиратската библиотека (+24TB, 3.8 милиона книги) Прочетете съпътстващите статии от TorrentFreak: <a %(torrentfreak)s>първа</a>, <a %(torrentfreak_2)s>втора</a> - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) съпътстващи статии от TorrentFreak: <a %(torrentfreak)s>първа</a>, <a %(torrentfreak_2)s>втора</a> Неотдавна „сенчестите библиотеки“ бяха на изчезване. Sci-Hub, огромният незаконен архив на академични статии, беше спрял да приема нови произведения поради съдебни дела. „Z-Library“, най-голямата незаконна библиотека с книги, видя как предполагаемите ѝ създатели бяха арестувани по обвинения за нарушаване на авторското право. Те невероятно успяха да избягат от ареста, но библиотеката им не е по-малко застрашена. Някои страни вече правят версия на това. TorrentFreak <a %(torrentfreak)s>съобщи</a>, че Китай и Япония са въвели AI изключения в своите закони за авторското право. Не е ясно за нас как това взаимодейства с международните договори, но със сигурност дава покритие на техните местни компании, което обяснява това, което сме виждали. Що се отнася до Архива на Анна — ще продължим нашата подземна работа, вкоренена в морално убеждение. Но най-голямото ни желание е да излезем на светло и да увеличим въздействието си законно. Моля, реформирайте авторското право. Когато Z-Library беше изправена пред закриване, аз вече бях направил резервно копие на цялата ѝ библиотека и търсех платформа, на която да я съхраня. Това беше моята мотивация да започна Архива на Анна: продължение на мисията зад тези по-ранни инициативи. Оттогава сме се разраснали до най-голямата сенчеста библиотека в света, съхраняваща над 140 милиона защитени с авторско право текстове в различни формати — книги, академични статии, списания, вестници и други. Моят екип и аз сме идеолози. Вярваме, че съхраняването и хостването на тези файлове е морално правилно. Библиотеките по света са изправени пред съкращения на финансирането и не можем да поверим наследството на човечеството на корпорации. След това дойде AI. Почти всички големи компании, изграждащи LLM, се свързаха с нас, за да обучават на нашите данни. Повечето (но не всички!) компании от САЩ преосмислиха, след като осъзнаха незаконната природа на нашата работа. За разлика от тях, китайските фирми с ентусиазъм приеха нашата колекция, очевидно необезпокоени от нейната законност. Това е забележително, като се има предвид ролята на Китай като подписал почти всички основни международни договори за авторско право. Дадохме високоскоростен достъп на около 30 компании. Повечето от тях са LLM компании, а някои са брокери на данни, които ще препродават нашата колекция. Повечето са китайски, въпреки че сме работили и с компании от САЩ, Европа, Русия, Южна Корея и Япония. DeepSeek <a %(arxiv)s>призна</a>, че по-ранна версия е била обучена на част от нашата колекция, въпреки че са мълчаливи за последния си модел (вероятно също обучен на нашите данни). Ако Западът иска да остане напред в надпреварата на LLM и в крайна сметка AGI, трябва да преразгледа позицията си относно авторското право и то скоро. Независимо дали сте съгласни с нас или не по нашия морален случай, това вече се превръща в икономически въпрос и дори въпрос на национална сигурност. Всички силови блокове изграждат изкуствени супер-учени, супер-хакери и супер-армии. Свободата на информацията се превръща в въпрос на оцеляване за тези страни — дори въпрос на национална сигурност. Нашият екип е от цял свят и нямаме конкретно подравняване. Но бихме насърчили страните със силни закони за авторското право да използват тази екзистенциална заплаха, за да ги реформират. И така, какво да се направи? Нашата първа препоръка е проста: съкратете срока на авторското право. В САЩ авторското право се предоставя за 70 години след смъртта на автора. Това е абсурдно. Можем да го приведем в съответствие с патентите, които се предоставят за 20 години след подаване. Това трябва да е повече от достатъчно време за авторите на книги, статии, музика, изкуство и други творчески произведения, за да бъдат напълно компенсирани за усилията си (включително дългосрочни проекти като филмови адаптации). След това, най-малкото, политиците трябва да включат изключения за масово съхранение и разпространение на текстове. Ако загубените приходи от индивидуални клиенти са основната грижа, разпространението на лично ниво може да остане забранено. В замяна тези, които са способни да управляват огромни хранилища — компании, обучаващи LLM, заедно с библиотеки и други архиви — ще бъдат обхванати от тези изключения. Реформата на авторското право е необходима за националната сигурност. Накратко: Китайските LLM (включително DeepSeek) са обучени на моя незаконен архив от книги и статии — най-големият в света. Западът трябва да преразгледа законите за авторското право като въпрос на национална сигурност. Моля, вижте <a %(all_isbns)s>оригиналната публикация в блога</a> за повече информация. Издадохме предизвикателство за подобрение на това. Щяхме да присъдим награда за първо място от $6,000, за второ място от $3,000 и за трето място от $1,000. Поради огромния отговор и невероятните предложения, решихме да увеличим малко наградния фонд и да присъдим четири трети места по $500 всяко. Победителите са по-долу, но не забравяйте да разгледате всички предложения <a %(annas_archive)s>тук</a> или да изтеглите нашия <a %(a_2025_01_isbn_visualization_files)s>комбиниран торент</a>. Първо място $6,000: phiresky Това <a %(phiresky_github)s>предложение</a> (<a %(annas_archive_note_2951)s>коментар в Gitlab</a>) е просто всичко, което искахме, и още! Особено ни харесаха невероятно гъвкавите опции за визуализация (дори поддържащи персонализирани шейдъри), но с изчерпателен списък от предварителни настройки. Също така ни хареса колко бързо и гладко е всичко, простата реализация (която дори няма бекенд), умната миникарта и обширното обяснение в техния <a %(phiresky_github)s>блог пост</a>. Невероятна работа и заслужен победител! - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Сърцата ни са пълни с благодарност. Забележителни идеи Небостъргачи за рядкост Много плъзгачи за сравнение на Datasets, сякаш сте DJ. Скала с брой книги. Красиви етикети. Готина стандартна цветова схема и топлинна карта. Уникален изглед на карта и филтри Анотации и също живи статистики Живи статистики Още някои идеи и реализации, които особено ни харесаха: Можем да продължим още дълго, но нека спрем тук. Не забравяйте да разгледате всички предложения <a %(annas_archive)s>тук</a>, или изтеглете нашия <a %(a_2025_01_isbn_visualization_files)s>комбиниран торент</a>. Толкова много предложения, и всяко носи уникална перспектива, било то в UI или изпълнение. Поне ще включим предложението на първо място в нашия основен уебсайт, а може би и някои други. Също така започнахме да мислим как да организираме процеса на идентифициране, потвърждаване и след това архивиране на най-редките книги. Още новини предстоят. Благодарим на всички, които участваха. Удивително е, че толкова много хора се грижат. Лесно превключване на Datasets за бързи сравнения. Всички ISBN CADAL SSNOs Изтичане на данни от CERLALC DuXiu SSIDs Индекс на електронни книги на EBSCOhost Google Книги Goodreads Интернет Архив ISBNdb Глобален регистър на издателите ISBN Libby Файлове в Архива на Анна Nexus/STC OCLC/Worldcat Руска държавна библиотека Имперска библиотека на Трантор Второ място $3,000: hypha „Докато перфектните квадрати и правоъгълници са математически приятни, те не осигуряват превъзходна локалност в контекста на картографирането. Вярвам, че асиметрията, присъща на тези криви на Хилберт или класическия Мортън, не е недостатък, а характеристика. Точно както известният ботушовиден контур на Италия я прави незабавно разпознаваема на карта, уникалните „особености“ на тези криви могат да служат като когнитивни ориентири. Тази отличителност може да подобри пространствената памет и да помогне на потребителите да се ориентират, потенциално улеснявайки намирането на специфични региони или забелязването на модели.“ Още едно невероятно <a %(annas_archive_note_2913)s>предложение</a>. Не толкова гъвкаво като първото място, но всъщност предпочетохме неговата макро-ниво визуализация пред първото място (крива за запълване на пространство, граници, етикетиране, подчертаване, панорама и мащабиране). Един <a %(annas_archive_note_2971)s>коментар</a> от Джо Дейвис ни впечатли: И все още много опции за визуализация и рендиране, както и невероятно гладък и интуитивен потребителски интерфейс. Солидно второ място! - Анна и екипът (<a %(reddit)s>Reddit</a>) Преди няколко месеца обявихме <a %(all_isbns)s>награда от $10,000</a> за създаване на най-добрата възможна визуализация на нашите данни, показваща пространството на ISBN. Подчертяхме показването на кои файлове вече сме архивирали и кои не, и по-късно добавихме набор от данни, описващ колко библиотеки притежават ISBN (мярка за рядкост). Бяхме поразени от отговора. Имаше толкова много креативност. Голямо благодаря на всички, които участваха: вашата енергия и ентусиазъм са заразителни! В крайна сметка искахме да отговорим на следните въпроси: <strong>кои книги съществуват в света, колко от тях вече сме архивирали и върху кои книги трябва да се съсредоточим след това?</strong> Чудесно е да видим, че толкова много хора се интересуват от тези въпроси. Започнахме с основна визуализация сами. В по-малко от 300kb, тази картина кратко представя най-големия напълно отворен „списък на книги“, създаван някога в историята на човечеството: Трето място $500 #1: maxlion В това <a %(annas_archive_note_2940)s>предложение</a> наистина ни харесаха различните видове изгледи, по-специално изгледите за сравнение и издател. Трето място $500 #2: abetusk Въпреки че не е най-полираната потребителска интерфейс, това <a %(annas_archive_note_2917)s>предложение</a> отговаря на много от изискванията. Особено ни хареса функцията му за сравнение. Трето място $500 #3: conundrumer0 Както и първото място, това <a %(annas_archive_note_2975)s>предложение</a> ни впечатли с гъвкавостта си. В крайна сметка това е, което прави един инструмент за визуализация страхотен: максимална гъвкавост за напреднали потребители, като същевременно се запазва простотата за обикновените потребители. Трето място $500 #4: charelf Последното <a %(annas_archive_note_2947)s>предложение</a>, което получава награда, е доста основно, но има някои уникални функции, които наистина ни харесаха. Хареса ни как показват колко Datasets покриват определен ISBN като мярка за популярност/надеждност. Също така много ни хареса простотата, но ефективността на използването на плъзгач за непрозрачност за сравнения. Победители на наградата за визуализация на ISBN на стойност $10,000 Накратко: Получихме невероятни предложения за наградата за визуализация на ISBN на стойност $10,000. Предистория Как може Архивът на Анна да постигне своята мисия да архивира цялото знание на човечеството, без да знае кои книги все още съществуват? Нуждаем се от списък със задачи. Един от начините да картографираме това е чрез ISBN номера, които от 70-те години на миналия век се присвояват на всяка публикувана книга (в повечето страни). Няма централна власт, която да знае всички ISBN назначения. Вместо това, това е разпределена система, където страните получават диапазони от числа, които след това се присвояват на големи издатели, които могат да ги подразпределят на по-малки издатели. Накрая, индивидуални номера се присвояват на книги. Започнахме да картографираме ISBN <a %(blog)s>преди две години</a> с нашето извличане от ISBNdb. Оттогава сме извлекли много повече източници на metadata, като <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby и други. Пълен списък може да се намери на страниците „Datasets“ и „Torrents“ в Архивът на Анна. Сега имаме най-голямата напълно отворена, лесно изтегляема колекция от metadata за книги (и следователно ISBN) в света. <a %(blog)s>Писали сме обширно</a> за това защо се грижим за съхранението и защо в момента сме в критичен прозорец. Трябва сега да идентифицираме редки, недостатъчно фокусирани и уникално застрашени книги и да ги съхраним. Добрата metadata за всички книги в света помага за това. Награда от $10,000 Силно внимание ще се обърне на използваемостта и колко добре изглежда. Показвайте действителната metadata за индивидуални ISBN при увеличаване, като заглавие и автор. По-добра крива за запълване на пространството. Например зиг-заг, преминаващ от 0 до 4 на първия ред и след това обратно (в обратен ред) от 5 до 9 на втория ред — прилагано рекурсивно. Различни или персонализирани цветови схеми. Специални изгледи за сравняване на Datasets. Начини за отстраняване на проблеми, като например друга metadata, която не съвпада добре (например значително различни заглавия). Анотиране на изображения с коментари върху ISBN или диапазони. Всякакви евристики за идентифициране на редки или застрашени книги. Каквито и креативни идеи да измислите! Код Кодът за генериране на тези изображения, както и други примери, може да бъде намерен в <a %(annas_archive)s>тази директория</a>. Създадохме компактен формат на данни, с който цялата необходима информация за ISBN е около 75MB (компресирана). Описанието на формата на данните и кодът за генерирането му могат да бъдат намерени <a %(annas_archive_l1244_1319)s>тук</a>. За наградата не сте задължени да го използвате, но вероятно е най-удобният формат за започване. Можете да трансформирате нашата metadata както искате (въпреки че целият ви код трябва да бъде с отворен код). Нямаме търпение да видим какво ще измислите. Успех! Форкнете това репо и редактирайте този HTML на блог поста (не се допускат други бекенди освен нашия Flask бекенд). Направете изображението по-горе плавно увеличаемо, така че да можете да увеличите до индивидуални ISBN. Кликването върху ISBN трябва да ви отведе до страница с metadata или търсене в Архивът на Анна. Трябва все още да можете да превключвате между всички различни datasets. Диапазоните на страните и издателите трябва да бъдат подчертани при задържане на мишката. Можете да използвате напр. <a %(github_xlcnd_isbnlib)s>data4info.py в isbnlib</a> за информация за страните и нашето извличане „isbngrp“ за издатели (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Трябва да работи добре на настолни и мобилни устройства. Има много за изследване тук, затова обявяваме награда за подобряване на визуализацията по-горе. За разлика от повечето ни награди, тази е с ограничено време. Трябва да <a %(annas_archive)s>представите</a> своя отворен код до 2025-01-31 (23:59 UTC). Най-доброто представяне ще получи $6,000, второто място е $3,000, а третото място е $1,000. Всички награди ще бъдат изплатени с Monero (XMR). По-долу са минималните критерии. Ако нито едно представяне не отговаря на критериите, може все пак да присъдим някои награди, но това ще бъде по наше усмотрение. За бонус точки (това са само идеи — оставете въображението си да се развихри): Можете напълно да се отклоните от минималните критерии и да направите напълно различна визуализация. Ако е наистина впечатляваща, тогава това отговаря на условията за наградата, но по наше усмотрение. Направете предложения, като публикувате коментар към <a %(annas_archive)s>този въпрос</a> с връзка към вашето разклонено хранилище, заявка за сливане или разлика. - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Тази картина е 1000×800 пиксела. Всеки пиксел представлява 2,500 ISBN. Ако имаме файл за ISBN, правим този пиксел по-зелен. Ако знаем, че ISBN е издаден, но нямаме съответстващ файл, правим го по-червен. В по-малко от 300kb, тази картина кратко представя най-големия напълно отворен „списък с книги“, създаван някога в историята на човечеството (няколко стотин GB компресирани напълно). Тя също показва: има много работа, която остава за архивиране на книги (имаме само 16%). Визуализиране на всички ISBN — награда от $10,000 до 2025-01-31 Тази картина представлява най-големия напълно отворен „списък с книги“, създаван някога в историята на човечеството. Визуализация Освен общия образ, можем също да разгледаме индивидуалните datasets, които сме придобили. Използвайте падащото меню и бутоните, за да превключвате между тях. Има много интересни модели, които да се видят в тези изображения. Защо има някаква регулярност на линии и блокове, която изглежда се случва на различни мащаби? Какви са празните области? Защо определени datasets са толкова групирани? Ще оставим тези въпроси като упражнение за читателя. - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Заключение С този стандарт можем да правим издания по-инкрементално и по-лесно да добавяме нови източници на данни. Вече имаме няколко вълнуващи издания в процес на подготовка! Също така се надяваме, че ще стане по-лесно за други библиотеки в сянка да огледалят нашите колекции. В крайна сметка, нашата цел е да запазим човешкото знание и култура завинаги, така че колкото повече излишък, толкова по-добре. Пример Нека разгледаме нашето последно издание на Z-Library като пример. То се състои от две колекции: „<span style="background: #fffaa3">zlib3_records</span>“ и „<span style="background: #ffd6fe">zlib3_files</span>“. Това ни позволява да извличаме и издаваме отделно metadata записи от действителните файлове на книгите. Така издадохме два торента с файлове с metadata: Също така издадохме множество торенти с папки с двоични данни, но само за колекцията „<span style="background: #ffd6fe">zlib3_files</span>“, общо 62: Чрез изпълнение на <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> можем да видим какво има вътре: В този случай, това е metadata на книга, както е докладвано от Z-Library. На най-горното ниво имаме само „aacid“ и „metadata“, но няма „data_folder“, тъй като няма съответстващи двоични данни. AACID съдържа „22430000“ като основен ID, което виждаме, че е взето от „zlibrary_id“. Можем да очакваме други AAC в тази колекция да имат същата структура. Сега нека изпълним <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Това е много по-малка AAC metadata, въпреки че основната част от този AAC се намира другаде в двоичен файл! Все пак, този път имаме „data_folder“, така че можем да очакваме съответстващите двоични данни да се намират на <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata“ съдържа „zlibrary_id“, така че можем лесно да го асоциираме със съответстващия AAC в колекцията „zlib_records“. Можехме да го асоциираме по различни начини, например чрез AACID — стандартът не предписва това. Обърнете внимание, че също така не е необходимо полето „metadata“ само по себе си да бъде JSON. То може да бъде низ, съдържащ XML или друг формат на данни. Можете дори да съхранявате информация за metadata в свързания двоичен блок, например ако е много данни. Хетерогенни файлове и metadata, възможно най-близо до оригиналния формат. Бинарните данни могат да бъдат обслужвани директно от уеб сървъри като Nginx. Хетерогенни идентификатори в изходните библиотеки или дори липса на идентификатори. Отделни издания на metadata срещу данни от файлове или издания само на metadata (например нашето издание на ISBNdb). Разпространение чрез торенти, но с възможност за други методи на разпространение (например IPFS). Непроменими записи, тъй като трябва да приемем, че нашите торенти ще съществуват вечно. Инкрементални издания / добавяеми издания. Машинно четими и записваеми, удобно и бързо, особено за нашия стек (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Доста лесна човешка инспекция, въпреки че това е второстепенно спрямо машинната четимост. Лесно за сеене на нашите колекции със стандартен нает seedbox. Цели на дизайна Не ни интересува файловете да са лесни за навигация ръчно на диск или да са търсими без предварителна обработка. Не ни интересува да сме директно съвместими със съществуващ софтуер за библиотеки. Докато трябва да е лесно за всеки да сеее нашата колекция, използвайки торенти, не очакваме файловете да са използваеми без значителни технически познания и ангажимент. Нашият основен случай на употреба е разпространението на файлове и свързаните с тях metadata от различни съществуващи колекции. Нашите най-важни съображения са: Някои не-цели: Тъй като Архивът на Анна е с отворен код, искаме да използваме нашия формат директно. Когато обновяваме нашия индекс за търсене, достъпваме само публично достъпни пътища, така че всеки, който клонира нашата библиотека, да може бързо да започне работа. <strong>AAC.</strong> AAC (Контейнер на Архива на Анна) е единичен елемент, състоящ се от <strong>metadata</strong>, и по избор <strong>бинарни данни</strong>, и двете от които са неизменяеми. Той има глобално уникален идентификатор, наречен <strong>AACID</strong>. <strong>AACID.</strong> Форматът на AACID е следният: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Например, действителен AACID, който сме пуснали, е <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Диапазон на AACID.</strong> Тъй като AACID съдържат монотонно увеличаващи се времеви печати, можем да използваме това, за да обозначим диапазони в рамките на определена колекция. Използваме този формат: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, където времевите печати са включени. Това е съвместимо с ISO 8601 нотацията. Диапазоните са непрекъснати и могат да се припокриват, но в случай на припокриване трябва да съдържат идентични записи като тези, които са били пуснати преди това в тази колекция (тъй като AACs са неизменяеми). Липсващи записи не са позволени. <code>{collection}</code>: името на колекцията, което може да съдържа ASCII букви, цифри и долни черти (но не и двойни долни черти). <code>{collection-specific ID}</code>: специфичен за колекцията идентификатор, ако е приложимо, например ID на Z-Library. Може да бъде пропуснат или съкратен. Трябва да бъде пропуснат или съкратен, ако AACID в противен случай би надвишил 150 знака. <code>{ISO 8601 timestamp}</code>: кратка версия на ISO 8601, винаги в UTC, например <code>20220723T194746Z</code>. Това число трябва да се увеличава монотонно за всяко издание, въпреки че точната му семантика може да се различава за всяка колекция. Препоръчваме да използвате времето на събиране или на генериране на ID. <code>{shortuuid}</code>: UUID, но компресиран до ASCII, например с използване на base57. В момента използваме <a %(github_skorokithakis_shortuuid)s>shortuuid</a> библиотеката на Python. <strong>Папка с бинарни данни.</strong> Папка с бинарните данни на диапазон от AACs, за една конкретна колекция. Те имат следните свойства: Директорията трябва да съдържа файлове с данни за всички AACs в посочения диапазон. Всеки файл с данни трябва да има AACID като име на файла (без разширения). Името на директорията трябва да бъде диапазон на AACID, предшествано от <code style="color: green">annas_archive_data__</code>, и без суфикс. Например, една от нашите действителни издания има директория, наречена<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Препоръчително е тези папки да бъдат с управляем размер, например не по-големи от 100GB-1TB всяка, въпреки че тази препоръка може да се промени с времето. <strong>Колекция.</strong> Всеки AAC принадлежи към колекция, която по дефиниция е списък от AACs, които са семантично съвместими. Това означава, че ако направите значителна промяна във формата на метаданните, трябва да създадете нова колекция. Стандартът <strong>Файл с метаданни.</strong> Файл с метаданни съдържа метаданните на диапазон от AACs, за една конкретна колекция. Те имат следните свойства: <code>data_folder</code> е по избор и е името на папката с бинарни данни, която съдържа съответните бинарни данни. Името на файла на съответните бинарни данни в тази папка е AACID на записа. Всеки JSON обект трябва да съдържа следните полета на най-горно ниво: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (по избор). Не се допускат други полета. Името на файла трябва да бъде диапазон на AACID, предшествано от <code style="color: red">annas_archive_meta__</code> и последвано от <code>.jsonl.zstd</code>. Например, едно от нашите издания се нарича<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Както е указано от разширението на файла, типът на файла е <a %(jsonlines)s>JSON Lines</a>, компресиран с <a %(zstd)s>Zstandard</a>. <code>metadata</code> е произволни метаданни, според семантиката на колекцията. Трябва да бъде семантично съвместим в рамките на колекцията. Префиксът <code style="color: red">annas_archive_meta__</code> може да бъде адаптиран към името на вашата институция, например <code style="color: red">my_institute_meta__</code>. <strong>Колекции „записи“ и „файлове“.</strong> По конвенция често е удобно да се пускат „записи“ и „файлове“ като различни колекции, за да могат да бъдат пуснати на различни графици, например въз основа на честотата на събиране. „Запис“ е колекция само с метаданни, съдържаща информация като заглавия на книги, автори, ISBN и т.н., докато „файлове“ са колекциите, които съдържат самите файлове (pdf, epub). В крайна сметка се спряхме на относително прост стандарт. Той е доста гъвкав, ненормативен и все още в процес на разработка. <strong>Торенти.</strong> Файловете с metadata и папките с двоични данни могат да бъдат обединени в торенти, с един торент на файл с metadata или един торент на папка с двоични данни. Торентите трябва да имат оригиналното име на файла/директорията плюс наставката <code>.torrent</code> като тяхно име. <a %(wikipedia_annas_archive)s>Архивът на Анна</a> се превърна в най-голямата библиотека в сянка в света и единствената библиотека в сянка от този мащаб, която е напълно с отворен код и отворени данни. По-долу е таблица от нашата страница с Datasets (леко модифицирана): Постигнахме това по три начина: Огледално копиране на съществуващи библиотеки в сянка с отворени данни (като Sci-Hub и Library Genesis). Помощ на библиотеки в сянка, които искат да бъдат по-отворени, но нямат време или ресурси за това (като колекцията комикси на Libgen). Извличане на данни от библиотеки, които не желаят да споделят в големи количества (като Z-Library). За (2) и (3) сега управляваме значителна колекция от торенти сами (стотици TBs). Досега сме подхождали към тези колекции като еднократни, което означава специализирана инфраструктура и организация на данните за всяка колекция. Това добавя значителен разход към всяко издание и прави особено трудно извършването на по-инкрементални издания. Затова решихме да стандартизираме нашите издания. Това е техническа публикация в блога, в която представяме нашия стандарт: <strong>Контейнери на Архива на Анна</strong>. Архивът на Анна Контейнери (AAC): стандартизиране на изданията от най-голямата библиотека в сянка в света Архивът на Анна се превърна в най-голямата библиотека в сянка в света, което изисква от нас да стандартизираме нашите издания. 300GB+ корици на книги пуснати Накрая, сме щастливи да обявим малко издание. В сътрудничество с хората, които управляват форка Libgen.rs, споделяме всички техни корици на книги чрез торенти и IPFS. Това ще разпредели натоварването от гледането на кориците между повече машини и ще ги запази по-добре. В много (но не всички) случаи, кориците на книгите са включени в самите файлове, така че това е вид „производни данни“. Но наличието им в IPFS все още е много полезно за ежедневната работа както на Архива на Анна, така и на различните форкове на Library Genesis. Както обикновено, можете да намерите това издание в Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архива на Анна</a>). Няма да го свързваме тук, но можете лесно да го намерите. Надяваме се, че можем да намалим темпото си малко, сега когато имаме прилична алтернатива на Z-Library. Това натоварване не е особено устойчиво. Ако сте заинтересовани да помогнете с програмиране, сървърни операции или работа по запазване, определено се свържете с нас. Все още има много <a %(annas_archive)s>работа за вършене</a>. Благодарим ви за интереса и подкрепата. Преминаване към ElasticSearch Някои заявки отнемаха изключително дълго време, до степен, че заемат всички отворени връзки. По подразбиране MySQL има минимална дължина на думата, или индексът ви може да стане наистина голям. Хората съобщаваха, че не могат да търсят „Ben Hur“. Търсенето беше само донякъде бързо, когато беше напълно заредено в паметта, което изискваше да получим по-скъпа машина, за да го изпълняваме, плюс някои команди за предварително зареждане на индекса при стартиране. Нямаше да можем лесно да го разширим, за да изградим нови функции, като по-добра <a %(wikipedia_cjk_characters)s>токенизация за езици без интервали</a>, филтриране/фасетиране, сортиране, предложения „имахте предвид“, автозавършване и т.н. Един от нашите <a %(annas_archive)s>билети</a> беше сбор от проблеми с нашата система за търсене. Използвахме MySQL пълнотекстово търсене, тъй като имахме всички наши данни в MySQL така или иначе. Но имаше своите ограничения: След разговор с куп експерти, се спряхме на ElasticSearch. Не е било перфектно (техните предложения „имахте предвид“ и функции за автозавършване са слаби), но като цяло е много по-добро от MySQL за търсене. Все още не сме <a %(youtube)s>твърде склонни</a> да го използваме за каквито и да било критични данни (въпреки че са направили много <a %(elastic_co)s>напредък</a>), но като цяло сме доста доволни от промяната. Засега сме внедрили много по-бързо търсене, по-добра поддръжка на езици, по-добро сортиране по релевантност, различни опции за сортиране и филтриране по език/тип книга/тип файл. Ако сте любопитни как работи, <a %(annas_archive_l140)s>погледнете</a> <a %(annas_archive_l1115)s>тук</a> <a %(annas_archive_l1635)s>за повече информация</a>. Доста е достъпно, въпреки че може да използва още някои коментари… Архивът на Анна е напълно с отворен код Вярваме, че информацията трябва да бъде свободна, и нашият собствен код не е изключение. Публикувахме целия си код в нашата частно хоствана Gitlab инстанция: <a %(annas_archive)s>Софтуер на Анна</a>. Също така използваме тракера на проблеми, за да организираме работата си. Ако искате да се включите в нашето развитие, това е чудесно място да започнете. За да ви дадем представа за нещата, върху които работим, вземете нашата последна работа по подобрения на производителността на клиентската страна. Тъй като все още не сме внедрили пагинация, често връщахме много дълги страници с резултати от търсене, с 100-200 резултата. Не искахме да прекъсваме резултатите от търсенето твърде рано, но това означаваше, че ще забави някои устройства. За това внедрихме малък трик: обгърнахме повечето резултати от търсенето в HTML коментари (<code><!-- --></code>), и след това написахме малко Javascript, което да открива кога резултатът трябва да стане видим, в който момент ще разопаковаме коментара: DOM „виртуализация“, реализирана в 23 реда, без нужда от сложни библиотеки! Това е видът бърз прагматичен код, който получавате, когато имате ограничено време и реални проблеми, които трябва да бъдат решени. Съобщено е, че нашето търсене вече работи добре на бавни устройства! Друго голямо усилие беше автоматизирането на изграждането на базата данни. Когато стартирахме, просто събрахме различни източници на случаен принцип. Сега искаме да ги поддържаме актуализирани, затова написахме куп скриптове за изтегляне на нови metadata от двата форка на Library Genesis и ги интегрирахме. Целта е не само да направим това полезно за нашия архив, но и да улесним всеки, който иска да експериментира с metadata на shadow library. Целта би била Jupyter notebook, който има всякакви интересни metadata на разположение, за да можем да правим повече изследвания, като например да разберем какъв <a %(blog)s>процент от ISBN-ите са запазени завинаги</a>. Накрая, обновихме нашата система за дарения. Сега можете да използвате кредитна карта, за да депозирате директно пари в нашите крипто портфейли, без наистина да е необходимо да знаете нещо за криптовалутите. Ще продължим да наблюдаваме колко добре работи това на практика, но това е голяма стъпка. Със спирането на Z-Library и (предполагаемите) основатели, които бяха арестувани, работим денонощно, за да предоставим добра алтернатива с Архива на Анна (няма да го свързваме тук, но можете да го намерите в Google). Ето някои от нещата, които постигнахме наскоро. Актуализация на Анна: напълно отворен код архив, ElasticSearch, 300GB+ корици на книги Работим денонощно, за да предоставим добра алтернатива с Архива на Анна. Ето някои от нещата, които постигнахме наскоро. Анализ Семантичните дубликати (различни сканирания на една и съща книга) теоретично могат да бъдат филтрирани, но това е сложно. Когато ръчно преглеждахме комиксите, намерихме твърде много фалшиви положителни резултати. Има някои дубликати само по MD5, което е относително разточително, но филтрирането им би ни дало само около 1% in спестявания. В този мащаб това все още е около 1TB, но също така, в този мащаб 1TB наистина няма значение. Предпочитаме да не рискуваме случайно унищожаване на данни в този процес. Намерихме куп не-книжни данни, като филми, базирани на комикси. Това също изглежда разточително, тъй като те вече са широко достъпни чрез други средства. Въпреки това, осъзнахме, че не можем просто да филтрираме филмовите файлове, тъй като има и <em>интерактивни комикси</em>, които са били пуснати на компютър, които някой е записал и запазил като филми. В крайна сметка, всичко, което бихме могли да изтрием от колекцията, би спестило само няколко процента. Тогава си спомнихме, че сме събирачи на данни, и хората, които ще огледалят това, също са събирачи на данни, и така, „КАКВО ИМАТЕ ПРЕДВИД, ИЗТРИВАНЕ?!“ :) Когато получите 95TB, изсипани във вашия клъстер за съхранение, се опитвате да разберете какво всъщност има там… Направихме някои анализи, за да видим дали можем да намалим размера малко, например чрез премахване на дубликати. Ето някои от нашите открития: Затова ви представяме пълната, немодифицирана колекция. Това е много данни, но се надяваме, че достатъчно хора ще се грижат да я разпространяват. Сътрудничество С оглед на размера си, тази колекция отдавна е в нашия списък с желания, така че след успеха ни с архивирането на Z-Library, насочихме вниманието си към тази колекция. Първоначално я извличахме директно, което беше доста предизвикателство, тъй като техният сървър не беше в най-добро състояние. По този начин получихме около 15TB, но процесът беше бавен. За щастие, успяхме да се свържем с оператора на библиотеката, който се съгласи да ни изпрати всички данни директно, което беше много по-бързо. Все пак отне повече от половин година за прехвърляне и обработка на всички данни и почти загубихме всичко заради повреда на диска, което би означавало да започнем отначало. Този опит ни накара да вярваме, че е важно да разпространим тези данни възможно най-бързо, за да могат да бъдат огледални навсякъде. Ние сме само на едно или две нещастни събития от загубата на тази колекция завинаги! Колекцията Бързото движение означава, че колекцията е малко неорганизирана… Нека да погледнем. Представете си, че имаме файлова система (която в действителност разделяме на торенти): Първата директория, <code>/repository</code>, е по-структурираната част от това. Тази директория съдържа така наречените „хиляда директории“: директории, всяка с хиляда файла, които са инкрементално номерирани в базата данни. Директорията <code>0</code> съдържа файлове с comic_id от 0 до 999 и така нататък. Това е същата схема, която Library Genesis използва за своите колекции от художествена и нехудожествена литература. Идеята е, че всяка „хиляда директория“ автоматично се превръща в торент, веднага щом се запълни. Въпреки това, операторът на Libgen.li никога не е създавал торенти за тази колекция, и така хиляда директории вероятно са станали неудобни и са отстъпили място на „неподредени директории“. Това са <code>/comics0</code> до <code>/comics4</code>. Всички те съдържат уникални структури на директории, които вероятно са имали смисъл за събиране на файловете, но сега не ни изглеждат много логични. За щастие, metadata все още се отнася директно към всички тези файлове, така че организацията им на диска всъщност няма значение! Metadata е налична под формата на MySQL база данни. Тя може да бъде изтеглена директно от уебсайта на Libgen.li, но ние също ще я направим достъпна в торент, заедно с нашата собствена таблица с всички MD5 хешове. <q>Д-р Барбара Гордън се опитва да се изгуби в обикновения свят на библиотеката…</q> Разклонения на Libgen Първо, малко предистория. Може би познавате Library Genesis заради тяхната епична колекция от книги. По-малко хора знаят, че доброволците на Library Genesis са създали и други проекти, като значителна колекция от списания и стандартни документи, пълен архив на Sci-Hub (в сътрудничество с основателя на Sci-Hub, Александра Елбакян) и наистина, огромна колекция от комикси. В даден момент различни оператори на огледала на Library Genesis поеха по различни пътища, което доведе до сегашната ситуация с наличието на няколко различни „разклонения“, всички все още носещи името Library Genesis. Разклонението Libgen.li уникално притежава тази колекция от комикси, както и значителна колекция от списания (върху която също работим). Кампания за набиране на средства Пускаме тези данни в няколко големи части. Първият торент е на <code>/comics0</code>, който събрахме в един огромен 12TB .tar файл. Това е по-добре за вашия твърд диск и торент софтуер, отколкото милиони по-малки файлове. Като част от това издание, провеждаме кампания за набиране на средства. Целим да съберем $20,000, за да покрием оперативните и договорните разходи за тази колекция, както и да подкрепим текущи и бъдещи проекти. Имаме някои <em>огромни</em> в процес на работа. <em>Кого подкрепям с моята дарение?</em> Накратко: ние архивираме цялото знание и култура на човечеството и го правим лесно достъпно. Целият ни код и данни са с отворен код, ние сме напълно доброволчески проект и досега сме запазили 125TB книги (в допълнение към съществуващите торенти на Libgen и Scihub). В крайна сметка изграждаме маховик, който позволява и стимулира хората да намират, сканират и архивират всички книги в света. Ще напишем за нашия главен план в бъдеща публикация. :) Ако дарите за 12-месечно членство „Amazing Archivist“ ($780), ще можете да <strong>„осиновите торент“</strong>, което означава, че ще поставим вашето потребителско име или съобщение в името на един от торентите! Можете да дарите, като отидете на <a %(wikipedia_annas_archive)s>Архивът на Анна</a> и кликнете върху бутона „Дарете“. Също така търсим още доброволци: софтуерни инженери, изследователи по сигурността, експерти по анонимни търговци и преводачи. Можете също така да ни подкрепите, като предоставите хостинг услуги. И разбира се, моля, разпространявайте нашите торенти! Благодарим на всички, които вече ни подкрепиха толкова щедро! Наистина правите разлика. Ето торентите, които са пуснати досега (все още обработваме останалите): Всички торенти могат да бъдат намерени на <a %(wikipedia_annas_archive)s>Архивът на Анна</a> под „Datasets“ (не свързваме директно там, за да не бъдат премахнати връзките към този блог от Reddit, Twitter и т.н.). Оттам следвайте връзката към уебсайта на Tor. <a %(news_ycombinator)s>Обсъдете в Hacker News</a> Какво следва? Група от торенти са чудесни за дългосрочно съхранение, но не толкова за ежедневен достъп. Ще работим с хостинг партньори, за да качим всички тези данни в интернет (тъй като Архивът на Анна не хоства нищо директно). Разбира се, ще можете да намерите тези връзки за изтегляне в Архивът на Анна. Също така каним всички да правят неща с тези данни! Помогнете ни да ги анализираме по-добре, да ги дублираме, да ги качим на IPFS, да ги ремиксираме, да обучите вашите AI модели с тях и т.н. Всичко е ваше, и нямаме търпение да видим какво ще направите с тях. Накрая, както казахме преди, все още имаме някои огромни издания, които предстоят (ако <em>някой</em> може <em>случайно</em> да ни изпрати дъмп на <em>определена</em> база данни ACS4, знаете къде да ни намерите...), както и изграждането на маховика за архивиране на всички книги в света. Така че останете на линия, ние едва започваме. - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Най-голямата shadow library на комикси вероятно е тази на определен форк на Library Genesis: Libgen.li. Единственият администратор, който управлява този сайт, успя да събере невероятна колекция от комикси с над 2 милиона файла, общо над 95TB. Въпреки това, за разлика от други колекции на Library Genesis, тази не беше достъпна в насипно състояние чрез торенти. Можете да получите достъп до тези комикси само индивидуално чрез неговия бавен личен сървър — една точка на провал. До днес! В тази публикация ще ви разкажем повече за тази колекция и за нашата кампания за набиране на средства, за да подкрепим повече от тази работа. Архивът на Анна е архивирал най-голямата в света shadow library на комикси (95TB) — можете да помогнете да я разпространите Най-голямата shadow library на комикси в света имаше една точка на провал... до днес. Предупреждение: тази публикация в блога е остаряла. Решихме, че IPFS все още не е готов за основно използване. Все още ще свързваме към файлове на IPFS от Архива на Анна, когато е възможно, но няма да го хостваме сами повече, нито препоръчваме на други да огледалят, използвайки IPFS. Моля, вижте нашата страница за торенти, ако искате да помогнете за запазването на нашата колекция. Поставяне на 5,998,794 книги в IPFS Умножаване на копията Обратно към първоначалния ни въпрос: как можем да твърдим, че ще запазим нашите колекции завинаги? Основният проблем тук е, че нашата колекция <a %(torrents_stats)s>расте</a> с бързи темпове, чрез изстъргване и отворен код на някои масивни колекции (в допълнение към невероятната работа, вече извършена от други библиотеки в сянка с отворени данни като Sci-Hub и Library Genesis). Този растеж на данните прави по-трудно огледалното разпространение на колекциите по света. Съхранението на данни е скъпо! Но ние сме оптимисти, особено когато наблюдаваме следните три тенденции. <a %(annas_archive_stats)s>Общият размер</a> на нашите колекции през последните няколко месеца, разбит по брой на торент сийдъри. Тенденции в цените на HDD от различни източници (кликнете, за да видите изследването). <a %(critical_window_chinese)s>Китайска версия 中文版</a>, обсъдете в <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Обрахме ниско висящите плодове Това следва директно от нашите приоритети, обсъдени по-горе. Предпочитаме да работим върху освобождаването на големи колекции първо. Сега, когато сме осигурили някои от най-големите колекции в света, очакваме нашият растеж да бъде много по-бавен. Все още има дълга опашка от по-малки колекции, и нови книги се сканират или публикуват всеки ден, но темпото вероятно ще бъде много по-бавно. Може да се удвоим или дори утроим по размер, но за по-дълъг период от време. Подобрения в OCR. Приоритети Научен и инженеринг софтуерен код Художествени или развлекателни версии на всичко по-горе Географски данни (напр. карти, геоложки проучвания) Вътрешни данни от корпорации или правителства (изтичания) Данни от измервания като научни измервания, икономически данни, корпоративни доклади Записи на metadata като цяло (на нехудожествени и художествени произведения; на други медии, изкуство, хора и т.н.; включително рецензии) Нехудожествени книги Нехудожествени списания, вестници, ръководства Нехудожествени транскрипции на разговори, документални филми, подкасти Органични данни като ДНК последователности, семена на растения или микробни проби Академични статии, списания, доклади Научни и инженеринг уебсайтове, онлайн дискусии Транскрипции на правни или съдебни производства Уникално застрашени от унищожение (напр. от война, съкращения на финансиране, съдебни дела или политическо преследване) Редки Уникално недооценени Защо ни е толкова грижа за статиите и книгите? Нека оставим настрана нашето основно убеждение в запазването като цяло — може би ще напишем друг пост за това. Така че защо статии и книги конкретно? Отговорът е прост: <strong>информационна плътност</strong>. На мегабайт съхранение, писменият текст съхранява най-много информация от всички медии. Докато ни е грижа както за знанието, така и за културата, повече ни е грижа за първото. Като цяло намираме йерархия на информационната плътност и важността на запазването, която изглежда приблизително така: Класирането в този списък е донякъде произволно — няколко елемента са равни или има разногласия в нашия екип — и вероятно забравяме някои важни категории. Но това е приблизително как приоритизираме. Някои от тези елементи са твърде различни от останалите, за да се тревожим за тях (или вече са поети от други институции), като органични данни или географски данни. Но повечето от елементите в този списък всъщност са важни за нас. Друг голям фактор в нашата приоритизация е колко е застрашено дадено произведение. Предпочитаме да се фокусираме върху произведения, които са: Накрая, ние се грижим за мащаба. Имаме ограничено време и пари, така че предпочитаме да прекараме месец, спасявайки 10,000 книги, отколкото 1,000 книги — ако са приблизително еднакво ценни и застрашени. <em><q>Изгубеното не може да бъде възстановено; но нека спасим това, което остава: не чрез сейфове и ключалки, които ги ограждат от общественото око и употреба, като ги предаваме на загубата на времето, а чрез такова умножаване на копия, което ще ги постави извън обсега на случайността.</q></em><br>— Томас Джеферсън, 1791 Библиотеки в сянка Кодът може да бъде с отворен код в Github, но Github като цяло не може лесно да бъде огледан и следователно запазен (въпреки че в този конкретен случай има достатъчно разпространени копия на повечето хранилища с код) Записите на metadata могат да бъдат свободно разглеждани на уебсайта на Worldcat, но не могат да бъдат изтегляни на едро (докато не ги <a %(worldcat_scrape)s>изстъргахме</a>) Reddit е свободен за използване, но наскоро въведе строги мерки срещу изстъргването, в отговор на гладните за данни LLM обучения (повече за това по-късно) Има много организации, които имат подобни мисии и приоритети. Наистина, има библиотеки, архиви, лаборатории, музеи и други институции, натоварени с опазването от този вид. Много от тях са добре финансирани от правителства, индивиди или корпорации. Но те имат един огромен сляп петно: правната система. Тук се крие уникалната роля на библиотеките в сянка и причината за съществуването на Архивът на Анна. Можем да правим неща, които другите институции не са позволени да правят. Сега, не е (често) така, че можем да архивираме материали, които са незаконни за съхранение другаде. Не, в много места е законно да се изгради архив с всякакви книги, статии, списания и т.н. Но това, което често липсва на легалните архиви, е <strong>излишък и дълговечност</strong>. Съществуват книги, от които има само едно копие в някоя физическа библиотека някъде. Съществуват записи на metadata, пазени от една единствена корпорация. Съществуват вестници, запазени само на микрофилм в един единствен архив. Библиотеките могат да получат съкращения на финансирането, корпорациите могат да фалират, архивите могат да бъдат бомбардирани и изгорени до основи. Това не е хипотетично — това се случва постоянно. Това, което можем да направим уникално в Архива на Анна, е да съхраняваме много копия на произведения в голям мащаб. Можем да събираме статии, книги, списания и още, и да ги разпространяваме на едро. В момента правим това чрез торенти, но точните технологии нямат значение и ще се променят с времето. Важната част е да се разпространят много копия по целия свят. Този цитат от преди повече от 200 години все още звучи вярно: Кратка бележка за общественото достояние. Тъй като Архивът на Анна се фокусира уникално върху дейности, които са незаконни на много места по света, не се занимаваме с широко достъпни колекции, като книги в общественото достояние. Правните субекти често вече се грижат добре за това. Въпреки това, има съображения, които понякога ни карат да работим върху публично достъпни колекции: - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Разходите за съхранение продължават да падат експоненциално 3. Подобрения в информационната плътност В момента съхраняваме книгите в суровите формати, в които ни се предоставят. Разбира се, те са компресирани, но често все още са големи сканирания или фотографии на страници. Досега единствените опции за намаляване на общия размер на нашата колекция бяха чрез по-агресивна компресия или дублиране. Въпреки това, за да постигнем значителни спестявания, и двете са твърде загубни за нашия вкус. Силната компресия на снимки може да направи текста едва четим. А дублирането изисква висока увереност, че книгите са точно същите, което често е твърде неточно, особено ако съдържанието е същото, но сканиранията са направени по различни поводи. Винаги е имало трета опция, но качеството ѝ е било толкова ужасно, че никога не сме я разглеждали: <strong>OCR, или Оптично разпознаване на символи</strong>. Това е процесът на преобразуване на снимки в обикновен текст, като се използва AI за разпознаване на символите в снимките. Инструменти за това съществуват отдавна и са доста добри, но „доста добри“ не е достатъчно за целите на съхранение. Въпреки това, последните мултимодални модели за дълбоко обучение направиха изключително бърз напредък, макар и все още на високи разходи. Очакваме както точността, така и разходите да се подобрят драстично през следващите години, до степен, в която ще стане реалистично да се приложи към цялата ни библиотека. Когато това се случи, вероятно ще запазим оригиналните файлове, но освен това бихме могли да имаме много по-малка версия на нашата библиотека, която повечето хора ще искат да огледалят. Основното е, че самият суров текст се компресира дори по-добре и е много по-лесен за дублиране, което ни дава още повече спестявания. Като цяло не е нереалистично да очакваме поне 5-10 пъти намаление на общия размер на файловете, може би дори повече. Дори при консервативно намаление от 5 пъти, ще разглеждаме <strong>$1,000–$3,000 за 10 години, дори ако нашата библиотека се утрои по размер</strong>. Към момента на писане, <a %(diskprices)s>цените на дисковете</a> на TB са около $12 за нови дискове, $8 за използвани дискове и $4 за лента. Ако сме консервативни и гледаме само нови дискове, това означава, че съхранението на петабайт струва около $12,000. Ако приемем, че нашата библиотека ще се утрои от 900TB до 2.7PB, това би означавало $32,400 за огледално разпространение на цялата ни библиотека. Добавяйки електричество, разходи за друго оборудване и т.н., нека го закръглим на $40,000. Или с лента повече като $15,000–$20,000. От една страна <strong>$15,000–$40,000 за сумата на цялото човешко знание е изгодно</strong>. От друга страна, това е малко стръмно да се очаква тонове пълни копия, особено ако искаме тези хора да продължат да сеят своите торенти за ползата на другите. Това е днес. Но прогресът върви напред: Цените на твърдите дискове на TB са били приблизително намалени на трета през последните 10 години и вероятно ще продължат да падат със същото темпо. Лентата изглежда е на подобна траектория. Цените на SSD падат дори по-бързо и може да изпреварят цените на HDD до края на десетилетието. Ако това се задържи, след 10 години може да гледаме само $5,000–$13,000 за огледално разпространение на цялата ни колекция (1/3), или дори по-малко, ако растем по-малко по размер. Въпреки че все още е много пари, това ще бъде постижимо за много хора. И може да бъде дори по-добре заради следващата точка… В Архива на Анна често ни питат как можем да твърдим, че ще запазим нашите колекции завинаги, когато общият размер вече наближава 1 петабайт (1000 TB) и продължава да расте. В тази статия ще разгледаме нашата философия и ще видим защо следващото десетилетие е критично за нашата мисия за запазване на знанието и културата на човечеството. Критичен прозорец Ако тези прогнози са точни, ние <strong>просто трябва да изчакаме няколко години</strong>, преди цялата ни колекция да бъде широко огледална. Така, по думите на Томас Джеферсън, „поставена извън обсега на случайността“. За съжаление, появата на LLM и тяхното обучение, което изисква много данни, постави много притежатели на авторски права в отбранителна позиция. Още повече, отколкото вече бяха. Много уебсайтове правят по-трудно изтеглянето и архивирането, съдебни дела летят наоколо, а през това време физическите библиотеки и архиви продължават да бъдат пренебрегвани. Можем само да очакваме тези тенденции да продължат да се влошават и много произведения да бъдат загубени много преди да влязат в общественото достояние. <strong>Ние сме на прага на революция в съхранението, но <q>загубеното не може да бъде възстановено.</q></strong> Имаме критичен прозорец от около 5-10 години, през който все още е сравнително скъпо да се управлява библиотека в сянка и да се създават много огледала по света, и през който достъпът все още не е напълно затворен. Ако можем да преодолеем този прозорец, тогава наистина ще сме съхранили знанието и културата на човечеството за вечни времена. Не трябва да позволяваме това време да бъде пропиляно. Не трябва да позволяваме този критичен прозорец да се затвори пред нас. Да го направим. Критичният прозорец на библиотеките в сянка Как можем да твърдим, че ще запазим нашите колекции завинаги, когато те вече наближават 1 PB? Колекция Малко повече информация за колекцията. <a %(duxiu)s>Duxiu</a> е огромна база данни от сканирани книги, създадена от <a %(chaoxing)s>SuperStar Digital Library Group</a>. Повечето са академични книги, сканирани, за да бъдат достъпни дигитално за университети и библиотеки. За нашата англоговоряща аудитория, <a %(library_princeton)s>Принстън</a> и <a %(guides_lib_uw)s>Университетът на Вашингтон</a> имат добри прегледи. Има и отлична статия, която дава повече информация: <a %(doi)s>„Цифровизация на китайски книги: Казус на търсачката SuperStar DuXiu Scholar“</a> (потърсете я в Архива на Анна). Книгите от Duxiu отдавна са пиратствани в китайския интернет. Обикновено се продават за по-малко от долар от препродавачи. Обикновено се разпространяват чрез китайския еквивалент на Google Drive, който често е хакнат, за да позволи повече място за съхранение. Някои технически детайли могат да бъдат намерени <a %(github_duty_machine)s>тук</a> и <a %(github_821_github_io)s>тук</a>. Въпреки че книгите са били полу-публично разпространени, е доста трудно да се получат в насипно състояние. Това беше високо в нашия списък със задачи и отделихме няколко месеца пълно работно време за него. Въпреки това, наскоро невероятен, удивителен и талантлив доброволец се свърза с нас, казвайки ни, че вече е свършил цялата тази работа — на големи разходи. Те споделиха цялата колекция с нас, без да очакват нищо в замяна, освен гаранцията за дългосрочно съхранение. Наистина забележително. Те се съгласиха да поискат помощ по този начин, за да се OCR-ира колекцията. Колекцията е 7,543,702 файла. Това е повече от нехудожествената част на Library Genesis (около 5.3 милиона). Общият размер на файловете е около 359TB (326TiB) в настоящата си форма. Отворени сме за други предложения и идеи. Просто се свържете с нас. Разгледайте Архива на Анна за повече информация относно нашите колекции, усилията за съхранение и как можете да помогнете. Благодаря! Примерни страници За да ни докажете, че имате добър процес, ето някои примерни страници, с които да започнете, от книга за свръхпроводници. Вашият процес трябва правилно да обработва математика, таблици, графики, бележки под линия и т.н. Изпратете вашите обработени страници на нашия имейл. Ако изглеждат добре, ще ви изпратим още насаме и очакваме да можете бързо да изпълните вашия процес и на тях. След като сме доволни, можем да сключим сделка. - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Китайска версия 中文版</a>, <a %(news_ycombinator)s>Обсъждане в Hacker News</a> Това е кратък блог пост. Търсим компания или институция, която да ни помогне с OCR и извличане на текст за масивна колекция, която придобихме, в замяна на изключителен ранен достъп. След периода на ембарго, разбира се, ще пуснем цялата колекция. Висококачественият академичен текст е изключително полезен за обучение на LLM. Въпреки че нашата колекция е китайска, това би трябвало да бъде полезно дори за обучение на английски LLM: моделите изглежда кодират концепции и знания независимо от изходния език. За това, текстът трябва да бъде извлечен от скановете. Какво получава Архивът на Анна от това? Пълнотекстово търсене на книгите за своите потребители. Тъй като нашите цели съвпадат с тези на разработчиците на LLM, търсим сътрудник. Готови сме да ви дадем <strong>изключителен ранен достъп до тази колекция в насипно състояние за 1 година</strong>, ако можете да извършите правилно OCR и извличане на текст. Ако сте готови да споделите целия код на вашия процес с нас, бихме били готови да удължим ембаргото на колекцията. Ексклузивен достъп за LLM компании до най-голямата колекция от китайски нехудожествени книги в света <em><strong>Накратко:</strong> Архивът на Анна придоби уникална колекция от 7.5 милиона / 350TB китайски нехудожествени книги — по-голяма от Library Genesis. Готови сме да дадем на компания за LLM изключителен достъп, в замяна на висококачествено OCR и извличане на текст.</em> Архитектура на системата Да кажем, че сте намерили някои компании, които са готови да хостват вашия уебсайт, без да ви затварят — нека ги наречем „свободолюбиви доставчици“ 😄. Бързо ще откриете, че хостването на всичко с тях е доста скъпо, така че може да искате да намерите някои „евтини доставчици“ и да извършите действителното хостване там, проксирайки през свободолюбивите доставчици. Ако го направите правилно, евтините доставчици никога няма да знаят какво хоствате и никога няма да получат оплаквания. С всички тези доставчици съществува риск те да ви затворят така или иначе, така че също се нуждаете от излишък. Нуждаем се от това на всички нива на нашия стек. Една донякъде свободолюбива компания, която се е поставила в интересна позиция, е Cloudflare. Те <a %(blog_cloudflare)s>твърдят</a>, че не са хостинг доставчик, а услуга, като интернет доставчик. Следователно те не подлежат на DMCA или други искания за премахване и препращат всички искания към вашия действителен хостинг доставчик. Те дори са стигнали до съда, за да защитят тази структура. Следователно можем да ги използваме като още един слой кеширане и защита. Cloudflare не приема анонимни плащания, така че можем да използваме само техния безплатен план. Това означава, че не можем да използваме техните функции за балансиране на натоварването или резервиране. Следователно <a %(annas_archive_l255)s>го внедрихме сами</a> на ниво домейн. При зареждане на страницата браузърът ще провери дали текущият домейн все още е наличен и ако не е, ще пренапише всички URL адреси към друг домейн. Тъй като Cloudflare кешира много страници, това означава, че потребителят може да попадне на нашия основен домейн, дори ако прокси сървърът е неактивен, и след това при следващото кликване да бъде прехвърлен на друг домейн. Все още имаме и нормални оперативни проблеми, с които да се справяме, като наблюдение на здравето на сървъра, регистриране на грешки в бекенда и фронтенда и т.н. Нашата архитектура за резервиране позволява по-голяма устойчивост и в това отношение, например чрез стартиране на напълно различен набор от сървъри на един от домейните. Можем дори да стартираме по-стари версии на кода и datasets на този отделен домейн, в случай че критична грешка в основната версия остане незабелязана. Можем също така да се предпазим от Cloudflare, като го премахнем от един от домейните, като този отделен домейн. Възможни са различни комбинации от тези идеи. Заключение Беше интересно преживяване да научим как да настроим стабилен и устойчив търсач на сенчести библиотеки. Има много повече детайли, които да споделим в бъдещи публикации, така че нека знаем какво бихте искали да научите повече! Както винаги, търсим дарения, за да подкрепим тази работа, така че не забравяйте да посетите страницата за дарения в Архива на Анна. Също така търсим други видове подкрепа, като грантове, дългосрочни спонсори, доставчици на високорискови плащания, може би дори (с вкус!) реклами. И ако искате да допринесете с вашето време и умения, винаги търсим разработчици, преводачи и т.н. Благодарим ви за интереса и подкрепата. Токени за иновации Нека започнем с нашия технологичен стек. Той е умишлено скучен. Използваме Flask, MariaDB и ElasticSearch. Това е буквално всичко. Търсенето е до голяма степен решен проблем и не възнамеряваме да го преоткриваме. Освен това трябва да изразходваме нашите <a %(mcfunley)s>иновационни токени</a> за нещо друго: да не бъдем свалени от властите. Колко законен или незаконен е всъщност Архивът на Анна? Това зависи основно от правната юрисдикция. Повечето държави вярват в някаква форма на авторско право, което означава, че на хора или компании се предоставя изключителен монопол върху определени видове произведения за определен период от време. Между другото, в Архива на Анна вярваме, че въпреки че има някои ползи, като цяло авторското право е нетна загуба за обществото — но това е история за друг път. Този изключителен монопол върху определени произведения означава, че е незаконно за всеки извън този монопол да разпространява директно тези произведения — включително и за нас. Но Архивът на Анна е търсачка, която не разпространява директно тези произведения (поне не на нашия уебсайт в откритата мрежа), така че би трябвало да сме в безопасност, нали? Не точно. В много юрисдикции не само е незаконно да се разпространяват защитени с авторски права произведения, но и да се свързва към места, които го правят. Класически пример за това е законът DMCA на Съединените щати. Това е най-строгият край на спектъра. В другия край на спектъра теоретично може да има държави без никакви закони за авторското право, но такива наистина не съществуват. Почти всяка държава има някаква форма на закон за авторското право. Прилагането е различна история. Има много държави с правителства, които не се интересуват от прилагането на закона за авторското право. Има и държави между двете крайности, които забраняват разпространението на защитени с авторски права произведения, но не забраняват свързването към такива произведения. Друго съображение е на ниво компания. Ако една компания оперира в юрисдикция, която не се интересува от авторското право, но самата компания не е готова да поеме никакъв риск, тогава те може да закрият вашия уебсайт веднага щом някой се оплаче за него. Накрая, голямо съображение са плащанията. Тъй като трябва да останем анонимни, не можем да използваме традиционни методи за плащане. Това ни оставя с криптовалути, и само малка част от компаниите ги поддържат (има виртуални дебитни карти, платени с крипто, но те често не се приемат). - Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Аз управлявам <a %(wikipedia_annas_archive)s>Архива на Анна</a>, най-голямата в света отворена и некомерсиална търсачка за <a %(wikipedia_shadow_library)s>библиотеки в сянка</a>, като Sci-Hub, Library Genesis и Z-Library. Нашата цел е да направим знанието и културата лесно достъпни и в крайна сметка да изградим общност от хора, които заедно архивират и съхраняват <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>всички книги в света</a>. В тази статия ще покажа как управляваме този уебсайт и уникалните предизвикателства, които идват с управлението на уебсайт със съмнителен правен статус, тъй като няма „AWS за сенчести благотворителни организации“. <em>Също така разгледайте сестринската статия <a %(blog_how_to_become_a_pirate_archivist)s>Как да станете пиратски архивист</a>.</em> Как да управляваме сянкова библиотека: операции в Архива на Анна Няма <q>AWS за сянкови благотворителни организации,</q> така че как управляваме Архива на Анна? Инструменти Сървър на приложението: Flask, MariaDB, ElasticSearch, Docker. Разработка: Gitlab, Weblate, Zulip. Управление на сървъра: Ansible, Checkmk, UFW. Статично хостване на Onion: Tor, Nginx. Прокси сървър: Varnish. Нека разгледаме какви инструменти използваме, за да постигнем всичко това. Това много се развива, докато се сблъскваме с нови проблеми и намираме нови решения. Има някои решения, по които сме се колебали. Едно от тях е комуникацията между сървърите: използвахме Wireguard за това, но установихме, че понякога спира да предава данни или предава данни само в една посока. Това се случи с няколко различни настройки на Wireguard, които опитахме, като <a %(github_costela_wesher)s>wesher</a> и <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Опитахме също така тунелиране на портове през SSH, използвайки autossh и sshuttle, но се сблъскахме с <a %(github_sshuttle)s>проблеми там</a> (въпреки че все още не ми е ясно дали autossh страда от проблеми с TCP-over-TCP или не — просто ми се струва като нестабилно решение, но може би всъщност е наред?). Вместо това се върнахме към директни връзки между сървърите, скривайки, че сървърът работи на евтини доставчици, използвайки IP-филтриране с UFW. Това има недостатъка, че Docker не работи добре с UFW, освен ако не използвате <code>network_mode: "host"</code>. Всичко това е малко по-податливо на грешки, защото ще изложите сървъра си в интернет с малка грешна конфигурация. Може би трябва да се върнем към autossh — обратната връзка би била много полезна тук. Също така сме се колебали между Varnish и Nginx. В момента предпочитаме Varnish, но той има своите особености и груби ръбове. Същото важи и за Checkmk: не го обичаме, но работи засега. Weblate е добре, но не е невероятно — понякога се страхувам, че ще загуби данните ми, когато се опитвам да го синхронизирам с нашето git хранилище. Flask е добър като цяло, но има някои странни особености, които са отнели много време за отстраняване на грешки, като конфигуриране на персонализирани домейни или проблеми с интеграцията му със SqlAlchemy. Досега другите инструменти са били страхотни: нямаме сериозни оплаквания относно MariaDB, ElasticSearch, Gitlab, Zulip, Docker и Tor. Всички те са имали някои проблеми, но нищо прекалено сериозно или отнемащо време. Общност Първото предизвикателство може да бъде изненадващо. То не е технически проблем или правен проблем. То е психологически проблем: извършването на тази работа в сенките може да бъде невероятно самотно. В зависимост от това, което планирате да направите, и вашия модел на заплаха, може да се наложи да бъдете много внимателни. В единия край на спектъра имаме хора като Александра Елбакян*, основателката на Sci-Hub, която е много открита за своите дейности. Но тя е в голям риск да бъде арестувана, ако посети западна страна в този момент, и може да се изправи пред десетилетия затвор. Това ли е риск, който бихте били готови да поемете? Ние сме в другия край на спектъра; много внимателни да не оставяме никакви следи и с висока оперативна сигурност. * Както е споменато на HN от "ynno", Александра първоначално не е искала да бъде известна: "Нейните сървъри бяха настроени да излъчват подробни съобщения за грешки от PHP, включително пълния път на грешния изходен файл, който беше под директория /home/<USER>" Така че, използвайте случайни потребителски имена на компютрите, които използвате за тези неща, в случай че нещо е конфигурирано неправилно. Тази тайна обаче идва с психологическа цена. Повечето хора обичат да бъдат признати за работата, която вършат, и все пак не можете да получите никакъв кредит за това в реалния живот. Дори простите неща могат да бъдат предизвикателни, като например приятели, които ви питат с какво сте се занимавали (в един момент "занимавам се с моя NAS / домашен лаб" става старо). Ето защо е толкова важно да намерите някаква общност. Можете да се откажете от част от оперативната сигурност, като се доверите на някои много близки приятели, на които знаете, че можете да се доверите дълбоко. Дори тогава бъдете внимателни да не поставяте нищо в писмена форма, в случай че трябва да предадат имейлите си на властите или ако техните устройства са компрометирани по някакъв друг начин. Още по-добре е да намерите някои съмишленици пирати. Ако вашите близки приятели се интересуват да се присъединят към вас, чудесно! В противен случай може да успеете да намерите други онлайн. За съжаление, това все още е нишова общност. Досега сме намерили само шепа други, които са активни в това пространство. Добри начални места изглежда са форумите на Library Genesis и r/DataHoarder. Екипът на Archive също има съмишленици, въпреки че те оперират в рамките на закона (дори и в някои сиви зони на закона). Традиционните "warez" и пиратски сцени също имат хора, които мислят по подобен начин. Отворени сме за идеи как да насърчим общността и да изследваме идеи. Чувствайте се свободни да ни пишете в Twitter или Reddit. Може би бихме могли да организираме някакъв форум или чат група. Едно предизвикателство е, че това лесно може да бъде цензурирано, когато се използват общи платформи, така че ще трябва да го хостваме сами. Има и компромис между това да направим тези дискусии напълно публични (повече потенциално участие) и да ги направим частни (да не позволим на потенциални "цели" да знаят, че ще ги изстържем). Ще трябва да помислим за това. Уведомете ни, ако се интересувате от това! Заключение Надяваме се, че това е полезно за новопрохождащите пиратски архивисти. С нетърпение ви приветстваме в този свят, така че не се колебайте да се свържете с нас. Нека запазим колкото се може повече от световното знание и култура и да го огледаме навсякъде. Проекти 4. Избор на данни Често можете да използвате метаданните, за да определите разумен подмножество от данни за изтегляне. Дори ако в крайна сметка искате да изтеглите всички данни, може да е полезно да приоритизирате най-важните елементи първо, в случай че бъдете засечени и защитите бъдат подобрени, или защото ще трябва да купите повече дискове, или просто защото нещо друго се появи в живота ви, преди да можете да изтеглите всичко. Например, колекция може да има множество издания на един и същ основен ресурс (като книга или филм), където едно е маркирано като най-добро качество. Запазването на тези издания първо би имало много смисъл. Може в крайна сметка да искате да запазите всички издания, тъй като в някои случаи метаданните може да са неправилно маркирани, или може да има неизвестни компромиси между изданията (например, "най-доброто издание" може да е най-добро в повечето отношения, но по-лошо в други, като филм с по-висока резолюция, но без субтитри). Можете също така да търсите в базата си данни с метаданни, за да намерите интересни неща. Кой е най-големият файл, който се хоства, и защо е толкова голям? Кой е най-малкият файл? Има ли интересни или неочаквани модели, когато става въпрос за определени категории, езици и т.н.? Има ли дублирани или много подобни заглавия? Има ли модели за това кога данните са добавени, като един ден, в който много файлове са добавени наведнъж? Често можете да научите много, като разглеждате набора от данни по различни начини. В нашия случай, ние премахнахме дублирането на книги от Z-Library спрямо md5 хешовете в Library Genesis, като по този начин спестихме много време за изтегляне и дисково пространство. Това обаче е доста уникална ситуация. В повечето случаи няма изчерпателни бази данни за това кои файлове вече са правилно запазени от други пирати. Това само по себе си е огромна възможност за някого там. Би било чудесно да има редовно актуализиран преглед на неща като музика и филми, които вече са широко разпространени на торент сайтове и следователно са с по-нисък приоритет за включване в пиратски огледала. 6. Разпространение Имате данните, като по този начин притежавате първото пиратско огледало на вашата цел (най-вероятно). В много отношения най-трудната част е приключила, но най-рисковата част все още е пред вас. В крайна сметка, досега сте били скрити; летейки под радара. Всичко, което трябваше да направите, беше да използвате добър VPN през цялото време, да не попълвате личните си данни във формуляри (разбира се), и може би да използвате специална сесия на браузъра (или дори различен компютър). Сега трябва да разпространите данните. В нашия случай първо искахме да върнем книгите в Library Genesis, но бързо открихме трудностите в това (сортировка на художествена срещу нехудожествена литература). Затова решихме да разпространяваме чрез торенти в стил Library Genesis. Ако имате възможност да допринесете към съществуващ проект, това може да ви спести много време. Въпреки това, в момента няма много добре организирани пиратски огледала. Така че да кажем, че решите да разпространявате торенти сами. Опитайте се да ги поддържате малки, за да са лесни за огледало на други уебсайтове. След това ще трябва да сеете торентите сами, докато все още оставате анонимни. Можете да използвате VPN (с или без пренасочване на портове) или да платите с тумблирани биткойни за Seedbox. Ако не знаете какво означават някои от тези термини, ще имате много за четене, тъй като е важно да разберете рисковите компромиси тук. Можете да хоствате самите торент файлове на съществуващи торент уебсайтове. В нашия случай избрахме да хостваме уебсайт, тъй като също искахме да разпространим нашата философия по ясен начин. Можете да направите това сами по подобен начин (ние използваме Njalla за нашите домейни и хостинг, платени с тумблирани биткойни), но също така не се колебайте да се свържете с нас, за да хостваме вашите торенти. Ние се стремим да изградим изчерпателен индекс на пиратски огледала с течение на времето, ако тази идея се наложи. Що се отнася до избора на VPN, много е писано за това вече, така че просто ще повторим общия съвет да избирате по репутация. Реални съдебно тествани политики без логове с дълги записи за защита на поверителността са най-нискорисковата опция, според нас. Обърнете внимание, че дори когато правите всичко правилно, никога не можете да достигнете до нулев риск. Например, когато сеете вашите торенти, силно мотивиран държавен актьор вероятно може да разгледа входящите и изходящите потоци от данни за VPN сървъри и да заключи кой сте. Или просто можете да направите грешка по някакъв начин. Вероятно вече сме го направили и ще го направим отново. За щастие, държавите не се интересуват <em>толкова</em> много от пиратството. Едно решение, което трябва да вземете за всеки проект, е дали да го публикувате с предишната идентичност или не. Ако продължите да използвате същото име, тогава грешки в оперативната сигурност от по-ранни проекти могат да се върнат, за да ви навредят. Но публикуването под различни имена означава, че не изграждате по-дълготрайна репутация. Ние избрахме да имаме силна оперативна сигурност от самото начало, за да можем да продължим да използваме същата идентичност, но няма да се колебаем да публикуваме под различно име, ако направим грешка или ако обстоятелствата го изискват. Разпространението на информацията може да бъде трудно. Както казахме, това все още е нишова общност. Първоначално публикувахме в Reddit, но наистина получихме внимание в Hacker News. Засега нашата препоръка е да го публикувате на няколко места и да видите какво ще се случи. И отново, свържете се с нас. Ще се радваме да разпространим думата за повече усилия за пиратски архивизъм. 1. Избор на домейн / философия Няма недостиг на знания и културно наследство, които да бъдат запазени, което може да бъде поразително. Затова често е полезно да отделите момент и да помислите какъв може да бъде вашият принос. Всеки има различен начин на мислене за това, но ето някои въпроси, които можете да си зададете: В нашия случай, ние се интересувахме особено от дългосрочното запазване на науката. Знаехме за Library Genesis и как тя беше напълно огледана много пъти чрез торенти. Обичахме тази идея. Тогава един ден, един от нас се опита да намери някои научни учебници в Library Genesis, но не успя да ги намери, което постави под съмнение колко пълна е тя наистина. След това потърсихме тези учебници онлайн и ги намерихме на други места, което засади семето за нашия проект. Дори преди да знаем за Z-Library, имахме идеята да не се опитваме да събираме всички тези книги ръчно, а да се фокусираме върху огледалото на съществуващи колекции и да ги допринасяме обратно към Library Genesis. Какви умения имате, които можете да използвате в своя полза? Например, ако сте експерт по онлайн сигурност, можете да намерите начини за преодоляване на блокирането на IP за сигурни цели. Ако сте добри в организирането на общности, тогава може би можете да съберете някои хора около цел. Полезно е да знаете малко програмиране, дори само за да поддържате добра оперативна сигурност през целия този процес. Коя би била област с висок лост за фокусиране? Ако ще прекарате X часа в пиратско архивиране, как можете да получите най-голямата "възвръщаемост на инвестицията"? Какви са уникалните начини, по които мислите за това? Може да имате някои интересни идеи или подходи, които другите може да са пропуснали. Колко време имате за това? Нашият съвет би бил да започнете с малки проекти и да правите по-големи, когато се усетите, но това може да стане всепоглъщащо. Защо се интересувате от това? Какво ви вълнува? Ако можем да съберем група хора, които всички архивират нещата, които ги интересуват, това би покрило много! Ще знаете много повече от средния човек за вашата страст, като какви са важните данни за запазване, кои са най-добрите колекции и онлайн общности и т.н. 3. Извличане на metadata Дата на добавяне/промяна: за да можете да се върнете по-късно и да изтеглите файлове, които не сте изтеглили преди (въпреки че често можете да използвате и ID или хеш за това). Хеш (md5, sha1): за да потвърдите, че сте изтеглили файла правилно. ID: може да бъде някакъв вътрешен ID, но ID като ISBN или DOI също са полезни. Име на файл / местоположение Описание, категория, тагове, автори, език и т.н. Размер: за да изчислите колко дисково пространство ви е необходимо. Нека станем малко по-технически тук. За действителното извличане на metadata от уебсайтове, ние сме запазили нещата доста прости. Използваме Python скриптове, понякога curl и MySQL база данни, за да съхраняваме резултатите. Не сме използвали никакъв сложен софтуер за извличане, който може да картографира сложни уебсайтове, тъй като досега ни трябваше само да извличаме един или два вида страници, като просто изброяваме чрез идентификатори и анализираме HTML. Ако няма лесно изброими страници, тогава може да ви трябва подходящ обхождач, който се опитва да намери всички страници. Преди да започнете да извличате цял уебсайт, опитайте да го направите ръчно за малко. Прегледайте няколко десетки страници сами, за да получите представа как работи това. Понякога вече ще се сблъскате с IP блокировки или друго интересно поведение по този начин. Същото важи и за извличането на данни: преди да се задълбочите твърде много в тази цел, уверете се, че можете ефективно да изтеглите нейните данни. За да заобиколите ограниченията, има няколко неща, които можете да опитате. Има ли други IP адреси или сървъри, които хостват същите данни, но нямат същите ограничения? Има ли API крайни точки, които нямат ограничения, докато други имат? При каква скорост на изтегляне вашият IP се блокира и за колко време? Или не сте блокирани, но сте забавени? Какво ще стане, ако създадете потребителски акаунт, как се променят нещата тогава? Можете ли да използвате HTTP/2, за да поддържате връзките отворени и увеличава ли това скоростта, с която можете да заявявате страници? Има ли страници, които изброяват няколко файла наведнъж и достатъчна ли е информацията, изброена там? Нещата, които вероятно искате да запазите, включват: Обикновено правим това на два етапа. Първо изтегляме суровите HTML файлове, обикновено директно в MySQL (за да избегнем много малки файлове, за които говорим повече по-долу). След това, в отделна стъпка, преминаваме през тези HTML файлове и ги анализираме в действителни MySQL таблици. По този начин не е нужно да изтегляте всичко отначало, ако откриете грешка в кода си за анализ, тъй като можете просто да преработите HTML файловете с новия код. Също така често е по-лесно да се паралелизира стъпката на обработка, като по този начин се спестява време (и можете да напишете кода за обработка, докато извличането работи, вместо да трябва да пишете и двете стъпки наведнъж). И накрая, обърнете внимание, че за някои цели извличането на метаданни е всичко, което има. Има някои огромни колекции от метаданни, които не са правилно запазени. Заглавие Избор на домейн / философия: Къде приблизително искате да се фокусирате и защо? Какви са вашите уникални страсти, умения и обстоятелства, които можете да използвате в своя полза? Избор на цел: Коя конкретна колекция ще огледате? Изстъргване на metadata: Каталогизиране на информация за файловете, без всъщност да се изтеглят (често много по-големите) файлове. Избор на данни: Въз основа на metadata, стесняване на това кои данни са най-релевантни за архивиране в момента. Може да бъде всичко, но често има разумен начин за спестяване на пространство и честотна лента. Изстъргване на данни: Всъщност получаване на данните. Разпространение: Опаковане в торенти, обявяване някъде, привличане на хора да го разпространят. 5. Извличане на данни Сега сте готови да изтеглите данните в голям обем. Както беше споменато по-рано, на този етап вече трябва да сте изтеглили ръчно куп файлове, за да разберете по-добре поведението и ограниченията на целта. Въпреки това, все още ще има изненади, когато всъщност започнете да изтегляте много файлове наведнъж. Нашият съвет тук е основно да го поддържате просто. Започнете, като просто изтеглите куп файлове. Можете да използвате Python и след това да разширите до множество нишки. Но понякога дори по-просто е да генерирате Bash файлове директно от базата данни и след това да стартирате няколко от тях в няколко терминални прозореца, за да увеличите мащаба. Бърз технически трик, който си струва да се спомене тук, е използването на OUTFILE в MySQL, което можете да напишете навсякъде, ако деактивирате "secure_file_priv" в mysqld.cnf (и не забравяйте също да деактивирате/замените AppArmor, ако сте на Linux). Съхраняваме данните на обикновени твърди дискове. Започнете с каквото имате и разширявайте бавно. Може да бъде поразително да мислите за съхранение на стотици TB данни. Ако това е ситуацията, с която се сблъсквате, просто извадете добър подмножество първо и в съобщението си поискайте помощ за съхранение на останалото. Ако искате да получите повече твърди дискове сами, тогава r/DataHoarder има някои добри ресурси за получаване на добри сделки. Опитайте се да не се притеснявате твърде много за сложни файлови системи. Лесно е да попаднете в заешката дупка на настройването на неща като ZFS. Една техническа подробност, за която трябва да сте наясно обаче, е, че много файлови системи не се справят добре с много файлове. Открихме, че простото решение е да създадете множество директории, например за различни диапазони от ID или хеш префикси. След изтеглянето на данните, не забравяйте да проверите целостта на файловете, използвайки хешовете в метаданните, ако са налични. 2. Избор на цел Достъпна: не използва много слоеве защита, за да ви попречи да извличате тяхната metadata и данни. Специална информация: имате някаква специална информация за тази цел, като например специален достъп до тази колекция или сте разбрали как да преодолеете техните защити. Това не е задължително (нашият предстоящ проект не прави нищо специално), но определено помага! Голяма И така, имаме нашата област, която разглеждаме, сега коя конкретна колекция да огледаме? Има няколко неща, които правят една цел добра: Когато намерихме нашите учебници по наука на уебсайтове, различни от Library Genesis, се опитахме да разберем как са се появили в интернет. След това открихме Z-Library и осъзнахме, че макар повечето книги да не се появяват първо там, те в крайна сметка се озовават там. Научихме за връзката му с Library Genesis и структурата на (финансовите) стимули и превъзходния потребителски интерфейс, които го правят много по-пълна колекция. След това направихме предварително извличане на metadata и данни и осъзнахме, че можем да заобиколим ограниченията за изтегляне на IP, използвайки специалния достъп на един от нашите членове до много прокси сървъри. Докато изследвате различни цели, вече е важно да скриете следите си, като използвате VPN и временни имейл адреси, за които ще говорим повече по-късно. Уникална: не е вече добре покрита от други проекти. Когато правим проект, той има няколко фази: Това не са напълно независими фази и често прозрения от по-късна фаза ви връщат към по-ранна фаза. Например, по време на изстъргване на metadata може да осъзнаете, че целта, която сте избрали, има защитни механизми извън вашето ниво на умения (като блокиране на IP), така че се връщате и намирате друга цел. - Анна и екипът (<a %(reddit)s>Reddit</a>) Цели книги могат да бъдат написани за <em>защо</em> на дигиталното съхранение като цяло и пиратския архивизъм в частност, но нека дадем кратко въведение за тези, които не са много запознати. Светът произвежда повече знания и култура от всякога, но също така повече от тях се губят от всякога. Човечеството в голяма степен поверява на корпорации като академични издатели, стрийминг услуги и социални медийни компании това наследство, и те често не са се доказали като добри настойници. Разгледайте документалния филм Digital Amnesia или наистина всяка лекция на Джейсън Скот. Има някои институции, които вършат добра работа по архивирането на колкото се може повече, но те са ограничени от закона. Като пирати, ние сме в уникална позиция да архивираме колекции, които те не могат да докоснат, поради прилагането на авторски права или други ограничения. Можем също така да огледаме колекции многократно по света, като по този начин увеличаваме шансовете за правилно съхранение. Засега няма да влизаме в дискусии за плюсовете и минусите на интелектуалната собственост, моралността на нарушаването на закона, размишленията върху цензурата или въпроса за достъпа до знания и култура. С всичко това настрана, нека се потопим в <em>как</em>. Ще споделим как нашият екип стана пиратски архивисти и уроците, които научихме по пътя. Има много предизвикателства, когато се впуснете в това пътуване, и се надяваме, че можем да ви помогнем с някои от тях. Как да станете пиратски архивист Първото предизвикателство може да бъде изненадващо. То не е технически проблем или правен проблем. То е психологически проблем. Преди да се потопим, две актуализации за Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>): Получихме изключително щедри дарения. Първото беше $10k от анонимен индивид, който също подкрепя "bookwarrior", оригиналния основател на Library Genesis. Специални благодарности на bookwarrior за улесняването на това дарение. Второто беше още $10k от анонимен дарител, който се свърза с нас след последното ни издание и беше вдъхновен да помогне. Имаше и редица по-малки дарения. Благодарим ви много за вашата щедра подкрепа. Имаме някои вълнуващи нови проекти в процес на разработка, които това ще подкрепи, така че останете на линия. Имахме някои технически затруднения с размера на второто ни издание, но нашите торенти вече са активни и се споделят. Също така получихме щедро предложение от анонимен индивид да сподели нашата колекция на своите много високоскоростни сървъри, така че правим специално качване на техните машини, след което всички останали, които изтеглят колекцията, трябва да видят значително подобрение в скоростта. Блог публикации Здравейте, аз съм Анна. Създадох <a %(wikipedia_annas_archive)s>Архива на Анна</a>, най-голямата сенчеста библиотека в света. Това е моят личен блог, в който аз и моите съотборници пишем за пиратство, дигитално съхранение и други теми. Свържете се с мен в <a %(reddit)s>Reddit</a>. Имайте предвид, че този уебсайт е просто блог. Тук хостваме само нашите собствени думи. Няма хоствани или свързани торенти или други защитени с авторски права файлове. <strong>Библиотека</strong> - Както повечето библиотеки, ние се фокусираме основно върху писмени материали като книги. Може да се разширим и в други видове медии в бъдеще. <strong>Огледало</strong> - Ние сме строго огледало на съществуващи библиотеки. Фокусираме се върху запазването, а не върху лесното търсене и изтегляне на книги (достъп) или създаването на голяма общност от хора, които допринасят с нови книги (източници). <strong>Пират</strong> - Ние умишлено нарушаваме закона за авторското право в повечето страни. Това ни позволява да правим нещо, което законните организации не могат: да гарантираме, че книгите са огледани навсякъде. <em>Ние не свързваме файловете от този блог. Моля, намерете ги сами.</em> - Анна и екипът (<a %(reddit)s>Reddit</a>) Този проект (РЕДАКЦИЯ: преместен в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>) има за цел да допринесе за запазването и освобождаването на човешкото знание. Ние правим нашия малък и скромен принос, следвайки стъпките на великите преди нас. Фокусът на този проект е илюстриран от неговото име: Първата библиотека, която сме огледали, е Z-Library. Това е популярна (и незаконна) библиотека. Те са взели колекцията на Library Genesis и са я направили лесно търсима. Освен това, те са станали много ефективни в привличането на нови книжни приноси, като стимулират допринасящите потребители с различни предимства. В момента те не връщат тези нови книги обратно на Library Genesis. И за разлика от Library Genesis, те не правят колекцията си лесно огледаема, което пречи на широкото запазване. Това е важно за техния бизнес модел, тъй като те таксуват за достъп до колекцията им в големи количества (повече от 10 книги на ден). Ние не правим морални преценки относно таксуването за масов достъп до незаконна колекция от книги. Без съмнение, Z-Library е успешна в разширяването на достъпа до знания и в намирането на повече книги. Ние сме тук, за да изпълним нашата част: да осигурим дългосрочното съхранение на тази частна колекция. Бихме искали да ви поканим да помогнете за запазването и освобождаването на човешкото знание, като изтеглите и сеете нашите торенти. Вижте страницата на проекта за повече информация относно организацията на данните. Също така много бихме искали да допринесете с вашите идеи за това кои колекции да огледаме следващите и как да го направим. Заедно можем да постигнем много. Това е само малък принос сред безброй други. Благодарим ви за всичко, което правите. Представяме Пиратското библиотечно огледало: Запазване на 7TB книги (които не са в Libgen) 10% of писменото наследство на човечеството запазено завинаги <strong>Google.</strong> В крайна сметка, те направиха това изследване за Google Books. Въпреки това, тяхната metadata не е достъпна в насипно състояние и е доста трудно да се извлече. <strong>Различни индивидуални библиотечни системи и архиви.</strong> Има библиотеки и архиви, които не са индексирани и агрегирани от никой от горепосочените, често защото са недофинансирани или по други причини не искат да споделят данните си с Open Library, OCLC, Google и т.н. Много от тях имат цифрови записи, достъпни чрез интернет, и често не са много добре защитени, така че ако искате да помогнете и да се забавлявате, учейки за странни библиотечни системи, това са страхотни начални точки. <strong>ISBNdb.</strong> Това е темата на този блог пост. ISBNdb извлича данни от различни уебсайтове за metadata на книги, по-специално данни за цени, които след това продават на книжарници, за да могат да определят цените на книгите си в съответствие с останалата част от пазара. Тъй като ISBN са доста универсални в наши дни, те ефективно изградиха „уеб страница за всяка книга“. <strong>Open Library.</strong> Както беше споменато по-рано, това е тяхната цяла мисия. Те са събрали огромни количества библиотечни данни от сътрудничещи библиотеки и национални архиви и продължават да го правят. Те също така имат доброволци библиотекари и технически екип, които се опитват да дедупликират записи и да ги маркират с всякакви metadata. Най-хубавото е, че техният dataset е напълно отворен. Можете просто да го <a %(openlibrary)s>изтеглите</a>. <strong>WorldCat.</strong> Това е уебсайт, управляван от неправителствената организация OCLC, която продава системи за управление на библиотеки. Те агрегират metadata за книги от много библиотеки и я правят достъпна чрез уебсайта на WorldCat. Въпреки това, те също така печелят пари, продавайки тези данни, така че не са достъпни за насипно изтегляне. Те имат някои по-ограничени насипни datasets, достъпни за изтегляне, в сътрудничество с конкретни библиотеки. 1. За някакво разумно определение на „завинаги“. ;) 2. Разбира се, писменото наследство на човечеството е много повече от книги, особено в днешно време. За целите на този пост и нашите последни издания се фокусираме върху книгите, но интересите ни се простират по-далеч. 3. Има много повече, което може да се каже за Аарон Суорц, но просто искахме да го споменем накратко, тъй като той играе ключова роля в тази история. С времето повече хора може да се натъкнат на името му за първи път и да се потопят в заешката дупка сами. <strong>Физически копия.</strong> Очевидно това не е много полезно, тъй като те са просто дубликати на същия материал. Би било страхотно, ако можем да съхраним всички анотации, които хората правят в книгите, като известните „драсканици в полетата“ на Ферма. Но уви, това ще остане мечта на архивиста. <strong>„Издания“.</strong> Тук броите всяка уникална версия на книга. Ако нещо в нея е различно, като различна корица или различен предговор, това се брои за различно издание. <strong>Файлове.</strong> Когато работите с библиотеки в сянка като Library Genesis, Sci-Hub или Z-Library, има допълнително съображение. Може да има множество сканирания на едно и също издание. И хората могат да правят по-добри версии на съществуващи файлове, като сканират текста с OCR или коригират страници, които са сканирани под ъгъл. Искаме да броим тези файлове само като едно издание, което би изисквало добро metadata или дедупликация чрез мерки за сходство на документи. <strong>„Произведения“.</strong> Например „Хари Потър и Стаята на тайните“ като логическа концепция, обхващаща всички версии на нея, като различни преводи и преиздания. Това е вид полезна дефиниция, но може да бъде трудно да се определи какво се брои. Например, вероятно искаме да съхраним различни преводи, въпреки че преизданията с малки разлики може да не са толкова важни. - Анна и екипът (<a %(reddit)s>Reddit</a>) С Пиратското библиотечно огледало (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>), нашата цел е да вземем всички книги в света и да ги запазим завинаги.<sup>1</sup> Между нашите торенти на Z-Library и оригиналните торенти на Library Genesis, имаме 11,783,153 файла. Но колко е това наистина? Ако правилно дедеуплицираме тези файлове, какъв процент от всички книги в света сме запазили? Наистина бихме искали да имаме нещо такова: Нека започнем с някои груби числа: В Z-Library/Libgen и Open Library има много повече книги, отколкото уникални ISBN. Това означава ли, че много от тези книги нямат ISBN, или просто липсва metadata за ISBN? Вероятно можем да отговорим на този въпрос с комбинация от автоматизирано съвпадение на базата на други атрибути (заглавие, автор, издател и т.н.), привличане на повече източници на данни и извличане на ISBN от самите сканирания на книги (в случая на Z-Library/Libgen). Колко от тези ISBN са уникални? Това е най-добре илюстрирано с диаграма на Вен: За да бъдем по-точни: Бяхме изненадани колко малко съвпадения има! ISBNdb има огромно количество ISBN, които не се появяват нито в Z-Library, нито в Open Library, и същото важи (в по-малка, но все пак значителна степен) за другите две. Това повдига много нови въпроси. Колко би помогнало автоматизираното съвпадение при маркирането на книгите, които не са маркирани с ISBN? Ще има ли много съвпадения и следователно увеличено припокриване? Също така, какво би се случило, ако добавим четвърти или пети набор от данни? Колко припокриване ще видим тогава? Това ни дава начална точка. Сега можем да разгледаме всички ISBN, които не бяха в набора от данни на Z-Library и които не съвпадат с полетата за заглавие/автор. Това може да ни даде възможност да запазим всички книги в света: първо чрез събиране на сканирания от интернет, след това чрез излизане в реалния живот за сканиране на книги. Последното дори може да бъде финансирано от тълпата или подтикнато от „награди“ от хора, които биха искали да видят определени книги дигитализирани. Всичко това е история за друг път. Ако искате да помогнете с някое от тези неща — по-нататъшен анализ; събиране на повече metadata; намиране на повече книги; OCR на книги; правене на това за други области (например статии, аудиокниги, филми, телевизионни предавания, списания) или дори предоставяне на част от тези данни за неща като ML / обучение на големи езикови модели — моля, свържете се с мен (<a %(reddit)s>Reddit</a>). Ако сте специално заинтересовани от анализа на данни, работим върху това да направим нашите набори от данни и скриптове достъпни в по-лесен за използване формат. Би било чудесно, ако можете просто да клонирате тетрадка и да започнете да експериментирате с това. Накрая, ако искате да подкрепите тази работа, моля, обмислете възможността да направите дарение. Това е изцяло доброволческа операция и вашият принос прави огромна разлика. Всяка помощ е от значение. Засега приемаме дарения в криптовалута; вижте страницата за дарения в Архива на Анна. За процент, ни трябва знаменател: общият брой на книгите, публикувани някога.<sup>2</sup> Преди края на Google Books, инженерът на проекта, Леонид Тайчър, <a %(booksearch_blogspot)s>се опита да оцени</a> този брой. Той стигна — с ирония — до 129,864,880 („поне до неделя“). Той оцени този брой, като създаде обединена база данни на всички книги в света. За това, той събра различни Datasets и след това ги обедини по различни начини. Като кратко отклонение, има още един човек, който се опита да каталогизира всички книги в света: Аарон Суорц, покойният дигитален активист и съосновател на Reddit.<sup>3</sup> Той <a %(youtube)s>започна Open Library</a> с целта „една уеб страница за всяка книга, публикувана някога“, комбинирайки данни от много различни източници. В крайна сметка той плати най-високата цена за своята работа по дигитално съхранение, когато беше преследван за масово изтегляне на академични статии, което доведе до неговото самоубийство. Ненужно е да казваме, че това е една от причините нашата група да е псевдонимна и защо сме много внимателни. Open Library все още се управлява героично от хората в Internet Archive, продължавайки наследството на Аарон. Ще се върнем към това по-късно в този пост. В блога на Google, Тайчър описва някои от предизвикателствата при оценката на този брой. Първо, какво представлява книга? Има няколко възможни дефиниции: „Издания“ изглежда най-практичната дефиниция за това какво са „книги“. Удобно, тази дефиниция също се използва за присвояване на уникални ISBN номера. ISBN, или Международен стандартен книжен номер, обикновено се използва за международна търговия, тъй като е интегриран с международната система за баркодове („Международен номер на артикул“). Ако искате да продавате книга в магазините, тя се нуждае от баркод, така че получавате ISBN. Блог постът на Тайчър споменава, че докато ISBN са полезни, те не са универсални, тъй като наистина са приети едва в средата на седемдесетте години и не навсякъде по света. Все пак, ISBN вероятно е най-широко използваният идентификатор на книжни издания, така че това е нашата най-добра начална точка. Ако можем да намерим всички ISBN в света, получаваме полезен списък на книгите, които все още трябва да бъдат съхранени. И така, откъде да вземем данните? Има няколко съществуващи усилия, които се опитват да съставят списък на всички книги в света: В този пост сме щастливи да обявим малко издание (в сравнение с предишните ни издания на Z-Library). Извлякохме по-голямата част от ISBNdb и направихме данните достъпни за торент на уебсайта на Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>; няма да го свързваме директно тук, просто го потърсете). Това са около 30.9 милиона записа (20GB като <a %(jsonlines)s>JSON Lines</a>; 4.4GB компресирани). На техния уебсайт те твърдят, че всъщност имат 32.6 милиона записа, така че може би по някакъв начин сме пропуснали някои, или <em>те</em> може да правят нещо грешно. Във всеки случай, засега няма да споделяме точно как го направихме — ще оставим това като упражнение за читателя. ;-) Това, което ще споделим, е някакъв предварителен анализ, за да се опитаме да се приближим до оценката на броя на книгите в света. Разгледахме три datasets: този нов dataset на ISBNdb, нашето оригинално издание на metadata, което извлякохме от библиотеката в сянка Z-Library (която включва Library Genesis), и данните от Open Library. ISBNdb дъмп, или Колко книги са запазени завинаги? Ако правилно дедеуплицираме файловете от библиотеките в сянка, какъв процент от всички книги в света сме запазили? Актуализации за <a %(wikipedia_annas_archive)s>Архивът на Анна</a>, най-голямата наистина отворена библиотека в човешката история. <em>WorldCat редизайн</em> Данни <strong>Формат?</strong> <a %(blog)s>Контейнери на Архива на Анна (AAC)</a>, които по същество са <a %(jsonlines)s>JSON Lines</a>, компресирани със <a %(zstd)s>Zstandard</a>, плюс някои стандартизирани семантики. Тези контейнери обхващат различни видове записи, базирани на различните извлечения, които сме използвали. Преди година <a %(blog)s>се заехме</a> да отговорим на този въпрос: <strong>Какъв процент от книгите са били трайно запазени от библиотеки в сянка?</strong> Нека разгледаме някои основни данни за информацията: След като една книга попадне в библиотека в сянка с отворени данни като <a %(wikipedia_library_genesis)s>Library Genesis</a>, а сега и <a %(wikipedia_annas_archive)s>Архивът на Анна</a>, тя се огледаля по целия свят (чрез торенти), като по този начин практически се запазва завинаги. За да отговорим на въпроса какъв процент от книгите е запазен, трябва да знаем знаменателя: колко книги съществуват общо? Идеално би било да имаме не просто число, а действителни metadata. Тогава можем не само да ги съпоставим с библиотеките в сянка, но и <strong>да създадем списък със задачи за останалите книги, които да бъдат запазени!</strong> Можем дори да започнем да мечтаем за усилие от страна на общността да премине през този списък със задачи. Извлякохме данни от <a %(wikipedia_isbndb_com)s>ISBNdb</a> и изтеглихме <a %(openlibrary)s>Open Library dataset</a>, но резултатите бяха незадоволителни. Основният проблем беше, че нямаше много припокриване на ISBN. Вижте тази диаграма на Вен от <a %(blog)s>нашия блог пост</a>: Бяхме много изненадани от това колко малко припокриване имаше между ISBNdb и Open Library, и двете от които щедро включват данни от различни източници, като уеб скрейпинг и библиотечни записи. Ако и двете вършат добра работа в намирането на повечето ISBN, техните кръгове със сигурност биха имали значително припокриване или единият би бил подмножество на другия. Това ни накара да се запитаме, колко книги попадат <em>напълно извън тези кръгове</em>? Нуждаем се от по-голяма база данни. Тогава насочихме вниманието си към най-голямата база данни за книги в света: <a %(wikipedia_worldcat)s>WorldCat</a>. Това е собствена база данни на неправителствената организация <a %(wikipedia_oclc)s>OCLC</a>, която събира metadata записи от библиотеки по целия свят, в замяна на предоставяне на тези библиотеки достъп до пълния набор от данни и показването им в резултатите от търсенето на крайните потребители. Въпреки че OCLC е неправителствена организация, техният бизнес модел изисква защита на тяхната база данни. Е, съжаляваме да кажем, приятели от OCLC, ние ще я раздадем на всички. :-) През изминалата година внимателно извлякохме всички записи от WorldCat. В началото имахме късмет. WorldCat тъкмо пускаше пълния редизайн на своя уебсайт (през август 2022 г.). Това включваше значителна преработка на техните бекенд системи, въвеждайки много пропуски в сигурността. Веднага се възползвахме от възможността и успяхме да извлечем стотици милиони (!) записи само за няколко дни. След това пропуските в сигурността бяха бавно поправяни един по един, докато последният, който намерихме, беше закърпен преди около месец. До този момент имахме почти всички записи и се стремяхме само към малко по-висококачествени записи. Така че почувствахме, че е време да ги пуснем! 1.3B WorldCat извличане <em><strong>Накратко:</strong> Архивът на Анна изстърга целия WorldCat (най-голямата в света колекция от библиотечни metadata), за да създаде списък със задачи на книги, които трябва да бъдат запазени.</em> WorldCat Предупреждение: тази публикация в блога е остаряла. Решихме, че IPFS все още не е готов за основно използване. Все още ще свързваме към файлове на IPFS от Архива на Анна, когато е възможно, но няма да го хостваме сами повече, нито препоръчваме на други да огледалят, използвайки IPFS. Моля, вижте нашата страница за торенти, ако искате да помогнете за запазването на нашата колекция. Помогнете за разпространението на Z-Library в IPFS Изтегляне от партньорски сървър SciDB Външен заем Външно заемане (печатът е деактивиран) Външно изтегляне Разгледайте метаданните Съдържа се в торенти Назад  (+%(num)s бонус) неплатен платен отменен просрочен чакам Анна да потвърди невалиден Текстът долу е само на Английски. Отиди Нулиране Напред Последен Ако имейл адресът Ви не работи във форумите на Libgen, препоръчваме да използвате <a %(a_mail)s>Proton Mail</a> (безплатно). Можете също така да <a %(a_manual)s>поискате активация</a> за акаунта Ви. (може да изисква <a %(a_browser)s>проверка на браузъра</a> - неограничени изтегляния!) Върз сървър в партньорство с нас №%(number)s (препоръчително) (малко по-бързо, но с листа на изчакване) (не се изисква проверка на браузъра) (без проверка на браузъра или списъци на чакащи) (няма списък на чакащите, но може да бъде много бавно) Бавен сървър в партньорство с нас №%(number)s Аудиокнига Комикс Книга (художествена) Книга (нехудожествена) Книга (непозната) Журнална статия Списание Музикални точки Други Стандарти Не всички страници могат да бъдат конвертирани в PDF Маркирано като развалено в Libgen.li Не е видимо в Libgen.li Не е видимо в Libgen.rs Художествена литература Не е видимо в Libgen.rs Нехудожествена литература Неуспешно изпълнение на exiftool върху този файл Отбелязано като “лош файл” в Z-Library Липсва в Z-Library Отбелязано като “спам” в Z-Library Файлът не може да бъде отворен (напр. повреден файл, DRM) Иск за авторски права Проблеми с изтеглянето (напр. невъзможност за свързване, съобщение за грешка, много бавно) Неправилни метаданни (напр. заглавие, описание, изображение на корицата) Други Лошо качество (напр. проблеми с форматирането, лошо качество на сканиране, липсващи страници) Спам / файлът трябва да бъде премахнат (напр. реклама, обидно съдържание) %(amount)s (%(amount_usd)s) %(amount)s общо %(amount)s (%(amount_usd)s) общо Брилянтен книжен червей Щастлив библиотекар Ослепителен събирач на данни Невероятен архивист Бонус изтегляния Cerlalc Чешки метаданни DuXiu 读秀 китайски сайт за книги EBSCOhost eBook Index Google Книги Goodreads HathiTrust IA IA Контролирано цифрово заемане ISBNdb-събира данни от стотици библиотеки, издатели, търговци и други източници по целия свят, които могат да се търсят по ISBN, заглавие, автор или издател ISBN GRP Libgen.li Изключвайки “scimag” Libgen.rs Нехудожествена и Художествена литература Libby MagzDB Nexus/STC OCLC (WorldCat) - е най-изчерпателната в света база данни с информация за библиотечните колекции OpenLibrary - е библиотечен каталог, изграждащ уеб страница за всяка книга, публикувана някога Руска държавна библиотека Sci-Hub - библиотека в сянка, с безплатен достъп до милиони научни статии, включително и платени Чрез Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Качвания в AA Z-Library - най-голямата библиотека с електронни книги в света Z-Library Chinese Заглавие за търсене, автор, език, тип файл, ISBN, MD5, … Търси Автор Коментари за описание и метаданни Издание Оригиналното име на файла Издател (търсене в конкретно поле) Заглавие Година на публикуване Покажи техническите детайли (на Английски) Тази монета има по-висока стойност от обичайния минимум. Моля, изберете различна продължителност или друга монета. Заявката не можа да бъде изпълнена. Опитайте отново след няколко минути и ако това продължи да се случва, свържете се с нас на %(email)s с екранна снимка. Възникна непозната грешка. Свържете се с нас на %(email)s с екранна снимка. Грешка при обработката на плащането. Моля, изчакайте малко и опитайте отново. Ако проблемът продължава повече от 24 часа, моля, свържете се с нас на %(email)s с екранна снимка. Провеждаме кампания за набиране на средства за <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">архивиране</a> най-голямата комиксова библиотека в света. Благодаря за подкрепата! <a href="/donate">Дарете.</a> Ако не можете да дарите, помислете дали да ни подкрепите, като кажете на приятелите си и ни последвате <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>. Не ни изпращайте имейл до <a %(a_request)s>заявка за книги</a><br>или малък (<10k) <a %(a_upload)s>качвания</a>. Архивът на Анна DMCA / искове за авторски права Поддържате връзка Reddit Алтернативи SLUM (%(unaffiliated)s) несвързан Архивът на Анна се нуждае от вашата помощ! Ако дарите сега, получавате <strong>двойно</strong> повече бързи изтегляния. Много се опитват да ни свалят, но ние се борим да отвърнем на удара. Ако дарите този месец, получавате <strong>двойно</strong> повече бързи изтегляния. Валидно до края на този месец. Спасяване на човешкото знание: страхотен празничен подарък! Членствата ще бъдат удължени съответно. Партньорските сървъри са недостъпни поради затваряне на хостинг услугите. Те трябва да бъдат скоро отново активни . За да увеличим устойчивостта на Архивът на Анна, търсим доброволци за поддържане на огледални сървъри. Разполагаме с нов метод за дарение: %(method_name)s. Моля имайте предвид %(donate_link_open_tag)sдаряване</a> — не е евтино да управлявате този уебсайт и вашето дарение наистина има значение. Много благодаря. Препоръчайте приятел и двамата ще получите %(percentage)s%% бонус бързи изтегляния! Изненадайте любим човек, подарете му акаунт с членство. Перфектният подарък за Свети Валентин! Научете повече… Акаунт Активност Разширено Блога на Анна ↗ Софтуерът на Анна ↗ бета Изследовател на кодове Датасетс Дари Изтеглени файлове ЧЗВ Начална страница Подобрете метаданните LLM данни Вход / Регистрация Моите дарения Публичен профил Търси Сигурност Торенти Превеждане ↗ Доброволчество & награди Последни изтегляния: 📚&nbsp;Най-голямата в света библиотека с отворени данни с отворен код. ⭐️&nbsp;Огледалата Sci-Hub, Library Genesis, Z-Library, и още други. 📈&nbsp;%(book_any)s книги, %(journal_article)s документи, %(book_comic)s комикси, %(magazine)s списания — запазени завинаги.  и  и още DuXiu - е търсачка, специализирана за китайски академични материали Интернет архивна библиотека за заемане LibGen - позволява безплатен достъп до съдържание, което е платено 📚&nbsp;Най-голямата в историята отворена библиотека. 📈&nbsp;%(book_count)s&nbsp;книги, %(paper_count)s&nbsp;документи — запазени завинаги. ⭐️&nbsp;Ние сме огледало на %(libraries)s. Ние събираме и публикуваме безплатно %(scraped)s. Целият ни код и данни са с напълно отворени. OpenLib - е библиотечен каталог, изграждащ страница за всяка книга, публикувана някога Sci-Hub - сайт, който предоставя безплатен достъп до милиони научни статии ,  📚 Най-голямата в света библиотека с данни с отворен код.<br>⭐️ Огледалата Scihub, Libgen, Zlib, и още. Z-Lib - сайт за електронни книги Архивът на Анна Невалидна заявка. Посетете %(websites)s. Най-голямата в света библиотека с отворени данни с отворен код. Огледала Sci-Hub, Library Genesis, Z-Library и др. Търсене в архива на Анна Архивът на Анна Моля, обновете страницата, за да опитате отново. <a %(a_contact)s>Свържете се с нас</a>, ако проблемът продължава няколко часа. 🔥 Проблем при зареждане на тази страница <li>1. Следвайте ни на <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, или <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Разпространете думата за Архивът на Анна в Twitter, Reddit, Tiktok, Instagram, във Вашето локално кафене или библиотека, или където и да отидете! Ние не вярваме в недостъпността — ако ни свалят, ще изскочим отново на друго място, защото целият ни код е отворен.</li><li>3. Ако имате възможност, обмислете да <a href="/donate">дарите</a>.</li><li>4. Помогнете да<a href="https://translate.annas-software.org/">преведем</a> нашия уебсайт на различни езици.</li><li>5. Ако сте софтуерен инженер, обмислете да допринесете към нашия <a href="https://annas-software.org/">отворен код</a>, или като правите достъпни нашите <a href="https://en.wikipedia.org/wiki/Pirate_Library_Mirror">торенти</a>.</li> 10. Създайте или помогнете да поддържаме страницата в Уикипедия за архива на Анна на вашия език. 11. Търсим да поставим малки реклами с вкус. Ако искате да рекламирате в архива на Анна, уведомете ни. 6. Ако сте изследовател по сигурността, можем да използваме вашите умения както за нападение, така и за защита. Вижте нашата страница <a %(a_security)s>Сигурност</a>. 7. Търсим експерти по плащания за анонимни търговци. Можете ли да ни помогнете да добавим по-удобни начини за даряване? PayPal, WeChat, подаръчни карти. Ако познавате някого, свържете се с нас. 8. Винаги търсим повече капацитет на сървъра. 9. Можете да помогнете, като докладвате за проблеми с файлове, оставяте коментари и създавате списъци направо на този сайт. Можете също така да помогнете, като <a %(a_upload)s>качите още книги</a> или коригирате проблеми с файловете или форматирате съществуващите книги. За по-подробна информация как да бъдеш доброволец, вижте нашата <a %(a_volunteering)s>страница за доброволчество и награди</a>. Ние силно вярваме в свободния поток на информация и запазването на знанието и културата. С тази търсачка ние надграждаме на раменете на гиганти. Ние дълбоко уважаваме упоритата работа на хората, които са създали различните скрити библиотеку, и се надяваме, че тази търсачка ще разшири обхвата им. За да бъдете информирани за напредъка ни, следвайте Анна на <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> или <a href="https://t.me/annasarchiveorg">Telegram</a>. За въпроси и обратна връзка, моля, свържете се с Анна на %(email)s. ID на акаунт: %(account_id)s Излизане ❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново. ✅Вече сте излезли. Презаредете страницата, за да влезете отново. Използвани са бързи изтегляния (последните 24 часа): <strong>%(used)s / %(total)s</strong> Членство: <strong>%(tier_name)s</strong> до %(until_date)s <a %(a_extend)s>(удължаване)</a> Можете да комбинирате няколко членства (бързите изтегляния за 24 часа ще бъдат добавени заедно). Членство: <strong>Няма</strong> <a %(a_become)s>(станете член)</a> Свържете се с Anna на %(email)s, ако се интересувате от надграждане на членството си до по-високо ниво. Публичен профил: %(profile_link)s Таен ключ (don’t share!): %(secret_key)s показване Присъединете се към нас тук! Надстройте до <a %(a_tier)s>по-високо ниво</a>, за да се присъедините към нашата група. Изключителна група в Telegram: %(link)s Акаунт кои изтегляния? Вход Не губете ключа си! Невалиден таен ключ. Проверете ключа си и опитайте отново или регистрирайте нов акаунт по-долу. Таен ключ Въведете тайния си ключ, за да влезете в системата: Имате стар акаунт, базиран на имейл? Въведете <a %(a_open)s>имейла си тук</a>. Регистрирайте нов акаунт Още нямате акаунт? Успешна регистрация! Тайният Ви ключ е <span %(span_key)s>%(key)s</span> Внимателно запаметете този ключ. Ако го изгубите, ще загубите достъп до профила си. <li %(li_item)s><strong>Отметка.</strong> Може да добавите отметка за тази препратка, за да намерите ключа си по-лесно.</li><li %(li_item)s><strong>Изтегли.</strong> Натиснете <a %(a_download)s>тази препратка</a>, за да изтеглите ключа си.</li><li %(li_item)s><strong>Управление на пароли.</strong> Използвайте софтуер за управление на пароли, за да запазите ключа, когато го въведете долу.</li> Вход / Регистриране Проверка на браузъра Предупреждение: кодът съдържа неправилни Unicode символи и може да се държи неправилно в различни ситуации. Суровият двоичен код може да бъде декодиран от base64 представянето в URL. Описание Етикет Префикс URL за конкретен код Сайт Кодове започващи с “%(prefix_label)s” Моля, не изтъргвайте тези страници. Вместо това, препоръчваме <a %(a_import)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB и изпълнение на нашия <a %(a_software)s>отворен код</a>. Суровите данни могат да бъдат ръчно изследвани чрез JSON файлове като <a %(a_json_file)s>този</a>. По-малко от %(count)s записа Общ URL Изследовател на кодове Индекс на Разгледайте кодовете, с които записите са маркирани, по префикс. Колоната “записи” показва броя на записите, маркирани с кодове с дадения префикс, както се вижда в търсачката (включително записите само с метаданни). Колоната “кодове” показва колко действителни кода имат даден префикс. Познат кодов префикс “%(key)s” Още… Префикс %(count)s запис съвпадащ с “%(prefix_label)s” %(count)s записа съвпадащи с “%(prefix_label)s” кодове записи “%%s” ще бъде заменено със стойността на кода Търсене в Архива на Анна Кодове URL за конкретен код: “%(url)s” Тази страница може да отнеме време за да се генерира, поради което изисква Cloudflare captcha. <a %(a_donate)s>Членовете</a> могат да пропуснат captcha. Злоупотребата е докладвана: По-добра версия Искате ли да докладвате този потребител за злоупотреба или неподходящо поведение? Проблем с файла: %(file_issue)s скрит коментар Отговор Докладване на злоупотреба Вие докладвахте този потребител за злоупотреба. Претенциите за авторски права към този имейл ще бъдат игнорирани; използвайте формуляра вместо това. Показване на имейл Много ще се радваме на вашите отзиви и въпроси! Въпреки това, поради количеството спам и безсмислени имейли, които получаваме, моля, отметнете кутиите, за да потвърдите, че разбирате тези условия за контакт с нас. Всички други начини за контакт с нас относно претенции за авторски права ще бъдат автоматично изтрити. За DMCA / искове за авторски права, използвайте <a %(a_copyright)s>тази форма</a>. Имейл за връзка Адреси в Архива на Анна (задължително). По един на ред. Моля, включвайте само адреси, които описват точно същото издание на книга. Ако искате да подадете иск за множество книги или множество издания, моля, подайте този формуляр няколко пъти. Искове, които обединяват множество книги или издания, ще бъдат отхвърлени. Адрес (задължително) Ясно описание на изходния материал (задължително) Имейл (задължително) Адреси на изходния материал, по един на ред (задължително). Моля, включвайте колкото се може повече, за да ни помогнете да потвърдим вашия иск (напр. Amazon, WorldCat, Google Books, DOI). ISBN номера на изходния материал (ако е приложимо). По един на ред. Моля, включвайте само тези, които точно съвпадат с изданието, за което подавате иск за авторските права. Вашето име (задължително) ❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново. ✅ Благодарим ви, че подадохте своя иск за авторски права. Ще го прегледаме възможно най-скоро. Моля, презаредете страницата, за да подадете нов иск. <a %(a_openlib)s>Open Library</a> адреси на изходния материал, по един на ред. Моля, отделете момент, за да потърсите изходния си материал в Open Library. Това ще ни помогне да потвърдим вашия иск. Телефонен номер (задължително) Декларация и подпис (задължително) Подайте иск Ако имате иск по DMCA или друг иск за авторски права, моля, попълнете този формуляр възможно най-точно. Ако срещнете някакви проблеми, моля, свържете се с нас на специалния ни адрес за DMCA: %(email)s. Обърнете внимание, че искове, изпратени на този адрес по имейл, няма да бъдат обработвани, той е само за въпроси. Моля, използвайте формуляра по-долу, за да подадете своите искове. Формуляр за искове по DMCA / Авторски права Примерен запис в Архива на Анна Торенти от Архива на Анна Формат на Контейнерите на Архива на Анна Скриптове за импортиране на метаданни Ако се интересувате от огледално копиране на този набор от данни за <a %(a_archival)s>архивиране</a> или за <a %(a_llm)s>обучение на LLM</a>, моля, свържете се с нас. Последно обновено: %(date)s Основен %(source)s сайт Документация на метаданни (повечето полета) Файлове, огледални от Архива на Анна: %(count)s (%(percent)s%%) Ресурси Общо файлове: %(count)s Общ размер на файловете: %(size)s Нашата публикация в блога за тези данни <a %(duxiu_link)s>Duxiu</a> е огромна база данни от сканирани книги, създадена от <a %(superstar_link)s>SuperStar Digital Library Group</a>. Повечето са академични книги, сканирани с цел да бъдат достъпни дигитално за университети и библиотеки. За нашата англоговоряща аудитория, <a %(princeton_link)s>Принстън</a> и <a %(uw_link)s>Университетът на Вашингтон</a> имат добри прегледи. Има и отлична статия, която дава повече информация: <a %(article_link)s>“Дигитализиране на китайски книги: Казус на търсачката SuperStar DuXiu Scholar”</a>. Книгите от Duxiu отдавна се пиратстват в китайския интернет. Обикновено се продават за по-малко от долар от препродавачи. Те обикновено се разпространяват чрез китайския еквивалент на Google Drive, който често е хакнат, за да позволи повече място за съхранение. Някои технически подробности могат да бъдат намерени <a %(link1)s>тук</a> и <a %(link2)s>тук</a>. Въпреки че книгите са били полуоткрито разпространявани, е доста трудно да се получат в големи количества. Това беше високо в нашия списък със задачи и отделихме няколко месеца пълно работно време за това. Въпреки това, в края на 2023 г. невероятен, удивителен и талантлив доброволец се свърза с нас, казвайки ни, че вече е свършил цялата тази работа — на големи разходи. Те споделиха цялата колекция с нас, без да очакват нищо в замяна, освен гаранцията за дългосрочно съхранение. Наистина забележително. Повече информация от нашите доброволци (сурови бележки): Адаптирано от нашата <a %(a_href)s>публикация в блога</a>. DuXiu 读秀 %(count)s файл %(count)s файлове Този набор от данни е тясно свързан с <a %(a_datasets_openlib)s>набора от данни на Open Library</a>. Той съдържа изтегляне на всички метаданни и голяма част от файловете от Контролираната цифрова библиотека на IA. Актуализациите се пускат във <a %(a_aac)s>формат на контейнери на Архива на Анна</a>. Тези записи се отнасят директно към набора от данни на Open Library, но също така съдържат записи, които не са в Open Library. Имаме и редица файлове с данни, изтеглени от членове на общността през годините. Колекцията се състои от две части. Нуждаете се и от двете части, за да получите всички данни (с изключение на заменените торенти, които са зачеркнати на страницата с торенти). Цифрова библиотека за заемане нашето първо издание, преди да стандартизираме на формата <a %(a_aac)s>Контейнери на Архива на Анна (AAC)</a>. Съдържа метаданни (като json и xml), pdf файлове (от цифровите системи за заемане acsm и lcpdf) и миниатюри на кориците. добавяне на нови издания, използващи AAC. Съдържа само метаданни с времеви печати след 2023-01-01, тъй като останалото вече е покрито от “ia”. Също така всички pdf файлове, този път от системите за заемане acsm и “bookreader” (уеб четецът на IA). Въпреки че името не е съвсем точно, все пак попълваме файловете на bookreader в колекцията ia2_acsmpdf_files, тъй като те са взаимно изключващи се. IA Контролирано дигитално заемане 98%%+ от файловете са с възможност за търсене. Нашата мисия е да архивираме всички книги в света (както и статии, списания и др.) и да ги направим широко достъпни. Вярваме, че всички книги трябва да бъдат огледално копирани широко, за да се осигури излишък и устойчивост. Затова събираме файлове от различни източници. Някои източници са напълно отворени и могат да бъдат огледално копирани на едро (като Sci-Hub). Други са затворени и защитени, затова се опитваме да ги изстържем, за да “освободим” техните книги. Трети попадат някъде по средата. Всички наши данни могат да бъдат <a %(a_torrents)s>торентирани</a>, а всички наши метаданни могат да бъдат <a %(a_anna_software)s>генерирани</a> или <a %(a_elasticsearch)s>изтеглени</a> като бази данни ElasticSearch и MariaDB. Суровите данни могат да бъдат ръчно разгледани чрез JSON файлове като <a %(a_dbrecord)s>този</a>. Метаданни Уебсайт за ISBN Последно обновено: %(isbn_country_date)s (%(link)s) Ресурси Международната агенция за ISBN редовно публикува диапазоните, които е разпределила на националните агенции за ISBN. От това можем да определим към коя страна, регион или езикова група принадлежи този ISBN. В момента използваме тези данни косвено, чрез Python библиотеката <a %(a_isbnlib)s>isbnlib</a>. Информация за страната на ISBN Това е извадка от много заявки към isbndb.com през септември 2022 г. Опитахме се да покрием всички диапазони на ISBN. Това са около 30.9 милиона записа. В техния сайт те имат 32.6 милиона записа, така че може би сме пропуснали някои, или <em>те</em> може да правят нещо грешно. JSON отговорите са доста необработени от техния сървър. Един проблем с качеството на данните, който забелязахме, е че за ISBN-13, които започват с различен префикс от “978-”, те все още включват поле “isbn”, което просто е ISBN-13 като първите 3 цифри са отрязани (и проверочната цифра преизчислена). Това очевидно е погрешно, но изглежда така го правят, така че не сме го променили. Друг потенциален проблем, с който може да се сблъскате, е фактът, че полето “isbn13” има дубликати, така че не можете да го използвате като първичен ключ в база данни. Полетата “isbn13”+“isbn” комбинирани изглежда са уникални. Издание 1 (2022-10-31) Торентите за художествена литература изостават (въпреки че ID-та ~4-6M не са торентирани, тъй като се припокриват с нашите Zlib торенти). Нашата блог публикация за издаването на комиксите Торенти за комикси в Архива на Анна За историята на различните разклонения на Library Genesis, вижте страницата за <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li съдържа по-голямата част от същото съдържание и метаданни като Libgen.rs, но има някои допълнителни колекции, а именно комикси, списания и стандартни документи. Той също така е интегрирал <a %(a_scihub)s>Sci-Hub</a> в своите метаданни и търсачка, което използваме за нашата база данни. Метаданните за тази библиотека са свободно достъпни <a %(a_libgen_li)s>на libgen.li</a>. Въпреки това, този сървър е бавен и не поддържа подновяване на прекъснатите връзки. Същите файлове са достъпни и на <a %(a_ftp)s>FTP сървър</a>, който работи по-добре. Изглежда, че и нехудожествената литература се е отклонила, но без нови торенти. Изглежда, че това се е случило от началото на 2022 г., въпреки че не сме го потвърдили. Според администратора на Libgen.li, колекцията “fiction_rus” (руска художествена литература) трябва да бъде покрита от редовно пускани торенти от <a %(a_booktracker)s>booktracker.org</a>, най-вече торентите <a %(a_flibusta)s>flibusta</a> и <a %(a_librusec)s>lib.rus.ec</a> торенти(за които ние сме огледала <a %(a_torrents)s>тук</a>, въпреки че все още не сме установили кои торенти съответстват на кои файлове). Колекцията от художествена литература има свои собствени торенти (отделени от <a %(a_href)s>Libgen.rs</a>), започващи от %(start)s. Определени диапазони без торенти (като диапазоните за художествена литература f_3463000 до f_4260000) вероятно са файлове от Z-Library (или други дубликати), въпреки че може да искаме да направим малко дублиране и да направим торентите за lgli-уникални файлове в тези диапазони. Статистики за всички колекции могат да бъдат намерени <a %(a_href)s>на сайта на libgen</a>. Торенти са налични за по-голямата част от допълнителното съдържание, като най-вече торенти за комикси, списания и стандартни документи са пуснати в сътрудничество с Архива на Анна. Имайте предвид, че торент файловете, отнасящи се до “libgen.is”, са огледала на <a %(a_libgen)s>Libgen.rs</a> (“.is” е различен домейн, използван от Libgen.rs). Полезен ресурс за използване на метаданните е <a %(a_href)s>тази страница</a>. %(icon)s Тяхната колекция “fiction_rus” (руска художествена литература) няма специални торенти, но е осигурила торенти от другаде, и ние поддържаме <a %(fiction_rus)s>тяхно огледало</a>. Торенти за руска художествена литература в Архива на Анна Торенти за художествена литература в Архива на Анна Форум за дискусии Метаданни Метаданни чрез FTP Торенти за списания в Архива на Анна Информация за полетата на метаданните Огледало на други торенти (и уникални торенти за художествена литература и комикси) Торенти за стандартни документи в Архива на Анна Libgen.li Торенти от Архива на Анна (корици на книги) Library Genesis е известен с това, че щедро предоставя своите данни в големи обеми чрез торенти. Нашата колекция от Libgen се състои от допълнителни данни, които те не пускат директно, в партньорство с тях. Огромни благодарности на всички, които работят с Library Genesis! Нашият блог за издаването на кориците на книги Тази страница е за версията “.rs”. Тя е известна с последователното публикуване както на своите метаданни, така и на пълното съдържание на своя каталог с книги. Колекцията от книги е разделена на секции за художествена и нехудожествена литература. Полезен ресурс за използване на метаданните е <a %(a_metadata)s>тази страница</a> (блокира IP диапазони, може да е необходим VPN). Към март 2024 г. нови торенти се публикуват в <a %(a_href)s>тази тема на форума</a> (блокира IP диапазони, може да е необходим VPN). Торенти за художествена литература в Архива на Анна Торенти за художествена литература на Libgen.rs Форум за дискусии на Libgen.rs Libgen.rs метаданни Информация за полетата на метаданните на Libgen.rs Торенти за нехудожествена литература на Libgen.rs Торенти за нехудожествена литература в Архива на Анна %(example)s за художествена книга. Това <a %(blog_post)s>първо издание</a> е доста малко: около 300GB корици на книги от разклонението на Libgen.rs, както за художествена, така и за нехудожествена литература. Те са организирани по същия начин, както се появяват на libgen.rs, напр.: %(example)s за нехудожествена книга. Точно както при колекцията на Z-Library, ние ги поставихме всички в голям .tar файл, който може да бъде качен с помощта на <a %(a_ratarmount)s>ratarmount</a>, ако искате да работите с файловете директно. Издание 1 (%(date)s) Кратката история на различните разклонения на Library Genesis (или “Libgen”) е, че с течение на времето различните хора, участващи в Library Genesis, го изоставиха и тръгнаха по различни пътища. Според този <a %(a_mhut)s>пост във форума</a>, Libgen.li първоначално е бил хостван на “http://free-books.dontexist.com”. Версията “.fun” беше създадена от първоначалния основател. Тя се преработи като услуга за нова, по-разпространена версия. <a %(a_li)s>Версията “.li”</a> има огромна колекция от комикси, както и друго съдържание, което все още не е достъпно за масово изтегляне чрез торенти. Тя има отделна колекция от торенти за художествени книги и съдържа метаданните на <a %(a_scihub)s>Sci-Hub</a> в своята база данни. Версията “.rs” има много подобни данни и най-последователно пуска своята колекция в масови торенти. Тя е приблизително разделена на секции “художествена литература” и “нехудожествена” литература. Първоначално на “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> в известен смисъл също е разклонение на Library Genesis, въпреки че използваха различно име за своя проект. Libgen.rs Също така обогатяваме нашата колекция с източници само с метаданни, които можем да съпоставим с файлове, например, използвайки ISBN номера или други полета. По-долу е представен преглед на тези източници. Отново, някои от тези източници са напълно отворени, докато за други трябва да извличаме данни. Имайте предвид, че при търсене на метаданни показваме оригиналните записи. Не правим никакво обединяване на записи. Източници само с метаданни Open Library е проект с отворен код на Internet Archive за каталогизиране на всяка книга в света. Той има една от най-големите възможности за търсене на книги в света и разполага с много книги за дигитално заемане. Неговият каталог с метаданни за книги е свободно достъпен за изтегляне и е включен в Архива на Анна (въпреки че в момента не е в търсенето, освен ако не търсите изрично по Open Library ID). Open Library Без дубликати Последно обновено Проценти на броя на файловете %% огледани от AA / налични торенти Размер Източник По-долу е кратък преглед на източниците на файловете в Архива на Анна. Тъй като сенчестите библиотеки често синхронизират данни една от друга, има значително припокриване между библиотеките. Затова числата не се събират до общата сума. Процентът “огледани и споделени файл(ове) от Архива на Анна” показва колко огледални файла имаме сами. Споделяме тези файлове на едро чрез торенти и ги правим достъпни за директно изтегляне чрез партньорски сайтове. Преглед Общо Торенти в Архива на Анна За повече информация относно Sci-Hub, посетете неговия <a %(a_scihub)s>официален сайт</a>, <a %(a_wikipedia)s>страница в Wikipedia</a> и това <a %(a_radiolab)s>подкаст интервю</a>. Имайте предвид, че Sci-Hub е <a %(a_reddit)s>замразен от 2021 г.</a>. Той беше замразен и преди, но през 2021 г. бяха добавени няколко милиона статии. Все пак, ограничен брой статии продължават да се добавят към колекциите на Libgen “scimag”, но не достатъчно, за да даде основание за нови групови торенти. Ние използваме метаданните на Sci-Hub, предоставени от <a %(a_libgen_li)s>Libgen.li</a> в неговата колекция “scimag”. Също така използваме набора от данни <a %(a_dois)s>dois-2022-02-12.7z</a>. Имайте предвид, че торентите “smarch” са <a %(a_smarch)s>остарели</a> и затова не са включени в нашия списък с торенти. Торенти в Libgen.li Торенти в Libgen.rs Метаданни и торенти Актуализации в Reddit Подкаст интервю Страница в Wikipedia Sci-Hub Sci-Hub: замразен от 2021 г.; повечето налични чрез торенти Libgen.li: малки допълнения оттогава</div> Някои библиотеки с изходни кодове насърчават масовото споделяне на своите данни чрез торенти, докато други не споделят лесно своите колекции. В последния случай, Архивът на Анна се опитва да извлече техните колекции и да ги направи достъпни (вижте нашата страница <a %(a_torrents)s>Торенти</a>). Има и междинни ситуации, например, когато библиотеки с изходни кодове са готови да споделят, но нямат ресурсите за това. В тези случаи, също се опитваме да помогнем. По-долу е представен преглед на това как взаимодействаме с различните библиотеки с изходни кодове. Библиотеки с изходни кодове %(icon)s Различни файлови бази данни, разпръснати из китайския интернет; често платени бази данни %(icon)s Повечето файлове са достъпни само с премиум акаунти в BaiduYun; бавни скорости на изтегляне. %(icon)s Архивът на Анна управлява колекция от <a %(duxiu)s>файлове на DuXiu</a> %(icon)s Различни бази данни с метаданни, разпръснати из китайския интернет; често платени бази данни %(icon)s Няма лесно достъпни метаданни за цялата им колекция. %(icon)s Архивът на Анна управлява колекция от <a %(duxiu)s>метаданни на DuXiu</a> Файлове %(icon)s Файловете са налични само за заемане на ограничена основа, с различни ограничения за достъп %(icon)s Архивът на Анна управлява колекция от <a %(ia)s>файлове на IA</a> %(icon)s Някои метаданни са налични чрез <a %(openlib)s>бази данни на Open Library</a>, но те не покриват цялата колекция на IA %(icon)s Няма лесно достъпни метаданни за цялата им колекция %(icon)s Архивът на Анна управлява колекция от <a %(ia)s>метаданни на IA</a> Последно обновено %(icon)s Архивът на Анна и Libgen.li съвместно управляват колекции от <a %(comics)s>комикси</a>, <a %(magazines)s>списания</a>, <a %(standarts)s>стандартни документи</a>, и <a %(fiction)s>художествена литература (отделена от Libgen.rs)</a>. %(icon)s Торентите за нехудожествена литература се споделят с Libgen.rs (и са огледални <a %(libgenli)s>тук</a>). %(icon)s Тримесечни <a %(dbdumps)s>HTTP копия на база данни</a> %(icon)s Автоматизирани торенти за <a %(nonfiction)s>Нехудожествена литература</a> и <a %(fiction)s>Художествена литература</a> %(icon)s Архивът на Анна управлява колекция от <a %(covers)s>торенти с корици на книги</a> %(icon)s Ежедневни <a %(dbdumps)s>HTTP пълни копия на база данни</a> Метаданни %(icon)s Месечно <a %(dbdumps)s>копиране на базатата данни</a> %(icon)s Налични торенти с данни <a %(scihub1)s>тук</a>, <a %(scihub2)s>тук</a>, и <a %(libgenli)s>тук</a> %(icon)s Някои нови файлове <a %(libgenrs)s>се</a> <a %(libgenli)s>добавят</a> към “scimag” на Libgen, но не е достатъчно, за да се гарантират нови торенти %(icon)s Sci-Hub е замразил нови файлове от 2021 г. %(icon)s Метаданни налични <a %(scihub1)s>тук</a> и <a %(scihub2)s>тук</a>, както и като част от <a %(libgenli)s>Libgen.li базата данни</a> (която използваме) Източник %(icon)s Различни по-малки или еднократни източници. Насърчаваме хората първо да качват в другите сенчести библиотеки, но понякога те имат колекции, които са твърде големи, за да бъдат сортирани от други, но не достатъчно големи, за да заслужават собствена категория. %(icon)s Не е налично директно в големи количества, защитено срещу скрапинг %(icon)s Архивът на Анна управлява колекция от <a %(worldcat)s>метаданни на OCLC (WorldCat)</a> %(icon)s Архивът на Анна и Z-Library съвместно управляват колекция от <a %(metadata)s>метаданни на Z-Library</a> и <a %(files)s>файлове на Z-Library</a> Datasets Комбинираме всички горепосочени източници в една обединена база данни, която използваме за обслужване на този сайт. Тази обединена база данни не е директно достъпна, но тъй като Архивът на Анна е напълно с отворен код, тя може сравнително лесно да бъде <a %(a_generated)s>генерирана</a> или <a %(a_downloaded)s>изтеглена</a> като бази данни ElasticSearch и MariaDB. Скриптовете на тази страница автоматично ще изтеглят всички необходими метаданни от споменатите по-горе източници. Ако искате да разгледате нашите данни преди да стартирате тези скриптове локално, можете да погледнете нашите JSON файлове, които водят към други JSON файлове. <a %(a_json)s>Този файл</a> е добър начален пункт. Обединена база данни Торенти от Архива на Анна разглеждане търсене Различни по-малки или еднократни източници. Насърчаваме хората първо да качват в други сенчести библиотеки, но понякога хората имат колекции, които са твърде големи, за да могат другите да ги сортират, но не достатъчно големи, за да дадат основание за собствена категория. Преглед от <a %(a1)s>страницата с Datasets</a>. От <a %(a_href)s>aaaaarg.fail</a>. Изглежда доста пълно. От нашия доброволец “cgiym”. От <a %(a_href)s><q>ACM Digital Library 2020</q></a> торент. Има доста голямо припокриване със съществуващите колекции от статии, но много малко съвпадения на MD5, затова решихме да го запазим изцяло. Скрап на <q>iRead eBooks</q> (= фонетично <q>ai rit i-books</q>; airitibooks.com), от доброволец <q>j</q>. Съответства на <q>airitibooks</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>. От колекция <a %(a1)s><q>Библиотека Александрина</q></a>. Частично от оригиналния източник, частично от the-eye.eu, частично от други огледала. От частен торент сайт за книги, <a %(a_href)s>Bibliotik</a> (често наричан “Bib”), чиито книги са били обединени в торенти по име (A.torrent, B.torrent) и разпространявани чрез the-eye.eu. От нашия доброволец “bpb9v”. За повече информация относно <a %(a_href)s>CADAL</a>, вижте бележките на нашата <a %(a_duxiu)s>страница с данни за DuXiu</a>. Още от нашия доброволец “bpb9v”, предимно файлове от DuXiu, както и папка “SuperStar_Journals” (SuperStar is the company behind DuXiu). От нашия доброволец “cgiym”, китайски текстове от различни източници (представени като поддиректории), включително от <a %(a_href)s>China Machine Press</a> (голям китайски издател). Некитайски колекции (представени като поддиректории) от нашия доброволец “cgiym”. Скрап на книги за китайска архитектура, от доброволец <q>cm</q>: <q>Получих го чрез експлоатация на уязвимост в мрежата на издателството, но тази пролука вече е затворена</q>. Съответства на <q>chinese_architecture</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>. Книги от академичното издателство <a %(a_href)s>De Gruyter</a>, събрани от няколко големи торента. "Изстърган" от <a %(a_href)s>docer.pl</a>, полски сайт за споделяне на файлове, фокусиран върху книги и други писмени произведения. "Изстърган" в края на 2023 г. от доброволеца “p”. Нямаме добри метаданни от оригиналния сайт (дори нямаме и разширенията на файловете), но филтрирахме за подобни на книги файлове, и често успявахме да извлечем метаданни от самите файлове. DuXiu epubs, директно от DuXiu, събрани от доброволец “w”. Само последните книги на DuXiu са достъпни директно чрез електронни книги, така че повечето от тях трябва да са нови. Останалите файлове от DuXiu от доброволец “m”, които не бяха във формат PDG на DuXiu (основният <a %(a_href)s>набор от данни на DuXiu</a>). Събрани от много оригинални източници, за съжаление без запазване на тези източници в пътя на файла. <span></span> <span></span> <span></span> Скрап на еротични книги, от доброволец <q>do no harm</q>. Съответства на <q>hentai</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>. <span></span> <span></span> Колекция, "изстъргана" от японски издател на манга от доброволец “t”. <a %(a_href)s>Избрани съдебни архиви на Лонгкуан</a>, предоставени от доброволец “c”. "Изстърган" от <a %(a_href)s>magzdb.org</a>, съюзник на Library Genesis (свързан е с началната страница на libgen.rs ), но който не искаше да предостави файловете си директно. Получен от доброволец “p” в края на 2023 г. <span></span> Различни малки качвания, твърде малки за собствена подколекция, но представени като директории. Електронни книги от AvaxHome, руски уебсайт за споделяне на файлове. Архив на вестници и списания. Съответства на <q>newsarch_magz</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>. Скрап на <a %(a1)s>Центъра за документация по философия</a>. Колекция на доброволец “o”, който събра полски книги директно от оригинални сайтове за издания (“сцена”). Комбинирани колекции на <a %(a_href)s>shuge.org</a> от доброволци “cgiym” и “woz9ts”. <span></span> <a %(a_href)s>“Имперска библиотека на Трантор”</a> (наречена на измислената библиотека), "изстъргана" през 2022 г. от доброволец “t”. <span></span> <span></span> <span></span> Под-под-колекции (представени като директории) от доброволец “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (от <a %(a_sikuquanshu)s>Dizhi(迪志)</a> в Тайван), mebook (mebook.cc, 我的小书屋, моята малка книжарница — woz9ts: “Този сайт основно се фокусира върху споделянето на висококачествени електронни книги, някои от които са форматирани от самия собственик. Собственикът беше <a %(a_arrested)s>арестуван</a> през 2019 г. и някой направи колекция от файловете, които той сподели.”). Останали файлове от DuXiu от доброволеца “woz9ts”, които не бяха във формат PDG на DuXiu (все още трябва да бъдат конвертирани в PDF). Колекцията “качвания” е разделена на по-малки подколекции, които са посочени в AACIDs и имената на торентите. От всички подколекции първо бяха премахнати дублиращите записи спрямо основната колекция, въпреки че JSON файловете с метаданни “upload_records“ все още съдържат много препратки към оригиналните файлове. Файловете, които не са книги, също бяха премахнати от повечето подколекции и обикновено <em>не</em> се отбелязват в “upload_records” JSON. Подколекциите са: Бележки Подколекция Много подколекции сами по себе си се състоят от под-подколекции (например от различни източници), които са представени като директории в полетата “filepath”. Качвания в Архива на Анна Нашата публикация в блога за тези данни <a %(a_worldcat)s>WorldCat</a> е собствена база данни на неправителствената организация <a %(a_oclc)s>OCLC</a>, която събира метаданни от библиотеки по целия свят. Вероятно това е най-голямата колекция от библиотечни метаданни в света. През октомври 2023 г. ние <a %(a_scrape)s>пуснахме</a> цялостно извличане на базата данни OCLC (WorldCat), във <a %(a_aac)s>формат Containers на Архива на Анна</a>. Октомври 2023 г., първоначално издание: OCLC (WorldCat) Торенти от Архива на Анна Примерен запис в Архива на Анна (оригинална колекция) Примерен запис в Архива на Анна (колекция “zlib3”) Торенти от Архива на Анна (метаданни + съдържание) Блог пост за Издание 1 Блог пост за Издание 2 В края на 2022 г. предполагаемите основатели на Z-Library бяха арестувани и домейните бяха иззети от властите на Съединените щати. Оттогава сайтът бавно се връща онлайн. Не е известно кой го управлява в момента. Актуализация към февруари 2023 г. Z-Library има своите корени в общността на <a %(a_href)s>Library Genesis</a> и първоначално се стартира с техните данни. Оттогава насам, тя се професионализира значително и има много по-модерен интерфейс. Поради това те могат да получават много повече дарения, както парични за поддържане и подобряване на уебсайта, така и дарения на нови книги. Те са натрупали голяма колекция в допълнение към Library Genesis. Колекцията се състои от три части. Оригиналните описателни страници за първите две части са запазени по-долу. Нуждаете се от всичките три части, за да получите всички данни (с изключение на заменените торенти, които са зачеркнати на страницата с торенти). %(title)s: нашето първо издание. Това беше първото издание на това, което тогава се наричаше “Пиратско библиотечно огледало” (“pilimi”). %(title)s: второ издание, този път с всички файлове опаковани в .tar файлове. %(title)s: добавени нови издания, използващи <a %(a_href)s>формата на Контейнери на Архива на Анна (AAC)</a>, понастоящем, издадени в сътрудничество с екипа на Z-Library. Първоначалното огледало беше старателно получено през 2021 и 2022. В този момент е леко остаряло: отразява състоянието на колекцията през юни 2021. Ще го актуализираме в бъдеще. В момента сме фокусирани върху издаването на това първо издание. Тъй като Library Genesis вече е запазена с публични торенти и е включена в Z-Library, направихме основно премахване на дублиращо съдържание спрямо Library Genesis през юни 2022. За това използвахме MD5 хешове. Вероятно има много повече дублиращо съдържание в библиотеката, като например множество файлови формати на една и съща книга. Това е трудно за точно откриване, затова не го правим. След премахването на дублиращото съдържание остават над 2 милиона файла, с общ размер малко под 7TB. Колекцията се състои от две части: MySQL “.sql.gz” архив на метаданните и 72 торент файла с размер около 50-100GB всеки. Метаданните съдържат данните, както са докладвани от сайта на Z-Library (заглавие, автор, описание, файлов тип), както и действителния размер на файла и md5sum, които наблюдавахме, тъй като понякога тези данни не съвпадат. Изглежда, че има диапазони от файлове, за които самата Z-Library има неправилни метаданни. В някои изолирани случаи може също да сме изтеглили неправилни файлове, които ще се опитаме да открием и поправим в бъдеще. Големите торент файлове съдържат действителните данни за книгите, като ID на Z-Library е използвано като име на файла. Разширенията на файловете могат да бъдат възстановени с помощта на архива на метаданните. Колекцията е смес от нехудожествено и художествено съдържание (не е разделена както в Library Genesis). Качеството също варира значително. Това първо издание вече е напълно достъпно. Имайте предвид, че торент файловете са достъпни само чрез нашето огледало в Tor. Издание 1 (%(date)s) Това е един допълнителен торент файл. Той не съдържа нова информация, но има някои данни, които могат да отнемат време за изчисление. Това го прави удобен, тъй като изтеглянето на този торент често е по-бързо, отколкото изчисляването му от нулата. По-специално, той съдържа SQLite индекси за tar файловете, за използване с <a %(a_href)s>ratarmount</a>. Издание 2 допълнение (%(date)s) Получихме всички книги, които бяха добавени в Z-Library между последното ни огледало и август 2022. Също така се върнахме и изтеглихме някои книги, които пропуснахме първия път. Всичко на всичко, тази нова колекция е около 24TB. Отново, тази колекция е премахната от дублиращо съдържание спрямо Library Genesis, тъй като вече има налични торенти за тази колекция. Данните са организирани подобно на първото издание. Има MySQL “.sql.gz” архив на метаданните, който също включва всички метаданни от първото издание, като по този начин го замества. Добавихме и някои нови колони: Споменахме това последния път, но за да изясним: “filename” и “md5” са действителните свойства на файла, докато “filename_reported” и “md5_reported” са това, което изтеглихме от Z-Library. Понякога тези две не съвпадат, затова включихме и двете. За това издание променихме колацията на “utf8mb4_unicode_ci”, която трябва да е съвместима с по-старите версии на MySQL. Файловете с данни са подобни на миналия път, въпреки че са много по-големи. Просто не можехме да се занимаваме с създаването на множество по-малки торент файлове. “pilimi-zlib2-0-14679999-extra.torrent” съдържа всички файлове, които пропуснахме в последното издание, докато другите торенти са всички нови диапазони на ID.  <strong>Обновяване %(date)s:</strong> Направихме повечето от нашите торенти твърде големи, което затрудни торент клиентите. Премахнахме ги и пуснахме нови торенти. <strong>Обновяване %(date)s:</strong> Все още, имаше твърде много файлове, затова ги опаковахме в tar файлове и отново пуснахме нови торенти. %(key)s: дали този файл вече е в Library Genesis, в колекцията на нехудожествена или художествена литература (съвпадение по md5). %(key)s: в кой торент се намира този файл. %(key)s: зададено, когато не успяхме да изтеглим книгата. Издание 2 (%(date)s) Издания на Zlib (оригинални описателни страници) Хост на псевдо домейн от най-високо ниво, реализиран от проекта OnioNS домейн Основен сайт "Изстърган" от Z-Library Колекцията “Chinese” в Z-Library изглежда същата като нашата колекция DuXiu, но с различни MD5. Изключваме тези файлове от торентите, за да избегнем дублиране, но все пак ги показваме в нашия индекс за търсене. Метаданни Получавате %(percentage)s%% бонус от бързи изтегляния, защото сте препоръчан от потребителя %(profile_link)s. Това важи за целия период на членство. Дарете Присъединете се Избрано до %(percentage)s%% отстъпки Alipay поддържа международни кредитни/дебитни карти. Вижте <a %(a_alipay)s>това ръководство</a> за повече информация. Изпратете ни подаръчни карти на Amazon.com, като използвате вашата кредитна/дебитна карта. Можете да купувате крипто валута чрез кредитни/дебитни карти. WeChat (Weixin Pay) поддържа международни кредитни/дебитни карти. В приложението WeChat отидете на “Аз → Услуги → Портфейл → Добавяне на карта”. Ако не виждате това, активирайте го чрез “Аз → Настройки → Общи → Инструменти → Weixin Pay → Активиране”. (използвайте при изпращане на Ethereum от Coinbase) копирано! копиране (най-ниската минимална сума) (предупреждение: висока минимална сума) -%(percentage)s%% 12 месеца 1 месец 24 месеца 3 месеца 48 месеца 6 месеца 96 месеца Изберете за колко време искате да се абонирате. <div %(div_monthly_cost)s></div><div %(div_after)s>след <span %(span_discount)s></span> отстъпки</div><div %(div_total)s></div> <div %(div_duration)s></div> %(percentage)s%% за 12 месеца за 1 месец за 24 месеца за 3 месеца за 48 месеца за 6 месеца за 96 месеца %(monthly_cost)s / месец свържете се с нас Директни <strong>SFTP</strong> сървъри Дарение или обмен на корпоративно ниво за нови колекции (напр. нови сканирания, набори от данни с OCR). Експертен достъп <strong>Неограничен</strong>високоскоростен достъп <div %(div_question)s>Мога ли да надградя членството си или да получа няколко членства?</div> <div %(div_question)s>Мога ли да направя дарение без да ставам член?</div> Разбира се. Приемаме дарения на всякаква сума на този Monero (XMR) адрес: %(address)s. <div %(div_question)s>Какво означават диапазоните на месец?</div> Можете да достигнете до по-ниската страна на диапазона, като приложите всички отстъпки, като например изберете период по-дълъг от месец. <div %(div_question)s>Подновяват ли се членствата автоматично?</div> Членствата<strong>не се</strong> подновяват автоматично. Можете да се присъедините за толкова дълго или кратко, колкото искате. <div %(div_question)s>За какво харчите даренията?</div> 100%%ще запази и направи достъпни световното знание и култура. В момента го изразходваме предимно за сървъри, съхранение и честотна лента. Никакви пари не отиват лично на членовете на екипа. <div %(div_question)s>Мога ли да направя голямо дарение?</div> Това би било невероятно! За дарения над няколко хиляди долара, моля, свържете се директно с нас на %(email)s. <div %(div_question)s>Имате ли други методи на плащане?</div> В момента не. Много хора не искат архиви като този да съществуват, така че трябва да внимаваме. Ако можете да ни помогнете да настроим безопасно други (по-удобни) методи на плащане, моля, свържете се с нас на %(email)s. Често Задавани Въпроси за дарения Имате <a %(a_donation)s>съществуващо дарение</a> в ход. Завършете или отменете това дарение, преди да направите ново дарение. <a %(a_all_donations)s>Преглед на всички мои дарения</a> За дарения над $5,000, моля, свържете се директно с нас на %(email)s. Приветстваме големи дарения от богатите хора или институции.  Имайте предвид, че докато членствата на тази страница са “на месец”, те са еднократни дарения (не се повтарят). Вижте <a %(faq)s>Често задавани въпроси за дарение</a>. Anna’s Archive е проект с нестопанска цел с отворен код и отворени данни. Като дарите и станете член, вие подкрепяте нашите действия и развитие. До всички наши членове: благодарим ви, че ни поддържате! ❤️ За повече информация, вижте <a %(a_donate)s>Често задавани въпроси за дарение</a>. За да станете член, моля <a %(a_login)s>Влезте или се регистрирайте</a>. Благодаря за подкрепата! $%(cost)s / месец Ако сте допуснали грешка по време на плащането, не можем да възстановим сумата, но ще се опитаме да се реваншираме. Намерете страницата “Крипто” във вашето приложение или уебсайт на PayPal. Това обикновено е под “Финанси”. Отидете на страницата “Биткойн” във вашето приложение или уебсайт на PayPal. Натиснете бутона “Прехвърляне” %(transfer_icon)s и след това “Изпращане”. Алипей Alipay / WeChat Amazon Карта за подарък %(amazon)s подаръчна карта Банкова карта Банкова карта (чрез приложение) Бинанс Кредитна/дебитна/Apple/Google (BMC) Кеш приложение Кредитна/дебитна карта Кредитна/дебитна карта 2 Кредитна/дебитна карта (резервна) Крипто %(bitcoin_icon)s Карта / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (редовен) нова система за незабавни плащания в Бразилия Pix Revolut (временно недостъпно) WeChat приложение за съобщения и мобилни плащания Изберете предпочитаната от вас крипто монета: Дарете с карта за подарък на Amazon. <strong>ВАЖНО:</strong> Тази опция е за %(amazon)s. Ако искате да използвате друг сайт на Amazon, изберете го по-горе. <strong>ВАЖНО:</strong> Ние поддържаме само Amazon.com, не другите уебсайтове на Amazon. Например .de, .co.uk, .ca НЕ се поддържат. Моля, НЕ пишете лично съобщение. Въведете точната сума: %(amount)s Имайте предвид, че трябва да закръглим до суми, приети от нашите дистрибутори (minimum %(minimum)s). Дарете с кредитна/дебитна, чрез приложението Alipay (много лесно за настройка). Инсталирайте приложението Alipay от <a %(a_app_store)s>Apple App Store</a> или <a %(a_play_store)s>Google Play Store</a>. Регистрирайте се с вашия телефонен номер. Не се изискват допълнителни лични данни. <span %(style)s>1</span>Инсталирайте приложението Alipay Поддържани: Visa, MasterCard, JCB, Diners Club и Discover. Вижте <a %(a_alipay)s>това ръководство</a> за повече информация. <span %(style)s>2</span>Добавете банкова карта С Binance можете да купите Bitcoin с кредитна/дебитна карта или банков акаунт и след това да дарите този Bitcoin на нас. По този начин можем да останем сигурни и анонимни при приемане на вашето дарение. Binance е наличен в почти всяка страна и поддържа повечето банки и кредитни/дебитни карти. Това е нашата основна препоръка в момента. Оценяваме, че отделяте време да научите как да дарите, използвайки този метод, тъй като това ни помага много. За кредитни карти, дебитни карти, Apple Pay и Google Pay използваме “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). В тяхната система, едно “кафе” е равно на $5, така че вашето дарение ще бъде закръглено до най-близкото кратно на 5. Дарете чрез Cash App. Ако имате Cash App, това е най-лесният начин да дарите! Имайте предвид, че за транзакции под %(amount)s, Cash App може да начисли такса %(fee)s. За %(amount)s или повече е безплатно! Дарете с кредитна или дебитна карта. Този метод използва доставчика на криптовалута като междинно преобразуване. Това може да бъде малко объркващо, така че моля, използвайте този метод само ако другите методи на плащане не работят. Също така не работи във всички страни. Не можем да поддържаме кредитни/дебитни карти директно, защото банките не искат да работят с нас. ☹ Въпреки това, има няколко начина да използвате кредитни/дебитни карти чрез други методи на плащане: С крипто можете да дарявате, като използвате BTC, ETH, XMR и SOL. Използвайте тази опция, ако вече сте запознати с криптовалутата. С крипто валута можете да дарявате, като използвате BTC, ETH, XMR и др. Крипто експресни услуги Ако използвате криптовалута за първи път, препоръчваме да използвате %(options)s, за да купите и дарите Bitcoin (оригиналната и най-използвана криптовалута). Имайте предвид, че за малки дарения таксите за кредитна карта може да премахнат нашата %(discount)s%% отстъпка, така че препоръчваме по-дългосрочни абонаменти. Дарете с кредитна/дебитна карта, PayPal или Venmo. Можете да изберете между тях на следващата страница. Google Pay и Apple Pay може също да работят. Имайте предвид, че за малки дарения таксите са високи, затова препоръчваме по-дългосрочни абонаменти. За да даряваме чрез PayPal, ще използваме PayPal Crypto, което ни позволява да останем анонимни. Оценяваме, че отделихте време, за да научите как да дарявате чрез този метод, тъй като ни помага много. Дарете чрез PayPal. Дарете чрез вашия редовен PayPal акаунт. Дарете чрез Revolut. Ако имате Revolut, това е най-лесният начин да дарите! Този метод на плащане позволява максимум %(amount)s. Моля, изберете различна продължителност или начин на плащане. Този метод на плащане изисква минимум %(amount)s. Моля, изберете различна продължителност или начин на плащане. Бинанс Coinbase Kraken Моля, изберете начин на плащане. “Приемете торент“: вашето потребителско име или съобщение в име на торент файл <div %(div_months)s>веднъж на всеки 12 месеца членство</div> Вашето потребителско име или анонимно споменаване в списъка на заслужилите Ранен достъп до нови функции Специален Телеграм с обновяване на заден фон %(number)s бързи изтегляния на ден ако дарите този месец! <a %(a_api)s>JSON API</a> достъп Легендарен статус в опазването на знанията и културата на човечеството Предишни предимства плюс: Спечелете <strong>%(percentage)s%% бонус изтегляния</strong> като <a %(a_refer)s>поканите приятели</a>. SciDB документи <strong>неограничени</strong> без проверка Когато задавате въпроси за акаунт или дарения, добавете вашия акаунт ID, екранни снимки, разписки, колкото се може повече информация. Проверяваме имейла си само на всеки 1-2 седмици, така че липсата на тази информация ще забави всяко решение. За да получите още повече изтегляния, <a %(a_refer)s>препоръчайте ни на приятелите си</a>! Ние сме малък екип от доброволци. Може да ни отнеме 1-2 седмици, за да ви отговорим. Имайте предвид, че името или снимката на акаунта може да изглеждат странно. Няма нужда да се тревожите! Тези акаунти се управляват от нашите партньори за дарения. Нашите акаунти не са били хакнати. Дарете <span %(span_cost)s></span> <span %(span_label)s></span> за 12 месеца “%(tier_name)s” за 1 месец “%(tier_name)s” за 24 месеца “%(tier_name)s” за 3 месеца “%(tier_name)s” за 48 месеца “%(tier_name)s” за 6 месеца “%(tier_name)s” за 96 месеца “%(tier_name)s” Все още можете да анулирате дарението по време на плащане. Щракнете върху бутона за дарение, за да потвърдите това дарение. <strong>Важна забележка:</strong> Цените на криптовалутите могат да варират силно, понякога дори до 20%% за няколко минути. Това все още е по-малко от таксите, които плащаме с много доставчици на плащания, които често таксуват 50-60%% за работа с “благотворителна организация в сянка” като нас. <u>Ако ни изпратите разписката с първоначалната цена, която сте платили, ние пак ще кредитираме акаунта ви за избраното членство</u> (стига разписката да не е по-стара от няколко часа). Наистина оценяваме, че сте готови да се примирите с подобни неща, за да ни подкрепите!❤️ ❌ Нещо се обърка. Презаредете страницата и опитайте отново. <span %(span_circle)s>1</span>Купете биткойн на Paypal <span %(span_circle)s>2</span>Прехвърлете Bitcoin на нашия адрес ✅ Пренасочване към страницата за дарения… Дарение Моля, изчакайте поне <span %(span_hours)s>24 часа</span> (и обновете тази страница), преди да се свържете с нас. Ако искате да направите дарение (каквато и да е сума) без членство, не се колебайте да използвате този адрес на Monero (XMR): %(address)s. След като изпратите вашата карта за подарък, нашата автоматизирана система ще я потвърди в рамките на няколко минути. Ако това не работи, опитайте да изпратите отново вашата карта за подарък (<a %(a_instr)s>инструкции</a>). Ако това все още не работи, моля, изпратете ни имейл и Анна ще го прегледа ръчно (това може да отнеме няколко дни) и не забравяйте да споменете дали вече сте опитали да изпратите отново. Пример: Моля, използвайте <a %(a_form)s>официалния формуляр на Amazon.com</a>, за да ни изпратите карта за подарък от %(amount)s на имейл адреса по-долу. Имейл на получателя “До” във формата: Карта за подарък на Amazon Не можем да приемем други методи за подаръчни карти, <strong>само изпратени директно от официалния формуляр на Amazon.com</strong>. Не можем да върнем вашата подаръчна карта, ако не използвате този формуляр. Използвайте само веднъж. Уникален за вашия акаунт, не споделяйте. Изчакване на карта за подарък... (опреснете страницата, за да проверите) Отворете <a %(a_href)s>страницата за дарение с QR код</a>. Сканирайте QR кода с приложението Alipay или натиснете бутона, за да отворите приложението Alipay. Моля, бъдете търпеливи; страницата може да се зареди по-бавно, тъй като е в Китай. <span %(style)s>3</span>Направете дарение (сканирайте QR кода или натиснете бутона) Купете монета PYUSD на PayPal Купете Bitcoin (BTC) в Cash App Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за трансакции. Ще запазите всичко, което остане. Отидете на страницата “Bitcoin” (BTC) в Cash App. Прехвърлете Bitcoin на нашия адрес За малки дарения (под $25), може да се наложи да използвате Rush или Priority. Кликнете върху бутона “Изпрати биткойн”, за да направите “теглене”. Превключете от долари на BTC, като натиснете иконата %(icon)s. Въведете сумата в BTC по-долу и кликнете “Изпрати”. Вижте <a %(help_video)s>това видео</a>, ако се затрудните. Експресните услуги са удобни, но начисляват по-високи такси. Можете да използвате това вместо крипто борса, ако искате бързо да направите по-голямо дарение и не ви пречи такса от $5-10. Уверете се, че изпращате точната крипто сума, показана на страницата за дарение, а не сумата в $USD. В противен случай таксата ще бъде извадена и не можем автоматично да обработим вашето членство. Понякога потвърждението може да отнеме до 24 часа, затова не забравяйте да обновите тази страница (дори ако е изтекла). Инструкции за кредитна/дебитна карта Дарете чрез нашата страница с кредитни/дебитни карти Някои от стъпките споменават крипто портфейли, но не се притеснявайте, не е нужно да научавате нищо за това крипто. %(coin_name)s инструкции Сканирайте този QR код с вашето приложение Crypto Wallet, за да попълните бързо данните за плащане Сканирайте QR код, който да платите Поддържаме само стандартната версия на крипто монети, без екзотични мрежи или версии на монети. Потвърждаването на транзакцията може да отнеме до един час, в зависимост от монетата. Дарете %(amount)s на <a %(a_page)s>тази работа</a>. Това дарение е изтекло. Моля, отменете и създайте ново. Ако вече сте платили: Да, изпратих разписката си по имейл Ако обменният курс на криптовалутата е варирал по време на транзакцията, не забравяйте да включите разписката, показваща първоначалния обменен курс. Наистина оценяваме, че си направихте труда да използвате крипто, това ни помага много! ❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново. <span %(span_circle)s>%(circle_number)s</span>Изпратете ни разписката по имейл Ако срещнете някакви проблеми, моля, свържете се с нас на %(email)s и включете възможно най-много информация (като екранни снимки). ✅ Благодаря за вашето дарение! Анна ръчно ще активира вашето членство в рамките на няколко дни. Изпратете разписка или екранна снимка на вашия адрес за потвърждение: Когато изпратите разписката си по имейл, щракнете върху този бутон, за да може Анна да я прегледа ръчно (това може да отнеме няколко дни): Изпратете разписка или екранна снимка на вашия адрес за верификация. НЕ използвайте този имейл за вашето дарение чрез PayPal. Отказ Да, моля отменете Сигурни ли сте, че искате да се откажете? Не се отказвайте, ако вече сте платили. ❌Нещо се обърка. Презаредете страницата и опитайте отново. Направете ново дарение ✅Вашето дарение е отменено. Дата: %(date)s Идентификатор: %(id)s Пренареждане Статус: <span %(span_label)s>%(label)s</span> Общо: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месец за %(duration)s месеца, включително %(discounts)s%% отстъпка)</span> Общо: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месец за %(duration)s месеци)</span> 1. Въведете вашия имейл. 2. Изберете вашия начин на плащане. 3. Изберете вашия начин на плащане отново. 4. Изберете портфейла “Лично хостван”. 5. Щракнете върху “Потвърждавам собствеността”. 6. Трябва да получите разписката по имейл. Моля, изпратете ни я и ние ще потвърдим вашето дарение възможно най-скоро. (може да искате да отмените и да създадете ново дарение) Инструкциите за плащане вече са остарели. Ако желаете да направите друго дарение, използвайте бутона “Повторна поръчка” по-горе. Вие вече сте платили. Ако все пак искате да прегледате инструкциите за плащане, щракнете тук: Показване на стари инструкции за плащане Ако страницата за дарения е блокирана, опитайте с различна интернет връзка (например VPN или интернет от телефон). За съжаление, страницата на Alipay често е достъпна само от <strong>континентален Китай</strong>. Може да се наложи временно да деактивирате вашия VPN или да използвате VPN към континентален Китай (или Хонконг също понякога работи). <span %(span_circle)s>1</span>Дарете чрез Alipay Дарете общата сума от %(total)s чрез <a %(a_account)s>този Alipay акаунт</a> Alipay инструкции <span %(span_circle)s>1</span>Прехвърлете към една от нашите крипто сметки Дарете цялата сума на %(total)s до един от тези адреси: Крипто инструкции Следвайте инструкциите, за да закупите биткойн (BTC). Трябва само да купите сумата, която искате да дарите, %(total)s. Въведете нашия биткойн (BTC) адрес като получател и следвайте инструкциите, за да изпратите вашето дарение от %(total)s: <span %(span_circle)s>1</span>Дарете чрез Pix Дарете общата сума от %(total)s, като използвате <a %(a_account)s>този Pix акаунт Pix инструкции <span %(span_circle)s>1</span>Дарете чрез WeChat Дарете общата сума от %(total)s чрез <a %(a_account)s>този WeChat акаунт</a> Инструкции за WeChat Използвайте някоя от следните експресни услуги “кредитна карта към Bitcoin”, които отнемат само няколко минути: BTC / Bitcoin адрес (външен портфейл): BTC / Bitcoin сума: Попълнете следните данни във формуляра: Ако някоя от тази информация е остаряла, моля, изпратете ни имейл, за да ни уведомите. Моля, използвайте този <span %(underline)s>точен размер</span>. Общата ви цена може да бъде по-висока заради таксите за кредитни карти. За малки суми това може да е повече от нашата отстъпка, за съжаление. (минимум: %(minimum)s) (минимум: %(minimum)s) (минимум: %(minimum)s) (минимум: %(minimum)s, без верификация за първата трансакция) (минимум: %(minimum)s) (минимум: %(minimum)s в зависимост от страната, без верификация за първата трансакция) Следвайте инструкциите, за да купите монета PYUSD (PayPal USD). Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за транзакция. Ще запазите всичко останало. Отидете на страницата “PYUSD” във вашето приложение или уебсайт на PayPal. Натиснете бутона “Прехвърляне” %(icon)s и след това “Изпращане”. Актуализиране на състоянието За да нулирате таймера, просто създайте ново дарение. Уверете се, че използвате сумата в BTC по-долу, <em>НЕ</em> в евро или долари, в противен случай няма да получим правилната сума и не можем автоматично да потвърдим членството ви. Купете Bitcoin (BTC) в Revolut Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за трансакции. Ще запазите всичко, което остане. Отидете на страницата “Crypto” в Revolut, за да купите Bitcoin (BTC). Прехвърлете Биткойн на нашия адрес За малки дарения (под $25) може да се наложи да използвате Rush или Priority. Кликнете върху бутона “Изпрати биткойн”, за да направите “теглене”. Превключете от евро на BTC, като натиснете иконата %(icon)s. Въведете сумата в BTC по-долу и кликнете “Изпрати”. Вижте <a %(help_video)s>това видео</a>, ако се затрудните. Статус: 1 2 Ръководство стъпка по стъпка Вижте ръководството стъпка по стъпка по-долу. В противен случай може да загубите достъп до този акаунт! Ако още не сте го направили, запишете своя секретен ключ за влизане: Благодарим ви за вашето дарение! Оставащо време: Дарение Прехвърлете %(amount)s към %(account)s Изчаква се потвърждение (опреснете страницата, за да проверите)… Изчаква се прехвърляне (опреснете страницата, за да проверите)… По-рано Бързите изтегляния през последните 24 часа са част от дневния лимит. Изтеглянията от Fast Partner Servers са маркирани с %(icon)s. Последните 18 часа Все още няма изтеглени файлове. Изтеглените файлове не се показват публично. Всички времена са в UTC. Изтеглени файлове Ако сте изтеглили файла с бързо и бавно изтегляне, той ще се появи два пъти. Не се притеснявайте твърде много, има много хора, които изтеглят от сайтове, свързани с нас, и е изключително рядко да попаднете в неприятности. Въпреки това, за да сте в безопасност, препоръчваме да използвате VPN (платен) или <a %(a_tor)s>Tor</a> (безплатен). Изтеглих 1984 от Джордж Оруел, ще дойде ли полицията на вратата ми? Вие сте Анна! Коя е Анна? Имаме един стабилен JSON API за членове, за получаване на бърз адрес за изтегляне: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (документация в самия JSON). За други случаи на употреба, като например повторно минаване през всички наши файлове, изграждане на персонализирано търсене и т.н., препоръчваме <a %(a_generate)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB. Необработените данни могат да бъдат ръчно изследвани <a %(a_explore)s>чрез JSON файлове</a>. Нашият списък със необработени торенти може да бъде изтеглен и като <a %(a_torrents)s>JSON</a>. Имате ли API? Ние не хостваме никакви материали, защитени с авторски права тук. Ние сме търсачка и като такава индексираме само метаданни, които вече са публично достъпни. Когато изтегляте от тези външни източници, препоръчваме да проверите законите във вашата държава относно това, което е позволено. Ние не носим отговорност за съдържание, хоствано от други. Ако имате оплаквания относно това, което виждате тук, най-добрият ви залог е да се свържете с оригиналния сайт. Редовно внасяме техните промени в нашата база данни. Ако наистина смятате, че имате валидна жалба по DMCA, на която трябва да отговорим, моля, попълнете <a %(a_copyright)s>формуляра за жалба по DMCA / Авторски права</a>. Вземаме вашите оплаквания на сериозно и ще се свържем с вас възможно най-скоро. Как да докладвам за нарушение на авторските права? Ето някои книги, които имат специално значение за света на сенчестите библиотеки и цифровото съхранение: Кои са вашите любими книги? Бихме искали също да напомним на всички, че целият ни код и данни са напълно с отворени. Това е уникално за проекти като нашия — ние не знаем за друг проект с подобен масивен каталог, който също е с напълно отворен код. Напълно приемаме всеки, който смята, че управляваме проекта си лошо, да вземе нашия код и данни, и да създаде своя собствена сенчеста библиотека! Не казваме това от злоба или нещо подобно — наистина мислим, че това би било страхотно, тъй като ще повиши стандарта за всички и ще запази по-добре наследството на човечеството. Мразя как управлявате този проект! Ще се радваме хората да създават <a %(a_mirrors)s>огледала</a>, и ние ще ги подкрепим финансово. Как мога да помогна? Наистина го правим. Нашето вдъхновение за събиране на метаданни е целта на Аарон Суорц за “една уеб страница за всяка книга, която някога е била публикувана”, за която той създаде <a %(a_openlib)s>Open Library</a>. Този проект се справя добре, но нашата уникална позиция ни позволява да получим метаданни, които те не могат. Друго вдъхновение беше нашето желание да знаем <a %(a_blog)s>колко книги има в света</a>, за да можем да изчислим колко книги все още трябва да спасим. Събирате ли метаданни? Имайте предвид, че mhut.org блокира определени IP диапазони, така че може да е необходим VPN. <strong>Android:</strong> Натиснете менюто с три точки в горния десен ъгъл и изберете “Добавяне към началния екран”. <strong>iOS:</strong> Натиснете бутона “Сподели” в долната част и изберете “Добави към началния екран”. Нямаме официално мобилно приложение, но можете да инсталирате този сайт като приложение. Имате ли мобилно приложение? Моля, изпратете ги на <a %(a_archive)s>Internet Archive</a>. Те ще ги съхранят правилно. Как да даря книги или други физически материали? Как да заявя книги? <a %(a_blog)s>Блогът на Анна</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — редовни актуализации <a %(a_software)s>Софтуер на Анна</a> — нашият отворен код <a %(a_datasets)s>Набор от данни</a> — за данните <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — алтернативни домейни Има ли повече ресурси за Архива на Анна? <a %(a_translate)s>Превод на Софтуера на Анна</a> — нашата система за превод <a %(a_wikipedia)s>Уикипедия</a> — повече за нас (моля, помогнете да поддържаме тази страница актуална или създайте една за вашия собствен език!) Изберете настройките, които харесвате, оставете полето за търсене празно, кликнете върху “Търсене” и след това запазете страницата, използвайки функцията за отметки на вашия браузър. Как да запазя настройките на търсенето си? Приветстваме изследователи по сигурността да търсят уязвимости в нашите системи. Ние сме големи поддръжници на отговорното разкриване. Свържете се с нас <a %(a_contact)s>тук</a>. В момента не можем да присъждаме награди за откриване на уязвимости, освен за уязвимости, които имат <a %(a_link)s>потенциал да компрометират нашата анонимност</a>, за които предлагаме награди в диапазона от $10k-50k. Бихме искали да предложим по-широк обхват за награди в бъдеще! Моля, имайте предвид, че социалните инженерни атаки са извън обхвата. Ако се интересувате от офанзивна сигурност и искате да помогнете за архивирането на световното знание и култура, не се колебайте да се свържете с нас. Има много начини, по които можете да помогнете. Имате ли програма за отговорно разкриване? Буквално нямаме достатъчно ресурси, за да предоставим на всеки в света високоскоростни изтегляния, колкото и да искаме. Ако богат благодетел иска да ни помогне и да осигури това за нас, би било невероятно, но дотогава ние правим всичко възможно. Ние сме проект с нестопанска цел, който едва се издържа чрез дарения. Ето защо внедрихме две системи за безплатни изтегляния, с нашите партньори: споделени сървъри с бавни изтегляния и малко по-бързи сървъри с листа на изчакване (за да намалим броя на хората, които изтеглят едновременно). Имаме и <a %(a_verification)s>проверка чрез браузъра</a> за нашите бавни изтегляния, защото ботове и скрепери ще злоупотребят с тях, правейки нещата още по-бавни за истинските потребители. Имайте предвид, че при използване на Tor Browser може да се наложи да коригирате вашите настройки за сигурност. При най-ниската от възможностите, наречена “Стандартна”, предизвикателството на Cloudflare turnstile успява. При по-високите възможности, наречени “По-безопасна” и “Най-безопасна”, предизвикателството не успява. Понякога при изтегляне на големи файлове, бавните изтегляния могат да се прекъснат по средата. Препоръчваме използването на мениджър за изтегляне (като JDownloader), за да се възобновят автоматично големите изтегляния. Защо бавните изтегляния са толкова бавни? Често задавани въпроси (FAQ) Използвайте <a %(a_list)s>генератора на списъци с торенти</a>, за да генерирате списък с торенти, които най-много се нуждаят от качване, в рамките на вашите ограничения за съхранение. Да, вижте страницата <a %(a_llm)s>данни на LLM</a>. Повечето торенти съдържат файловете директно, което означава, че можете да инструктирате торент клиентите да изтеглят само необходимите файлове. За да определите кои файлове да изтеглите, можете да <a %(a_generate)s>генерирате</a> нашите метаданни или да <a %(a_download)s>изтеглите</a> нашите бази данни ElasticSearch и MariaDB. За съжаление, редица торент колекции съдържат .zip или .tar файлове в основата си, в този случай трябва да изтеглите целия торент, преди да можете да изберете отделните файлове. Все още няма лесни за използване инструменти за филтриране на торенти, но приемаме съдействие. (Имаме <a %(a_ideas)s>някои идеи</a> за последния случай обаче.) Дълъг отговор: Кратък отговор: не е лесно. Опитваме се да поддържаме минимално дублиране или припокриване между торентите в този списък, но това не винаги може да бъде постигнато, и зависи силно от политиките на техните библиотеки. За библиотеките, които пускат свои торенти, това не е в нашите ръце. За торентите, пуснати от Архива на Анна, дублирането се премахва само въз основа на MD5 хеш, което означава, че различните версии на една и съща книга не се дублират. Да. Това всъщност са PDF и EPUB файлове, те просто нямат разширение в много от нашите торенти. Има две места, където можете да намерите метаданни за торент файловете, включително типовете/разширенията на файловете: 1. Всяка колекция или издание има свои метаданни. Например, <a %(a_libgen_nonfic)s>Libgen.rs торенти</a> имат съответната база данни с метаданни, хоствана на сайта на Libgen.rs. Обикновено, свързваме към съответните ресурси с метаданни от страницата с <a %(a_datasets)s>набора от данни на всяка колекция</a>. 2. Препоръчваме <a %(a_generate)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB. Те съдържат карта за всеки запис в Архива на Анна до съответните торент файлове (ако са налични), под “torrent_paths” в ElasticSearch JSON. Някои торент клиенти не поддържат големи размери на частите, които много от нашите торенти имат (за по-новите вече не го правим — въпреки че е валидно според спецификациите!). Затова опитайте с друг клиент, ако срещнете този проблем, или се оплачете на създателите на вашия торент клиент. Искам да помогна със качването, но нямам много дисково пространство. Торентите са твърде бавни; мога ли да изтегля данните директно от вас? Мога ли да изтегля само подмножество от файловете, като например само определен език или тема? Как се справяте с дублиращите се файлове в торентите? Мога ли да получа списъка с торенти като JSON? Не виждам PDF или EPUB файлове в торентите, само бинарни файлове? Какво да правя? Защо моят торент клиент не може да отвори някои от вашите торент файлове / магнитни връзки? ЧЗВ за торенти Как да кача нови книги? Моля, вижте <a %(a_href)s>този отличен проект</a>. Имате ли наблюдение на непрекъсността на работата? Какво е Архивът на Анна? Станете член, за да получите достъп до бързите изтегляния. Сега поддържаме Amazon подаръчни карти, кредитни и дебитни карти, криптовалути, Alipay и WeChat. Днес ви свършиха бързите изтегляния. Достъп Почасови изтегляния през последните 30 дни. Средна часова стойност: %(hourly)s. Средна дневна стойност: %(daily)s. Работим с партньори, за да направим колекциите си лесно и свободно достъпни за всеки. Вярваме, че всеки има право на достъп до колективната мъдрост на човечеството. И <a %(a_search)s>не за сметка на авторите</a>. Наборите от данни, използвани в архива на Анна, са напълно отворени и могат да бъдат дублирани групово чрез торенти. <a %(a_datasets)s>Научете повече...</a> Дългосрочен архив Пълна база данни Търсене Книги, вестници, списания, комикси, библиотечни записи, метаданни, … Целият ни <a %(a_code)s>код</a> и <a %(a_datasets)s>данни</a> са напълно с отворен код. <span %(span_anna)s>Anna’s Archive</span> е проект с нестопанска цел, който има две цели: <li><strong>Съхранение</strong> Запазване на всички знания и на културата на човечеството.</li><li><strong>Достъп:</strong> Осигуряване на достъп до тези знания и култура за всеки в света.</li> Имаме най-голямата в света колекция от висококачествени текстови данни. <a %(a_llm)s>Научете повече...</a> LLM данни за обучение 🪩 Огледала: призив за доброволци Ако управлявате анонимен платежен процесор с висок риск, моля, свържете се с нас. Също така търсим хора, които искат да поставят изискани малки реклами. Всички приходи отиват за нашите усилия за съхранение. Съхранение Смятаме, че сме съхранили около <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% от книгите в света</a>. Съхраняваме книги, статии, комикси, списания и други, като събираме тези материали от различни <a href="https://en.wikipedia.org/wiki/Shadow_library">сенчести библиотеки</a>, официални библиотеки, и други колекции на едно място. Всички тези данни се съхраняват завинаги, като се улеснява дублирането им в големи количества — чрез торенти — което води до много копия по целия свят. Някои сенчести библиотеки вече правят това сами (напр. Sci-Hub, Library Genesis), докато Архивът на Анна “освобождава” други библиотеки, които не предлагат масово разпространение (напр. Z-Library) или изобщо не са сенчести библиотеки (напр. Internet Archive, DuXiu). Това широко разпространение, комбинирано с отворен код, прави нашия сайт устойчив на премахвания и осигурява дългосрочното запазване на човешкото знание и култура. Научете повече за <a href="/datasets">нашите набори от данни</a>. Ако сте <a %(a_member)s>член</a>, не е необходима проверка в браузъра. 🧬&nbsp;SciDB е продължение на Sci-Hub. SciDB - система за управление на база данни, ориентирана към колони Отворено DOI - името е цифров идентификатор на обект, всеки обект - физически, цифров или абстрактен Sci-Hub е <a %(a_paused)s>спрял</a> качването на нови статии. Директен достъп до %(count)s академични статии 🧬&nbsp;SciDB е продължение на Sci-Hub, с познатия интерфейс и директно преглеждане на PDF файлове. Въведете вашия DOI, за да прегледате. Имаме пълната колекция на Sci-Hub, както и нови статии. Повечето могат да се разглеждат директно с познат интерфейс, подобен на Sci-Hub. Някои могат да се изтеглят чрез външни източници, в такъв случай показваме връзки към тях. Можете да помогнете изключително много, като заредите торенти. <a %(a_torrents)s>Научете повече...</a> >%(count)s сийдове <%(count)s сийдове %(count_min)s–%(count_max)s сийдове 🤝 Търсим доброволци Като неправителствен, отворен проект, винаги търсим хора, които да помогнат. IPFS изтегляния Списък от %(by)s създаден на <span %(span_time)s>%(time)s</span> Запази ❌ Грешка. Моля, опитайте отново. ✅ Запазено. Моля опреснете страницата. Списъкът е празен. редактиране Добавете или премахнете нещо от този списък, като намерите файл и отворите раздела „Списъци“. Списък Как можем да помогнем Премахване на припокриване (елиминирането на дублиращата се или излишна информация) Извличане на текст и метаданни OCR Можем да предоставим високоскоростен достъп до нашите пълни колекции, както и до неиздадени колекции. Това е достъп на корпоративно ниво, който можем да предоставим за дарения в размер на десетки хиляди USD. Също така сме готови да разменим това за висококачествени колекции, които все още нямаме. Можем да ви възстановим сумата, ако можете да ни предоставите обогатяване на нашите данни, като например: Подкрепете дългосрочното архивиране на знанието, като същевременно получите по-добри данни за вашия модел! <a %(a_contact)s>Свържете се с нас</a>, за да обсъдим как можем да работим заедно. Добре е известно, че LLM-ите процъфтяват върху висококачествени данни. Ние разполагаме с най-голямата колекция от книги, статии, списания и др. в света, които са едни от най-качествените текстови източници. Данни за LLM Уникален мащаб и обхват Нашата колекция съдържа над сто милиона файла, включително академични списания, учебници и списания. Постигаме този мащаб, като комбинираме големи съществуващи хранилища. Някои от нашите източници на колекции вече са налични в насипно състояние (Sci-Hub и части от Libgen). Други източници ние сме освободили сами. <a %(a_datasets)s>Datasets</a> показва пълен преглед. Нашата колекция включва милиони книги, статии и списания от преди ерата на електронните книги. Големи части от тази колекция вече са сканирани и вече имат малко вътрешно припокриване. Продължи Ако сте загубили ключа си, моля, <a %(a_contact)s>свържете се с нас</a> и предоставете възможно най-много информация. Може да се наложи временно да създадете нов профил, за да се свържете с нас. Моля, <a %(a_account)s>влезте в системата</a>, за да видите тази страница.</a> За да предотвратим създаването на много акаунти от спамботове, първо трябва да проверим вашия браузър. Ако попаднете в безкраен цикъл, препоръчваме да инсталирате <a %(a_privacypass)s>Privacy Pass</a>. Може също да помогне да изключите рекламните блокери и други разширения на браузъра. Вход / Регистрация Архивът на Анна временно не работи за поддръжка. Моля, върнете се след час. Алтернативен автор Алтернативно описание Алтернативно издание Алтернативно разширение Алтернативно име на файл Алтернативен издател Алтернативно заглавие данни с отворен код Прочетете още… описание Потърсете в архива на Анна номер на CADAL SSNO Потърсете в архива на Anna SSID номер на DuXiu Търсене в Архива на Анна за DuXiu DXID номер Потърсете с ISBN в архива на Анна Потърсете в архива на Анна номера на OCLC (WorldCat) Потърсете в архива на Ана за Open Library ID Онлайн визуализатор на Архива на Анна %(count)s засегнати страници След изтегляне: По-добра версия на този файл може да е достъпна на %(link)s Групово изтегляне на торенти колекция Използвайте онлайн инструменти за конвертиране между формати. Препоръчани инструменти за конвертиране: %(links)s За големи файлове препоръчваме използването на мениджър за изтегляне, за да се предотвратят прекъсвания. Препоръчани мениджъри за изтегляне: %(links)s EBSCOhost eBook Index (само за експерти) (също натиснете “GET” горе) (натиснете “GET” горе) Външни изтегляния Остават ви %(remaining)s за днес. Благодарим Ви, че сте член! ❤️ Изчерпали сте бързите изтегляния за днес. Изтеглили сте този файл наскоро. Връзките остават валидни за известно време. Станете <a %(a_membership)s>член</a>, за да подкрепите дългосрочното съхранение на книги, документи и др. В знак на благодарност за Вашата подкрепа получавате бързи изтегляния. ❤️ 🚀 Бързи изтегляния 🐢 Бавни изтегляния Вземете назаем от интернет архива ИПФС Вход #%(num)d (може да се наложи да пробвате няколко пъти с ИПФС) Libgen.li Libgen.rs Художествена Libgen.rs Нехудожествена техните реклами са известни с това, че съдържат злонамерен софтуер, затова използвайте блокер на реклами или не кликвайте върху реклами Amazon‘s “Send to Kindle” djazz‘s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Файловете на Nexus/STC могат да бъдат ненадеждни за изтегляне) Няма намерени изтегляния. Всички сървъри огледало обслужват един и същ файл и трябва да са безопасни за използване. Въпреки това винаги бъдете внимателни, когато изтегляте файлове от интернет. Например, не забравяйте да актуализирате устройствата си. (без пренасочване) Отвори в нашия визуализатор (отвори във визуализатор) Опция №%(num)d: %(link)s %(extra)s Намерете оригиналния запис в CADAL Търсете ръчно в DuXiu Намерете първоначалния запис в ISBNdb Намерете оригинален запис в WorldCat Намерете първоначалния запис в Open Library Търсете в различни бази данни с ISBN (само за поддръжници с деактивиран печат) PubMed Ще ви е необходим четец за електронни книги или PDF, за да отворите файла, в зависимост от формата на файла. Препоръчани четци за електронни книги: %(links)s Ана Архив 🧬 SciDB свободен и неограничен достъп до цялото научно знание Sci-Hub: %(doi)s (свързаният идентификатор на дигитален обект може да не е наличен в Sci-Hub) Можете да изпращате както PDF, така и EPUB файлове на вашия Kindle или Kobo eReader. Препоръчани инструменти: %(links)s Повече информация в <a %(a_slow)s>ЧЗВ</a>. Подкрепете авторите и библиотеките Ако това ви харесва и можете да си го позволите, обмислете да закупите оригинала или да подкрепите авторите директно. Ако това е налично във вашата местна библиотека, обмислете да го заемете безплатно оттам. Изтеглянията чрез партньорския сървър временно не са достъпни за този файл. торент От доверени партньори. Z-Library Z-Library TOR (изисква TOR браузър) покажи външни изтегляния <span class="font-bold">❌ Този файл може да има проблеми и е скрит от библиотека източник.</span> Понякога това е по искане на притежател на авторски права, понякога е защото е налична по-добра алтернатива, но понякога се дължи на проблем със самия файл. Все още може да е добре за изтегляне, но препоръчваме първо да потърсите алтернативен файл. Повече информация: Ако все пак искате да изтеглите този файл, уверете се, че използвате само надежден, актуализиран софтуер, за да го отворите. коментари за метаданни AA: Търсене в Архива на Анна за “%(name)s” Изследовател на кодове: Преглед в Codes Explorer “%(name)s” URL: Сайт: Ако имате този файл и той все още не е наличен в Архива на Анна, обмислете <a %(a_request)s>да го качите</a>. Файл за контролирано цифрово заемане на интернет архив “%(id)s” Това е запис на файл от Интернет архива, а не файл за директно изтегляне. Можете да опитате да заемете книгата (връзка по-долу) или да използвате този адрес, когато <a %(a_request)s>искате файла</a>. Подобряване на метаданни CADAL SSNO %(id)s метаданни запис Това е запис на метаданни, а не файл за изтегляне. Можете да използвате този адрес, когато <a %(a_request)s>искате файла</a>. DuXiu SSID %(id)s метаданни запис ISBNdb %(id)s запис на метаданни MagzDB ID %(id)s метаданни запис Nexus/STC ID %(id)s метаданни запис OCLC (WorldCat) номер %(id)s запис на метаданни Open Library %(id)s запис на метаданни Sci-Hub файл “%(id)s” Не е намерено „%(md5_input)s“ не беше намерен в нашата база данни. Добавете коментар (%(count)s) Можете да получите md5 от адреса, напр. MD5 на по-добра версия на този файл (ако е приложимо). Попълнете това, ако има друг файл, който много прилича на този файл (същото издание, същото разширение на файла, ако можете да намерите такъв), който хората трябва да използват вместо този файл. Ако знаете за по-добра версия на този файл извън Архива на Анна, тогава моля <a %(a_upload)s>качете го</a>. Нещо се обърка. Моля, презаредете страницата и опитайте отново. Оставихте коментар. Може да отнеме минута, докато се покаже. Моля, използвайте <a %(a_copyright)s>формуляра за искове по DMCA / авторски права</a>. Опишете проблема (задължително) Ако този файл е с високо качество, можете да обсъдите всичко за него тук! Ако не, моля използвайте бутона “Докладвай проблем с файла”. Отлично качество на файла (%(count)s) Качество на файла Научете как да <a %(a_metadata)s>подобрите метаданните</a> за този файл сами. Описание на проблема Моля, <a %(a_login)s>влезте в системата</a>. Обожавам тази книга! Помогнете на общността, като докладвате качеството на този файл! 🙌 Нещо се обърка. Моля, презаредете страницата и опитайте отново. Докладвайте проблем с файла (%(count)s) Благодарим ви, че подадохте вашия доклад. Той ще бъде показан на тази страница, както и прегледан ръчно от Анна (докато не въведем подходяща система за модерация). Оставете коментар Изпратете доклада Какво не е наред с този файл? Взимам на заем (%(count)s) Коментари (%(count)s) Изтегляния (%(count)s) Разглеждане на метаданните (%(count)s) Списъци (%(count)s) Статистика (%(count)s) За информация относно този конкретен файл, разгледайте неговия <a %(a_href)s>JSON файл</a>. Това е файл, управляван от библиотеката <a %(a_ia)s>IA’s Controlled Digital Lending</a> и индексиран от Архива на Анна за търсене. За информация относно различните datasets, които сме компилирали, вижте <a %(a_datasets)s>страницата с Datasets</a>. Метаданни от свързан запис Подобрете метаданните в Open Library “MD5 на файл” е хеш, който се изчислява от съдържанието на файла и е уникален въз основа на това съдържание. Всички сенчести библиотеки, които сме индексирали тук, основно използват MD5 за идентифициране на файлове. Един файл може да се появи в множество сенчести библиотеки. За информация относно различните datasets, които сме компилирали, вижте <a %(a_datasets)s>страницата с Datasets</a>. Докладвайте качеството на файла Общо изтегляния: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Чешки метаданни %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Предупреждение: множество свързани записи: Когато разглеждате книга в Архива на Анна, можете да видите различни полета: заглавие, автор, издател, издание, година, описание, име на файл и други. Всички тези части от информацията се наричат <em>метаданни</em>. Тъй като комбинираме книги от различни <em>библиотеки</em>, показваме каквито метаданни са налични в тази библиотека. Например, за книга, която сме получили от Library Genesis, ще покажем заглавието от базата данни на Library Genesis. Понякога една книга присъства в <em>няколко</em> библиотеки, които може да имат различни полета за метаданни. В такъв случай просто показваме най-дългата версия на всяко поле, тъй като тя вероятно съдържа най-полезната информация! Все пак ще покажем и другите полета под описанието, например като „алтернативно заглавие“ (но само ако са различни). Също така извличаме <em>кодове</em> като идентификатори и класификатори от библиотеката. <em>Идентификаторите</em> уникално представят конкретно издание на книга; примери са ISBN, DOI, Open Library ID, Google Books ID или Amazon ID. <em>Класификаторите</em> групират заедно няколко подобни книги; примери са Dewey Decimal (DCC), UDC, LCC, RVK или GOST. Понякога тези кодове са изрично свързани в библиотеките, а понякога можем да ги извлечем от името на файла или описанието (главно ISBN и DOI). Можем да използваме идентификаторите, за да намерим записи в <em>колекции само с метаданни</em>, като OpenLibrary, ISBNdb или WorldCat/OCLC. Има специален <em>раздел с метаданни</em> в нашата търсачка, ако искате да разгледате тези колекции. Използваме съвпадащи записи, за да запълним липсващите полета за метаданни (например ако липсва заглавие) или например като “алтернативно заглавие” (ако вече има съществуващо заглавие). За да видите точно откъде са дошли метаданните на книгата, вижте <em>“Технически детайли” таб</em> на страницата на книгата. Има връзка към необработения JSON за тази книга, с указатели към необработения JSON на оригиналните записи. За повече информация, вижте следните страници: <a %(a_datasets)s>Набори от данни</a>, <a %(a_search_metadata)s>Търсене (раздел метаданни)</a>, <a %(a_codes)s>Изследовател на кодове</a> и <a %(a_example)s>Примерен JSON с метаданни </a>. Накрая, всички наши метаданни могат да бъдат <a %(a_generated)s>генерирани</a> или <a %(a_downloaded)s>изтеглени</a> като ElasticSearch и MariaDB бази данни. Произход Можете да помогнете за съхранението на книги, като подобрите метаданните! Първо, прочетете информацията за метаданните в Архива на Анна, а след това научете как да подобрите метаданните чрез свързване с Open Library и спечелете безплатно членство в Архива на Анна. Подобряване на метаданни И така, ако срещнете файл с лоши метаданни, как трябва да го поправите? Можете да отидете в библиотеката му и да следвате нейните процедури за поправка на метаданни, но какво да правите, ако файлът присъства в няколко библиотеки? Има един идентификатор, който се третира специално в Архива на Анна. <strong>Полето annas_archive md5 в Open Library винаги замества всички други метаданни!</strong> Нека първо се върнем малко назад и да научим за Open Library. Open Library е основана през 2006 г. от Аарон Суорц с целта “една уеб страница за всяка книга, която е била публикувана някога”. Това е нещо като Wikipedia за метаданни на книги: всеки може да я редактира, тя е с безплатен лиценз и може да бъде изтеглена на части. Това е база данни за книги, която най-много съответства на нашата мисия — всъщност, Архивът на Анна е вдъхновен от визията и живота на Аарон Суорц. Вместо да преоткриваме колелото, решихме да насочим нашите доброволци към Open Library. Ако видите книга с неправилни метаданни, можете да помогнете по следния начин: Имайте предвид, че това работи само за книги, а не за академични статии или други видове файлове. За други видове файлове все още препоръчваме да намерите библиотеката им. Може да отнеме няколко седмици, за да бъдат включени промените в Архивът на Анна, тъй като трябва да изтеглим последния Open Library архив и да обновим нашия индекс за търсене.  Отидете на <a %(a_openlib)s>сайта на Open Library</a>. Намерете правилния запис на книгата. <strong>ВНИМАНИЕ:</strong> бъдете сигурни, че сте избрали правилното <strong>издание</strong>. В Open Library има “произведения” и “издания”. “Произведение” може да бъде “Хари Потър и философският камък”. “Издание” може да бъде: Първото издание от 1997 г., публикувано от Bloomsbery с 256 страници. Изданието с меки корици от 2003 г., публикувано от Raincoast Books с 223 страници. Полският превод от 2000 г. “Harry Potter I Kamie Filozoficzn” от Media Rodzina с 328 страници. Всички тези издания имат различни ISBN и различно съдържание, така че бъдете сигурни, че избирате правилното! Редактирайте записа (или го създайте, ако не съществува), и добавете колкото се може повече полезна информация! Вече сте тук, така че направете записа наистина невероятен. Под “ID Numbers” изберете “Архивът на Анна” и добавете MD5 на книгата от Архивът на Анна. Това е дългият низ от букви и цифри след “/md5/” в адреса. Опитайте се да намерите други файлове в Архивът на Анна, които също съответстват на този запис, и ги добавете също. В бъдеще можем да ги групираме като дубликати на страницата за търсене в Архивът на Анна. Когато сте готови, запишете адреса, който току-що обновихте. След като обновихте поне 30 записа с MD5 от Архивът на Анна, изпратете ни <a %(a_contact)s>имейл</a> със списъка. Ще ви дадем безплатно членство за Архивът на Анна, за да можете по-лесно да вършите тази работа (и като благодарност за вашата помощ). Тези редакции трябва да бъдат с високо качество и да добавят значителни количества информация, в противен случай вашата заявка ще бъде отхвърлена. Вашата заявка също ще бъде отхвърлена, ако някоя от редакциите бъде отменена или коригирана от модераторите на Open Library. Свързване с Open Library Ако се включите значително в разработката и операциите на нашата работа, можем да обсъдим споделянето на повече от приходите от дарения с вас, за да ги използвате при необходимост. Ще плащаме за хостинг само след като всичко е настроено и сте демонстрирали, че можете да поддържате архива с обновени данни. Това означава, че ще трябва да платите за първите 1-2 месеца от джоба си. Вашето време няма да бъде компенсирано (и нашето също), тъй като това е чисто доброволческа работа. Ние сме готови да покрием разходите за хостинг и VPN, първоначално до $200 на месец. Това е достатъчно за основен сървър за търсене и прокси, защитен от DMCA. Разходи за хостинг Моля, <strong>не се свързвайте с нас</strong> за разрешение или за основни въпроси. Действията говорят по-силно от думите! Цялата информация е налична, така че просто продължете с настройването на вашето огледало. Чувствайте се свободни да публикувате билети или заявки за сливане в нашия Gitlab, когато срещнете проблеми. Може да се наложи да изградим някои специфични за огледалото функции с вас, като например ребрандиране от “Архивът на Анна” към името на вашия сайт, (първоначално) деактивиране на потребителски акаунти или свързване обратно към нашия основен сайт от страниците на книгите. След като вашето огледало работи, моля, свържете се с нас. Ще се радваме да прегледаме вашата OpSec, и след като това е стабилно, ще свържем към вашето огледало и ще започнем да работим по-близо с вас. Предварително благодарим на всеки, който е готов да допринесе по този начин! Това не е за слабите сърца, но ще затвърди дълголетието на най-голямата ,наистина, отворена библиотека в историята. Първи стъпки За да увеличим устойчивостта на Архива на Анна, търсим доброволци за управление на огледалата. Вашата версия е ясно разграничена като огледало, напр. “Архивът на Боб, огледало на Архивът на Анна”. Вие сте готови да поемете рисковете, свързани с тази работа, които са значителни. Имате дълбоко разбиране за необходимата оперативна сигурност. Съдържанието на <a %(a_shadow)s>тези</a> <a %(a_pirate)s>публикации</a> е очевидно за вас. Първоначално няма да ви дадем достъп до изтеглянията от нашия партньорски сървър, но ако нещата вървят добре, можем да споделим това с вас. Вие управлявате отворения код на Архивът на Анна и редовно обновявате както кода, така и данните. Вие сте готови да допринесете за нашия <a %(a_codebase)s>код</a> — в сътрудничество с нашия екип — за да направите това възможно. Търсим това: Огледала: призив за доброволци Направи друго дарение. Все още няма дарения. <a %(a_donate)s>Направи първото ми дарение.</a> Подробностите за даренията не се показват публично. Моите дарения 📡 За групово отразяване на нашата колекция вижте <a %(a_datasets)s>Набори от данни</a> и <a %(a_torrents)s>Торенти</a> страници. Изтегляния от вашия IP адрес през последните 24 часа: %(count)s. За да получите достъп до по-дързи изтегляния и за да пропуснете проверките на браузъра Ви, <a %(a_membership)s>станете член</a>. Изтегли от партньорски уебсайт Чувствайте се свободни да продължите да разглеждате Архивът на Анна в друг раздел, докато чакате (ако вашият браузър поддържа опресняване на фоновите раздели). Чувствайте се свободни да изчакате зареждането на няколко страници за изтегляне едновременно (но моля, изтегляйте само един файл едновременно на сървър). След като получите връзка за изтегляне, той е валиден за няколко часа. Благодарим ви за търпението, това поддържа сайта безплатен за всички! 😊 <a %(a_main)s>&lt; Всички връзки за изтегляне на този файл:</a> ❌ Бавните изтегляния не са налични чрез Cloudflare VPN или от IP адреси на Cloudflare. ❌ Бавните изтегляния са достъпни само през официалния уебсайт. Посетете %(websites)s. <a %(a_download)s>📚 Изтеглете сега</a> За да дадете възможност на всички да изтеглят файловете безплатно, трябва да изчакате, преди да можете да изтеглите този файл. Моля, изчакайте <span %(span_countdown)s>%(wait_seconds)s</span> секунди, за да изтеглите този файл. Внимание: имаше много изтегляния от вашия IP адрес през последните 24 часа. Изтеглянията може да са по-бавни от обикновено. Ако използвате VPN, споделена интернет връзка или вашият интернет доставчик споделя IP адреси, това предупреждение може да се дължи на това. Запази ❌ Грешка. Моля, опитайте отново. ✅ Запазено. Моля, опреснете страницата. Променете името си. Идентификаторът Ви (частта след "#") не може да бъде променян. Профилът е създаден на <span %(span_time)s>%(time)s</span> редактиране Списъци Създайте нов списък, като намерите файл и отворите раздела "Списъци". Все още няма списъци Профилът не е намерен. Профил В момента не можем да удовлетворим заявките за книги. Не ни изпращайте заявки за книги по имейл. Моля, направете вашите заявки във форумите на Z-Library или Libgen. Запис в архива на Анна DOI: %(doi)s цифров идентификатор на обект Изтегли SciDB - система за управление на база данни (СУБД) с колони Nexus/STC Все още няма наличен преглед. Изтеглете файла от <a %(a_path)s>Архивът на Анна</a>. За да подкрепите достъпността и дългосрочното съхранение на това знание, станете <a %(a_donate)s>член</a>. Като бонус, 🧬&nbsp;SciDB се зарежда по-бързо за членовете, без никакви ограничения. Не работи? Опитай <a %(a_refresh)s>обновяване на браузъра си</a>. Sci-Hub библиотека за научни статии Добавяне на специфично поле за търсене Търсене на описания и коментари за метаданни Година на публикуване Разширено Достъп Съдържание Показване Списък Таблица Файлов тип Език Подредени по Най-голям Най-уместени Най-нови (размер на файла) (с отворен код) (година на публикация) Най-стари Случайно Малък Източник извлечени и с отворен код от AA Дигитално кредитиране (%(count)s) Статии от списания (%(count)s) Открихме съвпадения във: %(in)s. Можете да се обърнете към URL адреса, намерен там, когато <a %(a_request)s>искане на файл</a>. Metadata (%(count)s) За да разгледате индекса за търсене по кодове, използвайте <a %(a_href)s>Codes Explorer</a>. Индексът за търсене се актуализира ежемесечно. В момента включва записи до %(last_data_refresh_date)s. За повече техническа информация вижте %(link_open_tag)sстраница с набори от данни</a>. Изключете Включване само Непроверено Повече … Следващ … Предишен Този индекс за търсене в момента включва метаданни от библиотеката за контролирано цифрово заемане на интернет архива. <a %(a_datasets)s>Повече за нашите набори от данни</a>. За още цифрови библиотеки за заемане вижте <a %(a_wikipedia)s>Wikipedia</a> и <a %(a_mobileread)s>MobileRead Wiki</a>. За искове по DMCA / авторски права <a %(a_copyright)s>click here</a>. Време за изтегляне Грешка при търсенето. Опитайте <a %(a_reload)s>презареждане на страницата</a>. Ако проблемът продължава, изпратете ни имейл на %(email)s. Бързо изтегляне Всъщност, всеки може да помогне за запазването на тези файлове, като сийдва нашия <a %(a_torrents)s>обединен списък с торенти</a>. ➡️ Понякога това се случва неправилно, когато сървърът за търсене е бавен. В такива случаи, <a %(a_attrs)s>презареждането</a> може да помогне. ❌ Този файл може да има проблеми. Търсите статии? Този индекс за търсене в момента включва метаданни от различни източници на метаданни. <a %(a_datasets)s>Повече за нашите набори от данни</a>. Има много, много източници на метаданни за писмени произведения по света. <a %(a_wikipedia)s>Това Wikipedia страница</a> е добро начало, но ако знаете за други добри списъци, моля, уведомете ни. За метаданни показваме оригиналните записи. Не правим никакво сливане на записи. В момента разполагаме с най-изчерпателния отворен каталог в света на книгите, документите и другите писмени произведения. Ние отразяваме със Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>и още</a>. <span %(classname)s>Няма намерени файлове.</span> Опитайте с по-малко или различни думи за търсене и филтри. Резултати %(from)s-%(to)s (%(total)s общо) Ако намерите други “библиотеки в сянка”, които трябва да отразяваме, или ако имате въпроси, свържете се с нас на %(email)s. %(num)d частични съвпадения %(num)d+ частични съвпадения Въведете в полето, за да търсите файлове в цифрови библиотеки за заемане. Въведете в полето, за да търсите в нашия каталог с файлове %(count)s за директно изтегляне, които ние<a %(a_preserve)s>запазваме завинаги</a>. Въведете в полето, за да търсите. Въведете в полето, за да търсите в нашия каталог от %(count)s академични статии и журнали, които <a %(a_preserve)s>съхраняваме завинаги</a>. Въведете в полето, за да търсите метаданни от библиотеки. Това може да бъде полезно, когато <a %(a_request)s>искане на файл</a>. Съвет: използвайте клавишни комбинации “/” (фокус на търсенето), въведете (Търсене), “j” (нагоре), “k” (надолу) за по-бърза навигация. Това са записи на метаданни, <span %(classname)s>не</span> файлове за изтегляне. Настройки за търсене Търсене Дигитално кредитиране Download Статии от списания Метаданни Ново търсене %(search_input)s - Търси Търсенето отне твърде дълго време, което означава, че може да видите неточни резултати. Понякога <a %(a_reload)s>презареждането</a> на страницата помага. Търсенето отне твърде дълго време, което е обичайно за обширни заявки. Преброяването на филтъра може да не е точно. За големи качвания (над 10 000 файла), които не се приемат от Libgen или Z-Library, моля, свържете се с нас на %(a_email)s. За Libgen.li, уверете се, че първо сте влезли в <a %(a_forum)s>техния форум</a> с потребителско име %(username)s и парола %(password)s, и след това се върнете на тяхната <a %(a_upload_page)s>страница за качване</a>. Засега предлагаме да качвате нови книги във „разклоненията“ на LibGen. Ето едно <a %(a_guide)s>удобно указание</a>. Обърнете внимание на това, че и двете разклонения, които индексираме в този уебсайт, се извличат от същата система за качване. За малки качвания (до 10,000 файла) качете ги и на %(first)s, и на %(second)s. Алтернативно, можете да ги качите в Z-Library <a %(a_upload)s>тук</a>. За да качите академични статии, моля, освен в Library Genesis, качете ги и в <a %(a_stc_nexus)s>STC Nexus</a>. Те са най-добрата shadow library за нови статии. Все още не сме ги интегрирали, но ще го направим в някакъв момент. Можете да използвате техния <a %(a_telegram)s>бот за качване в Telegram</a> или да се свържете с адреса, посочен в тяхното съобщение, ако имате твърде много файлове за качване по този начин. <span %(label)s>Тежка доброволческа работа (награди от 50 до 5,000 USD):</span> ако можете да отделите много време и/или ресурси за нашата мисия, ще се радваме да работим по-тясно с вас. В крайна сметка можете да се присъедините към вътрешния екип. Въпреки че имаме ограничен бюджет, можем да присъждаме <span %(bold)s>💰 парични награди</span> за най-интензивната работа. <span %(label)s>Лека доброволческа работа:</span> ако можете да отделите само няколко часа тук и там, все още има много начини, по които можете да помогнете. Награждаваме последователните доброволци с <span %(bold)s>🤝 членства в Архивът на Анна</span>. Архивът на Анна разчита на доброволци като вас. Приветстваме всички нива на ангажираност и имаме две основни категории помощ, които търсим: Ако не можете да отделите време за доброволчество, все пак можете да ни помогнете много, като <a %(a_donate)s>дарите пари</a>, <a %(a_torrents)s>споделите нашите торенти</a>, <a %(a_uploading)s>качите книги</a> или <a %(a_help)s>разкажете на приятелите си за Архивът на Анна</a>. <span %(bold)s>Компании:</span> предлагаме високоскоростен директен достъп до нашите колекции в замяна на дарение на корпоративно ниво или в замяна на нови колекции (например нови сканирания, OCR’ed datasets, обогатяване на нашите данни). <a %(a_contact)s>Свържете се с нас</a>, ако това сте вие. Вижте също нашата <a %(a_llm)s>страница за LLM</a>. Награди Винаги търсим хора със солидни умения в програмирането или офанзивната сигурност, които да се включат. Можете да направите сериозен принос в запазването на наследството на човечеството. Като благодарност, даваме членство за солидни приноси. Като огромна благодарност, даваме парични награди за особено важни и трудни задачи. Това не трябва да се разглежда като заместител на работа, но е допълнителен стимул и може да помогне с възникналите разходи. Повечето от нашия код е с отворен код, и ще поискаме същото и от вашия код, когато присъждаме наградата. Има някои изключения, които можем да обсъдим на индивидуална основа. Наградите се присъждат на първия човек, който завърши задачата. Чувствайте се свободни да коментирате билет за награда, за да уведомите другите, че работите по нещо, така че другите да изчакат или да се свържат с вас, за да се обедините. Но бъдете наясно, че другите все още са свободни да работят по нея и да се опитат да ви изпреварят. Въпреки това, не присъждаме награди за небрежна работа. Ако две висококачествени предложения бъдат направени близо едно до друго (в рамките на ден или два), може да решим да присъдим награди и на двете, по наше усмотрение, например 100%% за първото предложение и 50%% за второто предложение (така общо 150%%). За по-големите награди (особено за наградите за скрапинг), моля свържете се с нас, когато сте завършили ~5%% от нея и сте уверени, че вашият метод ще се мащабира до пълния етап. Ще трябва да споделите метода си с нас, за да можем да дадем обратна връзка. Също така, по този начин можем да решим какво да правим, ако има няколко души, които се доближават до наградата, като например потенциално да я присъдим на няколко души, да насърчим хората да се обединят и т.н. ПРЕДУПРЕЖДЕНИЕ: задачите с висока награда са <span %(bold)s>трудни</span> — може би е разумно да започнете с по-лесни. Отидете на нашия <a %(a_gitlab)s>списък с проблеми в Gitlab</a> и сортирайте по “Приоритет на етикета”. Това показва приблизителния ред на задачите, които ни интересуват. Задачите без изрични награди все още са допустими за членство, особено тези, маркирани като “Приети” и “Любими на Анна”. Може да искате да започнете с “Начален проект”. Леко доброволчество Сега имаме и синхронизиран канал в Matrix на %(matrix)s. Ако имате няколко свободни часа, можете да помогнете по различни начини. Не забравяйте да се присъедините към <a %(a_telegram)s>чата на доброволците в Telegram</a>. Като знак на признателност, обикновено даваме 6 месеца “Късметлийски библиотекар” за основни постижения и повече за продължителна доброволческа работа. Всички постижения изискват висококачествена работа — небрежната работа ни вреди повече, отколкото ни помага, и ще я отхвърлим. Моля, <a %(a_contact)s>пишете ни по имейл</a>, когато достигнете някакъв етап. %(links)s връзки или екранни снимки на заявки, които сте изпълнили. Изпълнение на заявки за книги (или статии и т.н.) във форумите на Z-Library или Library Genesis. Нямаме собствена система за заявки на книги, но отразяваме тези библиотеки, така че подобряването им прави и Архивът на Анна по-добър. Етап Задача Зависи от задачата. Малки задачи, публикувани в нашия <a %(a_telegram)s>чат на доброволците в Telegram</a>. Обикновено за членство, понякога за малки награди. Малки задачи, публикувани в нашата група на чата за доброволците. Уверете се, че оставяте коментар за проблемите, които решавате, за да не дублират другите вашата работа. %(links)s връзки на записи, които сте подобрили. Можете да използвате <a %(a_list)s>списъка със случайни проблеми с metadata</a> като отправна точка. Подобряване на метаданни чрез <a %(a_metadata)s>свързване</a> с Open Library. Тези трябва да показват как уведомявате някого за Архивът на Анна и как той ви благодари. %(links)s връзки или екранни снимки. Разпространяване на информация за Архивът на Анна. Например, чрез препоръчване на книги в AA, свързване към нашите блог публикации или общо насочване на хората към нашия сайт. Пълен превод на език (ако не е бил близо до завършване вече.) <a %(a_translate)s>Превод</a> на сайта. Връзка към историята на редакциите, показваща, че сте направили значителни приноси. Подобряване на страницата в Wikipedia за Архивът на Анна на вашия език. Включете информация от страницата на AA в Wikipedia на други езици и от нашия сайт и блог. Добавете препратки към AA на други подобни страници. Доброволчество & награди 