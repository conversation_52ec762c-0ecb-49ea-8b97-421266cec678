msgid "layout.index.invalid_request"
msgstr "Невалидна заявка. Посетете %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub - сайт, който предоставя безплатен достъп до милиони научни статии"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen - позволява безплатен достъп до съдържание, което е платено"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib - сайт за електронни книги"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib - е библиотечен каталог, изграждащ страница за всяка книга, публикувана някога"

msgid "layout.index.header.tagline_ia"
msgstr "Интернет архивна библиотека за заемане"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu - е търсачка, специализирана за китайски академични материали"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " и "

msgid "layout.index.header.tagline_and_more"
msgstr "и още"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Ние сме огледало на %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Ние събираме и публикуваме безплатно %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Целият ни код и данни са с напълно отворени."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Най-голямата в историята отворена библиотека."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;книги, %(paper_count)s&nbsp;документи — запазени завинаги."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Най-голямата в света библиотека с отворени данни с отворен код. ⭐️&nbsp;Огледалата Sci-Hub, Library Genesis, Z-Library, и още други. 📈&nbsp;%(book_any)s книги, %(journal_article)s документи, %(book_comic)s комикси, %(magazine)s списания — запазени завинаги."

msgid "layout.index.header.tagline_short"
msgstr "📚 Най-голямата в света библиотека с данни с отворен код.<br>⭐️ Огледалата Scihub, Libgen, Zlib, и още."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Неправилни метаданни (напр. заглавие, описание, изображение на корицата)"

msgid "common.md5_report_type_mapping.download"
msgstr "Проблеми с изтеглянето (напр. невъзможност за свързване, съобщение за грешка, много бавно)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Файлът не може да бъде отворен (напр. повреден файл, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Лошо качество (напр. проблеми с форматирането, лошо качество на сканиране, липсващи страници)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Спам / файлът трябва да бъде премахнат (напр. реклама, обидно съдържание)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Иск за авторски права"

msgid "common.md5_report_type_mapping.other"
msgstr "Други"

msgid "common.membership.tier_name.bonus"
msgstr "Бонус изтегляния"

msgid "common.membership.tier_name.2"
msgstr "Брилянтен книжен червей"

msgid "common.membership.tier_name.3"
msgstr "Щастлив библиотекар"

msgid "common.membership.tier_name.4"
msgstr "Ослепителен събирач на данни"

msgid "common.membership.tier_name.5"
msgstr "Невероятен архивист"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s общо"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) общо"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s бонус)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "неплатен"

msgid "common.donation.order_processing_status_labels.1"
msgstr "платен"

msgid "common.donation.order_processing_status_labels.2"
msgstr "отменен"

msgid "common.donation.order_processing_status_labels.3"
msgstr "просрочен"

msgid "common.donation.order_processing_status_labels.4"
msgstr "чакам Анна да потвърди"

msgid "common.donation.order_processing_status_labels.5"
msgstr "невалиден"

msgid "page.donate.title"
msgstr "Дарение"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Имате <a %(a_donation)s>съществуващо дарение</a> в ход. Завършете или отменете това дарение, преди да направите ново дарение."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Преглед на всички мои дарения</a>"

msgid "page.donate.header.text1"
msgstr "Anna’s Archive е проект с нестопанска цел с отворен код и отворени данни. Като дарите и станете член, вие подкрепяте нашите действия и развитие. До всички наши членове: благодарим ви, че ни поддържате! ❤️"

msgid "page.donate.header.text2"
msgstr "За повече информация, вижте <a %(a_donate)s>Често задавани въпроси за дарение</a>."

msgid "page.donate.refer.text1"
msgstr "За да получите още повече изтегляния, <a %(a_refer)s>препоръчайте ни на приятелите си</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Получавате %(percentage)s%% бонус от бързи изтегляния, защото сте препоръчан от потребителя %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Това важи за целия период на членство."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s бързи изтегляния на ден"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "ако дарите този месец!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / месец"

msgid "page.donate.buttons.join"
msgstr "Присъединете се"

msgid "page.donate.buttons.selected"
msgstr "Избрано"

msgid "page.donate.buttons.up_to_discounts"
msgstr "до %(percentage)s%% отстъпки"

msgid "page.donate.perks.scidb"
msgstr "SciDB документи <strong>неограничени</strong> без проверка"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> достъп"

msgid "page.donate.perks.refer"
msgstr "Спечелете <strong>%(percentage)s%% бонус изтегляния</strong> като <a %(a_refer)s>поканите приятели</a>."

msgid "page.donate.perks.credits"
msgstr "Вашето потребителско име или анонимно споменаване в списъка на заслужилите"

msgid "page.donate.perks.previous_plus"
msgstr "Предишни предимства плюс:"

msgid "page.donate.perks.early_access"
msgstr "Ранен достъп до нови функции"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Специален Телеграм с обновяване на заден фон"

msgid "page.donate.perks.adopt"
msgstr "“Приемете торент“: вашето потребителско име или съобщение в име на торент файл <div %(div_months)s>веднъж на всеки 12 месеца членство</div>"

msgid "page.donate.perks.legendary"
msgstr "Легендарен статус в опазването на знанията и културата на човечеството"

msgid "page.donate.expert.title"
msgstr "Експертен достъп"

msgid "page.donate.expert.contact_us"
msgstr "свържете се с нас"

msgid "page.donate.small_team"
msgstr "Ние сме малък екип от доброволци. Може да ни отнеме 1-2 седмици, за да ви отговорим."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Неограничен</strong>високоскоростен достъп"

msgid "page.donate.expert.direct_sftp"
msgstr "Директни <strong>SFTP</strong> сървъри"

msgid "page.donate.expert.enterprise_donation"
msgstr "Дарение или обмен на корпоративно ниво за нови колекции (напр. нови сканирания, набори от данни с OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Приветстваме големи дарения от богатите хора или институции. "

msgid "page.donate.header.large_donations"
msgstr "За дарения над $5,000, моля, свържете се директно с нас на %(email)s."

msgid "page.donate.header.recurring"
msgstr "Имайте предвид, че докато членствата на тази страница са “на месец”, те са еднократни дарения (не се повтарят). Вижте <a %(faq)s>Често задавани въпроси за дарение</a>."

msgid "page.donate.without_membership"
msgstr "Ако искате да направите дарение (каквато и да е сума) без членство, не се колебайте да използвате този адрес на Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Моля, изберете начин на плащане."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(временно недостъпно)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s подаръчна карта"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Банкова карта (чрез приложение)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Крипто %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Кеш приложение"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Кредитна/дебитна карта"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (редовен)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Карта / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Кредитна/дебитна/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Алипей"

msgid "page.donate.payment.buttons.pix"
msgstr "нова система за незабавни плащания в Бразилия Pix"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Банкова карта"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Кредитна/дебитна карта (резервна)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Кредитна/дебитна карта 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Бинанс"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat приложение за съобщения и мобилни плащания"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay / WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "С крипто можете да дарявате, като използвате BTC, ETH, XMR и SOL. Използвайте тази опция, ако вече сте запознати с криптовалутата."

msgid "page.donate.payment.desc.crypto2"
msgstr "С крипто валута можете да дарявате, като използвате BTC, ETH, XMR и др."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Ако използвате криптовалута за първи път, препоръчваме да използвате %(options)s, за да купите и дарите Bitcoin (оригиналната и най-използвана криптовалута)."

msgid "page.donate.payment.processor.binance"
msgstr "Бинанс"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "За да даряваме чрез PayPal, ще използваме PayPal Crypto, което ни позволява да останем анонимни. Оценяваме, че отделихте време, за да научите как да дарявате чрез този метод, тъй като ни помага много."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Дарете чрез PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Дарете чрез Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Ако имате Cash App, това е най-лесният начин да дарите!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Имайте предвид, че за транзакции под %(amount)s, Cash App може да начисли такса %(fee)s. За %(amount)s или повече е безплатно!"

msgid "page.donate.payment.desc.revolut"
msgstr "Дарете чрез Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Ако имате Revolut, това е най-лесният начин да дарите!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Дарете с кредитна или дебитна карта."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay и Apple Pay може също да работят."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Имайте предвид, че за малки дарения таксите за кредитна карта може да премахнат нашата %(discount)s%% отстъпка, така че препоръчваме по-дългосрочни абонаменти."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Имайте предвид, че за малки дарения таксите са високи, затова препоръчваме по-дългосрочни абонаменти."

msgid "page.donate.payment.desc.binance_p1"
msgstr "С Binance можете да купите Bitcoin с кредитна/дебитна карта или банков акаунт и след това да дарите този Bitcoin на нас. По този начин можем да останем сигурни и анонимни при приемане на вашето дарение."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance е наличен в почти всяка страна и поддържа повечето банки и кредитни/дебитни карти. Това е нашата основна препоръка в момента. Оценяваме, че отделяте време да научите как да дарите, използвайки този метод, тъй като това ни помага много."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Дарете чрез вашия редовен PayPal акаунт."

msgid "page.donate.payment.desc.givebutter"
msgstr "Дарете с кредитна/дебитна карта, PayPal или Venmo. Можете да изберете между тях на следващата страница."

msgid "page.donate.payment.desc.amazon"
msgstr "Дарете с карта за подарък на Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Имайте предвид, че трябва да закръглим до суми, приети от нашите дистрибутори (minimum %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>ВАЖНО:</strong> Ние поддържаме само Amazon.com, не другите уебсайтове на Amazon. Например .de, .co.uk, .ca НЕ се поддържат."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>ВАЖНО:</strong> Тази опция е за %(amazon)s. Ако искате да използвате друг сайт на Amazon, изберете го по-горе."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Този метод използва доставчика на криптовалута като междинно преобразуване. Това може да бъде малко объркващо, така че моля, използвайте този метод само ако другите методи на плащане не работят. Също така не работи във всички страни."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Дарете с кредитна/дебитна, чрез приложението Alipay (много лесно за настройка)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Инсталирайте приложението Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Инсталирайте приложението Alipay от <a %(a_app_store)s>Apple App Store</a> или <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Регистрирайте се с вашия телефонен номер."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Не се изискват допълнителни лични данни."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Добавете банкова карта"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Поддържани: Visa, MasterCard, JCB, Diners Club и Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Вижте <a %(a_alipay)s>това ръководство</a> за повече информация."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Не можем да поддържаме кредитни/дебитни карти директно, защото банките не искат да работят с нас. ☹ Въпреки това, има няколко начина да използвате кредитни/дебитни карти чрез други методи на плащане:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Карта за подарък"

msgid "page.donate.ccexp.amazon_com"
msgstr "Изпратете ни подаръчни карти на Amazon.com, като използвате вашата кредитна/дебитна карта."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay поддържа международни кредитни/дебитни карти. Вижте <a %(a_alipay)s>това ръководство</a> за повече информация."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) поддържа международни кредитни/дебитни карти. В приложението WeChat отидете на “Аз → Услуги → Портфейл → Добавяне на карта”. Ако не виждате това, активирайте го чрез “Аз → Настройки → Общи → Инструменти → Weixin Pay → Активиране”."

msgid "page.donate.ccexp.crypto"
msgstr "Можете да купувате крипто валута чрез кредитни/дебитни карти."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Крипто експресни услуги"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Експресните услуги са удобни, но начисляват по-високи такси."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Можете да използвате това вместо крипто борса, ако искате бързо да направите по-голямо дарение и не ви пречи такса от $5-10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Уверете се, че изпращате точната крипто сума, показана на страницата за дарение, а не сумата в $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "В противен случай таксата ще бъде извадена и не можем автоматично да обработим вашето членство."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(минимум: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(минимум: %(minimum)s в зависимост от страната, без верификация за първата трансакция)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(минимум: %(minimum)s, без верификация за първата трансакция)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(минимум: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(минимум: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(минимум: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Ако някоя от тази информация е остаряла, моля, изпратете ни имейл, за да ни уведомите."

msgid "page.donate.payment.desc.bmc"
msgstr "За кредитни карти, дебитни карти, Apple Pay и Google Pay използваме “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). В тяхната система, едно “кафе” е равно на $5, така че вашето дарение ще бъде закръглено до най-близкото кратно на 5."

msgid "page.donate.duration.intro"
msgstr "Изберете за колко време искате да се абонирате."

msgid "page.donate.duration.1_mo"
msgstr "1 месец"

msgid "page.donate.duration.3_mo"
msgstr "3 месеца"

msgid "page.donate.duration.6_mo"
msgstr "6 месеца"

msgid "page.donate.duration.12_mo"
msgstr "12 месеца"

msgid "page.donate.duration.24_mo"
msgstr "24 месеца"

msgid "page.donate.duration.48_mo"
msgstr "48 месеца"

msgid "page.donate.duration.96_mo"
msgstr "96 месеца"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>след <span %(span_discount)s></span> отстъпки</div><div %(div_total)s></div> <div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Този метод на плащане изисква минимум %(amount)s. Моля, изберете различна продължителност или начин на плащане."

msgid "page.donate.buttons.donate"
msgstr "Дарете"

msgid "page.donate.payment.maximum_method"
msgstr "Този метод на плащане позволява максимум %(amount)s. Моля, изберете различна продължителност или начин на плащане."

msgid "page.donate.login2"
msgstr "За да станете член, моля <a %(a_login)s>Влезте или се регистрирайте</a>. Благодаря за подкрепата!"

msgid "page.donate.payment.crypto_select"
msgstr "Изберете предпочитаната от вас крипто монета:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(най-ниската минимална сума)"

msgid "page.donate.coinbase_eth"
msgstr "(използвайте при изпращане на Ethereum от Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(предупреждение: висока минимална сума)"

msgid "page.donate.submit.confirm"
msgstr "Щракнете върху бутона за дарение, за да потвърдите това дарение."

msgid "page.donate.submit.button"
msgstr "Дарете <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Все още можете да анулирате дарението по време на плащане."

msgid "page.donate.submit.success"
msgstr "✅ Пренасочване към страницата за дарения…"

msgid "page.donate.submit.failure"
msgstr "❌ Нещо се обърка. Презаредете страницата и опитайте отново."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / месец"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "за 1 месец"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "за 3 месеца"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "за 6 месеца"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "за 12 месеца"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "за 24 месеца"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "за 48 месеца"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "за 96 месеца"

msgid "page.donate.submit.button.label.1_mo"
msgstr "за 1 месец “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "за 3 месеца “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "за 6 месеца “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "за 12 месеца “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "за 24 месеца “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "за 48 месеца “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "за 96 месеца “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Дарение"

msgid "page.donation.header.date"
msgstr "Дата: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Общо: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месец за %(duration)s месеца, включително %(discounts)s%% отстъпка)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Общо: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / месец за %(duration)s месеци)</span>"

msgid "page.donation.header.status"
msgstr "Статус: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Идентификатор: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Отказ"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Сигурни ли сте, че искате да се откажете? Не се отказвайте, ако вече сте платили."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Да, моля отменете"

msgid "page.donation.header.cancel.success"
msgstr "✅Вашето дарение е отменено."

msgid "page.donation.header.cancel.new_donation"
msgstr "Направете ново дарение"

msgid "page.donation.header.cancel.failure"
msgstr "❌Нещо се обърка. Презаредете страницата и опитайте отново."

msgid "page.donation.header.reorder"
msgstr "Пренареждане"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Вие вече сте платили. Ако все пак искате да прегледате инструкциите за плащане, щракнете тук:"

msgid "page.donation.old_instructions.show_button"
msgstr "Показване на стари инструкции за плащане"

msgid "page.donation.thank_you_donation"
msgstr "Благодарим ви за вашето дарение!"

msgid "page.donation.thank_you.secret_key"
msgstr "Ако още не сте го направили, запишете своя секретен ключ за влизане:"

msgid "page.donation.thank_you.locked_out"
msgstr "В противен случай може да загубите достъп до този акаунт!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Инструкциите за плащане вече са остарели. Ако желаете да направите друго дарение, използвайте бутона “Повторна поръчка” по-горе."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Важна забележка:</strong> Цените на криптовалутите могат да варират силно, понякога дори до 20%% за няколко минути. Това все още е по-малко от таксите, които плащаме с много доставчици на плащания, които често таксуват 50-60%% за работа с “благотворителна организация в сянка” като нас. <u>Ако ни изпратите разписката с първоначалната цена, която сте платили, ние пак ще кредитираме акаунта ви за избраното членство</u> (стига разписката да не е по-стара от няколко часа). Наистина оценяваме, че сте готови да се примирите с подобни неща, за да ни подкрепите!❤️"

msgid "page.donation.expired"
msgstr "Това дарение е изтекло. Моля, отменете и създайте ново."

msgid "page.donation.payment.crypto.top_header"
msgstr "Крипто инструкции"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Прехвърлете към една от нашите крипто сметки"

msgid "page.donation.payment.crypto.text1"
msgstr "Дарете цялата сума на %(total)s до един от тези адреси:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Купете биткойн на Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Намерете страницата “Крипто” във вашето приложение или уебсайт на PayPal. Това обикновено е под “Финанси”."

msgid "page.donation.payment.paypal.text3"
msgstr "Следвайте инструкциите, за да закупите биткойн (BTC). Трябва само да купите сумата, която искате да дарите, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Прехвърлете Bitcoin на нашия адрес"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Отидете на страницата “Биткойн” във вашето приложение или уебсайт на PayPal. Натиснете бутона “Прехвърляне” %(transfer_icon)s и след това “Изпращане”."

msgid "page.donation.payment.paypal.text5"
msgstr "Въведете нашия биткойн (BTC) адрес като получател и следвайте инструкциите, за да изпратите вашето дарение от %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Инструкции за кредитна/дебитна карта"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Дарете чрез нашата страница с кредитни/дебитни карти"

msgid "page.donation.donate_on_this_page"
msgstr "Дарете %(amount)s на <a %(a_page)s>тази работа</a>."

msgid "page.donation.stepbystep_below"
msgstr "Вижте ръководството стъпка по стъпка по-долу."

msgid "page.donation.status_header"
msgstr "Статус:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Изчаква се потвърждение (опреснете страницата, за да проверите)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Изчаква се прехвърляне (опреснете страницата, за да проверите)…"

msgid "page.donation.time_left_header"
msgstr "Оставащо време:"

msgid "page.donation.might_want_to_cancel"
msgstr "(може да искате да отмените и да създадете ново дарение)"

msgid "page.donation.reset_timer"
msgstr "За да нулирате таймера, просто създайте ново дарение."

msgid "page.donation.refresh_status"
msgstr "Актуализиране на състоянието"

msgid "page.donation.footer.issues_contact"
msgstr "Ако срещнете някакви проблеми, моля, свържете се с нас на %(email)s и включете възможно най-много информация (като екранни снимки)."

msgid "page.donation.expired_already_paid"
msgstr "Ако вече сте платили:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Понякога потвърждението може да отнеме до 24 часа, затова не забравяйте да обновите тази страница (дори ако е изтекла)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Купете монета PYUSD на PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Следвайте инструкциите, за да купите монета PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за транзакция. Ще запазите всичко останало."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Отидете на страницата “PYUSD” във вашето приложение или уебсайт на PayPal. Натиснете бутона “Прехвърляне” %(icon)s и след това “Изпращане”."

msgid "page.donation.transfer_amount_to"
msgstr "Прехвърлете %(amount)s към %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Купете Bitcoin (BTC) в Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Отидете на страницата “Bitcoin” (BTC) в Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за трансакции. Ще запазите всичко, което остане."

msgid "page.donation.cash_app_btc.step2"
msgstr "Прехвърлете Bitcoin на нашия адрес"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Кликнете върху бутона “Изпрати биткойн”, за да направите “теглене”. Превключете от долари на BTC, като натиснете иконата %(icon)s. Въведете сумата в BTC по-долу и кликнете “Изпрати”. Вижте <a %(help_video)s>това видео</a>, ако се затрудните."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "За малки дарения (под $25), може да се наложи да използвате Rush или Priority."

msgid "page.donation.revolut.step1"
msgstr "Купете Bitcoin (BTC) в Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Отидете на страницата “Crypto” в Revolut, за да купите Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Купете малко повече (препоръчваме %(more)s повече) от сумата, която дарявате (%(amount)s), за да покриете таксите за трансакции. Ще запазите всичко, което остане."

msgid "page.donation.revolut.step2"
msgstr "Прехвърлете Биткойн на нашия адрес"

msgid "page.donation.revolut.step2.transfer"
msgstr "Кликнете върху бутона “Изпрати биткойн”, за да направите “теглене”. Превключете от евро на BTC, като натиснете иконата %(icon)s. Въведете сумата в BTC по-долу и кликнете “Изпрати”. Вижте <a %(help_video)s>това видео</a>, ако се затрудните."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Уверете се, че използвате сумата в BTC по-долу, <em>НЕ</em> в евро или долари, в противен случай няма да получим правилната сума и не можем автоматично да потвърдим членството ви."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "За малки дарения (под $25) може да се наложи да използвате Rush или Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Използвайте някоя от следните експресни услуги “кредитна карта към Bitcoin”, които отнемат само няколко минути:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Попълнете следните данни във формуляра:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin сума:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Моля, използвайте този <span %(underline)s>точен размер</span>. Общата ви цена може да бъде по-висока заради таксите за кредитни карти. За малки суми това може да е повече от нашата отстъпка, за съжаление."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin адрес (външен портфейл):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s инструкции"

msgid "page.donation.crypto_standard"
msgstr "Поддържаме само стандартната версия на крипто монети, без екзотични мрежи или версии на монети. Потвърждаването на транзакцията може да отнеме до един час, в зависимост от монетата."

msgid "page.donation.crypto_qr_code_title"
msgstr "Сканирайте QR код, който да платите"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Сканирайте този QR код с вашето приложение Crypto Wallet, за да попълните бързо данните за плащане"

msgid "page.donation.amazon.header"
msgstr "Карта за подарък на Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Моля, използвайте <a %(a_form)s>официалния формуляр на Amazon.com</a>, за да ни изпратите карта за подарък от %(amount)s на имейл адреса по-долу."

msgid "page.donation.amazon.only_official"
msgstr "Не можем да приемем други методи за подаръчни карти, <strong>само изпратени директно от официалния формуляр на Amazon.com</strong>. Не можем да върнем вашата подаръчна карта, ако не използвате този формуляр."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Въведете точната сума: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Моля, НЕ пишете лично съобщение."

msgid "page.donation.amazon.form_to"
msgstr "Имейл на получателя “До” във формата:"

msgid "page.donation.amazon.unique"
msgstr "Уникален за вашия акаунт, не споделяйте."

msgid "page.donation.amazon.only_use_once"
msgstr "Използвайте само веднъж."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Изчакване на карта за подарък... (опреснете страницата, за да проверите)"

msgid "page.donation.amazon.confirm_automated"
msgstr "След като изпратите вашата карта за подарък, нашата автоматизирана система ще я потвърди в рамките на няколко минути. Ако това не работи, опитайте да изпратите отново вашата карта за подарък (<a %(a_instr)s>инструкции</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Ако това все още не работи, моля, изпратете ни имейл и Анна ще го прегледа ръчно (това може да отнеме няколко дни) и не забравяйте да споменете дали вече сте опитали да изпратите отново."

msgid "page.donation.amazon.example"
msgstr "Пример:"

msgid "page.donate.strange_account"
msgstr "Имайте предвид, че името или снимката на акаунта може да изглеждат странно. Няма нужда да се тревожите! Тези акаунти се управляват от нашите партньори за дарения. Нашите акаунти не са били хакнати."

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay инструкции"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Дарете чрез Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Дарете общата сума от %(total)s чрез <a %(a_account)s>този Alipay акаунт</a>"

msgid "page.donation.page_blocked"
msgstr "Ако страницата за дарения е блокирана, опитайте с различна интернет връзка (например VPN или интернет от телефон)."

msgid "page.donation.payment.alipay.error"
msgstr "За съжаление, страницата на Alipay често е достъпна само от <strong>континентален Китай</strong>. Може да се наложи временно да деактивирате вашия VPN или да използвате VPN към континентален Китай (или Хонконг също понякога работи)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Направете дарение (сканирайте QR кода или натиснете бутона)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Отворете <a %(a_href)s>страницата за дарение с QR код</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Сканирайте QR кода с приложението Alipay или натиснете бутона, за да отворите приложението Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Моля, бъдете търпеливи; страницата може да се зареди по-бавно, тъй като е в Китай."

msgid "page.donation.payment.wechat.top_header"
msgstr "Инструкции за WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Дарете чрез WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Дарете общата сума от %(total)s чрез <a %(a_account)s>този WeChat акаунт</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix инструкции"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Дарете чрез Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Дарете общата сума от %(total)s, като използвате <a %(a_account)s>този Pix акаунт"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Изпратете ни разписката по имейл"

msgid "page.donation.footer.verification"
msgstr "Изпратете разписка или екранна снимка на вашия адрес за верификация. НЕ използвайте този имейл за вашето дарение чрез PayPal."

msgid "page.donation.footer.text1"
msgstr "Изпратете разписка или екранна снимка на вашия адрес за потвърждение:"

msgid "page.donation.footer.crypto_note"
msgstr "Ако обменният курс на криптовалутата е варирал по време на транзакцията, не забравяйте да включите разписката, показваща първоначалния обменен курс. Наистина оценяваме, че си направихте труда да използвате крипто, това ни помага много!"

msgid "page.donation.footer.text2"
msgstr "Когато изпратите разписката си по имейл, щракнете върху този бутон, за да може Анна да я прегледа ръчно (това може да отнеме няколко дни):"

msgid "page.donation.footer.button"
msgstr "Да, изпратих разписката си по имейл"

msgid "page.donation.footer.success"
msgstr "✅ Благодаря за вашето дарение! Анна ръчно ще активира вашето членство в рамките на няколко дни."

msgid "page.donation.footer.failure"
msgstr "❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново."

msgid "page.donation.stepbystep"
msgstr "Ръководство стъпка по стъпка"

msgid "page.donation.crypto_dont_worry"
msgstr "Някои от стъпките споменават крипто портфейли, но не се притеснявайте, не е нужно да научавате нищо за това крипто."

msgid "page.donation.hoodpay.step1"
msgstr "1. Въведете вашия имейл."

msgid "page.donation.hoodpay.step2"
msgstr "2. Изберете вашия начин на плащане."

msgid "page.donation.hoodpay.step3"
msgstr "3. Изберете вашия начин на плащане отново."

msgid "page.donation.hoodpay.step4"
msgstr "4. Изберете портфейла “Лично хостван”."

msgid "page.donation.hoodpay.step5"
msgstr "5. Щракнете върху “Потвърждавам собствеността”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Трябва да получите разписката по имейл. Моля, изпратете ни я и ние ще потвърдим вашето дарение възможно най-скоро."

msgid "page.donate.wait_new"
msgstr "Моля, изчакайте поне <span %(span_hours)s>24 часа</span> (и обновете тази страница), преди да се свържете с нас."

msgid "page.donate.mistake"
msgstr "Ако сте допуснали грешка по време на плащането, не можем да възстановим сумата, но ще се опитаме да се реваншираме."

msgid "page.my_donations.title"
msgstr "Моите дарения"

msgid "page.my_donations.not_shown"
msgstr "Подробностите за даренията не се показват публично."

msgid "page.my_donations.no_donations"
msgstr "Все още няма дарения. <a %(a_donate)s>Направи първото ми дарение.</a>"

msgid "page.my_donations.make_another"
msgstr "Направи друго дарение."

msgid "page.downloaded.title"
msgstr "Изтеглени файлове"

msgid "page.downloaded.fast_partner_star"
msgstr "Изтеглянията от Fast Partner Servers са маркирани с %(icon)s."

msgid "page.downloaded.twice"
msgstr "Ако сте изтеглили файла с бързо и бавно изтегляне, той ще се появи два пъти."

msgid "page.downloaded.fast_download_time"
msgstr "Бързите изтегляния през последните 24 часа са част от дневния лимит."

msgid "page.downloaded.times_utc"
msgstr "Всички времена са в UTC."

msgid "page.downloaded.not_public"
msgstr "Изтеглените файлове не се показват публично."

msgid "page.downloaded.no_files"
msgstr "Все още няма изтеглени файлове."

msgid "page.downloaded.last_18_hours"
msgstr "Последните 18 часа"

msgid "page.downloaded.earlier"
msgstr "По-рано"

msgid "page.account.logged_in.title"
msgstr "Акаунт"

msgid "page.account.logged_out.title"
msgstr "Вход / Регистриране"

msgid "page.account.logged_in.account_id"
msgstr "ID на акаунт: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Публичен профил: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Таен ключ (don’t share!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "показване"

msgid "page.account.logged_in.membership_has_some"
msgstr "Членство: <strong>%(tier_name)s</strong> до %(until_date)s <a %(a_extend)s>(удължаване)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Членство: <strong>Няма</strong> <a %(a_become)s>(станете член)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Използвани са бързи изтегляния (последните 24 часа): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "кои изтегляния?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Изключителна група в Telegram: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Присъединете се към нас тук!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Надстройте до <a %(a_tier)s>по-високо ниво</a>, за да се присъедините към нашата група."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Свържете се с Anna на %(email)s, ако се интересувате от надграждане на членството си до по-високо ниво."

msgid "page.contact.title"
msgstr "Имейл за връзка"

msgid "page.account.logged_in.membership_multiple"
msgstr "Можете да комбинирате няколко членства (бързите изтегляния за 24 часа ще бъдат добавени заедно)."

msgid "layout.index.header.nav.public_profile"
msgstr "Публичен профил"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Изтеглени файлове"

msgid "layout.index.header.nav.my_donations"
msgstr "Моите дарения"

msgid "page.account.logged_in.logout.button"
msgstr "Излизане"

msgid "page.account.logged_in.logout.success"
msgstr "✅Вече сте излезли. Презаредете страницата, за да влезете отново."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново."

msgid "page.account.logged_out.registered.text1"
msgstr "Успешна регистрация! Тайният Ви ключ е <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Внимателно запаметете този ключ. Ако го изгубите, ще загубите достъп до профила си."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Отметка.</strong> Може да добавите отметка за тази препратка, за да намерите ключа си по-лесно.</li><li %(li_item)s><strong>Изтегли.</strong> Натиснете <a %(a_download)s>тази препратка</a>, за да изтеглите ключа си.</li><li %(li_item)s><strong>Управление на пароли.</strong> Използвайте софтуер за управление на пароли, за да запазите ключа, когато го въведете долу.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Въведете тайния си ключ, за да влезете в системата:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Таен ключ"

msgid "page.account.logged_out.key_form.button"
msgstr "Вход"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Невалиден таен ключ. Проверете ключа си и опитайте отново или регистрирайте нов акаунт по-долу."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Не губете ключа си!"

msgid "page.account.logged_out.register.header"
msgstr "Още нямате акаунт?"

msgid "page.account.logged_out.register.button"
msgstr "Регистрирайте нов акаунт"

msgid "page.login.lost_key"
msgstr "Ако сте загубили ключа си, моля, <a %(a_contact)s>свържете се с нас</a> и предоставете възможно най-много информация."

msgid "page.login.lost_key_contact"
msgstr "Може да се наложи временно да създадете нов профил, за да се свържете с нас."

msgid "page.account.logged_out.old_email.button"
msgstr "Имате стар акаунт, базиран на имейл? Въведете <a %(a_open)s>имейла си тук</a>."

msgid "page.list.title"
msgstr "Списък"

msgid "page.list.header.edit.link"
msgstr "редактиране"

msgid "page.list.edit.button"
msgstr "Запази"

msgid "page.list.edit.success"
msgstr "✅ Запазено. Моля опреснете страницата."

msgid "page.list.edit.failure"
msgstr "❌ Грешка. Моля, опитайте отново."

msgid "page.list.by_and_date"
msgstr "Списък от %(by)s създаден на <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Списъкът е празен."

msgid "page.list.new_item"
msgstr "Добавете или премахнете нещо от този списък, като намерите файл и отворите раздела „Списъци“."

msgid "page.profile.title"
msgstr "Профил"

msgid "page.profile.not_found"
msgstr "Профилът не е намерен."

msgid "page.profile.header.edit"
msgstr "редактиране"

msgid "page.profile.change_display_name.text"
msgstr "Променете името си. Идентификаторът Ви (частта след \"#\") не може да бъде променян."

msgid "page.profile.change_display_name.button"
msgstr "Запази"

msgid "page.profile.change_display_name.success"
msgstr "✅ Запазено. Моля, опреснете страницата."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Грешка. Моля, опитайте отново."

msgid "page.profile.created_time"
msgstr "Профилът е създаден на <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Списъци"

msgid "page.profile.lists.no_lists"
msgstr "Все още няма списъци"

msgid "page.profile.lists.new_list"
msgstr "Създайте нов списък, като намерите файл и отворите раздела \"Списъци\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Реформата на авторското право е необходима за националната сигурност."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Накратко: Китайските LLM (включително DeepSeek) са обучени на моя незаконен архив от книги и статии — най-големият в света. Западът трябва да преразгледа законите за авторското право като въпрос на национална сигурност."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "съпътстващи статии от TorrentFreak: <a %(torrentfreak)s>първа</a>, <a %(torrentfreak_2)s>втора</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Неотдавна „сенчестите библиотеки“ бяха на изчезване. Sci-Hub, огромният незаконен архив на академични статии, беше спрял да приема нови произведения поради съдебни дела. „Z-Library“, най-голямата незаконна библиотека с книги, видя как предполагаемите ѝ създатели бяха арестувани по обвинения за нарушаване на авторското право. Те невероятно успяха да избягат от ареста, но библиотеката им не е по-малко застрашена."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Когато Z-Library беше изправена пред закриване, аз вече бях направил резервно копие на цялата ѝ библиотека и търсех платформа, на която да я съхраня. Това беше моята мотивация да започна Архива на Анна: продължение на мисията зад тези по-ранни инициативи. Оттогава сме се разраснали до най-голямата сенчеста библиотека в света, съхраняваща над 140 милиона защитени с авторско право текстове в различни формати — книги, академични статии, списания, вестници и други."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Моят екип и аз сме идеолози. Вярваме, че съхраняването и хостването на тези файлове е морално правилно. Библиотеките по света са изправени пред съкращения на финансирането и не можем да поверим наследството на човечеството на корпорации."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "След това дойде AI. Почти всички големи компании, изграждащи LLM, се свързаха с нас, за да обучават на нашите данни. Повечето (но не всички!) компании от САЩ преосмислиха, след като осъзнаха незаконната природа на нашата работа. За разлика от тях, китайските фирми с ентусиазъм приеха нашата колекция, очевидно необезпокоени от нейната законност. Това е забележително, като се има предвид ролята на Китай като подписал почти всички основни международни договори за авторско право."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Дадохме високоскоростен достъп на около 30 компании. Повечето от тях са LLM компании, а някои са брокери на данни, които ще препродават нашата колекция. Повечето са китайски, въпреки че сме работили и с компании от САЩ, Европа, Русия, Южна Корея и Япония. DeepSeek <a %(arxiv)s>призна</a>, че по-ранна версия е била обучена на част от нашата колекция, въпреки че са мълчаливи за последния си модел (вероятно също обучен на нашите данни)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Ако Западът иска да остане напред в надпреварата на LLM и в крайна сметка AGI, трябва да преразгледа позицията си относно авторското право и то скоро. Независимо дали сте съгласни с нас или не по нашия морален случай, това вече се превръща в икономически въпрос и дори въпрос на национална сигурност. Всички силови блокове изграждат изкуствени супер-учени, супер-хакери и супер-армии. Свободата на информацията се превръща в въпрос на оцеляване за тези страни — дори въпрос на национална сигурност."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Нашият екип е от цял свят и нямаме конкретно подравняване. Но бихме насърчили страните със силни закони за авторското право да използват тази екзистенциална заплаха, за да ги реформират. И така, какво да се направи?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Нашата първа препоръка е проста: съкратете срока на авторското право. В САЩ авторското право се предоставя за 70 години след смъртта на автора. Това е абсурдно. Можем да го приведем в съответствие с патентите, които се предоставят за 20 години след подаване. Това трябва да е повече от достатъчно време за авторите на книги, статии, музика, изкуство и други творчески произведения, за да бъдат напълно компенсирани за усилията си (включително дългосрочни проекти като филмови адаптации)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "След това, най-малкото, политиците трябва да включат изключения за масово съхранение и разпространение на текстове. Ако загубените приходи от индивидуални клиенти са основната грижа, разпространението на лично ниво може да остане забранено. В замяна тези, които са способни да управляват огромни хранилища — компании, обучаващи LLM, заедно с библиотеки и други архиви — ще бъдат обхванати от тези изключения."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Някои страни вече правят версия на това. TorrentFreak <a %(torrentfreak)s>съобщи</a>, че Китай и Япония са въвели AI изключения в своите закони за авторското право. Не е ясно за нас как това взаимодейства с международните договори, но със сигурност дава покритие на техните местни компании, което обяснява това, което сме виждали."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Що се отнася до Архива на Анна — ще продължим нашата подземна работа, вкоренена в морално убеждение. Но най-голямото ни желание е да излезем на светло и да увеличим въздействието си законно. Моля, реформирайте авторското право."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Прочетете съпътстващите статии от TorrentFreak: <a %(torrentfreak)s>първа</a>, <a %(torrentfreak_2)s>втора</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Победители на наградата за визуализация на ISBN на стойност $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Накратко: Получихме невероятни предложения за наградата за визуализация на ISBN на стойност $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Преди няколко месеца обявихме <a %(all_isbns)s>награда от $10,000</a> за създаване на най-добрата възможна визуализация на нашите данни, показваща пространството на ISBN. Подчертяхме показването на кои файлове вече сме архивирали и кои не, и по-късно добавихме набор от данни, описващ колко библиотеки притежават ISBN (мярка за рядкост)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Бяхме поразени от отговора. Имаше толкова много креативност. Голямо благодаря на всички, които участваха: вашата енергия и ентусиазъм са заразителни!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "В крайна сметка искахме да отговорим на следните въпроси: <strong>кои книги съществуват в света, колко от тях вече сме архивирали и върху кои книги трябва да се съсредоточим след това?</strong> Чудесно е да видим, че толкова много хора се интересуват от тези въпроси."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Започнахме с основна визуализация сами. В по-малко от 300kb, тази картина кратко представя най-големия напълно отворен „списък на книги“, създаван някога в историята на човечеството:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Всички ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Файлове в Архива на Анна"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Изтичане на данни от CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Индекс на електронни книги на EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Книги"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Интернет Архив"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Глобален регистър на издателите ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

msgid "blog.all-isbns-winners.opt.ol"
msgstr ""

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Руска държавна библиотека"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Имперска библиотека на Трантор"

#, fuzzy
msgid "common.back"
msgstr "Назад"

#, fuzzy
msgid "common.forward"
msgstr "Напред"

#, fuzzy
msgid "common.last"
msgstr "Последен"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Моля, вижте <a %(all_isbns)s>оригиналната публикация в блога</a> за повече информация."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Издадохме предизвикателство за подобрение на това. Щяхме да присъдим награда за първо място от $6,000, за второ място от $3,000 и за трето място от $1,000. Поради огромния отговор и невероятните предложения, решихме да увеличим малко наградния фонд и да присъдим четири трети места по $500 всяко. Победителите са по-долу, но не забравяйте да разгледате всички предложения <a %(annas_archive)s>тук</a> или да изтеглите нашия <a %(a_2025_01_isbn_visualization_files)s>комбиниран торент</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Първо място $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Това <a %(phiresky_github)s>предложение</a> (<a %(annas_archive_note_2951)s>коментар в Gitlab</a>) е просто всичко, което искахме, и още! Особено ни харесаха невероятно гъвкавите опции за визуализация (дори поддържащи персонализирани шейдъри), но с изчерпателен списък от предварителни настройки. Също така ни хареса колко бързо и гладко е всичко, простата реализация (която дори няма бекенд), умната миникарта и обширното обяснение в техния <a %(phiresky_github)s>блог пост</a>. Невероятна работа и заслужен победител!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Второ място $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Още едно невероятно <a %(annas_archive_note_2913)s>предложение</a>. Не толкова гъвкаво като първото място, но всъщност предпочетохме неговата макро-ниво визуализация пред първото място (крива за запълване на пространство, граници, етикетиране, подчертаване, панорама и мащабиране). Един <a %(annas_archive_note_2971)s>коментар</a> от Джо Дейвис ни впечатли:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "„Докато перфектните квадрати и правоъгълници са математически приятни, те не осигуряват превъзходна локалност в контекста на картографирането. Вярвам, че асиметрията, присъща на тези криви на Хилберт или класическия Мортън, не е недостатък, а характеристика. Точно както известният ботушовиден контур на Италия я прави незабавно разпознаваема на карта, уникалните „особености“ на тези криви могат да служат като когнитивни ориентири. Тази отличителност може да подобри пространствената памет и да помогне на потребителите да се ориентират, потенциално улеснявайки намирането на специфични региони или забелязването на модели.“"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "И все още много опции за визуализация и рендиране, както и невероятно гладък и интуитивен потребителски интерфейс. Солидно второ място!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Трето място $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "В това <a %(annas_archive_note_2940)s>предложение</a> наистина ни харесаха различните видове изгледи, по-специално изгледите за сравнение и издател."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Трето място $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Въпреки че не е най-полираната потребителска интерфейс, това <a %(annas_archive_note_2917)s>предложение</a> отговаря на много от изискванията. Особено ни хареса функцията му за сравнение."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Трето място $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Както и първото място, това <a %(annas_archive_note_2975)s>предложение</a> ни впечатли с гъвкавостта си. В крайна сметка това е, което прави един инструмент за визуализация страхотен: максимална гъвкавост за напреднали потребители, като същевременно се запазва простотата за обикновените потребители."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Трето място $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Последното <a %(annas_archive_note_2947)s>предложение</a>, което получава награда, е доста основно, но има някои уникални функции, които наистина ни харесаха. Хареса ни как показват колко Datasets покриват определен ISBN като мярка за популярност/надеждност. Също така много ни хареса простотата, но ефективността на използването на плъзгач за непрозрачност за сравнения."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Забележителни идеи"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Още някои идеи и реализации, които особено ни харесаха:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Небостъргачи за рядкост"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Живи статистики"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Анотации и също живи статистики"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Уникален изглед на карта и филтри"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Готина стандартна цветова схема и топлинна карта."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Лесно превключване на Datasets за бързи сравнения."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Красиви етикети."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Скала с брой книги."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Много плъзгачи за сравнение на Datasets, сякаш сте DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Можем да продължим още дълго, но нека спрем тук. Не забравяйте да разгледате всички предложения <a %(annas_archive)s>тук</a>, или изтеглете нашия <a %(a_2025_01_isbn_visualization_files)s>комбиниран торент</a>. Толкова много предложения, и всяко носи уникална перспектива, било то в UI или изпълнение."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Поне ще включим предложението на първо място в нашия основен уебсайт, а може би и някои други. Също така започнахме да мислим как да организираме процеса на идентифициране, потвърждаване и след това архивиране на най-редките книги. Още новини предстоят."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Благодарим на всички, които участваха. Удивително е, че толкова много хора се грижат."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Сърцата ни са пълни с благодарност."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Визуализиране на всички ISBN — награда от $10,000 до 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Тази картина представлява най-големия напълно отворен „списък с книги“, създаван някога в историята на човечеството."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Тази картина е 1000×800 пиксела. Всеки пиксел представлява 2,500 ISBN. Ако имаме файл за ISBN, правим този пиксел по-зелен. Ако знаем, че ISBN е издаден, но нямаме съответстващ файл, правим го по-червен."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "В по-малко от 300kb, тази картина кратко представя най-големия напълно отворен „списък с книги“, създаван някога в историята на човечеството (няколко стотин GB компресирани напълно)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Тя също показва: има много работа, която остава за архивиране на книги (имаме само 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Предистория"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Как може Архивът на Анна да постигне своята мисия да архивира цялото знание на човечеството, без да знае кои книги все още съществуват? Нуждаем се от списък със задачи. Един от начините да картографираме това е чрез ISBN номера, които от 70-те години на миналия век се присвояват на всяка публикувана книга (в повечето страни)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Няма централна власт, която да знае всички ISBN назначения. Вместо това, това е разпределена система, където страните получават диапазони от числа, които след това се присвояват на големи издатели, които могат да ги подразпределят на по-малки издатели. Накрая, индивидуални номера се присвояват на книги."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Започнахме да картографираме ISBN <a %(blog)s>преди две години</a> с нашето извличане от ISBNdb. Оттогава сме извлекли много повече източници на metadata, като <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby и други. Пълен списък може да се намери на страниците „Datasets“ и „Torrents“ в Архивът на Анна. Сега имаме най-голямата напълно отворена, лесно изтегляема колекция от metadata за книги (и следователно ISBN) в света."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "<a %(blog)s>Писали сме обширно</a> за това защо се грижим за съхранението и защо в момента сме в критичен прозорец. Трябва сега да идентифицираме редки, недостатъчно фокусирани и уникално застрашени книги и да ги съхраним. Добрата metadata за всички книги в света помага за това."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Визуализация"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Освен общия образ, можем също да разгледаме индивидуалните datasets, които сме придобили. Използвайте падащото меню и бутоните, за да превключвате между тях."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Има много интересни модели, които да се видят в тези изображения. Защо има някаква регулярност на линии и блокове, която изглежда се случва на различни мащаби? Какви са празните области? Защо определени datasets са толкова групирани? Ще оставим тези въпроси като упражнение за читателя."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Награда от $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Има много за изследване тук, затова обявяваме награда за подобряване на визуализацията по-горе. За разлика от повечето ни награди, тази е с ограничено време. Трябва да <a %(annas_archive)s>представите</a> своя отворен код до 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Най-доброто представяне ще получи $6,000, второто място е $3,000, а третото място е $1,000. Всички награди ще бъдат изплатени с Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "По-долу са минималните критерии. Ако нито едно представяне не отговаря на критериите, може все пак да присъдим някои награди, но това ще бъде по наше усмотрение."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Форкнете това репо и редактирайте този HTML на блог поста (не се допускат други бекенди освен нашия Flask бекенд)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Направете изображението по-горе плавно увеличаемо, така че да можете да увеличите до индивидуални ISBN. Кликването върху ISBN трябва да ви отведе до страница с metadata или търсене в Архивът на Анна."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Трябва все още да можете да превключвате между всички различни datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Диапазоните на страните и издателите трябва да бъдат подчертани при задържане на мишката. Можете да използвате напр. <a %(github_xlcnd_isbnlib)s>data4info.py в isbnlib</a> за информация за страните и нашето извличане „isbngrp“ за издатели (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Трябва да работи добре на настолни и мобилни устройства."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "За бонус точки (това са само идеи — оставете въображението си да се развихри):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Силно внимание ще се обърне на използваемостта и колко добре изглежда."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Показвайте действителната metadata за индивидуални ISBN при увеличаване, като заглавие и автор."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "По-добра крива за запълване на пространството. Например зиг-заг, преминаващ от 0 до 4 на първия ред и след това обратно (в обратен ред) от 5 до 9 на втория ред — прилагано рекурсивно."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Различни или персонализирани цветови схеми."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Специални изгледи за сравняване на Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Начини за отстраняване на проблеми, като например друга metadata, която не съвпада добре (например значително различни заглавия)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Анотиране на изображения с коментари върху ISBN или диапазони."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Всякакви евристики за идентифициране на редки или застрашени книги."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Каквито и креативни идеи да измислите!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Можете напълно да се отклоните от минималните критерии и да направите напълно различна визуализация. Ако е наистина впечатляваща, тогава това отговаря на условията за наградата, но по наше усмотрение."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Направете предложения, като публикувате коментар към <a %(annas_archive)s>този въпрос</a> с връзка към вашето разклонено хранилище, заявка за сливане или разлика."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Код"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Кодът за генериране на тези изображения, както и други примери, може да бъде намерен в <a %(annas_archive)s>тази директория</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Създадохме компактен формат на данни, с който цялата необходима информация за ISBN е около 75MB (компресирана). Описанието на формата на данните и кодът за генерирането му могат да бъдат намерени <a %(annas_archive_l1244_1319)s>тук</a>. За наградата не сте задължени да го използвате, но вероятно е най-удобният формат за започване. Можете да трансформирате нашата metadata както искате (въпреки че целият ви код трябва да бъде с отворен код)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Нямаме търпение да видим какво ще измислите. Успех!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Архивът на Анна Контейнери (AAC): стандартизиране на изданията от най-голямата библиотека в сянка в света"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Архивът на Анна се превърна в най-голямата библиотека в сянка в света, което изисква от нас да стандартизираме нашите издания."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Архивът на Анна</a> се превърна в най-голямата библиотека в сянка в света и единствената библиотека в сянка от този мащаб, която е напълно с отворен код и отворени данни. По-долу е таблица от нашата страница с Datasets (леко модифицирана):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Постигнахме това по три начина:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Огледално копиране на съществуващи библиотеки в сянка с отворени данни (като Sci-Hub и Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Помощ на библиотеки в сянка, които искат да бъдат по-отворени, но нямат време или ресурси за това (като колекцията комикси на Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Извличане на данни от библиотеки, които не желаят да споделят в големи количества (като Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "За (2) и (3) сега управляваме значителна колекция от торенти сами (стотици TBs). Досега сме подхождали към тези колекции като еднократни, което означава специализирана инфраструктура и организация на данните за всяка колекция. Това добавя значителен разход към всяко издание и прави особено трудно извършването на по-инкрементални издания."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Затова решихме да стандартизираме нашите издания. Това е техническа публикация в блога, в която представяме нашия стандарт: <strong>Контейнери на Архива на Анна</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Цели на дизайна"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Нашият основен случай на употреба е разпространението на файлове и свързаните с тях metadata от различни съществуващи колекции. Нашите най-важни съображения са:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Хетерогенни файлове и metadata, възможно най-близо до оригиналния формат."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Хетерогенни идентификатори в изходните библиотеки или дори липса на идентификатори."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Отделни издания на metadata срещу данни от файлове или издания само на metadata (например нашето издание на ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Разпространение чрез торенти, но с възможност за други методи на разпространение (например IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Непроменими записи, тъй като трябва да приемем, че нашите торенти ще съществуват вечно."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Инкрементални издания / добавяеми издания."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Машинно четими и записваеми, удобно и бързо, особено за нашия стек (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Доста лесна човешка инспекция, въпреки че това е второстепенно спрямо машинната четимост."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Лесно за сеене на нашите колекции със стандартен нает seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Бинарните данни могат да бъдат обслужвани директно от уеб сървъри като Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Някои не-цели:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Не ни интересува файловете да са лесни за навигация ръчно на диск или да са търсими без предварителна обработка."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Не ни интересува да сме директно съвместими със съществуващ софтуер за библиотеки."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Докато трябва да е лесно за всеки да сеее нашата колекция, използвайки торенти, не очакваме файловете да са използваеми без значителни технически познания и ангажимент."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Тъй като Архивът на Анна е с отворен код, искаме да използваме нашия формат директно. Когато обновяваме нашия индекс за търсене, достъпваме само публично достъпни пътища, така че всеки, който клонира нашата библиотека, да може бързо да започне работа."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Стандартът"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "В крайна сметка се спряхме на относително прост стандарт. Той е доста гъвкав, ненормативен и все още в процес на разработка."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Контейнер на Архива на Анна) е единичен елемент, състоящ се от <strong>metadata</strong>, и по избор <strong>бинарни данни</strong>, и двете от които са неизменяеми. Той има глобално уникален идентификатор, наречен <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Колекция.</strong> Всеки AAC принадлежи към колекция, която по дефиниция е списък от AACs, които са семантично съвместими. Това означава, че ако направите значителна промяна във формата на метаданните, трябва да създадете нова колекция."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Колекции „записи“ и „файлове“.</strong> По конвенция често е удобно да се пускат „записи“ и „файлове“ като различни колекции, за да могат да бъдат пуснати на различни графици, например въз основа на честотата на събиране. „Запис“ е колекция само с метаданни, съдържаща информация като заглавия на книги, автори, ISBN и т.н., докато „файлове“ са колекциите, които съдържат самите файлове (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Форматът на AACID е следният: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Например, действителен AACID, който сме пуснали, е <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: името на колекцията, което може да съдържа ASCII букви, цифри и долни черти (но не и двойни долни черти)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: кратка версия на ISO 8601, винаги в UTC, например <code>20220723T194746Z</code>. Това число трябва да се увеличава монотонно за всяко издание, въпреки че точната му семантика може да се различава за всяка колекция. Препоръчваме да използвате времето на събиране или на генериране на ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: специфичен за колекцията идентификатор, ако е приложимо, например ID на Z-Library. Може да бъде пропуснат или съкратен. Трябва да бъде пропуснат или съкратен, ако AACID в противен случай би надвишил 150 знака."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID, но компресиран до ASCII, например с използване на base57. В момента използваме <a %(github_skorokithakis_shortuuid)s>shortuuid</a> библиотеката на Python."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Диапазон на AACID.</strong> Тъй като AACID съдържат монотонно увеличаващи се времеви печати, можем да използваме това, за да обозначим диапазони в рамките на определена колекция. Използваме този формат: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, където времевите печати са включени. Това е съвместимо с ISO 8601 нотацията. Диапазоните са непрекъснати и могат да се припокриват, но в случай на припокриване трябва да съдържат идентични записи като тези, които са били пуснати преди това в тази колекция (тъй като AACs са неизменяеми). Липсващи записи не са позволени."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Файл с метаданни.</strong> Файл с метаданни съдържа метаданните на диапазон от AACs, за една конкретна колекция. Те имат следните свойства:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Името на файла трябва да бъде диапазон на AACID, предшествано от <code style=\"color: red\">annas_archive_meta__</code> и последвано от <code>.jsonl.zstd</code>. Например, едно от нашите издания се нарича<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Както е указано от разширението на файла, типът на файла е <a %(jsonlines)s>JSON Lines</a>, компресиран с <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Всеки JSON обект трябва да съдържа следните полета на най-горно ниво: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (по избор). Не се допускат други полета."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> е произволни метаданни, според семантиката на колекцията. Трябва да бъде семантично съвместим в рамките на колекцията."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> е по избор и е името на папката с бинарни данни, която съдържа съответните бинарни данни. Името на файла на съответните бинарни данни в тази папка е AACID на записа."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Префиксът <code style=\"color: red\">annas_archive_meta__</code> може да бъде адаптиран към името на вашата институция, например <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Папка с бинарни данни.</strong> Папка с бинарните данни на диапазон от AACs, за една конкретна колекция. Те имат следните свойства:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Името на директорията трябва да бъде диапазон на AACID, предшествано от <code style=\"color: green\">annas_archive_data__</code>, и без суфикс. Например, една от нашите действителни издания има директория, наречена<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Директорията трябва да съдържа файлове с данни за всички AACs в посочения диапазон. Всеки файл с данни трябва да има AACID като име на файла (без разширения)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Препоръчително е тези папки да бъдат с управляем размер, например не по-големи от 100GB-1TB всяка, въпреки че тази препоръка може да се промени с времето."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Торенти.</strong> Файловете с metadata и папките с двоични данни могат да бъдат обединени в торенти, с един торент на файл с metadata или един торент на папка с двоични данни. Торентите трябва да имат оригиналното име на файла/директорията плюс наставката <code>.torrent</code> като тяхно име."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Пример"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Нека разгледаме нашето последно издание на Z-Library като пример. То се състои от две колекции: „<span style=\"background: #fffaa3\">zlib3_records</span>“ и „<span style=\"background: #ffd6fe\">zlib3_files</span>“. Това ни позволява да извличаме и издаваме отделно metadata записи от действителните файлове на книгите. Така издадохме два торента с файлове с metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Също така издадохме множество торенти с папки с двоични данни, но само за колекцията „<span style=\"background: #ffd6fe\">zlib3_files</span>“, общо 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Чрез изпълнение на <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> можем да видим какво има вътре:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "В този случай, това е metadata на книга, както е докладвано от Z-Library. На най-горното ниво имаме само „aacid“ и „metadata“, но няма „data_folder“, тъй като няма съответстващи двоични данни. AACID съдържа „22430000“ като основен ID, което виждаме, че е взето от „zlibrary_id“. Можем да очакваме други AAC в тази колекция да имат същата структура."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Сега нека изпълним <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Това е много по-малка AAC metadata, въпреки че основната част от този AAC се намира другаде в двоичен файл! Все пак, този път имаме „data_folder“, така че можем да очакваме съответстващите двоични данни да се намират на <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. „Metadata“ съдържа „zlibrary_id“, така че можем лесно да го асоциираме със съответстващия AAC в колекцията „zlib_records“. Можехме да го асоциираме по различни начини, например чрез AACID — стандартът не предписва това."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Обърнете внимание, че също така не е необходимо полето „metadata“ само по себе си да бъде JSON. То може да бъде низ, съдържащ XML или друг формат на данни. Можете дори да съхранявате информация за metadata в свързания двоичен блок, например ако е много данни."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Заключение"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "С този стандарт можем да правим издания по-инкрементално и по-лесно да добавяме нови източници на данни. Вече имаме няколко вълнуващи издания в процес на подготовка!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Също така се надяваме, че ще стане по-лесно за други библиотеки в сянка да огледалят нашите колекции. В крайна сметка, нашата цел е да запазим човешкото знание и култура завинаги, така че колкото повече излишък, толкова по-добре."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Актуализация на Анна: напълно отворен код архив, ElasticSearch, 300GB+ корици на книги"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Работим денонощно, за да предоставим добра алтернатива с Архива на Анна. Ето някои от нещата, които постигнахме наскоро."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Със спирането на Z-Library и (предполагаемите) основатели, които бяха арестувани, работим денонощно, за да предоставим добра алтернатива с Архива на Анна (няма да го свързваме тук, но можете да го намерите в Google). Ето някои от нещата, които постигнахме наскоро."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Архивът на Анна е напълно с отворен код"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Вярваме, че информацията трябва да бъде свободна, и нашият собствен код не е изключение. Публикувахме целия си код в нашата частно хоствана Gitlab инстанция: <a %(annas_archive)s>Софтуер на Анна</a>. Също така използваме тракера на проблеми, за да организираме работата си. Ако искате да се включите в нашето развитие, това е чудесно място да започнете."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "За да ви дадем представа за нещата, върху които работим, вземете нашата последна работа по подобрения на производителността на клиентската страна. Тъй като все още не сме внедрили пагинация, често връщахме много дълги страници с резултати от търсене, с 100-200 резултата. Не искахме да прекъсваме резултатите от търсенето твърде рано, но това означаваше, че ще забави някои устройства. За това внедрихме малък трик: обгърнахме повечето резултати от търсенето в HTML коментари (<code><!-- --></code>), и след това написахме малко Javascript, което да открива кога резултатът трябва да стане видим, в който момент ще разопаковаме коментара:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM „виртуализация“, реализирана в 23 реда, без нужда от сложни библиотеки! Това е видът бърз прагматичен код, който получавате, когато имате ограничено време и реални проблеми, които трябва да бъдат решени. Съобщено е, че нашето търсене вече работи добре на бавни устройства!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Друго голямо усилие беше автоматизирането на изграждането на базата данни. Когато стартирахме, просто събрахме различни източници на случаен принцип. Сега искаме да ги поддържаме актуализирани, затова написахме куп скриптове за изтегляне на нови metadata от двата форка на Library Genesis и ги интегрирахме. Целта е не само да направим това полезно за нашия архив, но и да улесним всеки, който иска да експериментира с metadata на shadow library. Целта би била Jupyter notebook, който има всякакви интересни metadata на разположение, за да можем да правим повече изследвания, като например да разберем какъв <a %(blog)s>процент от ISBN-ите са запазени завинаги</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Накрая, обновихме нашата система за дарения. Сега можете да използвате кредитна карта, за да депозирате директно пари в нашите крипто портфейли, без наистина да е необходимо да знаете нещо за криптовалутите. Ще продължим да наблюдаваме колко добре работи това на практика, но това е голяма стъпка."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Преминаване към ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Един от нашите <a %(annas_archive)s>билети</a> беше сбор от проблеми с нашата система за търсене. Използвахме MySQL пълнотекстово търсене, тъй като имахме всички наши данни в MySQL така или иначе. Но имаше своите ограничения:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Някои заявки отнемаха изключително дълго време, до степен, че заемат всички отворени връзки."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "По подразбиране MySQL има минимална дължина на думата, или индексът ви може да стане наистина голям. Хората съобщаваха, че не могат да търсят „Ben Hur“."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Търсенето беше само донякъде бързо, когато беше напълно заредено в паметта, което изискваше да получим по-скъпа машина, за да го изпълняваме, плюс някои команди за предварително зареждане на индекса при стартиране."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Нямаше да можем лесно да го разширим, за да изградим нови функции, като по-добра <a %(wikipedia_cjk_characters)s>токенизация за езици без интервали</a>, филтриране/фасетиране, сортиране, предложения „имахте предвид“, автозавършване и т.н."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "След разговор с куп експерти, се спряхме на ElasticSearch. Не е било перфектно (техните предложения „имахте предвид“ и функции за автозавършване са слаби), но като цяло е много по-добро от MySQL за търсене. Все още не сме <a %(youtube)s>твърде склонни</a> да го използваме за каквито и да било критични данни (въпреки че са направили много <a %(elastic_co)s>напредък</a>), но като цяло сме доста доволни от промяната."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Засега сме внедрили много по-бързо търсене, по-добра поддръжка на езици, по-добро сортиране по релевантност, различни опции за сортиране и филтриране по език/тип книга/тип файл. Ако сте любопитни как работи, <a %(annas_archive_l140)s>погледнете</a> <a %(annas_archive_l1115)s>тук</a> <a %(annas_archive_l1635)s>за повече информация</a>. Доста е достъпно, въпреки че може да използва още някои коментари…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ корици на книги пуснати"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Накрая, сме щастливи да обявим малко издание. В сътрудничество с хората, които управляват форка Libgen.rs, споделяме всички техни корици на книги чрез торенти и IPFS. Това ще разпредели натоварването от гледането на кориците между повече машини и ще ги запази по-добре. В много (но не всички) случаи, кориците на книгите са включени в самите файлове, така че това е вид „производни данни“. Но наличието им в IPFS все още е много полезно за ежедневната работа както на Архива на Анна, така и на различните форкове на Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Както обикновено, можете да намерите това издание в Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архива на Анна</a>). Няма да го свързваме тук, но можете лесно да го намерите."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Надяваме се, че можем да намалим темпото си малко, сега когато имаме прилична алтернатива на Z-Library. Това натоварване не е особено устойчиво. Ако сте заинтересовани да помогнете с програмиране, сървърни операции или работа по запазване, определено се свържете с нас. Все още има много <a %(annas_archive)s>работа за вършене</a>. Благодарим ви за интереса и подкрепата."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Архивът на Анна е архивирал най-голямата в света shadow library на комикси (95TB) — можете да помогнете да я разпространите"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Най-голямата shadow library на комикси в света имаше една точка на провал... до днес."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Обсъдете в Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Най-голямата shadow library на комикси вероятно е тази на определен форк на Library Genesis: Libgen.li. Единственият администратор, който управлява този сайт, успя да събере невероятна колекция от комикси с над 2 милиона файла, общо над 95TB. Въпреки това, за разлика от други колекции на Library Genesis, тази не беше достъпна в насипно състояние чрез торенти. Можете да получите достъп до тези комикси само индивидуално чрез неговия бавен личен сървър — една точка на провал. До днес!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "В тази публикация ще ви разкажем повече за тази колекция и за нашата кампания за набиране на средства, за да подкрепим повече от тази работа."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Д-р Барбара Гордън се опитва да се изгуби в обикновения свят на библиотеката…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Разклонения на Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Първо, малко предистория. Може би познавате Library Genesis заради тяхната епична колекция от книги. По-малко хора знаят, че доброволците на Library Genesis са създали и други проекти, като значителна колекция от списания и стандартни документи, пълен архив на Sci-Hub (в сътрудничество с основателя на Sci-Hub, Александра Елбакян) и наистина, огромна колекция от комикси."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "В даден момент различни оператори на огледала на Library Genesis поеха по различни пътища, което доведе до сегашната ситуация с наличието на няколко различни „разклонения“, всички все още носещи името Library Genesis. Разклонението Libgen.li уникално притежава тази колекция от комикси, както и значителна колекция от списания (върху която също работим)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Сътрудничество"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "С оглед на размера си, тази колекция отдавна е в нашия списък с желания, така че след успеха ни с архивирането на Z-Library, насочихме вниманието си към тази колекция. Първоначално я извличахме директно, което беше доста предизвикателство, тъй като техният сървър не беше в най-добро състояние. По този начин получихме около 15TB, но процесът беше бавен."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "За щастие, успяхме да се свържем с оператора на библиотеката, който се съгласи да ни изпрати всички данни директно, което беше много по-бързо. Все пак отне повече от половин година за прехвърляне и обработка на всички данни и почти загубихме всичко заради повреда на диска, което би означавало да започнем отначало."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Този опит ни накара да вярваме, че е важно да разпространим тези данни възможно най-бързо, за да могат да бъдат огледални навсякъде. Ние сме само на едно или две нещастни събития от загубата на тази колекция завинаги!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Колекцията"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Бързото движение означава, че колекцията е малко неорганизирана… Нека да погледнем. Представете си, че имаме файлова система (която в действителност разделяме на торенти):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Първата директория, <code>/repository</code>, е по-структурираната част от това. Тази директория съдържа така наречените „хиляда директории“: директории, всяка с хиляда файла, които са инкрементално номерирани в базата данни. Директорията <code>0</code> съдържа файлове с comic_id от 0 до 999 и така нататък."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Това е същата схема, която Library Genesis използва за своите колекции от художествена и нехудожествена литература. Идеята е, че всяка „хиляда директория“ автоматично се превръща в торент, веднага щом се запълни."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Въпреки това, операторът на Libgen.li никога не е създавал торенти за тази колекция, и така хиляда директории вероятно са станали неудобни и са отстъпили място на „неподредени директории“. Това са <code>/comics0</code> до <code>/comics4</code>. Всички те съдържат уникални структури на директории, които вероятно са имали смисъл за събиране на файловете, но сега не ни изглеждат много логични. За щастие, metadata все още се отнася директно към всички тези файлове, така че организацията им на диска всъщност няма значение!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata е налична под формата на MySQL база данни. Тя може да бъде изтеглена директно от уебсайта на Libgen.li, но ние също ще я направим достъпна в торент, заедно с нашата собствена таблица с всички MD5 хешове."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Анализ"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Когато получите 95TB, изсипани във вашия клъстер за съхранение, се опитвате да разберете какво всъщност има там… Направихме някои анализи, за да видим дали можем да намалим размера малко, например чрез премахване на дубликати. Ето някои от нашите открития:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Семантичните дубликати (различни сканирания на една и съща книга) теоретично могат да бъдат филтрирани, но това е сложно. Когато ръчно преглеждахме комиксите, намерихме твърде много фалшиви положителни резултати."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Има някои дубликати само по MD5, което е относително разточително, но филтрирането им би ни дало само около 1% in спестявания. В този мащаб това все още е около 1TB, но също така, в този мащаб 1TB наистина няма значение. Предпочитаме да не рискуваме случайно унищожаване на данни в този процес."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Намерихме куп не-книжни данни, като филми, базирани на комикси. Това също изглежда разточително, тъй като те вече са широко достъпни чрез други средства. Въпреки това, осъзнахме, че не можем просто да филтрираме филмовите файлове, тъй като има и <em>интерактивни комикси</em>, които са били пуснати на компютър, които някой е записал и запазил като филми."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "В крайна сметка, всичко, което бихме могли да изтрием от колекцията, би спестило само няколко процента. Тогава си спомнихме, че сме събирачи на данни, и хората, които ще огледалят това, също са събирачи на данни, и така, „КАКВО ИМАТЕ ПРЕДВИД, ИЗТРИВАНЕ?!“ :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Затова ви представяме пълната, немодифицирана колекция. Това е много данни, но се надяваме, че достатъчно хора ще се грижат да я разпространяват."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Кампания за набиране на средства"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Пускаме тези данни в няколко големи части. Първият торент е на <code>/comics0</code>, който събрахме в един огромен 12TB .tar файл. Това е по-добре за вашия твърд диск и торент софтуер, отколкото милиони по-малки файлове."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Като част от това издание, провеждаме кампания за набиране на средства. Целим да съберем $20,000, за да покрием оперативните и договорните разходи за тази колекция, както и да подкрепим текущи и бъдещи проекти. Имаме някои <em>огромни</em> в процес на работа."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Кого подкрепям с моята дарение?</em> Накратко: ние архивираме цялото знание и култура на човечеството и го правим лесно достъпно. Целият ни код и данни са с отворен код, ние сме напълно доброволчески проект и досега сме запазили 125TB книги (в допълнение към съществуващите торенти на Libgen и Scihub). В крайна сметка изграждаме маховик, който позволява и стимулира хората да намират, сканират и архивират всички книги в света. Ще напишем за нашия главен план в бъдеща публикация. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Ако дарите за 12-месечно членство „Amazing Archivist“ ($780), ще можете да <strong>„осиновите торент“</strong>, което означава, че ще поставим вашето потребителско име или съобщение в името на един от торентите!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Можете да дарите, като отидете на <a %(wikipedia_annas_archive)s>Архивът на Анна</a> и кликнете върху бутона „Дарете“. Също така търсим още доброволци: софтуерни инженери, изследователи по сигурността, експерти по анонимни търговци и преводачи. Можете също така да ни подкрепите, като предоставите хостинг услуги. И разбира се, моля, разпространявайте нашите торенти!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Благодарим на всички, които вече ни подкрепиха толкова щедро! Наистина правите разлика."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Ето торентите, които са пуснати досега (все още обработваме останалите):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Всички торенти могат да бъдат намерени на <a %(wikipedia_annas_archive)s>Архивът на Анна</a> под „Datasets“ (не свързваме директно там, за да не бъдат премахнати връзките към този блог от Reddit, Twitter и т.н.). Оттам следвайте връзката към уебсайта на Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Какво следва?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Група от торенти са чудесни за дългосрочно съхранение, но не толкова за ежедневен достъп. Ще работим с хостинг партньори, за да качим всички тези данни в интернет (тъй като Архивът на Анна не хоства нищо директно). Разбира се, ще можете да намерите тези връзки за изтегляне в Архивът на Анна."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Също така каним всички да правят неща с тези данни! Помогнете ни да ги анализираме по-добре, да ги дублираме, да ги качим на IPFS, да ги ремиксираме, да обучите вашите AI модели с тях и т.н. Всичко е ваше, и нямаме търпение да видим какво ще направите с тях."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Накрая, както казахме преди, все още имаме някои огромни издания, които предстоят (ако <em>някой</em> може <em>случайно</em> да ни изпрати дъмп на <em>определена</em> база данни ACS4, знаете къде да ни намерите...), както и изграждането на маховика за архивиране на всички книги в света."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Така че останете на линия, ние едва започваме."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x нови книги добавени към Огледалото на пиратската библиотека (+24TB, 3.8 милиона книги)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "В оригиналното издание на Огледалото на пиратската библиотека (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>), направихме огледало на Z-Library, голяма незаконна колекция от книги. Като напомняне, това е, което написахме в оригиналната публикация в блога:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library е популярна (и незаконна) библиотека. Те са взели колекцията на Library Genesis и са я направили лесно търсима. Освен това, те са станали много ефективни в привличането на нови книжни приноси, като стимулират потребителите, които допринасят, с различни предимства. В момента те не връщат тези нови книги обратно на Library Genesis. И за разлика от Library Genesis, те не правят колекцията си лесно огледална, което предотвратява широко съхранение. Това е важно за техния бизнес модел, тъй като те таксуват за достъп до тяхната колекция в големи количества (повече от 10 книги на ден)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Ние не правим морални преценки относно таксуването за масов достъп до незаконна колекция от книги. Без съмнение, Z-Library е успешна в разширяването на достъпа до знания и в намирането на повече книги. Ние сме тук, за да изпълним нашата част: да осигурим дългосрочното съхранение на тази частна колекция."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Тази колекция датира от средата на 2021 г. Междувременно, Z-Library расте с поразителна скорост: те са добавили около 3.8 милиона нови книги. Разбира се, има някои дубликати, но по-голямата част от тях изглежда са наистина нови книги или по-качествени сканирания на вече подадени книги. Това е в голяма степен благодарение на увеличения брой доброволни модератори в Z-Library и тяхната система за масово качване с премахване на дубликати. Бихме искали да ги поздравим за тези постижения."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "С удоволствие съобщаваме, че сме получили всички книги, които бяха добавени към Z-Library между последното ни огледало и август 2022 г. Също така се върнахме и събрахме някои книги, които пропуснахме първия път. Всичко на всичко, тази нова колекция е около 24TB, което е много по-голямо от предишната (7TB). Нашето огледало сега е общо 31TB. Отново, премахнахме дубликатите спрямо Library Genesis, тъй като вече има налични торенти за тази колекция."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Моля, посетете Pirate Library Mirror, за да разгледате новата колекция (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>). Там има повече информация за това как са структурирани файловете и какво се е променило от последния път. Няма да го свързваме оттук, тъй като това е просто блог сайт, който не хоства никакви незаконни материали."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Разбира се, споделянето също е чудесен начин да ни помогнете. Благодарим на всички, които споделят нашия предишен набор от торенти. Благодарни сме за положителния отговор и сме щастливи, че има толкова много хора, които се грижат за съхранението на знания и култура по този необичаен начин."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Как да станете пиратски архивист"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Първото предизвикателство може да бъде изненадващо. То не е технически проблем или правен проблем. То е психологически проблем."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Преди да се потопим, две актуализации за Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Получихме изключително щедри дарения. Първото беше $10k от анонимен индивид, който също подкрепя \"bookwarrior\", оригиналния основател на Library Genesis. Специални благодарности на bookwarrior за улесняването на това дарение. Второто беше още $10k от анонимен дарител, който се свърза с нас след последното ни издание и беше вдъхновен да помогне. Имаше и редица по-малки дарения. Благодарим ви много за вашата щедра подкрепа. Имаме някои вълнуващи нови проекти в процес на разработка, които това ще подкрепи, така че останете на линия."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Имахме някои технически затруднения с размера на второто ни издание, но нашите торенти вече са активни и се споделят. Също така получихме щедро предложение от анонимен индивид да сподели нашата колекция на своите много високоскоростни сървъри, така че правим специално качване на техните машини, след което всички останали, които изтеглят колекцията, трябва да видят значително подобрение в скоростта."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Цели книги могат да бъдат написани за <em>защо</em> на дигиталното съхранение като цяло и пиратския архивизъм в частност, но нека дадем кратко въведение за тези, които не са много запознати. Светът произвежда повече знания и култура от всякога, но също така повече от тях се губят от всякога. Човечеството в голяма степен поверява на корпорации като академични издатели, стрийминг услуги и социални медийни компании това наследство, и те често не са се доказали като добри настойници. Разгледайте документалния филм Digital Amnesia или наистина всяка лекция на Джейсън Скот."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Има някои институции, които вършат добра работа по архивирането на колкото се може повече, но те са ограничени от закона. Като пирати, ние сме в уникална позиция да архивираме колекции, които те не могат да докоснат, поради прилагането на авторски права или други ограничения. Можем също така да огледаме колекции многократно по света, като по този начин увеличаваме шансовете за правилно съхранение."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Засега няма да влизаме в дискусии за плюсовете и минусите на интелектуалната собственост, моралността на нарушаването на закона, размишленията върху цензурата или въпроса за достъпа до знания и култура. С всичко това настрана, нека се потопим в <em>как</em>. Ще споделим как нашият екип стана пиратски архивисти и уроците, които научихме по пътя. Има много предизвикателства, когато се впуснете в това пътуване, и се надяваме, че можем да ви помогнем с някои от тях."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Общност"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Първото предизвикателство може да бъде изненадващо. То не е технически проблем или правен проблем. То е психологически проблем: извършването на тази работа в сенките може да бъде невероятно самотно. В зависимост от това, което планирате да направите, и вашия модел на заплаха, може да се наложи да бъдете много внимателни. В единия край на спектъра имаме хора като Александра Елбакян*, основателката на Sci-Hub, която е много открита за своите дейности. Но тя е в голям риск да бъде арестувана, ако посети западна страна в този момент, и може да се изправи пред десетилетия затвор. Това ли е риск, който бихте били готови да поемете? Ние сме в другия край на спектъра; много внимателни да не оставяме никакви следи и с висока оперативна сигурност."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Както е споменато на HN от \"ynno\", Александра първоначално не е искала да бъде известна: \"Нейните сървъри бяха настроени да излъчват подробни съобщения за грешки от PHP, включително пълния път на грешния изходен файл, който беше под директория /home/<USER>" Така че, използвайте случайни потребителски имена на компютрите, които използвате за тези неща, в случай че нещо е конфигурирано неправилно."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Тази тайна обаче идва с психологическа цена. Повечето хора обичат да бъдат признати за работата, която вършат, и все пак не можете да получите никакъв кредит за това в реалния живот. Дори простите неща могат да бъдат предизвикателни, като например приятели, които ви питат с какво сте се занимавали (в един момент \"занимавам се с моя NAS / домашен лаб\" става старо)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Ето защо е толкова важно да намерите някаква общност. Можете да се откажете от част от оперативната сигурност, като се доверите на някои много близки приятели, на които знаете, че можете да се доверите дълбоко. Дори тогава бъдете внимателни да не поставяте нищо в писмена форма, в случай че трябва да предадат имейлите си на властите или ако техните устройства са компрометирани по някакъв друг начин."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Още по-добре е да намерите някои съмишленици пирати. Ако вашите близки приятели се интересуват да се присъединят към вас, чудесно! В противен случай може да успеете да намерите други онлайн. За съжаление, това все още е нишова общност. Досега сме намерили само шепа други, които са активни в това пространство. Добри начални места изглежда са форумите на Library Genesis и r/DataHoarder. Екипът на Archive също има съмишленици, въпреки че те оперират в рамките на закона (дори и в някои сиви зони на закона). Традиционните \"warez\" и пиратски сцени също имат хора, които мислят по подобен начин."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Отворени сме за идеи как да насърчим общността и да изследваме идеи. Чувствайте се свободни да ни пишете в Twitter или Reddit. Може би бихме могли да организираме някакъв форум или чат група. Едно предизвикателство е, че това лесно може да бъде цензурирано, когато се използват общи платформи, така че ще трябва да го хостваме сами. Има и компромис между това да направим тези дискусии напълно публични (повече потенциално участие) и да ги направим частни (да не позволим на потенциални \"цели\" да знаят, че ще ги изстържем). Ще трябва да помислим за това. Уведомете ни, ако се интересувате от това!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Проекти"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Когато правим проект, той има няколко фази:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Избор на домейн / философия: Къде приблизително искате да се фокусирате и защо? Какви са вашите уникални страсти, умения и обстоятелства, които можете да използвате в своя полза?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Избор на цел: Коя конкретна колекция ще огледате?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Изстъргване на metadata: Каталогизиране на информация за файловете, без всъщност да се изтеглят (често много по-големите) файлове."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Избор на данни: Въз основа на metadata, стесняване на това кои данни са най-релевантни за архивиране в момента. Може да бъде всичко, но често има разумен начин за спестяване на пространство и честотна лента."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Изстъргване на данни: Всъщност получаване на данните."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Разпространение: Опаковане в торенти, обявяване някъде, привличане на хора да го разпространят."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Това не са напълно независими фази и често прозрения от по-късна фаза ви връщат към по-ранна фаза. Например, по време на изстъргване на metadata може да осъзнаете, че целта, която сте избрали, има защитни механизми извън вашето ниво на умения (като блокиране на IP), така че се връщате и намирате друга цел."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Избор на домейн / философия"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Няма недостиг на знания и културно наследство, които да бъдат запазени, което може да бъде поразително. Затова често е полезно да отделите момент и да помислите какъв може да бъде вашият принос."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Всеки има различен начин на мислене за това, но ето някои въпроси, които можете да си зададете:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Защо се интересувате от това? Какво ви вълнува? Ако можем да съберем група хора, които всички архивират нещата, които ги интересуват, това би покрило много! Ще знаете много повече от средния човек за вашата страст, като какви са важните данни за запазване, кои са най-добрите колекции и онлайн общности и т.н."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Какви умения имате, които можете да използвате в своя полза? Например, ако сте експерт по онлайн сигурност, можете да намерите начини за преодоляване на блокирането на IP за сигурни цели. Ако сте добри в организирането на общности, тогава може би можете да съберете някои хора около цел. Полезно е да знаете малко програмиране, дори само за да поддържате добра оперативна сигурност през целия този процес."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Колко време имате за това? Нашият съвет би бил да започнете с малки проекти и да правите по-големи, когато се усетите, но това може да стане всепоглъщащо."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Коя би била област с висок лост за фокусиране? Ако ще прекарате X часа в пиратско архивиране, как можете да получите най-голямата \"възвръщаемост на инвестицията\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Какви са уникалните начини, по които мислите за това? Може да имате някои интересни идеи или подходи, които другите може да са пропуснали."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "В нашия случай, ние се интересувахме особено от дългосрочното запазване на науката. Знаехме за Library Genesis и как тя беше напълно огледана много пъти чрез торенти. Обичахме тази идея. Тогава един ден, един от нас се опита да намери някои научни учебници в Library Genesis, но не успя да ги намери, което постави под съмнение колко пълна е тя наистина. След това потърсихме тези учебници онлайн и ги намерихме на други места, което засади семето за нашия проект. Дори преди да знаем за Z-Library, имахме идеята да не се опитваме да събираме всички тези книги ръчно, а да се фокусираме върху огледалото на съществуващи колекции и да ги допринасяме обратно към Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Избор на цел"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "И така, имаме нашата област, която разглеждаме, сега коя конкретна колекция да огледаме? Има няколко неща, които правят една цел добра:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Голяма"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Уникална: не е вече добре покрита от други проекти."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Достъпна: не използва много слоеве защита, за да ви попречи да извличате тяхната metadata и данни."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Специална информация: имате някаква специална информация за тази цел, като например специален достъп до тази колекция или сте разбрали как да преодолеете техните защити. Това не е задължително (нашият предстоящ проект не прави нищо специално), но определено помага!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Когато намерихме нашите учебници по наука на уебсайтове, различни от Library Genesis, се опитахме да разберем как са се появили в интернет. След това открихме Z-Library и осъзнахме, че макар повечето книги да не се появяват първо там, те в крайна сметка се озовават там. Научихме за връзката му с Library Genesis и структурата на (финансовите) стимули и превъзходния потребителски интерфейс, които го правят много по-пълна колекция. След това направихме предварително извличане на metadata и данни и осъзнахме, че можем да заобиколим ограниченията за изтегляне на IP, използвайки специалния достъп на един от нашите членове до много прокси сървъри."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Докато изследвате различни цели, вече е важно да скриете следите си, като използвате VPN и временни имейл адреси, за които ще говорим повече по-късно."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Извличане на metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Нека станем малко по-технически тук. За действителното извличане на metadata от уебсайтове, ние сме запазили нещата доста прости. Използваме Python скриптове, понякога curl и MySQL база данни, за да съхраняваме резултатите. Не сме използвали никакъв сложен софтуер за извличане, който може да картографира сложни уебсайтове, тъй като досега ни трябваше само да извличаме един или два вида страници, като просто изброяваме чрез идентификатори и анализираме HTML. Ако няма лесно изброими страници, тогава може да ви трябва подходящ обхождач, който се опитва да намери всички страници."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Преди да започнете да извличате цял уебсайт, опитайте да го направите ръчно за малко. Прегледайте няколко десетки страници сами, за да получите представа как работи това. Понякога вече ще се сблъскате с IP блокировки или друго интересно поведение по този начин. Същото важи и за извличането на данни: преди да се задълбочите твърде много в тази цел, уверете се, че можете ефективно да изтеглите нейните данни."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "За да заобиколите ограниченията, има няколко неща, които можете да опитате. Има ли други IP адреси или сървъри, които хостват същите данни, но нямат същите ограничения? Има ли API крайни точки, които нямат ограничения, докато други имат? При каква скорост на изтегляне вашият IP се блокира и за колко време? Или не сте блокирани, но сте забавени? Какво ще стане, ако създадете потребителски акаунт, как се променят нещата тогава? Можете ли да използвате HTTP/2, за да поддържате връзките отворени и увеличава ли това скоростта, с която можете да заявявате страници? Има ли страници, които изброяват няколко файла наведнъж и достатъчна ли е информацията, изброена там?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Нещата, които вероятно искате да запазите, включват:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Заглавие"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Име на файл / местоположение"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: може да бъде някакъв вътрешен ID, но ID като ISBN или DOI също са полезни."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Размер: за да изчислите колко дисково пространство ви е необходимо."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Хеш (md5, sha1): за да потвърдите, че сте изтеглили файла правилно."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Дата на добавяне/промяна: за да можете да се върнете по-късно и да изтеглите файлове, които не сте изтеглили преди (въпреки че често можете да използвате и ID или хеш за това)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Описание, категория, тагове, автори, език и т.н."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Обикновено правим това на два етапа. Първо изтегляме суровите HTML файлове, обикновено директно в MySQL (за да избегнем много малки файлове, за които говорим повече по-долу). След това, в отделна стъпка, преминаваме през тези HTML файлове и ги анализираме в действителни MySQL таблици. По този начин не е нужно да изтегляте всичко отначало, ако откриете грешка в кода си за анализ, тъй като можете просто да преработите HTML файловете с новия код. Също така често е по-лесно да се паралелизира стъпката на обработка, като по този начин се спестява време (и можете да напишете кода за обработка, докато извличането работи, вместо да трябва да пишете и двете стъпки наведнъж)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "И накрая, обърнете внимание, че за някои цели извличането на метаданни е всичко, което има. Има някои огромни колекции от метаданни, които не са правилно запазени."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Избор на данни"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Често можете да използвате метаданните, за да определите разумен подмножество от данни за изтегляне. Дори ако в крайна сметка искате да изтеглите всички данни, може да е полезно да приоритизирате най-важните елементи първо, в случай че бъдете засечени и защитите бъдат подобрени, или защото ще трябва да купите повече дискове, или просто защото нещо друго се появи в живота ви, преди да можете да изтеглите всичко."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Например, колекция може да има множество издания на един и същ основен ресурс (като книга или филм), където едно е маркирано като най-добро качество. Запазването на тези издания първо би имало много смисъл. Може в крайна сметка да искате да запазите всички издания, тъй като в някои случаи метаданните може да са неправилно маркирани, или може да има неизвестни компромиси между изданията (например, \"най-доброто издание\" може да е най-добро в повечето отношения, но по-лошо в други, като филм с по-висока резолюция, но без субтитри)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Можете също така да търсите в базата си данни с метаданни, за да намерите интересни неща. Кой е най-големият файл, който се хоства, и защо е толкова голям? Кой е най-малкият файл? Има ли интересни или неочаквани модели, когато става въпрос за определени категории, езици и т.н.? Има ли дублирани или много подобни заглавия? Има ли модели за това кога данните са добавени, като един ден, в който много файлове са добавени наведнъж? Често можете да научите много, като разглеждате набора от данни по различни начини."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "В нашия случай, ние премахнахме дублирането на книги от Z-Library спрямо md5 хешовете в Library Genesis, като по този начин спестихме много време за изтегляне и дисково пространство. Това обаче е доста уникална ситуация. В повечето случаи няма изчерпателни бази данни за това кои файлове вече са правилно запазени от други пирати. Това само по себе си е огромна възможност за някого там. Би било чудесно да има редовно актуализиран преглед на неща като музика и филми, които вече са широко разпространени на торент сайтове и следователно са с по-нисък приоритет за включване в пиратски огледала."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Извличане на данни"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Сега сте готови да изтеглите данните в голям обем. Както беше споменато по-рано, на този етап вече трябва да сте изтеглили ръчно куп файлове, за да разберете по-добре поведението и ограниченията на целта. Въпреки това, все още ще има изненади, когато всъщност започнете да изтегляте много файлове наведнъж."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Нашият съвет тук е основно да го поддържате просто. Започнете, като просто изтеглите куп файлове. Можете да използвате Python и след това да разширите до множество нишки. Но понякога дори по-просто е да генерирате Bash файлове директно от базата данни и след това да стартирате няколко от тях в няколко терминални прозореца, за да увеличите мащаба. Бърз технически трик, който си струва да се спомене тук, е използването на OUTFILE в MySQL, което можете да напишете навсякъде, ако деактивирате \"secure_file_priv\" в mysqld.cnf (и не забравяйте също да деактивирате/замените AppArmor, ако сте на Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Съхраняваме данните на обикновени твърди дискове. Започнете с каквото имате и разширявайте бавно. Може да бъде поразително да мислите за съхранение на стотици TB данни. Ако това е ситуацията, с която се сблъсквате, просто извадете добър подмножество първо и в съобщението си поискайте помощ за съхранение на останалото. Ако искате да получите повече твърди дискове сами, тогава r/DataHoarder има някои добри ресурси за получаване на добри сделки."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Опитайте се да не се притеснявате твърде много за сложни файлови системи. Лесно е да попаднете в заешката дупка на настройването на неща като ZFS. Една техническа подробност, за която трябва да сте наясно обаче, е, че много файлови системи не се справят добре с много файлове. Открихме, че простото решение е да създадете множество директории, например за различни диапазони от ID или хеш префикси."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "След изтеглянето на данните, не забравяйте да проверите целостта на файловете, използвайки хешовете в метаданните, ако са налични."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Разпространение"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Имате данните, като по този начин притежавате първото пиратско огледало на вашата цел (най-вероятно). В много отношения най-трудната част е приключила, но най-рисковата част все още е пред вас. В крайна сметка, досега сте били скрити; летейки под радара. Всичко, което трябваше да направите, беше да използвате добър VPN през цялото време, да не попълвате личните си данни във формуляри (разбира се), и може би да използвате специална сесия на браузъра (или дори различен компютър)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Сега трябва да разпространите данните. В нашия случай първо искахме да върнем книгите в Library Genesis, но бързо открихме трудностите в това (сортировка на художествена срещу нехудожествена литература). Затова решихме да разпространяваме чрез торенти в стил Library Genesis. Ако имате възможност да допринесете към съществуващ проект, това може да ви спести много време. Въпреки това, в момента няма много добре организирани пиратски огледала."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Така че да кажем, че решите да разпространявате торенти сами. Опитайте се да ги поддържате малки, за да са лесни за огледало на други уебсайтове. След това ще трябва да сеете торентите сами, докато все още оставате анонимни. Можете да използвате VPN (с или без пренасочване на портове) или да платите с тумблирани биткойни за Seedbox. Ако не знаете какво означават някои от тези термини, ще имате много за четене, тъй като е важно да разберете рисковите компромиси тук."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Можете да хоствате самите торент файлове на съществуващи торент уебсайтове. В нашия случай избрахме да хостваме уебсайт, тъй като също искахме да разпространим нашата философия по ясен начин. Можете да направите това сами по подобен начин (ние използваме Njalla за нашите домейни и хостинг, платени с тумблирани биткойни), но също така не се колебайте да се свържете с нас, за да хостваме вашите торенти. Ние се стремим да изградим изчерпателен индекс на пиратски огледала с течение на времето, ако тази идея се наложи."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Що се отнася до избора на VPN, много е писано за това вече, така че просто ще повторим общия съвет да избирате по репутация. Реални съдебно тествани политики без логове с дълги записи за защита на поверителността са най-нискорисковата опция, според нас. Обърнете внимание, че дори когато правите всичко правилно, никога не можете да достигнете до нулев риск. Например, когато сеете вашите торенти, силно мотивиран държавен актьор вероятно може да разгледа входящите и изходящите потоци от данни за VPN сървъри и да заключи кой сте. Или просто можете да направите грешка по някакъв начин. Вероятно вече сме го направили и ще го направим отново. За щастие, държавите не се интересуват <em>толкова</em> много от пиратството."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Едно решение, което трябва да вземете за всеки проект, е дали да го публикувате с предишната идентичност или не. Ако продължите да използвате същото име, тогава грешки в оперативната сигурност от по-ранни проекти могат да се върнат, за да ви навредят. Но публикуването под различни имена означава, че не изграждате по-дълготрайна репутация. Ние избрахме да имаме силна оперативна сигурност от самото начало, за да можем да продължим да използваме същата идентичност, но няма да се колебаем да публикуваме под различно име, ако направим грешка или ако обстоятелствата го изискват."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Разпространението на информацията може да бъде трудно. Както казахме, това все още е нишова общност. Първоначално публикувахме в Reddit, но наистина получихме внимание в Hacker News. Засега нашата препоръка е да го публикувате на няколко места и да видите какво ще се случи. И отново, свържете се с нас. Ще се радваме да разпространим думата за повече усилия за пиратски архивизъм."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Заключение"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Надяваме се, че това е полезно за новопрохождащите пиратски архивисти. С нетърпение ви приветстваме в този свят, така че не се колебайте да се свържете с нас. Нека запазим колкото се може повече от световното знание и култура и да го огледаме навсякъде."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Представяме Пиратското библиотечно огледало: Запазване на 7TB книги (които не са в Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Този проект (РЕДАКЦИЯ: преместен в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>) има за цел да допринесе за запазването и освобождаването на човешкото знание. Ние правим нашия малък и скромен принос, следвайки стъпките на великите преди нас."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Фокусът на този проект е илюстриран от неговото име:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Пират</strong> - Ние умишлено нарушаваме закона за авторското право в повечето страни. Това ни позволява да правим нещо, което законните организации не могат: да гарантираме, че книгите са огледани навсякъде."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Библиотека</strong> - Както повечето библиотеки, ние се фокусираме основно върху писмени материали като книги. Може да се разширим и в други видове медии в бъдеще."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Огледало</strong> - Ние сме строго огледало на съществуващи библиотеки. Фокусираме се върху запазването, а не върху лесното търсене и изтегляне на книги (достъп) или създаването на голяма общност от хора, които допринасят с нови книги (източници)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Първата библиотека, която сме огледали, е Z-Library. Това е популярна (и незаконна) библиотека. Те са взели колекцията на Library Genesis и са я направили лесно търсима. Освен това, те са станали много ефективни в привличането на нови книжни приноси, като стимулират допринасящите потребители с различни предимства. В момента те не връщат тези нови книги обратно на Library Genesis. И за разлика от Library Genesis, те не правят колекцията си лесно огледаема, което пречи на широкото запазване. Това е важно за техния бизнес модел, тъй като те таксуват за достъп до колекцията им в големи количества (повече от 10 книги на ден)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Ние не правим морални преценки относно таксуването за масов достъп до незаконна колекция от книги. Без съмнение, Z-Library е успешна в разширяването на достъпа до знания и в намирането на повече книги. Ние сме тук, за да изпълним нашата част: да осигурим дългосрочното съхранение на тази частна колекция."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Бихме искали да ви поканим да помогнете за запазването и освобождаването на човешкото знание, като изтеглите и сеете нашите торенти. Вижте страницата на проекта за повече информация относно организацията на данните."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Също така много бихме искали да допринесете с вашите идеи за това кои колекции да огледаме следващите и как да го направим. Заедно можем да постигнем много. Това е само малък принос сред безброй други. Благодарим ви за всичко, което правите."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Ние не свързваме файловете от този блог. Моля, намерете ги сами.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb дъмп, или Колко книги са запазени завинаги?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Ако правилно дедеуплицираме файловете от библиотеките в сянка, какъв процент от всички книги в света сме запазили?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "С Пиратското библиотечно огледало (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>), нашата цел е да вземем всички книги в света и да ги запазим завинаги.<sup>1</sup> Между нашите торенти на Z-Library и оригиналните торенти на Library Genesis, имаме 11,783,153 файла. Но колко е това наистина? Ако правилно дедеуплицираме тези файлове, какъв процент от всички книги в света сме запазили? Наистина бихме искали да имаме нещо такова:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of писменото наследство на човечеството запазено завинаги"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "За процент, ни трябва знаменател: общият брой на книгите, публикувани някога.<sup>2</sup> Преди края на Google Books, инженерът на проекта, Леонид Тайчър, <a %(booksearch_blogspot)s>се опита да оцени</a> този брой. Той стигна — с ирония — до 129,864,880 („поне до неделя“). Той оцени този брой, като създаде обединена база данни на всички книги в света. За това, той събра различни Datasets и след това ги обедини по различни начини."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Като кратко отклонение, има още един човек, който се опита да каталогизира всички книги в света: Аарон Суорц, покойният дигитален активист и съосновател на Reddit.<sup>3</sup> Той <a %(youtube)s>започна Open Library</a> с целта „една уеб страница за всяка книга, публикувана някога“, комбинирайки данни от много различни източници. В крайна сметка той плати най-високата цена за своята работа по дигитално съхранение, когато беше преследван за масово изтегляне на академични статии, което доведе до неговото самоубийство. Ненужно е да казваме, че това е една от причините нашата група да е псевдонимна и защо сме много внимателни. Open Library все още се управлява героично от хората в Internet Archive, продължавайки наследството на Аарон. Ще се върнем към това по-късно в този пост."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "В блога на Google, Тайчър описва някои от предизвикателствата при оценката на този брой. Първо, какво представлява книга? Има няколко възможни дефиниции:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Физически копия.</strong> Очевидно това не е много полезно, тъй като те са просто дубликати на същия материал. Би било страхотно, ако можем да съхраним всички анотации, които хората правят в книгите, като известните „драсканици в полетата“ на Ферма. Но уви, това ще остане мечта на архивиста."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>„Произведения“.</strong> Например „Хари Потър и Стаята на тайните“ като логическа концепция, обхващаща всички версии на нея, като различни преводи и преиздания. Това е вид полезна дефиниция, но може да бъде трудно да се определи какво се брои. Например, вероятно искаме да съхраним различни преводи, въпреки че преизданията с малки разлики може да не са толкова важни."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>„Издания“.</strong> Тук броите всяка уникална версия на книга. Ако нещо в нея е различно, като различна корица или различен предговор, това се брои за различно издание."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Файлове.</strong> Когато работите с библиотеки в сянка като Library Genesis, Sci-Hub или Z-Library, има допълнително съображение. Може да има множество сканирания на едно и също издание. И хората могат да правят по-добри версии на съществуващи файлове, като сканират текста с OCR или коригират страници, които са сканирани под ъгъл. Искаме да броим тези файлове само като едно издание, което би изисквало добро metadata или дедупликация чрез мерки за сходство на документи."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "„Издания“ изглежда най-практичната дефиниция за това какво са „книги“. Удобно, тази дефиниция също се използва за присвояване на уникални ISBN номера. ISBN, или Международен стандартен книжен номер, обикновено се използва за международна търговия, тъй като е интегриран с международната система за баркодове („Международен номер на артикул“). Ако искате да продавате книга в магазините, тя се нуждае от баркод, така че получавате ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Блог постът на Тайчър споменава, че докато ISBN са полезни, те не са универсални, тъй като наистина са приети едва в средата на седемдесетте години и не навсякъде по света. Все пак, ISBN вероятно е най-широко използваният идентификатор на книжни издания, така че това е нашата най-добра начална точка. Ако можем да намерим всички ISBN в света, получаваме полезен списък на книгите, които все още трябва да бъдат съхранени."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "И така, откъде да вземем данните? Има няколко съществуващи усилия, които се опитват да съставят списък на всички книги в света:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> В крайна сметка, те направиха това изследване за Google Books. Въпреки това, тяхната metadata не е достъпна в насипно състояние и е доста трудно да се извлече."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Както беше споменато по-рано, това е тяхната цяла мисия. Те са събрали огромни количества библиотечни данни от сътрудничещи библиотеки и национални архиви и продължават да го правят. Те също така имат доброволци библиотекари и технически екип, които се опитват да дедупликират записи и да ги маркират с всякакви metadata. Най-хубавото е, че техният dataset е напълно отворен. Можете просто да го <a %(openlibrary)s>изтеглите</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Това е уебсайт, управляван от неправителствената организация OCLC, която продава системи за управление на библиотеки. Те агрегират metadata за книги от много библиотеки и я правят достъпна чрез уебсайта на WorldCat. Въпреки това, те също така печелят пари, продавайки тези данни, така че не са достъпни за насипно изтегляне. Те имат някои по-ограничени насипни datasets, достъпни за изтегляне, в сътрудничество с конкретни библиотеки."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Това е темата на този блог пост. ISBNdb извлича данни от различни уебсайтове за metadata на книги, по-специално данни за цени, които след това продават на книжарници, за да могат да определят цените на книгите си в съответствие с останалата част от пазара. Тъй като ISBN са доста универсални в наши дни, те ефективно изградиха „уеб страница за всяка книга“."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Различни индивидуални библиотечни системи и архиви.</strong> Има библиотеки и архиви, които не са индексирани и агрегирани от никой от горепосочените, често защото са недофинансирани или по други причини не искат да споделят данните си с Open Library, OCLC, Google и т.н. Много от тях имат цифрови записи, достъпни чрез интернет, и често не са много добре защитени, така че ако искате да помогнете и да се забавлявате, учейки за странни библиотечни системи, това са страхотни начални точки."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "В този пост сме щастливи да обявим малко издание (в сравнение с предишните ни издания на Z-Library). Извлякохме по-голямата част от ISBNdb и направихме данните достъпни за торент на уебсайта на Pirate Library Mirror (РЕДАКЦИЯ: преместено в <a %(wikipedia_annas_archive)s>Архивът на Анна</a>; няма да го свързваме директно тук, просто го потърсете). Това са около 30.9 милиона записа (20GB като <a %(jsonlines)s>JSON Lines</a>; 4.4GB компресирани). На техния уебсайт те твърдят, че всъщност имат 32.6 милиона записа, така че може би по някакъв начин сме пропуснали някои, или <em>те</em> може да правят нещо грешно. Във всеки случай, засега няма да споделяме точно как го направихме — ще оставим това като упражнение за читателя. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Това, което ще споделим, е някакъв предварителен анализ, за да се опитаме да се приближим до оценката на броя на книгите в света. Разгледахме три datasets: този нов dataset на ISBNdb, нашето оригинално издание на metadata, което извлякохме от библиотеката в сянка Z-Library (която включва Library Genesis), и данните от Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Нека започнем с някои груби числа:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "В Z-Library/Libgen и Open Library има много повече книги, отколкото уникални ISBN. Това означава ли, че много от тези книги нямат ISBN, или просто липсва metadata за ISBN? Вероятно можем да отговорим на този въпрос с комбинация от автоматизирано съвпадение на базата на други атрибути (заглавие, автор, издател и т.н.), привличане на повече източници на данни и извличане на ISBN от самите сканирания на книги (в случая на Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Колко от тези ISBN са уникални? Това е най-добре илюстрирано с диаграма на Вен:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "За да бъдем по-точни:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Бяхме изненадани колко малко съвпадения има! ISBNdb има огромно количество ISBN, които не се появяват нито в Z-Library, нито в Open Library, и същото важи (в по-малка, но все пак значителна степен) за другите две. Това повдига много нови въпроси. Колко би помогнало автоматизираното съвпадение при маркирането на книгите, които не са маркирани с ISBN? Ще има ли много съвпадения и следователно увеличено припокриване? Също така, какво би се случило, ако добавим четвърти или пети набор от данни? Колко припокриване ще видим тогава?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Това ни дава начална точка. Сега можем да разгледаме всички ISBN, които не бяха в набора от данни на Z-Library и които не съвпадат с полетата за заглавие/автор. Това може да ни даде възможност да запазим всички книги в света: първо чрез събиране на сканирания от интернет, след това чрез излизане в реалния живот за сканиране на книги. Последното дори може да бъде финансирано от тълпата или подтикнато от „награди“ от хора, които биха искали да видят определени книги дигитализирани. Всичко това е история за друг път."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Ако искате да помогнете с някое от тези неща — по-нататъшен анализ; събиране на повече metadata; намиране на повече книги; OCR на книги; правене на това за други области (например статии, аудиокниги, филми, телевизионни предавания, списания) или дори предоставяне на част от тези данни за неща като ML / обучение на големи езикови модели — моля, свържете се с мен (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Ако сте специално заинтересовани от анализа на данни, работим върху това да направим нашите набори от данни и скриптове достъпни в по-лесен за използване формат. Би било чудесно, ако можете просто да клонирате тетрадка и да започнете да експериментирате с това."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Накрая, ако искате да подкрепите тази работа, моля, обмислете възможността да направите дарение. Това е изцяло доброволческа операция и вашият принос прави огромна разлика. Всяка помощ е от значение. Засега приемаме дарения в криптовалута; вижте страницата за дарения в Архива на Анна."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. За някакво разумно определение на „завинаги“. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Разбира се, писменото наследство на човечеството е много повече от книги, особено в днешно време. За целите на този пост и нашите последни издания се фокусираме върху книгите, но интересите ни се простират по-далеч."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Има много повече, което може да се каже за Аарон Суорц, но просто искахме да го споменем накратко, тъй като той играе ключова роля в тази история. С времето повече хора може да се натъкнат на името му за първи път и да се потопят в заешката дупка сами."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Критичният прозорец на библиотеките в сянка"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Как можем да твърдим, че ще запазим нашите колекции завинаги, когато те вече наближават 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Китайска версия 中文版</a>, обсъдете в <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "В Архива на Анна често ни питат как можем да твърдим, че ще запазим нашите колекции завинаги, когато общият размер вече наближава 1 петабайт (1000 TB) и продължава да расте. В тази статия ще разгледаме нашата философия и ще видим защо следващото десетилетие е критично за нашата мисия за запазване на знанието и културата на човечеството."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Общият размер</a> на нашите колекции през последните няколко месеца, разбит по брой на торент сийдъри."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Приоритети"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Защо ни е толкова грижа за статиите и книгите? Нека оставим настрана нашето основно убеждение в запазването като цяло — може би ще напишем друг пост за това. Така че защо статии и книги конкретно? Отговорът е прост: <strong>информационна плътност</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "На мегабайт съхранение, писменият текст съхранява най-много информация от всички медии. Докато ни е грижа както за знанието, така и за културата, повече ни е грижа за първото. Като цяло намираме йерархия на информационната плътност и важността на запазването, която изглежда приблизително така:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Академични статии, списания, доклади"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Органични данни като ДНК последователности, семена на растения или микробни проби"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Нехудожествени книги"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Научен и инженеринг софтуерен код"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Данни от измервания като научни измервания, икономически данни, корпоративни доклади"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Научни и инженеринг уебсайтове, онлайн дискусии"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Нехудожествени списания, вестници, ръководства"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Нехудожествени транскрипции на разговори, документални филми, подкасти"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Вътрешни данни от корпорации или правителства (изтичания)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Записи на metadata като цяло (на нехудожествени и художествени произведения; на други медии, изкуство, хора и т.н.; включително рецензии)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Географски данни (напр. карти, геоложки проучвания)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Транскрипции на правни или съдебни производства"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Художествени или развлекателни версии на всичко по-горе"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Класирането в този списък е донякъде произволно — няколко елемента са равни или има разногласия в нашия екип — и вероятно забравяме някои важни категории. Но това е приблизително как приоритизираме."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Някои от тези елементи са твърде различни от останалите, за да се тревожим за тях (или вече са поети от други институции), като органични данни или географски данни. Но повечето от елементите в този списък всъщност са важни за нас."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Друг голям фактор в нашата приоритизация е колко е застрашено дадено произведение. Предпочитаме да се фокусираме върху произведения, които са:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Редки"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Уникално недооценени"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Уникално застрашени от унищожение (напр. от война, съкращения на финансиране, съдебни дела или политическо преследване)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Накрая, ние се грижим за мащаба. Имаме ограничено време и пари, така че предпочитаме да прекараме месец, спасявайки 10,000 книги, отколкото 1,000 книги — ако са приблизително еднакво ценни и застрашени."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Библиотеки в сянка"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Има много организации, които имат подобни мисии и приоритети. Наистина, има библиотеки, архиви, лаборатории, музеи и други институции, натоварени с опазването от този вид. Много от тях са добре финансирани от правителства, индивиди или корпорации. Но те имат един огромен сляп петно: правната система."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Тук се крие уникалната роля на библиотеките в сянка и причината за съществуването на Архивът на Анна. Можем да правим неща, които другите институции не са позволени да правят. Сега, не е (често) така, че можем да архивираме материали, които са незаконни за съхранение другаде. Не, в много места е законно да се изгради архив с всякакви книги, статии, списания и т.н."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Но това, което често липсва на легалните архиви, е <strong>излишък и дълговечност</strong>. Съществуват книги, от които има само едно копие в някоя физическа библиотека някъде. Съществуват записи на metadata, пазени от една единствена корпорация. Съществуват вестници, запазени само на микрофилм в един единствен архив. Библиотеките могат да получат съкращения на финансирането, корпорациите могат да фалират, архивите могат да бъдат бомбардирани и изгорени до основи. Това не е хипотетично — това се случва постоянно."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Това, което можем да направим уникално в Архива на Анна, е да съхраняваме много копия на произведения в голям мащаб. Можем да събираме статии, книги, списания и още, и да ги разпространяваме на едро. В момента правим това чрез торенти, но точните технологии нямат значение и ще се променят с времето. Важната част е да се разпространят много копия по целия свят. Този цитат от преди повече от 200 години все още звучи вярно:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Изгубеното не може да бъде възстановено; но нека спасим това, което остава: не чрез сейфове и ключалки, които ги ограждат от общественото око и употреба, като ги предаваме на загубата на времето, а чрез такова умножаване на копия, което ще ги постави извън обсега на случайността.</q></em><br>— Томас Джеферсън, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Кратка бележка за общественото достояние. Тъй като Архивът на Анна се фокусира уникално върху дейности, които са незаконни на много места по света, не се занимаваме с широко достъпни колекции, като книги в общественото достояние. Правните субекти често вече се грижат добре за това. Въпреки това, има съображения, които понякога ни карат да работим върху публично достъпни колекции:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Записите на metadata могат да бъдат свободно разглеждани на уебсайта на Worldcat, но не могат да бъдат изтегляни на едро (докато не ги <a %(worldcat_scrape)s>изстъргахме</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Кодът може да бъде с отворен код в Github, но Github като цяло не може лесно да бъде огледан и следователно запазен (въпреки че в този конкретен случай има достатъчно разпространени копия на повечето хранилища с код)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit е свободен за използване, но наскоро въведе строги мерки срещу изстъргването, в отговор на гладните за данни LLM обучения (повече за това по-късно)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Умножаване на копията"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Обратно към първоначалния ни въпрос: как можем да твърдим, че ще запазим нашите колекции завинаги? Основният проблем тук е, че нашата колекция <a %(torrents_stats)s>расте</a> с бързи темпове, чрез изстъргване и отворен код на някои масивни колекции (в допълнение към невероятната работа, вече извършена от други библиотеки в сянка с отворени данни като Sci-Hub и Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Този растеж на данните прави по-трудно огледалното разпространение на колекциите по света. Съхранението на данни е скъпо! Но ние сме оптимисти, особено когато наблюдаваме следните три тенденции."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Обрахме ниско висящите плодове"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Това следва директно от нашите приоритети, обсъдени по-горе. Предпочитаме да работим върху освобождаването на големи колекции първо. Сега, когато сме осигурили някои от най-големите колекции в света, очакваме нашият растеж да бъде много по-бавен."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Все още има дълга опашка от по-малки колекции, и нови книги се сканират или публикуват всеки ден, но темпото вероятно ще бъде много по-бавно. Може да се удвоим или дори утроим по размер, но за по-дълъг период от време."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Разходите за съхранение продължават да падат експоненциално"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Към момента на писане, <a %(diskprices)s>цените на дисковете</a> на TB са около $12 за нови дискове, $8 за използвани дискове и $4 за лента. Ако сме консервативни и гледаме само нови дискове, това означава, че съхранението на петабайт струва около $12,000. Ако приемем, че нашата библиотека ще се утрои от 900TB до 2.7PB, това би означавало $32,400 за огледално разпространение на цялата ни библиотека. Добавяйки електричество, разходи за друго оборудване и т.н., нека го закръглим на $40,000. Или с лента повече като $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "От една страна <strong>$15,000–$40,000 за сумата на цялото човешко знание е изгодно</strong>. От друга страна, това е малко стръмно да се очаква тонове пълни копия, особено ако искаме тези хора да продължат да сеят своите торенти за ползата на другите."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Това е днес. Но прогресът върви напред:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Цените на твърдите дискове на TB са били приблизително намалени на трета през последните 10 години и вероятно ще продължат да падат със същото темпо. Лентата изглежда е на подобна траектория. Цените на SSD падат дори по-бързо и може да изпреварят цените на HDD до края на десетилетието."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Тенденции в цените на HDD от различни източници (кликнете, за да видите изследването)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Ако това се задържи, след 10 години може да гледаме само $5,000–$13,000 за огледално разпространение на цялата ни колекция (1/3), или дори по-малко, ако растем по-малко по размер. Въпреки че все още е много пари, това ще бъде постижимо за много хора. И може да бъде дори по-добре заради следващата точка…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Подобрения в информационната плътност"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "В момента съхраняваме книгите в суровите формати, в които ни се предоставят. Разбира се, те са компресирани, но често все още са големи сканирания или фотографии на страници."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Досега единствените опции за намаляване на общия размер на нашата колекция бяха чрез по-агресивна компресия или дублиране. Въпреки това, за да постигнем значителни спестявания, и двете са твърде загубни за нашия вкус. Силната компресия на снимки може да направи текста едва четим. А дублирането изисква висока увереност, че книгите са точно същите, което често е твърде неточно, особено ако съдържанието е същото, но сканиранията са направени по различни поводи."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Винаги е имало трета опция, но качеството ѝ е било толкова ужасно, че никога не сме я разглеждали: <strong>OCR, или Оптично разпознаване на символи</strong>. Това е процесът на преобразуване на снимки в обикновен текст, като се използва AI за разпознаване на символите в снимките. Инструменти за това съществуват отдавна и са доста добри, но „доста добри“ не е достатъчно за целите на съхранение."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Въпреки това, последните мултимодални модели за дълбоко обучение направиха изключително бърз напредък, макар и все още на високи разходи. Очакваме както точността, така и разходите да се подобрят драстично през следващите години, до степен, в която ще стане реалистично да се приложи към цялата ни библиотека."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Подобрения в OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Когато това се случи, вероятно ще запазим оригиналните файлове, но освен това бихме могли да имаме много по-малка версия на нашата библиотека, която повечето хора ще искат да огледалят. Основното е, че самият суров текст се компресира дори по-добре и е много по-лесен за дублиране, което ни дава още повече спестявания."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Като цяло не е нереалистично да очакваме поне 5-10 пъти намаление на общия размер на файловете, може би дори повече. Дори при консервативно намаление от 5 пъти, ще разглеждаме <strong>$1,000–$3,000 за 10 години, дори ако нашата библиотека се утрои по размер</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Критичен прозорец"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Ако тези прогнози са точни, ние <strong>просто трябва да изчакаме няколко години</strong>, преди цялата ни колекция да бъде широко огледална. Така, по думите на Томас Джеферсън, „поставена извън обсега на случайността“."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "За съжаление, появата на LLM и тяхното обучение, което изисква много данни, постави много притежатели на авторски права в отбранителна позиция. Още повече, отколкото вече бяха. Много уебсайтове правят по-трудно изтеглянето и архивирането, съдебни дела летят наоколо, а през това време физическите библиотеки и архиви продължават да бъдат пренебрегвани."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Можем само да очакваме тези тенденции да продължат да се влошават и много произведения да бъдат загубени много преди да влязат в общественото достояние."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Ние сме на прага на революция в съхранението, но <q>загубеното не може да бъде възстановено.</q></strong> Имаме критичен прозорец от около 5-10 години, през който все още е сравнително скъпо да се управлява библиотека в сянка и да се създават много огледала по света, и през който достъпът все още не е напълно затворен."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Ако можем да преодолеем този прозорец, тогава наистина ще сме съхранили знанието и културата на човечеството за вечни времена. Не трябва да позволяваме това време да бъде пропиляно. Не трябва да позволяваме този критичен прозорец да се затвори пред нас."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Да го направим."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Ексклузивен достъп за LLM компании до най-голямата колекция от китайски нехудожествени книги в света"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Китайска версия 中文版</a>, <a %(news_ycombinator)s>Обсъждане в Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Накратко:</strong> Архивът на Анна придоби уникална колекция от 7.5 милиона / 350TB китайски нехудожествени книги — по-голяма от Library Genesis. Готови сме да дадем на компания за LLM изключителен достъп, в замяна на висококачествено OCR и извличане на текст.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Това е кратък блог пост. Търсим компания или институция, която да ни помогне с OCR и извличане на текст за масивна колекция, която придобихме, в замяна на изключителен ранен достъп. След периода на ембарго, разбира се, ще пуснем цялата колекция."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Висококачественият академичен текст е изключително полезен за обучение на LLM. Въпреки че нашата колекция е китайска, това би трябвало да бъде полезно дори за обучение на английски LLM: моделите изглежда кодират концепции и знания независимо от изходния език."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "За това, текстът трябва да бъде извлечен от скановете. Какво получава Архивът на Анна от това? Пълнотекстово търсене на книгите за своите потребители."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Тъй като нашите цели съвпадат с тези на разработчиците на LLM, търсим сътрудник. Готови сме да ви дадем <strong>изключителен ранен достъп до тази колекция в насипно състояние за 1 година</strong>, ако можете да извършите правилно OCR и извличане на текст. Ако сте готови да споделите целия код на вашия процес с нас, бихме били готови да удължим ембаргото на колекцията."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Примерни страници"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "За да ни докажете, че имате добър процес, ето някои примерни страници, с които да започнете, от книга за свръхпроводници. Вашият процес трябва правилно да обработва математика, таблици, графики, бележки под линия и т.н."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Изпратете вашите обработени страници на нашия имейл. Ако изглеждат добре, ще ви изпратим още насаме и очакваме да можете бързо да изпълните вашия процес и на тях. След като сме доволни, можем да сключим сделка."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Колекция"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Малко повече информация за колекцията. <a %(duxiu)s>Duxiu</a> е огромна база данни от сканирани книги, създадена от <a %(chaoxing)s>SuperStar Digital Library Group</a>. Повечето са академични книги, сканирани, за да бъдат достъпни дигитално за университети и библиотеки. За нашата англоговоряща аудитория, <a %(library_princeton)s>Принстън</a> и <a %(guides_lib_uw)s>Университетът на Вашингтон</a> имат добри прегледи. Има и отлична статия, която дава повече информация: <a %(doi)s>„Цифровизация на китайски книги: Казус на търсачката SuperStar DuXiu Scholar“</a> (потърсете я в Архива на Анна)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Книгите от Duxiu отдавна са пиратствани в китайския интернет. Обикновено се продават за по-малко от долар от препродавачи. Обикновено се разпространяват чрез китайския еквивалент на Google Drive, който често е хакнат, за да позволи повече място за съхранение. Някои технически детайли могат да бъдат намерени <a %(github_duty_machine)s>тук</a> и <a %(github_821_github_io)s>тук</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Въпреки че книгите са били полу-публично разпространени, е доста трудно да се получат в насипно състояние. Това беше високо в нашия списък със задачи и отделихме няколко месеца пълно работно време за него. Въпреки това, наскоро невероятен, удивителен и талантлив доброволец се свърза с нас, казвайки ни, че вече е свършил цялата тази работа — на големи разходи. Те споделиха цялата колекция с нас, без да очакват нищо в замяна, освен гаранцията за дългосрочно съхранение. Наистина забележително. Те се съгласиха да поискат помощ по този начин, за да се OCR-ира колекцията."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Колекцията е 7,543,702 файла. Това е повече от нехудожествената част на Library Genesis (около 5.3 милиона). Общият размер на файловете е около 359TB (326TiB) в настоящата си форма."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Отворени сме за други предложения и идеи. Просто се свържете с нас. Разгледайте Архива на Анна за повече информация относно нашите колекции, усилията за съхранение и как можете да помогнете. Благодаря!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Предупреждение: тази публикация в блога е остаряла. Решихме, че IPFS все още не е готов за основно използване. Все още ще свързваме към файлове на IPFS от Архива на Анна, когато е възможно, но няма да го хостваме сами повече, нито препоръчваме на други да огледалят, използвайки IPFS. Моля, вижте нашата страница за торенти, ако искате да помогнете за запазването на нашата колекция."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Помогнете за разпространението на Z-Library в IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Как да управляваме сянкова библиотека: операции в Архива на Анна"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Няма <q>AWS за сянкови благотворителни организации,</q> така че как управляваме Архива на Анна?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Аз управлявам <a %(wikipedia_annas_archive)s>Архива на Анна</a>, най-голямата в света отворена и некомерсиална търсачка за <a %(wikipedia_shadow_library)s>библиотеки в сянка</a>, като Sci-Hub, Library Genesis и Z-Library. Нашата цел е да направим знанието и културата лесно достъпни и в крайна сметка да изградим общност от хора, които заедно архивират и съхраняват <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>всички книги в света</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "В тази статия ще покажа как управляваме този уебсайт и уникалните предизвикателства, които идват с управлението на уебсайт със съмнителен правен статус, тъй като няма „AWS за сенчести благотворителни организации“."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Също така разгледайте сестринската статия <a %(blog_how_to_become_a_pirate_archivist)s>Как да станете пиратски архивист</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Токени за иновации"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Нека започнем с нашия технологичен стек. Той е умишлено скучен. Използваме Flask, MariaDB и ElasticSearch. Това е буквално всичко. Търсенето е до голяма степен решен проблем и не възнамеряваме да го преоткриваме. Освен това трябва да изразходваме нашите <a %(mcfunley)s>иновационни токени</a> за нещо друго: да не бъдем свалени от властите."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Колко законен или незаконен е всъщност Архивът на Анна? Това зависи основно от правната юрисдикция. Повечето държави вярват в някаква форма на авторско право, което означава, че на хора или компании се предоставя изключителен монопол върху определени видове произведения за определен период от време. Между другото, в Архива на Анна вярваме, че въпреки че има някои ползи, като цяло авторското право е нетна загуба за обществото — но това е история за друг път."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Този изключителен монопол върху определени произведения означава, че е незаконно за всеки извън този монопол да разпространява директно тези произведения — включително и за нас. Но Архивът на Анна е търсачка, която не разпространява директно тези произведения (поне не на нашия уебсайт в откритата мрежа), така че би трябвало да сме в безопасност, нали? Не точно. В много юрисдикции не само е незаконно да се разпространяват защитени с авторски права произведения, но и да се свързва към места, които го правят. Класически пример за това е законът DMCA на Съединените щати."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Това е най-строгият край на спектъра. В другия край на спектъра теоретично може да има държави без никакви закони за авторското право, но такива наистина не съществуват. Почти всяка държава има някаква форма на закон за авторското право. Прилагането е различна история. Има много държави с правителства, които не се интересуват от прилагането на закона за авторското право. Има и държави между двете крайности, които забраняват разпространението на защитени с авторски права произведения, но не забраняват свързването към такива произведения."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Друго съображение е на ниво компания. Ако една компания оперира в юрисдикция, която не се интересува от авторското право, но самата компания не е готова да поеме никакъв риск, тогава те може да закрият вашия уебсайт веднага щом някой се оплаче за него."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Накрая, голямо съображение са плащанията. Тъй като трябва да останем анонимни, не можем да използваме традиционни методи за плащане. Това ни оставя с криптовалути, и само малка част от компаниите ги поддържат (има виртуални дебитни карти, платени с крипто, но те често не се приемат)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Архитектура на системата"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Да кажем, че сте намерили някои компании, които са готови да хостват вашия уебсайт, без да ви затварят — нека ги наречем „свободолюбиви доставчици“ 😄. Бързо ще откриете, че хостването на всичко с тях е доста скъпо, така че може да искате да намерите някои „евтини доставчици“ и да извършите действителното хостване там, проксирайки през свободолюбивите доставчици. Ако го направите правилно, евтините доставчици никога няма да знаят какво хоствате и никога няма да получат оплаквания."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "С всички тези доставчици съществува риск те да ви затворят така или иначе, така че също се нуждаете от излишък. Нуждаем се от това на всички нива на нашия стек."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Една донякъде свободолюбива компания, която се е поставила в интересна позиция, е Cloudflare. Те <a %(blog_cloudflare)s>твърдят</a>, че не са хостинг доставчик, а услуга, като интернет доставчик. Следователно те не подлежат на DMCA или други искания за премахване и препращат всички искания към вашия действителен хостинг доставчик. Те дори са стигнали до съда, за да защитят тази структура. Следователно можем да ги използваме като още един слой кеширане и защита."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare не приема анонимни плащания, така че можем да използваме само техния безплатен план. Това означава, че не можем да използваме техните функции за балансиране на натоварването или резервиране. Следователно <a %(annas_archive_l255)s>го внедрихме сами</a> на ниво домейн. При зареждане на страницата браузърът ще провери дали текущият домейн все още е наличен и ако не е, ще пренапише всички URL адреси към друг домейн. Тъй като Cloudflare кешира много страници, това означава, че потребителят може да попадне на нашия основен домейн, дори ако прокси сървърът е неактивен, и след това при следващото кликване да бъде прехвърлен на друг домейн."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Все още имаме и нормални оперативни проблеми, с които да се справяме, като наблюдение на здравето на сървъра, регистриране на грешки в бекенда и фронтенда и т.н. Нашата архитектура за резервиране позволява по-голяма устойчивост и в това отношение, например чрез стартиране на напълно различен набор от сървъри на един от домейните. Можем дори да стартираме по-стари версии на кода и datasets на този отделен домейн, в случай че критична грешка в основната версия остане незабелязана."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Можем също така да се предпазим от Cloudflare, като го премахнем от един от домейните, като този отделен домейн. Възможни са различни комбинации от тези идеи."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Инструменти"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Нека разгледаме какви инструменти използваме, за да постигнем всичко това. Това много се развива, докато се сблъскваме с нови проблеми и намираме нови решения."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Сървър на приложението: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Прокси сървър: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Управление на сървъра: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Разработка: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Статично хостване на Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Има някои решения, по които сме се колебали. Едно от тях е комуникацията между сървърите: използвахме Wireguard за това, но установихме, че понякога спира да предава данни или предава данни само в една посока. Това се случи с няколко различни настройки на Wireguard, които опитахме, като <a %(github_costela_wesher)s>wesher</a> и <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Опитахме също така тунелиране на портове през SSH, използвайки autossh и sshuttle, но се сблъскахме с <a %(github_sshuttle)s>проблеми там</a> (въпреки че все още не ми е ясно дали autossh страда от проблеми с TCP-over-TCP или не — просто ми се струва като нестабилно решение, но може би всъщност е наред?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Вместо това се върнахме към директни връзки между сървърите, скривайки, че сървърът работи на евтини доставчици, използвайки IP-филтриране с UFW. Това има недостатъка, че Docker не работи добре с UFW, освен ако не използвате <code>network_mode: \"host\"</code>. Всичко това е малко по-податливо на грешки, защото ще изложите сървъра си в интернет с малка грешна конфигурация. Може би трябва да се върнем към autossh — обратната връзка би била много полезна тук."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Също така сме се колебали между Varnish и Nginx. В момента предпочитаме Varnish, но той има своите особености и груби ръбове. Същото важи и за Checkmk: не го обичаме, но работи засега. Weblate е добре, но не е невероятно — понякога се страхувам, че ще загуби данните ми, когато се опитвам да го синхронизирам с нашето git хранилище. Flask е добър като цяло, но има някои странни особености, които са отнели много време за отстраняване на грешки, като конфигуриране на персонализирани домейни или проблеми с интеграцията му със SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Досега другите инструменти са били страхотни: нямаме сериозни оплаквания относно MariaDB, ElasticSearch, Gitlab, Zulip, Docker и Tor. Всички те са имали някои проблеми, но нищо прекалено сериозно или отнемащо време."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Заключение"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Беше интересно преживяване да научим как да настроим стабилен и устойчив търсач на сенчести библиотеки. Има много повече детайли, които да споделим в бъдещи публикации, така че нека знаем какво бихте искали да научите повече!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Както винаги, търсим дарения, за да подкрепим тази работа, така че не забравяйте да посетите страницата за дарения в Архива на Анна. Също така търсим други видове подкрепа, като грантове, дългосрочни спонсори, доставчици на високорискови плащания, може би дори (с вкус!) реклами. И ако искате да допринесете с вашето време и умения, винаги търсим разработчици, преводачи и т.н. Благодарим ви за интереса и подкрепата."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Анна и екипът (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Здравейте, аз съм Анна. Създадох <a %(wikipedia_annas_archive)s>Архива на Анна</a>, най-голямата сенчеста библиотека в света. Това е моят личен блог, в който аз и моите съотборници пишем за пиратство, дигитално съхранение и други теми."

#, fuzzy
msgid "blog.index.text2"
msgstr "Свържете се с мен в <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Имайте предвид, че този уебсайт е просто блог. Тук хостваме само нашите собствени думи. Няма хоствани или свързани торенти или други защитени с авторски права файлове."

#, fuzzy
msgid "blog.index.heading"
msgstr "Блог публикации"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat извличане"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Поставяне на 5,998,794 книги в IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Предупреждение: тази публикация в блога е остаряла. Решихме, че IPFS все още не е готов за основно използване. Все още ще свързваме към файлове на IPFS от Архива на Анна, когато е възможно, но няма да го хостваме сами повече, нито препоръчваме на други да огледалят, използвайки IPFS. Моля, вижте нашата страница за торенти, ако искате да помогнете за запазването на нашата колекция."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Накратко:</strong> Архивът на Анна изстърга целия WorldCat (най-голямата в света колекция от библиотечни metadata), за да създаде списък със задачи на книги, които трябва да бъдат запазени.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Преди година <a %(blog)s>се заехме</a> да отговорим на този въпрос: <strong>Какъв процент от книгите са били трайно запазени от библиотеки в сянка?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "След като една книга попадне в библиотека в сянка с отворени данни като <a %(wikipedia_library_genesis)s>Library Genesis</a>, а сега и <a %(wikipedia_annas_archive)s>Архивът на Анна</a>, тя се огледаля по целия свят (чрез торенти), като по този начин практически се запазва завинаги."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "За да отговорим на въпроса какъв процент от книгите е запазен, трябва да знаем знаменателя: колко книги съществуват общо? Идеално би било да имаме не просто число, а действителни metadata. Тогава можем не само да ги съпоставим с библиотеките в сянка, но и <strong>да създадем списък със задачи за останалите книги, които да бъдат запазени!</strong> Можем дори да започнем да мечтаем за усилие от страна на общността да премине през този списък със задачи."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Извлякохме данни от <a %(wikipedia_isbndb_com)s>ISBNdb</a> и изтеглихме <a %(openlibrary)s>Open Library dataset</a>, но резултатите бяха незадоволителни. Основният проблем беше, че нямаше много припокриване на ISBN. Вижте тази диаграма на Вен от <a %(blog)s>нашия блог пост</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Бяхме много изненадани от това колко малко припокриване имаше между ISBNdb и Open Library, и двете от които щедро включват данни от различни източници, като уеб скрейпинг и библиотечни записи. Ако и двете вършат добра работа в намирането на повечето ISBN, техните кръгове със сигурност биха имали значително припокриване или единият би бил подмножество на другия. Това ни накара да се запитаме, колко книги попадат <em>напълно извън тези кръгове</em>? Нуждаем се от по-голяма база данни."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Тогава насочихме вниманието си към най-голямата база данни за книги в света: <a %(wikipedia_worldcat)s>WorldCat</a>. Това е собствена база данни на неправителствената организация <a %(wikipedia_oclc)s>OCLC</a>, която събира metadata записи от библиотеки по целия свят, в замяна на предоставяне на тези библиотеки достъп до пълния набор от данни и показването им в резултатите от търсенето на крайните потребители."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Въпреки че OCLC е неправителствена организация, техният бизнес модел изисква защита на тяхната база данни. Е, съжаляваме да кажем, приятели от OCLC, ние ще я раздадем на всички. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "През изминалата година внимателно извлякохме всички записи от WorldCat. В началото имахме късмет. WorldCat тъкмо пускаше пълния редизайн на своя уебсайт (през август 2022 г.). Това включваше значителна преработка на техните бекенд системи, въвеждайки много пропуски в сигурността. Веднага се възползвахме от възможността и успяхме да извлечем стотици милиони (!) записи само за няколко дни."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat редизайн</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "След това пропуските в сигурността бяха бавно поправяни един по един, докато последният, който намерихме, беше закърпен преди около месец. До този момент имахме почти всички записи и се стремяхме само към малко по-висококачествени записи. Така че почувствахме, че е време да ги пуснем!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Нека разгледаме някои основни данни за информацията:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Формат?</strong> <a %(blog)s>Контейнери на Архива на Анна (AAC)</a>, които по същество са <a %(jsonlines)s>JSON Lines</a>, компресирани със <a %(zstd)s>Zstandard</a>, плюс някои стандартизирани семантики. Тези контейнери обхващат различни видове записи, базирани на различните извлечения, които сме използвали."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Данни"

msgid "dyn.buy_membership.error.unknown"
msgstr "Възникна непозната грешка. Свържете се с нас на %(email)s с екранна снимка."

msgid "dyn.buy_membership.error.minimum"
msgstr "Тази монета има по-висока стойност от обичайния минимум. Моля, изберете различна продължителност или друга монета."

msgid "dyn.buy_membership.error.try_again"
msgstr "Заявката не можа да бъде изпълнена. Опитайте отново след няколко минути и ако това продължи да се случва, свържете се с нас на %(email)s с екранна снимка."

msgid "dyn.buy_membership.error.wait"
msgstr "Грешка при обработката на плащането. Моля, изчакайте малко и опитайте отново. Ако проблемът продължава повече от 24 часа, моля, свържете се с нас на %(email)s с екранна снимка."

msgid "page.comments.hidden_comment"
msgstr "скрит коментар"

msgid "page.comments.file_issue"
msgstr "Проблем с файла: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "По-добра версия"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Искате ли да докладвате този потребител за злоупотреба или неподходящо поведение?"

msgid "page.comments.report_abuse"
msgstr "Докладване на злоупотреба"

msgid "page.comments.abuse_reported"
msgstr "Злоупотребата е докладвана:"

msgid "page.comments.reported_abuse_this_user"
msgstr "Вие докладвахте този потребител за злоупотреба."

msgid "page.comments.reply_button"
msgstr "Отговор"

msgid "page.md5.quality.logged_out_login"
msgstr "Моля, <a %(a_login)s>влезте в системата</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Оставихте коментар. Може да отнеме минута, докато се покаже."

msgid "page.md5.quality.comment_error"
msgstr "Нещо се обърка. Моля, презаредете страницата и опитайте отново."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s засегнати страници"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Не е видимо в Libgen.rs Нехудожествена литература"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Не е видимо в Libgen.rs Художествена литература"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Не е видимо в Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Маркирано като развалено в Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Липсва в Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Отбелязано като “спам” в Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Отбелязано като “лош файл” в Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Не всички страници могат да бъдат конвертирани в PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Неуспешно изпълнение на exiftool върху този файл"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Книга (непозната)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Книга (нехудожествена)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Книга (художествена)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Журнална статия"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Стандарти"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Списание"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Комикс"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Музикални точки"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Аудиокнига"

msgid "common.md5_content_type_mapping.other"
msgstr "Други"

msgid "common.access_types_mapping.aa_download"
msgstr "Изтегляне от партньорски сървър"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Външно изтегляне"

msgid "common.access_types_mapping.external_borrow"
msgstr "Външен заем"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Външно заемане (печатът е деактивиран)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Разгледайте метаданните"

msgid "common.access_types_mapping.torrents_available"
msgstr "Съдържа се в торенти"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library - най-голямата библиотека с електронни книги в света"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb-събира данни от стотици библиотеки, издатели, търговци и други източници по целия свят, които могат да се търсят по ISBN, заглавие, автор или издател"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary - е библиотечен каталог, изграждащ уеб страница за всяка книга, публикувана някога"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub - библиотека в сянка, с безплатен достъп до милиони научни статии, включително и платени"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat) - е най-изчерпателната в света база данни с информация за библиотечните колекции"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀 китайски сайт за книги"

msgid "common.record_sources_mapping.uploads"
msgstr "Качвания в AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Чешки метаданни"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Книги"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Руска държавна библиотека"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Заглавие"

msgid "common.specific_search_fields.author"
msgstr "Автор"

msgid "common.specific_search_fields.publisher"
msgstr "Издател"

msgid "common.specific_search_fields.edition_varia"
msgstr "Издание"

msgid "common.specific_search_fields.year"
msgstr "Година на публикуване"

msgid "common.specific_search_fields.original_filename"
msgstr "Оригиналното име на файла"

msgid "common.specific_search_fields.description_comments"
msgstr "Коментари за описание и метаданни"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Изтеглянията чрез партньорския сървър временно не са достъпни за този файл."

msgid "common.md5.servers.fast_partner"
msgstr "Върз сървър в партньорство с нас №%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(препоръчително)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(без проверка на браузъра или списъци на чакащи)"

msgid "common.md5.servers.slow_partner"
msgstr "Бавен сървър в партньорство с нас №%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(малко по-бързо, но с листа на изчакване)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(няма списък на чакащите, но може да бъде много бавно)"

msgid "page.md5.box.download.scihub"
msgstr "свободен и неограничен достъп до цялото научно знание Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Нехудожествена"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Художествена"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(също натиснете “GET” горе)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(натиснете “GET” горе)"

msgid "page.md5.box.download.libgen_ads"
msgstr "техните реклами са известни с това, че съдържат злонамерен софтуер, затова използвайте блокер на реклами или не кликвайте върху реклами"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Файловете на Nexus/STC могат да бъдат ненадеждни за изтегляне)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library TOR"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(изисква TOR браузър)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Вземете назаем от интернет архива"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(само за поддръжници с деактивиран печат)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(свързаният идентификатор на дигитален обект може да не е наличен в Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "колекция"

msgid "page.md5.box.download.torrent"
msgstr "торент"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Групово изтегляне на торенти"

msgid "page.md5.box.download.experts_only"
msgstr "(само за експерти)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Потърсете с ISBN в архива на Анна"

msgid "page.md5.box.download.other_isbn"
msgstr "Търсете в различни бази данни с ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Намерете първоначалния запис в ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Потърсете в архива на Ана за Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Намерете първоначалния запис в Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Потърсете в архива на Анна номера на OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Намерете оригинален запис в WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Потърсете в архива на Anna SSID номер на DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Търсете ръчно в DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Потърсете в архива на Анна номер на CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Намерете оригиналния запис в CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Търсене в Архива на Анна за DuXiu DXID номер"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Ана Архив 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(не се изисква проверка на браузъра)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Чешки метаданни %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Метаданни"

msgid "page.md5.box.descr_title"
msgstr "описание"

msgid "page.md5.box.alternative_filename"
msgstr "Алтернативно име на файл"

msgid "page.md5.box.alternative_title"
msgstr "Алтернативно заглавие"

msgid "page.md5.box.alternative_author"
msgstr "Алтернативен автор"

msgid "page.md5.box.alternative_publisher"
msgstr "Алтернативен издател"

msgid "page.md5.box.alternative_edition"
msgstr "Алтернативно издание"

msgid "page.md5.box.alternative_extension"
msgstr "Алтернативно разширение"

msgid "page.md5.box.metadata_comments_title"
msgstr "коментари за метаданни"

msgid "page.md5.box.alternative_description"
msgstr "Алтернативно описание"

msgid "page.md5.box.date_open_sourced_title"
msgstr "данни с отворен код"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub файл “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Файл за контролирано цифрово заемане на интернет архив “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Това е запис на файл от Интернет архива, а не файл за директно изтегляне. Можете да опитате да заемете книгата (връзка по-долу) или да използвате този адрес, когато <a %(a_request)s>искате файла</a>."

msgid "page.md5.header.consider_upload"
msgstr "Ако имате този файл и той все още не е наличен в Архива на Анна, обмислете <a %(a_request)s>да го качите</a>."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s запис на метаданни"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s запис на метаданни"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) номер %(id)s запис на метаданни"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s метаданни запис"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s метаданни запис"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s метаданни запис"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s метаданни запис"

msgid "page.md5.header.meta_desc"
msgstr "Това е запис на метаданни, а не файл за изтегляне. Можете да използвате този адрес, когато <a %(a_request)s>искате файла</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Метаданни от свързан запис"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Подобрете метаданните в Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Предупреждение: множество свързани записи:"

msgid "page.md5.header.improve_metadata"
msgstr "Подобряване на метаданни"

msgid "page.md5.text.report_quality"
msgstr "Докладвайте качеството на файла"

msgid "page.search.results.download_time"
msgstr "Време за изтегляне"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Сайт:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Търсене в Архива на Анна за “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Изследовател на кодове:"

msgid "page.md5.codes.code_search"
msgstr "Преглед в Codes Explorer “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Прочетете още…"

msgid "page.md5.tabs.downloads"
msgstr "Изтегляния (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Взимам на заем (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Разглеждане на метаданните (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Коментари (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Списъци (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Статистика (%(count)s)"

msgid "common.tech_details"
msgstr "Покажи техническите детайли (на Английски)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Този файл може да има проблеми и е скрит от библиотека източник.</span> Понякога това е по искане на притежател на авторски права, понякога е защото е налична по-добра алтернатива, но понякога се дължи на проблем със самия файл. Все още може да е добре за изтегляне, но препоръчваме първо да потърсите алтернативен файл. Повече информация:"

msgid "page.md5.box.download.better_file"
msgstr "По-добра версия на този файл може да е достъпна на %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Ако все пак искате да изтеглите този файл, уверете се, че използвате само надежден, актуализиран софтуер, за да го отворите."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Бързи изтегляния"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Станете <a %(a_membership)s>член</a>, за да подкрепите дългосрочното съхранение на книги, документи и др. В знак на благодарност за Вашата подкрепа получавате бързи изтегляния. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Ако дарите този месец, получавате <strong>двойно</strong> повече бързи изтегляния."

msgid "page.md5.box.download.header_fast_member"
msgstr "Остават ви %(remaining)s за днес. Благодарим Ви, че сте член! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Изчерпали сте бързите изтегляния за днес."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Изтеглили сте този файл наскоро. Връзките остават валидни за известно време."

msgid "page.md5.box.download.option"
msgstr "Опция №%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(без пренасочване)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(отвори във визуализатор)"

msgid "layout.index.header.banner.refer"
msgstr "Препоръчайте приятел и двамата ще получите %(percentage)s%% бонус бързи изтегляния!"

msgid "layout.index.header.learn_more"
msgstr "Научете повече…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Бавни изтегляния"

msgid "page.md5.box.download.trusted_partners"
msgstr "От доверени партньори."

msgid "page.md5.box.download.slow_faq"
msgstr "Повече информация в <a %(a_slow)s>ЧЗВ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(може да изисква <a %(a_browser)s>проверка на браузъра</a> - неограничени изтегляния!)"

msgid "page.md5.box.download.after_downloading"
msgstr "След изтегляне:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Отвори в нашия визуализатор"

msgid "page.md5.box.external_downloads"
msgstr "покажи външни изтегляния"

msgid "page.md5.box.download.header_external"
msgstr "Външни изтегляния"

msgid "page.md5.box.download.no_found"
msgstr "Няма намерени изтегляния."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Всички сървъри огледало обслужват един и същ файл и трябва да са безопасни за използване. Въпреки това винаги бъдете внимателни, когато изтегляте файлове от интернет. Например, не забравяйте да актуализирате устройствата си."

msgid "page.md5.box.download.dl_managers"
msgstr "За големи файлове препоръчваме използването на мениджър за изтегляне, за да се предотвратят прекъсвания."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Препоръчани мениджъри за изтегляне: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Ще ви е необходим четец за електронни книги или PDF, за да отворите файла, в зависимост от формата на файла."

msgid "page.md5.box.download.readers.links"
msgstr "Препоръчани четци за електронни книги: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Онлайн визуализатор на Архива на Анна"

msgid "page.md5.box.download.conversion"
msgstr "Използвайте онлайн инструменти за конвертиране между формати."

msgid "page.md5.box.download.conversion.links"
msgstr "Препоръчани инструменти за конвертиране: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Можете да изпращате както PDF, така и EPUB файлове на вашия Kindle или Kobo eReader."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Препоръчани инструменти: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon‘s “Send to Kindle”"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz‘s “Send to Kobo/Kindle”"

msgid "page.md5.box.download.support"
msgstr "Подкрепете авторите и библиотеките"

msgid "page.md5.box.download.support.authors"
msgstr "Ако това ви харесва и можете да си го позволите, обмислете да закупите оригинала или да подкрепите авторите директно."

msgid "page.md5.box.download.support.libraries"
msgstr "Ако това е налично във вашата местна библиотека, обмислете да го заемете безплатно оттам."

msgid "page.md5.quality.header"
msgstr "Качество на файла"

msgid "page.md5.quality.report"
msgstr "Помогнете на общността, като докладвате качеството на този файл! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Докладвайте проблем с файла (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Отлично качество на файла (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Добавете коментар (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Какво не е наред с този файл?"

msgid "page.md5.quality.copyright"
msgstr "Моля, използвайте <a %(a_copyright)s>формуляра за искове по DMCA / авторски права</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Опишете проблема (задължително)"

msgid "page.md5.quality.issue_description"
msgstr "Описание на проблема"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 на по-добра версия на този файл (ако е приложимо)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Попълнете това, ако има друг файл, който много прилича на този файл (същото издание, същото разширение на файла, ако можете да намерите такъв), който хората трябва да използват вместо този файл. Ако знаете за по-добра версия на този файл извън Архива на Анна, тогава моля <a %(a_upload)s>качете го</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Можете да получите md5 от адреса, напр."

msgid "page.md5.quality.submit_report"
msgstr "Изпратете доклада"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Научете как да <a %(a_metadata)s>подобрите метаданните</a> за този файл сами."

msgid "page.md5.quality.report_thanks"
msgstr "Благодарим ви, че подадохте вашия доклад. Той ще бъде показан на тази страница, както и прегледан ръчно от Анна (докато не въведем подходяща система за модерация)."

msgid "page.md5.quality.report_error"
msgstr "Нещо се обърка. Моля, презаредете страницата и опитайте отново."

msgid "page.md5.quality.great.summary"
msgstr "Ако този файл е с високо качество, можете да обсъдите всичко за него тук! Ако не, моля използвайте бутона “Докладвай проблем с файла”."

msgid "page.md5.quality.loved_the_book"
msgstr "Обожавам тази книга!"

msgid "page.md5.quality.submit_comment"
msgstr "Оставете коментар"

msgid "common.english_only"
msgstr "Текстът долу е само на Английски."

msgid "page.md5.text.stats.total_downloads"
msgstr "Общо изтегляния: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "“MD5 на файл” е хеш, който се изчислява от съдържанието на файла и е уникален въз основа на това съдържание. Всички сенчести библиотеки, които сме индексирали тук, основно използват MD5 за идентифициране на файлове."

msgid "page.md5.text.md5_info.text2"
msgstr "Един файл може да се появи в множество сенчести библиотеки. За информация относно различните datasets, които сме компилирали, вижте <a %(a_datasets)s>страницата с Datasets</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Това е файл, управляван от библиотеката <a %(a_ia)s>IA’s Controlled Digital Lending</a> и индексиран от Архива на Анна за търсене. За информация относно различните datasets, които сме компилирали, вижте <a %(a_datasets)s>страницата с Datasets</a>."

msgid "page.md5.text.file_info.text1"
msgstr "За информация относно този конкретен файл, разгледайте неговия <a %(a_href)s>JSON файл</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Проблем при зареждане на тази страница"

msgid "page.aarecord_issue.text"
msgstr "Моля, обновете страницата, за да опитате отново. <a %(a_contact)s>Свържете се с нас</a>, ако проблемът продължава няколко часа."

msgid "page.md5.invalid.header"
msgstr "Не е намерено"

msgid "page.md5.invalid.text"
msgstr "„%(md5_input)s“ не беше намерен в нашата база данни."

msgid "page.login.title"
msgstr "Вход / Регистрация"

msgid "page.browserverification.header"
msgstr "Проверка на браузъра"

msgid "page.login.text1"
msgstr "За да предотвратим създаването на много акаунти от спамботове, първо трябва да проверим вашия браузър."

msgid "page.login.text2"
msgstr "Ако попаднете в безкраен цикъл, препоръчваме да инсталирате <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Може също да помогне да изключите рекламните блокери и други разширения на браузъра."

msgid "page.codes.title"
msgstr "Кодове"

msgid "page.codes.heading"
msgstr "Изследовател на кодове"

#, fuzzy
msgid "page.codes.intro"
msgstr "Разгледайте кодовете, с които записите са маркирани, по префикс. Колоната “записи” показва броя на записите, маркирани с кодове с дадения префикс, както се вижда в търсачката (включително записите само с метаданни). Колоната “кодове” показва колко действителни кода имат даден префикс."

msgid "page.codes.why_cloudflare"
msgstr "Тази страница може да отнеме време за да се генерира, поради което изисква Cloudflare captcha. <a %(a_donate)s>Членовете</a> могат да пропуснат captcha."

msgid "page.codes.dont_scrape"
msgstr "Моля, не изтъргвайте тези страници. Вместо това, препоръчваме <a %(a_import)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB и изпълнение на нашия <a %(a_software)s>отворен код</a>. Суровите данни могат да бъдат ръчно изследвани чрез JSON файлове като <a %(a_json_file)s>този</a>."

msgid "page.codes.prefix"
msgstr "Префикс"

msgid "common.form.go"
msgstr "Отиди"

msgid "common.form.reset"
msgstr "Нулиране"

msgid "page.codes.search_archive_start"
msgstr "Търсене в Архива на Анна"

msgid "page.codes.bad_unicode"
msgstr "Предупреждение: кодът съдържа неправилни Unicode символи и може да се държи неправилно в различни ситуации. Суровият двоичен код може да бъде декодиран от base64 представянето в URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Познат кодов префикс “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Префикс"

msgid "page.codes.code_label"
msgstr "Етикет"

msgid "page.codes.code_description"
msgstr "Описание"

msgid "page.codes.code_url"
msgstr "URL за конкретен код"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” ще бъде заменено със стойността на кода"

msgid "page.codes.generic_url"
msgstr "Общ URL"

msgid "page.codes.code_website"
msgstr "Сайт"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s запис съвпадащ с “%(prefix_label)s”"
msgstr[1] "%(count)s записа съвпадащи с “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL за конкретен код: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Още…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Кодове започващи с “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Индекс на"

msgid "page.codes.records_prefix"
msgstr "записи"

msgid "page.codes.records_codes"
msgstr "кодове"

msgid "page.codes.fewer_than"
msgstr "По-малко от %(count)s записа"

msgid "page.contact.dmca.form"
msgstr "За DMCA / искове за авторски права, използвайте <a %(a_copyright)s>тази форма</a>."

msgid "page.contact.dmca.delete"
msgstr "Всички други начини за контакт с нас относно претенции за авторски права ще бъдат автоматично изтрити."

msgid "page.contact.checkboxes.text1"
msgstr "Много ще се радваме на вашите отзиви и въпроси!"

msgid "page.contact.checkboxes.text2"
msgstr "Въпреки това, поради количеството спам и безсмислени имейли, които получаваме, моля, отметнете кутиите, за да потвърдите, че разбирате тези условия за контакт с нас."

msgid "page.contact.checkboxes.copyright"
msgstr "Претенциите за авторски права към този имейл ще бъдат игнорирани; използвайте формуляра вместо това."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Партньорските сървъри са недостъпни поради затваряне на хостинг услугите. Те трябва да бъдат скоро отново активни ."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Членствата ще бъдат удължени съответно."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Не ни изпращайте имейл до <a %(a_request)s>заявка за книги</a><br>или малък (<10k) <a %(a_upload)s>качвания</a>."

msgid "page.donate.please_include"
msgstr "Когато задавате въпроси за акаунт или дарения, добавете вашия акаунт ID, екранни снимки, разписки, колкото се може повече информация. Проверяваме имейла си само на всеки 1-2 седмици, така че липсата на тази информация ще забави всяко решение."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Показване на имейл"

msgid "page.copyright.title"
msgstr "Формуляр за искове по DMCA / Авторски права"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Ако имате иск по DMCA или друг иск за авторски права, моля, попълнете този формуляр възможно най-точно. Ако срещнете някакви проблеми, моля, свържете се с нас на специалния ни адрес за DMCA: %(email)s. Обърнете внимание, че искове, изпратени на този адрес по имейл, няма да бъдат обработвани, той е само за въпроси. Моля, използвайте формуляра по-долу, за да подадете своите искове."

msgid "page.copyright.form.aa_urls"
msgstr "Адреси в Архива на Анна (задължително). По един на ред. Моля, включвайте само адреси, които описват точно същото издание на книга. Ако искате да подадете иск за множество книги или множество издания, моля, подайте този формуляр няколко пъти."

msgid "page.copyright.form.aa_urls.note"
msgstr "Искове, които обединяват множество книги или издания, ще бъдат отхвърлени."

msgid "page.copyright.form.name"
msgstr "Вашето име (задължително)"

msgid "page.copyright.form.address"
msgstr "Адрес (задължително)"

msgid "page.copyright.form.phone"
msgstr "Телефонен номер (задължително)"

msgid "page.copyright.form.email"
msgstr "Имейл (задължително)"

msgid "page.copyright.form.description"
msgstr "Ясно описание на изходния материал (задължително)"

msgid "page.copyright.form.isbns"
msgstr "ISBN номера на изходния материал (ако е приложимо). По един на ред. Моля, включвайте само тези, които точно съвпадат с изданието, за което подавате иск за авторските права."

msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> адреси на изходния материал, по един на ред. Моля, отделете момент, за да потърсите изходния си материал в Open Library. Това ще ни помогне да потвърдим вашия иск."

msgid "page.copyright.form.external_urls"
msgstr "Адреси на изходния материал, по един на ред (задължително). Моля, включвайте колкото се може повече, за да ни помогнете да потвърдим вашия иск (напр. Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Декларация и подпис (задължително)"

msgid "page.copyright.form.submit_claim"
msgstr "Подайте иск"

msgid "page.copyright.form.on_success"
msgstr "✅ Благодарим ви, че подадохте своя иск за авторски права. Ще го прегледаме възможно най-скоро. Моля, презаредете страницата, за да подадете нов иск."

msgid "page.copyright.form.on_failure"
msgstr "❌ Нещо се обърка. Моля, презаредете страницата и опитайте отново."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "Ако се интересувате от огледално копиране на този набор от данни за <a %(a_archival)s>архивиране</a> или за <a %(a_llm)s>обучение на LLM</a>, моля, свържете се с нас."

msgid "page.datasets.intro.text2"
msgstr "Нашата мисия е да архивираме всички книги в света (както и статии, списания и др.) и да ги направим широко достъпни. Вярваме, че всички книги трябва да бъдат огледално копирани широко, за да се осигури излишък и устойчивост. Затова събираме файлове от различни източници. Някои източници са напълно отворени и могат да бъдат огледално копирани на едро (като Sci-Hub). Други са затворени и защитени, затова се опитваме да ги изстържем, за да “освободим” техните книги. Трети попадат някъде по средата."

msgid "page.datasets.intro.text3"
msgstr "Всички наши данни могат да бъдат <a %(a_torrents)s>торентирани</a>, а всички наши метаданни могат да бъдат <a %(a_anna_software)s>генерирани</a> или <a %(a_elasticsearch)s>изтеглени</a> като бази данни ElasticSearch и MariaDB. Суровите данни могат да бъдат ръчно разгледани чрез JSON файлове като <a %(a_dbrecord)s>този</a>."

msgid "page.datasets.overview.title"
msgstr "Преглед"

msgid "page.datasets.overview.text1"
msgstr "По-долу е кратък преглед на източниците на файловете в Архива на Анна."

msgid "page.datasets.overview.source.header"
msgstr "Източник"

msgid "page.datasets.overview.size.header"
msgstr "Размер"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% огледани от AA / налични торенти"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Проценти на броя на файловете"

msgid "page.datasets.overview.last_updated.header"
msgstr "Последно обновено"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Нехудожествена и Художествена литература"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s файл"
msgstr[1] "%(count)s файлове"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Чрез Libgen.li “scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: замразен от 2021 г.; повечето налични чрез торенти"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: малки допълнения оттогава</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Изключвайки “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Торентите за художествена литература изостават (въпреки че ID-та ~4-6M не са торентирани, тъй като се припокриват с нашите Zlib торенти)."

msgid "page.datasets.zlibzh.searchable"
msgstr "Колекцията “Chinese” в Z-Library изглежда същата като нашата колекция DuXiu, но с различни MD5. Изключваме тези файлове от торентите, за да избегнем дублиране, но все пак ги показваме в нашия индекс за търсене."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA Контролирано цифрово заемане"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ от файловете са с възможност за търсене."

msgid "page.datasets.overview.total"
msgstr "Общо"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Без дубликати"

msgid "page.datasets.overview.text4"
msgstr "Тъй като сенчестите библиотеки често синхронизират данни една от друга, има значително припокриване между библиотеките. Затова числата не се събират до общата сума."

msgid "page.datasets.overview.text5"
msgstr "Процентът “огледани и споделени файл(ове) от Архива на Анна” показва колко огледални файла имаме сами. Споделяме тези файлове на едро чрез торенти и ги правим достъпни за директно изтегляне чрез партньорски сайтове."

msgid "page.datasets.source_libraries.title"
msgstr "Библиотеки с изходни кодове"

msgid "page.datasets.source_libraries.text1"
msgstr "Някои библиотеки с изходни кодове насърчават масовото споделяне на своите данни чрез торенти, докато други не споделят лесно своите колекции. В последния случай, Архивът на Анна се опитва да извлече техните колекции и да ги направи достъпни (вижте нашата страница <a %(a_torrents)s>Торенти</a>). Има и междинни ситуации, например, когато библиотеки с изходни кодове са готови да споделят, но нямат ресурсите за това. В тези случаи, също се опитваме да помогнем."

msgid "page.datasets.source_libraries.text2"
msgstr "По-долу е представен преглед на това как взаимодействаме с различните библиотеки с изходни кодове."

msgid "page.datasets.sources.source.header"
msgstr "Източник"

msgid "page.datasets.sources.files.header"
msgstr "Файлове"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Ежедневни <a %(dbdumps)s>HTTP пълни копия на база данни</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Автоматизирани торенти за <a %(nonfiction)s>Нехудожествена литература</a> и <a %(fiction)s>Художествена литература</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(covers)s>торенти с корици на книги</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub е замразил нови файлове от 2021 г."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Метаданни налични <a %(scihub1)s>тук</a> и <a %(scihub2)s>тук</a>, както и като част от <a %(libgenli)s>Libgen.li базата данни</a> (която използваме)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Налични торенти с данни <a %(scihub1)s>тук</a>, <a %(scihub2)s>тук</a>, и <a %(libgenli)s>тук</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Някои нови файлове <a %(libgenrs)s>се</a> <a %(libgenli)s>добавят</a> към “scimag” на Libgen, но не е достатъчно, за да се гарантират нови торенти"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Тримесечни <a %(dbdumps)s>HTTP копия на база данни</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Торентите за нехудожествена литература се споделят с Libgen.rs (и са огледални <a %(libgenli)s>тук</a>)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Архивът на Анна и Libgen.li съвместно управляват колекции от <a %(comics)s>комикси</a>, <a %(magazines)s>списания</a>, <a %(standarts)s>стандартни документи</a>, и <a %(fiction)s>художествена литература (отделена от Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Тяхната колекция “fiction_rus” (руска художествена литература) няма специални торенти, но е осигурила торенти от другаде, и ние поддържаме <a %(fiction_rus)s>тяхно огледало</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Архивът на Анна и Z-Library съвместно управляват колекция от <a %(metadata)s>метаданни на Z-Library</a> и <a %(files)s>файлове на Z-Library</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Някои метаданни са налични чрез <a %(openlib)s>бази данни на Open Library</a>, но те не покриват цялата колекция на IA"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Няма лесно достъпни метаданни за цялата им колекция"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(ia)s>метаданни на IA</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Файловете са налични само за заемане на ограничена основа, с различни ограничения за достъп"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(ia)s>файлове на IA</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Различни бази данни с метаданни, разпръснати из китайския интернет; често платени бази данни"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Няма лесно достъпни метаданни за цялата им колекция."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(duxiu)s>метаданни на DuXiu</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Различни файлови бази данни, разпръснати из китайския интернет; често платени бази данни"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Повечето файлове са достъпни само с премиум акаунти в BaiduYun; бавни скорости на изтегляне."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(duxiu)s>файлове на DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Различни по-малки или еднократни източници. Насърчаваме хората първо да качват в другите сенчести библиотеки, но понякога те имат колекции, които са твърде големи, за да бъдат сортирани от други, но не достатъчно големи, за да заслужават собствена категория."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Източници само с метаданни"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Също така обогатяваме нашата колекция с източници само с метаданни, които можем да съпоставим с файлове, например, използвайки ISBN номера или други полета. По-долу е представен преглед на тези източници. Отново, някои от тези източници са напълно отворени, докато за други трябва да извличаме данни."

msgid "page.faq.metadata.inspiration"
msgstr "Нашето вдъхновение за събиране на метаданни е целта на Аарон Суорц за “една уеб страница за всяка книга, която някога е била публикувана”, за която той създаде <a %(a_openlib)s>Open Library</a>. Този проект се справя добре, но нашата уникална позиция ни позволява да получим метаданни, които те не могат. Друго вдъхновение беше нашето желание да знаем <a %(a_blog)s>колко книги има в света</a>, за да можем да изчислим колко книги все още трябва да спасим."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Имайте предвид, че при търсене на метаданни показваме оригиналните записи. Не правим никакво обединяване на записи."

msgid "page.datasets.sources.last_updated.header"
msgstr "Последно обновено"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Месечно <a %(dbdumps)s>копиране на базатата данни</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Не е налично директно в големи количества, защитено срещу скрапинг"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(worldcat)s>метаданни на OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Обединена база данни"

msgid "page.datasets.unified_database.text1"
msgstr "Комбинираме всички горепосочени източници в една обединена база данни, която използваме за обслужване на този сайт. Тази обединена база данни не е директно достъпна, но тъй като Архивът на Анна е напълно с отворен код, тя може сравнително лесно да бъде <a %(a_generated)s>генерирана</a> или <a %(a_downloaded)s>изтеглена</a> като бази данни ElasticSearch и MariaDB. Скриптовете на тази страница автоматично ще изтеглят всички необходими метаданни от споменатите по-горе източници."

msgid "page.datasets.unified_database.text2"
msgstr "Ако искате да разгледате нашите данни преди да стартирате тези скриптове локално, можете да погледнете нашите JSON файлове, които водят към други JSON файлове. <a %(a_json)s>Този файл</a> е добър начален пункт."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Адаптирано от нашата <a %(a_href)s>публикация в блога</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> е огромна база данни от сканирани книги, създадена от <a %(superstar_link)s>SuperStar Digital Library Group</a>. Повечето са академични книги, сканирани с цел да бъдат достъпни дигитално за университети и библиотеки. За нашата англоговоряща аудитория, <a %(princeton_link)s>Принстън</a> и <a %(uw_link)s>Университетът на Вашингтон</a> имат добри прегледи. Има и отлична статия, която дава повече информация: <a %(article_link)s>“Дигитализиране на китайски книги: Казус на търсачката SuperStar DuXiu Scholar”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Книгите от Duxiu отдавна се пиратстват в китайския интернет. Обикновено се продават за по-малко от долар от препродавачи. Те обикновено се разпространяват чрез китайския еквивалент на Google Drive, който често е хакнат, за да позволи повече място за съхранение. Някои технически подробности могат да бъдат намерени <a %(link1)s>тук</a> и <a %(link2)s>тук</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Въпреки че книгите са били полуоткрито разпространявани, е доста трудно да се получат в големи количества. Това беше високо в нашия списък със задачи и отделихме няколко месеца пълно работно време за това. Въпреки това, в края на 2023 г. невероятен, удивителен и талантлив доброволец се свърза с нас, казвайки ни, че вече е свършил цялата тази работа — на големи разходи. Те споделиха цялата колекция с нас, без да очакват нищо в замяна, освен гаранцията за дългосрочно съхранение. Наистина забележително."

msgid "page.datasets.common.resources"
msgstr "Ресурси"

msgid "page.datasets.common.total_files"
msgstr "Общо файлове: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Общ размер на файловете: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Файлове, огледални от Архива на Анна: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Последно обновено: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Торенти от Архива на Анна"

msgid "page.datasets.common.aa_example_record"
msgstr "Примерен запис в Архива на Анна"

msgid "page.datasets.duxiu.blog_post"
msgstr "Нашата публикация в блога за тези данни"

msgid "page.datasets.common.import_scripts"
msgstr "Скриптове за импортиране на метаданни"

msgid "page.datasets.common.aac"
msgstr "Формат на Контейнерите на Архива на Анна"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Повече информация от нашите доброволци (сурови бележки):"

msgid "page.datasets.ia.title"
msgstr "IA Контролирано дигитално заемане"

msgid "page.datasets.ia.description"
msgstr "Този набор от данни е тясно свързан с <a %(a_datasets_openlib)s>набора от данни на Open Library</a>. Той съдържа изтегляне на всички метаданни и голяма част от файловете от Контролираната цифрова библиотека на IA. Актуализациите се пускат във <a %(a_aac)s>формат на контейнери на Архива на Анна</a>."

msgid "page.datasets.ia.description2"
msgstr "Тези записи се отнасят директно към набора от данни на Open Library, но също така съдържат записи, които не са в Open Library. Имаме и редица файлове с данни, изтеглени от членове на общността през годините."

msgid "page.datasets.ia.description3"
msgstr "Колекцията се състои от две части. Нуждаете се и от двете части, за да получите всички данни (с изключение на заменените торенти, които са зачеркнати на страницата с торенти)."

msgid "page.datasets.ia.part1"
msgstr "нашето първо издание, преди да стандартизираме на формата <a %(a_aac)s>Контейнери на Архива на Анна (AAC)</a>. Съдържа метаданни (като json и xml), pdf файлове (от цифровите системи за заемане acsm и lcpdf) и миниатюри на кориците."

msgid "page.datasets.ia.part2"
msgstr "добавяне на нови издания, използващи AAC. Съдържа само метаданни с времеви печати след 2023-01-01, тъй като останалото вече е покрито от “ia”. Също така всички pdf файлове, този път от системите за заемане acsm и “bookreader” (уеб четецът на IA). Въпреки че името не е съвсем точно, все пак попълваме файловете на bookreader в колекцията ia2_acsmpdf_files, тъй като те са взаимно изключващи се."

msgid "page.datasets.common.main_website"
msgstr "Основен %(source)s сайт"

msgid "page.datasets.ia.ia_lending"
msgstr "Цифрова библиотека за заемане"

msgid "page.datasets.common.metadata_docs"
msgstr "Документация на метаданни (повечето полета)"

msgid "page.datasets.isbn_ranges.title"
msgstr "Информация за страната на ISBN"

msgid "page.datasets.isbn_ranges.text1"
msgstr "Международната агенция за ISBN редовно публикува диапазоните, които е разпределила на националните агенции за ISBN. От това можем да определим към коя страна, регион или езикова група принадлежи този ISBN. В момента използваме тези данни косвено, чрез Python библиотеката <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Ресурси"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Последно обновено: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Уебсайт за ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Метаданни"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "За историята на различните разклонения на Library Genesis, вижте страницата за <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li съдържа по-голямата част от същото съдържание и метаданни като Libgen.rs, но има някои допълнителни колекции, а именно комикси, списания и стандартни документи. Той също така е интегрирал <a %(a_scihub)s>Sci-Hub</a> в своите метаданни и търсачка, което използваме за нашата база данни."

msgid "page.datasets.libgen_li.description3"
msgstr "Метаданните за тази библиотека са свободно достъпни <a %(a_libgen_li)s>на libgen.li</a>. Въпреки това, този сървър е бавен и не поддържа подновяване на прекъснатите връзки. Същите файлове са достъпни и на <a %(a_ftp)s>FTP сървър</a>, който работи по-добре."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Торенти са налични за по-голямата част от допълнителното съдържание, като най-вече торенти за комикси, списания и стандартни документи са пуснати в сътрудничество с Архива на Анна."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Колекцията от художествена литература има свои собствени торенти (отделени от <a %(a_href)s>Libgen.rs</a>), започващи от %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Според администратора на Libgen.li, колекцията “fiction_rus” (руска художествена литература) трябва да бъде покрита от редовно пускани торенти от <a %(a_booktracker)s>booktracker.org</a>, най-вече торентите <a %(a_flibusta)s>flibusta</a> и <a %(a_librusec)s>lib.rus.ec</a> торенти(за които ние сме огледала <a %(a_torrents)s>тук</a>, въпреки че все още не сме установили кои торенти съответстват на кои файлове)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Статистики за всички колекции могат да бъдат намерени <a %(a_href)s>на сайта на libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Изглежда, че и нехудожествената литература се е отклонила, но без нови торенти. Изглежда, че това се е случило от началото на 2022 г., въпреки че не сме го потвърдили."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Определени диапазони без торенти (като диапазоните за художествена литература f_3463000 до f_4260000) вероятно са файлове от Z-Library (или други дубликати), въпреки че може да искаме да направим малко дублиране и да направим торентите за lgli-уникални файлове в тези диапазони."

msgid "page.datasets.libgen_li.description5"
msgstr "Имайте предвид, че торент файловете, отнасящи се до “libgen.is”, са огледала на <a %(a_libgen)s>Libgen.rs</a> (“.is” е различен домейн, използван от Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Полезен ресурс за използване на метаданните е <a %(a_href)s>тази страница</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Торенти за художествена литература в Архива на Анна"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Торенти за комикси в Архива на Анна"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Торенти за списания в Архива на Анна"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Торенти за стандартни документи в Архива на Анна"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Торенти за руска художествена литература в Архива на Анна"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Метаданни"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Метаданни чрез FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Информация за полетата на метаданните"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Огледало на други торенти (и уникални торенти за художествена литература и комикси)"

msgid "page.datasets.libgen_li.forum"
msgstr "Форум за дискусии"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Нашата блог публикация за издаването на комиксите"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "Кратката история на различните разклонения на Library Genesis (или “Libgen”) е, че с течение на времето различните хора, участващи в Library Genesis, го изоставиха и тръгнаха по различни пътища."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Версията “.fun” беше създадена от първоначалния основател. Тя се преработи като услуга за нова, по-разпространена версия."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Версията “.rs” има много подобни данни и най-последователно пуска своята колекция в масови торенти. Тя е приблизително разделена на секции “художествена литература” и “нехудожествена” литература."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Първоначално на “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>Версията “.li”</a> има огромна колекция от комикси, както и друго съдържание, което все още не е достъпно за масово изтегляне чрез торенти. Тя има отделна колекция от торенти за художествени книги и съдържа метаданните на <a %(a_scihub)s>Sci-Hub</a> в своята база данни."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Според този <a %(a_mhut)s>пост във форума</a>, Libgen.li първоначално е бил хостван на “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> в известен смисъл също е разклонение на Library Genesis, въпреки че използваха различно име за своя проект."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Тази страница е за версията “.rs”. Тя е известна с последователното публикуване както на своите метаданни, така и на пълното съдържание на своя каталог с книги. Колекцията от книги е разделена на секции за художествена и нехудожествена литература."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Полезен ресурс за използване на метаданните е <a %(a_metadata)s>тази страница</a> (блокира IP диапазони, може да е необходим VPN)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Към март 2024 г. нови торенти се публикуват в <a %(a_href)s>тази тема на форума</a> (блокира IP диапазони, може да е необходим VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Торенти за нехудожествена литература в Архива на Анна"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Торенти за художествена литература в Архива на Анна"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs метаданни"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Информация за полетата на метаданните на Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Торенти за нехудожествена литература на Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Торенти за художествена литература на Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Форум за дискусии на Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Торенти от Архива на Анна (корици на книги)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Нашият блог за издаването на кориците на книги"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis е известен с това, че щедро предоставя своите данни в големи обеми чрез торенти. Нашата колекция от Libgen се състои от допълнителни данни, които те не пускат директно, в партньорство с тях. Огромни благодарности на всички, които работят с Library Genesis!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Издание 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Това <a %(blog_post)s>първо издание</a> е доста малко: около 300GB корици на книги от разклонението на Libgen.rs, както за художествена, така и за нехудожествена литература. Те са организирани по същия начин, както се появяват на libgen.rs, напр.:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s за нехудожествена книга."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s за художествена книга."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Точно както при колекцията на Z-Library, ние ги поставихме всички в голям .tar файл, който може да бъде качен с помощта на <a %(a_ratarmount)s>ratarmount</a>, ако искате да работите с файловете директно."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> е собствена база данни на неправителствената организация <a %(a_oclc)s>OCLC</a>, която събира метаданни от библиотеки по целия свят. Вероятно това е най-голямата колекция от библиотечни метаданни в света."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Октомври 2023 г., първоначално издание:"

msgid "page.datasets.worldcat.description2"
msgstr "През октомври 2023 г. ние <a %(a_scrape)s>пуснахме</a> цялостно извличане на базата данни OCLC (WorldCat), във <a %(a_aac)s>формат Containers на Архива на Анна</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Торенти от Архива на Анна"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Нашата публикация в блога за тези данни"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library е проект с отворен код на Internet Archive за каталогизиране на всяка книга в света. Той има една от най-големите възможности за търсене на книги в света и разполага с много книги за дигитално заемане. Неговият каталог с метаданни за книги е свободно достъпен за изтегляне и е включен в Архива на Анна (въпреки че в момента не е в търсенето, освен ако не търсите изрично по Open Library ID)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Метаданни"

msgid "page.datasets.isbndb.release1.title"
msgstr "Издание 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Това е извадка от много заявки към isbndb.com през септември 2022 г. Опитахме се да покрием всички диапазони на ISBN. Това са около 30.9 милиона записа. В техния сайт те имат 32.6 милиона записа, така че може би сме пропуснали някои, или <em>те</em> може да правят нещо грешно."

msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON отговорите са доста необработени от техния сървър. Един проблем с качеството на данните, който забелязахме, е че за ISBN-13, които започват с различен префикс от “978-”, те все още включват поле “isbn”, което просто е ISBN-13 като първите 3 цифри са отрязани (и проверочната цифра преизчислена). Това очевидно е погрешно, но изглежда така го правят, така че не сме го променили."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Друг потенциален проблем, с който може да се сблъскате, е фактът, че полето “isbn13” има дубликати, така че не можете да го използвате като първичен ключ в база данни. Полетата “isbn13”+“isbn” комбинирани изглежда са уникални."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "За повече информация относно Sci-Hub, посетете неговия <a %(a_scihub)s>официален сайт</a>, <a %(a_wikipedia)s>страница в Wikipedia</a> и това <a %(a_radiolab)s>подкаст интервю</a>."

msgid "page.datasets.scihub.description2"
msgstr "Имайте предвид, че Sci-Hub е <a %(a_reddit)s>замразен от 2021 г.</a>. Той беше замразен и преди, но през 2021 г. бяха добавени няколко милиона статии. Все пак, ограничен брой статии продължават да се добавят към колекциите на Libgen “scimag”, но не достатъчно, за да даде основание за нови групови торенти."

msgid "page.datasets.scihub.description3"
msgstr "Ние използваме метаданните на Sci-Hub, предоставени от <a %(a_libgen_li)s>Libgen.li</a> в неговата колекция “scimag”. Също така използваме набора от данни <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Имайте предвид, че торентите “smarch” са <a %(a_smarch)s>остарели</a> и затова не са включени в нашия списък с торенти."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Торенти в Архива на Анна"

msgid "page.datasets.scihub.link_metadata"
msgstr "Метаданни и торенти"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Торенти в Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Торенти в Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Актуализации в Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Страница в Wikipedia"

msgid "page.datasets.scihub.link_podcast"
msgstr "Подкаст интервю"

msgid "page.datasets.upload.title"
msgstr "Качвания в Архива на Анна"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Преглед от <a %(a1)s>страницата с Datasets</a>."

msgid "page.datasets.upload.description"
msgstr "Различни по-малки или еднократни източници. Насърчаваме хората първо да качват в други сенчести библиотеки, но понякога хората имат колекции, които са твърде големи, за да могат другите да ги сортират, но не достатъчно големи, за да дадат основание за собствена категория."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Колекцията “качвания” е разделена на по-малки подколекции, които са посочени в AACIDs и имената на торентите. От всички подколекции първо бяха премахнати дублиращите записи спрямо основната колекция, въпреки че JSON файловете с метаданни “upload_records“ все още съдържат много препратки към оригиналните файлове. Файловете, които не са книги, също бяха премахнати от повечето подколекции и обикновено <em>не</em> се отбелязват в “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Много подколекции сами по себе си се състоят от под-подколекции (например от различни източници), които са представени като директории в полетата “filepath”."

msgid "page.datasets.upload.subs.heading"
msgstr "Подколекциите са:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Подколекция"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Бележки"

msgid "page.datasets.upload.action.browse"
msgstr "разглеждане"

msgid "page.datasets.upload.action.search"
msgstr "търсене"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "От <a %(a_href)s>aaaaarg.fail</a>. Изглежда доста пълно. От нашия доброволец “cgiym”."

msgid "page.datasets.upload.source.acm"
msgstr "От <a %(a_href)s><q>ACM Digital Library 2020</q></a> торент. Има доста голямо припокриване със съществуващите колекции от статии, но много малко съвпадения на MD5, затова решихме да го запазим изцяло."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Скрап на <q>iRead eBooks</q> (= фонетично <q>ai rit i-books</q>; airitibooks.com), от доброволец <q>j</q>. Съответства на <q>airitibooks</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "От колекция <a %(a1)s><q>Библиотека Александрина</q></a>. Частично от оригиналния източник, частично от the-eye.eu, частично от други огледала."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "От частен торент сайт за книги, <a %(a_href)s>Bibliotik</a> (често наричан “Bib”), чиито книги са били обединени в торенти по име (A.torrent, B.torrent) и разпространявани чрез the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "От нашия доброволец “bpb9v”. За повече информация относно <a %(a_href)s>CADAL</a>, вижте бележките на нашата <a %(a_duxiu)s>страница с данни за DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Още от нашия доброволец “bpb9v”, предимно файлове от DuXiu, както и папка “SuperStar_Journals” (SuperStar is the company behind DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "От нашия доброволец “cgiym”, китайски текстове от различни източници (представени като поддиректории), включително от <a %(a_href)s>China Machine Press</a> (голям китайски издател)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Некитайски колекции (представени като поддиректории) от нашия доброволец “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Скрап на книги за китайска архитектура, от доброволец <q>cm</q>: <q>Получих го чрез експлоатация на уязвимост в мрежата на издателството, но тази пролука вече е затворена</q>. Съответства на <q>chinese_architecture</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Книги от академичното издателство <a %(a_href)s>De Gruyter</a>, събрани от няколко големи торента."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "\"Изстърган\" от <a %(a_href)s>docer.pl</a>, полски сайт за споделяне на файлове, фокусиран върху книги и други писмени произведения. \"Изстърган\" в края на 2023 г. от доброволеца “p”. Нямаме добри метаданни от оригиналния сайт (дори нямаме и разширенията на файловете), но филтрирахме за подобни на книги файлове, и често успявахме да извлечем метаданни от самите файлове."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, директно от DuXiu, събрани от доброволец “w”. Само последните книги на DuXiu са достъпни директно чрез електронни книги, така че повечето от тях трябва да са нови."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Останалите файлове от DuXiu от доброволец “m”, които не бяха във формат PDG на DuXiu (основният <a %(a_href)s>набор от данни на DuXiu</a>). Събрани от много оригинални източници, за съжаление без запазване на тези източници в пътя на файла."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Скрап на еротични книги, от доброволец <q>do no harm</q>. Съответства на <q>hentai</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Колекция, \"изстъргана\" от японски издател на манга от доброволец “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Избрани съдебни архиви на Лонгкуан</a>, предоставени от доброволец “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "\"Изстърган\" от <a %(a_href)s>magzdb.org</a>, съюзник на Library Genesis (свързан е с началната страница на libgen.rs ), но който не искаше да предостави файловете си директно. Получен от доброволец “p” в края на 2023 г."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Различни малки качвания, твърде малки за собствена подколекция, но представени като директории."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Електронни книги от AvaxHome, руски уебсайт за споделяне на файлове."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Архив на вестници и списания. Съответства на <q>newsarch_magz</q> metadata в <a %(a1)s><q>Други metadata скрапове</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Скрап на <a %(a1)s>Центъра за документация по философия</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Колекция на доброволец “o”, който събра полски книги директно от оригинални сайтове за издания (“сцена”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Комбинирани колекции на <a %(a_href)s>shuge.org</a> от доброволци “cgiym” и “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Имперска библиотека на Трантор”</a> (наречена на измислената библиотека), \"изстъргана\" през 2022 г. от доброволец “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Под-под-колекции (представени като директории) от доброволец “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (от <a %(a_sikuquanshu)s>Dizhi(迪志)</a> в Тайван), mebook (mebook.cc, 我的小书屋, моята малка книжарница — woz9ts: “Този сайт основно се фокусира върху споделянето на висококачествени електронни книги, някои от които са форматирани от самия собственик. Собственикът беше <a %(a_arrested)s>арестуван</a> през 2019 г. и някой направи колекция от файловете, които той сподели.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Останали файлове от DuXiu от доброволеца “woz9ts”, които не бяха във формат PDG на DuXiu (все още трябва да бъдат конвертирани в PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Торенти от Архива на Анна"

msgid "page.datasets.zlib.title"
msgstr "\"Изстърган\" от Z-Library"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library има своите корени в общността на <a %(a_href)s>Library Genesis</a> и първоначално се стартира с техните данни. Оттогава насам, тя се професионализира значително и има много по-модерен интерфейс. Поради това те могат да получават много повече дарения, както парични за поддържане и подобряване на уебсайта, така и дарения на нови книги. Те са натрупали голяма колекция в допълнение към Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Актуализация към февруари 2023 г."

msgid "page.datasets.zlib.description.allegations"
msgstr "В края на 2022 г. предполагаемите основатели на Z-Library бяха арестувани и домейните бяха иззети от властите на Съединените щати. Оттогава сайтът бавно се връща онлайн. Не е известно кой го управлява в момента."

msgid "page.datasets.zlib.description.three_parts"
msgstr "Колекцията се състои от три части. Оригиналните описателни страници за първите две части са запазени по-долу. Нуждаете се от всичките три части, за да получите всички данни (с изключение на заменените торенти, които са зачеркнати на страницата с торенти)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: нашето първо издание. Това беше първото издание на това, което тогава се наричаше “Пиратско библиотечно огледало” (“pilimi”)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: второ издание, този път с всички файлове опаковани в .tar файлове."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: добавени нови издания, използващи <a %(a_href)s>формата на Контейнери на Архива на Анна (AAC)</a>, понастоящем, издадени в сътрудничество с екипа на Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Торенти от Архива на Анна (метаданни + съдържание)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Примерен запис в Архива на Анна (оригинална колекция)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Примерен запис в Архива на Анна (колекция “zlib3”)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Основен сайт"

msgid "page.datasets.zlib.link.onion"
msgstr "Хост на псевдо домейн от най-високо ниво, реализиран от проекта OnioNS домейн"

msgid "page.datasets.zlib.blog.release1"
msgstr "Блог пост за Издание 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Блог пост за Издание 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Издания на Zlib (оригинални описателни страници)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Издание 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Първоначалното огледало беше старателно получено през 2021 и 2022. В този момент е леко остаряло: отразява състоянието на колекцията през юни 2021. Ще го актуализираме в бъдеще. В момента сме фокусирани върху издаването на това първо издание."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Тъй като Library Genesis вече е запазена с публични торенти и е включена в Z-Library, направихме основно премахване на дублиращо съдържание спрямо Library Genesis през юни 2022. За това използвахме MD5 хешове. Вероятно има много повече дублиращо съдържание в библиотеката, като например множество файлови формати на една и съща книга. Това е трудно за точно откриване, затова не го правим. След премахването на дублиращото съдържание остават над 2 милиона файла, с общ размер малко под 7TB."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Колекцията се състои от две части: MySQL “.sql.gz” архив на метаданните и 72 торент файла с размер около 50-100GB всеки. Метаданните съдържат данните, както са докладвани от сайта на Z-Library (заглавие, автор, описание, файлов тип), както и действителния размер на файла и md5sum, които наблюдавахме, тъй като понякога тези данни не съвпадат. Изглежда, че има диапазони от файлове, за които самата Z-Library има неправилни метаданни. В някои изолирани случаи може също да сме изтеглили неправилни файлове, които ще се опитаме да открием и поправим в бъдеще."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Големите торент файлове съдържат действителните данни за книгите, като ID на Z-Library е използвано като име на файла. Разширенията на файловете могат да бъдат възстановени с помощта на архива на метаданните."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Колекцията е смес от нехудожествено и художествено съдържание (не е разделена както в Library Genesis). Качеството също варира значително."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Това първо издание вече е напълно достъпно. Имайте предвид, че торент файловете са достъпни само чрез нашето огледало в Tor."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Издание 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Получихме всички книги, които бяха добавени в Z-Library между последното ни огледало и август 2022. Също така се върнахме и изтеглихме някои книги, които пропуснахме първия път. Всичко на всичко, тази нова колекция е около 24TB. Отново, тази колекция е премахната от дублиращо съдържание спрямо Library Genesis, тъй като вече има налични торенти за тази колекция."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Данните са организирани подобно на първото издание. Има MySQL “.sql.gz” архив на метаданните, който също включва всички метаданни от първото издание, като по този начин го замества. Добавихме и някои нови колони:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: дали този файл вече е в Library Genesis, в колекцията на нехудожествена или художествена литература (съвпадение по md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: в кой торент се намира този файл."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: зададено, когато не успяхме да изтеглим книгата."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Споменахме това последния път, но за да изясним: “filename” и “md5” са действителните свойства на файла, докато “filename_reported” и “md5_reported” са това, което изтеглихме от Z-Library. Понякога тези две не съвпадат, затова включихме и двете."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "За това издание променихме колацията на “utf8mb4_unicode_ci”, която трябва да е съвместима с по-старите версии на MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Файловете с данни са подобни на миналия път, въпреки че са много по-големи. Просто не можехме да се занимаваме с създаването на множество по-малки торент файлове. “pilimi-zlib2-0-14679999-extra.torrent” съдържа всички файлове, които пропуснахме в последното издание, докато другите торенти са всички нови диапазони на ID. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Обновяване %(date)s:</strong> Направихме повечето от нашите торенти твърде големи, което затрудни торент клиентите. Премахнахме ги и пуснахме нови торенти."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Обновяване %(date)s:</strong> Все още, имаше твърде много файлове, затова ги опаковахме в tar файлове и отново пуснахме нови торенти."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Издание 2 допълнение (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Това е един допълнителен торент файл. Той не съдържа нова информация, но има някои данни, които могат да отнемат време за изчисление. Това го прави удобен, тъй като изтеглянето на този торент често е по-бързо, отколкото изчисляването му от нулата. По-специално, той съдържа SQLite индекси за tar файловете, за използване с <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Често задавани въпроси (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Какво е Архивът на Анна?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> е проект с нестопанска цел, който има две цели:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Съхранение</strong> Запазване на всички знания и на културата на човечеството.</li><li><strong>Достъп:</strong> Осигуряване на достъп до тези знания и култура за всеки в света.</li>"

msgid "page.home.intro.open_source"
msgstr "Целият ни <a %(a_code)s>код</a> и <a %(a_datasets)s>данни</a> са напълно с отворен код."

msgid "page.home.preservation.header"
msgstr "Съхранение"

msgid "page.home.preservation.text1"
msgstr "Съхраняваме книги, статии, комикси, списания и други, като събираме тези материали от различни <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">сенчести библиотеки</a>, официални библиотеки, и други колекции на едно място. Всички тези данни се съхраняват завинаги, като се улеснява дублирането им в големи количества — чрез торенти — което води до много копия по целия свят. Някои сенчести библиотеки вече правят това сами (напр. Sci-Hub, Library Genesis), докато Архивът на Анна “освобождава” други библиотеки, които не предлагат масово разпространение (напр. Z-Library) или изобщо не са сенчести библиотеки (напр. Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Това широко разпространение, комбинирано с отворен код, прави нашия сайт устойчив на премахвания и осигурява дългосрочното запазване на човешкото знание и култура. Научете повече за <a href=\"/datasets\">нашите набори от данни</a>."

msgid "page.home.preservation.label"
msgstr "Смятаме, че сме съхранили около <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% от книгите в света</a>."

msgid "page.home.access.header"
msgstr "Достъп"

msgid "page.home.access.text"
msgstr "Работим с партньори, за да направим колекциите си лесно и свободно достъпни за всеки. Вярваме, че всеки има право на достъп до колективната мъдрост на човечеството. И <a %(a_search)s>не за сметка на авторите</a>."

msgid "page.home.access.label"
msgstr "Почасови изтегляния през последните 30 дни. Средна часова стойност: %(hourly)s. Средна дневна стойност: %(daily)s."

msgid "page.about.text2"
msgstr "Ние силно вярваме в свободния поток на информация и запазването на знанието и културата. С тази търсачка ние надграждаме на раменете на гиганти. Ние дълбоко уважаваме упоритата работа на хората, които са създали различните скрити библиотеку, и се надяваме, че тази търсачка ще разшири обхвата им."

msgid "page.about.text3"
msgstr "За да бъдете информирани за напредъка ни, следвайте Анна на <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> или <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. За въпроси и обратна връзка, моля, свържете се с Анна на %(email)s."

msgid "page.faq.help.title"
msgstr "Как мога да помогна?"

msgid "page.about.help.text"
msgstr "<li>1. Следвайте ни на <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, или <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Разпространете думата за Архивът на Анна в Twitter, Reddit, Tiktok, Instagram, във Вашето локално кафене или библиотека, или където и да отидете! Ние не вярваме в недостъпността — ако ни свалят, ще изскочим отново на друго място, защото целият ни код е отворен.</li><li>3. Ако имате възможност, обмислете да <a href=\"/donate\">дарите</a>.</li><li>4. Помогнете да<a href=\"https://translate.annas-software.org/\">преведем</a> нашия уебсайт на различни езици.</li><li>5. Ако сте софтуерен инженер, обмислете да допринесете към нашия <a href=\"https://annas-software.org/\">отворен код</a>, или като правите достъпни нашите <a href=\"https://en.wikipedia.org/wiki/Pirate_Library_Mirror\">торенти</a>.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "Сега имаме и синхронизиран канал в Matrix на %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Ако сте изследовател по сигурността, можем да използваме вашите умения както за нападение, така и за защита. Вижте нашата страница <a %(a_security)s>Сигурност</a>."

msgid "page.about.help.text7"
msgstr "7. Търсим експерти по плащания за анонимни търговци. Можете ли да ни помогнете да добавим по-удобни начини за даряване? PayPal, WeChat, подаръчни карти. Ако познавате някого, свържете се с нас."

msgid "page.about.help.text8"
msgstr "8. Винаги търсим повече капацитет на сървъра."

msgid "page.about.help.text9"
msgstr "9. Можете да помогнете, като докладвате за проблеми с файлове, оставяте коментари и създавате списъци направо на този сайт. Можете също така да помогнете, като <a %(a_upload)s>качите още книги</a> или коригирате проблеми с файловете или форматирате съществуващите книги."

msgid "page.about.help.text10"
msgstr "10. Създайте или помогнете да поддържаме страницата в Уикипедия за архива на Анна на вашия език."

msgid "page.about.help.text11"
msgstr "11. Търсим да поставим малки реклами с вкус. Ако искате да рекламирате в архива на Анна, уведомете ни."

msgid "page.faq.help.mirrors"
msgstr "Ще се радваме хората да създават <a %(a_mirrors)s>огледала</a>, и ние ще ги подкрепим финансово."

msgid "page.about.help.volunteer"
msgstr "За по-подробна информация как да бъдеш доброволец, вижте нашата <a %(a_volunteering)s>страница за доброволчество и награди</a>."

msgid "page.faq.slow.title"
msgstr "Защо бавните изтегляния са толкова бавни?"

msgid "page.faq.slow.text1"
msgstr "Буквално нямаме достатъчно ресурси, за да предоставим на всеки в света високоскоростни изтегляния, колкото и да искаме. Ако богат благодетел иска да ни помогне и да осигури това за нас, би било невероятно, но дотогава ние правим всичко възможно. Ние сме проект с нестопанска цел, който едва се издържа чрез дарения."

msgid "page.faq.slow.text2"
msgstr "Ето защо внедрихме две системи за безплатни изтегляния, с нашите партньори: споделени сървъри с бавни изтегляния и малко по-бързи сървъри с листа на изчакване (за да намалим броя на хората, които изтеглят едновременно)."

msgid "page.faq.slow.text3"
msgstr "Имаме и <a %(a_verification)s>проверка чрез браузъра</a> за нашите бавни изтегляния, защото ботове и скрепери ще злоупотребят с тях, правейки нещата още по-бавни за истинските потребители."

msgid "page.faq.slow.text4"
msgstr "Имайте предвид, че при използване на Tor Browser може да се наложи да коригирате вашите настройки за сигурност. При най-ниската от възможностите, наречена “Стандартна”, предизвикателството на Cloudflare turnstile успява. При по-високите възможности, наречени “По-безопасна” и “Най-безопасна”, предизвикателството не успява."

msgid "page.faq.slow.text5"
msgstr "Понякога при изтегляне на големи файлове, бавните изтегляния могат да се прекъснат по средата. Препоръчваме използването на мениджър за изтегляне (като JDownloader), за да се възобновят автоматично големите изтегляния."

msgid "page.donate.faq.title"
msgstr "Често Задавани Въпроси за дарения"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Подновяват ли се членствата автоматично?</div> Членствата<strong>не се</strong> подновяват автоматично. Можете да се присъедините за толкова дълго или кратко, колкото искате."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Мога ли да надградя членството си или да получа няколко членства?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Имате ли други методи на плащане?</div> В момента не. Много хора не искат архиви като този да съществуват, така че трябва да внимаваме. Ако можете да ни помогнете да настроим безопасно други (по-удобни) методи на плащане, моля, свържете се с нас на %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Какво означават диапазоните на месец?</div> Можете да достигнете до по-ниската страна на диапазона, като приложите всички отстъпки, като например изберете период по-дълъг от месец."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>За какво харчите даренията?</div> 100%%ще запази и направи достъпни световното знание и култура. В момента го изразходваме предимно за сървъри, съхранение и честотна лента. Никакви пари не отиват лично на членовете на екипа."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Мога ли да направя голямо дарение?</div> Това би било невероятно! За дарения над няколко хиляди долара, моля, свържете се директно с нас на %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Мога ли да направя дарение без да ставам член?</div> Разбира се. Приемаме дарения на всякаква сума на този Monero (XMR) адрес: %(address)s."

msgid "page.faq.upload.title"
msgstr "Как да кача нови книги?"

msgid "page.upload.zlib.text1"
msgstr "Алтернативно, можете да ги качите в Z-Library <a %(a_upload)s>тук</a>."

msgid "page.upload.upload_to_both"
msgstr "За малки качвания (до 10,000 файла) качете ги и на %(first)s, и на %(second)s."

msgid "page.upload.text1"
msgstr "Засега предлагаме да качвате нови книги във „разклоненията“ на LibGen. Ето едно <a %(a_guide)s>удобно указание</a>. Обърнете внимание на това, че и двете разклонения, които индексираме в този уебсайт, се извличат от същата система за качване."

msgid "page.upload.libgenli_login_instructions"
msgstr "За Libgen.li, уверете се, че първо сте влезли в <a %(a_forum)s>техния форум</a> с потребителско име %(username)s и парола %(password)s, и след това се върнете на тяхната <a %(a_upload_page)s>страница за качване</a>."

msgid "common.libgen.email"
msgstr "Ако имейл адресът Ви не работи във форумите на Libgen, препоръчваме да използвате <a %(a_mail)s>Proton Mail</a> (безплатно). Можете също така да <a %(a_manual)s>поискате активация</a> за акаунта Ви."

msgid "page.faq.mhut_upload"
msgstr "Имайте предвид, че mhut.org блокира определени IP диапазони, така че може да е необходим VPN."

msgid "page.upload.large.text"
msgstr "За големи качвания (над 10 000 файла), които не се приемат от Libgen или Z-Library, моля, свържете се с нас на %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "За да качите академични статии, моля, освен в Library Genesis, качете ги и в <a %(a_stc_nexus)s>STC Nexus</a>. Те са най-добрата shadow library за нови статии. Все още не сме ги интегрирали, но ще го направим в някакъв момент. Можете да използвате техния <a %(a_telegram)s>бот за качване в Telegram</a> или да се свържете с адреса, посочен в тяхното съобщение, ако имате твърде много файлове за качване по този начин."

msgid "page.faq.request.title"
msgstr "Как да заявя книги?"

msgid "page.request.cannot_accomodate"
msgstr "В момента не можем да удовлетворим заявките за книги."

msgid "page.request.forums"
msgstr "Моля, направете вашите заявки във форумите на Z-Library или Libgen."

msgid "page.request.dont_email"
msgstr "Не ни изпращайте заявки за книги по имейл."

msgid "page.faq.metadata.title"
msgstr "Събирате ли метаданни?"

msgid "page.faq.metadata.indeed"
msgstr "Наистина го правим."

msgid "page.faq.1984.title"
msgstr "Изтеглих 1984 от Джордж Оруел, ще дойде ли полицията на вратата ми?"

msgid "page.faq.1984.text"
msgstr "Не се притеснявайте твърде много, има много хора, които изтеглят от сайтове, свързани с нас, и е изключително рядко да попаднете в неприятности. Въпреки това, за да сте в безопасност, препоръчваме да използвате VPN (платен) или <a %(a_tor)s>Tor</a> (безплатен)."

msgid "page.faq.save_search.title"
msgstr "Как да запазя настройките на търсенето си?"

msgid "page.faq.save_search.text1"
msgstr "Изберете настройките, които харесвате, оставете полето за търсене празно, кликнете върху “Търсене” и след това запазете страницата, използвайки функцията за отметки на вашия браузър."

msgid "page.faq.mobile.title"
msgstr "Имате ли мобилно приложение?"

msgid "page.faq.mobile.text1"
msgstr "Нямаме официално мобилно приложение, но можете да инсталирате този сайт като приложение."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Натиснете менюто с три точки в горния десен ъгъл и изберете “Добавяне към началния екран”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Натиснете бутона “Сподели” в долната част и изберете “Добави към началния екран”."

msgid "page.faq.api.title"
msgstr "Имате ли API?"

msgid "page.faq.api.text1"
msgstr "Имаме един стабилен JSON API за членове, за получаване на бърз адрес за изтегляне: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (документация в самия JSON)."

msgid "page.faq.api.text2"
msgstr "За други случаи на употреба, като например повторно минаване през всички наши файлове, изграждане на персонализирано търсене и т.н., препоръчваме <a %(a_generate)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB. Необработените данни могат да бъдат ръчно изследвани <a %(a_explore)s>чрез JSON файлове</a>."

msgid "page.faq.api.text3"
msgstr "Нашият списък със необработени торенти може да бъде изтеглен и като <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "ЧЗВ за торенти"

msgid "page.faq.torrents.q1"
msgstr "Искам да помогна със качването, но нямам много дисково пространство."

msgid "page.faq.torrents.a1"
msgstr "Използвайте <a %(a_list)s>генератора на списъци с торенти</a>, за да генерирате списък с торенти, които най-много се нуждаят от качване, в рамките на вашите ограничения за съхранение."

msgid "page.faq.torrents.q2"
msgstr "Торентите са твърде бавни; мога ли да изтегля данните директно от вас?"

msgid "page.faq.torrents.a2"
msgstr "Да, вижте страницата <a %(a_llm)s>данни на LLM</a>."

msgid "page.faq.torrents.q3"
msgstr "Мога ли да изтегля само подмножество от файловете, като например само определен език или тема?"

msgid "page.faq.torrents.a3_short_answer"
msgstr "Кратък отговор: не е лесно."

msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Дълъг отговор:"

msgid "page.faq.torrents.a3"
msgstr "Повечето торенти съдържат файловете директно, което означава, че можете да инструктирате торент клиентите да изтеглят само необходимите файлове. За да определите кои файлове да изтеглите, можете да <a %(a_generate)s>генерирате</a> нашите метаданни или да <a %(a_download)s>изтеглите</a> нашите бази данни ElasticSearch и MariaDB. За съжаление, редица торент колекции съдържат .zip или .tar файлове в основата си, в този случай трябва да изтеглите целия торент, преди да можете да изберете отделните файлове."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Имаме <a %(a_ideas)s>някои идеи</a> за последния случай обаче.)"

msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Все още няма лесни за използване инструменти за филтриране на торенти, но приемаме съдействие."

msgid "page.faq.torrents.q4"
msgstr "Как се справяте с дублиращите се файлове в торентите?"

msgid "page.faq.torrents.a4"
msgstr "Опитваме се да поддържаме минимално дублиране или припокриване между торентите в този списък, но това не винаги може да бъде постигнато, и зависи силно от политиките на техните библиотеки. За библиотеките, които пускат свои торенти, това не е в нашите ръце. За торентите, пуснати от Архива на Анна, дублирането се премахва само въз основа на MD5 хеш, което означава, че различните версии на една и съща книга не се дублират."

msgid "page.faq.torrents.q5"
msgstr "Мога ли да получа списъка с торенти като JSON?"

msgid "page.faq.torrents.a5"
msgstr "Да."

msgid "page.faq.torrents.q6"
msgstr "Не виждам PDF или EPUB файлове в торентите, само бинарни файлове? Какво да правя?"

msgid "page.faq.torrents.a6"
msgstr "Това всъщност са PDF и EPUB файлове, те просто нямат разширение в много от нашите торенти. Има две места, където можете да намерите метаданни за торент файловете, включително типовете/разширенията на файловете:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Всяка колекция или издание има свои метаданни. Например, <a %(a_libgen_nonfic)s>Libgen.rs торенти</a> имат съответната база данни с метаданни, хоствана на сайта на Libgen.rs. Обикновено, свързваме към съответните ресурси с метаданни от страницата с <a %(a_datasets)s>набора от данни на всяка колекция</a>."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Препоръчваме <a %(a_generate)s>генериране</a> или <a %(a_download)s>изтегляне</a> на нашите бази данни ElasticSearch и MariaDB. Те съдържат карта за всеки запис в Архива на Анна до съответните торент файлове (ако са налични), под “torrent_paths” в ElasticSearch JSON."

msgid "page.faq.torrents.q7"
msgstr "Защо моят торент клиент не може да отвори някои от вашите торент файлове / магнитни връзки?"

msgid "page.faq.torrents.a7"
msgstr "Някои торент клиенти не поддържат големи размери на частите, които много от нашите торенти имат (за по-новите вече не го правим — въпреки че е валидно според спецификациите!). Затова опитайте с друг клиент, ако срещнете този проблем, или се оплачете на създателите на вашия торент клиент."

msgid "page.faq.security.title"
msgstr "Имате ли програма за отговорно разкриване?"

msgid "page.faq.security.text1"
msgstr "Приветстваме изследователи по сигурността да търсят уязвимости в нашите системи. Ние сме големи поддръжници на отговорното разкриване. Свържете се с нас <a %(a_contact)s>тук</a>."

msgid "page.faq.security.text2"
msgstr "В момента не можем да присъждаме награди за откриване на уязвимости, освен за уязвимости, които имат <a %(a_link)s>потенциал да компрометират нашата анонимност</a>, за които предлагаме награди в диапазона от $10k-50k. Бихме искали да предложим по-широк обхват за награди в бъдеще! Моля, имайте предвид, че социалните инженерни атаки са извън обхвата."

msgid "page.faq.security.text3"
msgstr "Ако се интересувате от офанзивна сигурност и искате да помогнете за архивирането на световното знание и култура, не се колебайте да се свържете с нас. Има много начини, по които можете да помогнете."

msgid "page.faq.resources.title"
msgstr "Има ли повече ресурси за Архива на Анна?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Блогът на Анна</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — редовни актуализации"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Софтуер на Анна</a> — нашият отворен код"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Превод на Софтуера на Анна</a> — нашата система за превод"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Набор от данни</a> — за данните"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — алтернативни домейни"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Уикипедия</a> — повече за нас (моля, помогнете да поддържаме тази страница актуална или създайте една за вашия собствен език!)"

msgid "page.faq.copyright.title"
msgstr "Как да докладвам за нарушение на авторските права?"

msgid "page.faq.copyright.text1"
msgstr "Ние не хостваме никакви материали, защитени с авторски права тук. Ние сме търсачка и като такава индексираме само метаданни, които вече са публично достъпни. Когато изтегляте от тези външни източници, препоръчваме да проверите законите във вашата държава относно това, което е позволено. Ние не носим отговорност за съдържание, хоствано от други."

msgid "page.faq.copyright.text2"
msgstr "Ако имате оплаквания относно това, което виждате тук, най-добрият ви залог е да се свържете с оригиналния сайт. Редовно внасяме техните промени в нашата база данни. Ако наистина смятате, че имате валидна жалба по DMCA, на която трябва да отговорим, моля, попълнете <a %(a_copyright)s>формуляра за жалба по DMCA / Авторски права</a>. Вземаме вашите оплаквания на сериозно и ще се свържем с вас възможно най-скоро."

msgid "page.faq.hate.title"
msgstr "Мразя как управлявате този проект!"

msgid "page.faq.hate.text1"
msgstr "Бихме искали също да напомним на всички, че целият ни код и данни са напълно с отворени. Това е уникално за проекти като нашия — ние не знаем за друг проект с подобен масивен каталог, който също е с напълно отворен код. Напълно приемаме всеки, който смята, че управляваме проекта си лошо, да вземе нашия код и данни, и да създаде своя собствена сенчеста библиотека! Не казваме това от злоба или нещо подобно — наистина мислим, че това би било страхотно, тъй като ще повиши стандарта за всички и ще запази по-добре наследството на човечеството."

msgid "page.faq.uptime.title"
msgstr "Имате ли наблюдение на непрекъсността на работата?"

msgid "page.faq.uptime.text1"
msgstr "Моля, вижте <a %(a_href)s>този отличен проект</a>."

msgid "page.faq.physical.title"
msgstr "Как да даря книги или други физически материали?"

msgid "page.faq.physical.text1"
msgstr "Моля, изпратете ги на <a %(a_archive)s>Internet Archive</a>. Те ще ги съхранят правилно."

msgid "page.faq.anna.title"
msgstr "Коя е Анна?"

msgid "page.faq.anna.text1"
msgstr "Вие сте Анна!"

msgid "page.faq.favorite.title"
msgstr "Кои са вашите любими книги?"

msgid "page.faq.favorite.text1"
msgstr "Ето някои книги, които имат специално значение за света на сенчестите библиотеки и цифровото съхранение:"

msgid "page.fast_downloads.no_more_new"
msgstr "Днес ви свършиха бързите изтегляния."

msgid "page.fast_downloads.no_member"
msgstr "Станете член, за да получите достъп до бързите изтегляния."

msgid "page.fast_downloads.no_member_2"
msgstr "Сега поддържаме Amazon подаръчни карти, кредитни и дебитни карти, криптовалути, Alipay и WeChat."

msgid "page.home.full_database.header"
msgstr "Пълна база данни"

msgid "page.home.full_database.subtitle"
msgstr "Книги, вестници, списания, комикси, библиотечни записи, метаданни, …"

msgid "page.home.full_database.search"
msgstr "Търсене"

msgid "page.home.scidb.header"
msgstr "SciDB - система за управление на база данни, ориентирана към колони"

msgid "layout.index.header.nav.beta"
msgstr "бета"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub е <a %(a_paused)s>спрял</a> качването на нови статии."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB е продължение на Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Директен достъп до %(count)s академични статии"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI - името е цифров идентификатор на обект, всеки обект - физически, цифров или абстрактен"

msgid "page.home.scidb.open"
msgstr "Отворено"

msgid "page.home.scidb.browser_verification"
msgstr "Ако сте <a %(a_member)s>член</a>, не е необходима проверка в браузъра."

msgid "page.home.archive.header"
msgstr "Дългосрочен архив"

msgid "page.home.archive.body"
msgstr "Наборите от данни, използвани в архива на Анна, са напълно отворени и могат да бъдат дублирани групово чрез торенти. <a %(a_datasets)s>Научете повече...</a>"

msgid "page.home.torrents.body"
msgstr "Можете да помогнете изключително много, като заредите торенти. <a %(a_torrents)s>Научете повече...</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s сийдове"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s сийдове"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s сийдове"

msgid "page.home.llm.header"
msgstr "LLM данни за обучение"

msgid "page.home.llm.body"
msgstr "Имаме най-голямата в света колекция от висококачествени текстови данни. <a %(a_llm)s>Научете повече...</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Огледала: призив за доброволци"

msgid "page.home.volunteering.header"
msgstr "🤝 Търсим доброволци"

msgid "page.home.volunteering.help_out"
msgstr "Като неправителствен, отворен проект, винаги търсим хора, които да помогнат."

msgid "page.home.payment_processor.body"
msgstr "Ако управлявате анонимен платежен процесор с висок риск, моля, свържете се с нас. Също така търсим хора, които искат да поставят изискани малки реклами. Всички приходи отиват за нашите усилия за съхранение."

msgid "layout.index.header.nav.annasblog"
msgstr "Блога на Анна ↗"

msgid "page.ipfs_downloads.title"
msgstr "IPFS изтегляния"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Всички връзки за изтегляне на този файл:</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "ИПФС Вход #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(може да се наложи да пробвате няколко пъти с ИПФС)"

msgid "page.partner_download.faster_downloads"
msgstr "За да получите достъп до по-дързи изтегляния и за да пропуснете проверките на браузъра Ви, <a %(a_membership)s>станете член</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 За групово отразяване на нашата колекция вижте <a %(a_datasets)s>Набори от данни</a> и <a %(a_torrents)s>Торенти</a> страници."

msgid "page.llm.title"
msgstr "Данни за LLM"

msgid "page.llm.intro"
msgstr "Добре е известно, че LLM-ите процъфтяват върху висококачествени данни. Ние разполагаме с най-голямата колекция от книги, статии, списания и др. в света, които са едни от най-качествените текстови източници."

msgid "page.llm.unique_scale"
msgstr "Уникален мащаб и обхват"

msgid "page.llm.unique_scale.text1"
msgstr "Нашата колекция съдържа над сто милиона файла, включително академични списания, учебници и списания. Постигаме този мащаб, като комбинираме големи съществуващи хранилища."

msgid "page.llm.unique_scale.text2"
msgstr "Някои от нашите източници на колекции вече са налични в насипно състояние (Sci-Hub и части от Libgen). Други източници ние сме освободили сами. <a %(a_datasets)s>Datasets</a> показва пълен преглед."

msgid "page.llm.unique_scale.text3"
msgstr "Нашата колекция включва милиони книги, статии и списания от преди ерата на електронните книги. Големи части от тази колекция вече са сканирани и вече имат малко вътрешно припокриване."

msgid "page.llm.how_we_can_help"
msgstr "Как можем да помогнем"

msgid "page.llm.how_we_can_help.text1"
msgstr "Можем да предоставим високоскоростен достъп до нашите пълни колекции, както и до неиздадени колекции."

msgid "page.llm.how_we_can_help.text2"
msgstr "Това е достъп на корпоративно ниво, който можем да предоставим за дарения в размер на десетки хиляди USD. Също така сме готови да разменим това за висококачествени колекции, които все още нямаме."

msgid "page.llm.how_we_can_help.text3"
msgstr "Можем да ви възстановим сумата, ако можете да ни предоставите обогатяване на нашите данни, като например:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Премахване на припокриване (елиминирането на дублиращата се или излишна информация)"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Извличане на текст и метаданни"

msgid "page.llm.how_we_can_help.text4"
msgstr "Подкрепете дългосрочното архивиране на знанието, като същевременно получите по-добри данни за вашия модел!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Свържете се с нас</a>, за да обсъдим как можем да работим заедно."

msgid "page.login.continue"
msgstr "Продължи"

msgid "page.login.please"
msgstr "Моля, <a %(a_account)s>влезте в системата</a>, за да видите тази страница.</a>"

msgid "page.maintenance.header"
msgstr "Архивът на Анна временно не работи за поддръжка. Моля, върнете се след час."

msgid "page.metadata.header"
msgstr "Подобряване на метаданни"

msgid "page.metadata.body1"
msgstr "Можете да помогнете за съхранението на книги, като подобрите метаданните! Първо, прочетете информацията за метаданните в Архива на Анна, а след това научете как да подобрите метаданните чрез свързване с Open Library и спечелете безплатно членство в Архива на Анна."

msgid "page.metadata.background.title"
msgstr "Произход"

msgid "page.metadata.background.body1"
msgstr "Когато разглеждате книга в Архива на Анна, можете да видите различни полета: заглавие, автор, издател, издание, година, описание, име на файл и други. Всички тези части от информацията се наричат <em>метаданни</em>."

msgid "page.metadata.background.body2"
msgstr "Тъй като комбинираме книги от различни <em>библиотеки</em>, показваме каквито метаданни са налични в тази библиотека. Например, за книга, която сме получили от Library Genesis, ще покажем заглавието от базата данни на Library Genesis."

msgid "page.metadata.background.body3"
msgstr "Понякога една книга присъства в <em>няколко</em> библиотеки, които може да имат различни полета за метаданни. В такъв случай просто показваме най-дългата версия на всяко поле, тъй като тя вероятно съдържа най-полезната информация! Все пак ще покажем и другите полета под описанието, например като „алтернативно заглавие“ (но само ако са различни)."

msgid "page.metadata.background.body4"
msgstr "Също така извличаме <em>кодове</em> като идентификатори и класификатори от библиотеката. <em>Идентификаторите</em> уникално представят конкретно издание на книга; примери са ISBN, DOI, Open Library ID, Google Books ID или Amazon ID. <em>Класификаторите</em> групират заедно няколко подобни книги; примери са Dewey Decimal (DCC), UDC, LCC, RVK или GOST. Понякога тези кодове са изрично свързани в библиотеките, а понякога можем да ги извлечем от името на файла или описанието (главно ISBN и DOI)."

msgid "page.metadata.background.body5"
msgstr "Можем да използваме идентификаторите, за да намерим записи в <em>колекции само с метаданни</em>, като OpenLibrary, ISBNdb или WorldCat/OCLC. Има специален <em>раздел с метаданни</em> в нашата търсачка, ако искате да разгледате тези колекции. Използваме съвпадащи записи, за да запълним липсващите полета за метаданни (например ако липсва заглавие) или например като “алтернативно заглавие” (ако вече има съществуващо заглавие)."

msgid "page.metadata.background.body6"
msgstr "За да видите точно откъде са дошли метаданните на книгата, вижте <em>“Технически детайли” таб</em> на страницата на книгата. Има връзка към необработения JSON за тази книга, с указатели към необработения JSON на оригиналните записи."

msgid "page.metadata.background.body7"
msgstr "За повече информация, вижте следните страници: <a %(a_datasets)s>Набори от данни</a>, <a %(a_search_metadata)s>Търсене (раздел метаданни)</a>, <a %(a_codes)s>Изследовател на кодове</a> и <a %(a_example)s>Примерен JSON с метаданни </a>. Накрая, всички наши метаданни могат да бъдат <a %(a_generated)s>генерирани</a> или <a %(a_downloaded)s>изтеглени</a> като ElasticSearch и MariaDB бази данни."

msgid "page.metadata.openlib.title"
msgstr "Свързване с Open Library"

msgid "page.metadata.openlib.body1"
msgstr "И така, ако срещнете файл с лоши метаданни, как трябва да го поправите? Можете да отидете в библиотеката му и да следвате нейните процедури за поправка на метаданни, но какво да правите, ако файлът присъства в няколко библиотеки?"

msgid "page.metadata.openlib.body2"
msgstr "Има един идентификатор, който се третира специално в Архива на Анна. <strong>Полето annas_archive md5 в Open Library винаги замества всички други метаданни!</strong> Нека първо се върнем малко назад и да научим за Open Library."

msgid "page.metadata.openlib.body3"
msgstr "Open Library е основана през 2006 г. от Аарон Суорц с целта “една уеб страница за всяка книга, която е била публикувана някога”. Това е нещо като Wikipedia за метаданни на книги: всеки може да я редактира, тя е с безплатен лиценз и може да бъде изтеглена на части. Това е база данни за книги, която най-много съответства на нашата мисия — всъщност, Архивът на Анна е вдъхновен от визията и живота на Аарон Суорц."

msgid "page.metadata.openlib.body4"
msgstr "Вместо да преоткриваме колелото, решихме да насочим нашите доброволци към Open Library. Ако видите книга с неправилни метаданни, можете да помогнете по следния начин:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Отидете на <a %(a_openlib)s>сайта на Open Library</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Намерете правилния запис на книгата. <strong>ВНИМАНИЕ:</strong> бъдете сигурни, че сте избрали правилното <strong>издание</strong>. В Open Library има “произведения” и “издания”."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "“Произведение” може да бъде “Хари Потър и философският камък”."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "“Издание” може да бъде:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Първото издание от 1997 г., публикувано от Bloomsbery с 256 страници."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Изданието с меки корици от 2003 г., публикувано от Raincoast Books с 223 страници."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Полският превод от 2000 г. “Harry Potter I Kamie Filozoficzn” от Media Rodzina с 328 страници."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Всички тези издания имат различни ISBN и различно съдържание, така че бъдете сигурни, че избирате правилното!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Редактирайте записа (или го създайте, ако не съществува), и добавете колкото се може повече полезна информация! Вече сте тук, така че направете записа наистина невероятен."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Под “ID Numbers” изберете “Архивът на Анна” и добавете MD5 на книгата от Архивът на Анна. Това е дългият низ от букви и цифри след “/md5/” в адреса."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Опитайте се да намерите други файлове в Архивът на Анна, които също съответстват на този запис, и ги добавете също. В бъдеще можем да ги групираме като дубликати на страницата за търсене в Архивът на Анна."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Когато сте готови, запишете адреса, който току-що обновихте. След като обновихте поне 30 записа с MD5 от Архивът на Анна, изпратете ни <a %(a_contact)s>имейл</a> със списъка. Ще ви дадем безплатно членство за Архивът на Анна, за да можете по-лесно да вършите тази работа (и като благодарност за вашата помощ). Тези редакции трябва да бъдат с високо качество и да добавят значителни количества информация, в противен случай вашата заявка ще бъде отхвърлена. Вашата заявка също ще бъде отхвърлена, ако някоя от редакциите бъде отменена или коригирана от модераторите на Open Library."

msgid "page.metadata.openlib.body5"
msgstr "Имайте предвид, че това работи само за книги, а не за академични статии или други видове файлове. За други видове файлове все още препоръчваме да намерите библиотеката им. Може да отнеме няколко седмици, за да бъдат включени промените в Архивът на Анна, тъй като трябва да изтеглим последния Open Library архив и да обновим нашия индекс за търсене."

msgid "page.mirrors.title"
msgstr "Огледала: призив за доброволци"

msgid "page.mirrors.intro"
msgstr "За да увеличим устойчивостта на Архива на Анна, търсим доброволци за управление на огледалата."

msgid "page.mirrors.text1"
msgstr "Търсим това:"

msgid "page.mirrors.list.run_anna"
msgstr "Вие управлявате отворения код на Архивът на Анна и редовно обновявате както кода, така и данните."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Вашата версия е ясно разграничена като огледало, напр. “Архивът на Боб, огледало на Архивът на Анна”."

msgid "page.mirrors.list.know_the_risks"
msgstr "Вие сте готови да поемете рисковете, свързани с тази работа, които са значителни. Имате дълбоко разбиране за необходимата оперативна сигурност. Съдържанието на <a %(a_shadow)s>тези</a> <a %(a_pirate)s>публикации</a> е очевидно за вас."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "Вие сте готови да допринесете за нашия <a %(a_codebase)s>код</a> — в сътрудничество с нашия екип — за да направите това възможно."

msgid "page.mirrors.list.maybe_partner"
msgstr "Първоначално няма да ви дадем достъп до изтеглянията от нашия партньорски сървър, но ако нещата вървят добре, можем да споделим това с вас."

msgid "page.mirrors.expenses.title"
msgstr "Разходи за хостинг"

msgid "page.mirrors.expenses.text1"
msgstr "Ние сме готови да покрием разходите за хостинг и VPN, първоначално до $200 на месец. Това е достатъчно за основен сървър за търсене и прокси, защитен от DMCA."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Ще плащаме за хостинг само след като всичко е настроено и сте демонстрирали, че можете да поддържате архива с обновени данни. Това означава, че ще трябва да платите за първите 1-2 месеца от джоба си."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Вашето време няма да бъде компенсирано (и нашето също), тъй като това е чисто доброволческа работа."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Ако се включите значително в разработката и операциите на нашата работа, можем да обсъдим споделянето на повече от приходите от дарения с вас, за да ги използвате при необходимост."

msgid "page.mirrors.getting_started.title"
msgstr "Първи стъпки"

msgid "page.mirrors.getting_started.text1"
msgstr "Моля, <strong>не се свързвайте с нас</strong> за разрешение или за основни въпроси. Действията говорят по-силно от думите! Цялата информация е налична, така че просто продължете с настройването на вашето огледало."

msgid "page.mirrors.getting_started.text2"
msgstr "Чувствайте се свободни да публикувате билети или заявки за сливане в нашия Gitlab, когато срещнете проблеми. Може да се наложи да изградим някои специфични за огледалото функции с вас, като например ребрандиране от “Архивът на Анна” към името на вашия сайт, (първоначално) деактивиране на потребителски акаунти или свързване обратно към нашия основен сайт от страниците на книгите."

msgid "page.mirrors.getting_started.text3"
msgstr "След като вашето огледало работи, моля, свържете се с нас. Ще се радваме да прегледаме вашата OpSec, и след като това е стабилно, ще свържем към вашето огледало и ще започнем да работим по-близо с вас."

msgid "page.mirrors.getting_started.text4"
msgstr "Предварително благодарим на всеки, който е готов да допринесе по този начин! Това не е за слабите сърца, но ще затвърди дълголетието на най-голямата ,наистина, отворена библиотека в историята."

msgid "page.partner_download.header"
msgstr "Изтегли от партньорски уебсайт"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Бавните изтегляния са достъпни само през официалния уебсайт. Посетете %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Бавните изтегляния не са налични чрез Cloudflare VPN или от IP адреси на Cloudflare."

msgid "page.partner_download.wait_banner"
msgstr "Моля, изчакайте <span %(span_countdown)s>%(wait_seconds)s</span> секунди, за да изтеглите този файл."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Изтеглете сега</a>"

msgid "page.partner_download.li4"
msgstr "Благодарим ви за търпението, това поддържа сайта безплатен за всички! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Внимание: имаше много изтегляния от вашия IP адрес през последните 24 часа. Изтеглянията може да са по-бавни от обикновено."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Изтегляния от вашия IP адрес през последните 24 часа: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Ако използвате VPN, споделена интернет връзка или вашият интернет доставчик споделя IP адреси, това предупреждение може да се дължи на това."

msgid "page.partner_download.wait"
msgstr "За да дадете възможност на всички да изтеглят файловете безплатно, трябва да изчакате, преди да можете да изтеглите този файл."

msgid "page.partner_download.li1"
msgstr "Чувствайте се свободни да продължите да разглеждате Архивът на Анна в друг раздел, докато чакате (ако вашият браузър поддържа опресняване на фоновите раздели)."

msgid "page.partner_download.li2"
msgstr "Чувствайте се свободни да изчакате зареждането на няколко страници за изтегляне едновременно (но моля, изтегляйте само един файл едновременно на сървър)."

msgid "page.partner_download.li3"
msgstr "След като получите връзка за изтегляне, той е валиден за няколко часа."

msgid "layout.index.header.title"
msgstr "Архивът на Анна"

msgid "page.scidb.header"
msgstr "SciDB - система за управление на база данни (СУБД) с колони"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s цифров идентификатор на обект"

msgid "page.scidb.aa_record"
msgstr "Запис в архива на Анна"

msgid "page.scidb.download"
msgstr "Изтегли"

msgid "page.scidb.scihub"
msgstr "Sci-Hub библиотека за научни статии"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "За да подкрепите достъпността и дългосрочното съхранение на това знание, станете <a %(a_donate)s>член</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "Като бонус, 🧬&nbsp;SciDB се зарежда по-бързо за членовете, без никакви ограничения."

msgid "page.scidb.refresh"
msgstr "Не работи? Опитай <a %(a_refresh)s>обновяване на браузъра си</a>."

msgid "page.scidb.no_preview_new"
msgstr "Все още няма наличен преглед. Изтеглете файла от <a %(a_path)s>Архивът на Анна</a>."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB е продължение на Sci-Hub, с познатия интерфейс и директно преглеждане на PDF файлове. Въведете вашия DOI, за да прегледате."

msgid "page.home.scidb.text3"
msgstr "Имаме пълната колекция на Sci-Hub, както и нови статии. Повечето могат да се разглеждат директно с познат интерфейс, подобен на Sci-Hub. Някои могат да се изтеглят чрез външни източници, в такъв случай показваме връзки към тях."

msgid "page.search.title.results"
msgstr "%(search_input)s - Търси"

msgid "page.search.title.new"
msgstr "Ново търсене"

msgid "page.search.icon.include_only"
msgstr "Включване само"

msgid "page.search.icon.exclude"
msgstr "Изключете"

msgid "page.search.icon.unchecked"
msgstr "Непроверено"

msgid "page.search.tabs.download"
msgstr "Download"

msgid "page.search.tabs.journals"
msgstr "Статии от списания"

msgid "page.search.tabs.digital_lending"
msgstr "Дигитално кредитиране"

msgid "page.search.tabs.metadata"
msgstr "Метаданни"

msgid "common.search.placeholder"
msgstr "Заглавие за търсене, автор, език, тип файл, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Търси"

msgid "page.search.search_settings"
msgstr "Настройки за търсене"

msgid "page.search.submit"
msgstr "Търсене"

msgid "page.search.too_long_broad_query"
msgstr "Търсенето отне твърде дълго време, което е обичайно за обширни заявки. Преброяването на филтъра може да не е точно."

msgid "page.search.too_inaccurate"
msgstr "Търсенето отне твърде дълго време, което означава, че може да видите неточни резултати. Понякога <a %(a_reload)s>презареждането</a> на страницата помага."

msgid "page.search.filters.display.header"
msgstr "Показване"

msgid "page.search.filters.display.list"
msgstr "Списък"

msgid "page.search.filters.display.table"
msgstr "Таблица"

msgid "page.search.advanced.header"
msgstr "Разширено"

msgid "page.search.advanced.description_comments"
msgstr "Търсене на описания и коментари за метаданни"

msgid "page.search.advanced.add_specific"
msgstr "Добавяне на специфично поле за търсене"

msgid "common.specific_search_fields.select"
msgstr "(търсене в конкретно поле)"

msgid "page.search.advanced.field.year_published"
msgstr "Година на публикуване"

msgid "page.search.filters.content.header"
msgstr "Съдържание"

msgid "page.search.filters.filetype.header"
msgstr "Файлов тип"

msgid "page.search.more"
msgstr "Повече …"

msgid "page.search.filters.access.header"
msgstr "Достъп"

msgid "page.search.filters.source.header"
msgstr "Източник"

msgid "page.search.filters.source.scraped"
msgstr "извлечени и с отворен код от AA"

msgid "page.search.filters.language.header"
msgstr "Език"

msgid "page.search.filters.order_by.header"
msgstr "Подредени по"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Най-уместени"

msgid "page.search.filters.sorting.newest"
msgstr "Най-нови"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(година на публикация)"

msgid "page.search.filters.sorting.oldest"
msgstr "Най-стари"

msgid "page.search.filters.sorting.largest"
msgstr "Най-голям"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(размер на файла)"

msgid "page.search.filters.sorting.smallest"
msgstr "Малък"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(с отворен код)"

msgid "page.search.filters.sorting.random"
msgstr "Случайно"

msgid "page.search.header.update_info"
msgstr "Индексът за търсене се актуализира ежемесечно. В момента включва записи до %(last_data_refresh_date)s. За повече техническа информация вижте %(link_open_tag)sстраница с набори от данни</a>."

msgid "page.search.header.codes_explorer"
msgstr "За да разгледате индекса за търсене по кодове, използвайте <a %(a_href)s>Codes Explorer</a>."

msgid "page.search.results.search_downloads"
msgstr "Въведете в полето, за да търсите в нашия каталог с файлове %(count)s за директно изтегляне, които ние<a %(a_preserve)s>запазваме завинаги</a>."

msgid "page.search.results.help_preserve"
msgstr "Всъщност, всеки може да помогне за запазването на тези файлове, като сийдва нашия <a %(a_torrents)s>обединен списък с торенти</a>."

msgid "page.search.results.most_comprehensive"
msgstr "В момента разполагаме с най-изчерпателния отворен каталог в света на книгите, документите и другите писмени произведения. Ние отразяваме със Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>и още</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Ако намерите други “библиотеки в сянка”, които трябва да отразяваме, или ако имате въпроси, свържете се с нас на %(email)s."

msgid "page.search.results.dmca"
msgstr "За искове по DMCA / авторски права <a %(a_copyright)s>click here</a>."

msgid "page.search.results.shortcuts"
msgstr "Съвет: използвайте клавишни комбинации “/” (фокус на търсенето), въведете (Търсене), “j” (нагоре), “k” (надолу) за по-бърза навигация."

msgid "page.search.results.looking_for_papers"
msgstr "Търсите статии?"

msgid "page.search.results.search_journals"
msgstr "Въведете в полето, за да търсите в нашия каталог от %(count)s академични статии и журнали, които <a %(a_preserve)s>съхраняваме завинаги</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Въведете в полето, за да търсите файлове в цифрови библиотеки за заемане."

msgid "page.search.results.digital_lending_info"
msgstr "Този индекс за търсене в момента включва метаданни от библиотеката за контролирано цифрово заемане на интернет архива. <a %(a_datasets)s>Повече за нашите набори от данни</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "За още цифрови библиотеки за заемане вижте <a %(a_wikipedia)s>Wikipedia</a> и <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Въведете в полето, за да търсите метаданни от библиотеки. Това може да бъде полезно, когато <a %(a_request)s>искане на файл</a>."

msgid "page.search.results.metadata_info"
msgstr "Този индекс за търсене в момента включва метаданни от различни източници на метаданни. <a %(a_datasets)s>Повече за нашите набори от данни</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "За метаданни показваме оригиналните записи. Не правим никакво сливане на записи."

msgid "page.search.results.metadata_info_more"
msgstr "Има много, много източници на метаданни за писмени произведения по света. <a %(a_wikipedia)s>Това Wikipedia страница</a> е добро начало, но ако знаете за други добри списъци, моля, уведомете ни."

msgid "page.search.results.search_generic"
msgstr "Въведете в полето, за да търсите."

msgid "page.search.results.these_are_records"
msgstr "Това са записи на метаданни, <span %(classname)s>не</span> файлове за изтегляне."

msgid "page.search.results.error.header"
msgstr "Грешка при търсенето."

msgid "page.search.results.error.unknown"
msgstr "Опитайте <a %(a_reload)s>презареждане на страницата</a>. Ако проблемът продължава, изпратете ни имейл на %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Няма намерени файлове.</span> Опитайте с по-малко или различни думи за търсене и филтри."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Понякога това се случва неправилно, когато сървърът за търсене е бавен. В такива случаи, <a %(a_attrs)s>презареждането</a> може да помогне."

msgid "page.search.found_matches.main"
msgstr "Открихме съвпадения във: %(in)s. Можете да се обърнете към URL адреса, намерен там, когато <a %(a_request)s>искане на файл</a>."

msgid "page.search.found_matches.journals"
msgstr "Статии от списания (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Дигитално кредитиране (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Резултати %(from)s-%(to)s (%(total)s общо)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ частични съвпадения"

msgid "page.search.results.partial"
msgstr "%(num)d частични съвпадения"

msgid "page.volunteering.title"
msgstr "Доброволчество & награди"

msgid "page.volunteering.intro.text1"
msgstr "Архивът на Анна разчита на доброволци като вас. Приветстваме всички нива на ангажираност и имаме две основни категории помощ, които търсим:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Лека доброволческа работа:</span> ако можете да отделите само няколко часа тук и там, все още има много начини, по които можете да помогнете. Награждаваме последователните доброволци с <span %(bold)s>🤝 членства в Архивът на Анна</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Тежка доброволческа работа (награди от 50 до 5,000 USD):</span> ако можете да отделите много време и/или ресурси за нашата мисия, ще се радваме да работим по-тясно с вас. В крайна сметка можете да се присъедините към вътрешния екип. Въпреки че имаме ограничен бюджет, можем да присъждаме <span %(bold)s>💰 парични награди</span> за най-интензивната работа."

msgid "page.volunteering.intro.text2"
msgstr "Ако не можете да отделите време за доброволчество, все пак можете да ни помогнете много, като <a %(a_donate)s>дарите пари</a>, <a %(a_torrents)s>споделите нашите торенти</a>, <a %(a_uploading)s>качите книги</a> или <a %(a_help)s>разкажете на приятелите си за Архивът на Анна</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Компании:</span> предлагаме високоскоростен директен достъп до нашите колекции в замяна на дарение на корпоративно ниво или в замяна на нови колекции (например нови сканирания, OCR’ed datasets, обогатяване на нашите данни). <a %(a_contact)s>Свържете се с нас</a>, ако това сте вие. Вижте също нашата <a %(a_llm)s>страница за LLM</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Леко доброволчество"

msgid "page.volunteering.section.light.text1"
msgstr "Ако имате няколко свободни часа, можете да помогнете по различни начини. Не забравяйте да се присъедините към <a %(a_telegram)s>чата на доброволците в Telegram</a>."

msgid "page.volunteering.section.light.text2"
msgstr "Като знак на признателност, обикновено даваме 6 месеца “Късметлийски библиотекар” за основни постижения и повече за продължителна доброволческа работа. Всички постижения изискват висококачествена работа — небрежната работа ни вреди повече, отколкото ни помага, и ще я отхвърлим. Моля, <a %(a_contact)s>пишете ни по имейл</a>, когато достигнете някакъв етап."

msgid "page.volunteering.table.header.task"
msgstr "Задача"

msgid "page.volunteering.table.header.milestone"
msgstr "Етап"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Разпространяване на информация за Архивът на Анна. Например, чрез препоръчване на книги в AA, свързване към нашите блог публикации или общо насочване на хората към нашия сайт."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s връзки или екранни снимки."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Тези трябва да показват как уведомявате някого за Архивът на Анна и как той ви благодари."

msgid "page.volunteering.table.open_library.task"
msgstr "Подобряване на метаданни чрез <a %(a_metadata)s>свързване</a> с Open Library."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Можете да използвате <a %(a_list)s>списъка със случайни проблеми с metadata</a> като отправна точка."

msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Уверете се, че оставяте коментар за проблемите, които решавате, за да не дублират другите вашата работа."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s връзки на записи, които сте подобрили."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Превод</a> на сайта."

msgid "page.volunteering.table.translate.milestone"
msgstr "Пълен превод на език (ако не е бил близо до завършване вече.)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "Подобряване на страницата в Wikipedia за Архивът на Анна на вашия език. Включете информация от страницата на AA в Wikipedia на други езици и от нашия сайт и блог. Добавете препратки към AA на други подобни страници."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Връзка към историята на редакциите, показваща, че сте направили значителни приноси."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Изпълнение на заявки за книги (или статии и т.н.) във форумите на Z-Library или Library Genesis. Нямаме собствена система за заявки на книги, но отразяваме тези библиотеки, така че подобряването им прави и Архивът на Анна по-добър."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s връзки или екранни снимки на заявки, които сте изпълнили."

msgid "page.volunteering.table.misc.task"
msgstr "Малки задачи, публикувани в нашия <a %(a_telegram)s>чат на доброволците в Telegram</a>. Обикновено за членство, понякога за малки награди."

msgid "page.volunteering.table.misc.task2"
msgstr "Малки задачи, публикувани в нашата група на чата за доброволците."

msgid "page.volunteering.table.misc.milestone"
msgstr "Зависи от задачата."

msgid "page.volunteering.section.bounties.heading"
msgstr "Награди"

msgid "page.volunteering.section.bounties.text1"
msgstr "Винаги търсим хора със солидни умения в програмирането или офанзивната сигурност, които да се включат. Можете да направите сериозен принос в запазването на наследството на човечеството."

msgid "page.volunteering.section.bounties.text2"
msgstr "Като благодарност, даваме членство за солидни приноси. Като огромна благодарност, даваме парични награди за особено важни и трудни задачи. Това не трябва да се разглежда като заместител на работа, но е допълнителен стимул и може да помогне с възникналите разходи."

msgid "page.volunteering.section.bounties.text3"
msgstr "Повечето от нашия код е с отворен код, и ще поискаме същото и от вашия код, когато присъждаме наградата. Има някои изключения, които можем да обсъдим на индивидуална основа."

msgid "page.volunteering.section.bounties.text4"
msgstr "Наградите се присъждат на първия човек, който завърши задачата. Чувствайте се свободни да коментирате билет за награда, за да уведомите другите, че работите по нещо, така че другите да изчакат или да се свържат с вас, за да се обедините. Но бъдете наясно, че другите все още са свободни да работят по нея и да се опитат да ви изпреварят. Въпреки това, не присъждаме награди за небрежна работа. Ако две висококачествени предложения бъдат направени близо едно до друго (в рамките на ден или два), може да решим да присъдим награди и на двете, по наше усмотрение, например 100%% за първото предложение и 50%% за второто предложение (така общо 150%%)."

msgid "page.volunteering.section.bounties.text5"
msgstr "За по-големите награди (особено за наградите за скрапинг), моля свържете се с нас, когато сте завършили ~5%% от нея и сте уверени, че вашият метод ще се мащабира до пълния етап. Ще трябва да споделите метода си с нас, за да можем да дадем обратна връзка. Също така, по този начин можем да решим какво да правим, ако има няколко души, които се доближават до наградата, като например потенциално да я присъдим на няколко души, да насърчим хората да се обединят и т.н."

msgid "page.volunteering.section.bounties.text6"
msgstr "ПРЕДУПРЕЖДЕНИЕ: задачите с висока награда са <span %(bold)s>трудни</span> — може би е разумно да започнете с по-лесни."

msgid "page.volunteering.section.bounties.text7"
msgstr "Отидете на нашия <a %(a_gitlab)s>списък с проблеми в Gitlab</a> и сортирайте по “Приоритет на етикета”. Това показва приблизителния ред на задачите, които ни интересуват. Задачите без изрични награди все още са допустими за членство, особено тези, маркирани като “Приети” и “Любими на Анна”. Може да искате да започнете с “Начален проект”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Актуализации за <a %(wikipedia_annas_archive)s>Архивът на Анна</a>, най-голямата наистина отворена библиотека в човешката история."

msgid "layout.index.title"
msgstr "Архивът на Анна"

msgid "layout.index.meta.description"
msgstr "Най-голямата в света библиотека с отворени данни с отворен код. Огледала Sci-Hub, Library Genesis, Z-Library и др."

msgid "layout.index.meta.opensearch"
msgstr "Търсене в архива на Анна"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Архивът на Анна се нуждае от вашата помощ!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Много се опитват да ни свалят, но ние се борим да отвърнем на удара."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Ако дарите сега, получавате <strong>двойно</strong> повече бързи изтегляния."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Валидно до края на този месец."

msgid "layout.index.header.nav.donate"
msgstr "Дари"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Спасяване на човешкото знание: страхотен празничен подарък!"

msgid "layout.index.header.banner.surprise"
msgstr "Изненадайте любим човек, подарете му акаунт с членство."

msgid "layout.index.header.banner.mirrors"
msgstr "За да увеличим устойчивостта на Архивът на Анна, търсим доброволци за поддържане на огледални сървъри."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Перфектният подарък за Свети Валентин!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Разполагаме с нов метод за дарение: %(method_name)s. Моля имайте предвид %(donate_link_open_tag)sдаряване</a> — не е евтино да управлявате този уебсайт и вашето дарение наистина има значение. Много благодаря."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Провеждаме кампания за набиране на средства за <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">архивиране</a> най-голямата комиксова библиотека в света. Благодаря за подкрепата! <a href=\"/donate\">Дарете.</a> Ако не можете да дарите, помислете дали да ни подкрепите, като кажете на приятелите си и ни последвате <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Последни изтегляния:"

msgid "layout.index.header.nav.search"
msgstr "Търси"

msgid "layout.index.header.nav.faq"
msgstr "ЧЗВ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Подобрете метаданните"

msgid "layout.index.header.nav.volunteering"
msgstr "Доброволчество & награди"

msgid "layout.index.header.nav.datasets"
msgstr "Датасетс"

msgid "layout.index.header.nav.torrents"
msgstr "Торенти"

msgid "layout.index.header.nav.activity"
msgstr "Активност"

msgid "layout.index.header.nav.codes"
msgstr "Изследовател на кодове"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM данни"

msgid "layout.index.header.nav.home"
msgstr "Начална страница"

msgid "layout.index.header.nav.annassoftware"
msgstr "Софтуерът на Анна ↗"

msgid "layout.index.header.nav.translate"
msgstr "Превеждане ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Вход / Регистрация"

msgid "layout.index.header.nav.account"
msgstr "Акаунт"

msgid "layout.index.footer.list1.header"
msgstr "Архивът на Анна"

msgid "layout.index.footer.list2.header"
msgstr "Поддържате връзка"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / искове за авторски права"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Разширено"

msgid "layout.index.header.nav.security"
msgstr "Сигурност"

msgid "layout.index.footer.list3.header"
msgstr "Алтернативи"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "несвързан"

msgid "page.search.results.issues"
msgstr "❌ Този файл може да има проблеми."

msgid "page.search.results.fast_download"
msgstr "Бързо изтегляне"

msgid "page.donate.copy"
msgstr "копиране"

msgid "page.donate.copied"
msgstr "копирано!"

msgid "page.search.pagination.prev"
msgstr "Предишен"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Следващ"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Огледало #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr ""

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% от писменото наследство на човечеството, запазено завинаги %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Датасет ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Изтеглете безплатна електронна книга/файл %(extension)s от:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr ""

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Анонимно Огледало #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Дарете"

#~ msgid "page.donate.header"
#~ msgstr "Дарете"

#~ msgid "page.donate.text1"
#~ msgstr "Архивър на Анна е опън сорс проект с нестопанска цел, опериран изцяло от доброволци. Ние събираме дарения, за да покрием разходите си, който включват хостинг, домейни, разработка, и други разходи."

#~ msgid "page.donate.text2"
#~ msgstr "С вашите дарения, ние можем да поддържаме уебсайта, подобряваме неговите функции, и да запаьим повече колекции."

#~ msgid "page.donate.text3"
#~ msgstr "Скорошни дарения: %(donations)s. Благодарим на вички за вашата щедрост. Много оценяваме, че поставихте доверието си в нас с каквото количесто сте могли."

#~ msgid "page.donate.text4"
#~ msgstr "За да дарите, избетете Вашия предпочитън способ долу. Ако срещнете затруднения, моля, свържете се с нас на %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Кредитна/Дебитна карта"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Крипто"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Въпроси"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr ""

#~ msgid "page.donate.cc.header"
#~ msgstr "Кредитна/дебитна карта"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Ние ползваме Sendwyre, за да внесем парите директно в нашия Bitcoin (BTC) портфейл. Това отнема около 5 минути."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Този метод има минимална сума на транзакцията от $30 и такса от около $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Стъпки:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Копирайте адреса на нашия Bitcoin (BTC) портфейл: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Отидете на %(link_open_tag)sтази страница</a> и натиснете върху „купете криптовалута незабавно“"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Поставете адреса на нашия портфейл и следвайте инструкциите"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Крипто"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(също работи за BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Моля, използвайте този %(link_open_tag)sакаунт в Alipay</a>, за да изпратите вашето дарение."

#~ msgid "page.donate.alipay.url"
#~ msgstr ""

#~ msgid "page.donate.out_of_order"
#~ msgstr ""

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.header"
#~ msgstr "Често задавани въпроси"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Архивът на Анна</span> е проект, който има за цел да каталогизира всички съществуващи книги чрез обобщаване на данни от различни източници. Ние също така проследяваме напредъка на човечеството към правенето на всички тези книги лесно достъпни в цифрова форма чрез „<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">скрити библиотеки</a>“. Научете повече <a href=\"/about\">за нас.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Книга (всякаква)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Дом"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Датасет ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Не е намерено"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "„%(isbn_input)s“ не е валиден ISBN номер. ISBN номерата са дълги 10 или 13 знака, без да се броят незадължителните тирета. Всички символи трябва да са числа, с изключение на последния знак, който също може да бъде „X“. Последният знак е „контролната цифра“, която трябва да съответства на стойността на контролната сума, изчислена от другите числа. Освен това трябва да бъде в валиден диапазон, определен от Международната агенция за ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Съвпадащи файлове в нашата база данни:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Няма намерени съответстващи файлове в нашата база данни."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Търсете ▶ %(num)d+ резултата за <span class=\"italic\">%(search_input)s</span> (в метаданни на скрита библиотека)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Търсете ▶ %(num)d резултата за <span class=\"italic\">%(search_input)s</span> (в метаданни на скритата библиотека)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Търсене ▶ Грешка при търсене на <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Търсене ▶ Ново търсене"

#~ msgid "page.donate.header.text3"
#~ msgstr "Можете също да направите дарение без да създавате акаунт:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Еднократно дарение (без предимства)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Изберете опция за плащане. Моля, обмислете използването на крипто-базирано плащане %(bitcoin_icon)s, тъй като имаме (много) по-малко такси."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Ако вече имате крипто пари, това са нашите адреси."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Благодарим ви много за помощта! Този проект не би бил възможен без вас."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "За да даряваме чрез PayPal, ще използваме PayPal Crypto, което ни позволява да останем анонимни. Оценяваме, че отделихте време, за да научите как да дарявате чрез този метод, тъй като ни помага много."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Следвайте инструкциите, за да закупите биткойн (BTC). Трябва само да закупите сумата, която искате да дарите."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Ако загубите малко биткойни поради колебания или такси, <em>моля, не се притеснявайте</em>. Това е нормално за криптовалутата, но ни позволява да работим анонимно."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Въведете своя биткойн (BTC) адрес като получател и следвайте инструкциите, за да изпратите своето дарение:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Използвайте <a %(a_account)s>този акаунт в Alipay</a> за да изпратите вашето дарение."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Използвайте <a %(a_account)s>този Pix акаунт</a> за да изпратите вашето дарение."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Ако вашият начин на плащане не е в списъка, най-лесното нещо, което можете да направите, е да изтеглите <a href=\"https://paypal.com/\">PayPal</a> или <a href=\"https://coinbase.com/\">Coinbase</a> на телефона си и купете малко биткойн (BTC) там. След това можете да го изпратите на нашия адрес: %(address)s. В повечето страни настройката трябва да отнеме само няколко минути."

#~ msgid "page.search.results.error.text"
#~ msgstr "Опитайте <a href=\"javascript:location.reload()\">да презаредите страницата</a>. Ако проблемът продължава, моля, уведомете ни в <a href=\"https://www.reddit.com/r/Annas_Archive/ \">Reddit</a> или <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "За да станете член <a href=\"/login\">Влезте или се Регистрирайте</a>. Ако предпочитате да не създавате акаунт, изберете “Направете еднократно анонимно дарение” по-горе. Благодаря за подкрепата!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Дом"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "За нас"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Дари"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Датасетс"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Мобилно приложение"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Блогът на Анна"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Софтуерът на Анна"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Превеждане"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s торенти"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Огледала %(libraries)s, и още."

#~ msgid "page.home.preservation.text"
#~ msgstr "Ние съхраняваме книги, документи, комикси, списания и други, като събираме тези материали от различни <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">„сенчести библиотеки“</a> на едно място. Всички тези данни се запазват завинаги, като се улеснява тяхното масово копиране, което води до многобройни копия по целия свят. Това широко разпространение, съчетано с кода с отворен код, също прави нашия уебсайт устойчив на сваляне. Научете повече за <a href=\"/datasets\">нашите набори от данни</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Датасет ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Не е намерено"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" не прилича на DOI. Трябва да започва с \"10.\" и да има наклонена черта \"/\" в него."

#~ msgid "page.doi.box.header"
#~ msgstr "идентификатор на цифров обект:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Каноничен URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Този файл може де е в %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Сходни файлове в нашата база данни:"

#~ msgid "page.doi.results.none"
#~ msgstr "Не са открити сходни файлове в нашата база данни."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Бързи изтелгяния</strong> Броят на бързите изтегляния за днес е изчерпан. Моля, свържете се с Анна чрез %(email)s, ако се интересувате от надграждане на членството Ви."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Броят на бързите изтегляния за днес е изчерпан. Моля, свържете се с Анна чрез %(email)s, ако се интересувате от надграждане на членството Ви."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Мога ли да допринеса по други начини?</div> Да! Вижте <a href=\"/about\">страницата с информация</a> под „Как да помогна“."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Не ми харесва, че “монетизирате” архива на Анна!</div>Ако не харесвате начина, по който работим с нашия проект, стартирайте своя собствена библиотека в сянка! Целият ни код и данни са с отворен код, така че нищо не ви спира. ;)"

#~ msgid "page.request.title"
#~ msgstr "Направете заявка за книги"

#~ msgid "page.request.text1"
#~ msgstr "Засега можете ли да отправяте запитвания за електронни книги във форума <a %(a_forum)s>Libgen.rs</a>? Можете да създадете акаунт там и да публикувате в някоя от тези теми:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>За електронни книги, използвайте <a %(a_ebook)s>тази тема</a>.</li><li %(li_item)s>За книгите, които не са достъпни като електронни книги използвайте <a %(a_regular)s>тази тема</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "И в двата случая не забравяйте да спазвате правилата, посочени в темите."

#~ msgid "page.upload.title"
#~ msgstr "Качване"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr "Големи качвания"

#~ msgid "page.about.title"
#~ msgstr "За нас"

#~ msgid "page.about.header"
#~ msgstr "За нас"

#~ msgid "page.home.search.header"
#~ msgstr "Търси"

#~ msgid "page.home.search.intro"
#~ msgstr "Търсете в нашия каталог от скрити библиотеки."

#~ msgid "page.home.random_book.header"
#~ msgstr "Случаена книга"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Вижте произволно намерена книга от каталога."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Случаена книга"

#~ msgid "page.about.text1"
#~ msgstr "Anna’s Archive е търсачка с нестопанска цел с отворен код за “<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">библиотеки в сянка</a>”. Създаден е от <a href=\"http://annas-blog.org\">Анна</a>, която смята, че има нужда от централно място за търсене на книги, вестници, комикси, списания и други документи."

#~ msgid "page.about.text4"
#~ msgstr "Ако имате валидно оплакване тип „DMCA“, вижте долната част на тази страница или се свържете с нас: %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Разгледай книги"

#~ msgid "page.home.explore.intro"
#~ msgstr "Това са комбинация от популярни книги и книги, които имат специално значение за света на скритите библиотеки и цифровото съхранение."

#~ msgid "page.wechat.header"
#~ msgstr "Неофициален WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "Имаме неофициална страница в WeChat, поддържан от член на общността. Използвайте кода по-долу за достъп."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "За нас"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Мобилно приложение"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Неофициален WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Поискани книги"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Качване"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Как да помогна"

#~ msgid "page.refer.title"
#~ msgstr "Препоръчайте приятели, за да получите бонуса за бързо изтегляне"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Членовете могат да препоръчват приятели и да печелят бонус изтегляния."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "За всеки приятел, който стане член:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Те</strong> получават %(percentage)s%% бонус изтегляния в допълнение към редовните ежедневни изтегляния за срока на тяхното членство."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Вие</strong> получавате същия брой бонус изтегляния в допълнение към обичайните ви ежедневни изтегляния за същата продължителност, за която вашият приятел се е регистрирал (до общо .%(max)s общи бонус изтегляния във всеки даден момент време). Трябва да поддържате активно членство, за да използвате вашите бонус изтегляния."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Пример:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Приятелят ви използва препоръчващата ви връзка, за да се регистрира за 3-месечно членство в “Lucky Librarian”, което идва с %(num)s бързи изтегляния."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Те получават %(num)s бонус изтегляния всеки ден за всичките тези 3 месеца."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Освен това получавате %(num)s бонус изтегляния всеки ден за същите 3 месеца."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Препоръчваща връзка:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Влизане</a> и ставане на член, за да насочвате приятелите си."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Станете член</a>, за да насочвате приятелите си."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Или добавете %(referral_suffix)s в края на всяка друга връзка и рефералът ще бъде запомнен, когато стане член."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Дарете общата сума от %(total)s, като използвате <a %(a_account)s>този акаунт в Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "Освен това можете да ги качите в Z-Library <a %(a_upload)s>тук</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "само този месец!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub е <a %(a_closed)s>спрял</a> качването на нови статии."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Изберете опция за плащане. Ние даваме отстъпки за крипто-базирани плащания %(bitcoin_icon)s, защото имаме (много) по-малко такси."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Изберете опция за плащане. В момента имаме само крипто-базирани плащания %(bitcoin_icon)s, тъй като традиционните оператори за обработка на плащания отказват да работят с нас."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Не можем да поддържаме директно кредитни/дебитни карти, защото банките не искат да работят с нас. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Въпреки това, има няколко начина да използвате кредитни/дебитни карти, използвайки нашите други методи на плащане:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Бавни и външни изтегляния"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Изтегляния"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Ако използвате крипто валута за първи път, предлагаме да използвате %(option1)s, %(option2)s или %(option3)s, за да купувате и дарявате биткойн (оригиналната и най-използваната криптовалута)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 връзки на записи, които сте подобрили."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 връзки или скрийншотове."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 връзки или скрийншотове на изпълнени заявки."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Ако се интересувате от огледално копиране на тези datasets за <a %(a_faq)s>архивиране</a> или <a %(a_llm)s>обучение на LLM</a>, моля свържете се с нас."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Ако се интересувате от огледално копиране на този набор от данни за <a %(a_archival)s>архивиране</a> или за <a %(a_llm)s>обучение на LLM</a>, моля, свържете се с нас."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Основен уебсайт"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Информация за страните по ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Ако се интересувате от огледално копиране на този набор от данни за <a %(a_archival)s>архивни</a> или <a %(a_llm)s>LLM обучение</a> цели, моля свържете се с нас."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Международната агенция за ISBN редовно публикува диапазоните, които е разпределила на националните агенции за ISBN. От това можем да определим към коя страна, регион или езикова група принадлежи този ISBN. В момента използваме тези данни косвено, чрез Python библиотеката <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Ресурси"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Последно обновено: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Уебсайт за ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Метаданни"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Изключвайки “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Нашето вдъхновение за събиране на метаданни е целта на Аарон Суорц за „една уеб страница за всяка книга, която някога е била публикувана“, за която той създаде <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Този проект се справи добре, но нашата уникална позиция ни позволява да получим метаданни, които те не могат."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Друга вдъхновение беше нашето желание да знаем <a %(a_blog)s>колко книги има в света</a>, за да можем да изчислим колко книги все още трябва да спасим."

#~ msgid "page.partner_download.text1"
#~ msgstr "За да дадем възможност на всички да изтеглят файлове безплатно, трябва да изчакате <strong>%(wait_seconds)s секунди</strong> преди да можете да изтеглите този файл."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Автоматично обновяване на страницата. Ако пропуснете прозореца за изтегляне, таймерът ще се рестартира, така че автоматичното обновяване е препоръчително."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Свали сега"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Конвертиране: използвайте онлайн инструменти за конвертиране между формати. Например, за конвертиране между epub и pdf, използвайте <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: изтеглете файла (поддържат се pdf или epub), след това <a %(a_kindle)s>изпратете го на Kindle</a> чрез уеб, приложение или имейл. Полезни инструменти: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Подкрепете авторите: Ако харесвате това и можете да си го позволите, помислете за закупуване на оригинала или за подкрепа на авторите директно."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Поддържайте библиотеките: Ако това е налично във вашата местна библиотека, помислете дали да не го заемете безплатно от там."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Не е налично директно в големи количества, само в полу-големи количества зад платена стена"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Архивът на Анна управлява колекция от <a %(isbndb)s>метаданни на ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb е компания, която събира метаданни за ISBN от различни онлайн книжарници. Архивът на Анна прави резервни копия на метаданните за книги от ISBNdb. Тези метаданни са достъпни чрез Архива на Анна (въпреки че в момента не са включени в търсенето, освен ако не търсите конкретно ISBN номер)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "За технически подробности, вижте по-долу. В даден момент можем да го използваме, за да определим кои книги все още липсват от сенчестите библиотеки, за да приоритизираме кои книги да намерим и/или сканираме."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Нашата блог публикация за тези данни"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Събиране на данни от ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "В момента имаме един торент, който съдържа 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> файл (20GB разархивиран): „isbndb_2022_09.jsonl.gz“. За да импортирате „.jsonl“ файл в PostgreSQL, можете да използвате нещо като <a %(a_script)s>този скрипт</a>. Можете дори да го подадете директно, използвайки нещо като %(example_code)s, така че да се разархивира в движение."

#~ msgid "page.donate.wait"
#~ msgstr "Моля, изчакайте поне <span %(span_hours)s>два часа</span> (и опреснете тази страница), преди да се свържете с нас."

#~ msgid "page.codes.search_archive"
#~ msgstr "Търсене в Архива на Анна за “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Дарете чрез Alipay или WeChat. Можете да избирате между тях на следващата страница."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Разпространение на информация за Архива на Анна в социалните медии и онлайн форуми, чрез препоръчване на книги или списъци в AA, или отговаряне на въпроси."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Колекцията от художествена литература се е разклонила, но все още има <a %(libgenli)s>торенти</a>, въпреки че не са актуализирани от 2022 г. (имаме директни изтегляния)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Архивът на Анна и Libgen.li съвместно управляват колекции от <a %(comics)s>комикси</a> и <a %(magazines)s>списания</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Няма торенти за колекции от руска художествена литература и стандартни документи."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Няма налични торенти за допълнителното съдържание. Торентите, които са на сайта Libgen.li, са огледала на други торенти, изброени тук. Единственото изключение са торентите за художествена литература, започващи от %(fiction_starting_point)s. Торентите за комикси и списания се пускат като сътрудничество между Архива на Анна и Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "От колекция <a %(a_href)s><q>Библиотека Александрина,</q></a> точният произход е неясен. Частично от the-eye.eu, частично от други източници."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Телеграм"

