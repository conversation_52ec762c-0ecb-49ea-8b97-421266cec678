��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b d  sd B  �f -   h   Ih �  Ni �  Hk Z  m !  \n T   ~o f   �o G   :p ^   �p �  �p R  �r �   �s �  �t �   jv   Pw �  _y �  E{ �   1} �  ~ �  � ?   a� �   �� M   ~�   ̂ %   ҄   �� G   	� $   Q�    v�    �� >   ��    �    
� *   � !   D� $   f�    �� :   �� -  ۈ   	� L   � 7   Y� 
   ��    ��    �� 
   ��    ̋    � 	   ��    �    � &   �    @�    F� 	   [� 
   e�    s�    �    ��     �� (  ، S  � �   U� -   ސ N  � �   [�   � �   �� $   �� �   ݔ $   _� �   �� )   -� '  W� $   � �  �� .   .� O   ]�    �� +  �� G  ߚ �  '� "  �    � L   .� Z   {� �   ֟ )   t� &   �� n   Š 5   4� I   j� .   ��    � m   � �  V� 6   �� p   .� �   �� A   J� :  �� :   Ǧ 
  � �   
� �   �� O   6� �   �� �   W� G   �� �   =� �   � V   լ N   ,� q   {�    � �   �� �   �� G   �� 
   ׯ �   � �   v�    H� l  O� �   �� �   \� f  X� �   ��   �� �   �� K   �� C   ۺ T   � l   t� \   � L   >� 5   �� �   �� Y   G� <   ��    ޽ c   � R   U� �   �� �   ]�    �� �   
�   � N  � "  h� ~   �� �   
� #  �� �   #� �   �� �   Y� p  �� �   \� �   ��    �� �   �� �   Y� �   � u  �� y   >� ~   �� �   7� �  �� u   t�   �� �   �     �� `   � �   |� E   � ^  J� �   �� _   N� i   ��    � �  6� �   4� o  ��    i� U   �� �   �� �   w� �   >� �   ;� �  � �  �� /   �� G  �� Q  �   Y� �  e�   �� �   �� Y   �� �   T� 	   �� �   �� 2  �� x  �� �   V� �   .� �   � 	   �� <  �� F  �� �   6�    &� �   +� !  �� �   ��   �� �   �� M   �� 
   �� g  �� =  S     � �   � �   m   V �   c 2  4 R   g C   � �   � 0   � 
   	   	 �   9
 	  ' )   1 G   [ �  � r   i q   � \   N Q  � $   �    " s  8 �   � s   U H   � �    )   � �   � �   �    �    �     � D   � :    5   Q R   � �   �    h (   � X   � O    #   R .   v .   � m   � 
   B "   M   p   y �   �     }   � �   � 9  �    	! �   ! �   �! �   q" O  # ]  ^$   �% }  �' �  B) G   �* 8   +    D+ �   b+ �  , �  �-   �/ <  �0   �1 3  �2 �   ,5 &   #6   J6 #  R7 E  v8    �9 �   �9 D  �: }   �; -  f< �   �=    m> (   u> Z   �>    �> �  ? w  �A !   C �   "E �   �E    �F �   �F �   �G G   �H m   �H   KI   QJ {   ^K {  �K ^   VM "  �M    �N �  �N �   �P �  DQ r  3S �  �U �   eW 
   X �   )X �  Y    �Z H  �Z �  �[ 	  �] �  �_ �   |a *  ab G   �c �  �c �   �e q   �f 6   
g e   Ag    �g 6   �g     �g )   h )   6h    `h �   wh �  i �  �k %  �m �   �o    �p �  �p �  �s R  �u j  )w ;  �x g  �z 
   8}   C} 	   K~    U~ �  f~   � �  � >  ȃ 
   � �  � �  Ǉ �  l� �  ?� �  �� �  �� X  ��    	� �   '� l   ֓ �  C� �  �� �   �� �   b� �   � ^  ��    � �   0� K   � S   -�    �� 1   �� 1   ǝ   �� ~  � ~  �� 9   � e  F� �   ��    P� �   Y� >   �� x   7� �   �� 1   j� h   ��    �   �   ;� �  D� h  � {   X�    ԰ q   � )  Z�    �� �   �� �  � �   �� =   3� *   q� ;  �� -   ط Q  � v  X� �  ϻ &   �� �   �� �   ;�    ľ }  ��    c� �   s� *   ]� �   �� �   *�   �� �   �� F   �� -   �� �   #� /   � �  A� B  �� �   � �   �� Z   �� <   	� �   F�   �� s  � �  �� �  `� 7   <� �   t� �   D� 9  -� �   g� �  � �  �� -   �� �  �� +   �� �  �� _   ��    � �  � �  � �  �� �   �� �   �� �  ��   8� �   :� �  �� �  �� �   ^� �  �� 8  �� ?   �� �   1� y   �� $   2�    W� G  ^� �   ��     K� 
  l� �  w�   )� �  =� |  �� �   t o         � �   �    M Q  V '   �    �    �    � &       -    C    V    k    r    �    �    �    �    �    � %   �    � 	   �    �     �   
 U   �     #	    D	 $   Q	    v	 2   �	 5   �	 "   �	    !
    -
    <
    R
    k
    }
    �
    �
    �
    �
 3   �
    �
    
 #     (   D 5   m )   �    � *   � )       5 E   I 4   �    � X   � <   !
    ^
    z
 !   �
    �
    �
    �
    �
                 3    @    ] 	   j 
   t     #   �    �    � 	   �    � 	   �    �          	   
        '    3    L    T    o    �    � 	   �    � $   �    �    � %   � 
       ' 
   7    B    \    e    t ]   � �   � ^   � �   � �  � �   d    �    �        ) 
   0    >    V    e R   � ;   � T       e ;   � "   � j   � W   N �   � `   v 7   �         0    A    G    O    W    h    z    �    �    �    �    �    �    � 
   �    �    �         
     	   + 
   5    @    ^ #  w    � 	   �    � #   �    � ;   � _    (   w    � =   �    �         �       �    � (   �    �    Q    e u   q #   � H   G   T" o   �" �   # �   �# (   `$ �   �$ �   �%   & �   $'     (    9( C   E( J   �( Y   �( [   .) X   �) G   �) o   +*     �* .   �*    �*    �* F   + &   L+    s+    y+ 
   �+    �+ f   �+    , .   -, H   \,    �,    �, T   �, T   +- q  �-    �.    / �    / 	   �/    �/    �/    �/ 	   �/ *   �/ V  0    u1    �1    �1    �1 .  �1 !   �2 	   3    3 r   3    �3    �3 (   �3    �3    �3 )   �3 �   4    �4    �4 M   �4    5    +5    ?5    E5 3   \5 V   �5    �5 +   �5 �   "6 d   �6 P   !7    r7 �   �7 I   g8    �8 ,   �8    �8 �   	9 �   �9    Z: G   r: �   �: �   O;    �;     <    =< a  I<     �=    �=    �= !   �= "   !> �   D>    �>     ? +   ? :   I? 	   �?    �?    �? &   �? H  �? R  4B �  �C 0   mE 3   �E    �E    �E   �E �   G �   �G    yH �   �H �  wI #   K    &K �  DK =  1M 
   oN    zN 3   �N 	   �N !  �N    �O 1  �O �  ,Q �   �R    �S x   �S 8   BT "   {T ]   �T &  �T �   #V �   W �  �W n   SY   �Y G   �Z �   [ �   �[ G   i\ �   �\ *   f] %   �] 
   �] 
   �]    �] "   �]    ^ N    ^ +   o^ 	   �^ '   �^ �   �^ )   �_ �   �_ |   �` �   2a %   �a !   �a    �a    b $   ,b #   Qb '   ub (   �b �   �b *   �c �   �c    }d �   �d |   Ce a   �e   "f �   :g .   h �   7h 	   �h   �h ]   �i    )j �  Fj    �k    l    l    0l &   Hl    ol    vl <   ~l �   �l �   cm 	   Vn    `n    fn �   ~n +  4o �   `p w   q    zq    �q    �q    �q    �q    �q    �q B   �q ;   ?r �  {r B   t    ]t g   st n   �t ?   Ju c   �u e   �u C   Tv    �v Y   �v 9   �v �   3w d   �w =   x    \x �   nx h   Yy B   �y o   z K   uz F   �z 
   { >   { m   R{    �{ H   @| �   �|    4}   <} @   I~ P   �~ �   �~    k �  t �   Q�    �    )�    B�    H�   O� +   S� l   � �   � �   �� �   r� �   � �   ߆ �   t� �   � P   ��   � f   � o  |� �   � �   �� 
   �� 
   �� 
   �� �   �� 
   ?� 
   M� L   [� W   �� �    � 
   ʏ g   ؏ <   @� w   }� =   �� c   3� T   �� 
   � �   �� 
   �� 
   �� 
   �� 
  �� |   Ȕ w  E�    �� 	   ʖ    Ԗ �   �    g� &   �� �   �� �   �� #   2�    V�    f� +   ~� 1   �� ,   ܙ     	�     *� �   K�     ;� �  \� �   �� �   ӝ J   Z� �   �� �   O� �  ;�   � �   � �   �� �   0�    �� R  ʥ    � [  :� �   �� 
  f� �   t� -  �� �   &� �   ׬ ~   g� (   � =   �    M� -   e� 
   ��    ��    �� �   Ů 
   �� e   �� .   �    A�    J�    P� #   X� r   |� I   � <   9� �   v� .   o�    �� 	   ��    ��    Ͳ    �    ��    �    
�    �    �    $�    +� %   3� �   Y�    ��    
�    �    �    #�    *�    2�    9�    A�    X� &   p� q   ��    	� -   � [   M� �   �� �   Y� �   �   �� �   ظ   �� +   �� �   � 6   d� L   �� E   � �   .� �   Ѽ >   �� c   �    P� Z   a� o   �� �   ,�    ��     ¿    �    �� 
   �     �    :� '   B�    j�    s�    ��    ��    ��    ��    ��    	�    �    "�    2�    :�    T� #   [�    � |   �� �   � #   �� "   �� o   �� W   U� }   �� #   +� &   O� 2   v� 9   �� >   �� )   "� �   L� �   �   ��    � 8   6� �   o� &   � �   ,� �   �� m   �� /   &�    V� �   k� �   �� c   �� 1   �� a   *� �   ��    Y� /   q�    �� 7   �� W   �� Y   I�    ��    ��    ��    �� �   �� :   _� ,   �� A   �� '   	�    1� *   H� A   s�    �� b   �� <   1� �   n� N   N� H   �� �   �� A   ��    ��    ��    �    �    :�    V�    q� !   �� '   �� 
  �� G   �� 9   -� F   g� !   ��    �� x   �� |   R� �   �� �   |�    $� �   ,� "   ��    �� �   ��    �� ,   �� :   �� 0   6� T   g� M   �� N   
�    Y�    s� �   �� +   �    G� Z   e� �   �� :   �� �   �� W   l� [   �� x    �    �� 0   �� f   ��    Q� ]   g�    �� �   �� 3   �� 6   ��    ��    � �   +� G   �� B   6� �   y� Y   �� C   W� �   �� �   �    ��    �� R   �� G   �    [�     k�    ��    ��    �� .   �� �   �� f   x�    ��    ��    � %   1� *   W� t   �� 0   �� m   (� R   ��    �� e   � �   i� 8   =� O   v�    �� J   �� 8    �    Y� p   i� j   �� 5   E� H   {�    �� 8   �� O   	�    Y� m   h� *   ��    � !   � S   9� �   �� D   T�    ��    �� D   ��    � W   ,� 4   �� �   �� �   B�    �� 3   �� �   
�    �� �   �� ;   b�    �� Z   �� �   �    �    
�    �    � *   $� 5   O� O   ��    �� 
   ��    �� "   � 7   *� 8   b�    �� S   �� ?   ��    5�    G� '   f�    ��    �� \   �� �   � R   �    c�    s� �   �� .  &� ]   U�    �� H  �� �  
 2   � k   �     H X  i *   � k   �    Y    m �  }    D W   _ n   � X   &	 [   	    �	 ^   �	 B   W
 "   �
 u   �
 F   3 2   z [   � +   	 D   5 p   z �   � -   �
 �   �
 m  z �   � 5   � E  � �    �   � (  � �   � -   � !   � �   � 0   � �  � d   � G        H    U �  u    - �   2   � $  
   / @   I I   � Y   � ,   .  -   [  T   �  N   �     -! ,   <! ;   i!    �!    �! 2   �! Z   " 5   f"    �" n   �" �   # �   �#    �$    �$    �$ M   �$ b   �$ ]   _% �   �% W   �&    �& !   �& �   '    �' �   �' �  �( �   + K   �+ #   K,    o,    u,    y, F   }, /   �, q   �, �   f- T   M.    �.    �. %   �.    �. `   /    i/ 8   }/    �/ 0   �/ +   �/    0    /0 X   50    �0    �0 '   �0 %   �0    �0 W    1 �   X1 ]   )2 e   �2 W   �2 �   E3 
   4    4 �   +4 �   �4 �   {5    *6 w   16 J   �6 A   �6 R   67 ]   �7 X   �7    @8 R   Y8    �8    �8    �8    �8    9    9    79    N9    l9 	   y9 &   �9 &   �9 &   �9    �9 +   : %   ?:    e:    ~:    �: A   �:    �:    ; ?   "; -   b; V   �; ,   �;    <    1<    G<    e<    �< p   �< Y   	= {   c= �   �=    �>    �>    �>    
? /   #? 	   S?    ]?    t? o   �?    @ #   @    B@ 
   I@ 	   T@ ?   ^@    �@ �   �@    �A    �A    �A "   �A    �A    B    +B    FB     dB &   �B (   �B    �B N   �B +   +C    WC    nC .   C J   �C $   �C &   D "   ED m   hD X   �D I   /E    yE !   �E 	   �E    �E    �E    �E a  �E ~   YG    �G    �G "   �G    H $   (H    MH 
   RH p   ]H D   �H �   I    �I #   �I w   J #   {J    �J "   �J %   �J /   K %   8K    ^K 	   xK 0   �K    �K    �K 1   �K   L C   /M <   sM F   �M     �M �   N (   �N    �N L   �N    ,O $   @O    eO 7   �O C   �O "   P �   $P    �P 
   �P    �P    �P    Q    Q    9Q    VQ    mQ X   �Q �   �Q    �R "   �R �   
S �   �S    {T    �T    �T    �T    �T    �T    U    U    2U    DU    WU    fU 
   wU    �U    �U    �U    �U    �U    �U �   �U �   �V T  �W    Y �  [ �   �\ {  ~]    �^ �    _    �_ �   ` �   �` �  �a �   wc ;  %d 6   ae �   �e :   :f    uf U   �f a   �f    Kg Z   �g �   &h �   �h �   \i +  j    1l �   Hl �   �l W   �m �   
n    �n �   �n K  �o �   �p �   �q 
   Ar W   Lr a   �r �   s �   �s r   ot u   �t    Xu     hu    �u @   �u )   �u    v r   v M   �v n   �v     Jw ~   kw �   �w 6   mx P   �x K   �x p   Ay b   �y U   z k   kz c   �z �   ;{ x   �{    >| 0   G| +   x| T   �| 2   �|    ,}    2} ?   :}    z}    �}    �} +   �} 4   �} >   
~    L~    `~ 
   m~    x~ 	   ~~ P   �~ n   �~ I   H 7   �    �    � *   �    �    +�    7�    D�    I�    N�    T�    Z�    f�    k�    t�    {�    ��    ��    ��    ��    Ѐ    ր 	   ߀    � *   �    �    <� c   Z�    �� T   ԁ �   )� 
   ۂ    � 
   ��    � 	   
�    �    � �   #� �   Ã I   I�    �� 
   �� m   ��    !� x   5� �   �� *   6�    a�    y� �   �� D   �� �   � o   �� +   )� �   U�    ߉    �� E   � �   V� !   ڊ �   �� v   �� �   � S   ƌ    �    )�    .� 
   B�    M� 
   g� 
   r�    }� �   �� u   %� �   �� �   $� �   �� m   � O   Q� �  �� k  o� �   ۔ �   ҕ   l� Q  ��    ޘ �   � �   �� �   �� ]  8� �  �� t   ;� x  ��    )� C   A� �   �� h  � :   x� �   �� 
   �� 	   ��    �� �   Ǥ H   q� ^   �� &   � X   @� H   �� R   � $   5� �   Z� D   � *   6� U   a� �   ��    s�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: et
Language-Team: et <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Raamatukogu on populaarne (ja ebaseaduslik) raamatukogu. Nad on võtnud Library Genesis kollektsiooni ja teinud selle kergesti otsitavaks. Lisaks sellele on nad muutunud väga tõhusaks uute raamatute panustamise soodustamisel, pakkudes panustavatele kasutajatele erinevaid eeliseid. Praegu ei panusta nad neid uusi raamatuid tagasi Library Genesis'isse. Ja erinevalt Library Genesis'est ei tee nad oma kollektsiooni kergesti peegeldatavaks, mis takistab laialdast säilitamist. See on nende ärimudeli jaoks oluline, kuna nad võtavad tasu oma kollektsiooni hulgipääsu eest (rohkem kui 10 raamatut päevas). Me ei tee moraalseid hinnanguid raha küsimise kohta ebaseadusliku raamatukogu massilise juurdepääsu eest. Pole kahtlustki, et Z-Library on olnud edukas teadmiste kättesaadavuse laiendamisel ja rohkemate raamatute hankimisel. Meie oleme siin lihtsalt oma osa tegemas: tagame selle privaatse kogu pikaajalise säilimise. - Anna ja meeskond (<a %(reddit)s>Reddit</a>) Piraadi Raamatukogu Peegli algses väljaandes (EDIT: kolitud <a %(wikipedia_annas_archive)s>Anna arhiiv</a>), tegime peegli Z-Raamatukogust, suurest ebaseaduslikust raamatukogust. Meeldetuletuseks, see on see, mida me kirjutasime selles algses blogipostituses: See kogu pärineb 2021. aasta keskpaigast. Vahepeal on Z-Library kasvanud hämmastava kiirusega: nad on lisanud umbes 3,8 miljonit uut raamatut. Seal on küll mõned duplikaadid, kuid enamik neist tundub olevat tõeliselt uued raamatud või varasemate raamatute kõrgema kvaliteediga skaneeringud. See on suures osas tänu Z-Library vabatahtlike moderaatorite arvu suurenemisele ja nende massilise üleslaadimise süsteemile koos duplikaatide eemaldamisega. Soovime neid nende saavutuste puhul õnnitleda. Meil on hea meel teatada, et oleme saanud kõik raamatud, mis lisati Z-Librarysse meie viimase peegli ja 2022. aasta augusti vahel. Oleme ka tagasi läinud ja kraapinud mõned raamatud, mis esimesel korral vahele jäid. Kokkuvõttes on see uus kogu umbes 24TB, mis on palju suurem kui eelmine (7TB). Meie peegel on nüüd kokku 31TB. Jällegi, me eemaldasime duplikaadid Library Genesis'iga, kuna selle kogu jaoks on juba saadaval torrente. Palun minge Piraatide Raamatukogu Peeglisse, et vaadata uut kogu (MUUDETUD: kolitud <a %(wikipedia_annas_archive)s>Anna Arhiiv</a>). Seal on rohkem teavet selle kohta, kuidas failid on struktureeritud ja mis on muutunud alates viimasest korrast. Me ei lingi seda siit, kuna see on lihtsalt blogi veebisait, mis ei majuta ebaseaduslikke materjale. Loomulikult on seemnete jagamine ka suurepärane viis meid aidata. Täname kõiki, kes jagavad meie eelmist torrentikomplekti. Oleme tänulikud positiivse vastuvõtu eest ja rõõmsad, et on nii palju inimesi, kes hoolivad teadmiste ja kultuuri säilitamisest sellisel ebatavalisel viisil. 3x uut raamatut lisatud Piraadi Raamatukogu Peeglisse (+24TB, 3,8 miljonit raamatut) Lugege kaasartikleid TorrentFreakilt: <a %(torrentfreak)s>esimene</a>, <a %(torrentfreak_2)s>teine</a> - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) kaasartiklid TorrentFreakilt: <a %(torrentfreak)s>esimene</a>, <a %(torrentfreak_2)s>teine</a> Mitte kaua aega tagasi olid "varjuraamatukogud" hääbumas. Sci-Hub, massiivne ebaseaduslik akadeemiliste artiklite arhiiv, oli kohtuasjade tõttu lõpetanud uute teoste vastuvõtmise. "Z-Library", suurim ebaseaduslik raamatukogu, nägi oma väidetavaid loojaid arreteerituna kriminaalsete autoriõiguse rikkumiste eest. Nad suutsid uskumatult oma arreteerimisest pääseda, kuid nende raamatukogu on endiselt ohus. Mõned riigid juba teevad seda versiooni. TorrentFreak <a %(torrentfreak)s>teatas</a>, et Hiina ja Jaapan on oma autoriõiguse seadustesse lisanud tehisintellekti erandid. Meile on ebaselge, kuidas see rahvusvaheliste lepingutega suhestub, kuid see annab kindlasti katet nende kodumaistele ettevõtetele, mis selgitab, mida oleme näinud. Mis puutub Anna Arhiivi — jätkame oma maa-alust tööd, mis on juurdunud moraalses veendumuses. Kuid meie suurim soov on astuda valgusesse ja suurendada oma mõju seaduslikult. Palun reformige autoriõigust. Kui Z-Library seisis sulgemise ees, olin juba varundanud kogu selle raamatukogu ja otsisin platvormi, kuhu seda paigutada. See oli minu motivatsioon Anna Arhiivi loomisel: jätkata nende varasemate algatuste missiooni. Oleme sellest ajast alates kasvanud maailma suurimaks varjuraamatukoguks, majutades üle 140 miljoni autoriõigusega kaitstud teksti mitmesugustes formaatides — raamatud, akadeemilised artiklid, ajakirjad, ajalehed ja palju muud. Minu meeskond ja mina oleme ideoloogid. Usume, et nende failide säilitamine ja majutamine on moraalselt õige. Raamatukogud üle maailma näevad rahastuse kärpeid ja me ei saa usaldada inimkonna pärandit ka korporatsioonidele. Siis tuli tehisintellekt. Praktiliselt kõik suuremad ettevõtted, kes arendavad LLM-e, võtsid meiega ühendust, et treenida meie andmetel. Enamik (kuid mitte kõik!) USA-s asuvad ettevõtted mõtlesid ümber, kui nad mõistsid meie töö ebaseaduslikku olemust. Seevastu Hiina ettevõtted on meie kollektsiooni entusiastlikult omaks võtnud, näiliselt muretsemata selle seaduslikkuse pärast. See on märkimisväärne, arvestades Hiina rolli peaaegu kõigi suuremate rahvusvaheliste autoriõiguse lepingute allakirjutajana. Oleme andnud kiirjuurdepääsu umbes 30 ettevõttele. Enamik neist on LLM-ettevõtted ja mõned on andmevahendajad, kes müüvad meie kollektsiooni edasi. Enamik on Hiinast, kuigi oleme teinud koostööd ka ettevõtetega USA-st, Euroopast, Venemaalt, Lõuna-Koreast ja Jaapanist. DeepSeek <a %(arxiv)s>tunnistas</a>, et varasem versioon oli treenitud osaliselt meie kollektsioonil, kuigi nad on oma uusima mudeli osas kidakeelsed (tõenäoliselt on see samuti meie andmetel treenitud). Kui Lääs soovib jääda LLM-ide ja lõpuks AGI võidujooksus ettepoole, peab ta oma seisukohta autoriõiguse osas ümber mõtlema ja seda kiiresti. Kas nõustute meiega või mitte meie moraalse juhtumi osas, on see nüüd muutumas majanduse ja isegi riikliku julgeoleku küsimuseks. Kõik võimublokid ehitavad kunstlikke superteadlasi, superhäkkereid ja superarmeesid. Informatsiooni vabadus muutub nende riikide jaoks ellujäämise küsimuseks — isegi riikliku julgeoleku küsimuseks. Meie meeskond on pärit üle kogu maailma ja meil pole kindlat joondumist. Kuid julgustaksime riike, kus on tugevad autoriõiguse seadused, kasutama seda eksistentsiaalset ohtu nende reformimiseks. Mida siis teha? Meie esimene soovitus on lihtne: lühendage autoriõiguse kehtivusaega. USA-s antakse autoriõigus 70 aastaks pärast autori surma. See on absurdne. Saame selle viia kooskõlla patentidega, mis antakse 20 aastaks pärast taotlemist. See peaks olema enam kui piisav aeg, et raamatute, artiklite, muusika, kunsti ja muude loominguliste teoste autorid saaksid oma pingutuste eest täielikult kompenseeritud (sealhulgas pikemaajalised projektid nagu filmi kohandused). Seejärel peaksid poliitikakujundajad vähemalt sisaldama erandeid tekstide massilise säilitamise ja levitamise jaoks. Kui peamine mure on üksikute klientide kaotatud tulu, võiks isiklikul tasandil levitamine jääda keelatuks. Seevastu need, kes suudavad hallata suuri kogusid — LLM-e treenivad ettevõtted, samuti raamatukogud ja muud arhiivid — oleksid nende erandite alla kuuluvad. Autoriõiguse reform on vajalik riikliku julgeoleku tagamiseks. Lühidalt: Hiina LLM-id (sealhulgas DeepSeek) on koolitatud minu ebaseaduslikul raamatute ja artiklite arhiivil — maailma suurimal. Läänes on vaja autoriõiguse seadust riikliku julgeoleku huvides põhjalikult muuta. Palun vaadake lisateabe saamiseks <a %(all_isbns)s>algset blogipostitust</a>. Esitasime väljakutse selle täiustamiseks. Esimese koha auhinnaks määrasime 6 000 dollarit, teise koha auhinnaks 3 000 dollarit ja kolmanda koha auhinnaks 1 000 dollarit. Tulenevalt tohutust vastukajast ja uskumatutest esitustest otsustasime auhinnafondi veidi suurendada ja anda neljale kolmanda koha võitjale 500 dollarit igaühele. Võitjad on allpool, kuid kindlasti vaadake kõiki esitlusi <a %(annas_archive)s>siin</a> või laadige alla meie <a %(a_2025_01_isbn_visualization_files)s>ühendatud torrent</a>. Esimene koht 6 000 dollarit: phiresky See <a %(phiresky_github)s>esitlus</a> (<a %(annas_archive_note_2951)s>Gitlabi kommentaar</a>) on lihtsalt kõik, mida soovisime, ja veelgi enam! Meile meeldisid eriti uskumatult paindlikud visualiseerimisvõimalused (isegi kohandatud shaderite tugi), kuid koos ulatusliku eelseadistuste loendiga. Samuti meeldis meile, kui kiire ja sujuv kõik on, lihtne teostus (millel pole isegi taustsüsteemi), nutikas minimap ja põhjalik selgitus nende <a %(phiresky_github)s>blogipostituses</a>. Uskumatu töö ja hästi teenitud võit! - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Meie südamed on täis tänulikkust. Märkimisväärsed ideed Pilvelõhkujad harulduse jaoks Palju liugureid andmekogumite võrdlemiseks, nagu oleksite DJ. Mõõtkava raamatute arvuga. Ilusad sildid. Lahe vaikimisi värviskeem ja soojuskaart. Ainulaadne kaardivaade ja filtrid Märkused ja ka reaalajas statistika Reaalajas statistika Veel mõned ideed ja teostused, mis meile eriti meeldisid: Võiksime veel jätkata, kuid peatume siin. Vaadake kindlasti kõiki esitlusi <a %(annas_archive)s>siin</a> või laadige alla meie <a %(a_2025_01_isbn_visualization_files)s>ühendatud torrent</a>. Nii palju esitlusi ja igaüks toob ainulaadse vaatenurga, olgu see siis kasutajaliideses või teostuses. Vähemalt lisame esimese koha esituse oma peamisele veebisaidile ja võib-olla ka mõned teised. Oleme hakanud mõtlema ka sellele, kuidas korraldada haruldasemate raamatute tuvastamise, kinnitamise ja seejärel arhiveerimise protsessi. Rohkem infot tulekul. Tänud kõigile osalejatele. On hämmastav, et nii paljud inimesed hoolivad. Lihtne andmekogumite vahetamine kiireteks võrdlusteks. Kõik ISBN-id CADAL SSNO-d CERLALC andmeleke DuXiu SSID-id EBSCOhosti e-raamatute indeks Google Books Goodreads Internet Archive ISBNdb ISBN-i globaalne kirjastajate register Libby Failid Anna Arhiivis Nexus/STC OCLC/Worldcat OpenLibrary Vene Riiklik Raamatukogu Trantori Keiserlik Raamatukogu Teine koht 3 000 dollarit: hypha „Kuigi täiuslikud ruudud ja ristkülikud on matemaatiliselt meeldivad, ei paku need kaardistamise kontekstis paremat lokaalsust. Usun, et nende Hilberti või klassikalise Mortoni asümmeetria ei ole viga, vaid omadus. Nii nagu Itaalia kuulus saapakuju muudab selle kaardil koheselt äratuntavaks, võivad nende kõverate ainulaadsed „kiiksud” toimida kognitiivsete maamärkidena. See eripära võib parandada ruumilist mälu ja aidata kasutajatel end orienteerida, muutes konkreetsete piirkondade leidmise või mustrite märkamise lihtsamaks.” Veel üks uskumatu <a %(annas_archive_note_2913)s>esitlus</a>. Mitte nii paindlik kui esimene koht, kuid tegelikult eelistasime selle makrotasandi visualiseerimist esimesele kohale (ruumitäitev kõver, piirid, märgistus, esiletõstmine, panoraamimine ja suumimine). Joe Davise <a %(annas_archive_note_2971)s>kommentaar</a> kõlas meiega: Ja veel palju võimalusi visualiseerimiseks ja renderdamiseks, samuti uskumatult sujuv ja intuitiivne kasutajaliides. Kindel teine koht! - Anna ja meeskond (<a %(reddit)s>Reddit</a>) Mõni kuu tagasi kuulutasime välja <a %(all_isbns)s>$10,000 preemia</a>, et luua meie andmete parim võimalik visualiseerimine, mis näitab ISBN-i ruumi. Rõhutasime, et näidata, millised failid meil on/ei ole veel arhiveeritud, ja hiljem andsime andmekogumi, mis kirjeldab, kui paljud raamatukogud omavad ISBN-e (harulduse mõõt). Oleme vastusest ülekoormatud. On olnud nii palju loovust. Suur aitäh kõigile, kes on osalenud: teie energia ja entusiasm on nakkavad! Lõppkokkuvõttes tahtsime vastata järgmistele küsimustele: <strong>millised raamatud maailmas eksisteerivad, kui palju oleme juba arhiveerinud ja millistele raamatutele peaksime järgmisena keskenduma?</strong> On tore näha, et nii paljud inimesed hoolivad nendest küsimustest. Alustasime ise lihtsa visualiseerimisega. Vähem kui 300kb-s esindab see pilt lühidalt suurimat täielikult avatud "raamatute nimekirja", mis on inimkonna ajaloos kunagi kokku pandud: Kolmas koht 500 dollarit #1: maxlion Selles <a %(annas_archive_note_2940)s>esitluses</a> meeldisid meile tõesti erinevad vaated, eriti võrdlus- ja kirjastajavaated. Kolmas koht 500 dollarit #2: abetusk Kuigi mitte kõige viimistletum kasutajaliides, vastab see <a %(annas_archive_note_2917)s>esitlus</a> paljudele nõuetele. Meile meeldis eriti selle võrdlusfunktsioon. Kolmas koht 500 dollarit #3: conundrumer0 Nagu esimene koht, avaldas see <a %(annas_archive_note_2975)s>esitlus</a> meile muljet oma paindlikkusega. Lõppkokkuvõttes on see see, mis teeb suurepärase visualiseerimistööriista: maksimaalne paindlikkus võimsatele kasutajatele, hoides samal ajal asjad lihtsana keskmistele kasutajatele. Kolmas koht 500 dollarit #4: charelf Viimane <a %(annas_archive_note_2947)s>esitlus</a>, mis sai auhinna, on üsna lihtne, kuid sellel on mõned ainulaadsed omadused, mis meile tõesti meeldisid. Meile meeldis, kuidas nad näitavad, kui palju andmekogumeid katab konkreetset ISBN-i kui populaarsuse/usaldusväärsuse mõõdet. Samuti meeldis meile tõesti võrdluste jaoks läbipaistvuse liuguri kasutamise lihtsus, kuid tõhusus. $10,000 ISBN visualiseerimise preemia võitjad Lühidalt: Saime hämmastavaid esitusi $10,000 ISBN visualiseerimise preemiale. Taust Kuidas saab Anna Arhiiv täita oma missiooni varundada kogu inimkonna teadmised, ilma et teaks, millised raamatud on veel olemas? Me vajame TODO-nimekirja. Üks viis selle kaardistamiseks on ISBN-numbrite kaudu, mis alates 1970. aastatest on määratud igale avaldatud raamatule (enamikus riikides). Puudub keskne asutus, kes teaks kõiki ISBN-määranguid. Selle asemel on see hajutatud süsteem, kus riigid saavad numbrivahemikke, kes seejärel määravad väiksemaid vahemikke suurtele kirjastajatele, kes võivad veelgi jagada vahemikke väiksematele kirjastajatele. Lõpuks määratakse individuaalsed numbrid raamatutele. Alustasime ISBN-ide kaardistamist <a %(blog)s>kaks aastat tagasi</a> meie ISBNdb andmete kogumisega. Sellest ajast alates oleme kogunud palju rohkem metadata allikaid, nagu <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby ja teised. Täielik nimekiri on leitav Anna Arhiivi lehtedel „Datasets” ja „Torrents”. Meil on nüüd kaugelt suurim täielikult avatud, kergesti allalaaditav raamatute metadata (ja seega ISBN-ide) kogu maailmas. Oleme <a %(blog)s>kirjutanud põhjalikult</a>, miks hoolime säilitamisest ja miks oleme praegu kriitilises aknas. Peame nüüd tuvastama haruldased, alafokuseeritud ja ainulaadselt ohustatud raamatud ning need säilitama. Hea metadata olemasolu kõigi maailma raamatute kohta aitab selles. 10 000 dollari suurune preemia Tugev kaalutlus antakse kasutatavusele ja sellele, kui hea see välja näeb. Näidake üksikute ISBN-ide tegelikku metadata, nagu pealkiri ja autor, kui suumite sisse. Parem ruumitäitev kõver. Näiteks siksak, mis läheb esimesel real 0-st 4-ni ja siis tagasi (tagurpidi) teisel real 5-st 9-ni — rakendatud rekursiivselt. Erinevad või kohandatavad värviskeemid. Erivaated andmekogumite võrdlemiseks. Võimalused probleemide silumiseks, näiteks muu metadata, mis ei ühti hästi (nt väga erinevad pealkirjad). Piltide kommenteerimine ISBN-ide või vahemike kohta. Igasugused heuristikad haruldaste või ohustatud raamatute tuvastamiseks. Millised loomingulised ideed teil ka ei oleks! Kood Kood nende piltide genereerimiseks, samuti muud näited, leiate <a %(annas_archive)s>sellest kataloogist</a>. Me lõime kompaktse andmeformaadi, millega kogu vajalik ISBN teave on umbes 75MB (kokkusurutud). Andmeformaadi kirjeldus ja kood selle genereerimiseks leiate <a %(annas_archive_l1244_1319)s>siit</a>. Preemia saamiseks ei ole teil kohustust seda kasutada, kuid see on tõenäoliselt kõige mugavam formaat alustamiseks. Võite meie metadata muuta, kuidas soovite (kuid kogu teie kood peab olema avatud lähtekoodiga). Me ei jõua ära oodata, mida te välja mõtlete. Edu! Forkige see repo ja muutke selle blogipostituse HTML-i (muid backende peale meie Flask backendi ei ole lubatud). Tehke ülaltoodud pilt sujuvalt suumimiseks, et saaksite suumida üksikute ISBN-ideni. ISBN-ide klõpsamine peaks viima teid Anna Arhiivi metadata lehele või otsingusse. Peate siiski suutma vahetada kõigi erinevate datasets'ide vahel. Riikide ja kirjastajate vahemikud peaksid olema esile tõstetud, kui nende kohal hõljutatakse. Võite kasutada näiteks <a %(github_xlcnd_isbnlib)s>data4info.py isbnlib'is</a> riigiinfo jaoks ja meie „isbngrp” kogumist kirjastajate jaoks (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). See peab hästi töötama nii lauaarvutis kui ka mobiilis. Siin on palju avastada, seega kuulutame välja preemia ülaltoodud visualiseerimise parandamiseks. Erinevalt enamikust meie preemiatest on see ajaliselt piiratud. Peate <a %(annas_archive)s>esitama</a> oma avatud lähtekoodiga koodi hiljemalt 2025-01-31 (23:59 UTC). Parim esitaja saab 6 000 dollarit, teine koht 3 000 dollarit ja kolmas koht 1 000 dollarit. Kõik preemiad makstakse välja Monero (XMR) abil. Allpool on minimaalsed kriteeriumid. Kui ükski esitus ei vasta kriteeriumidele, võime siiski preemiaid anda, kuid see jääb meie äranägemise järgi. Lisapunktide saamiseks (need on lihtsalt ideed — laske oma loovusel lennata): Võite täielikult kõrvale kalduda minimaalsetest kriteeriumidest ja teha täiesti erineva visualiseerimise. Kui see on tõeliselt suurejooneline, siis see kvalifitseerub preemiale, kuid meie äranägemisel. Esitage oma tööd, postitades kommentaari <a %(annas_archive)s>sellele probleemile</a> koos lingiga teie harutatud repo, liitmissoovi või erinevuse juurde. - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) See pilt on 1000×800 pikslit. Iga piksel esindab 2500 ISBN-i. Kui meil on ISBN-i jaoks fail, muudame selle pikseli rohelisemaks. Kui teame, et ISBN on välja antud, kuid meil pole vastavat faili, muudame selle punasemaks. Vähem kui 300 kb-s esindab see pilt lühidalt suurimat täielikult avatud "raamatute nimekirja", mis on inimkonna ajaloos kunagi kokku pandud (täielikult kokkusurutuna mõnisada GB). See näitab ka: raamatute varundamisel on veel palju tööd teha (meil on ainult 16%). Kõigi ISBN-ide visualiseerimine — 10 000 dollari suurune preemia 2025-01-31 See pilt esindab suurimat täielikult avatud "raamatute nimekirja", mis on inimkonna ajaloos kunagi kokku pandud. Visualiseerimine Peale ülevaatepildi saame vaadata ka individuaalseid datasets'e, mida oleme omandanud. Kasutage rippmenüüd ja nuppe nende vahel vahetamiseks. Nendes piltides on palju huvitavaid mustreid. Miks on seal teatud regulaarsus joonte ja plokkide osas, mis näib esinevat erinevatel skaaladel? Mis on tühjad alad? Miks on teatud datasets'id nii klasterdatud? Jätame need küsimused lugejale harjutuseks. - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Kokkuvõte Selle standardiga saame teha väljaandeid järk-järgult ja hõlpsamini lisada uusi andmeallikaid. Meil on juba mõned põnevad väljaanded töös! Loodame ka, et teistel varjatud raamatukogudel on lihtsam meie kogumikke peegeldada. Lõppude lõpuks on meie eesmärk säilitada inimteadmisi ja -kultuuri igavesti, seega mida rohkem varukoopiaid, seda parem. Näide Vaatame meie hiljutist Z-Library väljaannet näitena. See koosneb kahest kogumikust: “<span style="background: #fffaa3">zlib3_records</span>” ja “<span style="background: #ffd6fe">zlib3_files</span>”. See võimaldab meil eraldi koguda ja välja anda metadata kirjeid tegelikest raamatufailidest. Seetõttu andsime välja kaks torrentit metadata failidega: Samuti andsime välja hulga torrente binaarandmete kaustadega, kuid ainult “<span style="background: #ffd6fe">zlib3_files</span>” kogumiku jaoks, kokku 62: Käivitades <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> näeme, mis seal sees on: Antud juhul on tegemist Z-Library poolt teatatud raamatu metadata’ga. Kõrgemal tasemel on meil ainult “aacid” ja “metadata”, kuid mitte “data_folder”, kuna vastavat binaarandmeid ei ole. AACID sisaldab “22430000” kui peamist ID-d, mis on võetud “zlibrary_id” alt. Võime eeldada, et teistel AAC-del selles kogumikus on sama struktuur. Nüüd käivitame <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: See on palju väiksem AAC metadata, kuigi selle AAC põhiosa asub mujal binaarfailis! Lõppude lõpuks on meil seekord “data_folder”, seega võime eeldada, et vastavad binaarandmed asuvad aadressil <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” sisaldab “zlibrary_id”, seega saame selle hõlpsasti seostada vastava AAC-ga “zlib_records” kogumikus. Oleksime võinud seostada mitmel erineval viisil, näiteks AACID kaudu — standard seda ei määra. Pange tähele, et “metadata” väli ei pea olema JSON. See võib olla string, mis sisaldab XML-i või mõnda muud andmeformaati. Võite isegi salvestada metadata teabe seotud binaarplokki, näiteks kui see on palju andmeid. Heterogeensed failid ja metadata, võimalikult lähedal algsele formaadile. Binaarandmeid saab otse serveerida veebiserverite nagu Nginx kaudu. Heterogeensed identifikaatorid allikakogudes või isegi identifikaatorite puudumine. Eraldi metadata ja failide andmete väljaanded või ainult metadata väljaanded (nt meie ISBNdb väljaanne). Levitamine torrentide kaudu, kuid võimalusega kasutada ka teisi levitusmeetodeid (nt IPFS). Muutumatud kirjed, kuna peaksime eeldama, et meie torrendid elavad igavesti. Järkjärgulised väljaanded / lisatavad väljaanded. Masinloetav ja -kirjutatav, mugavalt ja kiiresti, eriti meie stacki jaoks (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Mõnevõrra lihtne inimeste kontroll, kuigi see on teisejärguline masinloetavuse suhtes. Lihtne külvata meie kogusid standardse renditud seedboxiga. Disaini eesmärgid Meid ei huvita, et faile oleks lihtne käsitsi kettal navigeerida või otsida ilma eeltöötluseta. Meid ei huvita, et oleksime otseselt ühilduvad olemasoleva raamatukogutarkvaraga. Kuigi peaks olema lihtne, et keegi saaks meie kogu torrentide abil külvata, ei eelda me, et failid oleksid kasutatavad ilma märkimisväärse tehnilise teadmise ja pühendumiseta. Meie peamine kasutusjuht on failide ja nendega seotud metadata levitamine erinevatest olemasolevatest kogudest. Meie kõige olulisemad kaalutlused on: Mõned mitte-eesmärgid: Kuna Anna Arhiiv on avatud lähtekoodiga, tahame oma formaati otse kasutada. Kui värskendame oma otsinguindeksit, pääseme ligi ainult avalikult kättesaadavatele teedele, et igaüks, kes meie raamatukogu harutab, saaks kiiresti tööle hakata. <strong>AAC.</strong> AAC (Anna Arhiivi Konteiner) on üksiküksus, mis koosneb <strong>metadata</strong>st ja valikuliselt <strong>binaarandmetest</strong>, mis mõlemad on muutumatud. Sellel on globaalselt unikaalne identifikaator, mida nimetatakse <strong>AACID</strong>ks. <strong>AACID.</strong> AACIDi formaat on järgmine: <code style="color: #0093ff">aacid__{kogumik}__{ISO 8601 ajatempel}__{kogumikuspetsiifiline ID}__{shortuuid}</code>. Näiteks üks tegelik AACID, mille oleme avaldanud, on <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID vahemik.</strong> Kuna AACIDid sisaldavad monotoonselt suurenevaid ajatemplid, saame seda kasutada vahemike tähistamiseks konkreetse kogumiku sees. Kasutame seda formaati: <code style="color: blue">aacid__{kogumik}__{alates_ajatempel}--{kuni_ajatempel}</code>, kus ajatemplid on kaasavad. See on kooskõlas ISO 8601 tähistusega. Vahemikud on pidevad ja võivad kattuda, kuid kattumise korral peavad need sisaldama identseid kirjeid, nagu varem selles kogumikus avaldatud (kuna AACid on muutumatud). Puuduvad kirjed ei ole lubatud. <code>{kogumik}</code>: kogumiku nimi, mis võib sisaldada ASCII tähti, numbreid ja alakriipse (kuid mitte topeltalakriipse). <code>{kogumikuspetsiifiline ID}</code>: kogumikuspetsiifiline identifikaator, kui see on asjakohane, nt Z-Library ID. Võib olla välja jäetud või kärbitud. Peab olema välja jäetud või kärbitud, kui AACID muidu ületaks 150 tähemärki. <code>{ISO 8601 ajatempel}</code>: lühike versioon ISO 8601-st, alati UTC-s, nt <code>20220723T194746Z</code>. See number peab iga väljaande puhul monotoonselt suurenema, kuigi selle täpne semantika võib kogumike lõikes erineda. Soovitame kasutada kraapimise või ID genereerimise aega. <code>{shortuuid}</code>: UUID, kuid tihendatud ASCII-ks, nt kasutades base57. Praegu kasutame <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python raamatukogu. <strong>Binaarandmete kaust.</strong> Kaust, mis sisaldab ühe konkreetse kogumiku AACide vahemiku binaarandmeid. Neil on järgmised omadused: Kataloog peab sisaldama andmefaile kõigi määratud vahemiku AACide jaoks. Iga andmefail peab olema oma AACIDiga failinimena (ilma laienditeta). Kataloogi nimi peab olema AACID vahemik, millele on lisatud eesliide <code style="color: green">anna_arhiivi_data__</code>, ja ilma järelliiteta. Näiteks üks meie tegelikest väljaannetest on kataloog nimega<br><code><span style="color: green">anna_arhiivi_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Soovitatav on muuta need kaustad suuruse poolest hallatavateks, näiteks mitte suuremaks kui 100GB-1TB igaüks, kuigi see soovitus võib aja jooksul muutuda. <strong>Kogumik.</strong> Iga AAC kuulub kogumikku, mis definitsiooni järgi on semantiliselt ühtne AACide loend. See tähendab, et kui teete metadata formaadis olulise muudatuse, peate looma uue kogumiku. Standard <strong>Metadata fail.</strong> Metadata fail sisaldab ühe konkreetse kogumiku AACide vahemiku metadata. Neil on järgmised omadused: <code>data_folder</code> on valikuline ja on binaarandmete kausta nimi, mis sisaldab vastavaid binaarandmeid. Vastavate binaarandmete failinimi selles kaustas on kirje AACID. Iga JSON objekt peab sisaldama järgmisi välju ülemisel tasemel: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valikuline). Teised väljad ei ole lubatud. Failinimi peab olema AACID vahemik, millele on lisatud eesliide <code style="color: red">anna_arhiivi_meta__</code> ja millele järgneb <code>.jsonl.zstd</code>. Näiteks üks meie väljaannetest on nimega<br><code><span style="color: red">anna_arhiivi_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Nagu faililaiend näitab, on failitüüp <a %(jsonlines)s>JSON Lines</a>, mis on tihendatud <a %(zstd)s>Zstandard</a>iga. <code>metadata</code> on suvaline metadata, vastavalt kogumiku semantikale. See peab olema semantiliselt ühtne kogumiku sees. <code style="color: red">anna_arhiivi_meta__</code> eesliidet võib kohandada teie asutuse nimele, nt <code style="color: red">minu_asutus_meta__</code>. <strong>„kirjete” ja „failide” kogumikud.</strong> Tavapäraselt on sageli mugav avaldada „kirjeid” ja „faile” eraldi kogumikena, et neid saaks avaldada erinevatel ajakavadel, nt kraapimismäärade alusel. „Kirje” on ainult metadata sisaldav kogumik, mis sisaldab teavet nagu raamatu pealkirjad, autorid, ISBN-id jne, samas kui „failid” on kogumikud, mis sisaldavad tegelikke faile (pdf, epub). Lõpuks otsustasime suhteliselt lihtsa standardi kasuks. See on üsna paindlik, mitte-normatiivne ja pidevas arengus. <strong>Torrendid.</strong> Metadata failid ja binaarandmete kaustad võivad olla koondatud torrentitesse, kusjuures üks torrent iga metadata faili või binaarandmete kausta kohta. Torrentidel peab olema algne faili/kausta nimi pluss <code>.torrent</code> järelliide failinimena. <a %(wikipedia_annas_archive)s>Anna Arhiiv</a> on kaugelt maailma suurim variraamatukogu ja ainus oma mastaabis variraamatukogu, mis on täielikult avatud lähtekoodiga ja avatud andmetega. Allpool on tabel meie Datasets lehelt (veidi muudetud): Saavutasime selle kolmel viisil: Olemasolevate avatud andmetega variraamatukogude peegeldamine (nagu Sci-Hub ja Library Genesis). Abistamine variraamatukogusid, kes soovivad olla avatumad, kuid kellel ei olnud selleks aega ega ressursse (nagu Libgeni koomiksikogu). Raamatukogude kraapimine, kes ei soovi jagada hulgi (nagu Z-Library). (2) ja (3) puhul haldame nüüd ise märkimisväärset kogust torrenteid (sadu TB-sid). Siiani oleme neid kogusid käsitlenud ühekordsetena, mis tähendab iga kogu jaoks kohandatud infrastruktuuri ja andmete korraldust. See lisab igale väljaandele märkimisväärset üldkulusid ja muudab eriti keeruliseks teha rohkem järkjärgulisi väljaandeid. Seetõttu otsustasime oma väljaanded standardiseerida. See on tehniline blogipostitus, milles tutvustame oma standardit: <strong>Anna Arhiivi Konteinerid</strong>. Anna Arhiivi Konteinerid (AAC): maailma suurima variraamatukogu väljaannete standardiseerimine Anna Arhiivist on saanud maailma suurim variraamatukogu, mis nõuab meie väljaannete standardiseerimist. 300GB+ raamatukaasi avaldatud Lõpuks on meil hea meel teatada väikesest väljaandest. Koostöös Libgen.rs haru operaatoritega jagame kõiki nende raamatukaasi torrentide ja IPFS-i kaudu. See jaotab kaante vaatamise koormuse rohkemate masinate vahel ja säilitab neid paremini. Paljudel juhtudel (kuid mitte kõigil) on raamatukaasid failides endas kaasas, seega on see omamoodi "tuletatud andmed". Kuid nende olemasolu IPFS-is on endiselt väga kasulik nii Anna Arhiivi kui ka erinevate Library Genesis harude igapäevaseks toimimiseks. Nagu tavaliselt, leiate selle väljaande Piraadi Raamatukogu Peeglist (MUUDATUSED: kolitud <a %(wikipedia_annas_archive)s>Anna Arhiivi</a>). Me ei lingi sellele siin, kuid leiate selle hõlpsasti. Loodetavasti saame nüüd, kui meil on korralik alternatiiv Z-Raamatukogule, oma tempot veidi lõdvendada. See töökoormus ei ole eriti jätkusuutlik. Kui olete huvitatud programmeerimise, serveri haldamise või säilitustöödega abistamisest, võtke kindlasti meiega ühendust. Veel on palju <a %(annas_archive)s>tööd teha</a>. Täname teid huvi ja toetuse eest. Üleminek ElasticSearchile Mõned päringud võtsid väga kaua aega, kuni nad hõivasid kõik avatud ühendused. Vaikimisi on MySQL-il minimaalne sõna pikkus, vastasel juhul võib teie indeks muutuda väga suureks. Inimesed teatasid, et nad ei saanud otsida "Ben Hur". Otsing oli ainult mõnevõrra kiire, kui see oli täielikult mällu laaditud, mis nõudis meilt kallima masina hankimist, et seda käitada, pluss mõned käsud indeksi eelkoormamiseks käivitamisel. Me ei oleks suutnud seda hõlpsasti laiendada, et luua uusi funktsioone, nagu parem <a %(wikipedia_cjk_characters)s>tükeldamine mitte-tühikuga keeltele</a>, filtreerimine/faktimine, sorteerimine, "kas mõtlesite" soovitused, automaatne täitmine jne. Üks meie <a %(annas_archive)s>piletitest</a> oli meie otsingusüsteemi probleemide kogum. Kasutasime MySQL täisteksti otsingut, kuna kogu meie andmed olid niikuinii MySQL-is. Kuid sellel olid piirangud: Pärast paljude ekspertidega rääkimist otsustasime ElasticSearchi kasuks. See pole olnud täiuslik (nende vaikimisi "kas mõtlesite" soovitused ja automaatse täitmise funktsioonid on kehvad), kuid üldiselt on see olnud palju parem kui MySQL otsingu jaoks. Me ei ole endiselt <a %(youtube)s>liiga innukad</a> seda kasutama mis tahes missioonikriitiliste andmete jaoks (kuigi nad on teinud palju <a %(elastic_co)s>edusamme</a>), kuid üldiselt oleme üleminekuga üsna rahul. Praegu oleme rakendanud palju kiirema otsingu, parema keeletoe, parema asjakohasuse sorteerimise, erinevad sorteerimisvõimalused ja filtreerimise keele/raamatu tüübi/faili tüübi järgi. Kui olete uudishimulik, kuidas see töötab, <a %(annas_archive_l140)s>vaadake</a> <a %(annas_archive_l1115)s>seda</a> <a %(annas_archive_l1635)s>lähemalt</a>. See on üsna ligipääsetav, kuigi võiks kasutada rohkem kommentaare… Anna Arhiiv on täielikult avatud lähtekoodiga Usume, et teave peaks olema vaba, ja meie enda kood ei ole erand. Oleme avaldanud kogu oma koodi meie eraviisiliselt hostitud Gitlabi instantsis: <a %(annas_archive)s>Anna Tarkvara</a>. Kasutame ka probleemide jälgijat oma töö korraldamiseks. Kui soovite meie arendustegevuses osaleda, on see suurepärane koht alustamiseks. Et anda teile aimu asjadest, millega tegeleme, võtke meie hiljutine töö kliendipoolsete jõudluse paranduste kallal. Kuna me pole veel lehekülgede jagamist rakendanud, tagastame sageli väga pikki otsingulehti, 100-200 tulemust. Me ei tahtnud otsingutulemusi liiga vara katkestada, kuid see tähendas, et see aeglustas mõningaid seadmeid. Selleks rakendasime väikese nipi: pakkisime enamiku otsingutulemustest HTML kommentaaridesse (<code><!-- --></code>), ja siis kirjutasime väikese Javascripti, mis tuvastab, millal tulemus peaks nähtavaks muutuma, sel hetkel eemaldame kommentaari: DOM "virtualiseerimine" teostatud 23 reaga, pole vaja uhkeid teeke! See on selline kiire pragmaatiline kood, mille saate, kui teil on piiratud aeg ja reaalsed probleemid, mis vajavad lahendamist. On teatatud, et meie otsing töötab nüüd hästi aeglastel seadmetel! Teine suur pingutus oli andmebaasi loomise automatiseerimine. Kui me käivitasime, tõmbasime lihtsalt juhuslikult erinevaid allikaid kokku. Nüüd tahame neid ajakohasena hoida, seega kirjutasime hulga skripte, et alla laadida uut metadata kahest Library Genesis harust ja neid integreerida. Eesmärk on mitte ainult muuta see meie arhiivi jaoks kasulikuks, vaid ka lihtsustada asju kõigile, kes soovivad varjatud raamatukogu metadata uurida. Eesmärk oleks Jupyteri märkmik, kus on saadaval igasugune huvitav metadata, et saaksime teha rohkem uurimistööd, näiteks välja selgitada, milline <a %(blog)s>protsent ISBN-idest on igaveseks säilitatud</a>. Lõpuks uuendasime oma annetussüsteemi. Nüüd saate kasutada krediitkaarti, et otse meie krüptorahakottidesse raha kanda, ilma et peaksite krüptovaluutade kohta midagi teadma. Jätkame jälgimist, kui hästi see praktikas töötab, kuid see on suur asi. Kuna Z-Library on maas ja selle (väidetavad) asutajad arreteeriti, oleme töötanud ööpäevaringselt, et pakkuda head alternatiivi Anna Arhiiviga (me ei lingi seda siin, kuid võite seda guugeldada). Siin on mõned asjad, mida oleme hiljuti saavutanud. Anna Uuendus: täielikult avatud lähtekoodiga arhiiv, ElasticSearch, 300GB+ raamatukaasi Oleme töötanud ööpäevaringselt, et pakkuda head alternatiivi Anna Arhiiviga. Siin on mõned asjad, mida oleme hiljuti saavutanud. Analüüs Semantilisi duplikaate (sama raamatu erinevad skaneeringud) saab teoreetiliselt välja filtreerida, kuid see on keeruline. Koomikseid käsitsi läbi vaadates leidsime liiga palju valepositiivseid. On mõned duplikaadid ainult MD5 järgi, mis on suhteliselt raiskav, kuid nende filtreerimine annaks meile ainult umbes 1% in kokkuhoidu. Selles mastaabis on see siiski umbes 1TB, kuid ka selles mastaabis ei oma 1TB tegelikult tähtsust. Me ei taha riskida andmete kogemata hävitamisega selles protsessis. Leidsime hulga mitte-raamatulisi andmeid, nagu koomiksiraamatutel põhinevad filmid. See tundub samuti raiskav, kuna need on juba laialdaselt kättesaadavad muude vahendite kaudu. Siiski mõistsime, et me ei saa lihtsalt filmifaile välja filtreerida, kuna on ka <em>interaktiivseid koomiksiraamatuid</em>, mis anti välja arvutis, mida keegi salvestas ja salvestas filmidena. Lõppkokkuvõttes säästaksime kogu kollektsioonist kustutades vaid paar protsenti. Siis meenus meile, et oleme andmehoidjad ja ka need, kes seda peegeldavad, on andmehoidjad, seega, "MIDA SA MÕTLED, KUSTUTA?!" :) Kui teie salvestusklastrisse visatakse 95TB, proovite mõista, mis seal üldse on… Tegime mõned analüüsid, et näha, kas saaksime suurust veidi vähendada, näiteks eemaldades duplikaadid. Siin on mõned meie leiud: Seetõttu esitleme teile täismahus, muutmata kollektsiooni. See on palju andmeid, kuid loodame, et piisavalt inimesi hoolib sellest, et seda ikkagi jagada. Koostöö Arvestades selle suurust, on see kogu olnud pikka aega meie soovinimekirjas, nii et pärast meie edu Z-Raamatukogu varundamisel seadsime oma sihid sellele kogule. Alguses kraapisime seda otse, mis oli üsna väljakutse, kuna nende server polnud parimas seisukorras. Saime sel viisil umbes 15TB, kuid see oli aeglane. Õnneks õnnestus meil võtta ühendust raamatukogu operaatoriga, kes nõustus meile kõik andmed otse saatma, mis oli palju kiirem. Andmete ülekandmine ja töötlemine võttis siiski rohkem kui pool aastat ning me peaaegu kaotasime kõik andmed ketta rikkumise tõttu, mis oleks tähendanud, et pidime alustama otsast peale. See kogemus on pannud meid uskuma, et on oluline need andmed võimalikult kiiresti levitada, et neid saaks laialdaselt peegeldada. Oleme vaid ühe või kahe ebaõnnestunud ajastusega juhtumi kaugusel sellest, et see kogu igaveseks kaotada! Kogu Kiire liikumine tähendab, et kogu on veidi organiseerimata… Vaatame lähemalt. Kujutage ette, et meil on failisüsteem (mida tegelikkuses jagame torrentide vahel): Esimene kataloog, <code>/repository</code>, on selle struktureeritum osa. See kataloog sisaldab niinimetatud "tuhandeid katalooge": katalooge, milles on igaühes tuhat faili, mis on andmebaasis järjekorras nummerdatud. Kataloog <code>0</code> sisaldab faile comic_id 0–999 ja nii edasi. See on sama skeem, mida Library Genesis on kasutanud oma ilukirjanduse ja mitte-ilukirjanduse kogude jaoks. Idee on, et iga "tuhande kataloog" muudetakse automaatselt torrentiks niipea, kui see on täis. Kuid Libgen.li operaator ei teinud kunagi selle kogu jaoks torrente, seega muutusid tuhandete kataloogid tõenäoliselt ebamugavaks ja andsid teed "sorteerimata kataloogidele". Need on <code>/comics0</code> kuni <code>/comics4</code>. Neil kõigil on unikaalsed kataloogistruktuurid, mis tõenäoliselt olid failide kogumiseks mõistlikud, kuid ei tundu meile praegu eriti loogilised. Õnneks viitab metadata endiselt otse kõigile neile failidele, seega nende salvestuskorraldus kettal ei oma tegelikult tähtsust! Metadata on saadaval MySQL andmebaasi kujul. Seda saab otse alla laadida Libgen.li veebisaidilt, kuid teeme selle kättesaadavaks ka torrentina koos meie enda tabeliga, mis sisaldab kõiki MD5 räsisid. <q>Dr. Barbara Gordon püüab end kaotada raamatukogu argises maailmas…</q> Libgeni harud Esmalt veidi taustast. Võib-olla teate Library Genesist nende eepilise raamatukogu poolest. Vähem inimesi teab, et Library Genesis vabatahtlikud on loonud ka teisi projekte, nagu märkimisväärne ajakirjade ja standarddokumentide kogu, täielik Sci-Hubi varukoopia (koostöös Sci-Hubi asutaja Alexandra Elbakyaniga) ja tõepoolest, tohutu koomiksite kogu. Mingil hetkel läksid Library Genesis peeglite erinevad operaatorid oma teed, mis viis praeguse olukorrani, kus on mitmeid erinevaid "harusid", mis kõik kannavad endiselt Library Genesis nime. Libgen.li harul on ainulaadselt see koomiksite kogu, samuti märkimisväärne ajakirjade kogu (millega me samuti tegeleme). Rahakogumine Väljastame need andmed suurtes osades. Esimene torrent on <code>/comics0</code>, mille panime ühte suurde 12TB .tar faili. See on teie kõvakettale ja torrentitarkvarale parem kui miljon väiksemat faili. Selle väljaande osana korraldame rahakogumise. Soovime koguda 20 000 dollarit, et katta selle kollektsiooni tegevus- ja lepingukulud ning võimaldada jätkuvaid ja tulevasi projekte. Meil on mõned <em>suured</em> projektid töös. <em>Keda ma oma annetusega toetan?</em> Lühidalt: me varundame kogu inimkonna teadmised ja kultuuri ning teeme need kergesti kättesaadavaks. Kogu meie kood ja andmed on avatud lähtekoodiga, oleme täielikult vabatahtlikel põhinev projekt ja oleme seni päästnud 125TB raamatuid (lisaks Libgeni ja Scihubi olemasolevatele torrentitele). Lõppkokkuvõttes ehitame hoogratast, mis võimaldab ja motiveerib inimesi leidma, skaneerima ja varundama kõiki maailma raamatuid. Kirjutame oma peaplaanist tulevases postituses. :) Kui annetate 12-kuulise "Amazing Archivist" liikmesuse eest (780 dollarit), saate <strong>“adopteerida torrent”</strong>, mis tähendab, et paneme teie kasutajanime või sõnumi ühe torrenti failinimeks! Saate annetada, minnes <a %(wikipedia_annas_archive)s>Anna arhiiv</a> ja klõpsates nupul "Annetage". Otsime ka rohkem vabatahtlikke: tarkvarainsenere, turvauurijaid, anonüümsete kaupmeeste eksperte ja tõlkijaid. Saate meid toetada ka majutusteenuste pakkumisega. Ja muidugi, palun jagage meie torrente! Tänu kõigile, kes on meid juba nii heldelt toetanud! Te tõesti muudate maailma. Siin on seni välja antud torrentid (töötleme veel ülejäänud): Kõik torrentid leiate <a %(wikipedia_annas_archive)s>Anna arhiiv</a> alt "Datasets" (me ei linki sinna otse, et lingid sellele blogile ei eemaldataks Redditist, Twitterist jne). Sealt edasi järgige linki Tor veebisaidile. <a %(news_ycombinator)s>Arutle Hacker Newsis</a> Mis edasi? Hulk torrente on suurepärased pikaajaliseks säilitamiseks, kuid mitte igapäevaseks juurdepääsuks. Teeme koostööd majutuspartneritega, et kõik need andmed veebis kättesaadavaks teha (kuna Anna arhiiv ei majuta midagi otse). Muidugi leiate need allalaadimislingid Anna arhiivist. Kutsume kõiki ka nende andmetega midagi ette võtma! Aidake meil neid paremini analüüsida, dubleerida, panna IPFS-i, remixida, treenida oma AI-mudeleid nendega jne. See kõik on teie, ja me ei jõua ära oodata, mida te sellega teete. Lõpuks, nagu varem öeldud, on meil veel mõned suured väljaanded tulemas (kui <em>keegi</em> võiks <em>kogemata</em> saata meile teatud <em>ACS4</em> andmebaasi dumpi, teate, kust meid leida...), samuti ehitame hoogratast kõigi maailma raamatute varundamiseks. Nii et püsige lainel, me alles alustame. - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Suurim koomiksite varjatud raamatukogu maailmas on tõenäoliselt ühe konkreetse Library Genesis haru oma: Libgen.li. Selle saidi haldaja suutis koguda hullumeelse koomiksite kogu, mis koosneb üle 2 miljoni failist, kogumahuga üle 95TB. Kuid erinevalt teistest Library Genesis kogudest ei olnud see saadaval hulgimüügina torrentide kaudu. Saate neid koomikseid üksikult tema aeglase isikliku serveri kaudu — üksik tõrkeallikas. Kuni tänaseni! Selles postituses räägime teile rohkem sellest kogust ja meie rahakogumisest, et toetada selle töö jätkamist. Anna Arhiiv on varundanud maailma suurima koomiksite varjatud raamatukogu (95TB) — saate aidata seda seemendada Maailma suurimal koomiksite varjatud raamatukogul oli üksik tõrkeallikas.. kuni tänaseni. Hoiatus: see blogipostitus on aegunud. Oleme otsustanud, et IPFS ei ole veel valmis laialdaseks kasutamiseks. Me linkime endiselt faile IPFS-is Anna Arhiivist, kui võimalik, kuid me ei hosti neid enam ise ega soovita teistel kasutada IPFS-i peegeldamiseks. Kui soovite aidata meie kollektsiooni säilitada, vaadake meie Torrentsi lehte. 5 998 794 raamatu paigutamine IPFS-i Koopiate paljundamine Tagasi meie algse küsimuse juurde: kuidas saame väita, et säilitame oma kogusid igavesti? Peamine probleem siin on see, et meie kogu on <a %(torrents_stats)s>kiiresti kasvanud</a>, kraapides ja avatud lähtekoodiga tehes mõned massiivsed kogud (lisaks hämmastavale tööle, mida on juba teinud teised avatud andmete variraamatukogud nagu Sci-Hub ja Library Genesis). See andmete kasv muudab kogude peegeldamise üle maailma raskemaks. Andmete salvestamine on kallis! Kuid oleme optimistlikud, eriti kui jälgime järgmisi kolme trendi. Meie kogude <a %(annas_archive_stats)s>kogumaht</a> viimaste kuude jooksul, jaotatuna torrentiseemnete arvu järgi. HDD hinnatrendid erinevatest allikatest (klõpsake uuringu vaatamiseks). <a %(critical_window_chinese)s>Hiina versioon 中文版</a>, arutle <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Oleme korjanud madalal rippuvad viljad See tuleneb otse meie eespool arutatud prioriteetidest. Eelistame töötada esmalt suurte kogude vabastamise kallal. Nüüd, kui oleme kindlustanud mõned maailma suurimad kogud, ootame, et meie kasv oleks palju aeglasem. Väiksemate kogude pikk saba on endiselt olemas ja iga päev skaneeritakse või avaldatakse uusi raamatuid, kuid tõenäoliselt on see tempo palju aeglasem. Võime endiselt kahekordistuda või isegi kolmekordistuda, kuid pikema aja jooksul. OCR-i parandused. Prioriteedid Teaduse ja inseneritarkvara kood Kõigi eelnevate ilukirjanduslikud või meelelahutuslikud versioonid Geograafilised andmed (nt kaardid, geoloogilised uuringud) Ettevõtete või valitsuste sisemised andmed (lekked) Mõõtmisandmed nagu teaduslikud mõõtmised, majandusandmed, ettevõtete aruanded Metadata kirjed üldiselt (nii ilukirjanduse kui ka mitteilukirjanduse; teiste meediumite, kunsti, inimeste jne kohta; sealhulgas arvustused) Teatmekirjandusraamatud Teadusajakirjad, ajalehed, käsiraamatud Loengute, dokumentaalfilmide, taskuhäälingute mitteilukirjanduslikud transkriptsioonid Orgaanilised andmed nagu DNA järjestused, taimeseemned või mikroobide proovid Teadusartiklid, ajakirjad, aruanded Teaduse ja inseneri veebisaidid, veebiarutelud Õigus- või kohtumenetluste transkriptsioonid Ainulaadselt hävimisohus (nt sõja, rahastamise kärbete, kohtuasjade või poliitilise tagakiusamise tõttu) Haruldased Ainulaadselt tähelepanuta jäetud Miks hoolime nii palju teadusartiklitest ja raamatutest? Jätame kõrvale meie põhilise usu säilitamisse üldiselt — võime sellest kirjutada teise postituse. Miks siis just teadusartiklid ja raamatud? Vastus on lihtne: <strong>informatsiooni tihedus</strong>. Iga salvestusmegabaidi kohta salvestab kirjalik tekst kõige rohkem teavet kõigist meediumitest. Kuigi hoolime nii teadmistest kui kultuurist, hoolime rohkem esimesest. Üldiselt leiame informatsiooni tiheduse ja säilitamise tähtsuse hierarhia, mis näeb välja umbes selline: Selle loendi järjestus on mõnevõrra meelevaldne — mitmed üksused on võrdsed või meie meeskonnas on erimeelsusi — ja me tõenäoliselt unustame mõned olulised kategooriad. Kuid see on ligikaudu see, kuidas me prioriteete seame. Mõned neist üksustest on teistega võrreldes liiga erinevad, et me peaksime muretsema (või on juba teiste asutuste poolt hoolitsetud), nagu orgaanilised andmed või geograafilised andmed. Kuid enamik loendis olevaid üksusi on meile tegelikult olulised. Teine suur tegur meie prioriteetide seadmisel on see, kui palju on teatud töö ohus. Eelistame keskenduda töödele, mis on: Lõpuks hoolime mastaabist. Meil on piiratud aeg ja raha, seega eelistame kulutada kuu aega 10 000 raamatu päästmiseks kui 1 000 raamatu päästmiseks — kui need on umbes võrdselt väärtuslikud ja ohus. <em><q>Kadunut ei saa taastada; kuid päästkem see, mis alles jääb: mitte võlvide ja lukkudega, mis kaitsevad neid avalikkuse silma ja kasutuse eest, määrates nad aja raiskamisele, vaid sellise koopiate paljundamisega, mis asetab nad õnnetuste haardeulatusest välja.</q></em><br>— Thomas Jefferson, 1791 Varjuraamatukogud Kood võib olla avatud lähtekoodiga Githubis, kuid Githubi tervikuna ei saa lihtsalt peegeldada ja seega säilitada (kuigi antud juhul on enamik koodirepositooriume piisavalt laialdaselt levitatud) Metadata kirjeid saab vabalt vaadata Worldcati veebisaidil, kuid mitte alla laadida hulgi (kuni me neid <a %(worldcat_scrape)s>kraapisime</a>) Reddit on tasuta kasutamiseks, kuid on hiljuti kehtestanud ranged kraapimisvastased meetmed, andmenäljaste LLM-i treeningute tõttu (sellest hiljem rohkem) On palju organisatsioone, kellel on sarnased missioonid ja sarnased prioriteedid. Tõepoolest, on raamatukogusid, arhiive, laboreid, muuseume ja teisi asutusi, kelle ülesandeks on selline säilitamine. Paljud neist on hästi rahastatud, valitsuste, üksikisikute või ettevõtete poolt. Kuid neil on üks suur pimeala: õigussüsteem. Siin peitub varjuraamatukogude ainulaadne roll ja põhjus, miks Anna Arhiiv eksisteerib. Me saame teha asju, mida teised asutused ei tohi teha. Nüüd, see ei ole (tihti) nii, et me saame arhiveerida materjale, mida mujal on ebaseaduslik säilitada. Ei, paljudes kohtades on seaduslik luua arhiiv mis tahes raamatutega, paberitega, ajakirjadega jne. Kuid see, mida juriidilistel arhiividel sageli puudub, on <strong>redundantsus ja pikaealisus</strong>. On olemas raamatuid, millest on ainult üks eksemplar kuskil füüsilises raamatukogus. On olemas metadata kirjeid, mida kaitseb üksainus korporatsioon. On olemas ajalehti, mis on säilinud ainult mikrofilmidel ühesainsas arhiivis. Raamatukogud võivad saada rahastuse kärpeid, korporatsioonid võivad pankrotti minna, arhiive võib pommitada ja maha põletada. See ei ole hüpoteetiline — see juhtub kogu aeg. Anna Arhiivis saame ainulaadselt teha seda, et salvestame palju teoseid, suures mahus. Saame koguda artikleid, raamatuid, ajakirju ja palju muud ning levitada neid hulgi. Praegu teeme seda torrentite kaudu, kuid täpsed tehnoloogiad ei ole olulised ja muutuvad aja jooksul. Oluline on saada palju koopiaid levitatud üle maailma. See üle 200 aasta vana tsitaat on endiselt tõene: Kiire märkus avaliku domeeni kohta. Kuna Anna Arhiiv keskendub ainulaadselt tegevustele, mis on paljudes kohtades maailmas ebaseaduslikud, ei vaeva me end laialdaselt kättesaadavate kogudega, nagu avaliku domeeni raamatud. Juriidilised üksused hoolitsevad sageli juba selle eest hästi. Siiski on kaalutlusi, mis panevad meid mõnikord töötama avalikult kättesaadavate kogudega: - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Salvestuskulud langevad jätkuvalt eksponentsiaalselt 3. Parandused teabe tiheduses Praegu salvestame raamatuid nende algses vormingus, nagu need meile antakse. Muidugi, need on tihendatud, kuid sageli on need siiski suured skaneeringud või lehekülgede fotod. Siiani on ainus võimalus meie kogu kogumahtu vähendada olnud agressiivsem tihendamine või dubleerimise eemaldamine. Kuid märkimisväärsete kokkuhoidude saavutamiseks on mõlemad meie maitse jaoks liiga kaotavad. Fotode tugev tihendamine võib muuta teksti vaevu loetavaks. Ja dubleerimise eemaldamine nõuab suurt kindlust, et raamatud on täpselt samad, mis on sageli liiga ebatäpne, eriti kui sisu on sama, kuid skaneeringud on tehtud erinevatel aegadel. Alati on olnud kolmas võimalus, kuid selle kvaliteet on olnud nii kohutav, et me pole seda kunagi kaalunud: <strong>OCR ehk optiline märgituvastus</strong>. See on protsess, kus fotod muudetakse lihttekstiks, kasutades tehisintellekti fotodel olevate märkide tuvastamiseks. Selleks on tööriistad juba ammu olemas olnud ja need on olnud üsna korralikud, kuid "üsna korralik" ei ole säilitamise eesmärgil piisav. Kuid hiljutised multimodaalsed süvaõppemudelid on teinud äärmiselt kiireid edusamme, kuigi endiselt kõrgete kuludega. Eeldame, et nii täpsus kui ka kulud paranevad lähiaastatel märkimisväärselt, kuni selleni, et see muutub realistlikuks kogu meie raamatukogule rakendamiseks. Kui see juhtub, säilitame tõenäoliselt siiski algsed failid, kuid lisaks võiks meil olla palju väiksem versioon meie raamatukogust, mida enamik inimesi soovib peegeldada. Trikk on selles, et lihttekst ise tihendub veelgi paremini ja on palju lihtsam dubleerimist eemaldada, andes meile veelgi rohkem kokkuhoidu. Kokkuvõttes ei ole ebareaalne oodata vähemalt 5-10-kordset kogufaili suuruse vähenemist, võib-olla isegi rohkem. Isegi konservatiivse 5-kordse vähenemise korral vaataksime <strong>10 aasta jooksul 1 000–3 000 dollarit, isegi kui meie raamatukogu kolmekordistub</strong>. Kirjutamise ajal on <a %(diskprices)s>kettahinnad</a> TB kohta umbes 12 dollarit uute ketaste puhul, 8 dollarit kasutatud ketaste puhul ja 4 dollarit lindi puhul. Kui oleme konservatiivsed ja vaatame ainult uusi kettaid, tähendab see, et petabaidi salvestamine maksab umbes 12 000 dollarit. Kui eeldame, et meie raamatukogu kolmekordistub 900TB-lt 2,7PB-le, tähendaks see 32 400 dollarit kogu meie raamatukogu peegeldamiseks. Lisades elektri, muu riistvara kulud ja nii edasi, ümardame selle 40 000 dollarini. Või lindiga rohkem nagu 15 000–20 000 dollarit. Ühelt poolt <strong>15 000–40 000 dollarit kogu inimteadmiste summa eest on soodne</strong>. Teiselt poolt on natuke järsk oodata tonni täiskoopiaid, eriti kui soovime, et need inimesed jätkaksid oma torrentite külvamist teiste hüvanguks. See on täna. Kuid edasiminek jätkub: Kõvaketta kulud TB kohta on viimase 10 aasta jooksul umbes kolmandiku võrra vähenenud ja tõenäoliselt langevad sarnase tempoga. Lint näib olevat sarnasel trajektooril. SSD hinnad langevad veelgi kiiremini ja võivad kümnendi lõpuks HDD hinnad üle võtta. Kui see kehtib, siis 10 aasta pärast võime vaadata ainult 5 000–13 000 dollarit, et peegeldada kogu meie kogu (1/3), või isegi vähem, kui kasvame vähem. Kuigi see on endiselt palju raha, on see paljudele inimestele saavutatav. Ja see võib olla veelgi parem järgmise punkti tõttu… Anna Arhiivis küsitakse meilt sageli, kuidas saame väita, et säilitame oma kogusid igavesti, kui kogumaht juba lähenev 1 petabaidile (1000 TB) ja kasvab endiselt. Selles artiklis vaatleme meie filosoofiat ja näeme, miks järgmine kümnend on meie missiooni jaoks inimkonna teadmiste ja kultuuri säilitamisel kriitiline. Kriitiline aken Kui need prognoosid on täpsed, <strong>peame lihtsalt ootama paar aastat</strong>, enne kui kogu meie kogu laialdaselt peegeldatakse. Seega, nagu Thomas Jefferson ütles, "asetatud õnnetuse käeulatusest välja." Kahjuks on LLM-ide tulek ja nende andmenäljas treenimine pannud paljud autoriõiguste omanikud kaitseseisundisse. Isegi rohkem kui nad juba olid. Paljud veebisaidid muudavad kraapimise ja arhiveerimise raskemaks, kohtuasjad lendavad ringi ja samal ajal jätkatakse füüsiliste raamatukogude ja arhiivide unarusse jätmist. Võime ainult oodata, et need suundumused halvenevad, ja paljud teosed kaovad ammu enne, kui nad avalikku omandisse jõuavad. <strong>Oleme säilitamise revolutsiooni eelõhtul, kuid <q>kaotatut ei saa taastada.</q></strong> Meil on umbes 5-10-aastane kriitiline aken, mille jooksul on veel üsna kallis variraamatukogu käitada ja luua palju peegleid üle maailma ning mille jooksul pole juurdepääs veel täielikult suletud. Kui suudame selle akna ületada, siis oleme tõepoolest säilitanud inimkonna teadmised ja kultuuri igavesti. Me ei tohiks lasta sellel ajal raisku minna. Me ei tohiks lasta sellel kriitilisel aknal meie ees sulguda. Lähme. Varjuliste raamatukogude kriitiline aken Kuidas saame väita, et säilitame oma kogusid igavesti, kui need juba lähenevad 1 PB-le? Kollektsioon Veidi rohkem teavet kollektsiooni kohta. <a %(duxiu)s>Duxiu</a> on massiivne skannitud raamatute andmebaas, mille on loonud <a %(chaoxing)s>SuperStar Digital Library Group</a>. Enamik on akadeemilised raamatud, mis on skannitud, et muuta need ülikoolidele ja raamatukogudele digitaalselt kättesaadavaks. Meie ingliskeelsele publikule on <a %(library_princeton)s>Princeton</a> ja <a %(guides_lib_uw)s>Washingtoni Ülikool</a> head ülevaated. Samuti on suurepärane artikkel, mis annab rohkem tausta: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (otsige see üles Anna Arhiivist). Duxiu raamatuid on Hiina internetis pikka aega piraatitud. Tavaliselt müüakse neid edasimüüjate poolt vähem kui dollari eest. Neid levitatakse tavaliselt Hiina Google Drive'i ekvivalendi kaudu, mida on sageli häkitud, et võimaldada rohkem salvestusruumi. Mõned tehnilised üksikasjad leiate <a %(github_duty_machine)s>siit</a> ja <a %(github_821_github_io)s>siit</a>. Kuigi raamatuid on poolavalikult levitatud, on neid üsna keeruline hulgi hankida. See oli meie TODO-nimekirjas kõrgel kohal ja eraldasime sellele mitu kuud täiskohaga tööd. Kuid hiljuti võttis meiega ühendust uskumatu, hämmastav ja andekas vabatahtlik, kes teatas, et on kogu selle töö juba ära teinud — suure kulu eest. Nad jagasid kogu kollektsiooni meiega, ootamata midagi vastutasuks, välja arvatud pikaajalise säilitamise garantii. Tõeliselt märkimisväärne. Nad nõustusid paluma abi selle kollektsiooni OCR-i tegemiseks. Kollektsioonis on 7 543 702 faili. See on rohkem kui Library Genesis mitte-ilukirjandus (umbes 5,3 miljonit). Kogufaili suurus on praegusel kujul umbes 359TB (326TiB). Oleme avatud teistele ettepanekutele ja ideedele. Lihtsalt võtke meiega ühendust. Vaadake Anna Arhiivi, et saada rohkem teavet meie kollektsioonide, säilitamispüüdluste ja selle kohta, kuidas saate aidata. Aitäh! Näidisleheküljed Et tõestada meile, et teil on hea torujuhe, on siin mõned näidisleheküljed, millega alustada, raamatust ülijuhtide kohta. Teie torujuhe peaks korralikult käsitlema matemaatikat, tabeleid, graafikuid, joonealuseid märkusi ja nii edasi. Saatke oma töödeldud leheküljed meie e-posti aadressile. Kui need näevad head välja, saadame teile rohkem privaatselt ja eeldame, et suudate ka nendele kiiresti oma torujuhet rakendada. Kui oleme rahul, saame sõlmida kokkuleppe. - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Hiina versioon 中文版</a>, <a %(news_ycombinator)s>Arutle Hacker Newsis</a> See on lühike blogipostitus. Otsime mõnda ettevõtet või asutust, kes aitaks meid OCR-i ja teksti eraldamisega meie omandatud massiivse kogu jaoks, vastutasuks eksklusiivse varajase juurdepääsu eest. Pärast embargo perioodi avaldame loomulikult kogu kogu. Kõrgekvaliteediline akadeemiline tekst on LLM-ide treenimiseks äärmiselt kasulik. Kuigi meie kollektsioon on hiinakeelne, peaks see olema kasulik ka ingliskeelsete LLM-ide treenimiseks: mudelid näivad kodeerivat kontseptsioone ja teadmisi sõltumata allikakeelest. Selleks tuleb tekst skannidest välja võtta. Mida saab Anna Arhiiv sellest? Raamatute täisteksti otsing oma kasutajatele. Kuna meie eesmärgid ühtivad LLM-i arendajate omadega, otsime koostööpartnerit. Oleme valmis andma teile <strong>eksklusiivse varajase juurdepääsu sellele kollektsioonile hulgi 1 aastaks</strong>, kui suudate teha korralikku OCR-i ja teksti väljavõtmist. Kui olete valmis jagama meiega kogu oma torujuhtme koodi, oleme valmis kollektsiooni pikemaks ajaks embargoga hoidma. Eksklusiivne juurdepääs LLM-ettevõtetele maailma suurimale Hiina mitte-ilukirjanduse kogule <em><strong>Lühidalt:</strong> Anna Arhiiv omandas ainulaadse 7,5 miljoni / 350TB Hiina mitte-ilukirjanduse raamatute kogu — suurem kui Library Genesis. Oleme valmis andma LLM-ettevõttele eksklusiivse juurdepääsu, vastutasuks kõrgekvaliteedilise OCR-i ja teksti eraldamise eest.</em> Süsteemi arhitektuur Oletame, et leidsite mõned ettevõtted, kes on valmis teie veebisaiti majutama ilma seda sulgemata — nimetame neid “vabadust armastavateks pakkujateks” 😄. Kiiresti avastate, et kõikide nende juures majutamine on üsna kallis, seega võiksite leida mõned “odavad pakkujad” ja teha tegeliku majutuse seal, proksides läbi vabadust armastavate pakkujate. Kui teete seda õigesti, ei tea odavad pakkujad kunagi, mida te majutate, ega saa kunagi kaebusi. Kõigi nende pakkujatega on oht, et nad sulgevad teid ikkagi, seega vajate ka varukoopiat. Vajame seda kõigil oma paki tasanditel. Üks mõnevõrra vabadust armastav ettevõte, kes on end huvitavasse positsiooni seadnud, on Cloudflare. Nad on <a %(blog_cloudflare)s>väitnud</a>, et nad ei ole majutusteenuse pakkuja, vaid utiliit, nagu ISP. Seetõttu ei allu nad DMCA-le ega muudele eemaldamistaotlustele ning edastavad kõik taotlused teie tegelikule majutusteenuse pakkujale. Nad on läinud nii kaugele, et on kohtusse läinud, et seda struktuuri kaitsta. Seetõttu saame neid kasutada veel ühe vahemälu ja kaitsekihina. Cloudflare ei aktsepteeri anonüümseid makseid, seega saame kasutada ainult nende tasuta plaani. See tähendab, et me ei saa kasutada nende koormuse tasakaalustamise või tõrkesiirde funktsioone. Seetõttu <a %(annas_archive_l255)s>rakendasime selle ise</a> domeeni tasandil. Lehe laadimisel kontrollib brauser, kas praegune domeen on endiselt saadaval, ja kui ei, siis kirjutab kõik URL-id ümber teisele domeenile. Kuna Cloudflare vahemällu salvestab palju lehti, tähendab see, et kasutaja saab maanduda meie peamisele domeenile, isegi kui proksiserver on maas, ja siis järgmisel klõpsul suunatakse teisele domeenile. Meil on endiselt ka tavalised operatiivsed mured, millega tegeleda, nagu serveri tervise jälgimine, tagaplaani ja esiplaani vigade logimine jne. Meie tõrkesiirde arhitektuur võimaldab ka selles osas suuremat vastupidavust, näiteks käivitades täiesti erineva serverikomplekti ühel domeenidest. Saame isegi käitada vanemaid koodi ja andmekogumite versioone sellel eraldi domeenil, juhuks kui peaversioonis jääb kriitiline viga märkamata. Saame ka kaitsta end Cloudflare’i vastu pöördumise eest, eemaldades selle ühelt domeenilt, näiteks sellelt eraldi domeenilt. Erinevad nende ideede permutatsioonid on võimalikud. Kokkuvõte See on olnud huvitav kogemus õppida, kuidas seadistada vastupidavat ja vastupidavat varjuraamatukogu otsingumootorit. Jagamiseks on veel palju üksikasju tulevastes postitustes, nii et andke teada, millest soovite rohkem teada saada! Nagu alati, otsime annetusi selle töö toetamiseks, seega vaadake kindlasti Anna Arhiivi annetuste lehte. Otsime ka teisi toetuse vorme, nagu toetused, pikaajalised sponsorid, kõrge riskiga makseteenuse pakkujad, võib-olla isegi (maitsekaid!) reklaame. Ja kui soovite panustada oma aega ja oskusi, otsime alati arendajaid, tõlkijaid ja nii edasi. Täname teid huvi ja toetuse eest. Innovatsioonimärgid Alustame oma tehnoloogiapakist. See on tahtlikult igav. Kasutame Flaski, MariaDB-d ja ElasticSearchi. See ongi kõik. Otsing on suures osas lahendatud probleem ja me ei kavatse seda uuesti leiutada. Pealegi peame oma <a %(mcfunley)s>innovatsioonimärgid</a> kulutama millelegi muule: mitte laskma end võimude poolt maha võtta. Kui seaduslik või ebaseaduslik on Anna Arhiiv täpselt? See sõltub peamiselt õiguslikust jurisdiktsioonist. Enamik riike usub mingisugusesse autoriõigusesse, mis tähendab, et inimestele või ettevõtetele antakse teatud tüüpi teoste eksklusiivne monopol teatud ajaks. Muide, Anna Arhiivis usume, et kuigi autoriõigusel on teatud eelised, on see ühiskonnale üldiselt kahjulik — aga see on lugu teiseks korraks. See eksklusiivne monopol teatud teostele tähendab, et väljaspool seda monopoli on ebaseaduslik neid teoseid otse levitada — sealhulgas meie jaoks. Kuid Anna Arhiiv on otsingumootor, mis ei levita neid teoseid otse (vähemalt mitte meie tavaveebisaidil), seega peaksime olema korras, eks? Mitte päris. Paljudes jurisdiktsioonides on ebaseaduslik mitte ainult autoriõigusega kaitstud teoste levitamine, vaid ka linkimine kohtadesse, kus seda tehakse. Klassikaline näide sellest on Ameerika Ühendriikide DMCA seadus. See on spektri kõige rangem ots. Teises spektri otsas võiks teoreetiliselt olla riike, kus autoriõiguse seadusi üldse ei ole, kuid neid tegelikult ei eksisteeri. Peaaegu igal riigil on mingisugune autoriõiguse seadus. Jõustamine on teine lugu. On palju riike, kus valitsused ei hooli autoriõiguse seaduse jõustamisest. Samuti on riike, mis asuvad kahe äärmuse vahel, keelates autoriõigusega kaitstud teoste levitamise, kuid mitte linkimise sellistele teostele. Teine kaalutlus on ettevõtte tasandil. Kui ettevõte tegutseb jurisdiktsioonis, mis ei hooli autoriõigusest, kuid ettevõte ise ei ole valmis riski võtma, võivad nad teie veebisaidi sulgeda kohe, kui keegi selle üle kaebab. Lõpuks on suur kaalutlus maksed. Kuna peame jääma anonüümseks, ei saa me kasutada traditsioonilisi maksemeetodeid. See jätab meile krüptovaluutad ja ainult väike osa ettevõtteid toetab neid (on olemas virtuaalsed deebetkaardid, mida makstakse krüptoga, kuid neid sageli ei aktsepteerita). - Anna ja meeskond (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ma juhin <a %(wikipedia_annas_archive)s>Anna Arhiivi</a>, maailma suurimat avatud lähtekoodiga mittetulunduslikku otsingumootorit <a %(wikipedia_shadow_library)s>variraamatukogudele</a>, nagu Sci-Hub, Library Genesis ja Z-Raamatukogu. Meie eesmärk on muuta teadmised ja kultuur kergesti kättesaadavaks ning lõpuks luua kogukond inimesi, kes koos arhiveerivad ja säilitavad <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>kõik maailma raamatud</a>. Selles artiklis näitan, kuidas me seda veebisaiti juhime ja millised on ainulaadsed väljakutsed, mis kaasnevad veebisaidi haldamisega, millel on küsitav juriidiline staatus, kuna varjatud heategevusorganisatsioonide jaoks ei ole "AWS-i". <em>Vaadake ka õeartiklit <a %(blog_how_to_become_a_pirate_archivist)s>Kuidas saada piraat-arhivaariks</a>.</em> Kuidas juhtida variraamatukogu: Anna Arhiivi tegevused Varjatud heategevusorganisatsioonide jaoks ei ole <q>AWS-i,</q> nii et kuidas me juhime Anna Arhiivi? Tööriistad Rakendusserver: Flask, MariaDB, ElasticSearch, Docker. Arendus: Gitlab, Weblate, Zulip. Serveri haldamine: Ansible, Checkmk, UFW. Sibula staatiline majutamine: Tor, Nginx. Proksiserver: Varnish. Vaatame, milliseid tööriistu me selle kõige saavutamiseks kasutame. See areneb väga palju, kui satume uute probleemide otsa ja leiame uusi lahendusi. On mõned otsused, mille üle oleme edasi-tagasi mõelnud. Üks neist on serveritevaheline suhtlus: varem kasutasime selleks Wireguardi, kuid avastasime, et see lõpetab aeg-ajalt andmete edastamise või edastab andmeid ainult ühes suunas. See juhtus mitmete erinevate Wireguardi seadistustega, mida proovisime, nagu näiteks <a %(github_costela_wesher)s>wesher</a> ja <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Proovisime ka SSH kaudu portide tunnelimist, kasutades autossh ja sshuttle, kuid sattusime <a %(github_sshuttle)s>probleemidesse</a> (kuigi mulle pole siiani selge, kas autossh kannatab TCP-over-TCP probleemide all või mitte — see tundub mulle lihtsalt kohmakas lahendus, aga võib-olla on see tegelikult korras?). Selle asemel pöördusime tagasi otseste ühenduste juurde serverite vahel, varjates, et server töötab odavatel pakkujatel, kasutades IP-filtreerimist UFW-ga. Sellel on puuduseks, et Docker ei tööta hästi UFW-ga, kui te ei kasuta <code>network_mode: "host"</code>. Kõik see on veidi vigadele vastuvõtlikum, sest väiksegi valekonfiguratsiooniga paljastate oma serveri internetile. Võib-olla peaksime tagasi liikuma autossh juurde — tagasiside oleks siin väga teretulnud. Oleme ka Varnishi ja Nginxi vahel edasi-tagasi mõelnud. Praegu meeldib meile Varnish, kuid sellel on oma veidrused ja karedad servad. Sama kehtib Checkmki kohta: me ei armasta seda, kuid see töötab praegu. Weblate on olnud okei, kuid mitte uskumatu — ma kardan mõnikord, et see kaotab mu andmed, kui üritan seda meie git repo-ga sünkroonida. Flask on üldiselt olnud hea, kuid sellel on mõned veidrad veidrused, mis on nõudnud palju aega silumiseks, nagu kohandatud domeenide seadistamine või probleemid selle SqlAlchemy integratsiooniga. Siiani on teised tööriistad olnud suurepärased: meil pole tõsiseid kaebusi MariaDB, ElasticSearchi, Gitlabi, Zulipi, Dockeri ja Tori kohta. Kõigil neil on olnud mõningaid probleeme, kuid mitte midagi liiga tõsist või aeganõudvat. Kogukond Esimene väljakutse võib olla üllatav. See ei ole tehniline probleem ega juriidiline probleem. See on psühholoogiline probleem: selle töö tegemine varjus võib olla uskumatult üksildane. Sõltuvalt sellest, mida te plaanite teha ja teie ohumudelist, peate võib-olla olema väga ettevaatlik. Spektri ühes otsas on inimesed nagu Alexandra Elbakyan*, Sci-Hubi asutaja, kes on oma tegevuse suhtes väga avatud. Kuid ta on kõrge riskiga arreteerimisele, kui ta praegu mõnda lääneriiki külastaks, ja võib seista silmitsi aastakümnete pikkuse vanglakaristusega. Kas see on risk, mida oleksite valmis võtma? Meie oleme spektri teises otsas; olles väga ettevaatlikud, et mitte jätta ühtegi jälge ja omades tugevat operatiivset turvalisust. * Nagu HN-is mainis "ynno", ei tahtnud Alexandra algselt olla tuntud: "Tema serverid olid seadistatud väljastama üksikasjalikke veateateid PHP-st, sealhulgas vigase allikafaili täielik tee, mis oli kataloogi /home/<USER>" Seega kasutage juhuslikke kasutajanimesid arvutites, mida selleks kasutate, juhuks kui midagi valesti seadistate. See salajasus aga kaasneb psühholoogilise hinnaga. Enamik inimesi armastab, kui nende tööd tunnustatakse, kuid te ei saa selle eest päriselus mingit au võtta. Isegi lihtsad asjad võivad olla väljakutsuvad, näiteks sõbrad, kes küsivad, millega olete tegelenud (mingil hetkel "jamamine minu NAS-iga / kodulaboriga" muutub vanaks). Seetõttu on nii oluline leida mõni kogukond. Võite loobuda mõnest operatiivsest turvalisusest, usaldades mõnda väga lähedast sõpra, keda teate, et saate sügavalt usaldada. Isegi siis olge ettevaatlik, et mitte midagi kirjalikult panna, juhuks kui nad peavad oma e-kirjad ametivõimudele üle andma või kui nende seadmed on mingil muul viisil ohustatud. Veel parem on leida mõni kaaslane piraat. Kui teie lähedased sõbrad on huvitatud teiega liitumisest, suurepärane! Vastasel juhul võite leida teisi veebis. Kahjuks on see endiselt nišikogukond. Siiani oleme leidnud vaid käputäie teisi, kes on selles valdkonnas aktiivsed. Hea alguskoht tundub olevat Library Genesis foorumid ja r/DataHoarder. Ka Arhiivimeeskonnal on sarnaselt mõtlevaid inimesi, kuigi nad tegutsevad seaduse piires (isegi kui mõnes seaduse hallis alas). Traditsioonilistel "warez" ja piraatluse stseenidel on ka inimesi, kes mõtlevad sarnaselt. Oleme avatud ideedele, kuidas edendada kogukonda ja uurida uusi mõtteid. Võtke meiega julgelt ühendust Twitteris või Redditis. Võib-olla võiksime korraldada mingisuguse foorumi või vestlusgrupi. Üks väljakutse on see, et see võib tavalistel platvormidel kergesti tsenseeritud saada, seega peaksime seda ise majutama. Samuti on kompromiss nende arutelude täielikult avalikuks muutmise (rohkem potentsiaalset kaasatust) ja privaatseks muutmise vahel (mitte lasta potentsiaalsetel "sihtmärkidel" teada, et kavatseme neid kraapida). Peame selle üle mõtlema. Andke meile teada, kui olete sellest huvitatud! Kokkuvõte Loodetavasti on see abiks äsja alustavatele piraatide arhiivipidajatele. Meil on hea meel teid sellesse maailma tervitada, seega ärge kartke ühendust võtta. Säilitame nii palju maailma teadmistest ja kultuurist kui võimalik ning peegeldame seda laialdaselt. Projektid 4. Andmete valik Sageli saate metadata abil välja selgitada mõistliku andmehulga, mida alla laadida. Isegi kui soovite lõpuks kõik andmed alla laadida, võib olla kasulik prioriseerida kõige olulisemad esemed esimesena, juhuks kui teid avastatakse ja kaitsemeetmeid parandatakse, või kuna peate ostma rohkem kettaid, või lihtsalt seetõttu, et teie elus tuleb midagi muud ette, enne kui jõuate kõik alla laadida. Näiteks võib kogus olla mitu väljaannet samast allikast (nagu raamat või film), kus üks on märgitud parima kvaliteediga. Nende väljaannete esmalt salvestamine oleks väga mõistlik. Võib-olla soovite lõpuks salvestada kõik väljaanded, kuna mõnel juhul võib metadata olla valesti märgistatud või võib väljaannete vahel olla tundmatuid kompromisse (näiteks võib "parim väljaanne" olla enamikus aspektides parim, kuid halvem teistes, nagu film, millel on kõrgem resolutsioon, kuid puuduvad subtiitrid). Samuti saate oma metadata andmebaasis otsida huvitavaid asju. Mis on suurim fail, mida hostitakse, ja miks see nii suur on? Mis on väikseim fail? Kas teatud kategooriate, keelte jms puhul on huvitavaid või ootamatuid mustreid? Kas on duplikaate või väga sarnaseid pealkirju? Kas on mustreid, millal andmed lisati, näiteks ühel päeval, mil lisati korraga palju faile? Sageli saate palju õppida, vaadates andmekogumit erinevatest vaatenurkadest. Meie puhul deduplitseerisime Z-Library raamatud Library Genesis md5 hashide vastu, säästes seeläbi palju allalaadimisaega ja kettaruumi. See on siiski üsna ainulaadne olukord. Enamasti pole olemas ulatuslikke andmebaase, millised failid on juba korralikult säilitatud teiste piraatide poolt. See on iseenesest suur võimalus kellelegi seal väljas. Oleks suurepärane, kui oleks regulaarselt uuendatav ülevaade asjadest nagu muusika ja filmid, mis on juba laialdaselt torrent-veebisaitidel külvatud ja seetõttu madalama prioriteediga piraatpeegeldustesse lisamiseks. 6. Levitus Teil on andmed, andes teile seega maailma esimese piraatpeegli oma sihtmärgist (tõenäoliselt). Paljuski on kõige raskem osa läbi, kuid kõige riskantsem osa on veel ees. Lõppude lõpuks olete seni olnud varjatud; lennanud radari all. Kõik, mida pidite tegema, oli kasutada head VPN-i kogu aeg, mitte täita oma isiklikke andmeid üheski vormis (duh) ja võib-olla kasutada spetsiaalset brauseriseanssi (või isegi teist arvutit). Nüüd peate andmeid levitama. Meie puhul tahtsime esmalt raamatud tagasi Library Genesis'ile panustada, kuid avastasime kiiresti selles raskused (ilukirjandus vs mitte-ilukirjandus sorteerimine). Seega otsustasime levitamise Library Genesis-stiilis torrentite abil. Kui teil on võimalus panustada olemasolevasse projekti, siis see võib säästa palju aega. Kuid praegu pole palju hästi organiseeritud piraatpeegleid. Oletame, et otsustate ise torrente levitada. Püüdke hoida need failid väikesed, et neid oleks lihtne peegeldada teistel veebisaitidel. Seejärel peate ise torrente külvama, jäädes samal ajal anonüümseks. Võite kasutada VPN-i (edasi- või tagasipordi suunamisega või ilma) või maksta tumbled Bitcoinidega Seedboxi eest. Kui te ei tea, mida mõned neist terminitest tähendavad, peate palju lugema, kuna on oluline, et mõistaksite siin riskide kompromisse. Võite torrentifailid ise hostida olemasolevatel torrent-veebisaitidel. Meie puhul otsustasime tegelikult hostida veebisaiti, kuna soovisime ka oma filosoofiat selgelt levitada. Saate seda ise sarnaselt teha (kasutame Njalla oma domeenide ja hostimise jaoks, makstes tumbled Bitcoinidega), kuid võite ka meiega ühendust võtta, et me saaksime teie torrente hostida. Soovime aja jooksul luua ulatusliku piraatpeeglite indeksi, kui see idee levib. Mis puutub VPN-i valikusse, siis sellest on juba palju kirjutatud, seega kordame lihtsalt üldist nõuannet valida maine järgi. Tegelikult kohtus testitud logivabad poliitikad, millel on pikk ajalugu privaatsuse kaitsmisel, on meie arvates madalaima riskiga valik. Pange tähele, et isegi kui teete kõik õigesti, ei saa te kunagi jõuda nullriskini. Näiteks kui külvate oma torrente, võib väga motiveeritud riikliku tasandi tegija tõenäoliselt vaadata VPN-serverite sissetulevaid ja väljaminevaid andmevooge ning järeldada, kes te olete. Või võite lihtsalt kuidagi eksida. Me oleme tõenäoliselt juba eksinud ja teeme seda uuesti. Õnneks ei hooli riigid <em>nii</em> palju piraatlusest. Iga projekti puhul tuleb teha otsus, kas avaldada see sama identiteediga nagu varem või mitte. Kui jätkate sama nime kasutamist, võivad varasemate projektide operatiivturvalisuse vead teile kätte maksta. Kuid erinevate nimede all avaldamine tähendab, et te ei ehita püsivamat mainet. Me otsustasime algusest peale tugeva operatiivturvalisuse kasuks, et saaksime jätkata sama identiteedi kasutamist, kuid me ei kõhkle avaldamast teise nime all, kui me eksime või kui asjaolud seda nõuavad. Sõna levitamine võib olla keeruline. Nagu me ütlesime, on see endiselt nišikogukond. Alguses postitasime Redditisse, kuid tõeliselt hoogu saime Hacker Newsis. Praegu on meie soovitus postitada see mõnesse kohta ja vaadata, mis juhtub. Ja jälle, võtke meiega ühendust. Meile meeldiks levitada rohkem piraatide arhiivimise jõupingutusi. 1. Domeeni valik / filosoofia Teadmiste ja kultuuripärandi säilitamiseks pole puudust, mis võib olla üle jõu käiv. Seetõttu on sageli kasulik võtta hetk ja mõelda, milline võiks olla teie panus. Igaühel on selle kohta erinev mõtlemisviis, kuid siin on mõned küsimused, mida võiksite endalt küsida: Meie puhul hoolisime eriti teaduse pikaajalisest säilitamisest. Teadsime Library Genesisest ja sellest, kuidas seda täielikult peegeldati mitu korda torrentite abil. Meile meeldis see idee. Siis ühel päeval üritas üks meist leida mõningaid teaduslikke õpikuid Library Genesisest, kuid ei leidnud neid, mis pani kahtluse alla, kui täielik see tegelikult oli. Seejärel otsisime neid õpikuid veebis ja leidsime need teistest kohtadest, mis istutas seemne meie projektile. Isegi enne, kui teadsime Z-Raamatukogust, oli meil idee mitte proovida kõiki neid raamatuid käsitsi koguda, vaid keskenduda olemasolevate kollektsioonide peegeldamisele ja nende tagasiviimisele Library Genesisesse. Millised oskused teil on, mida saate enda kasuks kasutada? Näiteks kui olete veebiturvalisuse ekspert, võite leida viise IP-blokkide ületamiseks turvaliste sihtmärkide jaoks. Kui olete suurepärane kogukondade organiseerimisel, siis võib-olla saate koondada mõned inimesed ühise eesmärgi ümber. Siiski on kasulik teada mõningaid programmeerimisoskusi, kui ainult selleks, et kogu selle protsessi jooksul head operatiivturvalisust säilitada. Milline oleks kõrge mõjuga ala, millele keskenduda? Kui kavatsete kulutada X tundi piraatarhiveerimisele, siis kuidas saate oma "investeeringult" suurima kasu? Millised on ainulaadsed viisid, kuidas te sellele mõtlete? Teil võivad olla mõned huvitavad ideed või lähenemisviisid, mida teised võivad olla kahe silma vahele jätnud. Kui palju aega teil selleks on? Meie nõuanne oleks alustada väikselt ja teha suuremaid projekte, kui olete sellega harjunud, kuid see võib muutuda kõikehõlmavaks. Miks olete sellest huvitatud? Mis teid kirglikuks teeb? Kui saame kokku hulga inimesi, kes kõik arhiveerivad neid asju, mis neile konkreetselt korda lähevad, kataks see palju! Te teate oma kire kohta palju rohkem kui keskmine inimene, näiteks millised on olulised andmed, mida säilitada, millised on parimad kollektsioonid ja veebikogukonnad jne. 3. Metadata kraapimine Lisamise/muutmise kuupäev: et saaksite hiljem tagasi tulla ja alla laadida faile, mida te varem ei alla laadinud (kuigi sageli saate selleks kasutada ka ID-d või räsikoodi). Räsikood (md5, sha1): et kinnitada, et olete faili õigesti alla laadinud. ID: võib olla mõni sisemine ID, kuid ID-d nagu ISBN või DOI on samuti kasulikud. Failinimi / asukoht Kirjeldus, kategooria, sildid, autorid, keel jne. Suurus: et arvutada, kui palju kettaruumi vajate. Läheme siin natuke tehnilisemaks. Veebisaitidelt metadata kraapimiseks oleme hoidnud asjad üsna lihtsad. Kasutame Python skripte, mõnikord curl'i ja MySQL andmebaasi tulemuste salvestamiseks. Me ei ole kasutanud mingeid keerulisi kraapimistarkvarasid, mis suudavad kaardistada keerulisi veebisaite, kuna seni oleme pidanud kraapima ainult ühte või kahte tüüpi lehti, lihtsalt ID-de kaudu loendades ja HTML-i parsides. Kui lehti ei ole lihtne loendada, võib teil vaja minna korralikku roomikut, mis püüab leida kõik lehed. Enne kui hakkate tervet veebisaiti kraapima, proovige seda natuke käsitsi teha. Minge ise läbi mõne tosina lehe, et saada aimu, kuidas see töötab. Mõnikord satute juba sel viisil IP-blokkide või muu huvitava käitumise otsa. Sama kehtib andmete kraapimise kohta: enne kui sihtmärgiga liiga sügavale minna, veenduge, et saate tegelikult selle andmeid tõhusalt alla laadida. Piirangutest möödahiilimiseks on mõned asjad, mida saate proovida. Kas on olemas muid IP-aadresse või servereid, mis majutavad samu andmeid, kuid millel ei ole samu piiranguid? Kas on olemas API lõpp-punkte, millel ei ole piiranguid, samas kui teistel on? Millise allalaadimiskiiruse juures teie IP blokeeritakse ja kui kauaks? Või teid ei blokeerita, vaid kiirust piiratakse? Mis juhtub, kui loote kasutajakonto, kuidas asjad siis muutuvad? Kas saate kasutada HTTP/2, et hoida ühendusi avatuna, ja kas see suurendab lehtede päringute kiirust? Kas on lehti, mis loetlevad korraga mitu faili, ja kas seal loetletud teave on piisav? Asjad, mida tõenäoliselt soovite salvestada, hõlmavad: Tavaliselt teeme seda kahes etapis. Esiteks laadime alla toored HTML-failid, tavaliselt otse MySQL-i (et vältida paljusid väikeseid faile, millest räägime allpool rohkem). Seejärel, eraldi etapis, läbime need HTML-failid ja parsimme need tegelikeks MySQL tabeliteks. Nii ei pea te kõike algusest peale uuesti alla laadima, kui avastate oma parsimiskoodis vea, kuna saate lihtsalt HTML-failid uue koodiga uuesti töödelda. Samuti on sageli lihtsam paralleelselt töödelda, säästes seega aega (ja saate kirjutada töötlemiskoodi, kui kraapimine käib, selle asemel et mõlemad etapid korraga kirjutada). Lõpuks tuleb märkida, et mõne sihtmärgi puhul on kogu olemasolev teave ainult metadata. Seal on mõned suured metadata kogud, mis pole korralikult säilitatud. Pealkiri Domeeni valik / filosoofia: Kus soovite ligikaudu keskenduda ja miks? Millised on teie ainulaadsed kirg, oskused ja asjaolud, mida saate enda kasuks kasutada? Sihtmärgi valik: Millist konkreetset kollektsiooni peegeldad? Metadata kraapimine: Failide kohta teabe kataloogimine, ilma et tegelikke (sageli palju suuremaid) faile alla laaditaks. Andmete valik: Metadata põhjal kitsendamine, millised andmed on praegu kõige olulisemad arhiveerida. Võib olla kõik, kuid sageli on mõistlik viis ruumi ja ribalaiuse säästmiseks. Andmete kraapimine: Tegelikult andmete hankimine. Levitamine: Selle pakendamine torrentitesse, kusagil kuulutamine, inimeste kaasamine selle levitamiseks. 5. Andmete kraapimine Nüüd olete valmis tegelikult andmeid hulgikaupa alla laadima. Nagu varem mainitud, peaksite selleks hetkeks olema juba käsitsi alla laadinud hulga faile, et paremini mõista sihtmärgi käitumist ja piiranguid. Kuid üllatusi on veel ees, kui hakkate korraga palju faile alla laadima. Meie nõuanne siin on peamiselt hoida see lihtsana. Alustage lihtsalt hulga failide allalaadimisest. Võite kasutada Pythoni ja seejärel laieneda mitmele lõimele. Kuid mõnikord on isegi lihtsam genereerida Bash-faile otse andmebaasist ja seejärel käivitada neid mitmes terminaliaknas, et skaleerida. Kiire tehniline nipp, mida siin mainida, on OUTFILE kasutamine MySQL-is, mida saate kirjutada kõikjale, kui keelata "secure_file_priv" mysqld.cnf-is (ja kindlasti keelake/ülekirjutage AppArmor, kui olete Linuxis). Me salvestame andmeid lihtsatele kõvaketastele. Alustage sellest, mis teil on, ja laienege aeglaselt. Võib olla ülekaalukas mõelda sadade TB-de andmete salvestamisele. Kui see on olukord, millega silmitsi seisate, pange esmalt välja hea alamhulk ja oma teadaandes küsige abi ülejäänu salvestamisel. Kui soovite ise rohkem kõvakettaid hankida, siis r/DataHoarderil on mõned head ressursid heade pakkumiste saamiseks. Püüdke mitte liiga palju muretseda uhkete failisüsteemide pärast. On lihtne langeda ZFS-i sarnaste asjade seadistamise jäneseauku. Üks tehniline detail, mida tasub teada, on see, et paljud failisüsteemid ei tule toime paljude failidega. Oleme leidnud, et lihtne lahendus on luua mitu kataloogi, näiteks erinevate ID vahemike või hash-prefiksite jaoks. Pärast andmete allalaadimist kontrollige kindlasti failide terviklikkust, kasutades metadata hash'e, kui need on saadaval. 2. Sihtmärgi valik Juurdepääsetav: ei kasuta hulgaliselt kaitsekihte, et takistada teie juurdepääsu nende metadata ja andmetele. Eriline ülevaade: teil on selle sihtmärgi kohta eriteave, näiteks teil on kuidagi eriline juurdepääs sellele kollektsioonile või olete välja mõelnud, kuidas nende kaitsetest mööda pääseda. See ei ole kohustuslik (meie eelseisev projekt ei tee midagi erilist), kuid see kindlasti aitab! Suur Niisiis, meil on ala, mida uurime, nüüd millist konkreetset kollektsiooni me peegeldame? Hea sihtmärgi jaoks on mõned asjad, mida arvestada: Kui leidsime oma teadusõpikud teistelt veebisaitidelt peale Library Genesis, püüdsime välja selgitada, kuidas need internetti jõudsid. Seejärel leidsime Z-Library ja mõistsime, et kuigi enamik raamatuid ei ilmu seal esmakordselt, jõuavad nad lõpuks sinna. Saime teada selle seosest Library Genesis'ega ning (rahalisest) stiimulistruktuurist ja paremast kasutajaliidesest, mis mõlemad tegid sellest palju täielikuma kollektsiooni. Seejärel tegime esialgse metadata ja andmete kraapimise ning mõistsime, et saame mööda nende IP allalaadimispiirangutest, kasutades ühe meie liikme erilist juurdepääsu paljudele puhverserveritele. Erinevaid sihtmärke uurides on juba oluline oma jälgi varjata, kasutades VPN-e ja ühekordseid e-posti aadresse, millest räägime hiljem rohkem. Ainulaadne: mitte juba teiste projektide poolt hästi kaetud. Kui teeme projekti, on sellel mitu etappi: Need ei ole täiesti sõltumatud etapid ja sageli saadavad hilisema etapi teadmised teid tagasi varasemasse etappi. Näiteks metadata kraapimise ajal võite avastada, et valitud sihtmärgil on kaitsemehhanismid, mis ületavad teie oskuste taset (näiteks IP-blokid), seega lähete tagasi ja leiate teise sihtmärgi. - Anna ja meeskond (<a %(reddit)s>Reddit</a>) Terveid raamatuid saab kirjutada digitaalse säilitamise <em>miks</em> üldiselt ja piraatide arhiivipidamise kohta eriti, kuid anname kiire ülevaate neile, kes pole liiga tuttavad. Maailm toodab rohkem teadmisi ja kultuuri kui kunagi varem, kuid ka rohkem sellest kaob kui kunagi varem. Inimkond usaldab suuresti selliseid ettevõtteid nagu akadeemilised kirjastajad, voogedastusteenused ja sotsiaalmeediaettevõtted selle pärandi hoidmisega, kuid nad pole sageli osutunud suurepärasteks hooldajateks. Vaadake dokumentaalfilmi "Digital Amnesia" või tõesti mõnda Jason Scotti ettekannet. On mõned asutused, kes teevad head tööd, arhiveerides nii palju kui võimalik, kuid nad on seadusega seotud. Piraatidena oleme ainulaadses positsioonis, et arhiveerida kogusid, mida nad ei saa puudutada, autoriõiguse jõustamise või muude piirangute tõttu. Samuti saame peegeldada kogusid korduvalt üle maailma, suurendades seeläbi korraliku säilitamise võimalusi. Praegu me ei hakka arutama intellektuaalomandi plusse ja miinuseid, seaduse rikkumise moraalsust, tsensuuri üle mõtisklemist ega teadmiste ja kultuuri kättesaadavuse küsimust. Kui see kõik on kõrvale jäetud, sukeldume <em>kuidas</em>. Jagame, kuidas meie meeskond sai piraatide arhiivipidajateks ja milliseid õppetunde me sellel teel õppisime. Sellele teekonnale asudes on palju väljakutseid ja loodetavasti saame teid mõnes neist aidata. Kuidas saada piraatide arhiivipidajaks Esimene väljakutse võib olla üllatav. See ei ole tehniline probleem ega juriidiline probleem. See on psühholoogiline probleem. Enne kui sukeldume, kaks uuendust Piraatide Raamatukogu Peegli kohta (MUUDETUD: kolitud <a %(wikipedia_annas_archive)s>Anna Arhiiv</a>): Saime mõned äärmiselt helded annetused. Esimene oli 10 000 dollarit anonüümselt isikult, kes on samuti toetanud "bookwarriorit", Library Genesis'i algset asutajat. Erilised tänud bookwarriorile selle annetuse vahendamise eest. Teine oli veel 10 000 dollarit anonüümselt annetajalt, kes võttis meiega ühendust pärast meie viimast väljaannet ja oli inspireeritud aitama. Samuti oli meil mitmeid väiksemaid annetusi. Suur tänu kogu teie helde toetuse eest. Meil on mõned põnevad uued projektid, mida see toetab, nii et olge kursis. Meil oli mõned tehnilised raskused meie teise väljaande suurusega, kuid meie torrendid on nüüd üleval ja jagamisel. Samuti saime helde pakkumise anonüümselt isikult, kes pakkus meie kogu jagamist oma väga kiiretel serveritel, nii et teeme nende masinatesse erilise üleslaadimise, pärast mida peaksid kõik teised, kes kogu alla laadivad, nägema suurt kiiruse paranemist. Blogipostitused Tere, mina olen Anna. Lõin <a %(wikipedia_annas_archive)s>Anna Arhiivi</a>, maailma suurima varjuraamatukogu. See on minu isiklik blogi, kus mina ja mu meeskonnakaaslased kirjutame piraatlusest, digitaalsest säilitamisest ja muust. Ühenda minuga <a %(reddit)s>Redditis</a>. Pange tähele, et see veebisait on lihtsalt blogi. Siin majutame ainult oma sõnu. Siin ei majutata ega lingita torrente ega muid autoriõigusega kaitstud faile. <strong>Raamatukogu</strong> - Nagu enamik raamatukogusid, keskendume peamiselt kirjalikele materjalidele nagu raamatud. Võime tulevikus laieneda ka teistele meediatüüpidele. <strong>Peegel</strong> - Oleme rangelt olemasolevate raamatukogude peegel. Keskendume säilitamisele, mitte raamatute hõlpsasti otsitavaks ja allalaaditavaks (juurdepääs) muutmisele või suure kogukonna loomisele, kes panustavad uusi raamatuid (allikad). <strong>Piraat</strong> - Me rikume teadlikult autoriõiguse seadusi enamikus riikides. See võimaldab meil teha midagi, mida juriidilised isikud ei saa teha: tagada, et raamatud on laialdaselt peegeldatud. <em>Me ei lingi sellelt blogilt failidele. Palun leidke need ise.</em> - Anna ja meeskond (<a %(reddit)s>Reddit</a>) See projekt (MUUDETUD: kolitud <a %(wikipedia_annas_archive)s>Anna Arhiiv</a>) eesmärk on aidata kaasa inimteadmiste säilitamisele ja vabastamisele. Teeme oma väikese ja tagasihoidliku panuse, järgides meie eelkäijate suuri tegusid. Projekti fookus on illustreeritud selle nimega: Esimene raamatukogu, mida oleme peegeldanud, on Z-Raamatukogu. See on populaarne (ja ebaseaduslik) raamatukogu. Nad on võtnud Library Genesis'i kollektsiooni ja teinud selle hõlpsasti otsitavaks. Lisaks on nad muutunud väga tõhusaks uute raamatute panuste hankimisel, motiveerides panustavaid kasutajaid erinevate hüvedega. Praegu ei anna nad neid uusi raamatuid tagasi Library Genesis'ile. Ja erinevalt Library Genesis'ist ei tee nad oma kollektsiooni hõlpsasti peegeldatavaks, mis takistab laialdast säilitamist. See on nende ärimudeli jaoks oluline, kuna nad võtavad tasu oma kollektsiooni hulgipääsu eest (rohkem kui 10 raamatut päevas). Me ei tee moraalseid hinnanguid raha küsimise kohta ebaseadusliku raamatukogu massilise juurdepääsu eest. Pole kahtlustki, et Z-Library on olnud edukas teadmiste kättesaadavuse laiendamisel ja rohkemate raamatute hankimisel. Meie oleme siin lihtsalt oma osa tegemas: tagame selle privaatse kogu pikaajalise säilimise. Kutsume teid üles aitama säilitada ja vabastada inimteadmisi, laadides alla ja jagades meie torrenteid. Vaadake projekti lehte, et saada rohkem teavet andmete korralduse kohta. Samuti kutsume teid väga üles panustama oma ideedega, milliseid kollektsioone järgmisena peegeldada ja kuidas seda teha. Koos suudame palju saavutada. See on vaid väike panus paljude teiste seas. Aitäh, kõige eest, mida teete. Tutvustame Piraatide Raamatukogu Peeglit: 7TB raamatute säilitamine (mis ei ole Libgenis) 10% o inimkonna kirjalikust pärandist igaveseks säilitatud <strong>Google.</strong> Lõppude lõpuks tegid nad seda uurimistööd Google Books jaoks. Kuid nende metadata pole massiliselt kättesaadav ja on üsna raske kraapida. <strong>Erinevad individuaalsed raamatukogusüsteemid ja arhiivid.</strong> On raamatukogusid ja arhiive, mida pole indekseeritud ja koondatud ühegi ülaltoodud poolt, sageli seetõttu, et nad on alarahastatud või muudel põhjustel ei soovi nad oma andmeid jagada Open Library, OCLC, Google'iga jne. Paljudel neist on digitaalsed kirjed, mis on interneti kaudu kättesaadavad, ja need pole sageli väga hästi kaitstud, seega kui soovite aidata ja õppida lõbusalt tundma kummalisi raamatukogusüsteeme, on need suurepärased alguspunktid. <strong>ISBNdb.</strong> See on selle blogipostituse teema. ISBNdb kraabib erinevaid veebisaite raamatu metadata jaoks, eriti hinnateabe jaoks, mida nad seejärel müüvad raamatumüüjatele, et nad saaksid oma raamatuid hinnastada vastavalt ülejäänud turule. Kuna ISBN-id on tänapäeval üsna universaalsed, on nad tõhusalt loonud “veebilehe iga raamatu jaoks”. <strong>Open Library.</strong> Nagu varem mainitud, on see nende kogu missioon. Nad on hankinud tohutul hulgal raamatukogude andmeid koostööd tegevate raamatukogude ja rahvusarhiivide kaudu ning jätkavad seda. Neil on ka vabatahtlikud raamatukoguhoidjad ja tehniline meeskond, kes püüavad kirjeid deduplitseerida ja neid igasuguste metadata'ga märgistada. Parim osa on see, et nende andmekogum on täiesti avatud. Saate selle lihtsalt <a %(openlibrary)s>alla laadida</a>. <strong>WorldCat.</strong> See on veebisait, mida haldab mittetulundusühing OCLC, mis müüb raamatukogude haldussüsteeme. Nad koondavad raamatute metadata paljudest raamatukogudest ja teevad selle kättesaadavaks WorldCati veebisaidi kaudu. Kuid nad teenivad ka raha, müües neid andmeid, seega pole need massiliseks allalaadimiseks saadaval. Neil on siiski mõned piiratud massilised andmekogumid, mis on saadaval allalaadimiseks koostöös konkreetsete raamatukogudega. 1. Mõne mõistliku "igavesti" definitsiooni järgi. ;) 2. Loomulikult on inimkonna kirjalik pärand palju enamat kui raamatud, eriti tänapäeval. Selle postituse ja meie hiljutiste väljaannete huvides keskendume raamatutele, kuid meie huvid ulatuvad kaugemale. 3. Aaron Swartzist võib palju rohkem rääkida, kuid tahtsime teda lühidalt mainida, kuna ta mängib selles loos keskset rolli. Aja möödudes võivad rohkem inimesed tema nime esimest korda kohata ja seejärel ise süvitsi minna. <strong>Füüsilised koopiad.</strong> Ilmselgelt pole see väga abiks, kuna need on lihtsalt sama materjali duplikaadid. Oleks tore, kui saaksime säilitada kõik märkused, mida inimesed raamatutes teevad, nagu Fermat’ kuulsad “marginaalsed kritseldused”. Kuid kahjuks jääb see arhiivipidaja unistuseks. <strong>“Väljaanded”.</strong> Siin loetakse iga raamatu ainulaadne versioon. Kui midagi on erinev, nagu erinev kaas või erinev eessõna, loetakse see erinevaks väljaandeks. <strong>Failid.</strong> Kui töötate variraamatukogudega nagu Library Genesis, Sci-Hub või Z-Library, on veel üks kaalutlus. Sama väljaande kohta võib olla mitu skaneeringut. Ja inimesed saavad olemasolevatest failidest paremaid versioone luua, skaneerides teksti OCR-i abil või parandades lehti, mis olid skaneeritud nurga all. Tahame lugeda neid faile ainult üheks väljaandeks, mis nõuaks head metadata või deduplitseerimist dokumendi sarnasuse mõõtmete abil. <strong>“Teosed”.</strong> Näiteks “Harry Potter ja saladuste kamber” kui loogiline kontseptsioon, mis hõlmab kõiki selle versioone, nagu erinevad tõlked ja kordustrükid. See on omamoodi kasulik määratlus, kuid võib olla raske tõmmata piiri, mis loeb. Näiteks tahame tõenäoliselt säilitada erinevaid tõlkeid, kuigi kordustrükid, millel on ainult väikesed erinevused, ei pruugi olla nii olulised. - Anna ja meeskond (<a %(reddit)s>Reddit</a>) Piraatide Raamatukogu Peegli (MUUDETUD: kolitud <a %(wikipedia_annas_archive)s>Anna Arhiiv</a>) abil on meie eesmärk võtta kõik maailma raamatud ja säilitada need igaveseks.<sup>1</sup> Meie Z-Raamatukogu torrentide ja algsete Library Genesis'i torrentide vahel on meil 11 783 153 faili. Aga kui palju see tegelikult on? Kui me deduplitseeriksime need failid korralikult, siis kui suur protsent kõigist maailma raamatutest oleks säilitatud? Me tõesti tahaksime midagi sellist: Alustame mõningate umbkaudsete numbritega: Nii Z-Library/Libgenis kui ka Open Librarys on palju rohkem raamatuid kui unikaalseid ISBN-e. Kas see tähendab, et paljudel neist raamatutest pole ISBN-e või on ISBN metadata lihtsalt puudu? Tõenäoliselt saame sellele küsimusele vastata, kasutades automaatset sobitamist teiste atribuutide (pealkiri, autor, kirjastaja jne) põhjal, tuues sisse rohkem andmeallikaid ja ekstraheerides ISBN-e tegelikest raamatuskaneeringutest (Z-Library/Libgeni puhul). Kui paljud neist ISBN-idest on unikaalsed? Seda on kõige parem illustreerida Venn-diagrammiga: Täpsemalt: Meid üllatas, kui vähe kattuvust on! ISBNdb-l on tohutult palju ISBN-e, mis ei esine ei Z-Library's ega Open Library's, ja sama kehtib (väiksemas, kuid siiski märkimisväärses ulatuses) ka teiste kahe kohta. See tekitab palju uusi küsimusi. Kui palju aitaks automaatne sobitamine raamatute märgistamisel, mis ei olnud ISBN-idega märgistatud? Kas oleks palju vasteid ja seega suurenenud kattuvus? Ja mis juhtuks, kui lisame neljanda või viienda andmekogu? Kui palju kattuvust siis näeksime? See annab meile lähtepunkti. Nüüd saame vaadata kõiki ISBN-e, mis ei olnud Z-Library andmekogus ja mis ei vasta ka pealkirja/autoriväljadele. See võib anda meile võimaluse säilitada kõik maailma raamatud: esmalt internetist skaneeringute kogumise teel, seejärel päriselus raamatute skaneerimise kaudu. Viimane võiks isegi olla rahastatud ühisrahastuse kaudu või "preemiate" abil inimestelt, kes sooviksid näha teatud raamatute digiteerimist. See kõik on aga lugu teiseks korraks. Kui soovite aidata kaasa mõnele neist tegevustest — edasine analüüs; rohkemate metadata kogumine; rohkemate raamatute leidmine; raamatute OCR-iga töötlemine; selle tegemine teistes valdkondades (nt teadusartiklid, audioraamatud, filmid, telesaated, ajakirjad) või isegi osa andmete kättesaadavaks tegemine asjade jaoks nagu ML / suurte keelemudelite treenimine — võtke minuga ühendust (<a %(reddit)s>Reddit</a>). Kui olete eriti huvitatud andmeanalüüsist, töötame selle nimel, et muuta meie andmekogud ja skriptid kättesaadavaks lihtsamini kasutatavas vormingus. Oleks tore, kui saaksite lihtsalt kahvliga märkmiku ja hakata sellega mängima. Lõpuks, kui soovite seda tööd toetada, kaaluge annetuse tegemist. See on täielikult vabatahtlikel põhinev operatsioon ja teie panus teeb tohutu vahe. Iga natuke aitab. Praegu võtame annetusi vastu krüptos; vaadake Anna Arhiivi annetuste lehte. Protsendi jaoks vajame nimetajat: kõigi kunagi avaldatud raamatute koguarv.<sup>2</sup> Enne Google Books'i lõppu püüdis projekti insener Leonid Taycher <a %(booksearch_blogspot)s>hinnata</a> seda arvu. Ta jõudis — naljatades — 129 864 880-ni („vähemalt kuni pühapäevani”). Ta hindas seda arvu, luues kõigi maailma raamatute ühtse andmebaasi. Selleks tõi ta kokku erinevad andmekogumid ja ühendas need mitmel viisil. Muuseasjaks, on veel üks inimene, kes püüdis kataloogida kõiki maailma raamatuid: Aaron Swartz, hiline digitaalse aktivist ja Redditi kaasasutaja.<sup>3</sup> Ta <a %(youtube)s>alustas Open Library</a> eesmärgiga luua “üks veebileht iga kunagi avaldatud raamatu jaoks”, kombineerides andmeid paljudest erinevatest allikatest. Ta maksis oma digitaalse säilitustöö eest kõrgeimat hinda, kui teda süüdistati akadeemiliste artiklite massilises allalaadimises, mis viis tema enesetapuni. Pole vaja öelda, et see on üks põhjusi, miks meie grupp on pseudonüümne ja miks oleme väga ettevaatlikud. Open Libraryt juhivad endiselt kangelaslikult Internet Archive'i inimesed, jätkates Aaroni pärandit. Me naaseme selle teema juurde hiljem selles postituses. Google'i blogipostituses kirjeldab Taycher mõningaid väljakutseid selle numbri hindamisel. Esiteks, mis moodustab raamatu? On mõned võimalikud määratlused: “Väljaanded” tunduvad kõige praktilisem määratlus, mis “raamatud” on. Mugavalt kasutatakse seda määratlust ka unikaalsete ISBN-numbrite määramiseks. ISBN ehk rahvusvaheline standardraamatu number on rahvusvahelises kaubanduses laialdaselt kasutatav, kuna see on integreeritud rahvusvahelise vöötkoodisüsteemiga (”International Article Number”). Kui soovite müüa raamatut poodides, vajab see vöötkoodi, seega saate ISBN-i. Taycheri blogipostituses mainitakse, et kuigi ISBN-id on kasulikud, ei ole need universaalsed, kuna neid hakati tõeliselt kasutama alles seitsmekümnendate keskpaigas ja mitte kõikjal maailmas. Sellegipoolest on ISBN tõenäoliselt kõige laialdasemalt kasutatav raamatu väljaannete identifikaator, seega on see meie parim lähtepunkt. Kui suudame leida kõik maailma ISBN-id, saame kasuliku nimekirja raamatutest, mis vajavad veel säilitamist. Kust me siis andmeid saame? On mitmeid olemasolevaid jõupingutusi, mis püüavad koostada nimekirja kõigist maailma raamatutest: Selles postituses on meil hea meel teatada väikesest väljaandest (võrreldes meie varasemate Z-Library väljaannetega). Kraapisime suurema osa ISBNdb-st ja tegime andmed torrentimiseks kättesaadavaks Pirate Library Mirrori veebisaidil (MUUDETUD: kolisime <a %(wikipedia_annas_archive)s>Anna Arhiivi</a>; me ei lingi seda siin otse, lihtsalt otsige seda). Need on umbes 30,9 miljonit kirjet (20GB kui <a %(jsonlines)s>JSON Lines</a>; 4,4GB tihendatud). Nende veebisaidil väidetakse, et neil on tegelikult 32,6 miljonit kirjet, seega võisime kuidagi mõne vahele jätta või <em>nemad</em> võivad midagi valesti teha. Igal juhul ei jaga me praegu täpselt, kuidas me seda tegime — jätame selle lugejale harjutuseks. ;-) Mida me jagame, on mõningane esialgne analüüs, et proovida lähemale jõuda maailma raamatute arvu hindamisele. Vaatasime kolme andmekogumit: see uus ISBNdb andmekogum, meie algne metadata väljaanne, mille kraapisime Z-Library variraamatukogust (mis sisaldab Library Genesis't), ja Open Library andmete dump. ISBNdb dump, või Kui Palju Raamatuid On Igaveseks Säilitatud? Kui me deduplitseeriksime korralikult variraamatukogude failid, siis kui suur protsent kõigist maailma raamatutest oleks säilitatud? Uuendused <a %(wikipedia_annas_archive)s>Anna Arhiivist</a>, suurimast tõeliselt avatud raamatukogust inimkonna ajaloos. <em>WorldCati ümberkujundamine</em> Andmed <strong>Formaat?</strong> <a %(blog)s>Anna Arhiivi Konteinerid (AAC)</a>, mis on sisuliselt <a %(jsonlines)s>JSON Lines</a>, mis on tihendatud <a %(zstd)s>Zstandard</a>iga, pluss mõned standardiseeritud semantika. Need konteinerid hõlmavad erinevat tüüpi kirjeid, mis põhinevad erinevatel kraapimistel, mida me kasutasime. Aasta tagasi <a %(blog)s>asutasime</a> selle küsimuse vastamiseks: <strong>Milline protsent raamatutest on varjuraamatukogude poolt püsivalt säilitatud?</strong> Vaatame mõningaid põhiandmeid: Kui raamat jõuab avatud andmetega varjuraamatukokku nagu <a %(wikipedia_library_genesis)s>Library Genesis</a> ja nüüd <a %(wikipedia_annas_archive)s>Anna Arhiiv</a>, peegeldatakse seda kogu maailmas (torrente kaudu), säilitades selle seega praktiliselt igavesti. Et vastata küsimusele, milline protsent raamatutest on säilitatud, peame teadma nimetajat: kui palju raamatuid kokku eksisteerib? Ja ideaalis ei ole meil mitte ainult number, vaid ka tegelik metadata. Siis saame neid mitte ainult varjuraamatukogudega võrrelda, vaid ka <strong>luua säilitamist vajavate raamatute TODO nimekirja!</strong> Võiksime isegi unistada ühisrahastatud jõupingutusest, et see TODO nimekiri läbi käia. Me kraapisime <a %(wikipedia_isbndb_com)s>ISBNdb</a> ja laadisime alla <a %(openlibrary)s>Open Library andmestiku</a>, kuid tulemused ei olnud rahuldavad. Peamine probleem oli, et ISBNide kattuvus oli väike. Vaadake seda Venn-diagrammi <a %(blog)s>meie blogipostitusest</a>: Olime väga üllatunud, kui vähe kattuvust oli ISBNdb ja Open Library vahel, mis mõlemad sisaldavad andmeid erinevatest allikatest, nagu veebikraapid ja raamatukogude andmed. Kui nad mõlemad teeksid head tööd, leides enamik ISBN-e, siis nende ringid kattuksid märkimisväärselt või üks oleks teise alamhulk. See pani meid mõtlema, kui palju raamatuid jääb <em>täiesti nende ringide väliselt</em>? Me vajame suuremat andmebaasi. Siis seadsime oma sihid maailma suurimale raamatute andmebaasile: <a %(wikipedia_worldcat)s>WorldCat</a>. See on mittetulundusühingu <a %(wikipedia_oclc)s>OCLC</a> omanduses olev andmebaas, mis koondab raamatukogude metadata kirjeid üle kogu maailma, andes vastutasuks neile raamatukogudele juurdepääsu kogu andmestikule ja võimaluse ilmuda lõppkasutajate otsingutulemustes. Kuigi OCLC on mittetulundusühing, nõuab nende ärimudel oma andmebaasi kaitsmist. Noh, me peame ütlema, sõbrad OCLC-s, me anname selle kõik ära. :-) Viimase aasta jooksul oleme hoolikalt kraapinud kõik WorldCati kirjed. Alguses vedas meil. WorldCat oli just oma veebisaidi täielikku ümberkujundamist (augustis 2022) välja toomas. See hõlmas nende taustsüsteemide olulist uuendamist, mis tõi kaasa palju turvavigu. Kasutasime kohe võimalust ja suutsime kraapida sadu miljoneid (!) kirjeid vaid mõne päevaga. Pärast seda parandati turvavead ükshaaval, kuni viimane, mille leidsime, parandati umbes kuu aega tagasi. Selleks ajaks oli meil peaaegu kõik kirjed olemas ja me läksime ainult veidi kõrgema kvaliteediga kirjeid püüdma. Nii et tundsime, et on aeg avaldada! 1,3B WorldCati kraapimine <em><strong>TL;DR:</strong> Anna Arhiiv kraapis kogu WorldCati (maailma suurim raamatukogu metadata kogum), et koostada säilitamist vajavate raamatute TODO nimekiri.</em> WorldCat Hoiatus: see blogipostitus on aegunud. Oleme otsustanud, et IPFS ei ole veel valmis laialdaseks kasutamiseks. Me linkime endiselt faile IPFS-is Anna Arhiivist, kui võimalik, kuid me ei hosti neid enam ise ega soovita teistel kasutada IPFS-i peegeldamiseks. Kui soovite aidata meie kollektsiooni säilitada, vaadake meie Torrentsi lehte. Aidake seemendada Z-Raamatukogu IPFS-is Partneri serveri allalaadimine SciDB Väline laenutus Väline laenutus (printimine keelatud) Väline allalaadimine Uurige metaandmeid Sisaldub torrentides Tagasi  (+%(num)s boonus) maksmata makstud tühistatud aegunud ootab Anna kinnitust kehtetu Allolev tekst jätkub inglise keeles. Mine Lähtesta Edasi Viimane Kui teie e-posti aadress ei tööta Libgeni foorumites, soovitame kasutada <a %(a_mail)s>Proton Maili</a> (tasuta). Samuti saate <a %(a_manual)s>manuaalselt taotleda</a> oma konto aktiveerimist. (võib nõuda <a %(a_browser)s>brauseri kinnitust</a> — piiramatud allalaadimised!) Kiire partneriserver #%(number)s (soovitatav) (veidi kiirem, kuid ootenimekirjaga) (brauseri kinnitust pole vaja) (ei ole vaja brauseri kinnitust ega ootenimekirju) (ei ole ootenimekirja, kuid võib olla väga aeglane) Aeglane Partneriserver #%(number)s Audioraamat Koomiksiraamat Raamat (ilukirjandus) Raamat (teaduskirjandus) Raamat (tundmatu) Ajakirja artikkel Ajakiri Noodid Muu Standardite dokument Kõiki lehekülgi ei õnnestunud PDF-iks teisendada Libgen.lis katkiseks märgitud Libgen.lis nähtamatu Libgen.rs ilukirjanduses nähtamatu Libgen.rs mitteilukirjanduses nähtamatu Selle faili puhul ebaõnnestus exiftooli käivitamine Märgitud kui „halb fail” Z-Library's Puudub Z-Libraryst Märgitud kui „rämpspost” Z-Library's Faili ei saa avada (nt rikutud fail, DRM) Autoriõiguse nõue Allalaadimisprobleemid (nt ei saa ühendust, veateade, väga aeglane) Valed metaandmed (nt pealkiri, kirjeldus, kaanepilt) Muu Kehv kvaliteet (nt vormindusprobleemid, halb skaneerimiskvaliteet, puuduvad leheküljed) Rämpspost / fail tuleks eemaldada (nt reklaam, solvav sisu) %(amount)s (%(amount_usd)s) %(amount)s kokku %(amount)s (%(amount_usd)s) kokku Briljantne raamatutark Õnnelik raamatukoguhoidja Särav andmekoguja Imeline arhivaar Boonusallalaadimised Cerlalc Tšehhi metaandmed DuXiu 读秀 EBSCOhost e-raamatute indeks Google Books Goodreads HathiTrust IA IA kontrollitud digitaalne laenutus ISBNdb ISBN GRP Libgen.li Välja arvatud "scimag" Libgen.rs Ilukirjandus ja aimekirjandus Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Vene Riiklik Raamatukogu Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Üleslaadimised AA-sse Z-Library Z-Library Hiina Pealkiri, autor, DOI, ISBN, MD5, … Otsi Autor Kirjeldus ja metaandmete kommentaarid Väljaanne Algne failinimi Kirjastaja (otsi konkreetset välja) Pealkiri Avaldamisaasta Tehnilised üksikasjad Sellel mündil on tavapärasest kõrgem miinimum. Palun valige teine kestus või teine münt. Päringut ei õnnestunud täita. Palun proovige mõne minuti pärast uuesti ja kui probleem püsib, võtke meiega ühendust aadressil %(email)s ja lisage ekraanipilt. Tekkis tundmatu viga. Palun võtke meiega ühendust aadressil %(email)s ja lisage ekraanipilt. Makse töötlemisel tekkis viga. Palun oodake hetk ja proovige uuesti. Kui probleem püsib kauem kui 24 tundi, võtke meiega ühendust aadressil %(email)s ja lisage ekraanipilt. Korraldame rahakogumist, et <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">varundada</a> maailma suurim koomiksite varjuraamatukogu. Aitäh teie toetuse eest! <a href="/donate">Annetage.</a> Kui te ei saa annetada, kaaluge meie toetamist, rääkides sellest oma sõpradele ja jälgides meid <a href="https://www.reddit.com/r/Annas_Archive">Redditis</a> või <a href="https://t.me/annasarchiveorg">Telegramis</a>. Ärge saatke meile e-kirju <a %(a_request)s>raamatute taotlemiseks</a><br>või väikeste (<10k) <a %(a_upload)s>üleslaadimiste</a> jaoks. Anna arhiiv DMCA / autoriõiguse nõuded Püsi ühenduses Reddit Alternatiivid SLUM (%(unaffiliated)s) seotud olematu Anna arhiiv vajab teie abi! Kui annetate nüüd, saate <strong>kahekordse</strong> arvu kiireid allalaadimisi. Paljud püüavad meid maha võtta, kuid me võitleme vastu. Kui annetate sel kuul, saate <strong>kahekordse</strong> arvu kiireid allalaadimisi. Kehtib kuni selle kuu lõpuni. Inimkonna teadmiste säilitamine: suurepärane pühadekink! Liikmesusi pikendatakse vastavalt. Partnerite serverid ei ole saadaval hostingu sulgemiste tõttu. Need peaksid peagi taas töökorras olema. Anna arhiivi vastupidavuse suurendamiseks otsime vabatahtlikke, kes haldaksid peegleid. Meil on uus annetamisviis saadaval: %(method_name)s. Palun kaaluge %(donate_link_open_tag)sannetuse tegemist</a> — selle veebisaidi ülalpidamine ei ole odav ja teie annetus teeb tõesti vahet. Suur tänu. Soovita sõpra ja nii sina kui ka su sõber saavad %(percentage)s%% boonuskiireid allalaadimisi! Üllatage kallimat, kinkige talle konto liikmelisusega. Täiuslik sõbrapäeva kingitus! Loe lähemalt… Konto Tegevus Täpsem Anna ajaveeb ↗ Anna tarkvara ↗ beeta Koodide uurija Andmekogumid Annetage Allalaaditud failid KKK Avaleht Paranda metaandmeid LLM andmed Logi sisse / Registreeru Minu annetused Avalik profiil Otsi Turvalisus Torrendid Tõlgi ↗ Vabatahtlik töö ja preemiad Viimased allalaadimised: 📚&nbsp;Maailma suurim avatud lähtekoodiga ja avatud andmetega raamatukogu. ⭐️&nbsp;Peegeldab Sci-Hubi, Library Genesist, Z-Libraryt ja palju muud. 📈&nbsp;%(book_any)s raamatut, %(journal_article)s artiklit, %(book_comic)s koomiksit, %(magazine)s ajakirja — igavesti säilitatud.  ja  ja rohkem DuXiu Internet Archive'i laenuraamatukogu LibGen 📚&nbsp;Suurim tõeliselt avatud raamatukogu inimajaloos. 📈&nbsp;%(book_count)s&nbsp;raamatut, %(paper_count)s&nbsp;artiklit — igavesti säilitatud. ⭐️&nbsp;Me peegeldame %(libraries)s. Me kogume ja avame %(scraped)s. Kogu meie kood ja andmed on täielikult avatud lähtekoodiga. OpenLib Sci-Hub ,  📚 Maailma suurim avatud lähtekoodiga ja avatud andmetega raamatukogu.<br>⭐️ Peegeldab Scihubi, Libgeni, Zlibi ja palju muud. Z-Lib Anna Arhiiv Vigane päring. Külastage %(websites)s. Maailma suurim avatud lähtekoodiga avatud andmete raamatukogu. Peegeldab Sci-Hubi, Library Genesist, Z-Libraryt ja palju muud. Otsi Anna arhiivist Anna arhiiv Palun värskendage, et uuesti proovida. <a %(a_contact)s>Võtke meiega ühendust</a>, kui probleem püsib mitu tundi. 🔥 Probleem selle lehe laadimisel <li>1. Jälgige meid <a href="https://www.reddit.com/r/Annas_Archive/">Redditis</a> või <a href="https://t.me/annasarchiveorg">Telegramis</a>.</li><li>2. Levitage sõna Anna Arhiivi kohta Twitteris, Redditis, TikTokis, Instagramis, oma kohalikus kohvikus või raamatukogus või kus iganes te käite! Me ei usu väravavalvesse — kui meid maha võetakse, ilmume lihtsalt mujal uuesti, kuna kogu meie kood ja andmed on täielikult avatud lähtekoodiga.</li><li>3. Kui saate, kaaluge <a href="/donate">annetuse tegemist</a>.</li><li>4. Aidake <a href="https://translate.annas-software.org/">tõlkida</a> meie veebisaiti erinevatesse keeltesse.</li><li>5. Kui olete tarkvarainsener, kaaluge panustamist meie <a href="https://annas-software.org/">avatud lähtekoodiga</a> projektidesse või meie <a href="/datasets">torrente</a> jagamist.</li> 10. Looge või aidake hooldada Anna Arhiivi Wikipedia lehte oma keeles. 11. Otsime väikeseid, maitsekalt paigutatud reklaame. Kui soovite reklaamida Anna Arhiivis, andke meile teada. 6. Kui olete turvateadlane, saame kasutada teie oskusi nii rünnakuks kui ka kaitseks. Vaadake meie <a %(a_security)s>Turvalisuse</a> lehte. 7. Otsime eksperte anonüümsete kaupmeeste maksete alal. Kas saate aidata meil lisada mugavamaid viise annetamiseks? PayPal, WeChat, kinkekaardid. Kui tunnete kedagi, palun võtke meiega ühendust. 8. Otsime alati rohkem serveriressursse. 9. Saate aidata, teatades failiprobleemidest, jättes kommentaare ja luues nimekirju otse sellel veebisaidil. Samuti saate aidata <a %(a_upload)s>üles laadida rohkem raamatuid</a> või parandada olemasolevate raamatute failiprobleeme või vormindust. Põhjalikuma teabe saamiseks vabatahtlikuks olemise kohta vaadake meie <a %(a_volunteering)s>Vabatahtlikud ja preemiad</a> lehte. Usume kindlalt teabe vaba liikumisse ning teadmiste ja kultuuri säilitamisse. Selle otsingumootoriga ehitame hiiglaste õlgadele. Austame sügavalt nende inimeste rasket tööd, kes on loonud erinevaid varjatud raamatukogusid, ja loodame, et see otsingumootor laiendab nende ulatust. Et olla kursis meie edusammudega, jälgige Annat <a href="https://www.reddit.com/r/Annas_Archive/">Redditis</a> või <a href="https://t.me/annasarchiveorg">Telegramis</a>. Küsimuste ja tagasiside saamiseks võtke ühendust Annaga aadressil %(email)s. Konto ID: %(account_id)s Logi välja ❌ Midagi läks valesti. Palun laadi leht uuesti ja proovi uuesti. ✅ Oled nüüd välja logitud. Laadi leht uuesti, et uuesti sisse logida. Kiirlaadimisi kasutatud (viimase 24 tunni jooksul): <strong>%(used)s / %(total)s</strong> Liikmesus: <strong>%(tier_name)s</strong> kuni %(until_date)s <a %(a_extend)s>(pikenda)</a> Sa saad kombineerida mitu liikmesust (kiirlaadimised 24 tunni jooksul liidetakse kokku). Liikmesus: <strong>Puudub</strong> <a %(a_become)s>(hakka liikmeks)</a> Võta ühendust Annaga aadressil %(email)s, kui oled huvitatud oma liikmesuse uuendamisest kõrgemale tasemele. Avalik profiil: %(profile_link)s Salajane võti (ärge jagage!): %(secret_key)s näita Liitu meiega siin! Uuenda <a %(a_tier)s>kõrgemale tasemele</a>, et liituda meie grupiga. Eksklusiivne Telegrami grupp: %(link)s Konto millised allalaadimised? Logi sisse Ärge kaotage oma võtit! Vigane salajane võti. Kontrollige oma võtit ja proovige uuesti või registreerige allpool uus konto. Salajane võti Sisestage oma salajane võti, et sisse logida: Vana e-posti põhine konto? Sisestage oma <a %(a_open)s>e-post siia</a>. Registreeri uus konto Kas teil pole veel kontot? Registreerimine õnnestus! Sinu salajane võti on: <span %(span_key)s>%(key)s</span> Salvesta see võti hoolikalt. Kui sa selle kaotad, kaotad juurdepääsu oma kontole. <li %(li_item)s><strong>Järjehoidja.</strong> Sa saad selle lehe järjehoidjatesse lisada, et oma võtit taastada.</li><li %(li_item)s><strong>Laadi alla.</strong> Klõpsa <a %(a_download)s>sellel lingil</a>, et oma võti alla laadida.</li><li %(li_item)s><strong>Paroolihaldur.</strong> Kasuta paroolihaldurit, et võti salvestada, kui sa selle allpool sisestad.</li> Logi sisse / Registreeru Brauseri kinnitamine Hoiatus: kood sisaldab valesid Unicode'i märke ja võib erinevates olukordades valesti käituda. Toorbinaari saab dekodeerida URL-i base64 esitusest. Kirjeldus Silt Prefiks URL konkreetse koodi jaoks Veebisait Koodid, mis algavad “%(prefix_label)s” Palun ärge kraapige neid lehti. Selle asemel soovitame <a %(a_import)s>genereerida</a> või <a %(a_download)s>alla laadida</a> meie ElasticSearchi ja MariaDB andmebaasid ning käivitada meie <a %(a_software)s>avatud lähtekoodiga tarkvara</a>. Toorandmeid saab käsitsi uurida JSON-failide kaudu, näiteks <a %(a_json_file)s>selle</a> kaudu. Vähem kui %(count)s kirjet Üldine URL Koodide Uurija Indeks Uurige koode, millega kirjed on märgistatud, prefiksi järgi. Veerus „kirjed” kuvatakse, kui palju kirjeid on antud prefiksiga koodidega märgistatud, nagu otsingumootoris näha (sealhulgas ainult metaandmetega kirjed). Veerus „koodid” kuvatakse, kui palju tegelikke koode on antud prefiksiga. Tuntud koodiprefiks „%(key)s” Rohkem… Prefiks %(count)s kirje vastab otsingule “%(prefix_label)s” %(count)s kirjet vastavad otsingule “%(prefix_label)s” koodid kirjed „%%s” asendatakse koodi väärtusega Otsi Anna Arhiivist Koodid URL konkreetse koodi jaoks: “%(url)s” Selle lehe genereerimine võib võtta aega, mistõttu on vajalik Cloudflare captcha. <a %(a_donate)s>Liikmed</a> saavad captcha vahele jätta. Väärkasutusest teatatud: Parem versioon Kas soovite teatada sellest kasutajast sobimatu või väärkäitumise tõttu? Faili probleem: %(file_issue)s peidetud kommentaar Vasta Teata väärkasutusest Teatasite sellest kasutajast väärkasutuse tõttu. Autoriõiguste nõudeid sellele e-postile ignoreeritakse; kasutage selle asemel vormi. Näita e-posti Ootame väga teie tagasisidet ja küsimusi! Kuid rämpsposti ja mõttetute e-kirjade hulga tõttu palun kontrollige kastikesi, et kinnitada, et mõistate neid tingimusi meiega ühenduse võtmiseks. Kõik muud viisid meiega autoriõiguste nõuete osas ühenduse võtmiseks kustutatakse automaatselt. DMCA / autoriõiguste nõuete korral kasutage <a %(a_copyright)s>seda vormi</a>. Kontakt e-post URL-id Anna arhiivis (kohustuslik). Üks rea kohta. Palun lisage ainult URL-id, mis kirjeldavad täpselt sama raamatu väljaannet. Kui soovite esitada nõude mitme raamatu või mitme väljaande kohta, esitage see vorm mitu korda. Nõuded, mis koondavad mitu raamatut või väljaannet, lükatakse tagasi. Aadress (kohustuslik) Allikmaterjali selge kirjeldus (kohustuslik) E-post (kohustuslik) Allikmaterjali URL-id, üks rea kohta (kohustuslik). Palun lisage nii palju kui võimalik, et aidata meil teie nõuet kinnitada (nt Amazon, WorldCat, Google Books, DOI). Allikmaterjali ISBN-id (kui on kohaldatav). Üks rea kohta. Palun lisage ainult need, mis täpselt vastavad väljaandele, mille kohta te autoriõiguse nõuet esitate. Teie nimi (kohustuslik) ❌ Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. ✅ Täname, et esitasite oma autoriõiguse nõude. Vaatame selle läbi esimesel võimalusel. Palun laadige leht uuesti, et esitada veel üks nõue. <a %(a_openlib)s>Open Library</a> allikmaterjali URL-id, üks rea kohta. Palun võtke hetk, et otsida Open Library'st oma allikmaterjali. See aitab meil teie nõuet kinnitada. Telefoninumber (kohustuslik) Avaldus ja allkiri (kohustuslik) Esita nõue Kui teil on DCMA või muu autoriõiguse nõue, täitke palun see vorm võimalikult täpselt. Kui teil tekib probleeme, võtke meiega ühendust meie spetsiaalsel DMCA aadressil: %(email)s. Pange tähele, et sellele aadressile saadetud nõudeid ei menetleta, see on mõeldud ainult küsimuste jaoks. Palun kasutage oma nõuete esitamiseks allolevat vormi. DMCA / Autoriõiguse nõude vorm Näidis kirje Anna Arhiivis Anna Arhiivi torrentid Anna Arhiivi Konteinerite formaat Skriptid metaandmete importimiseks Kui olete huvitatud selle andmekogu peegeldamisest <a %(a_archival)s>arhiivimise</a> või <a %(a_llm)s>LLM-i koolituse</a> eesmärgil, võtke meiega ühendust. Viimati uuendatud: %(date)s Peamine %(source)s veebisait Metaandmete dokumentatsioon (enamik välju) Anna Arhiivi peegeldatud failid: %(count)s (%(percent)s%%) Ressursid Failide koguarv: %(count)s Failide kogumaht: %(size)s Meie blogipostitus selle andmete kohta <a %(duxiu_link)s>Duxiu</a> on tohutu skaneeritud raamatute andmebaas, mille on loonud <a %(superstar_link)s>SuperStar Digital Library Group</a>. Enamik neist on akadeemilised raamatud, mis on skaneeritud, et muuta need ülikoolidele ja raamatukogudele digitaalselt kättesaadavaks. Meie ingliskeelsele publikule on <a %(princeton_link)s>Princeton</a> ja <a %(uw_link)s>Washingtoni Ülikool</a> head ülevaated. Samuti on suurepärane artikkel, mis annab rohkem tausta: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Duxiu raamatuid on Hiina internetis pikka aega piraatitud. Tavaliselt müüvad edasimüüjad neid vähem kui dollari eest. Neid levitatakse tavaliselt Hiina Google Drive'i ekvivalendi kaudu, mida on sageli häkitud, et võimaldada rohkem salvestusruumi. Mõned tehnilised üksikasjad leiate <a %(link1)s>siit</a> ja <a %(link2)s>siit</a>. Kuigi raamatuid on poolavalikult levitatud, on neid üsna raske hulgi hankida. See oli meie TODO-nimekirjas kõrgel kohal ja eraldasime sellele mitu kuud täiskohaga tööd. Kuid 2023. aasta lõpus võttis meiega ühendust uskumatu, hämmastav ja andekas vabatahtlik, kes teatas, et on kogu selle töö juba ära teinud — suure kuluga. Nad jagasid kogu kollektsiooni meiega, ootamata midagi vastutasuks, välja arvatud pikaajalise säilitamise garantii. Tõeliselt märkimisväärne. Lisateave meie vabatahtlikelt (toored märkmed): Kohandatud meie <a %(a_href)s>blogipostitusest</a>. DuXiu 读秀 %(count)s fail %(count)s failid See andmekogu on tihedalt seotud <a %(a_datasets_openlib)s>Open Library andmekoguga</a>. See sisaldab kogu metaandmete kraapimist ja suurt osa IA kontrollitud digitaalse laenutamise raamatukogu failidest. Uuendused avaldatakse <a %(a_aac)s>Anna arhiivi konteinerite formaadis</a>. Neid kirjeid viidatakse otse Open Library andmekogust, kuid need sisaldavad ka kirjeid, mida Open Library's ei ole. Meil on ka mitmeid andmefaile, mida kogukonna liikmed on aastate jooksul kraapinud. Kogumik koosneb kahest osast. Kõigi andmete saamiseks vajate mõlemat osa (välja arvatud asendatud torrendid, mis on torrentide lehel maha tõmmatud). Digitaalne Laenuraamatukogu meie esimene väljaanne, enne kui standardiseerisime <a %(a_aac)s>Anna Arhiivi Konteinerite (AAC) formaadi</a>. Sisaldab metaandmeid (json ja xml), pdf-faile (acsm ja lcpdf digitaalsetest laenutussüsteemidest) ja kaanepilte. inkrementaalsed uued väljaanded, kasutades AAC-d. Sisaldab ainult metaandmeid, mille ajatemplid on pärast 2023-01-01, kuna ülejäänu on juba kaetud "ia" poolt. Samuti kõik pdf-failid, seekord acsm ja "bookreader" (IA veebilugeja) laenutussüsteemidest. Kuigi nimi ei ole täpselt õige, lisame siiski bookreader-failid ia2_acsmpdf_files kollektsiooni, kuna need on vastastikku välistavad. IA kontrollitud digitaalne laenutus 98%%+ failidest on otsitavad. Meie missioon on arhiveerida kõik maailma raamatud (samuti artiklid, ajakirjad jne) ja muuta need laialdaselt kättesaadavaks. Usume, et kõik raamatud tuleks laialdaselt peegeldada, et tagada nende redundantsus ja vastupidavus. Seetõttu koondame faile erinevatest allikatest. Mõned allikad on täiesti avatud ja neid saab hulgi peegeldada (näiteks Sci-Hub). Teised on suletud ja kaitsvad, seega püüame neid kraapida, et nende raamatud „vabastada”. Teised jäävad kuhugi vahepeale. Kõiki meie andmeid saab <a %(a_torrents)s>torrentina</a> alla laadida ja kõiki meie metaandmeid saab <a %(a_anna_software)s>genereerida</a> või <a %(a_elasticsearch)s>alla laadida</a> ElasticSearchi ja MariaDB andmebaasidena. Toorandmeid saab käsitsi uurida JSON-failide kaudu, näiteks <a %(a_dbrecord)s>see</a>. Metaandmed ISBN veebisait Viimati uuendatud: %(isbn_country_date)s (%(link)s) Ressursid Rahvusvaheline ISBN Agentuur avaldab regulaarselt vahemikke, mille ta on eraldanud riiklikele ISBN agentuuridele. Sellest saame tuletada, millisesse riiki, piirkonda või keelerühma see ISBN kuulub. Praegu kasutame neid andmeid kaudselt, läbi <a %(a_isbnlib)s>isbnlib</a> Python'i teegi. ISBN riigi teave See on suur hulk kõnesid isbndb.com-ile 2022. aasta septembris. Püüdsime katta kõik ISBN vahemikud. Neid on umbes 30,9 miljonit kirjet. Nende veebisaidil väidetakse, et neil on tegelikult 32,6 miljonit kirjet, seega võisime kuidagi mõnest ilma jääda või <em>nad</em> võivad midagi valesti teha. JSON vastused on peaaegu toored nende serverist. Üks andmekvaliteedi probleem, mida märkasime, on see, et ISBN-13 numbrite puhul, mis algavad teise prefiksiga kui “978-”, sisaldavad nad siiski “isbn” välja, mis on lihtsalt ISBN-13 number, mille esimesed 3 numbrit on ära lõigatud (ja kontrollnumber ümber arvutatud). See on ilmselgelt vale, kuid nad näivad seda nii tegevat, seega me ei muutnud seda. Teine potentsiaalne probleem, millega võite kokku puutuda, on see, et “isbn13” väli on dubleeritud, seega ei saa seda kasutada andmebaasis esmase võtmena. “isbn13”+“isbn” väljad koos tunduvad olevat unikaalsed. Väljalase 1 (2022-10-31) Ilukirjanduse torrendid on maha jäänud (kuigi ID-d ~4-6M pole torrentitud, kuna need kattuvad meie Zlib torrentitega). Meie blogipostitus koomiksiraamatute väljaandmise kohta Koomiksite torrendid Anna arhiivis Erinevate Library Genesis harude tausta kohta vaadake lehte <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li sisaldab enamikku samast sisust ja metaandmetest nagu Libgen.rs, kuid sellel on mõned lisakogud, nimelt koomiksid, ajakirjad ja standarddokumendid. Samuti on see integreerinud <a %(a_scihub)s>Sci-Hub</a> oma metaandmetesse ja otsingumootorisse, mida me kasutame oma andmebaasi jaoks. Selle raamatukogu metaandmed on vabalt saadaval <a %(a_libgen_li)s>libgen.li lehel</a>. Kuid see server on aeglane ja ei toeta katkestatud ühenduste jätkamist. Samad failid on saadaval ka <a %(a_ftp)s>ühel FTP serveril</a>, mis töötab paremini. Näib, et ka aimekirjandus on eraldunud, kuid ilma uute torrentiteta. Tundub, et see on juhtunud alates 2022. aasta algusest, kuigi me pole seda kinnitanud. Libgen.li administraatori sõnul peaks „fiction_rus” (vene ilukirjandus) kollektsioon olema kaetud regulaarselt välja antud torrentitega <a %(a_booktracker)s>booktracker.org</a>-ist, eriti <a %(a_flibusta)s>flibusta</a> ja <a %(a_librusec)s>lib.rus.ec</a> torrentitega (mida me peegeldame <a %(a_torrents)s>siin</a>, kuigi me pole veel kindlaks teinud, millised torrendid vastavad millistele failidele). Ilukirjanduse kollektsioonil on oma torrendid (erinevad <a %(a_href)s>Libgen.rs-ist</a>), alustades %(start)s. Teatud vahemikud ilma torrentideta (näiteks ilukirjanduse vahemikud f_3463000 kuni f_4260000) on tõenäoliselt Z-Raamatukogu (või muud duplikaat) failid, kuigi me võiksime teha mõningast deduplikatsiooni ja luua torrentid lgli-ainulaadsete failide jaoks nendes vahemikes. Kõigi kogude statistika leiate <a %(a_href)s>libgeni veebisaidilt</a>. Torrendid on saadaval enamiku lisasisu jaoks, eriti koomiksite, ajakirjade ja standarddokumentide torrendid on välja antud koostöös Anna Arhiiviga. Pange tähele, et torrentifailid, mis viitavad „libgen.is“-le, on selgesõnaliselt <a %(a_libgen)s>Libgen.rs</a> peeglid („.is“ on erinev domeen, mida kasutab Libgen.rs). Kasulik ressurss metaandmete kasutamisel on <a %(a_href)s>see leht</a>. %(icon)s Nende „fiction_rus” kollektsioonil (vene ilukirjandus) ei ole pühendatud torrente, kuid see on kaetud teiste torrentitega ja me hoiame <a %(fiction_rus)s>peeglit</a>. Vene ilukirjanduse torrendid Anna Arhiivis Ilukirjanduse torrendid Anna arhiivis Arutelufoorum Metaandmed Metaandmed FTP kaudu Ajakirjade torrendid Anna arhiivis Metaandmeväljade teave Muude torrentite peegel (ja ainulaadsed ilukirjanduse ja koomiksite torrendid) Standarddokumentide torrendid Anna Arhiivis Libgen.li Torrendid Anna arhiivis (raamatukaaned) Library Genesis on tuntud oma andmete helde jagamise poolest torrentide kaudu. Meie Libgeni kollektsioon koosneb lisandandmetest, mida nad otse ei avalda, koostöös nendega. Suur tänu kõigile, kes Library Genesis'ega koostööd teevad! Meie blogi raamatukaante väljaande kohta See leht käsitleb „.rs“ versiooni. See on tuntud oma metaandmete ja kogu oma raamatukataloogi sisu järjekindla avaldamise poolest. Selle raamatukogu on jagatud ilukirjanduse ja teaduskirjanduse osaks. Kasulik ressurss metaandmete kasutamisel on <a %(a_metadata)s>see leht</a> (blokeerib IP-vahemikke, võib olla vajalik VPN). Alates 2024-03 postitatakse uusi torrenteid <a %(a_href)s>selles foorumiteemas</a> (blokeerib IP-vahemikke, võib olla vajalik VPN). Ilukirjanduse torrendid Anna arhiivis Libgen.rs ilukirjanduse torrendid Libgen.rs arutelufoorum Libgen.rs Metaandmed Libgen.rs metaandmete väljade teave Libgen.rs teabekirjanduse torrendid Teabekirjanduse torrendid Anna arhiivis %(example)s ilukirjanduse raamatu jaoks. See <a %(blog_post)s>esimene väljaanne</a> on üsna väike: umbes 300GB raamatukaasi Libgen.rs harust, nii ilukirjanduse kui ka teabekirjanduse jaoks. Need on organiseeritud samamoodi nagu need ilmuvad libgen.rs-is, nt: %(example)s teabekirjanduse raamatu jaoks. Nii nagu Z-Library kollektsiooniga, panime need kõik suurde .tar faili, mida saab monteerida kasutades <a %(a_ratarmount)s>ratarmount</a>, kui soovite faile otse teenindada. Väljaanne 1 (%(date)s) Lühike lugu erinevatest Library Genesis (või „Libgen“) harudest on see, et aja jooksul läksid erinevad Library Genesis'ega seotud inimesed tülli ja läksid oma teed. Selle <a %(a_mhut)s>foorumipostituse</a> kohaselt hostiti Libgen.li algselt aadressil „http://free-books.dontexist.com”. „.fun“ versiooni lõi algne asutaja. Seda uuendatakse uue, rohkem hajutatud versiooni kasuks. <a %(a_li)s>„.li“ versioonil</a> on tohutu koomiksite kogu, samuti muud sisu, mis pole (veel) saadaval massiliseks allalaadimiseks torrentite kaudu. Sellel on eraldi ilukirjanduse raamatute torrentikogu ja see sisaldab oma andmebaasis <a %(a_scihub)s>Sci-Hub</a> metaandmeid. „.rs“ versioonil on väga sarnased andmed ja see avaldab oma kollektsiooni kõige järjekindlamalt massiliste torrentitena. See on ligikaudu jagatud „ilukirjanduse“ ja „teaduskirjanduse“ osaks. Algselt aadressil „http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> on teatud mõttes ka Library Genesis'e haru, kuigi nad kasutasid oma projekti jaoks teistsugust nime. Libgen.rs Samuti rikastame oma kollektsiooni ainult metaandmetega allikatega, mida saame sobitada failidega, näiteks kasutades ISBN numbreid või muid välju. Allpool on nende ülevaade. Jällegi, mõned neist allikatest on täiesti avatud, samas kui teisi peame kraapima. Pange tähele, et metaandmete otsingus kuvame algseid kirjeid. Me ei tee kirjete ühendamist. Ainult metaandmetega allikad Open Library on Internet Archive'i avatud lähtekoodiga projekt, mille eesmärk on kataloogida kõik maailma raamatud. Sellel on üks maailma suurimaid raamatute skaneerimise operatsioone ja paljud raamatud on saadaval digitaalseks laenutamiseks. Selle raamatute metaandmete kataloog on vabalt allalaaditav ja on kaasatud Anna arhiivi (kuigi praegu mitte otsingus, välja arvatud juhul, kui otsite spetsiaalselt Open Library ID-d). Open Library Välja arvatud duplikaadid Viimati uuendatud Failide arvu protsendid %% peegeldatud AA / saadaval torrentid Suurus Allikas Allpool on kiire ülevaade failide allikatest Anna arhiivis. Kuna varjulised raamatukogud sünkroonivad sageli andmeid üksteiselt, on raamatukogude vahel märkimisväärne kattuvus. Seetõttu ei klapi numbrid kokku kogusummaga. “Anna arhiivi peegeldatud ja seemnetega varustatud” protsent näitab, kui palju faile me ise peegeldame. Me levitame neid faile hulgikorras torrentite kaudu ja teeme need kättesaadavaks otse allalaadimiseks partnerite veebisaitide kaudu. Ülevaade Kokku Torrendid Anna arhiivis Sci-Hubi tausta kohta leiate teavet selle <a %(a_scihub)s>ametlikult veebisaidilt</a>, <a %(a_wikipedia)s>Wikipedia lehelt</a> ja sellest <a %(a_radiolab)s>podcasti intervjuust</a>. Pange tähele, et Sci-Hub on olnud <a %(a_reddit)s>külmutatud alates 2021. aastast</a>. See oli varem külmutatud, kuid 2021. aastal lisati paar miljonit artiklit. Siiski lisatakse piiratud arv artikleid Libgeni “scimag” kogudesse, kuid mitte piisavalt, et õigustada uusi massilisi torrenteid. Kasutame Sci-Hubi metaandmeid, mida pakub <a %(a_libgen_li)s>Libgen.li</a> oma “scimag” kogus. Kasutame ka <a %(a_dois)s>dois-2022-02-12.7z</a> andmekogumit. Pange tähele, et “smarch” torrentid on <a %(a_smarch)s>aegunud</a> ja seetõttu ei ole meie torrentide nimekirjas. Torrendid Libgen.li-s Torrendid Libgen.rs-is Metaandmed ja torrendid Uuendused Redditis Podcasti intervjuu Wikipedia leht Sci-Hub Sci-Hub: külmutatud alates 2021; enamik saadaval torrentite kaudu Libgen.li: väikesed täiendused sellest ajast alates</div> Mõned allikakogud edendavad oma andmete massilist jagamist torrentite kaudu, samas kui teised ei jaga oma kollektsiooni kergesti. Viimasel juhul püüab Anna arhiiv nende kollektsioone kraapida ja kättesaadavaks teha (vt meie <a %(a_torrents)s>Torrentite</a> lehte). On ka vahepealseid olukordi, näiteks kui allikakogud on valmis jagama, kuid neil pole selleks ressursse. Nendel juhtudel püüame samuti aidata. Allpool on ülevaade, kuidas me suhtleme erinevate allikakogudega. Allikate raamatukogud %(icon)s Erinevad failide andmebaasid on hajutatud Hiina internetis; kuigi sageli tasulised andmebaasid %(icon)s Enamik faile on ligipääsetavad ainult premium BaiduYun kontode abil; aeglased allalaadimiskiirused. %(icon)s Anna arhiiv haldab <a %(duxiu)s>DuXiu failide</a> kogu %(icon)s Erinevad metaandmebaasid on hajutatud Hiina internetis; kuigi sageli tasulised andmebaasid %(icon)s Kogu nende kogu jaoks pole hõlpsasti ligipääsetavaid metaandmete väljavõtteid saadaval. %(icon)s Anna arhiiv haldab <a %(duxiu)s>DuXiu metaandmete</a> kogu Failid %(icon)s Faile saab laenutada ainult piiratud alusel, erinevate juurdepääsupiirangutega %(icon)s Anna arhiiv haldab <a %(ia)s>IA failide</a> kogu %(icon)s Mõned metaandmed saadaval <a %(openlib)s>Open Library andmebaasi väljavõtete</a> kaudu, kuid need ei kata kogu IA kogumit %(icon)s Kogu nende kogu jaoks pole hõlpsasti ligipääsetavaid metaandmete väljavõtteid saadaval %(icon)s Anna arhiiv haldab <a %(ia)s>IA metaandmete</a> kogu Viimati uuendatud %(icon)s Anna Arhiiv ja Libgen.li haldavad ühiselt kogusid <a %(comics)s>koomiksitest</a>, <a %(magazines)s>ajakirjadest</a>, <a %(standarts)s>standarddokumentidest</a> ja <a %(fiction)s>ilukirjandusest (eraldatud Libgen.rs-ist)</a>. %(icon)s Teatmekirjanduse torrendid jagatakse Libgen.rs-iga (ja peegeldatakse <a %(libgenli)s>siin</a>). %(icon)s Kvartali <a %(dbdumps)s>HTTP andmebaasi väljavõtted</a> %(icon)s Automaatsed torrendid <a %(nonfiction)s>teatmekirjanduse</a> ja <a %(fiction)s>ilukirjanduse</a> jaoks %(icon)s Anna arhiiv haldab <a %(covers)s>raamatukaante torrentite</a> kogu %(icon)s Igapäevased <a %(dbdumps)s>HTTP andmebaasi väljavõtted</a> Metaandmed %(icon)s Igakuised <a %(dbdumps)s>andmebaasi väljavõtted</a> %(icon)s Andmetorrentid saadaval <a %(scihub1)s>siin</a>, <a %(scihub2)s>siin</a> ja <a %(libgenli)s>siin</a> %(icon)s Mõned uued failid <a %(libgenrs)s>lisatakse</a> Libgeni "scimag"-i, kuid mitte piisavalt, et õigustada uusi torrente %(icon)s Sci-Hub on alates 2021. aastast uute failide lisamise peatanud. %(icon)s Metaandmete väljavõtted saadaval <a %(scihub1)s>siin</a> ja <a %(scihub2)s>siin</a>, samuti osana <a %(libgenli)s>Libgen.li andmebaasist</a> (mida me kasutame) Allikas %(icon)s Erinevad väiksemad või ühekordsed allikad. Soovitame inimestel esmalt üles laadida teistesse variraamatukogudesse, kuid mõnikord on inimestel kogusid, mis on teistele liiga suured sorteerimiseks, kuid mitte piisavalt suured, et väärida oma kategooriat. %(icon)s Pole otse hulgikogus saadaval, kaitstud kraapimise eest %(icon)s Anna arhiiv haldab <a %(worldcat)s>OCLC (WorldCat) metaandmete</a> kogu %(icon)s Anna arhiiv ja Z-Library haldavad koostöös <a %(metadata)s>Z-Library metaandmete</a> ja <a %(files)s>Z-Library failide</a> kogumikku Datasets Kombineerime kõik ülaltoodud allikad ühte ühtsesse andmebaasi, mida kasutame selle veebisaidi teenindamiseks. See ühtne andmebaas ei ole otseselt kättesaadav, kuid kuna Anna arhiiv on täielikult avatud lähtekoodiga, saab seda üsna lihtsalt <a %(a_generated)s>genereerida</a> või <a %(a_downloaded)s>alla laadida</a> ElasticSearchi ja MariaDB andmebaasidena. Sellel lehel olevad skriptid laadivad automaatselt alla kõik vajalikud metaandmed ülalmainitud allikatest. Kui soovite meie andmeid enne nende skriptide kohalikku käivitamist uurida, võite vaadata meie JSON-faile, mis viitavad edasi teistele JSON-failidele. <a %(a_json)s>See fail</a> on hea alguspunkt. Ühtne andmebaas Torrendid Anna Arhiivist sirvi otsing Erinevad väiksemad või ühekordsed allikad. Soovitame inimestel esmalt üles laadida teistesse variraamatukogudesse, kuid mõnikord on inimestel kogusid, mis on teistele liiga suured sorteerimiseks, kuid mitte piisavalt suured, et väärida oma kategooriat. Ülevaade <a %(a1)s>andmekogude lehelt</a>. Pärit <a %(a_href)s>aaaaarg.fail</a> lehelt. Tundub olevat üsna täielik. Meie vabatahtlikult “cgiym”. Torrendist <a %(a_href)s><q>ACM Digital Library 2020</q></a>. On üsna suur kattuvus olemasolevate artiklikogudega, kuid väga vähe MD5 vasteid, seega otsustasime selle täielikult säilitada. <i>iRead eBooks</i> (häälduslikult <i>ai rit i-books</i>; airitibooks.com) kraapimine, vabatahtlik <i>j</i>. Vastab <i>airitibooks</i> metadatale <a %(a1)s><i>Muud metadata kraapimised</i></a>. Kollektsioonist <a %(a1)s><i>Bibliotheca Alexandrina</i></a>. Osaliselt algsest allikast, osaliselt the-eye.eu-st, osaliselt teistest peeglitest. Eraõiguslikust raamatute torrent-veebisaidilt, <a %(a_href)s>Bibliotik</a> (sageli nimetatud kui “Bib”), kust raamatud olid koondatud torrentitesse nime järgi (A.torrent, B.torrent) ja levitatud the-eye.eu kaudu. Meie vabatahtlikult “bpb9v”. Lisateabe saamiseks <a %(a_href)s>CADAL</a> kohta vaadake märkmeid meie <a %(a_duxiu)s>DuXiu andmestiku lehel</a>. Rohkem meie vabatahtlikult “bpb9v”, peamiselt DuXiu failid, samuti kaust “WenQu” ja “SuperStar_Journals” (SuperStar on DuXiu taga olev ettevõte). Meie vabatahtlikult “cgiym”, Hiina tekstid erinevatest allikatest (esindatud alamkataloogidena), sealhulgas <a %(a_href)s>China Machine Press</a> (suur Hiina kirjastus). Mitte-Hiina kogud (esindatud alamkataloogidena) meie vabatahtlikult “cgiym”. Hiina arhitektuuri raamatute kraapimine, vabatahtlik <i>cm</i>: <i>Ma sain selle, kasutades kirjastuse võrgu haavatavust, kuid see lünk on nüüdseks suletud</i>. Vastab <i>chinese_architecture</i> metadatale <a %(a1)s><i>Muud metadata kraapimised</i></a>. Raamatud akadeemilisest kirjastusest <a %(a_href)s>De Gruyter</a>, kogutud mõnest suurest torrentist. <a %(a_href)s>docer.pl</a> kraapimine, Poola failijagamise veebisait, mis keskendub raamatutele ja muudele kirjalikele teostele. Kraabitud 2023. aasta lõpus vabatahtliku “p” poolt. Meil pole algselt veebisaidilt head metaandmeid (isegi mitte faililaiendeid), kuid filtreerisime raamatulaadseid faile ja suutsime sageli metaandmeid failidest endist välja võtta. DuXiu epubid, otse DuXiu'st, kogutud vabatahtliku “w” poolt. Ainult hiljutised DuXiu raamatud on otse e-raamatute kaudu saadaval, seega peavad enamik neist olema hiljutised. Ülejäänud DuXiu failid vabatahtlikult “m”, mis ei olnud DuXiu patenteeritud PDG formaadis (peamine <a %(a_href)s>DuXiu andmekogum</a>). Kogutud paljudest algallikatest, kahjuks ilma nende allikate säilitamiseta failiteel. <span></span> <span></span> <span></span> Erootiliste raamatute kraapimine, vabatahtlik <i>do no harm</i>. Vastab <i>hentai</i> metadatale <a %(a1)s><i>Muud metadata kraapimised</i></a>. <span></span> <span></span> Kogumik, mis kraabiti Jaapani Manga kirjastajalt vabatahtliku “t” poolt. <a %(a_href)s>Valitud Longquani kohtuarhiivid</a>, esitatud vabatahtliku “c” poolt. <a %(a_href)s>magzdb.org</a> kraapimine, Library Genesis'i liitlane (see on lingitud libgen.rs kodulehel), kuid kes ei soovinud oma faile otse pakkuda. Saadud vabatahtliku "p" poolt 2023. aasta lõpus. <span></span> Erinevad väikesed üleslaadimised, liiga väikesed, et olla oma alakogu, kuid esindatud kataloogidena. E-raamatud AvaxHome'ist, Venemaa failijagamise veebisaidilt. Ajalehtede ja ajakirjade arhiiv. Vastab <i>newsarch_magz</i> metadatale <a %(a1)s><i>Muud metadata kraapimised</i></a>. <a %(a1)s>Filosoofia Dokumentatsiooni Keskuse</a> kraapimine. Vabatahtliku "o" kogu, kes kogus Poola raamatuid otse algupärastest väljaandekohtadest ("scene"). <a %(a_href)s>shuge.org</a> ühendatud kogud vabatahtlike "cgiym" ja "woz9ts" poolt. <span></span> <a %(a_href)s>“Trantori Keiserlik Raamatukogu”</a> (nimetatud väljamõeldud raamatukogu järgi), kraabitud 2022. aastal vabatahtliku “t” poolt. <span></span> <span></span> <span></span> Alam-alakogud (esindatud kataloogidena) vabatahtlikult "woz9ts": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (autor <a %(a_sikuquanshu)s>Dizhi(迪志)</a> Taiwanis), mebook (mebook.cc, 我的小书屋, minu väike raamatukamber — woz9ts: "See sait keskendub peamiselt kvaliteetsete e-raamatufailide jagamisele, millest mõned on omaniku enda poolt küljendatud. Omanik arreteeriti <a %(a_arrested)s>2019. aastal</a> ja keegi tegi kogutud failidest kogu, mida ta jagas."). Ülejäänud DuXiu failid vabatahtlikult "woz9ts", mis polnud DuXiu patenteeritud PDG formaadis (veel teisendamata PDF-iks). "Üleslaadimise" kogu on jagatud väiksemateks alakogudeks, mis on märgitud AACID-des ja torrentide nimedes. Kõik alakogud deduplikeeriti esmalt põhikogu vastu, kuigi metaandmete "upload_records" JSON-failid sisaldavad endiselt palju viiteid algfailidele. Enamikust alakogudest eemaldati ka mitte-raamatufailid ja neid ei ole tavaliselt "upload_records" JSON-is märgitud. Alakogud on: Märkused Alamkollektsioon Paljud alakogud koosnevad ise alam-alakogudest (nt erinevatest algallikatest), mis on esindatud kataloogidena "filepath" väljad. Üleslaadimised Anna arhiivi Meie blogipostitus nende andmete kohta <a %(a_worldcat)s>WorldCat</a> on mittetulundusühingu <a %(a_oclc)s>OCLC</a> omanduses olev andmebaas, mis koondab raamatukogude metaandmeid üle kogu maailma. Tõenäoliselt on see maailma suurim raamatukogude metaandmete kogum. 2023. aasta oktoobris <a %(a_scrape)s>väljastasime</a> põhjaliku OCLC (WorldCat) andmebaasi kraapimise, <a %(a_aac)s>Anna arhiivi konteinerite formaadis</a>. Oktoober 2023, esialgne väljalase: OCLC (WorldCat) Torrendid Anna arhiivis Näidis kirje Anna Arhiivis (algne kogumik) Näidis kirje Anna Arhiivis (“zlib3” kogumik) Torrendid Anna Arhiivist (metaandmed + sisu) Blogipostitus Väljaanne 1 kohta Blogipostitus Väljaanne 2 kohta 2022. aasta lõpus arreteeriti väidetavad Z-Library asutajad ja domeenid konfiskeeriti Ameerika Ühendriikide ametivõimude poolt. Sellest ajast alates on veebisait aeglaselt taas internetti jõudmas. Praegu ei ole teada, kes seda haldab. Uuendatud seisuga veebruar 2023. Z-Library'l on juured <a %(a_href)s>Library Genesis</a> kogukonnas ja algselt alustati nende andmetega. Sellest ajast alates on see märkimisväärselt professionaalsemaks muutunud ja omab palju kaasaegsemat liidest. Seetõttu suudavad nad saada palju rohkem annetusi, nii rahaliselt, et oma veebisaiti edasi arendada, kui ka uute raamatute annetusi. Nad on kogunud suure kogumiku lisaks Library Genesis'ele. Kogumik koosneb kolmest osast. Esimese kahe osa algsed kirjelduslehed on säilitatud allpool. Kõigi andmete saamiseks on vaja kõiki kolme osa (välja arvatud asendatud torrendid, mis on torrentide lehel maha tõmmatud). %(title)s: meie esimene väljaanne. See oli esimene väljaanne, mida tollal kutsuti “Piraadi Raamatukogu Peegliks” (“pilimi”). %(title)s: teine väljaanne, seekord kõik failid .tar failidesse pakitud. %(title)s: järkjärgulised uued väljaanded, kasutades <a %(a_href)s>Anna Arhiivi Konteinerite (AAC) formaati</a>, nüüd välja antud koostöös Z-Library meeskonnaga. Esialgne peegel saadi vaevaliselt 2021. ja 2022. aasta jooksul. Praegusel hetkel on see veidi vananenud: see kajastab kogumiku seisukorda juunis 2021. Me uuendame seda tulevikus. Praegu keskendume selle esimese väljaande avaldamisele. Kuna Library Genesis on juba avalike torrentidega säilitatud ja on Z-Librarysse kaasatud, tegime 2022. aasta juunis Library Genesise vastu põhilise dubleerimise eemaldamise. Selleks kasutasime MD5 räsi. Raamatukogus on tõenäoliselt palju rohkem dubleeritud sisu, näiteks sama raamatu mitmes failivormingus. Seda on raske täpselt tuvastada, seega me ei tee seda. Pärast dubleerimise eemaldamist on meil alles üle 2 miljoni faili, kokku veidi alla 7TB. Kogumik koosneb kahest osast: MySQL “.sql.gz” metaandmete dump ja 72 torrentfaili, igaüks umbes 50-100GB. Metaandmed sisaldavad Z-Library veebisaidi poolt teatatud andmeid (pealkiri, autor, kirjeldus, failitüüp), samuti tegelikku failisuurust ja md5sum, mida me täheldasime, kuna mõnikord need ei ühti. Tundub, et on olemas failide vahemikud, mille puhul Z-Libraryl endal on valed metaandmed. Võib-olla oleme mõnel üksikul juhul valesti faile alla laadinud, mida püüame tulevikus tuvastada ja parandada. Suured torrentfailid sisaldavad tegelikke raamatute andmeid, failinimeks on Z-Library ID. Faililaiendeid saab metaandmete dumpi abil taastada. Kogumik on segu mitte-ilukirjanduslikust ja ilukirjanduslikust sisust (mitte eraldatud nagu Library Genesis). Kvaliteet varieerub samuti suuresti. See esimene väljaanne on nüüd täielikult saadaval. Pange tähele, et torrent-failid on saadaval ainult meie Tor-peegli kaudu. Väljaanne 1 (%(date)s) See on üksik lisatorrent-fail. See ei sisalda uut teavet, kuid selles on mõned andmed, mille arvutamine võib võtta aega. See teeb selle mugavaks, kuna selle torrent'i allalaadimine on sageli kiirem kui selle nullist arvutamine. Eelkõige sisaldab see SQLite indekseid tar-failide jaoks, kasutamiseks koos <a %(a_href)s>ratarmount</a>. Väljaanne 2 lisa (%(date)s) Oleme saanud kõik raamatud, mis lisati Z-Library'sse meie viimase peegli ja augusti 2022 vahel. Oleme ka tagasi läinud ja kraapinud mõned raamatud, mis esimesel korral vahele jäid. Kokkuvõttes on see uus kogumik umbes 24TB. Jällegi on see kogumik deduplikeeritud Library Genesis'e vastu, kuna selle kogumiku jaoks on juba torrentid saadaval. Andmed on korraldatud sarnaselt esimese väljaandega. Seal on MySQL “.sql.gz” metaandmete dump, mis sisaldab ka kõiki esimese väljaande metaandmeid, asendades selle. Samuti lisasime mõned uued veerud: Mainisime seda eelmisel korral, kuid lihtsalt selgituseks: “failinimi” ja “md5” on faili tegelikud omadused, samas kui “failinimi_teatatud” ja “md5_teatatud” on need, mida me Z-Libraryst kraapisime. Mõnikord need kaks ei ühti, seega lisasime mõlemad. Selle väljaande jaoks muutsime sorteerimist “utf8mb4_unicode_ci” peale, mis peaks olema ühilduv vanemate MySQL versioonidega. Andmefailid on sarnased eelmise korraga, kuigi need on palju suuremad. Me lihtsalt ei viitsinud luua hulgaliselt väiksemaid torrent-faile. “pilimi-zlib2-0-14679999-extra.torrent” sisaldab kõiki faile, mis eelmisel väljaandel vahele jäid, samas kui teised torrentid on kõik uued ID vahemikud.  <strong>Uuendus %(date)s:</strong> Me tegime enamik meie torrente liiga suureks, põhjustades torrent-klientidel raskusi. Oleme need eemaldanud ja välja andnud uued torrentid. <strong>Uuendus %(date)s:</strong> Faile oli endiselt liiga palju, seega pakkisime need tar-failidesse ja andsime uuesti välja uued torrentid. %(key)s: kas see fail on juba Library Genesises, kas ilukirjanduse või mitte-ilukirjanduse kogumikus (vastavuses md5 järgi). %(key)s: millises torrentis see fail on. %(key)s: määratud, kui me ei suutnud raamatut alla laadida. Väljaanne 2 (%(date)s) Zlib väljaanded (algsete kirjelduslehtedega) Tor domeen Peamine veebisait Z-Library kraapimine Z-Library “Hiina” kollektsioon näib olevat sama, mis meie DuXiu kollektsioon, kuid erinevate MD5-dega. Vältimaks duplikaate, jätame need failid torrentitest välja, kuid näitame neid siiski meie otsinguindeksis. Metaandmed Teie saate %(percentage)s%% rohkem kiireid allalaadimisi, kuna Teid kutsus kasutaja %(profile_link)s. See kehtib kogu liikmelisuse perioodi jooksul. Annetage Liitu Valitud kuni %(percentage)s%% allahindlused Alipay toetab rahvusvahelisi krediit-/deebetkaarte. Lisateabe saamiseks vaadake <a %(a_alipay)s>seda juhendit</a>. Saatke meile Amazon.com kinkekaarte, kasutades oma krediit-/deebetkaarti. Saate osta krüptovaluutat, kasutades krediit-/deebetkaarte. WeChat (Weixin Pay) toetab rahvusvahelisi krediit-/deebetkaarte. WeChati rakenduses minge “Mina => Teenused => Rahakott => Lisa kaart”. Kui te seda ei näe, lubage see valik “Mina => Seaded => Üldine => Tööriistad => Weixin Pay => Luba”. (kasutage, kui saadate Ethereumi Coinbase'ist) kopeeritud! kopeerima (madalaim miinimumsumma) (hoiatus: kõrge miinimumsumma) -%(percentage)s%% 12 kuud 1 kuu 24 kuud 3 kuud 48 kuud 6 kuud 96 kuud Valige, kui kauaks soovite tellimust. <div %(div_monthly_cost)s></div><div %(div_after)s>pärast <span %(span_discount)s></span> allahindlusi</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 kuud 1 kuu 24 kuud 3 kuud 48 kuud 6 kuud 96 kuud %(monthly_cost)s / kuu võtke meiega ühendust Otsesed <strong>SFTP</strong> serverid Ettevõtte tasemel annetus või vahetus uute kollektsioonide vastu (nt uued skaneeringud, OCR’itud andmekogud). Eksperdi juurdepääs <strong>Piiramatu</strong> kiire juurdepääs <div %(div_question)s>Kas ma saan oma liikmesust uuendada või mitu liikmesust saada?</div> <div %(div_question)s>Kas ma saan teha annetuse ilma liikmeks astumata?</div> Muidugi. Me aktsepteerime mis tahes summas annetusi sellele Monero (XMR) aadressile: %(address)s. <div %(div_question)s>Mida tähendavad vahemikud kuus?</div> Saate vahemiku madalamale poolele, rakendades kõiki allahindlusi, näiteks valides pikema perioodi kui kuu. <div %(div_question)s>Kas liikmelisus uuendatakse automaatselt?</div> Liikmelisus <strong>ei</strong> uuene automaatselt. Saate liituda nii pikaks või lühikeseks ajaks, kui soovite. <div %(div_question)s>Millele te annetusi kulutate?</div> 100%% läheb maailma teadmiste ja kultuuri säilitamiseks ja kättesaadavaks tegemiseks. Praegu kulutame seda peamiselt serveritele, salvestusruumile ja ribalaiusele. Ükski raha ei lähe isiklikult ühelegi meeskonnaliikmele. <div %(div_question)s>Kas ma saan teha suure annetuse?</div> See oleks imeline! Suuremate kui mõne tuhande dollari suuruste annetuste puhul võtke meiega otse ühendust aadressil %(email)s. <div %(div_question)s>Kas teil on muid makseviise?</div> Praegu mitte. Paljud inimesed ei soovi, et sellised arhiivid eksisteeriksid, seega peame olema ettevaatlikud. Kui saate aidata meil turvaliselt seadistada muid (mugavamaid) makseviise, võtke meiega ühendust aadressil %(email)s. Korduma kippuvad küsimused annetuste kohta Teil on <a %(a_donation)s>olemasolev annetus</a> pooleli. Palun lõpetage või tühistage see annetus enne uue annetuse tegemist. <a %(a_all_donations)s>Näita kõiki minu annetusi</a> Annetuste puhul üle 5000$ võtke meiega otse ühendust aadressil %(email)s. On teretulnud suured annetused jõukatelt isikutelt või asutustelt.  Olge teadlik, et kuigi sellel lehel olevad liikmelisused on „kuu kohta”, on need ühekordsed annetused (mitte korduvad). Vaadake <a %(faq)s>Annetuste KKK</a>. Anna Arhiiv on mittetulunduslik, avatud lähtekoodiga ja avatud andmetega projekt. Annetades ja liikmeks astudes toetate meie tegevust ja arengut. Kõigile meie liikmetele: aitäh, et aitate meil edasi tegutseda! ❤️ Lisateabe saamiseks vaadake <a %(a_donate)s>Annetuste KKK</a>. Liikmeks saamiseks palun <a %(a_login)s>Logi sisse või Registreeru</a>. Täname teid toetuse eest! $%(cost)s / kuus Kui tegite makse ajal vea, ei saa me raha tagasi maksta, kuid püüame olukorra lahendada. Leidke oma PayPali rakenduses või veebisaidil leht “Krüpto”. See asub tavaliselt jaotises “Rahandus”. Minge oma PayPali rakenduses või veebisaidil lehele “Bitcoin”. Vajutage nuppu “Ülekanne” %(transfer_icon)s ja seejärel “Saada”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Kingituskaart %(amazon)s kinkekaart Pangakaart Pangakaart (kasutades rakendust) Binance Krediit-/deebetkaart/Apple/Google (BMC) Cash App Krediit-/deebetkaart Krediit-/deebetkaart 2 Krediit-/deebetkaart (varu) Krüpto %(bitcoin_icon)s Kaart / PayPal / Venmo PayPal (USA) %(bitcoin_icon)s PayPal PayPal (tavaline) Pix (Brasiilia) Revolut (ajutiselt pole saadaval) WeChat Valige oma eelistatud krüptomünt: Annetage Amazoni kinkekaardiga. <strong>TÄHTIS:</strong> See valik on mõeldud %(amazon)s. Kui soovite kasutada teist Amazoni veebisaiti, valige see ülal. <strong>TÄHTIS:</strong> Toetame ainult Amazon.com-i, mitte teisi Amazoni veebisaite. Näiteks .de, .co.uk, .ca EI ole toetatud. Palun ärge kirjutage oma sõnumit. Sisestage täpne summa: %(amount)s Pange tähele, et peame ümardama meie edasimüüjate poolt aktsepteeritavate summadeni (miinimum %(minimum)s). Annetage krediit-/deebetkaardiga, kasutades Alipay rakendust (väga lihtne seadistada). Installige Alipay rakendus <a %(a_app_store)s>Apple App Store’ist</a> või <a %(a_play_store)s>Google Play Store’ist</a>. Registreeruge oma telefoninumbriga. Täiendavaid isikuandmeid ei ole vaja. <span %(style)s>1</span>Installige Alipay rakendus Toetatud: Visa, MasterCard, JCB, Diners Club ja Discover. Lisateabe saamiseks vaadake <a %(a_alipay)s>seda juhendit</a>. <span %(style)s>2</span>Lisage pangakaart Binance'i abil saate osta Bitcoini krediit-/deebetkaardi või pangakonto abil ja seejärel annetada selle Bitcoini meile. Nii saame jääda turvaliseks ja anonüümseks teie annetuse vastuvõtmisel. Binance on saadaval peaaegu igas riigis ja toetab enamikku panku ja krediit-/deebetkaarte. See on praegu meie peamine soovitus. Hindame, et võtate aega, et õppida, kuidas seda meetodit kasutades annetada, kuna see aitab meid palju. Krediitkaartide, deebetkaartide, Apple Pay ja Google Pay jaoks kasutame “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Nende süsteemis on üks “kohv” võrdne 5 dollariga, seega teie annetus ümardatakse lähima 5 kordse summani. Annetage Cash App'i abil. Kui Teil on Cash App, on see lihtsaim viis annetamiseks! Pange tähele, et tehingute puhul, mis on alla %(amount)s, võib Cash App võtta %(fee)s tasu. %(amount)s või suuremate summade puhul on see tasuta! Annetage krediit- või deebetkaardiga. See meetod kasutab vahekonversioonina krüptovaluuta pakkujat. See võib olla veidi segane, seega kasutage seda meetodit ainult siis, kui teised makseviisid ei tööta. See ei tööta ka kõigis riikides. Me ei saa otse toetada krediit-/deebetkaarte, sest pangad ei soovi meiega koostööd teha. ☹ Siiski on mitmeid viise, kuidas krediit-/deebetkaarte kasutada, kasutades teisi maksemeetodeid: Krüptoga saate annetada BTC, ETH, XMR ja SOL. Kasutage seda võimalust, kui olete juba krüptorahaga tuttav. Krüptoga saate annetada BTC, ETH, XMR ja muud. Krüpto kiirteenused Kui kasutate krüptoraha esimest korda, soovitame kasutada %(options)s Bitcoini (esimest ja enim kasutatud krüptoraha) ostmiseks ja annetamiseks. Pange tähele, et väikeste annetuste puhul võivad krediitkaardi tasud meie %(discount)s%% allahindluse kaotada, seega soovitame pikemaid tellimusi. Annetage krediit-/deebetkaardi, PayPali või Venmo abil. Järgmise lehelt saate valida nende vahel. Google Pay ja Apple Pay võivad samuti töötada. Pange tähele, et väikeste annetuste puhul on tasud kõrged, seega soovitame pikemaid tellimusi. PayPal USA kaudu annetamiseks kasutame PayPal Crypto't, mis võimaldab meil jääda anonüümseks. Hindame, et võtate aega, et õppida, kuidas seda meetodit kasutades annetada, kuna see aitab meid palju. Annetage PayPal'i abil. Annetage kasutades oma tavalist PayPali kontot. Annetage Revoluti abil. Kui Teil on Revolut, on see lihtsaim viis annetamiseks! See makseviis lubab maksimaalselt %(amount)s. Palun valige teine kestus või makseviis. See makseviis nõuab miinimumsummat %(amount)s. Palun valige teine kestus või makseviis. Binance Coinbase Kraken Palun valige makseviis. „Võta vastu torrent“: Teie kasutajanimi või sõnum torrentifaili nimes <div %(div_months)s>kord liikmelisuse iga 12. kuu järel</div> Teie kasutajanimi või anonüümne mainimine tänusõnades Varajane juurdepääs uutele funktsioonidele Ligipääs eksklusiivsele telegramm-kanalile varjatud uuendustega %(number)s kiiret allalaadimist päevas kui annetate sel kuul! <a %(a_api)s>JSON API</a>'ile juurdepääs Legendaarne staatus inimkonna teadmiste ja kultuuri säilitamisel Eelnevad eelised, pluss: Teenige <strong>%(percentage)s%% rohkem allalaadimisi</strong> <a %(a_refer)s>kutsudes sõpru</a>. <strong>Piiramatu</strong> SciDB artiklid ilma kinnitamiseta Konto või annetuste küsimuste korral lisage oma konto ID, ekraanipildid, kviitungid ja võimalikult palju teavet. Kontrollime oma e-posti ainult iga 1-2 nädala tagant, seega selle teabe mitte lisamine viivitab lahendust. Veel rohkemate allalaadimiste saamiseks <a %(a_refer)s>kutsuge oma sõpru</a>! Oleme väike vabatahtlike meeskond. Vastamine võib võtta 1-2 nädalat. Pange tähele, et konto nimi või pilt võib tunduda kummaline. Pole vaja muretseda! Neid kontosid haldavad meie annetuste partnerid. Meie kontosid ei ole häkitud. Annetage <span %(span_cost)s></span> <span %(span_label)s></span> 12 kuud “%(tier_name)s” 1 kuu “%(tier_name)s” 24 kuud “%(tier_name)s” 3 kuud “%(tier_name)s” 48 kuud “%(tier_name)s” 6 kuud “%(tier_name)s” 96 kuud “%(tier_name)s” Saate annetuse tühistada kassas. Kinnitamiseks klõpsake annetuse nuppu. <strong>Oluline märkus:</strong> Krüptohinnad võivad metsikult kõikuda, mõnikord isegi kuni 20%% mõne minuti jooksul. See on siiski vähem kui paljude makseteenuse pakkujatega kaasnevad tasud, kes sageli võtavad 50-60%% „varjatud heategevusorganisatsiooniga” töötamise eest. <u>Kui saadate meile kviitungi algse makstud hinnaga, krediteerime teie konto valitud liikmesuse eest</u> (kui kviitung ei ole vanem kui paar tundi). Me tõesti hindame, et olete valmis selliste asjadega tegelema, et meid toetada! ❤️ ❌ Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. <span %(span_circle)s>1</span>Ostke Bitcoin PayPali kaudu <span %(span_circle)s>2</span>Tehke Bitcoini ülekanne meie aadressile ✅ Suunamine annetuste lehele… Annetage Palun oodake vähemalt <span %(span_hours)s>24 tundi</span> (ja värskendage seda lehte) enne meiega ühenduse võtmist. Kui soovite teha annetuse (ükskõik millises summas) ilma liikmelisuseta, kasutage seda Monero (XMR) aadressi: %(address)s. Pärast kinkekaardi saatmist kinnitab meie automaatne süsteem selle mõne minuti jooksul. Kui see ei toimi, proovige kinkekaarti uuesti saata (<a %(a_instr)s>juhised</a>). Kui see ikka ei toimi, saatke meile e-kiri ja Anna vaatab selle käsitsi üle (see võib võtta paar päeva). Kindlasti mainige, kui olete juba proovinud uuesti saata. Näide: Palun kasutage <a %(a_form)s>ametlikku Amazon.com vormi</a>, et saata meile kinkekaart summas %(amount)s allolevale e-posti aadressile. Vormi “Saaja” e-posti aadress: Amazoni kinkekaart Me ei saa vastu võtta muid kinkekaartide meetodeid, <strong>ainult otse ametlikust Amazon.com vormist saadetud kinkekaarte</strong>. Kui te ei kasuta seda vormi, ei saa me teie kinkekaarti tagastada. Kasutage ainult üks kord. Ainult teie kontole unikaalne, ärge jagage. Ootame kinkekaarti… (värskendage lehte, et kontrollida) Avage <a %(a_href)s>QR-koodi annetuste leht</a>. Skaneerige QR-kood Alipay rakendusega või vajutage nuppu, et avada Alipay rakendus. Palun olge kannatlik; lehe laadimine võib võtta aega, kuna see asub Hiinas. <span %(style)s>3</span>Tehke annetus (skaneerige QR-kood või vajutage nuppu) Osta PYUSD münt PayPalis Osta Bitcoini (BTC) Cash Appis Osta natuke rohkem (soovitame %(more)s rohkem) kui annetatav summa (%(amount)s), et katta tehingutasud. Ülejäänud summa jääb teile. Mine Cash Appis lehele „Bitcoin” (BTC). Kanna Bitcoin meie aadressile Väikeste annetuste (alla $25) puhul peate võib-olla kasutama Rush või Priority teenust. Klõpsake nuppu „Saada bitcoin”, et teha „väljamakse”. Vahetage dollarid BTC vastu, vajutades %(icon)s ikooni. Sisestage allpool BTC summa ja klõpsake „Saada”. Kui jääte hätta, vaadake <a %(help_video)s>seda videot</a>. Kiirteenused on mugavad, kuid võtavad kõrgemaid tasusid. Võite seda kasutada krüptovahetuse asemel, kui soovite kiiresti teha suuremat annetust ja ei pahanda 5-10 dollari suuruse tasuga. Veenduge, et saadate annetuste lehel näidatud täpse krüptosumma, mitte summa $USD-s. Vastasel juhul arvestatakse tasu maha ja me ei saa teie liikmesust automaatselt töödelda. Mõnikord võib kinnitamine võtta kuni 24 tundi, seega veenduge, et värskendate seda lehte (isegi kui see on aegunud). Krediit- / deebetkaardi juhised Annetage meie krediit- / deebetkaardi lehe kaudu Mõned sammud mainivad krüptorahakotte, kuid ärge muretsege, te ei pea krüpto kohta midagi õppima. %(coin_name)s juhised Makse üksikasjade kiireks täitmiseks skannige see QR -kood oma krüpto rahakoti rakendusega Skaneerige QR -kood maksmiseks Toetame ainult krüptomüntide standardversioone, mitte eksootilisi võrke või müntide versioone. Tehingu kinnitamine võib sõltuvalt mündist võtta kuni tund aega. Annetage %(amount)s <a %(a_page)s>sellel lehel</a>. See annetus on aegunud. Palun tühistage ja looge uus. Kui olete juba maksnud: Jah, saatsin oma kviitungi Kui krüptovaluuta vahetuskurss tehingu ajal kõikus, lisage kindlasti kviitung, mis näitab algset vahetuskurssi. Me tõesti hindame, et võtate vaevaks kasutada krüptot, see aitab meid palju! ❌ Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. <span %(span_circle)s>%(circle_number)s</span>Saada meile kviitung Kui teil tekib probleeme, võtke meiega ühendust aadressil %(email)s ja lisage võimalikult palju teavet (näiteks ekraanipildid). ✅ Täname annetuse eest! Anna aktiveerib teie liikmesuse käsitsi mõne päeva jooksul. Saatke kviitung või ekraanipilt oma isiklikule kinnitusaadressile: Kui olete oma kviitungi saatnud, klõpsake seda nuppu, et Anna saaks selle käsitsi üle vaadata (see võib võtta paar päeva): Saatke kviitung või ekraanipilt oma isiklikule kinnitusaadressile. Ärge kasutage seda e-posti aadressi oma PayPali annetuse jaoks. Tühista Jah, palun tühistage Kas olete kindel, et soovite tühistada? Ärge tühistage, kui olete juba maksnud. ❌ Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. Tee uus annetus ✅ Teie annetus on tühistatud. Kuupäev: %(date)s Tunnus: %(id)s Telli uuesti Staatus: <span %(span_label)s>%(label)s</span> Kokku: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / kuu %(duration)s kuu jooksul, sisaldab %(discounts)s%% allahindlust)</span> Kokku: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / kuu %(duration)s kuu jooksul)</span> 1. Sisestage oma e-post. 2. Valige oma makseviis. 3. Valige uuesti oma makseviis. 4. Valige “Isehostitud” rahakott. 5. Klõpsake “Kinnitan omandiõigust”. 6. Te peaksite saama e-kirja kviitungiga. Palun saatke see meile ja me kinnitame teie annetuse niipea kui võimalik. (võite soovida tühistada ja luua uue annetuse) Maksejuhised on nüüd aegunud. Kui soovite teha uue annetuse, kasutage ülaltoodud nuppu „Telli uuesti”. Olete juba maksnud. Kui soovite siiski maksejuhiseid üle vaadata, klõpsake siin: Näita vanu maksejuhiseid Kui annetuste leht on blokeeritud, proovige teist internetiühendust (nt VPN või telefoni internet). Kahjuks on Alipay leht sageli ligipääsetav ainult <strong>Mandri-Hiinast</strong>. Võimalik, et peate ajutiselt oma VPN-i välja lülitama või kasutama VPN-i Mandri-Hiinasse (mõnikord töötab ka Hongkong). <span %(span_circle)s>1</span>Tehke annetus Alipay kaudu Annetage kogu summa %(total)s kasutades <a %(a_account)s>seda Alipay kontot</a> Alipay juhised <span %(span_circle)s>1</span>Tehke ülekanne ühele meie krüptokontodest Annetage kogu summa %(total)s ühele neist aadressidest: Krüpto juhised Järgige juhiseid Bitcoini (BTC) ostmiseks. Te peate ostma ainult selle summa, mida soovite annetada, %(total)s. Sisestage meie Bitcoini (BTC) aadress saajaks ja järgige juhiseid, et saata oma annetus summas %(total)s: <span %(span_circle)s>1</span>Tehke annetus Pix kaudu Annetage kogu summa %(total)s kasutades <a %(a_account)s>seda Pix kontot Pix juhised <span %(span_circle)s>1</span>Tehke annetus WeChat kaudu Annetage kogu summa %(total)s kasutades <a %(a_account)s>seda WeChat kontot</a> WeChat juhised Kasutage mõnda järgmistest „krediitkaardist Bitcoiniks” kiirteenustest, mis võtavad vaid paar minutit: BTC / Bitcoini aadress (väline rahakott): BTC / Bitcoini summa: Täitke vormis järgmised andmed: Kui mõni neist andmetest on aegunud, palun saatke meile e-kiri, et meid teavitada. Palun kasutage seda <span %(underline)s>täpset summat</span>. Teie kogukulu võib olla suurem krediitkaarditasude tõttu. Väikeste summade puhul võib see kahjuks olla suurem kui meie allahindlus. (minimaalne: %(minimum)s, esimese tehingu puhul pole kinnitust vaja) (minimaalne: %(minimum)s) (minimaalne: %(minimum)s) (minimaalne: %(minimum)s, esimese tehingu puhul pole kinnitust vaja) (minimaalne: %(minimum)s) (minimaalne: %(minimum)s sõltuvalt riigist, esimese tehingu puhul pole kinnitust vaja) Järgige juhiseid, et osta PYUSD münt (PayPal USD). Ostke veidi rohkem (soovitame %(more)s rohkem) kui annetatav summa (%(amount)s), et katta tehingutasud. Ülejäänud summa jääb teile. Minge oma PayPali rakenduses või veebisaidil lehele “PYUSD”. Vajutage nuppu “Ülekanne” %(icon)s ja seejärel “Saada”. Uuenda staatust Taimeri lähtestamiseks looge lihtsalt uus annetus. Kindlasti kasutage allolevat BTC summat, <em>MITTE</em> eurosid ega dollareid, vastasel juhul ei saa me õiget summat kätte ja ei saa teie liikmelisust automaatselt kinnitada. Osta Bitcoini (BTC) Revolutis Osta natuke rohkem (soovitame %(more)s rohkem) kui annetatav summa (%(amount)s), et katta tehingutasud. Ülejäänud summa jääb teile. Mine Revolutis lehele „Crypto”, et osta Bitcoini (BTC). Kanna Bitcoin meie aadressile Väikeste annetuste (alla $25) puhul peate võib-olla kasutama Rush või Priority teenust. Klõpsake nuppu „Saada bitcoin”, et teha „väljamakse”. Vahetage eurod BTC vastu, vajutades %(icon)s ikooni. Sisestage allpool BTC summa ja klõpsake „Saada”. Kui jääte hätta, vaadake <a %(help_video)s>seda videot</a>. Staatus: 1 2 Samm-sammuline juhend Vaadake allolevat samm-sammulist juhendit. Vastasel juhul võite sellest kontost välja jääda! Kui te pole seda veel teinud, kirjutage oma sisselogimise salajane võti üles: Täname teid annetuse eest! Aeg jäänud: Annetus Kandke %(amount)s üle %(account)s Ootab kinnitust (värskendage lehte, et kontrollida)… Ootab ülekannet (värskendage lehte, et kontrollida)… Varem Kiired allalaadimised viimase 24 tunni jooksul arvestatakse päevase limiidi hulka. Kiirete partneriserverite allalaadimised on märgitud %(icon)s. Viimased 18 tundi Faile pole veel alla laaditud. Allalaaditud faile ei kuvata avalikult. Kõik ajad on UTC-s. Allalaaditud failid Kui laadisite faili alla nii kiire kui ka aeglase allalaadimisega, kuvatakse see kaks korda. Ärge muretsege liiga palju, on palju inimesi, kes laadivad alla veebisaitidelt, millele me viitame, ja on äärmiselt haruldane, et tekib probleeme. Kuid turvalisuse tagamiseks soovitame kasutada VPN-i (tasuline) või <a %(a_tor)s>Tor'i</a> (tasuta). Ma laadisin alla George Orwelli raamatu "1984", kas politsei tuleb minu ukse taha? Sina oled Anna! Kes on Anna? Meil on üks stabiilne JSON API liikmetele, et saada kiire allalaadimise URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentatsioon JSON-is endas). Muude kasutusjuhtude jaoks, nagu kõigi meie failide läbivaatamine, kohandatud otsingu loomine jne, soovitame <a %(a_generate)s>genereerida</a> või <a %(a_download)s>alla laadida</a> meie ElasticSearchi ja MariaDB andmebaasid. Toorandmeid saab käsitsi uurida <a %(a_explore)s>JSON-failide kaudu</a>. Meie toorandmete torrentide loendi saab alla laadida ka <a %(a_torrents)s>JSON</a> formaadis. Kas teil on API? Me ei hosti siin ühtegi autoriõigustega kaitstud materjali. Oleme otsingumootor ja sellisena indekseerime ainult juba avalikult kättesaadavat metaandmet. Allalaadimisel nendest välistest allikatest soovitame kontrollida teie jurisdiktsiooni seadusi selles osas, mis on lubatud. Me ei vastuta teiste poolt hostitud sisu eest. Kui teil on kaebusi selle kohta, mida siin näete, on teie parim valik võtta ühendust algse veebilehega. Me tõmbame regulaarselt nende muudatusi oma andmebaasi. Kui te tõesti arvate, et teil on kehtiv DMCA kaebus, millele me peaksime reageerima, palun täitke <a %(a_copyright)s>DMCA / Autoriõiguse kaebuse vorm</a>. Me võtame teie kaebusi tõsiselt ja võtame teiega ühendust nii kiiresti kui võimalik. Kuidas ma saan teatada autoriõiguste rikkumisest? Siin on mõned raamatud, mis on varjuraamatukogude ja digitaalse säilitamise maailmas erilise tähtsusega: Millised on teie lemmikraamatud? Samuti tahame kõigile meelde tuletada, et kogu meie kood ja andmed on täiesti avatud lähtekoodiga. See on meie sarnaste projektide puhul ainulaadne — me ei tea ühtegi teist projekti, millel oleks sama ulatuslik kataloog ja mis oleks samuti täielikult avatud lähtekoodiga. Me tervitame väga kõiki, kes arvavad, et juhime oma projekti halvasti, et nad võtaksid meie koodi ja andmed ning looksid oma varjuraamatukogu! Me ei ütle seda kiusu pärast või midagi — me tõesti arvame, et see oleks suurepärane, kuna see tõstaks kõigi taset ja aitaks paremini säilitada inimkonna pärandit. Ma vihkan, kuidas te seda projekti juhite! Meil oleks hea meel, kui inimesed seadistaksid <a %(a_mirrors)s>peeglid</a>, ja me toetame seda rahaliselt. Kuidas saan aidata? Jah, me kogume. Meie inspiratsioon metaandmete kogumiseks on Aaron Swartzi eesmärk „üks veebileht iga kunagi avaldatud raamatu jaoks”, mille jaoks ta lõi <a %(a_openlib)s>Open Library</a>. See projekt on hästi toiminud, kuid meie ainulaadne positsioon võimaldab meil saada metaandmeid, mida nemad ei saa. Teine inspiratsioon oli meie soov teada saada <a %(a_blog)s>kui palju raamatuid maailmas on</a>, et saaksime arvutada, kui palju raamatuid on veel päästa. Kas te kogute metaandmeid? Pange tähele, et mhut.org blokeerib teatud IP-vahemikke, seega võib olla vajalik VPN. <strong>Android:</strong> Klõpsake paremas ülanurgas kolme punktiga menüüd ja valige „Lisa avalehele”. <strong>iOS:</strong> Klõpsake allosas nuppu „Jaga” ja valige „Lisa avalehele”. Meil ei ole ametlikku mobiilirakendust, kuid saate selle veebisaidi rakendusena installida. Kas teil on mobiilirakendus? Palun saatke need <a %(a_archive)s>Internet Archive</a>’i. Nad säilitavad need korralikult. Kuidas ma saan annetada raamatuid või muid füüsilisi materjale? Kuidas ma saan raamatuid taotleda? <a %(a_blog)s>Anna Blogi</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regulaarsed uuendused <a %(a_software)s>Anna Tarkvara</a> — meie avatud lähtekoodiga kood <a %(a_datasets)s>Andmekogud</a> — andmete kohta <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatiivsed domeenid Kas on rohkem ressursse Anna Arhiivi kohta? <a %(a_translate)s>Tõlgi Anna Tarkvaras</a> — meie tõlkesüsteem <a %(a_wikipedia)s>Wikipedia</a> — rohkem meie kohta (palun aidake seda lehte uuendada või looge oma keeles!) Valige endale sobivad seaded, jätke otsingukast tühjaks, klõpsake „Otsi” ja seejärel lisage leht järjehoidjatesse, kasutades oma brauseri järjehoidjate funktsiooni. Kuidas ma saan oma otsinguseadeid salvestada? Me tervitame turvaeksperte, kes otsivad haavatavusi meie süsteemides. Oleme vastutustundliku avalikustamise suured pooldajad. Võtke meiega ühendust <a %(a_contact)s>siin</a>. Praegu ei ole meil võimalik pakkuda vigade eest tasusid, välja arvatud haavatavused, mis <a %(a_link)s >võivad ohustada meie anonüümsust</a>, mille eest pakume tasusid vahemikus 10 000 kuni 50 000 dollarit. Tulevikus sooviksime pakkuda laiemat ulatust vigade eest tasumiseks! Palun pange tähele, et sotsiaalinseneria rünnakud on väljaspool meie huviorbiiti. Kui teid huvitab ründav turvalisus ja soovite aidata maailma teadmisi ja kultuuri arhiveerida, võtke kindlasti meiega ühendust. On palju viise, kuidas te saate aidata. Kas teil on vastutustundliku avalikustamise programm? Meil pole sõna otseses mõttes piisavalt ressursse, et pakkuda kõigile maailmas kiireid allalaadimisi, kui palju me ka ei tahaks. Kui mõni rikas heategija sooviks meid selles osas toetada, oleks see uskumatu, kuid seni anname endast parima. Oleme mittetulunduslik projekt, mis suudab vaevu end annetuste abil ülal pidada. Seetõttu rakendasime koos oma partneritega kaks süsteemi tasuta allalaadimisteks: jagatud serverid aeglaste allalaadimistega ja veidi kiiremad serverid ootenimekirjaga (et vähendada samal ajal allalaadivate inimeste arvu). Meil on ka <a %(a_verification)s>brauseri kontroll</a> aeglaste allalaadimiste jaoks, sest muidu kuritarvitavad neid botid ja kraapijad, muutes asjad seaduslike kasutajate jaoks veelgi aeglasemaks. Pange tähele, et Tor Browserit kasutades peate võib-olla oma turvaseadeid kohandama. Madalaimal valikul, mida nimetatakse “Standard”, õnnestub Cloudflare'i turnstile väljakutse. Kõrgematel valikutel, mida nimetatakse “Turvalisem” ja “Kõige turvalisem”, väljakutse ebaõnnestub. Suuremate failide puhul võivad aeglased allalaadimised vahel katkeda. Soovitame kasutada allalaadimishaldurit (näiteks JDownloader), et automaatselt jätkata suuri allalaadimisi. Miks on aeglased allalaadimised nii aeglased? Korduma kippuvad küsimused (KKK) Kasutage <a %(a_list)s>torrenti loendi generaatorit</a>, et luua loend torrentidest, mis vajavad kõige rohkem jagamist, vastavalt teie salvestusruumi piirangutele. Jah, vaadake <a %(a_llm)s>LLM andmete</a> lehte. Enamik torrentidest sisaldab faile otse, mis tähendab, et saate torrentiklientidele öelda, et laadige alla ainult vajalikud failid. Et määrata, millised failid alla laadida, saate <a %(a_generate)s>genereerida</a> meie metaandmeid või <a %(a_download)s>alla laadida</a> meie ElasticSearchi ja MariaDB andmebaasid. Kahjuks sisaldavad mitmed torrentikogumikud juurfailina .zip või .tar faile, sel juhul peate alla laadima kogu torrenti, enne kui saate valida üksikuid faile. Kasutajasõbralikke tööriistu torrentide filtreerimiseks pole veel saadaval, kuid ootame panuseid. (Meil on siiski <a %(a_ideas)s>mõned ideed</a> viimase juhtumi jaoks.) Pikk vastus: Lühike vastus: mitte lihtsalt. Püüame hoida minimaalset duplikaatide või kattuvuste arvu selles loendis olevate torrentide vahel, kuid see ei ole alati saavutatav ja sõltub suuresti allikate raamatukogude poliitikast. Raamatukogude puhul, mis avaldavad oma torrentid, ei ole see meie kontrolli all. Anna’s Archive'i poolt välja antud torrentide puhul eemaldame duplikaadid ainult MD5 hash'i alusel, mis tähendab, et sama raamatu erinevaid versioone ei eemaldata. Jah. Need on tegelikult PDF-id ja EPUB-id, neil lihtsalt puudub paljudes meie torrentites laiend. On kaks kohta, kus saate leida torrentifailide metaandmeid, sealhulgas failitüüpe/laiendeid: 1. Igal kogumikul või väljaandel on oma metaandmed. Näiteks <a %(a_libgen_nonfic)s>Libgen.rs torrentidel</a> on vastavad metaandmebaasid hostitud Libgen.rs veebilehel. Me tavaliselt lingime iga kogumiku <a %(a_datasets)s>andmekogu lehelt</a> vastavatele metaandmete ressurssidele. 2. Soovitame <a %(a_generate)s>genereerida</a> või <a %(a_download)s>alla laadida</a> meie ElasticSearchi ja MariaDB andmebaasid. Need sisaldavad vastendust iga Anna Arhiivi kirje ja selle vastavate torrentifailide vahel (kui saadaval), märksõna "torrent_paths" all ElasticSearchi JSON-is. Mõned torrentikliendid ei toeta suuri tükisuurusi, mida paljud meie torrentid sisaldavad (uuemate puhul me seda enam ei tee — kuigi see on spetsifikatsioonide järgi kehtiv!). Seega proovige teist klienti, kui sellega kokku puutute, või kaevake oma torrentikliendi tootjatele. Tahaksin aidata seemneid jagada, kuid mul pole palju kettaruumi. Torrentid on liiga aeglased; kas ma saan andmeid otse teilt alla laadida? Kas ma saan alla laadida ainult osa failidest, näiteks ainult teatud keeles või teemal? Kuidas te käsitlete duplikaate torrentides? Kas ma saan torrentide loendi JSON-formaadis? Ma ei näe torrentites PDF-e ega EPUB-e, ainult binaarfaile? Mida ma tegema peaksin? Miks ei saa minu torrentiklient avada mõnda teie torrentifaili / magnetlinki? Torrentide KKK Kuidas ma saan uusi raamatuid üles laadida? Palun vaadake <a %(a_href)s>seda suurepärast projekti</a>. Kas teil on tööaja jälgija? Mis on Anna arhiiv? Saage liikmeks, et kasutada kiireid allalaadimisi. Nüüd toetame Amazon kinkekaarte, krediit- ja deebetkaarte, krüptot, Alipayd ja WeChati. Olete täna kiirete allalaadimiste limiidi ületanud. Juurdepääs Tunnised allalaadimised viimase 30 päeva jooksul. Tunnine keskmine: %(hourly)s. Päevane keskmine: %(daily)s. Teeme koostööd partneritega, et muuta meie kogud kõigile kergesti ja tasuta kättesaadavaks. Usume, et kõigil on õigus inimkonna kollektiivsele tarkusele. Ja <a %(a_search)s>mitte autorite arvelt</a>. Anna arhiivis kasutatavad andmekogumid on täiesti avatud ja neid saab peegeldada hulgikogustes kasutades torrenteid. <a %(a_datasets)s>Loe lähemalt…</a> Pikaajaline arhiiv Täielik andmebaas Otsi Raamatud, artiklid, ajakirjad, koomiksid, raamatukogu kirjed, metaandmed, … Kogu meie <a %(a_code)s>kood</a> ja <a %(a_datasets)s>andmed</a> on täiesti avatud lähtekoodiga. <span %(span_anna)s>Anna arhiiv</span> on mittetulunduslik projekt, millel on kaks eesmärki: <li><strong>Säilitamine:</strong> Kogu inimkonna teadmiste ja kultuuri varundamine.</li><li><strong>Juurdepääs:</strong> Nende teadmiste ja kultuuri kättesaadavaks tegemine kõigile maailmas.</li> Meil on maailma suurim kvaliteetse tekstandmete kogu. <a %(a_llm)s>Loe lähemalt…</a> LLM training data 🪩 Peeglid: vabatahtlike otsing Kui haldad kõrge riskiga anonüümset maksetöötlejat, võta meiega ühendust. Otsime ka inimesi, kes sooviksid paigutada maitsekalt väikseid reklaame. Kõik tulud lähevad meie säilitustööle. Säilitamine Hinnanguliselt oleme säilitanud umbes <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% maailma raamatutest</a>. Me säilitame raamatuid, artikleid, koomikseid, ajakirju ja palju muud, tuues need materjalid erinevatest <a href="https://en.wikipedia.org/wiki/Shadow_library">varjuraamatukogudest</a>, ametlikest raamatukogudest ja teistest kogudest ühte kohta kokku. Kogu see teave säilitatakse igavesti, tehes selle hulgi kopeerimise lihtsaks — kasutades torrenteid —, mille tulemusena on palju koopiaid üle kogu maailma. Mõned varjuraamatukogud teevad seda juba ise (nt Sci-Hub, Library Genesis), samas kui Anna arhiiv “vabastab” teisi raamatukogusid, mis ei paku hulgijaotust (nt Z-Library) või ei ole üldse varjuraamatukogud (nt Internet Archive, DuXiu). See laialdane levik koos avatud lähtekoodiga muudab meie veebisaidi vastupidavaks mahavõtmistele ja tagab inimkonna teadmiste ja kultuuri pikaajalise säilimise. Lisateavet leiate <a href="/datasets">meie andmekogude kohta</a>. Kui olete <a %(a_member)s>liige</a>, ei ole brauseri kontrollimine vajalik. 🧬&nbsp;SciDB on Sci-Hub'i jätk. SciDB Ava DOI Sci-Hub on <a %(a_paused)s>peatanud</a> uute artiklite üleslaadimise. Otsene juurdepääs %(count)s teadusartiklitele 🧬&nbsp;SciDB on Sci-Hubi jätk, oma tuttava liidese ja PDF-ide otsevaatamisega. Sisestage oma DOI, et vaadata. Meil on olemas kogu Sci-Hubi kollektsioon, samuti uued teadusartiklid. Enamikku saab vaadata otse tuttava liidese kaudu, mis on sarnane Sci-Hubiga. Mõned saab alla laadida väliste allikate kaudu, sel juhul kuvame lingid nendele. Saate tohutult aidata, kui jagate torrenteid. <a %(a_torrents)s>Loe lähemalt…</a> >%(count)s jagajad <%(count)s jagajat %(count_min)s–%(count_max)s jagajat 🤝 Otsime vabatahtlikke Oleme mittetulunduslik avatud lähtekoodiga projekt ja otsime alati inimesi, kes saaksid aidata. IPFS allalaadimised Loend %(by)s, loodud <span %(span_time)s>%(time)s</span> Salvesta ❌ Midagi läks valesti. Palun proovige uuesti. ✅ Salvestatud. Palun laadige leht uuesti. Nimekiri on tühi. muuda Lisage või eemaldage sellest loendist, leides faili ja avades vahekaardi „Loendid“. Nimekiri Kuidas me saame aidata Kattuvuse eemaldamine (deduplikatsioon) Teksti ja metaandmete ekstraheerimine OCR Me suudame pakkuda kiiret juurdepääsu meie täiskogudele, samuti avaldamata kogudele. See on ettevõtte tasemel juurdepääs, mida saame pakkuda annetuste eest, mis jäävad kümnete tuhandete USD vahemikku. Oleme valmis ka vahetama seda kõrgekvaliteediliste kogude vastu, mida meil veel pole. Saame teile raha tagasi maksta, kui suudate pakkuda meile meie andmete rikastamist, näiteks: Toetage inimteadmiste pikaajalist arhiveerimist, saades samal ajal oma mudeli jaoks paremaid andmeid! <a %(a_contact)s>Võtke meiega ühendust</a>, et arutada, kuidas saame koostööd teha. On hästi teada, et LLM-id arenevad kõrgekvaliteediliste andmete põhjal. Meil on maailma suurim raamatute, artiklite, ajakirjade jne kogu, mis on ühed kõrgeima kvaliteediga tekstiallikad. LLM andmed Ainulaadne ulatus ja valik Meie kogus on üle saja miljoni faili, sealhulgas teadusajakirjad, õpikud ja ajakirjad. Selle ulatuse saavutame, kombineerides suuri olemasolevaid hoidlaid. Mõned meie allikakogud on juba saadaval hulgikogustes (Sci-Hub ja osad Libgenist). Teised allikad vabastasime ise. <a %(a_datasets)s>Datasets</a> näitab täielikku ülevaadet. Meie kogus on miljoneid raamatuid, artikleid ja ajakirju ajast enne e-raamatute ajastut. Suured osad sellest kogust on juba OCR-itud ja neil on juba vähe sisemist kattuvust. Jätka Kui olete oma võtme kaotanud, palun <a %(a_contact)s>võtke meiega ühendust</a> ja esitage võimalikult palju teavet. Võimalik, et peate meiega ühenduse võtmiseks ajutiselt uue konto looma. Palun <a %(a_account)s>logi sisse</a>, et seda lehte vaadata.</a> Spämmirobotite kontode loomise vältimiseks peame esmalt teie brauseri kinnitama. Kui satute lõputusse silmusesse, soovitame paigaldada <a %(a_privacypass)s>Privacy Pass</a>. Samuti võib aidata reklaamiblokeerijate ja teiste brauserilaienduste väljalülitamine. Logi sisse / Registreeru Anna arhiiv on ajutiselt hoolduseks suletud. Palun tulge tunni aja pärast tagasi. Alternatiivne autor Alternatiivne kirjeldus Alternatiivne väljaanne Alternatiivne laiend Alternatiivne failinimi Alternatiivne kirjastaja Alternatiivne pealkiri avatud lähtekoodiga kuupäev Loe edasi… kirjeldus Otsi Anna arhiivist CADAL SSNO numbrit Otsi Anna arhiivist DuXiu SSID numbrit Otsi Anna arhiivist DuXiu DXID numbrit Otsi Anna arhiivist ISBN-i Otsi Anna arhiivist OCLC (WorldCat) numbrit Otsi Anna arhiivist Open Library ID-d Anna Arhiivi veebivaatur %(count)s mõjutatud lehekülge Pärast allalaadimist: Selle faili parem versioon võib olla saadaval aadressil %(link)s Torrenti allalaadimised hulgi kollektsioon Kasutage veebitööriistu vormingute vaheliseks teisendamiseks. Soovitatavad teisendustööriistad: %(links)s Suurte failide puhul soovitame katkestuste vältimiseks kasutada allalaadimishaldurit. Soovitatavad allalaadimishaldurid: %(links)s EBSCOhost e-raamatute indeks (ainult ekspertidele) (klõpsake ka "GET" ülaosas) (klõpsake ülaosas "GET") Välised allalaadimised <strong>🚀 Kiired allalaadimised</strong> Teil on täna jäänud %(remaining)s. Aitäh, et olete liige! ❤️ <strong>🚀 Kiired allalaadimised</strong> Teie tänased kiired allalaadimised on otsas. <strong>🚀 Kiired allalaadimised</strong> Olete selle faili hiljuti alla laadinud. Lingid jäävad mõneks ajaks kehtima. <strong>🚀 Kiired allalaadimised</strong> Saage <a %(a_membership)s>liikmeks</a>, et toetada raamatute, artiklite ja muu pikaajalist säilitamist. Tänutäheks teie toetuse eest saate kiired allalaadimised. ❤️ 🚀 Kiired allalaadimised 🐢 Aeglased allalaadimised Laenutage Interneti Arhiivist IPFS Gateway #%(num)d (võib-olla peate IPFS-iga mitu korda proovima) Libgen.li Libgen.rs Ilukirjandus Libgen.rs Mitte-ilukirjandus nende reklaamid võivad sisaldada pahavara, seega kasutage reklaamiblokeerijat või ärge klõpsake reklaamidel Amazoni „Send to Kindle” djazz’i „Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC failide allalaadimine võib olla ebausaldusväärne) Allalaadimisi ei leitud. Kõik allalaadimisvõimalused sisaldavad sama faili ja peaksid olema ohutud kasutada. Sellegipoolest ole alati ettevaatlik failide allalaadimisel internetist, eriti Anna arhiivist väljaspoolt. Näiteks hoia oma seadmed ajakohasena. (ei ümbersuunamist) Ava meie vaaturis (ava vaaturis) Valik #%(num)d: %(link)s %(extra)s Leia algne kirje CADAL-ist Otsi käsitsi DuXiu-st Leia algne kirje ISBNdb-st Leia algne kirje WorldCat-ist Leia algne kirje Open Library-st Otsi erinevatest andmebaasidest ISBN-i (ainult prinditud puuetega kasutajatele) PubMed Faili avamiseks vajate e-raamatu või PDF-lugejat, sõltuvalt failivormingust. Soovitatavad e-raamatute lugejad: %(links)s Anna arhiiv 🧬 SciDB Sci-Hub: %(doi)s (seotud DOI ei pruugi olla saadaval Sci-Hubis) Saate saata nii PDF- kui ka EPUB-faile oma Kindle'i või Kobo e-lugerisse. Soovitatavad tööriistad: %(links)s Rohkem teavet <a %(a_slow)s>KKK-s</a>. Toetage autoreid ja raamatukogusid Kui see teile meeldib ja saate seda endale lubada, kaaluge originaali ostmist või autorite otsest toetamist. Kui see on saadaval teie kohalikus raamatukogus, kaaluge selle tasuta laenutamist sealt. Partneriserveri allalaadimised pole selle faili jaoks ajutiselt saadaval. torrent Usaldusväärsetelt partneritelt. Z-Library Z-Raamatukogu Toris (nõuab Tor Browserit) näita väliseid allalaadimisi <span class="font-bold">❌ Sellel failil võib olla probleeme ja see on allikakogust peidetud.</span> Mõnikord on see autoriõiguse omaniku nõudmisel, mõnikord on saadaval parem alternatiiv, kuid mõnikord on probleem failis endas. Võib-olla on see siiski allalaadimiseks sobiv, kuid soovitame esmalt otsida alternatiivset faili. Rohkem üksikasju: Kui soovite siiski selle faili alla laadida, kasutage kindlasti ainult usaldusväärset ja ajakohast tarkvara selle avamiseks. metaandmete kommentaarid AA: Otsi Anna arhiivist “%(name)s” Koodide uurija: Vaata koodide uurijas “%(name)s” URL: Veebisait: Kui teil on see fail ja see pole veel Anna arhiivis saadaval, kaaluge selle <a %(a_request)s>üleslaadimist</a>. Internet Archive'i kontrollitud digitaalne laenutusfail „%(id)s“ See on Internet Archive'i faili kirje, mitte otse allalaaditav fail. Võite proovida raamatut laenata (link allpool) või kasutada seda URL-i, kui <a %(a_request)s>taotlete faili</a>. Paranda metaandmeid CADAL SSNO %(id)s metaandmete kirje See on metaandmete kirje, mitte allalaaditav fail. Võite kasutada seda URL-i, kui <a %(a_request)s>taotlete faili</a>. DuXiu SSID %(id)s metaandmete kirje ISBNdb %(id)s metaandmete kirje MagzDB ID %(id)s metaandmete kirje Nexus/STC ID %(id)s metaandmete kirje OCLC (WorldCat) number %(id)s metaandmete kirje Open Library %(id)s metaandmete kirje Sci-Hub fail „%(id)s“ Ei leitud “%(md5_input)s” ei leitud meie andmebaasist. Lisa kommentaar (%(count)s) Saad md5 URL-ist, nt Selle faili parema versiooni MD5 (kui on olemas). Täida see, kui on olemas teine fail, mis vastab sellele failile (sama väljaanne, sama faililaiend, kui leiad), mida inimesed peaksid selle faili asemel kasutama. Kui tead paremat versiooni sellest failist väljaspool Anna arhiivi, siis palun <a %(a_upload)s>laadi see üles</a>. Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. Te jätsite kommentaari. Selle kuvamine võib võtta minuti. Palun kasuta <a %(a_copyright)s>DMCA / autoriõiguse nõude vormi</a>. Kirjelda probleemi (kohustuslik) Kui sellel failil on suurepärane kvaliteet, saate siin arutada kõike selle kohta! Kui ei, siis kasutage nuppu „Teata failiprobleemist”. Suurepärane faili kvaliteet (%(count)s) Faili kvaliteet Õppige, kuidas <a %(a_metadata)s>parandada selle faili metaandmeid</a> ise. Probleemi kirjeldus Palun <a %(a_login)s>logi sisse</a>. Mulle meeldis see raamat väga! Aita kogukonda, teatades selle faili kvaliteedist! 🙌 Midagi läks valesti. Palun laadige leht uuesti ja proovige uuesti. Teata failiprobleemist (%(count)s) Aitäh, et esitasite oma aruande. See kuvatakse sellel lehel ja vaadatakse käsitsi üle Anna poolt (kuni meil on korralik modereerimissüsteem). Jäta kommentaar Esita aruanne Mis on selle failiga valesti? Laena (%(count)s) Kommentaarid (%(count)s) Allalaadimised (%(count)s) Uuri metaandmeid (%(count)s) Nimekirjad (%(count)s) Statistika (%(count)s) Teabe saamiseks selle konkreetse faili kohta vaadake selle <a %(a_href)s>JSON faili</a>. See on fail, mida haldab <a %(a_ia)s>IA kontrollitud digitaalne laenutus</a> raamatukogu ja mille on otsinguks indekseerinud Anna arhiiv. Teabe saamiseks erinevate koostatud andmekogumite kohta vaadake <a %(a_datasets)s>Datasets lehte</a>. Lingitud kirje metaandmed Paranda metaandmeid Open Library's „Faili MD5” on räsi, mis arvutatakse faili sisust ja on selle sisu põhjal mõistlikult unikaalne. Kõik varjulised raamatukogud, mida oleme siin indekseerinud, kasutavad peamiselt MD5-sid failide tuvastamiseks. Fail võib ilmuda mitmes varjulises raamatukogus. Teabe saamiseks erinevate koostatud andmekogumite kohta vaadake <a %(a_datasets)s>Datasets lehte</a>. Teata faili kvaliteedist Kokku allalaadimisi: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tšehhi metaandmed %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Hoiatus: mitu lingitud kirjet: Kui vaatate raamatut Anna arhiivis, näete erinevaid välju: pealkiri, autor, kirjastaja, väljaanne, aasta, kirjeldus, failinimi ja palju muud. Kõiki neid teabekilde nimetatakse <em>metaandmeteks</em>. Kuna me ühendame raamatuid erinevatest <em>allikraamatukogudest</em>, kuvame mis tahes metaandmeid, mis on saadaval selles allikraamatukogus. Näiteks, kui saame raamatu Library Genesis'ist, kuvame pealkirja Library Genesis'i andmebaasist. Mõnikord on raamat olemas <em>mitmes</em> allikraamatukogus, millel võivad olla erinevad metaandmeväljad. Sel juhul kuvame lihtsalt iga välja pikima versiooni, kuna see sisaldab loodetavasti kõige kasulikumat teavet! Kuvame siiski teised väljad kirjelduse all, nt „alternatiivne pealkiri” (kuid ainult siis, kui need on erinevad). Samuti eraldame <em>koode</em>, nagu identifikaatorid ja klassifikaatorid, allikraamatukogust. <em>Identifikaatorid</em> esindavad ainulaadselt konkreetset raamatu väljaannet; näited on ISBN, DOI, Open Library ID, Google Books ID või Amazon ID. <em>Klassifikaatorid</em> rühmitavad kokku mitu sarnast raamatut; näited on Dewey kümnendsüsteem (DCC), UDC, LCC, RVK või GOST. Mõnikord on need koodid allikraamatukogudes selgesõnaliselt seotud ja mõnikord saame need eraldada failinimest või kirjeldusest (peamiselt ISBN ja DOI). Saame kasutada identifikaatoreid, et leida kirjeid <em>ainult metaandmetega kogudest</em>, nagu OpenLibrary, ISBNdb või WorldCat/OCLC. Meie otsingumootoris on spetsiaalne <em>metaandmete vahekaart</em>, kui soovite neid kogusid sirvida. Kasutame sobivaid kirjeid, et täita puuduvaid metaandmevälju (nt kui pealkiri puudub) või nt „alternatiivse pealkirjana” (kui on olemasolev pealkiri). Et näha täpselt, kust raamatu metaandmed pärinevad, vaadake raamatu lehel <em>„Tehnilised üksikasjad” vahekaarti</em>. Seal on link selle raamatu toorele JSON-ile, koos viidetega algkirjete toorele JSON-ile. Lisateabe saamiseks vaadake järgmisi lehti: <a %(a_datasets)s>Andmekogumid</a>, <a %(a_search_metadata)s>Otsing (metaandmete vahekaart)</a>, <a %(a_codes)s>Koodide uurija</a> ja <a %(a_example)s>Näide metaandmete JSON</a>. Lõpuks saab kõiki meie metaandmeid <a %(a_generated)s>genereerida</a> või <a %(a_downloaded)s>alla laadida</a> ElasticSearch ja MariaDB andmebaasidena. Taust Saate aidata raamatute säilitamisel, parandades metaandmeid! Esiteks lugege taustteavet metaandmete kohta Anna arhiivis ja seejärel õppige, kuidas metaandmeid parandada, linkides Open Library'ga, ning teenige tasuta liikmesus Anna arhiivis. Paranda metaandmeid Kui leiate faili, millel on halvad metaandmed, kuidas peaksite seda parandama? Võite minna allikraamatukokku ja järgida selle metaandmete parandamise protseduure, aga mida teha, kui fail on olemas mitmes allikraamatukogus? Anna arhiivis on üks identifikaator, mida käsitletakse eriliselt. <strong>Open Library's olev annas_archive md5 väli ületab alati kõik muud metaandmed!</strong> Astume samm tagasi ja õpime esmalt Open Library kohta. Open Library asutati 2006. aastal Aaron Swartzi poolt eesmärgiga „üks veebileht iga kunagi avaldatud raamatu jaoks”. See on omamoodi Wikipedia raamatu metaandmete jaoks: kõik saavad seda redigeerida, see on vabalt litsentseeritud ja seda saab massiliselt alla laadida. See on raamatute andmebaas, mis on meie missiooniga kõige rohkem kooskõlas — tegelikult on Anna arhiiv inspireeritud Aaron Swartzi visioonist ja elust. Selle asemel, et ratast uuesti leiutada, otsustasime suunata oma vabatahtlikud Open Library poole. Kui näete raamatut, millel on valed metaandmed, saate aidata järgmiselt: Pange tähele, et see kehtib ainult raamatute, mitte teadusartiklite või muude failitüüpide kohta. Muude failitüüpide puhul soovitame siiski leida allikakogu. Muudatuste lisamine Anna arhiivi võib võtta paar nädalat, kuna peame alla laadima uusima Open Library andmebaasi ja uuesti looma oma otsinguindeksi.  Minge <a %(a_openlib)s>Open Library veebisaidile</a>. Leidke õige raamatu kirje. <strong>HOIATUS:</strong> veenduge, et valite õige <strong>väljaande</strong>. Open Library's on „teosed” ja „väljaanded”. „Teos” võiks olla „Harry Potter ja tarkade kivi”. „Väljaanne” võiks olla: 1997. aasta esimene väljaanne, mille avaldas Bloomsbery ja millel on 256 lehekülge. 2003. aasta pehmekaaneline väljaanne, mille avaldas Raincoast Books ja millel on 223 lehekülge. 2000. aasta poolakeelne tõlge “Harry Potter I Kamie Filozoficzn”, mille avaldas Media Rodzina ja millel on 328 lehekülge. Kõigil neil väljaannetel on erinevad ISBN-id ja erinev sisu, seega vali kindlasti õige! Muuda kirjet (või loo see, kui seda pole olemas) ja lisa nii palju kasulikku teavet kui võimalik! Oled siin juba, seega tee kirje tõeliselt suurepäraseks. Vali jaotises “ID numbrid” “Anna arhiiv” ja lisa raamatu MD5 Anna arhiivist. See on pikk tähtede ja numbrite jada pärast “/md5/” URL-is. Proovi leida Anna arhiivist ka teisi faile, mis sellele kirjele vastavad, ja lisa need samuti. Tulevikus saame need Anna arhiivi otsingulehel duplikaatidena rühmitada. Kui oled lõpetanud, kirjuta üles URL, mida just uuendasid. Kui oled uuendanud vähemalt 30 kirjet Anna arhiivi MD5-dega, saada meile <a %(a_contact)s>e-kiri</a> ja saada meile nimekiri. Anname sulle tasuta liikmesuse Anna arhiivis, et saaksid seda tööd lihtsamini teha (ja tänutäheks abi eest). Need peavad olema kvaliteetsed muudatused, mis lisavad märkimisväärselt teavet, vastasel juhul lükatakse sinu taotlus tagasi. Sinu taotlus lükatakse tagasi ka siis, kui mõni muudatus tühistatakse või parandatakse Open Library moderaatorite poolt. Open Library linkimine Kui te osalete märkimisväärselt meie töö arendamises ja toimimises, saame arutada teiega rohkemate annetustulude jagamist, et saaksite neid vajadusel kasutada. Maksame majutuse eest ainult siis, kui teil on kõik seadistatud ja olete näidanud, et suudate arhiivi uuendustega ajakohasena hoida. See tähendab, et peate esimesed 1-2 kuud oma taskust maksma. Teie aega ei kompenseerita (ja ka meie oma mitte), kuna see on puhas vabatahtlik töö. Oleme valmis katma majutus- ja VPN-kulud, esialgu kuni 200 dollarit kuus. See on piisav põhilise otsinguserveri ja DMCA-kaitstud puhverserveri jaoks. Majutuskulud Palun <strong>ärge võtke meiega ühendust</strong> luba küsides või põhiliste küsimuste esitamiseks. Teod räägivad valjemini kui sõnad! Kogu teave on olemas, nii et alustage lihtsalt oma peegli seadistamist. Võite vabalt postitada pileteid või liitmissoove meie Gitlabi, kui teil tekib probleeme. Võime vajada teiega koos mõne peeglispetsiifilise funktsiooni loomist, näiteks „Anna’s Archive’i” ümberbrändimine teie veebisaidi nimeks, (esialgu) kasutajakontode keelamine või meie põhisaidile linkimine raamatute lehtedelt. Kui teie peegel töötab, võtke meiega kindlasti ühendust. Soovime üle vaadata teie OpSec’i ja kui see on kindel, linkime teie peegli ja hakkame teiega tihedamalt koostööd tegema. Tänud ette kõigile, kes on valmis sellisel viisil panustama! See ei ole nõrganärvilistele, kuid see kindlustaks inimajaloo suurima tõeliselt avatud raamatukogu pikaealisuse. Alustamine Anna arhiivi vastupidavuse suurendamiseks otsime vabatahtlikke, kes haldaksid peegleid. Teie versioon on selgelt eristatav kui peegel, nt „Bobi arhiiv, Anna’s Archive’i peegel”. Olete valmis võtma selle tööga seotud riske, mis on märkimisväärsed. Teil on sügav arusaam vajalikust operatiivturvalisusest. <a %(a_shadow)s>Nende</a> <a %(a_pirate)s>postituste</a> sisu on teile iseenesestmõistetav. Esialgu me ei anna teile juurdepääsu meie partneri serveri allalaadimistele, kuid kui kõik läheb hästi, saame seda teiega jagada. Te haldate Anna’s Archive’i avatud lähtekoodiga koodibaasi ja uuendate regulaarselt nii koodi kui ka andmeid. Olete valmis panustama meie <a %(a_codebase)s>koodibaasi</a> — koostöös meie meeskonnaga — selle saavutamiseks. Me otsime seda: Peeglid: vabatahtlike üleskutse Tee veel üks annetus. Annetusi pole veel. <a %(a_donate)s>Tee oma esimene annetus.</a> Annetuste üksikasju ei kuvata avalikult. Minu annetused 📡 Meie kogu hulgipeegeldamiseks vaadake <a %(a_datasets)s>Datasets</a> ja <a %(a_torrents)s>Torrents</a> lehti. Teie IP-aadressilt viimase 24 tunni jooksul tehtud allalaadimised: %(count)s. 🚀 Kiiremaks allalaadimiseks ja brauserikontrollide vahelejätmiseks <a %(a_membership)s>hakka liikmeks</a>. Laadi alla partneri veebisaidilt Oodates võite jätkata Anna Arhiivi sirvimist teises vahekaardis (kui teie brauser toetab taustavahekaartide värskendamist). Võite oodata, kuni mitu allalaadimislehte laaditakse samal ajal (kuid palun laadige korraga alla ainult üks fail serveri kohta). Kui saate allalaadimislingi, on see kehtiv mitu tundi. Tänan ootamise eest, see hoiab veebisaidi kõigile tasuta kättesaadavana! 😊 🔗 Kõik selle faili allalaadimislingid: <a %(a_main)s>Faili avaleht</a>. ❌ Aeglased allalaadimised ei ole saadaval Cloudflare VPN-ide kaudu ega muul viisil Cloudflare IP-aadressidelt. ❌ Aeglased allalaadimised on saadaval ainult ametliku veebisaidi kaudu. Külastage %(websites)s. 📚 Kasutage allalaadimiseks järgmist URL-i: <a %(a_download)s>Laadi kohe alla</a>. Selleks, et kõigil oleks võimalus faile tasuta alla laadida, peate enne selle faili allalaadimist ootama. Palun oodake <span %(span_countdown)s>%(wait_seconds)s</span> sekundit, et seda faili alla laadida. Hoiatus: teie IP-aadressilt on viimase 24 tunni jooksul tehtud palju allalaadimisi. Allalaadimised võivad olla tavapärasest aeglasemad. Kui kasutate VPN-i, jagatud internetiühendust või teie ISP jagab IP-aadresse, võib see hoiatus olla tingitud sellest. Salvesta ❌ Midagi läks valesti. Palun proovige uuesti. ✅ Salvestatud. Palun laadige leht uuesti. Muutke oma kuvatavat nime. Teie identifikaatorit (osa pärast „#“) ei saa muuta. Profiil loodud <span %(span_time)s>%(time)s</span> muuda Loendid Loo uus loend, leides faili ja avades vahekaardi „Loendid“. Veel loende pole veel Profiili ei leitud. Profiil Praegu ei saa me raamatutaotlusi rahuldada. Ärge saatke meile oma raamatutaotlusi e-posti teel. Palun esitage oma taotlused Z-Library või Libgeni foorumites. Kirje Anna arhiivis DOI: %(doi)s Laadi alla SciDB Nexus/STC Eelvaade pole veel saadaval. Laadige fail alla <a %(a_path)s>Anna Arhiivist</a>. Inimeste teadmiste kättesaadavuse ja pikaajalise säilitamise toetamiseks saage <a %(a_donate)s>liikmeks</a>. Boonusena laadib 🧬&nbsp;SciDB liikmetele kiiremini, ilma piiranguteta. Ei tööta? Proovige <a %(a_refresh)s>värskendada</a>. Sci-Hub Lisa konkreetne otsinguväli Otsi kirjeldusi ja metaandmete kommentaare Avaldamise aasta Täiustatud Juurdepääs Sisu Kuva Loend Tabel Failitüüp Keel Sorteeri Suurim Kõige asjakohasem Uusim (faili suurus) (avatud lähtekoodiga) (publikatsiooniaasta) Vanim Juhuslik Väikseim Allikas kraapitud ja avatud lähtekoodiga AA poolt Digitaalne laenutus (%(count)s) Ajakirja artiklid (%(count)s) Leidsime vasteid: %(in)s. Saad viidata seal leitud URL-ile, kui <a %(a_request)s>faili taotled</a>. Metateave (%(count)s) Koodide järgi otsinguindeksi uurimiseks kasutage <a %(a_href)s>Koodide uurijat</a>. Otsinguindeksit uuendatakse kord kuus. Praegu sisaldab see kirjeid kuni %(last_data_refresh_date)s. Tehnilise teabe saamiseks vaadake <a %(link_open_tag)s>andmekogude lehte</a>. Välistage Sisaldage ainult Kontrollimata veel… Järgmine … Eelmine See otsinguindeks sisaldab praegu Internet Archive'i kontrollitud digitaalsete laenuraamatukogude metaandmeid. <a %(a_datasets)s>Rohkem meie andmekogudest</a>. Rohkemate digitaalsete laenuraamatukogude kohta vaadake <a %(a_wikipedia)s>Wikipediat</a> ja <a %(a_mobileread)s>MobileRead Wiki</a>. DMCA / autoriõiguse nõuete jaoks <a %(a_copyright)s>klõpsake siin</a>. Allalaadimise aeg Otsingu viga. Proovi <a %(a_reload)s>lehte uuesti laadida</a>. Kui probleem püsib, saada meile e-kiri aadressil %(email)s. Kiire allalaadimine Tegelikult saab igaüks aidata neid faile säilitada, külvates meie <a %(a_torrents)s>ühtset torrentite nimekirja</a>. ➡️ Mõnikord juhtub see valesti, kui otsinguserver on aeglane. Sellistel juhtudel võib <a %(a_attrs)s>uuesti laadimine</a> aidata. ❌ Selle failiga võib esineda probleeme. Otsite teadusartikleid? See otsinguindeks sisaldab praegu metateavet erinevatest metateabe allikatest. <a %(a_datasets)s>Rohkem meie andmekogudest</a>. Kirjalike teoste metateabe allikaid on maailmas väga palju. <a %(a_wikipedia)s>See Wikipedia leht</a> on hea algus, kuid kui tead teisi häid nimekirju, anna meile teada. Metateabe puhul kuvame algsed kirjed. Me ei tee kirjete ühendamist. Meil on praegu maailma kõige ulatuslikum avatud raamatute, teadusartiklite ja muude kirjalike tööde kataloog. Me peegeldame Sci-Hub'i, Library Genesis't, Z-Library't, <a %(a_datasets)s>ja palju muud</a>. <span class="font-bold">Faile ei leitud.</span> Proovi vähemate või erinevate otsinguterminite ja filtritega. Tulemused %(from)s-%(to)s (%(total)s kokku) Kui leiate muid "varjuraamatukogusid", mida peaksime peegeldama, või kui teil on küsimusi, võtke meiega ühendust aadressil %(email)s. %(num)d osalised vasted %(num)d+ osalised vasted Sisestage kasti, et otsida faile digitaalsetest laenuraamatukogudest. Sisestage kasti, et otsida meie kataloogist %(count)s otse allalaaditavat faili, mida me <a %(a_preserve)s>säilitame igavesti</a>. Otsimiseks sisestage tekst kasti. Sisestage kasti, et otsida meie kataloogist %(count)s teadusartikleid ja ajakirjade artikleid, mida me <a %(a_preserve)s>säilitame igavesti</a>. Sisestage kasti, et otsida raamatukogude metaandmeid. See võib olla kasulik, kui <a %(a_request)s>taotlete faili</a>. Nipp: kasutage kiiremaks navigeerimiseks klaviatuuri otseteid “/” (otsingu fookus), “enter” (otsing), “j” (üles), “k” (alla), “<” (eelmine leht), “>” (järgmine leht). Need on metaandmete kirjed, <span %(classname)s>mitte</span> allalaaditavad failid. Otsingu seaded Otsi Digitaalne laenutus Laadi alla Teadusajakirjade artiklid Metaandmed Uus otsing %(search_input)s - Otsi Otsing võttis liiga kaua aega, mis tähendab, et tulemused võivad olla ebatäpsed. Mõnikord aitab lehe <a %(a_reload)s>uuesti laadimine</a>. Otsing võttis liiga kaua aega, mis on tavaline laiaulatuslike päringute puhul. Filtri arvud ei pruugi olla täpsed. Suuremate üleslaadimiste (üle 10 000 faili), mida Libgen või Z-Library ei aktsepteeri, võtke meiega ühendust aadressil %(a_email)s. Libgen.li puhul veenduge, et logite esmalt sisse <a %(a_forum)s >nende foorumisse</a> kasutajanimega %(username)s ja parooliga %(password)s, seejärel naaske nende <a %(a_upload_page)s >üleslaadimislehele</a>. Praegu soovitame uusi raamatuid üles laadida Library Genesis'i harudesse. Siin on <a %(a_guide)s>kasulik juhend</a>. Pange tähele, et mõlemad harud, mida me sellel veebisaidil indekseerime, tõmbavad samast üleslaadimissüsteemist. Väikeste üleslaadimiste jaoks (kuni 10 000 faili) laadige need palun üles nii %(first)s kui ka %(second)s. Teise võimalusena saate need üles laadida Z-Library <a %(a_upload)s>siin</a>. Akadeemiliste artiklite üleslaadimiseks laadige need lisaks Library Genesis'ile üles ka <a %(a_stc_nexus)s>STC Nexus'isse</a>. Nad on parim varjatud raamatukogu uute artiklite jaoks. Me pole neid veel integreerinud, kuid teeme seda mingil hetkel. Saate kasutada nende <a %(a_telegram)s>üleslaadimisbotti Telegramis</a> või võtke ühendust aadressiga, mis on loetletud nende kinnitatud sõnumis, kui teil on liiga palju faile, mida sel viisil üles laadida. <span %(label)s>Tõsine vabatahtlik töö (USD$50-USD$5,000 preemiad):</span> kui suudate pühendada palju aega ja/või ressursse meie missioonile, sooviksime teiega tihedamalt koostööd teha. Lõpuks võite liituda siseringi meeskonnaga. Kuigi meie eelarve on piiratud, suudame kõige intensiivsema töö eest anda <span %(bold)s>💰 rahalisi preemiaid</span>. <span %(label)s>Kerge vabatahtlik töö:</span> kui saate pühendada vaid paar tundi siin ja seal, on siiski palju viise, kuidas saate aidata. Premeerime järjepidevaid vabatahtlikke <span %(bold)s>🤝 liikmesustega Anna’s Archive’is</span>. Anna’s Archive tugineb sellistele vabatahtlikele nagu teie. Tervitame igasuguse pühendumise tasemega inimesi ja otsime abi kahes peamises kategoorias: Kui te ei saa oma aega vabatahtlikuks tööks pühendada, saate meid siiski palju aidata, <a %(a_donate)s>annetades raha</a>, <a %(a_torrents)s>külvates meie torrenteid</a>, <a %(a_uploading)s>üles laadides raamatuid</a> või <a %(a_help)s>rääkides oma sõpradele Anna arhiivist</a>. <span %(bold)s>Ettevõtted:</span> pakume kiiret otsest juurdepääsu meie kogudele vastutasuks ettevõtte tasemel annetuse või uute kogude vahetuse eest (nt uued skaneeringud, OCR’itud andmekogud, meie andmete rikastamine). <a %(a_contact)s>Võtke meiega ühendust</a>, kui see olete teie. Vaadake ka meie <a %(a_llm)s>LLM lehte</a>. Tööülesanded Otsime alati inimesi, kellel on tugevad programmeerimis- või ründeturbeoskused, et kaasa lüüa. Saate anda tõsise panuse inimkonna pärandi säilitamisse. Tänutäheks anname kindlate panuste eest liikmesuse. Suure tänutäheks anname rahalisi preemiaid eriti oluliste ja keeruliste ülesannete eest. Seda ei tohiks vaadelda kui töö asendajat, kuid see on lisastiimul ja võib aidata tekkivate kuludega. Enamik meie koodist on avatud lähtekoodiga ja me palume ka teie koodil olla avatud lähtekoodiga, kui preemiat antakse. On mõned erandid, mida saame arutada individuaalselt. Preemiad antakse esimesele inimesele, kes ülesande täidab. Võite vabalt kommenteerida preemiate piletit, et teised teaksid, et töötate millegi kallal, nii et teised võivad oodata või teiega ühendust võtta, et koostööd teha. Kuid olge teadlik, et teised võivad siiski ka selle kallal töötada ja proovida teid edestada. Siiski ei anna me preemiaid lohaka töö eest. Kui kaks kvaliteetset esitust tehakse üksteisele lähedal (päeva või kahe jooksul), võime oma äranägemisel otsustada anda preemiaid mõlemale, näiteks 100%% esimese esituse eest ja 50%% teise esituse eest (kokku 150%%). Suuremate preemiate (eriti andmete kogumise preemiate) puhul võtke meiega ühendust, kui olete lõpetanud ~5%% sellest ja olete kindel, et teie meetod skaleerub kogu verstapostini. Peate jagama oma meetodit meiega, et saaksime tagasisidet anda. Samuti saame sel viisil otsustada, mida teha, kui mitu inimest on preemiale lähedal, näiteks preemia andmine mitmele inimesele, inimeste julgustamine koostööd tegema jne. HOIATUS: kõrge preemiaga ülesanded on <span %(bold)s>rasked</span> — võib olla mõistlik alustada lihtsamatest. Minge meie <a %(a_gitlab)s>Gitlabi probleemide loendisse</a> ja sorteerige “Label priority” järgi. See näitab ligikaudu ülesannete järjekorda, mis meile korda lähevad. Ülesanded, millel pole selgesõnalisi preemiaid, on endiselt liikmesuse jaoks sobilikud, eriti need, mis on märgitud “Accepted” ja “Anna lemmik”. Võiksite alustada “Algaja projektiga”. Kerge vabatahtlik töö Nüüd on meil ka sünkroonitud Matrixi kanal aadressil %(matrix)s. Kui teil on paar tundi vaba aega, saate aidata mitmel viisil. Kindlasti liituge <a %(a_telegram)s>vabatahtlike vestlusega Telegramis</a>. Tänutäheks anname tavaliselt 6 kuud “Õnnelik Raamatukoguhoidja” staatust põhiliste verstapostide eest ja rohkem jätkuva vabatahtliku töö eest. Kõik verstapostid nõuavad kõrgekvaliteedilist tööd — lohakas töö teeb meile rohkem kahju kui kasu ja me lükkame selle tagasi. Palun <a %(a_contact)s>kirjutage meile</a>, kui jõuate verstapostini. %(links)s lingid või ekraanipildid täidetud taotlustest. Raamatute (või artiklite jne) taotluste täitmine Z-Library või Library Genesis foorumites. Meil pole oma raamatute taotlussüsteemi, kuid peegeldame neid raamatukogusid, seega nende paremaks muutmine teeb ka Anna arhiivi paremaks. Verstapost Ülesanne Sõltub ülesandest. Väikesed ülesanded, mis on postitatud meie <a %(a_telegram)s>vabatahtlike vestlusesse Telegramis</a>. Tavaliselt liikmelisuse eest, mõnikord väikeste preemiate eest. Väikesed ülesanded, mis on postitatud meie vabatahtlike vestlusgruppi. Kindlasti jätke kommentaar lahendatud probleemide kohta, et teised ei dubleeriks teie tööd. %(links)s lingid parandatud kirjetest. Võite kasutada <a %(a_list)s>juhuslike metadata probleemide loendit</a> lähtepunktina. Parandage metaandmeid, <a %(a_metadata)s>linkides</a> Open Library’ga. Need peaksid näitama, kuidas teavitad kedagi Anna Arhiivist ja nad tänavad sind. %(links)s lingid või ekraanipildid. Anna Arhiivi sõna levitamine. Näiteks, soovitades raamatuid AA-s, viidates meie blogipostitustele või suunates inimesi üldiselt meie veebisaidile. Tõlkige täielikult üks keel (kui see polnud juba peaaegu valmis). <a %(a_translate)s>Tõlkige</a> veebisait. Link redigeerimisajalukku, mis näitab, et olete teinud märkimisväärseid panuseid. Parandage Anna arhiivi Wikipedia lehte oma keeles. Lisage teavet AA Wikipedia lehelt teistes keeltes ning meie veebisaidilt ja blogist. Lisage viiteid AA-le teistel asjakohastel lehtedel. Vabatahtlik töö ja preemiad 