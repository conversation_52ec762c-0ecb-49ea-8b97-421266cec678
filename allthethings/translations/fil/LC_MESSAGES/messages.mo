��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ud �  Fg 0   �h   �h 1  j �  >l �  1n 4  �o U   �p q   Lq J   �q e   	r �  or �  lt 
  �u �  w   �x 8  �y   )| 7  +~   c� "  h� �  �� ?   }� �   �� b   �� /  	�    9� R  X� J   �� '   ��    � &   9� H   `� %   ��    ό +   � +   � 5   :�    p� F   �� M  ύ   � P   8� @   �� 
   ʐ    ؐ    �     �    �    %� 	   2�    <�    M� "   T�    w�    }� 	   �� 
   ��    ��    ��    ґ !   � �  � k  �� �   � 0   �� �  � �   �� &  "�   I� #   N� �   r� #   � �   @� (   � 7  -� #   e� �  �� /   I� e   y� 
   ߠ a  � �  L�   � �  �    �� M   �� n   � �   Q� 4   � :   T� s   �� R   � J   V� /   ��    Ѫ �   ֪    f� L   g� �   �� �   9� N   � P  _� 4   �� :  � �    � �   ޲ f   �� �   � �   Ӵ J   v�   �� �   ڶ n   �� A   -� �   o�    � �   � :  ¹ J   �� 
   H� �   S� �   $� 	   !� �  +� �   ޾   �� �  �� �   \� e  N�   �� ^   �� L   2� \   � �   �� w   `� q   �� 9   J� �   �� Z   � W   n�    ��    �� ]   ]� �   �� �   ��    7�   L�   ]� P  w� �  �� �   d� �   � Y  �� �   ;� �   � �   �� �  t� �   �   ��    �� �   �� �   �� �   �� �  o� �   �� �   �� �   �   �� �   �� O  f�   �� #   �� ^   �� �   J� N   �� �  0� �   �� t   �� �    � +   �� �  �� �   h� �  A�    �� w   �� �   [� �    �   �� �   �� �  �� �  �� *   �� �  �� �  P� F  %�   l� L  n $  � ^   � �   ? 	   � �   � �  � �  M   �	 1   �   H    � �  � V  �   � 
   � �   	 N  � �   ! g  � �   X T   T    � �  � �  �    /   D F  H s  � �     �   ! a   �" O   !#   q# 4   �$    �$ �  �$ 9  X& N  �' 4   �( J   ) l  a) �   �+ �   x, j   �, �  g- (   /    5/ �  N/ �   �0 �   �1 `   b2 �   �2 +   O3   {3 �   �4    �5 
   �5 -   �5 8   �5 6   6 >   L6 \   �6 �   �6    u7 4   �7 L   �7 T   8 $   l8 8   �8 -   �8 s   �8    l9    s9 2  �9 J  �:   < �   = �   > �   �> p  �?    
A �   A �   B �   �B y  �C �  E &  �F )  �H �  �J J   �L B   M -   FM �   tM N  0N �  P E  JR g  �S *  �T �  #V 4  �X :   �Y 5  Z [  M[ �  �\    C^ �   W^ [  =_ �   �` S  6a   �b    �c -   �c z   �c 	   Ad �  Kd �  g �  �h �   ]k �   "l    m   +m   ?n J   Ho q   �o 0  p 9  6q �   pr �  s �   �t @  0u    qv ?  �v �   �x (  py �  �{ 4  l~ �   �� 
   �   �� �  ��    f� h  }�   � W  � /  F� *  v� �  �� J   )�   t� �   x� �   n� J   � `   >�    �� :   �� "   � ,   
� !   :�    \� �   s� 9  A�   {� n  �� �   �� 	   �� 8   � ~  9� �  �� �  f� �  ;� �  � 
   ��   ��    ��    �� �  ȭ �  �� Z  5� �  �� 
   ,�   :� �  U�   E� �  X� (  ߿ �  � �  �� !   s� �   �� �   �� F  �    U� �   V� �   � �   �� �  Z�    �� �   � K   �� b   �    j� ;   ~� F   �� u  � �  w� �  %� ;   �   H� �   T�    �� �   �� E   �� �   �� �   �� )   J� x   t�    �� p  � �  t�   � �  2� ~   ��    o� ~   �� Q  �    U� �   \� �  �� �   �� :   �� ?   �� �  &� 0   �� �  �� �  �� �  G� %   D� �   j� �   �� �  }�    �    �   *� 4   9  �   n  �   ,   �    ]    0   f    � =   � �  � �  � �   S
   S ^   i I   � �   
 ]  �
 �  1   � �  � >   � �   * A    i  b �   � 5  � �  � 0   �   � ,   
!   7! g   F#    �# [  �# `  "& �  �(   C* U  I+ c  �, b  / �   f2   3    5 �   7 *  �7 b  �: :   5< �   p< �   = !   �=    �= k  �= �    ? @   �? 8  "@   [A 0  kC &  �D �  �F �   oH �  8I k  "K    �L �   �L    �M �  �M )   0O !   ZO    |O    �O >   �O    �O    �O    P    P    "P    4P    @P 	   FP 
   PP )   [P    �P .   �P    �P    �P    �P    �P �   �P l   �Q %   )R    OR '   `R -   �R )   �R .   �R %   S 	   5S    ?S    FS    VS    jS    S    �S    �S    �S    �S 3   �S    T    !T #   =T '   aT 2   �T )   �T    �T %   �T 1   "U    TU T   lU D   �U    V _   
V I   mV    �V    �V #   �V    
W    W    5W    RW    nW    �W    �W    �W    �W    �W 	   �W 
   �W    �W    �W    X    X 	   X    X 	   =X    GX    _X    eX 	   lX    vX    �X    �X    �X    �X    �X    �X    �X 	   Y    Y &   "Y    IY    RY '   [Y    �Y    �Y    �Y #   �Y    �Y    �Y    �Y l   
Z �   wZ k   ![ �   �[ �  Y\ w   Y^    �^    �^     _    _    $_    4_    L_ (   Y_ h   �_ =   �_ n   )` +   �` @   �` /   a z   5a r   �a   #b h   ,c M   �c /   �c    d     d 	   (d    2d    ;d    Ld    ad    fd    ud 
   ~d    �d    �d    �d    �d    �d    �d    �d    �d    e 	   e    #e 
   ,e    7e    Ue "  je    �f 	   �f    �f     �f    �f L   �f f   g .   ~g -   �g 8   �g    h    h    $h x   'h    �h    �h 2   �h �   �h    pi    �i z   �i )   j Z  Bj _   �m �   �m �   �n �   6o ?   /p +  op �   �q V  *r   �s    �t 
   �t A   �t F   �t V   4u i   �u j   �u N   `v r   �v &   "w .   Iw    xw    �w W   �w %   �w    x    x 
   1x    <x k   Zx 
   �x 0   �x P   y    Vy    ty Y   �y c   �y �  Hz    �{    �{ �   	|    �|    �|    �| $   �|    } 1   !} o  S} %   �~    �~    �~ 	    t   (   ��    ��    �� o   ��    /� 
   8� (   C�    l�    �� $   �� �   ��    \�    v� Y   ��    �    �    �    � 1   7� X   i�     =   ԃ �   � g   ք T   >� 
   �� '  �� S   Ɇ    � A   5�    w� �   �� �   h� "   '� I   J� �   �� �   8� "   *�     M�    n� �  �� ,   B� &   o�     �� %   �� )   ݍ �   �    ��    Ў 0   � D    �    e�    v�    �� *   �� �  ޏ �  d� 4  � H   %� 3   n�    �� %   �� ;  Ֆ �   � �   �    �� �   ҙ �  Ś    ��    �� v  �� S  6�    ��    �� 2   ��    ֠ f  �    N� J  k� �  ��    g�    h� �   � 9   � #   I� u   m� Q  � +  5� �   a� �  � y   Ƭ 0  @� e   q� �   ׮ �   �� f   [� �   ° .   �� $   ��    �    ��    � %   !�     G� J   h� 3   �� 	   � 3   � I  %� :   o�   �� �   �� �   V� $   �    �    3�    N� -   a�    �� (   �� +   ׷ �   � /   �� �   ,�    �� �   � �   �� �   v� W  �� �   O� -   0� �   ^� 	   �� T  � �   V� !   �� �  �    ��    ��    � #   � -   =�    k�    p� d   w� �   �� F  ��    ��    �    � �   (� O  � �   Q� �   �    ��    ��    ��    ��    ��    �    "� M   *� 3   x� P  �� g   ��    e� l   v� z   �� _   ^� y   �� c   8� b   ��    �� s   � ]   |� �   �� b   x� Z   ��    6�   G� k   I� 9   �� i   �� i   Y� <   ��     � 3   	� t   =� �   �� P   G� �   �� 
   M� *  X� M   �� o   �� �   A�    ��   �� �   �    �    �    7�    >� !  E� >   g� h   �� �   � �   �� �   �� 
  q� �   |� �   1� �   �� l   �� =  T� |   �� �  � �   ��   �� 
   �� 
   �� 
   �� �   �� 
   u� 
   �� Z   �� e   �� �   R� 
   @� �   N� Q   �� �   &� 8   �� �   �� o   s� 
   �� �   �� 
   �� 
   �� 
   �� L  �� �   �� �  ��    }�    ��    �� �   ��    h� *   ��   �� �   ��    _�    }�    �� >   �� >   �� 4   (�    ]�    |�   ��    �� �  �� )  �� ~   �� ]   2� �   ��   B�   F  e  J �   � �   f �   �    z �  �    ? �  ] �   �	 >  �
 �    a  � �    �   � �   x 4    :   :    u <   � 
   �    �    �   �     t   
 7   � 
   �    �    � &   � �   � L   � :   � 
   8       W    ` "   f %   �    �    �    �    �    �    �    �    � 3    �   7    �    �            $    4    E    X    i    � '   � m   �    0 5   B m   x �   � �   � �   � !  m �   � d  p    � �   � B   � e   � W   )  �   �    -! N   /" h   ~"    �" a   �" p   [# �   �#    d$     k$    �$    �$ 	   �$    �$    �$    �$    �$    %    %    -%    H%    `%    v%    �%    �%    �%    �%     �%    �% (   �% &   & �   8& �   �& 1   d' '   �' l   �' ]   +( t   �( /   �( 9   .) 0   h) :   �) M   �) -   "* �   P* <  8+ :  u,    �- K   �- �   . )   �.   �. �   �/ �   �0 T   l1    �1 �   �1 �   �2 w   N3 0   �3 y   �3   q4    �5 5   �5    �5 C   �5 �   86 �   �6    R7    Z7    c7 *   j7 �   �7 ;   -8 #   i8 >   �8 *   �8 !   �8     9 H   :9 "   �9 {   �9 H   ":   k: [   �; c   �; �   I< F   = $   d= #   �= $   �= #   �= $   �= #   > $   ?> E   d> G   �> �  �> G   �A 9   �A A   
B )   OB 
   yB �   �B �   C �   �C �   {D 
   6E �   AE !   �E    �E �   	F     �F *   
G @   5G 3   vG Z   �G ]   H U   cH    �H #   �H �   �H 5   �I '   �I g   "J   �J D   �K �   �K p   �L a   �L �   XM )   �M ;   N �   LN #   �N q   O "   zO �   �O ;   jP A   �P    �P #   Q �   %Q @   R H   ]R �   �R j   LS J   �S �   T �   �T 
    U    +U U   ;U @   �U    �U $   �U    V    !V    3V /   DV �   tV l   W    tW )   �W .   �W *   �W )   X �   =X =   �X �   Y U   �Y .   �Y p   Z   �Z 2   �[ a   �[    &\ G   >\ G   �\    �\ }   �\ �   d] /   �] Z   ^    s^ 2   �^ a   �^    _ {   5_ (   �_    �_ +   �_ l   `   �` E   �a    �a    �a E   b    Hb V   _b A   �b �   �b �   �c    Kd ;   ad �   �d "   me �   �e K   Xf '   �f g   �f   4g 	   Nh    Xh    Zh    \h +   rh 6   �h L   �h    "i    =i    Ni (   Wi B   �i >   �i    j X   	j H   bj    �j    �j <   �j    k    5k n   Mk   �k R   �l 
   m 
   "m �   0m t  �m Y   ko    �o �  �o �  iq -   .s o   \s     �s �  �s 3   v v   �v    *w    Bw C  Zw "   �y ]   �y m   z f   �z d   �z    Y{ d   w{ I   �{ #   &| w   J| E   �| 2   } \   ;} E   �} R   �} �   1~ �   �~ 6   � �   � g  �� �   �� 1   Ƃ �  �� (  � �   �� >  �� �   �� -   ��    ш �   � 4   �� 5  Չ }   � K   ��    Ռ    � �  �    �� �   �� Q  � L  7� :  �� H   �� a   � i   j� 9   Ԕ :   � a   I� _   ��    � )   � E   B�    ��    �� :    j   �� 0   h� 
   �� u   �� 	  � �   $�    ��    ֙    � A   � `   0� d   �� �   �� w   Ǜ    ?� 1   Q� 
  �� 
   �� �   �� �  K�   C� _   [� 5   ��    �    ��    �� M   � 3   O� �   �� 6  � m   S�    ��    ԥ %   � !   
� e   /�    �� ?   ��    � +   � '   �    ?�    Z� {   a�    ݧ    � %   ��    $�    B� �   F� �   ˨ a   �� |   � b   �� �   �    ȫ    ѫ �   � �   � �   ֭ 
   �� �   �� a   J� L   �� p   ��    j� T   �    ?� x   X�    ѱ    �    �    �    2�    I�    f�    |� 
   ��    �� 1   �� 1   � 1   � )   K� 6   u� /   ��    ܳ    ��    � =   5� "   s� 	   �� P   �� 9   � j   +� -   ��    ĵ    ڵ %   �� !   �    @� ~   Y� k   ض �   D�    Է    ո    �     �    0� F   F� 	   ��    ��    �� z   ��    :� !   X�    z� 
   �� 	   �� D   ��    ۺ   ��    �    /�    F� #   Y� '   }�     �� (   Ƽ *   � .   � 1   I� :   {�    �� Z   �� )   �    B�    ^� 8   o� R   �� +   �� 1   '� *   Y� s   �� `   �� Z   Y�    �� '   �� 	   ��    ��    �� (   � �  D� �   ��    W�    o� 3   s�    �� (   ��    ��    �� �   �� =   u� �   ��    �� !   �� �   �� !   i�    �� !   �� $   �� -   �� #   �    B�    \� 9   l�     �� ,   �� =   �� @  2� <   s� J   �� U   ��    Q� �   o� &   �    C� g   S�    �� )   �� !   �� Q   � <   n� "   �� �   ��    u�    ��    ��    ��    ��    ��    ��    �    3� j   O�   �� $   �� '   �� �   � �   �    ��    ��    ��    �    �    /�    B�    [�    p�    ��    ��    �� 
   ��    ��    ��    ��    ��    �� %   
�   3� 0  5� �  f� Y  � �  \� �   2� �  2� 
   �� >  ��    � 
  #� �   .� �  � �   �� �  �� 9   O� �   �� H   :�     �� N   �� Z   �� t   N� y   �� �   =� �   �� �   �� �  ��    F� �   _� �   /� i   � �   ��    >� �   Y� �  D� �   �� �   ��    �� t   �� �   &� �   �� �   �� r   =� �   ��    5� -   J�    x� G   �� <   ��    � �   -� M   �� �   � %   �� �   �� �   ]� G   � d   [� _   �� y    � f   �� ^   � �   `� s   �� �   b� �   ��    v  +   }  '   �  r   �  3   D    x     h   �    �        " A   * :   l :   �    �    � 
        	    ]   # ~   � d     8   e    � .   � 3   �    	     
   & 	   1    ;    C    L    S    _    d    s    � 
   �    �    �    � 
   �    �    � 
   �         #   ; �   _    � i   � �   a    ?    K    X    j    v    ~    � �   � �   0	 H   �	    
    
 |   3
    �
 �   �
 �   y *       2 �   K    � z   �
 �   ^ �   N /   � �       �    � N   � �   : !   � �   � �   � �   @ X       ]    w    � 
   �    �    �    �    � �   � |   � �    �   �   � }   � ^    �  c �  P   � �    N  � _    
   v! �   �! O  N" �   �# �  k$   3' �   H) �  �)    ]+ J   |+ �   �+ �  _, A   . 
  X. 	   c/    m/    t/ �   �/ B   (0 f   k0 4   �0 m   1 Y   u1 v   �1     F2 �   g2 V   <3 -   �3 N   �3 �   4    5  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: fil
Language-Team: fil <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Ang Z-Library ay isang popular (at ilegal) na aklatan. Kinuha nila ang koleksyon ng Library Genesis at ginawa itong madaling mahanap. Bukod pa rito, naging napaka-epektibo sila sa paghingi ng mga bagong kontribusyon ng libro, sa pamamagitan ng pagbibigay ng mga insentibo sa mga gumagamit na nag-aambag ng iba't ibang mga benepisyo. Sa kasalukuyan, hindi nila ibinabalik ang mga bagong aklat na ito sa Library Genesis. At hindi tulad ng Library Genesis, hindi nila ginagawang madaling i-mirror ang kanilang koleksyon, na pumipigil sa malawakang pangangalaga. Mahalaga ito sa kanilang modelo ng negosyo, dahil naniningil sila ng pera para sa pag-access sa kanilang koleksyon nang maramihan (higit sa 10 libro bawat araw). Hindi kami gumagawa ng mga moral na paghatol tungkol sa pagsingil ng pera para sa maramihang pag-access sa isang ilegal na koleksyon ng libro. Walang duda na naging matagumpay ang Z-Library sa pagpapalawak ng access sa kaalaman, at sa pagkuha ng mas maraming libro. Narito lamang kami upang gampanan ang aming bahagi: tiyakin ang pangmatagalang pag-iingat ng pribadong koleksyong ito. - Anna at ang koponan (<a %(reddit)s>Reddit</a>) Sa orihinal na paglabas ng Pirate Library Mirror (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), gumawa kami ng mirror ng Z-Library, isang malaking ilegal na koleksyon ng libro. Bilang paalala, ito ang isinulat namin sa orihinal na blog post na iyon: Ang koleksyong iyon ay nagsimula noong kalagitnaan ng 2021. Samantala, ang Z-Library ay lumago sa isang nakamamanghang bilis: nagdagdag sila ng humigit-kumulang 3.8 milyong bagong libro. May ilang mga duplicate doon, sigurado, ngunit ang karamihan nito ay tila mga lehitimong bagong libro, o mas mataas na kalidad na mga scan ng mga naunang isinumiteng libro. Ito ay sa malaking bahagi dahil sa pagtaas ng bilang ng mga boluntaryong moderator sa Z-Library, at ang kanilang bulk-upload system na may deduplication. Nais naming batiin sila sa mga tagumpay na ito. Masaya kaming ipahayag na nakuha namin ang lahat ng mga libro na idinagdag sa Z-Library sa pagitan ng aming huling salamin at Agosto 2022. Bumalik din kami at kinuha ang ilang mga libro na hindi namin nakuha sa unang pagkakataon. Sa kabuuan, ang bagong koleksyong ito ay humigit-kumulang 24TB, na mas malaki kaysa sa huli (7TB). Ang aming salamin ay ngayon ay 31TB sa kabuuan. Muli, nag-deduplicate kami laban sa Library Genesis, dahil mayroon nang mga torrent na magagamit para sa koleksyong iyon. Mangyaring pumunta sa Pirate Library Mirror upang tingnan ang bagong koleksyon (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>). Mayroong higit pang impormasyon doon tungkol sa kung paano nakaayos ang mga file, at kung ano ang nagbago mula noong huli. Hindi namin ito ililink mula rito, dahil ito ay isang blog website lamang na hindi nagho-host ng anumang ilegal na materyales. Siyempre, ang pag-seed ay isa ring mahusay na paraan upang matulungan kami. Salamat sa lahat ng nagse-seed ng aming nakaraang set ng mga torrent. Kami ay nagpapasalamat sa positibong tugon, at masaya na maraming tao ang nagmamalasakit sa pag-iingat ng kaalaman at kultura sa hindi pangkaraniwang paraang ito. 3x bagong mga libro ang idinagdag sa Pirate Library Mirror (+24TB, 3.8 milyong libro) Basahin ang mga kasamang artikulo ng TorrentFreak: <a %(torrentfreak)s>una</a>, <a %(torrentfreak_2)s>ikalawa</a> - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) mga kasamang artikulo ng TorrentFreak: <a %(torrentfreak)s>una</a>, <a %(torrentfreak_2)s>ikalawa</a> Hindi pa matagal na ang nakalipas, ang mga “shadow-libraries” ay nawawala na. Ang Sci-Hub, ang napakalaking ilegal na archive ng mga akademikong papel, ay tumigil sa pagtanggap ng mga bagong gawa, dahil sa mga kaso. Ang “Z-Library”, ang pinakamalaking ilegal na aklatan ng mga libro, ay nakita ang mga umano'y tagalikha nito na inaresto sa mga kasong kriminal na copyright. Sila ay hindi kapani-paniwalang nakatakas sa kanilang pag-aresto, ngunit ang kanilang aklatan ay hindi pa rin ligtas sa banta. Ang ilang mga bansa ay gumagawa na ng bersyon nito. Iniulat ng TorrentFreak <a %(torrentfreak)s>na ang Tsina at Japan ay nagpakilala ng mga AI exception sa kanilang mga batas sa copyright. Hindi malinaw sa amin kung paano ito nakikipag-ugnayan sa mga internasyonal na kasunduan, ngunit tiyak na nagbibigay ito ng proteksyon sa kanilang mga domestic na kumpanya, na nagpapaliwanag sa aming nakikita. Tungkol sa Arkibo ni Anna — ipagpapatuloy namin ang aming underground na gawain na nakaugat sa moral na paniniwala. Gayunpaman, ang aming pinakamalaking hangarin ay makapasok sa liwanag, at palakasin ang aming epekto nang legal. Mangyaring i-reporma ang copyright. Nang naharap sa pagsasara ang Z-Library, na-backup ko na ang buong aklatan nito at naghahanap ng plataporma para ilagay ito. Iyon ang aking motibasyon para simulan ang Arkibo ni Anna: isang pagpapatuloy ng misyon sa likod ng mga naunang inisyatiba. Mula noon, lumaki kami upang maging pinakamalaking shadow library sa mundo, na nagho-host ng higit sa 140 milyong copyrighted na teksto sa iba't ibang format — mga libro, akademikong papel, magasin, pahayagan, at iba pa. Ang aking koponan at ako ay mga ideologo. Naniniwala kami na ang pagpepreserba at pagho-host ng mga file na ito ay moral na tama. Ang mga aklatan sa buong mundo ay nakakaranas ng pagbawas sa pondo, at hindi rin natin maaasahan ang pamana ng sangkatauhan sa mga korporasyon. Pagkatapos ay dumating ang AI. Halos lahat ng pangunahing kumpanya na bumubuo ng LLMs ay nakipag-ugnayan sa amin upang sanayin sa aming data. Karamihan (ngunit hindi lahat!) ng mga kumpanyang nakabase sa US ay muling nag-isip nang mapagtanto nila ang ilegal na kalikasan ng aming gawain. Sa kabaligtaran, ang mga kumpanyang Tsino ay masigasig na tinanggap ang aming koleksyon, na tila hindi nababahala sa legalidad nito. Ito ay kapansin-pansin na ibinigay ang papel ng Tsina bilang isang signatory sa halos lahat ng pangunahing internasyonal na kasunduan sa copyright. Nagbigay kami ng high-speed access sa humigit-kumulang 30 kumpanya. Karamihan sa kanila ay mga kumpanya ng LLM, at ang ilan ay mga data broker, na muling ibebenta ang aming koleksyon. Karamihan ay Tsino, bagaman nakipagtulungan din kami sa mga kumpanya mula sa US, Europa, Russia, South Korea, at Japan. Inamin ng DeepSeek <a %(arxiv)s>na ang isang mas maagang bersyon ay sinanay sa bahagi ng aming koleksyon, bagaman sila ay tahimik tungkol sa kanilang pinakabagong modelo (marahil ay sinanay din sa aming data). Kung nais ng Kanluran na manatiling nangunguna sa karera ng LLMs, at sa huli, AGI, kailangan nitong muling isaalang-alang ang posisyon nito sa copyright, at sa lalong madaling panahon. Kung sumasang-ayon ka man sa amin o hindi sa aming moral na kaso, ito ay nagiging isang kaso ng ekonomiya, at maging ng pambansang seguridad. Ang lahat ng mga power bloc ay nagtatayo ng mga artipisyal na super-siyentipiko, super-hacker, at super-militar. Ang kalayaan ng impormasyon ay nagiging usapin ng kaligtasan para sa mga bansang ito — maging usapin ng pambansang seguridad. Ang aming koponan ay mula sa iba't ibang bahagi ng mundo, at wala kaming partikular na pagkakahanay. Ngunit hinihikayat namin ang mga bansang may malalakas na batas sa copyright na gamitin ang banta na ito upang i-reporma ang mga ito. Kaya ano ang dapat gawin? Ang aming unang rekomendasyon ay simple: paikliin ang termino ng copyright. Sa US, ang copyright ay ipinagkaloob sa loob ng 70 taon pagkatapos ng kamatayan ng may-akda. Ito ay katawa-tawa. Maaari nating iayon ito sa mga patent, na ipinagkaloob sa loob ng 20 taon pagkatapos ng pag-file. Ito ay dapat na higit pa sa sapat na oras para sa mga may-akda ng mga libro, papel, musika, sining, at iba pang malikhaing gawa, upang ganap na mabayaran para sa kanilang mga pagsisikap (kabilang ang mas mahabang proyekto tulad ng mga adaptasyon ng pelikula). Pagkatapos, sa pinakamababa, dapat isama ng mga gumagawa ng patakaran ang mga carve-out para sa mass-preservation at dissemination ng mga teksto. Kung ang nawalang kita mula sa mga indibidwal na customer ang pangunahing alalahanin, ang personal-level na pamamahagi ay maaaring manatiling ipinagbabawal. Sa turn, ang mga may kakayahang pamahalaan ang malalaking repositoryo — mga kumpanyang nagsasanay ng LLMs, kasama ang mga aklatan at iba pang mga archive — ay sakop ng mga pagbubukod na ito. Kailangan ang reporma sa copyright para sa pambansang seguridad TL;DR: Ang mga Chinese LLM (kasama ang DeepSeek) ay sinanay sa aking ilegal na archive ng mga libro at papel — ang pinakamalaki sa mundo. Kailangang baguhin ng Kanluran ang batas sa copyright bilang usapin ng pambansang seguridad. Mangyaring tingnan ang <a %(all_isbns)s>orihinal na blog post</a> para sa karagdagang impormasyon. Nagbigay kami ng hamon upang mapabuti ito. Magbibigay kami ng gantimpala sa unang puwesto ng $6,000, pangalawang puwesto ng $3,000, at pangatlong puwesto ng $1,000. Dahil sa napakaraming tugon at kamangha-manghang mga pagsusumite, nagpasya kaming bahagyang taasan ang premyo, at magbigay ng apat na gantimpala sa pangatlong puwesto ng tig-$500 bawat isa. Ang mga nagwagi ay nasa ibaba, ngunit siguraduhing tingnan ang lahat ng pagsusumite <a %(annas_archive)s>dito</a>, o i-download ang aming <a %(a_2025_01_isbn_visualization_files)s>pinagsamang torrent</a>. Unang puwesto $6,000: phiresky Ang <a %(phiresky_github)s>pagsusumiteng</a> ito (<a %(annas_archive_note_2951)s>komento sa Gitlab</a>) ay talagang lahat ng aming hinahanap, at higit pa! Partikular naming nagustuhan ang napaka-flexible na mga opsyon sa visualization (kahit na sumusuporta sa custom shaders), ngunit may komprehensibong listahan ng mga preset. Nagustuhan din namin kung gaano kabilis at maayos ang lahat, ang simpleng implementasyon (na wala pang backend), ang matalinong minimap, at malawak na paliwanag sa kanilang <a %(phiresky_github)s>blog post</a>. Kamangha-manghang trabaho, at karapat-dapat na nagwagi! - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Puno ng pasasalamat ang aming mga puso. Mga kapansin-pansing ideya Mga Gusali para sa Kakaibang mga Aklat Maraming sliders para ikumpara ang datasets, na parang ikaw ay isang DJ. Scale bar na may bilang ng mga aklat. Magagandang label. Cool na default na color scheme at heatmap. Natatanging pagtingin sa mapa at mga filter Mga Anotasyon, at pati na rin mga live na istatistika Mga Live na Istatistika Ilan pang mga ideya at implementasyon na partikular naming nagustuhan: Pwede pa sana kaming magpatuloy, pero itigil na natin dito. Siguraduhing tingnan ang lahat ng mga isinumite <a %(annas_archive)s>dito</a>, o i-download ang aming <a %(a_2025_01_isbn_visualization_files)s>pinagsamang torrent</a>. Napakaraming isinumite, at bawat isa ay nagdadala ng natatanging pananaw, maging sa UI o implementasyon. Isasama namin ang unang puwesto na isinumite sa aming pangunahing website, at marahil ang iba pa. Nagsimula na rin kaming mag-isip kung paano ayusin ang proseso ng pagtukoy, pagkumpirma, at pagkatapos ay pag-archive ng mga pinakabihirang aklat. Marami pang darating sa bahaging ito. Salamat sa lahat ng lumahok. Kamangha-mangha na maraming tao ang nagmamalasakit. Madaling pag-toggle ng datasets para sa mabilisang paghahambing. Lahat ng ISBN CADAL SSNOs Pagtagas ng data ng CERLALC DuXiu SSIDs eBook Index ng EBSCOhost Google Books Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Mga file sa Arkibo ni Anna Nexus/STC OCLC/Worldcat OpenLibrary Russian State Library Imperial Library of Trantor Pangalawang puwesto $3,000: hypha “Habang ang mga perpektong parisukat at parihaba ay matematikal na kaaya-aya, hindi sila nagbibigay ng mas mahusay na locality sa isang mapping context. Naniniwala ako na ang asymmetry na likas sa mga Hilbert o classic Morton ay hindi isang depekto kundi isang tampok. Tulad ng kilalang hugis-bota ng Italy na agad na nakikilala sa isang mapa, ang natatanging "quirks" ng mga kurba na ito ay maaaring magsilbing cognitive landmarks. Ang natatanging ito ay maaaring mapahusay ang spatial memory at makatulong sa mga gumagamit na mag-orient sa kanilang sarili, na posibleng gawing mas madali ang paghahanap ng mga tiyak na rehiyon o pag-obserba ng mga pattern.” Isa pang kamangha-manghang <a %(annas_archive_note_2913)s>pagsusumite</a>. Hindi kasing flexible ng unang puwesto, ngunit mas nagustuhan namin ang macro-level na visualization nito kaysa sa unang puwesto (space-filling curve, borders, labeling, highlighting, panning, at zooming). Isang <a %(annas_archive_note_2971)s>komento</a> ni Joe Davis ang umantig sa amin: At marami pa ring mga opsyon para sa visualization at rendering, pati na rin ang isang napaka-smooth at intuitive na UI. Isang matibay na pangalawang puwesto! - Anna at ang koponan (<a %(reddit)s>Reddit</a>) Ilang buwan na ang nakalipas, inihayag namin ang isang <a %(all_isbns)s>$10,000 bounty</a> upang makagawa ng pinakamahusay na posibleng visualization ng aming data na nagpapakita ng ISBN space. Binibigyang-diin namin ang pagpapakita kung aling mga file ang na-archive na namin/hindi pa, at kalaunan ay isang dataset na naglalarawan kung gaano karaming mga aklatan ang may hawak ng ISBNs (isang sukatan ng bihira). Kami ay labis na nasiyahan sa tugon. Napakaraming pagkamalikhain. Isang malaking pasasalamat sa lahat ng lumahok: ang inyong enerhiya at sigasig ay nakakahawa! Sa huli, nais naming sagutin ang mga sumusunod na tanong: <strong>alinsunod na mga libro ang umiiral sa mundo, ilan na ang na-archive namin, at aling mga libro ang dapat naming pagtuunan ng pansin sa susunod?</strong> Nakakatuwang makita na maraming tao ang nagmamalasakit sa mga tanong na ito. Nagsimula kami sa isang pangunahing biswal na representasyon. Sa mas mababa sa 300kb, ang larawang ito ay maikli ngunit malinaw na kumakatawan sa pinakamalaking ganap na bukas na “listahan ng mga libro” na kailanman ay naipon sa kasaysayan ng sangkatauhan: Pangatlong puwesto $500 #1: maxlion Sa <a %(annas_archive_note_2940)s>pagsusumiteng</a> ito, talagang nagustuhan namin ang iba't ibang uri ng mga view, partikular ang paghahambing at mga view ng publisher. Pangatlong puwesto $500 #2: abetusk Bagaman hindi ang pinaka-pulidong UI, ang <a %(annas_archive_note_2917)s>pagsusumiteng</a> ito ay nakakatugon sa maraming pamantayan. Partikular naming nagustuhan ang tampok na paghahambing nito. Pangatlong puwesto $500 #3: conundrumer0 Tulad ng unang puwesto, ang <a %(annas_archive_note_2975)s>pagsusumiteng</a> ito ay humanga sa amin sa kanyang flexibility. Sa huli, ito ang gumagawa ng isang mahusay na tool sa visualization: pinakamataas na flexibility para sa mga power user, habang pinapanatili itong simple para sa mga karaniwang gumagamit. Pangatlong puwesto $500 #4: charelf Ang huling <a %(annas_archive_note_2947)s>pagsusumite</a> na nakakuha ng gantimpala ay medyo basic, ngunit may ilang natatanging tampok na talagang nagustuhan namin. Nagustuhan namin kung paano nila ipinapakita kung gaano karaming mga datasets ang sumasaklaw sa isang partikular na ISBN bilang sukatan ng kasikatan/katiyakan. Talagang nagustuhan din namin ang pagiging simple ngunit epektibo ng paggamit ng opacity slider para sa mga paghahambing. Mga nanalo ng $10,000 ISBN visualization bounty TL;DR: Nakakuha kami ng ilang kamangha-manghang mga pagsusumite sa $10,000 ISBN visualization bounty. Background Paano makakamit ng Arkibo ni Anna ang misyon nitong i-backup ang lahat ng kaalaman ng sangkatauhan, nang hindi nalalaman kung aling mga libro ang naroon pa? Kailangan namin ng isang TODO list. Isang paraan upang i-mapa ito ay sa pamamagitan ng mga numero ng ISBN, na mula noong 1970s ay itinalaga sa bawat librong nailathala (sa karamihan ng mga bansa). Walang sentral na awtoridad na nakakaalam ng lahat ng mga pagtatalaga ng ISBN. Sa halip, ito ay isang distributed system, kung saan ang mga bansa ay nakakakuha ng mga saklaw ng numero, na pagkatapos ay nag-aassign ng mas maliliit na saklaw sa mga pangunahing publisher, na maaaring higit pang mag-sub-divide ng mga saklaw sa mga minor publisher. Sa wakas, ang mga indibidwal na numero ay itinalaga sa mga libro. Sinimulan naming i-map ang mga ISBN <a %(blog)s>dalawang taon na ang nakalipas</a> sa aming pag-scrape ng ISBNdb. Mula noon, marami pa kaming na-scrape na mga pinagmumulan ng metadata, tulad ng <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, at marami pa. Ang buong listahan ay makikita sa mga pahina ng “Datasets” at “Torrents” sa Arkibo ni Anna. Ngayon, mayroon na kaming pinakamalaking ganap na bukas, madaling mada-download na koleksyon ng metadata ng libro (at sa gayon ay mga ISBN) sa mundo. Kami ay <a %(blog)s>sumulat nang malawakan</a> tungkol sa kung bakit mahalaga sa amin ang pagpepreserba, at kung bakit kami ay kasalukuyang nasa isang kritikal na panahon. Dapat na naming tukuyin ang mga bihira, hindi masyadong napagtutuunan, at natatanging nasa panganib na mga libro at i-preserba ang mga ito. Ang pagkakaroon ng magandang metadata sa lahat ng mga libro sa mundo ay nakakatulong dito. $10,000 gantimpala Malakas na konsiderasyon ang ibibigay sa usability at kung gaano ito kaganda. Ipakita ang aktwal na metadata para sa mga indibidwal na ISBN kapag nag-zoom in, tulad ng pamagat at may-akda. Mas mahusay na space-filling curve. Halimbawa, isang zig-zag, mula 0 hanggang 4 sa unang hilera at pagkatapos ay pabalik (pabaligtad) mula 5 hanggang 9 sa pangalawang hilera — na inilapat nang recursive. Iba't ibang o nako-customize na mga scheme ng kulay. Espesyal na mga view para sa paghahambing ng mga datasets. Mga paraan para i-debug ang mga isyu, tulad ng ibang metadata na hindi magkatugma (hal. napakalayo ng mga pamagat). Paglalagay ng anotasyon sa mga larawan gamit ang mga komento sa mga ISBN o saklaw. Anumang heuristics para matukoy ang mga bihira o nanganganib na mga libro. Anumang malikhaing ideya na maaari mong maisip! Code Ang code para makabuo ng mga larawang ito, pati na rin ang iba pang mga halimbawa, ay matatagpuan sa <a %(annas_archive)s>directory na ito</a>. Naisip namin ang isang compact na format ng data, kung saan ang lahat ng kinakailangang impormasyon ng ISBN ay humigit-kumulang 75MB (compressed). Ang paglalarawan ng format ng data at code para makabuo nito ay matatagpuan <a %(annas_archive_l1244_1319)s>dito</a>. Para sa bounty, hindi mo kinakailangang gamitin ito, ngunit ito marahil ang pinaka-maginhawang format para makapagsimula. Maaari mong baguhin ang aming metadata sa anumang paraan na gusto mo (bagaman lahat ng iyong code ay kailangang open source). Hindi na kami makapaghintay na makita kung ano ang iyong maiisip. Good luck! I-fork ang repo na ito, at i-edit ang HTML ng blog post na ito (walang ibang backends bukod sa aming Flask backend ang pinapayagan). Gawing maayos na na-zoom ang larawan sa itaas, upang maaari kang mag-zoom hanggang sa mga indibidwal na ISBN. Ang pag-click sa mga ISBN ay dapat magdala sa iyo sa isang metadata page o paghahanap sa Arkibo ni Anna. Dapat mo pa ring magawang lumipat sa pagitan ng lahat ng iba't ibang datasets. Ang mga saklaw ng bansa at saklaw ng publisher ay dapat i-highlight kapag naka-hover. Maaari mong gamitin halimbawa ang <a %(github_xlcnd_isbnlib)s>data4info.py sa isbnlib</a> para sa impormasyon ng bansa, at ang aming “isbngrp” na scrape para sa mga publisher (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Dapat itong gumana nang maayos sa desktop at mobile. Maraming dapat tuklasin dito, kaya't inihahayag namin ang isang gantimpala para sa pagpapabuti ng visualization sa itaas. Hindi tulad ng karamihan sa aming mga gantimpala, ang isang ito ay may takdang oras. Kailangan mong <a %(annas_archive)s>isumite</a> ang iyong open source code bago ang 2025-01-31 (23:59 UTC). Ang pinakamahusay na pagsusumite ay makakakuha ng $6,000, ang pangalawang lugar ay $3,000, at ang pangatlong lugar ay $1,000. Ang lahat ng mga gantimpala ay igagawad gamit ang Monero (XMR). Nasa ibaba ang mga minimal na pamantayan. Kung walang pagsusumite na nakakatugon sa mga pamantayan, maaari pa rin kaming magbigay ng ilang mga gantimpala, ngunit ito ay nasa aming pagpapasya. Para sa mga bonus na puntos (ito ay mga ideya lamang — hayaang magpatuloy ang iyong pagkamalikhain): Maaari kang ganap na lumihis mula sa minimal na pamantayan, at gumawa ng ganap na naiibang visualization. Kung ito ay talagang kamangha-mangha, kwalipikado ito para sa bounty, ngunit nasa aming pagpapasya. Gumawa ng mga submission sa pamamagitan ng pag-post ng komento sa <a %(annas_archive)s>isyu na ito</a> na may link sa iyong forked na repo, merge request, o diff. - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ang larawang ito ay 1000×800 pixels. Bawat pixel ay kumakatawan sa 2,500 ISBNs. Kung mayroon kaming file para sa isang ISBN, ginagawa naming mas berde ang pixel na iyon. Kung alam naming naibigay na ang isang ISBN, ngunit wala kaming katugmang file, ginagawa naming mas pula ito. Sa mas mababa sa 300kb, ang larawang ito ay malinaw na kumakatawan sa pinakamalaking ganap na bukas na “listahan ng mga aklat” na kailanman ay naipon sa kasaysayan ng sangkatauhan (ilang daang GB na naka-compress nang buo). Ipinapakita rin nito: marami pang trabaho ang natitira sa pag-backup ng mga aklat (mayroon lamang kaming 16%). Pagpapakita ng Lahat ng ISBN — $10,000 gantimpala sa 2025-01-31 Ang larawang ito ay kumakatawan sa pinakamalaking ganap na bukas na “listahan ng mga aklat” na kailanman ay naipon sa kasaysayan ng sangkatauhan. Pagpapakita Bukod sa pangkalahatang imahe, maaari rin nating tingnan ang mga indibidwal na datasets na nakuha natin. Gamitin ang dropdown at mga button upang lumipat sa pagitan ng mga ito. Maraming mga kawili-wiling pattern na makikita sa mga larawang ito. Bakit may ilang regularidad ng mga linya at bloke, na tila nangyayari sa iba't ibang sukat? Ano ang mga bakanteng lugar? Bakit ang ilang mga datasets ay napaka-clustered? Iiwan namin ang mga tanong na ito bilang isang ehersisyo para sa mambabasa. - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklusyon Sa pamantayang ito, maaari tayong gumawa ng mga paglabas nang mas paunti-unti, at mas madaling magdagdag ng mga bagong pinagmumulan ng data. Mayroon na kaming ilang kapana-panabik na mga paglabas sa pipeline! Umaasa rin kami na magiging mas madali para sa ibang mga shadow library na i-mirror ang aming mga koleksyon. Pagkatapos ng lahat, ang aming layunin ay mapanatili ang kaalaman at kultura ng tao magpakailanman, kaya't mas maraming redundancy, mas mabuti. Halimbawa Tingnan natin ang aming kamakailang paglabas ng Z-Library bilang halimbawa. Ito ay binubuo ng dalawang koleksyon: “<span style="background: #fffaa3">zlib3_records</span>” at “<span style="background: #ffd6fe">zlib3_files</span>”. Ito ay nagpapahintulot sa amin na hiwalay na i-scrape at ilabas ang mga metadata record mula sa aktwal na mga file ng libro. Dahil dito, naglabas kami ng dalawang torrent na may mga metadata file: Naglabas din kami ng maraming torrent na may mga binary data folder, ngunit para lamang sa koleksyon na “<span style="background: #ffd6fe">zlib3_files</span>”, 62 sa kabuuan: Sa pamamagitan ng pagpapatakbo ng <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> makikita natin kung ano ang nasa loob: Sa kasong ito, ito ay metadata ng isang libro ayon sa ulat ng Z-Library. Sa pinakamataas na antas mayroon lamang kaming “aacid” at “metadata”, ngunit walang “data_folder”, dahil walang kaukulang binary data. Ang AACID ay naglalaman ng “22430000” bilang pangunahing ID, na makikita natin ay kinuha mula sa “zlibrary_id”. Maaari nating asahan na ang ibang mga AAC sa koleksyong ito ay may parehong istruktura. Ngayon, patakbuhin natin ang <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Ito ay mas maliit na AAC metadata, bagaman ang karamihan ng AAC na ito ay matatagpuan sa ibang lugar sa isang binary file! Pagkatapos ng lahat, mayroon tayong “data_folder” sa pagkakataong ito, kaya maaari nating asahan na ang kaukulang binary data ay matatagpuan sa <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Ang “metadata” ay naglalaman ng “zlibrary_id”, kaya madali nating maiuugnay ito sa kaukulang AAC sa koleksyong “zlib_records”. Maaari nating maiugnay sa iba't ibang paraan, hal., sa pamamagitan ng AACID — hindi ito inireseta ng pamantayan. Tandaan na hindi rin kinakailangan para sa field na “metadata” na maging JSON mismo. Maaari itong maging isang string na naglalaman ng XML o anumang iba pang format ng data. Maaari mo ring iimbak ang impormasyon ng metadata sa kaukulang binary blob, hal., kung ito ay maraming data. Heterogeneous na mga file at metadata, sa pinakamalapit na orihinal na format hangga't maaari. Maaaring direktang ihain ang binary na data ng mga webserver tulad ng Nginx. Heterogeneous na mga identifier sa mga source library, o kahit na kawalan ng mga identifier. Hiwalay na mga paglabas ng metadata kumpara sa data ng file, o mga paglabas na metadata lamang (hal. ang aming paglabas ng ISBNdb). Pamamahagi sa pamamagitan ng mga torrent, bagaman may posibilidad ng iba pang mga pamamaraan ng pamamahagi (hal. IPFS). Hindi nababagong mga talaan, dahil dapat nating ipagpalagay na ang aming mga torrent ay mabubuhay magpakailanman. Incremental na mga paglabas / nadaragdagang mga paglabas. Nababasa at nasusulat ng makina, maginhawa at mabilis, lalo na para sa aming stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Medyo madaling inspeksyon ng tao, bagaman ito ay pangalawa sa kakayahang mabasa ng makina. Madaling i-seed ang aming mga koleksyon gamit ang isang karaniwang nirentahang seedbox. Mga layunin sa disenyo Hindi namin pinapahalagahan ang mga file na madaling i-navigate nang manu-mano sa disk, o masaliksik nang walang preprocessing. Hindi namin pinapahalagahan ang pagiging direktang katugma sa umiiral na software ng library. Habang dapat madali para sa sinuman na i-seed ang aming koleksyon gamit ang mga torrent, hindi namin inaasahan na ang mga file ay magagamit nang walang makabuluhang teknikal na kaalaman at dedikasyon. Ang aming pangunahing kaso ng paggamit ay ang pamamahagi ng mga file at kaugnay na metadata mula sa iba't ibang umiiral na koleksyon. Ang aming pinakamahalagang konsiderasyon ay: Ilang hindi layunin: Dahil ang Arkibo ni Anna ay open source, nais naming gamitin ang aming format nang direkta. Kapag ina-update namin ang aming search index, ina-access lamang namin ang mga pampublikong magagamit na landas, upang ang sinumang mag-fork ng aming library ay makapagsimula agad. <strong>AAC.</strong> Ang AAC (Anna’s Archive Container) ay isang solong item na binubuo ng <strong>metadata</strong>, at opsyonal na <strong>binary data</strong>, na parehong hindi nababago. Ito ay may pandaigdigang natatanging tagatukoy, na tinatawag na <strong>AACID</strong>. <strong>AACID.</strong> Ang format ng AACID ay ganito: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Halimbawa, isang aktwal na AACID na inilabas namin ay <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Saklaw ng AACID.</strong> Dahil ang mga AACID ay naglalaman ng patuloy na tumataas na mga timestamp, maaari naming gamitin iyon upang tukuyin ang mga saklaw sa loob ng isang partikular na koleksyon. Ginagamit namin ang format na ito: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kung saan ang mga timestamp ay kasama. Ito ay naaayon sa ISO 8601 na notasyon. Ang mga saklaw ay tuloy-tuloy, at maaaring mag-overlap, ngunit sa kaso ng overlap ay dapat maglaman ng magkaparehong mga record tulad ng naunang inilabas sa koleksyon na iyon (dahil ang mga AAC ay hindi nababago). Hindi pinapayagan ang mga nawawalang record. <code>{collection}</code>: ang pangalan ng koleksyon, na maaaring maglaman ng mga ASCII na letra, numero, at underscores (ngunit walang dobleng underscores). <code>{collection-specific ID}</code>: isang koleksyon-tiyak na tagatukoy, kung naaangkop, hal. ang Z-Library ID. Maaaring hindi isama o paikliin. Dapat hindi isama o paikliin kung ang AACID ay lalampas sa 150 na karakter. <code>{ISO 8601 timestamp}</code>: isang maikling bersyon ng ISO 8601, palaging nasa UTC, hal. <code>20220723T194746Z</code>. Ang numerong ito ay dapat na patuloy na tumataas para sa bawat paglabas, kahit na ang eksaktong semantika nito ay maaaring magkaiba bawat koleksyon. Iminumungkahi naming gamitin ang oras ng pag-scrape o ng pagbuo ng ID. <code>{shortuuid}</code>: isang UUID ngunit naka-compress sa ASCII, hal. gamit ang base57. Sa kasalukuyan, ginagamit namin ang <a %(github_skorokithakis_shortuuid)s>shortuuid</a> na Python library. <strong>Binary data folder.</strong> Isang folder na may binary data ng isang saklaw ng mga AAC, para sa isang partikular na koleksyon. Ang mga ito ay may mga sumusunod na katangian: Ang direktoryo ay dapat maglaman ng mga data file para sa lahat ng AAC sa loob ng tinukoy na saklaw. Ang bawat data file ay dapat may AACID bilang pangalan ng file (walang mga extension). Ang pangalan ng direktoryo ay dapat na isang saklaw ng AACID, na may prefix na <code style="color: green">annas_archive_data__</code>, at walang suffix. Halimbawa, ang isa sa aming aktwal na mga paglabas ay may direktoryo na tinatawag na<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Inirerekomenda na gawing medyo madaling pamahalaan ang mga folder na ito sa laki, hal., hindi mas malaki sa 100GB-1TB bawat isa, bagaman maaaring magbago ang rekomendasyong ito sa paglipas ng panahon. <strong>Koleksyon.</strong> Ang bawat AAC ay kabilang sa isang koleksyon, na sa pamamagitan ng kahulugan ay isang listahan ng mga AAC na semantikal na pare-pareho. Ibig sabihin, kung gagawa ka ng makabuluhang pagbabago sa format ng metadata, kailangan mong lumikha ng bagong koleksyon. Ang pamantayan <strong>Metadata file.</strong> Ang isang metadata file ay naglalaman ng metadata ng isang saklaw ng mga AAC, para sa isang partikular na koleksyon. Ang mga ito ay may mga sumusunod na katangian: <code>data_folder</code> ay opsyonal, at ito ang pangalan ng binary data folder na naglalaman ng kaukulang binary data. Ang pangalan ng file ng kaukulang binary data sa loob ng folder na iyon ay ang AACID ng record. Ang bawat JSON object ay dapat maglaman ng mga sumusunod na field sa pinakamataas na antas: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsyonal). Walang ibang field ang pinapayagan. Ang pangalan ng file ay dapat na isang saklaw ng AACID, na may prefix na <code style="color: red">annas_archive_meta__</code> at sinusundan ng <code>.jsonl.zstd</code>. Halimbawa, ang isa sa aming mga paglabas ay tinatawag na<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Tulad ng ipinapahiwatig ng extension ng file, ang uri ng file ay <a %(jsonlines)s>JSON Lines</a> na naka-compress gamit ang <a %(zstd)s>Zstandard</a>. <code>metadata</code> ay arbitraryong metadata, ayon sa semantika ng koleksyon. Dapat itong semantikal na pare-pareho sa loob ng koleksyon. Ang <code style="color: red">annas_archive_meta__</code> na prefix ay maaaring iakma sa pangalan ng iyong institusyon, hal. <code style="color: red">my_institute_meta__</code>. <strong>“records” at “files” na koleksyon.</strong> Sa pamamagitan ng kaugalian, madalas na maginhawa na ilabas ang “records” at “files” bilang magkaibang koleksyon, upang maaari silang ilabas sa iba't ibang iskedyul, hal. batay sa mga rate ng pag-scrape. Ang “record” ay isang koleksyon na naglalaman lamang ng metadata, na naglalaman ng impormasyon tulad ng mga pamagat ng libro, mga may-akda, ISBN, atbp., habang ang “files” ay ang mga koleksyon na naglalaman ng aktwal na mga file mismo (pdf, epub). Sa huli, napagpasyahan namin ang isang medyo simpleng pamantayan. Ito ay medyo maluwag, hindi pamantayan, at isang patuloy na ginagawa. <strong>Torrents.</strong> Ang mga metadata file at binary data folder ay maaaring pagsama-samahin sa mga torrent, na may isang torrent bawat metadata file, o isang torrent bawat binary data folder. Ang mga torrent ay dapat may orihinal na pangalan ng file/directory kasama ang <code>.torrent</code> na suffix bilang kanilang filename. <a %(wikipedia_annas_archive)s>Ang Arkibo ni Anna</a> ay naging pinakamalaking shadow library sa mundo, at ang tanging shadow library sa kanyang saklaw na ganap na open-source at open-data. Sa ibaba ay isang talahanayan mula sa aming pahina ng Datasets (bahagyang binago): Nagawa namin ito sa tatlong paraan: Pag-mirror ng mga umiiral na open-data shadow libraries (tulad ng Sci-Hub at Library Genesis). Pagtulong sa mga shadow libraries na nais maging mas bukas, ngunit walang oras o mapagkukunan para gawin ito (tulad ng koleksyon ng komiks ng Libgen). Pag-scrape ng mga library na ayaw magbahagi ng maramihan (tulad ng Z-Library). Para sa (2) at (3) kami ngayon ay namamahala ng isang malaking koleksyon ng mga torrent mismo (100s ng TBs). Sa ngayon, tinuturing namin ang mga koleksyong ito bilang mga one-off, ibig sabihin ay may natatanging imprastraktura at organisasyon ng data para sa bawat koleksyon. Nagdadagdag ito ng malaking overhead sa bawat paglabas, at ginagawang partikular na mahirap ang paggawa ng mas incremental na mga paglabas. Iyan ang dahilan kung bakit nagpasya kaming i-standardize ang aming mga paglabas. Ito ay isang teknikal na blog post kung saan ipinapakilala namin ang aming pamantayan: <strong>Mga Container ng Arkibo ni Anna</strong>. Mga Container ng Arkibo ni Anna (AAC): pag-standardize ng mga release mula sa pinakamalaking shadow library sa mundo Ang Arkibo ni Anna ay naging pinakamalaking shadow library sa mundo, na nangangailangan sa amin na i-standardize ang aming mga release. 300GB+ ng mga pabalat ng libro ang inilabas Sa wakas, masaya kaming ipahayag ang isang maliit na paglabas. Sa pakikipagtulungan sa mga tao na nagpapatakbo ng Libgen.rs fork, ibinabahagi namin ang lahat ng kanilang mga pabalat ng libro sa pamamagitan ng torrents at IPFS. Ito ay magpapamahagi ng load ng pagtingin sa mga pabalat sa mas maraming makina, at mas mapapanatili ang mga ito. Sa maraming (ngunit hindi lahat) na kaso, ang mga pabalat ng libro ay kasama sa mga file mismo, kaya ito ay isang uri ng “derived data”. Ngunit ang pagkakaroon nito sa IPFS ay napaka-kapaki-pakinabang pa rin para sa pang-araw-araw na operasyon ng parehong Anna’s Archive at ang iba't ibang Library Genesis forks. Tulad ng dati, maaari mong mahanap ang paglabas na ito sa Pirate Library Mirror (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). Hindi namin ito ililink dito, ngunit madali mo itong mahahanap. Sana ay makapagpahinga kami ng kaunti, ngayong mayroon na kaming disenteng alternatibo sa Z-Library. Ang workload na ito ay hindi partikular na napapanatili. Kung ikaw ay interesado sa pagtulong sa programming, operasyon ng server, o gawain sa pangangalaga, tiyak na makipag-ugnayan sa amin. Marami pa ring <a %(annas_archive)s>trabaho na dapat gawin</a>. Salamat sa iyong interes at suporta. Lumipat sa ElasticSearch Ang ilang mga query ay tumatagal ng sobrang tagal, hanggang sa punto na sinasakop nila ang lahat ng bukas na koneksyon. Sa default, ang MySQL ay may minimum na haba ng salita, o ang iyong index ay maaaring maging napakalaki. Naiulat ng mga tao na hindi nila mahanap ang “Ben Hur”. Ang paghahanap ay medyo mabilis lamang kapag ganap na na-load sa memorya, na nangangailangan sa amin na kumuha ng mas mahal na makina upang patakbuhin ito, kasama ang ilang mga utos upang i-preload ang index sa pagsisimula. Hindi namin ito madaling mapalawak upang makabuo ng mga bagong tampok, tulad ng mas mahusay na <a %(wikipedia_cjk_characters)s>tokenization para sa mga wikang walang whitespace</a>, pag-filter/pag-faceting, pag-uuri, mga mungkahi ng "did you mean", autocomplete, at iba pa. Isa sa aming <a %(annas_archive)s>mga tiket</a> ay isang koleksyon ng mga isyu sa aming sistema ng paghahanap. Ginamit namin ang MySQL full-text search, dahil nasa MySQL na ang lahat ng aming data. Ngunit mayroon itong mga limitasyon: Matapos makipag-usap sa maraming eksperto, napagpasyahan naming gamitin ang ElasticSearch. Hindi ito perpekto (ang kanilang default na mga mungkahi ng "did you mean" at mga tampok ng autocomplete ay hindi maganda), ngunit sa kabuuan ay mas mahusay ito kaysa sa MySQL para sa paghahanap. Hindi pa rin kami <a %(youtube)s>masyadong interesado</a> na gamitin ito para sa anumang mission-critical na data (bagaman marami na silang <a %(elastic_co)s>pag-unlad</a>), ngunit sa kabuuan ay masaya kami sa paglipat. Sa ngayon, naipatupad namin ang mas mabilis na paghahanap, mas mahusay na suporta sa wika, mas mahusay na pag-uuri ng kaugnayan, iba't ibang mga pagpipilian sa pag-uuri, at pag-filter sa wika/uri ng libro/uri ng file. Kung ikaw ay interesado kung paano ito gumagana, <a %(annas_archive_l140)s>tingnan</a> <a %(annas_archive_l1115)s>ito</a> <a %(annas_archive_l1635)s>dito</a>. Medyo naa-access ito, bagaman maaari itong gumamit ng mas maraming komento… Ang Arkibo ni Anna ay ganap na open source Naniniwala kami na ang impormasyon ay dapat na libre, at ang aming sariling code ay hindi eksepsyon. Inilabas namin ang lahat ng aming code sa aming pribadong hosted na Gitlab instance: <a %(annas_archive)s>Software ni Anna</a>. Ginagamit din namin ang issue tracker upang ayusin ang aming trabaho. Kung nais mong makilahok sa aming pag-unlad, ito ay isang magandang lugar upang magsimula. Upang bigyan ka ng ideya ng mga bagay na aming pinagtatrabahuhan, tingnan ang aming kamakailang gawain sa mga pagpapabuti ng performance sa client-side. Dahil hindi pa namin naipatupad ang pagination, madalas kaming nagbabalik ng napakahabang mga pahina ng paghahanap, na may 100-200 na resulta. Ayaw naming putulin agad ang mga resulta ng paghahanap, ngunit nangangahulugan ito na babagal ang ilang mga device. Para dito, nagpatupad kami ng maliit na trick: binalot namin ang karamihan ng mga resulta ng paghahanap sa mga HTML comment (<code><!-- --></code>), at pagkatapos ay sumulat ng maliit na Javascript na magdedetect kung kailan dapat maging visible ang isang resulta, sa sandaling iyon ay aalisin namin ang comment: Naipatupad ang "virtualization" ng DOM sa 23 linya lamang, hindi na kailangan ng mga magarbong library! Ito ang uri ng mabilis at praktikal na code na nagagawa mo kapag limitado ang oras, at may mga totoong problemang kailangang lutasin. Naiulat na gumagana na ngayon nang maayos ang aming paghahanap sa mga mabagal na device! Isa pang malaking pagsisikap ay ang awtomatiko ang pagbuo ng database. Nang ilunsad namin, basta-basta na lang naming pinagsama-sama ang iba't ibang pinagmulan. Ngayon, nais naming panatilihing na-update ang mga ito, kaya nagsulat kami ng ilang script upang i-download ang bagong metadata mula sa dalawang Library Genesis forks, at isinasama ang mga ito. Ang layunin ay hindi lamang gawing kapaki-pakinabang ito para sa aming archive, kundi gawing madali para sa sinumang nais maglaro sa metadata ng shadow library. Ang layunin ay magkaroon ng isang Jupyter notebook na may iba't ibang kawili-wiling metadata na magagamit, upang makagawa kami ng mas maraming pananaliksik tulad ng pagtukoy kung <a %(blog)s>ilang porsyento ng mga ISBN ang napanatili magpakailanman</a>. Sa wakas, binago namin ang aming sistema ng donasyon. Maaari mo na ngayong gamitin ang credit card upang direktang magdeposito ng pera sa aming mga crypto wallet, nang hindi talaga kailangang malaman ang tungkol sa cryptocurrencies. Patuloy naming susubaybayan kung gaano ito kahusay sa praktika, ngunit ito ay isang malaking bagay. Sa pagbaba ng Z-Library at ang (umano'y) mga tagapagtatag nito ay naaresto, nagtatrabaho kami nang walang tigil upang magbigay ng magandang alternatibo sa Arkibo ni Anna (hindi namin ito ililink dito, ngunit maaari mo itong i-Google). Narito ang ilan sa mga bagay na aming nakamit kamakailan. Update ni Anna: ganap na open source na archive, ElasticSearch, 300GB+ ng mga pabalat ng libro Nagtatrabaho kami nang walang tigil upang magbigay ng magandang alternatibo sa Arkibo ni Anna. Narito ang ilan sa mga bagay na aming nakamit kamakailan. Pagsusuri Ang mga semantic duplicate (iba't ibang mga scan ng parehong libro) ay maaaring teoretikal na ma-filter, ngunit ito ay mahirap. Kapag manu-manong tinitingnan ang mga komiks, nakakita kami ng masyadong maraming maling positibo. Mayroong ilang mga duplicate na puro sa pamamagitan ng MD5, na medyo pag-aaksaya, ngunit ang pag-filter ng mga iyon ay magbibigay lamang sa amin ng humigit-kumulang 1% in na pagtitipid. Sa sukat na ito, iyon ay humigit-kumulang 1TB pa rin, ngunit din, sa sukat na ito, ang 1TB ay hindi talaga mahalaga. Mas gusto naming hindi ipagsapalaran ang aksidenteng pagkasira ng data sa prosesong ito. Nakatagpo kami ng isang bungkos ng non-book data, tulad ng mga pelikula batay sa mga komiks. Mukhang pag-aaksaya rin iyon, dahil ang mga ito ay malawak na magagamit sa pamamagitan ng iba pang mga paraan. Gayunpaman, napagtanto namin na hindi namin maaaring i-filter lamang ang mga file ng pelikula, dahil mayroon ding <em>interactive comic books</em> na inilabas sa computer, na naitala at na-save ng isang tao bilang mga pelikula. Sa huli, anumang bagay na maaari naming tanggalin mula sa koleksyon ay makakatipid lamang ng ilang porsyento. Pagkatapos ay naalala namin na kami ay mga data hoarder, at ang mga taong magmi-mirror nito ay mga data hoarder din, kaya, “ANO ANG IBIG MONG SABIHIN, TANGGALIN?!” :) Kapag nakakuha ka ng 95TB na ibinuhos sa iyong storage cluster, sinusubukan mong unawain kung ano ang nasa loob nito… Gumawa kami ng ilang pagsusuri upang makita kung maaari naming bawasan ang laki ng kaunti, tulad ng sa pamamagitan ng pag-alis ng mga duplicate. Narito ang ilan sa aming mga natuklasan: Kaya't ipinapakita namin sa inyo ang buong, hindi binagong koleksyon. Maraming data ito, ngunit umaasa kaming sapat na tao ang magmamalasakit na i-seed ito pa rin. Pakikipagtulungan Dahil sa laki nito, matagal nang nasa aming wishlist ang koleksyong ito, kaya pagkatapos ng aming tagumpay sa pag-backup ng Z-Library, itinakda namin ang aming mga mata sa koleksyong ito. Sa una, direkta naming kinopya ito, na medyo hamon, dahil ang kanilang server ay hindi nasa pinakamahusay na kondisyon. Nakakuha kami ng humigit-kumulang 15TB sa ganitong paraan, ngunit mabagal ang pag-usad. Sa kabutihang palad, nagawa naming makipag-ugnayan sa operator ng aklatan, na pumayag na ipadala sa amin ang lahat ng data nang direkta, na mas mabilis. Tumagal pa rin ng higit sa kalahating taon upang ilipat at iproseso ang lahat ng data, at halos nawala namin ang lahat ng ito dahil sa disk corruption, na nangangahulugang magsisimula muli. Ang karanasang ito ay nagpatibay sa aming paniniwala na mahalagang mailabas ang data na ito sa lalong madaling panahon, upang ito ay ma-mirror sa malawak na saklaw. Isa o dalawang hindi pinalad na insidente na lang ang layo namin mula sa pagkawala ng koleksyong ito magpakailanman! Ang koleksyon Ang mabilis na pagkilos ay nangangahulugan na ang koleksyon ay medyo hindi organisado… Tingnan natin. Isipin natin na mayroon tayong filesystem (na sa katotohanan ay hinahati natin sa mga torrents): Ang unang direktoryo, <code>/repository</code>, ay ang mas istrukturadong bahagi nito. Ang direktoryong ito ay naglalaman ng tinatawag na "thousand dirs": mga direktoryo na may tig-iisang libong file, na sunud-sunod na binibilang sa database. Ang Direktoryo <code>0</code> ay naglalaman ng mga file na may comic_id 0–999, at iba pa. Ito ay ang parehong scheme na ginagamit ng Library Genesis para sa mga koleksyon ng fiction at non-fiction. Ang ideya ay ang bawat "thousand dir" ay awtomatikong nagiging torrent sa sandaling ito ay napuno. Gayunpaman, ang operator ng Libgen.li ay hindi kailanman gumawa ng mga torrents para sa koleksyong ito, kaya't ang mga thousand dirs ay malamang na naging hindi maginhawa, at nagbigay-daan sa "unsorted dirs". Ito ay <code>/comics0</code> hanggang <code>/comics4</code>. Lahat sila ay naglalaman ng natatanging mga istruktura ng direktoryo, na marahil ay may kahulugan para sa pagkolekta ng mga file, ngunit hindi na masyadong may kahulugan sa amin ngayon. Sa kabutihang palad, ang metadata ay direktang tumutukoy pa rin sa lahat ng mga file na ito, kaya't ang kanilang organisasyon sa disk ay hindi talaga mahalaga! Ang metadata ay makukuha sa anyo ng isang MySQL database. Ito ay maaaring i-download nang direkta mula sa website ng Libgen.li, ngunit gagawin din naming magagamit ito sa isang torrent, kasama ang aming sariling talahanayan na may lahat ng MD5 hashes. <q>Sinusubukan ni Dr. Barbara Gordon na mawala sa karaniwang mundo ng aklatan…</q> Mga fork ng Libgen Una, ilang background. Maaaring kilala ninyo ang Library Genesis para sa kanilang napakalaking koleksyon ng mga libro. Mas kaunting tao ang nakakaalam na ang mga boluntaryo ng Library Genesis ay lumikha ng iba pang mga proyekto, tulad ng isang malaking koleksyon ng mga magasin at mga standard na dokumento, isang buong backup ng Sci-Hub (sa pakikipagtulungan sa tagapagtatag ng Sci-Hub, si Alexandra Elbakyan), at sa katunayan, isang napakalaking koleksyon ng mga komiks. Sa ilang punto, ang iba't ibang mga operator ng mga mirror ng Library Genesis ay naghiwalay ng landas, na nagbigay-daan sa kasalukuyang sitwasyon ng pagkakaroon ng ilang iba't ibang "forks", na lahat ay nagdadala pa rin ng pangalang Library Genesis. Ang Libgen.li fork ay natatanging may koleksyon ng mga komiks na ito, pati na rin ang isang malaking koleksyon ng mga magasin (na aming pinagtatrabahuhan din). Pangangalap ng Pondo Inilalabas namin ang data na ito sa ilang malalaking bahagi. Ang unang torrent ay ng <code>/comics0</code>, na inilagay namin sa isang malaking 12TB .tar file. Mas mabuti ito para sa iyong hard drive at torrent software kaysa sa napakaraming maliliit na file. Bilang bahagi ng paglabas na ito, nagsasagawa kami ng pangangalap ng pondo. Naghahanap kami na makalikom ng $20,000 upang masakop ang mga gastos sa operasyon at kontrata para sa koleksyon na ito, pati na rin paganahin ang mga kasalukuyan at hinaharap na proyekto. Mayroon kaming ilang <em>malalaking</em> proyekto na ginagawa. <em>Sino ang sinusuportahan ko sa aking donasyon?</em> Sa madaling sabi: sinusuportahan namin ang lahat ng kaalaman at kultura ng sangkatauhan, at ginagawa itong madaling ma-access. Ang lahat ng aming code at data ay open source, kami ay isang ganap na boluntaryong proyekto, at nakapagligtas na kami ng 125TB na halaga ng mga libro sa ngayon (bilang karagdagan sa mga umiiral na torrent ng Libgen at Scihub). Sa huli, bumubuo kami ng isang flywheel na nagbibigay-daan at humihikayat sa mga tao na hanapin, i-scan, at i-backup ang lahat ng mga libro sa mundo. Isusulat namin ang aming master plan sa isang hinaharap na post. :) Kung mag-donate ka para sa isang 12-buwang “Amazing Archivist” membership ($780), makakakuha ka ng <strong>“mag-ampon ng isang torrent”</strong>, na nangangahulugang ilalagay namin ang iyong username o mensahe sa filename ng isa sa mga torrent! Maaari kang mag-donate sa pamamagitan ng pagpunta sa <a %(wikipedia_annas_archive)s>Anna’s Archive</a> at pag-click sa button na “Donate”. Naghahanap din kami ng mas maraming boluntaryo: mga software engineer, mga mananaliksik sa seguridad, mga eksperto sa anonymous merchant, at mga tagasalin. Maaari mo rin kaming suportahan sa pamamagitan ng pagbibigay ng mga serbisyo sa pagho-host. At siyempre, mangyaring i-seed ang aming mga torrent! Salamat sa lahat ng napaka-mapagbigay na sumuporta sa amin! Tunay na gumagawa kayo ng pagkakaiba. Narito ang mga torrent na inilabas sa ngayon (pinoproseso pa namin ang iba pa): Ang lahat ng torrent ay matatagpuan sa <a %(wikipedia_annas_archive)s>Anna’s Archive</a> sa ilalim ng “Datasets” (hindi namin direktang iniuugnay doon, kaya ang mga link sa blog na ito ay hindi matatanggal mula sa Reddit, Twitter, atbp). Mula doon, sundin ang link sa Tor website. <a %(news_ycombinator)s>Talakayin sa Hacker News</a> Ano ang susunod? Ang isang bungkos ng mga torrent ay mahusay para sa pangmatagalang pangangalaga, ngunit hindi gaanong para sa pang-araw-araw na pag-access. Makikipagtulungan kami sa mga kasosyo sa pagho-host upang makuha ang lahat ng data na ito sa web (dahil ang Anna’s Archive ay hindi direktang nagho-host ng anuman). Siyempre, makikita mo ang mga link sa pag-download na ito sa Anna’s Archive. Inaanyayahan din namin ang lahat na gumawa ng mga bagay sa data na ito! Tulungan kaming mas mahusay na suriin ito, i-deduplicate ito, ilagay ito sa IPFS, i-remix ito, sanayin ang iyong mga AI model gamit ito, at iba pa. Nasa iyo na ang lahat, at hindi na kami makapaghintay na makita kung ano ang gagawin mo dito. Sa wakas, tulad ng sinabi dati, mayroon pa kaming ilang malalaking paglabas na paparating (kung <em>may isang tao</em> na <em>aksidenteng</em> magpadala sa amin ng dump ng isang <em>tiyak na</em> ACS4 database, alam mo kung saan kami mahahanap…), pati na rin ang pagbuo ng flywheel para sa pag-backup ng lahat ng mga libro sa mundo. Kaya manatiling nakatutok, nagsisimula pa lang kami. - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ang pinakamalaking shadow library ng mga komiks ay malamang na sa isang partikular na Library Genesis fork: Libgen.li. Ang isang administrator na nagpapatakbo ng site na iyon ay nagawang mangolekta ng isang napakalaking koleksyon ng mga komiks na may higit sa 2 milyong mga file, na umaabot sa higit sa 95TB. Gayunpaman, hindi tulad ng iba pang mga koleksyon ng Library Genesis, ang isang ito ay hindi magagamit nang maramihan sa pamamagitan ng torrents. Maaari mo lamang ma-access ang mga komiks na ito nang paisa-isa sa pamamagitan ng kanyang mabagal na personal na server — isang punto ng kabiguan. Hanggang ngayon! Sa post na ito, ibabahagi namin sa inyo ang higit pang impormasyon tungkol sa koleksyong ito, at tungkol sa aming fundraiser upang suportahan ang higit pang gawaing ito. Na-back up ng Anna’s Archive ang pinakamalaking shadow library ng komiks sa mundo (95TB) — maaari kang makatulong na i-seed ito Ang pinakamalaking shadow library ng mga komiks sa mundo ay may isang punto ng kabiguan.. hanggang ngayon. Babala: ang post sa blog na ito ay hindi na ginagamit. Napagpasyahan naming hindi pa handa ang IPFS para sa pangunahing oras. Magli-link pa rin kami sa mga file sa IPFS mula sa Arkibo ni Anna kung maaari, ngunit hindi na namin ito iho-host mismo, at hindi rin namin inirerekomenda ang iba na mag-mirror gamit ang IPFS. Pakitingnan ang aming pahina ng Torrents kung nais mong makatulong na mapanatili ang aming koleksyon. Paglalagay ng 5,998,794 na libro sa IPFS Pagpaparami ng mga kopya Bumalik tayo sa ating orihinal na tanong: paano natin maipagmamalaki na mapanatili ang ating mga koleksyon magpakailanman? Ang pangunahing problema dito ay ang ating koleksyon ay <a %(torrents_stats)s>lumalaki</a> nang mabilis, sa pamamagitan ng pag-scrape at open-sourcing ng ilang malalaking koleksyon (bukod pa sa kamangha-manghang gawaing nagawa na ng iba pang open-data shadow libraries tulad ng Sci-Hub at Library Genesis). Ang paglago ng data na ito ay nagpapahirap sa mga koleksyon na ma-mirror sa buong mundo. Mahal ang pag-iimbak ng data! Ngunit kami ay optimistiko, lalo na kapag obserbahan ang sumusunod na tatlong mga trend. Ang <a %(annas_archive_stats)s>kabuuang laki</a> ng aming mga koleksyon, sa nakalipas na ilang buwan, na hinati ayon sa bilang ng mga torrent seeder. Mga trend ng presyo ng HDD mula sa iba't ibang pinagmulan (i-click upang tingnan ang pag-aaral). <a %(critical_window_chinese)s>Bersyong Tsino 中文版</a>, talakayin sa <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Nakuha na namin ang mga mabababang bunga Ito ay direktang sumusunod mula sa aming mga prayoridad na tinalakay sa itaas. Mas gusto naming magtrabaho sa pagpapalaya ng malalaking koleksyon muna. Ngayon na nakuha na namin ang ilan sa pinakamalalaking koleksyon sa mundo, inaasahan naming mas mabagal ang aming paglago. Mayroon pa ring mahabang buntot ng mas maliliit na koleksyon, at ang mga bagong aklat ay nai-scan o nailalathala araw-araw, ngunit ang bilis ay malamang na mas mabagal. Maaari pa rin kaming magdoble o mag-triple sa laki, ngunit sa mas mahabang panahon. Mga Pagpapabuti sa OCR. Prayoridad Code ng software para sa agham at inhinyeriya Kathang-isip o libangan na bersyon ng lahat ng nabanggit Data ng heograpiya (hal. mga mapa, geological surveys) Panloob na data mula sa mga korporasyon o gobyerno (mga tagas) Data ng pagsukat tulad ng mga siyentipikong sukat, datos pang-ekonomiya, ulat ng korporasyon Mga tala ng metadata sa pangkalahatan (ng hindi kathang-isip at kathang-isip; ng ibang media, sining, tao, atbp; kabilang ang mga pagsusuri) Mga aklat na hindi kathang-isip Mga magasin na hindi kathang-isip, pahayagan, manwal Mga transcript na hindi kathang-isip ng mga talumpati, dokumentaryo, podcast Organikong data tulad ng mga DNA sequence, buto ng halaman, o mga sample ng mikrobyo Mga akademikong papel, journal, ulat Mga website ng agham at inhinyeriya, online na talakayan Mga transcript ng legal o hukuman na pagdinig Natatanging nasa panganib ng pagkasira (hal. dahil sa digmaan, pagbawas ng pondo, demanda, o pag-uusig sa politika) Bihira Natatanging hindi napagtutuunan Bakit ba tayo masyadong nagmamalasakit sa mga papel at aklat? Isantabi natin ang ating pangunahing paniniwala sa pagpreserba sa pangkalahatan — maaari tayong magsulat ng isa pang post tungkol doon. Kaya bakit partikular na mga papel at aklat? Ang sagot ay simple: <strong>density ng impormasyon</strong>. Sa bawat megabyte ng imbakan, ang nakasulat na teksto ay nag-iimbak ng pinakamaraming impormasyon sa lahat ng media. Habang nagmamalasakit kami sa parehong kaalaman at kultura, mas nagmamalasakit kami sa una. Sa kabuuan, nakikita namin ang isang hierarchy ng density ng impormasyon at kahalagahan ng pagpreserba na mukhang ganito: Ang pagraranggo sa listahang ito ay medyo arbitraryo — ang ilang mga item ay magkapantay o may hindi pagkakasundo sa loob ng aming koponan — at malamang na nakakalimutan namin ang ilang mahahalagang kategorya. Ngunit ito ay halos kung paano namin inuuna. Ang ilan sa mga item na ito ay masyadong naiiba mula sa iba para sa amin na mag-alala (o ay inaalagaan na ng ibang mga institusyon), tulad ng organikong data o data ng heograpiya. Ngunit karamihan sa mga item sa listahang ito ay talagang mahalaga sa amin. Isa pang malaking salik sa aming pag-priyoridad ay kung gaano kalaki ang panganib ng isang tiyak na gawain. Mas gusto naming mag-focus sa mga gawaing: Sa wakas, mahalaga sa amin ang saklaw. Mayroon kaming limitadong oras at pera, kaya mas gugustuhin naming gumugol ng isang buwan sa pag-save ng 10,000 libro kaysa sa 1,000 libro — kung sila ay halos pantay na mahalaga at nasa panganib. <em><q>Ang nawala ay hindi na maibabalik; ngunit iligtas natin ang natitira: hindi sa pamamagitan ng vaults at kandado na nagtatago sa kanila mula sa mata at paggamit ng publiko, sa pag-aalay sa kanila sa pag-aaksaya ng panahon, kundi sa pamamagitan ng gayong pagdami ng mga kopya, na ilalagay sila sa labas ng abot ng aksidente.</q></em><br>— Thomas Jefferson, 1791 Mga shadow library Ang code ay maaaring open source sa Github, ngunit ang Github bilang kabuuan ay hindi madaling ma-mirror at sa gayon ay ma-preserba (bagaman sa partikular na kasong ito ay may sapat na ipinamamahaging kopya ng karamihan sa mga code repository) Ang mga metadata record ay maaaring malayang makita sa website ng Worldcat, ngunit hindi ma-download nang maramihan (hanggang sa aming <a %(worldcat_scrape)s>na-scrape</a> ang mga ito) Ang Reddit ay libre gamitin, ngunit kamakailan lamang ay nagpatupad ng mahigpit na mga hakbang laban sa pag-scrape, kasunod ng data-hungry na pagsasanay ng LLM (higit pa tungkol dito mamaya) Maraming mga organisasyon na may katulad na misyon, at katulad na mga priyoridad. Sa katunayan, may mga aklatan, archive, lab, museo, at iba pang mga institusyon na may tungkulin sa pagpepreserba ng ganitong uri. Marami sa mga ito ay may sapat na pondo, mula sa mga gobyerno, indibidwal, o korporasyon. Ngunit mayroon silang isang malaking bulag na lugar: ang legal na sistema. Dito nakasalalay ang natatanging papel ng mga shadow library, at ang dahilan kung bakit umiiral ang Arkibo ni Anna. Maaari naming gawin ang mga bagay na hindi pinapayagan ng ibang mga institusyon. Ngayon, hindi ito (madalas) na maaari naming i-archive ang mga materyales na ilegal na i-preserba sa ibang lugar. Hindi, legal sa maraming lugar na bumuo ng isang archive na may anumang mga libro, papel, magasin, at iba pa. Ngunit ang madalas na kulang sa mga legal na archive ay <strong>redundancy at longevity</strong>. May mga aklat na iisa lamang ang kopya na matatagpuan sa isang pisikal na aklatan sa isang lugar. May mga metadata record na binabantayan ng isang korporasyon lamang. May mga pahayagan na tanging sa microfilm lamang na-preserba sa isang archive. Ang mga aklatan ay maaaring mawalan ng pondo, ang mga korporasyon ay maaaring mabangkarote, ang mga archive ay maaaring bombahin at sunugin hanggang sa mawala. Hindi ito haka-haka — nangyayari ito palagi. Ang bagay na kaya naming gawin nang natatangi sa Arkibo ni Anna ay ang mag-imbak ng maraming kopya ng mga akda, sa malakihang paraan. Maaari kaming mangolekta ng mga papel, aklat, magasin, at iba pa, at ipamahagi ang mga ito nang maramihan. Sa kasalukuyan, ginagawa namin ito sa pamamagitan ng torrents, ngunit hindi mahalaga ang eksaktong teknolohiya at magbabago ito sa paglipas ng panahon. Ang mahalaga ay ang pagkakaroon ng maraming kopya na naipapamahagi sa buong mundo. Ang sipi na ito mula mahigit 200 taon na ang nakalipas ay nananatiling totoo: Isang mabilis na tala tungkol sa pampublikong domain. Dahil ang Arkibo ni Anna ay natatanging nakatuon sa mga aktibidad na ilegal sa maraming lugar sa buong mundo, hindi namin pinapansin ang mga koleksyong malawakang magagamit, tulad ng mga aklat sa pampublikong domain. Madalas na inaalagaan na ito ng mga legal na entidad. Gayunpaman, may mga konsiderasyon na minsan ay nagtutulak sa amin na magtrabaho sa mga koleksyong pampublikong magagamit: - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Patuloy na bumababa ang mga gastos sa imbakan nang eksponensyal 3. Mga Pagpapabuti sa Densidad ng Impormasyon Sa kasalukuyan, iniimbak namin ang mga libro sa mga hilaw na format na ibinibigay sa amin. Oo, sila ay naka-compress, ngunit madalas pa rin silang malalaking scan o litrato ng mga pahina. Hanggang ngayon, ang tanging mga opsyon upang paliitin ang kabuuang laki ng aming koleksyon ay sa pamamagitan ng mas agresibong compression, o deduplication. Gayunpaman, upang makamit ang sapat na pagtitipid, parehong masyadong lossy para sa aming panlasa. Ang mabigat na compression ng mga larawan ay maaaring magdulot ng halos hindi mabasang teksto. At ang deduplication ay nangangailangan ng mataas na kumpiyansa na ang mga libro ay eksaktong pareho, na madalas na masyadong hindi tumpak, lalo na kung ang mga nilalaman ay pareho ngunit ang mga scan ay ginawa sa iba't ibang pagkakataon. Laging mayroong ikatlong opsyon, ngunit ang kalidad nito ay napakasama kaya hindi namin ito isinasaalang-alang: <strong>OCR, o Optical Character Recognition</strong>. Ito ay ang proseso ng pag-convert ng mga larawan sa plain text, sa pamamagitan ng paggamit ng AI upang matukoy ang mga karakter sa mga larawan. Ang mga tool para dito ay matagal nang umiiral, at medyo maayos, ngunit ang “medyo maayos” ay hindi sapat para sa mga layunin ng pagpepreserba. Gayunpaman, ang mga kamakailang multi-modal deep-learning models ay gumawa ng napakabilis na pag-unlad, bagaman sa mataas na gastos pa rin. Inaasahan naming parehong ang katumpakan at gastos ay magpapabuti nang malaki sa mga darating na taon, hanggang sa puntong magiging makatotohanan na itong ilapat sa aming buong aklatan. Kapag nangyari iyon, malamang na itatago pa rin namin ang mga orihinal na file, ngunit bilang karagdagan maaari kaming magkaroon ng mas maliit na bersyon ng aming aklatan na karamihan sa mga tao ay nais i-mirror. Ang kicker ay ang raw text mismo ay mas mahusay na naka-compress, at mas madaling i-deduplicate, na nagbibigay sa amin ng mas maraming pagtitipid. Sa kabuuan, hindi ito hindi makatotohanan na asahan ang hindi bababa sa 5-10x na pagbawas sa kabuuang laki ng file, marahil higit pa. Kahit na may konserbatibong 5x na pagbawas, tinitingnan namin ang <strong>$1,000–$3,000 sa loob ng 10 taon kahit na ang aming aklatan ay triple ang laki</strong>. Sa oras ng pagsulat, ang <a %(diskprices)s>mga presyo ng disk</a> bawat TB ay nasa paligid ng $12 para sa mga bagong disk, $8 para sa mga ginamit na disk, at $4 para sa tape. Kung tayo ay konserbatibo at titingnan lamang ang mga bagong disk, nangangahulugan ito na ang pag-iimbak ng isang petabyte ay nagkakahalaga ng humigit-kumulang $12,000. Kung ipagpalagay natin na ang ating aklatan ay magta-triple mula 900TB hanggang 2.7PB, nangangahulugan iyon ng $32,400 upang ma-mirror ang buong aklatan natin. Idagdag ang kuryente, gastos ng iba pang hardware, at iba pa, i-round up natin ito sa $40,000. O sa tape, mas katulad ng $15,000–$20,000. Sa isang banda <strong>$15,000–$40,000 para sa kabuuan ng lahat ng kaalaman ng tao ay isang napakagandang halaga</strong>. Sa kabilang banda, medyo mataas ito upang asahan ang maraming buong kopya, lalo na kung gusto rin naming panatilihin ng mga tao ang kanilang mga torrent para sa kapakinabangan ng iba. Iyan ay ngayon. Ngunit ang progreso ay patuloy na umuusad: Ang mga gastos sa hard drive bawat TB ay halos nabawasan ng ikatlo sa nakalipas na 10 taon, at malamang na patuloy na bababa sa katulad na bilis. Ang tape ay tila nasa katulad na landas. Ang mga presyo ng SSD ay bumababa pa nang mas mabilis, at maaaring maabutan ang mga presyo ng HDD sa pagtatapos ng dekada. Kung ito ay magpapatuloy, sa loob ng 10 taon maaari tayong tumingin sa halagang $5,000–$13,000 lamang upang ma-mirror ang buong koleksyon natin (1/3), o mas mababa pa kung mas mabagal ang ating paglaki. Habang ito ay marami pa ring pera, ito ay magiging abot-kaya para sa maraming tao. At maaaring mas maganda pa ito dahil sa susunod na punto… Sa Arkibo ni Anna, madalas kaming tinatanong kung paano namin maipagmamalaki na mapanatili ang aming mga koleksyon magpakailanman, kung ang kabuuang laki ay papalapit na sa 1 Petabyte (1000 TB), at patuloy pang lumalaki. Sa artikulong ito, titingnan natin ang aming pilosopiya, at tingnan kung bakit ang susunod na dekada ay kritikal para sa aming misyon ng pagpreserba ng kaalaman at kultura ng sangkatauhan. Kritikal na Bintana Kung ang mga pagtataya na ito ay tumpak, kailangan lang naming maghintay ng ilang taon bago ang aming buong koleksyon ay malawak na ma-mirror. Kaya, sa mga salita ni Thomas Jefferson, “mailagay sa labas ng abot ng aksidente.” Sa kasamaang palad, ang pagdating ng LLMs, at ang kanilang data-hungry na pagsasanay, ay naglagay ng maraming may hawak ng copyright sa depensiba. Higit pa kaysa sa dati. Maraming mga website ang nagpapahirap sa pag-scrape at pag-archive, ang mga demanda ay nagliliparan, at habang ang mga pisikal na aklatan at archive ay patuloy na napapabayaan. Maaari lamang nating asahan na ang mga trend na ito ay patuloy na lumala, at maraming mga gawa ang mawawala bago pa man sila pumasok sa pampublikong domain. <strong>Nasa bisperas tayo ng isang rebolusyon sa pagpepreserba, ngunit <q>ang nawala ay hindi na maibabalik.</q></strong> Mayroon tayong kritikal na bintana ng mga 5-10 taon kung saan medyo mahal pa rin ang pagpapatakbo ng shadow library at paglikha ng maraming mirror sa buong mundo, at kung saan ang access ay hindi pa ganap na isinara. Kung maiaabot natin ang bintanang ito, sa gayon ay talagang mapapanatili natin ang kaalaman at kultura ng sangkatauhan magpakailanman. Hindi natin dapat hayaang masayang ang panahong ito. Hindi natin dapat hayaang magsara ang kritikal na bintanang ito sa atin. Tara na. Ang kritikal na bintana ng mga shadow library Paano natin maipagmamalaki na mapanatili ang ating mga koleksyon magpakailanman, kung ang mga ito ay papalapit na sa 1 PB? Koleksyon Ilang karagdagang impormasyon tungkol sa koleksyon. <a %(duxiu)s>Duxiu</a> ay isang napakalaking database ng mga na-scan na libro, na nilikha ng <a %(chaoxing)s>SuperStar Digital Library Group</a>. Karamihan ay mga akademikong libro, na na-scan upang gawing magagamit nang digital sa mga unibersidad at aklatan. Para sa aming mga tagapagsalita ng Ingles, ang <a %(library_princeton)s>Princeton</a> at ang <a %(guides_lib_uw)s>University of Washington</a> ay may magagandang pangkalahatang-ideya. Mayroon ding isang mahusay na artikulo na nagbibigay ng higit pang background: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (hanapin ito sa Arkibo ni Anna). Ang mga libro mula sa Duxiu ay matagal nang na-pirata sa internet ng Tsina. Karaniwan silang ibinebenta ng mas mababa sa isang dolyar ng mga reseller. Karaniwan silang ipinapamahagi gamit ang katumbas na Tsino ng Google Drive, na madalas na na-hack upang payagan ang mas maraming espasyo sa imbakan. Ang ilang mga teknikal na detalye ay matatagpuan <a %(github_duty_machine)s>dito</a> at <a %(github_821_github_io)s>dito</a>. Bagamat ang mga libro ay semi-pampublikong ipinamamahagi, medyo mahirap makuha ang mga ito nang maramihan. Mataas ito sa aming listahan ng mga gagawin, at naglaan kami ng ilang buwan ng full-time na trabaho para dito. Gayunpaman, kamakailan ay may isang kamangha-mangha, kahanga-hanga, at talentadong boluntaryo na lumapit sa amin, na nagsasabi sa amin na nagawa na nila ang lahat ng trabahong ito — sa malaking gastos. Ibinahagi nila ang buong koleksyon sa amin, nang hindi umaasa ng anumang kapalit, maliban sa garantiya ng pangmatagalang pangangalaga. Tunay na kahanga-hanga. Pumayag silang humingi ng tulong sa ganitong paraan upang makuha ang koleksyon na ma-OCR. Ang koleksyon ay may 7,543,702 na mga file. Ito ay higit pa sa Library Genesis non-fiction (mga 5.3 milyon). Ang kabuuang laki ng file ay humigit-kumulang 359TB (326TiB) sa kasalukuyang anyo nito. Bukas kami sa iba pang mga mungkahi at ideya. Makipag-ugnayan lamang sa amin. Tingnan ang Arkibo ni Anna para sa karagdagang impormasyon tungkol sa aming mga koleksyon, mga pagsisikap sa pangangalaga, at kung paano ka makakatulong. Salamat! Mga halimbawa ng pahina Upang patunayan sa amin na mayroon kang magandang pipeline, narito ang ilang mga halimbawa ng pahina upang masimulan, mula sa isang libro tungkol sa superconductors. Dapat na maayos na hawakan ng iyong pipeline ang matematika, mga talahanayan, tsart, mga footnote, at iba pa. Ipadala ang iyong mga naprosesong pahina sa aming email. Kung maganda ang hitsura, magpapadala kami sa iyo ng higit pa sa pribado, at inaasahan naming mabilis mong mapatakbo ang iyong pipeline sa mga iyon din. Kapag kami ay nasiyahan, maaari tayong makipagkasundo. - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Bersyong Tsino 中文版</a>, <a %(news_ycombinator)s>Talakayin sa Hacker News</a> Ito ay isang maikling post sa blog. Naghahanap kami ng ilang kumpanya o institusyon na tutulong sa amin sa OCR at pagkuha ng teksto para sa isang napakalaking koleksyon na nakuha namin, kapalit ng eksklusibong maagang access. Pagkatapos ng panahon ng embargo, siyempre ilalabas namin ang buong koleksyon. Ang mataas na kalidad na akademikong teksto ay lubos na kapaki-pakinabang para sa pagsasanay ng mga LLM. Bagamat ang aming koleksyon ay Tsino, ito ay magiging kapaki-pakinabang din para sa pagsasanay ng mga English LLM: tila ang mga modelo ay nag-eencode ng mga konsepto at kaalaman anuman ang pinagmulan ng wika. Para dito, kailangang makuha ang teksto mula sa mga scan. Ano ang makukuha ng Arkibo ni Anna mula rito? Buong-teksto na paghahanap ng mga libro para sa mga gumagamit nito. Dahil ang aming mga layunin ay umaayon sa mga developer ng LLM, naghahanap kami ng isang katuwang. Handa kaming bigyan ka ng <strong>eksklusibong maagang access sa koleksyong ito nang maramihan sa loob ng 1 taon</strong>, kung magagawa mo ang tamang OCR at pagkuha ng teksto. Kung handa kang ibahagi sa amin ang buong code ng iyong pipeline, handa kaming i-embargo ang koleksyon nang mas matagal. Eksklusibong access para sa mga kumpanya ng LLM sa pinakamalaking koleksyon ng mga aklat na hindi kathang-isip sa Tsina sa buong mundo <em><strong>TL;DR:</strong> Nakuha ng Arkibo ni Anna ang natatanging koleksyon ng 7.5 milyong / 350TB na mga aklat na hindi kathang-isip sa Tsina — mas malaki kaysa sa Library Genesis. Handa kaming magbigay ng eksklusibong access sa isang kumpanya ng LLM, kapalit ng mataas na kalidad na OCR at pagkuha ng teksto.</em> Arkitektura ng sistema Kaya sabihin nating nakahanap ka ng ilang mga kumpanya na handang i-host ang iyong website nang hindi ka isinasara — tawagin natin silang “mga tagapagbigay ng kalayaan” 😄. Mabilis mong matutuklasan na ang pagho-host ng lahat sa kanila ay medyo mahal, kaya maaaring gusto mong maghanap ng ilang “murang tagapagbigay” at gawin ang aktwal na pagho-host doon, na nagpo-proxy sa pamamagitan ng mga tagapagbigay ng kalayaan. Kung gagawin mo ito ng tama, hindi malalaman ng mga murang tagapagbigay kung ano ang iyong hinahost, at hindi makakatanggap ng anumang reklamo. Sa lahat ng mga tagapagbigay na ito ay may panganib na isara ka pa rin nila, kaya kailangan mo rin ng redundancy. Kailangan namin ito sa lahat ng antas ng aming stack. Isang medyo tagapagbigay ng kalayaan na naglagay ng sarili sa isang kawili-wiling posisyon ay ang Cloudflare. Sila ay <a %(blog_cloudflare)s>nag-argumento</a> na hindi sila isang hosting provider, kundi isang utility, tulad ng isang ISP. Samakatuwid, hindi sila sakop ng DMCA o iba pang mga kahilingan sa pagtanggal, at ipinapasa ang anumang mga kahilingan sa iyong aktwal na hosting provider. Sila ay umabot pa sa pagpunta sa korte upang protektahan ang istrukturang ito. Kaya maaari naming gamitin sila bilang isa pang layer ng caching at proteksyon. Hindi tumatanggap ang Cloudflare ng mga anonymous na pagbabayad, kaya maaari lamang naming gamitin ang kanilang libreng plano. Nangangahulugan ito na hindi namin magagamit ang kanilang load balancing o failover na mga tampok. Kaya't <a %(annas_archive_l255)s>ipinatupad namin ito mismo</a> sa antas ng domain. Sa pag-load ng pahina, susuriin ng browser kung ang kasalukuyang domain ay magagamit pa, at kung hindi, isusulat muli nito ang lahat ng mga URL sa ibang domain. Dahil ang Cloudflare ay nag-cache ng maraming mga pahina, nangangahulugan ito na ang isang gumagamit ay maaaring mapunta sa aming pangunahing domain, kahit na ang proxy server ay down, at pagkatapos sa susunod na pag-click ay ililipat sa ibang domain. Mayroon din kaming mga normal na operational na alalahanin na dapat harapin, tulad ng pagsubaybay sa kalusugan ng server, pag-log ng mga error sa backend at frontend, at iba pa. Ang aming failover na arkitektura ay nagbibigay-daan para sa higit na katatagan sa harap na ito rin, halimbawa sa pamamagitan ng pagpapatakbo ng isang ganap na naiibang set ng mga server sa isa sa mga domain. Maaari pa naming patakbuhin ang mga mas lumang bersyon ng code at datasets sa hiwalay na domain na ito, sakaling hindi mapansin ang isang kritikal na bug sa pangunahing bersyon. Maaari rin naming i-hedge laban sa Cloudflare na bumaligtad sa amin, sa pamamagitan ng pag-alis nito mula sa isa sa mga domain, tulad ng hiwalay na domain na ito. Iba't ibang mga permutasyon ng mga ideyang ito ay posible. Konklusyon Ito ay naging isang kawili-wiling karanasan na matutunan kung paano mag-set up ng isang matatag at matibay na search engine para sa shadow library. Marami pang detalye na maibabahagi sa mga susunod na post, kaya ipaalam sa akin kung ano ang nais mong malaman pa! Tulad ng dati, naghahanap kami ng mga donasyon upang suportahan ang gawaing ito, kaya siguraduhing tingnan ang Pahina ng Donasyon sa Arkibo ni Anna. Naghahanap din kami ng iba pang uri ng suporta, tulad ng mga grant, pangmatagalang sponsor, mga high-risk na payment provider, marahil kahit (maayos na!) mga ad. At kung nais mong mag-ambag ng iyong oras at kasanayan, palagi kaming naghahanap ng mga developer, tagasalin, at iba pa. Salamat sa iyong interes at suporta. Mga token ng inobasyon Simulan natin sa aming tech stack. Sadyang simple ito. Gumagamit kami ng Flask, MariaDB, at ElasticSearch. Iyan lang talaga. Ang paghahanap ay halos nalutas na problema, at hindi namin balak na muling imbentuhin ito. Bukod pa rito, kailangan naming gastusin ang aming <a %(mcfunley)s>mga token ng inobasyon</a> sa ibang bagay: hindi mapatigil ng mga awtoridad. Kaya gaano ka-legal o ilegal ang Anna’s Archive? Ito ay kadalasang nakadepende sa legal na hurisdiksyon. Karamihan sa mga bansa ay naniniwala sa ilang anyo ng copyright, na nangangahulugang ang mga tao o kumpanya ay binibigyan ng eksklusibong monopolyo sa ilang uri ng mga gawa para sa isang tiyak na panahon. Bilang isang tabi, sa Anna’s Archive naniniwala kami na habang may ilang benepisyo, sa kabuuan ang copyright ay isang net-negative para sa lipunan — ngunit iyan ay isang kwento para sa ibang pagkakataon. Ang eksklusibong monopolyo na ito sa ilang mga gawa ay nangangahulugang ilegal para sa sinuman sa labas ng monopolyo na ito na direktang ipamahagi ang mga gawaing iyon — kabilang kami. Ngunit ang Anna’s Archive ay isang search engine na hindi direktang namamahagi ng mga gawaing iyon (hindi bababa sa aming clearnet website), kaya dapat ay okay kami, tama ba? Hindi eksakto. Sa maraming hurisdiksyon, hindi lamang ilegal na ipamahagi ang mga copyrighted na gawa, kundi pati na rin ang mag-link sa mga lugar na gumagawa nito. Isang klasikong halimbawa nito ay ang batas ng DMCA ng Estados Unidos. Iyan ang pinakamahigpit na dulo ng spectrum. Sa kabilang dulo ng spectrum ay maaaring teoretikal na may mga bansa na walang batas sa copyright, ngunit ang mga ito ay talagang hindi umiiral. Halos lahat ng bansa ay may ilang anyo ng batas sa copyright. Ang pagpapatupad ay ibang kwento. Maraming mga bansa na ang mga gobyerno ay hindi nagmamalasakit na ipatupad ang batas sa copyright. Mayroon ding mga bansa sa pagitan ng dalawang ekstremong ito, na nagbabawal sa pamamahagi ng mga copyrighted na gawa, ngunit hindi nagbabawal sa pag-link sa mga ganoong gawa. Isa pang konsiderasyon ay sa antas ng kumpanya. Kung ang isang kumpanya ay nagpapatakbo sa isang hurisdiksyon na hindi nagmamalasakit sa copyright, ngunit ang kumpanya mismo ay hindi handang kumuha ng anumang panganib, maaari nilang isara ang iyong website sa sandaling may magreklamo tungkol dito. Sa wakas, isang malaking konsiderasyon ay ang mga pagbabayad. Dahil kailangan naming manatiling anonymous, hindi namin magagamit ang tradisyunal na mga paraan ng pagbabayad. Ito ay nag-iiwan sa amin ng mga cryptocurrencies, at isang maliit na subset lamang ng mga kumpanya ang sumusuporta sa mga ito (may mga virtual debit card na binabayaran ng crypto, ngunit madalas na hindi tinatanggap). - Anna at ang koponan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Pinapatakbo ko ang <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>, ang pinakamalaking open-source non-profit na search engine sa mundo para sa <a %(wikipedia_shadow_library)s>mga shadow library</a>, tulad ng Sci-Hub, Library Genesis, at Z-Library. Ang aming layunin ay gawing madaling ma-access ang kaalaman at kultura, at sa huli ay bumuo ng isang komunidad ng mga tao na sama-samang nag-a-archive at nagpepreserba ng <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>lahat ng mga libro sa mundo</a>. Sa artikulong ito, ipapakita ko kung paano namin pinapatakbo ang website na ito, at ang mga natatanging hamon na kasama ng pagpapatakbo ng isang website na may kaduda-dudang legal na katayuan, dahil walang “AWS para sa mga shadow charities”. <em>Tingnan din ang kapatid na artikulo <a %(blog_how_to_become_a_pirate_archivist)s>Paano maging isang piratang archivist</a>.</em> Paano patakbuhin ang isang shadow library: mga operasyon sa Arkibo ni Anna Walang <q>AWS para sa mga shadow charities,</q> kaya paano namin pinapatakbo ang Arkibo ni Anna? Mga kasangkapan Application server: Flask, MariaDB, ElasticSearch, Docker. Pag-unlad: Gitlab, Weblate, Zulip. Pamamahala ng server: Ansible, Checkmk, UFW. Onion static hosting: Tor, Nginx. Proxy server: Varnish. Tingnan natin kung anong mga kasangkapan ang ginagamit namin upang makamit ang lahat ng ito. Ito ay patuloy na umuunlad habang nakakaranas kami ng mga bagong problema at nakakahanap ng mga bagong solusyon. May ilang mga desisyon na paulit-ulit naming pinag-isipan. Isa na rito ang komunikasyon sa pagitan ng mga server: dati naming ginagamit ang Wireguard para dito, ngunit natuklasan naming paminsan-minsan itong humihinto sa pagpapadala ng anumang data, o nagpapadala lamang ng data sa isang direksyon. Nangyari ito sa ilang iba't ibang setup ng Wireguard na sinubukan namin, tulad ng <a %(github_costela_wesher)s>wesher</a> at <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Sinubukan din naming i-tunnel ang mga port sa pamamagitan ng SSH, gamit ang autossh at sshuttle, ngunit nagkaroon kami ng <a %(github_sshuttle)s>mga problema doon</a> (bagaman hindi pa rin malinaw sa akin kung ang autossh ay nagkakaroon ng mga isyu sa TCP-over-TCP o hindi — parang hindi maayos na solusyon ito sa akin ngunit baka ayos lang naman?). Sa halip, bumalik kami sa direktang koneksyon sa pagitan ng mga server, itinatago na may server na tumatakbo sa murang mga provider gamit ang IP-filtering sa UFW. Ang downside nito ay hindi maganda ang pagkaka-ugnay ng Docker sa UFW, maliban kung gagamitin mo ang <code>network_mode: "host"</code>. Lahat ng ito ay medyo mas madaling magkamali, dahil maaring ma-expose mo ang iyong server sa internet sa isang maliit na maling configuration. Marahil ay dapat kaming bumalik sa autossh — ang feedback ay lubos na tatanggapin dito. Nagpalit-palit din kami sa pagitan ng Varnish at Nginx. Sa kasalukuyan, gusto namin ang Varnish, ngunit mayroon itong mga quirks at magaspang na gilid. Ang parehong bagay ay naaangkop sa Checkmk: hindi namin ito gusto, ngunit gumagana ito sa ngayon. Ang Weblate ay okay lang ngunit hindi kahanga-hanga — minsan natatakot akong mawala ang aking data tuwing sinusubukan kong i-sync ito sa aming git repo. Ang Flask ay naging maganda sa kabuuan, ngunit mayroon itong ilang kakaibang quirks na nagdulot ng maraming oras sa pag-debug, tulad ng pag-configure ng custom na mga domain, o mga isyu sa SqlAlchemy integration nito. Sa ngayon, ang iba pang mga tool ay naging mahusay: wala kaming seryosong reklamo tungkol sa MariaDB, ElasticSearch, Gitlab, Zulip, Docker, at Tor. Lahat ng ito ay nagkaroon ng ilang mga isyu, ngunit wala namang sobrang seryoso o kumakain ng oras. Komunidad Ang unang hamon ay maaaring nakakagulat. Hindi ito isang teknikal na problema, o isang legal na problema. Ito ay isang sikolohikal na problema: ang paggawa ng trabahong ito sa anino ay maaaring maging lubhang malungkot. Depende sa kung ano ang plano mong gawin, at ang iyong modelo ng banta, maaaring kailanganin mong maging napakaingat. Sa isang dulo ng spectrum ay may mga tao tulad ni Alexandra Elbakyan*, ang tagapagtatag ng Sci-Hub, na napaka-bukas tungkol sa kanyang mga gawain. Ngunit siya ay nasa mataas na panganib na maaresto kung bibisita siya sa isang kanlurang bansa sa puntong ito, at maaaring makaharap ng mga dekada ng pagkakakulong. Iyon ba ay isang panganib na handa mong kunin? Kami ay nasa kabilang dulo ng spectrum; napakaingat na huwag mag-iwan ng anumang bakas, at may malakas na operational security. * Tulad ng nabanggit sa HN ni "ynno", si Alexandra ay hindi gustong makilala: "Ang kanyang mga server ay naka-set up upang maglabas ng detalyadong mga mensahe ng error mula sa PHP, kabilang ang buong landas ng nagkakamaling source file, na nasa ilalim ng direktoryo /home/<USER>" Kaya, gumamit ng mga random na username sa mga computer na ginagamit mo para sa mga bagay na ito, sakaling magkamali ka ng pag-configure ng isang bagay. Ang lihim na iyon, gayunpaman, ay may kasamang sikolohikal na gastos. Karamihan sa mga tao ay gustong makilala para sa trabahong ginagawa nila, at gayunpaman hindi mo maaaring kunin ang anumang kredito para dito sa totoong buhay. Kahit na ang mga simpleng bagay ay maaaring maging hamon, tulad ng mga kaibigan na nagtatanong sa iyo kung ano ang iyong ginagawa (sa ilang punto "paglalaro sa aking NAS / homelab" ay nagiging luma). Ito ang dahilan kung bakit napakahalaga na makahanap ng ilang komunidad. Maaari kang magbigay ng kaunting operational security sa pamamagitan ng pagtitiwala sa ilang napakalapit na kaibigan, na alam mong maaari mong lubos na pagkatiwalaan. Kahit na sa ganoong sitwasyon, mag-ingat na huwag maglagay ng anumang bagay sa pagsulat, sakaling kailanganin nilang ibigay ang kanilang mga email sa mga awtoridad, o kung ang kanilang mga device ay nakompromiso sa ibang paraan. Mas mabuti pa ay makahanap ng ilang kapwa pirata. Kung ang iyong malalapit na kaibigan ay interesado sa pagsali sa iyo, mahusay! Kung hindi, maaari kang makahanap ng iba online. Sa kasamaang palad, ito ay isang niche na komunidad pa rin. Sa ngayon, nakahanap lamang kami ng ilang iba pa na aktibo sa espasyong ito. Ang mga magagandang panimulang lugar ay tila ang mga forum ng Library Genesis, at r/DataHoarder. Ang Archive Team ay mayroon ding mga indibidwal na may kaparehong pag-iisip, kahit na sila ay gumagana sa loob ng batas (kahit na sa ilang mga grey na lugar ng batas). Ang tradisyonal na "warez" at pirating scenes ay mayroon ding mga tao na nag-iisip sa katulad na paraan. Bukas kami sa mga ideya kung paano mapalago ang komunidad at tuklasin ang mga ideya. Huwag mag-atubiling magpadala ng mensahe sa amin sa Twitter o Reddit. Marahil ay maaari kaming mag-host ng isang uri ng forum o chat group. Isang hamon ay madali itong ma-censor kapag gumagamit ng mga karaniwang platform, kaya kailangan naming i-host ito mismo. Mayroon ding tradeoff sa pagitan ng pagkakaroon ng mga talakayang ito na ganap na pampubliko (mas maraming potensyal na pakikipag-ugnayan) laban sa paggawa nito nang pribado (hindi ipaalam sa mga potensyal na "target" na malapit na naming i-scrape ang mga ito). Kailangan naming pag-isipan iyon. Ipaalam sa amin kung interesado ka dito! Konklusyon Sana makatulong ito para sa mga nagsisimulang piratang arkibista. Kami ay nasasabik na tanggapin kayo sa mundong ito, kaya huwag mag-atubiling makipag-ugnayan. I-preserba natin ang kaalaman at kultura ng mundo hangga't maaari, at i-mirror ito sa malayo at malawak. Mga Proyekto 4. Pagpili ng Data Madalas mong magagamit ang metadata upang malaman ang isang makatwirang subset ng data na ida-download. Kahit na sa huli ay nais mong i-download ang lahat ng data, maaaring maging kapaki-pakinabang na unahin ang pinakamahalagang mga item muna, sakaling ikaw ay ma-detect at mapabuti ang mga depensa, o dahil kailangan mong bumili ng mas maraming disk, o simpleng dahil may ibang bagay na dumating sa iyong buhay bago mo ma-download ang lahat. Halimbawa, ang isang koleksyon ay maaaring magkaroon ng maraming edisyon ng parehong pangunahing mapagkukunan (tulad ng isang libro o pelikula), kung saan ang isa ay minarkahan bilang may pinakamagandang kalidad. Ang pag-save ng mga edisyong iyon muna ay may katuturan. Maaaring gusto mong i-save ang lahat ng edisyon sa huli, dahil sa ilang mga kaso ang metadata ay maaaring maling na-tag, o maaaring may mga hindi kilalang tradeoffs sa pagitan ng mga edisyon (halimbawa, ang "pinakamahusay na edisyon" ay maaaring pinakamahusay sa karamihan ng mga paraan ngunit mas masama sa iba pang mga paraan, tulad ng isang pelikula na may mas mataas na resolusyon ngunit nawawala ang mga subtitle). Maaari mo ring hanapin ang iyong metadata database upang makahanap ng mga kawili-wiling bagay. Ano ang pinakamalaking file na naka-host, at bakit ito napakalaki? Ano ang pinakamaliit na file? Mayroon bang mga kawili-wili o hindi inaasahang pattern pagdating sa ilang mga kategorya, wika, at iba pa? Mayroon bang mga duplicate o napaka-katulad na mga pamagat? Mayroon bang mga pattern kung kailan idinagdag ang data, tulad ng isang araw kung saan maraming mga file ang idinagdag nang sabay-sabay? Madalas kang makakakuha ng maraming kaalaman sa pamamagitan ng pagtingin sa dataset sa iba't ibang paraan. Sa aming kaso, nag-deduplicate kami ng mga libro mula sa Z-Library laban sa mga md5 hash sa Library Genesis, sa gayon ay nakakatipid ng maraming oras sa pag-download at espasyo sa disk. Ito ay isang medyo natatanging sitwasyon. Sa karamihan ng mga kaso, walang komprehensibong mga database ng kung aling mga file ang maayos na napreserba na ng mga kapwa pirata. Ito mismo ay isang malaking pagkakataon para sa isang tao diyan. Magiging maganda ang magkaroon ng regular na na-update na pangkalahatang-ideya ng mga bagay tulad ng musika at pelikula na malawak nang na-seed sa mga torrent website, at samakatuwid ay mas mababang prayoridad na isama sa mga pirate mirror. 6. Pamamahagi Mayroon ka nang data, sa gayon ay nagkakaroon ka ng pag-aari ng unang pirate mirror ng iyong target (malamang). Sa maraming paraan, ang pinakamahirap na bahagi ay tapos na, ngunit ang pinaka-mapanganib na bahagi ay nasa unahan mo pa. Pagkatapos ng lahat, sa ngayon ay naging stealth ka; lumilipad sa ilalim ng radar. Ang kailangan mo lang gawin ay gumamit ng magandang VPN sa buong proseso, hindi punan ang iyong personal na detalye sa anumang mga form (duh), at marahil gumamit ng espesyal na session ng browser (o kahit ibang computer). Ngayon kailangan mong ipamahagi ang data. Sa aming kaso, una naming nais na ibalik ang mga libro sa Library Genesis, ngunit mabilis naming natuklasan ang mga kahirapan sa iyon (fiction vs non-fiction sorting). Kaya't nagpasya kami sa pamamahagi gamit ang mga torrent na istilo ng Library Genesis. Kung mayroon kang pagkakataon na mag-ambag sa isang umiiral na proyekto, maaari kang makatipid ng maraming oras. Gayunpaman, hindi marami ang mga maayos na organisadong pirate mirror sa kasalukuyan. Kaya't sabihin nating nagpasya kang ipamahagi ang mga torrent mismo. Subukang panatilihing maliit ang mga file na iyon, upang madali silang ma-mirror sa ibang mga website. Kakailanganin mong i-seed ang mga torrent mismo, habang nananatiling anonymous. Maaari kang gumamit ng VPN (na may o walang port forwarding), o magbayad gamit ang tumbled Bitcoins para sa isang Seedbox. Kung hindi mo alam ang ilan sa mga terminong iyon, magkakaroon ka ng maraming babasahin, dahil mahalaga na maunawaan mo ang mga panganib na tradeoffs dito. Maaari mong i-host ang mga torrent file mismo sa mga umiiral na torrent website. Sa aming kaso, pinili naming aktwal na mag-host ng isang website, dahil nais din naming ipalaganap ang aming pilosopiya sa isang malinaw na paraan. Maaari mong gawin ito sa iyong sarili sa isang katulad na paraan (ginagamit namin ang Njalla para sa aming mga domain at hosting, na binabayaran gamit ang tumbled Bitcoins), ngunit huwag mag-atubiling makipag-ugnayan sa amin upang kami ang mag-host ng iyong mga torrent. Naghahanap kami na bumuo ng isang komprehensibong index ng mga pirate mirror sa paglipas ng panahon, kung ang ideyang ito ay makakuha ng traksyon. Tungkol sa pagpili ng VPN, marami na ang naisulat tungkol dito, kaya't uulitin lang namin ang pangkalahatang payo ng pagpili batay sa reputasyon. Ang mga aktwal na court-tested na no-log policies na may mahabang track record ng pagprotekta sa privacy ay ang pinakamababang panganib na opsyon, sa aming opinyon. Tandaan na kahit na gawin mo ang lahat ng tama, hindi mo kailanman maabot ang zero na panganib. Halimbawa, kapag nag-seed ng iyong mga torrent, ang isang lubos na motivated na nation-state actor ay malamang na makatingin sa papasok at papalabas na mga daloy ng data para sa mga VPN server, at matukoy kung sino ka. O maaari ka lang magkamali sa isang paraan. Marahil ay nagawa na namin, at gagawin muli. Sa kabutihang palad, ang mga nation state ay hindi gaanong nagmamalasakit <em>sa</em> piracy. Isang desisyon na gagawin para sa bawat proyekto, ay kung ilalathala ito gamit ang parehong pagkakakilanlan tulad ng dati, o hindi. Kung patuloy mong ginagamit ang parehong pangalan, ang mga pagkakamali sa operational security mula sa mga naunang proyekto ay maaaring bumalik upang kagatin ka. Ngunit ang pag-publish sa ilalim ng iba't ibang mga pangalan ay nangangahulugan na hindi ka makakabuo ng mas matagal na reputasyon. Pinili naming magkaroon ng malakas na operational security mula sa simula upang patuloy naming magamit ang parehong pagkakakilanlan, ngunit hindi kami mag-aatubiling mag-publish sa ilalim ng ibang pangalan kung magkamali kami o kung ang mga pangyayari ay tumawag para dito. Ang pagpapalaganap ng balita ay maaaring maging mahirap. Tulad ng sinabi namin, ito ay isang niche na komunidad pa rin. Orihinal kaming nag-post sa Reddit, ngunit talagang nakakuha ng traksyon sa Hacker News. Sa ngayon ang aming rekomendasyon ay i-post ito sa ilang mga lugar at tingnan kung ano ang mangyayari. At muli, makipag-ugnayan sa amin. Gusto naming ipalaganap ang balita ng higit pang mga pagsisikap sa pirate archivism. 1. Pagpili ng domain / pilosopiya Walang kakulangan ng kaalaman at pamana ng kultura na dapat iligtas, na maaaring maging napakalaki. Iyon ang dahilan kung bakit madalas na kapaki-pakinabang na maglaan ng sandali at isipin kung ano ang maaaring maging kontribusyon mo. Ang bawat isa ay may iba't ibang paraan ng pag-iisip tungkol dito, ngunit narito ang ilang mga tanong na maaari mong itanong sa iyong sarili: Sa aming kaso, partikular kaming nagmamalasakit sa pangmatagalang pag-iingat ng agham. Alam namin ang tungkol sa Library Genesis, at kung paano ito ganap na ginaya nang maraming beses gamit ang mga torrents. Gustung-gusto namin ang ideyang iyon. Pagkatapos isang araw, sinubukan ng isa sa amin na maghanap ng ilang mga aklat-aralin sa agham sa Library Genesis, ngunit hindi mahanap ang mga ito, na nagdududa kung gaano ito kumpleto. Pagkatapos ay hinanap namin ang mga aklat-aralin na iyon online, at natagpuan ang mga ito sa ibang mga lugar, na nagtanim ng binhi para sa aming proyekto. Kahit bago pa namin nalaman ang tungkol sa Z-Library, mayroon kaming ideya na hindi subukang kolektahin ang lahat ng mga aklat na iyon nang manu-mano, ngunit magtuon sa pag-mirror ng mga umiiral na koleksyon, at ibalik ang mga ito sa Library Genesis. Anong mga kasanayan ang mayroon ka na maaari mong gamitin sa iyong benepisyo? Halimbawa, kung ikaw ay isang eksperto sa online na seguridad, maaari kang makahanap ng mga paraan upang talunin ang mga IP block para sa mga secure na target. Kung mahusay ka sa pag-oorganisa ng mga komunidad, marahil ay maaari mong hikayatin ang ilang mga tao na magkaisa sa isang layunin. Kapaki-pakinabang na malaman ang ilang programming, kahit na para lamang mapanatili ang magandang operational security sa buong prosesong ito. Ano ang magiging isang mataas na leverage na lugar upang magtuon? Kung gagastos ka ng X oras sa pirate archiving, paano mo makukuha ang pinakamalaking "bang for your buck"? Ano ang mga natatanging paraan na iniisip mo tungkol dito? Maaaring mayroon kang ilang mga kawili-wiling ideya o diskarte na maaaring napalampas ng iba. Gaano karaming oras ang mayroon ka para dito? Ang aming payo ay magsimula sa maliit at gumawa ng mas malalaking proyekto habang nasasanay ka, ngunit maaari itong maging lahat ng nakakaubos. Bakit ka interesado dito? Ano ang kinahihiligan mo? Kung makakakuha tayo ng isang grupo ng mga tao na lahat ay nag-a-archive ng mga uri ng bagay na partikular nilang pinapahalagahan, iyon ay makakabuo ng marami! Malalaman mo ang higit pa kaysa sa karaniwang tao tungkol sa iyong hilig, tulad ng kung ano ang mahalagang data na dapat iligtas, ano ang pinakamahusay na mga koleksyon at online na komunidad, at iba pa. 3. Metadata scraping Petsa ng pagdagdag/pagbabago: upang makabalik ka mamaya at ma-download ang mga file na hindi mo na-download dati (bagaman madalas mo ring magamit ang ID o hash para dito). Hash (md5, sha1): upang kumpirmahin na na-download mo ang file nang maayos. ID: maaaring ilang internal na ID, ngunit ang mga ID tulad ng ISBN o DOI ay kapaki-pakinabang din. Filename / lokasyon Paglalarawan, kategorya, mga tag, mga may-akda, wika, atbp. Laki: upang makalkula kung gaano karaming disk space ang kailangan mo. Magiging mas teknikal tayo dito. Para sa aktwal na pag-scrape ng metadata mula sa mga website, pinanatili naming simple ang mga bagay. Gumagamit kami ng mga Python script, minsan curl, at isang MySQL database upang i-store ang mga resulta. Hindi kami gumamit ng anumang magarbong scraping software na makakapag-map ng mga kumplikadong website, dahil sa ngayon kailangan lang naming i-scrape ang isa o dalawang uri ng mga pahina sa pamamagitan ng pag-enumerate sa mga id at pag-parse ng HTML. Kung walang madaling ma-enumerate na mga pahina, maaaring kailanganin mo ng tamang crawler na susubukang hanapin ang lahat ng mga pahina. Bago ka magsimula sa pag-scrape ng buong website, subukan mo munang gawin ito nang manu-mano. Dumaan ka sa ilang dosenang pahina mismo, upang makakuha ng pakiramdam kung paano ito gumagana. Minsan makakaranas ka na ng mga IP block o iba pang kawili-wiling pag-uugali sa ganitong paraan. Ganito rin sa data scraping: bago masyadong lumalim sa target na ito, tiyakin na maaari mong aktwal na i-download ang data nito nang epektibo. Upang malampasan ang mga limitasyon, may ilang bagay na maaari mong subukan. Mayroon bang iba pang mga IP address o server na nagho-host ng parehong data ngunit walang parehong mga limitasyon? Mayroon bang mga API endpoint na walang mga limitasyon, habang ang iba ay mayroon? Sa anong rate ng pag-download ang iyong IP ay na-block, at gaano katagal? O hindi ka ba na-block ngunit na-throttle pababa? Paano kung lumikha ka ng user account, paano nagbabago ang mga bagay? Maaari mo bang gamitin ang HTTP/2 upang panatilihing bukas ang mga koneksyon, at pinapataas ba nito ang rate kung saan maaari kang humiling ng mga pahina? Mayroon bang mga pahina na naglilista ng maraming file nang sabay-sabay, at sapat ba ang impormasyong nakalista doon? Mga bagay na marahil nais mong i-save ay kinabibilangan ng: Karaniwan naming ginagawa ito sa dalawang yugto. Una, ida-download namin ang raw HTML files, karaniwang direkta sa MySQL (upang maiwasan ang maraming maliliit na file, na pag-uusapan pa natin sa ibaba). Pagkatapos, sa isang hiwalay na hakbang, dadaanan namin ang mga HTML file na iyon at i-parse ang mga ito sa aktwal na mga MySQL table. Sa ganitong paraan hindi mo kailangang i-re-download ang lahat mula sa simula kung makakita ka ng pagkakamali sa iyong parsing code, dahil maaari mo lamang i-reprocess ang mga HTML file gamit ang bagong code. Madalas din na mas madali ang pag-parallelize ng processing step, kaya nakakatipid ng oras (at maaari mong isulat ang processing code habang tumatakbo ang scraping, sa halip na kailangang isulat ang parehong mga hakbang nang sabay). Sa wakas, tandaan na para sa ilang target, ang metadata scraping lang ang mayroon. Mayroong ilang malalaking koleksyon ng metadata na hindi maayos na napreserba. Pamagat Pagpili ng domain / pilosopiya: Saan mo gustong magtuon, at bakit? Ano ang iyong natatanging mga hilig, kasanayan, at kalagayan na maaari mong gamitin sa iyong benepisyo? Pagpili ng target: Aling partikular na koleksyon ang iyong gagayahin? Metadata scraping: Pagkatalogo ng impormasyon tungkol sa mga file, nang hindi aktwal na dina-download ang (madalas na mas malalaking) mga file mismo. Pagpili ng data: Batay sa metadata, pinipili kung aling data ang pinaka-nauugnay na i-archive ngayon. Maaaring lahat, ngunit madalas may makatwirang paraan upang makatipid ng espasyo at bandwidth. Data scraping: Aktwal na pagkuha ng data. Pamamahagi: Pag-iimpake nito sa mga torrents, pag-aanunsyo nito sa isang lugar, pagkuha ng mga tao upang ipalaganap ito. 5. Pag-scrape ng Data Ngayon handa ka nang aktwal na i-download ang data nang maramihan. Tulad ng nabanggit dati, sa puntong ito dapat mo nang manu-manong na-download ang isang bungkos ng mga file, upang mas maunawaan ang pag-uugali at mga limitasyon ng target. Gayunpaman, magkakaroon pa rin ng mga sorpresa para sa iyo kapag aktwal mong na-download ang maraming mga file nang sabay-sabay. Ang aming payo dito ay panatilihing simple ito. Magsimula sa pamamagitan ng pag-download lamang ng isang bungkos ng mga file. Maaari mong gamitin ang Python, at pagkatapos ay palawakin sa maraming mga thread. Ngunit kung minsan mas simple pa ay ang pagbuo ng mga Bash file nang direkta mula sa database, at pagkatapos ay patakbuhin ang marami sa mga ito sa maraming terminal windows upang mag-scale up. Isang mabilis na teknikal na trick na nagkakahalaga ng pagbanggit dito ay ang paggamit ng OUTFILE sa MySQL, na maaari mong isulat kahit saan kung i-disable mo ang "secure_file_priv" sa mysqld.cnf (at siguraduhing i-disable/override din ang AppArmor kung ikaw ay nasa Linux). Iniimbak namin ang data sa mga simpleng hard disk. Magsimula sa kung ano ang mayroon ka, at dahan-dahang mag-expand. Maaari itong maging napakalaki na isipin ang tungkol sa pag-iimbak ng daan-daang TB ng data. Kung iyon ang sitwasyon na iyong kinakaharap, ilabas lamang ang isang magandang subset muna, at sa iyong anunsyo humingi ng tulong sa pag-iimbak ng natitira. Kung nais mo talagang makakuha ng mas maraming hard drive para sa iyong sarili, ang r/DataHoarder ay may ilang magagandang mapagkukunan sa pagkuha ng magagandang deal. Subukang huwag masyadong mag-alala tungkol sa mga magarbong filesystem. Madaling mahulog sa butas ng kuneho ng pag-set up ng mga bagay tulad ng ZFS. Isang teknikal na detalye na dapat malaman, gayunpaman, ay ang maraming mga filesystem ay hindi mahusay na humahawak sa maraming mga file. Natuklasan namin na ang isang simpleng solusyon ay ang paglikha ng maraming mga direktoryo, halimbawa para sa iba't ibang mga saklaw ng ID o mga hash prefix. Pagkatapos i-download ang data, siguraduhing suriin ang integridad ng mga file gamit ang mga hash sa metadata, kung magagamit. 2. Pagpili ng target Madaling ma-access: hindi gumagamit ng maraming layer ng proteksyon upang pigilan kang i-scrape ang kanilang metadata at data. Espesyal na kaalaman: mayroon kang espesyal na impormasyon tungkol sa target na ito, tulad ng pagkakaroon ng espesyal na access sa koleksyon na ito, o natuklasan mo kung paano talunin ang kanilang depensa. Hindi ito kinakailangan (ang aming paparating na proyekto ay hindi gumagawa ng anumang espesyal), ngunit tiyak na nakakatulong ito! Malaki Kaya, mayroon tayong lugar na tinitingnan natin, ngayon aling partikular na koleksyon ang ating gagayahin? May ilang bagay na nagiging magandang target: Nang matagpuan namin ang aming mga aklat sa agham sa mga website maliban sa Library Genesis, sinubukan naming alamin kung paano sila nakarating sa internet. Pagkatapos ay natagpuan namin ang Z-Library, at napagtanto na habang karamihan sa mga aklat ay hindi unang lumalabas doon, sa kalaunan ay napupunta sila doon. Natutunan namin ang tungkol sa kaugnayan nito sa Library Genesis, at ang (pinansyal) na istruktura ng insentibo at mas mahusay na user interface, na parehong nagbigay-daan upang maging mas kumpletong koleksyon ito. Pagkatapos ay gumawa kami ng ilang paunang metadata at data scraping, at napagtanto na maaari naming malampasan ang kanilang IP download limits, gamit ang espesyal na access ng isa sa aming mga miyembro sa maraming proxy server. Habang nag-eexplore ka ng iba't ibang target, mahalaga na itago na ang iyong mga bakas sa pamamagitan ng paggamit ng VPNs at mga disposable na email address, na pag-uusapan pa natin mamaya. Natatangi: hindi pa masyadong sakop ng ibang mga proyekto. Kapag gumagawa kami ng proyekto, mayroon itong ilang mga yugto: Ang mga ito ay hindi ganap na independiyenteng mga yugto, at madalas na ang mga pananaw mula sa isang mas huling yugto ay nagbabalik sa iyo sa isang mas maagang yugto. Halimbawa, sa panahon ng metadata scraping maaari mong mapagtanto na ang target na iyong pinili ay may mga mekanismong depensibo na lampas sa iyong antas ng kasanayan (tulad ng mga IP block), kaya bumalik ka at maghanap ng ibang target. - Anna at ang koponan (<a %(reddit)s>Reddit</a>) Maaaring isulat ang buong mga libro tungkol sa <em>bakit</em> ng digital na pag-iingat sa pangkalahatan, at piratang arkibismo sa partikular, ngunit magbibigay kami ng mabilis na primer para sa mga hindi masyadong pamilyar. Ang mundo ay gumagawa ng mas maraming kaalaman at kultura kaysa dati, ngunit mas marami rin ang nawawala kaysa dati. Ang sangkatauhan ay kadalasang nagtitiwala sa mga korporasyon tulad ng mga akademikong publisher, mga serbisyo sa streaming, at mga kumpanya ng social media sa pamana na ito, at madalas silang hindi napatunayang mahusay na mga tagapangalaga. Tingnan ang dokumentaryong Digital Amnesia, o talagang anumang talumpati ni Jason Scott. May ilang mga institusyon na mahusay sa pag-archive ng mas marami hangga't maaari, ngunit sila ay nakatali sa batas. Bilang mga pirata, kami ay nasa isang natatanging posisyon upang i-archive ang mga koleksyon na hindi nila mahipo, dahil sa pagpapatupad ng copyright o iba pang mga paghihigpit. Maaari rin naming i-mirror ang mga koleksyon nang maraming beses, sa buong mundo, sa gayon ay pinapataas ang mga pagkakataon ng wastong pag-iingat. Sa ngayon, hindi kami makikipag-usap sa mga talakayan tungkol sa mga kalamangan at kahinaan ng intelektwal na ari-arian, ang moralidad ng paglabag sa batas, mga pagninilay sa censorship, o ang isyu ng access sa kaalaman at kultura. Sa lahat ng iyon, sumisid tayo sa <em>paano</em>. Ibahagi namin kung paano naging piratang arkibista ang aming koponan, at ang mga aral na natutunan namin sa daan. Maraming mga hamon kapag sinimulan mo ang paglalakbay na ito, at sana ay matulungan ka namin sa ilan sa mga ito. Paano maging isang piratang arkibista Ang unang hamon ay maaaring nakakagulat. Hindi ito isang teknikal na problema, o isang legal na problema. Ito ay isang sikolohikal na problema. Bago tayo sumisid, dalawang update sa Pirate Library Mirror (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>): Nakakuha kami ng ilang napakabukas-palad na donasyon. Ang una ay $10k mula sa isang hindi nagpapakilalang indibidwal na sumusuporta rin sa "bookwarrior", ang orihinal na tagapagtatag ng Library Genesis. Espesyal na pasasalamat kay bookwarrior para sa pagpapadali ng donasyong ito. Ang pangalawa ay isa pang $10k mula sa isang hindi nagpapakilalang donor, na nakipag-ugnayan pagkatapos ng aming huling release, at na-inspire na tumulong. Nagkaroon din kami ng ilang mas maliliit na donasyon. Maraming salamat sa lahat ng inyong bukas-palad na suporta. Mayroon kaming ilang kapana-panabik na bagong proyekto sa pipeline na susuportahan nito, kaya manatiling nakatutok. Nagkaroon kami ng ilang teknikal na kahirapan sa laki ng aming pangalawang release, ngunit ang aming mga torrent ay naka-up at nagse-seed na ngayon. Nakakuha rin kami ng isang bukas-palad na alok mula sa isang hindi nagpapakilalang indibidwal na i-seed ang aming koleksyon sa kanilang napakataas na bilis na mga server, kaya kami ay gumagawa ng isang espesyal na pag-upload sa kanilang mga makina, pagkatapos nito ang lahat ng iba pa na nagda-download ng koleksyon ay dapat makakita ng malaking pagbuti sa bilis. Mga post sa blog Kumusta, ako si Anna. Nilikha ko ang <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>, ang pinakamalaking shadow library sa mundo. Ito ang aking personal na blog, kung saan ako at ang aking mga kasamahan ay nagsusulat tungkol sa piracy, digital preservation, at iba pa. Makipag-ugnayan sa akin sa <a %(reddit)s>Reddit</a>. Tandaan na ang website na ito ay isang blog lamang. Dito lamang namin ina-host ang aming sariling mga salita. Walang mga torrent o iba pang copyrighted na file na ina-host o naka-link dito. <strong>Aklatan</strong> - Tulad ng karamihan sa mga aklatan, nakatuon kami sa mga nakasulat na materyales tulad ng mga aklat. Maaaring palawakin namin ito sa iba pang uri ng media sa hinaharap. <strong>Salamin</strong> - Kami ay mahigpit na salamin ng mga umiiral na aklatan. Nakatuon kami sa pagpepreserba, hindi sa paggawa ng mga aklat na madaling mahanap at ma-download (access) o sa pagbuo ng malaking komunidad ng mga tao na nag-aambag ng mga bagong aklat (sourcing). <strong>Pirata</strong> - Sinasadya naming labagin ang batas ng copyright sa karamihan ng mga bansa. Ito ay nagbibigay-daan sa amin na gawin ang isang bagay na hindi magawa ng mga legal na entidad: tiyakin na ang mga aklat ay na-mirror sa malayo at malawak. <em>Hindi kami nagli-link sa mga file mula sa blog na ito. Mangyaring hanapin ito mismo.</em> - Anna at ang koponan (<a %(reddit)s>Reddit</a>) Ang proyektong ito (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>) ay naglalayong mag-ambag sa pagpepreserba at paglaya ng kaalaman ng tao. Ginagawa namin ang aming maliit at mapagkumbabang ambag, sa mga yapak ng mga dakila bago kami. Ang pokus ng proyektong ito ay inilalarawan ng pangalan nito: Ang unang aklatan na aming na-mirror ay ang Z-Library. Ito ay isang popular (at ilegal) na aklatan. Kinuha nila ang koleksyon ng Library Genesis at ginawa itong madaling mahanap. Bukod pa rito, naging napaka-epektibo sila sa paghingi ng mga bagong kontribusyon ng aklat, sa pamamagitan ng pagbibigay ng mga insentibo sa mga nag-aambag na gumagamit ng iba't ibang benepisyo. Sa kasalukuyan, hindi nila ibinabalik ang mga bagong aklat na ito sa Library Genesis. At hindi tulad ng Library Genesis, hindi nila ginagawang madaling ma-mirror ang kanilang koleksyon, na pumipigil sa malawakang pagpepreserba. Mahalaga ito sa kanilang modelo ng negosyo, dahil naniningil sila ng pera para sa pag-access sa kanilang koleksyon nang maramihan (higit sa 10 aklat bawat araw). Hindi kami gumagawa ng mga moral na paghatol tungkol sa pagsingil ng pera para sa maramihang pag-access sa isang ilegal na koleksyon ng libro. Walang duda na naging matagumpay ang Z-Library sa pagpapalawak ng access sa kaalaman, at sa pagkuha ng mas maraming libro. Narito lamang kami upang gampanan ang aming bahagi: tiyakin ang pangmatagalang pag-iingat ng pribadong koleksyong ito. Nais naming anyayahan kayo na tumulong sa pagpepreserba at paglaya ng kaalaman ng tao sa pamamagitan ng pag-download at pag-seed ng aming mga torrents. Tingnan ang pahina ng proyekto para sa karagdagang impormasyon tungkol sa kung paano nakaayos ang data. Nais din naming anyayahan kayo na mag-ambag ng inyong mga ideya kung aling mga koleksyon ang susunod na i-mirror, at kung paano ito gagawin. Sama-sama nating makakamit ang marami. Ito ay isang maliit na ambag lamang sa napakaraming iba pa. Salamat, sa lahat ng inyong ginagawa. Ipinapakilala ang Pirate Library Mirror: Pagpepreserba ng 7TB ng mga aklat (na wala sa Libgen) 10% ong nakasulat na pamana ng sangkatauhan ay na-preserba magpakailanman <strong>Google.</strong> Pagkatapos ng lahat, ginawa nila ang pananaliksik na ito para sa Google Books. Gayunpaman, ang kanilang metadata ay hindi naa-access sa bulk at medyo mahirap i-scrape. <strong>Iba't ibang indibidwal na sistema ng aklatan at archive.</strong> May mga aklatan at archive na hindi na-index at pinagsama-sama ng alinman sa mga nabanggit sa itaas, kadalasan dahil sila ay kulang sa pondo, o sa iba pang mga dahilan ayaw nilang ibahagi ang kanilang data sa Open Library, OCLC, Google, at iba pa. Marami sa mga ito ay may mga digital na talaan na naa-access sa pamamagitan ng internet, at madalas na hindi masyadong protektado, kaya kung nais mong tumulong at magkaroon ng kasiyahan sa pag-aaral tungkol sa kakaibang mga sistema ng aklatan, ito ay mahusay na mga panimulang punto. <strong>ISBNdb.</strong> Ito ang paksa ng blog post na ito. Ang ISBNdb ay nag-scrape ng iba't ibang mga website para sa metadata ng libro, partikular na ang data ng pagpepresyo, na kanilang ibinebenta sa mga bookseller, upang maipresyo nila ang kanilang mga libro alinsunod sa natitirang bahagi ng merkado. Dahil ang mga ISBN ay medyo unibersal ngayon, epektibo nilang binuo ang isang “web page para sa bawat libro”. <strong>Open Library.</strong> Tulad ng nabanggit kanina, ito ang kanilang buong misyon. Nakakuha sila ng napakalaking dami ng data ng aklatan mula sa mga nakikipagtulungan na mga aklatan at pambansang archive, at patuloy na ginagawa ito. Mayroon din silang mga volunteer librarian at isang teknikal na koponan na sinusubukang i-deduplicate ang mga talaan, at i-tag ang mga ito ng iba't ibang uri ng metadata. Ang pinakamaganda sa lahat, ang kanilang dataset ay ganap na bukas. Maaari mo lamang itong <a %(openlibrary)s>i-download</a>. <strong>WorldCat.</strong> Ito ay isang website na pinapatakbo ng non-profit na OCLC, na nagbebenta ng mga sistema ng pamamahala ng aklatan. Sinasama nila ang metadata ng libro mula sa maraming aklatan, at ginagawa itong magagamit sa pamamagitan ng website ng WorldCat. Gayunpaman, kumikita rin sila sa pagbebenta ng data na ito, kaya hindi ito magagamit para sa bulk download. Mayroon silang ilang mas limitadong bulk datasets na magagamit para sa pag-download, sa pakikipagtulungan sa mga tiyak na aklatan. 1. Para sa ilang makatwirang kahulugan ng "magpakailanman". ;) 2. Siyempre, ang nakasulat na pamana ng sangkatauhan ay higit pa sa mga aklat, lalo na sa kasalukuyan. Para sa kapakanan ng post na ito at ng aming mga kamakailang paglabas, nakatuon kami sa mga aklat, ngunit ang aming mga interes ay umaabot pa. 3. Marami pang maaaring sabihin tungkol kay Aaron Swartz, ngunit nais lamang naming banggitin siya ng maikli, dahil siya ay may mahalagang bahagi sa kuwentong ito. Habang lumilipas ang panahon, mas maraming tao ang maaaring makatagpo ng kanyang pangalan sa unang pagkakataon, at maaaring sumisid sa butas ng kuneho mismo. <strong>Pisikal na kopya.</strong> Maliwanag na hindi ito masyadong nakakatulong, dahil mga duplicate lang ito ng parehong materyal. Magiging maganda kung maipapreserba natin ang lahat ng anotasyon na ginagawa ng mga tao sa mga libro, tulad ng sikat na “mga sulat sa gilid” ni Fermat. Ngunit sa kasamaang palad, mananatili itong pangarap ng isang archivist. <strong>“Mga Edisyon”.</strong> Dito binibilang mo ang bawat natatanging bersyon ng isang libro. Kung may anumang pagkakaiba dito, tulad ng ibang pabalat o ibang paunang salita, ito ay binibilang bilang ibang edisyon. <strong>Mga File.</strong> Kapag nagtatrabaho sa mga shadow library tulad ng Library Genesis, Sci-Hub, o Z-Library, may karagdagang konsiderasyon. Maaaring may maraming scan ng parehong edisyon. At ang mga tao ay maaaring gumawa ng mas mahusay na bersyon ng mga umiiral na file, sa pamamagitan ng pag-scan ng teksto gamit ang OCR, o pagwawasto ng mga pahina na na-scan sa isang anggulo. Gusto naming bilangin lamang ang mga file na ito bilang isang edisyon, na mangangailangan ng magandang metadata, o deduplication gamit ang mga sukat ng pagkakatulad ng dokumento. <strong>“Mga Gawa”.</strong> Halimbawa, “Harry Potter and the Chamber of Secrets” bilang isang lohikal na konsepto, na sumasaklaw sa lahat ng bersyon nito, tulad ng iba't ibang pagsasalin at reprints. Ito ay isang uri ng kapaki-pakinabang na depinisyon, ngunit maaaring mahirap tukuyin kung ano ang bibilangin. Halimbawa, marahil gusto nating ipreserba ang iba't ibang pagsasalin, bagaman ang mga reprints na may kaunting pagkakaiba ay maaaring hindi gaanong mahalaga. - Anna at ang koponan (<a %(reddit)s>Reddit</a>) Sa Pirate Library Mirror (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>), ang aming layunin ay kunin ang lahat ng mga aklat sa mundo, at i-preserba ang mga ito magpakailanman.<sup>1</sup> Sa pagitan ng aming mga Z-Library torrents, at ang orihinal na Library Genesis torrents, mayroon kaming 11,783,153 na mga file. Ngunit gaano karami iyon, talaga? Kung maayos nating ma-deduplicate ang mga file na iyon, anong porsyento ng lahat ng mga aklat sa mundo ang na-preserba natin? Talagang nais naming magkaroon ng ganito: Magsimula tayo sa ilang magaspang na numero: Sa parehong Z-Library/Libgen at Open Library mayroong mas maraming libro kaysa sa natatanging mga ISBN. Ibig bang sabihin nito na maraming sa mga librong iyon ay walang ISBN, o ang metadata ng ISBN ay simpleng nawawala? Marahil ay masagot natin ang tanong na ito sa pamamagitan ng kumbinasyon ng automated matching batay sa iba pang mga katangian (pamagat, may-akda, publisher, atbp), pagkuha ng mas maraming pinagmumulan ng data, at pagkuha ng mga ISBN mula sa aktwal na mga scan ng libro mismo (sa kaso ng Z-Library/Libgen). Gaano karami sa mga ISBN na iyon ang natatangi? Ito ay pinakamahusay na ilarawan sa isang Venn diagram: Upang maging mas tiyak: Nagulat kami sa kung gaano kaliit ang pagkakapareho! Ang ISBNdb ay may napakaraming ISBNs na hindi lumalabas sa Z-Library o Open Library, at ganoon din ang sitwasyon (sa mas maliit ngunit kapansin-pansin pa ring antas) para sa dalawa pa. Ito ay nagbubukas ng maraming bagong katanungan. Gaano kalaki ang maitutulong ng awtomatikong pagtutugma sa pag-tag ng mga aklat na hindi na-tag ng ISBNs? Magkakaroon ba ng maraming tugma at samakatuwid ay mas mataas na pagkakapareho? Gayundin, ano ang mangyayari kung magdadagdag tayo ng ika-4 o ika-5 dataset? Gaano karaming pagkakapareho ang makikita natin noon? Ito ay nagbibigay sa atin ng panimulang punto. Maaari na nating tingnan ang lahat ng ISBNs na wala sa dataset ng Z-Library, at na hindi rin tumutugma sa mga field ng pamagat/awtor. Ito ay makakatulong sa atin sa pagpreserba ng lahat ng aklat sa mundo: una sa pamamagitan ng pag-scrape sa internet para sa mga scan, pagkatapos ay sa pamamagitan ng pagpunta sa totoong buhay upang i-scan ang mga aklat. Ang huli ay maaari pang pondohan ng karamihan, o itulak ng mga "bounty" mula sa mga tao na nais makita ang partikular na mga aklat na ma-digitize. Ang lahat ng iyon ay isang kwento para sa ibang pagkakataon. Kung nais mong tumulong sa alinman sa mga ito — karagdagang pagsusuri; pag-scrape ng higit pang metadata; paghahanap ng higit pang mga aklat; pag-OCR ng mga aklat; paggawa nito para sa ibang mga domain (hal. mga papel, audiobooks, pelikula, palabas sa TV, magasin) o kahit na gawing magagamit ang ilan sa mga data na ito para sa mga bagay tulad ng ML / malalaking modelo ng wika — mangyaring makipag-ugnayan sa akin (<a %(reddit)s>Reddit</a>). Kung ikaw ay partikular na interesado sa pagsusuri ng data, kami ay nagtatrabaho sa paggawa ng aming mga dataset at script na magagamit sa mas madaling gamitin na format. Magiging maganda kung maaari mo lamang i-fork ang isang notebook at simulang maglaro nito. Sa wakas, kung nais mong suportahan ang gawaing ito, mangyaring isaalang-alang ang pagbibigay ng donasyon. Ito ay isang ganap na boluntaryong operasyon, at ang iyong kontribusyon ay may malaking pagkakaiba. Bawat bahagi ay nakakatulong. Sa ngayon, tumatanggap kami ng mga donasyon sa crypto; tingnan ang pahina ng Donasyon sa Arkibo ni Anna. Para sa isang porsyento, kailangan natin ng denominator: ang kabuuang bilang ng mga aklat na kailanman ay nailathala.<sup>2</sup> Bago ang pagbagsak ng Google Books, isang inhinyero sa proyekto, si Leonid Taycher, <a %(booksearch_blogspot)s>sinubukang tantiyahin</a> ang bilang na ito. Nakarating siya — pabiro — sa 129,864,880 (“hindi bababa hanggang Linggo”). Tinantya niya ang bilang na ito sa pamamagitan ng pagbuo ng isang pinag-isang database ng lahat ng mga aklat sa mundo. Para dito, pinagsama-sama niya ang iba't ibang datasets at pagkatapos ay pinagsama-sama ang mga ito sa iba't ibang paraan. Bilang isang mabilis na pagbanggit, may isa pang tao na nagtangkang i-catalog ang lahat ng libro sa mundo: si Aaron Swartz, ang yumaong digital activist at co-founder ng Reddit.<sup>3</sup> Sinimulan niya ang <a %(youtube)s>Open Library</a> na may layuning “isang web page para sa bawat librong nailathala”, pinagsasama-sama ang data mula sa iba't ibang pinagmulan. Sa huli, binayaran niya ang pinakamataas na halaga para sa kanyang digital preservation work nang siya ay makasuhan dahil sa bulk-downloading ng mga academic papers, na humantong sa kanyang pagpapakamatay. Hindi na kailangang sabihin, ito ay isa sa mga dahilan kung bakit ang aming grupo ay pseudonymous, at kung bakit kami ay nag-iingat. Ang Open Library ay patuloy na pinapatakbo ng mga tao sa Internet Archive, na ipinagpapatuloy ang pamana ni Aaron. Babalikan namin ito mamaya sa post na ito. Sa blog post ng Google, inilalarawan ni Taycher ang ilan sa mga hamon sa pagtatantiya ng bilang na ito. Una, ano ang bumubuo sa isang libro? May ilang posibleng depinisyon: Ang “Mga Edisyon” ay tila ang pinaka praktikal na depinisyon ng kung ano ang “mga libro”. Maginhawa, ang depinisyong ito ay ginagamit din para sa pagtatalaga ng natatanging mga numero ng ISBN. Ang isang ISBN, o International Standard Book Number, ay karaniwang ginagamit para sa internasyonal na kalakalan, dahil ito ay isinama sa internasyonal na sistema ng barcode (”International Article Number”). Kung nais mong magbenta ng libro sa mga tindahan, kailangan nito ng barcode, kaya makakakuha ka ng ISBN. Binanggit sa blog post ni Taycher na habang ang mga ISBN ay kapaki-pakinabang, hindi sila unibersal, dahil talagang na-adopt lamang sila noong kalagitnaan ng dekada '70, at hindi sa lahat ng dako sa mundo. Gayunpaman, ang ISBN ay marahil ang pinaka malawak na ginagamit na identifier ng mga edisyon ng libro, kaya ito ang aming pinakamahusay na panimulang punto. Kung mahanap natin ang lahat ng ISBN sa mundo, makakakuha tayo ng kapaki-pakinabang na listahan ng kung aling mga libro ang kailangan pang ipreserba. Kaya, saan natin makukuha ang data? May ilang umiiral na mga pagsisikap na sinusubukang magtipon ng listahan ng lahat ng mga libro sa mundo: Sa post na ito, masaya naming inihahayag ang isang maliit na release (kumpara sa aming mga nakaraang Z-Library releases). Na-scrape namin ang karamihan ng ISBNdb, at ginawa ang data na magagamit para sa torrenting sa website ng Pirate Library Mirror (EDIT: inilipat sa <a %(wikipedia_annas_archive)s>Anna’s Archive</a>; hindi namin ito direktang i-link dito, hanapin mo na lang). Ito ay humigit-kumulang 30.9 milyong talaan (20GB bilang <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). Sa kanilang website sinasabi nila na talagang mayroon silang 32.6 milyong talaan, kaya maaaring may ilang hindi namin nakuha, o <em>sila</em> ang maaaring may maling ginagawa. Sa anumang kaso, sa ngayon ay hindi namin ibabahagi kung paano namin ito ginawa — iiwan namin iyon bilang isang ehersisyo para sa mambabasa. ;-) Ang aming ibabahagi ay ilang paunang pagsusuri, upang subukang makalapit sa pagtatantiya ng bilang ng mga libro sa mundo. Tiningnan namin ang tatlong datasets: ang bagong dataset ng ISBNdb, ang aming orihinal na release ng metadata na na-scrape namin mula sa Z-Library shadow library (na kinabibilangan ng Library Genesis), at ang Open Library data dump. ISBNdb dump, o Ilang Aklat ang Na-preserba Magpakailanman? Kung maayos nating ma-deduplicate ang mga file mula sa mga shadow library, anong porsyento ng lahat ng mga aklat sa mundo ang na-preserba natin? Mga update tungkol sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>, ang pinakamalaking tunay na bukas na aklatan sa kasaysayan ng tao. <em>Pag-redesign ng WorldCat</em> Data <strong>Format?</strong> <a %(blog)s>Mga Sisidlan ng Arkibo ni Anna (AAC)</a>, na sa esensya ay <a %(jsonlines)s>JSON Lines</a> na naka-compress gamit ang <a %(zstd)s>Zstandard</a>, kasama ang ilang mga standardized na semantika. Ang mga sisidlan na ito ay naglalaman ng iba't ibang uri ng mga record, batay sa iba't ibang mga kinayod na data na aming ipinatupad. Isang taon na ang nakalipas, kami ay <a %(blog)s>nagsimula</a> upang sagutin ang tanong na ito: <strong>Anong porsyento ng mga aklat ang permanenteng napanatili ng mga shadow library?</strong> Tingnan natin ang ilang pangunahing impormasyon tungkol sa data: Kapag ang isang aklat ay nakapasok sa isang open-data shadow library tulad ng <a %(wikipedia_library_genesis)s>Library Genesis</a>, at ngayon sa <a %(wikipedia_annas_archive)s>Arkibo ni Anna</a>, ito ay na-mimirror sa buong mundo (sa pamamagitan ng torrents), kaya't praktikal na napapanatili ito magpakailanman. Upang masagot ang tanong kung anong porsyento ng mga aklat ang napanatili, kailangan nating malaman ang denominator: ilan ang kabuuang bilang ng mga aklat? At sa ideal, hindi lamang tayo may numero, kundi aktwal na metadata. Pagkatapos ay maaari nating hindi lamang itugma ang mga ito laban sa mga shadow library, kundi pati na rin <strong>gumawa ng isang TODO list ng mga natitirang aklat na dapat mapanatili!</strong> Maaari pa tayong magsimulang mangarap ng isang crowdsourced na pagsisikap upang bumaba sa TODO list na ito. Kinayod namin ang <a %(wikipedia_isbndb_com)s>ISBNdb</a>, at na-download ang <a %(openlibrary)s>Open Library dataset</a>, ngunit hindi kasiya-siya ang mga resulta. Ang pangunahing problema ay hindi gaanong nag-overlap ang mga ISBN. Tingnan ang Venn diagram na ito mula sa <a %(blog)s>aming blog post</a>: Kami ay labis na nagulat sa kung gaano kaliit ang overlap sa pagitan ng ISBNdb at Open Library, na parehong malayang kinabibilangan ng data mula sa iba't ibang mga mapagkukunan, tulad ng web scrapes at mga tala ng aklatan. Kung pareho silang mahusay sa paghahanap ng karamihan sa mga ISBN na naroroon, tiyak na magkakaroon ng malaking overlap ang kanilang mga bilog, o ang isa ay magiging subset ng isa pa. Napaisip kami, gaano karaming mga libro ang ganap na nahuhulog <em>sa labas ng mga bilog na ito</em>? Kailangan namin ng mas malaking database. Doon namin itinakda ang aming mga mata sa pinakamalaking database ng libro sa mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Ito ay isang proprietary database ng non-profit na <a %(wikipedia_oclc)s>OCLC</a>, na nag-a-aggregate ng mga metadata record mula sa mga aklatan sa buong mundo, kapalit ng pagbibigay sa mga aklatang iyon ng access sa buong dataset, at pagpapakita sa kanila sa mga resulta ng paghahanap ng mga end-user. Kahit na ang OCLC ay isang non-profit, ang kanilang modelo ng negosyo ay nangangailangan ng proteksyon sa kanilang database. Well, paumanhin kami, mga kaibigan sa OCLC, ibinibigay namin ito lahat. :-) Sa nakaraang taon, maingat naming kinayod ang lahat ng mga record ng WorldCat. Sa una, nakakuha kami ng magandang pagkakataon. Ang WorldCat ay kasalukuyang naglalabas ng kanilang kumpletong pag-redesign ng website (noong Agosto 2022). Kasama rito ang malaking pagbabago sa kanilang mga backend system, na nagpakilala ng maraming mga kahinaan sa seguridad. Agad naming sinunggaban ang pagkakataon, at nagawa naming makakuha ng daan-daang milyong (!) mga record sa loob lamang ng ilang araw. Pagkatapos noon, ang mga kahinaan sa seguridad ay dahan-dahang naayos isa-isa, hanggang sa ang huli na aming natagpuan ay na-patch mga isang buwan na ang nakalipas. Sa oras na iyon ay halos lahat ng mga record ay nakuha na namin, at naglalayon na lamang kami para sa bahagyang mas mataas na kalidad ng mga record. Kaya't naramdaman naming oras na para ilabas ito! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Ang Arkibo ni Anna ay nag-scrape ng lahat ng WorldCat (ang pinakamalaking koleksyon ng metadata ng library sa mundo) upang makagawa ng isang TODO list ng mga aklat na kailangang mapanatili.</em> WorldCat Babala: ang post sa blog na ito ay hindi na ginagamit. Napagpasyahan naming hindi pa handa ang IPFS para sa pangunahing oras. Magli-link pa rin kami sa mga file sa IPFS mula sa Arkibo ni Anna kung maaari, ngunit hindi na namin ito iho-host mismo, at hindi rin namin inirerekomenda ang iba na mag-mirror gamit ang IPFS. Pakitingnan ang aming pahina ng Torrents kung nais mong makatulong na mapanatili ang aming koleksyon. Tumulong sa pag-seed ng Z-Library sa IPFS I-download mula sa Partner Server SciDB Panlabas na paghiram Panlabas na paghiram (para sa mga may kapansanan sa pag-print) Panlabas na pag-download Suriin ang metadata Kasama sa mga torrents Bumalik  (+%(num)s bonus) hindi bayad bayad kinansela nag-expire naghihintay ng kumpirmasyon mula kay Anna hindi wasto Ang teksto sa ibaba ay magpapatuloy sa Ingles. Go Reset Pasulong Huli Kung hindi gumagana ang iyong email address sa mga Libgen forums, inirerekomenda naming gamitin ang <a %(a_mail)s>Proton Mail</a> (libre). Maaari ka ring <a %(a_manual)s>manu-manong humiling</a> na ma-activate ang iyong account. (maaaring kailanganin ang <a %(a_browser)s>pagpapatunay ng browser</a> — walang limitasyong pag-download!) Mabilis na Partner Server #%(number)s (inirerekomenda) (medyo mas mabilis ngunit may waitlist) (walang kinakailangang pag-verify ng browser) (walang browser verification o waitlists) (walang waitlist, ngunit maaaring napakabagal) Mabagal na Partner Server #%(number)s Audiobook Komiks Aklat (fiction) Aklat (non-fiction) Aklat (hindi kilala) Artikulo sa journal Magasin Musikal na iskor Iba pa Dokumento ng mga pamantayan Hindi lahat ng pahina ay maaaring ma-convert sa PDF Minarkahang sira sa Libgen.li Hindi nakikita sa Libgen.li Hindi nakikita sa Libgen.rs Fiction Hindi nakikita sa Libgen.rs Non-Fiction Nabigo ang pagpapatakbo ng exiftool sa file na ito Minarkahan bilang "bad file" sa Z-Library Nawawala sa Z-Library Minarkahan bilang "spam" sa Z-Library Hindi mabuksan ang file (hal. sira ang file, DRM) Pag-angkin ng copyright Mga problema sa pag-download (hal. hindi makakonekta, mensahe ng error, napakabagal) Di wastong metadata (hal. pamagat, paglalarawan, larawan sa pabalat) Iba pa Mababang kalidad (hal. mga isyu sa pag-format, mababang kalidad ng scan, nawawalang mga pahina) Spam / dapat alisin ang file (hal. advertising, mapang-abusong nilalaman) %(amount)s (%(amount_usd)s) %(amount)s kabuuan %(amount)s (%(amount_usd)s) kabuuan Brilyanteng Bookworm Masuwerteng Librarian Nagniningning na Datahoarder Kamangha-manghang Archivist Bonus na mga pag-download Cerlalc Czech metadata DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Hindi kasama ang “scimag” Libgen.rs Non-Fiction and Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russian State Library Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Mga pag-upload sa AA Z-Library Z-Library Chinese Pamagat, may-akda, DOI, ISBN, MD5, … Maghanap May-akda Paglalarawan at mga komento sa metadata Edisyon Orihinal na Filename Tagapaglathala (partikular na field ng paghahanap) Pamagat Taon ng Pagkakalathala Mga teknikal na detalye Ang coin na ito ay may mas mataas na minimum kaysa karaniwan. Mangyaring pumili ng ibang tagal o ibang coin. Hindi makumpleto ang kahilingan. Pakisubukang muli sa loob ng ilang minuto, at kung patuloy itong nangyayari, makipag-ugnayan sa amin sa %(email)s kasama ang screenshot. May naganap na hindi kilalang error. Mangyaring makipag-ugnayan sa amin sa %(email)s kasama ang screenshot. Error sa pagproseso ng pagbabayad. Mangyaring maghintay ng sandali at subukang muli. Kung magpapatuloy ang isyu ng higit sa 24 oras, mangyaring makipag-ugnayan sa amin sa %(email)s kasama ang screenshot. Nagsasagawa kami ng fundraiser para sa <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">pag-backup</a> ng pinakamalaking shadow library ng komiks sa mundo. Salamat sa iyong suporta! <a href="/donate">Mag-donate.</a> Kung hindi ka makakapag-donate, isaalang-alang ang pagsuporta sa amin sa pamamagitan ng pagsasabi sa iyong mga kaibigan, at pagsunod sa amin sa <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>. Huwag kaming i-email upang <a %(a_request)s>humiling ng mga libro</a><br>o maliliit (<10k) <a %(a_upload)s>uploads</a>. Anna's Archive DMCA / mga reklamo sa copyright Manatiling nakikipag-ugnayan Reddit Mga Alternatibo SLUM (%(unaffiliated)s) hindi kaanib Kailangan ng tulong ng Anna’s Archive! Kung mag-donate ka ngayon, makakakuha ka ng <strong>doble</strong> na bilang ng mabilis na pag-download. Maraming sumusubok na pabagsakin kami, ngunit lumalaban kami. Kung mag-donate ka ngayong buwan, makakakuha ka ng <strong>dobleng</strong> bilang ng mabilis na pag-download. Balido hanggang sa katapusan ng buwang ito. Pag-save ng kaalaman ng tao: isang mahusay na regalo sa holiday! Ang mga membership ay palalawigin nang naaayon. Hindi available ang mga partner server dahil sa pagsasara ng hosting. Dapat ay bumalik na sila sa lalong madaling panahon. Upang mapataas ang katatagan ng Anna’s Archive, naghahanap kami ng mga boluntaryo na magpapatakbo ng mga mirror. Mayroon kaming bagong paraan ng donasyon na magagamit: %(method_name)s. Mangyaring isaalang-alang ang %(donate_link_open_tag)spag-donate</a> — hindi mura ang pagpapatakbo ng website na ito, at ang iyong donasyon ay talagang may malaking epekto. Maraming salamat. Mag-refer ng kaibigan, at pareho kayong makakakuha ng %(percentage)s%% bonus na mabilis na pag-download! Isurprise ang isang mahal sa buhay, bigyan sila ng account na may membership. Ang perpektong regalo para sa Araw ng mga Puso! Alamin pa… Account Aktibidad Advanced Blog ni Anna ↗ Software ni Anna ↗ beta Codes Explorer Datasets Mag-donate Mga na-download na file Mga FAQ Home Pagbutihin ang metadata LLM data Mag-log in / Magrehistro Aking mga donasyon Pampublikong profile Maghanap Seguridad Torrents Isalin ↗ Pagboboluntaryo at Mga Pabuya Mga recent download: 📚&nbsp;Ang pinakamalaking open-source open-data library sa mundo. ⭐️&nbsp;Kinokopya ang Sci-Hub, Library Genesis, Z-Library, at iba pa. 📈&nbsp;%(book_any)s mga libro, %(journal_article)s mga papel, %(book_comic)s mga komiks, %(magazine)s mga magasin — pinanatili magpakailanman.  at  at iba pa DuXiu Internet Archive Lending Library Libgen 📚&nbsp;Ang pinakamalaking tunay na bukas na aklatan sa kasaysayan ng tao. 📈&nbsp;%(book_count)s&nbsp;mga libro, %(paper_count)s&nbsp;mga papel — pinanatili magpakailanman. ⭐️&nbsp;Kinokopya namin ang %(libraries)s. Kinukuha at binubuksan namin ang %(scraped)s. Ang lahat ng aming code at data ay ganap na open source. OpenLib Sci-Hub ,  📚 Ang pinakamalaking open-source open-data library sa mundo.<br>⭐️ Kinokopya ang Scihub, Libgen, Zlib, at iba pa. Z-Lib Anna’s Archive Di wastong kahilingan. Bisitahin ang %(websites)s. Ang pinakamalaking open-source open-data library sa mundo. Kinokopya ang mga Sci-Hub, Library Genesis, Z-Library, at marami pang iba. Hanapin sa Anna’s Archive Anna’s Archive Paki-refresh upang subukang muli. <a %(a_contact)s>Makipag-ugnayan sa amin</a> kung magpapatuloy ang isyu nang ilang oras. 🔥 Problema sa pag-load ng pahinang ito <li>1. Sundan kami sa <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Ipakalat ang balita tungkol sa Anna’s Archive sa Twitter, Reddit, Tiktok, Instagram, sa iyong lokal na cafe o aklatan, o saan ka man pumunta! Hindi kami naniniwala sa gatekeeping — kung kami ay ma-take down, babalik lang kami sa ibang lugar, dahil ang lahat ng aming code at data ay ganap na open source.</li><li>3. Kung kaya mo, isaalang-alang ang <a href="/donate">pag-donate</a>.</li><li>4. Tumulong na <a href="https://translate.annas-software.org/">isalin</a> ang aming website sa iba't ibang wika.</li><li>5. Kung ikaw ay isang software engineer, isaalang-alang ang pag-ambag sa aming <a href="https://annas-software.org/">open source</a>, o pag-seed ng aming <a href="/datasets">torrents</a>.</li> 10. Lumikha o tumulong na mapanatili ang Wikipedia page para sa Anna’s Archive sa iyong wika. Naghahanap kami ng mga maliit at disente na mga patalastas. Kung nais mong mag-advertise sa Anna’s Archive, mangyaring ipaalam sa amin. 6. Kung ikaw ay isang security researcher, magagamit namin ang iyong mga kasanayan para sa parehong offense at defense. Tingnan ang aming <a %(a_security)s>Security</a> page. 7. Naghahanap kami ng mga eksperto sa pagbabayad para sa mga anonymous merchants. Maaari mo ba kaming tulungan na magdagdag ng mas maginhawang paraan ng pag-donate? PayPal, WeChat, gift cards. Kung may kilala ka, mangyaring makipag-ugnayan sa amin. 8. Palagi kaming naghahanap ng karagdagang kapasidad ng server. 9. Maaari kang tumulong sa pamamagitan ng pag-uulat ng mga isyu sa file, pag-iwan ng mga komento, at paglikha ng mga listahan dito mismo sa website. Maaari ka ring tumulong sa <a %(a_upload)s>pag-upload ng mas maraming libro</a>, o pag-aayos ng mga isyu sa file o pag-format ng mga umiiral na libro. Para sa mas malawak na impormasyon sa kung paano magboluntaryo, tingnan ang aming pahina ng <a %(a_volunteering)s>Pagboboluntaryo at Bounties. Malakas kaming naniniwala sa malayang daloy ng impormasyon, at pagpapanatili ng kaalaman at kultura. Sa search engine na ito, nagtatayo kami sa mga balikat ng mga higante. Malalim naming iginagalang ang masipag na mga tao na lumikha ng iba't ibang shadow libraries, at umaasa kami na ang search engine na ito ay magpapalawak ng kanilang abot. Upang manatiling updated sa aming progreso, sundan si Anna sa <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> o <a href="https://t.me/annasarchiveorg">Telegram</a>. Para sa mga tanong at feedback, mangyaring makipag-ugnayan kay Anna sa %(email)s. Account ID: %(account_id)s Mag-logout ❌ May nangyaring mali. Paki-reload ang pahina at subukang muli. ✅ Ikaw ay naka-logout na. I-reload ang pahina upang mag-log in muli. Mabilis na download na nagamit (huling 24 oras): <strong>%(used)s / %(total)s</strong> Pagiging Miyembro: <strong>%(tier_name)s</strong> hanggang %(until_date)s <a %(a_extend)s>(palawigin)</a> Maaari mong pagsamahin ang maraming membership (ang mabilis na pag-download kada 24 oras ay pagsasamahin). Pagiging Miyembro: <strong>Wala</strong> <a %(a_become)s>(maging miyembro)</a> Makipag-ugnayan kay Anna sa %(email)s kung interesado kang mag-upgrade ng iyong membership sa mas mataas na antas. Pampublikong profile: %(profile_link)s Lihim na susi (huwag ibahagi!): %(secret_key)s ipakita Sumali sa amin dito! Mag-upgrade sa isang <a %(a_tier)s>mas mataas na antas</a> upang sumali sa aming grupo. Eksklusibong Telegram group: %(link)s Account alin sa mga download? Mag-log in Huwag iwawala ang iyong susi! Di-wastong lihim na susi. Suriin ang iyong susi at subukang muli, o magrehistro ng bagong account sa ibaba. Lihim na susi Ilagay ang iyong lihim na susi upang mag-log in: Luma bang email-based na account? Ipasok ang iyong <a %(a_open)s>email dito</a>. Magrehistro ng bagong account Wala ka pang account? Matagumpay na rehistrasyon! Ang iyong lihim na susi ay: <span %(span_key)s>%(key)s</span> Itago nang maingat ang susi na ito. Kung mawawala ito, mawawala rin ang access mo sa iyong account. <li %(li_item)s><strong>I-bookmark.</strong> Maaari mong i-bookmark ang pahinang ito upang makuha ang iyong susi.</li><li %(li_item)s><strong>I-download.</strong> I-click ang <a %(a_download)s>link na ito</a> upang i-download ang iyong susi.</li><li %(li_item)s><strong>Tagapamahala ng password.</strong> Gamitin ang tagapamahala ng password upang itago ang susi kapag inilagay mo ito sa ibaba.</li> Mag-log in / Magrehistro Pagpapatunay ng browser Babala: ang code ay may mga maling Unicode na character sa loob nito, at maaaring kumilos nang hindi tama sa iba't ibang sitwasyon. Ang raw binary ay maaaring i-decode mula sa base64 na representasyon sa URL. Paglalarawan Label Prefix URL para sa isang partikular na code Website Mga code na nagsisimula sa “%(prefix_label)s” Mangyaring huwag kiskisan ang mga pahinang ito. Sa halip, inirerekomenda namin ang <a %(a_import)s>pagbuo</a> o <a %(a_download)s>pag-download</a> ng aming ElasticSearch at MariaDB database, at patakbuhin ang aming <a %(a_software)s>open source code </a>. Maaaring manu-manong i-explore ang raw data sa pamamagitan ng mga JSON file gaya ng <a %(a_json_file)s>ito</a>. Mas kaunti sa %(count)s na mga record Generic URL Codes Explorer Talaan ng I-explore ang mga code kung saan naka-tag ang mga record, sa pamamagitan ng prefix. Ipinapakita ng column na "records" ang bilang ng mga record na na-tag ng mga code na may ibinigay na prefix, tulad ng nakikita sa search engine (kabilang ang mga metadata-only na tala). Ipinapakita ng column na "mga code" kung gaano karaming mga aktwal na code ang may ibinigay na prefix. Kilalang prefix ng code na “%(key)s” Higit pa… Prefix %(count)s record na tumutugma sa “%(prefix_label)s” %(count)s record na tumutugma sa “%(prefix_label)s” Mga code Mga record Ang "%%s" ay papalitan ng halaga ng code Maghanap sa Arkibo ni Anna Mga Code URL for specific code: “%(url)s” Maaaring magtagal bago mabuo ang page na ito, kaya naman nangangailangan ito ng Cloudflare captcha. Maaaring laktawan ng <a %(a_donate)s>Mga Miyembro</a> ang captcha. Na-report na pang-aabuso: Mas magandang bersyon Gusto mo bang i-report ang user na ito para sa mapang-abuso o hindi angkop na pag-uugali? Isyu sa file: %(file_issue)s nakatagong komento Tumugon I-report ang pang-aabuso Na-report mo ang user na ito para sa pang-aabuso. Ang mga copyright claims sa email na ito ay hindi papansinin; gamitin ang form sa halip. Ipakita ang email Malugod naming tinatanggap ang inyong feedback at mga tanong! Gayunpaman, dahil sa dami ng spam at walang kwentang mga email na natatanggap namin, pakisuri ang mga kahon upang kumpirmahin na nauunawaan mo ang mga kundisyon para sa pakikipag-ugnayan sa amin. Anumang ibang paraan ng pakikipag-ugnayan sa amin tungkol sa copyright claims ay awtomatikong mabubura. Para sa mga DMCA / copyright claims, gamitin ang <a %(a_copyright)s>form na ito</a>. Contact email Mga URL sa Anna’s Archive (kinakailangan). Isa bawat linya. Mangyaring isama lamang ang mga URL na naglalarawan ng eksaktong parehong edisyon ng isang libro. Kung nais mong maghain ng pag-angkin para sa maraming libro o maraming edisyon, mangyaring isumite ang form na ito nang maraming beses. Ang mga pag-angkin na pinagsasama-sama ang maraming libro o edisyon ay tatanggihan. Address (kinakailangan) Malinaw na paglalarawan ng pinagmulan ng materyal (kinakailangan) E-mail (kinakailangan) Mga URL sa pinagmulan ng materyal, isa bawat linya (kinakailangan). Mangyaring isama ang mas marami hangga't maaari, upang matulungan kaming mapatunayan ang iyong pag-angkin (hal. Amazon, WorldCat, Google Books, DOI). Mga ISBN ng pinagmulan ng materyal (kung naaangkop). Isa bawat linya. Mangyaring isama lamang ang mga iyon na eksaktong tumutugma sa edisyon na iyong iniulat para sa pag-angkin ng copyright. Ang iyong pangalan (kinakailangan) ❌ May nangyaring mali. Mangyaring i-reload ang pahina at subukang muli. ✅ Salamat sa pagsusumite ng iyong pag-angkin ng copyright. Susuriin namin ito sa lalong madaling panahon. Mangyaring i-reload ang pahina upang maghain ng isa pa. <a %(a_openlib)s>Open Library</a> Mga URL ng pinagmulan ng materyal, isa bawat linya. Mangyaring maglaan ng sandali upang hanapin sa Open Library ang iyong pinagmulan ng materyal. Makakatulong ito sa amin na mapatunayan ang iyong pag-angkin. Numero ng telepono (kinakailangan) Pahayag at lagda (kinakailangan) Isumite ang pag-angkin Kung mayroon kang DMCA o iba pang pag-angkin ng copyright, mangyaring punan ang form na ito nang mas tumpak hangga't maaari. Kung makakaranas ka ng anumang isyu, mangyaring makipag-ugnayan sa amin sa aming dedikadong DMCA address: %(email)s. Tandaan na ang mga pag-angkin na ipinadala sa email address na ito ay hindi ipoproseso, ito ay para lamang sa mga katanungan. Mangyaring gamitin ang form sa ibaba upang isumite ang iyong mga pag-angkin. Formularyo ng DMCA / Pag-angkin ng Copyright Halimbawang talaan sa Anna’s Archive Mga Torrents ng Anna’s Archive Format ng Anna’s Archive Containers Mga script para sa pag-import ng metadata Kung interesado kayong i-mirror ang dataset na ito para sa <a %(a_archival)s>archival</a> o <a %(a_llm)s>LLM training</a> na mga layunin, mangyaring makipag-ugnayan sa amin. Huling na-update: %(date)s Pangunahing %(source)s website Dokumentasyon ng Metadata (kadalasang mga field) Mga file na na-mirror ng Anna’s Archive: %(count)s (%(percent)s%%) Mga Mapagkukunan Kabuuang mga file: %(count)s Kabuuang laki ng file: %(size)s Ang aming blog post tungkol sa data na ito <a %(duxiu_link)s>Duxiu</a> ay isang napakalaking database ng mga na-scan na libro, na nilikha ng <a %(superstar_link)s>SuperStar Digital Library Group</a>. Karamihan ay mga akademikong libro, na na-scan upang magamit nang digital sa mga unibersidad at mga aklatan. Para sa aming mga mambabasa na nagsasalita ng Ingles, ang <a %(princeton_link)s>Princeton</a> at ang <a %(uw_link)s>University of Washington</a> ay may magagandang pangkalahatang-ideya. Mayroon ding isang mahusay na artikulo na nagbibigay ng higit pang background: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Ang mga libro mula sa Duxiu ay matagal nang pinirata sa internet ng Tsina. Karaniwan silang ibinebenta ng mas mababa sa isang dolyar ng mga reseller. Karaniwan silang ipinapamahagi gamit ang katumbas ng Google Drive sa Tsina, na madalas na na-hack upang payagan ang mas maraming espasyo sa imbakan. Ang ilang mga teknikal na detalye ay matatagpuan <a %(link1)s>dito</a> at <a %(link2)s>dito</a>. Bagaman ang mga libro ay semi-pampublikong ipinamahagi, medyo mahirap makuha ang mga ito nang maramihan. Mataas ito sa aming TODO-list, at naglaan kami ng ilang buwan ng full-time na trabaho para dito. Gayunpaman, sa huling bahagi ng 2023, isang kamangha-mangha, kahanga-hanga, at talentadong boluntaryo ang lumapit sa amin, na nagsasabing nagawa na nila ang lahat ng trabahong ito — sa malaking gastos. Ibinahagi nila sa amin ang buong koleksyon, nang hindi umaasa ng anumang kapalit, maliban sa garantiya ng pangmatagalang pangangalaga. Tunay na kahanga-hanga. Higit pang impormasyon mula sa aming mga boluntaryo (mga hilaw na tala): Inangkop mula sa aming <a %(a_href)s>blog post</a>. DuXiu 读秀 page.datasets.file %(count)s mga file Ang dataset na ito ay malapit na nauugnay sa <a %(a_datasets_openlib)s>Open Library dataset</a>. Naglalaman ito ng scrape ng lahat ng metadata at isang malaking bahagi ng mga file mula sa IA’s Controlled Digital Lending Library. Ang mga update ay inilalabas sa <a %(a_aac)s>Anna’s Archive Containers format</a>. Ang mga rekord na ito ay direktang tinutukoy mula sa Open Library dataset, ngunit naglalaman din ng mga rekord na wala sa Open Library. Mayroon din kaming ilang mga data file na na-scrape ng mga miyembro ng komunidad sa paglipas ng mga taon. Ang koleksyon ay binubuo ng dalawang bahagi. Kailangan mo ang parehong bahagi upang makuha ang lahat ng data (maliban sa mga pinalitang torrents, na naka-cross out sa torrents page). Digital Lending Library ang aming unang release, bago namin na-standardize sa <a %(a_aac)s>format ng Anna’s Archive Containers (AAC)</a>. Naglalaman ng metadata (bilang json at xml), mga pdf (mula sa acsm at lcpdf digital lending systems), at mga cover thumbnails. incremental na mga bagong release, gamit ang AAC. Naglalaman lamang ng metadata na may mga timestamp pagkatapos ng 2023-01-01, dahil sakop na ng “ia” ang iba. Kasama rin ang lahat ng pdf files, sa pagkakataong ito mula sa acsm at “bookreader” (IA’s web reader) lending systems. Kahit na hindi eksakto ang pangalan, pinupunan pa rin namin ang mga bookreader files sa ia2_acsmpdf_files collection, dahil sila ay eksklusibong magkasama. IA Controlled Digital Lending 98%%+ ng mga file ay nahahanap. Ang aming misyon ay i-archive ang lahat ng mga libro sa mundo (pati na rin ang mga papel, magasin, atbp.), at gawing malawak na naa-access ang mga ito. Naniniwala kami na ang lahat ng mga libro ay dapat na i-mirror nang malawakan, upang matiyak ang redundancy at resiliency. Ito ang dahilan kung bakit pinagsasama-sama namin ang mga file mula sa iba't ibang mga mapagkukunan. Ang ilang mga mapagkukunan ay ganap na bukas at maaaring i-mirror nang maramihan (tulad ng Sci-Hub). Ang iba ay sarado at protektado, kaya sinusubukan naming i-scrape ang mga ito upang “palayain” ang kanilang mga libro. Ang iba naman ay nasa pagitan. Lahat ng aming data ay maaaring <a %(a_torrents)s>i-torrent</a>, at lahat ng aming metadata ay maaaring <a %(a_anna_software)s>i-generate</a> o <a %(a_elasticsearch)s>i-download</a> bilang ElasticSearch at MariaDB databases. Ang raw data ay maaaring manu-manong tuklasin sa pamamagitan ng mga JSON files tulad ng <a %(a_dbrecord)s>ito</a>. Metadata Website ng ISBN Huling na-update: %(isbn_country_date)s (%(link)s) Mga Mapagkukunan Ang International ISBN Agency ay regular na naglalabas ng mga saklaw na inilaan nito sa mga pambansang ahensya ng ISBN. Mula rito, maaari nating matukoy kung saang bansa, rehiyon, o pangkat ng wika kabilang ang ISBN na ito. Sa kasalukuyan, ginagamit namin ang datos na ito nang hindi direkta, sa pamamagitan ng <a %(a_isbnlib)s>isbnlib</a> na Python library. Impormasyon ng bansa sa ISBN Ito ay isang dump ng maraming tawag sa isbndb.com noong Setyembre 2022. Sinubukan naming saklawin ang lahat ng saklaw ng ISBN. Ito ay humigit-kumulang 30.9 milyong talaan. Sa kanilang website, sinasabi nila na mayroon silang 32.6 milyong talaan, kaya maaaring may mga na-miss kami, o <em>sila</em> ay maaaring may maling ginagawa. Ang mga tugon ng JSON ay halos raw mula sa kanilang server. Isang isyu sa kalidad ng datos na napansin namin, ay para sa mga ISBN-13 na numero na nagsisimula sa ibang prefix kaysa sa “978-”, kasama pa rin nila ang isang “isbn” field na simpleng ang ISBN-13 na numero na may unang 3 numero na tinanggal (at ang check digit ay muling kinalkula). Maliwanag na mali ito, ngunit ganito nila ginagawa, kaya hindi namin ito binago. Isa pang potensyal na isyu na maaari mong maranasan, ay ang katotohanan na ang “isbn13” field ay may mga duplicate, kaya hindi mo ito magagamit bilang pangunahing susi sa isang database. Ang pinagsamang “isbn13”+“isbn” fields ay tila natatangi. Release 1 (2022-10-31) Ang mga fiction torrents ay nasa likod (bagaman ang mga ID ~4-6M ay hindi na-torrent dahil nag-overlap ang mga ito sa aming mga Zlib torrents). Ang aming blog post tungkol sa paglabas ng mga comic book Comics torrents sa Anna’s Archive Para sa kasaysayan ng iba't ibang Library Genesis forks, tingnan ang pahina para sa <a %(a_libgen_rs)s>Libgen.rs</a>. Ang Libgen.li ay naglalaman ng karamihan sa parehong nilalaman at metadata tulad ng Libgen.rs, ngunit may ilang mga koleksyon sa ibabaw nito, katulad ng mga komiks, magasin, at mga standard na dokumento. Isinama rin nito ang <a %(a_scihub)s>Sci-Hub</a> sa metadata at search engine nito, na siyang ginagamit namin para sa aming database. Ang metadata para sa library na ito ay malayang makukuha <a %(a_libgen_li)s>sa libgen.li</a>. Gayunpaman, ang server na ito ay mabagal at hindi sumusuporta sa pagpapatuloy ng mga naputol na koneksyon. Ang parehong mga file ay makukuha rin sa <a %(a_ftp)s>isang FTP server</a>, na mas mahusay gumana. Mukhang nagkaroon din ng pagbabago sa non-fiction, ngunit wala pang mga bagong torrents. Mukhang nangyari ito mula noong unang bahagi ng 2022, bagaman hindi pa namin ito nabeberipika. Ayon sa administrador ng Libgen.li, ang koleksyon na “fiction_rus” (Russian fiction) ay dapat saklawin ng regular na inilalabas na torrents mula sa <a %(a_booktracker)s>booktracker.org</a>, partikular na ang <a %(a_flibusta)s>flibusta</a> at <a %(a_librusec)s>lib.rus.ec</a> torrents (na aming sinasalamin <a %(a_torrents)s>dito</a>, bagaman hindi pa namin natutukoy kung aling mga torrents ang tumutugma sa aling mga file). Ang koleksyon ng kathang-isip ay may sariling torrents (hiwalay mula sa <a %(a_href)s>Libgen.rs</a>) simula sa %(start)s. Ang ilang mga saklaw na walang torrents (tulad ng mga saklaw ng kathang-isip f_3463000 hanggang f_4260000) ay malamang na mga file ng Z-Library (o iba pang duplicate), bagaman maaaring nais naming magsagawa ng deduplication at gumawa ng torrents para sa mga natatanging file ng lgli sa mga saklaw na ito. Ang mga istatistika para sa lahat ng koleksyon ay matatagpuan <a %(a_href)s>sa website ng libgen</a>. Ang mga torrents ay magagamit para sa karamihan ng karagdagang nilalaman, partikular na ang mga torrents para sa komiks, magasin, at karaniwang dokumento ay inilabas sa pakikipagtulungan sa Arkibo ni Anna. Tandaan na ang mga torrent file na tumutukoy sa “libgen.is” ay tahasang mga salamin ng <a %(a_libgen)s>Libgen.rs</a> (“.is” ay isang ibang domain na ginagamit ng Libgen.rs). Isang kapaki-pakinabang na mapagkukunan sa paggamit ng metadata ay <a %(a_href)s>ang pahinang ito</a>. %(icon)s Ang kanilang koleksyon na “fiction_rus” (Russian fiction) ay walang dedikadong torrents, ngunit sakop ito ng torrents mula sa iba, at pinapanatili namin ang isang <a %(fiction_rus)s>salamin</a>. Mga Russian fiction torrents sa Arkibo ni Anna Fiction torrents sa Anna’s Archive Talakayan sa forum Metadata Metadata sa pamamagitan ng FTP Magazine torrents sa Anna’s Archive Impormasyon sa field ng metadata Salamin ng iba pang mga torrent (at natatanging fiction at comics torrent) Mga karaniwang dokumento torrents sa Arkibo ni Anna Libgen.li Torrents ng Anna’s Archive (mga pabalat ng libro) Ang Library Genesis ay kilala sa kanilang mapagbigay na pagbibigay ng data sa bulk sa pamamagitan ng torrents. Ang aming koleksyon ng Libgen ay binubuo ng mga auxiliary data na hindi nila direktang inilalabas, sa pakikipagtulungan sa kanila. Maraming salamat sa lahat ng kasangkot sa Library Genesis sa pakikipagtulungan sa amin! Ang aming blog tungkol sa paglabas ng mga pabalat ng libro Ang pahinang ito ay tungkol sa bersyong “.rs”. Kilala ito sa patuloy na pag-publish ng parehong metadata at ang buong nilalaman ng katalogo ng mga libro nito. Ang koleksyon ng mga libro nito ay nahahati sa pagitan ng isang fiction at non-fiction na bahagi. Isang kapaki-pakinabang na mapagkukunan sa paggamit ng metadata ay <a %(a_metadata)s>ang pahinang ito</a> (nagba-block ng mga IP range, maaaring kailanganin ang VPN). Simula 2024-03, ang mga bagong torrents ay ipo-post sa <a %(a_href)s>thread ng forum na ito</a> (nagba-block ng mga IP range, maaaring kailanganin ang VPN). Fiction torrents sa Anna’s Archive Libgen.rs Fiction torrents Libgen.rs Discussion forum Libgen.rs Metadata Impormasyon sa field ng metadata ng Libgen.rs Libgen.rs Non-fiction torrents Non-Fiction torrents sa Anna’s Archive %(example)s para sa isang fiction na libro. Ang <a %(blog_post)s>unang paglabas</a> na ito ay medyo maliit: mga 300GB ng mga pabalat ng libro mula sa Libgen.rs fork, parehong fiction at non-fiction. Ang mga ito ay nakaayos sa parehong paraan kung paano sila lumalabas sa libgen.rs, halimbawa: %(example)s para sa isang non-fiction na libro. Katulad ng sa koleksyon ng Z-Library, inilagay namin silang lahat sa isang malaking .tar file, na maaaring i-mount gamit ang <a %(a_ratarmount)s>ratarmount</a> kung nais mong direktang i-serve ang mga file. Paglabas 1 (%(date)s) Ang mabilis na kuwento ng iba't ibang mga sangay ng Library Genesis (o “Libgen”), ay sa paglipas ng panahon, ang iba't ibang tao na kasangkot sa Library Genesis ay nagkaroon ng hindi pagkakaunawaan, at naghiwalay ng landas. Ayon sa <a %(a_mhut)s>post sa forum</a> na ito, ang Libgen.li ay orihinal na naka-host sa “http://free-books.dontexist.com”. Ang bersyong “.fun” ay nilikha ng orihinal na tagapagtatag. Ito ay binabago pabor sa isang bago, mas distributed na bersyon. Ang <a %(a_li)s>bersyong “.li”</a> ay may napakalaking koleksyon ng mga comics, pati na rin ang iba pang nilalaman, na hindi (pa) magagamit para sa bulk download sa pamamagitan ng torrents. Mayroon itong hiwalay na koleksyon ng torrent ng mga fiction na libro, at naglalaman ito ng metadata ng <a %(a_scihub)s>Sci-Hub</a> sa database nito. Ang bersyong “.rs” ay may napakakatulad na data, at pinakakonsistent na naglalabas ng kanilang koleksyon sa bulk torrents. Ito ay humigit-kumulang na nahahati sa isang “fiction” at isang “non-fiction” na seksyon. Orihinal na nasa “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> sa ilang kahulugan ay isang sangay din ng Library Genesis, bagaman gumamit sila ng ibang pangalan para sa kanilang proyekto. Libgen.rs Pinayayaman din namin ang aming koleksyon gamit ang mga pinagmulan ng metadata lamang, na maaari naming itugma sa mga file, halimbawa gamit ang mga numero ng ISBN o iba pang mga field. Sa ibaba ay isang pangkalahatang-ideya ng mga iyon. Muli, ang ilan sa mga pinagmulan na ito ay ganap na bukas, habang ang iba ay kailangan naming i-scrape. Tandaan na sa paghahanap ng metadata, ipinapakita namin ang mga orihinal na rekord. Hindi kami nagsasagawa ng anumang pagsasama-sama ng mga rekord. Mga pinagmulan ng metadata lamang Ang Open Library ay isang open source na proyekto ng Internet Archive upang i-catalog ang bawat libro sa mundo. Ito ay may isa sa pinakamalaking operasyon ng pag-scan ng libro sa mundo, at maraming mga libro ang magagamit para sa digital lending. Ang catalog ng metadata ng libro nito ay malayang magagamit para sa pag-download, at kasama sa Anna’s Archive (bagaman hindi kasalukuyang nasa search, maliban kung malinaw mong hinahanap ang isang Open Library ID). Open Library Hindi kasama ang mga duplicate Huling na-update Mga porsyento ng bilang ng mga file %% na na-mirror ng AA / torrents na available Size Source Sa ibaba ay isang mabilis na pangkalahatang-ideya ng mga pinagmulan ng mga file sa Anna’s Archive. Dahil ang mga shadow library ay madalas na nagsi-sync ng data mula sa isa't isa, may malaking overlap sa pagitan ng mga library. Iyon ang dahilan kung bakit ang mga numero ay hindi nagdaragdag sa kabuuan. Ang porsyento ng "na-mirror at seeded ng Anna's Archive" ay nagpapakita kung gaano karaming mga file ang ating sinasalamin ang ating sarili. Binili namin ang mga file na iyon nang maramihan sa pamamagitan ng torrents, at ginagawang available ang mga ito para sa direktang pag-download sa pamamagitan ng mga website ng kasosyo. Pangkalahatang-ideya Total Torrents sa Anna’s Archive Para sa karagdagang impormasyon tungkol sa Sci-Hub, mangyaring bisitahin ang <a %(a_scihub)s>opisyal na website</a>, <a %(a_wikipedia)s>pahina ng Wikipedia</a>, at ang <a %(a_radiolab)s>panayam sa podcast</a> na ito. Tandaan na ang Sci-Hub ay <a %(a_reddit)s>naka-freeze mula pa noong 2021</a>. Naka-freeze na ito dati, ngunit noong 2021 ay nadagdagan ng ilang milyong papel. Gayunpaman, may ilang limitadong bilang ng mga papel na idinadagdag sa mga koleksyon ng Libgen “scimag”, bagaman hindi sapat upang magarantiya ang mga bagong bulk torrents. Ginagamit namin ang metadata ng Sci-Hub na ibinigay ng <a %(a_libgen_li)s>Libgen.li</a> sa koleksyon nitong “scimag”. Ginagamit din namin ang dataset na <a %(a_dois)s>dois-2022-02-12.7z</a>. Tandaan na ang mga “smarch” torrents ay <a %(a_smarch)s>hindi na ginagamit</a> at samakatuwid ay hindi kasama sa aming listahan ng torrents. Torrents sa Libgen.li Torrents sa Libgen.rs Metadata at torrents Mga update sa Reddit Panayam sa podcast Pahina ng Wikipedia Sci-Hub Sci-Hub: nagyelo mula noong 2021; pinaka-magagamit sa pamamagitan ng torrents Libgen.li: menor de edad karagdagan mula noon</div> Ang ilang mga source library ay nagpo-promote ng maramihang pagbabahagi ng kanilang data sa pamamagitan ng torrents, habang ang iba naman ay hindi agad-agad na ibinabahagi ang kanilang koleksyon. Sa huling kaso, sinusubukan ng Anna’s Archive na i-scrape ang kanilang mga koleksyon, at gawing available ang mga ito (tingnan ang aming <a %(a_torrents)s>Torrents</a> na pahina). Mayroon ding mga sitwasyon sa pagitan, halimbawa, kung saan ang mga source library ay handang magbahagi, ngunit walang sapat na mga mapagkukunan upang gawin ito. Sa mga kasong iyon, sinusubukan din naming tumulong. Sa ibaba ay isang pangkalahatang-ideya kung paano kami nakikipag-ugnayan sa iba't ibang source library. Source libraries %(icon)s Iba't ibang file databases na nakakalat sa internet ng Tsina; bagaman madalas na bayad na databases %(icon)s Karamihan sa mga file ay naa-access lamang gamit ang premium BaiduYun accounts; mabagal na bilis ng pag-download. %(icon)s Pinamamahalaan ng Anna’s Archive ang isang koleksyon ng <a %(duxiu)s>DuXiu files</a> %(icon)s Iba't ibang mga database ng metadata na nakakalat sa internet ng Tsina; bagaman madalas na mga bayad na database %(icon)s Walang madaling ma-access na metadata dumps na magagamit para sa kanilang buong koleksyon. %(icon)s Pinamamahalaan ng Anna’s Archive ang isang koleksyon ng <a %(duxiu)s>DuXiu metadata</a> Mga File %(icon)s Mga file na available lamang para sa limitadong paghiram, na may iba't ibang mga paghihigpit sa pag-access %(icon)s Pinamamahalaan ng Arkibo ni Anna ang isang koleksyon ng <a %(ia)s>mga file ng IA</a> %(icon)s May ilang metadata na available sa pamamagitan ng <a %(openlib)s>Open Library database dumps</a>, ngunit hindi nito saklaw ang buong koleksyon ng IA %(icon)s Walang madaling ma-access na metadata dumps na magagamit para sa kanilang buong koleksyon %(icon)s Pinamamahalaan ng Arkibo ni Anna ang isang koleksyon ng <a %(ia)s>IA metadata</a> Huling na-update %(icon)s Ang Arkibo ni Anna at Libgen.li ay magkasamang namamahala ng mga koleksyon ng <a %(comics)s>mga komiks</a>, <a %(magazines)s>magasin</a>, <a %(standarts)s>mga karaniwang dokumento</a>, at <a %(fiction)s>kathang-isip (hiwalay mula sa Libgen.rs)</a>. %(icon)s Ang mga Non-Fiction torrent ay ibinabahagi sa Libgen.rs (at naka-mirror <a %(libgenli)s>dito</a>). %(icon)s Quarterly <a %(dbdumps)s>HTTP database dumps</a> %(icon)s Awtomatikong mga torrent para sa <a %(nonfiction)s>Non-Fiction</a> at <a %(fiction)s>Fiction</a> %(icon)s Pinamamahalaan ng Arkibo ni Anna ang isang koleksyon ng <a %(covers)s>mga book cover torrent</a> %(icon)s Araw-araw na <a %(dbdumps)s>HTTP database dumps</a> Metadata %(icon)s Buwanang <a %(dbdumps)s>database dumps</a> %(icon)s Mga data torrent na available <a %(scihub1)s>dito</a>, <a %(scihub2)s>dito</a>, at <a %(libgenli)s>dito</a> %(icon)s May ilang bagong file na <a %(libgenrs)s>idinadagdag</a> sa “scimag” ng Libgen, ngunit hindi sapat upang magbigay ng bagong mga torrent %(icon)s Ang Sci-Hub ay huminto sa pagdagdag ng mga bagong file mula noong 2021. %(icon)s Mga metadata dump na available <a %(scihub1)s>dito</a> at <a %(scihub2)s>dito</a>, pati na rin bilang bahagi ng <a %(libgenli)s>Libgen.li database</a> (na ginagamit namin) Pinagmulan %(icon)s Iba't ibang mas maliit o isang beses na mga pinagmulan. Hinihikayat namin ang mga tao na mag-upload muna sa ibang shadow libraries, ngunit minsan ang mga tao ay may mga koleksyon na masyadong malaki para sa iba na ayusin, ngunit hindi sapat na malaki upang magkaroon ng sariling kategorya. %(icon)s Hindi direktang magagamit ng maramihan, protektado laban sa scraping %(icon)s Pinamamahalaan ng Anna’s Archive ang isang koleksyon ng <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Ang Arkibo ni Anna at Z-Library ay magkasamang namamahala ng koleksyon ng <a %(metadata)s>Z-Library metadata</a> at <a %(files)s>Z-Library files</a> Datasets Pinagsasama namin ang lahat ng mga pinagmulan sa itaas sa isang pinag-isang database na ginagamit namin upang maglingkod sa website na ito. Ang pinag-isang database na ito ay hindi direktang magagamit, ngunit dahil ang Anna’s Archive ay ganap na open source, maaari itong medyo madaling <a %(a_generated)s>mabuo</a> o <a %(a_downloaded)s>ma-download</a> bilang ElasticSearch at MariaDB databases. Ang mga script sa pahinang iyon ay awtomatikong magda-download ng lahat ng kinakailangang metadata mula sa mga pinagmulan na nabanggit sa itaas. Kung nais mong tuklasin ang aming data bago patakbuhin ang mga script na iyon nang lokal, maaari mong tingnan ang aming mga JSON file, na nagli-link pa sa iba pang mga JSON file. <a %(a_json)s>Ang file na ito</a> ay isang magandang panimulang punto. Pinag-isang database Mga Torrent ng Anna’s Archive browse search Iba't ibang mas maliit o isang beses na mga pinagmulan. Hinihikayat namin ang mga tao na mag-upload muna sa ibang shadow libraries, ngunit minsan ang mga tao ay may mga koleksyon na masyadong malaki para sa iba na ayusin, ngunit hindi sapat na malaki upang magkaroon ng sariling kategorya. Pangkalahatang-ideya mula sa <a %(a1)s>pahina ng datasets</a>. Mula sa <a %(a_href)s>aaaaarg.fail</a>. Mukhang kumpleto na. Mula sa aming boluntaryo na si “cgiym”. Mula sa isang <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. May mataas na pagkakatulad sa mga umiiral na koleksyon ng mga papel, ngunit kakaunti ang mga tugma sa MD5, kaya't napagpasyahan naming panatilihin ito nang buo. Scrape ng <q>iRead eBooks</q> (= phonetically <q>ai rit i-books</q>; airitibooks.com), ng boluntaryo <q>j</q>. Tumutugma sa <q>airitibooks</q> metadata sa <a %(a1)s><q>Iba pang metadata scrapes</q></a>. Mula sa koleksyon <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Bahagyang mula sa orihinal na pinagmulan, bahagyang mula sa the-eye.eu, bahagyang mula sa ibang mga salamin. Mula sa isang pribadong website ng mga libro torrent, <a %(a_href)s>Bibliotik</a> (madalas na tinutukoy bilang “Bib”), kung saan ang mga libro ay pinagsama-sama sa mga torrent ayon sa pangalan (A.torrent, B.torrent) at ipinamamahagi sa pamamagitan ng the-eye.eu. Mula sa aming boluntaryo na si “bpb9v”. Para sa karagdagang impormasyon tungkol sa <a %(a_href)s>CADAL</a>, tingnan ang mga tala sa aming <a %(a_duxiu)s>DuXiu dataset page</a>. Higit pa mula sa aming boluntaryo na si “bpb9v”, karamihan ay mga file ng DuXiu, pati na rin ang isang folder na “WenQu” at “SuperStar_Journals” (SuperStar ang kumpanya sa likod ng DuXiu). Mula sa aming boluntaryo na si “cgiym”, mga tekstong Tsino mula sa iba't ibang pinagmulan (kinakatawan bilang mga subdirectory), kabilang ang mula sa <a %(a_href)s>China Machine Press</a> (isang pangunahing tagapaglathala sa Tsina). Mga koleksyong hindi Tsino (kinakatawan bilang mga subdirectory) mula sa aming boluntaryo na si “cgiym”. Scrape ng mga aklat tungkol sa arkitekturang Tsino, ng boluntaryo <q>cm</q>: <q>Nakuha ko ito sa pamamagitan ng pagsasamantala sa isang kahinaan sa network sa bahay-publish, ngunit ang butas na iyon ay sarado na</q>. Tumutugma sa <q>chinese_architecture</q> metadata sa <a %(a1)s><q>Iba pang metadata scrapes</q></a>. Mga libro mula sa akademikong tagapaglathala na <a %(a_href)s>De Gruyter</a>, na nakolekta mula sa ilang malalaking torrent. Scrape ng <a %(a_href)s>docer.pl</a>, isang Polish na website ng pagbabahagi ng file na nakatuon sa mga libro at iba pang nakasulat na mga gawa. Na-scrape noong huling bahagi ng 2023 ng boluntaryo na si “p”. Wala kaming magandang metadata mula sa orihinal na website (kahit na mga extension ng file), ngunit nag-filter kami para sa mga file na parang libro at madalas naming nakuha ang metadata mula sa mga file mismo. DuXiu epubs, direkta mula sa DuXiu, na nakolekta ng boluntaryo na si “w”. Tanging mga kamakailang libro ng DuXiu ang direktang magagamit sa pamamagitan ng mga ebook, kaya karamihan sa mga ito ay dapat na kamakailan lamang. Natitirang mga file ng DuXiu mula sa boluntaryo na si “m”, na hindi nasa DuXiu proprietary PDG format (ang pangunahing <a %(a_href)s>DuXiu dataset</a>). Nakolekta mula sa maraming orihinal na pinagmulan, sa kasamaang-palad nang hindi napapanatili ang mga pinagmulan sa filepath. <span></span> <span></span> <span></span> Scrape ng mga erotikong aklat, ng boluntaryo <q>do no harm</q>. Tumutugma sa <q>hentai</q> metadata sa <a %(a1)s><q>Iba pang metadata scrapes</q></a>. <span></span> <span></span> Koleksyon na na-scrape mula sa isang Japanese Manga publisher ng boluntaryo na si “t”. <a %(a_href)s>Piniling mga hudisyal na archive ng Longquan</a>, ibinigay ng boluntaryo na si “c”. Scrape ng <a %(a_href)s>magzdb.org</a>, isang kaalyado ng Library Genesis (ito ay naka-link sa libgen.rs homepage) ngunit ayaw nilang direktang ibigay ang kanilang mga file. Nakuha ng boluntaryo na si “p” noong huling bahagi ng 2023. <span></span> Iba't ibang maliliit na pag-upload, masyadong maliit bilang kanilang sariling subkoleksyon, ngunit kinakatawan bilang mga direktoryo. Mga eBook mula sa AvaxHome, isang Russian na website para sa pagbabahagi ng file. Archive ng mga pahayagan at magasin. Tumutugma sa <q>newsarch_magz</q> metadata sa <a %(a1)s><q>Iba pang metadata scrapes</q></a>. Scrape ng <a %(a1)s>Philosophy Documentation Center</a>. Koleksyon ng boluntaryo na si “o” na nangolekta ng mga Polish na libro direkta mula sa mga orihinal na release (“scene”) na mga website. Pinagsamang mga koleksyon ng <a %(a_href)s>shuge.org</a> ng mga boluntaryo na sina “cgiym” at “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (pinangalanan mula sa kathang-isip na aklatan), na-scrape noong 2022 ng boluntaryo na si “t”. <span></span> <span></span> <span></span> Mga sub-sub-koleksyon (kinakatawan bilang mga direktoryo) mula sa boluntaryo na si “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (ni <a %(a_sikuquanshu)s>Dizhi(迪志)</a> sa Taiwan), mebook (mebook.cc, 我的小书屋, ang aking maliit na silid-aklatan — woz9ts: “Ang site na ito ay pangunahing nakatuon sa pagbabahagi ng mga de-kalidad na ebook file, ang ilan sa mga ito ay inayos ng may-ari mismo. Ang may-ari ay <a %(a_arrested)s>inaresto</a> noong 2019 at may gumawa ng koleksyon ng mga file na kanyang ibinahagi.”). Mga natitirang file ng DuXiu mula sa boluntaryong “woz9ts”, na hindi nasa pagmamay-aring PDG format ng DuXiu (kailangan pang i-convert sa PDF). Ang “upload” na koleksyon ay hinati sa mas maliliit na subcollections, na ipinapahiwatig sa mga AACIDs at mga pangalan ng torrent. Ang lahat ng subcollections ay unang na-deduplicate laban sa pangunahing koleksyon, bagaman ang metadata na “upload_records” JSON files ay naglalaman pa rin ng maraming mga sanggunian sa mga orihinal na file. Ang mga non-book files ay inalis din mula sa karamihan ng subcollections, at karaniwang <em>hindi</em> nabanggit sa “upload_records” JSON. Ang mga subcollections ay: Mga Tala Subkoleksyon Maraming subcollections mismo ay binubuo ng mga sub-sub-collections (hal. mula sa iba't ibang orihinal na pinagmulan), na kinakatawan bilang mga direktoryo sa mga “filepath” fields. Mga Upload sa Anna’s Archive Ang aming blog post tungkol sa data na ito <a %(a_worldcat)s>WorldCat</a> ay isang proprietary database ng non-profit na <a %(a_oclc)s>OCLC</a>, na nag-a-aggregate ng mga metadata record mula sa mga aklatan sa buong mundo. Malamang ito ang pinakamalaking koleksyon ng metadata ng aklatan sa buong mundo. Noong Oktubre 2023, <a %(a_scrape)s>naglabas</a> kami ng komprehensibong scrape ng OCLC (WorldCat) database, sa <a %(a_aac)s>format ng Anna’s Archive Containers</a>. Oktubre 2023, unang paglabas: OCLC (WorldCat) Torrents ng Anna’s Archive Halimbawang talaan sa Anna’s Archive (orihinal na koleksyon) Halimbawang talaan sa Anna’s Archive (“zlib3” koleksyon) Mga Torrent ng Anna’s Archive (metadata + content) Blog post tungkol sa Release 1 Blog post tungkol sa Release 2 Noong huling bahagi ng 2022, ang mga pinaghihinalaang tagapagtatag ng Z-Library ay inaresto, at ang mga domain ay kinumpiska ng mga awtoridad ng Estados Unidos. Mula noon, ang website ay dahan-dahang bumabalik online. Hindi alam kung sino ang kasalukuyang nagpapatakbo nito. Update noong Pebrero 2023. Ang Z-Library ay may mga ugat sa komunidad ng <a %(a_href)s>Library Genesis</a>, at orihinal na nagsimula gamit ang kanilang data. Mula noon, ito ay naging mas propesyonal, at may mas modernong interface. Dahil dito, nakakatanggap sila ng mas maraming donasyon, parehong pinansyal upang patuloy na mapabuti ang kanilang website, pati na rin ang mga donasyon ng mga bagong libro. Nakapag-ipon sila ng malaking koleksyon bukod pa sa Library Genesis. Ang koleksyon ay binubuo ng tatlong bahagi. Ang orihinal na mga pahina ng paglalarawan para sa unang dalawang bahagi ay napanatili sa ibaba. Kailangan mo ang lahat ng tatlong bahagi upang makuha ang lahat ng data (maliban sa mga pinalitang mga torrent, na naka-cross out sa pahina ng mga torrent). %(title)s: ang aming unang release. Ito ang pinakaunang release ng tinawag noon na “Pirate Library Mirror” (“pilimi”). %(title)s: pangalawang release, sa pagkakataong ito lahat ng file ay naka-wrap sa .tar files. %(title)s: incremental na mga bagong release, gamit ang <a %(a_href)s>Anna’s Archive Containers (AAC) format</a>, ngayon ay inilabas sa pakikipagtulungan sa team ng Z-Library. Ang unang mirror ay masusing nakuha sa loob ng 2021 at 2022. Sa puntong ito, ito ay bahagyang lipas na: ito ay sumasalamin sa estado ng koleksyon noong Hunyo 2021. Iu-update namin ito sa hinaharap. Sa ngayon, nakatuon kami sa paglabas ng unang release na ito. Dahil ang Library Genesis ay naka-preserba na sa pamamagitan ng mga pampublikong torrent, at kasama na sa Z-Library, nagsagawa kami ng pangunahing deduplication laban sa Library Genesis noong Hunyo 2022. Para dito, ginamit namin ang mga MD5 hash. Malamang na marami pang duplicate na nilalaman sa library, tulad ng maraming file format ng parehong libro. Mahirap itong matukoy nang tumpak, kaya hindi namin ito ginagawa. Pagkatapos ng deduplication, mayroon kaming mahigit 2 milyong file, na may kabuuang halos 7TB. Ang koleksyon ay binubuo ng dalawang bahagi: isang MySQL “.sql.gz” dump ng metadata, at ang 72 torrent files na may humigit-kumulang 50-100GB bawat isa. Ang metadata ay naglalaman ng data na iniulat ng website ng Z-Library (pamagat, may-akda, paglalarawan, uri ng file), pati na rin ang aktwal na laki ng file at md5sum na aming naobserbahan, dahil minsan hindi ito nagkakatugma. Mukhang may mga saklaw ng mga file kung saan ang Z-Library mismo ay may maling metadata. Maaaring mayroon din kaming maling na-download na mga file sa ilang mga hiwalay na kaso, na susubukan naming tukuyin at ayusin sa hinaharap. Ang malalaking torrent files ay naglalaman ng aktwal na data ng libro, na may Z-Library ID bilang filename. Ang mga extension ng file ay maaaring mabuo muli gamit ang metadata dump. Ang koleksyon ay isang halo ng non-fiction at fiction na nilalaman (hindi hiwalay tulad sa Library Genesis). Ang kalidad ay iba-iba rin. Ang unang release na ito ay ganap nang magagamit. Tandaan na ang mga torrent files ay magagamit lamang sa pamamagitan ng aming Tor mirror. Release 1 (%(date)s) Ito ay isang solong karagdagang torrent file. Wala itong bagong impormasyon, ngunit mayroon itong ilang data na maaaring tumagal ng ilang oras upang kalkulahin. Ginagawa nitong maginhawa na magkaroon nito, dahil ang pag-download ng torrent na ito ay kadalasang mas mabilis kaysa sa pagkalkula nito mula sa simula. Partikular, naglalaman ito ng mga SQLite indexes para sa mga tar files, para magamit sa <a %(a_href)s>ratarmount</a>. Release 2 addendum (%(date)s) Nakuha namin ang lahat ng mga libro na idinagdag sa Z-Library sa pagitan ng aming huling mirror at Agosto 2022. Bumalik din kami at kinolekta ang ilang mga libro na hindi namin nakuha noong una. Sa kabuuan, ang bagong koleksyon na ito ay humigit-kumulang 24TB. Muli, ang koleksyon na ito ay deduplicated laban sa Library Genesis, dahil mayroon nang mga torrent na magagamit para sa koleksyon na iyon. Ang data ay nakaayos na katulad ng sa unang release. Mayroong MySQL “.sql.gz” dump ng metadata, na kinabibilangan din ng lahat ng metadata mula sa unang release, kaya't pinapalitan ito. Nagdagdag din kami ng ilang bagong mga kolum: Binanggit namin ito noong nakaraang beses, ngunit upang linawin: ang “filename” at “md5” ay ang aktwal na mga katangian ng file, samantalang ang “filename_reported” at “md5_reported” ay ang aming kinolekta mula sa Z-Library. Minsan ang dalawang ito ay hindi nagkakatugma, kaya isinama namin ang pareho. Para sa release na ito, binago namin ang collation sa “utf8mb4_unicode_ci”, na dapat na compatible sa mas lumang mga bersyon ng MySQL. Ang mga data files ay katulad ng sa nakaraang release, bagaman mas malaki ang mga ito. Hindi na namin pinilit na lumikha ng maraming mas maliliit na torrent files. Ang “pilimi-zlib2-0-14679999-extra.torrent” ay naglalaman ng lahat ng mga file na hindi namin nakuha sa nakaraang release, habang ang iba pang mga torrent ay lahat ng bagong ID ranges.  <strong>Update %(date)s:</strong> Ginawa naming masyadong malaki ang karamihan sa aming mga torrent, na nagdudulot ng problema sa mga torrent client. Tinanggal namin ang mga ito at naglabas ng mga bagong torrent. <strong>Update %(date)s:</strong> Masyado pa ring maraming mga file, kaya binalot namin ang mga ito sa mga tar files at naglabas muli ng mga bagong torrent. %(key)s: kung ang file na ito ay nasa Library Genesis na, sa alinman sa non-fiction o fiction na koleksyon (naitugma sa pamamagitan ng md5). %(key)s: kung aling torrent ang file na ito ay nasa. %(key)s: itinakda kapag hindi namin ma-download ang libro. Release 2 (%(date)s) Mga release ng Zlib (orihinal na mga pahina ng paglalarawan) Tor domain Pangunahing website Z-Library scrape Ang koleksyong "Chinese" sa Z-Library ay mukhang kapareho ng aming koleksyon ng DuXiu, ngunit may iba't ibang MD5. Ibinubukod namin ang mga file na ito mula sa mga torrent upang maiwasan ang pagdoble, ngunit ipinapakita pa rin ang mga ito sa aming index ng paghahanap. Metadata Makakakuha ka ng %(percentage)s%% bonus na mabilis na downloads, dahil ikaw ay na-refer ng user na %(profile_link)s. Ito ay naaangkop sa buong panahon ng pagiging miyembro. Mag-donate Sumali Pinili hanggang sa %(percentage)s%% diskwento Sinusuportahan ng Alipay ang mga international credit/debit cards. Tingnan ang <a %(a_alipay)s>gabay na ito</a> para sa karagdagang impormasyon. Ipadala sa amin ang Amazon.com gift cards gamit ang iyong credit/debit card. Maaari kang bumili ng crypto gamit ang credit/debit cards. Sinusuportahan ng WeChat (Weixin Pay) ang mga international credit/debit cards. Sa WeChat app, pumunta sa “Me => Services => Wallet => Add a Card”. Kung hindi mo ito makita, i-enable ito gamit ang “Me => Settings => General => Tools => Weixin Pay => Enable”. (gamitin kapag nagpapadala ng Ethereum mula sa Coinbase) nakopya! kopya (pinakamababang minimum na halaga) (babala: mataas na minimum na halaga) -%(percentage)s%% 12 buwan 1 buwan 24 buwan 3 buwan 48 na buwan 6 buwan 96 na buwan Piliin kung gaano katagal mo gustong mag-subscribe. <div %(div_monthly_cost)s></div><div %(div_after)s>pagkatapos ng <span %(span_discount)s></span> mga diskwento</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% para sa 12 buwan para sa 1 buwan para sa 24 buwan para sa 3 buwan para sa 48 buwan para sa 6 na buwan para sa 96 buwan %(monthly_cost)s / buwan makipag-ugnayan sa amin Direktang <strong>SFTP</strong> servers Donasyon o palitan sa antas ng enterprise para sa mga bagong koleksyon (hal. bagong scan, OCR’ed datasets). Ekspertong Access <strong>Walang limitasyong</strong> high-speed access <div %(div_question)s>Maaari ko bang i-upgrade ang aking membership o magkaroon ng maraming membership?</div> <div %(div_question)s>Maaari ba akong magbigay ng donasyon nang hindi nagiging miyembro?</div> Oo naman. Tumatanggap kami ng donasyon ng anumang halaga sa Monero (XMR) address na ito: %(address)s. <div %(div_question)s>Ano ang ibig sabihin ng mga saklaw kada buwan?</div> Maaari mong makuha ang mas mababang bahagi ng saklaw sa pamamagitan ng pag-aaplay ng lahat ng mga diskwento, tulad ng pagpili ng panahon na mas mahaba sa isang buwan. <div %(div_question)s>Awtomatikong nagre-renew ba ang mga membership?</div> Ang mga membership ay <strong>hindi</strong> awtomatikong nagre-renew. Maaari kang sumali ng kasing haba o kasing ikli ng gusto mo. <div %(div_question)s>Saan napupunta ang mga donasyon?</div> 100%% ay napupunta sa pagpapanatili at pagpapalaganap ng kaalaman at kultura ng mundo. Sa kasalukuyan, ginagastos namin ito karamihan sa mga servers, storage, at bandwidth. Walang pera ang napupunta sa sinumang miyembro ng team. <div %(div_question)s>Maaari ba akong magbigay ng malaking donasyon?</div> Iyon ay magiging kamangha-mangha! Para sa mga donasyon na higit sa ilang libong dolyar, mangyaring makipag-ugnayan sa amin nang direkta sa %(email)s. <div %(div_question)s>Mayroon ba kayong ibang paraan ng pagbabayad?</div> Sa kasalukuyan, wala. Maraming tao ang ayaw na magkaroon ng mga archive na tulad nito, kaya kailangan naming maging maingat. Kung maaari mo kaming tulungan na mag-set up ng iba pang (mas maginhawang) paraan ng pagbabayad nang ligtas, mangyaring makipag-ugnayan sa amin sa %(email)s. FAQ ng Donasyon Mayroon kang <a %(a_donation)s>umiiral na donasyon</a> na kasalukuyang ginagawa. Pakitapos o ikansela ang donasyon na iyon bago gumawa ng bagong donasyon. <a %(a_all_donations)s>Tingnan ang lahat ng aking mga donasyon</a> Para sa mga donasyon na higit sa $5000, mangyaring makipag-ugnayan sa amin nang direkta sa %(email)s. Tinatanggap namin ang malalaking donasyon mula sa mayayamang indibidwal o institusyon.  Tandaan na habang ang mga membership sa pahinang ito ay “bawat buwan”, ito ay mga isang-beses na donasyon (hindi paulit-ulit). Tingnan ang <a %(faq)s>Donation FAQ</a>. Ang Anna’s Archive ay isang non-profit, open-source, open-data na proyekto. Sa pamamagitan ng pag-donate at pagiging miyembro, sinusuportahan mo ang aming mga operasyon at pag-unlad. Sa lahat ng aming mga miyembro: salamat sa pagpapanatili sa amin! ❤️ Para sa karagdagang impormasyon, tingnan ang <a %(a_donate)s>Donation FAQ</a>. Upang maging miyembro, mangyaring <a %(a_login)s>Mag-log in o Magrehistro</a>. Salamat sa iyong suporta! $%(cost)s / buwan Kung nagkamali ka sa pagbabayad, hindi kami makakapag-refund, ngunit susubukan naming ayusin ito. Hanapin ang pahina ng “Crypto” sa iyong PayPal app o website. Karaniwan itong nasa ilalim ng “Finances”. Pumunta sa pahina ng “Bitcoin” sa iyong PayPal app o website. Pindutin ang button na “Transfer” %(transfer_icon)s, at pagkatapos ay “Send”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s gift card Bank card Bank card (gamit ang app) Binance Credit/debit/Apple/Google (BMC) Cash App Credit/debit card Credit/debit card 2 Credit/debit card (backup) Crypto %(bitcoin_icon)s Card / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regular) Pix (Brazil) Revolut (pansamantalang hindi magagamit) WeChat Piliin ang iyong paboritong crypto coin: Mag-donate gamit ang Amazon gift card. <strong>MAHALAGA:</strong> Ang opsyong ito ay para sa %(amazon)s. Kung nais mong gumamit ng ibang Amazon website, piliin ito sa itaas. <strong>MAHALAGA:</strong> Sinusuportahan lang namin ang Amazon.com, hindi ang iba pang mga website ng Amazon. Halimbawa, .de, .co.uk, .ca, ay HINDI sinusuportahan. Mangyaring HUWAG maglagay ng sarili mong mensahe. Ilagay ang eksaktong halaga: %(amount)s Tandaan na kailangan naming i-round sa mga halagang tinatanggap ng aming mga reseller (minimum %(minimum)s). Mag-donate gamit ang credit/debit card, sa pamamagitan ng Alipay app (napakadaling i-set up). I-install ang Alipay app mula sa <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>. Magrehistro gamit ang iyong numero ng telepono. Walang karagdagang personal na detalye ang kinakailangan. <span %(style)s>1</span>I-install ang Alipay app Suportado: Visa, MasterCard, JCB, Diners Club at Discover. Tingnan ang <a %(a_alipay)s>gabay na ito</a> para sa karagdagang impormasyon. <span %(style)s>2</span>Idagdag ang bank card Sa Binance, maaari kang bumili ng Bitcoin gamit ang credit/debit card o bank account, at pagkatapos ay i-donate ang Bitcoin na iyon sa amin. Sa ganitong paraan, mananatili kaming ligtas at anonymous sa pagtanggap ng iyong donasyon. Ang Binance ay available sa halos lahat ng bansa, at sinusuportahan ang karamihan ng mga bangko at credit/debit cards. Ito ang aming pangunahing rekomendasyon sa kasalukuyan. Pinahahalagahan namin ang iyong oras sa pag-aaral kung paano mag-donate gamit ang pamamaraang ito, dahil malaki ang maitutulong nito sa amin. Para sa credit cards, debit cards, Apple Pay, at Google Pay, ginagamit namin ang “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Sa kanilang sistema, ang isang “coffee” ay katumbas ng $5, kaya ang iyong donasyon ay iroround sa pinakamalapit na multiple ng 5. Mag-donate gamit ang Cash App. Kung mayroon kang Cash App, ito ang pinakamadaling paraan upang mag-donate! Tandaan na para sa mga transaksyon na mas mababa sa %(amount)s, maaaring maningil ang Cash App ng %(fee)s na bayad. Para sa %(amount)s o higit pa, libre ito! Mag-donate gamit ang credit o debit card. Ang pamamaraang ito ay gumagamit ng cryptocurrency provider bilang intermediate conversion. Maaari itong maging medyo nakakalito, kaya't mangyaring gamitin lamang ang pamamaraang ito kung hindi gumagana ang iba pang mga paraan ng pagbabayad. Hindi rin ito gumagana sa lahat ng bansa. Hindi namin direktang masuportahan ang credit/debit cards, dahil ayaw makipagtrabaho ng mga bangko sa amin. ☹ Gayunpaman, may ilang paraan para magamit ang credit/debit cards, gamit ang ibang mga paraan ng pagbabayad: Sa pamamagitan ng crypto, maaari kang mag-donate gamit ang BTC, ETH, XMR, at SOL. Gamitin ang opsyong ito kung pamilyar ka na sa cryptocurrency. Sa pamamagitan ng crypto, maaari kang mag-donate gamit ang BTC, ETH, XMR, at iba pa. Crypto express services Kung gagamit ka ng crypto sa unang pagkakataon, iminumungkahi naming gamitin ang %(options)s upang bumili at mag-donate ng Bitcoin (ang orihinal at pinakaginagamit na cryptocurrency). Tandaan na para sa maliliit na donasyon, maaaring mawala ang aming %(discount)s%% na diskwento dahil sa mga bayarin sa credit card, kaya inirerekomenda namin ang mas mahabang subscription. Mag-donate gamit ang credit/debit card, PayPal, o Venmo. Maaari kang pumili sa pagitan ng mga ito sa susunod na pahina. Maaaring gumana rin ang Google Pay at Apple Pay. Tandaan na para sa maliliit na donasyon, mataas ang mga bayarin, kaya inirerekomenda namin ang mas mahabang subscription. Upang mag-donate gamit ang PayPal US, gagamit kami ng PayPal Crypto, na nagpapahintulot sa amin na manatiling anonymous. Pinahahalagahan namin ang oras na ginugol mo upang matutunan kung paano mag-donate gamit ang pamamaraang ito, dahil malaki ang maitutulong nito sa amin. Mag-donate gamit ang PayPal. Mag-donate gamit ang iyong regular na PayPal account. Mag-donate gamit ang Revolut. Kung may Revolut ka, ito ang pinakamadaling paraan para mag-donate! Ang pamamaraang ito ng pagbabayad ay nagpapahintulot lamang ng maximum na %(amount)s. Mangyaring pumili ng ibang duration o paraan ng pagbabayad. Ang pamamaraang ito ng pagbabayad ay nangangailangan ng minimum na %(amount)s. Mangyaring pumili ng ibang tagal o paraan ng pagbabayad. Binance Coinbase Kraken Mangyaring pumili ng paraan ng pagbabayad. “Adopt a torrent”: ang iyong username o mensahe sa isang torrent filename <div %(div_months)s>isang beses bawat 12 buwan ng pagiging miyembro</div> Ang iyong username o anonymous na pagbanggit sa mga credits Maagang access sa mga bagong tampok Eksklusibong Telegram na may mga update sa likod ng mga eksena %(number)s mabilis na downloads bawat araw kung mag-donate ka ngayong buwan! <a %(a_api)s>JSON API</a> access Legendary status sa pagpepreserba ng kaalaman at kultura ng sangkatauhan Mga naunang benepisyo, kasama ang: Kumita ng <strong>%(percentage)s%% bonus downloads</strong> sa pamamagitan ng <a %(a_refer)s>pag-refer ng mga kaibigan</a>. SciDB papers <strong>walang limitasyon</strong> nang walang beripikasyon Kapag nagtatanong tungkol sa account o donasyon, idagdag ang iyong account ID, mga screenshot, resibo, at iba pang impormasyon hangga't maaari. Sinusuri lamang namin ang aming email tuwing 1-2 linggo, kaya ang hindi paglalagay ng impormasyong ito ay magpapabagal sa anumang resolusyon. Para makakuha ng mas maraming downloads, <a %(a_refer)s>i-refer ang iyong mga kaibigan</a>! Kami ay isang maliit na grupo ng mga boluntaryo. Maaaring abutin ng 1-2 linggo bago kami makasagot. Tandaan na ang pangalan ng account o larawan ay maaaring mukhang kakaiba. Walang dapat ipag-alala! Ang mga account na ito ay pinamamahalaan ng aming mga donation partners. Ang aming mga account ay hindi na-hack. Mag-donate ng <span %(span_cost)s></span> <span %(span_label)s></span> para sa 12 buwan “%(tier_name)s” para sa 1 buwan “%(tier_name)s” para sa 24 buwan “%(tier_name)s” para sa 3 buwan “%(tier_name)s” para sa 48 buwan “%(tier_name)s” para sa 6 buwan “%(tier_name)s” para sa 96 buwan “%(tier_name)s” Maaari mo pa ring kanselahin ang donasyon sa panahon ng pag-checkout. I-click ang pindutan ng donasyon upang kumpirmahin ang donasyon na ito. <strong>Mahalagang paalala:</strong> Ang mga presyo ng crypto ay maaaring magbago nang malaki, minsan kahit na 20%% sa loob ng ilang minuto. Ito ay mas mababa pa rin kaysa sa mga bayarin na natatamo namin sa maraming mga provider ng pagbabayad, na madalas maningil ng 50-60%% para sa pakikipagtulungan sa isang “shadow charity” tulad namin. <u>Kung ipapadala mo sa amin ang resibo na may orihinal na presyo na binayaran mo, ikikredito pa rin namin ang iyong account para sa napiling membership</u> (hangga't ang resibo ay hindi mas matanda sa ilang oras). Talagang pinahahalagahan namin na handa kang tiisin ang mga ganitong bagay upang suportahan kami! ❤️ ❌ May nangyaring mali. Mangyaring i-reload ang page at subukang muli. <span %(span_circle)s>1</span>Bumili ng Bitcoin sa Paypal <span %(span_circle)s>2</span>Ilipat ang Bitcoin sa aming address ✅ Ire-redirect sa pahina ng donasyon… Mag-donate Mangyaring maghintay ng hindi bababa sa <span %(span_hours)s>24 na oras</span> (at i-refresh ang pahinang ito) bago makipag-ugnayan sa amin. Kung nais mong magbigay ng donasyon (anumang halaga) nang walang membership, malugod na gamitin ang Monero (XMR) address na ito: %(address)s. Pagkatapos magpadala ng iyong gift card, ang aming automated na sistema ay magkokompirma nito sa loob ng ilang minuto. Kung hindi ito gumana, subukang ipadala muli ang iyong gift card (<a %(a_instr)s>mga tagubilin</a>). Kung hindi pa rin ito gumana, mangyaring i-email kami at manu-manong susuriin ito ni Anna (maaari itong tumagal ng ilang araw), at tiyaking banggitin kung sinubukan mo nang ipadala muli. Halimbawa: Mangyaring gamitin ang <a %(a_form)s>opisyal na form ng Amazon.com</a> upang magpadala sa amin ng gift card na %(amount)s sa email address sa ibaba. “To” recipient email sa form: Amazon gift card Hindi kami makakatanggap ng ibang paraan ng gift cards, <strong>na ipinadala lamang direkta mula sa opisyal na form sa Amazon.com</strong>. Hindi namin maibabalik ang iyong gift card kung hindi mo gagamitin ang form na ito. Gamitin lamang nang isang beses. Natatangi sa iyong account, huwag ibahagi. Naghihintay ng gift card… (i-refresh ang pahina upang tingnan) Buksan ang <a %(a_href)s>QR-code donation page</a>. I-scan ang QR code gamit ang Alipay app, o pindutin ang button para buksan ang Alipay app. Mangyaring maging matiyaga; maaaring magtagal ang pag-load ng pahina dahil ito ay nasa Tsina. <span %(style)s>3</span>Gumawa ng donasyon (i-scan ang QR code o pindutin ang button) Bumili ng PYUSD coin sa PayPal Bumili ng Bitcoin (BTC) sa Cash App Bumili ng kaunti pang Bitcoin (inirerekomenda naming %(more)s higit pa) kaysa sa halagang idino-donate mo (%(amount)s), upang masakop ang mga bayarin sa transaksyon. Mapapasaiyo ang anumang matitira. Pumunta sa pahina ng “Bitcoin” (BTC) sa Cash App. I-transfer ang Bitcoin sa aming address Para sa maliliit na donasyon (mas mababa sa $25), maaaring kailanganin mong gumamit ng Rush o Priority. I-click ang “Send bitcoin” na button upang mag-“withdrawal”. Palitan mula dolyar patungong BTC sa pamamagitan ng pagpindot sa %(icon)s icon. I-enter ang BTC na halaga sa ibaba at i-click ang “Send”. Tingnan ang <a %(help_video)s>video na ito</a> kung ikaw ay nahihirapan. Ang express services ay maginhawa, ngunit may mas mataas na bayarin. Maaari mong gamitin ito sa halip na crypto exchange kung nais mong mabilis na makapagbigay ng mas malaking donasyon at hindi alintana ang bayad na $5-10. Siguraduhing ipadala ang eksaktong crypto amount na ipinapakita sa pahina ng donasyon, hindi ang halaga sa $USD. Kung hindi, ang bayad ay ibabawas at hindi namin maiproseso nang awtomatiko ang iyong membership. Minsan ang kumpirmasyon ay maaaring umabot ng hanggang 24 oras, kaya siguraduhing i-refresh ang pahinang ito (kahit na ito ay nag-expire na). Mga tagubilin para sa credit / debit card Mag-donate sa pamamagitan ng aming credit / debit card page Ang ilan sa mga hakbang ay binabanggit ang crypto wallets, ngunit huwag mag-alala, hindi mo kailangang matuto ng kahit ano tungkol sa crypto para dito. Mga tagubilin para sa %(coin_name)s I -scan ang QR code na ito gamit ang iyong Crypto Wallet app upang mabilis na punan ang mga detalye ng pagbabayad I -scan ang QR code upang magbayad Sinusuportahan lamang namin ang standard na bersyon ng crypto coins, walang exotic na network o bersyon ng coins. Maaaring tumagal ng hanggang isang oras upang makumpirma ang transaksyon, depende sa coin. Mag-donate ng %(amount)s sa <a %(a_page)s>pahinang ito</a>. Nag-expire na ang donasyon na ito. Pakikansela at gumawa ng bago. Kung nakapagbayad ka na: Oo, na-email ko na ang aking resibo Kung nagbago ang crypto exchange rate habang nagta-transact, siguraduhing isama ang resibo na nagpapakita ng orihinal na exchange rate. Talagang pinahahalagahan namin ang iyong pagsisikap na gumamit ng crypto, malaki ang naitutulong nito sa amin! ❌ May nangyaring mali. Pakireload ang pahina at subukang muli. <span %(span_circle)s>%(circle_number)s</span>I-email sa amin ang resibo Kung makakaranas ka ng anumang isyu, mangyaring makipag-ugnayan sa amin sa %(email)s at isama ang mas maraming impormasyon hangga't maaari (tulad ng mga screenshot). ✅ Salamat sa iyong donasyon! Manu-manong ia-activate ni Anna ang iyong membership sa loob ng ilang araw. Ipadala ang resibo o screenshot sa iyong personal na verification address: Kapag na-email mo na ang iyong resibo, i-click ang button na ito, upang manu-manong ma-review ni Anna (maaari itong tumagal ng ilang araw): Magpadala ng resibo o screenshot sa iyong personal na verification address. HUWAG gamitin ang email address na ito para sa iyong PayPal donation. Kanselahin Oo, pakikansela Sigurado ka bang nais mong kanselahin? Huwag kanselahin kung ikaw ay nakapagbayad na. ❌ May nangyaring mali. Pakireload ang pahina at subukang muli. Gumawa ng bagong donasyon ✅ Kanselado na ang iyong donasyon. Petsa: %(date)s Tagatukoy: %(id)s Muling mag-order Katayuan: <span %(span_label)s>%(label)s</span> Kabuuan: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / buwan para sa %(duration)s buwan, kasama ang %(discounts)s%% diskwento)</span> Kabuuan: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / buwan para sa %(duration)s buwan)</span> 1. Ipasok ang iyong email. 2. Piliin ang iyong paraan ng pagbabayad. 3. Piliin muli ang iyong paraan ng pagbabayad. 4. Piliin ang “Self-hosted” na wallet. 5. I-click ang “I confirm ownership”. 6. Dapat kang makatanggap ng email resibo. Pakipadala iyon sa amin, at ikukumpirma namin ang iyong donasyon sa lalong madaling panahon. (maaaring nais mong kanselahin at lumikha ng bagong donasyon) Ang mga tagubilin sa pagbabayad ay lipas na. Kung nais mong gumawa ng isa pang donasyon, gamitin ang “Muling mag-order” na button sa itaas. Nabayaran mo na. Kung nais mong suriin ang mga tagubilin sa pagbabayad, i-click dito: Ipakita ang lumang mga tagubilin sa pagbabayad Kung ma-block ang pahina ng donasyon, subukan ang ibang koneksyon sa internet (hal. VPN o internet ng telepono). Sa kasamaang palad, ang pahina ng Alipay ay madalas na naa-access lamang mula sa <strong>mainland China</strong>. Maaaring kailanganin mong pansamantalang i-disable ang iyong VPN, o gumamit ng VPN papuntang mainland China (o minsan ay gumagana rin ang Hong Kong). <span %(span_circle)s>1</span>Mag-donate sa Alipay I-donate ang kabuuang halaga na %(total)s gamit ang <a %(a_account)s>account na ito sa Alipay</a> Mga tagubilin sa Alipay <span %(span_circle)s>1</span>Ilipat sa isa sa aming mga crypto account I-donate ang kabuuang halaga ng %(total)s sa isa sa mga address na ito: Mga tagubilin sa Crypto Sundin ang mga tagubilin upang bumili ng Bitcoin (BTC). Kailangan mo lamang bumili ng halagang nais mong i-donate, %(total)s. Ilagay ang aming Bitcoin (BTC) address bilang tatanggap, at sundin ang mga tagubilin upang ipadala ang iyong donasyon na %(total)s: <span %(span_circle)s>1</span>Mag-donate sa Pix I-donate ang kabuuang halaga ng %(total)s gamit ang <a %(a_account)s>account na ito sa Pix Mga tagubilin sa Pix <span %(span_circle)s>1</span>Mag-donate sa WeChat I-donate ang kabuuang halaga na %(total)s gamit ang <a %(a_account)s>account na ito sa WeChat</a> Mga tagubilin sa WeChat Gamitin ang alinman sa mga sumusunod na “credit card to Bitcoin” express services, na tumatagal lamang ng ilang minuto: BTC / Bitcoin address (external wallet): Halaga ng BTC / Bitcoin: Punan ang mga sumusunod na detalye sa form: Kung ang alinman sa impormasyong ito ay hindi na napapanahon, mangyaring i-email kami upang ipaalam sa amin. Mangyaring gamitin ang <span %(underline)s>eksaktong halagang</span> ito. Ang iyong kabuuang gastos ay maaaring mas mataas dahil sa mga bayarin sa credit card. Para sa maliliit na halaga, maaaring mas mataas ito kaysa sa aming diskwento, sa kasamaang-palad. (minimum: %(minimum)s, walang beripikasyon para sa unang transaksyon) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, walang beripikasyon para sa unang transaksyon) (minimum: %(minimum)s) (minimum: %(minimum)s depende sa bansa, walang beripikasyon para sa unang transaksyon) Sundin ang mga tagubilin upang bumili ng PYUSD coin (PayPal USD). Bumili ng kaunti pang (inirerekomenda namin ang %(more)s higit pa) kaysa sa halagang iyong idino-donate (%(amount)s), upang masakop ang mga bayarin sa transaksyon. Mapapasaiyo ang anumang matitira. Pumunta sa pahina ng “PYUSD” sa iyong PayPal app o website. Pindutin ang “Transfer” na button %(icon)s, at pagkatapos ay “Send”. I-update ang katayuan Upang i-reset ang timer, lumikha lamang ng bagong donasyon. Tiyakin na gamitin ang halaga ng BTC sa ibaba, <em>HINDI</em> euros o dolyar, kung hindi ay hindi namin matatanggap ang tamang halaga at hindi namin maikukumpirma ang iyong pagiging miyembro nang awtomatiko. Bumili ng Bitcoin (BTC) sa Revolut Bumili ng kaunti pang Bitcoin (inirerekomenda naming %(more)s higit pa) kaysa sa halagang idino-donate mo (%(amount)s), upang masakop ang mga bayarin sa transaksyon. Mapapasaiyo ang anumang matitira. Pumunta sa pahina ng “Crypto” sa Revolut upang bumili ng Bitcoin (BTC). I-transfer ang Bitcoin sa aming address Para sa maliliit na donasyon (mas mababa sa $25), maaaring kailanganin mong gumamit ng Rush o Priority. I-click ang “Send bitcoin” na button upang mag-“withdrawal”. Palitan mula euro patungong BTC sa pamamagitan ng pagpindot sa %(icon)s icon. I-enter ang BTC na halaga sa ibaba at i-click ang “Send”. Tingnan ang <a %(help_video)s>video na ito</a> kung ikaw ay nahihirapan. Katayuan: 1 2 Step-by-step na gabay Tingnan ang step-by-step na gabay sa ibaba. Kung hindi, maaari kang ma-lock out sa account na ito! Kung hindi mo pa nagagawa, isulat ang iyong lihim na susi para sa pag-login: Salamat sa iyong donasyon! Natitirang oras: Donasyon I-transfer ang %(amount)s sa %(account)s Naghihintay ng kumpirmasyon (i-refresh ang pahina upang suriin)… Naghihintay ng transfer (i-refresh ang pahina upang suriin)… Kanina Ang mabilis na download sa huling 24 oras ay binibilang sa pang-araw-araw na limitasyon. Ang mga download mula sa Fast Partner Servers ay minarkahan ng %(icon)s. Huling 18 oras Wala pang na-download na file. Ang mga na-download na file ay hindi ipinapakita sa publiko. Ang lahat ng oras ay nasa UTC. Na-download na mga file Kung nag-download ka ng file na may parehong mabilis at mabagal na download, ito ay lalabas ng dalawang beses. Huwag masyadong mag-alala, maraming tao ang nagda-download mula sa mga website na naka-link sa amin, at napakabihirang magkaroon ng problema. Gayunpaman, upang manatiling ligtas, inirerekomenda namin ang paggamit ng VPN (bayad), o <a %(a_tor)s>Tor</a> (libre). Nag-download ako ng 1984 ni George Orwell, darating ba ang pulis sa aking pintuan? Ikaw si Anna! Sino si Anna? Mayroon kaming isang stable na JSON API para sa mga miyembro, para makakuha ng mabilis na download URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasyon sa loob ng JSON mismo). Para sa iba pang mga kaso ng paggamit, tulad ng pag-iterate sa lahat ng aming mga file, pagbuo ng custom na paghahanap, at iba pa, inirerekomenda namin ang <a %(a_generate)s>pag-generate</a> o <a %(a_download)s>pag-download</a> ng aming ElasticSearch at MariaDB databases. Ang raw data ay maaaring manu-manong tuklasin <a %(a_explore)s>sa pamamagitan ng mga JSON file</a>. Ang aming raw torrents list ay maaaring i-download bilang <a %(a_torrents)s>JSON</a> din. Mayroon ba kayong API? Hindi kami nagho-host ng anumang copyrighted na materyales dito. Kami ay isang search engine, at bilang ganoon, nag-iindex lamang kami ng metadata na pampublikong magagamit na. Kapag nagda-download mula sa mga panlabas na pinagmulan, iminumungkahi naming suriin ang mga batas sa iyong hurisdiksyon tungkol sa kung ano ang pinapayagan. Hindi kami responsable para sa nilalaman na hino-host ng iba. Kung mayroon kang mga reklamo tungkol sa nakikita mo dito, ang pinakamainam na gawin ay makipag-ugnayan sa orihinal na website. Regular naming kinukuha ang kanilang mga pagbabago sa aming database. Kung talagang sa tingin mo ay may valid na reklamo sa DMCA na dapat naming tugunan, mangyaring punan ang <a %(a_copyright)s>DMCA / Copyright claim form</a>. Seryoso naming tinatrato ang iyong mga reklamo, at babalikan ka namin sa lalong madaling panahon. Paano ko irereport ang paglabag sa copyright? Narito ang ilang mga libro na may espesyal na kahalagahan sa mundo ng shadow libraries at digital preservation: Ano ang mga paborito mong libro? Nais din naming ipaalala sa lahat na ang lahat ng aming code at data ay ganap na open source. Ito ay natatangi para sa mga proyekto tulad ng sa amin — wala kaming alam na anumang iba pang proyekto na may katulad na napakalaking katalogo na ganap ding open source. Malugod naming tinatanggap ang sinumang nag-iisip na mali ang pagpapatakbo namin ng aming proyekto na kunin ang aming code at data at mag-set up ng kanilang sariling shadow library! Hindi namin sinasabi ito dahil sa galit o kung ano pa man — tunay naming iniisip na magiging kahanga-hanga ito dahil itataas nito ang pamantayan para sa lahat, at mas mapapanatili ang pamana ng sangkatauhan. Ayoko kung paano mo pinapatakbo ang proyektong ito! Gusto naming mag-set up ang mga tao ng <a %(a_mirrors)s>mirrors</a>, at susuportahan namin ito sa pinansyal na paraan. Paano ako makakatulong? Oo, nangongolekta kami. Ang aming inspirasyon para sa pagkolekta ng metadata ay ang layunin ni Aaron Swartz na “isang web page para sa bawat aklat na kailanman ay nailathala”, kung saan nilikha niya ang <a %(a_openlib)s>Open Library</a>. Maganda ang nagawa ng proyektong iyon, ngunit ang aming natatanging posisyon ay nagbibigay-daan sa amin na makakuha ng metadata na hindi nila kaya. Ang isa pang inspirasyon ay ang aming pagnanais na malaman <a %(a_blog)s>kung gaano karaming mga libro ang mayroon sa mundo</a>, upang makalkula namin kung gaano karaming mga libro ang kailangan pa naming iligtas. Nangongolekta ba kayo ng metadata? Tandaan na ang mhut.org ay nagba-block ng ilang IP ranges, kaya maaaring kailanganin ang VPN. <strong>Android:</strong> I-click ang three-dot menu sa kanang itaas, at piliin ang “Add to Home Screen”. <strong>iOS:</strong> I-click ang “Share” button sa ibaba, at piliin ang “Add to Home Screen”. Wala kaming opisyal na mobile app, ngunit maaari mong i-install ang website na ito bilang isang app. Mayroon ba kayong mobile app? Pakipadala ang mga ito sa <a %(a_archive)s>Internet Archive</a>. Sila ang maayos na mag-iingat nito. Paano ako makapagdo-donate ng mga libro o iba pang pisikal na materyales? Paano ako mag-request ng mga libro? <a %(a_blog)s>Blog ni Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regular na mga update <a %(a_software)s>Software ni Anna</a> — ang aming open source code <a %(a_datasets)s>Datasets</a> — tungkol sa data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatibong mga domain Mayroon bang higit pang mga mapagkukunan tungkol sa Anna’s Archive? <a %(a_translate)s>Isalin sa Software ni Anna</a> — ang aming translation system <a %(a_wikipedia)s>Wikipedia</a> — higit pa tungkol sa amin (mangyaring tulungan panatilihing na-update ang pahinang ito, o lumikha ng isa para sa iyong sariling wika!) Piliin ang mga setting na gusto mo, iwanang walang laman ang search box, i-click ang “Search”, at pagkatapos ay i-bookmark ang pahina gamit ang bookmark feature ng iyong browser. Paano ko ise-save ang aking mga setting sa paghahanap? Tinatanggap namin ang mga security researcher na maghanap ng mga kahinaan sa aming mga sistema. Kami ay malalaking tagasuporta ng responsible disclosure. Makipag-ugnayan sa amin <a %(a_contact)s>dito</a>. Sa kasalukuyan, hindi kami makapagbigay ng bug bounties, maliban sa mga kahinaan na may <a %(a_link)s>potensyal na ikompromiso ang aming anonymity</a>, kung saan nag-aalok kami ng bounties sa halagang $10k-50k. Nais naming mag-alok ng mas malawak na saklaw para sa bug bounties sa hinaharap! Pakitandaan na ang mga social engineering attack ay wala sa saklaw. Kung ikaw ay interesado sa offensive security, at nais mong tumulong sa pag-archive ng kaalaman at kultura ng mundo, siguraduhing makipag-ugnayan sa amin. Maraming paraan kung paano ka makakatulong. Mayroon ba kayong responsible disclosure program? Literal na wala kaming sapat na mga mapagkukunan upang bigyan ang lahat sa mundo ng high-speed downloads, kahit gaano pa namin kagustuhin. Kung may isang mayamang tagapagtaguyod na nais magbigay nito para sa amin, magiging kamangha-mangha iyon, ngunit hanggang sa mangyari iyon, ginagawa namin ang aming makakaya. Kami ay isang non-profit na proyekto na halos umaasa lamang sa mga donasyon. Ito ang dahilan kung bakit namin ipinatupad ang dalawang sistema para sa libreng downloads, kasama ang aming mga kasosyo: mga shared servers na may mabagal na downloads, at bahagyang mas mabilis na servers na may waitlist (upang mabawasan ang bilang ng mga taong nagda-download nang sabay-sabay). Mayroon din kaming <a %(a_verification)s>browser verification</a> para sa aming mabagal na downloads, dahil kung hindi, aabusuhin ito ng mga bots at scrapers, na magpapabagal pa lalo para sa mga lehitimong gumagamit. Tandaan na, kapag gumagamit ng Tor Browser, maaaring kailanganin mong ayusin ang iyong mga setting ng seguridad. Sa pinakamababang opsyon, na tinatawag na “Standard”, nagtatagumpay ang Cloudflare turnstile challenge. Sa mas mataas na mga opsyon, na tinatawag na “Safer” at “Safest”, nabibigo ang challenge. Para sa malalaking file, minsan ang mabagal na pag-download ay maaaring maputol sa gitna. Inirerekomenda namin ang paggamit ng download manager (tulad ng JDownloader) upang awtomatikong ipagpatuloy ang malalaking pag-download. Bakit napakabagal ng mabagal na pag-download? Mga Madalas Itanong (FAQ) Gamitin ang <a %(a_list)s>torrent list generator</a> upang makabuo ng listahan ng mga torrents na pinaka-kailangan ng pag-torrent, sa loob ng iyong mga limitasyon sa storage space. Oo, tingnan ang pahina ng <a %(a_llm)s>LLM data</a>. Karamihan sa mga torrent ay naglalaman ng mga file nang direkta, na nangangahulugang maaari mong utusan ang mga torrent client na i-download lamang ang mga kinakailangang file. Upang matukoy kung aling mga file ang i-download, maaari mong <a %(a_generate)s>i-generate</a> ang aming metadata, o <a %(a_download)s>i-download</a> ang aming ElasticSearch at MariaDB databases. Sa kasamaang palad, ang ilang mga torrent collection ay naglalaman ng .zip o .tar file sa root, kung saan kailangan mong i-download ang buong torrent bago mo mapili ang mga indibidwal na file. Wala pang madaling gamiting mga kasangkapan para sa pag-filter ng mga torrent, ngunit tinatanggap namin ang mga kontribusyon. (Mayroon kaming <a %(a_ideas)s>ilang ideya</a> para sa huling kaso na ito.) Mahabang sagot: Maikling sagot: hindi madali. Sinusubukan naming panatilihing minimal ang pagdodoble o overlap sa pagitan ng mga torrent sa listahang ito, ngunit hindi ito palaging makakamit, at nakasalalay nang malaki sa mga patakaran ng mga source library. Para sa mga library na naglalabas ng kanilang sariling mga torrent, wala na ito sa aming mga kamay. Para sa mga torrent na inilabas ng Anna’s Archive, nagdededuplicate kami batay lamang sa MD5 hash, na nangangahulugang ang iba't ibang bersyon ng parehong libro ay hindi nadededuplicate. Oo. Ang mga ito ay talagang mga PDF at EPUB, wala lang silang extension sa marami sa aming mga torrent. Mayroong dalawang lugar kung saan maaari mong makita ang metadata para sa mga torrent file, kabilang ang mga uri ng file/extension: 1. Ang bawat koleksyon o release ay may sariling metadata. Halimbawa, ang mga <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> ay may katumbas na metadata database na naka-host sa website ng Libgen.rs. Karaniwan naming iniuugnay ang mga kaugnay na mapagkukunan ng metadata mula sa bawat pahina ng <a %(a_datasets)s>dataset</a> ng koleksyon. 2. Inirerekomenda namin ang <a %(a_generate)s>pag-generate</a> o <a %(a_download)s>pag-download</a> ng aming ElasticSearch at MariaDB databases. Ang mga ito ay naglalaman ng mapping para sa bawat record sa Anna’s Archive sa mga katumbas nitong torrent file (kung available), sa ilalim ng “torrent_paths” sa ElasticSearch JSON. Ang ilang torrent client ay hindi sumusuporta sa malalaking piraso, na marami sa aming mga torrent ang mayroon (para sa mga bago, hindi na namin ito ginagawa — kahit na ito ay wasto ayon sa mga detalye!). Kaya subukan ang ibang client kung makakaranas ka nito, o magreklamo sa mga gumawa ng iyong torrent client. Gusto kong tumulong sa pag-seed, ngunit wala akong masyadong disk space. Masyadong mabagal ang mga torrents; maaari ko bang i-download ang data nang direkta mula sa inyo? Maaari ko bang i-download lamang ang isang subset ng mga file, tulad ng isang partikular na wika o paksa? Paano ninyo hinahawakan ang mga duplicate sa mga torrent? Maaari ko bang makuha ang listahan ng torrent bilang JSON? Hindi ko nakikita ang mga PDF o EPUB sa mga torrent, tanging mga binary file? Ano ang gagawin ko? Bakit hindi mabuksan ng aking torrent client ang ilan sa inyong mga torrent file / magnet link? Torrents FAQ Paano ako mag-upload ng mga bagong libro? Pakiusap tingnan ang <a %(a_href)s>napakahusay na proyektong ito</a>. Mayroon ka bang uptime monitor? Ano ang Anna’s Archive? Maging miyembro upang magamit ang mabilis na pag-download. Ngayon ay sinusuportahan na namin ang Amazon gift cards, credit at debit cards, crypto, Alipay, at WeChat. Naubos mo na ang mabilis na pag-download ngayon. Pag-access Oras-oras na pag-download sa nakalipas na 30 araw. Oras-oras na average: %(hourly)s. Araw-araw na average: %(daily)s. Nakikipagtulungan kami sa mga kasosyo upang gawing madali at malayang magagamit ang aming mga koleksyon sa sinuman. Naniniwala kami na ang lahat ay may karapatan sa kolektibong karunungan ng sangkatauhan. At <a %(a_search)s>hindi sa kapinsalaan ng mga may-akda</a>. Ang mga datasets na ginamit sa Arkibo ni Anna ay ganap na bukas, at maaaring i-mirror nang maramihan gamit ang torrents. <a %(a_datasets)s>Matuto pa…</a> Pangmatagalang arkibo Buong database Maghanap Mga libro, papel, magasin, komiks, tala ng aklatan, metadata, … Ang lahat ng aming <a %(a_code)s>code</a> at <a %(a_datasets)s>data</a> ay ganap na open source. <span %(span_anna)s>Anna’s Archive</span> ay isang non-profit na proyekto na may dalawang layunin: <li><strong>Pagpapanatili:</strong> Pag-backup ng lahat ng kaalaman at kultura ng sangkatauhan.</li><li><strong>Pag-access:</strong> Ginagawang magagamit ang kaalamang ito at kultura sa sinuman sa mundo.</li> Mayroon kaming pinakamalaking koleksyon ng mataas na kalidad na text data sa buong mundo. <a %(a_llm)s>Matuto pa…</a> LLM training data 🪩 Mga Mirror: panawagan para sa mga boluntaryo Kung nagpapatakbo ka ng isang high-risk anonymous payment processor, mangyaring makipag-ugnayan sa amin. Naghahanap din kami ng mga taong nais maglagay ng mga disente at maliliit na patalastas. Ang lahat ng kita ay mapupunta sa aming mga pagsisikap sa pagpepreserba. Pagpapanatili Tinataya namin na napapanatili namin ang tungkol sa <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% ng mga libro sa mundo</a>. Pinapanatili namin ang mga libro, papel, komiks, magasin, at iba pa, sa pamamagitan ng pagdadala ng mga materyales na ito mula sa iba't ibang <a href="https://en.wikipedia.org/wiki/Shadow_library">shadow libraries</a>, opisyal na mga aklatan, at iba pang mga koleksyon sa isang lugar. Ang lahat ng data na ito ay pinapanatili magpakailanman sa pamamagitan ng pagpapadali ng pag-duplicate nito nang maramihan — gamit ang torrents — na nagreresulta sa maraming kopya sa buong mundo. Ang ilang shadow libraries ay ginagawa na ito mismo (hal. Sci-Hub, Library Genesis), habang ang Anna’s Archive ay “pinalalaya” ang ibang mga aklatan na hindi nag-aalok ng maramihang distribusyon (hal. Z-Library) o hindi shadow libraries (hal. Internet Archive, DuXiu). Ang malawak na distribusyong ito, kasama ang open-source na code, ay ginagawang matatag ang aming website laban sa mga takedown, at tinitiyak ang pangmatagalang pagpapanatili ng kaalaman at kultura ng sangkatauhan. Alamin pa ang tungkol sa <a href="/datasets">aming datasets</a>. Kung ikaw ay isang <a %(a_member)s>miyembro</a>, hindi na kailangan ng beripikasyon ng browser. 🧬&nbsp;Ang SciDB ay isang pagpapatuloy ng Sci-Hub. SciDB Bukas DOI Ang Sci-Hub ay <a %(a_paused)s>huminto</a> sa pag-upload ng mga bagong papel. Direktang access sa %(count)s mga akademikong papel 🧬&nbsp;Ang SciDB ay isang pagpapatuloy ng Sci-Hub, na may pamilyar na interface at direktang pagtingin ng mga PDF. Ipasok ang iyong DOI upang makita. Mayroon kaming buong koleksyon ng Sci-Hub, pati na rin ang mga bagong papel. Karamihan ay maaaring direktang makita gamit ang pamilyar na interface, katulad ng Sci-Hub. Ang ilan ay maaaring i-download sa pamamagitan ng mga panlabas na mapagkukunan, kung saan ipinapakita namin ang mga link patungo sa mga iyon. Maaari kang makatulong nang malaki sa pamamagitan ng pag-seed ng torrents. <a %(a_torrents)s>Matuto pa…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Naghahanap ng mga boluntaryo Bilang isang non-profit, open-source na proyekto, palagi kaming naghahanap ng mga taong makakatulong. IPFS downloads Listahan ni %(by)s, nilikha <span %(span_time)s>%(time)s</span> I-save ❌ May nangyaring mali. Pakisubukang muli. ✅ Nai-save. Pakiload muli ang pahina. Walang laman ang listahan. i-edit Magdagdag o mag-alis mula sa listahang ito sa pamamagitan ng paghahanap ng file at pagbubukas ng tab na “Mga Listahan”. Listahan Paano kami makakatulong Pag-alis ng pag-uulit (deduplication) Pagkuha ng teksto at metadata OCR Kaya naming magbigay ng mataas na bilis ng pag-access sa aming buong koleksyon, pati na rin sa mga hindi pa nailalabas na koleksyon. Ito ay enterprise-level na pag-access na maaari naming ibigay kapalit ng mga donasyon sa halagang sampu-sampung libong USD. Handa rin kaming ipagpalit ito para sa mataas na kalidad na mga koleksyon na wala pa kami. Maaari ka naming i-refund kung makakapagbigay ka sa amin ng pagpapayaman ng aming data, tulad ng: Suportahan ang pangmatagalang pag-archive ng kaalaman ng tao, habang nakakakuha ng mas mahusay na data para sa iyong modelo! <a %(a_contact)s>Makipag-ugnayan sa amin</a> upang talakayin kung paano tayo maaaring magtulungan. Naiintindihan na ang mga LLM ay umuunlad sa mataas na kalidad na data. Mayroon kaming pinakamalaking koleksyon ng mga libro, papel, magasin, atbp sa buong mundo, na ilan sa mga pinakamataas na kalidad na pinagmumulan ng teksto. LLM data Natatanging saklaw at lawak Ang aming koleksyon ay naglalaman ng higit sa isang daang milyong file, kabilang ang mga akademikong journal, mga aklat-aralin, at mga magasin. Naabot namin ang sukat na ito sa pamamagitan ng pagsasama-sama ng malalaking umiiral na mga repositoryo. Ang ilan sa aming mga pinagmumulan ng koleksyon ay magagamit na sa bulk (Sci-Hub, at mga bahagi ng Libgen). Ang iba pang mga pinagmumulan ay pinalaya namin mismo. <a %(a_datasets)s>Datasets</a> ay nagpapakita ng buong pangkalahatang-ideya. Ang aming koleksyon ay kinabibilangan ng milyun-milyong mga libro, papel, at magasin mula bago ang panahon ng e-book. Malalaking bahagi ng koleksyon na ito ay na-OCR na, at mayroon nang kaunting panloob na pag-uulit. Magpatuloy Kung nawala mo ang iyong susi, mangyaring <a %(a_contact)s>makipag-ugnayan sa amin</a> at magbigay ng mas maraming impormasyon hangga't maaari. Maaaring kailanganin mong pansamantalang lumikha ng bagong account upang makipag-ugnayan sa amin. Mangyaring <a %(a_account)s>mag-login</a> upang makita ang pahinang ito.</a> Upang maiwasan ang spam-bots sa paglikha ng maraming account, kailangan naming patunayan muna ang iyong browser. Kung ikaw ay maipit sa isang walang katapusang loop, inirerekomenda naming i-install ang <a %(a_privacypass)s>Privacy Pass</a>. Makakatulong din na patayin ang mga ad blocker at iba pang mga extension ng browser. Mag-log in / Magrehistro Ang Anna’s Archive ay pansamantalang hindi magagamit para sa maintenance. Mangyaring bumalik makalipas ang isang oras. Alternatibong may-akda Alternatibong paglalarawan Alternatibong edisyon Alternatibong ekstensyon Alternatibong filename Alternatibong tagapaglathala Alternatibong pamagat petsa ng pagbubukas ng source Magbasa pa… paglalarawan Hanapin sa Anna’s Archive ang CADAL SSNO number Hanapin sa Anna’s Archive ang DuXiu SSID number Hanapin sa Anna’s Archive ang DuXiu DXID number Maghanap sa Anna’s Archive para sa ISBN Hanapin sa Anna’s Archive ang OCLC (WorldCat) number Hanapin sa Anna’s Archive ang Open Library ID Online viewer ng Arkibo ni Anna %(count)s apektadong mga pahina Pagkatapos mag-download: Maaaring may mas magandang bersyon ng file na ito sa %(link)s Maramihang pag-download ng torrent koleksyon Gumamit ng mga online na kasangkapan upang mag-convert sa pagitan ng mga format. Inirerekomendang mga kasangkapan sa conversion: %(links)s Para sa malalaking file, inirerekomenda naming gumamit ng download manager upang maiwasan ang pagkaantala. Inirerekomendang download managers: %(links)s EBSCOhost eBook Index (para lamang sa mga eksperto) (pindutin din ang “GET” sa itaas) (pindutin ang “GET” sa itaas) Panlabas na pag-download <strong>🚀 Mabilis na pag-download</strong> Mayroon kang %(remaining)s natitira ngayon. Salamat sa pagiging miyembro! ❤️ <strong>🚀 Mabilis na pag-download</strong> Naubos mo na ang mabilis na pag-download para sa araw na ito. <strong>🚀 Mabilis na pag-download</strong> Na-download mo na ang file na ito kamakailan. Mananatiling valid ang mga link nang ilang sandali. <strong>🚀 Mabilis na pag-download</strong> Maging isang <a %(a_membership)s>miyembro</a> upang suportahan ang pangmatagalang pag-iingat ng mga libro, papel, at iba pa. Bilang pasasalamat sa iyong suporta, makakakuha ka ng mabilis na pag-download. ❤️ 🚀 Mabilis na pag-download 🐢 Mabagal na pag-download Hiramin mula sa Internet Archive IPFS Gateway #%(num)d (maaaring kailanganin mong subukan nang maraming beses gamit ang IPFS) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction ang kanilang mga ad ay kilalang naglalaman ng malisyosong software, kaya gumamit ng ad blocker o huwag mag-click sa mga ad Amazon's “Send to Kindle” djazz's “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Maaaring hindi maaasahan ang mga file ng Nexus/STC sa pag-download) Walang nahanap na pag-download. Ang lahat ng mga opsyon sa pag-download ay may parehong file, at dapat ligtas gamitin. Gayunpaman, palaging mag-ingat kapag nagda-download ng mga file mula sa internet, lalo na mula sa mga site na panlabas sa Anna’s Archive. Halimbawa, tiyaking palaging updated ang iyong mga device. (walang pag-redirect) Buksan sa aming viewer (buksan sa viewer) Opsyon #%(num)d: %(link)s %(extra)s Hanapin ang orihinal na talaan sa CADAL Maghanap nang manu-mano sa DuXiu Hanapin ang orihinal na rekord sa ISBNdb Hanapin ang orihinal na talaan sa WorldCat Hanapin ang orihinal na talaan sa Open Library Maghanap sa iba't ibang mga database para sa ISBN (para lamang sa mga patron na may kapansanan sa pag-print) PubMed Kakailanganin mo ng ebook o PDF reader upang mabuksan ang file, depende sa format ng file. Inirerekomendang ebook readers: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (maaaring hindi magagamit ang kaugnay na DOI sa Sci-Hub) Maaari mong ipadala ang parehong PDF at EPUB files sa iyong Kindle o Kobo eReader. Inirerekomendang mga kasangkapan: %(links)s Karagdagang impormasyon sa <a %(a_slow)s>FAQ</a>. Suportahan ang mga may-akda at mga aklatan Kung nagustuhan mo ito at kaya mo, isaalang-alang ang pagbili ng orihinal, o direktang suportahan ang mga may-akda. Kung ito ay available sa inyong lokal na aklatan, isaalang-alang na hiramin ito nang libre doon. Pansamantalang hindi magagamit ang mga pag-download ng Partner Server para sa file na ito. torrent Mula sa mga pinagkakatiwalaang kasosyo. Z-Library Z-Library sa Tor (kailangan ang Tor Browser) ipakita ang mga panlabas na pag-download <span class="font-bold">❌ Maaaring may mga isyu ang file na ito, at itinago mula sa isang source library.</span> Minsan ito ay sa kahilingan ng may-ari ng copyright, minsan ito ay dahil may mas magandang alternatibo, ngunit minsan ito ay dahil sa isyu sa file mismo. Maaaring okay pa rin itong i-download, ngunit inirerekomenda namin na maghanap muna ng alternatibong file. Higit pang detalye: Kung nais mo pa ring i-download ang file na ito, tiyaking gumamit lamang ng pinagkakatiwalaan at updated na software upang buksan ito. mga komento sa metadata AA: Maghanap sa Anna’s Archive para sa “%(name)s” Codes Explorer: Tingnan sa Codes Explorer “%(name)s” URL: Website: Kung mayroon ka ng file na ito at hindi pa ito available sa Anna’s Archive, isaalang-alang ang <a %(a_request)s>pag-upload nito</a>. Internet Archive Controlled Digital Lending file “%(id)s” Ito ay talaan ng isang file mula sa Internet Archive, hindi isang direktang mada-download na file. Maaari mong subukang hiramin ang libro (link sa ibaba), o gamitin ang URL na ito kapag <a %(a_request)s>humihiling ng file</a>. Pagbutihin ang metadata CADAL SSNO %(id)s metadata record Ito ay isang metadata record, hindi isang file na maaaring i-download. Maaari mong gamitin ang URL na ito kapag <a %(a_request)s>humihiling ng file</a>. DuXiu SSID %(id)s metadata record ISBNdb %(id)s metadata record MagzDB ID %(id)s tala ng metadata Nexus/STC ID %(id)s tala ng metadata OCLC (WorldCat) number %(id)s metadata record Open Library %(id)s metadata record Sci-Hub file “%(id)s” Hindi natagpuan “%(md5_input)s” ay hindi natagpuan sa aming database. Magdagdag ng komento (%(count)s) Maaari mong makuha ang md5 mula sa URL, hal. MD5 ng mas magandang bersyon ng file na ito (kung naaangkop). Punan ito kung mayroong ibang file na malapit na tumutugma sa file na ito (parehong edisyon, parehong file extension kung makakahanap ka), na dapat gamitin ng mga tao sa halip na file na ito. Kung alam mo ang mas magandang bersyon ng file na ito sa labas ng Anna’s Archive, mangyaring <a %(a_upload)s>i-upload ito</a>. May nangyaring mali. Pakireload ang pahina at subukang muli. Nag-iwan ka ng komento. Maaaring tumagal ng isang minuto bago ito lumabas. Mangyaring gamitin ang <a %(a_copyright)s>DMCA / Form ng pag-angkin ng copyright</a>. Ilarawan ang isyu (kailangan) Kung ang file na ito ay may mahusay na kalidad, maaari mong talakayin ang anumang bagay tungkol dito dito! Kung hindi, pakigamit ang “Iulat ang isyu sa file” na button. Mahusay na kalidad ng file (%(count)s) Kalidad ng file Matutunan kung paano <a %(a_metadata)s>pagbutihin ang metadata</a> para sa file na ito sa iyong sarili. Paglalarawan ng isyu Mangyaring <a %(a_login)s>mag-log in</a>. Gustung-gusto ko ang librong ito! Tumulong sa komunidad sa pamamagitan ng pag-uulat ng kalidad ng file na ito! 🙌 May nangyaring mali. Pakireload ang pahina at subukang muli. Iulat ang isyu ng file (%(count)s) Salamat sa pagsusumite ng iyong ulat. Ipapakita ito sa pahinang ito, at susuriin din nang manu-mano ni Anna (hanggang magkaroon kami ng tamang sistema ng moderasyon). Mag-iwan ng komento Ipasa ang ulat Ano ang mali sa file na ito? Hiramin (%(count)s) Mga komento (%(count)s) Mga Download (%(count)s) Suriin ang metadata (%(count)s) Mga Listahan (%(count)s) Mga Istatistika (%(count)s) Para sa impormasyon tungkol sa partikular na file na ito, tingnan ang kanyang <a %(a_href)s>JSON file</a>. Ito ay isang file na pinamamahalaan ng <a %(a_ia)s>IA’s Controlled Digital Lending</a> library, at na-index ng Anna’s Archive para sa paghahanap. Para sa impormasyon tungkol sa iba't ibang datasets na aming naipon, tingnan ang <a %(a_datasets)s>pahina ng Datasets</a>. Metadata mula sa naka-link na rekord Pagbutihin ang metadata sa Open Library Ang “file MD5” ay isang hash na kinukwenta mula sa nilalaman ng file, at medyo natatangi batay sa nilalaman na iyon. Lahat ng shadow libraries na na-index namin dito ay pangunahing gumagamit ng MD5s upang kilalanin ang mga file. Ang isang file ay maaaring lumitaw sa maraming shadow libraries. Para sa impormasyon tungkol sa iba't ibang datasets na aming naipon, tingnan ang <a %(a_datasets)s>pahina ng Datasets</a>. Iulat ang kalidad ng file Kabuuang downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Czech metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Babala: maraming naka-link na rekord: Kapag tiningnan mo ang isang libro sa Anna’s Archive, makikita mo ang iba't ibang mga field: pamagat, may-akda, publisher, edisyon, taon, paglalarawan, filename, at marami pa. Ang lahat ng mga piraso ng impormasyong iyon ay tinatawag na <em>metadata</em>. Dahil pinagsasama-sama namin ang mga libro mula sa iba't ibang <em>source libraries</em>, ipinapakita namin ang anumang metadata na magagamit sa source library na iyon. Halimbawa, para sa isang librong nakuha namin mula sa Library Genesis, ipapakita namin ang pamagat mula sa database ng Library Genesis. Minsan ang isang libro ay naroroon sa <em>maraming</em> source libraries, na maaaring may iba't ibang metadata fields. Sa kasong iyon, ipinapakita lang namin ang pinakamahabang bersyon ng bawat field, dahil sana ito ang naglalaman ng pinakakapaki-pakinabang na impormasyon! Ipapakita pa rin namin ang iba pang mga field sa ibaba ng paglalarawan, halimbawa bilang "alternative title" (ngunit kung iba lang sila). Kinukuha rin namin ang <em>mga code</em> tulad ng mga identifier at classifier mula sa source library. Ang <em>mga identifier</em> ay natatanging kumakatawan sa isang partikular na edisyon ng isang libro; ang mga halimbawa ay ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. Ang <em>mga classifier</em> ay naggugrupo ng maraming magkatulad na libro; ang mga halimbawa ay Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. Minsan ang mga code na ito ay tahasang naka-link sa mga source libraries, at minsan ay maaari naming kunin ang mga ito mula sa filename o paglalarawan (pangunahing ISBN at DOI). Maaari naming gamitin ang mga identifier upang makahanap ng mga talaan sa <em>metadata-only collections</em>, tulad ng OpenLibrary, ISBNdb, o WorldCat/OCLC. Mayroong isang partikular na <em>metadata tab</em> sa aming search engine kung nais mong mag-browse sa mga koleksyong iyon. Ginagamit namin ang mga tumutugmang talaan upang punan ang mga nawawalang metadata fields (hal. kung nawawala ang pamagat), o hal. bilang "alternative title" (kung may umiiral na pamagat). Upang makita kung saan eksaktong nagmula ang metadata ng isang libro, tingnan ang <em>“Technical details” tab</em> sa isang pahina ng libro. Mayroon itong link sa raw JSON para sa librong iyon, na may mga pointer sa raw JSON ng mga orihinal na talaan. Para sa karagdagang impormasyon, tingnan ang mga sumusunod na pahina: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, at <a %(a_example)s>Example metadata JSON</a>. Sa wakas, ang lahat ng aming metadata ay maaaring <a %(a_generated)s>generated</a> o <a %(a_downloaded)s>downloaded</a> bilang ElasticSearch at MariaDB databases. Background Maaari kang makatulong sa pagpreserba ng mga libro sa pamamagitan ng pagpapabuti ng metadata! Una, basahin ang background tungkol sa metadata sa Anna’s Archive, at pagkatapos ay alamin kung paano pagbutihin ang metadata sa pamamagitan ng pag-link sa Open Library, at kumita ng libreng membership sa Anna’s Archive. Pagbutihin ang metadata Kaya kung makatagpo ka ng file na may masamang metadata, paano mo ito aayusin? Maaari kang pumunta sa source library at sundin ang mga pamamaraan nito para sa pag-aayos ng metadata, ngunit ano ang gagawin kung ang isang file ay naroroon sa maraming source libraries? May isang identifier na espesyal na tinatrato sa Anna’s Archive. <strong>Ang annas_archive md5 field sa Open Library ay laging nangingibabaw sa lahat ng iba pang metadata!</strong> Balikan muna natin at alamin ang tungkol sa Open Library. Ang Open Library ay itinatag noong 2006 ni Aaron Swartz na may layuning "isang web page para sa bawat librong kailanman ay nailathala". Para itong Wikipedia para sa metadata ng libro: maaaring i-edit ito ng lahat, ito ay malayang lisensyado, at maaaring i-download nang maramihan. Ito ay isang database ng libro na pinaka-align sa aming misyon — sa katunayan, ang Anna’s Archive ay inspirasyon mula sa pananaw at buhay ni Aaron Swartz. Sa halip na muling imbentuhin ang gulong, nagpasya kaming idirekta ang aming mga boluntaryo patungo sa Open Library. Kung makakita ka ng libro na may maling metadata, maaari kang makatulong sa sumusunod na paraan: Tandaan na ito ay gumagana lamang para sa mga libro, hindi para sa mga akademikong papel o iba pang uri ng mga file. Para sa iba pang uri ng mga file, inirerekomenda pa rin naming hanapin ang pinagmulan ng library. Maaaring tumagal ng ilang linggo bago maisama ang mga pagbabago sa Anna’s Archive, dahil kailangan naming i-download ang pinakabagong data dump ng Open Library, at muling buuin ang aming search index.  Pumunta sa <a %(a_openlib)s>website ng Open Library</a>. Hanapin ang tamang talaan ng libro. <strong>BABALA:</strong> siguraduhing piliin ang tamang <strong>edisyon</strong>. Sa Open Library, mayroon kang "mga gawa" at "mga edisyon". Ang isang "gawa" ay maaaring "Harry Potter and the Philosopher's Stone". Ang isang "edisyon" ay maaaring: Ang unang edisyon noong 1997 na inilathala ng Bloomsbery na may 256 na pahina. Ang edisyon ng paperback noong 2003 na inilathala ng Raincoast Books na may 223 na pahina. Ang pagsasalin sa Polish noong 2000 na “Harry Potter I Kamie Filozoficzn” ng Media Rodzina na may 328 na pahina. Ang lahat ng mga edisyong iyon ay may iba't ibang ISBN at iba't ibang nilalaman, kaya siguraduhing piliin ang tamang isa! I-edit ang talaan (o lumikha nito kung wala pa), at magdagdag ng mas maraming kapaki-pakinabang na impormasyon hangga't maaari! Nandito ka na rin lang, gawin mo nang kamangha-mangha ang talaan. Sa ilalim ng “ID Numbers” piliin ang “Anna’s Archive” at idagdag ang MD5 ng libro mula sa Anna’s Archive. Ito ang mahabang string ng mga letra at numero pagkatapos ng “/md5/” sa URL. Subukang hanapin ang iba pang mga file sa Anna’s Archive na tumutugma rin sa talaang ito, at idagdag ang mga iyon. Sa hinaharap, maaari nating pangkatin ang mga iyon bilang mga duplicate sa pahina ng paghahanap ng Anna’s Archive. Kapag tapos ka na, isulat ang URL na kakalabas mo lang. Kapag nakapag-update ka na ng hindi bababa sa 30 talaan na may mga MD5 mula sa Anna’s Archive, magpadala sa amin ng <a %(a_contact)s>email</a> at ipadala sa amin ang listahan. Bibigyan ka namin ng libreng membership para sa Anna’s Archive, upang mas madali mong magawa ang trabahong ito (at bilang pasasalamat sa iyong tulong). Kailangang mataas ang kalidad ng mga pag-edit na nagdaragdag ng malaking halaga ng impormasyon, kung hindi ay tatanggihan ang iyong kahilingan. Tatanggihan din ang iyong kahilingan kung ang alinman sa mga pag-edit ay mababaligtad o itatama ng mga moderator ng Open Library. Pag-link sa Open Library Kung ikaw ay magiging malaki ang bahagi sa pag-unlad at operasyon ng aming trabaho, maaari nating pag-usapan ang pagbabahagi ng mas malaking bahagi ng donasyon sa iyo, upang magamit mo ayon sa kinakailangan. Babayaran lang namin ang pagho-host kapag naayos mo na ang lahat at naipakita mong kaya mong panatilihing napapanahon ang archive sa mga update. Ibig sabihin, kailangan mong magbayad para sa unang 1-2 buwan mula sa sarili mong bulsa. Ang inyong oras ay hindi babayaran (at ganoon din ang sa amin), dahil ito ay purong boluntaryong trabaho. Handa kaming sagutin ang mga gastusin sa hosting at VPN, sa simula hanggang $200 kada buwan. Ito ay sapat na para sa isang pangunahing search server at isang proxy na protektado ng DMCA. Mga gastusin sa pagho-host Pakiusap <strong>huwag kaming kontakin</strong> upang humingi ng pahintulot, o para sa mga simpleng tanong. Ang gawa ay mas malakas kaysa salita! Nasa labas na ang lahat ng impormasyon, kaya't ituloy na ang pag-set up ng iyong mirror. Huwag mag-atubiling mag-post ng mga tiket o merge requests sa aming Gitlab kapag nakaranas kayo ng mga isyu. Maaaring kailanganin naming bumuo ng ilang mga tampok na partikular sa mirror kasama ninyo, tulad ng pag-rebrand mula sa “Anna’s Archive” patungo sa pangalan ng inyong website, (paunang) pag-disable ng mga user account, o pag-link pabalik sa aming pangunahing site mula sa mga pahina ng libro. Kapag tumatakbo na ang iyong mirror, mangyaring makipag-ugnayan sa amin. Gusto naming suriin ang iyong OpSec, at kapag maayos na iyon, ililink namin ang iyong mirror, at magsisimula kaming makipagtulungan nang mas malapit sa iyo. Maraming salamat nang maaga sa sinumang handang mag-ambag sa ganitong paraan! Hindi ito para sa mahina ang loob, ngunit ito ay magpapatibay sa pangmatagalang buhay ng pinakamalaking tunay na Open Library sa kasaysayan ng tao. Pagsisimula Upang mapataas ang katatagan ng Anna’s Archive, naghahanap kami ng mga boluntaryo upang magpatakbo ng mga salamin. Ang iyong bersyon ay malinaw na nakikilala bilang isang salamin, halimbawa, “Archive ni Bob, isang salamin ng Archive ni Anna”. Handa kang tanggapin ang mga panganib na kaakibat ng trabahong ito, na hindi biro. May malalim kang pag-unawa sa kinakailangang seguridad sa operasyon. Ang nilalaman ng <a %(a_shadow)s>mga ito</a> <a %(a_pirate)s>mga post</a> ay malinaw sa iyo. Sa simula, hindi namin ibibigay ang access sa mga download ng aming partner server, ngunit kung magiging maayos ang lahat, maaari naming ibahagi iyon sa iyo. Pinapatakbo mo ang open source codebase ng Anna’s Archive, at regular mong ina-update ang parehong code at data. Handa kang mag-ambag sa aming <a %(a_codebase)s>codebase</a> — sa pakikipagtulungan sa aming koponan — upang maisakatuparan ito. Hinahanap namin ito: Mga salamin: panawagan para sa mga boluntaryo Gumawa ng isa pang donasyon. Wala pang donasyon. <a %(a_donate)s>Gawin ang aking unang donasyon.</a> Ang mga detalye ng donasyon ay hindi ipinapakita sa publiko. Aking mga donasyon 📡 Para sa maramihang pag-mirror ng aming koleksyon, tingnan ang mga pahina ng <a %(a_datasets)s>Datasets</a> at <a %(a_torrents)s>Torrents</a>. Mga pag-download mula sa iyong IP address sa nakalipas na 24 oras: %(count)s. 🚀 Para sa mas mabilis na pag-download at upang maiwasan ang mga pagsusuri ng browser, <a %(a_membership)s>maging miyembro</a>. I-download mula sa partner na website Malaya kang magpatuloy sa pag-browse sa Anna’s Archive sa ibang tab habang naghihintay (kung sinusuportahan ng iyong browser ang pag-refresh ng mga background tab). Malaya kang maghintay para sa maramihang mga pahina ng pag-download na mag-load nang sabay-sabay (ngunit mangyaring mag-download lamang ng isang file nang sabay-sabay bawat server). Kapag nakakuha ka ng download link, ito ay valid sa loob ng ilang oras. Salamat sa paghihintay, ito ay nagpapanatili ng website na accessible nang libre para sa lahat! 😊 🔗 Lahat ng download links para sa file na ito: <a %(a_main)s>Pangunahing pahina ng file</a>. ❌ Ang mabagal na pag-download ay hindi available sa pamamagitan ng Cloudflare VPNs o mula sa mga Cloudflare IP address. ❌ Ang mabagal na pag-download ay available lamang sa opisyal na website. Bisitahin ang %(websites)s. 📚 Gamitin ang sumusunod na URL upang mag-download: <a %(a_download)s>I-download ngayon</a>. Upang mabigyan ang lahat ng pagkakataon na mag-download ng mga file nang libre, kailangan mong maghintay bago mo ma-download ang file na ito. Mangyaring maghintay ng <span %(span_countdown)s>%(wait_seconds)s</span> segundo upang ma-download ang file na ito. Babala: maraming pag-download mula sa iyong IP address sa nakalipas na 24 oras. Ang mga pag-download ay maaaring mas mabagal kaysa karaniwan. Kung gumagamit ka ng VPN, shared internet connection, o ang iyong ISP ay nagbabahagi ng mga IP, maaaring dahil dito ang babalang ito. I-save ❌ May nangyaring mali. Pakisubukang muli. ✅ Nai-save. Pakiload muli ang pahina. Palitan ang iyong display name. Ang iyong identifier (ang bahagi pagkatapos ng “#”) ay hindi maaaring palitan. Profile nilikha <span %(span_time)s>%(time)s</span> i-edit Mga Listahan Gumawa ng bagong listahan sa pamamagitan ng paghahanap ng file at pagbukas ng tab na “Mga Listahan”. Wala pang listahan Hindi makita ang profile. Profile Sa kasalukuyan, hindi namin kayang tugunan ang mga book requests. Huwag mag-email sa amin ng inyong mga kahilingan sa libro. Mangyaring mag-request sa mga forum ng Z-Library o Libgen. Rekord sa Arkibo ni Anna DOI: %(doi)s I-download SciDB Nexus/STC Walang preview na magagamit pa. I-download ang file mula sa <a %(a_path)s>Arkibo ni Anna</a>. Upang suportahan ang accessibility at pangmatagalang pag-iingat ng kaalaman ng tao, maging isang <a %(a_donate)s>miyembro</a>. Bilang bonus, 🧬&nbsp;mas mabilis mag-load ang SciDB para sa mga miyembro, nang walang limitasyon. Hindi gumagana? Subukang <a %(a_refresh)s>i-refresh</a>. Sci-Hub Magdagdag ng partikular na field ng paghahanap Maghanap ng mga paglalarawan at komento sa metadata Taon ng publikasyon Advanced Pag-access Nilalaman Ipakita Listahan Talaan Uri ng File Wika Ayusin ayon sa Pinakamalaki Pinaka-nauugnay Pinakabago (laki ng file) (bukas na pinagmulan) (taon ng publikasyon) Pinakamatanda Random Pinakamaliit Pinagmulan kinuha at open-source ng AA Digital Lending (%(count)s) Mga Artikulo sa Journal (%(count)s) Nahanap namin ang mga tugma sa: %(in)s. Maaari mong gamitin ang URL na nahanap doon kapag <a %(a_request)s>humihiling ng file</a>. Metadata (%(count)s) Upang tuklasin ang search index sa pamamagitan ng mga code, gamitin ang <a %(a_href)s>Codes Explorer</a>. Ina-update buwan-buwan ang search index. Sa kasalukuyan, kasama nito ang mga entry hanggang %(last_data_refresh_date)s. Para sa karagdagang teknikal na impormasyon, tingnan ang <a %(link_open_tag)s>pahina ng datasets</a>. Huwag isama Isama lamang Hindi pa nasusuri higit pa… Susunod … Nakaraan Kasama sa search index na ito ang metadata mula sa Controlled Digital Lending library ng Internet Archive. <a %(a_datasets)s>Higit pa tungkol sa aming datasets</a>. Para sa karagdagang digital lending libraries, tingnan ang <a %(a_wikipedia)s>Wikipedia</a> at ang <a %(a_mobileread)s>MobileRead Wiki</a>. Para sa mga DMCA / copyright claims <a %(a_copyright)s>i-click dito</a>. Oras ng pag-download May error sa paghahanap. Subukang <a %(a_reload)s>i-reload ang pahina</a>. Kung magpapatuloy ang problema, mangyaring mag-email sa amin sa %(email)s. Mabilis na pag-download Sa katunayan, kahit sino ay maaaring makatulong na mapanatili ang mga file na ito sa pamamagitan ng pag-seed ng aming <a %(a_torrents)s>pinagsamang listahan ng mga torrent</a>. ➡️ Minsan nangyayari ito nang mali kapag mabagal ang search server. Sa ganitong mga kaso, makakatulong ang <a %(a_attrs)s>pag-reload</a>. ❌ Maaaring may mga isyu ang file na ito. Naghahanap ng mga papel? Kasama sa search index na ito ang metadata mula sa iba't ibang mga pinagmumulan ng metadata. <a %(a_datasets)s>Higit pa tungkol sa aming datasets</a>. Maraming, maraming mga pinagmumulan ng metadata para sa mga nakasulat na gawa sa buong mundo. <a %(a_wikipedia)s>Ang pahinang ito sa Wikipedia</a> ay isang magandang simula, ngunit kung alam mo ang iba pang magagandang listahan, mangyaring ipaalam sa amin. Para sa metadata, ipinapakita namin ang orihinal na mga talaan. Hindi kami nagsasagawa ng anumang pagsasama ng mga talaan. Sa kasalukuyan, mayroon kaming pinaka-komprehensibong bukas na katalogo ng mga libro, papel, at iba pang mga nakasulat na gawa sa buong mundo. Kami ay nagmi-mirror ng Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>at marami pa</a>. <span class="font-bold">Walang nahanap na mga file.</span> Subukang gumamit ng mas kaunti o ibang mga termino at filter sa paghahanap. Mga Resulta %(from)s-%(to)s (%(total)s kabuuan) Kung makakakita ka ng iba pang “shadow libraries” na dapat naming i-mirror, o kung mayroon kang anumang mga katanungan, mangyaring makipag-ugnayan sa amin sa %(email)s. %(num)d mga bahagyang tugma %(num)d+ mga bahagyang tugma Mag-type sa kahon upang maghanap ng mga file sa mga digital lending libraries. Mag-type sa kahon upang maghanap sa aming katalogo ng %(count)s direktang mada-download na mga file, na aming <a %(a_preserve)s>pinapanatili magpakailanman</a>. Mag-type sa kahon upang maghanap. Mag-type sa kahon upang maghanap sa aming katalogo ng %(count)s akademikong papel at journal articles, na aming <a %(a_preserve)s>pinapanatili magpakailanman</a>. Mag-type sa kahon upang maghanap ng metadata mula sa mga aklatan. Ito ay maaaring maging kapaki-pakinabang kapag <a %(a_request)s>humihiling ng isang file</a>. Tip: gamitin ang mga keyboard shortcut na “/” (search focus), “enter” (search), “j” (up), “k” (down), “<” (prev page), “>” (next page) para sa mas mabilis na pag-navigate. Ito ay mga tala ng metadata, <span %(classname)s>hindi</span> nada-download na mga file. Mga setting ng paghahanap Maghanap Digital Lending I-download Mga artikulo sa journal Metadata Bagong paghahanap %(search_input)s - Maghanap Masyadong matagal ang paghahanap, na nangangahulugang maaaring hindi tumpak ang mga resulta. Minsan nakakatulong ang <a %(a_reload)s>pag-reload</a> ng pahina. Masyadong matagal ang paghahanap, na karaniwan para sa malawak na mga query. Maaaring hindi tumpak ang bilang ng mga filter. Para sa malalaking uploads (higit sa 10,000 files) na hindi tinatanggap ng Libgen o Z-Library, mangyaring makipag-ugnayan sa amin sa %(a_email)s. Para sa Libgen.li, siguraduhing mag-login muna sa <a %(a_forum)s>kanilang forum</a> gamit ang username na %(username)s at password na %(password)s, at pagkatapos ay bumalik sa kanilang <a %(a_upload_page)s>pahina ng pag-upload</a>. Sa ngayon, inirerekomenda naming mag-upload ng mga bagong libro sa mga Library Genesis forks. Narito ang isang <a %(a_guide)s>handy guide</a>. Tandaan na ang parehong forks na ini-index namin sa website na ito ay kumukuha mula sa parehong sistema ng pag-upload. Para sa maliliit na pag-upload (hanggang 10,000 na file) mangyaring i-upload ang mga ito sa parehong %(first)s at %(second)s. Bilang alternatibo, maaari mo ring i-upload ang mga ito sa Z-Library <a %(a_upload)s>dito</a>. Para sa pag-upload ng mga academic papers, mangyaring (bukod sa Library Genesis) mag-upload din sa <a %(a_stc_nexus)s>STC Nexus</a>. Sila ang pinakamahusay na shadow library para sa mga bagong papers. Hindi pa namin sila na-integrate, ngunit gagawin namin sa hinaharap. Maaari mong gamitin ang kanilang <a %(a_telegram)s>upload bot sa Telegram</a>, o makipag-ugnayan sa address na nakalista sa kanilang pinned message kung mayroon kang masyadong maraming files na i-upload sa ganitong paraan. <span %(label)s>Mabigat na boluntaryong trabaho (USD$50-USD$5,000 gantimpala):</span> kung kaya mong maglaan ng maraming oras at/o mga mapagkukunan sa aming misyon, nais naming makipagtulungan sa iyo nang mas malapit. Sa kalaunan, maaari kang sumali sa aming pangunahing koponan. Bagaman mahigpit ang aming badyet, kaya naming magbigay ng <span %(bold)s>💰 gantimpalang pinansyal</span> para sa pinakamabigat na trabaho. <span %(label)s>Magaan na gawaing pagboboluntaryo:</span> kung maaari ka lamang maglaan ng ilang oras dito at doon, marami pa ring paraan na maaari kang tumulong. Ginagantimpalaan namin ang mga pare-parehong boluntaryo ng <span %(bold)s>🤝 na mga membership sa Anna's Archive</span>. Ang Anna's Archive ay umaasa sa mga boluntaryong katulad mo. Tinatanggap namin ang lahat ng antas ng pangako, at may dalawang pangunahing kategorya ng tulong na hinahanap namin: Kung hindi mo kayang magboluntaryo ng iyong oras, maaari ka pa ring makatulong nang malaki sa pamamagitan ng <a %(a_donate)s>pag-donate ng pera</a>, <a %(a_torrents)s>pag-seed ng aming mga torrent</a>, <a %(a_uploading)s>pag-upload ng mga libro</a>, o <a %(a_help)s>pagpapakilala sa iyong mga kaibigan tungkol sa Anna’s Archive</a>. <span %(bold)s>Mga Kumpanya:</span> nag-aalok kami ng high-speed direct access sa aming mga koleksyon kapalit ng enterprise-level na donasyon o kapalit ng mga bagong koleksyon (hal. bagong scan, OCR’ed datasets, pagpapayaman ng aming data). <a %(a_contact)s>Makipag-ugnayan sa amin</a> kung ikaw ito. Tingnan din ang aming <a %(a_llm)s>LLM page</a>. Mga Pabuya Palagi kaming naghahanap ng mga tao na may solidong kasanayan sa programming o offensive security upang makilahok. Maaari kang magbigay ng malaking kontribusyon sa pagpapanatili ng pamana ng sangkatauhan. Bilang pasasalamat, nagbibigay kami ng membership para sa solidong kontribusyon. Bilang malaking pasasalamat, nagbibigay kami ng mga pabuya sa pera para sa mga partikular na mahalaga at mahihirap na gawain. Hindi ito dapat ituring na kapalit ng trabaho, ngunit ito ay isang karagdagang insentibo at maaaring makatulong sa mga nagastos. Karamihan sa aming code ay open source, at hihilingin din namin na ang iyong code ay maging ganoon kapag nagbibigay ng pabuya. May ilang mga eksepsiyon na maaari naming talakayin sa indibidwal na batayan. Ang mga pabuya ay ibinibigay sa unang taong makakakumpleto ng isang gawain. Huwag mag-atubiling magkomento sa isang bounty ticket upang ipaalam sa iba na ikaw ay nagtatrabaho sa isang bagay, upang ang iba ay maghintay o makipag-ugnayan sa iyo upang makipagtulungan. Ngunit tandaan na ang iba ay malayang magtrabaho rin dito at subukang maunahan ka. Gayunpaman, hindi kami nagbibigay ng pabuya para sa magulong trabaho. Kung may dalawang mataas na kalidad na pagsusumite na ginawa nang magkalapit (sa loob ng isang araw o dalawa), maaari naming piliing magbigay ng pabuya sa pareho, ayon sa aming pagpapasya, halimbawa 100%% para sa unang pagsusumite at 50%% para sa pangalawang pagsusumite (kaya 150%% kabuuan). Para sa mas malalaking pabuya (lalo na ang mga scraping bounties), mangyaring makipag-ugnayan sa amin kapag nakumpleto mo na ang ~5%% nito, at kumpiyansa kang ang iyong pamamaraan ay mag-scale sa buong milestone. Kailangan mong ibahagi ang iyong pamamaraan sa amin upang makapagbigay kami ng feedback. Gayundin, sa ganitong paraan maaari naming magpasya kung ano ang gagawin kung mayroong maraming tao na malapit sa isang pabuya, tulad ng potensyal na pagbibigay nito sa maraming tao, paghikayat sa mga tao na makipagtulungan, atbp. BABALA: ang mga mataas na pabuya na gawain ay <span %(bold)s>mahirap</span> — maaaring mas mabuting magsimula sa mas madadaling gawain. Pumunta sa aming <a %(a_gitlab)s>listahan ng mga isyu sa Gitlab</a> at ayusin ayon sa “Label priority”. Ipinapakita nito ang pagkakasunud-sunod ng mga gawain na mahalaga sa amin. Ang mga gawain na walang tiyak na pabuya ay eligible pa rin para sa membership, lalo na ang mga may markang “Accepted” at “Paborito ni Anna”. Maaaring gusto mong magsimula sa isang “Starter project”. Magaan na boluntaryong trabaho Ngayon ay mayroon na rin kaming naka-sync na Matrix channel sa %(matrix)s. Kung may ilang oras kang bakante, maaari kang tumulong sa iba't ibang paraan. Siguraduhing sumali sa <a %(a_telegram)s>volunteers chat sa Telegram</a>. Bilang tanda ng aming pasasalamat, karaniwan kaming nagbibigay ng 6 na buwan ng “Maswerteng Librarian” para sa mga pangunahing milestone, at higit pa para sa patuloy na boluntaryong trabaho. Lahat ng milestone ay nangangailangan ng mataas na kalidad na trabaho — ang pabaya na trabaho ay mas makakasama kaysa makakatulong at tatanggihan namin ito. Mangyaring <a %(a_contact)s>i-email kami</a> kapag naabot mo na ang isang milestone. %(links)s mga link o screenshot ng mga kahilingang iyong natupad. Pagtupad ng mga kahilingan ng libro (o papel, atbp) sa Z-Library o sa Library Genesis forums. Wala kaming sariling sistema ng kahilingan ng libro, ngunit ini-mirror namin ang mga aklatan na iyon, kaya ang pagpapabuti sa kanila ay nagpapabuti rin sa Anna’s Archive. Milestone Gawain Depende sa gawain. Maliit na mga gawain na ipinost sa aming <a %(a_telegram)s>volunteers chat sa Telegram</a>. Karaniwan para sa membership, minsan para sa maliliit na gantimpala. Maliit na mga gawain na ipinapaskil sa aming volunteer chat group. Tiyaking mag-iwan ng komento sa mga isyung iyong inaayos, upang hindi maulit ng iba ang iyong trabaho. %(links)s mga link ng mga rekord na iyong pinahusay. Maaari mong gamitin ang <a %(a_list)s>listahan ng mga random na isyu sa metadata</a> bilang panimulang punto. Pagbutihin ang metadata sa pamamagitan ng <a %(a_metadata)s>pag-link</a> sa Open Library. Dapat ipakita ng mga ito na ipinaalam mo sa isang tao ang tungkol sa Arkibo ni Anna, at sila ay nagpapasalamat sa iyo. %(links)s mga link o screenshot. Pagpapalaganap ng balita tungkol sa Arkibo ni Anna. Halimbawa, sa pamamagitan ng pagrerekomenda ng mga libro sa AA, pag-link sa aming mga blog post, o sa pangkalahatan ay pagdidirekta ng mga tao sa aming website. Kumpletuhin ang pagsasalin ng isang wika (kung hindi pa ito malapit sa pagkakumpleto). <a %(a_translate)s>Pagsasalin</a> ng website. Link sa edit history na nagpapakita na gumawa ka ng makabuluhang kontribusyon. Pagbutihin ang Wikipedia page para sa Anna’s Archive sa iyong wika. Isama ang impormasyon mula sa Wikipedia page ng AA sa ibang mga wika, at mula sa aming website at blog. Magdagdag ng mga sanggunian sa AA sa iba pang kaugnay na mga pahina. Volunteering & Bounties 