��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b r  sd M  �f -   4h   bh �  �i �  qk s  Tm D  �n \   
p v   jp G   �p n   )q �  �q p  Is �   �t �  �u    �w !  �x �  �z ^  �| �      � �   � G   �� �   � ]   ��   [�    t� J  �� G   ݉ -   %�    S�    b� A   }� &   ��    � 7   ��    1� -   P�    ~� G   �� T  ܋   1� Q   E� 6   ��    Ύ    ݎ    �    �    �    :� 	   G�    Q�    b�     i�    ��    �� 	   �� 
   ��    Ǐ    ӏ    �    
� �  &� b  �� �   � -   �� L  Ӕ �    �   ϖ �   ߗ    �� �   ֘    |� �   �� "   ^� ?  ��    �� �  ߛ ?   m� n   ��    � S  )� c  }� �  � 2  Ѣ    � J   � R   e� �   �� /   o� )   �� o   ɥ 6   9� @   p� +   ��    ݦ �   � �  h� =   G� �   �� �   � A   � +  0� +   \� -  �� �   �� �   K� K   � �   R� �   � G   �� �   � �   � g   ޲ H   F� �   ��    � �   "� .  Ŵ G   ��    <� �   H� �   �    �� �  �� �   ~�    -� �  .� �   �� �  �� %  � O   C� R   �� b   �� �   I� v   �� X   M� 5   �� �   �� Y   }� J   ��    "� y   7� T   �� �   � �   �� "   l�   ��   �� P  �� h  �� �   b� �   �� =  �� �   +� �   �� �   �� t  D� �   ��   d�    v� �   �� �   4� �   (� x  �� �   e� �   �� �   �� �  3� }   %� H  �� M  ��    :� b   Y� �   �� F   `� �  �� �   5� s   �� �   T� -   �� O  � �   _� �  <�    �� a   �� �   ;� �   �� 	  �� �   �� �  �� �  �� 6   ;� ~  r� �  �� I  �� �  �� >  �� "  �� t   � �   �� 	   1� �   ;� I   � �  J  -  � �   # �    
   � �  � ^  t �   � 
   � �   � I  �	 �   �
 T  � �    V   �    O �  g �      � �   �   � c  � �   & z   h   � V   �   J 3   ^ 
   � K  � 
  � 6  � 4   1 G   f   � �   �! }   P" e   �" �  4# !   �$    �$ �  % �   �& �   m' S    ( �   T( ,   �( 
  ) 
  *    '+    7+ ,   C+ <   p+ =   �+ :   �+ Y   &, x   �,    �, .   - A   >- S   �- *   �- :   �- 6   :. }   q.    �.    �. 9  / L  H0 �   �1   �2 �   �3 �   .4 C  �4    36   L6 �   P7 �   �7 G  �8 �  �9 
  }; �  �= �  2? G   �@ H   A )   cA �   �A A  =B �  D >  3F ]  rG /  �H    J   L $   :M W  _M ,  �N y  �O    ^Q �   oQ _  hR �   �S q  YT �   �U    �V 3   �V q   �V 
   jW �  uW �  %Z �  �[ �   Z^ �   0_    �_ �   ` �   �` G   �a s   9b '  �b   �c �   �d �  �e g   Kg )  �g    �h �  �h �   �j   �k �  �m ,  Fp �   sr    :s   Fs �  Wt    .v _  Dv   �w 3  �y %  �{   ~ G  6 G   ~�   ƀ �   Ղ �   �� G   @� m   ��    �� @   � #   D� .   h� +   ��    Å �   ܅ �  �� �  ^� )  Y� �   �� 	   n� �  x� B  s� �  �� �  I� �  ֖ z  k�    �   � 	   �    � �  %� T  ��   � |  $�    ��   �� �  �� �  �� �  q� 
  o�   z� u  �� %   � �   6� m   � �  ]� �  G� �   �� �   �� �   M� o  �    r� �   �� J   6� S   ��    վ :   �� :   /� _  j� �  �� �  �� 5   <� �  r� �   K�    �� �   � D   �� �   � �   �� .   ^� c   ��    �� d  	� S  n� �  �� �  r� �   �    �� d   ��   
�    +� �   2� �  �� �   �� 0   8� 5   i� �  �� -   "� �  P� �  �� �  �� )   r� z   �� �   � e  �� �  �    �� �   �� /   �� �    � �   ��   �� �   �� [   �� -   ��   '� 5   C� �  y� M  � �   g� �   M� b   H� F   �� �   �� %  �� �  �� �  ]� �  J� :   H�   �� /  �� K  �� �    �  � �  � -   z �  � ,   � �  � S   �	    �	 2  
 #  E �  i   ' D  : �   -  } �   � �  ] �  X �    A  � i  � A   S! �   �! �   &"    �"    �" N  �" �   8$ 3   �$ #  $% �  H& <  ;( �  x) �  b+ �   /- �  �- D  �/    �0 �   �0    �1 �  �1 !   w3    �3    �3    �3 ,   �3    �3    4    !4    >4    E4 	   ]4    g4 	   n4    x4    �4 	   �4 (   �4    �4    �4    �4    �4 �   �4 ]   �5 %   6 
   B6 3   P6 ,   �6 5   �6 /   �6 #   7 
   ;7    F7    M7    ^7    r7    �7    �7    �7    �7    �7 2   �7    8    8     78 #   X8 0   |8 ,   �8    �8 $   �8 :   9 !   M9 P   o9 G   �9    : W   
: I   e:    �:    �: !   �:    �:    ; #   6;    Z;    p;    �;    �;    �;    �; 
   �; 	   �; 
   �;    �; "   �;    <    < 	   $<    .< 	   F<    P<    g<    m< 	   t<    ~<    �<    �<    �< #   �<    �<    �<    = 	   =    = #   *=    N=    U= '   [=    �=    �=    �=    �=    �=    �=    �= g   �= �   [> _   �> �   M? �  @ }   �A    cB )   vB    �B    �B    �B    �B    �B '   �B J   C 4   `C L   �C #   �C >   D /   ED �   uD d   E �   iE X   IF @   �F &   �F 
   
G    G 	   G    )G    2G    FG    ^G    cG    zG    �G    �G    �G    �G    �G    �G    �G    �G    �G    H 	   H    H    H    ,H    GH   \H    {I    I    �I ,   �I    �I [   �I b   J %   J 6   �J B   �J    K    'K    /K s   2K    �K    �K *   �K }   �K    hL    �L ~   �L $   M .  <M Y   kP |   �P �   BQ �   �Q A   �R �   �R �   �S Q  �T   �U    �V    �V B   W Z   KW T   �W ^   �W V   ZX J   �X a   �X !   ^Y 2   �Y    �Y 
   �Y I   �Y %   Z    9Z    @Z    RZ    bZ _   yZ 
   �Z 0   �Z u   [    �[     �[ J   �[ K   \   a\    �]    �] �   ^    �^    �^    �^    _    "_ 2   ._ U  a_    �` 
   �`    �` 
   �` T  a *   Yb    �b    �b w   �b    c 	   c 3   c    Sc    pc +   yc �   �c    @d    Qd I   cd #   �d    �d 	   �d    �d (   �d o   %e    �e 0   �e �   �e h   �f Z   g    `g   tg N   �h    �h 2   �h    i �   9i �   �i    �j I   �j �    k �   �k     xl     �l    �l �  �l 6   yn '   �n    �n +   �n    "o �   Bo     �o    p 7   #p C   [p    �p    �p #   �p )   �p ]  q �  qs E  �t ?   <w 6   |w    �w $   �w m  �w �   Sy �   Oz    �z �   { �  | "   �}     �}   ~ T  +� 	   ��    �� 8   ��    ց L  ߁    ,� i  L� �  �� �   m�    ^� �   y� C   � )   H� p   r� 9  � ,  � �   J� �  � y   ��    � k   � �   o� �   =� K   � �   N� /   �� *   .�    Y� 	   l�    v� *   �� #   �� C   ؒ 6   � 	   S� 4   ]� ;  �� ;   Δ    
� �   � �   �� *   (� !   S�    u�    �� 4   �� $   � -   � &   4� �   [� )   V� �   ��    B� �   [� �   %� �   �� A  ,� �   n� -   =� �   k� 	   �� V  � ~   _�    ޠ �  ��    ʢ    ע    � #   � *   (�    S�    [� c   b� �   ƣ �   u�    u�    ��    �� �   �� C  k� �   �� �   q�     �    �    ,�    A�    [�    q�    �� K   �� 1   ک �  � m   �    r� v   �� |   �� Y   y� w   ӭ a   K� Z   ��    � h   � P   z� �   ˯ _   j� T   ʰ    � �   6� j   &� G   �� d   ٲ g   >� B   �� 	   � ?   � u   3� �   �� 9   _� �   ��    K� 2  R� F   �� g   ̷ �   4�    ޸   � �   ��    ʻ    �    �    � )  � 0   <� e   m� �   ӽ �   �� �   �� �   ,� �   � �   �� �   r� ]   ,� (  �� h   �� }  � �   ��   v� 
   �� 
   �� 
   �� �   �� 
   ]� 
   k� M   y� j   �� �   2� 
   �   -� >   0� �   o� E   �� �   B� e   �� 
   +� �   9� 
   7� 
   E� 
   S� @  a� �   �� �  .�    �    7� 
   =� �   K�    
� 1   (�   Z� �   q� %   '�    M�    ]� ?   }� B   �� 7    � &   8� &   _�   �� !   �� �  ��   a� �   t� [   � �   l�   $�   1� i  5� �   �� �   i� �   ��    �� u  �� %   %� y  K� �   �� 4  �� �   �� ]  �� �   �� �   �� �   L� 3   �� :   �    >� 8   W�    ��    ��    �� �   �� 	   �� v   �� /   =�    m�    �    �� $   �� {   �� U   4� C   ��   �� %   ��    �    �    � !   0�    R�    e�    n�    t�    }�    ��    ��    �� +   �� �   ��    o�    �� 	   ��    ��    ��    ��    ��    ��    ��    �� )   �� s   $�    �� 2   �� [   �� �   <� �   �� �   �� 1  |� �   ��    }�    �� �   �� 3   B� S   v� G   �� �   � �   �� f   �� e         �  _   �  j   �  }   a    � 
   �    �        0     A    b #   j    �    �    � $   �    �        .    F    M    ]    j    r    � %   � ,   � {   � �   f (   � %    k   > f   � {    +   � )   � 1   � 9    ?   O 0   � �   �   � *  �	    �
 <   �
 �   . '   � �   � �   � x   �
 ;   / "   k �   � �   & x   � 3   Y x   � �       � (   �    � ;    t   H m   �    +    3    < )   C �   m 9    $   ? <   d %   � %   � "   � T       e j   � A   �   / L   > [   � �   � =   �         "     @    a     �    �     � =   � >   ! c  ` F   � 7    I   C .   � 
   � q   � �   9 �   � �   �    3  �   <  =   �     /! �   K!    )" %   <" D   b" >   �" m   �" Y   T# W   �#    $ !   %$ �   G$ 3   �$ *   )% W   T%   �% D   �& �   �& �   �' ]   3( ~   �( .   ) @   ?) �   �)    * i   6* !   �* �   �* 2   �+ 8   �+    �+ -   , �   5, P   '- Y   x- �   �- ^   ]. T   �. �   / �   �/    P0    Y0 B   r0 C   �0    �0 #   1    71    G1    ]1 -   l1 �   �1 d   +2 "   �2 !   �2 *   �2 +    3 $   ,3 r   Q3 <   �3 �   4 Z   �4 (   �4 �   	5 �   �5 .   �6 Z   �6    7 S   (7 ?   |7     �7 s   �7 ~   Q8 +   �8 N   �8    K9 .   `9 V   �9    �9 {    : -   |:    �: 2   �: e   �: �   ];    H<    _<    v< F   �<    �< \   �< @   H= �   �= �   0>    �> F   �> �   ?     �? �   �? H   �@ *   �@ V   A    nA    oB    wB    yB    {B .   �B 9   �B E   �B    >C    XC 	   iC #   sC @   �C @   �C 
   D O   'D O   wD    �D     �D 7   �D     2E    SE Q   hE �   �E G   �F    �F    	G �   G i  �G _   4I    �I c  �I �  
K ;   �L �   �L #   `M R  �M ,   �O d   P    iP    P �  �P    ]R i   tR �   �R �   mS f   �S     YT d   zT 7   �T    U {   0U B   �U 2   �U Y   "V -   |V Y   �V �   W �   �W 0   ^X �   �X �  ZY �   �Z 1   �[ z  �[    K] �   L^ 0  5_ �   f` /   -a    ]a �   xa :   3b -  nb i   �d O   e    Ve     ge �  �e    Pg �   Ug e  Bh :  �i F  �j F   *l W   ql [   �l /   %m 5   Um R   �m e   �m    Dn    Tn 9   pn *   �n    �n 3   �n m   'o *   �o    �o g   �o �   /p �   q    �q    �q    �q M   �q k   Mr c   �r �   s t   �s    `t $   ~t �   �t 
   �u �   �u �  @v 	  �x P   �y 0   ?z    pz    vz    |z E   �z 1   �z �   �z �   }{ T   ||    �|    �| %   �|    } k   7}    �} <   �}    �} 3   �} +   +~    W~    p~ Y   w~    �~    �~ .   �~         < v   @ �   � Y   �� h   � N   U� �   ��    u�    �� �   �� �   Y� �   &� 	   �� r   �� Q   r� >   ą V   � k   Z� `   Ɔ    '� g   E�    ��    ��    ؇    �    �    "�    8� &   L�    s�    �� 3   �� 3   �� 3   �� $   )� :   N� 2   �� "   ��    ߉    �� M   �     a� 
   �� =   �� 3   ˊ [   �� -   [�    ��    �� 5   �� ,   �    !� O   4� .   �� `   �� �   �    �    �� "   �    4� 7   S� 	   ��    ��    �� �   ��    @� #   `�    �� 
   �� 	   �� ?   �� !   ��   �    �    ,�    C� $   T� $   y�    �� %   �� '   �� +   � &   4� 2   [�    �� a   �� *   ��    "�    =� 8   N� <   �� $   ē @   � !   *� k   L� T   �� Z   
�    h�    p� 	   �� 
   ��    ��    �� �  ҕ o   Y�    ɗ    � '   �    � /   &�    V�    [� u   h� M   ޘ �   ,�    � '   $� �   L� '   Ԛ )   �� )   &� ,   P� ;   }� /   ��    � 
   � =   �    R� -   q� 6   �� A  ֜ C   � 8   \� `   ��     �� �   � $   ��    ؟ V   �    C� -   ]�    �� A   �� C   � )   +� �   U�    �    � "   �    3�    I�    a�    w�    ��    �� _   â   #�     0� !   Q� �   s� �   q�    (�    E�    c�    v�    ��    ��    ��    ˦    �    � 
   �    � 
   $�    2�    A�    S�    `�    l� -   |� �   �� �   �� �  |� (  	� �  2�   � �  �    �� #  ��    ��    ˲ �   ̳ �  �� �   ^� �  � 6   �� �   ͸ <   ��    ¹ F   ޹ O   %� h   u� u   ޺ �   T� �   � �   �� q  ��    � �   $� �   �� b   �� �   C�    � �   � �  �� �   �� �   f� 	   U� e   _� z   �� �   @� �   2� �   �� �   A�    ��    ��    � M   � 9   i�    �� �   �� P   N� v   �� (   � �   ?� �   �� J   �� ^   �� F   7� |   ~� f   �� *   b� �   �� h   � �   x�    �    �� 3   �� )   �� ]   �� 1   H�    z�    �� L   ��    ��    ��    � 4   � 6   A� E   x�    ��    �� 	   ��    �� 	   �� b   � �   g� X   �� 8   D�    }� &   �� .   ��    ��    ��    �� 	    �    
�    �    �    �    .�    5�    A�    M� 
   \�    j�    ��    ��    �� 	   �� 
   ��    �� %   ��    �� %   � ~   ?�    �� h   �� �   =�    �    � 
   �    &�    .�    7�    ;� �   B� �   �� S   y�    ��    �� w   ��    t� �   �� �   � '   ��    �� ~   �� �   R� W   � �   u� u   :� ,   �� |   ��    Z�     z� N   �� �   �� !   �� �   �� �   G� �   �� V   \�    ��    ��    �� 	   ��    �� 	   �    �    )� �   C� z   �� �   R� �   �� �   �� e   �� H   � �  N� z  �� 
  r� �   ��   � v  .�    �� �   �� >  �� �   �� �  z� �  ?� �   ,� ~  ��    4� E   H� �   �� j  � F   �� �   ��    ��    ��    �� �   �� D   �� l   �� -   6� a   d� G   �� _     )   n  �   �  H   U -   � U   � �   "      blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: es
Language-Team: es <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library es una biblioteca popular (e ilegal). Han tomado la colección de Library Genesis y la han hecho fácilmente buscable. Además de eso, se han vuelto muy efectivos en solicitar nuevas contribuciones de libros, incentivando a los usuarios contribuyentes con varios beneficios. Actualmente no contribuyen con estos nuevos libros de vuelta a Library Genesis. Y a diferencia de Library Genesis, no hacen que su colección sea fácilmente espejable, lo que impide una amplia preservación. Esto es importante para su modelo de negocio, ya que cobran dinero por acceder a su colección en masa (más de 10 libros por día). No hacemos juicios morales sobre cobrar dinero por el acceso masivo a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada. - Anna y el equipo (<a %(reddit)s>Reddit</a>) En el lanzamiento original del Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>), hicimos un espejo de Z-Library, una gran colección de libros ilegales. Como recordatorio, esto es lo que escribimos en esa publicación original del blog: Esa colección data de mediados de 2021. Mientras tanto, Z-Library ha estado creciendo a un ritmo asombroso: han añadido alrededor de 3.8 millones de nuevos libros. Hay algunos duplicados, claro, pero la mayoría parecen ser libros realmente nuevos o escaneos de mayor calidad de libros previamente enviados. Esto se debe en gran parte al aumento del número de moderadores voluntarios en Z-Library y a su sistema de carga masiva con deduplicación. Nos gustaría felicitarlos por estos logros. Nos complace anunciar que hemos obtenido todos los libros que se añadieron a Z-Library entre nuestro último espejo y agosto de 2022. También hemos vuelto atrás y recopilado algunos libros que nos perdimos la primera vez. En total, esta nueva colección es de aproximadamente 24TB, que es mucho más grande que la anterior (7TB). Nuestro espejo ahora tiene un total de 31TB. Nuevamente, deduplicamos contra Library Genesis, ya que ya hay torrents disponibles para esa colección. Por favor, visite el Espejo de la Biblioteca Pirata para ver la nueva colección (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Allí hay más información sobre cómo están estructurados los archivos y qué ha cambiado desde la última vez. No enlazaremos desde aquí, ya que este es solo un sitio web de blog que no aloja ningún material ilegal. Por supuesto, sembrar también es una excelente manera de ayudarnos. Gracias a todos los que están sembrando nuestro conjunto anterior de torrents. Estamos agradecidos por la respuesta positiva y felices de que haya tantas personas que se preocupan por la preservación del conocimiento y la cultura de esta manera inusual. 3x nuevos libros añadidos al Espejo de la Biblioteca Pirata (+24TB, 3.8 millones de libros) Lea los artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundo</a> - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundo</a> No hace mucho, las “bibliotecas fantasma” estaban desapareciendo. Sci-Hub, el enorme archivo ilegal de artículos académicos, había dejado de aceptar nuevas obras debido a demanda judiciales. “Z-Library” la mayor biblioteca ilegal de libros, vio a sus supuestos creadores arrestados por cargos de violación de derechos de autor. Increíblemente lograron escapar de su arresto, pero su biblioteca no está menos amenazada. Algunos países ya están haciendo una versión de esto. TorrentFreak <a %(torrentfreak)s>informó</a> que China y Japón han introducido excepciones de IA en sus leyes de copyright. No está claro para nosotros cómo esto interactúa con los tratados internacionales, pero ciertamente da cobertura a sus empresas nacionales, lo que explica lo que hemos estado viendo. En cuanto al Archivo de Anna, continuaremos nuestro trabajo subterráneo arraigado en la convicción moral. Sin embargo, nuestro mayor deseo es salir a la luz y amplificar nuestro impacto legalmente. Por favor, reformen el copyright. Cuando Z-Library enfrentó su cierre, ya había respaldado toda su biblioteca y estaba buscando una plataforma para alojarla. Esa fue mi motivación para iniciar el Archivo de Anna: una continuación de la misión detrás de esas iniciativas anteriores. Desde entonces, hemos crecido hasta convertirnos en la biblioteca fantasma más grande del mundo, albergando más de 140 millones de textos con derechos de autor en numerosos formatos: libros, artículos académicos, revistas, periódicos y más. Mi equipo y yo somos ideólogos. Creemos que preservar y alojar estos archivos es moralmente correcto. Las bibliotecas de todo el mundo están sufriendo recortes de financiación, y tampoco podemos confiar el patrimonio de la humanidad a las corporaciones. Luego llegó la IA. Prácticamente todas las grandes empresas que desarrollan LLMs nos contactaron para entrenar con nuestros datos. La mayoría (¡pero no todas!) de las empresas con sede en EE. UU. reconsideraron una vez que se dieron cuenta de la naturaleza ilegal de nuestro trabajo. En cambio, las empresas chinas han acogido con entusiasmo nuestra colección, aparentemente sin preocuparse por su legalidad. Esto es notable dado el papel de China como signatario de casi todos los principales tratados internacionales de derechos de autor. Hemos dado acceso de alta velocidad a unas 30 empresas. La mayoría de ellas son empresas de LLM, y algunas son corredores de datos, quienes revenderán nuestra colección. La mayoría son chinas, aunque también hemos trabajado con empresas de EE. UU., Europa, Rusia, Corea del Sur y Japón. DeepSeek <a %(arxiv)s>admitió</a> que una versión anterior fue entrenada con parte de nuestra colección, aunque son muy reservados sobre su último modelo (probablemente también entrenado con nuestros datos). Si Occidente quiere mantenerse a la vanguardia en la carrera de los LLMs y, en última instancia, de la AGI, necesita reconsiderar su posición sobre los derechos de autor, y pronto. Ya sea que esté de acuerdo con nosotros o no en nuestro argumento moral, esto se está convirtiendo ahora en un caso de economía, e incluso de seguridad nacional. Todos los bloques de poder están construyendo supercientíficos artificiales, superhackers y supermilitares. La libertad de información se está convirtiendo en una cuestión de supervivencia para estos países, incluso una cuestión de seguridad nacional. Nuestro equipo proviene de todo el mundo, y no tenemos una alineación particular. Pero animaríamos a los países con leyes de derechos de autor estrictas a usar esta amenaza existencial para reformarlas. Entonces, ¿qué hacer? Nuestra primera recomendación es sencilla: acortar el plazo del copyright. En EE. UU., el copyright se otorga por 70 años después de la muerte del autor. Esto es absurdo. Podemos alinearlo con las patentes, que se otorgan por 20 años después de la presentación. Esto debería ser más que suficiente tiempo para que los autores de libros, artículos, música, arte y otras obras creativas sean completamente compensados por sus esfuerzos (incluidos proyectos a largo plazo como adaptaciones cinematográficas). Luego, como mínimo, los legisladores deberían incluir excepciones para la preservación masiva y la difusión de textos. Si la pérdida de ingresos de clientes individuales es la principal preocupación, la distribución a nivel personal podría seguir prohibida. A su vez, aquellos capaces de gestionar vastos repositorios —empresas que entrenan LLM, junto con bibliotecas y otros archivos— estarían cubiertos por estas excepciones. La reforma de derechos de autor es necesaria para la seguridad nacional Resumen: Los modelos de lenguaje chinos (incluido DeepSeek) están entrenados en mi archivo ilegal de libros y documentos —el más grande del mundo—. Occidente necesita reformar la ley de derechos de autor como una cuestión de seguridad nacional. Por favor, consulte la <a %(all_isbns)s>entrada original del blog</a> para más información. Lanzamos un desafío para mejorar esto. Otorgaríamos un premio de $6,000 para el primer lugar, $3,000 para el segundo lugar y $1,000 para el tercer lugar. Debido a la abrumadora respuesta y las increíbles presentaciones, hemos decidido aumentar ligeramente el fondo de premios y otorgar un tercer lugar compartido de $500 cada uno. Los ganadores están a continuación, pero asegúrese de ver todas las presentaciones <a %(annas_archive)s>aquí</a>, o descargar nuestro <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Primer lugar $6,000: phiresky Esta <a %(phiresky_github)s>presentación</a> (<a %(annas_archive_note_2951)s>comentario en Gitlab</a>) es simplemente todo lo que queríamos, ¡y más! Nos gustaron especialmente las opciones de visualización increíblemente flexibles (incluso admitiendo shaders personalizados), pero con una lista completa de preajustes. También nos gustó lo rápido y fluido que es todo, la implementación simple (que ni siquiera tiene un backend), el ingenioso minimapa y la extensa explicación en su <a %(phiresky_github)s>entrada de blog</a>. ¡Increíble trabajo y un ganador bien merecido! - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nuestros corazones están llenos de gratitud. Ideas notables Rascacielos para la rareza Muchos deslizadores para comparar datasets, como si fueras un DJ. Barra de escala con número de libros. Etiquetas bonitas. Esquema de color predeterminado genial y mapa de calor. Vista de mapa única y filtros Anotaciones, y también estadísticas en vivo Estadísticas en vivo Algunas más ideas e implementaciones que nos gustaron particularmente: Podríamos seguir por un tiempo, pero detengámonos aquí. Asegúrate de ver todas las presentaciones <a %(annas_archive)s>aquí</a>, o descarga nuestro <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Tantas presentaciones, y cada una aporta una perspectiva única, ya sea en la interfaz de usuario o en la implementación. Al menos incorporaremos la presentación del primer lugar en nuestro sitio web principal, y quizás algunas otras. También hemos comenzado a pensar en cómo organizar el proceso de identificar, confirmar y luego archivar los libros más raros. Más por venir en este frente. Gracias a todos los que participaron. Es increíble que a tanta gente le importe. Cambio fácil de datasets para comparaciones rápidas. Todos los ISBN SSNOs de CADAL Filtración de datos de CERLALC SSIDs de DuXiu Índice de eBooks de EBSCOhost Google Books Goodreads Internet Archive ISBNdb Registro Global de Editores ISBN Libby Archivos en el Archivo de Anna Nexus/STC OCLC/Worldcat OpenLibrary Biblioteca Estatal Rusa Biblioteca Imperial de Trantor Segundo lugar $3,000: hypha “Si bien los cuadrados y rectángulos perfectos son matemáticamente agradables, no proporcionan una superior localidad en un contexto de mapeo. Creo que la asimetría inherente en estos Hilbert o Morton clásicos no es un defecto sino una característica. Al igual que el famoso contorno en forma de bota de Italia lo hace instantáneamente reconocible en un mapa, las "peculiaridades" únicas de estas curvas pueden servir como puntos de referencia cognitivos. Esta distintividad puede mejorar la memoria espacial y ayudar a los usuarios a orientarse, potencialmente haciendo más fácil localizar regiones específicas o notar patrones.” Otra increíble <a %(annas_archive_note_2913)s>presentación</a>. No tan flexible como el primer lugar, pero en realidad preferimos su visualización a nivel macro sobre el primer lugar (curva de llenado de espacio, bordes, etiquetado, resaltado, desplazamiento y zoom). Un <a %(annas_archive_note_2971)s>comentario</a> de Joe Davis resonó con nosotros: Y aún muchas opciones para visualizar y renderizar, así como una interfaz de usuario increíblemente fluida e intuitiva. ¡Un sólido segundo lugar! - Anna y el equipo (<a %(reddit)s>Reddit</a>) Hace unos meses anunciamos una <a %(all_isbns)s>recompensa de $10,000</a> para hacer la mejor visualización posible de nuestros datos mostrando el espacio ISBN. Enfatizamos mostrar qué archivos ya hemos archivado y cuáles no, y más tarde un conjunto de datos que describe cuántas bibliotecas tienen ISBN (una medida de rareza). Nos hemos sentido abrumados por la respuesta. Ha habido tanta creatividad. ¡Un gran agradecimiento a todos los que han participado: su energía y entusiasmo son contagiosos! En última instancia, queríamos responder a las siguientes preguntas: <strong>¿qué libros existen en el mundo, cuántos hemos archivado ya y en cuáles deberíamos centrarnos a continuación?</strong> Es genial ver que tantas personas se preocupan por estas preguntas. Comenzamos con una visualización básica nosotros mismos. En menos de 300kb, esta imagen representa sucintamente la mayor “lista de libros” completamente abierta jamás ensamblada en la historia de la humanidad: Tercer lugar $500 #1: maxlion En esta <a %(annas_archive_note_2940)s>presentación</a> realmente nos gustaron los diferentes tipos de vistas, en particular las vistas de comparación y de editor. Tercer lugar $500 #2: abetusk Aunque no es la interfaz de usuario más pulida, esta <a %(annas_archive_note_2917)s>presentación</a> cumple con muchos de los requisitos. Nos gustó particularmente su función de comparación. Tercer lugar $500 #3: conundrumer0 Al igual que el primer lugar, esta <a %(annas_archive_note_2975)s>presentación</a> nos impresionó con su flexibilidad. En última instancia, esto es lo que hace que una herramienta de visualización sea excelente: máxima flexibilidad para usuarios avanzados, manteniendo las cosas simples para los usuarios promedio. Tercer lugar $500 #4: charelf La última <a %(annas_archive_note_2947)s>presentación</a> en recibir un premio es bastante básica, pero tiene algunas características únicas que realmente nos gustaron. Nos gustó cómo muestran cuántos datasets cubren un ISBN particular como medida de popularidad/confiabilidad. También nos gustó mucho la simplicidad pero efectividad de usar un deslizador de opacidad para comparaciones. Ganadores de la recompensa de visualización de ISBN de $10,000 Resumen: Recibimos algunas presentaciones increíbles para la recompensa de visualización de ISBN de $10,000. Antecedentes ¿Cómo puede el Archivo de Anna lograr su misión de respaldar todo el conocimiento de la humanidad, sin saber qué libros aún están por ahí? Necesitamos una lista de tareas. Una forma de mapear esto es a través de los números ISBN, que desde la década de 1970 se han asignado a cada libro publicado (en la mayoría de los países). No hay una autoridad central que conozca todas las asignaciones de ISBN. En su lugar, es un sistema distribuido, donde los países obtienen rangos de números, que luego asignan rangos más pequeños a los principales editores, quienes podrían subdividir aún más los rangos a editores menores. Finalmente, se asignan números individuales a los libros. Comenzamos a mapear los ISBNs <a %(blog)s>hace dos años</a> con nuestro raspado de ISBNdb. Desde entonces, hemos raspado muchas más fuentes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, y más. Se puede encontrar una lista completa en las páginas de “Datasets” y “Torrents” en el Archivo de Anna. Ahora tenemos, con mucho, la colección de metadata de libros (y por lo tanto ISBNs) completamente abierta y fácilmente descargable más grande del mundo. Hemos <a %(blog)s>escrito extensamente</a> sobre por qué nos importa la preservación, y por qué actualmente estamos en una ventana crítica. Ahora debemos identificar libros raros, poco enfocados y singularmente en riesgo y preservarlos. Tener buena metadata de todos los libros del mundo ayuda con eso. Recompensa de $10,000 Se dará una fuerte consideración a la usabilidad y a lo bien que se vea. Muestra metadata real para ISBNs individuales al hacer zoom, como título y autor. Mejor curva de llenado de espacio. Por ejemplo, un zig-zag, yendo de 0 a 4 en la primera fila y luego de regreso (en reversa) de 5 a 9 en la segunda fila — aplicado recursivamente. Esquemas de color diferentes o personalizables. Vistas especiales para comparar datasets. Formas de depurar problemas, como otros metadata que no concuerdan bien (por ejemplo, títulos muy diferentes). Anotar imágenes con comentarios sobre ISBNs o rangos. Cualquier heurística para identificar libros raros o en riesgo. ¡Cualquier idea creativa que se te ocurra! Código El código para generar estas imágenes, así como otros ejemplos, se puede encontrar en <a %(annas_archive)s>este directorio</a>. Hemos ideado un formato de datos compacto, con el cual toda la información requerida de ISBN ocupa alrededor de 75MB (comprimido). La descripción del formato de datos y el código para generarlo se pueden encontrar <a %(annas_archive_l1244_1319)s>aquí</a>. Para la recompensa no estás obligado a usar esto, pero probablemente sea el formato más conveniente para comenzar. Puedes transformar nuestro metadata como desees (aunque todo tu código debe ser de código abierto). Estamos ansiosos por ver lo que se te ocurre. ¡Buena suerte! Haz un fork de este repositorio y edita este HTML de la publicación del blog (no se permiten otros backends además de nuestro backend Flask). Haz que la imagen de arriba sea suavemente ampliable, para que puedas hacer zoom hasta los ISBN individuales. Al hacer clic en los ISBNs, debería llevarte a una página de metadata o búsqueda en el Archivo de Anna. Debes poder seguir cambiando entre todos los diferentes datasets. Los rangos de países y editores deben destacarse al pasar el cursor. Puedes usar, por ejemplo, <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> para información de países, y nuestro raspado “isbngrp” para editores (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Debe funcionar bien en escritorio y móvil. Hay mucho por explorar aquí, por lo que estamos anunciando una recompensa por mejorar la visualización anterior. A diferencia de la mayoría de nuestras recompensas, esta tiene un límite de tiempo. Debes <a %(annas_archive)s>enviar</a> tu código de fuente abierta antes del 2025-01-31 (23:59 UTC). La mejor presentación recibirá $6,000, el segundo lugar $3,000, y el tercer lugar $1,000. Todas las recompensas se otorgarán usando Monero (XMR). A continuación se presentan los criterios mínimos. Si ninguna presentación cumple con los criterios, aún podríamos otorgar algunas recompensas, pero eso será a nuestra discreción. Para puntos extra (estas son solo ideas — deja que tu creatividad vuele): Puedes desviarte completamente de los criterios mínimos y hacer una visualización completamente diferente. Si es realmente espectacular, entonces califica para la recompensa, pero a nuestra discreción. Haz tus envíos publicando un comentario en <a %(annas_archive)s>este problema</a> con un enlace a tu repositorio bifurcado, solicitud de fusión o diferencia. - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Esta imagen tiene 1000×800 píxeles. Cada píxel representa 2,500 ISBN. Si tenemos un archivo para un ISBN, hacemos que ese píxel sea más verde. Si sabemos que se ha emitido un ISBN, pero no tenemos un archivo correspondiente, lo hacemos más rojo. En menos de 300kb, esta imagen representa sucintamente la “lista de libros” completamente abierta más grande jamás ensamblada en la historia de la humanidad (unos pocos cientos de GB comprimidos en su totalidad). También muestra: queda mucho trabajo por hacer en la copia de seguridad de libros (solo tenemos 16%%). Visualizando Todos los ISBN — Recompensa de $10,000 para el 2025-01-31 Esta imagen representa la “lista de libros” completamente abierta más grande jamás ensamblada en la historia de la humanidad. Visualización Además de la imagen general, también podemos ver los datasets individuales que hemos adquirido. Use el menú desplegable y los botones para cambiar entre ellos. Hay muchos patrones interesantes para ver en estas imágenes. ¿Por qué hay cierta regularidad de líneas y bloques, que parece ocurrir a diferentes escalas? ¿Cuáles son las áreas vacías? ¿Por qué ciertos datasets están tan agrupados? Dejaremos estas preguntas como un ejercicio para el lector. - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusión Con este estándar, podemos hacer lanzamientos de manera más incremental y agregar nuevas fuentes de datos más fácilmente. ¡Ya tenemos algunos lanzamientos emocionantes en proceso! También esperamos que sea más fácil para otras bibliotecas en la sombra espejar nuestras colecciones. Después de todo, nuestro objetivo es preservar el conocimiento y la cultura humana para siempre, así que cuanta más redundancia, mejor. Ejemplo Veamos nuestro reciente lanzamiento de Z-Library como ejemplo. Consiste en dos colecciones: “<span style="background: #fffaa3">zlib3_records</span>” y “<span style="background: #ffd6fe">zlib3_files</span>”. Esto nos permite extraer y liberar por separado los registros de metadata de los archivos de libros reales. Por lo tanto, lanzamos dos torrents con archivos de metadata: También lanzamos un montón de torrents con carpetas de datos binarios, pero solo para la colección “<span style="background: #ffd6fe">zlib3_files</span>”, 62 en total: Al ejecutar <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver lo que hay dentro: En este caso, es la metadata de un libro según lo reportado por Z-Library. A nivel superior solo tenemos “aacid” y “metadata”, pero no “data_folder”, ya que no hay datos binarios correspondientes. El AACID contiene “22430000” como el ID principal, que podemos ver que se toma de “zlibrary_id”. Podemos esperar que otros AAC en esta colección tengan la misma estructura. Ahora ejecutemos <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Esta es una metadata de AAC mucho más pequeña, aunque la mayor parte de este AAC se encuentra en otro lugar en un archivo binario. Después de todo, esta vez tenemos un “data_folder”, por lo que podemos esperar que los datos binarios correspondientes se encuentren en <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. La “metadata” contiene el “zlibrary_id”, por lo que podemos asociarlo fácilmente con el AAC correspondiente en la colección “zlib_records”. Podríamos haberlo asociado de varias maneras diferentes, por ejemplo, a través de AACID — el estándar no lo prescribe. Tenga en cuenta que tampoco es necesario que el campo “metadata” sea en sí mismo JSON. Podría ser una cadena que contenga XML o cualquier otro formato de datos. Incluso podría almacenar información de metadata en el blob binario asociado, por ejemplo, si es una gran cantidad de datos. Archivos y metadata heterogéneos, lo más cercano posible al formato original. Los datos binarios pueden ser servidos directamente por servidores web como Nginx. Identificadores heterogéneos en las bibliotecas de origen, o incluso la falta de identificadores. Lanzamientos separados de metadata frente a datos de archivos, o lanzamientos solo de metadata (por ejemplo, nuestro lanzamiento de ISBNdb). Distribución a través de torrents, aunque con la posibilidad de otros métodos de distribución (por ejemplo, IPFS). Registros inmutables, ya que debemos asumir que nuestros torrents vivirán para siempre. Lanzamientos incrementales / lanzamientos ampliables. Legible y escribible por máquinas, de manera conveniente y rápida, especialmente para nuestra pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspección humana algo fácil, aunque esto es secundario a la legibilidad por máquinas. Fácil de sembrar nuestras colecciones con un seedbox estándar alquilado. Objetivos de diseño No nos importa que los archivos sean fáciles de navegar manualmente en disco, o que sean buscables sin preprocesamiento. No nos importa ser directamente compatibles con el software de biblioteca existente. Aunque debería ser fácil para cualquiera sembrar nuestra colección usando torrents, no esperamos que los archivos sean utilizables sin un conocimiento técnico significativo y compromiso. Nuestro caso de uso principal es la distribución de archivos y metadata asociada de diferentes colecciones existentes. Nuestras consideraciones más importantes son: Algunos objetivos no prioritarios: Dado que Anna’s Archive es de código abierto, queremos probar nuestro formato directamente. Cuando actualizamos nuestro índice de búsqueda, solo accedemos a rutas disponibles públicamente, para que cualquiera que bifurque nuestra biblioteca pueda comenzar rápidamente. <strong>AAC.</strong> AAC (Contenedor del Archivo de Anna) es un único elemento que consiste en <strong>metadata</strong>, y opcionalmente <strong>datos binarios</strong>, ambos inmutables. Tiene un identificador único global, llamado <strong>AACID</strong>. <strong>AACID.</strong> El formato de AACID es el siguiente: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por ejemplo, un AACID real que hemos lanzado es <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Rango de AACID.</strong> Dado que los AACIDs contienen marcas de tiempo que aumentan de manera monótona, podemos usar eso para denotar rangos dentro de una colección particular. Usamos este formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, donde las marcas de tiempo son inclusivas. Esto es consistente con la notación ISO 8601. Los rangos son continuos y pueden superponerse, pero en caso de superposición deben contener registros idénticos a los lanzados previamente en esa colección (ya que los AACs son inmutables). No se permiten registros faltantes. <code>{collection}</code>: el nombre de la colección, que puede contener letras ASCII, números y guiones bajos (pero no dobles guiones bajos). <code>{collection-specific ID}</code>: un identificador específico de la colección, si es aplicable, por ejemplo, el ID de Z-Library. Puede ser omitido o truncado. Debe ser omitido o truncado si el AACID excedería de otro modo los 150 caracteres. <code>{ISO 8601 timestamp}</code>: una versión corta del ISO 8601, siempre en UTC, por ejemplo, <code>20220723T194746Z</code>. Este número debe aumentar de manera monótona para cada lanzamiento, aunque su semántica exacta puede diferir por colección. Sugerimos usar el tiempo de scraping o de generación del ID. <code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por ejemplo, usando base57. Actualmente usamos la biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Carpeta de datos binarios.</strong> Una carpeta con los datos binarios de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades: El directorio debe contener archivos de datos para todos los AACs dentro del rango especificado. Cada archivo de datos debe tener su AACID como nombre de archivo (sin extensiones). El nombre del directorio debe ser un rango de AACID, precedido por <code style="color: green">annas_archive_data__</code>, y sin sufijo. Por ejemplo, uno de nuestros lanzamientos reales tiene un directorio llamado<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Se recomienda que estas carpetas sean de un tamaño manejable, por ejemplo, que no superen los 100GB-1TB cada una, aunque esta recomendación puede cambiar con el tiempo. <strong>Colección.</strong> Cada AAC pertenece a una colección, que por definición es una lista de AACs que son semánticamente consistentes. Esto significa que si realizas un cambio significativo en el formato de los metadata, entonces debes crear una nueva colección. El estándar <strong>Archivo de metadata.</strong> Un archivo de metadata contiene los metadata de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades: <code>data_folder</code> es opcional, y es el nombre de la carpeta de datos binarios que contiene los datos binarios correspondientes. El nombre del archivo de los datos binarios correspondientes dentro de esa carpeta es el AACID del registro. Cada objeto JSON debe contener los siguientes campos en el nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). No se permiten otros campos. El nombre del archivo debe ser un rango de AACID, precedido por <code style="color: red">annas_archive_meta__</code> y seguido por <code>.jsonl.zstd</code>. Por ejemplo, uno de nuestros lanzamientos se llama<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Como indica la extensión del archivo, el tipo de archivo es <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>. <code>metadata</code> son metadata arbitrarios, según la semántica de la colección. Deben ser semánticamente consistentes dentro de la colección. El prefijo <code style="color: red">annas_archive_meta__</code> puede adaptarse al nombre de tu institución, por ejemplo, <code style="color: red">my_institute_meta__</code>. <strong>Colecciones de “registros” y “archivos”.</strong> Por convención, a menudo es conveniente lanzar “registros” y “archivos” como colecciones diferentes, para que puedan ser lanzadas en diferentes horarios, por ejemplo, basados en las tasas de scraping. Un “registro” es una colección solo de metadata, que contiene información como títulos de libros, autores, ISBNs, etc., mientras que “archivos” son las colecciones que contienen los archivos reales (pdf, epub). Finalmente, nos decidimos por un estándar relativamente simple. Es bastante flexible, no normativo y un trabajo en progreso. <strong>Torrents.</strong> Los archivos de metadata y las carpetas de datos binarios pueden agruparse en torrents, con un torrent por archivo de metadata o un torrent por carpeta de datos binarios. Los torrents deben tener el nombre original del archivo/directorio más un sufijo <code>.torrent</code> como su nombre de archivo. <a %(wikipedia_annas_archive)s>El Archivo de Anna</a> se ha convertido, con diferencia, en la biblioteca fantasma más grande del mundo, y la única biblioteca fantasma de su escala que es completamente de código abierto y datos abiertos. A continuación se muestra una tabla de nuestra página de Datasets (ligeramente modificada): Logramos esto de tres maneras: Reflejando bibliotecas en la sombra de datos abiertos existentes (como Sci-Hub y Library Genesis). Ayudando a bibliotecas en la sombra que quieren ser más abiertas, pero no tenían el tiempo o los recursos para hacerlo (como la colección de cómics de Libgen). Raspando bibliotecas que no desean compartir en masa (como Z-Library). Para (2) y (3) ahora gestionamos una considerable colección de torrents nosotros mismos (cientos de TBs). Hasta ahora hemos abordado estas colecciones como casos únicos, lo que significa infraestructura y organización de datos a medida para cada colección. Esto añade una carga significativa a cada lanzamiento y hace que sea particularmente difícil realizar lanzamientos más incrementales. Por eso decidimos estandarizar nuestros lanzamientos. Este es un post técnico en el que presentamos nuestro estándar: <strong>Contenedores de Anna’s Archive</strong>. Contenedores del Archivo de Anna (AAC): estandarizando lanzamientos de la biblioteca fantasma más grande del mundo El Archivo de Anna se ha convertido en la biblioteca fantasma más grande del mundo, lo que nos obliga a estandarizar nuestros lanzamientos. Más de 300GB de portadas de libros liberadas Finalmente, nos complace anunciar un pequeño lanzamiento. En colaboración con las personas que operan el fork Libgen.rs, estamos compartiendo todas sus portadas de libros a través de torrents e IPFS. Esto distribuirá la carga de ver las portadas entre más máquinas y las preservará mejor. En muchos (pero no todos) casos, las portadas de los libros están incluidas en los archivos mismos, por lo que esto es una especie de "datos derivados". Pero tenerlo en IPFS sigue siendo muy útil para la operación diaria tanto de Archivo de Anna como de los diversos forks de Library Genesis. Como de costumbre, puedes encontrar este lanzamiento en el Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). No lo enlazaremos aquí, pero puedes encontrarlo fácilmente. Esperamos poder relajar nuestro ritmo un poco, ahora que tenemos una alternativa decente a Z-Library. Esta carga de trabajo no es particularmente sostenible. Si estás interesado en ayudar con la programación, operaciones de servidor o trabajo de preservación, definitivamente contáctanos. Todavía hay mucho <a %(annas_archive)s>trabajo por hacer</a>. Gracias por tu interés y apoyo. Cambiar a ElasticSearch Algunas consultas tomaban mucho tiempo, hasta el punto de acaparar todas las conexiones abiertas. Por defecto, MySQL tiene una longitud mínima de palabra, o su índice puede volverse realmente grande. La gente reportó no poder buscar "Ben Hur". La búsqueda solo era algo rápida cuando estaba completamente cargada en memoria, lo que requería que obtuviéramos una máquina más cara para ejecutarla, además de algunos comandos para precargar el índice al inicio. No habríamos podido extenderlo fácilmente para construir nuevas funciones, como mejor <a %(wikipedia_cjk_characters)s>tokenización para idiomas sin espacios</a>, filtrado/facetas, ordenación, sugerencias de "¿quiso decir?", autocompletar, y así sucesivamente. Uno de nuestros <a %(annas_archive)s>tickets</a> era un conjunto de problemas con nuestro sistema de búsqueda. Usamos la búsqueda de texto completo de MySQL, ya que teníamos todos nuestros datos en MySQL de todos modos. Pero tenía sus límites: Después de hablar con un montón de expertos, nos decidimos por ElasticSearch. No ha sido perfecto (sus sugerencias de "¿quiso decir?" y funciones de autocompletar por defecto son malas), pero en general ha sido mucho mejor que MySQL para la búsqueda. Todavía no estamos <a %(youtube)s>muy entusiasmados</a> con usarlo para cualquier dato crítico (aunque han hecho mucho <a %(elastic_co)s>progreso</a>), pero en general estamos bastante contentos con el cambio. Por ahora, hemos implementado una búsqueda mucho más rápida, mejor soporte de idiomas, mejor ordenación por relevancia, diferentes opciones de ordenación y filtrado por idioma/tipo de libro/tipo de archivo. Si tienes curiosidad sobre cómo funciona, <a %(annas_archive_l140)s>échale</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>vistazo</a>. Es bastante accesible, aunque podría usar algunos comentarios más… El Archivo de Anna es completamente de código abierto Creemos que la información debe ser libre, y nuestro propio código no es una excepción. Hemos liberado todo nuestro código en nuestra instancia de Gitlab alojada de forma privada: <a %(annas_archive)s>Software de Anna</a>. También usamos el rastreador de problemas para organizar nuestro trabajo. Si deseas participar en nuestro desarrollo, este es un gran lugar para comenzar. Para darte una idea de las cosas en las que estamos trabajando, toma nuestro trabajo reciente en mejoras de rendimiento del lado del cliente. Como aún no hemos implementado la paginación, a menudo devolvíamos páginas de búsqueda muy largas, con 100-200 resultados. No queríamos cortar los resultados de búsqueda demasiado pronto, pero esto significaba que ralentizaría algunos dispositivos. Para esto, implementamos un pequeño truco: envolvimos la mayoría de los resultados de búsqueda en comentarios HTML (<code><!-- --></code>), y luego escribimos un pequeño Javascript que detectaría cuándo un resultado debería hacerse visible, momento en el cual desenvolveríamos el comentario: ¡La "virtualización" del DOM implementada en 23 líneas, sin necesidad de bibliotecas sofisticadas! Este es el tipo de código pragmático y rápido que se obtiene cuando se tiene tiempo limitado y problemas reales que necesitan ser resueltos. ¡Se ha informado que nuestra búsqueda ahora funciona bien en dispositivos lentos! Otro gran esfuerzo fue automatizar la construcción de la base de datos. Cuando lanzamos, simplemente juntamos diferentes fuentes de manera desordenada. Ahora queremos mantenerlas actualizadas, así que escribimos un montón de scripts para descargar nuevos metadata de los dos forks de Library Genesis e integrarlos. El objetivo no es solo hacer esto útil para nuestro archivo, sino facilitar las cosas a cualquiera que quiera experimentar con metadata de bibliotecas fantasma. El objetivo sería un cuaderno de Jupyter que tenga todo tipo de metadata interesante disponible, para que podamos hacer más investigaciones como averiguar qué <a %(blog)s>porcentaje de ISBNs se preservan para siempre</a>. Finalmente, renovamos nuestro sistema de donaciones. Ahora puede usar una tarjeta de crédito para depositar dinero directamente en nuestras billeteras de criptomonedas, sin realmente necesitar saber nada sobre criptomonedas. Seguiremos monitoreando qué tan bien funciona esto en la práctica, pero es un gran avance. Con Z-Library cayendo y sus (presuntos) fundadores siendo arrestados, hemos estado trabajando día y noche para ofrecer una buena alternativa con el Archivo de Anna (no lo enlazaremos aquí, pero puedes buscarlo en Google). Aquí están algunas de las cosas que hemos logrado recientemente. Actualización de Anna: archivo completamente de código abierto, ElasticSearch, más de 300GB de portadas de libros Hemos estado trabajando día y noche para ofrecer una buena alternativa con el Archivo de Anna. Aquí están algunas de las cosas que hemos logrado recientemente. Análisis Los duplicados semánticos (diferentes escaneos del mismo libro) teóricamente pueden ser filtrados, pero es complicado. Al revisar manualmente los cómics encontramos demasiados falsos positivos. Hay algunos duplicados puramente por MD5, lo cual es relativamente desperdiciado, pero eliminarlos solo nos daría alrededor de un 1%% in de ahorro. A esta escala eso sigue siendo alrededor de 1TB, pero también, a esta escala 1TB realmente no importa. Preferimos no arriesgarnos a destruir datos accidentalmente en este proceso. Encontramos un montón de datos que no son libros, como películas basadas en cómics. Eso también parece un desperdicio, ya que estos ya están ampliamente disponibles por otros medios. Sin embargo, nos dimos cuenta de que no podíamos simplemente filtrar los archivos de películas, ya que también hay <em>libros de cómics interactivos</em> que se lanzaron en la computadora, que alguien grabó y guardó como películas. En última instancia, cualquier cosa que pudiéramos eliminar de la colección solo ahorraría unos pocos porcentajes. Entonces recordamos que somos acumuladores de datos, y las personas que estarán espejando esto también son acumuladores de datos, así que, "¿QUÉ QUIERES DECIR CON ELIMINAR?!" :) Cuando recibes 95TB volcados en tu clúster de almacenamiento, intentas entender qué hay allí… Hicimos un análisis para ver si podíamos reducir un poco el tamaño, como eliminando duplicados. Aquí están algunos de nuestros hallazgos: Por lo tanto, les presentamos la colección completa y sin modificar. Es una gran cantidad de datos, pero esperamos que suficientes personas se interesen en sembrarla de todos modos. Colaboración Dada su magnitud, esta colección ha estado en nuestra lista de deseos durante mucho tiempo, así que después de nuestro éxito con la copia de seguridad de Z-Library, pusimos nuestra mira en esta colección. Al principio la extraíamos directamente, lo cual fue todo un desafío, ya que su servidor no estaba en las mejores condiciones. De esta manera obtuvimos alrededor de 15TB, pero fue un proceso lento. Afortunadamente, logramos ponernos en contacto con el operador de la biblioteca, quien accedió a enviarnos todos los datos directamente, lo cual fue mucho más rápido. Aún así, tomó más de medio año transferir y procesar todos los datos, y casi los perdimos todos debido a la corrupción del disco, lo que habría significado empezar de nuevo. Esta experiencia nos ha hecho creer que es importante difundir estos datos lo más rápido posible, para que puedan ser replicados ampliamente. ¡Estamos a solo uno o dos incidentes desafortunados de perder esta colección para siempre! La colección Moverse rápido significa que la colección está un poco desorganizada… Echemos un vistazo. Imagina que tenemos un sistema de archivos (que en realidad estamos dividiendo en torrents): El primer directorio, <code>/repository</code>, es la parte más estructurada de esto. Este directorio contiene los llamados “mil dirs”: directorios cada uno con mil archivos, que están numerados incrementalmente en la base de datos. El directorio <code>0</code> contiene archivos con comic_id 0–999, y así sucesivamente. Este es el mismo esquema que Library Genesis ha estado utilizando para sus colecciones de ficción y no ficción. La idea es que cada “mil dir” se convierta automáticamente en un torrent tan pronto como se llene. Sin embargo, el operador de Libgen.li nunca creó torrents para esta colección, por lo que los mil directorios probablemente se volvieron inconvenientes y dieron paso a “directorios desordenados”. Estos son <code>/comics0</code> hasta <code>/comics4</code>. Todos contienen estructuras de directorios únicas, que probablemente tenían sentido para recopilar los archivos, pero ahora no tienen mucho sentido para nosotros. Afortunadamente, los metadatos todavía se refieren directamente a todos estos archivos, ¡así que la organización de su almacenamiento en disco realmente no importa! El metadata está disponible en forma de una base de datos MySQL. Se puede descargar directamente desde el sitio web de Libgen.li, pero también lo pondremos a disposición en un torrent, junto con nuestra propia tabla con todos los hashes MD5. <q>La Dra. Barbara Gordon intenta perderse en el mundo mundano de la biblioteca…</q> Bifurcaciones de Libgen Primero, un poco de contexto. Puede que conozcas Library Genesis por su épica colección de libros. Menos personas saben que los voluntarios de Library Genesis han creado otros proyectos, como una considerable colección de revistas y documentos estándar, una copia de seguridad completa de Sci-Hub (en colaboración con la fundadora de Sci-Hub, Alexandra Elbakyan) y, de hecho, una enorme colección de cómics. En algún momento, diferentes operadores de espejos de Library Genesis tomaron caminos separados, lo que dio lugar a la situación actual de tener varias "bifurcaciones" diferentes, todas aún llevando el nombre de Library Genesis. La bifurcación Libgen.li tiene de manera única esta colección de cómics, así como una considerable colección de revistas (en la que también estamos trabajando). Recaudación de fondos Estamos lanzando estos datos en algunos grandes fragmentos. El primer torrent es de <code>/comics0</code>, que pusimos en un enorme archivo .tar de 12TB. Eso es mejor para tu disco duro y software de torrents que un millón de archivos más pequeños. Como parte de este lanzamiento, estamos realizando una recaudación de fondos. Buscamos recaudar $20,000 para cubrir los costos operativos y de contratación para esta colección, así como para habilitar proyectos continuos y futuros. Tenemos algunos <em>enormes</em> en proceso. <em>¿A quién estoy apoyando con mi donación?</em> En resumen: estamos respaldando todo el conocimiento y la cultura de la humanidad, y haciéndolo fácilmente accesible. Todo nuestro código y datos son de código abierto, somos un proyecto completamente dirigido por voluntarios, y hemos guardado 125TB de libros hasta ahora (además de los torrents existentes de Libgen y Scihub). En última instancia, estamos construyendo un volante que permite e incentiva a las personas a encontrar, escanear y respaldar todos los libros del mundo. Escribiremos sobre nuestro plan maestro en una publicación futura. :) Si donas una membresía de 12 meses de “Amazing Archivist” ($780), puedes <strong>“adoptar un torrent”</strong>, ¡Lo que significa que pondremos tu nombre de usuario o mensaje en el nombre de archivo de uno de los torrents! Puedes donar yendo a <a %(wikipedia_annas_archive)s>Archivo de Anna</a> y haciendo clic en el botón “Donar”. También estamos buscando más voluntarios: ingenieros de software, investigadores de seguridad, expertos en comercio anónimo y traductores. También puedes apoyarnos proporcionando servicios de alojamiento. Y, por supuesto, ¡por favor siembra nuestros torrents! ¡Gracias a todos los que ya nos han apoyado tan generosamente! Realmente están marcando la diferencia. Aquí están los torrents lanzados hasta ahora (todavía estamos procesando el resto): Todos los torrents se pueden encontrar en <a %(wikipedia_annas_archive)s>Archivo de Anna</a> bajo “Datasets” (no enlazamos directamente allí, para que los enlaces a este blog no sean eliminados de Reddit, Twitter, etc.). Desde allí, sigue el enlace al sitio web de Tor. <a %(news_ycombinator)s>Discutir en Hacker News</a> ¿Qué sigue? Un montón de torrents son geniales para la preservación a largo plazo, pero no tanto para el acceso diario. Estaremos trabajando con socios de alojamiento para subir todos estos datos a la web (ya que el Archivo de Anna no aloja nada directamente). Por supuesto, podrás encontrar estos enlaces de descarga en el Archivo de Anna. ¡También estamos invitando a todos a hacer cosas con estos datos! Ayúdanos a analizarlos mejor, deduplicarlos, ponerlos en IPFS, remixarlos, entrenar tus modelos de IA con ellos, y así sucesivamente. Son todos tuyos, y estamos ansiosos por ver qué haces con ellos. Finalmente, como se dijo antes, todavía tenemos algunos lanzamientos enormes por venir (si <em>alguien</em> pudiera <em>accidentalmente</em> enviarnos un volcado de una <em>cierta</em> base de datos ACS4, sabes dónde encontrarnos...), así como construir el volante para respaldar todos los libros del mundo. Así que mantente atento, apenas estamos comenzando. - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) La biblioteca fantasma más grande de cómics es probablemente la de un fork particular de Library Genesis: Libgen.li. El único administrador que dirige ese sitio logró recopilar una colección de cómics increíble de más de 2 millones de archivos, totalizando más de 95TB. Sin embargo, a diferencia de otras colecciones de Library Genesis, esta no estaba disponible en masa a través de torrents. Solo podías acceder a estos cómics individualmente a través de su lento servidor personal — un único punto de falla. ¡Hasta hoy! En esta publicación te contaremos más sobre esta colección y sobre nuestra recaudación de fondos para apoyar más de este trabajo. El Archivo de Anna ha respaldado la biblioteca fantasma de cómics más grande del mundo (95TB) — puedes ayudar a sembrarla La biblioteca fantasma de cómics más grande del mundo tenía un único punto de falla... hasta hoy. Advertencia: esta publicación de blog ha sido descontinuada. Hemos decidido que IPFS aún no está listo para el horario estelar. Aún enlazaremos a archivos en IPFS desde el Archivo de Anna cuando sea posible, pero ya no lo alojaremos nosotros mismos, ni recomendamos a otros que lo espejen usando IPFS. Por favor, consulta nuestra página de Torrents si deseas ayudar a preservar nuestra colección. Poniendo 5,998,794 libros en IPFS Una multiplicación de copias Volviendo a nuestra pregunta original: ¿cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad? El principal problema aquí es que nuestra colección ha estado <a %(torrents_stats)s>creciendo</a> rápidamente, al raspar y abrir algunas colecciones masivas (además del increíble trabajo ya realizado por otras bibliotecas en la sombra de datos abiertos como Sci-Hub y Library Genesis). Este crecimiento de datos dificulta que las colecciones sean espejadas en todo el mundo. ¡El almacenamiento de datos es caro! Pero somos optimistas, especialmente al observar las siguientes tres tendencias. El <a %(annas_archive_stats)s>tamaño total</a> de nuestras colecciones, en los últimos meses, desglosado por número de sembradores de torrents. Tendencias de precios de HDD de diferentes fuentes (haga clic para ver el estudio). <a %(critical_window_chinese)s>Versión en chino 中文版</a>, discute en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Hemos recogido los frutos más accesibles Esto sigue directamente de nuestras prioridades discutidas anteriormente. Preferimos trabajar en liberar grandes colecciones primero. Ahora que hemos asegurado algunas de las colecciones más grandes del mundo, esperamos que nuestro crecimiento sea mucho más lento. Todavía hay una larga cola de colecciones más pequeñas, y se escanean o publican nuevos libros todos los días, pero la tasa probablemente será mucho más lenta. Podríamos aún duplicar o incluso triplicar nuestro tamaño, pero en un período de tiempo más largo. Mejoras en OCR. Prioridades Código de software de ciencia e ingeniería Versiones ficticias o de entretenimiento de todo lo anterior Datos geográficos (por ejemplo, mapas, estudios geológicos) Datos internos de corporaciones o gobiernos (filtraciones) Datos de medición como mediciones científicas, datos económicos, informes corporativos Registros de metadata en general (de no ficción y ficción; de otros medios, arte, personas, etc.; incluyendo reseñas) Libros de no ficción Revistas de no ficción, periódicos, manuales Transcripciones de no ficción de charlas, documentales, podcasts Datos orgánicos como secuencias de ADN, semillas de plantas o muestras microbianas Artículos académicos, revistas, informes Sitios web de ciencia e ingeniería, discusiones en línea Transcripciones de procedimientos legales o judiciales Únicamente en riesgo de destrucción (por ejemplo, por guerra, recortes de financiación, demandas o persecución política) Raras Únicamente desatendidas ¿Por qué nos importan tanto los artículos y los libros? Dejemos de lado nuestra creencia fundamental en la preservación en general — podríamos escribir otra publicación sobre eso. Entonces, ¿por qué específicamente artículos y libros? La respuesta es simple: <strong>densidad de información</strong>. Por megabyte de almacenamiento, el texto escrito almacena la mayor cantidad de información de todos los medios. Aunque nos importa tanto el conocimiento como la cultura, nos importa más el primero. En general, encontramos una jerarquía de densidad de información e importancia de la preservación que se ve aproximadamente así: La clasificación en esta lista es algo arbitraria — varios elementos están empatados o hay desacuerdos dentro de nuestro equipo — y probablemente estamos olvidando algunas categorías importantes. Pero esta es aproximadamente nuestra prioridad. Algunos de estos elementos son demasiado diferentes de los otros como para preocuparnos (o ya están siendo atendidos por otras instituciones), como los datos orgánicos o los datos geográficos. Pero la mayoría de los elementos en esta lista son realmente importantes para nosotros. Otro gran factor en nuestra priorización es cuán en riesgo está una obra determinada. Preferimos centrarnos en obras que son: Finalmente, nos importa la escala. Tenemos tiempo y dinero limitados, por lo que preferimos pasar un mes salvando 10,000 libros que 1,000 libros, si son igualmente valiosos y están en riesgo. <em><q>Lo perdido no se puede recuperar; pero salvemos lo que queda: no con bóvedas y cerraduras que los alejan de la vista y uso del público, consignándolos al desperdicio del tiempo, sino mediante una multiplicación de copias que los coloque fuera del alcance de los accidentes.</q></em><br>— Thomas Jefferson, 1791 Bibliotecas en la sombra El código puede ser de código abierto en Github, pero Github en su totalidad no puede ser fácilmente espejado y, por lo tanto, preservado (aunque en este caso particular hay copias suficientemente distribuidas de la mayoría de los repositorios de código) Los registros de metadata se pueden ver libremente en el sitio web de Worldcat, pero no se pueden descargar en masa (hasta que los <a %(worldcat_scrape)s>raspamos</a>) Reddit es gratuito de usar, pero recientemente ha implementado medidas estrictas contra el raspado, a raíz del entrenamiento de LLM hambriento de datos (más sobre eso más adelante) Existen muchas organizaciones con misiones y prioridades similares. De hecho, hay bibliotecas, archivos, laboratorios, museos y otras instituciones encargadas de la preservación de este tipo. Muchas de ellas están bien financiadas, por gobiernos, individuos o corporaciones. Pero tienen un gran punto ciego: el sistema legal. Aquí radica el papel único de las bibliotecas en la sombra y la razón por la que existe el Archivo de Anna. Podemos hacer cosas que otras instituciones no tienen permitido hacer. Ahora bien, no es (a menudo) que podamos archivar materiales que son ilegales de preservar en otros lugares. No, es legal en muchos lugares construir un archivo con cualquier libro, documento, revista, etc. Pero lo que a menudo les falta a los archivos legales es <strong>redundancia y longevidad</strong>. Existen libros de los cuales solo hay una copia en alguna biblioteca física en algún lugar. Existen registros de metadata custodiados por una sola corporación. Existen periódicos solo preservados en microfilm en un solo archivo. Las bibliotecas pueden sufrir recortes de financiación, las corporaciones pueden quebrar, los archivos pueden ser bombardeados e incendiados. Esto no es hipotético, sucede todo el tiempo. Lo que podemos hacer de manera única en el Archivo de Anna es almacenar muchas copias de obras, a gran escala. Podemos recopilar documentos, libros, revistas y más, y distribuirlos en masa. Actualmente lo hacemos a través de torrents, pero las tecnologías exactas no importan y cambiarán con el tiempo. Lo importante es distribuir muchas copias por todo el mundo. Esta cita de hace más de 200 años sigue siendo cierta: Una breve nota sobre el dominio público. Dado que el Archivo de Anna se centra de manera única en actividades que son ilegales en muchos lugares del mundo, no nos preocupamos por colecciones ampliamente disponibles, como los libros de dominio público. Las entidades legales a menudo ya se encargan bien de eso. Sin embargo, hay consideraciones que a veces nos llevan a trabajar en colecciones de acceso público: - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Los costos de almacenamiento continúan disminuyendo exponencialmente 3. Mejoras en la densidad de información Actualmente almacenamos libros en los formatos en bruto que se nos entregan. Claro, están comprimidos, pero a menudo todavía son grandes escaneos o fotografías de páginas. Hasta ahora, las únicas opciones para reducir el tamaño total de nuestra colección han sido a través de una compresión más agresiva o la deduplicación. Sin embargo, para obtener ahorros significativos, ambas son demasiado pérdidas para nuestro gusto. La compresión intensa de fotos puede hacer que el texto sea apenas legible. Y la deduplicación requiere una alta confianza de que los libros sean exactamente iguales, lo cual a menudo es demasiado inexacto, especialmente si los contenidos son los mismos pero las digitalizaciones se realizan en diferentes ocasiones. Siempre ha habido una tercera opción, pero su calidad ha sido tan abismal que nunca la consideramos: <strong>OCR, o Reconocimiento Óptico de Caracteres</strong>. Este es el proceso de convertir fotos en texto plano, utilizando IA para detectar los caracteres en las fotos. Las herramientas para esto han existido durante mucho tiempo y han sido bastante decentes, pero "bastante decente" no es suficiente para fines de preservación. Sin embargo, los recientes modelos de aprendizaje profundo multimodal han hecho un progreso extremadamente rápido, aunque todavía a altos costos. Esperamos que tanto la precisión como los costos mejoren drásticamente en los próximos años, hasta el punto en que será realista aplicarlo a toda nuestra biblioteca. Cuando eso suceda, probablemente aún preservaremos los archivos originales, pero además podríamos tener una versión mucho más pequeña de nuestra biblioteca que la mayoría de las personas querrán espejar. Lo interesante es que el texto en bruto se comprime aún mejor y es mucho más fácil de deduplicar, lo que nos brinda aún más ahorros. En general, no es irrealista esperar al menos una reducción de 5-10 veces en el tamaño total de los archivos, quizás incluso más. Incluso con una reducción conservadora de 5 veces, estaríamos viendo <strong>$1,000–$3,000 en 10 años incluso si nuestra biblioteca se triplica en tamaño</strong>. Al momento de escribir, los <a %(diskprices)s>precios de los discos</a> por TB están alrededor de $12 para discos nuevos, $8 para discos usados y $4 para cinta. Si somos conservadores y solo miramos los discos nuevos, eso significa que almacenar un petabyte cuesta alrededor de $12,000. Si asumimos que nuestra biblioteca se triplicará de 900TB a 2.7PB, eso significaría $32,400 para espejar toda nuestra biblioteca. Sumando electricidad, costo de otros hardware, etc., redondeemos a $40,000. O con cinta, más como $15,000–$20,000. Por un lado, <strong>$15,000–$40,000 por la suma de todo el conocimiento humano es una ganga</strong>. Por otro lado, es un poco elevado esperar toneladas de copias completas, especialmente si también queremos que esas personas sigan sembrando sus torrents para el beneficio de otros. Eso es hoy. Pero el progreso avanza: Los costos de los discos duros por TB se han reducido aproximadamente a un tercio en los últimos 10 años, y probablemente seguirán disminuyendo a un ritmo similar. La cinta parece estar en una trayectoria similar. Los precios de los SSD están bajando aún más rápido, y podrían superar los precios de los HDD para finales de la década. Si esto se mantiene, en 10 años podríamos estar viendo solo $5,000–$13,000 para espejar toda nuestra colección (1/3), o incluso menos si crecemos menos en tamaño. Aunque sigue siendo mucho dinero, esto será alcanzable para muchas personas. Y podría ser aún mejor debido al siguiente punto… En el Archivo de Anna, a menudo nos preguntan cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad, cuando el tamaño total ya se está acercando a 1 Petabyte (1000 TB), y sigue creciendo. En este artículo veremos nuestra filosofía, y por qué la próxima década es crítica para nuestra misión de preservar el conocimiento y la cultura de la humanidad. Ventana crítica Si estas previsiones son precisas, <strong>solo necesitamos esperar un par de años</strong> antes de que toda nuestra colección sea ampliamente espejada. Así, en palabras de Thomas Jefferson, “colocados más allá del alcance del accidente.” Desafortunadamente, la llegada de los LLM y su entrenamiento ávido de datos ha puesto a muchos titulares de derechos de autor a la defensiva. Incluso más de lo que ya estaban. Muchos sitios web están dificultando el raspado y archivo, las demandas están volando, y mientras tanto, las bibliotecas y archivos físicos continúan siendo descuidados. Solo podemos esperar que estas tendencias continúen empeorando, y que muchas obras se pierdan mucho antes de que entren en el dominio público. <strong>Estamos en la víspera de una revolución en la preservación, pero <q>lo perdido no se puede recuperar.</q></strong> Tenemos una ventana crítica de aproximadamente 5-10 años durante la cual todavía es bastante costoso operar una biblioteca fantasma y crear muchos espejos alrededor del mundo, y durante la cual el acceso no se ha cerrado completamente aún. Si podemos superar esta ventana, entonces habremos preservado el conocimiento y la cultura de la humanidad para siempre. No debemos dejar que este tiempo se desperdicie. No debemos permitir que esta ventana crítica se cierre sobre nosotros. Vamos. La ventana crítica de las bibliotecas en la sombra ¿Cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad, cuando ya están acercándose a 1 PB? Colección Algo más de información sobre la colección. <a %(duxiu)s>Duxiu</a> es una base de datos masiva de libros escaneados, creada por el <a %(chaoxing)s>Grupo de Biblioteca Digital SuperStar</a>. La mayoría son libros académicos, escaneados para hacerlos disponibles digitalmente a universidades y bibliotecas. Para nuestro público de habla inglesa, <a %(library_princeton)s>Princeton</a> y la <a %(guides_lib_uw)s>Universidad de Washington</a> tienen buenos resúmenes. También hay un excelente artículo que ofrece más contexto: <a %(doi)s>“Digitalización de Libros Chinos: Un Estudio de Caso del Motor de Búsqueda SuperStar DuXiu Scholar”</a> (búscalo en el Archivo de Anna). Los libros de Duxiu han sido pirateados durante mucho tiempo en el internet chino. Usualmente se venden por menos de un dólar por revendedores. Normalmente se distribuyen usando el equivalente chino de Google Drive, que a menudo ha sido hackeado para permitir más espacio de almacenamiento. Algunos detalles técnicos se pueden encontrar <a %(github_duty_machine)s>aquí</a> y <a %(github_821_github_io)s>aquí</a>. Aunque los libros se han distribuido de manera semi-pública, es bastante difícil obtenerlos en grandes cantidades. Esto estaba en lo alto de nuestra lista de tareas pendientes, y asignamos varios meses de trabajo a tiempo completo para ello. Sin embargo, recientemente un voluntario increíble, asombroso y talentoso se puso en contacto con nosotros, diciéndonos que ya había hecho todo este trabajo, a un gran costo. Compartieron la colección completa con nosotros, sin esperar nada a cambio, excepto la garantía de preservación a largo plazo. Verdaderamente notable. Aceptaron pedir ayuda de esta manera para que la colección sea procesada con OCR. La colección consta de 7,543,702 archivos. Esto es más que la no ficción de Library Genesis (alrededor de 5.3 millones). El tamaño total de los archivos es de aproximadamente 359TB (326TiB) en su forma actual. Estamos abiertos a otras propuestas e ideas. Solo contáctanos. Consulta el Archivo de Anna para más información sobre nuestras colecciones, esfuerzos de preservación y cómo puedes ayudar. ¡Gracias! Páginas de ejemplo Para demostrarnos que tienes un buen pipeline, aquí tienes algunas páginas de ejemplo para comenzar, de un libro sobre superconductores. Tu pipeline debería manejar adecuadamente matemáticas, tablas, gráficos, notas al pie, etc. Envía tus páginas procesadas a nuestro correo electrónico. Si se ven bien, te enviaremos más en privado, y esperamos que puedas ejecutar rápidamente tu pipeline en ellas también. Una vez que estemos satisfechos, podemos llegar a un acuerdo. - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versión en chino 中文版</a>, <a %(news_ycombinator)s>Discutir en Hacker News</a> Esta es una breve publicación de blog. Estamos buscando alguna empresa o institución que nos ayude con OCR y extracción de texto para una colección masiva que adquirimos, a cambio de acceso exclusivo anticipado. Después del período de embargo, por supuesto, liberaremos toda la colección. El texto académico de alta calidad es extremadamente útil para el entrenamiento de LLM. Aunque nuestra colección es en chino, esto debería ser incluso útil para entrenar LLM en inglés: los modelos parecen codificar conceptos y conocimientos independientemente del idioma fuente. Para esto, el texto necesita ser extraído de las digitalizaciones. ¿Qué obtiene el Archivo de Anna de esto? Búsqueda de texto completo de los libros para sus usuarios. Debido a que nuestros objetivos se alinean con los de los desarrolladores de LLM, estamos buscando un colaborador. Estamos dispuestos a darte <strong>acceso anticipado exclusivo a esta colección en bloque por 1 año</strong>, si puedes realizar un OCR y extracción de texto adecuados. Si estás dispuesto a compartir todo el código de tu pipeline con nosotros, estaríamos dispuestos a embargar la colección por más tiempo. Acceso exclusivo para empresas de LLM a la mayor colección de libros de no ficción en chino del mundo <em><strong>Resumen:</strong> El Archivo de Anna adquirió una colección única de 7.5 millones / 350TB de libros de no ficción en chino, más grande que Library Genesis. Estamos dispuestos a dar a una empresa de LLM acceso exclusivo, a cambio de OCR de alta calidad y extracción de texto.</em> Arquitectura del sistema Entonces, digamos que encontraste algunas empresas dispuestas a alojar tu sitio web sin cerrarlo, llamémoslas “proveedores amantes de la libertad” 😄. Rápidamente descubrirás que alojar todo con ellos es bastante caro, por lo que podrías querer encontrar algunos “proveedores económicos” y hacer el alojamiento real allí, a través de los proveedores amantes de la libertad. Si lo haces bien, los proveedores económicos nunca sabrán qué estás alojando y nunca recibirán quejas. Con todos estos proveedores existe el riesgo de que te cierren de todos modos, por lo que también necesitas redundancia. Necesitamos esto en todos los niveles de nuestra pila. Una empresa algo amante de la libertad que se ha puesto en una posición interesante es Cloudflare. Han <a %(blog_cloudflare)s>argumentado</a> que no son un proveedor de alojamiento, sino una utilidad, como un ISP. Por lo tanto, no están sujetos a la DMCA u otras solicitudes de eliminación, y reenvían cualquier solicitud a tu proveedor de alojamiento real. Han llegado tan lejos como para ir a los tribunales para proteger esta estructura. Por lo tanto, podemos usarlos como otra capa de almacenamiento en caché y protección. Cloudflare no acepta pagos anónimos, por lo que solo podemos usar su plan gratuito. Esto significa que no podemos usar sus funciones de balanceo de carga o conmutación por error. Por lo tanto, <a %(annas_archive_l255)s>implementamos esto nosotros mismos</a> a nivel de dominio. Al cargar la página, el navegador verificará si el dominio actual todavía está disponible, y si no, reescribe todas las URL a un dominio diferente. Dado que Cloudflare almacena en caché muchas páginas, esto significa que un usuario puede aterrizar en nuestro dominio principal, incluso si el servidor proxy está caído, y luego en el siguiente clic ser movido a otro dominio. Todavía también tenemos preocupaciones operativas normales con las que lidiar, como monitorear la salud del servidor, registrar errores de backend y frontend, y así sucesivamente. Nuestra arquitectura de conmutación por error permite una mayor robustez en este frente también, por ejemplo, ejecutando un conjunto completamente diferente de servidores en uno de los dominios. Incluso podemos ejecutar versiones anteriores del código y los Datasets en este dominio separado, en caso de que un error crítico en la versión principal pase desapercibido. También podemos protegernos contra Cloudflare volviéndose en nuestra contra, eliminándolo de uno de los dominios, como este dominio separado. Diferentes permutaciones de estas ideas son posibles. Conclusión Ha sido una experiencia interesante aprender a configurar un motor de búsqueda de biblioteca fantasma robusto y resiliente. Hay muchos más detalles para compartir en publicaciones futuras, ¡así que házmelo saber si te gustaría aprender más sobre algo en particular! Como siempre, estamos buscando donaciones para apoyar este trabajo, así que asegúrate de visitar la página de Donaciones en el Archivo de Anna. También estamos buscando otros tipos de apoyo, como subvenciones, patrocinadores a largo plazo, proveedores de pagos de alto riesgo, quizás incluso anuncios (¡de buen gusto!). Y si deseas contribuir con tu tiempo y habilidades, siempre estamos buscando desarrolladores, traductores, etc. Gracias por tu interés y apoyo. Fichas de innovación Comencemos con nuestra pila tecnológica. Es deliberadamente aburrida. Usamos Flask, MariaDB y ElasticSearch. Eso es literalmente todo. La búsqueda es en gran medida un problema resuelto, y no pretendemos reinventarlo. Además, tenemos que gastar nuestras <a %(mcfunley)s>fichas de innovación</a> en otra cosa: no ser derribados por las autoridades. Entonces, ¿qué tan legal o ilegal es exactamente el Archivo de Anna? Esto depende principalmente de la jurisdicción legal. La mayoría de los países creen en alguna forma de derechos de autor, lo que significa que a las personas o empresas se les asigna un monopolio exclusivo sobre ciertos tipos de obras por un cierto período de tiempo. Como un aparte, en el Archivo de Anna creemos que, aunque hay algunos beneficios, en general los derechos de autor son un neto negativo para la sociedad, pero esa es una historia para otro momento. Este monopolio exclusivo sobre ciertas obras significa que es ilegal para cualquiera fuera de este monopolio distribuir directamente esas obras, incluyendo a nosotros. Pero el Archivo de Anna es un motor de búsqueda que no distribuye directamente esas obras (al menos no en nuestro sitio web de la clearnet), así que deberíamos estar bien, ¿verdad? No exactamente. En muchas jurisdicciones no solo es ilegal distribuir obras con derechos de autor, sino también enlazar a lugares que lo hacen. Un ejemplo clásico de esto es la ley DMCA de los Estados Unidos. Ese es el extremo más estricto del espectro. En el otro extremo del espectro, teóricamente podría haber países sin leyes de derechos de autor, pero estos realmente no existen. Prácticamente todos los países tienen alguna forma de ley de derechos de autor en sus libros. La aplicación es otra historia. Hay muchos países con gobiernos que no se preocupan por hacer cumplir la ley de derechos de autor. También hay países entre los dos extremos, que prohíben distribuir obras con derechos de autor, pero no prohíben enlazar a dichas obras. Otra consideración es a nivel de empresa. Si una empresa opera en una jurisdicción que no se preocupa por los derechos de autor, pero la propia empresa no está dispuesta a asumir ningún riesgo, entonces podrían cerrar su sitio web tan pronto como alguien se queje al respecto. Finalmente, una gran consideración son los pagos. Dado que necesitamos permanecer anónimos, no podemos usar métodos de pago tradicionales. Esto nos deja con las criptomonedas, y solo un pequeño subconjunto de empresas las acepta (hay tarjetas de débito virtuales pagadas con criptomonedas, pero a menudo no son aceptadas). - Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dirijo <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, el motor de búsqueda de código abierto sin fines de lucro más grande del mundo para <a %(wikipedia_shadow_library)s>bibliotecas en la sombra</a>, como Sci-Hub, Library Genesis y Z-Library. Nuestro objetivo es hacer que el conocimiento y la cultura sean fácilmente accesibles, y en última instancia, construir una comunidad de personas que juntos archiven y preserven <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos los libros del mundo</a>. En este artículo mostraré cómo operamos este sitio web, y los desafíos únicos que conlleva operar un sitio web con un estatus legal cuestionable, ya que no hay un “AWS para organizaciones benéficas en la sombra”. <em>También consulta el artículo hermano <a %(blog_how_to_become_a_pirate_archivist)s>Cómo convertirse en un archivista pirata</a>.</em> Cómo operar una biblioteca fantasma: operaciones en el Archivo de Anna No hay <q>AWS para organizaciones benéficas en la sombra,</q> entonces, ¿cómo operamos el Archivo de Anna? Herramientas Servidor de aplicaciones: Flask, MariaDB, ElasticSearch, Docker. Desarrollo: Gitlab, Weblate, Zulip. Gestión de servidores: Ansible, Checkmk, UFW. Alojamiento estático de Onion: Tor, Nginx. Servidor proxy: Varnish. Veamos qué herramientas usamos para lograr todo esto. Esto está evolucionando mucho a medida que nos encontramos con nuevos problemas y encontramos nuevas soluciones. Hay algunas decisiones sobre las que hemos ido y venido. Una es la comunicación entre servidores: solíamos usar Wireguard para esto, pero descubrimos que ocasionalmente deja de transmitir datos o solo transmite datos en una dirección. Esto sucedió con varias configuraciones diferentes de Wireguard que probamos, como <a %(github_costela_wesher)s>wesher</a> y <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. También intentamos tunelizar puertos a través de SSH, usando autossh y sshuttle, pero nos encontramos con <a %(github_sshuttle)s>problemas allí</a> (aunque todavía no me queda claro si autossh sufre de problemas de TCP sobre TCP o no; simplemente me parece una solución chapucera, pero ¿quizás está bien?). En su lugar, volvimos a las conexiones directas entre servidores, ocultando que un servidor está funcionando en los proveedores económicos usando filtrado de IP con UFW. Esto tiene la desventaja de que Docker no funciona bien con UFW, a menos que uses <code>network_mode: "host"</code>. Todo esto es un poco más propenso a errores, porque expondrás tu servidor a internet con solo una pequeña mala configuración. Quizás deberíamos volver a autossh; cualquier comentario sería muy bienvenido aquí. También hemos ido y venido entre Varnish y Nginx. Actualmente nos gusta Varnish, pero tiene sus peculiaridades y bordes ásperos. Lo mismo se aplica a Checkmk: no nos encanta, pero funciona por ahora. Weblate ha estado bien, pero no increíble; a veces temo que perderá mis datos cada vez que intento sincronizarlo con nuestro repositorio git. Flask ha sido bueno en general, pero tiene algunas peculiaridades extrañas que han costado mucho tiempo de depuración, como configurar dominios personalizados o problemas con su integración de SqlAlchemy. Hasta ahora, las otras herramientas han sido geniales: no tenemos quejas serias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker y Tor. Todos estos han tenido algunos problemas, pero nada demasiado serio o que consuma mucho tiempo. Comunidad El primer desafío podría ser sorprendente. No es un problema técnico, ni un problema legal. Es un problema psicológico: hacer este trabajo en las sombras puede ser increíblemente solitario. Dependiendo de lo que planees hacer y de tu modelo de amenaza, podrías tener que ser muy cuidadoso. En un extremo del espectro tenemos personas como Alexandra Elbakyan*, la fundadora de Sci-Hub, quien es muy abierta sobre sus actividades. Pero corre un alto riesgo de ser arrestada si visitara un país occidental en este momento, y podría enfrentar décadas de prisión. ¿Es ese un riesgo que estarías dispuesto a asumir? Nosotros estamos en el otro extremo del espectro; siendo muy cuidadosos de no dejar rastro alguno y teniendo una fuerte seguridad operativa. * Como mencionó en HN "ynno", Alexandra inicialmente no quería ser conocida: "Sus servidores estaban configurados para emitir mensajes de error detallados de PHP, incluyendo la ruta completa del archivo fuente con fallos, que estaba bajo el directorio /home/<USER>" Así que, usa nombres de usuario aleatorios en las computadoras que usas para estas cosas, en caso de que configures algo incorrectamente. Sin embargo, ese secreto tiene un costo psicológico. A la mayoría de las personas les encanta ser reconocidas por el trabajo que hacen, y sin embargo, no puedes recibir ningún crédito por esto en la vida real. Incluso las cosas simples pueden ser desafiantes, como cuando los amigos te preguntan en qué has estado trabajando (en algún momento "jugando con mi NAS / homelab" se vuelve repetitivo). Por eso es tan importante encontrar una comunidad. Puedes renunciar a algo de seguridad operativa confiando en algunos amigos muy cercanos, en quienes sabes que puedes confiar profundamente. Incluso entonces, ten cuidado de no poner nada por escrito, en caso de que tengan que entregar sus correos electrónicos a las autoridades, o si sus dispositivos están comprometidos de alguna otra manera. Mejor aún es encontrar algunos compañeros piratas. Si tus amigos cercanos están interesados en unirse a ti, ¡genial! De lo contrario, podrías encontrar a otros en línea. Lamentablemente, esta sigue siendo una comunidad de nicho. Hasta ahora solo hemos encontrado un puñado de otros que están activos en este espacio. Los lugares de inicio parecen ser los foros de Library Genesis y r/DataHoarder. El equipo de Archive Team también tiene individuos con ideas afines, aunque operan dentro de la ley (incluso si en algunas áreas grises de la ley). Las escenas tradicionales de "warez" y piratería también tienen personas que piensan de manera similar. Estamos abiertos a ideas sobre cómo fomentar la comunidad y explorar ideas. No dudes en enviarnos un mensaje en Twitter o Reddit. Quizás podríamos organizar algún tipo de foro o grupo de chat. Un desafío es que esto puede ser fácilmente censurado al usar plataformas comunes, por lo que tendríamos que alojarlo nosotros mismos. También hay un compromiso entre tener estas discusiones completamente públicas (más potencial de participación) versus hacerlas privadas (no dejar que los posibles "objetivos" sepan que estamos a punto de recopilarlos). Tendremos que pensar en eso. ¡Déjanos saber si estás interesado en esto! Conclusión Esperamos que esto sea útil para los archivistas piratas que recién comienzan. Estamos emocionados de darte la bienvenida a este mundo, así que no dudes en comunicarte. Preservemos tanto del conocimiento y la cultura del mundo como podamos, y espéjalo por todas partes. Proyectos 4. Selección de datos A menudo puedes usar la metadata para determinar un subconjunto razonable de datos para descargar. Incluso si eventualmente quieres descargar todos los datos, puede ser útil priorizar los elementos más importantes primero, en caso de que te detecten y mejoren las defensas, o porque necesitarías comprar más discos, o simplemente porque surge algo más en tu vida antes de que puedas descargar todo. Por ejemplo, una colección podría tener múltiples ediciones del mismo recurso subyacente (como un libro o una película), donde una está marcada como de mejor calidad. Guardar esas ediciones primero tendría mucho sentido. Eventualmente podrías querer guardar todas las ediciones, ya que en algunos casos los metadata podrían estar etiquetados incorrectamente, o podría haber compensaciones desconocidas entre ediciones (por ejemplo, la "mejor edición" podría ser la mejor en la mayoría de los aspectos pero peor en otros, como una película con mayor resolución pero sin subtítulos). También puedes buscar en tu base de datos de metadata para encontrar cosas interesantes. ¿Cuál es el archivo más grande que se aloja y por qué es tan grande? ¿Cuál es el archivo más pequeño? ¿Hay patrones interesantes o inesperados en cuanto a ciertas categorías, idiomas, etc.? ¿Hay títulos duplicados o muy similares? ¿Hay patrones sobre cuándo se agregaron los datos, como un día en el que se agregaron muchos archivos a la vez? A menudo puedes aprender mucho observando el conjunto de datos de diferentes maneras. En nuestro caso, deduplicamos los libros de Z-Library contra los hashes md5 en Library Genesis, ahorrando así mucho tiempo de descarga y espacio en disco. Sin embargo, esta es una situación bastante única. En la mayoría de los casos, no hay bases de datos completas de qué archivos ya están debidamente preservados por otros piratas. Esto en sí mismo es una gran oportunidad para alguien por ahí. Sería genial tener una visión general actualizada regularmente de cosas como música y películas que ya están ampliamente compartidas en sitios de torrents, y por lo tanto son de menor prioridad para incluir en espejos piratas. 6. Distribución Tienes los datos, dándote así la posesión del primer espejo pirata del mundo de tu objetivo (muy probablemente). En muchos sentidos, la parte más difícil ha terminado, pero la parte más arriesgada aún está por delante. Después de todo, hasta ahora has sido sigiloso; volando bajo el radar. Todo lo que tenías que hacer era usar un buen VPN en todo momento, no llenar tus datos personales en ningún formulario (obvio), y quizás usar una sesión de navegador especial (o incluso una computadora diferente). Ahora tienes que distribuir los datos. En nuestro caso, primero queríamos contribuir los libros de vuelta a Library Genesis, pero luego descubrimos rápidamente las dificultades en eso (clasificación de ficción vs no ficción). Así que decidimos distribuir usando torrents al estilo de Library Genesis. Si tienes la oportunidad de contribuir a un proyecto existente, eso podría ahorrarte mucho tiempo. Sin embargo, actualmente no hay muchos espejos piratas bien organizados. Así que digamos que decides distribuir torrents tú mismo. Intenta mantener esos archivos pequeños, para que sean fáciles de espejar en otros sitios web. Luego tendrás que sembrar los torrents tú mismo, mientras te mantienes anónimo. Puedes usar una VPN (con o sin reenvío de puertos), o pagar con Bitcoins mezclados por un Seedbox. Si no sabes qué significan algunos de esos términos, tendrás mucho que leer, ya que es importante que entiendas los riesgos aquí. Puedes alojar los archivos torrent en sitios web de torrents existentes. En nuestro caso, elegimos alojar un sitio web, ya que también queríamos difundir nuestra filosofía de manera clara. Puedes hacer esto tú mismo de manera similar (usamos Njalla para nuestros dominios y alojamiento, pagado con Bitcoins mezclados), pero también siéntete libre de contactarnos para que alojemos tus torrents. Estamos buscando construir un índice completo de espejos piratas con el tiempo, si esta idea se populariza. En cuanto a la selección de VPN, ya se ha escrito mucho sobre esto, así que solo repetiremos el consejo general de elegir por reputación. Las políticas de no registro probadas en tribunales con largos historiales de protección de la privacidad son la opción de menor riesgo, en nuestra opinión. Ten en cuenta que incluso cuando haces todo bien, nunca puedes llegar a un riesgo cero. Por ejemplo, al sembrar tus torrents, un actor estatal altamente motivado probablemente pueda observar los flujos de datos entrantes y salientes de los servidores VPN y deducir quién eres. O simplemente puedes cometer un error de alguna manera. Probablemente ya lo hemos hecho, y lo haremos de nuevo. Afortunadamente, a los estados nacionales no les importa <em>tanto</em> la piratería. Una decisión que tomar para cada proyecto es si publicarlo usando la misma identidad que antes o no. Si sigues usando el mismo nombre, los errores en la seguridad operativa de proyectos anteriores podrían volver a afectarte. Pero publicar bajo diferentes nombres significa que no construyes una reputación duradera. Elegimos tener una fuerte seguridad operativa desde el principio para poder seguir usando la misma identidad, pero no dudaremos en publicar bajo un nombre diferente si cometemos un error o si las circunstancias lo requieren. Difundir la palabra puede ser complicado. Como dijimos, esta sigue siendo una comunidad de nicho. Originalmente publicamos en Reddit, pero realmente ganamos tracción en Hacker News. Por ahora, nuestra recomendación es publicarlo en algunos lugares y ver qué pasa. Y nuevamente, contáctanos. Nos encantaría difundir la palabra sobre más esfuerzos de archivismo pirata. 1. Selección de dominio / filosofía No hay escasez de conocimiento y patrimonio cultural por salvar, lo cual puede ser abrumador. Por eso a menudo es útil tomarse un momento y pensar en cuál puede ser tu contribución. Todos tienen una forma diferente de pensar sobre esto, pero aquí hay algunas preguntas que podrías hacerte: En nuestro caso, nos preocupaba en particular la preservación a largo plazo de la ciencia. Sabíamos sobre Library Genesis, y cómo se había espejado completamente muchas veces usando torrents. Nos encantó esa idea. Luego, un día, uno de nosotros intentó encontrar algunos libros de texto científicos en Library Genesis, pero no pudo encontrarlos, poniendo en duda cuán completo realmente era. Luego buscamos esos libros de texto en línea y los encontramos en otros lugares, lo que plantó la semilla para nuestro proyecto. Incluso antes de saber sobre la Z-Library, teníamos la idea de no intentar recopilar todos esos libros manualmente, sino de enfocarnos en espejar colecciones existentes y contribuirlas de nuevo a Library Genesis. ¿Qué habilidades tienes que puedes usar a tu favor? Por ejemplo, si eres un experto en seguridad en línea, puedes encontrar formas de derrotar bloqueos de IP para objetivos seguros. Si eres excelente organizando comunidades, entonces quizás puedas reunir a algunas personas en torno a un objetivo. Sin embargo, es útil saber algo de programación, aunque solo sea para mantener una buena seguridad operativa durante este proceso. ¿Cuál sería un área de alto impacto en la que enfocarse? Si vas a pasar X horas en archivado pirata, entonces ¿cómo puedes obtener el mayor "provecho por tu dinero"? ¿Cuáles son las formas únicas en las que estás pensando sobre esto? Podrías tener algunas ideas o enfoques interesantes que otros podrían haber pasado por alto. ¿Cuánto tiempo tienes para esto? Nuestro consejo sería comenzar pequeño y hacer proyectos más grandes a medida que te acostumbras, pero puede llegar a consumir todo tu tiempo. ¿Por qué te interesa esto? ¿Qué te apasiona? Si podemos reunir a un grupo de personas que archiven los tipos de cosas que les importan específicamente, ¡eso cubriría mucho! Sabrás mucho más que la persona promedio sobre tu pasión, como qué datos son importantes de guardar, cuáles son las mejores colecciones y comunidades en línea, y así sucesivamente. 3. Raspado de metadata Fecha de añadido/modificado: para que puedas volver más tarde y descargar archivos que no descargaste antes (aunque a menudo también puedes usar el ID o hash para esto). Hash (md5, sha1): para confirmar que descargaste el archivo correctamente. ID: puede ser algún ID interno, pero los IDs como ISBN o DOI también son útiles. Nombre de archivo / ubicación Descripción, categoría, etiquetas, autores, idioma, etc. Tamaño: para calcular cuánto espacio en disco necesitas. Vamos a ponernos un poco más técnicos aquí. Para raspar realmente la metadata de los sitios web, hemos mantenido las cosas bastante simples. Usamos scripts de Python, a veces curl, y una base de datos MySQL para almacenar los resultados. No hemos usado ningún software de raspado sofisticado que pueda mapear sitios web complejos, ya que hasta ahora solo necesitábamos raspar uno o dos tipos de páginas simplemente enumerando a través de ids y analizando el HTML. Si no hay páginas fácilmente enumerables, entonces podrías necesitar un rastreador adecuado que intente encontrar todas las páginas. Antes de comenzar a extraer datos de todo un sitio web, intenta hacerlo manualmente por un tiempo. Recorre unas cuantas docenas de páginas tú mismo para entender cómo funciona. A veces, de esta manera, ya te encontrarás con bloqueos de IP u otros comportamientos interesantes. Lo mismo ocurre con la extracción de datos: antes de profundizar demasiado en este objetivo, asegúrate de que realmente puedes descargar sus datos de manera efectiva. Para sortear las restricciones, hay algunas cosas que puedes intentar. ¿Existen otras direcciones IP o servidores que alojen los mismos datos pero no tengan las mismas restricciones? ¿Hay puntos de acceso API que no tengan restricciones, mientras que otros sí? ¿A qué velocidad de descarga se bloquea tu IP y por cuánto tiempo? ¿O no estás bloqueado pero sí ralentizado? ¿Qué sucede si creas una cuenta de usuario, cómo cambian las cosas entonces? ¿Puedes usar HTTP/2 para mantener las conexiones abiertas, y eso aumenta la velocidad a la que puedes solicitar páginas? ¿Hay páginas que enumeran múltiples archivos a la vez, y la información listada allí es suficiente? Las cosas que probablemente quieras guardar incluyen: Normalmente hacemos esto en dos etapas. Primero descargamos los archivos HTML sin procesar, generalmente directamente en MySQL (para evitar muchos archivos pequeños, de lo cual hablamos más abajo). Luego, en un paso separado, revisamos esos archivos HTML y los analizamos en tablas MySQL reales. De esta manera, no tienes que volver a descargar todo desde cero si descubres un error en tu código de análisis, ya que puedes simplemente reprocesar los archivos HTML con el nuevo código. También es a menudo más fácil paralelizar el paso de procesamiento, ahorrando así algo de tiempo (y puedes escribir el código de procesamiento mientras el raspado está en marcha, en lugar de tener que escribir ambos pasos a la vez). Finalmente, ten en cuenta que para algunos objetivos el raspado de metadata es todo lo que hay. Hay algunas colecciones de metadata enormes que no están debidamente preservadas. Título Selección de dominio / filosofía: ¿En qué quieres enfocarte aproximadamente y por qué? ¿Cuáles son tus pasiones, habilidades y circunstancias únicas que puedes usar a tu favor? Selección de objetivo: ¿Qué colección específica vas a espejar? Raspado de metadata: Catalogar información sobre los archivos, sin descargar realmente los archivos (a menudo mucho más grandes) en sí mismos. Selección de datos: Basado en la metadata, determinar qué datos son más relevantes para archivar ahora. Podría ser todo, pero a menudo hay una forma razonable de ahorrar espacio y ancho de banda. Raspado de datos: Obtener realmente los datos. Distribución: Empaquetarlo en torrents, anunciarlo en algún lugar, hacer que la gente lo difunda. 5. Extracción de datos Ahora estás listo para descargar realmente los datos en masa. Como se mencionó antes, en este punto ya deberías haber descargado manualmente un montón de archivos, para entender mejor el comportamiento y las restricciones del objetivo. Sin embargo, todavía habrá sorpresas para ti una vez que realmente comiences a descargar muchos archivos a la vez. Nuestro consejo aquí es principalmente mantenerlo simple. Comienza simplemente descargando un montón de archivos. Puedes usar Python y luego expandir a múltiples hilos. Pero a veces incluso más simple es generar archivos Bash directamente desde la base de datos y luego ejecutar varios de ellos en múltiples ventanas de terminal para escalar. Un truco técnico rápido que vale la pena mencionar aquí es usar OUTFILE en MySQL, que puedes escribir en cualquier lugar si desactivas "secure_file_priv" en mysqld.cnf (y asegúrate de también desactivar/invalidar AppArmor si estás en Linux). Almacenamos los datos en discos duros simples. Comienza con lo que tengas y expande lentamente. Puede ser abrumador pensar en almacenar cientos de TBs de datos. Si esa es la situación que enfrentas, simplemente publica un buen subconjunto primero, y en tu anuncio pide ayuda para almacenar el resto. Si deseas obtener más discos duros tú mismo, entonces r/DataHoarder tiene algunos buenos recursos para conseguir buenas ofertas. Trata de no preocuparte demasiado por sistemas de archivos sofisticados. Es fácil caer en el agujero del conejo de configurar cosas como ZFS. Un detalle técnico a tener en cuenta, sin embargo, es que muchos sistemas de archivos no manejan bien muchos archivos. Hemos encontrado que una solución simple es crear múltiples directorios, por ejemplo, para diferentes rangos de ID o prefijos de hash. Después de descargar los datos, asegúrate de verificar la integridad de los archivos usando hashes en la metadata, si están disponibles. 2. Selección de objetivo Accesible: no utiliza toneladas de capas de protección para evitar que rasques su metadata y datos. Perspectiva especial: tienes alguna información especial sobre este objetivo, como que de alguna manera tienes acceso especial a esta colección, o descubriste cómo vencer sus defensas. Esto no es necesario (nuestro próximo proyecto no hace nada especial), ¡pero ciertamente ayuda! Grande Entonces, tenemos nuestra área que estamos observando, ahora ¿qué colección específica espejamos? Hay un par de cosas que hacen un buen objetivo: Cuando encontramos nuestros libros de texto de ciencia en sitios web distintos a Library Genesis, intentamos averiguar cómo llegaron a internet. Luego encontramos Z-Library, y nos dimos cuenta de que, aunque la mayoría de los libros no aparecen primero allí, eventualmente terminan allí. Aprendimos sobre su relación con Library Genesis, y la estructura de incentivos (financieros) y la interfaz de usuario superior, ambas hicieron que fuera una colección mucho más completa. Luego hicimos un raspado preliminar de metadata y datos, y nos dimos cuenta de que podíamos sortear sus límites de descarga de IP, aprovechando el acceso especial de uno de nuestros miembros a muchos servidores proxy. Mientras exploras diferentes objetivos, ya es importante ocultar tus huellas usando VPNs y direcciones de correo electrónico desechables, de lo cual hablaremos más adelante. Única: no ya bien cubierta por otros proyectos. Cuando realizamos un proyecto, tiene un par de fases: Estas no son fases completamente independientes, y a menudo las ideas de una fase posterior te envían de regreso a una fase anterior. Por ejemplo, durante el raspado de metadata podrías darte cuenta de que el objetivo que seleccionaste tiene mecanismos defensivos más allá de tu nivel de habilidad (como bloqueos de IP), por lo que vuelves atrás y encuentras un objetivo diferente. - Anna y el equipo (<a %(reddit)s>Reddit</a>) Se pueden escribir libros enteros sobre el <em>por qué</em> de la preservación digital en general, y el archivismo pirata en particular, pero déjenos dar una breve introducción para aquellos que no están muy familiarizados. El mundo está produciendo más conocimiento y cultura que nunca antes, pero también se está perdiendo más que nunca. La humanidad confía en gran medida en corporaciones como editoriales académicas, servicios de streaming y empresas de redes sociales para este patrimonio, y a menudo no han demostrado ser grandes guardianes. Echa un vistazo al documental Digital Amnesia, o realmente a cualquier charla de Jason Scott. Hay algunas instituciones que hacen un buen trabajo archivando tanto como pueden, pero están limitadas por la ley. Como piratas, estamos en una posición única para archivar colecciones que ellos no pueden tocar, debido a la aplicación de derechos de autor u otras restricciones. También podemos reflejar colecciones muchas veces, en todo el mundo, aumentando así las posibilidades de una preservación adecuada. Por ahora, no entraremos en discusiones sobre los pros y los contras de la propiedad intelectual, la moralidad de romper la ley, reflexiones sobre la censura o el tema del acceso al conocimiento y la cultura. Con todo eso fuera del camino, vamos a sumergirnos en el <em>cómo</em>. Compartiremos cómo nuestro equipo se convirtió en archivistas piratas y las lecciones que aprendimos en el camino. Hay muchos desafíos cuando emprendes este viaje, y esperamos poder ayudarte con algunos de ellos. Cómo convertirse en un archivista pirata El primer desafío podría ser sorprendente. No es un problema técnico ni un problema legal. Es un problema psicológico. Antes de sumergirnos, dos actualizaciones sobre el Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>): Recibimos algunas donaciones extremadamente generosas. La primera fue de $10,000 de un individuo anónimo que también ha estado apoyando a "bookwarrior", el fundador original de Library Genesis. Un agradecimiento especial a bookwarrior por facilitar esta donación. La segunda fue otra de $10,000 de un donante anónimo, que se puso en contacto después de nuestro último lanzamiento y se inspiró para ayudar. También tuvimos una serie de donaciones más pequeñas. Muchas gracias por todo su generoso apoyo. Tenemos algunos proyectos nuevos emocionantes en proceso que esto apoyará, así que estén atentos. Tuvimos algunas dificultades técnicas con el tamaño de nuestro segundo lanzamiento, pero nuestros torrents están activos y sembrando ahora. También recibimos una oferta generosa de un individuo anónimo para sembrar nuestra colección en sus servidores de muy alta velocidad, por lo que estamos haciendo una carga especial a sus máquinas, después de lo cual todos los demás que estén descargando la colección deberían ver una gran mejora en la velocidad. Entradas del blog Hola, soy Anna. Creé <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, la biblioteca fantasma más grande del mundo. Este es mi blog personal, en el que mis compañeros de equipo y yo escribimos sobre piratería, preservación digital y más. Conéctate conmigo en <a %(reddit)s>Reddit</a>. Ten en cuenta que este sitio web es solo un blog. Solo alojamos nuestras propias palabras aquí. No se alojan ni se enlazan torrents u otros archivos con derechos de autor aquí. <strong>Biblioteca</strong> - Como la mayoría de las bibliotecas, nos enfocamos principalmente en materiales escritos como libros. Podríamos expandirnos a otros tipos de medios en el futuro. <strong>Espejo</strong> - Somos estrictamente un espejo de bibliotecas existentes. Nos enfocamos en la preservación, no en hacer que los libros sean fácilmente buscables y descargables (acceso) o en fomentar una gran comunidad de personas que contribuyan con nuevos libros (fuente). <strong>Pirata</strong> - Deliberadamente violamos la ley de derechos de autor en la mayoría de los países. Esto nos permite hacer algo que las entidades legales no pueden hacer: asegurarnos de que los libros se reflejen ampliamente. <em>No enlazamos a los archivos desde este blog. Por favor, encuéntrelos usted mismo.</em> - Anna y el equipo (<a %(reddit)s>Reddit</a>) Este proyecto (EDITADO: trasladado a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>) tiene como objetivo contribuir a la preservación y liberación del conocimiento humano. Hacemos nuestra pequeña y humilde contribución, siguiendo los pasos de los grandes que nos precedieron. El enfoque de este proyecto se ilustra con su nombre: La primera biblioteca que hemos reflejado es Z-Library. Esta es una biblioteca popular (e ilegal). Han tomado la colección de Library Genesis y la han hecho fácilmente buscable. Además, se han vuelto muy efectivos en solicitar nuevas contribuciones de libros, incentivando a los usuarios contribuyentes con varios beneficios. Actualmente no contribuyen con estos nuevos libros de vuelta a Library Genesis. Y a diferencia de Library Genesis, no hacen que su colección sea fácilmente reflejable, lo que impide una amplia preservación. Esto es importante para su modelo de negocio, ya que cobran dinero por acceder a su colección en masa (más de 10 libros por día). No hacemos juicios morales sobre cobrar dinero por el acceso masivo a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada. Nos gustaría invitarle a ayudar a preservar y liberar el conocimiento humano descargando y compartiendo nuestros torrents. Consulte la página del proyecto para obtener más información sobre cómo están organizados los datos. También nos gustaría mucho invitarle a contribuir con sus ideas sobre qué colecciones reflejar a continuación y cómo hacerlo. Juntos podemos lograr mucho. Esta es solo una pequeña contribución entre muchas otras. Gracias, por todo lo que hace. Presentamos el Espejo de la Biblioteca Pirata: Preservando 7TB de libros (que no están en Libgen) 10%% of del patrimonio escrito de la humanidad preservado para siempre <strong>Google.</strong> Después de todo, hicieron esta investigación para Google Books. Sin embargo, su metadata no es accesible en masa y es bastante difícil de extraer. <strong>Varios sistemas de bibliotecas y archivos individuales.</strong> Hay bibliotecas y archivos que no han sido indexados y agregados por ninguno de los anteriores, a menudo porque están subfinanciados, o por otras razones no quieren compartir sus datos con Open Library, OCLC, Google, etc. Muchas de estas tienen registros digitales accesibles a través de internet, y a menudo no están muy bien protegidos, por lo que si quieres ayudar y divertirte aprendiendo sobre sistemas de bibliotecas extraños, estos son excelentes puntos de partida. <strong>ISBNdb.</strong> Este es el tema de esta publicación en el blog. ISBNdb extrae datos de varios sitios web para metadata de libros, en particular datos de precios, que luego venden a libreros, para que puedan fijar el precio de sus libros de acuerdo con el resto del mercado. Dado que los ISBN son bastante universales hoy en día, efectivamente construyeron una “página web para cada libro”. <strong>Open Library.</strong> Como se mencionó antes, esta es su misión completa. Han obtenido enormes cantidades de datos de bibliotecas de bibliotecas cooperantes y archivos nacionales, y continúan haciéndolo. También tienen bibliotecarios voluntarios y un equipo técnico que están tratando de deduplicar registros y etiquetarlos con todo tipo de metadata. Lo mejor de todo es que su conjunto de datos es completamente abierto. Puedes simplemente <a %(openlibrary)s>descargarlo</a>. <strong>WorldCat.</strong> Este es un sitio web administrado por la organización sin fines de lucro OCLC, que vende sistemas de gestión de bibliotecas. Agregan metadata de libros de muchas bibliotecas y la ponen a disposición a través del sitio web de WorldCat. Sin embargo, también ganan dinero vendiendo estos datos, por lo que no están disponibles para descarga masiva. Tienen algunos conjuntos de datos masivos más limitados disponibles para descargar, en cooperación con bibliotecas específicas. 1. Para alguna definición razonable de "para siempre". ;) 2. Por supuesto, el patrimonio escrito de la humanidad es mucho más que libros, especialmente hoy en día. Para el propósito de esta publicación y nuestros lanzamientos recientes, nos estamos enfocando en libros, pero nuestros intereses se extienden más allá. 3. Hay mucho más que se puede decir sobre Aaron Swartz, pero solo queríamos mencionarlo brevemente, ya que juega un papel fundamental en esta historia. A medida que pasa el tiempo, más personas podrían encontrarse con su nombre por primera vez, y posteriormente sumergirse en el tema por sí mismas. <strong>Copias físicas.</strong> Obviamente, esto no es muy útil, ya que son solo duplicados del mismo material. Sería genial si pudiéramos preservar todas las anotaciones que la gente hace en los libros, como los famosos "garabatos en los márgenes" de Fermat. Pero, por desgracia, eso seguirá siendo un sueño de archivista. <strong>“Ediciones”.</strong> Aquí cuentas cada versión única de un libro. Si algo es diferente, como una portada distinta o un prólogo diferente, cuenta como una edición diferente. <strong>Archivos.</strong> Al trabajar con bibliotecas en la sombra como Library Genesis, Sci-Hub o Z-Library, hay una consideración adicional. Puede haber múltiples escaneos de la misma edición. Y las personas pueden crear mejores versiones de archivos existentes, escaneando el texto usando OCR o corrigiendo páginas que fueron escaneadas en ángulo. Queremos contar estos archivos solo como una edición, lo que requeriría buenos metadata o deduplicación usando medidas de similitud de documentos. <strong>“Obras”.</strong> Por ejemplo, “Harry Potter y la Cámara Secreta” como un concepto lógico, que abarca todas sus versiones, como diferentes traducciones y reimpresiones. Esta es una definición algo útil, pero puede ser difícil trazar la línea de lo que cuenta. Por ejemplo, probablemente queramos preservar diferentes traducciones, aunque las reimpresiones con solo diferencias menores podrían no ser tan importantes. - Anna y el equipo (<a %(reddit)s>Reddit</a>) Con el Espejo de la Biblioteca Pirata (EDITADO: trasladado a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>), nuestro objetivo es tomar todos los libros del mundo y preservarlos para siempre.<sup>1</sup> Entre nuestros torrents de Z-Library y los torrents originales de Library Genesis, tenemos 11,783,153 archivos. Pero, ¿cuántos son realmente? Si deduplicáramos adecuadamente esos archivos, ¿qué porcentaje de todos los libros del mundo hemos preservado? Realmente nos gustaría tener algo como esto: Comencemos con algunos números aproximados: En tanto Z-Library/Libgen como Open Library hay muchos más libros que ISBN únicos. ¿Significa eso que muchos de esos libros no tienen ISBN, o simplemente falta el metadata del ISBN? Probablemente podamos responder a esta pregunta con una combinación de coincidencia automatizada basada en otros atributos (título, autor, editor, etc.), incorporando más fuentes de datos y extrayendo ISBN de los escaneos reales de los libros (en el caso de Z-Library/Libgen). ¿Cuántos de esos ISBN son únicos? Esto se ilustra mejor con un diagrama de Venn: Para ser más precisos: ¡Nos sorprendió lo poco que se superponen! ISBNdb tiene una gran cantidad de ISBN que no aparecen ni en Z-Library ni en Open Library, y lo mismo ocurre (en menor grado pero aún sustancial) con los otros dos. Esto plantea muchas nuevas preguntas. ¿Cuánto ayudaría la coincidencia automatizada en etiquetar los libros que no fueron etiquetados con ISBN? ¿Habría muchas coincidencias y, por lo tanto, un aumento en la superposición? Además, ¿qué pasaría si incorporamos un cuarto o quinto conjunto de datos? ¿Cuánta superposición veríamos entonces? Esto nos da un punto de partida. Ahora podemos mirar todos los ISBN que no estaban en el conjunto de datos de Z-Library, y que tampoco coinciden con los campos de título/autor. Eso puede darnos una idea de cómo preservar todos los libros del mundo: primero extrayendo de internet los escaneos, luego saliendo en la vida real para escanear libros. Esto último incluso podría ser financiado por la multitud, o impulsado por "recompensas" de personas que les gustaría ver ciertos libros digitalizados. Todo eso es una historia para otro momento. Si deseas ayudar con cualquiera de estas tareas — análisis adicional; recopilación de más metadata; búsqueda de más libros; OCR de libros; hacer esto para otros dominios (por ejemplo, artículos, audiolibros, películas, programas de televisión, revistas) o incluso hacer que algunos de estos datos estén disponibles para cosas como el entrenamiento de modelos de lenguaje grande/ML — por favor contáctame (<a %(reddit)s>Reddit</a>). Si estás específicamente interesado en el análisis de datos, estamos trabajando para hacer que nuestros Datasets y scripts estén disponibles en un formato más fácil de usar. Sería genial si pudieras simplemente bifurcar un cuaderno y comenzar a experimentar con esto. Finalmente, si deseas apoyar este trabajo, por favor considera hacer una donación. Esta es una operación llevada a cabo completamente por voluntarios, y tu contribución marca una gran diferencia. Cada aporte cuenta. Por ahora aceptamos donaciones en criptomonedas; consulta la página de Donaciones en el Archivo de Anna. Para un porcentaje, necesitamos un denominador: el número total de libros publicados.<sup>2</sup> Antes de la desaparición de Google Books, un ingeniero del proyecto, Leonid Taycher, <a %(booksearch_blogspot)s>intentó estimar</a> este número. Llegó — en tono de broma — a 129,864,880 (“al menos hasta el domingo”). Estimó este número construyendo una base de datos unificada de todos los libros del mundo. Para esto, reunió diferentes conjuntos de datos y luego los fusionó de varias maneras. Como un breve paréntesis, hay otra persona que intentó catalogar todos los libros del mundo: Aaron Swartz, el difunto activista digital y cofundador de Reddit.<sup>3</sup> Él <a %(youtube)s>inició Open Library</a> con el objetivo de “una página web para cada libro publicado”, combinando datos de muchas fuentes diferentes. Terminó pagando el precio más alto por su trabajo de preservación digital cuando fue procesado por descargar en masa artículos académicos, lo que llevó a su suicidio. No hace falta decir que esta es una de las razones por las que nuestro grupo es seudónimo y por la que estamos siendo muy cuidadosos. Open Library sigue siendo heroicamente gestionada por personas en el Internet Archive, continuando el legado de Aaron. Volveremos a esto más adelante en esta publicación. En la publicación del blog de Google, Taycher describe algunos de los desafíos al estimar este número. Primero, ¿qué constituye un libro? Hay algunas definiciones posibles: Las “Ediciones” parecen la definición más práctica de lo que son los “libros”. Convenientemente, esta definición también se utiliza para asignar números ISBN únicos. Un ISBN, o Número Estándar Internacional de Libros, se utiliza comúnmente para el comercio internacional, ya que está integrado con el sistema internacional de códigos de barras (“Número Internacional de Artículo”). Si quieres vender un libro en tiendas, necesita un código de barras, por lo que obtienes un ISBN. La publicación en el blog de Taycher menciona que, aunque los ISBN son útiles, no son universales, ya que solo se adoptaron realmente a mediados de los setenta, y no en todo el mundo. Aun así, el ISBN es probablemente el identificador más utilizado de ediciones de libros, por lo que es nuestro mejor punto de partida. Si podemos encontrar todos los ISBN del mundo, obtenemos una lista útil de qué libros aún necesitan ser preservados. Entonces, ¿de dónde obtenemos los datos? Hay varios esfuerzos existentes que están tratando de compilar una lista de todos los libros del mundo: En esta publicación, nos complace anunciar un pequeño lanzamiento (en comparación con nuestros lanzamientos anteriores de Z-Library). Hemos extraído la mayor parte de ISBNdb y hemos puesto los datos a disposición para torrenting en el sitio web del Espejo de la Biblioteca Pirata (EDITADO: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>; no lo enlazaremos directamente aquí, solo búsquelo). Estos son alrededor de 30.9 millones de registros (20GB como <a %(jsonlines)s>Líneas JSON</a>; 4.4GB comprimidos). En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que podríamos haber pasado por alto algunos, o <em>ellos</em> podrían estar haciendo algo mal. En cualquier caso, por ahora no compartiremos exactamente cómo lo hicimos; dejaremos eso como un ejercicio para el lector. ;-) Lo que compartiremos es un análisis preliminar, para tratar de acercarnos a estimar el número de libros en el mundo. Observamos tres conjuntos de datos: este nuevo conjunto de datos de ISBNdb, nuestra publicación original de metadata que extraímos de la biblioteca en la sombra Z-Library (que incluye Library Genesis), y el volcado de datos de Open Library. Volcado de ISBNdb, o ¿Cuántos libros se preservan para siempre? Si deduplicáramos adecuadamente los archivos de las bibliotecas en la sombra, ¿qué porcentaje de todos los libros del mundo hemos preservado? Actualizaciones sobre <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, la biblioteca verdaderamente abierta más grande en la historia de la humanidad. <em>Rediseño de WorldCat</em> Datos <strong>¿Formato?</strong> <a %(blog)s>Contenedores de Archivo de Anna (AAC)</a>, que es esencialmente <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>, además de algunas semánticas estandarizadas. Estos contenedores envuelven varios tipos de registros, basados en los diferentes raspados que implementamos. Hace un año, <a %(blog)s>nos propusimos</a> responder a esta pregunta: <strong>¿Qué porcentaje de libros han sido preservados permanentemente por bibliotecas en la sombra?</strong> Veamos alguna información básica sobre los datos: Una vez que un libro entra en una biblioteca fantasma de datos abiertos como <a %(wikipedia_library_genesis)s>Library Genesis</a>, y ahora <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, se replica por todo el mundo (a través de torrents), preservándolo prácticamente para siempre. Para responder a la pregunta de qué porcentaje de libros se ha preservado, necesitamos conocer el denominador: ¿cuántos libros existen en total? Y, idealmente, no solo tener un número, sino metadatos reales. Entonces no solo podemos compararlos con bibliotecas en la sombra, sino también <strong>crear una lista de tareas pendientes de los libros restantes por preservar!</strong> Incluso podríamos empezar a soñar con un esfuerzo colaborativo para completar esta lista de tareas pendientes. Raspamos <a %(wikipedia_isbndb_com)s>ISBNdb</a> y descargamos el <a %(openlibrary)s>conjunto de datos de Open Library</a>, pero los resultados fueron insatisfactorios. El principal problema fue que no había mucha superposición de ISBNs. Vea este diagrama de Venn de <a %(blog)s>nuestra publicación en el blog</a>: Nos sorprendió mucho lo poco que se superponían ISBNdb y Open Library, ambos de los cuales incluyen datos de diversas fuentes, como raspados web y registros de bibliotecas. Si ambos hacen un buen trabajo al encontrar la mayoría de los ISBNs, sus círculos seguramente tendrían una superposición sustancial, o uno sería un subconjunto del otro. Nos hizo preguntarnos, ¿cuántos libros quedan <em>completamente fuera de estos círculos</em>? Necesitamos una base de datos más grande. Fue entonces cuando fijamos nuestra mirada en la base de datos de libros más grande del mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta es una base de datos propietaria de la organización sin fines de lucro <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliotecas de todo el mundo, a cambio de dar a esas bibliotecas acceso al conjunto completo de datos y hacer que aparezcan en los resultados de búsqueda de los usuarios finales. Aunque OCLC es una organización sin fines de lucro, su modelo de negocio requiere proteger su base de datos. Bueno, lamentamos decirlo, amigos de OCLC, lo estamos regalando todo. :-) Durante el último año, hemos raspado meticulosamente todos los registros de WorldCat. Al principio, tuvimos un golpe de suerte. WorldCat estaba lanzando su rediseño completo del sitio web (en agosto de 2022). Esto incluyó una revisión sustancial de sus sistemas de backend, introduciendo muchas fallas de seguridad. Inmediatamente aprovechamos la oportunidad y pudimos raspar cientos de millones (!) de registros en pocos días. Después de eso, las fallas de seguridad se fueron corrigiendo una por una, hasta que la última que encontramos fue parcheada hace aproximadamente un mes. Para ese momento ya teníamos prácticamente todos los registros, y solo buscábamos registros de calidad ligeramente superior. Así que sentimos que es hora de lanzar! 1.3B extracción de WorldCat <em><strong>Resumen:</strong> El Archivo de Anna extrajo todo WorldCat (la colección de metadata de bibliotecas más grande del mundo) para hacer una lista de tareas pendientes de libros que necesitan ser preservados.</em> WorldCat Advertencia: esta publicación de blog ha sido descontinuada. Hemos decidido que IPFS aún no está listo para el horario estelar. Aún enlazaremos a archivos en IPFS desde el Archivo de Anna cuando sea posible, pero ya no lo alojaremos nosotros mismos, ni recomendamos a otros que lo espejen usando IPFS. Por favor, consulta nuestra página de Torrents si deseas ayudar a preservar nuestra colección. Ayuda a sembrar Z-Library en IPFS Descarga del servidor asociado SciDB Préstamo externo Préstamo externo (impresión deshabilitada) Descarga externa Explorar metadatos Contenido dentro de torrents Atrás  (+%(num)s adicionales) sin pagar pagado cancelado expiró Esperando confirmación de Anna inválido El texto siguiente continúa en inglés. Ir Restablecer Adelante Último Si tu correo electrónico no funciona en los foros de Libgen, recomendamos usar <a %(a_mail)s>Proton Mail</a> (gratis). También puedes <a %(a_manual)s>solicitar manualmente</a> que tu cuenta se active. (podría requerir <a %(a_browser)s>verificación del navegador</a> - ¡descargas ilimitadas!) Servidor Asociado Rápido #%(number)s (recomendado) (ligeramente más rápido pero con lista de espera) (no se requiere verificación del navegador) (sin verificación del navegador ni listas de espera) (sin lista de espera, pero puede ser muy lento) Servidor Asociado Lento #%(number)s Audiolibro Cómic Libro (ficción) Libro (no ficción) Libro (desconocido) Artículo periodístico Revista Partitura musical Otro Documentos estándar No todas las páginas pueden ser convertidas a PDF Marcado roto en Libgen.li No visible en Libgen.li No visible en Libgen.rs Ficción No visible en Libgen.rs Non-Fiction La ejecución de exiftool falló en este archivo Marcado como “archivo malo” en Z-Library Falta en Z-Library Marcado como “spam” en Z-Library El archivo no se puede abrir (p. ej. archivo dañado, DRM) Reclamación de derechos de autor Problemas de descarga (p. ej. no se puede conectar, mensaje de error, muy lento) Metadatos incorrectos (p. ej. título, descripción, imagen de portada) Otro Mala calidad (p. ej. problemas de formato, mala calidad de escaneo, páginas faltantes) Spam / se debe eliminar el archivo (p. ej. publicidad, contenido abusivo) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Ratón de Biblioteca Brillante Bibliotecario Afortunado Coleccionista de Datos Deslumbrante Archivista increíble Descargas adicionales Cerlalc Metadatos checos DuXiu 读秀 Índice de eBooks de EBSCOhost Google Libros Goodreads HathiTrust IA Préstamo Digital Controlado de IA ISBNdb ISBN GRP Libgen.li Excluyendo “scimag” Libgen.rs No Ficción y Ficción Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Biblioteca Estatal Rusa Sci-Hub A través de Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Cargas a AA Z-Library Z-Library Chino Título, autor, DOI, ISBN, MD5, … Buscar Autor Descripción y comentarios de metadatos Edición Nombre de archivo original Editor (buscar campo específico) Título Año publicado Detalles técnicos Esta moneda tiene un mínimo más alto de lo habitual. Selecciona una duración o una moneda diferente. No se pudo completar tu petición. Vuelve a intentarlo en unos minutos, si sigue sucediendo contáctanos a %(email)s con una captura de pantalla. Un error desconocido ocurrió. Por favor contáctanos en %(email)s con una captura de pantalla. Error en el procesamiento del pago. Por favor espera un momento y vuelve a intentarlo. Si el problema continúa por más de 24 horas, por favor contáctanos a %(email)s con una captura de pantalla. Estamos llevando a cabo una recaudación de fondos para <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">respaldar</a> la biblioteca fantasma de cómics más grande del mundo. ¡Gracias por tu apoyo! <a href="/donate">Donar.</a> Si no puedes donar, considera apoyarnos contándoselo a tus amigos y siguiéndonos en <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> o <a href="https://t.me/annasarchiveorg">Telegram</a>. No nos envíe correos electrónicos para <a %(a_request)s>solicitar libros</a> o <a %(a_upload)s>cargas</a> pequeñas (<10k). El Archivo de Anna DMCA / reclamaciones de derechos de autor Mantente en contacto Reddit Alternativas SLUM (%(unaffiliated)s) no afiliado ¡El Archivo de Anna necesita tu ayuda! Si dona ahora, conseguirá<strong>el doble</strong> de descargas rápidas. Muchos intentan derribarnos, pero nosotros luchamos. Si donas este mes, obtienes <strong>el doble</strong> de descargas rápidas. Válido hasta el final de este mes. Salvar el conocimiento humano: ¡Un gran regalo de vacaciones! Las membresías se extenderán en consecuencia. Los servidores asociados no están disponibles debido al cierre de los servicios de alojamiento. Deberían estar operativos nuevamente pronto. Para aumentar la resiliencia de Archivo de Anna, estamos buscando voluntarios para ejecutar espejos. Tenemos un nuevo método de donación disponible: %(method_name)s. Por favor considera %(donate_link_open_tag)sdonar</a> — no es barato mantener este sitio web y tu donación realmente marca la diferencia. Muchas gracias. Refiere a un amigo, ¡y ambos obtenéis %(percentage)s%% descargas rápidas adicionales! Sorprende a un ser querido, regálale una cuenta con membresía. ¡El regalo de San Valentín perfecto! Saber más… Cuenta Actividad Avanzado El Blog de Anna ↗ El Software de Anna ↗ beta Explorador de Códigos Datasets Donar Archivos descargados FAQ Inicio Mejorar metadatos Datos de LLM Iniciar sesión / Registrarse Mis donaciones Perfil público Buscar Seguridad Torrents Traducir ↗ Voluntariado y Recompensas Descargas recientes: 📚&nbsp;La biblioteca de código abierto y datos abiertos más grande del mundo. ⭐️&nbsp;Refleja Sci-Hub, Library Genesis, Z-Library, y más. 📈&nbsp;%(book_any)s libros, %(journal_article)s artículos, %(book_comic)s cómics, %(magazine)s revistas — preservados para siempre.  y  y más DuXiu Biblioteca de Préstamos de Internet Archive LibGen 📚&nbsp; La biblioteca verdaderamente abierta más grande de la historia de la humanidad. 📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;artículos— preservados para siempre. ⭐️&nbsp;Reflejamos %(libraries)s. Recopilamos y publicamos de forma abierta %(scraped)s. Todo nuestro código y datos son completamente de código abierto. OpenLib Sci-Hub ,  📚 La biblioteca de datos y código abierto más grande del mundo.<br>⭐️ Refleja Scihub, Libgen, Zlib y más. Z-Lib El Archivo de Anna Solicitud no válida. Visita %(websites)s. La biblioteca de datos abiertos y código abierto más grande del mundo. Refleja Sci-Hub, Library Genesis, Z-Library, y más. Buscar en el Archivo de Anna El Archivo de Anna Por favor, actualice para intentarlo de nuevo. <a %(a_contact)s>Contáctenos</a> si el problema persiste durante varias horas. 🔥 Problema al cargar esta página <li>1. Síguenos en <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Haz correr la voz sobre el Archivo de Anna en Twitter, Reddit, Tiktok, Instagram, en tu cafetería o biblioteca local, ¡o donde sea que vayas! No creemos en el control de acceso: si nos eliminan, simplemente apareceremos en otro lugar, ya que todo nuestro código y datos son de código abierto.</li><li>3. Si puedes, considera <a href="/donate">donar</a>.</li><li>4. Ayuda a <a href="https://translate.annas-software.org/">traducir</a> nuestro sitio web a diferentes idiomas.</li><li>5. Si eres ingeniero de software, considera contribuir a nuestro <a href="https://annas-software.org/">código abierto</a> o sembrar nuestros <a href="/datasets">torrents</a>.</li> 10. Crea o ayuda a mantener nuestra pagina de Wikipedia del Archivo de Anna en tu idioma. 11. Buscamos poner pequeños anuncios de buen gusto, si quieres anunciarte en el Archivo de Anna, por favor háznoslo saber. 6. Si eres un investigador de seguridad, podemos usar tus habilidades tanto para el ataque como la defensa. Visita nuestra página de <a %(a_security)s>Seguridad</a>. 7. Buscamos expertos en pagos para comerciantes anónimos. Nos ayudas a añadir mas formas convenientes para donar? PayPal, WeChat, tarjetas de regalo. Si conoces a alguien, por favor contáctanos. 8. Siempre estamos buscando más capacidad para nuestro servidor. 9. Puedes ayudar reportando problemas con archivos, dejando comentarios y creando listas en este mismo sitio. También puedes ayudarnos <a %(a_upload)s> subiendo más libros</a> o reparando problemas de archivos o dando formato a libros existentes. Para obtener información más detallada sobre cómo ser voluntario, consulte nuestra página de <a %(a_volunteering)s>Voluntariado y Recompensas</a>. Creemos firmemente en el libre flujo de la información y la preservación del conocimiento y la cultura. Con este motor de búsqueda nos apoyamos a hombros de gigantes. Respetamos profundamente el arduo trabajo de las personas que han creado las distintas bibliotecas fantasma y esperamos que este motor de búsqueda amplíe su alcance. Para mantenerte informado de nuestra evolución, sigue a Anna en <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> (paginas en inglés) o en <a href="https://t.me/annasarchiveorg"> Telegram</a> . Para preguntas y feedback contacta con Anna en %(email)s. ID de cuenta: %(account_id)s Cerrar sesión ❌ Algo salió mal. Vuelve a cargar la página y prueba otra vez. ✅ Tu sesión ya está cerrada. Vuelve a cargar la página para iniciar sesión de nuevo. Descargas rápidas usadas (últimas 24 horas): <strong>%(used)s / %(total)s</strong> Membresía: <strong>%(tier_name)s</strong> hasta %(until_date)s <a %(a_extend)s>(extender)</a> Puedes combinar varias membresías (Las descargas rápidas cada 24 horas se sumarán). Membresía: <strong>Ninguna</strong> <a %(a_become)s>(hacerse miembro)</a> Contacta con Anna en %(email)s si estás interesado en mejorar tu membresía a un nivel superior. Perfil público: %(profile_link)s Clave secreta (¡no la compartas!): %(secret_key)s mostrar Únete aquí! Mejora a un <a %(a_tier)s>nivel superior</a> para unirte a nuestro grupo. Grupo de Telegram exclusivo: %(link)s Cuenta ¿Qué descargas? Iniciar sesión ¡No pierdas tu clave! Clave secreta no válida. Verifica tu clave e inténtalo de nuevo, o registra una nueva cuenta. Clave secreta Introduce tu clave secreta para iniciar sesión: ¿Tienes una cuenta basada en un correo electrónico viejo? Introduce tu <a %(a_open)s>correo electrónico aquí</a>. Registrar una cuenta nueva ¿Todavía no tienes una cuenta? ¡Registro exitoso! Tu clave secreta es: <span %(span_key)s>%(key)s</span> Guarda esta clave con cuidado. Si la pierdes, perderás acceso a tu cuenta. <li %(li_item)s><strong>Marcador.</strong> Puedes añadir esta página a los marcadores para recuperar tu clave.</li><li %(li_item)s><strong>Descargar.</strong> Haz clic en <a %(a_download)s>este enlace</a> para descargar tu clave.</li><li %(li_item)s><strong>Gestor de contraseñas.</strong> Utiliza un gestor de contraseñas para guardar la clave cuando la introduzcas debajo.</li> Iniciar sesión / Registrarse Verificación del navegador Advertencia: el código tiene caracteres Unicode incorrectos y puede comportarse incorrectamente en varias situaciones. El binario en bruto se puede decodificar de la representación base64 en la URL. Descripción Etiqueta Prefijo URL para un código específico Página web Códigos que comienzan con <q>%(prefix_label)s</q> Por favor, no raspe estas páginas. En su lugar, recomendamos <a %(a_import)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos ElasticSearch y MariaDB, y ejecutar nuestro <a %(a_software)s>código abierto</a>. Los datos en bruto se pueden explorar manualmente a través de archivos JSON como <a %(a_json_file)s>este</a>. Menos de %(count)s registros URL genérica Explorador de Códigos Índice de Explore los códigos con los que se etiquetan los registros, por prefijo. La columna <q>records</q> muestra el número de registros etiquetados con códigos con el prefijo dado, tal como se ve en el motor de búsqueda (incluyendo registros solo de metadatos). La columna <q>codes</q> muestra cuántos códigos reales tienen un prefijo dado. Prefijo de código conocido <q>%(key)s</q> Más… Prefijo %(count)s registro que coincide con “%(prefix_label)s” %(count)s registros que coinciden con “%(prefix_label)s” códigos registros “%%s” será sustituido por el valor del código Buscar en el Archivo de Anna Códigos URL para código específico: “%(url)s” Esta página puede tardar un tiempo en generarse, por lo que requiere un captcha de Cloudflare. <a %(a_donate)s>Los miembros</a> pueden omitir el captcha. Abuso reportado: Versión mejorada ¿Desea reportar a este usuario por comportamiento abusivo o inapropiado? Problema de archivo: %(file_issue)s comentario oculto Responder Reportar abuso Usted reportó a este usuario por abuso. Se ignorarán los reclamos de derechos de autor a este correo electrónico; utilicé el formulario en su lugar. Mostrar correo electrónico ¡Agradecemos mucho sus comentarios y preguntas! Sin embargo, debido a la cantidad de correos electrónicos no deseados y sin sentido que recibimos, marque las casillas para confirmar que comprende estas condiciones para contactarnos. Cualquier otra forma de contactarnos sobre reclamos de derechos de autor se eliminará automáticamente. Para reclamos de DMCA o derechos de autor, utilice <a %(a_copyright)s>este formulario</a>. Correo Electrónico URLs en el Archivo de Anna (requerido). Una por línea. Por favor, solo incluya URLs que describan exactamente la misma edición de un libro. Si desea hacer una reclamación para múltiples libros o múltiples ediciones, por favor envíe este formulario varias veces. Las reclamaciones que agrupen múltiples libros o ediciones serán rechazadas. Dirección (requerido) Descripción clara del material fuente (requerido) Correo electrónico (requerido) URLs del material fuente, una por línea (requerido). Por favor, incluya tantas como sea posible para ayudarnos a verificar su reclamación (por ejemplo, Amazon, WorldCat, Google Books, DOI). ISBNs del material fuente (si aplica). Uno por línea. Por favor, solo incluya aquellos que coincidan exactamente con la edición para la cual está reportando una reclamación de derechos de autor. Su nombre (requerido) ❌ Algo salió mal. Por favor, recargue la página e intente nuevamente. ✅ Gracias por enviar su reclamación de derechos de autor. La revisaremos lo antes posible. Por favor, recargue la página para enviar otra. <a %(a_openlib)s>Open Library</a> URLs del material fuente, una por línea. Por favor, tómese un momento para buscar su material fuente en Open Library. Esto nos ayudará a verificar su reclamación. Número de teléfono (requerido) Declaración y firma (requerido) Enviar reclamación Si tiene una reclamación de derechos de autor o DMCA, por favor complete este formulario con la mayor precisión posible. Si encuentra algún problema, contáctenos en nuestra dirección dedicada a DMCA: %(email)s. Tenga en cuenta que las reclamaciones enviadas por correo electrónico a esta dirección no serán procesadas, solo es para preguntas. Por favor, use el formulario a continuación para enviar sus reclamaciones. Formulario de reclamación de derechos de autor / DMCA Ejemplo de registro en Anna’s Archive Torrents por Anna’s Archive Formato de Contenedores de Anna’s Archive Scripts para importar metadatos Si está interesado en reflejar este conjunto de datos para <a %(a_archival)s>archivos</a> o para <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos. Última actualización: %(date)s Sitio web principal %(source)s Documentación de metadatos (la mayoría de los campos) Archivos reflejados por Anna’s Archive: %(count)s (%(percent)s%%) Recursos Total de archivos: %(count)s Tamaño total de archivos: %(size)s Nuestra entrada de blog sobre estos datos <a %(duxiu_link)s>Duxiu</a> es una enorme base de datos de libros escaneados, creada por el <a %(superstar_link)s>SuperStar Digital Library Group</a>. La mayoría son libros académicos, escaneados para ponerlos a disposición digitalmente de universidades y bibliotecas. Para nuestra audiencia de habla inglesa, <a %(princeton_link)s>Princeton</a> y la <a %(uw_link)s>Universidad de Washington</a> tienen buenos resúmenes. También hay un excelente artículo que ofrece más información: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Los libros de Duxiu han sido pirateados durante mucho tiempo en internet chino. Usualmente son vendidos por menos de un dólar por revendedores. Normalmente se distribuyen usando el equivalente chino de Google Drive, que a menudo ha sido hackeado para permitir más espacio de almacenamiento. Algunos detalles técnicos se pueden encontrar <a %(link1)s>aquí</a> y <a %(link2)s>aquí</a>. Aunque los libros han sido distribuidos semi-públicamente, es bastante difícil obtenerlos en grandes cantidades. Teníamos esto en lo alto de nuestra lista de tareas pendientes, y asignamos varios meses de trabajo a tiempo completo para ello. Sin embargo, a finales de 2023, un voluntario increíble, asombroso y talentoso se puso en contacto con nosotros, diciéndonos que ya había hecho todo este trabajo, a un gran costo. Compartieron la colección completa con nosotros, sin esperar nada a cambio, excepto la garantía de preservación a largo plazo. Verdaderamente notable. Más información de nuestros voluntarios (notas sin procesar): Adaptado de nuestra <a %(a_href)s>entrada de blog</a>. DuXiu 读秀 %(count)s archivo %(count)s archivos Este conjunto de datos está estrechamente relacionado con el <a %(a_datasets_openlib)s>conjunto de datos de Open Library</a>. Contiene una recopilación de todos los metadatos y una gran parte de los archivos de la Biblioteca de Préstamo Digital Controlado de IA. Las actualizaciones se publican en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>. Estos registros se refieren directamente al conjunto de datos de Open Library, pero también contienen registros que no están en Open Library. También tenemos varios archivos de datos recopilados por miembros de la comunidad a lo largo de los años. La colección consta de dos partes. Necesita ambas partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents). Biblioteca de Préstamo Digital nuestra primera versión, antes de estandarizarnos en el <a %(a_aac)s>formato de Contenedores de Anna’s Archive (AAC)</a>. Contiene metadatos (en json y xml), pdfs (de los sistemas de préstamo digital acsm y lcpdf), y miniaturas de portadas. nuevas versiones incrementales, usando AAC. Solo contiene metadatos con marcas de tiempo posteriores al 2023-01-01, ya que el resto ya está cubierto por “ia”. También todos los archivos pdf, esta vez de los sistemas de préstamo acsm y “bookreader” (el lector web de IA). A pesar de que el nombre no sea exactamente correcto, aún así incluimos archivos de bookreader en la colección ia2_acsmpdf_files, ya que son mutuamente excluyentes. Préstamo Digital Controlado de IA 98%%+ de archivos son buscables. Nuestra misión es archivar todos los libros del mundo (así como artículos, revistas, etc.), y hacerlos ampliamente accesibles. Creemos que todos los libros deben ser replicados ampliamente, para asegurar redundancia y resiliencia. Por eso estamos reuniendo archivos de una variedad de fuentes. Algunas fuentes son completamente abiertas y pueden ser replicadas en masa (como Sci-Hub). Otras son cerradas y protectoras, por lo que intentamos extraerlas para “liberar” sus libros. Otras se encuentran en algún punto intermedio. Todos nuestros datos pueden ser <a %(a_torrents)s>torrentados</a>, y todos nuestros metadatos pueden ser <a %(a_anna_software)s>generados</a> o <a %(a_elasticsearch)s>descargados</a> como bases de datos ElasticSearch y MariaDB. Los datos en bruto pueden ser explorados manualmente a través de archivos JSON como <a %(a_dbrecord)s>este</a>. Metadatos Sitio web del ISBN Última actualización: %(isbn_country_date)s (%(link)s) Recursos La Agencia Internacional del ISBN publica regularmente los rangos que ha asignado a las agencias nacionales del ISBN. A partir de esto, podemos deducir a qué país, región o grupo de idiomas pertenece este ISBN. Actualmente usamos estos datos de manera indirecta, a través de la biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>. Información del país del ISBN Este es un volcado de muchas llamadas a isbndb.com durante septiembre de 2022. Intentamos cubrir todos los rangos de ISBN. Estos son alrededor de 30.9 millones de registros. En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que de alguna manera podríamos haber perdido algunos, o <em>ellos</em> podrían estar haciendo algo mal. Las respuestas JSON son prácticamente crudas desde su servidor. Un problema de calidad de datos que notamos es que para los números ISBN-13 que comienzan con un prefijo diferente a “978-”, aún incluyen un campo “isbn” que simplemente es el número ISBN-13 con los primeros 3 números eliminados (y el dígito de control recalculado). Esto es obviamente incorrecto, pero así es como parecen hacerlo, por lo que no lo alteramos. Otro problema potencial que podría encontrar es el hecho de que el campo “isbn13” tiene duplicados, por lo que no puede usarlo como clave primaria en una base de datos. Los campos combinados “isbn13”+“isbn” parecen ser únicos. Lanzamiento 1 (2022-10-31) Los torrents de ficción están atrasados (aunque los IDs ~4-6M no están en torrents ya que se superponen con nuestros torrents de Zlib). Nuestra publicación en el blog sobre el lanzamiento de los cómics Torrents de cómics en el Archivo de Anna Para la historia de los diferentes forks de Library Genesis, vea la página de <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li contiene la mayor parte del mismo contenido y metadatos que Libgen.rs, pero tiene algunas colecciones adicionales, a saber, cómics, revistas y documentos estándar. También ha integrado <a %(a_scihub)s>Sci-Hub</a> en sus metadatos y motor de búsqueda, que es lo que usamos para nuestra base de datos. Los metadatos de esta biblioteca están disponibles gratuitamente <a %(a_libgen_li)s>en libgen.li</a>. Sin embargo, este servidor es lento y no admite la reanudación de conexiones interrumpidas. Los mismos archivos también están disponibles en <a %(a_ftp)s>un servidor FTP</a>, que funciona mejor. La no ficción también parece haberse desviado, pero sin nuevos torrentes. Parece que esto ha sucedido desde principios de 2022, aunque no lo hemos verificado. Según el administrador de Libgen.li, la colección “fiction_rus” (ficción rusa) debería estar cubierta por torrents lanzados regularmente desde <a %(a_booktracker)s>booktracker.org</a>, especialmente los torrents de <a %(a_flibusta)s>flibusta</a> y <a %(a_librusec)s>lib.rus.ec</a> (que espejamos <a %(a_torrents)s>aquí</a>, aunque aún no hemos establecido qué torrents corresponden a qué archivos). La colección de ficción tiene sus propios torrents (divergente de <a %(a_href)s>Libgen.rs</a>) comenzando en %(start)s. Ciertos rangos sin torrents (como los rangos de ficción f_3463000 a f_4260000) son probablemente archivos de Z-Library (u otros duplicados), aunque podríamos querer hacer alguna deduplicación y crear torrents para archivos únicos de lgli en estos rangos. Las estadísticas de todas las colecciones se pueden encontrar <a %(a_href)s>en el sitio web de libgen</a>. Los torrents están disponibles para la mayoría del contenido adicional, especialmente los torrents para cómics, revistas y documentos estándar han sido lanzados en colaboración con el Archivo de Anna. Tenga en cuenta que los archivos torrent que se refieren a “libgen.is” son explícitamente espejos de <a %(a_libgen)s>Libgen.rs</a> (“.is” es un dominio diferente utilizado por Libgen.rs). Un recurso útil para usar los metadatos es <a %(a_href)s>esta página</a>. %(icon)s Su colección “fiction_rus” (ficción rusa) no tiene torrents dedicados, pero está cubierta por torrents de otros, y mantenemos un <a %(fiction_rus)s>espejo</a>. Torrents de ficción rusa en el Archivo de Anna Torrents de ficción en el Archivo de Anna Foro de discusión Metadatos Metadatos vía FTP Torrents de revistas en el Archivo de Anna Información del campo de metadatos Espejo de otros torrents (y torrents únicos de ficción y cómics) Torrents de documentos estándar en el Archivo de Anna Libgen.li Torrents por el Archivo de Anna (portadas de libros) Library Genesis es conocido por ya hacer generosamente sus datos disponibles en masa a través de torrents. Nuestra colección de Libgen consiste en datos auxiliares que ellos no liberan directamente, en asociación con ellos. ¡Muchas gracias a todos los involucrados con Library Genesis por trabajar con nosotros! Nuestro blog sobre el lanzamiento de las portadas de libros Esta página trata sobre la versión “.rs”. Es conocida por publicar de manera constante tanto sus metadatos como el contenido completo de su catálogo de libros. Su colección de libros está dividida entre una parte de ficción y otra de no ficción. Un recurso útil para usar los metadatos es <a %(a_metadata)s>esta página</a> (bloquea rangos de IP, puede ser necesario usar VPN). A partir de 2024-03, se están publicando nuevos torrents en <a %(a_href)s>este hilo del foro</a> (bloquea rangos de IP, puede ser necesario usar VPN). Torrents de ficción en el Archivo de Anna Torrents de ficción de Libgen.rs Foro de discusión de Libgen.rs Metadatos de Libgen.rs Información de los campos de metadatos de Libgen.rs Torrents de no ficción de Libgen.rs Torrents de no ficción en el Archivo de Anna %(example)s para un libro de ficción. Este <a %(blog_post)s>primer lanzamiento</a> es bastante pequeño: alrededor de 300GB de portadas de libros del fork de Libgen.rs, tanto de ficción como de no ficción. Están organizados de la misma manera en que aparecen en libgen.rs, por ejemplo: %(example)s para un libro de no ficción. Al igual que con la colección de Z-Library, los pusimos todos en un gran archivo .tar, que se puede montar usando <a %(a_ratarmount)s>ratarmount</a> si deseas servir los archivos directamente. Lanzamiento 1 (%(date)s) La breve historia de las diferentes bifurcaciones de Library Genesis (o “Libgen”) es que, con el tiempo, las diferentes personas involucradas en Library Genesis tuvieron una disputa y se separaron. Según esta <a %(a_mhut)s>publicación en el foro</a>, Libgen.li fue alojado originalmente en “http://free-books.dontexist.com”. La versión “.fun” fue creada por el fundador original. Está siendo renovada a favor de una nueva versión más distribuida. La <a %(a_li)s>versión “.li”</a> tiene una colección masiva de cómics, así como otros contenidos, que aún no están disponibles para descarga masiva a través de torrents. Tiene una colección de torrents separada de libros de ficción y contiene los metadatos de <a %(a_scihub)s>Sci-Hub</a> en su base de datos. La versión “.rs” tiene datos muy similares y publica su colección en torrents masivos de manera constante. Está dividida aproximadamente en una sección de “ficción” y otra de “no ficción”. Originalmente en “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> en cierto sentido también es una bifurcación de Library Genesis, aunque usaron un nombre diferente para su proyecto. Libgen.rs También enriquecemos nuestra colección con fuentes solo de metadatos, que podemos emparejar con archivos, por ejemplo, usando números ISBN u otros campos. A continuación se presenta una visión general de esas fuentes. Nuevamente, algunas de estas fuentes son completamente abiertas, mientras que para otras tenemos que extraer los datos. Tenga en cuenta que en la búsqueda de metadatos, mostramos los registros originales. No hacemos ninguna fusión de registros. Fuentes solo de metadatos Open Library es un proyecto de código abierto del Internet Archive para catalogar todos los libros del mundo. Tiene una de las operaciones de escaneo de libros más grandes del mundo y tiene muchos libros disponibles para préstamo digital. Su catálogo de metadatos de libros está disponible gratuitamente para su descarga y está incluido en el Archivo de Anna (aunque actualmente no está en búsqueda, excepto si buscas explícitamente un ID de Open Library). Open Library Excluyendo duplicados Última actualización Porcentajes del número de archivos %% reflejado por AA / torrents disponibles Tamaño Fuente A continuación se muestra un resumen rápido de las fuentes de los archivos en el Archivo de Anna. Dado que las bibliotecas en la sombra a menudo sincronizan datos entre sí, hay una considerable superposición entre las bibliotecas. Por eso los números no suman el total. El porcentaje de “espejado y sembrado por el Archivo de Anna” muestra cuántos archivos espejamos nosotros mismos. Sembramos esos archivos en masa a través de torrents y los hacemos disponibles para descarga directa a través de sitios web asociados. Descripción general Total Torrents en el Archivo de Anna Para obtener información sobre Sci-Hub, consulte su <a %(a_scihub)s>sitio web oficial</a>, <a %(a_wikipedia)s>página de Wikipedia</a> y esta <a %(a_radiolab)s>entrevista en podcast</a>. Tenga en cuenta que Sci-Hub ha estado <a %(a_reddit)s>congelado desde 2021</a>. Se congeló antes, pero en 2021 se añadieron unos pocos millones de artículos. Aún así, se añaden algunos artículos limitados a las colecciones “scimag” de Libgen, aunque no lo suficiente como para justificar nuevos torrents masivos. Usamos los metadatos de Sci-Hub proporcionados por <a %(a_libgen_li)s>Libgen.li</a> en su colección “scimag”. También utilizamos el conjunto de datos <a %(a_dois)s>dois-2022-02-12.7z</a>. Tenga en cuenta que los torrents “smarch” están <a %(a_smarch)s>obsoletos</a> y por lo tanto no se incluyen en nuestra lista de torrents. Torrents en Libgen.li Torrents en Libgen.rs Metadatos y torrents Actualizaciones en Reddit Entrevista en podcast Página de Wikipedia Sci-Hub Sci-Hub: congelado desde 2021; la mayoría disponible a través de torrents Libgen.li: adiciones menores desde entonces</div> Algunas bibliotecas fuente promueven el intercambio masivo de sus datos a través de torrents, mientras que otras no comparten fácilmente su colección. En este último caso, el Archivo de Anna intenta extraer sus colecciones y hacerlas disponibles (vea nuestra página de <a %(a_torrents)s>Torrents</a>). También hay situaciones intermedias, por ejemplo, donde las bibliotecas fuente están dispuestas a compartir, pero no tienen los recursos para hacerlo. En esos casos, también tratamos de ayudar. A continuación se presenta una visión general de cómo interactuamos con las diferentes bibliotecas fuente. Bibliotecas fuente %(icon)s Varias bases de datos de archivos dispersas por el internet chino; aunque a menudo son bases de datos de pago %(icon)s La mayoría de los archivos solo son accesibles usando cuentas premium de BaiduYun; velocidades de descarga lentas. %(icon)s El Archivo de Anna gestiona una colección de <a %(duxiu)s>archivos de DuXiu</a> %(icon)s Varias bases de datos de metadatos dispersas por el internet chino; aunque a menudo son bases de datos de pago %(icon)s No hay volcados de metadatos fácilmente accesibles disponibles para toda su colección. %(icon)s El Archivo de Anna gestiona una colección de <a %(duxiu)s>metadatos de DuXiu</a> Archivos %(icon)s Archivos solo disponibles para préstamo de manera limitada, con varias restricciones de acceso %(icon)s El Archivo de Anna gestiona una colección de <a %(ia)s>archivos IA</a> %(icon)s Algunos metadatos disponibles a través de <a %(openlib)s>volcados de base de datos de Open Library</a>, pero esos no cubren toda la colección de IA %(icon)s No hay volcado de metadatos fácilmente accesibles disponibles para toda su colección %(icon)s El Archivo de Anna gestiona una colección de <a %(ia)s>metadatos de IA</a> Última actualización %(icon)s El Archivo de Anna y Libgen.li gestionan colaborativamente colecciones de <a %(comics)s>cómics</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos estándar</a> y <a %(fiction)s>ficción (divergente de Libgen.rs)</a>. %(icon)s Los torrents de No Ficción se comparten con Libgen.rs (y se reflejan <a %(libgenli)s>aquí</a>). %(icon)s <a %(dbdumps)s>Volcados de base de datos HTTP</a> trimestrales %(icon)s Torrents automatizados para <a %(nonfiction)s>No Ficción</a> y <a %(fiction)s>Ficción</a> %(icon)s El Archivo de Anna gestiona una colección de <a %(covers)s>torrents de portadas de libros</a> %(icon)s <a %(dbdumps)s>Volcados de base de datos HTTP</a> diarios Metadatos %(icon)s <a %(dbdumps)s>Volcados de base de datos</a> mensuales %(icon)s Torrents de datos disponibles <a %(scihub1)s>aquí</a>, <a %(scihub2)s>aquí</a> y <a %(libgenli)s>aquí</a> %(icon)s Algunos archivos nuevos están <a %(libgenrs)s>siendo</a> <a %(libgenli)s>agregados</a> a “scimag” de Libgen, pero no lo suficiente como para justificar nuevos torrents %(icon)s Sci-Hub ha congelado nuevos archivos desde 2021. %(icon)s Volcados de metadatos disponibles <a %(scihub1)s>aquí</a> y <a %(scihub2)s>aquí</a>, así como parte de la <a %(libgenli)s>base de datos de Libgen.li</a> (que usamos) Fuente %(icon)s Varias fuentes más pequeñas o únicas. Animamos a las personas a subir primero a otras bibliotecas en la sombra, pero a veces las personas tienen colecciones que son demasiado grandes para que otros las clasifiquen, aunque no lo suficientemente grandes como para justificar su propia categoría. %(icon)s No disponible directamente en masa, protegido contra scraping %(icon)s El Archivo de Anna gestiona una colección de <a %(worldcat)s>metadatos de OCLC (WorldCat)</a> %(icon)s El Archivo de Anna y Z-Library gestionan colaborativamente una colección de <a %(metadata)s>metadatos de Z-Library</a> y <a %(files)s>archivos de Z-Library</a> Conjunto de datos Combinamos todas las fuentes anteriores en una base de datos unificada que utilizamos para servir este sitio web. Esta base de datos unificada no está disponible directamente, pero dado que el Archivo de Anna es completamente de código abierto, se puede <a %(a_generated)s>generar</a> o <a %(a_downloaded)s>descargar</a> bastante fácilmente como bases de datos ElasticSearch y MariaDB. Los scripts en esa página descargarán automáticamente todos los metadatos necesarios de las fuentes mencionadas anteriormente. Si desea explorar nuestros datos antes de ejecutar esos scripts localmente, puede consultar nuestros archivos JSON, que enlazan a otros archivos JSON. <a %(a_json)s>Este archivo</a> es un buen punto de partida. Base de datos unificada Torrents por el Archivo de Anna explorar buscar Varias fuentes más pequeñas o únicas. Animamos a las personas a subir primero a otras bibliotecas en la sombra, pero a veces las personas tienen colecciones que son demasiado grandes para que otros las clasifiquen, aunque no lo suficientemente grandes como para justificar su propia categoría. Resumen de la <a %(a1)s>página de datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bastante completo. De nuestro voluntario “cgiym”. De un torrent de <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tiene una superposición bastante alta con las colecciones de artículos existentes, pero muy pocas coincidencias de MD5, por lo que decidimos mantenerlo completamente. Extracción de <q>iRead eBooks</q> (= fonéticamente <q>ai rit i-books</q>; airitibooks.com), por el voluntario <q>j</q>. Corresponde a la metadata de <q>airitibooks</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>. De una colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte de la fuente original, parte de the-eye.eu, parte de otros espejos. De un sitio web privado de torrents de libros, <a %(a_href)s>Bibliotik</a> (a menudo referido como “Bib”), cuyos libros se agruparon en torrents por nombre (A.torrent, B.torrent) y se distribuyeron a través de the-eye.eu. De nuestro voluntario “bpb9v”. Para más información sobre <a %(a_href)s>CADAL</a>, vea las notas en nuestra <a %(a_duxiu)s>página del conjunto de datos de DuXiu</a>. Más de nuestro voluntario <q>bpb9v</q>, principalmente archivos de DuXiu, así como una carpeta <q>WenQu</q> y <q>SuperStar_Journals</q> (SuperStar es la empresa detrás de DuXiu). De nuestro voluntario “cgiym”, textos chinos de varias fuentes (representados como subdirectorios), incluyendo de <a %(a_href)s>China Machine Press</a> (un importante editor chino). Colecciones no chinas (representadas como subdirectorios) de nuestro voluntario <q>cgiym</q>. Extracción de libros sobre arquitectura china, por el voluntario <q>cm</q>: <q>Lo obtuve explotando una vulnerabilidad de red en la editorial, pero esa brecha ya ha sido cerrada</q>. Corresponde a la metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>. Libros de la editorial académica <a %(a_href)s>De Gruyter</a>, recopilados de algunos torrents grandes. Raspado de <a %(a_href)s>docer.pl</a>, un sitio web polaco de intercambio de archivos enfocado en libros y otras obras escritas. Raspado a finales de 2023 por el voluntario “p”. No tenemos buenos metadatos del sitio web original (ni siquiera extensiones de archivo), pero filtramos archivos con apariencia de libros y a menudo pudimos extraer metadatos de los propios archivos. Epubs de DuXiu, directamente de DuXiu, recopilados por el voluntario <q>w</q>. Solo los libros recientes de DuXiu están disponibles directamente a través de ebooks, por lo que la mayoría de estos deben ser recientes. Archivos restantes de DuXiu del voluntario “m”, que no estaban en el formato propietario PDG de DuXiu (el principal <a %(a_href)s>conjunto de datos de DuXiu</a>). Recopilados de muchas fuentes originales, lamentablemente sin preservar esas fuentes en la ruta del archivo. <span></span> <span></span> <span></span> Extracción de libros eróticos, por el voluntario <q>do no harm</q>. Corresponde a la metadata de <q>hentai</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>. <span></span> <span></span> Colección raspada de un editor japonés de manga por el voluntario <q>t</q>. <a %(a_href)s>Archivos judiciales seleccionados de Longquan</a>, proporcionados por el voluntario “c”. Raspado de <a %(a_href)s>magzdb.org</a>, un aliado de Library Genesis (está vinculado en la página de inicio de libgen.rs) pero que no quiso proporcionar sus archivos directamente. Obtenido por el voluntario “p” a finales de 2023. <span></span> Varias cargas pequeñas, demasiado pequeñas para ser su propia subcolección, pero representadas como directorios. El directorio <q>oo42hcksBxZYAOjqwGWu</q> corresponde a los metadatos <q>czech_oo42hcks</q> en <a %(a1)s><q>Otros metadatos extraídos/q></a>. Ebooks de AvaxHome, un sitio web ruso para compartir archivos. Archivo de periódicos y revistas. Corresponde a la metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>. Extracción del <a %(a1)s>Centro de Documentación de Filosofía</a>. Colección del voluntario <q>o</q> que recopiló libros polacos directamente de sitios web de lanzamiento original (<q>scene</q>). Colecciones combinadas de <a %(a_href)s>shuge.org</a> por los voluntarios “cgiym” y “woz9ts”. <span></span> <a %(a_href)s><q>Biblioteca Imperial de Trantor</q></a> (nombrada en honor a la biblioteca ficticia), recopilada en 2022 por el voluntario <q>t</q>. Corresponde a los metadatos de <q>trantor</q> en <a %(a1)s><q>Otras recopilaciones de metadatos</q></a>. <span></span> <span></span> <span></span> Sub-sub-colecciones (representadas como directorios) del voluntario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taiwán), mebook (mebook.cc, 我的小书屋, mi pequeño cuarto de libros — woz9ts: “Este sitio se centra principalmente en compartir archivos de ebooks de alta calidad, algunos de los cuales son maquetados por el propio dueño. El dueño fue <a %(a_arrested)s>arrestado</a> en 2019 y alguien hizo una colección de los archivos que compartió.”). Archivos restantes de DuXiu del voluntario <q>woz9ts</q>, que no estaban en el formato propietario PDG de DuXiu (aún por convertir a PDF). La colección “upload” se divide en subcolecciones más pequeñas, que se indican en los AACIDs y nombres de torrents. Todas las subcolecciones se deduplicaron primero contra la colección principal, aunque los archivos JSON de metadatos “upload_records” todavía contienen muchas referencias a los archivos originales. Los archivos que no son libros también se eliminaron de la mayoría de las subcolecciones y, por lo general, <em>no</em> se anotan en los JSON de “upload_records”. Las subcolecciones son: Notas Subcolección Muchas subcolecciones en sí mismas están compuestas por sub-sub-colecciones (por ejemplo, de diferentes fuentes originales), que se representan como directorios en los <q>filepath</q> campos. Subidas al Archivo de Anna Nuestra publicación en el blog sobre estos datos <a %(a_worldcat)s>WorldCat</a> es una base de datos propietaria de la organización sin fines de lucro <a %(a_oclc)s>OCLC</a>, que agrega registros de metadatos de bibliotecas de todo el mundo. Es probable que sea la colección de metadatos de bibliotecas más grande del mundo. En octubre de 2023 <a %(a_scrape)s>lanzamos</a> una recopilación exhaustiva de la base de datos OCLC (WorldCat), en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>. Octubre de 2023, lanzamiento inicial: OCLC (WorldCat) Torrents por el Archivo de Anna Ejemplo de registro en el Archivo de Anna (colección original) Ejemplo de registro en el Archivo de Anna (colección “zlib3”) Torrents por el Archivo de Anna (metadatos + contenido) Entrada de blog sobre el Lanzamiento 1 Entrada de blog sobre el Lanzamiento 2 A finales de 2022, los presuntos fundadores de Z-Library fueron arrestados y los dominios fueron incautados por las autoridades de Estados Unidos. Desde entonces, el sitio web ha estado volviendo a estar en línea lentamente. Se desconoce quién lo administra actualmente. Actualización a febrero de 2023. Z-Library tiene sus raíces en la comunidad de <a %(a_href)s>Library Genesis</a>, y originalmente se inició con sus datos. Desde entonces, se ha profesionalizado considerablemente y tiene una interfaz mucho más moderna. Por lo tanto, pueden recibir muchas más donaciones, tanto monetarias para seguir mejorando su sitio web, como donaciones de nuevos libros. Han acumulado una gran colección además de Library Genesis. La colección consta de tres partes. Las páginas de descripción originales para las dos primeras partes se conservan a continuación. Necesitas las tres partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents). %(title)s: nuestro primer lanzamiento. Este fue el primer lanzamiento de lo que entonces se llamaba el “Espejo de la Biblioteca Pirata” (“pilimi”). %(title)s: segundo lanzamiento, esta vez con todos los archivos envueltos en archivos .tar. %(title)s: nuevos lanzamientos incrementales, usando el <a %(a_href)s>formato de Contenedores del Archivo de Anna (AAC)</a>, ahora lanzado en colaboración con el equipo de Z-Library. El espejo inicial se obtuvo meticulosamente a lo largo de 2021 y 2022. En este punto está ligeramente desactualizado: refleja el estado de la colección en junio de 2021. Actualizaremos esto en el futuro. Ahora mismo estamos enfocados en lanzar esta primera versión. Dado que Library Genesis ya está preservado con torrents públicos y está incluido en Z-Library, hicimos una deduplicación básica contra Library Genesis en junio de 2022. Para esto utilizamos hashes MD5. Es probable que haya mucho más contenido duplicado en la biblioteca, como múltiples formatos de archivo con el mismo libro. Esto es difícil de detectar con precisión, por lo que no lo hacemos. Después de la deduplicación, nos quedamos con más de 2 millones de archivos, totalizando poco menos de 7TB. La colección consta de dos partes: un volcado MySQL “.sql.gz” de los metadatos, y los 72 archivos torrent de alrededor de 50-100GB cada uno. Los metadatos contienen los datos según lo reportado por el sitio web de Z-Library (título, autor, descripción, tipo de archivo), así como el tamaño real del archivo y el md5sum que observamos, ya que a veces estos no coinciden. Parece haber rangos de archivos para los cuales Z-Library en sí tiene metadatos incorrectos. También podríamos haber descargado archivos incorrectamente en algunos casos aislados, lo cual intentaremos detectar y corregir en el futuro. Los grandes archivos torrent contienen los datos reales de los libros, con el ID de Z-Library como nombre de archivo. Las extensiones de archivo pueden reconstruirse utilizando el volcado de metadatos. La colección es una mezcla de contenido de no ficción y ficción (no separado como en Library Genesis). La calidad también varía ampliamente. Esta primera versión ya está completamente disponible. Tenga en cuenta que los archivos torrent solo están disponibles a través de nuestro espejo Tor. Lanzamiento 1 (%(date)s) Este es un único archivo torrent adicional. No contiene ninguna información nueva, pero tiene algunos datos que pueden tardar un tiempo en calcularse. Eso lo hace conveniente de tener, ya que descargar este torrent suele ser más rápido que calcularlo desde cero. En particular, contiene índices SQLite para los archivos tar, para usar con <a %(a_href)s>ratarmount</a>. Addendum del Lanzamiento 2 (%(date)s) Hemos obtenido todos los libros que se añadieron a Z-Library entre nuestro último espejo y agosto de 2022. También hemos vuelto y recopilado algunos libros que nos perdimos la primera vez. En total, esta nueva colección es de aproximadamente 24TB. Nuevamente, esta colección está deduplicada contra Library Genesis, ya que ya hay torrents disponibles para esa colección. Los datos están organizados de manera similar al primer lanzamiento. Hay un volcado MySQL “.sql.gz” de los metadatos, que también incluye todos los metadatos del primer lanzamiento, reemplazándolo así. También añadimos algunas nuevas columnas: Mencionamos esto la última vez, pero solo para aclarar: “nombre de archivo” y “md5” son las propiedades reales del archivo, mientras que “nombre de archivo_reportado” y “md5_reportado” son lo que recopilamos de Z-Library. A veces estos dos no coinciden entre sí, por lo que incluimos ambos. Para este lanzamiento, cambiamos la intercalación a “utf8mb4_unicode_ci”, que debería ser compatible con versiones anteriores de MySQL. Los archivos de datos son similares a la última vez, aunque son mucho más grandes. Simplemente no nos molestamos en crear toneladas de archivos torrent más pequeños. “pilimi-zlib2-0-14679999-extra.torrent” contiene todos los archivos que nos perdimos en el último lanzamiento, mientras que los otros torrents son todos nuevos rangos de ID.  <strong>Actualización %(date)s:</strong> Hicimos la mayoría de nuestros torrents demasiado grandes, lo que causó problemas a los clientes de torrent. Los hemos eliminado y lanzado nuevos torrents. <strong>Actualización %(date)s:</strong> Todavía había demasiados archivos, así que los empaquetamos en archivos tar y lanzamos nuevos torrents nuevamente. %(key)s: si este archivo ya está en Library Genesis, ya sea en la colección de no ficción o de ficción (coincidencia por md5). %(key)s: en qué torrent se encuentra este archivo. %(key)s: establecido cuando no pudimos descargar el libro. Lanzamiento 2 (%(date)s) Lanzamientos de Zlib (páginas de descripción original) Dominio Tor Sitio web principal Extracción de Z-Library La colección “China” en Z-Library parece ser la misma que nuestra colección de DuXiu, pero con diferentes MD5s. Excluimos estos archivos de los torrents para evitar duplicaciones, pero aún los mostramos en nuestro índice de búsqueda. Metadatos Obtienes un %(percentage)s%% de descargas rápidas adicionales porque fuiste referido por el usuario %(profile_link)s. Esto se aplica a todo el periodo de membresía. Haz una donación Únete Seleccionado Descuentos de hasta %(percentage)s%% Alipay acepta tarjetas de crédito/débito internacionales. Consulta <a %(a_alipay)s>esta guía</a> para más información. Regálanos una tarjeta de regalo de Amazon.com usando tu tarjeta de crédito/débito. Puedes comprar criptomonedas usando tu tarjeta de crédito/débito. WeChat (Weixin Pay) admite tarjetas de crédito/débito internacionales. En la aplicación WeChat, accede a "Yo => Servicios => Monedero => Agregar una tarjeta". Si no lo ves, actívalo usando "Yo => Configuración => General => Herramientas => Weixin Pay => Habilitar". (para enviar Ethereum desde Coinbase) copiado! copiar (cantidad mínima más baja) (advertencia: monto mínimo alto) - %(percentage)s%% 12 meses 1 mes 24 meses 3 meses 48 meses 6 meses 96 meses Selecciona la duración de tu suscripción. <div %(div_monthly_cost)s></div><div %(div_after)s>después<span %(span_discount)s></span> del descuento</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% por 12 meses por 1 mes por 24 meses por 3 meses por 48 meses por 6 meses por 96 meses %(monthly_cost)s / mes contáctanos Servidores <strong>SFTP</strong> directos Donación o intercambio a nivel empresarial por nuevas colecciones (p.ej. nuevos escaneos, conjuntos de datos OCR). Acceso para expertos Acceso <strong>ilimitado</strong>de alta velocidad <div %(div_question)s>¿Puedo mejorar mi membresía o obtener múltiples membresías?</div> <div %(div_question)s>¿Puedo hacer una donación sin convertirme en miembro?</div> Claro que sí. Aceptamos donaciones de cualquier cantidad en esta dirección de Monero (XMR): %(address)s. <div %(div_question)s>¿Qué significan los rangos por mes?</div> Puede llegar al lado inferior de un rango aplicando todos los descuentos, como elegir un período más largo que un mes. <div %(div_question)s>¿Las membresías se renuevan automáticamente?</div> Las membresías <strong>no</strong> se renuevan automáticamente. Puedes unirte por tanto o tan poco tiempo como quieras. <div %(div_question)s>¿En qué gastas las donaciones?</div> El 100%% se destina a preservar y hacer accesible el conocimiento y la cultura del mundo. Actualmente lo gastamos principalmente en servidores, almacenamiento y ancho de banda. No se destinará dinero personalmente a ningún miembro del equipo. <div %(div_question)s>¿Puedo hacer una donación importante?</div> ¡Sería increíble!. Para donaciones superiores a unos pocos miles de dólares, ponte en contacto con nosotros directamente en %(email)s. <div %(div_question)s>¿Tenéis de otros métodos de pago?</div> Actualmente no. Mucha gente no quiere que existan archivos como este, por lo que debemos tener cuidado. Si puedes ayudarnos a establecer otros métodos de pago (más convenientes) de manera segura, contáctanos a %(email)s. FAQ sobre donaciones Tiene una <a %(a_donation)s>donación</a> en curso. Por favor termine o cancele sus donaciones actuales antes de realizar una nueva donación. <a %(a_all_donations)s>Ver todas mis donaciones</a> Para donaciones superiores a $5000 dólares, contáctanos directamente a %(email)s. Aceptamos donaciones grandes de instituciones o individuos adinerados.  Ten en cuenta que, aunque las membresías en esta página son “mensuales”, son donaciones únicas (no recurrentes). Consulta las <a %(faq)s>Preguntas frecuentes sobre donación</a>. El Archivo de Anna es un proyecto de código abierto, datos abiertos y sin fines de lucro. Donando y haciéndote miembro nos ayudas con el desarrollo y las operaciones. A todos nuestros miembros: ¡Gracias por ayudarnos a seguir! ❤️ Para más información, revisa la sección <a %(a_donate)s>Preguntas frecuentes sobre donaciones </a>. Para convertirte en miembro, <a %(a_login)s>inicia sesión o regístrate</a>. ¡Gracias por tu apoyo! $%(cost)s / mes Si cometió un error durante el pago no podemos hacer reembolsos, pero intentaremos corregirlo. Encuentre la sección "Cripto" en la web o app de PayPal. Por lo general, esto se encuentra en "Finanzas". Ve a la sección "Bitcoin" en la web o app de PayPal. Presiona el botón de "Transferir" %(transfer_icon)s, y pulsa "Enviar". Alipay Alipay/WeChat Tarjeta de regalo de Amazon Tarjeta de regalo de %(amazon)s Tarjeta bancaria Tarjeta bancaria (usando la app) Binance Crédito/débito/Apple/Google (BMC) Cash App Tarjeta de crédito/débito Tarjeta de crédito/débito 2 Tarjeta de Crédito/Débito (backup) Criptomonedas %(bitcoin_icon)s Tarjeta / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal (normal) Pix (Brasil) Revolut (no disponible temporalmente) WeChat Selecciona tu criptomoneda preferida: Dona usando una tarjeta de regalo de Amazon. <strong>IMPORTANTE:</strong> Esta opción es para %(amazon)s. Si desea usar otro sitio web de Amazon, selecciónelo arriba. <strong>IMPORTANTE:</strong> Solo admitimos Amazon.com, no otros sitios web de Amazon. Por ejemplo, .de, .co.uk, .ca NO son compatibles. Por favor NO escribas tu propio mensaje. Ingrese el importe exacto: %(amount)s Ten en cuenta que debemos redondear a cantidades aceptadas por nuestros revendedores (mínimo %(minimum)s). Dona usando una tarjeta de crédito/débito, a través de la app de Alipay (muy fácil de configurar). Instala la app de Alipay desde la <a %(a_app_store)s>App Store de Apple</a> o <a %(a_play_store)s>Play Store de Google</a>. Regístrate usando tu número de teléfono. No se requieren más detalles personales. <span %(style)s>1</span>Instalar la app de Alipay Admitidas: Visa, MasterCard, JCB, Diners Club y Discover. Consulta <a %(a_alipay)s>esta guía</a> para más información. <span %(style)s>2</span>Añadir tarjeta bancaria Con Binance, podes comprar Bitcoin con una tarjeta de crédito/débito o una cuenta bancaria y luego nos podes donar esos Bitcoin. De esa manera podemos permanecer seguros y anónimos al aceptar tu donación. Binance está disponible en casi todos los países y es compatible con la mayoría de los bancos y tarjetas de crédito/débito. Esta es actualmente nuestra principal recomendación. Agradecemos que te tomes el tiempo de aprender cómo donar usando este método, ya que nos ayuda mucho. Para tarjetas de crédito, tarjetas de débito, Apple Pay y Google Pay, utilizamos “Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). En su sistema, un “Coffee” equivale a 5 $, por lo que tu donación se redondeará al múltiplo de 5 más cercano. Dona usando Cash App. Si tienes Cash App, ¡esta es la forma más fácil de donar! Ten en cuenta que para transacciones inferiores a %(amount)s, Cash App puede cobrar una tarifa de %(fee)s. Por %(amount)s o más, ¡es gratis! Dona con tarjeta de crédito o débito. Este método utiliza un proveedor de criptomonedas como intermediario. Esto puede resultar un poco confuso, así que utiliza este método únicamente si otros métodos de pago no funcionan. Tampoco funciona en todos los países. No podemos aceptar tarjetas de crédito/débito directamente porque los bancos no quieren trabajar con nosotros. ☹ Sin embargo, hay varias formas de usar tarjetas de crédito/débito de todos modos, utilizando otros métodos de pago: Con criptomonedas puedes donar con BTC, ETH, XMR, y SOL. Usa esta opción si estás familiarizado con las criptomonedas. Con criptomonedas puedes donar usando BTC, ETH, XMR y más. Servicios exprés de criptomonedas Si está utilizando criptomonedas por primera vez, sugerimos usar %(options)s para comprar y donar Bitcoin (la criptomoneda original y más utilizada). Ten en cuenta que para donaciones pequeñas las tarifas de la tarjeta de crédito pueden eliminar nuestro descuento de %(discount)s%%, por lo que recomendamos suscripciones más largas. Done utilizando tarjeta de crédito/débito, PayPal o Venmo. Puedes elegir entre estas opciones en la siguiente página. Google Pay y Apple Pay también podrían funcionar. Ten en cuenta que para pequeñas donaciones las tarifas son elevadas, por lo que recomendamos suscripciones más largas. Para donar con PayPal, vamos a usar PayPal Crypto, que nos permite permanecer anónimos. Agradecemos que te tomes el tiempo para aprender a donar con este método, nos ayudas mucho. Dona usando PayPal. Donar usando tu cuenta normal de PayPal. Donar con Revolut. ¡Si tienes Revolut, esta es la forma más fácil de donar! Este método de pago solo permite un máximo de %(amount)s. Selecciona una duración o un método de pago diferente. Este método de pago tiene un mínimo de %(amount)s. Selecciona una duración o un método de pago diferente. Binance Coinbase Kraken Por favor, seleccione un método de pago. "Adoptar un Torrent": tu nombre de usuario o mensaje en el nombre de archivo de un torrent <div %(div_months)s>una vez cada 12 meses como miembro</div> Tu nombre de usuario o mención anónima en los créditos Acceso anticipado a nuevas funciones Telegram exclusivo con actualizaciones "detrás de cámaras" %(number)s descargas rápidas diarias ¡si realizas una donación este mes! <a %(a_api)s>JSON API</a> acceso a Estatus legendario en la preservación del conocimiento y la cultura de la humanidad Beneficios anteriores, más: Gana un <strong>%(percentage)s%% de descargas adicionales</strong> <a %(a_refer)s>refiriendo a amigos</a>. Artículos de SciDB <strong>ilimitados</strong> sin verificación Al hacer preguntas sobre la cuenta o donaciones, incluya su ID de cuenta, capturas de pantalla, recibos y toda la información posible. Solo revisamos nuestro correo electrónico cada 1-2 semanas, por lo que no incluir esta información retrasará cualquier resolución. Para conseguir aun más descargas, ¡<a %(a_refer)s>invita a tus amigos</a>! Somos un pequeño grupo de voluntarios. Podríamos tardar entre 1 y 2 semanas en responder. Ten en cuenta que el nombre de la cuenta o la imagen pueden parecer extraños. ¡No hay necesidad de preocuparse! Estas cuentas son administradas por nuestros socios de donación. Nuestras cuentas no han sido hackeadas. Dona <span %(span_cost)s></span> <span %(span_label)s></span> por 12 meses “%(tier_name)s” por 1 mes “%(tier_name)s” por 24 meses “%(tier_name)s” por 3 meses “%(tier_name)s” por 48 meses “%(tier_name)s” por 6 meses “%(tier_name)s” por 96 meses “%(tier_name)s” Aún puedes cancelar la donación durante el proceso de pago. Haz clic en el botón “Donar" para confirmar esta donación. <strong>Nota importante:</strong> Los precios de las criptomonedas pueden fluctuar mucho, a veces hasta un 20%% en unos pocos minutos. Esto sigue siendo inferior a las tarifas en las que incurrimos con muchos proveedores de pagos, que a menudo cobran entre un 50%%y un 60%% por trabajar con una "organización benéfica en la sombra" como nosotros. <u>Si nos envías el recibo con el precio original que pagaste, igualmente abonaremos en tu cuenta la membresía elegida</u> (siempre que el recibo no tenga más de unas pocas horas). ¡Apreciamos que estés dispuesto a soportar cosas así para apoyarnos! ❤️ ❌ Algo salió mal. Vuelve a cargar la página e inténtalo de nuevo. <span %(span_circle)s>1</span>Compra Bitcoin con Paypal <span %(span_circle)s>2</span>Transfiere los Bitcoin a nuestra dirección ✅ Redirigiendo a la página de donaciones… Dona aquí Por favor, espere al menos <span %(span_hours)s>24 horas</span> (y actualice esta página) antes de contactarnos. Si desea hacer una donación (cualquier cantidad) sin ser miembro, no dude en utilizar esta dirección de Monero (XMR): %(address)s. Después de enviar tu tarjeta regalo, nuestro sistema automatizado la confirmará en unos minutos. Si esto no funciona, intenta reenviar tu tarjeta de regalo (<a %(a_instr)s>instrucciones</a>). Si aun así no funciona, envíanos un correo electrónico y Anna lo revisará manualmente (esto puede tardar algunos días) y asegúrate de mencionar si ya intentaste reenviarlo. Ejemplo: Utiliza el <a %(a_form)s>formulario oficial de Amazon.com</a> para enviarnos una tarjeta de regalo de %(amount)s a la dirección de correo electrónico que aparece a continuación. “Para” correo electrónico destinatario en el formulario: Tarjeta de regalo de Amazon No podemos aceptar otros métodos de tarjetas de regalo, <strong>sólo las enviadas directamente desde el formulario oficial en Amazon.com</strong>. No podemos devolver tu tarjeta de regalo si no utilizas este formulario. Usar solo una vez. Único de tu cuenta, no la compartas. Esperando tarjeta de regalo... (actualiza la página para consultar) Abra la <a %(a_href)s>página de donación con código QR</a>. Escanee el código QR con la aplicación de Alipay, o presione el botón para abrir la aplicación de Alipay. Por favor, sea paciente; la página puede tardar un poco en cargar ya que está en China. <span %(style)s>3</span>Realizar donación (escanee el código QR o presione el botón) Comprar moneda PYUSD en PayPal Comprar Bitcoin (BTC) en Cash App Compre un poco más (recomendamos %(more)s más) que la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Usted se quedará con lo que sobre. Ir a la página de “Bitcoin” (BTC) en Cash App. Transfiera el Bitcoin a nuestra dirección Para donaciones pequeñas (menos de $25), es posible que necesite usar Rush o Priority. Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de dólares a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado. Los servicios exprés son cómodos, pero tienen un precio más alto. Puedes usar esto en lugar de un intercambio de criptomonedas si quieres hacer una donación mayor rápidamente y no te importe pagar una comisión de entre 5 y 10 dólares. Asegúrate de enviar la cantidad exacta de criptomonedas que se muestra en la página de donaciones, no la cantidad en dólares estadounidenses. De lo contrario, no podremos procesar automáticamente tu membresía y se restará la tarifa. A veces la confirmación puede tardar hasta 24 horas, así que asegúrese de actualizar esta página (incluso si ha expirado). Instrucciones para tarjeta de crédito/débito Dona a través de nuestra página de tarjeta de crédito/débito Algunos de los pasos mencionan las billeteras de criptomonedas, pero no te preocupes, no es necesario que aprendas nada sobre criptomonedas para esto. Instrucciones de %(coin_name)s Escanee este código QR con su aplicación Crypto Wallet para completar rápidamente los detalles de pago Escanear el código QR para pagar Sólo ofrecemos soporte para la versión estándar de criptomonedas, nada de redes exóticas ni versiones de monedas. Confirmar la transacción puede llevar hasta una hora, dependiendo de la moneda. Dona %(amount)s en <a %(a_page)s>esta página</a>. Esta donación ha caducado. Cancélala y crea una nueva. Si ya has pagado: Sí, mandé mi recibo por correo electrónico Si el tipo de cambio de las criptomonedas fluctuó durante la transacción, asegúrese de incluir el recibo que muestre el tipo de cambio original. Realmente apreciamos que se haya tomado la molestia de usar criptomonedas, ¡nos ayuda mucho! ❌ Algo salió mal. Por favor vuelve a cargar la página e inténtalo de nuevo. <span %(span_circle)s>%(circle_number)s</span>Mándanos el recibo por correo electrónico Si tienes algún problema, comunícate con nosotros a %(email)s e incluye tanta información como sea posible (como capturas de pantalla). ✅ ¡Gracias por tu donación! Anna activará manualmente tu membresía dentro de unos días. Manda un recibo o una captura de pantalla a tu dirección de verificación personal: Cuando hayas enviado tu recibo por correo electrónico, haz clic en este botón para que Anna pueda revisarlo manualmente (esto puede tardar algunos días): Envíe un recibo o captura de pantalla a su dirección personal de verificación. NO utilice esta dirección de correo electrónico para su donación por PayPal. Cancelar Sí, por favor, cancelar ¿Estás seguro que deseas cancelar? No canceles si ya has pagado. ❌ Algo salió mal. Vuelve a cargar la página e intenta de nuevo. Hacer una nueva donación ✅ Tu donación ha sido cancelada. Fecha: %(date)s Identificador: %(id)s Pedir de nuevo Estado: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses, incluyendo un descuento de %(discounts)s%%)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses)</span> 1. Ingresa tu correo electrónico. 2. Selecciona tu método de pago. 3. Selecciona tu método de pago de nuevo. 4. Selecciona la billetera "autohospedada". 5. Haz clic en "Confirmo propiedad". 6. Deberías recibir un recibo por correo electrónico. Envíanoslo y confirmaremos tu donación lo antes posible. (es posible que desees cancelar y crear una nueva donación) Las instrucciones de pago han caducado. Si deseas hacer otra donación, utiliza el botón "Pedir de nuevo" que se encuentra arriba. Ya has pagado. Si deseas revisar las instrucciones de pago de todos modos, haz clic aquí: Mostrar instrucciones de pago anteriores Si la página de donaciones se bloquea, pruebe con una conexión a internet diferente (por ejemplo, VPN o internet del teléfono). Lamentablemente, a menudo solo se puede acceder a la página de Alipay desde <strong>China continental</strong>. Es posible que tengas que desactivar temporalmente tu VPN o usar una VPN para China continental (o Hong Kong también funciona a veces). <span %(span_circle)s>1</span>Donar con Alipay Doná la cantidad total de %(total)s utilizando <a %(a_account)s>esta cuenta de Alipay</a> Instrucciones de Alipay <span %(span_circle)s>1</span>Transfiere a una de nuestras cuentas de criptomonedas Dona la cantidad total de %(total)s a una de estas direcciones: Instrucciones para criptomonedas Sigue las instrucciones para comprar Bitcoin (BTC). Sólo necesitas comprar la cantidad que deseas donar,%(total)s. Ingresa nuestra dirección de Bitcoin (BTC) como destinatario y sigue las instrucciones para enviar tu donación de %(total)s: <span %(span_circle)s>1</span>Donar con Pix Dona la cantidad total de %(total)s usando <a %(a_account)s>esta cuenta de Pix Instrucciones de Pix <span %(span_circle)s>1</span>Donar con WeChat Done la cantidad total de %(total)s utilizando <a %(a_account)s>esta cuenta WeChat</a> instrucciones para WeChat Utilice cualquiera de los siguientes servicios exprés de “tarjeta de crédito a Bitcoin”, que solo toman unos minutos: Dirección BTC / Bitcoin (billetera externa): Importe en BTC / Bitcoin: Complete los siguientes detalles en el formulario: Si alguno de estos datos no está actualizado, envíanos un correo electrónico para comunicárnoslo. Por favor, use esta <span %(underline)s>cantidad exacta</span>. Su costo total podría ser mayor debido a las tarifas de la tarjeta de crédito. Para cantidades pequeñas, esto puede ser más que nuestro descuento, desafortunadamente. (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s) (mínimo: %(minimum)s, sin verificación para la primera transacción) (mínimo: %(minimum)s) (mínimo: %(minimum)s dependiendo del país, sin verificación para la primera transacción) Sigue las instrucciones para comprar monedas PYUSD (PayPal USD). Compra un poco más (recomendamos %(more)s más) que la cantidad que estás donando (%(amount)s), para cubrir las tarifas de transacción. Lo que sobra lo guardarás. Ve a la página "PYUSD" en tu aplicación o sitio web de PayPal. Presiona el botón “Transferir” %(icon)s, y luego “Enviar”. Estado de actualización Para restablecer el cronómetro, simplemente crea una nueva donación. Asegúrese de usar el importe de BTC a continuación, <em>NO</em> euros o dólares, de lo contrario no recibiremos el importe correcto y no podremos confirmar automáticamente su membresía. Comprar Bitcoin (BTC) en Revolut Compre un poco más (recomendamos %(more)s más) que la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Usted se quedará con lo que sobre. Vaya a la página de “Crypto” en Revolut para comprar Bitcoin (BTC). Transfiera el Bitcoin a nuestra dirección Para donaciones pequeñas (menos de $25) es posible que necesite usar Rush o Priority. Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de euros a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado. Estado: 1 2 Guía paso por paso Consulta la guía paso a paso a continuación. De lo contrario, podrías perder el acceso a esta cuenta! Si aún no lo has hecho, anota tu clave secreta para iniciar sesión: Gracias por tu donación! Tiempo restante: Donación Transferir %(amount)s a %(account)s Esperando confirmación (actualiza la página para consultar)… Esperando transferencia (actualiza la página para consultar)… Anteriormente Las descargas rápidas de las últimas 24 horas cuentan para el límite diario. Las descargas desde Servidores Asociados Rápidos están marcadas con %(icon)s. Últimas 18 horas Ningún archivo descargado aún. Los archivos descargados no son públicamente visibles. Todos los tiempos están en UTC. Archivos descargados Si descargaste un archivo por descargas rápidas y lentas se mostrará dos veces. No te preocupes demasiado, hay muchas personas descargando de los sitios web a los que enlazamos, y es extremadamente raro meterse en problemas. Sin embargo, para estar seguro, recomendamos usar una VPN (de pago) o <a %(a_tor)s>Tor</a> (gratis). ¿He descargado 1984 de George Orwell, vendrá la policía a mi puerta? ¡Tú eres Anna! ¿Quién es Anna? Tenemos una API JSON estable para miembros, para obtener una URL de descarga rápida: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentación dentro del JSON). Para otros casos de uso, como iterar a través de todos nuestros archivos, construir búsquedas personalizadas, y así sucesivamente, recomendamos <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Los datos en bruto se pueden explorar manualmente <a %(a_explore)s>a través de archivos JSON</a>. Nuestra lista de torrents en bruto también se puede descargar como <a %(a_torrents)s>JSON</a>. ¿Tienen una API? No alojamos ningún material con derechos de autor aquí. Somos un motor de búsqueda y, como tal, solo indexamos metadatos que ya están disponibles públicamente. Al descargar de estas fuentes externas, le sugerimos que verifique las leyes en su jurisdicción con respecto a lo que está permitido. No somos responsables del contenido alojado por otros. Si tienes quejas sobre lo que ves aquí, lo mejor es contactar con el sitio web original. Regularmente incorporamos sus cambios en nuestra base de datos. Si realmente crees que tienes una queja válida de DMCA a la que debamos responder, por favor completa el <a %(a_copyright)s>formulario de reclamación de DMCA / Derechos de Autor</a>. Tomamos tus quejas en serio y te responderemos lo antes posible. ¿Cómo informo sobre una infracción de derechos de autor? Aquí hay algunos libros que tienen un significado especial para el mundo de las bibliotecas en la sombra y la preservación digital: ¿Cuáles son tus libros favoritos? También nos gustaría recordar a todos que todo nuestro código y datos son completamente de código abierto. Esto es único para proyectos como el nuestro: no conocemos ningún otro proyecto con un catálogo tan masivo que también sea completamente de código abierto. Damos la bienvenida a cualquiera que piense que gestionamos mal nuestro proyecto a tomar nuestro código y datos y crear su propia biblioteca fantasma. No lo decimos por despecho ni nada parecido; realmente creemos que sería increíble, ya que elevaría el nivel para todos y preservaría mejor el legado de la humanidad. ¡Odio cómo estás manejando este proyecto! Nos encantaría que la gente instalara <a %(a_mirrors)s>espejos</a> y lo apoyaremos financieramente. ¿Cómo puedo ayudar? De hecho lo hacemos. Nuestra inspiración para recopilar metadatos es el objetivo de Aaron Swartz de “una página web para cada libro jamás publicado”, para lo cual creó <a %(a_openlib)s>Open Library</a>. Ese proyecto ha tenido éxito, pero nuestra posición única nos permite obtener metadatos que ellos no pueden. Otra inspiración fue nuestro deseo de saber <a %(a_blog)s>cuántos libros hay en el mundo</a>, para poder calcular cuántos libros nos quedan por salvar. ¿Recopilas metadatos? Tenga en cuenta que mhut.org bloquea ciertos rangos de IP, por lo que podría ser necesario usar una VPN. <strong>Android:</strong> Haz clic en el menú de tres puntos en la parte superior derecha y selecciona “Añadir a la pantalla de inicio”. <strong>iOS:</strong> Haz clic en el botón “Compartir” en la parte inferior y selecciona “Agregar a la pantalla de inicio”. No tenemos una aplicación móvil oficial, pero se puede instalar este sitio web como una aplicación. ¿Tienen una aplicación móvil? Por favor, envíelos al <a %(a_archive)s>Internet Archive</a>. Ellos los preservarán adecuadamente. ¿Cómo puedo donar libros u otros materiales físicos? ¿Cómo solicito libros? <a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizaciones regulares <a %(a_software)s>Software de Anna</a> — nuestro código abierto <a %(a_datasets)s>Datasets</a> — sobre los datos <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternativos ¿Hay más recursos sobre el Archivo de Anna? <a %(a_translate)s>Traducir en el Software de Anna</a> — nuestro sistema de traducción <a %(a_wikipedia)s>Wikipedia</a> — más sobre nosotros (¡por favor ayuda a mantener esta página actualizada, o crea una en tu propio idioma!) Selecciona las configuraciones que te gusten, deja el cuadro de búsqueda vacío, haz clic en “Buscar” y luego guarda la página en tus favoritos usando la función de marcadores de tu navegador. ¿Cómo guardo mis configuraciones de búsqueda? Damos la bienvenida a los investigadores de seguridad para buscar vulnerabilidades en nuestros sistemas. Somos grandes defensores de la divulgación responsable. Contáctenos <a %(a_contact)s>aquí</a>. Actualmente no podemos otorgar recompensas por errores, excepto por vulnerabilidades que tengan el <a %(a_link)s>potencial de comprometer nuestro anonimato</a>, para las cuales ofrecemos recompensas en el rango de $10k-50k. Nos gustaría ofrecer un alcance más amplio para las recompensas por errores en el futuro. Ten en cuenta que los ataques de ingeniería social están fuera de alcance. Si estás interesado en la seguridad ofensiva y quieres ayudar a archivar el conocimiento y la cultura del mundo, asegúrate de contactarnos. Hay muchas maneras en las que puedes ayudar. ¿Tienen un programa de divulgación responsable? Literalmente no tenemos suficientes recursos para ofrecer descargas de alta velocidad a todo el mundo, por mucho que nos gustaría. Si un benefactor rico quisiera dar un paso adelante y proporcionarnos esto, sería increíble, pero hasta entonces, estamos haciendo nuestro mejor esfuerzo. Somos un proyecto sin fines de lucro que apenas puede sostenerse a través de donaciones. Por eso implementamos dos sistemas para descargas gratuitas, con nuestros socios: servidores compartidos con descargas lentas, y servidores ligeramente más rápidos con una lista de espera (para reducir el número de personas descargando al mismo tiempo). También tenemos <a %(a_verification)s>verificación del navegador</a> para nuestras descargas lentas, porque de lo contrario los bots y los raspadores las abusarán, haciendo las cosas aún más lentas para los usuarios legítimos. Tenga en cuenta que, al usar el Navegador Tor, es posible que deba ajustar su configuración de seguridad. En la opción más baja, llamada “Estándar”, el desafío de Cloudflare turnstile tiene éxito. En las opciones más altas, llamadas “Más seguro” y “El más seguro”, el desafío falla. Para archivos grandes, a veces las descargas lentas pueden interrumpirse a la mitad. Recomendamos usar un gestor de descargas (como JDownloader) para reanudar automáticamente las descargas grandes. ¿Por qué son tan lentas las descargas lentas? Preguntas Frecuentes (FAQ) Utiliza el <a %(a_list)s>generador de lista de torrents</a> para generar una lista de torrents que más necesitan ser compartidos, dentro de los límites de tu espacio de almacenamiento. Sí, consulta la página de <a %(a_llm)s>datos de LLM</a>. La mayoría de los torrents contienen los archivos directamente, lo que significa que puedes instruir a los clientes de torrents para que solo descarguen los archivos necesarios. Para determinar qué archivos descargar, puedes <a %(a_generate)s>generar</a> nuestros metadatos, o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Desafortunadamente, algunas colecciones de torrents contienen archivos .zip o .tar en la raíz, en cuyo caso necesitas descargar todo el torrent antes de poder seleccionar archivos individuales. Todavía no hay herramientas fáciles de usar para filtrar torrents, pero agradecemos las contribuciones. (Sin embargo, tenemos <a %(a_ideas)s>algunas ideas</a> para este último caso.) Respuesta larga: Respuesta corta: no fácilmente. Tratamos de mantener la duplicación o superposición mínima entre los torrents en esta lista, pero esto no siempre se puede lograr y depende en gran medida de las políticas de las bibliotecas fuente. Para las bibliotecas que publican sus propios torrents, está fuera de nuestras manos. Para los torrents lanzados por el Archivo de Anna, deduplicamos solo en base al hash MD5, lo que significa que diferentes versiones del mismo libro no se deduplican. Sí. Estos son en realidad archivos PDF y EPUB, solo que no tienen una extensión en muchos de nuestros torrents. Hay dos lugares en los que puedes encontrar los metadatos de los archivos torrent, incluidos los tipos/extensiones de archivos: 1. Cada colección o lanzamiento tiene sus propios metadatos. Por ejemplo, los <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> tienen una base de datos de metadatos correspondiente alojada en el sitio web de Libgen.rs. Normalmente enlazamos a recursos de metadatos relevantes desde la <a %(a_datasets)s>página del conjunto de datos</a> de cada colección. 2. Recomendamos <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Estas contienen un mapeo para cada registro en Anna’s Archive a sus archivos torrent correspondientes (si están disponibles), bajo “torrent_paths” en el JSON de ElasticSearch. Algunos clientes de torrents no soportan tamaños de piezas grandes, que muchos de nuestros torrents tienen (para los más nuevos ya no lo hacemos, ¡aunque es válido según las especificaciones!). Así que prueba con un cliente diferente si te encuentras con este problema, o queja a los creadores de tu cliente de torrents. Me gustaría ayudar a compartir, pero no tengo mucho espacio en disco. Los torrents son demasiado lentos; ¿puedo descargar los datos directamente de ustedes? ¿Puedo descargar solo un subconjunto de los archivos, como un idioma o tema en particular? ¿Cómo manejan los duplicados en los torrents? ¿Puedo obtener la lista de torrents en formato JSON? No veo archivos PDF o EPUB en los torrents, ¿solo archivos binarios? ¿Qué hago? ¿Por qué mi cliente de torrents no puede abrir algunos de sus archivos torrent/enlaces magnéticos? FAQ de Torrents ¿Cómo subo libros nuevos? Por favor, vea <a %(a_href)s>este excelente proyecto</a>. ¿Tiene un monitor de tiempo de actividad? ¿Qué es el Archivo de Anna? Hazte miembro para utilizar las descargas rápidas. Ahora aceptamos tarjetas de regalo de Amazon, tarjetas de crédito y débito, criptomonedas, Alipay y WeChat. Te has quedado sin descargas rápidas hoy. Acceso Descargas por hora en los últimos 30 días. Promedio por hora: %(hourly)s. Promedio diario: %(daily)s. Trabajamos con compañeros para hacer nuestras colecciones disponibles fácilmente y libremente a todos. Creemos que todos tienen el derecho de la sabiduría colectiva de la humanidad, y <a %(a_search)s>no a costa de los autores</a>. Los datasets utilizados en el Archivo de Anna son completamente abiertos y se pueden crear reflejos de forma masiva mediante torrents. <a %(a_datasets)s>Más información...</a> Archivo a largo plazo Base de datos completa Buscar Libros, artículos, revistas, cómics, registros de biblioteca, metadatos,… Todos nuestro <a %(a_code)s>código</a> y <a %(a_datasets)s>datos</a> son completamente de código abierto. <span %(span_anna)s>El Archivo de Anna</span> es un proyecto sin ánimo de lucro con dos objetivos: <li><strong>Preservación:</strong> Respaldar todo el conocimiento y la cultura de la humanidad.</li><li><strong>Acceso:</strong> Hacer este conocimiento y esta cultura disponibles para todo el mundo.</li> Tenemos la colección más grande del mundo de datos de texto de alta calidad. <a %(a_llm)s>Más información...</a> Datos de entrenamiento de LLM 🪩 Reflejos: se buscan voluntarios Si ejecuta un procesador de pagos anónimo de alto riesgo, contáctenos. También buscamos personas que quieran colocar pequeños anuncios de buen gusto. Todos los ingresos se destinan a nuestros esfuerzos de preservación. Preservación Estimamos que hemos preservado aproximadamente un <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% de los libros del mundo</a>. Preservamos libros, artículos, cómics, revistas y más, recopilando este material de varias <a href="https://es.wikipedia.org/wiki/Biblioteca_fantasma">bibliotecas fantasma</a>, bibliotecas oficiales y otras colecciones en un solo lugar. Todos estos datos son preservados para siempre al hacerlos fáciles de duplicar en masa — usando torrents — y resultando en múltiples copias por todo el mundo. Algunas bibliotecas fantasma ya hacen esto (p.ej. Sci-Hub, Library Genesis), mientras que el Archivo de Anna “libera” otras bibliotecas que no ofrecen distribución en masa (p.ej. Z-Library) o directamente no son bibliotecas fantasma (p.ej. Internet Archive, DuXiu). Esta amplia distribución, combinada con el código abierto, hace nuestra página web resiliente a ser retirada, y garantiza la preservación a largo plazo del conocimiento y la cultura de la humanidad. Descubre más sobre <a href="/datasets">nuestros datasets</a>. Si eres <a %(a_member)s>miembro</a>, no se requiere verificación del navegador. 🧬&nbsp;SciDB es una continuación de Sci-Hub. SciDB Abrir DOI Sci-Hub a <a %(a_paused)s>pausado</a> la subida de nuevos artículos. Acceso directo a %(count)s artículos académicos 🧬&nbsp;SciDB es una continuación de Sci-Hub, con su interfaz familiar y visualización directa de PDFs. Ingrese su DOI para ver. Tenemos la colección completa de Sci-Hub, así como nuevos artículos. La mayoría se pueden ver directamente con una interfaz familiar, similar a Sci-Hub. Algunos se pueden descargar a través de fuentes externas, en cuyo caso mostramos enlaces a esas. Puedes ser de enorme ayuda sembrando torrents. <a %(a_torrents)s> Conocer mas…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Buscando voluntarios Como un proyecto sin fines de lucro y de código abierto, siempre estamos buscando personas que nos ayuden. Descargas IPFS Lista por %(by)s, creado <span %(span_time)s>%(time)s</span> Guardar ❌ Algo salió mal. Por favor vuelve a intentarlo. ✅ Guardado. Por favor recarga la página. Esta lista está vacía. editar Encuentra un archivo y abre la pestaña "Listas" para agregarlo a o quitarlo de la lista. Lista Cómo podemos ayudar Eliminación de solapamientos (deduplicación) Extracción de texto y metadatos OCR Podemos proporcionar acceso de alta velocidad a nuestras colecciones completas, así como a colecciones no publicadas. Este es un acceso a nivel empresarial que podemos proporcionar por donaciones en el rango de decenas de miles de USD. También estamos dispuestos a intercambiar esto por colecciones de alta calidad que aún no tenemos. Podemos reembolsarle si puede proporcionarnos un enriquecimiento de nuestros datos, como: ¡Apoye el archivo a largo plazo del conocimiento humano, mientras obtiene mejores datos para su modelo! <a %(a_contact)s>Contáctenos</a> para discutir cómo podemos trabajar juntos. Se entiende bien que los LLM prosperan con datos de alta calidad. Tenemos la colección más grande de libros, artículos, revistas, etc. en el mundo, que son algunas de las fuentes de texto de mayor calidad. Datos de LLM Escala y alcance únicos Nuestra colección contiene más de cien millones de archivos, incluidos revistas académicas, libros de texto y revistas. Alcanzamos esta escala combinando grandes repositorios existentes. Algunas de nuestras colecciones fuente ya están disponibles en masa (Sci-Hub y partes de Libgen). Otras fuentes las liberamos nosotros mismos. <a %(a_datasets)s>Datasets</a> muestra una visión completa. Nuestra colección incluye millones de libros, artículos y revistas de antes de la era del libro electrónico. Grandes partes de esta colección ya han sido OCRizadas y ya tienen poco solapamiento interno. Continuar Si has perdido tu clave, <a %(a_contact)s>contáctenos</a> por favor y brinda tanta información como sea posible. Es posible que tengas que crear temporalmente una nueva cuenta para contactarnos. <a %(a_account)s>Inicie sesión</a> para ver esta página.</a> Para evitar que los bots creen múltiples cuentas, necesitamos verificar tu navegador. Si te quedas atrapado en un bucle infinito, te recomendamos instalar <a %(a_privacypass)s>Privacy Pass</a>. También puede ayudar desactivar los bloqueadores de anuncios y otras extensiones del navegador. Iniciar sesión / Registrarse Archivo de Anna está temporalmente fuera de servicio por mantenimiento. Por favor, vuelva en una hora. Autor alternativo Descripción alternativa Edición alternativa Extensión alternativa Nombre de archivo alternativo Editorial alternativa Título alternativo fecha de lanzamiento en Anna's Archive Leer más… descripción Buscar en el Archivo de Anna por número CADAL SSNO Buscar en el Archivo de Anna por número DuXiu SSID Buscar en el Archivo de Anna por número DuXiu DXID Buscar el ISBN en el Archivo de Anna Buscar en el Archivo de Anna el número de OCLC (WorldCat) Buscar el ID de Open Library en el archivo de Anna Visor en línea de Archivo de Anna %(count)s páginas afectadas Después de descargar: Es posible que haya una versión mejor de este archivo disponible en %(link)s Descarga masiva mediante torrent colección Utilice herramientas en línea para convertir entre formatos. Herramientas de conversión recomendadas: %(links)s Para archivos grandes, recomendamos usar un gestor de descargas para evitar interrupciones. Gestores de descargas recomendados: %(links)s Índice de eBooks de EBSCOhost (sólo para expertos) (haz clic también en “GET” en la parte superior) (haz clic en “GET” en la parte superior) Descargas externas Tienes %(remaining)s descargas restantes hoy. ¡Gracias por ser miembro! ❤️ Te has quedado sin descargas rápidas por hoy. Has descargado este archivo recientemente. Los enlaces mantendrán su validez durante un tiempo. Conviértase en <a %(a_membership)s>miembro</a> para apoyar la preservación a largo plazo de libros, artículos y más. Para mostrar nuestro agradecimiento por su apoyo obtendrá descargas rápidas. ❤️ 🚀 Descargas rápidas 🐢 Descargas lentas Tomar prestado de Internet Archive Puerta de enlace IPFS #%(num)d (es posible que debas intentarlo varias veces con IPFS) Libgen.li Libgen.rs Ficción Libgen.rs No Ficción sus anuncios son conocidos por contener software malicioso, así que use un bloqueador de anuncios o no haga clic en los anuncios “Enviar a Kindle” de Amazon “Enviar a Kobo/Kindle” de djazz MagzDB ManualsLib Nexus/STC (Los archivos Nexus/STC pueden ser poco fiables para descargar) No se encontró ninguna descarga. Todas las opciones de descarga tienen el mismo archivo, y deberían ser seguros de usar. Dicho esto, ten siempre cuidado al descargar archivos de Internet, especialmente desde sitios externos al Archivo de Anna. Por ejemplo, asegúrate de mantener tus dispositivos actualizados. (sin redirección) Abrir en nuestro visor (abrir en visor) Opción #%(num)d: %(link)s %(extra)s Buscar el registro original en CADAL Buscar manualmente en DuXiu Buscar el registro original en ISBNdb Buscar el registro original en WorldCat Buscar el registro original en Open Library Buscar el ISBN en otras bases de datos (sólo para usuarios con impresión deshabilitada) PubMed Necesitará un lector de ebooks o PDF para abrir el archivo, dependiendo del formato del archivo. Lectores de ebooks recomendados: %(links)s Archivo de Anna 🧬 SciDB Sci-Hub: %(doi)s (el DOI asociado podría no estar disponible en Sci-Hub) Puede enviar archivos PDF y EPUB a su Kindle o Kobo eReader. Herramientas recomendadas: %(links)s Más información en las <a %(a_slow)s>Preguntas Frecuentes</a>. Apoya a los autores y bibliotecas Si te gusta esto y puedes permitírtelo, considera comprar el original o apoyar directamente a los autores. Si está disponible en tu biblioteca local, considera pedirlo prestado gratis allí. Las descargas del Servidor Asociado están temporalmente deshabilitadas para este archivo. torrent De socios fiables. Z-Library TOR Z-Library (requiere el navegador TOR) mostrar descargas externas <span class="font-bold">❌Este archivo puede tener problemas y se ha ocultado de una biblioteca de origen.</span> A veces es a petición del titular de los derechos de autor, otras porque existe una alternativa mejor, pero a veces se debe a un problema con el propio archivo. Es posible que aún pueda descargarse, pero recomendamos que antes busques un archivo alternativo. Más detalles: Si aún quieres descargar este archivo, asegúrate de sólo usar software confiable y actualizado para abrirlo. comentarios de metadatos AA: Buscar "%(name)s" en el Archivo de Anna Explorador de Códigos: Ver en el Explorador de Códigos “%(name)s” URL: Página web: Si tiene este archivo y aún no está disponible en el Archivo de Anna, considera <a %(a_request)s>subirlo aquí</a>. Archivo de préstamos digitales controlados por Internet Archive “%(id)s” Este es un registro de un archivo de Internet Archive, no un archivo directamente descargable. Puedes intentar pedir prestado el libro (enlace a continuación) o utilizar esta URL cuando <a %(a_request)s>solicites un archivo</a>. Mejorar metadatos Registro de metadatos CADAL SSNO %(id)s Esto es un registro de metadatos, no un archivo descargable. Puedes utilizar esta URL cuando <a %(a_request)s>solicites un archivo</a>. Registro de metadatos DuXiu SSID %(id)s Registro de metadatos de %(id)s en ISBNdb Registro de metadatos de MagzDB ID %(id)s Registro de metadatos de Nexus/STC ID %(id)s Registro de metadatos del número %(id)s en OCLC (WorldCat) Registro de metadatos de %(id)s en Open Library Archivo Sci-Hub “%(id)s” No encontrado “%(md5_input)s” no se encuentra en nuestra base de datos. Agregar comentario (%(count)s) Puedes obtener el md5 de la URL, por ejemplo. MD5 de una mejor versión de este archivo (si aplica). Rellena esto si hay otro archivo que coincida estrechamente con este archivo (misma edición, misma extensión de archivo si puedes encontrar uno), que la gente debería usar en lugar de este archivo. Si conoces una mejor versión de este archivo fuera de Anna’s Archive, entonces por favor <a %(a_upload)s>súbela</a>. Algo salió mal. Por favor, recargue la página e intente de nuevo. Dejó un comentario. Puede tardar un minuto en aparecer. Por favor, usa el <a %(a_copyright)s>formulario de reclamación de DMCA / Derechos de Autor</a>. Describe el problema (requerido) Si este archivo tiene gran calidad, ¡puede discutir cualquier cosa sobre él aquí! Si no, por favor use el botón “Reportar problema con el archivo”. Gran calidad del archivo (%(count)s) Calidad del archivo Aprenda cómo <a %(a_metadata)s>mejorar los metadatos</a> de este archivo usted mismo. Descripción del problema Por favor, <a %(a_login)s>inicia sesión</a>. ¡Me encantó este libro! ¡Ayuda a la comunidad puntuando la calidad de este archivo! 🙌 Algo salió mal. Por favor, recargue la página e intente de nuevo. Reportar problema del archivo (%(count)s) Gracias por enviar su informe. Se mostrará en esta página y será revisado manualmente por Anna (hasta que tengamos un sistema de moderación adecuado). Dejar comentario Enviar reporte ¿Qué está mal con este archivo? Prestamos (%(count)s) Comentarios (%(count)s) Descargas (%(count)s) Explorar metadatos (%(count)s) Listas (%(count)s) Estadísticas (%(count)s) Para información sobre este archivo en particular, consulte su <a %(a_href)s>archivo JSON</a>. Este es un archivo gestionado por la biblioteca de <a %(a_ia)s>Préstamo Digital Controlado de IA</a> e indexado por el Archivo de Anna para búsqueda. Para información sobre los diversos Datasets que hemos compilado, vea la <a %(a_datasets)s>página de Datasets</a>. Metadatos del registro vinculado Mejorar metadatos en Open Library Un “MD5 del archivo” es un hash que se calcula a partir del contenido del archivo y es razonablemente único basado en ese contenido. Todas las bibliotecas en la sombra que hemos indexado aquí utilizan principalmente MD5s para identificar archivos. Un archivo puede aparecer en múltiples bibliotecas en la sombra. Para información sobre los diversos Datasets que hemos compilado, vea la <a %(a_datasets)s>página de Datasets</a>. Reportar calidad del archivo Total de descargas: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadatos checos %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Advertencia: múltiples registros vinculados: Cuando miras un libro en el Archivo de Anna, puedes ver varios campos: título, autor, editorial, edición, año, descripción, nombre de archivo y más. Todas esas piezas de información se llaman <em>metadatos</em>. Como combinamos libros de varias <em>bibliotecas fuente</em>, mostramos los metadatos disponibles en esa biblioteca fuente. Por ejemplo, para un libro que obtuvimos de Library Genesis, mostraremos el título de la base de datos de Library Genesis. A veces un libro está presente en <em>múltiples</em> bibliotecas fuente, que pueden tener diferentes campos de metadatos. En ese caso, simplemente mostramos la versión más larga de cada campo, ya que esa probablemente contiene la información más útil. Aún así, mostraremos los otros campos debajo de la descripción, por ejemplo, como "título alternativo" (pero solo si son diferentes). También extraemos <em>códigos</em> como identificadores y clasificadores de la biblioteca fuente. <em>Identificadores</em> representan de manera única una edición particular de un libro; ejemplos son ISBN, DOI, Open Library ID, Google Books ID o Amazon ID. <em>Clasificadores</em> agrupan varios libros similares; ejemplos son Dewey Decimal (DCC), UDC, LCC, RVK o GOST. A veces estos códigos están explícitamente vinculados en las bibliotecas fuente, y a veces podemos extraerlos del nombre de archivo o descripción (principalmente ISBN y DOI). Podemos usar identificadores para encontrar registros en <em>colecciones solo de metadatos</em>, como OpenLibrary, ISBNdb o WorldCat/OCLC. Hay una pestaña específica de <em>metadatos</em> en nuestro motor de búsqueda si deseas explorar esas colecciones. Usamos registros coincidentes para completar campos de metadatos faltantes (por ejemplo, si falta un título), o por ejemplo, como "título alternativo" (si ya existe un título). Para ver exactamente de dónde provienen los metadatos de un libro, consulta la pestaña <em>“Detalles técnicos”</em> en la página de un libro. Tiene un enlace al JSON sin procesar de ese libro, con enlaces al JSON sin procesar de los registros originales. Para más información, consulta las siguientes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Buscar (pestaña de metadatos)</a>, <a %(a_codes)s>Explorador de códigos</a> y <a %(a_example)s>Ejemplo de JSON de metadatos</a>. Finalmente, todos nuestros metadatos pueden ser <a %(a_generated)s>generados</a> o <a %(a_downloaded)s>descargados</a> como bases de datos ElasticSearch y MariaDB. Antecedentes ¡Puedes ayudar a la preservación de libros mejorando los metadatos! Primero, lee la información de fondo sobre metadatos en el Archivo de Anna, y luego aprende cómo mejorar los metadatos a través de la vinculación con Open Library, y gana una membresía gratuita en el Archivo de Anna. Mejorar metadatos Entonces, si encuentras un archivo con malos metadatos, ¿cómo deberías arreglarlo? Puedes ir a la biblioteca fuente y seguir sus procedimientos para corregir los metadatos, pero ¿qué hacer si un archivo está presente en múltiples bibliotecas fuente? Hay un identificador que se trata de manera especial en el Archivo de Anna. <strong>¡El campo md5 de annas_archive en Open Library siempre anula todos los demás metadatos!</strong> Retrocedamos un poco primero y aprendamos sobre Open Library. Open Library fue fundada en 2006 por Aaron Swartz con el objetivo de "una página web para cada libro jamás publicado". Es una especie de Wikipedia para metadatos de libros: todos pueden editarla, tiene una licencia libre y puede descargarse en bloque. Es una base de datos de libros que está más alineada con nuestra misión; de hecho, el Archivo de Anna se ha inspirado en la visión y vida de Aaron Swartz. En lugar de reinventar la rueda, decidimos redirigir a nuestros voluntarios hacia Open Library. Si ves un libro que tiene metadatos incorrectos, puedes ayudar de la siguiente manera: Ten en cuenta que esto solo funciona para libros, no para artículos académicos u otros tipos de archivos. Para otros tipos de archivos, aún recomendamos encontrar la biblioteca fuente. Puede tomar algunas semanas para que los cambios se incluyan en Anna’s Archive, ya que necesitamos descargar el último volcado de datos de Open Library y regenerar nuestro índice de búsqueda.  Ve al <a %(a_openlib)s>sitio web de Open Library</a>. Encuentra el registro correcto del libro. <strong>ADVERTENCIA:</strong> asegúrate de seleccionar la <strong>edición</strong> correcta. En Open Library, tienes "obras" y "ediciones". Una "obra" podría ser "Harry Potter y la piedra filosofal". Una "edición" podría ser: La primera edición de 1997 publicada por Bloomsbery con 256 páginas. La edición de bolsillo de 2003 publicada por Raincoast Books con 223 páginas. La traducción polaca de 2000 “Harry Potter I Kamie Filozoficzn” por Media Rodzina con 328 páginas. Todas esas ediciones tienen diferentes ISBN y contenidos distintos, ¡así que asegúrate de seleccionar la correcta! ¡Edite el registro (o créelo si no existe), y añada tanta información útil como pueda! Ya que está aquí, aproveche para hacer que el registro sea realmente increíble. En “Números de ID” selecciona “Anna’s Archive” y añade el MD5 del libro de Anna’s Archive. Este es el largo string de letras y números después de “/md5/” en la URL. Intenta encontrar otros archivos en Anna’s Archive que también coincidan con este registro, y añádelos también. En el futuro, podremos agruparlos como duplicados en la página de búsqueda de Anna’s Archive. Cuando termines, anota la URL que acabas de actualizar. Una vez que hayas actualizado al menos 30 registros con los MD5 de Anna’s Archive, envíanos un <a %(a_contact)s>correo electrónico</a> con la lista. Te daremos una membresía gratuita para Anna’s Archive, para que puedas hacer este trabajo más fácilmente (y como agradecimiento por tu ayuda). Estas deben ser ediciones de alta calidad que añadan cantidades sustanciales de información, de lo contrario, tu solicitud será rechazada. Tu solicitud también será rechazada si alguna de las ediciones es revertida o corregida por los moderadores de Open Library. Vinculación con Open Library Si te involucras significativamente en el desarrollo y las operaciones de nuestro trabajo, podemos discutir compartir más de los ingresos por donaciones contigo, para que los despliegues según sea necesario. Solo pagaremos por el alojamiento una vez que tengas todo configurado y hayas demostrado que puedes mantener el archivo actualizado con las actualizaciones. Esto significa que tendrás que pagar los primeros 1-2 meses de tu bolsillo. Tu tiempo no será compensado (y el nuestro tampoco), ya que esto es trabajo puramente voluntario. Estamos dispuestos a cubrir los gastos de alojamiento y VPN, inicialmente hasta 200 dólares por mes. Esto es suficiente para un servidor de búsqueda básico y un proxy protegido por DMCA. Gastos de alojamiento Por favor, <strong>no nos contactes</strong> para pedir permiso o para preguntas básicas. ¡Las acciones hablan más que las palabras! Toda la información está disponible, así que adelante con la configuración de tu espejo. Siéntete libre de publicar tickets o solicitudes de fusión en nuestro Gitlab cuando te encuentres con problemas. Es posible que necesitemos construir algunas características específicas del espejo contigo, como el cambio de marca de “Anna’s Archive” al nombre de tu sitio web, (inicialmente) deshabilitar cuentas de usuario, o enlazar de nuevo a nuestro sitio principal desde las páginas de libros. Una vez que tengas tu espejo en funcionamiento, por favor contáctanos. Nos encantaría revisar tu OpSec, y una vez que esté sólido, enlazaremos a tu espejo y comenzaremos a trabajar más de cerca contigo. ¡Gracias de antemano a cualquiera que esté dispuesto a contribuir de esta manera! No es para los débiles de corazón, pero solidificaría la longevidad de la biblioteca verdaderamente abierta más grande en la historia de la humanidad. Empezando Para aumentar la resiliencia del Archivo de Anna, estamos buscando voluntarios para ejecutar espejos. Su versión está claramente distinguida como un espejo, por ejemplo, “Archivo de Bob, un espejo del Archivo de Anna”. Está dispuesto a asumir los riesgos asociados con este trabajo, que son significativos. Tiene un profundo entendimiento de la seguridad operativa requerida. El contenido de <a %(a_shadow)s>estos</a> <a %(a_pirate)s>posts</a> le es evidente. Inicialmente no le daremos acceso a las descargas de nuestro servidor asociado, pero si las cosas van bien, podemos compartir eso con usted. Usted ejecuta la base de código de código abierto del Archivo de Anna y actualiza regularmente tanto el código como los datos. Estás dispuesto a contribuir a nuestra <a %(a_codebase)s>base de código</a> — en colaboración con nuestro equipo — para que esto suceda. Estamos buscando esto: Espejos: llamado a voluntarios Haz otra donación. Ningunas donaciones todavía. <a %(a_donate)s>Hacer mi primera donación.</a> Los detalles de donaciones no son públicamente visibles. Mis donaciones 📡 Para realizar una duplicación masiva de nuestra colección, consulta las páginas de <a %(a_datasets)s>Datasets</a> y <a %(a_torrents)s>Torrents</a>. Descargas realizadas desde su dirección IP en las últimas 24 horas: %(count)s. 🚀 Para obtener descargas más rápidas y saltarte verificaciones de navegador, <a %(a_membership)s>hazte socio</a>. Descargar desde una página web asociada Si lo deseas, puedes seguir navegando por el Archivo de Anna en una pestaña diferente mientras esperas (si tu navegador admite la actualización de pestañas en segundo plano). Si lo deseas, puedes esperar a que se carguen varias páginas de descarga al mismo tiempo (pero por favor, solo descarga un archivo a la vez por servidor). Una vez que obtenga un enlace de descarga, será válido por varias horas. ¡Gracias por esperar, esto mantiene el sitio web accesible de forma gratuita para todos! 😊 <a %(a_main)s>&lt; Todos los enlaces de descarga para este archivo</a> ❌ Las descargas lentas no están disponibles a través de las VPN de Cloudflare ni desde las direcciones IP de Cloudflare. ❌ Las descargas lentas solo están disponibles a través del sitio web oficial. Visita %(websites)s. <a %(a_download)s>📚 Descargar ahora</a> Para dar a todos la oportunidad de descargar archivos de forma gratuita, necesitas esperar antes de poder descargar este archivo. Por favor, espere <span %(span_countdown)s>%(wait_seconds)s</span> segundos para descargar este archivo. Atención: ha habido muchas descargas desde tu dirección IP en las últimas 24 horas. Las descargas pueden ser más lentas de lo habitual. Si está utilizando una VPN, una conexión a internet compartida o su ISP comparte IPs, esta advertencia podría deberse a eso. Guardar ❌ Algo salió mal. Por favor vuelve a intentarlo. ✅ Guardado. Vuelve a cargar la página. Cambiar tu nombre público. Tu identificador (la parte después del "#") no se puede cambiar. Perfil creado <span %(span_time)s>%(time)s</span> editar Listas Crea una lista nueva localizando un archivo y abriendo la pestaña "Listas". No hay listas aún El perfil no fue encontrado. Perfil Ahora mismo no podemos aceptar peticiones de libros. No nos envíes tus peticiones por correo electrónico. Por favor realice sus solicitudes en los foros de Z-Library o Libgen. Registro en el Archivo de Anna DOI: %(doi)s Descargar SciDB Nexus/STC No hay vista previa disponible aún. Descargue el archivo desde <a %(a_path)s>Archivo de Anna</a>. Para apoyar la accesibilidad y la preservación a largo plazo del conocimiento humano, conviértete en <a %(a_donate)s>miembro</a>. Como un bono, 🧬&nbsp;SciDB carga más rápido para los miembros, sin ningún límite. ¿No funciona? Prueba a <a %(a_refresh)s>actualizar</a>. Sci-Hub Agregar campo de búsqueda específico Buscar descripción y metadatos de comentarios Año de publicación Avanzado Acceso Contenido Mostrar Lista Tabla Tipo de archivo Idioma Ordenar por Más grande Más relevante Más reciente (tamaño del archivo) (código abierto) (año de publicación) Más antiguo Aleatorio Más pequeño Fuente extraído y de código abierto por AA Préstamo Digital (%(count)s) Artículos periodísticos (%(count)s) Encontramos coincidencias en: %(in)s. Puedes referirte a la URL encontrada allí al <a %(a_request)s>solicitar un archivo</a>. Metadatos (%(count)s) Para explorar el índice de búsqueda por códigos, utiliza el <a %(a_href)s>Explorador de Códigos</a>. El índice de búsqueda se actualiza mensualmente. Actualmente incluye entradas hasta %(last_data_refresh_date)s. Para más información técnica, consulta la página %(link_open_tag)sDatasets</a>. Excluir Incluir solo Sin verificar más… Próximo … Previo Este índice de búsqueda incluye actualmente metadatos de la biblioteca de Préstamos Digitales Controlados de Internet Archive. <a %(a_datasets)s>Más sobre nuestros datasets</a>. Para más bibliotecas de préstamo digital, consulta <a %(a_wikipedia)s>Wikipedia</a> y <a %(a_mobileread)s>MobileRead Wiki</a>. Para reclamaciones de derechos de autor/DMCA <a %(a_copyright)s>haz clic aquí</a>. Tiempo de descarga Error durante la búsqueda. Intenta <a %(a_reload)s>recargar la página</a>. Si el problema persiste, envíanos un correo electrónico a %(email)s. Descarga rápida De hecho, cualquiera puede ayudar a preservar estos archivos sembrando nuestra <a %(a_torrents)s>lista unificada de torrents</a>. ➡️ A veces esto ocurre incorrectamente cuando el servidor de búsqueda es lento. En tales casos, <a %(a_attrs)s>recargar</a> puede ayudar. ❌ Este archivo puede tener problemas. ¿Buscas artículos? Este índice de búsqueda incluye actualmente metadatos de varias fuentes. <a %(a_datasets)s>Más sobre nuestros datasets</a>. Existen muchísimas fuentes de metadatos para obras escritas en todo el mundo. <a %(a_wikipedia)s>Esta página de Wikipedia</a> es un buen comienzo, pero si conoces otras buenas listas, háznoslo saber. Para metadatos, mostramos los registros originales. No realizamos ninguna combinación. Actualmente contamos con el catálogo abierto de libros, artículos y otros trabajos escritos más completo del mundo. Reflejamos Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>y más</a>. <span %(classname)s>No se encontraron archivos.</span> Intente con menos términos de búsqueda o diferentes filtros. Resultados %(from)s-%(to)s (%(total)s total) Si encuentras otras "bibliotecas fantasma" que deberíamos reflejar, o si tienes alguna pregunta, contáctanos en %(email)s. %(num)d coincidencias parciales %(num)d+ coincidencias parciales Escribe en el cuadro para buscar archivos en bibliotecas de préstamo digital. Escribe en el cuadro para buscar en nuestro catálogo de %(count)s archivos descargables directamente, que <a %(a_preserve)s>conservamos para siempre</a>. Escribe en el cuadro para buscar. Escribe en el cuadro para buscar en nuestro catálogo de %(count)s artículos académicos y periodísticos, que <a %(a_preserve)s>preservamos para siempre</a>. Escribe en el cuadro para buscar metadatos de bibliotecas. Esto puede resultar útil al <a %(a_request)s>solicitar un archivo</a>. Consejo: utiliza los atajos de teclado "/" (enfoque de búsqueda), "enter" (buscar), "j" (arriba), "k" (abajo) para una navegación más rápida. Estos son registros de metadatos, <span %(classname)s>no</span> archivos descargables. Configuración de búsqueda Buscar Préstamos digitales Descargar Artículos periodísticos Metadatos Nueva búsqueda %(search_input)s - Buscar La búsqueda tardó demasiado, lo que significa que podrían aparecer resultados inexactos. A veces, <a %(a_reload)s>recargar</a> la página ayuda. La búsqueda tardó demasiado, lo que es común para consultas amplias. Los términos en el filtro pueden no ser precisos. Para subidas grandes (más de 10.000 ficheros) que no sean aceptadas en Libgen o Z-Library, por favor contacte con nosotros por %(a_email)s. Para Libgen.li, asegúrese de primero iniciar sesión en <a %(a_forum)s>su foro</a> con el nombre de usuario %(username)s y la contraseña %(password)s, y luego regrese a su <a %(a_upload_page)s>página de carga</a>. De momento, sugerimos subir libros nuevos a las bifurcaciones de Library Genesis. Aquí hay una <a %(a_guide)s>guía útil</a>. Ten en cuenta que ambas bifurcaciones que incluimos en este sitio web usan el mismo sistema de subida. Para cargas pequeñas (hasta 10,000 archivos) por favor súbalos tanto a %(first)s como a %(second)s. Alternativamente, puedes subirlos a Z-Library <a %(a_upload)s>aquí</a>. Para subir artículos académicos, por favor también (además de Library Genesis) súbelos a <a %(a_stc_nexus)s>STC Nexus</a>. Son la mejor biblioteca fantasma para nuevos artículos. Aún no los hemos integrado, pero lo haremos en algún momento. Puedes usar su <a %(a_telegram)s>bot de subida en Telegram</a>, o contactar la dirección listada en su mensaje fijado si tienes demasiados archivos para subir de esta manera. <span %(label)s>Trabajo voluntario intenso (recompensas de USD$50-USD$5,000):</span> si puedes dedicar mucho tiempo y/o recursos a nuestra misión, nos encantaría trabajar más de cerca contigo. Eventualmente, puedes unirte al equipo interno. Aunque tenemos un presupuesto ajustado, podemos otorgar <span %(bold)s>💰 recompensas monetarias</span> por el trabajo más intenso. <span %(label)s>Trabajo voluntario ligero:</span> si solo puedes dedicar unas pocas horas de vez en cuando, todavía hay muchas maneras en las que puedes ayudar. Recompensamos a los voluntarios consistentes con <span %(bold)s>🤝 membresías a Anna’s Archive</span>. El Archivo de Anna depende de voluntarios como usted. Aceptamos todos los niveles de compromiso y tenemos dos categorías principales de ayuda que buscamos: Si no puedes ofrecer tu tiempo como voluntario, aún puedes ayudarnos mucho <a %(a_donate)s>donando dinero</a>, <a %(a_torrents)s>sembrando nuestros torrents</a>, <a %(a_uploading)s>subiendo libros</a> o <a %(a_help)s>contándole a tus amigos sobre el Archivo de Anna</a>. <span %(bold)s>Empresas:</span> ofrecemos acceso directo de alta velocidad a nuestras colecciones a cambio de una donación a nivel empresarial o intercambio por nuevas colecciones (por ejemplo, nuevos escaneos, datasets OCR, enriquecimiento de nuestros datos). <a %(a_contact)s>Contáctanos</a> si este es tu caso. Consulta también nuestra <a %(a_llm)s>página de LLM</a>. Recompensas Siempre estamos buscando personas con sólidas habilidades en programación o seguridad ofensiva para involucrarse. Puedes hacer una contribución significativa en la preservación del legado de la humanidad. Como agradecimiento, ofrecemos membresías por contribuciones sólidas. Como un gran agradecimiento, ofrecemos recompensas monetarias por tareas particularmente importantes y difíciles. Esto no debe considerarse como un reemplazo de un trabajo, pero es un incentivo adicional y puede ayudar con los costos incurridos. La mayor parte de nuestro código es de código abierto, y pediremos lo mismo de tu código al otorgar la recompensa. Hay algunas excepciones que podemos discutir de manera individual. Las recompensas se conceden a la primera persona que complete una tarea. No dudes en comentar un ticket de recompensa para informar a los demás de que estás trabajando en algo, de modo que otros puedan esperar o ponerse en contacto contigo para formar un equipo. Pero ten en cuenta que los demás también son libres de trabajar en ello e intentar ganarte. Sin embargo, no concedemos recompensas por un trabajo descuidado. Si se realizan dos presentaciones de alta calidad cerca una de la otra (dentro de un día o dos), podríamos optar por otorgar recompensas a ambas, a nuestra discreción, por ejemplo, 100%% para la primera presentación y 50%% para la segunda presentación (así que 150%% en total). Para las recompensas más grandes (especialmente las de scraping), por favor contáctanos cuando hayas completado ~5%% de la tarea, y estés seguro de que tu método se escalará hasta el hito completo. Tendrás que compartir tu método con nosotros para que podamos dar retroalimentación. Además, de esta manera podemos decidir qué hacer si hay varias personas acercándose a una recompensa, como potencialmente otorgarla a múltiples personas, animar a las personas a formar equipo, etc. ADVERTENCIA: las tareas de alta recompensa son <span %(bold)s>difíciles</span> — podría ser prudente comenzar con las más fáciles. Ve a nuestra <a %(a_gitlab)s>lista de problemas en Gitlab</a> y ordena por “Prioridad de etiqueta”. Esto muestra aproximadamente el orden de las tareas que nos importan. Las tareas sin recompensas explícitas aún son elegibles para membresía, especialmente aquellas marcadas como “Aceptado” y “Favorito de Anna”. Podrías querer comenzar con un “Proyecto inicial”. Voluntariado ligero Ahora también tenemos un canal de Matrix sincronizado en %(matrix)s. Si tienes unas horas libres, puedes ayudar de varias maneras. Asegúrate de unirte al <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Como muestra de agradecimiento, generalmente otorgamos 6 meses de “Bibliotecario Suertudo” por hitos básicos, y más por trabajo voluntario continuo. Todos los hitos requieren trabajo de alta calidad: el trabajo descuidado nos perjudica más de lo que nos ayuda y lo rechazaremos. Por favor, <a %(a_contact)s>envíanos un correo</a> cuando alcances un hito. Enlaces o capturas de pantalla de solicitudes que cumpliste %(links)s. Cumplir con solicitudes de libros (o artículos, etc.) en los foros de Z-Library o Library Genesis. No tenemos nuestro propio sistema de solicitudes de libros, pero espejamos esas bibliotecas, así que mejorarlas también mejora el Archivo de Anna. Hito Tarea Depende de la tarea. Pequeñas tareas publicadas en nuestro <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Usualmente para membresía, a veces para pequeñas recompensas. Tareas pequeñas publicadas en nuestro grupo de chat de voluntarios. Asegúrate de dejar un comentario sobre los problemas que resuelvas, para que otros no dupliquen tu trabajo. Enlaces de registros que mejoraste %(links)s. Puedes usar la <a %(a_list)s>lista de problemas de metadata aleatorios</a> como punto de partida. Mejorar metadatos <a %(a_metadata)s>enlazándolos</a> con Open Library. Estos deberían mostrarte informando a alguien sobre Anna’s Archive, y ellos agradeciéndote. Enlaces o capturas de pantalla %(links)s. Difundiendo la palabra de Anna’s Archive. Por ejemplo, recomendando libros en AA, enlazando a nuestras publicaciones de blog o, en general, dirigiendo a las personas a nuestro sitio web. Traducir completamente un idioma (si no estaba cerca de completarse ya.) <a %(a_translate)s>Traducir</a> el sitio web. Enlace al historial de ediciones mostrando que hiciste contribuciones significativas. Mejorar la página de Wikipedia del Archivo de Anna en tu idioma. Incluye información de la página de Wikipedia de AA en otros idiomas, y de nuestro sitio web y blog. Añade referencias a AA en otras páginas relevantes. Voluntariado y Recompensas 