msgid "layout.index.invalid_request"
msgstr "Solicitud no válida. Visita %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Biblioteca de Préstamos de Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " y "

msgid "layout.index.header.tagline_and_more"
msgstr "y más"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Reflejamos %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Recopilamos y publicamos de forma abierta %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Todo nuestro código y datos son completamente de código abierto."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp; La biblioteca verdaderamente abierta más grande de la historia de la humanidad."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;artículos— preservados para siempre."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La biblioteca de código abierto y datos abiertos más grande del mundo. ⭐️&nbsp;Refleja Sci-Hub, Library Genesis, Z-Library, y más. 📈&nbsp;%(book_any)s libros, %(journal_article)s artículos, %(book_comic)s cómics, %(magazine)s revistas — preservados para siempre."

msgid "layout.index.header.tagline_short"
msgstr "📚 La biblioteca de datos y código abierto más grande del mundo.<br>⭐️ Refleja Scihub, Libgen, Zlib y más."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadatos incorrectos (p. ej. título, descripción, imagen de portada)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemas de descarga (p. ej. no se puede conectar, mensaje de error, muy lento)"

msgid "common.md5_report_type_mapping.broken"
msgstr "El archivo no se puede abrir (p. ej. archivo dañado, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Mala calidad (p. ej. problemas de formato, mala calidad de escaneo, páginas faltantes)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / se debe eliminar el archivo (p. ej. publicidad, contenido abusivo)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamación de derechos de autor"

msgid "common.md5_report_type_mapping.other"
msgstr "Otro"

msgid "common.membership.tier_name.bonus"
msgstr "Descargas adicionales"

msgid "common.membership.tier_name.2"
msgstr "Ratón de Biblioteca Brillante"

msgid "common.membership.tier_name.3"
msgstr "Bibliotecario Afortunado"

msgid "common.membership.tier_name.4"
msgstr "Coleccionista de Datos Deslumbrante"

msgid "common.membership.tier_name.5"
msgstr "Archivista increíble"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s adicionales)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "sin pagar"

msgid "common.donation.order_processing_status_labels.1"
msgstr "pagado"

msgid "common.donation.order_processing_status_labels.2"
msgstr "cancelado"

msgid "common.donation.order_processing_status_labels.3"
msgstr "expiró"

msgid "common.donation.order_processing_status_labels.4"
msgstr "Esperando confirmación de Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "inválido"

msgid "page.donate.title"
msgstr "Dona aquí"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Tiene una <a %(a_donation)s>donación</a> en curso. Por favor termine o cancele sus donaciones actuales antes de realizar una nueva donación."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Ver todas mis donaciones</a>"

msgid "page.donate.header.text1"
msgstr "El Archivo de Anna es un proyecto de código abierto, datos abiertos y sin fines de lucro. Donando y haciéndote miembro nos ayudas con el desarrollo y las operaciones. A todos nuestros miembros: ¡Gracias por ayudarnos a seguir! ❤️"

msgid "page.donate.header.text2"
msgstr "Para más información, revisa la sección <a %(a_donate)s>Preguntas frecuentes sobre donaciones </a>."

msgid "page.donate.refer.text1"
msgstr "Para conseguir aun más descargas, ¡<a %(a_refer)s>invita a tus amigos</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Obtienes un %(percentage)s%% de descargas rápidas adicionales porque fuiste referido por el usuario %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Esto se aplica a todo el periodo de membresía."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s descargas rápidas diarias"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "¡si realizas una donación este mes!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mes"

msgid "page.donate.buttons.join"
msgstr "Únete"

msgid "page.donate.buttons.selected"
msgstr "Seleccionado"

msgid "page.donate.buttons.up_to_discounts"
msgstr "Descuentos de hasta %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "Artículos de SciDB <strong>ilimitados</strong> sin verificación"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> acceso a"

msgid "page.donate.perks.refer"
msgstr "Gana un <strong>%(percentage)s%% de descargas adicionales</strong> <a %(a_refer)s>refiriendo a amigos</a>."

msgid "page.donate.perks.credits"
msgstr "Tu nombre de usuario o mención anónima en los créditos"

msgid "page.donate.perks.previous_plus"
msgstr "Beneficios anteriores, más:"

msgid "page.donate.perks.early_access"
msgstr "Acceso anticipado a nuevas funciones"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram exclusivo con actualizaciones \"detrás de cámaras\""

msgid "page.donate.perks.adopt"
msgstr "\"Adoptar un Torrent\": tu nombre de usuario o mensaje en el nombre de archivo de un torrent <div %(div_months)s>una vez cada 12 meses como miembro</div>"

msgid "page.donate.perks.legendary"
msgstr "Estatus legendario en la preservación del conocimiento y la cultura de la humanidad"

msgid "page.donate.expert.title"
msgstr "Acceso para expertos"

msgid "page.donate.expert.contact_us"
msgstr "contáctanos"

msgid "page.donate.small_team"
msgstr "Somos un pequeño grupo de voluntarios. Podríamos tardar entre 1 y 2 semanas en responder."

msgid "page.donate.expert.unlimited_access"
msgstr "Acceso <strong>ilimitado</strong>de alta velocidad"

msgid "page.donate.expert.direct_sftp"
msgstr "Servidores <strong>SFTP</strong> directos"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donación o intercambio a nivel empresarial por nuevas colecciones (p.ej. nuevos escaneos, conjuntos de datos OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Aceptamos donaciones grandes de instituciones o individuos adinerados. "

msgid "page.donate.header.large_donations"
msgstr "Para donaciones superiores a $5000 dólares, contáctanos directamente a %(email)s."

msgid "page.donate.header.recurring"
msgstr "Ten en cuenta que, aunque las membresías en esta página son “mensuales”, son donaciones únicas (no recurrentes). Consulta las <a %(faq)s>Preguntas frecuentes sobre donación</a>."

msgid "page.donate.without_membership"
msgstr "Si desea hacer una donación (cualquier cantidad) sin ser miembro, no dude en utilizar esta dirección de Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Por favor, seleccione un método de pago."

msgid "page.donate.discount"
msgstr "- %(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(no disponible temporalmente)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Tarjeta de regalo de %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Tarjeta bancaria (usando la app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Criptomonedas %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Tarjeta de crédito/débito"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (normal)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Tarjeta / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Crédito/débito/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Tarjeta bancaria"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Tarjeta de Crédito/Débito (backup)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Tarjeta de crédito/débito 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay/WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "Con criptomonedas puedes donar con BTC, ETH, XMR, y SOL. Usa esta opción si estás familiarizado con las criptomonedas."

msgid "page.donate.payment.desc.crypto2"
msgstr "Con criptomonedas puedes donar usando BTC, ETH, XMR y más."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si está utilizando criptomonedas por primera vez, sugerimos usar %(options)s para comprar y donar Bitcoin (la criptomoneda original y más utilizada)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Para donar con PayPal, vamos a usar PayPal Crypto, que nos permite permanecer anónimos. Agradecemos que te tomes el tiempo para aprender a donar con este método, nos ayudas mucho."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Dona usando PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Dona usando Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si tienes Cash App, ¡esta es la forma más fácil de donar!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Ten en cuenta que para transacciones inferiores a %(amount)s, Cash App puede cobrar una tarifa de %(fee)s. Por %(amount)s o más, ¡es gratis!"

msgid "page.donate.payment.desc.revolut"
msgstr "Donar con Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "¡Si tienes Revolut, esta es la forma más fácil de donar!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Dona con tarjeta de crédito o débito."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay y Apple Pay también podrían funcionar."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Ten en cuenta que para donaciones pequeñas las tarifas de la tarjeta de crédito pueden eliminar nuestro descuento de %(discount)s%%, por lo que recomendamos suscripciones más largas."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Ten en cuenta que para pequeñas donaciones las tarifas son elevadas, por lo que recomendamos suscripciones más largas."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, podes comprar Bitcoin con una tarjeta de crédito/débito o una cuenta bancaria y luego nos podes donar esos Bitcoin. De esa manera podemos permanecer seguros y anónimos al aceptar tu donación."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance está disponible en casi todos los países y es compatible con la mayoría de los bancos y tarjetas de crédito/débito. Esta es actualmente nuestra principal recomendación. Agradecemos que te tomes el tiempo de aprender cómo donar usando este método, ya que nos ayuda mucho."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Donar usando tu cuenta normal de PayPal."

msgid "page.donate.payment.desc.givebutter"
msgstr "Done utilizando tarjeta de crédito/débito, PayPal o Venmo. Puedes elegir entre estas opciones en la siguiente página."

msgid "page.donate.payment.desc.amazon"
msgstr "Dona usando una tarjeta de regalo de Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Ten en cuenta que debemos redondear a cantidades aceptadas por nuestros revendedores (mínimo %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Solo admitimos Amazon.com, no otros sitios web de Amazon. Por ejemplo, .de, .co.uk, .ca NO son compatibles."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> Esta opción es para %(amazon)s. Si desea usar otro sitio web de Amazon, selecciónelo arriba."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Este método utiliza un proveedor de criptomonedas como intermediario. Esto puede resultar un poco confuso, así que utiliza este método únicamente si otros métodos de pago no funcionan. Tampoco funciona en todos los países."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona usando una tarjeta de crédito/débito, a través de la app de Alipay (muy fácil de configurar)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Instalar la app de Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instala la app de Alipay desde la <a %(a_app_store)s>App Store de Apple</a> o <a %(a_play_store)s>Play Store de Google</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Regístrate usando tu número de teléfono."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "No se requieren más detalles personales."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Añadir tarjeta bancaria"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Admitidas: Visa, MasterCard, JCB, Diners Club y Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Consulta <a %(a_alipay)s>esta guía</a> para más información."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "No podemos aceptar tarjetas de crédito/débito directamente porque los bancos no quieren trabajar con nosotros. ☹ Sin embargo, hay varias formas de usar tarjetas de crédito/débito de todos modos, utilizando otros métodos de pago:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Tarjeta de regalo de Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Regálanos una tarjeta de regalo de Amazon.com usando tu tarjeta de crédito/débito."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay acepta tarjetas de crédito/débito internacionales. Consulta <a %(a_alipay)s>esta guía</a> para más información."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) admite tarjetas de crédito/débito internacionales. En la aplicación WeChat, accede a \"Yo => Servicios => Monedero => Agregar una tarjeta\". Si no lo ves, actívalo usando \"Yo => Configuración => General => Herramientas => Weixin Pay => Habilitar\"."

msgid "page.donate.ccexp.crypto"
msgstr "Puedes comprar criptomonedas usando tu tarjeta de crédito/débito."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servicios exprés de criptomonedas"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Los servicios exprés son cómodos, pero tienen un precio más alto."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Puedes usar esto en lugar de un intercambio de criptomonedas si quieres hacer una donación mayor rápidamente y no te importe pagar una comisión de entre 5 y 10 dólares."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Asegúrate de enviar la cantidad exacta de criptomonedas que se muestra en la página de donaciones, no la cantidad en dólares estadounidenses."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "De lo contrario, no podremos procesar automáticamente tu membresía y se restará la tarifa."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(mínimo: %(minimum)s dependiendo del país, sin verificación para la primera transacción)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(mínimo: %(minimum)s, sin verificación para la primera transacción)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(mínimo: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si alguno de estos datos no está actualizado, envíanos un correo electrónico para comunicárnoslo."

msgid "page.donate.payment.desc.bmc"
msgstr "Para tarjetas de crédito, tarjetas de débito, Apple Pay y Google Pay, utilizamos “Buy Me a Coffee\" (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). En su sistema, un “Coffee” equivale a 5 $, por lo que tu donación se redondeará al múltiplo de 5 más cercano."

msgid "page.donate.duration.intro"
msgstr "Selecciona la duración de tu suscripción."

msgid "page.donate.duration.1_mo"
msgstr "1 mes"

msgid "page.donate.duration.3_mo"
msgstr "3 meses"

msgid "page.donate.duration.6_mo"
msgstr "6 meses"

msgid "page.donate.duration.12_mo"
msgstr "12 meses"

msgid "page.donate.duration.24_mo"
msgstr "24 meses"

msgid "page.donate.duration.48_mo"
msgstr "48 meses"

msgid "page.donate.duration.96_mo"
msgstr "96 meses"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>después<span %(span_discount)s></span> del descuento</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Este método de pago tiene un mínimo de %(amount)s. Selecciona una duración o un método de pago diferente."

msgid "page.donate.buttons.donate"
msgstr "Haz una donación"

msgid "page.donate.payment.maximum_method"
msgstr "Este método de pago solo permite un máximo de %(amount)s. Selecciona una duración o un método de pago diferente."

msgid "page.donate.login2"
msgstr "Para convertirte en miembro, <a %(a_login)s>inicia sesión o regístrate</a>. ¡Gracias por tu apoyo!"

msgid "page.donate.payment.crypto_select"
msgstr "Selecciona tu criptomoneda preferida:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(cantidad mínima más baja)"

msgid "page.donate.coinbase_eth"
msgstr "(para enviar Ethereum desde Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(advertencia: monto mínimo alto)"

msgid "page.donate.submit.confirm"
msgstr "Haz clic en el botón “Donar\" para confirmar esta donación."

msgid "page.donate.submit.button"
msgstr "Dona <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Aún puedes cancelar la donación durante el proceso de pago."

msgid "page.donate.submit.success"
msgstr "✅ Redirigiendo a la página de donaciones…"

msgid "page.donate.submit.failure"
msgstr "❌ Algo salió mal. Vuelve a cargar la página e inténtalo de nuevo."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mes"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "por 1 mes"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "por 3 meses"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "por 6 meses"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "por 12 meses"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "por 24 meses"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "por 48 meses"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "por 96 meses"

msgid "page.donate.submit.button.label.1_mo"
msgstr "por 1 mes “%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "por 3 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "por 6 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "por 12 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "por 24 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "por 48 meses “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "por 96 meses “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donación"

msgid "page.donation.header.date"
msgstr "Fecha: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses, incluyendo un descuento de %(discounts)s%%)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mes por %(duration)s meses)</span>"

msgid "page.donation.header.status"
msgstr "Estado: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificador: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Cancelar"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "¿Estás seguro que deseas cancelar? No canceles si ya has pagado."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Sí, por favor, cancelar"

msgid "page.donation.header.cancel.success"
msgstr "✅ Tu donación ha sido cancelada."

msgid "page.donation.header.cancel.new_donation"
msgstr "Hacer una nueva donación"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Algo salió mal. Vuelve a cargar la página e intenta de nuevo."

msgid "page.donation.header.reorder"
msgstr "Pedir de nuevo"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Ya has pagado. Si deseas revisar las instrucciones de pago de todos modos, haz clic aquí:"

msgid "page.donation.old_instructions.show_button"
msgstr "Mostrar instrucciones de pago anteriores"

msgid "page.donation.thank_you_donation"
msgstr "Gracias por tu donación!"

msgid "page.donation.thank_you.secret_key"
msgstr "Si aún no lo has hecho, anota tu clave secreta para iniciar sesión:"

msgid "page.donation.thank_you.locked_out"
msgstr "De lo contrario, podrías perder el acceso a esta cuenta!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Las instrucciones de pago han caducado. Si deseas hacer otra donación, utiliza el botón \"Pedir de nuevo\" que se encuentra arriba."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> Los precios de las criptomonedas pueden fluctuar mucho, a veces hasta un 20%% en unos pocos minutos. Esto sigue siendo inferior a las tarifas en las que incurrimos con muchos proveedores de pagos, que a menudo cobran entre un 50%%y un 60%% por trabajar con una \"organización benéfica en la sombra\" como nosotros. <u>Si nos envías el recibo con el precio original que pagaste, igualmente abonaremos en tu cuenta la membresía elegida</u> (siempre que el recibo no tenga más de unas pocas horas). ¡Apreciamos que estés dispuesto a soportar cosas así para apoyarnos! ❤️"

msgid "page.donation.expired"
msgstr "Esta donación ha caducado. Cancélala y crea una nueva."

msgid "page.donation.payment.crypto.top_header"
msgstr "Instrucciones para criptomonedas"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfiere a una de nuestras cuentas de criptomonedas"

msgid "page.donation.payment.crypto.text1"
msgstr "Dona la cantidad total de %(total)s a una de estas direcciones:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compra Bitcoin con Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Encuentre la sección \"Cripto\" en la web o app de PayPal. Por lo general, esto se encuentra en \"Finanzas\"."

msgid "page.donation.payment.paypal.text3"
msgstr "Sigue las instrucciones para comprar Bitcoin (BTC). Sólo necesitas comprar la cantidad que deseas donar,%(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfiere los Bitcoin a nuestra dirección"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Ve a la sección \"Bitcoin\" en la web o app de PayPal. Presiona el botón de \"Transferir\" %(transfer_icon)s, y pulsa \"Enviar\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Ingresa nuestra dirección de Bitcoin (BTC) como destinatario y sigue las instrucciones para enviar tu donación de %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instrucciones para tarjeta de crédito/débito"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona a través de nuestra página de tarjeta de crédito/débito"

msgid "page.donation.donate_on_this_page"
msgstr "Dona %(amount)s en <a %(a_page)s>esta página</a>."

msgid "page.donation.stepbystep_below"
msgstr "Consulta la guía paso a paso a continuación."

msgid "page.donation.status_header"
msgstr "Estado:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Esperando confirmación (actualiza la página para consultar)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Esperando transferencia (actualiza la página para consultar)…"

msgid "page.donation.time_left_header"
msgstr "Tiempo restante:"

msgid "page.donation.might_want_to_cancel"
msgstr "(es posible que desees cancelar y crear una nueva donación)"

msgid "page.donation.reset_timer"
msgstr "Para restablecer el cronómetro, simplemente crea una nueva donación."

msgid "page.donation.refresh_status"
msgstr "Estado de actualización"

msgid "page.donation.footer.issues_contact"
msgstr "Si tienes algún problema, comunícate con nosotros a %(email)s e incluye tanta información como sea posible (como capturas de pantalla)."

msgid "page.donation.expired_already_paid"
msgstr "Si ya has pagado:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "A veces la confirmación puede tardar hasta 24 horas, así que asegúrese de actualizar esta página (incluso si ha expirado)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Comprar moneda PYUSD en PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Sigue las instrucciones para comprar monedas PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Compra un poco más (recomendamos %(more)s más) que la cantidad que estás donando (%(amount)s), para cubrir las tarifas de transacción. Lo que sobra lo guardarás."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Ve a la página \"PYUSD\" en tu aplicación o sitio web de PayPal. Presiona el botón “Transferir” %(icon)s, y luego “Enviar”."

msgid "page.donation.transfer_amount_to"
msgstr "Transferir %(amount)s a %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Comprar Bitcoin (BTC) en Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Ir a la página de “Bitcoin” (BTC) en Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compre un poco más (recomendamos %(more)s más) que la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Usted se quedará con lo que sobre."

msgid "page.donation.cash_app_btc.step2"
msgstr "Transfiera el Bitcoin a nuestra dirección"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de dólares a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Para donaciones pequeñas (menos de $25), es posible que necesite usar Rush o Priority."

msgid "page.donation.revolut.step1"
msgstr "Comprar Bitcoin (BTC) en Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Vaya a la página de “Crypto” en Revolut para comprar Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Compre un poco más (recomendamos %(more)s más) que la cantidad que está donando (%(amount)s), para cubrir las tarifas de transacción. Usted se quedará con lo que sobre."

msgid "page.donation.revolut.step2"
msgstr "Transfiera el Bitcoin a nuestra dirección"

msgid "page.donation.revolut.step2.transfer"
msgstr "Haga clic en el botón “Enviar bitcoin” para hacer un “retiro”. Cambie de euros a BTC presionando el ícono %(icon)s. Ingrese la cantidad de BTC a continuación y haga clic en “Enviar”. Vea <a %(help_video)s>este video</a> si se queda atascado."

msgid "page.donation.revolut.btc_amount_below"
msgstr "Asegúrese de usar el importe de BTC a continuación, <em>NO</em> euros o dólares, de lo contrario no recibiremos el importe correcto y no podremos confirmar automáticamente su membresía."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Para donaciones pequeñas (menos de $25) es posible que necesite usar Rush o Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Utilice cualquiera de los siguientes servicios exprés de “tarjeta de crédito a Bitcoin”, que solo toman unos minutos:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Complete los siguientes detalles en el formulario:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Importe en BTC / Bitcoin:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Por favor, use esta <span %(underline)s>cantidad exacta</span>. Su costo total podría ser mayor debido a las tarifas de la tarjeta de crédito. Para cantidades pequeñas, esto puede ser más que nuestro descuento, desafortunadamente."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Dirección BTC / Bitcoin (billetera externa):"

msgid "page.donation.crypto_instructions"
msgstr "Instrucciones de %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Sólo ofrecemos soporte para la versión estándar de criptomonedas, nada de redes exóticas ni versiones de monedas. Confirmar la transacción puede llevar hasta una hora, dependiendo de la moneda."

msgid "page.donation.crypto_qr_code_title"
msgstr "Escanear el código QR para pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Escanee este código QR con su aplicación Crypto Wallet para completar rápidamente los detalles de pago"

msgid "page.donation.amazon.header"
msgstr "Tarjeta de regalo de Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Utiliza el <a %(a_form)s>formulario oficial de Amazon.com</a> para enviarnos una tarjeta de regalo de %(amount)s a la dirección de correo electrónico que aparece a continuación."

msgid "page.donation.amazon.only_official"
msgstr "No podemos aceptar otros métodos de tarjetas de regalo, <strong>sólo las enviadas directamente desde el formulario oficial en Amazon.com</strong>. No podemos devolver tu tarjeta de regalo si no utilizas este formulario."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Ingrese el importe exacto: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Por favor NO escribas tu propio mensaje."

msgid "page.donation.amazon.form_to"
msgstr "“Para” correo electrónico destinatario en el formulario:"

msgid "page.donation.amazon.unique"
msgstr "Único de tu cuenta, no la compartas."

msgid "page.donation.amazon.only_use_once"
msgstr "Usar solo una vez."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Esperando tarjeta de regalo... (actualiza la página para consultar)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Después de enviar tu tarjeta regalo, nuestro sistema automatizado la confirmará en unos minutos. Si esto no funciona, intenta reenviar tu tarjeta de regalo (<a %(a_instr)s>instrucciones</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Si aun así no funciona, envíanos un correo electrónico y Anna lo revisará manualmente (esto puede tardar algunos días) y asegúrate de mencionar si ya intentaste reenviarlo."

msgid "page.donation.amazon.example"
msgstr "Ejemplo:"

msgid "page.donate.strange_account"
msgstr "Ten en cuenta que el nombre de la cuenta o la imagen pueden parecer extraños. ¡No hay necesidad de preocuparse! Estas cuentas son administradas por nuestros socios de donación. Nuestras cuentas no han sido hackeadas."

msgid "page.donation.payment.alipay.top_header"
msgstr "Instrucciones de Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donar con Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Doná la cantidad total de %(total)s utilizando <a %(a_account)s>esta cuenta de Alipay</a>"

msgid "page.donation.page_blocked"
msgstr "Si la página de donaciones se bloquea, pruebe con una conexión a internet diferente (por ejemplo, VPN o internet del teléfono)."

msgid "page.donation.payment.alipay.error"
msgstr "Lamentablemente, a menudo solo se puede acceder a la página de Alipay desde <strong>China continental</strong>. Es posible que tengas que desactivar temporalmente tu VPN o usar una VPN para China continental (o Hong Kong también funciona a veces)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Realizar donación (escanee el código QR o presione el botón)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Abra la <a %(a_href)s>página de donación con código QR</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Escanee el código QR con la aplicación de Alipay, o presione el botón para abrir la aplicación de Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Por favor, sea paciente; la página puede tardar un poco en cargar ya que está en China."

msgid "page.donation.payment.wechat.top_header"
msgstr "instrucciones para WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donar con WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Done la cantidad total de %(total)s utilizando <a %(a_account)s>esta cuenta WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Instrucciones de Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donar con Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Dona la cantidad total de %(total)s usando <a %(a_account)s>esta cuenta de Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Mándanos el recibo por correo electrónico"

msgid "page.donation.footer.verification"
msgstr "Envíe un recibo o captura de pantalla a su dirección personal de verificación. NO utilice esta dirección de correo electrónico para su donación por PayPal."

msgid "page.donation.footer.text1"
msgstr "Manda un recibo o una captura de pantalla a tu dirección de verificación personal:"

msgid "page.donation.footer.crypto_note"
msgstr "Si el tipo de cambio de las criptomonedas fluctuó durante la transacción, asegúrese de incluir el recibo que muestre el tipo de cambio original. Realmente apreciamos que se haya tomado la molestia de usar criptomonedas, ¡nos ayuda mucho!"

msgid "page.donation.footer.text2"
msgstr "Cuando hayas enviado tu recibo por correo electrónico, haz clic en este botón para que Anna pueda revisarlo manualmente (esto puede tardar algunos días):"

msgid "page.donation.footer.button"
msgstr "Sí, mandé mi recibo por correo electrónico"

msgid "page.donation.footer.success"
msgstr "✅ ¡Gracias por tu donación! Anna activará manualmente tu membresía dentro de unos días."

msgid "page.donation.footer.failure"
msgstr "❌ Algo salió mal. Por favor vuelve a cargar la página e inténtalo de nuevo."

msgid "page.donation.stepbystep"
msgstr "Guía paso por paso"

msgid "page.donation.crypto_dont_worry"
msgstr "Algunos de los pasos mencionan las billeteras de criptomonedas, pero no te preocupes, no es necesario que aprendas nada sobre criptomonedas para esto."

msgid "page.donation.hoodpay.step1"
msgstr "1. Ingresa tu correo electrónico."

msgid "page.donation.hoodpay.step2"
msgstr "2. Selecciona tu método de pago."

msgid "page.donation.hoodpay.step3"
msgstr "3. Selecciona tu método de pago de nuevo."

msgid "page.donation.hoodpay.step4"
msgstr "4. Selecciona la billetera \"autohospedada\"."

msgid "page.donation.hoodpay.step5"
msgstr "5. Haz clic en \"Confirmo propiedad\"."

msgid "page.donation.hoodpay.step6"
msgstr "6. Deberías recibir un recibo por correo electrónico. Envíanoslo y confirmaremos tu donación lo antes posible."

msgid "page.donate.wait_new"
msgstr "Por favor, espere al menos <span %(span_hours)s>24 horas</span> (y actualice esta página) antes de contactarnos."

msgid "page.donate.mistake"
msgstr "Si cometió un error durante el pago no podemos hacer reembolsos, pero intentaremos corregirlo."

msgid "page.my_donations.title"
msgstr "Mis donaciones"

msgid "page.my_donations.not_shown"
msgstr "Los detalles de donaciones no son públicamente visibles."

msgid "page.my_donations.no_donations"
msgstr "Ningunas donaciones todavía. <a %(a_donate)s>Hacer mi primera donación.</a>"

msgid "page.my_donations.make_another"
msgstr "Haz otra donación."

msgid "page.downloaded.title"
msgstr "Archivos descargados"

msgid "page.downloaded.fast_partner_star"
msgstr "Las descargas desde Servidores Asociados Rápidos están marcadas con %(icon)s."

msgid "page.downloaded.twice"
msgstr "Si descargaste un archivo por descargas rápidas y lentas se mostrará dos veces."

msgid "page.downloaded.fast_download_time"
msgstr "Las descargas rápidas de las últimas 24 horas cuentan para el límite diario."

msgid "page.downloaded.times_utc"
msgstr "Todos los tiempos están en UTC."

msgid "page.downloaded.not_public"
msgstr "Los archivos descargados no son públicamente visibles."

msgid "page.downloaded.no_files"
msgstr "Ningún archivo descargado aún."

msgid "page.downloaded.last_18_hours"
msgstr "Últimas 18 horas"

msgid "page.downloaded.earlier"
msgstr "Anteriormente"

msgid "page.account.logged_in.title"
msgstr "Cuenta"

msgid "page.account.logged_out.title"
msgstr "Iniciar sesión / Registrarse"

msgid "page.account.logged_in.account_id"
msgstr "ID de cuenta: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Perfil público: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clave secreta (¡no la compartas!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "mostrar"

msgid "page.account.logged_in.membership_has_some"
msgstr "Membresía: <strong>%(tier_name)s</strong> hasta %(until_date)s <a %(a_extend)s>(extender)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Membresía: <strong>Ninguna</strong> <a %(a_become)s>(hacerse miembro)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Descargas rápidas usadas (últimas 24 horas): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "¿Qué descargas?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupo de Telegram exclusivo: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Únete aquí!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Mejora a un <a %(a_tier)s>nivel superior</a> para unirte a nuestro grupo."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Contacta con Anna en %(email)s si estás interesado en mejorar tu membresía a un nivel superior."

msgid "page.contact.title"
msgstr "Correo Electrónico"

msgid "page.account.logged_in.membership_multiple"
msgstr "Puedes combinar varias membresías (Las descargas rápidas cada 24 horas se sumarán)."

msgid "layout.index.header.nav.public_profile"
msgstr "Perfil público"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Archivos descargados"

msgid "layout.index.header.nav.my_donations"
msgstr "Mis donaciones"

msgid "page.account.logged_in.logout.button"
msgstr "Cerrar sesión"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Tu sesión ya está cerrada. Vuelve a cargar la página para iniciar sesión de nuevo."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Algo salió mal. Vuelve a cargar la página y prueba otra vez."

msgid "page.account.logged_out.registered.text1"
msgstr "¡Registro exitoso! Tu clave secreta es: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Guarda esta clave con cuidado. Si la pierdes, perderás acceso a tu cuenta."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Marcador.</strong> Puedes añadir esta página a los marcadores para recuperar tu clave.</li><li %(li_item)s><strong>Descargar.</strong> Haz clic en <a %(a_download)s>este enlace</a> para descargar tu clave.</li><li %(li_item)s><strong>Gestor de contraseñas.</strong> Utiliza un gestor de contraseñas para guardar la clave cuando la introduzcas debajo.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Introduce tu clave secreta para iniciar sesión:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clave secreta"

msgid "page.account.logged_out.key_form.button"
msgstr "Iniciar sesión"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clave secreta no válida. Verifica tu clave e inténtalo de nuevo, o registra una nueva cuenta."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "¡No pierdas tu clave!"

msgid "page.account.logged_out.register.header"
msgstr "¿Todavía no tienes una cuenta?"

msgid "page.account.logged_out.register.button"
msgstr "Registrar una cuenta nueva"

msgid "page.login.lost_key"
msgstr "Si has perdido tu clave, <a %(a_contact)s>contáctenos</a> por favor y brinda tanta información como sea posible."

msgid "page.login.lost_key_contact"
msgstr "Es posible que tengas que crear temporalmente una nueva cuenta para contactarnos."

msgid "page.account.logged_out.old_email.button"
msgstr "¿Tienes una cuenta basada en un correo electrónico viejo? Introduce tu <a %(a_open)s>correo electrónico aquí</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "editar"

msgid "page.list.edit.button"
msgstr "Guardar"

msgid "page.list.edit.success"
msgstr "✅ Guardado. Por favor recarga la página."

msgid "page.list.edit.failure"
msgstr "❌ Algo salió mal. Por favor vuelve a intentarlo."

msgid "page.list.by_and_date"
msgstr "Lista por %(by)s, creado <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Esta lista está vacía."

msgid "page.list.new_item"
msgstr "Encuentra un archivo y abre la pestaña \"Listas\" para agregarlo a o quitarlo de la lista."

msgid "page.profile.title"
msgstr "Perfil"

msgid "page.profile.not_found"
msgstr "El perfil no fue encontrado."

msgid "page.profile.header.edit"
msgstr "editar"

msgid "page.profile.change_display_name.text"
msgstr "Cambiar tu nombre público. Tu identificador (la parte después del \"#\") no se puede cambiar."

msgid "page.profile.change_display_name.button"
msgstr "Guardar"

msgid "page.profile.change_display_name.success"
msgstr "✅ Guardado. Vuelve a cargar la página."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo salió mal. Por favor vuelve a intentarlo."

msgid "page.profile.created_time"
msgstr "Perfil creado <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listas"

msgid "page.profile.lists.no_lists"
msgstr "No hay listas aún"

msgid "page.profile.lists.new_list"
msgstr "Crea una lista nueva localizando un archivo y abriendo la pestaña \"Listas\"."

msgid "blog.ai-copyright.title"
msgstr "La reforma de derechos de autor es necesaria para la seguridad nacional"

msgid "blog.ai-copyright.tldr"
msgstr "Resumen: Los modelos de lenguaje chinos (incluido DeepSeek) están entrenados en mi archivo ilegal de libros y documentos —el más grande del mundo—. Occidente necesita reformar la ley de derechos de autor como una cuestión de seguridad nacional."

msgid "blog.ai-copyright.subtitle"
msgstr "artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundo</a>"

msgid "blog.ai-copyright.text1"
msgstr "No hace mucho, las “bibliotecas fantasma” estaban desapareciendo. Sci-Hub, el enorme archivo ilegal de artículos académicos, había dejado de aceptar nuevas obras debido a demanda judiciales. “Z-Library” la mayor biblioteca ilegal de libros, vio a sus supuestos creadores arrestados por cargos de violación de derechos de autor. Increíblemente lograron escapar de su arresto, pero su biblioteca no está menos amenazada."

msgid "blog.ai-copyright.text2"
msgstr "Cuando Z-Library enfrentó su cierre, ya había respaldado toda su biblioteca y estaba buscando una plataforma para alojarla. Esa fue mi motivación para iniciar el Archivo de Anna: una continuación de la misión detrás de esas iniciativas anteriores. Desde entonces, hemos crecido hasta convertirnos en la biblioteca fantasma más grande del mundo, albergando más de 140 millones de textos con derechos de autor en numerosos formatos: libros, artículos académicos, revistas, periódicos y más."

msgid "blog.ai-copyright.text3"
msgstr "Mi equipo y yo somos ideólogos. Creemos que preservar y alojar estos archivos es moralmente correcto. Las bibliotecas de todo el mundo están sufriendo recortes de financiación, y tampoco podemos confiar el patrimonio de la humanidad a las corporaciones."

msgid "blog.ai-copyright.text4"
msgstr "Luego llegó la IA. Prácticamente todas las grandes empresas que desarrollan LLMs nos contactaron para entrenar con nuestros datos. La mayoría (¡pero no todas!) de las empresas con sede en EE. UU. reconsideraron una vez que se dieron cuenta de la naturaleza ilegal de nuestro trabajo. En cambio, las empresas chinas han acogido con entusiasmo nuestra colección, aparentemente sin preocuparse por su legalidad. Esto es notable dado el papel de China como signatario de casi todos los principales tratados internacionales de derechos de autor."

msgid "blog.ai-copyright.text5"
msgstr "Hemos dado acceso de alta velocidad a unas 30 empresas. La mayoría de ellas son empresas de LLM, y algunas son corredores de datos, quienes revenderán nuestra colección. La mayoría son chinas, aunque también hemos trabajado con empresas de EE. UU., Europa, Rusia, Corea del Sur y Japón. DeepSeek <a %(arxiv)s>admitió</a> que una versión anterior fue entrenada con parte de nuestra colección, aunque son muy reservados sobre su último modelo (probablemente también entrenado con nuestros datos)."

msgid "blog.ai-copyright.text6"
msgstr "Si Occidente quiere mantenerse a la vanguardia en la carrera de los LLMs y, en última instancia, de la AGI, necesita reconsiderar su posición sobre los derechos de autor, y pronto. Ya sea que esté de acuerdo con nosotros o no en nuestro argumento moral, esto se está convirtiendo ahora en un caso de economía, e incluso de seguridad nacional. Todos los bloques de poder están construyendo supercientíficos artificiales, superhackers y supermilitares. La libertad de información se está convirtiendo en una cuestión de supervivencia para estos países, incluso una cuestión de seguridad nacional."

msgid "blog.ai-copyright.text7"
msgstr "Nuestro equipo proviene de todo el mundo, y no tenemos una alineación particular. Pero animaríamos a los países con leyes de derechos de autor estrictas a usar esta amenaza existencial para reformarlas. Entonces, ¿qué hacer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Nuestra primera recomendación es sencilla: acortar el plazo del copyright. En EE. UU., el copyright se otorga por 70 años después de la muerte del autor. Esto es absurdo. Podemos alinearlo con las patentes, que se otorgan por 20 años después de la presentación. Esto debería ser más que suficiente tiempo para que los autores de libros, artículos, música, arte y otras obras creativas sean completamente compensados por sus esfuerzos (incluidos proyectos a largo plazo como adaptaciones cinematográficas)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Luego, como mínimo, los legisladores deberían incluir excepciones para la preservación masiva y la difusión de textos. Si la pérdida de ingresos de clientes individuales es la principal preocupación, la distribución a nivel personal podría seguir prohibida. A su vez, aquellos capaces de gestionar vastos repositorios —empresas que entrenan LLM, junto con bibliotecas y otros archivos— estarían cubiertos por estas excepciones."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Algunos países ya están haciendo una versión de esto. TorrentFreak <a %(torrentfreak)s>informó</a> que China y Japón han introducido excepciones de IA en sus leyes de copyright. No está claro para nosotros cómo esto interactúa con los tratados internacionales, pero ciertamente da cobertura a sus empresas nacionales, lo que explica lo que hemos estado viendo."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "En cuanto al Archivo de Anna, continuaremos nuestro trabajo subterráneo arraigado en la convicción moral. Sin embargo, nuestro mayor deseo es salir a la luz y amplificar nuestro impacto legalmente. Por favor, reformen el copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lea los artículos complementarios de TorrentFreak: <a %(torrentfreak)s>primero</a>, <a %(torrentfreak_2)s>segundo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Ganadores de la recompensa de visualización de ISBN de $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Resumen: Recibimos algunas presentaciones increíbles para la recompensa de visualización de ISBN de $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Hace unos meses anunciamos una <a %(all_isbns)s>recompensa de $10,000</a> para hacer la mejor visualización posible de nuestros datos mostrando el espacio ISBN. Enfatizamos mostrar qué archivos ya hemos archivado y cuáles no, y más tarde un conjunto de datos que describe cuántas bibliotecas tienen ISBN (una medida de rareza)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Nos hemos sentido abrumados por la respuesta. Ha habido tanta creatividad. ¡Un gran agradecimiento a todos los que han participado: su energía y entusiasmo son contagiosos!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "En última instancia, queríamos responder a las siguientes preguntas: <strong>¿qué libros existen en el mundo, cuántos hemos archivado ya y en cuáles deberíamos centrarnos a continuación?</strong> Es genial ver que tantas personas se preocupan por estas preguntas."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Comenzamos con una visualización básica nosotros mismos. En menos de 300kb, esta imagen representa sucintamente la mayor “lista de libros” completamente abierta jamás ensamblada en la historia de la humanidad:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Todos los ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Archivos en el Archivo de Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNOs de CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Filtración de datos de CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSIDs de DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registro Global de Editores ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Estatal Rusa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Atrás"

msgid "common.forward"
msgstr "Adelante"

msgid "common.last"
msgstr "Último"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Por favor, consulte la <a %(all_isbns)s>entrada original del blog</a> para más información."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Lanzamos un desafío para mejorar esto. Otorgaríamos un premio de $6,000 para el primer lugar, $3,000 para el segundo lugar y $1,000 para el tercer lugar. Debido a la abrumadora respuesta y las increíbles presentaciones, hemos decidido aumentar ligeramente el fondo de premios y otorgar un tercer lugar compartido de $500 cada uno. Los ganadores están a continuación, pero asegúrese de ver todas las presentaciones <a %(annas_archive)s>aquí</a>, o descargar nuestro <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primer lugar $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Esta <a %(phiresky_github)s>presentación</a> (<a %(annas_archive_note_2951)s>comentario en Gitlab</a>) es simplemente todo lo que queríamos, ¡y más! Nos gustaron especialmente las opciones de visualización increíblemente flexibles (incluso admitiendo shaders personalizados), pero con una lista completa de preajustes. También nos gustó lo rápido y fluido que es todo, la implementación simple (que ni siquiera tiene un backend), el ingenioso minimapa y la extensa explicación en su <a %(phiresky_github)s>entrada de blog</a>. ¡Increíble trabajo y un ganador bien merecido!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Segundo lugar $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Otra increíble <a %(annas_archive_note_2913)s>presentación</a>. No tan flexible como el primer lugar, pero en realidad preferimos su visualización a nivel macro sobre el primer lugar (curva de llenado de espacio, bordes, etiquetado, resaltado, desplazamiento y zoom). Un <a %(annas_archive_note_2971)s>comentario</a> de Joe Davis resonó con nosotros:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Si bien los cuadrados y rectángulos perfectos son matemáticamente agradables, no proporcionan una superior localidad en un contexto de mapeo. Creo que la asimetría inherente en estos Hilbert o Morton clásicos no es un defecto sino una característica. Al igual que el famoso contorno en forma de bota de Italia lo hace instantáneamente reconocible en un mapa, las \"peculiaridades\" únicas de estas curvas pueden servir como puntos de referencia cognitivos. Esta distintividad puede mejorar la memoria espacial y ayudar a los usuarios a orientarse, potencialmente haciendo más fácil localizar regiones específicas o notar patrones.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Y aún muchas opciones para visualizar y renderizar, así como una interfaz de usuario increíblemente fluida e intuitiva. ¡Un sólido segundo lugar!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tercer lugar $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "En esta <a %(annas_archive_note_2940)s>presentación</a> realmente nos gustaron los diferentes tipos de vistas, en particular las vistas de comparación y de editor."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tercer lugar $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Aunque no es la interfaz de usuario más pulida, esta <a %(annas_archive_note_2917)s>presentación</a> cumple con muchos de los requisitos. Nos gustó particularmente su función de comparación."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tercer lugar $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Al igual que el primer lugar, esta <a %(annas_archive_note_2975)s>presentación</a> nos impresionó con su flexibilidad. En última instancia, esto es lo que hace que una herramienta de visualización sea excelente: máxima flexibilidad para usuarios avanzados, manteniendo las cosas simples para los usuarios promedio."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tercer lugar $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "La última <a %(annas_archive_note_2947)s>presentación</a> en recibir un premio es bastante básica, pero tiene algunas características únicas que realmente nos gustaron. Nos gustó cómo muestran cuántos datasets cubren un ISBN particular como medida de popularidad/confiabilidad. También nos gustó mucho la simplicidad pero efectividad de usar un deslizador de opacidad para comparaciones."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ideas notables"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Algunas más ideas e implementaciones que nos gustaron particularmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Rascacielos para la rareza"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Estadísticas en vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotaciones, y también estadísticas en vivo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista de mapa única y filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Esquema de color predeterminado genial y mapa de calor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Cambio fácil de datasets para comparaciones rápidas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etiquetas bonitas."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de escala con número de libros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Muchos deslizadores para comparar datasets, como si fueras un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Podríamos seguir por un tiempo, pero detengámonos aquí. Asegúrate de ver todas las presentaciones <a %(annas_archive)s>aquí</a>, o descarga nuestro <a %(a_2025_01_isbn_visualization_files)s>torrent combinado</a>. Tantas presentaciones, y cada una aporta una perspectiva única, ya sea en la interfaz de usuario o en la implementación."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Al menos incorporaremos la presentación del primer lugar en nuestro sitio web principal, y quizás algunas otras. También hemos comenzado a pensar en cómo organizar el proceso de identificar, confirmar y luego archivar los libros más raros. Más por venir en este frente."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Gracias a todos los que participaron. Es increíble que a tanta gente le importe."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nuestros corazones están llenos de gratitud."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizando Todos los ISBN — Recompensa de $10,000 para el 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Esta imagen representa la “lista de libros” completamente abierta más grande jamás ensamblada en la historia de la humanidad."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Esta imagen tiene 1000×800 píxeles. Cada píxel representa 2,500 ISBN. Si tenemos un archivo para un ISBN, hacemos que ese píxel sea más verde. Si sabemos que se ha emitido un ISBN, pero no tenemos un archivo correspondiente, lo hacemos más rojo."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "En menos de 300kb, esta imagen representa sucintamente la “lista de libros” completamente abierta más grande jamás ensamblada en la historia de la humanidad (unos pocos cientos de GB comprimidos en su totalidad)."

msgid "blog.all-isbns.text3"
msgstr "También muestra: queda mucho trabajo por hacer en la copia de seguridad de libros (solo tenemos 16%%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Antecedentes"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "¿Cómo puede el Archivo de Anna lograr su misión de respaldar todo el conocimiento de la humanidad, sin saber qué libros aún están por ahí? Necesitamos una lista de tareas. Una forma de mapear esto es a través de los números ISBN, que desde la década de 1970 se han asignado a cada libro publicado (en la mayoría de los países)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "No hay una autoridad central que conozca todas las asignaciones de ISBN. En su lugar, es un sistema distribuido, donde los países obtienen rangos de números, que luego asignan rangos más pequeños a los principales editores, quienes podrían subdividir aún más los rangos a editores menores. Finalmente, se asignan números individuales a los libros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Comenzamos a mapear los ISBNs <a %(blog)s>hace dos años</a> con nuestro raspado de ISBNdb. Desde entonces, hemos raspado muchas más fuentes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, y más. Se puede encontrar una lista completa en las páginas de “Datasets” y “Torrents” en el Archivo de Anna. Ahora tenemos, con mucho, la colección de metadata de libros (y por lo tanto ISBNs) completamente abierta y fácilmente descargable más grande del mundo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Hemos <a %(blog)s>escrito extensamente</a> sobre por qué nos importa la preservación, y por qué actualmente estamos en una ventana crítica. Ahora debemos identificar libros raros, poco enfocados y singularmente en riesgo y preservarlos. Tener buena metadata de todos los libros del mundo ayuda con eso."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualización"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Además de la imagen general, también podemos ver los datasets individuales que hemos adquirido. Use el menú desplegable y los botones para cambiar entre ellos."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Hay muchos patrones interesantes para ver en estas imágenes. ¿Por qué hay cierta regularidad de líneas y bloques, que parece ocurrir a diferentes escalas? ¿Cuáles son las áreas vacías? ¿Por qué ciertos datasets están tan agrupados? Dejaremos estas preguntas como un ejercicio para el lector."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Recompensa de $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Hay mucho por explorar aquí, por lo que estamos anunciando una recompensa por mejorar la visualización anterior. A diferencia de la mayoría de nuestras recompensas, esta tiene un límite de tiempo. Debes <a %(annas_archive)s>enviar</a> tu código de fuente abierta antes del 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La mejor presentación recibirá $6,000, el segundo lugar $3,000, y el tercer lugar $1,000. Todas las recompensas se otorgarán usando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "A continuación se presentan los criterios mínimos. Si ninguna presentación cumple con los criterios, aún podríamos otorgar algunas recompensas, pero eso será a nuestra discreción."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Haz un fork de este repositorio y edita este HTML de la publicación del blog (no se permiten otros backends además de nuestro backend Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Haz que la imagen de arriba sea suavemente ampliable, para que puedas hacer zoom hasta los ISBN individuales. Al hacer clic en los ISBNs, debería llevarte a una página de metadata o búsqueda en el Archivo de Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Debes poder seguir cambiando entre todos los diferentes datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Los rangos de países y editores deben destacarse al pasar el cursor. Puedes usar, por ejemplo, <a %(github_xlcnd_isbnlib)s>data4info.py en isbnlib</a> para información de países, y nuestro raspado “isbngrp” para editores (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Debe funcionar bien en escritorio y móvil."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Para puntos extra (estas son solo ideas — deja que tu creatividad vuele):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Se dará una fuerte consideración a la usabilidad y a lo bien que se vea."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Muestra metadata real para ISBNs individuales al hacer zoom, como título y autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Mejor curva de llenado de espacio. Por ejemplo, un zig-zag, yendo de 0 a 4 en la primera fila y luego de regreso (en reversa) de 5 a 9 en la segunda fila — aplicado recursivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Esquemas de color diferentes o personalizables."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Vistas especiales para comparar datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Formas de depurar problemas, como otros metadata que no concuerdan bien (por ejemplo, títulos muy diferentes)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Anotar imágenes con comentarios sobre ISBNs o rangos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Cualquier heurística para identificar libros raros o en riesgo."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "¡Cualquier idea creativa que se te ocurra!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Puedes desviarte completamente de los criterios mínimos y hacer una visualización completamente diferente. Si es realmente espectacular, entonces califica para la recompensa, pero a nuestra discreción."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Haz tus envíos publicando un comentario en <a %(annas_archive)s>este problema</a> con un enlace a tu repositorio bifurcado, solicitud de fusión o diferencia."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Código"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "El código para generar estas imágenes, así como otros ejemplos, se puede encontrar en <a %(annas_archive)s>este directorio</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Hemos ideado un formato de datos compacto, con el cual toda la información requerida de ISBN ocupa alrededor de 75MB (comprimido). La descripción del formato de datos y el código para generarlo se pueden encontrar <a %(annas_archive_l1244_1319)s>aquí</a>. Para la recompensa no estás obligado a usar esto, pero probablemente sea el formato más conveniente para comenzar. Puedes transformar nuestro metadata como desees (aunque todo tu código debe ser de código abierto)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Estamos ansiosos por ver lo que se te ocurre. ¡Buena suerte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contenedores del Archivo de Anna (AAC): estandarizando lanzamientos de la biblioteca fantasma más grande del mundo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "El Archivo de Anna se ha convertido en la biblioteca fantasma más grande del mundo, lo que nos obliga a estandarizar nuestros lanzamientos."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>El Archivo de Anna</a> se ha convertido, con diferencia, en la biblioteca fantasma más grande del mundo, y la única biblioteca fantasma de su escala que es completamente de código abierto y datos abiertos. A continuación se muestra una tabla de nuestra página de Datasets (ligeramente modificada):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Logramos esto de tres maneras:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Reflejando bibliotecas en la sombra de datos abiertos existentes (como Sci-Hub y Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Ayudando a bibliotecas en la sombra que quieren ser más abiertas, pero no tenían el tiempo o los recursos para hacerlo (como la colección de cómics de Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspando bibliotecas que no desean compartir en masa (como Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Para (2) y (3) ahora gestionamos una considerable colección de torrents nosotros mismos (cientos de TBs). Hasta ahora hemos abordado estas colecciones como casos únicos, lo que significa infraestructura y organización de datos a medida para cada colección. Esto añade una carga significativa a cada lanzamiento y hace que sea particularmente difícil realizar lanzamientos más incrementales."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Por eso decidimos estandarizar nuestros lanzamientos. Este es un post técnico en el que presentamos nuestro estándar: <strong>Contenedores de Anna’s Archive</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Objetivos de diseño"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Nuestro caso de uso principal es la distribución de archivos y metadata asociada de diferentes colecciones existentes. Nuestras consideraciones más importantes son:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Archivos y metadata heterogéneos, lo más cercano posible al formato original."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificadores heterogéneos en las bibliotecas de origen, o incluso la falta de identificadores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Lanzamientos separados de metadata frente a datos de archivos, o lanzamientos solo de metadata (por ejemplo, nuestro lanzamiento de ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribución a través de torrents, aunque con la posibilidad de otros métodos de distribución (por ejemplo, IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registros inmutables, ya que debemos asumir que nuestros torrents vivirán para siempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Lanzamientos incrementales / lanzamientos ampliables."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Legible y escribible por máquinas, de manera conveniente y rápida, especialmente para nuestra pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspección humana algo fácil, aunque esto es secundario a la legibilidad por máquinas."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Fácil de sembrar nuestras colecciones con un seedbox estándar alquilado."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Los datos binarios pueden ser servidos directamente por servidores web como Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Algunos objetivos no prioritarios:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "No nos importa que los archivos sean fáciles de navegar manualmente en disco, o que sean buscables sin preprocesamiento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "No nos importa ser directamente compatibles con el software de biblioteca existente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Aunque debería ser fácil para cualquiera sembrar nuestra colección usando torrents, no esperamos que los archivos sean utilizables sin un conocimiento técnico significativo y compromiso."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Dado que Anna’s Archive es de código abierto, queremos probar nuestro formato directamente. Cuando actualizamos nuestro índice de búsqueda, solo accedemos a rutas disponibles públicamente, para que cualquiera que bifurque nuestra biblioteca pueda comenzar rápidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "El estándar"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Finalmente, nos decidimos por un estándar relativamente simple. Es bastante flexible, no normativo y un trabajo en progreso."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contenedor del Archivo de Anna) es un único elemento que consiste en <strong>metadata</strong>, y opcionalmente <strong>datos binarios</strong>, ambos inmutables. Tiene un identificador único global, llamado <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Colección.</strong> Cada AAC pertenece a una colección, que por definición es una lista de AACs que son semánticamente consistentes. Esto significa que si realizas un cambio significativo en el formato de los metadata, entonces debes crear una nueva colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Colecciones de “registros” y “archivos”.</strong> Por convención, a menudo es conveniente lanzar “registros” y “archivos” como colecciones diferentes, para que puedan ser lanzadas en diferentes horarios, por ejemplo, basados en las tasas de scraping. Un “registro” es una colección solo de metadata, que contiene información como títulos de libros, autores, ISBNs, etc., mientras que “archivos” son las colecciones que contienen los archivos reales (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> El formato de AACID es el siguiente: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por ejemplo, un AACID real que hemos lanzado es <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: el nombre de la colección, que puede contener letras ASCII, números y guiones bajos (pero no dobles guiones bajos)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: una versión corta del ISO 8601, siempre en UTC, por ejemplo, <code>20220723T194746Z</code>. Este número debe aumentar de manera monótona para cada lanzamiento, aunque su semántica exacta puede diferir por colección. Sugerimos usar el tiempo de scraping o de generación del ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificador específico de la colección, si es aplicable, por ejemplo, el ID de Z-Library. Puede ser omitido o truncado. Debe ser omitido o truncado si el AACID excedería de otro modo los 150 caracteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por ejemplo, usando base57. Actualmente usamos la biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Rango de AACID.</strong> Dado que los AACIDs contienen marcas de tiempo que aumentan de manera monótona, podemos usar eso para denotar rangos dentro de una colección particular. Usamos este formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, donde las marcas de tiempo son inclusivas. Esto es consistente con la notación ISO 8601. Los rangos son continuos y pueden superponerse, pero en caso de superposición deben contener registros idénticos a los lanzados previamente en esa colección (ya que los AACs son inmutables). No se permiten registros faltantes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Archivo de metadata.</strong> Un archivo de metadata contiene los metadata de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "El nombre del archivo debe ser un rango de AACID, precedido por <code style=\"color: red\">annas_archive_meta__</code> y seguido por <code>.jsonl.zstd</code>. Por ejemplo, uno de nuestros lanzamientos se llama<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Como indica la extensión del archivo, el tipo de archivo es <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada objeto JSON debe contener los siguientes campos en el nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). No se permiten otros campos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> son metadata arbitrarios, según la semántica de la colección. Deben ser semánticamente consistentes dentro de la colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> es opcional, y es el nombre de la carpeta de datos binarios que contiene los datos binarios correspondientes. El nombre del archivo de los datos binarios correspondientes dentro de esa carpeta es el AACID del registro."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "El prefijo <code style=\"color: red\">annas_archive_meta__</code> puede adaptarse al nombre de tu institución, por ejemplo, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Carpeta de datos binarios.</strong> Una carpeta con los datos binarios de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "El nombre del directorio debe ser un rango de AACID, precedido por <code style=\"color: green\">annas_archive_data__</code>, y sin sufijo. Por ejemplo, uno de nuestros lanzamientos reales tiene un directorio llamado<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "El directorio debe contener archivos de datos para todos los AACs dentro del rango especificado. Cada archivo de datos debe tener su AACID como nombre de archivo (sin extensiones)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Se recomienda que estas carpetas sean de un tamaño manejable, por ejemplo, que no superen los 100GB-1TB cada una, aunque esta recomendación puede cambiar con el tiempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Los archivos de metadata y las carpetas de datos binarios pueden agruparse en torrents, con un torrent por archivo de metadata o un torrent por carpeta de datos binarios. Los torrents deben tener el nombre original del archivo/directorio más un sufijo <code>.torrent</code> como su nombre de archivo."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Ejemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Veamos nuestro reciente lanzamiento de Z-Library como ejemplo. Consiste en dos colecciones: “<span style=\"background: #fffaa3\">zlib3_records</span>” y “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Esto nos permite extraer y liberar por separado los registros de metadata de los archivos de libros reales. Por lo tanto, lanzamos dos torrents con archivos de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "También lanzamos un montón de torrents con carpetas de datos binarios, pero solo para la colección “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 en total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Al ejecutar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> podemos ver lo que hay dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "En este caso, es la metadata de un libro según lo reportado por Z-Library. A nivel superior solo tenemos “aacid” y “metadata”, pero no “data_folder”, ya que no hay datos binarios correspondientes. El AACID contiene “22430000” como el ID principal, que podemos ver que se toma de “zlibrary_id”. Podemos esperar que otros AAC en esta colección tengan la misma estructura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Ahora ejecutemos <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Esta es una metadata de AAC mucho más pequeña, aunque la mayor parte de este AAC se encuentra en otro lugar en un archivo binario. Después de todo, esta vez tenemos un “data_folder”, por lo que podemos esperar que los datos binarios correspondientes se encuentren en <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. La “metadata” contiene el “zlibrary_id”, por lo que podemos asociarlo fácilmente con el AAC correspondiente en la colección “zlib_records”. Podríamos haberlo asociado de varias maneras diferentes, por ejemplo, a través de AACID — el estándar no lo prescribe."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Tenga en cuenta que tampoco es necesario que el campo “metadata” sea en sí mismo JSON. Podría ser una cadena que contenga XML o cualquier otro formato de datos. Incluso podría almacenar información de metadata en el blob binario asociado, por ejemplo, si es una gran cantidad de datos."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusión"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con este estándar, podemos hacer lanzamientos de manera más incremental y agregar nuevas fuentes de datos más fácilmente. ¡Ya tenemos algunos lanzamientos emocionantes en proceso!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "También esperamos que sea más fácil para otras bibliotecas en la sombra espejar nuestras colecciones. Después de todo, nuestro objetivo es preservar el conocimiento y la cultura humana para siempre, así que cuanta más redundancia, mejor."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Actualización de Anna: archivo completamente de código abierto, ElasticSearch, más de 300GB de portadas de libros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Hemos estado trabajando día y noche para ofrecer una buena alternativa con el Archivo de Anna. Aquí están algunas de las cosas que hemos logrado recientemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Con Z-Library cayendo y sus (presuntos) fundadores siendo arrestados, hemos estado trabajando día y noche para ofrecer una buena alternativa con el Archivo de Anna (no lo enlazaremos aquí, pero puedes buscarlo en Google). Aquí están algunas de las cosas que hemos logrado recientemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "El Archivo de Anna es completamente de código abierto"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Creemos que la información debe ser libre, y nuestro propio código no es una excepción. Hemos liberado todo nuestro código en nuestra instancia de Gitlab alojada de forma privada: <a %(annas_archive)s>Software de Anna</a>. También usamos el rastreador de problemas para organizar nuestro trabajo. Si deseas participar en nuestro desarrollo, este es un gran lugar para comenzar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Para darte una idea de las cosas en las que estamos trabajando, toma nuestro trabajo reciente en mejoras de rendimiento del lado del cliente. Como aún no hemos implementado la paginación, a menudo devolvíamos páginas de búsqueda muy largas, con 100-200 resultados. No queríamos cortar los resultados de búsqueda demasiado pronto, pero esto significaba que ralentizaría algunos dispositivos. Para esto, implementamos un pequeño truco: envolvimos la mayoría de los resultados de búsqueda en comentarios HTML (<code><!-- --></code>), y luego escribimos un pequeño Javascript que detectaría cuándo un resultado debería hacerse visible, momento en el cual desenvolveríamos el comentario:"

msgid "blog.annas-update-2022.open-source.text3"
msgstr "¡La \"virtualización\" del DOM implementada en 23 líneas, sin necesidad de bibliotecas sofisticadas! Este es el tipo de código pragmático y rápido que se obtiene cuando se tiene tiempo limitado y problemas reales que necesitan ser resueltos. ¡Se ha informado que nuestra búsqueda ahora funciona bien en dispositivos lentos!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Otro gran esfuerzo fue automatizar la construcción de la base de datos. Cuando lanzamos, simplemente juntamos diferentes fuentes de manera desordenada. Ahora queremos mantenerlas actualizadas, así que escribimos un montón de scripts para descargar nuevos metadata de los dos forks de Library Genesis e integrarlos. El objetivo no es solo hacer esto útil para nuestro archivo, sino facilitar las cosas a cualquiera que quiera experimentar con metadata de bibliotecas fantasma. El objetivo sería un cuaderno de Jupyter que tenga todo tipo de metadata interesante disponible, para que podamos hacer más investigaciones como averiguar qué <a %(blog)s>porcentaje de ISBNs se preservan para siempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, renovamos nuestro sistema de donaciones. Ahora puede usar una tarjeta de crédito para depositar dinero directamente en nuestras billeteras de criptomonedas, sin realmente necesitar saber nada sobre criptomonedas. Seguiremos monitoreando qué tan bien funciona esto en la práctica, pero es un gran avance."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Cambiar a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Uno de nuestros <a %(annas_archive)s>tickets</a> era un conjunto de problemas con nuestro sistema de búsqueda. Usamos la búsqueda de texto completo de MySQL, ya que teníamos todos nuestros datos en MySQL de todos modos. Pero tenía sus límites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Algunas consultas tomaban mucho tiempo, hasta el punto de acaparar todas las conexiones abiertas."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Por defecto, MySQL tiene una longitud mínima de palabra, o su índice puede volverse realmente grande. La gente reportó no poder buscar \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La búsqueda solo era algo rápida cuando estaba completamente cargada en memoria, lo que requería que obtuviéramos una máquina más cara para ejecutarla, además de algunos comandos para precargar el índice al inicio."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "No habríamos podido extenderlo fácilmente para construir nuevas funciones, como mejor <a %(wikipedia_cjk_characters)s>tokenización para idiomas sin espacios</a>, filtrado/facetas, ordenación, sugerencias de \"¿quiso decir?\", autocompletar, y así sucesivamente."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Después de hablar con un montón de expertos, nos decidimos por ElasticSearch. No ha sido perfecto (sus sugerencias de \"¿quiso decir?\" y funciones de autocompletar por defecto son malas), pero en general ha sido mucho mejor que MySQL para la búsqueda. Todavía no estamos <a %(youtube)s>muy entusiasmados</a> con usarlo para cualquier dato crítico (aunque han hecho mucho <a %(elastic_co)s>progreso</a>), pero en general estamos bastante contentos con el cambio."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Por ahora, hemos implementado una búsqueda mucho más rápida, mejor soporte de idiomas, mejor ordenación por relevancia, diferentes opciones de ordenación y filtrado por idioma/tipo de libro/tipo de archivo. Si tienes curiosidad sobre cómo funciona, <a %(annas_archive_l140)s>échale</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>vistazo</a>. Es bastante accesible, aunque podría usar algunos comentarios más…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Más de 300GB de portadas de libros liberadas"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, nos complace anunciar un pequeño lanzamiento. En colaboración con las personas que operan el fork Libgen.rs, estamos compartiendo todas sus portadas de libros a través de torrents e IPFS. Esto distribuirá la carga de ver las portadas entre más máquinas y las preservará mejor. En muchos (pero no todos) casos, las portadas de los libros están incluidas en los archivos mismos, por lo que esto es una especie de \"datos derivados\". Pero tenerlo en IPFS sigue siendo muy útil para la operación diaria tanto de Archivo de Anna como de los diversos forks de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costumbre, puedes encontrar este lanzamiento en el Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). No lo enlazaremos aquí, pero puedes encontrarlo fácilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Esperamos poder relajar nuestro ritmo un poco, ahora que tenemos una alternativa decente a Z-Library. Esta carga de trabajo no es particularmente sostenible. Si estás interesado en ayudar con la programación, operaciones de servidor o trabajo de preservación, definitivamente contáctanos. Todavía hay mucho <a %(annas_archive)s>trabajo por hacer</a>. Gracias por tu interés y apoyo."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "El Archivo de Anna ha respaldado la biblioteca fantasma de cómics más grande del mundo (95TB) — puedes ayudar a sembrarla"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La biblioteca fantasma de cómics más grande del mundo tenía un único punto de falla... hasta hoy."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discutir en Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La biblioteca fantasma más grande de cómics es probablemente la de un fork particular de Library Genesis: Libgen.li. El único administrador que dirige ese sitio logró recopilar una colección de cómics increíble de más de 2 millones de archivos, totalizando más de 95TB. Sin embargo, a diferencia de otras colecciones de Library Genesis, esta no estaba disponible en masa a través de torrents. Solo podías acceder a estos cómics individualmente a través de su lento servidor personal — un único punto de falla. ¡Hasta hoy!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "En esta publicación te contaremos más sobre esta colección y sobre nuestra recaudación de fondos para apoyar más de este trabajo."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>La Dra. Barbara Gordon intenta perderse en el mundo mundano de la biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Bifurcaciones de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Primero, un poco de contexto. Puede que conozcas Library Genesis por su épica colección de libros. Menos personas saben que los voluntarios de Library Genesis han creado otros proyectos, como una considerable colección de revistas y documentos estándar, una copia de seguridad completa de Sci-Hub (en colaboración con la fundadora de Sci-Hub, Alexandra Elbakyan) y, de hecho, una enorme colección de cómics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "En algún momento, diferentes operadores de espejos de Library Genesis tomaron caminos separados, lo que dio lugar a la situación actual de tener varias \"bifurcaciones\" diferentes, todas aún llevando el nombre de Library Genesis. La bifurcación Libgen.li tiene de manera única esta colección de cómics, así como una considerable colección de revistas (en la que también estamos trabajando)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Colaboración"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Dada su magnitud, esta colección ha estado en nuestra lista de deseos durante mucho tiempo, así que después de nuestro éxito con la copia de seguridad de Z-Library, pusimos nuestra mira en esta colección. Al principio la extraíamos directamente, lo cual fue todo un desafío, ya que su servidor no estaba en las mejores condiciones. De esta manera obtuvimos alrededor de 15TB, pero fue un proceso lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Afortunadamente, logramos ponernos en contacto con el operador de la biblioteca, quien accedió a enviarnos todos los datos directamente, lo cual fue mucho más rápido. Aún así, tomó más de medio año transferir y procesar todos los datos, y casi los perdimos todos debido a la corrupción del disco, lo que habría significado empezar de nuevo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Esta experiencia nos ha hecho creer que es importante difundir estos datos lo más rápido posible, para que puedan ser replicados ampliamente. ¡Estamos a solo uno o dos incidentes desafortunados de perder esta colección para siempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La colección"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Moverse rápido significa que la colección está un poco desorganizada… Echemos un vistazo. Imagina que tenemos un sistema de archivos (que en realidad estamos dividiendo en torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "El primer directorio, <code>/repository</code>, es la parte más estructurada de esto. Este directorio contiene los llamados “mil dirs”: directorios cada uno con mil archivos, que están numerados incrementalmente en la base de datos. El directorio <code>0</code> contiene archivos con comic_id 0–999, y así sucesivamente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Este es el mismo esquema que Library Genesis ha estado utilizando para sus colecciones de ficción y no ficción. La idea es que cada “mil dir” se convierta automáticamente en un torrent tan pronto como se llene."

msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Sin embargo, el operador de Libgen.li nunca creó torrents para esta colección, por lo que los mil directorios probablemente se volvieron inconvenientes y dieron paso a “directorios desordenados”. Estos son <code>/comics0</code> hasta <code>/comics4</code>. Todos contienen estructuras de directorios únicas, que probablemente tenían sentido para recopilar los archivos, pero ahora no tienen mucho sentido para nosotros. Afortunadamente, los metadatos todavía se refieren directamente a todos estos archivos, ¡así que la organización de su almacenamiento en disco realmente no importa!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "El metadata está disponible en forma de una base de datos MySQL. Se puede descargar directamente desde el sitio web de Libgen.li, pero también lo pondremos a disposición en un torrent, junto con nuestra propia tabla con todos los hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Análisis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Cuando recibes 95TB volcados en tu clúster de almacenamiento, intentas entender qué hay allí… Hicimos un análisis para ver si podíamos reducir un poco el tamaño, como eliminando duplicados. Aquí están algunos de nuestros hallazgos:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Los duplicados semánticos (diferentes escaneos del mismo libro) teóricamente pueden ser filtrados, pero es complicado. Al revisar manualmente los cómics encontramos demasiados falsos positivos."

msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Hay algunos duplicados puramente por MD5, lo cual es relativamente desperdiciado, pero eliminarlos solo nos daría alrededor de un 1%% in de ahorro. A esta escala eso sigue siendo alrededor de 1TB, pero también, a esta escala 1TB realmente no importa. Preferimos no arriesgarnos a destruir datos accidentalmente en este proceso."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Encontramos un montón de datos que no son libros, como películas basadas en cómics. Eso también parece un desperdicio, ya que estos ya están ampliamente disponibles por otros medios. Sin embargo, nos dimos cuenta de que no podíamos simplemente filtrar los archivos de películas, ya que también hay <em>libros de cómics interactivos</em> que se lanzaron en la computadora, que alguien grabó y guardó como películas."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "En última instancia, cualquier cosa que pudiéramos eliminar de la colección solo ahorraría unos pocos porcentajes. Entonces recordamos que somos acumuladores de datos, y las personas que estarán espejando esto también son acumuladores de datos, así que, \"¿QUÉ QUIERES DECIR CON ELIMINAR?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Por lo tanto, les presentamos la colección completa y sin modificar. Es una gran cantidad de datos, pero esperamos que suficientes personas se interesen en sembrarla de todos modos."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Recaudación de fondos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Estamos lanzando estos datos en algunos grandes fragmentos. El primer torrent es de <code>/comics0</code>, que pusimos en un enorme archivo .tar de 12TB. Eso es mejor para tu disco duro y software de torrents que un millón de archivos más pequeños."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte de este lanzamiento, estamos realizando una recaudación de fondos. Buscamos recaudar $20,000 para cubrir los costos operativos y de contratación para esta colección, así como para habilitar proyectos continuos y futuros. Tenemos algunos <em>enormes</em> en proceso."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>¿A quién estoy apoyando con mi donación?</em> En resumen: estamos respaldando todo el conocimiento y la cultura de la humanidad, y haciéndolo fácilmente accesible. Todo nuestro código y datos son de código abierto, somos un proyecto completamente dirigido por voluntarios, y hemos guardado 125TB de libros hasta ahora (además de los torrents existentes de Libgen y Scihub). En última instancia, estamos construyendo un volante que permite e incentiva a las personas a encontrar, escanear y respaldar todos los libros del mundo. Escribiremos sobre nuestro plan maestro en una publicación futura. :)"

msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si donas una membresía de 12 meses de “Amazing Archivist” ($780), puedes <strong>“adoptar un torrent”</strong>, ¡Lo que significa que pondremos tu nombre de usuario o mensaje en el nombre de archivo de uno de los torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Puedes donar yendo a <a %(wikipedia_annas_archive)s>Archivo de Anna</a> y haciendo clic en el botón “Donar”. También estamos buscando más voluntarios: ingenieros de software, investigadores de seguridad, expertos en comercio anónimo y traductores. También puedes apoyarnos proporcionando servicios de alojamiento. Y, por supuesto, ¡por favor siembra nuestros torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "¡Gracias a todos los que ya nos han apoyado tan generosamente! Realmente están marcando la diferencia."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Aquí están los torrents lanzados hasta ahora (todavía estamos procesando el resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Todos los torrents se pueden encontrar en <a %(wikipedia_annas_archive)s>Archivo de Anna</a> bajo “Datasets” (no enlazamos directamente allí, para que los enlaces a este blog no sean eliminados de Reddit, Twitter, etc.). Desde allí, sigue el enlace al sitio web de Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "¿Qué sigue?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un montón de torrents son geniales para la preservación a largo plazo, pero no tanto para el acceso diario. Estaremos trabajando con socios de alojamiento para subir todos estos datos a la web (ya que el Archivo de Anna no aloja nada directamente). Por supuesto, podrás encontrar estos enlaces de descarga en el Archivo de Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "¡También estamos invitando a todos a hacer cosas con estos datos! Ayúdanos a analizarlos mejor, deduplicarlos, ponerlos en IPFS, remixarlos, entrenar tus modelos de IA con ellos, y así sucesivamente. Son todos tuyos, y estamos ansiosos por ver qué haces con ellos."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como se dijo antes, todavía tenemos algunos lanzamientos enormes por venir (si <em>alguien</em> pudiera <em>accidentalmente</em> enviarnos un volcado de una <em>cierta</em> base de datos ACS4, sabes dónde encontrarnos...), así como construir el volante para respaldar todos los libros del mundo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Así que mantente atento, apenas estamos comenzando."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nuevos libros añadidos al Espejo de la Biblioteca Pirata (+24TB, 3.8 millones de libros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "En el lanzamiento original del Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>), hicimos un espejo de Z-Library, una gran colección de libros ilegales. Como recordatorio, esto es lo que escribimos en esa publicación original del blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library es una biblioteca popular (e ilegal). Han tomado la colección de Library Genesis y la han hecho fácilmente buscable. Además de eso, se han vuelto muy efectivos en solicitar nuevas contribuciones de libros, incentivando a los usuarios contribuyentes con varios beneficios. Actualmente no contribuyen con estos nuevos libros de vuelta a Library Genesis. Y a diferencia de Library Genesis, no hacen que su colección sea fácilmente espejable, lo que impide una amplia preservación. Esto es importante para su modelo de negocio, ya que cobran dinero por acceder a su colección en masa (más de 10 libros por día)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "No hacemos juicios morales sobre cobrar dinero por el acceso masivo a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Esa colección data de mediados de 2021. Mientras tanto, Z-Library ha estado creciendo a un ritmo asombroso: han añadido alrededor de 3.8 millones de nuevos libros. Hay algunos duplicados, claro, pero la mayoría parecen ser libros realmente nuevos o escaneos de mayor calidad de libros previamente enviados. Esto se debe en gran parte al aumento del número de moderadores voluntarios en Z-Library y a su sistema de carga masiva con deduplicación. Nos gustaría felicitarlos por estos logros."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Nos complace anunciar que hemos obtenido todos los libros que se añadieron a Z-Library entre nuestro último espejo y agosto de 2022. También hemos vuelto atrás y recopilado algunos libros que nos perdimos la primera vez. En total, esta nueva colección es de aproximadamente 24TB, que es mucho más grande que la anterior (7TB). Nuestro espejo ahora tiene un total de 31TB. Nuevamente, deduplicamos contra Library Genesis, ya que ya hay torrents disponibles para esa colección."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Por favor, visite el Espejo de la Biblioteca Pirata para ver la nueva colección (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Allí hay más información sobre cómo están estructurados los archivos y qué ha cambiado desde la última vez. No enlazaremos desde aquí, ya que este es solo un sitio web de blog que no aloja ningún material ilegal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Por supuesto, sembrar también es una excelente manera de ayudarnos. Gracias a todos los que están sembrando nuestro conjunto anterior de torrents. Estamos agradecidos por la respuesta positiva y felices de que haya tantas personas que se preocupan por la preservación del conocimiento y la cultura de esta manera inusual."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Cómo convertirse en un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "El primer desafío podría ser sorprendente. No es un problema técnico ni un problema legal. Es un problema psicológico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Antes de sumergirnos, dos actualizaciones sobre el Espejo de la Biblioteca Pirata (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Recibimos algunas donaciones extremadamente generosas. La primera fue de $10,000 de un individuo anónimo que también ha estado apoyando a \"bookwarrior\", el fundador original de Library Genesis. Un agradecimiento especial a bookwarrior por facilitar esta donación. La segunda fue otra de $10,000 de un donante anónimo, que se puso en contacto después de nuestro último lanzamiento y se inspiró para ayudar. También tuvimos una serie de donaciones más pequeñas. Muchas gracias por todo su generoso apoyo. Tenemos algunos proyectos nuevos emocionantes en proceso que esto apoyará, así que estén atentos."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Tuvimos algunas dificultades técnicas con el tamaño de nuestro segundo lanzamiento, pero nuestros torrents están activos y sembrando ahora. También recibimos una oferta generosa de un individuo anónimo para sembrar nuestra colección en sus servidores de muy alta velocidad, por lo que estamos haciendo una carga especial a sus máquinas, después de lo cual todos los demás que estén descargando la colección deberían ver una gran mejora en la velocidad."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Se pueden escribir libros enteros sobre el <em>por qué</em> de la preservación digital en general, y el archivismo pirata en particular, pero déjenos dar una breve introducción para aquellos que no están muy familiarizados. El mundo está produciendo más conocimiento y cultura que nunca antes, pero también se está perdiendo más que nunca. La humanidad confía en gran medida en corporaciones como editoriales académicas, servicios de streaming y empresas de redes sociales para este patrimonio, y a menudo no han demostrado ser grandes guardianes. Echa un vistazo al documental Digital Amnesia, o realmente a cualquier charla de Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Hay algunas instituciones que hacen un buen trabajo archivando tanto como pueden, pero están limitadas por la ley. Como piratas, estamos en una posición única para archivar colecciones que ellos no pueden tocar, debido a la aplicación de derechos de autor u otras restricciones. También podemos reflejar colecciones muchas veces, en todo el mundo, aumentando así las posibilidades de una preservación adecuada."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Por ahora, no entraremos en discusiones sobre los pros y los contras de la propiedad intelectual, la moralidad de romper la ley, reflexiones sobre la censura o el tema del acceso al conocimiento y la cultura. Con todo eso fuera del camino, vamos a sumergirnos en el <em>cómo</em>. Compartiremos cómo nuestro equipo se convirtió en archivistas piratas y las lecciones que aprendimos en el camino. Hay muchos desafíos cuando emprendes este viaje, y esperamos poder ayudarte con algunos de ellos."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunidad"

msgid "blog.how-to.community.text1"
msgstr "El primer desafío podría ser sorprendente. No es un problema técnico, ni un problema legal. Es un problema psicológico: hacer este trabajo en las sombras puede ser increíblemente solitario. Dependiendo de lo que planees hacer y de tu modelo de amenaza, podrías tener que ser muy cuidadoso. En un extremo del espectro tenemos personas como Alexandra Elbakyan*, la fundadora de Sci-Hub, quien es muy abierta sobre sus actividades. Pero corre un alto riesgo de ser arrestada si visitara un país occidental en este momento, y podría enfrentar décadas de prisión. ¿Es ese un riesgo que estarías dispuesto a asumir? Nosotros estamos en el otro extremo del espectro; siendo muy cuidadosos de no dejar rastro alguno y teniendo una fuerte seguridad operativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como mencionó en HN \"ynno\", Alexandra inicialmente no quería ser conocida: \"Sus servidores estaban configurados para emitir mensajes de error detallados de PHP, incluyendo la ruta completa del archivo fuente con fallos, que estaba bajo el directorio /home/<USER>" Así que, usa nombres de usuario aleatorios en las computadoras que usas para estas cosas, en caso de que configures algo incorrectamente."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Sin embargo, ese secreto tiene un costo psicológico. A la mayoría de las personas les encanta ser reconocidas por el trabajo que hacen, y sin embargo, no puedes recibir ningún crédito por esto en la vida real. Incluso las cosas simples pueden ser desafiantes, como cuando los amigos te preguntan en qué has estado trabajando (en algún momento \"jugando con mi NAS / homelab\" se vuelve repetitivo)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Por eso es tan importante encontrar una comunidad. Puedes renunciar a algo de seguridad operativa confiando en algunos amigos muy cercanos, en quienes sabes que puedes confiar profundamente. Incluso entonces, ten cuidado de no poner nada por escrito, en caso de que tengan que entregar sus correos electrónicos a las autoridades, o si sus dispositivos están comprometidos de alguna otra manera."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Mejor aún es encontrar algunos compañeros piratas. Si tus amigos cercanos están interesados en unirse a ti, ¡genial! De lo contrario, podrías encontrar a otros en línea. Lamentablemente, esta sigue siendo una comunidad de nicho. Hasta ahora solo hemos encontrado un puñado de otros que están activos en este espacio. Los lugares de inicio parecen ser los foros de Library Genesis y r/DataHoarder. El equipo de Archive Team también tiene individuos con ideas afines, aunque operan dentro de la ley (incluso si en algunas áreas grises de la ley). Las escenas tradicionales de \"warez\" y piratería también tienen personas que piensan de manera similar."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Estamos abiertos a ideas sobre cómo fomentar la comunidad y explorar ideas. No dudes en enviarnos un mensaje en Twitter o Reddit. Quizás podríamos organizar algún tipo de foro o grupo de chat. Un desafío es que esto puede ser fácilmente censurado al usar plataformas comunes, por lo que tendríamos que alojarlo nosotros mismos. También hay un compromiso entre tener estas discusiones completamente públicas (más potencial de participación) versus hacerlas privadas (no dejar que los posibles \"objetivos\" sepan que estamos a punto de recopilarlos). Tendremos que pensar en eso. ¡Déjanos saber si estás interesado en esto!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Proyectos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Cuando realizamos un proyecto, tiene un par de fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selección de dominio / filosofía: ¿En qué quieres enfocarte aproximadamente y por qué? ¿Cuáles son tus pasiones, habilidades y circunstancias únicas que puedes usar a tu favor?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selección de objetivo: ¿Qué colección específica vas a espejar?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raspado de metadata: Catalogar información sobre los archivos, sin descargar realmente los archivos (a menudo mucho más grandes) en sí mismos."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selección de datos: Basado en la metadata, determinar qué datos son más relevantes para archivar ahora. Podría ser todo, pero a menudo hay una forma razonable de ahorrar espacio y ancho de banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raspado de datos: Obtener realmente los datos."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribución: Empaquetarlo en torrents, anunciarlo en algún lugar, hacer que la gente lo difunda."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Estas no son fases completamente independientes, y a menudo las ideas de una fase posterior te envían de regreso a una fase anterior. Por ejemplo, durante el raspado de metadata podrías darte cuenta de que el objetivo que seleccionaste tiene mecanismos defensivos más allá de tu nivel de habilidad (como bloqueos de IP), por lo que vuelves atrás y encuentras un objetivo diferente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selección de dominio / filosofía"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "No hay escasez de conocimiento y patrimonio cultural por salvar, lo cual puede ser abrumador. Por eso a menudo es útil tomarse un momento y pensar en cuál puede ser tu contribución."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Todos tienen una forma diferente de pensar sobre esto, pero aquí hay algunas preguntas que podrías hacerte:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "¿Por qué te interesa esto? ¿Qué te apasiona? Si podemos reunir a un grupo de personas que archiven los tipos de cosas que les importan específicamente, ¡eso cubriría mucho! Sabrás mucho más que la persona promedio sobre tu pasión, como qué datos son importantes de guardar, cuáles son las mejores colecciones y comunidades en línea, y así sucesivamente."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "¿Qué habilidades tienes que puedes usar a tu favor? Por ejemplo, si eres un experto en seguridad en línea, puedes encontrar formas de derrotar bloqueos de IP para objetivos seguros. Si eres excelente organizando comunidades, entonces quizás puedas reunir a algunas personas en torno a un objetivo. Sin embargo, es útil saber algo de programación, aunque solo sea para mantener una buena seguridad operativa durante este proceso."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "¿Cuánto tiempo tienes para esto? Nuestro consejo sería comenzar pequeño y hacer proyectos más grandes a medida que te acostumbras, pero puede llegar a consumir todo tu tiempo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "¿Cuál sería un área de alto impacto en la que enfocarse? Si vas a pasar X horas en archivado pirata, entonces ¿cómo puedes obtener el mayor \"provecho por tu dinero\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "¿Cuáles son las formas únicas en las que estás pensando sobre esto? Podrías tener algunas ideas o enfoques interesantes que otros podrían haber pasado por alto."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "En nuestro caso, nos preocupaba en particular la preservación a largo plazo de la ciencia. Sabíamos sobre Library Genesis, y cómo se había espejado completamente muchas veces usando torrents. Nos encantó esa idea. Luego, un día, uno de nosotros intentó encontrar algunos libros de texto científicos en Library Genesis, pero no pudo encontrarlos, poniendo en duda cuán completo realmente era. Luego buscamos esos libros de texto en línea y los encontramos en otros lugares, lo que plantó la semilla para nuestro proyecto. Incluso antes de saber sobre la Z-Library, teníamos la idea de no intentar recopilar todos esos libros manualmente, sino de enfocarnos en espejar colecciones existentes y contribuirlas de nuevo a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selección de objetivo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Entonces, tenemos nuestra área que estamos observando, ahora ¿qué colección específica espejamos? Hay un par de cosas que hacen un buen objetivo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Única: no ya bien cubierta por otros proyectos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accesible: no utiliza toneladas de capas de protección para evitar que rasques su metadata y datos."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Perspectiva especial: tienes alguna información especial sobre este objetivo, como que de alguna manera tienes acceso especial a esta colección, o descubriste cómo vencer sus defensas. Esto no es necesario (nuestro próximo proyecto no hace nada especial), ¡pero ciertamente ayuda!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Cuando encontramos nuestros libros de texto de ciencia en sitios web distintos a Library Genesis, intentamos averiguar cómo llegaron a internet. Luego encontramos Z-Library, y nos dimos cuenta de que, aunque la mayoría de los libros no aparecen primero allí, eventualmente terminan allí. Aprendimos sobre su relación con Library Genesis, y la estructura de incentivos (financieros) y la interfaz de usuario superior, ambas hicieron que fuera una colección mucho más completa. Luego hicimos un raspado preliminar de metadata y datos, y nos dimos cuenta de que podíamos sortear sus límites de descarga de IP, aprovechando el acceso especial de uno de nuestros miembros a muchos servidores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Mientras exploras diferentes objetivos, ya es importante ocultar tus huellas usando VPNs y direcciones de correo electrónico desechables, de lo cual hablaremos más adelante."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Raspado de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Vamos a ponernos un poco más técnicos aquí. Para raspar realmente la metadata de los sitios web, hemos mantenido las cosas bastante simples. Usamos scripts de Python, a veces curl, y una base de datos MySQL para almacenar los resultados. No hemos usado ningún software de raspado sofisticado que pueda mapear sitios web complejos, ya que hasta ahora solo necesitábamos raspar uno o dos tipos de páginas simplemente enumerando a través de ids y analizando el HTML. Si no hay páginas fácilmente enumerables, entonces podrías necesitar un rastreador adecuado que intente encontrar todas las páginas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Antes de comenzar a extraer datos de todo un sitio web, intenta hacerlo manualmente por un tiempo. Recorre unas cuantas docenas de páginas tú mismo para entender cómo funciona. A veces, de esta manera, ya te encontrarás con bloqueos de IP u otros comportamientos interesantes. Lo mismo ocurre con la extracción de datos: antes de profundizar demasiado en este objetivo, asegúrate de que realmente puedes descargar sus datos de manera efectiva."

msgid "blog.how-to.projects.metadata.text3"
msgstr "Para sortear las restricciones, hay algunas cosas que puedes intentar. ¿Existen otras direcciones IP o servidores que alojen los mismos datos pero no tengan las mismas restricciones? ¿Hay puntos de acceso API que no tengan restricciones, mientras que otros sí? ¿A qué velocidad de descarga se bloquea tu IP y por cuánto tiempo? ¿O no estás bloqueado pero sí ralentizado? ¿Qué sucede si creas una cuenta de usuario, cómo cambian las cosas entonces? ¿Puedes usar HTTP/2 para mantener las conexiones abiertas, y eso aumenta la velocidad a la que puedes solicitar páginas? ¿Hay páginas que enumeran múltiples archivos a la vez, y la información listada allí es suficiente?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Las cosas que probablemente quieras guardar incluyen:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Título"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nombre de archivo / ubicación"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: puede ser algún ID interno, pero los IDs como ISBN o DOI también son útiles."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Tamaño: para calcular cuánto espacio en disco necesitas."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): para confirmar que descargaste el archivo correctamente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Fecha de añadido/modificado: para que puedas volver más tarde y descargar archivos que no descargaste antes (aunque a menudo también puedes usar el ID o hash para esto)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descripción, categoría, etiquetas, autores, idioma, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Normalmente hacemos esto en dos etapas. Primero descargamos los archivos HTML sin procesar, generalmente directamente en MySQL (para evitar muchos archivos pequeños, de lo cual hablamos más abajo). Luego, en un paso separado, revisamos esos archivos HTML y los analizamos en tablas MySQL reales. De esta manera, no tienes que volver a descargar todo desde cero si descubres un error en tu código de análisis, ya que puedes simplemente reprocesar los archivos HTML con el nuevo código. También es a menudo más fácil paralelizar el paso de procesamiento, ahorrando así algo de tiempo (y puedes escribir el código de procesamiento mientras el raspado está en marcha, en lugar de tener que escribir ambos pasos a la vez)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, ten en cuenta que para algunos objetivos el raspado de metadata es todo lo que hay. Hay algunas colecciones de metadata enormes que no están debidamente preservadas."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selección de datos"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "A menudo puedes usar la metadata para determinar un subconjunto razonable de datos para descargar. Incluso si eventualmente quieres descargar todos los datos, puede ser útil priorizar los elementos más importantes primero, en caso de que te detecten y mejoren las defensas, o porque necesitarías comprar más discos, o simplemente porque surge algo más en tu vida antes de que puedas descargar todo."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Por ejemplo, una colección podría tener múltiples ediciones del mismo recurso subyacente (como un libro o una película), donde una está marcada como de mejor calidad. Guardar esas ediciones primero tendría mucho sentido. Eventualmente podrías querer guardar todas las ediciones, ya que en algunos casos los metadata podrían estar etiquetados incorrectamente, o podría haber compensaciones desconocidas entre ediciones (por ejemplo, la \"mejor edición\" podría ser la mejor en la mayoría de los aspectos pero peor en otros, como una película con mayor resolución pero sin subtítulos)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "También puedes buscar en tu base de datos de metadata para encontrar cosas interesantes. ¿Cuál es el archivo más grande que se aloja y por qué es tan grande? ¿Cuál es el archivo más pequeño? ¿Hay patrones interesantes o inesperados en cuanto a ciertas categorías, idiomas, etc.? ¿Hay títulos duplicados o muy similares? ¿Hay patrones sobre cuándo se agregaron los datos, como un día en el que se agregaron muchos archivos a la vez? A menudo puedes aprender mucho observando el conjunto de datos de diferentes maneras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "En nuestro caso, deduplicamos los libros de Z-Library contra los hashes md5 en Library Genesis, ahorrando así mucho tiempo de descarga y espacio en disco. Sin embargo, esta es una situación bastante única. En la mayoría de los casos, no hay bases de datos completas de qué archivos ya están debidamente preservados por otros piratas. Esto en sí mismo es una gran oportunidad para alguien por ahí. Sería genial tener una visión general actualizada regularmente de cosas como música y películas que ya están ampliamente compartidas en sitios de torrents, y por lo tanto son de menor prioridad para incluir en espejos piratas."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extracción de datos"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Ahora estás listo para descargar realmente los datos en masa. Como se mencionó antes, en este punto ya deberías haber descargado manualmente un montón de archivos, para entender mejor el comportamiento y las restricciones del objetivo. Sin embargo, todavía habrá sorpresas para ti una vez que realmente comiences a descargar muchos archivos a la vez."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nuestro consejo aquí es principalmente mantenerlo simple. Comienza simplemente descargando un montón de archivos. Puedes usar Python y luego expandir a múltiples hilos. Pero a veces incluso más simple es generar archivos Bash directamente desde la base de datos y luego ejecutar varios de ellos en múltiples ventanas de terminal para escalar. Un truco técnico rápido que vale la pena mencionar aquí es usar OUTFILE en MySQL, que puedes escribir en cualquier lugar si desactivas \"secure_file_priv\" en mysqld.cnf (y asegúrate de también desactivar/invalidar AppArmor si estás en Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Almacenamos los datos en discos duros simples. Comienza con lo que tengas y expande lentamente. Puede ser abrumador pensar en almacenar cientos de TBs de datos. Si esa es la situación que enfrentas, simplemente publica un buen subconjunto primero, y en tu anuncio pide ayuda para almacenar el resto. Si deseas obtener más discos duros tú mismo, entonces r/DataHoarder tiene algunos buenos recursos para conseguir buenas ofertas."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Trata de no preocuparte demasiado por sistemas de archivos sofisticados. Es fácil caer en el agujero del conejo de configurar cosas como ZFS. Un detalle técnico a tener en cuenta, sin embargo, es que muchos sistemas de archivos no manejan bien muchos archivos. Hemos encontrado que una solución simple es crear múltiples directorios, por ejemplo, para diferentes rangos de ID o prefijos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Después de descargar los datos, asegúrate de verificar la integridad de los archivos usando hashes en la metadata, si están disponibles."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribución"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Tienes los datos, dándote así la posesión del primer espejo pirata del mundo de tu objetivo (muy probablemente). En muchos sentidos, la parte más difícil ha terminado, pero la parte más arriesgada aún está por delante. Después de todo, hasta ahora has sido sigiloso; volando bajo el radar. Todo lo que tenías que hacer era usar un buen VPN en todo momento, no llenar tus datos personales en ningún formulario (obvio), y quizás usar una sesión de navegador especial (o incluso una computadora diferente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Ahora tienes que distribuir los datos. En nuestro caso, primero queríamos contribuir los libros de vuelta a Library Genesis, pero luego descubrimos rápidamente las dificultades en eso (clasificación de ficción vs no ficción). Así que decidimos distribuir usando torrents al estilo de Library Genesis. Si tienes la oportunidad de contribuir a un proyecto existente, eso podría ahorrarte mucho tiempo. Sin embargo, actualmente no hay muchos espejos piratas bien organizados."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Así que digamos que decides distribuir torrents tú mismo. Intenta mantener esos archivos pequeños, para que sean fáciles de espejar en otros sitios web. Luego tendrás que sembrar los torrents tú mismo, mientras te mantienes anónimo. Puedes usar una VPN (con o sin reenvío de puertos), o pagar con Bitcoins mezclados por un Seedbox. Si no sabes qué significan algunos de esos términos, tendrás mucho que leer, ya que es importante que entiendas los riesgos aquí."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Puedes alojar los archivos torrent en sitios web de torrents existentes. En nuestro caso, elegimos alojar un sitio web, ya que también queríamos difundir nuestra filosofía de manera clara. Puedes hacer esto tú mismo de manera similar (usamos Njalla para nuestros dominios y alojamiento, pagado con Bitcoins mezclados), pero también siéntete libre de contactarnos para que alojemos tus torrents. Estamos buscando construir un índice completo de espejos piratas con el tiempo, si esta idea se populariza."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "En cuanto a la selección de VPN, ya se ha escrito mucho sobre esto, así que solo repetiremos el consejo general de elegir por reputación. Las políticas de no registro probadas en tribunales con largos historiales de protección de la privacidad son la opción de menor riesgo, en nuestra opinión. Ten en cuenta que incluso cuando haces todo bien, nunca puedes llegar a un riesgo cero. Por ejemplo, al sembrar tus torrents, un actor estatal altamente motivado probablemente pueda observar los flujos de datos entrantes y salientes de los servidores VPN y deducir quién eres. O simplemente puedes cometer un error de alguna manera. Probablemente ya lo hemos hecho, y lo haremos de nuevo. Afortunadamente, a los estados nacionales no les importa <em>tanto</em> la piratería."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una decisión que tomar para cada proyecto es si publicarlo usando la misma identidad que antes o no. Si sigues usando el mismo nombre, los errores en la seguridad operativa de proyectos anteriores podrían volver a afectarte. Pero publicar bajo diferentes nombres significa que no construyes una reputación duradera. Elegimos tener una fuerte seguridad operativa desde el principio para poder seguir usando la misma identidad, pero no dudaremos en publicar bajo un nombre diferente si cometemos un error o si las circunstancias lo requieren."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Difundir la palabra puede ser complicado. Como dijimos, esta sigue siendo una comunidad de nicho. Originalmente publicamos en Reddit, pero realmente ganamos tracción en Hacker News. Por ahora, nuestra recomendación es publicarlo en algunos lugares y ver qué pasa. Y nuevamente, contáctanos. Nos encantaría difundir la palabra sobre más esfuerzos de archivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Esperamos que esto sea útil para los archivistas piratas que recién comienzan. Estamos emocionados de darte la bienvenida a este mundo, así que no dudes en comunicarte. Preservemos tanto del conocimiento y la cultura del mundo como podamos, y espéjalo por todas partes."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentamos el Espejo de la Biblioteca Pirata: Preservando 7TB de libros (que no están en Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Este proyecto (EDITADO: trasladado a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>) tiene como objetivo contribuir a la preservación y liberación del conocimiento humano. Hacemos nuestra pequeña y humilde contribución, siguiendo los pasos de los grandes que nos precedieron."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "El enfoque de este proyecto se ilustra con su nombre:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Deliberadamente violamos la ley de derechos de autor en la mayoría de los países. Esto nos permite hacer algo que las entidades legales no pueden hacer: asegurarnos de que los libros se reflejen ampliamente."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Como la mayoría de las bibliotecas, nos enfocamos principalmente en materiales escritos como libros. Podríamos expandirnos a otros tipos de medios en el futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Espejo</strong> - Somos estrictamente un espejo de bibliotecas existentes. Nos enfocamos en la preservación, no en hacer que los libros sean fácilmente buscables y descargables (acceso) o en fomentar una gran comunidad de personas que contribuyan con nuevos libros (fuente)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La primera biblioteca que hemos reflejado es Z-Library. Esta es una biblioteca popular (e ilegal). Han tomado la colección de Library Genesis y la han hecho fácilmente buscable. Además, se han vuelto muy efectivos en solicitar nuevas contribuciones de libros, incentivando a los usuarios contribuyentes con varios beneficios. Actualmente no contribuyen con estos nuevos libros de vuelta a Library Genesis. Y a diferencia de Library Genesis, no hacen que su colección sea fácilmente reflejable, lo que impide una amplia preservación. Esto es importante para su modelo de negocio, ya que cobran dinero por acceder a su colección en masa (más de 10 libros por día)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "No hacemos juicios morales sobre cobrar dinero por el acceso masivo a una colección de libros ilegal. No cabe duda de que Z-Library ha tenido éxito en expandir el acceso al conocimiento y en obtener más libros. Simplemente estamos aquí para hacer nuestra parte: asegurar la preservación a largo plazo de esta colección privada."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Nos gustaría invitarle a ayudar a preservar y liberar el conocimiento humano descargando y compartiendo nuestros torrents. Consulte la página del proyecto para obtener más información sobre cómo están organizados los datos."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "También nos gustaría mucho invitarle a contribuir con sus ideas sobre qué colecciones reflejar a continuación y cómo hacerlo. Juntos podemos lograr mucho. Esta es solo una pequeña contribución entre muchas otras. Gracias, por todo lo que hace."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>No enlazamos a los archivos desde este blog. Por favor, encuéntrelos usted mismo.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Volcado de ISBNdb, o ¿Cuántos libros se preservan para siempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si deduplicáramos adecuadamente los archivos de las bibliotecas en la sombra, ¿qué porcentaje de todos los libros del mundo hemos preservado?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Con el Espejo de la Biblioteca Pirata (EDITADO: trasladado a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>), nuestro objetivo es tomar todos los libros del mundo y preservarlos para siempre.<sup>1</sup> Entre nuestros torrents de Z-Library y los torrents originales de Library Genesis, tenemos 11,783,153 archivos. Pero, ¿cuántos son realmente? Si deduplicáramos adecuadamente esos archivos, ¿qué porcentaje de todos los libros del mundo hemos preservado? Realmente nos gustaría tener algo como esto:"

msgid "blog.isbndb-dump.10%"
msgstr "10%% of del patrimonio escrito de la humanidad preservado para siempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Para un porcentaje, necesitamos un denominador: el número total de libros publicados.<sup>2</sup> Antes de la desaparición de Google Books, un ingeniero del proyecto, Leonid Taycher, <a %(booksearch_blogspot)s>intentó estimar</a> este número. Llegó — en tono de broma — a 129,864,880 (“al menos hasta el domingo”). Estimó este número construyendo una base de datos unificada de todos los libros del mundo. Para esto, reunió diferentes conjuntos de datos y luego los fusionó de varias maneras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como un breve paréntesis, hay otra persona que intentó catalogar todos los libros del mundo: Aaron Swartz, el difunto activista digital y cofundador de Reddit.<sup>3</sup> Él <a %(youtube)s>inició Open Library</a> con el objetivo de “una página web para cada libro publicado”, combinando datos de muchas fuentes diferentes. Terminó pagando el precio más alto por su trabajo de preservación digital cuando fue procesado por descargar en masa artículos académicos, lo que llevó a su suicidio. No hace falta decir que esta es una de las razones por las que nuestro grupo es seudónimo y por la que estamos siendo muy cuidadosos. Open Library sigue siendo heroicamente gestionada por personas en el Internet Archive, continuando el legado de Aaron. Volveremos a esto más adelante en esta publicación."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "En la publicación del blog de Google, Taycher describe algunos de los desafíos al estimar este número. Primero, ¿qué constituye un libro? Hay algunas definiciones posibles:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copias físicas.</strong> Obviamente, esto no es muy útil, ya que son solo duplicados del mismo material. Sería genial si pudiéramos preservar todas las anotaciones que la gente hace en los libros, como los famosos \"garabatos en los márgenes\" de Fermat. Pero, por desgracia, eso seguirá siendo un sueño de archivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obras”.</strong> Por ejemplo, “Harry Potter y la Cámara Secreta” como un concepto lógico, que abarca todas sus versiones, como diferentes traducciones y reimpresiones. Esta es una definición algo útil, pero puede ser difícil trazar la línea de lo que cuenta. Por ejemplo, probablemente queramos preservar diferentes traducciones, aunque las reimpresiones con solo diferencias menores podrían no ser tan importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Ediciones”.</strong> Aquí cuentas cada versión única de un libro. Si algo es diferente, como una portada distinta o un prólogo diferente, cuenta como una edición diferente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Archivos.</strong> Al trabajar con bibliotecas en la sombra como Library Genesis, Sci-Hub o Z-Library, hay una consideración adicional. Puede haber múltiples escaneos de la misma edición. Y las personas pueden crear mejores versiones de archivos existentes, escaneando el texto usando OCR o corrigiendo páginas que fueron escaneadas en ángulo. Queremos contar estos archivos solo como una edición, lo que requeriría buenos metadata o deduplicación usando medidas de similitud de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Las “Ediciones” parecen la definición más práctica de lo que son los “libros”. Convenientemente, esta definición también se utiliza para asignar números ISBN únicos. Un ISBN, o Número Estándar Internacional de Libros, se utiliza comúnmente para el comercio internacional, ya que está integrado con el sistema internacional de códigos de barras (“Número Internacional de Artículo”). Si quieres vender un libro en tiendas, necesita un código de barras, por lo que obtienes un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "La publicación en el blog de Taycher menciona que, aunque los ISBN son útiles, no son universales, ya que solo se adoptaron realmente a mediados de los setenta, y no en todo el mundo. Aun así, el ISBN es probablemente el identificador más utilizado de ediciones de libros, por lo que es nuestro mejor punto de partida. Si podemos encontrar todos los ISBN del mundo, obtenemos una lista útil de qué libros aún necesitan ser preservados."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Entonces, ¿de dónde obtenemos los datos? Hay varios esfuerzos existentes que están tratando de compilar una lista de todos los libros del mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Después de todo, hicieron esta investigación para Google Books. Sin embargo, su metadata no es accesible en masa y es bastante difícil de extraer."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como se mencionó antes, esta es su misión completa. Han obtenido enormes cantidades de datos de bibliotecas de bibliotecas cooperantes y archivos nacionales, y continúan haciéndolo. También tienen bibliotecarios voluntarios y un equipo técnico que están tratando de deduplicar registros y etiquetarlos con todo tipo de metadata. Lo mejor de todo es que su conjunto de datos es completamente abierto. Puedes simplemente <a %(openlibrary)s>descargarlo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Este es un sitio web administrado por la organización sin fines de lucro OCLC, que vende sistemas de gestión de bibliotecas. Agregan metadata de libros de muchas bibliotecas y la ponen a disposición a través del sitio web de WorldCat. Sin embargo, también ganan dinero vendiendo estos datos, por lo que no están disponibles para descarga masiva. Tienen algunos conjuntos de datos masivos más limitados disponibles para descargar, en cooperación con bibliotecas específicas."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este es el tema de esta publicación en el blog. ISBNdb extrae datos de varios sitios web para metadata de libros, en particular datos de precios, que luego venden a libreros, para que puedan fijar el precio de sus libros de acuerdo con el resto del mercado. Dado que los ISBN son bastante universales hoy en día, efectivamente construyeron una “página web para cada libro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Varios sistemas de bibliotecas y archivos individuales.</strong> Hay bibliotecas y archivos que no han sido indexados y agregados por ninguno de los anteriores, a menudo porque están subfinanciados, o por otras razones no quieren compartir sus datos con Open Library, OCLC, Google, etc. Muchas de estas tienen registros digitales accesibles a través de internet, y a menudo no están muy bien protegidos, por lo que si quieres ayudar y divertirte aprendiendo sobre sistemas de bibliotecas extraños, estos son excelentes puntos de partida."

msgid "blog.isbndb-dump.text8"
msgstr "En esta publicación, nos complace anunciar un pequeño lanzamiento (en comparación con nuestros lanzamientos anteriores de Z-Library). Hemos extraído la mayor parte de ISBNdb y hemos puesto los datos a disposición para torrenting en el sitio web del Espejo de la Biblioteca Pirata (EDITADO: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>; no lo enlazaremos directamente aquí, solo búsquelo). Estos son alrededor de 30.9 millones de registros (20GB como <a %(jsonlines)s>Líneas JSON</a>; 4.4GB comprimidos). En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que podríamos haber pasado por alto algunos, o <em>ellos</em> podrían estar haciendo algo mal. En cualquier caso, por ahora no compartiremos exactamente cómo lo hicimos; dejaremos eso como un ejercicio para el lector. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Lo que compartiremos es un análisis preliminar, para tratar de acercarnos a estimar el número de libros en el mundo. Observamos tres conjuntos de datos: este nuevo conjunto de datos de ISBNdb, nuestra publicación original de metadata que extraímos de la biblioteca en la sombra Z-Library (que incluye Library Genesis), y el volcado de datos de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Comencemos con algunos números aproximados:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "En tanto Z-Library/Libgen como Open Library hay muchos más libros que ISBN únicos. ¿Significa eso que muchos de esos libros no tienen ISBN, o simplemente falta el metadata del ISBN? Probablemente podamos responder a esta pregunta con una combinación de coincidencia automatizada basada en otros atributos (título, autor, editor, etc.), incorporando más fuentes de datos y extrayendo ISBN de los escaneos reales de los libros (en el caso de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "¿Cuántos de esos ISBN son únicos? Esto se ilustra mejor con un diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Para ser más precisos:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "¡Nos sorprendió lo poco que se superponen! ISBNdb tiene una gran cantidad de ISBN que no aparecen ni en Z-Library ni en Open Library, y lo mismo ocurre (en menor grado pero aún sustancial) con los otros dos. Esto plantea muchas nuevas preguntas. ¿Cuánto ayudaría la coincidencia automatizada en etiquetar los libros que no fueron etiquetados con ISBN? ¿Habría muchas coincidencias y, por lo tanto, un aumento en la superposición? Además, ¿qué pasaría si incorporamos un cuarto o quinto conjunto de datos? ¿Cuánta superposición veríamos entonces?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Esto nos da un punto de partida. Ahora podemos mirar todos los ISBN que no estaban en el conjunto de datos de Z-Library, y que tampoco coinciden con los campos de título/autor. Eso puede darnos una idea de cómo preservar todos los libros del mundo: primero extrayendo de internet los escaneos, luego saliendo en la vida real para escanear libros. Esto último incluso podría ser financiado por la multitud, o impulsado por \"recompensas\" de personas que les gustaría ver ciertos libros digitalizados. Todo eso es una historia para otro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si deseas ayudar con cualquiera de estas tareas — análisis adicional; recopilación de más metadata; búsqueda de más libros; OCR de libros; hacer esto para otros dominios (por ejemplo, artículos, audiolibros, películas, programas de televisión, revistas) o incluso hacer que algunos de estos datos estén disponibles para cosas como el entrenamiento de modelos de lenguaje grande/ML — por favor contáctame (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si estás específicamente interesado en el análisis de datos, estamos trabajando para hacer que nuestros Datasets y scripts estén disponibles en un formato más fácil de usar. Sería genial si pudieras simplemente bifurcar un cuaderno y comenzar a experimentar con esto."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, si deseas apoyar este trabajo, por favor considera hacer una donación. Esta es una operación llevada a cabo completamente por voluntarios, y tu contribución marca una gran diferencia. Cada aporte cuenta. Por ahora aceptamos donaciones en criptomonedas; consulta la página de Donaciones en el Archivo de Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Para alguna definición razonable de \"para siempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Por supuesto, el patrimonio escrito de la humanidad es mucho más que libros, especialmente hoy en día. Para el propósito de esta publicación y nuestros lanzamientos recientes, nos estamos enfocando en libros, pero nuestros intereses se extienden más allá."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Hay mucho más que se puede decir sobre Aaron Swartz, pero solo queríamos mencionarlo brevemente, ya que juega un papel fundamental en esta historia. A medida que pasa el tiempo, más personas podrían encontrarse con su nombre por primera vez, y posteriormente sumergirse en el tema por sí mismas."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La ventana crítica de las bibliotecas en la sombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "¿Cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad, cuando ya están acercándose a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versión en chino 中文版</a>, discute en <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "En el Archivo de Anna, a menudo nos preguntan cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad, cuando el tamaño total ya se está acercando a 1 Petabyte (1000 TB), y sigue creciendo. En este artículo veremos nuestra filosofía, y por qué la próxima década es crítica para nuestra misión de preservar el conocimiento y la cultura de la humanidad."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "El <a %(annas_archive_stats)s>tamaño total</a> de nuestras colecciones, en los últimos meses, desglosado por número de sembradores de torrents."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioridades"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "¿Por qué nos importan tanto los artículos y los libros? Dejemos de lado nuestra creencia fundamental en la preservación en general — podríamos escribir otra publicación sobre eso. Entonces, ¿por qué específicamente artículos y libros? La respuesta es simple: <strong>densidad de información</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Por megabyte de almacenamiento, el texto escrito almacena la mayor cantidad de información de todos los medios. Aunque nos importa tanto el conocimiento como la cultura, nos importa más el primero. En general, encontramos una jerarquía de densidad de información e importancia de la preservación que se ve aproximadamente así:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Artículos académicos, revistas, informes"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Datos orgánicos como secuencias de ADN, semillas de plantas o muestras microbianas"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libros de no ficción"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Código de software de ciencia e ingeniería"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Datos de medición como mediciones científicas, datos económicos, informes corporativos"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sitios web de ciencia e ingeniería, discusiones en línea"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistas de no ficción, periódicos, manuales"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcripciones de no ficción de charlas, documentales, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Datos internos de corporaciones o gobiernos (filtraciones)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Registros de metadata en general (de no ficción y ficción; de otros medios, arte, personas, etc.; incluyendo reseñas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Datos geográficos (por ejemplo, mapas, estudios geológicos)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripciones de procedimientos legales o judiciales"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versiones ficticias o de entretenimiento de todo lo anterior"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La clasificación en esta lista es algo arbitraria — varios elementos están empatados o hay desacuerdos dentro de nuestro equipo — y probablemente estamos olvidando algunas categorías importantes. Pero esta es aproximadamente nuestra prioridad."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Algunos de estos elementos son demasiado diferentes de los otros como para preocuparnos (o ya están siendo atendidos por otras instituciones), como los datos orgánicos o los datos geográficos. Pero la mayoría de los elementos en esta lista son realmente importantes para nosotros."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Otro gran factor en nuestra priorización es cuán en riesgo está una obra determinada. Preferimos centrarnos en obras que son:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Raras"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Únicamente desatendidas"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Únicamente en riesgo de destrucción (por ejemplo, por guerra, recortes de financiación, demandas o persecución política)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, nos importa la escala. Tenemos tiempo y dinero limitados, por lo que preferimos pasar un mes salvando 10,000 libros que 1,000 libros, si son igualmente valiosos y están en riesgo."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliotecas en la sombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Existen muchas organizaciones con misiones y prioridades similares. De hecho, hay bibliotecas, archivos, laboratorios, museos y otras instituciones encargadas de la preservación de este tipo. Muchas de ellas están bien financiadas, por gobiernos, individuos o corporaciones. Pero tienen un gran punto ciego: el sistema legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Aquí radica el papel único de las bibliotecas en la sombra y la razón por la que existe el Archivo de Anna. Podemos hacer cosas que otras instituciones no tienen permitido hacer. Ahora bien, no es (a menudo) que podamos archivar materiales que son ilegales de preservar en otros lugares. No, es legal en muchos lugares construir un archivo con cualquier libro, documento, revista, etc."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Pero lo que a menudo les falta a los archivos legales es <strong>redundancia y longevidad</strong>. Existen libros de los cuales solo hay una copia en alguna biblioteca física en algún lugar. Existen registros de metadata custodiados por una sola corporación. Existen periódicos solo preservados en microfilm en un solo archivo. Las bibliotecas pueden sufrir recortes de financiación, las corporaciones pueden quebrar, los archivos pueden ser bombardeados e incendiados. Esto no es hipotético, sucede todo el tiempo."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Lo que podemos hacer de manera única en el Archivo de Anna es almacenar muchas copias de obras, a gran escala. Podemos recopilar documentos, libros, revistas y más, y distribuirlos en masa. Actualmente lo hacemos a través de torrents, pero las tecnologías exactas no importan y cambiarán con el tiempo. Lo importante es distribuir muchas copias por todo el mundo. Esta cita de hace más de 200 años sigue siendo cierta:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Lo perdido no se puede recuperar; pero salvemos lo que queda: no con bóvedas y cerraduras que los alejan de la vista y uso del público, consignándolos al desperdicio del tiempo, sino mediante una multiplicación de copias que los coloque fuera del alcance de los accidentes.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Una breve nota sobre el dominio público. Dado que el Archivo de Anna se centra de manera única en actividades que son ilegales en muchos lugares del mundo, no nos preocupamos por colecciones ampliamente disponibles, como los libros de dominio público. Las entidades legales a menudo ya se encargan bien de eso. Sin embargo, hay consideraciones que a veces nos llevan a trabajar en colecciones de acceso público:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Los registros de metadata se pueden ver libremente en el sitio web de Worldcat, pero no se pueden descargar en masa (hasta que los <a %(worldcat_scrape)s>raspamos</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "El código puede ser de código abierto en Github, pero Github en su totalidad no puede ser fácilmente espejado y, por lo tanto, preservado (aunque en este caso particular hay copias suficientemente distribuidas de la mayoría de los repositorios de código)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit es gratuito de usar, pero recientemente ha implementado medidas estrictas contra el raspado, a raíz del entrenamiento de LLM hambriento de datos (más sobre eso más adelante)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Una multiplicación de copias"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Volviendo a nuestra pregunta original: ¿cómo podemos afirmar que preservamos nuestras colecciones a perpetuidad? El principal problema aquí es que nuestra colección ha estado <a %(torrents_stats)s>creciendo</a> rápidamente, al raspar y abrir algunas colecciones masivas (además del increíble trabajo ya realizado por otras bibliotecas en la sombra de datos abiertos como Sci-Hub y Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Este crecimiento de datos dificulta que las colecciones sean espejadas en todo el mundo. ¡El almacenamiento de datos es caro! Pero somos optimistas, especialmente al observar las siguientes tres tendencias."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Hemos recogido los frutos más accesibles"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Esto sigue directamente de nuestras prioridades discutidas anteriormente. Preferimos trabajar en liberar grandes colecciones primero. Ahora que hemos asegurado algunas de las colecciones más grandes del mundo, esperamos que nuestro crecimiento sea mucho más lento."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Todavía hay una larga cola de colecciones más pequeñas, y se escanean o publican nuevos libros todos los días, pero la tasa probablemente será mucho más lenta. Podríamos aún duplicar o incluso triplicar nuestro tamaño, pero en un período de tiempo más largo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Los costos de almacenamiento continúan disminuyendo exponencialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Al momento de escribir, los <a %(diskprices)s>precios de los discos</a> por TB están alrededor de $12 para discos nuevos, $8 para discos usados y $4 para cinta. Si somos conservadores y solo miramos los discos nuevos, eso significa que almacenar un petabyte cuesta alrededor de $12,000. Si asumimos que nuestra biblioteca se triplicará de 900TB a 2.7PB, eso significaría $32,400 para espejar toda nuestra biblioteca. Sumando electricidad, costo de otros hardware, etc., redondeemos a $40,000. O con cinta, más como $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Por un lado, <strong>$15,000–$40,000 por la suma de todo el conocimiento humano es una ganga</strong>. Por otro lado, es un poco elevado esperar toneladas de copias completas, especialmente si también queremos que esas personas sigan sembrando sus torrents para el beneficio de otros."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Eso es hoy. Pero el progreso avanza:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Los costos de los discos duros por TB se han reducido aproximadamente a un tercio en los últimos 10 años, y probablemente seguirán disminuyendo a un ritmo similar. La cinta parece estar en una trayectoria similar. Los precios de los SSD están bajando aún más rápido, y podrían superar los precios de los HDD para finales de la década."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendencias de precios de HDD de diferentes fuentes (haga clic para ver el estudio)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si esto se mantiene, en 10 años podríamos estar viendo solo $5,000–$13,000 para espejar toda nuestra colección (1/3), o incluso menos si crecemos menos en tamaño. Aunque sigue siendo mucho dinero, esto será alcanzable para muchas personas. Y podría ser aún mejor debido al siguiente punto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Mejoras en la densidad de información"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Actualmente almacenamos libros en los formatos en bruto que se nos entregan. Claro, están comprimidos, pero a menudo todavía son grandes escaneos o fotografías de páginas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Hasta ahora, las únicas opciones para reducir el tamaño total de nuestra colección han sido a través de una compresión más agresiva o la deduplicación. Sin embargo, para obtener ahorros significativos, ambas son demasiado pérdidas para nuestro gusto. La compresión intensa de fotos puede hacer que el texto sea apenas legible. Y la deduplicación requiere una alta confianza de que los libros sean exactamente iguales, lo cual a menudo es demasiado inexacto, especialmente si los contenidos son los mismos pero las digitalizaciones se realizan en diferentes ocasiones."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Siempre ha habido una tercera opción, pero su calidad ha sido tan abismal que nunca la consideramos: <strong>OCR, o Reconocimiento Óptico de Caracteres</strong>. Este es el proceso de convertir fotos en texto plano, utilizando IA para detectar los caracteres en las fotos. Las herramientas para esto han existido durante mucho tiempo y han sido bastante decentes, pero \"bastante decente\" no es suficiente para fines de preservación."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Sin embargo, los recientes modelos de aprendizaje profundo multimodal han hecho un progreso extremadamente rápido, aunque todavía a altos costos. Esperamos que tanto la precisión como los costos mejoren drásticamente en los próximos años, hasta el punto en que será realista aplicarlo a toda nuestra biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Mejoras en OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Cuando eso suceda, probablemente aún preservaremos los archivos originales, pero además podríamos tener una versión mucho más pequeña de nuestra biblioteca que la mayoría de las personas querrán espejar. Lo interesante es que el texto en bruto se comprime aún mejor y es mucho más fácil de deduplicar, lo que nos brinda aún más ahorros."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "En general, no es irrealista esperar al menos una reducción de 5-10 veces en el tamaño total de los archivos, quizás incluso más. Incluso con una reducción conservadora de 5 veces, estaríamos viendo <strong>$1,000–$3,000 en 10 años incluso si nuestra biblioteca se triplica en tamaño</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Ventana crítica"

msgid "blog.critical-window.the-window.text1"
msgstr "Si estas previsiones son precisas, <strong>solo necesitamos esperar un par de años</strong> antes de que toda nuestra colección sea ampliamente espejada. Así, en palabras de Thomas Jefferson, “colocados más allá del alcance del accidente.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Desafortunadamente, la llegada de los LLM y su entrenamiento ávido de datos ha puesto a muchos titulares de derechos de autor a la defensiva. Incluso más de lo que ya estaban. Muchos sitios web están dificultando el raspado y archivo, las demandas están volando, y mientras tanto, las bibliotecas y archivos físicos continúan siendo descuidados."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Solo podemos esperar que estas tendencias continúen empeorando, y que muchas obras se pierdan mucho antes de que entren en el dominio público."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Estamos en la víspera de una revolución en la preservación, pero <q>lo perdido no se puede recuperar.</q></strong> Tenemos una ventana crítica de aproximadamente 5-10 años durante la cual todavía es bastante costoso operar una biblioteca fantasma y crear muchos espejos alrededor del mundo, y durante la cual el acceso no se ha cerrado completamente aún."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si podemos superar esta ventana, entonces habremos preservado el conocimiento y la cultura de la humanidad para siempre. No debemos dejar que este tiempo se desperdicie. No debemos permitir que esta ventana crítica se cierre sobre nosotros."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Vamos."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Acceso exclusivo para empresas de LLM a la mayor colección de libros de no ficción en chino del mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versión en chino 中文版</a>, <a %(news_ycombinator)s>Discutir en Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>Resumen:</strong> El Archivo de Anna adquirió una colección única de 7.5 millones / 350TB de libros de no ficción en chino, más grande que Library Genesis. Estamos dispuestos a dar a una empresa de LLM acceso exclusivo, a cambio de OCR de alta calidad y extracción de texto.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Esta es una breve publicación de blog. Estamos buscando alguna empresa o institución que nos ayude con OCR y extracción de texto para una colección masiva que adquirimos, a cambio de acceso exclusivo anticipado. Después del período de embargo, por supuesto, liberaremos toda la colección."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "El texto académico de alta calidad es extremadamente útil para el entrenamiento de LLM. Aunque nuestra colección es en chino, esto debería ser incluso útil para entrenar LLM en inglés: los modelos parecen codificar conceptos y conocimientos independientemente del idioma fuente."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Para esto, el texto necesita ser extraído de las digitalizaciones. ¿Qué obtiene el Archivo de Anna de esto? Búsqueda de texto completo de los libros para sus usuarios."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Debido a que nuestros objetivos se alinean con los de los desarrolladores de LLM, estamos buscando un colaborador. Estamos dispuestos a darte <strong>acceso anticipado exclusivo a esta colección en bloque por 1 año</strong>, si puedes realizar un OCR y extracción de texto adecuados. Si estás dispuesto a compartir todo el código de tu pipeline con nosotros, estaríamos dispuestos a embargar la colección por más tiempo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Páginas de ejemplo"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Para demostrarnos que tienes un buen pipeline, aquí tienes algunas páginas de ejemplo para comenzar, de un libro sobre superconductores. Tu pipeline debería manejar adecuadamente matemáticas, tablas, gráficos, notas al pie, etc."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Envía tus páginas procesadas a nuestro correo electrónico. Si se ven bien, te enviaremos más en privado, y esperamos que puedas ejecutar rápidamente tu pipeline en ellas también. Una vez que estemos satisfechos, podemos llegar a un acuerdo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Colección"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Algo más de información sobre la colección. <a %(duxiu)s>Duxiu</a> es una base de datos masiva de libros escaneados, creada por el <a %(chaoxing)s>Grupo de Biblioteca Digital SuperStar</a>. La mayoría son libros académicos, escaneados para hacerlos disponibles digitalmente a universidades y bibliotecas. Para nuestro público de habla inglesa, <a %(library_princeton)s>Princeton</a> y la <a %(guides_lib_uw)s>Universidad de Washington</a> tienen buenos resúmenes. También hay un excelente artículo que ofrece más contexto: <a %(doi)s>“Digitalización de Libros Chinos: Un Estudio de Caso del Motor de Búsqueda SuperStar DuXiu Scholar”</a> (búscalo en el Archivo de Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Los libros de Duxiu han sido pirateados durante mucho tiempo en el internet chino. Usualmente se venden por menos de un dólar por revendedores. Normalmente se distribuyen usando el equivalente chino de Google Drive, que a menudo ha sido hackeado para permitir más espacio de almacenamiento. Algunos detalles técnicos se pueden encontrar <a %(github_duty_machine)s>aquí</a> y <a %(github_821_github_io)s>aquí</a>."

msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Aunque los libros se han distribuido de manera semi-pública, es bastante difícil obtenerlos en grandes cantidades. Esto estaba en lo alto de nuestra lista de tareas pendientes, y asignamos varios meses de trabajo a tiempo completo para ello. Sin embargo, recientemente un voluntario increíble, asombroso y talentoso se puso en contacto con nosotros, diciéndonos que ya había hecho todo este trabajo, a un gran costo. Compartieron la colección completa con nosotros, sin esperar nada a cambio, excepto la garantía de preservación a largo plazo. Verdaderamente notable. Aceptaron pedir ayuda de esta manera para que la colección sea procesada con OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La colección consta de 7,543,702 archivos. Esto es más que la no ficción de Library Genesis (alrededor de 5.3 millones). El tamaño total de los archivos es de aproximadamente 359TB (326TiB) en su forma actual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Estamos abiertos a otras propuestas e ideas. Solo contáctanos. Consulta el Archivo de Anna para más información sobre nuestras colecciones, esfuerzos de preservación y cómo puedes ayudar. ¡Gracias!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Advertencia: esta publicación de blog ha sido descontinuada. Hemos decidido que IPFS aún no está listo para el horario estelar. Aún enlazaremos a archivos en IPFS desde el Archivo de Anna cuando sea posible, pero ya no lo alojaremos nosotros mismos, ni recomendamos a otros que lo espejen usando IPFS. Por favor, consulta nuestra página de Torrents si deseas ayudar a preservar nuestra colección."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Ayuda a sembrar Z-Library en IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cómo operar una biblioteca fantasma: operaciones en el Archivo de Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "No hay <q>AWS para organizaciones benéficas en la sombra,</q> entonces, ¿cómo operamos el Archivo de Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Dirijo <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, el motor de búsqueda de código abierto sin fines de lucro más grande del mundo para <a %(wikipedia_shadow_library)s>bibliotecas en la sombra</a>, como Sci-Hub, Library Genesis y Z-Library. Nuestro objetivo es hacer que el conocimiento y la cultura sean fácilmente accesibles, y en última instancia, construir una comunidad de personas que juntos archiven y preserven <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>todos los libros del mundo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "En este artículo mostraré cómo operamos este sitio web, y los desafíos únicos que conlleva operar un sitio web con un estatus legal cuestionable, ya que no hay un “AWS para organizaciones benéficas en la sombra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>También consulta el artículo hermano <a %(blog_how_to_become_a_pirate_archivist)s>Cómo convertirse en un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Fichas de innovación"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Comencemos con nuestra pila tecnológica. Es deliberadamente aburrida. Usamos Flask, MariaDB y ElasticSearch. Eso es literalmente todo. La búsqueda es en gran medida un problema resuelto, y no pretendemos reinventarlo. Además, tenemos que gastar nuestras <a %(mcfunley)s>fichas de innovación</a> en otra cosa: no ser derribados por las autoridades."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Entonces, ¿qué tan legal o ilegal es exactamente el Archivo de Anna? Esto depende principalmente de la jurisdicción legal. La mayoría de los países creen en alguna forma de derechos de autor, lo que significa que a las personas o empresas se les asigna un monopolio exclusivo sobre ciertos tipos de obras por un cierto período de tiempo. Como un aparte, en el Archivo de Anna creemos que, aunque hay algunos beneficios, en general los derechos de autor son un neto negativo para la sociedad, pero esa es una historia para otro momento."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Este monopolio exclusivo sobre ciertas obras significa que es ilegal para cualquiera fuera de este monopolio distribuir directamente esas obras, incluyendo a nosotros. Pero el Archivo de Anna es un motor de búsqueda que no distribuye directamente esas obras (al menos no en nuestro sitio web de la clearnet), así que deberíamos estar bien, ¿verdad? No exactamente. En muchas jurisdicciones no solo es ilegal distribuir obras con derechos de autor, sino también enlazar a lugares que lo hacen. Un ejemplo clásico de esto es la ley DMCA de los Estados Unidos."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Ese es el extremo más estricto del espectro. En el otro extremo del espectro, teóricamente podría haber países sin leyes de derechos de autor, pero estos realmente no existen. Prácticamente todos los países tienen alguna forma de ley de derechos de autor en sus libros. La aplicación es otra historia. Hay muchos países con gobiernos que no se preocupan por hacer cumplir la ley de derechos de autor. También hay países entre los dos extremos, que prohíben distribuir obras con derechos de autor, pero no prohíben enlazar a dichas obras."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Otra consideración es a nivel de empresa. Si una empresa opera en una jurisdicción que no se preocupa por los derechos de autor, pero la propia empresa no está dispuesta a asumir ningún riesgo, entonces podrían cerrar su sitio web tan pronto como alguien se queje al respecto."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, una gran consideración son los pagos. Dado que necesitamos permanecer anónimos, no podemos usar métodos de pago tradicionales. Esto nos deja con las criptomonedas, y solo un pequeño subconjunto de empresas las acepta (hay tarjetas de débito virtuales pagadas con criptomonedas, pero a menudo no son aceptadas)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Arquitectura del sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Entonces, digamos que encontraste algunas empresas dispuestas a alojar tu sitio web sin cerrarlo, llamémoslas “proveedores amantes de la libertad” 😄. Rápidamente descubrirás que alojar todo con ellos es bastante caro, por lo que podrías querer encontrar algunos “proveedores económicos” y hacer el alojamiento real allí, a través de los proveedores amantes de la libertad. Si lo haces bien, los proveedores económicos nunca sabrán qué estás alojando y nunca recibirán quejas."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con todos estos proveedores existe el riesgo de que te cierren de todos modos, por lo que también necesitas redundancia. Necesitamos esto en todos los niveles de nuestra pila."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Una empresa algo amante de la libertad que se ha puesto en una posición interesante es Cloudflare. Han <a %(blog_cloudflare)s>argumentado</a> que no son un proveedor de alojamiento, sino una utilidad, como un ISP. Por lo tanto, no están sujetos a la DMCA u otras solicitudes de eliminación, y reenvían cualquier solicitud a tu proveedor de alojamiento real. Han llegado tan lejos como para ir a los tribunales para proteger esta estructura. Por lo tanto, podemos usarlos como otra capa de almacenamiento en caché y protección."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare no acepta pagos anónimos, por lo que solo podemos usar su plan gratuito. Esto significa que no podemos usar sus funciones de balanceo de carga o conmutación por error. Por lo tanto, <a %(annas_archive_l255)s>implementamos esto nosotros mismos</a> a nivel de dominio. Al cargar la página, el navegador verificará si el dominio actual todavía está disponible, y si no, reescribe todas las URL a un dominio diferente. Dado que Cloudflare almacena en caché muchas páginas, esto significa que un usuario puede aterrizar en nuestro dominio principal, incluso si el servidor proxy está caído, y luego en el siguiente clic ser movido a otro dominio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Todavía también tenemos preocupaciones operativas normales con las que lidiar, como monitorear la salud del servidor, registrar errores de backend y frontend, y así sucesivamente. Nuestra arquitectura de conmutación por error permite una mayor robustez en este frente también, por ejemplo, ejecutando un conjunto completamente diferente de servidores en uno de los dominios. Incluso podemos ejecutar versiones anteriores del código y los Datasets en este dominio separado, en caso de que un error crítico en la versión principal pase desapercibido."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "También podemos protegernos contra Cloudflare volviéndose en nuestra contra, eliminándolo de uno de los dominios, como este dominio separado. Diferentes permutaciones de estas ideas son posibles."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Herramientas"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Veamos qué herramientas usamos para lograr todo esto. Esto está evolucionando mucho a medida que nos encontramos con nuevos problemas y encontramos nuevas soluciones."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servidor de aplicaciones: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servidor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestión de servidores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Desarrollo: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Alojamiento estático de Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Hay algunas decisiones sobre las que hemos ido y venido. Una es la comunicación entre servidores: solíamos usar Wireguard para esto, pero descubrimos que ocasionalmente deja de transmitir datos o solo transmite datos en una dirección. Esto sucedió con varias configuraciones diferentes de Wireguard que probamos, como <a %(github_costela_wesher)s>wesher</a> y <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. También intentamos tunelizar puertos a través de SSH, usando autossh y sshuttle, pero nos encontramos con <a %(github_sshuttle)s>problemas allí</a> (aunque todavía no me queda claro si autossh sufre de problemas de TCP sobre TCP o no; simplemente me parece una solución chapucera, pero ¿quizás está bien?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "En su lugar, volvimos a las conexiones directas entre servidores, ocultando que un servidor está funcionando en los proveedores económicos usando filtrado de IP con UFW. Esto tiene la desventaja de que Docker no funciona bien con UFW, a menos que uses <code>network_mode: \"host\"</code>. Todo esto es un poco más propenso a errores, porque expondrás tu servidor a internet con solo una pequeña mala configuración. Quizás deberíamos volver a autossh; cualquier comentario sería muy bienvenido aquí."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "También hemos ido y venido entre Varnish y Nginx. Actualmente nos gusta Varnish, pero tiene sus peculiaridades y bordes ásperos. Lo mismo se aplica a Checkmk: no nos encanta, pero funciona por ahora. Weblate ha estado bien, pero no increíble; a veces temo que perderá mis datos cada vez que intento sincronizarlo con nuestro repositorio git. Flask ha sido bueno en general, pero tiene algunas peculiaridades extrañas que han costado mucho tiempo de depuración, como configurar dominios personalizados o problemas con su integración de SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Hasta ahora, las otras herramientas han sido geniales: no tenemos quejas serias sobre MariaDB, ElasticSearch, Gitlab, Zulip, Docker y Tor. Todos estos han tenido algunos problemas, pero nada demasiado serio o que consuma mucho tiempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusión"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Ha sido una experiencia interesante aprender a configurar un motor de búsqueda de biblioteca fantasma robusto y resiliente. Hay muchos más detalles para compartir en publicaciones futuras, ¡así que házmelo saber si te gustaría aprender más sobre algo en particular!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como siempre, estamos buscando donaciones para apoyar este trabajo, así que asegúrate de visitar la página de Donaciones en el Archivo de Anna. También estamos buscando otros tipos de apoyo, como subvenciones, patrocinadores a largo plazo, proveedores de pagos de alto riesgo, quizás incluso anuncios (¡de buen gusto!). Y si deseas contribuir con tu tiempo y habilidades, siempre estamos buscando desarrolladores, traductores, etc. Gracias por tu interés y apoyo."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna y el equipo (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hola, soy Anna. Creé <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, la biblioteca fantasma más grande del mundo. Este es mi blog personal, en el que mis compañeros de equipo y yo escribimos sobre piratería, preservación digital y más."

#, fuzzy
msgid "blog.index.text2"
msgstr "Conéctate conmigo en <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Ten en cuenta que este sitio web es solo un blog. Solo alojamos nuestras propias palabras aquí. No se alojan ni se enlazan torrents u otros archivos con derechos de autor aquí."

#, fuzzy
msgid "blog.index.heading"
msgstr "Entradas del blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B extracción de WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Poniendo 5,998,794 libros en IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Advertencia: esta publicación de blog ha sido descontinuada. Hemos decidido que IPFS aún no está listo para el horario estelar. Aún enlazaremos a archivos en IPFS desde el Archivo de Anna cuando sea posible, pero ya no lo alojaremos nosotros mismos, ni recomendamos a otros que lo espejen usando IPFS. Por favor, consulta nuestra página de Torrents si deseas ayudar a preservar nuestra colección."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Resumen:</strong> El Archivo de Anna extrajo todo WorldCat (la colección de metadata de bibliotecas más grande del mundo) para hacer una lista de tareas pendientes de libros que necesitan ser preservados.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Hace un año, <a %(blog)s>nos propusimos</a> responder a esta pregunta: <strong>¿Qué porcentaje de libros han sido preservados permanentemente por bibliotecas en la sombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Una vez que un libro entra en una biblioteca fantasma de datos abiertos como <a %(wikipedia_library_genesis)s>Library Genesis</a>, y ahora <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, se replica por todo el mundo (a través de torrents), preservándolo prácticamente para siempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Para responder a la pregunta de qué porcentaje de libros se ha preservado, necesitamos conocer el denominador: ¿cuántos libros existen en total? Y, idealmente, no solo tener un número, sino metadatos reales. Entonces no solo podemos compararlos con bibliotecas en la sombra, sino también <strong>crear una lista de tareas pendientes de los libros restantes por preservar!</strong> Incluso podríamos empezar a soñar con un esfuerzo colaborativo para completar esta lista de tareas pendientes."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Raspamos <a %(wikipedia_isbndb_com)s>ISBNdb</a> y descargamos el <a %(openlibrary)s>conjunto de datos de Open Library</a>, pero los resultados fueron insatisfactorios. El principal problema fue que no había mucha superposición de ISBNs. Vea este diagrama de Venn de <a %(blog)s>nuestra publicación en el blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Nos sorprendió mucho lo poco que se superponían ISBNdb y Open Library, ambos de los cuales incluyen datos de diversas fuentes, como raspados web y registros de bibliotecas. Si ambos hacen un buen trabajo al encontrar la mayoría de los ISBNs, sus círculos seguramente tendrían una superposición sustancial, o uno sería un subconjunto del otro. Nos hizo preguntarnos, ¿cuántos libros quedan <em>completamente fuera de estos círculos</em>? Necesitamos una base de datos más grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Fue entonces cuando fijamos nuestra mirada en la base de datos de libros más grande del mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Esta es una base de datos propietaria de la organización sin fines de lucro <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliotecas de todo el mundo, a cambio de dar a esas bibliotecas acceso al conjunto completo de datos y hacer que aparezcan en los resultados de búsqueda de los usuarios finales."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Aunque OCLC es una organización sin fines de lucro, su modelo de negocio requiere proteger su base de datos. Bueno, lamentamos decirlo, amigos de OCLC, lo estamos regalando todo. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Durante el último año, hemos raspado meticulosamente todos los registros de WorldCat. Al principio, tuvimos un golpe de suerte. WorldCat estaba lanzando su rediseño completo del sitio web (en agosto de 2022). Esto incluyó una revisión sustancial de sus sistemas de backend, introduciendo muchas fallas de seguridad. Inmediatamente aprovechamos la oportunidad y pudimos raspar cientos de millones (!) de registros en pocos días."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Rediseño de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Después de eso, las fallas de seguridad se fueron corrigiendo una por una, hasta que la última que encontramos fue parcheada hace aproximadamente un mes. Para ese momento ya teníamos prácticamente todos los registros, y solo buscábamos registros de calidad ligeramente superior. Así que sentimos que es hora de lanzar!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Veamos alguna información básica sobre los datos:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>¿Formato?</strong> <a %(blog)s>Contenedores de Archivo de Anna (AAC)</a>, que es esencialmente <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>, además de algunas semánticas estandarizadas. Estos contenedores envuelven varios tipos de registros, basados en los diferentes raspados que implementamos."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Datos"

msgid "dyn.buy_membership.error.unknown"
msgstr "Un error desconocido ocurrió. Por favor contáctanos en %(email)s con una captura de pantalla."

msgid "dyn.buy_membership.error.minimum"
msgstr "Esta moneda tiene un mínimo más alto de lo habitual. Selecciona una duración o una moneda diferente."

msgid "dyn.buy_membership.error.try_again"
msgstr "No se pudo completar tu petición. Vuelve a intentarlo en unos minutos, si sigue sucediendo contáctanos a %(email)s con una captura de pantalla."

msgid "dyn.buy_membership.error.wait"
msgstr "Error en el procesamiento del pago. Por favor espera un momento y vuelve a intentarlo. Si el problema continúa por más de 24 horas, por favor contáctanos a %(email)s con una captura de pantalla."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "comentario oculto"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema de archivo: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versión mejorada"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "¿Desea reportar a este usuario por comportamiento abusivo o inapropiado?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Reportar abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso reportado:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Usted reportó a este usuario por abuso."

msgid "page.comments.reply_button"
msgstr "Responder"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Por favor, <a %(a_login)s>inicia sesión</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Dejó un comentario. Puede tardar un minuto en aparecer."

msgid "page.md5.quality.comment_error"
msgstr "Algo salió mal. Por favor, recargue la página e intente de nuevo."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s páginas afectadas"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "No visible en Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "No visible en Libgen.rs Ficción"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "No visible en Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcado roto en Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Falta en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcado como “spam” en Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcado como “archivo malo” en Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "No todas las páginas pueden ser convertidas a PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "La ejecución de exiftool falló en este archivo"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (desconocido)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libro (no ficción)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libro (ficción)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artículo periodístico"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documentos estándar"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Cómic"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musical"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibro"

msgid "common.md5_content_type_mapping.other"
msgstr "Otro"

msgid "common.access_types_mapping.aa_download"
msgstr "Descarga del servidor asociado"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Descarga externa"

msgid "common.access_types_mapping.external_borrow"
msgstr "Préstamo externo"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Préstamo externo (impresión deshabilitada)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Explorar metadatos"

msgid "common.access_types_mapping.torrents_available"
msgstr "Contenido dentro de torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chino"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Cargas a AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadatos checos"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Libros"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Estatal Rusa"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Título"

msgid "common.specific_search_fields.author"
msgstr "Autor"

msgid "common.specific_search_fields.publisher"
msgstr "Editor"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edición"

msgid "common.specific_search_fields.year"
msgstr "Año publicado"

msgid "common.specific_search_fields.original_filename"
msgstr "Nombre de archivo original"

msgid "common.specific_search_fields.description_comments"
msgstr "Descripción y comentarios de metadatos"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Las descargas del Servidor Asociado están temporalmente deshabilitadas para este archivo."

msgid "common.md5.servers.fast_partner"
msgstr "Servidor Asociado Rápido #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recomendado)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(sin verificación del navegador ni listas de espera)"

msgid "common.md5.servers.slow_partner"
msgstr "Servidor Asociado Lento #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(ligeramente más rápido pero con lista de espera)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(sin lista de espera, pero puede ser muy lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs No Ficción"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Ficción"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(haz clic también en “GET” en la parte superior)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(haz clic en “GET” en la parte superior)"

msgid "page.md5.box.download.libgen_ads"
msgstr "sus anuncios son conocidos por contener software malicioso, así que use un bloqueador de anuncios o no haga clic en los anuncios"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Los archivos Nexus/STC pueden ser poco fiables para descargar)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "TOR Z-Library"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requiere el navegador TOR)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Tomar prestado de Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(sólo para usuarios con impresión deshabilitada)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(el DOI asociado podría no estar disponible en Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "colección"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Descarga masiva mediante torrent"

msgid "page.md5.box.download.experts_only"
msgstr "(sólo para expertos)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Buscar el ISBN en el Archivo de Anna"

msgid "page.md5.box.download.other_isbn"
msgstr "Buscar el ISBN en otras bases de datos"

msgid "page.md5.box.download.original_isbndb"
msgstr "Buscar el registro original en ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Buscar el ID de Open Library en el archivo de Anna"

msgid "page.md5.box.download.original_openlib"
msgstr "Buscar el registro original en Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Buscar en el Archivo de Anna el número de OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Buscar el registro original en WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Buscar en el Archivo de Anna por número DuXiu SSID"

msgid "page.md5.box.download.original_duxiu"
msgstr "Buscar manualmente en DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Buscar en el Archivo de Anna por número CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Buscar el registro original en CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Buscar en el Archivo de Anna por número DuXiu DXID"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Índice de eBooks de EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Archivo de Anna 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(no se requiere verificación del navegador)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadatos checos %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metadatos"

msgid "page.md5.box.descr_title"
msgstr "descripción"

msgid "page.md5.box.alternative_filename"
msgstr "Nombre de archivo alternativo"

msgid "page.md5.box.alternative_title"
msgstr "Título alternativo"

msgid "page.md5.box.alternative_author"
msgstr "Autor alternativo"

msgid "page.md5.box.alternative_publisher"
msgstr "Editorial alternativa"

msgid "page.md5.box.alternative_edition"
msgstr "Edición alternativa"

msgid "page.md5.box.alternative_extension"
msgstr "Extensión alternativa"

msgid "page.md5.box.metadata_comments_title"
msgstr "comentarios de metadatos"

msgid "page.md5.box.alternative_description"
msgstr "Descripción alternativa"

msgid "page.md5.box.date_open_sourced_title"
msgstr "fecha de lanzamiento en Anna's Archive"

msgid "page.md5.header.scihub"
msgstr "Archivo Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Archivo de préstamos digitales controlados por Internet Archive “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Este es un registro de un archivo de Internet Archive, no un archivo directamente descargable. Puedes intentar pedir prestado el libro (enlace a continuación) o utilizar esta URL cuando <a %(a_request)s>solicites un archivo</a>."

msgid "page.md5.header.consider_upload"
msgstr "Si tiene este archivo y aún no está disponible en el Archivo de Anna, considera <a %(a_request)s>subirlo aquí</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Registro de metadatos de %(id)s en ISBNdb"

msgid "page.md5.header.meta_openlib"
msgstr "Registro de metadatos de %(id)s en Open Library"

msgid "page.md5.header.meta_oclc"
msgstr "Registro de metadatos del número %(id)s en OCLC (WorldCat)"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Registro de metadatos DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Registro de metadatos CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Registro de metadatos de MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Registro de metadatos de Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Esto es un registro de metadatos, no un archivo descargable. Puedes utilizar esta URL cuando <a %(a_request)s>solicites un archivo</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadatos del registro vinculado"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Mejorar metadatos en Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Advertencia: múltiples registros vinculados:"

msgid "page.md5.header.improve_metadata"
msgstr "Mejorar metadatos"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Reportar calidad del archivo"

msgid "page.search.results.download_time"
msgstr "Tiempo de descarga"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Página web:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Buscar \"%(name)s\" en el Archivo de Anna"

msgid "page.md5.codes.code_explorer"
msgstr "Explorador de Códigos:"

msgid "page.md5.codes.code_search"
msgstr "Ver en el Explorador de Códigos “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Leer más…"

msgid "page.md5.tabs.downloads"
msgstr "Descargas (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Prestamos (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Explorar metadatos (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comentarios (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Listas (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Estadísticas (%(count)s)"

msgid "common.tech_details"
msgstr "Detalles técnicos"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌Este archivo puede tener problemas y se ha ocultado de una biblioteca de origen.</span> A veces es a petición del titular de los derechos de autor, otras porque existe una alternativa mejor, pero a veces se debe a un problema con el propio archivo. Es posible que aún pueda descargarse, pero recomendamos que antes busques un archivo alternativo. Más detalles:"

msgid "page.md5.box.download.better_file"
msgstr "Es posible que haya una versión mejor de este archivo disponible en %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Si aún quieres descargar este archivo, asegúrate de sólo usar software confiable y actualizado para abrirlo."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Descargas rápidas"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Conviértase en <a %(a_membership)s>miembro</a> para apoyar la preservación a largo plazo de libros, artículos y más. Para mostrar nuestro agradecimiento por su apoyo obtendrá descargas rápidas. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si donas este mes, obtienes <strong>el doble</strong> de descargas rápidas."

msgid "page.md5.box.download.header_fast_member"
msgstr "Tienes %(remaining)s descargas restantes hoy. ¡Gracias por ser miembro! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Te has quedado sin descargas rápidas por hoy."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Has descargado este archivo recientemente. Los enlaces mantendrán su validez durante un tiempo."

msgid "page.md5.box.download.option"
msgstr "Opción #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(sin redirección)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(abrir en visor)"

msgid "layout.index.header.banner.refer"
msgstr "Refiere a un amigo, ¡y ambos obtenéis %(percentage)s%% descargas rápidas adicionales!"

msgid "layout.index.header.learn_more"
msgstr "Saber más…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Descargas lentas"

msgid "page.md5.box.download.trusted_partners"
msgstr "De socios fiables."

msgid "page.md5.box.download.slow_faq"
msgstr "Más información en las <a %(a_slow)s>Preguntas Frecuentes</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(podría requerir <a %(a_browser)s>verificación del navegador</a> - ¡descargas ilimitadas!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Después de descargar:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Abrir en nuestro visor"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostrar descargas externas"

msgid "page.md5.box.download.header_external"
msgstr "Descargas externas"

msgid "page.md5.box.download.no_found"
msgstr "No se encontró ninguna descarga."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Todas las opciones de descarga tienen el mismo archivo, y deberían ser seguros de usar. Dicho esto, ten siempre cuidado al descargar archivos de Internet, especialmente desde sitios externos al Archivo de Anna. Por ejemplo, asegúrate de mantener tus dispositivos actualizados."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Para archivos grandes, recomendamos usar un gestor de descargas para evitar interrupciones."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Gestores de descargas recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Necesitará un lector de ebooks o PDF para abrir el archivo, dependiendo del formato del archivo."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Lectores de ebooks recomendados: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visor en línea de Archivo de Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Utilice herramientas en línea para convertir entre formatos."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Herramientas de conversión recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Puede enviar archivos PDF y EPUB a su Kindle o Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Herramientas recomendadas: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Enviar a Kindle” de Amazon"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Enviar a Kobo/Kindle” de djazz"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Apoya a los autores y bibliotecas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si te gusta esto y puedes permitírtelo, considera comprar el original o apoyar directamente a los autores."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si está disponible en tu biblioteca local, considera pedirlo prestado gratis allí."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Calidad del archivo"

msgid "page.md5.quality.report"
msgstr "¡Ayuda a la comunidad puntuando la calidad de este archivo! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Reportar problema del archivo (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Gran calidad del archivo (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Agregar comentario (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "¿Qué está mal con este archivo?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Por favor, usa el <a %(a_copyright)s>formulario de reclamación de DMCA / Derechos de Autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Describe el problema (requerido)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descripción del problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 de una mejor versión de este archivo (si aplica)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Rellena esto si hay otro archivo que coincida estrechamente con este archivo (misma edición, misma extensión de archivo si puedes encontrar uno), que la gente debería usar en lugar de este archivo. Si conoces una mejor versión de este archivo fuera de Anna’s Archive, entonces por favor <a %(a_upload)s>súbela</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Puedes obtener el md5 de la URL, por ejemplo."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Enviar reporte"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Aprenda cómo <a %(a_metadata)s>mejorar los metadatos</a> de este archivo usted mismo."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Gracias por enviar su informe. Se mostrará en esta página y será revisado manualmente por Anna (hasta que tengamos un sistema de moderación adecuado)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Algo salió mal. Por favor, recargue la página e intente de nuevo."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si este archivo tiene gran calidad, ¡puede discutir cualquier cosa sobre él aquí! Si no, por favor use el botón “Reportar problema con el archivo”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "¡Me encantó este libro!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Dejar comentario"

msgid "common.english_only"
msgstr "El texto siguiente continúa en inglés."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total de descargas: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “MD5 del archivo” es un hash que se calcula a partir del contenido del archivo y es razonablemente único basado en ese contenido. Todas las bibliotecas en la sombra que hemos indexado aquí utilizan principalmente MD5s para identificar archivos."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un archivo puede aparecer en múltiples bibliotecas en la sombra. Para información sobre los diversos Datasets que hemos compilado, vea la <a %(a_datasets)s>página de Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Este es un archivo gestionado por la biblioteca de <a %(a_ia)s>Préstamo Digital Controlado de IA</a> e indexado por el Archivo de Anna para búsqueda. Para información sobre los diversos Datasets que hemos compilado, vea la <a %(a_datasets)s>página de Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Para información sobre este archivo en particular, consulte su <a %(a_href)s>archivo JSON</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Problema al cargar esta página"

msgid "page.aarecord_issue.text"
msgstr "Por favor, actualice para intentarlo de nuevo. <a %(a_contact)s>Contáctenos</a> si el problema persiste durante varias horas."

msgid "page.md5.invalid.header"
msgstr "No encontrado"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” no se encuentra en nuestra base de datos."

msgid "page.login.title"
msgstr "Iniciar sesión / Registrarse"

msgid "page.browserverification.header"
msgstr "Verificación del navegador"

msgid "page.login.text1"
msgstr "Para evitar que los bots creen múltiples cuentas, necesitamos verificar tu navegador."

msgid "page.login.text2"
msgstr "Si te quedas atrapado en un bucle infinito, te recomendamos instalar <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "También puede ayudar desactivar los bloqueadores de anuncios y otras extensiones del navegador."

msgid "page.codes.title"
msgstr "Códigos"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorador de Códigos"

msgid "page.codes.intro"
msgstr "Explore los códigos con los que se etiquetan los registros, por prefijo. La columna <q>records</q> muestra el número de registros etiquetados con códigos con el prefijo dado, tal como se ve en el motor de búsqueda (incluyendo registros solo de metadatos). La columna <q>codes</q> muestra cuántos códigos reales tienen un prefijo dado."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Esta página puede tardar un tiempo en generarse, por lo que requiere un captcha de Cloudflare. <a %(a_donate)s>Los miembros</a> pueden omitir el captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Por favor, no raspe estas páginas. En su lugar, recomendamos <a %(a_import)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos ElasticSearch y MariaDB, y ejecutar nuestro <a %(a_software)s>código abierto</a>. Los datos en bruto se pueden explorar manualmente a través de archivos JSON como <a %(a_json_file)s>este</a>."

msgid "page.codes.prefix"
msgstr "Prefijo"

msgid "common.form.go"
msgstr "Ir"

msgid "common.form.reset"
msgstr "Restablecer"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Buscar en el Archivo de Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Advertencia: el código tiene caracteres Unicode incorrectos y puede comportarse incorrectamente en varias situaciones. El binario en bruto se puede decodificar de la representación base64 en la URL."

msgid "page.codes.known_code_prefix"
msgstr "Prefijo de código conocido <q>%(key)s</q>"

msgid "page.codes.code_prefix"
msgstr "Prefijo"

msgid "page.codes.code_label"
msgstr "Etiqueta"

msgid "page.codes.code_description"
msgstr "Descripción"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL para un código específico"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” será sustituido por el valor del código"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL genérica"

msgid "page.codes.code_website"
msgstr "Página web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s registro que coincide con “%(prefix_label)s”"
msgstr[1] "%(count)s registros que coinciden con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL para código específico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Más…"

msgid "page.codes.codes_starting_with"
msgstr "Códigos que comienzan con <q>%(prefix_label)s</q>"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Índice de"

msgid "page.codes.records_prefix"
msgstr "registros"

msgid "page.codes.records_codes"
msgstr "códigos"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s registros"

msgid "page.contact.dmca.form"
msgstr "Para reclamos de DMCA o derechos de autor, utilice <a %(a_copyright)s>este formulario</a>."

msgid "page.contact.dmca.delete"
msgstr "Cualquier otra forma de contactarnos sobre reclamos de derechos de autor se eliminará automáticamente."

msgid "page.contact.checkboxes.text1"
msgstr "¡Agradecemos mucho sus comentarios y preguntas!"

msgid "page.contact.checkboxes.text2"
msgstr "Sin embargo, debido a la cantidad de correos electrónicos no deseados y sin sentido que recibimos, marque las casillas para confirmar que comprende estas condiciones para contactarnos."

msgid "page.contact.checkboxes.copyright"
msgstr "Se ignorarán los reclamos de derechos de autor a este correo electrónico; utilicé el formulario en su lugar."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Los servidores asociados no están disponibles debido al cierre de los servicios de alojamiento. Deberían estar operativos nuevamente pronto."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Las membresías se extenderán en consecuencia."

msgid "layout.index.footer.dont_email"
msgstr "No nos envíe correos electrónicos para <a %(a_request)s>solicitar libros</a> o <a %(a_upload)s>cargas</a> pequeñas (<10k)."

msgid "page.donate.please_include"
msgstr "Al hacer preguntas sobre la cuenta o donaciones, incluya su ID de cuenta, capturas de pantalla, recibos y toda la información posible. Solo revisamos nuestro correo electrónico cada 1-2 semanas, por lo que no incluir esta información retrasará cualquier resolución."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostrar correo electrónico"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulario de reclamación de derechos de autor / DMCA"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si tiene una reclamación de derechos de autor o DMCA, por favor complete este formulario con la mayor precisión posible. Si encuentra algún problema, contáctenos en nuestra dirección dedicada a DMCA: %(email)s. Tenga en cuenta que las reclamaciones enviadas por correo electrónico a esta dirección no serán procesadas, solo es para preguntas. Por favor, use el formulario a continuación para enviar sus reclamaciones."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs en el Archivo de Anna (requerido). Una por línea. Por favor, solo incluya URLs que describan exactamente la misma edición de un libro. Si desea hacer una reclamación para múltiples libros o múltiples ediciones, por favor envíe este formulario varias veces."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Las reclamaciones que agrupen múltiples libros o ediciones serán rechazadas."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Su nombre (requerido)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Dirección (requerido)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Número de teléfono (requerido)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Correo electrónico (requerido)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descripción clara del material fuente (requerido)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs del material fuente (si aplica). Uno por línea. Por favor, solo incluya aquellos que coincidan exactamente con la edición para la cual está reportando una reclamación de derechos de autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs del material fuente, una por línea. Por favor, tómese un momento para buscar su material fuente en Open Library. Esto nos ayudará a verificar su reclamación."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs del material fuente, una por línea (requerido). Por favor, incluya tantas como sea posible para ayudarnos a verificar su reclamación (por ejemplo, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaración y firma (requerido)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Enviar reclamación"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Gracias por enviar su reclamación de derechos de autor. La revisaremos lo antes posible. Por favor, recargue la página para enviar otra."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Algo salió mal. Por favor, recargue la página e intente nuevamente."

msgid "page.datasets.title"
msgstr "Conjunto de datos"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si está interesado en reflejar este conjunto de datos para <a %(a_archival)s>archivos</a> o para <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Nuestra misión es archivar todos los libros del mundo (así como artículos, revistas, etc.), y hacerlos ampliamente accesibles. Creemos que todos los libros deben ser replicados ampliamente, para asegurar redundancia y resiliencia. Por eso estamos reuniendo archivos de una variedad de fuentes. Algunas fuentes son completamente abiertas y pueden ser replicadas en masa (como Sci-Hub). Otras son cerradas y protectoras, por lo que intentamos extraerlas para “liberar” sus libros. Otras se encuentran en algún punto intermedio."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Todos nuestros datos pueden ser <a %(a_torrents)s>torrentados</a>, y todos nuestros metadatos pueden ser <a %(a_anna_software)s>generados</a> o <a %(a_elasticsearch)s>descargados</a> como bases de datos ElasticSearch y MariaDB. Los datos en bruto pueden ser explorados manualmente a través de archivos JSON como <a %(a_dbrecord)s>este</a>."

msgid "page.datasets.overview.title"
msgstr "Descripción general"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "A continuación se muestra un resumen rápido de las fuentes de los archivos en el Archivo de Anna."

msgid "page.datasets.overview.source.header"
msgstr "Fuente"

msgid "page.datasets.overview.size.header"
msgstr "Tamaño"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% reflejado por AA / torrents disponibles"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Porcentajes del número de archivos"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Última actualización"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "No Ficción y Ficción"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s archivo"
msgstr[1] "%(count)s archivos"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "A través de Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelado desde 2021; la mayoría disponible a través de torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: adiciones menores desde entonces</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluyendo “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Los torrents de ficción están atrasados (aunque los IDs ~4-6M no están en torrents ya que se superponen con nuestros torrents de Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "La colección “China” en Z-Library parece ser la misma que nuestra colección de DuXiu, pero con diferentes MD5s. Excluimos estos archivos de los torrents para evitar duplicaciones, pero aún los mostramos en nuestro índice de búsqueda."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Préstamo Digital Controlado de IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ de archivos son buscables."

msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluyendo duplicados"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Dado que las bibliotecas en la sombra a menudo sincronizan datos entre sí, hay una considerable superposición entre las bibliotecas. Por eso los números no suman el total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "El porcentaje de “espejado y sembrado por el Archivo de Anna” muestra cuántos archivos espejamos nosotros mismos. Sembramos esos archivos en masa a través de torrents y los hacemos disponibles para descarga directa a través de sitios web asociados."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliotecas fuente"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Algunas bibliotecas fuente promueven el intercambio masivo de sus datos a través de torrents, mientras que otras no comparten fácilmente su colección. En este último caso, el Archivo de Anna intenta extraer sus colecciones y hacerlas disponibles (vea nuestra página de <a %(a_torrents)s>Torrents</a>). También hay situaciones intermedias, por ejemplo, donde las bibliotecas fuente están dispuestas a compartir, pero no tienen los recursos para hacerlo. En esos casos, también tratamos de ayudar."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "A continuación se presenta una visión general de cómo interactuamos con las diferentes bibliotecas fuente."

msgid "page.datasets.sources.source.header"
msgstr "Fuente"

msgid "page.datasets.sources.files.header"
msgstr "Archivos"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Volcados de base de datos HTTP</a> diarios"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automatizados para <a %(nonfiction)s>No Ficción</a> y <a %(fiction)s>Ficción</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(covers)s>torrents de portadas de libros</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub ha congelado nuevos archivos desde 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Volcados de metadatos disponibles <a %(scihub1)s>aquí</a> y <a %(scihub2)s>aquí</a>, así como parte de la <a %(libgenli)s>base de datos de Libgen.li</a> (que usamos)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrents de datos disponibles <a %(scihub1)s>aquí</a>, <a %(scihub2)s>aquí</a> y <a %(libgenli)s>aquí</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Algunos archivos nuevos están <a %(libgenrs)s>siendo</a> <a %(libgenli)s>agregados</a> a “scimag” de Libgen, pero no lo suficiente como para justificar nuevos torrents"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Volcados de base de datos HTTP</a> trimestrales"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Los torrents de No Ficción se comparten con Libgen.rs (y se reflejan <a %(libgenli)s>aquí</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s El Archivo de Anna y Libgen.li gestionan colaborativamente colecciones de <a %(comics)s>cómics</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos estándar</a> y <a %(fiction)s>ficción (divergente de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Su colección “fiction_rus” (ficción rusa) no tiene torrents dedicados, pero está cubierta por torrents de otros, y mantenemos un <a %(fiction_rus)s>espejo</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s El Archivo de Anna y Z-Library gestionan colaborativamente una colección de <a %(metadata)s>metadatos de Z-Library</a> y <a %(files)s>archivos de Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Algunos metadatos disponibles a través de <a %(openlib)s>volcados de base de datos de Open Library</a>, pero esos no cubren toda la colección de IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s No hay volcado de metadatos fácilmente accesibles disponibles para toda su colección"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(ia)s>metadatos de IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Archivos solo disponibles para préstamo de manera limitada, con varias restricciones de acceso"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(ia)s>archivos IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Varias bases de datos de metadatos dispersas por el internet chino; aunque a menudo son bases de datos de pago"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s No hay volcados de metadatos fácilmente accesibles disponibles para toda su colección."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(duxiu)s>metadatos de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Varias bases de datos de archivos dispersas por el internet chino; aunque a menudo son bases de datos de pago"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La mayoría de los archivos solo son accesibles usando cuentas premium de BaiduYun; velocidades de descarga lentas."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(duxiu)s>archivos de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Varias fuentes más pequeñas o únicas. Animamos a las personas a subir primero a otras bibliotecas en la sombra, pero a veces las personas tienen colecciones que son demasiado grandes para que otros las clasifiquen, aunque no lo suficientemente grandes como para justificar su propia categoría."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fuentes solo de metadatos"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "También enriquecemos nuestra colección con fuentes solo de metadatos, que podemos emparejar con archivos, por ejemplo, usando números ISBN u otros campos. A continuación se presenta una visión general de esas fuentes. Nuevamente, algunas de estas fuentes son completamente abiertas, mientras que para otras tenemos que extraer los datos."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Nuestra inspiración para recopilar metadatos es el objetivo de Aaron Swartz de “una página web para cada libro jamás publicado”, para lo cual creó <a %(a_openlib)s>Open Library</a>. Ese proyecto ha tenido éxito, pero nuestra posición única nos permite obtener metadatos que ellos no pueden. Otra inspiración fue nuestro deseo de saber <a %(a_blog)s>cuántos libros hay en el mundo</a>, para poder calcular cuántos libros nos quedan por salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Tenga en cuenta que en la búsqueda de metadatos, mostramos los registros originales. No hacemos ninguna fusión de registros."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Última actualización"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Volcados de base de datos</a> mensuales"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s No disponible directamente en masa, protegido contra scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(worldcat)s>metadatos de OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Base de datos unificada"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Combinamos todas las fuentes anteriores en una base de datos unificada que utilizamos para servir este sitio web. Esta base de datos unificada no está disponible directamente, pero dado que el Archivo de Anna es completamente de código abierto, se puede <a %(a_generated)s>generar</a> o <a %(a_downloaded)s>descargar</a> bastante fácilmente como bases de datos ElasticSearch y MariaDB. Los scripts en esa página descargarán automáticamente todos los metadatos necesarios de las fuentes mencionadas anteriormente."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si desea explorar nuestros datos antes de ejecutar esos scripts localmente, puede consultar nuestros archivos JSON, que enlazan a otros archivos JSON. <a %(a_json)s>Este archivo</a> es un buen punto de partida."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptado de nuestra <a %(a_href)s>entrada de blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> es una enorme base de datos de libros escaneados, creada por el <a %(superstar_link)s>SuperStar Digital Library Group</a>. La mayoría son libros académicos, escaneados para ponerlos a disposición digitalmente de universidades y bibliotecas. Para nuestra audiencia de habla inglesa, <a %(princeton_link)s>Princeton</a> y la <a %(uw_link)s>Universidad de Washington</a> tienen buenos resúmenes. También hay un excelente artículo que ofrece más información: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Los libros de Duxiu han sido pirateados durante mucho tiempo en internet chino. Usualmente son vendidos por menos de un dólar por revendedores. Normalmente se distribuyen usando el equivalente chino de Google Drive, que a menudo ha sido hackeado para permitir más espacio de almacenamiento. Algunos detalles técnicos se pueden encontrar <a %(link1)s>aquí</a> y <a %(link2)s>aquí</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Aunque los libros han sido distribuidos semi-públicamente, es bastante difícil obtenerlos en grandes cantidades. Teníamos esto en lo alto de nuestra lista de tareas pendientes, y asignamos varios meses de trabajo a tiempo completo para ello. Sin embargo, a finales de 2023, un voluntario increíble, asombroso y talentoso se puso en contacto con nosotros, diciéndonos que ya había hecho todo este trabajo, a un gran costo. Compartieron la colección completa con nosotros, sin esperar nada a cambio, excepto la garantía de preservación a largo plazo. Verdaderamente notable."

msgid "page.datasets.common.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total de archivos: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Tamaño total de archivos: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Archivos reflejados por Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Última actualización: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents por Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Ejemplo de registro en Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Nuestra entrada de blog sobre estos datos"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts para importar metadatos"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formato de Contenedores de Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Más información de nuestros voluntarios (notas sin procesar):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "Préstamo Digital Controlado de IA"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Este conjunto de datos está estrechamente relacionado con el <a %(a_datasets_openlib)s>conjunto de datos de Open Library</a>. Contiene una recopilación de todos los metadatos y una gran parte de los archivos de la Biblioteca de Préstamo Digital Controlado de IA. Las actualizaciones se publican en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Estos registros se refieren directamente al conjunto de datos de Open Library, pero también contienen registros que no están en Open Library. También tenemos varios archivos de datos recopilados por miembros de la comunidad a lo largo de los años."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La colección consta de dos partes. Necesita ambas partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "nuestra primera versión, antes de estandarizarnos en el <a %(a_aac)s>formato de Contenedores de Anna’s Archive (AAC)</a>. Contiene metadatos (en json y xml), pdfs (de los sistemas de préstamo digital acsm y lcpdf), y miniaturas de portadas."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "nuevas versiones incrementales, usando AAC. Solo contiene metadatos con marcas de tiempo posteriores al 2023-01-01, ya que el resto ya está cubierto por “ia”. También todos los archivos pdf, esta vez de los sistemas de préstamo acsm y “bookreader” (el lector web de IA). A pesar de que el nombre no sea exactamente correcto, aún así incluimos archivos de bookreader en la colección ia2_acsmpdf_files, ya que son mutuamente excluyentes."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sitio web principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca de Préstamo Digital"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentación de metadatos (la mayoría de los campos)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Información del país del ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "La Agencia Internacional del ISBN publica regularmente los rangos que ha asignado a las agencias nacionales del ISBN. A partir de esto, podemos deducir a qué país, región o grupo de idiomas pertenece este ISBN. Actualmente usamos estos datos de manera indirecta, a través de la biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Recursos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Última actualización: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sitio web del ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadatos"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Para la historia de los diferentes forks de Library Genesis, vea la página de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li contiene la mayor parte del mismo contenido y metadatos que Libgen.rs, pero tiene algunas colecciones adicionales, a saber, cómics, revistas y documentos estándar. También ha integrado <a %(a_scihub)s>Sci-Hub</a> en sus metadatos y motor de búsqueda, que es lo que usamos para nuestra base de datos."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Los metadatos de esta biblioteca están disponibles gratuitamente <a %(a_libgen_li)s>en libgen.li</a>. Sin embargo, este servidor es lento y no admite la reanudación de conexiones interrumpidas. Los mismos archivos también están disponibles en <a %(a_ftp)s>un servidor FTP</a>, que funciona mejor."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Los torrents están disponibles para la mayoría del contenido adicional, especialmente los torrents para cómics, revistas y documentos estándar han sido lanzados en colaboración con el Archivo de Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La colección de ficción tiene sus propios torrents (divergente de <a %(a_href)s>Libgen.rs</a>) comenzando en %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Según el administrador de Libgen.li, la colección “fiction_rus” (ficción rusa) debería estar cubierta por torrents lanzados regularmente desde <a %(a_booktracker)s>booktracker.org</a>, especialmente los torrents de <a %(a_flibusta)s>flibusta</a> y <a %(a_librusec)s>lib.rus.ec</a> (que espejamos <a %(a_torrents)s>aquí</a>, aunque aún no hemos establecido qué torrents corresponden a qué archivos)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Las estadísticas de todas las colecciones se pueden encontrar <a %(a_href)s>en el sitio web de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "La no ficción también parece haberse desviado, pero sin nuevos torrentes. Parece que esto ha sucedido desde principios de 2022, aunque no lo hemos verificado."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Ciertos rangos sin torrents (como los rangos de ficción f_3463000 a f_4260000) son probablemente archivos de Z-Library (u otros duplicados), aunque podríamos querer hacer alguna deduplicación y crear torrents para archivos únicos de lgli en estos rangos."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Tenga en cuenta que los archivos torrent que se refieren a “libgen.is” son explícitamente espejos de <a %(a_libgen)s>Libgen.rs</a> (“.is” es un dominio diferente utilizado por Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Un recurso útil para usar los metadatos es <a %(a_href)s>esta página</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrents de ficción en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrents de cómics en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrents de revistas en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos estándar en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de ficción rusa en el Archivo de Anna"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadatos vía FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Información del campo de metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Espejo de otros torrents (y torrents únicos de ficción y cómics)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Foro de discusión"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nuestra publicación en el blog sobre el lanzamiento de los cómics"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "La breve historia de las diferentes bifurcaciones de Library Genesis (o “Libgen”) es que, con el tiempo, las diferentes personas involucradas en Library Genesis tuvieron una disputa y se separaron."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La versión “.fun” fue creada por el fundador original. Está siendo renovada a favor de una nueva versión más distribuida."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La versión “.rs” tiene datos muy similares y publica su colección en torrents masivos de manera constante. Está dividida aproximadamente en una sección de “ficción” y otra de “no ficción”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originalmente en “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>versión “.li”</a> tiene una colección masiva de cómics, así como otros contenidos, que aún no están disponibles para descarga masiva a través de torrents. Tiene una colección de torrents separada de libros de ficción y contiene los metadatos de <a %(a_scihub)s>Sci-Hub</a> en su base de datos."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Según esta <a %(a_mhut)s>publicación en el foro</a>, Libgen.li fue alojado originalmente en “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> en cierto sentido también es una bifurcación de Library Genesis, aunque usaron un nombre diferente para su proyecto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Esta página trata sobre la versión “.rs”. Es conocida por publicar de manera constante tanto sus metadatos como el contenido completo de su catálogo de libros. Su colección de libros está dividida entre una parte de ficción y otra de no ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Un recurso útil para usar los metadatos es <a %(a_metadata)s>esta página</a> (bloquea rangos de IP, puede ser necesario usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de 2024-03, se están publicando nuevos torrents en <a %(a_href)s>este hilo del foro</a> (bloquea rangos de IP, puede ser necesario usar VPN)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de no ficción en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de ficción en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Información de los campos de metadatos de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de no ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de ficción de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Foro de discusión de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents por el Archivo de Anna (portadas de libros)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Nuestro blog sobre el lanzamiento de las portadas de libros"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis es conocido por ya hacer generosamente sus datos disponibles en masa a través de torrents. Nuestra colección de Libgen consiste en datos auxiliares que ellos no liberan directamente, en asociación con ellos. ¡Muchas gracias a todos los involucrados con Library Genesis por trabajar con nosotros!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Lanzamiento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Este <a %(blog_post)s>primer lanzamiento</a> es bastante pequeño: alrededor de 300GB de portadas de libros del fork de Libgen.rs, tanto de ficción como de no ficción. Están organizados de la misma manera en que aparecen en libgen.rs, por ejemplo:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s para un libro de no ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s para un libro de ficción."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Al igual que con la colección de Z-Library, los pusimos todos en un gran archivo .tar, que se puede montar usando <a %(a_ratarmount)s>ratarmount</a> si deseas servir los archivos directamente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> es una base de datos propietaria de la organización sin fines de lucro <a %(a_oclc)s>OCLC</a>, que agrega registros de metadatos de bibliotecas de todo el mundo. Es probable que sea la colección de metadatos de bibliotecas más grande del mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octubre de 2023, lanzamiento inicial:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "En octubre de 2023 <a %(a_scrape)s>lanzamos</a> una recopilación exhaustiva de la base de datos OCLC (WorldCat), en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents por el Archivo de Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nuestra publicación en el blog sobre estos datos"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library es un proyecto de código abierto del Internet Archive para catalogar todos los libros del mundo. Tiene una de las operaciones de escaneo de libros más grandes del mundo y tiene muchos libros disponibles para préstamo digital. Su catálogo de metadatos de libros está disponible gratuitamente para su descarga y está incluido en el Archivo de Anna (aunque actualmente no está en búsqueda, excepto si buscas explícitamente un ID de Open Library)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Lanzamiento 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Este es un volcado de muchas llamadas a isbndb.com durante septiembre de 2022. Intentamos cubrir todos los rangos de ISBN. Estos son alrededor de 30.9 millones de registros. En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que de alguna manera podríamos haber perdido algunos, o <em>ellos</em> podrían estar haciendo algo mal."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Las respuestas JSON son prácticamente crudas desde su servidor. Un problema de calidad de datos que notamos es que para los números ISBN-13 que comienzan con un prefijo diferente a “978-”, aún incluyen un campo “isbn” que simplemente es el número ISBN-13 con los primeros 3 números eliminados (y el dígito de control recalculado). Esto es obviamente incorrecto, pero así es como parecen hacerlo, por lo que no lo alteramos."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Otro problema potencial que podría encontrar es el hecho de que el campo “isbn13” tiene duplicados, por lo que no puede usarlo como clave primaria en una base de datos. Los campos combinados “isbn13”+“isbn” parecen ser únicos."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Para obtener información sobre Sci-Hub, consulte su <a %(a_scihub)s>sitio web oficial</a>, <a %(a_wikipedia)s>página de Wikipedia</a> y esta <a %(a_radiolab)s>entrevista en podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Tenga en cuenta que Sci-Hub ha estado <a %(a_reddit)s>congelado desde 2021</a>. Se congeló antes, pero en 2021 se añadieron unos pocos millones de artículos. Aún así, se añaden algunos artículos limitados a las colecciones “scimag” de Libgen, aunque no lo suficiente como para justificar nuevos torrents masivos."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Usamos los metadatos de Sci-Hub proporcionados por <a %(a_libgen_li)s>Libgen.li</a> en su colección “scimag”. También utilizamos el conjunto de datos <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Tenga en cuenta que los torrents “smarch” están <a %(a_smarch)s>obsoletos</a> y por lo tanto no se incluyen en nuestra lista de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents en el Archivo de Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadatos y torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents en Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents en Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Actualizaciones en Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Página de Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Entrevista en podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Subidas al Archivo de Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Resumen de la <a %(a1)s>página de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Varias fuentes más pequeñas o únicas. Animamos a las personas a subir primero a otras bibliotecas en la sombra, pero a veces las personas tienen colecciones que son demasiado grandes para que otros las clasifiquen, aunque no lo suficientemente grandes como para justificar su propia categoría."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La colección “upload” se divide en subcolecciones más pequeñas, que se indican en los AACIDs y nombres de torrents. Todas las subcolecciones se deduplicaron primero contra la colección principal, aunque los archivos JSON de metadatos “upload_records” todavía contienen muchas referencias a los archivos originales. Los archivos que no son libros también se eliminaron de la mayoría de las subcolecciones y, por lo general, <em>no</em> se anotan en los JSON de “upload_records”."

msgid "page.datasets.upload.subsubcollections"
msgstr "Muchas subcolecciones en sí mismas están compuestas por sub-sub-colecciones (por ejemplo, de diferentes fuentes originales), que se representan como directorios en los <q>filepath</q> campos."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Las subcolecciones son:"

msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcolección"

msgid "page.datasets.upload.subs.notes"
msgstr "Notas"

msgid "page.datasets.upload.action.browse"
msgstr "explorar"

msgid "page.datasets.upload.action.search"
msgstr "buscar"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Parece estar bastante completo. De nuestro voluntario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "De un torrent de <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Tiene una superposición bastante alta con las colecciones de artículos existentes, pero muy pocas coincidencias de MD5, por lo que decidimos mantenerlo completamente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Extracción de <q>iRead eBooks</q> (= fonéticamente <q>ai rit i-books</q>; airitibooks.com), por el voluntario <q>j</q>. Corresponde a la metadata de <q>airitibooks</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "De una colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte de la fuente original, parte de the-eye.eu, parte de otros espejos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "De un sitio web privado de torrents de libros, <a %(a_href)s>Bibliotik</a> (a menudo referido como “Bib”), cuyos libros se agruparon en torrents por nombre (A.torrent, B.torrent) y se distribuyeron a través de the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "De nuestro voluntario “bpb9v”. Para más información sobre <a %(a_href)s>CADAL</a>, vea las notas en nuestra <a %(a_duxiu)s>página del conjunto de datos de DuXiu</a>."

msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Más de nuestro voluntario <q>bpb9v</q>, principalmente archivos de DuXiu, así como una carpeta <q>WenQu</q> y <q>SuperStar_Journals</q> (SuperStar es la empresa detrás de DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "De nuestro voluntario “cgiym”, textos chinos de varias fuentes (representados como subdirectorios), incluyendo de <a %(a_href)s>China Machine Press</a> (un importante editor chino)."

msgid "page.datasets.upload.source.cgiym_more"
msgstr "Colecciones no chinas (representadas como subdirectorios) de nuestro voluntario <q>cgiym</q>."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Extracción de libros sobre arquitectura china, por el voluntario <q>cm</q>: <q>Lo obtuve explotando una vulnerabilidad de red en la editorial, pero esa brecha ya ha sido cerrada</q>. Corresponde a la metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libros de la editorial académica <a %(a_href)s>De Gruyter</a>, recopilados de algunos torrents grandes."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Raspado de <a %(a_href)s>docer.pl</a>, un sitio web polaco de intercambio de archivos enfocado en libros y otras obras escritas. Raspado a finales de 2023 por el voluntario “p”. No tenemos buenos metadatos del sitio web original (ni siquiera extensiones de archivo), pero filtramos archivos con apariencia de libros y a menudo pudimos extraer metadatos de los propios archivos."

msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epubs de DuXiu, directamente de DuXiu, recopilados por el voluntario <q>w</q>. Solo los libros recientes de DuXiu están disponibles directamente a través de ebooks, por lo que la mayoría de estos deben ser recientes."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Archivos restantes de DuXiu del voluntario “m”, que no estaban en el formato propietario PDG de DuXiu (el principal <a %(a_href)s>conjunto de datos de DuXiu</a>). Recopilados de muchas fuentes originales, lamentablemente sin preservar esas fuentes en la ruta del archivo."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Extracción de libros eróticos, por el voluntario <q>do no harm</q>. Corresponde a la metadata de <q>hentai</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

msgid "page.datasets.upload.source.japanese_manga"
msgstr "Colección raspada de un editor japonés de manga por el voluntario <q>t</q>."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivos judiciales seleccionados de Longquan</a>, proporcionados por el voluntario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Raspado de <a %(a_href)s>magzdb.org</a>, un aliado de Library Genesis (está vinculado en la página de inicio de libgen.rs) pero que no quiso proporcionar sus archivos directamente. Obtenido por el voluntario “p” a finales de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

msgid "page.datasets.upload.source.misc"
msgstr "Varias cargas pequeñas, demasiado pequeñas para ser su propia subcolección, pero representadas como directorios. El directorio <q>oo42hcksBxZYAOjqwGWu</q> corresponde a los metadatos <q>czech_oo42hcks</q> en <a %(a1)s><q>Otros metadatos extraídos/q></a>."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks de AvaxHome, un sitio web ruso para compartir archivos."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivo de periódicos y revistas. Corresponde a la metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Otras extracciones de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Extracción del <a %(a1)s>Centro de Documentación de Filosofía</a>."

msgid "page.datasets.upload.source.polish"
msgstr "Colección del voluntario <q>o</q> que recopiló libros polacos directamente de sitios web de lanzamiento original (<q>scene</q>)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Colecciones combinadas de <a %(a_href)s>shuge.org</a> por los voluntarios “cgiym” y “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s><q>Biblioteca Imperial de Trantor</q></a> (nombrada en honor a la biblioteca ficticia), recopilada en 2022 por el voluntario <q>t</q>. Corresponde a los metadatos de <q>trantor</q> en <a %(a1)s><q>Otras recopilaciones de metadatos</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-colecciones (representadas como directorios) del voluntario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (por <a %(a_sikuquanshu)s>Dizhi(迪志)</a> en Taiwán), mebook (mebook.cc, 我的小书屋, mi pequeño cuarto de libros — woz9ts: “Este sitio se centra principalmente en compartir archivos de ebooks de alta calidad, algunos de los cuales son maquetados por el propio dueño. El dueño fue <a %(a_arrested)s>arrestado</a> en 2019 y alguien hizo una colección de los archivos que compartió.”)."

msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Archivos restantes de DuXiu del voluntario <q>woz9ts</q>, que no estaban en el formato propietario PDG de DuXiu (aún por convertir a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents por el Archivo de Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Extracción de Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library tiene sus raíces en la comunidad de <a %(a_href)s>Library Genesis</a>, y originalmente se inició con sus datos. Desde entonces, se ha profesionalizado considerablemente y tiene una interfaz mucho más moderna. Por lo tanto, pueden recibir muchas más donaciones, tanto monetarias para seguir mejorando su sitio web, como donaciones de nuevos libros. Han acumulado una gran colección además de Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Actualización a febrero de 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "A finales de 2022, los presuntos fundadores de Z-Library fueron arrestados y los dominios fueron incautados por las autoridades de Estados Unidos. Desde entonces, el sitio web ha estado volviendo a estar en línea lentamente. Se desconoce quién lo administra actualmente."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "La colección consta de tres partes. Las páginas de descripción originales para las dos primeras partes se conservan a continuación. Necesitas las tres partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nuestro primer lanzamiento. Este fue el primer lanzamiento de lo que entonces se llamaba el “Espejo de la Biblioteca Pirata” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: segundo lanzamiento, esta vez con todos los archivos envueltos en archivos .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: nuevos lanzamientos incrementales, usando el <a %(a_href)s>formato de Contenedores del Archivo de Anna (AAC)</a>, ahora lanzado en colaboración con el equipo de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents por el Archivo de Anna (metadatos + contenido)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Ejemplo de registro en el Archivo de Anna (colección original)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Ejemplo de registro en el Archivo de Anna (colección “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sitio web principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Dominio Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Entrada de blog sobre el Lanzamiento 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Entrada de blog sobre el Lanzamiento 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Lanzamientos de Zlib (páginas de descripción original)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Lanzamiento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "El espejo inicial se obtuvo meticulosamente a lo largo de 2021 y 2022. En este punto está ligeramente desactualizado: refleja el estado de la colección en junio de 2021. Actualizaremos esto en el futuro. Ahora mismo estamos enfocados en lanzar esta primera versión."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Dado que Library Genesis ya está preservado con torrents públicos y está incluido en Z-Library, hicimos una deduplicación básica contra Library Genesis en junio de 2022. Para esto utilizamos hashes MD5. Es probable que haya mucho más contenido duplicado en la biblioteca, como múltiples formatos de archivo con el mismo libro. Esto es difícil de detectar con precisión, por lo que no lo hacemos. Después de la deduplicación, nos quedamos con más de 2 millones de archivos, totalizando poco menos de 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La colección consta de dos partes: un volcado MySQL “.sql.gz” de los metadatos, y los 72 archivos torrent de alrededor de 50-100GB cada uno. Los metadatos contienen los datos según lo reportado por el sitio web de Z-Library (título, autor, descripción, tipo de archivo), así como el tamaño real del archivo y el md5sum que observamos, ya que a veces estos no coinciden. Parece haber rangos de archivos para los cuales Z-Library en sí tiene metadatos incorrectos. También podríamos haber descargado archivos incorrectamente en algunos casos aislados, lo cual intentaremos detectar y corregir en el futuro."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Los grandes archivos torrent contienen los datos reales de los libros, con el ID de Z-Library como nombre de archivo. Las extensiones de archivo pueden reconstruirse utilizando el volcado de metadatos."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La colección es una mezcla de contenido de no ficción y ficción (no separado como en Library Genesis). La calidad también varía ampliamente."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Esta primera versión ya está completamente disponible. Tenga en cuenta que los archivos torrent solo están disponibles a través de nuestro espejo Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Lanzamiento 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Hemos obtenido todos los libros que se añadieron a Z-Library entre nuestro último espejo y agosto de 2022. También hemos vuelto y recopilado algunos libros que nos perdimos la primera vez. En total, esta nueva colección es de aproximadamente 24TB. Nuevamente, esta colección está deduplicada contra Library Genesis, ya que ya hay torrents disponibles para esa colección."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Los datos están organizados de manera similar al primer lanzamiento. Hay un volcado MySQL “.sql.gz” de los metadatos, que también incluye todos los metadatos del primer lanzamiento, reemplazándolo así. También añadimos algunas nuevas columnas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: si este archivo ya está en Library Genesis, ya sea en la colección de no ficción o de ficción (coincidencia por md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: en qué torrent se encuentra este archivo."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: establecido cuando no pudimos descargar el libro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Mencionamos esto la última vez, pero solo para aclarar: “nombre de archivo” y “md5” son las propiedades reales del archivo, mientras que “nombre de archivo_reportado” y “md5_reportado” son lo que recopilamos de Z-Library. A veces estos dos no coinciden entre sí, por lo que incluimos ambos."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Para este lanzamiento, cambiamos la intercalación a “utf8mb4_unicode_ci”, que debería ser compatible con versiones anteriores de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Los archivos de datos son similares a la última vez, aunque son mucho más grandes. Simplemente no nos molestamos en crear toneladas de archivos torrent más pequeños. “pilimi-zlib2-0-14679999-extra.torrent” contiene todos los archivos que nos perdimos en el último lanzamiento, mientras que los otros torrents son todos nuevos rangos de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Actualización %(date)s:</strong> Hicimos la mayoría de nuestros torrents demasiado grandes, lo que causó problemas a los clientes de torrent. Los hemos eliminado y lanzado nuevos torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Actualización %(date)s:</strong> Todavía había demasiados archivos, así que los empaquetamos en archivos tar y lanzamos nuevos torrents nuevamente."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addendum del Lanzamiento 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Este es un único archivo torrent adicional. No contiene ninguna información nueva, pero tiene algunos datos que pueden tardar un tiempo en calcularse. Eso lo hace conveniente de tener, ya que descargar este torrent suele ser más rápido que calcularlo desde cero. En particular, contiene índices SQLite para los archivos tar, para usar con <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Preguntas Frecuentes (FAQ)"

msgid "page.faq.what_is.title"
msgstr "¿Qué es el Archivo de Anna?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>El Archivo de Anna</span> es un proyecto sin ánimo de lucro con dos objetivos:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Preservación:</strong> Respaldar todo el conocimiento y la cultura de la humanidad.</li><li><strong>Acceso:</strong> Hacer este conocimiento y esta cultura disponibles para todo el mundo.</li>"

msgid "page.home.intro.open_source"
msgstr "Todos nuestro <a %(a_code)s>código</a> y <a %(a_datasets)s>datos</a> son completamente de código abierto."

msgid "page.home.preservation.header"
msgstr "Preservación"

msgid "page.home.preservation.text1"
msgstr "Preservamos libros, artículos, cómics, revistas y más, recopilando este material de varias <a href=\"https://es.wikipedia.org/wiki/Biblioteca_fantasma\">bibliotecas fantasma</a>, bibliotecas oficiales y otras colecciones en un solo lugar. Todos estos datos son preservados para siempre al hacerlos fáciles de duplicar en masa — usando torrents — y resultando en múltiples copias por todo el mundo. Algunas bibliotecas fantasma ya hacen esto (p.ej. Sci-Hub, Library Genesis), mientras que el Archivo de Anna “libera” otras bibliotecas que no ofrecen distribución en masa (p.ej. Z-Library) o directamente no son bibliotecas fantasma (p.ej. Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Esta amplia distribución, combinada con el código abierto, hace nuestra página web resiliente a ser retirada, y garantiza la preservación a largo plazo del conocimiento y la cultura de la humanidad. Descubre más sobre <a href=\"/datasets\">nuestros datasets</a>."

msgid "page.home.preservation.label"
msgstr "Estimamos que hemos preservado aproximadamente un <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% de los libros del mundo</a>."

msgid "page.home.access.header"
msgstr "Acceso"

msgid "page.home.access.text"
msgstr "Trabajamos con compañeros para hacer nuestras colecciones disponibles fácilmente y libremente a todos. Creemos que todos tienen el derecho de la sabiduría colectiva de la humanidad, y <a %(a_search)s>no a costa de los autores</a>."

msgid "page.home.access.label"
msgstr "Descargas por hora en los últimos 30 días. Promedio por hora: %(hourly)s. Promedio diario: %(daily)s."

msgid "page.about.text2"
msgstr "Creemos firmemente en el libre flujo de la información y la preservación del conocimiento y la cultura. Con este motor de búsqueda nos apoyamos a hombros de gigantes. Respetamos profundamente el arduo trabajo de las personas que han creado las distintas bibliotecas fantasma y esperamos que este motor de búsqueda amplíe su alcance."

msgid "page.about.text3"
msgstr "Para mantenerte informado de nuestra evolución, sigue a Anna en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> (paginas en inglés) o en <a href=\"https://t.me/annasarchiveorg\"> Telegram</a> . Para preguntas y feedback contacta con Anna en %(email)s."

msgid "page.faq.help.title"
msgstr "¿Cómo puedo ayudar?"

msgid "page.about.help.text"
msgstr "<li>1. Síguenos en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Haz correr la voz sobre el Archivo de Anna en Twitter, Reddit, Tiktok, Instagram, en tu cafetería o biblioteca local, ¡o donde sea que vayas! No creemos en el control de acceso: si nos eliminan, simplemente apareceremos en otro lugar, ya que todo nuestro código y datos son de código abierto.</li><li>3. Si puedes, considera <a href=\"/donate\">donar</a>.</li><li>4. Ayuda a <a href=\"https://translate.annas-software.org/\">traducir</a> nuestro sitio web a diferentes idiomas.</li><li>5. Si eres ingeniero de software, considera contribuir a nuestro <a href=\"https://annas-software.org/\">código abierto</a> o sembrar nuestros <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Ahora también tenemos un canal de Matrix sincronizado en %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Si eres un investigador de seguridad, podemos usar tus habilidades tanto para el ataque como la defensa. Visita nuestra página de <a %(a_security)s>Seguridad</a>."

msgid "page.about.help.text7"
msgstr "7. Buscamos expertos en pagos para comerciantes anónimos. Nos ayudas a añadir mas formas convenientes para donar? PayPal, WeChat, tarjetas de regalo. Si conoces a alguien, por favor contáctanos."

msgid "page.about.help.text8"
msgstr "8. Siempre estamos buscando más capacidad para nuestro servidor."

msgid "page.about.help.text9"
msgstr "9. Puedes ayudar reportando problemas con archivos, dejando comentarios y creando listas en este mismo sitio. También puedes ayudarnos <a %(a_upload)s> subiendo más libros</a> o reparando problemas de archivos o dando formato a libros existentes."

msgid "page.about.help.text10"
msgstr "10. Crea o ayuda a mantener nuestra pagina de Wikipedia del Archivo de Anna en tu idioma."

msgid "page.about.help.text11"
msgstr "11. Buscamos poner pequeños anuncios de buen gusto, si quieres anunciarte en el Archivo de Anna, por favor háznoslo saber."

msgid "page.faq.help.mirrors"
msgstr "Nos encantaría que la gente instalara <a %(a_mirrors)s>espejos</a> y lo apoyaremos financieramente."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Para obtener información más detallada sobre cómo ser voluntario, consulte nuestra página de <a %(a_volunteering)s>Voluntariado y Recompensas</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "¿Por qué son tan lentas las descargas lentas?"

msgid "page.faq.slow.text1"
msgstr "Literalmente no tenemos suficientes recursos para ofrecer descargas de alta velocidad a todo el mundo, por mucho que nos gustaría. Si un benefactor rico quisiera dar un paso adelante y proporcionarnos esto, sería increíble, pero hasta entonces, estamos haciendo nuestro mejor esfuerzo. Somos un proyecto sin fines de lucro que apenas puede sostenerse a través de donaciones."

msgid "page.faq.slow.text2"
msgstr "Por eso implementamos dos sistemas para descargas gratuitas, con nuestros socios: servidores compartidos con descargas lentas, y servidores ligeramente más rápidos con una lista de espera (para reducir el número de personas descargando al mismo tiempo)."

msgid "page.faq.slow.text3"
msgstr "También tenemos <a %(a_verification)s>verificación del navegador</a> para nuestras descargas lentas, porque de lo contrario los bots y los raspadores las abusarán, haciendo las cosas aún más lentas para los usuarios legítimos."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Tenga en cuenta que, al usar el Navegador Tor, es posible que deba ajustar su configuración de seguridad. En la opción más baja, llamada “Estándar”, el desafío de Cloudflare turnstile tiene éxito. En las opciones más altas, llamadas “Más seguro” y “El más seguro”, el desafío falla."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Para archivos grandes, a veces las descargas lentas pueden interrumpirse a la mitad. Recomendamos usar un gestor de descargas (como JDownloader) para reanudar automáticamente las descargas grandes."

msgid "page.donate.faq.title"
msgstr "FAQ sobre donaciones"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>¿Las membresías se renuevan automáticamente?</div> Las membresías <strong>no</strong> se renuevan automáticamente. Puedes unirte por tanto o tan poco tiempo como quieras."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>¿Puedo mejorar mi membresía o obtener múltiples membresías?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>¿Tenéis de otros métodos de pago?</div> Actualmente no. Mucha gente no quiere que existan archivos como este, por lo que debemos tener cuidado. Si puedes ayudarnos a establecer otros métodos de pago (más convenientes) de manera segura, contáctanos a %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>¿Qué significan los rangos por mes?</div> Puede llegar al lado inferior de un rango aplicando todos los descuentos, como elegir un período más largo que un mes."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>¿En qué gastas las donaciones?</div> El 100%% se destina a preservar y hacer accesible el conocimiento y la cultura del mundo. Actualmente lo gastamos principalmente en servidores, almacenamiento y ancho de banda. No se destinará dinero personalmente a ningún miembro del equipo."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>¿Puedo hacer una donación importante?</div> ¡Sería increíble!. Para donaciones superiores a unos pocos miles de dólares, ponte en contacto con nosotros directamente en %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>¿Puedo hacer una donación sin convertirme en miembro?</div> Claro que sí. Aceptamos donaciones de cualquier cantidad en esta dirección de Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "¿Cómo subo libros nuevos?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativamente, puedes subirlos a Z-Library <a %(a_upload)s>aquí</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Para cargas pequeñas (hasta 10,000 archivos) por favor súbalos tanto a %(first)s como a %(second)s."

msgid "page.upload.text1"
msgstr "De momento, sugerimos subir libros nuevos a las bifurcaciones de Library Genesis. Aquí hay una <a %(a_guide)s>guía útil</a>. Ten en cuenta que ambas bifurcaciones que incluimos en este sitio web usan el mismo sistema de subida."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Para Libgen.li, asegúrese de primero iniciar sesión en <a %(a_forum)s>su foro</a> con el nombre de usuario %(username)s y la contraseña %(password)s, y luego regrese a su <a %(a_upload_page)s>página de carga</a>."

msgid "common.libgen.email"
msgstr "Si tu correo electrónico no funciona en los foros de Libgen, recomendamos usar <a %(a_mail)s>Proton Mail</a> (gratis). También puedes <a %(a_manual)s>solicitar manualmente</a> que tu cuenta se active."

msgid "page.faq.mhut_upload"
msgstr "Tenga en cuenta que mhut.org bloquea ciertos rangos de IP, por lo que podría ser necesario usar una VPN."

msgid "page.upload.large.text"
msgstr "Para subidas grandes (más de 10.000 ficheros) que no sean aceptadas en Libgen o Z-Library, por favor contacte con nosotros por %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Para subir artículos académicos, por favor también (además de Library Genesis) súbelos a <a %(a_stc_nexus)s>STC Nexus</a>. Son la mejor biblioteca fantasma para nuevos artículos. Aún no los hemos integrado, pero lo haremos en algún momento. Puedes usar su <a %(a_telegram)s>bot de subida en Telegram</a>, o contactar la dirección listada en su mensaje fijado si tienes demasiados archivos para subir de esta manera."

msgid "page.faq.request.title"
msgstr "¿Cómo solicito libros?"

msgid "page.request.cannot_accomodate"
msgstr "Ahora mismo no podemos aceptar peticiones de libros."

msgid "page.request.forums"
msgstr "Por favor realice sus solicitudes en los foros de Z-Library o Libgen."

msgid "page.request.dont_email"
msgstr "No nos envíes tus peticiones por correo electrónico."

msgid "page.faq.metadata.title"
msgstr "¿Recopilas metadatos?"

msgid "page.faq.metadata.indeed"
msgstr "De hecho lo hacemos."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "¿He descargado 1984 de George Orwell, vendrá la policía a mi puerta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "No te preocupes demasiado, hay muchas personas descargando de los sitios web a los que enlazamos, y es extremadamente raro meterse en problemas. Sin embargo, para estar seguro, recomendamos usar una VPN (de pago) o <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "¿Cómo guardo mis configuraciones de búsqueda?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selecciona las configuraciones que te gusten, deja el cuadro de búsqueda vacío, haz clic en “Buscar” y luego guarda la página en tus favoritos usando la función de marcadores de tu navegador."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "¿Tienen una aplicación móvil?"

msgid "page.faq.mobile.text1"
msgstr "No tenemos una aplicación móvil oficial, pero se puede instalar este sitio web como una aplicación."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Haz clic en el menú de tres puntos en la parte superior derecha y selecciona “Añadir a la pantalla de inicio”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Haz clic en el botón “Compartir” en la parte inferior y selecciona “Agregar a la pantalla de inicio”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "¿Tienen una API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Tenemos una API JSON estable para miembros, para obtener una URL de descarga rápida: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentación dentro del JSON)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Para otros casos de uso, como iterar a través de todos nuestros archivos, construir búsquedas personalizadas, y así sucesivamente, recomendamos <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Los datos en bruto se pueden explorar manualmente <a %(a_explore)s>a través de archivos JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Nuestra lista de torrents en bruto también se puede descargar como <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "FAQ de Torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Me gustaría ayudar a compartir, pero no tengo mucho espacio en disco."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Utiliza el <a %(a_list)s>generador de lista de torrents</a> para generar una lista de torrents que más necesitan ser compartidos, dentro de los límites de tu espacio de almacenamiento."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Los torrents son demasiado lentos; ¿puedo descargar los datos directamente de ustedes?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Sí, consulta la página de <a %(a_llm)s>datos de LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "¿Puedo descargar solo un subconjunto de los archivos, como un idioma o tema en particular?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Respuesta corta: no fácilmente."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Respuesta larga:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "La mayoría de los torrents contienen los archivos directamente, lo que significa que puedes instruir a los clientes de torrents para que solo descarguen los archivos necesarios. Para determinar qué archivos descargar, puedes <a %(a_generate)s>generar</a> nuestros metadatos, o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Desafortunadamente, algunas colecciones de torrents contienen archivos .zip o .tar en la raíz, en cuyo caso necesitas descargar todo el torrent antes de poder seleccionar archivos individuales."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Sin embargo, tenemos <a %(a_ideas)s>algunas ideas</a> para este último caso.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Todavía no hay herramientas fáciles de usar para filtrar torrents, pero agradecemos las contribuciones."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "¿Cómo manejan los duplicados en los torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Tratamos de mantener la duplicación o superposición mínima entre los torrents en esta lista, pero esto no siempre se puede lograr y depende en gran medida de las políticas de las bibliotecas fuente. Para las bibliotecas que publican sus propios torrents, está fuera de nuestras manos. Para los torrents lanzados por el Archivo de Anna, deduplicamos solo en base al hash MD5, lo que significa que diferentes versiones del mismo libro no se deduplican."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "¿Puedo obtener la lista de torrents en formato JSON?"

msgid "page.faq.torrents.a5"
msgstr "Sí."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "No veo archivos PDF o EPUB en los torrents, ¿solo archivos binarios? ¿Qué hago?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Estos son en realidad archivos PDF y EPUB, solo que no tienen una extensión en muchos de nuestros torrents. Hay dos lugares en los que puedes encontrar los metadatos de los archivos torrent, incluidos los tipos/extensiones de archivos:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Cada colección o lanzamiento tiene sus propios metadatos. Por ejemplo, los <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> tienen una base de datos de metadatos correspondiente alojada en el sitio web de Libgen.rs. Normalmente enlazamos a recursos de metadatos relevantes desde la <a %(a_datasets)s>página del conjunto de datos</a> de cada colección."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Recomendamos <a %(a_generate)s>generar</a> o <a %(a_download)s>descargar</a> nuestras bases de datos de ElasticSearch y MariaDB. Estas contienen un mapeo para cada registro en Anna’s Archive a sus archivos torrent correspondientes (si están disponibles), bajo “torrent_paths” en el JSON de ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "¿Por qué mi cliente de torrents no puede abrir algunos de sus archivos torrent/enlaces magnéticos?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Algunos clientes de torrents no soportan tamaños de piezas grandes, que muchos de nuestros torrents tienen (para los más nuevos ya no lo hacemos, ¡aunque es válido según las especificaciones!). Así que prueba con un cliente diferente si te encuentras con este problema, o queja a los creadores de tu cliente de torrents."

#, fuzzy
msgid "page.faq.security.title"
msgstr "¿Tienen un programa de divulgación responsable?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Damos la bienvenida a los investigadores de seguridad para buscar vulnerabilidades en nuestros sistemas. Somos grandes defensores de la divulgación responsable. Contáctenos <a %(a_contact)s>aquí</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Actualmente no podemos otorgar recompensas por errores, excepto por vulnerabilidades que tengan el <a %(a_link)s>potencial de comprometer nuestro anonimato</a>, para las cuales ofrecemos recompensas en el rango de $10k-50k. Nos gustaría ofrecer un alcance más amplio para las recompensas por errores en el futuro. Ten en cuenta que los ataques de ingeniería social están fuera de alcance."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si estás interesado en la seguridad ofensiva y quieres ayudar a archivar el conocimiento y la cultura del mundo, asegúrate de contactarnos. Hay muchas maneras en las que puedes ayudar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "¿Hay más recursos sobre el Archivo de Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualizaciones regulares"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software de Anna</a> — nuestro código abierto"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traducir en el Software de Anna</a> — nuestro sistema de traducción"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — sobre los datos"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternativos"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — más sobre nosotros (¡por favor ayuda a mantener esta página actualizada, o crea una en tu propio idioma!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "¿Cómo informo sobre una infracción de derechos de autor?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "No alojamos ningún material con derechos de autor aquí. Somos un motor de búsqueda y, como tal, solo indexamos metadatos que ya están disponibles públicamente. Al descargar de estas fuentes externas, le sugerimos que verifique las leyes en su jurisdicción con respecto a lo que está permitido. No somos responsables del contenido alojado por otros."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si tienes quejas sobre lo que ves aquí, lo mejor es contactar con el sitio web original. Regularmente incorporamos sus cambios en nuestra base de datos. Si realmente crees que tienes una queja válida de DMCA a la que debamos responder, por favor completa el <a %(a_copyright)s>formulario de reclamación de DMCA / Derechos de Autor</a>. Tomamos tus quejas en serio y te responderemos lo antes posible."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "¡Odio cómo estás manejando este proyecto!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "También nos gustaría recordar a todos que todo nuestro código y datos son completamente de código abierto. Esto es único para proyectos como el nuestro: no conocemos ningún otro proyecto con un catálogo tan masivo que también sea completamente de código abierto. Damos la bienvenida a cualquiera que piense que gestionamos mal nuestro proyecto a tomar nuestro código y datos y crear su propia biblioteca fantasma. No lo decimos por despecho ni nada parecido; realmente creemos que sería increíble, ya que elevaría el nivel para todos y preservaría mejor el legado de la humanidad."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "¿Tiene un monitor de tiempo de actividad?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Por favor, vea <a %(a_href)s>este excelente proyecto</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "¿Cómo puedo donar libros u otros materiales físicos?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Por favor, envíelos al <a %(a_archive)s>Internet Archive</a>. Ellos los preservarán adecuadamente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "¿Quién es Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "¡Tú eres Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "¿Cuáles son tus libros favoritos?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Aquí hay algunos libros que tienen un significado especial para el mundo de las bibliotecas en la sombra y la preservación digital:"

msgid "page.fast_downloads.no_more_new"
msgstr "Te has quedado sin descargas rápidas hoy."

msgid "page.fast_downloads.no_member"
msgstr "Hazte miembro para utilizar las descargas rápidas."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Ahora aceptamos tarjetas de regalo de Amazon, tarjetas de crédito y débito, criptomonedas, Alipay y WeChat."

msgid "page.home.full_database.header"
msgstr "Base de datos completa"

msgid "page.home.full_database.subtitle"
msgstr "Libros, artículos, revistas, cómics, registros de biblioteca, metadatos,…"

msgid "page.home.full_database.search"
msgstr "Buscar"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub a <a %(a_paused)s>pausado</a> la subida de nuevos artículos."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB es una continuación de Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Acceso directo a %(count)s artículos académicos"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Abrir"

msgid "page.home.scidb.browser_verification"
msgstr "Si eres <a %(a_member)s>miembro</a>, no se requiere verificación del navegador."

msgid "page.home.archive.header"
msgstr "Archivo a largo plazo"

msgid "page.home.archive.body"
msgstr "Los datasets utilizados en el Archivo de Anna son completamente abiertos y se pueden crear reflejos de forma masiva mediante torrents. <a %(a_datasets)s>Más información...</a>"

msgid "page.home.torrents.body"
msgstr "Puedes ser de enorme ayuda sembrando torrents. <a %(a_torrents)s> Conocer mas…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "Datos de entrenamiento de LLM"

msgid "page.home.llm.body"
msgstr "Tenemos la colección más grande del mundo de datos de texto de alta calidad. <a %(a_llm)s>Más información...</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Reflejos: se buscan voluntarios"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Buscando voluntarios"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Como un proyecto sin fines de lucro y de código abierto, siempre estamos buscando personas que nos ayuden."

msgid "page.home.payment_processor.body"
msgstr "Si ejecuta un procesador de pagos anónimo de alto riesgo, contáctenos. También buscamos personas que quieran colocar pequeños anuncios de buen gusto. Todos los ingresos se destinan a nuestros esfuerzos de preservación."

msgid "layout.index.header.nav.annasblog"
msgstr "El Blog de Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Descargas IPFS"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Todos los enlaces de descarga para este archivo</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Puerta de enlace IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(es posible que debas intentarlo varias veces con IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Para obtener descargas más rápidas y saltarte verificaciones de navegador, <a %(a_membership)s>hazte socio</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Para realizar una duplicación masiva de nuestra colección, consulta las páginas de <a %(a_datasets)s>Datasets</a> y <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Datos de LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Se entiende bien que los LLM prosperan con datos de alta calidad. Tenemos la colección más grande de libros, artículos, revistas, etc. en el mundo, que son algunas de las fuentes de texto de mayor calidad."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Escala y alcance únicos"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Nuestra colección contiene más de cien millones de archivos, incluidos revistas académicas, libros de texto y revistas. Alcanzamos esta escala combinando grandes repositorios existentes."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Algunas de nuestras colecciones fuente ya están disponibles en masa (Sci-Hub y partes de Libgen). Otras fuentes las liberamos nosotros mismos. <a %(a_datasets)s>Datasets</a> muestra una visión completa."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Nuestra colección incluye millones de libros, artículos y revistas de antes de la era del libro electrónico. Grandes partes de esta colección ya han sido OCRizadas y ya tienen poco solapamiento interno."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Cómo podemos ayudar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Podemos proporcionar acceso de alta velocidad a nuestras colecciones completas, así como a colecciones no publicadas."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Este es un acceso a nivel empresarial que podemos proporcionar por donaciones en el rango de decenas de miles de USD. También estamos dispuestos a intercambiar esto por colecciones de alta calidad que aún no tenemos."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Podemos reembolsarle si puede proporcionarnos un enriquecimiento de nuestros datos, como:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Eliminación de solapamientos (deduplicación)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extracción de texto y metadatos"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "¡Apoye el archivo a largo plazo del conocimiento humano, mientras obtiene mejores datos para su modelo!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contáctenos</a> para discutir cómo podemos trabajar juntos."

msgid "page.login.continue"
msgstr "Continuar"

msgid "page.login.please"
msgstr "<a %(a_account)s>Inicie sesión</a> para ver esta página.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Archivo de Anna está temporalmente fuera de servicio por mantenimiento. Por favor, vuelva en una hora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Mejorar metadatos"

#, fuzzy
msgid "page.metadata.body1"
msgstr "¡Puedes ayudar a la preservación de libros mejorando los metadatos! Primero, lee la información de fondo sobre metadatos en el Archivo de Anna, y luego aprende cómo mejorar los metadatos a través de la vinculación con Open Library, y gana una membresía gratuita en el Archivo de Anna."

msgid "page.metadata.background.title"
msgstr "Antecedentes"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Cuando miras un libro en el Archivo de Anna, puedes ver varios campos: título, autor, editorial, edición, año, descripción, nombre de archivo y más. Todas esas piezas de información se llaman <em>metadatos</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Como combinamos libros de varias <em>bibliotecas fuente</em>, mostramos los metadatos disponibles en esa biblioteca fuente. Por ejemplo, para un libro que obtuvimos de Library Genesis, mostraremos el título de la base de datos de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "A veces un libro está presente en <em>múltiples</em> bibliotecas fuente, que pueden tener diferentes campos de metadatos. En ese caso, simplemente mostramos la versión más larga de cada campo, ya que esa probablemente contiene la información más útil. Aún así, mostraremos los otros campos debajo de la descripción, por ejemplo, como \"título alternativo\" (pero solo si son diferentes)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "También extraemos <em>códigos</em> como identificadores y clasificadores de la biblioteca fuente. <em>Identificadores</em> representan de manera única una edición particular de un libro; ejemplos son ISBN, DOI, Open Library ID, Google Books ID o Amazon ID. <em>Clasificadores</em> agrupan varios libros similares; ejemplos son Dewey Decimal (DCC), UDC, LCC, RVK o GOST. A veces estos códigos están explícitamente vinculados en las bibliotecas fuente, y a veces podemos extraerlos del nombre de archivo o descripción (principalmente ISBN y DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Podemos usar identificadores para encontrar registros en <em>colecciones solo de metadatos</em>, como OpenLibrary, ISBNdb o WorldCat/OCLC. Hay una pestaña específica de <em>metadatos</em> en nuestro motor de búsqueda si deseas explorar esas colecciones. Usamos registros coincidentes para completar campos de metadatos faltantes (por ejemplo, si falta un título), o por ejemplo, como \"título alternativo\" (si ya existe un título)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Para ver exactamente de dónde provienen los metadatos de un libro, consulta la pestaña <em>“Detalles técnicos”</em> en la página de un libro. Tiene un enlace al JSON sin procesar de ese libro, con enlaces al JSON sin procesar de los registros originales."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Para más información, consulta las siguientes páginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Buscar (pestaña de metadatos)</a>, <a %(a_codes)s>Explorador de códigos</a> y <a %(a_example)s>Ejemplo de JSON de metadatos</a>. Finalmente, todos nuestros metadatos pueden ser <a %(a_generated)s>generados</a> o <a %(a_downloaded)s>descargados</a> como bases de datos ElasticSearch y MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Vinculación con Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Entonces, si encuentras un archivo con malos metadatos, ¿cómo deberías arreglarlo? Puedes ir a la biblioteca fuente y seguir sus procedimientos para corregir los metadatos, pero ¿qué hacer si un archivo está presente en múltiples bibliotecas fuente?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Hay un identificador que se trata de manera especial en el Archivo de Anna. <strong>¡El campo md5 de annas_archive en Open Library siempre anula todos los demás metadatos!</strong> Retrocedamos un poco primero y aprendamos sobre Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library fue fundada en 2006 por Aaron Swartz con el objetivo de \"una página web para cada libro jamás publicado\". Es una especie de Wikipedia para metadatos de libros: todos pueden editarla, tiene una licencia libre y puede descargarse en bloque. Es una base de datos de libros que está más alineada con nuestra misión; de hecho, el Archivo de Anna se ha inspirado en la visión y vida de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "En lugar de reinventar la rueda, decidimos redirigir a nuestros voluntarios hacia Open Library. Si ves un libro que tiene metadatos incorrectos, puedes ayudar de la siguiente manera:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Ve al <a %(a_openlib)s>sitio web de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Encuentra el registro correcto del libro. <strong>ADVERTENCIA:</strong> asegúrate de seleccionar la <strong>edición</strong> correcta. En Open Library, tienes \"obras\" y \"ediciones\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Una \"obra\" podría ser \"Harry Potter y la piedra filosofal\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Una \"edición\" podría ser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La primera edición de 1997 publicada por Bloomsbery con 256 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "La edición de bolsillo de 2003 publicada por Raincoast Books con 223 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La traducción polaca de 2000 “Harry Potter I Kamie Filozoficzn” por Media Rodzina con 328 páginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Todas esas ediciones tienen diferentes ISBN y contenidos distintos, ¡así que asegúrate de seleccionar la correcta!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "¡Edite el registro (o créelo si no existe), y añada tanta información útil como pueda! Ya que está aquí, aproveche para hacer que el registro sea realmente increíble."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "En “Números de ID” selecciona “Anna’s Archive” y añade el MD5 del libro de Anna’s Archive. Este es el largo string de letras y números después de “/md5/” en la URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Intenta encontrar otros archivos en Anna’s Archive que también coincidan con este registro, y añádelos también. En el futuro, podremos agruparlos como duplicados en la página de búsqueda de Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Cuando termines, anota la URL que acabas de actualizar. Una vez que hayas actualizado al menos 30 registros con los MD5 de Anna’s Archive, envíanos un <a %(a_contact)s>correo electrónico</a> con la lista. Te daremos una membresía gratuita para Anna’s Archive, para que puedas hacer este trabajo más fácilmente (y como agradecimiento por tu ayuda). Estas deben ser ediciones de alta calidad que añadan cantidades sustanciales de información, de lo contrario, tu solicitud será rechazada. Tu solicitud también será rechazada si alguna de las ediciones es revertida o corregida por los moderadores de Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Ten en cuenta que esto solo funciona para libros, no para artículos académicos u otros tipos de archivos. Para otros tipos de archivos, aún recomendamos encontrar la biblioteca fuente. Puede tomar algunas semanas para que los cambios se incluyan en Anna’s Archive, ya que necesitamos descargar el último volcado de datos de Open Library y regenerar nuestro índice de búsqueda."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Espejos: llamado a voluntarios"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Para aumentar la resiliencia del Archivo de Anna, estamos buscando voluntarios para ejecutar espejos."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Estamos buscando esto:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Usted ejecuta la base de código de código abierto del Archivo de Anna y actualiza regularmente tanto el código como los datos."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Su versión está claramente distinguida como un espejo, por ejemplo, “Archivo de Bob, un espejo del Archivo de Anna”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Está dispuesto a asumir los riesgos asociados con este trabajo, que son significativos. Tiene un profundo entendimiento de la seguridad operativa requerida. El contenido de <a %(a_shadow)s>estos</a> <a %(a_pirate)s>posts</a> le es evidente."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Estás dispuesto a contribuir a nuestra <a %(a_codebase)s>base de código</a> — en colaboración con nuestro equipo — para que esto suceda."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inicialmente no le daremos acceso a las descargas de nuestro servidor asociado, pero si las cosas van bien, podemos compartir eso con usted."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Gastos de alojamiento"

msgid "page.mirrors.expenses.text1"
msgstr "Estamos dispuestos a cubrir los gastos de alojamiento y VPN, inicialmente hasta 200 dólares por mes. Esto es suficiente para un servidor de búsqueda básico y un proxy protegido por DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Solo pagaremos por el alojamiento una vez que tengas todo configurado y hayas demostrado que puedes mantener el archivo actualizado con las actualizaciones. Esto significa que tendrás que pagar los primeros 1-2 meses de tu bolsillo."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Tu tiempo no será compensado (y el nuestro tampoco), ya que esto es trabajo puramente voluntario."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si te involucras significativamente en el desarrollo y las operaciones de nuestro trabajo, podemos discutir compartir más de los ingresos por donaciones contigo, para que los despliegues según sea necesario."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Empezando"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Por favor, <strong>no nos contactes</strong> para pedir permiso o para preguntas básicas. ¡Las acciones hablan más que las palabras! Toda la información está disponible, así que adelante con la configuración de tu espejo."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Siéntete libre de publicar tickets o solicitudes de fusión en nuestro Gitlab cuando te encuentres con problemas. Es posible que necesitemos construir algunas características específicas del espejo contigo, como el cambio de marca de “Anna’s Archive” al nombre de tu sitio web, (inicialmente) deshabilitar cuentas de usuario, o enlazar de nuevo a nuestro sitio principal desde las páginas de libros."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Una vez que tengas tu espejo en funcionamiento, por favor contáctanos. Nos encantaría revisar tu OpSec, y una vez que esté sólido, enlazaremos a tu espejo y comenzaremos a trabajar más de cerca contigo."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "¡Gracias de antemano a cualquiera que esté dispuesto a contribuir de esta manera! No es para los débiles de corazón, pero solidificaría la longevidad de la biblioteca verdaderamente abierta más grande en la historia de la humanidad."

msgid "page.partner_download.header"
msgstr "Descargar desde una página web asociada"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Las descargas lentas solo están disponibles a través del sitio web oficial. Visita %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Las descargas lentas no están disponibles a través de las VPN de Cloudflare ni desde las direcciones IP de Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Por favor, espere <span %(span_countdown)s>%(wait_seconds)s</span> segundos para descargar este archivo."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Descargar ahora</a>"

#, fuzzy
msgid "page.partner_download.li4"
msgstr "¡Gracias por esperar, esto mantiene el sitio web accesible de forma gratuita para todos! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Atención: ha habido muchas descargas desde tu dirección IP en las últimas 24 horas. Las descargas pueden ser más lentas de lo habitual."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descargas realizadas desde su dirección IP en las últimas 24 horas: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si está utilizando una VPN, una conexión a internet compartida o su ISP comparte IPs, esta advertencia podría deberse a eso."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Para dar a todos la oportunidad de descargar archivos de forma gratuita, necesitas esperar antes de poder descargar este archivo."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Si lo deseas, puedes seguir navegando por el Archivo de Anna en una pestaña diferente mientras esperas (si tu navegador admite la actualización de pestañas en segundo plano)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Si lo deseas, puedes esperar a que se carguen varias páginas de descarga al mismo tiempo (pero por favor, solo descarga un archivo a la vez por servidor)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Una vez que obtenga un enlace de descarga, será válido por varias horas."

msgid "layout.index.header.title"
msgstr "El Archivo de Anna"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Registro en el Archivo de Anna"

msgid "page.scidb.download"
msgstr "Descargar"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Para apoyar la accesibilidad y la preservación a largo plazo del conocimiento humano, conviértete en <a %(a_donate)s>miembro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Como un bono, 🧬&nbsp;SciDB carga más rápido para los miembros, sin ningún límite."

msgid "page.scidb.refresh"
msgstr "¿No funciona? Prueba a <a %(a_refresh)s>actualizar</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "No hay vista previa disponible aún. Descargue el archivo desde <a %(a_path)s>Archivo de Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB es una continuación de Sci-Hub, con su interfaz familiar y visualización directa de PDFs. Ingrese su DOI para ver."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Tenemos la colección completa de Sci-Hub, así como nuevos artículos. La mayoría se pueden ver directamente con una interfaz familiar, similar a Sci-Hub. Algunos se pueden descargar a través de fuentes externas, en cuyo caso mostramos enlaces a esas."

msgid "page.search.title.results"
msgstr "%(search_input)s - Buscar"

msgid "page.search.title.new"
msgstr "Nueva búsqueda"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Incluir solo"

msgid "page.search.icon.exclude"
msgstr "Excluir"

msgid "page.search.icon.unchecked"
msgstr "Sin verificar"

msgid "page.search.tabs.download"
msgstr "Descargar"

msgid "page.search.tabs.journals"
msgstr "Artículos periodísticos"

msgid "page.search.tabs.digital_lending"
msgstr "Préstamos digitales"

msgid "page.search.tabs.metadata"
msgstr "Metadatos"

msgid "common.search.placeholder"
msgstr "Título, autor, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Buscar"

msgid "page.search.search_settings"
msgstr "Configuración de búsqueda"

msgid "page.search.submit"
msgstr "Buscar"

msgid "page.search.too_long_broad_query"
msgstr "La búsqueda tardó demasiado, lo que es común para consultas amplias. Los términos en el filtro pueden no ser precisos."

msgid "page.search.too_inaccurate"
msgstr "La búsqueda tardó demasiado, lo que significa que podrían aparecer resultados inexactos. A veces, <a %(a_reload)s>recargar</a> la página ayuda."

msgid "page.search.filters.display.header"
msgstr "Mostrar"

msgid "page.search.filters.display.list"
msgstr "Lista"

msgid "page.search.filters.display.table"
msgstr "Tabla"

msgid "page.search.advanced.header"
msgstr "Avanzado"

msgid "page.search.advanced.description_comments"
msgstr "Buscar descripción y metadatos de comentarios"

msgid "page.search.advanced.add_specific"
msgstr "Agregar campo de búsqueda específico"

msgid "common.specific_search_fields.select"
msgstr "(buscar campo específico)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Año de publicación"

msgid "page.search.filters.content.header"
msgstr "Contenido"

msgid "page.search.filters.filetype.header"
msgstr "Tipo de archivo"

msgid "page.search.more"
msgstr "más…"

msgid "page.search.filters.access.header"
msgstr "Acceso"

msgid "page.search.filters.source.header"
msgstr "Fuente"

msgid "page.search.filters.source.scraped"
msgstr "extraído y de código abierto por AA"

msgid "page.search.filters.language.header"
msgstr "Idioma"

msgid "page.search.filters.order_by.header"
msgstr "Ordenar por"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Más relevante"

msgid "page.search.filters.sorting.newest"
msgstr "Más reciente"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(año de publicación)"

msgid "page.search.filters.sorting.oldest"
msgstr "Más antiguo"

msgid "page.search.filters.sorting.largest"
msgstr "Más grande"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(tamaño del archivo)"

msgid "page.search.filters.sorting.smallest"
msgstr "Más pequeño"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(código abierto)"

msgid "page.search.filters.sorting.random"
msgstr "Aleatorio"

msgid "page.search.header.update_info"
msgstr "El índice de búsqueda se actualiza mensualmente. Actualmente incluye entradas hasta %(last_data_refresh_date)s. Para más información técnica, consulta la página %(link_open_tag)sDatasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Para explorar el índice de búsqueda por códigos, utiliza el <a %(a_href)s>Explorador de Códigos</a>."

msgid "page.search.results.search_downloads"
msgstr "Escribe en el cuadro para buscar en nuestro catálogo de %(count)s archivos descargables directamente, que <a %(a_preserve)s>conservamos para siempre</a>."

msgid "page.search.results.help_preserve"
msgstr "De hecho, cualquiera puede ayudar a preservar estos archivos sembrando nuestra <a %(a_torrents)s>lista unificada de torrents</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Actualmente contamos con el catálogo abierto de libros, artículos y otros trabajos escritos más completo del mundo. Reflejamos Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>y más</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Si encuentras otras \"bibliotecas fantasma\" que deberíamos reflejar, o si tienes alguna pregunta, contáctanos en %(email)s."

msgid "page.search.results.dmca"
msgstr "Para reclamaciones de derechos de autor/DMCA <a %(a_copyright)s>haz clic aquí</a>."

msgid "page.search.results.shortcuts"
msgstr "Consejo: utiliza los atajos de teclado \"/\" (enfoque de búsqueda), \"enter\" (buscar), \"j\" (arriba), \"k\" (abajo) para una navegación más rápida."

msgid "page.search.results.looking_for_papers"
msgstr "¿Buscas artículos?"

msgid "page.search.results.search_journals"
msgstr "Escribe en el cuadro para buscar en nuestro catálogo de %(count)s artículos académicos y periodísticos, que <a %(a_preserve)s>preservamos para siempre</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Escribe en el cuadro para buscar archivos en bibliotecas de préstamo digital."

msgid "page.search.results.digital_lending_info"
msgstr "Este índice de búsqueda incluye actualmente metadatos de la biblioteca de Préstamos Digitales Controlados de Internet Archive. <a %(a_datasets)s>Más sobre nuestros datasets</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Para más bibliotecas de préstamo digital, consulta <a %(a_wikipedia)s>Wikipedia</a> y <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Escribe en el cuadro para buscar metadatos de bibliotecas. Esto puede resultar útil al <a %(a_request)s>solicitar un archivo</a>."

msgid "page.search.results.metadata_info"
msgstr "Este índice de búsqueda incluye actualmente metadatos de varias fuentes. <a %(a_datasets)s>Más sobre nuestros datasets</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Para metadatos, mostramos los registros originales. No realizamos ninguna combinación."

msgid "page.search.results.metadata_info_more"
msgstr "Existen muchísimas fuentes de metadatos para obras escritas en todo el mundo. <a %(a_wikipedia)s>Esta página de Wikipedia</a> es un buen comienzo, pero si conoces otras buenas listas, háznoslo saber."

msgid "page.search.results.search_generic"
msgstr "Escribe en el cuadro para buscar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Estos son registros de metadatos, <span %(classname)s>no</span> archivos descargables."

msgid "page.search.results.error.header"
msgstr "Error durante la búsqueda."

msgid "page.search.results.error.unknown"
msgstr "Intenta <a %(a_reload)s>recargar la página</a>. Si el problema persiste, envíanos un correo electrónico a %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>No se encontraron archivos.</span> Intente con menos términos de búsqueda o diferentes filtros."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A veces esto ocurre incorrectamente cuando el servidor de búsqueda es lento. En tales casos, <a %(a_attrs)s>recargar</a> puede ayudar."

msgid "page.search.found_matches.main"
msgstr "Encontramos coincidencias en: %(in)s. Puedes referirte a la URL encontrada allí al <a %(a_request)s>solicitar un archivo</a>."

msgid "page.search.found_matches.journals"
msgstr "Artículos periodísticos (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Préstamo Digital (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadatos (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Resultados %(from)s-%(to)s (%(total)s total)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ coincidencias parciales"

msgid "page.search.results.partial"
msgstr "%(num)d coincidencias parciales"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariado y Recompensas"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "El Archivo de Anna depende de voluntarios como usted. Aceptamos todos los niveles de compromiso y tenemos dos categorías principales de ayuda que buscamos:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Trabajo voluntario ligero:</span> si solo puedes dedicar unas pocas horas de vez en cuando, todavía hay muchas maneras en las que puedes ayudar. Recompensamos a los voluntarios consistentes con <span %(bold)s>🤝 membresías a Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Trabajo voluntario intenso (recompensas de USD$50-USD$5,000):</span> si puedes dedicar mucho tiempo y/o recursos a nuestra misión, nos encantaría trabajar más de cerca contigo. Eventualmente, puedes unirte al equipo interno. Aunque tenemos un presupuesto ajustado, podemos otorgar <span %(bold)s>💰 recompensas monetarias</span> por el trabajo más intenso."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si no puedes ofrecer tu tiempo como voluntario, aún puedes ayudarnos mucho <a %(a_donate)s>donando dinero</a>, <a %(a_torrents)s>sembrando nuestros torrents</a>, <a %(a_uploading)s>subiendo libros</a> o <a %(a_help)s>contándole a tus amigos sobre el Archivo de Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Empresas:</span> ofrecemos acceso directo de alta velocidad a nuestras colecciones a cambio de una donación a nivel empresarial o intercambio por nuevas colecciones (por ejemplo, nuevos escaneos, datasets OCR, enriquecimiento de nuestros datos). <a %(a_contact)s>Contáctanos</a> si este es tu caso. Consulta también nuestra <a %(a_llm)s>página de LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariado ligero"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si tienes unas horas libres, puedes ayudar de varias maneras. Asegúrate de unirte al <a %(a_telegram)s>chat de voluntarios en Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Como muestra de agradecimiento, generalmente otorgamos 6 meses de “Bibliotecario Suertudo” por hitos básicos, y más por trabajo voluntario continuo. Todos los hitos requieren trabajo de alta calidad: el trabajo descuidado nos perjudica más de lo que nos ayuda y lo rechazaremos. Por favor, <a %(a_contact)s>envíanos un correo</a> cuando alcances un hito."

msgid "page.volunteering.table.header.task"
msgstr "Tarea"

msgid "page.volunteering.table.header.milestone"
msgstr "Hito"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Difundiendo la palabra de Anna’s Archive. Por ejemplo, recomendando libros en AA, enlazando a nuestras publicaciones de blog o, en general, dirigiendo a las personas a nuestro sitio web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "Enlaces o capturas de pantalla %(links)s."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Estos deberían mostrarte informando a alguien sobre Anna’s Archive, y ellos agradeciéndote."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Mejorar metadatos <a %(a_metadata)s>enlazándolos</a> con Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Puedes usar la <a %(a_list)s>lista de problemas de metadata aleatorios</a> como punto de partida."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Asegúrate de dejar un comentario sobre los problemas que resuelvas, para que otros no dupliquen tu trabajo."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "Enlaces de registros que mejoraste %(links)s."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traducir</a> el sitio web."

msgid "page.volunteering.table.translate.milestone"
msgstr "Traducir completamente un idioma (si no estaba cerca de completarse ya.)"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Mejorar la página de Wikipedia del Archivo de Anna en tu idioma. Incluye información de la página de Wikipedia de AA en otros idiomas, y de nuestro sitio web y blog. Añade referencias a AA en otras páginas relevantes."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Enlace al historial de ediciones mostrando que hiciste contribuciones significativas."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Cumplir con solicitudes de libros (o artículos, etc.) en los foros de Z-Library o Library Genesis. No tenemos nuestro propio sistema de solicitudes de libros, pero espejamos esas bibliotecas, así que mejorarlas también mejora el Archivo de Anna."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "Enlaces o capturas de pantalla de solicitudes que cumpliste %(links)s."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Pequeñas tareas publicadas en nuestro <a %(a_telegram)s>chat de voluntarios en Telegram</a>. Usualmente para membresía, a veces para pequeñas recompensas."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Tareas pequeñas publicadas en nuestro grupo de chat de voluntarios."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depende de la tarea."

msgid "page.volunteering.section.bounties.heading"
msgstr "Recompensas"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Siempre estamos buscando personas con sólidas habilidades en programación o seguridad ofensiva para involucrarse. Puedes hacer una contribución significativa en la preservación del legado de la humanidad."

msgid "page.volunteering.section.bounties.text2"
msgstr "Como agradecimiento, ofrecemos membresías por contribuciones sólidas. Como un gran agradecimiento, ofrecemos recompensas monetarias por tareas particularmente importantes y difíciles. Esto no debe considerarse como un reemplazo de un trabajo, pero es un incentivo adicional y puede ayudar con los costos incurridos."

msgid "page.volunteering.section.bounties.text3"
msgstr "La mayor parte de nuestro código es de código abierto, y pediremos lo mismo de tu código al otorgar la recompensa. Hay algunas excepciones que podemos discutir de manera individual."

msgid "page.volunteering.section.bounties.text4"
msgstr "Las recompensas se conceden a la primera persona que complete una tarea. No dudes en comentar un ticket de recompensa para informar a los demás de que estás trabajando en algo, de modo que otros puedan esperar o ponerse en contacto contigo para formar un equipo. Pero ten en cuenta que los demás también son libres de trabajar en ello e intentar ganarte. Sin embargo, no concedemos recompensas por un trabajo descuidado. Si se realizan dos presentaciones de alta calidad cerca una de la otra (dentro de un día o dos), podríamos optar por otorgar recompensas a ambas, a nuestra discreción, por ejemplo, 100%% para la primera presentación y 50%% para la segunda presentación (así que 150%% en total)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Para las recompensas más grandes (especialmente las de scraping), por favor contáctanos cuando hayas completado ~5%% de la tarea, y estés seguro de que tu método se escalará hasta el hito completo. Tendrás que compartir tu método con nosotros para que podamos dar retroalimentación. Además, de esta manera podemos decidir qué hacer si hay varias personas acercándose a una recompensa, como potencialmente otorgarla a múltiples personas, animar a las personas a formar equipo, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ADVERTENCIA: las tareas de alta recompensa son <span %(bold)s>difíciles</span> — podría ser prudente comenzar con las más fáciles."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Ve a nuestra <a %(a_gitlab)s>lista de problemas en Gitlab</a> y ordena por “Prioridad de etiqueta”. Esto muestra aproximadamente el orden de las tareas que nos importan. Las tareas sin recompensas explícitas aún son elegibles para membresía, especialmente aquellas marcadas como “Aceptado” y “Favorito de Anna”. Podrías querer comenzar con un “Proyecto inicial”."

msgid "blog.template.subheading"
msgstr "Actualizaciones sobre <a %(wikipedia_annas_archive)s>el Archivo de Anna</a>, la biblioteca verdaderamente abierta más grande en la historia de la humanidad."

msgid "layout.index.title"
msgstr "El Archivo de Anna"

msgid "layout.index.meta.description"
msgstr "La biblioteca de datos abiertos y código abierto más grande del mundo. Refleja Sci-Hub, Library Genesis, Z-Library, y más."

msgid "layout.index.meta.opensearch"
msgstr "Buscar en el Archivo de Anna"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "¡El Archivo de Anna necesita tu ayuda!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Muchos intentan derribarnos, pero nosotros luchamos."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si dona ahora, conseguirá<strong>el doble</strong> de descargas rápidas."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Válido hasta el final de este mes."

msgid "layout.index.header.nav.donate"
msgstr "Donar"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvar el conocimiento humano: ¡Un gran regalo de vacaciones!"

msgid "layout.index.header.banner.surprise"
msgstr "Sorprende a un ser querido, regálale una cuenta con membresía."

msgid "layout.index.header.banner.mirrors"
msgstr "Para aumentar la resiliencia de Archivo de Anna, estamos buscando voluntarios para ejecutar espejos."

msgid "layout.index.header.banner.valentine_gift"
msgstr "¡El regalo de San Valentín perfecto!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Tenemos un nuevo método de donación disponible: %(method_name)s. Por favor considera %(donate_link_open_tag)sdonar</a> — no es barato mantener este sitio web y tu donación realmente marca la diferencia. Muchas gracias."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Estamos llevando a cabo una recaudación de fondos para <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">respaldar</a> la biblioteca fantasma de cómics más grande del mundo. ¡Gracias por tu apoyo! <a href=\"/donate\">Donar.</a> Si no puedes donar, considera apoyarnos contándoselo a tus amigos y siguiéndonos en <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Descargas recientes:"

msgid "layout.index.header.nav.search"
msgstr "Buscar"

msgid "layout.index.header.nav.faq"
msgstr "FAQ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Mejorar metadatos"

msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariado y Recompensas"

msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

msgid "layout.index.header.nav.activity"
msgstr "Actividad"

msgid "layout.index.header.nav.codes"
msgstr "Explorador de Códigos"

msgid "layout.index.header.nav.llm_data"
msgstr "Datos de LLM"

msgid "layout.index.header.nav.home"
msgstr "Inicio"

msgid "layout.index.header.nav.annassoftware"
msgstr "El Software de Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traducir ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Iniciar sesión / Registrarse"

msgid "layout.index.header.nav.account"
msgstr "Cuenta"

msgid "layout.index.footer.list1.header"
msgstr "El Archivo de Anna"

msgid "layout.index.footer.list2.header"
msgstr "Mantente en contacto"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / reclamaciones de derechos de autor"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Avanzado"

msgid "layout.index.header.nav.security"
msgstr "Seguridad"

msgid "layout.index.footer.list3.header"
msgstr "Alternativas"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "no afiliado"

msgid "page.search.results.issues"
msgstr "❌ Este archivo puede tener problemas."

msgid "page.search.results.fast_download"
msgstr "Descarga rápida"

msgid "page.donate.copy"
msgstr "copiar"

msgid "page.donate.copied"
msgstr "copiado!"

msgid "page.search.pagination.prev"
msgstr "Previo"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Próximo"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Espejo #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% del patrimonio escrito de la humanidad preservado para siempre %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datasets ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Descargar desde:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Nosotros tenemos multiples opciones de descargas en caso de que una de ellas esté fuera de Servicio. Todos ellos tienen exactamente el mismo archivo."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Recuerda que el Archivo de Anna no aloja ninguno de los contenidos de esta página. Nos limitamos a enlazar con páginas web ajenas. Si crees que tienes una reclamación válida en virtud de la DMCA, consulta la página %(about_link)sAcerca de</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Espejo anónimo Z-Library #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Dona aquí"

#~ msgid "page.donate.header"
#~ msgstr "Dona aquí"

#~ msgid "page.donate.text1"
#~ msgstr "Anna’s Archive es un proyecto de código abierto sin ánimo de lucro, gestionado íntegramente por voluntarios. Aceptamos donaciones para cubrir nuestros costes, que incluyen alojamiento, nombres de dominio, desarrollo y otros gastos."

#~ msgid "page.donate.text2"
#~ msgstr "Con sus contribuciones podemos mantener este sitio en funcionamiento, mejorar sus funciones y preservar más colecciones."

#~ msgid "page.donate.text3"
#~ msgstr "Donaciones recientes: %(donations)s. Gracias a todos por vuestra generosidad. Apreciamos de verdad que depositéis vuestra confianza en nosotros, con cualquier cantidad de la que podáis disponer."

#~ msgid "page.donate.text4"
#~ msgstr "Para donar, selecciona el método abajo. Si tienes algún problema, contáctanos por email en %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Tarjeta de crédito/débito"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Cripto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Preguntas"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Ve a %(link_open_tag)sesta página</a> y sigue las instrucciones, ya sea escaneando el código QR o haciendo clic en el enlace \"paypal.me\". Si no funciona, intenta actualizar la página, ya que eso podría darle una cuenta diferente."

#~ msgid "page.donate.cc.header"
#~ msgstr "Tarjeta de crédito/débito"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Utilizamos Sendwyre para depositar dinero directamente en nuestro monedero Bitcoin (BTC). Tarda unos 5 minutos en completarse."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Este método tiene una cantidad mínima por transacción de $30, y un costo de alrededor de $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Pasos:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copia nuestra \"Bitcoin (BTC) wallet address\" : %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Ve a %(link_open_tag)sthis page</a> y haz click en \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Pega nuestra \"wallet address\", y sigue las instrucciones"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Cripto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(también funciona para BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Utilice esta %(link_open_tag)scuenta de Alipay</a> para enviar su donación. Si no funciona, intente actualizar la página, ya que eso podría generarle una cuenta diferente."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Esta opción de donación no esta actualmente disponible. Por favor, inténtelo mas tarde. Aun constante, ¡agradecemos mucho su interés por aportar una donación!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Por favor use %(link_open_tag)sesta página de Pix</a> para enviar su donación. Si no funciona, intente refrescar la página, pues tal vez eso le dará una cuenta diferente."

#~ msgid "page.donate.faq.header"
#~ msgstr "Preguntas frecuentes"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> es un proyecto que pretende catalogar todos los libros existentes, agregando datos de diversas fuentes. También hacemos un seguimiento del progreso de la humanidad para hacer que todos estos libros estén fácilmente disponibles en forma digital, a través de“<a href=\"https://es.wikipedia.org/wiki/Biblioteca_fantasma\">bibliotecas fantasmas</a>”. Más información <a href=\"/about\">sobre nosotros.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr "Membresía: <strong>%s(tier_name)</strong> hasta %(until_date) <a %(a_extend)s>(extend)</a>"

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr "🚀 Descargas rápidas de nuestros compañeros (se necesita <a %(a_login)s>iniciar sesión</a>)"

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr "🚀 Descargas rápidas (has iniciado sesión!)"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Libro (cualquiera)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Inicio"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datasets ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "No encontrado"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” no es un número ISBN válido. Los ISBN tienen 10 o 13 caracteres, sin contar los guiones opcionales. Todos los caracteres deben ser números, excepto el último carácter, que también puede ser \"X\". El último carácter es el \"dígito de control\", que debe coincidir con un valor de suma de comprobación que se calcula a partir de los otros números.También debe estar en un rango válido, asignado por la Agencia Internacional del ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "Archivos coincidentes en nuestra base de datos:"

#~ msgid "page.isbn.results.none"
#~ msgstr "No se han encontrado archivos coincidentes en nuestra base de datos."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Buscar ▶ %(num)d+ resultados para <span class=\"italic\">%(search_input)s</span> (en los metadatos de la biblioteca oculta)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Buscar ▶ %(num)d resultados para <span class=\"italic\">%(search_input)s</span> (en los metadatos de la biblioteca oculta)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Buscar ▶ Error de búsqueda de <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Buscar ▶ Nueva búsqueda"

#~ msgid "page.donate.header.text3"
#~ msgstr "También puedes hacer una donacion sin crear una cuenta (Todos los métodos de pago son compatibles con donaciones y membresías):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Donación única (sin beneficios)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Seleccione una opción de pago. Considere utilizar un pago basado en criptomonedas %(bitcoin_icon)s, porque incurrimos en menores comisiones."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Si ya tienes criptomonedas, estas son nuestras direcciones."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "¡Muchas gracias por ayudar! Este proyecto no sería posible sin vosotros."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Para donar con PayPal, usamos PayPal Crypto, que nos permite seguir siendo anónimos. Te agradecemos para tomar el tiempo para aprender a donar con este método, nos ayuda mucho."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Sigue las instrucciones para comprar Bitcoin (BTC). Solo necesitas comprar lo que quieres donar."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Si incurre en pérdidas de Bitcoin por fluctuaciones o tasas, <em>no se preocupe</em>. Es un comportamiento esperado para criptomonedas que nos permite actuar anónimamente."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Anota nuestra dirección de Bitcoin (BTC) como el receptor, y sigue las instrucciones para enviar tu donación:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Por favor usa <a %(a_account)s>esta cuenta de Alipay</a> para enviar tu donación."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Por favor usa <a %(a_account)s>esta cuenta de Pix</a> para enviar tu donación."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Si tu método de pago no está en la lista, lo más fácil sería descargar <a href=\"https://paypal.com/\">PayPal</a> o <a href=\"https://coinbase .com/\">Coinbase</a> en tu teléfono y compra allí un poco de Bitcoin (BTC). Luego podrás enviarlo a nuestra dirección: %(address)s. En la mayoría de los países, la configuración debería llevar sólo unos minutos."

#~ msgid "page.search.results.error.text"
#~ msgstr "Intente <a href=\"javascript:location.reload()\">recargando la pagina</a>. Si el problema persiste, infórmenos en <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Para convertirte en miembro, por favor <a href=\"/login\">Inicia sesión o regístrate</a>. Si prefieres no crear una cuenta, selecciona la opción de \"Hacer una donación anónima única\". ¡Gracias por el apoyo!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Inicio"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Acerca de"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Dona aquí"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datasets"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Aplicación movil"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traducir"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr "%(count)s torrents"

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;copias del contenido de %(libraries)s, y más."

#~ msgid "page.home.preservation.text"
#~ msgstr "Preservamos libros, artículos, cómics, revistas, y más, reuniendo estos materiales de varias <a href=\"https://es.wikipedia.org/wiki/Biblioteca_fantasma\">bibliotecas fantasma</a> en un solo lugar. Todos estos datos son preservados para siempre haciéndolo fácil de copiar en masa, que resuelta en muchas copias en todo el mundo. Esta distribución extensa, combinada con código abierto, también hace nuestro sitio web resiliente contra las peticiones de eliminación de datos. Aprende más sobre <a href=\"/datasets\">nuestros datasets</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datasets ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "No encontrado"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" no parece un DOI. Debería empezar con un \"10.” y contener una barra oblicua (/)."

#~ msgid "page.doi.box.header"
#~ msgstr "%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL Canónica: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Este archivo puede estar en %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Archivos coincidentes en nuestra base de datos:"

#~ msgid "page.doi.results.none"
#~ msgstr "No se encontraron archivos coincidentes en nuestra base de datos."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Descarga rápida</strong> Has agotado tus descargas rápidas de hoy. Por favor, contacta con Anna en %(email)s si estás interesado en mejorar tu suscripción."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Has agotado tus descargas rápidas diarias. Contacta con Anna mediante %(email)s si estás interesado en mejorar tu membresía."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>¿Puedo contribuir de otra manera?</div> ¡Sí! Ver el <a href=\"/about\">acerca de la página</a> en \"Cómo ayudar”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>¡No me gusta que “moneticéis” El Archivo de Anna!</div> Si no te gusta cómo operamos nuestro proyecto, ve y opera tu propia biblioteca en la sombra! Todo nuestro código y datos están abiertos al público, así que nada te lo impide. ;)"

#~ msgid "page.request.title"
#~ msgstr "Pedir libros"

#~ msgid "page.request.text1"
#~ msgstr "Por ahora, ¿podéis pedir los libros en el <a %(a_forum)s>foro de Libgen.rs</a>? Puedes crear una cuenta allí y publicar en uno de estos hilos:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Para libros electrónicos, usa <a %(a_ebook)s>este hilo</a>.</li><li %(li_item)s>Para libros que no estén disponibles en formato digital, usa <a %(a_regular)s>este hilo</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "En ambos casos, asegúrate de seguir las reglas mencionadas en los hilos."

#~ msgid "page.upload.title"
#~ msgstr "Subir"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Subidas grandes"

#~ msgid "page.about.title"
#~ msgstr "Acerca de"

#~ msgid "page.about.header"
#~ msgstr "Acerca de"

#~ msgid "page.home.search.header"
#~ msgstr "Buscar"

#~ msgid "page.home.search.intro"
#~ msgstr "Busca en nuestro catálogo."

#~ msgid "page.home.random_book.header"
#~ msgstr "Libro aleatorio"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Ir a un libro aleatorio del catálogo."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Libro aleatorio"

#~ msgid "page.about.text1"
#~ msgstr "El Archivo de Anna es un motor de búsqueda de código abierto sin fines de lucro para “<a href=\"https://es.wikipedia.org/wiki/Biblioteca_fantasma\">bibliotecas fantasma</a>. Fue creado por <a href=\"http://annas-blog.org\">Anna</a>, que sentía que había una necesidad de un lugar central para buscar libros, artículos, cómics, revistas y otros documentos."

#~ msgid "page.about.text4"
#~ msgstr "Si tienes un reclamo válido del DMCA, ve a la parte de abajo de esta página o contáctanos a través de %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Explorar libros"

#~ msgid "page.home.explore.intro"
#~ msgstr "Esta es una combinación de libros populares y libros que tienen un significado especial para el mundo de las bibliotecas fantasmas y la preservación digital."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat no oficial"

#~ msgid "page.wechat.body"
#~ msgstr "Tenemos una página de WeChat no oficial, mantenida por un miembro de la comunidad. Utiliza el siguiente código para acceder."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Acerca de"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Aplicación móvil"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "WeChat no oficial"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Solicitar libros"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Subir"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Referir a amigos"

#~ msgid "page.about.help.header"
#~ msgstr "Cómo ayudar"

#~ msgid "page.refer.title"
#~ msgstr "Refiere a amigos para obtener descargas rápidas adicionales"

#~ msgid "page.refer.section1.intro"
#~ msgstr "Los miembros pueden referir a amigos y ganar descargas adicionales."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Por cada amigo que se haga miembro:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Ellos</strong> obtienen un %(percentage)s%% de descargas adicionales además de las descargas diarias, durante el periodo de su membresía."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Tú</strong> obtienes el mismo número de descargas adicionales además de tus descargas diarias habituales, durante el periodo de membresía de tu amigo (hasta un total de %(max)s descargas adicionales en cualquier momento). Debes seguir siendo miembro para usar tus descargas adicionales."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Ejemplo:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Tu amigo usa tu enlace de referido para activar la membresía de \"Bibliotecario Afortunado\" por 3 meses, que viene con %(num)s descargas rápidas."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Él obtiene %(num)s descargas adicionales cada día durante esos 3 meses."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Tú también recibes %(num)s descargas adicionales cada día durante los mismos 3 meses."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Enlace de referido:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Inicia sesión</a> y hazte miembro para referir a amigos."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Hazte miembro</a> para referir a amigos."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "O añade %(referral_suffix)s al final de cualquier enlace, y el referid se recordará cuando se haga miembro."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Dona la cantidad total de %(total)s usando <a %(a_account)s>esta cuenta de Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "También puedes subirlos a Z-Library <a %(a_upload)s>aquí</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Para aumentar la resiliencia del Archivo de Anna, buscamos voluntarios para crear reflejos <a href=\"/mirrors\">Más información…</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Reflejos: se buscan voluntarios"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "solo este mes!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ha <a %(a_closed)s>pausado</a> la carga de nuevos artículos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selecciona un método de pago. Ofrecemos descuentos al pagar con criptomonedas %(bitcoin_icon)s porque incurrimos en (muchas) menos tarifas."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selecciona una opción de pago. Actualmente solo aceptamos pagos con criptomonedas %(bitcoin_icon)s, ya que los procesadores de pago tradicionales se niegan a trabajar con nosotros."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "No podemos admitir tarjetas de crédito/débito directamente porque los bancos no quieren trabajar con nosotros. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Sin embargo, existen varias formas de utilizar tarjetas de crédito/débito, utilizando nuestros otros métodos de pago:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Descargas lentas y externas"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Descargas"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si estás usando criptomonedas por primera vez, te sugerimos usar %(option1)s, %(option2)s o %(option3)s para comprar y donar Bitcoin (la criptomoneda original y más utilizada)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 enlaces de registros que mejoraste."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 enlaces o capturas de pantalla."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 enlaces o capturas de pantalla de solicitudes que cumpliste."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si está interesado en reflejar estos Datasets para <a %(a_faq)s>archivos</a> o <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si está interesado en replicar este conjunto de datos para <a %(a_archival)s>archivos</a> o <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sitio web principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Información del país del ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si está interesado en reflejar este conjunto de datos para propósitos de <a %(a_archival)s>archivo</a> o <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "La Agencia Internacional del ISBN publica regularmente los rangos que ha asignado a las agencias nacionales del ISBN. A partir de esto, podemos deducir a qué país, región o grupo de idiomas pertenece este ISBN. Actualmente utilizamos estos datos de manera indirecta, a través de la biblioteca de Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Recursos"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Última actualización: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sitio web del ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadatos"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluyendo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Nuestra inspiración para recolectar metadatos fue el objetivo de Aaron Swartz en \"una página web por cada libro jamás publicada\", para el cual creó <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "A ese proyecto le ha ido bien, pero nuestra posición única nos permite obtener metadatos que ellos no pueden."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Otra inspiración fue el deseo de saber <a %(a_blog)s>cuántos libros hay en el mundo</a>, para poder calcular cuántos libros aún nos quedan por guardar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Para dar a todos la oportunidad de descargar archivos de forma gratuita, necesitas esperar <strong>%(wait_seconds)s segundos</strong> antes de poder descargar este archivo."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Actualizar la página automáticamente. Si pierdes la ventana de descarga, el temporizador se reiniciará, por lo que se recomienda la actualización automática."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Descargar ahora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Convertir: use herramientas en línea para convertir entre formatos. Por ejemplo, para convertir entre epub y pdf, use <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: descargue el archivo (se admiten pdf o epub), luego <a %(a_kindle)s>envíelo a Kindle</a> usando la web, la aplicación o el correo electrónico. Herramientas útiles: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Apoya a los autores: Si te gusta y puedes costearlo, considera comprar el original, o apoya directamente a los autores."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Apoya a las bibliotecas: Si esto está disponible en tu biblioteca local, considera pedirlo prestado gratis allí."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s No disponible directamente en masa, solo en semi-masa detrás de un muro de pago"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s El Archivo de Anna gestiona una colección de <a %(isbndb)s>metadatos de ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb es una empresa que extrae datos de varias librerías en línea para encontrar metadatos de ISBN. El Archivo de Anna ha estado haciendo copias de seguridad de los metadatos de libros de ISBNdb. Estos metadatos están disponibles a través del Archivo de Anna (aunque actualmente no en la búsqueda, excepto si busca explícitamente un número de ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Para detalles técnicos, vea abajo. En algún momento podemos usarlo para determinar qué libros aún faltan en las bibliotecas en la sombra, con el fin de priorizar qué libros encontrar y/o escanear."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nuestra publicación en el blog sobre estos datos"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Extracción de ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actualmente tenemos un solo torrent, que contiene un archivo de 4.4GB comprimido en formato <a %(a_jsonl)s>JSON Lines</a> (20GB descomprimido): “isbndb_2022_09.jsonl.gz”. Para importar un archivo “.jsonl” en PostgreSQL, puede usar algo como <a %(a_script)s>este script</a>. Incluso puede canalizarlo directamente usando algo como %(example_code)s para que se descomprima sobre la marcha."

#~ msgid "page.donate.wait"
#~ msgstr "Por favor, espera al menos <span %(span_hours)s>dos horas</span> (y recarga la página) antes de contactarnos."

#~ msgid "page.codes.search_archive"
#~ msgstr "Buscar en el Archivo de Anna por “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Dona usando Alipay o WeChat. Puedes elegir entre estos en la página siguiente."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Difundir la palabra sobre el Archivo de Anna en redes sociales y foros en línea, recomendando libros o listas en AA, o respondiendo preguntas."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La colección de ficción ha divergido pero aún tiene <a %(libgenli)s>torrents</a>, aunque no se ha actualizado desde 2022 (tenemos descargas directas)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s El Archivo de Anna y Libgen.li gestionan colaborativamente colecciones de <a %(comics)s>cómics</a> y <a %(magazines)s>revistas</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s No hay torrents para colecciones de ficción rusa y documentos estándar."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "No hay torrents disponibles para el contenido adicional. Los torrents que están en el sitio web de Libgen.li son espejos de otros torrents listados aquí. La única excepción son los torrents de ficción que comienzan en %(fiction_starting_point)s. Los torrents de cómics y revistas se publican como una colaboración entre el Archivo de Anna y Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "De una colección <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origen exacto desconocido. Parte de the-eye.eu, parte de otras fuentes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

