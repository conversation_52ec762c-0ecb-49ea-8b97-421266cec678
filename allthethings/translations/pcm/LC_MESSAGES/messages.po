#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Request no correct. Visit %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " and "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "and plenti more"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;We dey mirror %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "We dey scrape and open-source %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "All our code and data dey completely open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Di largest truly open library for human history."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;books, %(paper_count)s&nbsp;papers — dem go dey preserve forever."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Di world’s largest open-source open-data library. ⭐️&nbsp;Mirrors Sci-Hub, Library Genesis, Z-Library, and more. 📈&nbsp;%(book_any)s books, %(journal_article)s papers, %(book_comic)s comics, %(magazine)s magazines — dem go dey preserve forever."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Di world’s largest open-source open-data library.<br>⭐️ Mirrors Scihub, Libgen, Zlib, and more."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Wrong metadata (e.g. title, description, cover image)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Download wahala (e.g. no fit connect, error message, very slow)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "File no fit open (e.g. corrupted file, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Poor quality (e.g. formatting issues, poor scan quality, missing pages)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / file suppose comot (e.g. advertising, abusive content)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Copyright claim"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Other"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonus downloads"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brilliant Bookworm"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Lucky Librarian"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Dazzling Datahoarder"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Amazing Archivist"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "neva pay"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "don pay"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "cansul"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "don finish"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "de wait make Anna confirm"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "no correct"

#, fuzzy
msgid "page.donate.title"
msgstr "Donate"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "You get one <a %(a_donation)s>donation wey you don start</a>. Abeg finish or cansul am before you make new donation."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>See all my donations</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Anna’s Archive na non-profit, open-source, open-data project. If you donate and become member, you go help our operations and development. To all our members: thank you for keeping us going! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "For more information, check out the <a %(a_donate)s>Donation FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "To get even more downloads, <a %(a_refer)s>refer your friends</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "You get %(percentage)s%% bonus fast downloads, because you were referred by user %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "This one dey apply for the whole membership period."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s fast downloads per day"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "if you donate dis month!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / month"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Join"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selected"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "up to %(percentage)s%% discounts"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB papas <strong>unlimited</strong> witout verification"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> access"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Earn <strong>%(percentage)s%% bonus downloads</strong> by <a %(a_refer)s>referring friends</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Your username or anonymous mention for di credits"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Previous perks, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Early access to new features"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Exclusive Telegram wit behind-the-scenes updates"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adopt a torrent”: your username or message for torrent filename <div %(div_months)s>once every 12 months of membership</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legendary status for preservation of humanity’s knowledge and culture"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Expert Access"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contact us"

#, fuzzy
msgid "page.donate.small_team"
msgstr "We be small team of volunteers. E fit take us 1-2 weeks to respond."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Unlimited</strong> high-speed access"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direct <strong>SFTP</strong> servers"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "We welcome big donations from rich people or institutions. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "For donations wey pass $5000 abeg contact us directly for %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Make you sabi se even though di membership for dis page na “per month”, dem be one-time donation (dem no dey repeat). See di <a %(faq)s>Donation FAQ</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "If yu wan dash us moni (any amount) witout membership, yu fit use dis Monero (XMR) address: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Abeg chuz payment method."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(for now e no dey available)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s gift card"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bank card (wey dey use app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Credit/debit card"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regula)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kard / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit/debit/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bank card"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Credit/debit card (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Credit/debit card 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binans"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Wit crypto yu fit dash us moni using BTC, ETH, XMR, and SOL. Use dis option if yu sabi cryptocurrency well well."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Wit crypto yu fit dash us moni using BTC, ETH, XMR, and more."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "If na di first time wey you dey use crypto, we suggest make you use %(options)s to buy and donate Bitcoin (di original and most used cryptocurrency)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binans"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "To donate using PayPal US, we go use PayPal Crypto, wey go allow us remain anonymous. We appreciate say you take time to learn how to donate using dis method, since e go help us well well."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donate using PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donate wit Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "If yu get Cash App, dis na di easiest way to donate!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Note say for transactions wey dey under %(amount)s, Cash App fit charge %(fee)s fee. For %(amount)s or above, e dey free!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donate wit Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "If yu get Revolut, dis na di easiest way to donate!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donate wit credit or debit card."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay and Apple Pay fit work too."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Note say for small donations di credit card fees fit cancel our %(discount)s%% discount, so we recommend longer subscriptions."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Note say for small donations di fees dey high, so we recommend longer subscriptions."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Wit Binance, yu fit buy Bitcoin wit credit/debit card or bank account, and then donate dat Bitcoin to us. Dat way we fit remain secure and anonymous wen we dey accept your donation."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance dey available for almost every country, and e support most banks and credit/debit cards. Dis na our main recommendation for now. We appreciate say yu take time to learn how to donate using dis method, as e dey help us well well."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donate wit yu regula PayPal account."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donate wit credit/debit card, PayPal, or Venmo. Yu fit choose between dem for di next page."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donate wit Amazon gift card."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Note say we need to round to amounts wey our resellers dey accept (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> We only support Amazon.com, not other Amazon websites. For example, .de, .co.uk, .ca, no dey supported."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANT:</strong> Dis op-shon na for %(amazon)s. If yu wan' use anoda Amazon website, selek am abov."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Dis method dey use cryptocurrency provider as intermediate conversion. E fit dey a bit confusing, so abeg only use dis method if other payment methods no work. E no dey work for all countries too."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donate wit credit/debit card, through the Alipay app (e easy wella to set up)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Install Alipay app"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Instol di Alipay app from di <a %(a_app_store)s>Apple App Store</a> or <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Regista wit your phone nombar."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "No oda pesonal detials dey required."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Add bank card"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supported: Visa, MasterCard, JCB, Diners Club and Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "See <a %(a_alipay)s>dis guide</a> for more informeshon."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "We no fit support kredit/debit kards directly, because banks no wan work wit us. ☹ But, dia dey plenty ways to use kredit/debit kards anyhow, using oda payment methods:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Gift Card"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Send us Amazon.com gift cards using your credit/debit card."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay dey support international kredit/debit kards. See <a %(a_alipay)s>dis guide</a> for more information."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) dey support international credit/debit cards. For di WeChat app, go to “Me => Services => Wallet => Add a Card”. If yu no see am, enable am using “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "You fit buy crypto wit credit/debit cards."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Crypto express services"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Express services dey convenient, but dem dey charge higher fees."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "You fit use dis instead of a crypto exchange if you wan quickly make bigger donation and you no mind fee of $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Make sure say you send di exact crypto amount wey dey show for di donation page, no be di amount for $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Otherwise di fee go dey subtracted and we no fit automatically process your membership."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s depending on country, no verification for first transaction)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, no verification for first transaction)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, no verification for first transaction)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "If any of dis informéshon don old, abeg send us email mek we sabi."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "For credit cards, debit cards, Apple Pay, and Google Pay, we dey use “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). For dia system, one “coffee” na $5, so your donation go round up to di nearest multiple of 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Select how long you wan subscribe for."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 month"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 months"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 months"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 months"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 months"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 months"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 months"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>after <span %(span_discount)s></span> discounts</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Dis payment method need minimum of %(amount)s. Abeg select different duration or payment method."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donate"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Dis payment method only allow maximum of %(amount)s. Abeg select different duration or payment method."

#, fuzzy
msgid "page.donate.login2"
msgstr "To become member, abeg <a %(a_login)s>Log in or Register</a>. Thanks for your support!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Select your preferred crypto coin:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(lowest minimum amount)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(Use am wen pesin wan send Ethereum from Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(warning: high minimum amount)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Click di donate button to confirm dis donation."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donate <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "You fit still cancel di donation during checkout."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ We dey redirect you to di donation page…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Somtin go wrong. Abeg reload di page and try again."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / month"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "for 1 month"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "for 3 months"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "for 6 months"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "for 12 months"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "for 24 months"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "for 48 months"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "for 96 months"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "for 1 month “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "for 3 months “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "for 6 months “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "for 12 months “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "for 24 months “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "for 48 months “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "for 96 months “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donation"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Date: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months, including %(discounts)s%% discount)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifier: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Kansul"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Yu sure sey yu wan kansul? No kansul if yu don pay alredi."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Yes, abeg kansul am"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Yu don kansul yu donéshon."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Mek new donéshon"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Somtin don go rong. Abeg reload di page and try again."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reorder"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Yu don pay alredi. If yu wan review di payment instrukshon again, klik hia:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Show old payment instrukshon"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Tank yu for yu donéshon!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "If yu neva do am, write down yu secret key for log in:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "If not, yu fit lock out from dis akount!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Di payment instrukshon don old. If yu wan mek anoda donéshon, use di “Reorder” button wey dey up."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Important note:</strong> Crypto price fit change anyhow, sometimes even reach 20%% for few minutes. Dis still better pass di fees wey we dey pay many payment providers, wey dey charge 50-60%% to work with “shadow charity” like us. <u>If yu send us di receipt with di original price wey yu pay, we go still credit yu akount for di membership wey yu choose</u> (as long as di receipt no old pass few hours). We really appreciate sey yu fit manage dis kind wahala to support us! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Dis donéshon don expire. Abeg kansul am and create new one."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Krypto instrɔkshɔn"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transfa go wan of our krypto akant"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donet di totol amɔunt of %(total)s to wan of dis adrese:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Bay Bitcoin for Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Fain di “Krypto” pej for PayPal app or website. Dis dey ushualli unda “Finances”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Folo di instrɔkshɔn to bay Bitcoin (BTC). Yu onli nid to bay di amɔunt wey yu wan donet, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfa di Bitcoin to our adrese"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Go to di “Bitcoin” pej for PayPal app or website. Pres di “Transfa” bɔtin %(transfer_icon)s, and den “Send”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Enta our Bitcoin (BTC) adrese as di risipient, and folo di instrɔkshɔn to send yu doneshɔn of %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Kredit / debit kad instrɔkshɔn"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donet tru our kredit / debit kad pej"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donet %(amount)s on <a %(a_page)s>dis pej</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Si di step-bai-step gaid bɛlo."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Wetin for konfirmeshɔn (rɛfresh di pej to chɛk)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Wetin for transfa (rɛfresh di pej to chɛk)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Taim lɛft:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(yu fit wan kansul and kriɛt a nyu doneshɔn)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "To risɛt di taim, simplli kriɛt a nyu doneshɔn."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Update status"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "If you run into any issues, please contact us at %(email)s and include as much information as possible (such as screenshots)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "If yu don pay alredi:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Sometime, e fit tek up to 24 hours for confirmation to show, so make sure say you refresh dis page (even if e don expire)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Buy PYUSD coin on PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Follow the instructions to buy PYUSD coin (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Buy small more (we recommend %(more)s more) than di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Go to di “PYUSD” page for your PayPal app or website. Press di “Transfer” button %(icon)s, and then “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transfer %(amount)s to %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Buy Bitcoin (BTC) for Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Go di “Bitcoin” (BTC) page for Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Buy small more (we recommend %(more)s more) pass di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfer di Bitcoin to our address"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Click di “Send bitcoin” button to make “withdrawal”. Switch from dollars to BTC by pressing di %(icon)s icon. Enter di BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "For small donations (under $25), you fit need use Rush or Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Buy Bitcoin (BTC) for Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Go di “Crypto” page for Revolut to buy Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Buy small more (we recommend %(more)s more) pass di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfer di Bitcoin to our address"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Click di “Send bitcoin” button to make “withdrawal”. Switch from euros to BTC by pressing di %(icon)s icon. Enter di BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Mek sure se na di BTC amount wey dey below, <em>NO BE</em> euros or dollars, otherwise we no go fit receive di correct amount and we no go fit confirm your membership automatically."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "For small donations (under $25) you fit need use Rush or Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Use any of di following “credit card to Bitcoin” express services, wey only take few minutes:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Fill di follow detéls for di form:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin amount:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Abeg use dis <span %(underline)s>exact amount</span>. Your total cost fit high pass because of credit card fees. For small amounts, dis fit pass our discount, unfortunately."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin address (external wallet):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instructions"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "We only support di standard version of crypto coins, no exotic networks or versions of coins. E fit take up to one hour to confirm di transaction, depending on di coin."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR Code to Pay"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan this QR code with your crypto wallet app to quickly fill in the payment details"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon gift card"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Abeg use di <a %(a_form)s>official Amazon.com form</a> to send us gift card of %(amount)s to di email address below."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "We no fit accept other methods of gift cards, <strong>only sent directly from di official form on Amazon.com</strong>. We no fit return your gift card if you no use this form."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Enta di exact amount: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Abeg no write your own message."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“To” recipient email for di form:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unique to your account, no share am."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Use am only once."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Dey wait for gift card… (refresh di page to check)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "After you send your gift card, our automated system go confirm am within few minutes. If e no work, try resend your gift card (<a %(a_instr)s>instructions</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "If e still no work, abeg email us and Anna go manually review am (e fit take few days), and make sure say you mention if you don try resend am already."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Example:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Note say di account name or picture fit look strange. No need to worry! These accounts dey managed by our donation partners. Our accounts no dey hacked."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay instrɔkshọn"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donet wit Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis Alipay akɔunt</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "If di donation page block, try use different internet connection (e.g. VPN or phone internet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Sɔri, di Alipay pej di ɔnli de aksesibul fɔ <strong>mainland China</strong>. Yu fit nid to tɛmprari disebul yɔ VPN, ɔ yus VPN we go konekt to mainland China (ɔ Hong Kong fit wɔrk somtaim)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Make donation (scan QR code or press button)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Open di <a %(a_href)s>QR-code donation page</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scan di QR code wit di Alipay app, or press di button to open di Alipay app."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Abeg make you get patience; di page fit take small time to load because e dey China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat instrɔkshọn"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donet wit WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis WeChat akɔunt</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix instrɔkshọn"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donet wit Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis Pix akɔunt"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Imel us di risit"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Send receipt or screenshot to your personal verification address. No use dis email address for your PayPal donation."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Send risit ɔ screenshot to yɔ pɛsɔnɔl verifikeshọn adres:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "If di kripto ekschej reit fluktuet durin di transakshọn, mek shɔr se yu inklud di risit we de shɔw di orijinal ekschej reit. We rili aprishiet se yu tek di trɔbul to yus kripto, e help us wel-wel!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Wen yu don imel yɔ risit, klik dis bọtịn, mek Anna fit manwali riviw am (dis fit tek smɔl deys):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Yes, a don imel mi risit"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Tenk yu fɔ yɔ doneshọn! Anna go manwali aktivet yɔ membaship witin smɔl deys."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Sọmtin go rɔng. Abeg rilod di pej an trai agen."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Step-bai-step gaid"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Sọm of di steps mɛntshọn kripto wɔlɛts, bọt no wori, yu nɔ nid to lɛn enitin abaut kripto fɔ dis."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Put ya email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Choose ya payment method."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Choose ya payment method again."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Select “Self-hosted” wallet."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Click “I confirm ownership”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. You go receive email receipt. Abeg send am to us, and we go confirm your donation as soon as possible."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Abeg wait at least <span %(span_hours)s>24 hours</span> (and refresh dis page) before you contact us."

#, fuzzy
msgid "page.donate.mistake"
msgstr "If you make mistake during payment, we no fit do refunds, but we go try make am right."

#, fuzzy
msgid "page.my_donations.title"
msgstr "My donations"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donations details no dey publicly show."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "No donations yet. <a %(a_donate)s>Make my first donation.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Make another donation."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Downloaded files"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Downloads from Fast Partner Servers dey marked by %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "If you download file with both fast and slow downloads, e go show up twice."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Fast downloads for the last 24 hours dey count towards the daily limit."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "All times dey for UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Downloaded files no dey publicly show."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "No files downloaded yet."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Last 18 hours"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Earlier"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Account"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Log in / Register"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Akàụnt ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Pọblik profaịl: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Sikret ki (nọ shéàm!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "shọw"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Mẹmbaship: <strong>%(tier_name)s</strong> ontíl %(until_date)s <a %(a_extend)s>(ẹkstẹnd)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Mẹmbaship: <strong>Nọn</strong> <a %(a_become)s>(bíkọm a mẹmbà)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Fast downloads yúzd (làst 24 àwàz): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "wich downloads?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Ẹksklúsív Telegram grúp: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Jọin ós hia!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Ọpgreid to a <a %(a_tier)s>hàia tia</a> to jọin ówa grúp."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kọntàkt Anna fọ %(email)s if yú dè intarẹstẹd in ọpgreidín yọ mẹmbaship to a hàia tia."

#, fuzzy
msgid "page.contact.title"
msgstr "Contact email"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Yú fít kọmbain múltípul mẹmbaships (fast downloads pèr 24 àwàz wíl bì àdẹd tógẹda)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Pọblik profaịl"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Downloadẹd fáils"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Mai donéshọns"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Logout"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Yú dè nàu lógd àut. Rílód di peij to lóg in agẹ́n."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Sọmtin wẹnt rọng. Plís rílód di peij ànd trai agẹ́n."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registration don successful! Your secret key na: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Keep dis key well well. If you lose am, you go lose access to your account."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Bookmark.</strong> You fit bookmark dis page to fit get your key back.</li><li %(li_item)s><strong>Download.</strong> Click <a %(a_download)s>dis link</a> to download your key.</li><li %(li_item)s><strong>Password manager.</strong> Use password manager to save the key when you enter am below.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Enter your secret key to log in:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Secret key"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Log in"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Invalid secret key. Check your key well and try again, or you fit register new account below."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "No lose your key!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Yu neva get akant?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Regista nyu akɔunt"

#, fuzzy
msgid "page.login.lost_key"
msgstr "If you lose your key, abeg <a %(a_contact)s>contact us</a> and provide as much information as you fit."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "You fit need create new account temporarily to contact us."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Old email-based account? Enter your <a %(a_open)s>email here</a>."

#, fuzzy
msgid "page.list.title"
msgstr "List"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "edit"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Save"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Saved. Abeg reload the page."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Something don go wrong. Abeg try again."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "List by %(by)s, created <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "List empty."

#, fuzzy
msgid "page.list.new_item"
msgstr "Add or remove from dis list by finding file and opening the “Lists” tab."

#, fuzzy
msgid "page.profile.title"
msgstr "Profile"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profile no dey."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "edit"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Change your display name. Your identifier (the part after “#”) no fit change."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Save"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ E don save. Abeg reload di page."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Somtin go wrong. Abeg try again."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profile don create <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Lists"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "No lists yet"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Create new list by finding file and opening di “Lists” tab."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Copyright reform dey necessary for national security"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Chinese LLMs (including DeepSeek) dey train on my illegal archive of books and papers — di largest for di world. Di West need to change copyright law as matter of national security."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Not too long ago, “shadow-libraries” dey die. Sci-Hub, di big illegal archive of academic papers, don stop to dey take new works, because of lawsuits. “Z-Library”, di largest illegal library of books, see say dem arrest di people wey dem say create am on criminal copyright charges. Dem manage escape di arrest, but their library still dey under threat."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "When Z-Library face shutdown, I don already back up di whole library and dey find platform to house am. Na im make me start Anna’s Archive: continuation of di mission behind those earlier initiatives. We don grow to be di largest shadow library for di world, wey dey host more than 140 million copyrighted texts across different formats — books, academic papers, magazines, newspapers, and beyond."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "My team and I na ideologues. We believe say to preserve and host these files dey morally right. Libraries around di world dey see funding cuts, and we no fit trust humanity’s heritage to corporations either."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Den AI come. Almost all major companies wey dey build LLMs contact us to train on our data. Most (but not all!) US-based companies change their mind once dem realize di illegal nature of our work. But Chinese firms dey happy to use our collection, dem no dey worry about di legality. Dis dey notable given say China dey part of almost all major international copyright treaties."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "We don give high-speed access to about 30 companies. Most of dem na LLM companies, and some na data brokers, wey go resell our collection. Most na Chinese, but we don also work wit companies from di US, Europe, Russia, South Korea, and Japan. DeepSeek <a %(arxiv)s>admit</a> say one earlier version train on part of our collection, but dem no wan talk about their latest model (probably also train on our data though)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "If di West wan stay ahead for di race of LLMs, and ultimately, AGI, dem need to rethink their position on copyright, and soon. Whether you agree with us or not on our moral case, dis don become matter of economics, and even national security. All power blocs dey build artificial super-scientists, super-hackers, and super-militaries. Freedom of information dey become matter of survival for these countries — even matter of national security."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Our team dey from all over di world, and we no get particular alignment. But we go encourage countries wey get strong copyright laws to use dis existential threat to reform dem. So wetin to do?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Our first recommendation dey straightforward: make dem shorten di copyright term. For di US, copyright dey granted for 70 years after di author’s death. Dis dey absurd. We fit bring dis in line wit patents, wey dem dey grant for 20 years after filing. Dis suppose dey enough time for authors of books, papers, music, art, and other creative works, to get fully compensated for their efforts (including longer-term projects such as movie adaptations)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Den, na di minimum, policymakers suppose include carve-outs for di mass-preservation an dissemination of texts. If di main worry na lost revenue from individual customers, personal-level distribution fit remain prohibited. In turn, those wey fit manage vast repositories — companies wey dey train LLMs, along with libraries an other archives — go dey covered by these exceptions."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Some countries don already dey do one version of dis. TorrentFreak <a %(torrentfreak)s>report</a> say China an Japan don introduce AI exceptions to dia copyright laws. E no clear to us how dis dey interact with international treaties, but e certainly dey give cover to dia domestic companies, wey explain wetin we don dey see."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "As for Anna’s Archive — we go continue our underground work wey dey rooted in moral conviction. But our greatest wish na to enter di light, an amplify our impact legally. Abeg reform copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Read di companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Winners of di $10,000 ISBN visualization bounty"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: We get some incredible submissions to di $10,000 ISBN visualization bounty."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "A few months ago we announce a <a %(all_isbns)s>$10,000 bounty</a> to make di best possible visualization of our data wey dey show di ISBN space. We emphasize showing which files we don archive already, an we later add a dataset wey dey describe how many libraries hold ISBNs (a measure of rarity)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "We don dey overwhelmed by di response. E get so much creativity. Big thank you to everyone wey participate: your energy an enthusiasm dey infectious!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Ultimately we wan answer di following questions: <strong>which books dey exist for di world, how many we don archive already, an which books we suppose focus on next?</strong> E dey great to see so many people care about these questions."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "We start with a basic visualization ourselves. In less than 300kb, dis picture succinctly represent di largest fully open “list of books” wey dem don ever assemble for di history of humanity:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "All ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Files in Anna’s Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC data leak"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russian State Library"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperial Library of Trantor"

#, fuzzy
msgid "common.back"
msgstr "Go Bak"

#, fuzzy
msgid "common.forward"
msgstr "Go Frɔnt"

#, fuzzy
msgid "common.last"
msgstr "Las"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Abeg chẹk di <a %(all_isbns)s>orijinal blog post</a> fọ mor infọmation."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Wi giv chalenj mek pipul impruv dis. Wi go giv fọst plẹs bounti of $6,000, sekọnd plẹs of $3,000, an tọd plẹs of $1,000. Bikọs pipul respond wel an di submisshọn dem dey wondaful, wi don desaid to increas di prais pool smọl, an giv fọ pipul tọd plẹs of $500 ich. Di winas dey bẹẹlo, bot mek shọr to luk all submisshọn dem <a %(annas_archive)s>hịa</a>, or download aw <a %(a_2025_01_isbn_visualization_files)s>kombined torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Fọst plẹs $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Dis <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>Gitlab comment</a>) na everytin we want, an even more! We especially like di incredibly flexible visualization options (even support custom shaders), but wit a comprehensive list of presets. We also like how fast an smooth everytin be, di simple implementation (wey no even get backend), di clever minimap, an extensive explanation for dia <a %(phiresky_github)s>blog post</a>. Incredible work, an di well-deserved winner!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Second place $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Anoda incredible <a %(annas_archive_note_2913)s>submission</a>. E no dey as flexible as di first place, but we actually prefer its macro-level visualization over di first place (space-filling curve, borders, labeling, highlighting, panning, an zooming). A <a %(annas_archive_note_2971)s>comment</a> by Joe Davis resonate wit us:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“While perfect squares an rectangles dey mathematically pleasing, dem no dey provide superior locality for mapping context. I believe say di asymmetry wey dey inside these Hilbert or classic Morton no be flaw but na feature. Just like Italy's famously boot-shaped outline make am instantly recognizable on a map, di unique \"quirks\" of these curves fit serve as cognitive landmarks. This distinctiveness fit enhance spatial memory an help users orient themselves, potentially making locating specific regions or noticing patterns easier.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "An still plenty options dey for visualizing an rendering, as well as an incredibly smooth an intuitive UI. A solid second place!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Third place $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "For dis <a %(annas_archive_note_2940)s>submission</a> we really like di different kinds of views, particularly di comparison an publisher views."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Third place $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Even though e no be di most polished UI, dis <a %(annas_archive_note_2917)s>submission</a> check plenty of di boxes. We particularly like its comparison feature."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Third place $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Like di first place, dis <a %(annas_archive_note_2975)s>submission</a> impress us wit its flexibility. Ultimately dis na wetin make for a great visualization tool: maximal flexibility for power users, while keeping things simple for average users."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Third place $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Di final <a %(annas_archive_note_2947)s>submission</a> to get a bounty dey pretty basic, but get some unique features wey we really like. We like how dem show how many datasets dey cover a particular ISBN as a measure of popularity/reliability. We also really like di simplicity but effectiveness of using an opacity slider for comparisons."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Notébul Aidiya Dem"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Som Aidiya an Wetin We Do Wey We Lík Wél Wél:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Skyscrapers fɔ Rérity"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Lív Stats"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotations, an Lív Stats"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Uník Map Viu an Filtas"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Kúl Dífɔlt Kolɔ Skim an Hítmap."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Ízi Tóglin ɔf Datasets fɔ Kwík Kɔmparisɔn."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Fain Lebuls."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skél Ba wit Nɔmba ɔf Buks."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Plénti Slídas fɔ Kɔmpar Datasets, laik sé yú bì DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Wi fít kɔntinyu fɔ sɔmtáim, bɔt mek wi stɔp hia. Mék shɔ sé yú chék ɔl di sɔbmíshɔn <a %(annas_archive)s>hia</a>, ɔr dɔnlód awá <a %(a_2025_01_isbn_visualization_files)s>kɔmbáind tɔrent</a>. Plénti sɔbmíshɔn, an ích wán bríng uník pɛrspéktiv, wéda na fɔ UI ɔr impliméntéshɔn."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Wi go at líst ínkɔrpɔrét di fés plés sɔbmíshɔn íntu awá mɛin wébsait, an prɔbabli sɔm ɔda wans. Wi dɔn stáat tɔ tink abaut háu tɔ ɔgánáiz di prɔsés ɔf aidentifáyin, kɔnfámin, an den ákaivin di rérést buks. Mɔ tɔ kɔm fɔ dis frɔnt."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Ténk yú evribɔdi wey pátísipét. Ít’s amézin sé plénti pipul kɛa."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Awá háts dɔn ful wit gratitúd."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizin All ISBNs — $10,000 bounti by 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dis picha reprẹsent di bigest ful open “list of buks” wey dem don gader fọ di histọri of pipul."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dis picture na 1000×800 pixels. Each pixel dey represent 2,500 ISBNs. If we get file for an ISBN, we go make that pixel more green. If we know say ISBN don dey issued, but we no get matching file, we go make am more red."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In less than 300kb, dis picture dey succinctly represent di largest fully open “list of books” wey dem don ever assemble for di history of humanity (a few hundred GB compressed in full)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "E also dey show: plenty work still dey to back up books (we only get 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Background"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hao Anna’s Archive fitachieve im mission of backing up all of humanity’s knowledge, witaut knowing which books dey still dey out dia? We need one TODO list. One way to map dis out na through ISBN numbers, wey since di 1970s dem don dey assign to every book wey dem publish (for most kontries)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "No central authority dey wey sabi all ISBN assignments. Instead, na distributed system, wey kontries dey get ranges of numbers, wey dem go assign smaller ranges to major publishers, wey fit still sub-divide ranges to minor publishers. Finally, dem dey assign individual numbers to books."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Wi start to map ISBNs <a %(blog)s>tu yiaz ago</a> wit aw skreyp of ISBNdb. Sins den, wi don skreyp plenti mor metadata sorsis, laik <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, an mor. Yu fit faind di ful list fọ di “Datasets” an “Torrents” pages fọ Anna’s Archive. Na wi get di bigest ful open, izi to download kolekshọn of buk metadata (an so ISBNs) fọ di wold."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "We don <a %(blog)s>write plenty</a> about why we care about preservation, and why we dey currently for critical time. We must now identify rare, underfocused, and uniquely at-risk books and preserve dem. Having good metadata on all books for di world go help with dat."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizing"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Apart from di overview image, we fit also look at individual Datasets we don acquire. Use di dropdown and buttons to switch between dem."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Plenty interesting patterns dey to see for these pictures. Why some regularity of lines and blocks dey, wey dey happen for different scales? Wetin be di empty areas? Why some Datasets dey so clustered? We go leave these questions as exercise for di reader."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 bounty"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Plenty dey to explore here, so we dey announce bounty for improving di visualization above. Unlike most of our bounties, dis one get time limit. You gatz <a %(annas_archive)s>submit</a> your open source code by 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Di best submission go get $6,000, second place na $3,000, and third place na $1,000. All bounties go dey awarded using Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Below na di minimal criteria. If no submission meet di criteria, we fit still award some bounties, but dat go dey at our discretion."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork dis repo, and edit dis blog post HTML (no other backends besides our Flask backend dey allowed)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Make di picture above smoothly zoomable, so you fit zoom all di way to individual ISBNs. Clicking ISBNs suppose carry you go metadata page or search on Anna’s Archive."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "You must still fit switch between all different Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Country ranges and publisher ranges suppose dey highlighted on hover. You fit use e.g. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> for country info, and our “isbngrp” scrape for publishers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "E must work well on desktop and mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "For bonus points (these na just ideas — make your creativity run wild):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Strong consideration go dey given to usability and how good e look."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Show actual metadata for individual ISBNs when you dey zoom in, like title and author."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Better space-filling curve. E.g. a zig-zag, going from 0 to 4 on di first row and then back (in reverse) from 5 to 9 on di second row — recursively applied."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Different or customizable color schemes."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Special views for comparing Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Wé wey yu fit yus fain out wétin dey rong, laik oda metadata wey no dey match well (e.g. title wey dey very different)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "To mark image wit comment for ISBN or range."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Any way wey fit help to sabi rare or book wey dey at-risk."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Any creative idea wey yu fit tink of!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Yu FIT change from di minimal criteria, and do somtin wey dey completely different. If e dey really spectacular, den e go qualify for di bounty, but na our decision."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Make yu submit by posting comment to <a %(annas_archive)s>dis issue</a> wit link to your forked repo, merge request, or diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Code"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Di kọd to jeneret dis imẹjiz, as wel as oda egzampls, yu fit faind dem fọ <a %(annas_archive)s>dis direktri</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Wi kam up wit a kompakt data fọmat, wey all di rikwaiad ISBN infọmation na about 75MB (kompresd). Di deskripshọn of di data fọmat an kọd to jeneret am yu fit faind <a %(annas_archive_l1244_1319)s>hịa</a>. Fọ di bounti yu no nid to yus dis, bot e fit bi di most konvinient fọmat to start wit. Yu fit transform aw metadata anyhow yu want (bot all yu kọd gatz bi open sors)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Wi no fit wait to si wetin yu go kam up wit. Gudlọk!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna’s Archive Kontena (AAC): mek we standardize di riliz we dey do from di world biggest shadow library"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’s Archive don become di biggest shadow library for di world, so we need to standardize di riliz we dey do."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna’s Archive</a> don become by far di biggest shadow library for di world, and di only shadow library of im scale wey dey fully open-source and open-data. Below na table from our Datasets page (small modification dey):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "We achieve dis in three ways:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Mirroring existing open-data shadow libraries (like Sci-Hub and Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Helping out shadow libraries wey wan dey more open, but no get di time or resources to do so (like di Libgen comics collection)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Scraping libraries wey no wan share in bulk (like Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "For (2) an (3) wi de manage big kolekshọn of torrents by awẹsef (100s of TBs). So far wi don de handle dis kolekshọn as wan-wan, wey mean se difrent infrastrọkchọ an data organizashọn for each kolekshọn. Dis de add big ovạhed to each riliz, an mek am hard to do mor incrementa riliz."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Na why wi desaid to standardaiz our riliz. Dis na teknikal blog post wey wi de introdius our standard: <strong>Anna’s Archive Containers</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Design goals"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Our main use case na di distribushọn of files an di metadata wey de wit dem from difrent kolekshọn wey de. Our most important tins wey wi de konsida na:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Difrent kain files an metadata, as dem de for di orijinal format as much as posibul."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Difrent kain aidentifayas for di sors laibraris, or even if aidentifayas no de."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separet riliz of metadata vs file data, or metadata-only riliz (e.g. our ISBNdb riliz)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribushọn tru torrents, but wit di posibulity of oda distribushọn methods (e.g. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Records wey no go change, sins wi go asium se our torrents go de liv foreva."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Incrementa riliz / appendable riliz."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Mashin-fit to read an rait, kwikly an konviniently, espeshaly for our stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Sọmtin wey pesin fit luk wit ai, but dis na sekondri to mashin readability."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Izi to sid our kolekshọn wit standard rented seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binary data fit de served direkly by webservers laik Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Sọmtin wey no be goal:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Wi no de konsan if files de izi to waka tru wit hand for disk, or seachable witaut preprocessing."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Wi no dey worry say we go fit work well well wit di library software wey dey already."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Even though e suppose dey easy for anybody to seed our collection using torrents, we no dey expect say di files go dey usable without better technical knowledge and commitment."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Since Anna’s Archive na open source, we wan use our format directly. When we dey refresh our search index, we dey only access publicly available paths, so that anybody wey fork our library fit start dey run am quick quick."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Di standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "At di end, we settle for a relatively simple standard. E dey fairly loose, non-normative, and e still dey work in progress."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archive Container) na one single item wey get <strong>metadata</strong>, and optionally <strong>binary data</strong>, both of dem no fit change. E get one globally unique identifier, wey dem dey call <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collection.</strong> Each AAC dey belong to one collection, wey by definition na list of AACs wey dey semantically consistent. E mean say if you make significant change to di format of di metadata, then you go need create new collection."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“ríkɔdz” an “faɪlz” kɔlɛkshɔn.</strong> Bai kɔnvénshɔn, íts ɔftin kɔnvinyɛnt tɔ rilís “ríkɔdz” an “faɪlz” az difrɛnt kɔlɛkshɔn, só dé fít rilís fɔ difrɛnt skɛjul, fɔ ígzampul bésd ɔn skrɛpin réts. A “ríkɔd” na metadata-ónli kɔlɛkshɔn, wey kɔntén infɔméshɔn laik buk táituls, ɔtɔrz, ISBNs, etc, waíl “faɪlz” na di kɔlɛkshɔn wey kɔntén di ríl faɪlz dɛm sɛlf (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Di fɔmat ɔf AACID na dis: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Fɔ ígzampul, wán ríl AACID wey wi rilís na <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: di kɔlɛkshɔn ném, wey fít kɔntén ASCII létas, nɔmbaz, an ɔndáskɔz (bɔt nɔ dɔbul ɔndáskɔ)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: a shɔt vɛshɔn ɔf di ISO 8601, ɔlwéz fɔ UTC, fɔ ígzampul <code>20220723T194746Z</code>. Dis nɔmba gɛt tɔ mɔnɔtɔnikali ínkriis fɔ evri rilís, dɔ íts ígzakt simántiks fít difa pɛ kɔlɛkshɔn. Wi sɔjest yúz di taim ɔf skrɛpin ɔr ɔf jɛnérétin di ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: a kɔlɛkshɔn-spésifik aidentifáia, if ápplikébul, fɔ ígzampul di Z-Library ID. Fít bì ɔmíted ɔr trɔnkéted. Mɔst bì ɔmíted ɔr trɔnkéted if di AACID wúd ɔdawáiz íksíd 150 káraktaz."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: na UUID wey dem compress to ASCII, e.g. using base57. We dey currently use the <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python library."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID range.</strong> Since AACIDs get monotonically increasing timestamps, we fit use am to show ranges inside one particular collection. We dey use dis format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, where the timestamps dey inclusive. Dis dey consistent with ISO 8601 notation. Ranges dey continuous, and fit overlap, but if overlap dey, dem must get identical records as the one wey dem release before for that collection (since AACs no dey change). Missing records no dey allowed."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata file.</strong> One metadata file dey contain the metadata of a range of AACs, for one particular collection. Dem get the following properties:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Filename must be an AACID range, wey dem prefix with <code style=\"color: red\">annas_archive_meta__</code> and follow with <code>.jsonl.zstd</code>. For example, one of our releases dey called<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "As the file extension show, the file type na <a %(jsonlines)s>JSON Lines</a> wey dem compress with <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Each JSON object must contain the following fields for the top level: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). No other fields dey allowed."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> na arbitrary metadata, per the semantics of the collection. E must dey semantically consistent inside the collection."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> na optional, and na the name of binary data folder wey dey contain the corresponding binary data. The filename of the corresponding binary data inside that folder na the record’s AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "The <code style=\"color: red\">annas_archive_meta__</code> prefix fit dey adapt to the name of your institution, e.g. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binary data folder.</strong> One folder wey get the binary data of a range of AACs, for one particular collection. Dem get the following properties:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Directory name must be an AACID range, wey dem prefix with <code style=\"color: green\">annas_archive_data__</code>, and no suffix. For example, one of our actual releases get a directory wey dem call<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "The directory must contain data files for all AACs inside the specified range. Each data file must get its AACID as the filename (no extensions)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "E dey recommended to make these folders somewhat manageable in size, e.g. no bigger than 100GB-1TB each, though dis recommendation fit change over time."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> The metadata files and binary data folders fit dey bundle inside torrents, with one torrent per metadata file, or one torrent per binary data folder. The torrents must get the original file/directory name plus a <code>.torrent</code> suffix as their filename."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Example"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Make we look our recent Z-Library release as example. E get two collections: “<span style=\"background: #fffaa3\">zlib3_records</span>” and “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dis one dey allow us to separately scrape and release metadata records from di actual book files. As such, we release two torrents with metadata files:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "We also release plenty torrents with binary data folders, but na only for di “<span style=\"background: #ffd6fe\">zlib3_files</span>” collection, 62 in total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "By running <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> we fit see wetin dey inside:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "For dis case, na metadata of a book as Z-Library report am. For di top-level we only get “aacid” and “metadata”, but no “data_folder”, since no dey corresponding binary data. Di AACID get “22430000” as di primary ID, wey we fit see say dem take from “zlibrary_id”. We fit expect other AACs for dis collection to get di same structure."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Now make we run <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dis na much smaller AAC metadata, though the bulk of dis AAC dey elsewhere inside a binary file! After all, we get “data_folder” dis time, so we fit expect the corresponding binary data to dey located at <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. The “metadata” get the “zlibrary_id”, so we fit easily associate am with the corresponding AAC inside the “zlib_records” collection. We fit don associate in a number of different ways, e.g. through AACID — the standard no dey prescribe that."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Note say e no dey necessary for the “metadata” field to dey JSON itself. E fit be a string wey contain XML or any other data format. You fit even store metadata information inside the associated binary blob, e.g. if e dey plenty."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusion"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "With dis standard, we fit make releases more incrementally, and more easily add new data sources. We don already get a few exciting releases for the pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "We also hope say e go become easier for other shadow libraries to mirror our collections. After all, our goal na to preserve human knowledge and culture forever, so the more redundancy the better."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna Improvment: Open Source Archive, ElasticSearch, 300GB+ of Book Covers"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "We don de wok tireless to provide beta alternative wit Anna Improvment. Here na some of di tins we don achieve recently."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "As Z-Library don go down and dem arrest di (alleged) founders, we don de wok tireless to provide beta alternative wit Anna Improvment (we no go put link here, but you fit Google am). Here na some of di tins we don achieve recently."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna Improvment na fully open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "We believe say information suppose dey free, and our own code no be exception. We don release all our code for our privately hosted Gitlab instance: <a %(annas_archive)s>Anna Software</a>. We dey also use di issue tracker to organize our work. If you wan engage with our development, dis na great place to start."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "To give you small taste of di tins we dey work on, check our recent work on client-side performance improvements. Since we never implement pagination yet, we dey often return very long search pages, with 100-200 results. We no wan cut off di search results too soon, but dis mean say e go slow down some devices. For dis, we implement small trick: we wrap most search results in HTML comments (<code><!-- --></code>), and then write small Javascript wey go detect when result suppose become visible, at which moment we go unwrap di comment:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualization\" implemented in 23 lines, no need for fancy libraries! Dis na di kind quick pragmatic code wey you go end up with when you get limited time, and real problems wey need solution. E don dey reported say our search now dey work well on slow devices!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Another big effort na to automate building di database. When we launch, we just dey pull different sources together anyhow. Now we wan keep dem updated, so we write plenty scripts to download new metadata from di two Library Genesis forks, and integrate dem. Di goal na to not just make dis useful for our archive, but to make tins easy for anyone wey wan play around with shadow library metadata. Di goal go be Jupyter notebook wey get all sorts of interesting metadata available, so we fit do more research like figuring out wetin <a %(blog)s>percentage of ISBNs dey preserved forever</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finally, we revamp our donation system. You fit now use credit card to directly deposit money into our crypto wallets, without really needing to know anything about cryptocurrencies. We go keep monitoring how well dis dey work in practice, but dis na big deal."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Switch to ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "One of our <a %(annas_archive)s>tickets</a> na grab-bag of issues with our search system. We dey use MySQL full-text search, since we get all our data for MySQL anyway. But e get im limits:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Some queries dey take super long, to di point where dem go hog all di open connections."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "By default MySQL get minimum word length, or your index fit get really large. People report say dem no fit search for “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Search dey only somewhat fast when fully loaded in memory, which require us to get more expensive machine to run dis on, plus some commands to preload di index on startup."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "We no fit extend am easily to build new features, like better <a %(wikipedia_cjk_characters)s>tokenization for non-whitespaced languages</a>, filtering/faceting, sorting, \"did you mean\" suggestions, autocomplete, and so on."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "After we talk to plenty experts, we settle on ElasticSearch. E no dey perfect (their default “did you mean” suggestions and autocomplete features no good), but overall e don better pass MySQL for search. We still no <a %(youtube)s>too keen</a> on using am for any mission-critical data (though dem don make plenty <a %(elastic_co)s>progress</a>), but overall we dey quite happy with di switch."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "For now, we don implement much faster search, better language support, better relevancy sorting, different sorting options, and filtering on language/book type/file type. If you dey curious how e dey work, <a %(annas_archive_l140)s>make</a> <a %(annas_archive_l1115)s>you</a> <a %(annas_archive_l1635)s>look</a>. E dey fairly accessible, though e fit need some more comments…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ of book covers released"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finally, we dey happy to announce small release. In collaboration with di people wey operate di Libgen.rs fork, we dey share all their book covers through torrents and IPFS. Dis go distribute di load of viewing di covers among more machines, and go preserve dem better. In many (but not all) cases, di book covers dey included in di files themselves, so dis na kind of “derived data”. But having am in IPFS still dey very useful for daily operation of both Anna Improvment and di various Library Genesis forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "As usual, you fit find dis release at di Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna Improvment</a>). We no go put link to am here, but you fit easily find am."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hopefully we fit relax our pace small, now wey we get decent alternative to Z-Library. Dis workload no dey particularly sustainable. If you dey interested in helping out with programming, server operations, or preservation work, definitely reach out to us. E still get plenty <a %(annas_archive)s>work to be done</a>. Thanks for your interest and support."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna and di team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna Improvment don back up di world’s largest comics shadow library (95TB) — you fit help seed am"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Di bigest shadow library wey get comic books for di world bin get only one point wey fit fail.. until today."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Talk am for Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Di bigest shadow library wey get comic books fit be one Library Genesis fork: Libgen.li. Di one admin wey dey run dat site manage gather one crazy comics collection wey get over 2 million files, wey reach over 95TB. But, unlike other Library Genesis collections, dis one no dey available in bulk through torrents. You fit only access these comics one by one through im slow personal server — one single point wey fit fail. Until today!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "For dis post, we go tell you more about dis collection, and about our fundraiser to support more of dis work."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon dey try lose herself for di ordinary world of di library…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen forks"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "First, make we give some background. You fit sabi Library Genesis for their epic book collection. Fewer people sabi say Library Genesis volunteers don create other projects, like one big collection of magazines and standard documents, one full backup of Sci-Hub (in collaboration with di founder of Sci-Hub, Alexandra Elbakyan), and truly, one massive collection of comics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "At some point, different operators of Library Genesis mirrors go their separate ways, wey give rise to di current situation of having a number of different “forks”, all still carrying di name Library Genesis. Di Libgen.li fork uniquely get dis comics collection, as well as one big magazines collection (wey we dey also work on)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaboration"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Because of di size, dis collection don dey our wishlist for long, so after our success with backing up Z-Library, we set our sights on dis collection. At first, we scrape am directly, wey be big challenge, since their server no dey in di best condition. We get about 15TB dis way, but e dey slow."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Luckily, we manage contact di operator of di library, wey agree to send us all di data directly, wey fast pass. E still take more than half a year to transfer and process all di data, and we nearly lose all of am to disk corruption, wey for mean say we go start all over."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Dis experience don make us believe say e dey important to get dis data out there as quickly as possible, so e fit dey mirrored far and wide. We dey just one or two unlucky timed incidents away from losing dis collection forever!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Di collection"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "To move fast mean say di collection dey a little unorganized… Make we look am. Imagine say we get one filesystem (wey in reality we dey split across torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Di first directory, <code>/repository</code>, na di more structured part of dis. Dis directory contain wetin dem dey call “thousand dirs”: directories each with a thousand files, wey dem dey number incrementally for di database. Directory <code>0</code> contain files with comic_id 0–999, and so on."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dis na di same scheme wey Library Genesis don dey use for its fiction and non-fiction collections. Di idea be say every “thousand dir” go automatically turn into a torrent as soon as e full."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "However, di Libgen.li operator never make torrents for dis collection, and so di thousand dirs likely become inconvenient, and give way to “unsorted dirs”. These na <code>/comics0</code> through <code>/comics4</code>. Dem all contain unique directory structures, wey probably make sense for collecting di files, but no make too much sense to us now. Luckily, di metadata still dey refer directly to all these files, so their storage organization on disk no really matter!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Di metadata dey available in di form of a MySQL database. Dis fit be downloaded directly from di Libgen.li website, but we go also make am available in a torrent, alongside our own table with all di MD5 hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analysis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Wen yu get 95TB wey dem dump for your storage cluster, yu go wan try understand wetin dey inside… We do some analysis to see if we fit reduce the size small, like by removing duplicates. Here be some of our findings:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantic duplicates (different scans of the same book) fit theoretically dey filtered out, but e dey tricky. Wen we manually look through the comics, we find too many false positives."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Some duplicates dey purely by MD5, wey dey relatively wasteful, but to filter those out go only give us about 1% in savings. For this scale, na still about 1TB, but also, for this scale 1TB no really matter. We go prefer make we no risk accidentally destroy data for this process."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "We find plenty non-book data, like movies wey dem base on comic books. That one too dey wasteful, since these dey already widely available through other means. However, we realize say we no fit just filter out movie files, since some <em>interactive comic books</em> dey wey dem release on computer, wey person record and save as movies."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Ultimately, anything we fit delete from the collection go only save few percent. Then we remember say we be data hoarders, and the people wey go dey mirror this na data hoarders too, so, “WETIN YOU MEAN, DELETE?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "So, we dey present to you, the full, unmodified collection. E plenty well well, but we hope say enough people go care to seed am anyway."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Fundraiser"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "We dey release this data in some big chunks. The first torrent na of <code>/comics0</code>, wey we put inside one huge 12TB .tar file. E better for your hard drive and torrent software than plenty smaller files."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "As part of this release, we dey do fundraiser. We dey look to raise $20,000 to cover operational and contracting costs for this collection, as well as enable ongoing and future projects. We get some <em>massive</em> ones wey dey come."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Who am I supporting with my donation?</em> In short: we dey back up all knowledge and culture of humanity, and make am easily accessible. All our code and data dey open source, we be completely volunteer-run project, and we don save 125TB worth of books so far (in addition to Libgen and Scihub’s existing torrents). Ultimately we dey build a flywheel wey go enable and incentivize people to find, scan, and backup all the books for the world. We go write about our master plan for future post. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "If you donate for a 12 month “Amazing Archivist” membership ($780), you go fit <strong>“adopt a torrent”</strong>, meaning say we go put your username or message for the filename of one of the torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "You fit donate by going to <a %(wikipedia_annas_archive)s>Anna’s Archive</a> and clicking the “Donate” button. We dey also look for more volunteers: software engineers, security researchers, anonymous merchant experts, and translators. You fit also support us by providing hosting services. And of course, abeg seed our torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Thanks to everyone wey don generously support us already! You dey truly make a difference."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Here be the torrents wey we don release so far (we dey still process the rest):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "All torrents fit dey found on <a %(wikipedia_annas_archive)s>Anna’s Archive</a> under “Datasets” (we no dey link there directly, so links to this blog no go dey removed from Reddit, Twitter, etc). From there, follow the link to the Tor website."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wetin dey next?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Plenty torrents dey great for long-term preservation, but no too much for everyday access. We go dey work with hosting partners to get all this data up on the web (since Anna’s Archive no dey host anything directly). Of course you go fit find these download links on Anna’s Archive."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "We dey also invite everyone to do stuff with this data! Help us better analyze am, deduplicate am, put am on IPFS, remix am, train your AI models with am, and so on. E dey all yours, and we no fit wait to see wetin you go do with am."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finally, as we don talk before, we still get some massive releases wey dey come (if <em>somebody</em> fit <em>accidentally</em> send us a dump of a <em>certain</em> ACS4 database, you sabi where to find us…), as well as building di flywheel for backing up all di books for di world."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "So mek yu dey redi, wi just dey start."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nyu buks aded to di Pirate Library Mirror (+24TB, 3.8 milion buks)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "For di original release of di Pirate Library Mirror (EDIT: dem don move am to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), we make mirror of Z-Library, one big illegal book collection. As reminder, dis na wetin we write for dat original blog post:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library na popular (and illegal) library. Dem don take the Library Genesis collection and make am easily searchable. On top of that, dem don become very effective at soliciting new book contributions, by incentivizing contributing users with various perks. Dem currently no dey contribute these new books back to Library Genesis. And unlike Library Genesis, dem no dey make their collection easily mirrorable, wey dey prevent wide preservation. This dey important to their business model, since dem dey charge money for accessing their collection in bulk (more than 10 books per day)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "We no dey make moral judgements about charging money for bulk access to an illegal book collection. E dey beyond doubt say the Z-Library don successful in expanding access to knowledge, and sourcing more books. We dey simply here to do our part: ensuring the long-term preservation of this private collection."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Dat kolekshọn wey dem date am go bak to mid-2021. As we dey talk so, di Z-Library dey grow for one kind fast rate: dem don add about 3.8 million new books. E get some duplicates for dia, sure, but majority of dem be new books wey dem scan well well, or better quality scans of books wey dem don submit before. Dis na because of di increased number of volunteer moderators for Z-Library, and dia bulk-upload system wey dey remove duplicates. We wan congratulate dem for dis achievements."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "We dey happy to announce say we don get all di books wey dem add to di Z-Library between our last mirror and August 2022. We also go back go collect some books wey we miss di first time. Altogether, dis new kolekshọn na about 24TB, wey big pass di last one (7TB). Our mirror don reach 31TB in total. Again, we remove duplicates against Library Genesis, since torrents dey available for dat kolekshọn already."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Abeg go to di Pirate Library Mirror to check di new kolekshọn (EDIT: dem don move am go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). More information dey dia about how di files dey structured, and wetin don change since last time. We no go link am from here, since dis na just blog website wey no dey host any illegal materials."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Of course, to seed na better way to help us. Thanks to everybody wey dey seed our previous set of torrents. We dey grateful for di positive response, and happy say plenty people dey care about preservation of knowledge and culture in dis unusual way."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna and di team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "How to become a pirate archivist"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Di first challenge fit surprise you. E no be technical problem, or legal problem. E be psychological problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Before we dive in, two updates dey on di Pirate Library Mirror (EDIT: dem don move am go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "We get some extremely generous donations. Di first one na $10k from one anonymous person wey also dey support \"bookwarrior\", di original founder of Library Genesis. Special thanks to bookwarrior for facilitating dis donation. Di second one na another $10k from another anonymous donor, wey contact us after our last release, and e inspire am to help. We also get plenty smaller donations. Thanks so much for all your generous support. We get some exciting new projects for di pipeline wey dis go support, so make you dey watch."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "We get some technical difficulties with di size of our second release, but our torrents dey up and seeding now. We also get one generous offer from one anonymous person to seed our kolekshọn on dia very-high-speed servers, so we dey do special upload to dia machines, after which everybody wey dey download di kolekshọn go see big improvement in speed."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Entire books fit dey written about di <em>why</em> of digital preservation in general, and pirate archivism in particular, but make we give quick primer for those wey no too familiar. Di world dey produce more knowledge and culture than ever before, but also more of am dey lost than ever before. Humanity largely dey trust corporations like academic publishers, streaming services, and social media companies with dis heritage, and dem often no dey prove to be great stewards. Check out di documentary Digital Amnesia, or really any talk by Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Some institutions dey wey dey do good job to archive as much as dem fit, but dem dey bound by di law. As pirates, we dey in unique position to archive collections wey dem no fit touch, because of copyright enforcement or other restrictions. We fit also mirror collections many times over, across di world, thereby increasing di chances of proper preservation."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "For now, we no go enter discussions about di pros and cons of intellectual property, di morality of breaking di law, musings on censorship, or di issue of access to knowledge and culture. With all dat out of di way, make we dive into di <em>how</em>. We go share how our team become pirate archivists, and di lessons wey we learn along di way. Plenty challenges dey when you embark on dis journey, and hopefully we fit help you through some of dem."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Community"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Di first challenge fit surprise you. E no be technical problem, or legal problem. E be psychological problem: doing dis work in di shadows fit dey incredibly lonely. Depending on wetin you dey plan to do, and your threat model, you fit need to dey very careful. On di one end of di spectrum we get people like Alexandra Elbakyan*, di founder of Sci-Hub, wey dey very open about her activities. But she dey at high risk of being arrested if she visit western country at dis point, and fit face decades of prison time. Na risk you go wan take? We dey at di other end of di spectrum; dey very careful not to leave any trace, and get strong operational security."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* As dem mention for HN by \"ynno\", Alexandra initially no want make people sabi her: \"Her servers dey set up to emit detailed error messages from PHP, including full path of faulting source file, wey dey under directory /home/<USER>" So, use random usernames on di computers you dey use for dis stuff, in case you misconfigure something."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Dat secrecy, however, dey come with psychological cost. Most people love make dem recognize dem for di work wey dem dey do, and yet you no fit take any credit for dis in real life. Even simple things fit dey challenging, like friends dey ask you wetin you don dey up to (at some point \"messing with my NAS / homelab\" go tire)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dis na why e dey so important to find some community. You fit give up some operational security by confiding in some very close friends, wey you know you fit trust deeply. Even then make you dey careful not to put anything in writing, in case dem need to turn over dia emails to di authorities, or if dia devices dey compromised in some other manner."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Better still na to find some fellow pirates. If your close friends dey interested in joining you, great! Otherwise, you fit find others online. Sadly dis still be niche community. So far we don find only a handful of others wey dey active in dis space. Good starting places seem to be di Library Genesis forums, and r/DataHoarder. Di Archive Team also get likeminded individuals, though dem dey operate within di law (even if in some grey areas of di law). Di traditional \"warez\" and pirating scenes also get folks wey dey think in similar ways."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Wi de open to ideas on how we fit build community an explore ideas. Feel free to message us for Twitter or Reddit. Maybe we fit host some kind forum or chat group. One wahala be say dis fit easily get censored when we dey use common platforms, so we go need to host am by ourselves. E get as e be between making these discussions fully public (more potential engagement) versus making am private (not letting potential \"targets\" know say we dey about to scrape them). We go need to think about that. Let us know if you dey interested in this!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projects"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "When we dey do project, e get some phases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domain selection / philosophy: Where you roughly wan focus on, and why? Wetin be your unique passions, skills, and circumstances wey you fit use to your benefit?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Target selection: Which specific collection you go mirror?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata scraping: Cataloging information about the files, without actually downloading the (often much larger) files themselves."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Data selection: Based on the metadata, narrowing down which data dey most relevant to archive right now. Fit be everything, but often e get reasonable way to save space and bandwidth."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Data scraping: Actually getting the data."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution: Packaging am up in torrents, announcing am somewhere, getting people to spread am."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "These no dey completely independent phases, and often insights from a later phase fit send you back to an earlier phase. For example, during metadata scraping you fit realize say the target wey you select get defensive mechanisms beyond your skill level (like IP blocks), so you go back and find different target."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domain selection / philosophy"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "No shortage of knowledge and cultural heritage dey to save, which fit dey overwhelming. That's why e dey often useful to take a moment and think about wetin your contribution fit be."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Everybody get different way of thinking about this, but here be some questions wey you fit ask yourself:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Why you dey interested in this? Wetin you dey passionate about? If we fit get bunch of people wey all dey archive the kinds of things wey dem specifically care about, that go cover a lot! You go know a lot more than the average person about your passion, like wetin be important data to save, wetin be the best collections and online communities, and so on."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Wetin skills you get wey you fit use to your benefit? For example, if you be online security expert, you fit find ways of defeating IP blocks for secure targets. If you dey great at organizing communities, then maybe you fit gather some people together around a goal. E dey useful to sabi some programming though, if na only for keeping good operational security throughout dis process."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "How much time you get for this? Our advice go be to start small and do bigger projects as you get the hang of it, but e fit consume all your time."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wetin go bi di high-leverage erịa to fokus on? If yu wan spend X awas on pirate archiving, den how yu fit get di bigest \"bang for your buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wetin be di unique ways wey you dey tink about dis mata? You fit get some interesting ideas or approaches wey oda pipu fit never tink of."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "For our case, we care particularly about the long term preservation of science. We sabi about Library Genesis, and how e dey fully mirrored many times over using torrents. We love that idea. Then one day, one of us try to find some scientific textbooks on Library Genesis, but no fit find them, bringing into doubt how complete e really be. We then search those textbooks online, and find them in other places, which plant the seed for our project. Even before we sabi about the Z-Library, we get the idea of not trying to collect all those books manually, but to focus on mirroring existing collections, and contributing them back to Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Target selection"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "So, we get our area wey we dey look at, now which specific collection we go mirror? E get some things wey make for a good target:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Large"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unique: no dey already well-covered by other projects."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Ezi: e no dey use plenty layers of protection wey go stop you from scraping dia metadata and data."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Special insight: you get some special information about dis target, like say you somehow get special access to dis collection, or you sabi how to defeat dia defenses. E no dey required (our upcoming project no dey do anything special), but e go help well well!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "When we find our science textbooks for websites wey no be Library Genesis, we try to figure out how dem take land for internet. We come find Z-Library, and realize say even though most books no dey first show for dia, dem dey eventually end up dia. We learn about dia relationship with Library Genesis, and the (financial) incentive structure and better user interface, both of dem make am a more complete collection. We come do some preliminary metadata and data scraping, and realize say we fit bypass dia IP download limits, using one of our members' special access to plenty proxy servers."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "As you dey explore different targets, e dey important to hide your tracks by using VPNs and throwaway email addresses, we go talk more about am later."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata scraping"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Make we get a bit more technical here. For actually scraping the metadata from websites, we keep things simple. We dey use Python scripts, sometimes curl, and a MySQL database to store the results. We never use any fancy scraping software wey fit map complex websites, since so far we only need to scrape one or two kinds of pages by just enumerating through ids and parsing the HTML. If dia no be easily enumerated pages, then you fit need a proper crawler wey go try find all pages."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Before you start to scrape a whole website, try do am manually for a bit. Go through a few dozen pages yourself, to get a sense of how e dey work. Sometimes you go already run into IP blocks or other interesting behavior this way. The same goes for data scraping: before you go too deep into this target, make sure say you fit actually download its data effectively."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "To get around restrictions, there are a few things you fit try. Are there any other IP addresses or servers wey host the same data but no get the same restrictions? Are there any API endpoints wey no get restrictions, while others get? At what rate of downloading your IP dey get blocked, and for how long? Or dem no block you but throttle you down? What if you create a user account, how things go change then? You fit use HTTP/2 to keep connections open, and e go increase the rate at which you fit request pages? Are there pages wey list multiple files at once, and the information wey dem list there dey sufficient?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Things wey you probably want to save include:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Title"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Filename / location"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: fit be some internal ID, but IDs like ISBN or DOI dey useful too."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Size: to calculate how much disk space you need."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): to confirm say you download the file properly."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Date added/modified: so you fit come back later and download files wey you no download before (though you fit often also use the ID or hash for this)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Description, category, tags, authors, language, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "We dey typically do this in two stages. First we download the raw HTML files, usually directly into MySQL (to avoid plenty small files, which we go talk more about below). Then, in a separate step, we go through those HTML files and parse them into actual MySQL tables. This way you no go need to re-download everything from scratch if you discover a mistake in your parsing code, since you fit just reprocess the HTML files with the new code. E dey also often easier to parallelize the processing step, thus saving some time (and you fit write the processing code while the scraping dey run, instead of having to write both steps at once)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finally, note say for some targets metadata scraping na all wey dey. There are some huge metadata collections out there wey dem no properly preserve."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Data selection"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Sọmtain yu fit yus di metadata te sabi di resọnabul subset of data wey yu go download. Ivun if yu wan download all di data at di end, e fit dey useful to first download di most important items, in case dem catch yu an dem improve di defences, or because yu go need buy more disks, or simply because somtin else fit happen for yu life before yu fit download everytin."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "For example, wan collection fit get multiple editions of di same resource (like book or film), wey one dey marked as di best quality. To save those editions first go make sense. Yu fit wan save all editions later, because sometimes di metadata fit dey tagged wrong, or fit get unknown tradeoffs between editions (for example, di \"best edition\" fit dey best for most ways but worse for other ways, like film wey get higher resolution but no get subtitles)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Yu fit also search yu metadata database to find interesting things. Wetin be di biggest file wey dem host, an why e big like dat? Wetin be di smallest file? E get interesting or unexpected patterns when e come to certain categories, languages, an so on? E get duplicate or very similar titles? E get patterns to when dem add data, like one day wey dem add many files at once? Yu fit learn plenty by looking at di dataset in different ways."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "For our case, we deduplicated Z-Library books against di md5 hashes in Library Genesis, thereby saving plenty download time an disk space. Dis na pretty unique situation sha. For most cases, e no get comprehensive databases of which files dem don already properly preserve by fellow pirates. Dis in itself na big opportunity for person wey dey out there. E go good to get regularly updated overview of things like music an films wey dem don already widely seed on torrent websites, an therefore dey lower priority to include in pirate mirrors."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Data scraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Now yu dey ready to actually download di data in bulk. As we don mention before, for dis point yu suppose don manually download some files, to better understand di behavior an restrictions of di target. However, surprises still dey wait for yu once yu actually start to download plenty files at once."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Our advice here na mainly to keep am simple. Start by just downloading some files. Yu fit use Python, an then expand to multiple threads. But sometimes even simpler na to generate Bash files directly from di database, an then run multiple of dem in multiple terminal windows to scale up. One quick technical trick wey worth mentioning here na using OUTFILE in MySQL, wey yu fit write anywhere if yu disable \"secure_file_priv\" in mysqld.cnf (an make sure to also disable/override AppArmor if yu dey on Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "We dey store di data on simple hard disks. Start out with wetin yu get, an expand slowly. E fit dey overwhelming to think about storing hundreds of TBs of data. If na di situation wey yu dey face, just put out good subset first, an for yu announcement ask for help in storing di rest. If yu wan get more hard drives yourself, then r/DataHoarder get some good resources on getting good deals."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Try not to worry too much about fancy filesystems. E easy to fall into di rabbit hole of setting up things like ZFS. One technical detail to be aware of though, na say many filesystems no dey deal well with plenty files. We don find say simple workaround na to create multiple directories, e.g. for different ID ranges or hash prefixes."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "After downloading di data, make sure to check di integrity of di files using hashes in di metadata, if e dey available."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Yu don get di data, thereby giving yu possession of di world's first pirate mirror of yu target (most likely). For many ways di hardest part don pass, but di riskiest part still dey ahead of yu. After all, so far yu don dey stealth; flying under di radar. All yu need do na to use good VPN throughout, no fill in yu personal details for any forms (duh), an perhaps use special browser session (or even different computer)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Now yu need distribute di data. For our case we first wan contribute di books back to Library Genesis, but then quickly discover di difficulties in dat (fiction vs non-fiction sorting). So we decide on distribution using Library Genesis-style torrents. If yu get di opportunity to contribute to existing project, then dat fit save yu plenty time. However, e no get many well-organized pirate mirrors out there currently."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "So make we say you decide to distribute torrents yourself. Try keep those files small, so dem go easy to mirror on other websites. You go then have to seed the torrents yourself, while still staying anonymous. You fit use a VPN (with or without port forwarding), or pay with tumbled Bitcoins for a Seedbox. If you no sabi wetin some of those terms mean, you go get plenty reading to do, since e dey important say you understand the risk tradeoffs here."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "You fit host di torrent files dem for di existing torrent websites. For our own case, we choose to actually host a website, because we also wan spread our philosophy in a clear way. You fit do am yourself in a similar way (we dey use Njalla for our domains and hosting, we dey pay with tumbled Bitcoins), but you fit also contact us make we host your torrents. We dey look to build a comprehensive index of pirate mirrors over time, if dis idea catch on."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "As for VPN selection, plenty don dey write about dis already, so we go just repeat di general advice of choosing by reputation. Actual court-tested no-log policies with long track records of protecting privacy na di lowest risk option, for our opinion. Note say even when yu do everything right, yu no fit ever get to zero risk. For example, when seeding yu torrents, highly motivated nation-state actor fit probably look at incoming an outgoing data flows for VPN servers, an deduce who yu be. Or yu fit just simply mess up somehow. We probably don already mess up, an go still mess up again. Luckily, nation states no dey care <em>that</em> much about piracy."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "One decision to make for each project, na whether to publish am using di same identity as before, or not. If yu keep using di same name, then mistakes in operational security from earlier projects fit come back to bite yu. But to publish under different names means say yu no go build longer lasting reputation. We choose to get strong operational security from di start so we fit keep using di same identity, but we no go hesitate to publish under different name if we mess up or if di circumstances call for am."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "To get di word out fit dey tricky. As we talk, dis still be niche community. We originally post on Reddit, but really get traction on Hacker News. For now our recommendation na to post am in few places an see wetin go happen. An again, contact us. We go love to spread di word of more pirate archivism efforts."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hopefully dis go dey helpful for newly starting pirate archivists. We dey excited to welcome yu to dis world, so no hesitate to reach out. Make we preserve as much of di world's knowledge an culture as we fit, an mirror am far an wide."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna and di team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Introducing di Pirate Library Mirror: Preserving 7TB of books (wey no dey for Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dis prọjekt (EDIT: e don mov go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>) wan help for di preserv an librate human knowledge. We dey make our small an humble kontribushon, as we dey follow di footstep of di great pipul wey don dey before us."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Di focus of dis prọjekt dey show for di name:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirate</strong> - We dey deliberately break di copyright law for most kontris. Dis dey allow us do wetin legal entities no fit do: make sure say books dey mirror far an wide."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Library</strong> - Like most libraries, we dey focus mainly on written materials like books. We fit expand go other types of media for future."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - We dey strictly as mirror of di libraries wey don dey. We dey focus on preservation, no be to make books easy to search an download (access) or to build big community of pipul wey dey contribute new books (sourcing)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Di first library wey we don mirror na Z-Library. Dis na popular (an illegal) library. Dem don take di Library Genesis collection an make am easy to search. On top of dat, dem don dey very effective for collecting new book contributions, by giving contributing users different perks. Dem no dey contribute dis new books back to Library Genesis. An unlike Library Genesis, dem no dey make their collection easy to mirror, wey dey stop wide preservation. Dis dey important to their business model, since dem dey charge money to access their collection in bulk (more than 10 books per day)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "We no dey make moral judgements about charging money for bulk access to an illegal book collection. E dey beyond doubt say the Z-Library don successful in expanding access to knowledge, and sourcing more books. We dey simply here to do our part: ensuring the long-term preservation of this private collection."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "We wan invite you to help preserve an liberate human knowledge by downloading an seeding our torrents. See di project page for more information about how di data dey organized."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "We go also like make you contribute your ideas for which collections to mirror next, an how to go about am. Together we fit achieve plenty. Dis na just small contribution among many others. Thank you, for all wey you dey do."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna and di team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>We no dey link to di files from dis blog. Abeg find am yourself.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, or How Many Books Are Preserved Forever?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "If we fit properly deduplicate di files from shadow libraries, wetin be di percentage of all di books for di world wey we don preserve?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "With di Pirate Library Mirror (EDIT: e don mov go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), our aim na to take all di books for di world, an preserve dem forever.<sup>1</sup> Between our Z-Library torrents, an di original Library Genesis torrents, we get 11,783,153 files. But how many be dat, really? If we properly deduplicated those files, wetin be di percentage of all di books for di world wey we don preserve? We go really like get something like dis:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of humanity’s written heritage preserved forever"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "For a percentage, we need a denominator: di total number of books wey dem don ever publish.<sup>2</sup> Before di end of Google Books, one engineer for di project, Leonid Taycher, <a %(booksearch_blogspot)s>try to estimate</a> dis number. E come up — tongue-in-cheek — with 129,864,880 (“at least until Sunday”). E estimate dis number by building a unified database of all di books for di world. For dis, e gather different datasets an then merge dem in various ways."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "As quick aside, another person wey try to catalog all di books for di world na Aaron Swartz, di late digital activist an Reddit co-founder.<sup>3</sup> E <a %(youtube)s>start Open Library</a> with di goal of “one web page for every book wey dem don ever publish”, combining data from plenty different sources. E end up paying di ultimate price for e digital preservation work when dem prosecute am for bulk-downloading academic papers, wey lead to e suicide. Needless to say, dis na one of di reasons our group dey pseudonymous, an why we dey very careful. Open Library still dey heroically run by folks for di Internet Archive, continuing Aaron’s legacy. We go come back to dis later for dis post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "For di Google blog post, Taycher describe some of di challenges with estimating dis number. First, wetin be book? There are a few possible definitions:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Physical copies.</strong> Obviously dis no dey very helpful, since dem just dey duplicate di same material. E go cool if we fit preserve all annotations wey pipul dey make for books, like Fermat’s famous “scribbles in di margins”. But alas, dat go remain archivist’s dream."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Works”.</strong> For example “Harry Potter and the Chamber of Secrets” as a logical concept, wey dey include all versions of am, like different translations an reprints. Dis na kind of useful definition, but e fit hard to draw di line of wetin count. For example, we probably wan preserve different translations, though reprints with only minor differences fit no dey as important."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Editions”.</strong> Here you go count every unique version of a book. If anything about am dey different, like different cover or different preface, e go count as different edition."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Files.</strong> Wen pesin dey wok wit shado librari dem laik Library Genesis, Sci-Hub, or Z-Library, dia dey get anoda mata to tink about. E fit get plenti scan of di same edition. An pipul fit make beta version of di file wey dey, by scanning di text wit OCR, or correct pages wey dem scan for angle. We wan make sure say we go count dis files as one edition, wey go need beta metadata, or deduplication using document similarity measures."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Editions” dey seem laik di most practical definition of wetin “books” be. E dey convenient, as dis definition dey also use for assign unique ISBN numbers. ISBN, or International Standard Book Number, dey commonly use for international commerce, since e dey integrated wit di international barcode system (”International Article Number”). If you wan sell book for store, e need barcode, so you go get ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher’s blog post mention say even though ISBNs dey useful, dem no dey universal, since dem only start to dey use am well for di mid-seventies, and no be everywhere for di world. Still, ISBN dey probably be di most widely used identifier of book editions, so na our best starting point. If we fit find all di ISBNs for di world, we go get useful list of which books still need to be preserved."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "So, where we go get di data? E get number of existing efforts wey dey try compile list of all di books for di world:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> After all, dem do dis research for Google Books. However, dia metadata no dey accessible in bulk and e dey hard to scrape."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> As we don mention before, na dia entire mission be dis. Dem don source massive amounts of library data from cooperating libraries and national archives, and dem still dey do so. Dem also get volunteer librarians and technical team wey dey try deduplicate records, and tag dem wit all sorts of metadata. Best of all, dia dataset dey completely open. You fit simply <a %(openlibrary)s>download am</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dis na website wey non-profit OCLC dey run, wey dey sell library management systems. Dem dey aggregate book metadata from plenty libraries, and make am available through di WorldCat website. However, dem dey also make money selling dis data, so e no dey available for bulk download. Dem get some more limited bulk datasets available for download, in cooperation wit specific libraries."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dis na di topic of dis blog post. ISBNdb dey scrape various websites for book metadata, particularly pricing data, wey dem dey sell to booksellers, so dem fit price dia books in accordance wit di rest of di market. Since ISBNs dey fairly universal nowadays, dem effectively build “web page for every book”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Various individual library systems and archives.</strong> E get libraries and archives wey dem never index and aggregate by any of di ones above, often because dem dey underfunded, or for other reasons dem no wan share dia data wit Open Library, OCLC, Google, and so on. Plenty of dem get digital records accessible through di internet, and dem often no dey very well protected, so if you wan help out and have some fun learning about weird library systems, dis na great starting points."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "For dis post, we dey happy to announce small release (compared to our previous Z-Library releases). We scrape most of ISBNdb, and make di data available for torrenting on di website of di Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>; we no go link am here directly, just search for am). Dis na about 30.9 million records (20GB as <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). For dia website dem claim say dem actually get 32.6 million records, so we fit somehow miss some, or <em>dem</em> fit dey do something wrong. In any case, for now we no go share exactly how we do am — we go leave that as an exercise for di reader. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wet we go share na some preliminary analysis, to try to get closer to estimating di number of books for di world. We look at three datasets: dis new ISBNdb dataset, our original release of metadata wey we scrape from di Z-Library shadow library (wey include Library Genesis), and di Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Make we start wit some rough numbers:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "For both Z-Library/Libgen and Open Library, e get many more books than unique ISBNs. E mean say plenty of those books no get ISBNs, or na di ISBN metadata dey simply miss? We fit probably answer dis question wit combination of automated matching based on other attributes (title, author, publisher, etc), pulling in more data sources, and extracting ISBNs from di actual book scans themselves (for di case of Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "How many of those ISBNs dey unique? Dis na best illustrated wit Venn diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "To be more precise:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "We dey surprised by how little overlap dey! ISBNdb get huge amount of ISBNs wey no show up in either Z-Library or Open Library, and di same hold (to a smaller but still substantial degree) for di other two. Dis dey raise plenty new questions. How much automated matching go help in tagging di books wey dem no tag wit ISBNs? E go get plenty matches and therefore increased overlap? Also, wetin go happen if we bring in 4th or 5th dataset? How much overlap we go see then?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dis dey give us starting point. We fit now look at all di ISBNs wey no dey for di Z-Library dataset, and wey no match title/author fields either. Dat fit give us handle on preserving all di books for di world: first by scraping di internet for scans, then by going out in real life to scan books. Di latter fit even be crowd-funded, or driven by “bounties” from people wey go like see particular books digitized. All dat na story for different time."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "If you wan help out wit any of dis — further analysis; scraping more metadata; finding more books; OCR’ing of books; doing dis for other domains (e.g. papers, audiobooks, movies, tv shows, magazines) or even making some of dis data available for things like ML / large language model training — abeg contact me (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "If you dey specifically interested in di data analysis, we dey work on making our datasets and scripts available in more easy to use format. E go great if you fit just fork notebook and start to dey play wit dis."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finally, if you wan support dis work, abeg consider making donation. Dis na entirely volunteer-run operation, and your contribution dey make huge difference. Every bit dey help. For now we dey take donations in crypto; see di Donate page on Anna’s Archive."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna and di team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Fɔ sɔm kind defínishọn wey make sense fɔ \"fɔreva\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Of kɔs, di tin wey pipul don write na im be di heritij wey di wold get, e pass buks, speshali fɔ dis kain taim. Fɔ di sake of dis post an di new tins wey we don release, we go focus on buks, but wetin we dey intarested in dey pass dat one."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Plenty tins dey wey we fit tok about Aaron Swartz, but we just wan mention am small, since e play important role fɔ dis stori. As time dey go, more pipul fit hear im name fɔ di first time, an dem fit decide to find out more by demsef."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Di important window of shadow libraries"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "How we fit tok say we go keep our kolekshọn fɔeva, wen dem don dey reach 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Chinese version 中文版</a>, tok about am fɔ <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Fɔ Anna’s Archive, pipul dey always ask us how we fit tok say we go keep our kolekshọn fɔeva, wen di total size don dey reach 1 Petabyte (1000 TB), an e still dey grow. Fɔ dis article, we go look our filosofi, an see why di next ten years dey important fɔ our mission to keep di knowledge an culture wey humanity get."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Di <a %(annas_archive_stats)s>total size</a> of our kolekshọn, fɔ di last few months, break am down by number of torrent seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorities"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Why we dey care so much about papers and books? Make we put aside our fundamental belief in preservation in general — we fit write another post about dat one. So why papers and books specifically? Di answer dey simple: <strong>information density</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte of storage, written text dey store di most information out of all media. While we dey care about both knowledge and culture, we dey care more about di former. Overall, we find one hierarchy of information density and importance of preservation wey look roughly like dis:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Academic papers, journals, reports"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organic data like DNA sequences, plant seeds, or microbial samples"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Non-fiction books"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Science & engineering software code"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Measurement data like scientific measurements, economic data, corporate reports"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Science & engineering websites, online discussions"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Non-fiction magazines, newspapers, manuals"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Non-fiction transcripts of talks, documentaries, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Intana data wey kompinis o govment get (leaks)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata records wey dey general (of non-fiction an fiction; of oda media, art, pipul, etc; wey include reviews)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geographic data (e.g. maps, geological surveys)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcripts of legal o court proceedings"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fictional o entertainment versions of all di tins wey dey above"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Di ranking for dis list na somehow arbitrary — some items dey tie or get disagreements inside our team — an we fit dey forget some important categories. But na roughly how we dey prioritize."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Some of dis items dey too different from di oda ones for us to worry about (or dem don already take care of am by oda institutions), like organic data or geographic data. But most of di items for dis list dey actually important to us."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Anoda big factor for our prioritization na how much at risk one certain work dey. We prefer to focus on works wey be:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rare"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniquely underfocused"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniquely at risk of destruction (e.g. by war, funding cuts, lawsuits, or political persecution)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finally, we care about scale. We get limited time an money, so we go rather spend one month to save 10,000 books than 1,000 books — if dem dey about equally valuable an at risk."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Shadow libraries"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Plenty organizations dey wey get similar missions, an similar priorities. True true, libraries, archives, labs, museums, an oda institutions dey wey dem task with preservation of dis kind. Many of dem dey well-funded, by governments, individuals, or corporations. But dem get one massive blind spot: di legal system."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Na here shadow libraries get unique role, an na di reason why Anna’s Archive dey exist. We fit do things wey oda institutions no dey allowed to do. Now, no be say we fit archive materials wey illegal to preserve elsewhere. No, e dey legal for many places to build an archive with any books, papers, magazines, an so on."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "But wetin legal archives dey lack na <strong>redundancy an longevity</strong>. Some buks dey wey na only one copy dey fɔ some physical library somewhere. Some metadata records dey wey only one company dey protect. Some newspapers dey wey na only microfilm dey preserve dem fɔ one archive. Libraries fit get funding cuts, companies fit go bankrupt, archives fit get bombed an burn down. Dis no be hypothetical — e dey happen all di time."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Di tin wey we fit do fɔ Anna’s Archive na to store plenty copies of works, fɔ large scale. We fit kolekt papers, buks, magazines, an more, an distribute dem in bulk. We dey do dis through torrents now, but di exact technologies no matter an go change over time. Di important part na to get plenty copies distributed across di world. Dis quote from over 200 years ago still dey true:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Di tin wey don lost no fit recover; but make we save wetin remain: no be by vaults an locks wey go hide dem from di public eye an use, consigning dem to di waste of time, but by such a multiplication of copies, as go place dem beyond di reach of accident.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Quick note about public domain. Since Anna’s Archive dey focus on activities wey dey illegal fɔ many places around di world, we no dey bother with widely available collections, like public domain books. Legal entities dey often take good care of dat. However, some reasons dey wey make us sometimes work on publicly available collections:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata records fit dey freely viewed on di Worldcat website, but no fit download in bulk (until we <a %(worldcat_scrape)s>scraped</a> dem)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Code fit dey open source on Github, but Github as a whole no fit easily mirror an thus preserve (though fɔ dis particular case, plenty distributed copies of most code repositories dey)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit dey free to use, but recently dem don put up strong anti-scraping measures, because of data-hungry LLM training (more about dat later)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "A multiplication of copies"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Back to our original question: how we fit tok say we go keep our kolekshọn fɔeva? Di main problem here na say our kolekshọn don dey <a %(torrents_stats)s>grow</a> fast, by scraping an open-sourcing some big collections (on top of di amazing work wey other open-data shadow libraries like Sci-Hub an Library Genesis don already do)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Dis growth in data dey make am harder for di collections to dey mirror around di world. Data storage dey expensive! But we dey optimistic, especially when we dey observe di following three trends."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Wi don pluk di low-hangin frut"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dis wan dey follow di priorities wey we don discuss above. We dey prefer to work on liberatin big collections first. Now wey we don secure some of di biggest collections for di world, we dey expect our growth to slow down well well."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Still dey plenty small small collections, and new books dey get scanned or published every day, but di rate go slow down well well. We fit still double or even triple for size, but na over long time period."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Storage costs dey continue to drop well well"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "As at di time wey we dey write dis, <a %(diskprices)s>disk prices</a> per TB dey around $12 for new disks, $8 for used disks, and $4 for tape. If we dey conservative and look only at new disks, e mean say to store one petabyte go cost about $12,000. If we assume say our library go triple from 900TB to 2.7PB, e go mean $32,400 to mirror our entire library. Add electricity, cost of other hardware, and so on, make we round am up to $40,000. Or with tape more like $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "On one hand <strong>$15,000–$40,000 for di sum of all human knowledge na steal</strong>. On di other hand, e dey a bit steep to expect plenty full copies, especially if we go like make those people dey keep seeding their torrents for di benefit of others."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Na today be dat. But progress dey move forward:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Hard drive costs per TB don roughly cut into third over di last 10 years, and e go likely continue to drop at similar pace. Tape dey appear to dey on similar path. SSD prices dey drop even faster, and fit take over HDD prices by di end of di decade."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD price trends from different sources (click to view study)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "If dis hold, then in 10 years we fit dey look at only $5,000–$13,000 to mirror our entire collection (1/3rd), or even less if we grow less in size. While e still plenty money, dis go dey attainable for many people. And e fit even better because of di next point…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Improvements in information density"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "We dey currently store books for di raw formats wey dem give us. Sure, dem dey compressed, but often dem still dey large scans or photographs of pages."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Until now, di only options to shrink di total size of our collection na through more aggressive compression, or deduplication. However, to get significant enough savings, both dey too lossy for our taste. Heavy compression of photos fit make text barely readable. And deduplication require high confidence of books being exactly di same, which dey often too inaccurate, especially if di contents dey di same but di scans dey made on different occasions."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "There don always dey a third option, but e quality don dey so bad we never consider am: <strong>OCR, or Optical Character Recognition</strong>. Dis na di process of converting photos into plain text, by using AI to detect di characters in di photos. Tools for dis don long exist, and don dey pretty decent, but “pretty decent” no dey enough for preservation purposes."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "However, recent multi-modal deep-learning models don make extremely rapid progress, though still at high costs. We dey expect both accuracy and costs to improve dramatically in coming years, to di point where e go become realistic to apply to our entire library."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR improvements."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "When dat happen, we go likely still preserve di original files, but in addition we fit get a much smaller version of our library wey most people go want to mirror. Di kicker na say raw text itself dey compress even better, and dey much easier to deduplicate, giving us even more savings."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Overall e no dey unrealistic to expect at least a 5-10x reduction in total file size, perhaps even more. Even with a conservative 5x reduction, we go dey look at <strong>$1,000–$3,000 in 10 years even if our library triples in size</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Critical window"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "If these forecasts dey accurate, we <strong>just need to wait a couple of years</strong> before our entire collection go dey widely mirrored. Thus, in di words of Thomas Jefferson, “placed beyond di reach of accident.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Unfortunately, di advent of LLMs, and their data-hungry training, don put plenty copyright holders on di defensive. Even more than dem already dey. Many websites dey make am harder to scrape and archive, lawsuits dey fly around, and all di while physical libraries and archives dey continue to dey neglected."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Wi fit expect say dis trends go continue to dey worse, and many works go lost well before dem enter public domain."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>We dey on di eve of a revolution for preservation, but <q>di ones wey don lost no fit recover.</q></strong> We get critical window of about 5-10 years wey e still dey fairly expensive to operate shadow library and create many mirrors around di world, and wey access never completely shut down yet."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "If we fit bridge dis window, then we go truly preserve humanity knowledge and culture forever. We no suppose let dis time waste. We no suppose let dis critical window close on us."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Make we go."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Exclusive access for LLM companies to largest Chinese non-fiction book collection for di world"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Chinese version 中文版</a>, <a %(news_ycombinator)s>Discuss on Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archive don acquire unique collection of 7.5 million / 350TB Chinese non-fiction books — e big pass Library Genesis. We dey willing to give LLM company exclusive access, in exchange for high-quality OCR and text extraction.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dis na short blog post. We dey look for some company or institution to help us with OCR and text extraction for di massive collection we acquire, in exchange for exclusive early access. After di embargo period, we go release di entire collection."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "High-quality academic text dey extremely useful for training of LLMs. Even though our collection na Chinese, e go still dey useful for training English LLMs: models dey encode concepts and knowledge regardless of di source language."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "For dis, text need to dey extracted from di scans. Wetin Anna’s Archive go gain from am? Full-text search of di books for its users."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Because our goals align with that of LLM developers, we dey look for collaborator. We dey willing to give you <strong>exclusive early access to dis collection in bulk for 1 year</strong>, if you fit do proper OCR and text extraction. If you dey willing to share di entire code of your pipeline with us, we go dey willing to embargo di collection for longer."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Example pages"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "To prove to us say you get good pipeline, here dey some example pages to start with, from one book on superconductors. Your pipeline suppose properly handle math, tables, charts, footnotes, an so on."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Send your processed pages to our email. If dem look good, we go send you more in private, an we expect you to fit quickly run your pipeline on those as well. Once we dey satisfied, we fit make a deal."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collection"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Some more information about di collection. <a %(duxiu)s>Duxiu</a> na massive database of scanned books, wey <a %(chaoxing)s>SuperStar Digital Library Group</a> create. Most na academic books, dem scan am to make dem available digitally to universities an libraries. For our English-speaking audience, <a %(library_princeton)s>Princeton</a> an di <a %(guides_lib_uw)s>University of Washington</a> get good overviews. Dere dey also one excellent article wey dey give more background: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (look am up for Anna’s Archive)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "D buks wey dey from Duxiu don dey pirated for Chinese internet for long. Dem dey usually sell am for less than one dollar by resellers. Dem dey typically distribute am using di Chinese version of Google Drive, wey dem don hack many times to allow for more storage space. Some technical details dey <a %(github_duty_machine)s>here</a> and <a %(github_821_github_io)s>here</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Even though di books don dey semi-publicly distributed, e dey quite difficult to obtain dem in bulk. We get dis high on our TODO-list, and allocate multiple months of full-time work for am. However, recently an incredible, amazing, and talented volunteer reach out to us, tell us say dem don do all dis work already — at great expense. Dem share di full collection with us, without expecting anything in return, except di guarantee of long-term preservation. Truly remarkable. Dem agree to ask for help in dis way to get di collection OCR'ed."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Di collection na 7,543,702 files. Dis pass Library Genesis non-fiction (about 5.3 million). Total file size na about 359TB (326TiB) in its current form."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "We dey open to other proposals and ideas. Just contact us. Check out Anna’s Archive for more information about our collections, preservation efforts, and how you fit help. Thanks!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Warning: dis blog post don dey deprecated. We don decide say IPFS never ready for prime time. We go still link to files on IPFS from Anna’s Archive when possible, but we no go host am ourselves again, nor we dey recommend others to mirror using IPFS. Abeg see our Torrents page if you wan help preserve our collection."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Help seed Z-Library on IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "How to run a shadow library: operations at Anna’s Archive"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Dè no dey <q>AWS fɔ shadó cháríti dem,</q> so how wí go run Anna’s Archive?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "A de run <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di wórld bigest open-sọs nɔn-prọfit sẹch injin fɔ <a %(wikipedia_shadow_library)s>shadó laibrari dem</a>, laik Sci-Hub, Library Genesis, an Z-Library. Aw aim na to mek nọlej an kọltọr redili aksesibul, an fainali to bild a kọmuniti of pipul wey go join hand to arkaiv an prẹzav <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>ọl di buks fɔ di wórld</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Fɔ dis atikul a go shọ how wí de run dis website, an di speshial chálẹnj wey de kam wit ọpẹretin a website wey get kwẹshọnabul ligal stetus, sins dè no dey “AWS fɔ shadó cháríti dem”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Mek yú chẹk di sista atikul <a %(blog_how_to_become_a_pirate_archivist)s>How to become a pirate archivist</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Inovẹshọn tọnkin"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Mek wí start wit aw tech stack. I de delibẹretli bọrin. Wí de yus Flask, MariaDB, an ElasticSearch. Na dat bi ọl. Sẹch na somtin wey dem dọn solv, an wí nọn de plan to reinvẹnt am. Bẹsáid, wí gọt to spend aw <a %(mcfunley)s>inovẹshọn tọnkin</a> fɔ anọda tin: mek dem nọn tek wí daun."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "So how ligal or iligal Anna’s Archive bi? Dis mosli depend on di ligal jọrisdikshọn. Mos kontris biliv in som fọm of kọpirait, wey min se pipul or kọmpani dem get eksklusiv monopoli on sẹtin kain wọk fɔ sẹtin taim. As an asáid, fɔ Anna’s Archive wí biliv se wail som bẹnẹfit dey, ọvọral kọpirait na net-negativ fɔ sọsáyeti — bọt dat na tori fɔ anọda taim."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dis eksklusiv monopoli on sẹtin wọk min se i de iligal fɔ eniwọn wey nọn de insaid dis monopoli to dairektli distribut dos wọk — inkludin wí. Bọt Anna’s Archive na sẹch injin wey nọn dairektli distribut dos wọk (at list nọn fɔ aw klianet website), so wí shud dey ọkei, raít? Nọn egzaktli. Fɔ meni jọrisdikshọn, i nọn onli iligal to distribut kọpiraited wọk, bọt also to link to ples wey de du am. A klasik igzampul of dis na di Yunaited Stets’ DMCA lɔ."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dat na di strikt end of di spẹktrum. On di oda end of di spẹktrum, dere fit dey kontris wey nọn get kọpirait lɔ at ọl, bọt dis kain kontris nọn rili exist. Prẹti moch evri kontri get som fọm of kọpirait lɔ on di buks. Infọsmẹnt na anọda tori. Plenty kontris dey wey di gọvẹnmẹnt nọn kẹr to infọs kọpirait lɔ. Also, kontris dey in bitwin di tu ekstrem, wey prohibit distributin kọpiraited wọk, bọt nọn prohibit linkin to sọọch wọk."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Anọda konsidereshọn na at di kọmpani-level. If a kọmpani ọpẹret fɔ jọrisdikshọn wey nọn kẹr abaut kọpirait, bọt di kọmpani sɛf nọn wan tek eni risk, den dem fit shọt daun yọ website as sọn as eniwọn kọnplẹn abaut am."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Fainali, a big konsidereshọn na peymẹnt. Sins wí nid to stẹ anọnimọs, wí nọn fit yus tradishọnal peymẹnt metod. Dis liv wí wit kriptọkọrensi, an onli a smọl subset of kọmpani dem de sọọch (dey dey virtual debit kads wey kripto de pey, bọt dem nọn de ofin asepted)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Sistem akitẹktọ"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "So mek wí se yú fain som kọmpani dem wey de wilin to host yọ website witaut shọt yú daun — mek wí kọọl dis “fridọm-lọvin providas” 😄. Yú go kwikli fain se hostin evritin wit dem de rader ekspensiv, so yú fit wan fain som “chip providas” an du di riyal hostin dere, prọksiin tru di fridọm-lọvin providas. If yú du am raít, di chip providas nọn go evan sabi wetin yú de host, an dem nọn go risiv eni kọnplẹnt."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Wit ọl dis providas, riski dey se dem go shọt yú daun eniwei, so yú nid redundansi. Wí nid dis on ọl levuls of aw stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Wọn sọọmwat fridọm-lọvin kọmpani wey put im sɛf fɔ an intarestin pọzishọn na Cloudflare. Dem <a %(blog_cloudflare)s>argu</a> se dem nọn bi hostin provida, bọt a yutiliti, laik an ISP. Dem nọn de subject to DMCA or oda tẹkdaun rikwẹst, an dem de fọwọd eni rikwẹst to yọ riyal hostin provida. Dem dọn go as far as go kọt to prọtẹkt dis strọkchọ. Wí fit yus dem as anọda leya of kashin an prọtẹkshọn."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare nọn de asept anọnimọs peymẹnt, so wí fit onli yus dia fri plan. Dis min se wí nọn fit yus dia lọd balansin or failovẹ fịchọs. Wí deọfọ <a %(annas_archive_l255)s>implimẹnt dis aw sɛf</a> at di domẹn level. On pej lọd, di brọwsa go chẹk if di kọnrent domẹn stil de avẹlabl, an if nọn, i go riraít ọl URLs to anọda domẹn. Sins Cloudflare de kash meni pejes, dis min se a yúza fit land on aw main domẹn, ivin if di prọksi sẹva de daun, an den on di nekst klik, dem go muv to anọda domẹn."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Wí stil get nọọmal ọpẹreshọnal kọnsẹn to dil wit, laik monitọrin sẹva hẹlt, lọgin backend an frontend erọs, an so on. Aw failovẹ akitẹktọ alaw fɔ mọ robustnes on dis front as wel, fọ igzampul bai runnin a kọnplitli difrent set of sẹvas on wọn of di domẹns. Wí fit ivin run ọlda vẹshọn of di kọd an Datasets on dis sepẹret domẹn, in kes a kritikal bọg in di main vẹshọn go nọnotis."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Wí fit also hẹj agẹnst Cloudflare tọnin agẹnst wí, bai rimuvin am from wọn of di domẹns, laik dis sepẹret domẹn. Difrent pẹmyuteshọn of dis ideas de posibul."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Tuls"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Mek wi luk di tools wey wi dey use to achieve all dis. Dis dey change as we dey face new problems an find new solutions."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Application server: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy server: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Server management: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Development: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion static hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Some decisions dey wey we don dey change mind on. One na di communication between servers: we bin dey use Wireguard for dis, but we find say e dey sometimes stop to transmit any data, or e go only transmit data for one direction. Dis happen with different Wireguard setups wey we try, like <a %(github_costela_wesher)s>wesher</a> and <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. We also try to tunnel ports over SSH, using autossh and sshuttle, but we face <a %(github_sshuttle)s>problems there</a> (even though e still no clear to me if autossh dey suffer from TCP-over-TCP issues or not — e just dey feel like one janky solution to me but maybe e dey actually okay?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Instead, we go back to direct connections between servers, dey hide say server dey run on cheap providers using IP-filtering with UFW. Dis get downside say Docker no dey work well with UFW, unless you use <code>network_mode: \"host\"</code>. All dis dey more prone to error, because you fit expose your server to di internet with just small misconfiguration. Maybe we suppose go back to autossh — feedback go dey very welcome here."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "We don also dey change mind on Varnish vs. Nginx. We currently like Varnish, but e get e own quirks and rough edges. Di same apply to Checkmk: we no love am, but e dey work for now. Weblate dey okay but no dey incredible — I sometimes dey fear say e go lose my data whenever I try to sync am with our git repo. Flask dey good overall, but e get some weird quirks wey don cost plenty time to debug, like configuring custom domains, or issues with e SqlAlchemy integration."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "So far di other tools dey great: we no get serious complaints about MariaDB, ElasticSearch, Gitlab, Zulip, Docker, and Tor. All of dem don get some issues, but nothing wey dey overly serious or time-consuming."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "E don be interesting experience to learn how to set up a robust and resilient shadow library search engine. Plenty more details dey to share for later posts, so mek I know wetin you go like learn more about!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "As olweiz, wí de luk fɔ doneshọn to support dis wọk, so mek yú chẹk di Donate pej on Anna’s Archive. Wí de also luk fɔ oda kain support, laik grants, long-tẹm sponsọs, high-risk peymẹnt providas, prọbabli ivin (tẹstful!) ads. An if yú wan kontribyut yọ taim an skil, wí de olweiz luk fɔ dẹvẹlọpas, transletọs, an so on. Tẹnk yú fɔ yọ intarest an support."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hi, na me be Anna. I create <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di world biggest shadow library. Dis na my personal blog, where me and my teammates dey write about piracy, digital preservation, and more."

#, fuzzy
msgid "blog.index.text2"
msgstr "Connect with me on <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Make you note say dis website na just a blog. We only dey host our own words here. No torrents or other copyrighted files dey hosted or linked here."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blog posts"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Putting 5,998,794 books on IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Warning: dis blog post don dey deprecated. We don decide say IPFS never ready for prime time. We go still link to files on IPFS from Anna’s Archive when possible, but we no go host am ourselves again, nor we dey recommend others to mirror using IPFS. Abeg see our Torrents page if you wan help preserve our collection."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Archive don scrape all of WorldCat (di world biggest library metadata collection) to make a TODO list of books wey need to be preserved.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "One year ago, we <a %(blog)s>set out</a> to answer dis question: <strong>Wetin be di percentage of books wey shadow libraries don permanently preserve?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Once book enter open-data shadow library like <a %(wikipedia_library_genesis)s>Library Genesis</a>, and now <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, e go dey mirrored all over di world (through torrents), thereby practically preserving am forever."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "To answer di question of which percentage of books don dey preserved, we need to know di denominator: how many books dey in total? And ideally we no just get number, but actual metadata. Then we fit not only match dem against shadow libraries, but also <strong>create a TODO list of remaining books to preserve!</strong> We fit even start to dream of a crowdsourced effort to go down dis TODO list."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "We scrape <a %(wikipedia_isbndb_com)s>ISBNdb</a>, and download di <a %(openlibrary)s>Open Library dataset</a>, but di results no dey satisfactory. Di main problem na say no be plenty overlap of ISBNs. See dis Venn diagram from <a %(blog)s>our blog post</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Wi bi vẹri sopraiz se di smọl ovạlap wey de de bitwin ISBNdb an Open Library, wey de tu of dem de kari data from difrent sors, laik web skreps an laibrari rekọds. If dem de do beta wok for faind most ISBNs wey de, dia sẹkul go shọli get big ovạlap, or wan go de insaid di oda. I mek us wanda, how many buks de <em>komplitli autsaid dis sẹkul</em>? Wi nid bigga databẹs."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Na dat taim wey wi set our ai for di bigest buk databẹs for di wold: <a %(wikipedia_worldcat)s>WorldCat</a>. Dis na proprayẹtri databẹs wey di non-profit <a %(wikipedia_oclc)s>OCLC</a> get, wey de kolekt metadata rekọds from laibraris all ova di wold, in ekschej for givin dose laibraris akses to di ful dataset, an mek dem de shọ for end-yuzas’ search rizọlts."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Even do OCLC na non-profit, dia biznis model nid dem to protekt dia databẹs. Well, wi sori to tok, frends for OCLC, wi de giv am all awẹ. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Over di past year, we don meticulously scrape all WorldCat records. At first, we hit one lucky break. WorldCat just dey roll out their complete website redesign (for Aug 2022). Dis one include substantial overhaul of their backend systems, introduce many security flaws. We immediately seize di opportunity, and we fit scrape hundreds of millions (!) of records in mere days."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "After that, security flaws dey slowly fix one by one, until di final one wey we find dey patched about a month ago. By that time we don get pretty much all records, and we dey only go for slightly higher quality records. So we feel say e dey time to release!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Make we look some basic information on di data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Anna’s Archive Containers (AAC)</a>, wey essentially be <a %(jsonlines)s>JSON Lines</a> wey dem compress with <a %(zstd)s>Zstandard</a>, plus some standardized semantics. Dis containers dey wrap various types of records, based on di different scrapes we deploy."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Unknown error don happen. Abeg contact us for %(email)s with screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Dis coin get higher than usual minimum. Abeg select different duration or different coin."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Request no fit complete. Abeg try again in few minutes, and if e still dey happen contact us for %(email)s with screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Error for payment processing. Abeg wait small and try again. If di issue still dey for more than 24 hours, abeg contact us for %(email)s with screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "hidden comment"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "File wahala: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Beta version"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Yu wan report dis user for bad or wrong behavior?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Report bad behavior"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Bad behavior don report:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Yu don report dis user for bad behavior."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Reply"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Please <a %(a_login)s>log in</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Yu don leave comment. E fit take one minute make e show."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Somtin go wrong. Abeg reload di page an try again."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s affected pages"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Not dey visible for Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "No dey visible for Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "No dey visible for Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Mark as broken for Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Miss from Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Mark am “spam” for Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Mark am “bad file” for Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "No fit convert all pages to PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Exiftool no work for dis file"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Book (unknown)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Book (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Book (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Journal article"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standards document"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Magazine"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Comic book"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Musical score"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiobook"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Other"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Partner Server download"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "External download"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "External borrow"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "External borrow (print disabled)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Check Metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Wetin dey inside torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads to AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Czech metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russian State Library"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Title"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Author"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Publisher"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edition"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Year wey dem publish am"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Original filename"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Description and metadata comments"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partner Server downloads temporarily not available for this file."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Fast Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recommended)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(no browser verification or waitlists)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Slow Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(slightly faster but with waitlist)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(no waitlist, but can be very slow)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(also click “GET” at the top)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(click “GET” at the top)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "their ads are known to contain malicious software, so use an ad blocker or don’t click ads"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC files fit no dey reliable to download)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library for Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(you go need Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Borrow from the Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(print disabled patrons only)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI wey dem fit no get for Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "kolekshon"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Bulk torrent downloads"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(na for experts only)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Sabi Anna’s Archive for ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Sabi different databases for ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Find original record for ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Sabi Anna’s Archive for Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Find original record for Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Sabi Anna’s Archive for OCLC (WorldCat) number"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Find original record for WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Sabi Anna’s Archive for DuXiu SSID number"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Sabi manually for DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Sabi Anna’s Archive for CADAL SSNO number"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Find original record for CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Sabi Anna’s Archive for DuXiu DXID number"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(no need browser verification)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Czech metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "description"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternative filename"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Anoda title"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Anoda author"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternative publisher"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternative edition"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternative extension"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata comments"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternative description"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "date open sourced"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dis na record of file from Internet Archive, no be directly downloadable file. You fit try borrow di book (link below), or use dis URL when <a %(a_request)s>you dey request file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "If you get dis file and e no dey available for Anna’s Archive, consider <a %(a_request)s>to upload am</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nomba %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dis na metadata record, e no be downloadable file. You fit use dis URL when <a %(a_request)s>you dey request file</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata from linked record"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Improve metadata on Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Warning: multiple linked records:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Improve metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Report file quality"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Download time"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Website:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Search Anna’s Archive for “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Codes Explorer:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "View in Codes Explorer “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Read more…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Borrow (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Explore metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Comments (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Lists (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Stats (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Technical details"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Dis file fit get wahala, dem don hide am from one source library.</span> Sometimes na request from di copyright holder, sometimes na because better alternative dey available, but sometimes na because wahala dey with di file itself. E fit still dey okay to download, but we recommend make you first search for alternative file. More details:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "A better version of this file might be available at %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "If you still want to download this file, be sure to only use trusted, updated software to open it."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Fast downloads"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Fast downloads</strong> Become a <a %(a_membership)s>member</a> to support the long-term preservation of books, papers, and more. To show our gratitude for your support, you get fast downloads. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "If you donate this month, you go get <strong>double</strong> di number of fast downloads."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Fast downloads</strong> You have %(remaining)s left today. Thanks for being a member! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Fast downloads</strong> You’ve run out of fast downloads for today."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Fast downloads</strong> You downloaded this file recently. Links remain valid for a while."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Option #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(no redirect)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(open for viewer)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Tell am to your friend, and both you and your friend go get %(percentage)s%% bonus fast downloads!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Learn more…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Slow downloads"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "From trusted partners."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "More information dey for <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(e fit need <a %(a_browser)s>browser verification</a> — unlimited downloads!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Afta yu download:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Open am for our viewer"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "show external downloads"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "External downloads"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "No downloads found."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "All download options get the same file, and e suppose safe to use. But, always dey careful when you dey download files from internet, especially from sites wey no be Anna’s Archive. For example, make sure say your devices dey updated."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "For big files, we dey recommend make you use download manager to stop any wahala."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Recommended download managers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "You go need ebook or PDF reader to open di file, depend on di file format."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Recommended ebook readers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Anna’s Archive online viewer"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Use online tools to change between formats."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Recommended conversion tools: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "You fit send both PDF and EPUB files to your Kindle or Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Recommended tools: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon‘s “Send to Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz‘s “Send to Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Support authors and libraries"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "If yu laik dis an yu fit afod am, tink about to buy di orijinal, or support di authos direct."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "If dis dey availabul for yu local libry, tink about to borrow am for free dia."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "File quality"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Help out the community by reporting the quality of this file! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Report file issue (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Great file quality (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Add comment (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "What is wrong with this file?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Please use the <a %(a_copyright)s>DMCA / Copyright claim form</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Describe the issue (required)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Issue description"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 of a better version of this file (if applicable)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Fill this in if there is another file that closely matches this file (same edition, same file extension if you can find one), which people should use instead of this file. If you know of a better version of this file outside of Anna’s Archive, then please <a %(a_upload)s>upload it</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "You can get the md5 from the URL, e.g."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Submit report"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lern how to <a %(a_metadata)s>improve di metadata</a> for dis file yasef."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Tank yu for submit yua report. E go show for dis page, an Anna go review am manually (until we get beta moderation system)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Somtin go wrong. Abeg reload di page an try again."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "If dis file get beta quality, yu fit discuss anytin about am here! If e no get, abeg use di “Report file issue” button."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "I love dis book!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Leave comment"

#, fuzzy
msgid "common.english_only"
msgstr "Text below continues in English."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“File MD5” na hash wey dem compute from di file content, an e dey reasonably unique based on dat content. All shadow libraries wey we don index here dey use MD5s to identify files."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "File fit show for multiple shadow libraries. For information about di various datasets wey we don compile, see di <a %(a_datasets)s>Datasets page</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dis na file wey di <a %(a_ia)s>IA’s Controlled Digital Lending</a> library dey manage, an Anna’s Archive don index am for search. For information about di various datasets wey we don compile, see di <a %(a_datasets)s>Datasets page</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "For information about dis particular file, check out im <a %(a_href)s>JSON file</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Wahala dey load dis page"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Abeg refresh make you try again. <a %(a_contact)s>Contact us</a> if di wahala still dey for many hours."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "No dey"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” no dey our database."

#, fuzzy
msgid "page.login.title"
msgstr "Log in / Register"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Browser verification"

#, fuzzy
msgid "page.login.text1"
msgstr "To stop spam-bots from creating plenty accounts, we need to verify your browser first."

#, fuzzy
msgid "page.login.text2"
msgstr "If you dey caught for infinite loop, we recommend make you install <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "E fit also help make you turn off ad blockers and other browser extensions."

#, fuzzy
msgid "page.codes.title"
msgstr "Codes"

#, fuzzy
msgid "page.codes.heading"
msgstr "Codes Explorer"

#, fuzzy
msgid "page.codes.intro"
msgstr "Check di codes wey dem tag records with, by prefix. Di “records” column dey show di number of records wey dem tag with codes wey get di given prefix, as e dey for di search engine (including metadata-only records). Di “codes” column dey show how many actual codes get di given prefix."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Dis page fit take small time to generate, na why e need Cloudflare captcha. <a %(a_donate)s>Members</a> fit skip di captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Abeg no scrape dis pages. Instead we recommend <a %(a_import)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases, and run our <a %(a_software)s>open source code</a>. Di raw data fit manually explore through JSON files like <a %(a_json_file)s>dis one</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefix"

#, fuzzy
msgid "common.form.go"
msgstr "Go"

#, fuzzy
msgid "common.form.reset"
msgstr "Reset"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Sabi Anna’s Archive"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Warning: code get incorrect Unicode characters inside, and fit behave anyhow for different situations. Di raw binary fit decode from di base64 representation for di URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Known code prefix “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefix"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Description"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL for a specific code"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” go replace with di code’s value"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generic URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Website"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] ""
msgstr[1] "%(count)s records wey match “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL for specific code: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mọ…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codes wey start with “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indẹks of"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "records"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codes"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Less than %(count)s records"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "For DMCA / copyright claims, use <a %(a_copyright)s>dis form</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Any other ways to contact us about copyright claims go automatically delete."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "We really welcome your feedback and questions!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "But, because of di plenty spam and nonsense emails we dey get, abeg check di boxes to confirm say you understand dis conditions to contact us."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Copyright claims to dis email go ignore; use di form instead."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner servers no dey available because dem don close hosting. Dem go dey up again soon."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Membership go dey extend as e suppose be."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "No email us to <a %(a_request)s>request books</a><br>or small (<10k) <a %(a_upload)s>uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "If you get any question about account or donation, abeg add your account ID, screenshots, receipts, and as much information as you fit add. We dey check our email every 1-2 weeks, so if you no add this information, e go delay any resolution."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Show email"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Copyright claim form"

#, fuzzy
msgid "page.copyright.intro"
msgstr "If you get DMCA or oda copyright claim, abeg fill dis form as correct as possible. If you get any wahala, abeg contact us for our special DMCA address: %(email)s. Note say claims wey dem email go dis address no go process, e dey only for questions. Abeg use di form below to submit your claims."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs for Anna’s Archive (required). One per line. Abeg only put URLs wey describe di exact same edition of di book. If you wan make claim for multiple books or multiple editions, abeg submit dis form multiple times."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Claims wey bundle multiple books or editions together go reject."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Your name (required)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Address (required)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Phone number (required)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (required)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Clear description of di source material (required)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs of source material (if applicable). One per line. Abeg only put di ones wey match di edition wey you dey report copyright claim for."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs of source material, one per line. Abeg take small time search Open Library for your source material. Dis go help us verify your claim."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs to source material, one per line (required). Abeg put as many as possible, to help us verify your claim (e.g. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Statement and signature (required)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Submit claim"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Thank you for submitting your copyright claim. We go review am as soon as possible. Abeg reload di page to file another one."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Something go wrong. Abeg reload di page and try again."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "If you are interested in mirroring this dataset for <a %(a_archival)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, please contact us."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Our mission na to archive all di books for di world (as well as papers, magazines, etc), an make dem widely accessible. We believe say all books suppose dey mirrored far an wide, to ensure redundancy an resiliency. Na why we dey pool together files from different sources. Some sources dey completely open an fit mirror in bulk (like Sci-Hub). Others dey closed an protective, so we dey try scrape dem to “liberate” their books. Others dey somewhere in between."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "All our data fit <a %(a_torrents)s>torrent</a>, an all our metadata fit <a %(a_anna_software)s>generate</a> or <a %(a_elasticsearch)s>download</a> as ElasticSearch an MariaDB databases. Di raw data fit manually explore through JSON files like <a %(a_dbrecord)s>dis</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Overview"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Below na quick overview of di sources of di files for Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Source"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Size"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% mirrored by AA / torrents dey available"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentages of number of files"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Last updated"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction and Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] ""
msgstr[1] "%(count)s files"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: e don freeze since 2021; most dey available through torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: small small additions since then</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excluding “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fiction torrents dey behind (though IDs ~4-6M never torrented since dem dey overlap with our Zlib torrents)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Di “Chinese” kolekshon we dey Z-Library be like our DuXiu kolekshon, but wit different MD5s. We no dey put dis files for torrents to avoid duplication, but we still dey show dem for our search index."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ of files fit search."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excluding duplicates"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Becos di shadow libraries dey often sync data from each other, e get plenty overlap between di libraries. Na why di numbers no dey add up to di total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Di “mirrored and seeded by Anna’s Archive” percentage dey show how many files we dey mirror by ourself. We dey seed those files in bulk through torrents, and make dem available for direct download through partner websites."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Source libraries"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Som librari dem dey promote di bulk sharing of dia data through torrents, while some no dey quick share dia collection. For di latter case, Anna’s Archive dey try scrape dia collections, and make dem available (see our <a %(a_torrents)s>Torrents</a> page). Some dey in-between situations, for example, where librari dem wan share, but dem no get di resources to do so. For those cases, we dey try help out."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Below na overview of how we dey interface with di different librari dem."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Source"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Files"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Daily <a %(dbdumps)s>HTTP database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automated torrents for <a %(nonfiction)s>Non-Fiction</a> and <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna’s Archive manages a collection of <a %(covers)s>book cover torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub has frozen new files since 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata dumps available <a %(scihub1)s>here</a> and <a %(scihub2)s>here</a>, as well as as part of the <a %(libgenli)s>Libgen.li database</a> (which we use)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Data torrents available <a %(scihub1)s>here</a>, <a %(scihub2)s>here</a>, and <a %(libgenli)s>here</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Some new files are <a %(libgenrs)s>being</a> <a %(libgenli)s>added</a> to Libgen’s “scimag”, but not enough to warrant new torrents"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Quarterly <a %(dbdumps)s>HTTP database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiction torrents are shared with Libgen.rs (and mirrored <a %(libgenli)s>here</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna’s Archive an' Libgen.li dey manage kolekshon of <a %(comics)s>comic books</a>, <a %(magazines)s>magazines</a>, <a %(standarts)s>standard documents</a>, an' <a %(fiction)s>fiction (wey don diverge from Libgen.rs)</a> together."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Dia “fiction_rus” kolekshon (Russian fiction) no get dia own torrents, but oda torrents dey cover am, an' we dey keep <a %(fiction_rus)s>mirror</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna’s Archive and Z-Library collaboratively manage a collection of <a %(metadata)s>Z-Library metadata</a> and <a %(files)s>Z-Library files</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Some metadata available through <a %(openlib)s>Open Library database dumps</a>, but those don’t cover the entire IA collection"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s No get metadata dumps wey easy to access for dia whole collection"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna’s Archive dey manage collection of <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Files dey available for borrowing only for small time, with different access restrictions"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna’s Archive dey manage collection of <a %(ia)s>IA files</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Different metadata databases dey scatter for Chinese internet; but dem dey often be paid databases"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s No get metadata dumps wey easy to access for dia whole collection."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’s Archive dey manage collection of <a %(duxiu)s>DuXiu metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Different file databases dey scatter for Chinese internet; but dem dey often be paid databases"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Most files dey only accessible with premium BaiduYun accounts; download speed dey slow."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Archive dey manage collection of <a %(duxiu)s>DuXiu files</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Different smaller or one-off sources. We dey encourage people to upload to other shadow libraries first, but sometimes people get collections wey too big for others to sort through, but no big enough to get their own category."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Metadata-only sources"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "We dey also enrich our collection with metadata-only sources, which we fit match to files, e.g. using ISBN numbers or other fields. Below na overview of those. Again, some of these sources dey completely open, while for others we dey scrape dem."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Our inspiration for collecting metadata is Aaron Swartz’ goal of “one web page for every book ever published”, for which he created <a %(a_openlib)s>Open Library</a>. That project has done well, but our unique position allows us to get metadata that they can’t. Another inspiration was our desire to know <a %(a_blog)s>how many books there are in the world</a>, so we can calculate how many books we still have left to save."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Note say for metadata search, we dey show di original records. We no dey do any merging of records."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Last updated"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Monthly <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Not available directly in bulk, protected against scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’s Archive manages a collection of <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Unified database"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "We dey combine all di sources above into one unified database wey we dey use to serve this website. This unified database no dey available directly, but since Anna’s Archive dey fully open source, e fit dey fairly easily <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases. Di scripts on that page go automatically download all di requisite metadata from di sources wey we mention above."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "If you wan explore our data before you run those scripts locally, you fit look at our JSON files, wey link further to other JSON files. <a %(a_json)s>This file</a> na good starting point."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adapted from our <a %(a_href)s>blog post</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> na big database of scanned books, wey <a %(superstar_link)s>SuperStar Digital Library Group</a> create. Most of dem na academic books, dem scan am so dat universities and libraries fit get dem for digital form. For our English-speaking audience, <a %(princeton_link)s>Princeton</a> and the <a %(uw_link)s>University of Washington</a> get better overview. One correct article dey wey give more background: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "The books from Duxiu don dey pirated for Chinese internet for long. Usually, dem dey sell am for less than one dollar by resellers. Dem dey usually distribute am using the Chinese version of Google Drive, wey dem don hack many times to allow for more storage space. Some technical details dey <a %(link1)s>here</a> and <a %(link2)s>here</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Even though dem don semi-publicly distribute the books, e still dey hard to get dem in bulk. We put this one high for our TODO-list, and we allocate many months of full-time work for am. But, for late 2023, one incredible, amazing, and talented volunteer reach out to us, tell us say dem don do all this work already — at great expense. Dem share the full collection with us, without expecting anything in return, except say we go guarantee long-term preservation. Truly remarkable."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resources"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total files: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Total filesize: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Files wey Anna’s Archive mirror: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Last updated: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents by Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Example record for Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Our blog post about this data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scripts for importing metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers format"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "More information from our volunteers (raw notes):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dis dataset dey closely related to di <a %(a_datasets_openlib)s>Open Library dataset</a>. E contain scrape of all metadata and large portion of files from di IA’s Controlled Digital Lending Library. Updates dey release in di <a %(a_aac)s>Anna’s Archive Containers format</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Dis records dey refer directly from di Open Library dataset, but e still contain records wey no dey Open Library. We still get number of data files wey community members don scrape over di years."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Di collection get two parts. You need both parts to get all data (except superseded torrents, wey dem cross out for di torrents page)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "our first release, before we standardized on the <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. E get metadata (as json and xml), pdfs (from acsm and lcpdf digital lending systems), and cover thumbnails."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "incremental new releases, wey dey use AAC. E only get metadata with timestamps after 2023-01-01, since the rest don already dey covered by “ia”. Also all pdf files, this time from the acsm and “bookreader” (IA’s web reader) lending systems. Even though the name no dey exactly right, we still dey put bookreader files into the ia2_acsmpdf_files collection, since dem no dey the same."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Main %(source)s website"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digital Lending Library"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata documentation (most fields)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN kontrị infọmation"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Di International ISBN Agency dey release di ranges wey e don allocate to national ISBN agencies. From dis one, we fit sabi which kontrị, region, or language group dis ISBN belong. We dey use dis data indirectly now, through di <a %(a_isbnlib)s>isbnlib</a> Python library."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resources"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Last updated: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN website"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "For di backstory of di different Library Genesis forks, see di page for di <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Di Libgen.li get most of di same content and metadata as di Libgen.rs, but e get some collections on top of dis one, like comics, magazines, and standard documents. E don also integrate <a %(a_scihub)s>Sci-Hub</a> into its metadata and search engine, which na wetin we dey use for our database."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Di metadata for dis library dey freely available <a %(a_libgen_li)s>at libgen.li</a>. But, dis server dey slow and e no support resuming broken connections. Di same files dey also available on <a %(a_ftp)s>an FTP server</a>, wey dey work better."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents dey available for most of di extra content, especially torrents for comics, magazines, an' standard documents wey dem release with Anna’s Archive."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Di fiction kolekshon get dia own torrents (wey don diverge from <a %(a_href)s>Libgen.rs</a>) wey start for %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Accordin' to di Libgen.li admin, di “fiction_rus” (Russian fiction) kolekshon suppose dey covered by torrents wey dem dey release regularly from <a %(a_booktracker)s>booktracker.org</a>, especially di <a %(a_flibusta)s>flibusta</a> an' <a %(a_librusec)s>lib.rus.ec</a> torrents (wey we dey mirror <a %(a_torrents)s>here</a>, even though we never fit establish which torrents dey correspond to which files)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistics for all di kolekshons fit dey found <a %(a_href)s>on libgen's website</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fikshon sef dey show sey e don change, but witout new torrents. E be like sey dis don happen since early 2022, but we never confirm am."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certain ranges wey no get torrents (like fiction ranges f_3463000 to f_4260000) fit be Z-Library (or oda duplicate) files, but we fit wan' do some deduplication an' make torrents for lgli-unique files inside dis ranges."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Make you note say di torrent files wey dey refer to “libgen.is” na mirrors of <a %(a_libgen)s>Libgen.rs</a> (“.is” na different domain wey Libgen.rs dey use)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "One helpful resource to use di metadata na <a %(a_href)s>this page</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiction torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Comics torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Magazine torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standard document torrents on Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russian fiction torrents on Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata field information"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror of other torrents (and unique fiction and comics torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Discussion forum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Our blog post about di comic books release"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Di quick story of di different Library Genesis (or “Libgen”) forks, be say over time, di different people wey dey involved with Library Genesis get quarrel, and dem go their separate ways."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Di “.fun” version na di original founder create am. Dem dey revamp am for new, more distributed version."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Di “.rs” version get very similar data, and dem dey consistently release their collection in bulk torrents. E dey roughly split into “fiction” and “non-fiction” section."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originally for “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Di <a %(a_li)s>“.li” version</a> get massive collection of comics, as well as other content, wey no dey (yet) available for bulk download through torrents. E get separate torrent collection of fiction books, and e contain di metadata of <a %(a_scihub)s>Sci-Hub</a> for its database."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Acording to dis <a %(a_mhut)s>forum post</a>, Libgen.li bin dey orijinaly host for “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> in some sense na fork of Library Genesis too, though dem use different name for their project."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Dis page na about di “.rs” version. E dey known for consistently publishing both its metadata and di full contents of its book catalog. Its book collection dey split between fiction and non-fiction portion."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "One helpful resource to use di metadata na <a %(a_metadata)s>this page</a> (blocks IP ranges, VPN fit dey required)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "As of 2024-03, new torrents are being posted in <a %(a_href)s>dis forum thread</a> (e dey block IP ranges, VPN fit dey required)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-Fiction torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiction torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata field information"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-fiction torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiction torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Discussion forum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents by Anna’s Archive (book covers)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Our blog about di book covers release"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis dey known for how dem dey generously make dia data available in bulk through torrents. Our Libgen collection get auxiliary data wey dem no dey release directly, in partnership with dem. Much thanks to everybody wey dey involved with Library Genesis for working with us!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Dis <a %(blog_post)s>first release</a> small well well: about 300GB of book covers from di Libgen.rs fork, both fiction and non-fiction. Dem dey organized di same way as how dem dey appear for libgen.rs, e.g.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s for non-fiction book."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s for fiction book."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Just like with di Z-Library collection, we put dem all inside big .tar file, wey you fit mount using <a %(a_ratarmount)s>ratarmount</a> if you wan serve di files directly."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> na proprietary database by di non-profit <a %(a_oclc)s>OCLC</a>, wey dey gather metadata records from libraries all over di world. E fit be di biggest library metadata collection for di world."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "October 2023, fess release:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "For October 2023 we <a %(a_scrape)s>release</a> comprehensive scrape of di OCLC (WorldCat) database, for di <a %(a_aac)s>Anna’s Archive Containers format</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents by Anna’s Archive"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Our blog post about dis data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library na open source project by di Internet Archive to catalog every book for di world. E get one of di world’s largest book scanning operations, and e get many books available for digital lending. E book metadata catalog dey freely available for download, and e dey included for Anna’s Archive (though e no dey currently for search, except if you explicitly search for an Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Release 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dis na dump of plenty calls to isbndb.com during September 2022. We try cover all ISBN ranges. Dem be about 30.9 million records. For their website, dem claim say dem actually get 32.6 million records, so we fit don miss some, or <em>dem</em> fit dey do something wrong."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Di JSON responses dey almost raw from their server. One data quality issue wey we notice be say for ISBN-13 numbers wey start with different prefix than “978-”, dem still include “isbn” field wey simply be di ISBN-13 number with di first 3 numbers cut off (and di check digit recalculated). Dis one dey obviously wrong, but na so dem dey do am, so we no alter am."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Another potential issue wey you fit face be say di “isbn13” field get duplicates, so you no fit use am as primary key for database. “isbn13”+“isbn” fields combined dey unique."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "For bakgraund on Sci-Hub, abeg refer to im <a %(a_scihub)s>official website</a>, <a %(a_wikipedia)s>Wikipedia page</a>, an dis <a %(a_radiolab)s>podcast interview</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Note say Sci-Hub don <a %(a_reddit)s>freeze since 2021</a>. E don freeze before, but for 2021 dem add few million papers. Still, some small number of papers dey add to di Libgen “scimag” collections, but e no reach to make new bulk torrents."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "We dey use di Sci-Hub metadata as Libgen.li provide am for dia “scimag” collection. We still dey use di <a %(a_dois)s>dois-2022-02-12.7z</a> dataset."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Note say di “smarch” torrents don <a %(a_smarch)s>deprecate</a> and dem no dey include am for our torrents list."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents for Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata an torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents for Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents for Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Updates for Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia page"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast interview"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uploads to Anna’s Archive"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Ovasabi fọm <a %(a1)s>datasets piji</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Different smaller or one-off sources. We dey encourage people to upload to other shadow libraries first, but sometimes people get collections wey too big for others to sort through, but no big enough to get their own category."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "The “upload” collection don split into smaller subcollections, wey dem indicate for the AACIDs and torrent names. All subcollections first deduplicate against the main collection, but the metadata “upload_records” JSON files still get plenty references to the original files. Non-book files dem also remove from most subcollections, and dem no dey usually <em>note</em> am for the “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Many subcollections demself get sub-sub-collections (e.g. from different original sources), wey dem represent as directories for the “filepath” fields."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "The subcollections be:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkolekshọn"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notis"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "browse"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "search"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "From <a %(a_href)s>aaaaarg.fail</a>. E dey fairly complete. From our volunteer “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "From an <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. E get high overlap with existing papers collections, but very few MD5 matches, so we decide to keep am completely."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Skreip fọ <q>iRead eBooks</q> (= fọnetikali <q>ai rit i-books</q>; airitibooks.com), we volọntia <q>j</q> du. I match wit <q>airitibooks</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Fọ kolekshọn <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sọmtin fọm di orijina sọns, sọmtin fọm di-eye.eu, sọmtin fọm oda mirọs."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "From a private books torrent website, <a %(a_href)s>Bibliotik</a> (dem dey often call am “Bib”), of which books dem bundle into torrents by name (A.torrent, B.torrent) and distribute through the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "From our volunteer “bpb9v”. For more information about <a %(a_href)s>CADAL</a>, see the notes for our <a %(a_duxiu)s>DuXiu dataset page</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "More from our volunteer “bpb9v”, mostly DuXiu files, as well as a folder “WenQu” and “SuperStar_Journals” (SuperStar na the company wey dey behind DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "From our volunteer “cgiym”, Chinese texts from various sources (represented as subdirectories), including from <a %(a_href)s>China Machine Press</a> (a major Chinese publisher)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Non-Chinese collections (represented as subdirectories) from our volunteer “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Skreip fọ buks wey tok abaut Chinese akitẹktshọ, we volọntia <q>cm</q> du: <q>A tek am bai eksplọit netwọk vọnẹrabiliti fọ di pọnlishin haus, bọt dat luphọl don klọs</q>. I match wit <q>chinese_architecture</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Books from academic publishing house <a %(a_href)s>De Gruyter</a>, collected from a few large torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape of <a %(a_href)s>docer.pl</a>, a polish file sharing website wey dey focus on books and other written works. Scraped for late 2023 by volunteer “p”. We no get good metadata from the original website (not even file extensions), but we filter for book-like files and we fit often extract metadata from the files demself."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, directly from DuXiu, collected by volunteer “w”. Only recent DuXiu books dey available directly through ebooks, so most of these must be recent."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Remaining DuXiu files from volunteer “m”, wey no dey in the DuXiu proprietary PDG format (the main <a %(a_href)s>DuXiu dataset</a>). Collected from many original sources, unfortunately without preserving those sources in the filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Skreip fọ erọtik buks, we volọntia <q>do no harm</q> du. I match wit <q>hentai</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collection wey volunteer “t” scrape from Japanese Manga publisher."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Selected judicial archives of Longquan</a>, provided by volunteer “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape of <a %(a_href)s>magzdb.org</a>, ally of Library Genesis (e dey linked for libgen.rs homepage) but dem no want provide their files directly. Volunteer “p” obtain am for late 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Different small uploads, too small to be their own subcollection, but represented as directories."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks fọm AvaxHome, wan Rọshian fayl shẹrin website."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Akaiv fọ nyuspepaz an magazins. I match wit <q>newsarch_magz</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Skreip fọ di <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collection of volunteer “o” wey collect Polish books directly from original release (“scene”) websites."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Combined collections of <a %(a_href)s>shuge.org</a> by volunteers “cgiym” and “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (named after the fictional library), scraped in 2022 by volunteer “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-collections (represented as directories) from volunteer “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (by <a %(a_sikuquanshu)s>Dizhi(迪志)</a> for Taiwan), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: “This site mainly focus on sharing high quality ebook files, some of which na the owner typeset by himself. The owner was <a %(a_arrested)s>arrested</a> for 2019 and someone make a collection of files wey he share.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Remaining DuXiu files from volunteer “woz9ts”, wey no dey the DuXiu proprietary PDG format (still to be converted to PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents wey Anna’s Archive get"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library scrape"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library start from di <a %(a_href)s>Library Genesis</a> community, and dem first use dia data take start. Since den, dem don professionalize well well, and dem get more modern interface. Na why dem fit get plenty donations, both money to take improve dia website, and new books donations. Dem don gather big collection join Library Genesis own."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Update as of February 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "For late 2022, dem arrest di people wey dem say start Z-Library, and United States authorities seize dia domains. Since den, di website don dey try come back online small small. Nobody sabi who dey run am now."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Di collection get three parts. Di original description pages for di first two parts dey preserved below. You need all three parts to get all data (except superseded torrents, wey dem cross out for di torrents page)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: our first release. Dis na di very first release of wetin dem call “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: second release, dis time with all files wrapped inside .tar files."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: incremental new releases, using di <a %(a_href)s>Anna’s Archive Containers (AAC) format</a>, now released in collaboration with di Z-Library team."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents wey Anna’s Archive get (metadata + content)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Example record for Anna’s Archive (original collection)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Example record for Anna’s Archive (“zlib3” collection)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Main website"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domain"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blog post about Release 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blog post about Release 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib releases (original description pages)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Di first mirror wey dem painstakingly get na for di course of 2021 and 2022. For dis point, e don small outdated: e dey reflect di state of di collection for June 2021. We go update am for future. Right now, we dey focus on getting dis first release out."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Since Library Genesis don already dey preserved with public torrents, and e dey included for Z-Library, we do basic deduplication against Library Genesis for June 2022. For dis, we use MD5 hashes. E get as e be, plenty duplicate content dey for di library, like multiple file formats with di same book. Dis one hard to detect accurately, so we no dey do am. After di deduplication, we still get over 2 million files, totalling just under 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Di collection get two parts: one MySQL “.sql.gz” dump of di metadata, and di 72 torrent files of around 50-100GB each. Di metadata contain di data as Z-Library website report (title, author, description, filetype), as well as di actual filesize and md5sum wey we observe, since sometimes dem no dey agree. E be like say some files for Z-Library get incorrect metadata. We fit also get some files wey we download incorrectly for some isolated cases, we go try detect and fix am for future."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Di large torrent files contain di actual book data, with di Z-Library ID as di filename. Di file extensions fit reconstruct using di metadata dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Di collection na mix of non-fiction and fiction content (no separate like for Library Genesis). Di quality dey vary well well."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Dis first release don fully available now. Note say di torrent files dey only available through our Tor mirror."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Release 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "We don get all books wey dem add to Z-Library between our last mirror and August 2022. We don also go back and scrape some books wey we miss di first time around. All in all, dis new collection na about 24TB. Again, dis collection don deduplicated against Library Genesis, since torrents don already dey available for dat collection."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Di data dey organized similar to di first release. E get one MySQL “.sql.gz” dump of di metadata, wey also include all di metadata from di first release, thereby superseding am. We also add some new columns:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: whether dis file dey already for Library Genesis, for either di non-fiction or fiction collection (matched by md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: which torrent dis file dey inside."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: set when we no fit download di book."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "We mention dis last time, but just to clarify: “filename” and “md5” na di actual properties of di file, whereas “filename_reported” and “md5_reported” na wetin we scrape from Z-Library. Sometimes dis two no dey agree with each other, so we include both."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "For dis release, we change di collation to “utf8mb4_unicode_ci”, wey suppose compatible with older versions of MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Di data files dey similar to last time, though dem big pass. We no fit bother create plenty smaller torrent files. “pilimi-zlib2-0-14679999-extra.torrent” contain all di files wey we miss for di last release, while di other torrents na all new ID ranges. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> We make most of our torrents too big, causing torrent clients to struggle. We don remove dem and release new torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> E still get too many files, so we wrap dem in tar files and release new torrents again."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Release 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dis na single extra torrent file. E no get any new information, but e get some data wey fit take time to compute. Dat one make am convenient to get, because to download dis torrent dey fast pass to compute am from scratch. E contain SQLite indexes for the tar files, for use with <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Frequently Asked Questions (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Wetin be Anna’s Archive?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> na non-profit project wey get two goals:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservation:</strong> To back up all knowledge and culture wey humanity get.</li><li><strong>Access:</strong> To make this knowledge and culture available to anybody for the world.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "All our <a %(a_code)s>code</a> and <a %(a_datasets)s>data</a> dey completely open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Preservation"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "We dey preserve books, papers, comics, magazines, and more, by bringing these materials from different <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>, official libraries, and other collections together for one place. All this data go dey preserved forever by making am easy to duplicate am in bulk — using torrents — wey go result in many copies around the world. Some shadow libraries dey already do this themselves (e.g. Sci-Hub, Library Genesis), while Anna’s Archive dey “liberate” other libraries wey no dey offer bulk distribution (e.g. Z-Library) or no be shadow libraries at all (e.g. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "This wide distribution, combined with open-source code, dey make our website strong against takedowns, and e dey ensure the long-term preservation of humanity’s knowledge and culture. Learn more about <a href=\"/datasets\">our datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "We estimate say we don preserve about <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% of the world’s books</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Access"

#, fuzzy
msgid "page.home.access.text"
msgstr "We dey work with partners to make our collections easy and free for anybody to access. We believe say everybody get right to the collective wisdom of humanity. And <a %(a_search)s>no be at the expense of authors</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Hourly downloads in the last 30 days. Hourly average: %(hourly)s. Daily average: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "We strongly believe in the free flow of information, and preservation of knowledge and culture. With this search engine, we dey build on the shoulders of giants. We deeply respect the hard work of the people wey don create the different shadow libraries, and we hope say this search engine go broaden their reach."

#, fuzzy
msgid "page.about.text3"
msgstr "To stay updated on our progress, follow Anna on <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. For questions and feedback abeg contact Anna at %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "How I fit help?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Follow us on <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Spread the word about Anna’s Archive on Twitter, Reddit, Tiktok, Instagram, for your local cafe or library, or anywhere you go! We no believe in gatekeeping — if dem take us down we go just pop up somewhere else, since all our code and data dey fully open source.</li><li>3. If you fit, consider <a href=\"/donate\">donating</a>.</li><li>4. Help <a href=\"https://translate.annas-software.org/\">translate</a> our website into different languages.</li><li>5. If you be software engineer, consider contributing to our <a href=\"https://annas-software.org/\">open source</a>, or seeding our <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "We don get Matrix chanel wey dey sync now for %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. If you be security researcher, we fit use your skills both for offense and defense. Check out our <a %(a_security)s>Security</a> page."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. We dey look for experts in payments for anonymous merchants. You fit help us add more convenient ways to donate? PayPal, WeChat, gift cards. If you know anybody, abeg contact us."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Wi de always dey look for more server capacity."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. You fit help by reporting file issues, leaving comments, and creating lists right for dis website. You fit also help by <a %(a_upload)s>uploading more books</a>, or fixing up file issues or formatting of existing books."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Create or help maintain di Wikipedia page for Anna’s Archive for your language."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. We dey look to place small, tasteful advertisements. If you wan advertise for Anna’s Archive, abeg let us know."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "We go love make people set up <a %(a_mirrors)s>mirrors</a>, and we go support dis financially."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "For more extensive information on how to volunteer, see our <a %(a_volunteering)s>Volunteering & Bounties</a> page."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Wetin make di slow downloads slow?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "We no get enough resources to give everybody for di world high-speed downloads, as much as we go like. If one rich benefactor go like step up and provide dis for us, e go dey incredible, but until then, we dey try our best. We be non-profit project wey dey barely sustain itself through donations."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Na why we implement two systems for free downloads, with our partners: shared servers with slow downloads, and slightly faster servers with waitlist (to reduce di number of people wey dey download at di same time)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "We also get <a %(a_verification)s>browser verification</a> for our slow downloads, because otherwise bots and scrapers go abuse them, make things even slower for legitimate users."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Note say, when you dey use di Tor Browser, you fit need to adjust your security settings. On di lowest of di options, wey dem call “Standard”, di Cloudflare turnstile challenge dey succeed. On di higher options, wey dem call “Safer” and “Safest”, di challenge dey fail."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "If you download big files, sometimes e fit break for middle. We dey recommend make you use download manager (like JDownloader) to continue big downloads automatically."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donation FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Do memberships automatically renew?</div> Memberships <strong>no dey</strong> automatically renew. You fit join for as long or short as you want."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>I fit upgrade my membership or get multiple memberships?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Do you have other payment methods?</div> Currently no. Plenty people no want archives like dis to exist, so we gatz dey careful. If you fit help us set up other (more convenient) payment methods safely, abeg contact us for %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Wetin di ranges per month mean?</div> You fit reach di lower side of a range by applying all di discounts, like choosing a period wey long pass one month."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>What do you spend donations on?</div> 100%% dey go to preserving and making accessible di world's knowledge and culture. Currently we dey spend am mostly on servers, storage, and bandwidth. No money dey go to any team members personally."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Can I make a large donation?</div> E go dey amazing! For donations wey pass few thousand dollars, abeg contact us directly for %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Can I make a donation without becoming a member?</div> Sure thing. We dey accept donations of any amount for dis Monero (XMR) address: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "How I go upload new books?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternatively, you fit upload them to Z-Library <a %(a_upload)s>here</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "For small uploads (up to 10,000 files) abeg upload dem to both %(first)s and %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "For now, we suggest make you upload new books to di Library Genesis forks. Here be <a %(a_guide)s>handy guide</a>. Note say both forks wey we dey index for dis website dey pull from dis same upload system."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "For Libgen.li, make sure say you first login for <a %(a_forum)s >their forum</a> with username %(username)s and password %(password)s, and then return to their <a %(a_upload_page)s >upload page</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "If your email address no dey work for di Libgen forums, we recommend make you use <a %(a_mail)s>Proton Mail</a> (free). You fit also <a %(a_manual)s>manually request</a> for your account to be activated."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Note say mhut.org dey block certain IP ranges, so VPN fit dey required."

#, fuzzy
msgid "page.upload.large.text"
msgstr "If you get large uploads (over 10,000 files) wey no dey accepted by Libgen or Z-Library, abeg contact us for %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "To upload academic papers, abeg also (in addition to Library Genesis) upload to <a %(a_stc_nexus)s>STC Nexus</a>. Dem be di best shadow library for new papers. We never integrate them yet, but we go do am at some point. You fit use their <a %(a_telegram)s>upload bot on Telegram</a>, or contact di address wey dem list for their pinned message if you get too many files to upload dis way."

#, fuzzy
msgid "page.faq.request.title"
msgstr "How I go fit request books?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "For now, we no fit accommodate book requests."

#, fuzzy
msgid "page.request.forums"
msgstr "Abeg make your requests for Z-Library or Libgen forums."

#, fuzzy
msgid "page.request.dont_email"
msgstr "No email us your book requests."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Una dey collect metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Yes, we dey collect am."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "I download 1984 by George Orwell, police go come my door?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "No worry too much, plenty people dey download from websites wey we link to, and e rare well well to enter wahala. But to stay safe, we recommend make you use VPN (paid), or <a %(a_tor)s>Tor</a> (free)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "How I go fit save my search settings?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Select the settings wey you like, leave the search box empty, click “Search”, and then bookmark the page using your browser’s bookmark feature."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Una get mobile app?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "We no get official mobile app, but you fit install this website as app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Click the three-dot menu for top right, and select “Add to Home Screen”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Click the “Share” button for bottom, and select “Add to Home Screen”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Una get API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "We get one stable JSON API for members, to get fast download URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation dey inside JSON itself)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "For other use cases, like to iterate through all our files, build custom search, and so on, we recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. The raw data fit be manually explored <a %(a_explore)s>through JSON files</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Our raw torrents list fit be downloaded as <a %(a_torrents)s>JSON</a> as well."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "I go like help seed, but I no get plenty disk space."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Use di <a %(a_list)s>torrent list generator</a> to generate list of torrents wey need torrenting pass, inside your storage space limits."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Di torrents dey too slow; I fit download di data directly from una?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Yes, see di <a %(a_llm)s>LLM data</a> page."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "I fit download only some of di files, like only one particular language or topic?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Short answer: e no easy."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Long answer:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Most torrents get di files directly, wey mean say you fit tell torrent clients to only download di files wey you need. To know which files to download, you fit <a %(a_generate)s>generate</a> our metadata, or <a %(a_download)s>download</a> our ElasticSearch and MariaDB databases. Unfortunately, some torrent collections get .zip or .tar files for di root, so you need to download di whole torrent before you fit select individual files."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(We get <a %(a_ideas)s>some ideas</a> for the latter case sha.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "No easy to use tools for filtá di torrents no dey available yet, but we dey welcome contributions."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "How una dey handle duplicates for di torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "We dey try to keep minimal duplication or overlap between di torrents for this list, but e no dey always possible, and e depend well well on di policies of di source libraries. For libraries wey dey put out their own torrents, e dey out of our hands. For torrents wey Anna’s Archive release, we dey deduplicate only based on MD5 hash, wey mean say different versions of di same book no go deduplicate."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "I fit get di torrent list as JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Yes."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "I no see PDFs or EPUBs for di torrents, only binary files? Wetin I go do?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "These na actually PDFs and EPUBs, dem just no get extension for many of our torrents. Two places dey wey you fit find di metadata for torrent files, including di file types/extensions:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Each collection or release get their own metadata. For example, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> get corresponding metadata database wey dem host for di Libgen.rs website. We dey usually link to relevant metadata resources from each collection’s <a %(a_datasets)s>dataset page</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. We recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. These ones get mapping for each record for Anna’s Archive to its corresponding torrent files (if available), under “torrent_paths” for di ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Why my torrent client no fit open some of una torrent files / magnet links?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Some torrent clients no dey support large piece sizes, wey plenty of our torrents get (for newer ones we no dey do am again — even though e dey valid per specs!). So try different client if you run into this, or complain to the makers of your torrent client."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Una get responsible disclosure program?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "We welcome security researchers to search for vulnerabilities for our systems. We be big supporters of responsible disclosure. Contact us <a %(a_contact)s>here</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "We no fit award bug bounties for now, except for vulnerabilities wey get di <a %(a_link)s>potential to compromise our anonymity</a>, for which we dey offer bounties in di $10k-50k range. We go like offer wider scope for bug bounties for di future! Abeg note say social engineering attacks dey out of scope."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "If you dey interested in offensive security, and you wan help archive di world’s knowledge and culture, make sure say you contact us. Plenty ways dey wey you fit help."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "More resources dey about Anna’s Archive?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regila updates"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — our open source code"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Translate on Anna’s Software</a> — our translation system"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — about di data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domains"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — more about us (abeg help keep dis page updated, or create one for your own language!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "How I go report copyright infringement?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "We no dey host any copyrighted materials here. We be search engine, and as such only index metadata wey don already dey publicly available. When you dey download from these external sources, we go suggest make you check di laws for your area with respect to wetin dem allow. We no dey responsible for content wey others dey host."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "If you get complaints about wetin you see here, your best bet na to contact di original website. We dey regularly pull their changes enter our database. If you really think say you get valid DMCA complaint we suppose respond to, abeg fill out di <a %(a_copyright)s>DMCA / Copyright claim form</a>. We dey take your complaints seriously, and we go get back to you as soon as possible."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "I hate how una dey run this project!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "We go also like remind everybody say all our code and data dey completely open source. This one unique for projects like ours — we no sabi any other project with similarly massive catalog wey dey fully open source too. We very much welcome anybody wey think say we dey run our project poorly to take our code and data and set up their own shadow library! We no dey talk this one out of spite or something — we genuinely think say this go dey awesome since e go raise di bar for everybody, and better preserve humanity's legacy."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Yu get uptime monitor?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Abeg see <a %(a_href)s>dis beta project</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "How I fit donate books or oda physical materials?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Abeg send dem to di <a %(a_archive)s>Internet Archive</a>. Dem go preserve dem well."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Hu be Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Na yu be Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Wetin be your favorite books?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Here be some books wey carry special significance to di world of shadow libraries and digital preservation:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "You don run out of fast downloads today."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Become member to use fast downloads."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "We don add Amazon gift cards, credit and debit cards, crypto, Alipay, and WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Full database"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Books, papers, magazines, comics, library records, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Search"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub don <a %(a_paused)s>pause</a> uploading of new papers."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB na di continuation of Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direct akses to %(count)s akademik pepa dem"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Open"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "If yu be <a %(a_member)s>memba</a>, browser verification no dey required."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Long-term archive"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Di datasets wey dem dey use for Anna’s Archive dey completely open, and you fit mirror dem in bulk using torrents. <a %(a_datasets)s>Learn more…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "You fit help well well by seeding torrents. <a %(a_torrents)s>Learn more…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "We get di world’s largest collection of high-quality text data. <a %(a_llm)s>Learn more…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Mirrors: call for volunteers"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 We dey find volunteers"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "As we be non-profit, open-source project, we dey always find people wey go help."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "If you dey run high-risk anonymous payment processor, abeg contact us. We dey also find people wey go place tasteful small ads. All di proceeds go to our preservation efforts."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS downloads"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 All download links for this file: <a %(a_main)s>File main page</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(you fit try many times wit IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 To get faster downloads and skip di browser checks, <a %(a_membership)s>become a member</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 For bulk mirroring of our collection, check out di <a %(a_datasets)s>Datasets</a> and <a %(a_torrents)s>Torrents</a> pages."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM data"

#, fuzzy
msgid "page.llm.intro"
msgstr "E dey well known say LLMs dey perform well with high-quality data. We get the biggest collection of books, papers, magazines, etc for the world, wey be some of the highest quality text sources."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unique scale and range"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Our collection get over hundred million files, including academic journals, textbooks, and magazines. We achieve this scale by combining big existing repositories."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Some of our source collections dey already available in bulk (Sci-Hub, and parts of Libgen). Other sources na we liberate am ourselves. <a %(a_datasets)s>Datasets</a> dey show full overview."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Our collection get millions of books, papers, and magazines from before the e-book era. Big parts of this collection don already dey OCR’ed, and don already get small internal overlap."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "How we fit help"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "We fit provide high-speed access to our full collections, as well as to unreleased collections."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "This na enterprise-level access we fit provide for donations wey dey range tens of thousands USD. We dey also willing to trade this for high-quality collections wey we never get yet."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "We fit refund you if you fit provide us with enrichment of our data, like:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Removing overlap (deduplication)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Text and metadata extraction"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Support long-term archival of human knowledge, while you dey get better data for your model!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contact us</a> make we discuss how we fit work together."

#, fuzzy
msgid "page.login.continue"
msgstr "Continue"

#, fuzzy
msgid "page.login.please"
msgstr "Abeg <a %(a_account)s>login</a> to view dis page.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Archive dey temporarily down for maintenance. Abeg come back in one hour."

#, fuzzy
msgid "page.metadata.header"
msgstr "Improve metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "You fit help preserve books by improving metadata! First, read di background about metadata for Anna’s Archive, and then learn how to improve metadata through linking with Open Library, and earn free membership for Anna’s Archive."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Background"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "When you look one book for Anna’s Archive, you go see different fields: title, author, publisher, edition, year, description, filename, and more. All those pieces of information na <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Since we dey combine books from different <em>source libraries</em>, we dey show any metadata wey dey available for that source library. For example, for one book wey we get from Library Genesis, we go show di title from Library Genesis’ database."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Sometimes one book go dey <em>multiple</em> source libraries, wey fit get different metadata fields. For that case, we go just show di longest version of each field, since that one hopefully get di most useful information! We go still show di other fields below di description, e.g. as ”alternative title” (but only if dem different)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "We dey also extract <em>codes</em> like identifiers and classifiers from di source library. <em>Identifiers</em> dey uniquely represent one particular edition of one book; examples na ISBN, DOI, Open Library ID, Google Books ID, or Amazon ID. <em>Classifiers</em> dey group together multiple similar books; examples na Dewey Decimal (DCC), UDC, LCC, RVK, or GOST. Sometimes these codes dey explicitly linked for source libraries, and sometimes we fit extract dem from di filename or description (primarily ISBN and DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "We fit use identifiers to find records for <em>metadata-only collections</em>, like OpenLibrary, ISBNdb, or WorldCat/OCLC. There dey specific <em>metadata tab</em> for our search engine if you wan browse those collections. We dey use matching records to fill in missing metadata fields (e.g. if title dey miss), or e.g. as “alternative title” (if existing title dey)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "To see exactly where metadata of one book come from, see di <em>“Technical details” tab</em> for one book page. E get link to di raw JSON for that book, with pointers to di raw JSON of di original records."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "For more information, see di following pages: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, and <a %(a_example)s>Example metadata JSON</a>. Finally, all our metadata fit be <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library linking"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "So if you see one file with bad metadata, how you go fit fix am? You fit go di source library and follow their procedures for fixing metadata, but wetin you go do if di file dey multiple source libraries?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "There dey one identifier wey dem dey treat special for Anna’s Archive. <strong>The annas_archive md5 field for Open Library always dey override all other metadata!</strong> Make we first go back small and learn about Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library start for 2006 by Aaron Swartz with di goal of “one web page for every book ever published”. E be like Wikipedia for book metadata: everybody fit edit am, e dey freely licensed, and fit be downloaded in bulk. E be book database wey align well with our mission — in fact, Anna’s Archive don dey inspired by Aaron Swartz’ vision and life."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Instead of reinventing di wheel, we decide to redirect our volunteers towards Open Library. If you see one book wey get incorrect metadata, you fit help out in di following way:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Go to di <a %(a_openlib)s>Open Library website</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Find di correct book record. <strong>WARNING:</strong> make sure say you select di correct <strong>edition</strong>. For Open Library, you get “works” and “editions”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "A “work” fit be “Harry Potter and the Philosopher's Stone”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "An “edition” fit be:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Di 1997 first edition wey Bloomsbery publish wit 256 pages."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Di 2003 paperback edition wey Raincoast Books publish wit 223 pages."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Di 2000 Polish translation “Harry Potter I Kamie Filozoficzn” by Media Rodzina wit 328 pages."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "All di editions get different ISBNs and different contents, so make sure say you select di correct one!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edit di record (or create am if e no dey), and add as much useful information as you fit! You dey here now anyway, so make di record really amazing."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Under “ID Numbers” select “Anna’s Archive” and add di MD5 of di book from Anna’s Archive. Dis na di long string of letters and numbers wey dey after “/md5/” for di URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Try to find other files for Anna’s Archive wey match dis record too, and add dem as well. For di future we fit group dem as duplicates for Anna’s Archive search page."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "When you don finish, write down di URL wey you just update. Once you don update at least 30 records wit Anna’s Archive MD5s, send us an <a %(a_contact)s>email</a> and send us di list. We go give you free membership for Anna’s Archive, so you fit do dis work more easily (and as thank you for your help). Dis ones gatz be high quality edits wey add plenty information, otherwise your request go reject. Your request go also reject if any of di edits get revert or correct by Open Library moderators."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Note say dis one only work for books, no be academic papers or other types of files. For other types of files we still recommend make you find di source library. E fit take few weeks for changes to dey included for Anna’s Archive, since we need to download di latest Open Library data dump, and regenerate our search index."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Mirrors: call for volunteers"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "To increase the resiliency of Anna’s Archive, we dey look for volunteers to run mirrors."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Wi de look for dis:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "You dey run di Anna’s Archive open source codebase, and you dey regularly update both di code and di data."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Your version clear say e be mirror, example “Bob’s Archive, an Anna’s Archive mirror”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "You dey willing to take di risks wey dey follow dis work, wey dey significant. You get deep understanding of di operational security wey e need. Di contents of <a %(a_shadow)s>dis</a> <a %(a_pirate)s>posts</a> dey clear to you."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "You dey willing to contribute to our <a %(a_codebase)s>codebase</a> — in collaboration with our team — to make dis happen."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Initially we no go give you access to our partner server downloads, but if things go well, we fit share am with you."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hosting expenses"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "We dey willing to cover hosting and VPN expenses, initially up to $200 per month. Dis go dey enough for basic search server and DMCA-protected proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "We go only pay for hosting once you don set everything up, and don show say you fit keep di archive up to date with updates. Dis mean say you go pay for di first 1-2 months from your pocket."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Your time no go dey compensated (and our own no dey too), since dis na pure volunteer work."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "If you get significantly involved for di development and operations of our work, we fit discuss sharing more of di donation revenue with you, make you deploy as necessary."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Getting started"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Abeg <strong>no contact us</strong> to ask for permission, or for basic questions. Actions dey speak louder than words! All di information dey out there, so just go ahead with setting up your mirror."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Feel free to post tickets or merge requests to our Gitlab when you run into issues. We fit need build some mirror-specific features with you, like rebranding from “Anna’s Archive” to your website name, (initially) disabling user accounts, or linking back to our main site from book pages."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Once you don get your mirror running, abeg contact us. We go like review your OpSec, and once e solid, we go link to your mirror, and start to work closer with you."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Thanks in advance to anyone wey willing to contribute in dis way! E no be for faint heart, but e go solidify di longevity of di largest truly open library for human history."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Download from partner website"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Slow downloads dey only available through di official website. Visit %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Slow downloads no dey available through Cloudflare VPNs or otherwise from Cloudflare IP addresses."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Abeg mek yu wait <span %(span_countdown)s>%(wait_seconds)s</span> sekond mek yu fit download dis file."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Use di following URL to download: <a %(a_download)s>Download now</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Thanks for waiting, dis dey keep di website accessible for free for everybody! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Warning: plenty downloads don happen from your IP address for di last 24 hours. Downloads fit slow pass usual."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads from your IP address for di last 24 hours: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "If you dey use VPN, shared internet connection, or your ISP dey share IPs, dis warning fit be because of dat."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "To giv evribodi chanse to download files for free, yu need to wait bifor yu fit download dis file."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Feel free to continue browsing Anna’s Archive for different tab while you dey wait (if your browser fit refresh background tabs)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Feel free to wait for multiple download pages to load at di same time (but abeg only download one file at di same time per server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Once you get a download link e go valid for several hours."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rekɔd fɔ Anna’s Archive"

#, fuzzy
msgid "page.scidb.download"
msgstr "Download"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "To support di accessibility an long-term preservation of human knowledge, become a <a %(a_donate)s>memba</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "As a bonus, 🧬&nbsp;SciDB go load faster for membas, witout any limits."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "No dey work? Try <a %(a_refresh)s>refresh</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "No preview dey available yet. Download file from <a %(a_path)s>Anna’s Archive</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB na continuation of Sci-Hub, wit di same interface an direct viewing of PDFs. Enter your DOI to view."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "We get di full Sci-Hub collection, as well as new papers. Most of dem fit be viewed directly wit di same interface, like Sci-Hub. Some fit be downloaded through external sources, in which case we go show links to those."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Search"

#, fuzzy
msgid "page.search.title.new"
msgstr "New search"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Include only"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Exclude"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Unchecked"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Download"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Journal articles"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digital Lending"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Title, author, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Search"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Search settings"

#, fuzzy
msgid "page.search.submit"
msgstr "Search"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Di search take too long, wey common for broad queries. Di filter counts fit no dey accurate."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Di search take too long, wey mean say you fit see inaccurate results. Sometimes <a %(a_reload)s>reloading</a> di page dey help."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Show"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "List"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tébul"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Advanced"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Search descriptions and metadata comments"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Add speshial saach field"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(saach speshial field)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Year wey dem publish am"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Content"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Filetype"

#, fuzzy
msgid "page.search.more"
msgstr "more…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Access"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Source"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "wey AA scrape and open-source"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Language"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Order by"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Most relevant"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Newest"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publication year)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Oldest"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Largest"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(filesize)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Smallest"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open sourced)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Random"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "The saach index dey update every month. E currently get entries reach %(last_data_refresh_date)s. For more technical information, see the %(link_open_tag)sdatasets page</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "To explore di search index by codes, use di <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Type for the box to saach our catalog of %(count)s directly downloadable files, wey we <a %(a_preserve)s>preserve forever</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "As e be, anybody fit help preserve these files by seeding our <a %(a_torrents)s>unified list of torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "We currently get the world’s most comprehensive open catalog of books, papers, and other written works. We dey mirror Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>and more</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "If you find other “shadow libraries” wey we suppose mirror, or if you get any questions, abeg contact us for %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "For DMCA / copyright claims <a %(a_copyright)s>click here</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: use keyboard shortcuts “/” (saach focus), “enter” (saach), “j” (up), “k” (down), “<” (prev page), “>” (next page) for quicker navigation."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "You dey find papers?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Type for the box to saach our catalog of %(count)s academic papers and journal articles, wey we <a %(a_preserve)s>preserve forever</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Type for the box to saach for files for digital lending libraries."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "This saach index currently get metadata from the Internet Archive’s Controlled Digital Lending library. <a %(a_datasets)s>More about our datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "For more digital lending libraries, see <a %(a_wikipedia)s>Wikipedia</a> and the <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Type for the box to saach for metadata from libraries. This fit dey useful when <a %(a_request)s>requesting a file</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "This saach index currently get metadata from various metadata sources. <a %(a_datasets)s>More about our datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "For metadata, we de show di original records. We no dey do any merging of records."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Plenti, plenti sources of metadata for written works dey around di world. <a %(a_wikipedia)s>Dis Wikipedia page</a> na good start, but if you sabi other good lists, abeg let us know."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Type for di box to search."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Dis na metadata records, <span %(classname)s>no be</span> downloadable files."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Error during search."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Try <a %(a_reload)s>reload di page</a>. If di problem still dey, abeg email us for %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">No files found.</span> Try fewer or different search terms and filters."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Sometimes dis kin tin fit happen wrong when di search server slow. For dis kind cases, <a %(a_attrs)s>reloading</a> fit help."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "We don find matches for: %(in)s. You fit refer to di URL wey dey there when <a %(a_request)s>you dey request file</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Journal Articles (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digital Lending (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Results %(from)s-%(to)s (%(total)s total)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ partial matches"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d partial matches"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Volunteering & Bounties"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive dey rely on volunteers like you. We welcome all commitment levels, and we get two main categories of help we dey look for:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Light volunteering work:</span> if you fit only spare few hours here and there, plenty ways still dey wey you fit help out. We dey reward consistent volunteers with <span %(bold)s>🤝 memberships to Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Heavy volunteering work (USD$50-USD$5,000 bounties):</span> if you fit get plenty time and/or resources to support our mission, we go like make you dey work close with us. Eventually, you fit join the inner team. Even though our budget tight, we fit give <span %(bold)s>💰 monetary bounties</span> for the most intense work."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "If you no fit volunteer your time, you fit still help us well by <a %(a_donate)s>donating money</a>, <a %(a_torrents)s>seeding our torrents</a>, <a %(a_uploading)s>uploading books</a>, or <a %(a_help)s>telling your friends about Anna’s Archive</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Companies:</span> we dey offer high-speed direct access to our collections in exchange for enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets, enriching our data). <a %(a_contact)s>Contact us</a> if na you be this. See also our <a %(a_llm)s>LLM page</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Light volunteering"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "If you get few hours to spare, you fit help out in plenty ways. Make sure say you join the <a %(a_telegram)s>volunteers chat on Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "As token of appreciation, we dey usually give out 6 months of “Lucky Librarian” for basic milestones, and more for continued volunteering work. All milestones require high quality work — sloppy work dey hurt us more than e go help and we go reject am. Abeg <a %(a_contact)s>email us</a> when you reach a milestone."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Task"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Milestone"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "To spread di word about Anna’s Archive. For example, by recommending books for AA, linking to our blog posts, or generally directing people to our website."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s links or screenshots."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Dis one suppose show say you dey tell person about Anna’s Archive, and dem dey thank you."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Improve metadata by <a %(a_metadata)s>linking</a> with Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "You fit use the <a %(a_list)s >list of random metadata issues</a> as starting point."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Make sure say you leave comment on issues you fix, so others no go duplicate your work."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s links of records wey you don improve."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Translating</a> the website."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Fully translate one language (if e no dey close to completion already.)"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Improve the Wikipedia page for Anna’s Archive for your language. Include information from AA’s Wikipedia page for other languages, and from our website and blog. Add references to AA for other relevant pages."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link to edit history wey show say you make significant contributions."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Fulfill book (or paper, etc) requests for the Z-Library or the Library Genesis forums. We no get our own book request system, but we dey mirror those libraries, so making them better dey make Anna’s Archive better too."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s links or screenshots of requests wey you don fulfill."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Small tasks wey we post for our <a %(a_telegram)s>volunteers chat on Telegram</a>. Usually for membership, sometimes for small bounties."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Small tasks wey dem post for our volunteer chat group."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depends on the task."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Bountis"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Wi de always dey find people wey sabi programming well well or sabi security work to join us. You fit help preserve wetin humanity don do."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "As thank you, we go give membership for solid contributions. As big thank you, we go give money bountis for important and hard tasks. No see am as job replacement, but e fit be extra incentive and fit help with costs wey you go get."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Most of our code na open source, and we go ask make your code be open source too when we dey award bounty. Some exceptions dey wey we fit discuss one-on-one."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Bountis dey go to the first person wey complete task. Feel free to comment for bounty ticket make others know say you dey work on something, so others fit hold off or contact you to team up. But know say others still free to work on am too and try beat you to am. But we no go award bountis for sloppy work. If two high quality submissions dey close to each other (within a day or two), we fit choose to award bountis to both, at our discretion, for example 100%% for the first submission and 50%% for the second submission (so 150%% total)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "For the bigger bountis (especially scraping bountis), abeg contact us when you don complete ~5%% of am, and you sure say your method go scale to the full milestone. You go need share your method with us so we fit give feedback. Also, this way we fit decide wetin to do if multiple people dey close to bounty, like potentially awarding am to multiple people, encouraging people to team up, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "WARNING: the high-bounty tasks dey <span %(bold)s>difficult</span> — e fit make sense to start with easier ones."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Go to our <a %(a_gitlab)s>Gitlab issues list</a> and sort by “Label priority”. This go show roughly the order of tasks we care about. Tasks wey no get explicit bountis still fit get membership, especially those wey dem mark “Accepted” and “Anna’s favorite”. You fit wan start with “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates about <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di biggest truly open library for human history."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Di world biggest open-source open-data library. E dey mirror Sci-Hub, Library Genesis, Z-Library, and more."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Search Anna’s Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’s Archive need your help!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Plenty people dey try bring us down, but we dey fight back."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "If you donate now, you go get <strong>double</strong> di number of fast downloads."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "E dey valid until di end of dis month."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donate"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "To save human knowledge: na better holiday gift!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Surprise person wey you love, give dem account with membership."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "To make Anna’s Archive strong well well, we dey find volunteers to run mirrors."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Di perfect Valentine’s gift!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "We get new donation method available: %(method_name)s. Abeg consider %(donate_link_open_tag)sdonating</a> — e no cheap to run dis website, and your donation really dey make difference. Thank you so much."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "We dey run fundraiser for <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">backing up</a> di biggest comics shadow library for di world. Thanks for your support! <a href=\"/donate\">Donate.</a> If you no fit donate, consider to support us by telling your friends, and follow us for <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, or <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Recent downloads:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Search"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Improve metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Volunteering & Bounties"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activity"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Codes Explorer"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Home"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Translate ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Log in / Register"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Account"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Stay in touch"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / copyright claims"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Advanced"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Security"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatives"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "unaffiliated"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Dis file fit get wahala."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Fast download"

#, fuzzy
msgid "page.donate.copy"
msgstr "copy"

#, fuzzy
msgid "page.donate.copied"
msgstr "copied!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Previous"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Nest"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "only dis month!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub don <a %(a_closed)s>pause</a> di upload of new papers."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Chuz payment option. We dey give discount for crypto-based payments %(bitcoin_icon)s, because we no dey pay plenty fees."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Chuz payment option. For now, na only crypto-based payments we get %(bitcoin_icon)s, because traditional payment processors no wan work with us."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "We no fit support credit/debit cards directly, because banks no wan work wit us. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "However, dere dey several ways to use credit/debit cards anyway, using our other payment methods:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Slow & external downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "If yu dey use crypto for di first time, we suggest make yu use %(option1)s, %(option2)s, or %(option3)s to buy and donate Bitcoin (di original and most used cryptocurrency)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 links of records wey you improve."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 links or screenshots."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 links or screenshots of requests wey you fulfill."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "If yu dey interested to mirror dis datasets for <a %(a_faq)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, abeg contact us."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "If you dey interested to mirror dis dataset for <a %(a_archival)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, abeg contact us."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Main website"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN country information"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "If you dey interested in mirroring this dataset for <a %(a_archival)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, abeg contact us."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Di International ISBN Agency dey regularly release di ranges wey dem don allocate to national ISBN agencies. From this we fit derive which country, region, or language group this ISBN belong. We currently dey use this data indirectly, through di <a %(a_isbnlib)s>isbnlib</a> Python library."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resources"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Last updated: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN website"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excluding “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Our inspiration for collecting metadata na Aaron Swartz’ goal of “one web page for every book wey dem ever publish”, for which he create <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "That project don do well, but our unique position allow us to get metadata wey dem no fit get."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Another inspiration na our desire to know <a %(a_blog)s>how many books dey for the world</a>, so we fit calculate how many books we still get left to save."

#~ msgid "page.partner_download.text1"
#~ msgstr "To give everybody chance to download files for free, you need to wait <strong>%(wait_seconds)s seconds</strong> before you fit download dis file."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automatically refresh page. If you miss the download window, the timer will restart, so automatic refreshing is recommended."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Download now"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Convert: use online tools to change between formats. For example, to change between epub and pdf, use <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: download the file (pdf or epub dey supported), then <a %(a_kindle)s>send am to Kindle</a> using web, app, or email. Helpful tools: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Support authors: If you like this and fit afford am, try buy the original, or support the authors directly."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Support libraries: If this one dey available for your local library, try borrow am for free there."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Not available directly in bulk, only in semi-bulk behind a paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive manages a collection of <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb na company wey dey scrape different online bookstores to find ISBN metadata. Anna’s Archive don dey make backups of di ISBNdb book metadata. Dis metadata dey available through Anna’s Archive (but e no dey for search now, except if you search for ISBN number directly)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "For technical details, see below. At some point we fit use am to determine which books still dey miss from shadow libraries, so we go fit prioritize which books to find and/or scan."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Our blog post about this data"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb scrape"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Currently we get single torrent, wey contain 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> file (20GB unzipped): “isbndb_2022_09.jsonl.gz”. To import “.jsonl” file into PostgreSQL, you fit use something like <a %(a_script)s>this script</a>. You fit even pipe am directly using something like %(example_code)s so e go decompress on di fly."

#~ msgid "page.donate.wait"
#~ msgstr "Abeg wait at least <span %(span_hours)s>two hours</span> (and refresh this page) before you contact us."

#~ msgid "page.codes.search_archive"
#~ msgstr "Search Anna’s Archive for “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donate wit Alipay or WeChat. Yu fit choose between dem for di next page."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Spread the word of Anna’s Archive for social media and online forums, by recommending book or lists on AA, or answering questions."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiction collection has diverged but still has <a %(libgenli)s>torrents</a>, though not updated since 2022 (we do have direct downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna’s Archive and Libgen.li collaboratively manage collections of <a %(comics)s>comic books</a> and <a %(magazines)s>magazines</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s No torrents for Russian fiction and standard documents collections."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "No get torrents wey dey available for di extra content. Di torrents wey dey for di Libgen.li website na mirrors of other torrents wey dem list here. Di only exception na fiction torrents wey start for %(fiction_starting_point)s. Di comics and magazines torrents na collaboration between Anna’s Archive and Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "From a collection <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> exact origin no clear. Partly from the-eye.eu, partly from other sources."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

