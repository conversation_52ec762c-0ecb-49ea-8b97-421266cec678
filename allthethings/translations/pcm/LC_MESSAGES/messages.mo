��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b J  ud 5  �f -   �g    $h �  %i �  k S  �l �   �m E   �n k   @o E   �o c   �o i  Vp F  �q �   s �  �s �   _u z  1v �  �w �  Oy �   
{ �  �{   �} 4    �   I M   � �  V�    '� �  F� E   7� "   }�    ��    �� :   ̄    �    %� $   2�    W�    o� 
   �� 0   �� :  ƅ   � M   � 1   \� 	   ��    ��    ��    ��        ܈ 	   �    �    � "   �    .�    4� 	   N� 
   X�    f�    r�    ��    ��   �� H  ݋ �   &� -   �� *  Ս �    � �   �� �   ��    H� �   e�    �� �   � !   �� �   ג    ϓ T  � /   A� R   q� 
   ĕ )  ϕ   �� �  �   ��    �� C    V   � �   ]� (   �� %   %� y   K� ,   Ŝ :   � %   -�    S� v   X� �  ϝ 6   U� e   �� �   � 9   ��   ֠ '   � �   � �   �� �   �� I   � �   R� }   �� E   v� �   �� �   �� J   Y� 5   �� i   ڧ    D� �   P�    ٨ E   ک 
    � �   +� �   ʪ    �� a  �� �   �� �   �� b  �� �   �� (  � �   	� T   � ;   H� O   �� W   Ե ]   ,� L   �� $   ׶ �   �� M   �� 7   ҷ    
� a   � U   y� �   ϸ �   ��    � �   6�   � G  �   c� �   �� �   � 9  �� �   8� �   �� �   �� e  � �   z� �   �    	� �   � �   �� �   �� h  I� }   �� �   0� �   �� �  e� {   2�   �� �   ��    �� Q   �� �   ;� =   �� +  �� �   &� j   �� q   &�    ��   �� �   �� c  x�    �� W   �� �   L� �   �� �   }� �   ]� �  � y  �� $   #� 8  H�   �� 
  �� O  ��   �� �   �� J   �� x   1�    �� �   ��   k� Q  �� �   �� �   �� �   �� 
   � (  %�   N� �   ^� 
   C� �   Q� 1  �� �   %� �  �� �   �� U   ��    �� u  �� M  p� 
   �� �   �� �   �� �  �� �   �� O  S� Z   �� O   �� �   N  3   I    }   � �   �   � &   � E   � �    m   � f   D l   � @      Y	    y	 P  �	 �   �
 �   � >   / �   n !   � �   !
 �   
    � 
   � #   � ?    /   Z .   � O   � p   	    z *   � 9   � B   � "   4 2   W (   � _   �           .   1 �   M �    u   � �   q -  %    S �   d �    �   � <  9 A  v �  � �  q U  � E   J! /   �! &   �! �   �! �  " s  E$   �%   �& �   �' �  �(   �* /   �+ �   �+ 
  �, E  �-    5/ �   E/ 4  $0 r   Y1 1  �1 �   �2    �3 '   �3 Q   �3 
   84 m  C4 w  �6    )8 �   J: �   �: 
   �; �   �; �   o< E   8= p   ~= �   �= �   �> �   �? e  V@ ^   �A   B    'C �  ;C �   E �  �E ,  NG �  {I �   9K 
   �K �   �K �  �L    UN >  lN �  �O �  <Q �  2S   U +  V E   IW �  �W �   TY z   $Z ;   �Z S   �Z    /[ :   4[ $   o[ )   �[ !   �[    �[ x   �[ �  p\ �  _ �  �` �   �b 	   qc �  {c �  f F  �g ^  2i !  �j   �l 
   �n �   �n    �o    �o r  �o �  Wq �  s   �t    �v �  w �  �x �  Sz �  | �  �}   u� 6  w�     �� �   σ h   �� �  � �  {� �   �� �   �� �   � e  ��    � �   &� @   �� E   ��    D� 4   X� 0   �� �  �� n  �� k  � -   ~� �  �� �   -�    Ö �   ɖ :   k� �   �� �   (� )   �� `   
�    k� ,  |� �  �� �  �� P  .� w   �    �� b   �   n�    s� �   y� Q  �� �   M� 6   � *   � 9  F� -   �� *  �� g  ٨ �  A�     � m   #� �   ��    � d  0� 
   �� �   �� ,   }� �   �� �   ?� �   ֲ �   ɳ I   �� -   ʴ �   �� /   �� J  '� 5  r� �   �� �   Y� U   :� 6   �� �   ǻ �  [� O  K� �  �� �  J� ?   �� �   '� �   � !  
� �   /� �  �� �  �� -   B� �  p� %   F� �  l� M   �    f� �  z� �  R� X  � �   q�   F� �  I� �  %� �   �� �  ~� �  !� t   �� �  $� 2  �� 5   �� �   3� s   ��    /�    J� ,  O� �   |� /   �   M� �  Q�   �� �  �� w  f� �   �� w  p�   ��    �� �    �    �� @  ��    ��    �    1�    7�     G�    h�    z�    ��    ��    ��    ��    ��    �� 
   ��    �� 
   ��     �    %�    (� 	   .�    8� �   <� O   �    X� 
   x� #   ��    �� &   �� #   ��    � 	   4� 
   >�    I�    X�    k�    z�    �� 
   ��    ��    ��    ��    ��    �� $   � )   9�    c� $   ��    ��     �� +   ��    � ?   � 5   W�    �� G   �� =   ��    �    5� !   F�    h�    {�    ��    ��    ��    ��    ��    ��    ��    �� 	   	� 
   �    �    !�    ?�    F� 	   O�    Y� 	   p�    z�    ��    �� 	   ��    ��    ��    ��    ��    ��    ��    � 
   $� 	   2�    <� "   N�    q�    x� !   �    ��    �� 	   ��    ��    ��    ��    �� Y   � {   f� H   �� �   +� �  �� a   v�    ��    �� 
   �    �    �    #�    ;�     H� R   i� ;   �� Y   �� &   R� 0   y� )   �� Y   �� Q   .  �   �  b   N ?   �    � 
           &    /    8    J    `    e    t    }    �    �    �    �    �    �    �    �    �    �     
   
        0 	  B    L    R    b     h    � :   � a   � (   - *   V 1   �    �    �    � j   �    1    7 '   H k   p    �    � g       m   � U   �
 u   �
 �   ^ �   � 2   � �   � s   �
 9  $ �   ^    >    [ E   b A   � M   � c   8 f   � M    i   Q $   � )   �    
     @   ! &   b    �    �    �    � ]   � 
        # A   D    �    � R   � K     B  L    �    � �   �    `    l    r    y    � +   � (  �    �    
        % $  1    V    v    ~ R   �    �    � +   �        ( $   . |   S    �    � 1   �    (    D    S    Y (   m =   � 
   � .   � �    L   � A   � 
   , �   : @       U 2   h    � �   � �   F     �  :   �  �   !! �   �!    P" "   h"    �" &  �"    �# #   �#    �# "   $    ?$ �   ^$    �$    % $   % <   C% 	   �%    �%    �%    �% #  �% U  �' �  R) 1   7+ -   i+    �+ "   �+   �+ �   �, �   �-    *. �   B. �  /    �0    �0 �  �0 
  �2    �3    �3 .   �3 	   4   4    5   95 s  H6 �   �7    x8 l   �8 *   �8 $   '9 l   L9 &  �9 �   �: �   �; �  b< u   �= �   s> U   O? �   �? �   C@ G   �@ �   2A ,   �A %   B    'B    8B    AB &   RB    yB A   �B .   �B 	   C *   C   :C %   XD �   ~D t   QE �   �E %   HF    nF    �F    �F $   �F    �F )   �F    %G �   CG !   H �   7H    �H �   �H y   �I l   3J   �J �   �K +   uL z   �L 	   M �   &M c   N    �N �  �N    (P    5P    JP    WP *   vP    �P    �P G   �P �   �P �   �Q    qR    zR    �R �   �R �   FS �   <T t   �T    KU    bU    yU    �U    �U    �U    �U E   �U 1   V �  BV H   �W    $X g   5X `   �X O   �X k   NY K   �Y R   Z    YZ b   _Z I   �Z �   [ J   �[ L   �[    .\ �   ;\ `   -] 9   �] `   �] W   )^ 5   �^    �^ 2   �^ o   �^ �   c_ 1   �_ �   (`    �` �   �` C   �a ^   b �   eb     c �  	c �   �d    �e !   �e    �e    �e �   �e *   �f [   �f �   0g �   �g �   �h �   Ii �   j �   �j �   Pk W   l %  ^l g   �m I  �m �   6o �   �o 
   �p 
   �p 
   �p �   �p 
   �q 
   �q F   �q X   �q �   ?r 
   �r a   s ;   ns {   �s =   &t o   dt _   �t 
   4u ~   Bu 
   �u 
   �u 
   �u   �u ~   �w �  nx    z    %z    +z �   :z    �z    �z �   { �   �{    �|    �|    �| 9   �| <   } 6   P}    �}    �} �   �}    �~ Z  �~ �   � x   ܀ M   U� �   �� �   C� �  B� �  �� �   � ~   ~� o   ��    m� 5  ��    �� M  ։ �   $� 
  �� y   �   �� �   �� y   � |   �� +   � -   @�    n� *   �� 
   ��    ��    Ɛ �   א    �� b   �� 3   �    C�    J�    O�     X� l   y� ;   � *   "� �   M� 2   9�    l�    t�    y�    ��    �� 	       ̔ 	   Ԕ    ޔ 	   �    � 	   �� &   � �   +�    ɕ 
   ڕ    � 
   ��    � 
   �    � 
   *�    8� 
   Q� $   \� ^   �� 
   �� ,   � T   � �   p� �   � �   ˘   s� �   w� �   �    � t   $� /   �� D   ɜ ;   � �   J� �   � E   �� V   ��    M� V   _� Y   �� y   �    ��     ��    ��    à 	   ؠ    �    ��    �    %�    .�    @�    T�    o�    ��    ��    ��    ��    ѡ    ޡ    �    � "   
�    -� n   J� �   ��    =�     ]� X   ~� N   ף q   &�    �� $   �� *   ܤ ;   � 7   C� %   {� �   �� �   W� 
  D�    O� 4   d� y   ��     � �   4� �   �� p   �� =   �    S� �   k� ~   � [   �� &   ܬ T   � �   X�    � $   *�    O� 3   c� f   �� `   ��    _�    f�    o�    v� �   �� 1   �    D� 0   a� !   ��    ��     Ͱ G   �    6� _   L� :   �� �   � B   ٲ C   � �   `� ?   �� !   9�    [� !   {�     �� !   ��     � !   � 1   #� /   U� �  �� 7   t� 4   �� >   � .    �    O� e   V� h   �� �   %� �   ƹ    ^� t   g� %   ܺ    � �   �    û $   ջ 4   �� 0   /� L   `� T   �� D   �    G�    `� �   � ,   � "   D� C   g� �   �� @   �� r   ɿ j   <� W   �� z   ��     z� $   �� m   ��    .� T   I�    �� �   �� .   [� <   ��    ��    �� �   �� 6   �� >   �� }   6� W   �� @   � f   M� t   ��    )�    0� :   D� :   �    ��    ��    ��    ��    � -   � �   D� g   ��    8�    I� "   f� #   �� #   �� i   �� .   ;� f   j� K   ��    � ^   :� �   �� .   ^� O   ��    �� @   �� 9   4�    n� f   �� k   �� +   V� H   ��    �� .   �� O   
�    ]� a   s� (   ��    �� #   � C   8� �   |� =   *�    h�    � =   ��    �� R   �� 7   >� �   v� w   � 
   �� 2   �� �   ��    }� �   �� 9   3� "   m� B   �� �   ��    ��    ��    ��    ��    �� (   �� 6   �    M�    g�    s� "   |� 5   �� /   ��    � G   
� ;   U� 
   ��    �� &   ��    ��    �� K   � �   S� 9   �    W�    f� �   r� &  � N   :�    �� I  ��   �� '   `� k   ��    ��   � $   &� ^   K�    ��    �� �  ��    �� G   �� f   �� c   L� G   ��    �� T   � 1   a�    �� q   �� @   !� 0   b� W   �� *   �� P   � z   g� �   �� %   x� �   �� 2  C� �   v� '    � )  H� �   r� �   I�   �� �   � "   ��     �� �   � +   �� �  �� c   m� ?   ��    �    � �  7�    �� �   �� +  �� "  ��   �� 4   �� C   � Q   V� .   �� "   �� I   �� K   D�    ��    �� ,   ��    ��    �� $   � Q   <� (   ��    �� [   �� �   � �   ��    �� 
   ��    �� @   �� Y   �� T   N� �   �� `   j     �  !   �  �   �     � �   � �  ^ �   � I   � .   +    Z    `    e >   i +   � t   � �   I O   %    u    � %   �    � P   �    .	 ;   =	    y	 +   ~	     �	    �	    �	 L   �	    )
    .
     >
    _
    |
 _   �
 �   �
 J   � \   � I   ? �   �    K
    T
 �   k
 �    �   �    � f   � :   � 5   4 V   j i   � K   +    w R   �    �    �            +    @    V    b    t    � +   � +   � +   �     0   0 )   a    �    �    � <   �     	   ) +   3 '   _ Q   � (   �         !   .    P    m i   � R   � g   = �   �    {    �     �    � !   � 	   �         \   .    � #   �    � 
   � 	   � 1   �     �   1 
       ,    C #   U    y    �    � !   � %   � !       :    X J   _ $   �    �    � $   � D   !    f /   �    � ]   � N   / A   ~    �    � 	   �    �    �     p  - b   �           *        B  %   R     x     }  l   �  =   �  �   1!    �! !   �! v   " !   �"    �"     �" #   �" ,   # #   D#    h#    �# (   �#    �# &   �# 5   �#    '$ 2   H% 8   {% B   �%    �% {   &    �&    �& I   �&    ' !   '    ;' B   L' 2   �'    �' {   �' 
   \( 
   j(    x(    �(    �(    �(    �(    �(    ) T   ) �   j)    Z*     v* �   �* �   P+    �+    �+    ,    ),    9,    P,    c,    |,    �,    �,    �,    �, 
   �,    �,    �,    -    -    - !   .- �   P- �   . R  /   g0 s  p2 �   �3 d  �4 
   6 �   &6    7 �   "7 �   �7 g  �8 �   @: E  �: 4   8< �   m< C   =    `= ;   y= D   �= a   �= g   \> �   �> �   Y? �   @ �  �@    �B �   �B �   uC [   4D �   �D    &E �   7E &  �E �   &G �   �G    yH Z   �H ^   �H �   CI t   'J l   �J ~   	K    �K    �K    �K =   �K '   L    6L �   CL ?   �L a   M    fM �   �M �   N :   �N S   �N H   O f   dO V   �O J   "P b   mP f   �P n   7Q m   �Q    R $   R $   >R Q   cR 6   �R    �R    �R ?   �R    7S    DS    TS -   \S    �S 7   �S    �S    �S    T    T 	   T T   $T m   yT I   �T .   1U    `U    hU )   �U    �U    �U    �U    �U    �U    �U    �U    �U    �U    �U    V 
   V    V 
   $V    /V    >V    QV    XV    _V    hV    oV    �V    �V v   �V    =W M   RW �   �W    NX    VX 	   cX    mX    uX    zX    ~X �   �X y    Y >   �Y 
   �Y    �Y ]   �Y 
   ZZ m   hZ �   �Z    [[    x[ u   �[ �   \ R   �\ �   
] _   �] )   *^ {   T^    �^    �^ B   _ ~   D_    �_ �   �_ w   f` �   �` M   �a    �a    �a    �a    �a    b    b 
   b    (b    Bb \   �b y   c �   �c �   `d X   .e I   �e �  �e V  Vg �   �h �   �i �   *j 7  %k    ]l �   el �   �l �   �m   wn �  �p r   r =  �r    �s 9   �s �   t @  �t ?   �u �   ,v 	   	w    w    w �   -w 6   �w W   �w /   Ex T   ux D   �x [   y    ky �   �y G   )z /   qz E   �z �   �z    �{  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: pcm
Language-Team: pcm <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library na popular (and illegal) library. Dem don take the Library Genesis collection and make am easily searchable. On top of that, dem don become very effective at soliciting new book contributions, by incentivizing contributing users with various perks. Dem currently no dey contribute these new books back to Library Genesis. And unlike Library Genesis, dem no dey make their collection easily mirrorable, wey dey prevent wide preservation. This dey important to their business model, since dem dey charge money for accessing their collection in bulk (more than 10 books per day). We no dey make moral judgements about charging money for bulk access to an illegal book collection. E dey beyond doubt say the Z-Library don successful in expanding access to knowledge, and sourcing more books. We dey simply here to do our part: ensuring the long-term preservation of this private collection. - Anna and di team (<a %(reddit)s>Reddit</a>) For di original release of di Pirate Library Mirror (EDIT: dem don move am to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), we make mirror of Z-Library, one big illegal book collection. As reminder, dis na wetin we write for dat original blog post: Dat kolekshọn wey dem date am go bak to mid-2021. As we dey talk so, di Z-Library dey grow for one kind fast rate: dem don add about 3.8 million new books. E get some duplicates for dia, sure, but majority of dem be new books wey dem scan well well, or better quality scans of books wey dem don submit before. Dis na because of di increased number of volunteer moderators for Z-Library, and dia bulk-upload system wey dey remove duplicates. We wan congratulate dem for dis achievements. We dey happy to announce say we don get all di books wey dem add to di Z-Library between our last mirror and August 2022. We also go back go collect some books wey we miss di first time. Altogether, dis new kolekshọn na about 24TB, wey big pass di last one (7TB). Our mirror don reach 31TB in total. Again, we remove duplicates against Library Genesis, since torrents dey available for dat kolekshọn already. Abeg go to di Pirate Library Mirror to check di new kolekshọn (EDIT: dem don move am go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). More information dey dia about how di files dey structured, and wetin don change since last time. We no go link am from here, since dis na just blog website wey no dey host any illegal materials. Of course, to seed na better way to help us. Thanks to everybody wey dey seed our previous set of torrents. We dey grateful for di positive response, and happy say plenty people dey care about preservation of knowledge and culture in dis unusual way. 3x nyu buks aded to di Pirate Library Mirror (+24TB, 3.8 milion buks) Read di companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a> - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) companion articles by TorrentFreak: <a %(torrentfreak)s>first</a>, <a %(torrentfreak_2)s>second</a> Not too long ago, “shadow-libraries” dey die. Sci-Hub, di big illegal archive of academic papers, don stop to dey take new works, because of lawsuits. “Z-Library”, di largest illegal library of books, see say dem arrest di people wey dem say create am on criminal copyright charges. Dem manage escape di arrest, but their library still dey under threat. Some countries don already dey do one version of dis. TorrentFreak <a %(torrentfreak)s>report</a> say China an Japan don introduce AI exceptions to dia copyright laws. E no clear to us how dis dey interact with international treaties, but e certainly dey give cover to dia domestic companies, wey explain wetin we don dey see. As for Anna’s Archive — we go continue our underground work wey dey rooted in moral conviction. But our greatest wish na to enter di light, an amplify our impact legally. Abeg reform copyright. When Z-Library face shutdown, I don already back up di whole library and dey find platform to house am. Na im make me start Anna’s Archive: continuation of di mission behind those earlier initiatives. We don grow to be di largest shadow library for di world, wey dey host more than 140 million copyrighted texts across different formats — books, academic papers, magazines, newspapers, and beyond. My team and I na ideologues. We believe say to preserve and host these files dey morally right. Libraries around di world dey see funding cuts, and we no fit trust humanity’s heritage to corporations either. Den AI come. Almost all major companies wey dey build LLMs contact us to train on our data. Most (but not all!) US-based companies change their mind once dem realize di illegal nature of our work. But Chinese firms dey happy to use our collection, dem no dey worry about di legality. Dis dey notable given say China dey part of almost all major international copyright treaties. We don give high-speed access to about 30 companies. Most of dem na LLM companies, and some na data brokers, wey go resell our collection. Most na Chinese, but we don also work wit companies from di US, Europe, Russia, South Korea, and Japan. DeepSeek <a %(arxiv)s>admit</a> say one earlier version train on part of our collection, but dem no wan talk about their latest model (probably also train on our data though). If di West wan stay ahead for di race of LLMs, and ultimately, AGI, dem need to rethink their position on copyright, and soon. Whether you agree with us or not on our moral case, dis don become matter of economics, and even national security. All power blocs dey build artificial super-scientists, super-hackers, and super-militaries. Freedom of information dey become matter of survival for these countries — even matter of national security. Our team dey from all over di world, and we no get particular alignment. But we go encourage countries wey get strong copyright laws to use dis existential threat to reform dem. So wetin to do? Our first recommendation dey straightforward: make dem shorten di copyright term. For di US, copyright dey granted for 70 years after di author’s death. Dis dey absurd. We fit bring dis in line wit patents, wey dem dey grant for 20 years after filing. Dis suppose dey enough time for authors of books, papers, music, art, and other creative works, to get fully compensated for their efforts (including longer-term projects such as movie adaptations). Den, na di minimum, policymakers suppose include carve-outs for di mass-preservation an dissemination of texts. If di main worry na lost revenue from individual customers, personal-level distribution fit remain prohibited. In turn, those wey fit manage vast repositories — companies wey dey train LLMs, along with libraries an other archives — go dey covered by these exceptions. Copyright reform dey necessary for national security TL;DR: Chinese LLMs (including DeepSeek) dey train on my illegal archive of books and papers — di largest for di world. Di West need to change copyright law as matter of national security. Abeg chẹk di <a %(all_isbns)s>orijinal blog post</a> fọ mor infọmation. Wi giv chalenj mek pipul impruv dis. Wi go giv fọst plẹs bounti of $6,000, sekọnd plẹs of $3,000, an tọd plẹs of $1,000. Bikọs pipul respond wel an di submisshọn dem dey wondaful, wi don desaid to increas di prais pool smọl, an giv fọ pipul tọd plẹs of $500 ich. Di winas dey bẹẹlo, bot mek shọr to luk all submisshọn dem <a %(annas_archive)s>hịa</a>, or download aw <a %(a_2025_01_isbn_visualization_files)s>kombined torrent</a>. Fọst plẹs $6,000: phiresky Dis <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>Gitlab comment</a>) na everytin we want, an even more! We especially like di incredibly flexible visualization options (even support custom shaders), but wit a comprehensive list of presets. We also like how fast an smooth everytin be, di simple implementation (wey no even get backend), di clever minimap, an extensive explanation for dia <a %(phiresky_github)s>blog post</a>. Incredible work, an di well-deserved winner! - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Awá háts dɔn ful wit gratitúd. Notébul Aidiya Dem Skyscrapers fɔ Rérity Plénti Slídas fɔ Kɔmpar Datasets, laik sé yú bì DJ. Skél Ba wit Nɔmba ɔf Buks. Fain Lebuls. Kúl Dífɔlt Kolɔ Skim an Hítmap. Uník Map Viu an Filtas Annotations, an Lív Stats Lív Stats Som Aidiya an Wetin We Do Wey We Lík Wél Wél: Wi fít kɔntinyu fɔ sɔmtáim, bɔt mek wi stɔp hia. Mék shɔ sé yú chék ɔl di sɔbmíshɔn <a %(annas_archive)s>hia</a>, ɔr dɔnlód awá <a %(a_2025_01_isbn_visualization_files)s>kɔmbáind tɔrent</a>. Plénti sɔbmíshɔn, an ích wán bríng uník pɛrspéktiv, wéda na fɔ UI ɔr impliméntéshɔn. Wi go at líst ínkɔrpɔrét di fés plés sɔbmíshɔn íntu awá mɛin wébsait, an prɔbabli sɔm ɔda wans. Wi dɔn stáat tɔ tink abaut háu tɔ ɔgánáiz di prɔsés ɔf aidentifáyin, kɔnfámin, an den ákaivin di rérést buks. Mɔ tɔ kɔm fɔ dis frɔnt. Ténk yú evribɔdi wey pátísipét. Ít’s amézin sé plénti pipul kɛa. Ízi Tóglin ɔf Datasets fɔ Kwík Kɔmparisɔn. All ISBNs CADAL SSNOs CERLALC data leak DuXiu SSIDs EBSCOhost’s eBook Index Google Books Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Files in Anna’s Archive Nexus/STC OCLC/Worldcat OpenLibrary Russian State Library Imperial Library of Trantor Second place $3,000: hypha “While perfect squares an rectangles dey mathematically pleasing, dem no dey provide superior locality for mapping context. I believe say di asymmetry wey dey inside these Hilbert or classic Morton no be flaw but na feature. Just like Italy's famously boot-shaped outline make am instantly recognizable on a map, di unique "quirks" of these curves fit serve as cognitive landmarks. This distinctiveness fit enhance spatial memory an help users orient themselves, potentially making locating specific regions or noticing patterns easier.” Anoda incredible <a %(annas_archive_note_2913)s>submission</a>. E no dey as flexible as di first place, but we actually prefer its macro-level visualization over di first place (space-filling curve, borders, labeling, highlighting, panning, an zooming). A <a %(annas_archive_note_2971)s>comment</a> by Joe Davis resonate wit us: An still plenty options dey for visualizing an rendering, as well as an incredibly smooth an intuitive UI. A solid second place! - Anna and di team (<a %(reddit)s>Reddit</a>) A few months ago we announce a <a %(all_isbns)s>$10,000 bounty</a> to make di best possible visualization of our data wey dey show di ISBN space. We emphasize showing which files we don archive already, an we later add a dataset wey dey describe how many libraries hold ISBNs (a measure of rarity). We don dey overwhelmed by di response. E get so much creativity. Big thank you to everyone wey participate: your energy an enthusiasm dey infectious! Ultimately we wan answer di following questions: <strong>which books dey exist for di world, how many we don archive already, an which books we suppose focus on next?</strong> E dey great to see so many people care about these questions. We start with a basic visualization ourselves. In less than 300kb, dis picture succinctly represent di largest fully open “list of books” wey dem don ever assemble for di history of humanity: Third place $500 #1: maxlion For dis <a %(annas_archive_note_2940)s>submission</a> we really like di different kinds of views, particularly di comparison an publisher views. Third place $500 #2: abetusk Even though e no be di most polished UI, dis <a %(annas_archive_note_2917)s>submission</a> check plenty of di boxes. We particularly like its comparison feature. Third place $500 #3: conundrumer0 Like di first place, dis <a %(annas_archive_note_2975)s>submission</a> impress us wit its flexibility. Ultimately dis na wetin make for a great visualization tool: maximal flexibility for power users, while keeping things simple for average users. Third place $500 #4: charelf Di final <a %(annas_archive_note_2947)s>submission</a> to get a bounty dey pretty basic, but get some unique features wey we really like. We like how dem show how many datasets dey cover a particular ISBN as a measure of popularity/reliability. We also really like di simplicity but effectiveness of using an opacity slider for comparisons. Winners of di $10,000 ISBN visualization bounty TL;DR: We get some incredible submissions to di $10,000 ISBN visualization bounty. Background Hao Anna’s Archive fitachieve im mission of backing up all of humanity’s knowledge, witaut knowing which books dey still dey out dia? We need one TODO list. One way to map dis out na through ISBN numbers, wey since di 1970s dem don dey assign to every book wey dem publish (for most kontries). No central authority dey wey sabi all ISBN assignments. Instead, na distributed system, wey kontries dey get ranges of numbers, wey dem go assign smaller ranges to major publishers, wey fit still sub-divide ranges to minor publishers. Finally, dem dey assign individual numbers to books. Wi start to map ISBNs <a %(blog)s>tu yiaz ago</a> wit aw skreyp of ISBNdb. Sins den, wi don skreyp plenti mor metadata sorsis, laik <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, an mor. Yu fit faind di ful list fọ di “Datasets” an “Torrents” pages fọ Anna’s Archive. Na wi get di bigest ful open, izi to download kolekshọn of buk metadata (an so ISBNs) fọ di wold. We don <a %(blog)s>write plenty</a> about why we care about preservation, and why we dey currently for critical time. We must now identify rare, underfocused, and uniquely at-risk books and preserve dem. Having good metadata on all books for di world go help with dat. $10,000 bounty Strong consideration go dey given to usability and how good e look. Show actual metadata for individual ISBNs when you dey zoom in, like title and author. Better space-filling curve. E.g. a zig-zag, going from 0 to 4 on di first row and then back (in reverse) from 5 to 9 on di second row — recursively applied. Different or customizable color schemes. Special views for comparing Datasets. Wé wey yu fit yus fain out wétin dey rong, laik oda metadata wey no dey match well (e.g. title wey dey very different). To mark image wit comment for ISBN or range. Any way wey fit help to sabi rare or book wey dey at-risk. Any creative idea wey yu fit tink of! Code Di kọd to jeneret dis imẹjiz, as wel as oda egzampls, yu fit faind dem fọ <a %(annas_archive)s>dis direktri</a>. Wi kam up wit a kompakt data fọmat, wey all di rikwaiad ISBN infọmation na about 75MB (kompresd). Di deskripshọn of di data fọmat an kọd to jeneret am yu fit faind <a %(annas_archive_l1244_1319)s>hịa</a>. Fọ di bounti yu no nid to yus dis, bot e fit bi di most konvinient fọmat to start wit. Yu fit transform aw metadata anyhow yu want (bot all yu kọd gatz bi open sors). Wi no fit wait to si wetin yu go kam up wit. Gudlọk! Fork dis repo, and edit dis blog post HTML (no other backends besides our Flask backend dey allowed). Make di picture above smoothly zoomable, so you fit zoom all di way to individual ISBNs. Clicking ISBNs suppose carry you go metadata page or search on Anna’s Archive. You must still fit switch between all different Datasets. Country ranges and publisher ranges suppose dey highlighted on hover. You fit use e.g. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> for country info, and our “isbngrp” scrape for publishers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). E must work well on desktop and mobile. Plenty dey to explore here, so we dey announce bounty for improving di visualization above. Unlike most of our bounties, dis one get time limit. You gatz <a %(annas_archive)s>submit</a> your open source code by 2025-01-31 (23:59 UTC). Di best submission go get $6,000, second place na $3,000, and third place na $1,000. All bounties go dey awarded using Monero (XMR). Below na di minimal criteria. If no submission meet di criteria, we fit still award some bounties, but dat go dey at our discretion. For bonus points (these na just ideas — make your creativity run wild): Yu FIT change from di minimal criteria, and do somtin wey dey completely different. If e dey really spectacular, den e go qualify for di bounty, but na our decision. Make yu submit by posting comment to <a %(annas_archive)s>dis issue</a> wit link to your forked repo, merge request, or diff. - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dis picture na 1000×800 pixels. Each pixel dey represent 2,500 ISBNs. If we get file for an ISBN, we go make that pixel more green. If we know say ISBN don dey issued, but we no get matching file, we go make am more red. In less than 300kb, dis picture dey succinctly represent di largest fully open “list of books” wey dem don ever assemble for di history of humanity (a few hundred GB compressed in full). E also dey show: plenty work still dey to back up books (we only get 16%). Visualizin All ISBNs — $10,000 bounti by 2025-01-31 Dis picha reprẹsent di bigest ful open “list of buks” wey dem don gader fọ di histọri of pipul. Visualizing Apart from di overview image, we fit also look at individual Datasets we don acquire. Use di dropdown and buttons to switch between dem. Plenty interesting patterns dey to see for these pictures. Why some regularity of lines and blocks dey, wey dey happen for different scales? Wetin be di empty areas? Why some Datasets dey so clustered? We go leave these questions as exercise for di reader. - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusion With dis standard, we fit make releases more incrementally, and more easily add new data sources. We don already get a few exciting releases for the pipeline! We also hope say e go become easier for other shadow libraries to mirror our collections. After all, our goal na to preserve human knowledge and culture forever, so the more redundancy the better. Example Make we look our recent Z-Library release as example. E get two collections: “<span style="background: #fffaa3">zlib3_records</span>” and “<span style="background: #ffd6fe">zlib3_files</span>”. Dis one dey allow us to separately scrape and release metadata records from di actual book files. As such, we release two torrents with metadata files: We also release plenty torrents with binary data folders, but na only for di “<span style="background: #ffd6fe">zlib3_files</span>” collection, 62 in total: By running <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> we fit see wetin dey inside: For dis case, na metadata of a book as Z-Library report am. For di top-level we only get “aacid” and “metadata”, but no “data_folder”, since no dey corresponding binary data. Di AACID get “22430000” as di primary ID, wey we fit see say dem take from “zlibrary_id”. We fit expect other AACs for dis collection to get di same structure. Now make we run <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Dis na much smaller AAC metadata, though the bulk of dis AAC dey elsewhere inside a binary file! After all, we get “data_folder” dis time, so we fit expect the corresponding binary data to dey located at <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. The “metadata” get the “zlibrary_id”, so we fit easily associate am with the corresponding AAC inside the “zlib_records” collection. We fit don associate in a number of different ways, e.g. through AACID — the standard no dey prescribe that. Note say e no dey necessary for the “metadata” field to dey JSON itself. E fit be a string wey contain XML or any other data format. You fit even store metadata information inside the associated binary blob, e.g. if e dey plenty. Difrent kain files an metadata, as dem de for di orijinal format as much as posibul. Binary data fit de served direkly by webservers laik Nginx. Difrent kain aidentifayas for di sors laibraris, or even if aidentifayas no de. Separet riliz of metadata vs file data, or metadata-only riliz (e.g. our ISBNdb riliz). Distribushọn tru torrents, but wit di posibulity of oda distribushọn methods (e.g. IPFS). Records wey no go change, sins wi go asium se our torrents go de liv foreva. Incrementa riliz / appendable riliz. Mashin-fit to read an rait, kwikly an konviniently, espeshaly for our stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Sọmtin wey pesin fit luk wit ai, but dis na sekondri to mashin readability. Izi to sid our kolekshọn wit standard rented seedbox. Design goals Wi no de konsan if files de izi to waka tru wit hand for disk, or seachable witaut preprocessing. Wi no dey worry say we go fit work well well wit di library software wey dey already. Even though e suppose dey easy for anybody to seed our collection using torrents, we no dey expect say di files go dey usable without better technical knowledge and commitment. Our main use case na di distribushọn of files an di metadata wey de wit dem from difrent kolekshọn wey de. Our most important tins wey wi de konsida na: Sọmtin wey no be goal: Since Anna’s Archive na open source, we wan use our format directly. When we dey refresh our search index, we dey only access publicly available paths, so that anybody wey fork our library fit start dey run am quick quick. <strong>AAC.</strong> AAC (Anna’s Archive Container) na one single item wey get <strong>metadata</strong>, and optionally <strong>binary data</strong>, both of dem no fit change. E get one globally unique identifier, wey dem dey call <strong>AACID</strong>. <strong>AACID.</strong> Di fɔmat ɔf AACID na dis: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Fɔ ígzampul, wán ríl AACID wey wi rilís na <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID range.</strong> Since AACIDs get monotonically increasing timestamps, we fit use am to show ranges inside one particular collection. We dey use dis format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, where the timestamps dey inclusive. Dis dey consistent with ISO 8601 notation. Ranges dey continuous, and fit overlap, but if overlap dey, dem must get identical records as the one wey dem release before for that collection (since AACs no dey change). Missing records no dey allowed. <code>{collection}</code>: di kɔlɛkshɔn ném, wey fít kɔntén ASCII létas, nɔmbaz, an ɔndáskɔz (bɔt nɔ dɔbul ɔndáskɔ). <code>{collection-specific ID}</code>: a kɔlɛkshɔn-spésifik aidentifáia, if ápplikébul, fɔ ígzampul di Z-Library ID. Fít bì ɔmíted ɔr trɔnkéted. Mɔst bì ɔmíted ɔr trɔnkéted if di AACID wúd ɔdawáiz íksíd 150 káraktaz. <code>{ISO 8601 timestamp}</code>: a shɔt vɛshɔn ɔf di ISO 8601, ɔlwéz fɔ UTC, fɔ ígzampul <code>20220723T194746Z</code>. Dis nɔmba gɛt tɔ mɔnɔtɔnikali ínkriis fɔ evri rilís, dɔ íts ígzakt simántiks fít difa pɛ kɔlɛkshɔn. Wi sɔjest yúz di taim ɔf skrɛpin ɔr ɔf jɛnérétin di ID. <code>{shortuuid}</code>: na UUID wey dem compress to ASCII, e.g. using base57. We dey currently use the <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python library. <strong>Binary data folder.</strong> One folder wey get the binary data of a range of AACs, for one particular collection. Dem get the following properties: The directory must contain data files for all AACs inside the specified range. Each data file must get its AACID as the filename (no extensions). Directory name must be an AACID range, wey dem prefix with <code style="color: green">annas_archive_data__</code>, and no suffix. For example, one of our actual releases get a directory wey dem call<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. E dey recommended to make these folders somewhat manageable in size, e.g. no bigger than 100GB-1TB each, though dis recommendation fit change over time. <strong>Collection.</strong> Each AAC dey belong to one collection, wey by definition na list of AACs wey dey semantically consistent. E mean say if you make significant change to di format of di metadata, then you go need create new collection. Di standard <strong>Metadata file.</strong> One metadata file dey contain the metadata of a range of AACs, for one particular collection. Dem get the following properties: <code>data_folder</code> na optional, and na the name of binary data folder wey dey contain the corresponding binary data. The filename of the corresponding binary data inside that folder na the record’s AACID. Each JSON object must contain the following fields for the top level: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). No other fields dey allowed. Filename must be an AACID range, wey dem prefix with <code style="color: red">annas_archive_meta__</code> and follow with <code>.jsonl.zstd</code>. For example, one of our releases dey called<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. As the file extension show, the file type na <a %(jsonlines)s>JSON Lines</a> wey dem compress with <a %(zstd)s>Zstandard</a>. <code>metadata</code> na arbitrary metadata, per the semantics of the collection. E must dey semantically consistent inside the collection. The <code style="color: red">annas_archive_meta__</code> prefix fit dey adapt to the name of your institution, e.g. <code style="color: red">my_institute_meta__</code>. <strong>“ríkɔdz” an “faɪlz” kɔlɛkshɔn.</strong> Bai kɔnvénshɔn, íts ɔftin kɔnvinyɛnt tɔ rilís “ríkɔdz” an “faɪlz” az difrɛnt kɔlɛkshɔn, só dé fít rilís fɔ difrɛnt skɛjul, fɔ ígzampul bésd ɔn skrɛpin réts. A “ríkɔd” na metadata-ónli kɔlɛkshɔn, wey kɔntén infɔméshɔn laik buk táituls, ɔtɔrz, ISBNs, etc, waíl “faɪlz” na di kɔlɛkshɔn wey kɔntén di ríl faɪlz dɛm sɛlf (pdf, epub). At di end, we settle for a relatively simple standard. E dey fairly loose, non-normative, and e still dey work in progress. <strong>Torrents.</strong> The metadata files and binary data folders fit dey bundle inside torrents, with one torrent per metadata file, or one torrent per binary data folder. The torrents must get the original file/directory name plus a <code>.torrent</code> suffix as their filename. <a %(wikipedia_annas_archive)s>Anna’s Archive</a> don become by far di biggest shadow library for di world, and di only shadow library of im scale wey dey fully open-source and open-data. Below na table from our Datasets page (small modification dey): We achieve dis in three ways: Mirroring existing open-data shadow libraries (like Sci-Hub and Library Genesis). Helping out shadow libraries wey wan dey more open, but no get di time or resources to do so (like di Libgen comics collection). Scraping libraries wey no wan share in bulk (like Z-Library). For (2) an (3) wi de manage big kolekshọn of torrents by awẹsef (100s of TBs). So far wi don de handle dis kolekshọn as wan-wan, wey mean se difrent infrastrọkchọ an data organizashọn for each kolekshọn. Dis de add big ovạhed to each riliz, an mek am hard to do mor incrementa riliz. Na why wi desaid to standardaiz our riliz. Dis na teknikal blog post wey wi de introdius our standard: <strong>Anna’s Archive Containers</strong>. Anna’s Archive Kontena (AAC): mek we standardize di riliz we dey do from di world biggest shadow library Anna’s Archive don become di biggest shadow library for di world, so we need to standardize di riliz we dey do. 300GB+ of book covers released Finally, we dey happy to announce small release. In collaboration with di people wey operate di Libgen.rs fork, we dey share all their book covers through torrents and IPFS. Dis go distribute di load of viewing di covers among more machines, and go preserve dem better. In many (but not all) cases, di book covers dey included in di files themselves, so dis na kind of “derived data”. But having am in IPFS still dey very useful for daily operation of both Anna Improvment and di various Library Genesis forks. As usual, you fit find dis release at di Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna Improvment</a>). We no go put link to am here, but you fit easily find am. Hopefully we fit relax our pace small, now wey we get decent alternative to Z-Library. Dis workload no dey particularly sustainable. If you dey interested in helping out with programming, server operations, or preservation work, definitely reach out to us. E still get plenty <a %(annas_archive)s>work to be done</a>. Thanks for your interest and support. Switch to ElasticSearch Some queries dey take super long, to di point where dem go hog all di open connections. By default MySQL get minimum word length, or your index fit get really large. People report say dem no fit search for “Ben Hur”. Search dey only somewhat fast when fully loaded in memory, which require us to get more expensive machine to run dis on, plus some commands to preload di index on startup. We no fit extend am easily to build new features, like better <a %(wikipedia_cjk_characters)s>tokenization for non-whitespaced languages</a>, filtering/faceting, sorting, "did you mean" suggestions, autocomplete, and so on. One of our <a %(annas_archive)s>tickets</a> na grab-bag of issues with our search system. We dey use MySQL full-text search, since we get all our data for MySQL anyway. But e get im limits: After we talk to plenty experts, we settle on ElasticSearch. E no dey perfect (their default “did you mean” suggestions and autocomplete features no good), but overall e don better pass MySQL for search. We still no <a %(youtube)s>too keen</a> on using am for any mission-critical data (though dem don make plenty <a %(elastic_co)s>progress</a>), but overall we dey quite happy with di switch. For now, we don implement much faster search, better language support, better relevancy sorting, different sorting options, and filtering on language/book type/file type. If you dey curious how e dey work, <a %(annas_archive_l140)s>make</a> <a %(annas_archive_l1115)s>you</a> <a %(annas_archive_l1635)s>look</a>. E dey fairly accessible, though e fit need some more comments… Anna Improvment na fully open source We believe say information suppose dey free, and our own code no be exception. We don release all our code for our privately hosted Gitlab instance: <a %(annas_archive)s>Anna Software</a>. We dey also use di issue tracker to organize our work. If you wan engage with our development, dis na great place to start. To give you small taste of di tins we dey work on, check our recent work on client-side performance improvements. Since we never implement pagination yet, we dey often return very long search pages, with 100-200 results. We no wan cut off di search results too soon, but dis mean say e go slow down some devices. For dis, we implement small trick: we wrap most search results in HTML comments (<code><!-- --></code>), and then write small Javascript wey go detect when result suppose become visible, at which moment we go unwrap di comment: DOM "virtualization" implemented in 23 lines, no need for fancy libraries! Dis na di kind quick pragmatic code wey you go end up with when you get limited time, and real problems wey need solution. E don dey reported say our search now dey work well on slow devices! Another big effort na to automate building di database. When we launch, we just dey pull different sources together anyhow. Now we wan keep dem updated, so we write plenty scripts to download new metadata from di two Library Genesis forks, and integrate dem. Di goal na to not just make dis useful for our archive, but to make tins easy for anyone wey wan play around with shadow library metadata. Di goal go be Jupyter notebook wey get all sorts of interesting metadata available, so we fit do more research like figuring out wetin <a %(blog)s>percentage of ISBNs dey preserved forever</a>. Finally, we revamp our donation system. You fit now use credit card to directly deposit money into our crypto wallets, without really needing to know anything about cryptocurrencies. We go keep monitoring how well dis dey work in practice, but dis na big deal. As Z-Library don go down and dem arrest di (alleged) founders, we don de wok tireless to provide beta alternative wit Anna Improvment (we no go put link here, but you fit Google am). Here na some of di tins we don achieve recently. Anna Improvment: Open Source Archive, ElasticSearch, 300GB+ of Book Covers We don de wok tireless to provide beta alternative wit Anna Improvment. Here na some of di tins we don achieve recently. Analysis Semantic duplicates (different scans of the same book) fit theoretically dey filtered out, but e dey tricky. Wen we manually look through the comics, we find too many false positives. Some duplicates dey purely by MD5, wey dey relatively wasteful, but to filter those out go only give us about 1% in savings. For this scale, na still about 1TB, but also, for this scale 1TB no really matter. We go prefer make we no risk accidentally destroy data for this process. We find plenty non-book data, like movies wey dem base on comic books. That one too dey wasteful, since these dey already widely available through other means. However, we realize say we no fit just filter out movie files, since some <em>interactive comic books</em> dey wey dem release on computer, wey person record and save as movies. Ultimately, anything we fit delete from the collection go only save few percent. Then we remember say we be data hoarders, and the people wey go dey mirror this na data hoarders too, so, “WETIN YOU MEAN, DELETE?!” :) Wen yu get 95TB wey dem dump for your storage cluster, yu go wan try understand wetin dey inside… We do some analysis to see if we fit reduce the size small, like by removing duplicates. Here be some of our findings: So, we dey present to you, the full, unmodified collection. E plenty well well, but we hope say enough people go care to seed am anyway. Collaboration Because of di size, dis collection don dey our wishlist for long, so after our success with backing up Z-Library, we set our sights on dis collection. At first, we scrape am directly, wey be big challenge, since their server no dey in di best condition. We get about 15TB dis way, but e dey slow. Luckily, we manage contact di operator of di library, wey agree to send us all di data directly, wey fast pass. E still take more than half a year to transfer and process all di data, and we nearly lose all of am to disk corruption, wey for mean say we go start all over. Dis experience don make us believe say e dey important to get dis data out there as quickly as possible, so e fit dey mirrored far and wide. We dey just one or two unlucky timed incidents away from losing dis collection forever! Di collection To move fast mean say di collection dey a little unorganized… Make we look am. Imagine say we get one filesystem (wey in reality we dey split across torrents): Di first directory, <code>/repository</code>, na di more structured part of dis. Dis directory contain wetin dem dey call “thousand dirs”: directories each with a thousand files, wey dem dey number incrementally for di database. Directory <code>0</code> contain files with comic_id 0–999, and so on. Dis na di same scheme wey Library Genesis don dey use for its fiction and non-fiction collections. Di idea be say every “thousand dir” go automatically turn into a torrent as soon as e full. However, di Libgen.li operator never make torrents for dis collection, and so di thousand dirs likely become inconvenient, and give way to “unsorted dirs”. These na <code>/comics0</code> through <code>/comics4</code>. Dem all contain unique directory structures, wey probably make sense for collecting di files, but no make too much sense to us now. Luckily, di metadata still dey refer directly to all these files, so their storage organization on disk no really matter! Di metadata dey available in di form of a MySQL database. Dis fit be downloaded directly from di Libgen.li website, but we go also make am available in a torrent, alongside our own table with all di MD5 hashes. <q>Dr. Barbara Gordon dey try lose herself for di ordinary world of di library…</q> Libgen forks First, make we give some background. You fit sabi Library Genesis for their epic book collection. Fewer people sabi say Library Genesis volunteers don create other projects, like one big collection of magazines and standard documents, one full backup of Sci-Hub (in collaboration with di founder of Sci-Hub, Alexandra Elbakyan), and truly, one massive collection of comics. At some point, different operators of Library Genesis mirrors go their separate ways, wey give rise to di current situation of having a number of different “forks”, all still carrying di name Library Genesis. Di Libgen.li fork uniquely get dis comics collection, as well as one big magazines collection (wey we dey also work on). Fundraiser We dey release this data in some big chunks. The first torrent na of <code>/comics0</code>, wey we put inside one huge 12TB .tar file. E better for your hard drive and torrent software than plenty smaller files. As part of this release, we dey do fundraiser. We dey look to raise $20,000 to cover operational and contracting costs for this collection, as well as enable ongoing and future projects. We get some <em>massive</em> ones wey dey come. <em>Who am I supporting with my donation?</em> In short: we dey back up all knowledge and culture of humanity, and make am easily accessible. All our code and data dey open source, we be completely volunteer-run project, and we don save 125TB worth of books so far (in addition to Libgen and Scihub’s existing torrents). Ultimately we dey build a flywheel wey go enable and incentivize people to find, scan, and backup all the books for the world. We go write about our master plan for future post. :) If you donate for a 12 month “Amazing Archivist” membership ($780), you go fit <strong>“adopt a torrent”</strong>, meaning say we go put your username or message for the filename of one of the torrents! You fit donate by going to <a %(wikipedia_annas_archive)s>Anna’s Archive</a> and clicking the “Donate” button. We dey also look for more volunteers: software engineers, security researchers, anonymous merchant experts, and translators. You fit also support us by providing hosting services. And of course, abeg seed our torrents! Thanks to everyone wey don generously support us already! You dey truly make a difference. Here be the torrents wey we don release so far (we dey still process the rest): All torrents fit dey found on <a %(wikipedia_annas_archive)s>Anna’s Archive</a> under “Datasets” (we no dey link there directly, so links to this blog no go dey removed from Reddit, Twitter, etc). From there, follow the link to the Tor website. <a %(news_ycombinator)s>Talk am for Hacker News</a> Wetin dey next? Plenty torrents dey great for long-term preservation, but no too much for everyday access. We go dey work with hosting partners to get all this data up on the web (since Anna’s Archive no dey host anything directly). Of course you go fit find these download links on Anna’s Archive. We dey also invite everyone to do stuff with this data! Help us better analyze am, deduplicate am, put am on IPFS, remix am, train your AI models with am, and so on. E dey all yours, and we no fit wait to see wetin you go do with am. Finally, as we don talk before, we still get some massive releases wey dey come (if <em>somebody</em> fit <em>accidentally</em> send us a dump of a <em>certain</em> ACS4 database, you sabi where to find us…), as well as building di flywheel for backing up all di books for di world. So mek yu dey redi, wi just dey start. - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Di bigest shadow library wey get comic books fit be one Library Genesis fork: Libgen.li. Di one admin wey dey run dat site manage gather one crazy comics collection wey get over 2 million files, wey reach over 95TB. But, unlike other Library Genesis collections, dis one no dey available in bulk through torrents. You fit only access these comics one by one through im slow personal server — one single point wey fit fail. Until today! For dis post, we go tell you more about dis collection, and about our fundraiser to support more of dis work. Anna Improvment don back up di world’s largest comics shadow library (95TB) — you fit help seed am Di bigest shadow library wey get comic books for di world bin get only one point wey fit fail.. until today. Warning: dis blog post don dey deprecated. We don decide say IPFS never ready for prime time. We go still link to files on IPFS from Anna’s Archive when possible, but we no go host am ourselves again, nor we dey recommend others to mirror using IPFS. Abeg see our Torrents page if you wan help preserve our collection. Putting 5,998,794 books on IPFS A multiplication of copies Back to our original question: how we fit tok say we go keep our kolekshọn fɔeva? Di main problem here na say our kolekshọn don dey <a %(torrents_stats)s>grow</a> fast, by scraping an open-sourcing some big collections (on top of di amazing work wey other open-data shadow libraries like Sci-Hub an Library Genesis don already do). Dis growth in data dey make am harder for di collections to dey mirror around di world. Data storage dey expensive! But we dey optimistic, especially when we dey observe di following three trends. Di <a %(annas_archive_stats)s>total size</a> of our kolekshọn, fɔ di last few months, break am down by number of torrent seeders. HDD price trends from different sources (click to view study). <a %(critical_window_chinese)s>Chinese version 中文版</a>, tok about am fɔ <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Wi don pluk di low-hangin frut Dis wan dey follow di priorities wey we don discuss above. We dey prefer to work on liberatin big collections first. Now wey we don secure some of di biggest collections for di world, we dey expect our growth to slow down well well. Still dey plenty small small collections, and new books dey get scanned or published every day, but di rate go slow down well well. We fit still double or even triple for size, but na over long time period. OCR improvements. Priorities Science & engineering software code Fictional o entertainment versions of all di tins wey dey above Geographic data (e.g. maps, geological surveys) Intana data wey kompinis o govment get (leaks) Measurement data like scientific measurements, economic data, corporate reports Metadata records wey dey general (of non-fiction an fiction; of oda media, art, pipul, etc; wey include reviews) Non-fiction books Non-fiction magazines, newspapers, manuals Non-fiction transcripts of talks, documentaries, podcasts Organic data like DNA sequences, plant seeds, or microbial samples Academic papers, journals, reports Science & engineering websites, online discussions Transcripts of legal o court proceedings Uniquely at risk of destruction (e.g. by war, funding cuts, lawsuits, or political persecution) Rare Uniquely underfocused Why we dey care so much about papers and books? Make we put aside our fundamental belief in preservation in general — we fit write another post about dat one. So why papers and books specifically? Di answer dey simple: <strong>information density</strong>. Per megabyte of storage, written text dey store di most information out of all media. While we dey care about both knowledge and culture, we dey care more about di former. Overall, we find one hierarchy of information density and importance of preservation wey look roughly like dis: Di ranking for dis list na somehow arbitrary — some items dey tie or get disagreements inside our team — an we fit dey forget some important categories. But na roughly how we dey prioritize. Some of dis items dey too different from di oda ones for us to worry about (or dem don already take care of am by oda institutions), like organic data or geographic data. But most of di items for dis list dey actually important to us. Anoda big factor for our prioritization na how much at risk one certain work dey. We prefer to focus on works wey be: Finally, we care about scale. We get limited time an money, so we go rather spend one month to save 10,000 books than 1,000 books — if dem dey about equally valuable an at risk. <em><q>Di tin wey don lost no fit recover; but make we save wetin remain: no be by vaults an locks wey go hide dem from di public eye an use, consigning dem to di waste of time, but by such a multiplication of copies, as go place dem beyond di reach of accident.</q></em><br>— Thomas Jefferson, 1791 Shadow libraries Code fit dey open source on Github, but Github as a whole no fit easily mirror an thus preserve (though fɔ dis particular case, plenty distributed copies of most code repositories dey) Metadata records fit dey freely viewed on di Worldcat website, but no fit download in bulk (until we <a %(worldcat_scrape)s>scraped</a> dem) Reddit dey free to use, but recently dem don put up strong anti-scraping measures, because of data-hungry LLM training (more about dat later) Plenty organizations dey wey get similar missions, an similar priorities. True true, libraries, archives, labs, museums, an oda institutions dey wey dem task with preservation of dis kind. Many of dem dey well-funded, by governments, individuals, or corporations. But dem get one massive blind spot: di legal system. Na here shadow libraries get unique role, an na di reason why Anna’s Archive dey exist. We fit do things wey oda institutions no dey allowed to do. Now, no be say we fit archive materials wey illegal to preserve elsewhere. No, e dey legal for many places to build an archive with any books, papers, magazines, an so on. But wetin legal archives dey lack na <strong>redundancy an longevity</strong>. Some buks dey wey na only one copy dey fɔ some physical library somewhere. Some metadata records dey wey only one company dey protect. Some newspapers dey wey na only microfilm dey preserve dem fɔ one archive. Libraries fit get funding cuts, companies fit go bankrupt, archives fit get bombed an burn down. Dis no be hypothetical — e dey happen all di time. Di tin wey we fit do fɔ Anna’s Archive na to store plenty copies of works, fɔ large scale. We fit kolekt papers, buks, magazines, an more, an distribute dem in bulk. We dey do dis through torrents now, but di exact technologies no matter an go change over time. Di important part na to get plenty copies distributed across di world. Dis quote from over 200 years ago still dey true: Quick note about public domain. Since Anna’s Archive dey focus on activities wey dey illegal fɔ many places around di world, we no dey bother with widely available collections, like public domain books. Legal entities dey often take good care of dat. However, some reasons dey wey make us sometimes work on publicly available collections: - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Storage costs dey continue to drop well well 3. Improvements in information density We dey currently store books for di raw formats wey dem give us. Sure, dem dey compressed, but often dem still dey large scans or photographs of pages. Until now, di only options to shrink di total size of our collection na through more aggressive compression, or deduplication. However, to get significant enough savings, both dey too lossy for our taste. Heavy compression of photos fit make text barely readable. And deduplication require high confidence of books being exactly di same, which dey often too inaccurate, especially if di contents dey di same but di scans dey made on different occasions. There don always dey a third option, but e quality don dey so bad we never consider am: <strong>OCR, or Optical Character Recognition</strong>. Dis na di process of converting photos into plain text, by using AI to detect di characters in di photos. Tools for dis don long exist, and don dey pretty decent, but “pretty decent” no dey enough for preservation purposes. However, recent multi-modal deep-learning models don make extremely rapid progress, though still at high costs. We dey expect both accuracy and costs to improve dramatically in coming years, to di point where e go become realistic to apply to our entire library. When dat happen, we go likely still preserve di original files, but in addition we fit get a much smaller version of our library wey most people go want to mirror. Di kicker na say raw text itself dey compress even better, and dey much easier to deduplicate, giving us even more savings. Overall e no dey unrealistic to expect at least a 5-10x reduction in total file size, perhaps even more. Even with a conservative 5x reduction, we go dey look at <strong>$1,000–$3,000 in 10 years even if our library triples in size</strong>. As at di time wey we dey write dis, <a %(diskprices)s>disk prices</a> per TB dey around $12 for new disks, $8 for used disks, and $4 for tape. If we dey conservative and look only at new disks, e mean say to store one petabyte go cost about $12,000. If we assume say our library go triple from 900TB to 2.7PB, e go mean $32,400 to mirror our entire library. Add electricity, cost of other hardware, and so on, make we round am up to $40,000. Or with tape more like $15,000–$20,000. On one hand <strong>$15,000–$40,000 for di sum of all human knowledge na steal</strong>. On di other hand, e dey a bit steep to expect plenty full copies, especially if we go like make those people dey keep seeding their torrents for di benefit of others. Na today be dat. But progress dey move forward: Hard drive costs per TB don roughly cut into third over di last 10 years, and e go likely continue to drop at similar pace. Tape dey appear to dey on similar path. SSD prices dey drop even faster, and fit take over HDD prices by di end of di decade. If dis hold, then in 10 years we fit dey look at only $5,000–$13,000 to mirror our entire collection (1/3rd), or even less if we grow less in size. While e still plenty money, dis go dey attainable for many people. And e fit even better because of di next point… Fɔ Anna’s Archive, pipul dey always ask us how we fit tok say we go keep our kolekshọn fɔeva, wen di total size don dey reach 1 Petabyte (1000 TB), an e still dey grow. Fɔ dis article, we go look our filosofi, an see why di next ten years dey important fɔ our mission to keep di knowledge an culture wey humanity get. Critical window If these forecasts dey accurate, we <strong>just need to wait a couple of years</strong> before our entire collection go dey widely mirrored. Thus, in di words of Thomas Jefferson, “placed beyond di reach of accident.” Unfortunately, di advent of LLMs, and their data-hungry training, don put plenty copyright holders on di defensive. Even more than dem already dey. Many websites dey make am harder to scrape and archive, lawsuits dey fly around, and all di while physical libraries and archives dey continue to dey neglected. Wi fit expect say dis trends go continue to dey worse, and many works go lost well before dem enter public domain. <strong>We dey on di eve of a revolution for preservation, but <q>di ones wey don lost no fit recover.</q></strong> We get critical window of about 5-10 years wey e still dey fairly expensive to operate shadow library and create many mirrors around di world, and wey access never completely shut down yet. If we fit bridge dis window, then we go truly preserve humanity knowledge and culture forever. We no suppose let dis time waste. We no suppose let dis critical window close on us. Make we go. Di important window of shadow libraries How we fit tok say we go keep our kolekshọn fɔeva, wen dem don dey reach 1 PB? Collection Some more information about di collection. <a %(duxiu)s>Duxiu</a> na massive database of scanned books, wey <a %(chaoxing)s>SuperStar Digital Library Group</a> create. Most na academic books, dem scan am to make dem available digitally to universities an libraries. For our English-speaking audience, <a %(library_princeton)s>Princeton</a> an di <a %(guides_lib_uw)s>University of Washington</a> get good overviews. Dere dey also one excellent article wey dey give more background: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (look am up for Anna’s Archive). D buks wey dey from Duxiu don dey pirated for Chinese internet for long. Dem dey usually sell am for less than one dollar by resellers. Dem dey typically distribute am using di Chinese version of Google Drive, wey dem don hack many times to allow for more storage space. Some technical details dey <a %(github_duty_machine)s>here</a> and <a %(github_821_github_io)s>here</a>. Even though di books don dey semi-publicly distributed, e dey quite difficult to obtain dem in bulk. We get dis high on our TODO-list, and allocate multiple months of full-time work for am. However, recently an incredible, amazing, and talented volunteer reach out to us, tell us say dem don do all dis work already — at great expense. Dem share di full collection with us, without expecting anything in return, except di guarantee of long-term preservation. Truly remarkable. Dem agree to ask for help in dis way to get di collection OCR'ed. Di collection na 7,543,702 files. Dis pass Library Genesis non-fiction (about 5.3 million). Total file size na about 359TB (326TiB) in its current form. We dey open to other proposals and ideas. Just contact us. Check out Anna’s Archive for more information about our collections, preservation efforts, and how you fit help. Thanks! Example pages To prove to us say you get good pipeline, here dey some example pages to start with, from one book on superconductors. Your pipeline suppose properly handle math, tables, charts, footnotes, an so on. Send your processed pages to our email. If dem look good, we go send you more in private, an we expect you to fit quickly run your pipeline on those as well. Once we dey satisfied, we fit make a deal. - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Chinese version 中文版</a>, <a %(news_ycombinator)s>Discuss on Hacker News</a> Dis na short blog post. We dey look for some company or institution to help us with OCR and text extraction for di massive collection we acquire, in exchange for exclusive early access. After di embargo period, we go release di entire collection. High-quality academic text dey extremely useful for training of LLMs. Even though our collection na Chinese, e go still dey useful for training English LLMs: models dey encode concepts and knowledge regardless of di source language. For dis, text need to dey extracted from di scans. Wetin Anna’s Archive go gain from am? Full-text search of di books for its users. Because our goals align with that of LLM developers, we dey look for collaborator. We dey willing to give you <strong>exclusive early access to dis collection in bulk for 1 year</strong>, if you fit do proper OCR and text extraction. If you dey willing to share di entire code of your pipeline with us, we go dey willing to embargo di collection for longer. Exclusive access for LLM companies to largest Chinese non-fiction book collection for di world <em><strong>TL;DR:</strong> Anna’s Archive don acquire unique collection of 7.5 million / 350TB Chinese non-fiction books — e big pass Library Genesis. We dey willing to give LLM company exclusive access, in exchange for high-quality OCR and text extraction.</em> Sistem akitẹktọ So mek wí se yú fain som kọmpani dem wey de wilin to host yọ website witaut shọt yú daun — mek wí kọọl dis “fridọm-lọvin providas” 😄. Yú go kwikli fain se hostin evritin wit dem de rader ekspensiv, so yú fit wan fain som “chip providas” an du di riyal hostin dere, prọksiin tru di fridọm-lọvin providas. If yú du am raít, di chip providas nọn go evan sabi wetin yú de host, an dem nọn go risiv eni kọnplẹnt. Wit ọl dis providas, riski dey se dem go shọt yú daun eniwei, so yú nid redundansi. Wí nid dis on ọl levuls of aw stack. Wọn sọọmwat fridọm-lọvin kọmpani wey put im sɛf fɔ an intarestin pọzishọn na Cloudflare. Dem <a %(blog_cloudflare)s>argu</a> se dem nọn bi hostin provida, bọt a yutiliti, laik an ISP. Dem nọn de subject to DMCA or oda tẹkdaun rikwẹst, an dem de fọwọd eni rikwẹst to yọ riyal hostin provida. Dem dọn go as far as go kọt to prọtẹkt dis strọkchọ. Wí fit yus dem as anọda leya of kashin an prọtẹkshọn. Cloudflare nọn de asept anọnimọs peymẹnt, so wí fit onli yus dia fri plan. Dis min se wí nọn fit yus dia lọd balansin or failovẹ fịchọs. Wí deọfọ <a %(annas_archive_l255)s>implimẹnt dis aw sɛf</a> at di domẹn level. On pej lọd, di brọwsa go chẹk if di kọnrent domẹn stil de avẹlabl, an if nọn, i go riraít ọl URLs to anọda domẹn. Sins Cloudflare de kash meni pejes, dis min se a yúza fit land on aw main domẹn, ivin if di prọksi sẹva de daun, an den on di nekst klik, dem go muv to anọda domẹn. Wí stil get nọọmal ọpẹreshọnal kọnsẹn to dil wit, laik monitọrin sẹva hẹlt, lọgin backend an frontend erọs, an so on. Aw failovẹ akitẹktọ alaw fɔ mọ robustnes on dis front as wel, fọ igzampul bai runnin a kọnplitli difrent set of sẹvas on wọn of di domẹns. Wí fit ivin run ọlda vẹshọn of di kọd an Datasets on dis sepẹret domẹn, in kes a kritikal bọg in di main vẹshọn go nọnotis. Wí fit also hẹj agẹnst Cloudflare tọnin agẹnst wí, bai rimuvin am from wọn of di domẹns, laik dis sepẹret domẹn. Difrent pẹmyuteshọn of dis ideas de posibul. Conclusion E don be interesting experience to learn how to set up a robust and resilient shadow library search engine. Plenty more details dey to share for later posts, so mek I know wetin you go like learn more about! As olweiz, wí de luk fɔ doneshọn to support dis wọk, so mek yú chẹk di Donate pej on Anna’s Archive. Wí de also luk fɔ oda kain support, laik grants, long-tẹm sponsọs, high-risk peymẹnt providas, prọbabli ivin (tẹstful!) ads. An if yú wan kontribyut yọ taim an skil, wí de olweiz luk fɔ dẹvẹlọpas, transletọs, an so on. Tẹnk yú fɔ yọ intarest an support. Inovẹshọn tọnkin Mek wí start wit aw tech stack. I de delibẹretli bọrin. Wí de yus Flask, MariaDB, an ElasticSearch. Na dat bi ọl. Sẹch na somtin wey dem dọn solv, an wí nọn de plan to reinvẹnt am. Bẹsáid, wí gọt to spend aw <a %(mcfunley)s>inovẹshọn tọnkin</a> fɔ anọda tin: mek dem nọn tek wí daun. So how ligal or iligal Anna’s Archive bi? Dis mosli depend on di ligal jọrisdikshọn. Mos kontris biliv in som fọm of kọpirait, wey min se pipul or kọmpani dem get eksklusiv monopoli on sẹtin kain wọk fɔ sẹtin taim. As an asáid, fɔ Anna’s Archive wí biliv se wail som bẹnẹfit dey, ọvọral kọpirait na net-negativ fɔ sọsáyeti — bọt dat na tori fɔ anọda taim. Dis eksklusiv monopoli on sẹtin wọk min se i de iligal fɔ eniwọn wey nọn de insaid dis monopoli to dairektli distribut dos wọk — inkludin wí. Bọt Anna’s Archive na sẹch injin wey nọn dairektli distribut dos wọk (at list nọn fɔ aw klianet website), so wí shud dey ọkei, raít? Nọn egzaktli. Fɔ meni jọrisdikshọn, i nọn onli iligal to distribut kọpiraited wọk, bọt also to link to ples wey de du am. A klasik igzampul of dis na di Yunaited Stets’ DMCA lɔ. Dat na di strikt end of di spẹktrum. On di oda end of di spẹktrum, dere fit dey kontris wey nọn get kọpirait lɔ at ọl, bọt dis kain kontris nọn rili exist. Prẹti moch evri kontri get som fọm of kọpirait lɔ on di buks. Infọsmẹnt na anọda tori. Plenty kontris dey wey di gọvẹnmẹnt nọn kẹr to infọs kọpirait lɔ. Also, kontris dey in bitwin di tu ekstrem, wey prohibit distributin kọpiraited wọk, bọt nọn prohibit linkin to sọọch wọk. Anọda konsidereshọn na at di kọmpani-level. If a kọmpani ọpẹret fɔ jọrisdikshọn wey nọn kẹr abaut kọpirait, bọt di kọmpani sɛf nọn wan tek eni risk, den dem fit shọt daun yọ website as sọn as eniwọn kọnplẹn abaut am. Fainali, a big konsidereshọn na peymẹnt. Sins wí nid to stẹ anọnimọs, wí nọn fit yus tradishọnal peymẹnt metod. Dis liv wí wit kriptọkọrensi, an onli a smọl subset of kọmpani dem de sọọch (dey dey virtual debit kads wey kripto de pey, bọt dem nọn de ofin asepted). - Anna an di tim (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) A de run <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di wórld bigest open-sọs nɔn-prọfit sẹch injin fɔ <a %(wikipedia_shadow_library)s>shadó laibrari dem</a>, laik Sci-Hub, Library Genesis, an Z-Library. Aw aim na to mek nọlej an kọltọr redili aksesibul, an fainali to bild a kọmuniti of pipul wey go join hand to arkaiv an prẹzav <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>ọl di buks fɔ di wórld</a>. Fɔ dis atikul a go shọ how wí de run dis website, an di speshial chálẹnj wey de kam wit ọpẹretin a website wey get kwẹshọnabul ligal stetus, sins dè no dey “AWS fɔ shadó cháríti dem”. <em>Mek yú chẹk di sista atikul <a %(blog_how_to_become_a_pirate_archivist)s>How to become a pirate archivist</a>.</em> How to run a shadow library: operations at Anna’s Archive Dè no dey <q>AWS fɔ shadó cháríti dem,</q> so how wí go run Anna’s Archive? Tuls Application server: Flask, MariaDB, ElasticSearch, Docker. Development: Gitlab, Weblate, Zulip. Server management: Ansible, Checkmk, UFW. Onion static hosting: Tor, Nginx. Proxy server: Varnish. Mek wi luk di tools wey wi dey use to achieve all dis. Dis dey change as we dey face new problems an find new solutions. Some decisions dey wey we don dey change mind on. One na di communication between servers: we bin dey use Wireguard for dis, but we find say e dey sometimes stop to transmit any data, or e go only transmit data for one direction. Dis happen with different Wireguard setups wey we try, like <a %(github_costela_wesher)s>wesher</a> and <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. We also try to tunnel ports over SSH, using autossh and sshuttle, but we face <a %(github_sshuttle)s>problems there</a> (even though e still no clear to me if autossh dey suffer from TCP-over-TCP issues or not — e just dey feel like one janky solution to me but maybe e dey actually okay?). Instead, we go back to direct connections between servers, dey hide say server dey run on cheap providers using IP-filtering with UFW. Dis get downside say Docker no dey work well with UFW, unless you use <code>network_mode: "host"</code>. All dis dey more prone to error, because you fit expose your server to di internet with just small misconfiguration. Maybe we suppose go back to autossh — feedback go dey very welcome here. We don also dey change mind on Varnish vs. Nginx. We currently like Varnish, but e get e own quirks and rough edges. Di same apply to Checkmk: we no love am, but e dey work for now. Weblate dey okay but no dey incredible — I sometimes dey fear say e go lose my data whenever I try to sync am with our git repo. Flask dey good overall, but e get some weird quirks wey don cost plenty time to debug, like configuring custom domains, or issues with e SqlAlchemy integration. So far di other tools dey great: we no get serious complaints about MariaDB, ElasticSearch, Gitlab, Zulip, Docker, and Tor. All of dem don get some issues, but nothing wey dey overly serious or time-consuming. Community Di first challenge fit surprise you. E no be technical problem, or legal problem. E be psychological problem: doing dis work in di shadows fit dey incredibly lonely. Depending on wetin you dey plan to do, and your threat model, you fit need to dey very careful. On di one end of di spectrum we get people like Alexandra Elbakyan*, di founder of Sci-Hub, wey dey very open about her activities. But she dey at high risk of being arrested if she visit western country at dis point, and fit face decades of prison time. Na risk you go wan take? We dey at di other end of di spectrum; dey very careful not to leave any trace, and get strong operational security. * As dem mention for HN by "ynno", Alexandra initially no want make people sabi her: "Her servers dey set up to emit detailed error messages from PHP, including full path of faulting source file, wey dey under directory /home/<USER>" So, use random usernames on di computers you dey use for dis stuff, in case you misconfigure something. Dat secrecy, however, dey come with psychological cost. Most people love make dem recognize dem for di work wey dem dey do, and yet you no fit take any credit for dis in real life. Even simple things fit dey challenging, like friends dey ask you wetin you don dey up to (at some point "messing with my NAS / homelab" go tire). Dis na why e dey so important to find some community. You fit give up some operational security by confiding in some very close friends, wey you know you fit trust deeply. Even then make you dey careful not to put anything in writing, in case dem need to turn over dia emails to di authorities, or if dia devices dey compromised in some other manner. Better still na to find some fellow pirates. If your close friends dey interested in joining you, great! Otherwise, you fit find others online. Sadly dis still be niche community. So far we don find only a handful of others wey dey active in dis space. Good starting places seem to be di Library Genesis forums, and r/DataHoarder. Di Archive Team also get likeminded individuals, though dem dey operate within di law (even if in some grey areas of di law). Di traditional "warez" and pirating scenes also get folks wey dey think in similar ways. Wi de open to ideas on how we fit build community an explore ideas. Feel free to message us for Twitter or Reddit. Maybe we fit host some kind forum or chat group. One wahala be say dis fit easily get censored when we dey use common platforms, so we go need to host am by ourselves. E get as e be between making these discussions fully public (more potential engagement) versus making am private (not letting potential "targets" know say we dey about to scrape them). We go need to think about that. Let us know if you dey interested in this! Conclusion Hopefully dis go dey helpful for newly starting pirate archivists. We dey excited to welcome yu to dis world, so no hesitate to reach out. Make we preserve as much of di world's knowledge an culture as we fit, an mirror am far an wide. Projects 4. Data selection Sọmtain yu fit yus di metadata te sabi di resọnabul subset of data wey yu go download. Ivun if yu wan download all di data at di end, e fit dey useful to first download di most important items, in case dem catch yu an dem improve di defences, or because yu go need buy more disks, or simply because somtin else fit happen for yu life before yu fit download everytin. For example, wan collection fit get multiple editions of di same resource (like book or film), wey one dey marked as di best quality. To save those editions first go make sense. Yu fit wan save all editions later, because sometimes di metadata fit dey tagged wrong, or fit get unknown tradeoffs between editions (for example, di "best edition" fit dey best for most ways but worse for other ways, like film wey get higher resolution but no get subtitles). Yu fit also search yu metadata database to find interesting things. Wetin be di biggest file wey dem host, an why e big like dat? Wetin be di smallest file? E get interesting or unexpected patterns when e come to certain categories, languages, an so on? E get duplicate or very similar titles? E get patterns to when dem add data, like one day wey dem add many files at once? Yu fit learn plenty by looking at di dataset in different ways. For our case, we deduplicated Z-Library books against di md5 hashes in Library Genesis, thereby saving plenty download time an disk space. Dis na pretty unique situation sha. For most cases, e no get comprehensive databases of which files dem don already properly preserve by fellow pirates. Dis in itself na big opportunity for person wey dey out there. E go good to get regularly updated overview of things like music an films wey dem don already widely seed on torrent websites, an therefore dey lower priority to include in pirate mirrors. 6. Distribution Yu don get di data, thereby giving yu possession of di world's first pirate mirror of yu target (most likely). For many ways di hardest part don pass, but di riskiest part still dey ahead of yu. After all, so far yu don dey stealth; flying under di radar. All yu need do na to use good VPN throughout, no fill in yu personal details for any forms (duh), an perhaps use special browser session (or even different computer). Now yu need distribute di data. For our case we first wan contribute di books back to Library Genesis, but then quickly discover di difficulties in dat (fiction vs non-fiction sorting). So we decide on distribution using Library Genesis-style torrents. If yu get di opportunity to contribute to existing project, then dat fit save yu plenty time. However, e no get many well-organized pirate mirrors out there currently. So make we say you decide to distribute torrents yourself. Try keep those files small, so dem go easy to mirror on other websites. You go then have to seed the torrents yourself, while still staying anonymous. You fit use a VPN (with or without port forwarding), or pay with tumbled Bitcoins for a Seedbox. If you no sabi wetin some of those terms mean, you go get plenty reading to do, since e dey important say you understand the risk tradeoffs here. You fit host di torrent files dem for di existing torrent websites. For our own case, we choose to actually host a website, because we also wan spread our philosophy in a clear way. You fit do am yourself in a similar way (we dey use Njalla for our domains and hosting, we dey pay with tumbled Bitcoins), but you fit also contact us make we host your torrents. We dey look to build a comprehensive index of pirate mirrors over time, if dis idea catch on. As for VPN selection, plenty don dey write about dis already, so we go just repeat di general advice of choosing by reputation. Actual court-tested no-log policies with long track records of protecting privacy na di lowest risk option, for our opinion. Note say even when yu do everything right, yu no fit ever get to zero risk. For example, when seeding yu torrents, highly motivated nation-state actor fit probably look at incoming an outgoing data flows for VPN servers, an deduce who yu be. Or yu fit just simply mess up somehow. We probably don already mess up, an go still mess up again. Luckily, nation states no dey care <em>that</em> much about piracy. One decision to make for each project, na whether to publish am using di same identity as before, or not. If yu keep using di same name, then mistakes in operational security from earlier projects fit come back to bite yu. But to publish under different names means say yu no go build longer lasting reputation. We choose to get strong operational security from di start so we fit keep using di same identity, but we no go hesitate to publish under different name if we mess up or if di circumstances call for am. To get di word out fit dey tricky. As we talk, dis still be niche community. We originally post on Reddit, but really get traction on Hacker News. For now our recommendation na to post am in few places an see wetin go happen. An again, contact us. We go love to spread di word of more pirate archivism efforts. 1. Domain selection / philosophy No shortage of knowledge and cultural heritage dey to save, which fit dey overwhelming. That's why e dey often useful to take a moment and think about wetin your contribution fit be. Everybody get different way of thinking about this, but here be some questions wey you fit ask yourself: For our case, we care particularly about the long term preservation of science. We sabi about Library Genesis, and how e dey fully mirrored many times over using torrents. We love that idea. Then one day, one of us try to find some scientific textbooks on Library Genesis, but no fit find them, bringing into doubt how complete e really be. We then search those textbooks online, and find them in other places, which plant the seed for our project. Even before we sabi about the Z-Library, we get the idea of not trying to collect all those books manually, but to focus on mirroring existing collections, and contributing them back to Library Genesis. Wetin skills you get wey you fit use to your benefit? For example, if you be online security expert, you fit find ways of defeating IP blocks for secure targets. If you dey great at organizing communities, then maybe you fit gather some people together around a goal. E dey useful to sabi some programming though, if na only for keeping good operational security throughout dis process. Wetin go bi di high-leverage erịa to fokus on? If yu wan spend X awas on pirate archiving, den how yu fit get di bigest "bang for your buck"? Wetin be di unique ways wey you dey tink about dis mata? You fit get some interesting ideas or approaches wey oda pipu fit never tink of. How much time you get for this? Our advice go be to start small and do bigger projects as you get the hang of it, but e fit consume all your time. Why you dey interested in this? Wetin you dey passionate about? If we fit get bunch of people wey all dey archive the kinds of things wey dem specifically care about, that go cover a lot! You go know a lot more than the average person about your passion, like wetin be important data to save, wetin be the best collections and online communities, and so on. 3. Metadata scraping Date added/modified: so you fit come back later and download files wey you no download before (though you fit often also use the ID or hash for this). Hash (md5, sha1): to confirm say you download the file properly. ID: fit be some internal ID, but IDs like ISBN or DOI dey useful too. Filename / location Description, category, tags, authors, language, etc. Size: to calculate how much disk space you need. Make we get a bit more technical here. For actually scraping the metadata from websites, we keep things simple. We dey use Python scripts, sometimes curl, and a MySQL database to store the results. We never use any fancy scraping software wey fit map complex websites, since so far we only need to scrape one or two kinds of pages by just enumerating through ids and parsing the HTML. If dia no be easily enumerated pages, then you fit need a proper crawler wey go try find all pages. Before you start to scrape a whole website, try do am manually for a bit. Go through a few dozen pages yourself, to get a sense of how e dey work. Sometimes you go already run into IP blocks or other interesting behavior this way. The same goes for data scraping: before you go too deep into this target, make sure say you fit actually download its data effectively. To get around restrictions, there are a few things you fit try. Are there any other IP addresses or servers wey host the same data but no get the same restrictions? Are there any API endpoints wey no get restrictions, while others get? At what rate of downloading your IP dey get blocked, and for how long? Or dem no block you but throttle you down? What if you create a user account, how things go change then? You fit use HTTP/2 to keep connections open, and e go increase the rate at which you fit request pages? Are there pages wey list multiple files at once, and the information wey dem list there dey sufficient? Things wey you probably want to save include: We dey typically do this in two stages. First we download the raw HTML files, usually directly into MySQL (to avoid plenty small files, which we go talk more about below). Then, in a separate step, we go through those HTML files and parse them into actual MySQL tables. This way you no go need to re-download everything from scratch if you discover a mistake in your parsing code, since you fit just reprocess the HTML files with the new code. E dey also often easier to parallelize the processing step, thus saving some time (and you fit write the processing code while the scraping dey run, instead of having to write both steps at once). Finally, note say for some targets metadata scraping na all wey dey. There are some huge metadata collections out there wey dem no properly preserve. Title Domain selection / philosophy: Where you roughly wan focus on, and why? Wetin be your unique passions, skills, and circumstances wey you fit use to your benefit? Target selection: Which specific collection you go mirror? Metadata scraping: Cataloging information about the files, without actually downloading the (often much larger) files themselves. Data selection: Based on the metadata, narrowing down which data dey most relevant to archive right now. Fit be everything, but often e get reasonable way to save space and bandwidth. Data scraping: Actually getting the data. Distribution: Packaging am up in torrents, announcing am somewhere, getting people to spread am. 5. Data scraping Now yu dey ready to actually download di data in bulk. As we don mention before, for dis point yu suppose don manually download some files, to better understand di behavior an restrictions of di target. However, surprises still dey wait for yu once yu actually start to download plenty files at once. Our advice here na mainly to keep am simple. Start by just downloading some files. Yu fit use Python, an then expand to multiple threads. But sometimes even simpler na to generate Bash files directly from di database, an then run multiple of dem in multiple terminal windows to scale up. One quick technical trick wey worth mentioning here na using OUTFILE in MySQL, wey yu fit write anywhere if yu disable "secure_file_priv" in mysqld.cnf (an make sure to also disable/override AppArmor if yu dey on Linux). We dey store di data on simple hard disks. Start out with wetin yu get, an expand slowly. E fit dey overwhelming to think about storing hundreds of TBs of data. If na di situation wey yu dey face, just put out good subset first, an for yu announcement ask for help in storing di rest. If yu wan get more hard drives yourself, then r/DataHoarder get some good resources on getting good deals. Try not to worry too much about fancy filesystems. E easy to fall into di rabbit hole of setting up things like ZFS. One technical detail to be aware of though, na say many filesystems no dey deal well with plenty files. We don find say simple workaround na to create multiple directories, e.g. for different ID ranges or hash prefixes. After downloading di data, make sure to check di integrity of di files using hashes in di metadata, if e dey available. 2. Target selection Ezi: e no dey use plenty layers of protection wey go stop you from scraping dia metadata and data. Special insight: you get some special information about dis target, like say you somehow get special access to dis collection, or you sabi how to defeat dia defenses. E no dey required (our upcoming project no dey do anything special), but e go help well well! Large So, we get our area wey we dey look at, now which specific collection we go mirror? E get some things wey make for a good target: When we find our science textbooks for websites wey no be Library Genesis, we try to figure out how dem take land for internet. We come find Z-Library, and realize say even though most books no dey first show for dia, dem dey eventually end up dia. We learn about dia relationship with Library Genesis, and the (financial) incentive structure and better user interface, both of dem make am a more complete collection. We come do some preliminary metadata and data scraping, and realize say we fit bypass dia IP download limits, using one of our members' special access to plenty proxy servers. As you dey explore different targets, e dey important to hide your tracks by using VPNs and throwaway email addresses, we go talk more about am later. Unique: no dey already well-covered by other projects. When we dey do project, e get some phases: These no dey completely independent phases, and often insights from a later phase fit send you back to an earlier phase. For example, during metadata scraping you fit realize say the target wey you select get defensive mechanisms beyond your skill level (like IP blocks), so you go back and find different target. - Anna and di team (<a %(reddit)s>Reddit</a>) Entire books fit dey written about di <em>why</em> of digital preservation in general, and pirate archivism in particular, but make we give quick primer for those wey no too familiar. Di world dey produce more knowledge and culture than ever before, but also more of am dey lost than ever before. Humanity largely dey trust corporations like academic publishers, streaming services, and social media companies with dis heritage, and dem often no dey prove to be great stewards. Check out di documentary Digital Amnesia, or really any talk by Jason Scott. Some institutions dey wey dey do good job to archive as much as dem fit, but dem dey bound by di law. As pirates, we dey in unique position to archive collections wey dem no fit touch, because of copyright enforcement or other restrictions. We fit also mirror collections many times over, across di world, thereby increasing di chances of proper preservation. For now, we no go enter discussions about di pros and cons of intellectual property, di morality of breaking di law, musings on censorship, or di issue of access to knowledge and culture. With all dat out of di way, make we dive into di <em>how</em>. We go share how our team become pirate archivists, and di lessons wey we learn along di way. Plenty challenges dey when you embark on dis journey, and hopefully we fit help you through some of dem. How to become a pirate archivist Di first challenge fit surprise you. E no be technical problem, or legal problem. E be psychological problem. Before we dive in, two updates dey on di Pirate Library Mirror (EDIT: dem don move am go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>): We get some extremely generous donations. Di first one na $10k from one anonymous person wey also dey support "bookwarrior", di original founder of Library Genesis. Special thanks to bookwarrior for facilitating dis donation. Di second one na another $10k from another anonymous donor, wey contact us after our last release, and e inspire am to help. We also get plenty smaller donations. Thanks so much for all your generous support. We get some exciting new projects for di pipeline wey dis go support, so make you dey watch. We get some technical difficulties with di size of our second release, but our torrents dey up and seeding now. We also get one generous offer from one anonymous person to seed our kolekshọn on dia very-high-speed servers, so we dey do special upload to dia machines, after which everybody wey dey download di kolekshọn go see big improvement in speed. Blog posts Hi, na me be Anna. I create <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di world biggest shadow library. Dis na my personal blog, where me and my teammates dey write about piracy, digital preservation, and more. Connect with me on <a %(reddit)s>Reddit</a>. Make you note say dis website na just a blog. We only dey host our own words here. No torrents or other copyrighted files dey hosted or linked here. <strong>Library</strong> - Like most libraries, we dey focus mainly on written materials like books. We fit expand go other types of media for future. <strong>Mirror</strong> - We dey strictly as mirror of di libraries wey don dey. We dey focus on preservation, no be to make books easy to search an download (access) or to build big community of pipul wey dey contribute new books (sourcing). <strong>Pirate</strong> - We dey deliberately break di copyright law for most kontris. Dis dey allow us do wetin legal entities no fit do: make sure say books dey mirror far an wide. <em>We no dey link to di files from dis blog. Abeg find am yourself.</em> - Anna and di team (<a %(reddit)s>Reddit</a>) Dis prọjekt (EDIT: e don mov go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>) wan help for di preserv an librate human knowledge. We dey make our small an humble kontribushon, as we dey follow di footstep of di great pipul wey don dey before us. Di focus of dis prọjekt dey show for di name: Di first library wey we don mirror na Z-Library. Dis na popular (an illegal) library. Dem don take di Library Genesis collection an make am easy to search. On top of dat, dem don dey very effective for collecting new book contributions, by giving contributing users different perks. Dem no dey contribute dis new books back to Library Genesis. An unlike Library Genesis, dem no dey make their collection easy to mirror, wey dey stop wide preservation. Dis dey important to their business model, since dem dey charge money to access their collection in bulk (more than 10 books per day). We no dey make moral judgements about charging money for bulk access to an illegal book collection. E dey beyond doubt say the Z-Library don successful in expanding access to knowledge, and sourcing more books. We dey simply here to do our part: ensuring the long-term preservation of this private collection. We wan invite you to help preserve an liberate human knowledge by downloading an seeding our torrents. See di project page for more information about how di data dey organized. We go also like make you contribute your ideas for which collections to mirror next, an how to go about am. Together we fit achieve plenty. Dis na just small contribution among many others. Thank you, for all wey you dey do. Introducing di Pirate Library Mirror: Preserving 7TB of books (wey no dey for Libgen) 10% of humanity’s written heritage preserved forever <strong>Google.</strong> After all, dem do dis research for Google Books. However, dia metadata no dey accessible in bulk and e dey hard to scrape. <strong>Various individual library systems and archives.</strong> E get libraries and archives wey dem never index and aggregate by any of di ones above, often because dem dey underfunded, or for other reasons dem no wan share dia data wit Open Library, OCLC, Google, and so on. Plenty of dem get digital records accessible through di internet, and dem often no dey very well protected, so if you wan help out and have some fun learning about weird library systems, dis na great starting points. <strong>ISBNdb.</strong> Dis na di topic of dis blog post. ISBNdb dey scrape various websites for book metadata, particularly pricing data, wey dem dey sell to booksellers, so dem fit price dia books in accordance wit di rest of di market. Since ISBNs dey fairly universal nowadays, dem effectively build “web page for every book”. <strong>Open Library.</strong> As we don mention before, na dia entire mission be dis. Dem don source massive amounts of library data from cooperating libraries and national archives, and dem still dey do so. Dem also get volunteer librarians and technical team wey dey try deduplicate records, and tag dem wit all sorts of metadata. Best of all, dia dataset dey completely open. You fit simply <a %(openlibrary)s>download am</a>. <strong>WorldCat.</strong> Dis na website wey non-profit OCLC dey run, wey dey sell library management systems. Dem dey aggregate book metadata from plenty libraries, and make am available through di WorldCat website. However, dem dey also make money selling dis data, so e no dey available for bulk download. Dem get some more limited bulk datasets available for download, in cooperation wit specific libraries. 1. Fɔ sɔm kind defínishọn wey make sense fɔ "fɔreva". ;) 2. Of kɔs, di tin wey pipul don write na im be di heritij wey di wold get, e pass buks, speshali fɔ dis kain taim. Fɔ di sake of dis post an di new tins wey we don release, we go focus on buks, but wetin we dey intarested in dey pass dat one. 3. Plenty tins dey wey we fit tok about Aaron Swartz, but we just wan mention am small, since e play important role fɔ dis stori. As time dey go, more pipul fit hear im name fɔ di first time, an dem fit decide to find out more by demsef. <strong>Physical copies.</strong> Obviously dis no dey very helpful, since dem just dey duplicate di same material. E go cool if we fit preserve all annotations wey pipul dey make for books, like Fermat’s famous “scribbles in di margins”. But alas, dat go remain archivist’s dream. <strong>“Editions”.</strong> Here you go count every unique version of a book. If anything about am dey different, like different cover or different preface, e go count as different edition. <strong>Files.</strong> Wen pesin dey wok wit shado librari dem laik Library Genesis, Sci-Hub, or Z-Library, dia dey get anoda mata to tink about. E fit get plenti scan of di same edition. An pipul fit make beta version of di file wey dey, by scanning di text wit OCR, or correct pages wey dem scan for angle. We wan make sure say we go count dis files as one edition, wey go need beta metadata, or deduplication using document similarity measures. <strong>“Works”.</strong> For example “Harry Potter and the Chamber of Secrets” as a logical concept, wey dey include all versions of am, like different translations an reprints. Dis na kind of useful definition, but e fit hard to draw di line of wetin count. For example, we probably wan preserve different translations, though reprints with only minor differences fit no dey as important. - Anna and di team (<a %(reddit)s>Reddit</a>) With di Pirate Library Mirror (EDIT: e don mov go <a %(wikipedia_annas_archive)s>Anna’s Archive</a>), our aim na to take all di books for di world, an preserve dem forever.<sup>1</sup> Between our Z-Library torrents, an di original Library Genesis torrents, we get 11,783,153 files. But how many be dat, really? If we properly deduplicated those files, wetin be di percentage of all di books for di world wey we don preserve? We go really like get something like dis: Make we start wit some rough numbers: For both Z-Library/Libgen and Open Library, e get many more books than unique ISBNs. E mean say plenty of those books no get ISBNs, or na di ISBN metadata dey simply miss? We fit probably answer dis question wit combination of automated matching based on other attributes (title, author, publisher, etc), pulling in more data sources, and extracting ISBNs from di actual book scans themselves (for di case of Z-Library/Libgen). How many of those ISBNs dey unique? Dis na best illustrated wit Venn diagram: To be more precise: We dey surprised by how little overlap dey! ISBNdb get huge amount of ISBNs wey no show up in either Z-Library or Open Library, and di same hold (to a smaller but still substantial degree) for di other two. Dis dey raise plenty new questions. How much automated matching go help in tagging di books wey dem no tag wit ISBNs? E go get plenty matches and therefore increased overlap? Also, wetin go happen if we bring in 4th or 5th dataset? How much overlap we go see then? Dis dey give us starting point. We fit now look at all di ISBNs wey no dey for di Z-Library dataset, and wey no match title/author fields either. Dat fit give us handle on preserving all di books for di world: first by scraping di internet for scans, then by going out in real life to scan books. Di latter fit even be crowd-funded, or driven by “bounties” from people wey go like see particular books digitized. All dat na story for different time. If you wan help out wit any of dis — further analysis; scraping more metadata; finding more books; OCR’ing of books; doing dis for other domains (e.g. papers, audiobooks, movies, tv shows, magazines) or even making some of dis data available for things like ML / large language model training — abeg contact me (<a %(reddit)s>Reddit</a>). If you dey specifically interested in di data analysis, we dey work on making our datasets and scripts available in more easy to use format. E go great if you fit just fork notebook and start to dey play wit dis. Finally, if you wan support dis work, abeg consider making donation. Dis na entirely volunteer-run operation, and your contribution dey make huge difference. Every bit dey help. For now we dey take donations in crypto; see di Donate page on Anna’s Archive. For a percentage, we need a denominator: di total number of books wey dem don ever publish.<sup>2</sup> Before di end of Google Books, one engineer for di project, Leonid Taycher, <a %(booksearch_blogspot)s>try to estimate</a> dis number. E come up — tongue-in-cheek — with 129,864,880 (“at least until Sunday”). E estimate dis number by building a unified database of all di books for di world. For dis, e gather different datasets an then merge dem in various ways. As quick aside, another person wey try to catalog all di books for di world na Aaron Swartz, di late digital activist an Reddit co-founder.<sup>3</sup> E <a %(youtube)s>start Open Library</a> with di goal of “one web page for every book wey dem don ever publish”, combining data from plenty different sources. E end up paying di ultimate price for e digital preservation work when dem prosecute am for bulk-downloading academic papers, wey lead to e suicide. Needless to say, dis na one of di reasons our group dey pseudonymous, an why we dey very careful. Open Library still dey heroically run by folks for di Internet Archive, continuing Aaron’s legacy. We go come back to dis later for dis post. For di Google blog post, Taycher describe some of di challenges with estimating dis number. First, wetin be book? There are a few possible definitions: “Editions” dey seem laik di most practical definition of wetin “books” be. E dey convenient, as dis definition dey also use for assign unique ISBN numbers. ISBN, or International Standard Book Number, dey commonly use for international commerce, since e dey integrated wit di international barcode system (”International Article Number”). If you wan sell book for store, e need barcode, so you go get ISBN. Taycher’s blog post mention say even though ISBNs dey useful, dem no dey universal, since dem only start to dey use am well for di mid-seventies, and no be everywhere for di world. Still, ISBN dey probably be di most widely used identifier of book editions, so na our best starting point. If we fit find all di ISBNs for di world, we go get useful list of which books still need to be preserved. So, where we go get di data? E get number of existing efforts wey dey try compile list of all di books for di world: For dis post, we dey happy to announce small release (compared to our previous Z-Library releases). We scrape most of ISBNdb, and make di data available for torrenting on di website of di Pirate Library Mirror (EDIT: moved to <a %(wikipedia_annas_archive)s>Anna’s Archive</a>; we no go link am here directly, just search for am). Dis na about 30.9 million records (20GB as <a %(jsonlines)s>JSON Lines</a>; 4.4GB gzipped). For dia website dem claim say dem actually get 32.6 million records, so we fit somehow miss some, or <em>dem</em> fit dey do something wrong. In any case, for now we no go share exactly how we do am — we go leave that as an exercise for di reader. ;-) Wet we go share na some preliminary analysis, to try to get closer to estimating di number of books for di world. We look at three datasets: dis new ISBNdb dataset, our original release of metadata wey we scrape from di Z-Library shadow library (wey include Library Genesis), and di Open Library data dump. ISBNdb dump, or How Many Books Are Preserved Forever? If we fit properly deduplicate di files from shadow libraries, wetin be di percentage of all di books for di world wey we don preserve? Updates about <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, di biggest truly open library for human history. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Anna’s Archive Containers (AAC)</a>, wey essentially be <a %(jsonlines)s>JSON Lines</a> wey dem compress with <a %(zstd)s>Zstandard</a>, plus some standardized semantics. Dis containers dey wrap various types of records, based on di different scrapes we deploy. One year ago, we <a %(blog)s>set out</a> to answer dis question: <strong>Wetin be di percentage of books wey shadow libraries don permanently preserve?</strong> Make we look some basic information on di data: Once book enter open-data shadow library like <a %(wikipedia_library_genesis)s>Library Genesis</a>, and now <a %(wikipedia_annas_archive)s>Anna’s Archive</a>, e go dey mirrored all over di world (through torrents), thereby practically preserving am forever. To answer di question of which percentage of books don dey preserved, we need to know di denominator: how many books dey in total? And ideally we no just get number, but actual metadata. Then we fit not only match dem against shadow libraries, but also <strong>create a TODO list of remaining books to preserve!</strong> We fit even start to dream of a crowdsourced effort to go down dis TODO list. We scrape <a %(wikipedia_isbndb_com)s>ISBNdb</a>, and download di <a %(openlibrary)s>Open Library dataset</a>, but di results no dey satisfactory. Di main problem na say no be plenty overlap of ISBNs. See dis Venn diagram from <a %(blog)s>our blog post</a>: Wi bi vẹri sopraiz se di smọl ovạlap wey de de bitwin ISBNdb an Open Library, wey de tu of dem de kari data from difrent sors, laik web skreps an laibrari rekọds. If dem de do beta wok for faind most ISBNs wey de, dia sẹkul go shọli get big ovạlap, or wan go de insaid di oda. I mek us wanda, how many buks de <em>komplitli autsaid dis sẹkul</em>? Wi nid bigga databẹs. Na dat taim wey wi set our ai for di bigest buk databẹs for di wold: <a %(wikipedia_worldcat)s>WorldCat</a>. Dis na proprayẹtri databẹs wey di non-profit <a %(wikipedia_oclc)s>OCLC</a> get, wey de kolekt metadata rekọds from laibraris all ova di wold, in ekschej for givin dose laibraris akses to di ful dataset, an mek dem de shọ for end-yuzas’ search rizọlts. Even do OCLC na non-profit, dia biznis model nid dem to protekt dia databẹs. Well, wi sori to tok, frends for OCLC, wi de giv am all awẹ. :-) Over di past year, we don meticulously scrape all WorldCat records. At first, we hit one lucky break. WorldCat just dey roll out their complete website redesign (for Aug 2022). Dis one include substantial overhaul of their backend systems, introduce many security flaws. We immediately seize di opportunity, and we fit scrape hundreds of millions (!) of records in mere days. After that, security flaws dey slowly fix one by one, until di final one wey we find dey patched about a month ago. By that time we don get pretty much all records, and we dey only go for slightly higher quality records. So we feel say e dey time to release! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Anna’s Archive don scrape all of WorldCat (di world biggest library metadata collection) to make a TODO list of books wey need to be preserved.</em> WorldCat Warning: dis blog post don dey deprecated. We don decide say IPFS never ready for prime time. We go still link to files on IPFS from Anna’s Archive when possible, but we no go host am ourselves again, nor we dey recommend others to mirror using IPFS. Abeg see our Torrents page if you wan help preserve our collection. Help seed Z-Library on IPFS Partner Server download SciDB External borrow External borrow (print disabled) External download Check Metadata Wetin dey inside torrents Go Bak  (+%(num)s bonus) neva pay don pay cansul don finish de wait make Anna confirm no correct Text below continues in English. Go Reset Go Frɔnt Las If your email address no dey work for di Libgen forums, we recommend make you use <a %(a_mail)s>Proton Mail</a> (free). You fit also <a %(a_manual)s>manually request</a> for your account to be activated. (e fit need <a %(a_browser)s>browser verification</a> — unlimited downloads!) Fast Partner Server #%(number)s (recommended) (slightly faster but with waitlist) (no need browser verification) (no browser verification or waitlists) (no waitlist, but can be very slow) Slow Partner Server #%(number)s Audiobook Comic book Book (fiction) Book (non-fiction) Book (unknown) Journal article Magazine Musical score Other Standards document No fit convert all pages to PDF Mark as broken for Libgen.li No dey visible for Libgen.li No dey visible for Libgen.rs Fiction Not dey visible for Libgen.rs Non-Fiction Exiftool no work for dis file Mark am “bad file” for Z-Library Miss from Z-Library Mark am “spam” for Z-Library File no fit open (e.g. corrupted file, DRM) Copyright claim Download wahala (e.g. no fit connect, error message, very slow) Wrong metadata (e.g. title, description, cover image) Other Poor quality (e.g. formatting issues, poor scan quality, missing pages) Spam / file suppose comot (e.g. advertising, abusive content) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brilliant Bookworm Lucky Librarian Dazzling Datahoarder Amazing Archivist Bonus downloads Cerlalc Czech metadata DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Excluding “scimag” Libgen.rs Non-Fiction and Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russian State Library Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads to AA Z-Library Z-Library Chinese Title, author, DOI, ISBN, MD5, … Search Author Description and metadata comments Edition Original filename Publisher (saach speshial field) Title Year wey dem publish am Technical details Dis coin get higher than usual minimum. Abeg select different duration or different coin. Request no fit complete. Abeg try again in few minutes, and if e still dey happen contact us for %(email)s with screenshot. Unknown error don happen. Abeg contact us for %(email)s with screenshot. Error for payment processing. Abeg wait small and try again. If di issue still dey for more than 24 hours, abeg contact us for %(email)s with screenshot. We dey run fundraiser for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">backing up</a> di biggest comics shadow library for di world. Thanks for your support! <a href="/donate">Donate.</a> If you no fit donate, consider to support us by telling your friends, and follow us for <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>. No email us to <a %(a_request)s>request books</a><br>or small (<10k) <a %(a_upload)s>uploads</a>. Anna’s Archive DMCA / copyright claims Stay in touch Reddit Alternatives SLUM (%(unaffiliated)s) unaffiliated Anna’s Archive need your help! If you donate now, you go get <strong>double</strong> di number of fast downloads. Plenty people dey try bring us down, but we dey fight back. If you donate this month, you go get <strong>double</strong> di number of fast downloads. E dey valid until di end of dis month. To save human knowledge: na better holiday gift! Membership go dey extend as e suppose be. Partner servers no dey available because dem don close hosting. Dem go dey up again soon. To make Anna’s Archive strong well well, we dey find volunteers to run mirrors. We get new donation method available: %(method_name)s. Abeg consider %(donate_link_open_tag)sdonating</a> — e no cheap to run dis website, and your donation really dey make difference. Thank you so much. Tell am to your friend, and both you and your friend go get %(percentage)s%% bonus fast downloads! Surprise person wey you love, give dem account with membership. Di perfect Valentine’s gift! Learn more… Account Activity Advanced Anna’s Blog ↗ Anna’s Software ↗ beta Codes Explorer Datasets Donate Downloadẹd fáils FAQ Home Improve metadata LLM data Log in / Register Mai donéshọns Pọblik profaịl Search Security Torrents Translate ↗ Volunteering & Bounties Recent downloads: 📚&nbsp;Di world’s largest open-source open-data library. ⭐️&nbsp;Mirrors Sci-Hub, Library Genesis, Z-Library, and more. 📈&nbsp;%(book_any)s books, %(journal_article)s papers, %(book_comic)s comics, %(magazine)s magazines — dem go dey preserve forever.  and  and plenti more DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Di largest truly open library for human history. 📈&nbsp;%(book_count)s&nbsp;books, %(paper_count)s&nbsp;papers — dem go dey preserve forever. ⭐️&nbsp;We dey mirror %(libraries)s. We dey scrape and open-source %(scraped)s. All our code and data dey completely open source. OpenLib Sci-Hub ,  📚 Di world’s largest open-source open-data library.<br>⭐️ Mirrors Scihub, Libgen, Zlib, and more. Z-Lib Anna’s Archive Request no correct. Visit %(websites)s. Di world biggest open-source open-data library. E dey mirror Sci-Hub, Library Genesis, Z-Library, and more. Search Anna’s Archive Anna’s Archive Abeg refresh make you try again. <a %(a_contact)s>Contact us</a> if di wahala still dey for many hours. 🔥 Wahala dey load dis page <li>1. Follow us on <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, or <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spread the word about Anna’s Archive on Twitter, Reddit, Tiktok, Instagram, for your local cafe or library, or anywhere you go! We no believe in gatekeeping — if dem take us down we go just pop up somewhere else, since all our code and data dey fully open source.</li><li>3. If you fit, consider <a href="/donate">donating</a>.</li><li>4. Help <a href="https://translate.annas-software.org/">translate</a> our website into different languages.</li><li>5. If you be software engineer, consider contributing to our <a href="https://annas-software.org/">open source</a>, or seeding our <a href="/datasets">torrents</a>.</li> 10. Create or help maintain di Wikipedia page for Anna’s Archive for your language. 11. We dey look to place small, tasteful advertisements. If you wan advertise for Anna’s Archive, abeg let us know. 6. If you be security researcher, we fit use your skills both for offense and defense. Check out our <a %(a_security)s>Security</a> page. 7. We dey look for experts in payments for anonymous merchants. You fit help us add more convenient ways to donate? PayPal, WeChat, gift cards. If you know anybody, abeg contact us. 8. Wi de always dey look for more server capacity. 9. You fit help by reporting file issues, leaving comments, and creating lists right for dis website. You fit also help by <a %(a_upload)s>uploading more books</a>, or fixing up file issues or formatting of existing books. For more extensive information on how to volunteer, see our <a %(a_volunteering)s>Volunteering & Bounties</a> page. We strongly believe in the free flow of information, and preservation of knowledge and culture. With this search engine, we dey build on the shoulders of giants. We deeply respect the hard work of the people wey don create the different shadow libraries, and we hope say this search engine go broaden their reach. To stay updated on our progress, follow Anna on <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> or <a href="https://t.me/annasarchiveorg">Telegram</a>. For questions and feedback abeg contact Anna at %(email)s. Akàụnt ID: %(account_id)s Logout ❌ Sọmtin wẹnt rọng. Plís rílód di peij ànd trai agẹ́n. ✅ Yú dè nàu lógd àut. Rílód di peij to lóg in agẹ́n. Fast downloads yúzd (làst 24 àwàz): <strong>%(used)s / %(total)s</strong> Mẹmbaship: <strong>%(tier_name)s</strong> ontíl %(until_date)s <a %(a_extend)s>(ẹkstẹnd)</a> Yú fít kọmbain múltípul mẹmbaships (fast downloads pèr 24 àwàz wíl bì àdẹd tógẹda). Mẹmbaship: <strong>Nọn</strong> <a %(a_become)s>(bíkọm a mẹmbà)</a> Kọntàkt Anna fọ %(email)s if yú dè intarẹstẹd in ọpgreidín yọ mẹmbaship to a hàia tia. Pọblik profaịl: %(profile_link)s Sikret ki (nọ shéàm!): %(secret_key)s shọw Jọin ós hia! Ọpgreid to a <a %(a_tier)s>hàia tia</a> to jọin ówa grúp. Ẹksklúsív Telegram grúp: %(link)s Account wich downloads? Log in No lose your key! Invalid secret key. Check your key well and try again, or you fit register new account below. Secret key Enter your secret key to log in: Old email-based account? Enter your <a %(a_open)s>email here</a>. Regista nyu akɔunt Yu neva get akant? Registration don successful! Your secret key na: <span %(span_key)s>%(key)s</span> Keep dis key well well. If you lose am, you go lose access to your account. <li %(li_item)s><strong>Bookmark.</strong> You fit bookmark dis page to fit get your key back.</li><li %(li_item)s><strong>Download.</strong> Click <a %(a_download)s>dis link</a> to download your key.</li><li %(li_item)s><strong>Password manager.</strong> Use password manager to save the key when you enter am below.</li> Log in / Register Browser verification Warning: code get incorrect Unicode characters inside, and fit behave anyhow for different situations. Di raw binary fit decode from di base64 representation for di URL. Description Label Prefix URL for a specific code Website Codes wey start with “%(prefix_label)s” Abeg no scrape dis pages. Instead we recommend <a %(a_import)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases, and run our <a %(a_software)s>open source code</a>. Di raw data fit manually explore through JSON files like <a %(a_json_file)s>dis one</a>. Less than %(count)s records Generic URL Codes Explorer Indẹks of Check di codes wey dem tag records with, by prefix. Di “records” column dey show di number of records wey dem tag with codes wey get di given prefix, as e dey for di search engine (including metadata-only records). Di “codes” column dey show how many actual codes get di given prefix. Known code prefix “%(key)s” Mọ… Prefix page.codes.record_starting_with %(count)s records wey match “%(prefix_label)s” codes records “%%s” go replace with di code’s value Sabi Anna’s Archive Codes URL for specific code: “%(url)s” Dis page fit take small time to generate, na why e need Cloudflare captcha. <a %(a_donate)s>Members</a> fit skip di captcha. Bad behavior don report: Beta version Yu wan report dis user for bad or wrong behavior? File wahala: %(file_issue)s hidden comment Reply Report bad behavior Yu don report dis user for bad behavior. Copyright claims to dis email go ignore; use di form instead. Show email We really welcome your feedback and questions! But, because of di plenty spam and nonsense emails we dey get, abeg check di boxes to confirm say you understand dis conditions to contact us. Any other ways to contact us about copyright claims go automatically delete. For DMCA / copyright claims, use <a %(a_copyright)s>dis form</a>. Contact email URLs for Anna’s Archive (required). One per line. Abeg only put URLs wey describe di exact same edition of di book. If you wan make claim for multiple books or multiple editions, abeg submit dis form multiple times. Claims wey bundle multiple books or editions together go reject. Address (required) Clear description of di source material (required) E-mail (required) URLs to source material, one per line (required). Abeg put as many as possible, to help us verify your claim (e.g. Amazon, WorldCat, Google Books, DOI). ISBNs of source material (if applicable). One per line. Abeg only put di ones wey match di edition wey you dey report copyright claim for. Your name (required) ❌ Something go wrong. Abeg reload di page and try again. ✅ Thank you for submitting your copyright claim. We go review am as soon as possible. Abeg reload di page to file another one. <a %(a_openlib)s>Open Library</a> URLs of source material, one per line. Abeg take small time search Open Library for your source material. Dis go help us verify your claim. Phone number (required) Statement and signature (required) Submit claim If you get DMCA or oda copyright claim, abeg fill dis form as correct as possible. If you get any wahala, abeg contact us for our special DMCA address: %(email)s. Note say claims wey dem email go dis address no go process, e dey only for questions. Abeg use di form below to submit your claims. DMCA / Copyright claim form Example record for Anna’s Archive Torrents by Anna’s Archive Anna’s Archive Containers format Scripts for importing metadata If you are interested in mirroring this dataset for <a %(a_archival)s>archival</a> or <a %(a_llm)s>LLM training</a> purposes, please contact us. Last updated: %(date)s Main %(source)s website Metadata documentation (most fields) Files wey Anna’s Archive mirror: %(count)s (%(percent)s%%) Resources Total files: %(count)s Total filesize: %(size)s Our blog post about this data <a %(duxiu_link)s>Duxiu</a> na big database of scanned books, wey <a %(superstar_link)s>SuperStar Digital Library Group</a> create. Most of dem na academic books, dem scan am so dat universities and libraries fit get dem for digital form. For our English-speaking audience, <a %(princeton_link)s>Princeton</a> and the <a %(uw_link)s>University of Washington</a> get better overview. One correct article dey wey give more background: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. The books from Duxiu don dey pirated for Chinese internet for long. Usually, dem dey sell am for less than one dollar by resellers. Dem dey usually distribute am using the Chinese version of Google Drive, wey dem don hack many times to allow for more storage space. Some technical details dey <a %(link1)s>here</a> and <a %(link2)s>here</a>. Even though dem don semi-publicly distribute the books, e still dey hard to get dem in bulk. We put this one high for our TODO-list, and we allocate many months of full-time work for am. But, for late 2023, one incredible, amazing, and talented volunteer reach out to us, tell us say dem don do all this work already — at great expense. Dem share the full collection with us, without expecting anything in return, except say we go guarantee long-term preservation. Truly remarkable. More information from our volunteers (raw notes): Adapted from our <a %(a_href)s>blog post</a>. DuXiu 读秀 page.datasets.file %(count)s files Dis dataset dey closely related to di <a %(a_datasets_openlib)s>Open Library dataset</a>. E contain scrape of all metadata and large portion of files from di IA’s Controlled Digital Lending Library. Updates dey release in di <a %(a_aac)s>Anna’s Archive Containers format</a>. Dis records dey refer directly from di Open Library dataset, but e still contain records wey no dey Open Library. We still get number of data files wey community members don scrape over di years. Di collection get two parts. You need both parts to get all data (except superseded torrents, wey dem cross out for di torrents page). Digital Lending Library our first release, before we standardized on the <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. E get metadata (as json and xml), pdfs (from acsm and lcpdf digital lending systems), and cover thumbnails. incremental new releases, wey dey use AAC. E only get metadata with timestamps after 2023-01-01, since the rest don already dey covered by “ia”. Also all pdf files, this time from the acsm and “bookreader” (IA’s web reader) lending systems. Even though the name no dey exactly right, we still dey put bookreader files into the ia2_acsmpdf_files collection, since dem no dey the same. IA Controlled Digital Lending 98%%+ of files fit search. Our mission na to archive all di books for di world (as well as papers, magazines, etc), an make dem widely accessible. We believe say all books suppose dey mirrored far an wide, to ensure redundancy an resiliency. Na why we dey pool together files from different sources. Some sources dey completely open an fit mirror in bulk (like Sci-Hub). Others dey closed an protective, so we dey try scrape dem to “liberate” their books. Others dey somewhere in between. All our data fit <a %(a_torrents)s>torrent</a>, an all our metadata fit <a %(a_anna_software)s>generate</a> or <a %(a_elasticsearch)s>download</a> as ElasticSearch an MariaDB databases. Di raw data fit manually explore through JSON files like <a %(a_dbrecord)s>dis</a>. Metadata ISBN website Last updated: %(isbn_country_date)s (%(link)s) Resources Di International ISBN Agency dey release di ranges wey e don allocate to national ISBN agencies. From dis one, we fit sabi which kontrị, region, or language group dis ISBN belong. We dey use dis data indirectly now, through di <a %(a_isbnlib)s>isbnlib</a> Python library. ISBN kontrị infọmation Dis na dump of plenty calls to isbndb.com during September 2022. We try cover all ISBN ranges. Dem be about 30.9 million records. For their website, dem claim say dem actually get 32.6 million records, so we fit don miss some, or <em>dem</em> fit dey do something wrong. Di JSON responses dey almost raw from their server. One data quality issue wey we notice be say for ISBN-13 numbers wey start with different prefix than “978-”, dem still include “isbn” field wey simply be di ISBN-13 number with di first 3 numbers cut off (and di check digit recalculated). Dis one dey obviously wrong, but na so dem dey do am, so we no alter am. Another potential issue wey you fit face be say di “isbn13” field get duplicates, so you no fit use am as primary key for database. “isbn13”+“isbn” fields combined dey unique. Release 1 (2022-10-31) Fiction torrents dey behind (though IDs ~4-6M never torrented since dem dey overlap with our Zlib torrents). Our blog post about di comic books release Comics torrents for Anna’s Archive For di backstory of di different Library Genesis forks, see di page for di <a %(a_libgen_rs)s>Libgen.rs</a>. Di Libgen.li get most of di same content and metadata as di Libgen.rs, but e get some collections on top of dis one, like comics, magazines, and standard documents. E don also integrate <a %(a_scihub)s>Sci-Hub</a> into its metadata and search engine, which na wetin we dey use for our database. Di metadata for dis library dey freely available <a %(a_libgen_li)s>at libgen.li</a>. But, dis server dey slow and e no support resuming broken connections. Di same files dey also available on <a %(a_ftp)s>an FTP server</a>, wey dey work better. Non-fikshon sef dey show sey e don change, but witout new torrents. E be like sey dis don happen since early 2022, but we never confirm am. Accordin' to di Libgen.li admin, di “fiction_rus” (Russian fiction) kolekshon suppose dey covered by torrents wey dem dey release regularly from <a %(a_booktracker)s>booktracker.org</a>, especially di <a %(a_flibusta)s>flibusta</a> an' <a %(a_librusec)s>lib.rus.ec</a> torrents (wey we dey mirror <a %(a_torrents)s>here</a>, even though we never fit establish which torrents dey correspond to which files). Di fiction kolekshon get dia own torrents (wey don diverge from <a %(a_href)s>Libgen.rs</a>) wey start for %(start)s. Certain ranges wey no get torrents (like fiction ranges f_3463000 to f_4260000) fit be Z-Library (or oda duplicate) files, but we fit wan' do some deduplication an' make torrents for lgli-unique files inside dis ranges. Statistics for all di kolekshons fit dey found <a %(a_href)s>on libgen's website</a>. Torrents dey available for most of di extra content, especially torrents for comics, magazines, an' standard documents wey dem release with Anna’s Archive. Make you note say di torrent files wey dey refer to “libgen.is” na mirrors of <a %(a_libgen)s>Libgen.rs</a> (“.is” na different domain wey Libgen.rs dey use). One helpful resource to use di metadata na <a %(a_href)s>this page</a>. %(icon)s Dia “fiction_rus” kolekshon (Russian fiction) no get dia own torrents, but oda torrents dey cover am, an' we dey keep <a %(fiction_rus)s>mirror</a>. Russian fiction torrents on Anna’s Archive Fiction torrents for Anna’s Archive Discussion forum Metadata Metadata via FTP Magazine torrents for Anna’s Archive Metadata field information Mirror of other torrents (and unique fiction and comics torrents) Standard document torrents on Anna’s Archive Libgen.li Torrents by Anna’s Archive (book covers) Library Genesis dey known for how dem dey generously make dia data available in bulk through torrents. Our Libgen collection get auxiliary data wey dem no dey release directly, in partnership with dem. Much thanks to everybody wey dey involved with Library Genesis for working with us! Our blog about di book covers release Dis page na about di “.rs” version. E dey known for consistently publishing both its metadata and di full contents of its book catalog. Its book collection dey split between fiction and non-fiction portion. One helpful resource to use di metadata na <a %(a_metadata)s>this page</a> (blocks IP ranges, VPN fit dey required). As of 2024-03, new torrents are being posted in <a %(a_href)s>dis forum thread</a> (e dey block IP ranges, VPN fit dey required). Fiction torrents for Anna’s Archive Libgen.rs Fiction torrents Libgen.rs Discussion forum Libgen.rs Metadata Libgen.rs metadata field information Libgen.rs Non-fiction torrents Non-Fiction torrents for Anna’s Archive %(example)s for fiction book. Dis <a %(blog_post)s>first release</a> small well well: about 300GB of book covers from di Libgen.rs fork, both fiction and non-fiction. Dem dey organized di same way as how dem dey appear for libgen.rs, e.g.: %(example)s for non-fiction book. Just like with di Z-Library collection, we put dem all inside big .tar file, wey you fit mount using <a %(a_ratarmount)s>ratarmount</a> if you wan serve di files directly. Release 1 (%(date)s) Di quick story of di different Library Genesis (or “Libgen”) forks, be say over time, di different people wey dey involved with Library Genesis get quarrel, and dem go their separate ways. Acording to dis <a %(a_mhut)s>forum post</a>, Libgen.li bin dey orijinaly host for “http://free-books.dontexist.com”. Di “.fun” version na di original founder create am. Dem dey revamp am for new, more distributed version. Di <a %(a_li)s>“.li” version</a> get massive collection of comics, as well as other content, wey no dey (yet) available for bulk download through torrents. E get separate torrent collection of fiction books, and e contain di metadata of <a %(a_scihub)s>Sci-Hub</a> for its database. Di “.rs” version get very similar data, and dem dey consistently release their collection in bulk torrents. E dey roughly split into “fiction” and “non-fiction” section. Originally for “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> in some sense na fork of Library Genesis too, though dem use different name for their project. Libgen.rs We dey also enrich our collection with metadata-only sources, which we fit match to files, e.g. using ISBN numbers or other fields. Below na overview of those. Again, some of these sources dey completely open, while for others we dey scrape dem. Note say for metadata search, we dey show di original records. We no dey do any merging of records. Metadata-only sources Open Library na open source project by di Internet Archive to catalog every book for di world. E get one of di world’s largest book scanning operations, and e get many books available for digital lending. E book metadata catalog dey freely available for download, and e dey included for Anna’s Archive (though e no dey currently for search, except if you explicitly search for an Open Library ID). Open Library Excluding duplicates Last updated Percentages of number of files %% mirrored by AA / torrents dey available Size Source Below na quick overview of di sources of di files for Anna’s Archive. Becos di shadow libraries dey often sync data from each other, e get plenty overlap between di libraries. Na why di numbers no dey add up to di total. Di “mirrored and seeded by Anna’s Archive” percentage dey show how many files we dey mirror by ourself. We dey seed those files in bulk through torrents, and make dem available for direct download through partner websites. Overview Total Torrents for Anna’s Archive For bakgraund on Sci-Hub, abeg refer to im <a %(a_scihub)s>official website</a>, <a %(a_wikipedia)s>Wikipedia page</a>, an dis <a %(a_radiolab)s>podcast interview</a>. Note say Sci-Hub don <a %(a_reddit)s>freeze since 2021</a>. E don freeze before, but for 2021 dem add few million papers. Still, some small number of papers dey add to di Libgen “scimag” collections, but e no reach to make new bulk torrents. We dey use di Sci-Hub metadata as Libgen.li provide am for dia “scimag” collection. We still dey use di <a %(a_dois)s>dois-2022-02-12.7z</a> dataset. Note say di “smarch” torrents don <a %(a_smarch)s>deprecate</a> and dem no dey include am for our torrents list. Torrents for Libgen.li Torrents for Libgen.rs Metadata an torrents Updates for Reddit Podcast interview Wikipedia page Sci-Hub Sci-Hub: e don freeze since 2021; most dey available through torrents Libgen.li: small small additions since then</div> Som librari dem dey promote di bulk sharing of dia data through torrents, while some no dey quick share dia collection. For di latter case, Anna’s Archive dey try scrape dia collections, and make dem available (see our <a %(a_torrents)s>Torrents</a> page). Some dey in-between situations, for example, where librari dem wan share, but dem no get di resources to do so. For those cases, we dey try help out. Below na overview of how we dey interface with di different librari dem. Source libraries %(icon)s Different file databases dey scatter for Chinese internet; but dem dey often be paid databases %(icon)s Most files dey only accessible with premium BaiduYun accounts; download speed dey slow. %(icon)s Anna’s Archive dey manage collection of <a %(duxiu)s>DuXiu files</a> %(icon)s Different metadata databases dey scatter for Chinese internet; but dem dey often be paid databases %(icon)s No get metadata dumps wey easy to access for dia whole collection. %(icon)s Anna’s Archive dey manage collection of <a %(duxiu)s>DuXiu metadata</a> Files %(icon)s Files dey available for borrowing only for small time, with different access restrictions %(icon)s Anna’s Archive dey manage collection of <a %(ia)s>IA files</a> %(icon)s Some metadata available through <a %(openlib)s>Open Library database dumps</a>, but those don’t cover the entire IA collection %(icon)s No get metadata dumps wey easy to access for dia whole collection %(icon)s Anna’s Archive dey manage collection of <a %(ia)s>IA metadata</a> Last updated %(icon)s Anna’s Archive an' Libgen.li dey manage kolekshon of <a %(comics)s>comic books</a>, <a %(magazines)s>magazines</a>, <a %(standarts)s>standard documents</a>, an' <a %(fiction)s>fiction (wey don diverge from Libgen.rs)</a> together. %(icon)s Non-Fiction torrents are shared with Libgen.rs (and mirrored <a %(libgenli)s>here</a>). %(icon)s Quarterly <a %(dbdumps)s>HTTP database dumps</a> %(icon)s Automated torrents for <a %(nonfiction)s>Non-Fiction</a> and <a %(fiction)s>Fiction</a> %(icon)s Anna’s Archive manages a collection of <a %(covers)s>book cover torrents</a> %(icon)s Daily <a %(dbdumps)s>HTTP database dumps</a> Metadata %(icon)s Monthly <a %(dbdumps)s>database dumps</a> %(icon)s Data torrents available <a %(scihub1)s>here</a>, <a %(scihub2)s>here</a>, and <a %(libgenli)s>here</a> %(icon)s Some new files are <a %(libgenrs)s>being</a> <a %(libgenli)s>added</a> to Libgen’s “scimag”, but not enough to warrant new torrents %(icon)s Sci-Hub has frozen new files since 2021. %(icon)s Metadata dumps available <a %(scihub1)s>here</a> and <a %(scihub2)s>here</a>, as well as as part of the <a %(libgenli)s>Libgen.li database</a> (which we use) Source %(icon)s Different smaller or one-off sources. We dey encourage people to upload to other shadow libraries first, but sometimes people get collections wey too big for others to sort through, but no big enough to get their own category. %(icon)s Not available directly in bulk, protected against scraping %(icon)s Anna’s Archive manages a collection of <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Anna’s Archive and Z-Library collaboratively manage a collection of <a %(metadata)s>Z-Library metadata</a> and <a %(files)s>Z-Library files</a> Datasets We dey combine all di sources above into one unified database wey we dey use to serve this website. This unified database no dey available directly, but since Anna’s Archive dey fully open source, e fit dey fairly easily <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases. Di scripts on that page go automatically download all di requisite metadata from di sources wey we mention above. If you wan explore our data before you run those scripts locally, you fit look at our JSON files, wey link further to other JSON files. <a %(a_json)s>This file</a> na good starting point. Unified database Torrents wey Anna’s Archive get browse search Different smaller or one-off sources. We dey encourage people to upload to other shadow libraries first, but sometimes people get collections wey too big for others to sort through, but no big enough to get their own category. Ovasabi fọm <a %(a1)s>datasets piji</a>. From <a %(a_href)s>aaaaarg.fail</a>. E dey fairly complete. From our volunteer “cgiym”. From an <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. E get high overlap with existing papers collections, but very few MD5 matches, so we decide to keep am completely. Skreip fọ <q>iRead eBooks</q> (= fọnetikali <q>ai rit i-books</q>; airitibooks.com), we volọntia <q>j</q> du. I match wit <q>airitibooks</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>. Fọ kolekshọn <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sọmtin fọm di orijina sọns, sọmtin fọm di-eye.eu, sọmtin fọm oda mirọs. From a private books torrent website, <a %(a_href)s>Bibliotik</a> (dem dey often call am “Bib”), of which books dem bundle into torrents by name (A.torrent, B.torrent) and distribute through the-eye.eu. From our volunteer “bpb9v”. For more information about <a %(a_href)s>CADAL</a>, see the notes for our <a %(a_duxiu)s>DuXiu dataset page</a>. More from our volunteer “bpb9v”, mostly DuXiu files, as well as a folder “WenQu” and “SuperStar_Journals” (SuperStar na the company wey dey behind DuXiu). From our volunteer “cgiym”, Chinese texts from various sources (represented as subdirectories), including from <a %(a_href)s>China Machine Press</a> (a major Chinese publisher). Non-Chinese collections (represented as subdirectories) from our volunteer “cgiym”. Skreip fọ buks wey tok abaut Chinese akitẹktshọ, we volọntia <q>cm</q> du: <q>A tek am bai eksplọit netwọk vọnẹrabiliti fọ di pọnlishin haus, bọt dat luphọl don klọs</q>. I match wit <q>chinese_architecture</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>. Books from academic publishing house <a %(a_href)s>De Gruyter</a>, collected from a few large torrents. Scrape of <a %(a_href)s>docer.pl</a>, a polish file sharing website wey dey focus on books and other written works. Scraped for late 2023 by volunteer “p”. We no get good metadata from the original website (not even file extensions), but we filter for book-like files and we fit often extract metadata from the files demself. DuXiu epubs, directly from DuXiu, collected by volunteer “w”. Only recent DuXiu books dey available directly through ebooks, so most of these must be recent. Remaining DuXiu files from volunteer “m”, wey no dey in the DuXiu proprietary PDG format (the main <a %(a_href)s>DuXiu dataset</a>). Collected from many original sources, unfortunately without preserving those sources in the filepath. <span></span> <span></span> <span></span> Skreip fọ erọtik buks, we volọntia <q>do no harm</q> du. I match wit <q>hentai</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>. <span></span> <span></span> Collection wey volunteer “t” scrape from Japanese Manga publisher. <a %(a_href)s>Selected judicial archives of Longquan</a>, provided by volunteer “c”. Scrape of <a %(a_href)s>magzdb.org</a>, ally of Library Genesis (e dey linked for libgen.rs homepage) but dem no want provide their files directly. Volunteer “p” obtain am for late 2023. <span></span> Different small uploads, too small to be their own subcollection, but represented as directories. Ebooks fọm AvaxHome, wan Rọshian fayl shẹrin website. Akaiv fọ nyuspepaz an magazins. I match wit <q>newsarch_magz</q> metadata fọ <a %(a1)s><q>Oda metadata skreips</q></a>. Skreip fọ di <a %(a1)s>Philosophy Documentation Center</a>. Collection of volunteer “o” wey collect Polish books directly from original release (“scene”) websites. Combined collections of <a %(a_href)s>shuge.org</a> by volunteers “cgiym” and “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (named after the fictional library), scraped in 2022 by volunteer “t”. <span></span> <span></span> <span></span> Sub-sub-collections (represented as directories) from volunteer “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (by <a %(a_sikuquanshu)s>Dizhi(迪志)</a> for Taiwan), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: “This site mainly focus on sharing high quality ebook files, some of which na the owner typeset by himself. The owner was <a %(a_arrested)s>arrested</a> for 2019 and someone make a collection of files wey he share.”). Remaining DuXiu files from volunteer “woz9ts”, wey no dey the DuXiu proprietary PDG format (still to be converted to PDF). The “upload” collection don split into smaller subcollections, wey dem indicate for the AACIDs and torrent names. All subcollections first deduplicate against the main collection, but the metadata “upload_records” JSON files still get plenty references to the original files. Non-book files dem also remove from most subcollections, and dem no dey usually <em>note</em> am for the “upload_records” JSON. The subcollections be: Notis Subkolekshọn Many subcollections demself get sub-sub-collections (e.g. from different original sources), wey dem represent as directories for the “filepath” fields. Uploads to Anna’s Archive Our blog post about dis data <a %(a_worldcat)s>WorldCat</a> na proprietary database by di non-profit <a %(a_oclc)s>OCLC</a>, wey dey gather metadata records from libraries all over di world. E fit be di biggest library metadata collection for di world. For October 2023 we <a %(a_scrape)s>release</a> comprehensive scrape of di OCLC (WorldCat) database, for di <a %(a_aac)s>Anna’s Archive Containers format</a>. October 2023, fess release: OCLC (WorldCat) Torrents by Anna’s Archive Example record for Anna’s Archive (original collection) Example record for Anna’s Archive (“zlib3” collection) Torrents wey Anna’s Archive get (metadata + content) Blog post about Release 1 Blog post about Release 2 For late 2022, dem arrest di people wey dem say start Z-Library, and United States authorities seize dia domains. Since den, di website don dey try come back online small small. Nobody sabi who dey run am now. Update as of February 2023. Z-Library start from di <a %(a_href)s>Library Genesis</a> community, and dem first use dia data take start. Since den, dem don professionalize well well, and dem get more modern interface. Na why dem fit get plenty donations, both money to take improve dia website, and new books donations. Dem don gather big collection join Library Genesis own. Di collection get three parts. Di original description pages for di first two parts dey preserved below. You need all three parts to get all data (except superseded torrents, wey dem cross out for di torrents page). %(title)s: our first release. Dis na di very first release of wetin dem call “Pirate Library Mirror” (“pilimi”). %(title)s: second release, dis time with all files wrapped inside .tar files. %(title)s: incremental new releases, using di <a %(a_href)s>Anna’s Archive Containers (AAC) format</a>, now released in collaboration with di Z-Library team. Di first mirror wey dem painstakingly get na for di course of 2021 and 2022. For dis point, e don small outdated: e dey reflect di state of di collection for June 2021. We go update am for future. Right now, we dey focus on getting dis first release out. Since Library Genesis don already dey preserved with public torrents, and e dey included for Z-Library, we do basic deduplication against Library Genesis for June 2022. For dis, we use MD5 hashes. E get as e be, plenty duplicate content dey for di library, like multiple file formats with di same book. Dis one hard to detect accurately, so we no dey do am. After di deduplication, we still get over 2 million files, totalling just under 7TB. Di collection get two parts: one MySQL “.sql.gz” dump of di metadata, and di 72 torrent files of around 50-100GB each. Di metadata contain di data as Z-Library website report (title, author, description, filetype), as well as di actual filesize and md5sum wey we observe, since sometimes dem no dey agree. E be like say some files for Z-Library get incorrect metadata. We fit also get some files wey we download incorrectly for some isolated cases, we go try detect and fix am for future. Di large torrent files contain di actual book data, with di Z-Library ID as di filename. Di file extensions fit reconstruct using di metadata dump. Di collection na mix of non-fiction and fiction content (no separate like for Library Genesis). Di quality dey vary well well. Dis first release don fully available now. Note say di torrent files dey only available through our Tor mirror. Release 1 (%(date)s) Dis na single extra torrent file. E no get any new information, but e get some data wey fit take time to compute. Dat one make am convenient to get, because to download dis torrent dey fast pass to compute am from scratch. E contain SQLite indexes for the tar files, for use with <a %(a_href)s>ratarmount</a>. Release 2 addendum (%(date)s) We don get all books wey dem add to Z-Library between our last mirror and August 2022. We don also go back and scrape some books wey we miss di first time around. All in all, dis new collection na about 24TB. Again, dis collection don deduplicated against Library Genesis, since torrents don already dey available for dat collection. Di data dey organized similar to di first release. E get one MySQL “.sql.gz” dump of di metadata, wey also include all di metadata from di first release, thereby superseding am. We also add some new columns: We mention dis last time, but just to clarify: “filename” and “md5” na di actual properties of di file, whereas “filename_reported” and “md5_reported” na wetin we scrape from Z-Library. Sometimes dis two no dey agree with each other, so we include both. For dis release, we change di collation to “utf8mb4_unicode_ci”, wey suppose compatible with older versions of MySQL. Di data files dey similar to last time, though dem big pass. We no fit bother create plenty smaller torrent files. “pilimi-zlib2-0-14679999-extra.torrent” contain all di files wey we miss for di last release, while di other torrents na all new ID ranges.  <strong>Update %(date)s:</strong> We make most of our torrents too big, causing torrent clients to struggle. We don remove dem and release new torrents. <strong>Update %(date)s:</strong> E still get too many files, so we wrap dem in tar files and release new torrents again. %(key)s: whether dis file dey already for Library Genesis, for either di non-fiction or fiction collection (matched by md5). %(key)s: which torrent dis file dey inside. %(key)s: set when we no fit download di book. Release 2 (%(date)s) Zlib releases (original description pages) Tor domain Main website Z-Library scrape Di “Chinese” kolekshon we dey Z-Library be like our DuXiu kolekshon, but wit different MD5s. We no dey put dis files for torrents to avoid duplication, but we still dey show dem for our search index. Metadata You get %(percentage)s%% bonus fast downloads, because you were referred by user %(profile_link)s. This one dey apply for the whole membership period. Donate Join Selected up to %(percentage)s%% discounts Alipay dey support international kredit/debit kards. See <a %(a_alipay)s>dis guide</a> for more information. Send us Amazon.com gift cards using your credit/debit card. You fit buy crypto wit credit/debit cards. WeChat (Weixin Pay) dey support international credit/debit cards. For di WeChat app, go to “Me => Services => Wallet => Add a Card”. If yu no see am, enable am using “Me => Settings => General => Tools => Weixin Pay => Enable”. (Use am wen pesin wan send Ethereum from Coinbase) copied! copy (lowest minimum amount) (warning: high minimum amount) -%(percentage)s%% 12 months 1 month 24 months 3 months 48 months 6 months 96 months Select how long you wan subscribe for. <div %(div_monthly_cost)s></div><div %(div_after)s>after <span %(span_discount)s></span> discounts</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% for 12 months for 1 month for 24 months for 3 months for 48 months for 6 months for 96 months %(monthly_cost)s / month contact us Direct <strong>SFTP</strong> servers Enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets). Expert Access <strong>Unlimited</strong> high-speed access <div %(div_question)s>I fit upgrade my membership or get multiple memberships?</div> <div %(div_question)s>Can I make a donation without becoming a member?</div> Sure thing. We dey accept donations of any amount for dis Monero (XMR) address: %(address)s. <div %(div_question)s>Wetin di ranges per month mean?</div> You fit reach di lower side of a range by applying all di discounts, like choosing a period wey long pass one month. <div %(div_question)s>Do memberships automatically renew?</div> Memberships <strong>no dey</strong> automatically renew. You fit join for as long or short as you want. <div %(div_question)s>What do you spend donations on?</div> 100%% dey go to preserving and making accessible di world's knowledge and culture. Currently we dey spend am mostly on servers, storage, and bandwidth. No money dey go to any team members personally. <div %(div_question)s>Can I make a large donation?</div> E go dey amazing! For donations wey pass few thousand dollars, abeg contact us directly for %(email)s. <div %(div_question)s>Do you have other payment methods?</div> Currently no. Plenty people no want archives like dis to exist, so we gatz dey careful. If you fit help us set up other (more convenient) payment methods safely, abeg contact us for %(email)s. Donation FAQ You get one <a %(a_donation)s>donation wey you don start</a>. Abeg finish or cansul am before you make new donation. <a %(a_all_donations)s>See all my donations</a> For donations wey pass $5000 abeg contact us directly for %(email)s. We welcome big donations from rich people or institutions.  Make you sabi se even though di membership for dis page na “per month”, dem be one-time donation (dem no dey repeat). See di <a %(faq)s>Donation FAQ</a>. Anna’s Archive na non-profit, open-source, open-data project. If you donate and become member, you go help our operations and development. To all our members: thank you for keeping us going! ❤️ For more information, check out the <a %(a_donate)s>Donation FAQ</a>. To become member, abeg <a %(a_login)s>Log in or Register</a>. Thanks for your support! $%(cost)s / month If you make mistake during payment, we no fit do refunds, but we go try make am right. Fain di “Krypto” pej for PayPal app or website. Dis dey ushualli unda “Finances”. Go to di “Bitcoin” pej for PayPal app or website. Pres di “Transfa” bɔtin %(transfer_icon)s, and den “Send”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s gift card Bank card Bank card (wey dey use app) Binans Kredit/debit/Apple/Google (BMC) Cash App Credit/debit card Credit/debit card 2 Credit/debit card (backup) Crypto %(bitcoin_icon)s Kard / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regula) Pix (Brazil) Revolut (for now e no dey available) WeChat Select your preferred crypto coin: Donate wit Amazon gift card. <strong>IMPORTANT:</strong> Dis op-shon na for %(amazon)s. If yu wan' use anoda Amazon website, selek am abov. <strong>IMPORTANT:</strong> We only support Amazon.com, not other Amazon websites. For example, .de, .co.uk, .ca, no dey supported. Abeg no write your own message. Enta di exact amount: %(amount)s Note say we need to round to amounts wey our resellers dey accept (minimum %(minimum)s). Donate wit credit/debit card, through the Alipay app (e easy wella to set up). Instol di Alipay app from di <a %(a_app_store)s>Apple App Store</a> or <a %(a_play_store)s>Google Play Store</a>. Regista wit your phone nombar. No oda pesonal detials dey required. <span %(style)s>1</span>Install Alipay app Supported: Visa, MasterCard, JCB, Diners Club and Discover. See <a %(a_alipay)s>dis guide</a> for more informeshon. <span %(style)s>2</span>Add bank card Wit Binance, yu fit buy Bitcoin wit credit/debit card or bank account, and then donate dat Bitcoin to us. Dat way we fit remain secure and anonymous wen we dey accept your donation. Binance dey available for almost every country, and e support most banks and credit/debit cards. Dis na our main recommendation for now. We appreciate say yu take time to learn how to donate using dis method, as e dey help us well well. For credit cards, debit cards, Apple Pay, and Google Pay, we dey use “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). For dia system, one “coffee” na $5, so your donation go round up to di nearest multiple of 5. Donate wit Cash App. If yu get Cash App, dis na di easiest way to donate! Note say for transactions wey dey under %(amount)s, Cash App fit charge %(fee)s fee. For %(amount)s or above, e dey free! Donate wit credit or debit card. Dis method dey use cryptocurrency provider as intermediate conversion. E fit dey a bit confusing, so abeg only use dis method if other payment methods no work. E no dey work for all countries too. We no fit support kredit/debit kards directly, because banks no wan work wit us. ☹ But, dia dey plenty ways to use kredit/debit kards anyhow, using oda payment methods: Wit crypto yu fit dash us moni using BTC, ETH, XMR, and SOL. Use dis option if yu sabi cryptocurrency well well. Wit crypto yu fit dash us moni using BTC, ETH, XMR, and more. Crypto express services If na di first time wey you dey use crypto, we suggest make you use %(options)s to buy and donate Bitcoin (di original and most used cryptocurrency). Note say for small donations di credit card fees fit cancel our %(discount)s%% discount, so we recommend longer subscriptions. Donate wit credit/debit card, PayPal, or Venmo. Yu fit choose between dem for di next page. Google Pay and Apple Pay fit work too. Note say for small donations di fees dey high, so we recommend longer subscriptions. To donate using PayPal US, we go use PayPal Crypto, wey go allow us remain anonymous. We appreciate say you take time to learn how to donate using dis method, since e go help us well well. Donate using PayPal. Donate wit yu regula PayPal account. Donate wit Revolut. If yu get Revolut, dis na di easiest way to donate! Dis payment method only allow maximum of %(amount)s. Abeg select different duration or payment method. Dis payment method need minimum of %(amount)s. Abeg select different duration or payment method. Binans Coinbase Kraken Abeg chuz payment method. “Adopt a torrent”: your username or message for torrent filename <div %(div_months)s>once every 12 months of membership</div> Your username or anonymous mention for di credits Early access to new features Exclusive Telegram wit behind-the-scenes updates %(number)s fast downloads per day if you donate dis month! <a %(a_api)s>JSON API</a> access Legendary status for preservation of humanity’s knowledge and culture Previous perks, plus: Earn <strong>%(percentage)s%% bonus downloads</strong> by <a %(a_refer)s>referring friends</a>. SciDB papas <strong>unlimited</strong> witout verification If you get any question about account or donation, abeg add your account ID, screenshots, receipts, and as much information as you fit add. We dey check our email every 1-2 weeks, so if you no add this information, e go delay any resolution. To get even more downloads, <a %(a_refer)s>refer your friends</a>! We be small team of volunteers. E fit take us 1-2 weeks to respond. Note say di account name or picture fit look strange. No need to worry! These accounts dey managed by our donation partners. Our accounts no dey hacked. Donate <span %(span_cost)s></span> <span %(span_label)s></span> for 12 months “%(tier_name)s” for 1 month “%(tier_name)s” for 24 months “%(tier_name)s” for 3 months “%(tier_name)s” for 48 months “%(tier_name)s” for 6 months “%(tier_name)s” for 96 months “%(tier_name)s” You fit still cancel di donation during checkout. Click di donate button to confirm dis donation. <strong>Important note:</strong> Crypto price fit change anyhow, sometimes even reach 20%% for few minutes. Dis still better pass di fees wey we dey pay many payment providers, wey dey charge 50-60%% to work with “shadow charity” like us. <u>If yu send us di receipt with di original price wey yu pay, we go still credit yu akount for di membership wey yu choose</u> (as long as di receipt no old pass few hours). We really appreciate sey yu fit manage dis kind wahala to support us! ❤️ ❌ Somtin go wrong. Abeg reload di page and try again. <span %(span_circle)s>1</span>Bay Bitcoin for Paypal <span %(span_circle)s>2</span>Transfa di Bitcoin to our adrese ✅ We dey redirect you to di donation page… Donate Abeg wait at least <span %(span_hours)s>24 hours</span> (and refresh dis page) before you contact us. If yu wan dash us moni (any amount) witout membership, yu fit use dis Monero (XMR) address: %(address)s. After you send your gift card, our automated system go confirm am within few minutes. If e no work, try resend your gift card (<a %(a_instr)s>instructions</a>). If e still no work, abeg email us and Anna go manually review am (e fit take few days), and make sure say you mention if you don try resend am already. Example: Abeg use di <a %(a_form)s>official Amazon.com form</a> to send us gift card of %(amount)s to di email address below. “To” recipient email for di form: Amazon gift card We no fit accept other methods of gift cards, <strong>only sent directly from di official form on Amazon.com</strong>. We no fit return your gift card if you no use this form. Use am only once. Unique to your account, no share am. Dey wait for gift card… (refresh di page to check) Open di <a %(a_href)s>QR-code donation page</a>. Scan di QR code wit di Alipay app, or press di button to open di Alipay app. Abeg make you get patience; di page fit take small time to load because e dey China. <span %(style)s>3</span>Make donation (scan QR code or press button) Buy PYUSD coin on PayPal Buy Bitcoin (BTC) for Cash App Buy small more (we recommend %(more)s more) pass di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain. Go di “Bitcoin” (BTC) page for Cash App. Transfer di Bitcoin to our address For small donations (under $25), you fit need use Rush or Priority. Click di “Send bitcoin” button to make “withdrawal”. Switch from dollars to BTC by pressing di %(icon)s icon. Enter di BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck. Express services dey convenient, but dem dey charge higher fees. You fit use dis instead of a crypto exchange if you wan quickly make bigger donation and you no mind fee of $5-10. Make sure say you send di exact crypto amount wey dey show for di donation page, no be di amount for $USD. Otherwise di fee go dey subtracted and we no fit automatically process your membership. Sometime, e fit tek up to 24 hours for confirmation to show, so make sure say you refresh dis page (even if e don expire). Kredit / debit kad instrɔkshɔn Donet tru our kredit / debit kad pej Sọm of di steps mɛntshọn kripto wɔlɛts, bọt no wori, yu nɔ nid to lɛn enitin abaut kripto fɔ dis. %(coin_name)s instructions Scan this QR code with your crypto wallet app to quickly fill in the payment details Scan QR Code to Pay We only support di standard version of crypto coins, no exotic networks or versions of coins. E fit take up to one hour to confirm di transaction, depending on di coin. Donet %(amount)s on <a %(a_page)s>dis pej</a>. Dis donéshon don expire. Abeg kansul am and create new one. If yu don pay alredi: Yes, a don imel mi risit If di kripto ekschej reit fluktuet durin di transakshọn, mek shɔr se yu inklud di risit we de shɔw di orijinal ekschej reit. We rili aprishiet se yu tek di trɔbul to yus kripto, e help us wel-wel! ❌ Sọmtin go rɔng. Abeg rilod di pej an trai agen. <span %(span_circle)s>%(circle_number)s</span>Imel us di risit If you run into any issues, please contact us at %(email)s and include as much information as possible (such as screenshots). ✅ Tenk yu fɔ yɔ doneshọn! Anna go manwali aktivet yɔ membaship witin smɔl deys. Send risit ɔ screenshot to yɔ pɛsɔnɔl verifikeshọn adres: Wen yu don imel yɔ risit, klik dis bọtịn, mek Anna fit manwali riviw am (dis fit tek smɔl deys): Send receipt or screenshot to your personal verification address. No use dis email address for your PayPal donation. Kansul Yes, abeg kansul am Yu sure sey yu wan kansul? No kansul if yu don pay alredi. ❌ Somtin don go rong. Abeg reload di page and try again. Mek new donéshon ✅ Yu don kansul yu donéshon. Date: %(date)s Identifier: %(id)s Reorder Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months, including %(discounts)s%% discount)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / month for %(duration)s months)</span> 1. Put ya email. 2. Choose ya payment method. 3. Choose ya payment method again. 4. Select “Self-hosted” wallet. 5. Click “I confirm ownership”. 6. You go receive email receipt. Abeg send am to us, and we go confirm your donation as soon as possible. (yu fit wan kansul and kriɛt a nyu doneshɔn) Di payment instrukshon don old. If yu wan mek anoda donéshon, use di “Reorder” button wey dey up. Yu don pay alredi. If yu wan review di payment instrukshon again, klik hia: Show old payment instrukshon If di donation page block, try use different internet connection (e.g. VPN or phone internet). Sɔri, di Alipay pej di ɔnli de aksesibul fɔ <strong>mainland China</strong>. Yu fit nid to tɛmprari disebul yɔ VPN, ɔ yus VPN we go konekt to mainland China (ɔ Hong Kong fit wɔrk somtaim). <span %(span_circle)s>1</span>Donet wit Alipay Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis Alipay akɔunt</a> Alipay instrɔkshọn <span %(span_circle)s>1</span>Transfa go wan of our krypto akant Donet di totol amɔunt of %(total)s to wan of dis adrese: Krypto instrɔkshɔn Folo di instrɔkshɔn to bay Bitcoin (BTC). Yu onli nid to bay di amɔunt wey yu wan donet, %(total)s. Enta our Bitcoin (BTC) adrese as di risipient, and folo di instrɔkshɔn to send yu doneshɔn of %(total)s: <span %(span_circle)s>1</span>Donet wit Pix Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis Pix akɔunt Pix instrɔkshọn <span %(span_circle)s>1</span>Donet wit WeChat Donet di totol amɔunt of %(total)s yus <a %(a_account)s>dis WeChat akɔunt</a> WeChat instrɔkshọn Use any of di following “credit card to Bitcoin” express services, wey only take few minutes: BTC / Bitcoin address (external wallet): BTC / Bitcoin amount: Fill di follow detéls for di form: If any of dis informéshon don old, abeg send us email mek we sabi. Abeg use dis <span %(underline)s>exact amount</span>. Your total cost fit high pass because of credit card fees. For small amounts, dis fit pass our discount, unfortunately. (minimum: %(minimum)s, no verification for first transaction) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, no verification for first transaction) (minimum: %(minimum)s) (minimum: %(minimum)s depending on country, no verification for first transaction) Follow the instructions to buy PYUSD coin (PayPal USD). Buy small more (we recommend %(more)s more) than di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain. Go to di “PYUSD” page for your PayPal app or website. Press di “Transfer” button %(icon)s, and then “Send”. Update status To risɛt di taim, simplli kriɛt a nyu doneshɔn. Mek sure se na di BTC amount wey dey below, <em>NO BE</em> euros or dollars, otherwise we no go fit receive di correct amount and we no go fit confirm your membership automatically. Buy Bitcoin (BTC) for Revolut Buy small more (we recommend %(more)s more) pass di amount wey you wan donate (%(amount)s), to cover transaction fees. You go keep anything wey remain. Go di “Crypto” page for Revolut to buy Bitcoin (BTC). Transfer di Bitcoin to our address For small donations (under $25) you fit need use Rush or Priority. Click di “Send bitcoin” button to make “withdrawal”. Switch from euros to BTC by pressing di %(icon)s icon. Enter di BTC amount below and click “Send”. See <a %(help_video)s>this video</a> if you get stuck. Status: 1 2 Step-bai-step gaid Si di step-bai-step gaid bɛlo. If not, yu fit lock out from dis akount! If yu neva do am, write down yu secret key for log in: Tank yu for yu donéshon! Taim lɛft: Donation Transfer %(amount)s to %(account)s Wetin for konfirmeshɔn (rɛfresh di pej to chɛk)… Wetin for transfa (rɛfresh di pej to chɛk)… Earlier Fast downloads for the last 24 hours dey count towards the daily limit. Downloads from Fast Partner Servers dey marked by %(icon)s. Last 18 hours No files downloaded yet. Downloaded files no dey publicly show. All times dey for UTC. Downloaded files If you download file with both fast and slow downloads, e go show up twice. No worry too much, plenty people dey download from websites wey we link to, and e rare well well to enter wahala. But to stay safe, we recommend make you use VPN (paid), or <a %(a_tor)s>Tor</a> (free). I download 1984 by George Orwell, police go come my door? Na yu be Anna! Hu be Anna? We get one stable JSON API for members, to get fast download URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation dey inside JSON itself). For other use cases, like to iterate through all our files, build custom search, and so on, we recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. The raw data fit be manually explored <a %(a_explore)s>through JSON files</a>. Our raw torrents list fit be downloaded as <a %(a_torrents)s>JSON</a> as well. Una get API? We no dey host any copyrighted materials here. We be search engine, and as such only index metadata wey don already dey publicly available. When you dey download from these external sources, we go suggest make you check di laws for your area with respect to wetin dem allow. We no dey responsible for content wey others dey host. If you get complaints about wetin you see here, your best bet na to contact di original website. We dey regularly pull their changes enter our database. If you really think say you get valid DMCA complaint we suppose respond to, abeg fill out di <a %(a_copyright)s>DMCA / Copyright claim form</a>. We dey take your complaints seriously, and we go get back to you as soon as possible. How I go report copyright infringement? Here be some books wey carry special significance to di world of shadow libraries and digital preservation: Wetin be your favorite books? We go also like remind everybody say all our code and data dey completely open source. This one unique for projects like ours — we no sabi any other project with similarly massive catalog wey dey fully open source too. We very much welcome anybody wey think say we dey run our project poorly to take our code and data and set up their own shadow library! We no dey talk this one out of spite or something — we genuinely think say this go dey awesome since e go raise di bar for everybody, and better preserve humanity's legacy. I hate how una dey run this project! We go love make people set up <a %(a_mirrors)s>mirrors</a>, and we go support dis financially. How I fit help? Yes, we dey collect am. Our inspiration for collecting metadata is Aaron Swartz’ goal of “one web page for every book ever published”, for which he created <a %(a_openlib)s>Open Library</a>. That project has done well, but our unique position allows us to get metadata that they can’t. Another inspiration was our desire to know <a %(a_blog)s>how many books there are in the world</a>, so we can calculate how many books we still have left to save. Una dey collect metadata? Note say mhut.org dey block certain IP ranges, so VPN fit dey required. <strong>Android:</strong> Click the three-dot menu for top right, and select “Add to Home Screen”. <strong>iOS:</strong> Click the “Share” button for bottom, and select “Add to Home Screen”. We no get official mobile app, but you fit install this website as app. Una get mobile app? Abeg send dem to di <a %(a_archive)s>Internet Archive</a>. Dem go preserve dem well. How I fit donate books or oda physical materials? How I go fit request books? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regila updates <a %(a_software)s>Anna’s Software</a> — our open source code <a %(a_datasets)s>Datasets</a> — about di data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domains More resources dey about Anna’s Archive? <a %(a_translate)s>Translate on Anna’s Software</a> — our translation system <a %(a_wikipedia)s>Wikipedia</a> — more about us (abeg help keep dis page updated, or create one for your own language!) Select the settings wey you like, leave the search box empty, click “Search”, and then bookmark the page using your browser’s bookmark feature. How I go fit save my search settings? We welcome security researchers to search for vulnerabilities for our systems. We be big supporters of responsible disclosure. Contact us <a %(a_contact)s>here</a>. We no fit award bug bounties for now, except for vulnerabilities wey get di <a %(a_link)s>potential to compromise our anonymity</a>, for which we dey offer bounties in di $10k-50k range. We go like offer wider scope for bug bounties for di future! Abeg note say social engineering attacks dey out of scope. If you dey interested in offensive security, and you wan help archive di world’s knowledge and culture, make sure say you contact us. Plenty ways dey wey you fit help. Una get responsible disclosure program? We no get enough resources to give everybody for di world high-speed downloads, as much as we go like. If one rich benefactor go like step up and provide dis for us, e go dey incredible, but until then, we dey try our best. We be non-profit project wey dey barely sustain itself through donations. Na why we implement two systems for free downloads, with our partners: shared servers with slow downloads, and slightly faster servers with waitlist (to reduce di number of people wey dey download at di same time). We also get <a %(a_verification)s>browser verification</a> for our slow downloads, because otherwise bots and scrapers go abuse them, make things even slower for legitimate users. Note say, when you dey use di Tor Browser, you fit need to adjust your security settings. On di lowest of di options, wey dem call “Standard”, di Cloudflare turnstile challenge dey succeed. On di higher options, wey dem call “Safer” and “Safest”, di challenge dey fail. If you download big files, sometimes e fit break for middle. We dey recommend make you use download manager (like JDownloader) to continue big downloads automatically. Wetin make di slow downloads slow? Frequently Asked Questions (FAQ) Use di <a %(a_list)s>torrent list generator</a> to generate list of torrents wey need torrenting pass, inside your storage space limits. Yes, see di <a %(a_llm)s>LLM data</a> page. Most torrents get di files directly, wey mean say you fit tell torrent clients to only download di files wey you need. To know which files to download, you fit <a %(a_generate)s>generate</a> our metadata, or <a %(a_download)s>download</a> our ElasticSearch and MariaDB databases. Unfortunately, some torrent collections get .zip or .tar files for di root, so you need to download di whole torrent before you fit select individual files. No easy to use tools for filtá di torrents no dey available yet, but we dey welcome contributions. (We get <a %(a_ideas)s>some ideas</a> for the latter case sha.) Long answer: Short answer: e no easy. We dey try to keep minimal duplication or overlap between di torrents for this list, but e no dey always possible, and e depend well well on di policies of di source libraries. For libraries wey dey put out their own torrents, e dey out of our hands. For torrents wey Anna’s Archive release, we dey deduplicate only based on MD5 hash, wey mean say different versions of di same book no go deduplicate. Yes. These na actually PDFs and EPUBs, dem just no get extension for many of our torrents. Two places dey wey you fit find di metadata for torrent files, including di file types/extensions: 1. Each collection or release get their own metadata. For example, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> get corresponding metadata database wey dem host for di Libgen.rs website. We dey usually link to relevant metadata resources from each collection’s <a %(a_datasets)s>dataset page</a>. 2. We recommend <a %(a_generate)s>generating</a> or <a %(a_download)s>downloading</a> our ElasticSearch and MariaDB databases. These ones get mapping for each record for Anna’s Archive to its corresponding torrent files (if available), under “torrent_paths” for di ElasticSearch JSON. Some torrent clients no dey support large piece sizes, wey plenty of our torrents get (for newer ones we no dey do am again — even though e dey valid per specs!). So try different client if you run into this, or complain to the makers of your torrent client. I go like help seed, but I no get plenty disk space. Di torrents dey too slow; I fit download di data directly from una? I fit download only some of di files, like only one particular language or topic? How una dey handle duplicates for di torrents? I fit get di torrent list as JSON? I no see PDFs or EPUBs for di torrents, only binary files? Wetin I go do? Why my torrent client no fit open some of una torrent files / magnet links? Torrents FAQ How I go upload new books? Abeg see <a %(a_href)s>dis beta project</a>. Yu get uptime monitor? Wetin be Anna’s Archive? Become member to use fast downloads. We don add Amazon gift cards, credit and debit cards, crypto, Alipay, and WeChat. You don run out of fast downloads today. Access Hourly downloads in the last 30 days. Hourly average: %(hourly)s. Daily average: %(daily)s. We dey work with partners to make our collections easy and free for anybody to access. We believe say everybody get right to the collective wisdom of humanity. And <a %(a_search)s>no be at the expense of authors</a>. Di datasets wey dem dey use for Anna’s Archive dey completely open, and you fit mirror dem in bulk using torrents. <a %(a_datasets)s>Learn more…</a> Long-term archive Full database Search Books, papers, magazines, comics, library records, metadata, … All our <a %(a_code)s>code</a> and <a %(a_datasets)s>data</a> dey completely open source. <span %(span_anna)s>Anna’s Archive</span> na non-profit project wey get two goals: <li><strong>Preservation:</strong> To back up all knowledge and culture wey humanity get.</li><li><strong>Access:</strong> To make this knowledge and culture available to anybody for the world.</li> We get di world’s largest collection of high-quality text data. <a %(a_llm)s>Learn more…</a> LLM training data 🪩 Mirrors: call for volunteers If you dey run high-risk anonymous payment processor, abeg contact us. We dey also find people wey go place tasteful small ads. All di proceeds go to our preservation efforts. Preservation We estimate say we don preserve about <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% of the world’s books</a>. We dey preserve books, papers, comics, magazines, and more, by bringing these materials from different <a href="https://en.wikipedia.org/wiki/Shadow_library">shadow libraries</a>, official libraries, and other collections together for one place. All this data go dey preserved forever by making am easy to duplicate am in bulk — using torrents — wey go result in many copies around the world. Some shadow libraries dey already do this themselves (e.g. Sci-Hub, Library Genesis), while Anna’s Archive dey “liberate” other libraries wey no dey offer bulk distribution (e.g. Z-Library) or no be shadow libraries at all (e.g. Internet Archive, DuXiu). This wide distribution, combined with open-source code, dey make our website strong against takedowns, and e dey ensure the long-term preservation of humanity’s knowledge and culture. Learn more about <a href="/datasets">our datasets</a>. If yu be <a %(a_member)s>memba</a>, browser verification no dey required. 🧬&nbsp;SciDB na di continuation of Sci-Hub. SciDB Open DOI Sci-Hub don <a %(a_paused)s>pause</a> uploading of new papers. Direct akses to %(count)s akademik pepa dem 🧬&nbsp;SciDB na continuation of Sci-Hub, wit di same interface an direct viewing of PDFs. Enter your DOI to view. We get di full Sci-Hub collection, as well as new papers. Most of dem fit be viewed directly wit di same interface, like Sci-Hub. Some fit be downloaded through external sources, in which case we go show links to those. You fit help well well by seeding torrents. <a %(a_torrents)s>Learn more…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 We dey find volunteers As we be non-profit, open-source project, we dey always find people wey go help. IPFS downloads List by %(by)s, created <span %(span_time)s>%(time)s</span> Save ❌ Something don go wrong. Abeg try again. ✅ Saved. Abeg reload the page. List empty. edit Add or remove from dis list by finding file and opening the “Lists” tab. List How we fit help Removing overlap (deduplication) Text and metadata extraction OCR We fit provide high-speed access to our full collections, as well as to unreleased collections. This na enterprise-level access we fit provide for donations wey dey range tens of thousands USD. We dey also willing to trade this for high-quality collections wey we never get yet. We fit refund you if you fit provide us with enrichment of our data, like: Support long-term archival of human knowledge, while you dey get better data for your model! <a %(a_contact)s>Contact us</a> make we discuss how we fit work together. E dey well known say LLMs dey perform well with high-quality data. We get the biggest collection of books, papers, magazines, etc for the world, wey be some of the highest quality text sources. LLM data Unique scale and range Our collection get over hundred million files, including academic journals, textbooks, and magazines. We achieve this scale by combining big existing repositories. Some of our source collections dey already available in bulk (Sci-Hub, and parts of Libgen). Other sources na we liberate am ourselves. <a %(a_datasets)s>Datasets</a> dey show full overview. Our collection get millions of books, papers, and magazines from before the e-book era. Big parts of this collection don already dey OCR’ed, and don already get small internal overlap. Continue If you lose your key, abeg <a %(a_contact)s>contact us</a> and provide as much information as you fit. You fit need create new account temporarily to contact us. Abeg <a %(a_account)s>login</a> to view dis page.</a> To stop spam-bots from creating plenty accounts, we need to verify your browser first. If you dey caught for infinite loop, we recommend make you install <a %(a_privacypass)s>Privacy Pass</a>. E fit also help make you turn off ad blockers and other browser extensions. Log in / Register Anna’s Archive dey temporarily down for maintenance. Abeg come back in one hour. Anoda author Alternative description Alternative edition Alternative extension Alternative filename Alternative publisher Anoda title date open sourced Read more… description Sabi Anna’s Archive for CADAL SSNO number Sabi Anna’s Archive for DuXiu SSID number Sabi Anna’s Archive for DuXiu DXID number Sabi Anna’s Archive for ISBN Sabi Anna’s Archive for OCLC (WorldCat) number Sabi Anna’s Archive for Open Library ID Anna’s Archive online viewer %(count)s affected pages Afta yu download: A better version of this file might be available at %(link)s Bulk torrent downloads kolekshon Use online tools to change between formats. Recommended conversion tools: %(links)s For big files, we dey recommend make you use download manager to stop any wahala. Recommended download managers: %(links)s EBSCOhost eBook Index (na for experts only) (also click “GET” at the top) (click “GET” at the top) External downloads <strong>🚀 Fast downloads</strong> You have %(remaining)s left today. Thanks for being a member! ❤️ <strong>🚀 Fast downloads</strong> You’ve run out of fast downloads for today. <strong>🚀 Fast downloads</strong> You downloaded this file recently. Links remain valid for a while. <strong>🚀 Fast downloads</strong> Become a <a %(a_membership)s>member</a> to support the long-term preservation of books, papers, and more. To show our gratitude for your support, you get fast downloads. ❤️ 🚀 Fast downloads 🐢 Slow downloads Borrow from the Internet Archive IPFS Gateway #%(num)d (you fit try many times wit IPFS) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction their ads are known to contain malicious software, so use an ad blocker or don’t click ads Amazon‘s “Send to Kindle” djazz‘s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC files fit no dey reliable to download) No downloads found. All download options get the same file, and e suppose safe to use. But, always dey careful when you dey download files from internet, especially from sites wey no be Anna’s Archive. For example, make sure say your devices dey updated. (no redirect) Open am for our viewer (open for viewer) Option #%(num)d: %(link)s %(extra)s Find original record for CADAL Sabi manually for DuXiu Find original record for ISBNdb Find original record for WorldCat Find original record for Open Library Sabi different databases for ISBN (print disabled patrons only) PubMed You go need ebook or PDF reader to open di file, depend on di file format. Recommended ebook readers: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (DOI wey dem fit no get for Sci-Hub) You fit send both PDF and EPUB files to your Kindle or Kobo eReader. Recommended tools: %(links)s More information dey for <a %(a_slow)s>FAQ</a>. Support authors and libraries If yu laik dis an yu fit afod am, tink about to buy di orijinal, or support di authos direct. If dis dey availabul for yu local libry, tink about to borrow am for free dia. Partner Server downloads temporarily not available for this file. torrent From trusted partners. Z-Library Z-Library for Tor (you go need Tor Browser) show external downloads <span class="font-bold">❌ Dis file fit get wahala, dem don hide am from one source library.</span> Sometimes na request from di copyright holder, sometimes na because better alternative dey available, but sometimes na because wahala dey with di file itself. E fit still dey okay to download, but we recommend make you first search for alternative file. More details: If you still want to download this file, be sure to only use trusted, updated software to open it. metadata comments AA: Search Anna’s Archive for “%(name)s” Codes Explorer: View in Codes Explorer “%(name)s” URL: Website: If you get dis file and e no dey available for Anna’s Archive, consider <a %(a_request)s>to upload am</a>. Internet Archive Controlled Digital Lending file “%(id)s” Dis na record of file from Internet Archive, no be directly downloadable file. You fit try borrow di book (link below), or use dis URL when <a %(a_request)s>you dey request file</a>. Improve metadata CADAL SSNO %(id)s metadata record Dis na metadata record, e no be downloadable file. You fit use dis URL when <a %(a_request)s>you dey request file</a>. DuXiu SSID %(id)s metadata record ISBNdb %(id)s metadata record MagzDB ID %(id)s metadata record Nexus/STC ID %(id)s metadata record OCLC (WorldCat) nomba %(id)s metadata record Open Library %(id)s metadata record Sci-Hub file “%(id)s” No dey “%(md5_input)s” no dey our database. Add comment (%(count)s) You can get the md5 from the URL, e.g. MD5 of a better version of this file (if applicable). Fill this in if there is another file that closely matches this file (same edition, same file extension if you can find one), which people should use instead of this file. If you know of a better version of this file outside of Anna’s Archive, then please <a %(a_upload)s>upload it</a>. Somtin go wrong. Abeg reload di page an try again. Yu don leave comment. E fit take one minute make e show. Please use the <a %(a_copyright)s>DMCA / Copyright claim form</a>. Describe the issue (required) If dis file get beta quality, yu fit discuss anytin about am here! If e no get, abeg use di “Report file issue” button. Great file quality (%(count)s) File quality Lern how to <a %(a_metadata)s>improve di metadata</a> for dis file yasef. Issue description Please <a %(a_login)s>log in</a>. I love dis book! Help out the community by reporting the quality of this file! 🙌 Somtin go wrong. Abeg reload di page an try again. Report file issue (%(count)s) Tank yu for submit yua report. E go show for dis page, an Anna go review am manually (until we get beta moderation system). Leave comment Submit report What is wrong with this file? Borrow (%(count)s) Comments (%(count)s) Downloads (%(count)s) Explore metadata (%(count)s) Lists (%(count)s) Stats (%(count)s) For information about dis particular file, check out im <a %(a_href)s>JSON file</a>. Dis na file wey di <a %(a_ia)s>IA’s Controlled Digital Lending</a> library dey manage, an Anna’s Archive don index am for search. For information about di various datasets wey we don compile, see di <a %(a_datasets)s>Datasets page</a>. Metadata from linked record Improve metadata on Open Library “File MD5” na hash wey dem compute from di file content, an e dey reasonably unique based on dat content. All shadow libraries wey we don index here dey use MD5s to identify files. File fit show for multiple shadow libraries. For information about di various datasets wey we don compile, see di <a %(a_datasets)s>Datasets page</a>. Report file quality Total downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Czech metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Warning: multiple linked records: When you look one book for Anna’s Archive, you go see different fields: title, author, publisher, edition, year, description, filename, and more. All those pieces of information na <em>metadata</em>. Since we dey combine books from different <em>source libraries</em>, we dey show any metadata wey dey available for that source library. For example, for one book wey we get from Library Genesis, we go show di title from Library Genesis’ database. Sometimes one book go dey <em>multiple</em> source libraries, wey fit get different metadata fields. For that case, we go just show di longest version of each field, since that one hopefully get di most useful information! We go still show di other fields below di description, e.g. as ”alternative title” (but only if dem different). We dey also extract <em>codes</em> like identifiers and classifiers from di source library. <em>Identifiers</em> dey uniquely represent one particular edition of one book; examples na ISBN, DOI, Open Library ID, Google Books ID, or Amazon ID. <em>Classifiers</em> dey group together multiple similar books; examples na Dewey Decimal (DCC), UDC, LCC, RVK, or GOST. Sometimes these codes dey explicitly linked for source libraries, and sometimes we fit extract dem from di filename or description (primarily ISBN and DOI). We fit use identifiers to find records for <em>metadata-only collections</em>, like OpenLibrary, ISBNdb, or WorldCat/OCLC. There dey specific <em>metadata tab</em> for our search engine if you wan browse those collections. We dey use matching records to fill in missing metadata fields (e.g. if title dey miss), or e.g. as “alternative title” (if existing title dey). To see exactly where metadata of one book come from, see di <em>“Technical details” tab</em> for one book page. E get link to di raw JSON for that book, with pointers to di raw JSON of di original records. For more information, see di following pages: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, and <a %(a_example)s>Example metadata JSON</a>. Finally, all our metadata fit be <a %(a_generated)s>generated</a> or <a %(a_downloaded)s>downloaded</a> as ElasticSearch and MariaDB databases. Background You fit help preserve books by improving metadata! First, read di background about metadata for Anna’s Archive, and then learn how to improve metadata through linking with Open Library, and earn free membership for Anna’s Archive. Improve metadata So if you see one file with bad metadata, how you go fit fix am? You fit go di source library and follow their procedures for fixing metadata, but wetin you go do if di file dey multiple source libraries? There dey one identifier wey dem dey treat special for Anna’s Archive. <strong>The annas_archive md5 field for Open Library always dey override all other metadata!</strong> Make we first go back small and learn about Open Library. Open Library start for 2006 by Aaron Swartz with di goal of “one web page for every book ever published”. E be like Wikipedia for book metadata: everybody fit edit am, e dey freely licensed, and fit be downloaded in bulk. E be book database wey align well with our mission — in fact, Anna’s Archive don dey inspired by Aaron Swartz’ vision and life. Instead of reinventing di wheel, we decide to redirect our volunteers towards Open Library. If you see one book wey get incorrect metadata, you fit help out in di following way: Note say dis one only work for books, no be academic papers or other types of files. For other types of files we still recommend make you find di source library. E fit take few weeks for changes to dey included for Anna’s Archive, since we need to download di latest Open Library data dump, and regenerate our search index.  Go to di <a %(a_openlib)s>Open Library website</a>. Find di correct book record. <strong>WARNING:</strong> make sure say you select di correct <strong>edition</strong>. For Open Library, you get “works” and “editions”. A “work” fit be “Harry Potter and the Philosopher's Stone”. An “edition” fit be: Di 1997 first edition wey Bloomsbery publish wit 256 pages. Di 2003 paperback edition wey Raincoast Books publish wit 223 pages. Di 2000 Polish translation “Harry Potter I Kamie Filozoficzn” by Media Rodzina wit 328 pages. All di editions get different ISBNs and different contents, so make sure say you select di correct one! Edit di record (or create am if e no dey), and add as much useful information as you fit! You dey here now anyway, so make di record really amazing. Under “ID Numbers” select “Anna’s Archive” and add di MD5 of di book from Anna’s Archive. Dis na di long string of letters and numbers wey dey after “/md5/” for di URL. Try to find other files for Anna’s Archive wey match dis record too, and add dem as well. For di future we fit group dem as duplicates for Anna’s Archive search page. When you don finish, write down di URL wey you just update. Once you don update at least 30 records wit Anna’s Archive MD5s, send us an <a %(a_contact)s>email</a> and send us di list. We go give you free membership for Anna’s Archive, so you fit do dis work more easily (and as thank you for your help). Dis ones gatz be high quality edits wey add plenty information, otherwise your request go reject. Your request go also reject if any of di edits get revert or correct by Open Library moderators. Open Library linking If you get significantly involved for di development and operations of our work, we fit discuss sharing more of di donation revenue with you, make you deploy as necessary. We go only pay for hosting once you don set everything up, and don show say you fit keep di archive up to date with updates. Dis mean say you go pay for di first 1-2 months from your pocket. Your time no go dey compensated (and our own no dey too), since dis na pure volunteer work. We dey willing to cover hosting and VPN expenses, initially up to $200 per month. Dis go dey enough for basic search server and DMCA-protected proxy. Hosting expenses Abeg <strong>no contact us</strong> to ask for permission, or for basic questions. Actions dey speak louder than words! All di information dey out there, so just go ahead with setting up your mirror. Feel free to post tickets or merge requests to our Gitlab when you run into issues. We fit need build some mirror-specific features with you, like rebranding from “Anna’s Archive” to your website name, (initially) disabling user accounts, or linking back to our main site from book pages. Once you don get your mirror running, abeg contact us. We go like review your OpSec, and once e solid, we go link to your mirror, and start to work closer with you. Thanks in advance to anyone wey willing to contribute in dis way! E no be for faint heart, but e go solidify di longevity of di largest truly open library for human history. Getting started To increase the resiliency of Anna’s Archive, we dey look for volunteers to run mirrors. Your version clear say e be mirror, example “Bob’s Archive, an Anna’s Archive mirror”. You dey willing to take di risks wey dey follow dis work, wey dey significant. You get deep understanding of di operational security wey e need. Di contents of <a %(a_shadow)s>dis</a> <a %(a_pirate)s>posts</a> dey clear to you. Initially we no go give you access to our partner server downloads, but if things go well, we fit share am with you. You dey run di Anna’s Archive open source codebase, and you dey regularly update both di code and di data. You dey willing to contribute to our <a %(a_codebase)s>codebase</a> — in collaboration with our team — to make dis happen. Wi de look for dis: Mirrors: call for volunteers Make another donation. No donations yet. <a %(a_donate)s>Make my first donation.</a> Donations details no dey publicly show. My donations 📡 For bulk mirroring of our collection, check out di <a %(a_datasets)s>Datasets</a> and <a %(a_torrents)s>Torrents</a> pages. Downloads from your IP address for di last 24 hours: %(count)s. 🚀 To get faster downloads and skip di browser checks, <a %(a_membership)s>become a member</a>. Download from partner website Feel free to continue browsing Anna’s Archive for different tab while you dey wait (if your browser fit refresh background tabs). Feel free to wait for multiple download pages to load at di same time (but abeg only download one file at di same time per server). Once you get a download link e go valid for several hours. Thanks for waiting, dis dey keep di website accessible for free for everybody! 😊 🔗 All download links for this file: <a %(a_main)s>File main page</a>. ❌ Slow downloads no dey available through Cloudflare VPNs or otherwise from Cloudflare IP addresses. ❌ Slow downloads dey only available through di official website. Visit %(websites)s. 📚 Use di following URL to download: <a %(a_download)s>Download now</a>. To giv evribodi chanse to download files for free, yu need to wait bifor yu fit download dis file. Abeg mek yu wait <span %(span_countdown)s>%(wait_seconds)s</span> sekond mek yu fit download dis file. Warning: plenty downloads don happen from your IP address for di last 24 hours. Downloads fit slow pass usual. If you dey use VPN, shared internet connection, or your ISP dey share IPs, dis warning fit be because of dat. Save ❌ Somtin go wrong. Abeg try again. ✅ E don save. Abeg reload di page. Change your display name. Your identifier (the part after “#”) no fit change. Profile don create <span %(span_time)s>%(time)s</span> edit Lists Create new list by finding file and opening di “Lists” tab. No lists yet Profile no dey. Profile For now, we no fit accommodate book requests. No email us your book requests. Abeg make your requests for Z-Library or Libgen forums. Rekɔd fɔ Anna’s Archive DOI: %(doi)s Download SciDB Nexus/STC No preview dey available yet. Download file from <a %(a_path)s>Anna’s Archive</a>. To support di accessibility an long-term preservation of human knowledge, become a <a %(a_donate)s>memba</a>. As a bonus, 🧬&nbsp;SciDB go load faster for membas, witout any limits. No dey work? Try <a %(a_refresh)s>refresh</a>. Sci-Hub Add speshial saach field Search descriptions and metadata comments Year wey dem publish am Advanced Access Content Show List Tébul Filetype Language Order by Largest Most relevant Newest (filesize) (open sourced) (publication year) Oldest Random Smallest Source wey AA scrape and open-source Digital Lending (%(count)s) Journal Articles (%(count)s) We don find matches for: %(in)s. You fit refer to di URL wey dey there when <a %(a_request)s>you dey request file</a>. Metadata (%(count)s) To explore di search index by codes, use di <a %(a_href)s>Codes Explorer</a>. The saach index dey update every month. E currently get entries reach %(last_data_refresh_date)s. For more technical information, see the %(link_open_tag)sdatasets page</a>. Exclude Include only Unchecked more… Nest … Previous This saach index currently get metadata from the Internet Archive’s Controlled Digital Lending library. <a %(a_datasets)s>More about our datasets</a>. For more digital lending libraries, see <a %(a_wikipedia)s>Wikipedia</a> and the <a %(a_mobileread)s>MobileRead Wiki</a>. For DMCA / copyright claims <a %(a_copyright)s>click here</a>. Download time Error during search. Try <a %(a_reload)s>reload di page</a>. If di problem still dey, abeg email us for %(email)s. Fast download As e be, anybody fit help preserve these files by seeding our <a %(a_torrents)s>unified list of torrents</a>. ➡️ Sometimes dis kin tin fit happen wrong when di search server slow. For dis kind cases, <a %(a_attrs)s>reloading</a> fit help. ❌ Dis file fit get wahala. You dey find papers? This saach index currently get metadata from various metadata sources. <a %(a_datasets)s>More about our datasets</a>. Plenti, plenti sources of metadata for written works dey around di world. <a %(a_wikipedia)s>Dis Wikipedia page</a> na good start, but if you sabi other good lists, abeg let us know. For metadata, we de show di original records. We no dey do any merging of records. We currently get the world’s most comprehensive open catalog of books, papers, and other written works. We dey mirror Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>and more</a>. <span class="font-bold">No files found.</span> Try fewer or different search terms and filters. Results %(from)s-%(to)s (%(total)s total) If you find other “shadow libraries” wey we suppose mirror, or if you get any questions, abeg contact us for %(email)s. %(num)d partial matches %(num)d+ partial matches Type for the box to saach for files for digital lending libraries. Type for the box to saach our catalog of %(count)s directly downloadable files, wey we <a %(a_preserve)s>preserve forever</a>. Type for di box to search. Type for the box to saach our catalog of %(count)s academic papers and journal articles, wey we <a %(a_preserve)s>preserve forever</a>. Type for the box to saach for metadata from libraries. This fit dey useful when <a %(a_request)s>requesting a file</a>. Tip: use keyboard shortcuts “/” (saach focus), “enter” (saach), “j” (up), “k” (down), “<” (prev page), “>” (next page) for quicker navigation. Dis na metadata records, <span %(classname)s>no be</span> downloadable files. Search settings Search Digital Lending Download Journal articles Metadata New search %(search_input)s - Search Di search take too long, wey mean say you fit see inaccurate results. Sometimes <a %(a_reload)s>reloading</a> di page dey help. Di search take too long, wey common for broad queries. Di filter counts fit no dey accurate. If you get large uploads (over 10,000 files) wey no dey accepted by Libgen or Z-Library, abeg contact us for %(a_email)s. For Libgen.li, make sure say you first login for <a %(a_forum)s >their forum</a> with username %(username)s and password %(password)s, and then return to their <a %(a_upload_page)s >upload page</a>. For now, we suggest make you upload new books to di Library Genesis forks. Here be <a %(a_guide)s>handy guide</a>. Note say both forks wey we dey index for dis website dey pull from dis same upload system. For small uploads (up to 10,000 files) abeg upload dem to both %(first)s and %(second)s. Alternatively, you fit upload them to Z-Library <a %(a_upload)s>here</a>. To upload academic papers, abeg also (in addition to Library Genesis) upload to <a %(a_stc_nexus)s>STC Nexus</a>. Dem be di best shadow library for new papers. We never integrate them yet, but we go do am at some point. You fit use their <a %(a_telegram)s>upload bot on Telegram</a>, or contact di address wey dem list for their pinned message if you get too many files to upload dis way. <span %(label)s>Heavy volunteering work (USD$50-USD$5,000 bounties):</span> if you fit get plenty time and/or resources to support our mission, we go like make you dey work close with us. Eventually, you fit join the inner team. Even though our budget tight, we fit give <span %(bold)s>💰 monetary bounties</span> for the most intense work. <span %(label)s>Light volunteering work:</span> if you fit only spare few hours here and there, plenty ways still dey wey you fit help out. We dey reward consistent volunteers with <span %(bold)s>🤝 memberships to Anna’s Archive</span>. Anna’s Archive dey rely on volunteers like you. We welcome all commitment levels, and we get two main categories of help we dey look for: If you no fit volunteer your time, you fit still help us well by <a %(a_donate)s>donating money</a>, <a %(a_torrents)s>seeding our torrents</a>, <a %(a_uploading)s>uploading books</a>, or <a %(a_help)s>telling your friends about Anna’s Archive</a>. <span %(bold)s>Companies:</span> we dey offer high-speed direct access to our collections in exchange for enterprise-level donation or exchange for new collections (e.g. new scans, OCR’ed datasets, enriching our data). <a %(a_contact)s>Contact us</a> if na you be this. See also our <a %(a_llm)s>LLM page</a>. Bountis Wi de always dey find people wey sabi programming well well or sabi security work to join us. You fit help preserve wetin humanity don do. As thank you, we go give membership for solid contributions. As big thank you, we go give money bountis for important and hard tasks. No see am as job replacement, but e fit be extra incentive and fit help with costs wey you go get. Most of our code na open source, and we go ask make your code be open source too when we dey award bounty. Some exceptions dey wey we fit discuss one-on-one. Bountis dey go to the first person wey complete task. Feel free to comment for bounty ticket make others know say you dey work on something, so others fit hold off or contact you to team up. But know say others still free to work on am too and try beat you to am. But we no go award bountis for sloppy work. If two high quality submissions dey close to each other (within a day or two), we fit choose to award bountis to both, at our discretion, for example 100%% for the first submission and 50%% for the second submission (so 150%% total). For the bigger bountis (especially scraping bountis), abeg contact us when you don complete ~5%% of am, and you sure say your method go scale to the full milestone. You go need share your method with us so we fit give feedback. Also, this way we fit decide wetin to do if multiple people dey close to bounty, like potentially awarding am to multiple people, encouraging people to team up, etc. WARNING: the high-bounty tasks dey <span %(bold)s>difficult</span> — e fit make sense to start with easier ones. Go to our <a %(a_gitlab)s>Gitlab issues list</a> and sort by “Label priority”. This go show roughly the order of tasks we care about. Tasks wey no get explicit bountis still fit get membership, especially those wey dem mark “Accepted” and “Anna’s favorite”. You fit wan start with “Starter project”. Light volunteering We don get Matrix chanel wey dey sync now for %(matrix)s. If you get few hours to spare, you fit help out in plenty ways. Make sure say you join the <a %(a_telegram)s>volunteers chat on Telegram</a>. As token of appreciation, we dey usually give out 6 months of “Lucky Librarian” for basic milestones, and more for continued volunteering work. All milestones require high quality work — sloppy work dey hurt us more than e go help and we go reject am. Abeg <a %(a_contact)s>email us</a> when you reach a milestone. %(links)s links or screenshots of requests wey you don fulfill. Fulfill book (or paper, etc) requests for the Z-Library or the Library Genesis forums. We no get our own book request system, but we dey mirror those libraries, so making them better dey make Anna’s Archive better too. Milestone Task Depends on the task. Small tasks wey we post for our <a %(a_telegram)s>volunteers chat on Telegram</a>. Usually for membership, sometimes for small bounties. Small tasks wey dem post for our volunteer chat group. Make sure say you leave comment on issues you fix, so others no go duplicate your work. %(links)s links of records wey you don improve. You fit use the <a %(a_list)s >list of random metadata issues</a> as starting point. Improve metadata by <a %(a_metadata)s>linking</a> with Open Library. Dis one suppose show say you dey tell person about Anna’s Archive, and dem dey thank you. %(links)s links or screenshots. To spread di word about Anna’s Archive. For example, by recommending books for AA, linking to our blog posts, or generally directing people to our website. Fully translate one language (if e no dey close to completion already.) <a %(a_translate)s>Translating</a> the website. Link to edit history wey show say you make significant contributions. Improve the Wikipedia page for Anna’s Archive for your language. Include information from AA’s Wikipedia page for other languages, and from our website and blog. Add references to AA for other relevant pages. Volunteering & Bounties 