��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b *  yd )  �f +   �g   �g   �h �  k Y  �l   n L   ,o f   yo E   �o b   &p �  �p O  +r �   {s �  gt �   'v �  w �  �x �  �z �   p| �  F} �  �~ 7   �� �   �� S   �� �  �    �� ,  ڃ E   � (   M�    v�    �� C   ��    �    � &   �    9� #   T�    x� 7   �� 6  �� 
  �� B   � 5   H�    ~� 
   ��    �� 
   ��    �� 
   ֊ 	   �    �    �� "   �    )�    /� 	   C� 
   M�    [�    g� &   ��    �� f  �� H  '� �   p� +   �� U  &� �   |�    � �   �    � �   ��    �� �   �� !   r� &  ��    �� y  ؖ -   R� R   ��    Ә 9  ܘ 4  � �  K�   �    � J   � Z   b� �   �� &   `� 0   �� x   �� :   1� H   l� /   ��    � q   � �  \� =   � r   F� �   �� >   h�   �� 0   ��   ڥ �   ܦ �   j� G   	� �   Q� �   � E   �� �   �� �   � [   �� A    � r   B� 
   �� �   ì &  Y� E   �� 
   Ʈ �   Ѯ �   f�    3� _  <� �   �� �   3� t  2� �   �� 6  ��   Ÿ B   ع :   � Z   V� i   �� ^   � V   z� 0   ѻ �   � R   �� :   ؼ 
   � f   � W   �� �   ݽ �   ��    !� �   1� �   $� G  � 9  e� �   �� �   (� %  � �   )� �   �� �   s� `  � �   d� �   � 
   �� �   �� �   �� �   V� g  � o   v� �   �� �   u� �  � t   ��   =�   F� "   L� W   o� �   �� H   [� j  �� �   � _   �� t   �    �� �  �� �   �� p  `�    �� d   �� �   N� �   �� �   �� �   �� �  �� �  =� $   �� P  �� p  N�   ��   ��   W� �   w� S   i� z   ��    8� �   @� @  �� p  =� �   �� �   �� �   z� 	   � N  � B  g� �   �� 	   �� �   �� &  7� �   ^� �  2� �   *� T   � 
   Y� j  g� N  �� 
   ! �   ,    !   �   * Y  
 \   g I   � �    4   	    A	   S	 
  r
   � +   � E   � �  
 �    �   � [    j  a     �    � x  	 �   � �   A F   � �   
 *   � �   � �   �    �    � (   � ;    9   ? 7   y K   �    �    } $   � =   � F   � ,   7 9   d :   � j   �    D    L   a   c �   l   E }   V �   � ?  �     �! �   �! �   �" �   5# S  �# g  % �  ~& �  V( m  �) E   Z+ 7   �+ &   �+ �   �+ �  �, �  �. )  )0 7  S1   �2   �3 "  �5 0   �6 	  �6 /  �7 Y  !9 
   {: �   �: +  ]; �   �< A  = �   [>    /? *   ;? `   f?    �? o  �? �  ?B ?  �C �   	F �   �F 
   aG �   oG �   LH E   I s   cI   �I �   �J y   �K q  XL \   �M   'N    >O �  OO �   CQ �  �Q [  �S �  6V �   �W 
   �X �   �X �  �Y    =[ U  N[ �  �\   o^   t` �   yb *  ic E   �d �  �d �   �f w   og 7   �g X   h    xh :   �h "   �h ,   �h "   i    /i �   Fi �  �i �  �l 4  �n �   �p 
   �q �  �q �  �t O  �v o  �w 8  _y G  �{ 
   �} �   �} 
   �~    �~ �  �~   �� �  �� (  }�    �� �  �� �  �� �  ^� �  2� �  � 2  � H  �    g� �   �� ^   4� �  �� �  e� �   � ~   �� �   (� S  כ    +� �   @� @   � J   %�    p� 7   �� 9   ��   �� �  � {  �� +   '� �  S� �   �    ~� �   �� 2   '� s   Z� �   Ω %   �� Z   ��    � X  � )  t� �  �� ^  .� �   ��    � i   !� 4  ��    �� �   ų   H� �   ȶ 4   j� 0   �� 1  з +   � (  .� q  W� �  ɼ    �� �   �� �   L� *  ο �  ��    z� �   �� +   c� �   �� �   0�   �� �   �� N   �� +   �� �   
� 5   �� ^  1� )  �� �   �� �   �� X   z� 3   �� �   � �  �� W  �� �  � �  �� 0   � �   ��   |� (  �� �   �� �  a� �  ?� +   �� �  "� "   �� �  � R   ��    2� �  K� �  H� q  E� �   ��   �� �  ��   �� �   �� �  m� �  #� �   �� �  >� 9  � :   T� ~   ��    �    ��    �� (  �� �   �� 9   ��   �� �  ��   �� �  �� |  v  �   � �  �   3    @ �   `     j   %   {    �    � 
   � #   �    �    
        1    9    K    S    Z    a     i    �    �    �    �    �    � �   � Q   �	    �	 
   
 "   
 '   :
 /   b
 -   �
    �
    �
 
   �
    �
    	        %    8    @    S    Y )   j    �    � )   �     � .    &   G    n    � 0   �    � N   � 8   3
    l
 d   r
 <   �
        0    B    b    t    �    �    �    �    �    �    � 
   � 	   
 
       "    %    C    J 	   S    ] 	   v    �    �    � 	   �    �    �    �    �    �    
    +    3 	   G    Q &   d    � 	   � #   �    �    �    �    �    � 
   �     [    �   y N    �   b �   t   �    P    \    t    �    �    �    �    � S   � 4   2 ^   g %   � 1   � )    h   H ^   � �    W   � <   I    �    �    � 	   �    �    �    �    �    �                #    '    ,    >    G    \    l    } 	   � 	   �    �    �    � 	  �    �    �    �    �     @    Y   X %   � &   � .   �    .    6    > m   A    �    � *   � k   �    X    k o   w %   � <  
 `   J" ~   �" �   *# �   �# 5   �$ �   �$ �   �% '  C& �   k'    j(    �( 4   �( ?   �( P    ) ^   Q) S   �) C   * g   H* "   �* ,   �*     +    + N   + #   d+    �+    �+    �+    �+ k   �+    1, 7   B, F   z,    �,    �, S   �, a   @- y  �-    /    1/ �   G/    �/    �/    �/    0    0 ,   $0 =  Q0 !   �1    �1 
   �1    �1   �1    �2    3    3 }   3    �3    �3 *   �3    �3    �3 %   �3 �   4    �4 
   �4 O   �4    5    05    A5    F5 *   X5 M   �5 
   �5 <   �5 �   6 Q   �6 H   
7    S7 �   b7 A   W8    �8 .   �8    �8 �   �8 �   �9    H: D   ]: �   �: �   C;    < !   '< 
   I< r  W< "   �= !   �=    > "   ->    P> �   n>    ?    ? *   5? <   `? 	   �?    �?    �? #   �? :  @ k  CB �  �C 3   �E 3   �E    F    F   6F �   QG �   H    �H �   �H �  �I    ,K    JK �  gK   =M    ZN 
   cN 0   qN 	   �N    �N    �O )  �O �  Q �   �R    �S r   �S /    T $   PT x   uT   �T   
V �   %W �  �W w   JY �   �Y P   �Z �   [ �   �[ H   `\ �   �\ 3   \] +   �]    �]    �]    �] !   �]    ^ N   !^ )   p^ 	   �^ $   �^   �^ %   �_ �   ` ~   �` �   _a +   �a %   b    :b    Tb "   gb    �b "   �b (   �b �   �b    �c �   �c    �d �   �d u   �e �   �e 0  ~f �   �g *   uh �   �h 	   )i �   3i ]   1j    �j �  �j    :l    Gl    ^l    ml )   �l 
   �l    �l E   �l �   m �   �m    �n    �n    �n �   �n   fo �   |p n   $q    �q    �q    �q    �q    �q    r    r @   r (   [r �  �r W   Lt    �t ]   �t i   u P   }u b   �u K   1v P   }v    �v _   �v G   4w �   |w J   �w J   Hx    �x �   �x Y   �y =   �y k   ,z V   �z 7   �z    '{ 5   0{ m   f{ �   �{ 2   m| �   �|    C}   I} A   L~ ]   �~ �   �~    � �  � �   X�    #�    5�    N�    R� �   W� *   J� i   u� �   ߃ �   �� �   h� �   �� �   �� �   O� �   � [   ��   �� d   � J  w� �    �   j� 
   M� 
   [� 
   i� �   w� 
   � 
   � C   #� R   g� �   �� 
   �� c   �� 5   �� w   2� :   �� r   � ^   X� 
   �� �   ő 
   G� 
   U� 
   c�   q� �   s� �  ��    ��    ��    �� �   ��    d�    �� �   �� �   ��     )�    J�    Z� /   s� 2   �� -   ֙    �     � �   <�    #� �  A� �   ڜ �   �� L   G� �   ��   ,� �  A�   � �   � ~   �� v   0�    �� ]  ��    � a  ;� �   �� 
  �� �   ��   � �   .� �   ܬ {   c� *   ߭ 5   
�    @� -   W� 
   �� 
   ��    �� �   ��    �� c   �� +   �    7�    =�    E�    K� q   i� B   ۰ 5   �   T� +   \�    ��    ��    ��     ��    Ҳ    �    �    �� 
   �    � 
   �    '� $   3� �   X�    ��    �    �    #�    3� 
   B�    P� 
   _�    m�    �� %   �� ~   ��    8� 2   G� \   z� �   ׵ �   �� �   L�   � �   � .  ��     ź �   � 2   i� =   �� L   ڻ �   '� �   ¼ @   �� `   �    I� e   \� X   ¾ v   �    ��     ��    ��    ʿ    ޿    �    ��     �    &�    /�    A�    U�    q�    ��    ��    ��    ��    ��    ��    ��    �    �    +� |   I� �   ��    G� &   d� j   �� Y   �� r   P� '   �� 8   �� .   $� 9   S� >   �� )   �� �   ��   �� 
  ��    �� D   �� �   %� $   �� �   �� �   �� u   �� 2   �    8� �   Q�    �� [   n� '   �� U   �� �   H�    '� #   9�    ]� @   p� e   �� d   �    |�    ��    �� "   �� �   �� A   P� !   �� 2   �� &   ��    � !   .� B   P�    �� b   �� <   � �   K� J   F� C   �� �   �� >   ��    ��    ��    �    "� #   A�    e� !   �� :   �� 3   �� O  � 4   e� 6   �� D   �� *   �    A� j   G� �   �� �   7� �   �� 	   �� }   �� !   %�    G� �   W�    � $   $� 5   I� /   � S   �� J   � K   N�    ��     �� �   �� -   n� !   �� P   �� �   � 8   �� �   6� ]   �� Y   � q   v� %   �� ,   � q   ;�    �� [   ��    )� �   F� 3   �� 9   *�    d� ,   �� �   �� 4   o� M   �� }   �� Z   p� K   �� �   � �   ��    �    &� L   ;� 4   ��    �� #   ��    ��    �    � -   *� �   X� k   ��    U�    o�    ��     �� "   �� t   �� 3   ^� �   �� Z   �     p� i   �� �   �� .   �� X   �    ^� D   w� A   ��    �� y   � w   �� +   	� A   5�    w� .   �� X   ��    � a   *� )   ��    �� '   �� ]   �� �   R� B   �    \�    s� B   ��    �� S   �� =   8� �   v� j   �    �� =   �� �   ��    |� �   �� A   1� !   s� O   �� �   ��    ��    ��    ��    �� )   �� -   � T   M�    �� 
   ��    �� #   �� 8   �� 9   ,� 	   f� E   p� >   ��    ��    � 0   !�    R�    g� Y   w� �   �� H   ��    �� 
   � �   � 0  �� J   ��    9  Q  J  �  � 0    X   O    � 4  � /   � `   )    � 
   � �  �    v N   � t   � ^   P	 R   �	    
 a   
 8   z
    �
 {   �
 @   J -   � W   � *    S   < �   � �   
 ,   �
 �   �
 T  � �   � )   � H  � �    �   �   � �   � ,   }    � �   � +   [ �  � s   l ?   �         , �  B    � �   � &  �    � +  � G    D   Y R   � 0   � #   "  R   F  Z   �     �  "   ! :   $!    _!    }! +   �! Q   �! +   "    ;" ^   C" �   �" ~   }# 
   �# 
   
$    $ N   $ V   l$ I   �$ �   
% X   �%    A& &   S& �   z&    B' �   K' �  �' �   �* P   x+ .   �+    �+    �+    , B   , 1   K, �   }, �   - N   �-    .    .. %   A.    g. Y   �.    �. :   �.    (/ "   ./ $   Q/    v/    �/ Y   �/    �/    �/ "   0    &0    @0 Z   D0 �   �0 N   a1 e   �1 L   2 �   c2    3    !3 �   :3 �   �3 �   �4    g5 q   p5 D   �5 B   '6 `   j6 k   �6 M   77    �7 W   �7    �7    8    8    08    C8    V8    h8    z8 
   �8    �8 *   �8 *   �8 *   �8    '9 +   A9 $   m9    �9    �9    �9 C   �9    :    3: <   ;: *   x: T   �: *   �:    #;    9;     M;    n;    �; k   �; T   < j   `< �   �<    �=    �=    �=    �= 6   	> 	   @>    J>    f> w   y>    �>    ?    +? 
   2? 	   =? 5   G?    }?   �?    �@    �@    �@ '   �@    �@    A    2A    PA #   pA *   �A (   �A    �A Q   �A     AB    bB    yB A   �B H   �B    C (   3C     \C h   }C U   �C J   <D    �D    �D 	   �D    �D    �D    �D �  �D v   �F    G    G '   !G    IG $   ZG    G 	   �G v   �G 8   H �   >H    
I    I x   <I    �I    �I #   �I &   J 0   >J #   oJ    �J    �J 3   �J    �J !   K 6   )K   `K @   yL >   �L I   �L    CM    `M    �M    �M P   N    YN &   lN    �N E   �N @   �N     2O �   SO    �O    P    P    0P    AP    YP    rP    �P    �P T   �P �   Q    �Q "   R �   7R �   �R    �S    �S    �S    �S    �S     T    T    ,T    BT    TT    gT    vT 
   �T    �T    �T    �T    �T    �T #   �T �   U �   �U k  �V 
  X �  )Z �   �[ a  �\    �] �   ^    �^ �   	_ �   �_ t  �` �   ;b A  �b 5   >d �   td =   e    Ue ;   pe ?   �e h   �e r   Uf �   �f �   �g �   <h B  �h    =k �   Rk �    l e   �l �   .m    �m �   �m a  �n �   1p �   �p    �q ^   �q [   !r �   }r �   ws e   �s z   _t    �t !   �t    u E   'u 7   mu    �u y   �u >   /v i   nv    �v �   �v |   �w >   x P   Gx P   �x h   �x b   Ry M   �y h   z f   lz |   �z n   P{    �{ "   �{ $   �{ G   
| 0   U|    �|    �| D   �|    �|    �|    } 9   
} *   G} J   r}    �}    �}    �}    �} 	   �} a   �} s   X~ T   �~ ;   !    ]    e +   � 
   �    �    �    �    �    �    �    �    �    �    � 
   �    �    #�    3�    E�    U� 	   \�    f�    l� !   r�    ��    �� t   π    D� Q   Y� �   �� 	   g�    q�    ~�    ��    ��    ��    �� �   �� v   A� =   ��    ��    � w   �    �� �   ��    !� !   ��    Å ~   ܅ �   [� S   1� �   �� _   >� -   �� �   ̈    U�    k� F   �� �   ɉ    U� �   r� ~   � �   �� Q   ,�    ~�    ��    ��    ��    ��    �� 	   ʌ    Ԍ �   � b   }� �   �� �   e� �   6� e   � G   �� �  ɐ f  �� �   �� �   �   x� L  |�    ɗ �   җ   �� �   �� j  C� �  �� ~   o� C  �    2� =   H� �   �� L  � D   e� �   ��    ��    ��    �� �   �� 3   I� k   }� -   � ^   � F   v� I   �� $   � �   ,� Q   �� ,   � K   ?� �   ��    `�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: nb_NO
Language-Team: nb_NO <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library er et populært (og ulovlig) bibliotek. De har tatt Library Genesis-samlingen og gjort den lett søkbar. I tillegg har de blitt svært effektive til å be om nye bokbidrag, ved å gi insentiver til bidragsytere med ulike fordeler. De bidrar for tiden ikke med disse nye bøkene tilbake til Library Genesis. Og i motsetning til Library Genesis, gjør de ikke samlingen sin lett speilbar, noe som hindrer bred bevaring. Dette er viktig for deres forretningsmodell, siden de tar betalt for tilgang til samlingen i bulk (mer enn 10 bøker per dag). Vi gjør ikke moralske vurderinger om å ta betalt for bulktilgang til en ulovlig boksamling. Det er uten tvil at Z-Library har vært vellykket i å utvide tilgangen til kunnskap og skaffe flere bøker. Vi er her for å gjøre vår del: sikre den langsiktige bevaringen av denne private samlingen. - Anna og teamet (<a %(reddit)s>Reddit</a>) I den opprinnelige utgivelsen av Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), laget vi et speil av Z-Library, en stor ulovlig boksamling. Som en påminnelse, dette er hva vi skrev i det opprinnelige blogginnlegget: Den samlingen daterte seg tilbake til midten av 2021. I mellomtiden har Z-Library vokst i et forbløffende tempo: de har lagt til omtrent 3,8 millioner nye bøker. Det er noen duplikater der, selvfølgelig, men det meste ser ut til å være legitime nye bøker, eller høyere kvalitetsskanninger av tidligere innsendte bøker. Dette er i stor grad på grunn av det økte antallet frivillige moderatorer på Z-Library, og deres bulk-opplastingssystem med deduplisering. Vi vil gratulere dem med disse prestasjonene. Vi er glade for å kunngjøre at vi har fått alle bøkene som ble lagt til Z-Library mellom vår siste speiling og august 2022. Vi har også gått tilbake og skrapet noen bøker som vi gikk glipp av første gang. Alt i alt er denne nye samlingen omtrent 24TB, som er mye større enn den forrige (7TB). Vårt speil er nå totalt 31TB. Igjen, vi dedupliserte mot Library Genesis, siden det allerede er tilgjengelige torrenter for den samlingen. Vennligst gå til Pirate Library Mirror for å sjekke ut den nye samlingen (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Det er mer informasjon der om hvordan filene er strukturert, og hva som har endret seg siden sist. Vi vil ikke lenke til det herfra, siden dette bare er en bloggside som ikke hoster ulovlige materialer. Selvfølgelig er seeding også en flott måte å hjelpe oss på. Takk til alle som seeder vårt forrige sett med torrenter. Vi er takknemlige for den positive responsen, og glade for at det er så mange som bryr seg om bevaring av kunnskap og kultur på denne uvanlige måten. 3x nye bøker lagt til i Pirate Library Mirror (+24TB, 3,8 millioner bøker) Les følgesartiklene av TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>andre</a> - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ledsagerartikler av TorrentFreak: <a %(torrentfreak)s>første</a>, <a %(torrentfreak_2)s>andre</a> For ikke så lenge siden var "skyggebiblioteker" i ferd med å dø ut. Sci-Hub, det massive ulovlige arkivet av akademiske artikler, hadde sluttet å ta inn nye verk på grunn av søksmål. "Z-Library", det største ulovlige biblioteket av bøker, så sine angivelige skapere arrestert på grunn av brudd på opphavsretten. De klarte utrolig nok å unnslippe arrestasjonen, men biblioteket deres er ikke mindre truet. Noen land gjør allerede en versjon av dette. TorrentFreak <a %(torrentfreak)s>rapporterte</a> at Kina og Japan har innført AI-unntak i sine opphavsrettslover. Det er uklart for oss hvordan dette samhandler med internasjonale traktater, men det gir absolutt dekning til deres innenlandske selskaper, noe som forklarer det vi har sett. Når det gjelder Annas Arkiv — vi vil fortsette vårt undergrunnsarbeid forankret i moralsk overbevisning. Likevel er vårt største ønske å komme frem i lyset og forsterke vår innvirkning lovlig. Vennligst reformer opphavsretten. Da Z-Library sto overfor nedleggelse, hadde jeg allerede sikkerhetskopiert hele biblioteket og lette etter en plattform for å huse det. Det var min motivasjon for å starte Annas Arkiv: en fortsettelse av oppdraget bak de tidligere initiativene. Vi har siden vokst til å bli det største skyggebiblioteket i verden, med over 140 millioner opphavsrettsbeskyttede tekster i ulike formater — bøker, akademiske artikler, magasiner, aviser og mer. Mitt team og jeg er ideologer. Vi mener at det er moralsk riktig å bevare og huse disse filene. Biblioteker over hele verden opplever kutt i finansieringen, og vi kan heller ikke stole på at menneskehetens arv blir ivaretatt av selskaper. Så kom AI. Nesten alle store selskaper som bygger LLM-er kontaktet oss for å trene på våre data. De fleste (men ikke alle!) amerikanske selskaper ombestemte seg når de innså den ulovlige naturen av vårt arbeid. Derimot har kinesiske firmaer entusiastisk omfavnet vår samling, tilsynelatende uforstyrret av dens lovlighet. Dette er bemerkelsesverdig gitt Kinas rolle som signatar til nesten alle store internasjonale opphavsrettstraktater. Vi har gitt høyhastighetstilgang til omtrent 30 selskaper. De fleste av dem er LLM-selskaper, og noen er datameglere, som vil videreselge vår samling. De fleste er kinesiske, selv om vi også har jobbet med selskaper fra USA, Europa, Russland, Sør-Korea og Japan. DeepSeek <a %(arxiv)s>innrømmet</a> at en tidligere versjon ble trent på deler av vår samling, selv om de er tause om deres nyeste modell (sannsynligvis også trent på våre data). Hvis Vesten vil holde seg foran i kappløpet om LLM-er, og til slutt, AGI, må den revurdere sin posisjon på opphavsrett, og snart. Enten du er enig med oss eller ikke i vår moralske sak, blir dette nå en sak om økonomi, og til og med nasjonal sikkerhet. Alle maktblokker bygger kunstige supervitenskapsmenn, superhackere og supermilitærer. Informasjonsfrihet blir et spørsmål om overlevelse for disse landene — til og med et spørsmål om nasjonal sikkerhet. Vårt team er fra hele verden, og vi har ingen spesiell tilknytning. Men vi vil oppfordre land med sterke opphavsrettslover til å bruke denne eksistensielle trusselen til å reformere dem. Så hva skal vi gjøre? Vår første anbefaling er enkel: forkorte opphavsrettsperioden. I USA gis opphavsrett i 70 år etter forfatterens død. Dette er absurd. Vi kan bringe dette i tråd med patenter, som gis i 20 år etter innlevering. Dette bør være mer enn nok tid for forfattere av bøker, artikler, musikk, kunst og andre kreative verk, til å bli fullt kompensert for sine anstrengelser (inkludert langsiktige prosjekter som filmatiseringer). Deretter bør beslutningstakere som et minimum inkludere unntak for massebevaring og spredning av tekster. Hvis tapt inntekt fra individuelle kunder er hovedbekymringen, kan distribusjon på personlig nivå forbli forbudt. Til gjengjeld vil de som er i stand til å administrere store arkiver — selskaper som trener LLM-er, sammen med biblioteker og andre arkiver — være dekket av disse unntakene. Opphavsrettsreform er nødvendig for nasjonal sikkerhet TL;DR: Kinesiske LLM-er (inkludert DeepSeek) er trent på mitt ulovlige arkiv av bøker og artikler — det største i verden. Vesten må overhale opphavsrettslovgivningen som et spørsmål om nasjonal sikkerhet. Vennligst se <a %(all_isbns)s>den opprinnelige bloggposten</a> for mer informasjon. Vi utfordret til å forbedre dette. Vi ville gi en førstepremie på $6,000, andreplass på $3,000, og tredjeplass på $1,000. På grunn av den overveldende responsen og de utrolige bidragene, har vi besluttet å øke premiepotten litt, og gi en delt tredjeplass til fire vinnere på $500 hver. Vinnerne er nedenfor, men sørg for å se alle bidragene <a %(annas_archive)s>her</a>, eller last ned vår <a %(a_2025_01_isbn_visualization_files)s>kombinerte torrent</a>. Førsteplass $6,000: phiresky Denne <a %(phiresky_github)s>innsendelsen</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) er rett og slett alt vi ønsket, og mer! Vi likte spesielt de utrolig fleksible visualiseringsalternativene (til og med støtte for tilpassede shaders), men med en omfattende liste over forhåndsinnstillinger. Vi likte også hvor raskt og smidig alt er, den enkle implementeringen (som ikke engang har en backend), den smarte minikartet, og den omfattende forklaringen i deres <a %(phiresky_github)s>bloggpost</a>. Utrolig arbeid, og en velfortjent vinner! - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Våre hjerter er fulle av takknemlighet. Merkbare ideer Skyskrapere for sjeldenhet Mange skyveknapper for å sammenligne datasets, som om du er en DJ. Skalastrek med antall bøker. Fine etiketter. Kul standard fargepalett og varmekart. Unik kartvisning og filtre Merknader, og også live-statistikk Live-statistikk Noen flere ideer og implementeringer vi spesielt likte: Vi kunne fortsette en stund, men la oss stoppe her. Sørg for å se på alle innsendelser <a %(annas_archive)s>her</a>, eller last ned vår <a %(a_2025_01_isbn_visualization_files)s>kombinerte torrent</a>. Så mange innsendelser, og hver gir et unikt perspektiv, enten i brukergrensesnitt eller implementering. Vi vil i det minste innlemme førsteplassinnsendelsen på vår hovedside, og kanskje noen andre. Vi har også begynt å tenke på hvordan vi kan organisere prosessen med å identifisere, bekrefte og deretter arkivere de sjeldneste bøkene. Mer kommer på denne fronten. Takk til alle som deltok. Det er fantastisk at så mange bryr seg. Enkel veksling av datasets for raske sammenligninger. Alle ISBN-er CADAL SSNO-er CERLALC datalekkasje DuXiu SSID-er EBSCOhost’s eBook Index Google Bøker Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Filer i Annas Arkiv Nexus/STC OCLC/Worldcat OpenLibrary Russlands statsbibliotek Det keiserlige biblioteket på Trantor Andreplass $3,000: hypha «Selv om perfekte kvadrater og rektangler er matematisk tiltalende, gir de ikke overlegen lokalitet i en kartleggingskontekst. Jeg mener at asymmetrien som er iboende i disse Hilbert- eller klassiske Morton-kurvene ikke er en feil, men en funksjon. Akkurat som Italias berømte støvleformede omriss gjør det umiddelbart gjenkjennelig på et kart, kan de unike "særtrekkene" ved disse kurvene tjene som kognitive landemerker. Denne særpreget kan forbedre romlig hukommelse og hjelpe brukere med å orientere seg, potensielt gjøre det lettere å lokalisere spesifikke regioner eller legge merke til mønstre.» Et annet utrolig <a %(annas_archive_note_2913)s>bidrag</a>. Ikke like fleksibelt som førsteplassen, men vi foretrakk faktisk dens makronivåvisualisering over førsteplassen (romfyllende kurve, grenser, merking, fremheving, panorering og zooming). En <a %(annas_archive_note_2971)s>kommentar</a> av Joe Davis resonerte med oss: Og fortsatt mange alternativer for visualisering og gjengivelse, samt en utrolig jevn og intuitiv brukergrensesnitt. En solid andreplass! - Anna og teamet (<a %(reddit)s>Reddit</a>) For noen måneder siden annonserte vi en <a %(all_isbns)s>$10,000 premie</a> for å lage den best mulige visualiseringen av våre data som viser ISBN-området. Vi la vekt på å vise hvilke filer vi allerede har/ikke har arkivert, og vi la senere til et datasett som beskriver hvor mange biblioteker som har ISBN-er (et mål på sjeldenhet). Vi har blitt overveldet av responsen. Det har vært så mye kreativitet. En stor takk til alle som har deltatt: deres energi og entusiasme er smittsom! Til slutt ønsket vi å svare på følgende spørsmål: <strong>hvilke bøker finnes i verden, hvor mange har vi allerede arkivert, og hvilke bøker bør vi fokusere på neste?</strong> Det er flott å se så mange mennesker bry seg om disse spørsmålene. Vi startet med en grunnleggende visualisering selv. På mindre enn 300kb representerer dette bildet kortfattet den største fullt åpne “listen over bøker” noensinne samlet i menneskehetens historie: Tredjeplass $500 #1: maxlion I denne <a %(annas_archive_note_2940)s>innsendelsen</a> likte vi virkelig de forskjellige typene visninger, spesielt sammenlignings- og utgivervisningene. Tredjeplass $500 #2: abetusk Selv om det ikke er det mest polerte brukergrensesnittet, oppfyller denne <a %(annas_archive_note_2917)s>innsendelsen</a> mange av kriteriene. Vi likte spesielt sammenligningsfunksjonen. Tredjeplass $500 #3: conundrumer0 Som førsteplassen, imponerte denne <a %(annas_archive_note_2975)s>innsendelsen</a> oss med sin fleksibilitet. Til syvende og sist er det dette som gjør et flott visualiseringsverktøy: maksimal fleksibilitet for avanserte brukere, samtidig som det holder ting enkelt for gjennomsnittsbrukere. Tredjeplass $500 #4: charelf Den siste <a %(annas_archive_note_2947)s>innsendelsen</a> som får en premie er ganske grunnleggende, men har noen unike funksjoner som vi virkelig likte. Vi likte hvordan de viser hvor mange Datasets som dekker en bestemt ISBN som et mål på popularitet/pålitelighet. Vi likte også virkelig enkelheten, men effektiviteten av å bruke en opasitetsglider for sammenligninger. Vinnere av $10,000 ISBN-visualiseringspremien Kort fortalt: Vi fikk noen utrolige bidrag til $10,000 ISBN-visualiseringspremien. Bakgrunn Hvordan kan Annas Arkiv oppnå sitt mål om å sikkerhetskopiere all menneskehetens kunnskap, uten å vite hvilke bøker som fortsatt finnes der ute? Vi trenger en TODO-liste. En måte å kartlegge dette på er gjennom ISBN-numre, som siden 1970-tallet har blitt tildelt hver bok som er utgitt (i de fleste land). Det finnes ingen sentral myndighet som kjenner til alle ISBN-tildelinger. I stedet er det et distribuert system, der land får tildelt nummerområder, som deretter tildeler mindre områder til store forlag, som igjen kan underdele områder til mindre forlag. Til slutt tildeles individuelle numre til bøker. Vi begynte å kartlegge ISBN-er <a %(blog)s>for to år siden</a> med vår skraping av ISBNdb. Siden da har vi skrapet mange flere metadata-kilder, som <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby og flere. En fullstendig liste kan finnes på sidene "Datasets" og "Torrents" på Annas Arkiv. Vi har nå den desidert største fullt åpne, lett nedlastbare samlingen av bokmetadata (og dermed ISBN-er) i verden. Vi har <a %(blog)s>skrevet omfattende</a> om hvorfor vi bryr oss om bevaring, og hvorfor vi for øyeblikket er i et kritisk vindu. Vi må nå identifisere sjeldne, underfokuserte og unikt truede bøker og bevare dem. Å ha god metadata på alle bøker i verden hjelper med det. $10,000 dusør Sterk vurdering vil bli gitt til brukervennlighet og hvor godt det ser ut. Vis faktisk metadata for individuelle ISBN-er når du zoomer inn, som tittel og forfatter. Bedre plassfyllingskurve. F.eks. en sikksakk, som går fra 0 til 4 på første rad og deretter tilbake (i revers) fra 5 til 9 på andre rad — anvendt rekursivt. Ulike eller tilpassbare fargeskjemaer. Spesielle visninger for å sammenligne datasets. Måter å feilsøke problemer på, som annen metadata som ikke stemmer godt overens (f.eks. svært forskjellige titler). Annotere bilder med kommentarer om ISBN-er eller områder. Eventuelle heuristikker for å identifisere sjeldne eller truede bøker. Hvilke kreative ideer du enn kan komme opp med! Kode Koden for å generere disse bildene, samt andre eksempler, kan finnes i <a %(annas_archive)s>denne katalogen</a>. Vi kom opp med et kompakt dataformat, hvor all nødvendig ISBN-informasjon er omtrent 75MB (komprimert). Beskrivelsen av dataformatet og koden for å generere det kan finnes <a %(annas_archive_l1244_1319)s>her</a>. For belønningen er du ikke pålagt å bruke dette, men det er sannsynligvis det mest praktiske formatet å starte med. Du kan transformere vår metadata slik du vil (selv om all koden din må være åpen kilde). Vi kan ikke vente med å se hva du kommer opp med. Lykke til! Fork dette repoet, og rediger dette blogginnleggets HTML (ingen andre backends enn vår Flask-backend er tillatt). Gjør bildet ovenfor jevnt zoombart, slik at du kan zoome helt inn til individuelle ISBN-er. Klikking på ISBN-er bør ta deg til en metadata-side eller søk på Annas Arkiv. Du må fortsatt kunne bytte mellom alle forskjellige datasets. Landsområder og forlagsområder bør fremheves ved hover. Du kan bruke f.eks. <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> for landinfo, og vår "isbngrp" skraping for forlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Det må fungere godt på både desktop og mobil. Det er mye å utforske her, så vi kunngjør en dusør for å forbedre visualiseringen ovenfor. I motsetning til de fleste av våre dusører, er denne tidsbegrenset. Du må <a %(annas_archive)s>sende inn</a> din åpen kildekode innen 2025-01-31 (23:59 UTC). Den beste innsendelsen vil få $6,000, andreplass er $3,000, og tredjeplass er $1,000. Alle dusører vil bli utdelt ved bruk av Monero (XMR). Nedenfor er de minimale kriteriene. Hvis ingen innsendelse oppfyller kriteriene, kan vi fortsatt tildele noen dusører, men det vil være etter vårt skjønn. For bonuspoeng (dette er bare ideer — la kreativiteten løpe løpsk): Du KAN fullstendig avvike fra de minimale kriteriene, og lage en helt annen visualisering. Hvis den er virkelig spektakulær, kvalifiserer den for belønningen, men etter vårt skjønn. Gjør innleveringer ved å legge inn en kommentar til <a %(annas_archive)s>denne saken</a> med en lenke til ditt forgrenede repo, forespørsel om sammenslåing, eller diff. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dette bildet er 1000×800 piksler. Hver piksel representerer 2,500 ISBN-er. Hvis vi har en fil for en ISBN, gjør vi den pikselen mer grønn. Hvis vi vet at en ISBN er utstedt, men vi ikke har en matchende fil, gjør vi den mer rød. På mindre enn 300kb representerer dette bildet kortfattet den største fullt åpne "boklisten" som noensinne er samlet i menneskehetens historie (noen hundre GB komprimert i sin helhet). Det viser også: det er mye arbeid igjen med å sikkerhetskopiere bøker (vi har bare 16%). Visualisering av alle ISBN-er — $10,000 premie innen 2025-01-31 Dette bildet representerer den største fullt åpne "boklisten" som noensinne er samlet i menneskehetens historie. Visualisering I tillegg til oversiktsbildet kan vi også se på individuelle datasets vi har anskaffet. Bruk rullegardinmenyen og knappene for å bytte mellom dem. Det er mange interessante mønstre å se i disse bildene. Hvorfor er det en viss regelmessighet av linjer og blokker, som ser ut til å skje i forskjellige skalaer? Hva er de tomme områdene? Hvorfor er visse datasets så klumpet sammen? Vi lar disse spørsmålene være en øvelse for leseren. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklusjon Med denne standarden kan vi gjøre utgivelser mer trinnvis, og lettere legge til nye datakilder. Vi har allerede noen spennende utgivelser på gang! Vi håper også at det blir enklere for andre skyggebiblioteker å speile våre samlinger. Tross alt er målet vårt å bevare menneskelig kunnskap og kultur for alltid, så jo mer redundans, desto bedre. Eksempel La oss se på vår nylige Z-Library-utgivelse som et eksempel. Den består av to samlinger: “<span style="background: #fffaa3">zlib3_records</span>” og “<span style="background: #ffd6fe">zlib3_files</span>”. Dette lar oss skrape og utgi metadataoppføringer separat fra de faktiske bokfilene. Som sådan utga vi to torrenter med metadatafiler: Vi utga også en rekke torrenter med binære datafoldere, men kun for “<span style="background: #ffd6fe">zlib3_files</span>”-samlingen, totalt 62: Ved å kjøre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se hva som er inni: I dette tilfellet er det metadata for en bok som rapportert av Z-Library. På toppnivå har vi kun “aacid” og “metadata”, men ingen “data_folder”, siden det ikke er noen tilsvarende binære data. AACID inneholder “22430000” som primær-ID, som vi kan se er hentet fra “zlibrary_id”. Vi kan forvente at andre AAC-er i denne samlingen har samme struktur. La oss nå kjøre <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Dette er en mye mindre AAC-metadata, selv om hoveddelen av denne AAC er plassert et annet sted i en binær fil! Tross alt har vi en “data_folder” denne gangen, så vi kan forvente at den tilsvarende binære dataen er plassert på <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” inneholder “zlibrary_id”, så vi kan enkelt knytte det til den tilsvarende AAC i “zlib_records”-samlingen. Vi kunne ha assosiert på en rekke forskjellige måter, f.eks. gjennom AACID — standarden foreskriver ikke det. Merk at det heller ikke er nødvendig for “metadata”-feltet å være JSON. Det kan være en streng som inneholder XML eller et hvilket som helst annet dataformat. Du kan til og med lagre metadata-informasjon i den tilknyttede binære bloben, f.eks. hvis det er mye data. Heterogene filer og metadata, så nær originalformatet som mulig. Binære data kan serveres direkte av webservere som Nginx. Heterogene identifikatorer i kildesamlingene, eller til og med mangel på identifikatorer. Separate utgivelser av metadata vs fildata, eller kun metadata-utgivelser (f.eks. vår ISBNdb-utgivelse). Distribusjon gjennom torrenter, men med mulighet for andre distribusjonsmetoder (f.eks. IPFS). Uforanderlige oppføringer, siden vi bør anta at våre torrenter vil leve for alltid. Inkrementelle utgivelser / utvidbare utgivelser. Maskinlesbare og skrivbare, praktisk og raskt, spesielt for vår stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Noe enkel menneskelig inspeksjon, selv om dette er sekundært til maskinlesbarhet. Enkel å så våre samlinger med en standard leid seedbox. Designmål Vi bryr oss ikke om at filer er enkle å navigere manuelt på disk, eller søkbare uten forbehandling. Vi bryr oss ikke om å være direkte kompatible med eksisterende biblioteksprogramvare. Selv om det skal være enkelt for alle å så vår samling ved hjelp av torrenter, forventer vi ikke at filene skal være brukbare uten betydelig teknisk kunnskap og engasjement. Vår primære brukstilfelle er distribusjon av filer og tilhørende metadata fra forskjellige eksisterende samlinger. Våre viktigste hensyn er: Noen ikke-mål: Siden Annas Arkiv er åpen kildekode, ønsker vi å bruke vårt eget format direkte. Når vi oppdaterer vår søkeindeks, har vi kun tilgang til offentlig tilgjengelige stier, slik at alle som forgrener vårt bibliotek kan komme raskt i gang. <strong>AAC.</strong> AAC (Annas Arkiv Beholder) er en enkelt enhet bestående av <strong>metadata</strong>, og eventuelt <strong>binære data</strong>, som begge er uforanderlige. Den har en globalt unik identifikator, kalt <strong>AACID</strong>. <strong>AACID.</strong> Formatet til AACID er slik: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. For eksempel, en faktisk AACID vi har utgitt er <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-område.</strong> Siden AACID-er inneholder monotont økende tidsstempler, kan vi bruke det til å angi områder innenfor en bestemt samling. Vi bruker dette formatet: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, hvor tidsstemplene er inkludert. Dette er i samsvar med ISO 8601-notasjon. Områder er kontinuerlige, og kan overlappe, men i tilfelle av overlapping må de inneholde identiske poster som den som tidligere ble utgitt i den samlingen (siden AAC-er er uforanderlige). Manglende poster er ikke tillatt. <code>{collection}</code>: navnet på samlingen, som kan inneholde ASCII-bokstaver, tall og understreker (men ingen doble understreker). <code>{collection-specific ID}</code>: en samlingsspesifikk identifikator, hvis aktuelt, for eksempel Z-Library ID. Kan utelates eller forkortes. Må utelates eller forkortes hvis AACID ellers ville overstige 150 tegn. <code>{ISO 8601 timestamp}</code>: en kort versjon av ISO 8601, alltid i UTC, for eksempel <code>20220723T194746Z</code>. Dette tallet må øke monotont for hver utgivelse, selv om dens eksakte semantikk kan variere per samling. Vi foreslår å bruke tiden for skraping eller generering av ID. <code>{shortuuid}</code>: en UUID men komprimert til ASCII, for eksempel ved bruk av base57. Vi bruker for tiden <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket. <strong>Binær data-mappe.</strong> En mappe med de binære dataene til en rekke AAC-er, for en bestemt samling. Disse har følgende egenskaper: Katalogen må inneholde datafiler for alle AAC-er innenfor det angitte området. Hver datafil må ha sin AACID som filnavn (ingen filendelser). Katalognavnet må være et AACID-område, prefikset med <code style="color: green">annas_archive_data__</code>, og uten suffiks. For eksempel har en av våre faktiske utgivelser en katalog kalt<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Det anbefales å gjøre disse mappene noe håndterbare i størrelse, f.eks. ikke større enn 100GB-1TB hver, selv om denne anbefalingen kan endres over tid. <strong>Samling.</strong> Hver AAC tilhører en samling, som per definisjon er en liste over AAC-er som er semantisk konsistente. Det betyr at hvis du gjør en betydelig endring i formatet på metadataene, må du opprette en ny samling. Standarden <strong>Metadata-fil.</strong> En metadata-fil inneholder metadataene til et område av AAC-er, for en bestemt samling. Disse har følgende egenskaper: <code>data_folder</code> er valgfritt, og er navnet på binærdata-mappen som inneholder de tilsvarende binære dataene. Filnavnet til de tilsvarende binære dataene i den mappen er postens AACID. Hvert JSON-objekt må inneholde følgende felt på toppnivå: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valgfritt). Ingen andre felt er tillatt. Filnavnet må være et AACID-område, prefikset med <code style="color: red">annas_archive_meta__</code> og etterfulgt av <code>.jsonl.zstd</code>. For eksempel, en av våre utgivelser heter<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Som indikert av filtypen, er filtypen <a %(jsonlines)s>JSON Lines</a> komprimert med <a %(zstd)s>Zstandard</a>. <code>metadata</code> er vilkårlige metadata, i henhold til semantikken til samlingen. Det må være semantisk konsistent innenfor samlingen. Prefikset <code style="color: red">annas_archive_meta__</code> kan tilpasses navnet på din institusjon, f.eks. <code style="color: red">my_institute_meta__</code>. <strong>«registre» og «filer» samlinger.</strong> Av konvensjon er det ofte praktisk å utgi «registre» og «filer» som forskjellige samlinger, slik at de kan utgis på forskjellige tidspunkter, for eksempel basert på skrapefrekvenser. Et «register» er en samling kun med metadata, som inneholder informasjon som boktitler, forfattere, ISBN-er osv., mens «filer» er samlingene som inneholder selve filene (pdf, epub). Til slutt landet vi på en relativt enkel standard. Den er ganske løs, ikke-normativ, og et arbeid under utvikling. <strong>Torrenter.</strong> Metadatafilene og binære datamapper kan pakkes i torrenter, med én torrent per metadatafil, eller én torrent per binær datamappe. Torrentene må ha det originale fil-/katalognavnet pluss et <code>.torrent</code> suffiks som filnavn. <a %(wikipedia_annas_archive)s>Annas Arkiv</a> har blitt det desidert største skyggebiblioteket i verden, og det eneste skyggebiblioteket av sin skala som er fullstendig åpen kilde og åpen data. Nedenfor er en tabell fra vår Datasets-side (litt modifisert): Vi oppnådde dette på tre måter: Speiling av eksisterende åpen-data skyggebiblioteker (som Sci-Hub og Library Genesis). Hjelpe skyggebiblioteker som ønsker å være mer åpne, men som ikke hadde tid eller ressurser til å gjøre det (som Libgen tegneseriesamlingen). Skraping av biblioteker som ikke ønsker å dele i bulk (som Z-Library). For (2) og (3) administrerer vi nå en betydelig samling av torrenter selv (100-talls TB). Så langt har vi tilnærmet oss disse samlingene som enkeltstående, noe som betyr skreddersydd infrastruktur og dataorganisering for hver samling. Dette legger betydelig overhead til hver utgivelse, og gjør det spesielt vanskelig å gjøre mer inkrementelle utgivelser. Derfor bestemte vi oss for å standardisere våre utgivelser. Dette er et teknisk blogginnlegg der vi introduserer vår standard: <strong>Annas Arkiv Beholdere</strong>. Annas Arkiv Beholdere (AAB): standardisering av utgivelser fra verdens største skyggebibliotek Annas Arkiv har blitt det største skyggebiblioteket i verden, noe som krever at vi standardiserer våre utgivelser. 300GB+ med bokomslag utgitt Endelig er vi glade for å kunngjøre en liten utgivelse. I samarbeid med folkene som driver Libgen.rs-forken, deler vi alle deres bokomslag gjennom torrenter og IPFS. Dette vil fordele belastningen av å se omslagene blant flere maskiner, og bevare dem bedre. I mange (men ikke alle) tilfeller er bokomslagene inkludert i filene selv, så dette er en slags “avledede data”. Men å ha det i IPFS er fortsatt veldig nyttig for daglig drift av både Annas Arkiv og de ulike Library Genesis-forkene. Som vanlig kan du finne denne utgivelsen på Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi vil ikke lenke til den her, men du kan lett finne den. Forhåpentligvis kan vi slappe av litt nå som vi har et anstendig alternativ til Z-Library. Denne arbeidsmengden er ikke spesielt bærekraftig. Hvis du er interessert i å hjelpe til med programmering, serverdrift eller bevaringsarbeid, ta gjerne kontakt med oss. Det er fortsatt mye <a %(annas_archive)s>arbeid som må gjøres</a>. Takk for din interesse og støtte. Bytte til ElasticSearch Noen forespørsler tok veldig lang tid, til det punktet hvor de ville oppta alle åpne forbindelser. Som standard har MySQL en minimumsordlengde, ellers kan indeksen din bli veldig stor. Folk rapporterte at de ikke kunne søke etter «Ben Hur». Søket var bare noe raskt når det var fullstendig lastet i minnet, noe som krevde at vi fikk en dyrere maskin for å kjøre dette på, pluss noen kommandoer for å forhåndslaste indeksen ved oppstart. Vi ville ikke ha vært i stand til å utvide det enkelt for å bygge nye funksjoner, som bedre <a %(wikipedia_cjk_characters)s>tokenisering for språk uten mellomrom</a>, filtrering/fasettering, sortering, "mente du" forslag, autofullfør, og så videre. En av våre <a %(annas_archive)s>billetter</a> var en samling av problemer med søkesystemet vårt. Vi brukte MySQL fulltekst-søk, siden vi hadde alle dataene våre i MySQL uansett. Men det hadde sine begrensninger: Etter å ha snakket med en rekke eksperter, valgte vi ElasticSearch. Det har ikke vært perfekt (deres standard "mente du" forslag og autofullføringsfunksjoner er dårlige), men totalt sett har det vært mye bedre enn MySQL for søk. Vi er fortsatt ikke <a %(youtube)s>for begeistret</a> for å bruke det til noen kritiske data (selv om de har gjort mye <a %(elastic_co)s>fremgang</a>), men totalt sett er vi ganske fornøyde med overgangen. Foreløpig har vi implementert mye raskere søk, bedre språkundestøttelse, bedre relevanssortering, forskjellige sorteringsalternativer og filtrering på språk/boktype/filtype. Hvis du er nysgjerrig på hvordan det fungerer, <a %(annas_archive_l140)s>ta</a> <a %(annas_archive_l1115)s>en</a> <a %(annas_archive_l1635)s>titt</a>. Det er ganske tilgjengelig, selv om det kunne trengt noen flere kommentarer… Annas Arkiv er fullt åpen kildekode Vi mener at informasjon skal være gratis, og vår egen kode er intet unntak. Vi har gitt ut all vår kode på vår privat hostede Gitlab-instans: <a %(annas_archive)s>Annas Programvare</a>. Vi bruker også problemsporeren for å organisere arbeidet vårt. Hvis du vil engasjere deg i utviklingen vår, er dette et flott sted å starte. For å gi deg en smakebit på hva vi jobber med, kan vi nevne vårt nylige arbeid med ytelsesforbedringer på klientsiden. Siden vi ikke har implementert paginering ennå, ville vi ofte returnere veldig lange resultatsider, med 100-200 resultater. Vi ønsket ikke å kutte av søkeresultatene for tidlig, men dette betydde at det ville bremse noen enheter. For dette implementerte vi et lite triks: vi pakket de fleste søkeresultatene inn i HTML-kommentarer (<code><!-- --></code>), og skrev deretter litt Javascript som ville oppdage når et resultat skulle bli synlig, på hvilket tidspunkt vi ville pakke ut kommentaren: DOM "virtualisering" implementert i 23 linjer, ingen behov for fancy biblioteker! Dette er den typen rask pragmatisk kode du ender opp med når du har begrenset tid, og reelle problemer som må løses. Det har blitt rapportert at søket vårt nå fungerer godt på trege enheter! En annen stor innsats var å automatisere byggingen av databasen. Da vi lanserte, trakk vi bare sammen forskjellige kilder tilfeldig. Nå vil vi holde dem oppdatert, så vi skrev en rekke skript for å laste ned ny metadata fra de to Library Genesis-forkene, og integrere dem. Målet er ikke bare å gjøre dette nyttig for vårt arkiv, men å gjøre det enkelt for alle som vil eksperimentere med skyggebibliotek-metadata. Målet ville være en Jupyter-notatbok som har alle slags interessante metadata tilgjengelig, slik at vi kan gjøre mer forskning som å finne ut hva <a %(blog)s>prosentandelen av ISBN-er som bevares for alltid</a>. Til slutt har vi fornyet donasjonssystemet vårt. Du kan nå bruke et kredittkort for å sette inn penger direkte i våre kryptolommebøker, uten egentlig å trenge å vite noe om kryptovalutaer. Vi vil fortsette å overvåke hvor godt dette fungerer i praksis, men dette er en stor sak. Med Z-Library nede og dets (påståtte) grunnleggere arrestert, har vi jobbet døgnet rundt for å tilby et godt alternativ med Annas Arkiv (vi vil ikke lenke til det her, men du kan Google det). Her er noen av tingene vi nylig har oppnådd. Annas Oppdatering: fullt åpen kildekode-arkiv, ElasticSearch, 300GB+ med bokomslag Vi har jobbet døgnet rundt for å tilby et godt alternativ med Annas Arkiv. Her er noen av tingene vi nylig har oppnådd. Analyse Semantiske duplikater (forskjellige skanninger av samme bok) kan teoretisk filtreres ut, men det er vanskelig. Når vi manuelt så gjennom tegneseriene fant vi for mange falske positiver. Det er noen duplikater kun ved MD5, som er relativt sløsende, men å filtrere dem ut ville bare gi oss omtrent 1% in besparelser. I denne skalaen er det fortsatt omtrent 1TB, men også, i denne skalaen spiller 1TB egentlig ingen rolle. Vi vil heller ikke risikere å ødelegge data ved en feiltakelse i denne prosessen. Vi fant en haug med ikke-bokdata, som filmer basert på tegneserier. Det virker også sløsende, siden disse allerede er allment tilgjengelige gjennom andre midler. Imidlertid innså vi at vi ikke bare kunne filtrere ut filmfiler, siden det også finnes <em>interaktive tegneseriebøker</em> som ble utgitt på datamaskinen, som noen har tatt opp og lagret som filmer. Til syvende og sist ville alt vi kunne slette fra samlingen bare spare noen få prosent. Så husket vi at vi er datahamstrere, og de som vil speile dette er også datahamstrere, så, "HVA MENER DU MED, SLETTE?!" :) Når du får 95TB dumpet inn i lagringsklyngen din, prøver du å forstå hva som egentlig er der inne… Vi gjorde noen analyser for å se om vi kunne redusere størrelsen litt, for eksempel ved å fjerne duplikater. Her er noen av våre funn: Vi presenterer derfor for deg, den fullstendige, uendrede samlingen. Det er mye data, men vi håper at nok folk vil bry seg om å dele den uansett. Samarbeid Gitt størrelsen har denne samlingen lenge vært på vår ønskeliste, så etter vår suksess med å sikkerhetskopiere Z-Library, satte vi sikte på denne samlingen. Først skrapet vi den direkte, noe som var en ganske utfordring, siden serveren deres ikke var i beste stand. Vi fikk omtrent 15TB på denne måten, men det gikk sakte. Heldigvis klarte vi å komme i kontakt med operatøren av biblioteket, som gikk med på å sende oss alle dataene direkte, noe som var mye raskere. Det tok fortsatt mer enn et halvt år å overføre og behandle alle dataene, og vi holdt på å miste alt på grunn av diskfeil, noe som ville betydd å starte helt på nytt. Denne opplevelsen har fått oss til å tro at det er viktig å få disse dataene ut så raskt som mulig, slik at de kan speiles vidt og bredt. Vi er bare en eller to uheldige hendelser unna å miste denne samlingen for alltid! Samlingen Å bevege seg raskt betyr at samlingen er litt uorganisert… La oss ta en titt. Tenk deg at vi har et filsystem (som i virkeligheten er delt opp i torrenter): Den første katalogen, <code>/repository</code>, er den mer strukturerte delen av dette. Denne katalogen inneholder såkalte "tusen mapper": kataloger hver med tusen filer, som er nummerert fortløpende i databasen. Katalogen <code>0</code> inneholder filer med comic_id 0–999, og så videre. Dette er det samme opplegget som Library Genesis har brukt for sine skjønnlitterære og faglitterære samlinger. Ideen er at hver "tusen mappe" automatisk blir gjort om til en torrent så snart den er fylt opp. Imidlertid laget aldri operatøren av Libgen.li torrenter for denne samlingen, og derfor ble tusen mappene sannsynligvis upraktiske, og ga vei til "usorterte mapper". Disse er <code>/comics0</code> gjennom <code>/comics4</code>. De inneholder alle unike katalogstrukturer, som sannsynligvis ga mening for å samle filene, men gir ikke så mye mening for oss nå. Heldigvis refererer metadataene fortsatt direkte til alle disse filene, så deres lagringsorganisering på disk spiller faktisk ingen rolle! Metadataene er tilgjengelige i form av en MySQL-database. Denne kan lastes ned direkte fra Libgen.li-nettstedet, men vi vil også gjøre den tilgjengelig i en torrent, sammen med vår egen tabell med alle MD5-hashene. <q>Dr. Barbara Gordon prøver å miste seg selv i bibliotekets hverdagsverden…</q> Libgen-forker Først litt bakgrunn. Du kjenner kanskje Library Genesis for deres episke boksamling. Færre vet at Library Genesis-frivillige har opprettet andre prosjekter, som en betydelig samling av magasiner og standarddokumenter, en full sikkerhetskopi av Sci-Hub (i samarbeid med grunnleggeren av Sci-Hub, Alexandra Elbakyan), og faktisk en massiv samling av tegneserier. På et tidspunkt gikk forskjellige operatører av Library Genesis-speil hver sin vei, noe som førte til den nåværende situasjonen med å ha en rekke forskjellige “forker”, alle fortsatt med navnet Library Genesis. Libgen.li-forken har unikt denne tegneseriesamlingen, samt en betydelig magasinsamling (som vi også jobber med). Innsamling Vi slipper disse dataene i noen store biter. Den første torrenten er av <code>/comics0</code>, som vi la i en enorm 12TB .tar-fil. Det er bedre for harddisken din og torrentprogramvaren enn en haug med mindre filer. Som en del av denne utgivelsen, gjennomfører vi en innsamlingsaksjon. Vi ønsker å samle inn $20,000 for å dekke drifts- og kontraktskostnader for denne samlingen, samt muliggjøre pågående og fremtidige prosjekter. Vi har noen <em>enorme</em> på gang. <em>Hvem støtter jeg med min donasjon?</em> Kort sagt: vi sikkerhetskopierer all kunnskap og kultur i menneskeheten, og gjør den lett tilgjengelig. All vår kode og data er åpen kildekode, vi er et fullstendig frivillig drevet prosjekt, og vi har lagret 125TB med bøker så langt (i tillegg til Libgen og Scihub sine eksisterende torrenter). Til syvende og sist bygger vi et svinghjul som muliggjør og motiverer folk til å finne, skanne og sikkerhetskopiere alle bøkene i verden. Vi vil skrive om vår hovedplan i et fremtidig innlegg. :) Hvis du donerer for et 12-måneders "Fantastisk Arkivar"-medlemskap ($780), får du <strong>“adoptere en torrent”</strong>, noe som betyr at vi vil sette brukernavnet ditt eller en melding i filnavnet til en av torrentene! Du kan donere ved å gå til <a %(wikipedia_annas_archive)s>Annas Arkiv</a> og klikke på “Doner”-knappen. Vi ser også etter flere frivillige: programvareingeniører, sikkerhetsforskere, eksperter på anonyme betalinger og oversettere. Du kan også støtte oss ved å tilby hostingtjenester. Og selvfølgelig, vennligst del våre torrenter! Takk til alle som allerede har støttet oss så generøst! Dere gjør virkelig en forskjell. Her er torrentene som er utgitt så langt (vi behandler fortsatt resten): Alle torrentene kan finnes på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under “Datasets” (vi lenker ikke direkte dit, slik at lenker til denne bloggen ikke blir fjernet fra Reddit, Twitter, etc). Derfra kan du følge lenken til Tor-nettstedet. <a %(news_ycombinator)s>Diskuter på Hacker News</a> Hva er det neste? En haug med torrenter er flotte for langtidsbevaring, men ikke så mye for daglig tilgang. Vi vil jobbe med hostingpartnere for å få all denne dataen opp på nettet (siden Annas Arkiv ikke hoster noe direkte). Selvfølgelig vil du kunne finne disse nedlastingslenkene på Annas Arkiv. Vi inviterer også alle til å gjøre ting med disse dataene! Hjelp oss med å analysere dem bedre, fjerne duplikater, legge dem på IPFS, remikse dem, trene AI-modellene dine med dem, og så videre. Det er alt ditt, og vi kan ikke vente med å se hva du gjør med det. Til slutt, som sagt før, har vi fortsatt noen massive utgivelser på vei (hvis <em>noen</em> kunne <em>tilfeldigvis</em> sende oss en dump av en <em>spesiell</em> ACS4-database, vet du hvor du finner oss...), samt bygge svinghjulet for å sikkerhetskopiere alle bøkene i verden. Så følg med, vi har bare så vidt begynt. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Det største skyggebiblioteket for tegneserier er sannsynligvis det til en bestemt Library Genesis-fork: Libgen.li. Den ene administratoren som driver det nettstedet klarte å samle en vanvittig tegneseriesamling på over 2 millioner filer, totalt over 95TB. Men i motsetning til andre Library Genesis-samlinger, var denne ikke tilgjengelig i bulk gjennom torrenter. Du kunne bare få tilgang til disse tegneseriene individuelt gjennom hans trege personlige server — et enkelt feilpunkt. Inntil i dag! I dette innlegget vil vi fortelle deg mer om denne samlingen, og om vår innsamlingsaksjon for å støtte mer av dette arbeidet. Annas Arkiv har sikkerhetskopiert verdens største skyggebibliotek for tegneserier (95TB) — du kan hjelpe til med å seede det Verdens største skyggebibliotek for tegneserier hadde et enkelt feilpunkt... inntil i dag. Advarsel: dette blogginnlegget er foreldet. Vi har bestemt at IPFS ennå ikke er klar for prime time. Vi vil fortsatt lenke til filer på IPFS fra Annas Arkiv når det er mulig, men vi vil ikke lenger hoste det selv, og vi anbefaler heller ikke andre å speile ved hjelp av IPFS. Vennligst se vår Torrents-side hvis du vil hjelpe til med å bevare vår samling. Legger 5 998 794 bøker på IPFS En multiplikasjon av kopier Tilbake til vårt opprinnelige spørsmål: hvordan kan vi hevde å bevare våre samlinger i all evighet? Hovedproblemet her er at samlingen vår har <a %(torrents_stats)s>vokst</a> raskt, ved å skrape og åpen-kildekode noen massive samlinger (i tillegg til det fantastiske arbeidet som allerede er gjort av andre åpne data-skyggebiblioteker som Sci-Hub og Library Genesis). Denne veksten i data gjør det vanskeligere for samlingene å bli speilet rundt om i verden. Dataplass er dyrt! Men vi er optimistiske, spesielt når vi observerer de følgende tre trendene. Den <a %(annas_archive_stats)s>totale størrelsen</a> på våre samlinger, de siste månedene, oppdelt etter antall torrent-seedere. Prisstrender for HDD fra forskjellige kilder (klikk for å se studie). <a %(critical_window_chinese)s>Kinesisk versjon 中文版</a>, diskuter på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Vi har plukket de lavthengende fruktene Dette følger direkte fra våre prioriteringer diskutert ovenfor. Vi foretrekker å arbeide med å frigjøre store samlinger først. Nå som vi har sikret noen av de største samlingene i verden, forventer vi at vår vekst vil være mye langsommere. Det er fortsatt en lang hale av mindre samlinger, og nye bøker blir skannet eller publisert hver dag, men tempoet vil sannsynligvis være mye langsommere. Vi kan fortsatt doble eller til og med tredoble i størrelse, men over en lengre tidsperiode. Forbedringer i OCR. Prioriteter Vitenskapelig og teknisk programvarekode Fiktive eller underholdningsversjoner av alt det ovennevnte Geografiske data (f.eks. kart, geologiske undersøkelser) Intern data fra selskaper eller myndigheter (lekkasjer) Måledata som vitenskapelige målinger, økonomiske data, bedriftsrapporter Metadataoppføringer generelt (av sakprosa og skjønnlitteratur; av andre medier, kunst, personer, etc.; inkludert anmeldelser) Sakprosa bøker Sakprosa magasiner, aviser, manualer Sakprosa transkripsjoner av foredrag, dokumentarer, podkaster Organiske data som DNA-sekvenser, plantefrø eller mikrobielle prøver Akademiske artikler, tidsskrifter, rapporter Vitenskap og ingeniørnettsteder, nettbaserte diskusjoner Transkripsjoner av juridiske eller rettslige forhandlinger Unikt utsatt for ødeleggelse (f.eks. av krig, kutt i finansiering, søksmål eller politisk forfølgelse) Sjeldne Unikt underfokuserte Hvorfor bryr vi oss så mye om artikler og bøker? La oss sette til side vår grunnleggende tro på bevaring generelt — vi kan skrive et annet innlegg om det. Så hvorfor artikler og bøker spesielt? Svaret er enkelt: <strong>informasjonstetthet</strong>. Per megabyte lagring, lagrer skrevet tekst mest informasjon av alle medier. Selv om vi bryr oss om både kunnskap og kultur, bryr vi oss mer om førstnevnte. Totalt sett finner vi en hierarki av informasjonstetthet og viktighet av bevaring som ser omtrent slik ut: Rangeringen i denne listen er noe vilkårlig — flere elementer er uavgjort eller har uenigheter innen vårt team — og vi glemmer sannsynligvis noen viktige kategorier. Men dette er omtrent hvordan vi prioriterer. Noen av disse elementene er for forskjellige fra de andre til at vi trenger å bekymre oss for dem (eller er allerede tatt hånd om av andre institusjoner), som organiske data eller geografiske data. Men de fleste av elementene på denne listen er faktisk viktige for oss. En annen stor faktor i vår prioritering er hvor mye risiko et bestemt verk er i. Vi foretrekker å fokusere på verk som er: Til slutt bryr vi oss om skala. Vi har begrenset tid og penger, så vi vil heller bruke en måned på å redde 10 000 bøker enn 1 000 bøker — hvis de er omtrent like verdifulle og utsatt. <em><q>Det tapte kan ikke gjenopprettes; men la oss redde det som gjenstår: ikke ved hvelv og låser som beskytter dem fra offentligheten og bruk, ved å overlate dem til tidens avfall, men ved en slik multiplikasjon av kopier, som skal plassere dem utenfor rekkevidden av uhell.</q></em><br>— Thomas Jefferson, 1791 Skyggebiblioteker Kode kan være åpen kildekode på Github, men Github som helhet kan ikke enkelt speiles og dermed bevares (selv om det i dette tilfellet er tilstrekkelig distribuerte kopier av de fleste kodearkivene) Metadata-poster kan fritt vises på Worldcat-nettstedet, men ikke lastes ned i bulk (før vi <a %(worldcat_scrape)s>skrapet</a> dem) Reddit er gratis å bruke, men har nylig innført strenge anti-skrapetiltak, i kjølvannet av datahungrige LLM-treninger (mer om det senere) Det finnes mange organisasjoner med lignende oppdrag og prioriteringer. Faktisk finnes det biblioteker, arkiver, laboratorier, museer og andre institusjoner som har som oppgave å bevare denne typen materiale. Mange av disse er godt finansiert av regjeringer, enkeltpersoner eller selskaper. Men de har ett stort blindpunkt: rettssystemet. Her ligger den unike rollen til skyggebiblioteker, og grunnen til at Annas Arkiv eksisterer. Vi kan gjøre ting som andre institusjoner ikke har lov til å gjøre. Nå er det ikke (ofte) slik at vi kan arkivere materialer som er ulovlige å bevare andre steder. Nei, det er lovlig mange steder å bygge et arkiv med bøker, artikler, magasiner, og så videre. Men det som ofte mangler i juridiske arkiver er <strong>redundans og lang levetid</strong>. Det finnes bøker hvor det bare eksisterer én kopi i et fysisk bibliotek et sted. Det finnes metadata-poster som voktes av et enkelt selskap. Det finnes aviser som kun er bevart på mikrofilm i et enkelt arkiv. Biblioteker kan få kutt i finansieringen, selskaper kan gå konkurs, arkiver kan bli bombet og brent til grunnen. Dette er ikke hypotetisk — dette skjer hele tiden. Det vi unikt kan gjøre på Annas Arkiv er å lagre mange kopier av verk, i stor skala. Vi kan samle artikler, bøker, magasiner og mer, og distribuere dem i bulk. Vi gjør dette for tiden gjennom torrenter, men de eksakte teknologiene spiller ingen rolle og vil endre seg over tid. Det viktige er å få mange kopier distribuert over hele verden. Dette sitatet fra over 200 år siden er fortsatt aktuelt: En rask merknad om offentlig domene. Siden Annas Arkiv unikt fokuserer på aktiviteter som er ulovlige mange steder i verden, bryr vi oss ikke om allment tilgjengelige samlinger, som bøker i offentlig domene. Juridiske enheter tar ofte allerede godt vare på det. Imidlertid er det hensyn som gjør at vi noen ganger arbeider med offentlig tilgjengelige samlinger: - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Lagringskostnadene fortsetter å falle eksponentielt 3. Forbedringer i informasjonsdensitet Vi lagrer for tiden bøker i de rå formatene de blir gitt til oss. Selvfølgelig er de komprimert, men ofte er de fortsatt store skanninger eller fotografier av sider. Inntil nå har de eneste alternativene for å redusere den totale størrelsen på samlingen vår vært gjennom mer aggressiv komprimering, eller deduplisering. Imidlertid, for å oppnå betydelige nok besparelser, er begge for tapende for vår smak. Kraftig komprimering av bilder kan gjøre tekst knapt lesbar. Og deduplisering krever høy tillit til at bøkene er nøyaktig de samme, noe som ofte er for unøyaktig, spesielt hvis innholdet er det samme, men skanningene er gjort på forskjellige tidspunkter. Det har alltid vært et tredje alternativ, men kvaliteten har vært så elendig at vi aldri vurderte det: <strong>OCR, eller optisk tegngjenkjenning</strong>. Dette er prosessen med å konvertere bilder til ren tekst, ved å bruke AI for å oppdage tegnene i bildene. Verktøy for dette har eksistert lenge, og har vært ganske gode, men "ganske gode" er ikke nok for bevaringsformål. Imidlertid har nylige multimodale dyp-læringsmodeller gjort ekstremt raske fremskritt, selv om de fortsatt er kostbare. Vi forventer at både nøyaktighet og kostnader vil forbedres dramatisk i de kommende årene, til det punktet hvor det vil bli realistisk å anvende på hele biblioteket vårt. Når det skjer, vil vi sannsynligvis fortsatt bevare de originale filene, men i tillegg kunne vi ha en mye mindre versjon av biblioteket vårt som de fleste vil ønske å speile. Poenget er at rå tekst i seg selv komprimerer enda bedre, og er mye lettere å deduplisere, noe som gir oss enda flere besparelser. Totalt sett er det ikke urealistisk å forvente minst en 5-10x reduksjon i total filstørrelse, kanskje enda mer. Selv med en konservativ 5x reduksjon, vil vi se på <strong>$1,000–$3,000 om 10 år selv om biblioteket vårt tredobles i størrelse</strong>. På tidspunktet for skrivingen er <a %(diskprices)s>diskpriser</a> per TB rundt $12 for nye disker, $8 for brukte disker, og $4 for tape. Hvis vi er konservative og kun ser på nye disker, betyr det at lagring av en petabyte koster omtrent $12,000. Hvis vi antar at biblioteket vårt vil tredobles fra 900TB til 2,7PB, vil det bety $32,400 for å speile hele biblioteket vårt. Legger vi til strøm, kostnad for annet utstyr, og så videre, la oss runde det opp til $40,000. Eller med tape mer som $15,000–$20,000. På den ene siden er <strong>$15,000–$40,000 for summen av all menneskelig kunnskap et kupp</strong>. På den andre siden er det litt bratt å forvente massevis av fullstendige kopier, spesielt hvis vi også vil at de menneskene skal fortsette å dele sine torrenter til fordel for andre. Det er i dag. Men fremgangen marsjerer fremover: Kostnadene for harddisker per TB har blitt omtrent tredelt de siste 10 årene, og vil sannsynligvis fortsette å falle i samme tempo. Tape ser ut til å være på en lignende bane. SSD-prisene faller enda raskere, og kan overta HDD-prisene innen slutten av tiåret. Hvis dette holder, kan vi om 10 år se på bare $5,000–$13,000 for å speile hele samlingen vår (1/3), eller enda mindre hvis vi vokser mindre i størrelse. Selv om det fortsatt er mye penger, vil dette være oppnåelig for mange mennesker. Og det kan bli enda bedre på grunn av det neste punktet… På Annas Arkiv blir vi ofte spurt hvordan vi kan hevde å bevare våre samlinger i all evighet, når den totale størrelsen allerede nærmer seg 1 Petabyte (1000 TB), og fortsatt vokser. I denne artikkelen vil vi se på vår filosofi, og se hvorfor det neste tiåret er kritisk for vårt oppdrag med å bevare menneskehetens kunnskap og kultur. Kritisk vindu Hvis disse prognosene er nøyaktige, trenger vi <strong>bare å vente et par år</strong> før hele samlingen vår vil bli bredt speilet. Dermed, i Thomas Jeffersons ord, "plassert utenfor rekkevidde for uhell." Dessverre har fremveksten av LLM-er, og deres datahungrige trening, satt mange rettighetshavere på defensiven. Enda mer enn de allerede var. Mange nettsteder gjør det vanskeligere å skrape og arkivere, søksmål flyr rundt, og samtidig fortsetter fysiske biblioteker og arkiver å bli neglisjert. Vi kan bare forvente at disse trendene vil fortsette å forverres, og mange verk vil gå tapt lenge før de kommer inn i det offentlige domene. <strong>Vi står på terskelen til en revolusjon innen bevaring, men <q>det tapte kan ikke gjenopprettes.</q></strong> Vi har et kritisk vindu på omtrent 5-10 år der det fortsatt er ganske kostbart å drive et skyggebibliotek og opprette mange speil rundt om i verden, og der tilgangen ennå ikke er fullstendig stengt. Hvis vi kan bygge bro over dette vinduet, vil vi faktisk ha bevart menneskehetens kunnskap og kultur for alltid. Vi bør ikke la denne tiden gå til spille. Vi bør ikke la dette kritiske vinduet lukkes for oss. La oss gå. Det kritiske vinduet for skyggebiblioteker Hvordan kan vi hevde å bevare våre samlinger i all evighet, når de allerede nærmer seg 1 PB? Samling Litt mer informasjon om samlingen. <a %(duxiu)s>Duxiu</a> er en massiv database med skannede bøker, opprettet av <a %(chaoxing)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøker, skannet for å gjøre dem tilgjengelige digitalt for universiteter og biblioteker. For vårt engelsktalende publikum har <a %(library_princeton)s>Princeton</a> og <a %(guides_lib_uw)s>University of Washington</a> gode oversikter. Det finnes også en utmerket artikkel som gir mer bakgrunn: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (søk den opp i Annas Arkiv). Bøkene fra Duxiu har lenge blitt piratkopiert på det kinesiske internett. Vanligvis blir de solgt for mindre enn en dollar av forhandlere. De distribueres vanligvis ved hjelp av den kinesiske ekvivalenten til Google Drive, som ofte har blitt hacket for å tillate mer lagringsplass. Noen tekniske detaljer kan finnes <a %(github_duty_machine)s>her</a> og <a %(github_821_github_io)s>her</a>. Selv om bøkene har blitt semi-offentlig distribuert, er det ganske vanskelig å skaffe dem i bulk. Vi hadde dette høyt på vår TODO-liste, og allokerte flere måneder med fulltidsarbeid for det. Imidlertid nådde nylig en utrolig, fantastisk og talentfull frivillig ut til oss, og fortalte at de allerede hadde gjort alt dette arbeidet — til stor kostnad. De delte hele samlingen med oss, uten å forvente noe i retur, bortsett fra garantien om langsiktig bevaring. Virkelig bemerkelsesverdig. De gikk med på å be om hjelp på denne måten for å få samlingen OCR'et. Samlingen består av 7 543 702 filer. Dette er mer enn Library Genesis sakprosa (omtrent 5,3 millioner). Total filstørrelse er omtrent 359TB (326TiB) i sin nåværende form. Vi er åpne for andre forslag og ideer. Bare kontakt oss. Sjekk ut Annas Arkiv for mer informasjon om våre samlinger, bevaringsinnsats, og hvordan du kan hjelpe. Takk! Eksempelsider For å bevise for oss at du har en god pipeline, her er noen eksempelsider å starte med, fra en bok om superledere. Din pipeline bør håndtere matematikk, tabeller, diagrammer, fotnoter, og så videre på riktig måte. Send dine behandlede sider til vår e-post. Hvis de ser bra ut, vil vi sende deg flere privat, og vi forventer at du raskt kan kjøre din pipeline på dem også. Når vi er fornøyde, kan vi inngå en avtale. - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kinesisk versjon 中文版</a>, <a %(news_ycombinator)s>Diskuter på Hacker News</a> Dette er et kort blogginnlegg. Vi ser etter et selskap eller en institusjon som kan hjelpe oss med OCR og tekstekstraksjon for en massiv samling vi har anskaffet, i bytte mot eksklusiv tidlig tilgang. Etter embargo-perioden vil vi selvfølgelig frigjøre hele samlingen. Høykvalitets akademisk tekst er ekstremt nyttig for trening av LLM-er. Selv om samlingen vår er kinesisk, bør dette også være nyttig for trening av engelske LLM-er: modeller ser ut til å kode konsepter og kunnskap uavhengig av kildespråket. For dette må tekst trekkes ut fra skanningene. Hva får Annas Arkiv ut av det? Fulltekstsøk i bøkene for sine brukere. Fordi våre mål samsvarer med LLM-utviklernes, ser vi etter en samarbeidspartner. Vi er villige til å gi deg <strong>eksklusiv tidlig tilgang til denne samlingen i bulk i 1 år</strong>, hvis du kan utføre riktig OCR og tekstekstraksjon. Hvis du er villig til å dele hele koden til din pipeline med oss, vil vi være villige til å forlenge embargoen på samlingen. Eksklusiv tilgang for LLM-selskaper til verdens største samling av kinesiske sakprosabøker <em><strong>Kort fortalt:</strong> Annas Arkiv har anskaffet en unik samling på 7,5 millioner / 350TB kinesiske sakprosabøker — større enn Library Genesis. Vi er villige til å gi et LLM-selskap eksklusiv tilgang, i bytte mot høy kvalitet på OCR og tekstekstraksjon.</em> Systemarkitektur La oss si at du har funnet noen selskaper som er villige til å være vert for nettstedet ditt uten å stenge deg ned — la oss kalle dem “frihetselskende leverandører” 😄. Du vil raskt oppdage at det er ganske dyrt å ha alt hos dem, så du vil kanskje finne noen “billige leverandører” og gjøre den faktiske hosting der, med proxy gjennom de frihetselskende leverandørene. Hvis du gjør det riktig, vil de billige leverandørene aldri vite hva du hoster, og aldri motta noen klager. Med alle disse leverandørene er det en risiko for at de stenger deg ned uansett, så du trenger også redundans. Vi trenger dette på alle nivåer i vår stack. Et noe frihetselskende selskap som har satt seg selv i en interessant posisjon er Cloudflare. De har <a %(blog_cloudflare)s>argumentert</a> for at de ikke er en hosting-leverandør, men en tjeneste, som en ISP. De er derfor ikke underlagt DMCA eller andre forespørsler om fjerning, og videresender eventuelle forespørsler til din faktiske hosting-leverandør. De har til og med gått til retten for å beskytte denne strukturen. Vi kan derfor bruke dem som et ekstra lag med caching og beskyttelse. Cloudflare aksepterer ikke anonyme betalinger, så vi kan bare bruke deres gratis plan. Dette betyr at vi ikke kan bruke deres lastbalansering eller failover-funksjoner. Vi har derfor <a %(annas_archive_l255)s>implementert dette selv</a> på domenenivå. Ved sideinnlasting vil nettleseren sjekke om det nåværende domenet fortsatt er tilgjengelig, og hvis ikke, omskriver den alle URL-er til et annet domene. Siden Cloudflare cacher mange sider, betyr dette at en bruker kan lande på vårt hoveddomene, selv om proxy-serveren er nede, og deretter ved neste klikk bli flyttet over til et annet domene. Vi har fortsatt også vanlige driftsmessige bekymringer å håndtere, som å overvåke serverhelse, loggføre backend- og frontend-feil, og så videre. Vår failover-arkitektur gir mer robusthet på denne fronten også, for eksempel ved å kjøre et helt annet sett med servere på ett av domenene. Vi kan til og med kjøre eldre versjoner av koden og datasettene på dette separate domenet, i tilfelle en kritisk feil i hovedversjonen går ubemerket. Vi kan også sikre oss mot at Cloudflare vender seg mot oss, ved å fjerne det fra ett av domenene, som dette separate domenet. Ulike permutasjoner av disse ideene er mulige. Konklusjon Det har vært en interessant opplevelse å lære hvordan man setter opp en robust og motstandsdyktig skyggebibliotek-søkemotor. Det er mange flere detaljer å dele i senere innlegg, så gi meg beskjed om hva du vil lære mer om! Som alltid ser vi etter donasjoner for å støtte dette arbeidet, så sørg for å sjekke ut Doner-siden på Annas Arkiv. Vi ser også etter andre typer støtte, som tilskudd, langsiktige sponsorer, høyrisiko betalingsleverandører, kanskje til og med (smakfulle!) annonser. Og hvis du vil bidra med din tid og ferdigheter, ser vi alltid etter utviklere, oversettere, og så videre. Takk for din interesse og støtte. Innovasjonspoeng La oss starte med vår teknologistabel. Den er bevisst kjedelig. Vi bruker Flask, MariaDB og ElasticSearch. Det er bokstavelig talt det. Søking er stort sett et løst problem, og vi har ikke tenkt å gjenoppfinne det. Dessuten må vi bruke våre <a %(mcfunley)s>innovasjonspoeng</a> på noe annet: å unngå å bli tatt ned av myndighetene. Så hvor lovlig eller ulovlig er egentlig Annas Arkiv? Dette avhenger stort sett av den juridiske jurisdiksjonen. De fleste land tror på en form for opphavsrett, noe som betyr at personer eller selskaper tildeles et eksklusivt monopol på visse typer verk i en viss periode. Som en sidebemerkning, i Annas Arkiv mener vi at selv om det er noen fordeler, er opphavsrett totalt sett en netto-negativ for samfunnet — men det er en historie for en annen gang. Dette eksklusive monopolet på visse verk betyr at det er ulovlig for noen utenfor dette monopolet å direkte distribuere disse verkene — inkludert oss. Men Annas Arkiv er en søkemotor som ikke direkte distribuerer disse verkene (i det minste ikke på vårt clearnet-nettsted), så vi burde være i orden, ikke sant? Ikke helt. I mange jurisdiksjoner er det ikke bare ulovlig å distribuere opphavsrettsbeskyttede verk, men også å lenke til steder som gjør det. Et klassisk eksempel på dette er USAs DMCA-lov. Det er den strengeste enden av spekteret. I den andre enden av spekteret kan det teoretisk sett være land uten opphavsrettslovgivning overhodet, men disse eksisterer egentlig ikke. Nesten alle land har en form for opphavsrettslov på bøkene. Håndhevelse er en annen historie. Det er mange land med regjeringer som ikke bryr seg om å håndheve opphavsrettslovgivning. Det finnes også land mellom de to ytterpunktene, som forbyr distribusjon av opphavsrettsbeskyttede verk, men ikke forbyr lenking til slike verk. En annen vurdering er på selskapsnivå. Hvis et selskap opererer i en jurisdiksjon som ikke bryr seg om opphavsrett, men selskapet selv ikke er villig til å ta noen risiko, kan de stenge ned nettstedet ditt så snart noen klager på det. Til slutt er en stor vurdering betalinger. Siden vi må forbli anonyme, kan vi ikke bruke tradisjonelle betalingsmetoder. Dette etterlater oss med kryptovalutaer, og bare et lite utvalg av selskaper støtter disse (det finnes virtuelle debetkort betalt med krypto, men de blir ofte ikke akseptert). - Anna og teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Jeg driver <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største åpen kildekode, ideelle søkemotor for <a %(wikipedia_shadow_library)s>skyggebiblioteker</a>, som Sci-Hub, Library Genesis og Z-Library. Vårt mål er å gjøre kunnskap og kultur lett tilgjengelig, og til slutt bygge et fellesskap av mennesker som sammen arkiverer og bevarer <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle bøkene i verden</a>. I denne artikkelen vil jeg vise hvordan vi driver dette nettstedet, og de unike utfordringene som følger med å drive et nettsted med tvilsom juridisk status, siden det ikke finnes noen "AWS for skyggeorganisasjoner". <em>Sjekk også ut søsterartikkelen <a %(blog_how_to_become_a_pirate_archivist)s>Hvordan bli en piratarkivar</a>.</em> Hvordan drive et skyggebibliotek: drift ved Annas Arkiv Det finnes ingen <q>AWS for skyggeorganisasjoner,</q> så hvordan driver vi Annas Arkiv? Verktøy Applikasjonsserver: Flask, MariaDB, ElasticSearch, Docker. Utvikling: Gitlab, Weblate, Zulip. Serveradministrasjon: Ansible, Checkmk, UFW. Onion statisk hosting: Tor, Nginx. Proxy-server: Varnish. La oss se på hvilke verktøy vi bruker for å oppnå alt dette. Dette utvikler seg veldig mye etter hvert som vi støter på nye problemer og finner nye løsninger. Det er noen avgjørelser vi har gått frem og tilbake på. En av dem er kommunikasjonen mellom servere: vi pleide å bruke Wireguard til dette, men fant ut at det av og til slutter å overføre data, eller bare overfører data i én retning. Dette skjedde med flere forskjellige Wireguard-oppsett som vi prøvde, som <a %(github_costela_wesher)s>wesher</a> og <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi prøvde også å tunnelere porter over SSH, ved å bruke autossh og sshuttle, men støtte på <a %(github_sshuttle)s>problemer der</a> (selv om det fortsatt ikke er klart for meg om autossh lider av TCP-over-TCP-problemer eller ikke — det føles bare som en klønete løsning for meg, men kanskje det faktisk er greit?). I stedet gikk vi tilbake til direkte forbindelser mellom servere, og skjulte at en server kjører på de billige leverandørene ved å bruke IP-filtrering med UFW. Dette har den ulempen at Docker ikke fungerer godt med UFW, med mindre du bruker <code>network_mode: "host"</code>. Alt dette er litt mer feilutsatt, fordi du vil eksponere serveren din for internett med bare en liten feilkonfigurasjon. Kanskje vi burde gå tilbake til autossh — tilbakemeldinger vil være veldig velkomne her. Vi har også gått frem og tilbake på Varnish vs. Nginx. Vi liker for øyeblikket Varnish, men det har sine særegenheter og ujevnheter. Det samme gjelder for Checkmk: vi elsker det ikke, men det fungerer for nå. Weblate har vært greit, men ikke utrolig — jeg frykter noen ganger at det vil miste dataene mine når jeg prøver å synkronisere det med git-repoet vårt. Flask har vært bra generelt, men det har noen rare særegenheter som har kostet mye tid å feilsøke, som å konfigurere egendefinerte domener, eller problemer med SqlAlchemy-integrasjonen. Så langt har de andre verktøyene vært flotte: vi har ingen alvorlige klager på MariaDB, ElasticSearch, Gitlab, Zulip, Docker og Tor. Alle disse har hatt noen problemer, men ingenting altfor alvorlig eller tidkrevende. Fellesskap Den første utfordringen kan være overraskende. Det er ikke et teknisk problem, eller et juridisk problem. Det er et psykologisk problem: å gjøre dette arbeidet i skyggene kan være utrolig ensomt. Avhengig av hva du planlegger å gjøre, og din trusselmodell, må du kanskje være veldig forsiktig. I den ene enden av spekteret har vi folk som Alexandra Elbakyan*, grunnleggeren av Sci-Hub, som er veldig åpen om sine aktiviteter. Men hun er i stor fare for å bli arrestert hvis hun besøker et vestlig land nå, og kan risikere flere tiår i fengsel. Er det en risiko du er villig til å ta? Vi er i den andre enden av spekteret; vi er veldig forsiktige med å ikke etterlate noen spor, og har sterk operasjonell sikkerhet. * Som nevnt på HN av "ynno", ønsket Alexandra opprinnelig ikke å bli kjent: "Serverne hennes var satt opp til å sende detaljerte feilmeldinger fra PHP, inkludert full sti til feilkilden, som var under katalogen /home/<USER>" Så, bruk tilfeldige brukernavn på datamaskinene du bruker til dette, i tilfelle du feilkonfigurerer noe. Den hemmeligholdelsen kommer imidlertid med en psykologisk kostnad. De fleste elsker å bli anerkjent for arbeidet de gjør, men du kan ikke ta noen ære for dette i det virkelige liv. Selv enkle ting kan være utfordrende, som når venner spør hva du har holdt på med (på et tidspunkt blir "tukle med min NAS / hjemmelab" gammelt). Dette er grunnen til at det er så viktig å finne et fellesskap. Du kan gi opp litt operasjonell sikkerhet ved å betro deg til noen veldig nære venner, som du vet du kan stole dypt på. Selv da, vær forsiktig med å ikke skrive noe ned, i tilfelle de må overlevere e-postene sine til myndighetene, eller hvis enhetene deres blir kompromittert på en annen måte. Enda bedre er det å finne noen medpirater. Hvis dine nære venner er interessert i å bli med, flott! Ellers kan du kanskje finne andre online. Dessverre er dette fortsatt et nisjefellesskap. Så langt har vi funnet bare en håndfull andre som er aktive på dette området. Gode startsteder ser ut til å være Library Genesis-forumene og r/DataHoarder. Archive Team har også likesinnede individer, selv om de opererer innenfor loven (selv om det er i noen gråsoner av loven). De tradisjonelle "warez"- og piratscenene har også folk som tenker på lignende måter. Vi er åpne for ideer om hvordan vi kan fremme fellesskap og utforske ideer. Ta gjerne kontakt med oss på Twitter eller Reddit. Kanskje vi kunne arrangere et slags forum eller en chattegruppe. En utfordring er at dette lett kan bli sensurert når man bruker vanlige plattformer, så vi måtte ha hostet det selv. Det er også en avveining mellom å ha disse diskusjonene helt offentlige (mer potensielt engasjement) versus å gjøre dem private (ikke la potensielle "mål" vite at vi er i ferd med å skrape dem). Vi må tenke på det. Gi oss beskjed hvis du er interessert i dette! Konklusjon Forhåpentligvis er dette nyttig for nyoppstartede piratarkivarer. Vi er glade for å ønske deg velkommen til denne verden, så ikke nøl med å ta kontakt. La oss bevare så mye av verdens kunnskap og kultur som vi kan, og speile det vidt og bredt. Prosjekter 4. Datavalg Ofte kan du bruke metadataene til å finne ut et rimelig delsett av data å laste ned. Selv om du til slutt ønsker å laste ned alle dataene, kan det være nyttig å prioritere de viktigste elementene først, i tilfelle du blir oppdaget og forsvarene forbedres, eller fordi du må kjøpe flere disker, eller rett og slett fordi noe annet dukker opp i livet ditt før du rekker å laste ned alt. For eksempel kan en samling ha flere utgaver av den samme underliggende ressursen (som en bok eller en film), der en er merket som den beste kvaliteten. Å lagre disse utgavene først ville være veldig fornuftig. Du vil kanskje til slutt lagre alle utgavene, siden metadataene i noen tilfeller kan være feilmerket, eller det kan være ukjente avveininger mellom utgavene (for eksempel kan "beste utgave" være best på de fleste måter, men dårligere på andre måter, som en film med høyere oppløsning men uten undertekster). Du kan også søke i metadata-databasen din for å finne interessante ting. Hva er den største filen som er vert, og hvorfor er den så stor? Hva er den minste filen? Er det interessante eller uventede mønstre når det gjelder visse kategorier, språk, og så videre? Er det dupliserte eller svært like titler? Er det mønstre for når data ble lagt til, som en dag der mange filer ble lagt til samtidig? Du kan ofte lære mye ved å se på datasettet på forskjellige måter. I vårt tilfelle dedupliserte vi Z-Library-bøker mot md5-hashene i Library Genesis, og sparte dermed mye nedlastingstid og diskplass. Dette er en ganske unik situasjon. I de fleste tilfeller finnes det ingen omfattende databaser over hvilke filer som allerede er riktig bevart av andre pirater. Dette i seg selv er en stor mulighet for noen der ute. Det ville vært flott å ha en jevnlig oppdatert oversikt over ting som musikk og filmer som allerede er bredt delt på torrent-nettsteder, og som derfor har lavere prioritet å inkludere i piratspeil. 6. Distribusjon Du har dataene, og gir deg dermed verdens første piratspeil av målet ditt (mest sannsynlig). På mange måter er den vanskeligste delen over, men den mest risikable delen ligger fortsatt foran deg. Tross alt har du så langt vært i det skjulte; fløyet under radaren. Alt du måtte gjøre var å bruke en god VPN hele veien, ikke fylle inn dine personlige detaljer i noen skjemaer (selvfølgelig), og kanskje bruke en spesiell nettleserøkt (eller til og med en annen datamaskin). Nå må du distribuere dataene. I vårt tilfelle ønsket vi først å bidra med bøkene tilbake til Library Genesis, men oppdaget raskt vanskelighetene med det (fiksjon vs. ikke-fiksjon sortering). Så vi bestemte oss for distribusjon ved hjelp av Library Genesis-stil torrents. Hvis du har muligheten til å bidra til et eksisterende prosjekt, kan det spare deg for mye tid. Imidlertid er det for tiden ikke mange godt organiserte piratspeil der ute. Så la oss si at du bestemmer deg for å distribuere torrenter selv. Prøv å holde disse filene små, slik at de er enkle å speile på andre nettsteder. Du må da seede torrentene selv, mens du fortsatt forblir anonym. Du kan bruke en VPN (med eller uten port forwarding), eller betale med tumlede Bitcoins for en Seedbox. Hvis du ikke vet hva noen av disse begrepene betyr, har du en del lesing å gjøre, siden det er viktig at du forstår risikovurderingene her. Du kan hoste torrentfilene selv på eksisterende torrent-nettsteder. I vårt tilfelle valgte vi å faktisk hoste en nettside, siden vi også ønsket å spre vår filosofi på en klar måte. Du kan gjøre dette selv på en lignende måte (vi bruker Njalla for våre domener og hosting, betalt med tumlede Bitcoins), men ta gjerne kontakt med oss for å la oss hoste dine torrenter. Vi ønsker å bygge en omfattende indeks over piratspeil over tid, hvis denne ideen fanger an. Når det gjelder valg av VPN, er det skrevet mye om dette allerede, så vi gjentar bare det generelle rådet om å velge etter omdømme. Faktiske retts-testede ingen-logg-policyer med lange meritter for å beskytte personvern er det laveste risikovalget, etter vår mening. Merk at selv når du gjør alt riktig, kan du aldri komme til null risiko. For eksempel, når du sår dine torrenter, kan en høyt motivert nasjonalstatlig aktør sannsynligvis se på innkommende og utgående dataflyter for VPN-servere, og utlede hvem du er. Eller du kan bare gjøre en feil på en eller annen måte. Vi har sannsynligvis allerede gjort det, og vil gjøre det igjen. Heldigvis bryr ikke nasjonalstater seg <em>så</em> mye om piratkopiering. En beslutning som må tas for hvert prosjekt, er om det skal publiseres med samme identitet som før, eller ikke. Hvis du fortsetter å bruke det samme navnet, kan feil i operasjonell sikkerhet fra tidligere prosjekter komme tilbake og bite deg. Men å publisere under forskjellige navn betyr at du ikke bygger et varig rykte. Vi valgte å ha sterk operasjonell sikkerhet fra starten av, slik at vi kan fortsette å bruke den samme identiteten, men vi vil ikke nøle med å publisere under et annet navn hvis vi gjør en feil eller om omstendighetene krever det. Å spre ordet kan være vanskelig. Som vi sa, er dette fortsatt et nisjefellesskap. Vi postet opprinnelig på Reddit, men fikk virkelig gjennomslag på Hacker News. For nå er vår anbefaling å poste det på noen få steder og se hva som skjer. Og igjen, kontakt oss. Vi vil gjerne spre ordet om flere piratarkivisme-innsatser. 1. Domenevalg / filosofi Det er ingen mangel på kunnskap og kulturarv som må bevares, noe som kan være overveldende. Derfor er det ofte nyttig å ta et øyeblikk og tenke på hva ditt bidrag kan være. Alle har en annen måte å tenke på dette, men her er noen spørsmål du kan stille deg selv: I vårt tilfelle brydde vi oss spesielt om den langsiktige bevaringen av vitenskap. Vi visste om Library Genesis, og hvordan det ble fullstendig speilet mange ganger ved hjelp av torrents. Vi elsket den ideen. Så en dag prøvde en av oss å finne noen vitenskapelige lærebøker på Library Genesis, men kunne ikke finne dem, noe som satte spørsmålstegn ved hvor komplett det egentlig var. Vi søkte deretter etter disse lærebøkene på nettet, og fant dem andre steder, noe som plantet frøet for vårt prosjekt. Selv før vi visste om Z-Library, hadde vi ideen om ikke å prøve å samle alle disse bøkene manuelt, men å fokusere på å speile eksisterende samlinger, og bidra med dem tilbake til Library Genesis. Hvilke ferdigheter har du som du kan bruke til din fordel? For eksempel, hvis du er en ekspert på nettbasert sikkerhet, kan du finne måter å overvinne IP-blokkeringer for sikre mål. Hvis du er flink til å organisere fellesskap, kan du kanskje samle noen mennesker rundt et mål. Det er nyttig å kunne litt programmering, om ikke annet for å opprettholde god operasjonell sikkerhet gjennom hele prosessen. Hva ville være et område med høy innflytelse å fokusere på? Hvis du skal bruke X timer på piratarkivering, hvordan kan du få mest mulig "utbytte for innsatsen"? Hva er unike måter du tenker på dette? Du kan ha noen interessante ideer eller tilnærminger som andre kanskje har oversett. Hvor mye tid har du til dette? Vårt råd ville være å starte i det små og gjøre større prosjekter etter hvert som du får taket på det, men det kan bli altoppslukende. Hvorfor er du interessert i dette? Hva brenner du for? Hvis vi kan få en gjeng med folk som alle arkiverer de tingene de spesifikt bryr seg om, ville det dekke mye! Du vil vite mye mer enn den gjennomsnittlige personen om din lidenskap, som hva som er viktig data å bevare, hva som er de beste samlingene og nettmiljøene, og så videre. 3. Metadata-skraping Dato lagt til/modifisert: slik at du kan komme tilbake senere og laste ned filer du ikke lastet ned før (selv om du ofte også kan bruke ID eller hash for dette). Hash (md5, sha1): for å bekrefte at du lastet ned filen riktig. ID: kan være en intern ID, men ID-er som ISBN eller DOI er også nyttige. Filnavn / plassering Beskrivelse, kategori, tagger, forfattere, språk, etc. Størrelse: for å beregne hvor mye diskplass du trenger. La oss bli litt mer tekniske her. For å faktisk skrape metadata fra nettsteder, har vi holdt ting ganske enkelt. Vi bruker Python-skript, noen ganger curl, og en MySQL-database for å lagre resultatene i. Vi har ikke brukt noen avansert skrapesoftware som kan kartlegge komplekse nettsteder, siden vi så langt bare har trengt å skrape en eller to typer sider ved å bare enumerere gjennom id-er og analysere HTML. Hvis det ikke er lett å enumerere sider, kan det hende du trenger en skikkelig crawler som prøver å finne alle sidene. Før du begynner å skrape et helt nettsted, prøv å gjøre det manuelt en stund. Gå gjennom noen dusin sider selv, for å få en følelse av hvordan det fungerer. Noen ganger vil du allerede støte på IP-blokkeringer eller annen interessant oppførsel på denne måten. Det samme gjelder for dataskraping: før du går for dypt inn i dette målet, sørg for at du faktisk kan laste ned dataene effektivt. For å omgå restriksjoner, er det noen ting du kan prøve. Er det noen andre IP-adresser eller servere som hoster de samme dataene, men som ikke har de samme restriksjonene? Er det noen API-endepunkter som ikke har restriksjoner, mens andre har? Ved hvilken nedlastingshastighet blir IP-en din blokkert, og hvor lenge? Eller blir du ikke blokkert, men nedjustert? Hva om du oppretter en brukerkonto, hvordan endrer ting seg da? Kan du bruke HTTP/2 for å holde forbindelser åpne, og øker det hastigheten du kan be om sider med? Er det sider som lister opp flere filer samtidig, og er informasjonen som er oppført der tilstrekkelig? Ting du sannsynligvis vil lagre inkluderer: Vi gjør vanligvis dette i to trinn. Først laster vi ned de rå HTML-filene, vanligvis direkte inn i MySQL (for å unngå mange små filer, som vi snakker mer om nedenfor). Deretter, i et separat trinn, går vi gjennom disse HTML-filene og parser dem inn i faktiske MySQL-tabeller. På denne måten trenger du ikke å laste ned alt på nytt fra bunnen av hvis du oppdager en feil i parseringskoden din, siden du bare kan behandle HTML-filene med den nye koden. Det er også ofte lettere å parallellisere behandlingssteget, og dermed spare tid (og du kan skrive behandlingskoden mens skrapingen kjører, i stedet for å måtte skrive begge trinnene samtidig). Til slutt, merk at for noen mål er metadata-skraping alt som finnes. Det finnes noen enorme metadata-samlinger der ute som ikke er ordentlig bevart. Tittel Domenevalg / filosofi: Hvor vil du grovt sett fokusere, og hvorfor? Hva er dine unike lidenskaper, ferdigheter og omstendigheter som du kan bruke til din fordel? Målvalg: Hvilken spesifikk samling vil du speile? Metadata-skraping: Katalogisere informasjon om filene, uten å faktisk laste ned de (ofte mye større) filene selv. Datavalg: Basert på metadata, begrense hvilke data som er mest relevante å arkivere akkurat nå. Det kan være alt, men ofte er det en fornuftig måte å spare plass og båndbredde på. Data-skraping: Faktisk hente dataene. Distribusjon: Pakke det inn i torrenter, kunngjøre det et sted, få folk til å spre det. 5. Datascraping Nå er du klar til å faktisk laste ned dataene i bulk. Som nevnt tidligere, på dette tidspunktet bør du allerede manuelt ha lastet ned en haug med filer, for bedre å forstå oppførselen og begrensningene til målet. Imidlertid vil det fortsatt være overraskelser i vente for deg når du faktisk begynner å laste ned mange filer samtidig. Vårt råd her er hovedsakelig å holde det enkelt. Start med å bare laste ned en haug med filer. Du kan bruke Python, og deretter utvide til flere tråder. Men noen ganger er det enda enklere å generere Bash-filer direkte fra databasen, og deretter kjøre flere av dem i flere terminalvinduer for å skalere opp. Et raskt teknisk triks verdt å nevne her er å bruke OUTFILE i MySQL, som du kan skrive hvor som helst hvis du deaktiverer "secure_file_priv" i mysqld.cnf (og vær sikker på å også deaktivere/overstyre AppArmor hvis du er på Linux). Vi lagrer dataene på enkle harddisker. Start med det du har, og utvid sakte. Det kan være overveldende å tenke på å lagre hundrevis av TB med data. Hvis det er situasjonen du står overfor, legg bare ut et godt delsett først, og i kunngjøringen din be om hjelp til å lagre resten. Hvis du ønsker å skaffe flere harddisker selv, har r/DataHoarder noen gode ressurser for å få gode tilbud. Prøv å ikke bekymre deg for mye om fancy filsystemer. Det er lett å falle ned i kaninhullet med å sette opp ting som ZFS. En teknisk detalj å være oppmerksom på, er at mange filsystemer ikke håndterer mange filer godt. Vi har funnet ut at en enkel løsning er å lage flere kataloger, f.eks. for forskjellige ID-områder eller hash-prefikser. Etter å ha lastet ned dataene, sørg for å sjekke integriteten til filene ved hjelp av hasher i metadataene, hvis tilgjengelig. 2. Målutvelgelse Tilgjengelig: bruker ikke mange lag med beskyttelse for å hindre deg i å skrape deres metadata og data. Spesiell innsikt: du har noe spesiell informasjon om dette målet, som at du på en eller annen måte har spesiell tilgang til denne samlingen, eller du har funnet ut hvordan du kan overvinne deres forsvar. Dette er ikke nødvendig (vårt kommende prosjekt gjør ikke noe spesielt), men det hjelper absolutt! Stor Så, vi har vårt område som vi ser på, nå hvilken spesifikk samling skal vi speile? Det er et par ting som gjør et godt mål: Da vi fant våre vitenskapelige lærebøker på andre nettsteder enn Library Genesis, prøvde vi å finne ut hvordan de kom seg ut på internett. Vi fant deretter Z-Library, og innså at selv om de fleste bøker ikke først dukker opp der, ender de til slutt opp der. Vi lærte om forholdet til Library Genesis, og den (økonomiske) insentivstrukturen og overlegne brukergrensesnittet, som begge gjorde det til en mye mer komplett samling. Vi gjorde deretter noen foreløpige metadata- og datauttrekk, og innså at vi kunne omgå deres IP-nedlastingsgrenser, ved å utnytte en av våre medlemmers spesielle tilgang til mange proxy-servere. Når du utforsker forskjellige mål, er det allerede viktig å skjule sporene dine ved å bruke VPN-er og engangse-postadresser, som vi vil snakke mer om senere. Unik: ikke allerede godt dekket av andre prosjekter. Når vi gjør et prosjekt, har det et par faser: Disse er ikke helt uavhengige faser, og ofte sender innsikter fra en senere fase deg tilbake til en tidligere fase. For eksempel, under metadata-skraping kan du innse at målet du valgte har forsvarsmekanismer utover ditt ferdighetsnivå (som IP-blokkeringer), så du går tilbake og finner et annet mål. - Anna og teamet (<a %(reddit)s>Reddit</a>) Hele bøker kan skrives om <em>hvorfor</em> digital bevaring generelt, og piratarkivisme spesielt, men la oss gi en rask innføring for de som ikke er så kjent. Verden produserer mer kunnskap og kultur enn noen gang før, men også mer av det går tapt enn noen gang før. Menneskeheten stoler i stor grad på selskaper som akademiske forlag, strømmetjenester og sosiale medieselskaper med denne arven, og de har ofte ikke vist seg å være gode forvaltere. Sjekk ut dokumentaren Digital Amnesia, eller egentlig hvilken som helst tale av Jason Scott. Det er noen institusjoner som gjør en god jobb med å arkivere så mye de kan, men de er bundet av loven. Som pirater er vi i en unik posisjon til å arkivere samlinger som de ikke kan røre, på grunn av opphavsrettshåndhevelse eller andre restriksjoner. Vi kan også speile samlinger mange ganger over, over hele verden, og dermed øke sjansene for riktig bevaring. For nå vil vi ikke gå inn i diskusjoner om fordeler og ulemper ved immaterielle rettigheter, moralen ved å bryte loven, funderinger om sensur, eller spørsmålet om tilgang til kunnskap og kultur. Med alt det ute av veien, la oss dykke inn i <em>hvordan</em>. Vi vil dele hvordan vårt team ble piratarkivarer, og de leksjonene vi lærte underveis. Det er mange utfordringer når du begir deg ut på denne reisen, og forhåpentligvis kan vi hjelpe deg gjennom noen av dem. Hvordan bli en piratarkivar Den første utfordringen kan være overraskende. Det er ikke et teknisk problem, eller et juridisk problem. Det er et psykologisk problem. Før vi dykker inn, to oppdateringer om Pirate Library Mirror (EDIT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>): Vi fikk noen ekstremt sjenerøse donasjoner. Den første var $10k fra den anonyme personen som også har støttet "bookwarrior", den opprinnelige grunnleggeren av Library Genesis. Spesiell takk til bookwarrior for å ha tilrettelagt for denne donasjonen. Den andre var ytterligere $10k fra en anonym giver, som tok kontakt etter vår siste utgivelse, og ble inspirert til å hjelpe. Vi hadde også en rekke mindre donasjoner. Tusen takk for all deres sjenerøse støtte. Vi har noen spennende nye prosjekter på gang som dette vil støtte, så følg med. Vi hadde noen tekniske problemer med størrelsen på vår andre utgivelse, men våre torrenter er oppe og seeder nå. Vi fikk også et sjenerøst tilbud fra en anonym person om å seede vår samling på deres svært høyhastighetsservere, så vi gjør en spesiell opplasting til deres maskiner, etter hvilket alle andre som laster ned samlingen bør se en stor forbedring i hastighet. Blogginnlegg Hei, jeg er Anna. Jeg opprettet <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, verdens største skyggebibliotek. Dette er min personlige blogg, der jeg og teamet mitt skriver om piratkopiering, digital bevaring og mer. Koble med meg på <a %(reddit)s>Reddit</a>. Merk at dette nettstedet bare er en blogg. Vi hoster kun våre egne ord her. Ingen torrents eller andre opphavsrettsbeskyttede filer er hostet eller lenket her. <strong>Bibliotek</strong> - Som de fleste biblioteker fokuserer vi primært på skriftlige materialer som bøker. Vi kan utvide til andre typer medier i fremtiden. <strong>Speil</strong> - Vi er strengt tatt et speil av eksisterende biblioteker. Vi fokuserer på bevaring, ikke på å gjøre bøker lett søkbare og nedlastbare (tilgang) eller å fremme et stort fellesskap av mennesker som bidrar med nye bøker (kilde). <strong>Pirat</strong> - Vi bryter bevisst opphavsrettsloven i de fleste land. Dette lar oss gjøre noe som juridiske enheter ikke kan gjøre: sørge for at bøker speiles vidt og bredt. <em>Vi lenker ikke til filene fra denne bloggen. Vennligst finn det selv.</em> - Anna og teamet (<a %(reddit)s>Reddit</a>) Dette prosjektet (REDIGERT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) har som mål å bidra til bevaring og frigjøring av menneskelig kunnskap. Vi gjør vårt lille og ydmyke bidrag, i fotsporene til de store før oss. Fokuset til dette prosjektet er illustrert av navnet: Det første biblioteket vi har speilet er Z-Library. Dette er et populært (og ulovlig) bibliotek. De har tatt Library Genesis-samlingen og gjort den lett søkbar. I tillegg har de blitt veldig effektive til å be om nye bokbidrag, ved å gi insentiver til bidragsytere med ulike fordeler. De bidrar for tiden ikke med disse nye bøkene tilbake til Library Genesis. Og i motsetning til Library Genesis, gjør de ikke samlingen sin lett speilbar, noe som forhindrer bred bevaring. Dette er viktig for deres forretningsmodell, siden de tar betalt for tilgang til samlingen i bulk (mer enn 10 bøker per dag). Vi gjør ikke moralske vurderinger om å ta betalt for bulktilgang til en ulovlig boksamling. Det er uten tvil at Z-Library har vært vellykket i å utvide tilgangen til kunnskap og skaffe flere bøker. Vi er her for å gjøre vår del: sikre den langsiktige bevaringen av denne private samlingen. Vi vil gjerne invitere deg til å hjelpe med å bevare og frigjøre menneskelig kunnskap ved å laste ned og dele våre torrents. Se prosjektsiden for mer informasjon om hvordan dataene er organisert. Vi vil også veldig gjerne invitere deg til å bidra med dine ideer om hvilke samlinger som skal speiles neste gang, og hvordan vi skal gå frem. Sammen kan vi oppnå mye. Dette er bare et lite bidrag blant utallige andre. Takk, for alt du gjør. Vi introduserer Pirate Library Mirror: Bevaring av 7TB med bøker (som ikke er i Libgen) 10% of menneskehetens skrevne arv bevart for alltid <strong>Google.</strong> Tross alt gjorde de denne forskningen for Google Books. Imidlertid er deres metadata ikke tilgjengelig i bulk og ganske vanskelig å skrape. <strong>Ulike individuelle biblioteksystemer og arkiver.</strong> Det finnes biblioteker og arkiver som ikke er indeksert og aggregert av noen av de ovennevnte, ofte fordi de er underfinansiert, eller av andre grunner ikke ønsker å dele sine data med Open Library, OCLC, Google, og så videre. Mange av disse har digitale poster tilgjengelige via internett, og de er ofte ikke veldig godt beskyttet, så hvis du vil hjelpe til og ha det gøy med å lære om rare biblioteksystemer, er disse gode startpunkter. <strong>ISBNdb.</strong> Dette er temaet for dette blogginnlegget. ISBNdb skraper ulike nettsteder for bokmetadata, spesielt prisdata, som de deretter selger til bokhandlere, slik at de kan prise bøkene sine i samsvar med resten av markedet. Siden ISBN-er er ganske universelle nå til dags, har de effektivt bygget en "webside for hver bok". <strong>Open Library.</strong> Som nevnt tidligere, er dette hele deres oppdrag. De har hentet store mengder bibliotekdata fra samarbeidende biblioteker og nasjonale arkiver, og fortsetter å gjøre det. De har også frivillige bibliotekarer og et teknisk team som prøver å fjerne duplikater i registrene og merke dem med alle slags metadata. Best av alt, deres datasett er helt åpent. Du kan enkelt <a %(openlibrary)s>laste det ned</a>. <strong>WorldCat.</strong> Dette er en nettside drevet av den ideelle organisasjonen OCLC, som selger bibliotekstyringssystemer. De samler bokmetadata fra mange biblioteker og gjør det tilgjengelig gjennom WorldCat-nettsiden. Imidlertid tjener de også penger på å selge disse dataene, så de er ikke tilgjengelige for bulk nedlasting. De har noen mer begrensede bulk-datasett tilgjengelig for nedlasting, i samarbeid med spesifikke biblioteker. 1. For en rimelig definisjon av "for alltid". ;) 2. Selvfølgelig er menneskehetens skriftlige arv mye mer enn bøker, spesielt i dag. For denne postens skyld og våre nylige utgivelser fokuserer vi på bøker, men våre interesser strekker seg lenger. 3. Det er mye mer som kan sies om Aaron Swartz, men vi ønsket bare å nevne ham kort, siden han spiller en sentral rolle i denne historien. Etter hvert som tiden går, kan flere komme over navnet hans for første gang, og deretter dykke ned i kaninhullet selv. <strong>Fysiske kopier.</strong> Åpenbart er dette ikke veldig nyttig, siden de bare er duplikater av det samme materialet. Det ville vært kult om vi kunne bevare alle notatene folk gjør i bøker, som Fermats berømte “skriblerier i margen”. Men dessverre vil det forbli en arkivars drøm. <strong>“Utgaver”.</strong> Her teller du hver unik versjon av en bok. Hvis noe ved den er annerledes, som et annet omslag eller et annet forord, teller det som en annen utgave. <strong>Filer.</strong> Når man arbeider med skyggelibraryer som Library Genesis, Sci-Hub eller Z-Library, er det en ekstra betraktning. Det kan være flere skanninger av den samme utgaven. Og folk kan lage bedre versjoner av eksisterende filer, ved å skanne teksten ved hjelp av OCR, eller rette opp sider som ble skannet i en vinkel. Vi ønsker å bare telle disse filene som én utgave, noe som vil kreve god metadata, eller deduplisering ved hjelp av dokumentlikhetsmål. <strong>“Verker”.</strong> For eksempel “Harry Potter og Mysteriekammeret” som et logisk konsept, som omfatter alle versjoner av det, som forskjellige oversettelser og nyutgivelser. Dette er en slags nyttig definisjon, men det kan være vanskelig å trekke grensen for hva som teller. For eksempel vil vi sannsynligvis bevare forskjellige oversettelser, selv om nyutgivelser med bare små forskjeller kanskje ikke er like viktige. - Anna og teamet (<a %(reddit)s>Reddit</a>) Med Pirate Library Mirror (REDIGERT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), er målet vårt å ta alle bøkene i verden og bevare dem for alltid.<sup>1</sup> Mellom våre Z-Library-torrenter og de originale Library Genesis-torrentene har vi 11 783 153 filer. Men hvor mange er det egentlig? Hvis vi dedupliserte disse filene ordentlig, hvilken prosentandel av alle bøkene i verden har vi bevart? Vi vil virkelig gjerne ha noe som dette: La oss starte med noen grove tall: I både Z-Library/Libgen og Open Library er det mange flere bøker enn unike ISBN-er. Betyr det at mange av disse bøkene ikke har ISBN-er, eller mangler ISBN-metadataen rett og slett? Vi kan sannsynligvis svare på dette spørsmålet med en kombinasjon av automatisert matching basert på andre attributter (tittel, forfatter, utgiver, etc), hente inn flere datakilder, og trekke ut ISBN-er fra de faktiske bokskanningene selv (i tilfelle av Z-Library/Libgen). Hvor mange av disse ISBN-ene er unike? Dette illustreres best med et Venn-diagram: For å være mer presis: Vi ble overrasket over hvor lite overlapp det er! ISBNdb har en enorm mengde ISBN-er som ikke dukker opp i verken Z-Library eller Open Library, og det samme gjelder (i mindre, men fortsatt betydelig grad) for de to andre. Dette reiser mange nye spørsmål. Hvor mye ville automatisert matching hjelpe med å merke bøkene som ikke var merket med ISBN-er? Ville det være mange treff og dermed økt overlapp? Også, hva ville skje hvis vi bringer inn et 4. eller 5. datasett? Hvor mye overlapp ville vi se da? Dette gir oss et utgangspunkt. Vi kan nå se på alle ISBN-ene som ikke var i Z-Library-datasettet, og som heller ikke matcher tittel/forfatter-feltene. Det kan gi oss et grep om å bevare alle bøkene i verden: først ved å skrape internett for skanninger, deretter ved å gå ut i det virkelige liv for å skanne bøker. Det sistnevnte kan til og med være folkefinansiert, eller drevet av “dusører” fra folk som ønsker å se bestemte bøker digitalisert. Alt dette er en historie for en annen gang. Hvis du vil hjelpe til med noe av dette — videre analyse; skrape mer metadata; finne flere bøker; OCR’ing av bøker; gjøre dette for andre domener (f.eks. artikler, lydbøker, filmer, TV-serier, magasiner) eller til og med gjøre noen av disse dataene tilgjengelige for ting som ML / store språkmodelltrening — vennligst kontakt meg (<a %(reddit)s>Reddit</a>). Hvis du er spesielt interessert i dataanalyse, jobber vi med å gjøre våre datasets og skript tilgjengelige i et mer brukervennlig format. Det ville vært flott om du bare kunne forke en notatbok og begynne å eksperimentere med dette. Til slutt, hvis du ønsker å støtte dette arbeidet, vennligst vurder å gi en donasjon. Dette er en helt frivillig drevet operasjon, og ditt bidrag gjør en stor forskjell. Hver bit hjelper. Foreløpig tar vi imot donasjoner i krypto; se Doner-siden på Annas Arkiv. For en prosentandel trenger vi en nevner: det totale antallet bøker som noen gang er utgitt.<sup>2</sup> Før Google Books opphørte, prøvde en ingeniør på prosjektet, Leonid Taycher, <a %(booksearch_blogspot)s>å estimere</a> dette tallet. Han kom frem til — med et glimt i øyet — 129 864 880 (“i det minste frem til søndag”). Han estimerte dette tallet ved å bygge en samlet database over alle bøkene i verden. For dette samlet han forskjellige datasett og slo dem sammen på ulike måter. Som en rask digresjon, er det en annen person som forsøkte å katalogisere alle bøkene i verden: Aaron Swartz, den avdøde digitale aktivisten og Reddit-medstifteren.<sup>3</sup> Han <a %(youtube)s>startet Open Library</a> med målet om “en nettside for hver bok som noen gang er utgitt”, ved å kombinere data fra mange forskjellige kilder. Han endte opp med å betale den ultimate prisen for sitt digitale bevaringsarbeid da han ble tiltalt for masse-nedlasting av akademiske artikler, noe som førte til hans selvmord. Det er unødvendig å si at dette er en av grunnene til at gruppen vår er pseudonym, og hvorfor vi er veldig forsiktige. Open Library drives fortsatt heroisk av folkene på Internet Archive, og fortsetter Aarons arv. Vi kommer tilbake til dette senere i dette innlegget. I Google-blogginnlegget beskriver Taycher noen av utfordringene med å estimere dette tallet. Først, hva utgjør en bok? Det finnes noen mulige definisjoner: “Utgaver” synes å være den mest praktiske definisjonen av hva “bøker” er. Praktisk nok brukes denne definisjonen også for å tildele unike ISBN-numre. Et ISBN, eller International Standard Book Number, brukes ofte for internasjonal handel, siden det er integrert med det internasjonale strekkodesystemet (”International Article Number”). Hvis du vil selge en bok i butikker, trenger den en strekkode, så du får et ISBN. Taychers blogginnlegg nevner at selv om ISBN-er er nyttige, er de ikke universelle, siden de egentlig bare ble tatt i bruk på midten av syttitallet, og ikke overalt i verden. Likevel er ISBN sannsynligvis den mest brukte identifikatoren for bokutgaver, så det er vårt beste utgangspunkt. Hvis vi kan finne alle ISBN-ene i verden, får vi en nyttig liste over hvilke bøker som fortsatt trenger å bevares. Så, hvor får vi dataene fra? Det finnes en rekke eksisterende innsats som prøver å samle en liste over alle bøkene i verden: I dette innlegget er vi glade for å kunngjøre en liten utgivelse (sammenlignet med våre tidligere Z-Library-utgivelser). Vi skrapet det meste av ISBNdb, og gjorde dataene tilgjengelige for torrenting på nettstedet til Pirate Library Mirror (REDIGERT: flyttet til <a %(wikipedia_annas_archive)s>Annas Arkiv</a>; vi vil ikke lenke det her direkte, bare søk etter det). Dette er omtrent 30,9 millioner poster (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippet). På deres nettsted hevder de at de faktisk har 32,6 millioner poster, så vi kan ha gått glipp av noen, eller <em>de</em> kan gjøre noe galt. Uansett, for nå vil vi ikke dele nøyaktig hvordan vi gjorde det — vi vil la det være en øvelse for leseren. ;-) Det vi vil dele er noen foreløpige analyser, for å prøve å komme nærmere å estimere antallet bøker i verden. Vi så på tre datasett: dette nye ISBNdb-datasettet, vår opprinnelige utgivelse av metadata som vi skrapet fra Z-Library skyggelibrary (som inkluderer Library Genesis), og Open Library data dump. ISBNdb dump, eller Hvor Mange Bøker Er Bevart For Alltid? Hvis vi skulle deduplisere filene fra skyggelibraryene ordentlig, hvilken prosentandel av alle bøkene i verden har vi bevart? Oppdateringer om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det største virkelig åpne biblioteket i menneskets historie. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Annas Arkiv Beholdere (AAC)</a>, som i hovedsak er <a %(jsonlines)s>JSON Lines</a> komprimert med <a %(zstd)s>Zstandard</a>, pluss noen standardiserte semantikker. Disse beholderne omslutter ulike typer poster, basert på de forskjellige skrapene vi utførte. For et år siden <a %(blog)s>satte vi i gang</a> for å svare på dette spørsmålet: <strong>Hvilken prosentandel av bøker har blitt permanent bevart av skyggebiblioteker?</strong> La oss se på noen grunnleggende opplysninger om dataene: Når en bok kommer inn i et åpen-data skyggebibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, og nå <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, blir den speilet over hele verden (gjennom torrents), og dermed praktisk talt bevart for alltid. For å svare på spørsmålet om hvilken prosentandel av bøker som er bevart, må vi vite nevneren: hvor mange bøker finnes totalt? Og ideelt sett har vi ikke bare et tall, men faktisk metadata. Da kan vi ikke bare matche dem mot skyggebiblioteker, men også <strong>lage en TODO-liste over gjenværende bøker å bevare!</strong> Vi kunne til og med begynne å drømme om en folkefinansiert innsats for å gå ned denne TODO-listen. Vi skrapet <a %(wikipedia_isbndb_com)s>ISBNdb</a>, og lastet ned <a %(openlibrary)s>Open Library-datasettet</a>, men resultatene var utilfredsstillende. Hovedproblemet var at det ikke var mye overlapp av ISBN-er. Se dette Venn-diagrammet fra <a %(blog)s>vårt blogginnlegg</a>: Vi ble veldig overrasket over hvor lite overlapp det var mellom ISBNdb og Open Library, som begge liberalt inkluderer data fra ulike kilder, som webskrap og bibliotekregistre. Hvis de begge gjør en god jobb med å finne de fleste ISBN-er der ute, ville sirklene deres sikkert hatt betydelig overlapp, eller en ville vært en delmengde av den andre. Det fikk oss til å lure på, hvor mange bøker faller <em>helt utenfor disse sirklene</em>? Vi trenger en større database. Det var da vi satte blikket på den største bokdatabasen i verden: <a %(wikipedia_worldcat)s>WorldCat</a>. Dette er en proprietær database av den ideelle organisasjonen <a %(wikipedia_oclc)s>OCLC</a>, som samler metadata fra biblioteker over hele verden, i bytte mot å gi disse bibliotekene tilgang til hele datasettet, og få dem til å vises i sluttbrukernes søkeresultater. Selv om OCLC er en ideell organisasjon, krever deres forretningsmodell at de beskytter databasen sin. Vel, vi beklager å si, venner hos OCLC, vi gir alt bort. :-) I løpet av det siste året har vi nøye skrapet alle WorldCat-poster. Først fikk vi et lykketreff. WorldCat var nettopp i ferd med å rulle ut sin komplette nettsidedesign (i august 2022). Dette inkluderte en betydelig overhaling av deres backend-systemer, som introduserte mange sikkerhetsfeil. Vi grep umiddelbart muligheten, og klarte å skrape hundrevis av millioner (!) av poster på bare noen få dager. Etter det ble sikkerhetsfeil sakte fikset én etter én, inntil den siste vi fant ble lappet for omtrent en måned siden. På det tidspunktet hadde vi stort sett alle postene, og gikk bare for litt høyere kvalitetsposter. Så vi følte det var på tide å slippe det! 1,3 milliarder WorldCat-uttrekk <em><strong>TL;DR:</strong> Annas Arkiv skrapet hele WorldCat (verdens største bibliotekmetadata-samling) for å lage en TODO-liste over bøker som må bevares.</em> WorldCat Advarsel: dette blogginnlegget er foreldet. Vi har bestemt at IPFS ennå ikke er klar for prime time. Vi vil fortsatt lenke til filer på IPFS fra Annas Arkiv når det er mulig, men vi vil ikke lenger hoste det selv, og vi anbefaler heller ikke andre å speile ved hjelp av IPFS. Vennligst se vår Torrents-side hvis du vil hjelpe til med å bevare vår samling. Hjelp til å seede Z-Library på IPFS Last ned fra en partnerserver SciDB Eksternt lån Eksternt lån (utskrift deaktivert) Ekstern nedlasting Utforsk metadata Inneholdt i torrenter Tilbake  (+%(num)s bonus) ubetalt betalt avlyst utløpt venter på Anna for å godkjenne ugyldig Teksten under er på engelsk. Gå Tilbakestill Neste Siste Hvis epost-adressen din ikke fungerer på Libgen forumet, anbefaler vi deg å bruke <a %(a_mail)s>Proton Mail</a> (gratis). Du kan også <a %(a_manual)s>be manuelt</a> for at kontoen din skal bli aktivert. (kan kreve <a %(a_browser)s>nettleserbekreftelse</a> — uendelige nedlastinger!) Rask partnerserver #%(number)s (anbefalt) (litt raskere, men med venteliste) (ingen nettleserbekreftelse nødvendig) (ingen nettleserverifisering eller ventelister) (ingen venteliste, men kan være veldig treg) Treg partnerserver #%(number)s Lydbok Tegneserie Bok (skjønnlitteratur) Bok (sakprosa) Bok (ukjent) Tidsskriftartikkel Magasin Musikalsk partitur Annet Standarddokument Ikke alle sider kunne konverteres til PDF Markert ødelagt på Libgen.li Ikke synlig på Libgen.li Ikke synlig i Libgen.rs skjønnlitteratur Ikke synlig i Libgen.rs sakprosa Kjøring av exiftool mislyktes på denne filen Merket som «dårlig fil» i Z-Library Borte fra Z-Library Merket som «spam» i Z-Library Fil kan ikke åpnes (f.eks. korruptert fil, DRM) Opphavsrettskrav Problemer med nedlasting (f.eks. kan ikke koble til, feilmelding, veldig treg) Feil metadata (f.eks. tittel, beskrivelse, forsidebilde) Annet Dårlig kvalitet (f.eks. problemer med formatering, dårlig kvaltitet på skanning, manglende sider) Spam / fil burde fjernes (f.eks. reklame, støtende innhold) %(amount)s (%(amount_usd)s) %(amount)s totalt %(amount)s%(amount_usd)s totalt Strålende bokorm Heldig biblotekar Dazzling Datahoarder Strålende arkivar Bonusnedlastinger Cerlalc Tsjekkiske metadata DuXiu 读秀 EBSCOhost eBook Index Google Bøker Goodreads HathiTrust IA IA Kontrollert Digital Utlån ISBNdb ISBN GRP Libgen.li Ekskluderer “scimag” Libgen.rs Sakprosa og Skjønnlitteratur Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russlands statsbibliotek Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Opplastinger til AA Z-Library Z-Library Kinesisk Tittel, forfatter, DOI, ISBN, MD5, … Søk Forfatter Beskrivelse og metadata kommentarer Utgave Opprinnelig filnavn Forlag (søk spesifikt felt) Tittel Utgivelsesår Tekniske detaljer Denne mynten har et høyere enn vanlig minimum. Velg en annen varighet eller en annen mynt. Forespørselen kunne ikke bli gjennomført. Prøv igjen i noen få minutter, og hvis det fortsetter å skje kontakt oss på %(email)s med et skjermbilde. En ukjent feil oppsto. Vennligst kontakt oss på %(email)s med et skjermbilde. Feil i betalingsbehandlingen. Vennligst vent et øyeblikk og prøv igjen. Hvis problemet vedvarer i mer enn 24 timer, vennligst kontakt oss på %(email)s med et skjermbilde. Vi kjører en innsamlingsaksjon for <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">sikkerhetskopiering</a> av verdens største tegneserie-skyggebibliotek. Takk for din støtte! <a href="/donate">Doner.</a> Hvis du ikke kan donere, vurder å støtte oss ved å fortelle vennene dine, og følge oss på <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>. Ikke send oss e-post for å <a %(a_request)s>be om bøker</a><br>eller små (<10k) <a %(a_upload)s>opplastinger</a>. Annas arkiv DMCA / opphavsrettskrav Hold kontakten Reddit Alternativer SLUM (%(unaffiliated)s) ikke tilknyttet Annas Arkiv trenger din hjelp! Hvis du donerer nå, får du <strong>dobbelt</strong> så mange raske nedlastinger. Mange prøver å ta oss ned, men vi kjemper tilbake. Hvis du donerer denne måneden, får du <strong>dobbelt</strong> så mange raske nedlastinger. Gyldig til slutten av denne måneden. Å redde menneskelig kunnskap: en flott julegave! Medlemskap vil bli forlenget tilsvarende. Partner-servere er utilgjengelige på grunn av stengte hostingtjenester. De bør være oppe igjen snart. For å øke motstandsdyktigheten til Annas Arkiv, ser vi etter frivillige til å kjøre speil. Vi har en ny donasjonsmetode tilgjengelig: %(method_name)s. Vennligst vurder å <a %(donate_link_open_tag)s>donere</a> — det er ikke billig å drive denne nettsiden, og din donasjon gjør virkelig en forskjell. Tusen takk. Henvis en venn, og både du og din venn får %(percentage)s%% bonus raske nedlastinger! Overrask en du er glad i, og gi dem en konto med medlemskap. Den perfekte Valentinsgaven! Lær mer… Konto Aktivitet Avansert Annas Blogg ↗ Annas Programvare ↗ beta Koder Utforsker Datasett Doner Nedlastede filer FAQ Hjem Forbedre metadata LLM-data Logg inn / Registrer Mine donasjoner Offentlig profil Søk Sikkerhet Torrenter Oversett ↗ Frivillighet & Belønninger Nylige nedlastinger: 📚&nbsp;Verdens største åpen-kildekode åpen-data bibliotek. ⭐️&nbsp;Speiler Sci-Hub, Library Genesis, Z-Library, og flere. 📈&nbsp;%(book_any)s bøker, %(journal_article)s papirer, %(book_comic)s tegneserier, %(magazine)s magasiner — bevart for alltid.  og  og mer DuXiu Internet Archive Lånebibliotek LibGen 📚&nbsp;Det største, virkelige åpne biblioteket i historien. 📈&nbsp;%(book_count)s&nbsp;bøker, %(paper_count)s&nbsp;papirer — bevart for alltid. ⭐️&nbsp;Vi speiler %(libraries)s. Vi skraper og open-source %(scraped)s. All vår kode og data er helt åpen kildekode. OpenLib Sci-Hub ,  📚 Verdens største åpen-kildekode åpen-data bibliotek.<br>⭐️ Speiler Scihub, Libgen, Zlib, og flere. Z-Lib Annas Arkiv Ugyldig forespørsel. Besøk %(websites)s. Verdens største åpen kildekode åpen-data bibliotek. Speiler Sci-Hub, Library Genesis, Z-Library, og mer. Søk i Annas Arkiv Annas Arkiv Vennligst oppdater for å prøve igjen. <a %(a_contact)s>Kontakt oss</a> hvis problemet vedvarer i flere timer. 🔥 Problem med å laste denne siden <li>1. Følg oss på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Spre ordet om Annas Arkiv på Twitter, Reddit, Tiktok, Instagram, på din lokale kafé eller bibliotek, eller hvor enn du går! Vi tror ikke på å holde ting skjult — hvis vi blir tatt ned, dukker vi bare opp et annet sted, siden all vår kode og data er fullstendig åpen kildekode.</li><li>3. Hvis du har mulighet, vurder å <a href="/donate">donere</a>.</li><li>4. Hjelp oss med å <a href="https://translate.annas-software.org/">oversette</a> nettstedet vårt til forskjellige språk.</li><li>5. Hvis du er programvareingeniør, vurder å bidra til vår <a href="https://annas-software.org/">åpen kildekode</a>, eller så våre <a href="/datasets">torrenter</a>.</li> 10. Opprett eller hjelp til med å vedlikeholde Wikipedia-siden for Annas Arkiv på ditt språk. 11. Vi ser etter å plassere små, smakfulle annonser. Hvis du ønsker å annonsere på Annas Arkiv, vennligst gi oss beskjed. 6. Hvis du er en sikkerhetsforsker, kan vi bruke dine ferdigheter både til angrep og forsvar. Sjekk ut vår <a %(a_security)s>Sikkerhet</a>-side. 7. Vi ser etter eksperter på betalinger for anonyme kjøpmenn. Kan du hjelpe oss med å legge til mer praktiske måter å donere på? PayPal, WeChat, gavekort. Hvis du kjenner noen, vennligst kontakt oss. 8. Vi er alltid på utkikk etter mer serverkapasitet. 9. Du kan hjelpe ved å rapportere filproblemer, legge igjen kommentarer og lage lister rett på denne nettsiden. Du kan også hjelpe ved å <a %(a_upload)s>laste opp flere bøker</a>, eller fikse filproblemer eller formatering av eksisterende bøker. For mer omfattende informasjon om hvordan du kan være frivillig, se vår <a %(a_volunteering)s>Frivillighet & Belønninger</a>-side. Vi tror sterkt på fri flyt av informasjon, og bevaring av kunnskap og kultur. Med denne søkemotoren bygger vi på skuldrene til giganter. Vi har dyp respekt for det harde arbeidet til de som har skapt de ulike skyggebibliotekene, og vi håper at denne søkemotoren vil utvide deres rekkevidde. For å holde deg oppdatert på vår fremgang, følg Anna på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> eller <a href="https://t.me/annasarchiveorg">Telegram</a>. For spørsmål og tilbakemeldinger, vennligst kontakt Anna på %(email)s. Konto-ID: %(account_id)s Logg ut ❌ Noe gikk galt. Oppfrisk siden og prøv på nytt. ✅ Du er nå logget ut. Oppfrisk siden for å logge inn igjen. Raske nedlastninger brukt (siste 24 timer): <strong>%(used)s/ %(total)s</strong> Medlemskap: <strong>%(tier_name)s</strong> inntil %(until_date)s <a %(a_extend)s>(forleng)</a> Du kan kombinere flere medlemskap (raske nedlastninger per 24 timer legges sammen). Medlemskap: <strong>Ingen</strong> <a %(a_become)s>(bli medlem)</a> Kontakt Anna hos %(email)s hvis du er interessert i å oppgrade medlemskapet ditt til et høyere nivå. Offentlig profil: %(profile_link)s Hemmelig nøkkel (ikke del!): %(secret_key)s vis Bli med oss her! Oppgrader til et <a %(a_tier)s>høyere nivå</a> for å bli med i vår gruppe. Eksklusiv Telegram-gruppe: %(link)s Konto hvilke nedlastinger? Logg inn Ikke mist nøkkelen din! Ugyldig hemmelig nøkkel. Gjennomgå nøkkelen din og prøv på nytt, eller registrer en ny konto nedenfor. Hemmelig nøkkel Skriv inn den hemmelige nøkkelen din for å logge inn: Gammel epost-basert konto? Skriv inn < %(a_open)s>eposten din her</a>. Registrer ny konto Har du ikke konto enda? Registrasjon vellykket! Din hemmelige nøkkel er: <span %(span_key)s>%(key)s</span> Lagre denne nøklen på et trygt sted. Hvis du mister den, så mister du tilgang til kontoen din. <li %(li_item)s><strong>Bokmerk.</strong> Du kan bokmerke denne siden for å få tak i nøkkelen din igjen.</li><li %(li_item)s><strong>Nedlast.</strong> Klikk <a %(a_download)s>denne lenka</a> for å laste ned nøkkelen din.</li><li %(li_item)s><strong>Passordbehandler.</strong> Bruk et passordsbehandlingsprogram for å lagre nøkkelen din når du skriver det nedenfor.</li> Logg inn / Registrer Nettleserverifisering Advarsel: koden har feil Unicode-tegn i seg, og kan oppføre seg feil i ulike situasjoner. Den rå binæren kan dekodes fra base64-representasjonen i URL-en. Beskrivelse Etikett Prefiks URL for en spesifikk kode Nettsted Koder som starter med “%(prefix_label)s” Vennligst ikke skrap disse sidene. I stedet anbefaler vi <a %(a_import)s>generering</a> eller <a %(a_download)s>nedlasting</a> av våre ElasticSearch- og MariaDB-databaser, og kjøring av vår <a %(a_software)s>åpen kildekode</a>. Rådataene kan manuelt utforskes gjennom JSON-filer som <a %(a_json_file)s>denne</a>. Færre enn %(count)s oppføringer Generisk URL Kodeutforsker Indeks over Utforsk kodene som poster er merket med, etter prefiks. Kolonnen «poster» viser antall poster merket med koder med den gitte prefiksen, slik det vises i søkemotoren (inkludert metadata-only poster). Kolonnen «koder» viser hvor mange faktiske koder som har en gitt prefiks. Kjent kodeprefiks «%(key)s» Mer… Prefiks %(count)s oppføring som samsvarer med “%(prefix_label)s” %(count)s oppføringer som samsvarer med “%(prefix_label)s” koder oppføringer «%%s» vil bli erstattet med kodens verdi Søk i Annas Arkiv Koder URL for spesifikk kode: “%(url)s” Denne siden kan ta litt tid å generere, og derfor krever den en Cloudflare captcha. <a %(a_donate)s>Medlemmer</a> kan hoppe over captchaen. Misbruk rapportert: Bedre versjon Ønsker du å rapportere denne brukeren for misbruk eller upassende oppførsel? Filproblem: %(file_issue)s skjult kommentar Svar Rapporter misbruk Du rapporterte denne brukeren for misbruk. Opphavsrettskrav til denne e-posten vil bli ignorert; bruk skjemaet i stedet. Vis e-post Vi setter stor pris på dine tilbakemeldinger og spørsmål! Men på grunn av mengden spam og tullete e-poster vi mottar, vennligst kryss av boksene for å bekrefte at du forstår disse betingelsene for å kontakte oss. Alle andre måter å kontakte oss om opphavsrettskrav vil automatisk bli slettet. For DMCA / opphavsrettskrav, bruk <a %(a_copyright)s>dette skjemaet</a>. Kontakt e-post URL-er på Annas Arkiv (påkrevd). Én per linje. Vennligst inkluder kun URL-er som beskriver nøyaktig samme utgave av en bok. Hvis du ønsker å gjøre krav på flere bøker eller flere utgaver, vennligst send inn dette skjemaet flere ganger. Krav som samler flere bøker eller utgaver sammen vil bli avvist. Adresse (påkrevd) Klar beskrivelse av kildematerialet (påkrevd) E-post (påkrevd) URL-er til kildematerialet, én per linje (påkrevd). Vennligst inkluder så mange som mulig, for å hjelpe oss med å verifisere ditt krav (f.eks. Amazon, WorldCat, Google Books, DOI). ISBN-er for kildematerialet (hvis aktuelt). Én per linje. Vennligst inkluder kun de som nøyaktig samsvarer med utgaven du rapporterer et opphavsrettskrav for. Ditt navn (påkrevd) ❌ Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. ✅ Takk for at du sendte inn ditt opphavsrettskrav. Vi vil gjennomgå det så snart som mulig. Vennligst last inn siden på nytt for å sende inn et nytt krav. <a %(a_openlib)s>Open Library</a> URL-er for kildematerialet, én per linje. Vennligst ta deg tid til å søke i Open Library etter ditt kildemateriale. Dette vil hjelpe oss med å verifisere ditt krav. Telefonnummer (påkrevd) Erklæring og signatur (påkrevd) Send inn krav Hvis du har et DCMA- eller annet opphavsrettskrav, vennligst fyll ut dette skjemaet så nøyaktig som mulig. Hvis du støter på problemer, vennligst kontakt oss på vår dedikerte DMCA-adresse: %(email)s. Vær oppmerksom på at krav sendt til denne adressen ikke vil bli behandlet, den er kun for spørsmål. Vennligst bruk skjemaet nedenfor for å sende inn dine krav. DMCA / Skjema for opphavsrettskrav Eksempelpost på Anna’s Archive Torrenter av Anna’s Archive Anna’s Archive Containers format Skript for import av metadata Hvis du er interessert i å speile dette datasettet for <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-trening</a>, vennligst kontakt oss. Sist oppdatert: %(date)s Hoved %(source)s nettsted Metadata-dokumentasjon (de fleste feltene) Filer speilet av Anna’s Archive: %(count)s (%(percent)s%%) Ressurser Totalt antall filer: %(count)s Total filstørrelse: %(size)s Vårt blogginnlegg om disse dataene <a %(duxiu_link)s>Duxiu</a> er en massiv database med skannede bøker, opprettet av <a %(superstar_link)s>SuperStar Digital Library Group</a>. De fleste er akademiske bøker, skannet for å gjøre dem tilgjengelige digitalt for universiteter og biblioteker. For vårt engelsktalende publikum har <a %(princeton_link)s>Princeton</a> og <a %(uw_link)s>University of Washington</a> gode oversikter. Det finnes også en utmerket artikkel som gir mer bakgrunn: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Bøkene fra Duxiu har lenge blitt piratkopiert på det kinesiske internettet. Vanligvis blir de solgt for mindre enn en dollar av forhandlere. De distribueres typisk ved hjelp av den kinesiske ekvivalenten til Google Drive, som ofte har blitt hacket for å tillate mer lagringsplass. Noen tekniske detaljer kan finnes <a %(link1)s>her</a> og <a %(link2)s>her</a>. Selv om bøkene har blitt semi-offentlig distribuert, er det ganske vanskelig å skaffe dem i bulk. Vi hadde dette høyt på vår TODO-liste, og allokerte flere måneder med fulltidsarbeid for det. Men, sent i 2023 tok en utrolig, fantastisk og talentfull frivillig kontakt med oss og fortalte at de allerede hadde gjort alt dette arbeidet — til stor kostnad. De delte hele samlingen med oss, uten å forvente noe i retur, bortsett fra garantien om langsiktig bevaring. Virkelig bemerkelsesverdig. Mer informasjon fra våre frivillige (rå notater): Tilpasset fra vårt <a %(a_href)s>blogginnlegg</a>. DuXiu 读秀 %(count)s fil %(count)s filer Dette datasettet er nært knyttet til <a %(a_datasets_openlib)s>Open Library-datasettet</a>. Det inneholder en skraping av all metadata og en stor del av filer fra IA’s Controlled Digital Lending Library. Oppdateringer blir utgitt i <a %(a_aac)s>Annas Arkiv Containers-format</a>. Disse postene refereres direkte fra Open Library-datasettet, men inneholder også poster som ikke er i Open Library. Vi har også en rekke datafiler skrapet av samfunnsmedlemmer gjennom årene. Samlingen består av to deler. Du trenger begge delene for å få alle data (unntatt erstattede torrenter, som er krysset ut på torrentsiden). Digitalt Utlånsbibliotek vår første utgivelse, før vi standardiserte på <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Inneholder metadata (som json og xml), pdf-er (fra acsm og lcpdf digitale utlånssystemer), og omslagsminiatyrer. inkrementelle nye utgivelser, ved bruk av AAC. Inneholder kun metadata med tidsstempler etter 2023-01-01, siden resten allerede er dekket av “ia”. Også alle pdf-filer, denne gangen fra acsm og “bookreader” (IAs nettleser) utlånssystemer. Til tross for at navnet ikke er helt riktig, fyller vi fortsatt bookreader-filer inn i ia2_acsmpdf_files-samlingen, siden de er gjensidig utelukkende. IA Kontrollert Digital Utlån 98%%+ av filene er søkbare. Vårt oppdrag er å arkivere alle bøkene i verden (samt artikler, magasiner, osv.), og gjøre dem bredt tilgjengelige. Vi mener at alle bøker bør speiles vidt og bredt, for å sikre redundans og motstandsdyktighet. Dette er grunnen til at vi samler filer fra en rekke kilder. Noen kilder er helt åpne og kan speiles i bulk (som Sci-Hub). Andre er lukkede og beskyttende, så vi prøver å skrape dem for å «frigjøre» bøkene deres. Andre faller et sted imellom. Alle våre data kan <a %(a_torrents)s>torrentes</a>, og all vår metadata kan <a %(a_anna_software)s>genereres</a> eller <a %(a_elasticsearch)s>lastes ned</a> som ElasticSearch- og MariaDB-databaser. Rådataene kan manuelt utforskes gjennom JSON-filer som <a %(a_dbrecord)s>denne</a>. Metadata ISBN-nettsted Sist oppdatert: %(isbn_country_date)s (%(link)s) Ressurser Det internasjonale ISBN-byrået utgir jevnlig områdene som er tildelt nasjonale ISBN-byråer. Fra dette kan vi utlede hvilket land, region eller språkgruppe denne ISBN tilhører. Vi bruker for øyeblikket disse dataene indirekte, gjennom <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket. ISBN-landinformasjon Dette er en dump av mange kall til isbndb.com i løpet av september 2022. Vi prøvde å dekke alle ISBN-områder. Dette er omtrent 30,9 millioner poster. På deres nettsted hevder de at de faktisk har 32,6 millioner poster, så vi kan ha gått glipp av noen, eller <em>de</em> kan gjøre noe feil. JSON-responsene er stort sett rå fra deres server. Et datakvalitetsproblem vi la merke til, er at for ISBN-13-numre som starter med et annet prefiks enn “978-”, inkluderer de fortsatt et “isbn”-felt som ganske enkelt er ISBN-13-nummeret med de første 3 sifrene kuttet av (og kontrollsifferet rekalkulert). Dette er åpenbart feil, men det er slik de ser ut til å gjøre det, så vi endret det ikke. Et annet potensielt problem du kan støte på, er at “isbn13”-feltet har duplikater, så du kan ikke bruke det som en primærnøkkel i en database. Kombinasjonen av “isbn13”+“isbn”-feltene ser imidlertid ut til å være unik. Utgivelse 1 (2022-10-31) Fiksjonstorrents er forsinket (selv om ID-er ~4-6M ikke er torrentet siden de overlapper med våre Zlib-torrents). Vårt blogginnlegg om utgivelsen av tegneserier Tegneserie-torrenter på Annas Arkiv For bakgrunnshistorien til de forskjellige Library Genesis-forgreningene, se siden for <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li inneholder det meste av det samme innholdet og metadataene som Libgen.rs, men har noen samlinger i tillegg, nemlig tegneserier, magasiner og standarddokumenter. Det har også integrert <a %(a_scihub)s>Sci-Hub</a> i sine metadata og søkemotor, som vi bruker for vår database. Metadataene for dette biblioteket er fritt tilgjengelige <a %(a_libgen_li)s>på libgen.li</a>. Imidlertid er denne serveren treg og støtter ikke gjenopptakelse av brutte forbindelser. De samme filene er også tilgjengelige på <a %(a_ftp)s>en FTP-server</a>, som fungerer bedre. Sakprosa ser også ut til å ha avviket, men uten nye torrents. Det ser ut til at dette har skjedd siden tidlig i 2022, selv om vi ikke har verifisert dette. Ifølge Libgen.li-administratoren bør «fiction_rus»-samlingen (russisk skjønnlitteratur) dekkes av regelmessig utgitte torrenter fra <a %(a_booktracker)s>booktracker.org</a>, spesielt <a %(a_flibusta)s>flibusta</a> og <a %(a_librusec)s>lib.rus.ec</a> torrenter (som vi speiler <a %(a_torrents)s>her</a>, selv om vi ennå ikke har fastslått hvilke torrenter som tilsvarer hvilke filer). Skjønnlitteratursamlingen har sine egne torrenter (avviket fra <a %(a_href)s>Libgen.rs</a>) som starter på %(start)s. Visse områder uten torrenter (som skjønnlitteraturområder f_3463000 til f_4260000) er sannsynligvis Z-Library (eller andre duplikat) filer, selv om vi kanskje vil gjøre noe deduplisering og lage torrenter for lgli-unike filer i disse områdene. Statistikk for alle samlinger kan finnes <a %(a_href)s>på libgens nettsted</a>. Torrenter er tilgjengelige for det meste av det ekstra innholdet, spesielt torrenter for tegneserier, magasiner og standarddokumenter har blitt utgitt i samarbeid med Annas Arkiv. Merk at torrentfilene som refererer til “libgen.is” eksplisitt er speil av <a %(a_libgen)s>Libgen.rs</a> (“.is” er et annet domene brukt av Libgen.rs). En nyttig ressurs for bruk av metadata er <a %(a_href)s>denne siden</a>. %(icon)s Deres «fiction_rus»-samling (russisk skjønnlitteratur) har ingen dedikerte torrenter, men dekkes av torrenter fra andre, og vi holder en <a %(fiction_rus)s>speil</a>. Russiske skjønnlitteraturtorrenter på Annas Arkiv Skjønnlitteratur-torrenter på Annas Arkiv Diskusjonsforum Metadata Metadata via FTP Magasin-torrenter på Annas Arkiv Metadata feltinformasjon Speil av andre torrenter (og unike skjønnlitteratur- og tegneserie-torrenter) Standarddokumenttorrenter på Annas Arkiv Libgen.li Torrenter av Annas Arkiv (bokomslag) Library Genesis er kjent for allerede generøst å gjøre sine data tilgjengelige i bulk gjennom torrenter. Vår Libgen-samling består av tilleggsdata som de ikke utgir direkte, i samarbeid med dem. Stor takk til alle involverte i Library Genesis for å samarbeide med oss! Vår blogg om utgivelsen av bokomslag Denne siden handler om “.rs”-versjonen. Den er kjent for konsekvent å publisere både metadataene og hele innholdet i bokkatalogen sin. Boksamlingen er delt mellom en skjønnlitterær og en ikke-skjønnlitterær del. En nyttig ressurs for bruk av metadata er <a %(a_metadata)s>denne siden</a> (blokkerer IP-områder, VPN kan være nødvendig). Fra og med 2024-03, blir nye torrenter lagt ut i <a %(a_href)s>denne forumtråden</a> (blokkerer IP-områder, VPN kan være nødvendig). Skjønnlitteratur-torrenter på Annas Arkiv Libgen.rs Skjønnlitteratur-torrenter Libgen.rs Diskusjonsforum Libgen.rs Metadata Libgen.rs metadata feltinformasjon Libgen.rs Sakprosa-torrenter Sakprosa-torrenter på Annas Arkiv %(example)s for en skjønnlitterær bok. Denne <a %(blog_post)s>første utgivelsen</a> er ganske liten: omtrent 300GB med bokomslag fra Libgen.rs-forken, både skjønnlitteratur og sakprosa. De er organisert på samme måte som de vises på libgen.rs, f.eks.: %(example)s for en sakprosabok. Akkurat som med Z-Library-samlingen, har vi lagt dem alle i en stor .tar-fil, som kan monteres ved hjelp av <a %(a_ratarmount)s>ratarmount</a> hvis du vil servere filene direkte. Utgivelse 1 (%(date)s) Den korte historien om de forskjellige Library Genesis (eller “Libgen”) forgreningene, er at over tid, de forskjellige personene involvert i Library Genesis hadde en uenighet, og gikk hver sin vei. Ifølge dette <a %(a_mhut)s>forumpostet</a> var Libgen.li opprinnelig hostet på «http://free-books.dontexist.com». “.fun”-versjonen ble opprettet av den opprinnelige grunnleggeren. Den blir fornyet til fordel for en ny, mer distribuert versjon. <a %(a_li)s>“.li”-versjonen</a> har en massiv samling av tegneserier, samt annet innhold, som ikke (ennå) er tilgjengelig for bulk-nedlasting gjennom torrenter. Den har en egen torrent-samling av skjønnlitterære bøker, og den inneholder metadataene til <a %(a_scihub)s>Sci-Hub</a> i sin database. “.rs”-versjonen har svært lignende data, og utgir mest konsekvent samlingen sin i bulk-torrenter. Den er grovt delt inn i en “skjønnlitteratur” og en “ikke-skjønnlitteratur” seksjon. Opprinnelig på «http://gen.lib.rus.ec». <a %(a_zlib)s>Z-Library</a> er på en måte også en forgrening av Library Genesis, selv om de brukte et annet navn for prosjektet sitt. Libgen.rs Vi beriker også samlingen vår med kun metadata-kilder, som vi kan matche til filer, for eksempel ved bruk av ISBN-numre eller andre felt. Nedenfor er en oversikt over disse. Igjen, noen av disse kildene er helt åpne, mens for andre må vi skrape dem. Merk at i metadatasøk viser vi de originale postene. Vi gjør ingen sammenslåing av poster. Kun metadata-kilder Open Library er et åpen kildekode-prosjekt av Internet Archive for å katalogisere hver bok i verden. Det har en av verdens største bokskanningsoperasjoner, og har mange bøker tilgjengelig for digital utlån. Dets bokmetadata-katalog er fritt tilgjengelig for nedlasting, og er inkludert på Annas Arkiv (men ikke for øyeblikket i søk, bortsett fra hvis du eksplisitt søker etter en Open Library ID). Open Library Ekskluderer duplikater Sist oppdatert Prosentandel av antall filer %% speilet av AA / tilgjengelige torrents Størrelse Kilde Nedenfor er en rask oversikt over kildene til filene på Annas Arkiv. Siden skyggebibliotekene ofte synkroniserer data fra hverandre, er det betydelig overlapping mellom bibliotekene. Derfor stemmer ikke tallene overens med totalen. Prosentandelen “speilet og sådd av Annas Arkiv” viser hvor mange filer vi speiler selv. Vi sår disse filene i bulk gjennom torrenter, og gjør dem tilgjengelige for direkte nedlasting gjennom partnernettsteder. Oversikt Totalt Torrenter på Annas Arkiv For en bakgrunn om Sci-Hub, vennligst se på dens <a %(a_scihub)s>offisielle nettside</a>, <a %(a_wikipedia)s>Wikipedia-side</a>, og denne <a %(a_radiolab)s>podcast-intervjuet</a>. Merk at Sci-Hub har vært <a %(a_reddit)s>frosset siden 2021</a>. Det var frosset før, men i 2021 ble noen millioner artikler lagt til. Fortsatt blir et begrenset antall artikler lagt til Libgen “scimag”-samlingene, men ikke nok til å rettferdiggjøre nye bulk-torrenter. Vi bruker Sci-Hub metadata som levert av <a %(a_libgen_li)s>Libgen.li</a> i dens “scimag”-samling. Vi bruker også <a %(a_dois)s>dois-2022-02-12.7z</a> datasettet. Merk at “smarch”-torrentene er <a %(a_smarch)s>utdatert</a> og derfor ikke inkludert i vår torrent-liste. Torrenter på Libgen.li Torrenter på Libgen.rs Metadata og torrenter Oppdateringer på Reddit Podcast-intervju Wikipedia-side Sci-Hub Sci-Hub: frosset siden 2021; mest tilgjengelig gjennom torrenter Libgen.li: mindre tillegg siden da</div> Noen kildesamlinger fremmer deling av dataene sine i bulk gjennom torrents, mens andre ikke deler samlingen sin like lett. I sistnevnte tilfelle prøver Anna’s Archive å skrape samlingene deres og gjøre dem tilgjengelige (se vår <a %(a_torrents)s>Torrents</a>-side). Det finnes også mellomløsninger, for eksempel der kildesamlinger er villige til å dele, men ikke har ressursene til å gjøre det. I slike tilfeller prøver vi også å hjelpe til. Nedenfor er en oversikt over hvordan vi samhandler med de forskjellige kildesamlingene. Kildebiblioteker %(icon)s Ulike fil-databaser spredt rundt på det kinesiske internett; ofte betalte databaser %(icon)s De fleste filer kun tilgjengelig med premium BaiduYun-kontoer; langsomme nedlastingshastigheter. %(icon)s Anna’s Arkiv administrerer en samling av <a %(duxiu)s>DuXiu-filer</a> %(icon)s Ulike metadata-databaser spredt rundt på det kinesiske internett; ofte betalte databaser %(icon)s Ingen lett tilgjengelige metadata-dumper for hele samlingen deres. %(icon)s Annas Arkiv administrerer en samling av <a %(duxiu)s>DuXiu-metadata</a> Filer %(icon)s Filer kun tilgjengelig for utlån på begrenset basis, med ulike tilgangsrestriksjoner %(icon)s Annas Arkiv administrerer en samling av <a %(ia)s>IA-filer</a> %(icon)s Noe metadata tilgjengelig gjennom <a %(openlib)s>Open Library database-dumper</a>, men de dekker ikke hele IA-samlingen %(icon)s Ingen lett tilgjengelige metadata-dumper for hele samlingen deres %(icon)s Annas Arkiv administrerer en samling av <a %(ia)s>IA-metadata</a> Sist oppdatert %(icon)s Annas Arkiv og Libgen.li administrerer i fellesskap samlinger av <a %(comics)s>tegneserier</a>, <a %(magazines)s>magasiner</a>, <a %(standarts)s>standarddokumenter</a> og <a %(fiction)s>skjønnlitteratur (avviket fra Libgen.rs)</a>. %(icon)s Ikke-fiksjon-torrenter deles med Libgen.rs (og speiles <a %(libgenli)s>her</a>). %(icon)s Kvartalsvise <a %(dbdumps)s>HTTP-database-dumper</a> %(icon)s Automatiserte torrenter for <a %(nonfiction)s>Sakprosa</a> og <a %(fiction)s>Skjønnlitteratur</a> %(icon)s Annas Arkiv administrerer en samling av <a %(covers)s>bokomslagstorrenter</a> %(icon)s Daglige <a %(dbdumps)s>HTTP database dumps</a> Metadata %(icon)s Månedlige <a %(dbdumps)s>databasedumper</a> %(icon)s Datatorrenter tilgjengelig <a %(scihub1)s>her</a>, <a %(scihub2)s>her</a> og <a %(libgenli)s>her</a> %(icon)s Noen nye filer <a %(libgenrs)s>blir</a> <a %(libgenli)s>lagt til</a> i Libgens “scimag”, men ikke nok til å rettferdiggjøre nye torrenter %(icon)s Sci-Hub har frosset nye filer siden 2021. %(icon)s Metadata-dumper tilgjengelig <a %(scihub1)s>her</a> og <a %(scihub2)s>her</a>, samt som en del av <a %(libgenli)s>Libgen.li-databasen</a> (som vi bruker) Kilde %(icon)s Ulike mindre eller engangskilder. Vi oppfordrer folk til å laste opp til andre skyggebiblioteker først, men noen ganger har folk samlinger som er for store til at andre kan sortere gjennom dem, men ikke store nok til å fortjene sin egen kategori. %(icon)s Ikke tilgjengelig direkte i bulk, beskyttet mot scraping %(icon)s Annas Arkiv administrerer en samling av <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Annas Arkiv og Z-Library administrerer i fellesskap en samling av <a %(metadata)s>Z-Library metadata</a> og <a %(files)s>Z-Library filer</a> Datasets Vi kombinerer alle de ovennevnte kildene til én enhetlig database som vi bruker til å betjene denne nettsiden. Denne enhetlige databasen er ikke tilgjengelig direkte, men siden Anna’s Archive er fullstendig åpen kildekode, kan den ganske enkelt <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>lastes ned</a> som ElasticSearch- og MariaDB-databaser. Skriptene på den siden vil automatisk laste ned all nødvendig metadata fra de nevnte kildene. Hvis du vil utforske dataene våre før du kjører disse skriptene lokalt, kan du se på JSON-filene våre, som lenker videre til andre JSON-filer. <a %(a_json)s>Denne filen</a> er et godt utgangspunkt. Enhetlig database Torrenter av Annas Arkiv bla søk Ulike mindre eller engangs kilder. Vi oppfordrer folk til å laste opp til andre skyggebiblioteker først, men noen ganger har folk samlinger som er for store for andre å sortere gjennom, men ikke store nok til å fortjene sin egen kategori. Oversikt fra <a %(a1)s>Datasets-siden</a>. Fra <a %(a_href)s>aaaaarg.fail</a>. Ser ut til å være ganske komplett. Fra vår frivillige “cgiym”. Fra en <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ganske høy overlapp med eksisterende papirersamlinger, men svært få MD5-treff, så vi bestemte oss for å beholde den helt. Skraping av <q>iRead eBooks</q> (= fonetisk <q>ai rit i-books</q>; airitibooks.com), av frivillig <q>j</q>. Tilsvarer <q>airitibooks</q> metadata i <a %(a1)s><q>Andre metadata-skrapinger</q></a>. Fra en samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvis fra den opprinnelige kilden, delvis fra the-eye.eu, delvis fra andre speil. Fra en privat boktorrent-nettside, <a %(a_href)s>Bibliotik</a> (ofte referert til som “Bib”), hvor bøker ble samlet i torrents etter navn (A.torrent, B.torrent) og distribuert gjennom the-eye.eu. Fra vår frivillige “bpb9v”. For mer informasjon om <a %(a_href)s>CADAL</a>, se notatene på vår <a %(a_duxiu)s>DuXiu-datasett-side</a>. Mer fra vår frivillige “bpb9v”, hovedsakelig DuXiu-filer, samt en mappe “WenQu” og “SuperStar_Journals” (SuperStar er selskapet bak DuXiu). Fra vår frivillige “cgiym”, kinesiske tekster fra ulike kilder (representert som underkataloger), inkludert fra <a %(a_href)s>China Machine Press</a> (en stor kinesisk forlegger). Ikke-kinesiske samlinger (representert som underkataloger) fra vår frivillige “cgiym”. Skraping av bøker om kinesisk arkitektur, av frivillig <q>cm</q>: <q>Jeg fikk tak i det ved å utnytte en nettverkssårbarhet hos forlaget, men den smutthullet er nå lukket</q>. Tilsvarer <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andre metadata-skrapinger</q></a>. Bøker fra det akademiske forlaget <a %(a_href)s>De Gruyter</a>, samlet fra noen få store torrents. Skraping av <a %(a_href)s>docer.pl</a>, en polsk fildelingsside med fokus på bøker og andre skriftlige verk. Skrapet sent i 2023 av frivillig “p”. Vi har ikke god metadata fra den opprinnelige nettsiden (ikke engang filutvidelser), men vi filtrerte for boklignende filer og klarte ofte å trekke ut metadata fra filene selv. DuXiu-epubs, direkte fra DuXiu, samlet av frivillig “w”. Bare nyere DuXiu-bøker er tilgjengelige direkte gjennom e-bøker, så de fleste av disse må være nyere. Gjenværende DuXiu-filer fra frivillig “m”, som ikke var i DuXius proprietære PDG-format (hoved-<a %(a_href)s>DuXiu-datasettet</a>). Samlet fra mange opprinnelige kilder, dessverre uten å bevare disse kildene i filbanen. <span></span> <span></span> <span></span> Skraping av erotiske bøker, av frivillig <q>do no harm</q>. Tilsvarer <q>hentai</q> metadata i <a %(a1)s><q>Andre metadata-skrapinger</q></a>. <span></span> <span></span> Samling skrapet fra en japansk mangaforlegger av frivillig “t”. <a %(a_href)s>Utvalgte rettsarkiver fra Longquan</a>, levert av frivillig “c”. Skraping av <a %(a_href)s>magzdb.org</a>, en alliert av Library Genesis (den er lenket på libgen.rs-hjemmesiden) men som ikke ønsket å levere filene sine direkte. Skaffet av frivillig “p” sent i 2023. <span></span> Ulike små opplastinger, for små til å være egne undersamlinger, men representert som kataloger. E-bøker fra AvaxHome, et russisk fildelingsnettsted. Arkiv av aviser og magasiner. Tilsvarer <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andre metadata-skrapinger</q></a>. Skraping av <a %(a1)s>Philosophy Documentation Center</a>. Samling av frivillig “o” som samlet polske bøker direkte fra opprinnelige utgivelses (“scene”) nettsider. Kombinerte samlinger av <a %(a_href)s>shuge.org</a> av frivillige “cgiym” og “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (oppkalt etter det fiktive biblioteket), skrapet i 2022 av frivillig “t”. <span></span> <span></span> <span></span> Under-under-samlinger (representert som kataloger) fra frivillig “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (av <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mitt lille bokrom — woz9ts: “Dette nettstedet fokuserer hovedsakelig på å dele høykvalitets e-bokfiler, noen av dem er satt opp av eieren selv. Eieren ble <a %(a_arrested)s>arrestert</a> i 2019 og noen laget en samling av filene han delte.”). Gjenværende DuXiu-filer fra frivillig “woz9ts”, som ikke var i DuXius proprietære PDG-format (fortsatt å konvertere til PDF). "Opplastings"-samlingen er delt opp i mindre underkolleksjoner, som er angitt i AACIDs og torrentnavn. Alle underkolleksjoner ble først deduplisert mot hovedsamlingen, selv om metadata "upload_records" JSON-filer fortsatt inneholder mange referanser til de opprinnelige filene. Ikke-bokfiler ble også fjernet fra de fleste underkolleksjoner, og er vanligvis <em>ikke</em> notert i "upload_records" JSON. Underkolleksjonene er: Notater Underkolleksjon Mange underkolleksjoner består selv av under-underkolleksjoner (f.eks. fra forskjellige opprinnelige kilder), som er representert som kataloger i "filepath"-feltene. Opplastinger til Annas Arkiv Vår bloggpost om disse dataene <a %(a_worldcat)s>WorldCat</a> er en proprietær database av den ideelle organisasjonen <a %(a_oclc)s>OCLC</a>, som samler metadata-poster fra biblioteker over hele verden. Det er sannsynligvis den største bibliotek-metadata-samlingen i verden. I oktober 2023 <a %(a_scrape)s>utga</a> vi en omfattende skraping av OCLC (WorldCat)-databasen, i <a %(a_aac)s>Annas Arkiv Containers-format</a>. Oktober 2023, første utgivelse: OCLC (WorldCat) Torrenter av Annas Arkiv Eksempelpost på Annas Arkiv (original samling) Eksempelpost på Annas Arkiv (“zlib3” samling) Torrenter av Annas Arkiv (metadata + innhold) Blogginnlegg om Utgivelse 1 Blogginnlegg om Utgivelse 2 Sent i 2022 ble de påståtte grunnleggerne av Z-Library arrestert, og domenene ble beslaglagt av amerikanske myndigheter. Siden da har nettstedet sakte men sikkert kommet tilbake på nettet. Det er ukjent hvem som driver det nå. Oppdatering per februar 2023. Z-Library har sine røtter i <a %(a_href)s>Library Genesis</a>-samfunnet, og startet opprinnelig med deres data. Siden da har det blitt betydelig profesjonalisert, og har et mye mer moderne grensesnitt. De er derfor i stand til å få mange flere donasjoner, både økonomisk for å fortsette å forbedre nettstedet, samt donasjoner av nye bøker. De har samlet en stor samling i tillegg til Library Genesis. Samlingen består av tre deler. De originale beskrivelsessidene for de to første delene er bevart nedenfor. Du trenger alle tre delene for å få all data (unntatt erstattede torrenter, som er krysset ut på torrentsiden). %(title)s: vår første utgivelse. Dette var den aller første utgivelsen av det som da ble kalt “Pirate Library Mirror” (“pilimi”). %(title)s: andre utgivelse, denne gangen med alle filer pakket i .tar-filer. %(title)s: inkrementelle nye utgivelser, ved bruk av <a %(a_href)s>Annas Arkiv Beholdere (AAC) format</a>, nå utgitt i samarbeid med Z-Library-teamet. Det opprinnelige speilet ble møysommelig innhentet i løpet av 2021 og 2022. På dette tidspunktet er det litt utdatert: det gjenspeiler tilstanden til samlingen i juni 2021. Vi vil oppdatere dette i fremtiden. Akkurat nå fokuserer vi på å få ut denne første utgivelsen. Siden Library Genesis allerede er bevart med offentlige torrenter, og er inkludert i Z-Library, gjorde vi en grunnleggende deduplisering mot Library Genesis i juni 2022. For dette brukte vi MD5-hasher. Det er sannsynligvis mye mer duplikatinnhold i biblioteket, som flere filformater med samme bok. Dette er vanskelig å oppdage nøyaktig, så vi gjør det ikke. Etter dedupliseringen sitter vi igjen med over 2 millioner filer, totalt litt under 7TB. Samlingen består av to deler: en MySQL “.sql.gz” dump av metadata, og de 72 torrentfilene på rundt 50-100GB hver. Metadataen inneholder data som rapportert av Z-Library-nettstedet (tittel, forfatter, beskrivelse, filtype), samt den faktiske filstørrelsen og md5sum som vi observerte, siden disse noen ganger ikke stemmer overens. Det ser ut til å være områder av filer der Z-Library selv har feil metadata. Vi kan også ha lastet ned filer feil i noen isolerte tilfeller, som vi vil prøve å oppdage og rette i fremtiden. De store torrentfilene inneholder de faktiske bokdataene, med Z-Library ID som filnavn. Filutvidelsene kan rekonstrueres ved hjelp av metadata-dumpen. Samlingen er en blanding av sakprosa og skjønnlitteratur (ikke adskilt som i Library Genesis). Kvaliteten varierer også mye. Denne første utgivelsen er nå fullt tilgjengelig. Merk at torrentfilene kun er tilgjengelige gjennom vår Tor-speil. Utgivelse 1 (%(date)s) Dette er en enkelt ekstra torrentfil. Den inneholder ingen ny informasjon, men den har noen data i seg som kan ta en stund å beregne. Det gjør det praktisk å ha, siden nedlasting av denne torrenten ofte er raskere enn å beregne den fra bunnen av. Spesielt inneholder den SQLite-indekser for tar-filene, for bruk med <a %(a_href)s>ratarmount</a>. Utgivelse 2 tillegg (%(date)s) Vi har fått alle bøkene som ble lagt til Z-Library mellom vårt siste speil og august 2022. Vi har også gått tilbake og skrapet noen bøker som vi gikk glipp av første gang. Alt i alt er denne nye samlingen på rundt 24TB. Igjen, denne samlingen er deduplisert mot Library Genesis, siden det allerede finnes torrents tilgjengelig for den samlingen. Dataene er organisert på samme måte som den første utgivelsen. Det er en MySQL “.sql.gz” dump av metadata, som også inkluderer all metadata fra den første utgivelsen, og dermed erstatter den. Vi har også lagt til noen nye kolonner: Vi nevnte dette sist, men bare for å klargjøre: “filnavn” og “md5” er de faktiske egenskapene til filen, mens “filnavn_rapportert” og “md5_rapportert” er det vi skrapte fra Z-Library. Noen ganger stemmer ikke disse to overens, så vi inkluderte begge. For denne utgivelsen endret vi sorteringen til “utf8mb4_unicode_ci”, som skal være kompatibel med eldre versjoner av MySQL. Datafilene er lik de forrige, men de er mye større. Vi orket rett og slett ikke å lage mange mindre torrentfiler. “pilimi-zlib2-0-14679999-extra.torrent” inneholder alle filene vi gikk glipp av i forrige utgivelse, mens de andre torrentene er alle nye ID-områder.  <strong>Oppdatering %(date)s:</strong> Vi gjorde de fleste av våre torrents for store, noe som fikk torrentklienter til å slite. Vi har fjernet dem og utgitt nye torrents. <strong>Oppdatering %(date)s:</strong> Det var fortsatt for mange filer, så vi pakket dem inn i tar-filer og utga nye torrents igjen. %(key)s: om denne filen allerede er i Library Genesis, enten i sakprosa- eller skjønnlitteratursamlingen (matchet av md5). %(key)s: hvilken torrent denne filen er i. %(key)s: satt når vi ikke klarte å laste ned boken. Utgivelse 2 (%(date)s) Zlib-utgivelser (originale beskrivelsessider) Tor-domene Hovednettsted Z-Library skraping Den “kinesiske” samlingen i Z-Library ser ut til å være den samme som vår DuXiu-samling, men med forskjellige MD5-er. Vi ekskluderer disse filene fra torrents for å unngå duplisering, men viser dem fortsatt i vårt søkeindeks. Metadata Du får %(percentage)s%% bonus raske nedlastinger, fordi du ble henvist av bruker %(profile_link)s. Dette gjelder for hele medlemskapsperioden. Doner Bli med Valgt opp til %(percentage)s avslag Alipay støtter internasjonale kreditt-/debetkort. Se <a %(a_alipay)s>denne veiledningen</a> for mer informasjon. Send oss Amazon.com-gavekort ved å bruke ditt kreditt-/debetkort. Du kan kjøpe krypto ved å bruke kreditt-/debetkort. WeChat (Weixin Pay) støtter internasjonale kreditt-/debetkort. I WeChat-appen, gå til «Meg => Tjenester => Lommebok => Legg til et kort». Hvis du ikke ser det, aktiver det ved å bruke «Meg => Innstillinger => Generelt => Verktøy => Weixin Pay => Aktiver». (bruk når du sender Ethereum fra Coinbase) kopiert! kopier (laveste minimumsbeløp) (advarsel: høyt minimumsbeløp) -%(percentage)s%% 12 måneder 1 måned 24 måneder 3 måneder 48 måneder 6 måneder 96 måneder Velg hvor lenge du vil abonnere for. <div %(div_monthly_cost)s></div><div %(div_after)s>etter <span %(span_discount)s></span> rabatter</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% for 12 måneder for 1 måned for 24 måneder for 3 måneder i 48 måneder for 6 måneder i 96 måneder %(monthly_cost)s / måned kontakt oss Direkte <strong>SFTP</strong> servere Donasjon på bedriftsnivå eller utveksling for nye samlinger på bedriftsnivå (f.eks. nye skanninger, OCR-baserte datasett). Eksperttilgang <strong>Ubegrenset</strong> høy-hastighet tilgang <div %(div_question)s>Kan jeg oppgradere medlemskapet mitt eller få flere medlemskap?</div> <div %(div_question)s>Kan jeg gi en donasjon uten å bli medlem?</div> Selvfølgelig. Vi aksepterer donasjoner av alle beløp på denne Monero (XMR)-adressen: %(address)s. <div %(div_question)s>Hva betyr intervallene per måned?</div> Du kan komme til den lavere siden av et intervall ved å bruke alle rabattene, for eksempel ved å velge en periode lengre enn en måned. <div %(div_question)s>Fornyes medlemskap automatisk?</div> Medlemskap <strong>fornyes ikke</strong> automatisk. Du kan bli med så lenge eller kort du vil. <div %(div_question)s>Hva bruker du donasjoner på?</div> 100%% går til å bevare og tilgjengeliggjøre verdens kunnskap og kultur. Foreløpelig bruker vi det for det meste på servere, lagring og båndbredde. Penger går ikke til noen teammedlemmer personlig. <div %(div_question)s>Kan jeg gi en stor donasjon?</div> Det hadde vært rått! For donasjoner over noen få tusen dollar, vennglist kontakt oss direkte på %(email)s. <div %(div_question)s>Har du andre betalingsmeteoder?</div> Akkurat nå, nei. Det er mange folk som ikke har lyst til at arkiver som dette skal eksistere, så vi må være forsiktig. Hvis du kan hjelpe oss sette opp mer andre (mer praktiske) betalingsmeteoder trygt, vennligst ta kontakt på %(email)s. Vanlige spørsmål om donasjoner Du har en <a %(a_donation)s>eksisterende donasjon</a> pågående. Fullfør eller avbryt donasjonen før du starter en ny donasjon. <a %(a_all_donations)s>Se alle mine donasjoner</a> For donasjoner over $5000, kontakt oss direkte på %(email)s. Vi tar imot store donasjoner fra velstående individer eller institusjoner.  Vær oppmerksom på at selv om medlemskapene på denne siden er "per måned", er de engangsdonasjoner (ikke-gjentakende). Se <a %(faq)s>Donasjons-FAQ</a>. Annas Arkiv er en ikke-for-profitt, åpen-kildekode, åpen-data prosjekt. Med å donere og med å bli en medlem, støtter du våres operasjon og utvikling. Til alle medlemmene våres: tusen takk for at du holder oss oppe! ❤️ For mer informasjon, sjekk ut <a %(a_donate)s>Donasjons-FAQ</a>. For å bli medlem, vennligst <a %(a_login)s>Logg inn eller Registrer deg</a>. Takk for støtten! $%(cost)s / måned Hvis du gjorde en feil under betalingen, kan vi ikke gi refusjon, men vi vil prøve å rette det opp. Finn "Krypto" fanen i PayPal appen eller nettsiden. Dette er vanligvis under "Økonomi". Gå til "Bitcoin"-fanen i din PayPal app eller nettside. Trykk på "Overfør" knappen %(transfer_icon)s, også "Send". Alipay Alipay 支付宝 / WeChat 微信 Amazon-gavekort %(amazon)s gavekort Bankkort Bankkort (bruker app) Binance Kreditt/debet/Apple/Google (BMC) Cash App Kredit-/debetkort Kredit-/debetkort 2 Kreditt-/debetkort (backup) Krypto %(bitcoin_icon)s Kort / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (vanlig) Pix (Brazil) Revolut (midlertidig utilgjengelig) WeChat Velg din foretrukne kryptomynt: Doner med et Amazon gavekort. <strong>VIKTIG:</strong> Dette alternativet er for %(amazon)s. Hvis du vil bruke et annet Amazon-nettsted, velg det ovenfor. <strong>VIKTIG:</strong> Vi støtter kun Amazon.com, ikke andre Amazon-nettsider. For eksempel er .de, co.uk, .ca IKKE støttet. IKKE skriv din egen melding. Skriv inn nøyaktig beløp: %(amount)s Legg merke til at vi må avrunde til beløp som er akseptert av forhandlerene våre (minimum %(minimum)s). Doner ved å bruke et kreditt-/debetkort, gjennom Alipay-appen (superenkel å sette opp). Installer Alipay-appen fra <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>. Registrer deg med telefonnummeret ditt. Ingen ytterligere personlige opplysninger er nødvendig. <span %(style)s>1</span>Installer Alipay-appen Støttet: Visa, MasterCard, JCB, Diners Club og Discover. Se <a %(a_alipay)s>denne veiledningen</a> for mer informasjon. <span %(style)s>2</span>Legg til bankkort Med Binance kjøper du Bitcoin med et kreditt-/debetkort eller bankkonto, og deretter donerer du den Bitcoinen til oss. På den måten kan vi forbli sikre og anonyme når vi mottar din donasjon. Binance er tilgjengelig i nesten alle land, og støtter de fleste banker og kreditt-/debetkort. Dette er for øyeblikket vår hovedanbefaling. Vi setter pris på at du tar deg tid til å lære hvordan du donerer ved hjelp av denne metoden, da det hjelper oss mye. For kredittkort, debetkort, Apple Pay og Google Pay bruker vi “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). I deres system er en “kaffe” lik $5, så din donasjon vil bli avrundet til nærmeste multiplum av 5. Doner med Cash App. Hvis du har Cash App, så er dette den letteste veien til å donere! Legg merke til at for transaksjoner under %(amount)s, kan Cash App belaste en %(fee)s avgift. For %(amount)s eller høyere, så er det gratis! Doner med et kredit eller debetkort. Denne metoden bruker en kryptovalutaleverandør som en mellomliggende konvertering. Dette kan være litt forvirrende, så vennligst bruk bare denne metoden hvis andre betalingsmetoder ikke fungerer. Det fungerer heller ikke i alle land. Vi kan ikke støtte kreditt-/debetkort direkte, fordi bankene ikke vil samarbeide med oss. ☹ Men det finnes flere måter å bruke kreditt-/debetkort på likevel, ved å bruke andre betalingsmetoder: Med krypto kan du donere med BTC, ETH, XMR og SOL. Bruk dette alternativet hvis du allerede kjenner til kryptovaluta. Med krypto kan du donere med BTC, ETH, XMR og mer. Krypto ekspresstjenester Hvis du bruker krypto for første gang, foreslår vi at du bruker %(options)s for å kjøpe og donere Bitcoin (den originale og mest brukte kryptovalutaen). Merk at for små donasjoner kan kredittkortgebyrene eliminere vår %(discount)s%% rabatt, så vi anbefaler lengre abonnementer. Doner med kreditt-/debetkort, PayPal eller Venmo. Du kan velge mellom disse på neste side. Google Pay og Apple Pay kan fungere og. Merk at for små donasjoner er avgiftene høye, så vi anbefaler lengre abonnementer. For å donere med PayPal US, vi kommer til å bruke PayPal Crypto, som lar oss forbli anonym. Vi setter pris på at du tar tiden til å lære hvordan du donerer ved hjelp av denne meteoden, fordi det hjelper oss skikkelig. Doner med PayPal. Doner med din vanlige PayPal-konto. Doner med Revolut. Hvis du har Revolut, er dette den enkleste måten å donere på! Denne betalingsmåten tillater kun maksimalt %(amount)s. Velg en annen varighet eller betalingsmåte. Denne betalingsmåten krever et minimum på %(amount)s. Velg en annen varighet eller betalingsmåte. Binance Coinbase Kraken Vennligst velg en betalingsmetode. "Adopter en torrent": brukernavnet ditt eller en melding i filnavnet til en torrent-fil <div %(div_months)s>en gang hver 12. måned med medlemskap</div> Ditt brukernavn eller anonymt nevnelse i listen over bidragsytere Tidlig tilgang til nye funksjoner Eksklusiv Telegram med oppdateringer bak kulissene %(number)s raske nedlastinger hver dag hvis du donerer denne måneden! <a %(a_api)s>JSON API</a>-tilgang Legendarisk status i bevaring av menneskehetens kunnskap og kultur Tidligere fordeler, og: Tjen <strong>%(percentage)s%% bonusnedlastinger</strong> ved å <a %(a_refer)s>henvise venner</a>. SciDB journaler <strong>ubegrenset</strong> uten bekreftelse Når du stiller spørsmål om konto eller donasjoner, legg ved din kontoid, skjermbilder, kvitteringer, så mye informasjon som mulig. Vi sjekker bare e-posten vår hver 1-2 uke, så å ikke inkludere denne informasjonen vil forsinke enhver løsning. For å få enda flere nedlastinger, <a %(a_refer)s>henvis dine venner</a>! Vi er et lite team av frivillige. Det kan ta oss 1-2 uker å svare. Legg merke til at kontonavnet eller profilbildet kan se litt rar ut. Ikke bekymre deg, disse kontoene er styrt av våres donasjonspartnere. Våre kontoer har ikke blitt hacket. Doner <span %(span_cost)s></span> <span %(span_label)s></span> for 12 måneder "%(tier_name)s" for 1 måned "%(tier_name)s" for 24 måneder "%(tier_name)s" for 3 måneder "%(tier_name)s" for 48 måneder “%(tier_name)s” for 6 måneder "%(tier_name)s" i 96 måneder “%(tier_name)s” Du kan fortsatt kansellere donasjonen mens du er i kassen. Klikk på doner-knappen for å bekrefte donasjonen. <strong>Viktig! NB:</strong> Kryptovaluta kan svinge voldsomt, noen ganger til og med så mye som 20%% i noen få minutter. Dette er fortsatt mindre enn gebyrene vi pådrar oss med mange betalingsleverandører, som ofte tar 50 til 60%% for å samarbeide med en "skyggeveldedighet" som oss.<u>Hvis du sender oss kvitteringen med den originale prisen du betalte, vil vi fortsatt kreditere kontoen din for det valgte medlemskapet</u> (så lenge kvitteringen ikke er eldre enn noen få timer). Vi setter stor pris på at du er villig til å holde ut med ting som dette for å støtte oss! ❤️ ❌ Noe gikk galt. Oppfrisk siden og prøv på nytt. <span %(span_circle)s>1</span>Kjøp Bitcoin på PayPal <span %(span_circle)s>2</span>Overfør Bitcoinen til adressen våres ✅ Videresender deg til donasjonssiden… Doner Vennligst vent minst <span %(span_hours)s>24 timer</span> (og oppdater denne siden) før du kontakter oss. Hvis du ønsker å gi en donasjon (hvilket som helst beløp) uten medlemskap, kan du bruke denne Monero (XMR)-adressen: %(address)s. Etter å ha sendt gavekortet ditt, vil vi automatisk bekrefte det innen noen få minutter. Hvis dette ikke fungerer, prøv å send gavekortet på nytt (<a %(a_instr)s>instruksjoner</a>). Hvis det heller ikke går, send oss en epost også vil Anna sjekke det manuelt (dette kan ta noen få dager), og husk å nevn at du har prøvd å sende på nytt allerede. Eksempel: Bruk <a %(a_form)s>Amazon.coms offisielle skjema</a> for å sende oss et gavekort på %(amount)s til epost adressen nedenfor. "Til" mottakers epost i skjemaet: Amazon-gavekort Vi kan ikke godta andre måter av gavekort, <strong>kun de sendt direkte fra Amazon.coms offisielle skjema</strong>. Vi kan ikke returnere gavekortet hvis du ikke bruker dette skjemaet. Bruk kun én gang. Unikt til kontoen din, ikke del det. Venter på gavekort... (oppdater siden for å sjekke) Åpne <a %(a_href)s>QR-kode donasjonssiden</a>. Skann QR-koden med Alipay-appen, eller trykk på knappen for å åpne Alipay-appen. Vennligst vær tålmodig; siden kan ta litt tid å laste da den er i Kina. <span %(style)s>3</span>Gi donasjon (skann QR-kode eller trykk på knappen) Kjøp PYUSD-mynten på PayPal Kjøp Bitcoin (BTC) på Cash App Kjøp litt mer (vi anbefaler %(more)s mer) enn beløpet du donerer (%(amount)s), for å dekke transaksjonsgebyrer. Du beholder det som er til overs. Gå til “Bitcoin” (BTC)-siden i Cash App. Overfør Bitcoin til vår adresse For små donasjoner (under $25), kan det hende du må bruke Rush eller Priority. Klikk på “Send bitcoin”-knappen for å gjøre et “uttak”. Bytt fra dollar til BTC ved å trykke på %(icon)s-ikonet. Skriv inn BTC-beløpet nedenfor og klikk “Send”. Se <a %(help_video)s>denne videoen</a> hvis du står fast. Ekspresstjenester er praktiske, men har høyere gebyrer. Du kan bruke dette i stedet for en kryptobørs hvis du ønsker å raskt gi en større donasjon og ikke har noe imot et gebyr på $5-10. Sørg for å sende nøyaktig kryptobeløp som vises på donasjonssiden, ikke beløpet i $USD. Ellers vil gebyret bli trukket fra, og vi kan ikke automatisk behandle medlemskapet ditt. Noen ganger kan bekreftelse ta opptil 24 timer, så sørg for å oppdatere denne siden (selv om den har utløpt). Instruksjoner for kreditt / debetkort Doner gjennom våres kreditt / debetkortside Noen av trinnene nevner kryptolommebøker, men ikke bekymre deg, du må ikke lære noe om kryptovaluta for dette. Instruksjoner for %(coin_name)s Skann denne QR -koden med Crypto Wallet -appen din for raskt å fylle ut betalingsdetaljene Skann QR -kode for å betale Vi støtter kun standardversjonen av kryptomynter, ingen eksotiske nettverk eller versjoner av mynter. Det kan ta opptil en time å bekrefte transaksjonen, avhengig av mynten. Doner %(amount)s på <a %(a_page)s>denne siden</a>. Donasjonen har utløpt. Vennligst avbryt og lag en ny en. Hvis du allerede har betalt: Ja, jeg har sendt kvitteringen min på epost Hvis kryptovalutakursen svingte under transaksjonen, husk å send kvitteringen med den originale kursen. Vi setter stor pris på at du tar deg bryet med å bruke kryptovaluta, det hjelper mye! ❌ Noe gikk galt. Oppfrisk siden og prøv på nytt. <span %(span_circle)s>%(circle_number)s</span>Send oss kvitteringen på epost Hvis du kommer borti noen problemer, kontakt oss på %(email)s og inkluder så mye informasjon som du kan (som skjermbilder). ✅ Takk for donasjonen! Anna vil manuelt aktivere medlemskapet ditt innen noen få dager. Send en kvittering eller skjermbilde til din personlige bekreftelseadresse: Når du har sendt kvitteringen på epost, trykk denne knappen, slik at Anna kan sjekke det manuelt (dette kan ta noen få dager): Send en kvittering eller skjermbilde til din personlige verifikasjonsadresse. Ikke bruk denne e-postadressen for din PayPal-donasjon. Avbryt Ja, vennligst avbryt Er du sikker på at du vil avbryte? Ikke avbryt hvis du har allerede betalt. ❌ Noe gikk galt. Oppfrisk siden og prøv på nytt. Lag en ny donasjon ✅ Din donasjon har blitt avbrutt. Dato: %(date)s Identifikator: %(id)s Bestill på nytt Status: <span %(span_label)s>%(label)s</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned for %(duration)s måneder, inkluderer %(discounts)s%% i rabatt)</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / måned for %(duration)s måneder)</span> 1. Skriv inn eposten din. 2. Velg betalingsmåte. 3. Velg betalingsmåte igjen. 4. Velg "Selvvertet"-lommeboken. 5. Trykk "Jeg bekrefter eierskap". 6. Du skal motta en kvittering på epost. Send det til oss, også vil vi bekrefte donasjonen din så fort som mulig. (du burde kanskje avbryte og starte en ny donasjon) Betalingsinstruksjonene er utdatert. Hvis du har lyst til å lage en annen donasjon, klikk på "Bestill på nytt" knappen ovenfor. Du har allerede betalt. Hvis du vil uansett se gjennom betalingsinstruksjonene, klikk her: Vis gamle betalingsinstruksjoner Hvis donasjonssiden blir blokkert, prøv en annen internettforbindelse (f.eks. VPN eller mobilinternett). Dessverre er Alipay-siden ofte kun tilgjengelig fra <strong>fastlands-Kina</strong>. Du må kanskje midlertidig deaktivere VPN-en din, eller bruke en VPN til fastlands-Kina (eller Hong Kong fungerer også noen ganger). <span %(span_circle)s>1</span>Doner med Alipay Doner totalbeløpet på %(total)s ved å bruke <a %(a_account)s>denne Alipay-kontoen</a> Instruksjoner for Alipay <span %(span_circle)s>1</span>Overfør til en av våre kryptokontoer Doner det totale beløpet av %(total)s til en av disse adressene: Instruksjoner for krypto Følg instruksjonene for å kjøpe Bitcoin (BTC). Du trenger kun å kjøpe beløpet du har lyst til å donere, %(total)s. Skriv inn våres Bitcoin (BTC) adressen som mottaker, og følg instruksjonene for å sende donasjonen din av %(total)s: <span %(span_circle)s>1</span>Doner med Pix Doner summen på %(total)s til <a %(a_account)s>denne Pix kontoen Instruksjoner for Pix <span %(span_circle)s>1</span>Doner på WeChat Doner totalbeløpet på %(total)s ved å bruke <a %(a_account)s>denne WeChat-kontoen</a> WeChat-instruksjoner Bruk en av følgende “kredittkort til Bitcoin”-ekspresstjenester, som bare tar noen minutter: BTC / Bitcoin-adresse (ekstern lommebok): BTC / Bitcoin-beløp: Fyll inn følgende detaljer i skjemaet: Hvis noen av denne informasjonen er utdatert, vennligst send oss en e-post for å gi beskjed. Vennligst bruk dette <span %(underline)s>eksakte beløpet</span>. Din totale kostnad kan være høyere på grunn av kredittkortgebyrer. For små beløp kan dette dessverre være mer enn vår rabatt. (minimum: %(minimum)s, ingen verifisering for første transaksjon) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, ingen verifisering for første transaksjon) (minimum: %(minimum)s) (minimum: %(minimum)s avhengig av land, ingen verifisering for første transaksjon) Følg instruksjonene for å kjøpe PYUSD-mynten (PayPal USD). Kjøp litt til (vi anbefaler at du kjøper %(more)s mer) enn mengden du donerer (%(amount)s), for å dekke transaksjonsgebyrer. Du får beholde alt som er til overs. Gå til "PYUSD" siden i PayPal appen eller nettsiden. Trykk på "Overfør" knappen %(icon)s, også "Send". Oppdateringstatus For å tilbakestille tidtakeren, bare opprett en ny donasjon. Sørg for å bruke BTC-beløpet nedenfor, <em>IKKE</em> euro eller dollar, ellers vil vi ikke motta riktig beløp og kan ikke automatisk bekrefte medlemskapet ditt. Kjøp Bitcoin (BTC) på Revolut Kjøp litt mer (vi anbefaler %(more)s mer) enn beløpet du donerer (%(amount)s), for å dekke transaksjonsgebyrer. Du beholder det som er til overs. Gå til “Crypto”-siden i Revolut for å kjøpe Bitcoin (BTC). Overfør Bitcoin til vår adresse For små donasjoner (under $25) kan det hende du må bruke Rush eller Priority. Klikk på “Send bitcoin”-knappen for å gjøre et “uttak”. Bytt fra euro til BTC ved å trykke på %(icon)s-ikonet. Skriv inn BTC-beløpet nedenfor og klikk “Send”. Se <a %(help_video)s>denne videoen</a> hvis du står fast. Status: 1 2 Trinnvise instruksjoner Se trinn-for-trinn-veiledningen nedenfor. Ellers kan du bli låst ute av denne kontoen! Hvis du ikke allerede har gjort det, skriv ned din hemmelige nøkkel for innlogging: Takk for donasjonen din! Tid igjen: Donasjon Overfør %(amount)s til %(account)s Venter for bekreftelse (oppfrisk siden for å sjekke)… Venter for overførelse (oppfrisk siden for å sjekke)… Tidligere Raske nedlastinger de siste 24 timene teller mot den daglige grensen. Nedlastinger fra raske partner-servere er merket med %(icon)s. Siste 18 timer Ingen filer nedlastet ennå. Nedlastet filer er ikke vist til det offentlige. Alle tider er i UTC. Nedlastet filer Hvis du lastet ned en fil med både raske og trege nedlastinger, vil den vises to ganger. Ikke bekymre deg for mye, det er mange som laster ned fra nettsteder vi lenker til, og det er ekstremt sjeldent å få problemer. For å være trygg anbefaler vi å bruke en VPN (betalt), eller <a %(a_tor)s>Tor</a> (gratis). Jeg lastet ned 1984 av George Orwell, vil politiet komme på døren min? Du er Anna! Hvem er Anna? Vi har en stabil JSON API for medlemmer, for å få en rask nedlastings-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasjon innenfor JSON selv). For andre bruksområder, som å iterere gjennom alle våre filer, bygge tilpasset søk, og så videre, anbefaler vi å <a %(a_generate)s>generere</a> eller <a %(a_download)s>laste ned</a> våre ElasticSearch- og MariaDB-databaser. Rådataene kan manuelt utforskes <a %(a_explore)s>gjennom JSON-filer</a>. Vår rå torrentliste kan også lastes ned som <a %(a_torrents)s>JSON</a>. Har dere en API? Vi hoster ingen opphavsrettsbeskyttet materiale her. Vi er en søkemotor, og indekserer derfor kun metadata som allerede er offentlig tilgjengelig. Når du laster ned fra disse eksterne kildene, foreslår vi at du sjekker lovene i din jurisdiksjon med hensyn til hva som er tillatt. Vi er ikke ansvarlige for innhold som hostes av andre. Hvis du har klager på det du ser her, er det beste alternativet å kontakte den opprinnelige nettsiden. Vi henter regelmessig deres endringer inn i vår database. Hvis du virkelig mener at du har en gyldig DMCA-klage vi bør svare på, vennligst fyll ut <a %(a_copyright)s>DMCA / Opphavsrettskjemaet</a>. Vi tar dine klager på alvor, og vil komme tilbake til deg så snart som mulig. Hvordan rapporterer jeg brudd på opphavsretten? Her er noen bøker som har spesiell betydning for skyggebiblioteker og digital bevaring: Hva er dine favorittbøker? Vi vil også minne alle om at all vår kode og data er fullstendig åpen kildekode. Dette er unikt for prosjekter som vårt — vi er ikke klar over noen andre prosjekter med en tilsvarende massiv katalog som også er fullstendig åpen kildekode. Vi ønsker veldig velkommen alle som mener vi driver prosjektet vårt dårlig til å ta vår kode og data og sette opp sitt eget skyggebibliotek! Vi sier ikke dette av ondskap eller noe — vi mener virkelig at dette ville vært fantastisk siden det ville heve standarden for alle, og bedre bevare menneskehetens arv. Jeg hater hvordan dere driver dette prosjektet! Vi vil gjerne at folk setter opp <a %(a_mirrors)s>speil</a>, og vi vil støtte dette økonomisk. Hvordan kan jeg hjelpe? Det gjør vi. Vår inspirasjon for å samle metadata er Aaron Swartz’ mål om “en nettside for hver bok som noen gang er utgitt”, som han opprettet <a %(a_openlib)s>Open Library</a> for. Det prosjektet har gjort det bra, men vår unike posisjon lar oss få metadata som de ikke kan. En annen inspirasjon var vårt ønske om å vite <a %(a_blog)s>hvor mange bøker det finnes i verden</a>, slik at vi kan beregne hvor mange bøker vi fortsatt har igjen å redde. Samler dere metadata? Merk at mhut.org blokkerer visse IP-områder, så en VPN kan være nødvendig. <strong>Android:</strong> Klikk på menyen med tre prikker øverst til høyre, og velg «Legg til på Hjem-skjerm». <strong>iOS:</strong> Klikk på «Del»-knappen nederst, og velg «Legg til på Hjem-skjerm». Vi har ingen offisiell mobilapp, men du kan installere denne nettsiden som en app. Har dere en mobilapp? Vennligst send dem til <a %(a_archive)s>Internet Archive</a>. De vil bevare dem på riktig måte. Hvordan donerer jeg bøker eller annet fysisk materiale? Hvordan ber jeg om bøker? <a %(a_blog)s>Annas Blogg</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelmessige oppdateringer <a %(a_software)s>Annas Programvare</a> — vår åpen kildekode <a %(a_datasets)s>Datasett</a> — om dataene <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domener Finnes det flere ressurser om Annas Arkiv? <a %(a_translate)s>Oversett på Annas Programvare</a> — vårt oversettelsessystem <a %(a_wikipedia)s>Wikipedia</a> — mer om oss (vennligst hjelp med å holde denne siden oppdatert, eller opprett en for ditt eget språk!) Velg innstillingene du liker, hold søkeboksen tom, klikk "Søk", og deretter bokmerke siden ved hjelp av nettleserens bokmerkefunksjon. Hvordan lagrer jeg søkeinnstillingene mine? Vi ønsker sikkerhetsforskere velkommen til å lete etter sårbarheter i våre systemer. Vi er store tilhengere av ansvarlig avsløring. Kontakt oss <a %(a_contact)s>her</a>. Vi er for øyeblikket ikke i stand til å tildele bug-bounties, bortsett fra sårbarheter som har <a %(a_link)s>potensial til å kompromittere vår anonymitet</a>, for hvilke vi tilbyr bounties i området $10k-50k. Vi ønsker å tilby bredere omfang for bug-bounties i fremtiden! Vennligst merk at sosialtekniske angrep er utenfor omfanget. Hvis du er interessert i offensiv sikkerhet, og ønsker å hjelpe til med å arkivere verdens kunnskap og kultur, sørg for å kontakte oss. Det er mange måter du kan hjelpe til på. Har dere et ansvarlig avsløringsprogram? Vi har bokstavelig talt ikke nok ressurser til å gi alle i verden høyhastighetsnedlastinger, så mye som vi ønsker det. Hvis en rik velgjører ønsker å trå til og gi oss dette, ville det vært utrolig, men inntil da gjør vi vårt beste. Vi er et non-profit prosjekt som knapt kan opprettholde seg selv gjennom donasjoner. Dette er grunnen til at vi implementerte to systemer for gratis nedlastinger, med våre partnere: delte servere med langsomme nedlastinger, og litt raskere servere med venteliste (for å redusere antall personer som laster ned samtidig). Vi har også <a %(a_verification)s>nettleserverifisering</a> for våre langsomme nedlastinger, fordi ellers vil roboter og skrapere misbruke dem, noe som gjør ting enda tregere for legitime brukere. Merk at når du bruker Tor Browser, kan det hende du må justere sikkerhetsinnstillingene dine. På det laveste alternativet, kalt “Standard”, lykkes Cloudflare turnstile-utfordringen. På de høyere alternativene, kalt “Sikrere” og “Sikrest”, mislykkes utfordringen. For store filer kan nedlastinger noen ganger bryte i midten. Vi anbefaler å bruke en nedlastingsbehandler (som JDownloader) for automatisk å gjenoppta store nedlastinger. Hvorfor er de trege nedlastingene så trege? Ofte stilte spørsmål (FAQ) Bruk <a %(a_list)s>torrentlistegeneratoren</a> for å generere en liste over torrents som mest trenger seeding, innenfor dine lagringsplassgrenser. Ja, se siden for <a %(a_llm)s>LLM-data</a>. De fleste torrentene inneholder filene direkte, noe som betyr at du kan instruere torrentklienter til kun å laste ned de nødvendige filene. For å bestemme hvilke filer du skal laste ned, kan du <a %(a_generate)s>generere</a> vår metadata, eller <a %(a_download)s>laste ned</a> våre ElasticSearch- og MariaDB-databaser. Dessverre inneholder en rekke torrent-samlinger .zip- eller .tar-filer i roten, i så fall må du laste ned hele torrenten før du kan velge individuelle filer. Ingen brukervennlige verktøy for å filtrere ned torrents er tilgjengelige ennå, men vi ønsker bidrag velkommen. (Vi har <a %(a_ideas)s>noen ideer</a> for sistnevnte tilfelle.) Langt svar: Kort svar: ikke lett. Vi prøver å holde minimal duplisering eller overlapping mellom torrentene i denne listen, men dette kan ikke alltid oppnås, og avhenger sterkt av retningslinjene til kildene. For biblioteker som gir ut sine egne torrents, er det utenfor vår kontroll. For torrents utgitt av Anna’s Archive, dedupliserer vi kun basert på MD5-hash, noe som betyr at forskjellige versjoner av samme bok ikke blir deduplisert. Ja. Disse er faktisk PDF-er og EPUB-er, de har bare ikke en filendelse i mange av våre torrents. Det er to steder hvor du kan finne metadata for torrentfiler, inkludert filtyper/-endelser: 1. Hver samling eller utgivelse har sine egne metadata. For eksempel har <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> en tilsvarende metadatadatabase som er vert på Libgen.rs-nettstedet. Vi linker vanligvis til relevante metadataressurser fra hver samlings <a %(a_datasets)s>datasettside</a>. 2. Vi anbefaler å <a %(a_generate)s>generere</a> eller <a %(a_download)s>laste ned</a> våre ElasticSearch- og MariaDB-databaser. Disse inneholder en mapping for hver post i Annas Arkiv til de tilsvarende torrentfilene (hvis tilgjengelig), under “torrent_paths” i ElasticSearch JSON. Noen torrent-klienter støtter ikke store stykke-størrelser, som mange av torrentene våre har (for nyere gjør vi ikke dette lenger — selv om det er gyldig i henhold til spesifikasjonene!). Så prøv en annen klient hvis du støter på dette, eller klag til produsentene av torrent-klienten din. Jeg vil gjerne hjelpe til med å seede, men jeg har ikke mye diskplass. Torrentene er for trege; kan jeg laste ned dataene direkte fra dere? Kan jeg laste ned bare et utvalg av filene, som bare et bestemt språk eller emne? Hvordan håndterer dere duplikater i torrentene? Kan jeg få torrentlisten som JSON? Jeg ser ikke PDF-er eller EPUB-er i torrentene, bare binære filer? Hva gjør jeg? Hvorfor kan ikke torrent-klienten min åpne noen av torrent-filene / magnet-linkene deres? Torrents FAQ Hvordan laster jeg opp nye bøker? Vennligst se <a %(a_href)s>dette utmerkede prosjektet</a>. Har du en oppetidsovervåker? Hva er Annas Arkiv? Bli medlem for å bruke raske nedlastinger. Vi støtter nå Amazon-gavekort, kreditt- og debetkort, krypto, Alipay og WeChat. Du har brukt opp dagens raske nedlastinger. Tilgang Nedlastinger per time de siste 30 dagene. Timegjennomsnitt: %(hourly)s. Døgnsnitt: %(daily)s. Vi samarbeider med partnere for å gjøre våre samlinger lett tilgjengelige og gratis for alle. Vi mener at alle har rett til menneskehetens kollektive visdom. Og <a %(a_search)s>ikke på bekostning av forfattere</a>. Datasettene brukt i Annas Arkiv er helt åpne, og kan speiles i bulk ved hjelp av torrenter. <a %(a_datasets)s>Lær mer…</a> Langtidsarkiv Full database Søk Bøker, artikler, magasiner, tegneserier, bibliotekoppføringer, metadata, … All vår <a %(a_code)s>kode</a> og <a %(a_datasets)s>data</a> er helt åpen kildekode. <span %(span_anna)s>Annas arkiv</span> er et ideelt prosjekt med to mål: <li><strong>Bevaring:</strong> Sikkerhetskopiere all kunnskap og menneskelig kultur.</li><li><strong>Tilgang:</strong> Gjøre kunnskap og kulturen vi har sikkerhetskopiert tilgjengelig for hvem som helst i verden.</li> Vi har verdens største samling av høykvalitets tekstdata. <a %(a_llm)s>Lær mer…</a> LLM training data 🪩 Speil: oppfordring til frivillige Hvis du driver en høyrisiko anonym betalingsformidler, vennligst kontakt oss. Vi ser også etter folk som ønsker å plassere smakfulle små annonser. Alle inntekter går til våre bevaringsinnsats. Bevaring Vi anslår at vi har bevart omtrent <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% av verdens bøker</a>. Vi bevarer bøker, artikler, tegneserier, magasiner og mer, ved å samle disse materialene fra ulike <a href="https://en.wikipedia.org/wiki/Shadow_library">skyggebiblioteker</a>, offisielle biblioteker og andre samlinger på ett sted. Alle disse dataene bevares for alltid ved å gjøre det enkelt å duplisere dem i bulk — ved hjelp av torrents — noe som resulterer i mange kopier rundt om i verden. Noen skyggebiblioteker gjør allerede dette selv (f.eks. Sci-Hub, Library Genesis), mens Anna’s Arkiv “frigjør” andre biblioteker som ikke tilbyr bulkdistribusjon (f.eks. Z-Library) eller ikke er skyggebiblioteker i det hele tatt (f.eks. Internet Archive, DuXiu). Denne brede distribusjonen, kombinert med åpen kildekode, gjør nettstedet vårt motstandsdyktig mot nedstengninger, og sikrer langsiktig bevaring av menneskehetens kunnskap og kultur. Lær mer om <a href="/datasets">våre datasett</a>. Hvis du er <a %(a_member)s>medlem</a>, er nettleserverifisering ikke nødvendig. 🧬&nbsp;SciDB er en fortsettelse av Sci-Hub. SciDB Åpen DOI Sci-Hub har <a %(a_paused)s>pauset</a> opplasting av nye artikler. Direkte tilgang til %(count)s akademiske artikler 🧬&nbsp;SciDB er en fortsettelse av Sci-Hub, med sitt kjente grensesnitt og direkte visning av PDF-er. Skriv inn din DOI for å se. Vi har hele Sci-Hub-samlingen, samt nye artikler. De fleste kan vises direkte med et kjent grensesnitt, likt Sci-Hub. Noen kan lastes ned gjennom eksterne kilder, i så fall viser vi lenker til disse. Du kan hjelpe enormt ved å seede torrenter. <a %(a_torrents)s>Lær mer…</a> >%(count)s seedere <%(count)s seedere %(count_min)s–%(count_max)s seedere 🤝 Ser etter frivillige Som et non-profit, åpen kildekode-prosjekt, ser vi alltid etter folk som kan hjelpe til. IPFS-nedlastinger Liste av %(by)s, laget <span %(span_time)s>%(time)s</span> Lagre ❌ Noe gikk galt. Prøv på nytt. ✅ Lagret. Last inn siden på nytt. Listen er tom. rediger Legg til eller fjern fra denne lista med å finne en fil og å trykke på "Lister" fanen. Liste Hvordan vi kan hjelpe Fjerne overlapping (deduplisering) Tekst- og metadatauttrekk OCR Vi kan tilby høyhastighetstilgang til våre fulle samlinger, samt til uutgitte samlinger. Dette er tilgang på bedriftsnivå som vi kan tilby for donasjoner i størrelsesorden titusenvis av USD. Vi er også villige til å bytte dette mot høykvalitetssamlinger som vi ennå ikke har. Vi kan refundere deg hvis du kan gi oss berikelse av våre data, for eksempel: Støtt langsiktig arkivering av menneskelig kunnskap, samtidig som du får bedre data for din modell! <a %(a_contact)s>Kontakt oss</a> for å diskutere hvordan vi kan samarbeide. Det er godt forstått at LLM-er trives på data av høy kvalitet. Vi har verdens største samling av bøker, artikler, magasiner osv., som er noen av de høyeste kvalitetstekstene. LLM-data Unik skala og rekkevidde Vår samling inneholder over hundre millioner filer, inkludert akademiske tidsskrifter, lærebøker og magasiner. Vi oppnår denne skalaen ved å kombinere store eksisterende arkiver. Noen av våre kildesamlinger er allerede tilgjengelige i bulk (Sci-Hub og deler av Libgen). Andre kilder har vi frigjort selv. <a %(a_datasets)s>Datasets</a> viser en full oversikt. Vår samling inkluderer millioner av bøker, artikler og magasiner fra før e-bok-æraen. Store deler av denne samlingen har allerede blitt OCR’et, og har allerede lite intern overlapping. Fortsett Hvis du har mistet nøkkelen din, vennligst <a %(a_contact)s>kontakt oss</a> og gi så mye informasjon som mulig. Du må kanskje midlertidig opprette en ny konto for å kontakte oss. Vennligst <a %(a_account)s>logg inn</a> for å se denne siden.</a> For å forhindre at spam-bots oppretter mange kontoer, må vi først verifisere nettleseren din. Hvis du blir fanget i en uendelig løkke, anbefaler vi å installere <a %(a_privacypass)s>Privacy Pass</a>. Det kan også hjelpe å slå av annonseblokkere og andre nettleserutvidelser. Logg inn / Registrer Anna’s Archive er midlertidig nede for vedlikehold. Vennligst kom tilbake om en time. Alternativ forfatter Alternativ beskrivelse Alternativ utgave Alternativ filtype Alternativ filnavn Alternativ forlag Alternativ tittel dato åpen kildekode Les mer… beskrivelse Søk i Annas Arkiv etter CADAL SSNO-nummer Søk i Annas Arkiv etter DuXiu SSID-nummer Søk i Annas Arkiv etter DuXiu DXID-nummer Søk Annas arkiv for ISBN Søk Annas arkiv for OCLC (WorldCat) nummer Søk Annas arkiv for Open Library ID Annas Arkiv nettvisning %(count)s berørte sider Etter nedlasting: En bedre versjon av denne filen kan være tilgjengelig på %(link)s Bulk torrent nedlastinger samling Bruk nettbaserte verktøy for å konvertere mellom formater. Anbefalte konverteringsverktøy: %(links)s For store filer anbefaler vi å bruke en nedlastingsbehandler for å unngå avbrudd. Anbefalte nedlastingsbehandlere: %(links)s EBSCOhost eBook Index (kun for eksperter) (også klikk "SKAFF" på toppen) (trykk "SKAFF" på toppen) Eksterne nedlastinger <strong>🚀 Raske nedlastinger</strong> Du har %(remaining)s igjen i dag. Takk for at du er medlem! ❤️ <strong>🚀 Raske nedlastinger</strong> Du har brukt opp dagens raske nedlastinger. <strong>🚀 Raske nedlastinger</strong> Du lastet ned denne filen nylig. Lenker forblir gyldige en stund. <strong>🚀 Raske nedlastinger</strong> Bli <a %(a_membership)s>medlem</a> for å støtte langsiktig bevaring av bøker, artikler og mer. For å vise vår takknemlighet for din støtte, får du raske nedlastinger. ❤️ 🚀 Raske nedlastinger 🐢 Trege nedlastinger Lån fra Internet Archive IPFS-gateway #%(num)d (det kan hende at du må prøve flere ganger med IPFS) Libgen.li Libgen.rs skjønnlitteratur Libgen.rs sakprosa deres annonser er kjent for å inneholde skadelig programvare, så bruk en annonseblokker eller ikke klikk på annonser Amazons «Send to Kindle» djazz' «Send to Kobo/Kindle» MagzDB ManualsLib Nexus/STC (Nexus/STC-filer kan være upålitelige å laste ned) Ingen nedlastinger funnet. Alle nedlastingsalternativene har samme fil, og bør være trygge å bruke. Når det er sagt, vær alltid forsiktig når du laster ned filer fra internett, spesielt fra nettsteder utenfor Annas Arkiv. For eksempel, sørg for å holde enhetene dine oppdatert. (ingen omdirigering) Åpne i vår visning (åpne i visning) Alternativ #%(num)d: %(link)s %(extra)s Finn originalposten i CADAL Søk manuelt på DuXiu Finn original rekord i ISBNdb Finn original rekord i WorldCat Finn original rekord i Open Library Søk forskjellige andre databaser for ISBN (utskrift deaktivert, kun for betalende) PubMed Du trenger en e-bok- eller PDF-leser for å åpne filen, avhengig av filformatet. Anbefalte e-boklesere: %(links)s Annas arkiv 🧬 SciDB Sci-Hub: %(doi)s (det kan hente at assosiert DOI er ikke tilgjengelig på Sci-Hub) Du kan sende både PDF- og EPUB-filer til din Kindle eller Kobo eReader. Anbefalte verktøy: %(links)s Mer informasjon i <a %(a_slow)s>FAQ</a>. Støtt forfattere og biblioteker Hvis du liker dette og har råd til det, vurder å kjøpe originalen, eller støtte forfatterne direkte. Hvis dette er tilgjengelig på ditt lokale bibliotek, vurder å låne det gratis der. Partner Server-nedlastinger midlertidig ikke tilgjengelig for denne filen. torrent Fra pålitelige partnere. Z-Library Z-bibliotek på Tor (krever Tor-nettleseren) vis eksterne nedlastinger <span class="font-bold">❌ Denne filen kan ha problemer, og har blitt skjult fra et kildebibliotek.</span> Noen ganger er dette på forespørsel fra en rettighetshaver, noen ganger fordi et bedre alternativ er tilgjengelig, men noen ganger er det på grunn av et problem med selve filen. Det kan fortsatt være greit å laste ned, men vi anbefaler først å søke etter en alternativ fil. Mer informasjon: Hvis du fortsatt vil laste ned denne filen, sørg for å kun bruke pålitelig, oppdatert programvare for å åpne den. metadata kommentarer AA: Søk i Annas Arkiv etter “%(name)s” Koder Utforsker: Vis i Koder Utforsker “%(name)s” URL: Nettsted: Hvis du har denne filen og den ikke er tilgjengelig i Annas Arkiv ennå, vurder å <a %(a_request)s>laste den opp</a>. Internet Archive Controlled Digital Lending fil "%(id)s" Dette er en rekord av en fil fra Internet Archive, og er ikke en direkte nedlastbar fil. Du kan prøve å låne boka (lenke nedenfor), eller bruke denne URL-en når du <a %(a_request)s>ber om en fil</a>. Forbedre metadata CADAL SSNO %(id)s metadata post Dette er en metadata-post, ikke en nedlastbar fil. Du kan bruke denne URL-en når du <a %(a_request)s>ber om en fil</a>. DuXiu SSID %(id)s metadata post ISBNdb %(id)s metadata rekord MagzDB ID %(id)s metadataoppføring Nexus/STC ID %(id)s metadataoppføring OCLC (WorldCat) nummer %(id)s metadataoppføring Open Library %(id)s metadata rekord Sci-Hub fil "%(id)s" Ikke funnet "%(md5_input)s" var ikke funnet i databasen våres. Legg til kommentar (%(count)s) Du kan få md5 fra URL-en, f.eks. MD5 av en bedre versjon av denne filen (hvis aktuelt). Fyll ut dette hvis det finnes en annen fil som ligner denne filen (samme utgave, samme filtype hvis du kan finne en), som folk bør bruke i stedet for denne filen. Hvis du vet om en bedre versjon av denne filen utenfor Anna’s Archive, vennligst <a %(a_upload)s>last den opp</a>. Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. Du la igjen en kommentar. Det kan ta et minutt før den vises. Vennligst bruk <a %(a_copyright)s>DMCA / Skjema for opphavsrettskrav</a>. Beskriv problemet (påkrevd) Hvis denne filen har god kvalitet, kan du diskutere alt om den her! Hvis ikke, vennligst bruk knappen «Rapporter filproblem». God filkvalitet (%(count)s) Filkvalitet Lær hvordan du <a %(a_metadata)s>forbedrer metadataen</a> for denne filen selv. Problembeskrivelse Vennligst <a %(a_login)s>logg inn</a>. Jeg elsket denne boken! Hjelp fellesskapet ved å rapportere kvaliteten på denne filen! 🙌 Noe gikk galt. Vennligst last inn siden på nytt og prøv igjen. Rapporter filproblem (%(count)s) Takk for at du sendte inn rapporten din. Den vil vises på denne siden, samt bli manuelt gjennomgått av Anna (inntil vi har et ordentlig moderasjonssystem). Legg igjen kommentar Send rapport Hva er galt med denne filen? Lån (%(count)s) Kommentarer (%(count)s) Nedlastinger (%(count)s) Utforsk metadata (%(count)s) Lister (%(count)s) Statistikk (%(count)s) For informasjon om denne spesifikke filen, sjekk ut dens <a %(a_href)s>JSON-fil</a>. Dette er en fil administrert av <a %(a_ia)s>IA’s Controlled Digital Lending</a>-bibliotek, og indeksert av Annas Arkiv for søk. For informasjon om de forskjellige datasetene vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>. Metadata fra tilknyttet post Forbedre metadata på Open Library En «fil MD5» er en hash som beregnes fra filinnholdet, og er rimelig unik basert på det innholdet. Alle skyggelibraryer som vi har indeksert her bruker primært MD5-er for å identifisere filer. En fil kan vises i flere skyggelibraryer. For informasjon om de forskjellige datasetene vi har samlet, se <a %(a_datasets)s>Datasets-siden</a>. Rapporter filkvalitet Totale nedlastinger: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tsjekkisk metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Bøker %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Advarsel: flere tilknyttede poster: Når du ser på en bok på Annas Arkiv, kan du se ulike felt: tittel, forfatter, utgiver, utgave, år, beskrivelse, filnavn, og mer. Alle disse informasjonsbitene kalles <em>metadata</em>. Siden vi kombinerer bøker fra ulike <em>kildesamlinger</em>, viser vi de metadataene som er tilgjengelige i den kildesamlingen. For eksempel, for en bok vi har fått fra Library Genesis, viser vi tittelen fra Library Genesis’ database. Noen ganger finnes en bok i <em>flere</em> kildesamlinger, som kan ha forskjellige metadatafelt. I så fall viser vi ganske enkelt den lengste versjonen av hvert felt, siden den forhåpentligvis inneholder den mest nyttige informasjonen! Vi viser fortsatt de andre feltene under beskrivelsen, f.eks. som ”alternativ tittel” (men bare hvis de er forskjellige). Vi trekker også ut <em>koder</em> som identifikatorer og klassifikatorer fra kildesamlingen. <em>Identifikatorer</em> representerer en bestemt utgave av en bok unikt; eksempler er ISBN, DOI, Open Library ID, Google Books ID, eller Amazon ID. <em>Klassifikatorer</em> grupperer sammen flere lignende bøker; eksempler er Dewey Decimal (DCC), UDC, LCC, RVK, eller GOST. Noen ganger er disse kodene eksplisitt koblet i kildesamlinger, og noen ganger kan vi trekke dem ut fra filnavnet eller beskrivelsen (primært ISBN og DOI). Vi kan bruke identifikatorer til å finne poster i <em>metadata-only samlinger</em>, som OpenLibrary, ISBNdb, eller WorldCat/OCLC. Det er en spesifikk <em>metadata-fane</em> i søkemotoren vår hvis du vil bla gjennom disse samlingene. Vi bruker matchende poster til å fylle ut manglende metadatafelt (f.eks. hvis en tittel mangler), eller f.eks. som “alternativ tittel” (hvis det finnes en eksisterende tittel). For å se nøyaktig hvor metadataene til en bok kom fra, se <em>“Tekniske detaljer” fanen</em> på en bokside. Den har en lenke til rå JSON for den boken, med pekere til rå JSON av de originale postene. For mer informasjon, se følgende sider: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Søk (metadata-fane)</a>, <a %(a_codes)s>Kodeutforsker</a>, og <a %(a_example)s>Eksempel metadata JSON</a>. Til slutt kan alle våre metadata <a %(a_generated)s>genereres</a> eller <a %(a_downloaded)s>lastes ned</a> som ElasticSearch og MariaDB databaser. Bakgrunn Du kan bidra til bevaring av bøker ved å forbedre metadata! Først, les bakgrunnen om metadata på Annas Arkiv, og lær deretter hvordan du kan forbedre metadata gjennom kobling med Open Library, og få gratis medlemskap på Annas Arkiv. Forbedre metadata Så hvis du støter på en fil med dårlige metadata, hvordan skal du fikse det? Du kan gå til kildesamlingen og følge dens prosedyrer for å fikse metadata, men hva skal du gjøre hvis en fil finnes i flere kildesamlinger? Det er en identifikator som behandles spesielt på Annas Arkiv. <strong>Feltet annas_archive md5 på Open Library overstyrer alltid alle andre metadata!</strong> La oss først gå litt tilbake og lære om Open Library. Open Library ble grunnlagt i 2006 av Aaron Swartz med mål om “en nettside for hver bok som noen gang er utgitt”. Det er en slags Wikipedia for bokmetadata: alle kan redigere det, det er fritt lisensiert, og kan lastes ned i bulk. Det er en bokdatabase som er mest i tråd med vårt oppdrag — faktisk har Annas Arkiv blitt inspirert av Aaron Swartz’ visjon og liv. I stedet for å finne opp hjulet på nytt, bestemte vi oss for å omdirigere våre frivillige til Open Library. Hvis du ser en bok som har feil metadata, kan du hjelpe til på følgende måte: Merk at dette kun fungerer for bøker, ikke akademiske artikler eller andre typer filer. For andre typer filer anbefaler vi fortsatt å finne kildebiblioteket. Det kan ta noen uker før endringene blir inkludert i Anna’s Archive, siden vi må laste ned den nyeste Open Library-datadumpen og regenerere vår søkeindeks.  Gå til <a %(a_openlib)s>Open Library nettsiden</a>. Finn den riktige bokposten. <strong>ADVARSEL:</strong> vær sikker på å velge riktig <strong>utgave</strong>. I Open Library har du “verk” og “utgaver”. Et “verk” kan være “Harry Potter og De Vises Stein”. En “utgave” kan være: Førsteutgaven fra 1997 utgitt av Bloomsbery med 256 sider. Pocketutgaven fra 2003 utgitt av Raincoast Books med 223 sider. Den polske oversettelsen fra 2000 “Harry Potter I Kamie Filozoficzn” av Media Rodzina med 328 sider. Alle disse utgavene har forskjellige ISBN-er og forskjellig innhold, så vær sikker på at du velger den riktige! Rediger posten (eller opprett den hvis ingen eksisterer), og legg til så mye nyttig informasjon som mulig! Du er her nå uansett, så du kan like godt gjøre posten virkelig fantastisk. Under “ID-nummer” velger du “Anna’s Archive” og legger til MD5-en til boken fra Anna’s Archive. Dette er den lange strengen av bokstaver og tall etter “/md5/” i URL-en. Prøv å finne andre filer i Anna’s Archive som også samsvarer med denne posten, og legg til disse også. I fremtiden kan vi gruppere disse som duplikater på Anna’s Archive søkeside. Når du er ferdig, skriv ned URL-en som du nettopp oppdaterte. Når du har oppdatert minst 30 poster med Anna’s Archive MD5-er, send oss en <a %(a_contact)s>e-post</a> og send oss listen. Vi gir deg et gratis medlemskap for Anna’s Archive, slik at du lettere kan gjøre dette arbeidet (og som en takk for hjelpen din). Disse må være høykvalitetsredigeringer som legger til betydelige mengder informasjon, ellers vil forespørselen din bli avvist. Forespørselen din vil også bli avvist hvis noen av redigeringene blir omgjort eller korrigert av Open Library-moderatorer. Open Library kobling Hvis du blir betydelig involvert i utviklingen og driften av vårt arbeid, kan vi diskutere å dele mer av donasjonsinntektene med deg, slik at du kan bruke dem etter behov. Vi vil kun betale for vertskap når du har alt satt opp, og har vist at du er i stand til å holde arkivet oppdatert med oppdateringer. Dette betyr at du må betale for de første 1-2 månedene selv. Din tid vil ikke bli kompensert (og det blir heller ikke vår), siden dette er rent frivillig arbeid. Vi er villige til å dekke vertskaps- og VPN-utgifter, i utgangspunktet opptil $200 per måned. Dette er tilstrekkelig for en grunnleggende søkeserver og en DMCA-beskyttet proxy. Vertskapsutgifter Vennligst <strong>ikke kontakt oss</strong> for å be om tillatelse, eller for grunnleggende spørsmål. Handlinger taler høyere enn ord! All informasjonen er der ute, så bare sett i gang med å sette opp ditt speil. Føl deg fri til å legge inn billetter eller forespørsler om sammenslåing til vår Gitlab når du støter på problemer. Vi kan trenge å bygge noen speilspesifikke funksjoner med deg, som å omprofilere fra «Annas Arkiv» til ditt nettstednavn, (i utgangspunktet) deaktivere brukerkontoer, eller lenke tilbake til vårt hovednettsted fra boksidene. Når du har ditt speil oppe og kjører, vennligst kontakt oss. Vi vil gjerne gjennomgå din OpSec, og når den er solid, vil vi lenke til ditt speil og begynne å jobbe tettere sammen med deg. Takk på forhånd til alle som er villige til å bidra på denne måten! Det er ikke for de svake, men det vil styrke varigheten av det største virkelig åpne biblioteket i menneskets historie. Komme i gang For å øke motstandsdyktigheten til Annas Arkiv, ser vi etter frivillige til å kjøre speil. Din versjon er tydelig merket som et speil, f.eks. «Bobs Arkiv, et speil av Annas Arkiv». Du er villig til å ta risikoene forbundet med dette arbeidet, som er betydelige. Du har en dyp forståelse av den nødvendige operasjonelle sikkerheten. Innholdet i <a %(a_shadow)s>disse</a> <a %(a_pirate)s>innleggene</a> er selvinnlysende for deg. I utgangspunktet vil vi ikke gi deg tilgang til våre partner-servernedlastinger, men hvis alt går bra, kan vi dele det med deg. Du kjører den åpne kildekoden til Annas Arkiv, og du oppdaterer regelmessig både koden og dataene. Du er villig til å bidra til vår <a %(a_codebase)s>kodebase</a> — i samarbeid med vårt team — for å få dette til. Vi ser etter dette: Speil: oppfordring til frivillige Lag en annen donasjon. Ingen donasjoner ennå. <a %(a_donate)s>Lag min første donasjon.</a> Detaljer om donasjoner er ikke vist til det offentlige. Mine donasjoner 📡 For bulk speiling av vår samling, sjekk ut <a %(a_datasets)s>Datasets</a> og <a %(a_torrents)s>Torrents</a>-sidene. Nedlastinger fra din IP-adresse de siste 24 timene: %(count)s. 🚀 For raskere nedlastinger og for å hoppe over nettlesersjekkene, <a %(a_membership)s>bli medlem</a>. Last ned fra partnernettsted Du kan gjerne fortsette å bla gjennom Anna’s Archive i en annen fane mens du venter (hvis nettleseren din støtter oppdatering av bakgrunnsfaner). Du kan gjerne vente på at flere nedlastingssider lastes samtidig (men vennligst last ned kun én fil om gangen per server). Når du får en nedlastingslenke, er den gyldig i flere timer. Takk for at du venter, dette holder nettsiden tilgjengelig gratis for alle! 😊 🔗 Alle nedlastingslenker for denne filen: <a %(a_main)s>Filens hovedside</a>. ❌ Sakte nedlastinger er ikke tilgjengelige gjennom Cloudflare VPN-er eller fra Cloudflare IP-adresser. ❌ Sakte nedlastinger er kun tilgjengelige gjennom den offisielle nettsiden. Besøk %(websites)s. 📚 Bruk følgende URL for å laste ned: <a %(a_download)s>Last ned nå</a>. For å gi alle muligheten til å laste ned filer gratis, må du vente før du kan laste ned denne filen. Vennligst vent <span %(span_countdown)s>%(wait_seconds)s</span> sekunder for å laste ned denne filen. Advarsel: det har vært mange nedlastinger fra din IP-adresse de siste 24 timene. Nedlastinger kan være tregere enn vanlig. Hvis du bruker en VPN, delt internettforbindelse, eller din ISP deler IP-er, kan denne advarselen skyldes det. Lagre ❌ Noe gikk galt. Prøv på nytt. ✅ Lagret. Last inn siden på nytt. Bytt visningsnavn. Din identifikator (delen etter "#") kan ikke endres. Profil laget <span %(span_time)s>%(time)s</span> rediger Lister Lag en ny liste med å finne en fil også trykke på "Lister" fanen. Ingen lister ennå Kunne ikke finne profil. Profil For øyeblikket kan vi ikke imøtekomme bokforespørsler. Ikke send oss e-post med bokforespørsler. Vennligst legg inn dine forespørsler på Z-Library eller Libgen-forumene. Post i Annas Arkiv DOI: %(doi)s Last ned SciDB Nexus/STC Ingen forhåndsvisning tilgjengelig ennå. Last ned filen fra <a %(a_path)s>Anna’s Archive</a>. For å støtte tilgjengeligheten og langsiktig bevaring av menneskelig kunnskap, bli en <a %(a_donate)s>medlem</a>. Som en bonus, 🧬&nbsp;SciDB laster raskere for medlemmer, uten noen begrensninger. Fungerer det ikke? Prøv å <a %(a_refresh)s>oppdatere</a>. Sci-Hub Legg til spesifikt søkefelt Søk i beskrivelser og metadata-kommentarer Utgivelsesår Avansert Tilgang Innhold Visning Liste Tabell Filtype Språk Sorter etter Størst Mest relevant Nyeste (filstørrelse) (åpen kildekode) (utgivelsesår) Eldste Tilfeldig Minst Kilde skrapet og åpen-kildekodet av AA Digitalt utlån (%(count)s) Tidsskriftartikler (%(count)s) Vi har funnet treff i: %(in)s. Du kan referere til URL-en som finnes der når du <a %(a_request)s>ber om en fil</a>. Metadata (%(count)s) For å utforske søkeindeksen etter koder, bruk <a %(a_href)s>Codes Explorer</a>. Søkeindeksen oppdateres månedlig. Den inkluderer for øyeblikket oppføringer frem til %(last_data_refresh_date)s. For mer teknisk informasjon, se <a %(link_open_tag)s>datasetsiden</a>. Ekskluder Inkluder kun Umerket mer… Neste … Forrige Denne søkeindeksen inkluderer for øyeblikket metadata fra Internet Archive's Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Mer om våre datasett</a>. For flere digitale utlånsbiblioteker, se <a %(a_wikipedia)s>Wikipedia</a> og <a %(a_mobileread)s>MobileRead Wiki</a>. For DMCA / opphavsrettskrav <a %(a_copyright)s>klikk her</a>. Nedlastingstid Feil under søk. Prøv <a %(a_reload)s>å laste siden på nytt</a>. Hvis problemet vedvarer, vennligst send oss en e-post på %(email)s. Rask nedlasting Faktisk kan hvem som helst hjelpe med å bevare disse filene ved å seede vår <a %(a_torrents)s>samlede liste over torrenter</a>. ➡️ Noen ganger skjer dette feilaktig når søkeserveren er treg. I slike tilfeller kan <a %(a_attrs)s>omlasting</a> hjelpe. ❌ Denne filen kan ha problemer. Leter du etter artikler? Denne søkeindeksen inkluderer for øyeblikket metadata fra ulike metadatakilder. <a %(a_datasets)s>Mer om våre datasett</a>. Det finnes mange, mange kilder til metadata for skriftlige verk rundt om i verden. <a %(a_wikipedia)s>Denne Wikipedia-siden</a> er en god start, men hvis du kjenner til andre gode lister, vennligst gi oss beskjed. For metadata viser vi de originale postene. Vi gjør ingen sammenslåing av poster. Vi har for øyeblikket verdens mest omfattende åpne katalog av bøker, artikler og andre skriftlige verk. Vi speiler Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>og mer</a>. <span class="font-bold">Ingen filer funnet.</span> Prøv færre eller andre søkeord og filtre. Resultater %(from)s-%(to)s (%(total)s totalt) Hvis du finner andre “skyggebiblioteker” som vi bør speile, eller hvis du har noen spørsmål, vennligst kontakt oss på %(email)s. %(num)d delvise treff %(num)d+ delvise treff Skriv i boksen for å søke etter filer i digitale utlånsbiblioteker. Skriv i boksen for å søke i vår katalog med %(count)s filer som kan lastes ned direkte, som vi <a %(a_preserve)s>bevarer for alltid</a>. Skriv i boksen for å søke. Skriv i boksen for å søke i vår katalog av %(count)s akademiske artikler og tidsskriftsartikler, som vi <a %(a_preserve)s>bevarer for alltid</a>. Skriv i boksen for å søke etter metadata fra biblioteker. Dette kan være nyttig når du <a %(a_request)s>ber om en fil</a>. Tips: bruk hurtigtaster “/” (søke fokus), “enter” (søk), “j” (opp), “k” (ned), “<” (forrige side), “>” (neste side) for raskere navigering. Dette er metadataoppføringer, <span %(classname)s>ikke</span> nedlastbare filer. Søkeinnstillinger Søk Digital utlån Last ned Tidsskriftartikler Metadata Nytt søk %(search_input)s - Søk Søket tok for lang tid, noe som betyr at du kan se unøyaktige resultater. Noen ganger hjelper det å <a %(a_reload)s>laste siden på nytt</a>. Søket tok for lang tid, noe som er vanlig for brede søk. Filtertellingene kan være unøyaktige. For store opplastinger (over 10 000 filer) som ikke blir akseptert av Libgen eller Z-Library, vennligst kontakt oss på %(a_email)s. For Libgen.li, sørg for å først logge inn på <a %(a_forum)s>deres forum</a> med brukernavn %(username)s og passord %(password)s, og deretter gå tilbake til deres <a %(a_upload_page)s>opplastingsside</a>. For nå, anbefaler vi å laste opp bøker til Library Genesis-forgreningene. Her er en <a %(a_guide)s>fornuftig guide</a>. Legg merke til at begge forgreningene vi indekserer på denne netsiden drar fra samme opplastningssystem. For små opplastinger (opptil 10 000 filer) vennligst last dem opp til både %(first)s og %(second)s. Alternativt kan du laste dem opp til Z-Library <a %(a_upload)s>her</a>. For å laste opp akademiske artikler, vennligst last dem også opp til <a %(a_stc_nexus)s>STC Nexus</a> (i tillegg til Library Genesis). De er det beste skyggebiblioteket for nye artikler. Vi har ikke integrert dem ennå, men vi vil gjøre det på et tidspunkt. Du kan bruke deres <a %(a_telegram)s>opplastingsbot på Telegram</a>, eller kontakte adressen som er oppført i deres festede melding hvis du har for mange filer til å laste opp på denne måten. <span %(label)s>Krevende frivillig arbeid (USD$50-USD$5,000 belønninger):</span> hvis du kan dedikere mye tid og/eller ressurser til vårt oppdrag, vil vi gjerne samarbeide tettere med deg. Etter hvert kan du bli en del av kjerneteamet. Selv om vi har et stramt budsjett, kan vi tildele <span %(bold)s>💰 pengepremier</span> for det mest intense arbeidet. <span %(label)s>Lett frivillig arbeid:</span> hvis du bare kan avse noen timer her og der, er det fortsatt mange måter du kan hjelpe til på. Vi belønner konsekvente frivillige med <span %(bold)s>🤝 medlemskap til Annas Arkiv</span>. Annas Arkiv er avhengig av frivillige som deg. Vi ønsker alle engasjementsnivåer velkommen, og har to hovedkategorier av hjelp vi ser etter: Hvis du ikke kan bidra med din tid, kan du fortsatt hjelpe oss mye ved å <a %(a_donate)s>donere penger</a>, <a %(a_torrents)s>dele våre torrents</a>, <a %(a_uploading)s>laste opp bøker</a>, eller <a %(a_help)s>fortelle vennene dine om Anna’s Archive</a>. <span %(bold)s>Bedrifter:</span> vi tilbyr høyhastighets direkte tilgang til våre samlinger i bytte mot donasjoner på bedriftsnivå eller bytte mot nye samlinger (f.eks. nye skanninger, OCR’ede datasets, berikelse av våre data). <a %(a_contact)s>Kontakt oss</a> hvis dette gjelder deg. Se også vår <a %(a_llm)s>LLM-side</a>. Dusører Vi er alltid på utkikk etter folk med solide programmerings- eller offensive sikkerhetsferdigheter som kan bidra. Du kan gjøre en betydelig innsats for å bevare menneskehetens arv. Som en takk gir vi bort medlemskap for solide bidrag. Som en stor takk gir vi bort monetære dusører for spesielt viktige og vanskelige oppgaver. Dette bør ikke sees på som en erstatning for en jobb, men det er et ekstra insentiv og kan hjelpe med påløpte kostnader. Mesteparten av koden vår er åpen kildekode, og vi vil be om det samme av koden din når vi tildeler dusøren. Det finnes noen unntak som vi kan diskutere individuelt. Dusører tildeles den første personen som fullfører en oppgave. Kommenter gjerne på en dusørbillett for å la andre vite at du jobber med noe, slik at andre kan holde seg unna eller kontakte deg for å samarbeide. Men vær oppmerksom på at andre fortsatt er fri til å jobbe med det og prøve å slå deg. Vi tildeler imidlertid ikke dusører for slurvete arbeid. Hvis to høykvalitetsinnsendelser gjøres nær hverandre (innen en dag eller to), kan vi velge å tildele dusører til begge, etter vårt skjønn, for eksempel 100%% for den første innsendingen og 50%% for den andre innsendingen (så totalt 150%%). For de større dusørene (spesielt skrapedusører), vennligst kontakt oss når du har fullført ~5%% av det, og du er trygg på at metoden din vil skalere til hele milepælen. Du må dele metoden din med oss slik at vi kan gi tilbakemelding. På denne måten kan vi også bestemme hva vi skal gjøre hvis det er flere personer som nærmer seg en dusør, for eksempel potensielt tildele den til flere personer, oppmuntre folk til å samarbeide, osv. ADVARSEL: oppgavene med høy dusør er <span %(bold)s>vanskelige</span> — det kan være lurt å starte med enklere oppgaver. Gå til vår <a %(a_gitlab)s>Gitlab-issues-liste</a> og sorter etter "Label priority". Dette viser omtrent rekkefølgen på oppgavene vi bryr oss om. Oppgaver uten eksplisitte dusører er fortsatt kvalifisert for medlemskap, spesielt de merket "Accepted" og "Annas favoritt". Du vil kanskje starte med et "Starter project". Lett frivillig arbeid Vi har nå også en synkronisert Matrix-kanal på %(matrix)s. Hvis du har noen timer til overs, kan du hjelpe til på flere måter. Sørg for å bli med i <a %(a_telegram)s>frivilligchatten på Telegram</a>. Som en takk gir vi vanligvis ut 6 måneder med “LykkeLibrarian” for grunnleggende milepæler, og mer for fortsatt frivillig arbeid. Alle milepæler krever arbeid av høy kvalitet — slurvete arbeid skader oss mer enn det hjelper, og vi vil avvise det. Vennligst <a %(a_contact)s>send oss en e-post</a> når du når en milepæl. %(links)s lenker eller skjermbilder av forespørsler du har oppfylt. Oppfylle bok- (eller papir, etc.) forespørsler på Z-Library eller Library Genesis-forumene. Vi har ikke vårt eget bokforespørselssystem, men vi speiler disse bibliotekene, så å gjøre dem bedre gjør også Anna’s Archive bedre. Milepæl Oppgave Avhenger av oppgaven. Små oppgaver lagt ut i vår <a %(a_telegram)s>frivilligchat på Telegram</a>. Vanligvis for medlemskap, noen ganger for små belønninger. Små oppgaver lagt ut i vår frivillige chatgruppe. Sørg for å legge igjen en kommentar på problemer du løser, slik at andre ikke dupliserer arbeidet ditt. %(links)s lenker til poster du har forbedret. Du kan bruke <a %(a_list)s >listen over tilfeldige metadata-problemer</a> som et utgangspunkt. Forbedre metadata ved å <a %(a_metadata)s>lenke</a> med Open Library. Disse bør vise at du forteller noen om Annas Arkiv, og at de takker deg. %(links)s lenker eller skjermbilder. Spre ordet om Annas Arkiv. For eksempel ved å anbefale bøker på AA, lenke til våre blogginnlegg, eller generelt henvise folk til vår nettside. Fullstendig oversettelse av et språk (hvis det ikke allerede var nesten ferdig). <a %(a_translate)s>Oversette</a> nettstedet. Lenke til redigeringshistorikk som viser at du har gjort betydelige bidrag. Forbedre Wikipedia-siden for Anna’s Archive på ditt språk. Inkluder informasjon fra AA’s Wikipedia-side på andre språk, og fra vår nettside og blogg. Legg til referanser til AA på andre relevante sider. Frivillighet og belønninger 