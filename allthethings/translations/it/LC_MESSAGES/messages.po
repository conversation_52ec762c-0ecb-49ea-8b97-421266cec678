msgid "layout.index.invalid_request"
msgstr "Richiesta non valida. Collegati all'indirizzo %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " e "

msgid "layout.index.header.tagline_and_more"
msgstr "e altro ancora"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Mirroring di %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Estraiamo e rendiamo open-source i dati di %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Tutto il nostro codice sorgente e i dati sono completamente open source."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;La più grande biblioteca veramente aperta della storia dell'umanità."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libri, %(paper_count)s&nbsp;documenti, preservati per sempre."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;La più grande biblioteca open-source e open-data al mondo. ⭐️&nbsp;Mirror di Sci-Hub, Library Genesis, Z-Library e molti altri. 📈&nbsp;%(book_any)s libri, %(journal_article)s pubblicazioni, %(book_comic)s fumetti, %(magazine)s riviste, preservati per sempre."

msgid "layout.index.header.tagline_short"
msgstr "📚 La più grande biblioteca open-source e open-data al mondo.<br>⭐️ Mirror di Scihub, Libgen, Zlib e altro."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadati errati (ad esempio: titolo, descrizione, immagine di copertina)"

msgid "common.md5_report_type_mapping.download"
msgstr "Problemi di download (es. problemi di connessione, messaggio di errore, connessione lenta)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Impossibile aprire il file (es. file corrotto, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Bassa qualità (es. problemi di formattazione, bassa qualità di scansione, pagine mancanti)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam/file da rimuovere (es. pubblicità, contenuti offensivi)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamo per violazione di copyright"

msgid "common.md5_report_type_mapping.other"
msgstr "Altro"

msgid "common.membership.tier_name.bonus"
msgstr "Download bonus"

msgid "common.membership.tier_name.2"
msgstr "Topo di biblioteca geniale"

msgid "common.membership.tier_name.3"
msgstr "Bibliotecario fortunato"

msgid "common.membership.tier_name.4"
msgstr "Datahoarder stupefacente"

msgid "common.membership.tier_name.5"
msgstr "Archivista straordinario"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s totale"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totale"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "non retribuito"

msgid "common.donation.order_processing_status_labels.1"
msgstr "retribuito"

msgid "common.donation.order_processing_status_labels.2"
msgstr "annullato"

msgid "common.donation.order_processing_status_labels.3"
msgstr "scaduto"

msgid "common.donation.order_processing_status_labels.4"
msgstr "in attesa di conferma da Anna"

msgid "common.donation.order_processing_status_labels.5"
msgstr "non valido"

msgid "page.donate.title"
msgstr "Donazioni"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Hai già una <a %(a_donation)s>donazione<a/> in corso. Per favore, concludi o annulla quella donazione prima di effettuarne una nuova."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Visualizza tutte le mie donazioni</a>"

msgid "page.donate.header.text1"
msgstr "L'Archivio di Anna è un progetto non-profit, open-source e open-data. Effettuando una donazione, entrerai a far parte della nostra community e sosterrai il nostro lavoro. A tutti i nostri membri, grazie per il vostro aiuto! ❤️"

msgid "page.donate.header.text2"
msgstr "Per maggiori informazioni, consulta le <a %(a_donate)s>FAQ Donazioni</a>."

msgid "page.donate.refer.text1"
msgstr "Per ottenere ancora più download, <a %(a_refer)s>invita chi conosci</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Hai ottenuto il %(percentage)s%% bonus di download rapidi perché l'utente %(profile_link)s ha utilizzato il tuo codice referral."

msgid "page.donate.bonus_downloads.period"
msgstr "Questo si applica all'intero periodo di abbonamento."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s download veloci al giorno"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "se doni questo mese!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mese"

msgid "page.donate.buttons.join"
msgstr "Unisciti a noi"

msgid "page.donate.buttons.selected"
msgstr "Selezionato"

msgid "page.donate.buttons.up_to_discounts"
msgstr "fino al %(percentage)s%% di sconto"

msgid "page.donate.perks.scidb"
msgstr "Paper di SciDB <strong>illimitati</strong> senza verifica"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Accesso API JSON</a>"

msgid "page.donate.perks.refer"
msgstr "Guadagna <strong>%(percentage)s%% download aggiuntivi</strong> <a %(a_refer)s>invitando degli amici</a>."

msgid "page.donate.perks.credits"
msgstr "Il tuo nome utente o una menzione anonima nei ringraziamenti"

msgid "page.donate.perks.previous_plus"
msgstr "Tutti i vantaggi precedenti e in più:"

msgid "page.donate.perks.early_access"
msgstr "Accesso anticipato alle nuove funzionalità"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Canale Telegram riservato con aggiornamenti \"dietro le quinte\""

msgid "page.donate.perks.adopt"
msgstr "\"Adotta un torrent\": il tuo nome utente o un messaggio all'interno del nome di un file torrent <div %(div_months)s>una volta ogni 12 mesi di abbonamento</div>"

msgid "page.donate.perks.legendary"
msgstr "Uno status leggendario nella conservazione della conoscenza e della cultura dell'umanità"

msgid "page.donate.expert.title"
msgstr "Accesso per esperti"

msgid "page.donate.expert.contact_us"
msgstr "contattaci"

msgid "page.donate.small_team"
msgstr "Siamo un piccolo team di volontari e volontarie. Potremmo aver bisogno di 1-2 settimane per risponderti."

msgid "page.donate.expert.unlimited_access"
msgstr "Accesso <strong>illimitato</strong> ad alta velocità"

msgid "page.donate.expert.direct_sftp"
msgstr "Accesso diretto ai server <strong>SFTP</strong>"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donazioni livello enterprise o scambi per nuove raccolte (es. nuove scansioni, set di dati scansionati tramite OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Accettiamo volentieri donazioni importanti da parte di persone o istituzioni facoltose. "

msgid "page.donate.header.large_donations"
msgstr "Per donazioni superiori ai 5000$, per favore contattaci direttamente a %(email)s."

msgid "page.donate.header.recurring"
msgstr "Tieni presente che, sebbene gli abbonamenti su questa pagina siano \"mensili\", si tratta di donazioni una tantum (non ricorrenti). Consulta le <a %(faq)s>FAQ Donazioni</a>."

msgid "page.donate.without_membership"
msgstr "Se desideri fare una donazione (di qualsiasi importo) senza iscriverti, utilizza questo indirizzo Monero (XMR): %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Seleziona un metodo di pagamento."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporaneamente non disponibile)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Carta regalo %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carta bancaria (usando l'app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Criptovaluta %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carta di credito/debito"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regolare)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Carta / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Credito/debito/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasile)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Carta bancaria"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carta di credito/debito (riserva)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carta di credito/debito 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay/WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "Con le criptovalute puoi donare utilizzando BTC, ETH, XMR e SOL. Utilizza questa opzione solo se hai già familiarità con le criptovalute."

msgid "page.donate.payment.desc.crypto2"
msgstr "Puoi donare criptovalute usando BTC, ETH, XMR e molte altre."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Se stai usando criptovalute per la prima volta, ti suggeriamo di usare %(options)s per acquistare e donare Bitcoin (la criptovaluta originale e più utilizzata)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Per donazioni tramite PayPal US, usiamo PayPal Crypto che ci consente di rimanere anonimi. Apprezziamo il tempo che dedichi a imparare come donare tramite questo metodo, e questo ci aiuta molto."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Dona tramite PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Dona tramite Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Se possiedi Cash App, questo è il modo più facile per donare!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Nota: per transazioni al di sotto di %(amount)s, Cash App potrebbe addebitarti una commissione del %(fee)s. Per %(amount)s o più, è gratis!"

msgid "page.donate.payment.desc.revolut"
msgstr "Dona utilizzando Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Se hai Revolut, questo è il modo più semplice per donare!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Dona tramite carta di credito o debito."

msgid "page.donate.payment.desc.google_apple"
msgstr "Anche Google Pay e Apple Pay potrebbero funzionare."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Nota: per le donazioni di piccolo importo, le commissioni delle carte di credito potrebbero eliminare il nostro sconto del %(discount)s. Consigliamo quindi abbonamenti di durata maggiore."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Nota: per le donazioni di piccolo importo, le commissioni potrebbero essere elevate. Ti consigliamo quindi abbonamenti più lunghi."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, acquisti Bitcoin con una carta di credito/debito o un conto bancario, e poi doni quei Bitcoin a noi. In questo modo possiamo rimanere sicuri e anonimi quando accettiamo la tua donazione."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance è disponibile in quasi tutti i paesi e supporta la maggior parte delle banche e delle carte di credito/debito. Attualmente è il nostro principale suggerimento. Apprezziamo che ti prenda del tempo per imparare come donare utilizzando questo metodo, questo ci aiuta molto."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Dona utilizzando il tuo account PayPal regolare."

msgid "page.donate.payment.desc.givebutter"
msgstr "Dona utilizzando carta di credito/debito, PayPal o Venmo. Puoi scegliere tra questi nella pagina successiva."

msgid "page.donate.payment.desc.amazon"
msgstr "Dona tramite una gift card di Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Nota: dobbiamo arrotondare a importi accettati dai nostri rivenditori (minimo %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANTE:</strong> Supportiamo solo Amazon.com, non gli altri domini Amazon. Per esempio, .it, .de, .co, .uk, .ca, NON sono supportati."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANTE:</strong> questa opzione è relativa ad %(amazon)s. Se desideri utilizzare un altro sito Amazon, selezionalo sopra."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Questo metodo utilizza un fornitore di criptovalute come conversione intermedia. Questo può risultare confusionario, quindi utilizza questo metodo solo se gli altri metodi di pagamento non funzionano. Inoltre, non funziona in tutti i paesi."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona utilizzando una carta di credito/debito, tramite l'app Alipay (facilissima da configurare)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installa l'app Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installa l'app Alipay dall'<a %(a_app_store)s>Apple App Store</a> o dal <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrati utilizzando il tuo numero di telefono."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Non sono richiesti ulteriori informazioni personali."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Aggiungi carta bancaria"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supportate: Visa, MasterCard, JCB, Diners Club e Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Consulta <a %(a_alipay)s>questa guida</a> per maggiori informazioni."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Non possiamo supportare direttamente le carte di credito/debito, perché le banche non vogliono lavorare con noi. ☹ Tuttavia, ci sono diversi modi per utilizzare comunque le carte di credito/debito, utilizzando altri metodi di pagamento:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Carta Regalo Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Inviaci buoni regalo Amazon.com utilizzando la tua carta di credito/debito."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay supporta carte di credito/debito internazionali. Vedi <a %(a_alipay)s>questa guida</a> per maggiori informazioni."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) supporta carte di credito/debito internazionali. Nell'app WeChat, vai su “Io => Servizi => Portafoglio => Aggiungi una Carta”. Se non lo vedi, abilitalo andando su “Io => Impostazioni => Generale => Strumenti => Weixin Pay => Abilita”."

msgid "page.donate.ccexp.crypto"
msgstr "Puoi acquistare crypto utilizzando carte di credito/debito."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servizi rapidi di criptovaluta"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "I servizi prioritari sono comodi, ma applicano commissioni più elevate."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Puoi utilizzare questo al posto di una piattaforma di criptovalute se desideri effettuare rapidamente una donazione più rilevante e non ti crea problemi pagare una commissione di $5-10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assicurati di inviare l'importo esatto in criptovaluta visualizzato sulla pagina delle donazioni, non l'importo in USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Altrimenti la commissione verrà addebitata e non potremo elaborare automaticamente il tuo abbonamento."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimo: %(minimum)s a seconda del paese, nessuna verifica per la prima transazione)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimo: %(minimum)s, nessuna verifica per la prima transazione)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimo: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimo: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Se una di queste informazioni non è aggiornata, inviateci un'email per farcelo sapere."

msgid "page.donate.payment.desc.bmc"
msgstr "Per carte di credito, carte di debito, Apple Pay e Google Pay, utilizziamo “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Nel loro sistema, un “caffè” equivale a $5, quindi la tua donazione sarà arrotondata al multiplo di 5 più vicino."

msgid "page.donate.duration.intro"
msgstr "Seleziona la durata dell'abbonamento."

msgid "page.donate.duration.1_mo"
msgstr "1 mese"

msgid "page.donate.duration.3_mo"
msgstr "3 mesi"

msgid "page.donate.duration.6_mo"
msgstr "6 mesi"

msgid "page.donate.duration.12_mo"
msgstr "12 mesi"

msgid "page.donate.duration.24_mo"
msgstr "24 mesi"

msgid "page.donate.duration.48_mo"
msgstr "48 mesi"

msgid "page.donate.duration.96_mo"
msgstr "96 mesi"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>dopo<span %(span_discount)s></span> sconti</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Questo metodo di pagamento richiede un minimo di %(amount)s. Per favore, seleziona una durata o un metodo di pagamento diverso."

msgid "page.donate.buttons.donate"
msgstr "Dona"

msgid "page.donate.payment.maximum_method"
msgstr "Questo metodo di pagamento richiede un massimo di %(amount)s. Per favore, seleziona una durata o un metodo di pagamento differente."

msgid "page.donate.login2"
msgstr "Per diventare membro, per favore <a %(a_login)s>Effettua l'accesso o registrati</a>. Grazie per il tuo supporto!"

msgid "page.donate.payment.crypto_select"
msgstr "Seleziona la tua criptovaluta preferita:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(importo minimo)"

msgid "page.donate.coinbase_eth"
msgstr "(da utilizzare quando si invia Ethereum da Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(attenzione: importo minimo elevato)"

msgid "page.donate.submit.confirm"
msgstr "Clicca sul pulsante \"Dona\" per confermare la donazione."

msgid "page.donate.submit.button"
msgstr "Dona <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Puoi annullare la tua donazione durante il checkout."

msgid "page.donate.submit.success"
msgstr "✅ Reindirizzamento verso la pagina di donazione…"

msgid "page.donate.submit.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s/mese"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "per 1 mese"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "per 3 mesi"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "per 6 mesi"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "per 12 mesi"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "per 24 mesi"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "per 48 mesi"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "per 96 mesi"

msgid "page.donate.submit.button.label.1_mo"
msgstr "per 1 mese \"%(tier_name)s\""

msgid "page.donate.submit.button.label.3_mo"
msgstr "per 3 mesi \"%(tier_name)s\""

msgid "page.donate.submit.button.label.6_mo"
msgstr "per 6 mesi \"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "per 12 mesi \"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "per 24 mesi \"%(tier_name)s\""

msgid "page.donate.submit.button.label.48_mo"
msgstr "per 48 mesi “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "per 96 mesi “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donazione"

msgid "page.donation.header.date"
msgstr "Data: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/mesi per %(duration)s mesi, incluso %(discounts)s%% sconto)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/mese per %(duration)s mesi)</span>"

msgid "page.donation.header.status"
msgstr "Stato: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identificatore: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Annulla"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Sei sicuro di voler annullare? Non annullare se hai già pagato."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Sì, annulla"

msgid "page.donation.header.cancel.success"
msgstr "✅ La tua donazione è stata annullata."

msgid "page.donation.header.cancel.new_donation"
msgstr "Nuova donazione"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo."

msgid "page.donation.header.reorder"
msgstr "Ordina di nuovo"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Hai già pagato. Sei vuoi comunque rivedere le istruzioni di pagamento, clicca qui:"

msgid "page.donation.old_instructions.show_button"
msgstr "Visualizza le vecchie istruzioni di pagamento"

msgid "page.donation.thank_you_donation"
msgstr "Grazie per la tua donazione!"

msgid "page.donation.thank_you.secret_key"
msgstr "Se non l'hai già fatto, recupera la tua chiave segreta che ti permette di connetterti:"

msgid "page.donation.thank_you.locked_out"
msgstr "Altrimenti potresti rimanere bloccato fuori da questo account!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Le istruzioni di pagamento sono obsolete. Se vuoi fare una nuova donazione, usa il tasto \"Ordina di nuovo\" qui sopra."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> I prezzi delle criptovalute possono fluttuare in modo selvaggio, a volte anche del 20%% in pochi minuti. Si tratta comunque di una percentuale inferiore alle commissioni che sosteniamo con molti fornitori di pagamenti, che spesso addebitano il 50-60%% per lavorare con un \"ente di beneficenza ombra\" come noi. <u>Se ci invii la ricevuta con il prezzo originale pagato, ti abiliteremo comunque l'account con la sottoscrizione dell'abbonamento scelto</u> (purché la ricevuta non sia più vecchia di qualche ora). Apprezziamo molto il fatto che tu sia disposto a sopportare cose del genere per sostenerci! ❤️"

msgid "page.donation.expired"
msgstr "Questa donazione è scaduta. Per favore, cancellala e creane una nuova."

msgid "page.donation.payment.crypto.top_header"
msgstr "Istruzioni per criptovalute"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Trasferisci a uno dei nostri account di criptovalute"

msgid "page.donation.payment.crypto.text1"
msgstr "Dona il valore totale di %(total)s a uno dei nostri indirizzi:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Compra Bitcoin tramite PayPal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Trova la pagina \"Cripto\" nella tua app o sito PayPal. Si trova generalmente sotto \"Finanze\". (N.B.: attualmente in Italia non è possibile acquistare e gestire i criptoasset direttamente dal proprio conto PayPal)."

msgid "page.donation.payment.paypal.text3"
msgstr "Segui le istruzioni per acquistare Bitcoin (BTC). È sufficiente acquistare l'importo che si desidera donare, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span> Trasferisci il Bitcoin al nostro indirizzo"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vai alla pagina \"Bitcoin\" nella tua app o sul sito di PayPal. Premi il pulsante \"Trasferisci\" %(transfer_icon)s e poi \"Invia\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Inserisci il nostro indirizzo Bitcoin (BTC) come destinatario e segui le istruzioni per inviare la tua donazione di %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Istruzioni per carta di credito/debito"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona tramite la nostra pagina delle carte di credito/debito"

msgid "page.donation.donate_on_this_page"
msgstr "Dona %(amount)s su <a %(a_page)s>questa pagina</a>."

msgid "page.donation.stepbystep_below"
msgstr "Leggi la guida passo-passo qui sotto."

msgid "page.donation.status_header"
msgstr "Stato:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "In attesa di conferma (aggiorna la pagina per verificare)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "In attesa del trasferimento (aggiorna la pagina per verificare)…"

msgid "page.donation.time_left_header"
msgstr "Tempo rimasto:"

msgid "page.donation.might_want_to_cancel"
msgstr "(potresti voler cancellare e creare una nuova donazione)"

msgid "page.donation.reset_timer"
msgstr "Per reimpostare il timer, semplicemente crea una nuova donazione."

msgid "page.donation.refresh_status"
msgstr "Stato dell'aggiornamento"

msgid "page.donation.footer.issues_contact"
msgstr "Se riscontrassi dei problemi, contattaci via email a %(email)s e includi più informazioni possibili (es. screenshot)."

msgid "page.donation.expired_already_paid"
msgstr "Se hai già pagato:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "A volte la conferma può richiedere fino a 24 ore, quindi assicurati di aggiornare questa pagina (anche se è scaduta)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Compra PYUSD su PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Segui le istruzioni per acquistare PYUSD (Paypal USD)."

msgid "page.donation.pyusd.more"
msgstr "Acquista di più (consigliamo %(more)s in più) dell'ammontare che stai donando (%(amount)s) per coprire le spese di transazione. L'importo che dovesse avanzare resterà tuo."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Vai alla pagina \"PYUSD\" nella tua app o sito PayPal. Premi il pulsante \"Trasferisci\" %(icon)s e poi \"Invia\"."

msgid "page.donation.transfer_amount_to"
msgstr "Trasferisci %(amount)s a %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Acquista Bitcoin (BTC) su Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vai alla pagina “Bitcoin” (BTC) su Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Acquista un po' di più (consigliamo %(more)s in più) rispetto all'importo che stai donando (%(amount)s), per coprire le commissioni di transazione. Manterrai qualsiasi importo rimanente."

msgid "page.donation.cash_app_btc.step2"
msgstr "Trasferisci i Bitcoin al nostro indirizzo"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clicca sul pulsante “Invia bitcoin” per effettuare un “prelievo”. Passa dai dollari ai BTC premendo l'icona %(icon)s. Inserisci l'importo in BTC qui sotto e clicca su “Invia”. Guarda <a %(help_video)s>questo video</a> se hai difficoltà."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Per piccole donazioni (meno di $25), potrebbe essere necessario utilizzare Rush o Priority."

msgid "page.donation.revolut.step1"
msgstr "Acquista Bitcoin (BTC) su Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Vai alla pagina “Criptovaluta” su Revolut per acquistare Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Acquista un po' di più (consigliamo %(more)s in più) rispetto all'importo che stai donando (%(amount)s), per coprire le commissioni di transazione. Manterrai qualsiasi importo rimanente."

msgid "page.donation.revolut.step2"
msgstr "Trasferisci i Bitcoin al nostro indirizzo"

msgid "page.donation.revolut.step2.transfer"
msgstr "Clicca sul pulsante “Invia bitcoin” per effettuare un “prelievo”. Passa dagli euro ai BTC premendo l'icona %(icon)s. Inserisci l'importo in BTC qui sotto e clicca su “Invia”. Guarda <a %(help_video)s>questo video</a> se hai difficoltà."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Assicurati di utilizzare l’importo in BTC indicato qui sotto, <em>NON</em> l'importo in euro o dollari; diversamente, non riceveremo l’importo corretto e non potremo confermare automaticamente la tua iscrizione."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Per piccole donazioni (meno di $25) potrebbe essere necessario utilizzare Rush o Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Utilizza uno dei seguenti servizi “carta di credito a Bitcoin” express, che richiedono solo pochi minuti:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Compilate i seguenti dettagli nel modulo:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Importo BTC / Bitcoin:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Utilizzate questo <span %(underline)s>importo esatto</span>. Il costo totale potrebbe essere più alto a causa delle commissioni della carta di credito. Per importi piccoli, questo potrebbe superare il nostro sconto, purtroppo."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Indirizzo BTC / Bitcoin (portafoglio esterno):"

msgid "page.donation.crypto_instructions"
msgstr "istruzioni %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Supportiamo solo le versioni standard delle criptovalute; nessuna \"criptovaluta esotica\". Può richiedere fino a un'ora per la conferma di avvenuta transazione, in base alla criptovaluta utilizzata."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scansionare il codice QR da pagare"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scansiona questo codice QR con l'app Crypto Wallet per compilare rapidamente i dettagli di pagamento"

msgid "page.donation.amazon.header"
msgstr "Gift card Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Per favore, utilizza <a %(a_form)s>il form ufficiale di Amazon</a> per inviarci una gift card di %(amount)s all'indirizzo email sottostante."

msgid "page.donation.amazon.only_official"
msgstr "Non possiamo accettare altre forme di buoni regali, <strong> solo provenienti direttamente dal form ufficiale di Amazon.com</strong>. Non possiamo restituire il buono regalo se si utilizza un form diverso."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Inserisci l'importo esatto: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Per favore, NON scrivere il tuo messaggio."

msgid "page.donation.amazon.form_to"
msgstr "Campo \"destinatario email\" del form:"

msgid "page.donation.amazon.unique"
msgstr "Unico per il tuo account, non condividere."

msgid "page.donation.amazon.only_use_once"
msgstr "Da utilizzare solo una volta."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "In attesa del buono regalo...(aggiorna la pagina per controllare)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Dopo aver mandato la tua gift card, il nostro sistema automatico la confermerà nell'arco di pochi minuti. Se non funziona, prova a inviarla di nuovo (<a %(a_instr)s>instructions</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Se ancora non funziona, per favore scrivici un'email e il team dell'Archivio di Anna effettuerà un controllo manuale (potrebbe essere necessario qualche giorno). Non dimenticare di indicare se avevi già provato a rimandare questa comunicazione."

msgid "page.donation.amazon.example"
msgstr "Esempio:"

msgid "page.donate.strange_account"
msgstr "Nota: il nome o l'immagine dell'account potrebbero sembrare strani. Non c'è da preoccuparsi! Questi account sono gestiti dai nostri partner per le donazioni. I nostri account non sono stati violati."

msgid "page.donation.payment.alipay.top_header"
msgstr "Istruzioni Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Dona tramite Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account Alipay</a>"

msgid "page.donation.page_blocked"
msgstr "Se la pagina delle donazioni viene bloccata, prova una connessione a Internet diversa (ad esempio VPN o connessione dal telefono)."

msgid "page.donation.payment.alipay.error"
msgstr "Sfortunatamente, la pagina Alipay è spesso accessibile solo dalla <strong>Cina continentale</strong>. Potresti dover disabilitare temporaneamente la tua VPN, o usare una VPN impostata nella Cina continentale (a volte funziona anche Hong Kong)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Effettua la donazione (scansiona il codice QR o premi il pulsante)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Apri la <a %(a_href)s>pagina per fare una donazione con codice QR</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scansiona il codice QR con l'app Alipay, o premi il pulsante per aprire l'app Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Un po' di pazienza; il caricamento della pagina, che si trova in Cina, potrebbe richiedere un po' di tempo."

msgid "page.donation.payment.wechat.top_header"
msgstr "Istruzioni WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Dona tramite WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account WeChat</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Istruzioni Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Dona tramite Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Dona il valore totale di %(total)s tramite <a %(a_account)s>questo account Pix"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Inviaci la ricevuta per email"

msgid "page.donation.footer.verification"
msgstr "Inviate una ricevuta o uno screenshot al vostro indirizzo di verifica personale. NON utilizzate questo indirizzo email per la vostra donazione tramite PayPal."

msgid "page.donation.footer.text1"
msgstr "Manda una ricevuta o screenshot al tuo indirizzo email di verifica personale:"

msgid "page.donation.footer.crypto_note"
msgstr "Se il tasso di conversione della criptovaluta dovesse fluttuare nel mezzo della transazione, assicurati di includere la ricevuta mostrando il tasso di conversione originale. Apprezziamo davvero che ti prendi il disturbo di usare criptovalute, ci aiuta un sacco!"

msgid "page.donation.footer.text2"
msgstr "Quando ci avrai inviato la tua ricevuta, premi questo pulsante cosicché Anna possa verificarla manualmente (potrebbe essere necessario qualche giorno):"

msgid "page.donation.footer.button"
msgstr "Sì, ho inviato la mia ricevuta"

msgid "page.donation.footer.success"
msgstr "✅ Grazie per la donazione! Anna attiverà manualmente il tuo abbonamento entro qualche giorno."

msgid "page.donation.footer.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo."

msgid "page.donation.stepbystep"
msgstr "Guida passo-passo"

msgid "page.donation.crypto_dont_worry"
msgstr "Alcuni dei passaggi menzionati parlano di crypto wallet, ma non preoccuparti: non è necessario che tu impari nulla sulle crypto appositamente."

msgid "page.donation.hoodpay.step1"
msgstr "1. Inserisci la tua email."

msgid "page.donation.hoodpay.step2"
msgstr "2. Seleziona il tuo metodo di pagamento."

msgid "page.donation.hoodpay.step3"
msgstr "3. Seleziona il tuo metodo di pagamento di nuovo."

msgid "page.donation.hoodpay.step4"
msgstr "4. Seleziona \"Self-hosted\" wallet."

msgid "page.donation.hoodpay.step5"
msgstr "5. Clicca \"Confermo ownership\"."

msgid "page.donation.hoodpay.step6"
msgstr "6. Dovresti ricevere una ricevuta tramite email. Per favore, mandacela e confermeremo la tua donazione il prima possibile."

msgid "page.donate.wait_new"
msgstr "Aspetta almeno <span %(span_hours)s24 ore</span> (e aggiorna questa pagina) prima di contattarci."

msgid "page.donate.mistake"
msgstr "Se commetti un errore durante il pagamento, sfortunatamente non possiamo emettere rimborsi ma cercheremo comunque di sistemare le cose."

msgid "page.my_donations.title"
msgstr "Le mie donazioni"

msgid "page.my_donations.not_shown"
msgstr "I dettagli delle donazioni non sono mostrati pubblicamente."

msgid "page.my_donations.no_donations"
msgstr "Ancora nessuna donazione. <a %(a_donate)s>Fai la tua prima donazione.</a>"

msgid "page.my_donations.make_another"
msgstr "Fai un'altra donazione."

msgid "page.downloaded.title"
msgstr "File scaricati"

msgid "page.downloaded.fast_partner_star"
msgstr "I download dai Fast Partner Server sono identificati da %(icon)s."

msgid "page.downloaded.twice"
msgstr "Se hai scaricato un file sia con il download lento che rapido, verrà visualizzato due volte."

msgid "page.downloaded.fast_download_time"
msgstr "I download veloci delle ultime 24 ore vengono conteggiati ai fini del limite giornaliero."

msgid "page.downloaded.times_utc"
msgstr "Tutti gli orari sono espressi in UTC."

msgid "page.downloaded.not_public"
msgstr "I file scaricati non sono mostrati pubblicamente."

msgid "page.downloaded.no_files"
msgstr "Ancora nessun file scaricato."

msgid "page.downloaded.last_18_hours"
msgstr "Ultime 18 ore"

msgid "page.downloaded.earlier"
msgstr "Prima"

msgid "page.account.logged_in.title"
msgstr "Account"

msgid "page.account.logged_out.title"
msgstr "Accedi/Registrati"

msgid "page.account.logged_in.account_id"
msgstr "ID dell'account: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Profilo pubblico: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Chiave segreta (non condividerla!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "Mostra"

msgid "page.account.logged_in.membership_has_some"
msgstr "Abbonamento: <strong>%(tier_name)s</strong> fino al %(until_date)s <a %(a_extend)s>(estendi)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Abbonamento: <strong>Nessuno</strong> <a %(a_become)s>(diventa un membro)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Download veloci utilizzati (ultime 24 ore): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "Quali download?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Gruppo Telegram esclusivo: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Unisciti a noi qui!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Passa a un <a %(a_tier)s>livello superiore</a> per unirti al nostro gruppo."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Contatta Anna via %(email)s se vuoi fare l'upgrade del tuo abbonamento."

msgid "page.contact.title"
msgstr "Email di contatto"

msgid "page.account.logged_in.membership_multiple"
msgstr "Puoi combinare più abbonamenti (i download veloci saranno aggiunti insieme per 24 ore)."

msgid "layout.index.header.nav.public_profile"
msgstr "Profilo pubblico"

msgid "layout.index.header.nav.downloaded_files"
msgstr "File scaricati"

msgid "layout.index.header.nav.my_donations"
msgstr "Le mie donazioni"

msgid "page.account.logged_in.logout.button"
msgstr "Disconnetti"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Sei stato disconnesso. Ricarica la pagina per accedere nuovamente."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo."

msgid "page.account.logged_out.registered.text1"
msgstr "Registrazione completata! La tua chiave segreta è: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Salva questa chiave con cura. Se la perdi, perderai accesso al tuo account."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Preferiti.</strong> Puoi aggiungere questa pagina ai preferiti per recuperare la tua chiave.</li><li %(li_item)s><strong>Scarica.</strong> Premi su <a %(a_download)s>questo link</a> per scaricare la tua chiave.</li><li %(li_item)s><strong>Gestore di password.</strong> Utilizza un gestore di password per salvare la chiave quando la inserisci qui di seguito.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Inserisci la tua chiave segreta per accedere:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Chiave segreta"

msgid "page.account.logged_out.key_form.button"
msgstr "Accedi"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Chiave segreta non valida. Verifica la tua chiave e riprova, o alternativamente effettua la registrazione di un nuovo account qui sotto."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Non perdere la tua chiave!"

msgid "page.account.logged_out.register.header"
msgstr "Non hai ancora un account?"

msgid "page.account.logged_out.register.button"
msgstr "Registra un nuovo account"

msgid "page.login.lost_key"
msgstr "Se hai perso la tua chiave, <a %(a_contact)s>contattaci</a> e forniscici quante più informazioni possibili."

msgid "page.login.lost_key_contact"
msgstr "Potrebbe essere necessario creare temporaneamente un nuovo account per contattarci."

msgid "page.account.logged_out.old_email.button"
msgstr "Hai un vecchio account via email? Inserisci qui il tuo indirizzo <a %(a_open)s>email</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "modifica"

msgid "page.list.edit.button"
msgstr "Salva"

msgid "page.list.edit.success"
msgstr "✅ Salvato. Per favore, ricarica la pagina."

msgid "page.list.edit.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, riprova."

msgid "page.list.by_and_date"
msgstr "Lista di %(by)s, creata il <span %(span_time)s>alle %(time)s</span>"

msgid "page.list.empty"
msgstr "La lista è vuota."

msgid "page.list.new_item"
msgstr "Per aggiungere o rimuovere un file da questo elenco, basta cercare un file e aprire la scheda \"Liste\"."

msgid "page.profile.title"
msgstr "Profilo"

msgid "page.profile.not_found"
msgstr "Profilo non trovato."

msgid "page.profile.header.edit"
msgstr "modifica"

msgid "page.profile.change_display_name.text"
msgstr "Cambia il tuo nome profilo pubblico. Il tuo identificatore (la parte dopo il \"#\") non può essere cambiata."

msgid "page.profile.change_display_name.button"
msgstr "Salva"

msgid "page.profile.change_display_name.success"
msgstr "✅ Salvato. Per favore, ricarica la pagina."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Qualcosa è andato storto. Per favore, riprova."

msgid "page.profile.created_time"
msgstr "Profilo creato il <span %(span_time)s> alle %(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Liste"

msgid "page.profile.lists.no_lists"
msgstr "Ancora nessuna lista"

msgid "page.profile.lists.new_list"
msgstr "Crea un nuovo elenco cercando un file e aprendo la scheda \"Liste\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "La riforma del copyright è necessaria per la sicurezza nazionale"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "In breve: gli LLM cinesi (incluso DeepSeek) sono addestrati sul mio archivio illegale di libri e articoli — il più grande al mondo. L'Occidente deve rivedere la legge sul copyright per motivi di sicurezza nazionale."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articoli correlati di TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Non molto tempo fa, le “biblioteche ombra” stavano scomparendo. Sci-Hub, il grande archivio illegale di articoli accademici, aveva smesso di accettare nuove opere a causa di cause legali. “Z-Library”, la più grande biblioteca illegale di libri, ha visto i suoi presunti creatori arrestati per accuse di violazione del copyright. Sono incredibilmente riusciti a sfuggire all'arresto, ma la loro biblioteca è comunque minacciata."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quando Z-Library ha rischiato la chiusura, avevo già fatto il backup dell'intera biblioteca e stavo cercando una piattaforma per ospitarla. Questa è stata la mia motivazione per avviare l'Archivio di Anna: una continuazione della missione dietro quelle iniziative precedenti. Da allora siamo cresciuti fino a diventare la più grande biblioteca ombra al mondo, ospitando oltre 140 milioni di testi protetti da copyright in numerosi formati — libri, articoli accademici, riviste, giornali e altro ancora."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Il mio team ed io siamo idealisti. Crediamo che preservare e ospitare questi file sia moralmente giusto. Le biblioteche di tutto il mondo stanno subendo tagli ai finanziamenti, e non possiamo nemmeno fidarci del patrimonio dell'umanità alle corporazioni."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Poi è arrivata l'IA. Praticamente tutte le principali aziende che costruiscono LLM ci hanno contattato per addestrarsi sui nostri dati. La maggior parte delle aziende statunitensi (ma non tutte!) ha cambiato idea una volta realizzata la natura illegale del nostro lavoro. Al contrario, le aziende cinesi hanno accolto con entusiasmo la nostra collezione, apparentemente non preoccupate della sua legalità. Questo è notevole dato il ruolo della Cina come firmataria di quasi tutti i principali trattati internazionali sul copyright."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Abbiamo dato accesso ad alta velocità a circa 30 aziende. La maggior parte di esse sono aziende LLM, e alcune sono broker di dati, che rivenderanno la nostra collezione. La maggior parte sono cinesi, anche se abbiamo lavorato anche con aziende degli Stati Uniti, Europa, Russia, Corea del Sud e Giappone. DeepSeek <a %(arxiv)s>ha ammesso</a> che una versione precedente è stata addestrata su parte della nostra collezione, anche se sono riservati riguardo al loro ultimo modello (probabilmente anch'esso addestrato sui nostri dati)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Se l'Occidente vuole rimanere avanti nella corsa agli LLM, e in ultima analisi, all'AGI, deve riconsiderare la sua posizione sul copyright, e presto. Che siate d'accordo o meno con noi sul nostro caso morale, questo sta diventando un caso di economia, e persino di sicurezza nazionale. Tutti i blocchi di potere stanno costruendo super-scienziati artificiali, super-hacker e super-militari. La libertà di informazione sta diventando una questione di sopravvivenza per questi paesi — persino una questione di sicurezza nazionale."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Il nostro team proviene da tutto il mondo, e non abbiamo un allineamento particolare. Ma incoraggeremmo i paesi con leggi sul copyright rigide a usare questa minaccia esistenziale per riformarle. Quindi cosa fare?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "La nostra prima raccomandazione è semplice: accorciare il termine del copyright. Negli Stati Uniti, il copyright è concesso per 70 anni dopo la morte dell'autore. Questo è assurdo. Possiamo allinearlo ai brevetti, che sono concessi per 20 anni dopo il deposito. Questo dovrebbe essere più che sufficiente per gli autori di libri, articoli, musica, arte e altre opere creative, per essere completamente compensati per i loro sforzi (inclusi progetti a lungo termine come gli adattamenti cinematografici)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Poi, come minimo, i responsabili politici dovrebbero includere eccezioni per la conservazione e la diffusione di massa dei testi. Se la perdita di entrate dai clienti individuali è la principale preoccupazione, la distribuzione a livello personale potrebbe rimanere vietata. A loro volta, coloro che sono in grado di gestire vasti archivi — aziende che addestrano LLM, insieme a biblioteche e altri archivi — sarebbero coperti da queste eccezioni."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Alcuni paesi stanno già facendo una versione di questo. TorrentFreak <a %(torrentfreak)s>ha riportato</a> che Cina e Giappone hanno introdotto eccezioni per l'IA nelle loro leggi sul copyright. Non ci è chiaro come questo interagisca con i trattati internazionali, ma certamente offre copertura alle loro aziende domestiche, il che spiega ciò che abbiamo osservato."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Per quanto riguarda l'Archivio di Anna — continueremo il nostro lavoro sotterraneo radicato nella convinzione morale. Tuttavia, il nostro più grande desiderio è di entrare alla luce e amplificare il nostro impatto legalmente. Per favore, riformate il copyright."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leggi gli articoli correlati di TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vincitori della ricompensa di $10,000 per la visualizzazione degli ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "In breve: Abbiamo ricevuto alcune incredibili proposte per la ricompensa di $10,000 per la visualizzazione degli ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Qualche mese fa abbiamo annunciato una <a %(all_isbns)s>ricompensa di $10,000</a> per realizzare la migliore visualizzazione possibile dei nostri dati mostrando lo spazio degli ISBN. Abbiamo sottolineato l'importanza di mostrare quali file abbiamo/abbiamo già archiviato, e successivamente un dataset che descrive quante biblioteche possiedono ISBN (una misura di rarità)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Siamo stati sopraffatti dalla risposta. C'è stata così tanta creatività. Un grande grazie a tutti coloro che hanno partecipato: la vostra energia ed entusiasmo sono contagiosi!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Alla fine volevamo rispondere alle seguenti domande: <strong>quali libri esistono nel mondo, quanti ne abbiamo già archiviati e su quali libri dovremmo concentrarci in seguito?</strong> È fantastico vedere che così tante persone si interessano a queste domande."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Abbiamo iniziato con una visualizzazione di base. In meno di 300kb, questa immagine rappresenta sinteticamente la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tutti gli ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "File nell'Archivio di Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNO di CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuga di dati CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSID di DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indice eBook di EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Libri"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registro Globale degli Editori ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Biblioteca Statale Russa"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Biblioteca Imperiale di Trantor"

#, fuzzy
msgid "common.back"
msgstr "Indietro"

#, fuzzy
msgid "common.forward"
msgstr "Avanti"

#, fuzzy
msgid "common.last"
msgstr "Ultimo"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Si prega di consultare il <a %(all_isbns)s>post originale del blog</a> per ulteriori informazioni."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Abbiamo lanciato una sfida per migliorare questo. Avremmo assegnato un premio di $6.000 per il primo posto, $3.000 per il secondo posto e $1.000 per il terzo posto. A causa della risposta travolgente e delle incredibili proposte, abbiamo deciso di aumentare leggermente il montepremi e assegnare un terzo posto a quattro partecipanti, ciascuno con $500. I vincitori sono elencati di seguito, ma assicuratevi di guardare tutte le proposte <a %(annas_archive)s>qui</a>, o scaricare il nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinato</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Primo posto $6.000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Questa <a %(phiresky_github)s>proposta</a> (<a %(annas_archive_note_2951)s>commento su Gitlab</a>) è semplicemente tutto ciò che volevamo, e anche di più! Ci sono piaciute particolarmente le opzioni di visualizzazione incredibilmente flessibili (anche supportando shader personalizzati), ma con un elenco completo di preset. Ci è piaciuta anche la velocità e la fluidità di tutto, la semplice implementazione (che non ha nemmeno un backend), la mappa intelligente e l'ampia spiegazione nel loro <a %(phiresky_github)s>post sul blog</a>. Un lavoro incredibile, e un vincitore meritatissimo!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Secondo posto $3.000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Un'altra incredibile <a %(annas_archive_note_2913)s>proposta</a>. Non così flessibile come il primo posto, ma in realtà abbiamo preferito la sua visualizzazione a livello macro rispetto al primo posto (curva riempitiva dello spazio, bordi, etichettatura, evidenziazione, panoramica e zoom). Un <a %(annas_archive_note_2971)s>commento</a> di Joe Davis ha risuonato con noi:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Anche se i quadrati e i rettangoli perfetti sono matematicamente piacevoli, non forniscono una superiorità di località in un contesto di mappatura. Credo che l'asimmetria insita in questi Hilbert o Morton classici non sia un difetto ma una caratteristica. Proprio come il famoso contorno a forma di stivale dell'Italia lo rende immediatamente riconoscibile su una mappa, le \"stranezze\" uniche di queste curve possono servire come punti di riferimento cognitivi. Questa distintività può migliorare la memoria spaziale e aiutare gli utenti a orientarsi, potenzialmente rendendo più facile localizzare regioni specifiche o notare schemi.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ancora molte opzioni per la visualizzazione e il rendering, oltre a un'interfaccia utente incredibilmente fluida e intuitiva. Un solido secondo posto!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Terzo posto $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In questa <a %(annas_archive_note_2940)s>proposta</a> ci sono piaciuti davvero i diversi tipi di visualizzazioni, in particolare le visualizzazioni di confronto e quelle dell'editore."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Terzo posto $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Anche se non è l'interfaccia utente più raffinata, questa <a %(annas_archive_note_2917)s>proposta</a> soddisfa molti dei criteri. Ci è piaciuta particolarmente la sua funzione di confronto."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Terzo posto $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Come il primo posto, questa <a %(annas_archive_note_2975)s>proposta</a> ci ha impressionato per la sua flessibilità. In definitiva, questo è ciò che rende uno strumento di visualizzazione eccezionale: massima flessibilità per gli utenti esperti, mantenendo le cose semplici per gli utenti medi."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Terzo posto $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "L'ultima <a %(annas_archive_note_2947)s>proposta</a> a ricevere un premio è piuttosto basilare, ma ha alcune caratteristiche uniche che ci sono piaciute molto. Ci è piaciuto come mostrano quanti datasets coprono un particolare ISBN come misura di popolarità/affidabilità. Ci è piaciuta anche la semplicità ma l'efficacia dell'uso di un cursore di opacità per i confronti."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idee notevoli"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Alcune altre idee e implementazioni che ci sono piaciute particolarmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Grattacieli per rarità"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistiche in tempo reale"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotazioni e anche statistiche in tempo reale"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista mappa unica e filtri"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schema di colori predefinito e mappa di calore."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facile attivazione dei datasets per confronti rapidi."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etichette carine."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra di scala con numero di libri."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Tanti cursori per confrontare datasets, come se fossi un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Potremmo continuare ancora per un po', ma fermiamoci qui. Assicurati di guardare tutte le proposte <a %(annas_archive)s>qui</a>, o scarica il nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinato</a>. Tante proposte, e ognuna porta una prospettiva unica, sia nell'interfaccia utente che nell'implementazione."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Incorporeremo almeno la proposta vincente nel nostro sito principale, e forse anche altre. Abbiamo anche iniziato a pensare a come organizzare il processo di identificazione, conferma e poi archiviazione dei libri più rari. Altre novità in arrivo su questo fronte."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Grazie a tutti coloro che hanno partecipato. È incredibile che così tante persone ci tengano."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "I nostri cuori sono pieni di gratitudine."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualizzare Tutti gli ISBN — Ricompensa di $10,000 entro il 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Questa immagine rappresenta la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Questa immagine è di 1000×800 pixel. Ogni pixel rappresenta 2.500 ISBN. Se abbiamo un file per un ISBN, rendiamo quel pixel più verde. Se sappiamo che un ISBN è stato emesso, ma non abbiamo un file corrispondente, lo rendiamo più rosso."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In meno di 300kb, questa immagine rappresenta sinteticamente la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità (alcune centinaia di GB compressi in totale)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Mostra anche: c'è ancora molto lavoro da fare per il backup dei libri (ne abbiamo solo 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Sfondo"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Come può l'Archivio di Anna raggiungere la sua missione di fare il backup di tutta la conoscenza dell'umanità, senza sapere quali libri sono ancora là fuori? Abbiamo bisogno di una lista di cose da fare. Un modo per mappare questo è attraverso i numeri ISBN, che dagli anni '70 sono stati assegnati a ogni libro pubblicato (nella maggior parte dei paesi)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Non esiste un'autorità centrale che conosca tutte le assegnazioni ISBN. Invece, è un sistema distribuito, dove i paesi ottengono intervalli di numeri, che poi assegnano intervalli più piccoli ai grandi editori, che potrebbero ulteriormente suddividere gli intervalli ai piccoli editori. Infine, i numeri individuali sono assegnati ai libri."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Abbiamo iniziato a mappare gli ISBN <a %(blog)s>due anni fa</a> con il nostro scraping di ISBNdb. Da allora, abbiamo estratto molti più fonti di metadata, come <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby e altri. Un elenco completo può essere trovato nelle pagine “Datasets” e “Torrents” dell'Archivio di Anna. Ora abbiamo di gran lunga la più grande collezione completamente aperta e facilmente scaricabile di metadata di libri (e quindi ISBN) al mondo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Abbiamo <a %(blog)s>scritto ampiamente</a> sul perché ci interessa la conservazione e perché siamo attualmente in una finestra critica. Dobbiamo ora identificare i libri rari, poco focalizzati e unicamente a rischio e preservarli. Avere buoni metadata su tutti i libri del mondo aiuta in questo."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualizzazione"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Oltre all'immagine panoramica, possiamo anche esaminare i singoli datasets che abbiamo acquisito. Usa il menu a tendina e i pulsanti per passare da uno all'altro."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Ci sono molti schemi interessanti da vedere in queste immagini. Perché c'è una certa regolarità di linee e blocchi, che sembra accadere a scale diverse? Quali sono le aree vuote? Perché certi datasets sono così raggruppati? Lasceremo queste domande come esercizio per il lettore."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Ricompensa di $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "C'è molto da esplorare qui, quindi stiamo annunciando una ricompensa per migliorare la visualizzazione sopra. A differenza della maggior parte delle nostre ricompense, questa è a tempo limitato. Devi <a %(annas_archive)s>inviare</a> il tuo codice open source entro il 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "La migliore proposta riceverà $6,000, il secondo posto $3,000 e il terzo posto $1,000. Tutte le ricompense saranno assegnate utilizzando Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Di seguito sono riportati i criteri minimi. Se nessuna proposta soddisfa i criteri, potremmo comunque assegnare alcune ricompense, ma sarà a nostra discrezione."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forka questo repo e modifica questo post del blog in HTML (non sono consentiti altri backend oltre al nostro backend Flask)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Rendi l'immagine sopra zoomabile in modo fluido, così puoi zoomare fino agli ISBN individuali. Cliccando sugli ISBN si dovrebbe accedere a una pagina di metadata o a una ricerca nell'Archivio di Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Devi comunque essere in grado di passare tra tutti i diversi datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Gli intervalli di paesi e gli intervalli degli editori dovrebbero essere evidenziati al passaggio del mouse. Puoi usare ad esempio <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> per le informazioni sui paesi, e il nostro scraping “isbngrp” per gli editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Deve funzionare bene su desktop e mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Per punti bonus (queste sono solo idee — lascia che la tua creatività si scateni):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Verrà data forte considerazione all'usabilità e all'aspetto estetico."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Mostra i metadata effettivi per gli ISBN individuali quando si zooma, come titolo e autore."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Migliore curva di riempimento dello spazio. Ad esempio, un zig-zag, andando da 0 a 4 sulla prima riga e poi indietro (in senso inverso) da 5 a 9 sulla seconda riga — applicato ricorsivamente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Schemi di colore diversi o personalizzabili."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Viste speciali per confrontare i datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modi per risolvere problemi, come altri metadata che non concordano bene (ad esempio titoli molto diversi)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotare immagini con commenti su ISBN o intervalli."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualsiasi euristica per identificare libri rari o a rischio."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Qualsiasi idea creativa che puoi inventare!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Puoi completamente deviare dai criteri minimi e fare una visualizzazione completamente diversa. Se è davvero spettacolare, allora si qualifica per la ricompensa, ma a nostra discrezione."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Fai le tue proposte postando un commento a <a %(annas_archive)s>questo problema</a> con un link al tuo repository forkato, richiesta di merge o diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Codice"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Il codice per generare queste immagini, così come altri esempi, può essere trovato in <a %(annas_archive)s>questa directory</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Abbiamo ideato un formato dati compatto, con il quale tutte le informazioni ISBN richieste occupano circa 75MB (compresso). La descrizione del formato dati e il codice per generarlo possono essere trovati <a %(annas_archive_l1244_1319)s>qui</a>. Per la ricompensa non sei obbligato a usarlo, ma è probabilmente il formato più conveniente per iniziare. Puoi trasformare i nostri metadata come vuoi (anche se tutto il tuo codice deve essere open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Non vediamo l'ora di vedere cosa inventerai. Buona fortuna!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contenitori di Archivio di Anna (AAC): standardizzare le pubblicazioni dalla più grande biblioteca ombra del mondo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "L'Archivio di Anna è diventato la più grande biblioteca ombra del mondo, richiedendoci di standardizzare le nostre pubblicazioni."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>L'Archivio di Anna</a> è diventato di gran lunga la più grande biblioteca ombra del mondo, e l'unica biblioteca ombra della sua scala che è completamente open-source e open-data. Di seguito è riportata una tabella dalla nostra pagina Datasets (leggermente modificata):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Abbiamo raggiunto questo obiettivo in tre modi:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Rispecchiando le biblioteche ombra open-data esistenti (come Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Aiutando le biblioteche ombra che vogliono essere più aperte, ma non avevano il tempo o le risorse per farlo (come la collezione di fumetti di Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raccogliendo dati da biblioteche che non desiderano condividere in massa (come Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Per (2) e (3) ora gestiamo una considerevole collezione di torrent noi stessi (centinaia di TB). Finora abbiamo trattato queste collezioni come casi unici, il che significa infrastruttura e organizzazione dei dati su misura per ogni collezione. Questo aggiunge un notevole carico a ogni rilascio e rende particolarmente difficile effettuare rilasci più incrementali."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Ecco perché abbiamo deciso di standardizzare i nostri rilasci. Questo è un post tecnico del blog in cui stiamo introducendo il nostro standard: <strong>Contenitori di Archivio di Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Obiettivi di progettazione"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Il nostro caso d'uso principale è la distribuzione di file e metadata associati da diverse collezioni esistenti. Le nostre considerazioni più importanti sono:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "File e metadata eterogenei, nel formato più vicino possibile all'originale."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatori eterogenei nelle librerie di origine, o addirittura assenza di identificatori."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Rilasci separati di metadata rispetto ai dati dei file, o rilasci solo di metadata (ad esempio il nostro rilascio ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribuzione tramite torrent, ma con la possibilità di altri metodi di distribuzione (ad esempio IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registri immutabili, poiché dovremmo presumere che i nostri torrent vivranno per sempre."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Rilasci incrementali / rilasci aggiuntivi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Leggibile e scrivibile da macchine, in modo conveniente e veloce, specialmente per il nostro stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Ispezione umana relativamente facile, anche se questo è secondario rispetto alla leggibilità da parte delle macchine."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile da seminare le nostre collezioni con un seedbox standard a noleggio."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "I dati binari possono essere serviti direttamente da server web come Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Alcuni obiettivi non prioritari:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Non ci interessa che i file siano facili da navigare manualmente su disco, o ricercabili senza pre-elaborazione."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Non ci interessa essere direttamente compatibili con il software di libreria esistente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Sebbene dovrebbe essere facile per chiunque seminare la nostra collezione utilizzando i torrent, non ci aspettiamo che i file siano utilizzabili senza una significativa conoscenza tecnica e impegno."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Poiché l'Archivio di Anna è open source, vogliamo utilizzare direttamente il nostro formato. Quando aggiorniamo il nostro indice di ricerca, accediamo solo a percorsi pubblicamente disponibili, in modo che chiunque faccia un fork della nostra libreria possa avviarsi rapidamente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Lo standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Alla fine, abbiamo optato per uno standard relativamente semplice. È piuttosto flessibile, non normativo e in fase di sviluppo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archive Container) è un singolo elemento composto da <strong>metadata</strong> e, facoltativamente, da <strong>dati binari</strong>, entrambi immutabili. Ha un identificatore univoco globale, chiamato <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collezione.</strong> Ogni AAC appartiene a una collezione, che per definizione è un elenco di AAC semanticamente coerenti. Ciò significa che se apporti una modifica significativa al formato dei metadata, devi creare una nuova collezione."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Collezioni di “record” e “file”.</strong> Per convenzione, è spesso conveniente rilasciare “record” e “file” come collezioni diverse, in modo che possano essere rilasciate in orari diversi, ad esempio in base ai tassi di scraping. Un “record” è una collezione solo di metadata, contenente informazioni come titoli di libri, autori, ISBN, ecc., mentre i “file” sono le collezioni che contengono i file effettivi (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Il formato di AACID è il seguente: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Ad esempio, un AACID effettivo che abbiamo rilasciato è <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: il nome della collezione, che può contenere lettere ASCII, numeri e underscore (ma non doppi underscore)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: una versione breve dell'ISO 8601, sempre in UTC, ad esempio <code>20220723T194746Z</code>. Questo numero deve aumentare monotonamente per ogni rilascio, sebbene i suoi esatti significati possano differire per collezione. Suggeriamo di utilizzare il tempo di scraping o di generazione dell'ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificatore specifico della collezione, se applicabile, ad esempio l'ID di Z-Library. Può essere omesso o troncato. Deve essere omesso o troncato se l'AACID altrimenti supererebbe i 150 caratteri."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID ma compresso in ASCII, ad esempio utilizzando base57. Attualmente utilizziamo la libreria Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Intervallo AACID.</strong> Poiché gli AACID contengono timestamp che aumentano monotonamente, possiamo usarli per indicare intervalli all'interno di una particolare collezione. Utilizziamo questo formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, dove i timestamp sono inclusivi. Questo è coerente con la notazione ISO 8601. Gli intervalli sono continui e possono sovrapporsi, ma in caso di sovrapposizione devono contenere record identici a quelli precedentemente rilasciati in quella collezione (poiché gli AAC sono immutabili). Non sono consentiti record mancanti."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>File di metadata.</strong> Un file di metadata contiene i metadata di un intervallo di AAC, per una particolare collezione. Questi hanno le seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Il nome del file deve essere un intervallo AACID, preceduto da <code style=\"color: red\">annas_archive_meta__</code> e seguito da <code>.jsonl.zstd</code>. Ad esempio, uno dei nostri rilasci è chiamato<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Come indicato dall'estensione del file, il tipo di file è <a %(jsonlines)s>JSON Lines</a> compresso con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Ogni oggetto JSON deve contenere i seguenti campi al livello superiore: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opzionale). Non sono consentiti altri campi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> sono metadata arbitrari, secondo la semantica della collezione. Devono essere semanticamente coerenti all'interno della collezione."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> è opzionale ed è il nome della cartella di dati binari che contiene i dati binari corrispondenti. Il nome del file dei dati binari corrispondenti all'interno di quella cartella è l'AACID del record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Il prefisso <code style=\"color: red\">annas_archive_meta__</code> può essere adattato al nome della tua istituzione, ad esempio <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Cartella di dati binari.</strong> Una cartella con i dati binari di un intervallo di AAC, per una particolare collezione. Questi hanno le seguenti proprietà:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Il nome della directory deve essere un intervallo AACID, preceduto da <code style=\"color: green\">annas_archive_data__</code>, e senza suffisso. Ad esempio, uno dei nostri rilasci effettivi ha una directory chiamata<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "La directory deve contenere file di dati per tutti gli AAC all'interno dell'intervallo specificato. Ogni file di dati deve avere il suo AACID come nome del file (senza estensioni)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Si consiglia di rendere queste cartelle gestibili in termini di dimensioni, ad esempio non più grandi di 100GB-1TB ciascuna, anche se questa raccomandazione potrebbe cambiare nel tempo."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> I file di metadata e le cartelle di dati binari possono essere raggruppati in torrent, con un torrent per file di metadata o un torrent per cartella di dati binari. I torrent devono avere il nome originale del file/directory più un suffisso <code>.torrent</code> come nome del file."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Esempio"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Prendiamo come esempio il nostro recente rilascio di Z-Library. Consiste di due collezioni: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Questo ci permette di estrarre e rilasciare separatamente i record di metadata dai file effettivi dei libri. Pertanto, abbiamo rilasciato due torrent con file di metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Abbiamo anche rilasciato un gruppo di torrent con cartelle di dati binari, ma solo per la collezione “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 in totale:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Eseguendo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> possiamo vedere cosa c’è dentro:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In questo caso, si tratta di metadata di un libro come riportato da Z-Library. A livello superiore abbiamo solo “aacid” e “metadata”, ma nessuna “data_folder”, poiché non ci sono dati binari corrispondenti. L'AACID contiene “22430000” come ID principale, che possiamo vedere è preso da “zlibrary_id”. Possiamo aspettarci che altri AAC in questa collezione abbiano la stessa struttura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Ora eseguiamo <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Questo è un metadata AAC molto più piccolo, sebbene la maggior parte di questo AAC si trovi altrove in un file binario! Dopotutto, questa volta abbiamo una “data_folder”, quindi possiamo aspettarci che i dati binari corrispondenti si trovino in <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Il “metadata” contiene lo “zlibrary_id”, quindi possiamo facilmente associarlo all'AAC corrispondente nella collezione “zlib_records”. Avremmo potuto associarlo in diversi modi, ad esempio tramite AACID — lo standard non lo prescrive."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nota che non è necessario che il campo “metadata” sia esso stesso JSON. Potrebbe essere una stringa contenente XML o qualsiasi altro formato di dati. Potresti persino memorizzare le informazioni di metadata nel blob binario associato, ad esempio se si tratta di molti dati."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusione"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con questo standard, possiamo effettuare rilasci in modo più incrementale e aggiungere più facilmente nuove fonti di dati. Abbiamo già alcuni rilasci entusiasmanti in cantiere!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Speriamo anche che diventi più facile per altre biblioteche ombra fare il mirror delle nostre collezioni. Dopotutto, il nostro obiettivo è preservare per sempre la conoscenza e la cultura umana, quindi più ridondanza c'è, meglio è."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Aggiornamento di Anna: archivio completamente open source, ElasticSearch, oltre 300GB di copertine di libri"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Abbiamo lavorato incessantemente per fornire una buona alternativa con l'Archivio di Anna. Ecco alcune delle cose che abbiamo realizzato di recente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Con la chiusura di Z-Library e l'arresto dei suoi (presunti) fondatori, abbiamo lavorato incessantemente per fornire una buona alternativa con l'Archivio di Anna (non lo linkeremo qui, ma puoi cercarlo su Google). Ecco alcune delle cose che abbiamo realizzato di recente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "L'Archivio di Anna è completamente open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Crediamo che l'informazione debba essere libera, e il nostro codice non fa eccezione. Abbiamo rilasciato tutto il nostro codice sulla nostra istanza Gitlab privata: <a %(annas_archive)s>Software di Anna</a>. Utilizziamo anche il tracker dei problemi per organizzare il nostro lavoro. Se vuoi partecipare al nostro sviluppo, questo è un ottimo punto di partenza."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Per darti un assaggio delle cose su cui stiamo lavorando, prendi il nostro recente lavoro sui miglioramenti delle prestazioni lato client. Poiché non abbiamo ancora implementato la paginazione, spesso restituivamo pagine di ricerca molto lunghe, con 100-200 risultati. Non volevamo interrompere troppo presto i risultati della ricerca, ma questo significava rallentare alcuni dispositivi. Per questo, abbiamo implementato un piccolo trucco: abbiamo avvolto la maggior parte dei risultati di ricerca in commenti HTML (<code><!-- --></code>), e poi abbiamo scritto un piccolo Javascript che rilevava quando un risultato doveva diventare visibile, momento in cui avremmo rimosso il commento:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "La \"virtualizzazione\" del DOM implementata in 23 righe, senza bisogno di librerie sofisticate! Questo è il tipo di codice pragmatico e veloce che si ottiene quando si ha poco tempo e problemi reali da risolvere. È stato segnalato che la nostra ricerca ora funziona bene su dispositivi lenti!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un altro grande sforzo è stato automatizzare la costruzione del database. Quando abbiamo lanciato, abbiamo semplicemente messo insieme diverse fonti in modo casuale. Ora vogliamo mantenerle aggiornate, quindi abbiamo scritto una serie di script per scaricare nuovi metadata dai due fork di Library Genesis e integrarli. L'obiettivo è non solo rendere questo utile per il nostro archivio, ma anche facilitare le cose a chiunque voglia sperimentare con i metadata delle biblioteche ombra. L'obiettivo sarebbe un notebook Jupyter che abbia a disposizione tutti i tipi di metadata interessanti, così possiamo fare più ricerche come capire quale <a %(blog)s>percentuale di ISBN è preservata per sempre</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Infine, abbiamo rinnovato il nostro sistema di donazioni. Ora puoi usare una carta di credito per depositare direttamente denaro nei nostri portafogli di criptovaluta, senza dover sapere davvero nulla sulle criptovalute. Continueremo a monitorare quanto bene funzioni in pratica, ma è un grande passo avanti."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Passa a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Uno dei nostri <a %(annas_archive)s>ticket</a> era un insieme di problemi con il nostro sistema di ricerca. Usavamo la ricerca full-text di MySQL, dato che avevamo tutti i nostri dati in MySQL comunque. Ma aveva i suoi limiti:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Alcune query richiedevano un tempo lunghissimo, al punto da monopolizzare tutte le connessioni aperte."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Per impostazione predefinita, MySQL ha una lunghezza minima delle parole, o il tuo indice può diventare davvero grande. Le persone hanno segnalato di non poter cercare \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "La ricerca era solo relativamente veloce quando completamente caricata in memoria, il che ci richiedeva di ottenere una macchina più costosa per eseguirla, oltre ad alcuni comandi per precaricare l'indice all'avvio."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Non saremmo stati in grado di estenderlo facilmente per costruire nuove funzionalità, come una migliore <a %(wikipedia_cjk_characters)s>tokenizzazione per lingue senza spazi</a>, filtraggio/faccettatura, ordinamento, suggerimenti \"intendevi dire\", completamento automatico, e così via."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Dopo aver parlato con un gruppo di esperti, abbiamo optato per ElasticSearch. Non è stato perfetto (i loro suggerimenti \"intendevi dire\" e le funzionalità di completamento automatico di default non sono granché), ma nel complesso è stato molto meglio di MySQL per la ricerca. Non siamo ancora <a %(youtube)s>troppo entusiasti</a> di usarlo per dati mission-critical (anche se hanno fatto molti <a %(elastic_co)s>progressi</a>), ma nel complesso siamo piuttosto soddisfatti del cambiamento."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Per ora, abbiamo implementato una ricerca molto più veloce, un miglior supporto linguistico, un ordinamento per rilevanza migliore, diverse opzioni di ordinamento e filtraggio per lingua/tipo di libro/tipo di file. Se sei curioso di sapere come funziona, <a %(annas_archive_l140)s>dai</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>occhiata</a>. È abbastanza accessibile, anche se potrebbe usare qualche commento in più…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Oltre 300GB di copertine di libri rilasciate"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Infine, siamo felici di annunciare un piccolo rilascio. In collaborazione con le persone che gestiscono il fork Libgen.rs, stiamo condividendo tutte le loro copertine di libri tramite torrent e IPFS. Questo distribuirà il carico di visualizzazione delle copertine tra più macchine e le preserverà meglio. In molti (ma non tutti) i casi, le copertine dei libri sono incluse nei file stessi, quindi si tratta di una sorta di \"dati derivati\". Ma averle in IPFS è comunque molto utile per l'operatività quotidiana sia di Archivio di Anna che dei vari fork di Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Come al solito, puoi trovare questo rilascio al Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>). Non lo linkeremo qui, ma puoi trovarlo facilmente."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Speriamo di poter rallentare un po' il nostro ritmo, ora che abbiamo un'alternativa decente a Z-Library. Questo carico di lavoro non è particolarmente sostenibile. Se sei interessato ad aiutarci con la programmazione, le operazioni sui server o il lavoro di conservazione, contattaci sicuramente. C'è ancora molto <a %(annas_archive)s>lavoro da fare</a>. Grazie per il tuo interesse e supporto."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Archivio di Anna ha eseguito il backup della più grande biblioteca ombra di fumetti del mondo (95TB) — puoi aiutare a condividerla"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "La più grande biblioteca ombra di fumetti del mondo aveva un singolo punto di fallimento... fino ad oggi."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discuti su Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "La più grande biblioteca ombra di fumetti è probabilmente quella di un particolare fork di Library Genesis: Libgen.li. L'unico amministratore che gestisce quel sito è riuscito a raccogliere una collezione di fumetti incredibile di oltre 2 milioni di file, per un totale di oltre 95TB. Tuttavia, a differenza di altre collezioni di Library Genesis, questa non era disponibile in blocco tramite torrent. Potevi accedere a questi fumetti solo individualmente tramite il suo server personale lento — un singolo punto di fallimento. Fino ad oggi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In questo post vi parleremo di più di questa collezione e della nostra raccolta fondi per supportare ulteriormente questo lavoro."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>La Dott.ssa Barbara Gordon cerca di perdersi nel mondo banale della biblioteca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Fork di Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Innanzitutto, un po' di contesto. Potreste conoscere Library Genesis per la loro epica collezione di libri. Poche persone sanno che i volontari di Library Genesis hanno creato altri progetti, come una vasta collezione di riviste e documenti standard, un backup completo di Sci-Hub (in collaborazione con la fondatrice di Sci-Hub, Alexandra Elbakyan) e, in effetti, una collezione massiccia di fumetti."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Ad un certo punto, diversi operatori degli specchi di Library Genesis hanno preso strade separate, il che ha dato origine alla situazione attuale di avere diversi \"fork\", tutti ancora con il nome Library Genesis. Il fork Libgen.li ha in modo univoco questa collezione di fumetti, oltre a una considerevole collezione di riviste (su cui stiamo anche lavorando)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaborazione"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Data la sua dimensione, questa collezione è stata a lungo nella nostra lista dei desideri, quindi dopo il nostro successo con il backup di Z-Library, abbiamo puntato su questa collezione. All'inizio l'abbiamo raschiata direttamente, il che è stata una vera sfida, poiché il loro server non era nelle migliori condizioni. In questo modo abbiamo ottenuto circa 15TB, ma è stato un processo lento."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Fortunatamente, siamo riusciti a metterci in contatto con l'operatore della biblioteca, che ha accettato di inviarci tutti i dati direttamente, il che è stato molto più veloce. Ci è voluto comunque più di mezzo anno per trasferire e processare tutti i dati, e abbiamo quasi perso tutto a causa della corruzione del disco, il che avrebbe significato ricominciare da capo."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Questa esperienza ci ha fatto credere che sia importante diffondere questi dati il più rapidamente possibile, in modo che possano essere replicati ampiamente. Siamo solo a uno o due incidenti sfortunati dal perdere questa collezione per sempre!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "La collezione"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Muoversi velocemente significa che la collezione è un po' disorganizzata… Diamo un'occhiata. Immaginate di avere un file system (che in realtà stiamo suddividendo tra i torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "La prima directory, <code>/repository</code>, è la parte più strutturata di questo. Questa directory contiene i cosiddetti “mille dir”: directory ciascuna con migliaia di file, che sono numerati incrementali nel database. La directory <code>0</code> contiene file con comic_id da 0 a 999, e così via."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Questo è lo stesso schema che Library Genesis ha utilizzato per le sue collezioni di narrativa e saggistica. L'idea è che ogni “mille dir” venga automaticamente trasformato in un torrent non appena è pieno."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tuttavia, l'operatore di Libgen.li non ha mai creato torrent per questa collezione, e quindi i mille dir probabilmente sono diventati scomodi, e hanno lasciato il posto a “dir non ordinati”. Questi sono <code>/comics0</code> fino a <code>/comics4</code>. Contengono tutte strutture di directory uniche, che probabilmente avevano senso per raccogliere i file, ma non hanno molto senso per noi ora. Fortunatamente, i metadata si riferiscono ancora direttamente a tutti questi file, quindi la loro organizzazione di archiviazione su disco non ha effettivamente importanza!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "I metadata sono disponibili sotto forma di un database MySQL. Questo può essere scaricato direttamente dal sito web di Libgen.li, ma lo renderemo disponibile anche in un torrent, insieme alla nostra tabella con tutti gli hash MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analisi"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quando ricevi 95TB scaricati nel tuo cluster di archiviazione, cerchi di capire cosa c'è dentro… Abbiamo fatto alcune analisi per vedere se potevamo ridurre un po' le dimensioni, ad esempio rimuovendo i duplicati. Ecco alcune delle nostre scoperte:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "I duplicati semantici (scansioni diverse dello stesso libro) possono teoricamente essere filtrati, ma è complicato. Quando abbiamo esaminato manualmente i fumetti abbiamo trovato troppi falsi positivi."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Ci sono alcuni duplicati puramente per MD5, che è relativamente dispendioso, ma filtrarli ci darebbe solo circa 1% in di risparmio. A questa scala è comunque circa 1TB, ma anche, a questa scala 1TB non importa davvero. Preferiremmo non rischiare di distruggere accidentalmente i dati in questo processo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Abbiamo trovato un sacco di dati non relativi ai libri, come film basati su fumetti. Anche questo sembra dispendioso, poiché sono già ampiamente disponibili attraverso altri mezzi. Tuttavia, ci siamo resi conto che non potevamo semplicemente filtrare i file dei film, poiché ci sono anche <em>fumetti interattivi</em> che sono stati rilasciati sul computer, che qualcuno ha registrato e salvato come film."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Alla fine, qualsiasi cosa avremmo potuto eliminare dalla collezione avrebbe risparmiato solo pochi percentuali. Poi ci siamo ricordati che siamo accumulatori di dati, e le persone che faranno il mirror di questo sono anche accumulatori di dati, e quindi, \"COSA INTENDI, ELIMINARE?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Vi presentiamo quindi la collezione completa e non modificata. È un sacco di dati, ma speriamo che abbastanza persone si prendano cura di condividerli comunque."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Raccolta fondi"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Stiamo rilasciando questi dati in alcuni grandi blocchi. Il primo torrent è di <code>/comics0</code>, che abbiamo messo in un enorme file .tar da 12TB. È meglio per il tuo hard disk e il software torrent rispetto a una miriade di file più piccoli."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Come parte di questo rilascio, stiamo facendo una raccolta fondi. Stiamo cercando di raccogliere $20,000 per coprire i costi operativi e di contrattazione per questa collezione, oltre a consentire progetti in corso e futuri. Abbiamo alcuni <em>enormi</em> in lavorazione."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Chi sto supportando con la mia donazione?</em> In breve: stiamo facendo il backup di tutta la conoscenza e la cultura dell'umanità, rendendola facilmente accessibile. Tutto il nostro codice e i dati sono open source, siamo un progetto gestito completamente da volontari, e finora abbiamo salvato 125TB di libri (oltre ai torrent esistenti di Libgen e Scihub). In definitiva, stiamo costruendo un volano che consente e incentiva le persone a trovare, scansionare e fare il backup di tutti i libri del mondo. Scriveremo del nostro piano maestro in un post futuro. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Se doni per un abbonamento di 12 mesi \"Ammirevole archivista\" ($780), puoi <strong>“adottare un torrent”</strong>, il che significa che metteremo il tuo nome utente o messaggio nel nome di uno dei torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Puoi donare andando su <a %(wikipedia_annas_archive)s>Archivio di Anna</a> e cliccando sul pulsante “Dona”. Stiamo anche cercando più volontari: ingegneri software, ricercatori di sicurezza, esperti di commercio anonimo e traduttori. Puoi anche supportarci fornendo servizi di hosting. E naturalmente, per favore condividi i nostri torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Grazie a tutti coloro che ci hanno già supportato così generosamente! State davvero facendo la differenza."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Ecco i torrent rilasciati finora (stiamo ancora elaborando il resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tutti i torrent possono essere trovati su <a %(wikipedia_annas_archive)s>Archivio di Anna</a> sotto “Datasets” (non colleghiamo direttamente lì, quindi i link a questo blog non vengono rimossi da Reddit, Twitter, ecc.). Da lì, segui il link al sito web di Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Cosa c'è dopo?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un gruppo di torrent è ottimo per la conservazione a lungo termine, ma non tanto per l'accesso quotidiano. Lavoreremo con partner di hosting per mettere tutti questi dati sul web (poiché l'Archivio di Anna non ospita nulla direttamente). Naturalmente, potrai trovare questi link di download sull'Archivio di Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Invitiamo anche tutti a fare qualcosa con questi dati! Aiutaci ad analizzarli meglio, deduplicarli, metterli su IPFS, remixarli, addestrare i tuoi modelli di IA con essi, e così via. Sono tutti tuoi, e non vediamo l'ora di vedere cosa farai con essi."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Infine, come detto prima, abbiamo ancora alcuni rilasci enormi in arrivo (se <em>qualcuno</em> potesse <em>accidentalmente</em> inviarci un dump di un <em>certo</em> database ACS4, sapete dove trovarci...), oltre a costruire il volano per fare il backup di tutti i libri del mondo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Quindi restate sintonizzati, abbiamo appena iniziato."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nuovi libri aggiunti al Pirate Library Mirror (+24TB, 3,8 milioni di libri)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Nel rilascio originale del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>), abbiamo fatto un mirror di Z-Library, una grande collezione di libri illegale. Come promemoria, ecco cosa abbiamo scritto in quel post originale del blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono la loro collezione facilmente replicabile, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Non facciamo giudizi morali sul far pagare denaro per l'accesso in massa a una collezione di libri illegali. È indubbio che la Z-Library abbia avuto successo nell'espandere l'accesso alla conoscenza e nel procurare più libri. Siamo semplicemente qui per fare la nostra parte: garantire la conservazione a lungo termine di questa collezione privata."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Quella collezione risale alla metà del 2021. Nel frattempo, la Z-Library è cresciuta a un ritmo sorprendente: hanno aggiunto circa 3,8 milioni di nuovi libri. Ci sono alcuni duplicati, certo, ma la maggior parte sembra essere libri veramente nuovi o scansioni di qualità superiore di libri precedentemente inviati. Questo è in gran parte dovuto all'aumento del numero di moderatori volontari alla Z-Library e al loro sistema di caricamento in massa con deduplicazione. Vorremmo congratularci con loro per questi successi."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Siamo felici di annunciare che abbiamo ottenuto tutti i libri aggiunti alla Z-Library tra il nostro ultimo mirror e agosto 2022. Siamo anche tornati indietro e abbiamo recuperato alcuni libri che ci erano sfuggiti la prima volta. In totale, questa nuova collezione è di circa 24TB, che è molto più grande della precedente (7TB). Il nostro mirror è ora di 31TB in totale. Ancora una volta, abbiamo deduplicato rispetto a Library Genesis, poiché ci sono già torrent disponibili per quella collezione."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Per favore, vai al Pirate Library Mirror per dare un'occhiata alla nuova collezione (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>). Lì ci sono ulteriori informazioni su come sono strutturati i file e cosa è cambiato dall'ultima volta. Non collegheremo da qui, poiché questo è solo un sito web di blog che non ospita materiali illegali."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Naturalmente, fare seeding è anche un ottimo modo per aiutarci. Grazie a tutti coloro che stanno facendo seeding del nostro set precedente di torrent. Siamo grati per la risposta positiva e felici che ci siano così tante persone che si prendono cura della conservazione della conoscenza e della cultura in questo modo insolito."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Come diventare un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "La prima sfida potrebbe essere sorprendente. Non è un problema tecnico, né un problema legale. È un problema psicologico."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Prima di immergerci, due aggiornamenti sul Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Abbiamo ricevuto alcune donazioni estremamente generose. La prima è stata di $10k da un individuo anonimo che ha anche supportato \"bookwarrior\", il fondatore originale di Library Genesis. Un ringraziamento speciale a bookwarrior per aver facilitato questa donazione. La seconda è stata un'altra donazione di $10k da un donatore anonimo, che ci ha contattato dopo la nostra ultima pubblicazione ed è stato ispirato ad aiutare. Abbiamo anche ricevuto un numero di donazioni più piccole. Grazie mille per tutto il vostro generoso supporto. Abbiamo alcuni nuovi progetti entusiasmanti in cantiere che questo sosterrà, quindi rimanete sintonizzati."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Abbiamo avuto alcune difficoltà tecniche con la dimensione della nostra seconda pubblicazione, ma i nostri torrent sono ora attivi e in seeding. Abbiamo anche ricevuto un'offerta generosa da un individuo anonimo per fare seeding della nostra collezione sui loro server ad altissima velocità, quindi stiamo facendo un caricamento speciale sui loro macchinari, dopodiché tutti gli altri che stanno scaricando la collezione dovrebbero vedere un grande miglioramento nella velocità."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Interi libri possono essere scritti sul perché della conservazione digitale in generale, e dell'archivismo pirata in particolare, ma lasciateci dare un breve riassunto per coloro che non sono troppo familiari. Il mondo sta producendo più conoscenza e cultura che mai, ma anche più di essa viene persa che mai. L'umanità affida in gran parte questo patrimonio a società come editori accademici, servizi di streaming e aziende di social media, e spesso non si sono dimostrate grandi custodi. Date un'occhiata al documentario Digital Amnesia, o a qualsiasi discorso di Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Ci sono alcune istituzioni che fanno un buon lavoro nell'archiviare il più possibile, ma sono vincolate dalla legge. Come pirati, siamo in una posizione unica per archiviare collezioni che non possono toccare, a causa dell'applicazione del copyright o di altre restrizioni. Possiamo anche fare il mirror delle collezioni molte volte, in tutto il mondo, aumentando così le possibilità di una corretta conservazione."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Per ora, non entreremo in discussioni sui pro e contro della proprietà intellettuale, la moralità di infrangere la legge, riflessioni sulla censura o la questione dell'accesso alla conoscenza e alla cultura. Con tutto ciò fuori dai piedi, immergiamoci nel come. Condivideremo come il nostro team è diventato archivisti pirati e le lezioni che abbiamo imparato lungo il percorso. Ci sono molte sfide quando si intraprende questo viaggio, e speriamo di potervi aiutare a superarne alcune."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Comunità"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "La prima sfida potrebbe essere sorprendente. Non è un problema tecnico, né un problema legale. È un problema psicologico: fare questo lavoro nell'ombra può essere incredibilmente solitario. A seconda di ciò che stai pianificando di fare e del tuo modello di minaccia, potresti dover essere molto attento. All'estremità dello spettro abbiamo persone come Alexandra Elbakyan*, la fondatrice di Sci-Hub, che è molto aperta sulle sue attività. Ma è ad alto rischio di essere arrestata se dovesse visitare un paese occidentale in questo momento, e potrebbe affrontare decenni di carcere. È un rischio che saresti disposto a correre? Noi siamo all'altra estremità dello spettro; stiamo molto attenti a non lasciare alcuna traccia e ad avere una forte sicurezza operativa."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Come menzionato su HN da \"ynno\", inizialmente Alexandra non voleva essere conosciuta: \"I suoi server erano configurati per emettere messaggi di errore dettagliati da PHP, incluso il percorso completo del file sorgente difettoso, che era sotto la directory /home/<USER>" Quindi, usa nomi utente casuali sui computer che usi per queste cose, nel caso in cui configuri qualcosa in modo errato."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Questa segretezza, tuttavia, comporta un costo psicologico. La maggior parte delle persone ama essere riconosciuta per il lavoro che svolge, eppure non puoi prenderti alcun merito per questo nella vita reale. Anche le cose semplici possono essere difficili, come gli amici che ti chiedono cosa hai fatto di recente (a un certo punto \"smanettare con il mio NAS / homelab\" diventa vecchio)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Ecco perché è così importante trovare una comunità. Puoi rinunciare a un po' di sicurezza operativa confidandoti con alcuni amici molto stretti, di cui sai di poterti fidare profondamente. Anche allora, fai attenzione a non mettere nulla per iscritto, nel caso in cui debbano consegnare le loro email alle autorità, o se i loro dispositivi sono compromessi in qualche altro modo."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Meglio ancora è trovare alcuni compagni pirati. Se i tuoi amici stretti sono interessati a unirsi a te, fantastico! Altrimenti, potresti essere in grado di trovare altri online. Purtroppo questa è ancora una comunità di nicchia. Finora abbiamo trovato solo una manciata di altri che sono attivi in questo spazio. Buoni punti di partenza sembrano essere i forum di Library Genesis e r/DataHoarder. Anche l'Archive Team ha individui affini, sebbene operino entro i limiti della legge (anche se in alcune aree grigie della legge). Le tradizionali scene \"warez\" e di pirateria hanno anche persone che pensano in modi simili."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Siamo aperti a idee su come promuovere la comunità ed esplorare nuove idee. Sentitevi liberi di contattarci su Twitter o Reddit. Forse potremmo ospitare una sorta di forum o gruppo di chat. Una sfida è che questo può facilmente essere censurato quando si utilizzano piattaforme comuni, quindi dovremmo ospitarlo noi stessi. C'è anche un compromesso tra avere queste discussioni completamente pubbliche (più potenziale di coinvolgimento) e renderle private (non far sapere ai potenziali \"bersagli\" che stiamo per raccoglierli). Dovremo rifletterci su. Fateci sapere se siete interessati a questo!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Progetti"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quando realizziamo un progetto, ci sono un paio di fasi:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selezione del dominio / filosofia: Dove vuoi concentrarti approssimativamente e perché? Quali sono le tue passioni, abilità e circostanze uniche che puoi utilizzare a tuo vantaggio?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selezione del target: Quale collezione specifica mirerai?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raccolta dei metadata: Catalogare le informazioni sui file, senza effettivamente scaricare i file stessi (spesso molto più grandi)."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selezione dei dati: Basandosi sui metadata, restringere quali dati sono più rilevanti da archiviare in questo momento. Potrebbe essere tutto, ma spesso c'è un modo ragionevole per risparmiare spazio e larghezza di banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raccolta dei dati: Ottenere effettivamente i dati."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribuzione: Impacchettare il tutto in torrent, annunciarlo da qualche parte, farlo diffondere dalle persone."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Queste non sono fasi completamente indipendenti, e spesso le intuizioni di una fase successiva ti riportano a una fase precedente. Ad esempio, durante la raccolta dei metadata potresti renderti conto che il target che hai selezionato ha meccanismi difensivi oltre il tuo livello di abilità (come i blocchi IP), quindi torni indietro e trovi un target diverso."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selezione del dominio / filosofia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Non manca la conoscenza e il patrimonio culturale da salvare, il che può essere travolgente. Ecco perché è spesso utile prendersi un momento e pensare a quale può essere il tuo contributo."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Ognuno ha un modo diverso di pensare a questo, ma ecco alcune domande che potresti porti:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Perché sei interessato a questo? Di cosa sei appassionato? Se riusciamo a ottenere un gruppo di persone che archiviano tutti i tipi di cose a cui tengono specificamente, copriremmo molto! Saprai molto di più della persona media sulla tua passione, come quali sono i dati importanti da salvare, quali sono le migliori collezioni e comunità online, e così via."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Quali abilità possiedi che puoi utilizzare a tuo vantaggio? Ad esempio, se sei un esperto di sicurezza online, puoi trovare modi per superare i blocchi IP per target sicuri. Se sei bravo a organizzare comunità, allora forse puoi radunare alcune persone attorno a un obiettivo. È utile conoscere un po' di programmazione, anche solo per mantenere una buona sicurezza operativa durante questo processo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quanto tempo hai a disposizione per questo? Il nostro consiglio sarebbe di iniziare in piccolo e fare progetti più grandi man mano che ci prendi la mano, ma può diventare totalizzante."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Quale sarebbe un'area ad alto rendimento su cui concentrarsi? Se hai intenzione di spendere X ore nell'archiviazione pirata, come puoi ottenere il massimo \"ritorno sull'investimento\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Quali sono i modi unici in cui stai pensando a questo? Potresti avere alcune idee o approcci interessanti che altri potrebbero aver trascurato."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Nel nostro caso, ci interessava in particolare la conservazione a lungo termine della scienza. Conoscevamo Library Genesis e come fosse completamente mirroring molte volte tramite torrent. Ci piaceva quell'idea. Poi un giorno, uno di noi ha provato a trovare alcuni libri di testo scientifici su Library Genesis, ma non è riuscito a trovarli, mettendo in dubbio quanto fosse davvero completo. Abbiamo quindi cercato quei libri di testo online e li abbiamo trovati in altri luoghi, il che ha piantato il seme per il nostro progetto. Anche prima di conoscere Z-Library, avevamo l'idea di non cercare di raccogliere tutti quei libri manualmente, ma di concentrarci sul mirroring delle collezioni esistenti e di contribuire a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selezione del target"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Quindi, abbiamo la nostra area di interesse, ora quale collezione specifica vogliamo mirrorare? Ci sono un paio di cose che rendono un obiettivo valido:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unica: non già ben coperta da altri progetti."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessibile: non utilizza tonnellate di livelli di protezione per impedirti di estrarre i loro metadata e dati."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Intuizione speciale: hai qualche informazione speciale su questo obiettivo, come un accesso speciale a questa collezione, o hai capito come superare le loro difese. Questo non è richiesto (il nostro progetto in arrivo non fa nulla di speciale), ma certamente aiuta!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quando abbiamo trovato i nostri libri di testo scientifici su siti web diversi da Library Genesis, abbiamo cercato di capire come fossero arrivati su internet. Abbiamo poi trovato Z-Library e ci siamo resi conto che, sebbene la maggior parte dei libri non appaia lì per la prima volta, alla fine ci finiscono. Abbiamo appreso della sua relazione con Library Genesis e della struttura di incentivi (finanziari) e dell'interfaccia utente superiore, entrambe le quali lo rendevano una collezione molto più completa. Abbiamo quindi effettuato alcune estrazioni preliminari di metadata e dati, e ci siamo resi conto che potevamo aggirare i loro limiti di download IP, sfruttando l'accesso speciale di uno dei nostri membri a molti server proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Mentre esplori diversi obiettivi, è già importante nascondere le tue tracce utilizzando VPN e indirizzi email usa e getta, di cui parleremo più avanti."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Estrazione di metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Diventiamo un po' più tecnici qui. Per estrarre effettivamente i metadata dai siti web, abbiamo mantenuto le cose piuttosto semplici. Utilizziamo script Python, a volte curl, e un database MySQL per memorizzare i risultati. Non abbiamo utilizzato alcun software di estrazione sofisticato che possa mappare siti web complessi, poiché finora abbiamo solo avuto bisogno di estrarre uno o due tipi di pagine semplicemente enumerando gli id e analizzando l'HTML. Se non ci sono pagine facilmente enumerabili, allora potresti aver bisogno di un vero e proprio crawler che cerchi di trovare tutte le pagine."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Prima di iniziare a estrarre un intero sito web, prova a farlo manualmente per un po'. Passa attraverso alcune decine di pagine da solo, per avere un'idea di come funziona. A volte incontrerai già blocchi IP o altri comportamenti interessanti in questo modo. Lo stesso vale per l'estrazione dei dati: prima di approfondire troppo questo obiettivo, assicurati di poter effettivamente scaricare i suoi dati in modo efficace."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Per aggirare le restrizioni, ci sono alcune cose che puoi provare. Ci sono altri indirizzi IP o server che ospitano gli stessi dati ma non hanno le stesse restrizioni? Ci sono endpoint API che non hanno restrizioni, mentre altri sì? A quale velocità di download il tuo IP viene bloccato, e per quanto tempo? Oppure non sei bloccato ma rallentato? Cosa succede se crei un account utente, come cambiano le cose allora? Puoi usare HTTP/2 per mantenere aperte le connessioni, e questo aumenta la velocità con cui puoi richiedere le pagine? Ci sono pagine che elencano più file contemporaneamente, e le informazioni elencate lì sono sufficienti?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Le cose che probabilmente vuoi salvare includono:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titolo"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nome file / posizione"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: può essere un ID interno, ma anche ID come ISBN o DOI sono utili."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dimensione: per calcolare quanto spazio su disco ti serve."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): per confermare che hai scaricato correttamente il file."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data aggiunta/modificata: così puoi tornare più tardi e scaricare file che non hai scaricato prima (anche se spesso puoi usare anche l'ID o l'hash per questo)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Descrizione, categoria, tag, autori, lingua, ecc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Di solito facciamo questo in due fasi. Prima scarichiamo i file HTML grezzi, di solito direttamente in MySQL (per evitare molti piccoli file, di cui parliamo più avanti). Poi, in un passaggio separato, esaminiamo quei file HTML e li analizziamo in tabelle MySQL effettive. In questo modo non devi riscaricare tutto da zero se scopri un errore nel tuo codice di analisi, poiché puoi semplicemente riprocessare i file HTML con il nuovo codice. È anche spesso più facile parallelizzare il passaggio di elaborazione, risparmiando così del tempo (e puoi scrivere il codice di elaborazione mentre l'estrazione è in esecuzione, invece di dover scrivere entrambi i passaggi contemporaneamente)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Infine, nota che per alcuni obiettivi il scraping dei metadata è tutto ciò che c'è. Ci sono alcune enormi collezioni di metadata là fuori che non sono adeguatamente preservate."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selezione dei dati"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Spesso puoi utilizzare i metadata per determinare un sottoinsieme ragionevole di dati da scaricare. Anche se alla fine vuoi scaricare tutti i dati, può essere utile dare priorità agli elementi più importanti per primi, nel caso in cui tu venga rilevato e le difese vengano migliorate, o perché avresti bisogno di acquistare più dischi, o semplicemente perché qualcosa d'altro si presenta nella tua vita prima che tu possa scaricare tutto."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Ad esempio, una collezione potrebbe avere più edizioni della stessa risorsa di base (come un libro o un film), dove una è contrassegnata come di migliore qualità. Salvare prima quelle edizioni avrebbe molto senso. Potresti alla fine voler salvare tutte le edizioni, poiché in alcuni casi i metadata potrebbero essere etichettati in modo errato, o potrebbero esserci compromessi sconosciuti tra le edizioni (ad esempio, la \"migliore edizione\" potrebbe essere la migliore in molti modi ma peggiore in altri, come un film con una risoluzione più alta ma senza sottotitoli)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Puoi anche cercare nel tuo database di metadata per trovare cose interessanti. Qual è il file più grande che è ospitato, e perché è così grande? Qual è il file più piccolo? Ci sono schemi interessanti o inaspettati quando si tratta di certe categorie, lingue, e così via? Ci sono titoli duplicati o molto simili? Ci sono schemi su quando i dati sono stati aggiunti, come un giorno in cui molti file sono stati aggiunti contemporaneamente? Spesso puoi imparare molto osservando il dataset in modi diversi."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Nel nostro caso, abbiamo deduplicato i libri di Z-Library rispetto agli hash md5 in Library Genesis, risparmiando così molto tempo di download e spazio su disco. Questa è una situazione piuttosto unica però. Nella maggior parte dei casi non ci sono database completi di quali file sono già adeguatamente preservati da altri pirati. Questo di per sé è una grande opportunità per qualcuno là fuori. Sarebbe fantastico avere una panoramica regolarmente aggiornata di cose come musica e film che sono già ampiamente condivisi su siti torrent, e sono quindi a bassa priorità da includere nei mirror pirata."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Scraping dei dati"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Ora sei pronto per scaricare effettivamente i dati in massa. Come menzionato prima, a questo punto dovresti già aver scaricato manualmente un bel po' di file, per comprendere meglio il comportamento e le restrizioni dell'obiettivo. Tuttavia, ci saranno ancora sorprese in serbo per te una volta che effettivamente inizi a scaricare molti file contemporaneamente."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Il nostro consiglio qui è principalmente di mantenerlo semplice. Inizia semplicemente scaricando un bel po' di file. Puoi usare Python, e poi espandere a più thread. Ma a volte è ancora più semplice generare file Bash direttamente dal database, e poi eseguirne più in più finestre di terminale per aumentare la scala. Un rapido trucco tecnico che vale la pena menzionare qui è l'uso di OUTFILE in MySQL, che puoi scrivere ovunque se disabiliti \"secure_file_priv\" in mysqld.cnf (e assicurati di disabilitare/sovrascrivere anche AppArmor se sei su Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Conserviamo i dati su semplici dischi rigidi. Inizia con quello che hai, ed espandi lentamente. Può essere opprimente pensare di dover conservare centinaia di TB di dati. Se questa è la situazione che stai affrontando, metti fuori un buon sottoinsieme per primo, e nel tuo annuncio chiedi aiuto per conservare il resto. Se vuoi ottenere più dischi rigidi da solo, allora r/DataHoarder ha alcune buone risorse per ottenere buoni affari."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Cerca di non preoccuparti troppo di filesystem sofisticati. È facile cadere nella trappola di configurare cose come ZFS. Un dettaglio tecnico di cui essere consapevoli, però, è che molti filesystem non gestiscono bene molti file. Abbiamo scoperto che una semplice soluzione è creare più directory, ad esempio per diversi intervalli di ID o prefissi di hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Dopo aver scaricato i dati, assicurati di controllare l'integrità dei file utilizzando gli hash nei metadata, se disponibili."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribuzione"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Hai i dati, dandoti così il possesso del primo mirror pirata al mondo del tuo obiettivo (molto probabilmente). In molti modi la parte più difficile è finita, ma la parte più rischiosa è ancora davanti a te. Dopotutto, finora sei stato furtivo; volando sotto il radar. Tutto quello che dovevi fare era usare una buona VPN per tutto il tempo, non inserire i tuoi dati personali in nessun modulo (ovviamente), e forse usare una sessione del browser speciale (o anche un computer diverso)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Ora devi distribuire i dati. Nel nostro caso volevamo prima contribuire i libri a Library Genesis, ma poi abbiamo rapidamente scoperto le difficoltà in questo (ordinamento fiction vs non-fiction). Quindi abbiamo deciso di distribuire utilizzando torrent in stile Library Genesis. Se hai l'opportunità di contribuire a un progetto esistente, allora questo potrebbe farti risparmiare molto tempo. Tuttavia, attualmente non ci sono molti mirror pirata ben organizzati là fuori."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Quindi diciamo che decidi di distribuire i torrent da solo. Cerca di mantenere quei file piccoli, in modo che siano facili da mirroring su altri siti web. Dovrai quindi seminare i torrent da solo, rimanendo comunque anonimo. Puoi usare una VPN (con o senza port forwarding), o pagare con Bitcoin mescolati per un Seedbox. Se non sai cosa significano alcuni di questi termini, avrai un bel po' di letture da fare, poiché è importante che tu comprenda i compromessi di rischio qui."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Puoi ospitare i file torrent stessi su siti torrent esistenti. Nel nostro caso, abbiamo scelto di ospitare effettivamente un sito web, poiché volevamo anche diffondere la nostra filosofia in modo chiaro. Puoi farlo da solo in modo simile (usiamo Njalla per i nostri domini e hosting, pagati con Bitcoin mescolati), ma sentiti libero di contattarci per farci ospitare i tuoi torrent. Stiamo cercando di costruire un indice completo di mirror pirata nel tempo, se questa idea prende piede."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Per quanto riguarda la selezione della VPN, molto è stato scritto su questo già, quindi ripeteremo solo il consiglio generale di scegliere in base alla reputazione. Politiche di no-log testate in tribunale con lunghe storie di protezione della privacy sono l'opzione a minor rischio, a nostro avviso. Nota che anche quando fai tutto bene, non puoi mai arrivare a rischio zero. Ad esempio, quando semini i tuoi torrent, un attore statale altamente motivato può probabilmente osservare i flussi di dati in entrata e in uscita per i server VPN, e dedurre chi sei. Oppure puoi semplicemente sbagliare in qualche modo. Probabilmente lo abbiamo già fatto, e lo faremo di nuovo. Fortunatamente, gli stati nazionali non si preoccupano <em>così</em> tanto della pirateria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Una decisione da prendere per ogni progetto è se pubblicarlo utilizzando la stessa identità di prima, o no. Se continui a usare lo stesso nome, allora errori nella sicurezza operativa di progetti precedenti potrebbero tornare a morderti. Ma pubblicare sotto nomi diversi significa che non costruisci una reputazione duratura. Abbiamo scelto di avere una forte sicurezza operativa fin dall'inizio in modo da poter continuare a usare la stessa identità, ma non esiteremo a pubblicare sotto un nome diverso se sbagliamo o se le circostanze lo richiedono."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Far conoscere il progetto può essere complicato. Come abbiamo detto, questa è ancora una comunità di nicchia. Inizialmente abbiamo postato su Reddit, ma abbiamo davvero ottenuto trazione su Hacker News. Per ora la nostra raccomandazione è di postarlo in alcuni posti e vedere cosa succede. E ancora, contattaci. Ci piacerebbe diffondere la parola di più sforzi di archivismo pirata."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusione"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Speriamo che questo sia utile per i nuovi archivisti pirata. Siamo entusiasti di darvi il benvenuto in questo mondo, quindi non esitate a contattarci. Conserviamo quanta più conoscenza e cultura del mondo possibile e facciamone il mirror ovunque."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentazione del Pirate Library Mirror: Conservare 7TB di libri (che non sono in Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Questo progetto (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>) mira a contribuire alla conservazione e liberazione della conoscenza umana. Facciamo il nostro piccolo e umile contributo, seguendo le orme dei grandi che ci hanno preceduto."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "L'obiettivo di questo progetto è illustrato dal suo nome:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Violiamo deliberatamente la legge sul copyright nella maggior parte dei paesi. Questo ci permette di fare qualcosa che le entità legali non possono fare: assicurarci che i libri siano replicati ovunque."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteca</strong> - Come la maggior parte delle biblioteche, ci concentriamo principalmente su materiali scritti come i libri. Potremmo espanderci in altri tipi di media in futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Mirror</strong> - Siamo strettamente un mirror di biblioteche esistenti. Ci concentriamo sulla conservazione, non sulla facilità di ricerca e download dei libri (accesso) o sulla creazione di una grande comunità di persone che contribuiscono con nuovi libri (approvvigionamento)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "La prima biblioteca che abbiamo replicato è Z-Library. Questa è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non restituiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono facilmente replicabile la loro collezione, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Non facciamo giudizi morali sul far pagare denaro per l'accesso in massa a una collezione di libri illegali. È indubbio che la Z-Library abbia avuto successo nell'espandere l'accesso alla conoscenza e nel procurare più libri. Siamo semplicemente qui per fare la nostra parte: garantire la conservazione a lungo termine di questa collezione privata."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Vorremmo invitarvi ad aiutare a preservare e liberare la conoscenza umana scaricando e seminando i nostri torrent. Consultate la pagina del progetto per ulteriori informazioni su come sono organizzati i dati."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Invitiamo anche molto volentieri a contribuire con le vostre idee su quali collezioni replicare successivamente e su come procedere. Insieme possiamo ottenere molto. Questo è solo un piccolo contributo tra innumerevoli altri. Grazie, per tutto ciò che fate."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Non colleghiamo i file da questo blog. Trovateli da soli.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump di ISBNdb, o Quanti Libri Sono Conservati per Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Se dovessimo deduplicare correttamente i file delle biblioteche ombra, quale percentuale di tutti i libri del mondo abbiamo conservato?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Con il Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>), il nostro obiettivo è prendere tutti i libri del mondo e conservarli per sempre.<sup>1</sup> Tra i nostri torrent di Z-Library e i torrent originali di Library Genesis, abbiamo 11.783.153 file. Ma quanti sono davvero? Se deduplicassimo correttamente quei file, quale percentuale di tutti i libri del mondo abbiamo conservato? Ci piacerebbe davvero avere qualcosa del genere:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of del patrimonio scritto dell'umanità conservato per sempre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Per una percentuale, abbiamo bisogno di un denominatore: il numero totale di libri mai pubblicati.<sup>2</sup> Prima della fine di Google Books, un ingegnere del progetto, Leonid Taycher, <a %(booksearch_blogspot)s>ha cercato di stimare</a> questo numero. Ha proposto — con ironia — 129.864.880 (“almeno fino a domenica”). Ha stimato questo numero costruendo un database unificato di tutti i libri del mondo. Per questo, ha riunito diversi Datasets e poi li ha fusi in vari modi."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Come breve parentesi, c'è un'altra persona che ha tentato di catalogare tutti i libri del mondo: Aaron Swartz, il defunto attivista digitale e co-fondatore di Reddit.<sup>3</sup> Ha <a %(youtube)s>iniziato Open Library</a> con l'obiettivo di “una pagina web per ogni libro mai pubblicato”, combinando dati da molte fonti diverse. Finì per pagare il prezzo più alto per il suo lavoro di preservazione digitale quando fu perseguito per il download massivo di articoli accademici, portandolo al suicidio. Inutile dire che questa è una delle ragioni per cui il nostro gruppo è pseudonimo e perché stiamo facendo molta attenzione. Open Library è ancora gestita eroicamente da persone dell'Internet Archive, continuando l'eredità di Aaron. Torneremo su questo più avanti in questo post."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Nel post sul blog di Google, Taycher descrive alcune delle sfide nel stimare questo numero. Innanzitutto, cosa costituisce un libro? Ci sono alcune possibili definizioni:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copie fisiche.</strong> Ovviamente questo non è molto utile, poiché sono solo duplicati dello stesso materiale. Sarebbe fantastico se potessimo preservare tutte le annotazioni che le persone fanno nei libri, come i famosi “scarabocchi nei margini” di Fermat. Ma ahimè, questo rimarrà un sogno per gli archivisti."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Opere”.</strong> Ad esempio “Harry Potter e la Camera dei Segreti” come concetto logico, che comprende tutte le versioni, come diverse traduzioni e ristampe. Questa è una definizione piuttosto utile, ma può essere difficile tracciare il confine di ciò che conta. Ad esempio, probabilmente vogliamo preservare diverse traduzioni, anche se le ristampe con solo lievi differenze potrebbero non essere così importanti."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edizioni”.</strong> Qui si conta ogni versione unica di un libro. Se qualcosa è diverso, come una copertina diversa o una prefazione diversa, conta come un'edizione diversa."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>File.</strong> Quando si lavora con biblioteche ombra come Library Genesis, Sci-Hub o Z-Library, c'è un'ulteriore considerazione. Ci possono essere più scansioni della stessa edizione. E le persone possono creare versioni migliori dei file esistenti, scansionando il testo usando l'OCR o rettificando le pagine che sono state scansionate ad un angolo. Vogliamo contare questi file come una sola edizione, il che richiederebbe buoni metadata o deduplicazione usando misure di somiglianza dei documenti."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Le “Edizioni” sembrano la definizione più pratica di cosa siano i “libri”. Comodamente, questa definizione è anche usata per assegnare numeri ISBN unici. Un ISBN, o International Standard Book Number, è comunemente usato per il commercio internazionale, poiché è integrato con il sistema internazionale di codici a barre (“International Article Number”). Se vuoi vendere un libro nei negozi, ha bisogno di un codice a barre, quindi ottieni un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Il post sul blog di Taycher menziona che mentre gli ISBN sono utili, non sono universali, poiché sono stati adottati solo a metà degli anni settanta, e non ovunque nel mondo. Tuttavia, l'ISBN è probabilmente l'identificatore più ampiamente usato delle edizioni dei libri, quindi è il nostro miglior punto di partenza. Se possiamo trovare tutti gli ISBN nel mondo, otteniamo una lista utile di quali libri devono ancora essere preservati."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Quindi, dove otteniamo i dati? Ci sono diversi sforzi esistenti che stanno cercando di compilare una lista di tutti i libri del mondo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Dopotutto, hanno fatto questa ricerca per Google Books. Tuttavia, i loro metadata non sono accessibili in massa e piuttosto difficili da estrarre."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Come menzionato prima, questa è la loro intera missione. Hanno raccolto enormi quantità di dati bibliotecari da biblioteche cooperative e archivi nazionali, e continuano a farlo. Hanno anche bibliotecari volontari e un team tecnico che stanno cercando di deduplicare i record e taggarli con tutti i tipi di metadata. La cosa migliore è che il loro dataset è completamente aperto. Puoi semplicemente <a %(openlibrary)s>scaricarlo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Questo è un sito web gestito dalla non-profit OCLC, che vende sistemi di gestione bibliotecaria. Aggregano metadata di libri da molte biblioteche e li rendono disponibili attraverso il sito web WorldCat. Tuttavia, fanno anche soldi vendendo questi dati, quindi non sono disponibili per il download in massa. Hanno alcuni dataset più limitati disponibili per il download, in collaborazione con biblioteche specifiche."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Questo è l'argomento di questo post sul blog. ISBNdb estrae vari siti web per i metadata dei libri, in particolare i dati sui prezzi, che poi vendono ai librai, in modo che possano prezzare i loro libri in accordo con il resto del mercato. Poiché gli ISBN sono abbastanza universali al giorno d'oggi, hanno effettivamente costruito una “pagina web per ogni libro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Vari sistemi bibliotecari e archivi individuali.</strong> Ci sono biblioteche e archivi che non sono stati indicizzati e aggregati da nessuno di quelli sopra, spesso perché sono sottofinanziati, o per altre ragioni non vogliono condividere i loro dati con Open Library, OCLC, Google, e così via. Molti di questi hanno registri digitali accessibili tramite internet, e spesso non sono molto ben protetti, quindi se vuoi aiutare e divertirti imparando sui sistemi bibliotecari strani, questi sono ottimi punti di partenza."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In questo post, siamo felici di annunciare un piccolo rilascio (rispetto ai nostri precedenti rilasci di Z-Library). Abbiamo estratto la maggior parte di ISBNdb e reso i dati disponibili per il torrenting sul sito web del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>; non lo collegheremo qui direttamente, basta cercarlo). Si tratta di circa 30,9 milioni di record (20GB come <a %(jsonlines)s>JSON Lines</a>; 4,4GB compressi). Sul loro sito web affermano di avere effettivamente 32,6 milioni di record, quindi potremmo averne persi alcuni, o <em>loro</em> potrebbero aver commesso qualche errore. In ogni caso, per ora non condivideremo esattamente come abbiamo fatto — lasceremo questo come esercizio per il lettore. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Quello che condivideremo è un'analisi preliminare, per cercare di avvicinarci a stimare il numero di libri nel mondo. Abbiamo esaminato tre dataset: questo nuovo dataset di ISBNdb, il nostro rilascio originale di metadata che abbiamo estratto dalla biblioteca ombra Z-Library (che include Library Genesis), e il dump di dati di Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Iniziamo con alcuni numeri approssimativi:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Sia in Z-Library/Libgen che in Open Library ci sono molti più libri che ISBN unici. Significa che molti di quei libri non hanno ISBN, o i metadata degli ISBN sono semplicemente mancanti? Probabilmente possiamo rispondere a questa domanda con una combinazione di abbinamento automatico basato su altri attributi (titolo, autore, editore, ecc.), integrando più fonti di dati ed estraendo gli ISBN dalle scansioni effettive dei libri stessi (nel caso di Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Quanti di quegli ISBN sono unici? Questo è meglio illustrato con un diagramma di Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Per essere più precisi:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Siamo rimasti sorpresi da quanto poco ci sia sovrapposizione! ISBNdb ha un'enorme quantità di ISBN che non compaiono né in Z-Library né in Open Library, e lo stesso vale (in misura minore ma comunque sostanziale) per le altre due. Questo solleva molte nuove domande. Quanto aiuterebbe l'abbinamento automatico nel taggare i libri che non sono stati taggati con ISBN? Ci sarebbero molte corrispondenze e quindi un aumento della sovrapposizione? Inoltre, cosa accadrebbe se introducessimo un quarto o quinto dataset? Quanta sovrapposizione vedremmo allora?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Questo ci dà un punto di partenza. Ora possiamo esaminare tutti gli ISBN che non erano nel dataset di Z-Library e che non corrispondono nemmeno ai campi titolo/autore. Questo può darci un'idea su come preservare tutti i libri del mondo: prima raschiando internet per scansioni, poi uscendo nella vita reale per scansionare i libri. Quest'ultimo potrebbe persino essere finanziato dal pubblico, o guidato da \"ricompense\" da parte di persone che vorrebbero vedere determinati libri digitalizzati. Tutto ciò è una storia per un altro momento."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Se vuoi aiutare in qualsiasi modo — ulteriori analisi; raschiare più metadata; trovare più libri; fare OCR dei libri; fare questo per altri domini (ad esempio articoli, audiolibri, film, serie TV, riviste) o persino rendere disponibili alcuni di questi dati per cose come l'addestramento di modelli di linguaggio di grandi dimensioni — per favore contattami (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Se sei specificamente interessato all'analisi dei dati, stiamo lavorando per rendere i nostri dataset e script disponibili in un formato più facile da usare. Sarebbe fantastico se potessi semplicemente fare un fork di un notebook e iniziare a sperimentare."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Infine, se vuoi supportare questo lavoro, per favore considera di fare una donazione. Questa è un'operazione gestita interamente da volontari, e il tuo contributo fa una grande differenza. Ogni piccolo aiuto conta. Per ora accettiamo donazioni in criptovaluta; vedi la pagina delle Donazioni su Archivio di Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Per una definizione ragionevole di \"per sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Ovviamente, il patrimonio scritto dell'umanità è molto più dei libri, specialmente al giorno d'oggi. Per il bene di questo post e delle nostre recenti pubblicazioni ci stiamo concentrando sui libri, ma i nostri interessi si estendono oltre."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. C'è molto di più che si può dire su Aaron Swartz, ma volevamo solo menzionarlo brevemente, poiché gioca un ruolo fondamentale in questa storia. Col passare del tempo, più persone potrebbero imbattersi nel suo nome per la prima volta, e successivamente esplorare il suo mondo."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "La finestra critica delle biblioteche ombra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Come possiamo affermare di preservare le nostre collezioni in perpetuo, quando stanno già avvicinandosi a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versione cinese 中文版</a>, discuti su <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "All'Archivio di Anna, ci viene spesso chiesto come possiamo affermare di preservare le nostre collezioni in perpetuo, quando la dimensione totale si sta già avvicinando a 1 Petabyte (1000 TB), e continua a crescere. In questo articolo esamineremo la nostra filosofia, e vedremo perché il prossimo decennio è critico per la nostra missione di preservare la conoscenza e la cultura dell'umanità."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "La <a %(annas_archive_stats)s>dimensione totale</a> delle nostre collezioni, negli ultimi mesi, suddivisa per numero di seeders di torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Priorità"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Perché ci preoccupiamo così tanto di articoli e libri? Mettiamo da parte la nostra convinzione fondamentale nella preservazione in generale — potremmo scrivere un altro post su questo. Quindi perché articoli e libri specificamente? La risposta è semplice: <strong>densità di informazione</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte di archiviazione, il testo scritto conserva la maggior quantità di informazioni rispetto a tutti i media. Mentre ci preoccupiamo sia della conoscenza che della cultura, ci preoccupiamo di più della prima. In generale, troviamo una gerarchia di densità di informazione e importanza della preservazione che appare più o meno così:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Articoli accademici, riviste, rapporti"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dati organici come sequenze di DNA, semi di piante o campioni microbici"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libri di saggistica"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Codice software per scienza e ingegneria"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dati di misurazione come misurazioni scientifiche, dati economici, rapporti aziendali"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Siti web di scienza e ingegneria, discussioni online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Riviste non-fiction, giornali, manuali"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Trascrizioni non-fiction di discorsi, documentari, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dati interni di aziende o governi (fughe di notizie)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Record di metadata in generale (di non-fiction e fiction; di altri media, arte, persone, ecc.; incluse recensioni)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dati geografici (ad es. mappe, rilevamenti geologici)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Trascrizioni di procedimenti legali o giudiziari"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versioni fittizie o di intrattenimento di tutto quanto sopra"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "La classifica in questo elenco è in qualche modo arbitraria — diversi elementi sono pari o ci sono disaccordi all'interno del nostro team — e probabilmente stiamo dimenticando alcune categorie importanti. Ma questo è approssimativamente come diamo priorità."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Alcuni di questi elementi sono troppo diversi dagli altri per preoccuparci (o sono già gestiti da altre istituzioni), come i dati organici o i dati geografici. Ma la maggior parte degli elementi in questo elenco è effettivamente importante per noi."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un altro grande fattore nella nostra priorità è quanto è a rischio un determinato lavoro. Preferiamo concentrarci su opere che sono:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rare"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamente trascurate"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamente a rischio di distruzione (ad es. per guerra, tagli ai finanziamenti, cause legali o persecuzione politica)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Infine, ci interessa la scala. Abbiamo tempo e denaro limitati, quindi preferiamo passare un mese a salvare 10.000 libri piuttosto che 1.000 libri — se sono ugualmente preziosi e a rischio."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Biblioteche ombra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Ci sono molte organizzazioni che hanno missioni simili e priorità simili. In effetti, ci sono biblioteche, archivi, laboratori, musei e altre istituzioni incaricate della conservazione di questo tipo. Molte di queste sono ben finanziate, da governi, individui o aziende. Ma hanno un enorme punto cieco: il sistema legale."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Qui risiede il ruolo unico delle biblioteche ombra e il motivo per cui esiste l'Archivio di Anna. Possiamo fare cose che altre istituzioni non sono autorizzate a fare. Ora, non è (spesso) che possiamo archiviare materiali che sono illegali da conservare altrove. No, è legale in molti luoghi costruire un archivio con qualsiasi libro, documento, rivista e così via."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ma ciò che spesso manca agli archivi legali è <strong>ridondanza e longevità</strong>. Esistono libri di cui esiste solo una copia in qualche biblioteca fisica da qualche parte. Esistono record di metadata custoditi da una singola azienda. Esistono giornali conservati solo su microfilm in un unico archivio. Le biblioteche possono subire tagli ai finanziamenti, le aziende possono fallire, gli archivi possono essere bombardati e bruciati fino a terra. Questo non è ipotetico: accade continuamente."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "La cosa che possiamo fare in modo unico su Archivio di Anna è conservare molte copie delle opere, su larga scala. Possiamo raccogliere articoli, libri, riviste e altro, e distribuirli in massa. Attualmente lo facciamo tramite torrent, ma le tecnologie esatte non importano e cambieranno nel tempo. La parte importante è distribuire molte copie in tutto il mondo. Questa citazione di oltre 200 anni fa è ancora valida:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Ciò che è perduto non può essere recuperato; ma salviamo ciò che rimane: non con volte e serrature che li proteggono dagli occhi e dall'uso del pubblico, consegnandoli al logorio del tempo, ma con una tale moltiplicazione di copie, da metterle al di là della portata degli incidenti.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Una breve nota sul dominio pubblico. Poiché Archivio di Anna si concentra in modo unico su attività che sono illegali in molti luoghi del mondo, non ci preoccupiamo delle collezioni ampiamente disponibili, come i libri di dominio pubblico. Le entità legali spesso se ne prendono già cura adeguatamente. Tuttavia, ci sono considerazioni che a volte ci portano a lavorare su collezioni pubblicamente disponibili:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "I record di metadata possono essere visualizzati liberamente sul sito Worldcat, ma non scaricati in massa (fino a quando non li abbiamo <a %(worldcat_scrape)s>estratti</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Il codice può essere open source su Github, ma Github nel suo insieme non può essere facilmente mirrorato e quindi preservato (anche se in questo caso particolare ci sono copie sufficientemente distribuite della maggior parte dei repository di codice)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit è gratuito da usare, ma ha recentemente introdotto misure anti-scraping rigorose, a seguito dell'addestramento di LLM affamati di dati (ne parleremo più avanti)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Una moltiplicazione di copie"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tornando alla nostra domanda originale: come possiamo affermare di preservare le nostre collezioni in perpetuo? Il problema principale qui è che la nostra collezione è <a %(torrents_stats)s>cresciuta</a> rapidamente, estraendo e rendendo open source alcune collezioni massive (oltre al lavoro straordinario già svolto da altre biblioteche ombra di dati aperti come Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Questa crescita dei dati rende più difficile il mirroring delle collezioni in tutto il mondo. L'archiviazione dei dati è costosa! Ma siamo ottimisti, soprattutto osservando le seguenti tre tendenze."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Abbiamo raccolto i frutti più facili"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Questo segue direttamente dalle nostre priorità discusse sopra. Preferiamo lavorare prima sulla liberazione di grandi collezioni. Ora che abbiamo assicurato alcune delle collezioni più grandi del mondo, ci aspettiamo che la nostra crescita sia molto più lenta."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "C'è ancora una lunga coda di collezioni più piccole, e nuovi libri vengono scansionati o pubblicati ogni giorno, ma il tasso sarà probabilmente molto più lento. Potremmo ancora raddoppiare o addirittura triplicare di dimensioni, ma in un periodo di tempo più lungo."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. I costi di archiviazione continuano a diminuire esponenzialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Al momento della scrittura, i <a %(diskprices)s>prezzi dei dischi</a> per TB sono circa $12 per dischi nuovi, $8 per dischi usati e $4 per nastro. Se siamo conservatori e guardiamo solo ai dischi nuovi, significa che archiviare un petabyte costa circa $12.000. Se assumiamo che la nostra biblioteca triplicherà da 900TB a 2,7PB, ciò significherebbe $32.400 per mirrorare l'intera biblioteca. Aggiungendo elettricità, costo di altro hardware, e così via, arrotondiamo a $40.000. O con nastro più come $15.000–$20.000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Da un lato <strong>$15.000–$40.000 per la somma di tutta la conoscenza umana è un affare</strong>. Dall'altro, è un po' ripido aspettarsi tonnellate di copie complete, specialmente se vorremmo anche che quelle persone continuassero a seminare i loro torrent a beneficio degli altri."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Questo è oggi. Ma il progresso avanza:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "I costi degli hard disk per TB sono stati ridotti di circa un terzo negli ultimi 10 anni e probabilmente continueranno a diminuire a un ritmo simile. Il nastro sembra seguire una traiettoria simile. I prezzi degli SSD stanno scendendo ancora più velocemente e potrebbero superare i prezzi degli HDD entro la fine del decennio."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendenze dei prezzi degli HDD da fonti diverse (clicca per visualizzare lo studio)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Se questo si mantiene, allora tra 10 anni potremmo guardare a soli $5.000–$13.000 per mirrorare l'intera collezione (1/3), o anche meno se cresciamo meno in dimensioni. Anche se ancora una grande somma di denaro, sarà accessibile per molte persone. E potrebbe essere ancora meglio grazie al prossimo punto…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Miglioramenti nella densità delle informazioni"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Attualmente conserviamo i libri nei formati grezzi in cui ci vengono forniti. Certo, sono compressi, ma spesso sono ancora grandi scansioni o fotografie di pagine."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Fino ad ora, le uniche opzioni per ridurre la dimensione totale della nostra collezione sono state una compressione più aggressiva o la deduplicazione. Tuttavia, per ottenere risparmi significativi, entrambe sono troppo lossy per i nostri gusti. La compressione pesante delle foto può rendere il testo appena leggibile. E la deduplicazione richiede un'elevata sicurezza che i libri siano esattamente gli stessi, il che è spesso troppo impreciso, specialmente se i contenuti sono gli stessi ma le scansioni sono fatte in occasioni diverse."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "C'è sempre stata una terza opzione, ma la sua qualità è stata così pessima che non l'abbiamo mai considerata: <strong>OCR, o Riconoscimento Ottico dei Caratteri</strong>. Questo è il processo di conversione delle foto in testo semplice, utilizzando l'IA per rilevare i caratteri nelle foto. Gli strumenti per questo esistono da tempo e sono stati abbastanza decenti, ma \"abbastanza decenti\" non è sufficiente per scopi di conservazione."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tuttavia, i recenti modelli di deep learning multimodali hanno fatto progressi estremamente rapidi, sebbene ancora a costi elevati. Ci aspettiamo che sia l'accuratezza che i costi migliorino notevolmente nei prossimi anni, al punto che diventerà realistico applicarli all'intera nostra biblioteca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Miglioramenti dell'OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quando ciò accadrà, probabilmente conserveremo ancora i file originali, ma in aggiunta potremmo avere una versione molto più piccola della nostra biblioteca che la maggior parte delle persone vorrà mirrorare. Il punto è che il testo grezzo stesso si comprime ancora meglio ed è molto più facile da deduplicare, offrendoci ancora più risparmi."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "In generale, non è irrealistico aspettarsi almeno una riduzione di 5-10 volte della dimensione totale dei file, forse anche di più. Anche con una riduzione conservativa di 5 volte, ci aspetteremmo <strong>$1,000–$3,000 in 10 anni anche se la nostra biblioteca triplicasse di dimensioni</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Finestra critica"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Se queste previsioni sono accurate, <strong>basta aspettare un paio d'anni</strong> prima che l'intera nostra collezione venga ampiamente mirrorata. Così, nelle parole di Thomas Jefferson, \"posta al di là della portata degli incidenti\"."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Purtroppo, l'avvento degli LLM e il loro addestramento affamato di dati ha messo molti detentori di diritti d'autore sulla difensiva. Ancora più di quanto già non fossero. Molti siti web stanno rendendo più difficile lo scraping e l'archiviazione, le cause legali sono in aumento, e nel frattempo le biblioteche fisiche e gli archivi continuano a essere trascurati."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Possiamo solo aspettarci che queste tendenze continuino a peggiorare e che molte opere vadano perse ben prima di entrare nel pubblico dominio."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Siamo alla vigilia di una rivoluzione nella conservazione, ma <q>ciò che è perso non può essere recuperato.</q></strong> Abbiamo una finestra critica di circa 5-10 anni durante la quale è ancora piuttosto costoso gestire una biblioteca ombra e creare molti mirror in tutto il mondo, e durante la quale l'accesso non è ancora stato completamente chiuso."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Se riusciamo a colmare questa finestra, allora avremo davvero preservato la conoscenza e la cultura dell'umanità in perpetuo. Non dovremmo lasciare che questo tempo vada sprecato. Non dovremmo lasciare che questa finestra critica si chiuda su di noi."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Andiamo."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accesso esclusivo per le aziende LLM alla più grande collezione di libri di saggistica cinese al mondo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versione cinese 中文版</a>, <a %(news_ycombinator)s>Discuti su Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> L'Archivio di Anna ha acquisito una collezione unica di 7,5 milioni / 350TB di libri di saggistica cinese — più grande di Library Genesis. Siamo disposti a dare a un'azienda LLM l'accesso esclusivo, in cambio di un OCR di alta qualità e dell'estrazione del testo.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Questo è un breve post sul blog. Stiamo cercando un'azienda o un'istituzione che ci aiuti con l'OCR e l'estrazione del testo per una collezione massiccia che abbiamo acquisito, in cambio di un accesso esclusivo anticipato. Dopo il periodo di embargo, rilasceremo ovviamente l'intera collezione."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Testi accademici di alta qualità sono estremamente utili per l'addestramento degli LLM. Anche se la nostra collezione è cinese, dovrebbe essere utile anche per l'addestramento degli LLM in inglese: i modelli sembrano codificare concetti e conoscenze indipendentemente dalla lingua di origine."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Per questo, il testo deve essere estratto dalle scansioni. Cosa ottiene l'Archivio di Anna da tutto ciò? La ricerca a testo completo dei libri per i suoi utenti."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Poiché i nostri obiettivi sono allineati con quelli degli sviluppatori di LLM, stiamo cercando un collaboratore. Siamo disposti a darti <strong>accesso anticipato esclusivo a questa collezione in blocco per 1 anno</strong>, se puoi fare un OCR e un'estrazione del testo adeguati. Se sei disposto a condividere con noi l'intero codice del tuo pipeline, saremmo disposti a mantenere la collezione riservata per un periodo più lungo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Pagine di esempio"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Per dimostrarci che hai una buona pipeline, ecco alcune pagine di esempio da cui iniziare, tratte da un libro sui superconduttori. La tua pipeline dovrebbe gestire correttamente matematica, tabelle, grafici, note a piè di pagina, e così via."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Invia le tue pagine elaborate alla nostra email. Se sembrano buone, te ne invieremo altre in privato, e ci aspettiamo che tu sia in grado di eseguire rapidamente la tua pipeline anche su quelle. Una volta soddisfatti, possiamo fare un accordo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collezione"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Alcune informazioni aggiuntive sulla collezione. <a %(duxiu)s>Duxiu</a> è un enorme database di libri scansionati, creato dal <a %(chaoxing)s>SuperStar Digital Library Group</a>. La maggior parte sono libri accademici, scansionati per renderli disponibili digitalmente a università e biblioteche. Per il nostro pubblico anglofono, <a %(library_princeton)s>Princeton</a> e l'<a %(guides_lib_uw)s>Università di Washington</a> offrono buone panoramiche. C'è anche un eccellente articolo che fornisce maggiori dettagli: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercalo nell'Archivio di Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "I libri di Duxiu sono stati a lungo piratati su internet cinese. Di solito vengono venduti per meno di un dollaro dai rivenditori. Vengono tipicamente distribuiti utilizzando l'equivalente cinese di Google Drive, che è stato spesso hackerato per consentire più spazio di archiviazione. Alcuni dettagli tecnici possono essere trovati <a %(github_duty_machine)s>qui</a> e <a %(github_821_github_io)s>qui</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Sebbene i libri siano stati distribuiti in modo semi-pubblico, è piuttosto difficile ottenerli in blocco. Avevamo questo obiettivo in cima alla nostra lista di cose da fare, e abbiamo allocato diversi mesi di lavoro a tempo pieno per questo. Tuttavia, recentemente un incredibile, straordinario e talentuoso volontario ci ha contattato, dicendoci che aveva già fatto tutto questo lavoro — a grande costo. Ha condiviso con noi l'intera collezione, senza aspettarsi nulla in cambio, tranne la garanzia di una conservazione a lungo termine. Veramente notevole. Ha accettato di chiedere aiuto in questo modo per ottenere l'OCR della collezione."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "La collezione è composta da 7.543.702 file. Questo è più della non-fiction di Library Genesis (circa 5,3 milioni). La dimensione totale dei file è di circa 359TB (326TiB) nella sua forma attuale."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Siamo aperti ad altre proposte e idee. Contattaci. Dai un'occhiata all'Archivio di Anna per ulteriori informazioni sulle nostre collezioni, gli sforzi di conservazione e come puoi aiutare. Grazie!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Avviso: questo post del blog è stato deprecato. Abbiamo deciso che IPFS non è ancora pronto per il grande pubblico. Continueremo a collegarci ai file su IPFS dall'Archivio di Anna quando possibile, ma non lo ospiteremo più noi stessi, né raccomandiamo ad altri di fare mirror utilizzando IPFS. Si prega di consultare la nostra pagina Torrents se si desidera aiutare a preservare la nostra collezione."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Aiuta a seminare Z-Library su IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Come gestire una biblioteca ombra: operazioni presso l'Archivio di Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Non esiste <q>AWS per le associazioni di beneficenza ombra,</q> quindi come gestiamo l'Archivio di Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Gestisco <a %(wikipedia_annas_archive)s>l'Archivio di Anna</a>, il più grande motore di ricerca open-source senza scopo di lucro al mondo per <a %(wikipedia_shadow_library)s>biblioteche ombra</a>, come Sci-Hub, Library Genesis e Z-Library. Il nostro obiettivo è rendere la conoscenza e la cultura facilmente accessibili, e alla fine costruire una comunità di persone che insieme archiviano e preservano <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tutti i libri del mondo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In questo articolo mostrerò come gestiamo questo sito web, e le sfide uniche che comporta operare un sito web con uno status legale discutibile, poiché non esiste un “AWS per le associazioni di beneficenza ombra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Controlla anche l'articolo correlato <a %(blog_how_to_become_a_pirate_archivist)s>Come diventare un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Gettoni di innovazione"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Iniziamo con il nostro stack tecnologico. È volutamente noioso. Usiamo Flask, MariaDB ed ElasticSearch. E questo è letteralmente tutto. La ricerca è in gran parte un problema risolto e non intendiamo reinventarla. Inoltre, dobbiamo spendere i nostri <a %(mcfunley)s>gettoni di innovazione</a> su qualcos'altro: non essere chiusi dalle autorità."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Quindi quanto è legale o illegale l'Archivio di Anna esattamente? Questo dipende principalmente dalla giurisdizione legale. La maggior parte dei paesi crede in una qualche forma di copyright, il che significa che a persone o aziende viene assegnato un monopolio esclusivo su determinati tipi di opere per un certo periodo di tempo. Come nota a margine, all'Archivio di Anna crediamo che, sebbene ci siano alcuni benefici, nel complesso il copyright sia un netto negativo per la società — ma questa è una storia per un'altra volta."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Questo monopolio esclusivo su determinate opere significa che è illegale per chiunque al di fuori di questo monopolio distribuire direttamente quelle opere — inclusi noi. Ma l'Archivio di Anna è un motore di ricerca che non distribuisce direttamente quelle opere (almeno non sul nostro sito web clearnet), quindi dovremmo essere a posto, giusto? Non esattamente. In molte giurisdizioni non è solo illegale distribuire opere protette da copyright, ma anche collegarsi a luoghi che lo fanno. Un classico esempio di questo è la legge DMCA degli Stati Uniti."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Questo è l'estremo più severo dello spettro. All'altro estremo dello spettro potrebbero teoricamente esserci paesi senza alcuna legge sul copyright, ma questi non esistono realmente. Praticamente ogni paese ha una qualche forma di legge sul copyright nei libri. L'applicazione è un'altra storia. Ci sono molti paesi con governi che non si preoccupano di far rispettare la legge sul copyright. Ci sono anche paesi tra i due estremi, che vietano la distribuzione di opere protette da copyright, ma non vietano di collegarsi a tali opere."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Un'altra considerazione è a livello aziendale. Se un'azienda opera in una giurisdizione che non si preoccupa del copyright, ma l'azienda stessa non è disposta a correre alcun rischio, allora potrebbero chiudere il tuo sito web non appena qualcuno si lamenta."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Infine, una grande considerazione sono i pagamenti. Poiché dobbiamo rimanere anonimi, non possiamo utilizzare metodi di pagamento tradizionali. Questo ci lascia con le criptovalute, e solo un piccolo sottoinsieme di aziende le supporta (ci sono carte di debito virtuali pagate con criptovaluta, ma spesso non sono accettate)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architettura del sistema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Quindi supponiamo che tu abbia trovato alcune aziende disposte a ospitare il tuo sito web senza chiuderti — chiamiamoli “fornitori amanti della libertà” 😄. Scoprirai rapidamente che ospitare tutto con loro è piuttosto costoso, quindi potresti voler trovare alcuni “fornitori economici” e fare l'hosting effettivo lì, passando attraverso i fornitori amanti della libertà. Se lo fai bene, i fornitori economici non sapranno mai cosa stai ospitando e non riceveranno mai reclami."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con tutti questi fornitori c'è il rischio che ti chiudano comunque, quindi hai anche bisogno di ridondanza. Abbiamo bisogno di questo a tutti i livelli del nostro stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Una compagnia in qualche modo amante della libertà che si è messa in una posizione interessante è Cloudflare. Hanno <a %(blog_cloudflare)s>sostenuto</a> di non essere un fornitore di hosting, ma un servizio, come un ISP. Pertanto, non sono soggetti a richieste di rimozione DMCA o altre, e inoltrano qualsiasi richiesta al tuo effettivo fornitore di hosting. Sono arrivati al punto di andare in tribunale per proteggere questa struttura. Possiamo quindi usarli come un altro strato di caching e protezione."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare non accetta pagamenti anonimi, quindi possiamo utilizzare solo il loro piano gratuito. Questo significa che non possiamo utilizzare le loro funzionalità di bilanciamento del carico o failover. Pertanto, <a %(annas_archive_l255)s>abbiamo implementato questo noi stessi</a> a livello di dominio. Al caricamento della pagina, il browser controllerà se il dominio corrente è ancora disponibile e, in caso contrario, riscriverà tutti gli URL su un altro dominio. Poiché Cloudflare memorizza nella cache molte pagine, ciò significa che un utente può atterrare sul nostro dominio principale, anche se il server proxy è inattivo, e poi al clic successivo essere spostato su un altro dominio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Abbiamo ancora anche preoccupazioni operative normali da affrontare, come il monitoraggio della salute del server, la registrazione degli errori del backend e del frontend, e così via. La nostra architettura di failover consente una maggiore robustezza anche su questo fronte, ad esempio eseguendo un set completamente diverso di server su uno dei domini. Possiamo persino eseguire versioni precedenti del codice e dei datasets su questo dominio separato, nel caso in cui un bug critico nella versione principale passi inosservato."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Possiamo anche proteggerci contro Cloudflare che si rivolta contro di noi, rimuovendolo da uno dei domini, come questo dominio separato. Sono possibili diverse permutazioni di queste idee."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Strumenti"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vediamo quali strumenti usiamo per realizzare tutto questo. Questo è in continua evoluzione man mano che ci imbattiamo in nuovi problemi e troviamo nuove soluzioni."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Server applicazioni: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Server proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestione server: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Sviluppo: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hosting statico Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Ci sono alcune decisioni su cui abbiamo riflettuto a lungo. Una riguarda la comunicazione tra server: usavamo Wireguard per questo, ma abbiamo scoperto che occasionalmente smette di trasmettere dati, o trasmette dati solo in una direzione. Questo è successo con diverse configurazioni di Wireguard che abbiamo provato, come <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Abbiamo anche provato a tunnelizzare le porte tramite SSH, usando autossh e sshuttle, ma abbiamo incontrato <a %(github_sshuttle)s>problemi lì</a> (anche se non è ancora chiaro se autossh soffra di problemi TCP-over-TCP o meno — mi sembra solo una soluzione traballante, ma forse va bene?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Invece, siamo tornati alle connessioni dirette tra server, nascondendo che un server è in esecuzione su fornitori economici usando il filtraggio IP con UFW. Questo ha lo svantaggio che Docker non funziona bene con UFW, a meno che non si usi <code>network_mode: \"host\"</code>. Tutto ciò è un po' più soggetto a errori, perché esporrai il tuo server a internet con una piccola configurazione errata. Forse dovremmo tornare ad autossh — un feedback sarebbe molto apprezzato qui."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Abbiamo anche riflettuto su Varnish vs. Nginx. Attualmente ci piace Varnish, ma ha le sue stranezze e spigoli. Lo stesso vale per Checkmk: non lo amiamo, ma funziona per ora. Weblate è stato accettabile ma non incredibile — a volte temo che perderà i miei dati ogni volta che provo a sincronizzarlo con il nostro repository git. Flask è stato buono nel complesso, ma ha alcune stranezze che hanno richiesto molto tempo per essere risolte, come configurare domini personalizzati o problemi con la sua integrazione SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Finora gli altri strumenti sono stati ottimi: non abbiamo lamentele serie su MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Tutti questi hanno avuto alcuni problemi, ma nulla di troppo serio o che richieda molto tempo."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusione"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "È stata un'esperienza interessante imparare a configurare un motore di ricerca per una biblioteca ombra robusto e resiliente. Ci sono molti altri dettagli da condividere in post futuri, quindi fatemi sapere di cosa vorreste sapere di più!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Come sempre, stiamo cercando donazioni per supportare questo lavoro, quindi assicuratevi di visitare la pagina delle Donazioni su Archivio di Anna. Stiamo anche cercando altri tipi di supporto, come sovvenzioni, sponsor a lungo termine, fornitori di sistemi di pagamento ad alto rischio, forse anche annunci (di buon gusto!). E se volete contribuire con il vostro tempo e le vostre competenze, siamo sempre alla ricerca di sviluppatori, traduttori e così via. Grazie per il vostro interesse e supporto."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Ciao, sono Anna. Ho creato <a %(wikipedia_annas_archive)s>Archivio di Anna</a>, la più grande biblioteca ombra del mondo. Questo è il mio blog personale, in cui io e i miei compagni di squadra scriviamo di pirateria, conservazione digitale e altro."

#, fuzzy
msgid "blog.index.text2"
msgstr "Connettiti con me su <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Nota che questo sito web è solo un blog. Ospitiamo solo le nostre parole qui. Non ospitiamo né colleghiamo torrent o altri file protetti da copyright."

#, fuzzy
msgid "blog.index.heading"
msgstr "Post del blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 miliardi di dati estratti da WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Mettere 5.998.794 libri su IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Avviso: questo post del blog è stato deprecato. Abbiamo deciso che IPFS non è ancora pronto per il grande pubblico. Continueremo a collegarci ai file su IPFS dall'Archivio di Anna quando possibile, ma non lo ospiteremo più noi stessi, né raccomandiamo ad altri di fare mirror utilizzando IPFS. Si prega di consultare la nostra pagina Torrents se si desidera aiutare a preservare la nostra collezione."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>In breve:</strong> Archivio di Anna ha estratto tutti i dati di WorldCat (la più grande collezione di metadata di biblioteche al mondo) per creare una lista di libri da preservare.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Un anno fa, ci siamo <a %(blog)s>prefissati</a> di rispondere a questa domanda: <strong>Quale percentuale di libri è stata permanentemente preservata dalle biblioteche ombra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Una volta che un libro entra in una biblioteca ombra open-data come <a %(wikipedia_library_genesis)s>Library Genesis</a>, e ora <a %(wikipedia_annas_archive)s>Archivio di Anna</a>, viene replicato in tutto il mondo (tramite torrent), preservandolo praticamente per sempre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Per rispondere alla domanda su quale percentuale di libri sia stata preservata, dobbiamo conoscere il denominatore: quanti libri esistono in totale? E idealmente non abbiamo solo un numero, ma anche i metadata effettivi. In questo modo possiamo non solo confrontarli con le biblioteche ombra, ma anche <strong>creare una lista di libri rimanenti da preservare!</strong> Potremmo persino iniziare a sognare uno sforzo collettivo per affrontare questa lista."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Abbiamo estratto dati da <a %(wikipedia_isbndb_com)s>ISBNdb</a> e scaricato il <a %(openlibrary)s>dataset di Open Library</a>, ma i risultati non sono stati soddisfacenti. Il problema principale era che non c'era molta sovrapposizione di ISBN. Guarda questo diagramma di Venn dal <a %(blog)s>nostro post sul blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Siamo rimasti molto sorpresi dalla scarsa sovrapposizione tra ISBNdb e Open Library, entrambi includono liberamente dati da varie fonti, come estrazioni web e registri di biblioteche. Se entrambi facessero un buon lavoro nel trovare la maggior parte degli ISBN esistenti, i loro cerchi avrebbero sicuramente una sovrapposizione sostanziale, o uno sarebbe un sottoinsieme dell'altro. Ci siamo chiesti, quanti libri cadono <em>completamente al di fuori di questi cerchi</em>? Abbiamo bisogno di un database più grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "È allora che abbiamo puntato al più grande database di libri al mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Questo è un database proprietario della non-profit <a %(wikipedia_oclc)s>OCLC</a>, che aggrega record di metadata da biblioteche di tutto il mondo, in cambio di dare a quelle biblioteche accesso al dataset completo e di farle apparire nei risultati di ricerca degli utenti finali."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Anche se OCLC è una non-profit, il loro modello di business richiede la protezione del loro database. Bene, ci dispiace dirlo, amici di OCLC, stiamo dando via tutto. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Nel corso dell'ultimo anno, abbiamo meticolosamente estratto tutti i record di WorldCat. All'inizio, abbiamo avuto un colpo di fortuna. WorldCat stava appena lanciando il completo redesign del loro sito web (ad agosto 2022). Questo includeva una revisione sostanziale dei loro sistemi backend, introducendo molte falle di sicurezza. Abbiamo immediatamente colto l'opportunità e siamo riusciti a estrarre centinaia di milioni (!) di record in pochi giorni."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redesign di WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dopo di ciò, le falle di sicurezza sono state lentamente corrette una per una, fino a quando l'ultima che abbiamo trovato è stata risolta circa un mese fa. A quel punto avevamo praticamente tutti i record e stavamo solo cercando di ottenere record di qualità leggermente superiore. Quindi abbiamo sentito che è il momento di rilasciare!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Diamo un'occhiata ad alcune informazioni di base sui dati:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contenitori di Anna’s Archive (AAC)</a>, che sono essenzialmente <a %(jsonlines)s>JSON Lines</a> compressi con <a %(zstd)s>Zstandard</a>, più alcune semantiche standardizzate. Questi contenitori avvolgono vari tipi di record, basati sui diversi scraping che abbiamo implementato."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dati"

msgid "dyn.buy_membership.error.unknown"
msgstr "Si è verificato un errore sconosciuto. Per favore, scrivici a %(email)s allegando uno screenshot."

msgid "dyn.buy_membership.error.minimum"
msgstr "Questa moneta ha un minimo più alto del solito. Per favore, seleziona una durata o una moneta diversa."

msgid "dyn.buy_membership.error.try_again"
msgstr "La richiesta non può essere completata. Per favore, riprova tra alcuni minuti e, se il problema persiste, scrivici a %(email)s allegando uno screenshot."

msgid "dyn.buy_membership.error.wait"
msgstr "Errore nell'elaboramento del pagamento. Attendi qualche istante e riprova. Se il problema persiste per più di 24 ore, contattaci all'indirizzo %(email)s con uno screenshot."

msgid "page.comments.hidden_comment"
msgstr "commento nascosto"

msgid "page.comments.file_issue"
msgstr "Problema con il file: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Versione migliore"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Vuoi segnalare questo utente per comportamento abusivo o inappropriato?"

msgid "page.comments.report_abuse"
msgstr "Segnala abuso"

msgid "page.comments.abuse_reported"
msgstr "Abuso segnalato:"

msgid "page.comments.reported_abuse_this_user"
msgstr "Hai segnalato questo utente per abuso."

msgid "page.comments.reply_button"
msgstr "Rispondi"

msgid "page.md5.quality.logged_out_login"
msgstr "Per favore <a %(a_login)s>accedi</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Hai lasciato un commento. Potrebbe volerci un minuto prima che venga visualizzato."

msgid "page.md5.quality.comment_error"
msgstr "Qualcosa è andato storto. Ricarica la pagina e riprova."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s pagine interessate"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visibile su Libgen.rs Non-Fiction"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visibile su Libgen.rs Fiction"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visibile in Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Contrassegnato come non valido in Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Mancante in Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Segnata come \"spam\" in Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Contrassegnato come “file danneggiato” in Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Non tutte le pagine possono essere convertite in PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "L'esecuzione di exiftool su questo file non è riuscita"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (sconosciuto)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libri (saggistica)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libri (narrativa)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Articoli scientifici"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documenti normativi"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Riviste"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Fumetti"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partiture musicali"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibro"

msgid "common.md5_content_type_mapping.other"
msgstr "Altro"

msgid "common.access_types_mapping.aa_download"
msgstr "Download dal server partner"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Download esterno"

msgid "common.access_types_mapping.external_borrow"
msgstr "Prestito esterno"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Prestito esterno (stampa disabilitata)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Esplora i metadati"

msgid "common.access_types_mapping.torrents_available"
msgstr "Contenuti nei torrent"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Cinese"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu"

msgid "common.record_sources_mapping.uploads"
msgstr "Caricamenti su AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "Indice eBook EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadati cechi"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Libri"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Biblioteca Statale Russa"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Titolo"

msgid "common.specific_search_fields.author"
msgstr "Autore"

msgid "common.specific_search_fields.publisher"
msgstr "Casa editrice"

msgid "common.specific_search_fields.edition_varia"
msgstr "Edizione"

msgid "common.specific_search_fields.year"
msgstr "Anno di pubblicazione"

msgid "common.specific_search_fields.original_filename"
msgstr "Nome del file originale"

msgid "common.specific_search_fields.description_comments"
msgstr "Descrizione e commenti nei metadati"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Download da server partner attualmente non disponibile per questo file."

msgid "common.md5.servers.fast_partner"
msgstr "Server veloce del partner #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(consigliato)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(senza verifica del browser o liste d'attesa)"

msgid "common.md5.servers.slow_partner"
msgstr "Server lento del partner #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(un po' più veloce ma con lista d'attesa)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(senza lista d'attesa, ma potenzialmente molto lento)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs - Saggistica"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs - Narrativa"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(fai clic anche su \"GET\" in alto)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(fai clic su “GET” in alto)"

msgid "page.md5.box.download.libgen_ads"
msgstr "i loro annunci sono noti per contenere software dannosi, quindi usa un ad blocker o evitare di fare clic sugli annunci"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(È possibile che i file Nexus/STC non siano affidabili per il download)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library TOR"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(necessita di browser TOR)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Prendi in prestito da Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(solo per utenti verificati con stampa disabitata)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(il DOI associato potrebbe non essere disponibile su Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "raccolta"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Scarica torrent in blocco"

msgid "page.md5.box.download.experts_only"
msgstr "(solo esperti)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Cerca nell'Archivio di Anna per ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Cerca in altri database per ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Trova il record originale in ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Cerca nell'Archivio di Anna per ID Open Library"

msgid "page.md5.box.download.original_openlib"
msgstr "Trova il record originale in Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Cerca nell'Archivio di Anna per codice OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Trova il record originale in WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Cerca nell'Anna’s Archive il numero SSID DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Ricerca manuale su DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Cerca nell'Anna’s Archive il numero CADAL SSNO"

msgid "page.md5.box.download.original_cadal"
msgstr "Trova l'archivio originale su CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Cerca nell'Archivio di Anna per codice DXID DuXiu"

msgid "page.md5.box.download.edsebk"
msgstr "Indice eBook EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(nessuna verifica richiesta dal browser)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "SSNO CADAL %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadati cechi %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Libri %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Metadati"

msgid "page.md5.box.descr_title"
msgstr "Descrizione"

msgid "page.md5.box.alternative_filename"
msgstr "Nome file alternativo"

msgid "page.md5.box.alternative_title"
msgstr "Titolo alternativo"

msgid "page.md5.box.alternative_author"
msgstr "Autore alternativo"

msgid "page.md5.box.alternative_publisher"
msgstr "Editore alternativo"

msgid "page.md5.box.alternative_edition"
msgstr "Edizione alternativa"

msgid "page.md5.box.alternative_extension"
msgstr "Estensione alternativa"

msgid "page.md5.box.metadata_comments_title"
msgstr "Commenti sui metadati"

msgid "page.md5.box.alternative_description"
msgstr "Descrizione alternativa"

msgid "page.md5.box.date_open_sourced_title"
msgstr "Data \"open sourced\""

msgid "page.md5.header.scihub"
msgstr "Sci-Hub file “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending file “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Questo è il record relativo a un file proveniente da Internet Archive, non è il file sorgente scaricabile. Puoi provare a prendere in prestito il libro (link qui sotto) oppure usare questo URL per <a %(a_request)s> richiedere un file</a>."

msgid "page.md5.header.consider_upload"
msgstr "Se possiedi questo file e non è ancora disponibile nell'Archivio di Anna, puoi decidere di <a %(a_request)s>caricarlo</a>."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s record di metadati"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s record di metadati"

msgid "page.md5.header.meta_oclc"
msgstr "Numero OCLC (WorldCat) %(id)s record di metadati"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Record di metadati DuXiu SSID %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "Record di metadati CADAL SSNO %(id)s"

msgid "page.md5.header.meta_magzdb_id"
msgstr "Record di metadati di MagzDB ID %(id)s"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Record di metadati di Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Questo è un record di metadati, non un file scaricabile. Puoi utilizzare questo URL <a %(a_request)s>per richiedere un file</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Metadati dal record collegato"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Migliora i metadati su Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Attenzione: più registri con link:"

msgid "page.md5.header.improve_metadata"
msgstr "Migliora i metadati"

msgid "page.md5.text.report_quality"
msgstr "Segnala la qualità del file"

msgid "page.search.results.download_time"
msgstr "Tempo di download"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "Sito web:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Cerca nell'Archivio di Anna per “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Esploratore di Codici:"

msgid "page.md5.codes.code_search"
msgstr "Visualizza in Esploratore di Codici “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Maggiori informazioni…"

msgid "page.md5.tabs.downloads"
msgstr "Download (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Prestiti (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Esplora metadati (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Commenti (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Liste (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistiche (%(count)s)"

msgid "common.tech_details"
msgstr "Dettagli tecnici"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Questo file potrebbe presentare delle anomalie ed è stato nascosto da una libreria di origine.</span> Questo talvolta succede su richiesta del possessore di copyright oppure perché c'è un alternativa migliore disponibile, ma più spesso perché c'è un problema con il file stesso. Il download potrebbe comunque andare a buon fine ma raccomandiamo di cercare un file alternativo. Maggiori informazioni:"

msgid "page.md5.box.download.better_file"
msgstr "Una versione migliore di questo file potrebbe essere disponibile su %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Se desideri comunque scaricare questo file, assicurati di utilizzare solo software affidabili e aggiornati per aprirlo."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Download veloci"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Diventa un <a %(a_membership)s>membro</a> per supportarci nella conservazione a lungo termine di libri, pubblicazioni e molto altro. Per dimostrarti quanto te ne siamo grati, avrai accesso ai download rapidi. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Se doni questo mese, otterrai <strong>il doppio</strong> del numero di download veloci."

msgid "page.md5.box.download.header_fast_member"
msgstr "Ne hai %(remaining)s rimanenti per oggi. Grazie per essere dei nostri! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Hai esaurito i download rapidi per oggi."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Di recente hai scaricato questo file. I link restano validi per un po'."

msgid "page.md5.box.download.option"
msgstr "Opzione #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(nessun reindirizzamento)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(apri nel visualizzatore)"

msgid "layout.index.header.banner.refer"
msgstr "Segnala un amico/a ed entrambi/e avrete il %(percentage)s%% di bonus di download rapidi!"

msgid "layout.index.header.learn_more"
msgstr "Maggiori informazioni…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Download lenti"

msgid "page.md5.box.download.trusted_partners"
msgstr "Da partner affidabili."

msgid "page.md5.box.download.slow_faq"
msgstr "Maggiori informazioni nelle <a %(a_slow)s>FAQ</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(potrebbe richiedere la <a %(a_browser)s>verifica del browser</a> — download illimitati!)"

msgid "page.md5.box.download.after_downloading"
msgstr "Dopo il download:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Apri nel nostro visualizzatore"

msgid "page.md5.box.external_downloads"
msgstr "mostra download esterni"

msgid "page.md5.box.download.header_external"
msgstr "Download esterni"

msgid "page.md5.box.download.no_found"
msgstr "Nessun download trovato."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Tutti i mirror possiedono lo stesso file e dovrebbero essere sicuri da usare. Fai sempre attenzione, però, quando scarichi file da Internet e assicurati di mantenere aggiornati i tuoi dispositivi."

msgid "page.md5.box.download.dl_managers"
msgstr "Per file di grandi dimensioni, consigliamo di utilizzare un download manager per evitare interruzioni."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Download manager consigliati: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "A seconda del formato del file, per aprirlo avrai bisogno di un lettore ebook o PDF."

msgid "page.md5.box.download.readers.links"
msgstr "Lettori ebook consigliati: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Visualizzatore online dell'Archivio di Anna"

msgid "page.md5.box.download.conversion"
msgstr "Utilizza strumenti online per la conversione tra formati."

msgid "page.md5.box.download.conversion.links"
msgstr "Strumenti di conversione consigliati: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Puoi inviare file PDF ed EPUB al tuo eReader Kindle o Kobo."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Strumenti consigliati: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "“Invia a Kindle” di Amazon"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "“Invia a Kobo/Kindle” di djazz"

msgid "page.md5.box.download.support"
msgstr "Supporta autori e biblioteche"

msgid "page.md5.box.download.support.authors"
msgstr "Se ti piace e puoi permettertelo, considera di acquistare l'originale o di supportare direttamente gli autori."

msgid "page.md5.box.download.support.libraries"
msgstr "Se è disponibile presso la tua biblioteca locale, considera di prenderlo in prestito gratuitamente lì."

msgid "page.md5.quality.header"
msgstr "Qualità del file"

msgid "page.md5.quality.report"
msgstr "Aiuta la community segnalando la qualità di questo file! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Segnala un problema con il file (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Ottima qualità del file (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Aggiungi commento (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Cosa c'è che non va in questo file?"

msgid "page.md5.quality.copyright"
msgstr "Per favore usa il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Descrivi il problema (obbligatorio)"

msgid "page.md5.quality.issue_description"
msgstr "Descrizione del problema"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 di una versione migliore di questo file (se applicabile)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Compila questo campo se c'è un altro file che quasi coincide con questo file (stessa edizione, stessa estensione del file se riesci a trovarne uno), che le persone dovrebbero usare invece di questo file. Se conosci una versione migliore di questo file al di fuori di Anna’s Archive, allora per favore <a %(a_upload)s>caricala</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Puoi ottenere l'md5 dall'URL, ad esempio"

msgid "page.md5.quality.submit_report"
msgstr "Invia segnalazione"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Scopri come <a %(a_metadata)s>migliorare i metadati</a> di questo file in modo autonomo."

msgid "page.md5.quality.report_thanks"
msgstr "Grazie per aver inviato il tuo report. Sarà mostrato su questa pagina e verrà esaminato manualmente da Anna (fino a quando non avremo un sistema di moderazione adeguato)."

msgid "page.md5.quality.report_error"
msgstr "Qualcosa è andato storto. Ricarica la pagina e riprova."

msgid "page.md5.quality.great.summary"
msgstr "Se questo file è di alta qualità, puoi discutere di qualsiasi cosa che lo riguardi qui! In caso contrario, utilizza il pulsante “Segnala un problema con il file”."

msgid "page.md5.quality.loved_the_book"
msgstr "Ho adorato questo libro!"

msgid "page.md5.quality.submit_comment"
msgstr "Lascia un commento"

msgid "common.english_only"
msgstr "Il testo seguente è disponibile solo in inglese."

msgid "page.md5.text.stats.total_downloads"
msgstr "Download totali: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "Un 'file MD5' è un hash calcolato a partire dal contenuto del file e risulta ragionevolmente univoco sulla base di quel contenuto. Tutte le biblioteche-ombra che abbiamo indicizzato qui utilizzano principalmente gli MD5 per identificare i file."

msgid "page.md5.text.md5_info.text2"
msgstr "Un file potrebbe essere presente in più biblioteche-ombra. Per informazioni sui vari dataset che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Dataset</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Questo file è gestito dalla biblioteca <a %(a_ia)s>Controlled Digital Lending di IA</a> e indicizzato dall'Archivio di Anna per la ricerca. Per informazioni sui vari dataset che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dedicata</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Per informazioni su questo particolare file, consulta il suo <a %(a_href)s>file JSON</a>."

msgid "page.aarecord_issue.title"
msgstr "🔥 Errore nel caricare questa pagina"

msgid "page.aarecord_issue.text"
msgstr "Per favore, aggiorna per riprovare. <a %(a_contact)s>Contattaci</a> se il problema persiste per diverse ore."

msgid "page.md5.invalid.header"
msgstr "Non trovato"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” non è stato trovato nel nostro database."

msgid "page.login.title"
msgstr "Accedi/Registrati"

msgid "page.browserverification.header"
msgstr "Verifica del browser"

msgid "page.login.text1"
msgstr "Per impedire agli spam-bot di creare una marea di account falsi, dobbiamo prima verificare il tuo browser."

msgid "page.login.text2"
msgstr "Se ti trovi in un loop infinito, ti consigliamo di installare <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Potrebbe essere utile disattivare il blocco degli annunci (Adblock) e altre estensioni del browser."

msgid "page.codes.title"
msgstr "Codici"

msgid "page.codes.heading"
msgstr "Utilità di esplorazione codici"

#, fuzzy
msgid "page.codes.intro"
msgstr "Esplora i codici con cui sono contrassegnati i record, per prefisso. La colonna 'record' mostra il numero di record contrassegnati con codici che hanno il prefisso dato, come visibile nel motore di ricerca (inclusi i record solo con metadati). La colonna 'codici' mostra quanti codici effettivi hanno un determinato prefisso."

msgid "page.codes.why_cloudflare"
msgstr "Questa pagina potrebbe metterci un po' a caricare, questo é perché richiede un CAPTCHA di Cloudflare. I <a %(a_donate)s>Membri</a> possono saltare il captcha."

msgid "page.codes.dont_scrape"
msgstr "Si prega di non fare scraping di queste pagine. Consigliamo invece di <a %(a_import)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB, e di eseguire il nostro <a %(a_software)s>codice open source</a>. I dati non elaborati possono essere esplorati manualmente tramite file JSON come <a %(a_json_file)s>questo</a>."

msgid "page.codes.prefix"
msgstr "Prefisso"

msgid "common.form.go"
msgstr "Vai"

msgid "common.form.reset"
msgstr "Reimposta"

msgid "page.codes.search_archive_start"
msgstr "Cerca nell'Archivio di Anna"

msgid "page.codes.bad_unicode"
msgstr "Avviso: il codice contiene dei caratteri Unicode invalidi, e potrebbe comportarsi in maniera errata in varie situazioni. Il binario grezzo può essere decifrato dalla rappresentazione in base64 nell'URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefisso del codice noto “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Prefisso"

msgid "page.codes.code_label"
msgstr "Etichetta"

msgid "page.codes.code_description"
msgstr "Descrizione"

msgid "page.codes.code_url"
msgstr "URL per un codice specifico"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sarà sostituito con il valore del codice"

msgid "page.codes.generic_url"
msgstr "URL generico"

msgid "page.codes.code_website"
msgstr "Sito web"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s record corrispondente a “%(prefix_label)s”"
msgstr[1] "%(count)s record corrispondenti a “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "URL per codice specifico: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Altro…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codici che iniziano con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indice di"

msgid "page.codes.records_prefix"
msgstr "record"

msgid "page.codes.records_codes"
msgstr "codici"

msgid "page.codes.fewer_than"
msgstr "Meno di %(count)s record"

msgid "page.contact.dmca.form"
msgstr "Per reclami DMCA/copyright, compila <a %(a_copyright)s>questo form</a>."

msgid "page.contact.dmca.delete"
msgstr "Qualsiasi altro modo per contattarci in merito a richieste di copyright verrà automaticamente cancellato."

msgid "page.contact.checkboxes.text1"
msgstr "Accogliamo con piacere i tuoi commenti e le tue domande!"

msgid "page.contact.checkboxes.text2"
msgstr "Tuttavia, a causa della mole di spam e di e-mail senza senso che riceviamo, ti chiediamo di spuntare le caselle per confermare di aver compreso i termini per contattarci."

msgid "page.contact.checkboxes.copyright"
msgstr "Le richieste di copyright inviate a questa email saranno ignorate; utilizza invece il modulo dedicato."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "I server partner non sono disponibili a causa della chiusura di alcuni servizi di hosting. Dovrebbero essere di nuovo operativi a breve."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Gli abbonamenti saranno prolungati di conseguenza."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Non scriverci un'email per <a %(a_request)s>richieste di libri</a><br> o per <a %(a_upload)s>piccoli quantitativi di caricamenti</a>(<10k)."

msgid "page.donate.please_include"
msgstr "Quando fai domande sull'account o sulle donazioni, aggiungi il tuo account ID, screenshot, ricevute, quante più informazioni possibili. Controlliamo la nostra email solo ogni 1-2 settimane, quindi non includere queste informazioni ritarderà qualsiasi risoluzione."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Mostra email"

msgid "page.copyright.title"
msgstr "Modulo di reclamo DMCA/copyright"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Se hai un reclamo DMCA o di altro tipo relativo al copyright, compila questo modulo nel modo più preciso possibile. Se riscontri problemi, contattaci al nostro indirizzo DMCA dedicato: %(email)s. Nota che questo indirizzo è riservato alle domande; eventuali reclami inviati non verranno gestiti. Utilizza il modulo sottostante per inviare i tuoi reclami."

msgid "page.copyright.form.aa_urls"
msgstr "URL nell'Archivio di Anna (obbligatorio). Uno per riga. Includi solo URL che descrivono esattamente la stessa edizione di un libro. Se desideri fare un reclamo per più libri o più edizioni, invia questo modulo più volte."

msgid "page.copyright.form.aa_urls.note"
msgstr "I reclami riferiti a più libri o edizioni verranno respinti."

msgid "page.copyright.form.name"
msgstr "Il tuo nome (obbligatorio)"

msgid "page.copyright.form.address"
msgstr "Indirizzo (obbligatorio)"

msgid "page.copyright.form.phone"
msgstr "Numero di telefono (obbligatorio)"

msgid "page.copyright.form.email"
msgstr "E-mail (obbligatoria)"

msgid "page.copyright.form.description"
msgstr "Descrizione chiara del materiale di origine (obbligatoria)"

msgid "page.copyright.form.isbns"
msgstr "ISBN del materiale di origine (se applicabile). Uno per riga. Includi solo i codici che corrispondono esattamente all'edizione per cui stai effettuando un reclamo relativo al copyright."

msgid "page.copyright.form.openlib_urls"
msgstr "URL <a %(a_openlib)s>Open Library</a> del materiale di origine, uno per riga. Dedica un attimo a cercare il materiale di origine su Open Library; questo ci aiuterà a verificare il tuo reclamo."

msgid "page.copyright.form.external_urls"
msgstr "URL del materiale di origine, uno per riga (obbligatorio). Includi quanti più URL possibile, per aiutarci a verificare il tuo reclamo (es. Amazon, WorldCat, Google Libri, DOI)."

msgid "page.copyright.form.statement"
msgstr "Dichiarazione e firma (obbligatorie)"

msgid "page.copyright.form.submit_claim"
msgstr "Invia reclamo"

msgid "page.copyright.form.on_success"
msgstr "✅ Grazie per aver inviato il tuo reclamo relativo al copyright. Lo esamineremo il prima possibile. Ricarica la pagina per inviarne un altro."

msgid "page.copyright.form.on_failure"
msgstr "❌ Qualcosa è andato storto. Ricarica la pagina e riprova."

msgid "page.datasets.title"
msgstr "Dataset"

msgid "page.datasets.common.intro"
msgstr "Contattaci se sei interessato/a a eseguire il mirroring di questo dataset per scopi di <a %(a_archival)s>archiviazione</a> o <a %(a_llm)s>addestramento dell'LLM</a>."

msgid "page.datasets.intro.text2"
msgstr "La nostra missione è archiviare tutti i libri del mondo (così come articoli, riviste, ecc.) e renderli ampiamente accessibili. Crediamo che tutti i libri debbano essere replicati in modo esteso, per garantire ridondanza e resilienza. Per questo motivo, stiamo raccogliendo file da una varietà di fonti: alcune sono completamente aperte e possono essere replicate in toto (come Sci-Hub). Altre sono chiuse e protette, quindi cerchiamo di eseguirne lo scraping per “liberare” i libri. Altre ancora rappresentano una via di mezzo."

msgid "page.datasets.intro.text3"
msgstr "Tutti i nostri dati possono essere <a %(a_torrents)s>scaricati via torrent</a> e tutti i nostri metadati possono essere <a %(a_anna_software)s>generati</a> o <a %(a_elasticsearch)s>scaricati</a> come database ElasticSearch e MariaDB. I dati non elaborati possono essere consultati manualmente attraverso file JSON come <a %(a_dbrecord)s>questo</a>."

msgid "page.datasets.overview.title"
msgstr "Panoramica"

msgid "page.datasets.overview.text1"
msgstr "Di seguito una rapida panoramica delle fonti dei file nell'Archivio di Anna."

msgid "page.datasets.overview.source.header"
msgstr "Sorgente"

msgid "page.datasets.overview.size.header"
msgstr "Dimensione"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% in mirroring da AA / torrent disponibili"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentuali del numero di file"

msgid "page.datasets.overview.last_updated.header"
msgstr "Ultimo aggiornamento"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Saggi e narrativa"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s file"
msgstr[1] "%(count)s file"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Tramite Libgen.li “scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelato dal 2021; la maggior parte dell'archivio è disponibile tramite torrent"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: aggiunte minori dopo quel momento</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Escluso “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "I torrent di narrativa sono in ritardo (anche se per gli ID ~4-6M non sono stati creati torrent, poiché si sovrappongono ai nostri torrent di Zlib)."

msgid "page.datasets.zlibzh.searchable"
msgstr "La raccolta \"Cinese\" in Z-Library sembra coincidere con la nostra raccolta DuXiu, ma con MD5 diversi. Escludiamo questi file dai torrent per evitare duplicati, ma li mostriamo comunque nel nostro indice di ricerca."

msgid "common.record_sources_mapping.iacdl"
msgstr "Controlled Digital Lending di IA"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ dei file sono ricercabili."

msgid "page.datasets.overview.total"
msgstr "Totale"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Esclusi duplicati"

msgid "page.datasets.overview.text4"
msgstr "Poiché le biblioteche-ombra spesso sincronizzano i dati tra loro, c'è una notevole sovrapposizione. Ecco perché i totali non corrispondono."

msgid "page.datasets.overview.text5"
msgstr "La percentuale \"mirrored and seeded by Anna’s Archive\" mostra di quanti file eseguiamo direttamente il mirroring. Eseguiamo il seed di questi file in blocco tramite torrent e li rendiamo disponibili per il download diretto tramite siti web partner."

msgid "page.datasets.source_libraries.title"
msgstr "Biblioteche di origine"

msgid "page.datasets.source_libraries.text1"
msgstr "Alcune biblioteche di origine promuovono la condivisione in blocco dei loro dati tramite torrent, mentre altre non condividono facilmente la loro raccolta. In quest'ultimo caso, l'Archivio di Anna cerca di raggruppare le loro raccolte e renderle disponibili (vedi la nostra pagina <a %(a_torrents)s>Torrent</a>). Ci sono anche situazioni intermedie, ad esempio casi in cui le biblioteche di origine sono disposte a condividere, ma non hanno le risorse per farlo. In questi casi, cerchiamo di dare una mano."

msgid "page.datasets.source_libraries.text2"
msgstr "Di seguito è riportata una panoramica delle nostre modalità di interazione con le diverse biblioteche di origine."

msgid "page.datasets.sources.source.header"
msgstr "Fonte"

msgid "page.datasets.sources.files.header"
msgstr "File"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dump dei database HTTP</a> giornalieri"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrent automatizzati per <a %(nonfiction)s>Saggistica</a> e <a %(fiction)s>Narrativa</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(covers)s>torrent di copertine di libri</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub ha congelato i nuovi file dal 2021."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dump di metadati disponibili <a %(scihub1)s>qui</a> e <a %(scihub2)s>qui</a>, oltre che come parte del <a %(libgenli)s>database di Libgen.li</a> (che utilizziamo)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrent di dati disponibili <a %(scihub1)s>qui</a>, <a %(scihub2)s>qui</a> e <a %(libgenli)s>qui</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Alcuni nuovi file vengono <a %(libgenrs)s>aggiunti</a> a <a %(libgenli)s>Libgen’s “scimag”</a>, ma non abbastanza da giustificare nuovi torrent"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dump del database HTTP</a> trimestrali"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s I torrent di Saggistica sono condivisi con Libgen.rs (e replicati <a %(libgenli)s>qui</a>)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s L'Archivio di Anna e Libgen.li gestiscono collaborativamente raccolte di <a %(comics)s>fumetti</a>, <a %(magazines)s>riviste</a>, <a %(standarts)s>documenti standard</a> e <a %(fiction)s>narrativa (diverse da quelli di Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s La loro raccolta “fiction_rus” (narrativa russa) non ha torrent dedicati, ma è dsponibile tramite torrent di altri; noi manteniamo un <a %(fiction_rus)s>mirror</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s L'Archivio di Anna e Z-Library gestiscono in modo collaborativo una raccolta di <a %(metadata)s>metadati di Z-Library</a> e <a %(files)s>file di Z-Library</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Alcuni metadati sono disponibili tramite <a %(openlib)s>dump del database di Open Library</a>, ma non sono relativi all'intera raccolta IA"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nessun dump di metadati facilmente accessibile disponibile per l'intera raccolta"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(ia)s>metadati IA</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s File disponibili per il prestito solo con in modo limitato, con varie restrizioni di accesso"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(ia)s>file IA</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Vari database di metadati sparsi per la rete Internet cinese; spesso si tratta di database a pagamento"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nessun dump di metadati facilmente accessibile disponibile per l'intera raccolta."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(duxiu)s>metadati DuXiu</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Vari database di file sparsi sulla rete Internet cinese; spesso database a pagamento"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s La maggior parte dei file è accessibile solo utilizzando account premium BaiduYun; velocità di download limitate."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(duxiu)s>file DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Varie fonti minori o occasionali. Incoraggiamo le persone a caricare prima su altre biblioteche-ombra, ma a volte le raccolte sono troppo grandi per essere ordinate da altri, ma non abbastanza da giustificare una categoria propria."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Fonti solo metadati"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Arricchiamo anche la nostra raccolta con fonti solo metadati, che possiamo abbinare a file, ad esempio utilizzando i numeri ISBN o altri campi. Di seguito è riportata una panoramica di queste fonti. Anche in questo caso, alcune sono completamente aperte, mentre altre richiedono lo scraping da parte nostra."

msgid "page.faq.metadata.inspiration"
msgstr "La nostra ispirazione per raccogliere metadati è l'obiettivo di Aaron Swartz di “una pagina web per ogni libro mai pubblicato”, per il quale ha creato <a %(a_openlib)s>Open Library</a>. Quel progetto ha avuto successo, ma la nostra posizione unica ci permette di ottenere metadati non accessibili a quel progetto. Un'altra ispirazione è stata il nostro desiderio di sapere <a %(a_blog)s>quanti libri ci sono nel mondo</a>, per poter calcolare quanti libri ci restano ancora da salvare."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nota che, nella ricerca dei metadati, mostriamo i record originali. Non eseguiamo alcuna unione dei record."

msgid "page.datasets.sources.last_updated.header"
msgstr "Ultimo aggiornamento"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Dump del database</a> mensili"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Non disponibile direttamente in blocco, con protezione anti-scraping"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s L'Archivio di Anna gestisce una raccolta di <a %(worldcat)s>metadati OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Database unificato"

msgid "page.datasets.unified_database.text1"
msgstr "Combiniamo tutte le fonti sopra menzionate in un unico database unificato che utilizziamo per alimentare questo sito web; questo database non è disponibile direttamente, ma poiché l'Archivio di Anna è completamente open source, può essere <a %(a_generated)s>generato</a> o <a %(a_downloaded)s>scaricato</a> abbastanza facilmente come database ElasticSearch e MariaDB. Gli script su quella pagina scaricheranno automaticamente tutti i metadati necessari dalle fonti sopra citate."

msgid "page.datasets.unified_database.text2"
msgstr "Se desideri esplorare i nostri dati prima di eseguire quegli script in locale, puoi consultare i nostri file JSON, che includono collegamenti ad altri file JSON. <a %(a_json)s>Questo file</a> è un buon punto di partenza."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adattato dal nostro <a %(a_href)s>post sul blog</a>."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> è un enorme database di libri digitalizzati, creato dal <a %(superstar_link)s>SuperStar Digital Library Group</a>. La maggior parte sono libri accademici, digitalizzati per renderli disponibili a università e biblioteche. Per il nostro pubblico di lingua inglese, <a %(princeton_link)s>Princeton</a> e l'<a %(uw_link)s>Università di Washington</a> offrono buone panoramiche. C'è anche un eccellente articolo che fornisce ulteriori informazioni: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "I libri di Duxiu sono stati a lungo piratati sulla rete Internet cinese. Di solito vengono venduti per meno di un dollaro e vengono tipicamente distribuiti utilizzando l'equivalente cinese di Google Drive, che è stato spesso hackerato per aumentare lo spazio di archiviazione. Alcuni dettagli tecnici sono reperibili <a %(link1)s>qui</a> e <a %(link2)s>qui</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Sebbene i libri siano stati distribuiti in modalità semi-pubblica, è piuttosto difficile ottenerli in blocco. Questa attività era in cima alla nostra lista di cose da fare e ci abbiamo dedicato diversi mesi di lavoro a tempo pieno. Tuttavia, alla fine del 2023, un volontario incredibile, straordinario e talentuoso ci ha contattato, dicendoci che aveva già fatto tutto questo enorme lavoro. Ci ha condiviso l'intera raccolta, senza aspettarsi nulla in cambio, tranne la garanzia di una conservazione a lungo termine. Veramente eccezionale."

msgid "page.datasets.common.resources"
msgstr "Risorse"

msgid "page.datasets.common.total_files"
msgstr "File totali: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Dimensione totale dei file: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "File replicati dall'Archivio di Anna: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Ultimo aggiornamento: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Torrent dell'Archivio di Anna"

msgid "page.datasets.common.aa_example_record"
msgstr "Esempio di record sull'Archivio di Anna"

msgid "page.datasets.duxiu.blog_post"
msgstr "Il nostro post sul blog riguardo a questi dati"

msgid "page.datasets.common.import_scripts"
msgstr "Script per l'importazione dei metadati"

msgid "page.datasets.common.aac"
msgstr "Formato \"Anna’s Archive Containers\""

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Maggiori informazioni dai nostri volontari (note non elaborate):"

msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

msgid "page.datasets.ia.description"
msgstr "Questo dataset è strettamente correlato al <a %(a_datasets_openlib)s>dataset di Open Library</a>. Contiene una raccolta di tutti i metadati e una grande porzione di file dalla IA Controlled Digital Lending Library. Gli aggiornamenti vengono rilasciati nel <a %(a_aac)s>formato \"Anna’s Archive Containers\"</a>."

msgid "page.datasets.ia.description2"
msgstr "Questi record sono riferiti direttamente dal dataset di Open Library, ma contengono anche record che non sono presenti su quella piattaforma. Abbiamo anche un certo numero di file di dati ottenuti dai membri della community nel corso degli anni."

msgid "page.datasets.ia.description3"
msgstr "La raccolta è composta da due parti. Entrambe sono necessarie per ottenere tutti i dati (eccetto i torrent obsoleti, che sono barrati nella pagina dei torrent)."

msgid "page.datasets.ia.part1"
msgstr "la nostra prima release, prima di allinearci sul formato <a %(a_aac)s>Anna’s Archive Containers (AAC)</a>. Contiene metadati (in formato json e xml), pdf (dai sistemi di prestito digitale acsm e lcpdf) e miniature delle copertine."

msgid "page.datasets.ia.part2"
msgstr "nuove release incrementali, utilizzando AAC. Contiene solo metadati con timestamp successivi al 1° gennaio 2023, poiché il resto è già coperto da “ia”. Anche tutti i file pdf, questa volta dai sistemi di prestito acsm e “bookreader” (il lettore web di IA). Nonostante il nome non sia completamente corretto, continuiamo a popolare i file bookreader nella raccolta ia2_acsmpdf_files, poiché sono mutuamente esclusive."

msgid "page.datasets.common.main_website"
msgstr "Sito web principale %(source)s"

msgid "page.datasets.ia.ia_lending"
msgstr "Biblioteca per il prestito digitale"

msgid "page.datasets.common.metadata_docs"
msgstr "Documentazione dei metadati (la maggior parte dei campi)"

msgid "page.datasets.isbn_ranges.title"
msgstr "Informazioni sul paese ISBN"

msgid "page.datasets.isbn_ranges.text1"
msgstr "L'Agenzia Internazionale ISBN pubblica regolarmente gli intervalli di codici assegnati alle agenzie nazionali. Da questi possiamo dedurre a quale paese, regione o gruppo linguistico appartiene un ISBN. Attualmente utilizziamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Risorse"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultimo aggiornamento: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sito web ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadati"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Per la storia dei diversi fork di Library Genesis, consulta la pagina dedicata a <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li contiene la maggior parte degli stessi contenuti e metadati di Libgen.rs, ma con alcune raccolte aggiuntive, come fumetti, riviste e documenti standard. Inoltre, ha integrato <a %(a_scihub)s>Sci-Hub</a> nei suoi metadati e nel suo motore di ricerca, che usiamo per il nostro database."

msgid "page.datasets.libgen_li.description3"
msgstr "I metadati per questa biblioteca sono liberamente disponibili <a %(a_libgen_li)s>su libgen.li</a>. Tuttavia, questo server è lento e non supporta il riavvio delle connessioni interrotte. Gli stessi file sono disponibili anche su <a %(a_ftp)s>un server FTP</a>, che funziona meglio."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Sono disponibili torrent per la maggior parte dei contenuti aggiuntivi; in particolare, i torrent per fumetti, riviste e documenti standard sono stati rilasciati in collaborazione con l'Archivio di Anna."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "La raccolta di narrativa dispone di torrent propri (diversi da quelli di <a %(a_href)s>Libgen.rs</a>) a partire da %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Secondo l'amministratore di Libgen.li, la raccolta “fiction_rus” (narrativa russa) dovrebbe essere disponibile tramite torrent pubblicati regolarmente da <a %(a_booktracker)s>booktracker.org</a>, in particolare i torrent <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (che noi manteniamo <a %(a_torrents)s>qui</a>, anche se non abbiamo ancora definito quali torrent corrispondono a quali file)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Le statistiche per tutte le raccolte sono reperibili <a %(a_href)s>sul sito di libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Anche la saggistica sembra essersi diversificata, ma senza nuovi torrent. Sembra che ciò sia accaduto dall'inizio del 2022, anche se non lo abbiamo verificato."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Alcuni intervalli senza torrent (come quelli di narrativa da f_3463000 a f_4260000) sono probabilmente file duplicati di Z-Library (o altri); potrebbe essere opportuno fare un po' di deduplica e creare torrent per i file univoci di lgli in questi intervalli."

msgid "page.datasets.libgen_li.description5"
msgstr "Nota che i file torrent che fanno riferimento a “libgen.is” sono esplicitamente mirror di <a %(a_libgen)s>Libgen.rs</a> (“.is” è un dominio diverso utilizzato da Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Una risorsa utile per utilizzare i metadati è <a %(a_href)s>questa pagina</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrent di narrativa nell'Archivio di Anna"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrent di fumetti nell'Archivio di Anna"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrent di riviste nell'Archivio di Anna"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrent di documenti standard nell'Archivio di Anna"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrent di narrativa russa nell'Archivio di Anna"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadati"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadati via FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Informazioni sui campi dei metadati"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Mirror di altri torrent (e torrent unici di narrativa e fumetti)"

msgid "page.datasets.libgen_li.forum"
msgstr "Forum di discussione"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Il nostro post sul blog riguardo alle release di fumetti"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "In breve, per quanto riguarda i diversi fork di Library Genesis (o “Libgen”), nel tempo le diverse persone coinvolte in Library Genesis hanno avuto dissapori e le loro strade si sono separate."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "La versione “.fun” è stata creata dal fondatore originale. È in fase di rinnovamento a favore di una nuova versione più distribuita."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "La versione “.rs” ha dati molto simili e rilascia la loro raccolta tramite torrent in bulk con la maggiore coerenza. È grossomodo divisa in una sezione “narrativa” e una “saggistica”."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originariamente su “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "La <a %(a_li)s>versione “.li”</a> ha una vasta raccolta di fumetti, oltre ad altri contenuti, che non sono (ancora) disponibili per il download in massa tramite torrent. Ha una raccolta separata di torrent di libri di narrativa e il suo database include i metadati di <a %(a_scihub)s>Sci-Hub</a>."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Secondo questo <a %(a_mhut)s>post sul forum</a>, Libgen.li era originariamente in hosting su “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> in un certo senso è anche una fork di Library Genesis, anche se hanno usato un nome diverso per il loro progetto."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Questa pagina riguarda la versione “.rs”, nota per pubblicare in modo coerente sia i propri metadati sia il contenuto completo del proprio catalogo di libri. La sua raccolta di libri è divisa tra narrativa e saggistica."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Una risorsa utile per utilizzare i metadati è <a %(a_metadata)s>questa pagina</a> (blocca intervalli di IP, potrebbe essere necessaria una VPN)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partire da marzo 2024, nuovi torrent vengono pubblicati in <a %(a_href)s>questo thread del forum</a> (blocca intervalli IP, potrebbe essere necessaria una VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrent di saggistica nell'Archivio di Anna"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrent di narrativa nell'Archivio di Anna"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadati di Libgen.rs"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Informazioni sui campi di metadati di Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrent di saggistica di Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrent di narrativa di Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum di discussione di Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrent dell'Archivio di Anna (copertine dei libri)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Il nostro blog sulla release delle copertine dei libri"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis è noto per rendere già generosamente disponibili i propri dati in blocco tramite torrent. La nostra raccolta di Libgen consiste in dati aggiuntivi che LibGen non pubblica direttamente, in collaborazione con loro. Un grande ringraziamento a tutti coloro che lavorano con Library Genesis per la loro collaborazione!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Questa <a %(blog_post)s>prima release</a> è piuttosto piccola: circa 300GB di copertine di libri dal fork di Libgen.rs, sia di narrativa che di saggistica. Sono organizzati nello stesso modo in cui appaiono su libgen.rs, ad esempio:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s per un libro di saggistica."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s per un libro di narrativa."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Proprio come con la raccolta di Z-Library, abbiamo messo tutti i titoli in un grande file .tar, accessibile usando <a %(a_ratarmount)s>ratarmount</a> se vuoi fornire i file direttamente."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> è un database proprietario della non-profit <a %(a_oclc)s>OCLC</a>, che aggrega record di metadati dalle biblioteche di tutto il mondo. È probabilmente la più grande collezione di metadati di biblioteche al mondo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Ottobre 2023, rilascio iniziale:"

msgid "page.datasets.worldcat.description2"
msgstr "Nel mese di ottobre 2023 abbiamo <a %(a_scrape)s>rilasciato</a> una raccolta completa del database OCLC (WorldCat), nel <a %(a_aac)s>formato \"Anna’s Archive Containers\"</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrent dell'Archivio di Anna"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Il nostro post sul blog riguardo a questi dati"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library è un progetto open source di Internet Archive per catalogare ogni libro nel mondo. Ha attuato una delle principali iniziative di digitalizzazione di libri al mondo e ha molti libri disponibili per il prestito digitale. Il suo catalogo di metadati dei libri è liberamente disponibile per il download ed è incluso nell'Archivio di Anna (anche se attualmente non nella ricerca, tranne cercando esplicitamente un ID di Open Library)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Metadati"

msgid "page.datasets.isbndb.release1.title"
msgstr "Release 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Questo è un dump di molte chiamate a isbndb.com durante il mese di settembre 2022. Abbiamo cercato di coprire tutti gli intervalli ISBN. Si tratta di circa 30,9 milioni di record. Sul loro sito web affermano di avere, in effetti, 32,6 milioni di record, quindi alcuni potrebbero mancarci, o <em>loro</em> potrebbero sbagliarsi."

msgid "page.datasets.isbndb.release1.text2"
msgstr "Le risposte JSON sono praticamente dati non elaborati provenienti dal loro server. Un problema di qualità dei dati che abbiamo notato è che i numeri ISBN-13 che iniziano con un prefisso diverso da \"978-\", includono comunque un campo \"isbn\" che è semplicemente il numero ISBN-13 senza i primi 3 numeri (e la cifra di controllo ricalcolata). Questo ovviamente non è corretto, ma apparentemente loro procedono, quindi non abbiamo apportato modifiche."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Un altro potenziale problema che potresti incontrare è il fatto che il campo \"isbn13\" ha duplicati, quindi non puoi usarlo come chiave primaria in un database. I campi combinati \"isbn13\" + \"isbn\" sembrerebbero univoci."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Per una panoramica su Sci-Hub, puoi fare riferimento al suo <a %(a_scihub)s>sito ufficiale</a>, alla <a %(a_wikipedia)s>pagina Wikipedia</a> e a questa <a %(a_radiolab)s>intervista podcast</a>."

msgid "page.datasets.scihub.description2"
msgstr "Nota che Sci-Hub è stato <a %(a_reddit)s>congelato dal 2021</a>. Era stato congelato in precedenza, ma nel 2021 sono stati aggiunti alcuni milioni di articoli. Tuttavia, un numero limitato di articoli viene ancora aggiunto alle raccolte “scimag” di Libgen, sebbene non abbastanza da giustificare nuovi torrent in blocco."

msgid "page.datasets.scihub.description3"
msgstr "Utilizziamo i metadati di Sci-Hub forniti da <a %(a_libgen_li)s>Libgen.li</a> nella sua raccolta “scimag”. Utilizziamo anche il dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Nota che i torrent “smarch” sono <a %(a_smarch)s>deprecati</a> e quindi non inclusi nel nostro elenco di torrent."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent nell'Archivio di Anna"

msgid "page.datasets.scihub.link_metadata"
msgstr "Metadati e torrent"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent su Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent su Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Aggiornamenti su Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pagina Wikipedia"

msgid "page.datasets.scihub.link_podcast"
msgstr "Intervista podcast"

msgid "page.datasets.upload.title"
msgstr "Caricamenti nell'Archivio di Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Panoramica dalla <a %(a1)s>pagina dei datasets</a>."

msgid "page.datasets.upload.description"
msgstr "Varie fonti minori o occasionali. Incoraggiamo le persone a caricare prima su altre biblioteche-ombra, ma a volte le loro raccolte sono troppo grandi per essere ordinate da altri, ma non abbastanza da giustificare una categoria propria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "La raccolta “upload” è suddivisa in raccolte secondarie più piccole, che sono indicate negli AACID e nei nomi dei torrent. Tutte le raccolte secondarie sono state prima deduplicate rispetto a quella principale, anche se i file JSON dei metadati “upload_records” contengono ancora molti riferimenti ai file originali. I file non di libri sono stati anche rimossi dalla maggior parte delle raccolte secondarie e tipicamente <em>non</em> sono indicati negli “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Molte raccolte secondarie sono a loro volta composte da ulteriori raccolte secondarie (ad esempio da diverse fonti originali), che sono rappresentate come directory nei campi “filepath”."

msgid "page.datasets.upload.subs.heading"
msgstr "Le raccolte secondarie sono:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Sottocollezione"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Note"

msgid "page.datasets.upload.action.browse"
msgstr "sfoglia"

msgid "page.datasets.upload.action.search"
msgstr "cerca"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Da <a %(a_href)s>aaaaarg.fail</a>. Apprentemente abbastanza completo. Dal nostro volontario “cgiym”."

msgid "page.datasets.upload.source.acm"
msgstr "Da un torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Ha una livello di sovrapposizione abbastanza alto con le raccolte di articoli esistenti, ma pochissime corrispondenze MD5, quindi abbiamo deciso di mantenerlo completamente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raccolta di <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), da parte del volontario <q>j</q>. Corrisponde ai metadata di <q>airitibooks</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Da una collezione <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. In parte dalla fonte originale, in parte da the-eye.eu, in parte da altri mirror."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Da un sito web privato di torrent di libri, <a %(a_href)s>Bibliotik</a> (spesso chiamato “Bib”), i cui libri sono stati raggruppati in torrent per nome (A.torrent, B.torrent) e distribuiti tramite the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Dal nostro volontario “bpb9v”. Per ulteriori informazioni su <a %(a_href)s>CADAL</a>, vedi le note nella nostra <a %(a_duxiu)s>pagina del dataset DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Altro dal nostro volontario “bpb9v”, principalmente file DuXiu, oltre a una cartella “WenQu” e “SuperStar_Journals” (SuperStar è la società dietro DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Dal nostro volontario “cgiym”, testi cinesi da varie fonti (rappresentati come sottodirectory), inclusi quelli di <a %(a_href)s>China Machine Press</a> (un importante editore cinese)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Raccolte non cinesi (rappresentate come sottodirectory) dal nostro volontario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raccolta di libri sull'architettura cinese, da parte del volontario <q>cm</q>: <q>L'ho ottenuto sfruttando una vulnerabilità di rete presso la casa editrice, ma quella falla è stata chiusa</q>. Corrisponde ai metadata di <q>chinese_architecture</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Libri della casa editrice accademica <a %(a_href)s>De Gruyter</a>, raccolti da alcuni torrent di grandi dimensioni."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Dallo scraping di <a %(a_href)s>docer.pl</a>, un sito web polacco di condivisione di file focalizzato su libri e altre opere scritte. Raccolto alla fine del 2023 dal volontario “p”. Non abbiamo buoni metadati dal sito originale (nemmeno le estensioni dei file), ma abbiamo filtrato i file simili a libri e spesso siamo riusciti a estrarre i metadati dai file stessi."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "Epub DuXiu, direttamente da DuXiu, raccolti dal volontario “w”. Solo i libri recenti di DuXiu sono disponibili direttamente tramite ebook, quindi la maggior parte di questi titoli deve essere recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "File DuXiu rimanenti dal volontario “m”, che non erano nel formato proprietario PDG di DuXiu (il principale <a %(a_href)s>dataset DuXiu</a>). Raccolti da molte fonti originali, purtroppo senza preservarle nel percorso del file."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raccolta di libri erotici, da parte del volontario <q>do no harm</q>. Corrisponde ai metadata di <q>hentai</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Raccolta da un editore giapponese di Manga di cui è stato eseguito lo scraping dal volontario “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivi giudiziari selezionati di Longquan</a>, forniti dal volontario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scraping di <a %(a_href)s>magzdb.org</a>, un alleato di Library Genesis (ha un link sulla homepage di libgen.rs) ma che non voleva fornire i propri file direttamente. Ottenuto dal volontario “p” alla fine del 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Vari caricamenti di piccole dimensioni, troppo per farne una raccolta secondaria a sé stante, ma rappresentati come directory."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebook da AvaxHome, un sito russo di condivisione file."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivio di giornali e riviste. Corrisponde ai metadata di <q>newsarch_magz</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raccolta del <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Raccolta del volontario “o” che ha riunito libri polacchi direttamente dai siti di release (“scene”) originali."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Raccolte combinate di <a %(a_href)s>shuge.org</a> dai volontari “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Biblioteca Imperiale di Trantor”</a> (chiamata così in onore della biblioteca fittizia), raccolta nel 2022 dal volontario “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Raccolte secondarie di secondo livello (rappresentate come directory) dal volontario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (di <a %(a_sikuquanshu)s>Dizhi(迪志)</a> a Taiwan), mebook (mebook.cc, 我的小书屋, la mia piccola libreria — woz9ts: “Questo sito si concentra principalmente sulla condivisione di file ebook di alta qualità, alcuni dei quali impaginati dallo stesso proprietario. Il proprietario è stato <a %(a_arrested)s>arrestato</a> nel 2019 e qualcuno ha creato una raccolta dei file che ha condiviso.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "File rimanenti di DuXiu dal volontario “woz9ts”, che non erano nel formato proprietario PDG di DuXiu (ancora da convertire in PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Torrent dell'Archivio di Anna"

msgid "page.datasets.zlib.title"
msgstr "Scrape di Z-Library"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ha avuto origine dalla comunità di <a %(a_href)s>Library Genesis</a> e inizialmente è stata avviata con i loro dati. Da allora, si è notevolmente professionalizzata e presenta un'interfaccia molto più moderna. Pertanto, è in grado di ottenere molte più donazioni, sia monetarie per continuare a migliorare il sito web, sia di nuovi libri. Ha accumulato una raccolta di grandi dimensioni oltre a quella di Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Aggiornamento a febbraio 2023."

msgid "page.datasets.zlib.description.allegations"
msgstr "Alla fine del 2022, i presunti fondatori di Z-Library sono stati arrestati e i domini sono stati sequestrati dalle autorità degli Stati Uniti. Da allora il sito web ha lentamente ripreso a funzionare. Non si sa chi lo gestisca attualmente."

msgid "page.datasets.zlib.description.three_parts"
msgstr "La raccolta è composta da tre parti. Le pagine di descrizione originali per le prime due parti sono conservate di seguito. Sono necessarie tutte e tre le parti per ottenere tutti i dati (eccetto i torrent obsoleti, che sono barrati nella relativa pagina)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: la nostra prima release. È stata la primissima release di quello che allora si chiamava “Pirate Library Mirror” (“pilimi”)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: seconda release, questa volta con tutti i file in formato .tar."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: nuove release incrementali, utilizzando il formato <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, ora pubblicate in collaborazione con il team di Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrent dell'Archivio di Anna (metadati + contenuti)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Esempio di record nell'Archivio di Anna (raccolta originale)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Esempio di record nell'Archivio di Anna (raccolta “zlib3”)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Sito principale"

msgid "page.datasets.zlib.link.onion"
msgstr "Dominio Tor"

msgid "page.datasets.zlib.blog.release1"
msgstr "Post del blog sulla Release 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Post del blog sulla Release 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Release di Zlib (pagine di descrizione originali)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Release 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Il mirror iniziale è stato ottenuto con grande sforzo nel corso del 2021 e 2022. A questo punto è leggermente obsoleto: riflette lo stato della raccolta a giugno 2021. Lo aggiorneremo in futuro. Al momento siamo concentrati sulla pubblicazione di questa prima release."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Dal momento che Library Genesis è già preservata con torrent pubblici ed è inclusa in Z-Library, abbiamo effettuato una deduplicazione di base rispetto a Library Genesis a giugno 2022, utilizzando hash MD5. È probabile che ci siano molti altri contenuti duplicati nella biblioteca, ad esempio gli stessi libri in formati di file multipli. Questo è difficile da rilevare con precisione, quindi non lo facciamo. Dopo la deduplicazione, ci rimangono oltre 2 milioni di file, per un totale di poco meno di 7TB."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "La raccolta consiste di due parti: un dump MySQL “.sql.gz” dei metadati e i 72 file torrent di circa 50-100GB ciascuno. I metadati contengono i dati riportati dal sito web di Z-Library (titolo, autore, descrizione, tipo di file), così come la dimensione effettiva del file e l'md5sum che abbiamo osservato, poiché a volte questi non coincidono. Apparentemente ci sono intervalli di file per i quali la stessa Z-Library ha metadati errati. Potremmo anche aver scaricato file in modo errato in alcuni casi isolati, che cercheremo di rilevare e correggere in futuro."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "I file torrent di grandi dimensioni contengono i dati effettivi dei libri, con l'ID di Z-Library come nome del file. Le estensioni dei file possono essere ricostruite utilizzando il dump dei metadati."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "La raccolta è un mix di contenuti di saggistica e narrativa (non separati come in Library Genesis). Anche la qualità è molto variabile."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Questa prima versione è ora completamente disponibile. Nota che i file torrent sono disponibili solo attraverso il nostro mirror Tor."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Release 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Abbiamo ottenuto tutti i libri aggiunti alla Z-Library tra il nostro ultimo mirror e agosto 2022. Siamo anche tornati indietro e abbiamo recuperato alcuni libri che ci erano sfuggiti la prima volta. In tutto, questa nuova raccolta è di circa 24TB. Anche questa è deduplicata rispetto a Library Genesis, poiché ci sono già torrent disponibili per quella raccolta."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "I dati sono organizzati in modo simile alla prima versione. C'è un dump MySQL “.sql.gz” dei metadati, che include anche tutti i metadati della prima versione, e quindi la sostituisce. Abbiamo anche aggiunto alcune nuove colonne:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: se questo file è già in Library Genesis, sia nella raccolta di saggistica che in quella di narrativa (corrispondenza tramite md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in quale torrent si trova questo file."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: impostato quando non siamo riusciti a scaricare il libro."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Lo abbiamo già detto, ma a titolo di chiarimento: “filename” e “md5” sono le proprietà effettive del file, mentre “filename_reported” e “md5_reported” sono i dati recuperati da Z-Library. A volte non coincidono, quindi li abbiamo inclusi entrambi."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Per questa versione, abbiamo cambiato la collazione in “utf8mb4_unicode_ci”, che dovrebbe essere compatibile con le versioni meno recenti di MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "I file di dati sono simili all'ultima volta, anche se sono molto più grandi. Creare una quantità di file torrent più piccoli non aveva senso. “pilimi-zlib2-0-14679999-extra.torrent” contiene tutti i file che ci sono sfuggiti nell'ultima release, mentre gli altri torrent includono tutti nuovi intervalli di ID. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Aggiornamento %(date)s:</strong> La maggior parte dei nostri torrent erano troppo grandi e creavano difficoltà ai client. Li abbiamo rimossi e ne abbiamo rilasciati di nuovi."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Aggiornamento %(date)s:</strong> I file erano ancora troppo numerosi, quindi li abbiamo convertiti in formato tar e abbiamo di nuovo rilasciato nuovi torrent."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addendum release 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Questo è un singolo file torrent aggiuntivo. Non contiene nuove informazioni, ma ha alcuni dati il cui calcolo può richiedere del tempo. Questo lo rende comodo, poiché scaricare questo torrent è spesso più veloce che calcolarlo da zero. In particolare, contiene indici SQLite per i file tar, da utilizzare con <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Frequently Asked Questions (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Che cos'è l'Archivio di Anna?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Archive</span> è un progetto non-profit con due obiettivi:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Conservazione:</strong> Backup di tutta la conoscenza e cultura dell'umanità.</li><li><strong>Accessibilità:</strong> Rendere questa conoscenza e cultura disponibile a chiunque nel mondo.</li>"

msgid "page.home.intro.open_source"
msgstr "Tutti i nostri <a %(a_code)s>codici</a> e <a %(a_datasets)s>dati</a> sono completamente open source."

msgid "page.home.preservation.header"
msgstr "Conservazione"

msgid "page.home.preservation.text1"
msgstr "Conserviamo libri, documenti, fumetti, riviste e altro ancora, riunendo in un'unica posizione questi materiali provenienti da varie <a href=\"https://it.wikipedia.org/wiki/Biblioteca_ombra\">biblioteche-ombra</a>, biblioteche ufficiali e altre raccolte. Tutti questi dati vengono conservati per sempre rendendo facile la loro duplicazione in blocco (tramite i torrent), con il risultato di avere molte copie distribuite in tutto il mondo. Alcune biblioteche-ombra lo fanno già autonomamente (ad esempio Sci-Hub, Library Genesis), mentre l'Archivio di Anna ne \"libera\" altre che non offrono la distribuzione in blocco (ad esempio Z-Library) o non sono affatto biblioteche-ombra (ad esempio Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Questa ampia distribuzione, unita al codice open-source, rende il nostro sito web resistente agli attacchi e garantisce la conservazione a lungo termine della conoscenza e della cultura dell'umanità. Per saperne di più sui nostri <a href=\"/datasets\"> set di dati</a>."

msgid "page.home.preservation.label"
msgstr "Stimiamo di aver conservato circa <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% di tutti i libri esistenti al mondo</a>."

msgid "page.home.access.header"
msgstr "Accesso"

msgid "page.home.access.text"
msgstr "Lavoriamo con i nostri partner per rendere le nostre raccolte facilmente e liberamente accessibili a chiunque. Crediamo che tutti abbiano diritto alla saggezza collettiva dell'umanità. E <a %(a_search)s>non a spese degli autori</a>."

msgid "page.home.access.label"
msgstr "Download per ora negli ultimi 30 giorni. Media oraria: %(hourly)s. Media giornaliera: %(daily)s."

msgid "page.about.text2"
msgstr "Crediamo fermamente nella libera circolazione di informazioni e nella conservazione della conoscenza e della cultura. Con questo motore di ricerca, costruiamo su delle basi solide. Rispettiamo profondamente il duro lavoro delle persone che hanno creato le varie biblioteche ombra e speriamo che questo motore di ricerca possa ampliare la loro portata."

msgid "page.about.text3"
msgstr "Per restare al corrente dei nostri progressi, segui l'Archivio di Anna su <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o su <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Per domande o feedback, contatta Anna all'indirizzo email %(email)s."

msgid "page.faq.help.title"
msgstr "Come posso contribuire?"

msgid "page.about.help.text"
msgstr "<li>1. Seguici su <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Diffondi il verbo riguardo all'Archivio di Anna su Twitter/X, Reddit, TikTok, Instagram, nella tua università, a lavoro o in biblioteca. Sentiti libero/a di farlo ovunque tu vada! Non crediamo nel gatekeeping: se il nostro sito venisse oscurato, torneremmo online altrove, dal momento che il nostro codice sorgente è totalmente open source.</li><li>3. Se puoi, prendi in considerazione di supportarci tramite <a href=\"/donate\">una donazione</a>.</li><li>4. Aiutaci a <a href=\"https://translate.annas-software.org/\">tradurre</a> il nostro sito in varie lingue (sì, l'abbiamo tradotto anche in italiano!).</li><li>5. Se sei un ingegnere/a informatico/a, sarebbe fantastico se potessi contribuire al nostro codice sorgente <a href=\"https://annas-software.org/\">open source</a>, o eseguire il seeding dei nostri <a href=\"/datasets\">torrent</a>.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "Ora abbiamo anche un canale Matrix sincronizzato su %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Se sei un esperto/a di sicurezza informatica, possiamo sfruttare le tue competenze sia per scopi di attacco che di difesa. Dai un'occhiata alla nostra pagina <a %(a_security)s>Security</a>."

msgid "page.about.help.text7"
msgstr "7. Siamo alla ricerca di esperti/e in pagamenti anonimi. Potresti aiutarci ad aggiungere metodi di pagamento più convenienti per le donazioni? PayPal, WeChat, gift card. Se conosci qualcuno/a, per favore contattaci."

msgid "page.about.help.text8"
msgstr "8. Siamo sempre alla ricerca di server con maggiore capacità."

msgid "page.about.help.text9"
msgstr "9. Puoi aiutarci segnalandoci i problemi ai file, lasciandoci dei commenti e creando liste su questo sito web. Inoltre, puoi aiutarci anche <a %(a_upload)s>caricando dei libri</a> oppure sistemando le anomalie ai file o la formattazione dei libri attualmente presenti."

msgid "page.about.help.text10"
msgstr "10. Creare o aiutare a mantenere la pagina dell'Archivio di Anna su Wikipedia nella tua lingua."

msgid "page.about.help.text11"
msgstr "11. Stiamo cercando di inserire piccoli annunci pubblicitari \"di buon gusto\". Se volessi mettere un'inserzione pubblicitaria sul nostro sito, faccelo sapere."

msgid "page.faq.help.mirrors"
msgstr "Ci piacerebbe molto che le persone creassero degli <a %(a_mirrors)s>mirror</a> perché noi li sosterremmo finanziariamente."

msgid "page.about.help.volunteer"
msgstr "Per informazioni più dettagliate su come fare volontariato, consulta la nostra pagina <a %(a_volunteering)s>Volontariato & Ricompense</a>."

msgid "page.faq.slow.title"
msgstr "Perché i download lenti sono così lenti?"

msgid "page.faq.slow.text1"
msgstr "Non abbiamo letteralmente abbastanza risorse per offrire a tutti nel mondo download ad alta velocità, per quanto questo ci piacerebbe. Se un ricco benefattore volesse farsi avanti e fornircelo, sarebbe incredibile, ma fino ad allora, stiamo facendo del nostro meglio. Siamo un progetto senza scopo di lucro che riesce a malapena a sostenersi attraverso le donazioni."

msgid "page.faq.slow.text2"
msgstr "Ecco perché abbiamo implementato due sistemi per i download gratuiti, con i nostri partner: server condivisi con download lenti e server leggermente più veloci con una lista d'attesa (per ridurre il numero di persone che scaricano contemporaneamente)."

msgid "page.faq.slow.text3"
msgstr "Abbiamo anche <a %(a_verification)s>la verifica del browser</a> per i nostri download lenti, perché altrimenti i bot e gli scraper ne abuserebbero, rendendo le cose ancora più lente per gli utenti legittimi."

msgid "page.faq.slow.text4"
msgstr "Nota che, quando utilizzi il browser Tor, potresti dover regolare le tue impostazioni di sicurezza. Al livello più basso delle opzioni, chiamato “Standard”, la challenge del turnstile Cloudflare riesce. Ai livelli più alti, chiamati “Più sicuro” e “Massima sicurezza”, la challenge non riesce."

msgid "page.faq.slow.text5"
msgstr "A volte, per i file di grandi dimensioni, i download lenti possono interrompersi a metà. Consigliamo di utilizzare un gestore di download (come JDownloader) per riprendere automaticamente i download di grandi dimensioni."

msgid "page.donate.faq.title"
msgstr "FAQ Donazioni"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Gli abbonamenti si rinnovano automaticamente?</div> Gli abbonamenti <strong>non</strong> si rinnovano automaticamente. Puoi rimanere abbonato/a quanto a lungo vuoi."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Posso aggiornare il mio abbonamento oppure ottenere più abbonamenti?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Avete altri metodi di pagamento?</div> Attualmente no. Molte persone non vogliono che archivi come questo esistano quindi bisogna essere cauti. Se puoi aiutarci a inserire altri metodi di pagamento (più convenienti) in sicurezza, per favore contattaci a %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Cosa significano gli intervalli per mese?</div> Puoi arrivare all'estremità inferiore di un intervallo applicando tutti gli sconti, ad esempio scegliendo un periodo più lungo di un mese."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Per cosa spendete i fondi delle donazioni?</div> Il 100%% è speso nel preservare e rendere accessibile la conoscenza e la cultura di tutto il mondo. Al momento la gran parte delle spese sono destinate a server, memoria e banda. Nessuna parte dei fondi va personalmente ad alcun membro del team."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Posso fare una grossa donazione?</div> Sarebbe fantastico! Per donazioni oltre qualche migliaio di dollari, per favore contattaci direttamente via email a %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Posso fare una donazione senza diventare membro?</div> Certamente. Accettiamo donazioni di qualsiasi importo a questo indirizzo Monero (XMR): %(address)s."

msgid "page.faq.upload.title"
msgstr "Come posso caricare nuovi libri?"

msgid "page.upload.zlib.text1"
msgstr "In alternativa, puoi caricarli su Z-Library <a %(a_upload)s>qui</a>."

msgid "page.upload.upload_to_both"
msgstr "Piccoli upload (fino a 10.000 file): carica sia su %(first)s sia su %(second)s."

msgid "page.upload.text1"
msgstr "Per il momento, ti consigliamo di caricare nuovi libri su un fork di Library Genesis. Ecco una <a %(a_guide)s>guida pratica</a>. Nota: entrambi i fork che vengono indicizzati su questo sito si basano sullo stesso sistema di caricamenti."

msgid "page.upload.libgenli_login_instructions"
msgstr "Per Libgen.li, assicurati di eseguire prima il login sul <a %(a_forum)s>forum del sito</a> con nome utente %(username)s e password %(password)s, e poi torna alla <a %(a_upload_page)s>pagina di upload</a>."

msgid "common.libgen.email"
msgstr "Se il tuo indirizzo email non funziona sui forum Libgen, ti consigliamo di usare <a %(a_mail)s>Proton Mail</a> (gratuito). Puoi anche <a %(a_manual)s>richiedere manualmente</a> l'attivazione del tuo account."

msgid "page.faq.mhut_upload"
msgstr "Nota che mhut.org blocca alcune serie di IP, quindi potrebbe essere necessario un VPN."

msgid "page.upload.large.text"
msgstr "Per il caricamento di grandi quantità di file (più di 10.000 file) che non vengono accettati da Libgen o Z-Library, per favore scrivici a %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Per caricare articoli accademici, per favore (oltre a Library Genesis) caricali anche su <a %(a_stc_nexus)s>STC Nexus</a>. Sono la migliore biblioteca ombra per nuovi articoli. Non li abbiamo ancora integrati, ma lo faremo prima o poi. Puoi usare il loro <a %(a_telegram)s>bot di caricamento su Telegram</a>, o contattare l'indirizzo elencato nel loro messaggi fissati se hai troppi file da caricare in questo modo."

msgid "page.faq.request.title"
msgstr "Come posso richiedere il caricamento di nuovi libri?"

msgid "page.request.cannot_accomodate"
msgstr "Al momento purtroppo non riusciamo a gestire le richieste di nuovi libri."

msgid "page.request.forums"
msgstr "Per favore, formula le tue richieste nei forum Z-Library o Libgen."

msgid "page.request.dont_email"
msgstr "Non scriverci un'email per effettuare una richiesta di nuovi libri."

msgid "page.faq.metadata.title"
msgstr "Raccogliete metadati?"

msgid "page.faq.metadata.indeed"
msgstr "Certamente."

msgid "page.faq.1984.title"
msgstr "Ho scaricato 1984 di George Orwell, la polizia verrà a casa mia?"

msgid "page.faq.1984.text"
msgstr "Non preoccuparti troppo, ci sono molte persone che scaricano dai siti web collegati da noi, ed è estremamente raro avere problemi. Tuttavia, per rimanere al sicuro, consigliamo di utilizzare un VPN (a pagamento), o <a %(a_tor)s>Tor</a> (gratuito)."

msgid "page.faq.save_search.title"
msgstr "Come salvo le mie impostazioni di ricerca?"

msgid "page.faq.save_search.text1"
msgstr "Seleziona le impostazioni che preferisci, lascia la casella di ricerca vuota, clicca su “Cerca”, e poi aggiungi la pagina ai preferiti utilizzando la funzione segnalibro del tuo browser."

msgid "page.faq.mobile.title"
msgstr "Avete un'app mobile?"

msgid "page.faq.mobile.text1"
msgstr "Non abbiamo un'app mobile ufficiale, ma puoi installare questo sito web come app."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Clicca sul menu a tre puntini in alto a destra e seleziona “Aggiungi a schermata Home”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Clicca sul pulsante “Condividi” in basso e seleziona “Aggiungi a schermata Home”."

msgid "page.faq.api.title"
msgstr "Avete un'API?"

msgid "page.faq.api.text1"
msgstr "Abbiamo un'API JSON stabile per i membri, per ottenere un URL di download veloce: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentazione all'interno del JSON stesso)."

msgid "page.faq.api.text2"
msgstr "Per altri casi d'uso, come iterare attraverso tutti i nostri file, costruire ricerche personalizzate, e così via, consigliamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. I dati raw possono essere esplorati manualmente <a %(a_explore)s>attraverso i file JSON</a>."

msgid "page.faq.api.text3"
msgstr "La nostra lista di torrent raw può essere scaricata anche come <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "FAQ Torrent"

msgid "page.faq.torrents.q1"
msgstr "Vorrei aiutare a fare seeding, ma non ho molto spazio su disco."

msgid "page.faq.torrents.a1"
msgstr "Utilizza il <a %(a_list)s>generatore di liste torrent</a> per generare una lista di torrent che hanno più bisogno di torrenting, entro i limiti del tuo spazio di archiviazione."

msgid "page.faq.torrents.q2"
msgstr "I torrent sono troppo lenti; posso scaricare i dati direttamente da voi?"

msgid "page.faq.torrents.a2"
msgstr "Sì, consulta la pagina <a %(a_llm)s>LLM data</a>."

msgid "page.faq.torrents.q3"
msgstr "Posso scaricare solo un sottoinsieme dei file, come solo una particolare lingua o argomento?"

msgid "page.faq.torrents.a3_short_answer"
msgstr "Risposta breve: non con facilità."

msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Risposta lunga:"

msgid "page.faq.torrents.a3"
msgstr "La maggior parte dei torrent contiene direttamente i file, il che significa che puoi indicare ai client torrent di scaricare solo i file desiderati. Per determinare quali file scaricare, puoi <a %(a_generate)s>generare</a> i nostri metadati, oppure <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Purtroppo, una parte delle raccolte di torrent contiene file .zip o .tar in radice; in questo caso devi prima scaricare l'intero torrent per poter selezionare i singoli file."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Ma abbiamo <a %(a_ideas)s>alcune idee</a> per quest'ultimo caso)"

msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Non esistono ancora strumenti di facile utilizzo per filtrare i torrent, ma accogliamo con piacere qualsiasi contributo."

msgid "page.faq.torrents.q4"
msgstr "Come gestite i duplicati nei torrent?"

msgid "page.faq.torrents.a4"
msgstr "Cerchiamo di mantenere al minimo la duplicazione o la sovrapposizione tra i torrent in questo elenco, ma questo non è sempre possibile e dipende molto anche dalle politiche delle biblioteche di origine. Per le biblioteche che pubblicano i propri torrent, è fuori dal nostro controllo. Per i torrent rilasciati dall'Archivio di Anna, deduplichiamo solo in base all'hash MD5, il che significa che versioni diverse dello stesso libro non vengono deduplicate."

msgid "page.faq.torrents.q5"
msgstr "Posso ottenere la lista dei torrent in formato JSON?"

msgid "page.faq.torrents.a5"
msgstr "Sì."

msgid "page.faq.torrents.q6"
msgstr "Non vedo i PDF o EPUB nei torrent, solo file binari. Cosa devo fare?"

msgid "page.faq.torrents.a6"
msgstr "Si tratta in realtà PDF ed EPUB, semplicemente in molti dei nostri torrent non hanno un'estensione. Ci sono due posizioni in cui puoi trovare i metadati per i file torrent, inclusi i tipi/estensioni dei file:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Ogni raccolta o release ha i propri metadati. Ad esempio, i <a %(a_libgen_nonfic)s>torrent di Libgen.rs</a> hanno un database di metadati corrispondenti in hosting sul sito di Libgen.rs. Solitamente colleghiamo le risorse dei metadati rilevanti dalla <a %(a_datasets)s>pagina del dataset</a> di ciascuna raccolta."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Raccomandiamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Questi contengono una mappatura per ogni record in Anna’s Archive ai suoi file torrent corrispondenti (se disponibili), sotto \"torrent_paths\" nel JSON di ElasticSearch."

msgid "page.faq.torrents.q7"
msgstr "Perché il mio client torrent non riesce ad aprire alcuni dei vostri file torrent/link magnet?"

msgid "page.faq.torrents.a7"
msgstr "Alcuni client torrent non supportano elementi di grandi dimensioni, come molti dei nostri torrent (per quelli più recenti lo evitiamo, anche se è un comportamento valido in base alle specifiche!). Quindi prova a utilizzare un client diverso se riscontri questo problema, o fallo presente agli sviluppatori del tuo client."

msgid "page.faq.security.title"
msgstr "Avete un programma di divulgazione responsabile?"

msgid "page.faq.security.text1"
msgstr "Accogliamo con piacere esperti di sicurezza che cercano vulnerabilità nei nostri sistemi. Siamo grandi sostenitori della divulgazione responsabile. Contattaci <a %(a_contact)s>qui</a>."

msgid "page.faq.security.text2"
msgstr "Attualmente non siamo in grado di assegnare ricompense per bug, tranne per le vulnerabilità che hanno il <a %(a_link)s>potenziale di compromettere il nostro anonimato</a>, per le quali offriamo ricompense nell'ordine di $10k-50k. Vorremmo offrire un più vasto campo d'azione per le ricompense per bug in futuro! Si prega di notare che gli attacchi di ingegneria sociale non rientrano nell'ambito."

msgid "page.faq.security.text3"
msgstr "Se sei interessato alla sicurezza offensiva e vuoi aiutare ad archiviare la conoscenza e la cultura del mondo, assicurati di contattarci. Ci sono molti modi in cui puoi aiutare."

msgid "page.faq.resources.title"
msgstr "Ci sono altre risorse sull'Archivio di Anna?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog di Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — aggiornamenti regolari"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software di Anna</a> — il nostro codice open source"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traduci su Software di Anna</a> — il nostro sistema di traduzione"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — informazioni sui dati"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domini alternativi"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — dove troverai maggiori informazioni su di noi (per favore aiuta a mantenere aggiornata la pagina o creane una nella tua lingua!)"

msgid "page.faq.copyright.title"
msgstr "Come posso segnalare una violazione del copyright?"

msgid "page.faq.copyright.text1"
msgstr "Non ospitiamo materiale protetto da copyright. Siamo un motore di ricerca e, in quanto tale, indicizziamo solo i metadati che sono già disponibili pubblicamente. Quando scarichi da queste fonti esterne, ti suggeriamo di controllare le leggi nella tua giurisdizione riguardo a ciò che è consentito e non. Non siamo responsabili per i contenuti in hosting da altri."

msgid "page.faq.copyright.text2"
msgstr "Se hai reclami su ciò che vedi qui, la tua opzione migliore è contattare il sito web originale. Regolarmente aggiorniamo il nostro database con le loro modifiche. Se pensi davvero di avere un reclamo DMCA valido a cui dovremmo rispondere, compila il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>. Prendiamo i reclami seriamente e risponderemo il prima possibile."

msgid "page.faq.hate.title"
msgstr "Odio come state gestendo questo progetto!"

msgid "page.faq.hate.text1"
msgstr "Vorremmo anche ricordare a tutti che tutto il nostro codice e i nostri dati sono completamente open source. Questo è unico per progetti come il nostro — non siamo a conoscenza di nessun altro progetto con un catalogo altrettanto massiccio che sia completamente open source. Invitiamo con molto piacere chiunque pensi che stiamo gestendo male il nostro progetto a prendere il nostro codice ed i nostri dati a creare la propria biblioteca ombra! Non lo diciamo per dispetto o altro — pensiamo sinceramente che sarebbe fantastico poiché alzerebbe il livello per tutti e preserverebbe meglio l'eredità dell'umanità."

msgid "page.faq.uptime.title"
msgstr "Hai un monitor di uptime?"

msgid "page.faq.uptime.text1"
msgstr "Fai riferimento a <a %(a_href)s>questo eccellente progetto</a>."

msgid "page.faq.physical.title"
msgstr "Come posso donare libri o altri materiali fisici?"

msgid "page.faq.physical.text1"
msgstr "Puoi inviarli a <a %(a_archive)s>Internet Archive</a>, che provvederà a preservarli."

msgid "page.faq.anna.title"
msgstr "Chi è Anna?"

msgid "page.faq.anna.text1"
msgstr "Tu sei Anna!"

msgid "page.faq.favorite.title"
msgstr "Quali sono i vostri libri preferiti?"

msgid "page.faq.favorite.text1"
msgstr "Ecco alcuni libri che hanno un significato speciale per il mondo delle biblioteche ombra e della conservazione digitale:"

msgid "page.fast_downloads.no_more_new"
msgstr "Hai terminato i tuoi download rapidi per oggi."

msgid "page.fast_downloads.no_member"
msgstr "Iscriviti per usufruire dei download veloci."

msgid "page.fast_downloads.no_member_2"
msgstr "Ora supportiamo carte regalo Amazon, carte di credito e debito, criptovaluta, Alipay e WeChat."

msgid "page.home.full_database.header"
msgstr "Database completo"

msgid "page.home.full_database.subtitle"
msgstr "Libri, pubblicazioni, riviste, fumetti, archivi delle biblioteche, metadati, …"

msgid "page.home.full_database.search"
msgstr "Cerca"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ha <a %(a_paused)s>sospeso</a> il caricamento di nuovi paper."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB è la continuazione di Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Accesso diretto ai %(count)s paper accademici"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Apri"

msgid "page.home.scidb.browser_verification"
msgstr "Se sei già <a %(a_member)s>iscritto/a</a>, non è richiesta la verifica via browser."

msgid "page.home.archive.header"
msgstr "Archivio a lungo termine"

msgid "page.home.archive.body"
msgstr "I set di dati utilizzati nell'Archivio di Anna sono completamente aperti e possono essere sottoposti a mirroring in blocco utilizzando i torrent. <a %(a_datasets)s>Maggiori informazioni…</a>"

msgid "page.home.torrents.body"
msgstr "Puoi aiutarci immensamente facendo il seeding dei torrent. <a %(a_torrents)s>Maggiori informazioni…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeder"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeder"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeder"

msgid "page.home.llm.header"
msgstr "Dati di addestramento LLM"

msgid "page.home.llm.body"
msgstr "Abbiamo la più grande raccolta al mondo di dati testuali in alta qualità.<a %(a_llm)s>Maggiori informazioni…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Mirror: call per volontari/ie"

msgid "page.home.volunteering.header"
msgstr "🤝 Cerchiamo volontari"

msgid "page.home.volunteering.help_out"
msgstr "Come progetto open-source senza scopo di lucro, siamo sempre alla ricerca di persone che ci aiutino."

msgid "page.home.payment_processor.body"
msgstr "Se gestisci un sistema di pagamento anonimo ad alto rischio, contattaci. Siamo anche alla ricerca di persone che vogliano pubblicare piccoli annunci \"di buon gusto\". Tutti i proventi sono destinati al nostro impegno per la conservazione e tutela del sapere."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog di Anna ↗"

msgid "page.ipfs_downloads.title"
msgstr "Download IPFS"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Tutti i link per scaricare questo file</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Gateway IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(potrebbe essere necessario provare più volte con IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Per ottenere download rapidi e saltare il controllo del browser, <a %(a_membership)s>iscriviti</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Per il mirroring in blocco della nostra raccolta, vai alle pagine <a %(a_datasets)s>Dataset</a> e <a %(a_torrents)s>Torrent</a>."

msgid "page.llm.title"
msgstr "Dati LLM"

msgid "page.llm.intro"
msgstr "È ben noto che gli LLM funzionano bene a partire da dati di alta qualità. Abbiamo la più grande raccolta di libri, articoli, riviste, ecc. al mondo, che sono alcune delle fonti testuali di qualità più elevata."

msgid "page.llm.unique_scale"
msgstr "Scala e ampiezza uniche"

msgid "page.llm.unique_scale.text1"
msgstr "La nostra raccolta contiene oltre cento milioni di file, inclusi riviste accademiche e di altro tipo e libri di testo. Otteniamo questa scala combinando repository esistenti di grandi dimensioni."

msgid "page.llm.unique_scale.text2"
msgstr "Alcune delle nostre raccolte di origine sono già disponibili in blocco (Sci-Hub e parti di Libgen). Altre fonti le abbiamo liberate noi stessi. <a %(a_datasets)s>Dataset</a> mostra una panoramica completa."

msgid "page.llm.unique_scale.text3"
msgstr "La nostra raccolta include milioni di libri, articoli e riviste precedenti all'epoca degli e-book. Parti importanti di questa raccolta sono già state sottoposte a OCR e hanno già poca sovrapposizione interna."

msgid "page.llm.how_we_can_help"
msgstr "Come possiamo aiutare"

msgid "page.llm.how_we_can_help.text1"
msgstr "Siamo in grado di fornire accesso ad alta velocità alle nostre raccolte complete, così come a quelle non ancora pubblicate."

msgid "page.llm.how_we_can_help.text2"
msgstr "Un accesso a livello aziendale che possiamo fornire in cambio di donazioni nell'ordine di decine di migliaia di USD, oppure di raccolte di alta qualità di cui ancora non disponiamo."

msgid "page.llm.how_we_can_help.text3"
msgstr "Possiamo ricompensarti se sei in grado di arricchire i nostri dati, ad esempio nei modi che seguono:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Rimozione delle sovrapposizioni (deduplicazione)"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Estrazione di testo e metadati"

msgid "page.llm.how_we_can_help.text4"
msgstr "Supporta l'archiviazione a lungo termine della conoscenza umana, e al contempo ottieni dati migliori per il tuo modello!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contattaci</a> per discutere di una possibile collaborazione."

msgid "page.login.continue"
msgstr "Continua"

msgid "page.login.please"
msgstr "Per favore, effettua il <a %(a_account)slogin</a> per vedere questa pagina.</a>"

msgid "page.maintenance.header"
msgstr "L'Archivio di Anna è temporaneamente fuori servizio per manutenzione. Si prega di tornare tra un'ora."

msgid "page.metadata.header"
msgstr "Migliora i metadati"

msgid "page.metadata.body1"
msgstr "Puoi contribuire alla conservazione dei libri migliorando i metadati! Prima, leggi le informazioni di base sui metadati nell'Archivio di Anna, impara come migliorare i metadati collegandoli con Open Library, e guadagna un abbonamento gratuito all'Archivio di Anna."

msgid "page.metadata.background.title"
msgstr "Informazioni di base"

msgid "page.metadata.background.body1"
msgstr "Quando consulti un libro nell'Archivio di Anna, puoi vedere vari campi: titolo, autore, editore, edizione, anno, descrizione, nome del file e altro. Tutte queste informazioni sono chiamate <em>metadati</em>."

msgid "page.metadata.background.body2"
msgstr "Poiché combiniamo libri da varie <em>biblioteche di origine</em>, mostriamo qualsiasi metadato sia disponibile in quella biblioteca di origine. Ad esempio, per un libro che abbiamo ottenuto da Library Genesis, mostreremo il titolo dal database di Library Genesis."

msgid "page.metadata.background.body3"
msgstr "A volte un libro è presente in <em>più</em> biblioteche di origine, che potrebbero avere campi di metadati diversi. In tal caso, mostriamo semplicemente la versione più lunga di ciascun campo, che sperabilmente includerà le informazioni più utili. Mostriamo comunque gli altri campi sotto la descrizione, ad esempio come \"titolo alternativo\" (ma solo se sono diversi)."

msgid "page.metadata.background.body4"
msgstr "Estraiamo anche <em>codici</em> come gli identificatori e classificatori dalla biblioteca di origine. <em>Gli identificatori</em> rappresentano in modo univoco una particolare edizione di un libro; esempi sono ISBN, DOI, Open Library ID, Google Books ID o Amazon ID. <em>I classificatori</em> raggruppano insieme più libri simili; esempi sono Dewey Decimal (DCC), UDC, LCC, RVK o GOST. A volte questi codici sono esplicitamente collegati nelle biblioteche di origine, e a volte possiamo estrarli dal nome del file o dalla descrizione (principalmente ISBN e DOI)."

msgid "page.metadata.background.body5"
msgstr "Possiamo usare gli identificatori per trovare record in <em>raccolte di soli metadati</em>, come OpenLibrary, ISBNdb o WorldCat/OCLC. C'è una specifica <em>scheda dei metadati</em> nel nostro motore di ricerca se desideri sfogliare quelle raccolte. Utilizziamo i record corrispondenti per compilare i campi di metadati mancanti (ad esempio, se manca un titolo), o ad esempio come \"titolo alternativo\" (se un titolo è presente)."

msgid "page.metadata.background.body6"
msgstr "Per vedere esattamente da dove provengono i metadati di un libro, consulta la <em>scheda \"Dettagli tecnici\"</em> sulla pagina di un libro. In essa è presente un link al file JSON non elaborato di quel libro, con collegamenti al file JSON non elaborato dei record originali."

msgid "page.metadata.background.body7"
msgstr "Per ulteriori informazioni, consulta le seguenti pagine: <a %(a_datasets)s>Dataset</a>, <a %(a_search_metadata)s>Ricerca (scheda metadati)</a>, <a %(a_codes)s>Esploratore di codici</a> e <a %(a_example)s>Esempio di metadati JSON</a>. Infine, tutti i nostri metadati possono essere <a %(a_generated)s>generati</a> o <a %(a_downloaded)s>scaricati</a> come database ElasticSearch e MariaDB."

msgid "page.metadata.openlib.title"
msgstr "Collegamento con Open Library"

msgid "page.metadata.openlib.body1"
msgstr "Quindi, se trovi un file con metadati errati, cosa dovresti fare per correggerlo? Puoi fare riferimento alla biblioteca di origine e seguire le loro procedure per la correzione dei metadati, ma cosa fare se un file è presente in più biblioteche di origine?"

msgid "page.metadata.openlib.body2"
msgstr "C'è un identificatore che è trattato in modo speciale nell'Archivio di Anna’. <strong>Il campo annas_archive md5 su Open Library sovrascrive sempre tutti gli altri metadati!</strong> Per prima cosa è bene fare un passo indietro e imparare a conoscere Open Library."

msgid "page.metadata.openlib.body3"
msgstr "Open Library è stata fondata nel 2006 da Aaron Swartz con l'obiettivo di creare \"una pagina web per ogni libro mai pubblicato\". È una sorta di Wikipedia per i metadati dei libri: chiunque può modificarla, è in licenza libera e può essere scaricata in blocco. È un database di libri che è più allineato con la nostra missione: infatti, l'Archivio di Anna è ispirato alla visione e alla vita di Aaron Swartz."

msgid "page.metadata.openlib.body4"
msgstr "Invece di reinventare la ruota, abbiamo deciso di indirizzare i nostri volontari verso Open Library. Se noti un libro con metadati errati, puoi aiutare nel seguente modo:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Collegati al <a %(a_openlib)s>sito web di Open Library</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Trova il record corretto del libro. <strong>ATTENZIONE:</strong> assicurati di selezionare la <strong>edizione</strong> corretta. In Open Library, ci sono \"opere\" ed \"edizioni\"."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Un'\"opera\" potrebbe essere \"Harry Potter e la Pietra Filosofale\"."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Un'\"edizione\" potrebbe essere:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "La prima edizione del 1997 pubblicata da Bloomsbury di 256 pagine."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "L'edizione tascabile del 2003 di 223 pagine, pubblicata da Raincoast Books."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "La traduzione polacca del 2000, di 328 pagine, “Harry Potter I Kamie Filozoficzn” di Media Rodzina."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Tutte queste edizioni hanno ISBN e contenuti diversi, quindi assicurati di selezionare quella giusta!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Modifica il record (o crealo se non esiste) e aggiungi quante più informazioni utili possibile! Intanto che ci sei, approfittane per rendere questo record davvero completo."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Sotto “ID Numbers” seleziona “Anna's Archive” e aggiungi l'MD5 del libro dall'Archivio di Anna. Si tratta della lunga stringa di lettere e numeri dopo “/md5/” nell'URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Cerca di trovare altri file nell'Archivio di Anna che corrispondano a questo record e aggiungi anche quelli. In futuro potremo raggrupparli come duplicati nella pagina di ricerca dell'Archivio di Anna."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando hai finito, annota l'URL che hai appena aggiornato. Una volta che hai aggiornato almeno 30 record con gli MD5 dell'Archivio di Anna, inviaci una <a %(a_contact)s>email</a> con l'elenco. Ti forniremo un abbonamento gratuito all'Archivio di Anna, così potrai svolgere questa attività più facilmente (e come ringraziamento per il tuo aiuto). Le modifiche devono essere di alta qualità e aggiungere una quantità sostanziale di informazioni, altrimenti la tua richiesta verrà respinta. La tua richiesta verrà respinta anche se una delle tue modifiche viene annullata o corretta dai moderatori di Open Library."

msgid "page.metadata.openlib.body5"
msgstr "Nota che questo vale solo per i libri, non per articoli accademici o altri tipi di file. Per altri tipi di file consigliamo comunque di trovare la biblioteca di origine. Potrebbero volerci alcune settimane prima che le modifiche vengano incluse nell'Archivio di Anna, poiché dobbiamo scaricare l'ultimo dump di dati di Open Library e rigenerare il nostro indice di ricerca."

msgid "page.mirrors.title"
msgstr "Mirror: chiamata per volontari e volontarie"

msgid "page.mirrors.intro"
msgstr "Per aumentare la resilienza dell'Archivio di Anna, stiamo cercando volontari e volontarie per gestire i mirror."

msgid "page.mirrors.text1"
msgstr "Ecco cosa cerchiamo:"

msgid "page.mirrors.list.run_anna"
msgstr "Gestisci il codice open source dell'Archivio di Anna e aggiorni regolarmente sia il codice che i dati."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "La tua versione è chiaramente indicata come mirror, ad esempio “Archivio di Bob, un mirror dell'Archivio di Anna”."

msgid "page.mirrors.list.know_the_risks"
msgstr "Sei disponibile ad assumerti i rischi associati a questo lavoro, che sono significativi. Hai una profonda comprensione della sicurezza operativa richiesta. Il contenuto di <a %(a_shadow)s>questi</a> <a %(a_pirate)s>post</a> ti è chiaro ed evidente."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "A questo scopo, sei disponibile a contribuire al nostro <a %(a_codebase)s>codice sorgente</a>, in collaborazione con il nostro team."

msgid "page.mirrors.list.maybe_partner"
msgstr "Inizialmente non ti daremo accesso ai download del nostro server partner, ma se le cose andranno bene, lo condivideremo con te."

msgid "page.mirrors.expenses.title"
msgstr "Spese di hosting"

msgid "page.mirrors.expenses.text1"
msgstr "Siamo disponibili a coprire le spese di hosting e VPN, inizialmente fino a $200 al mese. Questo è sufficiente per un server di ricerca di base e un proxy protetto da DMCA."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Pagheremo per l'hosting solo una volta che avrai tutto configurato e avrai dimostrato di essere in grado di mantenere l'archivio aggiornato. Questo significa che dovrai pagare di tasca tua per i primi 1-2 mesi."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Non riceverai un compenso per il tuo tempo (così come non lo riceviamo noi), poiché si tratta di puro volontariato."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Se il tuo coinvolgimento nello sviluppo e nel funzionamento del nostro lavoro diventa significativo, possiamo discutere la condivisione con te di una quota maggiore delle donazioni, perché tu le utilizzi secondo necessità."

msgid "page.mirrors.getting_started.title"
msgstr "Come iniziare"

msgid "page.mirrors.getting_started.text1"
msgstr "Per favore <strong>non contattarci</strong> per chiederci l'autorizzazione a cominciare, o per domande di base. Un'azione parla più di mille parole! Tutte le informazioni necessarie sono disponibili, quindi procedi pure con l'impostazione del tuo mirror."

msgid "page.mirrors.getting_started.text2"
msgstr "Non esitare a pubblicare ticket o richieste di merge sul nostro Gitlab quando incontri problemi. Potremmo dover costruire alcune funzionalità specifiche per il mirror con te, come il rebranding da “L'Archivio di Anna” al nome del tuo sito web, disabilitare gli account utente (inizialmente), o creare collegamenti dalle pagine dei libri al nostro sito principale."

msgid "page.mirrors.getting_started.text3"
msgstr "Una volta che il tuo mirror è attivo, contattaci. Vorremo rivedere la tua OpSec, e una volta che sarà solida, collegheremo il tuo mirror e inizieremo a collaborare più da vicino."

msgid "page.mirrors.getting_started.text4"
msgstr "Grazie in anticipo a chiunque sia disposto a contribuire in questo modo! Non è per i deboli di cuore, ma consoliderebbe la longevità della più grande biblioteca veramente aperta nella storia umana."

msgid "page.partner_download.header"
msgstr "Scarica dal sito web del partner"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ I download lenti sono disponibili solo attraverso il sito ufficiale. Visita %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ I download lenti non sono disponibili attraverso le VPN Cloudflare o dagli indirizzi IP Cloudflare."

msgid "page.partner_download.wait_banner"
msgstr "Attendi <span %(span_countdown)s>%(wait_seconds)s</span> secondi per scaricare questo file."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Scarica ora</a>"

msgid "page.partner_download.li4"
msgstr "Grazie per l'attesa, questo mantiene il sito accessibile gratuitamente per tutti! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Attenzione: nelle ultime 24 ore sono stati effettuati molti download dal tuo indirizzo IP. I download potrebbero essere più lenti del solito."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Download dal tuo indirizzo IP nelle ultime 24 ore: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Se stai usando una VPN, una connessione internet condivisa, o il tuo ISP condivide gli IP, questo avviso potrebbe essere dovuto a ciò."

msgid "page.partner_download.wait"
msgstr "Per dare a tutte le persone l'opportunità di scaricare file gratuitamente, devi attendere prima di poter scaricare questo file."

msgid "page.partner_download.li1"
msgstr "Non esitare a continuare a navigare all'interno dell'Archivio di Anna utilizzando un'altra scheda mentre aspetti (se il tuo browser supporta l'aggiornamento delle schede in background)."

msgid "page.partner_download.li2"
msgstr "Sentiti libero di aspettare che più pagine di download si carichino contemporaneamente (ma per favore scarica solo un file alla volta per server)."

msgid "page.partner_download.li3"
msgstr "Una volta ottenuto un link di download, questo è valido per diverse ore."

msgid "layout.index.header.title"
msgstr "Anna’s Archive"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Record in Anna’s Archive"

msgid "page.scidb.download"
msgstr "Scarica"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Per supportare l'accessibilità e la conservazione a lungo termine della conoscenza umana, diventa un <a %(a_donate)s>membro</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "Come bonus, 🧬&nbsp;SciDB si carica più velocemente per i membri, senza alcun limite."

msgid "page.scidb.refresh"
msgstr "Non funziona? Prova ad <a %(a_refresh)s>aggiornare</a>."

msgid "page.scidb.no_preview_new"
msgstr "Nessuna anteprima disponibile al momento. Scarica il file dall'<a %(a_path)s>Archivio di Anna</a>."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB è una continuazione di Sci-Hub, con la sua interfaccia familiare e la visualizzazione diretta dei PDF. Inserisci il tuo DOI per visualizzare."

msgid "page.home.scidb.text3"
msgstr "Abbiamo l'intera raccolta di Sci-Hub, oltre a nuovi articoli. La maggior parte può essere visualizzata direttamente con un'interfaccia familiare, simile a Sci-Hub. Alcuni possono essere scaricati tramite fonti esterne; in quel caso, visualizziamo i link relativi."

msgid "page.search.title.results"
msgstr "%(search_input)s - Cerca"

msgid "page.search.title.new"
msgstr "Nuova ricerca"

msgid "page.search.icon.include_only"
msgstr "Includi solo"

msgid "page.search.icon.exclude"
msgstr "Escludi"

msgid "page.search.icon.unchecked"
msgstr "Non verificato"

msgid "page.search.tabs.download"
msgstr "Scarica"

msgid "page.search.tabs.journals"
msgstr "Articoli di giornale"

msgid "page.search.tabs.digital_lending"
msgstr "Prestito digitale"

msgid "page.search.tabs.metadata"
msgstr "Metadati"

msgid "common.search.placeholder"
msgstr "Titolo, autore, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Cerca"

msgid "page.search.search_settings"
msgstr "Impostazioni di ricerca"

msgid "page.search.submit"
msgstr "Cerca"

msgid "page.search.too_long_broad_query"
msgstr "La ricerca ha richiesto troppo tempo, cosa comune per le query ampie. Il conteggio dei filtri potrebbe non essere accurato."

msgid "page.search.too_inaccurate"
msgstr "La ricerca ha richiesto troppo tempo, il che significa che i risultati potrebbero essere imprecisi. A volte può servire <a %(a_reload)s></a> ricaricare la pagina."

msgid "page.search.filters.display.header"
msgstr "Visualizza"

msgid "page.search.filters.display.list"
msgstr "Elenco"

msgid "page.search.filters.display.table"
msgstr "Tabella"

msgid "page.search.advanced.header"
msgstr "Avanzato"

msgid "page.search.advanced.description_comments"
msgstr "Cerca descrizioni e commenti ai metadati"

msgid "page.search.advanced.add_specific"
msgstr "Aggiungi un campo di ricerca specifico"

msgid "common.specific_search_fields.select"
msgstr "(ricerca in un campo specifico)"

msgid "page.search.advanced.field.year_published"
msgstr "Anno di pubblicazione"

msgid "page.search.filters.content.header"
msgstr "Contenuto"

msgid "page.search.filters.filetype.header"
msgstr "Tipo di file"

msgid "page.search.more"
msgstr "altro…"

msgid "page.search.filters.access.header"
msgstr "Accesso"

msgid "page.search.filters.source.header"
msgstr "Fonte"

msgid "page.search.filters.source.scraped"
msgstr "Scovato e reso open-source da AA"

msgid "page.search.filters.language.header"
msgstr "Lingua"

msgid "page.search.filters.order_by.header"
msgstr "Ordina per"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Più rilevanti"

msgid "page.search.filters.sorting.newest"
msgstr "Più recente"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(anno di pubblicazione)"

msgid "page.search.filters.sorting.oldest"
msgstr "Meno recente"

msgid "page.search.filters.sorting.largest"
msgstr "Più grande"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(dimensione del file)"

msgid "page.search.filters.sorting.smallest"
msgstr "Più piccolo"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(reso open source)"

msgid "page.search.filters.sorting.random"
msgstr "Casuale"

msgid "page.search.header.update_info"
msgstr "L'indice di ricerca è aggiornato mensilmente. Al momento include voci fino al %(last_data_refresh_date)s. Per maggiori informazioni tecniche , consulta la %(link_open_tag)spagina dei dataset</a>."

msgid "page.search.header.codes_explorer"
msgstr "Per esplorare l'indice di ricerca tramite codici, utilizza il <a %(a_href)s>Codes Explorer</a>."

msgid "page.search.results.search_downloads"
msgstr "Digita nella casella per cercare nel nostro catalogo tra i %(count)s file direttamente scaricabili che <a %(a_preserve)s>conserveremo per sempre</a>."

msgid "page.search.results.help_preserve"
msgstr "Chiunque può contribuire a proteggere questi file facendo da seeder al nostro <a %(a_torrents)s>elenco unificato di torrent</a>."

msgid "page.search.results.most_comprehensive"
msgstr "Attualmente disponiamo del catalogo aperto più completo al mondo di libri, pubblicazioni, riviste e altre opere scritte. Siamo un mirror di Sci-Hub, Library Genesis, Z-Library e <a %(a_datasets)s>altri ancora</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Se conosci altre biblioteche ombra di cui dovremmo fare il mirroring o se hai delle domande, contattaci all'indirizzo email %(email)s."

msgid "page.search.results.dmca"
msgstr "Per reclami DMCA/copyright, <a %(a_copyright)s>clicca qui</a>."

msgid "page.search.results.shortcuts"
msgstr "Suggerimento: utilizza le scorciatoie da tastiera \"/\" (focus di ricerca), \"Invio\" (ricerca), \"J\" (su), \"K\" (giù) per una navigazione più rapida."

msgid "page.search.results.looking_for_papers"
msgstr "Stai cercando dei paper?"

msgid "page.search.results.search_journals"
msgstr "Digita nella casella per cercare nel nostro catalogo di %(count)s documenti accademici e articoli di giornali/riviste che noi <a %(a_preserve)s>conserveremo per sempre</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Digita nella casella per cercare i file nelle biblioteche con prestito digitale."

msgid "page.search.results.digital_lending_info"
msgstr "Questo indice di ricerca include i metadati relativi alla libreria \"Controlled Digital Lending\" di Internet Archive. <a %(a_datasets)s>Maggiori informazioni sui nostri dataset</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Per altre biblioteche con prestiti digitali, consulta <a %(a_wikipedia)s>Wikipedia</a> e <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Digita nella casella per cercare i metadati dalle biblioteche. Questo può essere utile quando <a %(a_request)s>richiedi un file</a>."

msgid "page.search.results.metadata_info"
msgstr "Questo indice di ricerca include attualmente metadati provenienti da varie fonti. <a %(a_datasets)s>Maggiori informazioni sui nostri dataset</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Per i metadati, mostriamo i record originali. Non effettuiamo unioni di record."

msgid "page.search.results.metadata_info_more"
msgstr "Esistono moltissime fonti di metadati per le opere scritte in tutto il mondo. <a %(a_wikipedia)s>Questa pagina di Wikipedia</a> è un buon inizio ma se conosci altri elenchi validi, faccelo sapere!"

msgid "page.search.results.search_generic"
msgstr "Digita nella casella per effettuare la ricerca."

msgid "page.search.results.these_are_records"
msgstr "Questi sono record di metadati, <span %(classname)s>non</span> file scaricabili."

msgid "page.search.results.error.header"
msgstr "Errore durante la ricerca."

msgid "page.search.results.error.unknown"
msgstr "Prova a <a %(a_reload)s>ricaricare la pagina</a>. Se il problema persiste, per favore scrivici un'email a %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Nessun file trovato.</span> Prova a utilizzare meno termini di ricerca o filtri, o a modificarli."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A volte questo accade in modo errato quando il server di ricerca è lento. In tali casi, un <a %(a_attrs)s>reload</a> può aiutare."

msgid "page.search.found_matches.main"
msgstr "Abbiamo trovato delle corrispondenze in: %(in)s. Puoi fare riferimento all'URL trovato quando <a %(a_request)s>richiedi un file</a>."

msgid "page.search.found_matches.journals"
msgstr "Articoli di giornale (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Prestiti digitali (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Metadati (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Risultato %(from)s-%(to)s (%(total)s totale)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ corrispondenze parziali"

msgid "page.search.results.partial"
msgstr "%(num)d corrispondenze parziali"

msgid "page.volunteering.title"
msgstr "Volontariato & Ricompense"

msgid "page.volunteering.intro.text1"
msgstr "L'Archivio di Anna si affida a volontari e volontarie come te. Tutti i livelli di impegno sono benvenuti e cerchiamo aiuto in due categorie principali:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Attività di volontariato leggero:</span> se puoi dedicare solo poche ore qua e là, ci sono ancora molti modi in cui puoi aiutare. Ricompensiamo le persone più costanti con <span %(bold)s>🤝 abbonamenti all'Archivio di Anna</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Attività di volontariato intenso (ricompense da USD$50 a USD$5.000):</span> se sei in grado di dedicare molto tempo e/o risorse alla nostra missione, ci piacerebbe collaborare più da vicino con te. Potresti perfino arrivare a unirti al nostro team interno. Anche se abbiamo un budget limitato, siamo in grado di assegnare <span %(bold)s>💰 ricompense monetarie</span> per le attività più intense."

msgid "page.volunteering.intro.text2"
msgstr "Se non puoi dedicarci tempo come volontario o volontaria, puoi comunque aiutarci molto <a %(a_donate)s>facendo una donazione in denaro</a>, <a %(a_torrents)s>eseguendo il seeding dei nostri torrent</a>, <a %(a_uploading)s>caricando libri</a> o <a %(a_help)s>parlando ai tuoi amici dell'Archivio di Anna</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Aziende:</span> offriamo accesso diretto ad alta velocità alle nostre raccolte in cambio di donazioni di livello aziendale o di nuove raccolte (ad esempio, nuove scansioni, dataset di cui è stato eseguito l'OCR, arricchimento dei nostri dati). <a %(a_contact)s>Contattaci</a> se la cosa ti interessa. Consulta anche la nostra <a %(a_llm)s>pagina LLM</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Volontariato light"

msgid "page.volunteering.section.light.text1"
msgstr "Se hai qualche ora libera, puoi aiutarci in diversi modi. Assicurati di seguire la <a %(a_telegram)s>chat dei volontari su Telegram</a>."

msgid "page.volunteering.section.light.text2"
msgstr "Come segno di apprezzamento, di solito offriamo 6 mesi di abbonamento “Bibliotecario fortunato” per traguardi di base, e di più per attività di volontariato continuative. Tutti i traguardi richiedono lavoro di alta qualità: un lavoro scadente ci danneggia più di quanto ci aiuti e lo rifiuteremo. Per favore <a %(a_contact)s>inviaci un'email</a> quando raggiungi un traguardo."

msgid "page.volunteering.table.header.task"
msgstr "Attività"

msgid "page.volunteering.table.header.milestone"
msgstr "Traguardo"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Parlando dell'Archivio di Anna ad altre persone: ad esempio, consigliando libri su AA, creando link ai nostri post sul blog o rimandando le persone al nostro sito web."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s link o screenshot."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Questi dovrebbero evidenziare che parli ad altre persone dell'Archivio di Anna, con i loro ringraziamenti."

msgid "page.volunteering.table.open_library.task"
msgstr "Migliora i metadati <a %(a_metadata)s>collegandoli</a> con Open Library."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Puoi usare la <a %(a_list)s >lista di problemi vari di metadati</a> come punto di partenza."

msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assicurati di lasciare un commento sui problemi che risolvi, in modo da evitare che altri ripetano il tuo lavoro."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s link di record che hai migliorato."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Tradurre</a> il sito web."

msgid "page.volunteering.table.translate.milestone"
msgstr "Tradurre completamente una lingua (se non era già quasi completata)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "Migliora la pagina Wikipedia dell'Archivio di Anna nella tua lingua. Includi informazioni dalla pagina Wikipedia in altre lingue, e dal nostro sito web e blog. Aggiungi riferimenti su altre pagine rilevanti."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Link alla cronologia delle modifiche che mostra che hai fornito contributi significativi."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Soddisfare le richieste di libri (o articoli, ecc.) sui forum di Z-Library o Library Genesis. Non abbiamo un nostro sistema di richieste di libri, ma facciamo il mirror di quelle biblioteche, quindi migliorarle rende migliore anche l'Archivio di Anna."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s link o screenshot di richieste che hai soddisfatto."

msgid "page.volunteering.table.misc.task"
msgstr "Piccole attività pubblicate nella nostra <a %(a_telegram)s>chat dei volontari su Telegram</a>. Di solito in cambio dell'abbonamento, a volte di piccole ricompense."

msgid "page.volunteering.table.misc.task2"
msgstr "Piccole attività pubblicate nella nostra chat di gruppo per volontari."

msgid "page.volunteering.table.misc.milestone"
msgstr "Dipende dall'attività."

msgid "page.volunteering.section.bounties.heading"
msgstr "Taglie"

msgid "page.volunteering.section.bounties.text1"
msgstr "Siamo sempre alla ricerca di persone con solide competenze di programmazione o sicurezza offensiva. Puoi dare un contributo significativo alla preservazione dell'eredità dell'umanità."

msgid "page.volunteering.section.bounties.text2"
msgstr "A titolo di ringraziamento, regaliamo un abbonamento per contributi importanti. A titolo di ulteriore ringraziamento, offriamo ricompense monetarie per attività particolarmente importanti e difficili. Questo non dovrebbe essere visto come un sostituto di un lavoro, ma è un incentivo extra e può aiutare con i costi sostenuti."

msgid "page.volunteering.section.bounties.text3"
msgstr "La maggior parte del nostro codice è open source, e chiederemo che lo sia anche il tuo quando ti assegneremo la ricompensa prevista. Ci sono alcune eccezioni che possiamo discutere su base individuale."

msgid "page.volunteering.section.bounties.text4"
msgstr "Le ricompense vengono assegnate alla prima persona che completa un'attività. Non esitare a lasciare un commento su un ticket di ricompensa per far sapere ad altre persone che ci stai lavorando, in modo che possano aspettare o contattarti per collaborare. Ma sii consapevole che le altre persone sono comunque libere di cercare di batterti sul tempo. Tuttavia, non assegniamo ricompense per lavori scadenti. Se due submit di alta qualità vengono effettuate a un giorno o due di distanza tra loro, potremo scegliere di assegnare una ricompensa a entrambe, a nostra discrezione, ad esempio 100%% per la prima submit e 50%% per la seconda (quindi 150%% in totale)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Per le ricompense più grandi (specialmente di scraping), contattaci quando hai completato più o meno il 5%% dell'attività, e hai la certezza che il tuo metodo scalerà fino al completamento. Dovrai condividere il tuo metodo con noi in modo che possiamo darti un feedback. Inoltre, in questo modo possiamo decidere cosa fare se ci sono più persone che sono vicine a una ricompensa, ad esempio assegnarla a più persone, incoraggiarle a collaborare, ecc."

msgid "page.volunteering.section.bounties.text6"
msgstr "ATTENZIONE: le attività con ricompense elevate sono <span %(bold)s>complesse</span>. È consigliabile iniziare con quelle più facili."

msgid "page.volunteering.section.bounties.text7"
msgstr "Vai alla nostra <a %(a_gitlab)s>lista di problemi su Gitlab</a> e ordina per “Priorità etichetta” per visualizzare in ordine le attività che ci interessano. Le attività senza ricompense esplicite sono comunque idonee per ricevere un abbonamento, specialmente quelle contrassegnate come “Accettata” e “Preferita di Anna”. Può essere consigliabile iniziare da un “Progetto iniziale”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Aggiornamenti su <a %(wikipedia_annas_archive)s>L'Archivio di Anna</a>, la più grande biblioteca veramente aperta nella storia umana."

msgid "layout.index.title"
msgstr "Anna’s Archive"

msgid "layout.index.meta.description"
msgstr "La più grande biblioteca open-source e open-data al mondo. Mirror di Sci-Hub, Library Genesis, Z-Library e molti altri."

msgid "layout.index.meta.opensearch"
msgstr "Cerca in Anna’s Archive"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "L'Archivio di Anna ha bisogno del tuo aiuto!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Molti cercano di abbatterci, ma noi non molliamo."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Se doni ora, ottieni <strong>il doppio</strong> di download veloci."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Offerta valida fino alla fine di questo mese."

msgid "layout.index.header.nav.donate"
msgstr "Donare"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Salvare la conoscenza umana: un fantastico regalo per le feste!"

msgid "layout.index.header.banner.surprise"
msgstr "Sorprendi una persona cara e regalale un abbonamento."

msgid "layout.index.header.banner.mirrors"
msgstr "Per aumentare la resilienza dell'Archivio di Anna, stiamo cercando volontari e volontarie per gestire i mirror."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Il regalo perfetto per San Valentino!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Abbiamo un nuovo metodo di donazione:%(method_name)s. Prendi in considerazione l'idea di %(donate_link_open_tag)sdonare</a>: non è economico far funzionare questo sito e la tua donazione può fare una grande differenza. Grazie mille."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Stiamo organizzando una raccolta fondi per <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">sostenere</a> la più grande biblioteca ombra di fumetti al mondo. Grazie per sostegno che ci darai! <a href=\"/donate\">Dona ora.</a> Se non puoi donare, prendi in considerazione l'idea di supportarci spargendo la voce e di seguirci su <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a> o su <a href=\"https://t.me/annasarchiveorg\">Telegram </a>."

msgid "layout.index.header.recent_downloads"
msgstr "Download recenti:"

msgid "layout.index.header.nav.search"
msgstr "Cerca"

msgid "layout.index.header.nav.faq"
msgstr "FAQ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Migliora i metadati"

msgid "layout.index.header.nav.volunteering"
msgstr "Volontariato & Ricompense"

msgid "layout.index.header.nav.datasets"
msgstr "Set di dati"

msgid "layout.index.header.nav.torrents"
msgstr "Torrent"

msgid "layout.index.header.nav.activity"
msgstr "Attività"

msgid "layout.index.header.nav.codes"
msgstr "Esploratore di Codici"

msgid "layout.index.header.nav.llm_data"
msgstr "Dati LLM"

msgid "layout.index.header.nav.home"
msgstr "Home"

msgid "layout.index.header.nav.annassoftware"
msgstr "Software di Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Traduci ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Accedi/Registrati"

msgid "layout.index.header.nav.account"
msgstr "Account"

msgid "layout.index.footer.list1.header"
msgstr "Anna’s Archive"

msgid "layout.index.footer.list2.header"
msgstr "Restare in contatto"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA/reclami per copyright"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Avanzato"

msgid "layout.index.header.nav.security"
msgstr "Sicurezza"

msgid "layout.index.footer.list3.header"
msgstr "Alternative"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non affiliato"

msgid "page.search.results.issues"
msgstr "❌ Questo file potrebbe avere dei problemi."

msgid "page.search.results.fast_download"
msgstr "Download rapido"

msgid "page.donate.copy"
msgstr "Copia"

msgid "page.donate.copied"
msgstr "Copiato!"

msgid "page.search.pagination.prev"
msgstr "Precedente"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Successivo"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "Mirror #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "Il 5%% del patrimonio scritto dell'umanità preservato per sempre %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datasets ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Scarica gratuitamente ebook/file %(extension)s da:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Abbiamo più opzioni di download nel caso in cui una di esse non funzioni. Tutte le opzioni di download hanno lo stesso identico file."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Nora che qui Anna’s Archive non ospita nessuno dei contenuti. Semplicemente colleghiamo a siti web di altri. Se pensi di detenere un reclamo DMCA valido, per favore vedi %(about_link)s la pagina about."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library Mirror Anonimo #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "Donazioni"

#~ msgid "page.donate.header"
#~ msgstr "Donazioni"

#~ msgid "page.donate.text1"
#~ msgstr "Anna's Archive è un progetto open source senza scopo di lucro, gestito completamente da volontari. Accettiamo donazioni per coprire i nostri costi, che includono hosting, nomi di dominio, sviluppo e altre spese."

#~ msgid "page.donate.text2"
#~ msgstr "Con il tuo contributo siamo in grado di mantenere attivo questo sito, migliorarne le funzionalità e conservare più raccolte."

#~ msgid "page.donate.text3"
#~ msgstr "Donazioni recenti: %(donations)s. Grazie a tutti per la vostra generosità. Apprezziamo molto la fiducia che riponi in noi, con qualsiasi importo tu possa risparmiare."

#~ msgid "page.donate.text4"
#~ msgstr "Per donare, seleziona il tuo metodo preferito qui sotto. In caso di problemi, contattaci all'indirizzo %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "PayPal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Carta di credito/debito"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Criptovalute"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Domande"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Vai a %(link_open_tag)squesta pagina</a> e segui le istruzioni, alternativamente leggendo il QR code oppure cliccando il link “paypal.me”. Se non dovessero funzionare prova ad aggiornare la pagina, poichè potrebbe darti un account diverso."

#~ msgid "page.donate.cc.header"
#~ msgstr "Carta di credito/debito"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Utilizziamo Sendwyre per depositare denaro direttamente nel nostro portafoglio Bitcoin (BTC). Il processo richiede circa 5 minuti."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Questo metodo ha un importo minimo di transazione di $ 30 e una commissione di circa $ 5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Passi:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Copia l'indirizzo del nostro portafoglio Bitcoin (BTC): %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Vai a %(link_open_tag)squesta pagina</a> e clicca su \"compra criptovalute istantaneamente\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Incolla l'indirizzo del nostro portafoglio e segui le istruzioni"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Criptovalute"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(Funziona anche per BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Utilizza questo %(link_open_tag)saccount Alipay</a> per inviare la tua donazione."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Questa metodo di donazione non è attualmente disponibile. Per favore controlla più tardi. Ti ringraziamo per voler donare, lo apprezziamo veramente!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Per favore, usa %(link_open_tag)squesta pagina Pix</a> per mandare la tua donazione. Se non funziona, prova aggiornando la pagina, potrebbe darti un account differente."

#~ msgid "page.donate.faq.header"
#~ msgstr "Domande frequenti"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">L'archivio di Anna</span> è un progetto che mira a catalogare tutti i libri esistenti, aggregando dati da varie fonti. Tracciamo anche il progresso dell'umanità nel rendere tutti questi libri facilmente disponibili il formato digitale, attraverso“<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">shadow libraries</a>”. Altre informazioni <a href=\"/about\">Chi siamo.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Libri (tutti)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Home"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Dataset ▶ ISBN ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Non trovato"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” non è un numero ISBN valido. Gli ISBN sono lunghi 10 o 13 caratteri, senza contare i trattini opzionali. Tutti i caratteri devono essere numeri, ecceto l'ultimo carattere, che può essere anche una \"X\". L'ultimo carattere è il \"carattere di controllo\", e deve corrispondere al valore checksum ottenuto dal calcolo degli altri numeri. Deve inoltre essere in un range valido, assegnato dall'agenzia internazionale ISBN."

#~ msgid "page.isbn.results.text"
#~ msgstr "File corrispondenti nel nostro database:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Nessun file corrispondente trovato nel nostro database."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Cerca ▶ %(num)d+ risultati per <span class=\"italic\">%(search_input)s</span> (in shadow library metadata)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Cerca ▶ %(num)d risultati per <span class=\"italic\">%(search_input)s</span> (in shadow library metadata)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Cerca ▶ Errore di ricerca per <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Cerca ▶ Nuova ricerca"

#~ msgid "page.donate.header.text3"
#~ msgstr "È possibile effettuare una donazione anche senza creare un account (gli stessi metodi di pagamento sono supportati per le donazioni una tantum e le iscrizioni):"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Effettuare una donazione anonima una tantum (senza vantaggi)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Seleziona una delle opzioni di pagamento. Per favore, considera l'utilizzo di pagamenti in cryptovalute o %(bitcoin_icon)s: paghiamo (molte) meno commissioni."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Se hai già criptovalute, questi sono i nostri indirizzi."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Grazie mille per il tuo aiuto! Questo progetto non sarebbe possibile senza di te."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Per donazioni tramite PayPal US, usiamo PayPal Crypto, che ci consente di rimanere anonimi. Apprezziamo il tempo che dedichi ad imparare come donare tramite questo metodo, che ci aiuta moltissimo."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Seguite le istruzioni per acquistare Bitcoin (BTC). È sufficiente acquistare l'importo che si desidera donare."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Se perdete qualche Bitcoin a causa delle fluttuazioni o delle commissioni, <em>non preoccupatevi</em>. Questo è normale con le criptovalute, ma ci permette di operare in modo anonimo."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Inserisci il nostro indirizzo Bitcoin (BTC) come destinatario, e segui le istruzioni per inviare la tua donazione:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Per favore usa <a %(a_account)s>questo account Alipay</a> per inviare la tua donazione."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Per favore usa <a %(a_account)s>questo account Pix</a> per inviare la tua donazione."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr "Se il tuo metodo di pagamento non è in lista, la cosa più semplice sarebbe scaricare <a href=\"https://paypal.com/\">PayPal</a> o <a href=\"https://coinbase.com/\">Coinbase</a> sul tuo telefono, e comprare una frazione di Bitcoin (BTC) lì. Puoi poi mandarli al nostro indirizzo: %(address)s. Nella maggior parte dei paesi, dovrebbe richiedere solo qualche minuto."

#~ msgid "page.search.results.error.text"
#~ msgstr "Prova a <a href=\"javascript:location.reload()\">ricaricare la pagina</a>. Se il problema persiste, per favore faccielo sapere su <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr "Per diventare un iscritto, effettua il <a href=\"/login\">Log in o Registrati</a>. Se preferisci non creare un account, seleziona “Effettua una singola donazione anonima” sopra. Grazie per il tuo supporto!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Home"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Di"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donazioni"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Set di dati"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Applicazione mobile"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Anna’s Blog"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Anna’s Software"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Traduci"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Mirror %(libraries)s e molto altro."

#~ msgid "page.home.preservation.text"
#~ msgstr "Conserviamo libri, articoli scientifici, fumetti, riviste e molto altro, riunendo in un unico luogo questi materiali che provengono da varie <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">librerie ombra</a> . Tutti questi dati sono conservati per sempre grazie alla facilità di duplicazione in massa, che si traduce in molte copie in tutto il mondo. Questa ampia distribuzione, unita al codice open-source, rende il nostro sito resistente ai takedown. Maggiori informazioni sul <a href=\"/datasets\">nostro dataset</a>."

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Dataset ▶ DOI ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Non trovato"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" non sembra essere un DOI. Il codice deve iniziare con \"10.\" e deve contenere \"/\"."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL canonico: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Il file potrebbe essere in %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "File corrispondenti nel nostro database:"

#~ msgid "page.doi.results.none"
#~ msgstr "Nessun file corrispondente trovato nel nostro database."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "<strong>🚀 Download rapidi</strong> Hai terminato i tuoi download rapidi per oggi. Contatta Anna via email a %(email)s se vuoi aggiornare il tuo abbonamento."

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Hai esaurito i tuoi download veloci per oggi. Contatta Anna via email a %(email)s se vuoi aggiornare il tuo abbonamento."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Posso contribuire in altri modi?</div> Certo! Guarda <a href=\"/about\">Chi siamo</a> nella sezione “Come aiutarci”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Non mi piace che state monetizzando \"Anna's Archive\"!</div> Se non ti piace come stiamo gestendo il progetto, apri la tua libreria nascosta! Tutto il nostro codice e i dati sono open source quindi non c'è niente a fermarti. ;)"

#~ msgid "page.request.title"
#~ msgstr "Richiedi libri"

#~ msgid "page.request.text1"
#~ msgstr "Per il momento, puoi cortesemente effettuare le richieste di eBook sul <a %(a_forum)s>forum Libgen.rs</a>? Puoi creare un account lì e postare in uno di questi thread:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Per gli eBook, usa <a %(a_ebook)s>questo thread</a>.</li><li %(li_item)s>Per i libri che non sono disponibili come eBook, usa <a %(a_regular)s>questo thread</a>.</li>"

#~ msgid "page.request.text3"
#~ msgstr "In entrambi i casi, assicurati di seguire le regole menzionate nei thread."

#~ msgid "page.upload.title"
#~ msgstr "Carica"

#~ msgid "page.upload.libgen.header"
#~ msgstr "Library Genesis"

#~ msgid "page.upload.zlib.header"
#~ msgstr "Z-Library"

#~ msgid "page.upload.large.header"
#~ msgstr "Caricamenti di grandi dimensioni"

#~ msgid "page.about.title"
#~ msgstr "Chi siamo"

#~ msgid "page.about.header"
#~ msgstr "Chi siamo"

#~ msgid "page.home.search.header"
#~ msgstr "Cerca"

#~ msgid "page.home.search.intro"
#~ msgstr "Cerca nel nostro catalogo."

#~ msgid "page.home.random_book.header"
#~ msgstr "Libro casuale"

#~ msgid "page.home.random_book.intro"
#~ msgstr "Vai a un libro casuale del nostro catalogo."

#~ msgid "page.home.random_book.submit"
#~ msgstr "Libro casuale"

#~ msgid "page.about.text1"
#~ msgstr "Anna’s Archive è un motore di ricerca open-source e senza scopo di lucro per le “<a href=\"https://it.wikipedia.org/wiki/Biblioteca_ombra\">biblioteche ombra</a>”. Questo sito è stato creato da <a href=\"http://annas-blog.org\">Anna</a> che sentiva ci fosse l'esigenza di un unico luogo per la ricerca di libri, pubblicazioni, fumetti, riviste ecc. nei database delle varie biblioteche ombra."

#~ msgid "page.about.text4"
#~ msgstr "Se hai un reclamo DMCA valido, vai a fondo pagina o contattaci all'indirizzo email %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Esplora i libri"

#~ msgid "page.home.explore.intro"
#~ msgstr "Si tratta di una combinazione di libri popolari e di libri che hanno un significato speciale per il mondo delle biblioteche shadow e della conservazione digitale."

#~ msgid "page.wechat.header"
#~ msgstr "WeChat non ufficiale"

#~ msgid "page.wechat.body"
#~ msgstr "Abbiamo una pagina WeChat non ufficiale, gestita da un membro della comunità. Utilizza il codice qui sotto per accedere."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Chi siamo"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "App per smartphone"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "WeChat non ufficiale"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Richiedi libri"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Carica"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "Invita gli amici"

#~ msgid "page.about.help.header"
#~ msgstr "Come supportarci"

#~ msgid "page.refer.title"
#~ msgstr "Invita gli amici per ricevere dei bonus per i download rapidi"

#~ msgid "page.refer.section1.intro"
#~ msgstr "I membri possono segnalare amici e guadagnare download bonus."

#~ msgid "page.refer.section1.list_start"
#~ msgstr "Per ogni amico che diventa membro:"

#~ msgid "page.refer.section1.list_1"
#~ msgstr "<strong>Ricevono</strong> una %(percentage)s%% di download bonus oltre ai normali download giornalieri, per l'intera durata del loro abbonamento."

#~ msgid "page.refer.section1.list_2"
#~ msgstr "<strong>Otterrai</strong> lo stesso numero di download bonus, in aggiunta ai tuoi download giornalieri regolari, e per la stessa durata per la quale il tuo amico si è abbonato (fino a un totale di %(max)s download bonus totali in qualsiasi momento). Per utilizzare i download bonus è necessario mantenere un abbonamento attivo."

#~ msgid "page.refer.section2.list_start"
#~ msgstr "Esempio:"

#~ msgid "page.refer.section2.list_1"
#~ msgstr "Il tuo amico utilizza il link referral per attivare per 3 mesi l'abbonamento \"Bibliotecario fortunato\" che contiene %(num)s download rapidi."

#~ msgid "page.refer.section2.list_2"
#~ msgstr "Riceveranno %(num)s download bonus ogni giorno, per tutti i 3 mesi di abbonamento."

#~ msgid "page.refer.section2.list_3"
#~ msgstr "Anche tu riceverai %(num)s download bonus ogni giorno, sempre per gli stessi 3 mesi di abbonamento."

#~ msgid "page.refer.linkbox.header"
#~ msgstr "<strong>Referral link:</strong> "

#~ msgid "page.refer.linkbox.login"
#~ msgstr "<a %(a_account)s>Accedi</a> e diventa un membro per invitare gli amici."

#~ msgid "page.refer.linkbox.donate"
#~ msgstr "<a %(a_donate)s>Diventa un membro</a> per segnalare gli amici."

#~ msgid "page.refer.linkbox.remember"
#~ msgstr "Oppure aggiungi %(referral_suffix)s alla fine di ogni altro link e il link referral verrà memorizzato quando i tuoi amici diventeranno membri."

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Dona il valore totale di %(total)s tramite <a %(a_account)s>questo account Alipay"

#~ msgid "page.upload.zlib.text"
#~ msgstr "In alternativa, puoi caricarli in Z-Library <a %(a_upload)s>qui</a>."

#~ msgid "page.home.mirrors.body"
#~ msgstr "Per aumentare la resilienza di Anna's Archive, cerchiamo volontari e volontarie per creare o gestire mirror. <a href=\"/mirrors\">Per saperne di più...</a>"

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "Mirror: call per volontari/ie"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "solo questo mese!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ha <a %(a_closed)s>sospeso</a> il caricamento di nuovi articoli."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Seleziona una modalità di pagamento. Offriamo sconti per pagamenti tramite cryptovalute %(bitcoin_icon)s perchè abbiamo (molte) meno commissioni."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Scegli un'opzione di pagamento. Al momento supportiamo soltanto pagamenti con criptovalute %(bitcoin_icon)s dato che i fornitori di pagamento tradizionali si rifiutano di lavorare con noi."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Non possiamo supportare direttamente le carte di credito/debito, perché le banche non vogliono lavorare con noi. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tuttavia, ci sono diversi modi per utilizzare comunque le carte di credito/debito, utilizzando i nostri altri metodi di pagamento:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Download lenti e da fonte esterna"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Download"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Se è la prima volta che utilizzi criptovalute, consigliamo di utilizzare %(option1)s, %(option2)s, o %(option3)s per acquistare e donare Bitcoin (la cripto valuta più nota e utilizzata)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 collegamenti di record che hai migliorato."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 collegamenti o screenshot."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 collegamenti o screenshot delle richieste che hai soddisfatto."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Se sei interessato a fare il mirroring di questi datasets per scopi di <a %(a_faq)s>archiviazione</a> o <a %(a_llm)s>addestramento LLM</a>, contattaci."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Se sei interessato a replicare questo dataset per scopi di <a %(a_archival)s>archiviazione</a> o <a %(a_llm)s>addestramento LLM</a>, contattaci."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sito principale"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Informazioni sui paesi ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Se sei interessato a fare il mirroring di questo dataset per scopi di <a %(a_archival)s>archiviazione</a> o <a %(a_llm)s>addestramento LLM</a>, ti preghiamo di contattarci."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "L'Agenzia Internazionale ISBN rilascia regolarmente gli intervalli che ha assegnato alle agenzie nazionali ISBN. Da questo possiamo derivare a quale paese, regione o gruppo linguistico appartiene questo ISBN. Attualmente utilizziamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Risorse"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultimo aggiornamento: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sito web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Escludendo “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "La nostra ispirazione per la raccolta dei metadati è l'obiettivo di Aaron Swartz relativo a \"creare una pagina web per ogni libro mai pubblicato\", motivo per cui ha dato origine a <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Quel progetto è andato bene, ma la nostra posizione unica ci permette di ottenere metadati che loro non riescono a ottenere."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Un'altra ispirazione è stata il desiderio di sapere <a %(a_blog)s>quanti libri ci sono nel mondo</a>, in modo da poter calcolare quanti libri ci restano da salvare."

#~ msgid "page.partner_download.text1"
#~ msgstr "Per dare a tutti l'opportunità di scaricare file gratuitamente, è necessario attendere <strong>%(wait_seconds)s secondi</strong> prima di poter scaricare questo file."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Aggiorna automaticamente la pagina. Se perdi la finestra di download, il timer si riavvierà, quindi è consigliato l'aggiornamento automatico."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Scarica ora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converti: usa strumenti online per convertire tra formati. Ad esempio, per convertire tra epub e pdf, usa <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: scarica il file (pdf o epub sono supportati), poi <a %(a_kindle)s>invia a Kindle</a> utilizzando web, app o email. Strumenti utili: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Supporta gli autori e le autrici: se ti piace e te lo puoi permettere, prendi in considerazione l'idea di acquistare l'originale o di supportare direttamente chi l'ha realizzato."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Supporta le biblioteche: se è disponibile nella tua biblioteca locale, considera la possibilità di recarti lì a prenderlo in prestito gratuitamente."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Non disponibile direttamente in massa, solo in semi-massa dietro un paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s L'Archivio di Anna gestisce una collezione di <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb è un'azienda che raccoglie dati da vari negozi di libri online per trovare metadata ISBN. L'Archivio di Anna ha effettuato backup dei metadata dei libri di ISBNdb. Questi metadata sono disponibili tramite l'Archivio di Anna (anche se attualmente non nella ricerca, a meno che non si cerchi esplicitamente un numero ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Per dettagli tecnici, vedere sotto. A un certo punto possiamo usarlo per determinare quali libri mancano ancora dalle biblioteche ombra, al fine di dare priorità ai libri da trovare e/o scansionare."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Il nostro post sul blog riguardo questi dati"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Raccolta dati ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Attualmente abbiamo un singolo torrent, che contiene un file JSON Lines gzippato da 4,4GB (20GB decompresso): \"isbndb_2022_09.jsonl.gz\". Per importare un file \".jsonl\" in PostgreSQL, puoi usare qualcosa come <a %(a_script)s>questo script</a>. Puoi anche pipearlo direttamente usando qualcosa come %(example_code)s in modo che si decomprima al volo."

#~ msgid "page.donate.wait"
#~ msgstr "Perfavore, attendi almeno <span %(span_hours)s>due ore</span> (e ricarica questa pagina) prima di contattarci."

#~ msgid "page.codes.search_archive"
#~ msgstr "Cerca nell’Archivio di Anna “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Dona tramite Alipay o WeChat. Nella prossima pagina potrai scegliere tra queste opzioni."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Diffondere la voce sull'Archivio di Anna sui social media e nei forum online, raccomandando libri o liste su AA, o rispondendo a domande."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s La raccolta di Narrativa si è evoluta ma include ancora <a %(libgenli)s>torrent</a>, anche se non aggiornati dal 2022 (sono comunque disponibili download diretti)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s L'Archivio di Anna e Libgen.li gestiscono collaborativamente raccolte di <a %(comics)s>fumetti</a> e <a %(magazines)s>riviste</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nessun torrent per le raccolte di narrativa russa e di documenti standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Non ci sono torrent disponibili per i contenuti aggiuntivi. I torrent presenti sul sito Libgen.li sono mirror di altri torrent elencati qui. L'unica eccezione sono i torrent di narrativa a partire da %(fiction_starting_point)s. I torrent di fumetti e riviste sono rilasciati in collaborazione tra l'Archivio di Anna e Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Da una raccolta <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origine esatta non chiara. In parte da the-eye.eu, in parte da altre fonti."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

