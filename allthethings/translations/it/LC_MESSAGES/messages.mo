��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b y  sd ^  �f +   Lh   xh 
  �i �  �k p  �m I  o N   Op n   �p E   
q d   Sq �  �q p  ns 	  �t �  �u �   �w   �x   �z   } �   ' �  � �  �� A   �� �    � b   ۄ *  >�    i� S  �� E   ډ )    � 
   J�    X� <   p� #   ��    ъ /   �    � .   .�    ]� I   x� A   
  � _   � 5   o�    �� 
   ��     
   ׎    �    �� 	   �    �    '� #   .�    R�    X� 	   s� 
   }�    ��    ��    ��    Џ �  � v  r� �   � +   �� v  �� �   %�   ٖ �   �    �� �   Ҙ    �� �   �� !   h� *  ��    �� z  қ G   M� v   ��    � g  � W  {� �  Ӡ )  ��    ޣ G   �� [   <� �   �� ,   Z� *   �� k   �� 4   � <   S� +   ��    �� �   æ �  F� ;   
� |   I� �   Ʃ F   �� T  ת )   ,� %  V� �   |� �   � U   �� �   � �   ȯ E   ^� �   �� �   �� ]   d� I   ²    �    �� �   ��   ?� E   \�    �� �   �� �   b�    O� �  W� �   ڸ   �� �  �� �   $� L  �   T� L   k� K   �� ]   � z   b� i   �� Y   G� *   �� �   �� w   k� K   ��    /� p   J� W   �� �   � �   ��     {�   ��   �� X  �� p  � �   �� �   � G  �� �   G� �   �� �   �� u  [� �   �� �   ��    �� �   �� �   9� �   � r  �� �   U� �   �� �   s� �  (� �   �� 6  s� /  �� /   �� X   
� �   c� Z   �� o  W� �   �� s   �� �   �� ,   �� @  �� �   �� �  ��    D� f   Z� �   �� �   u�   N� �   n� �  Q� �  ?� /   �� j  +� �  �� %  H� �  n� 5  1�   g� k   w� �   ��    x� �   �� 1  K� �  }�     �   4 �   0    � �  � v  p �   � 
   � �   � 3  � �   � =  �	 �   � X   �    +
 �  :
 h  �    5 �   D   ? 9  O �   � Y  [ l   � E   " 
  h 2   s    � ;  � �   �   � 5    E   > "  � �   � �   * j   � �       �!    �! �  �! �   y# �   B$ S   �$ �   "% (   �%   �%   �&    �' 	   ( (   ( <   8( 5   u( 4   �( U   �( r   6)    �) &   �) :   �) G   * &   g* 4   �* 0   �* u   �*    j+    o+ 0  �+ Z  �,   . �   / �   0 �   �0 N  ]1    �2 �   �2 �   �3 �   i4 B  5 p  V6 �  �7 �  �9 �  d; E   = C   I= 2   �= �   �=   d> �  �@ *  =B ^  hC +  �D   �E   �G '   I G  FI 7  �J �  �K    TM �   eM p  TN �   �O m  TP �   �Q    �R +   �R p   �R 
   dS �  oS �  V �  �W �   Z �   �Z    �[ �   �[ �   �\ E   �] p   �] '  ]^ &  �_ �   �` �  Oa g    c ,  hc    �d �  �d �   �f �  Gg �  Ei   l �   n    �n �   �n �  �o    �q \  �q   ?s 0  Wu   �w   �y F  �z E   �{ �  5| �   %~ �    G   � h   � 	   6� ;   @� !   |� '   �� "   ƀ    � �    � �  �� �  n�   Q� �   c� 	   D�   N� +  W� �  �� �  � o  �� X  ��    R� �   ^�    V�    _� �  u� ?  2�   r� c  t�    ؟ �  � �  ԡ �  �� �  ��    }� *  ~� �  �� $   -� �   R� Y   � �  m� �  Q� �   � �   �� �   -� j  �    S� �   m� I   � F   Y�    �� 1   �� :   � Z  #� �  ~� �  &� 1   �� �  ޿ �   ��    H� �   O� 9   � �   A� �   �� 2   �� o   ��    H� k  ]� 0  �� �  �� j  �� ~   �    �� o   �� 
  #�    .� �   5� �  �� �   �� .   O� 8   ~� h  �� +    � G  L� �  �� �  6� #   !� |   E� �   �� �  P� �  �� 
   �� �   �� .   �� �   �� �   �� !  L� �   n� B   T� +   �� 
  �� :   �� �  	� ^  �� �   �   �� Y   �� A   E� �   ��   3� �  F� �  �� �  �� 6   e� �   ��   �� H  �� �   �� �  �� �  �� +   h� �  �� *   v� �  �� W   y    � -  �    �  8   � 9  � �  �	   � �   � �  � �  { �   6   � V  � :    �   S �   �    b    � @  � �   � :      � �  � =  �   � �  �! �   c# �  $ T  �% )   ,' �   V'    ( �  &( "   �)    �)    �)     * &   *    8*    I*    \*    r*    {*    �* 
   �* 	   �*    �*    �* 
   �* 1   �*    + 	   +    "+    )+ �   0+ [    , %   \, 
   �, *   �, (   �, -   �, 5   - $   H- 
   m-    x-    �-    �-    �-    �-    �-    �-    �-    �- 4   . +   8.    d. !   ~. %   �. 7   �. 7   �.    6/     L/ 3   m/ #   �/ Z   �/ H    0    i0 \   o0 =   �0    
1    &1 "   81    [1    v1    �1    �1    �1    �1    �1    �1    �1    2 	   2 
   2    %2     (2    I2    P2 	   Y2    c2 	   x2    �2    �2    �2 	   �2    �2    �2    �2    �2    �2    3    %3    -3 	   ?3    I3 #   Z3    ~3    �3 #   �3    �3    �3 
   �3    �3    �3    4    4 g   ,4 �   �4 b   .5 �   �5 �  ?6 �   !8    �8    �8    �8    �8    �8     9 
   9 ,   &9 C   S9 1   �9 W   �9 -   !: ?   O: 2   �: �   �: o   K; �   �; X   �< 5   �< %   5=    [=    t= 	   |=    �=    �=    �=    �=    �=    �=    �=    �=    �=    �=    �=    >    >    *>    ;>    L> 	   R>    \>    d>    p>    �>   �>    �?    �?    �?     �?    �? P   �? [   C@ '   �@ 7   �@ H   �@    HA    PA    XA r   [A    �A    �A ;   �A x   !B    �B    �B l   �B &   2C �  YC _   ;G �   �G �   9H �   �H >   �I   J �   K _  �K   M    N    1N O   =N F   �N Q   �N `   &O X   �O M   �O G   .P "   vP 2   �P    �P    �P K   �P #   3Q    WQ    _Q    oQ    vQ �   �Q    R -   )R Y   WR    �R    �R U   �R K   <S �  �S    U    U �   3U    �U 	   V    V    V    :V .   CV a  rV    �W    �W    �W 	   X E  $X &   jY    �Y    �Y q   �Y    Z    Z 3   #Z    WZ    sZ '   zZ �   �Z    C[    T[ G   f[ $   �[    �[    �[ 
   �[ &   �[ f   #\    �\ 8   �\ �   �\ j   {] G   �]    .^ �   @^ =    _    ^_ :   w_    �_ �   �_ �   z`    4a <   Oa �   �a �   b !   �b $   �b 
   $c d  2c     �d '   �d    �d %   �d &   $e �   Ke    �e    f 8   /f ?   hf    �f    �f $   �f .   �f O  g j  ki    �j @   �l 4   8m    mm    zm 8  �m �   �n �   �o #   ip �   �p �  vq    $s     Bs   cs \  zu    �v 
   �v 6   �v    %w 6  -w    dx H  �x �  �y �   �{    i| �   �| 8   } (   O} r   x} &  �}    �   -� �  ΀ }   p�   � Z   � �   L� �   � O   υ �   � 0   ҆ *   �    .�    C�    L� (   ]� #   �� @   �� 3   � 	   � 3   )� K  ]� 6   �� �   �� �   �� �   S� *   �� !   !� !   C�    e� /   {� "   �� +   Ό &   �� �   !� '   � �   3�    � �   � �   ȏ �   L� ,  ؐ �   � /   ʒ �   �� 	   �� 4  �� k   Ȕ    4� �  H�    �    �    %�    :� +   Y� 
   ��    �� L   �� �   � �   u� 
   p�    {�    �� �   �� E  b� �   �� u   Z�    М    �    ��    
�    %�    8�    I� Z   Q� 2   �� �  ߝ s   ڟ    N� ]   e� |   à P   @� o   �� Z   � T   \�    �� e   �� J   � �   g� Y   �� N   U�    �� �   �� d   �� >   � b   Q� d   �� >   �    X� 5   a� m   �� �   � 4   �� �   ٨    �� �   �� M   |� a   ʪ �   ,�    ԫ �  ܫ �   ��    ��    ��    ή    ֮ �   ܮ 3   ɯ h   �� �   f� �   U� �   0� �   ² �   �� �   8� �   � Z   �� +  �� s   $� r  �� �   � �   ع 
   �� 
   κ 
   ܺ �   � 
   �� 
   �� g   �� ]   � �   l� 
   G�    U� 6   ս �   � ;   �� w   ɾ [   A� 
   �� �   �� 
   B� 
   P� 
   ^� V  l� �   �� �  K�    7�    T�    Y� �   i� !   (� .   J� �   y� �   q�     !�    B�    R� <   p� >   �� 4   ��    !�    ?� �   ]�    N� �  m�    #� �   $� J   �� �   ��   �� �  �� 9  �� �   �� �   �� �   J�    �� X  ��    ?� n  ]� �   �� 	  �� �   �� >  X� �   �� �   O� �   �� /   �� B   ��    �� 1   �    >�    J�    Z� �   n�    E� �   N� 4   ��    �    
�    � "   %� x   H� K   �� ;   
�   I� 4   Q�    ��    ��    �� $   ��    ��    ��    ��    ��    ��    ��    �    
� %   � �   8�    ��    �� 
   ��    �� 
   �    � 
   �    '�    3� 
   I� /   T� t   ��    �� 5   
� a   C� �   �� �   V� �   )� =  �� �   "�   �� 
   �� �   � <   �� Q   �� X   "� �   {� �   '� I   � p   Y�    �� �   �� �   c� ~   9�    �� 
   ��    ��    ��    ��    �    &� !   .�    P�    Y�    q� !   ��    ��    ��    ��    ��    � 
   �    &� !   .�    P� (   W� %   �� �   �� �   -� *   �� &   �� [   � `   m� r   �� 1   A� 4   s� -   �� :   �� D   � /   V� �   ��   N� '  g�    �� ?   �� �   �� '   t� �   �� �   �� �   ~� <   
�    G� �   f� �   � l   �� 3   1  �   e  �   �     � 0   �    � ;    �   H    �    L    T    ] !   d �   � <   % +   b >   � $   �    � !    Y   ) &   � h   � 9    	  M I   W h   � �   
 =   �    	    ,	    G	    c	    ~	    �	    �	 4   �	 7   
 �  F
 O   � ;   
 I   W
 4   �
 	   �
 a   �
 |   B �   � �   x    o �   x $       * �   ;    	 *   ' A   R F   � U   � k   1 Z   �    � "    �   2 0   � )     [   J �   � H   � �   � w   � g    w   � &   � ;   # �   _    � d    "   m �   � 3   W G   �    �    �    O   
 K   ] v   � `     M   � �   � �   h           @     O   ]     �  (   �     �     �     ! ,   ! �   I! c   �!    1" (   L" 1   u" "   �"    �" z   �" 8   e# u   �# S   $ -   h$ �   �$ �   % 1   & Y   @&    �& R   �& >   �&    >' x   Z' ~   �' .   R( N   �(    �( 1   �( Y   )    k) m   }) .   �)    * )   1* W   [* �   �*    �+    �+    �+ @   �+    , T   1, 6   �, �   �, l   l-    �- A   �- �   4. !   / �   ./ K   �/ )   70 Z   a0 �   �0    �1    �1    �1    �1 %   �1 >   �1 W   72    �2    �2 	   �2 $   �2 <   �2 B   '3    j3 Y   p3 A   �3 
   4    4 1   84 %   j4    �4 ]   �4 �   �4 A   �5    86    E6 �   R6 J  	7 [   T8 
   �8 n  �8 x  -: 2   �; x   �; $   R< k  w< )   �> {   
?    �?    �? �  �?    �A V   �A u   B o   |B Q   �B    >C U   SC 1   �C 4   �C x   D G   �D 8   �D V   
E ,   aE V   �E �   �E �   �F *   JG �   uG �  /H �   �I 0   pJ o  �J �   L �   M 4  �M �   O *   �O     P �   @P 2   �P �  %Q x   S A   �S    �S "   �S �  T    �U �   �U <  �V *  �W C  Y ?   SZ H   �Z \   �Z %   9[ 4   _[ D   �[ ^   �[    8\     D\ ?   e\    �\    �\ ,   �\ ^   ] .   j]    �] `   �] �   ^ �   �^    �_    �_    �_ P   �_ d   /` X   �` �   �` t   �a    1b "   Kb   nb 
   pc �   ~c �  )d 
  �f U   h /   Wh    �h    �h    �h E   �h -   �h �   
i   �i i   �j    k    .k $   @k    ek d   ~k 
   �k C   �k    5l 3   ;l ,   ol    �l    �l f   �l    m    %m 0   ;m    lm    �m }   �m �   
n d   �n x   )o N   �o �   �o    �p    �p �   �p �   �q �   |r    Os l   Xs S   �s O   t j   it d   �t c   9u    �u f   �u    v    )v    Av    Vv    mv    �v    �v    �v    �v    �v 0   �v 0   w 1   Ew $   ww 6   �w /   �w +   x    /x    Lx L   ^x    �x    �x 9   �x /   y f   8y '   �y    �y    �y !   �y    z    /z M   @z (   �z G   �z �   �z    �{    �{ &    |    '| 8   =| 	   v|    �|    �| v   �|    $} "   C}    f} 
   m} 	   x} H   �}    �} �   �}    �~    �~    �~ $   �~ #   "    F #   _ %   � )   �     � 2   �    '� T   .� $   ��    ��    Ā =   Հ ;   �     O� 2   p�    �� n   �� h   0� G   ��    �    � 	    � 
   
�    �    3� �  K� w   ��    u�    �� .   ��    �� 2   Յ    � 	   
� {   � =   �� �   ц     $   և �   �� $   }�     �� &   È )   � 0   � &   E�    l�    �� =   ��    Љ (   � =   � M  U� 8   �� R   ܋ L   /� #   |� �   �� $   I�    n� X   ��    ٍ %   �    � >   1� 8   p� +   �� �   Վ    ��    �� $   ��    ͏    �    ��    �    )�    ;� Y   S� �   ��    �� #   đ �   � �   ޒ    ��    ��    ��    ԓ    �    ��    �    '�    <�    N�    a�    p� 
   ��    ��    ��    ��    ��    ɔ #   ٔ �   ��   ͕ u  ֖ 3  L� �  ��   .� �  A�    Ş   ڞ    �   �� 
  �� �  � �   �� v  S� <   ʥ �   � A   ��    �� B   � K   ]� g   �� e   � �   w� �   %� �   ک j  ��    � �   -� �   � u   � �   W�    � �   � p  � �   �� �   <� 
   � o   � w   �� �   ��    �� f   u� �   ܶ    a� +   v�    �� I   �� ;   �    @� �   Q� =   ׸ h   �     ~� �   �� �   Y� I   � V   7� =   �� g   ̻ ]   4� &   �� �   �� [   :� �   �� �   %�    �� 3   �� ,   � k   � ;   ��    ��    ſ B   ˿    �    #�    8� I   @� C   �� B   ��    �    ,�    9�    A� 	   G� b   Q� �   �� X   6� 7   ��    �� &   �� (   ��    �    5�    >� 	   F� 
   P�    [�    b�    j�    w� 
   ~�    ��    ��    ��    ��    ��    ��    ��    ��    �    �     �    ;�     Y� �   z�    �� _   � �   t�    9�    A�    N�    ]� 
   f�    q� 
   u� �   �� �   5� >   ��    ��    � t   #�    �� �   �� �   *� ,   ��    �� �   �� �   �� O   S� �   �� u   z� ,   �� �   �    ��     �� P   �� �   6� /   �� �   �� �   �� �   /� P   ��    �    +�    1�    C�    K�    `� 
   i�    w� �   �� {   4� �   �� �   I� �   � O   � D   S� �  �� �  8� �   �� �   �� 3  o� s  ��    � �   � I  �� �   "� �  �� �  �� �   M� �  ��    g� ?   z� �   �� �  C� =   �� �   � 	   �� 	   �    � �   *� G   �� q   � ,   �� [   �� H   � j   [�    �� �   �� D   �� ,   �� Y   �� �   W�    '�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: it
Language-Team: it <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non contribuiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono la loro collezione facilmente replicabile, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno). Non facciamo giudizi morali sul far pagare denaro per l'accesso in massa a una collezione di libri illegali. È indubbio che la Z-Library abbia avuto successo nell'espandere l'accesso alla conoscenza e nel procurare più libri. Siamo semplicemente qui per fare la nostra parte: garantire la conservazione a lungo termine di questa collezione privata. - Anna e il team (<a %(reddit)s>Reddit</a>) Nel rilascio originale del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>), abbiamo fatto un mirror di Z-Library, una grande collezione di libri illegale. Come promemoria, ecco cosa abbiamo scritto in quel post originale del blog: Quella collezione risale alla metà del 2021. Nel frattempo, la Z-Library è cresciuta a un ritmo sorprendente: hanno aggiunto circa 3,8 milioni di nuovi libri. Ci sono alcuni duplicati, certo, ma la maggior parte sembra essere libri veramente nuovi o scansioni di qualità superiore di libri precedentemente inviati. Questo è in gran parte dovuto all'aumento del numero di moderatori volontari alla Z-Library e al loro sistema di caricamento in massa con deduplicazione. Vorremmo congratularci con loro per questi successi. Siamo felici di annunciare che abbiamo ottenuto tutti i libri aggiunti alla Z-Library tra il nostro ultimo mirror e agosto 2022. Siamo anche tornati indietro e abbiamo recuperato alcuni libri che ci erano sfuggiti la prima volta. In totale, questa nuova collezione è di circa 24TB, che è molto più grande della precedente (7TB). Il nostro mirror è ora di 31TB in totale. Ancora una volta, abbiamo deduplicato rispetto a Library Genesis, poiché ci sono già torrent disponibili per quella collezione. Per favore, vai al Pirate Library Mirror per dare un'occhiata alla nuova collezione (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>). Lì ci sono ulteriori informazioni su come sono strutturati i file e cosa è cambiato dall'ultima volta. Non collegheremo da qui, poiché questo è solo un sito web di blog che non ospita materiali illegali. Naturalmente, fare seeding è anche un ottimo modo per aiutarci. Grazie a tutti coloro che stanno facendo seeding del nostro set precedente di torrent. Siamo grati per la risposta positiva e felici che ci siano così tante persone che si prendono cura della conservazione della conoscenza e della cultura in questo modo insolito. 3x nuovi libri aggiunti al Pirate Library Mirror (+24TB, 3,8 milioni di libri) Leggi gli articoli correlati di TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a> - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) articoli correlati di TorrentFreak: <a %(torrentfreak)s>primo</a>, <a %(torrentfreak_2)s>secondo</a> Non molto tempo fa, le “biblioteche ombra” stavano scomparendo. Sci-Hub, il grande archivio illegale di articoli accademici, aveva smesso di accettare nuove opere a causa di cause legali. “Z-Library”, la più grande biblioteca illegale di libri, ha visto i suoi presunti creatori arrestati per accuse di violazione del copyright. Sono incredibilmente riusciti a sfuggire all'arresto, ma la loro biblioteca è comunque minacciata. Alcuni paesi stanno già facendo una versione di questo. TorrentFreak <a %(torrentfreak)s>ha riportato</a> che Cina e Giappone hanno introdotto eccezioni per l'IA nelle loro leggi sul copyright. Non ci è chiaro come questo interagisca con i trattati internazionali, ma certamente offre copertura alle loro aziende domestiche, il che spiega ciò che abbiamo osservato. Per quanto riguarda l'Archivio di Anna — continueremo il nostro lavoro sotterraneo radicato nella convinzione morale. Tuttavia, il nostro più grande desiderio è di entrare alla luce e amplificare il nostro impatto legalmente. Per favore, riformate il copyright. Quando Z-Library ha rischiato la chiusura, avevo già fatto il backup dell'intera biblioteca e stavo cercando una piattaforma per ospitarla. Questa è stata la mia motivazione per avviare l'Archivio di Anna: una continuazione della missione dietro quelle iniziative precedenti. Da allora siamo cresciuti fino a diventare la più grande biblioteca ombra al mondo, ospitando oltre 140 milioni di testi protetti da copyright in numerosi formati — libri, articoli accademici, riviste, giornali e altro ancora. Il mio team ed io siamo idealisti. Crediamo che preservare e ospitare questi file sia moralmente giusto. Le biblioteche di tutto il mondo stanno subendo tagli ai finanziamenti, e non possiamo nemmeno fidarci del patrimonio dell'umanità alle corporazioni. Poi è arrivata l'IA. Praticamente tutte le principali aziende che costruiscono LLM ci hanno contattato per addestrarsi sui nostri dati. La maggior parte delle aziende statunitensi (ma non tutte!) ha cambiato idea una volta realizzata la natura illegale del nostro lavoro. Al contrario, le aziende cinesi hanno accolto con entusiasmo la nostra collezione, apparentemente non preoccupate della sua legalità. Questo è notevole dato il ruolo della Cina come firmataria di quasi tutti i principali trattati internazionali sul copyright. Abbiamo dato accesso ad alta velocità a circa 30 aziende. La maggior parte di esse sono aziende LLM, e alcune sono broker di dati, che rivenderanno la nostra collezione. La maggior parte sono cinesi, anche se abbiamo lavorato anche con aziende degli Stati Uniti, Europa, Russia, Corea del Sud e Giappone. DeepSeek <a %(arxiv)s>ha ammesso</a> che una versione precedente è stata addestrata su parte della nostra collezione, anche se sono riservati riguardo al loro ultimo modello (probabilmente anch'esso addestrato sui nostri dati). Se l'Occidente vuole rimanere avanti nella corsa agli LLM, e in ultima analisi, all'AGI, deve riconsiderare la sua posizione sul copyright, e presto. Che siate d'accordo o meno con noi sul nostro caso morale, questo sta diventando un caso di economia, e persino di sicurezza nazionale. Tutti i blocchi di potere stanno costruendo super-scienziati artificiali, super-hacker e super-militari. La libertà di informazione sta diventando una questione di sopravvivenza per questi paesi — persino una questione di sicurezza nazionale. Il nostro team proviene da tutto il mondo, e non abbiamo un allineamento particolare. Ma incoraggeremmo i paesi con leggi sul copyright rigide a usare questa minaccia esistenziale per riformarle. Quindi cosa fare? La nostra prima raccomandazione è semplice: accorciare il termine del copyright. Negli Stati Uniti, il copyright è concesso per 70 anni dopo la morte dell'autore. Questo è assurdo. Possiamo allinearlo ai brevetti, che sono concessi per 20 anni dopo il deposito. Questo dovrebbe essere più che sufficiente per gli autori di libri, articoli, musica, arte e altre opere creative, per essere completamente compensati per i loro sforzi (inclusi progetti a lungo termine come gli adattamenti cinematografici). Poi, come minimo, i responsabili politici dovrebbero includere eccezioni per la conservazione e la diffusione di massa dei testi. Se la perdita di entrate dai clienti individuali è la principale preoccupazione, la distribuzione a livello personale potrebbe rimanere vietata. A loro volta, coloro che sono in grado di gestire vasti archivi — aziende che addestrano LLM, insieme a biblioteche e altri archivi — sarebbero coperti da queste eccezioni. La riforma del copyright è necessaria per la sicurezza nazionale In breve: gli LLM cinesi (incluso DeepSeek) sono addestrati sul mio archivio illegale di libri e articoli — il più grande al mondo. L'Occidente deve rivedere la legge sul copyright per motivi di sicurezza nazionale. Si prega di consultare il <a %(all_isbns)s>post originale del blog</a> per ulteriori informazioni. Abbiamo lanciato una sfida per migliorare questo. Avremmo assegnato un premio di $6.000 per il primo posto, $3.000 per il secondo posto e $1.000 per il terzo posto. A causa della risposta travolgente e delle incredibili proposte, abbiamo deciso di aumentare leggermente il montepremi e assegnare un terzo posto a quattro partecipanti, ciascuno con $500. I vincitori sono elencati di seguito, ma assicuratevi di guardare tutte le proposte <a %(annas_archive)s>qui</a>, o scaricare il nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinato</a>. Primo posto $6.000: phiresky Questa <a %(phiresky_github)s>proposta</a> (<a %(annas_archive_note_2951)s>commento su Gitlab</a>) è semplicemente tutto ciò che volevamo, e anche di più! Ci sono piaciute particolarmente le opzioni di visualizzazione incredibilmente flessibili (anche supportando shader personalizzati), ma con un elenco completo di preset. Ci è piaciuta anche la velocità e la fluidità di tutto, la semplice implementazione (che non ha nemmeno un backend), la mappa intelligente e l'ampia spiegazione nel loro <a %(phiresky_github)s>post sul blog</a>. Un lavoro incredibile, e un vincitore meritatissimo! - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) I nostri cuori sono pieni di gratitudine. Idee notevoli Grattacieli per rarità Tanti cursori per confrontare datasets, come se fossi un DJ. Barra di scala con numero di libri. Etichette carine. Schema di colori predefinito e mappa di calore. Vista mappa unica e filtri Annotazioni e anche statistiche in tempo reale Statistiche in tempo reale Alcune altre idee e implementazioni che ci sono piaciute particolarmente: Potremmo continuare ancora per un po', ma fermiamoci qui. Assicurati di guardare tutte le proposte <a %(annas_archive)s>qui</a>, o scarica il nostro <a %(a_2025_01_isbn_visualization_files)s>torrent combinato</a>. Tante proposte, e ognuna porta una prospettiva unica, sia nell'interfaccia utente che nell'implementazione. Incorporeremo almeno la proposta vincente nel nostro sito principale, e forse anche altre. Abbiamo anche iniziato a pensare a come organizzare il processo di identificazione, conferma e poi archiviazione dei libri più rari. Altre novità in arrivo su questo fronte. Grazie a tutti coloro che hanno partecipato. È incredibile che così tante persone ci tengano. Facile attivazione dei datasets per confronti rapidi. Tutti gli ISBN SSNO di CADAL Fuga di dati CERLALC SSID di DuXiu Indice eBook di EBSCOhost Google Libri Goodreads Internet Archive ISBNdb Registro Globale degli Editori ISBN Libby File nell'Archivio di Anna Nexus/STC OCLC/Worldcat OpenLibrary Biblioteca Statale Russa Biblioteca Imperiale di Trantor Secondo posto $3.000: hypha “Anche se i quadrati e i rettangoli perfetti sono matematicamente piacevoli, non forniscono una superiorità di località in un contesto di mappatura. Credo che l'asimmetria insita in questi Hilbert o Morton classici non sia un difetto ma una caratteristica. Proprio come il famoso contorno a forma di stivale dell'Italia lo rende immediatamente riconoscibile su una mappa, le "stranezze" uniche di queste curve possono servire come punti di riferimento cognitivi. Questa distintività può migliorare la memoria spaziale e aiutare gli utenti a orientarsi, potenzialmente rendendo più facile localizzare regioni specifiche o notare schemi.” Un'altra incredibile <a %(annas_archive_note_2913)s>proposta</a>. Non così flessibile come il primo posto, ma in realtà abbiamo preferito la sua visualizzazione a livello macro rispetto al primo posto (curva riempitiva dello spazio, bordi, etichettatura, evidenziazione, panoramica e zoom). Un <a %(annas_archive_note_2971)s>commento</a> di Joe Davis ha risuonato con noi: E ancora molte opzioni per la visualizzazione e il rendering, oltre a un'interfaccia utente incredibilmente fluida e intuitiva. Un solido secondo posto! - Anna e il team (<a %(reddit)s>Reddit</a>) Qualche mese fa abbiamo annunciato una <a %(all_isbns)s>ricompensa di $10,000</a> per realizzare la migliore visualizzazione possibile dei nostri dati mostrando lo spazio degli ISBN. Abbiamo sottolineato l'importanza di mostrare quali file abbiamo/abbiamo già archiviato, e successivamente un dataset che descrive quante biblioteche possiedono ISBN (una misura di rarità). Siamo stati sopraffatti dalla risposta. C'è stata così tanta creatività. Un grande grazie a tutti coloro che hanno partecipato: la vostra energia ed entusiasmo sono contagiosi! Alla fine volevamo rispondere alle seguenti domande: <strong>quali libri esistono nel mondo, quanti ne abbiamo già archiviati e su quali libri dovremmo concentrarci in seguito?</strong> È fantastico vedere che così tante persone si interessano a queste domande. Abbiamo iniziato con una visualizzazione di base. In meno di 300kb, questa immagine rappresenta sinteticamente la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità: Terzo posto $500 #1: maxlion In questa <a %(annas_archive_note_2940)s>proposta</a> ci sono piaciuti davvero i diversi tipi di visualizzazioni, in particolare le visualizzazioni di confronto e quelle dell'editore. Terzo posto $500 #2: abetusk Anche se non è l'interfaccia utente più raffinata, questa <a %(annas_archive_note_2917)s>proposta</a> soddisfa molti dei criteri. Ci è piaciuta particolarmente la sua funzione di confronto. Terzo posto $500 #3: conundrumer0 Come il primo posto, questa <a %(annas_archive_note_2975)s>proposta</a> ci ha impressionato per la sua flessibilità. In definitiva, questo è ciò che rende uno strumento di visualizzazione eccezionale: massima flessibilità per gli utenti esperti, mantenendo le cose semplici per gli utenti medi. Terzo posto $500 #4: charelf L'ultima <a %(annas_archive_note_2947)s>proposta</a> a ricevere un premio è piuttosto basilare, ma ha alcune caratteristiche uniche che ci sono piaciute molto. Ci è piaciuto come mostrano quanti datasets coprono un particolare ISBN come misura di popolarità/affidabilità. Ci è piaciuta anche la semplicità ma l'efficacia dell'uso di un cursore di opacità per i confronti. Vincitori della ricompensa di $10,000 per la visualizzazione degli ISBN In breve: Abbiamo ricevuto alcune incredibili proposte per la ricompensa di $10,000 per la visualizzazione degli ISBN. Sfondo Come può l'Archivio di Anna raggiungere la sua missione di fare il backup di tutta la conoscenza dell'umanità, senza sapere quali libri sono ancora là fuori? Abbiamo bisogno di una lista di cose da fare. Un modo per mappare questo è attraverso i numeri ISBN, che dagli anni '70 sono stati assegnati a ogni libro pubblicato (nella maggior parte dei paesi). Non esiste un'autorità centrale che conosca tutte le assegnazioni ISBN. Invece, è un sistema distribuito, dove i paesi ottengono intervalli di numeri, che poi assegnano intervalli più piccoli ai grandi editori, che potrebbero ulteriormente suddividere gli intervalli ai piccoli editori. Infine, i numeri individuali sono assegnati ai libri. Abbiamo iniziato a mappare gli ISBN <a %(blog)s>due anni fa</a> con il nostro scraping di ISBNdb. Da allora, abbiamo estratto molti più fonti di metadata, come <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby e altri. Un elenco completo può essere trovato nelle pagine “Datasets” e “Torrents” dell'Archivio di Anna. Ora abbiamo di gran lunga la più grande collezione completamente aperta e facilmente scaricabile di metadata di libri (e quindi ISBN) al mondo. Abbiamo <a %(blog)s>scritto ampiamente</a> sul perché ci interessa la conservazione e perché siamo attualmente in una finestra critica. Dobbiamo ora identificare i libri rari, poco focalizzati e unicamente a rischio e preservarli. Avere buoni metadata su tutti i libri del mondo aiuta in questo. Ricompensa di $10,000 Verrà data forte considerazione all'usabilità e all'aspetto estetico. Mostra i metadata effettivi per gli ISBN individuali quando si zooma, come titolo e autore. Migliore curva di riempimento dello spazio. Ad esempio, un zig-zag, andando da 0 a 4 sulla prima riga e poi indietro (in senso inverso) da 5 a 9 sulla seconda riga — applicato ricorsivamente. Schemi di colore diversi o personalizzabili. Viste speciali per confrontare i datasets. Modi per risolvere problemi, come altri metadata che non concordano bene (ad esempio titoli molto diversi). Annotare immagini con commenti su ISBN o intervalli. Qualsiasi euristica per identificare libri rari o a rischio. Qualsiasi idea creativa che puoi inventare! Codice Il codice per generare queste immagini, così come altri esempi, può essere trovato in <a %(annas_archive)s>questa directory</a>. Abbiamo ideato un formato dati compatto, con il quale tutte le informazioni ISBN richieste occupano circa 75MB (compresso). La descrizione del formato dati e il codice per generarlo possono essere trovati <a %(annas_archive_l1244_1319)s>qui</a>. Per la ricompensa non sei obbligato a usarlo, ma è probabilmente il formato più conveniente per iniziare. Puoi trasformare i nostri metadata come vuoi (anche se tutto il tuo codice deve essere open source). Non vediamo l'ora di vedere cosa inventerai. Buona fortuna! Forka questo repo e modifica questo post del blog in HTML (non sono consentiti altri backend oltre al nostro backend Flask). Rendi l'immagine sopra zoomabile in modo fluido, così puoi zoomare fino agli ISBN individuali. Cliccando sugli ISBN si dovrebbe accedere a una pagina di metadata o a una ricerca nell'Archivio di Anna. Devi comunque essere in grado di passare tra tutti i diversi datasets. Gli intervalli di paesi e gli intervalli degli editori dovrebbero essere evidenziati al passaggio del mouse. Puoi usare ad esempio <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> per le informazioni sui paesi, e il nostro scraping “isbngrp” per gli editori (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Deve funzionare bene su desktop e mobile. C'è molto da esplorare qui, quindi stiamo annunciando una ricompensa per migliorare la visualizzazione sopra. A differenza della maggior parte delle nostre ricompense, questa è a tempo limitato. Devi <a %(annas_archive)s>inviare</a> il tuo codice open source entro il 2025-01-31 (23:59 UTC). La migliore proposta riceverà $6,000, il secondo posto $3,000 e il terzo posto $1,000. Tutte le ricompense saranno assegnate utilizzando Monero (XMR). Di seguito sono riportati i criteri minimi. Se nessuna proposta soddisfa i criteri, potremmo comunque assegnare alcune ricompense, ma sarà a nostra discrezione. Per punti bonus (queste sono solo idee — lascia che la tua creatività si scateni): Puoi completamente deviare dai criteri minimi e fare una visualizzazione completamente diversa. Se è davvero spettacolare, allora si qualifica per la ricompensa, ma a nostra discrezione. Fai le tue proposte postando un commento a <a %(annas_archive)s>questo problema</a> con un link al tuo repository forkato, richiesta di merge o diff. - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Questa immagine è di 1000×800 pixel. Ogni pixel rappresenta 2.500 ISBN. Se abbiamo un file per un ISBN, rendiamo quel pixel più verde. Se sappiamo che un ISBN è stato emesso, ma non abbiamo un file corrispondente, lo rendiamo più rosso. In meno di 300kb, questa immagine rappresenta sinteticamente la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità (alcune centinaia di GB compressi in totale). Mostra anche: c'è ancora molto lavoro da fare per il backup dei libri (ne abbiamo solo 16%). Visualizzare Tutti gli ISBN — Ricompensa di $10,000 entro il 2025-01-31 Questa immagine rappresenta la più grande “lista di libri” completamente aperta mai assemblata nella storia dell'umanità. Visualizzazione Oltre all'immagine panoramica, possiamo anche esaminare i singoli datasets che abbiamo acquisito. Usa il menu a tendina e i pulsanti per passare da uno all'altro. Ci sono molti schemi interessanti da vedere in queste immagini. Perché c'è una certa regolarità di linee e blocchi, che sembra accadere a scale diverse? Quali sono le aree vuote? Perché certi datasets sono così raggruppati? Lasceremo queste domande come esercizio per il lettore. - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusione Con questo standard, possiamo effettuare rilasci in modo più incrementale e aggiungere più facilmente nuove fonti di dati. Abbiamo già alcuni rilasci entusiasmanti in cantiere! Speriamo anche che diventi più facile per altre biblioteche ombra fare il mirror delle nostre collezioni. Dopotutto, il nostro obiettivo è preservare per sempre la conoscenza e la cultura umana, quindi più ridondanza c'è, meglio è. Esempio Prendiamo come esempio il nostro recente rilascio di Z-Library. Consiste di due collezioni: “<span style="background: #fffaa3">zlib3_records</span>” e “<span style="background: #ffd6fe">zlib3_files</span>”. Questo ci permette di estrarre e rilasciare separatamente i record di metadata dai file effettivi dei libri. Pertanto, abbiamo rilasciato due torrent con file di metadata: Abbiamo anche rilasciato un gruppo di torrent con cartelle di dati binari, ma solo per la collezione “<span style="background: #ffd6fe">zlib3_files</span>”, 62 in totale: Eseguendo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> possiamo vedere cosa c’è dentro: In questo caso, si tratta di metadata di un libro come riportato da Z-Library. A livello superiore abbiamo solo “aacid” e “metadata”, ma nessuna “data_folder”, poiché non ci sono dati binari corrispondenti. L'AACID contiene “22430000” come ID principale, che possiamo vedere è preso da “zlibrary_id”. Possiamo aspettarci che altri AAC in questa collezione abbiano la stessa struttura. Ora eseguiamo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Questo è un metadata AAC molto più piccolo, sebbene la maggior parte di questo AAC si trovi altrove in un file binario! Dopotutto, questa volta abbiamo una “data_folder”, quindi possiamo aspettarci che i dati binari corrispondenti si trovino in <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Il “metadata” contiene lo “zlibrary_id”, quindi possiamo facilmente associarlo all'AAC corrispondente nella collezione “zlib_records”. Avremmo potuto associarlo in diversi modi, ad esempio tramite AACID — lo standard non lo prescrive. Nota che non è necessario che il campo “metadata” sia esso stesso JSON. Potrebbe essere una stringa contenente XML o qualsiasi altro formato di dati. Potresti persino memorizzare le informazioni di metadata nel blob binario associato, ad esempio se si tratta di molti dati. File e metadata eterogenei, nel formato più vicino possibile all'originale. I dati binari possono essere serviti direttamente da server web come Nginx. Identificatori eterogenei nelle librerie di origine, o addirittura assenza di identificatori. Rilasci separati di metadata rispetto ai dati dei file, o rilasci solo di metadata (ad esempio il nostro rilascio ISBNdb). Distribuzione tramite torrent, ma con la possibilità di altri metodi di distribuzione (ad esempio IPFS). Registri immutabili, poiché dovremmo presumere che i nostri torrent vivranno per sempre. Rilasci incrementali / rilasci aggiuntivi. Leggibile e scrivibile da macchine, in modo conveniente e veloce, specialmente per il nostro stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Ispezione umana relativamente facile, anche se questo è secondario rispetto alla leggibilità da parte delle macchine. Facile da seminare le nostre collezioni con un seedbox standard a noleggio. Obiettivi di progettazione Non ci interessa che i file siano facili da navigare manualmente su disco, o ricercabili senza pre-elaborazione. Non ci interessa essere direttamente compatibili con il software di libreria esistente. Sebbene dovrebbe essere facile per chiunque seminare la nostra collezione utilizzando i torrent, non ci aspettiamo che i file siano utilizzabili senza una significativa conoscenza tecnica e impegno. Il nostro caso d'uso principale è la distribuzione di file e metadata associati da diverse collezioni esistenti. Le nostre considerazioni più importanti sono: Alcuni obiettivi non prioritari: Poiché l'Archivio di Anna è open source, vogliamo utilizzare direttamente il nostro formato. Quando aggiorniamo il nostro indice di ricerca, accediamo solo a percorsi pubblicamente disponibili, in modo che chiunque faccia un fork della nostra libreria possa avviarsi rapidamente. <strong>AAC.</strong> AAC (Anna’s Archive Container) è un singolo elemento composto da <strong>metadata</strong> e, facoltativamente, da <strong>dati binari</strong>, entrambi immutabili. Ha un identificatore univoco globale, chiamato <strong>AACID</strong>. <strong>AACID.</strong> Il formato di AACID è il seguente: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Ad esempio, un AACID effettivo che abbiamo rilasciato è <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Intervallo AACID.</strong> Poiché gli AACID contengono timestamp che aumentano monotonamente, possiamo usarli per indicare intervalli all'interno di una particolare collezione. Utilizziamo questo formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, dove i timestamp sono inclusivi. Questo è coerente con la notazione ISO 8601. Gli intervalli sono continui e possono sovrapporsi, ma in caso di sovrapposizione devono contenere record identici a quelli precedentemente rilasciati in quella collezione (poiché gli AAC sono immutabili). Non sono consentiti record mancanti. <code>{collection}</code>: il nome della collezione, che può contenere lettere ASCII, numeri e underscore (ma non doppi underscore). <code>{collection-specific ID}</code>: un identificatore specifico della collezione, se applicabile, ad esempio l'ID di Z-Library. Può essere omesso o troncato. Deve essere omesso o troncato se l'AACID altrimenti supererebbe i 150 caratteri. <code>{ISO 8601 timestamp}</code>: una versione breve dell'ISO 8601, sempre in UTC, ad esempio <code>20220723T194746Z</code>. Questo numero deve aumentare monotonamente per ogni rilascio, sebbene i suoi esatti significati possano differire per collezione. Suggeriamo di utilizzare il tempo di scraping o di generazione dell'ID. <code>{shortuuid}</code>: un UUID ma compresso in ASCII, ad esempio utilizzando base57. Attualmente utilizziamo la libreria Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Cartella di dati binari.</strong> Una cartella con i dati binari di un intervallo di AAC, per una particolare collezione. Questi hanno le seguenti proprietà: La directory deve contenere file di dati per tutti gli AAC all'interno dell'intervallo specificato. Ogni file di dati deve avere il suo AACID come nome del file (senza estensioni). Il nome della directory deve essere un intervallo AACID, preceduto da <code style="color: green">annas_archive_data__</code>, e senza suffisso. Ad esempio, uno dei nostri rilasci effettivi ha una directory chiamata<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Si consiglia di rendere queste cartelle gestibili in termini di dimensioni, ad esempio non più grandi di 100GB-1TB ciascuna, anche se questa raccomandazione potrebbe cambiare nel tempo. <strong>Collezione.</strong> Ogni AAC appartiene a una collezione, che per definizione è un elenco di AAC semanticamente coerenti. Ciò significa che se apporti una modifica significativa al formato dei metadata, devi creare una nuova collezione. Lo standard <strong>File di metadata.</strong> Un file di metadata contiene i metadata di un intervallo di AAC, per una particolare collezione. Questi hanno le seguenti proprietà: <code>data_folder</code> è opzionale ed è il nome della cartella di dati binari che contiene i dati binari corrispondenti. Il nome del file dei dati binari corrispondenti all'interno di quella cartella è l'AACID del record. Ogni oggetto JSON deve contenere i seguenti campi al livello superiore: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opzionale). Non sono consentiti altri campi. Il nome del file deve essere un intervallo AACID, preceduto da <code style="color: red">annas_archive_meta__</code> e seguito da <code>.jsonl.zstd</code>. Ad esempio, uno dei nostri rilasci è chiamato<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Come indicato dall'estensione del file, il tipo di file è <a %(jsonlines)s>JSON Lines</a> compresso con <a %(zstd)s>Zstandard</a>. <code>metadata</code> sono metadata arbitrari, secondo la semantica della collezione. Devono essere semanticamente coerenti all'interno della collezione. Il prefisso <code style="color: red">annas_archive_meta__</code> può essere adattato al nome della tua istituzione, ad esempio <code style="color: red">my_institute_meta__</code>. <strong>Collezioni di “record” e “file”.</strong> Per convenzione, è spesso conveniente rilasciare “record” e “file” come collezioni diverse, in modo che possano essere rilasciate in orari diversi, ad esempio in base ai tassi di scraping. Un “record” è una collezione solo di metadata, contenente informazioni come titoli di libri, autori, ISBN, ecc., mentre i “file” sono le collezioni che contengono i file effettivi (pdf, epub). Alla fine, abbiamo optato per uno standard relativamente semplice. È piuttosto flessibile, non normativo e in fase di sviluppo. <strong>Torrents.</strong> I file di metadata e le cartelle di dati binari possono essere raggruppati in torrent, con un torrent per file di metadata o un torrent per cartella di dati binari. I torrent devono avere il nome originale del file/directory più un suffisso <code>.torrent</code> come nome del file. <a %(wikipedia_annas_archive)s>L'Archivio di Anna</a> è diventato di gran lunga la più grande biblioteca ombra del mondo, e l'unica biblioteca ombra della sua scala che è completamente open-source e open-data. Di seguito è riportata una tabella dalla nostra pagina Datasets (leggermente modificata): Abbiamo raggiunto questo obiettivo in tre modi: Rispecchiando le biblioteche ombra open-data esistenti (come Sci-Hub e Library Genesis). Aiutando le biblioteche ombra che vogliono essere più aperte, ma non avevano il tempo o le risorse per farlo (come la collezione di fumetti di Libgen). Raccogliendo dati da biblioteche che non desiderano condividere in massa (come Z-Library). Per (2) e (3) ora gestiamo una considerevole collezione di torrent noi stessi (centinaia di TB). Finora abbiamo trattato queste collezioni come casi unici, il che significa infrastruttura e organizzazione dei dati su misura per ogni collezione. Questo aggiunge un notevole carico a ogni rilascio e rende particolarmente difficile effettuare rilasci più incrementali. Ecco perché abbiamo deciso di standardizzare i nostri rilasci. Questo è un post tecnico del blog in cui stiamo introducendo il nostro standard: <strong>Contenitori di Archivio di Anna</strong>. Contenitori di Archivio di Anna (AAC): standardizzare le pubblicazioni dalla più grande biblioteca ombra del mondo L'Archivio di Anna è diventato la più grande biblioteca ombra del mondo, richiedendoci di standardizzare le nostre pubblicazioni. Oltre 300GB di copertine di libri rilasciate Infine, siamo felici di annunciare un piccolo rilascio. In collaborazione con le persone che gestiscono il fork Libgen.rs, stiamo condividendo tutte le loro copertine di libri tramite torrent e IPFS. Questo distribuirà il carico di visualizzazione delle copertine tra più macchine e le preserverà meglio. In molti (ma non tutti) i casi, le copertine dei libri sono incluse nei file stessi, quindi si tratta di una sorta di "dati derivati". Ma averle in IPFS è comunque molto utile per l'operatività quotidiana sia di Archivio di Anna che dei vari fork di Library Genesis. Come al solito, puoi trovare questo rilascio al Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>). Non lo linkeremo qui, ma puoi trovarlo facilmente. Speriamo di poter rallentare un po' il nostro ritmo, ora che abbiamo un'alternativa decente a Z-Library. Questo carico di lavoro non è particolarmente sostenibile. Se sei interessato ad aiutarci con la programmazione, le operazioni sui server o il lavoro di conservazione, contattaci sicuramente. C'è ancora molto <a %(annas_archive)s>lavoro da fare</a>. Grazie per il tuo interesse e supporto. Passa a ElasticSearch Alcune query richiedevano un tempo lunghissimo, al punto da monopolizzare tutte le connessioni aperte. Per impostazione predefinita, MySQL ha una lunghezza minima delle parole, o il tuo indice può diventare davvero grande. Le persone hanno segnalato di non poter cercare "Ben Hur". La ricerca era solo relativamente veloce quando completamente caricata in memoria, il che ci richiedeva di ottenere una macchina più costosa per eseguirla, oltre ad alcuni comandi per precaricare l'indice all'avvio. Non saremmo stati in grado di estenderlo facilmente per costruire nuove funzionalità, come una migliore <a %(wikipedia_cjk_characters)s>tokenizzazione per lingue senza spazi</a>, filtraggio/faccettatura, ordinamento, suggerimenti "intendevi dire", completamento automatico, e così via. Uno dei nostri <a %(annas_archive)s>ticket</a> era un insieme di problemi con il nostro sistema di ricerca. Usavamo la ricerca full-text di MySQL, dato che avevamo tutti i nostri dati in MySQL comunque. Ma aveva i suoi limiti: Dopo aver parlato con un gruppo di esperti, abbiamo optato per ElasticSearch. Non è stato perfetto (i loro suggerimenti "intendevi dire" e le funzionalità di completamento automatico di default non sono granché), ma nel complesso è stato molto meglio di MySQL per la ricerca. Non siamo ancora <a %(youtube)s>troppo entusiasti</a> di usarlo per dati mission-critical (anche se hanno fatto molti <a %(elastic_co)s>progressi</a>), ma nel complesso siamo piuttosto soddisfatti del cambiamento. Per ora, abbiamo implementato una ricerca molto più veloce, un miglior supporto linguistico, un ordinamento per rilevanza migliore, diverse opzioni di ordinamento e filtraggio per lingua/tipo di libro/tipo di file. Se sei curioso di sapere come funziona, <a %(annas_archive_l140)s>dai</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>occhiata</a>. È abbastanza accessibile, anche se potrebbe usare qualche commento in più… L'Archivio di Anna è completamente open source Crediamo che l'informazione debba essere libera, e il nostro codice non fa eccezione. Abbiamo rilasciato tutto il nostro codice sulla nostra istanza Gitlab privata: <a %(annas_archive)s>Software di Anna</a>. Utilizziamo anche il tracker dei problemi per organizzare il nostro lavoro. Se vuoi partecipare al nostro sviluppo, questo è un ottimo punto di partenza. Per darti un assaggio delle cose su cui stiamo lavorando, prendi il nostro recente lavoro sui miglioramenti delle prestazioni lato client. Poiché non abbiamo ancora implementato la paginazione, spesso restituivamo pagine di ricerca molto lunghe, con 100-200 risultati. Non volevamo interrompere troppo presto i risultati della ricerca, ma questo significava rallentare alcuni dispositivi. Per questo, abbiamo implementato un piccolo trucco: abbiamo avvolto la maggior parte dei risultati di ricerca in commenti HTML (<code><!-- --></code>), e poi abbiamo scritto un piccolo Javascript che rilevava quando un risultato doveva diventare visibile, momento in cui avremmo rimosso il commento: La "virtualizzazione" del DOM implementata in 23 righe, senza bisogno di librerie sofisticate! Questo è il tipo di codice pragmatico e veloce che si ottiene quando si ha poco tempo e problemi reali da risolvere. È stato segnalato che la nostra ricerca ora funziona bene su dispositivi lenti! Un altro grande sforzo è stato automatizzare la costruzione del database. Quando abbiamo lanciato, abbiamo semplicemente messo insieme diverse fonti in modo casuale. Ora vogliamo mantenerle aggiornate, quindi abbiamo scritto una serie di script per scaricare nuovi metadata dai due fork di Library Genesis e integrarli. L'obiettivo è non solo rendere questo utile per il nostro archivio, ma anche facilitare le cose a chiunque voglia sperimentare con i metadata delle biblioteche ombra. L'obiettivo sarebbe un notebook Jupyter che abbia a disposizione tutti i tipi di metadata interessanti, così possiamo fare più ricerche come capire quale <a %(blog)s>percentuale di ISBN è preservata per sempre</a>. Infine, abbiamo rinnovato il nostro sistema di donazioni. Ora puoi usare una carta di credito per depositare direttamente denaro nei nostri portafogli di criptovaluta, senza dover sapere davvero nulla sulle criptovalute. Continueremo a monitorare quanto bene funzioni in pratica, ma è un grande passo avanti. Con la chiusura di Z-Library e l'arresto dei suoi (presunti) fondatori, abbiamo lavorato incessantemente per fornire una buona alternativa con l'Archivio di Anna (non lo linkeremo qui, ma puoi cercarlo su Google). Ecco alcune delle cose che abbiamo realizzato di recente. Aggiornamento di Anna: archivio completamente open source, ElasticSearch, oltre 300GB di copertine di libri Abbiamo lavorato incessantemente per fornire una buona alternativa con l'Archivio di Anna. Ecco alcune delle cose che abbiamo realizzato di recente. Analisi I duplicati semantici (scansioni diverse dello stesso libro) possono teoricamente essere filtrati, ma è complicato. Quando abbiamo esaminato manualmente i fumetti abbiamo trovato troppi falsi positivi. Ci sono alcuni duplicati puramente per MD5, che è relativamente dispendioso, ma filtrarli ci darebbe solo circa 1% in di risparmio. A questa scala è comunque circa 1TB, ma anche, a questa scala 1TB non importa davvero. Preferiremmo non rischiare di distruggere accidentalmente i dati in questo processo. Abbiamo trovato un sacco di dati non relativi ai libri, come film basati su fumetti. Anche questo sembra dispendioso, poiché sono già ampiamente disponibili attraverso altri mezzi. Tuttavia, ci siamo resi conto che non potevamo semplicemente filtrare i file dei film, poiché ci sono anche <em>fumetti interattivi</em> che sono stati rilasciati sul computer, che qualcuno ha registrato e salvato come film. Alla fine, qualsiasi cosa avremmo potuto eliminare dalla collezione avrebbe risparmiato solo pochi percentuali. Poi ci siamo ricordati che siamo accumulatori di dati, e le persone che faranno il mirror di questo sono anche accumulatori di dati, e quindi, "COSA INTENDI, ELIMINARE?!" :) Quando ricevi 95TB scaricati nel tuo cluster di archiviazione, cerchi di capire cosa c'è dentro… Abbiamo fatto alcune analisi per vedere se potevamo ridurre un po' le dimensioni, ad esempio rimuovendo i duplicati. Ecco alcune delle nostre scoperte: Vi presentiamo quindi la collezione completa e non modificata. È un sacco di dati, ma speriamo che abbastanza persone si prendano cura di condividerli comunque. Collaborazione Data la sua dimensione, questa collezione è stata a lungo nella nostra lista dei desideri, quindi dopo il nostro successo con il backup di Z-Library, abbiamo puntato su questa collezione. All'inizio l'abbiamo raschiata direttamente, il che è stata una vera sfida, poiché il loro server non era nelle migliori condizioni. In questo modo abbiamo ottenuto circa 15TB, ma è stato un processo lento. Fortunatamente, siamo riusciti a metterci in contatto con l'operatore della biblioteca, che ha accettato di inviarci tutti i dati direttamente, il che è stato molto più veloce. Ci è voluto comunque più di mezzo anno per trasferire e processare tutti i dati, e abbiamo quasi perso tutto a causa della corruzione del disco, il che avrebbe significato ricominciare da capo. Questa esperienza ci ha fatto credere che sia importante diffondere questi dati il più rapidamente possibile, in modo che possano essere replicati ampiamente. Siamo solo a uno o due incidenti sfortunati dal perdere questa collezione per sempre! La collezione Muoversi velocemente significa che la collezione è un po' disorganizzata… Diamo un'occhiata. Immaginate di avere un file system (che in realtà stiamo suddividendo tra i torrent): La prima directory, <code>/repository</code>, è la parte più strutturata di questo. Questa directory contiene i cosiddetti “mille dir”: directory ciascuna con migliaia di file, che sono numerati incrementali nel database. La directory <code>0</code> contiene file con comic_id da 0 a 999, e così via. Questo è lo stesso schema che Library Genesis ha utilizzato per le sue collezioni di narrativa e saggistica. L'idea è che ogni “mille dir” venga automaticamente trasformato in un torrent non appena è pieno. Tuttavia, l'operatore di Libgen.li non ha mai creato torrent per questa collezione, e quindi i mille dir probabilmente sono diventati scomodi, e hanno lasciato il posto a “dir non ordinati”. Questi sono <code>/comics0</code> fino a <code>/comics4</code>. Contengono tutte strutture di directory uniche, che probabilmente avevano senso per raccogliere i file, ma non hanno molto senso per noi ora. Fortunatamente, i metadata si riferiscono ancora direttamente a tutti questi file, quindi la loro organizzazione di archiviazione su disco non ha effettivamente importanza! I metadata sono disponibili sotto forma di un database MySQL. Questo può essere scaricato direttamente dal sito web di Libgen.li, ma lo renderemo disponibile anche in un torrent, insieme alla nostra tabella con tutti gli hash MD5. <q>La Dott.ssa Barbara Gordon cerca di perdersi nel mondo banale della biblioteca…</q> Fork di Libgen Innanzitutto, un po' di contesto. Potreste conoscere Library Genesis per la loro epica collezione di libri. Poche persone sanno che i volontari di Library Genesis hanno creato altri progetti, come una vasta collezione di riviste e documenti standard, un backup completo di Sci-Hub (in collaborazione con la fondatrice di Sci-Hub, Alexandra Elbakyan) e, in effetti, una collezione massiccia di fumetti. Ad un certo punto, diversi operatori degli specchi di Library Genesis hanno preso strade separate, il che ha dato origine alla situazione attuale di avere diversi "fork", tutti ancora con il nome Library Genesis. Il fork Libgen.li ha in modo univoco questa collezione di fumetti, oltre a una considerevole collezione di riviste (su cui stiamo anche lavorando). Raccolta fondi Stiamo rilasciando questi dati in alcuni grandi blocchi. Il primo torrent è di <code>/comics0</code>, che abbiamo messo in un enorme file .tar da 12TB. È meglio per il tuo hard disk e il software torrent rispetto a una miriade di file più piccoli. Come parte di questo rilascio, stiamo facendo una raccolta fondi. Stiamo cercando di raccogliere $20,000 per coprire i costi operativi e di contrattazione per questa collezione, oltre a consentire progetti in corso e futuri. Abbiamo alcuni <em>enormi</em> in lavorazione. <em>Chi sto supportando con la mia donazione?</em> In breve: stiamo facendo il backup di tutta la conoscenza e la cultura dell'umanità, rendendola facilmente accessibile. Tutto il nostro codice e i dati sono open source, siamo un progetto gestito completamente da volontari, e finora abbiamo salvato 125TB di libri (oltre ai torrent esistenti di Libgen e Scihub). In definitiva, stiamo costruendo un volano che consente e incentiva le persone a trovare, scansionare e fare il backup di tutti i libri del mondo. Scriveremo del nostro piano maestro in un post futuro. :) Se doni per un abbonamento di 12 mesi "Ammirevole archivista" ($780), puoi <strong>“adottare un torrent”</strong>, il che significa che metteremo il tuo nome utente o messaggio nel nome di uno dei torrent! Puoi donare andando su <a %(wikipedia_annas_archive)s>Archivio di Anna</a> e cliccando sul pulsante “Dona”. Stiamo anche cercando più volontari: ingegneri software, ricercatori di sicurezza, esperti di commercio anonimo e traduttori. Puoi anche supportarci fornendo servizi di hosting. E naturalmente, per favore condividi i nostri torrent! Grazie a tutti coloro che ci hanno già supportato così generosamente! State davvero facendo la differenza. Ecco i torrent rilasciati finora (stiamo ancora elaborando il resto): Tutti i torrent possono essere trovati su <a %(wikipedia_annas_archive)s>Archivio di Anna</a> sotto “Datasets” (non colleghiamo direttamente lì, quindi i link a questo blog non vengono rimossi da Reddit, Twitter, ecc.). Da lì, segui il link al sito web di Tor. <a %(news_ycombinator)s>Discuti su Hacker News</a> Cosa c'è dopo? Un gruppo di torrent è ottimo per la conservazione a lungo termine, ma non tanto per l'accesso quotidiano. Lavoreremo con partner di hosting per mettere tutti questi dati sul web (poiché l'Archivio di Anna non ospita nulla direttamente). Naturalmente, potrai trovare questi link di download sull'Archivio di Anna. Invitiamo anche tutti a fare qualcosa con questi dati! Aiutaci ad analizzarli meglio, deduplicarli, metterli su IPFS, remixarli, addestrare i tuoi modelli di IA con essi, e così via. Sono tutti tuoi, e non vediamo l'ora di vedere cosa farai con essi. Infine, come detto prima, abbiamo ancora alcuni rilasci enormi in arrivo (se <em>qualcuno</em> potesse <em>accidentalmente</em> inviarci un dump di un <em>certo</em> database ACS4, sapete dove trovarci...), oltre a costruire il volano per fare il backup di tutti i libri del mondo. Quindi restate sintonizzati, abbiamo appena iniziato. - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) La più grande biblioteca ombra di fumetti è probabilmente quella di un particolare fork di Library Genesis: Libgen.li. L'unico amministratore che gestisce quel sito è riuscito a raccogliere una collezione di fumetti incredibile di oltre 2 milioni di file, per un totale di oltre 95TB. Tuttavia, a differenza di altre collezioni di Library Genesis, questa non era disponibile in blocco tramite torrent. Potevi accedere a questi fumetti solo individualmente tramite il suo server personale lento — un singolo punto di fallimento. Fino ad oggi! In questo post vi parleremo di più di questa collezione e della nostra raccolta fondi per supportare ulteriormente questo lavoro. Archivio di Anna ha eseguito il backup della più grande biblioteca ombra di fumetti del mondo (95TB) — puoi aiutare a condividerla La più grande biblioteca ombra di fumetti del mondo aveva un singolo punto di fallimento... fino ad oggi. Avviso: questo post del blog è stato deprecato. Abbiamo deciso che IPFS non è ancora pronto per il grande pubblico. Continueremo a collegarci ai file su IPFS dall'Archivio di Anna quando possibile, ma non lo ospiteremo più noi stessi, né raccomandiamo ad altri di fare mirror utilizzando IPFS. Si prega di consultare la nostra pagina Torrents se si desidera aiutare a preservare la nostra collezione. Mettere 5.998.794 libri su IPFS Una moltiplicazione di copie Tornando alla nostra domanda originale: come possiamo affermare di preservare le nostre collezioni in perpetuo? Il problema principale qui è che la nostra collezione è <a %(torrents_stats)s>cresciuta</a> rapidamente, estraendo e rendendo open source alcune collezioni massive (oltre al lavoro straordinario già svolto da altre biblioteche ombra di dati aperti come Sci-Hub e Library Genesis). Questa crescita dei dati rende più difficile il mirroring delle collezioni in tutto il mondo. L'archiviazione dei dati è costosa! Ma siamo ottimisti, soprattutto osservando le seguenti tre tendenze. La <a %(annas_archive_stats)s>dimensione totale</a> delle nostre collezioni, negli ultimi mesi, suddivisa per numero di seeders di torrent. Tendenze dei prezzi degli HDD da fonti diverse (clicca per visualizzare lo studio). <a %(critical_window_chinese)s>Versione cinese 中文版</a>, discuti su <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Abbiamo raccolto i frutti più facili Questo segue direttamente dalle nostre priorità discusse sopra. Preferiamo lavorare prima sulla liberazione di grandi collezioni. Ora che abbiamo assicurato alcune delle collezioni più grandi del mondo, ci aspettiamo che la nostra crescita sia molto più lenta. C'è ancora una lunga coda di collezioni più piccole, e nuovi libri vengono scansionati o pubblicati ogni giorno, ma il tasso sarà probabilmente molto più lento. Potremmo ancora raddoppiare o addirittura triplicare di dimensioni, ma in un periodo di tempo più lungo. Miglioramenti dell'OCR. Priorità Codice software per scienza e ingegneria Versioni fittizie o di intrattenimento di tutto quanto sopra Dati geografici (ad es. mappe, rilevamenti geologici) Dati interni di aziende o governi (fughe di notizie) Dati di misurazione come misurazioni scientifiche, dati economici, rapporti aziendali Record di metadata in generale (di non-fiction e fiction; di altri media, arte, persone, ecc.; incluse recensioni) Libri di saggistica Riviste non-fiction, giornali, manuali Trascrizioni non-fiction di discorsi, documentari, podcast Dati organici come sequenze di DNA, semi di piante o campioni microbici Articoli accademici, riviste, rapporti Siti web di scienza e ingegneria, discussioni online Trascrizioni di procedimenti legali o giudiziari Unicamente a rischio di distruzione (ad es. per guerra, tagli ai finanziamenti, cause legali o persecuzione politica) Rare Unicamente trascurate Perché ci preoccupiamo così tanto di articoli e libri? Mettiamo da parte la nostra convinzione fondamentale nella preservazione in generale — potremmo scrivere un altro post su questo. Quindi perché articoli e libri specificamente? La risposta è semplice: <strong>densità di informazione</strong>. Per megabyte di archiviazione, il testo scritto conserva la maggior quantità di informazioni rispetto a tutti i media. Mentre ci preoccupiamo sia della conoscenza che della cultura, ci preoccupiamo di più della prima. In generale, troviamo una gerarchia di densità di informazione e importanza della preservazione che appare più o meno così: La classifica in questo elenco è in qualche modo arbitraria — diversi elementi sono pari o ci sono disaccordi all'interno del nostro team — e probabilmente stiamo dimenticando alcune categorie importanti. Ma questo è approssimativamente come diamo priorità. Alcuni di questi elementi sono troppo diversi dagli altri per preoccuparci (o sono già gestiti da altre istituzioni), come i dati organici o i dati geografici. Ma la maggior parte degli elementi in questo elenco è effettivamente importante per noi. Un altro grande fattore nella nostra priorità è quanto è a rischio un determinato lavoro. Preferiamo concentrarci su opere che sono: Infine, ci interessa la scala. Abbiamo tempo e denaro limitati, quindi preferiamo passare un mese a salvare 10.000 libri piuttosto che 1.000 libri — se sono ugualmente preziosi e a rischio. <em><q>Ciò che è perduto non può essere recuperato; ma salviamo ciò che rimane: non con volte e serrature che li proteggono dagli occhi e dall'uso del pubblico, consegnandoli al logorio del tempo, ma con una tale moltiplicazione di copie, da metterle al di là della portata degli incidenti.</q></em><br>— Thomas Jefferson, 1791 Biblioteche ombra Il codice può essere open source su Github, ma Github nel suo insieme non può essere facilmente mirrorato e quindi preservato (anche se in questo caso particolare ci sono copie sufficientemente distribuite della maggior parte dei repository di codice) I record di metadata possono essere visualizzati liberamente sul sito Worldcat, ma non scaricati in massa (fino a quando non li abbiamo <a %(worldcat_scrape)s>estratti</a>) Reddit è gratuito da usare, ma ha recentemente introdotto misure anti-scraping rigorose, a seguito dell'addestramento di LLM affamati di dati (ne parleremo più avanti) Ci sono molte organizzazioni che hanno missioni simili e priorità simili. In effetti, ci sono biblioteche, archivi, laboratori, musei e altre istituzioni incaricate della conservazione di questo tipo. Molte di queste sono ben finanziate, da governi, individui o aziende. Ma hanno un enorme punto cieco: il sistema legale. Qui risiede il ruolo unico delle biblioteche ombra e il motivo per cui esiste l'Archivio di Anna. Possiamo fare cose che altre istituzioni non sono autorizzate a fare. Ora, non è (spesso) che possiamo archiviare materiali che sono illegali da conservare altrove. No, è legale in molti luoghi costruire un archivio con qualsiasi libro, documento, rivista e così via. Ma ciò che spesso manca agli archivi legali è <strong>ridondanza e longevità</strong>. Esistono libri di cui esiste solo una copia in qualche biblioteca fisica da qualche parte. Esistono record di metadata custoditi da una singola azienda. Esistono giornali conservati solo su microfilm in un unico archivio. Le biblioteche possono subire tagli ai finanziamenti, le aziende possono fallire, gli archivi possono essere bombardati e bruciati fino a terra. Questo non è ipotetico: accade continuamente. La cosa che possiamo fare in modo unico su Archivio di Anna è conservare molte copie delle opere, su larga scala. Possiamo raccogliere articoli, libri, riviste e altro, e distribuirli in massa. Attualmente lo facciamo tramite torrent, ma le tecnologie esatte non importano e cambieranno nel tempo. La parte importante è distribuire molte copie in tutto il mondo. Questa citazione di oltre 200 anni fa è ancora valida: Una breve nota sul dominio pubblico. Poiché Archivio di Anna si concentra in modo unico su attività che sono illegali in molti luoghi del mondo, non ci preoccupiamo delle collezioni ampiamente disponibili, come i libri di dominio pubblico. Le entità legali spesso se ne prendono già cura adeguatamente. Tuttavia, ci sono considerazioni che a volte ci portano a lavorare su collezioni pubblicamente disponibili: - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. I costi di archiviazione continuano a diminuire esponenzialmente 3. Miglioramenti nella densità delle informazioni Attualmente conserviamo i libri nei formati grezzi in cui ci vengono forniti. Certo, sono compressi, ma spesso sono ancora grandi scansioni o fotografie di pagine. Fino ad ora, le uniche opzioni per ridurre la dimensione totale della nostra collezione sono state una compressione più aggressiva o la deduplicazione. Tuttavia, per ottenere risparmi significativi, entrambe sono troppo lossy per i nostri gusti. La compressione pesante delle foto può rendere il testo appena leggibile. E la deduplicazione richiede un'elevata sicurezza che i libri siano esattamente gli stessi, il che è spesso troppo impreciso, specialmente se i contenuti sono gli stessi ma le scansioni sono fatte in occasioni diverse. C'è sempre stata una terza opzione, ma la sua qualità è stata così pessima che non l'abbiamo mai considerata: <strong>OCR, o Riconoscimento Ottico dei Caratteri</strong>. Questo è il processo di conversione delle foto in testo semplice, utilizzando l'IA per rilevare i caratteri nelle foto. Gli strumenti per questo esistono da tempo e sono stati abbastanza decenti, ma "abbastanza decenti" non è sufficiente per scopi di conservazione. Tuttavia, i recenti modelli di deep learning multimodali hanno fatto progressi estremamente rapidi, sebbene ancora a costi elevati. Ci aspettiamo che sia l'accuratezza che i costi migliorino notevolmente nei prossimi anni, al punto che diventerà realistico applicarli all'intera nostra biblioteca. Quando ciò accadrà, probabilmente conserveremo ancora i file originali, ma in aggiunta potremmo avere una versione molto più piccola della nostra biblioteca che la maggior parte delle persone vorrà mirrorare. Il punto è che il testo grezzo stesso si comprime ancora meglio ed è molto più facile da deduplicare, offrendoci ancora più risparmi. In generale, non è irrealistico aspettarsi almeno una riduzione di 5-10 volte della dimensione totale dei file, forse anche di più. Anche con una riduzione conservativa di 5 volte, ci aspetteremmo <strong>$1,000–$3,000 in 10 anni anche se la nostra biblioteca triplicasse di dimensioni</strong>. Al momento della scrittura, i <a %(diskprices)s>prezzi dei dischi</a> per TB sono circa $12 per dischi nuovi, $8 per dischi usati e $4 per nastro. Se siamo conservatori e guardiamo solo ai dischi nuovi, significa che archiviare un petabyte costa circa $12.000. Se assumiamo che la nostra biblioteca triplicherà da 900TB a 2,7PB, ciò significherebbe $32.400 per mirrorare l'intera biblioteca. Aggiungendo elettricità, costo di altro hardware, e così via, arrotondiamo a $40.000. O con nastro più come $15.000–$20.000. Da un lato <strong>$15.000–$40.000 per la somma di tutta la conoscenza umana è un affare</strong>. Dall'altro, è un po' ripido aspettarsi tonnellate di copie complete, specialmente se vorremmo anche che quelle persone continuassero a seminare i loro torrent a beneficio degli altri. Questo è oggi. Ma il progresso avanza: I costi degli hard disk per TB sono stati ridotti di circa un terzo negli ultimi 10 anni e probabilmente continueranno a diminuire a un ritmo simile. Il nastro sembra seguire una traiettoria simile. I prezzi degli SSD stanno scendendo ancora più velocemente e potrebbero superare i prezzi degli HDD entro la fine del decennio. Se questo si mantiene, allora tra 10 anni potremmo guardare a soli $5.000–$13.000 per mirrorare l'intera collezione (1/3), o anche meno se cresciamo meno in dimensioni. Anche se ancora una grande somma di denaro, sarà accessibile per molte persone. E potrebbe essere ancora meglio grazie al prossimo punto… All'Archivio di Anna, ci viene spesso chiesto come possiamo affermare di preservare le nostre collezioni in perpetuo, quando la dimensione totale si sta già avvicinando a 1 Petabyte (1000 TB), e continua a crescere. In questo articolo esamineremo la nostra filosofia, e vedremo perché il prossimo decennio è critico per la nostra missione di preservare la conoscenza e la cultura dell'umanità. Finestra critica Se queste previsioni sono accurate, <strong>basta aspettare un paio d'anni</strong> prima che l'intera nostra collezione venga ampiamente mirrorata. Così, nelle parole di Thomas Jefferson, "posta al di là della portata degli incidenti". Purtroppo, l'avvento degli LLM e il loro addestramento affamato di dati ha messo molti detentori di diritti d'autore sulla difensiva. Ancora più di quanto già non fossero. Molti siti web stanno rendendo più difficile lo scraping e l'archiviazione, le cause legali sono in aumento, e nel frattempo le biblioteche fisiche e gli archivi continuano a essere trascurati. Possiamo solo aspettarci che queste tendenze continuino a peggiorare e che molte opere vadano perse ben prima di entrare nel pubblico dominio. <strong>Siamo alla vigilia di una rivoluzione nella conservazione, ma <q>ciò che è perso non può essere recuperato.</q></strong> Abbiamo una finestra critica di circa 5-10 anni durante la quale è ancora piuttosto costoso gestire una biblioteca ombra e creare molti mirror in tutto il mondo, e durante la quale l'accesso non è ancora stato completamente chiuso. Se riusciamo a colmare questa finestra, allora avremo davvero preservato la conoscenza e la cultura dell'umanità in perpetuo. Non dovremmo lasciare che questo tempo vada sprecato. Non dovremmo lasciare che questa finestra critica si chiuda su di noi. Andiamo. La finestra critica delle biblioteche ombra Come possiamo affermare di preservare le nostre collezioni in perpetuo, quando stanno già avvicinandosi a 1 PB? Collezione Alcune informazioni aggiuntive sulla collezione. <a %(duxiu)s>Duxiu</a> è un enorme database di libri scansionati, creato dal <a %(chaoxing)s>SuperStar Digital Library Group</a>. La maggior parte sono libri accademici, scansionati per renderli disponibili digitalmente a università e biblioteche. Per il nostro pubblico anglofono, <a %(library_princeton)s>Princeton</a> e l'<a %(guides_lib_uw)s>Università di Washington</a> offrono buone panoramiche. C'è anche un eccellente articolo che fornisce maggiori dettagli: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercalo nell'Archivio di Anna). I libri di Duxiu sono stati a lungo piratati su internet cinese. Di solito vengono venduti per meno di un dollaro dai rivenditori. Vengono tipicamente distribuiti utilizzando l'equivalente cinese di Google Drive, che è stato spesso hackerato per consentire più spazio di archiviazione. Alcuni dettagli tecnici possono essere trovati <a %(github_duty_machine)s>qui</a> e <a %(github_821_github_io)s>qui</a>. Sebbene i libri siano stati distribuiti in modo semi-pubblico, è piuttosto difficile ottenerli in blocco. Avevamo questo obiettivo in cima alla nostra lista di cose da fare, e abbiamo allocato diversi mesi di lavoro a tempo pieno per questo. Tuttavia, recentemente un incredibile, straordinario e talentuoso volontario ci ha contattato, dicendoci che aveva già fatto tutto questo lavoro — a grande costo. Ha condiviso con noi l'intera collezione, senza aspettarsi nulla in cambio, tranne la garanzia di una conservazione a lungo termine. Veramente notevole. Ha accettato di chiedere aiuto in questo modo per ottenere l'OCR della collezione. La collezione è composta da 7.543.702 file. Questo è più della non-fiction di Library Genesis (circa 5,3 milioni). La dimensione totale dei file è di circa 359TB (326TiB) nella sua forma attuale. Siamo aperti ad altre proposte e idee. Contattaci. Dai un'occhiata all'Archivio di Anna per ulteriori informazioni sulle nostre collezioni, gli sforzi di conservazione e come puoi aiutare. Grazie! Pagine di esempio Per dimostrarci che hai una buona pipeline, ecco alcune pagine di esempio da cui iniziare, tratte da un libro sui superconduttori. La tua pipeline dovrebbe gestire correttamente matematica, tabelle, grafici, note a piè di pagina, e così via. Invia le tue pagine elaborate alla nostra email. Se sembrano buone, te ne invieremo altre in privato, e ci aspettiamo che tu sia in grado di eseguire rapidamente la tua pipeline anche su quelle. Una volta soddisfatti, possiamo fare un accordo. - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versione cinese 中文版</a>, <a %(news_ycombinator)s>Discuti su Hacker News</a> Questo è un breve post sul blog. Stiamo cercando un'azienda o un'istituzione che ci aiuti con l'OCR e l'estrazione del testo per una collezione massiccia che abbiamo acquisito, in cambio di un accesso esclusivo anticipato. Dopo il periodo di embargo, rilasceremo ovviamente l'intera collezione. Testi accademici di alta qualità sono estremamente utili per l'addestramento degli LLM. Anche se la nostra collezione è cinese, dovrebbe essere utile anche per l'addestramento degli LLM in inglese: i modelli sembrano codificare concetti e conoscenze indipendentemente dalla lingua di origine. Per questo, il testo deve essere estratto dalle scansioni. Cosa ottiene l'Archivio di Anna da tutto ciò? La ricerca a testo completo dei libri per i suoi utenti. Poiché i nostri obiettivi sono allineati con quelli degli sviluppatori di LLM, stiamo cercando un collaboratore. Siamo disposti a darti <strong>accesso anticipato esclusivo a questa collezione in blocco per 1 anno</strong>, se puoi fare un OCR e un'estrazione del testo adeguati. Se sei disposto a condividere con noi l'intero codice del tuo pipeline, saremmo disposti a mantenere la collezione riservata per un periodo più lungo. Accesso esclusivo per le aziende LLM alla più grande collezione di libri di saggistica cinese al mondo <em><strong>TL;DR:</strong> L'Archivio di Anna ha acquisito una collezione unica di 7,5 milioni / 350TB di libri di saggistica cinese — più grande di Library Genesis. Siamo disposti a dare a un'azienda LLM l'accesso esclusivo, in cambio di un OCR di alta qualità e dell'estrazione del testo.</em> Architettura del sistema Quindi supponiamo che tu abbia trovato alcune aziende disposte a ospitare il tuo sito web senza chiuderti — chiamiamoli “fornitori amanti della libertà” 😄. Scoprirai rapidamente che ospitare tutto con loro è piuttosto costoso, quindi potresti voler trovare alcuni “fornitori economici” e fare l'hosting effettivo lì, passando attraverso i fornitori amanti della libertà. Se lo fai bene, i fornitori economici non sapranno mai cosa stai ospitando e non riceveranno mai reclami. Con tutti questi fornitori c'è il rischio che ti chiudano comunque, quindi hai anche bisogno di ridondanza. Abbiamo bisogno di questo a tutti i livelli del nostro stack. Una compagnia in qualche modo amante della libertà che si è messa in una posizione interessante è Cloudflare. Hanno <a %(blog_cloudflare)s>sostenuto</a> di non essere un fornitore di hosting, ma un servizio, come un ISP. Pertanto, non sono soggetti a richieste di rimozione DMCA o altre, e inoltrano qualsiasi richiesta al tuo effettivo fornitore di hosting. Sono arrivati al punto di andare in tribunale per proteggere questa struttura. Possiamo quindi usarli come un altro strato di caching e protezione. Cloudflare non accetta pagamenti anonimi, quindi possiamo utilizzare solo il loro piano gratuito. Questo significa che non possiamo utilizzare le loro funzionalità di bilanciamento del carico o failover. Pertanto, <a %(annas_archive_l255)s>abbiamo implementato questo noi stessi</a> a livello di dominio. Al caricamento della pagina, il browser controllerà se il dominio corrente è ancora disponibile e, in caso contrario, riscriverà tutti gli URL su un altro dominio. Poiché Cloudflare memorizza nella cache molte pagine, ciò significa che un utente può atterrare sul nostro dominio principale, anche se il server proxy è inattivo, e poi al clic successivo essere spostato su un altro dominio. Abbiamo ancora anche preoccupazioni operative normali da affrontare, come il monitoraggio della salute del server, la registrazione degli errori del backend e del frontend, e così via. La nostra architettura di failover consente una maggiore robustezza anche su questo fronte, ad esempio eseguendo un set completamente diverso di server su uno dei domini. Possiamo persino eseguire versioni precedenti del codice e dei datasets su questo dominio separato, nel caso in cui un bug critico nella versione principale passi inosservato. Possiamo anche proteggerci contro Cloudflare che si rivolta contro di noi, rimuovendolo da uno dei domini, come questo dominio separato. Sono possibili diverse permutazioni di queste idee. Conclusione È stata un'esperienza interessante imparare a configurare un motore di ricerca per una biblioteca ombra robusto e resiliente. Ci sono molti altri dettagli da condividere in post futuri, quindi fatemi sapere di cosa vorreste sapere di più! Come sempre, stiamo cercando donazioni per supportare questo lavoro, quindi assicuratevi di visitare la pagina delle Donazioni su Archivio di Anna. Stiamo anche cercando altri tipi di supporto, come sovvenzioni, sponsor a lungo termine, fornitori di sistemi di pagamento ad alto rischio, forse anche annunci (di buon gusto!). E se volete contribuire con il vostro tempo e le vostre competenze, siamo sempre alla ricerca di sviluppatori, traduttori e così via. Grazie per il vostro interesse e supporto. Gettoni di innovazione Iniziamo con il nostro stack tecnologico. È volutamente noioso. Usiamo Flask, MariaDB ed ElasticSearch. E questo è letteralmente tutto. La ricerca è in gran parte un problema risolto e non intendiamo reinventarla. Inoltre, dobbiamo spendere i nostri <a %(mcfunley)s>gettoni di innovazione</a> su qualcos'altro: non essere chiusi dalle autorità. Quindi quanto è legale o illegale l'Archivio di Anna esattamente? Questo dipende principalmente dalla giurisdizione legale. La maggior parte dei paesi crede in una qualche forma di copyright, il che significa che a persone o aziende viene assegnato un monopolio esclusivo su determinati tipi di opere per un certo periodo di tempo. Come nota a margine, all'Archivio di Anna crediamo che, sebbene ci siano alcuni benefici, nel complesso il copyright sia un netto negativo per la società — ma questa è una storia per un'altra volta. Questo monopolio esclusivo su determinate opere significa che è illegale per chiunque al di fuori di questo monopolio distribuire direttamente quelle opere — inclusi noi. Ma l'Archivio di Anna è un motore di ricerca che non distribuisce direttamente quelle opere (almeno non sul nostro sito web clearnet), quindi dovremmo essere a posto, giusto? Non esattamente. In molte giurisdizioni non è solo illegale distribuire opere protette da copyright, ma anche collegarsi a luoghi che lo fanno. Un classico esempio di questo è la legge DMCA degli Stati Uniti. Questo è l'estremo più severo dello spettro. All'altro estremo dello spettro potrebbero teoricamente esserci paesi senza alcuna legge sul copyright, ma questi non esistono realmente. Praticamente ogni paese ha una qualche forma di legge sul copyright nei libri. L'applicazione è un'altra storia. Ci sono molti paesi con governi che non si preoccupano di far rispettare la legge sul copyright. Ci sono anche paesi tra i due estremi, che vietano la distribuzione di opere protette da copyright, ma non vietano di collegarsi a tali opere. Un'altra considerazione è a livello aziendale. Se un'azienda opera in una giurisdizione che non si preoccupa del copyright, ma l'azienda stessa non è disposta a correre alcun rischio, allora potrebbero chiudere il tuo sito web non appena qualcuno si lamenta. Infine, una grande considerazione sono i pagamenti. Poiché dobbiamo rimanere anonimi, non possiamo utilizzare metodi di pagamento tradizionali. Questo ci lascia con le criptovalute, e solo un piccolo sottoinsieme di aziende le supporta (ci sono carte di debito virtuali pagate con criptovaluta, ma spesso non sono accettate). - Anna e il team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Gestisco <a %(wikipedia_annas_archive)s>l'Archivio di Anna</a>, il più grande motore di ricerca open-source senza scopo di lucro al mondo per <a %(wikipedia_shadow_library)s>biblioteche ombra</a>, come Sci-Hub, Library Genesis e Z-Library. Il nostro obiettivo è rendere la conoscenza e la cultura facilmente accessibili, e alla fine costruire una comunità di persone che insieme archiviano e preservano <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tutti i libri del mondo</a>. In questo articolo mostrerò come gestiamo questo sito web, e le sfide uniche che comporta operare un sito web con uno status legale discutibile, poiché non esiste un “AWS per le associazioni di beneficenza ombra”. <em>Controlla anche l'articolo correlato <a %(blog_how_to_become_a_pirate_archivist)s>Come diventare un archivista pirata</a>.</em> Come gestire una biblioteca ombra: operazioni presso l'Archivio di Anna Non esiste <q>AWS per le associazioni di beneficenza ombra,</q> quindi come gestiamo l'Archivio di Anna? Strumenti Server applicazioni: Flask, MariaDB, ElasticSearch, Docker. Sviluppo: Gitlab, Weblate, Zulip. Gestione server: Ansible, Checkmk, UFW. Hosting statico Onion: Tor, Nginx. Server proxy: Varnish. Vediamo quali strumenti usiamo per realizzare tutto questo. Questo è in continua evoluzione man mano che ci imbattiamo in nuovi problemi e troviamo nuove soluzioni. Ci sono alcune decisioni su cui abbiamo riflettuto a lungo. Una riguarda la comunicazione tra server: usavamo Wireguard per questo, ma abbiamo scoperto che occasionalmente smette di trasmettere dati, o trasmette dati solo in una direzione. Questo è successo con diverse configurazioni di Wireguard che abbiamo provato, come <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Abbiamo anche provato a tunnelizzare le porte tramite SSH, usando autossh e sshuttle, ma abbiamo incontrato <a %(github_sshuttle)s>problemi lì</a> (anche se non è ancora chiaro se autossh soffra di problemi TCP-over-TCP o meno — mi sembra solo una soluzione traballante, ma forse va bene?). Invece, siamo tornati alle connessioni dirette tra server, nascondendo che un server è in esecuzione su fornitori economici usando il filtraggio IP con UFW. Questo ha lo svantaggio che Docker non funziona bene con UFW, a meno che non si usi <code>network_mode: "host"</code>. Tutto ciò è un po' più soggetto a errori, perché esporrai il tuo server a internet con una piccola configurazione errata. Forse dovremmo tornare ad autossh — un feedback sarebbe molto apprezzato qui. Abbiamo anche riflettuto su Varnish vs. Nginx. Attualmente ci piace Varnish, ma ha le sue stranezze e spigoli. Lo stesso vale per Checkmk: non lo amiamo, ma funziona per ora. Weblate è stato accettabile ma non incredibile — a volte temo che perderà i miei dati ogni volta che provo a sincronizzarlo con il nostro repository git. Flask è stato buono nel complesso, ma ha alcune stranezze che hanno richiesto molto tempo per essere risolte, come configurare domini personalizzati o problemi con la sua integrazione SqlAlchemy. Finora gli altri strumenti sono stati ottimi: non abbiamo lamentele serie su MariaDB, ElasticSearch, Gitlab, Zulip, Docker e Tor. Tutti questi hanno avuto alcuni problemi, ma nulla di troppo serio o che richieda molto tempo. Comunità La prima sfida potrebbe essere sorprendente. Non è un problema tecnico, né un problema legale. È un problema psicologico: fare questo lavoro nell'ombra può essere incredibilmente solitario. A seconda di ciò che stai pianificando di fare e del tuo modello di minaccia, potresti dover essere molto attento. All'estremità dello spettro abbiamo persone come Alexandra Elbakyan*, la fondatrice di Sci-Hub, che è molto aperta sulle sue attività. Ma è ad alto rischio di essere arrestata se dovesse visitare un paese occidentale in questo momento, e potrebbe affrontare decenni di carcere. È un rischio che saresti disposto a correre? Noi siamo all'altra estremità dello spettro; stiamo molto attenti a non lasciare alcuna traccia e ad avere una forte sicurezza operativa. * Come menzionato su HN da "ynno", inizialmente Alexandra non voleva essere conosciuta: "I suoi server erano configurati per emettere messaggi di errore dettagliati da PHP, incluso il percorso completo del file sorgente difettoso, che era sotto la directory /home/<USER>" Quindi, usa nomi utente casuali sui computer che usi per queste cose, nel caso in cui configuri qualcosa in modo errato. Questa segretezza, tuttavia, comporta un costo psicologico. La maggior parte delle persone ama essere riconosciuta per il lavoro che svolge, eppure non puoi prenderti alcun merito per questo nella vita reale. Anche le cose semplici possono essere difficili, come gli amici che ti chiedono cosa hai fatto di recente (a un certo punto "smanettare con il mio NAS / homelab" diventa vecchio). Ecco perché è così importante trovare una comunità. Puoi rinunciare a un po' di sicurezza operativa confidandoti con alcuni amici molto stretti, di cui sai di poterti fidare profondamente. Anche allora, fai attenzione a non mettere nulla per iscritto, nel caso in cui debbano consegnare le loro email alle autorità, o se i loro dispositivi sono compromessi in qualche altro modo. Meglio ancora è trovare alcuni compagni pirati. Se i tuoi amici stretti sono interessati a unirsi a te, fantastico! Altrimenti, potresti essere in grado di trovare altri online. Purtroppo questa è ancora una comunità di nicchia. Finora abbiamo trovato solo una manciata di altri che sono attivi in questo spazio. Buoni punti di partenza sembrano essere i forum di Library Genesis e r/DataHoarder. Anche l'Archive Team ha individui affini, sebbene operino entro i limiti della legge (anche se in alcune aree grigie della legge). Le tradizionali scene "warez" e di pirateria hanno anche persone che pensano in modi simili. Siamo aperti a idee su come promuovere la comunità ed esplorare nuove idee. Sentitevi liberi di contattarci su Twitter o Reddit. Forse potremmo ospitare una sorta di forum o gruppo di chat. Una sfida è che questo può facilmente essere censurato quando si utilizzano piattaforme comuni, quindi dovremmo ospitarlo noi stessi. C'è anche un compromesso tra avere queste discussioni completamente pubbliche (più potenziale di coinvolgimento) e renderle private (non far sapere ai potenziali "bersagli" che stiamo per raccoglierli). Dovremo rifletterci su. Fateci sapere se siete interessati a questo! Conclusione Speriamo che questo sia utile per i nuovi archivisti pirata. Siamo entusiasti di darvi il benvenuto in questo mondo, quindi non esitate a contattarci. Conserviamo quanta più conoscenza e cultura del mondo possibile e facciamone il mirror ovunque. Progetti 4. Selezione dei dati Spesso puoi utilizzare i metadata per determinare un sottoinsieme ragionevole di dati da scaricare. Anche se alla fine vuoi scaricare tutti i dati, può essere utile dare priorità agli elementi più importanti per primi, nel caso in cui tu venga rilevato e le difese vengano migliorate, o perché avresti bisogno di acquistare più dischi, o semplicemente perché qualcosa d'altro si presenta nella tua vita prima che tu possa scaricare tutto. Ad esempio, una collezione potrebbe avere più edizioni della stessa risorsa di base (come un libro o un film), dove una è contrassegnata come di migliore qualità. Salvare prima quelle edizioni avrebbe molto senso. Potresti alla fine voler salvare tutte le edizioni, poiché in alcuni casi i metadata potrebbero essere etichettati in modo errato, o potrebbero esserci compromessi sconosciuti tra le edizioni (ad esempio, la "migliore edizione" potrebbe essere la migliore in molti modi ma peggiore in altri, come un film con una risoluzione più alta ma senza sottotitoli). Puoi anche cercare nel tuo database di metadata per trovare cose interessanti. Qual è il file più grande che è ospitato, e perché è così grande? Qual è il file più piccolo? Ci sono schemi interessanti o inaspettati quando si tratta di certe categorie, lingue, e così via? Ci sono titoli duplicati o molto simili? Ci sono schemi su quando i dati sono stati aggiunti, come un giorno in cui molti file sono stati aggiunti contemporaneamente? Spesso puoi imparare molto osservando il dataset in modi diversi. Nel nostro caso, abbiamo deduplicato i libri di Z-Library rispetto agli hash md5 in Library Genesis, risparmiando così molto tempo di download e spazio su disco. Questa è una situazione piuttosto unica però. Nella maggior parte dei casi non ci sono database completi di quali file sono già adeguatamente preservati da altri pirati. Questo di per sé è una grande opportunità per qualcuno là fuori. Sarebbe fantastico avere una panoramica regolarmente aggiornata di cose come musica e film che sono già ampiamente condivisi su siti torrent, e sono quindi a bassa priorità da includere nei mirror pirata. 6. Distribuzione Hai i dati, dandoti così il possesso del primo mirror pirata al mondo del tuo obiettivo (molto probabilmente). In molti modi la parte più difficile è finita, ma la parte più rischiosa è ancora davanti a te. Dopotutto, finora sei stato furtivo; volando sotto il radar. Tutto quello che dovevi fare era usare una buona VPN per tutto il tempo, non inserire i tuoi dati personali in nessun modulo (ovviamente), e forse usare una sessione del browser speciale (o anche un computer diverso). Ora devi distribuire i dati. Nel nostro caso volevamo prima contribuire i libri a Library Genesis, ma poi abbiamo rapidamente scoperto le difficoltà in questo (ordinamento fiction vs non-fiction). Quindi abbiamo deciso di distribuire utilizzando torrent in stile Library Genesis. Se hai l'opportunità di contribuire a un progetto esistente, allora questo potrebbe farti risparmiare molto tempo. Tuttavia, attualmente non ci sono molti mirror pirata ben organizzati là fuori. Quindi diciamo che decidi di distribuire i torrent da solo. Cerca di mantenere quei file piccoli, in modo che siano facili da mirroring su altri siti web. Dovrai quindi seminare i torrent da solo, rimanendo comunque anonimo. Puoi usare una VPN (con o senza port forwarding), o pagare con Bitcoin mescolati per un Seedbox. Se non sai cosa significano alcuni di questi termini, avrai un bel po' di letture da fare, poiché è importante che tu comprenda i compromessi di rischio qui. Puoi ospitare i file torrent stessi su siti torrent esistenti. Nel nostro caso, abbiamo scelto di ospitare effettivamente un sito web, poiché volevamo anche diffondere la nostra filosofia in modo chiaro. Puoi farlo da solo in modo simile (usiamo Njalla per i nostri domini e hosting, pagati con Bitcoin mescolati), ma sentiti libero di contattarci per farci ospitare i tuoi torrent. Stiamo cercando di costruire un indice completo di mirror pirata nel tempo, se questa idea prende piede. Per quanto riguarda la selezione della VPN, molto è stato scritto su questo già, quindi ripeteremo solo il consiglio generale di scegliere in base alla reputazione. Politiche di no-log testate in tribunale con lunghe storie di protezione della privacy sono l'opzione a minor rischio, a nostro avviso. Nota che anche quando fai tutto bene, non puoi mai arrivare a rischio zero. Ad esempio, quando semini i tuoi torrent, un attore statale altamente motivato può probabilmente osservare i flussi di dati in entrata e in uscita per i server VPN, e dedurre chi sei. Oppure puoi semplicemente sbagliare in qualche modo. Probabilmente lo abbiamo già fatto, e lo faremo di nuovo. Fortunatamente, gli stati nazionali non si preoccupano <em>così</em> tanto della pirateria. Una decisione da prendere per ogni progetto è se pubblicarlo utilizzando la stessa identità di prima, o no. Se continui a usare lo stesso nome, allora errori nella sicurezza operativa di progetti precedenti potrebbero tornare a morderti. Ma pubblicare sotto nomi diversi significa che non costruisci una reputazione duratura. Abbiamo scelto di avere una forte sicurezza operativa fin dall'inizio in modo da poter continuare a usare la stessa identità, ma non esiteremo a pubblicare sotto un nome diverso se sbagliamo o se le circostanze lo richiedono. Far conoscere il progetto può essere complicato. Come abbiamo detto, questa è ancora una comunità di nicchia. Inizialmente abbiamo postato su Reddit, ma abbiamo davvero ottenuto trazione su Hacker News. Per ora la nostra raccomandazione è di postarlo in alcuni posti e vedere cosa succede. E ancora, contattaci. Ci piacerebbe diffondere la parola di più sforzi di archivismo pirata. 1. Selezione del dominio / filosofia Non manca la conoscenza e il patrimonio culturale da salvare, il che può essere travolgente. Ecco perché è spesso utile prendersi un momento e pensare a quale può essere il tuo contributo. Ognuno ha un modo diverso di pensare a questo, ma ecco alcune domande che potresti porti: Nel nostro caso, ci interessava in particolare la conservazione a lungo termine della scienza. Conoscevamo Library Genesis e come fosse completamente mirroring molte volte tramite torrent. Ci piaceva quell'idea. Poi un giorno, uno di noi ha provato a trovare alcuni libri di testo scientifici su Library Genesis, ma non è riuscito a trovarli, mettendo in dubbio quanto fosse davvero completo. Abbiamo quindi cercato quei libri di testo online e li abbiamo trovati in altri luoghi, il che ha piantato il seme per il nostro progetto. Anche prima di conoscere Z-Library, avevamo l'idea di non cercare di raccogliere tutti quei libri manualmente, ma di concentrarci sul mirroring delle collezioni esistenti e di contribuire a Library Genesis. Quali abilità possiedi che puoi utilizzare a tuo vantaggio? Ad esempio, se sei un esperto di sicurezza online, puoi trovare modi per superare i blocchi IP per target sicuri. Se sei bravo a organizzare comunità, allora forse puoi radunare alcune persone attorno a un obiettivo. È utile conoscere un po' di programmazione, anche solo per mantenere una buona sicurezza operativa durante questo processo. Quale sarebbe un'area ad alto rendimento su cui concentrarsi? Se hai intenzione di spendere X ore nell'archiviazione pirata, come puoi ottenere il massimo "ritorno sull'investimento"? Quali sono i modi unici in cui stai pensando a questo? Potresti avere alcune idee o approcci interessanti che altri potrebbero aver trascurato. Quanto tempo hai a disposizione per questo? Il nostro consiglio sarebbe di iniziare in piccolo e fare progetti più grandi man mano che ci prendi la mano, ma può diventare totalizzante. Perché sei interessato a questo? Di cosa sei appassionato? Se riusciamo a ottenere un gruppo di persone che archiviano tutti i tipi di cose a cui tengono specificamente, copriremmo molto! Saprai molto di più della persona media sulla tua passione, come quali sono i dati importanti da salvare, quali sono le migliori collezioni e comunità online, e così via. 3. Estrazione di metadata Data aggiunta/modificata: così puoi tornare più tardi e scaricare file che non hai scaricato prima (anche se spesso puoi usare anche l'ID o l'hash per questo). Hash (md5, sha1): per confermare che hai scaricato correttamente il file. ID: può essere un ID interno, ma anche ID come ISBN o DOI sono utili. Nome file / posizione Descrizione, categoria, tag, autori, lingua, ecc. Dimensione: per calcolare quanto spazio su disco ti serve. Diventiamo un po' più tecnici qui. Per estrarre effettivamente i metadata dai siti web, abbiamo mantenuto le cose piuttosto semplici. Utilizziamo script Python, a volte curl, e un database MySQL per memorizzare i risultati. Non abbiamo utilizzato alcun software di estrazione sofisticato che possa mappare siti web complessi, poiché finora abbiamo solo avuto bisogno di estrarre uno o due tipi di pagine semplicemente enumerando gli id e analizzando l'HTML. Se non ci sono pagine facilmente enumerabili, allora potresti aver bisogno di un vero e proprio crawler che cerchi di trovare tutte le pagine. Prima di iniziare a estrarre un intero sito web, prova a farlo manualmente per un po'. Passa attraverso alcune decine di pagine da solo, per avere un'idea di come funziona. A volte incontrerai già blocchi IP o altri comportamenti interessanti in questo modo. Lo stesso vale per l'estrazione dei dati: prima di approfondire troppo questo obiettivo, assicurati di poter effettivamente scaricare i suoi dati in modo efficace. Per aggirare le restrizioni, ci sono alcune cose che puoi provare. Ci sono altri indirizzi IP o server che ospitano gli stessi dati ma non hanno le stesse restrizioni? Ci sono endpoint API che non hanno restrizioni, mentre altri sì? A quale velocità di download il tuo IP viene bloccato, e per quanto tempo? Oppure non sei bloccato ma rallentato? Cosa succede se crei un account utente, come cambiano le cose allora? Puoi usare HTTP/2 per mantenere aperte le connessioni, e questo aumenta la velocità con cui puoi richiedere le pagine? Ci sono pagine che elencano più file contemporaneamente, e le informazioni elencate lì sono sufficienti? Le cose che probabilmente vuoi salvare includono: Di solito facciamo questo in due fasi. Prima scarichiamo i file HTML grezzi, di solito direttamente in MySQL (per evitare molti piccoli file, di cui parliamo più avanti). Poi, in un passaggio separato, esaminiamo quei file HTML e li analizziamo in tabelle MySQL effettive. In questo modo non devi riscaricare tutto da zero se scopri un errore nel tuo codice di analisi, poiché puoi semplicemente riprocessare i file HTML con il nuovo codice. È anche spesso più facile parallelizzare il passaggio di elaborazione, risparmiando così del tempo (e puoi scrivere il codice di elaborazione mentre l'estrazione è in esecuzione, invece di dover scrivere entrambi i passaggi contemporaneamente). Infine, nota che per alcuni obiettivi il scraping dei metadata è tutto ciò che c'è. Ci sono alcune enormi collezioni di metadata là fuori che non sono adeguatamente preservate. Titolo Selezione del dominio / filosofia: Dove vuoi concentrarti approssimativamente e perché? Quali sono le tue passioni, abilità e circostanze uniche che puoi utilizzare a tuo vantaggio? Selezione del target: Quale collezione specifica mirerai? Raccolta dei metadata: Catalogare le informazioni sui file, senza effettivamente scaricare i file stessi (spesso molto più grandi). Selezione dei dati: Basandosi sui metadata, restringere quali dati sono più rilevanti da archiviare in questo momento. Potrebbe essere tutto, ma spesso c'è un modo ragionevole per risparmiare spazio e larghezza di banda. Raccolta dei dati: Ottenere effettivamente i dati. Distribuzione: Impacchettare il tutto in torrent, annunciarlo da qualche parte, farlo diffondere dalle persone. 5. Scraping dei dati Ora sei pronto per scaricare effettivamente i dati in massa. Come menzionato prima, a questo punto dovresti già aver scaricato manualmente un bel po' di file, per comprendere meglio il comportamento e le restrizioni dell'obiettivo. Tuttavia, ci saranno ancora sorprese in serbo per te una volta che effettivamente inizi a scaricare molti file contemporaneamente. Il nostro consiglio qui è principalmente di mantenerlo semplice. Inizia semplicemente scaricando un bel po' di file. Puoi usare Python, e poi espandere a più thread. Ma a volte è ancora più semplice generare file Bash direttamente dal database, e poi eseguirne più in più finestre di terminale per aumentare la scala. Un rapido trucco tecnico che vale la pena menzionare qui è l'uso di OUTFILE in MySQL, che puoi scrivere ovunque se disabiliti "secure_file_priv" in mysqld.cnf (e assicurati di disabilitare/sovrascrivere anche AppArmor se sei su Linux). Conserviamo i dati su semplici dischi rigidi. Inizia con quello che hai, ed espandi lentamente. Può essere opprimente pensare di dover conservare centinaia di TB di dati. Se questa è la situazione che stai affrontando, metti fuori un buon sottoinsieme per primo, e nel tuo annuncio chiedi aiuto per conservare il resto. Se vuoi ottenere più dischi rigidi da solo, allora r/DataHoarder ha alcune buone risorse per ottenere buoni affari. Cerca di non preoccuparti troppo di filesystem sofisticati. È facile cadere nella trappola di configurare cose come ZFS. Un dettaglio tecnico di cui essere consapevoli, però, è che molti filesystem non gestiscono bene molti file. Abbiamo scoperto che una semplice soluzione è creare più directory, ad esempio per diversi intervalli di ID o prefissi di hash. Dopo aver scaricato i dati, assicurati di controllare l'integrità dei file utilizzando gli hash nei metadata, se disponibili. 2. Selezione del target Accessibile: non utilizza tonnellate di livelli di protezione per impedirti di estrarre i loro metadata e dati. Intuizione speciale: hai qualche informazione speciale su questo obiettivo, come un accesso speciale a questa collezione, o hai capito come superare le loro difese. Questo non è richiesto (il nostro progetto in arrivo non fa nulla di speciale), ma certamente aiuta! Grande Quindi, abbiamo la nostra area di interesse, ora quale collezione specifica vogliamo mirrorare? Ci sono un paio di cose che rendono un obiettivo valido: Quando abbiamo trovato i nostri libri di testo scientifici su siti web diversi da Library Genesis, abbiamo cercato di capire come fossero arrivati su internet. Abbiamo poi trovato Z-Library e ci siamo resi conto che, sebbene la maggior parte dei libri non appaia lì per la prima volta, alla fine ci finiscono. Abbiamo appreso della sua relazione con Library Genesis e della struttura di incentivi (finanziari) e dell'interfaccia utente superiore, entrambe le quali lo rendevano una collezione molto più completa. Abbiamo quindi effettuato alcune estrazioni preliminari di metadata e dati, e ci siamo resi conto che potevamo aggirare i loro limiti di download IP, sfruttando l'accesso speciale di uno dei nostri membri a molti server proxy. Mentre esplori diversi obiettivi, è già importante nascondere le tue tracce utilizzando VPN e indirizzi email usa e getta, di cui parleremo più avanti. Unica: non già ben coperta da altri progetti. Quando realizziamo un progetto, ci sono un paio di fasi: Queste non sono fasi completamente indipendenti, e spesso le intuizioni di una fase successiva ti riportano a una fase precedente. Ad esempio, durante la raccolta dei metadata potresti renderti conto che il target che hai selezionato ha meccanismi difensivi oltre il tuo livello di abilità (come i blocchi IP), quindi torni indietro e trovi un target diverso. - Anna e il team (<a %(reddit)s>Reddit</a>) Interi libri possono essere scritti sul perché della conservazione digitale in generale, e dell'archivismo pirata in particolare, ma lasciateci dare un breve riassunto per coloro che non sono troppo familiari. Il mondo sta producendo più conoscenza e cultura che mai, ma anche più di essa viene persa che mai. L'umanità affida in gran parte questo patrimonio a società come editori accademici, servizi di streaming e aziende di social media, e spesso non si sono dimostrate grandi custodi. Date un'occhiata al documentario Digital Amnesia, o a qualsiasi discorso di Jason Scott. Ci sono alcune istituzioni che fanno un buon lavoro nell'archiviare il più possibile, ma sono vincolate dalla legge. Come pirati, siamo in una posizione unica per archiviare collezioni che non possono toccare, a causa dell'applicazione del copyright o di altre restrizioni. Possiamo anche fare il mirror delle collezioni molte volte, in tutto il mondo, aumentando così le possibilità di una corretta conservazione. Per ora, non entreremo in discussioni sui pro e contro della proprietà intellettuale, la moralità di infrangere la legge, riflessioni sulla censura o la questione dell'accesso alla conoscenza e alla cultura. Con tutto ciò fuori dai piedi, immergiamoci nel come. Condivideremo come il nostro team è diventato archivisti pirati e le lezioni che abbiamo imparato lungo il percorso. Ci sono molte sfide quando si intraprende questo viaggio, e speriamo di potervi aiutare a superarne alcune. Come diventare un archivista pirata La prima sfida potrebbe essere sorprendente. Non è un problema tecnico, né un problema legale. È un problema psicologico. Prima di immergerci, due aggiornamenti sul Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>): Abbiamo ricevuto alcune donazioni estremamente generose. La prima è stata di $10k da un individuo anonimo che ha anche supportato "bookwarrior", il fondatore originale di Library Genesis. Un ringraziamento speciale a bookwarrior per aver facilitato questa donazione. La seconda è stata un'altra donazione di $10k da un donatore anonimo, che ci ha contattato dopo la nostra ultima pubblicazione ed è stato ispirato ad aiutare. Abbiamo anche ricevuto un numero di donazioni più piccole. Grazie mille per tutto il vostro generoso supporto. Abbiamo alcuni nuovi progetti entusiasmanti in cantiere che questo sosterrà, quindi rimanete sintonizzati. Abbiamo avuto alcune difficoltà tecniche con la dimensione della nostra seconda pubblicazione, ma i nostri torrent sono ora attivi e in seeding. Abbiamo anche ricevuto un'offerta generosa da un individuo anonimo per fare seeding della nostra collezione sui loro server ad altissima velocità, quindi stiamo facendo un caricamento speciale sui loro macchinari, dopodiché tutti gli altri che stanno scaricando la collezione dovrebbero vedere un grande miglioramento nella velocità. Post del blog Ciao, sono Anna. Ho creato <a %(wikipedia_annas_archive)s>Archivio di Anna</a>, la più grande biblioteca ombra del mondo. Questo è il mio blog personale, in cui io e i miei compagni di squadra scriviamo di pirateria, conservazione digitale e altro. Connettiti con me su <a %(reddit)s>Reddit</a>. Nota che questo sito web è solo un blog. Ospitiamo solo le nostre parole qui. Non ospitiamo né colleghiamo torrent o altri file protetti da copyright. <strong>Biblioteca</strong> - Come la maggior parte delle biblioteche, ci concentriamo principalmente su materiali scritti come i libri. Potremmo espanderci in altri tipi di media in futuro. <strong>Mirror</strong> - Siamo strettamente un mirror di biblioteche esistenti. Ci concentriamo sulla conservazione, non sulla facilità di ricerca e download dei libri (accesso) o sulla creazione di una grande comunità di persone che contribuiscono con nuovi libri (approvvigionamento). <strong>Pirata</strong> - Violiamo deliberatamente la legge sul copyright nella maggior parte dei paesi. Questo ci permette di fare qualcosa che le entità legali non possono fare: assicurarci che i libri siano replicati ovunque. <em>Non colleghiamo i file da questo blog. Trovateli da soli.</em> - Anna e il team (<a %(reddit)s>Reddit</a>) Questo progetto (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>) mira a contribuire alla conservazione e liberazione della conoscenza umana. Facciamo il nostro piccolo e umile contributo, seguendo le orme dei grandi che ci hanno preceduto. L'obiettivo di questo progetto è illustrato dal suo nome: La prima biblioteca che abbiamo replicato è Z-Library. Questa è una biblioteca popolare (e illegale). Hanno preso la collezione di Library Genesis e l'hanno resa facilmente ricercabile. Inoltre, sono diventati molto efficaci nel sollecitare nuovi contributi di libri, incentivando gli utenti che contribuiscono con vari vantaggi. Attualmente non restituiscono questi nuovi libri a Library Genesis. E a differenza di Library Genesis, non rendono facilmente replicabile la loro collezione, il che impedisce una vasta conservazione. Questo è importante per il loro modello di business, poiché addebitano denaro per accedere alla loro collezione in blocco (più di 10 libri al giorno). Non facciamo giudizi morali sul far pagare denaro per l'accesso in massa a una collezione di libri illegali. È indubbio che la Z-Library abbia avuto successo nell'espandere l'accesso alla conoscenza e nel procurare più libri. Siamo semplicemente qui per fare la nostra parte: garantire la conservazione a lungo termine di questa collezione privata. Vorremmo invitarvi ad aiutare a preservare e liberare la conoscenza umana scaricando e seminando i nostri torrent. Consultate la pagina del progetto per ulteriori informazioni su come sono organizzati i dati. Invitiamo anche molto volentieri a contribuire con le vostre idee su quali collezioni replicare successivamente e su come procedere. Insieme possiamo ottenere molto. Questo è solo un piccolo contributo tra innumerevoli altri. Grazie, per tutto ciò che fate. Presentazione del Pirate Library Mirror: Conservare 7TB di libri (che non sono in Libgen) 10% of del patrimonio scritto dell'umanità conservato per sempre <strong>Google.</strong> Dopotutto, hanno fatto questa ricerca per Google Books. Tuttavia, i loro metadata non sono accessibili in massa e piuttosto difficili da estrarre. <strong>Vari sistemi bibliotecari e archivi individuali.</strong> Ci sono biblioteche e archivi che non sono stati indicizzati e aggregati da nessuno di quelli sopra, spesso perché sono sottofinanziati, o per altre ragioni non vogliono condividere i loro dati con Open Library, OCLC, Google, e così via. Molti di questi hanno registri digitali accessibili tramite internet, e spesso non sono molto ben protetti, quindi se vuoi aiutare e divertirti imparando sui sistemi bibliotecari strani, questi sono ottimi punti di partenza. <strong>ISBNdb.</strong> Questo è l'argomento di questo post sul blog. ISBNdb estrae vari siti web per i metadata dei libri, in particolare i dati sui prezzi, che poi vendono ai librai, in modo che possano prezzare i loro libri in accordo con il resto del mercato. Poiché gli ISBN sono abbastanza universali al giorno d'oggi, hanno effettivamente costruito una “pagina web per ogni libro”. <strong>Open Library.</strong> Come menzionato prima, questa è la loro intera missione. Hanno raccolto enormi quantità di dati bibliotecari da biblioteche cooperative e archivi nazionali, e continuano a farlo. Hanno anche bibliotecari volontari e un team tecnico che stanno cercando di deduplicare i record e taggarli con tutti i tipi di metadata. La cosa migliore è che il loro dataset è completamente aperto. Puoi semplicemente <a %(openlibrary)s>scaricarlo</a>. <strong>WorldCat.</strong> Questo è un sito web gestito dalla non-profit OCLC, che vende sistemi di gestione bibliotecaria. Aggregano metadata di libri da molte biblioteche e li rendono disponibili attraverso il sito web WorldCat. Tuttavia, fanno anche soldi vendendo questi dati, quindi non sono disponibili per il download in massa. Hanno alcuni dataset più limitati disponibili per il download, in collaborazione con biblioteche specifiche. 1. Per una definizione ragionevole di "per sempre". ;) 2. Ovviamente, il patrimonio scritto dell'umanità è molto più dei libri, specialmente al giorno d'oggi. Per il bene di questo post e delle nostre recenti pubblicazioni ci stiamo concentrando sui libri, ma i nostri interessi si estendono oltre. 3. C'è molto di più che si può dire su Aaron Swartz, ma volevamo solo menzionarlo brevemente, poiché gioca un ruolo fondamentale in questa storia. Col passare del tempo, più persone potrebbero imbattersi nel suo nome per la prima volta, e successivamente esplorare il suo mondo. <strong>Copie fisiche.</strong> Ovviamente questo non è molto utile, poiché sono solo duplicati dello stesso materiale. Sarebbe fantastico se potessimo preservare tutte le annotazioni che le persone fanno nei libri, come i famosi “scarabocchi nei margini” di Fermat. Ma ahimè, questo rimarrà un sogno per gli archivisti. <strong>“Edizioni”.</strong> Qui si conta ogni versione unica di un libro. Se qualcosa è diverso, come una copertina diversa o una prefazione diversa, conta come un'edizione diversa. <strong>File.</strong> Quando si lavora con biblioteche ombra come Library Genesis, Sci-Hub o Z-Library, c'è un'ulteriore considerazione. Ci possono essere più scansioni della stessa edizione. E le persone possono creare versioni migliori dei file esistenti, scansionando il testo usando l'OCR o rettificando le pagine che sono state scansionate ad un angolo. Vogliamo contare questi file come una sola edizione, il che richiederebbe buoni metadata o deduplicazione usando misure di somiglianza dei documenti. <strong>“Opere”.</strong> Ad esempio “Harry Potter e la Camera dei Segreti” come concetto logico, che comprende tutte le versioni, come diverse traduzioni e ristampe. Questa è una definizione piuttosto utile, ma può essere difficile tracciare il confine di ciò che conta. Ad esempio, probabilmente vogliamo preservare diverse traduzioni, anche se le ristampe con solo lievi differenze potrebbero non essere così importanti. - Anna e il team (<a %(reddit)s>Reddit</a>) Con il Pirate Library Mirror (MODIFICA: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>), il nostro obiettivo è prendere tutti i libri del mondo e conservarli per sempre.<sup>1</sup> Tra i nostri torrent di Z-Library e i torrent originali di Library Genesis, abbiamo 11.783.153 file. Ma quanti sono davvero? Se deduplicassimo correttamente quei file, quale percentuale di tutti i libri del mondo abbiamo conservato? Ci piacerebbe davvero avere qualcosa del genere: Iniziamo con alcuni numeri approssimativi: Sia in Z-Library/Libgen che in Open Library ci sono molti più libri che ISBN unici. Significa che molti di quei libri non hanno ISBN, o i metadata degli ISBN sono semplicemente mancanti? Probabilmente possiamo rispondere a questa domanda con una combinazione di abbinamento automatico basato su altri attributi (titolo, autore, editore, ecc.), integrando più fonti di dati ed estraendo gli ISBN dalle scansioni effettive dei libri stessi (nel caso di Z-Library/Libgen). Quanti di quegli ISBN sono unici? Questo è meglio illustrato con un diagramma di Venn: Per essere più precisi: Siamo rimasti sorpresi da quanto poco ci sia sovrapposizione! ISBNdb ha un'enorme quantità di ISBN che non compaiono né in Z-Library né in Open Library, e lo stesso vale (in misura minore ma comunque sostanziale) per le altre due. Questo solleva molte nuove domande. Quanto aiuterebbe l'abbinamento automatico nel taggare i libri che non sono stati taggati con ISBN? Ci sarebbero molte corrispondenze e quindi un aumento della sovrapposizione? Inoltre, cosa accadrebbe se introducessimo un quarto o quinto dataset? Quanta sovrapposizione vedremmo allora? Questo ci dà un punto di partenza. Ora possiamo esaminare tutti gli ISBN che non erano nel dataset di Z-Library e che non corrispondono nemmeno ai campi titolo/autore. Questo può darci un'idea su come preservare tutti i libri del mondo: prima raschiando internet per scansioni, poi uscendo nella vita reale per scansionare i libri. Quest'ultimo potrebbe persino essere finanziato dal pubblico, o guidato da "ricompense" da parte di persone che vorrebbero vedere determinati libri digitalizzati. Tutto ciò è una storia per un altro momento. Se vuoi aiutare in qualsiasi modo — ulteriori analisi; raschiare più metadata; trovare più libri; fare OCR dei libri; fare questo per altri domini (ad esempio articoli, audiolibri, film, serie TV, riviste) o persino rendere disponibili alcuni di questi dati per cose come l'addestramento di modelli di linguaggio di grandi dimensioni — per favore contattami (<a %(reddit)s>Reddit</a>). Se sei specificamente interessato all'analisi dei dati, stiamo lavorando per rendere i nostri dataset e script disponibili in un formato più facile da usare. Sarebbe fantastico se potessi semplicemente fare un fork di un notebook e iniziare a sperimentare. Infine, se vuoi supportare questo lavoro, per favore considera di fare una donazione. Questa è un'operazione gestita interamente da volontari, e il tuo contributo fa una grande differenza. Ogni piccolo aiuto conta. Per ora accettiamo donazioni in criptovaluta; vedi la pagina delle Donazioni su Archivio di Anna. Per una percentuale, abbiamo bisogno di un denominatore: il numero totale di libri mai pubblicati.<sup>2</sup> Prima della fine di Google Books, un ingegnere del progetto, Leonid Taycher, <a %(booksearch_blogspot)s>ha cercato di stimare</a> questo numero. Ha proposto — con ironia — 129.864.880 (“almeno fino a domenica”). Ha stimato questo numero costruendo un database unificato di tutti i libri del mondo. Per questo, ha riunito diversi Datasets e poi li ha fusi in vari modi. Come breve parentesi, c'è un'altra persona che ha tentato di catalogare tutti i libri del mondo: Aaron Swartz, il defunto attivista digitale e co-fondatore di Reddit.<sup>3</sup> Ha <a %(youtube)s>iniziato Open Library</a> con l'obiettivo di “una pagina web per ogni libro mai pubblicato”, combinando dati da molte fonti diverse. Finì per pagare il prezzo più alto per il suo lavoro di preservazione digitale quando fu perseguito per il download massivo di articoli accademici, portandolo al suicidio. Inutile dire che questa è una delle ragioni per cui il nostro gruppo è pseudonimo e perché stiamo facendo molta attenzione. Open Library è ancora gestita eroicamente da persone dell'Internet Archive, continuando l'eredità di Aaron. Torneremo su questo più avanti in questo post. Nel post sul blog di Google, Taycher descrive alcune delle sfide nel stimare questo numero. Innanzitutto, cosa costituisce un libro? Ci sono alcune possibili definizioni: Le “Edizioni” sembrano la definizione più pratica di cosa siano i “libri”. Comodamente, questa definizione è anche usata per assegnare numeri ISBN unici. Un ISBN, o International Standard Book Number, è comunemente usato per il commercio internazionale, poiché è integrato con il sistema internazionale di codici a barre (“International Article Number”). Se vuoi vendere un libro nei negozi, ha bisogno di un codice a barre, quindi ottieni un ISBN. Il post sul blog di Taycher menziona che mentre gli ISBN sono utili, non sono universali, poiché sono stati adottati solo a metà degli anni settanta, e non ovunque nel mondo. Tuttavia, l'ISBN è probabilmente l'identificatore più ampiamente usato delle edizioni dei libri, quindi è il nostro miglior punto di partenza. Se possiamo trovare tutti gli ISBN nel mondo, otteniamo una lista utile di quali libri devono ancora essere preservati. Quindi, dove otteniamo i dati? Ci sono diversi sforzi esistenti che stanno cercando di compilare una lista di tutti i libri del mondo: In questo post, siamo felici di annunciare un piccolo rilascio (rispetto ai nostri precedenti rilasci di Z-Library). Abbiamo estratto la maggior parte di ISBNdb e reso i dati disponibili per il torrenting sul sito web del Pirate Library Mirror (EDIT: spostato su <a %(wikipedia_annas_archive)s>Archivio di Anna</a>; non lo collegheremo qui direttamente, basta cercarlo). Si tratta di circa 30,9 milioni di record (20GB come <a %(jsonlines)s>JSON Lines</a>; 4,4GB compressi). Sul loro sito web affermano di avere effettivamente 32,6 milioni di record, quindi potremmo averne persi alcuni, o <em>loro</em> potrebbero aver commesso qualche errore. In ogni caso, per ora non condivideremo esattamente come abbiamo fatto — lasceremo questo come esercizio per il lettore. ;-) Quello che condivideremo è un'analisi preliminare, per cercare di avvicinarci a stimare il numero di libri nel mondo. Abbiamo esaminato tre dataset: questo nuovo dataset di ISBNdb, il nostro rilascio originale di metadata che abbiamo estratto dalla biblioteca ombra Z-Library (che include Library Genesis), e il dump di dati di Open Library. Dump di ISBNdb, o Quanti Libri Sono Conservati per Sempre? Se dovessimo deduplicare correttamente i file delle biblioteche ombra, quale percentuale di tutti i libri del mondo abbiamo conservato? Aggiornamenti su <a %(wikipedia_annas_archive)s>L'Archivio di Anna</a>, la più grande biblioteca veramente aperta nella storia umana. <em>Redesign di WorldCat</em> Dati <strong>Formato?</strong> <a %(blog)s>Contenitori di Anna’s Archive (AAC)</a>, che sono essenzialmente <a %(jsonlines)s>JSON Lines</a> compressi con <a %(zstd)s>Zstandard</a>, più alcune semantiche standardizzate. Questi contenitori avvolgono vari tipi di record, basati sui diversi scraping che abbiamo implementato. Un anno fa, ci siamo <a %(blog)s>prefissati</a> di rispondere a questa domanda: <strong>Quale percentuale di libri è stata permanentemente preservata dalle biblioteche ombra?</strong> Diamo un'occhiata ad alcune informazioni di base sui dati: Una volta che un libro entra in una biblioteca ombra open-data come <a %(wikipedia_library_genesis)s>Library Genesis</a>, e ora <a %(wikipedia_annas_archive)s>Archivio di Anna</a>, viene replicato in tutto il mondo (tramite torrent), preservandolo praticamente per sempre. Per rispondere alla domanda su quale percentuale di libri sia stata preservata, dobbiamo conoscere il denominatore: quanti libri esistono in totale? E idealmente non abbiamo solo un numero, ma anche i metadata effettivi. In questo modo possiamo non solo confrontarli con le biblioteche ombra, ma anche <strong>creare una lista di libri rimanenti da preservare!</strong> Potremmo persino iniziare a sognare uno sforzo collettivo per affrontare questa lista. Abbiamo estratto dati da <a %(wikipedia_isbndb_com)s>ISBNdb</a> e scaricato il <a %(openlibrary)s>dataset di Open Library</a>, ma i risultati non sono stati soddisfacenti. Il problema principale era che non c'era molta sovrapposizione di ISBN. Guarda questo diagramma di Venn dal <a %(blog)s>nostro post sul blog</a>: Siamo rimasti molto sorpresi dalla scarsa sovrapposizione tra ISBNdb e Open Library, entrambi includono liberamente dati da varie fonti, come estrazioni web e registri di biblioteche. Se entrambi facessero un buon lavoro nel trovare la maggior parte degli ISBN esistenti, i loro cerchi avrebbero sicuramente una sovrapposizione sostanziale, o uno sarebbe un sottoinsieme dell'altro. Ci siamo chiesti, quanti libri cadono <em>completamente al di fuori di questi cerchi</em>? Abbiamo bisogno di un database più grande. È allora che abbiamo puntato al più grande database di libri al mondo: <a %(wikipedia_worldcat)s>WorldCat</a>. Questo è un database proprietario della non-profit <a %(wikipedia_oclc)s>OCLC</a>, che aggrega record di metadata da biblioteche di tutto il mondo, in cambio di dare a quelle biblioteche accesso al dataset completo e di farle apparire nei risultati di ricerca degli utenti finali. Anche se OCLC è una non-profit, il loro modello di business richiede la protezione del loro database. Bene, ci dispiace dirlo, amici di OCLC, stiamo dando via tutto. :-) Nel corso dell'ultimo anno, abbiamo meticolosamente estratto tutti i record di WorldCat. All'inizio, abbiamo avuto un colpo di fortuna. WorldCat stava appena lanciando il completo redesign del loro sito web (ad agosto 2022). Questo includeva una revisione sostanziale dei loro sistemi backend, introducendo molte falle di sicurezza. Abbiamo immediatamente colto l'opportunità e siamo riusciti a estrarre centinaia di milioni (!) di record in pochi giorni. Dopo di ciò, le falle di sicurezza sono state lentamente corrette una per una, fino a quando l'ultima che abbiamo trovato è stata risolta circa un mese fa. A quel punto avevamo praticamente tutti i record e stavamo solo cercando di ottenere record di qualità leggermente superiore. Quindi abbiamo sentito che è il momento di rilasciare! 1,3 miliardi di dati estratti da WorldCat <em><strong>In breve:</strong> Archivio di Anna ha estratto tutti i dati di WorldCat (la più grande collezione di metadata di biblioteche al mondo) per creare una lista di libri da preservare.</em> WorldCat Avviso: questo post del blog è stato deprecato. Abbiamo deciso che IPFS non è ancora pronto per il grande pubblico. Continueremo a collegarci ai file su IPFS dall'Archivio di Anna quando possibile, ma non lo ospiteremo più noi stessi, né raccomandiamo ad altri di fare mirror utilizzando IPFS. Si prega di consultare la nostra pagina Torrents se si desidera aiutare a preservare la nostra collezione. Aiuta a seminare Z-Library su IPFS Download dal server partner SciDB Prestito esterno Prestito esterno (stampa disabilitata) Download esterno Esplora i metadati Contenuti nei torrent Indietro  (+%(num)s bonus) non retribuito retribuito annullato scaduto in attesa di conferma da Anna non valido Il testo seguente è disponibile solo in inglese. Vai Reimposta Avanti Ultimo Se il tuo indirizzo email non funziona sui forum Libgen, ti consigliamo di usare <a %(a_mail)s>Proton Mail</a> (gratuito). Puoi anche <a %(a_manual)s>richiedere manualmente</a> l'attivazione del tuo account. (potrebbe richiedere la <a %(a_browser)s>verifica del browser</a> — download illimitati!) Server veloce del partner #%(number)s (consigliato) (un po' più veloce ma con lista d'attesa) (nessuna verifica richiesta dal browser) (senza verifica del browser o liste d'attesa) (senza lista d'attesa, ma potenzialmente molto lento) Server lento del partner #%(number)s Audiolibro Fumetti Libri (narrativa) Libri (saggistica) Libro (sconosciuto) Articoli scientifici Riviste Partiture musicali Altro Documenti normativi Non tutte le pagine possono essere convertite in PDF Contrassegnato come non valido in Libgen.li Non visibile in Libgen.li Non visibile su Libgen.rs Fiction Non visibile su Libgen.rs Non-Fiction L'esecuzione di exiftool su questo file non è riuscita Contrassegnato come “file danneggiato” in Z-Library Mancante in Z-Library Segnata come "spam" in Z-Library Impossibile aprire il file (es. file corrotto, DRM) Reclamo per violazione di copyright Problemi di download (es. problemi di connessione, messaggio di errore, connessione lenta) Metadati errati (ad esempio: titolo, descrizione, immagine di copertina) Altro Bassa qualità (es. problemi di formattazione, bassa qualità di scansione, pagine mancanti) Spam/file da rimuovere (es. pubblicità, contenuti offensivi) %(amount)s (%(amount_usd)s) %(amount)s totale %(amount)s (%(amount_usd)s) totale Topo di biblioteca geniale Bibliotecario fortunato Datahoarder stupefacente Archivista straordinario Download bonus Cerlalc Metadati cechi DuXiu Indice eBook EBSCOhost Google Libri Goodreads HathiTrust IA Controlled Digital Lending di IA ISBNdb ISBN GRP Libgen.li Escluso “scimag” Libgen.rs Saggi e narrativa Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Biblioteca Statale Russa Sci-Hub Tramite Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Caricamenti su AA Z-Library Z-Library Cinese Titolo, autore, DOI, ISBN, MD5, … Cerca Autore Descrizione e commenti nei metadati Edizione Nome del file originale Casa editrice (ricerca in un campo specifico) Titolo Anno di pubblicazione Dettagli tecnici Questa moneta ha un minimo più alto del solito. Per favore, seleziona una durata o una moneta diversa. La richiesta non può essere completata. Per favore, riprova tra alcuni minuti e, se il problema persiste, scrivici a %(email)s allegando uno screenshot. Si è verificato un errore sconosciuto. Per favore, scrivici a %(email)s allegando uno screenshot. Errore nell'elaboramento del pagamento. Attendi qualche istante e riprova. Se il problema persiste per più di 24 ore, contattaci all'indirizzo %(email)s con uno screenshot. Stiamo organizzando una raccolta fondi per <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">sostenere</a> la più grande biblioteca ombra di fumetti al mondo. Grazie per sostegno che ci darai! <a href="/donate">Dona ora.</a> Se non puoi donare, prendi in considerazione l'idea di supportarci spargendo la voce e di seguirci su <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> o su <a href="https://t.me/annasarchiveorg">Telegram </a>. Non scriverci un'email per <a %(a_request)s>richieste di libri</a><br> o per <a %(a_upload)s>piccoli quantitativi di caricamenti</a>(<10k). Anna’s Archive DMCA/reclami per copyright Restare in contatto Reddit Alternative SLUM (%(unaffiliated)s) non affiliato L'Archivio di Anna ha bisogno del tuo aiuto! Se doni ora, ottieni <strong>il doppio</strong> di download veloci. Molti cercano di abbatterci, ma noi non molliamo. Se doni questo mese, otterrai <strong>il doppio</strong> del numero di download veloci. Offerta valida fino alla fine di questo mese. Salvare la conoscenza umana: un fantastico regalo per le feste! Gli abbonamenti saranno prolungati di conseguenza. I server partner non sono disponibili a causa della chiusura di alcuni servizi di hosting. Dovrebbero essere di nuovo operativi a breve. Per aumentare la resilienza dell'Archivio di Anna, stiamo cercando volontari e volontarie per gestire i mirror. Abbiamo un nuovo metodo di donazione:%(method_name)s. Prendi in considerazione l'idea di %(donate_link_open_tag)sdonare</a>: non è economico far funzionare questo sito e la tua donazione può fare una grande differenza. Grazie mille. Segnala un amico/a ed entrambi/e avrete il %(percentage)s%% di bonus di download rapidi! Sorprendi una persona cara e regalale un abbonamento. Il regalo perfetto per San Valentino! Maggiori informazioni… Account Attività Avanzato Blog di Anna ↗ Software di Anna ↗ beta Esploratore di Codici Set di dati Donare File scaricati FAQ Home Migliora i metadati Dati LLM Accedi/Registrati Le mie donazioni Profilo pubblico Cerca Sicurezza Torrent Traduci ↗ Volontariato & Ricompense Download recenti: 📚&nbsp;La più grande biblioteca open-source e open-data al mondo. ⭐️&nbsp;Mirror di Sci-Hub, Library Genesis, Z-Library e molti altri. 📈&nbsp;%(book_any)s libri, %(journal_article)s pubblicazioni, %(book_comic)s fumetti, %(magazine)s riviste, preservati per sempre.  e  e altro ancora DuXiu Internet Archive Lending Library LibGen 📚&nbsp;La più grande biblioteca veramente aperta della storia dell'umanità. 📈&nbsp;%(book_count)s&nbsp;libri, %(paper_count)s&nbsp;documenti, preservati per sempre. ⭐️&nbsp;Mirroring di %(libraries)s. Estraiamo e rendiamo open-source i dati di %(scraped)s. Tutto il nostro codice sorgente e i dati sono completamente open source. OpenLib Sci-Hub ,  📚 La più grande biblioteca open-source e open-data al mondo.<br>⭐️ Mirror di Scihub, Libgen, Zlib e altro. Z-Lib Anna’s Archive Richiesta non valida. Collegati all'indirizzo %(websites)s. La più grande biblioteca open-source e open-data al mondo. Mirror di Sci-Hub, Library Genesis, Z-Library e molti altri. Cerca in Anna’s Archive Anna’s Archive Per favore, aggiorna per riprovare. <a %(a_contact)s>Contattaci</a> se il problema persiste per diverse ore. 🔥 Errore nel caricare questa pagina <li>1. Seguici su <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> o <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Diffondi il verbo riguardo all'Archivio di Anna su Twitter/X, Reddit, TikTok, Instagram, nella tua università, a lavoro o in biblioteca. Sentiti libero/a di farlo ovunque tu vada! Non crediamo nel gatekeeping: se il nostro sito venisse oscurato, torneremmo online altrove, dal momento che il nostro codice sorgente è totalmente open source.</li><li>3. Se puoi, prendi in considerazione di supportarci tramite <a href="/donate">una donazione</a>.</li><li>4. Aiutaci a <a href="https://translate.annas-software.org/">tradurre</a> il nostro sito in varie lingue (sì, l'abbiamo tradotto anche in italiano!).</li><li>5. Se sei un ingegnere/a informatico/a, sarebbe fantastico se potessi contribuire al nostro codice sorgente <a href="https://annas-software.org/">open source</a>, o eseguire il seeding dei nostri <a href="/datasets">torrent</a>.</li> 10. Creare o aiutare a mantenere la pagina dell'Archivio di Anna su Wikipedia nella tua lingua. 11. Stiamo cercando di inserire piccoli annunci pubblicitari "di buon gusto". Se volessi mettere un'inserzione pubblicitaria sul nostro sito, faccelo sapere. 6. Se sei un esperto/a di sicurezza informatica, possiamo sfruttare le tue competenze sia per scopi di attacco che di difesa. Dai un'occhiata alla nostra pagina <a %(a_security)s>Security</a>. 7. Siamo alla ricerca di esperti/e in pagamenti anonimi. Potresti aiutarci ad aggiungere metodi di pagamento più convenienti per le donazioni? PayPal, WeChat, gift card. Se conosci qualcuno/a, per favore contattaci. 8. Siamo sempre alla ricerca di server con maggiore capacità. 9. Puoi aiutarci segnalandoci i problemi ai file, lasciandoci dei commenti e creando liste su questo sito web. Inoltre, puoi aiutarci anche <a %(a_upload)s>caricando dei libri</a> oppure sistemando le anomalie ai file o la formattazione dei libri attualmente presenti. Per informazioni più dettagliate su come fare volontariato, consulta la nostra pagina <a %(a_volunteering)s>Volontariato & Ricompense</a>. Crediamo fermamente nella libera circolazione di informazioni e nella conservazione della conoscenza e della cultura. Con questo motore di ricerca, costruiamo su delle basi solide. Rispettiamo profondamente il duro lavoro delle persone che hanno creato le varie biblioteche ombra e speriamo che questo motore di ricerca possa ampliare la loro portata. Per restare al corrente dei nostri progressi, segui l'Archivio di Anna su <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> o su <a href="https://t.me/annasarchiveorg">Telegram</a>. Per domande o feedback, contatta Anna all'indirizzo email %(email)s. ID dell'account: %(account_id)s Disconnetti ❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo. ✅ Sei stato disconnesso. Ricarica la pagina per accedere nuovamente. Download veloci utilizzati (ultime 24 ore): <strong>%(used)s / %(total)s</strong> Abbonamento: <strong>%(tier_name)s</strong> fino al %(until_date)s <a %(a_extend)s>(estendi)</a> Puoi combinare più abbonamenti (i download veloci saranno aggiunti insieme per 24 ore). Abbonamento: <strong>Nessuno</strong> <a %(a_become)s>(diventa un membro)</a> Contatta Anna via %(email)s se vuoi fare l'upgrade del tuo abbonamento. Profilo pubblico: %(profile_link)s Chiave segreta (non condividerla!): %(secret_key)s Mostra Unisciti a noi qui! Passa a un <a %(a_tier)s>livello superiore</a> per unirti al nostro gruppo. Gruppo Telegram esclusivo: %(link)s Account Quali download? Accedi Non perdere la tua chiave! Chiave segreta non valida. Verifica la tua chiave e riprova, o alternativamente effettua la registrazione di un nuovo account qui sotto. Chiave segreta Inserisci la tua chiave segreta per accedere: Hai un vecchio account via email? Inserisci qui il tuo indirizzo <a %(a_open)s>email</a>. Registra un nuovo account Non hai ancora un account? Registrazione completata! La tua chiave segreta è: <span %(span_key)s>%(key)s</span> Salva questa chiave con cura. Se la perdi, perderai accesso al tuo account. <li %(li_item)s><strong>Preferiti.</strong> Puoi aggiungere questa pagina ai preferiti per recuperare la tua chiave.</li><li %(li_item)s><strong>Scarica.</strong> Premi su <a %(a_download)s>questo link</a> per scaricare la tua chiave.</li><li %(li_item)s><strong>Gestore di password.</strong> Utilizza un gestore di password per salvare la chiave quando la inserisci qui di seguito.</li> Accedi/Registrati Verifica del browser Avviso: il codice contiene dei caratteri Unicode invalidi, e potrebbe comportarsi in maniera errata in varie situazioni. Il binario grezzo può essere decifrato dalla rappresentazione in base64 nell'URL. Descrizione Etichetta Prefisso URL per un codice specifico Sito web Codici che iniziano con “%(prefix_label)s” Si prega di non fare scraping di queste pagine. Consigliamo invece di <a %(a_import)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB, e di eseguire il nostro <a %(a_software)s>codice open source</a>. I dati non elaborati possono essere esplorati manualmente tramite file JSON come <a %(a_json_file)s>questo</a>. Meno di %(count)s record URL generico Utilità di esplorazione codici Indice di Esplora i codici con cui sono contrassegnati i record, per prefisso. La colonna 'record' mostra il numero di record contrassegnati con codici che hanno il prefisso dato, come visibile nel motore di ricerca (inclusi i record solo con metadati). La colonna 'codici' mostra quanti codici effettivi hanno un determinato prefisso. Prefisso del codice noto “%(key)s” Altro… Prefisso %(count)s record corrispondente a “%(prefix_label)s” %(count)s record corrispondenti a “%(prefix_label)s” codici record “%%s” sarà sostituito con il valore del codice Cerca nell'Archivio di Anna Codici URL per codice specifico: “%(url)s” Questa pagina potrebbe metterci un po' a caricare, questo é perché richiede un CAPTCHA di Cloudflare. I <a %(a_donate)s>Membri</a> possono saltare il captcha. Abuso segnalato: Versione migliore Vuoi segnalare questo utente per comportamento abusivo o inappropriato? Problema con il file: %(file_issue)s commento nascosto Rispondi Segnala abuso Hai segnalato questo utente per abuso. Le richieste di copyright inviate a questa email saranno ignorate; utilizza invece il modulo dedicato. Mostra email Accogliamo con piacere i tuoi commenti e le tue domande! Tuttavia, a causa della mole di spam e di e-mail senza senso che riceviamo, ti chiediamo di spuntare le caselle per confermare di aver compreso i termini per contattarci. Qualsiasi altro modo per contattarci in merito a richieste di copyright verrà automaticamente cancellato. Per reclami DMCA/copyright, compila <a %(a_copyright)s>questo form</a>. Email di contatto URL nell'Archivio di Anna (obbligatorio). Uno per riga. Includi solo URL che descrivono esattamente la stessa edizione di un libro. Se desideri fare un reclamo per più libri o più edizioni, invia questo modulo più volte. I reclami riferiti a più libri o edizioni verranno respinti. Indirizzo (obbligatorio) Descrizione chiara del materiale di origine (obbligatoria) E-mail (obbligatoria) URL del materiale di origine, uno per riga (obbligatorio). Includi quanti più URL possibile, per aiutarci a verificare il tuo reclamo (es. Amazon, WorldCat, Google Libri, DOI). ISBN del materiale di origine (se applicabile). Uno per riga. Includi solo i codici che corrispondono esattamente all'edizione per cui stai effettuando un reclamo relativo al copyright. Il tuo nome (obbligatorio) ❌ Qualcosa è andato storto. Ricarica la pagina e riprova. ✅ Grazie per aver inviato il tuo reclamo relativo al copyright. Lo esamineremo il prima possibile. Ricarica la pagina per inviarne un altro. URL <a %(a_openlib)s>Open Library</a> del materiale di origine, uno per riga. Dedica un attimo a cercare il materiale di origine su Open Library; questo ci aiuterà a verificare il tuo reclamo. Numero di telefono (obbligatorio) Dichiarazione e firma (obbligatorie) Invia reclamo Se hai un reclamo DMCA o di altro tipo relativo al copyright, compila questo modulo nel modo più preciso possibile. Se riscontri problemi, contattaci al nostro indirizzo DMCA dedicato: %(email)s. Nota che questo indirizzo è riservato alle domande; eventuali reclami inviati non verranno gestiti. Utilizza il modulo sottostante per inviare i tuoi reclami. Modulo di reclamo DMCA/copyright Esempio di record sull'Archivio di Anna Torrent dell'Archivio di Anna Formato "Anna’s Archive Containers" Script per l'importazione dei metadati Contattaci se sei interessato/a a eseguire il mirroring di questo dataset per scopi di <a %(a_archival)s>archiviazione</a> o <a %(a_llm)s>addestramento dell'LLM</a>. Ultimo aggiornamento: %(date)s Sito web principale %(source)s Documentazione dei metadati (la maggior parte dei campi) File replicati dall'Archivio di Anna: %(count)s (%(percent)s%%) Risorse File totali: %(count)s Dimensione totale dei file: %(size)s Il nostro post sul blog riguardo a questi dati <a %(duxiu_link)s>Duxiu</a> è un enorme database di libri digitalizzati, creato dal <a %(superstar_link)s>SuperStar Digital Library Group</a>. La maggior parte sono libri accademici, digitalizzati per renderli disponibili a università e biblioteche. Per il nostro pubblico di lingua inglese, <a %(princeton_link)s>Princeton</a> e l'<a %(uw_link)s>Università di Washington</a> offrono buone panoramiche. C'è anche un eccellente articolo che fornisce ulteriori informazioni: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. I libri di Duxiu sono stati a lungo piratati sulla rete Internet cinese. Di solito vengono venduti per meno di un dollaro e vengono tipicamente distribuiti utilizzando l'equivalente cinese di Google Drive, che è stato spesso hackerato per aumentare lo spazio di archiviazione. Alcuni dettagli tecnici sono reperibili <a %(link1)s>qui</a> e <a %(link2)s>qui</a>. Sebbene i libri siano stati distribuiti in modalità semi-pubblica, è piuttosto difficile ottenerli in blocco. Questa attività era in cima alla nostra lista di cose da fare e ci abbiamo dedicato diversi mesi di lavoro a tempo pieno. Tuttavia, alla fine del 2023, un volontario incredibile, straordinario e talentuoso ci ha contattato, dicendoci che aveva già fatto tutto questo enorme lavoro. Ci ha condiviso l'intera raccolta, senza aspettarsi nulla in cambio, tranne la garanzia di una conservazione a lungo termine. Veramente eccezionale. Maggiori informazioni dai nostri volontari (note non elaborate): Adattato dal nostro <a %(a_href)s>post sul blog</a>. DuXiu 读秀 %(count)s file %(count)s file Questo dataset è strettamente correlato al <a %(a_datasets_openlib)s>dataset di Open Library</a>. Contiene una raccolta di tutti i metadati e una grande porzione di file dalla IA Controlled Digital Lending Library. Gli aggiornamenti vengono rilasciati nel <a %(a_aac)s>formato "Anna’s Archive Containers"</a>. Questi record sono riferiti direttamente dal dataset di Open Library, ma contengono anche record che non sono presenti su quella piattaforma. Abbiamo anche un certo numero di file di dati ottenuti dai membri della community nel corso degli anni. La raccolta è composta da due parti. Entrambe sono necessarie per ottenere tutti i dati (eccetto i torrent obsoleti, che sono barrati nella pagina dei torrent). Biblioteca per il prestito digitale la nostra prima release, prima di allinearci sul formato <a %(a_aac)s>Anna’s Archive Containers (AAC)</a>. Contiene metadati (in formato json e xml), pdf (dai sistemi di prestito digitale acsm e lcpdf) e miniature delle copertine. nuove release incrementali, utilizzando AAC. Contiene solo metadati con timestamp successivi al 1° gennaio 2023, poiché il resto è già coperto da “ia”. Anche tutti i file pdf, questa volta dai sistemi di prestito acsm e “bookreader” (il lettore web di IA). Nonostante il nome non sia completamente corretto, continuiamo a popolare i file bookreader nella raccolta ia2_acsmpdf_files, poiché sono mutuamente esclusive. IA Controlled Digital Lending 98%%+ dei file sono ricercabili. La nostra missione è archiviare tutti i libri del mondo (così come articoli, riviste, ecc.) e renderli ampiamente accessibili. Crediamo che tutti i libri debbano essere replicati in modo esteso, per garantire ridondanza e resilienza. Per questo motivo, stiamo raccogliendo file da una varietà di fonti: alcune sono completamente aperte e possono essere replicate in toto (come Sci-Hub). Altre sono chiuse e protette, quindi cerchiamo di eseguirne lo scraping per “liberare” i libri. Altre ancora rappresentano una via di mezzo. Tutti i nostri dati possono essere <a %(a_torrents)s>scaricati via torrent</a> e tutti i nostri metadati possono essere <a %(a_anna_software)s>generati</a> o <a %(a_elasticsearch)s>scaricati</a> come database ElasticSearch e MariaDB. I dati non elaborati possono essere consultati manualmente attraverso file JSON come <a %(a_dbrecord)s>questo</a>. Metadati Sito web ISBN Ultimo aggiornamento: %(isbn_country_date)s (%(link)s) Risorse L'Agenzia Internazionale ISBN pubblica regolarmente gli intervalli di codici assegnati alle agenzie nazionali. Da questi possiamo dedurre a quale paese, regione o gruppo linguistico appartiene un ISBN. Attualmente utilizziamo questi dati indirettamente, tramite la libreria Python <a %(a_isbnlib)s>isbnlib</a>. Informazioni sul paese ISBN Questo è un dump di molte chiamate a isbndb.com durante il mese di settembre 2022. Abbiamo cercato di coprire tutti gli intervalli ISBN. Si tratta di circa 30,9 milioni di record. Sul loro sito web affermano di avere, in effetti, 32,6 milioni di record, quindi alcuni potrebbero mancarci, o <em>loro</em> potrebbero sbagliarsi. Le risposte JSON sono praticamente dati non elaborati provenienti dal loro server. Un problema di qualità dei dati che abbiamo notato è che i numeri ISBN-13 che iniziano con un prefisso diverso da "978-", includono comunque un campo "isbn" che è semplicemente il numero ISBN-13 senza i primi 3 numeri (e la cifra di controllo ricalcolata). Questo ovviamente non è corretto, ma apparentemente loro procedono, quindi non abbiamo apportato modifiche. Un altro potenziale problema che potresti incontrare è il fatto che il campo "isbn13" ha duplicati, quindi non puoi usarlo come chiave primaria in un database. I campi combinati "isbn13" + "isbn" sembrerebbero univoci. Release 1 (2022-10-31) I torrent di narrativa sono in ritardo (anche se per gli ID ~4-6M non sono stati creati torrent, poiché si sovrappongono ai nostri torrent di Zlib). Il nostro post sul blog riguardo alle release di fumetti Torrent di fumetti nell'Archivio di Anna Per la storia dei diversi fork di Library Genesis, consulta la pagina dedicata a <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li contiene la maggior parte degli stessi contenuti e metadati di Libgen.rs, ma con alcune raccolte aggiuntive, come fumetti, riviste e documenti standard. Inoltre, ha integrato <a %(a_scihub)s>Sci-Hub</a> nei suoi metadati e nel suo motore di ricerca, che usiamo per il nostro database. I metadati per questa biblioteca sono liberamente disponibili <a %(a_libgen_li)s>su libgen.li</a>. Tuttavia, questo server è lento e non supporta il riavvio delle connessioni interrotte. Gli stessi file sono disponibili anche su <a %(a_ftp)s>un server FTP</a>, che funziona meglio. Anche la saggistica sembra essersi diversificata, ma senza nuovi torrent. Sembra che ciò sia accaduto dall'inizio del 2022, anche se non lo abbiamo verificato. Secondo l'amministratore di Libgen.li, la raccolta “fiction_rus” (narrativa russa) dovrebbe essere disponibile tramite torrent pubblicati regolarmente da <a %(a_booktracker)s>booktracker.org</a>, in particolare i torrent <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (che noi manteniamo <a %(a_torrents)s>qui</a>, anche se non abbiamo ancora definito quali torrent corrispondono a quali file). La raccolta di narrativa dispone di torrent propri (diversi da quelli di <a %(a_href)s>Libgen.rs</a>) a partire da %(start)s. Alcuni intervalli senza torrent (come quelli di narrativa da f_3463000 a f_4260000) sono probabilmente file duplicati di Z-Library (o altri); potrebbe essere opportuno fare un po' di deduplica e creare torrent per i file univoci di lgli in questi intervalli. Le statistiche per tutte le raccolte sono reperibili <a %(a_href)s>sul sito di libgen</a>. Sono disponibili torrent per la maggior parte dei contenuti aggiuntivi; in particolare, i torrent per fumetti, riviste e documenti standard sono stati rilasciati in collaborazione con l'Archivio di Anna. Nota che i file torrent che fanno riferimento a “libgen.is” sono esplicitamente mirror di <a %(a_libgen)s>Libgen.rs</a> (“.is” è un dominio diverso utilizzato da Libgen.rs). Una risorsa utile per utilizzare i metadati è <a %(a_href)s>questa pagina</a>. %(icon)s La loro raccolta “fiction_rus” (narrativa russa) non ha torrent dedicati, ma è dsponibile tramite torrent di altri; noi manteniamo un <a %(fiction_rus)s>mirror</a>. Torrent di narrativa russa nell'Archivio di Anna Torrent di narrativa nell'Archivio di Anna Forum di discussione Metadati Metadati via FTP Torrent di riviste nell'Archivio di Anna Informazioni sui campi dei metadati Mirror di altri torrent (e torrent unici di narrativa e fumetti) Torrent di documenti standard nell'Archivio di Anna Libgen.li Torrent dell'Archivio di Anna (copertine dei libri) Library Genesis è noto per rendere già generosamente disponibili i propri dati in blocco tramite torrent. La nostra raccolta di Libgen consiste in dati aggiuntivi che LibGen non pubblica direttamente, in collaborazione con loro. Un grande ringraziamento a tutti coloro che lavorano con Library Genesis per la loro collaborazione! Il nostro blog sulla release delle copertine dei libri Questa pagina riguarda la versione “.rs”, nota per pubblicare in modo coerente sia i propri metadati sia il contenuto completo del proprio catalogo di libri. La sua raccolta di libri è divisa tra narrativa e saggistica. Una risorsa utile per utilizzare i metadati è <a %(a_metadata)s>questa pagina</a> (blocca intervalli di IP, potrebbe essere necessaria una VPN). A partire da marzo 2024, nuovi torrent vengono pubblicati in <a %(a_href)s>questo thread del forum</a> (blocca intervalli IP, potrebbe essere necessaria una VPN). Torrent di narrativa nell'Archivio di Anna Torrent di narrativa di Libgen.rs Forum di discussione di Libgen.rs Metadati di Libgen.rs Informazioni sui campi di metadati di Libgen.rs Torrent di saggistica di Libgen.rs Torrent di saggistica nell'Archivio di Anna %(example)s per un libro di narrativa. Questa <a %(blog_post)s>prima release</a> è piuttosto piccola: circa 300GB di copertine di libri dal fork di Libgen.rs, sia di narrativa che di saggistica. Sono organizzati nello stesso modo in cui appaiono su libgen.rs, ad esempio: %(example)s per un libro di saggistica. Proprio come con la raccolta di Z-Library, abbiamo messo tutti i titoli in un grande file .tar, accessibile usando <a %(a_ratarmount)s>ratarmount</a> se vuoi fornire i file direttamente. Release 1 (%(date)s) In breve, per quanto riguarda i diversi fork di Library Genesis (o “Libgen”), nel tempo le diverse persone coinvolte in Library Genesis hanno avuto dissapori e le loro strade si sono separate. Secondo questo <a %(a_mhut)s>post sul forum</a>, Libgen.li era originariamente in hosting su “http://free-books.dontexist.com”. La versione “.fun” è stata creata dal fondatore originale. È in fase di rinnovamento a favore di una nuova versione più distribuita. La <a %(a_li)s>versione “.li”</a> ha una vasta raccolta di fumetti, oltre ad altri contenuti, che non sono (ancora) disponibili per il download in massa tramite torrent. Ha una raccolta separata di torrent di libri di narrativa e il suo database include i metadati di <a %(a_scihub)s>Sci-Hub</a>. La versione “.rs” ha dati molto simili e rilascia la loro raccolta tramite torrent in bulk con la maggiore coerenza. È grossomodo divisa in una sezione “narrativa” e una “saggistica”. Originariamente su “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> in un certo senso è anche una fork di Library Genesis, anche se hanno usato un nome diverso per il loro progetto. Libgen.rs Arricchiamo anche la nostra raccolta con fonti solo metadati, che possiamo abbinare a file, ad esempio utilizzando i numeri ISBN o altri campi. Di seguito è riportata una panoramica di queste fonti. Anche in questo caso, alcune sono completamente aperte, mentre altre richiedono lo scraping da parte nostra. Nota che, nella ricerca dei metadati, mostriamo i record originali. Non eseguiamo alcuna unione dei record. Fonti solo metadati Open Library è un progetto open source di Internet Archive per catalogare ogni libro nel mondo. Ha attuato una delle principali iniziative di digitalizzazione di libri al mondo e ha molti libri disponibili per il prestito digitale. Il suo catalogo di metadati dei libri è liberamente disponibile per il download ed è incluso nell'Archivio di Anna (anche se attualmente non nella ricerca, tranne cercando esplicitamente un ID di Open Library). Open Library Esclusi duplicati Ultimo aggiornamento Percentuali del numero di file %% in mirroring da AA / torrent disponibili Dimensione Sorgente Di seguito una rapida panoramica delle fonti dei file nell'Archivio di Anna. Poiché le biblioteche-ombra spesso sincronizzano i dati tra loro, c'è una notevole sovrapposizione. Ecco perché i totali non corrispondono. La percentuale "mirrored and seeded by Anna’s Archive" mostra di quanti file eseguiamo direttamente il mirroring. Eseguiamo il seed di questi file in blocco tramite torrent e li rendiamo disponibili per il download diretto tramite siti web partner. Panoramica Totale Torrent nell'Archivio di Anna Per una panoramica su Sci-Hub, puoi fare riferimento al suo <a %(a_scihub)s>sito ufficiale</a>, alla <a %(a_wikipedia)s>pagina Wikipedia</a> e a questa <a %(a_radiolab)s>intervista podcast</a>. Nota che Sci-Hub è stato <a %(a_reddit)s>congelato dal 2021</a>. Era stato congelato in precedenza, ma nel 2021 sono stati aggiunti alcuni milioni di articoli. Tuttavia, un numero limitato di articoli viene ancora aggiunto alle raccolte “scimag” di Libgen, sebbene non abbastanza da giustificare nuovi torrent in blocco. Utilizziamo i metadati di Sci-Hub forniti da <a %(a_libgen_li)s>Libgen.li</a> nella sua raccolta “scimag”. Utilizziamo anche il dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Nota che i torrent “smarch” sono <a %(a_smarch)s>deprecati</a> e quindi non inclusi nel nostro elenco di torrent. Torrent su Libgen.li Torrent su Libgen.rs Metadati e torrent Aggiornamenti su Reddit Intervista podcast Pagina Wikipedia Sci-Hub Sci-Hub: congelato dal 2021; la maggior parte dell'archivio è disponibile tramite torrent Libgen.li: aggiunte minori dopo quel momento</div> Alcune biblioteche di origine promuovono la condivisione in blocco dei loro dati tramite torrent, mentre altre non condividono facilmente la loro raccolta. In quest'ultimo caso, l'Archivio di Anna cerca di raggruppare le loro raccolte e renderle disponibili (vedi la nostra pagina <a %(a_torrents)s>Torrent</a>). Ci sono anche situazioni intermedie, ad esempio casi in cui le biblioteche di origine sono disposte a condividere, ma non hanno le risorse per farlo. In questi casi, cerchiamo di dare una mano. Di seguito è riportata una panoramica delle nostre modalità di interazione con le diverse biblioteche di origine. Biblioteche di origine %(icon)s Vari database di file sparsi sulla rete Internet cinese; spesso database a pagamento %(icon)s La maggior parte dei file è accessibile solo utilizzando account premium BaiduYun; velocità di download limitate. %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(duxiu)s>file DuXiu</a> %(icon)s Vari database di metadati sparsi per la rete Internet cinese; spesso si tratta di database a pagamento %(icon)s Nessun dump di metadati facilmente accessibile disponibile per l'intera raccolta. %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(duxiu)s>metadati DuXiu</a> File %(icon)s File disponibili per il prestito solo con in modo limitato, con varie restrizioni di accesso %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(ia)s>file IA</a> %(icon)s Alcuni metadati sono disponibili tramite <a %(openlib)s>dump del database di Open Library</a>, ma non sono relativi all'intera raccolta IA %(icon)s Nessun dump di metadati facilmente accessibile disponibile per l'intera raccolta %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(ia)s>metadati IA</a> Ultimo aggiornamento %(icon)s L'Archivio di Anna e Libgen.li gestiscono collaborativamente raccolte di <a %(comics)s>fumetti</a>, <a %(magazines)s>riviste</a>, <a %(standarts)s>documenti standard</a> e <a %(fiction)s>narrativa (diverse da quelli di Libgen.rs)</a>. %(icon)s I torrent di Saggistica sono condivisi con Libgen.rs (e replicati <a %(libgenli)s>qui</a>). %(icon)s <a %(dbdumps)s>Dump del database HTTP</a> trimestrali %(icon)s Torrent automatizzati per <a %(nonfiction)s>Saggistica</a> e <a %(fiction)s>Narrativa</a> %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(covers)s>torrent di copertine di libri</a> %(icon)s <a %(dbdumps)s>Dump dei database HTTP</a> giornalieri Metadati %(icon)s <a %(dbdumps)s>Dump del database</a> mensili %(icon)s Torrent di dati disponibili <a %(scihub1)s>qui</a>, <a %(scihub2)s>qui</a> e <a %(libgenli)s>qui</a> %(icon)s Alcuni nuovi file vengono <a %(libgenrs)s>aggiunti</a> a <a %(libgenli)s>Libgen’s “scimag”</a>, ma non abbastanza da giustificare nuovi torrent %(icon)s Sci-Hub ha congelato i nuovi file dal 2021. %(icon)s Dump di metadati disponibili <a %(scihub1)s>qui</a> e <a %(scihub2)s>qui</a>, oltre che come parte del <a %(libgenli)s>database di Libgen.li</a> (che utilizziamo) Fonte %(icon)s Varie fonti minori o occasionali. Incoraggiamo le persone a caricare prima su altre biblioteche-ombra, ma a volte le raccolte sono troppo grandi per essere ordinate da altri, ma non abbastanza da giustificare una categoria propria. %(icon)s Non disponibile direttamente in blocco, con protezione anti-scraping %(icon)s L'Archivio di Anna gestisce una raccolta di <a %(worldcat)s>metadati OCLC (WorldCat)</a> %(icon)s L'Archivio di Anna e Z-Library gestiscono in modo collaborativo una raccolta di <a %(metadata)s>metadati di Z-Library</a> e <a %(files)s>file di Z-Library</a> Dataset Combiniamo tutte le fonti sopra menzionate in un unico database unificato che utilizziamo per alimentare questo sito web; questo database non è disponibile direttamente, ma poiché l'Archivio di Anna è completamente open source, può essere <a %(a_generated)s>generato</a> o <a %(a_downloaded)s>scaricato</a> abbastanza facilmente come database ElasticSearch e MariaDB. Gli script su quella pagina scaricheranno automaticamente tutti i metadati necessari dalle fonti sopra citate. Se desideri esplorare i nostri dati prima di eseguire quegli script in locale, puoi consultare i nostri file JSON, che includono collegamenti ad altri file JSON. <a %(a_json)s>Questo file</a> è un buon punto di partenza. Database unificato Torrent dell'Archivio di Anna sfoglia cerca Varie fonti minori o occasionali. Incoraggiamo le persone a caricare prima su altre biblioteche-ombra, ma a volte le loro raccolte sono troppo grandi per essere ordinate da altri, ma non abbastanza da giustificare una categoria propria. Panoramica dalla <a %(a1)s>pagina dei datasets</a>. Da <a %(a_href)s>aaaaarg.fail</a>. Apprentemente abbastanza completo. Dal nostro volontario “cgiym”. Da un torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Ha una livello di sovrapposizione abbastanza alto con le raccolte di articoli esistenti, ma pochissime corrispondenze MD5, quindi abbiamo deciso di mantenerlo completamente. Raccolta di <q>iRead eBooks</q> (= foneticamente <q>ai rit i-books</q>; airitibooks.com), da parte del volontario <q>j</q>. Corrisponde ai metadata di <q>airitibooks</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>. Da una collezione <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. In parte dalla fonte originale, in parte da the-eye.eu, in parte da altri mirror. Da un sito web privato di torrent di libri, <a %(a_href)s>Bibliotik</a> (spesso chiamato “Bib”), i cui libri sono stati raggruppati in torrent per nome (A.torrent, B.torrent) e distribuiti tramite the-eye.eu. Dal nostro volontario “bpb9v”. Per ulteriori informazioni su <a %(a_href)s>CADAL</a>, vedi le note nella nostra <a %(a_duxiu)s>pagina del dataset DuXiu</a>. Altro dal nostro volontario “bpb9v”, principalmente file DuXiu, oltre a una cartella “WenQu” e “SuperStar_Journals” (SuperStar è la società dietro DuXiu). Dal nostro volontario “cgiym”, testi cinesi da varie fonti (rappresentati come sottodirectory), inclusi quelli di <a %(a_href)s>China Machine Press</a> (un importante editore cinese). Raccolte non cinesi (rappresentate come sottodirectory) dal nostro volontario “cgiym”. Raccolta di libri sull'architettura cinese, da parte del volontario <q>cm</q>: <q>L'ho ottenuto sfruttando una vulnerabilità di rete presso la casa editrice, ma quella falla è stata chiusa</q>. Corrisponde ai metadata di <q>chinese_architecture</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>. Libri della casa editrice accademica <a %(a_href)s>De Gruyter</a>, raccolti da alcuni torrent di grandi dimensioni. Dallo scraping di <a %(a_href)s>docer.pl</a>, un sito web polacco di condivisione di file focalizzato su libri e altre opere scritte. Raccolto alla fine del 2023 dal volontario “p”. Non abbiamo buoni metadati dal sito originale (nemmeno le estensioni dei file), ma abbiamo filtrato i file simili a libri e spesso siamo riusciti a estrarre i metadati dai file stessi. Epub DuXiu, direttamente da DuXiu, raccolti dal volontario “w”. Solo i libri recenti di DuXiu sono disponibili direttamente tramite ebook, quindi la maggior parte di questi titoli deve essere recente. File DuXiu rimanenti dal volontario “m”, che non erano nel formato proprietario PDG di DuXiu (il principale <a %(a_href)s>dataset DuXiu</a>). Raccolti da molte fonti originali, purtroppo senza preservarle nel percorso del file. <span></span> <span></span> <span></span> Raccolta di libri erotici, da parte del volontario <q>do no harm</q>. Corrisponde ai metadata di <q>hentai</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>. <span></span> <span></span> Raccolta da un editore giapponese di Manga di cui è stato eseguito lo scraping dal volontario “t”. <a %(a_href)s>Archivi giudiziari selezionati di Longquan</a>, forniti dal volontario “c”. Scraping di <a %(a_href)s>magzdb.org</a>, un alleato di Library Genesis (ha un link sulla homepage di libgen.rs) ma che non voleva fornire i propri file direttamente. Ottenuto dal volontario “p” alla fine del 2023. <span></span> Vari caricamenti di piccole dimensioni, troppo per farne una raccolta secondaria a sé stante, ma rappresentati come directory. Ebook da AvaxHome, un sito russo di condivisione file. Archivio di giornali e riviste. Corrisponde ai metadata di <q>newsarch_magz</q> in <a %(a1)s><q>Altri metadata raccolti</q></a>. Raccolta del <a %(a1)s>Philosophy Documentation Center</a>. Raccolta del volontario “o” che ha riunito libri polacchi direttamente dai siti di release (“scene”) originali. Raccolte combinate di <a %(a_href)s>shuge.org</a> dai volontari “cgiym” e “woz9ts”. <span></span> <a %(a_href)s>“Biblioteca Imperiale di Trantor”</a> (chiamata così in onore della biblioteca fittizia), raccolta nel 2022 dal volontario “t”. <span></span> <span></span> <span></span> Raccolte secondarie di secondo livello (rappresentate come directory) dal volontario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (di <a %(a_sikuquanshu)s>Dizhi(迪志)</a> a Taiwan), mebook (mebook.cc, 我的小书屋, la mia piccola libreria — woz9ts: “Questo sito si concentra principalmente sulla condivisione di file ebook di alta qualità, alcuni dei quali impaginati dallo stesso proprietario. Il proprietario è stato <a %(a_arrested)s>arrestato</a> nel 2019 e qualcuno ha creato una raccolta dei file che ha condiviso.”). File rimanenti di DuXiu dal volontario “woz9ts”, che non erano nel formato proprietario PDG di DuXiu (ancora da convertire in PDF). La raccolta “upload” è suddivisa in raccolte secondarie più piccole, che sono indicate negli AACID e nei nomi dei torrent. Tutte le raccolte secondarie sono state prima deduplicate rispetto a quella principale, anche se i file JSON dei metadati “upload_records” contengono ancora molti riferimenti ai file originali. I file non di libri sono stati anche rimossi dalla maggior parte delle raccolte secondarie e tipicamente <em>non</em> sono indicati negli “upload_records” JSON. Le raccolte secondarie sono: Note Sottocollezione Molte raccolte secondarie sono a loro volta composte da ulteriori raccolte secondarie (ad esempio da diverse fonti originali), che sono rappresentate come directory nei campi “filepath”. Caricamenti nell'Archivio di Anna Il nostro post sul blog riguardo a questi dati <a %(a_worldcat)s>WorldCat</a> è un database proprietario della non-profit <a %(a_oclc)s>OCLC</a>, che aggrega record di metadati dalle biblioteche di tutto il mondo. È probabilmente la più grande collezione di metadati di biblioteche al mondo. Nel mese di ottobre 2023 abbiamo <a %(a_scrape)s>rilasciato</a> una raccolta completa del database OCLC (WorldCat), nel <a %(a_aac)s>formato "Anna’s Archive Containers"</a>. Ottobre 2023, rilascio iniziale: OCLC (WorldCat) Torrent dell'Archivio di Anna Esempio di record nell'Archivio di Anna (raccolta originale) Esempio di record nell'Archivio di Anna (raccolta “zlib3”) Torrent dell'Archivio di Anna (metadati + contenuti) Post del blog sulla Release 1 Post del blog sulla Release 2 Alla fine del 2022, i presunti fondatori di Z-Library sono stati arrestati e i domini sono stati sequestrati dalle autorità degli Stati Uniti. Da allora il sito web ha lentamente ripreso a funzionare. Non si sa chi lo gestisca attualmente. Aggiornamento a febbraio 2023. Z-Library ha avuto origine dalla comunità di <a %(a_href)s>Library Genesis</a> e inizialmente è stata avviata con i loro dati. Da allora, si è notevolmente professionalizzata e presenta un'interfaccia molto più moderna. Pertanto, è in grado di ottenere molte più donazioni, sia monetarie per continuare a migliorare il sito web, sia di nuovi libri. Ha accumulato una raccolta di grandi dimensioni oltre a quella di Library Genesis. La raccolta è composta da tre parti. Le pagine di descrizione originali per le prime due parti sono conservate di seguito. Sono necessarie tutte e tre le parti per ottenere tutti i dati (eccetto i torrent obsoleti, che sono barrati nella relativa pagina). %(title)s: la nostra prima release. È stata la primissima release di quello che allora si chiamava “Pirate Library Mirror” (“pilimi”). %(title)s: seconda release, questa volta con tutti i file in formato .tar. %(title)s: nuove release incrementali, utilizzando il formato <a %(a_href)s>Anna’s Archive Containers (AAC)</a>, ora pubblicate in collaborazione con il team di Z-Library. Il mirror iniziale è stato ottenuto con grande sforzo nel corso del 2021 e 2022. A questo punto è leggermente obsoleto: riflette lo stato della raccolta a giugno 2021. Lo aggiorneremo in futuro. Al momento siamo concentrati sulla pubblicazione di questa prima release. Dal momento che Library Genesis è già preservata con torrent pubblici ed è inclusa in Z-Library, abbiamo effettuato una deduplicazione di base rispetto a Library Genesis a giugno 2022, utilizzando hash MD5. È probabile che ci siano molti altri contenuti duplicati nella biblioteca, ad esempio gli stessi libri in formati di file multipli. Questo è difficile da rilevare con precisione, quindi non lo facciamo. Dopo la deduplicazione, ci rimangono oltre 2 milioni di file, per un totale di poco meno di 7TB. La raccolta consiste di due parti: un dump MySQL “.sql.gz” dei metadati e i 72 file torrent di circa 50-100GB ciascuno. I metadati contengono i dati riportati dal sito web di Z-Library (titolo, autore, descrizione, tipo di file), così come la dimensione effettiva del file e l'md5sum che abbiamo osservato, poiché a volte questi non coincidono. Apparentemente ci sono intervalli di file per i quali la stessa Z-Library ha metadati errati. Potremmo anche aver scaricato file in modo errato in alcuni casi isolati, che cercheremo di rilevare e correggere in futuro. I file torrent di grandi dimensioni contengono i dati effettivi dei libri, con l'ID di Z-Library come nome del file. Le estensioni dei file possono essere ricostruite utilizzando il dump dei metadati. La raccolta è un mix di contenuti di saggistica e narrativa (non separati come in Library Genesis). Anche la qualità è molto variabile. Questa prima versione è ora completamente disponibile. Nota che i file torrent sono disponibili solo attraverso il nostro mirror Tor. Release 1 (%(date)s) Questo è un singolo file torrent aggiuntivo. Non contiene nuove informazioni, ma ha alcuni dati il cui calcolo può richiedere del tempo. Questo lo rende comodo, poiché scaricare questo torrent è spesso più veloce che calcolarlo da zero. In particolare, contiene indici SQLite per i file tar, da utilizzare con <a %(a_href)s>ratarmount</a>. Addendum release 2 (%(date)s) Abbiamo ottenuto tutti i libri aggiunti alla Z-Library tra il nostro ultimo mirror e agosto 2022. Siamo anche tornati indietro e abbiamo recuperato alcuni libri che ci erano sfuggiti la prima volta. In tutto, questa nuova raccolta è di circa 24TB. Anche questa è deduplicata rispetto a Library Genesis, poiché ci sono già torrent disponibili per quella raccolta. I dati sono organizzati in modo simile alla prima versione. C'è un dump MySQL “.sql.gz” dei metadati, che include anche tutti i metadati della prima versione, e quindi la sostituisce. Abbiamo anche aggiunto alcune nuove colonne: Lo abbiamo già detto, ma a titolo di chiarimento: “filename” e “md5” sono le proprietà effettive del file, mentre “filename_reported” e “md5_reported” sono i dati recuperati da Z-Library. A volte non coincidono, quindi li abbiamo inclusi entrambi. Per questa versione, abbiamo cambiato la collazione in “utf8mb4_unicode_ci”, che dovrebbe essere compatibile con le versioni meno recenti di MySQL. I file di dati sono simili all'ultima volta, anche se sono molto più grandi. Creare una quantità di file torrent più piccoli non aveva senso. “pilimi-zlib2-0-14679999-extra.torrent” contiene tutti i file che ci sono sfuggiti nell'ultima release, mentre gli altri torrent includono tutti nuovi intervalli di ID.  <strong>Aggiornamento %(date)s:</strong> La maggior parte dei nostri torrent erano troppo grandi e creavano difficoltà ai client. Li abbiamo rimossi e ne abbiamo rilasciati di nuovi. <strong>Aggiornamento %(date)s:</strong> I file erano ancora troppo numerosi, quindi li abbiamo convertiti in formato tar e abbiamo di nuovo rilasciato nuovi torrent. %(key)s: se questo file è già in Library Genesis, sia nella raccolta di saggistica che in quella di narrativa (corrispondenza tramite md5). %(key)s: in quale torrent si trova questo file. %(key)s: impostato quando non siamo riusciti a scaricare il libro. Release 2 (%(date)s) Release di Zlib (pagine di descrizione originali) Dominio Tor Sito principale Scrape di Z-Library La raccolta "Cinese" in Z-Library sembra coincidere con la nostra raccolta DuXiu, ma con MD5 diversi. Escludiamo questi file dai torrent per evitare duplicati, ma li mostriamo comunque nel nostro indice di ricerca. Metadati Hai ottenuto il %(percentage)s%% bonus di download rapidi perché l'utente %(profile_link)s ha utilizzato il tuo codice referral. Questo si applica all'intero periodo di abbonamento. Dona Unisciti a noi Selezionato fino al %(percentage)s%% di sconto Alipay supporta carte di credito/debito internazionali. Vedi <a %(a_alipay)s>questa guida</a> per maggiori informazioni. Inviaci buoni regalo Amazon.com utilizzando la tua carta di credito/debito. Puoi acquistare crypto utilizzando carte di credito/debito. WeChat (Weixin Pay) supporta carte di credito/debito internazionali. Nell'app WeChat, vai su “Io => Servizi => Portafoglio => Aggiungi una Carta”. Se non lo vedi, abilitalo andando su “Io => Impostazioni => Generale => Strumenti => Weixin Pay => Abilita”. (da utilizzare quando si invia Ethereum da Coinbase) Copiato! Copia (importo minimo) (attenzione: importo minimo elevato) -%(percentage)s%% 12 mesi 1 mese 24 mesi 3 mesi 48 mesi 6 mesi 96 mesi Seleziona la durata dell'abbonamento. <div %(div_monthly_cost)s></div><div %(div_after)s>dopo<span %(span_discount)s></span> sconti</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% per 12 mesi per 1 mese per 24 mesi per 3 mesi per 48 mesi per 6 mesi per 96 mesi %(monthly_cost)s/mese contattaci Accesso diretto ai server <strong>SFTP</strong> Donazioni livello enterprise o scambi per nuove raccolte (es. nuove scansioni, set di dati scansionati tramite OCR). Accesso per esperti Accesso <strong>illimitato</strong> ad alta velocità <div %(div_question)s>Posso aggiornare il mio abbonamento oppure ottenere più abbonamenti?</div> <div %(div_question)s>Posso fare una donazione senza diventare membro?</div> Certamente. Accettiamo donazioni di qualsiasi importo a questo indirizzo Monero (XMR): %(address)s. <div %(div_question)s>Cosa significano gli intervalli per mese?</div> Puoi arrivare all'estremità inferiore di un intervallo applicando tutti gli sconti, ad esempio scegliendo un periodo più lungo di un mese. <div %(div_question)s>Gli abbonamenti si rinnovano automaticamente?</div> Gli abbonamenti <strong>non</strong> si rinnovano automaticamente. Puoi rimanere abbonato/a quanto a lungo vuoi. <div %(div_question)s>Per cosa spendete i fondi delle donazioni?</div> Il 100%% è speso nel preservare e rendere accessibile la conoscenza e la cultura di tutto il mondo. Al momento la gran parte delle spese sono destinate a server, memoria e banda. Nessuna parte dei fondi va personalmente ad alcun membro del team. <div %(div_question)s>Posso fare una grossa donazione?</div> Sarebbe fantastico! Per donazioni oltre qualche migliaio di dollari, per favore contattaci direttamente via email a %(email)s. <div %(div_question)s>Avete altri metodi di pagamento?</div> Attualmente no. Molte persone non vogliono che archivi come questo esistano quindi bisogna essere cauti. Se puoi aiutarci a inserire altri metodi di pagamento (più convenienti) in sicurezza, per favore contattaci a %(email)s. FAQ Donazioni Hai già una <a %(a_donation)s>donazione<a/> in corso. Per favore, concludi o annulla quella donazione prima di effettuarne una nuova. <a %(a_all_donations)s>Visualizza tutte le mie donazioni</a> Per donazioni superiori ai 5000$, per favore contattaci direttamente a %(email)s. Accettiamo volentieri donazioni importanti da parte di persone o istituzioni facoltose.  Tieni presente che, sebbene gli abbonamenti su questa pagina siano "mensili", si tratta di donazioni una tantum (non ricorrenti). Consulta le <a %(faq)s>FAQ Donazioni</a>. L'Archivio di Anna è un progetto non-profit, open-source e open-data. Effettuando una donazione, entrerai a far parte della nostra community e sosterrai il nostro lavoro. A tutti i nostri membri, grazie per il vostro aiuto! ❤️ Per maggiori informazioni, consulta le <a %(a_donate)s>FAQ Donazioni</a>. Per diventare membro, per favore <a %(a_login)s>Effettua l'accesso o registrati</a>. Grazie per il tuo supporto! $%(cost)s / mese Se commetti un errore durante il pagamento, sfortunatamente non possiamo emettere rimborsi ma cercheremo comunque di sistemare le cose. Trova la pagina "Cripto" nella tua app o sito PayPal. Si trova generalmente sotto "Finanze". (N.B.: attualmente in Italia non è possibile acquistare e gestire i criptoasset direttamente dal proprio conto PayPal). Vai alla pagina "Bitcoin" nella tua app o sul sito di PayPal. Premi il pulsante "Trasferisci" %(transfer_icon)s e poi "Invia". Alipay Alipay/WeChat Carta Regalo Amazon Carta regalo %(amazon)s Carta bancaria Carta bancaria (usando l'app) Binance Credito/debito/Apple/Google (BMC) Cash App Carta di credito/debito Carta di credito/debito 2 Carta di credito/debito (riserva) Criptovaluta %(bitcoin_icon)s Carta / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regolare) Pix (Brasile) Revolut (temporaneamente non disponibile) WeChat Seleziona la tua criptovaluta preferita: Dona tramite una gift card di Amazon. <strong>IMPORTANTE:</strong> questa opzione è relativa ad %(amazon)s. Se desideri utilizzare un altro sito Amazon, selezionalo sopra. <strong>IMPORTANTE:</strong> Supportiamo solo Amazon.com, non gli altri domini Amazon. Per esempio, .it, .de, .co, .uk, .ca, NON sono supportati. Per favore, NON scrivere il tuo messaggio. Inserisci l'importo esatto: %(amount)s Nota: dobbiamo arrotondare a importi accettati dai nostri rivenditori (minimo %(minimum)s). Dona utilizzando una carta di credito/debito, tramite l'app Alipay (facilissima da configurare). Installa l'app Alipay dall'<a %(a_app_store)s>Apple App Store</a> o dal <a %(a_play_store)s>Google Play Store</a>. Registrati utilizzando il tuo numero di telefono. Non sono richiesti ulteriori informazioni personali. <span %(style)s>1</span>Installa l'app Alipay Supportate: Visa, MasterCard, JCB, Diners Club e Discover. Consulta <a %(a_alipay)s>questa guida</a> per maggiori informazioni. <span %(style)s>2</span>Aggiungi carta bancaria Con Binance, acquisti Bitcoin con una carta di credito/debito o un conto bancario, e poi doni quei Bitcoin a noi. In questo modo possiamo rimanere sicuri e anonimi quando accettiamo la tua donazione. Binance è disponibile in quasi tutti i paesi e supporta la maggior parte delle banche e delle carte di credito/debito. Attualmente è il nostro principale suggerimento. Apprezziamo che ti prenda del tempo per imparare come donare utilizzando questo metodo, questo ci aiuta molto. Per carte di credito, carte di debito, Apple Pay e Google Pay, utilizziamo “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Nel loro sistema, un “caffè” equivale a $5, quindi la tua donazione sarà arrotondata al multiplo di 5 più vicino. Dona tramite Cash App. Se possiedi Cash App, questo è il modo più facile per donare! Nota: per transazioni al di sotto di %(amount)s, Cash App potrebbe addebitarti una commissione del %(fee)s. Per %(amount)s o più, è gratis! Dona tramite carta di credito o debito. Questo metodo utilizza un fornitore di criptovalute come conversione intermedia. Questo può risultare confusionario, quindi utilizza questo metodo solo se gli altri metodi di pagamento non funzionano. Inoltre, non funziona in tutti i paesi. Non possiamo supportare direttamente le carte di credito/debito, perché le banche non vogliono lavorare con noi. ☹ Tuttavia, ci sono diversi modi per utilizzare comunque le carte di credito/debito, utilizzando altri metodi di pagamento: Con le criptovalute puoi donare utilizzando BTC, ETH, XMR e SOL. Utilizza questa opzione solo se hai già familiarità con le criptovalute. Puoi donare criptovalute usando BTC, ETH, XMR e molte altre. Servizi rapidi di criptovaluta Se stai usando criptovalute per la prima volta, ti suggeriamo di usare %(options)s per acquistare e donare Bitcoin (la criptovaluta originale e più utilizzata). Nota: per le donazioni di piccolo importo, le commissioni delle carte di credito potrebbero eliminare il nostro sconto del %(discount)s. Consigliamo quindi abbonamenti di durata maggiore. Dona utilizzando carta di credito/debito, PayPal o Venmo. Puoi scegliere tra questi nella pagina successiva. Anche Google Pay e Apple Pay potrebbero funzionare. Nota: per le donazioni di piccolo importo, le commissioni potrebbero essere elevate. Ti consigliamo quindi abbonamenti più lunghi. Per donazioni tramite PayPal US, usiamo PayPal Crypto che ci consente di rimanere anonimi. Apprezziamo il tempo che dedichi a imparare come donare tramite questo metodo, e questo ci aiuta molto. Dona tramite PayPal. Dona utilizzando il tuo account PayPal regolare. Dona utilizzando Revolut. Se hai Revolut, questo è il modo più semplice per donare! Questo metodo di pagamento richiede un massimo di %(amount)s. Per favore, seleziona una durata o un metodo di pagamento differente. Questo metodo di pagamento richiede un minimo di %(amount)s. Per favore, seleziona una durata o un metodo di pagamento diverso. Binance Coinbase Kraken Seleziona un metodo di pagamento. "Adotta un torrent": il tuo nome utente o un messaggio all'interno del nome di un file torrent <div %(div_months)s>una volta ogni 12 mesi di abbonamento</div> Il tuo nome utente o una menzione anonima nei ringraziamenti Accesso anticipato alle nuove funzionalità Canale Telegram riservato con aggiornamenti "dietro le quinte" %(number)s download veloci al giorno se doni questo mese! <a %(a_api)s>Accesso API JSON</a> Uno status leggendario nella conservazione della conoscenza e della cultura dell'umanità Tutti i vantaggi precedenti e in più: Guadagna <strong>%(percentage)s%% download aggiuntivi</strong> <a %(a_refer)s>invitando degli amici</a>. Paper di SciDB <strong>illimitati</strong> senza verifica Quando fai domande sull'account o sulle donazioni, aggiungi il tuo account ID, screenshot, ricevute, quante più informazioni possibili. Controlliamo la nostra email solo ogni 1-2 settimane, quindi non includere queste informazioni ritarderà qualsiasi risoluzione. Per ottenere ancora più download, <a %(a_refer)s>invita chi conosci</a>! Siamo un piccolo team di volontari e volontarie. Potremmo aver bisogno di 1-2 settimane per risponderti. Nota: il nome o l'immagine dell'account potrebbero sembrare strani. Non c'è da preoccuparsi! Questi account sono gestiti dai nostri partner per le donazioni. I nostri account non sono stati violati. Dona <span %(span_cost)s></span> <span %(span_label)s></span> per 12 mesi "%(tier_name)s" per 1 mese "%(tier_name)s" per 24 mesi "%(tier_name)s" per 3 mesi "%(tier_name)s" per 48 mesi “%(tier_name)s” per 6 mesi "%(tier_name)s" per 96 mesi “%(tier_name)s” Puoi annullare la tua donazione durante il checkout. Clicca sul pulsante "Dona" per confermare la donazione. <strong>Nota importante:</strong> I prezzi delle criptovalute possono fluttuare in modo selvaggio, a volte anche del 20%% in pochi minuti. Si tratta comunque di una percentuale inferiore alle commissioni che sosteniamo con molti fornitori di pagamenti, che spesso addebitano il 50-60%% per lavorare con un "ente di beneficenza ombra" come noi. <u>Se ci invii la ricevuta con il prezzo originale pagato, ti abiliteremo comunque l'account con la sottoscrizione dell'abbonamento scelto</u> (purché la ricevuta non sia più vecchia di qualche ora). Apprezziamo molto il fatto che tu sia disposto a sopportare cose del genere per sostenerci! ❤️ ❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo. <span %(span_circle)s>1</span>Compra Bitcoin tramite PayPal <span %(span_circle)s>2</span> Trasferisci il Bitcoin al nostro indirizzo ✅ Reindirizzamento verso la pagina di donazione… Donazioni Aspetta almeno <span %(span_hours)s24 ore</span> (e aggiorna questa pagina) prima di contattarci. Se desideri fare una donazione (di qualsiasi importo) senza iscriverti, utilizza questo indirizzo Monero (XMR): %(address)s. Dopo aver mandato la tua gift card, il nostro sistema automatico la confermerà nell'arco di pochi minuti. Se non funziona, prova a inviarla di nuovo (<a %(a_instr)s>instructions</a>). Se ancora non funziona, per favore scrivici un'email e il team dell'Archivio di Anna effettuerà un controllo manuale (potrebbe essere necessario qualche giorno). Non dimenticare di indicare se avevi già provato a rimandare questa comunicazione. Esempio: Per favore, utilizza <a %(a_form)s>il form ufficiale di Amazon</a> per inviarci una gift card di %(amount)s all'indirizzo email sottostante. Campo "destinatario email" del form: Gift card Amazon Non possiamo accettare altre forme di buoni regali, <strong> solo provenienti direttamente dal form ufficiale di Amazon.com</strong>. Non possiamo restituire il buono regalo se si utilizza un form diverso. Da utilizzare solo una volta. Unico per il tuo account, non condividere. In attesa del buono regalo...(aggiorna la pagina per controllare) Apri la <a %(a_href)s>pagina per fare una donazione con codice QR</a>. Scansiona il codice QR con l'app Alipay, o premi il pulsante per aprire l'app Alipay. Un po' di pazienza; il caricamento della pagina, che si trova in Cina, potrebbe richiedere un po' di tempo. <span %(style)s>3</span>Effettua la donazione (scansiona il codice QR o premi il pulsante) Compra PYUSD su PayPal Acquista Bitcoin (BTC) su Cash App Acquista un po' di più (consigliamo %(more)s in più) rispetto all'importo che stai donando (%(amount)s), per coprire le commissioni di transazione. Manterrai qualsiasi importo rimanente. Vai alla pagina “Bitcoin” (BTC) su Cash App. Trasferisci i Bitcoin al nostro indirizzo Per piccole donazioni (meno di $25), potrebbe essere necessario utilizzare Rush o Priority. Clicca sul pulsante “Invia bitcoin” per effettuare un “prelievo”. Passa dai dollari ai BTC premendo l'icona %(icon)s. Inserisci l'importo in BTC qui sotto e clicca su “Invia”. Guarda <a %(help_video)s>questo video</a> se hai difficoltà. I servizi prioritari sono comodi, ma applicano commissioni più elevate. Puoi utilizzare questo al posto di una piattaforma di criptovalute se desideri effettuare rapidamente una donazione più rilevante e non ti crea problemi pagare una commissione di $5-10. Assicurati di inviare l'importo esatto in criptovaluta visualizzato sulla pagina delle donazioni, non l'importo in USD. Altrimenti la commissione verrà addebitata e non potremo elaborare automaticamente il tuo abbonamento. A volte la conferma può richiedere fino a 24 ore, quindi assicurati di aggiornare questa pagina (anche se è scaduta). Istruzioni per carta di credito/debito Dona tramite la nostra pagina delle carte di credito/debito Alcuni dei passaggi menzionati parlano di crypto wallet, ma non preoccuparti: non è necessario che tu impari nulla sulle crypto appositamente. istruzioni %(coin_name)s Scansiona questo codice QR con l'app Crypto Wallet per compilare rapidamente i dettagli di pagamento Scansionare il codice QR da pagare Supportiamo solo le versioni standard delle criptovalute; nessuna "criptovaluta esotica". Può richiedere fino a un'ora per la conferma di avvenuta transazione, in base alla criptovaluta utilizzata. Dona %(amount)s su <a %(a_page)s>questa pagina</a>. Questa donazione è scaduta. Per favore, cancellala e creane una nuova. Se hai già pagato: Sì, ho inviato la mia ricevuta Se il tasso di conversione della criptovaluta dovesse fluttuare nel mezzo della transazione, assicurati di includere la ricevuta mostrando il tasso di conversione originale. Apprezziamo davvero che ti prendi il disturbo di usare criptovalute, ci aiuta un sacco! ❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo. <span %(span_circle)s>%(circle_number)s</span>Inviaci la ricevuta per email Se riscontrassi dei problemi, contattaci via email a %(email)s e includi più informazioni possibili (es. screenshot). ✅ Grazie per la donazione! Anna attiverà manualmente il tuo abbonamento entro qualche giorno. Manda una ricevuta o screenshot al tuo indirizzo email di verifica personale: Quando ci avrai inviato la tua ricevuta, premi questo pulsante cosicché Anna possa verificarla manualmente (potrebbe essere necessario qualche giorno): Inviate una ricevuta o uno screenshot al vostro indirizzo di verifica personale. NON utilizzate questo indirizzo email per la vostra donazione tramite PayPal. Annulla Sì, annulla Sei sicuro di voler annullare? Non annullare se hai già pagato. ❌ Qualcosa è andato storto. Per favore, ricarica la pagina e prova di nuovo. Nuova donazione ✅ La tua donazione è stata annullata. Data: %(date)s Identificatore: %(id)s Ordina di nuovo Stato: <span %(span_label)s>%(label)s</span> Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/mesi per %(duration)s mesi, incluso %(discounts)s%% sconto)</span> Totale: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s/mese per %(duration)s mesi)</span> 1. Inserisci la tua email. 2. Seleziona il tuo metodo di pagamento. 3. Seleziona il tuo metodo di pagamento di nuovo. 4. Seleziona "Self-hosted" wallet. 5. Clicca "Confermo ownership". 6. Dovresti ricevere una ricevuta tramite email. Per favore, mandacela e confermeremo la tua donazione il prima possibile. (potresti voler cancellare e creare una nuova donazione) Le istruzioni di pagamento sono obsolete. Se vuoi fare una nuova donazione, usa il tasto "Ordina di nuovo" qui sopra. Hai già pagato. Sei vuoi comunque rivedere le istruzioni di pagamento, clicca qui: Visualizza le vecchie istruzioni di pagamento Se la pagina delle donazioni viene bloccata, prova una connessione a Internet diversa (ad esempio VPN o connessione dal telefono). Sfortunatamente, la pagina Alipay è spesso accessibile solo dalla <strong>Cina continentale</strong>. Potresti dover disabilitare temporaneamente la tua VPN, o usare una VPN impostata nella Cina continentale (a volte funziona anche Hong Kong). <span %(span_circle)s>1</span>Dona tramite Alipay Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account Alipay</a> Istruzioni Alipay <span %(span_circle)s>1</span>Trasferisci a uno dei nostri account di criptovalute Dona il valore totale di %(total)s a uno dei nostri indirizzi: Istruzioni per criptovalute Segui le istruzioni per acquistare Bitcoin (BTC). È sufficiente acquistare l'importo che si desidera donare, %(total)s. Inserisci il nostro indirizzo Bitcoin (BTC) come destinatario e segui le istruzioni per inviare la tua donazione di %(total)s: <span %(span_circle)s>1</span>Dona tramite Pix Dona il valore totale di %(total)s tramite <a %(a_account)s>questo account Pix Istruzioni Pix <span %(span_circle)s>1</span>Dona tramite WeChat Dona l'importo totale di %(total)s utilizzando <a %(a_account)s>questo account WeChat</a> Istruzioni WeChat Utilizza uno dei seguenti servizi “carta di credito a Bitcoin” express, che richiedono solo pochi minuti: Indirizzo BTC / Bitcoin (portafoglio esterno): Importo BTC / Bitcoin: Compilate i seguenti dettagli nel modulo: Se una di queste informazioni non è aggiornata, inviateci un'email per farcelo sapere. Utilizzate questo <span %(underline)s>importo esatto</span>. Il costo totale potrebbe essere più alto a causa delle commissioni della carta di credito. Per importi piccoli, questo potrebbe superare il nostro sconto, purtroppo. (minimo: %(minimum)s) (minimo: %(minimum)s) (minimum: %(minimum)s) (minimo: %(minimum)s, nessuna verifica per la prima transazione) (minimo: %(minimum)s) (minimo: %(minimum)s a seconda del paese, nessuna verifica per la prima transazione) Segui le istruzioni per acquistare PYUSD (Paypal USD). Acquista di più (consigliamo %(more)s in più) dell'ammontare che stai donando (%(amount)s) per coprire le spese di transazione. L'importo che dovesse avanzare resterà tuo. Vai alla pagina "PYUSD" nella tua app o sito PayPal. Premi il pulsante "Trasferisci" %(icon)s e poi "Invia". Stato dell'aggiornamento Per reimpostare il timer, semplicemente crea una nuova donazione. Assicurati di utilizzare l’importo in BTC indicato qui sotto, <em>NON</em> l'importo in euro o dollari; diversamente, non riceveremo l’importo corretto e non potremo confermare automaticamente la tua iscrizione. Acquista Bitcoin (BTC) su Revolut Acquista un po' di più (consigliamo %(more)s in più) rispetto all'importo che stai donando (%(amount)s), per coprire le commissioni di transazione. Manterrai qualsiasi importo rimanente. Vai alla pagina “Criptovaluta” su Revolut per acquistare Bitcoin (BTC). Trasferisci i Bitcoin al nostro indirizzo Per piccole donazioni (meno di $25) potrebbe essere necessario utilizzare Rush o Priority. Clicca sul pulsante “Invia bitcoin” per effettuare un “prelievo”. Passa dagli euro ai BTC premendo l'icona %(icon)s. Inserisci l'importo in BTC qui sotto e clicca su “Invia”. Guarda <a %(help_video)s>questo video</a> se hai difficoltà. Stato: 1 2 Guida passo-passo Leggi la guida passo-passo qui sotto. Altrimenti potresti rimanere bloccato fuori da questo account! Se non l'hai già fatto, recupera la tua chiave segreta che ti permette di connetterti: Grazie per la tua donazione! Tempo rimasto: Donazione Trasferisci %(amount)s a %(account)s In attesa di conferma (aggiorna la pagina per verificare)… In attesa del trasferimento (aggiorna la pagina per verificare)… Prima I download veloci delle ultime 24 ore vengono conteggiati ai fini del limite giornaliero. I download dai Fast Partner Server sono identificati da %(icon)s. Ultime 18 ore Ancora nessun file scaricato. I file scaricati non sono mostrati pubblicamente. Tutti gli orari sono espressi in UTC. File scaricati Se hai scaricato un file sia con il download lento che rapido, verrà visualizzato due volte. Non preoccuparti troppo, ci sono molte persone che scaricano dai siti web collegati da noi, ed è estremamente raro avere problemi. Tuttavia, per rimanere al sicuro, consigliamo di utilizzare un VPN (a pagamento), o <a %(a_tor)s>Tor</a> (gratuito). Ho scaricato 1984 di George Orwell, la polizia verrà a casa mia? Tu sei Anna! Chi è Anna? Abbiamo un'API JSON stabile per i membri, per ottenere un URL di download veloce: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentazione all'interno del JSON stesso). Per altri casi d'uso, come iterare attraverso tutti i nostri file, costruire ricerche personalizzate, e così via, consigliamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. I dati raw possono essere esplorati manualmente <a %(a_explore)s>attraverso i file JSON</a>. La nostra lista di torrent raw può essere scaricata anche come <a %(a_torrents)s>JSON</a>. Avete un'API? Non ospitiamo materiale protetto da copyright. Siamo un motore di ricerca e, in quanto tale, indicizziamo solo i metadati che sono già disponibili pubblicamente. Quando scarichi da queste fonti esterne, ti suggeriamo di controllare le leggi nella tua giurisdizione riguardo a ciò che è consentito e non. Non siamo responsabili per i contenuti in hosting da altri. Se hai reclami su ciò che vedi qui, la tua opzione migliore è contattare il sito web originale. Regolarmente aggiorniamo il nostro database con le loro modifiche. Se pensi davvero di avere un reclamo DMCA valido a cui dovremmo rispondere, compila il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>. Prendiamo i reclami seriamente e risponderemo il prima possibile. Come posso segnalare una violazione del copyright? Ecco alcuni libri che hanno un significato speciale per il mondo delle biblioteche ombra e della conservazione digitale: Quali sono i vostri libri preferiti? Vorremmo anche ricordare a tutti che tutto il nostro codice e i nostri dati sono completamente open source. Questo è unico per progetti come il nostro — non siamo a conoscenza di nessun altro progetto con un catalogo altrettanto massiccio che sia completamente open source. Invitiamo con molto piacere chiunque pensi che stiamo gestendo male il nostro progetto a prendere il nostro codice ed i nostri dati a creare la propria biblioteca ombra! Non lo diciamo per dispetto o altro — pensiamo sinceramente che sarebbe fantastico poiché alzerebbe il livello per tutti e preserverebbe meglio l'eredità dell'umanità. Odio come state gestendo questo progetto! Ci piacerebbe molto che le persone creassero degli <a %(a_mirrors)s>mirror</a> perché noi li sosterremmo finanziariamente. Come posso contribuire? Certamente. La nostra ispirazione per raccogliere metadati è l'obiettivo di Aaron Swartz di “una pagina web per ogni libro mai pubblicato”, per il quale ha creato <a %(a_openlib)s>Open Library</a>. Quel progetto ha avuto successo, ma la nostra posizione unica ci permette di ottenere metadati non accessibili a quel progetto. Un'altra ispirazione è stata il nostro desiderio di sapere <a %(a_blog)s>quanti libri ci sono nel mondo</a>, per poter calcolare quanti libri ci restano ancora da salvare. Raccogliete metadati? Nota che mhut.org blocca alcune serie di IP, quindi potrebbe essere necessario un VPN. <strong>Android:</strong> Clicca sul menu a tre puntini in alto a destra e seleziona “Aggiungi a schermata Home”. <strong>iOS:</strong> Clicca sul pulsante “Condividi” in basso e seleziona “Aggiungi a schermata Home”. Non abbiamo un'app mobile ufficiale, ma puoi installare questo sito web come app. Avete un'app mobile? Puoi inviarli a <a %(a_archive)s>Internet Archive</a>, che provvederà a preservarli. Come posso donare libri o altri materiali fisici? Come posso richiedere il caricamento di nuovi libri? <a %(a_blog)s>Blog di Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — aggiornamenti regolari <a %(a_software)s>Software di Anna</a> — il nostro codice open source <a %(a_datasets)s>Datasets</a> — informazioni sui dati <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domini alternativi Ci sono altre risorse sull'Archivio di Anna? <a %(a_translate)s>Traduci su Software di Anna</a> — il nostro sistema di traduzione <a %(a_wikipedia)s>Wikipedia</a> — dove troverai maggiori informazioni su di noi (per favore aiuta a mantenere aggiornata la pagina o creane una nella tua lingua!) Seleziona le impostazioni che preferisci, lascia la casella di ricerca vuota, clicca su “Cerca”, e poi aggiungi la pagina ai preferiti utilizzando la funzione segnalibro del tuo browser. Come salvo le mie impostazioni di ricerca? Accogliamo con piacere esperti di sicurezza che cercano vulnerabilità nei nostri sistemi. Siamo grandi sostenitori della divulgazione responsabile. Contattaci <a %(a_contact)s>qui</a>. Attualmente non siamo in grado di assegnare ricompense per bug, tranne per le vulnerabilità che hanno il <a %(a_link)s>potenziale di compromettere il nostro anonimato</a>, per le quali offriamo ricompense nell'ordine di $10k-50k. Vorremmo offrire un più vasto campo d'azione per le ricompense per bug in futuro! Si prega di notare che gli attacchi di ingegneria sociale non rientrano nell'ambito. Se sei interessato alla sicurezza offensiva e vuoi aiutare ad archiviare la conoscenza e la cultura del mondo, assicurati di contattarci. Ci sono molti modi in cui puoi aiutare. Avete un programma di divulgazione responsabile? Non abbiamo letteralmente abbastanza risorse per offrire a tutti nel mondo download ad alta velocità, per quanto questo ci piacerebbe. Se un ricco benefattore volesse farsi avanti e fornircelo, sarebbe incredibile, ma fino ad allora, stiamo facendo del nostro meglio. Siamo un progetto senza scopo di lucro che riesce a malapena a sostenersi attraverso le donazioni. Ecco perché abbiamo implementato due sistemi per i download gratuiti, con i nostri partner: server condivisi con download lenti e server leggermente più veloci con una lista d'attesa (per ridurre il numero di persone che scaricano contemporaneamente). Abbiamo anche <a %(a_verification)s>la verifica del browser</a> per i nostri download lenti, perché altrimenti i bot e gli scraper ne abuserebbero, rendendo le cose ancora più lente per gli utenti legittimi. Nota che, quando utilizzi il browser Tor, potresti dover regolare le tue impostazioni di sicurezza. Al livello più basso delle opzioni, chiamato “Standard”, la challenge del turnstile Cloudflare riesce. Ai livelli più alti, chiamati “Più sicuro” e “Massima sicurezza”, la challenge non riesce. A volte, per i file di grandi dimensioni, i download lenti possono interrompersi a metà. Consigliamo di utilizzare un gestore di download (come JDownloader) per riprendere automaticamente i download di grandi dimensioni. Perché i download lenti sono così lenti? Frequently Asked Questions (FAQ) Utilizza il <a %(a_list)s>generatore di liste torrent</a> per generare una lista di torrent che hanno più bisogno di torrenting, entro i limiti del tuo spazio di archiviazione. Sì, consulta la pagina <a %(a_llm)s>LLM data</a>. La maggior parte dei torrent contiene direttamente i file, il che significa che puoi indicare ai client torrent di scaricare solo i file desiderati. Per determinare quali file scaricare, puoi <a %(a_generate)s>generare</a> i nostri metadati, oppure <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Purtroppo, una parte delle raccolte di torrent contiene file .zip o .tar in radice; in questo caso devi prima scaricare l'intero torrent per poter selezionare i singoli file. Non esistono ancora strumenti di facile utilizzo per filtrare i torrent, ma accogliamo con piacere qualsiasi contributo. (Ma abbiamo <a %(a_ideas)s>alcune idee</a> per quest'ultimo caso) Risposta lunga: Risposta breve: non con facilità. Cerchiamo di mantenere al minimo la duplicazione o la sovrapposizione tra i torrent in questo elenco, ma questo non è sempre possibile e dipende molto anche dalle politiche delle biblioteche di origine. Per le biblioteche che pubblicano i propri torrent, è fuori dal nostro controllo. Per i torrent rilasciati dall'Archivio di Anna, deduplichiamo solo in base all'hash MD5, il che significa che versioni diverse dello stesso libro non vengono deduplicate. Sì. Si tratta in realtà PDF ed EPUB, semplicemente in molti dei nostri torrent non hanno un'estensione. Ci sono due posizioni in cui puoi trovare i metadati per i file torrent, inclusi i tipi/estensioni dei file: 1. Ogni raccolta o release ha i propri metadati. Ad esempio, i <a %(a_libgen_nonfic)s>torrent di Libgen.rs</a> hanno un database di metadati corrispondenti in hosting sul sito di Libgen.rs. Solitamente colleghiamo le risorse dei metadati rilevanti dalla <a %(a_datasets)s>pagina del dataset</a> di ciascuna raccolta. 2. Raccomandiamo di <a %(a_generate)s>generare</a> o <a %(a_download)s>scaricare</a> i nostri database ElasticSearch e MariaDB. Questi contengono una mappatura per ogni record in Anna’s Archive ai suoi file torrent corrispondenti (se disponibili), sotto "torrent_paths" nel JSON di ElasticSearch. Alcuni client torrent non supportano elementi di grandi dimensioni, come molti dei nostri torrent (per quelli più recenti lo evitiamo, anche se è un comportamento valido in base alle specifiche!). Quindi prova a utilizzare un client diverso se riscontri questo problema, o fallo presente agli sviluppatori del tuo client. Vorrei aiutare a fare seeding, ma non ho molto spazio su disco. I torrent sono troppo lenti; posso scaricare i dati direttamente da voi? Posso scaricare solo un sottoinsieme dei file, come solo una particolare lingua o argomento? Come gestite i duplicati nei torrent? Posso ottenere la lista dei torrent in formato JSON? Non vedo i PDF o EPUB nei torrent, solo file binari. Cosa devo fare? Perché il mio client torrent non riesce ad aprire alcuni dei vostri file torrent/link magnet? FAQ Torrent Come posso caricare nuovi libri? Fai riferimento a <a %(a_href)s>questo eccellente progetto</a>. Hai un monitor di uptime? Che cos'è l'Archivio di Anna? Iscriviti per usufruire dei download veloci. Ora supportiamo carte regalo Amazon, carte di credito e debito, criptovaluta, Alipay e WeChat. Hai terminato i tuoi download rapidi per oggi. Accesso Download per ora negli ultimi 30 giorni. Media oraria: %(hourly)s. Media giornaliera: %(daily)s. Lavoriamo con i nostri partner per rendere le nostre raccolte facilmente e liberamente accessibili a chiunque. Crediamo che tutti abbiano diritto alla saggezza collettiva dell'umanità. E <a %(a_search)s>non a spese degli autori</a>. I set di dati utilizzati nell'Archivio di Anna sono completamente aperti e possono essere sottoposti a mirroring in blocco utilizzando i torrent. <a %(a_datasets)s>Maggiori informazioni…</a> Archivio a lungo termine Database completo Cerca Libri, pubblicazioni, riviste, fumetti, archivi delle biblioteche, metadati, … Tutti i nostri <a %(a_code)s>codici</a> e <a %(a_datasets)s>dati</a> sono completamente open source. <span %(span_anna)s>Anna’s Archive</span> è un progetto non-profit con due obiettivi: <li><strong>Conservazione:</strong> Backup di tutta la conoscenza e cultura dell'umanità.</li><li><strong>Accessibilità:</strong> Rendere questa conoscenza e cultura disponibile a chiunque nel mondo.</li> Abbiamo la più grande raccolta al mondo di dati testuali in alta qualità.<a %(a_llm)s>Maggiori informazioni…</a> Dati di addestramento LLM 🪩 Mirror: call per volontari/ie Se gestisci un sistema di pagamento anonimo ad alto rischio, contattaci. Siamo anche alla ricerca di persone che vogliano pubblicare piccoli annunci "di buon gusto". Tutti i proventi sono destinati al nostro impegno per la conservazione e tutela del sapere. Conservazione Stimiamo di aver conservato circa <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% di tutti i libri esistenti al mondo</a>. Conserviamo libri, documenti, fumetti, riviste e altro ancora, riunendo in un'unica posizione questi materiali provenienti da varie <a href="https://it.wikipedia.org/wiki/Biblioteca_ombra">biblioteche-ombra</a>, biblioteche ufficiali e altre raccolte. Tutti questi dati vengono conservati per sempre rendendo facile la loro duplicazione in blocco (tramite i torrent), con il risultato di avere molte copie distribuite in tutto il mondo. Alcune biblioteche-ombra lo fanno già autonomamente (ad esempio Sci-Hub, Library Genesis), mentre l'Archivio di Anna ne "libera" altre che non offrono la distribuzione in blocco (ad esempio Z-Library) o non sono affatto biblioteche-ombra (ad esempio Internet Archive, DuXiu). Questa ampia distribuzione, unita al codice open-source, rende il nostro sito web resistente agli attacchi e garantisce la conservazione a lungo termine della conoscenza e della cultura dell'umanità. Per saperne di più sui nostri <a href="/datasets"> set di dati</a>. Se sei già <a %(a_member)s>iscritto/a</a>, non è richiesta la verifica via browser. 🧬&nbsp;SciDB è la continuazione di Sci-Hub. SciDB Apri DOI Sci-Hub ha <a %(a_paused)s>sospeso</a> il caricamento di nuovi paper. Accesso diretto ai %(count)s paper accademici 🧬&nbsp;SciDB è una continuazione di Sci-Hub, con la sua interfaccia familiare e la visualizzazione diretta dei PDF. Inserisci il tuo DOI per visualizzare. Abbiamo l'intera raccolta di Sci-Hub, oltre a nuovi articoli. La maggior parte può essere visualizzata direttamente con un'interfaccia familiare, simile a Sci-Hub. Alcuni possono essere scaricati tramite fonti esterne; in quel caso, visualizziamo i link relativi. Puoi aiutarci immensamente facendo il seeding dei torrent. <a %(a_torrents)s>Maggiori informazioni…</a> >%(count)s seeder <%(count)s seeder %(count_min)s–%(count_max)s seeder 🤝 Cerchiamo volontari Come progetto open-source senza scopo di lucro, siamo sempre alla ricerca di persone che ci aiutino. Download IPFS Lista di %(by)s, creata il <span %(span_time)s>alle %(time)s</span> Salva ❌ Qualcosa è andato storto. Per favore, riprova. ✅ Salvato. Per favore, ricarica la pagina. La lista è vuota. modifica Per aggiungere o rimuovere un file da questo elenco, basta cercare un file e aprire la scheda "Liste". Lista Come possiamo aiutare Rimozione delle sovrapposizioni (deduplicazione) Estrazione di testo e metadati OCR Siamo in grado di fornire accesso ad alta velocità alle nostre raccolte complete, così come a quelle non ancora pubblicate. Un accesso a livello aziendale che possiamo fornire in cambio di donazioni nell'ordine di decine di migliaia di USD, oppure di raccolte di alta qualità di cui ancora non disponiamo. Possiamo ricompensarti se sei in grado di arricchire i nostri dati, ad esempio nei modi che seguono: Supporta l'archiviazione a lungo termine della conoscenza umana, e al contempo ottieni dati migliori per il tuo modello! <a %(a_contact)s>Contattaci</a> per discutere di una possibile collaborazione. È ben noto che gli LLM funzionano bene a partire da dati di alta qualità. Abbiamo la più grande raccolta di libri, articoli, riviste, ecc. al mondo, che sono alcune delle fonti testuali di qualità più elevata. Dati LLM Scala e ampiezza uniche La nostra raccolta contiene oltre cento milioni di file, inclusi riviste accademiche e di altro tipo e libri di testo. Otteniamo questa scala combinando repository esistenti di grandi dimensioni. Alcune delle nostre raccolte di origine sono già disponibili in blocco (Sci-Hub e parti di Libgen). Altre fonti le abbiamo liberate noi stessi. <a %(a_datasets)s>Dataset</a> mostra una panoramica completa. La nostra raccolta include milioni di libri, articoli e riviste precedenti all'epoca degli e-book. Parti importanti di questa raccolta sono già state sottoposte a OCR e hanno già poca sovrapposizione interna. Continua Se hai perso la tua chiave, <a %(a_contact)s>contattaci</a> e forniscici quante più informazioni possibili. Potrebbe essere necessario creare temporaneamente un nuovo account per contattarci. Per favore, effettua il <a %(a_account)slogin</a> per vedere questa pagina.</a> Per impedire agli spam-bot di creare una marea di account falsi, dobbiamo prima verificare il tuo browser. Se ti trovi in un loop infinito, ti consigliamo di installare <a %(a_privacypass)s>Privacy Pass</a>. Potrebbe essere utile disattivare il blocco degli annunci (Adblock) e altre estensioni del browser. Accedi/Registrati L'Archivio di Anna è temporaneamente fuori servizio per manutenzione. Si prega di tornare tra un'ora. Autore alternativo Descrizione alternativa Edizione alternativa Estensione alternativa Nome file alternativo Editore alternativo Titolo alternativo Data "open sourced" Maggiori informazioni… Descrizione Cerca nell'Anna’s Archive il numero CADAL SSNO Cerca nell'Anna’s Archive il numero SSID DuXiu Cerca nell'Archivio di Anna per codice DXID DuXiu Cerca nell'Archivio di Anna per ISBN Cerca nell'Archivio di Anna per codice OCLC (WorldCat) Cerca nell'Archivio di Anna per ID Open Library Visualizzatore online dell'Archivio di Anna %(count)s pagine interessate Dopo il download: Una versione migliore di questo file potrebbe essere disponibile su %(link)s Scarica torrent in blocco raccolta Utilizza strumenti online per la conversione tra formati. Strumenti di conversione consigliati: %(links)s Per file di grandi dimensioni, consigliamo di utilizzare un download manager per evitare interruzioni. Download manager consigliati: %(links)s Indice eBook EBSCOhost (solo esperti) (fai clic anche su "GET" in alto) (fai clic su “GET” in alto) Download esterni Ne hai %(remaining)s rimanenti per oggi. Grazie per essere dei nostri! ❤️ Hai esaurito i download rapidi per oggi. Di recente hai scaricato questo file. I link restano validi per un po'. Diventa un <a %(a_membership)s>membro</a> per supportarci nella conservazione a lungo termine di libri, pubblicazioni e molto altro. Per dimostrarti quanto te ne siamo grati, avrai accesso ai download rapidi. ❤️ 🚀 Download veloci 🐢 Download lenti Prendi in prestito da Internet Archive Gateway IPFS #%(num)d (potrebbe essere necessario provare più volte con IPFS) Libgen.li Libgen.rs - Narrativa Libgen.rs - Saggistica i loro annunci sono noti per contenere software dannosi, quindi usa un ad blocker o evitare di fare clic sugli annunci “Invia a Kindle” di Amazon “Invia a Kobo/Kindle” di djazz MagzDB ManualsLib Nexus/STC (È possibile che i file Nexus/STC non siano affidabili per il download) Nessun download trovato. Tutti i mirror possiedono lo stesso file e dovrebbero essere sicuri da usare. Fai sempre attenzione, però, quando scarichi file da Internet e assicurati di mantenere aggiornati i tuoi dispositivi. (nessun reindirizzamento) Apri nel nostro visualizzatore (apri nel visualizzatore) Opzione #%(num)d: %(link)s %(extra)s Trova l'archivio originale su CADAL Ricerca manuale su DuXiu Trova il record originale in ISBNdb Trova il record originale in WorldCat Trova il record originale in Open Library Cerca in altri database per ISBN (solo per utenti verificati con stampa disabitata) PubMed A seconda del formato del file, per aprirlo avrai bisogno di un lettore ebook o PDF. Lettori ebook consigliati: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (il DOI associato potrebbe non essere disponibile su Sci-Hub) Puoi inviare file PDF ed EPUB al tuo eReader Kindle o Kobo. Strumenti consigliati: %(links)s Maggiori informazioni nelle <a %(a_slow)s>FAQ</a>. Supporta autori e biblioteche Se ti piace e puoi permettertelo, considera di acquistare l'originale o di supportare direttamente gli autori. Se è disponibile presso la tua biblioteca locale, considera di prenderlo in prestito gratuitamente lì. Download da server partner attualmente non disponibile per questo file. torrent Da partner affidabili. Z-Library Z-Library TOR (necessita di browser TOR) mostra download esterni <span class="font-bold">❌ Questo file potrebbe presentare delle anomalie ed è stato nascosto da una libreria di origine.</span> Questo talvolta succede su richiesta del possessore di copyright oppure perché c'è un alternativa migliore disponibile, ma più spesso perché c'è un problema con il file stesso. Il download potrebbe comunque andare a buon fine ma raccomandiamo di cercare un file alternativo. Maggiori informazioni: Se desideri comunque scaricare questo file, assicurati di utilizzare solo software affidabili e aggiornati per aprirlo. Commenti sui metadati AA: Cerca nell'Archivio di Anna per “%(name)s” Esploratore di Codici: Visualizza in Esploratore di Codici “%(name)s” URL: Sito web: Se possiedi questo file e non è ancora disponibile nell'Archivio di Anna, puoi decidere di <a %(a_request)s>caricarlo</a>. Internet Archive Controlled Digital Lending file “%(id)s” Questo è il record relativo a un file proveniente da Internet Archive, non è il file sorgente scaricabile. Puoi provare a prendere in prestito il libro (link qui sotto) oppure usare questo URL per <a %(a_request)s> richiedere un file</a>. Migliora i metadati Record di metadati CADAL SSNO %(id)s Questo è un record di metadati, non un file scaricabile. Puoi utilizzare questo URL <a %(a_request)s>per richiedere un file</a>. Record di metadati DuXiu SSID %(id)s ISBNdb %(id)s record di metadati Record di metadati di MagzDB ID %(id)s Record di metadati di Nexus/STC ID %(id)s Numero OCLC (WorldCat) %(id)s record di metadati Open Library %(id)s record di metadati Sci-Hub file “%(id)s” Non trovato “%(md5_input)s” non è stato trovato nel nostro database. Aggiungi commento (%(count)s) Puoi ottenere l'md5 dall'URL, ad esempio MD5 di una versione migliore di questo file (se applicabile). Compila questo campo se c'è un altro file che quasi coincide con questo file (stessa edizione, stessa estensione del file se riesci a trovarne uno), che le persone dovrebbero usare invece di questo file. Se conosci una versione migliore di questo file al di fuori di Anna’s Archive, allora per favore <a %(a_upload)s>caricala</a>. Qualcosa è andato storto. Ricarica la pagina e riprova. Hai lasciato un commento. Potrebbe volerci un minuto prima che venga visualizzato. Per favore usa il <a %(a_copyright)s>modulo di reclamo DMCA / Copyright</a>. Descrivi il problema (obbligatorio) Se questo file è di alta qualità, puoi discutere di qualsiasi cosa che lo riguardi qui! In caso contrario, utilizza il pulsante “Segnala un problema con il file”. Ottima qualità del file (%(count)s) Qualità del file Scopri come <a %(a_metadata)s>migliorare i metadati</a> di questo file in modo autonomo. Descrizione del problema Per favore <a %(a_login)s>accedi</a>. Ho adorato questo libro! Aiuta la community segnalando la qualità di questo file! 🙌 Qualcosa è andato storto. Ricarica la pagina e riprova. Segnala un problema con il file (%(count)s) Grazie per aver inviato il tuo report. Sarà mostrato su questa pagina e verrà esaminato manualmente da Anna (fino a quando non avremo un sistema di moderazione adeguato). Lascia un commento Invia segnalazione Cosa c'è che non va in questo file? Prestiti (%(count)s) Commenti (%(count)s) Download (%(count)s) Esplora metadati (%(count)s) Liste (%(count)s) Statistiche (%(count)s) Per informazioni su questo particolare file, consulta il suo <a %(a_href)s>file JSON</a>. Questo file è gestito dalla biblioteca <a %(a_ia)s>Controlled Digital Lending di IA</a> e indicizzato dall'Archivio di Anna per la ricerca. Per informazioni sui vari dataset che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dedicata</a>. Metadati dal record collegato Migliora i metadati su Open Library Un 'file MD5' è un hash calcolato a partire dal contenuto del file e risulta ragionevolmente univoco sulla base di quel contenuto. Tutte le biblioteche-ombra che abbiamo indicizzato qui utilizzano principalmente gli MD5 per identificare i file. Un file potrebbe essere presente in più biblioteche-ombra. Per informazioni sui vari dataset che abbiamo compilato, consulta la <a %(a_datasets)s>pagina dei Dataset</a>. Segnala la qualità del file Download totali: %(total)s SSNO CADAL %(id)s} Cerlalc %(id)s} Metadati cechi %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Libri %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Attenzione: più registri con link: Quando consulti un libro nell'Archivio di Anna, puoi vedere vari campi: titolo, autore, editore, edizione, anno, descrizione, nome del file e altro. Tutte queste informazioni sono chiamate <em>metadati</em>. Poiché combiniamo libri da varie <em>biblioteche di origine</em>, mostriamo qualsiasi metadato sia disponibile in quella biblioteca di origine. Ad esempio, per un libro che abbiamo ottenuto da Library Genesis, mostreremo il titolo dal database di Library Genesis. A volte un libro è presente in <em>più</em> biblioteche di origine, che potrebbero avere campi di metadati diversi. In tal caso, mostriamo semplicemente la versione più lunga di ciascun campo, che sperabilmente includerà le informazioni più utili. Mostriamo comunque gli altri campi sotto la descrizione, ad esempio come "titolo alternativo" (ma solo se sono diversi). Estraiamo anche <em>codici</em> come gli identificatori e classificatori dalla biblioteca di origine. <em>Gli identificatori</em> rappresentano in modo univoco una particolare edizione di un libro; esempi sono ISBN, DOI, Open Library ID, Google Books ID o Amazon ID. <em>I classificatori</em> raggruppano insieme più libri simili; esempi sono Dewey Decimal (DCC), UDC, LCC, RVK o GOST. A volte questi codici sono esplicitamente collegati nelle biblioteche di origine, e a volte possiamo estrarli dal nome del file o dalla descrizione (principalmente ISBN e DOI). Possiamo usare gli identificatori per trovare record in <em>raccolte di soli metadati</em>, come OpenLibrary, ISBNdb o WorldCat/OCLC. C'è una specifica <em>scheda dei metadati</em> nel nostro motore di ricerca se desideri sfogliare quelle raccolte. Utilizziamo i record corrispondenti per compilare i campi di metadati mancanti (ad esempio, se manca un titolo), o ad esempio come "titolo alternativo" (se un titolo è presente). Per vedere esattamente da dove provengono i metadati di un libro, consulta la <em>scheda "Dettagli tecnici"</em> sulla pagina di un libro. In essa è presente un link al file JSON non elaborato di quel libro, con collegamenti al file JSON non elaborato dei record originali. Per ulteriori informazioni, consulta le seguenti pagine: <a %(a_datasets)s>Dataset</a>, <a %(a_search_metadata)s>Ricerca (scheda metadati)</a>, <a %(a_codes)s>Esploratore di codici</a> e <a %(a_example)s>Esempio di metadati JSON</a>. Infine, tutti i nostri metadati possono essere <a %(a_generated)s>generati</a> o <a %(a_downloaded)s>scaricati</a> come database ElasticSearch e MariaDB. Informazioni di base Puoi contribuire alla conservazione dei libri migliorando i metadati! Prima, leggi le informazioni di base sui metadati nell'Archivio di Anna, impara come migliorare i metadati collegandoli con Open Library, e guadagna un abbonamento gratuito all'Archivio di Anna. Migliora i metadati Quindi, se trovi un file con metadati errati, cosa dovresti fare per correggerlo? Puoi fare riferimento alla biblioteca di origine e seguire le loro procedure per la correzione dei metadati, ma cosa fare se un file è presente in più biblioteche di origine? C'è un identificatore che è trattato in modo speciale nell'Archivio di Anna’. <strong>Il campo annas_archive md5 su Open Library sovrascrive sempre tutti gli altri metadati!</strong> Per prima cosa è bene fare un passo indietro e imparare a conoscere Open Library. Open Library è stata fondata nel 2006 da Aaron Swartz con l'obiettivo di creare "una pagina web per ogni libro mai pubblicato". È una sorta di Wikipedia per i metadati dei libri: chiunque può modificarla, è in licenza libera e può essere scaricata in blocco. È un database di libri che è più allineato con la nostra missione: infatti, l'Archivio di Anna è ispirato alla visione e alla vita di Aaron Swartz. Invece di reinventare la ruota, abbiamo deciso di indirizzare i nostri volontari verso Open Library. Se noti un libro con metadati errati, puoi aiutare nel seguente modo: Nota che questo vale solo per i libri, non per articoli accademici o altri tipi di file. Per altri tipi di file consigliamo comunque di trovare la biblioteca di origine. Potrebbero volerci alcune settimane prima che le modifiche vengano incluse nell'Archivio di Anna, poiché dobbiamo scaricare l'ultimo dump di dati di Open Library e rigenerare il nostro indice di ricerca.  Collegati al <a %(a_openlib)s>sito web di Open Library</a>. Trova il record corretto del libro. <strong>ATTENZIONE:</strong> assicurati di selezionare la <strong>edizione</strong> corretta. In Open Library, ci sono "opere" ed "edizioni". Un'"opera" potrebbe essere "Harry Potter e la Pietra Filosofale". Un'"edizione" potrebbe essere: La prima edizione del 1997 pubblicata da Bloomsbury di 256 pagine. L'edizione tascabile del 2003 di 223 pagine, pubblicata da Raincoast Books. La traduzione polacca del 2000, di 328 pagine, “Harry Potter I Kamie Filozoficzn” di Media Rodzina. Tutte queste edizioni hanno ISBN e contenuti diversi, quindi assicurati di selezionare quella giusta! Modifica il record (o crealo se non esiste) e aggiungi quante più informazioni utili possibile! Intanto che ci sei, approfittane per rendere questo record davvero completo. Sotto “ID Numbers” seleziona “Anna's Archive” e aggiungi l'MD5 del libro dall'Archivio di Anna. Si tratta della lunga stringa di lettere e numeri dopo “/md5/” nell'URL. Cerca di trovare altri file nell'Archivio di Anna che corrispondano a questo record e aggiungi anche quelli. In futuro potremo raggrupparli come duplicati nella pagina di ricerca dell'Archivio di Anna. Quando hai finito, annota l'URL che hai appena aggiornato. Una volta che hai aggiornato almeno 30 record con gli MD5 dell'Archivio di Anna, inviaci una <a %(a_contact)s>email</a> con l'elenco. Ti forniremo un abbonamento gratuito all'Archivio di Anna, così potrai svolgere questa attività più facilmente (e come ringraziamento per il tuo aiuto). Le modifiche devono essere di alta qualità e aggiungere una quantità sostanziale di informazioni, altrimenti la tua richiesta verrà respinta. La tua richiesta verrà respinta anche se una delle tue modifiche viene annullata o corretta dai moderatori di Open Library. Collegamento con Open Library Se il tuo coinvolgimento nello sviluppo e nel funzionamento del nostro lavoro diventa significativo, possiamo discutere la condivisione con te di una quota maggiore delle donazioni, perché tu le utilizzi secondo necessità. Pagheremo per l'hosting solo una volta che avrai tutto configurato e avrai dimostrato di essere in grado di mantenere l'archivio aggiornato. Questo significa che dovrai pagare di tasca tua per i primi 1-2 mesi. Non riceverai un compenso per il tuo tempo (così come non lo riceviamo noi), poiché si tratta di puro volontariato. Siamo disponibili a coprire le spese di hosting e VPN, inizialmente fino a $200 al mese. Questo è sufficiente per un server di ricerca di base e un proxy protetto da DMCA. Spese di hosting Per favore <strong>non contattarci</strong> per chiederci l'autorizzazione a cominciare, o per domande di base. Un'azione parla più di mille parole! Tutte le informazioni necessarie sono disponibili, quindi procedi pure con l'impostazione del tuo mirror. Non esitare a pubblicare ticket o richieste di merge sul nostro Gitlab quando incontri problemi. Potremmo dover costruire alcune funzionalità specifiche per il mirror con te, come il rebranding da “L'Archivio di Anna” al nome del tuo sito web, disabilitare gli account utente (inizialmente), o creare collegamenti dalle pagine dei libri al nostro sito principale. Una volta che il tuo mirror è attivo, contattaci. Vorremo rivedere la tua OpSec, e una volta che sarà solida, collegheremo il tuo mirror e inizieremo a collaborare più da vicino. Grazie in anticipo a chiunque sia disposto a contribuire in questo modo! Non è per i deboli di cuore, ma consoliderebbe la longevità della più grande biblioteca veramente aperta nella storia umana. Come iniziare Per aumentare la resilienza dell'Archivio di Anna, stiamo cercando volontari e volontarie per gestire i mirror. La tua versione è chiaramente indicata come mirror, ad esempio “Archivio di Bob, un mirror dell'Archivio di Anna”. Sei disponibile ad assumerti i rischi associati a questo lavoro, che sono significativi. Hai una profonda comprensione della sicurezza operativa richiesta. Il contenuto di <a %(a_shadow)s>questi</a> <a %(a_pirate)s>post</a> ti è chiaro ed evidente. Inizialmente non ti daremo accesso ai download del nostro server partner, ma se le cose andranno bene, lo condivideremo con te. Gestisci il codice open source dell'Archivio di Anna e aggiorni regolarmente sia il codice che i dati. A questo scopo, sei disponibile a contribuire al nostro <a %(a_codebase)s>codice sorgente</a>, in collaborazione con il nostro team. Ecco cosa cerchiamo: Mirror: chiamata per volontari e volontarie Fai un'altra donazione. Ancora nessuna donazione. <a %(a_donate)s>Fai la tua prima donazione.</a> I dettagli delle donazioni non sono mostrati pubblicamente. Le mie donazioni 📡 Per il mirroring in blocco della nostra raccolta, vai alle pagine <a %(a_datasets)s>Dataset</a> e <a %(a_torrents)s>Torrent</a>. Download dal tuo indirizzo IP nelle ultime 24 ore: %(count)s. 🚀 Per ottenere download rapidi e saltare il controllo del browser, <a %(a_membership)s>iscriviti</a>. Scarica dal sito web del partner Non esitare a continuare a navigare all'interno dell'Archivio di Anna utilizzando un'altra scheda mentre aspetti (se il tuo browser supporta l'aggiornamento delle schede in background). Sentiti libero di aspettare che più pagine di download si carichino contemporaneamente (ma per favore scarica solo un file alla volta per server). Una volta ottenuto un link di download, questo è valido per diverse ore. Grazie per l'attesa, questo mantiene il sito accessibile gratuitamente per tutti! 😊 <a %(a_main)s>&lt; Tutti i link per scaricare questo file</a> ❌ I download lenti non sono disponibili attraverso le VPN Cloudflare o dagli indirizzi IP Cloudflare. ❌ I download lenti sono disponibili solo attraverso il sito ufficiale. Visita %(websites)s. <a %(a_download)s>📚 Scarica ora</a> Per dare a tutte le persone l'opportunità di scaricare file gratuitamente, devi attendere prima di poter scaricare questo file. Attendi <span %(span_countdown)s>%(wait_seconds)s</span> secondi per scaricare questo file. Attenzione: nelle ultime 24 ore sono stati effettuati molti download dal tuo indirizzo IP. I download potrebbero essere più lenti del solito. Se stai usando una VPN, una connessione internet condivisa, o il tuo ISP condivide gli IP, questo avviso potrebbe essere dovuto a ciò. Salva ❌ Qualcosa è andato storto. Per favore, riprova. ✅ Salvato. Per favore, ricarica la pagina. Cambia il tuo nome profilo pubblico. Il tuo identificatore (la parte dopo il "#") non può essere cambiata. Profilo creato il <span %(span_time)s> alle %(time)s</span> modifica Liste Crea un nuovo elenco cercando un file e aprendo la scheda "Liste". Ancora nessuna lista Profilo non trovato. Profilo Al momento purtroppo non riusciamo a gestire le richieste di nuovi libri. Non scriverci un'email per effettuare una richiesta di nuovi libri. Per favore, formula le tue richieste nei forum Z-Library o Libgen. Record in Anna’s Archive DOI: %(doi)s Scarica SciDB Nexus/STC Nessuna anteprima disponibile al momento. Scarica il file dall'<a %(a_path)s>Archivio di Anna</a>. Per supportare l'accessibilità e la conservazione a lungo termine della conoscenza umana, diventa un <a %(a_donate)s>membro</a>. Come bonus, 🧬&nbsp;SciDB si carica più velocemente per i membri, senza alcun limite. Non funziona? Prova ad <a %(a_refresh)s>aggiornare</a>. Sci-Hub Aggiungi un campo di ricerca specifico Cerca descrizioni e commenti ai metadati Anno di pubblicazione Avanzato Accesso Contenuto Visualizza Elenco Tabella Tipo di file Lingua Ordina per Più grande Più rilevanti Più recente (dimensione del file) (reso open source) (anno di pubblicazione) Meno recente Casuale Più piccolo Fonte Scovato e reso open-source da AA Prestiti digitali (%(count)s) Articoli di giornale (%(count)s) Abbiamo trovato delle corrispondenze in: %(in)s. Puoi fare riferimento all'URL trovato quando <a %(a_request)s>richiedi un file</a>. Metadati (%(count)s) Per esplorare l'indice di ricerca tramite codici, utilizza il <a %(a_href)s>Codes Explorer</a>. L'indice di ricerca è aggiornato mensilmente. Al momento include voci fino al %(last_data_refresh_date)s. Per maggiori informazioni tecniche , consulta la %(link_open_tag)spagina dei dataset</a>. Escludi Includi solo Non verificato altro… Successivo … Precedente Questo indice di ricerca include i metadati relativi alla libreria "Controlled Digital Lending" di Internet Archive. <a %(a_datasets)s>Maggiori informazioni sui nostri dataset</a>. Per altre biblioteche con prestiti digitali, consulta <a %(a_wikipedia)s>Wikipedia</a> e <a %(a_mobileread)s>MobileRead Wiki</a>. Per reclami DMCA/copyright, <a %(a_copyright)s>clicca qui</a>. Tempo di download Errore durante la ricerca. Prova a <a %(a_reload)s>ricaricare la pagina</a>. Se il problema persiste, per favore scrivici un'email a %(email)s. Download rapido Chiunque può contribuire a proteggere questi file facendo da seeder al nostro <a %(a_torrents)s>elenco unificato di torrent</a>. ➡️ A volte questo accade in modo errato quando il server di ricerca è lento. In tali casi, un <a %(a_attrs)s>reload</a> può aiutare. ❌ Questo file potrebbe avere dei problemi. Stai cercando dei paper? Questo indice di ricerca include attualmente metadati provenienti da varie fonti. <a %(a_datasets)s>Maggiori informazioni sui nostri dataset</a>. Esistono moltissime fonti di metadati per le opere scritte in tutto il mondo. <a %(a_wikipedia)s>Questa pagina di Wikipedia</a> è un buon inizio ma se conosci altri elenchi validi, faccelo sapere! Per i metadati, mostriamo i record originali. Non effettuiamo unioni di record. Attualmente disponiamo del catalogo aperto più completo al mondo di libri, pubblicazioni, riviste e altre opere scritte. Siamo un mirror di Sci-Hub, Library Genesis, Z-Library e <a %(a_datasets)s>altri ancora</a>. <span %(classname)s>Nessun file trovato.</span> Prova a utilizzare meno termini di ricerca o filtri, o a modificarli. Risultato %(from)s-%(to)s (%(total)s totale) Se conosci altre biblioteche ombra di cui dovremmo fare il mirroring o se hai delle domande, contattaci all'indirizzo email %(email)s. %(num)d corrispondenze parziali %(num)d+ corrispondenze parziali Digita nella casella per cercare i file nelle biblioteche con prestito digitale. Digita nella casella per cercare nel nostro catalogo tra i %(count)s file direttamente scaricabili che <a %(a_preserve)s>conserveremo per sempre</a>. Digita nella casella per effettuare la ricerca. Digita nella casella per cercare nel nostro catalogo di %(count)s documenti accademici e articoli di giornali/riviste che noi <a %(a_preserve)s>conserveremo per sempre</a>. Digita nella casella per cercare i metadati dalle biblioteche. Questo può essere utile quando <a %(a_request)s>richiedi un file</a>. Suggerimento: utilizza le scorciatoie da tastiera "/" (focus di ricerca), "Invio" (ricerca), "J" (su), "K" (giù) per una navigazione più rapida. Questi sono record di metadati, <span %(classname)s>non</span> file scaricabili. Impostazioni di ricerca Cerca Prestito digitale Scarica Articoli di giornale Metadati Nuova ricerca %(search_input)s - Cerca La ricerca ha richiesto troppo tempo, il che significa che i risultati potrebbero essere imprecisi. A volte può servire <a %(a_reload)s></a> ricaricare la pagina. La ricerca ha richiesto troppo tempo, cosa comune per le query ampie. Il conteggio dei filtri potrebbe non essere accurato. Per il caricamento di grandi quantità di file (più di 10.000 file) che non vengono accettati da Libgen o Z-Library, per favore scrivici a %(a_email)s. Per Libgen.li, assicurati di eseguire prima il login sul <a %(a_forum)s>forum del sito</a> con nome utente %(username)s e password %(password)s, e poi torna alla <a %(a_upload_page)s>pagina di upload</a>. Per il momento, ti consigliamo di caricare nuovi libri su un fork di Library Genesis. Ecco una <a %(a_guide)s>guida pratica</a>. Nota: entrambi i fork che vengono indicizzati su questo sito si basano sullo stesso sistema di caricamenti. Piccoli upload (fino a 10.000 file): carica sia su %(first)s sia su %(second)s. In alternativa, puoi caricarli su Z-Library <a %(a_upload)s>qui</a>. Per caricare articoli accademici, per favore (oltre a Library Genesis) caricali anche su <a %(a_stc_nexus)s>STC Nexus</a>. Sono la migliore biblioteca ombra per nuovi articoli. Non li abbiamo ancora integrati, ma lo faremo prima o poi. Puoi usare il loro <a %(a_telegram)s>bot di caricamento su Telegram</a>, o contattare l'indirizzo elencato nel loro messaggi fissati se hai troppi file da caricare in questo modo. <span %(label)s>Attività di volontariato intenso (ricompense da USD$50 a USD$5.000):</span> se sei in grado di dedicare molto tempo e/o risorse alla nostra missione, ci piacerebbe collaborare più da vicino con te. Potresti perfino arrivare a unirti al nostro team interno. Anche se abbiamo un budget limitato, siamo in grado di assegnare <span %(bold)s>💰 ricompense monetarie</span> per le attività più intense. <span %(label)s>Attività di volontariato leggero:</span> se puoi dedicare solo poche ore qua e là, ci sono ancora molti modi in cui puoi aiutare. Ricompensiamo le persone più costanti con <span %(bold)s>🤝 abbonamenti all'Archivio di Anna</span>. L'Archivio di Anna si affida a volontari e volontarie come te. Tutti i livelli di impegno sono benvenuti e cerchiamo aiuto in due categorie principali: Se non puoi dedicarci tempo come volontario o volontaria, puoi comunque aiutarci molto <a %(a_donate)s>facendo una donazione in denaro</a>, <a %(a_torrents)s>eseguendo il seeding dei nostri torrent</a>, <a %(a_uploading)s>caricando libri</a> o <a %(a_help)s>parlando ai tuoi amici dell'Archivio di Anna</a>. <span %(bold)s>Aziende:</span> offriamo accesso diretto ad alta velocità alle nostre raccolte in cambio di donazioni di livello aziendale o di nuove raccolte (ad esempio, nuove scansioni, dataset di cui è stato eseguito l'OCR, arricchimento dei nostri dati). <a %(a_contact)s>Contattaci</a> se la cosa ti interessa. Consulta anche la nostra <a %(a_llm)s>pagina LLM</a>. Taglie Siamo sempre alla ricerca di persone con solide competenze di programmazione o sicurezza offensiva. Puoi dare un contributo significativo alla preservazione dell'eredità dell'umanità. A titolo di ringraziamento, regaliamo un abbonamento per contributi importanti. A titolo di ulteriore ringraziamento, offriamo ricompense monetarie per attività particolarmente importanti e difficili. Questo non dovrebbe essere visto come un sostituto di un lavoro, ma è un incentivo extra e può aiutare con i costi sostenuti. La maggior parte del nostro codice è open source, e chiederemo che lo sia anche il tuo quando ti assegneremo la ricompensa prevista. Ci sono alcune eccezioni che possiamo discutere su base individuale. Le ricompense vengono assegnate alla prima persona che completa un'attività. Non esitare a lasciare un commento su un ticket di ricompensa per far sapere ad altre persone che ci stai lavorando, in modo che possano aspettare o contattarti per collaborare. Ma sii consapevole che le altre persone sono comunque libere di cercare di batterti sul tempo. Tuttavia, non assegniamo ricompense per lavori scadenti. Se due submit di alta qualità vengono effettuate a un giorno o due di distanza tra loro, potremo scegliere di assegnare una ricompensa a entrambe, a nostra discrezione, ad esempio 100%% per la prima submit e 50%% per la seconda (quindi 150%% in totale). Per le ricompense più grandi (specialmente di scraping), contattaci quando hai completato più o meno il 5%% dell'attività, e hai la certezza che il tuo metodo scalerà fino al completamento. Dovrai condividere il tuo metodo con noi in modo che possiamo darti un feedback. Inoltre, in questo modo possiamo decidere cosa fare se ci sono più persone che sono vicine a una ricompensa, ad esempio assegnarla a più persone, incoraggiarle a collaborare, ecc. ATTENZIONE: le attività con ricompense elevate sono <span %(bold)s>complesse</span>. È consigliabile iniziare con quelle più facili. Vai alla nostra <a %(a_gitlab)s>lista di problemi su Gitlab</a> e ordina per “Priorità etichetta” per visualizzare in ordine le attività che ci interessano. Le attività senza ricompense esplicite sono comunque idonee per ricevere un abbonamento, specialmente quelle contrassegnate come “Accettata” e “Preferita di Anna”. Può essere consigliabile iniziare da un “Progetto iniziale”. Volontariato light Ora abbiamo anche un canale Matrix sincronizzato su %(matrix)s. Se hai qualche ora libera, puoi aiutarci in diversi modi. Assicurati di seguire la <a %(a_telegram)s>chat dei volontari su Telegram</a>. Come segno di apprezzamento, di solito offriamo 6 mesi di abbonamento “Bibliotecario fortunato” per traguardi di base, e di più per attività di volontariato continuative. Tutti i traguardi richiedono lavoro di alta qualità: un lavoro scadente ci danneggia più di quanto ci aiuti e lo rifiuteremo. Per favore <a %(a_contact)s>inviaci un'email</a> quando raggiungi un traguardo. %(links)s link o screenshot di richieste che hai soddisfatto. Soddisfare le richieste di libri (o articoli, ecc.) sui forum di Z-Library o Library Genesis. Non abbiamo un nostro sistema di richieste di libri, ma facciamo il mirror di quelle biblioteche, quindi migliorarle rende migliore anche l'Archivio di Anna. Traguardo Attività Dipende dall'attività. Piccole attività pubblicate nella nostra <a %(a_telegram)s>chat dei volontari su Telegram</a>. Di solito in cambio dell'abbonamento, a volte di piccole ricompense. Piccole attività pubblicate nella nostra chat di gruppo per volontari. Assicurati di lasciare un commento sui problemi che risolvi, in modo da evitare che altri ripetano il tuo lavoro. %(links)s link di record che hai migliorato. Puoi usare la <a %(a_list)s >lista di problemi vari di metadati</a> come punto di partenza. Migliora i metadati <a %(a_metadata)s>collegandoli</a> con Open Library. Questi dovrebbero evidenziare che parli ad altre persone dell'Archivio di Anna, con i loro ringraziamenti. %(links)s link o screenshot. Parlando dell'Archivio di Anna ad altre persone: ad esempio, consigliando libri su AA, creando link ai nostri post sul blog o rimandando le persone al nostro sito web. Tradurre completamente una lingua (se non era già quasi completata) <a %(a_translate)s>Tradurre</a> il sito web. Link alla cronologia delle modifiche che mostra che hai fornito contributi significativi. Migliora la pagina Wikipedia dell'Archivio di Anna nella tua lingua. Includi informazioni dalla pagina Wikipedia in altre lingue, e dal nostro sito web e blog. Aggiungi riferimenti su altre pagine rilevanti. Volontariato & Ricompense 