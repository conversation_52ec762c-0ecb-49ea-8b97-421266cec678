��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ld �  Wg 2   i =  Ri N  �j 7  �l �  o k  �p _   6r }   �r G   s d   \s   �s �  �u �   _w �  ]x h  �z �  J| �  �~ �  �� V  w� .  ΅ �  �� K   �� L  <� Q   �� G  ۋ    #� �  >� G   ;� *   ��    �� !   �� Y   � '   =�    e� K   {� 0   ǒ !   ��    � N   *� �  y� �  ?� �   	� ?   ��    Η -   ߗ    
�    $� #   0�    T� 	   h�    r�    �� %   ��    �� &   �� 	   ݘ 
   �    ��    �    �    9� &  Q� �  x� �   )� 2   �    �   $� \  B� �   ��    n� �   ��    (� �   C�    � b  "�    �� �  �� =   f� q   ��    � C  � i  a� J  ˭ �  �    ű l   ٱ �   F� 
  � E   � `   8� �   �� F   3� r   z� i   � 	   W�    a� ?  � Q   !� �   s�   6� f   C� Q  �� Z   �� O  W� �   �� �   @� h   %�   �� �   �� G   \� S  �� �   �� �   �� S   �� i   �� 	   D� �   N� a  � L   e�    �� �   ��   ��    ��   �� �   ��   �� �  �� �   �� �  �� ;  � i   R� _   �� N   � �   k�    �� c   z� 3   �� �   � u   �� i   \�    �� �   �� ]   p� �   �� �   ��    �� �  �� :  F� �  �� �  #� �   �� 2  �� �  � �   �� �   �� �   s� �  r� �   
� -  ��    �� �   � 
  �� 	  �� �  �� �   �� �   )� �   �� 4  �� �   �� �  �� c  y� O   �� |   -� �   �� |   u�   �� �     r   �     P /   � �      � �  �    z r   � �   
   � <  �	    0  6   g B   � �  � Q  � :  � (  9 k  b h  � �   7 �   �     �   � z  V �  �  A  �"   	$ �   %    �% P  �% �  J( ;  *    U+   h+ �  k, ,  . �  C/   2 j   3    p3 -  �3 �  �5    �7 F  �7 _  �8   C: �   J= 	  J> �   T@ i   �@ �  aA 6   �B    'C �  @C h  E k  oF K   �G L   'H w  tH �   �J �   �K �   DL �  �L "   �N    �N F  �N �   ?Q �   1R ]   �R �   S 6   �S M  �S L  "U    oV    V *   �V Q   �V 0   	W 6   :W N   qW �   �W    WX 9   vX f   �X N   Y -   fY K   �Y 0   �Y r   Z    �Z    �Z R  �Z h  �[ >  f] q  �^ �   `   a E  b    Zc )  yc �   �d �   �e �  ff �  �g ~  �i �  Sl �  An L   p K   [p    �p �   �p L  �q   �s J  �u �  (w T  �x �  7z (  �| ?   �} ;  !~ �  ] �  �    ۂ ^  � �  S� �   ݅ c  z�   އ    � 9    � �   :�    ȉ   ۉ �  � �  ׎ �   ��    x�    �� ;  �� ,  � L   � r   e� 5  ؖ /  � �   >� �  � i   �� b  �    �� �  �� �   w� x  V� ,  ϣ v  �� �   s�    i�   p�   �� !   �� �  ��   p� �  �� j  _� ;  ʶ �  � L   �� J  ݹ    (� �   I� K   ս �   !� 	   �� R   �� $   � /   6� 5   f� %   �� �   ¿ s  �� n   � �  o� H  �    h� �  {� �  p� �  =� �  �� B  �� X   �    Y� S  `�    ��    �� �  �� �  �� �  �� �  7� 	   �� I  �� C  F� �  �� �  =� �  � �  ��   ��    �� �   �� �   �� P  o� <  �� �   �  �   � �   � �  }     m �   � h   ] Q   �     9   1 >   k   � �  �
   � T   � b  � �   a    J �   W ]    �   m    J   & k   q !   � �  � )  � C  �    �   !    �! �   �! d  L" 	   �# �   �# u  |$ �   �' V   �( Z   %) �  �) 2   3+ �  f+ �  @. a  0 '   t2 �   �2 �   <3 �  �3   �6    �8 4  �8 <   �9   *: �   0; |  <   �= x   �> 2   ? R  K? W   �@ M  �@ �  DD 5  F }  BG E   �H U   I �   \I �  :J   M �  O �  Q 3   S �   9S �  -T n  �U �   W �  �W �  �Z 2   �\ �  �\ 6   g_ L  �_ �   �a    nb �  �b �  2e �  �g   �i l  �j �  l �  �n �   �r �  �s   �u �   �w �  Nx �  I| Q   �} �   '~ �   �~ #   l 	   � �  � �   i� 3   I� N  }� B  ̃ |  � t  �� �  � �   ԋ S   u  � #   �� �   ��    �� �  �� H   �� 3   �    "�    (�    5�    P�    i� $   ��    ��    �� 	   ͔    ה    �    ��    
�    #� 0   *�    [�    b�    o�    v�   }� u   � )   ��    � *   2�     ]� 3   ~� <   �� )   �    �    2�    9�    R�    q�    ~�    ��    �� 	   ��    �� <   �� -   � .   � @   J� I   �� ;   ՙ 6   � !   H� -   j� A   ��    ښ �   � M   z� 	   ț q   қ \   D�    ��    �� "   Μ    �    �    �    3�    R�    k�    s�    ��    ��    �� 	   ŝ 
   ϝ    ڝ 3   ݝ    �    � 	   !�    +� 	   A� -   K�    y�    � 	   �� (   ��    ��    Ş    ޞ    �    �     �    (� 	   C�    M� +   ^�    ��    �� '   ��    ��    ğ 	   ڟ $   �    	� 	   �     � �   6� �   �� v   �� �   �   �� �   �    ��    Х    �    ��    ��    � 	   '� N   1� `   �� Q   � _   3�    �� K   �� ?   �� �   >� �   è   _� �   |� o   � '   r�    ��    ��    �� 	   ӫ    ݫ !   �� 	   � !   "�    D�    W� '   ^�    �� 	   ��    ��    ��    ɬ    ݬ    �    �    
�    �    &� 0   7�    h�   ��    ��    ��    �� #   ��    خ R   ߮ l   2� :   �� _   گ K   :�    ��    ��    �� �   ��    9�    ?� 7   [� s   �� $   �    ,� �   H� A   ܲ �  � }   Ѷ �   O� �   �   �� H   ȹ O  � �   a� _  ��   W� !   f�    �� l   �� u   � f   {� f   � �   I� ]   �� �   7� +   �� G   ��    ;� $   B� r   g� $   ��    �� $   �    4� *   A� �   l� 	   �� '   �� �   &� !   �� -   �� ]   � �   a� �  ��    ��     � �   �    �� 	   ��    ��    �    )� ,   <� �  i� !   � 	   ?� !   I�    k� �  ~� 6   �    Q�    ^� @   t� 	   ��    �� 5   �� $   � 	   '� '   1� �   Y�     �    =� ?   Y� %   ��    ��    ��    �� 9   �� o   ,�    �� 6   �� �   �� W   �� r   ��    o� �   �� Q   v�    �� $   ��     � �   � �   ��    d� a   z� �   �� �   ��    y�    ��    �� �  �� (   e� "   ��    �� -   �� ?   �� �   8�    � %   +� E   Q� ^   ��    ��    � "   !� 6   D� �  {�   � �   � <   �� E   ��    %�    2� c  I� #  �� �   ��    �� 6  �� )  ��    � 1   :� �  l� �  &�    ��    �� 1   ��    � ]  !� 
   � �  �� �  ;�   7�    9� �   T� H   
� +   S� �   � k  	� Q  u� �   �� �  �� �   �� a  1� ~   �� �     �   �  ]   �     H    1   e '   �    �    � %   � $    l   6 6   � 	   � H   � �  - 3   � )  � �    �   � <   �	 (   �	 1   
    6
 .   P
 .   
 B   �
 2   �
 @  $ 8   e �   �    �
 �   �
 �   � �   $ �  �   t 3   � �   � 	   Q �  [ �   � !   t &  �    �    �    �    � I    	   M 	   W c   a �   � Z  �    �    � *   � �   # j   �   | �   a    �    
    #    B '   [    �    � H   � /   � T    x   l"    �" �   �" �   �# y   C$ �   �$ �   T% |   �%    V& x   c& p   �& �   M' ~   ( v   �(    
)   ) �   0* M   �* t   + �   �+ D   ,    V, @   f, �   �, �   B- S   �- �   O. 	   G/ �  Q/ `   �0 �   31 �   �1    2 @  �2   �4    �5 %   	6    /6    66 w  =6 ;   �7 �   �7 �   ~8   g9 �   l: �   ; �   < �   �< �   �= w   l> i  �> ^   N@ �  �@ �   �B 0  �C 
   �D 
   �D 
   �D �   �D 
   �E 
   �E s   �E |   RF   �F 
   �G O  �G G   EI �   �I H   )J �   rJ �   K 
   �K 7  �K 
   �L 
   �L 
   �L ~  
M �   �O *  *P 3   UR 	   �R    �R �   �R 3   �S 6   �S   �S �   U "   �U    V 0   V I   IV E   �V L   �V (   &W (   OW $  xW    �X   �X G  �Z �   \ t   �\ �   &] K  �] <  <_ �  ya �   ld �   He �   f    �f �  �f    �h �  �h   wj T  �k �   �l o  �m �   o �   �o �   �p L   9q F   �q    �q 7   �q    r    .r    Jr   cr    }s }   �s 3   t    ?t    Lt    St    `t �   �t n   )u x   �u R  v 7   dw    �w 	   �w 	   �w    �w    �w    �w 
   	x    x 
    x    +x 
   4x    ?x A   Hx �   �x    $y    5y 
   Ay    Ly 
   Xy    cy 
   ly    wy    �y    �y 6   �y �   �y    oz :   �z �   �z �   g{ �   1|   } �  ~ �   � ~  ��    � �   � '   �� S   ӂ C   '� �   k� I  :� _   �� v   �    [� �   l� �   � �   ��    4�     ;�    \�    u�    �� $   ��    Ȉ 2   Ј    � %   � &   2� =   Y�    ��    ��    ω    �    �    �    �    !�    6� B   =� 6   �� �   �� �   ]� E   � %   1� |   W� �   Ԍ �   ]� -   � 6   � <   Q� @   �� D   ώ 0   � 
  E� U  P� \  �� &   � -   *� �   X� T   ܓ   1�   I� �   g� E   � *   e� �   �� �   h� �   7� F   �� �    � �   �� $   O� 9   t� "   �� L   ћ �   � �   ��    0�    8�    A� 0   H� �   y� `   >� !   �� *   �� 7   � '   $� ,   L� ?   y� .   �� |   � =   e� W  �� g   �� q   c� �   բ A   ɣ    �    &�    B�    ]�    y�    ��    �� B   Ѥ 6   �   K� l   e� B   Ҩ W   � -   m�    �� �   �� �   9� �   ˪   ��    �� �   Ƭ +   _�    �� �   ��    }� `   �� M   �� G   E� n   �� �   �� b   ��     � /   � �   5� S   ɱ E   � �   c� ]  � T   M� �   �� �   A� o   ͵ �   =� C   ʶ L   � �   [� +   � {   E� /   ��   � :   � f   <�    �� -   º �   � l   � h   M� �   �� �   a� i   �� �   e� �   ,�    � !   � o   � l   ��    �� -   	�    7�    I� 	   Z� 7   d� |   �� ]   � -   w�    ��    ��    �� .   �� �   *� 5   �� �   �� o   ��    � �   %� 
  �� ,   �� g   � $   k� B   �� I   ��    � s   6� �   �� *   U� J   �� !   �� -   �� m   �    �� �   �� B   *� $   m� <   �� H   ��   � 8   �    T�    o� 8   ��    �� S   �� 5   2� �   h� �   ]�    � T    � �   u� .   c� �   �� ^   �� 6   �� u   � D  �� 	   ��    ��    �� -   �� ]   � `   r� d   �� 3   8�    l�    |� !   �� A   �� A   ��    )� Z   0� [   ��    �� E   �� B   <� $   � '   �� y   �� C  F� v   ��    �     � �   ?�   � b   ��    �� �  � �  �� E   �� x   � '   ��   �� 9   �� �   �� $   ��    �� �  �� '   �� m   �� }   *� �   �� �   .� *   �� d   �� T   R� B   �� �   �� V   v� 8   �� X   � W   _� T   �� �   � �   �� B   �� �   �� �  �� �   �� 6   p� z  �� $  "�   G� u  L�   �� 0   ��    
� �   #� G   �� �  /� l   �� g   .� 
   �� ,   ��   �� 	   � �   � �  � Q  � �  � `   � W   � x   K <   � =   	 y   ?	 �   �	    D
 N   T
 Q   �
 0   �
 %   & l   L �   � ?   P    � �   � :   
 �   [    4    J    ` T   g s   � t   0 �   � �   � *   
 (   5 �   ^    X �   _ (   0  4 \   e +   �    �    �    � c   � 3   c �   � 7  G |       �     *   ,     W u   x    � P       W H   ^ Q   �    �     o    	   � -   �    � '   �    � {   � �   v ]   7  {   �  o   ! �   �!    {"    �" �   �" �   �# �   �$    �% �   �% �   A& a   �& �   1' �   �' r   W(    �( }   �(    \)    i) 	   v)    �)    �)    �)    �) '   �)    �)    * ,   * ,   8* ,   e*    �* ;   �* )   �* 6   + %   N+    t+ ^   �+ '   �+    , N   %,     t, ~   �, 5   -    J-    b- #   t- #   �-    �- �   �- n   �. �   �. *  |/    �0    �0 3   �0    1 =   71 	   u1    1 #   �1 �   �1    �2 !   �2    �2 
   �2 	   �2 P   �2 6   53 .  l3    �4    �4    �4 ,   �4 ,   "5    O5 0   d5 ,   �5 6   �5 .   �5 *   (6    S6 ~   Z6 )   �6    7    7 N   -7 X   |7    �7 6   �7 !   '8 �   I8 u   �8 r   _9    �9 '   �9 	   :    :    $: !   ;: �  ]: �   9<    �<    = *   = $   3= 9   X=    �=    �= �   �= L   A> !  �>    �? -   �? �   �? -   �@ '   �@ ,   A /   GA 9   wA -   �A    �A    �A X   B !   tB (   �B N   �B Q  C i   `D i   �D Z   4E    �E �   �E -   cF    �F g   �F    G 4   G !   QG _   sG i   �G 0   =H �   nH    JI    `I -   pI    �I    �I    �I #   �I    J     J s   2J c  �J 6   
L -   AL   oL �   �M $   �N #   �N    �N    �N #   �N    O    %O    >O    ZO    lO    O    �O 
   �O    �O    �O    �O    �O    �O ?   �O �   7P D  /Q �  tR �  :T [  �V 1  (Y �  ZZ    \ =  $\    b] J  �]   �^   �_ �   �a �  �b R   �d �   e W   �e *   Mf J   xf a   �f �   %g    �g   (h �   1i �   �i �  �j    �m �   �m �   gn �   3o �   �o    �p �   �p �  �q �   �s �   vt 	   ju y   tu �   �u   �v �   �w �   6x �   �x *   Zy $   �y    �y Y   �y *   z    Hz �   Uz ]   {    t{ <   �{ �   1| �   �| N   �} �   �} w   �~ v    �   � G   � �   Z� ~   � �   �� �   [�    � H   � Q   8� {   �� P   �    W� 	   ^� W   h� !   �� 6   �    � ?   ,� B   l� W   ��    �    '�    4� /   G� 	   w� �   �� �   � u   �� T   
�    b� '   j� 0   �� 	   È 	   ͈    ׈    �    � 	   ��    �    �    (�    /�    <� 	   C�    M�    T�    p�    ��    ��    ��    �� 	   �� S   ǉ    � !   :� �   \�    �� �   � 
  ��    ��    �� 	   ӌ    ݌    �    �    �� �   �� �   Í r   h�    ێ -   � �   "�    �� �   ԏ �   �� F   S�    �� �   �� �   �� i   }� �   � �   Ԕ )   f� �   ��    7�    L� i   b� �   ̖ 0   �� �   �� �   y�    4� �   5�    Ú    К    ך    �    ��    �    #�    0� �   J� �   � �   Ŝ �   d� V  T� �   �� k   F� �  �� �  �� '  }� �   �� ;  �� �  �� 	   ?� �   I� t  :� �   ��   ��   �� �   �� �  O�    '� L   C� �   �� �  i� X   6� T  ��    � 	   ��    � �   � Q   ҹ �   $� +   �� p   Һ U   C� �   �� :   !� �   \� W   7� 5   �� 6   Ž �   ��    �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ja
Language-Team: ja <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-ライブラリは人気のある（そして違法な）ライブラリです。彼らはLibrary Genesisのコレクションを取り、それを簡単に検索可能にしました。その上、彼らは新しい書籍の寄稿を奨励することで非常に効果的になっています。現在、これらの新しい書籍をLibrary Genesisに戻すことはありません。そして、Library Genesisとは異なり、彼らのコレクションを簡単にミラーリングできるようにはしていません。これは、彼らのビジネスモデルにとって重要です。なぜなら、彼らはコレクションへの一括アクセス（1日に10冊以上）に対して料金を請求しているからです。 違法な書籍コレクションへの一括アクセスに対して料金を請求することについて道徳的な判断はしません。Z-ライブラリが知識へのアクセスを拡大し、より多くの書籍を調達することに成功したことは疑いの余地がありません。私たちは単に私たちの役割を果たしています：このプライベートコレクションの長期保存を確保することです。 - アンナとチーム (<a %(reddit)s>Reddit</a>) 海賊図書館ミラーの最初のリリースで（編集済み：<a %(wikipedia_annas_archive)s>Anna’s Archive</a>に移動）、Z-ライブラリという大規模な違法書籍コレクションのミラーを作成しました。これは元のブログ投稿で書いた内容のリマインダーです： そのコレクションは2021年中頃に遡ります。その間に、Z-ライブラリは驚異的な速度で成長しており、約380万冊の新しい書籍を追加しました。確かに重複もありますが、その大部分は正当に新しい書籍、または以前に提出された書籍の高品質なスキャンのようです。これは主に、Z-ライブラリのボランティアモデレーターの数が増え、重複排除機能を備えた一括アップロードシステムがあるためです。これらの成果を祝福したいと思います。 私たちは、前回のミラーと2022年8月の間にZ-ライブラリに追加されたすべての書籍を取得したことを喜んで発表します。また、最初に見逃した書籍をいくつかスクレイピングしました。全体として、この新しいコレクションは約24TBで、前回のもの（7TB）よりもはるかに大きいです。私たちのミラーは現在合計31TBです。Library Genesisに対して重複排除を行いました。すでにそのコレクションのトレントが利用可能だからです。 新しいコレクションを確認するには、Pirate Library Mirrorにアクセスしてください（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動しました）。ファイルの構造や前回からの変更点についての詳細情報があります。ここからリンクはしません。これは違法な資料をホストしていないブログウェブサイトだからです。 もちろん、シードすることも私たちを助ける素晴らしい方法です。以前のトレントセットをシードしている皆さん、ありがとうございます。私たちはポジティブな反応に感謝し、この異例の方法で知識と文化の保存に関心を持つ多くの人々がいることを嬉しく思います。 3x 新しい本が海賊図書館ミラーに追加されました（+24TB、380万冊の本） TorrentFreak による関連記事もご覧ください：<a %(torrentfreak)s>第1弾</a>、<a %(torrentfreak_2)s>第2弾</a> - Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreakによる関連記事: <a %(torrentfreak)s>最初</a>, <a %(torrentfreak_2)s>2番目</a> 少し前まで、「シャドウライブラリ」は衰退の一途をたどっていました。学術論文の巨大な違法アーカイブ「Sci-Hub」は、訴訟の影響により新たな論文の受け入れを停止しました。また、世界最大の違法電子書籍ライブラリ「Z-Library」では、運営者とされる人物が著作権侵害の罪で逮捕されました。彼らは奇跡的にその逮捕を免れましたが、Z-Library自体はいまだ深刻な危機に直面しています。 いくつかの国はすでにこれを行っています。TorrentFreakは、中国と日本が著作権法にAIに関する例外規定を設けたと<a %(torrentfreak)s>報じています</a>。これが国際条約とどのように相互作用するかは不明ですが、確かに国内企業に対する保護を提供しており、私たちが見てきたことを説明しています。 Anna’s Archiveについては、道徳的信念に根ざした地下活動を続けます。しかし、私たちの最大の願いは、光の中に出て、法的に影響を拡大することです。どうか著作権を改革してください。 Z-Library が閉鎖の危機に直面していたとき、私はすでにその全ライブラリのバックアップを取得しており、それを保管できるプラットフォームを探していました。それが私が「Anna’s Archive」を立ち上げるきっかけとなった動機です。以前のプロジェクトが担っていた使命を受け継ぐものとして、活動を始めました。それ以来、私たちは世界最大のシャドウライブラリへと成長し、書籍、学術論文、雑誌、新聞など多様な形式で、1億4000万点以上の著作物を収蔵・公開しています。 私たちのチームは強い信念のもとに活動しています。これらのファイルを保存・公開することは、道徳的に正しい行為だと確信しています。図書館は世界的に資金不足に苦しんでおり、人類の知的遺産を営利企業の管理下に置くことは、望ましい未来とは言えません。 その後、AIが登場しました。ほぼすべての大手LLM（大規模言語モデル）開発企業が、私たちのデータを学習に使用するために連絡を取ってきました。しかし、このサイトの活動が違法であることに気づくと、アメリカを拠点とする企業の多く（すべてではない）は検討をやめました。一方、中国の企業は、その合法性にさほど関心を示すことなく、このサイトのコレクションを熱心に活用しています。これは、中国がほぼすべての主要な国際著作権条約の署名国であることを考えると、注目に値します。 私たちは、約30社に高速アクセスを提供してきました。その多くはLLM（大規模言語モデル）企業であり、一部は当アーカイブのコレクションを再販するデータブローカーです。大半は中国の企業ですが、アメリカ、ヨーロッパ、ロシア、韓国、日本の企業とも連携してきました。DeepSeekは、<a %(arxiv)s>以前のバージョン</a>が私たちのコレクションの一部を学習に使っていたことを認めていますが、最新モデルについては口を閉ざしたままです（おそらくこちらも私たちのデータで訓練されたと思われます）。 もし西側諸国がLLM開発、そして最終的にはAGI（汎用人工知能）の競争において先行し続けたいのであれば、著作権に対する立場を見直す必要があります——それも早急にです。私たちの「道徳的な主張」に賛同するかどうかにかかわらず、いまやこれは経済の問題であり、さらには国家安全保障の問題にもなりつつあります。すべての勢力圏が「人工的なスーパー科学者」「スーパー・ハッカー」「スーパー・軍事機構」の構築を進めています。情報の自由は、もはや国家の生存に関わる問題——安全保障上の死活問題になりつつあるのです。 私たちのチームは世界中から集まっており、特定のアライメントはありません。しかし、厳格な著作権法を持つ国々には、この“存亡に関わる脅威”をきっかけに法制度を見直すことを強く勧めたいと考えています。では、どうすればいいのでしょうか？ 私たちがまず提案したいのは、著作権保護期間の見直しです。米国では著者の死後70年という極端に長い期間が設定されていますが、これは現代の知識社会においては非合理的です。特許制度のように20年という現実的な基準を設けることで、創作者に正当な利益を保障しつつ、社会全体の知的アクセスも促進されます。長編映画などのプロジェクトを含めても、20年という期間は十分に意味ある対価回収期間です。 少なくとも、政策立案者は、大量のテキストを保存・普及する行為に対する例外規定を設けるべきです。収益の損失が「個々の消費者からの購入減少」に起因するのであれば、個人レベルでの配布は引き続き禁止しても構いません。代わりに、LLMを訓練する企業、図書館、その他のアーカイブなど、広大なリポジトリを管理できる者は、これらの例外の対象となります。 著作権制度の改革は、国家の安全保障のために必要です 要約：DeepSeekを含む中国の大規模言語モデル（LLM）は、このサイトが保有する世界最大規模の（違法な）書籍・論文アーカイブを学習データとして使用しています。西側諸国は、国家安全保障の観点から著作権法を抜本的に見直す必要があります。 詳しくは、<a %(all_isbns)s>元のブログ記事</a>をご覧ください。 この可視化をさらに改善するためのチャレンジを実施しました。1位には6,000ドル、2位には3,000ドル、3位には1,000ドルの賞金を用意していました。圧倒的な反響と素晴らしい提出物により、賞金プールを少し増やし、3位を4人に500ドルずつ授与することに決定しました。受賞者は以下の通りですが、すべての提出物を<a %(annas_archive)s>こちら</a>で確認するか、<a %(a_2025_01_isbn_visualization_files)s>結合トレント</a>をダウンロードしてください。 1位 6,000ドル: phiresky この<a %(phiresky_github)s>提出物</a>（<a %(annas_archive_note_2951)s>Gitlabコメント</a>）は、私たちが望んでいたすべてを満たし、それ以上のものでした！特に印象的だったのは、驚くほど柔軟な可視化オプション（カスタムシェーダーまで対応）と、それに加えて豊富なプリセットが用意されている点です。さらに、全体の動作が非常に高速かつスムーズであること、バックエンドなしでも動作するシンプルな実装、そしてミニマップの工夫や、<a %(phiresky_github)s>ブログ記事</a>での丁寧な解説にも感銘を受けました。本当に素晴らしい作品であり、まさに文句なしの受賞者です！ - Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 感謝の気持ちでいっぱいです。 注目アイデア 希少性のための高層ビル たくさんのスライダーでデータセットを比較、まるでDJのように。 本の数を示すスケールバー。 美しいラベル。 クールなデフォルトのカラースキームとヒートマップ。 ユニークな地図ビューとフィルター 注釈、そしてライブ統計 ライブ統計 特に印象に残った、他のアイデアや実装をご紹介します： まだまだ紹介したいところですが、今回はこのあたりで一区切りとします。すべての応募作品は<a %(annas_archive)s>こちら</a>からご覧いただけますし、<a %(a_2025_01_isbn_visualization_files)s>こちらのトレント</a>からまとめてダウンロードすることもできます。本当に多くの応募があり、どの作品にも UI や実装面での独自の視点が光っていました。 少なくとも、1位の応募作品は私たちのメインサイトに統合する予定です。また、他のいくつかの作品についても採用を検討しています。さらに現在、最も希少な書籍を特定し、確認し、そしてアーカイブしていくプロセスをどのように体系化するかについても、構想を始めています。この点については、今後さらにお知らせしていきます。 参加してくれた皆さんに感謝します。多くの人が関心を持ってくれていることは素晴らしいです。 データセットを簡単に切り替えて素早く比較。 すべてのISBN CADALデータベースのSSNO（識別子） CERLALCデータ漏洩 DuXiu SSIDs EBSCOhostのeBookインデックス Google ブックス Goodreads Internet Archive ISBNdb ISBN出版社のグローバル登録 Libby Anna’s Archive にあるファイル Nexus/STC OCLC/Worldcat OpenLibrary ロシア国立図書館 トランター帝国図書館 2位 3,000ドル: hypha 「完全な正方形や長方形は数学的には美しく感じられますが、マッピングにおいては必ずしも局所性（locality）に優れているわけではありません。私は、ヒルベルト曲線や古典的なモートン曲線に見られる非対称性は、欠点ではなく“特徴”だと考えています。たとえば、イタリアが“ブーツ型”であることで地図上ですぐに認識できるように、こうした曲線の独特な『クセ』も認知的なランドマークとして機能するかもしれません。この個性が空間記憶を高め、ユーザーが自分の位置を把握しやすくなったり、特定の領域を見つけたり、パターンに気づいたりする助けになる可能性があります。」 もう一つの素晴らしい<a %(annas_archive_note_2913)s>提出物</a>。1位ほど柔軟ではありませんが、実際には1位よりもマクロレベルのビジュアライゼーションを好みました（スペースフィリングカーブ、境界、ラベリング、ハイライト、パン、ズーム）。Joe Davisによる<a %(annas_archive_note_2971)s>コメント</a>が私たちの心に響きました： さらに、可視化やレンダリングの選択肢も豊富で、驚くほどスムーズかつ直感的なUIも備えています。まさに堂々の第2位にふさわしい作品です！ - アンナとチーム (<a %(reddit)s>Reddit</a>) 数か月前、私たちは ISBN 空間を可視化するための最良のビジュアライゼーションを募集する <a %(all_isbns)s>1万ドルの懸賞</a> を発表しました。特に強調したのは、「どの ISBN ファイルがすでにアーカイブされていて、どれが未アーカイブなのか」を明確に示すことでした。その後、ISBN がどれだけ多くの図書館に所蔵されているか（希少性の指標）を示すデータセットも提供しました。 皆さまからのご応募に、嬉しい驚きを感じています。多くの創意工夫に満ちた作品に触れ、大変感動しました。ご参加いただいたすべての方々に心より御礼申し上げます。皆さまの熱意と情熱に感謝いたします！ 最終的に、次の質問に答えたいと考えました：<strong>世界にはどの本が存在し、すでにどれだけアーカイブされているのか、これからどの本に注力すべきなのか？</strong> こうした問いに多くの方が関心を寄せてくださっていることが、とても嬉しく思います。 私たち自身でシンプルな可視化から始めました。たった300KB未満のデータで、人類史上最大の完全にオープンな「書籍リスト」を簡潔に表現した図です： 3位 500ドル #1: maxlion この<a %(annas_archive_note_2940)s>提出物</a>では、特に比較ビューと出版社ビューの異なる種類のビューが気に入りました。 3位 500ドル #2: abetusk 最も洗練されたUIではありませんが、この<a %(annas_archive_note_2917)s>提出物</a>は多くの要件を満たしています。特に比較機能が気に入りました。 3位 500ドル #3: conundrumer0 1位と同様に、この<a %(annas_archive_note_2975)s>提出物</a>はその柔軟性で私たちを驚かせました。最終的に、これはパワーユーザーにとって最大限の柔軟性を提供しつつ、一般ユーザーにとってはシンプルさを保つ優れたビジュアライゼーションツールを作る要因です。 3位 500ドル #4: charelf 賞金を獲得した最後の<a %(annas_archive_note_2947)s>提出物</a>は非常に基本的ですが、私たちが本当に気に入ったユニークな機能があります。特に、特定のISBNをカバーするDatasetsの数を人気/信頼性の指標として示す方法が気に入りました。また、比較のために不透明度スライダーを使用するシンプルさと効果的な方法も非常に気に入りました。 10,000ドル相当の「ISBN可視化」懸賞金の受賞者 要約：1万ドルのISBN可視化コンテストに、驚くべき応募作品が多数寄せられました。 背景 Anna’s Archiveは「人類の知識すべてをバックアップする」という使命を掲げていますが、世の中にまだどんな本が存在しているのかが分からなければ、その達成は不可能です。私たちには「やるべきことリスト（TODOリスト）」が必要です。そのための手がかりの一つが ISBN番号です。1970年代以降、多くの国で出版された本にはこのISBNが割り振られており、これを手がかりに、出版された書籍全体の地図を描くことができるのです。 すべてのISBN割り当てを知っている中央の権威は存在しません。代わりに、これは分散システムであり、国が番号の範囲を取得し、それを主要な出版社に割り当て、さらに小さな出版社に範囲を細分化することがあります。最終的に個々の番号が本に割り当てられます。 私たちは<a %(blog)s>2年前</a>にISBNdbのスクレイピングでISBNのマッピングを開始しました。それ以来、<a %(blog_2)s>Worldcat</a>、Google Books、Goodreads、Libbyなど、多くのmetadataソースをスクレイピングしてきました。完全なリストはアンナのアーカイブの「Datasets」と「Torrents」ページで見つけることができます。現在、私たちは世界で最大の完全にオープンで簡単にダウンロード可能な書籍metadata（したがってISBN）のコレクションを持っています。 保存活動の重要性、そして今がその取り組みにおいて決定的な時期である理由については、<a %(blog)s>過去の記事</a>で詳しく述べています。今こそ、希少で注目されにくく、消失のリスクが高い書籍を特定し、保存していく必要があります。そのためには、世界中の書籍に関する質の高いメタデータの整備が不可欠です。 $10,000の報奨金 使いやすさや見た目の美しさは、選考において重要な評価ポイントとなります。 拡大表示時には、ISBNごとに対応する書籍タイトルや著者情報などの詳細メタデータを表示するようにしてください。 より優れたスペース充填曲線を使用してください。たとえば、1行目では 0〜4 の順に進み、2行目では 5〜9 を**逆順（ジグザグ）**で進むようなパターンを、再帰的に適用する方法などが考えられます。 異なるまたはカスタマイズ可能なカラースキーム。 データセットの比較用に専用ビューを用意すること（差分表示など）。 タイトルなどのメタデータに大きな不一致がある場合など、問題をデバッグするための方法を提供してください。 ISBNや範囲に関するコメントで画像に注釈を付ける。 レア本・消失リスク本を検出するヒューリスティック手法があればご提案ください。 思いつく限りのクリエイティブなアイデアを、ぜひ自由に提案してください！ コード これらの画像を生成するコードや他の例は、<a %(annas_archive)s>このディレクトリ</a>にあります。 必要なISBN情報をすべて約75MB（圧縮時）に収めたコンパクトなデータ形式を考案しました。そのデータ形式の説明および生成用コードは、<a %(annas_archive_l1244_1319)s>こちら</a>にあります。この賞金チャレンジにおいてこの形式を使う義務はありませんが、最も手軽に始められる方法かもしれません。メタデータの加工・変換は自由に行っていただいて構いません（ただし、コードはすべてオープンソースである必要があります）。 創造力あふれる挑戦をお待ちしています。健闘を祈ります！ このリポジトリをフォークし、ブログ記事のHTMLを編集してください。なお、当方の Flask バックエンド以外のバックエンドは許可していません。 上記の画像をスムーズにズーム可能にし、個々のISBNまでズームできるようにしてください。ISBNをクリックすると、Anna’s Archive 上のメタデータページまたは検索結果へ遷移するようにしてください。 すべてのデータセットを自由に切り替えられるようにする必要があります。 国の範囲と出版社の範囲はホバー時にハイライトされるべきです。例えば、<a %(github_xlcnd_isbnlib)s>isbnlibのdata4info.py</a>を国情報に使用し、出版社には私たちの「isbngrp」スクレイプを使用できます（<a %(annas_archive)s>dataset</a>、<a %(annas_archive_2)s>torrent</a>）。 デスクトップとモバイルの両方でうまく動作する必要があります。 ここには多くの探求の余地があるため、上記の視覚化を改善するための報奨金を発表します。ほとんどの報奨金とは異なり、これは時間制限があります。2025-01-31（23:59 UTC）までにオープンソースコードを<a %(annas_archive)s>提出</a>する必要があります。 最優秀作品には$6,000、2位には$3,000、3位には$1,000が授与されます。すべての報奨金はMonero（XMR）で支払われます。 以下は最低限の基準です。万が一、どの応募もこれらの条件を満たさない場合であっても、一部の応募に対しては当方の判断により報奨金を授与する可能性があります。 追加ポイント対象（以下は一例です — 創造力を存分に発揮してください）： 最低条件から大きく外れて、まったく異なるビジュアライゼーションを作っていただいても構いません。もし本当に素晴らしいものであれば、当方の裁量で報奨金の対象とさせていただきます。 <a %(annas_archive)s>この問題</a>にコメントを投稿し、フォークしたリポジトリ、マージリクエスト、または差分へのリンクを添えて提出してください。 - Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) この画像は 1000×800 ピクセルで構成されています。各ピクセルは 2,500 件のISBNを表しています。ISBNのファイルがある場合、そのピクセルをより緑にします。ISBNが発行されていることがわかっているが、対応するファイルがない場合は、より赤くします。 この画像はわずか 300KB未満でありながら、人類史上最大規模の完全にオープンな「書籍リスト」（※圧縮しても数百GB規模）を簡潔に表現しています。 また、書籍のバックアップ作業がまだまだ道半ばであることも示しています。現時点でカバーできているのは、全体のわずか16%%にすぎません。 すべてのISBNの可視化 — 締切：2025年1月31日、賞金総額1万ドル この図は、人類史上最大規模の完全オープンな「書籍一覧」を表しています。 可視化 概要画像に加えて、取得済みの個別のデータセットも閲覧できます。ドロップダウンメニューやボタンを使って切り替えてください。 これらの画像には多くの興味深いパターンがあります。なぜ異なるスケールで線やブロックの規則性があるのでしょうか？空白の領域は何でしょうか？なぜ特定のDatasetsはこんなに集まっているのでしょうか？これらの質問は読者への課題として残しておきます。 - アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 結論 この標準を使用することで、リリースをより段階的に行い、新しいデータソースをより簡単に追加できます。すでにいくつかのエキサイティングなリリースが進行中です！ 他のシャドウライブラリがこののコレクションをミラーリングしやすくなることを願っています。結局のところ、私たちの目標は人類の知識と文化を永遠に保存することなので、冗長性が多いほど良いのです。 例 最近のZ-ライブラリのリリースを例に見てみましょう。これは2つのコレクションで構成されています：「<span style="background: #fffaa3">zlib3_records</span>」と「<span style="background: #ffd6fe">zlib3_files</span>」。これにより、実際の書籍ファイルからメタデータレコードを個別にスクレイピングしてリリースすることができます。このようにして、メタデータファイルを含む2つのトレントをリリースしました。 また、バイナリデータフォルダを含むトレントも多数リリースしましたが、これは「<span style="background: #ffd6fe">zlib3_files</span>」コレクションのみで、合計62個です。 <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>を実行することで、中身を確認できます。 この場合、Z-ライブラリによって報告された書籍のメタデータです。トップレベルには「aacid」と「metadata」しかなく、「data_folder」はありません。対応するバイナリデータがないためです。AACIDには「22430000」が主なIDとして含まれており、「zlibrary_id」から取得されたことがわかります。このコレクションの他のAACも同じ構造を持つと予想されます。 次に、<code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>を実行してみましょう。 これははるかに小さなAACメタデータですが、このAACの大部分はバイナリファイルの別の場所にあります！結局のところ、今回は「data_folder」があるので、対応するバイナリデータが<code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>にあると予想できます。「metadata」には「zlibrary_id」が含まれているので、「zlib_records」コレクション内の対応するAACと簡単に関連付けることができます。AACIDを通じて関連付けることも可能です — 標準ではそれを規定していません。 「metadata」フィールド自体がJSONである必要はないことにも注意してください。XMLや他のデータ形式を含む文字列である可能性もあります。大量のデータであれば、関連するバイナリブロブにメタデータ情報を保存することもできます。 異種混在のファイルとメタデータを、可能な限り元の形式のままで扱うこと。 バイナリデータはNginxのようなウェブサーバーによって直接提供可能。 ソースライブラリ内の異種識別子、または識別子の欠如。 metadataとファイルデータの別々のリリース、またはmetadataのみのリリース（例：私たちのISBNdbリリース）。 基本的にはトレントを用いて配布しますが、他の配布手段（例：IPFS）も視野に入れています。 不変の記録、トレントが永遠に存在することを想定する必要があるため。 増分リリース／追記可能なリリース。 機械による読み書きが容易かつ迅速に行える形式であり、とくに私たちのスタック（Python、MySQL、ElasticSearch、Transmission、Debian、ext4）に最適化されています。 人間による確認もある程度は容易ですが、あくまで機械による可読性が優先されます。 標準的なレンタルシードボックスでコレクションを簡単にシードできること。 デザインの目的 ファイルがディスク上で手動で閲覧しやすいことや、前処理なしで検索可能であることは重視していません。 既存の図書館ソフトウェアとの直接的な互換性は重視していません。 トレントを使ってコレクションをシードするのは誰にでも簡単にできるようにすべきですが、ファイルを実際に活用するには、相応の技術知識と覚悟が必要になると想定しています。 主なユースケースは、既存のさまざまなコレクションから、ファイルとそれに関連するメタデータを配布することです。その際に最も重視している点は、以下のとおりです： 非目標： Anna’s Archive はオープンソースであるため、自分たちのフォーマットを実際に自ら使用（ドッグフーディング）したいと考えています。検索インデックスを更新する際には、誰でも利用できる公開パスのみを使用することで、ライブラリをフォークした人がすぐに動かせるようにしています。 <strong>AAC</strong>（Anna's Archive Container）は、<strong>メタデータ</strong>および（任意で）<strong>バイナリデータ</strong>からなる単一のアイテムで、どちらも不変です。AAC は<strong>AACID</strong>と呼ばれるグローバルに一意な識別子を持ちます。 <strong>AACID（アークID）</strong>AACIDのフォーマットは次のようになっています：<code style="color: #0093ff">aacid__{collection}{ISO 8601 タイムスタンプ}{コレクション固有のID}__{shortuuid}</code>たとえば、実際にリリースされたAACIDの一例は、<code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>です。 <strong>AACIDレンジ</strong>：AACIDには単調増加のタイムスタンプが含まれているため、それを利用して特定のコレクション内の範囲を示すことができます。フォーマットは以下の通りです：<code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>。これは ISO 8601 の表記法に準拠しています。範囲は連続している必要があり、重複しても構いませんが、その場合、重複するレコードはすでに同一コレクション内でリリースされたものと完全に一致していなければなりません（AACは不変であるため）。欠落レコードは許可されていません。 <code>{collection}</code>：コレクション名を表します。ASCII文字（英字）、数字、アンダースコア（_）を含むことができますが、ダブルアンダースコア（__）は使用できません。 <code>{collection-specific ID}</code>：必要に応じて使われる、コレクションごとのID（例：Z-LibraryのIDなど）です。使わなくても構いませんし、短くしてもOKです。ただし、AACID全体が150文字を超えそうな場合は、省略や短縮が必須です。 <code>{ISO 8601 timestamp}</code>：ISO 8601形式の短縮版で、常にUTC（協定世界時）で表記されます。例：<code>20220723T194746Z</code>。この数字は、リリースのたびに単調に増加する必要がありますが、正確な意味づけはコレクションごとに異なる場合があります。スクレイピング実行時やID生成時のタイムスタンプを使用することを推奨します。 <code>{shortuuid}</code>: UUIDですが、ASCIIに圧縮されています。例としてbase57を使用します。現在、<a %(github_skorokithakis_shortuuid)s>shortuuid</a> Pythonライブラリを使用しています。 <strong>バイナリデータのフォルダ.</strong> 特定のコレクションのAACの範囲のバイナリデータを含むフォルダ。これらには次の特性があります： ディレクトリには、指定された範囲内のすべてのAACのデータファイルを含める必要があります。各データファイルは、そのAACIDをファイル名として持たなければなりません（拡張子なし）。 ディレクトリ名は、<code style="color: green">annas_archive_data__</code>で始まり、サフィックスはありません。例えば、私たちの実際のリリースの一つには、次のようなディレクトリがあります：<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>。 各フォ各フォルダのサイズは大きくなりすぎないよう、100GB〜1TB程度を目安にするのがおすすめです。この目安は将来変わることがあります。 <strong>コレクション：</strong> 各 AAC はコレクションに属しており、コレクションとは意味的に一貫性のある AAC のリストです。メタデータの形式に大きな変更を加える場合は、新しいコレクションを作成する必要があります。 標準 <strong>メタデータファイル.</strong> メタデータファイルは、特定のコレクションのAACの範囲のメタデータを含みます。これらには次の特性があります： <code>data_folder</code> は任意項目です。この項目は、対応するバイナリデータを格納するフォルダ名を指定します。そのフォルダ内にある対応データのファイル名は、各レコードの AACID と一致します。 各JSONオブジェクトは、トップレベルに次のフィールドを含む必要があります：<strong>aacid</strong>、<strong>metadata</strong>、<strong>data_folder</strong>（オプション）。他のフィールドは許可されていません。 ファイル名は、<code style="color: red">annas_archive_meta__</code>で始まり、<code>.jsonl.zstd</code>で終わるAACID範囲でなければなりません。例えば、私たちのリリースの一つは次のように呼ばれています：<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>。 ファイル拡張子が示すように、ファイルタイプは<a %(jsonlines)s>JSON Lines</a>で<a %(zstd)s>Zstandard</a>で圧縮されています。 <code>metadata</code>は、コレクションのセマンティクスに従った任意のメタデータです。コレクション内で意味的に一貫している必要があります。 <code style="color: red">annas_archive_meta__</code>プレフィックスは、あなたの機関の名前に適応させることができます。例：<code style="color: red">my_institute_meta__</code>。 <strong>「レコード」と「ファイル」のコレクション.</strong> 慣例として、「レコード」と「ファイル」を異なるコレクションとしてリリースすることが便利なことが多く、例えばスクレイピングの速度に基づいて異なるスケジュールでリリースできます。「レコード」はメタデータのみのコレクションで、本のタイトル、著者、ISBNなどの情報を含み、「ファイル」は実際のファイル（pdf、epub）を含むコレクションです。 最終的に、比較的シンプルな標準仕様に落ち着きました。これはまだ進行中のものであり、厳格な規格ではなく、柔軟性のあるものです。 <strong>トレント。</strong> メタデータファイルとバイナリデータフォルダはトレントでまとめることができ、1つのメタデータファイルにつき1つのトレント、または1つのバイナリデータフォルダにつき1つのトレントとします。トレントは元のファイル/ディレクトリ名に<code>.torrent</code>のサフィックスを付けたファイル名でなければなりません。 <a %(wikipedia_annas_archive)s>Anna's Archive</a>は、世界最大のシャドウライブラリとなり、この規模で完全にオープンソースかつオープンデータで運営されている唯一のライブラリです。以下は、このサイトの「データセット」ページに掲載されている表（若干修正済み）： これを達成するために、私たちは3つの方法を取りました： 既存のオープンデータシャドウライブラリ（Sci-HubやLibrary Genesisなど）をミラーリングする。 Libgen のコミックコレクションのように、オープン化を望んでいながらも人的・時間的リソースが不足しているシャドウライブラリの支援を行います。 Z-Library のように一括共有を望まないライブラリから、データをスクレイピングしています。 （2）および（3）について、私たちは現在、数百TB規模のトレントコレクションを自ら管理しています。これまで、これらのコレクションには**個別対応（ワンオフ）**で取り組んできました。つまり、コレクションごとに専用のインフラやデータ構成を用意してきたということです。その結果、各リリースに大きな作業負担がかかるようになり、小規模な追加リリースが困難になっています。 そのため、リリースを標準化することに決めました。これは技術的なブログ記事であり、私たちの標準である<strong>Anna’s Archive Containers</strong>を紹介しています。 Anna’s Archive Containers（AAC）：世界最大のシャドウライブラリによる配布形式の標準化 Anna’s Archiveは世界最大のシャドウライブラリとなり、リリースの標準化が必要になりました。 300GB以上のブックカバーをリリース 最後に、小さなリリースを発表できることを嬉しく思います。Libgen.rsフォークを運営している方々と協力して、すべての書籍カバーをトレントとIPFSを通じて共有しています。これにより、カバーの表示負荷がより多くのマシンに分散され、より良く保存されます。多くの場合（すべてではありませんが）、書籍カバーはファイル自体に含まれているため、これは「派生データ」のようなものです。しかし、IPFSにあることは、アンナのアーカイブやさまざまなLibrary Genesisフォークの日常運用に非常に役立ちます。 通常通り、このリリースはパイレートライブラリミラー（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）で見つけることができます。ここではリンクしませんが、簡単に見つけることができます。 Z-ライブラリの代替手段が整った今、ペースを少し緩めることができることを願っています。この作業負荷は特に持続可能ではありません。プログラミング、サーバー運用、または保存作業に興味がある場合は、ぜひご連絡ください。まだ多くの<a %(annas_archive)s>作業が残っています</a>。ご関心とご支援に感謝します。 ElasticSearchへの切り替え 一部のクエリは非常に時間がかかり、すべてのオープン接続を占有するほどでした。 デフォルトではMySQLには最小単語長があり、インデックスが非常に大きくなる可能性があります。「ベン・ハー」を検索できないと報告する人もいました。 検索はメモリに完全にロードされたときにのみやや速く、これを実行するためにより高価なマシンを取得し、起動時にインデックスをプリロードするためのいくつかのコマンドが必要でした。 新しい機能を構築するために簡単に拡張することはできませんでした。例えば、非空白言語の<a %(wikipedia_cjk_characters)s>トークン化の改善</a>、フィルタリング/ファセット化、ソート、「もしかして」提案、オートコンプリートなどです。 私たちの<a %(annas_archive)s>チケット</a>の一つは、検索システムに関する問題の寄せ集めでした。すべてのデータをMySQLに持っていたので、MySQLの全文検索を使用しました。しかし、それには限界がありました： 多くの専門家と話した後、ElasticSearchに決定しました。完璧ではありません（デフォルトの「もしかして」提案とオートコンプリート機能はひどいです）が、全体的にはMySQLよりも検索においてはるかに良いです。ミッションクリティカルなデータに使用することにはまだ<a %(youtube)s>あまり乗り気ではありません</a>が（多くの<a %(elastic_co)s>進展</a>がありましたが）、全体的にはこの切り替えに非常に満足しています。 現在のところ、より高速な検索、より良い言語サポート、より良い関連性のあるソート、異なるソートオプション、言語/書籍タイプ/ファイルタイプでのフィルタリングを実装しました。どのように機能するか興味がある場合は、<a %(annas_archive_l140)s>こちらを</a> <a %(annas_archive_l1115)s>ご覧</a> <a %(annas_archive_l1635)s>ください</a>。かなりアクセスしやすいですが、もう少しコメントが必要かもしれません… アンナのアーカイブは完全にオープンソースです 情報は自由であるべきだと私たちは信じており、私たち自身のコードも例外ではありません。私たちはすべてのコードをプライベートでホストしているGitlabインスタンスで公開しました：<a %(annas_archive)s>アンナのソフトウェア</a>。また、作業を整理するためにイシュートラッカーを使用しています。開発に参加したい場合は、ここから始めるのが良いでしょう。 現在取り組んでいる内容の一部をご紹介すると、最近はクライアント側のパフォーマンス改善に注力しています。まだページネーション（ページ分割）を実装していないため、検索結果ページには 100〜200 件ほどの結果が一度に表示されることがありました。検索結果を早い段階で打ち切りたくはなかったのですが、これにより一部の端末では動作が重くなってしまうという問題がありました。そこで、私たちはちょっとした工夫を施しました。検索結果の大部分をHTMLコメント(<code><!-- --></code>)でラップし、「結果が表示される必要がある」と判断されたタイミングで、コメントを外して表示するようなJavaScriptを組み込みました。 DOM「仮想化」は23行で実装され、派手なライブラリは不要です！限られた時間で解決すべき実際の問題があるときに、こうした迅速で実用的なコードが生まれます。現在、遅いデバイスでも検索がうまく機能するとの報告があります！ もう一つの大きな取り組みは、データベースの構築を自動化することでした。ローンチ時には、異なるソースを無計画にまとめていました。今ではそれらを更新し続けたいので、2つのLibrary Genesisフォークから新しいmetadataをダウンロードし、それらを統合するスクリプトをいくつか書きました。目標は、私たちのアーカイブに役立つだけでなく、シャドウライブラリmetadataを扱いたい人々にとっても簡単にすることです。目標は、すべての種類の興味深いmetadataが利用可能なJupyterノートブックを作成し、<a %(blog)s>ISBNの何パーセントが永遠に保存されるか</a>を調べるような研究をさらに行うことです。 最後に、寄付システムを刷新しました。クレジットカードを使用して、暗号通貨について何も知らなくても、直接私たちの暗号ウォレットにお金を入金できるようになりました。これが実際にどれだけうまく機能するかを引き続き監視しますが、これは大きな進展です。 Z-Libraryが閉鎖され、その（疑われる）創設者が逮捕されたことで、私たちはアンナのアーカイブで良い代替手段を提供するために昼夜を問わず取り組んできました（ここではリンクしませんが、Googleで検索できます）。最近達成したことのいくつかを以下に示します。 アンナのアップデート: 完全オープンソースのアーカイブ、ElasticSearch、300GB以上のブックカバー 私たちはアンナのアーカイブで良い代替手段を提供するために昼夜を問わず取り組んできました。最近達成したことのいくつかを以下に示します。 分析 意味的な重複（同じ本の異なるスキャン）は理論的にはフィルタリング可能ですが、難しいです。コミックを手動で見ていると、誤検出が多すぎました。 MD5による純粋な重複もいくつかあり、これは比較的無駄ですが、それをフィルタリングしても約1%% inの節約にしかなりません。この規模ではそれでも約1TBですが、この規模では1TBはあまり重要ではありません。このプロセスでデータを誤って破壊するリスクを冒したくありません。 私たちは、コミックを原作とした映画のような、書籍以外のデータをたくさん見つけました。それらは他の手段で広く利用可能であるため、無駄に思えます。しかし、コンピュータでリリースされた<em>インタラクティブなコミックブック</em>があり、それを誰かが映画として記録して保存したため、映画ファイルを単にフィルタリングすることはできないと気付きました。 最終的に、コレクションから削除できるものは数パーセントしか節約できませんでした。そして、私たちはデータホーダーであり、これをミラーリングする人々もデータホーダーであることを思い出しました。「削除って何のこと？！」 :) 95TBがストレージクラスターに投入されると、そこに何があるのかを理解しようとします…重複を削除するなどしてサイズを少しでも減らせるかどうか分析しました。以下は私たちの発見の一部です： したがって、私たちはあなたに完全で未修正のコレクションを提供します。データは大量ですが、それでも多くの人がシードしてくれることを願っています。 共同作業 このコレクションは、その規模から以前より私たちの「やりたいことリスト」の上位にありました。Z-Libraryのバックアップに成功したあと、次のターゲットとしてこのコレクションに狙いを定めました。当初は直接スクレイピングを試みましたが、これはなかなかの挑戦でした。というのも、相手側のサーバー状態があまり良くなかったためです。この方法で約15TBのデータを取得することができましたが、非常に時間のかかる作業でした。 幸運にも、ライブラリの運営者と連絡を取ることができ、すべてのデータを直接送ってもらうことに同意してもらいました。これにより、はるかに速くなりました。それでも、すべてのデータを転送して処理するのに半年以上かかり、ディスクの破損でほとんどすべてを失うところでした。そうなれば、最初からやり直しになるところでした。 この経験から、データをできるだけ早く公開し、広くミラーリングすることが重要だと考えるようになりました。私たちは、このコレクションを永遠に失うまで、あと一つか二つの不運なタイミングの出来事からしか離れていません！ コレクション 迅速に動くということは、コレクションが少し整理されていないことを意味します…見てみましょう。ファイルシステムがあると想像してください（実際にはトレントに分割していますが）。 最初のディレクトリ、<code>/repository</code>は、これのより構造化された部分です。このディレクトリには、いわゆる「千ディレクトリ」が含まれています。各ディレクトリには千のファイルがあり、データベースで順次番号が付けられています。ディレクトリ<code>0</code>には、comic_id 0–999のファイルが含まれています。 これは、Library Genesisがフィクションとノンフィクションのコレクションに使用しているのと同じスキームです。アイデアは、各「千ディレクトリ」がいっぱいになるとすぐに自動的にトレントに変換されるというものです。 しかし、Libgen.liの運営者はこのコレクションのトレントを作成しなかったため、千ディレクトリはおそらく不便になり、「未整理ディレクトリ」に道を譲りました。これらは<code>/comics0</code>から<code>/comics4</code>までです。これらはすべてユニークなディレクトリ構造を持っており、ファイルを収集するためには理にかなっていたかもしれませんが、今ではあまり意味を成しません。幸いなことに、metadataはこれらすべてのファイルを直接参照しているため、ディスク上のストレージの組織は実際には問題ではありません！ metadataはMySQLデータベースの形式で利用可能です。これはLibgen.liのウェブサイトから直接ダウンロードできますが、私たちのMD5ハッシュを含む独自のテーブルと一緒にトレントでも提供します。 <q>バーバラ・ゴードン博士は、図書館の平凡な世界に没頭しようとします…</q> Libgenフォーク まず、背景を少し説明します。Library Genesisはその壮大な書籍コレクションで知られているかもしれません。Library Genesisのボランティアが他にもプロジェクトを立ち上げていることを知っている人は少ないです。例えば、雑誌や標準文書の大規模なコレクション、Sci-Hubの完全なバックアップ（Sci-Hubの創設者アレクサンドラ・エルバキアンとの協力による）、そして実際に膨大なコミックのコレクションがあります。 ある時点で、Library Genesisのミラーの異なる運営者たちがそれぞれの道を歩み始め、現在のように複数の異なる「フォーク」が存在する状況が生まれましたが、すべてがLibrary Genesisの名前を保持しています。Libgen.liフォークは、このコミックコレクションと、かなりの雑誌コレクションを独自に持っています（これも私たちが取り組んでいるところです）。 募金活動 このデータをいくつかの大きなチャンクでリリースしています。最初のトレントは<code>/comics0</code>で、12TBの巨大な.tarファイルにまとめました。それは、無数の小さなファイルよりもハードドライブやトレントソフトウェアにとって良いです。 このリリースの一環として、募金活動を行っています。このコレクションの運営費と契約費用をカバーし、継続的および将来のプロジェクトを可能にするために、20,000ドルを集めることを目指しています。いくつかの<em>大規模な</em>プロジェクトが進行中です。 <em>私の寄付で誰を支援しているのですか？</em> 簡単に言えば、私たちは人類のすべての知識と文化をバックアップし、それを簡単にアクセスできるようにしています。私たちのコードとデータはすべてオープンソースであり、完全にボランティアで運営されているプロジェクトです。これまでに125TBの書籍を保存しました（LibgenとScihubの既存のトレントに加えて）。最終的には、世界中のすべての書籍を見つけ、スキャンし、バックアップすることを可能にし、奨励するフライホイールを構築しています。私たちのマスタープランについては、今後の投稿で書く予定です。 :) 12か月の「Amazing Archivist」メンバーシップ（780ドル）に寄付すると、<strong>「トレントを採用する」</strong>ことができ、トレントのファイル名にあなたのユーザー名やメッセージを入れます！ <a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>にアクセスして「寄付」ボタンをクリックすることで寄付できます。また、ソフトウェアエンジニア、セキュリティ研究者、匿名の商人専門家、翻訳者など、より多くのボランティアを探しています。ホスティングサービスを提供することで私たちをサポートすることもできます。そしてもちろん、私たちのトレントをシードしてください！ すでに温かくご支援いただいている皆さま、本当にありがとうございます。皆さまのご協力が大きな力になっています。 現在までにリリースされたトレントはこちらです（残りは現在処理中です）： すべてのトレントは、<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>の「Datasets」セクションで見つけることができます（直接リンクはしていませんので、このブログへのリンクがRedditやTwitterなどから削除されないようにしています）。そこから、Torウェブサイトへのリンクをたどってください。 <a %(news_ycombinator)s>Hacker Newsで議論する</a> 次のステップは？ トレントの束は長期保存には最適ですが、日常的なアクセスにはあまり向いていません。私たちはホスティングパートナーと協力して、これらのデータをウェブ上にアップロードする予定です（アンナのアーカイブは直接ホストしていません）。もちろん、これらのダウンロードリンクはアンナのアーカイブで見つけることができます。 私たちはまた、皆さんがこのデータで何かをすることを招待しています！データをよりよく分析し、重複を排除し、IPFSに載せ、リミックスし、AIモデルをトレーニングするなど、すべてあなたのものです。あなたがそれで何をするのか、私たちは楽しみにしています。 最後に、前述のように、まだ大規模なリリースが控えています（もし<em>誰かが</em>偶然に<em>特定の<em>ACS4データベースのダンプを送ってくれたら、私たちの居場所はわかっていますよ…）、そして世界中の本をバックアップするためのフライホイールを構築しています。 どうぞお楽しみに、私たちはまだ始まったばかりです。 - アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) コミックブックの最大のシャドウライブラリは、おそらく特定のLibrary Genesisフォーク、Libgen.liのものです。そのサイトを運営する1人の管理者は、200万以上のファイルを集め、合計95TBを超える驚異的なコミックコレクションを収集しました。しかし、他のLibrary Genesisコレクションとは異なり、これはトレントを通じて一括で利用できませんでした。これらのコミックは彼の遅い個人サーバーを通じて個別にしかアクセスできませんでした — 単一障害点です。今日まで！ この投稿では、このコレクションについて、そしてこの作業をさらにサポートするための資金調達についてお話しします。 アンナのアーカイブは、世界最大のコミックシャドウライブラリ（95TB）をバックアップしました — あなたもシードに協力できます 世界最大の漫画のシャドウライブラリには、これまで単一の障害点がありましたが…今日までです。 警告：このブログ投稿は廃止されました。IPFSはまだ本格的に使用する準備ができていないと判断しました。可能な場合はアンナのアーカイブからIPFS上のファイルへのリンクを提供しますが、もはや自分たちでホストすることはなく、他の人がIPFSを使用してミラーリングすることも推奨しません。コレクションの保存を手伝いたい場合は、トレントページをご覧ください。 5,998,794冊の本をIPFSに配置 コピーの増殖 元の質問に戻りますが、どのようにして私たちのコレクションを永続的に保存できると主張できるのでしょうか？ここでの主な問題は、私たちのコレクションが<a %(torrents_stats)s>急速に成長している</a>ことです。これは、他のオープンデータシャドウライブラリー（Sci-HubやLibrary Genesisのような）によってすでに行われた素晴らしい作業に加えて、大規模なコレクションをスクレイピングしてオープンソース化することによってです。 このデータの成長は、コレクションを世界中でミラーすることを難しくします。データストレージは高価です！しかし、私たちは次の3つのトレンドを観察することで楽観的です。 過去数か月間のトレントシーダー数で分解されたコレクションの<a %(annas_archive_stats)s>総サイズ</a>。 異なるソースからのHDD価格のトレンド（クリックして調査を表示）。 <a %(critical_window_chinese)s>中国語版 中文版</a>、<a %(reddit)s>Reddit</a>、<a %(news_ycombinator)s>Hacker News</a>で議論 1. 簡単に手に入るものを手に入れました これは、上で議論した私たちの優先事項から直接続くものです。私たちはまず大規模なコレクションを解放することに取り組むことを好みます。今、世界最大のコレクションのいくつかを確保したので、成長ははるかに遅くなると予想しています。 まだ小さなコレクションの長い尾があり、新しい本が毎日スキャンされたり出版されたりしていますが、その速度はおそらくはるかに遅くなるでしょう。私たちはまだサイズが2倍または3倍になるかもしれませんが、より長い期間にわたってです。 OCRの改善。 優先事項 科学・工学ソフトウェアコード 上記のすべてのフィクションまたはエンターテインメント版 地理データ（例：地図、地質調査） 企業や政府からの内部データ（リーク） 科学的測定、経済データ、企業報告書のような測定データ メタデータ記録全般（ノンフィクションとフィクション、他のメディア、アート、人々などのレビューを含む） ノンフィクション書籍 ノンフィクション雑誌、新聞、マニュアル 講演、ドキュメンタリー、ポッドキャストのノンフィクションの書き起こし DNA配列、植物の種子、微生物サンプルのような有機データ 学術論文、ジャーナル、レポート 科学・工学ウェブサイト、オンラインディスカッション 法的または裁判手続きの書き起こし 特に破壊の危険にさらされている（例：戦争、資金削減、訴訟、政治的迫害による） 希少 特に注目されていない なぜ私たちは論文や本にそれほど関心を持っているのでしょうか？保存に対する基本的な信念を脇に置いておきましょう—それについては別の投稿を書くかもしれません。では、なぜ論文や本なのでしょうか？答えは簡単です：<strong>情報密度</strong>。 ストレージのメガバイトあたり、書かれたテキストはすべてのメディアの中で最も多くの情報を保存します。私たちは知識と文化の両方を大切にしていますが、前者をより重視しています。全体として、情報密度と保存の重要性の階層はおおよそ次のようになります： このリストの順位はやや恣意的です—いくつかの項目は同点であったり、チーム内で意見が分かれたりしています—そして、重要なカテゴリーをいくつか忘れているかもしれません。しかし、これはおおよそ私たちが優先する順序です。 これらの項目の中には、他のものとあまりにも異なっているために心配する必要がないもの（または他の機関によってすでに処理されているもの）もあります。例えば、有機データや地理データです。しかし、このリストのほとんどの項目は実際に私たちにとって重要です。 私たちの優先順位付けにおけるもう一つの大きな要因は、特定の作品がどれだけ危険にさらされているかです。私たちは、以下のような作品に焦点を当てることを好みます： 最後に、私たちは規模を重視します。時間とお金が限られているので、同じくらい価値があり危険にさらされているなら、1,000冊の本を救うよりも10,000冊の本を救うために1か月を費やしたいと思います。 <em><q>失われたものは回復できませんが、残っているものを保存しましょう。それを公共の目と使用から隔てる金庫や鍵ではなく、事故の手の届かないところに置くようなコピーの増殖によって。</q></em><br>— トーマス・ジェファーソン、1791年 シャドウライブラリー コードはGithubでオープンソースにすることができますが、Github全体を簡単にミラーして保存することはできません（ただし、この特定のケースでは、ほとんどのコードリポジトリの十分に分散されたコピーがあります）。 メタデータレコードはWorldcatのウェブサイトで自由に閲覧できますが、大量にダウンロードすることはできません（私たちが<a %(worldcat_scrape)s>スクレイピング</a>するまでは）。 Redditは無料で使用できますが、最近、データを大量に必要とするLLMトレーニングの影響で、厳しいスクレイピング対策を導入しました（詳細は後述します）。 同様の使命を持ち、同様の優先順位を持つ組織はたくさんあります。実際、図書館、アーカイブ、研究所、博物館、その他の保存を担当する機関があります。それらの多くは、政府、個人、または企業によって十分に資金提供されています。しかし、彼らには一つの大きな盲点があります：法制度です。 ここにシャドウライブラリーの独自の役割と、アンナのアーカイブが存在する理由があります。私たちは他の機関が許可されていないことを行うことができます。今、他の場所で保存することが違法な資料をアーカイブできるわけではありません。いいえ、どんな本、論文、雑誌などであってもアーカイブを構築することは多くの場所で合法です。 しかし、法的なアーカイブがしばしば欠けているのは<strong>冗長性と長寿命</strong>です。どこかの物理的な図書館にしか存在しない本が存在します。単一の企業によって守られているメタデータ記録が存在します。単一のアーカイブにしかマイクロフィルムで保存されていない新聞が存在します。図書館は資金削減を受けることがあり、企業は破産することがあり、アーカイブは爆撃されて焼失することがあります。これは仮定ではありません—これは常に起こっています。 Anna’s Archiveで私たちが独自にできることは、大規模に多くの作品のコピーを保存することです。論文、本、雑誌などを収集し、大量に配布することができます。現在はトレントを通じてこれを行っていますが、正確な技術は重要ではなく、時間とともに変わります。重要なのは、世界中に多くのコピーを配布することです。200年以上前のこの引用は今でも真実です： パブリックドメインについての簡単な注意。Annaのアーカイブは、世界中の多くの場所で違法とされる活動に特化しているため、パブリックドメインの本のような広く利用可能なコレクションにはこだわりません。法的な団体がそれをよく管理していることが多いです。しかし、時には公開されているコレクションに取り組む理由があります。 - アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. ストレージコストは指数関数的に下がり続けています 3. 情報密度の改善 現在、私たちは提供された生の形式で本を保存しています。もちろん、圧縮されていますが、多くの場合、ページの大きなスキャンや写真です。 これまでのところ、コレクション全体のサイズを縮小する唯一の選択肢は、より積極的な圧縮や重複排除でした。しかし、十分な節約を得るためには、どちらも私たちの好みにはあまりにも損失が大きすぎます。写真の重い圧縮は、テキストをほとんど読めなくすることがあります。そして、重複排除には、書籍が完全に同じであるという高い信頼性が必要であり、特に内容が同じでもスキャンが異なる場合には、しばしば不正確です。 常に第三の選択肢は存在していましたが、その品質があまりにもひどかったため、考慮に入れたことはありませんでした：<strong>OCR、または光学文字認識</strong>です。これは、AIを使用して写真内の文字を検出し、写真をプレーンテキストに変換するプロセスです。このためのツールは長い間存在しており、かなり優れていますが、「かなり優れている」だけでは保存目的には不十分です。 しかし、最近のマルチモーダルディープラーニングモデルは非常に急速に進歩していますが、依然として高コストです。今後数年で精度とコストの両方が劇的に改善され、私たちの全ライブラリに適用することが現実的になると期待しています。 その時が来れば、元のファイルを保存し続ける可能性が高いですが、さらに多くの人がミラーしたいと思うような、はるかに小さなバージョンのライブラリを持つことができるかもしれません。重要なのは、生のテキスト自体がさらに圧縮されやすく、重複排除がはるかに簡単であるため、さらに多くの節約ができることです。 全体として、ファイルサイズの合計が少なくとも5〜10倍、場合によってはそれ以上に削減されることを期待するのは非現実的ではありません。保守的に5倍の削減でも、ライブラリが3倍に増えても10年で<strong>1,000〜3,000ドルになると見込まれます</strong>。 執筆時点で、<a %(diskprices)s>ディスクの価格</a>は、新しいディスクで1TBあたり約12ドル、中古ディスクで8ドル、テープで4ドルです。新しいディスクのみを考慮すると、ペタバイトを保存するのに約12,000ドルかかります。私たちのライブラリーが900TBから2.7PBに3倍になると仮定すると、ライブラリー全体をミラーするのに32,400ドルかかります。電気代、他のハードウェアのコストなどを加えると、40,000ドルに丸めましょう。テープを使用すると、15,000ドルから20,000ドル程度です。 一方で<strong>全人類の知識の合計が15,000ドルから40,000ドルというのはお得です</strong>。しかし、特に他の人々の利益のためにトレントをシードし続けることを望む場合、完全なコピーを大量に期待するのは少し高いです。 それが今日です。しかし、進歩は前進します： 過去10年間でHDDの価格は1TBあたり約3分の1に削減されており、同様のペースで下がり続ける可能性があります。テープも同様の軌道にあるようです。SSDの価格はさらに速く下がっており、10年後にはHDDの価格を超える可能性があります。 これが続けば、10年後にはコレクション全体をミラーするのに5,000ドルから13,000ドル（3分の1）しかかからないかもしれません。サイズがあまり増えなければさらに少なくなるかもしれません。まだ多くのお金ですが、多くの人にとって手の届くものになるでしょう。そして、次のポイントのおかげでさらに良くなるかもしれません… Anna’s Archiveでは、コレクションの総サイズがすでに1ペタバイト（1000TB）に近づいており、さらに増加している中で、どのようにして永続的に保存できると主張できるのかとよく質問されます。この記事では、私たちの哲学を見て、人類の知識と文化を保存するという私たちの使命にとって次の10年がなぜ重要なのかを考察します。 重要なウィンドウ これらの予測が正確であれば、私たちは<strong>数年待つだけで</strong>、私たちのコレクション全体が広くミラーされるようになるでしょう。したがって、トーマス・ジェファーソンの言葉を借りれば、「事故の手の届かないところに置かれる」ことになります。 残念ながら、LLMの出現とそのデータを大量に必要とするトレーニングにより、多くの著作権者が防御的になっています。以前よりもさらに多くのウェブサイトがスクレイピングやアーカイブを難しくし、訴訟が飛び交い、その間にも物理的な図書館やアーカイブは引き続き無視されています。 これらの傾向が悪化し続け、多くの作品がパブリックドメインに入る前に失われることを予想するしかありません。 <strong>私たちは保存の革命の前夜にいますが、<q>失われたものは回復できません。</q></strong> シャドウライブラリを運営し、世界中に多くのミラーを作成するのがまだかなり高価であり、アクセスが完全に遮断されていない約5〜10年の重要なウィンドウがあります。 このウィンドウを乗り越えることができれば、人類の知識と文化を永続的に保存することができるでしょう。この時間を無駄にしてはいけません。この重要なウィンドウを閉じさせてはいけません。 行きましょう。 シャドウライブラリーの重要なウィンドウ すでに1PBに近づいているコレクションをどのようにして永続的に保存できると主張できるのでしょうか？ コレクション コレクションに関する詳細情報です。<a %(duxiu)s>Duxiu</a>は、<a %(chaoxing)s>SuperStar Digital Library Group</a>によって作成された膨大なスキャンされた書籍のデータベースです。ほとんどが学術書で、大学や図書館でデジタルで利用できるようにスキャンされています。英語を話す観客のために、<a %(library_princeton)s>プリンストン</a>と<a %(guides_lib_uw)s>ワシントン大学</a>が良い概要を提供しています。また、背景を詳しく説明した優れた記事もあります：<a %(doi)s>「中国の書籍のデジタル化：SuperStar DuXiu Scholar Search Engineのケーススタディ」</a>（アンナのアーカイブで検索してください）。 Duxiuの書籍は長い間、中国のインターネットで海賊版として流通してきました。通常、再販業者によって1ドル未満で販売されています。通常、中国版のGoogleドライブを使用して配布されており、しばしばストレージスペースを増やすためにハッキングされています。技術的な詳細は<a %(github_duty_machine)s>こちら</a>と<a %(github_821_github_io)s>こちら</a>で見つけることができます。 これらの書籍は半公開的に配布されてきましたが、大量に入手するのは非常に困難です。私たちはこれをTODOリストの上位に置き、フルタイムで数ヶ月を割り当てました。しかし、最近、信じられないほど素晴らしい才能あるボランティアが私たちに連絡を取り、すでにすべての作業を行ったと教えてくれました。彼らは見返りを求めず、長期保存の保証だけを求めて、全コレクションを共有してくれました。本当に驚くべきことです。彼らはこの方法で助けを求めることに同意し、コレクションをOCR化することを求めました。 コレクションは7,543,702ファイルです。これはLibrary Genesisのノンフィクション（約530万）よりも多いです。現在の形式での総ファイルサイズは約359TB（326TiB）です。 他の提案やアイデアにもオープンです。ぜひご連絡ください。アンナのアーカイブで私たちのコレクション、保存活動、そしてどのように協力できるかについての詳細情報をご覧ください。ありがとうございます！ サンプルページ 私たちに優れたパイプラインがあることを証明するために、超伝導体に関する本から始めるためのサンプルページをいくつか用意しました。あなたのパイプラインは、数学、表、チャート、脚注などを適切に処理する必要があります。 処理したページを私たちのメールに送ってください。見栄えが良ければ、プライベートでさらに送りますので、それらにも迅速にパイプラインを実行できることを期待しています。満足したら、契約を結ぶことができます。 - アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>中国語版 中文版</a>、<a %(news_ycombinator)s>Hacker Newsで議論する</a> これは短いブログ投稿です。私たちは、取得した膨大なコレクションのOCRとテキスト抽出を手伝ってくれる企業や機関を探しています。独占的な早期アクセスと引き換えに。禁輸期間後、もちろんコレクション全体を公開します。 高品質の学術テキストは、LLMのトレーニングに非常に役立ちます。私たちのコレクションは中国語ですが、英語のLLMのトレーニングにも役立つはずです：モデルはソース言語に関係なく概念と知識をエンコードするようです。 これには、スキャンからテキストを抽出する必要があります。アンナのアーカイブが得るものは何ですか？ユーザーのための書籍の全文検索です。 私たちの目標はLLM開発者の目標と一致しているため、協力者を探しています。適切なOCRとテキスト抽出ができるなら、<strong>このコレクションへの独占的な早期アクセスを1年間提供する用意があります</strong>。パイプラインのコード全体を共有する用意があるなら、コレクションの禁輸期間を延長する用意があります。 世界最大の中国ノンフィクション書籍コレクションへのLLM企業の独占アクセス <em><strong>要約:</strong> アンナのアーカイブは、7.5百万冊/350TBの中国ノンフィクション書籍のユニークなコレクションを取得しました — Library Genesisよりも大きいです。高品質のOCRとテキスト抽出と引き換えに、LLM企業に独占アクセスを提供する用意があります。</em> システム構成 では、あなたのウェブサイトを閉鎖せずにホスティングしてくれる企業を見つけたとしましょう—これらを「自由を愛するプロバイダー」と呼びましょう😄。すぐに、すべてを彼らと一緒にホスティングするのはかなり高価であることに気づくでしょう。そこで、「安価なプロバイダー」を見つけて、実際のホスティングをそこで行い、自由を愛するプロバイダーを通じてプロキシすることを考えるかもしれません。うまくやれば、安価なプロバイダーはあなたが何をホスティングしているのかを知ることはなく、苦情を受けることもありません。 これらすべてのプロバイダーには、いずれにせよ閉鎖されるリスクがあるため、冗長性も必要です。私たちはスタックのすべてのレベルでこれを必要としています。 ある程度自由を愛する企業で興味深い立場を取っているのがCloudflareです。彼らは<a %(blog_cloudflare)s>主張</a>しているのは、ホスティングプロバイダーではなく、ISPのようなユーティリティであるということです。したがって、DMCAや他の削除要求の対象にはならず、実際のホスティングプロバイダーに要求を転送します。この構造を保護するために法廷にまで行ったこともあります。したがって、キャッシングと保護のもう一つの層として彼らを利用することができます。 Cloudflareは匿名の支払いを受け付けていないため、無料プランしか利用できません。これにより、ロードバランシングやフェイルオーバー機能を使用することはできません。したがって、ドメインレベルで<a %(annas_archive_l255)s>これを自分たちで実装しました</a>。ページが読み込まれると、ブラウザは現在のドメインがまだ利用可能かどうかを確認し、そうでない場合はすべてのURLを別のドメインに書き換えます。Cloudflareが多くのページをキャッシュしているため、ユーザーはプロキシサーバーがダウンしていてもメインドメインにアクセスでき、次のクリックで別のドメインに移動することができます。 また、サーバーの健康状態の監視、バックエンドとフロントエンドのエラーのログ記録など、通常の運用上の懸念にも対処する必要があります。私たちのフェイルオーバーアーキテクチャは、この面でもより堅牢性を提供します。たとえば、ドメインの一つで完全に異なるサーバーセットを実行することによってです。メインバージョンで重大なバグが見逃された場合に備えて、この別のドメインで古いバージョンのコードとデータセットを実行することもできます。 Cloudflareが私たちに対して反旗を翻すことに備えて、この別のドメインからCloudflareを削除することで対策を講じることもできます。これらのアイデアの異なる組み合わせが可能です。 結論 堅牢で回復力のあるシャドウライブラリ検索エンジンをセットアップする方法を学ぶのは興味深い経験でした。今後の投稿で共有する詳細がたくさんありますので、もっと知りたいことがあれば教えてください！ いつものように、この作業を支援するための寄付を募っていますので、Annaのアーカイブの寄付ページをぜひご覧ください。また、助成金、長期スポンサー、ハイリスクの支払いプロバイダー、場合によっては（センスの良い！）広告など、他の種類のサポートも探しています。時間やスキルを提供したい方は、開発者や翻訳者などを常に募集しています。ご関心とご支援に感謝します。 イノベーショントークン 技術スタックから始めましょう。それは意図的に退屈です。Flask、MariaDB、ElasticSearchを使用しています。それがすべてです。検索はほぼ解決された問題であり、再発明するつもりはありません。それに、私たちは<a %(mcfunley)s>イノベーショントークン</a>を他のことに使わなければなりません：当局に取り締まられないことです。 では、アンナのアーカイブはどれほど合法または違法なのでしょうか？これは主に法的管轄に依存します。ほとんどの国は何らかの形で著作権を信じており、特定の期間、特定の種類の作品に対して人や企業に独占的な権利を与えます。ちなみに、アンナのアーカイブでは、いくつかの利点がある一方で、全体として著作権は社会にとってマイナスであると考えていますが、それはまた別の話です。 特定の作品に対するこの独占的な権利は、この独占の外にいる誰もがそれらの作品を直接配布することを違法としています—私たちも含めて。しかし、アンナのアーカイブはそれらの作品を直接配布する検索エンジンであり（少なくとも私たちのクリーンネットのウェブサイトではありません）、それなら大丈夫ですよね？必ずしもそうではありません。多くの法域では、著作権で保護された作品を配布することが違法であるだけでなく、それを行う場所へのリンクを貼ることも違法です。この典型的な例がアメリカ合衆国のDMCA法です。 これはスペクトラムの最も厳しい端です。スペクトラムの他の端には、理論的には著作権法が全く存在しない国があるかもしれませんが、実際には存在しません。ほとんどすべての国には何らかの形で著作権法があります。執行は別の話です。著作権法を執行することに関心のない政府を持つ国はたくさんあります。また、両極端の間に位置する国もあり、著作権で保護された作品の配布を禁止していますが、そのような作品へのリンクを禁止していません。 もう一つの考慮事項は、企業レベルでのことです。ある企業が著作権を気にしない法域で運営されていても、その企業自体がリスクを取ることを望まない場合、誰かが苦情を申し立てた時点でウェブサイトを閉鎖するかもしれません。 最後に、大きな考慮事項は支払いです。匿名性を保つ必要があるため、従来の支払い方法を使用することはできません。これにより、暗号通貨が残りますが、それをサポートする企業はごく一部です（暗号通貨で支払われる仮想デビットカードもありますが、しばしば受け入れられません）。 - アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 私は<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>を運営しています。これは、Sci-Hub、Library Genesis、Z-ライブラリのような<a %(wikipedia_shadow_library)s>シャドウライブラリ</a>のための世界最大のオープンソース非営利検索エンジンです。私たちの目標は、知識と文化を容易にアクセス可能にし、最終的には世界中の<a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>すべての本をアーカイブし保存する</a>人々のコミュニティを構築することです。 この記事では、このウェブサイトをどのように運営しているか、そして法的に疑わしい地位を持つウェブサイトを運営する際の独自の課題について説明します。シャドウチャリティのための「AWS」は存在しません。 <em>姉妹記事<a %(blog_how_to_become_a_pirate_archivist)s>「海賊アーキビストになる方法」</a>もご覧ください。</em> アンナのアーカイブでのシャドウライブラリの運営方法 シャドウチャリティのための[AWS]はないので、アンナのアーカイブをどのように運営しているのでしょうか？ ツール アプリケーションサーバー：Flask、MariaDB、ElasticSearch、Docker。 開発：Gitlab、Weblate、Zulip。 サーバー管理：Ansible、Checkmk、UFW。 オニオン静的ホスティング：Tor、Nginx。 プロキシサーバー：Varnish。 これを達成するために使用しているツールを見てみましょう。これは新しい問題に直面し、新しい解決策を見つけるにつれて非常に進化しています。 いくつかの決定については、行ったり来たりしています。一つはサーバー間の通信です：以前はWireguardを使用していましたが、時折データの送信が停止したり、一方向にしかデータを送信しなかったりすることがわかりました。<a %(github_costela_wesher)s>wesher</a>や<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>など、試したいくつかの異なるWireguardセットアップでこれが発生しました。また、autosshやsshuttleを使用してSSH経由でポートをトンネリングすることも試みましたが、<a %(github_sshuttle)s>問題に直面しました</a>（ただし、autosshがTCP-over-TCPの問題を抱えているかどうかはまだ不明です—私には不安定な解決策のように感じますが、実際には問題ないかもしれません）。 代わりに、サーバー間の直接接続に戻り、UFWを使用して安価なプロバイダーでサーバーが稼働していることを隠しています。これには、DockerがUFWとうまく機能しないという欠点がありますが、<code>network_mode: "host"</code>を使用すれば問題ありません。これらすべては、わずかな誤設定でサーバーをインターネットに公開してしまうため、ややエラーが発生しやすくなります。おそらくautosshに戻るべきかもしれません—ここでのフィードバックを非常に歓迎します。 私たちは、VarnishとNginxの間で行ったり来たりしてきました。現在はVarnishを好んでいますが、いくつかの癖や粗さがあります。同じことがCheckmkにも言えます。好きではありませんが、今のところは機能しています。Weblateはまあまあですが、素晴らしいとは言えません。gitリポジトリと同期しようとするたびにデータを失うのではないかと心配になることがあります。Flaskは全体的に良いですが、カスタムドメインの設定やSqlAlchemyの統合に関する問題など、デバッグに多くの時間を費やした奇妙な癖があります。 これまでのところ、他のツールは素晴らしいです。MariaDB、ElasticSearch、Gitlab、Zulip、Docker、Torについては、深刻な不満はありません。これらにはいくつかの問題がありましたが、深刻すぎたり時間がかかりすぎたりするものはありませんでした。 コミュニティ 最初の課題は意外なものかもしれません。それは技術的な問題でも、法的な問題でもありません。心理的な問題です：この作業を影で行うことは非常に孤独になる可能性があります。あなたが何を計画しているか、そしてあなたの脅威モデルによっては、非常に注意深く行動する必要があるかもしれません。スペクトラムの一方の端には、Sci-Hubの創設者であるアレクサンドラ・エルバキアンのような人々がいます。彼女は自分の活動について非常にオープンですが、現在のところ西側諸国を訪れると逮捕されるリスクが高く、数十年の刑務所生活を送る可能性があります。それはあなたが取る価値のあるリスクですか？私たちはスペクトラムの他の端にいます。痕跡を残さないように非常に注意深く行動し、強力な運用セキュリティを持っています。 * HNで「ynno」によって言及されたように、アレクサンドラは最初は知られたくなかった：「彼女のサーバーは、PHPからの詳細なエラーメッセージを発信するように設定されており、フォールトソースファイルのフルパスを含んでいました。これは、彼女がオンラインで使用していたユーザー名に追跡できるもので、彼女の本名に関連付けられていました。この暴露があるまで、彼女は匿名でした。」したがって、この種の作業に使用するコンピュータではランダムなユーザー名を使用してください。何かを誤設定した場合に備えて。 しかし、その秘密主義には心理的なコストが伴います。ほとんどの人は自分の仕事が認められることを愛していますが、現実の世界ではその功績を認められることはできません。友人があなたが何をしているのか尋ねるような単純なことさえも挑戦的です（ある時点で「NAS / ホームラボで遊んでいる」と言うのは古くなります）。 だからこそ、コミュニティを見つけることが非常に重要です。非常に信頼できる親しい友人に打ち明けることで、運用セキュリティを少し犠牲にすることができます。それでも、彼らが当局にメールを提出しなければならない場合や、デバイスが他の方法で侵害されている場合に備えて、何も書面に残さないように注意してください。 さらに良いのは、仲間の海賊を見つけることです。親しい友人が参加に興味を持っているなら、素晴らしいことです！そうでない場合は、オンラインで他の人を見つけることができるかもしれません。残念ながら、これはまだニッチなコミュニティです。これまでのところ、この分野で活動している他の人はほんの一握りしか見つかっていません。良い出発点は、Library Genesisフォーラムやr/DataHoarderのようです。アーカイブチームにも同じ考えを持つ人々がいますが、彼らは法律の範囲内で活動しています（たとえ法律のグレーゾーンであっても）。伝統的な「warez」や海賊シーンにも、同様の考えを持つ人々がいます。 コミュニティを育成し、アイデアを探求する方法についてのアイデアを募集しています。TwitterやRedditで私たちにメッセージを送ってください。フォーラムやチャットグループをホストすることもできるかもしれません。一般的なプラットフォームを使用すると簡単に検閲される可能性があるため、自分たちでホストする必要があります。また、これらの議論を完全に公開する（より多くのエンゲージメントの可能性）か、非公開にする（潜在的な「ターゲット」に私たちが彼らをスクレイプしようとしていることを知らせない）かのトレードオフもあります。それについて考える必要があります。興味がある場合はお知らせください！ 結論 新たに始める海賊アーカイブ主義者にとって、これが役立つことを願っています。この世界にあなたを迎えることに興奮していますので、遠慮なく連絡してください。できるだけ多くの世界の知識と文化を保存し、それを広くミラーリングしましょう。 プロジェクト 4. データ選択 多くの場合、metadataを使用してダウンロードするデータの合理的なサブセットを見つけることができます。最終的にすべてのデータをダウンロードしたい場合でも、最も重要な項目を優先することは有用です。検出されて防御が強化される場合や、ディスクを追加購入する必要がある場合、またはすべてをダウンロードする前に他のことが発生する場合に備えてです。 たとえば、コレクションには同じ基礎リソース（本や映画など）の複数の版があり、そのうちの1つが最高品質としてマークされている場合があります。最初にそれらの版を保存することは理にかなっています。最終的にはすべての版を保存したいかもしれません。なぜなら、場合によってはmetadataが誤ってタグ付けされているか、版間で未知のトレードオフがあるかもしれないからです（たとえば、「最高の版」はほとんどの点で最高ですが、他の点で劣っているかもしれません。たとえば、映画が高解像度であるが字幕がないなど）。 metadataデータベースを検索して興味深いものを見つけることもできます。ホストされている最大のファイルは何で、なぜそれほど大きいのか？最小のファイルは何か？特定のカテゴリ、言語などに関して興味深いまたは予期しないパターンはありますか？重複または非常に似たタイトルはありますか？データが追加された時期にパターンはありますか？たとえば、ある日に多くのファイルが一度に追加されたなど。データセットをさまざまな方法で見ることで多くのことを学ぶことができます。 私たちの場合、Z-ライブラリの本をLibrary Genesisのmd5ハッシュと重複排除することで、多くのダウンロード時間とディスクスペースを節約しました。これは非常にユニークな状況ですが、ほとんどの場合、どのファイルがすでに仲間の海賊によって適切に保存されているかを包括的に示すデータベースは存在しません。これは、どこかの誰かにとって大きなチャンスです。音楽や映画など、すでにトレントサイトで広くシードされているものの定期的な更新概要があれば、海賊ミラーに含める優先度が低くなるでしょう。 6. 配布 データを手に入れたことで、世界初のターゲットの海賊ミラーを所有することになりました（おそらく）。多くの面で最も難しい部分は終わりましたが、最もリスクの高い部分はまだ先にあります。結局のところ、これまでのところはステルスで、レーダーの下を飛んでいました。良いVPNを常に使用し、個人情報をフォームに入力しない（当然）、特別なブラウザセッション（または別のコンピュータ）を使用するだけで済みました。 今度はデータを配布しなければなりません。私たちの場合、最初は本をLibrary Genesisに戻すことを考えましたが、すぐにその難しさ（フィクションとノンフィクションの分類）に気付きました。それで、Library Genesisスタイルのトレントを使用して配布することに決めました。既存のプロジェクトに貢献する機会があれば、それは多くの時間を節約することができます。しかし、現在、よく組織された海賊ミラーはあまりありません。 では、自分でトレントを配布することにしたとしましょう。それらのファイルを小さく保ち、他のウェブサイトでミラーリングしやすくしましょう。その後、自分でトレントをシードしながら匿名性を保つ必要があります。VPN（ポートフォワーディングの有無にかかわらず）を使用するか、シードボックスのために混合ビットコインで支払うことができます。これらの用語のいくつかが何を意味するのかわからない場合は、リスクのトレードオフを理解することが重要なので、読むべきことがたくさんあります。 トレントファイル自体を既存のトレントウェブサイトにホストすることができます。私たちの場合、実際にウェブサイトをホストすることを選びました。なぜなら、私たちの哲学を明確に広めたいと考えたからです。あなたも同様の方法でこれを行うことができます（私たちはNjallaをドメインとホスティングに使用し、混合ビットコインで支払っています）が、私たちに連絡してトレントをホストしてもらうこともできます。このアイデアが広まれば、時間をかけて海賊ミラーの包括的なインデックスを構築することを目指しています。 VPNの選択については、すでに多くのことが書かれているので、評判で選ぶという一般的なアドバイスを繰り返すだけにします。実際に裁判でテストされたログなしポリシーで、プライバシーを長期間保護してきた実績があるものが、私たちの意見では最もリスクの低いオプションです。すべてを正しく行っても、リスクをゼロにすることはできないことに注意してください。たとえば、トレントをシードしているとき、非常に動機のある国家主体がVPNサーバーの入出力データフローを見て、あなたが誰であるかを推測することができるかもしれません。または、単に何かを間違えることもあります。私たちはおそらくすでに間違えたことがあり、また間違えるでしょう。幸いなことに、国家は<em>それほど</em>海賊行為に関心を持っていません。 各プロジェクトで決定することの一つは、以前と同じアイデンティティを使用して公開するかどうかです。同じ名前を使い続けると、以前のプロジェクトでの運用セキュリティのミスが後で問題になる可能性があります。しかし、異なる名前で公開することは、長期的な評判を築かないことを意味します。私たちは、最初から強力な運用セキュリティを持つことを選び、同じアイデンティティを使い続けることができるようにしましたが、間違えたり、状況が求める場合には、異なる名前で公開することをためらいません。 情報を広めることは難しいかもしれません。私たちが言ったように、これはまだニッチなコミュニティです。私たちは最初にRedditに投稿しましたが、実際にはHacker Newsで注目を集めました。今のところ、いくつかの場所に投稿して何が起こるかを見てみることをお勧めします。そして、再び私たちに連絡してください。私たちは、より多くの海賊アーカイブ活動の言葉を広めたいと思っています。 1. ドメイン選択 / 哲学 保存すべき知識や文化遺産は不足しておらず、圧倒されることがあります。だからこそ、一瞬立ち止まって、自分の貢献が何であるかを考えることがしばしば有用です。 これについて考える方法は人それぞれですが、自分自身に問いかけることができる質問をいくつか紹介します。 私たちの場合、特に科学の長期保存に関心がありました。Library Genesisについて知っており、トレントを使用して何度も完全にミラーリングされていることを知っていました。そのアイデアが大好きでした。ある日、私たちの一人がLibrary Genesisで科学の教科書を探そうとしましたが、見つからず、その完全性に疑問を抱きました。それからオンラインでその教科書を検索し、他の場所で見つけ、それが私たちのプロジェクトの種を植えました。Z-ライブラリについて知る前から、すべての本を手動で集めようとするのではなく、既存のコレクションをミラーリングし、それをLibrary Genesisに貢献するというアイデアを持っていました。 どのようなスキルを持っていて、それをどのように活用できますか？例えば、オンラインセキュリティの専門家であれば、安全なターゲットのIPブロックを打破する方法を見つけることができます。コミュニティを組織するのが得意であれば、目標に向かって人々を集めることができるかもしれません。ただし、このプロセス全体を通じて良好な運用セキュリティを維持するために、プログラミングの知識があると便利です。 どこに焦点を当てると高いレバレッジが得られますか？海賊アーカイブにX時間を費やす場合、どのようにして「コストパフォーマンス」を最大化できますか？ これについてどのように考えていますか？他の人が見逃しているかもしれない興味深いアイデアやアプローチを持っているかもしれません。 これにどれくらいの時間を割けますか？私たちのアドバイスは、小さく始めて、慣れてきたら大きなプロジェクトに取り組むことですが、すべてを消費することもあります。 なぜこれに興味があるのですか？何に情熱を持っていますか？もし、特定のことに関心を持つ人々が集まってアーカイブを行うことができれば、それは多くをカバーすることになります！あなたは、自分の情熱について、平均的な人よりも多くのことを知っているでしょう。例えば、保存すべき重要なデータ、最高のコレクションやオンラインコミュニティなどです。 3. Metadataスクレイピング 追加/変更日: 後で戻ってきて、以前ダウンロードしなかったファイルをダウンロードできるように（ただし、IDやハッシュを使用することもできます）。 ハッシュ (md5, sha1): ファイルを正しくダウンロードしたことを確認するため。 ID: 内部IDでも構いませんが、ISBNやDOIのようなIDは便利です。 ファイル名 / 場所 説明、カテゴリ、タグ、著者、言語など。 サイズ: 必要なディスク容量を計算するため。 ここで少し技術的な話をしましょう。ウェブサイトからmetadataを実際にスクレイピングするために、私たちは非常にシンプルな方法を採用しています。Pythonスクリプトや時にはcurlを使用し、結果をMySQLデータベースに保存しています。複雑なウェブサイトをマッピングできる高度なスクレイピングソフトウェアは使用していません。これまでのところ、IDを列挙してHTMLを解析するだけで1種類か2種類のページをスクレイピングする必要があるだけでした。簡単に列挙できるページがない場合は、すべてのページを見つけようとする適切なクローラーが必要かもしれません。 ウェブサイト全体をスクレイピングする前に、少し手動で試してみてください。数十ページを自分で確認し、その仕組みを理解してください。この方法でIPブロックや他の興味深い動作に遭遇することがあります。データスクレイピングについても同様です。このターゲットに深く入り込む前に、実際にデータを効果的にダウンロードできるか確認してください。 制限を回避するために試せることがいくつかあります。同じデータをホストしているが同じ制限がない他のIPアドレスやサーバーはありますか？制限がないAPIエンドポイントはありますか？どのダウンロード速度でIPがブロックされ、どのくらいの期間ブロックされますか？ブロックされずに速度が制限されることはありますか？ユーザーアカウントを作成すると、どのように変わりますか？HTTP/2を使用して接続を開いたままにし、ページをリクエストする速度を上げることはできますか？複数のファイルを一度にリストするページがあり、そこにリストされている情報は十分ですか？ 保存しておくべき情報には、以下のようなものが含まれます： 通常、これを2段階で行います。最初に生のHTMLファイルをダウンロードし、通常はMySQLに直接保存します（小さなファイルがたくさんできるのを避けるため、これについては後で詳しく説明します）。次に、別のステップでそれらのHTMLファイルを実際のMySQLテーブルに解析します。これにより、解析コードにミスを発見した場合でも、すべてを最初から再ダウンロードする必要がなく、新しいコードでHTMLファイルを再処理するだけで済みます。また、処理ステップを並列化するのが簡単なことが多く、時間を節約できます（スクレイピングが実行されている間に処理コードを書くことができ、両方のステップを一度に書く必要がありません）。 最後に、いくつかのターゲットにとってはmetadataスクレイピングがすべてであることに注意してください。適切に保存されていない巨大なmetadataコレクションが存在します。 タイトル ドメイン選択 / 哲学：どこに焦点を当てたいのか、そしてなぜか？あなたのユニークな情熱、スキル、状況をどのように活用できるか？ ターゲット選択：どの特定のコレクションをミラーリングしますか？ メタデータスクレイピング: ファイル自体をダウンロードせずに、ファイルに関する情報をカタログ化すること。 データ選択: メタデータに基づいて、今すぐアーカイブするのに最も関連性の高いデータを絞り込むこと。すべてを対象にすることも可能ですが、スペースと帯域幅を節約する合理的な方法があることが多いです。 データスクレイピング: 実際にデータを取得すること。 配布: トレントでパッケージ化し、どこかで発表し、人々に広めてもらうこと。 5. データスクレイピング これで、実際にデータを一括でダウンロードする準備が整いました。前述のように、この時点でターゲットの動作や制限をよりよく理解するために、手動でいくつかのファイルをダウンロードしているはずです。しかし、実際に大量のファイルを一度にダウンロードし始めると、まだ驚きが待っているでしょう。 ここでのアドバイスは、主にシンプルに保つことです。まずは、いくつかのファイルをダウンロードすることから始めましょう。Pythonを使用し、その後複数のスレッドに拡張することができます。しかし、時には、データベースから直接Bashファイルを生成し、それを複数のターミナルウィンドウで実行してスケールアップする方が簡単です。ここで言及する価値のある技術的なトリックは、MySQLでOUTFILEを使用することです。mysqld.cnfで「secure_file_priv」を無効にすれば、どこにでも書き込むことができます（Linuxを使用している場合は、AppArmorも無効化/オーバーライドすることを忘れないでください）。 データはシンプルなハードディスクに保存します。手持ちのものから始めて、ゆっくりと拡張していきましょう。何百TBものデータを保存することを考えると圧倒されるかもしれません。そのような状況に直面している場合は、まず良いサブセットを公開し、残りの保存に協力を求めるアナウンスを出しましょう。自分でさらにハードドライブを入手したい場合は、r/DataHoarderにはお得な情報を得るための良いリソースがあります。 ファンシーなファイルシステムについてあまり心配しないようにしましょう。ZFSのようなものを設定することに夢中になるのは簡単です。しかし、技術的な詳細として、多くのファイルを扱うのが苦手なファイルシステムが多いことに注意してください。私たちが見つけたシンプルな回避策は、異なるID範囲やハッシュプレフィックスごとに複数のディレクトリを作成することです。 データをダウンロードした後、可能であればmetadataのハッシュを使用してファイルの整合性を確認してください。 2. ターゲット選択 アクセス可能: メタデータやデータのスクレイピングを防ぐための多層の保護を使用していない。 特別な洞察: このターゲットについて特別な情報を持っている、例えば、特別なアクセス権を持っている、または防御を打破する方法を見つけたなど。これは必須ではありません（私たちの今後のプロジェクトは特別なことをしていません）が、確かに役立ちます！ 大規模 私たちが注目している分野が決まったので、どの特定のコレクションをミラーリングするか？良いターゲットとなる要素はいくつかあります。 私たちがLibrary Genesis以外のウェブサイトで科学の教科書を見つけたとき、それらがどのようにインターネットに流出したのかを調べようとしました。そしてZ-ライブラリを見つけ、多くの本が最初にそこに現れるわけではないが、最終的にはそこにたどり着くことを理解しました。Library Genesisとの関係や、（金銭的な）インセンティブ構造と優れたユーザーインターフェースについて学び、これらがより完全なコレクションを作り上げていることを知りました。その後、予備的なmetadataとデータスクレイピングを行い、メンバーの一人が多くのプロキシサーバーに特別にアクセスできることを利用して、IPダウンロード制限を回避できることに気付きました。 さまざまなターゲットを探索する際には、VPNや使い捨てのメールアドレスを使用して足跡を隠すことがすでに重要です。これについては後ほど詳しく説明します。 ユニーク: 他のプロジェクトで既に十分にカバーされていない。 私たちがプロジェクトを行う際には、いくつかの段階があります： これらは完全に独立したフェーズではなく、後のフェーズからの洞察が前のフェーズに戻ることがあります。例えば、メタデータスクレイピング中に、選択したターゲットがスキルレベルを超える防御メカニズム（IPブロックなど）を持っていることに気づいた場合、別のターゲットを見つけるために戻ることがあります。 - アンナとチーム (<a %(reddit)s>Reddit</a>) デジタル保存の「なぜ」について、特に海賊アーキビズムについては、丸ごと本が書けるほどですが、あまり詳しくない方のために簡単に説明します。世界はこれまで以上に多くの知識と文化を生み出していますが、同時にこれまで以上に多くのものが失われています。人類は主に学術出版社、ストリーミングサービス、ソーシャルメディア企業などの企業にこの遺産を託していますが、彼らは必ずしも優れた管理者であるとは限りません。ドキュメンタリー「デジタル・アムネジア」や、ジェイソン・スコットの講演をぜひご覧ください。 多くのものをアーカイブするのに優れた機関もありますが、彼らは法律に縛られています。海賊として、私たちは著作権の執行やその他の制約のために触れることができないコレクションをアーカイブするユニークな立場にあります。また、世界中でコレクションを何度もミラーリングすることで、適切な保存の可能性を高めることができます。 今のところ、知的財産の利点と欠点、法律を破ることの道徳性、検閲についての考察、知識と文化へのアクセスの問題についての議論には入りません。それらをすべて片付けたところで、<em>どのように</em>進めるかに入りましょう。私たちのチームがどのようにして海賊アーキビストになったのか、そしてその過程で学んだ教訓を共有します。この旅に出るときには多くの課題がありますが、いくつかの課題を乗り越える手助けができればと思います。 海賊アーキビストになる方法 最初の課題は意外なものかもしれません。それは技術的な問題でも、法的な問題でもありません。心理的な問題です。 始める前に、海賊図書館ミラー（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）に関する2つの更新情報があります。 非常に寛大な寄付をいただきました。最初は、Library Genesisの創設者である「bookwarrior」を支援している匿名の個人からの1万ドルの寄付でした。この寄付を実現してくれたbookwarriorに特別な感謝を。2つ目は、私たちの最後のリリース後に連絡をくれた匿名の寄付者からのもう1万ドルの寄付でした。さらに、小さな寄付もいくつかいただきました。皆様の寛大なご支援に心から感謝いたします。これにより、いくつかのエキサイティングな新プロジェクトを進めることができるので、どうぞお楽しみに。 2回目のリリースのサイズに関して技術的な問題がありましたが、トレントは現在稼働中でシードされています。また、匿名の個人から非常に高速なサーバーでコレクションをシードするという寛大な申し出をいただきましたので、彼らのマシンに特別なアップロードを行っています。その後、コレクションをダウンロードしている他のすべての人は、速度の大幅な改善を実感できるはずです。 ブログ投稿 こんにちは、私はアンナです。私は<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>、世界最大のシャドウライブラリを作りました。これは私の個人ブログで、私とチームメイトが海賊版、デジタル保存などについて書いています。 <a %(reddit)s>Reddit</a>で私とつながりましょう。 このウェブサイトはブログだけです。ここでは私たち自身の言葉だけをホストしています。トレントや他の著作権で保護されたファイルはここでホストされておらず、リンクもされていません。 <strong>図書館</strong> - ほとんどの図書館と同様に、私たちは主に書籍のような書かれた資料に焦点を当てています。将来的には他の種類のメディアに拡大するかもしれません。 <strong>ミラー</strong> - 私たちは既存の図書館の厳密なミラーです。私たちは保存に焦点を当てており、本を簡単に検索してダウンロードできるようにすること（アクセス）や、新しい本を提供する人々の大きなコミュニティを育成すること（ソーシング）には焦点を当てていません。 <strong>海賊</strong> - 私たちは意図的に多くの国で著作権法を侵害しています。これにより、法的な団体ができないことを行うことができます：本が広くミラーされることを確実にすることです。 <em>このブログからファイルへのリンクはしていません。ご自身で見つけてください。</em> - アンナとチーム (<a %(reddit)s>Reddit</a>) このプロジェクト（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）は、人類の知識の保存と解放に貢献することを目的としています。私たちは、私たちの前に立った偉人たちの足跡をたどりながら、小さく謙虚な貢献をしています。 このプロジェクトの焦点は、その名前によって示されています： 私たちが最初にミラーした図書館はZ-ライブラリです。これは人気のある（そして違法な）図書館です。彼らはLibrary Genesisのコレクションを取り、それを簡単に検索できるようにしました。その上、彼らは新しい本の提供を奨励することで、非常に効果的に新しい本の提供を募っています。現在、これらの新しい本をLibrary Genesisに戻して提供していません。そして、Library Genesisとは異なり、彼らのコレクションを簡単にミラーできるようにしていないため、広範な保存が妨げられています。これは、彼らがコレクションへのアクセスに対して料金を請求しているため、彼らのビジネスモデルにとって重要です（1日に10冊以上）。 違法な書籍コレクションへの一括アクセスに対して料金を請求することについて道徳的な判断はしません。Z-ライブラリが知識へのアクセスを拡大し、より多くの書籍を調達することに成功したことは疑いの余地がありません。私たちは単に私たちの役割を果たしています：このプライベートコレクションの長期保存を確保することです。 私たちは、トレントをダウンロードしてシードすることで、人類の知識を保存し解放する手助けをしていただきたいと思っています。データがどのように整理されているかについての詳細は、プロジェクトページをご覧ください。 また、次にどのコレクションをミラーするか、そしてそれをどのように行うかについてのアイデアを提供していただきたいと思っています。共に多くを成し遂げることができます。これは無数の他の貢献の中の小さな貢献に過ぎません。あなたがしているすべてのことに感謝します。 海賊図書館ミラーの紹介：Libgenにない7TBの本を保存 人類の文書遺産の約10%%が、恒久的に保存された状態にあります <strong>Google。</strong> 結局のところ、彼らはGoogle Booksのためにこの研究を行いました。しかし、彼らのmetadataは一括でアクセスできず、スクレイピングも難しいです。 <strong>さまざまな個別の図書館システムとアーカイブ。</strong> 上記のいずれにもインデックスされておらず、集約されていない図書館やアーカイブがあります。これはしばしば資金不足のため、または他の理由でOpen Library、OCLC、Googleなどとデータを共有したくないためです。これらの多くはインターネットを通じてアクセス可能なデジタル記録を持っており、しばしばあまり保護されていません。したがって、助けたいと思っていて、奇妙な図書館システムについて学ぶ楽しみを持ちたい場合、これらは素晴らしい出発点です。 <strong>ISBNdb。</strong> これはこのブログ投稿のトピックです。ISBNdbはさまざまなウェブサイトから書籍metadataをスクレイピングし、特に価格データを収集し、それを書籍販売者に販売しています。これにより、彼らは市場の他の部分と一致するように書籍の価格を設定できます。ISBNは現在かなり普遍的であるため、彼らは事実上「すべての本のためのウェブページ」を構築しました。 <strong>Open Library。</strong> 前述の通り、これが彼らの全体の使命です。彼らは協力している図書館や国立アーカイブから大量の図書館データを収集し続けています。また、ボランティアの司書や技術チームが記録の重複を排除し、さまざまなmetadataでタグ付けしようとしています。何よりも、彼らのデータセットは完全にオープンです。単に<a %(openlibrary)s>ダウンロード</a>できます。 <strong>WorldCat。</strong> これは非営利のOCLCが運営するウェブサイトで、図書館管理システムを販売しています。彼らは多くの図書館からの書籍metadataを集約し、WorldCatウェブサイトを通じて提供しています。しかし、彼らはこのデータを販売しているため、一括ダウンロードはできません。特定の図書館と協力して、より限定的な一括データセットをダウンロード可能にしています。 1. 「永遠」の合理的な定義による。 ;) 2. もちろん、人類の書かれた遺産は本だけではありません。特に現代では。この投稿と最近のリリースのために本に焦点を当てていますが、私たちの興味はさらに広がっています。 3. アーロン・シュワルツについてはもっと多くのことが言えますが、彼がこの物語で重要な役割を果たしているため、簡単に触れておきたいと思います。時間が経つにつれて、彼の名前を初めて知る人が増えるかもしれませんし、その後、自分で深く掘り下げることができるでしょう。 <strong>物理的なコピー。</strong> これは同じ資料の複製に過ぎないので、あまり役に立ちません。人々が本に書き込むすべての注釈を保存できたら素晴らしいでしょう。フェルマーの有名な「余白の落書き」のように。しかし、それはアーカイブ担当者の夢のままでしょう。 <strong>「版」。</strong> ここでは、本のすべてのユニークなバージョンを数えます。異なるカバーや異なる序文など、何かが異なれば、それは異なる版と見なされます。 <strong>ファイル。</strong> Library Genesis、Sci-Hub、Z-ライブラリのようなシャドウライブラリを扱う際には、追加の考慮事項があります。同じ版の複数のスキャンが存在することがあります。また、OCRを使用してテキストをスキャンしたり、斜めにスキャンされたページを修正したりして、既存のファイルをより良いバージョンにすることができます。これらのファイルを1つの版としてカウントするには、良いmetadataが必要であり、または文書の類似性測定を使用して重複を排除する必要があります。 <strong>「作品」。</strong> 例えば、「ハリー・ポッターと秘密の部屋」という論理的な概念で、異なる翻訳や再版など、すべてのバージョンを含むものです。これはある意味で便利な定義ですが、何が含まれるかの線引きが難しいことがあります。例えば、異なる翻訳は保存したいかもしれませんが、わずかな違いしかない再版はそれほど重要ではないかもしれません。 - アンナとチーム (<a %(reddit)s>Reddit</a>) 海賊図書館ミラー（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）では、世界中のすべての本を取り、永遠に保存することを目指しています。<sup>1</sup> Z-ライブラリのトレントと、元のLibrary Genesisのトレントの間で、11,783,153のファイルがあります。しかし、それは実際にはどれくらいの量なのでしょうか？これらのファイルを適切に重複排除した場合、世界中のすべての本の何パーセントを保存したことになるのでしょうか？私たちは本当にこのようなものを持ちたいと思っています： まずは大まかな数字から始めましょう： Z-ライブラリ/LibgenとOpen Libraryの両方には、ユニークなISBNよりも多くの本があります。これは、多くの本にISBNがないことを意味するのでしょうか、それともISBNのmetadataが単に欠けているのでしょうか？この質問には、他の属性（タイトル、著者、出版社など）に基づく自動マッチングの組み合わせ、より多くのデータソースの取り込み、実際の本のスキャンからのISBNの抽出（Z-ライブラリ/Libgenの場合）でおそらく答えることができます。 それらのISBNのうち、どれだけがユニークなのでしょうか？これはベン図で最もよく示されます： 正確に言うと： 私たちは、重複が非常に少ないことに驚きました！ISBNdbには、Z-ライブラリやOpen Libraryのどちらにも表示されない大量のISBNがあり、他の2つについても同様です（小さいながらも依然としてかなりの程度）。これは多くの新しい疑問を引き起こします。ISBNでタグ付けされていない本にタグを付けるために自動マッチングがどれだけ役立つでしょうか？多くのマッチがあり、それによって重複が増えるでしょうか？また、4番目または5番目のデータセットを導入した場合、どれだけの重複が見られるでしょうか？ これにより、出発点が得られました。Z-ライブラリのデータセットに含まれていないISBNや、タイトル/著者フィールドとも一致しないものをすべて確認できます。これにより、世界中のすべての本を保存する手がかりが得られます。まずはインターネットからスキャンを収集し、その後、実際に本をスキャンするために外に出ることです。後者はクラウドファンディングで資金を集めたり、特定の本をデジタル化したい人々からの「報奨金」で推進されたりする可能性もあります。これらはまた別の機会にお話しします。 これらのいずれかに協力したい場合—さらなる分析、metadataのスクレイピング、より多くの本の発見、本のOCR、他の分野（例：論文、オーディオブック、映画、テレビ番組、雑誌）での実施、またはML/大規模言語モデルのトレーニング用にデータを利用可能にすること—私に連絡してください (<a %(reddit)s>Reddit</a>)。 データ分析に特に興味がある場合、私たちはデータセットとスクリプトをより使いやすい形式で提供する作業を進めています。ノートブックをフォークして、すぐに試してみることができれば素晴らしいです。 最後に、この作業を支援したい場合は、寄付を検討してください。これは完全にボランティアで運営されており、あなたの貢献が大きな違いを生みます。少しでも助けになります。現在、暗号通貨での寄付を受け付けています。Anna’s Archiveの寄付ページをご覧ください。 パーセンテージを得るには、分母が必要です：これまでに出版されたすべての本の総数です。<sup>2</sup> Google Booksの終焉前に、このプロジェクトのエンジニアであるレオニード・タイチャーが<a %(booksearch_blogspot)s>この数を推定しようとしました</a>。彼は、舌を巻いて、129,864,880（「少なくとも日曜日まで」）という数を出しました。彼は、世界中のすべての本の統一データベースを構築することでこの数を推定しました。これには、さまざまなデータセットをまとめ、それらをさまざまな方法で統合しました。 ちょっとした余談ですが、世界中のすべての本をカタログ化しようとしたもう一人の人物がいます：故デジタル活動家でRedditの共同創設者であるアーロン・シュワルツです。<sup>3</sup> 彼は<a %(youtube)s>Open Libraryを始めました</a>、目標は「これまでに出版されたすべての本のための1つのウェブページ」であり、多くの異なるソースからのデータを組み合わせました。彼は学術論文を大量にダウンロードしたことで起訴され、最終的には自殺に至るという代償を払いました。言うまでもなく、これが私たちのグループが仮名で活動している理由の一つであり、非常に慎重に行動している理由です。Open Libraryは、インターネットアーカイブの人々によって、アーロンの遺産を引き継ぎ、英雄的に運営されています。この投稿の後半でこれに戻ります。 Googleのブログ投稿で、タイチャーはこの数を推定する際のいくつかの課題を説明しています。まず、何が本を構成するのか？いくつかの可能な定義があります： 「版」は「本」が何であるかの最も実用的な定義のようです。便利なことに、この定義はユニークなISBN番号を割り当てるためにも使用されます。ISBN、または国際標準図書番号は、国際的な商取引で一般的に使用されており、国際バーコードシステム（「国際商品番号」）と統合されています。書店で本を販売したい場合、バーコードが必要なので、ISBNを取得します。 Taycherのブログ投稿では、ISBNは便利ですが、普遍的ではないと述べています。なぜなら、1970年代半ばにしか本格的に採用されておらず、世界中で採用されているわけではないからです。それでも、ISBNはおそらく本の版の最も広く使用されている識別子であり、私たちの最良の出発点です。世界中のすべてのISBNを見つけることができれば、まだ保存が必要な本のリストを得ることができます。 では、データはどこから取得するのでしょうか？世界中のすべての本のリストをまとめようとしている既存の取り組みがいくつかあります。 この投稿では、私たちの以前のZ-ライブラリのリリースと比較して小さなリリースを発表できることを嬉しく思います。私たちはISBNdbの大部分をスクレイピングし、海賊図書館ミラーのウェブサイトでトレントとしてデータを利用可能にしました（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動しました。ここでは直接リンクしませんが、検索してください）。これらは約3090万件のレコードです（<a %(jsonlines)s>JSON Lines</a>として20GB、圧縮して4.4GB）。彼らのウェブサイトでは実際には3260万件のレコードがあると主張しているので、何かを見逃したか、<em>彼ら</em>が何か間違っている可能性があります。いずれにせよ、今のところ私たちがどのようにそれを行ったかを正確に共有することはありません — それは読者への課題として残します。 ;-) 私たちが共有するのは、世界中の本の数を推定するための予備的な分析です。私たちは3つのデータセットを調べました：この新しいISBNdbデータセット、Z-ライブラリシャドウライブラリからスクレイピングしたmetadataの元のリリース（Library Genesisを含む）、およびOpen Libraryのデータダンプです。 ISBNdbダンプ、またはどれだけの本が永遠に保存されるのか？ シャドウライブラリのファイルを適切に重複排除した場合、世界中のすべての本の何パーセントを保存したことになるのでしょうか？ 人類史上最大の真にオープンな図書館、<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に関する更新情報。 <em>WorldCatのリデザイン</em> データ <strong>フォーマットは？</strong> <a %(blog)s>アンナのアーカイブコンテナ（AAC）</a>、これは本質的に<a %(jsonlines)s>JSON Lines</a>を<a %(zstd)s>Zstandard</a>で圧縮し、いくつかの標準化されたセマンティクスを加えたものです。これらのコンテナは、私たちが展開したさまざまなスクレイピングに基づいて、さまざまなタイプのレコードをラップしています。 1年前、私たちはこの質問に答えるために<a %(blog)s>取り組み始めました</a>：<strong>シャドウライブラリによって永久に保存された本の割合はどれくらいですか？</strong> データの基本情報を見てみましょう： 本が<a %(wikipedia_library_genesis)s>Library Genesis</a>のようなオープンデータのシャドウライブラリ、そして今では<a %(wikipedia_annas_archive)s>Annaのアーカイブ</a>に入ると、それは世界中でミラーリングされ（トレントを通じて）、実質的に永遠に保存されます。 保存された本の割合を答えるには、分母を知る必要があります。つまり、合計で何冊の本が存在するのか？理想的には、単なる数字ではなく、実際のメタデータを持っていることです。そうすれば、シャドウライブラリと照合するだけでなく、<strong>残りの本を保存するためのTODOリストを作成することができます！</strong> さらには、このTODOリストを進めるためのクラウドソースの取り組みを夢見ることもできるかもしれません。 私たちは<a %(wikipedia_isbndb_com)s>ISBNdb</a>をスクレイピングし、<a %(openlibrary)s>Open Library dataset</a>をダウンロードしましたが、結果は満足のいくものではありませんでした。主な問題は、ISBNの重複があまりなかったことです。<a %(blog)s>私たちのブログ記事</a>のこのベン図をご覧ください。 私たちは、ISBNdbとOpen Libraryの間にほとんど重複がないことに非常に驚きました。どちらもウェブスクレイピングや図書館の記録など、さまざまなソースからデータを自由に取り入れています。もし両者が外にあるほとんどのISBNを見つけるのが得意であれば、それらの円は確実に大きく重なるか、一方が他方の部分集合であるはずです。これにより、これらの円の完全に外にある本がどれだけあるのか疑問に思いました。私たちはより大きなデータベースが必要です。 そこで私たちは、世界最大の書籍データベースである<a %(wikipedia_worldcat)s>WorldCat</a>に目を向けました。これは非営利団体<a %(wikipedia_oclc)s>OCLC</a>による独自のデータベースで、世界中の図書館からmetadataレコードを集約し、それに対して図書館に完全なデータセットへのアクセスを提供し、エンドユーザーの検索結果に表示されるようにしています。 OCLCは非営利団体ですが、そのビジネスモデルはデータベースを保護することを必要としています。さて、OCLCの皆さん、申し訳ありませんが、私たちはすべてを公開します。 :-) 過去1年間、私たちはWorldCatのすべてのレコードを細心の注意を払ってスクレイピングしてきました。最初は幸運に恵まれました。WorldCatはちょうどウェブサイトの完全なリニューアルを開始したところでした（2022年8月）。これにはバックエンドシステムの大幅なオーバーホールが含まれており、多くのセキュリティの欠陥が導入されました。私たちはすぐにこの機会を捉え、数日で数億ものレコードをスクレイピングすることができました。 その後、セキュリティの欠陥は一つずつゆっくりと修正され、最後に見つけたものが約1か月前に修正されました。その時点で私たちはほとんどすべてのレコードを持っており、わずかに高品質なレコードを求めていただけでした。そこで、リリースする時が来たと感じました！ 1.3B WorldCat スクレイピング <em><strong>要約:</strong> Annaのアーカイブは、保存が必要な本のTODOリストを作成するために、WorldCat（世界最大の図書館メタデータコレクション）をすべてスクレイピングしました。</em> WorldCat 警告：このブログ投稿は廃止されました。IPFSはまだ本格的に使用する準備ができていないと判断しました。可能な場合はアンナのアーカイブからIPFS上のファイルへのリンクを提供しますが、もはや自分たちでホストすることはなく、他の人がIPFSを使用してミラーリングすることも推奨しません。コレクションの保存を手伝いたい場合は、トレントページをご覧ください。 IPFSでZ-ライブラリをシードするのを手伝ってください パートナーサーバーからダウンロード SciDB 外部借入 外部借入(印刷不可) 外部ダウンロード メタデータを検索する トレントに含まれています 戻る  (+%(num)s ボーナス) 未払い 支払い済み キャンセル済み 期限切れ アンナの承認待ち 無効 テキストは英語で以下に続きます。 移動 リセット 進む 最後 Libgen forumsでEメールが動作しない場合、わたしたちは<a %(a_mail)s>Proton Mail</a>(無料)を推奨します。あなたのアカウントを有効にするために<a %(a_manual)s>手動リクエスト</a>を行うことも可能です。 (<a %(a_browser)s>ブラウザの認証</a>が必要な場合がございます。— ダウンロード無制限！) 高速な内部のサーバー#%(number)s （おすすめ） （少し速いが待機リストあり） (ブラウザの認証は不要) （ブラウザの確認や待機リストなし） （待機リストなしだが非常に遅い場合あり） 低速な内部のサーバー#%(number)s オーディオブック 漫画 本 (フィクション) 本 (ノンフィクション) 本 (不明) 学術雑誌 雑誌 音源 その他 標準書式 すべてのページをPDFに変換できませんでした Libgen.liで壊れたと見なされました libgen.li では見ることができません Libgen,rs フィクションでは見ることができません Libgen.rs のノンフィクションでは見ることができません このファイルでexiftoolの実行に失敗しました Z-Libraryで「不良ファイル」としてマーク Z-Libraryから消えています Z-Libraryで「スパム」としてマーク ファイルを開けません(例 : ファイルの破損、DRM) 著作権の主張 ダウンロードに関する問題が発生しました。(例 : 接続ができない、回線速度が遅い、エラーメッセージ) 正しくないメタデータ (例: タイトル、説明、カバー画像) その他 低品質のデータ(例：フォーマットの問題、スキャン品質、ページが欠けているなど) スパムファイルは削除されます。(例：広告、公序良俗に反するもの) %(amount)s (%(amount_usd)s) 合計%(amount)s %(amount)s (%(amount_usd)s) 合計 華麗なる本の虫 幸運の司書 まばゆき情報保持者 驚異のアーカイビスト 追加ダウンロード Cerlalc チェコのメタデータ DuXiu 读秀 EBSCOhost eBook Index Googleブックス Goodreads HathiTrust IA IA コントロールデジタルレンディング ISBNdb ISBN GRP Libgen.li 「scimag」を除く Libgen.rs ノンフィクションとフィクション Libby MagzDB Nexus/STC OCLC (WorldCat(ワールドキャット) OpenLibrary ロシア国立図書館 Sci-Hub Libgen.li「scimag」経由 Sci-Hub / Libgen “scimag” Trantor AAへのアップロード Z-Library Z-Library 中文 タイトル、著者、DOI、ISBN、MD5… 検索 著者 説明とメタデータのコメント 版 元のファイル名 出版社 （特定の検索フィールド） タイトル 出版年 技術詳細 (英語) このコインの最小値は他のコインよりも高くなります。別の期間、又別のコインを選択してください。 リクエストを完了できませんでした。数分後にもう一度お試しください。引き続きこのエラーが起きる場合は%(email)sにスクリーンショットを添付しご連絡ください。 不明なエラーが発生しました。%(email)s にスクリーンショットとともに連絡をください。 支払い処理にエラーが発生しました。しばらく待ってから再度お試しください。24時間以上問題が続く場合は、スクリーンショットを添えて%(email)sまでご連絡ください。 世界最大のコミックの影の図書館を<a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">支援</a>するための募金活動を行っております。貴方の協力に感謝いたします！<a href="/donate">寄付</a>もし寄付ができなければ、あなたの友達に伝えることや、<a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>、<a href="https://t.me/annasarchiveorg">Telegram</a>で私達をフォローすることも考えていただけると幸いです。 <a %(a_request)s>本のリクエスト</a>や小さな（<10k）<a %(a_upload)s>アップロード</a>のためにメールを送らないでください。 アンナのアーカイブ DMCA/著作権主張 連絡を取る Reddit ミラーサイト SLUM (%(unaffiliated)s) 非提携 アンナのアーカイブはあなたの助けを必要としています！ 今寄付すると、高速ダウンロードの回数が<strong>2倍</strong>になります。 私たちを潰そうとする者は多いですが、決して屈しません。 今月寄付すると、速いダウンロードの数が<strong>倍</strong>になります。 今月末まで有効です。 人類の知識を保存する: 素晴らしいホリデーギフトです! メンバーシップはそれに応じて延長されます。 パートナーサーバーはホスティングの閉鎖により利用できません。まもなく再開される予定です。 Anna’s Archive の耐障害性を高めるために、ミラーサイトの運用に協力してくださるボランティアを募集しています。 %(method_name)s という新しい寄付の方法をご用意しました。%(donate_link_open_tag)s寄付</a>を検討していただけるとありがたいです。 - このウェブサイトを運営するのは決して安くはありません。ありがとうございます。 友達を紹介すると、あなたと友達の両方に%(percentage)s%%のボーナス高速ダウンロードがもらえます！ 愛する人を驚かせて、メンバーシップ付きのアカウントをプレゼントしましょう。 完璧なバレンタインギフト！ 詳細を確認… アカウント アクティビティ 高度な アンナのブログ↗ アンナのソフトウェア↗ ベータ コードエクスプローラー データセット 寄付 ダウンロード済みのファイル よくある質問 ホーム メタデータの修正 LLMデータ ログイン/登録 私の寄付 公開プロフィール 検索 セキュリティ Torrents 翻訳する ↗ ボランティア参加とバグ報奨金制度 最近のダウンロード： 📚 世界最大のオープンソース・オープンデータの図書館。 ⭐️ Z-Library、Library Genesis、Sci-Hubなど。 📈 %(book_any)s冊の本、%(journal_article)sもの論文、%(book_comic)s冊の漫画、%(magazine)s冊の雑誌が永久に保存。  と  その他多数 DuXiu Internet Archive の貸出図書館 LibGen 📚人類史上最も巨大な開かれた図書館オープンライブラリ。 📈&nbsp;%(book_count)s&nbsp;冊の本、 %(paper_count)s&nbsp;の論文 — 永遠に保存されます。 ⭐️%(libraries)sのミラーを提供しています。 私たちは%(scraped)sをスクレイピングし、オープンソース化しています。 私たちのコードとデータはすべてオープンソースです。 OpenLib Sci-Hub 、  📚 世界で最も大きなオープンソース・オープンデータの図書館。<br> ⭐️Scihub、 Libgen、 Zlib、その他多数のミラー。 Z-Lib アンナのアーカイブ 無効なリクエストです。訪問 [%(websites)s]. 世界最大のオープンソース、オープンデータの図書館。Sci-Hub,Genesis,Z-Libraryなどなど。 アンナのアーカイブを検索 アンナのアーカイブ 再試行するにはリフレッシュしてください。問題が数時間続く場合は<a %(a_contact)s>お問い合わせ</a>ください。 🔥 このページの読み込みに問題が発生しました <li>1.私たちの<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>、<a href="https://t.me/annasarchiveorg">Telegram</a>をフォローしてください。</li><li>2.Twitter、Reddit、Tiktok、Instagram、お近くのカフェや図書館など、あなたの行く先々でアンナのアーカイブのことを広めてください！私たちのコードやデータはすべてオープンソースなので、もし閉鎖されても、別の場所ですぐに再開できます。</li><li>3.可能であれば<a href="/donate">寄付</a>してください。</li><li>4.<a href="https://translate.annas-software.org/">翻訳</a>を手伝ってください。</li><li>5.もしあなたがソフトウェアエンジニアであれば、私たちの<a href="https://annas-software.org/">オープンソース</a>に貢献したり、<a href="/datasets">トレント</a>でシードすることを検討してください。</li> 10. あなたの言語でAnna’s ArchiveのWikipediaページを作成または維持するのを手伝ってください。 11. 控えめな小さな広告を掲載したいと考えています。Anna’s Archiveに広告を掲載したい場合は、お知らせください。 6. あなたがセキュリティ研究者であれば、攻撃と防御の両方であなたのスキルを活用できます。<a %(a_security)s>セキュリティ</a>ページをご覧ください。 7. 匿名の商人向けの支払いの専門家を探しています。寄付のためのより便利な方法を追加するのを手伝っていただけませんか？PayPal、WeChat、ギフトカードなど。知り合いがいれば、ぜひご連絡ください。 8. 私たちは常にサーバー容量の拡大を求めています。 9. ファイルの問題を報告したり、コメントを残したり、このウェブサイト上でリストを作成することで支援できます。また、<a %(a_upload)s>本をもっとアップロード</a>したり、既存の本のファイル問題やフォーマットを修正することでも支援できます。 ボランティア活動に関する詳細情報は、<a %(a_volunteering)s>ボランティア & バウンティ</a>ページをご覧ください。 情報の自由な流れ、そして知識と文化の保存を強く信じています。この検索エンジンによって、巨人の肩の上に立つことになります。さまざまな影の図書館を作ってきた人々の努力に深く敬意を表し、この検索エンジンがその範囲を広げることを望んでいます。 進捗状況を知るには、アンナを<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>、<a href="https://t.me/annasarchiveorg">Telegram</a>でフォローしてください。ご質問やご意見は、アンナの%(email)sまでご連絡ください。 アカウントID: %(account_id)s ログアウト ❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。 ✅ログアウトされています。ページを再読み込みしてもう一度ログインしてください。 高速ダウンロードは過去24時間で<strong>%(used)s / %(total)s</strong>使用されました メンバーシップ：%(until_date)sまで<strong>%(tier_name)s</strong><a %(a_extend)s>(延長)</a> 複数のメンバーシップを組み合わせることができます（24時間ごとの高速ダウンロードが合算されます）。 メンバーシップ：<strong>未加入</strong><a %(a_become)s>(メンバーになる)</a> メンバーシップのより高いティアへのアップグレードをご希望の場合はアンナ(%(email)s)にご連絡ください。 公開プロフィール：%(profile_link)s 秘密キー（他人に教えないでください）：%(secret_key)s 表示 ここに参加してください！ グループに参加するには、<a %(a_tier)s>上位プラン</a>にアップグレードしてください。 限定Telegramグループ: %(link)s アカウント どのダウンロードですか？ ログイン キーを失くさないでください！ 誤った秘密鍵です。キーを確認してもう一度試すか、以下より新規アカウントを作成してください。 秘密鍵 秘密鍵を入力してログイン： 古いEメールに基づいたアカウントをお持ちでしたら<a %(a_open)s>こちら</a>にEメールを入力してください。 新しいアカウントを登録 まだアカウントがありませんか？ 登録が完了しました！あなたの秘密鍵は<span %(span_key)s>%(key)s</span>です 注意を払ってこの鍵を管理してください。鍵をなくした場合はあなたのアカウントにアクセスできなくなります。 <li %(li_item)s><strong>ブックマーク</strong>このページをブックマークして鍵を保存することができます。</li><li %(li_item)s><strong>ダウンロード</strong><a %(a_download)s>こちら</a>をクリックして鍵をダウンロードできます。</li><li %(li_item)s><strong>パスワードマネージャー</strong>利便性のために鍵は事前に入力されており、ログイン時にパスワード・マネージャーに保存できます。</li> ログイン/登録 ブラウザ検証 警告: コードに不正なUnicode文字が含まれており、さまざまな状況で正しく動作しない可能性があります。生のバイナリはURLのbase64表現からデコードできます。 説明 ラベル プレフィックス 特定のコードのURL ウェブサイト <q>%(prefix_label)s</q>で始まるコード これらのページをスクレイピングしないでください。代わりに、<a %(a_import)s>生成</a>または<a %(a_download)s>ダウンロード</a>して、ElasticSearchとMariaDBデータベースを使用し、<a %(a_software)s>オープンソースコード</a>を実行することをお勧めします。生データは<a %(a_json_file)s>このような</a>JSONファイルを通じて手動で探索できます。 %(count)s件未満のレコード 汎用URL コードエクスプローラー インデックス プレフィックスごとにタグ付けされたレコードのコードを探索します。「レコード」列は、検索エンジンで見られるプレフィックス付きのコードでタグ付けされたレコードの数を示します（メタデータのみのレコードを含む）。「コード」列は、特定のプレフィックスを持つ実際のコードの数を示します。 既知のコードプレフィックス <q>%(key)s</q> もっと… プレフィックス 「%(prefix_label)s」に一致する%(count)s件のレコード コード レコード 「%%」はコードの値に置き換えられます アンナのアーカイブを検索 コード 特定のコードのURL: 「%(url)s」 このページの生成には時間がかかる場合があるため、Cloudflareのキャプチャが必要です。<a %(a_donate)s>メンバー</a>はキャプチャをスキップできます。 報告された不正行為: より良いバージョン このユーザーを不適切な行動で報告しますか？ ファイルの問題: %(file_issue)s 非表示コメント 返信 不正行為を報告 このユーザーを不正行為で報告しました。 このメールへの著作権主張は無視されます。代わりにフォームをご利用ください。 メールを表示 ご意見やご質問をお待ちしております！ ただし、スパムや無意味なメールが多いため、以下のボックスをチェックして、当サイトへの連絡条件を理解したことを確認してください。 著作権クレームに関する他の連絡方法は自動的に削除されます。 DMCA / 著作権クレームについては<a %(a_copyright)s>このフォーム</a>を使用してください。 連絡用メール Anna’s ArchiveのURL（必須）。1行に1つずつ。同じ版の書籍を示すURLのみを含めてください。複数の書籍や複数の版について申告する場合は、このフォームを複数回提出してください。 複数の書籍や版をまとめて申告する場合は、却下されます。 住所（必須） 資料の明確な説明（必須） Eメール（必須） 資料のURL。1行に1つずつ（必須）。確認を容易にするため、できるだけ多く含めてください（例：Amazon、WorldCat、Google Books、DOI）。 資料のISBN（該当する場合）。1行に1つずつ。著作権侵害を報告する版と完全に一致するもののみを含めてください。 お名前（必須） ❌ 問題が発生しました。ページをリロードして、再度お試しください。 ✅ 著作権侵害の申告を提出していただき、ありがとうございます。できるだけ早く確認いたします。別の申告を行うには、ページをリロードしてください。 <a %(a_openlib)s>Open Library</a>の資料のURL。1行に1つずつ。Open Libraryで資料を検索する時間を取ってください。これにより、申告の確認が容易になります。 電話番号（必須） 声明と署名（必須） 申告を提出 DMCAやその他の著作権侵害の申告がある場合は、このフォームにできるだけ正確に記入してください。問題が発生した場合は、専用のDMCAアドレス：%(email)sまでご連絡ください。このアドレスに送信された申告は処理されませんので、質問のみ受け付けます。申告を提出するには、以下のフォームをご利用ください。 DMCA / 著作権侵害申告フォーム Anna’s Archiveの例レコード アンナのトレント Anna’s Archive Containersフォーマット メタデータをインポートするためのスクリプト このデータセットを<a %(a_archival)s>アーカイブ</a>または<a %(a_llm)s>LLMトレーニング</a>の目的でミラーリングすることに興味がある場合は、お問い合わせください。 最終更新日: %(date)s メイン%(source)sウェブサイト メタデータドキュメント（ほとんどのフィールド） Anna’s Archiveによってミラーリングされたファイル: %(count)s (%(percent)s%%) リソース 総ファイル数: %(count)s 総ファイルサイズ: %(size)s このデータに関する私たちのブログ記事 <a %(duxiu_link)s>Duxiu</a>は、<a %(superstar_link)s>SuperStar Digital Library Group</a>によって作成された大規模なスキャン書籍データベースです。ほとんどが学術書であり、大学や図書館でデジタルで利用できるようにスキャンされています。英語を話す読者のために、<a %(princeton_link)s>プリンストン大学</a>と<a %(uw_link)s>ワシントン大学</a>が良い概要を提供しています。また、背景情報を提供する優れた記事もあります：<a %(article_link)s>「中国の書籍のデジタル化：SuperStar DuXiu Scholar Search Engineのケーススタディ」</a>。 Duxiuの書籍は長い間、中国のインターネット上で海賊版として流通してきました。通常、再販業者によって1ドル未満で販売されています。これらは通常、中国版のGoogle Driveを使用して配布されており、しばしばより多くのストレージスペースを確保するためにハッキングされています。いくつかの技術的な詳細は<a %(link1)s>こちら</a>と<a %(link2)s>こちら</a>で見つけることができます。 これらの書籍は半公開で配布されてきましたが、大量に入手するのは非常に困難です。私たちはこれをTODOリストの上位に置き、フルタイムで数ヶ月間取り組むことにしました。しかし、2023年後半に信じられないほど素晴らしく才能のあるボランティアが私たちに連絡を取り、すでにこの作業をすべて行ったと教えてくれました—多大な費用をかけて。彼らは長期保存の保証を除いて何も期待せずに、全コレクションを私たちと共有してくれました。本当に驚くべきことです。 ボランティアからの詳細情報（生のメモ）： 私たちの<a %(a_href)s>ブログ記事</a>からの抜粋です。 DuXiu 读秀 %(count)s ファイル このデータセットは<a %(a_datasets_openlib)s>Open Libraryデータセット</a>と密接に関連しています。IAの制御デジタル貸出図書館からのすべてのメタデータと多くのファイルのスクレイプが含まれています。更新は<a %(a_aac)s>Anna’s Archive Containers形式</a>でリリースされます。 これらのレコードはOpen Libraryデータセットから直接参照されていますが、Open Libraryにないレコードも含まれています。また、コミュニティメンバーが長年にわたってスクレイプした多数のデータファイルもあります。 コレクションは2つの部分で構成されています。すべてのデータを取得するには両方の部分が必要です（トレントページで取り消されたトレントを除く）。 デジタル貸出図書館 最初のリリースでは、<a %(a_aac)s>Anna’s Archive Containers (AAC) フォーマット</a>を標準化する前のものです。メタデータ（jsonおよびxml形式）、pdf（acsmおよびlcpdfデジタル貸出システムから）、およびカバーサムネイルが含まれています。 AACを使用した増分リリース。2023-01-01以降のタイムスタンプを持つメタデータのみが含まれています。それ以前のものはすでに「ia」によってカバーされています。また、今回はacsmおよび「bookreader」（IAのウェブリーダー）貸出システムからのすべてのpdfファイルも含まれています。名前が正確ではないにもかかわらず、bookreaderファイルをia2_acsmpdf_filesコレクションに追加しています。これらは相互排他的です。 IA制御デジタル貸出 98%%以上のファイルが検索可能です。 私たちの使命は、世界中のすべての本（論文、雑誌なども含む）をアーカイブし、それらを広くアクセス可能にすることです。すべての本は広範にミラーリングされ、冗長性と回復力を確保するべきだと信じています。これが、さまざまなソースからファイルを集めている理由です。いくつかのソースは完全にオープンで、大量にミラーリングできます（例えば、Sci-Hub）。他のソースは閉鎖的で保護的なので、それらの本を「解放」するためにスクレイピングを試みます。さらに他のソースはその中間に位置します。 私たちのすべてのデータは<a %(a_torrents)s>トレント</a>でダウンロードでき、すべてのメタデータはElasticSearchおよびMariaDBデータベースとして<a %(a_anna_software)s>生成</a>または<a %(a_elasticsearch)s>ダウンロード</a>できます。生データは<a %(a_dbrecord)s>こちら</a>のようなJSONファイルを通じて手動で探索できます。 メタデータ ISBNウェブサイト 最終更新日: %(isbn_country_date)s (%(link)s) リソース 国際ISBN機関は、各国のISBN機関に割り当てた範囲を定期的に公開しています。これにより、このISBNがどの国、地域、または言語グループに属するかを特定できます。現在、このデータはPythonライブラリの<a %(a_isbnlib)s>isbnlib</a>を通じて間接的に使用しています。 ISBN国情報 これは、2022年9月中にisbndb.comに多数のリクエストを送信した結果のダンプです。すべてのISBN範囲をカバーしようとしました。約3090万件のレコードがあります。彼らのウェブサイトでは実際には3260万件のレコードがあると主張しているので、何かを見逃したか、または<em>彼ら</em>が何か間違っている可能性があります。 JSONレスポンスはほぼサーバーからの生データです。気付いたデータ品質の問題の一つは、「978-」以外のプレフィックスで始まるISBN-13番号に対しても、「isbn」フィールドが単に最初の3桁を切り取った（およびチェックディジットを再計算した）ISBN-13番号を含んでいることです。これは明らかに間違っていますが、彼らはこのようにしているようなので、変更しませんでした。 もう一つの潜在的な問題は、「isbn13」フィールドに重複があるため、データベースの主キーとして使用できないことです。「isbn13」+「isbn」フィールドを組み合わせると一意であるようです。 リリース1 (2022-10-31) フィクションのトレントは遅れています（ただし、IDが約4-6MのものはZlibのトレントと重複しているためトレント化されていません）。 コミックブックリリースに関する私たちのブログ投稿 Anna’s Archiveのコミックトレント 異なるLibrary Genesisフォークの背景については、<a %(a_libgen_rs)s>Libgen.rs</a>のページを参照してください。 Libgen.liには、Libgen.rsとほぼ同じコンテンツとメタデータが含まれていますが、これに加えてコミック、雑誌、標準文書のコレクションがあります。また、<a %(a_scihub)s>Sci-Hub</a>をメタデータと検索エンジンに統合しており、これを私たちのデータベースで使用しています。 このライブラリのメタデータは<a %(a_libgen_li)s>libgen.liで</a>無料で利用できます。ただし、このサーバーは遅く、接続が切れた場合の再開をサポートしていません。同じファイルは<a %(a_ftp)s>FTPサーバー</a>でも利用可能で、こちらの方が動作が良好です。 ノンフィクションもまた分岐しているようですが、新しいトレントはありません。これは2022年初頭から起こっているようですが、まだ確認はしていません。 Libgen.liの管理者によると、「fiction_rus」（ロシアのフィクション）コレクションは、<a %(a_booktracker)s>booktracker.org</a>から定期的にリリースされるトレントでカバーされるべきであり、特に<a %(a_flibusta)s>flibusta</a>と<a %(a_librusec)s>lib.rus.ec</a>のトレント（私たちは<a %(a_torrents)s>ここ</a>でミラーしていますが、どのトレントがどのファイルに対応しているかはまだ確立していません）。 フィクションコレクションには独自のトレントがあり（<a %(a_href)s>Libgen.rs</a>から分岐）、%(start)sから始まります。 トレントのない特定の範囲（例えば、フィクションの範囲f_3463000からf_4260000）は、おそらくZ-Library（または他の重複）ファイルである可能性が高いですが、これらの範囲でlgli-ユニークなファイルのために重複排除を行い、トレントを作成したいかもしれません。 すべてのコレクションの統計は<a %(a_href)s>libgenのウェブサイト</a>で見つけることができます。 追加コンテンツのほとんどにはトレントが利用可能で、特にコミック、雑誌、標準文書のトレントはアンナのアーカイブとの共同でリリースされています。 「libgen.is」に言及しているトレントファイルは、<a %(a_libgen)s>Libgen.rs</a>のミラーであることを明示しています（「.is」はLibgen.rsが使用する別のドメインです）。 メタデータの使用に役立つリソースは<a %(a_href)s>このページ</a>です。 %(icon)s 彼らの「fiction_rus」コレクション（ロシアのフィクション）は専用のトレントがありませんが、他のトレントでカバーされており、私たちは<a %(fiction_rus)s>ミラー</a>を保持しています。 アンナのアーカイブのロシアのフィクショントレント Anna’s Archiveのフィクショントレント ディスカッションフォーラム メタデータ FTP経由のメタデータ Anna’s Archiveの雑誌トレント メタデータフィールド情報 他のトレントのミラー（およびユニークなフィクションとコミックのトレント） アンナのアーカイブの標準文書トレント Libgen.li アンナのアーカイブによるトレント（ブックカバー） Library Genesisは、すでにトレントを通じてデータを一括で提供していることで知られています。私たちのLibgenコレクションは、彼らが直接リリースしない補助データで構成されており、彼らとのパートナーシップのもとで提供されています。Library Genesisの皆様、ご協力いただきありがとうございます！ ブックカバーリリースに関するブログ このページは「.rs」バージョンについてです。このバージョンは、メタデータと書籍カタログの全内容を一貫して公開することで知られています。書籍コレクションはフィクションとノンフィクションに分かれています。 メタデータの使用に役立つリソースは<a %(a_metadata)s>このページ</a>です（IP範囲をブロックするため、VPNが必要な場合があります）。 2024年3月現在、新しいトレントは<a %(a_href)s>このフォーラムスレッド</a>に投稿されています（IP範囲をブロックするため、VPNが必要な場合があります）。 アンナのアーカイブのフィクショントレント Libgen.rs フィクショントレント Libgen.rs ディスカッションフォーラム Libgen.rs メタデータ Libgen.rs メタデータフィールド情報 Libgen.rs ノンフィクショントレント アンナのアーカイブのノンフィクショントレント フィクション書籍の場合は%(example)s。 この<a %(blog_post)s>最初のリリース</a>はかなり小規模です：Libgen.rsフォークからの約300GBのブックカバー、フィクションとノンフィクションの両方が含まれています。これらはlibgen.rsに表示されるのと同じ方法で整理されています。例えば： ノンフィクション書籍の場合は%(example)s。 Z-Libraryコレクションと同様に、すべてを大きな.tarファイルにまとめました。ファイルを直接提供したい場合は、<a %(a_ratarmount)s>ratarmount</a>を使用してマウントできます。 リリース1 (%(date)s) Library Genesis（または「Libgen」）の異なるフォークの簡単な話は、時間が経つにつれて、Library Genesisに関わる異なる人々が意見の相違を起こし、別々の道を歩んだということです。 この<a %(a_mhut)s>フォーラム投稿</a>によると、Libgen.liは元々「http://free-books.dontexist.com」でホストされていました。 「.fun」バージョンは、元の創設者によって作成されました。新しい、より分散型のバージョンに向けて改良されています。 <a %(a_li)s>「.li」バージョン</a>は、コミックの膨大なコレクションを持っており、他のコンテンツも含まれていますが、トレントを通じて一括ダウンロードすることは（まだ）できません。フィクション書籍の別のトレントコレクションがあり、データベースには<a %(a_scihub)s>Sci-Hub</a>のメタデータが含まれています。 「.rs」バージョンは非常に似たデータを持ち、コレクションを一括トレントで最も一貫してリリースしています。大まかに「フィクション」と「ノンフィクション」のセクションに分かれています。 元の場所は「http://gen.lib.rus.ec」です。 <a %(a_zlib)s>Z-Library</a>もある意味ではLibrary Genesisのフォークですが、彼らはプロジェクトに別の名前を使用しました。 Libgen.rs また、メタデータのみのソースを使用してコレクションを充実させています。これらは、ISBN番号や他のフィールドを使用してファイルと一致させることができます。以下はその概要です。再度、これらのソースの一部は完全にオープンである一方、他のソースはスクレイピングが必要です。 メタデータ検索では、元のレコードを表示することに注意してください。レコードのマージは行いません。 メタデータのみのソース Open Libraryは、世界中のすべての本をカタログ化することを目的としたInternet Archiveのオープンソースプロジェクトです。世界最大級の書籍スキャン作業を行っており、多くの書籍がデジタル貸出可能です。その書籍メタデータカタログは無料でダウンロード可能で、アンナのアーカイブにも含まれています（ただし、現在のところ検索には含まれていません。Open Library IDを明示的に検索した場合を除きます）。 Open Library 重複を除外 最終更新日 ファイル数の割合 AAによってミラーリングされた%% / トレントが利用可能 サイズ ソース 以下は、アンナのアーカイブにあるファイルのソースの簡単な概要です。 シャドウライブラリはしばしば互いにデータを同期するため、ライブラリ間でかなりの重複があります。そのため、数値が合計に一致しないのです。 「Anna’s Archiveによってミラーおよびシードされた」割合は、私たちが自分たちでミラーしているファイルの数を示しています。これらのファイルはトレントを通じて一括でシードされ、パートナーウェブサイトを通じて直接ダウンロード可能にしています。 概要 合計 アンナのアーカイブのトレント Sci-Hubの背景については、その<a %(a_scihub)s>公式ウェブサイト</a>、<a %(a_wikipedia)s>Wikipediaページ</a>、およびこの<a %(a_radiolab)s>ポッドキャストインタビュー</a>を参照してください。 Sci-Hubは<a %(a_reddit)s>2021年から凍結されています</a>。以前も凍結されていましたが、2021年には数百万の論文が追加されました。それでも、限られた数の論文がLibgenの「scimag」コレクションに追加されていますが、新しい大規模なトレントを作成するほどではありません。 Sci-Hubのメタデータは、<a %(a_libgen_li)s>Libgen.li</a>の「scimag」コレクションに基づいて利用しています。また、<a %(a_dois)s>dois-2022-02-12.7z</a>のデータセットも活用しています。 「smarch」トレントは<a %(a_smarch)s>廃止</a>されているため、私たちのトレントリストには含まれていません。 Libgen.liのトレント Libgen.rsのトレント メタデータとトレント Redditでの更新情報 ポッドキャストインタビュー Wikipediaページ Sci-Hub Sci-Hub: 2021年以降凍結; ほとんどがトレントで利用可能 Libgen.li: それ以降の小さな追加</div> 一部のソースライブラリは、トレントを通じてデータを大量に共有することを推奨していますが、他のライブラリはコレクションを容易に共有しません。後者の場合、Anna’s Archiveはコレクションをスクレイピングし、利用可能にしようとします（詳細は<a %(a_torrents)s>トレント</a>ページをご覧ください）。また、ソースライブラリが共有に前向きであるが、リソースが不足している場合もあります。そのような場合、私たちも支援を試みます。 以下は、異なるソースライブラリとどのようにインターフェースしているかの概要です。 ソースライブラリ %(icon)s 中国のインターネット上に散在するさまざまなファイルデータベース；ただし、しばしば有料データベース %(icon)s ほとんどのファイルはプレミアムBaiduYunアカウントを使用しないとアクセスできません；ダウンロード速度が遅いです。 %(icon)s アンナのアーカイブは<a %(duxiu)s>DuXiuファイル</a>のコレクションを管理しています。 %(icon)s 中国のインターネットに散在する様々なメタデータデータベース；ただし、しばしば有料のデータベース %(icon)s 彼らの全コレクションに対して簡単にアクセスできるメタデータダンプは利用できません。 %(icon)s アンナのアーカイブは<a %(duxiu)s>DuXiuメタデータ</a>のコレクションを管理しています。 ファイル %(icon)s ファイルは、さまざまなアクセス制限がある中で、限られた期間のみ借用可能です %(icon)s アンナのアーカイブは<a %(ia)s>IAファイル</a>のコレクションを管理しています %(icon)s 一部のメタデータは<a %(openlib)s>Open Libraryデータベースダンプ</a>を通じて利用可能ですが、それらはIAコレクション全体をカバーしていません %(icon)s 彼らのコレクション全体に対する簡単にアクセスできるメタデータダンプはありません %(icon)s アンナのアーカイブは、<a %(ia)s>IAメタデータ</a>のコレクションを管理しています 最終更新 %(icon)s アンナのアーカイブとLibgen.liは共同で<a %(comics)s>コミック</a>、<a %(magazines)s>雑誌</a>、<a %(standarts)s>標準文書</a>、および<a %(fiction)s>フィクション（Libgen.rsから分岐）</a>のコレクションを管理しています。 %(icon)s ノンフィクショントレントはLibgen.rsと共有されています（<a %(libgenli)s>こちら</a>でミラーされています）。 %(icon)s 四半期ごとの<a %(dbdumps)s>HTTPデータベースダンプ</a> %(icon)s <a %(nonfiction)s>ノンフィクション</a>と<a %(fiction)s>フィクション</a>の自動トレント %(icon)s アンナのアーカイブは<a %(covers)s>ブックカバートレント</a>のコレクションを管理しています %(icon)s 毎日の<a %(dbdumps)s>HTTPデータベースダンプ</a> メタデータ %(icon)s 毎月の<a %(dbdumps)s>データベースダンプ</a> %(icon)s データトレントは<a %(scihub1)s>こちら</a>、<a %(scihub2)s>こちら</a>、および<a %(libgenli)s>こちら</a>で利用可能です %(icon)s 一部の新規ファイルはLibgenの「scimag」に<a %(libgenrs)s>追加中</a>ですが、<a %(libgenli)s>新たなトレントを作成するには不十分です</a>。 %(icon)s Sci-Hubは2021年以降、新しいファイルを凍結しています。 %(icon)s メタデータダンプは<a %(scihub1)s>こちら</a>と<a %(scihub2)s>こちら</a>で利用可能であり、<a %(libgenli)s>Libgen.liデータベース</a>の一部としても利用可能です（私たちが使用しています） ソース %(icon)s さまざまな小規模または一時的なソース。私たちは他のシャドウライブラリに最初にアップロードすることを奨励していますが、時には他の人が整理するには大きすぎるコレクションを持っている人もいますが、それほど大きくないため独自のカテゴリを設けるには至りません。 %(icon)s 直接大量には利用できず、スクレイピングから保護されています %(icon)s アンナのアーカイブは<a %(worldcat)s>OCLC (WorldCat)メタデータ</a>のコレクションを管理しています。 %(icon)s アンナのアーカイブとZ-Libraryは、<a %(metadata)s>Z-Libraryメタデータ</a>と<a %(files)s>Z-Libraryファイル</a>のコレクションを共同で管理しています データセット 上記のすべてのソースを1つの統合データベースに結合し、このウェブサイトで使用しています。この統合データベースは直接利用できませんが、Anna’s Archiveは完全にオープンソースであるため、比較的簡単に<a %(a_generated)s>生成</a>または<a %(a_downloaded)s>ダウンロード</a>してElasticSearchおよびMariaDBデータベースとして利用できます。そのページのスクリプトは、上記のソースから必要なメタデータを自動的にダウンロードします。 これらのスクリプトをローカルで実行する前にデータを探索したい場合は、JSONファイルを確認できます。これらのファイルは他のJSONファイルにリンクしています。<a %(a_json)s>このファイル</a>が良い出発点です。 統合データベース Anna’s Archiveによるトレント 閲覧 検索 さまざまな小規模または一時的なソース。私たちは他のシャドウライブラリに最初にアップロードすることを奨励していますが、時には他の人が整理するには大きすぎるコレクションを持っている人もいますが、それほど大きくないため独自のカテゴリを設けるには至りません。 <a %(a1)s>データセットページ</a>からの概要。 <a %(a_href)s>aaaaarg.fail</a>から。かなり完全なもののようです。ボランティア「cgiym」から提供されました。 <a %(a_href)s><q>ACM Digital Library 2020</q></a>のトレントから。既存の論文コレクションとかなり重複していますが、MD5の一致はほとんどないため、完全に保持することにしました。 ボランティアの<q>j</q>による<q>iRead eBooks</q>（発音的には<q>ai rit i-books</q>; airitibooks.com）のスクレイピング。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>airitibooks</q>メタデータに対応。 <a %(a1)s><q>アレクサンドリア図書館</q></a>のコレクションから。元のソースから一部、the-eye.euから一部、他のミラーから一部。 プライベートな書籍トレントサイト<a %(a_href)s>Bibliotik</a>（「Bib」とも呼ばれる）から、書籍は名前ごとにトレント（A.torrent、B.torrent）にまとめられ、the-eye.euを通じて配布されました。 ボランティア「bpb9v」から提供されました。<a %(a_href)s>CADAL</a>に関する詳細は、<a %(a_duxiu)s>DuXiuデータセットページ</a>のノートをご覧ください。 さらにボランティア「bpb9v」から、主にDuXiuファイル、および「WenQu」と「SuperStar_Journals」（SuperStarはDuXiuの背後にある会社）のフォルダー。 ボランティア「cgiym」から、中国のさまざまなソースからのテキスト（サブディレクトリとして表現）、<a %(a_href)s>China Machine Press</a>（主要な中国の出版社）を含む。 ボランティア「cgiym」からの非中国語のコレクション（サブディレクトリとして表現）。 ボランティアの<q>cm</q>による中国建築に関する書籍のスクレイピング：<q>出版社のネットワーク脆弱性を利用して入手しましたが、その抜け穴はすでに閉じられています</q>。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>chinese_architecture</q>メタデータに対応。 学術出版社<a %(a_href)s>De Gruyter</a>の書籍、大規模なトレントから収集。 書籍やその他の書かれた作品に焦点を当てたポーランドのファイル共有サイト<a %(a_href)s>docer.pl</a>のスクレイプ。2023年後半にボランティア「p」によってスクレイプされました。元のウェブサイトからの良いメタデータはありません（ファイル拡張子さえもありません）が、書籍のようなファイルをフィルタリングし、ファイル自体からメタデータを抽出することができました。 DuXiuのepub、DuXiuから直接、ボランティア「w」によって収集。最近のDuXiuの書籍のみが直接電子書籍として利用可能なので、これらのほとんどは最近のものに違いありません。 ボランティア「m」からの残りのDuXiuファイル、DuXiuの独自のPDG形式ではないもの（主要な<a %(a_href)s>DuXiuデータセット</a>）。多くの元のソースから収集されましたが、残念ながらそのソースをファイルパスに保存していません。 <span></span> <span></span> <span></span> ボランティアの<q>do no harm</q>によるエロティック書籍のスクレイピング。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>hentai</q>メタデータに対応。 <span></span> <span></span> ボランティア「t」によって日本のマンガ出版社からスクレイプされたコレクション。 <a %(a_href)s>龍泉の選ばれた司法アーカイブ</a>、ボランティア「c」によって提供されました。 <a %(a_href)s>magzdb.org</a>のスクレイプ、Library Genesisの同盟者（libgen.rsのホームページにリンクされています）が直接ファイルを提供したくなかったため。2023年後半にボランティア「p」によって取得されました。 <span></span> さまざまな小さなアップロード、単独ではサブコレクションとしては小さすぎるため、ディレクトリとしてまとめられています。<q>oo42hcksBxZYAOjqwGWu</q> ディレクトリは、<a %(a1)s><q>Other metadata scrapes</q></a> 内の <q>czech_oo42hcks</q> メタデータに対応します。 ロシアのファイル共有サイトAvaxHomeからの電子書籍。 新聞と雑誌のアーカイブ。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>newsarch_magz</q>メタデータに対応。 <a %(a1)s>Philosophy Documentation Center</a>のスクレイピング。 ボランティア「o」のコレクション、オリジナルリリース（「シーン」）ウェブサイトから直接収集されたポーランドの書籍。 ボランティア「cgiym」と「woz9ts」によって収集された<a %(a_href)s>shuge.org</a>の統合コレクション。 <span></span> <a %(a_href)s>「トランター帝国図書館」</a>（架空の図書館に由来する名称）は、2022年にボランティア「t」によってスクレイピングされました。これは、<a %(a1)s><q>Other metadata scrapes</q></a> 内の <q>trantor</q> メタデータに対応しています。 <span></span> <span></span> <span></span> ボランティア「woz9ts」からのサブサブコレクション（ディレクトリとして表現）：<a %(a_program_think)s>program-think</a>、<a %(a_haodoo)s>haodoo</a>、<a %(a_skqs)s>skqs</a>（台湾の<a %(a_sikuquanshu)s>Dizhi(迪志)</a>による）、mebook（mebook.cc、私の小さな書房、woz9ts：「このサイトは主に高品質の電子書籍ファイルの共有に焦点を当てており、その一部はオーナー自身が組版したものです。オーナーは2019年に<a %(a_arrested)s>逮捕</a>され、彼が共有したファイルのコレクションが作成されました。」）。 ボランティア「woz9ts」からの残りのDuXiuファイルで、DuXiu独自のPDG形式ではないもの（まだPDFに変換されていない）。 「アップロード」コレクションは小さなサブコレクションに分割されており、AACIDやトレント名で示されています。すべてのサブコレクションは最初にメインコレクションと重複排除されましたが、メタデータ「upload_records」JSONファイルには元のファイルへの多くの参照がまだ含まれています。非書籍ファイルもほとんどのサブコレクションから削除されており、通常「upload_records」JSONには記載されていません。 サブコレクションは次のとおりです： ノート サブコレクション 多くのサブコレクション自体がサブサブコレクション（例：異なる元ソースから）で構成されており、「filepath」フィールドにディレクトリとして表現されています。 アンナのアーカイブへのアップロード このデータに関する私たちのブログ投稿 <a %(a_worldcat)s>WorldCat</a>は、非営利団体<a %(a_oclc)s>OCLC</a>による独自のデータベースで、世界中の図書館からメタデータレコードを集約しています。これはおそらく世界最大の図書館メタデータコレクションです。 2023年10月に、OCLC (WorldCat)データベースの包括的なスクレイプを<a %(a_scrape)s>リリース</a>し、<a %(a_aac)s>アンナのアーカイブコンテナ形式</a>で提供しました。 2023年10月、初回リリース: OCLC (WorldCat) アンナのアーカイブによるトレント Anna’s Archiveの例レコード（オリジナルコレクション） Anna’s Archiveの例レコード（「zlib3」コレクション） Anna’s Archiveによるトレント（メタデータ＋コンテンツ） リリース1に関するブログ投稿 リリース2に関するブログ投稿 2022年後半、Z-Libraryの創設者とされる人物が逮捕され、ドメインがアメリカ当局によって押収されました。それ以来、ウェブサイトは徐々に再びオンラインに戻りつつあります。現在誰が運営しているのかは不明です。 2023年2月の更新。 Z-Libraryは<a %(a_href)s>Library Genesis</a>コミュニティにルーツがあり、元々は彼らのデータを使ってブートストラップされました。それ以来、かなりプロフェッショナルになり、よりモダンなインターフェースを持つようになりました。そのため、ウェブサイトの改善のための金銭的な寄付や新しい本の寄付を多く受けることができました。Library Genesisに加えて、大規模なコレクションを蓄積しています。 コレクションは3つの部分で構成されています。最初の2つの部分のオリジナルの説明ページは以下に保存されています。すべてのデータを取得するには、3つの部分すべてが必要です（トレントページで取り消し線が引かれているものを除く）。 %(title)s：最初のリリース。これは「Pirate Library Mirror」（「pilimi」）と呼ばれていたものの最初のリリースでした。 %(title)s：2回目のリリース、今回はすべてのファイルが.tarファイルに包まれています。 %(title)s：<a %(a_href)s>Anna’s Archive Containers (AAC)形式</a>を使用した増分新リリース、現在はZ-Libraryチームとのコラボレーションでリリースされています。 最初のミラーは2021年から2022年にかけて丹念に取得されました。この時点でやや古くなっています：2021年6月のコレクションの状態を反映しています。将来的にはこれを更新する予定です。現在はこの最初のリリースを出すことに集中しています。 Library Genesisはすでにパブリックトレントで保存されており、Z-Libraryにも含まれているため、2022年6月にLibrary Genesisに対して基本的な重複排除を行いました。これにはMD5ハッシュを使用しました。ライブラリには、同じ本の複数のファイル形式など、さらに多くの重複コンテンツが存在する可能性があります。これを正確に検出するのは難しいため、行っていません。重複排除の後、約200万ファイルが残り、合計で7TB未満です。 コレクションは2つの部分で構成されています：メタデータのMySQL “.sql.gz” ダンプと、約50-100GBの72個のトレントファイルです。メタデータには、Z-Libraryウェブサイトで報告されたデータ（タイトル、著者、説明、ファイルタイプ）と、実際のファイルサイズおよび観測されたmd5sumが含まれています。これらは時々一致しないことがあります。Z-Library自体が不正確なメタデータを持っているファイルの範囲があるようです。また、いくつかの孤立したケースでは、誤ってダウンロードしたファイルがあるかもしれませんが、将来的に検出して修正する予定です。 大きなトレントファイルには、Z-Library IDをファイル名として実際の書籍データが含まれています。ファイル拡張子はメタデータダンプを使用して再構築できます。 コレクションはノンフィクションとフィクションのコンテンツの混合であり（Library Genesisのように分離されていません）、品質も大きく異なります。 この最初のリリースは現在完全に利用可能です。トレントファイルは私たちのTorミラーを通じてのみ利用可能であることに注意してください。 リリース1（%(date)s） これは単一の追加トレントファイルです。新しい情報は含まれていませんが、計算に時間がかかるデータが含まれています。そのため、最初から計算するよりもこのトレントをダウンロードする方が速いことが多いです。特に、<a %(a_href)s>ratarmount</a>で使用するためのtarファイルのSQLiteインデックスが含まれています。 リリース2補遺 (%(date)s) 前回のミラーと2022年8月の間にZ-Libraryに追加されたすべての本を取得しました。また、最初の時に見逃した本もいくつかスクレイピングしました。全体として、この新しいコレクションは約24TBです。再度、このコレクションはLibrary Genesisに対して重複排除されています。すでにそのコレクションのトレントが利用可能だからです。 データは最初のリリースと同様に整理されています。メタデータのMySQL “.sql.gz” ダンプがあり、これには最初のリリースのすべてのメタデータも含まれており、それを上書きします。新しい列も追加しました： 前回も言及しましたが、念のために明確にしておきます：「filename」と「md5」はファイルの実際のプロパティであり、「filename_reported」と「md5_reported」はZ-Libraryからスクレイピングしたものです。これらが一致しないことがあるため、両方を含めました。 このリリースでは、照合順序を「utf8mb4_unicode_ci」に変更しました。これにより、古いバージョンのMySQLとも互換性があります。 データファイルは前回と似ていますが、はるかに大きいです。小さなトレントファイルを大量に作成するのは面倒だったためです。「pilimi-zlib2-0-14679999-extra.torrent」には前回のリリースで見逃したすべてのファイルが含まれており、他のトレントはすべて新しいID範囲です。  <strong>更新 %(date)s：</strong> トレントファイルが大きすぎて、トレントクライアントが苦労していました。それらを削除し、新しいトレントをリリースしました。 <strong>更新 %(date)s：</strong> まだファイルが多すぎたため、tarファイルにまとめて再度新しいトレントをリリースしました。 %(key)s：このファイルがLibrary Genesisにすでに含まれているかどうか（ノンフィクションまたはフィクションコレクションにmd5で一致）。 %(key)s：このファイルがどのトレントに含まれているか。 %(key)s：本をダウンロードできなかった場合に設定。 リリース2 (%(date)s) Zlibリリース（オリジナルの説明ページ） Torドメイン メインウェブサイト Z-Libraryスクレイプ Z-Libraryの「中国語」コレクションは、MD5が異なるだけで、私たちのDuXiuコレクションと同じようです。重複を避けるためにこれらのファイルをトレントから除外しますが、検索インデックスには表示されます。 メタデータ あなたは%(profile_link)sからの紹介で%(percentage)s%%個の追加の高速ダウンロードを獲得しました。 これは会員期間全体に適用されます。 寄付する 参加 選択済み 最大%(percentage)s%%の割引 Alipayは世界のクレジット/デビットカードをサポートしています。詳細については<a %(a_alipay)s>Alipayガイド</a>をご覧ください。 クレジット/デビットカードを使用してAmazon.comギフトカードを送信してください。 クレジットカードまたはデビットカードを使って、暗号通貨を購入することができます。 WeChat（Weixin Pay）は世界のクレジット/デビットカードに対応しています。WeChatアプリで、「Me => Services => Wallet => Add a Card」に進んでください。それが表示されない場合は、「Me => Settings => General => Tools => Weixin Pay => Enable」を使用して有効にしてください。 （CoinbaseからEthereumを送信する際に使用） コピーされました！ コピー 最小額 （警告：高い最小額） -%(percentage)s%% 12か月間 1か月間 24か月間 3か月間 48ヶ月 6か月間 96ヶ月 購読(サブスクライブ)期間を選択してください。 <div %(div_monthly_cost)s></div><div %(div_after)s>以降<span %(span_discount)s></span>割引</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12か月間 1か月間 24か月間 3か月間 48ヶ月 6か月間 96ヶ月 %(monthly_cost)s/1か月 お問い合わせ ダイレクトな<strong> SFTP </strong> サーバー エンタープライズレベルの寄付や、 新たな蔵書のかわりに。(例:新たなスキャン、OCRデータセットなど) エキスパートアクセス <strong> 無制限の </strong>高ダウンロード速度 <div %(div_question)s>メンバーシップをアップグレードしたり、複数のメンバーシップを取得することはできますか？</div> <div %(div_question)s>メンバーにならずに寄付することはできますか？</div> もちろんです。このMonero (XMR)アドレスに任意の金額を寄付できます: %(address)s。 <div %(div_question)s>月ごとの範囲は何を意味しますか？</div> すべての割引を適用することで、範囲の下限に到達できます。例えば、1か月以上の期間を選択するなどです。 <div %(div_question)s>メンバーシップは自動で継続されますか？</div>メンバーシップは<strong>決して</strong>自動で継続されません。好きなだけ参加することも、短期間のみ参加することも可能です。 <div %(div_question)s>寄付されたお金は何に使用されますか？</div>その100%%が世界の知識と文化を保存し、アクセスできるようにするために使われています。現在、私たちはそのほとんどをサーバーやストレージ、帯域幅に費やしております。どのチームメンバーにも個人的にお金が行くことは決してありません。 <div %(div_question)s>多額の寄付をすることはできますか？</div> それは素晴らしいことだと思います！数千ドル以上の寄付は、%(email)sに直接ご連絡ください。 <div %(div_question)s>他の支払い方法はありますか？</div>現在はありません。多くの人がこのようなアーカイブの存在を望んでいませんので、慎重にならざるを得ません。もしあなたが他の（より便利な）支払い方法を安全に設定するのを手伝ってくれるなら、%(email)sに連絡してください。 寄付のFAQ <a %(a_donation)s>既存の寄付</a>が進行中です。新しい寄付を行う前にその寄付を終了するかキャンセルしてください。 <a %(a_all_donations)s>寄付履歴</a> $5000 以上の寄付については、直接 %(email)s にご連絡ください。 裕福な個人や団体からの大口寄付を歓迎します。  このページのメンバーシップは「月ごと」ですが、一度限りの寄付です。(毎月の請求は発生しません。)<a %(faq)s>詳しくは、寄付のFAQ</a>をご覧ください。 Anna's Archiveは非営利のオープンソース、オープンデータ・プロジェクトです。あなたが寄付をし、メンバーになることで、私たちの運営と開発がサポートされます。メンバーの皆様へ：私たちの活動を支えてくださりありがとうございます！❤️ 詳しくは、<a %(a_donate)s>寄付に関するよくある質問</a>をご覧ください。 メンバーになるには<a %(a_login)s>ログイン・登録</a>が必要です。ご支援感謝いたします！ 月額 $%(cost)s お支払い時に誤りがあった場合でも、返金はできませんが、できる限り対応させていただきます。 PayPalのアプリまたはウェブサイトで「Crypt」ページを探してください。これは通常「Finances」の下にあります。 PayPalアプリまたはウェブサイトで「ビットコイン」ページに行き、「譲渡」ボタン%(transfer_icon)sを押して「送金」してください。 Alipay Alipay 支付宝 / WeChat 微信 アマゾンギフト券 %(amazon)s ギフトカード 銀行カード 銀行カード（アプリ使用） Binance クレジット/デビット/Apple/Google（BMC） Cash App クレジット/デビットカード クレジット/デビットカード2 クレジット/デビットカード（バックアップ） 暗号通貨%(bitcoin_icon)s カード / PayPal / Venmo PayPal %(bitcoin_icon)s PayPal PayPal（通常） Pix(ブラジルのみ) Revolut (一時利用不可) WeChat お支払いに利用される通貨を選択してください： 寄付にアマゾンギフト券を使用します。 <strong>注意: </strong> このオプションは%(amazon)s用です。他のAmazonウェブサイトを利用したい場合は、上で選択してください。 <strong>重要：</strong>Amazon.comにのみ対応しております。Amazon.co.jp等の他のアマゾンには対応していません。 メッセージ欄には何も変更を加えないでください。 正確な金額を入力：%(amount)s 販売店が認める金額(最低%(minimum)s単位)に四捨五入する必要があることに注意してください。 Alipayアプリを通じて、クレジット/デビットカードを使用して寄付します（設定が非常に簡単です）。 <a %(a_app_store)s>Apple App Store</a>または<a %(a_play_store)s>Google Play Store</a>からAlipayアプリをインストールします。 電話番号を使用して登録します。 それ以上の個人情報は必要ありません。 <span %(style)s>1</span>Alipayアプリをインストール 対応カード: Visa, MasterCard, JCB, Diners Club, Discover。 詳細は<a %(a_alipay)s>Alipayガイド</a>をご覧ください。 <span %(style)s>2</span>銀行カードを追加 Binanceを使用すると、クレジット/デビットカードや銀行口座でビットコインを購入し、それを私たちに寄付してください。これにより、安全かつ匿名性を保ったまま寄付を受け取ることができます。 Binanceはほぼすべての国で利用可能で、銀行やクレジット/デビットカードに対応しています。これが、現在私たちが推奨する主な方法です。これを用いた寄付の方法を学んでいただけることに感謝します。これは、私たちにとって大きな助けとなります。 クレジットカード、デビットカード、Apple Pay、Google Payを使用する場合、「Buy Me a Coffee」（BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>）を使用します。このシステムでは、1つの「コーヒー」は5ドルに相当するため、寄付は5の倍数に丸められます。 寄付にCash Appを使用します。 Cash Appを利用した寄付が簡単です! Cash Appで%(amount)s以下の取引を行う場合、%(fee)sの手数料がかかります。%(amount)s以上では無料です！ 寄付にクレジットカード又はデビットーカードを使用します。 この方法は暗号通貨プロバイダーを経由します。少し戸惑うかもしれないので、他の支払い方法がうまくいかない場合にのみこの方法を使用してください。また、すべての国で機能するわけではありません。 銀行が私たちと取引をしたがらないため、クレジット/デビットカードを直接的にサポートすることはできません。☹ しかし、他の方法を使用してクレジット/デビットカードを使用する手段はいくつかあります。 暗号通貨での寄付にはBTC、ETH、XMR、SOLがご利用いただけます。このオプションは暗号通貨に精通しているユーザのみ使用してください。 BTC、ETH、XMRなどで暗号通貨での寄付が行なえます。 暗号通貨エクスプレスサービス 初めて暗号通貨を使用する場合は、%(options)sを使用してビットコイン（最初に作られ、最も使用されている暗号通貨）を購入して寄付することをお勧めします。 クレジットカードで少額の寄付を行う場合、%(discount)s%%の割引が受けられなくなる可能性がございますので、長期のサブスクリプションをお勧めします。 クレジット/デビットカード、PayPal、Venmoを使用して寄付できます。次のページで選択できます。 Google Pay及びApple Payが使用できる可能性があります。 少額の寄付を行う場合、手数料が高額になる場合がございますので長期のサブスクリプションをお勧めします。 PayPalでの寄付には匿名性を保てるPayPalクリプトをご利用ください。この寄付の方法に時間を割いていただき、ありがとうございました。 寄付にPayPalを使用します。 通常のPayPalアカウントを使って寄付する。 Revolutを使って寄付する。 Revolutをお持ちの場合、これが最も簡単な寄付方法です！ この決済では%(amount)s以下の取引のみ対応しております。異なる期間、決済方法を選択してください。 この決済では%(amount)s以上の取引のみ対応しております。異なる期間、決済方法を選択してください。 Binance Coinbase Kraken お支払い方法を選択してください。 “トレントを採用”：あなたのユーザー名またはメッセージをトレントのファイル名に挿入できます<div %(div_months)s>メンバーシップ12ヶ月ごと</div> あなたのユーザーネームまたは匿名の情報がクレジットに記載されます 新機能への早期アクセス 舞台裏を伝える独占テレグラム 一日あたり%(number)s回の高速ダウンロード 今月寄付していただければ！ <a %(a_api)s>JSON API</a> へのアクセス 人類の知識と文化の保存における伝説的な貢献 これまでの特典と、さらに追加で: <a %(a_refer)s>友達を紹介</a>して<strong>%(percentage)s%%追加ダウンロード</strong>を獲得しましょう。 SciDBの論文を認証無しで<strong>無制限に</strong> アカウントや寄付に関する質問をする際には、アカウントID、スクリーンショット、領収書など、できるだけ多くの情報を添付してください。メールは1〜2週間に一度しか確認しないため、これらの情報が含まれていないと解決が遅れる可能性があります。 <a %(a_refer)s>友達を紹介して、</a>！さらに多くのダウンロード回数をゲット！ 私たちは小さなボランティアチームです。返信には1〜2週間かかる場合があります。 なお、アカウント名や画像が変になる場合があります。ご安心ください！これらのアカウントは、私たちの寄付先によって管理されています。ハッキングされたことはありません。 <span %(span_cost)s></span> <span %(span_label)s></span>を寄付 12か月の"%(tier_name)s" １か月の"%(tier_name)s" 24か月の"%(tier_name)s" ３か月の"%(tier_name)s" 48ヶ月間「%(tier_name)s」 6か月の"%(tier_name)s" 96ヶ月間「%(tier_name)s」 チェックアウト時に寄付をキャンセルできます。 寄付ボタンをクリックして寄付を承認。 <strong>重要な注意:</strong> 暗号通貨の価格は大きく変動することがあり、時には数分で20%%以上も変動することがあります。ただこれは、私たちのような "影の慈善団体 "と協力するために50～60%%の手数料を請求することの多い大多数の支払いプロバイダーで発生する手数料よりかはまだ少ないです。<u>お支払いいただいた金額が記載された領収書をお送りいただければ、（領収書が数時間以上経過していない限り）選択されたメンバーシップの金額をアカウントに加算いたします</u>。私たちをサポートするために、このようなことを我慢していただけることに本当に感謝しております！❤️ ❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。 <span %(span_circle)s>1</span>Paypalでビットコインを購入 <span %(span_circle)s>2</span>ビットコインを私達のアドレスに送金する ✅寄付ページへリダイレクト中… 寄付 お問い合わせの前に、少なくとも<span %(span_hours)s>24時間</span>お待ちいただき、このページを更新してください。 会員登録なしでの寄付（任意の金額）を希望される場合は、Monero (XMR)アドレス: %(address)sをご利用ください。 ギフトカードが送付されたあと数分のうちに、私達のシステムが自動的に認証を行います。うまく動作しない場合は再度送信してください。(<a %(a_instr)s>インストラクション</a>) それでも解決できない場合は、メールでご連絡ください。担当者が手動で確認します（数日かかることがあります）。再送をすでにお試しいただいている場合は、その旨を必ずご記載ください。 例： <a %(a_form)s>Amazon.com公式フォーム</a>を使用して、%(amount)sのギフト券を以下のメールアドレスに送信してください。 フォームの "To "受信者Eメール： アマゾンギフト券 <strong>Amazon.comの公式フォームから送信されたもの</strong>以外のギフト券は認証できません。このフォームを使用しなかった場合でも、私達は返金を行えません。 一度限りの使用。 あなたの専用アカウントです。アカウントの共有はしないでください。 ギフトカードを待機中...(ページをリフレッシュして確認) <a %(a_href)s>QRコード寄付ページ</a>を開いてください。 AlipayアプリでQRコードを読み取るか、ボタンを押してアプリを起動してください。 ページは中国から読み込まれるため、表示に時間がかかる場合があります。少々お待ちください。 <span %(style)s>3</span>寄付を行う（QRコードをスキャンまたはボタンを押す） PayPalでPYUSDコインを購入 Cash Appでビットコイン（BTC）を購入 手数料分を含めて、%(amount)sより少し多め（%(more)s）に購入することをおすすめします。残額は保持されます。 Cash Appの「ビットコイン（BTC）」のページを開いてください。 ビットコインをこちらのアドレスにご送金ください 25ドル（約3,900円）未満の少額寄付には、「Rush」または「Priority」の使用が必要となる場合があります。 「ビットコインを送る」を押して出金を開始します。%(icon)s アイコンを押して、ドルや日本円からBTCに切り替えます。下に送金したいBTCの金額を入力し、「送信」をクリックしてください。操作に迷った場合は、<a %(help_video)s>こちらの動画</a>をご覧ください。 エクスプレスサービスは便利ですが、手数料が高くなります。 より大きな寄付を迅速に行いたい場合で、$5-10の手数料を気にしない場合は、これを取引所の代わりに使用できます。 寄付ページに表示されている暗号通貨の金額を、正確に送信してください。$USDの金額ではありません。 そうでないと、手数料が差し引かれ、メンバーシップを自動的に処理できません。 確認には最大24時間かかることがありますので、このページを更新してください（期限切れの場合も）。 クレジット/デビットカードを利用した寄付の方法 私達のクレジット/デビットカードのページを通して寄付 手順の一部では暗号通貨ウォレットについて言及していますが、心配しないでください。この手順の為に暗号通貨を学ぶ必要はありません。 %(coin_name)sのインストラクション Crypto WalletアプリでこのQRコードをスキャンして、支払いの詳細をすばやく入力してください QRコードをスキャンして支払います 対応しているのは、仮想通貨の標準的なバージョンのみです。特殊なネットワークや派生バージョンには対応しておりません。取引の確認には、使用する通貨によって最大1時間かかることがあります。 %(amount)sを<a %(a_page)s>このページ</a>で寄付。 この寄付は期限切れです。キャンセルして、もう一度やり直してください。 決済済みである場合： はい。私は領収書を送信しました 寄付中に暗号通貨のレートが変動した場合は元のレートを示す領収書を必ず添付してください。暗号通貨をご利用いただいたことにとても感謝しており、とても助かっています!! ❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。 <span %(span_circle)s>%(circle_number)s</span>領収書をEメールで我々に送信してください 問題が発生した場合は%(email)sまでご連絡ください。またできるだけ多くの情報(スクリーンショットなど)を添付してください。 ✅寄付ありがとうございます。アンナが手動であなたのメンバーシップを有効化するまで数日ほどお待ち下さい。 領収書もしくはあなたの認証番号のスクリーンショットを送信してください。 領収書を我々にEメールで送信したらこのボタンを押してください。アンナが手動でレビューを行います(この処理には数日かかることがあります)： 領収書またはスクリーンショットを個人認証アドレスに送信してください。このメールアドレスをPayPal寄付に使用しないでください。 キャンセル はい、キャンセルします 本当にキャンセルしますか？支払い済みの場合キャンセルは行わないでください。 ❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。 新しく寄付をする ✅寄付はキャンセルされました。 日付：%(date)s 識別子:%(id)s 再注文 ステータス：<span %(span_label)s>%(label)s</span> 合計：%(total)s <span %(span_details)s>(%(monthly_amount_usd)s /%(duration)sか月間、%(discounts)s%% の割引)</span> 合計%(total)s<span %(span_details)s>(%(monthly_amount_usd)s / %(duration)sか月間)</span> 1. あなたのメールアドレスを入力 2. 支払方法を選択 3. 再度支払方法を選択 4. 「Self-hosted」を選択 5. 「I confirm ownership」をクリック。 6.電子メールで領収書が届きます。それを私たちに送ってください。できるだけ早く寄付を確認します。 (キャンセルを行い、新たに寄付を行う) そのお支払い方法は現在お取り扱いしておりません。再度寄付をご希望の方は、上記の「再注文」ボタンをご利用ください。 支払い済みです。支払い方法を確認したい場合はこちらをクリックしてください： 古い支払い方法を見る 寄付ページがブロックされた場合は、別のインターネット接続（例：VPNや携帯電話のインターネット）を試してください。 残念ながら、Alipayページは<strong>中国本土</strong>からのみアクセス可能な場合が多いです。VPNを一時的に無効にするか、中国本土（または時々香港も）へのVPNを使用する必要があるかもしれません。 <span %(span_circle)s>1</span>Aipayで寄付 総額 %(total)s を <a %(a_account)s>こちらのAlipayアカウント</a> からご寄付ください Alipayのインストラクション <span %(span_circle)s>1</span>私達の暗号資産口座へ送金 %(total)sの額をこれらのアドレスのいずれかに寄付する: 暗号資産での方法 Bitcoin (BTC)の購入方法を参照してください。寄付分( %(total)s)のみでの購入で結構です。 私達のビットコイン(BTC)アドレスを受取人として指定し、%(total)sの寄付を行うためのインストラクションを参照してください。 <span %(span_circle)s>1</span>Pixで寄付 <a %(a_account)s>このPixアカウントを使用して%(total)sを寄付 Pixのインストラクション <span %(span_circle)s>1</span>WeChatで寄付 総額 %(total)s を <a %(a_account)s>こちらのWeChatアカウント</a> を使ってご寄付ください WeChatガイド 以下の「クレジットカードからビットコイン」への即時サービスを使用してください。数分で完了します： BTC / ビットコインアドレス（外部ウォレット）： BTC / ビットコインの金額： 以下の詳細をフォームに記入してください： この情報が古い場合は、メールでお知らせください。 この<span %(underline)s>正確な金額</span>を使用してください。クレジットカード手数料のため、合計費用が高くなる場合があります。少額の場合、残念ながら割引よりも高くなることがあります。 （最低：%(minimum)s、初回取引は認証不要） （最低：%(minimum)s） （最低：%(minimum)s） （最低：%(minimum)s、初回取引は認証不要） （最低：%(minimum)s） （最低：%(minimum)s、国によって異なり、初回取引は認証不要） PYUSDコイン(PayPal USD)の購入方法を見る。 寄付する金額（%(amount)s）に加えて、手数料をカバーするために少し多めにご購入いただくことをおすすめします（%(more)s 追加）。余った分はそのままご自身でお使いいただけます。 PayPalのウェブサイトまたはアプリの「PYUSD」ページへ行き、「Transfer」ボタン%(icon)sをクリックして「Send」をクリックしてください。 状態を更新 タイマーをリセットするには、新たに寄付を行ってください。 下記のBTCの金額を必ず使用してください。<em>日本円やドルではなく</em>BTCで送金をお願いします。正しく送金されない場合、メンバーシップを自動的に確認できなくなります。 Revolutでビットコイン（BTC）を購入 寄付する金額（%(amount)s）に加えて、手数料をカバーするために少し多めにご購入いただくことをおすすめします（%(more)s 追加）。余った分はそのままご自身でお使いいただけます。 Revolutの「Crypto」ページに移動してビットコイン（BTC）を購入します。 ビットコインを私たちのアドレスに送金 小額の寄付（$25未満）の場合、RushまたはPriorityを使用する必要があるかもしれません。 「ビットコインを送る」ボタンをクリックして「引き出し」を行います。%(icon)sアイコンを押してユーロからBTCに切り替えます。下記のBTC金額を入力し、「送信」をクリックします。困った場合は<a %(help_video)s>このビデオ</a>をご覧ください。 状態： 1 2 ステップ・バイ・ステップガイド ページ下部のステップ・バイ・ステップガイドを参照してください。 保存しないと、このアカウントを利用できなくなるおそれがあります！ まだ記録していない場合は、ログイン用の秘密キーを必ず控えてください: 寄付のご協力ありがとうございます！ 残り時間： 寄付 %(amount)sを%(account)sへ移動 認証を待機中(ページをリフレッシュして確認)… 譲渡を待機中(ページをリフレッシュして確認)… 以前 過去24時間の高速ダウンロードは、1日の制限にカウントされます。 Fast Partner Serversからのダウンロードは%(icon)sでマークされています。 直近18時間 まだどのファイルもダウンロードされていません。 ダウンロードされたファイルは公開されません。 時刻はすべてUTC基準です。 ダウンロード済みのファイル 高速と低速の両方のダウンロードでファイルをダウンロードした場合、2つ表示されます。 あまり心配しないでください。私たちがリンクしているウェブサイトから多くの人がダウンロードしており、トラブルになることは非常に稀です。ただし、安全のためにVPN（有料）や<a %(a_tor)s>Tor</a>（無料）を使用することをお勧めします。 ジョージ・オーウェルの「1984」をダウンロードしましたが、警察が来るのでしょうか？ あなたがアンナです！ アンナとは誰ですか？ メンバー向けに安定したJSON APIがあり、迅速なダウンロードURLを取得できます：<a %(a_fast_download)s>/dyn/api/fast_download.json</a>（JSON内にドキュメントがあります）。 他の使用例、例えばすべてのファイルを繰り返し処理する、カスタム検索を構築するなどの場合は、<a %(a_generate)s>生成</a>または<a %(a_download)s>ダウンロード</a>するElasticSearchおよびMariaDBデータベースをお勧めします。生データは<a %(a_explore)s>JSONファイルを通じて</a>手動で探索できます。 生のトレントリストも<a %(a_torrents)s>JSON</a>としてダウンロードできます。 APIはありますか？ ここでは著作権のある資料はホストしていません。私たちは検索エンジンであり、既に公開されているメタデータのみをインデックスしています。これらの外部ソースからダウンロードする際には、許可されている内容に関してあなたの管轄地域の法律を確認することをお勧めします。他者がホストするコンテンツについては責任を負いません。 ここで見たものについて苦情がある場合、最善の方法は元のウェブサイトに連絡することです。私たちは定期的に彼らの変更をデータベースに取り込んでいます。本当に有効なDMCAの苦情があると思われる場合は、<a %(a_copyright)s>DMCA / 著作権クレームフォーム</a>に記入してください。私たちはあなたの苦情を真剣に受け止め、できるだけ早くご連絡いたします。 著作権侵害を報告するにはどうすればいいですか？ シャドウライブラリやデジタル保存の世界に特別な意味を持つ本をいくつか紹介します： お気に入りの本は何ですか？ また、私たちのコードとデータは完全にオープンソースであることを皆さんにお伝えしたいと思います。私たちのようなプロジェクトで、これほど大規模なカタログを持ちながら完全にオープンソースである例は他に知りません。私たちのプロジェクトを不十分に運営していると考える方は、私たちのコードとデータを取り、自分たちのシャドウライブラリを設立していただきたいと思います。これは恨みや何かから言っているのではなく、本当に素晴らしいことだと思っています。なぜなら、それによって全体の基準が引き上げられ、人類の遺産がより良く保存されるからです。 このプロジェクトの運営方法が嫌いです！ <a %(a_mirrors)s>ミラーサイト</a>を設置していただけると嬉しいです。これに対しては財政的な支援を行います。 どうすれば手伝えますか？ はい、収集します。 私たちがメタデータを収集するインスピレーションは、アーロン・スワーツの「これまでに出版されたすべての本のための1つのウェブページ」という目標であり、彼はそのために<a %(a_openlib)s>Open Library</a>を作成しました。そのプロジェクトは成功していますが、私たちの独自の立場により、彼らが取得できないメタデータを入手することができます。もう一つのインスピレーションは、<a %(a_blog)s>世界にどれだけの本があるのか</a>を知りたいという願望であり、それによってまだ保存すべき本の数を計算することができます。 メタデータを収集しますか？ mhut.orgは特定のIP範囲をブロックしているため、VPNが必要になる場合があります。 <strong>Android:</strong> 右上の三点メニューをクリックし、「ホーム画面に追加」を選択します。 <strong>iOS:</strong> 下部の「共有」ボタンをクリックし、「ホーム画面に追加」を選択してください。 公式のモバイルアプリはありませんが、このウェブサイトをアプリとしてインストールすることができます。 モバイルアプリはありますか？ <a %(a_archive)s>Internet Archive</a>に送ってください。彼らが適切に保存します。 本や他の物理的な資料を寄付するにはどうすればいいですか？ 本をリクエストするにはどうすればよいですか？ <a %(a_blog)s>アンナのブログ</a>、<a %(a_reddit_u)s>Reddit</a>、<a %(a_reddit_r)s>サブレディット</a> — 定期的な更新 <a %(a_software)s>Anna’s Software</a> — 私たちのオープンソースコード <a %(a_datasets)s>Datasets</a> — データについて <a %(a_li)s>.li</a>、<a %(a_se)s>.se</a>、<a %(a_org)s>.org</a> — 代替ドメイン アンナのアーカイブについてのさらなるリソースはありますか？ <a %(a_translate)s>Anna’s Softwareで翻訳</a> — 私たちの翻訳システム <a %(a_wikipedia)s>ウィキペディア</a> — 私たちについての詳細（このページを更新していただくか、あなたの言語で新しいページを作成してください！） お好みの設定を選択し、検索ボックスを空のままにして「検索」をクリックし、ブラウザのブックマーク機能を使用してページをブックマークしてください。 検索設定を保存するにはどうすればよいですか？ セキュリティ研究者の皆さんには、私たちのシステムの脆弱性を探していただきたいと思っています。私たちは責任ある開示の大支持者です。こちら<a %(a_contact)s>から</a>ご連絡ください。 現在、バグ報奨金の提供は行っておりませんが、<a %(a_link)s>匿名性に影響を及ぼす可能性のある</a>脆弱性については、1万〜5万ドルの範囲で報奨金を提供しています。将来的には、より広範な対象を含めた報奨金制度を検討しています。なお、ソーシャルエンジニアリング攻撃は対象外ですのでご注意ください。 攻撃的セキュリティに興味があり、世界の知識や文化をアーカイブする手助けをしたい場合は、ぜひご連絡ください。あなたが貢献できる方法はたくさんあります。 責任ある開示プログラムはありますか？ 私たちは、世界中のすべての人に高速ダウンロードを提供するためのリソースが文字通り不足しています。もし裕福な支援者がこれを提供してくれるなら素晴らしいことですが、それまでは最善を尽くしています。私たちは寄付でかろうじて自立している非営利プロジェクトです。 このため、パートナーと協力して、無料ダウンロードのための2つのシステムを実装しました：遅いダウンロードの共有サーバーと、待機リスト付きのやや速いサーバー（同時にダウンロードする人数を減らすため）。 ボットやスクレイパーが悪用しないように、遅いダウンロードには<a %(a_verification)s>ブラウザ検証</a>も行っています。これにより、正当なユーザーのために速度がさらに遅くなるのを防ぎます。 Torブラウザを使用する際、セキュリティ設定を調整する必要がある場合があります。「標準」と呼ばれる最も低いオプションでは、Cloudflareのターンスタイルチャレンジが成功します。より高いオプションである「より安全」および「最も安全」では、チャレンジが失敗します。 大きなファイルの場合、ダウンロードが途中で中断されることがあります。大きなファイルのダウンロードを自動的に再開するために、ダウンロードマネージャー（例えばJDownloader）の使用をお勧めします。 なぜダウンロードが遅いのですか？ よくある質問 (FAQ) <a %(a_list)s>トレントリストジェネレーター</a>を使用して、ストレージスペースの制限内で最もトレントが必要なトレントのリストを生成します。 はい、<a %(a_llm)s>LLMデータ</a>ページをご覧ください。 ほとんどのトレントはファイルを直接含んでいるため、トレントクライアントに必要なファイルのみをダウンロードするよう指示できます。どのファイルをダウンロードするかを決定するには、<a %(a_generate)s>メタデータを生成</a>するか、<a %(a_download)s>ElasticSearchおよびMariaDBデータベースをダウンロード</a>できます。ただし、一部のトレントコレクションにはルートに.zipや.tarファイルが含まれているため、個々のファイルを選択する前にトレント全体をダウンロードする必要があります。 フィルタリング用の簡単なツールはまだ利用できませんが、貢献を歓迎します。 （ただし、後者の場合には<a %(a_ideas)s>いくつかのアイデア</a>があります。） 長い答え: 短い答え: 簡単ではありません。 このリストのトレント間で重複や重なりを最小限に抑えるよう努めていますが、これは常に達成できるわけではなく、ソースライブラリのポリシーに大きく依存します。ライブラリが独自のトレントを公開している場合、それは私たちの手に負えません。Anna’s Archiveがリリースするトレントについては、MD5ハッシュに基づいて重複を排除しますが、同じ本の異なるバージョンは重複排除されません。 はい。 これらは実際にはPDFやEPUBですが、多くのトレントでは拡張子がありません。トレントファイルのメタデータ、ファイルタイプや拡張子を含む情報は2か所で見つけることができます： 1. 各コレクションやリリースには独自のメタデータがあります。例えば、<a %(a_libgen_nonfic)s>Libgen.rsのトレント</a>には、Libgen.rsウェブサイト上に対応するメタデータデータベースがあります。通常、各コレクションの<a %(a_datasets)s>データセットページ</a>から関連するメタデータリソースへのリンクを提供しています。 2. <a %(a_generate)s>生成</a>または<a %(a_download)s>ダウンロード</a>することをお勧めします。これには、Anna’s Archiveの各レコードと対応するトレントファイル（利用可能な場合）へのマッピングが含まれています。ElasticSearch JSONの「torrent_paths」にあります。 一部のトレントクライアントは大きなピースサイズをサポートしていませんが、私たちのトレントの多くはそれを持っています（新しいものではこれを行っていませんが、仕様上は有効です！）。この問題に直面した場合は、別のクライアントを試すか、トレントクライアントの製作者に苦情を申し立ててください。 シードを手伝いたいのですが、ディスクスペースがあまりありません。 トレントが遅すぎます。データを直接ダウンロードできますか？ 特定の言語やトピックなど、ファイルの一部だけをダウンロードすることはできますか？ トレントの重複はどのように処理しますか？ トレントリストをJSON形式で取得できますか？ トレントにPDFやEPUBが見当たらず、バイナリファイルのみですが、どうすればいいですか？ なぜ私のトレントクライアントはあなたのトレントファイルやマグネットリンクを開けないのですか？ トレントFAQ 新しい本をアップロードするにはどうすればよいですか？ <a %(a_href)s>この素晴らしいプロジェクト</a>をご覧ください。 稼働時間モニターをお持ちですか？ Anna’s Archiveとは何ですか？ 高速ダウンロードをご利用になられる場合はメンバーになる必要がございます。 現在、Amazonギフトカード、クレジットカードおよびデビットカード、暗号通貨、Alipay、WeChatに対応しています。 本日の高速ダウンロードの上限に達しました。 アクセス 過去30日間の1時間あたりのダウンロード数。一時間あたりで%(hourly)s回。一日あたりで%(daily)s回。 私たちはパートナーと協力して、コレクションを誰でも簡単かつ無料でアクセスできるようにしています。私たちは、人類の集団的知恵に対する権利が誰にでもあると信じています。そして<a %(a_search)s>著者の犠牲を払うことなく</a>。 アンナのアーカイブで使用されるデータセットは完全にオープンソースであり、torrentsを使用して一括でミラーリングできます。<a %(a_datasets)s>もっと詳しく…</a> 長期アーカイブ 全データベース 検索 本、論文、雑誌、コミック、図書館の記録、メタデータなど… すべての<a %(a_code)s>コード</a>と<a %(a_datasets)s>データ</a>は完全にオープンソースです。 <span %(span_anna)s>アンナのアーカイブ</span>は2つの目標を掲げた非営利プロジェクトです: <li><strong>保存：</strong>人類のあらゆる知識と文化をバックアップします。</li><li><strong>アクセス：</strong> 世界中の誰もがこの知識と文化を利用できるようにします。</li> 世界最大の高品質テキストデータコレクションを保有しています。<a %(a_llm)s>詳細はこちら…</a> 大規模言語モデルの学習データ 🪩 ミラー: ボランティア募集 高リスクの匿名決済プロセッサーを運営している場合は、ぜひご連絡ください。また、控えめな小さな広告を掲載したい方も募集しています。すべての収益は保存活動に充てられます。 保存 私達は<a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">全世界の5%%の本</a>が保存されたと推定しています。 私たちは、本、論文、コミック、雑誌などを保存しています。これらの資料をさまざまな<a href="https://en.wikipedia.org/wiki/Shadow_library">シャドウライブラリ</a>、公式図書館、その他のコレクションから一つの場所に集めています。このデータは、トレントを使用して大量に複製することで、世界中に多くのコピーを作成し、永遠に保存されます。一部のシャドウライブラリはすでにこれを自分たちで行っています（例：Sci-Hub、Library Genesis）。一方、Anna’s Archiveは、大量配布を提供しない他の図書館（例：Z-Library）や、シャドウライブラリではない図書館（例：Internet Archive、DuXiu）を「解放」しています。 この広範な配布とオープンソースコードの組み合わせにより、当ウェブサイトは削除に対して耐性があり、人類の知識と文化の長期保存を確保します。<a href="/datasets">私たちのデータセット</a>について詳しく学びましょう。</a> もし<a %(a_member)s>メンバー</a>ならブラウザの認証は必要ありません。 🧬&nbsp;SciDBはSci-Hubの継続です。 SciDB 開く DOI Sci-Hubは新しい論文のアップロードを<a %(a_paused)s>一時停止</a>しています。 %(count)s本の学術論文への直接アクセス 🧬&nbsp;SciDBはSci-Hubの継続であり、馴染みのあるインターフェースとPDFの直接表示を提供します。DOIを入力して表示してください。 私たちは、Sci-Hubの全コレクションと新しい論文を提供しています。ほとんどは、Sci-Hubに似た使い慣れたインターフェースで直接閲覧できます。一部は外部ソースからダウンロードする必要があり、その場合はリンクを表示します。 トレントをシードすることで大いに助けることができます。<a %(a_torrents)s>詳細はこちら…</a> >%(count)s シーダー <%(count)s シーダー %(count_min)s–%(count_max)s シーダー 🤝 ボランティア募集中 非営利のオープンソースプロジェクトとして、常に協力してくれる人を探しています。 IPFS ダウンロード %(by)sによって<span %(span_time)s>%(time)s</span>に作成されたリスト 保存 ❌エラーが発生しました。もう一度お試しください。 ✅保存されました。このページを再読み込みしてください。 リストが空です。 編集 ファイルを探して「リスト」タブを開くことでこのリストに追加、削除できます。 リスト 私たちがどのように支援できるか 重複除去 テキストとメタデータの抽出 OCR 私たちは、未公開のコレクションを含む全コレクションへの高速アクセスを提供できます。 これは、数万ドルの寄付で提供できる企業レベルのアクセスです。また、まだ持っていない高品質のコレクションと交換することも可能です。 データの充実化を提供していただければ、返金いたします。例えば： 人類の知識の長期保存を支援しながら、モデルのためにより良いデータを取得しましょう！ <a %(a_contact)s>お問い合わせ</a>して、どのように協力できるかを話し合いましょう。 LLMは高品質なデータで成長することがよく理解されています。私たちは世界最大の書籍、論文、雑誌などのコレクションを持っており、これらは最高品質のテキストソースの一部です。 LLMデータ 独自の規模と範囲 私たちのコレクションには、学術雑誌、教科書、雑誌を含む1億以上のファイルが含まれています。この規模は、大規模な既存のリポジトリを組み合わせることで達成されます。 私たちのソースコレクションの一部は、すでに大量に利用可能です（Sci-HubやLibgenの一部）。他のソースは私たち自身で解放しました。<a %(a_datasets)s>Datasets</a>で全体の概要を示しています。 私たちのコレクションには、電子書籍時代以前の数百万冊の書籍、論文、雑誌が含まれています。このコレクションの大部分はすでにOCRされており、内部の重複はほとんどありません。 継続する キーをお忘れの場合は、<a %(a_contact)s>お問い合わせフォーム</a>よりご連絡いただき、できるだけ多くの情報をご提供ください。 お問い合わせいただくには、一時的に新しいアカウントを作成していただく必要がある場合があります。 このページを表示するには、<a %(a_account)s>ログイン</a>してください。</a> スパムボットがアカウントを大量に作成するのを防止するため、まずあなたのブラウザを認証する必要があります。 無限ループが発生した場合は、<a %(a_privacypass)s>Privacy Pass</a>をインストールすることをお勧めします。 広告ブロッカーや他のブラウザ拡張機能をオフにすることも役立つかもしれません。 ログイン/登録 Anna’s Archiveはメンテナンスのため一時的に停止しています。1時間後に再度お越しください。 別の著者 別の説明 別の版 別の拡張子 別のファイル名 別の出版社 別のタイトル オープンソース化された日付 もっと読む… 説明 Anna’s ArchiveでCADAL SSNO番号を検索 Anna’s ArchiveでDuXiu SSID番号を検索 Anna’s ArchiveでDuXiu DXID番号を検索 ISBNでAnna's Archiveを検索 アンナのアーカイブでOCLC(Worldcat)番号で検索 Open Library IDでAnna's Archiveを検索 アンナのアーカイブオンラインビューア %(count)s 影響を受けたページ ダウンロード後: このファイルのより良いバージョンが %(link)s にあるかもしれません。 トレントの一括ダウンロード コレクション 形式間の変換にはオンラインツールを使用してください。 推奨変換ツール: %(links)s 大きなファイルの場合、中断を防ぐためにダウンロードマネージャーの使用をお勧めします。 推奨ダウンロードマネージャー: %(links)s EBSCOhostのeBook索引 (技術者のみ) (上部の「GET」もクリック) (上部の「GET」をクリック) 外部ダウンロード 今日はあと%(remaining)s回<strong>🚀高速ダウンロード</strong>が使用可能です。メンバーになってくれてありがとうございます！❤️ <strong>🚀 高速ダウンロード</strong> 今日の高速ダウンロードの上限に達しました。 <strong>🚀高速ダウンロード</strong>最近ダウンロード済みのファイルです。リンクはしばらく有効です。 <strong>🚀 高速ダウンロード</strong><a %(a_membership)s>メンバー</a>になることで書籍や論文などの長期保存を支援することができます。私達からそのご支援への感謝の気持ちを込めて、高速ダウンロードがご利用可能です。❤️ 🚀 高速ダウンロード 🐢 低速ダウンロード インターネットアーカイブからの情報 IPFSゲートウェイ #%(num)d (IPFS で複数回試す必要があるかもしれません) Libgen.li Library.rs フィクション Library.rs ノンフィクション その広告には悪意のあるソフトウェアが含まれていることが知られているため、広告ブロッカーを使用するか、広告をクリックしないでください。 Amazonの「Send to Kindle」 djazzの「Send to Kobo/Kindle」 MagzDB ManualsLib Nexus/STC (Nexus/STCファイルはダウンロードが不安定な場合があります) ダウンロードが見つかりませんでした。 すべてのミラーは同じファイルを提供するため、安全に使用できます。 とはいえ、インターネットからファイルをダウンロードするときは常に注意が必要です。 たとえば、デバイスを最新の状態に保つようにしてください。 （リダイレクトなし） 当社のビューアで開く （ビューアで開く） オプション #%(num)d: %(link)s %(extra)s CADALでオリジナルレコードを探す DuXiuで手動検索 ISBNdbでオリジナルのレコードを探す WorldCatで元のレコードを見つける Open Libraryでオリジナルのレコードを検索 ISBNでその他のデータベースを検索 （印刷障害のある利用者のみ） PubMed ファイルを開くには、ファイル形式に応じて電子書籍リーダーまたはPDFリーダーが必要です。 推奨電子書籍リーダー: %(links)s Anna's Archive 🧬SciDB Sci-Hub: %(doi)s (関連するDOIはSci-Hubで公開されていない可能性があります) PDFとEPUBの両方のファイルをKindleまたはKobo eReaderに送信できます。 推奨ツール: %(links)s 詳細は<a %(a_slow)s>FAQ</a>をご覧ください。 著者と図書館を支援する これが気に入っていて、余裕がある場合は、オリジナルを購入するか、著者を直接支援することを検討してください。 これが地元の図書館で利用可能な場合、そこで無料で借りることを検討してください。 このファイルのパートナーサーバーからのダウンロードは一時的に利用できません。 トレント 信頼できるパートナーから。 Z-Library TOR上のZ-Library (Tor Browserが必要) 外部ダウンロードを表示 <span class="font-bold">❌ このファイルには問題がある可能性があり、ソースライブラリから隠されています。</span> これは著作権者の要求によるもので、より良い代替品が利用できるためですが、ファイル自体の問題である場合もあります。ダウンロードしても問題ないかもしれませんが、まずは代替ファイルを検索することをお勧めします。詳細はこちら: このファイルをダウンロードする場合は、信頼できる最新のソフトウェアのみを使用してファイルを開くようにしてください。 メタデータのコメント AA: 「%(name)s」をAnna’s Archiveで検索 コードエクスプローラー： コードエクスプローラーで表示 “%(name)s” URL： ウェブサイト： このファイルをお持ちで、Anna’s Archiveにまだない場合は、<a %(a_request)s>アップロード</a>をご検討ください。 Internet Archiveのオンライン貸出システムのファイル "%(id)s" これはInternet Archiveにあるファイルの記録であり、直接ダウンロードできるファイルではありません。本を借りる（下記リンク）か、<a %(a_request)s>ファイルをリクエストする</a>際にこのURLを使用することができます。 メタデータを改善 CADAL SSNO %(id)s メタデータレコード これはメタデータの記録であり、ダウンロード可能なファイルではありません。<a %(a_request)s>ファイルをリクエストする</a>際にこのURLを使用できます。 DuXiu SSID %(id)s メタデータレコード ISBNdb%(id)sのメタデータの記録 MagzDB ID %(id)s メタデータレコード Nexus/STC ID %(id)s メタデータレコード OCLC (WorldCat) 番号 %(id)s メタデータレコード Open Library%(id)sのメタデータの記録 Sci-Hubファイル "%(id)s" 見つかりませんでした "%(md5_input)s" は、私たちのデータベースで見つかりませんでした。 コメントを追加 (%(count)s) URLからmd5を取得できます。例: このファイルのより良いバージョンのMD5 (該当する場合)。 このファイルに非常に近い別のファイル (同じ版、同じファイル拡張子が見つかれば) がある場合は、こちらを記入してください。Anna’s Archiveの外でこのファイルのより良いバージョンを知っている場合は、<a %(a_upload)s>アップロード</a>してください。 何か問題が発生しました。ページをリロードして、もう一度お試しください。 コメントを残しました。表示されるまでに少し時間がかかることがあります。 <a %(a_copyright)s>DMCA / 著作権侵害申告フォーム</a>をご利用ください。 問題の説明 (必須) このファイルの品質が高い場合、ここで何でも議論できます！そうでない場合は、「ファイルの問題を報告」ボタンを使用してください。 素晴らしいファイル品質 (%(count)s) ファイル品質 <a %(a_metadata)s>このファイルのメタデータを改善する方法</a>を学びましょう。 問題の説明 <a %(a_login)s>ログイン</a>してください。 この本が大好きでした！ このファイルの品質を報告してコミュニティを支援してください！ 🙌 何か問題が発生しました。ページをリロードして、もう一度お試しください。 ファイルの問題を報告する (%(count)s) ご報告いただきありがとうございます。報告内容はこのページに表示され、適切なモデレーションシステムが整うまで、アンナによって手動でレビューされます。 コメントを残す 報告を送信 このファイルの何が問題ですか？ 貸出(%(count)s) コメント (%(count)s) ダウンロード(%(count)s) メタデータを探索(%(count)s) リスト(%(count)s) 状態(%(count)s) この特定のファイルに関する情報は、その<a %(a_href)s>JSONファイル</a>をご覧ください。 これは<a %(a_ia)s>IAの制御デジタル貸出</a>ライブラリによって管理され、アンナのアーカイブによって検索のためにインデックスされたファイルです。私たちが編纂したさまざまなデータセットに関する情報は、<a %(a_datasets)s>データセットページ</a>をご覧ください。 リンクされたレコードからのメタデータ Open Libraryでメタデータを改善する 「ファイルMD5」とは、ファイルの内容から計算されるハッシュで、その内容に基づいて合理的に一意です。ここでインデックスされたすべてのシャドウライブラリは、主にMD5を使用してファイルを識別します。 ファイルは複数のシャドウライブラリに表示されることがあります。私たちが編纂したさまざまなデータセットに関する情報は、<a %(a_datasets)s>データセットページ</a>をご覧ください。 ファイルの品質を報告する 総ダウンロード数: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} チェコのメタデータ %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google ブックス %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} 警告: 複数のリンクされたレコードがあります: Anna’s Archiveで本を見ると、タイトル、著者、出版社、版、年、説明、ファイル名など、さまざまなフィールドが表示されます。これらすべての情報は<em>メタデータ</em>と呼ばれます。 私たちはさまざまな<em>ソースライブラリ</em>から本を組み合わせているため、そのソースライブラリで利用可能なメタデータを表示します。例えば、Library Genesisから取得した本の場合、Library Genesisのデータベースからタイトルを表示します。 時々、本が<em>複数の</em>ソースライブラリに存在し、異なるメタデータフィールドを持つことがあります。その場合、各フィールドの最も長いバージョンを表示します。なぜなら、それが最も有用な情報を含んでいる可能性が高いからです！他のフィールドは説明の下に「代替タイトル」として表示します（ただし、異なる場合のみ）。 また、ソースライブラリから識別子や分類子などの<em>コード</em>を抽出します。<em>識別子</em>は特定の版を一意に表します。例としては、ISBN、DOI、Open Library ID、Google Books ID、またはAmazon IDがあります。<em>分類子</em>は、複数の類似した本をグループ化します。例としては、デューイ十進分類法（DCC）、UDC、LCC、RVK、またはGOSTがあります。これらのコードは、ソースライブラリで明示的にリンクされている場合もあれば、ファイル名や説明から抽出できる場合もあります（主にISBNとDOI）。 識別子を使用して、OpenLibrary、ISBNdb、またはWorldCat/OCLCなどの<em>メタデータ専用コレクション</em>のレコードを見つけることができます。これらのコレクションを閲覧したい場合は、検索エンジンの特定の<em>メタデータタブ</em>があります。マッチングレコードを使用して、欠落しているメタデータフィールドを埋めることができます（例：タイトルが欠落している場合）、または「代替タイトル」として使用します（既存のタイトルがある場合）。 本のメタデータがどこから来たのか正確に知りたい場合は、本のページの<em>「技術的詳細」タブ</em>を参照してください。そこには、その本の生のJSONへのリンクがあり、元のレコードの生のJSONへのポインタが含まれています。 詳細については、次のページを参照してください：<a %(a_datasets)s>Datasets</a>、<a %(a_search_metadata)s>Search (metadata tab)</a>、<a %(a_codes)s>Codes Explorer</a>、および<a %(a_example)s>Example metadata JSON</a>。最後に、すべてのメタデータは<a %(a_generated)s>生成</a>または<a %(a_downloaded)s>ダウンロード</a>してElasticSearchおよびMariaDBデータベースとして利用できます。 背景 メタデータを改善することで、本の保存に貢献できます！まず、Anna’s Archiveでメタデータの背景を読み、その後、Open Libraryとのリンクを通じてメタデータを改善する方法を学び、Anna’s Archiveで無料のメンバーシップを獲得しましょう。 メタデータを改善する では、メタデータが不完全なファイルに出会った場合、どうすれば修正できますか？ソースライブラリに行き、そのメタデータ修正手順に従うことができますが、ファイルが複数のソースライブラリに存在する場合はどうすればよいでしょうか？ Anna’s Archiveでは特別に扱われる識別子があります。<strong>Open Libraryのannas_archive md5フィールドは、他のすべてのメタデータを上書きします！</strong> まずはOpen Libraryについて少し学びましょう。 Open Libraryは2006年にAaron Swartzによって設立され、「これまでに出版されたすべての本のための1つのウェブページ」を目指しています。これは、誰でも編集できる、自由にライセンスされた、バルクでダウンロードできる、書籍メタデータのためのWikipediaのようなものです。これは私たちの使命と最も一致する書籍データベースです。実際、Anna’s ArchiveはAaron Swartzのビジョンと人生に触発されています。 車輪の再発明を避けるために、私たちはボランティアをOpen Libraryに向けることにしました。メタデータが間違っている本を見つけた場合、次の方法で支援できます： これは書籍にのみ適用され、学術論文やその他の種類のファイルには適用されないことに注意してください。他の種類のファイルについては、ソースライブラリを見つけることをお勧めします。Anna’s Archiveに変更が反映されるまでには数週間かかる場合があります。これは、最新のOpen Libraryデータダンプをダウンロードし、検索インデックスを再生成する必要があるためです。  <a %(a_openlib)s>Open Libraryのウェブサイト</a>にアクセスします。 正しい本のレコードを見つけます。<strong>警告：</strong>正しい<strong>版</strong>を選択することを確認してください。Open Libraryでは、「作品」と「版」があります。 「作品」は「ハリー・ポッターと賢者の石」のようなものです。 「版」は次のようなものです： 1997年にBloomsberyから出版された初版で、256ページです。 2003年にRaincoast Booksから出版されたペーパーバック版で、223ページです。 2000年にMedia Rodzinaから出版されたポーランド語訳「Harry Potter I Kamie Filozoficzn」で、328ページです。 これらの版はすべて異なるISBNと異なる内容を持っているので、正しいものを選んでください！ レコードを編集する（または存在しない場合は作成する）際に、できるだけ多くの有用な情報を追加してください！せっかくここにいるのだから、レコードを本当に素晴らしいものにしましょう。 「ID番号」の下で「Anna’s Archive」を選択し、Anna’s Archiveから本のMD5を追加します。これはURLの「/md5/」の後に続く長い文字列です。 Anna’s Archiveでこのレコードに一致する他のファイルを見つけ、それらも追加してください。将来的には、Anna’s Archiveの検索ページでそれらを重複としてグループ化できるようになります。 完了したら、更新したURLを書き留めてください。Anna’s ArchiveのMD5で少なくとも30のレコードを更新したら、<a %(a_contact)s>メール</a>を送ってリストを送信してください。これにより、Anna’s Archiveの無料メンバーシップを提供し、作業をより簡単に行えるようにします（お手伝いへの感謝として）。これらは大量の情報を追加する高品質な編集でなければならず、そうでない場合はリクエストが拒否されます。また、編集がOpen Libraryのモデレーターによって取り消されたり修正された場合もリクエストは拒否されます。 Open Libraryとのリンク 私たちの作業の開発と運用に大きく関与する場合、必要に応じて寄付収益の一部を共有することについて話し合うことができます。 すべてが設定され、アーカイブを更新し続ける能力を示した後にのみホスティング費用を支払います。つまり、最初の1〜2ヶ月は自己負担となります。 あなたの時間は補償されません（私たちの時間も同様です）、これは純粋なボランティア活動だからです。 私たちはホスティングとVPNの費用をカバーする用意があります。最初は月額200ドルまでです。これは基本的な検索サーバーとDMCA保護されたプロキシに十分です。 ホスティング費用 許可を求めたり、基本的な質問をするために<strong>私たちに連絡しないでください</strong>。行動は言葉よりも雄弁です！すべての情報はそこにあるので、ミラーの設定を進めてください。 問題が発生した場合は、Gitlabにチケットやマージリクエストを投稿してください。ミラー固有の機能をあなたと一緒に構築する必要があるかもしれません。例えば、「Anna’s Archive」からあなたのウェブサイト名へのリブランディング、（最初は）ユーザーアカウントの無効化、または本のページから私たちのメインサイトへのリンクなどです。 ミラーが稼働したら、ぜひご連絡ください。あなたのOpSecをレビューしたいと思います。それが確固たるものであれば、あなたのミラーにリンクし、より緊密に協力していきます。 この方法で貢献してくれるすべての人に事前に感謝します！これは心の弱い人向けではありませんが、人類史上最大の真にオープンな図書館の長寿を確固たるものにするでしょう。 始め方 Anna’s Archiveの回復力を高めるために、ミラーを運営するボランティアを募集しています。 あなたのバージョンは明確にミラーとして区別されます。例えば、「Bob’s Archive, an Anna’s Archive mirror」のように。 この作業に伴うリスクを引き受ける意思があり、そのリスクは重大です。運用セキュリティに関する深い理解があります。<a %(a_shadow)s>これら</a>の<a %(a_pirate)s>投稿</a>の内容はあなたにとって自明です。 最初はパートナーサーバーのダウンロードへのアクセスを提供しませんが、うまくいけば共有することができます。 あなたはAnna’s Archiveのオープンソースコードベースを運用し、定期的にコードとデータの両方を更新します。 この実現のために、私たちのチームと協力して<a %(a_codebase)s>コードベース</a>に貢献する意思があります。 私たちはこれを探しています： ミラー：ボランティア募集 他の寄付をする。 まだ寄付がされていません。<a %(a_donate)s>初めての寄付をする。</a> 寄付の詳細は公開されません。 私の寄付 📡すべてのコレクションをミラーリングする場合は、<a %(a_datasets)s>データセット</a>及び<a %(a_torrents)s>トレント</a>ページを参照してください。 過去24時間以内のあなたのIPアドレスからのダウンロード数: %(count)s。 🚀高速ダウンロードを使用するには<a %(a_membership)s>メンバーシップ</a>に入る必要があります。 パートナーのウェブサイトからダウンロード 待っている間、別のタブでAnna’s Archiveを引き続き閲覧してください（ブラウザがバックグラウンドタブの更新をサポートしている場合）。 複数のダウンロードページを同時に読み込むことができますが、1つのサーバーにつき1つのファイルのみダウンロードしてください。 一度ダウンロードリンクを取得すると、数時間有効です。 お待ちいただきありがとうございます。これにより、すべての人が無料でウェブサイトにアクセスできるようになります！😊 🔗 このファイルのすべてのダウンロードリンク：<a %(a_main)s>ファイルメインページ</a>。 ❌ Cloudflare VPNやその他のCloudflare IPアドレスからの遅いダウンロードは利用できません。 ❌ 公式ウェブサイトを通じてのみ遅いダウンロードが利用可能です。%(websites)sを訪問してください。 📚<a %(a_download)s>ここ</a>からダウンロードできます。 すべての人に無料でファイルをダウンロードする機会を提供するために、このファイルをダウンロードする前にお待ちいただく必要があります。 このファイルをダウンロードするには<span %(span_countdown)s>%(wait_seconds)s</span>秒お待ちください。 警告: 過去24時間以内にあなたのIPアドレスから大量のダウンロードがありました。ダウンロード速度が通常より遅くなる可能性があります。 VPN、共有インターネット接続、またはISPがIPを共有している場合、この警告はそのためかもしれません。 保存 ❌エラーが発生しました。もう一度お試しください。 ✅保存されました。このページを再読み込みしてください。 あなたの表示名を変更してください。あなたの識別子（"#"以降の部分）は変更できません。 <span %(span_time)s>%(time)s</span>にプロフィールが作成されました 編集 リスト ファイルを探し、「リスト」タブを開いて新しくリストを作成。 まだリストがありません プロフィールが見つかりませんでした。 プロフィール 現在、書籍のリクエストには対応できません。 本のリクエストをメールで送らないでください。 Z-LibraryまたはLibgenのフォーラムでリクエストを行ってください。 Anna’s Archiveにある記録 DOI: %(doi)s ダウンロード SciDB科学データ向けのデータベース Nexus/STC プレビューはまだ利用できません。<a %(a_path)s>Anna’s Archive</a>からファイルをダウンロードしてください。 人類の知識のアクセス性と長期保存を支援するために、<a %(a_donate)s>メンバー</a>になってください。 ボーナスとして、🧬&nbsp;SciDBはメンバーのために制限なしでより速く読み込まれます。 問題がある場合は、<a %(a_refresh)s>再読み込み</a>してください。 Sci-Hub 特定の検索フィールドを追加 説明とメタデータのコメントを検索 出版年 高度な アクセス コンテンツ 表示 リスト テーブル ファイルタイプ 言語 並び替え 最大 最適な 最新 （ファイルサイズ） （オープンソース） （出版年） 最も古い ランダム 最小 情報源 AAによってスクレイピングされ、オープンソース化されました デジタル貸出 (%(count)s) ジャーナル記事 (%(count)s) 次の場所で一致が見つかりました: %(in)s。<a %(a_request)s>ファイルをリクエストする</a>際にそこにあるURLを参照できます。 メタデータ (%(count)s) コードで検索インデックスを探索するには、<a %(a_href)s>コードエクスプローラー</a>を使用してください。 この検索インデックスは毎月更新されます。現在、%(last_data_refresh_date)sまでのエントリーが含まれています。より詳細な技術情報については、%(link_open_tag)sデータセットのページ</a>をご覧ください。 除外する のみを含める 未確認 さらに表示 次へ … 前へ この検索機能には、Internet Archive のデジタル貸出ライブラリのメタデータも含まれています。<a %(a_datasets)s>データセットについて詳しく見る</a>。 他のデジタル貸出ライブラリについては、<a %(a_wikipedia)s>Wikipedia</a> および <a %(a_mobileread)s>MobileRead Wiki</a> をご覧ください。 DMCAや著作権に関する申し立ては、<a %(a_copyright)s>こちらをクリック</a>してください。 ダウンロード時間 検索中にエラーが発生しました。 <a %(a_reload)s>ページを更新</a>してみてください。問題が解決しない場合は、%(email)sまでメールでご連絡ください。 高速ダウンロード 実際、誰でも<a %(a_torrents)s>統一されたトレントリスト</a>をシードすることで、これらのファイルを保存するのを手助けできます。 ➡️ 検索サーバーが遅い場合、これが誤って発生することがあります。そのような場合は、<a %(a_attrs)s>リロード</a>することで解決することがあります。 ❌ このファイルには問題がある可能性があります。 論文をお探しですか？ この検索インデックスには、さまざまなメタデータソースからのメタデータが含まれています。<a %(a_datasets)s>私たちのデータセットについてもっと知る</a>。 世界中の書籍に関するメタデータのソースは非常に多くあります。<a %(a_wikipedia)s>このWikipediaページ</a>は良い出発点ですが、他の良いリストをご存知の場合はお知らせください。 メタデータについては、元の記録を表示します。記録のマージは行いません。 現在、私たちは書籍や論文などを集めた、世界最大級のオープンカタログを公開中です。Sci-Hub、Library Genesis、Z-Library、<a %(a_datasets)s>そのほかいろいろ</a>もミラーしています。 <span %(classname)s>ファイルが見つかりません。</span> 検索語やフィルタを変更して、もう一度お試しください。 結果 %(from)s-%(to)s (%(total)s 件中) 他にもミラーすべき「シャドウライブラリ」を見つけた場合や、ご不明な点がありましたら、%(email)s までご連絡ください。 %(num)d 部分一致 %(num)d+ 部分一致 検索ボックスに入力すると、デジタル貸出ライブラリのファイルを探せます。 ここに入力して、%(count)s 件の直接ダウンロードできるファイルを検索しましょう。これらのファイルは<a %(a_preserve)s>永遠に保存されます</a>。 検索ボックスに入力してください。 ボックスに入力して、%(count)sの学術論文やジャーナル記事のカタログを検索してください。これらは<a %(a_preserve)s>永遠に保存</a>されます。 図書館からメタデータを検索するには、ボックスに入力してください。これは<a %(a_request)s>ファイルをリクエストする</a>際に役立ちます。 ヒント：より速く操作するには、以下のキーボードショートカットをご利用ください：「/」検索バーにフォーカス、「Enter」検索、「j」上へ、「k」下へ、「<」前のページ、「>」次のページ これらはメタデータレコードであり、<span %(classname)s>ダウンロード可能なファイルではありません</span>。 検索設定 検索 デジタル貸出 ダウンロード ジャーナル記事 メタデータ 新規検索 %(search_input)s - 検索 検索に時間がかかりすぎましたので、不正確な結果が表示される可能性があります。時々、<a %(a_reload)s>ページをリロードする</a>と役立つことがあります。 検索に時間がかかりすぎました。これは広範なクエリでは一般的です。フィルターのカウントが正確でない場合があります。 10,000ファイル以上の大規模なアップロードがLibgenやZ-Libraryに受け入れられない場合は、%(a_email)sまでご連絡ください。 Libgen.liの場合、まず<a %(a_forum)s>彼らのフォーラム</a>にユーザー名%(username)sとパスワード%(password)sでログインし、その後<a %(a_upload_page)s>アップロードページ</a>に戻ってください。 今のところ、私達はGenesis forksへアップロードすることを提案します。<a %(a_guide)s>こちら</a>がハンディガイドです。このウェブサイトでインデックスしているforkはどちらもこの同じアップロードシステムから引っ張ってきていることに注意してください。 小規模なアップロード（最大10,000ファイル）については、%(first)s と %(second)s の両方にアップロードしてください。 または、Z-Libraryに<a %(a_upload)s>こちら</a>からアップロードすることもできます。 学術論文をアップロードする場合は、Library Genesisに加えて<a %(a_stc_nexus)s>STC Nexus</a>にもアップロードしてください。彼らは新しい論文のための最高のシャドウライブラリです。まだ統合していませんが、いずれ統合する予定です。<a %(a_telegram)s>Telegramのアップロードボット</a>を使用するか、ピン留めされたメッセージに記載されているアドレスに連絡してください。 <span %(label)s>重労働ボランティア作業（USD$50-USD$5,000の報奨金）:</span> もしあなたが私たちの使命に多くの時間やリソースを捧げることができるなら、もっと密接に協力したいと考えています。最終的には内側のチームに参加することも可能です。予算は限られていますが、最も集中的な作業には<span %(bold)s>💰金銭的報奨金</span>を授与することができます。 <span %(label)s>軽いボランティア作業：</span> 時間が限られている場合でも、手助けできる方法はたくさんあります。継続的なボランティアには、<span %(bold)s>🤝 Anna’s Archiveのメンバーシップ</span>を報酬として提供します。 Anna’s Archiveはあなたのようなボランティアに依存しています。すべてのコミットメントレベルを歓迎し、私たちが求めている主な支援カテゴリーは2つあります： 時間をボランティアすることができない場合でも、<a %(a_donate)s>寄付</a>、<a %(a_torrents)s>トレントのシード</a>、<a %(a_uploading)s>本のアップロード</a>、または<a %(a_help)s>友達にAnna’s Archiveを紹介する</a>ことで大いに助けることができます。 <span %(bold)s>企業:</span> 企業レベルの寄付や新しいコレクション（例：新しいスキャン、OCR済みのデータセット、データの充実）との交換で、私たちのコレクションへの高速直接アクセスを提供します。<a %(a_contact)s>お問い合わせ</a>ください。また、<a %(a_llm)s>LLMページ</a>もご覧ください。 報奨金 私たちは、堅実なプログラミングスキルや攻撃的なセキュリティスキルを持つ人々を常に求めています。あなたは人類の遺産を保存するために大きな貢献をすることができます。 感謝の気持ちとして、堅実な貢献に対してメンバーシップを提供します。特に重要で困難なタスクに対しては、金銭的な報奨金を提供します。これは仕事の代替として見なされるべきではありませんが、追加のインセンティブとなり、発生した費用を補助することができます。 私たちのコードのほとんどはオープンソースであり、報奨金を授与する際にはあなたのコードも同様にオープンソースであることを求めます。個別に議論できる例外もあります。 報奨金はタスクを最初に完了した人に授与されます。報奨金チケットにコメントして、他の人にあなたが何かに取り組んでいることを知らせることができますので、他の人が待つか、あなたに連絡してチームを組むことができます。しかし、他の人もそれに取り組んであなたを追い越そうとすることは自由です。ただし、いい加減な仕事には報奨金を授与しません。高品質な提出物が近い時期（1日か2日以内）に提出された場合、私たちの裁量で両方に報奨金を授与することがあります。例えば、最初の提出物に100%%、2番目の提出物に50%%（合計150%%）を授与することがあります。 大きな報奨金（特にスクレイピング報奨金）の場合、約5%%を完了し、その方法が全体のマイルストーンにスケールする自信がある場合は、私たちに連絡してください。方法を共有してフィードバックを受ける必要があります。また、この方法で複数の人が報奨金に近づいている場合にどうするかを決定できます。例えば、複数の人に報奨金を授与する、チームを組むことを奨励するなどです。 警告：高額報奨金のタスクは<span %(bold)s>難しい</span>です—簡単なものから始めるのが賢明かもしれません。 <a %(a_gitlab)s>Gitlabの問題リスト</a>に移動し、「ラベル優先度」で並べ替えてください。これにより、私たちが重視するタスクの順序が大まかに示されます。明示的な報奨金がないタスクでも、特に「Accepted」や「Anna’s favorite」とマークされたものはメンバーシップの対象となります。「スタータープロジェクト」から始めるのが良いかもしれません。 軽作業ボランティア 現在、%(matrix)sで同期されたMatrixチャンネルもあります。 数時間の余裕がある場合、さまざまな方法で手助けすることができます。<a %(a_telegram)s>Telegramのボランティアチャット</a>に参加することを忘れないでください。 感謝の印として、基本的なマイルストーンには通常6ヶ月の「ラッキーライブラリアン」を提供し、継続的なボランティア作業にはさらに多くを提供します。すべてのマイルストーンには高品質の作業が必要です—いい加減な作業は私たちにとって害になるため、拒否されます。マイルストーンに達したら<a %(a_contact)s>メール</a>してください。 %(links)s 完了したリクエストのリンクまたはスクリーンショット。 Z-LibraryやLibrary Genesisフォーラムでの本（または論文など）のリクエストを満たす。私たちには独自の本リクエストシステムはありませんが、これらのライブラリをミラーしているため、それらを改善することはAnna’s Archiveを改善することにもなります。 マイルストーン タスク タスクによる。 <a %(a_telegram)s>Telegramのボランティアチャット</a>に投稿された小さなタスク。通常はメンバーシップのため、時には小さな報奨金のため。 ボランティアチャットグループに投稿された小さなタスク。 修正した問題には必ずコメントを残し、他の人が同じ作業を繰り返さないようにしてください。 %(links)s 改善した記録のリンク。 <a %(a_list)s >ランダムなメタデータの問題のリスト</a>を出発点として使用できます。 Open Libraryと<a %(a_metadata)s>リンク</a>してメタデータを改善する。 これらは、アンナのアーカイブについて誰かに知らせ、その人が感謝している様子を示すべきです。 %(links)s リンクまたはスクリーンショット。 アンナのアーカイブを広めること。例えば、AAで本を推薦したり、ブログ記事にリンクしたり、一般的に私たちのウェブサイトに人々を誘導したりすることです。 言語全体を完全に翻訳する（すでにほぼ完了していない場合）。 <a %(a_translate)s>ウェブサイトの翻訳</a>。 重要な貢献をした編集履歴へのリンク。 あなたの言語でAnna’s ArchiveのWikipediaページを改善する。他の言語のAAのWikipediaページや私たちのウェブサイトとブログから情報を含める。他の関連ページにAAへの参照を追加する。 ボランティアと報奨金 