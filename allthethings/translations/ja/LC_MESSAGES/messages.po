msgid "layout.index.invalid_request"
msgstr "無効なリクエストです。訪問 [%(websites)s]."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive の貸出図書館"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr "、 "

msgid "layout.index.header.tagline_and"
msgstr " と "

msgid "layout.index.header.tagline_and_more"
msgstr "その他多数"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️%(libraries)sのミラーを提供しています。"

msgid "layout.index.header.tagline_newnew2b"
msgstr "私たちは%(scraped)sをスクレイピングし、オープンソース化しています。"

msgid "layout.index.header.tagline_open_source"
msgstr "私たちのコードとデータはすべてオープンソースです。"

msgid "layout.index.header.tagline_new1"
msgstr "📚人類史上最も巨大な開かれた図書館オープンライブラリ。"

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;冊の本、 %(paper_count)s&nbsp;の論文 — 永遠に保存されます。"

msgid "layout.index.header.tagline"
msgstr "📚 世界最大のオープンソース・オープンデータの図書館。 ⭐️ Z-Library、Library Genesis、Sci-Hubなど。 📈 %(book_any)s冊の本、%(journal_article)sもの論文、%(book_comic)s冊の漫画、%(magazine)s冊の雑誌が永久に保存。"

msgid "layout.index.header.tagline_short"
msgstr "📚 世界で最も大きなオープンソース・オープンデータの図書館。<br> ⭐️Scihub、 Libgen、 Zlib、その他多数のミラー。"

msgid "common.md5_report_type_mapping.metadata"
msgstr "正しくないメタデータ (例: タイトル、説明、カバー画像)"

msgid "common.md5_report_type_mapping.download"
msgstr "ダウンロードに関する問題が発生しました。(例 : 接続ができない、回線速度が遅い、エラーメッセージ)"

msgid "common.md5_report_type_mapping.broken"
msgstr "ファイルを開けません(例 : ファイルの破損、DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "低品質のデータ(例：フォーマットの問題、スキャン品質、ページが欠けているなど)"

msgid "common.md5_report_type_mapping.spam"
msgstr "スパムファイルは削除されます。(例：広告、公序良俗に反するもの)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "著作権の主張"

msgid "common.md5_report_type_mapping.other"
msgstr "その他"

msgid "common.membership.tier_name.bonus"
msgstr "追加ダウンロード"

msgid "common.membership.tier_name.2"
msgstr "華麗なる本の虫"

msgid "common.membership.tier_name.3"
msgstr "幸運の司書"

msgid "common.membership.tier_name.4"
msgstr "まばゆき情報保持者"

msgid "common.membership.tier_name.5"
msgstr "驚異のアーカイビスト"

msgid "common.membership.format_currency.total"
msgstr "合計%(amount)s"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) 合計"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s ボーナス)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "未払い"

msgid "common.donation.order_processing_status_labels.1"
msgstr "支払い済み"

msgid "common.donation.order_processing_status_labels.2"
msgstr "キャンセル済み"

msgid "common.donation.order_processing_status_labels.3"
msgstr "期限切れ"

msgid "common.donation.order_processing_status_labels.4"
msgstr "アンナの承認待ち"

msgid "common.donation.order_processing_status_labels.5"
msgstr "無効"

msgid "page.donate.title"
msgstr "寄付"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "<a %(a_donation)s>既存の寄付</a>が進行中です。新しい寄付を行う前にその寄付を終了するかキャンセルしてください。"

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>寄付履歴</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archiveは非営利のオープンソース、オープンデータ・プロジェクトです。あなたが寄付をし、メンバーになることで、私たちの運営と開発がサポートされます。メンバーの皆様へ：私たちの活動を支えてくださりありがとうございます！❤️"

msgid "page.donate.header.text2"
msgstr "詳しくは、<a %(a_donate)s>寄付に関するよくある質問</a>をご覧ください。"

msgid "page.donate.refer.text1"
msgstr "<a %(a_refer)s>友達を紹介して、</a>！さらに多くのダウンロード回数をゲット！"

msgid "page.donate.bonus_downloads.main"
msgstr "あなたは%(profile_link)sからの紹介で%(percentage)s%%個の追加の高速ダウンロードを獲得しました。"

msgid "page.donate.bonus_downloads.period"
msgstr "これは会員期間全体に適用されます。"

msgid "page.donate.perks.fast_downloads"
msgstr "一日あたり%(number)s回の高速ダウンロード"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "今月寄付していただければ！"

msgid "page.donate.membership_per_month"
msgstr "月額 $%(cost)s"

msgid "page.donate.buttons.join"
msgstr "参加"

msgid "page.donate.buttons.selected"
msgstr "選択済み"

msgid "page.donate.buttons.up_to_discounts"
msgstr "最大%(percentage)s%%の割引"

msgid "page.donate.perks.scidb"
msgstr "SciDBの論文を認証無しで<strong>無制限に</strong>"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> へのアクセス"

msgid "page.donate.perks.refer"
msgstr "<a %(a_refer)s>友達を紹介</a>して<strong>%(percentage)s%%追加ダウンロード</strong>を獲得しましょう。"

msgid "page.donate.perks.credits"
msgstr "あなたのユーザーネームまたは匿名の情報がクレジットに記載されます"

msgid "page.donate.perks.previous_plus"
msgstr "これまでの特典と、さらに追加で:"

msgid "page.donate.perks.early_access"
msgstr "新機能への早期アクセス"

msgid "page.donate.perks.exclusive_telegram"
msgstr "舞台裏を伝える独占テレグラム"

msgid "page.donate.perks.adopt"
msgstr "“トレントを採用”：あなたのユーザー名またはメッセージをトレントのファイル名に挿入できます<div %(div_months)s>メンバーシップ12ヶ月ごと</div>"

msgid "page.donate.perks.legendary"
msgstr "人類の知識と文化の保存における伝説的な貢献"

msgid "page.donate.expert.title"
msgstr "エキスパートアクセス"

msgid "page.donate.expert.contact_us"
msgstr "お問い合わせ"

msgid "page.donate.small_team"
msgstr "私たちは小さなボランティアチームです。返信には1〜2週間かかる場合があります。"

msgid "page.donate.expert.unlimited_access"
msgstr "<strong> 無制限の </strong>高ダウンロード速度"

msgid "page.donate.expert.direct_sftp"
msgstr "ダイレクトな<strong> SFTP </strong> サーバー"

msgid "page.donate.expert.enterprise_donation"
msgstr "エンタープライズレベルの寄付や、 新たな蔵書のかわりに。(例:新たなスキャン、OCRデータセットなど)"

msgid "page.donate.header.large_donations_wealthy"
msgstr "裕福な個人や団体からの大口寄付を歓迎します。 "

msgid "page.donate.header.large_donations"
msgstr "$5000 以上の寄付については、直接 %(email)s にご連絡ください。"

msgid "page.donate.header.recurring"
msgstr "このページのメンバーシップは「月ごと」ですが、一度限りの寄付です。(毎月の請求は発生しません。)<a %(faq)s>詳しくは、寄付のFAQ</a>をご覧ください。"

msgid "page.donate.without_membership"
msgstr "会員登録なしでの寄付（任意の金額）を希望される場合は、Monero (XMR)アドレス: %(address)sをご利用ください。"

msgid "page.donate.payment.select_method"
msgstr "お支払い方法を選択してください。"

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(一時利用不可)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s ギフトカード"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "銀行カード（アプリ使用）"

msgid "page.donate.payment.buttons.crypto"
msgstr "暗号通貨%(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "クレジット/デビットカード"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal（通常）"

msgid "page.donate.payment.buttons.givebutter"
msgstr "カード / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "クレジット/デビット/Apple/Google（BMC）"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix(ブラジルのみ)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "銀行カード"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "クレジット/デビットカード（バックアップ）"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "クレジット/デビットカード2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "暗号通貨での寄付にはBTC、ETH、XMR、SOLがご利用いただけます。このオプションは暗号通貨に精通しているユーザのみ使用してください。"

msgid "page.donate.payment.desc.crypto2"
msgstr "BTC、ETH、XMRなどで暗号通貨での寄付が行なえます。"

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "初めて暗号通貨を使用する場合は、%(options)sを使用してビットコイン（最初に作られ、最も使用されている暗号通貨）を購入して寄付することをお勧めします。"

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "PayPalでの寄付には匿名性を保てるPayPalクリプトをご利用ください。この寄付の方法に時間を割いていただき、ありがとうございました。"

msgid "page.donate.payment.desc.paypal_short"
msgstr "寄付にPayPalを使用します。"

msgid "page.donate.payment.desc.cashapp"
msgstr "寄付にCash Appを使用します。"

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Cash Appを利用した寄付が簡単です!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Cash Appで%(amount)s以下の取引を行う場合、%(fee)sの手数料がかかります。%(amount)s以上では無料です！"

msgid "page.donate.payment.desc.revolut"
msgstr "Revolutを使って寄付する。"

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Revolutをお持ちの場合、これが最も簡単な寄付方法です！"

msgid "page.donate.payment.desc.credit_debit"
msgstr "寄付にクレジットカード又はデビットーカードを使用します。"

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay及びApple Payが使用できる可能性があります。"

msgid "page.donate.payment.desc.elimate_discount"
msgstr "クレジットカードで少額の寄付を行う場合、%(discount)s%%の割引が受けられなくなる可能性がございますので、長期のサブスクリプションをお勧めします。"

msgid "page.donate.payment.desc.longer_subs"
msgstr "少額の寄付を行う場合、手数料が高額になる場合がございますので長期のサブスクリプションをお勧めします。"

msgid "page.donate.payment.desc.binance_p1"
msgstr "Binanceを使用すると、クレジット/デビットカードや銀行口座でビットコインを購入し、それを私たちに寄付してください。これにより、安全かつ匿名性を保ったまま寄付を受け取ることができます。"

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binanceはほぼすべての国で利用可能で、銀行やクレジット/デビットカードに対応しています。これが、現在私たちが推奨する主な方法です。これを用いた寄付の方法を学んでいただけることに感謝します。これは、私たちにとって大きな助けとなります。"

msgid "page.donate.payment.desc.paypalreg"
msgstr "通常のPayPalアカウントを使って寄付する。"

msgid "page.donate.payment.desc.givebutter"
msgstr "クレジット/デビットカード、PayPal、Venmoを使用して寄付できます。次のページで選択できます。"

msgid "page.donate.payment.desc.amazon"
msgstr "寄付にアマゾンギフト券を使用します。"

msgid "page.donate.payment.desc.amazon_round"
msgstr "販売店が認める金額(最低%(minimum)s単位)に四捨五入する必要があることに注意してください。"

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>重要：</strong>Amazon.comにのみ対応しております。Amazon.co.jp等の他のアマゾンには対応していません。"

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>注意: </strong> このオプションは%(amazon)s用です。他のAmazonウェブサイトを利用したい場合は、上で選択してください。"

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "この方法は暗号通貨プロバイダーを経由します。少し戸惑うかもしれないので、他の支払い方法がうまくいかない場合にのみこの方法を使用してください。また、すべての国で機能するわけではありません。"

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Alipayアプリを通じて、クレジット/デビットカードを使用して寄付します（設定が非常に簡単です）。"

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Alipayアプリをインストール"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "<a %(a_app_store)s>Apple App Store</a>または<a %(a_play_store)s>Google Play Store</a>からAlipayアプリをインストールします。"

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "電話番号を使用して登録します。"

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "それ以上の個人情報は必要ありません。"

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>銀行カードを追加"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "対応カード: Visa, MasterCard, JCB, Diners Club, Discover。"

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "詳細は<a %(a_alipay)s>Alipayガイド</a>をご覧ください。"

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "銀行が私たちと取引をしたがらないため、クレジット/デビットカードを直接的にサポートすることはできません。☹ しかし、他の方法を使用してクレジット/デビットカードを使用する手段はいくつかあります。"

msgid "page.donate.payment.buttons.amazon"
msgstr "アマゾンギフト券"

msgid "page.donate.ccexp.amazon_com"
msgstr "クレジット/デビットカードを使用してAmazon.comギフトカードを送信してください。"

msgid "page.donate.ccexp.alipay"
msgstr "Alipayは世界のクレジット/デビットカードをサポートしています。詳細については<a %(a_alipay)s>Alipayガイド</a>をご覧ください。"

msgid "page.donate.ccexp.wechat"
msgstr "WeChat（Weixin Pay）は世界のクレジット/デビットカードに対応しています。WeChatアプリで、「Me => Services => Wallet => Add a Card」に進んでください。それが表示されない場合は、「Me => Settings => General => Tools => Weixin Pay => Enable」を使用して有効にしてください。"

msgid "page.donate.ccexp.crypto"
msgstr "クレジットカードまたはデビットカードを使って、暗号通貨を購入することができます。"

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "暗号通貨エクスプレスサービス"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "エクスプレスサービスは便利ですが、手数料が高くなります。"

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "より大きな寄付を迅速に行いたい場合で、$5-10の手数料を気にしない場合は、これを取引所の代わりに使用できます。"

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "寄付ページに表示されている暗号通貨の金額を、正確に送信してください。$USDの金額ではありません。"

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "そうでないと、手数料が差し引かれ、メンバーシップを自動的に処理できません。"

msgid "page.donation.payment2cc.method.paybis"
msgstr "（最低：%(minimum)s）"

msgid "page.donation.payment2cc.method.switchere"
msgstr "（最低：%(minimum)s、国によって異なり、初回取引は認証不要）"

msgid "page.donation.payment2cc.method.munzen"
msgstr "（最低：%(minimum)s、初回取引は認証不要）"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "（最低：%(minimum)s）"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "（最低：%(minimum)s）"

msgid "page.donation.payment2cc.method.coingate"
msgstr "（最低：%(minimum)s、初回取引は認証不要）"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "この情報が古い場合は、メールでお知らせください。"

msgid "page.donate.payment.desc.bmc"
msgstr "クレジットカード、デビットカード、Apple Pay、Google Payを使用する場合、「Buy Me a Coffee」（BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>）を使用します。このシステムでは、1つの「コーヒー」は5ドルに相当するため、寄付は5の倍数に丸められます。"

msgid "page.donate.duration.intro"
msgstr "購読(サブスクライブ)期間を選択してください。"

msgid "page.donate.duration.1_mo"
msgstr "1か月間"

msgid "page.donate.duration.3_mo"
msgstr "3か月間"

msgid "page.donate.duration.6_mo"
msgstr "6か月間"

msgid "page.donate.duration.12_mo"
msgstr "12か月間"

msgid "page.donate.duration.24_mo"
msgstr "24か月間"

msgid "page.donate.duration.48_mo"
msgstr "48ヶ月"

msgid "page.donate.duration.96_mo"
msgstr "96ヶ月"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>以降<span %(span_discount)s></span>割引</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "この決済では%(amount)s以上の取引のみ対応しております。異なる期間、決済方法を選択してください。"

msgid "page.donate.buttons.donate"
msgstr "寄付する"

msgid "page.donate.payment.maximum_method"
msgstr "この決済では%(amount)s以下の取引のみ対応しております。異なる期間、決済方法を選択してください。"

msgid "page.donate.login2"
msgstr "メンバーになるには<a %(a_login)s>ログイン・登録</a>が必要です。ご支援感謝いたします！"

msgid "page.donate.payment.crypto_select"
msgstr "お支払いに利用される通貨を選択してください："

msgid "page.donate.currency_lowest_minimum"
msgstr "最小額"

msgid "page.donate.coinbase_eth"
msgstr "（CoinbaseからEthereumを送信する際に使用）"

msgid "page.donate.currency_warning_high_minimum"
msgstr "（警告：高い最小額）"

msgid "page.donate.submit.confirm"
msgstr "寄付ボタンをクリックして寄付を承認。"

msgid "page.donate.submit.button"
msgstr "<span %(span_cost)s></span> <span %(span_label)s></span>を寄付"

msgid "page.donate.submit.cancel_note"
msgstr "チェックアウト時に寄付をキャンセルできます。"

msgid "page.donate.submit.success"
msgstr "✅寄付ページへリダイレクト中…"

msgid "page.donate.submit.failure"
msgstr "❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。"

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s/1か月"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "1か月間"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "3か月間"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "6か月間"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "12か月間"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "24か月間"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "48ヶ月"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "96ヶ月"

msgid "page.donate.submit.button.label.1_mo"
msgstr "１か月の\"%(tier_name)s\""

msgid "page.donate.submit.button.label.3_mo"
msgstr "３か月の\"%(tier_name)s\""

msgid "page.donate.submit.button.label.6_mo"
msgstr "6か月の\"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "12か月の\"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "24か月の\"%(tier_name)s\""

msgid "page.donate.submit.button.label.48_mo"
msgstr "48ヶ月間「%(tier_name)s」"

msgid "page.donate.submit.button.label.96_mo"
msgstr "96ヶ月間「%(tier_name)s」"

msgid "page.donation.title"
msgstr "寄付"

msgid "page.donation.header.date"
msgstr "日付：%(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "合計：%(total)s <span %(span_details)s>(%(monthly_amount_usd)s /%(duration)sか月間、%(discounts)s%% の割引)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "合計%(total)s<span %(span_details)s>(%(monthly_amount_usd)s / %(duration)sか月間)</span>"

msgid "page.donation.header.status"
msgstr "ステータス：<span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "識別子:%(id)s"

msgid "page.donation.header.cancel.button"
msgstr "キャンセル"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "本当にキャンセルしますか？支払い済みの場合キャンセルは行わないでください。"

msgid "page.donation.header.cancel.confirm.button"
msgstr "はい、キャンセルします"

msgid "page.donation.header.cancel.success"
msgstr "✅寄付はキャンセルされました。"

msgid "page.donation.header.cancel.new_donation"
msgstr "新しく寄付をする"

msgid "page.donation.header.cancel.failure"
msgstr "❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。"

msgid "page.donation.header.reorder"
msgstr "再注文"

msgid "page.donation.old_instructions.intro_paid"
msgstr "支払い済みです。支払い方法を確認したい場合はこちらをクリックしてください："

msgid "page.donation.old_instructions.show_button"
msgstr "古い支払い方法を見る"

msgid "page.donation.thank_you_donation"
msgstr "寄付のご協力ありがとうございます！"

msgid "page.donation.thank_you.secret_key"
msgstr "まだ記録していない場合は、ログイン用の秘密キーを必ず控えてください:"

msgid "page.donation.thank_you.locked_out"
msgstr "保存しないと、このアカウントを利用できなくなるおそれがあります！"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "そのお支払い方法は現在お取り扱いしておりません。再度寄付をご希望の方は、上記の「再注文」ボタンをご利用ください。"

msgid "page.donate.submit.crypto_note"
msgstr "<strong>重要な注意:</strong> 暗号通貨の価格は大きく変動することがあり、時には数分で20%%以上も変動することがあります。ただこれは、私たちのような \"影の慈善団体 \"と協力するために50～60%%の手数料を請求することの多い大多数の支払いプロバイダーで発生する手数料よりかはまだ少ないです。<u>お支払いいただいた金額が記載された領収書をお送りいただければ、（領収書が数時間以上経過していない限り）選択されたメンバーシップの金額をアカウントに加算いたします</u>。私たちをサポートするために、このようなことを我慢していただけることに本当に感謝しております！❤️"

msgid "page.donation.expired"
msgstr "この寄付は期限切れです。キャンセルして、もう一度やり直してください。"

msgid "page.donation.payment.crypto.top_header"
msgstr "暗号資産での方法"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>私達の暗号資産口座へ送金"

msgid "page.donation.payment.crypto.text1"
msgstr "%(total)sの額をこれらのアドレスのいずれかに寄付する:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Paypalでビットコインを購入"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "PayPalのアプリまたはウェブサイトで「Crypt」ページを探してください。これは通常「Finances」の下にあります。"

msgid "page.donation.payment.paypal.text3"
msgstr "Bitcoin (BTC)の購入方法を参照してください。寄付分( %(total)s)のみでの購入で結構です。"

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>ビットコインを私達のアドレスに送金する"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "PayPalアプリまたはウェブサイトで「ビットコイン」ページに行き、「譲渡」ボタン%(transfer_icon)sを押して「送金」してください。"

msgid "page.donation.payment.paypal.text5"
msgstr "私達のビットコイン(BTC)アドレスを受取人として指定し、%(total)sの寄付を行うためのインストラクションを参照してください。"

msgid "page.donation.credit_debit_card_instructions"
msgstr "クレジット/デビットカードを利用した寄付の方法"

msgid "page.donation.credit_debit_card_our_page"
msgstr "私達のクレジット/デビットカードのページを通して寄付"

msgid "page.donation.donate_on_this_page"
msgstr "%(amount)sを<a %(a_page)s>このページ</a>で寄付。"

msgid "page.donation.stepbystep_below"
msgstr "ページ下部のステップ・バイ・ステップガイドを参照してください。"

msgid "page.donation.status_header"
msgstr "状態："

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "認証を待機中(ページをリフレッシュして確認)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "譲渡を待機中(ページをリフレッシュして確認)…"

msgid "page.donation.time_left_header"
msgstr "残り時間："

msgid "page.donation.might_want_to_cancel"
msgstr "(キャンセルを行い、新たに寄付を行う)"

msgid "page.donation.reset_timer"
msgstr "タイマーをリセットするには、新たに寄付を行ってください。"

msgid "page.donation.refresh_status"
msgstr "状態を更新"

msgid "page.donation.footer.issues_contact"
msgstr "問題が発生した場合は%(email)sまでご連絡ください。またできるだけ多くの情報(スクリーンショットなど)を添付してください。"

msgid "page.donation.expired_already_paid"
msgstr "決済済みである場合："

msgid "page.donation.confirmation_can_take_a_while"
msgstr "確認には最大24時間かかることがありますので、このページを更新してください（期限切れの場合も）。"

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "PayPalでPYUSDコインを購入"

msgid "page.donation.pyusd.instructions"
msgstr "PYUSDコイン(PayPal USD)の購入方法を見る。"

msgid "page.donation.pyusd.more"
msgstr "寄付する金額（%(amount)s）に加えて、手数料をカバーするために少し多めにご購入いただくことをおすすめします（%(more)s 追加）。余った分はそのままご自身でお使いいただけます。"

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "PayPalのウェブサイトまたはアプリの「PYUSD」ページへ行き、「Transfer」ボタン%(icon)sをクリックして「Send」をクリックしてください。"

msgid "page.donation.transfer_amount_to"
msgstr "%(amount)sを%(account)sへ移動"

msgid "page.donation.cash_app_btc.step1"
msgstr "Cash Appでビットコイン（BTC）を購入"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Cash Appの「ビットコイン（BTC）」のページを開いてください。"

msgid "page.donation.cash_app_btc.step1.more"
msgstr "手数料分を含めて、%(amount)sより少し多め（%(more)s）に購入することをおすすめします。残額は保持されます。"

msgid "page.donation.cash_app_btc.step2"
msgstr "ビットコインをこちらのアドレスにご送金ください"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "「ビットコインを送る」を押して出金を開始します。%(icon)s アイコンを押して、ドルや日本円からBTCに切り替えます。下に送金したいBTCの金額を入力し、「送信」をクリックしてください。操作に迷った場合は、<a %(help_video)s>こちらの動画</a>をご覧ください。"

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "25ドル（約3,900円）未満の少額寄付には、「Rush」または「Priority」の使用が必要となる場合があります。"

msgid "page.donation.revolut.step1"
msgstr "Revolutでビットコイン（BTC）を購入"

msgid "page.donation.revolut.step1.text1"
msgstr "Revolutの「Crypto」ページに移動してビットコイン（BTC）を購入します。"

msgid "page.donation.revolut.step1.more"
msgstr "寄付する金額（%(amount)s）に加えて、手数料をカバーするために少し多めにご購入いただくことをおすすめします（%(more)s 追加）。余った分はそのままご自身でお使いいただけます。"

msgid "page.donation.revolut.step2"
msgstr "ビットコインを私たちのアドレスに送金"

msgid "page.donation.revolut.step2.transfer"
msgstr "「ビットコインを送る」ボタンをクリックして「引き出し」を行います。%(icon)sアイコンを押してユーロからBTCに切り替えます。下記のBTC金額を入力し、「送信」をクリックします。困った場合は<a %(help_video)s>このビデオ</a>をご覧ください。"

msgid "page.donation.revolut.btc_amount_below"
msgstr "下記のBTCの金額を必ず使用してください。<em>日本円やドルではなく</em>BTCで送金をお願いします。正しく送金されない場合、メンバーシップを自動的に確認できなくなります。"

msgid "page.donation.revolut.step2.rush_priority"
msgstr "小額の寄付（$25未満）の場合、RushまたはPriorityを使用する必要があるかもしれません。"

msgid "page.donation.payment2cc.cc2btc"
msgstr "以下の「クレジットカードからビットコイン」への即時サービスを使用してください。数分で完了します："

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "以下の詳細をフォームに記入してください："

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / ビットコインの金額："

msgid "page.donation.payment2cc.exact_amount"
msgstr "この<span %(underline)s>正確な金額</span>を使用してください。クレジットカード手数料のため、合計費用が高くなる場合があります。少額の場合、残念ながら割引よりも高くなることがあります。"

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / ビットコインアドレス（外部ウォレット）："

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)sのインストラクション"

msgid "page.donation.crypto_standard"
msgstr "対応しているのは、仮想通貨の標準的なバージョンのみです。特殊なネットワークや派生バージョンには対応しておりません。取引の確認には、使用する通貨によって最大1時間かかることがあります。"

msgid "page.donation.crypto_qr_code_title"
msgstr "QRコードをスキャンして支払います"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Crypto WalletアプリでこのQRコードをスキャンして、支払いの詳細をすばやく入力してください"

msgid "page.donation.amazon.header"
msgstr "アマゾンギフト券"

msgid "page.donation.amazon.form_instructions"
msgstr "<a %(a_form)s>Amazon.com公式フォーム</a>を使用して、%(amount)sのギフト券を以下のメールアドレスに送信してください。"

msgid "page.donation.amazon.only_official"
msgstr "<strong>Amazon.comの公式フォームから送信されたもの</strong>以外のギフト券は認証できません。このフォームを使用しなかった場合でも、私達は返金を行えません。"

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "正確な金額を入力：%(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "メッセージ欄には何も変更を加えないでください。"

msgid "page.donation.amazon.form_to"
msgstr "フォームの \"To \"受信者Eメール："

msgid "page.donation.amazon.unique"
msgstr "あなたの専用アカウントです。アカウントの共有はしないでください。"

msgid "page.donation.amazon.only_use_once"
msgstr "一度限りの使用。"

msgid "page.donation.amazon.waiting_gift_card"
msgstr "ギフトカードを待機中...(ページをリフレッシュして確認)"

msgid "page.donation.amazon.confirm_automated"
msgstr "ギフトカードが送付されたあと数分のうちに、私達のシステムが自動的に認証を行います。うまく動作しない場合は再度送信してください。(<a %(a_instr)s>インストラクション</a>)"

msgid "page.donation.amazon.doesnt_work"
msgstr "それでも解決できない場合は、メールでご連絡ください。担当者が手動で確認します（数日かかることがあります）。再送をすでにお試しいただいている場合は、その旨を必ずご記載ください。"

msgid "page.donation.amazon.example"
msgstr "例："

msgid "page.donate.strange_account"
msgstr "なお、アカウント名や画像が変になる場合があります。ご安心ください！これらのアカウントは、私たちの寄付先によって管理されています。ハッキングされたことはありません。"

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipayのインストラクション"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Aipayで寄付"

msgid "page.donation.payment.alipay.text1_new"
msgstr "総額 %(total)s を <a %(a_account)s>こちらのAlipayアカウント</a> からご寄付ください"

msgid "page.donation.page_blocked"
msgstr "寄付ページがブロックされた場合は、別のインターネット接続（例：VPNや携帯電話のインターネット）を試してください。"

msgid "page.donation.payment.alipay.error"
msgstr "残念ながら、Alipayページは<strong>中国本土</strong>からのみアクセス可能な場合が多いです。VPNを一時的に無効にするか、中国本土（または時々香港も）へのVPNを使用する必要があるかもしれません。"

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>寄付を行う（QRコードをスキャンまたはボタンを押す）"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "<a %(a_href)s>QRコード寄付ページ</a>を開いてください。"

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "AlipayアプリでQRコードを読み取るか、ボタンを押してアプリを起動してください。"

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "ページは中国から読み込まれるため、表示に時間がかかる場合があります。少々お待ちください。"

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChatガイド"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>WeChatで寄付"

msgid "page.donation.payment.wechat.text1"
msgstr "総額 %(total)s を <a %(a_account)s>こちらのWeChatアカウント</a> を使ってご寄付ください"

msgid "page.donation.payment.pix.top_header"
msgstr "Pixのインストラクション"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Pixで寄付"

msgid "page.donation.payment.pix.text1"
msgstr "<a %(a_account)s>このPixアカウントを使用して%(total)sを寄付"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>領収書をEメールで我々に送信してください"

msgid "page.donation.footer.verification"
msgstr "領収書またはスクリーンショットを個人認証アドレスに送信してください。このメールアドレスをPayPal寄付に使用しないでください。"

msgid "page.donation.footer.text1"
msgstr "領収書もしくはあなたの認証番号のスクリーンショットを送信してください。"

msgid "page.donation.footer.crypto_note"
msgstr "寄付中に暗号通貨のレートが変動した場合は元のレートを示す領収書を必ず添付してください。暗号通貨をご利用いただいたことにとても感謝しており、とても助かっています!!"

msgid "page.donation.footer.text2"
msgstr "領収書を我々にEメールで送信したらこのボタンを押してください。アンナが手動でレビューを行います(この処理には数日かかることがあります)："

msgid "page.donation.footer.button"
msgstr "はい。私は領収書を送信しました"

msgid "page.donation.footer.success"
msgstr "✅寄付ありがとうございます。アンナが手動であなたのメンバーシップを有効化するまで数日ほどお待ち下さい。"

msgid "page.donation.footer.failure"
msgstr "❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。"

msgid "page.donation.stepbystep"
msgstr "ステップ・バイ・ステップガイド"

msgid "page.donation.crypto_dont_worry"
msgstr "手順の一部では暗号通貨ウォレットについて言及していますが、心配しないでください。この手順の為に暗号通貨を学ぶ必要はありません。"

msgid "page.donation.hoodpay.step1"
msgstr "1. あなたのメールアドレスを入力"

msgid "page.donation.hoodpay.step2"
msgstr "2. 支払方法を選択"

msgid "page.donation.hoodpay.step3"
msgstr "3. 再度支払方法を選択"

msgid "page.donation.hoodpay.step4"
msgstr "4. 「Self-hosted」を選択"

msgid "page.donation.hoodpay.step5"
msgstr "5. 「I confirm ownership」をクリック。"

msgid "page.donation.hoodpay.step6"
msgstr "6.電子メールで領収書が届きます。それを私たちに送ってください。できるだけ早く寄付を確認します。"

msgid "page.donate.wait_new"
msgstr "お問い合わせの前に、少なくとも<span %(span_hours)s>24時間</span>お待ちいただき、このページを更新してください。"

msgid "page.donate.mistake"
msgstr "お支払い時に誤りがあった場合でも、返金はできませんが、できる限り対応させていただきます。"

msgid "page.my_donations.title"
msgstr "私の寄付"

msgid "page.my_donations.not_shown"
msgstr "寄付の詳細は公開されません。"

msgid "page.my_donations.no_donations"
msgstr "まだ寄付がされていません。<a %(a_donate)s>初めての寄付をする。</a>"

msgid "page.my_donations.make_another"
msgstr "他の寄付をする。"

msgid "page.downloaded.title"
msgstr "ダウンロード済みのファイル"

msgid "page.downloaded.fast_partner_star"
msgstr "Fast Partner Serversからのダウンロードは%(icon)sでマークされています。"

msgid "page.downloaded.twice"
msgstr "高速と低速の両方のダウンロードでファイルをダウンロードした場合、2つ表示されます。"

msgid "page.downloaded.fast_download_time"
msgstr "過去24時間の高速ダウンロードは、1日の制限にカウントされます。"

msgid "page.downloaded.times_utc"
msgstr "時刻はすべてUTC基準です。"

msgid "page.downloaded.not_public"
msgstr "ダウンロードされたファイルは公開されません。"

msgid "page.downloaded.no_files"
msgstr "まだどのファイルもダウンロードされていません。"

msgid "page.downloaded.last_18_hours"
msgstr "直近18時間"

msgid "page.downloaded.earlier"
msgstr "以前"

msgid "page.account.logged_in.title"
msgstr "アカウント"

msgid "page.account.logged_out.title"
msgstr "ログイン/登録"

msgid "page.account.logged_in.account_id"
msgstr "アカウントID: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "公開プロフィール：%(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "秘密キー（他人に教えないでください）：%(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "表示"

msgid "page.account.logged_in.membership_has_some"
msgstr "メンバーシップ：%(until_date)sまで<strong>%(tier_name)s</strong><a %(a_extend)s>(延長)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "メンバーシップ：<strong>未加入</strong><a %(a_become)s>(メンバーになる)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "高速ダウンロードは過去24時間で<strong>%(used)s / %(total)s</strong>使用されました"

msgid "page.account.logged_in.which_downloads"
msgstr "どのダウンロードですか？"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "限定Telegramグループ: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "ここに参加してください！"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "グループに参加するには、<a %(a_tier)s>上位プラン</a>にアップグレードしてください。"

msgid "page.account.logged_in.membership_upgrade"
msgstr "メンバーシップのより高いティアへのアップグレードをご希望の場合はアンナ(%(email)s)にご連絡ください。"

msgid "page.contact.title"
msgstr "連絡用メール"

msgid "page.account.logged_in.membership_multiple"
msgstr "複数のメンバーシップを組み合わせることができます（24時間ごとの高速ダウンロードが合算されます）。"

msgid "layout.index.header.nav.public_profile"
msgstr "公開プロフィール"

msgid "layout.index.header.nav.downloaded_files"
msgstr "ダウンロード済みのファイル"

msgid "layout.index.header.nav.my_donations"
msgstr "私の寄付"

msgid "page.account.logged_in.logout.button"
msgstr "ログアウト"

msgid "page.account.logged_in.logout.success"
msgstr "✅ログアウトされています。ページを再読み込みしてもう一度ログインしてください。"

msgid "page.account.logged_in.logout.failure"
msgstr "❌エラーが発生しました。ページを再読込みしてもう一度試してみてください。"

msgid "page.account.logged_out.registered.text1"
msgstr "登録が完了しました！あなたの秘密鍵は<span %(span_key)s>%(key)s</span>です"

msgid "page.account.logged_out.registered.text2"
msgstr "注意を払ってこの鍵を管理してください。鍵をなくした場合はあなたのアカウントにアクセスできなくなります。"

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>ブックマーク</strong>このページをブックマークして鍵を保存することができます。</li><li %(li_item)s><strong>ダウンロード</strong><a %(a_download)s>こちら</a>をクリックして鍵をダウンロードできます。</li><li %(li_item)s><strong>パスワードマネージャー</strong>利便性のために鍵は事前に入力されており、ログイン時にパスワード・マネージャーに保存できます。</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "秘密鍵を入力してログイン："

msgid "page.account.logged_out.key_form.placeholder"
msgstr "秘密鍵"

msgid "page.account.logged_out.key_form.button"
msgstr "ログイン"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "誤った秘密鍵です。キーを確認してもう一度試すか、以下より新規アカウントを作成してください。"

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "キーを失くさないでください！"

msgid "page.account.logged_out.register.header"
msgstr "まだアカウントがありませんか？"

msgid "page.account.logged_out.register.button"
msgstr "新しいアカウントを登録"

msgid "page.login.lost_key"
msgstr "キーをお忘れの場合は、<a %(a_contact)s>お問い合わせフォーム</a>よりご連絡いただき、できるだけ多くの情報をご提供ください。"

msgid "page.login.lost_key_contact"
msgstr "お問い合わせいただくには、一時的に新しいアカウントを作成していただく必要がある場合があります。"

msgid "page.account.logged_out.old_email.button"
msgstr "古いEメールに基づいたアカウントをお持ちでしたら<a %(a_open)s>こちら</a>にEメールを入力してください。"

msgid "page.list.title"
msgstr "リスト"

msgid "page.list.header.edit.link"
msgstr "編集"

msgid "page.list.edit.button"
msgstr "保存"

msgid "page.list.edit.success"
msgstr "✅保存されました。このページを再読み込みしてください。"

msgid "page.list.edit.failure"
msgstr "❌エラーが発生しました。もう一度お試しください。"

msgid "page.list.by_and_date"
msgstr "%(by)sによって<span %(span_time)s>%(time)s</span>に作成されたリスト"

msgid "page.list.empty"
msgstr "リストが空です。"

msgid "page.list.new_item"
msgstr "ファイルを探して「リスト」タブを開くことでこのリストに追加、削除できます。"

msgid "page.profile.title"
msgstr "プロフィール"

msgid "page.profile.not_found"
msgstr "プロフィールが見つかりませんでした。"

msgid "page.profile.header.edit"
msgstr "編集"

msgid "page.profile.change_display_name.text"
msgstr "あなたの表示名を変更してください。あなたの識別子（\"#\"以降の部分）は変更できません。"

msgid "page.profile.change_display_name.button"
msgstr "保存"

msgid "page.profile.change_display_name.success"
msgstr "✅保存されました。このページを再読み込みしてください。"

msgid "page.profile.change_display_name.failure"
msgstr "❌エラーが発生しました。もう一度お試しください。"

msgid "page.profile.created_time"
msgstr "<span %(span_time)s>%(time)s</span>にプロフィールが作成されました"

msgid "page.profile.lists.header"
msgstr "リスト"

msgid "page.profile.lists.no_lists"
msgstr "まだリストがありません"

msgid "page.profile.lists.new_list"
msgstr "ファイルを探し、「リスト」タブを開いて新しくリストを作成。"

msgid "blog.ai-copyright.title"
msgstr "著作権制度の改革は、国家の安全保障のために必要です"

msgid "blog.ai-copyright.tldr"
msgstr "要約：DeepSeekを含む中国の大規模言語モデル（LLM）は、このサイトが保有する世界最大規模の（違法な）書籍・論文アーカイブを学習データとして使用しています。西側諸国は、国家安全保障の観点から著作権法を抜本的に見直す必要があります。"

msgid "blog.ai-copyright.subtitle"
msgstr "TorrentFreakによる関連記事: <a %(torrentfreak)s>最初</a>, <a %(torrentfreak_2)s>2番目</a>"

msgid "blog.ai-copyright.text1"
msgstr "少し前まで、「シャドウライブラリ」は衰退の一途をたどっていました。学術論文の巨大な違法アーカイブ「Sci-Hub」は、訴訟の影響により新たな論文の受け入れを停止しました。また、世界最大の違法電子書籍ライブラリ「Z-Library」では、運営者とされる人物が著作権侵害の罪で逮捕されました。彼らは奇跡的にその逮捕を免れましたが、Z-Library自体はいまだ深刻な危機に直面しています。"

msgid "blog.ai-copyright.text2"
msgstr "Z-Library が閉鎖の危機に直面していたとき、私はすでにその全ライブラリのバックアップを取得しており、それを保管できるプラットフォームを探していました。それが私が「Anna’s Archive」を立ち上げるきっかけとなった動機です。以前のプロジェクトが担っていた使命を受け継ぐものとして、活動を始めました。それ以来、私たちは世界最大のシャドウライブラリへと成長し、書籍、学術論文、雑誌、新聞など多様な形式で、1億4000万点以上の著作物を収蔵・公開しています。"

msgid "blog.ai-copyright.text3"
msgstr "私たちのチームは強い信念のもとに活動しています。これらのファイルを保存・公開することは、道徳的に正しい行為だと確信しています。図書館は世界的に資金不足に苦しんでおり、人類の知的遺産を営利企業の管理下に置くことは、望ましい未来とは言えません。"

msgid "blog.ai-copyright.text4"
msgstr "その後、AIが登場しました。ほぼすべての大手LLM（大規模言語モデル）開発企業が、私たちのデータを学習に使用するために連絡を取ってきました。しかし、このサイトの活動が違法であることに気づくと、アメリカを拠点とする企業の多く（すべてではない）は検討をやめました。一方、中国の企業は、その合法性にさほど関心を示すことなく、このサイトのコレクションを熱心に活用しています。これは、中国がほぼすべての主要な国際著作権条約の署名国であることを考えると、注目に値します。"

msgid "blog.ai-copyright.text5"
msgstr "私たちは、約30社に高速アクセスを提供してきました。その多くはLLM（大規模言語モデル）企業であり、一部は当アーカイブのコレクションを再販するデータブローカーです。大半は中国の企業ですが、アメリカ、ヨーロッパ、ロシア、韓国、日本の企業とも連携してきました。DeepSeekは、<a %(arxiv)s>以前のバージョン</a>が私たちのコレクションの一部を学習に使っていたことを認めていますが、最新モデルについては口を閉ざしたままです（おそらくこちらも私たちのデータで訓練されたと思われます）。"

msgid "blog.ai-copyright.text6"
msgstr "もし西側諸国がLLM開発、そして最終的にはAGI（汎用人工知能）の競争において先行し続けたいのであれば、著作権に対する立場を見直す必要があります——それも早急にです。私たちの「道徳的な主張」に賛同するかどうかにかかわらず、いまやこれは経済の問題であり、さらには国家安全保障の問題にもなりつつあります。すべての勢力圏が「人工的なスーパー科学者」「スーパー・ハッカー」「スーパー・軍事機構」の構築を進めています。情報の自由は、もはや国家の生存に関わる問題——安全保障上の死活問題になりつつあるのです。"

msgid "blog.ai-copyright.text7"
msgstr "私たちのチームは世界中から集まっており、特定のアライメントはありません。しかし、厳格な著作権法を持つ国々には、この“存亡に関わる脅威”をきっかけに法制度を見直すことを強く勧めたいと考えています。では、どうすればいいのでしょうか？"

msgid "blog.ai-copyright.text8"
msgstr "私たちがまず提案したいのは、著作権保護期間の見直しです。米国では著者の死後70年という極端に長い期間が設定されていますが、これは現代の知識社会においては非合理的です。特許制度のように20年という現実的な基準を設けることで、創作者に正当な利益を保障しつつ、社会全体の知的アクセスも促進されます。長編映画などのプロジェクトを含めても、20年という期間は十分に意味ある対価回収期間です。"

msgid "blog.ai-copyright.text9"
msgstr "少なくとも、政策立案者は、大量のテキストを保存・普及する行為に対する例外規定を設けるべきです。収益の損失が「個々の消費者からの購入減少」に起因するのであれば、個人レベルでの配布は引き続き禁止しても構いません。代わりに、LLMを訓練する企業、図書館、その他のアーカイブなど、広大なリポジトリを管理できる者は、これらの例外の対象となります。"

msgid "blog.ai-copyright.text10"
msgstr "いくつかの国はすでにこれを行っています。TorrentFreakは、中国と日本が著作権法にAIに関する例外規定を設けたと<a %(torrentfreak)s>報じています</a>。これが国際条約とどのように相互作用するかは不明ですが、確かに国内企業に対する保護を提供しており、私たちが見てきたことを説明しています。"

msgid "blog.ai-copyright.text11"
msgstr "Anna’s Archiveについては、道徳的信念に根ざした地下活動を続けます。しかし、私たちの最大の願いは、光の中に出て、法的に影響を拡大することです。どうか著作権を改革してください。"

msgid "blog.ai-copyright.signature"
msgstr "- Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

msgid "blog.ai-copyright.postscript"
msgstr "TorrentFreak による関連記事もご覧ください：<a %(torrentfreak)s>第1弾</a>、<a %(torrentfreak_2)s>第2弾</a>"

msgid "blog.all-isbns-winners.title"
msgstr "10,000ドル相当の「ISBN可視化」懸賞金の受賞者"

msgid "blog.all-isbns-winners.tldr"
msgstr "要約：1万ドルのISBN可視化コンテストに、驚くべき応募作品が多数寄せられました。"

msgid "blog.all-isbns-winners.text1"
msgstr "数か月前、私たちは ISBN 空間を可視化するための最良のビジュアライゼーションを募集する <a %(all_isbns)s>1万ドルの懸賞</a> を発表しました。特に強調したのは、「どの ISBN ファイルがすでにアーカイブされていて、どれが未アーカイブなのか」を明確に示すことでした。その後、ISBN がどれだけ多くの図書館に所蔵されているか（希少性の指標）を示すデータセットも提供しました。"

msgid "blog.all-isbns-winners.text2"
msgstr "皆さまからのご応募に、嬉しい驚きを感じています。多くの創意工夫に満ちた作品に触れ、大変感動しました。ご参加いただいたすべての方々に心より御礼申し上げます。皆さまの熱意と情熱に感謝いたします！"

msgid "blog.all-isbns-winners.text3"
msgstr "最終的に、次の質問に答えたいと考えました：<strong>世界にはどの本が存在し、すでにどれだけアーカイブされているのか、これからどの本に注力すべきなのか？</strong> こうした問いに多くの方が関心を寄せてくださっていることが、とても嬉しく思います。"

msgid "blog.all-isbns-winners.text4"
msgstr "私たち自身でシンプルな可視化から始めました。たった300KB未満のデータで、人類史上最大の完全にオープンな「書籍リスト」を簡潔に表現した図です："

msgid "blog.all-isbns-winners.opt.all"
msgstr "すべてのISBN"

msgid "blog.all-isbns-winners.opt.md5"
msgstr "Anna’s Archive にあるファイル"

msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADALデータベースのSSNO（識別子）"

msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALCデータ漏洩"

msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhostのeBookインデックス"

msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google ブックス"

msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN出版社のグローバル登録"

msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

msgid "blog.all-isbns-winners.opt.rgb"
msgstr "ロシア国立図書館"

msgid "blog.all-isbns-winners.opt.trantor"
msgstr "トランター帝国図書館"

msgid "common.back"
msgstr "戻る"

msgid "common.forward"
msgstr "進む"

msgid "common.last"
msgstr "最後"

msgid "blog.all-isbns-winner.text5"
msgstr "詳しくは、<a %(all_isbns)s>元のブログ記事</a>をご覧ください。"

msgid "blog.all-isbns-winner.text6"
msgstr "この可視化をさらに改善するためのチャレンジを実施しました。1位には6,000ドル、2位には3,000ドル、3位には1,000ドルの賞金を用意していました。圧倒的な反響と素晴らしい提出物により、賞金プールを少し増やし、3位を4人に500ドルずつ授与することに決定しました。受賞者は以下の通りですが、すべての提出物を<a %(annas_archive)s>こちら</a>で確認するか、<a %(a_2025_01_isbn_visualization_files)s>結合トレント</a>をダウンロードしてください。"

msgid "blog.all-isbns-winners.first"
msgstr "1位 6,000ドル: phiresky"

msgid "blog.all-isbns-winners.first.text1"
msgstr "この<a %(phiresky_github)s>提出物</a>（<a %(annas_archive_note_2951)s>Gitlabコメント</a>）は、私たちが望んでいたすべてを満たし、それ以上のものでした！特に印象的だったのは、驚くほど柔軟な可視化オプション（カスタムシェーダーまで対応）と、それに加えて豊富なプリセットが用意されている点です。さらに、全体の動作が非常に高速かつスムーズであること、バックエンドなしでも動作するシンプルな実装、そしてミニマップの工夫や、<a %(phiresky_github)s>ブログ記事</a>での丁寧な解説にも感銘を受けました。本当に素晴らしい作品であり、まさに文句なしの受賞者です！"

msgid "blog.all-isbns-winners.second"
msgstr "2位 3,000ドル: hypha"

msgid "blog.all-isbns-winners.second.text1"
msgstr "もう一つの素晴らしい<a %(annas_archive_note_2913)s>提出物</a>。1位ほど柔軟ではありませんが、実際には1位よりもマクロレベルのビジュアライゼーションを好みました（スペースフィリングカーブ、境界、ラベリング、ハイライト、パン、ズーム）。Joe Davisによる<a %(annas_archive_note_2971)s>コメント</a>が私たちの心に響きました："

msgid "blog.all-isbns-winners.second.quote"
msgstr "「完全な正方形や長方形は数学的には美しく感じられますが、マッピングにおいては必ずしも局所性（locality）に優れているわけではありません。私は、ヒルベルト曲線や古典的なモートン曲線に見られる非対称性は、欠点ではなく“特徴”だと考えています。たとえば、イタリアが“ブーツ型”であることで地図上ですぐに認識できるように、こうした曲線の独特な『クセ』も認知的なランドマークとして機能するかもしれません。この個性が空間記憶を高め、ユーザーが自分の位置を把握しやすくなったり、特定の領域を見つけたり、パターンに気づいたりする助けになる可能性があります。」"

msgid "blog.all-isbns-winners.second.text2"
msgstr "さらに、可視化やレンダリングの選択肢も豊富で、驚くほどスムーズかつ直感的なUIも備えています。まさに堂々の第2位にふさわしい作品です！"

msgid "blog.all-isbns-winners.third1"
msgstr "3位 500ドル #1: maxlion"

msgid "blog.all-isbns-winners.third1.text1"
msgstr "この<a %(annas_archive_note_2940)s>提出物</a>では、特に比較ビューと出版社ビューの異なる種類のビューが気に入りました。"

msgid "blog.all-isbns-winners.third2"
msgstr "3位 500ドル #2: abetusk"

msgid "blog.all-isbns-winners.third2.text1"
msgstr "最も洗練されたUIではありませんが、この<a %(annas_archive_note_2917)s>提出物</a>は多くの要件を満たしています。特に比較機能が気に入りました。"

msgid "blog.all-isbns-winners.third3"
msgstr "3位 500ドル #3: conundrumer0"

msgid "blog.all-isbns-winners.third3.text1"
msgstr "1位と同様に、この<a %(annas_archive_note_2975)s>提出物</a>はその柔軟性で私たちを驚かせました。最終的に、これはパワーユーザーにとって最大限の柔軟性を提供しつつ、一般ユーザーにとってはシンプルさを保つ優れたビジュアライゼーションツールを作る要因です。"

msgid "blog.all-isbns-winners.third4"
msgstr "3位 500ドル #4: charelf"

msgid "blog.all-isbns-winners.third4.text1"
msgstr "賞金を獲得した最後の<a %(annas_archive_note_2947)s>提出物</a>は非常に基本的ですが、私たちが本当に気に入ったユニークな機能があります。特に、特定のISBNをカバーするDatasetsの数を人気/信頼性の指標として示す方法が気に入りました。また、比較のために不透明度スライダーを使用するシンプルさと効果的な方法も非常に気に入りました。"

msgid "blog.all-isbns-winners.notable"
msgstr "注目アイデア"

msgid "blog.all-isbns-winners.notable.text1"
msgstr "特に印象に残った、他のアイデアや実装をご紹介します："

msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "希少性のための高層ビル"

msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "ライブ統計"

msgid "blog.all-isbns-winners.notable.reguster"
msgstr "注釈、そしてライブ統計"

msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "ユニークな地図ビューとフィルター"

msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "クールなデフォルトのカラースキームとヒートマップ。"

msgid "blog.all-isbns-winners.notable.timharding"
msgstr "データセットを簡単に切り替えて素早く比較。"

msgid "blog.all-isbns-winners.notable.j1618"
msgstr "美しいラベル。"

msgid "blog.all-isbns-winners.notable.immartian"
msgstr "本の数を示すスケールバー。"

msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "たくさんのスライダーでデータセットを比較、まるでDJのように。"

msgid "blog.all-isbns-winners.notable.text2"
msgstr "まだまだ紹介したいところですが、今回はこのあたりで一区切りとします。すべての応募作品は<a %(annas_archive)s>こちら</a>からご覧いただけますし、<a %(a_2025_01_isbn_visualization_files)s>こちらのトレント</a>からまとめてダウンロードすることもできます。本当に多くの応募があり、どの作品にも UI や実装面での独自の視点が光っていました。"

msgid "blog.all-isbns-winners.notable.text3"
msgstr "少なくとも、1位の応募作品は私たちのメインサイトに統合する予定です。また、他のいくつかの作品についても採用を検討しています。さらに現在、最も希少な書籍を特定し、確認し、そしてアーカイブしていくプロセスをどのように体系化するかについても、構想を始めています。この点については、今後さらにお知らせしていきます。"

msgid "blog.all-isbns-winners.notable.text4"
msgstr "参加してくれた皆さんに感謝します。多くの人が関心を持ってくれていることは素晴らしいです。"

msgid "blog.all-isbns-winners.gratitude"
msgstr "感謝の気持ちでいっぱいです。"

msgid "blog.all-isbns-winners.footer"
msgstr "- Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

msgid "blog.all-isbns.title"
msgstr "すべてのISBNの可視化 — 締切：2025年1月31日、賞金総額1万ドル"

msgid "blog.all-isbns.tldr"
msgstr "この図は、人類史上最大規模の完全オープンな「書籍一覧」を表しています。"

msgid "blog.all-isbns.text1"
msgstr "この画像は 1000×800 ピクセルで構成されています。各ピクセルは 2,500 件のISBNを表しています。ISBNのファイルがある場合、そのピクセルをより緑にします。ISBNが発行されていることがわかっているが、対応するファイルがない場合は、より赤くします。"

msgid "blog.all-isbns.text2"
msgstr "この画像はわずか 300KB未満でありながら、人類史上最大規模の完全にオープンな「書籍リスト」（※圧縮しても数百GB規模）を簡潔に表現しています。"

msgid "blog.all-isbns.text3"
msgstr "また、書籍のバックアップ作業がまだまだ道半ばであることも示しています。現時点でカバーできているのは、全体のわずか16%%にすぎません。"

msgid "blog.all-isbns.background"
msgstr "背景"

msgid "blog.all-isbns.background.text1"
msgstr "Anna’s Archiveは「人類の知識すべてをバックアップする」という使命を掲げていますが、世の中にまだどんな本が存在しているのかが分からなければ、その達成は不可能です。私たちには「やるべきことリスト（TODOリスト）」が必要です。そのための手がかりの一つが ISBN番号です。1970年代以降、多くの国で出版された本にはこのISBNが割り振られており、これを手がかりに、出版された書籍全体の地図を描くことができるのです。"

msgid "blog.all-isbns.background.text2"
msgstr "すべてのISBN割り当てを知っている中央の権威は存在しません。代わりに、これは分散システムであり、国が番号の範囲を取得し、それを主要な出版社に割り当て、さらに小さな出版社に範囲を細分化することがあります。最終的に個々の番号が本に割り当てられます。"

msgid "blog.all-isbns.background.text3"
msgstr "私たちは<a %(blog)s>2年前</a>にISBNdbのスクレイピングでISBNのマッピングを開始しました。それ以来、<a %(blog_2)s>Worldcat</a>、Google Books、Goodreads、Libbyなど、多くのmetadataソースをスクレイピングしてきました。完全なリストはアンナのアーカイブの「Datasets」と「Torrents」ページで見つけることができます。現在、私たちは世界で最大の完全にオープンで簡単にダウンロード可能な書籍metadata（したがってISBN）のコレクションを持っています。"

msgid "blog.all-isbns.background.text4"
msgstr "保存活動の重要性、そして今がその取り組みにおいて決定的な時期である理由については、<a %(blog)s>過去の記事</a>で詳しく述べています。今こそ、希少で注目されにくく、消失のリスクが高い書籍を特定し、保存していく必要があります。そのためには、世界中の書籍に関する質の高いメタデータの整備が不可欠です。"

msgid "blog.all-isbns.visualizing"
msgstr "可視化"

msgid "blog.all-isbns.visualizing.text1"
msgstr "概要画像に加えて、取得済みの個別のデータセットも閲覧できます。ドロップダウンメニューやボタンを使って切り替えてください。"

msgid "blog.all-isbns.visualizing.text2"
msgstr "これらの画像には多くの興味深いパターンがあります。なぜ異なるスケールで線やブロックの規則性があるのでしょうか？空白の領域は何でしょうか？なぜ特定のDatasetsはこんなに集まっているのでしょうか？これらの質問は読者への課題として残しておきます。"

msgid "blog.all-isbns.bounty"
msgstr "$10,000の報奨金"

msgid "blog.all-isbns.bounty.text1"
msgstr "ここには多くの探求の余地があるため、上記の視覚化を改善するための報奨金を発表します。ほとんどの報奨金とは異なり、これは時間制限があります。2025-01-31（23:59 UTC）までにオープンソースコードを<a %(annas_archive)s>提出</a>する必要があります。"

msgid "blog.all-isbns.bounty.text2"
msgstr "最優秀作品には$6,000、2位には$3,000、3位には$1,000が授与されます。すべての報奨金はMonero（XMR）で支払われます。"

msgid "blog.all-isbns.bounty.text3"
msgstr "以下は最低限の基準です。万が一、どの応募もこれらの条件を満たさない場合であっても、一部の応募に対しては当方の判断により報奨金を授与する可能性があります。"

msgid "blog.all-isbns.bounty.req1"
msgstr "このリポジトリをフォークし、ブログ記事のHTMLを編集してください。なお、当方の Flask バックエンド以外のバックエンドは許可していません。"

msgid "blog.all-isbns.bounty.req2"
msgstr "上記の画像をスムーズにズーム可能にし、個々のISBNまでズームできるようにしてください。ISBNをクリックすると、Anna’s Archive 上のメタデータページまたは検索結果へ遷移するようにしてください。"

msgid "blog.all-isbns.bounty.req3"
msgstr "すべてのデータセットを自由に切り替えられるようにする必要があります。"

msgid "blog.all-isbns.bounty.req4"
msgstr "国の範囲と出版社の範囲はホバー時にハイライトされるべきです。例えば、<a %(github_xlcnd_isbnlib)s>isbnlibのdata4info.py</a>を国情報に使用し、出版社には私たちの「isbngrp」スクレイプを使用できます（<a %(annas_archive)s>dataset</a>、<a %(annas_archive_2)s>torrent</a>）。"

msgid "blog.all-isbns.bounty.req5"
msgstr "デスクトップとモバイルの両方でうまく動作する必要があります。"

msgid "blog.all-isbns.bounty.text4"
msgstr "追加ポイント対象（以下は一例です — 創造力を存分に発揮してください）："

msgid "blog.all-isbns.bounty.bonus1"
msgstr "使いやすさや見た目の美しさは、選考において重要な評価ポイントとなります。"

msgid "blog.all-isbns.bounty.bonus2"
msgstr "拡大表示時には、ISBNごとに対応する書籍タイトルや著者情報などの詳細メタデータを表示するようにしてください。"

msgid "blog.all-isbns.bounty.bonus3"
msgstr "より優れたスペース充填曲線を使用してください。たとえば、1行目では 0〜4 の順に進み、2行目では 5〜9 を**逆順（ジグザグ）**で進むようなパターンを、再帰的に適用する方法などが考えられます。"

msgid "blog.all-isbns.bounty.bonus4"
msgstr "異なるまたはカスタマイズ可能なカラースキーム。"

msgid "blog.all-isbns.bounty.bonus5"
msgstr "データセットの比較用に専用ビューを用意すること（差分表示など）。"

msgid "blog.all-isbns.bounty.bonus6"
msgstr "タイトルなどのメタデータに大きな不一致がある場合など、問題をデバッグするための方法を提供してください。"

msgid "blog.all-isbns.bounty.bonus7"
msgstr "ISBNや範囲に関するコメントで画像に注釈を付ける。"

msgid "blog.all-isbns.bounty.bonus8"
msgstr "レア本・消失リスク本を検出するヒューリスティック手法があればご提案ください。"

msgid "blog.all-isbns.bounty.bonus9"
msgstr "思いつく限りのクリエイティブなアイデアを、ぜひ自由に提案してください！"

msgid "blog.all-isbns.bounty.text5"
msgstr "最低条件から大きく外れて、まったく異なるビジュアライゼーションを作っていただいても構いません。もし本当に素晴らしいものであれば、当方の裁量で報奨金の対象とさせていただきます。"

msgid "blog.all-isbns.bounty.text6"
msgstr "<a %(annas_archive)s>この問題</a>にコメントを投稿し、フォークしたリポジトリ、マージリクエスト、または差分へのリンクを添えて提出してください。"

msgid "blog.all-isbns.bounty.code"
msgstr "コード"

msgid "blog.all-isbns.bounty.code.text1"
msgstr "これらの画像を生成するコードや他の例は、<a %(annas_archive)s>このディレクトリ</a>にあります。"

msgid "blog.all-isbns.bounty.code.text2"
msgstr "必要なISBN情報をすべて約75MB（圧縮時）に収めたコンパクトなデータ形式を考案しました。そのデータ形式の説明および生成用コードは、<a %(annas_archive_l1244_1319)s>こちら</a>にあります。この賞金チャレンジにおいてこの形式を使う義務はありませんが、最も手軽に始められる方法かもしれません。メタデータの加工・変換は自由に行っていただいて構いません（ただし、コードはすべてオープンソースである必要があります）。"

msgid "blog.all-isbns.bounty.code.text3"
msgstr "創造力あふれる挑戦をお待ちしています。健闘を祈ります！"

msgid "blog.all-isbns.signature"
msgstr "- Annaとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

msgid "blog.annas-archive-containers.title"
msgstr "Anna’s Archive Containers（AAC）：世界最大のシャドウライブラリによる配布形式の標準化"

msgid "blog.annas-archive-containers.tldr"
msgstr "Anna’s Archiveは世界最大のシャドウライブラリとなり、リリースの標準化が必要になりました。"

msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna's Archive</a>は、世界最大のシャドウライブラリとなり、この規模で完全にオープンソースかつオープンデータで運営されている唯一のライブラリです。以下は、このサイトの「データセット」ページに掲載されている表（若干修正済み）："

msgid "blog.annas-archive-containers.text2"
msgstr "これを達成するために、私たちは3つの方法を取りました："

msgid "blog.annas-archive-containers.text2.li1"
msgstr "既存のオープンデータシャドウライブラリ（Sci-HubやLibrary Genesisなど）をミラーリングする。"

msgid "blog.annas-archive-containers.text2.li2"
msgstr "Libgen のコミックコレクションのように、オープン化を望んでいながらも人的・時間的リソースが不足しているシャドウライブラリの支援を行います。"

msgid "blog.annas-archive-containers.text2.li3"
msgstr "Z-Library のように一括共有を望まないライブラリから、データをスクレイピングしています。"

msgid "blog.annas-archive-containers.text3"
msgstr "（2）および（3）について、私たちは現在、数百TB規模のトレントコレクションを自ら管理しています。これまで、これらのコレクションには**個別対応（ワンオフ）**で取り組んできました。つまり、コレクションごとに専用のインフラやデータ構成を用意してきたということです。その結果、各リリースに大きな作業負担がかかるようになり、小規模な追加リリースが困難になっています。"

msgid "blog.annas-archive-containers.text4"
msgstr "そのため、リリースを標準化することに決めました。これは技術的なブログ記事であり、私たちの標準である<strong>Anna’s Archive Containers</strong>を紹介しています。"

msgid "blog.annas-archive-containers.goals.heading"
msgstr "デザインの目的"

msgid "blog.annas-archive-containers.goals.text1"
msgstr "主なユースケースは、既存のさまざまなコレクションから、ファイルとそれに関連するメタデータを配布することです。その際に最も重視している点は、以下のとおりです："

msgid "blog.annas-archive-containers.goals.goal1"
msgstr "異種混在のファイルとメタデータを、可能な限り元の形式のままで扱うこと。"

msgid "blog.annas-archive-containers.goals.goal2"
msgstr "ソースライブラリ内の異種識別子、または識別子の欠如。"

msgid "blog.annas-archive-containers.goals.goal3"
msgstr "metadataとファイルデータの別々のリリース、またはmetadataのみのリリース（例：私たちのISBNdbリリース）。"

msgid "blog.annas-archive-containers.goals.goal4"
msgstr "基本的にはトレントを用いて配布しますが、他の配布手段（例：IPFS）も視野に入れています。"

msgid "blog.annas-archive-containers.goals.goal5"
msgstr "不変の記録、トレントが永遠に存在することを想定する必要があるため。"

msgid "blog.annas-archive-containers.goals.goal6"
msgstr "増分リリース／追記可能なリリース。"

msgid "blog.annas-archive-containers.goals.goal7"
msgstr "機械による読み書きが容易かつ迅速に行える形式であり、とくに私たちのスタック（Python、MySQL、ElasticSearch、Transmission、Debian、ext4）に最適化されています。"

msgid "blog.annas-archive-containers.goals.goal8"
msgstr "人間による確認もある程度は容易ですが、あくまで機械による可読性が優先されます。"

msgid "blog.annas-archive-containers.goals.goal9"
msgstr "標準的なレンタルシードボックスでコレクションを簡単にシードできること。"

msgid "blog.annas-archive-containers.goals.goal10"
msgstr "バイナリデータはNginxのようなウェブサーバーによって直接提供可能。"

msgid "blog.annas-archive-containers.goals.text2"
msgstr "非目標："

msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "ファイルがディスク上で手動で閲覧しやすいことや、前処理なしで検索可能であることは重視していません。"

msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "既存の図書館ソフトウェアとの直接的な互換性は重視していません。"

msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "トレントを使ってコレクションをシードするのは誰にでも簡単にできるようにすべきですが、ファイルを実際に活用するには、相応の技術知識と覚悟が必要になると想定しています。"

msgid "blog.annas-archive-containers.goals.text3"
msgstr "Anna’s Archive はオープンソースであるため、自分たちのフォーマットを実際に自ら使用（ドッグフーディング）したいと考えています。検索インデックスを更新する際には、誰でも利用できる公開パスのみを使用することで、ライブラリをフォークした人がすぐに動かせるようにしています。"

msgid "blog.annas-archive-containers.standard.heading"
msgstr "標準"

msgid "blog.annas-archive-containers.standard.text1"
msgstr "最終的に、比較的シンプルな標準仕様に落ち着きました。これはまだ進行中のものであり、厳格な規格ではなく、柔軟性のあるものです。"

msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC</strong>（Anna's Archive Container）は、<strong>メタデータ</strong>および（任意で）<strong>バイナリデータ</strong>からなる単一のアイテムで、どちらも不変です。AAC は<strong>AACID</strong>と呼ばれるグローバルに一意な識別子を持ちます。"

msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>コレクション：</strong> 各 AAC はコレクションに属しており、コレクションとは意味的に一貫性のある AAC のリストです。メタデータの形式に大きな変更を加える場合は、新しいコレクションを作成する必要があります。"

msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>「レコード」と「ファイル」のコレクション.</strong> 慣例として、「レコード」と「ファイル」を異なるコレクションとしてリリースすることが便利なことが多く、例えばスクレイピングの速度に基づいて異なるスケジュールでリリースできます。「レコード」はメタデータのみのコレクションで、本のタイトル、著者、ISBNなどの情報を含み、「ファイル」は実際のファイル（pdf、epub）を含むコレクションです。"

msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID（アークID）</strong>AACIDのフォーマットは次のようになっています：<code style=\"color: #0093ff\">aacid__{collection}{ISO 8601 タイムスタンプ}{コレクション固有のID}__{shortuuid}</code>たとえば、実際にリリースされたAACIDの一例は、<code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>です。"

msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>：コレクション名を表します。ASCII文字（英字）、数字、アンダースコア（_）を含むことができますが、ダブルアンダースコア（__）は使用できません。"

msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>：ISO 8601形式の短縮版で、常にUTC（協定世界時）で表記されます。例：<code>20220723T194746Z</code>。この数字は、リリースのたびに単調に増加する必要がありますが、正確な意味づけはコレクションごとに異なる場合があります。スクレイピング実行時やID生成時のタイムスタンプを使用することを推奨します。"

msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>：必要に応じて使われる、コレクションごとのID（例：Z-LibraryのIDなど）です。使わなくても構いませんし、短くしてもOKです。ただし、AACID全体が150文字を超えそうな場合は、省略や短縮が必須です。"

msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUIDですが、ASCIIに圧縮されています。例としてbase57を使用します。現在、<a %(github_skorokithakis_shortuuid)s>shortuuid</a> Pythonライブラリを使用しています。"

msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACIDレンジ</strong>：AACIDには単調増加のタイムスタンプが含まれているため、それを利用して特定のコレクション内の範囲を示すことができます。フォーマットは以下の通りです：<code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>。これは ISO 8601 の表記法に準拠しています。範囲は連続している必要があり、重複しても構いませんが、その場合、重複するレコードはすでに同一コレクション内でリリースされたものと完全に一致していなければなりません（AACは不変であるため）。欠落レコードは許可されていません。"

msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>メタデータファイル.</strong> メタデータファイルは、特定のコレクションのAACの範囲のメタデータを含みます。これらには次の特性があります："

msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "ファイル名は、<code style=\"color: red\">annas_archive_meta__</code>で始まり、<code>.jsonl.zstd</code>で終わるAACID範囲でなければなりません。例えば、私たちのリリースの一つは次のように呼ばれています：<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>。"

msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "ファイル拡張子が示すように、ファイルタイプは<a %(jsonlines)s>JSON Lines</a>で<a %(zstd)s>Zstandard</a>で圧縮されています。"

msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "各JSONオブジェクトは、トップレベルに次のフィールドを含む必要があります：<strong>aacid</strong>、<strong>metadata</strong>、<strong>data_folder</strong>（オプション）。他のフィールドは許可されていません。"

msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code>は、コレクションのセマンティクスに従った任意のメタデータです。コレクション内で意味的に一貫している必要があります。"

msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> は任意項目です。この項目は、対応するバイナリデータを格納するフォルダ名を指定します。そのフォルダ内にある対応データのファイル名は、各レコードの AACID と一致します。"

msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code>プレフィックスは、あなたの機関の名前に適応させることができます。例：<code style=\"color: red\">my_institute_meta__</code>。"

msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>バイナリデータのフォルダ.</strong> 特定のコレクションのAACの範囲のバイナリデータを含むフォルダ。これらには次の特性があります："

msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "ディレクトリ名は、<code style=\"color: green\">annas_archive_data__</code>で始まり、サフィックスはありません。例えば、私たちの実際のリリースの一つには、次のようなディレクトリがあります：<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>。"

msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "ディレクトリには、指定された範囲内のすべてのAACのデータファイルを含める必要があります。各データファイルは、そのAACIDをファイル名として持たなければなりません（拡張子なし）。"

msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "各フォ各フォルダのサイズは大きくなりすぎないよう、100GB〜1TB程度を目安にするのがおすすめです。この目安は将来変わることがあります。"

msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>トレント。</strong> メタデータファイルとバイナリデータフォルダはトレントでまとめることができ、1つのメタデータファイルにつき1つのトレント、または1つのバイナリデータフォルダにつき1つのトレントとします。トレントは元のファイル/ディレクトリ名に<code>.torrent</code>のサフィックスを付けたファイル名でなければなりません。"

msgid "blog.annas-archive-containers.example.heading"
msgstr "例"

msgid "blog.annas-archive-containers.example.text1"
msgstr "最近のZ-ライブラリのリリースを例に見てみましょう。これは2つのコレクションで構成されています：「<span style=\"background: #fffaa3\">zlib3_records</span>」と「<span style=\"background: #ffd6fe\">zlib3_files</span>」。これにより、実際の書籍ファイルからメタデータレコードを個別にスクレイピングしてリリースすることができます。このようにして、メタデータファイルを含む2つのトレントをリリースしました。"

msgid "blog.annas-archive-containers.example.text2"
msgstr "また、バイナリデータフォルダを含むトレントも多数リリースしましたが、これは「<span style=\"background: #ffd6fe\">zlib3_files</span>」コレクションのみで、合計62個です。"

msgid "blog.annas-archive-containers.example.text3"
msgstr "<code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>を実行することで、中身を確認できます。"

msgid "blog.annas-archive-containers.example.text4"
msgstr "この場合、Z-ライブラリによって報告された書籍のメタデータです。トップレベルには「aacid」と「metadata」しかなく、「data_folder」はありません。対応するバイナリデータがないためです。AACIDには「22430000」が主なIDとして含まれており、「zlibrary_id」から取得されたことがわかります。このコレクションの他のAACも同じ構造を持つと予想されます。"

msgid "blog.annas-archive-containers.example.text5"
msgstr "次に、<code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>を実行してみましょう。"

msgid "blog.annas-archive-containers.example.text6"
msgstr "これははるかに小さなAACメタデータですが、このAACの大部分はバイナリファイルの別の場所にあります！結局のところ、今回は「data_folder」があるので、対応するバイナリデータが<code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>にあると予想できます。「metadata」には「zlibrary_id」が含まれているので、「zlib_records」コレクション内の対応するAACと簡単に関連付けることができます。AACIDを通じて関連付けることも可能です — 標準ではそれを規定していません。"

msgid "blog.annas-archive-containers.example.text7"
msgstr "「metadata」フィールド自体がJSONである必要はないことにも注意してください。XMLや他のデータ形式を含む文字列である可能性もあります。大量のデータであれば、関連するバイナリブロブにメタデータ情報を保存することもできます。"

msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "結論"

msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "この標準を使用することで、リリースをより段階的に行い、新しいデータソースをより簡単に追加できます。すでにいくつかのエキサイティングなリリースが進行中です！"

msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "他のシャドウライブラリがこののコレクションをミラーリングしやすくなることを願っています。結局のところ、私たちの目標は人類の知識と文化を永遠に保存することなので、冗長性が多いほど良いのです。"

msgid "blog.annas-archive-containers.conclusion"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

msgid "blog.annas-update-2022.title"
msgstr "アンナのアップデート: 完全オープンソースのアーカイブ、ElasticSearch、300GB以上のブックカバー"

msgid "blog.annas-update-2022.tldr"
msgstr "私たちはアンナのアーカイブで良い代替手段を提供するために昼夜を問わず取り組んできました。最近達成したことのいくつかを以下に示します。"

msgid "blog.annas-update-2022.text1"
msgstr "Z-Libraryが閉鎖され、その（疑われる）創設者が逮捕されたことで、私たちはアンナのアーカイブで良い代替手段を提供するために昼夜を問わず取り組んできました（ここではリンクしませんが、Googleで検索できます）。最近達成したことのいくつかを以下に示します。"

msgid "blog.annas-update-2022.open-source"
msgstr "アンナのアーカイブは完全にオープンソースです"

msgid "blog.annas-update-2022.open-source.text1"
msgstr "情報は自由であるべきだと私たちは信じており、私たち自身のコードも例外ではありません。私たちはすべてのコードをプライベートでホストしているGitlabインスタンスで公開しました：<a %(annas_archive)s>アンナのソフトウェア</a>。また、作業を整理するためにイシュートラッカーを使用しています。開発に参加したい場合は、ここから始めるのが良いでしょう。"

msgid "blog.annas-update-2022.open-source.text2"
msgstr "現在取り組んでいる内容の一部をご紹介すると、最近はクライアント側のパフォーマンス改善に注力しています。まだページネーション（ページ分割）を実装していないため、検索結果ページには 100〜200 件ほどの結果が一度に表示されることがありました。検索結果を早い段階で打ち切りたくはなかったのですが、これにより一部の端末では動作が重くなってしまうという問題がありました。そこで、私たちはちょっとした工夫を施しました。検索結果の大部分をHTMLコメント(<code><!-- --></code>)でラップし、「結果が表示される必要がある」と判断されたタイミングで、コメントを外して表示するようなJavaScriptを組み込みました。"

msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM「仮想化」は23行で実装され、派手なライブラリは不要です！限られた時間で解決すべき実際の問題があるときに、こうした迅速で実用的なコードが生まれます。現在、遅いデバイスでも検索がうまく機能するとの報告があります！"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "もう一つの大きな取り組みは、データベースの構築を自動化することでした。ローンチ時には、異なるソースを無計画にまとめていました。今ではそれらを更新し続けたいので、2つのLibrary Genesisフォークから新しいmetadataをダウンロードし、それらを統合するスクリプトをいくつか書きました。目標は、私たちのアーカイブに役立つだけでなく、シャドウライブラリmetadataを扱いたい人々にとっても簡単にすることです。目標は、すべての種類の興味深いmetadataが利用可能なJupyterノートブックを作成し、<a %(blog)s>ISBNの何パーセントが永遠に保存されるか</a>を調べるような研究をさらに行うことです。"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "最後に、寄付システムを刷新しました。クレジットカードを使用して、暗号通貨について何も知らなくても、直接私たちの暗号ウォレットにお金を入金できるようになりました。これが実際にどれだけうまく機能するかを引き続き監視しますが、これは大きな進展です。"

msgid "blog.annas-update-2022.es"
msgstr "ElasticSearchへの切り替え"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "私たちの<a %(annas_archive)s>チケット</a>の一つは、検索システムに関する問題の寄せ集めでした。すべてのデータをMySQLに持っていたので、MySQLの全文検索を使用しました。しかし、それには限界がありました："

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "一部のクエリは非常に時間がかかり、すべてのオープン接続を占有するほどでした。"

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "デフォルトではMySQLには最小単語長があり、インデックスが非常に大きくなる可能性があります。「ベン・ハー」を検索できないと報告する人もいました。"

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "検索はメモリに完全にロードされたときにのみやや速く、これを実行するためにより高価なマシンを取得し、起動時にインデックスをプリロードするためのいくつかのコマンドが必要でした。"

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "新しい機能を構築するために簡単に拡張することはできませんでした。例えば、非空白言語の<a %(wikipedia_cjk_characters)s>トークン化の改善</a>、フィルタリング/ファセット化、ソート、「もしかして」提案、オートコンプリートなどです。"

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "多くの専門家と話した後、ElasticSearchに決定しました。完璧ではありません（デフォルトの「もしかして」提案とオートコンプリート機能はひどいです）が、全体的にはMySQLよりも検索においてはるかに良いです。ミッションクリティカルなデータに使用することにはまだ<a %(youtube)s>あまり乗り気ではありません</a>が（多くの<a %(elastic_co)s>進展</a>がありましたが）、全体的にはこの切り替えに非常に満足しています。"

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "現在のところ、より高速な検索、より良い言語サポート、より良い関連性のあるソート、異なるソートオプション、言語/書籍タイプ/ファイルタイプでのフィルタリングを実装しました。どのように機能するか興味がある場合は、<a %(annas_archive_l140)s>こちらを</a> <a %(annas_archive_l1115)s>ご覧</a> <a %(annas_archive_l1635)s>ください</a>。かなりアクセスしやすいですが、もう少しコメントが必要かもしれません…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB以上のブックカバーをリリース"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "最後に、小さなリリースを発表できることを嬉しく思います。Libgen.rsフォークを運営している方々と協力して、すべての書籍カバーをトレントとIPFSを通じて共有しています。これにより、カバーの表示負荷がより多くのマシンに分散され、より良く保存されます。多くの場合（すべてではありませんが）、書籍カバーはファイル自体に含まれているため、これは「派生データ」のようなものです。しかし、IPFSにあることは、アンナのアーカイブやさまざまなLibrary Genesisフォークの日常運用に非常に役立ちます。"

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "通常通り、このリリースはパイレートライブラリミラー（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）で見つけることができます。ここではリンクしませんが、簡単に見つけることができます。"

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Z-ライブラリの代替手段が整った今、ペースを少し緩めることができることを願っています。この作業負荷は特に持続可能ではありません。プログラミング、サーバー運用、または保存作業に興味がある場合は、ぜひご連絡ください。まだ多くの<a %(annas_archive)s>作業が残っています</a>。ご関心とご支援に感謝します。"

msgid "blog.all-isbns-winners.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "アンナのアーカイブは、世界最大のコミックシャドウライブラリ（95TB）をバックアップしました — あなたもシードに協力できます"

msgid "blog.backed-up-libgen-li.tldr"
msgstr "世界最大の漫画のシャドウライブラリには、これまで単一の障害点がありましたが…今日までです。"

msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Hacker Newsで議論する</a>"

msgid "blog.backed-up-libgen-li.text1"
msgstr "コミックブックの最大のシャドウライブラリは、おそらく特定のLibrary Genesisフォーク、Libgen.liのものです。そのサイトを運営する1人の管理者は、200万以上のファイルを集め、合計95TBを超える驚異的なコミックコレクションを収集しました。しかし、他のLibrary Genesisコレクションとは異なり、これはトレントを通じて一括で利用できませんでした。これらのコミックは彼の遅い個人サーバーを通じて個別にしかアクセスできませんでした — 単一障害点です。今日まで！"

msgid "blog.backed-up-libgen-li.text2"
msgstr "この投稿では、このコレクションについて、そしてこの作業をさらにサポートするための資金調達についてお話しします。"

msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>バーバラ・ゴードン博士は、図書館の平凡な世界に没頭しようとします…</q>"

msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgenフォーク"

msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "まず、背景を少し説明します。Library Genesisはその壮大な書籍コレクションで知られているかもしれません。Library Genesisのボランティアが他にもプロジェクトを立ち上げていることを知っている人は少ないです。例えば、雑誌や標準文書の大規模なコレクション、Sci-Hubの完全なバックアップ（Sci-Hubの創設者アレクサンドラ・エルバキアンとの協力による）、そして実際に膨大なコミックのコレクションがあります。"

msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "ある時点で、Library Genesisのミラーの異なる運営者たちがそれぞれの道を歩み始め、現在のように複数の異なる「フォーク」が存在する状況が生まれましたが、すべてがLibrary Genesisの名前を保持しています。Libgen.liフォークは、このコミックコレクションと、かなりの雑誌コレクションを独自に持っています（これも私たちが取り組んでいるところです）。"

msgid "blog.backed-up-libgen-li.collaboration"
msgstr "共同作業"

msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "このコレクションは、その規模から以前より私たちの「やりたいことリスト」の上位にありました。Z-Libraryのバックアップに成功したあと、次のターゲットとしてこのコレクションに狙いを定めました。当初は直接スクレイピングを試みましたが、これはなかなかの挑戦でした。というのも、相手側のサーバー状態があまり良くなかったためです。この方法で約15TBのデータを取得することができましたが、非常に時間のかかる作業でした。"

msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "幸運にも、ライブラリの運営者と連絡を取ることができ、すべてのデータを直接送ってもらうことに同意してもらいました。これにより、はるかに速くなりました。それでも、すべてのデータを転送して処理するのに半年以上かかり、ディスクの破損でほとんどすべてを失うところでした。そうなれば、最初からやり直しになるところでした。"

msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "この経験から、データをできるだけ早く公開し、広くミラーリングすることが重要だと考えるようになりました。私たちは、このコレクションを永遠に失うまで、あと一つか二つの不運なタイミングの出来事からしか離れていません！"

msgid "blog.backed-up-libgen-li.collection"
msgstr "コレクション"

msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "迅速に動くということは、コレクションが少し整理されていないことを意味します…見てみましょう。ファイルシステムがあると想像してください（実際にはトレントに分割していますが）。"

msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "最初のディレクトリ、<code>/repository</code>は、これのより構造化された部分です。このディレクトリには、いわゆる「千ディレクトリ」が含まれています。各ディレクトリには千のファイルがあり、データベースで順次番号が付けられています。ディレクトリ<code>0</code>には、comic_id 0–999のファイルが含まれています。"

msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "これは、Library Genesisがフィクションとノンフィクションのコレクションに使用しているのと同じスキームです。アイデアは、各「千ディレクトリ」がいっぱいになるとすぐに自動的にトレントに変換されるというものです。"

msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "しかし、Libgen.liの運営者はこのコレクションのトレントを作成しなかったため、千ディレクトリはおそらく不便になり、「未整理ディレクトリ」に道を譲りました。これらは<code>/comics0</code>から<code>/comics4</code>までです。これらはすべてユニークなディレクトリ構造を持っており、ファイルを収集するためには理にかなっていたかもしれませんが、今ではあまり意味を成しません。幸いなことに、metadataはこれらすべてのファイルを直接参照しているため、ディスク上のストレージの組織は実際には問題ではありません！"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "metadataはMySQLデータベースの形式で利用可能です。これはLibgen.liのウェブサイトから直接ダウンロードできますが、私たちのMD5ハッシュを含む独自のテーブルと一緒にトレントでも提供します。"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "分析"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "95TBがストレージクラスターに投入されると、そこに何があるのかを理解しようとします…重複を削除するなどしてサイズを少しでも減らせるかどうか分析しました。以下は私たちの発見の一部です："

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "意味的な重複（同じ本の異なるスキャン）は理論的にはフィルタリング可能ですが、難しいです。コミックを手動で見ていると、誤検出が多すぎました。"

msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "MD5による純粋な重複もいくつかあり、これは比較的無駄ですが、それをフィルタリングしても約1%% inの節約にしかなりません。この規模ではそれでも約1TBですが、この規模では1TBはあまり重要ではありません。このプロセスでデータを誤って破壊するリスクを冒したくありません。"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "私たちは、コミックを原作とした映画のような、書籍以外のデータをたくさん見つけました。それらは他の手段で広く利用可能であるため、無駄に思えます。しかし、コンピュータでリリースされた<em>インタラクティブなコミックブック</em>があり、それを誰かが映画として記録して保存したため、映画ファイルを単にフィルタリングすることはできないと気付きました。"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "最終的に、コレクションから削除できるものは数パーセントしか節約できませんでした。そして、私たちはデータホーダーであり、これをミラーリングする人々もデータホーダーであることを思い出しました。「削除って何のこと？！」 :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "したがって、私たちはあなたに完全で未修正のコレクションを提供します。データは大量ですが、それでも多くの人がシードしてくれることを願っています。"

msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "募金活動"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "このデータをいくつかの大きなチャンクでリリースしています。最初のトレントは<code>/comics0</code>で、12TBの巨大な.tarファイルにまとめました。それは、無数の小さなファイルよりもハードドライブやトレントソフトウェアにとって良いです。"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "このリリースの一環として、募金活動を行っています。このコレクションの運営費と契約費用をカバーし、継続的および将来のプロジェクトを可能にするために、20,000ドルを集めることを目指しています。いくつかの<em>大規模な</em>プロジェクトが進行中です。"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>私の寄付で誰を支援しているのですか？</em> 簡単に言えば、私たちは人類のすべての知識と文化をバックアップし、それを簡単にアクセスできるようにしています。私たちのコードとデータはすべてオープンソースであり、完全にボランティアで運営されているプロジェクトです。これまでに125TBの書籍を保存しました（LibgenとScihubの既存のトレントに加えて）。最終的には、世界中のすべての書籍を見つけ、スキャンし、バックアップすることを可能にし、奨励するフライホイールを構築しています。私たちのマスタープランについては、今後の投稿で書く予定です。 :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "12か月の「Amazing Archivist」メンバーシップ（780ドル）に寄付すると、<strong>「トレントを採用する」</strong>ことができ、トレントのファイル名にあなたのユーザー名やメッセージを入れます！"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>にアクセスして「寄付」ボタンをクリックすることで寄付できます。また、ソフトウェアエンジニア、セキュリティ研究者、匿名の商人専門家、翻訳者など、より多くのボランティアを探しています。ホスティングサービスを提供することで私たちをサポートすることもできます。そしてもちろん、私たちのトレントをシードしてください！"

msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "すでに温かくご支援いただいている皆さま、本当にありがとうございます。皆さまのご協力が大きな力になっています。"

msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "現在までにリリースされたトレントはこちらです（残りは現在処理中です）："

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "すべてのトレントは、<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>の「Datasets」セクションで見つけることができます（直接リンクはしていませんので、このブログへのリンクがRedditやTwitterなどから削除されないようにしています）。そこから、Torウェブサイトへのリンクをたどってください。"

msgid "blog.backed-up-libgen-li.next"
msgstr "次のステップは？"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "トレントの束は長期保存には最適ですが、日常的なアクセスにはあまり向いていません。私たちはホスティングパートナーと協力して、これらのデータをウェブ上にアップロードする予定です（アンナのアーカイブは直接ホストしていません）。もちろん、これらのダウンロードリンクはアンナのアーカイブで見つけることができます。"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "私たちはまた、皆さんがこのデータで何かをすることを招待しています！データをよりよく分析し、重複を排除し、IPFSに載せ、リミックスし、AIモデルをトレーニングするなど、すべてあなたのものです。あなたがそれで何をするのか、私たちは楽しみにしています。"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "最後に、前述のように、まだ大規模なリリースが控えています（もし<em>誰かが</em>偶然に<em>特定の<em>ACS4データベースのダンプを送ってくれたら、私たちの居場所はわかっていますよ…）、そして世界中の本をバックアップするためのフライホイールを構築しています。"

msgid "blog.backed-up-libgen-li.next.text4"
msgstr "どうぞお楽しみに、私たちはまだ始まったばかりです。"

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x 新しい本が海賊図書館ミラーに追加されました（+24TB、380万冊の本）"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "海賊図書館ミラーの最初のリリースで（編集済み：<a %(wikipedia_annas_archive)s>Anna’s Archive</a>に移動）、Z-ライブラリという大規模な違法書籍コレクションのミラーを作成しました。これは元のブログ投稿で書いた内容のリマインダーです："

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-ライブラリは人気のある（そして違法な）ライブラリです。彼らはLibrary Genesisのコレクションを取り、それを簡単に検索可能にしました。その上、彼らは新しい書籍の寄稿を奨励することで非常に効果的になっています。現在、これらの新しい書籍をLibrary Genesisに戻すことはありません。そして、Library Genesisとは異なり、彼らのコレクションを簡単にミラーリングできるようにはしていません。これは、彼らのビジネスモデルにとって重要です。なぜなら、彼らはコレクションへの一括アクセス（1日に10冊以上）に対して料金を請求しているからです。"

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "違法な書籍コレクションへの一括アクセスに対して料金を請求することについて道徳的な判断はしません。Z-ライブラリが知識へのアクセスを拡大し、より多くの書籍を調達することに成功したことは疑いの余地がありません。私たちは単に私たちの役割を果たしています：このプライベートコレクションの長期保存を確保することです。"

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "そのコレクションは2021年中頃に遡ります。その間に、Z-ライブラリは驚異的な速度で成長しており、約380万冊の新しい書籍を追加しました。確かに重複もありますが、その大部分は正当に新しい書籍、または以前に提出された書籍の高品質なスキャンのようです。これは主に、Z-ライブラリのボランティアモデレーターの数が増え、重複排除機能を備えた一括アップロードシステムがあるためです。これらの成果を祝福したいと思います。"

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "私たちは、前回のミラーと2022年8月の間にZ-ライブラリに追加されたすべての書籍を取得したことを喜んで発表します。また、最初に見逃した書籍をいくつかスクレイピングしました。全体として、この新しいコレクションは約24TBで、前回のもの（7TB）よりもはるかに大きいです。私たちのミラーは現在合計31TBです。Library Genesisに対して重複排除を行いました。すでにそのコレクションのトレントが利用可能だからです。"

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "新しいコレクションを確認するには、Pirate Library Mirrorにアクセスしてください（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動しました）。ファイルの構造や前回からの変更点についての詳細情報があります。ここからリンクはしません。これは違法な資料をホストしていないブログウェブサイトだからです。"

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "もちろん、シードすることも私たちを助ける素晴らしい方法です。以前のトレントセットをシードしている皆さん、ありがとうございます。私たちはポジティブな反応に感謝し、この異例の方法で知識と文化の保存に関心を持つ多くの人々がいることを嬉しく思います。"

msgid "blog.3x-new-books.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>)"

msgid "blog.how-to.title"
msgstr "海賊アーキビストになる方法"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "最初の課題は意外なものかもしれません。それは技術的な問題でも、法的な問題でもありません。心理的な問題です。"

msgid "blog.how-to.updates"
msgstr "始める前に、海賊図書館ミラー（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）に関する2つの更新情報があります。"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "非常に寛大な寄付をいただきました。最初は、Library Genesisの創設者である「bookwarrior」を支援している匿名の個人からの1万ドルの寄付でした。この寄付を実現してくれたbookwarriorに特別な感謝を。2つ目は、私たちの最後のリリース後に連絡をくれた匿名の寄付者からのもう1万ドルの寄付でした。さらに、小さな寄付もいくつかいただきました。皆様の寛大なご支援に心から感謝いたします。これにより、いくつかのエキサイティングな新プロジェクトを進めることができるので、どうぞお楽しみに。"

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "2回目のリリースのサイズに関して技術的な問題がありましたが、トレントは現在稼働中でシードされています。また、匿名の個人から非常に高速なサーバーでコレクションをシードするという寛大な申し出をいただきましたので、彼らのマシンに特別なアップロードを行っています。その後、コレクションをダウンロードしている他のすべての人は、速度の大幅な改善を実感できるはずです。"

#, fuzzy
msgid "blog.how-to.text1"
msgstr "デジタル保存の「なぜ」について、特に海賊アーキビズムについては、丸ごと本が書けるほどですが、あまり詳しくない方のために簡単に説明します。世界はこれまで以上に多くの知識と文化を生み出していますが、同時にこれまで以上に多くのものが失われています。人類は主に学術出版社、ストリーミングサービス、ソーシャルメディア企業などの企業にこの遺産を託していますが、彼らは必ずしも優れた管理者であるとは限りません。ドキュメンタリー「デジタル・アムネジア」や、ジェイソン・スコットの講演をぜひご覧ください。"

#, fuzzy
msgid "blog.how-to.text2"
msgstr "多くのものをアーカイブするのに優れた機関もありますが、彼らは法律に縛られています。海賊として、私たちは著作権の執行やその他の制約のために触れることができないコレクションをアーカイブするユニークな立場にあります。また、世界中でコレクションを何度もミラーリングすることで、適切な保存の可能性を高めることができます。"

#, fuzzy
msgid "blog.how-to.text3"
msgstr "今のところ、知的財産の利点と欠点、法律を破ることの道徳性、検閲についての考察、知識と文化へのアクセスの問題についての議論には入りません。それらをすべて片付けたところで、<em>どのように</em>進めるかに入りましょう。私たちのチームがどのようにして海賊アーキビストになったのか、そしてその過程で学んだ教訓を共有します。この旅に出るときには多くの課題がありますが、いくつかの課題を乗り越える手助けができればと思います。"

msgid "blog.how-to.community"
msgstr "コミュニティ"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "最初の課題は意外なものかもしれません。それは技術的な問題でも、法的な問題でもありません。心理的な問題です：この作業を影で行うことは非常に孤独になる可能性があります。あなたが何を計画しているか、そしてあなたの脅威モデルによっては、非常に注意深く行動する必要があるかもしれません。スペクトラムの一方の端には、Sci-Hubの創設者であるアレクサンドラ・エルバキアンのような人々がいます。彼女は自分の活動について非常にオープンですが、現在のところ西側諸国を訪れると逮捕されるリスクが高く、数十年の刑務所生活を送る可能性があります。それはあなたが取る価値のあるリスクですか？私たちはスペクトラムの他の端にいます。痕跡を残さないように非常に注意深く行動し、強力な運用セキュリティを持っています。"

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* HNで「ynno」によって言及されたように、アレクサンドラは最初は知られたくなかった：「彼女のサーバーは、PHPからの詳細なエラーメッセージを発信するように設定されており、フォールトソースファイルのフルパスを含んでいました。これは、彼女がオンラインで使用していたユーザー名に追跡できるもので、彼女の本名に関連付けられていました。この暴露があるまで、彼女は匿名でした。」したがって、この種の作業に使用するコンピュータではランダムなユーザー名を使用してください。何かを誤設定した場合に備えて。"

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "しかし、その秘密主義には心理的なコストが伴います。ほとんどの人は自分の仕事が認められることを愛していますが、現実の世界ではその功績を認められることはできません。友人があなたが何をしているのか尋ねるような単純なことさえも挑戦的です（ある時点で「NAS / ホームラボで遊んでいる」と言うのは古くなります）。"

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "だからこそ、コミュニティを見つけることが非常に重要です。非常に信頼できる親しい友人に打ち明けることで、運用セキュリティを少し犠牲にすることができます。それでも、彼らが当局にメールを提出しなければならない場合や、デバイスが他の方法で侵害されている場合に備えて、何も書面に残さないように注意してください。"

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "さらに良いのは、仲間の海賊を見つけることです。親しい友人が参加に興味を持っているなら、素晴らしいことです！そうでない場合は、オンラインで他の人を見つけることができるかもしれません。残念ながら、これはまだニッチなコミュニティです。これまでのところ、この分野で活動している他の人はほんの一握りしか見つかっていません。良い出発点は、Library Genesisフォーラムやr/DataHoarderのようです。アーカイブチームにも同じ考えを持つ人々がいますが、彼らは法律の範囲内で活動しています（たとえ法律のグレーゾーンであっても）。伝統的な「warez」や海賊シーンにも、同様の考えを持つ人々がいます。"

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "コミュニティを育成し、アイデアを探求する方法についてのアイデアを募集しています。TwitterやRedditで私たちにメッセージを送ってください。フォーラムやチャットグループをホストすることもできるかもしれません。一般的なプラットフォームを使用すると簡単に検閲される可能性があるため、自分たちでホストする必要があります。また、これらの議論を完全に公開する（より多くのエンゲージメントの可能性）か、非公開にする（潜在的な「ターゲット」に私たちが彼らをスクレイプしようとしていることを知らせない）かのトレードオフもあります。それについて考える必要があります。興味がある場合はお知らせください！"

msgid "blog.how-to.projects"
msgstr "プロジェクト"

msgid "blog.how-to.projects.text1"
msgstr "私たちがプロジェクトを行う際には、いくつかの段階があります："

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "ドメイン選択 / 哲学：どこに焦点を当てたいのか、そしてなぜか？あなたのユニークな情熱、スキル、状況をどのように活用できるか？"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "ターゲット選択：どの特定のコレクションをミラーリングしますか？"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "メタデータスクレイピング: ファイル自体をダウンロードせずに、ファイルに関する情報をカタログ化すること。"

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "データ選択: メタデータに基づいて、今すぐアーカイブするのに最も関連性の高いデータを絞り込むこと。すべてを対象にすることも可能ですが、スペースと帯域幅を節約する合理的な方法があることが多いです。"

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "データスクレイピング: 実際にデータを取得すること。"

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "配布: トレントでパッケージ化し、どこかで発表し、人々に広めてもらうこと。"

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "これらは完全に独立したフェーズではなく、後のフェーズからの洞察が前のフェーズに戻ることがあります。例えば、メタデータスクレイピング中に、選択したターゲットがスキルレベルを超える防御メカニズム（IPブロックなど）を持っていることに気づいた場合、別のターゲットを見つけるために戻ることがあります。"

msgid "blog.how-to.projects.domain"
msgstr "1. ドメイン選択 / 哲学"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "保存すべき知識や文化遺産は不足しておらず、圧倒されることがあります。だからこそ、一瞬立ち止まって、自分の貢献が何であるかを考えることがしばしば有用です。"

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "これについて考える方法は人それぞれですが、自分自身に問いかけることができる質問をいくつか紹介します。"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "なぜこれに興味があるのですか？何に情熱を持っていますか？もし、特定のことに関心を持つ人々が集まってアーカイブを行うことができれば、それは多くをカバーすることになります！あなたは、自分の情熱について、平均的な人よりも多くのことを知っているでしょう。例えば、保存すべき重要なデータ、最高のコレクションやオンラインコミュニティなどです。"

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "どのようなスキルを持っていて、それをどのように活用できますか？例えば、オンラインセキュリティの専門家であれば、安全なターゲットのIPブロックを打破する方法を見つけることができます。コミュニティを組織するのが得意であれば、目標に向かって人々を集めることができるかもしれません。ただし、このプロセス全体を通じて良好な運用セキュリティを維持するために、プログラミングの知識があると便利です。"

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "これにどれくらいの時間を割けますか？私たちのアドバイスは、小さく始めて、慣れてきたら大きなプロジェクトに取り組むことですが、すべてを消費することもあります。"

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "どこに焦点を当てると高いレバレッジが得られますか？海賊アーカイブにX時間を費やす場合、どのようにして「コストパフォーマンス」を最大化できますか？"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "これについてどのように考えていますか？他の人が見逃しているかもしれない興味深いアイデアやアプローチを持っているかもしれません。"

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "私たちの場合、特に科学の長期保存に関心がありました。Library Genesisについて知っており、トレントを使用して何度も完全にミラーリングされていることを知っていました。そのアイデアが大好きでした。ある日、私たちの一人がLibrary Genesisで科学の教科書を探そうとしましたが、見つからず、その完全性に疑問を抱きました。それからオンラインでその教科書を検索し、他の場所で見つけ、それが私たちのプロジェクトの種を植えました。Z-ライブラリについて知る前から、すべての本を手動で集めようとするのではなく、既存のコレクションをミラーリングし、それをLibrary Genesisに貢献するというアイデアを持っていました。"

msgid "blog.how-to.projects.target"
msgstr "2. ターゲット選択"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "私たちが注目している分野が決まったので、どの特定のコレクションをミラーリングするか？良いターゲットとなる要素はいくつかあります。"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "大規模"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "ユニーク: 他のプロジェクトで既に十分にカバーされていない。"

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "アクセス可能: メタデータやデータのスクレイピングを防ぐための多層の保護を使用していない。"

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "特別な洞察: このターゲットについて特別な情報を持っている、例えば、特別なアクセス権を持っている、または防御を打破する方法を見つけたなど。これは必須ではありません（私たちの今後のプロジェクトは特別なことをしていません）が、確かに役立ちます！"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "私たちがLibrary Genesis以外のウェブサイトで科学の教科書を見つけたとき、それらがどのようにインターネットに流出したのかを調べようとしました。そしてZ-ライブラリを見つけ、多くの本が最初にそこに現れるわけではないが、最終的にはそこにたどり着くことを理解しました。Library Genesisとの関係や、（金銭的な）インセンティブ構造と優れたユーザーインターフェースについて学び、これらがより完全なコレクションを作り上げていることを知りました。その後、予備的なmetadataとデータスクレイピングを行い、メンバーの一人が多くのプロキシサーバーに特別にアクセスできることを利用して、IPダウンロード制限を回避できることに気付きました。"

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "さまざまなターゲットを探索する際には、VPNや使い捨てのメールアドレスを使用して足跡を隠すことがすでに重要です。これについては後ほど詳しく説明します。"

msgid "blog.how-to.projects.metadata"
msgstr "3. Metadataスクレイピング"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "ここで少し技術的な話をしましょう。ウェブサイトからmetadataを実際にスクレイピングするために、私たちは非常にシンプルな方法を採用しています。Pythonスクリプトや時にはcurlを使用し、結果をMySQLデータベースに保存しています。複雑なウェブサイトをマッピングできる高度なスクレイピングソフトウェアは使用していません。これまでのところ、IDを列挙してHTMLを解析するだけで1種類か2種類のページをスクレイピングする必要があるだけでした。簡単に列挙できるページがない場合は、すべてのページを見つけようとする適切なクローラーが必要かもしれません。"

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "ウェブサイト全体をスクレイピングする前に、少し手動で試してみてください。数十ページを自分で確認し、その仕組みを理解してください。この方法でIPブロックや他の興味深い動作に遭遇することがあります。データスクレイピングについても同様です。このターゲットに深く入り込む前に、実際にデータを効果的にダウンロードできるか確認してください。"

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "制限を回避するために試せることがいくつかあります。同じデータをホストしているが同じ制限がない他のIPアドレスやサーバーはありますか？制限がないAPIエンドポイントはありますか？どのダウンロード速度でIPがブロックされ、どのくらいの期間ブロックされますか？ブロックされずに速度が制限されることはありますか？ユーザーアカウントを作成すると、どのように変わりますか？HTTP/2を使用して接続を開いたままにし、ページをリクエストする速度を上げることはできますか？複数のファイルを一度にリストするページがあり、そこにリストされている情報は十分ですか？"

msgid "blog.how-to.projects.metadata.text4"
msgstr "保存しておくべき情報には、以下のようなものが含まれます："

msgid "blog.how-to.projects.metadata.title"
msgstr "タイトル"

msgid "blog.how-to.projects.metadata.location"
msgstr "ファイル名 / 場所"

msgid "blog.how-to.projects.metadata.id"
msgstr "ID: 内部IDでも構いませんが、ISBNやDOIのようなIDは便利です。"

msgid "blog.how-to.projects.metadata.size"
msgstr "サイズ: 必要なディスク容量を計算するため。"

msgid "blog.how-to.projects.metadata.hash"
msgstr "ハッシュ (md5, sha1): ファイルを正しくダウンロードしたことを確認するため。"

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "追加/変更日: 後で戻ってきて、以前ダウンロードしなかったファイルをダウンロードできるように（ただし、IDやハッシュを使用することもできます）。"

msgid "blog.how-to.projects.metadata.notes"
msgstr "説明、カテゴリ、タグ、著者、言語など。"

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "通常、これを2段階で行います。最初に生のHTMLファイルをダウンロードし、通常はMySQLに直接保存します（小さなファイルがたくさんできるのを避けるため、これについては後で詳しく説明します）。次に、別のステップでそれらのHTMLファイルを実際のMySQLテーブルに解析します。これにより、解析コードにミスを発見した場合でも、すべてを最初から再ダウンロードする必要がなく、新しいコードでHTMLファイルを再処理するだけで済みます。また、処理ステップを並列化するのが簡単なことが多く、時間を節約できます（スクレイピングが実行されている間に処理コードを書くことができ、両方のステップを一度に書く必要がありません）。"

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "最後に、いくつかのターゲットにとってはmetadataスクレイピングがすべてであることに注意してください。適切に保存されていない巨大なmetadataコレクションが存在します。"

msgid "blog.how-to.projects.data"
msgstr "4. データ選択"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "多くの場合、metadataを使用してダウンロードするデータの合理的なサブセットを見つけることができます。最終的にすべてのデータをダウンロードしたい場合でも、最も重要な項目を優先することは有用です。検出されて防御が強化される場合や、ディスクを追加購入する必要がある場合、またはすべてをダウンロードする前に他のことが発生する場合に備えてです。"

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "たとえば、コレクションには同じ基礎リソース（本や映画など）の複数の版があり、そのうちの1つが最高品質としてマークされている場合があります。最初にそれらの版を保存することは理にかなっています。最終的にはすべての版を保存したいかもしれません。なぜなら、場合によってはmetadataが誤ってタグ付けされているか、版間で未知のトレードオフがあるかもしれないからです（たとえば、「最高の版」はほとんどの点で最高ですが、他の点で劣っているかもしれません。たとえば、映画が高解像度であるが字幕がないなど）。"

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "metadataデータベースを検索して興味深いものを見つけることもできます。ホストされている最大のファイルは何で、なぜそれほど大きいのか？最小のファイルは何か？特定のカテゴリ、言語などに関して興味深いまたは予期しないパターンはありますか？重複または非常に似たタイトルはありますか？データが追加された時期にパターンはありますか？たとえば、ある日に多くのファイルが一度に追加されたなど。データセットをさまざまな方法で見ることで多くのことを学ぶことができます。"

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "私たちの場合、Z-ライブラリの本をLibrary Genesisのmd5ハッシュと重複排除することで、多くのダウンロード時間とディスクスペースを節約しました。これは非常にユニークな状況ですが、ほとんどの場合、どのファイルがすでに仲間の海賊によって適切に保存されているかを包括的に示すデータベースは存在しません。これは、どこかの誰かにとって大きなチャンスです。音楽や映画など、すでにトレントサイトで広くシードされているものの定期的な更新概要があれば、海賊ミラーに含める優先度が低くなるでしょう。"

msgid "blog.how-to.projects.scraping"
msgstr "5. データスクレイピング"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "これで、実際にデータを一括でダウンロードする準備が整いました。前述のように、この時点でターゲットの動作や制限をよりよく理解するために、手動でいくつかのファイルをダウンロードしているはずです。しかし、実際に大量のファイルを一度にダウンロードし始めると、まだ驚きが待っているでしょう。"

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "ここでのアドバイスは、主にシンプルに保つことです。まずは、いくつかのファイルをダウンロードすることから始めましょう。Pythonを使用し、その後複数のスレッドに拡張することができます。しかし、時には、データベースから直接Bashファイルを生成し、それを複数のターミナルウィンドウで実行してスケールアップする方が簡単です。ここで言及する価値のある技術的なトリックは、MySQLでOUTFILEを使用することです。mysqld.cnfで「secure_file_priv」を無効にすれば、どこにでも書き込むことができます（Linuxを使用している場合は、AppArmorも無効化/オーバーライドすることを忘れないでください）。"

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "データはシンプルなハードディスクに保存します。手持ちのものから始めて、ゆっくりと拡張していきましょう。何百TBものデータを保存することを考えると圧倒されるかもしれません。そのような状況に直面している場合は、まず良いサブセットを公開し、残りの保存に協力を求めるアナウンスを出しましょう。自分でさらにハードドライブを入手したい場合は、r/DataHoarderにはお得な情報を得るための良いリソースがあります。"

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "ファンシーなファイルシステムについてあまり心配しないようにしましょう。ZFSのようなものを設定することに夢中になるのは簡単です。しかし、技術的な詳細として、多くのファイルを扱うのが苦手なファイルシステムが多いことに注意してください。私たちが見つけたシンプルな回避策は、異なるID範囲やハッシュプレフィックスごとに複数のディレクトリを作成することです。"

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "データをダウンロードした後、可能であればmetadataのハッシュを使用してファイルの整合性を確認してください。"

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. 配布"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "データを手に入れたことで、世界初のターゲットの海賊ミラーを所有することになりました（おそらく）。多くの面で最も難しい部分は終わりましたが、最もリスクの高い部分はまだ先にあります。結局のところ、これまでのところはステルスで、レーダーの下を飛んでいました。良いVPNを常に使用し、個人情報をフォームに入力しない（当然）、特別なブラウザセッション（または別のコンピュータ）を使用するだけで済みました。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "今度はデータを配布しなければなりません。私たちの場合、最初は本をLibrary Genesisに戻すことを考えましたが、すぐにその難しさ（フィクションとノンフィクションの分類）に気付きました。それで、Library Genesisスタイルのトレントを使用して配布することに決めました。既存のプロジェクトに貢献する機会があれば、それは多くの時間を節約することができます。しかし、現在、よく組織された海賊ミラーはあまりありません。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "では、自分でトレントを配布することにしたとしましょう。それらのファイルを小さく保ち、他のウェブサイトでミラーリングしやすくしましょう。その後、自分でトレントをシードしながら匿名性を保つ必要があります。VPN（ポートフォワーディングの有無にかかわらず）を使用するか、シードボックスのために混合ビットコインで支払うことができます。これらの用語のいくつかが何を意味するのかわからない場合は、リスクのトレードオフを理解することが重要なので、読むべきことがたくさんあります。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "トレントファイル自体を既存のトレントウェブサイトにホストすることができます。私たちの場合、実際にウェブサイトをホストすることを選びました。なぜなら、私たちの哲学を明確に広めたいと考えたからです。あなたも同様の方法でこれを行うことができます（私たちはNjallaをドメインとホスティングに使用し、混合ビットコインで支払っています）が、私たちに連絡してトレントをホストしてもらうこともできます。このアイデアが広まれば、時間をかけて海賊ミラーの包括的なインデックスを構築することを目指しています。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "VPNの選択については、すでに多くのことが書かれているので、評判で選ぶという一般的なアドバイスを繰り返すだけにします。実際に裁判でテストされたログなしポリシーで、プライバシーを長期間保護してきた実績があるものが、私たちの意見では最もリスクの低いオプションです。すべてを正しく行っても、リスクをゼロにすることはできないことに注意してください。たとえば、トレントをシードしているとき、非常に動機のある国家主体がVPNサーバーの入出力データフローを見て、あなたが誰であるかを推測することができるかもしれません。または、単に何かを間違えることもあります。私たちはおそらくすでに間違えたことがあり、また間違えるでしょう。幸いなことに、国家は<em>それほど</em>海賊行為に関心を持っていません。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "各プロジェクトで決定することの一つは、以前と同じアイデンティティを使用して公開するかどうかです。同じ名前を使い続けると、以前のプロジェクトでの運用セキュリティのミスが後で問題になる可能性があります。しかし、異なる名前で公開することは、長期的な評判を築かないことを意味します。私たちは、最初から強力な運用セキュリティを持つことを選び、同じアイデンティティを使い続けることができるようにしましたが、間違えたり、状況が求める場合には、異なる名前で公開することをためらいません。"

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "情報を広めることは難しいかもしれません。私たちが言ったように、これはまだニッチなコミュニティです。私たちは最初にRedditに投稿しましたが、実際にはHacker Newsで注目を集めました。今のところ、いくつかの場所に投稿して何が起こるかを見てみることをお勧めします。そして、再び私たちに連絡してください。私たちは、より多くの海賊アーカイブ活動の言葉を広めたいと思っています。"

msgid "blog.how-to.conclusion"
msgstr "結論"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "新たに始める海賊アーカイブ主義者にとって、これが役立つことを願っています。この世界にあなたを迎えることに興奮していますので、遠慮なく連絡してください。できるだけ多くの世界の知識と文化を保存し、それを広くミラーリングしましょう。"

msgid "blog.how-to.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>)"

msgid "blog.introducing.title"
msgstr "海賊図書館ミラーの紹介：Libgenにない7TBの本を保存"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "このプロジェクト（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）は、人類の知識の保存と解放に貢献することを目的としています。私たちは、私たちの前に立った偉人たちの足跡をたどりながら、小さく謙虚な貢献をしています。"

#, fuzzy
msgid "blog.introducing.text2"
msgstr "このプロジェクトの焦点は、その名前によって示されています："

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>海賊</strong> - 私たちは意図的に多くの国で著作権法を侵害しています。これにより、法的な団体ができないことを行うことができます：本が広くミラーされることを確実にすることです。"

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>図書館</strong> - ほとんどの図書館と同様に、私たちは主に書籍のような書かれた資料に焦点を当てています。将来的には他の種類のメディアに拡大するかもしれません。"

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>ミラー</strong> - 私たちは既存の図書館の厳密なミラーです。私たちは保存に焦点を当てており、本を簡単に検索してダウンロードできるようにすること（アクセス）や、新しい本を提供する人々の大きなコミュニティを育成すること（ソーシング）には焦点を当てていません。"

#, fuzzy
msgid "blog.introducing.text3"
msgstr "私たちが最初にミラーした図書館はZ-ライブラリです。これは人気のある（そして違法な）図書館です。彼らはLibrary Genesisのコレクションを取り、それを簡単に検索できるようにしました。その上、彼らは新しい本の提供を奨励することで、非常に効果的に新しい本の提供を募っています。現在、これらの新しい本をLibrary Genesisに戻して提供していません。そして、Library Genesisとは異なり、彼らのコレクションを簡単にミラーできるようにしていないため、広範な保存が妨げられています。これは、彼らがコレクションへのアクセスに対して料金を請求しているため、彼らのビジネスモデルにとって重要です（1日に10冊以上）。"

#, fuzzy
msgid "blog.introducing.text4"
msgstr "違法な書籍コレクションへの一括アクセスに対して料金を請求することについて道徳的な判断はしません。Z-ライブラリが知識へのアクセスを拡大し、より多くの書籍を調達することに成功したことは疑いの余地がありません。私たちは単に私たちの役割を果たしています：このプライベートコレクションの長期保存を確保することです。"

#, fuzzy
msgid "blog.introducing.text5"
msgstr "私たちは、トレントをダウンロードしてシードすることで、人類の知識を保存し解放する手助けをしていただきたいと思っています。データがどのように整理されているかについての詳細は、プロジェクトページをご覧ください。"

#, fuzzy
msgid "blog.introducing.text6"
msgstr "また、次にどのコレクションをミラーするか、そしてそれをどのように行うかについてのアイデアを提供していただきたいと思っています。共に多くを成し遂げることができます。これは無数の他の貢献の中の小さな貢献に過ぎません。あなたがしているすべてのことに感謝します。"

msgid "blog.introducing.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>このブログからファイルへのリンクはしていません。ご自身で見つけてください。</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdbダンプ、またはどれだけの本が永遠に保存されるのか？"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "シャドウライブラリのファイルを適切に重複排除した場合、世界中のすべての本の何パーセントを保存したことになるのでしょうか？"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "海賊図書館ミラー（編集済み：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動）では、世界中のすべての本を取り、永遠に保存することを目指しています。<sup>1</sup> Z-ライブラリのトレントと、元のLibrary Genesisのトレントの間で、11,783,153のファイルがあります。しかし、それは実際にはどれくらいの量なのでしょうか？これらのファイルを適切に重複排除した場合、世界中のすべての本の何パーセントを保存したことになるのでしょうか？私たちは本当にこのようなものを持ちたいと思っています："

msgid "blog.isbndb-dump.10%"
msgstr "人類の文書遺産の約10%%が、恒久的に保存された状態にあります"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "パーセンテージを得るには、分母が必要です：これまでに出版されたすべての本の総数です。<sup>2</sup> Google Booksの終焉前に、このプロジェクトのエンジニアであるレオニード・タイチャーが<a %(booksearch_blogspot)s>この数を推定しようとしました</a>。彼は、舌を巻いて、129,864,880（「少なくとも日曜日まで」）という数を出しました。彼は、世界中のすべての本の統一データベースを構築することでこの数を推定しました。これには、さまざまなデータセットをまとめ、それらをさまざまな方法で統合しました。"

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "ちょっとした余談ですが、世界中のすべての本をカタログ化しようとしたもう一人の人物がいます：故デジタル活動家でRedditの共同創設者であるアーロン・シュワルツです。<sup>3</sup> 彼は<a %(youtube)s>Open Libraryを始めました</a>、目標は「これまでに出版されたすべての本のための1つのウェブページ」であり、多くの異なるソースからのデータを組み合わせました。彼は学術論文を大量にダウンロードしたことで起訴され、最終的には自殺に至るという代償を払いました。言うまでもなく、これが私たちのグループが仮名で活動している理由の一つであり、非常に慎重に行動している理由です。Open Libraryは、インターネットアーカイブの人々によって、アーロンの遺産を引き継ぎ、英雄的に運営されています。この投稿の後半でこれに戻ります。"

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Googleのブログ投稿で、タイチャーはこの数を推定する際のいくつかの課題を説明しています。まず、何が本を構成するのか？いくつかの可能な定義があります："

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>物理的なコピー。</strong> これは同じ資料の複製に過ぎないので、あまり役に立ちません。人々が本に書き込むすべての注釈を保存できたら素晴らしいでしょう。フェルマーの有名な「余白の落書き」のように。しかし、それはアーカイブ担当者の夢のままでしょう。"

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>「作品」。</strong> 例えば、「ハリー・ポッターと秘密の部屋」という論理的な概念で、異なる翻訳や再版など、すべてのバージョンを含むものです。これはある意味で便利な定義ですが、何が含まれるかの線引きが難しいことがあります。例えば、異なる翻訳は保存したいかもしれませんが、わずかな違いしかない再版はそれほど重要ではないかもしれません。"

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>「版」。</strong> ここでは、本のすべてのユニークなバージョンを数えます。異なるカバーや異なる序文など、何かが異なれば、それは異なる版と見なされます。"

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>ファイル。</strong> Library Genesis、Sci-Hub、Z-ライブラリのようなシャドウライブラリを扱う際には、追加の考慮事項があります。同じ版の複数のスキャンが存在することがあります。また、OCRを使用してテキストをスキャンしたり、斜めにスキャンされたページを修正したりして、既存のファイルをより良いバージョンにすることができます。これらのファイルを1つの版としてカウントするには、良いmetadataが必要であり、または文書の類似性測定を使用して重複を排除する必要があります。"

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "「版」は「本」が何であるかの最も実用的な定義のようです。便利なことに、この定義はユニークなISBN番号を割り当てるためにも使用されます。ISBN、または国際標準図書番号は、国際的な商取引で一般的に使用されており、国際バーコードシステム（「国際商品番号」）と統合されています。書店で本を販売したい場合、バーコードが必要なので、ISBNを取得します。"

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycherのブログ投稿では、ISBNは便利ですが、普遍的ではないと述べています。なぜなら、1970年代半ばにしか本格的に採用されておらず、世界中で採用されているわけではないからです。それでも、ISBNはおそらく本の版の最も広く使用されている識別子であり、私たちの最良の出発点です。世界中のすべてのISBNを見つけることができれば、まだ保存が必要な本のリストを得ることができます。"

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "では、データはどこから取得するのでしょうか？世界中のすべての本のリストをまとめようとしている既存の取り組みがいくつかあります。"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google。</strong> 結局のところ、彼らはGoogle Booksのためにこの研究を行いました。しかし、彼らのmetadataは一括でアクセスできず、スクレイピングも難しいです。"

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library。</strong> 前述の通り、これが彼らの全体の使命です。彼らは協力している図書館や国立アーカイブから大量の図書館データを収集し続けています。また、ボランティアの司書や技術チームが記録の重複を排除し、さまざまなmetadataでタグ付けしようとしています。何よりも、彼らのデータセットは完全にオープンです。単に<a %(openlibrary)s>ダウンロード</a>できます。"

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat。</strong> これは非営利のOCLCが運営するウェブサイトで、図書館管理システムを販売しています。彼らは多くの図書館からの書籍metadataを集約し、WorldCatウェブサイトを通じて提供しています。しかし、彼らはこのデータを販売しているため、一括ダウンロードはできません。特定の図書館と協力して、より限定的な一括データセットをダウンロード可能にしています。"

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb。</strong> これはこのブログ投稿のトピックです。ISBNdbはさまざまなウェブサイトから書籍metadataをスクレイピングし、特に価格データを収集し、それを書籍販売者に販売しています。これにより、彼らは市場の他の部分と一致するように書籍の価格を設定できます。ISBNは現在かなり普遍的であるため、彼らは事実上「すべての本のためのウェブページ」を構築しました。"

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>さまざまな個別の図書館システムとアーカイブ。</strong> 上記のいずれにもインデックスされておらず、集約されていない図書館やアーカイブがあります。これはしばしば資金不足のため、または他の理由でOpen Library、OCLC、Googleなどとデータを共有したくないためです。これらの多くはインターネットを通じてアクセス可能なデジタル記録を持っており、しばしばあまり保護されていません。したがって、助けたいと思っていて、奇妙な図書館システムについて学ぶ楽しみを持ちたい場合、これらは素晴らしい出発点です。"

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "この投稿では、私たちの以前のZ-ライブラリのリリースと比較して小さなリリースを発表できることを嬉しく思います。私たちはISBNdbの大部分をスクレイピングし、海賊図書館ミラーのウェブサイトでトレントとしてデータを利用可能にしました（編集：<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に移動しました。ここでは直接リンクしませんが、検索してください）。これらは約3090万件のレコードです（<a %(jsonlines)s>JSON Lines</a>として20GB、圧縮して4.4GB）。彼らのウェブサイトでは実際には3260万件のレコードがあると主張しているので、何かを見逃したか、<em>彼ら</em>が何か間違っている可能性があります。いずれにせよ、今のところ私たちがどのようにそれを行ったかを正確に共有することはありません — それは読者への課題として残します。 ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "私たちが共有するのは、世界中の本の数を推定するための予備的な分析です。私たちは3つのデータセットを調べました：この新しいISBNdbデータセット、Z-ライブラリシャドウライブラリからスクレイピングしたmetadataの元のリリース（Library Genesisを含む）、およびOpen Libraryのデータダンプです。"

msgid "blog.isbndb-dump.text10"
msgstr "まずは大まかな数字から始めましょう："

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Z-ライブラリ/LibgenとOpen Libraryの両方には、ユニークなISBNよりも多くの本があります。これは、多くの本にISBNがないことを意味するのでしょうか、それともISBNのmetadataが単に欠けているのでしょうか？この質問には、他の属性（タイトル、著者、出版社など）に基づく自動マッチングの組み合わせ、より多くのデータソースの取り込み、実際の本のスキャンからのISBNの抽出（Z-ライブラリ/Libgenの場合）でおそらく答えることができます。"

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "それらのISBNのうち、どれだけがユニークなのでしょうか？これはベン図で最もよく示されます："

msgid "blog.isbndb-dump.text13"
msgstr "正確に言うと："

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "私たちは、重複が非常に少ないことに驚きました！ISBNdbには、Z-ライブラリやOpen Libraryのどちらにも表示されない大量のISBNがあり、他の2つについても同様です（小さいながらも依然としてかなりの程度）。これは多くの新しい疑問を引き起こします。ISBNでタグ付けされていない本にタグを付けるために自動マッチングがどれだけ役立つでしょうか？多くのマッチがあり、それによって重複が増えるでしょうか？また、4番目または5番目のデータセットを導入した場合、どれだけの重複が見られるでしょうか？"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "これにより、出発点が得られました。Z-ライブラリのデータセットに含まれていないISBNや、タイトル/著者フィールドとも一致しないものをすべて確認できます。これにより、世界中のすべての本を保存する手がかりが得られます。まずはインターネットからスキャンを収集し、その後、実際に本をスキャンするために外に出ることです。後者はクラウドファンディングで資金を集めたり、特定の本をデジタル化したい人々からの「報奨金」で推進されたりする可能性もあります。これらはまた別の機会にお話しします。"

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "これらのいずれかに協力したい場合—さらなる分析、metadataのスクレイピング、より多くの本の発見、本のOCR、他の分野（例：論文、オーディオブック、映画、テレビ番組、雑誌）での実施、またはML/大規模言語モデルのトレーニング用にデータを利用可能にすること—私に連絡してください (<a %(reddit)s>Reddit</a>)。"

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "データ分析に特に興味がある場合、私たちはデータセットとスクリプトをより使いやすい形式で提供する作業を進めています。ノートブックをフォークして、すぐに試してみることができれば素晴らしいです。"

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "最後に、この作業を支援したい場合は、寄付を検討してください。これは完全にボランティアで運営されており、あなたの貢献が大きな違いを生みます。少しでも助けになります。現在、暗号通貨での寄付を受け付けています。Anna’s Archiveの寄付ページをご覧ください。"

msgid "blog.isbndb-dump.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>)"

msgid "blog.isbndb-dump.fn1"
msgstr "1. 「永遠」の合理的な定義による。 ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. もちろん、人類の書かれた遺産は本だけではありません。特に現代では。この投稿と最近のリリースのために本に焦点を当てていますが、私たちの興味はさらに広がっています。"

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. アーロン・シュワルツについてはもっと多くのことが言えますが、彼がこの物語で重要な役割を果たしているため、簡単に触れておきたいと思います。時間が経つにつれて、彼の名前を初めて知る人が増えるかもしれませんし、その後、自分で深く掘り下げることができるでしょう。"

msgid "blog.critical-window.title"
msgstr "シャドウライブラリーの重要なウィンドウ"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "すでに1PBに近づいているコレクションをどのようにして永続的に保存できると主張できるのでしょうか？"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>中国語版 中文版</a>、<a %(reddit)s>Reddit</a>、<a %(news_ycombinator)s>Hacker News</a>で議論"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Anna’s Archiveでは、コレクションの総サイズがすでに1ペタバイト（1000TB）に近づいており、さらに増加している中で、どのようにして永続的に保存できると主張できるのかとよく質問されます。この記事では、私たちの哲学を見て、人類の知識と文化を保存するという私たちの使命にとって次の10年がなぜ重要なのかを考察します。"

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "過去数か月間のトレントシーダー数で分解されたコレクションの<a %(annas_archive_stats)s>総サイズ</a>。"

msgid "blog.critical-window.priorities"
msgstr "優先事項"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "なぜ私たちは論文や本にそれほど関心を持っているのでしょうか？保存に対する基本的な信念を脇に置いておきましょう—それについては別の投稿を書くかもしれません。では、なぜ論文や本なのでしょうか？答えは簡単です：<strong>情報密度</strong>。"

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "ストレージのメガバイトあたり、書かれたテキストはすべてのメディアの中で最も多くの情報を保存します。私たちは知識と文化の両方を大切にしていますが、前者をより重視しています。全体として、情報密度と保存の重要性の階層はおおよそ次のようになります："

msgid "blog.critical-window.priorities.order.papers"
msgstr "学術論文、ジャーナル、レポート"

msgid "blog.critical-window.priorities.order.organic"
msgstr "DNA配列、植物の種子、微生物サンプルのような有機データ"

msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "ノンフィクション書籍"

msgid "blog.critical-window.priorities.order.code"
msgstr "科学・工学ソフトウェアコード"

msgid "blog.critical-window.priorities.order.measurements"
msgstr "科学的測定、経済データ、企業報告書のような測定データ"

msgid "blog.critical-window.priorities.order.science-websites"
msgstr "科学・工学ウェブサイト、オンラインディスカッション"

msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "ノンフィクション雑誌、新聞、マニュアル"

msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "講演、ドキュメンタリー、ポッドキャストのノンフィクションの書き起こし"

msgid "blog.critical-window.priorities.order.leaks"
msgstr "企業や政府からの内部データ（リーク）"

msgid "blog.critical-window.priorities.order.metadata"
msgstr "メタデータ記録全般（ノンフィクションとフィクション、他のメディア、アート、人々などのレビューを含む）"

msgid "blog.critical-window.priorities.order.geographic"
msgstr "地理データ（例：地図、地質調査）"

msgid "blog.critical-window.priorities.order.transcripts"
msgstr "法的または裁判手続きの書き起こし"

msgid "blog.critical-window.priorities.order.fiction"
msgstr "上記のすべてのフィクションまたはエンターテインメント版"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "このリストの順位はやや恣意的です—いくつかの項目は同点であったり、チーム内で意見が分かれたりしています—そして、重要なカテゴリーをいくつか忘れているかもしれません。しかし、これはおおよそ私たちが優先する順序です。"

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "これらの項目の中には、他のものとあまりにも異なっているために心配する必要がないもの（または他の機関によってすでに処理されているもの）もあります。例えば、有機データや地理データです。しかし、このリストのほとんどの項目は実際に私たちにとって重要です。"

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "私たちの優先順位付けにおけるもう一つの大きな要因は、特定の作品がどれだけ危険にさらされているかです。私たちは、以下のような作品に焦点を当てることを好みます："

msgid "blog.critical-window.priorities.rarity.rare"
msgstr "希少"

msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "特に注目されていない"

msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "特に破壊の危険にさらされている（例：戦争、資金削減、訴訟、政治的迫害による）"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "最後に、私たちは規模を重視します。時間とお金が限られているので、同じくらい価値があり危険にさらされているなら、1,000冊の本を救うよりも10,000冊の本を救うために1か月を費やしたいと思います。"

msgid "blog.critical-window.shadowlib"
msgstr "シャドウライブラリー"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "同様の使命を持ち、同様の優先順位を持つ組織はたくさんあります。実際、図書館、アーカイブ、研究所、博物館、その他の保存を担当する機関があります。それらの多くは、政府、個人、または企業によって十分に資金提供されています。しかし、彼らには一つの大きな盲点があります：法制度です。"

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "ここにシャドウライブラリーの独自の役割と、アンナのアーカイブが存在する理由があります。私たちは他の機関が許可されていないことを行うことができます。今、他の場所で保存することが違法な資料をアーカイブできるわけではありません。いいえ、どんな本、論文、雑誌などであってもアーカイブを構築することは多くの場所で合法です。"

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "しかし、法的なアーカイブがしばしば欠けているのは<strong>冗長性と長寿命</strong>です。どこかの物理的な図書館にしか存在しない本が存在します。単一の企業によって守られているメタデータ記録が存在します。単一のアーカイブにしかマイクロフィルムで保存されていない新聞が存在します。図書館は資金削減を受けることがあり、企業は破産することがあり、アーカイブは爆撃されて焼失することがあります。これは仮定ではありません—これは常に起こっています。"

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Anna’s Archiveで私たちが独自にできることは、大規模に多くの作品のコピーを保存することです。論文、本、雑誌などを収集し、大量に配布することができます。現在はトレントを通じてこれを行っていますが、正確な技術は重要ではなく、時間とともに変わります。重要なのは、世界中に多くのコピーを配布することです。200年以上前のこの引用は今でも真実です："

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>失われたものは回復できませんが、残っているものを保存しましょう。それを公共の目と使用から隔てる金庫や鍵ではなく、事故の手の届かないところに置くようなコピーの増殖によって。</q></em><br>— トーマス・ジェファーソン、1791年"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "パブリックドメインについての簡単な注意。Annaのアーカイブは、世界中の多くの場所で違法とされる活動に特化しているため、パブリックドメインの本のような広く利用可能なコレクションにはこだわりません。法的な団体がそれをよく管理していることが多いです。しかし、時には公開されているコレクションに取り組む理由があります。"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "メタデータレコードはWorldcatのウェブサイトで自由に閲覧できますが、大量にダウンロードすることはできません（私たちが<a %(worldcat_scrape)s>スクレイピング</a>するまでは）。"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "コードはGithubでオープンソースにすることができますが、Github全体を簡単にミラーして保存することはできません（ただし、この特定のケースでは、ほとんどのコードリポジトリの十分に分散されたコピーがあります）。"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Redditは無料で使用できますが、最近、データを大量に必要とするLLMトレーニングの影響で、厳しいスクレイピング対策を導入しました（詳細は後述します）。"

msgid "blog.critical-window.copies"
msgstr "コピーの増殖"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "元の質問に戻りますが、どのようにして私たちのコレクションを永続的に保存できると主張できるのでしょうか？ここでの主な問題は、私たちのコレクションが<a %(torrents_stats)s>急速に成長している</a>ことです。これは、他のオープンデータシャドウライブラリー（Sci-HubやLibrary Genesisのような）によってすでに行われた素晴らしい作業に加えて、大規模なコレクションをスクレイピングしてオープンソース化することによってです。"

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "このデータの成長は、コレクションを世界中でミラーすることを難しくします。データストレージは高価です！しかし、私たちは次の3つのトレンドを観察することで楽観的です。"

msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. 簡単に手に入るものを手に入れました"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "これは、上で議論した私たちの優先事項から直接続くものです。私たちはまず大規模なコレクションを解放することに取り組むことを好みます。今、世界最大のコレクションのいくつかを確保したので、成長ははるかに遅くなると予想しています。"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "まだ小さなコレクションの長い尾があり、新しい本が毎日スキャンされたり出版されたりしていますが、その速度はおそらくはるかに遅くなるでしょう。私たちはまだサイズが2倍または3倍になるかもしれませんが、より長い期間にわたってです。"

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. ストレージコストは指数関数的に下がり続けています"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "執筆時点で、<a %(diskprices)s>ディスクの価格</a>は、新しいディスクで1TBあたり約12ドル、中古ディスクで8ドル、テープで4ドルです。新しいディスクのみを考慮すると、ペタバイトを保存するのに約12,000ドルかかります。私たちのライブラリーが900TBから2.7PBに3倍になると仮定すると、ライブラリー全体をミラーするのに32,400ドルかかります。電気代、他のハードウェアのコストなどを加えると、40,000ドルに丸めましょう。テープを使用すると、15,000ドルから20,000ドル程度です。"

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "一方で<strong>全人類の知識の合計が15,000ドルから40,000ドルというのはお得です</strong>。しかし、特に他の人々の利益のためにトレントをシードし続けることを望む場合、完全なコピーを大量に期待するのは少し高いです。"

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "それが今日です。しかし、進歩は前進します："

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "過去10年間でHDDの価格は1TBあたり約3分の1に削減されており、同様のペースで下がり続ける可能性があります。テープも同様の軌道にあるようです。SSDの価格はさらに速く下がっており、10年後にはHDDの価格を超える可能性があります。"

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "異なるソースからのHDD価格のトレンド（クリックして調査を表示）。"

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "これが続けば、10年後にはコレクション全体をミラーするのに5,000ドルから13,000ドル（3分の1）しかかからないかもしれません。サイズがあまり増えなければさらに少なくなるかもしれません。まだ多くのお金ですが、多くの人にとって手の届くものになるでしょう。そして、次のポイントのおかげでさらに良くなるかもしれません…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. 情報密度の改善"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "現在、私たちは提供された生の形式で本を保存しています。もちろん、圧縮されていますが、多くの場合、ページの大きなスキャンや写真です。"

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "これまでのところ、コレクション全体のサイズを縮小する唯一の選択肢は、より積極的な圧縮や重複排除でした。しかし、十分な節約を得るためには、どちらも私たちの好みにはあまりにも損失が大きすぎます。写真の重い圧縮は、テキストをほとんど読めなくすることがあります。そして、重複排除には、書籍が完全に同じであるという高い信頼性が必要であり、特に内容が同じでもスキャンが異なる場合には、しばしば不正確です。"

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "常に第三の選択肢は存在していましたが、その品質があまりにもひどかったため、考慮に入れたことはありませんでした：<strong>OCR、または光学文字認識</strong>です。これは、AIを使用して写真内の文字を検出し、写真をプレーンテキストに変換するプロセスです。このためのツールは長い間存在しており、かなり優れていますが、「かなり優れている」だけでは保存目的には不十分です。"

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "しかし、最近のマルチモーダルディープラーニングモデルは非常に急速に進歩していますが、依然として高コストです。今後数年で精度とコストの両方が劇的に改善され、私たちの全ライブラリに適用することが現実的になると期待しています。"

msgid "blog.critical-window.ocr"
msgstr "OCRの改善。"

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "その時が来れば、元のファイルを保存し続ける可能性が高いですが、さらに多くの人がミラーしたいと思うような、はるかに小さなバージョンのライブラリを持つことができるかもしれません。重要なのは、生のテキスト自体がさらに圧縮されやすく、重複排除がはるかに簡単であるため、さらに多くの節約ができることです。"

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "全体として、ファイルサイズの合計が少なくとも5〜10倍、場合によってはそれ以上に削減されることを期待するのは非現実的ではありません。保守的に5倍の削減でも、ライブラリが3倍に増えても10年で<strong>1,000〜3,000ドルになると見込まれます</strong>。"

msgid "blog.critical-window.the-window"
msgstr "重要なウィンドウ"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "これらの予測が正確であれば、私たちは<strong>数年待つだけで</strong>、私たちのコレクション全体が広くミラーされるようになるでしょう。したがって、トーマス・ジェファーソンの言葉を借りれば、「事故の手の届かないところに置かれる」ことになります。"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "残念ながら、LLMの出現とそのデータを大量に必要とするトレーニングにより、多くの著作権者が防御的になっています。以前よりもさらに多くのウェブサイトがスクレイピングやアーカイブを難しくし、訴訟が飛び交い、その間にも物理的な図書館やアーカイブは引き続き無視されています。"

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "これらの傾向が悪化し続け、多くの作品がパブリックドメインに入る前に失われることを予想するしかありません。"

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>私たちは保存の革命の前夜にいますが、<q>失われたものは回復できません。</q></strong> シャドウライブラリを運営し、世界中に多くのミラーを作成するのがまだかなり高価であり、アクセスが完全に遮断されていない約5〜10年の重要なウィンドウがあります。"

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "このウィンドウを乗り越えることができれば、人類の知識と文化を永続的に保存することができるでしょう。この時間を無駄にしてはいけません。この重要なウィンドウを閉じさせてはいけません。"

msgid "blog.critical-window.the-window.text6"
msgstr "行きましょう。"

msgid "blog.critical-window.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

msgid "blog.duxiu-exclusive.title"
msgstr "世界最大の中国ノンフィクション書籍コレクションへのLLM企業の独占アクセス"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>中国語版 中文版</a>、<a %(news_ycombinator)s>Hacker Newsで議論する</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>要約:</strong> アンナのアーカイブは、7.5百万冊/350TBの中国ノンフィクション書籍のユニークなコレクションを取得しました — Library Genesisよりも大きいです。高品質のOCRとテキスト抽出と引き換えに、LLM企業に独占アクセスを提供する用意があります。</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "これは短いブログ投稿です。私たちは、取得した膨大なコレクションのOCRとテキスト抽出を手伝ってくれる企業や機関を探しています。独占的な早期アクセスと引き換えに。禁輸期間後、もちろんコレクション全体を公開します。"

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "高品質の学術テキストは、LLMのトレーニングに非常に役立ちます。私たちのコレクションは中国語ですが、英語のLLMのトレーニングにも役立つはずです：モデルはソース言語に関係なく概念と知識をエンコードするようです。"

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "これには、スキャンからテキストを抽出する必要があります。アンナのアーカイブが得るものは何ですか？ユーザーのための書籍の全文検索です。"

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "私たちの目標はLLM開発者の目標と一致しているため、協力者を探しています。適切なOCRとテキスト抽出ができるなら、<strong>このコレクションへの独占的な早期アクセスを1年間提供する用意があります</strong>。パイプラインのコード全体を共有する用意があるなら、コレクションの禁輸期間を延長する用意があります。"

msgid "blog.duxiu-exclusive.example_pages"
msgstr "サンプルページ"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "私たちに優れたパイプラインがあることを証明するために、超伝導体に関する本から始めるためのサンプルページをいくつか用意しました。あなたのパイプラインは、数学、表、チャート、脚注などを適切に処理する必要があります。"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "処理したページを私たちのメールに送ってください。見栄えが良ければ、プライベートでさらに送りますので、それらにも迅速にパイプラインを実行できることを期待しています。満足したら、契約を結ぶことができます。"

msgid "blog.duxiu-exclusive.collection"
msgstr "コレクション"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "コレクションに関する詳細情報です。<a %(duxiu)s>Duxiu</a>は、<a %(chaoxing)s>SuperStar Digital Library Group</a>によって作成された膨大なスキャンされた書籍のデータベースです。ほとんどが学術書で、大学や図書館でデジタルで利用できるようにスキャンされています。英語を話す観客のために、<a %(library_princeton)s>プリンストン</a>と<a %(guides_lib_uw)s>ワシントン大学</a>が良い概要を提供しています。また、背景を詳しく説明した優れた記事もあります：<a %(doi)s>「中国の書籍のデジタル化：SuperStar DuXiu Scholar Search Engineのケーススタディ」</a>（アンナのアーカイブで検索してください）。"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Duxiuの書籍は長い間、中国のインターネットで海賊版として流通してきました。通常、再販業者によって1ドル未満で販売されています。通常、中国版のGoogleドライブを使用して配布されており、しばしばストレージスペースを増やすためにハッキングされています。技術的な詳細は<a %(github_duty_machine)s>こちら</a>と<a %(github_821_github_io)s>こちら</a>で見つけることができます。"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "これらの書籍は半公開的に配布されてきましたが、大量に入手するのは非常に困難です。私たちはこれをTODOリストの上位に置き、フルタイムで数ヶ月を割り当てました。しかし、最近、信じられないほど素晴らしい才能あるボランティアが私たちに連絡を取り、すでにすべての作業を行ったと教えてくれました。彼らは見返りを求めず、長期保存の保証だけを求めて、全コレクションを共有してくれました。本当に驚くべきことです。彼らはこの方法で助けを求めることに同意し、コレクションをOCR化することを求めました。"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "コレクションは7,543,702ファイルです。これはLibrary Genesisのノンフィクション（約530万）よりも多いです。現在の形式での総ファイルサイズは約359TB（326TiB）です。"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "他の提案やアイデアにもオープンです。ぜひご連絡ください。アンナのアーカイブで私たちのコレクション、保存活動、そしてどのように協力できるかについての詳細情報をご覧ください。ありがとうございます！"

msgid "blog.duxiu-exclusive.signoff"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "警告：このブログ投稿は廃止されました。IPFSはまだ本格的に使用する準備ができていないと判断しました。可能な場合はアンナのアーカイブからIPFS上のファイルへのリンクを提供しますが、もはや自分たちでホストすることはなく、他の人がIPFSを使用してミラーリングすることも推奨しません。コレクションの保存を手伝いたい場合は、トレントページをご覧ください。"

msgid "blog.zlib-on-ipfs.title"
msgstr "IPFSでZ-ライブラリをシードするのを手伝ってください"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "アンナのアーカイブでのシャドウライブラリの運営方法"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "シャドウチャリティのための[AWS]はないので、アンナのアーカイブをどのように運営しているのでしょうか？"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "私は<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>を運営しています。これは、Sci-Hub、Library Genesis、Z-ライブラリのような<a %(wikipedia_shadow_library)s>シャドウライブラリ</a>のための世界最大のオープンソース非営利検索エンジンです。私たちの目標は、知識と文化を容易にアクセス可能にし、最終的には世界中の<a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>すべての本をアーカイブし保存する</a>人々のコミュニティを構築することです。"

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "この記事では、このウェブサイトをどのように運営しているか、そして法的に疑わしい地位を持つウェブサイトを運営する際の独自の課題について説明します。シャドウチャリティのための「AWS」は存在しません。"

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>姉妹記事<a %(blog_how_to_become_a_pirate_archivist)s>「海賊アーキビストになる方法」</a>もご覧ください。</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "イノベーショントークン"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "技術スタックから始めましょう。それは意図的に退屈です。Flask、MariaDB、ElasticSearchを使用しています。それがすべてです。検索はほぼ解決された問題であり、再発明するつもりはありません。それに、私たちは<a %(mcfunley)s>イノベーショントークン</a>を他のことに使わなければなりません：当局に取り締まられないことです。"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "では、アンナのアーカイブはどれほど合法または違法なのでしょうか？これは主に法的管轄に依存します。ほとんどの国は何らかの形で著作権を信じており、特定の期間、特定の種類の作品に対して人や企業に独占的な権利を与えます。ちなみに、アンナのアーカイブでは、いくつかの利点がある一方で、全体として著作権は社会にとってマイナスであると考えていますが、それはまた別の話です。"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "特定の作品に対するこの独占的な権利は、この独占の外にいる誰もがそれらの作品を直接配布することを違法としています—私たちも含めて。しかし、アンナのアーカイブはそれらの作品を直接配布する検索エンジンであり（少なくとも私たちのクリーンネットのウェブサイトではありません）、それなら大丈夫ですよね？必ずしもそうではありません。多くの法域では、著作権で保護された作品を配布することが違法であるだけでなく、それを行う場所へのリンクを貼ることも違法です。この典型的な例がアメリカ合衆国のDMCA法です。"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "これはスペクトラムの最も厳しい端です。スペクトラムの他の端には、理論的には著作権法が全く存在しない国があるかもしれませんが、実際には存在しません。ほとんどすべての国には何らかの形で著作権法があります。執行は別の話です。著作権法を執行することに関心のない政府を持つ国はたくさんあります。また、両極端の間に位置する国もあり、著作権で保護された作品の配布を禁止していますが、そのような作品へのリンクを禁止していません。"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "もう一つの考慮事項は、企業レベルでのことです。ある企業が著作権を気にしない法域で運営されていても、その企業自体がリスクを取ることを望まない場合、誰かが苦情を申し立てた時点でウェブサイトを閉鎖するかもしれません。"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "最後に、大きな考慮事項は支払いです。匿名性を保つ必要があるため、従来の支払い方法を使用することはできません。これにより、暗号通貨が残りますが、それをサポートする企業はごく一部です（暗号通貨で支払われる仮想デビットカードもありますが、しばしば受け入れられません）。"

msgid "blog.how-to-run.architecture"
msgstr "システム構成"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "では、あなたのウェブサイトを閉鎖せずにホスティングしてくれる企業を見つけたとしましょう—これらを「自由を愛するプロバイダー」と呼びましょう😄。すぐに、すべてを彼らと一緒にホスティングするのはかなり高価であることに気づくでしょう。そこで、「安価なプロバイダー」を見つけて、実際のホスティングをそこで行い、自由を愛するプロバイダーを通じてプロキシすることを考えるかもしれません。うまくやれば、安価なプロバイダーはあなたが何をホスティングしているのかを知ることはなく、苦情を受けることもありません。"

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "これらすべてのプロバイダーには、いずれにせよ閉鎖されるリスクがあるため、冗長性も必要です。私たちはスタックのすべてのレベルでこれを必要としています。"

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "ある程度自由を愛する企業で興味深い立場を取っているのがCloudflareです。彼らは<a %(blog_cloudflare)s>主張</a>しているのは、ホスティングプロバイダーではなく、ISPのようなユーティリティであるということです。したがって、DMCAや他の削除要求の対象にはならず、実際のホスティングプロバイダーに要求を転送します。この構造を保護するために法廷にまで行ったこともあります。したがって、キャッシングと保護のもう一つの層として彼らを利用することができます。"

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflareは匿名の支払いを受け付けていないため、無料プランしか利用できません。これにより、ロードバランシングやフェイルオーバー機能を使用することはできません。したがって、ドメインレベルで<a %(annas_archive_l255)s>これを自分たちで実装しました</a>。ページが読み込まれると、ブラウザは現在のドメインがまだ利用可能かどうかを確認し、そうでない場合はすべてのURLを別のドメインに書き換えます。Cloudflareが多くのページをキャッシュしているため、ユーザーはプロキシサーバーがダウンしていてもメインドメインにアクセスでき、次のクリックで別のドメインに移動することができます。"

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "また、サーバーの健康状態の監視、バックエンドとフロントエンドのエラーのログ記録など、通常の運用上の懸念にも対処する必要があります。私たちのフェイルオーバーアーキテクチャは、この面でもより堅牢性を提供します。たとえば、ドメインの一つで完全に異なるサーバーセットを実行することによってです。メインバージョンで重大なバグが見逃された場合に備えて、この別のドメインで古いバージョンのコードとデータセットを実行することもできます。"

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Cloudflareが私たちに対して反旗を翻すことに備えて、この別のドメインからCloudflareを削除することで対策を講じることもできます。これらのアイデアの異なる組み合わせが可能です。"

msgid "blog.how-to-run.tools"
msgstr "ツール"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "これを達成するために使用しているツールを見てみましょう。これは新しい問題に直面し、新しい解決策を見つけるにつれて非常に進化しています。"

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "アプリケーションサーバー：Flask、MariaDB、ElasticSearch、Docker。"

msgid "blog.how-to-run.tools.proxy"
msgstr "プロキシサーバー：Varnish。"

msgid "blog.how-to-run.tools.management"
msgstr "サーバー管理：Ansible、Checkmk、UFW。"

msgid "blog.how-to-run.tools.dev"
msgstr "開発：Gitlab、Weblate、Zulip。"

msgid "blog.how-to-run.tools.onion"
msgstr "オニオン静的ホスティング：Tor、Nginx。"

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "いくつかの決定については、行ったり来たりしています。一つはサーバー間の通信です：以前はWireguardを使用していましたが、時折データの送信が停止したり、一方向にしかデータを送信しなかったりすることがわかりました。<a %(github_costela_wesher)s>wesher</a>や<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>など、試したいくつかの異なるWireguardセットアップでこれが発生しました。また、autosshやsshuttleを使用してSSH経由でポートをトンネリングすることも試みましたが、<a %(github_sshuttle)s>問題に直面しました</a>（ただし、autosshがTCP-over-TCPの問題を抱えているかどうかはまだ不明です—私には不安定な解決策のように感じますが、実際には問題ないかもしれません）。"

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "代わりに、サーバー間の直接接続に戻り、UFWを使用して安価なプロバイダーでサーバーが稼働していることを隠しています。これには、DockerがUFWとうまく機能しないという欠点がありますが、<code>network_mode: \"host\"</code>を使用すれば問題ありません。これらすべては、わずかな誤設定でサーバーをインターネットに公開してしまうため、ややエラーが発生しやすくなります。おそらくautosshに戻るべきかもしれません—ここでのフィードバックを非常に歓迎します。"

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "私たちは、VarnishとNginxの間で行ったり来たりしてきました。現在はVarnishを好んでいますが、いくつかの癖や粗さがあります。同じことがCheckmkにも言えます。好きではありませんが、今のところは機能しています。Weblateはまあまあですが、素晴らしいとは言えません。gitリポジトリと同期しようとするたびにデータを失うのではないかと心配になることがあります。Flaskは全体的に良いですが、カスタムドメインの設定やSqlAlchemyの統合に関する問題など、デバッグに多くの時間を費やした奇妙な癖があります。"

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "これまでのところ、他のツールは素晴らしいです。MariaDB、ElasticSearch、Gitlab、Zulip、Docker、Torについては、深刻な不満はありません。これらにはいくつかの問題がありましたが、深刻すぎたり時間がかかりすぎたりするものはありませんでした。"

msgid "blog.how-to-run.conclusions"
msgstr "結論"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "堅牢で回復力のあるシャドウライブラリ検索エンジンをセットアップする方法を学ぶのは興味深い経験でした。今後の投稿で共有する詳細がたくさんありますので、もっと知りたいことがあれば教えてください！"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "いつものように、この作業を支援するための寄付を募っていますので、Annaのアーカイブの寄付ページをぜひご覧ください。また、助成金、長期スポンサー、ハイリスクの支払いプロバイダー、場合によっては（センスの良い！）広告など、他の種類のサポートも探しています。時間やスキルを提供したい方は、開発者や翻訳者などを常に募集しています。ご関心とご支援に感謝します。"

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- アンナとチーム (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "こんにちは、私はアンナです。私は<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>、世界最大のシャドウライブラリを作りました。これは私の個人ブログで、私とチームメイトが海賊版、デジタル保存などについて書いています。"

#, fuzzy
msgid "blog.index.text2"
msgstr "<a %(reddit)s>Reddit</a>で私とつながりましょう。"

#, fuzzy
msgid "blog.index.text3"
msgstr "このウェブサイトはブログだけです。ここでは私たち自身の言葉だけをホストしています。トレントや他の著作権で保護されたファイルはここでホストされておらず、リンクもされていません。"

msgid "blog.index.heading"
msgstr "ブログ投稿"

msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat スクレイピング"

msgid "blog.books-on-ipfs.title"
msgstr "5,998,794冊の本をIPFSに配置"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "警告：このブログ投稿は廃止されました。IPFSはまだ本格的に使用する準備ができていないと判断しました。可能な場合はアンナのアーカイブからIPFS上のファイルへのリンクを提供しますが、もはや自分たちでホストすることはなく、他の人がIPFSを使用してミラーリングすることも推奨しません。コレクションの保存を手伝いたい場合は、トレントページをご覧ください。"

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>要約:</strong> Annaのアーカイブは、保存が必要な本のTODOリストを作成するために、WorldCat（世界最大の図書館メタデータコレクション）をすべてスクレイピングしました。</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "1年前、私たちはこの質問に答えるために<a %(blog)s>取り組み始めました</a>：<strong>シャドウライブラリによって永久に保存された本の割合はどれくらいですか？</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "本が<a %(wikipedia_library_genesis)s>Library Genesis</a>のようなオープンデータのシャドウライブラリ、そして今では<a %(wikipedia_annas_archive)s>Annaのアーカイブ</a>に入ると、それは世界中でミラーリングされ（トレントを通じて）、実質的に永遠に保存されます。"

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "保存された本の割合を答えるには、分母を知る必要があります。つまり、合計で何冊の本が存在するのか？理想的には、単なる数字ではなく、実際のメタデータを持っていることです。そうすれば、シャドウライブラリと照合するだけでなく、<strong>残りの本を保存するためのTODOリストを作成することができます！</strong> さらには、このTODOリストを進めるためのクラウドソースの取り組みを夢見ることもできるかもしれません。"

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "私たちは<a %(wikipedia_isbndb_com)s>ISBNdb</a>をスクレイピングし、<a %(openlibrary)s>Open Library dataset</a>をダウンロードしましたが、結果は満足のいくものではありませんでした。主な問題は、ISBNの重複があまりなかったことです。<a %(blog)s>私たちのブログ記事</a>のこのベン図をご覧ください。"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "私たちは、ISBNdbとOpen Libraryの間にほとんど重複がないことに非常に驚きました。どちらもウェブスクレイピングや図書館の記録など、さまざまなソースからデータを自由に取り入れています。もし両者が外にあるほとんどのISBNを見つけるのが得意であれば、それらの円は確実に大きく重なるか、一方が他方の部分集合であるはずです。これにより、これらの円の完全に外にある本がどれだけあるのか疑問に思いました。私たちはより大きなデータベースが必要です。"

msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "そこで私たちは、世界最大の書籍データベースである<a %(wikipedia_worldcat)s>WorldCat</a>に目を向けました。これは非営利団体<a %(wikipedia_oclc)s>OCLC</a>による独自のデータベースで、世界中の図書館からmetadataレコードを集約し、それに対して図書館に完全なデータセットへのアクセスを提供し、エンドユーザーの検索結果に表示されるようにしています。"

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "OCLCは非営利団体ですが、そのビジネスモデルはデータベースを保護することを必要としています。さて、OCLCの皆さん、申し訳ありませんが、私たちはすべてを公開します。 :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "過去1年間、私たちはWorldCatのすべてのレコードを細心の注意を払ってスクレイピングしてきました。最初は幸運に恵まれました。WorldCatはちょうどウェブサイトの完全なリニューアルを開始したところでした（2022年8月）。これにはバックエンドシステムの大幅なオーバーホールが含まれており、多くのセキュリティの欠陥が導入されました。私たちはすぐにこの機会を捉え、数日で数億ものレコードをスクレイピングすることができました。"

msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCatのリデザイン</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "その後、セキュリティの欠陥は一つずつゆっくりと修正され、最後に見つけたものが約1か月前に修正されました。その時点で私たちはほとんどすべてのレコードを持っており、わずかに高品質なレコードを求めていただけでした。そこで、リリースする時が来たと感じました！"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "データの基本情報を見てみましょう："

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>フォーマットは？</strong> <a %(blog)s>アンナのアーカイブコンテナ（AAC）</a>、これは本質的に<a %(jsonlines)s>JSON Lines</a>を<a %(zstd)s>Zstandard</a>で圧縮し、いくつかの標準化されたセマンティクスを加えたものです。これらのコンテナは、私たちが展開したさまざまなスクレイピングに基づいて、さまざまなタイプのレコードをラップしています。"

msgid "blog.worldcat-scrape.data"
msgstr "データ"

msgid "dyn.buy_membership.error.unknown"
msgstr "不明なエラーが発生しました。%(email)s にスクリーンショットとともに連絡をください。"

msgid "dyn.buy_membership.error.minimum"
msgstr "このコインの最小値は他のコインよりも高くなります。別の期間、又別のコインを選択してください。"

msgid "dyn.buy_membership.error.try_again"
msgstr "リクエストを完了できませんでした。数分後にもう一度お試しください。引き続きこのエラーが起きる場合は%(email)sにスクリーンショットを添付しご連絡ください。"

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "支払い処理にエラーが発生しました。しばらく待ってから再度お試しください。24時間以上問題が続く場合は、スクリーンショットを添えて%(email)sまでご連絡ください。"

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "非表示コメント"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "ファイルの問題: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "より良いバージョン"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "このユーザーを不適切な行動で報告しますか？"

msgid "page.comments.report_abuse"
msgstr "不正行為を報告"

msgid "page.comments.abuse_reported"
msgstr "報告された不正行為:"

msgid "page.comments.reported_abuse_this_user"
msgstr "このユーザーを不正行為で報告しました。"

msgid "page.comments.reply_button"
msgstr "返信"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "<a %(a_login)s>ログイン</a>してください。"

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "コメントを残しました。表示されるまでに少し時間がかかることがあります。"

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "何か問題が発生しました。ページをリロードして、もう一度お試しください。"

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s 影響を受けたページ"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Libgen.rs のノンフィクションでは見ることができません"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Libgen,rs フィクションでは見ることができません"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "libgen.li では見ることができません"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Libgen.liで壊れたと見なされました"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Z-Libraryから消えています"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Z-Libraryで「スパム」としてマーク"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Z-Libraryで「不良ファイル」としてマーク"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "すべてのページをPDFに変換できませんでした"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "このファイルでexiftoolの実行に失敗しました"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "本 (不明)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "本 (ノンフィクション)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "本 (フィクション)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "学術雑誌"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "標準書式"

msgid "common.md5_content_type_mapping.magazine"
msgstr "雑誌"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "漫画"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "音源"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "オーディオブック"

msgid "common.md5_content_type_mapping.other"
msgstr "その他"

msgid "common.access_types_mapping.aa_download"
msgstr "パートナーサーバーからダウンロード"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "外部ダウンロード"

msgid "common.access_types_mapping.external_borrow"
msgstr "外部借入"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "外部借入(印刷不可)"

msgid "common.access_types_mapping.meta_explore"
msgstr "メタデータを検索する"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "トレントに含まれています"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library 中文"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat(ワールドキャット)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "AAへのアップロード"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "チェコのメタデータ"

msgid "common.record_sources_mapping.gbooks"
msgstr "Googleブックス"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "ロシア国立図書館"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "タイトル"

msgid "common.specific_search_fields.author"
msgstr "著者"

msgid "common.specific_search_fields.publisher"
msgstr "出版社"

msgid "common.specific_search_fields.edition_varia"
msgstr "版"

msgid "common.specific_search_fields.year"
msgstr "出版年"

msgid "common.specific_search_fields.original_filename"
msgstr "元のファイル名"

msgid "common.specific_search_fields.description_comments"
msgstr "説明とメタデータのコメント"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "このファイルのパートナーサーバーからのダウンロードは一時的に利用できません。"

msgid "common.md5.servers.fast_partner"
msgstr "高速な内部のサーバー#%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "（おすすめ）"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "（ブラウザの確認や待機リストなし）"

msgid "common.md5.servers.slow_partner"
msgstr "低速な内部のサーバー#%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "（少し速いが待機リストあり）"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "（待機リストなしだが非常に遅い場合あり）"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Library.rs ノンフィクション"

msgid "page.md5.box.download.lgrsfic"
msgstr "Library.rs フィクション"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(上部の「GET」もクリック)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(上部の「GET」をクリック)"

msgid "page.md5.box.download.libgen_ads"
msgstr "その広告には悪意のあるソフトウェアが含まれていることが知られているため、広告ブロッカーを使用するか、広告をクリックしないでください。"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STCファイルはダウンロードが不安定な場合があります)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "TOR上のZ-Library"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(Tor Browserが必要)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "インターネットアーカイブからの情報"

msgid "page.md5.box.download.print_disabled_only"
msgstr "（印刷障害のある利用者のみ）"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(関連するDOIはSci-Hubで公開されていない可能性があります)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "コレクション"

msgid "page.md5.box.download.torrent"
msgstr "トレント"

msgid "page.md5.box.download.bulk_torrents"
msgstr "トレントの一括ダウンロード"

msgid "page.md5.box.download.experts_only"
msgstr "(技術者のみ)"

msgid "page.md5.box.download.aa_isbn"
msgstr "ISBNでAnna's Archiveを検索"

msgid "page.md5.box.download.other_isbn"
msgstr "ISBNでその他のデータベースを検索"

msgid "page.md5.box.download.original_isbndb"
msgstr "ISBNdbでオリジナルのレコードを探す"

msgid "page.md5.box.download.aa_openlib"
msgstr "Open Library IDでAnna's Archiveを検索"

msgid "page.md5.box.download.original_openlib"
msgstr "Open Libraryでオリジナルのレコードを検索"

msgid "page.md5.box.download.aa_oclc"
msgstr "アンナのアーカイブでOCLC(Worldcat)番号で検索"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "WorldCatで元のレコードを見つける"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Anna’s ArchiveでDuXiu SSID番号を検索"

msgid "page.md5.box.download.original_duxiu"
msgstr "DuXiuで手動検索"

msgid "page.md5.box.download.aa_cadal"
msgstr "Anna’s ArchiveでCADAL SSNO番号を検索"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "CADALでオリジナルレコードを探す"

msgid "page.md5.box.download.aa_dxid"
msgstr "Anna’s ArchiveでDuXiu DXID番号を検索"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhostのeBook索引"

msgid "page.md5.box.download.scidb"
msgstr "Anna's Archive 🧬SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(ブラウザの認証は不要)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "チェコのメタデータ %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google ブックス %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "メタデータ"

msgid "page.md5.box.descr_title"
msgstr "説明"

msgid "page.md5.box.alternative_filename"
msgstr "別のファイル名"

msgid "page.md5.box.alternative_title"
msgstr "別のタイトル"

msgid "page.md5.box.alternative_author"
msgstr "別の著者"

msgid "page.md5.box.alternative_publisher"
msgstr "別の出版社"

msgid "page.md5.box.alternative_edition"
msgstr "別の版"

msgid "page.md5.box.alternative_extension"
msgstr "別の拡張子"

msgid "page.md5.box.metadata_comments_title"
msgstr "メタデータのコメント"

msgid "page.md5.box.alternative_description"
msgstr "別の説明"

msgid "page.md5.box.date_open_sourced_title"
msgstr "オープンソース化された日付"

msgid "page.md5.header.scihub"
msgstr "Sci-Hubファイル \"%(id)s\""

msgid "page.md5.header.ia"
msgstr "Internet Archiveのオンライン貸出システムのファイル \"%(id)s\""

msgid "page.md5.header.ia_desc"
msgstr "これはInternet Archiveにあるファイルの記録であり、直接ダウンロードできるファイルではありません。本を借りる（下記リンク）か、<a %(a_request)s>ファイルをリクエストする</a>際にこのURLを使用することができます。"

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "このファイルをお持ちで、Anna’s Archiveにまだない場合は、<a %(a_request)s>アップロード</a>をご検討ください。"

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb%(id)sのメタデータの記録"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library%(id)sのメタデータの記録"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) 番号 %(id)s メタデータレコード"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s メタデータレコード"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s メタデータレコード"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s メタデータレコード"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s メタデータレコード"

msgid "page.md5.header.meta_desc"
msgstr "これはメタデータの記録であり、ダウンロード可能なファイルではありません。<a %(a_request)s>ファイルをリクエストする</a>際にこのURLを使用できます。"

msgid "page.md5.text.linked_metadata"
msgstr "リンクされたレコードからのメタデータ"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Open Libraryでメタデータを改善する"

msgid "page.md5.warning.multiple_links"
msgstr "警告: 複数のリンクされたレコードがあります:"

msgid "page.md5.header.improve_metadata"
msgstr "メタデータを改善"

msgid "page.md5.text.report_quality"
msgstr "ファイルの品質を報告する"

msgid "page.search.results.download_time"
msgstr "ダウンロード時間"

msgid "page.md5.codes.url"
msgstr "URL："

msgid "page.md5.codes.website"
msgstr "ウェブサイト："

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "「%(name)s」をAnna’s Archiveで検索"

msgid "page.md5.codes.code_explorer"
msgstr "コードエクスプローラー："

msgid "page.md5.codes.code_search"
msgstr "コードエクスプローラーで表示 “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "もっと読む…"

msgid "page.md5.tabs.downloads"
msgstr "ダウンロード(%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "貸出(%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "メタデータを探索(%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "コメント (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "リスト(%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "状態(%(count)s)"

msgid "common.tech_details"
msgstr "技術詳細 (英語)"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ このファイルには問題がある可能性があり、ソースライブラリから隠されています。</span> これは著作権者の要求によるもので、より良い代替品が利用できるためですが、ファイル自体の問題である場合もあります。ダウンロードしても問題ないかもしれませんが、まずは代替ファイルを検索することをお勧めします。詳細はこちら:"

msgid "page.md5.box.download.better_file"
msgstr "このファイルのより良いバージョンが %(link)s にあるかもしれません。"

msgid "page.md5.box.issues.text2"
msgstr "このファイルをダウンロードする場合は、信頼できる最新のソフトウェアのみを使用してファイルを開くようにしてください。"

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 高速ダウンロード"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 高速ダウンロード</strong><a %(a_membership)s>メンバー</a>になることで書籍や論文などの長期保存を支援することができます。私達からそのご支援への感謝の気持ちを込めて、高速ダウンロードがご利用可能です。❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "今月寄付すると、速いダウンロードの数が<strong>倍</strong>になります。"

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "今日はあと%(remaining)s回<strong>🚀高速ダウンロード</strong>が使用可能です。メンバーになってくれてありがとうございます！❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 高速ダウンロード</strong> 今日の高速ダウンロードの上限に達しました。"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀高速ダウンロード</strong>最近ダウンロード済みのファイルです。リンクはしばらく有効です。"

msgid "page.md5.box.download.option"
msgstr "オプション #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "（リダイレクトなし）"

msgid "page.md5.box.download.open_in_viewer"
msgstr "（ビューアで開く）"

msgid "layout.index.header.banner.refer"
msgstr "友達を紹介すると、あなたと友達の両方に%(percentage)s%%のボーナス高速ダウンロードがもらえます！"

msgid "layout.index.header.learn_more"
msgstr "詳細を確認…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 低速ダウンロード"

msgid "page.md5.box.download.trusted_partners"
msgstr "信頼できるパートナーから。"

msgid "page.md5.box.download.slow_faq"
msgstr "詳細は<a %(a_slow)s>FAQ</a>をご覧ください。"

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(<a %(a_browser)s>ブラウザの認証</a>が必要な場合がございます。— ダウンロード無制限！)"

msgid "page.md5.box.download.after_downloading"
msgstr "ダウンロード後:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "当社のビューアで開く"

msgid "page.md5.box.external_downloads"
msgstr "外部ダウンロードを表示"

msgid "page.md5.box.download.header_external"
msgstr "外部ダウンロード"

msgid "page.md5.box.download.no_found"
msgstr "ダウンロードが見つかりませんでした。"

msgid "page.md5.box.download.no_issues_notice"
msgstr "すべてのミラーは同じファイルを提供するため、安全に使用できます。 とはいえ、インターネットからファイルをダウンロードするときは常に注意が必要です。 たとえば、デバイスを最新の状態に保つようにしてください。"

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "大きなファイルの場合、中断を防ぐためにダウンロードマネージャーの使用をお勧めします。"

msgid "page.md5.box.download.dl_managers.links"
msgstr "推奨ダウンロードマネージャー: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "ファイルを開くには、ファイル形式に応じて電子書籍リーダーまたはPDFリーダーが必要です。"

msgid "page.md5.box.download.readers.links"
msgstr "推奨電子書籍リーダー: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "アンナのアーカイブオンラインビューア"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "形式間の変換にはオンラインツールを使用してください。"

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "推奨変換ツール: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "PDFとEPUBの両方のファイルをKindleまたはKobo eReaderに送信できます。"

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "推奨ツール: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazonの「Send to Kindle」"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazzの「Send to Kobo/Kindle」"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "著者と図書館を支援する"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "これが気に入っていて、余裕がある場合は、オリジナルを購入するか、著者を直接支援することを検討してください。"

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "これが地元の図書館で利用可能な場合、そこで無料で借りることを検討してください。"

msgid "page.md5.quality.header"
msgstr "ファイル品質"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "このファイルの品質を報告してコミュニティを支援してください！ 🙌"

msgid "page.md5.quality.report_issue"
msgstr "ファイルの問題を報告する (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "素晴らしいファイル品質 (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "コメントを追加 (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "このファイルの何が問題ですか？"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "<a %(a_copyright)s>DMCA / 著作権侵害申告フォーム</a>をご利用ください。"

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "問題の説明 (必須)"

msgid "page.md5.quality.issue_description"
msgstr "問題の説明"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "このファイルのより良いバージョンのMD5 (該当する場合)。"

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "このファイルに非常に近い別のファイル (同じ版、同じファイル拡張子が見つかれば) がある場合は、こちらを記入してください。Anna’s Archiveの外でこのファイルのより良いバージョンを知っている場合は、<a %(a_upload)s>アップロード</a>してください。"

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "URLからmd5を取得できます。例:"

msgid "page.md5.quality.submit_report"
msgstr "報告を送信"

msgid "page.md5.quality.improve_the_metadata"
msgstr "<a %(a_metadata)s>このファイルのメタデータを改善する方法</a>を学びましょう。"

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "ご報告いただきありがとうございます。報告内容はこのページに表示され、適切なモデレーションシステムが整うまで、アンナによって手動でレビューされます。"

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "何か問題が発生しました。ページをリロードして、もう一度お試しください。"

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "このファイルの品質が高い場合、ここで何でも議論できます！そうでない場合は、「ファイルの問題を報告」ボタンを使用してください。"

msgid "page.md5.quality.loved_the_book"
msgstr "この本が大好きでした！"

msgid "page.md5.quality.submit_comment"
msgstr "コメントを残す"

msgid "common.english_only"
msgstr "テキストは英語で以下に続きます。"

msgid "page.md5.text.stats.total_downloads"
msgstr "総ダウンロード数: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "「ファイルMD5」とは、ファイルの内容から計算されるハッシュで、その内容に基づいて合理的に一意です。ここでインデックスされたすべてのシャドウライブラリは、主にMD5を使用してファイルを識別します。"

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "ファイルは複数のシャドウライブラリに表示されることがあります。私たちが編纂したさまざまなデータセットに関する情報は、<a %(a_datasets)s>データセットページ</a>をご覧ください。"

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "これは<a %(a_ia)s>IAの制御デジタル貸出</a>ライブラリによって管理され、アンナのアーカイブによって検索のためにインデックスされたファイルです。私たちが編纂したさまざまなデータセットに関する情報は、<a %(a_datasets)s>データセットページ</a>をご覧ください。"

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "この特定のファイルに関する情報は、その<a %(a_href)s>JSONファイル</a>をご覧ください。"

msgid "page.aarecord_issue.title"
msgstr "🔥 このページの読み込みに問題が発生しました"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "再試行するにはリフレッシュしてください。問題が数時間続く場合は<a %(a_contact)s>お問い合わせ</a>ください。"

msgid "page.md5.invalid.header"
msgstr "見つかりませんでした"

msgid "page.md5.invalid.text"
msgstr "\"%(md5_input)s\" は、私たちのデータベースで見つかりませんでした。"

msgid "page.login.title"
msgstr "ログイン/登録"

msgid "page.browserverification.header"
msgstr "ブラウザ検証"

msgid "page.login.text1"
msgstr "スパムボットがアカウントを大量に作成するのを防止するため、まずあなたのブラウザを認証する必要があります。"

msgid "page.login.text2"
msgstr "無限ループが発生した場合は、<a %(a_privacypass)s>Privacy Pass</a>をインストールすることをお勧めします。"

msgid "page.login.text3"
msgstr "広告ブロッカーや他のブラウザ拡張機能をオフにすることも役立つかもしれません。"

msgid "page.codes.title"
msgstr "コード"

msgid "page.codes.heading"
msgstr "コードエクスプローラー"

#, fuzzy
msgid "page.codes.intro"
msgstr "プレフィックスごとにタグ付けされたレコードのコードを探索します。「レコード」列は、検索エンジンで見られるプレフィックス付きのコードでタグ付けされたレコードの数を示します（メタデータのみのレコードを含む）。「コード」列は、特定のプレフィックスを持つ実際のコードの数を示します。"

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "このページの生成には時間がかかる場合があるため、Cloudflareのキャプチャが必要です。<a %(a_donate)s>メンバー</a>はキャプチャをスキップできます。"

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "これらのページをスクレイピングしないでください。代わりに、<a %(a_import)s>生成</a>または<a %(a_download)s>ダウンロード</a>して、ElasticSearchとMariaDBデータベースを使用し、<a %(a_software)s>オープンソースコード</a>を実行することをお勧めします。生データは<a %(a_json_file)s>このような</a>JSONファイルを通じて手動で探索できます。"

msgid "page.codes.prefix"
msgstr "プレフィックス"

msgid "common.form.go"
msgstr "移動"

msgid "common.form.reset"
msgstr "リセット"

msgid "page.codes.search_archive_start"
msgstr "アンナのアーカイブを検索"

msgid "page.codes.bad_unicode"
msgstr "警告: コードに不正なUnicode文字が含まれており、さまざまな状況で正しく動作しない可能性があります。生のバイナリはURLのbase64表現からデコードできます。"

msgid "page.codes.known_code_prefix"
msgstr "既知のコードプレフィックス <q>%(key)s</q>"

msgid "page.codes.code_prefix"
msgstr "プレフィックス"

msgid "page.codes.code_label"
msgstr "ラベル"

msgid "page.codes.code_description"
msgstr "説明"

msgid "page.codes.code_url"
msgstr "特定のコードのURL"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "「%%」はコードの値に置き換えられます"

msgid "page.codes.generic_url"
msgstr "汎用URL"

msgid "page.codes.code_website"
msgstr "ウェブサイト"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "「%(prefix_label)s」に一致する%(count)s件のレコード"

#, fuzzy
msgid "page.codes.url_link"
msgstr "特定のコードのURL: 「%(url)s」"

#, fuzzy
msgid "page.codes.more"
msgstr "もっと…"

msgid "page.codes.codes_starting_with"
msgstr "<q>%(prefix_label)s</q>で始まるコード"

msgid "page.codes.index_of_dir_path"
msgstr "インデックス"

msgid "page.codes.records_prefix"
msgstr "レコード"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "コード"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "%(count)s件未満のレコード"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "DMCA / 著作権クレームについては<a %(a_copyright)s>このフォーム</a>を使用してください。"

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "著作権クレームに関する他の連絡方法は自動的に削除されます。"

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "ご意見やご質問をお待ちしております！"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "ただし、スパムや無意味なメールが多いため、以下のボックスをチェックして、当サイトへの連絡条件を理解したことを確認してください。"

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "このメールへの著作権主張は無視されます。代わりにフォームをご利用ください。"

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "パートナーサーバーはホスティングの閉鎖により利用できません。まもなく再開される予定です。"

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "メンバーシップはそれに応じて延長されます。"

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "<a %(a_request)s>本のリクエスト</a>や小さな（<10k）<a %(a_upload)s>アップロード</a>のためにメールを送らないでください。"

#, fuzzy
msgid "page.donate.please_include"
msgstr "アカウントや寄付に関する質問をする際には、アカウントID、スクリーンショット、領収書など、できるだけ多くの情報を添付してください。メールは1〜2週間に一度しか確認しないため、これらの情報が含まれていないと解決が遅れる可能性があります。"

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "メールを表示"

msgid "page.copyright.title"
msgstr "DMCA / 著作権侵害申告フォーム"

#, fuzzy
msgid "page.copyright.intro"
msgstr "DMCAやその他の著作権侵害の申告がある場合は、このフォームにできるだけ正確に記入してください。問題が発生した場合は、専用のDMCAアドレス：%(email)sまでご連絡ください。このアドレスに送信された申告は処理されませんので、質問のみ受け付けます。申告を提出するには、以下のフォームをご利用ください。"

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "Anna’s ArchiveのURL（必須）。1行に1つずつ。同じ版の書籍を示すURLのみを含めてください。複数の書籍や複数の版について申告する場合は、このフォームを複数回提出してください。"

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "複数の書籍や版をまとめて申告する場合は、却下されます。"

msgid "page.copyright.form.name"
msgstr "お名前（必須）"

msgid "page.copyright.form.address"
msgstr "住所（必須）"

msgid "page.copyright.form.phone"
msgstr "電話番号（必須）"

msgid "page.copyright.form.email"
msgstr "Eメール（必須）"

msgid "page.copyright.form.description"
msgstr "資料の明確な説明（必須）"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "資料のISBN（該当する場合）。1行に1つずつ。著作権侵害を報告する版と完全に一致するもののみを含めてください。"

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a>の資料のURL。1行に1つずつ。Open Libraryで資料を検索する時間を取ってください。これにより、申告の確認が容易になります。"

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "資料のURL。1行に1つずつ（必須）。確認を容易にするため、できるだけ多く含めてください（例：Amazon、WorldCat、Google Books、DOI）。"

msgid "page.copyright.form.statement"
msgstr "声明と署名（必須）"

msgid "page.copyright.form.submit_claim"
msgstr "申告を提出"

msgid "page.copyright.form.on_success"
msgstr "✅ 著作権侵害の申告を提出していただき、ありがとうございます。できるだけ早く確認いたします。別の申告を行うには、ページをリロードしてください。"

msgid "page.copyright.form.on_failure"
msgstr "❌ 問題が発生しました。ページをリロードして、再度お試しください。"

msgid "page.datasets.title"
msgstr "データセット"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "このデータセットを<a %(a_archival)s>アーカイブ</a>または<a %(a_llm)s>LLMトレーニング</a>の目的でミラーリングすることに興味がある場合は、お問い合わせください。"

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "私たちの使命は、世界中のすべての本（論文、雑誌なども含む）をアーカイブし、それらを広くアクセス可能にすることです。すべての本は広範にミラーリングされ、冗長性と回復力を確保するべきだと信じています。これが、さまざまなソースからファイルを集めている理由です。いくつかのソースは完全にオープンで、大量にミラーリングできます（例えば、Sci-Hub）。他のソースは閉鎖的で保護的なので、それらの本を「解放」するためにスクレイピングを試みます。さらに他のソースはその中間に位置します。"

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "私たちのすべてのデータは<a %(a_torrents)s>トレント</a>でダウンロードでき、すべてのメタデータはElasticSearchおよびMariaDBデータベースとして<a %(a_anna_software)s>生成</a>または<a %(a_elasticsearch)s>ダウンロード</a>できます。生データは<a %(a_dbrecord)s>こちら</a>のようなJSONファイルを通じて手動で探索できます。"

msgid "page.datasets.overview.title"
msgstr "概要"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "以下は、アンナのアーカイブにあるファイルのソースの簡単な概要です。"

msgid "page.datasets.overview.source.header"
msgstr "ソース"

msgid "page.datasets.overview.size.header"
msgstr "サイズ"

msgid "page.datasets.overview.mirrored.header"
msgstr "AAによってミラーリングされた%% / トレントが利用可能"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "ファイル数の割合"

msgid "page.datasets.overview.last_updated.header"
msgstr "最終更新日"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "ノンフィクションとフィクション"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s ファイル"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Libgen.li「scimag」経由"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: 2021年以降凍結; ほとんどがトレントで利用可能"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: それ以降の小さな追加</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "「scimag」を除く"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "フィクションのトレントは遅れています（ただし、IDが約4-6MのものはZlibのトレントと重複しているためトレント化されていません）。"

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Z-Libraryの「中国語」コレクションは、MD5が異なるだけで、私たちのDuXiuコレクションと同じようです。重複を避けるためにこれらのファイルをトレントから除外しますが、検索インデックスには表示されます。"

msgid "common.record_sources_mapping.iacdl"
msgstr "IA コントロールデジタルレンディング"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%以上のファイルが検索可能です。"

msgid "page.datasets.overview.total"
msgstr "合計"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "重複を除外"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "シャドウライブラリはしばしば互いにデータを同期するため、ライブラリ間でかなりの重複があります。そのため、数値が合計に一致しないのです。"

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "「Anna’s Archiveによってミラーおよびシードされた」割合は、私たちが自分たちでミラーしているファイルの数を示しています。これらのファイルはトレントを通じて一括でシードされ、パートナーウェブサイトを通じて直接ダウンロード可能にしています。"

msgid "page.datasets.source_libraries.title"
msgstr "ソースライブラリ"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "一部のソースライブラリは、トレントを通じてデータを大量に共有することを推奨していますが、他のライブラリはコレクションを容易に共有しません。後者の場合、Anna’s Archiveはコレクションをスクレイピングし、利用可能にしようとします（詳細は<a %(a_torrents)s>トレント</a>ページをご覧ください）。また、ソースライブラリが共有に前向きであるが、リソースが不足している場合もあります。そのような場合、私たちも支援を試みます。"

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "以下は、異なるソースライブラリとどのようにインターフェースしているかの概要です。"

msgid "page.datasets.sources.source.header"
msgstr "ソース"

msgid "page.datasets.sources.files.header"
msgstr "ファイル"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s 毎日の<a %(dbdumps)s>HTTPデータベースダンプ</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s <a %(nonfiction)s>ノンフィクション</a>と<a %(fiction)s>フィクション</a>の自動トレント"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s アンナのアーカイブは<a %(covers)s>ブックカバートレント</a>のコレクションを管理しています"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hubは2021年以降、新しいファイルを凍結しています。"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s メタデータダンプは<a %(scihub1)s>こちら</a>と<a %(scihub2)s>こちら</a>で利用可能であり、<a %(libgenli)s>Libgen.liデータベース</a>の一部としても利用可能です（私たちが使用しています）"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s データトレントは<a %(scihub1)s>こちら</a>、<a %(scihub2)s>こちら</a>、および<a %(libgenli)s>こちら</a>で利用可能です"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s 一部の新規ファイルはLibgenの「scimag」に<a %(libgenrs)s>追加中</a>ですが、<a %(libgenli)s>新たなトレントを作成するには不十分です</a>。"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s 四半期ごとの<a %(dbdumps)s>HTTPデータベースダンプ</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s ノンフィクショントレントはLibgen.rsと共有されています（<a %(libgenli)s>こちら</a>でミラーされています）。"

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s アンナのアーカイブとLibgen.liは共同で<a %(comics)s>コミック</a>、<a %(magazines)s>雑誌</a>、<a %(standarts)s>標準文書</a>、および<a %(fiction)s>フィクション（Libgen.rsから分岐）</a>のコレクションを管理しています。"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s 彼らの「fiction_rus」コレクション（ロシアのフィクション）は専用のトレントがありませんが、他のトレントでカバーされており、私たちは<a %(fiction_rus)s>ミラー</a>を保持しています。"

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s アンナのアーカイブとZ-Libraryは、<a %(metadata)s>Z-Libraryメタデータ</a>と<a %(files)s>Z-Libraryファイル</a>のコレクションを共同で管理しています"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s 一部のメタデータは<a %(openlib)s>Open Libraryデータベースダンプ</a>を通じて利用可能ですが、それらはIAコレクション全体をカバーしていません"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s 彼らのコレクション全体に対する簡単にアクセスできるメタデータダンプはありません"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s アンナのアーカイブは、<a %(ia)s>IAメタデータ</a>のコレクションを管理しています"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s ファイルは、さまざまなアクセス制限がある中で、限られた期間のみ借用可能です"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s アンナのアーカイブは<a %(ia)s>IAファイル</a>のコレクションを管理しています"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s 中国のインターネットに散在する様々なメタデータデータベース；ただし、しばしば有料のデータベース"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s 彼らの全コレクションに対して簡単にアクセスできるメタデータダンプは利用できません。"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s アンナのアーカイブは<a %(duxiu)s>DuXiuメタデータ</a>のコレクションを管理しています。"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s 中国のインターネット上に散在するさまざまなファイルデータベース；ただし、しばしば有料データベース"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s ほとんどのファイルはプレミアムBaiduYunアカウントを使用しないとアクセスできません；ダウンロード速度が遅いです。"

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s アンナのアーカイブは<a %(duxiu)s>DuXiuファイル</a>のコレクションを管理しています。"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s さまざまな小規模または一時的なソース。私たちは他のシャドウライブラリに最初にアップロードすることを奨励していますが、時には他の人が整理するには大きすぎるコレクションを持っている人もいますが、それほど大きくないため独自のカテゴリを設けるには至りません。"

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "メタデータのみのソース"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "また、メタデータのみのソースを使用してコレクションを充実させています。これらは、ISBN番号や他のフィールドを使用してファイルと一致させることができます。以下はその概要です。再度、これらのソースの一部は完全にオープンである一方、他のソースはスクレイピングが必要です。"

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "私たちがメタデータを収集するインスピレーションは、アーロン・スワーツの「これまでに出版されたすべての本のための1つのウェブページ」という目標であり、彼はそのために<a %(a_openlib)s>Open Library</a>を作成しました。そのプロジェクトは成功していますが、私たちの独自の立場により、彼らが取得できないメタデータを入手することができます。もう一つのインスピレーションは、<a %(a_blog)s>世界にどれだけの本があるのか</a>を知りたいという願望であり、それによってまだ保存すべき本の数を計算することができます。"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "メタデータ検索では、元のレコードを表示することに注意してください。レコードのマージは行いません。"

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "最終更新"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s 毎月の<a %(dbdumps)s>データベースダンプ</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s 直接大量には利用できず、スクレイピングから保護されています"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s アンナのアーカイブは<a %(worldcat)s>OCLC (WorldCat)メタデータ</a>のコレクションを管理しています。"

msgid "page.datasets.unified_database.title"
msgstr "統合データベース"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "上記のすべてのソースを1つの統合データベースに結合し、このウェブサイトで使用しています。この統合データベースは直接利用できませんが、Anna’s Archiveは完全にオープンソースであるため、比較的簡単に<a %(a_generated)s>生成</a>または<a %(a_downloaded)s>ダウンロード</a>してElasticSearchおよびMariaDBデータベースとして利用できます。そのページのスクリプトは、上記のソースから必要なメタデータを自動的にダウンロードします。"

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "これらのスクリプトをローカルで実行する前にデータを探索したい場合は、JSONファイルを確認できます。これらのファイルは他のJSONファイルにリンクしています。<a %(a_json)s>このファイル</a>が良い出発点です。"

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "私たちの<a %(a_href)s>ブログ記事</a>からの抜粋です。"

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a>は、<a %(superstar_link)s>SuperStar Digital Library Group</a>によって作成された大規模なスキャン書籍データベースです。ほとんどが学術書であり、大学や図書館でデジタルで利用できるようにスキャンされています。英語を話す読者のために、<a %(princeton_link)s>プリンストン大学</a>と<a %(uw_link)s>ワシントン大学</a>が良い概要を提供しています。また、背景情報を提供する優れた記事もあります：<a %(article_link)s>「中国の書籍のデジタル化：SuperStar DuXiu Scholar Search Engineのケーススタディ」</a>。"

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Duxiuの書籍は長い間、中国のインターネット上で海賊版として流通してきました。通常、再販業者によって1ドル未満で販売されています。これらは通常、中国版のGoogle Driveを使用して配布されており、しばしばより多くのストレージスペースを確保するためにハッキングされています。いくつかの技術的な詳細は<a %(link1)s>こちら</a>と<a %(link2)s>こちら</a>で見つけることができます。"

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "これらの書籍は半公開で配布されてきましたが、大量に入手するのは非常に困難です。私たちはこれをTODOリストの上位に置き、フルタイムで数ヶ月間取り組むことにしました。しかし、2023年後半に信じられないほど素晴らしく才能のあるボランティアが私たちに連絡を取り、すでにこの作業をすべて行ったと教えてくれました—多大な費用をかけて。彼らは長期保存の保証を除いて何も期待せずに、全コレクションを私たちと共有してくれました。本当に驚くべきことです。"

msgid "page.datasets.common.resources"
msgstr "リソース"

msgid "page.datasets.common.total_files"
msgstr "総ファイル数: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "総ファイルサイズ: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Anna’s Archiveによってミラーリングされたファイル: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "最終更新日: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "アンナのトレント"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Anna’s Archiveの例レコード"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "このデータに関する私たちのブログ記事"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "メタデータをインポートするためのスクリプト"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containersフォーマット"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "ボランティアからの詳細情報（生のメモ）："

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA制御デジタル貸出"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "このデータセットは<a %(a_datasets_openlib)s>Open Libraryデータセット</a>と密接に関連しています。IAの制御デジタル貸出図書館からのすべてのメタデータと多くのファイルのスクレイプが含まれています。更新は<a %(a_aac)s>Anna’s Archive Containers形式</a>でリリースされます。"

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "これらのレコードはOpen Libraryデータセットから直接参照されていますが、Open Libraryにないレコードも含まれています。また、コミュニティメンバーが長年にわたってスクレイプした多数のデータファイルもあります。"

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "コレクションは2つの部分で構成されています。すべてのデータを取得するには両方の部分が必要です（トレントページで取り消されたトレントを除く）。"

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "最初のリリースでは、<a %(a_aac)s>Anna’s Archive Containers (AAC) フォーマット</a>を標準化する前のものです。メタデータ（jsonおよびxml形式）、pdf（acsmおよびlcpdfデジタル貸出システムから）、およびカバーサムネイルが含まれています。"

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "AACを使用した増分リリース。2023-01-01以降のタイムスタンプを持つメタデータのみが含まれています。それ以前のものはすでに「ia」によってカバーされています。また、今回はacsmおよび「bookreader」（IAのウェブリーダー）貸出システムからのすべてのpdfファイルも含まれています。名前が正確ではないにもかかわらず、bookreaderファイルをia2_acsmpdf_filesコレクションに追加しています。これらは相互排他的です。"

msgid "page.datasets.common.main_website"
msgstr "メイン%(source)sウェブサイト"

msgid "page.datasets.ia.ia_lending"
msgstr "デジタル貸出図書館"

msgid "page.datasets.common.metadata_docs"
msgstr "メタデータドキュメント（ほとんどのフィールド）"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN国情報"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "国際ISBN機関は、各国のISBN機関に割り当てた範囲を定期的に公開しています。これにより、このISBNがどの国、地域、または言語グループに属するかを特定できます。現在、このデータはPythonライブラリの<a %(a_isbnlib)s>isbnlib</a>を通じて間接的に使用しています。"

msgid "page.datasets.isbn_ranges.resources"
msgstr "リソース"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "最終更新日: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBNウェブサイト"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "メタデータ"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "異なるLibrary Genesisフォークの背景については、<a %(a_libgen_rs)s>Libgen.rs</a>のページを参照してください。"

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.liには、Libgen.rsとほぼ同じコンテンツとメタデータが含まれていますが、これに加えてコミック、雑誌、標準文書のコレクションがあります。また、<a %(a_scihub)s>Sci-Hub</a>をメタデータと検索エンジンに統合しており、これを私たちのデータベースで使用しています。"

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "このライブラリのメタデータは<a %(a_libgen_li)s>libgen.liで</a>無料で利用できます。ただし、このサーバーは遅く、接続が切れた場合の再開をサポートしていません。同じファイルは<a %(a_ftp)s>FTPサーバー</a>でも利用可能で、こちらの方が動作が良好です。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "追加コンテンツのほとんどにはトレントが利用可能で、特にコミック、雑誌、標準文書のトレントはアンナのアーカイブとの共同でリリースされています。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "フィクションコレクションには独自のトレントがあり（<a %(a_href)s>Libgen.rs</a>から分岐）、%(start)sから始まります。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Libgen.liの管理者によると、「fiction_rus」（ロシアのフィクション）コレクションは、<a %(a_booktracker)s>booktracker.org</a>から定期的にリリースされるトレントでカバーされるべきであり、特に<a %(a_flibusta)s>flibusta</a>と<a %(a_librusec)s>lib.rus.ec</a>のトレント（私たちは<a %(a_torrents)s>ここ</a>でミラーしていますが、どのトレントがどのファイルに対応しているかはまだ確立していません）。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "すべてのコレクションの統計は<a %(a_href)s>libgenのウェブサイト</a>で見つけることができます。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "ノンフィクションもまた分岐しているようですが、新しいトレントはありません。これは2022年初頭から起こっているようですが、まだ確認はしていません。"

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "トレントのない特定の範囲（例えば、フィクションの範囲f_3463000からf_4260000）は、おそらくZ-Library（または他の重複）ファイルである可能性が高いですが、これらの範囲でlgli-ユニークなファイルのために重複排除を行い、トレントを作成したいかもしれません。"

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "「libgen.is」に言及しているトレントファイルは、<a %(a_libgen)s>Libgen.rs</a>のミラーであることを明示しています（「.is」はLibgen.rsが使用する別のドメインです）。"

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "メタデータの使用に役立つリソースは<a %(a_href)s>このページ</a>です。"

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Anna’s Archiveのフィクショントレント"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Anna’s Archiveのコミックトレント"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Anna’s Archiveの雑誌トレント"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "アンナのアーカイブの標準文書トレント"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "アンナのアーカイブのロシアのフィクショントレント"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "メタデータ"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "FTP経由のメタデータ"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "メタデータフィールド情報"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "他のトレントのミラー（およびユニークなフィクションとコミックのトレント）"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "ディスカッションフォーラム"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "コミックブックリリースに関する私たちのブログ投稿"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Library Genesis（または「Libgen」）の異なるフォークの簡単な話は、時間が経つにつれて、Library Genesisに関わる異なる人々が意見の相違を起こし、別々の道を歩んだということです。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "「.fun」バージョンは、元の創設者によって作成されました。新しい、より分散型のバージョンに向けて改良されています。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "「.rs」バージョンは非常に似たデータを持ち、コレクションを一括トレントで最も一貫してリリースしています。大まかに「フィクション」と「ノンフィクション」のセクションに分かれています。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "元の場所は「http://gen.lib.rus.ec」です。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>「.li」バージョン</a>は、コミックの膨大なコレクションを持っており、他のコンテンツも含まれていますが、トレントを通じて一括ダウンロードすることは（まだ）できません。フィクション書籍の別のトレントコレクションがあり、データベースには<a %(a_scihub)s>Sci-Hub</a>のメタデータが含まれています。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "この<a %(a_mhut)s>フォーラム投稿</a>によると、Libgen.liは元々「http://free-books.dontexist.com」でホストされていました。"

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a>もある意味ではLibrary Genesisのフォークですが、彼らはプロジェクトに別の名前を使用しました。"

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "このページは「.rs」バージョンについてです。このバージョンは、メタデータと書籍カタログの全内容を一貫して公開することで知られています。書籍コレクションはフィクションとノンフィクションに分かれています。"

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "メタデータの使用に役立つリソースは<a %(a_metadata)s>このページ</a>です（IP範囲をブロックするため、VPNが必要な場合があります）。"

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "2024年3月現在、新しいトレントは<a %(a_href)s>このフォーラムスレッド</a>に投稿されています（IP範囲をブロックするため、VPNが必要な場合があります）。"

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "アンナのアーカイブのノンフィクショントレント"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "アンナのアーカイブのフィクショントレント"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs メタデータ"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs メタデータフィールド情報"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs ノンフィクショントレント"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs フィクショントレント"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs ディスカッションフォーラム"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "アンナのアーカイブによるトレント（ブックカバー）"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "ブックカバーリリースに関するブログ"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesisは、すでにトレントを通じてデータを一括で提供していることで知られています。私たちのLibgenコレクションは、彼らが直接リリースしない補助データで構成されており、彼らとのパートナーシップのもとで提供されています。Library Genesisの皆様、ご協力いただきありがとうございます！"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "リリース1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "この<a %(blog_post)s>最初のリリース</a>はかなり小規模です：Libgen.rsフォークからの約300GBのブックカバー、フィクションとノンフィクションの両方が含まれています。これらはlibgen.rsに表示されるのと同じ方法で整理されています。例えば："

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "ノンフィクション書籍の場合は%(example)s。"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "フィクション書籍の場合は%(example)s。"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Z-Libraryコレクションと同様に、すべてを大きな.tarファイルにまとめました。ファイルを直接提供したい場合は、<a %(a_ratarmount)s>ratarmount</a>を使用してマウントできます。"

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a>は、非営利団体<a %(a_oclc)s>OCLC</a>による独自のデータベースで、世界中の図書館からメタデータレコードを集約しています。これはおそらく世界最大の図書館メタデータコレクションです。"

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "2023年10月、初回リリース:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "2023年10月に、OCLC (WorldCat)データベースの包括的なスクレイプを<a %(a_scrape)s>リリース</a>し、<a %(a_aac)s>アンナのアーカイブコンテナ形式</a>で提供しました。"

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "アンナのアーカイブによるトレント"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "このデータに関する私たちのブログ投稿"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Libraryは、世界中のすべての本をカタログ化することを目的としたInternet Archiveのオープンソースプロジェクトです。世界最大級の書籍スキャン作業を行っており、多くの書籍がデジタル貸出可能です。その書籍メタデータカタログは無料でダウンロード可能で、アンナのアーカイブにも含まれています（ただし、現在のところ検索には含まれていません。Open Library IDを明示的に検索した場合を除きます）。"

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "メタデータ"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "リリース1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "これは、2022年9月中にisbndb.comに多数のリクエストを送信した結果のダンプです。すべてのISBN範囲をカバーしようとしました。約3090万件のレコードがあります。彼らのウェブサイトでは実際には3260万件のレコードがあると主張しているので、何かを見逃したか、または<em>彼ら</em>が何か間違っている可能性があります。"

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSONレスポンスはほぼサーバーからの生データです。気付いたデータ品質の問題の一つは、「978-」以外のプレフィックスで始まるISBN-13番号に対しても、「isbn」フィールドが単に最初の3桁を切り取った（およびチェックディジットを再計算した）ISBN-13番号を含んでいることです。これは明らかに間違っていますが、彼らはこのようにしているようなので、変更しませんでした。"

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "もう一つの潜在的な問題は、「isbn13」フィールドに重複があるため、データベースの主キーとして使用できないことです。「isbn13」+「isbn」フィールドを組み合わせると一意であるようです。"

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Sci-Hubの背景については、その<a %(a_scihub)s>公式ウェブサイト</a>、<a %(a_wikipedia)s>Wikipediaページ</a>、およびこの<a %(a_radiolab)s>ポッドキャストインタビュー</a>を参照してください。"

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Sci-Hubは<a %(a_reddit)s>2021年から凍結されています</a>。以前も凍結されていましたが、2021年には数百万の論文が追加されました。それでも、限られた数の論文がLibgenの「scimag」コレクションに追加されていますが、新しい大規模なトレントを作成するほどではありません。"

msgid "page.datasets.scihub.description3"
msgstr "Sci-Hubのメタデータは、<a %(a_libgen_li)s>Libgen.li</a>の「scimag」コレクションに基づいて利用しています。また、<a %(a_dois)s>dois-2022-02-12.7z</a>のデータセットも活用しています。"

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "「smarch」トレントは<a %(a_smarch)s>廃止</a>されているため、私たちのトレントリストには含まれていません。"

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "アンナのアーカイブのトレント"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "メタデータとトレント"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Libgen.rsのトレント"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Libgen.liのトレント"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Redditでの更新情報"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipediaページ"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "ポッドキャストインタビュー"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "アンナのアーカイブへのアップロード"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "<a %(a1)s>データセットページ</a>からの概要。"

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "さまざまな小規模または一時的なソース。私たちは他のシャドウライブラリに最初にアップロードすることを奨励していますが、時には他の人が整理するには大きすぎるコレクションを持っている人もいますが、それほど大きくないため独自のカテゴリを設けるには至りません。"

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "「アップロード」コレクションは小さなサブコレクションに分割されており、AACIDやトレント名で示されています。すべてのサブコレクションは最初にメインコレクションと重複排除されましたが、メタデータ「upload_records」JSONファイルには元のファイルへの多くの参照がまだ含まれています。非書籍ファイルもほとんどのサブコレクションから削除されており、通常「upload_records」JSONには記載されていません。"

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "多くのサブコレクション自体がサブサブコレクション（例：異なる元ソースから）で構成されており、「filepath」フィールドにディレクトリとして表現されています。"

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "サブコレクションは次のとおりです："

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "サブコレクション"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "ノート"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "閲覧"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "検索"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "<a %(a_href)s>aaaaarg.fail</a>から。かなり完全なもののようです。ボランティア「cgiym」から提供されました。"

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "<a %(a_href)s><q>ACM Digital Library 2020</q></a>のトレントから。既存の論文コレクションとかなり重複していますが、MD5の一致はほとんどないため、完全に保持することにしました。"

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "ボランティアの<q>j</q>による<q>iRead eBooks</q>（発音的には<q>ai rit i-books</q>; airitibooks.com）のスクレイピング。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>airitibooks</q>メタデータに対応。"

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "<a %(a1)s><q>アレクサンドリア図書館</q></a>のコレクションから。元のソースから一部、the-eye.euから一部、他のミラーから一部。"

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "プライベートな書籍トレントサイト<a %(a_href)s>Bibliotik</a>（「Bib」とも呼ばれる）から、書籍は名前ごとにトレント（A.torrent、B.torrent）にまとめられ、the-eye.euを通じて配布されました。"

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "ボランティア「bpb9v」から提供されました。<a %(a_href)s>CADAL</a>に関する詳細は、<a %(a_duxiu)s>DuXiuデータセットページ</a>のノートをご覧ください。"

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "さらにボランティア「bpb9v」から、主にDuXiuファイル、および「WenQu」と「SuperStar_Journals」（SuperStarはDuXiuの背後にある会社）のフォルダー。"

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "ボランティア「cgiym」から、中国のさまざまなソースからのテキスト（サブディレクトリとして表現）、<a %(a_href)s>China Machine Press</a>（主要な中国の出版社）を含む。"

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "ボランティア「cgiym」からの非中国語のコレクション（サブディレクトリとして表現）。"

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "ボランティアの<q>cm</q>による中国建築に関する書籍のスクレイピング：<q>出版社のネットワーク脆弱性を利用して入手しましたが、その抜け穴はすでに閉じられています</q>。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>chinese_architecture</q>メタデータに対応。"

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "学術出版社<a %(a_href)s>De Gruyter</a>の書籍、大規模なトレントから収集。"

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "書籍やその他の書かれた作品に焦点を当てたポーランドのファイル共有サイト<a %(a_href)s>docer.pl</a>のスクレイプ。2023年後半にボランティア「p」によってスクレイプされました。元のウェブサイトからの良いメタデータはありません（ファイル拡張子さえもありません）が、書籍のようなファイルをフィルタリングし、ファイル自体からメタデータを抽出することができました。"

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiuのepub、DuXiuから直接、ボランティア「w」によって収集。最近のDuXiuの書籍のみが直接電子書籍として利用可能なので、これらのほとんどは最近のものに違いありません。"

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "ボランティア「m」からの残りのDuXiuファイル、DuXiuの独自のPDG形式ではないもの（主要な<a %(a_href)s>DuXiuデータセット</a>）。多くの元のソースから収集されましたが、残念ながらそのソースをファイルパスに保存していません。"

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "ボランティアの<q>do no harm</q>によるエロティック書籍のスクレイピング。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>hentai</q>メタデータに対応。"

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "ボランティア「t」によって日本のマンガ出版社からスクレイプされたコレクション。"

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>龍泉の選ばれた司法アーカイブ</a>、ボランティア「c」によって提供されました。"

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "<a %(a_href)s>magzdb.org</a>のスクレイプ、Library Genesisの同盟者（libgen.rsのホームページにリンクされています）が直接ファイルを提供したくなかったため。2023年後半にボランティア「p」によって取得されました。"

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

msgid "page.datasets.upload.source.misc"
msgstr "さまざまな小さなアップロード、単独ではサブコレクションとしては小さすぎるため、ディレクトリとしてまとめられています。<q>oo42hcksBxZYAOjqwGWu</q> ディレクトリは、<a %(a1)s><q>Other metadata scrapes</q></a> 内の <q>czech_oo42hcks</q> メタデータに対応します。"

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "ロシアのファイル共有サイトAvaxHomeからの電子書籍。"

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "新聞と雑誌のアーカイブ。<a %(a1)s><q>その他のメタデータスクレイピング</q></a>の<q>newsarch_magz</q>メタデータに対応。"

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Philosophy Documentation Center</a>のスクレイピング。"

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "ボランティア「o」のコレクション、オリジナルリリース（「シーン」）ウェブサイトから直接収集されたポーランドの書籍。"

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "ボランティア「cgiym」と「woz9ts」によって収集された<a %(a_href)s>shuge.org</a>の統合コレクション。"

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>「トランター帝国図書館」</a>（架空の図書館に由来する名称）は、2022年にボランティア「t」によってスクレイピングされました。これは、<a %(a1)s><q>Other metadata scrapes</q></a> 内の <q>trantor</q> メタデータに対応しています。"

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "ボランティア「woz9ts」からのサブサブコレクション（ディレクトリとして表現）：<a %(a_program_think)s>program-think</a>、<a %(a_haodoo)s>haodoo</a>、<a %(a_skqs)s>skqs</a>（台湾の<a %(a_sikuquanshu)s>Dizhi(迪志)</a>による）、mebook（mebook.cc、私の小さな書房、woz9ts：「このサイトは主に高品質の電子書籍ファイルの共有に焦点を当てており、その一部はオーナー自身が組版したものです。オーナーは2019年に<a %(a_arrested)s>逮捕</a>され、彼が共有したファイルのコレクションが作成されました。」）。"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "ボランティア「woz9ts」からの残りのDuXiuファイルで、DuXiu独自のPDG形式ではないもの（まだPDFに変換されていない）。"

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Anna’s Archiveによるトレント"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Libraryスクレイプ"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Libraryは<a %(a_href)s>Library Genesis</a>コミュニティにルーツがあり、元々は彼らのデータを使ってブートストラップされました。それ以来、かなりプロフェッショナルになり、よりモダンなインターフェースを持つようになりました。そのため、ウェブサイトの改善のための金銭的な寄付や新しい本の寄付を多く受けることができました。Library Genesisに加えて、大規模なコレクションを蓄積しています。"

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "2023年2月の更新。"

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "2022年後半、Z-Libraryの創設者とされる人物が逮捕され、ドメインがアメリカ当局によって押収されました。それ以来、ウェブサイトは徐々に再びオンラインに戻りつつあります。現在誰が運営しているのかは不明です。"

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "コレクションは3つの部分で構成されています。最初の2つの部分のオリジナルの説明ページは以下に保存されています。すべてのデータを取得するには、3つの部分すべてが必要です（トレントページで取り消し線が引かれているものを除く）。"

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s：最初のリリース。これは「Pirate Library Mirror」（「pilimi」）と呼ばれていたものの最初のリリースでした。"

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s：2回目のリリース、今回はすべてのファイルが.tarファイルに包まれています。"

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s：<a %(a_href)s>Anna’s Archive Containers (AAC)形式</a>を使用した増分新リリース、現在はZ-Libraryチームとのコラボレーションでリリースされています。"

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Anna’s Archiveによるトレント（メタデータ＋コンテンツ）"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Anna’s Archiveの例レコード（オリジナルコレクション）"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Anna’s Archiveの例レコード（「zlib3」コレクション）"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "メインウェブサイト"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Torドメイン"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "リリース1に関するブログ投稿"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "リリース2に関するブログ投稿"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlibリリース（オリジナルの説明ページ）"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "リリース1（%(date)s）"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "最初のミラーは2021年から2022年にかけて丹念に取得されました。この時点でやや古くなっています：2021年6月のコレクションの状態を反映しています。将来的にはこれを更新する予定です。現在はこの最初のリリースを出すことに集中しています。"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Library Genesisはすでにパブリックトレントで保存されており、Z-Libraryにも含まれているため、2022年6月にLibrary Genesisに対して基本的な重複排除を行いました。これにはMD5ハッシュを使用しました。ライブラリには、同じ本の複数のファイル形式など、さらに多くの重複コンテンツが存在する可能性があります。これを正確に検出するのは難しいため、行っていません。重複排除の後、約200万ファイルが残り、合計で7TB未満です。"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "コレクションは2つの部分で構成されています：メタデータのMySQL “.sql.gz” ダンプと、約50-100GBの72個のトレントファイルです。メタデータには、Z-Libraryウェブサイトで報告されたデータ（タイトル、著者、説明、ファイルタイプ）と、実際のファイルサイズおよび観測されたmd5sumが含まれています。これらは時々一致しないことがあります。Z-Library自体が不正確なメタデータを持っているファイルの範囲があるようです。また、いくつかの孤立したケースでは、誤ってダウンロードしたファイルがあるかもしれませんが、将来的に検出して修正する予定です。"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "大きなトレントファイルには、Z-Library IDをファイル名として実際の書籍データが含まれています。ファイル拡張子はメタデータダンプを使用して再構築できます。"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "コレクションはノンフィクションとフィクションのコンテンツの混合であり（Library Genesisのように分離されていません）、品質も大きく異なります。"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "この最初のリリースは現在完全に利用可能です。トレントファイルは私たちのTorミラーを通じてのみ利用可能であることに注意してください。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "リリース2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "前回のミラーと2022年8月の間にZ-Libraryに追加されたすべての本を取得しました。また、最初の時に見逃した本もいくつかスクレイピングしました。全体として、この新しいコレクションは約24TBです。再度、このコレクションはLibrary Genesisに対して重複排除されています。すでにそのコレクションのトレントが利用可能だからです。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "データは最初のリリースと同様に整理されています。メタデータのMySQL “.sql.gz” ダンプがあり、これには最初のリリースのすべてのメタデータも含まれており、それを上書きします。新しい列も追加しました："

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s：このファイルがLibrary Genesisにすでに含まれているかどうか（ノンフィクションまたはフィクションコレクションにmd5で一致）。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s：このファイルがどのトレントに含まれているか。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s：本をダウンロードできなかった場合に設定。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "前回も言及しましたが、念のために明確にしておきます：「filename」と「md5」はファイルの実際のプロパティであり、「filename_reported」と「md5_reported」はZ-Libraryからスクレイピングしたものです。これらが一致しないことがあるため、両方を含めました。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "このリリースでは、照合順序を「utf8mb4_unicode_ci」に変更しました。これにより、古いバージョンのMySQLとも互換性があります。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "データファイルは前回と似ていますが、はるかに大きいです。小さなトレントファイルを大量に作成するのは面倒だったためです。「pilimi-zlib2-0-14679999-extra.torrent」には前回のリリースで見逃したすべてのファイルが含まれており、他のトレントはすべて新しいID範囲です。 "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>更新 %(date)s：</strong> トレントファイルが大きすぎて、トレントクライアントが苦労していました。それらを削除し、新しいトレントをリリースしました。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>更新 %(date)s：</strong> まだファイルが多すぎたため、tarファイルにまとめて再度新しいトレントをリリースしました。"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "リリース2補遺 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "これは単一の追加トレントファイルです。新しい情報は含まれていませんが、計算に時間がかかるデータが含まれています。そのため、最初から計算するよりもこのトレントをダウンロードする方が速いことが多いです。特に、<a %(a_href)s>ratarmount</a>で使用するためのtarファイルのSQLiteインデックスが含まれています。"

#, fuzzy
msgid "page.faq.title"
msgstr "よくある質問 (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Anna’s Archiveとは何ですか？"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>アンナのアーカイブ</span>は2つの目標を掲げた非営利プロジェクトです:"

msgid "page.home.intro.text2"
msgstr "<li><strong>保存：</strong>人類のあらゆる知識と文化をバックアップします。</li><li><strong>アクセス：</strong> 世界中の誰もがこの知識と文化を利用できるようにします。</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "すべての<a %(a_code)s>コード</a>と<a %(a_datasets)s>データ</a>は完全にオープンソースです。"

msgid "page.home.preservation.header"
msgstr "保存"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "私たちは、本、論文、コミック、雑誌などを保存しています。これらの資料をさまざまな<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">シャドウライブラリ</a>、公式図書館、その他のコレクションから一つの場所に集めています。このデータは、トレントを使用して大量に複製することで、世界中に多くのコピーを作成し、永遠に保存されます。一部のシャドウライブラリはすでにこれを自分たちで行っています（例：Sci-Hub、Library Genesis）。一方、Anna’s Archiveは、大量配布を提供しない他の図書館（例：Z-Library）や、シャドウライブラリではない図書館（例：Internet Archive、DuXiu）を「解放」しています。"

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "この広範な配布とオープンソースコードの組み合わせにより、当ウェブサイトは削除に対して耐性があり、人類の知識と文化の長期保存を確保します。<a href=\"/datasets\">私たちのデータセット</a>について詳しく学びましょう。</a>"

msgid "page.home.preservation.label"
msgstr "私達は<a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">全世界の5%%の本</a>が保存されたと推定しています。"

msgid "page.home.access.header"
msgstr "アクセス"

#, fuzzy
msgid "page.home.access.text"
msgstr "私たちはパートナーと協力して、コレクションを誰でも簡単かつ無料でアクセスできるようにしています。私たちは、人類の集団的知恵に対する権利が誰にでもあると信じています。そして<a %(a_search)s>著者の犠牲を払うことなく</a>。"

msgid "page.home.access.label"
msgstr "過去30日間の1時間あたりのダウンロード数。一時間あたりで%(hourly)s回。一日あたりで%(daily)s回。"

msgid "page.about.text2"
msgstr "情報の自由な流れ、そして知識と文化の保存を強く信じています。この検索エンジンによって、巨人の肩の上に立つことになります。さまざまな影の図書館を作ってきた人々の努力に深く敬意を表し、この検索エンジンがその範囲を広げることを望んでいます。"

msgid "page.about.text3"
msgstr "進捗状況を知るには、アンナを<a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>、<a href=\"https://t.me/annasarchiveorg\">Telegram</a>でフォローしてください。ご質問やご意見は、アンナの%(email)sまでご連絡ください。"

#, fuzzy
msgid "page.faq.help.title"
msgstr "どうすれば手伝えますか？"

msgid "page.about.help.text"
msgstr "<li>1.私たちの<a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>、<a href=\"https://t.me/annasarchiveorg\">Telegram</a>をフォローしてください。</li><li>2.Twitter、Reddit、Tiktok、Instagram、お近くのカフェや図書館など、あなたの行く先々でアンナのアーカイブのことを広めてください！私たちのコードやデータはすべてオープンソースなので、もし閉鎖されても、別の場所ですぐに再開できます。</li><li>3.可能であれば<a href=\"/donate\">寄付</a>してください。</li><li>4.<a href=\"https://translate.annas-software.org/\">翻訳</a>を手伝ってください。</li><li>5.もしあなたがソフトウェアエンジニアであれば、私たちの<a href=\"https://annas-software.org/\">オープンソース</a>に貢献したり、<a href=\"/datasets\">トレント</a>でシードすることを検討してください。</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "現在、%(matrix)sで同期されたMatrixチャンネルもあります。"

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. あなたがセキュリティ研究者であれば、攻撃と防御の両方であなたのスキルを活用できます。<a %(a_security)s>セキュリティ</a>ページをご覧ください。"

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. 匿名の商人向けの支払いの専門家を探しています。寄付のためのより便利な方法を追加するのを手伝っていただけませんか？PayPal、WeChat、ギフトカードなど。知り合いがいれば、ぜひご連絡ください。"

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. 私たちは常にサーバー容量の拡大を求めています。"

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. ファイルの問題を報告したり、コメントを残したり、このウェブサイト上でリストを作成することで支援できます。また、<a %(a_upload)s>本をもっとアップロード</a>したり、既存の本のファイル問題やフォーマットを修正することでも支援できます。"

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. あなたの言語でAnna’s ArchiveのWikipediaページを作成または維持するのを手伝ってください。"

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. 控えめな小さな広告を掲載したいと考えています。Anna’s Archiveに広告を掲載したい場合は、お知らせください。"

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "<a %(a_mirrors)s>ミラーサイト</a>を設置していただけると嬉しいです。これに対しては財政的な支援を行います。"

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "ボランティア活動に関する詳細情報は、<a %(a_volunteering)s>ボランティア & バウンティ</a>ページをご覧ください。"

#, fuzzy
msgid "page.faq.slow.title"
msgstr "なぜダウンロードが遅いのですか？"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "私たちは、世界中のすべての人に高速ダウンロードを提供するためのリソースが文字通り不足しています。もし裕福な支援者がこれを提供してくれるなら素晴らしいことですが、それまでは最善を尽くしています。私たちは寄付でかろうじて自立している非営利プロジェクトです。"

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "このため、パートナーと協力して、無料ダウンロードのための2つのシステムを実装しました：遅いダウンロードの共有サーバーと、待機リスト付きのやや速いサーバー（同時にダウンロードする人数を減らすため）。"

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "ボットやスクレイパーが悪用しないように、遅いダウンロードには<a %(a_verification)s>ブラウザ検証</a>も行っています。これにより、正当なユーザーのために速度がさらに遅くなるのを防ぎます。"

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Torブラウザを使用する際、セキュリティ設定を調整する必要がある場合があります。「標準」と呼ばれる最も低いオプションでは、Cloudflareのターンスタイルチャレンジが成功します。より高いオプションである「より安全」および「最も安全」では、チャレンジが失敗します。"

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "大きなファイルの場合、ダウンロードが途中で中断されることがあります。大きなファイルのダウンロードを自動的に再開するために、ダウンロードマネージャー（例えばJDownloader）の使用をお勧めします。"

msgid "page.donate.faq.title"
msgstr "寄付のFAQ"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>メンバーシップは自動で継続されますか？</div>メンバーシップは<strong>決して</strong>自動で継続されません。好きなだけ参加することも、短期間のみ参加することも可能です。"

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>メンバーシップをアップグレードしたり、複数のメンバーシップを取得することはできますか？</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>他の支払い方法はありますか？</div>現在はありません。多くの人がこのようなアーカイブの存在を望んでいませんので、慎重にならざるを得ません。もしあなたが他の（より便利な）支払い方法を安全に設定するのを手伝ってくれるなら、%(email)sに連絡してください。"

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>月ごとの範囲は何を意味しますか？</div> すべての割引を適用することで、範囲の下限に到達できます。例えば、1か月以上の期間を選択するなどです。"

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>寄付されたお金は何に使用されますか？</div>その100%%が世界の知識と文化を保存し、アクセスできるようにするために使われています。現在、私たちはそのほとんどをサーバーやストレージ、帯域幅に費やしております。どのチームメンバーにも個人的にお金が行くことは決してありません。"

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>多額の寄付をすることはできますか？</div> それは素晴らしいことだと思います！数千ドル以上の寄付は、%(email)sに直接ご連絡ください。"

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>メンバーにならずに寄付することはできますか？</div> もちろんです。このMonero (XMR)アドレスに任意の金額を寄付できます: %(address)s。"

#, fuzzy
msgid "page.faq.upload.title"
msgstr "新しい本をアップロードするにはどうすればよいですか？"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "または、Z-Libraryに<a %(a_upload)s>こちら</a>からアップロードすることもできます。"

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "小規模なアップロード（最大10,000ファイル）については、%(first)s と %(second)s の両方にアップロードしてください。"

msgid "page.upload.text1"
msgstr "今のところ、私達はGenesis forksへアップロードすることを提案します。<a %(a_guide)s>こちら</a>がハンディガイドです。このウェブサイトでインデックスしているforkはどちらもこの同じアップロードシステムから引っ張ってきていることに注意してください。"

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.liの場合、まず<a %(a_forum)s>彼らのフォーラム</a>にユーザー名%(username)sとパスワード%(password)sでログインし、その後<a %(a_upload_page)s>アップロードページ</a>に戻ってください。"

msgid "common.libgen.email"
msgstr "Libgen forumsでEメールが動作しない場合、わたしたちは<a %(a_mail)s>Proton Mail</a>(無料)を推奨します。あなたのアカウントを有効にするために<a %(a_manual)s>手動リクエスト</a>を行うことも可能です。"

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "mhut.orgは特定のIP範囲をブロックしているため、VPNが必要になる場合があります。"

#, fuzzy
msgid "page.upload.large.text"
msgstr "10,000ファイル以上の大規模なアップロードがLibgenやZ-Libraryに受け入れられない場合は、%(a_email)sまでご連絡ください。"

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "学術論文をアップロードする場合は、Library Genesisに加えて<a %(a_stc_nexus)s>STC Nexus</a>にもアップロードしてください。彼らは新しい論文のための最高のシャドウライブラリです。まだ統合していませんが、いずれ統合する予定です。<a %(a_telegram)s>Telegramのアップロードボット</a>を使用するか、ピン留めされたメッセージに記載されているアドレスに連絡してください。"

#, fuzzy
msgid "page.faq.request.title"
msgstr "本をリクエストするにはどうすればよいですか？"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "現在、書籍のリクエストには対応できません。"

#, fuzzy
msgid "page.request.forums"
msgstr "Z-LibraryまたはLibgenのフォーラムでリクエストを行ってください。"

#, fuzzy
msgid "page.request.dont_email"
msgstr "本のリクエストをメールで送らないでください。"

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "メタデータを収集しますか？"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "はい、収集します。"

#, fuzzy
msgid "page.faq.1984.title"
msgstr "ジョージ・オーウェルの「1984」をダウンロードしましたが、警察が来るのでしょうか？"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "あまり心配しないでください。私たちがリンクしているウェブサイトから多くの人がダウンロードしており、トラブルになることは非常に稀です。ただし、安全のためにVPN（有料）や<a %(a_tor)s>Tor</a>（無料）を使用することをお勧めします。"

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "検索設定を保存するにはどうすればよいですか？"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "お好みの設定を選択し、検索ボックスを空のままにして「検索」をクリックし、ブラウザのブックマーク機能を使用してページをブックマークしてください。"

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "モバイルアプリはありますか？"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "公式のモバイルアプリはありませんが、このウェブサイトをアプリとしてインストールすることができます。"

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> 右上の三点メニューをクリックし、「ホーム画面に追加」を選択します。"

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> 下部の「共有」ボタンをクリックし、「ホーム画面に追加」を選択してください。"

#, fuzzy
msgid "page.faq.api.title"
msgstr "APIはありますか？"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "メンバー向けに安定したJSON APIがあり、迅速なダウンロードURLを取得できます：<a %(a_fast_download)s>/dyn/api/fast_download.json</a>（JSON内にドキュメントがあります）。"

#, fuzzy
msgid "page.faq.api.text2"
msgstr "他の使用例、例えばすべてのファイルを繰り返し処理する、カスタム検索を構築するなどの場合は、<a %(a_generate)s>生成</a>または<a %(a_download)s>ダウンロード</a>するElasticSearchおよびMariaDBデータベースをお勧めします。生データは<a %(a_explore)s>JSONファイルを通じて</a>手動で探索できます。"

#, fuzzy
msgid "page.faq.api.text3"
msgstr "生のトレントリストも<a %(a_torrents)s>JSON</a>としてダウンロードできます。"

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "トレントFAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "シードを手伝いたいのですが、ディスクスペースがあまりありません。"

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "<a %(a_list)s>トレントリストジェネレーター</a>を使用して、ストレージスペースの制限内で最もトレントが必要なトレントのリストを生成します。"

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "トレントが遅すぎます。データを直接ダウンロードできますか？"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "はい、<a %(a_llm)s>LLMデータ</a>ページをご覧ください。"

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "特定の言語やトピックなど、ファイルの一部だけをダウンロードすることはできますか？"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "短い答え: 簡単ではありません。"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "長い答え:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "ほとんどのトレントはファイルを直接含んでいるため、トレントクライアントに必要なファイルのみをダウンロードするよう指示できます。どのファイルをダウンロードするかを決定するには、<a %(a_generate)s>メタデータを生成</a>するか、<a %(a_download)s>ElasticSearchおよびMariaDBデータベースをダウンロード</a>できます。ただし、一部のトレントコレクションにはルートに.zipや.tarファイルが含まれているため、個々のファイルを選択する前にトレント全体をダウンロードする必要があります。"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "（ただし、後者の場合には<a %(a_ideas)s>いくつかのアイデア</a>があります。）"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "フィルタリング用の簡単なツールはまだ利用できませんが、貢献を歓迎します。"

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "トレントの重複はどのように処理しますか？"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "このリストのトレント間で重複や重なりを最小限に抑えるよう努めていますが、これは常に達成できるわけではなく、ソースライブラリのポリシーに大きく依存します。ライブラリが独自のトレントを公開している場合、それは私たちの手に負えません。Anna’s Archiveがリリースするトレントについては、MD5ハッシュに基づいて重複を排除しますが、同じ本の異なるバージョンは重複排除されません。"

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "トレントリストをJSON形式で取得できますか？"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "はい。"

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "トレントにPDFやEPUBが見当たらず、バイナリファイルのみですが、どうすればいいですか？"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "これらは実際にはPDFやEPUBですが、多くのトレントでは拡張子がありません。トレントファイルのメタデータ、ファイルタイプや拡張子を含む情報は2か所で見つけることができます："

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. 各コレクションやリリースには独自のメタデータがあります。例えば、<a %(a_libgen_nonfic)s>Libgen.rsのトレント</a>には、Libgen.rsウェブサイト上に対応するメタデータデータベースがあります。通常、各コレクションの<a %(a_datasets)s>データセットページ</a>から関連するメタデータリソースへのリンクを提供しています。"

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. <a %(a_generate)s>生成</a>または<a %(a_download)s>ダウンロード</a>することをお勧めします。これには、Anna’s Archiveの各レコードと対応するトレントファイル（利用可能な場合）へのマッピングが含まれています。ElasticSearch JSONの「torrent_paths」にあります。"

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "なぜ私のトレントクライアントはあなたのトレントファイルやマグネットリンクを開けないのですか？"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "一部のトレントクライアントは大きなピースサイズをサポートしていませんが、私たちのトレントの多くはそれを持っています（新しいものではこれを行っていませんが、仕様上は有効です！）。この問題に直面した場合は、別のクライアントを試すか、トレントクライアントの製作者に苦情を申し立ててください。"

#, fuzzy
msgid "page.faq.security.title"
msgstr "責任ある開示プログラムはありますか？"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "セキュリティ研究者の皆さんには、私たちのシステムの脆弱性を探していただきたいと思っています。私たちは責任ある開示の大支持者です。こちら<a %(a_contact)s>から</a>ご連絡ください。"

msgid "page.faq.security.text2"
msgstr "現在、バグ報奨金の提供は行っておりませんが、<a %(a_link)s>匿名性に影響を及ぼす可能性のある</a>脆弱性については、1万〜5万ドルの範囲で報奨金を提供しています。将来的には、より広範な対象を含めた報奨金制度を検討しています。なお、ソーシャルエンジニアリング攻撃は対象外ですのでご注意ください。"

#, fuzzy
msgid "page.faq.security.text3"
msgstr "攻撃的セキュリティに興味があり、世界の知識や文化をアーカイブする手助けをしたい場合は、ぜひご連絡ください。あなたが貢献できる方法はたくさんあります。"

#, fuzzy
msgid "page.faq.resources.title"
msgstr "アンナのアーカイブについてのさらなるリソースはありますか？"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>アンナのブログ</a>、<a %(a_reddit_u)s>Reddit</a>、<a %(a_reddit_r)s>サブレディット</a> — 定期的な更新"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — 私たちのオープンソースコード"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Anna’s Softwareで翻訳</a> — 私たちの翻訳システム"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — データについて"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>、<a %(a_se)s>.se</a>、<a %(a_org)s>.org</a> — 代替ドメイン"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>ウィキペディア</a> — 私たちについての詳細（このページを更新していただくか、あなたの言語で新しいページを作成してください！）"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "著作権侵害を報告するにはどうすればいいですか？"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "ここでは著作権のある資料はホストしていません。私たちは検索エンジンであり、既に公開されているメタデータのみをインデックスしています。これらの外部ソースからダウンロードする際には、許可されている内容に関してあなたの管轄地域の法律を確認することをお勧めします。他者がホストするコンテンツについては責任を負いません。"

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "ここで見たものについて苦情がある場合、最善の方法は元のウェブサイトに連絡することです。私たちは定期的に彼らの変更をデータベースに取り込んでいます。本当に有効なDMCAの苦情があると思われる場合は、<a %(a_copyright)s>DMCA / 著作権クレームフォーム</a>に記入してください。私たちはあなたの苦情を真剣に受け止め、できるだけ早くご連絡いたします。"

#, fuzzy
msgid "page.faq.hate.title"
msgstr "このプロジェクトの運営方法が嫌いです！"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "また、私たちのコードとデータは完全にオープンソースであることを皆さんにお伝えしたいと思います。私たちのようなプロジェクトで、これほど大規模なカタログを持ちながら完全にオープンソースである例は他に知りません。私たちのプロジェクトを不十分に運営していると考える方は、私たちのコードとデータを取り、自分たちのシャドウライブラリを設立していただきたいと思います。これは恨みや何かから言っているのではなく、本当に素晴らしいことだと思っています。なぜなら、それによって全体の基準が引き上げられ、人類の遺産がより良く保存されるからです。"

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "稼働時間モニターをお持ちですか？"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "<a %(a_href)s>この素晴らしいプロジェクト</a>をご覧ください。"

#, fuzzy
msgid "page.faq.physical.title"
msgstr "本や他の物理的な資料を寄付するにはどうすればいいですか？"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "<a %(a_archive)s>Internet Archive</a>に送ってください。彼らが適切に保存します。"

#, fuzzy
msgid "page.faq.anna.title"
msgstr "アンナとは誰ですか？"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "あなたがアンナです！"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "お気に入りの本は何ですか？"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "シャドウライブラリやデジタル保存の世界に特別な意味を持つ本をいくつか紹介します："

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "本日の高速ダウンロードの上限に達しました。"

msgid "page.fast_downloads.no_member"
msgstr "高速ダウンロードをご利用になられる場合はメンバーになる必要がございます。"

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "現在、Amazonギフトカード、クレジットカードおよびデビットカード、暗号通貨、Alipay、WeChatに対応しています。"

msgid "page.home.full_database.header"
msgstr "全データベース"

msgid "page.home.full_database.subtitle"
msgstr "本、論文、雑誌、コミック、図書館の記録、メタデータなど…"

msgid "page.home.full_database.search"
msgstr "検索"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "ベータ"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hubは新しい論文のアップロードを<a %(a_paused)s>一時停止</a>しています。"

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDBはSci-Hubの継続です。"

msgid "page.home.scidb.subtitle"
msgstr "%(count)s本の学術論文への直接アクセス"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "開く"

msgid "page.home.scidb.browser_verification"
msgstr "もし<a %(a_member)s>メンバー</a>ならブラウザの認証は必要ありません。"

msgid "page.home.archive.header"
msgstr "長期アーカイブ"

msgid "page.home.archive.body"
msgstr "アンナのアーカイブで使用されるデータセットは完全にオープンソースであり、torrentsを使用して一括でミラーリングできます。<a %(a_datasets)s>もっと詳しく…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "トレントをシードすることで大いに助けることができます。<a %(a_torrents)s>詳細はこちら…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s シーダー"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s シーダー"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s シーダー"

msgid "page.home.llm.header"
msgstr "大規模言語モデルの学習データ"

msgid "page.home.llm.body"
msgstr "世界最大の高品質テキストデータコレクションを保有しています。<a %(a_llm)s>詳細はこちら…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 ミラー: ボランティア募集"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 ボランティア募集中"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "非営利のオープンソースプロジェクトとして、常に協力してくれる人を探しています。"

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "高リスクの匿名決済プロセッサーを運営している場合は、ぜひご連絡ください。また、控えめな小さな広告を掲載したい方も募集しています。すべての収益は保存活動に充てられます。"

msgid "layout.index.header.nav.annasblog"
msgstr "アンナのブログ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS ダウンロード"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 このファイルのすべてのダウンロードリンク：<a %(a_main)s>ファイルメインページ</a>。"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFSゲートウェイ #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(IPFS で複数回試す必要があるかもしれません)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀高速ダウンロードを使用するには<a %(a_membership)s>メンバーシップ</a>に入る必要があります。"

msgid "page.partner_download.bulk_mirroring"
msgstr "📡すべてのコレクションをミラーリングする場合は、<a %(a_datasets)s>データセット</a>及び<a %(a_torrents)s>トレント</a>ページを参照してください。"

#, fuzzy
msgid "page.llm.title"
msgstr "LLMデータ"

#, fuzzy
msgid "page.llm.intro"
msgstr "LLMは高品質なデータで成長することがよく理解されています。私たちは世界最大の書籍、論文、雑誌などのコレクションを持っており、これらは最高品質のテキストソースの一部です。"

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "独自の規模と範囲"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "私たちのコレクションには、学術雑誌、教科書、雑誌を含む1億以上のファイルが含まれています。この規模は、大規模な既存のリポジトリを組み合わせることで達成されます。"

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "私たちのソースコレクションの一部は、すでに大量に利用可能です（Sci-HubやLibgenの一部）。他のソースは私たち自身で解放しました。<a %(a_datasets)s>Datasets</a>で全体の概要を示しています。"

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "私たちのコレクションには、電子書籍時代以前の数百万冊の書籍、論文、雑誌が含まれています。このコレクションの大部分はすでにOCRされており、内部の重複はほとんどありません。"

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "私たちがどのように支援できるか"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "私たちは、未公開のコレクションを含む全コレクションへの高速アクセスを提供できます。"

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "これは、数万ドルの寄付で提供できる企業レベルのアクセスです。また、まだ持っていない高品質のコレクションと交換することも可能です。"

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "データの充実化を提供していただければ、返金いたします。例えば："

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "重複除去"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "テキストとメタデータの抽出"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "人類の知識の長期保存を支援しながら、モデルのためにより良いデータを取得しましょう！"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>お問い合わせ</a>して、どのように協力できるかを話し合いましょう。"

msgid "page.login.continue"
msgstr "継続する"

#, fuzzy
msgid "page.login.please"
msgstr "このページを表示するには、<a %(a_account)s>ログイン</a>してください。</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Archiveはメンテナンスのため一時的に停止しています。1時間後に再度お越しください。"

#, fuzzy
msgid "page.metadata.header"
msgstr "メタデータを改善する"

#, fuzzy
msgid "page.metadata.body1"
msgstr "メタデータを改善することで、本の保存に貢献できます！まず、Anna’s Archiveでメタデータの背景を読み、その後、Open Libraryとのリンクを通じてメタデータを改善する方法を学び、Anna’s Archiveで無料のメンバーシップを獲得しましょう。"

#, fuzzy
msgid "page.metadata.background.title"
msgstr "背景"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Anna’s Archiveで本を見ると、タイトル、著者、出版社、版、年、説明、ファイル名など、さまざまなフィールドが表示されます。これらすべての情報は<em>メタデータ</em>と呼ばれます。"

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "私たちはさまざまな<em>ソースライブラリ</em>から本を組み合わせているため、そのソースライブラリで利用可能なメタデータを表示します。例えば、Library Genesisから取得した本の場合、Library Genesisのデータベースからタイトルを表示します。"

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "時々、本が<em>複数の</em>ソースライブラリに存在し、異なるメタデータフィールドを持つことがあります。その場合、各フィールドの最も長いバージョンを表示します。なぜなら、それが最も有用な情報を含んでいる可能性が高いからです！他のフィールドは説明の下に「代替タイトル」として表示します（ただし、異なる場合のみ）。"

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "また、ソースライブラリから識別子や分類子などの<em>コード</em>を抽出します。<em>識別子</em>は特定の版を一意に表します。例としては、ISBN、DOI、Open Library ID、Google Books ID、またはAmazon IDがあります。<em>分類子</em>は、複数の類似した本をグループ化します。例としては、デューイ十進分類法（DCC）、UDC、LCC、RVK、またはGOSTがあります。これらのコードは、ソースライブラリで明示的にリンクされている場合もあれば、ファイル名や説明から抽出できる場合もあります（主にISBNとDOI）。"

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "識別子を使用して、OpenLibrary、ISBNdb、またはWorldCat/OCLCなどの<em>メタデータ専用コレクション</em>のレコードを見つけることができます。これらのコレクションを閲覧したい場合は、検索エンジンの特定の<em>メタデータタブ</em>があります。マッチングレコードを使用して、欠落しているメタデータフィールドを埋めることができます（例：タイトルが欠落している場合）、または「代替タイトル」として使用します（既存のタイトルがある場合）。"

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "本のメタデータがどこから来たのか正確に知りたい場合は、本のページの<em>「技術的詳細」タブ</em>を参照してください。そこには、その本の生のJSONへのリンクがあり、元のレコードの生のJSONへのポインタが含まれています。"

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "詳細については、次のページを参照してください：<a %(a_datasets)s>Datasets</a>、<a %(a_search_metadata)s>Search (metadata tab)</a>、<a %(a_codes)s>Codes Explorer</a>、および<a %(a_example)s>Example metadata JSON</a>。最後に、すべてのメタデータは<a %(a_generated)s>生成</a>または<a %(a_downloaded)s>ダウンロード</a>してElasticSearchおよびMariaDBデータベースとして利用できます。"

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Libraryとのリンク"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "では、メタデータが不完全なファイルに出会った場合、どうすれば修正できますか？ソースライブラリに行き、そのメタデータ修正手順に従うことができますが、ファイルが複数のソースライブラリに存在する場合はどうすればよいでしょうか？"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Anna’s Archiveでは特別に扱われる識別子があります。<strong>Open Libraryのannas_archive md5フィールドは、他のすべてのメタデータを上書きします！</strong> まずはOpen Libraryについて少し学びましょう。"

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Libraryは2006年にAaron Swartzによって設立され、「これまでに出版されたすべての本のための1つのウェブページ」を目指しています。これは、誰でも編集できる、自由にライセンスされた、バルクでダウンロードできる、書籍メタデータのためのWikipediaのようなものです。これは私たちの使命と最も一致する書籍データベースです。実際、Anna’s ArchiveはAaron Swartzのビジョンと人生に触発されています。"

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "車輪の再発明を避けるために、私たちはボランティアをOpen Libraryに向けることにしました。メタデータが間違っている本を見つけた場合、次の方法で支援できます："

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " <a %(a_openlib)s>Open Libraryのウェブサイト</a>にアクセスします。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "正しい本のレコードを見つけます。<strong>警告：</strong>正しい<strong>版</strong>を選択することを確認してください。Open Libraryでは、「作品」と「版」があります。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "「作品」は「ハリー・ポッターと賢者の石」のようなものです。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "「版」は次のようなものです："

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997年にBloomsberyから出版された初版で、256ページです。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003年にRaincoast Booksから出版されたペーパーバック版で、223ページです。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000年にMedia Rodzinaから出版されたポーランド語訳「Harry Potter I Kamie Filozoficzn」で、328ページです。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "これらの版はすべて異なるISBNと異なる内容を持っているので、正しいものを選んでください！"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "レコードを編集する（または存在しない場合は作成する）際に、できるだけ多くの有用な情報を追加してください！せっかくここにいるのだから、レコードを本当に素晴らしいものにしましょう。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "「ID番号」の下で「Anna’s Archive」を選択し、Anna’s Archiveから本のMD5を追加します。これはURLの「/md5/」の後に続く長い文字列です。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Anna’s Archiveでこのレコードに一致する他のファイルを見つけ、それらも追加してください。将来的には、Anna’s Archiveの検索ページでそれらを重複としてグループ化できるようになります。"

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "完了したら、更新したURLを書き留めてください。Anna’s ArchiveのMD5で少なくとも30のレコードを更新したら、<a %(a_contact)s>メール</a>を送ってリストを送信してください。これにより、Anna’s Archiveの無料メンバーシップを提供し、作業をより簡単に行えるようにします（お手伝いへの感謝として）。これらは大量の情報を追加する高品質な編集でなければならず、そうでない場合はリクエストが拒否されます。また、編集がOpen Libraryのモデレーターによって取り消されたり修正された場合もリクエストは拒否されます。"

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "これは書籍にのみ適用され、学術論文やその他の種類のファイルには適用されないことに注意してください。他の種類のファイルについては、ソースライブラリを見つけることをお勧めします。Anna’s Archiveに変更が反映されるまでには数週間かかる場合があります。これは、最新のOpen Libraryデータダンプをダウンロードし、検索インデックスを再生成する必要があるためです。"

#, fuzzy
msgid "page.mirrors.title"
msgstr "ミラー：ボランティア募集"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Anna’s Archiveの回復力を高めるために、ミラーを運営するボランティアを募集しています。"

#, fuzzy
msgid "page.mirrors.text1"
msgstr "私たちはこれを探しています："

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "あなたはAnna’s Archiveのオープンソースコードベースを運用し、定期的にコードとデータの両方を更新します。"

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "あなたのバージョンは明確にミラーとして区別されます。例えば、「Bob’s Archive, an Anna’s Archive mirror」のように。"

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "この作業に伴うリスクを引き受ける意思があり、そのリスクは重大です。運用セキュリティに関する深い理解があります。<a %(a_shadow)s>これら</a>の<a %(a_pirate)s>投稿</a>の内容はあなたにとって自明です。"

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "この実現のために、私たちのチームと協力して<a %(a_codebase)s>コードベース</a>に貢献する意思があります。"

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "最初はパートナーサーバーのダウンロードへのアクセスを提供しませんが、うまくいけば共有することができます。"

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "ホスティング費用"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "私たちはホスティングとVPNの費用をカバーする用意があります。最初は月額200ドルまでです。これは基本的な検索サーバーとDMCA保護されたプロキシに十分です。"

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "すべてが設定され、アーカイブを更新し続ける能力を示した後にのみホスティング費用を支払います。つまり、最初の1〜2ヶ月は自己負担となります。"

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "あなたの時間は補償されません（私たちの時間も同様です）、これは純粋なボランティア活動だからです。"

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "私たちの作業の開発と運用に大きく関与する場合、必要に応じて寄付収益の一部を共有することについて話し合うことができます。"

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "始め方"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "許可を求めたり、基本的な質問をするために<strong>私たちに連絡しないでください</strong>。行動は言葉よりも雄弁です！すべての情報はそこにあるので、ミラーの設定を進めてください。"

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "問題が発生した場合は、Gitlabにチケットやマージリクエストを投稿してください。ミラー固有の機能をあなたと一緒に構築する必要があるかもしれません。例えば、「Anna’s Archive」からあなたのウェブサイト名へのリブランディング、（最初は）ユーザーアカウントの無効化、または本のページから私たちのメインサイトへのリンクなどです。"

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "ミラーが稼働したら、ぜひご連絡ください。あなたのOpSecをレビューしたいと思います。それが確固たるものであれば、あなたのミラーにリンクし、より緊密に協力していきます。"

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "この方法で貢献してくれるすべての人に事前に感謝します！これは心の弱い人向けではありませんが、人類史上最大の真にオープンな図書館の長寿を確固たるものにするでしょう。"

msgid "page.partner_download.header"
msgstr "パートナーのウェブサイトからダウンロード"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ 公式ウェブサイトを通じてのみ遅いダウンロードが利用可能です。%(websites)sを訪問してください。"

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Cloudflare VPNやその他のCloudflare IPアドレスからの遅いダウンロードは利用できません。"

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "このファイルをダウンロードするには<span %(span_countdown)s>%(wait_seconds)s</span>秒お待ちください。"

msgid "page.partner_download.url"
msgstr "📚<a %(a_download)s>ここ</a>からダウンロードできます。"

#, fuzzy
msgid "page.partner_download.li4"
msgstr "お待ちいただきありがとうございます。これにより、すべての人が無料でウェブサイトにアクセスできるようになります！😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "警告: 過去24時間以内にあなたのIPアドレスから大量のダウンロードがありました。ダウンロード速度が通常より遅くなる可能性があります。"

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "過去24時間以内のあなたのIPアドレスからのダウンロード数: %(count)s。"

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "VPN、共有インターネット接続、またはISPがIPを共有している場合、この警告はそのためかもしれません。"

#, fuzzy
msgid "page.partner_download.wait"
msgstr "すべての人に無料でファイルをダウンロードする機会を提供するために、このファイルをダウンロードする前にお待ちいただく必要があります。"

#, fuzzy
msgid "page.partner_download.li1"
msgstr "待っている間、別のタブでAnna’s Archiveを引き続き閲覧してください（ブラウザがバックグラウンドタブの更新をサポートしている場合）。"

#, fuzzy
msgid "page.partner_download.li2"
msgstr "複数のダウンロードページを同時に読み込むことができますが、1つのサーバーにつき1つのファイルのみダウンロードしてください。"

#, fuzzy
msgid "page.partner_download.li3"
msgstr "一度ダウンロードリンクを取得すると、数時間有効です。"

msgid "layout.index.header.title"
msgstr "アンナのアーカイブ"

msgid "page.scidb.header"
msgstr "SciDB科学データ向けのデータベース"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Anna’s Archiveにある記録"

msgid "page.scidb.download"
msgstr "ダウンロード"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "人類の知識のアクセス性と長期保存を支援するために、<a %(a_donate)s>メンバー</a>になってください。"

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "ボーナスとして、🧬&nbsp;SciDBはメンバーのために制限なしでより速く読み込まれます。"

msgid "page.scidb.refresh"
msgstr "問題がある場合は、<a %(a_refresh)s>再読み込み</a>してください。"

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "プレビューはまだ利用できません。<a %(a_path)s>Anna’s Archive</a>からファイルをダウンロードしてください。"

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDBはSci-Hubの継続であり、馴染みのあるインターフェースとPDFの直接表示を提供します。DOIを入力して表示してください。"

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "私たちは、Sci-Hubの全コレクションと新しい論文を提供しています。ほとんどは、Sci-Hubに似た使い慣れたインターフェースで直接閲覧できます。一部は外部ソースからダウンロードする必要があり、その場合はリンクを表示します。"

msgid "page.search.title.results"
msgstr "%(search_input)s - 検索"

msgid "page.search.title.new"
msgstr "新規検索"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "のみを含める"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "除外する"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "未確認"

msgid "page.search.tabs.download"
msgstr "ダウンロード"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "ジャーナル記事"

msgid "page.search.tabs.digital_lending"
msgstr "デジタル貸出"

msgid "page.search.tabs.metadata"
msgstr "メタデータ"

msgid "common.search.placeholder"
msgstr "タイトル、著者、DOI、ISBN、MD5…"

msgid "common.search.submit"
msgstr "検索"

msgid "page.search.search_settings"
msgstr "検索設定"

msgid "page.search.submit"
msgstr "検索"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "検索に時間がかかりすぎました。これは広範なクエリでは一般的です。フィルターのカウントが正確でない場合があります。"

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "検索に時間がかかりすぎましたので、不正確な結果が表示される可能性があります。時々、<a %(a_reload)s>ページをリロードする</a>と役立つことがあります。"

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "表示"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "リスト"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "テーブル"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "高度な"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "説明とメタデータのコメントを検索"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "特定の検索フィールドを追加"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "（特定の検索フィールド）"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "出版年"

msgid "page.search.filters.content.header"
msgstr "コンテンツ"

msgid "page.search.filters.filetype.header"
msgstr "ファイルタイプ"

msgid "page.search.more"
msgstr "さらに表示"

msgid "page.search.filters.access.header"
msgstr "アクセス"

msgid "page.search.filters.source.header"
msgstr "情報源"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "AAによってスクレイピングされ、オープンソース化されました"

msgid "page.search.filters.language.header"
msgstr "言語"

msgid "page.search.filters.order_by.header"
msgstr "並び替え"

msgid "page.search.filters.sorting.most_relevant"
msgstr "最適な"

msgid "page.search.filters.sorting.newest"
msgstr "最新"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "（出版年）"

msgid "page.search.filters.sorting.oldest"
msgstr "最も古い"

msgid "page.search.filters.sorting.largest"
msgstr "最大"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "（ファイルサイズ）"

msgid "page.search.filters.sorting.smallest"
msgstr "最小"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "（オープンソース）"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "ランダム"

msgid "page.search.header.update_info"
msgstr "この検索インデックスは毎月更新されます。現在、%(last_data_refresh_date)sまでのエントリーが含まれています。より詳細な技術情報については、%(link_open_tag)sデータセットのページ</a>をご覧ください。"

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "コードで検索インデックスを探索するには、<a %(a_href)s>コードエクスプローラー</a>を使用してください。"

msgid "page.search.results.search_downloads"
msgstr "ここに入力して、%(count)s 件の直接ダウンロードできるファイルを検索しましょう。これらのファイルは<a %(a_preserve)s>永遠に保存されます</a>。"

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "実際、誰でも<a %(a_torrents)s>統一されたトレントリスト</a>をシードすることで、これらのファイルを保存するのを手助けできます。"

msgid "page.search.results.most_comprehensive"
msgstr "現在、私たちは書籍や論文などを集めた、世界最大級のオープンカタログを公開中です。Sci-Hub、Library Genesis、Z-Library、<a %(a_datasets)s>そのほかいろいろ</a>もミラーしています。"

msgid "page.search.results.other_shadow_libs"
msgstr "他にもミラーすべき「シャドウライブラリ」を見つけた場合や、ご不明な点がありましたら、%(email)s までご連絡ください。"

msgid "page.search.results.dmca"
msgstr "DMCAや著作権に関する申し立ては、<a %(a_copyright)s>こちらをクリック</a>してください。"

msgid "page.search.results.shortcuts"
msgstr "ヒント：より速く操作するには、以下のキーボードショートカットをご利用ください：「/」検索バーにフォーカス、「Enter」検索、「j」上へ、「k」下へ、「<」前のページ、「>」次のページ"

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "論文をお探しですか？"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "ボックスに入力して、%(count)sの学術論文やジャーナル記事のカタログを検索してください。これらは<a %(a_preserve)s>永遠に保存</a>されます。"

msgid "page.search.results.search_digital_lending"
msgstr "検索ボックスに入力すると、デジタル貸出ライブラリのファイルを探せます。"

msgid "page.search.results.digital_lending_info"
msgstr "この検索機能には、Internet Archive のデジタル貸出ライブラリのメタデータも含まれています。<a %(a_datasets)s>データセットについて詳しく見る</a>。"

msgid "page.search.results.digital_lending_info_more"
msgstr "他のデジタル貸出ライブラリについては、<a %(a_wikipedia)s>Wikipedia</a> および <a %(a_mobileread)s>MobileRead Wiki</a> をご覧ください。"

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "図書館からメタデータを検索するには、ボックスに入力してください。これは<a %(a_request)s>ファイルをリクエストする</a>際に役立ちます。"

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "この検索インデックスには、さまざまなメタデータソースからのメタデータが含まれています。<a %(a_datasets)s>私たちのデータセットについてもっと知る</a>。"

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "メタデータについては、元の記録を表示します。記録のマージは行いません。"

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "世界中の書籍に関するメタデータのソースは非常に多くあります。<a %(a_wikipedia)s>このWikipediaページ</a>は良い出発点ですが、他の良いリストをご存知の場合はお知らせください。"

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "検索ボックスに入力してください。"

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "これらはメタデータレコードであり、<span %(classname)s>ダウンロード可能なファイルではありません</span>。"

msgid "page.search.results.error.header"
msgstr "検索中にエラーが発生しました。"

msgid "page.search.results.error.unknown"
msgstr "<a %(a_reload)s>ページを更新</a>してみてください。問題が解決しない場合は、%(email)sまでメールでご連絡ください。"

msgid "page.search.results.none"
msgstr "<span %(classname)s>ファイルが見つかりません。</span> 検索語やフィルタを変更して、もう一度お試しください。"

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ 検索サーバーが遅い場合、これが誤って発生することがあります。そのような場合は、<a %(a_attrs)s>リロード</a>することで解決することがあります。"

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "次の場所で一致が見つかりました: %(in)s。<a %(a_request)s>ファイルをリクエストする</a>際にそこにあるURLを参照できます。"

msgid "page.search.found_matches.journals"
msgstr "ジャーナル記事 (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "デジタル貸出 (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "メタデータ (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "結果 %(from)s-%(to)s (%(total)s 件中)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ 部分一致"

msgid "page.search.results.partial"
msgstr "%(num)d 部分一致"

#, fuzzy
msgid "page.volunteering.title"
msgstr "ボランティアと報奨金"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archiveはあなたのようなボランティアに依存しています。すべてのコミットメントレベルを歓迎し、私たちが求めている主な支援カテゴリーは2つあります："

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>軽いボランティア作業：</span> 時間が限られている場合でも、手助けできる方法はたくさんあります。継続的なボランティアには、<span %(bold)s>🤝 Anna’s Archiveのメンバーシップ</span>を報酬として提供します。"

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>重労働ボランティア作業（USD$50-USD$5,000の報奨金）:</span> もしあなたが私たちの使命に多くの時間やリソースを捧げることができるなら、もっと密接に協力したいと考えています。最終的には内側のチームに参加することも可能です。予算は限られていますが、最も集中的な作業には<span %(bold)s>💰金銭的報奨金</span>を授与することができます。"

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "時間をボランティアすることができない場合でも、<a %(a_donate)s>寄付</a>、<a %(a_torrents)s>トレントのシード</a>、<a %(a_uploading)s>本のアップロード</a>、または<a %(a_help)s>友達にAnna’s Archiveを紹介する</a>ことで大いに助けることができます。"

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>企業:</span> 企業レベルの寄付や新しいコレクション（例：新しいスキャン、OCR済みのデータセット、データの充実）との交換で、私たちのコレクションへの高速直接アクセスを提供します。<a %(a_contact)s>お問い合わせ</a>ください。また、<a %(a_llm)s>LLMページ</a>もご覧ください。"

msgid "page.volunteering.section.light.heading"
msgstr "軽作業ボランティア"

msgid "page.volunteering.section.light.text1"
msgstr "数時間の余裕がある場合、さまざまな方法で手助けすることができます。<a %(a_telegram)s>Telegramのボランティアチャット</a>に参加することを忘れないでください。"

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "感謝の印として、基本的なマイルストーンには通常6ヶ月の「ラッキーライブラリアン」を提供し、継続的なボランティア作業にはさらに多くを提供します。すべてのマイルストーンには高品質の作業が必要です—いい加減な作業は私たちにとって害になるため、拒否されます。マイルストーンに達したら<a %(a_contact)s>メール</a>してください。"

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "タスク"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "マイルストーン"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "アンナのアーカイブを広めること。例えば、AAで本を推薦したり、ブログ記事にリンクしたり、一般的に私たちのウェブサイトに人々を誘導したりすることです。"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s リンクまたはスクリーンショット。"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "これらは、アンナのアーカイブについて誰かに知らせ、その人が感謝している様子を示すべきです。"

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Open Libraryと<a %(a_metadata)s>リンク</a>してメタデータを改善する。"

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "<a %(a_list)s >ランダムなメタデータの問題のリスト</a>を出発点として使用できます。"

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "修正した問題には必ずコメントを残し、他の人が同じ作業を繰り返さないようにしてください。"

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s 改善した記録のリンク。"

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>ウェブサイトの翻訳</a>。"

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "言語全体を完全に翻訳する（すでにほぼ完了していない場合）。"

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "あなたの言語でAnna’s ArchiveのWikipediaページを改善する。他の言語のAAのWikipediaページや私たちのウェブサイトとブログから情報を含める。他の関連ページにAAへの参照を追加する。"

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "重要な貢献をした編集履歴へのリンク。"

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Z-LibraryやLibrary Genesisフォーラムでの本（または論文など）のリクエストを満たす。私たちには独自の本リクエストシステムはありませんが、これらのライブラリをミラーしているため、それらを改善することはAnna’s Archiveを改善することにもなります。"

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s 完了したリクエストのリンクまたはスクリーンショット。"

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "<a %(a_telegram)s>Telegramのボランティアチャット</a>に投稿された小さなタスク。通常はメンバーシップのため、時には小さな報奨金のため。"

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "ボランティアチャットグループに投稿された小さなタスク。"

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "タスクによる。"

msgid "page.volunteering.section.bounties.heading"
msgstr "報奨金"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "私たちは、堅実なプログラミングスキルや攻撃的なセキュリティスキルを持つ人々を常に求めています。あなたは人類の遺産を保存するために大きな貢献をすることができます。"

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "感謝の気持ちとして、堅実な貢献に対してメンバーシップを提供します。特に重要で困難なタスクに対しては、金銭的な報奨金を提供します。これは仕事の代替として見なされるべきではありませんが、追加のインセンティブとなり、発生した費用を補助することができます。"

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "私たちのコードのほとんどはオープンソースであり、報奨金を授与する際にはあなたのコードも同様にオープンソースであることを求めます。個別に議論できる例外もあります。"

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "報奨金はタスクを最初に完了した人に授与されます。報奨金チケットにコメントして、他の人にあなたが何かに取り組んでいることを知らせることができますので、他の人が待つか、あなたに連絡してチームを組むことができます。しかし、他の人もそれに取り組んであなたを追い越そうとすることは自由です。ただし、いい加減な仕事には報奨金を授与しません。高品質な提出物が近い時期（1日か2日以内）に提出された場合、私たちの裁量で両方に報奨金を授与することがあります。例えば、最初の提出物に100%%、2番目の提出物に50%%（合計150%%）を授与することがあります。"

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "大きな報奨金（特にスクレイピング報奨金）の場合、約5%%を完了し、その方法が全体のマイルストーンにスケールする自信がある場合は、私たちに連絡してください。方法を共有してフィードバックを受ける必要があります。また、この方法で複数の人が報奨金に近づいている場合にどうするかを決定できます。例えば、複数の人に報奨金を授与する、チームを組むことを奨励するなどです。"

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "警告：高額報奨金のタスクは<span %(bold)s>難しい</span>です—簡単なものから始めるのが賢明かもしれません。"

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "<a %(a_gitlab)s>Gitlabの問題リスト</a>に移動し、「ラベル優先度」で並べ替えてください。これにより、私たちが重視するタスクの順序が大まかに示されます。明示的な報奨金がないタスクでも、特に「Accepted」や「Anna’s favorite」とマークされたものはメンバーシップの対象となります。「スタータープロジェクト」から始めるのが良いかもしれません。"

msgid "blog.template.subheading"
msgstr "人類史上最大の真にオープンな図書館、<a %(wikipedia_annas_archive)s>アンナのアーカイブ</a>に関する更新情報。"

msgid "layout.index.title"
msgstr "アンナのアーカイブ"

msgid "layout.index.meta.description"
msgstr "世界最大のオープンソース、オープンデータの図書館。Sci-Hub,Genesis,Z-Libraryなどなど。"

msgid "layout.index.meta.opensearch"
msgstr "アンナのアーカイブを検索"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "アンナのアーカイブはあなたの助けを必要としています！"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "私たちを潰そうとする者は多いですが、決して屈しません。"

msgid "layout.index.header.banner.fundraiser.now"
msgstr "今寄付すると、高速ダウンロードの回数が<strong>2倍</strong>になります。"

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "今月末まで有効です。"

msgid "layout.index.header.nav.donate"
msgstr "寄付"

msgid "layout.index.header.banner.holiday_gift"
msgstr "人類の知識を保存する: 素晴らしいホリデーギフトです!"

msgid "layout.index.header.banner.surprise"
msgstr "愛する人を驚かせて、メンバーシップ付きのアカウントをプレゼントしましょう。"

msgid "layout.index.header.banner.mirrors"
msgstr "Anna’s Archive の耐障害性を高めるために、ミラーサイトの運用に協力してくださるボランティアを募集しています。"

msgid "layout.index.header.banner.valentine_gift"
msgstr "完璧なバレンタインギフト！"

msgid "layout.index.header.banner.new_donation_method"
msgstr "%(method_name)s という新しい寄付の方法をご用意しました。%(donate_link_open_tag)s寄付</a>を検討していただけるとありがたいです。 - このウェブサイトを運営するのは決して安くはありません。ありがとうございます。"

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "世界最大のコミックの影の図書館を<a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">支援</a>するための募金活動を行っております。貴方の協力に感謝いたします！<a href=\"/donate\">寄付</a>もし寄付ができなければ、あなたの友達に伝えることや、<a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>、<a href=\"https://t.me/annasarchiveorg\">Telegram</a>で私達をフォローすることも考えていただけると幸いです。"

msgid "layout.index.header.recent_downloads"
msgstr "最近のダウンロード："

msgid "layout.index.header.nav.search"
msgstr "検索"

msgid "layout.index.header.nav.faq"
msgstr "よくある質問"

msgid "layout.index.header.nav.improve_metadata"
msgstr "メタデータの修正"

msgid "layout.index.header.nav.volunteering"
msgstr "ボランティア参加とバグ報奨金制度"

msgid "layout.index.header.nav.datasets"
msgstr "データセット"

msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

msgid "layout.index.header.nav.activity"
msgstr "アクティビティ"

msgid "layout.index.header.nav.codes"
msgstr "コードエクスプローラー"

msgid "layout.index.header.nav.llm_data"
msgstr "LLMデータ"

msgid "layout.index.header.nav.home"
msgstr "ホーム"

msgid "layout.index.header.nav.annassoftware"
msgstr "アンナのソフトウェア↗"

msgid "layout.index.header.nav.translate"
msgstr "翻訳する ↗"

msgid "layout.index.header.nav.login_register"
msgstr "ログイン/登録"

msgid "layout.index.header.nav.account"
msgstr "アカウント"

msgid "layout.index.footer.list1.header"
msgstr "アンナのアーカイブ"

msgid "layout.index.footer.list2.header"
msgstr "連絡を取る"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA/著作権主張"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "高度な"

msgid "layout.index.header.nav.security"
msgstr "セキュリティ"

msgid "layout.index.footer.list3.header"
msgstr "ミラーサイト"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "非提携"

msgid "page.search.results.issues"
msgstr "❌ このファイルには問題がある可能性があります。"

msgid "page.search.results.fast_download"
msgstr "高速ダウンロード"

msgid "page.donate.copy"
msgstr "コピー"

msgid "page.donate.copied"
msgstr "コピーされました！"

msgid "page.search.pagination.prev"
msgstr "前へ"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "次へ"

#~ msgid "page.md5.box.download.mirror"
#~ msgstr "ミラー #%(num)d: %(link)s %(extra)s"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr ""

#~ msgid "page.home.progress_bar.text"
#~ msgstr "人類の文字遺産の 5%% が永久に保存される %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr ""

#~ msgid "page.md5.box.download.text"
#~ msgstr "無料の電子ブック/ファイル %(extension)s を次からダウンロードします。"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr ""

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr "Z-Library 匿名ミラー #%(num)d"

#~ msgid "page.donate.title"
#~ msgstr "寄付"

#~ msgid "page.donate.header"
#~ msgstr "寄付"

#~ msgid "page.donate.text1"
#~ msgstr "アンナのアーカイブは、非営利のオープンソースプロジェクトで、完全にボランティアによって運営されています。ホスティング、ドメイン名、開発、その他の経費をまかなうために寄付を募っています。"

#~ msgid "page.donate.text2"
#~ msgstr "皆様からのご寄付により、本サイトの運営、機能の向上、より多くのコレクションの保存が可能となります。"

#~ msgid "page.donate.text3"
#~ msgstr "最近の寄付金: %(donations)s. 皆さまのご厚意に本当にありがとうございます。私たちは、あなたが私たちに信頼を寄せてくださることに本当に感謝しております、どんな金額でも構いません。"

#~ msgid "page.donate.text4"
#~ msgstr "寄付をご希望の方は、下記よりご希望の方法をお選びください。何か問題が発生した場合は、%(email)sまでご連絡ください。"

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "クレジットカード／デビットカード"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "仮想通貨"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "問題"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "%(link_open_tag)sこのページ</a>にアクセスし、QRコードをスキャンするか、\"paypal.me\"リンクをクリックして指示に従ってください。もしうまくいかない場合は、ページを更新してみてください。そうすれば、別のアカウントが表示されるかもしれません。"

#~ msgid "page.donate.cc.header"
#~ msgstr "クレジットカード／デビットカード"

#~ msgid "page.donate.cc.text1"
#~ msgstr "ビットコイン(BTC)ウォレットに直接入金するために、Sendwyreを使用しています。5分程度で完了します。"

#~ msgid "page.donate.cc.text2"
#~ msgstr "この方法の最低取引額は 30 ドルで、手数料は約 5 ドルです。"

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "段取り："

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1.わたしたちのビットコイン(BTC)財布アドレスをコピーする%(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2.%(link_open_tag)sこのページ</a>にアクセスし、\"buy crypto instantly\" をクリックします"

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. アンナのウォレットアドレスを貼り付け、指示に従います"

#~ msgid "page.donate.crypto.header"
#~ msgstr "暗号"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(BCHでも使えます)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "寄付を送るには、%(link_open_tag)sこのAlipayアカウント</a>を使用してください。もしうまくいかない場合は、ページを更新してみてください、そうすれば違うアカウントが表示されるかもしれません。"

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "この寄付オプションは現在、注文を受け付けておりません。後ほどご確認ください。寄付を希望していただき、誠にありがとうございます!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "寄付金を送るには、%(link_open_tag)sこのPixページ</a>をご利用ください。もしうまくいかない場合は、ページを更新してみてください。そうすれば、別のアカウントが表示されるかもしれません。"

#~ msgid "page.donate.faq.header"
#~ msgstr "よくある質問"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">アンナのアーカイブ</span>は、様々なソースからのデータを集約し、現存する全ての書籍のカタログ化を目指すプロジェクトです。また、<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">影の図書館</a>を通じて、これらすべての書籍をデジタル形式で簡単に利用できるようにするための人類の歩みを追跡しています。<a href=\"/about\">私たちについて</a>もっと知る"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "本 (全て)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "トップページ"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "データセット ▶ ISBN ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "見つかりませんでした"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "\"%(isbn_input)s\" は有効なISBN番号ではありません。ISBNは10文字または13文字で、オプションのダッシュは含まれません。最後の文字が \"X \"である場合を除き、すべての文字は数字でなければなりません。最後の文字は「チェックデジット」であり、他の数字から計算されるチェックサム値と一致しなければなりません。また、国際ISBN機関によって割り当てられた有効範囲内でなければなりません。"

#~ msgid "page.isbn.results.text"
#~ msgstr "データベースに該当するファイル:"

#~ msgid "page.isbn.results.none"
#~ msgstr "データベースに該当するファイルはありません。"

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "検索 ▶ %(num)d+ の<span class=\"italic\">%(search_input)s</span> の検索結果(影の図書館メタデータ内)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "検索 ▶ (影の図書館メタデータ内) %(num)d の <span class=\"italic\">%(search_input)s</span> の 結果"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "検索 ▶ <span class=\"italic\">%(search_input)s</span> の検索エラー"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "検索 ▶ 新規検索"

#~ msgid "page.donate.header.text3"
#~ msgstr "アカウントの作成なしで寄付ができます："

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "一度きりの寄付(特典なし)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "支払いオプションを選択してください。手数料が(かなり)少ない暗号通貨%(bitcoin_icon)sでの支払いも考えていただけると幸いです。"

#~ msgid "page.donate.crypto.intro"
#~ msgstr "すでに暗号通貨をお持ちの方は、こちらが私たちのアドレスになります."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "助けてくれてありがとう！ このプロジェクトは、あなたなしでは成り立ちません。"

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "PayPalでの寄付には匿名性を保てるPayPalクリプトをご利用ください。この寄付の方法に時間を割いていただき、ありがとうございました。"

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "指示に従ってビットコイン（BTC）を購入してください。寄付したい金額だけ購入してください。"

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "変動や手数料のためにビットコインを失っても<em>ご心配なく</em>。暗号通貨では普通のことです、暗号通貨のおかげで私達は匿名で活動できます。"

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "私達のビットコイン(BTC)アドレスを受取人として指定し、指示に従って寄付を行ってください:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "寄付には<a %(a_account)s>こちらのAlipayアカウント</a>をお使いください。"

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "寄付には<a %(a_account)s>こちらのPixアカウント</a>をお使いください。"

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr "<a href=\"javascript:location.reload()\">ページを再読み込み</a>してみてください。それでも問題が解決しない場合は、<a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>、または<a href=\"https://t.me/annasarchiveorg\">Telegram</a>でお知らせください。"

#~ msgid "page.donate.login"
#~ msgstr "メンバーになるには<a href=\"/login\">ログインまたは登録</a>が必要です。アカウントを作成したくなければ上記の\"一度限りの匿名の寄付\"をクリックしてください。支援に感謝しております！"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "トップページ"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "当サイトについて"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "寄付"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "データセット"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "携帯アプリ"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "アンナのブログ"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "アンナのソフトウェア"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "翻訳"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;%(libraries)sなど他にも多数。"

#~ msgid "page.home.preservation.text"
#~ msgstr "私達は書籍、論文、コミック、雑誌などの資料を、様々な<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">影の図書館</a>から一箇所に集めています。これらのデータはすべて永久に保存され、一括複製を容易ににすることで世界中に多くのコピーが存在することになります。このような幅広い配布とオープンソース・コードの組み合わせにより、私たちのウェブサイトはサイトの閉鎖にも強くなっています。<a href=\"/datasets\">私達のデータセット</a>についてもっと知る。"

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "データセット ▶DOI ▶ DOI%(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "見つかりませんでした"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" はDOIではないようです。\"10.\"で始まり、その中にスラッシュがあるはずです。"

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "正規のURL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "このファイルは %(link_open_tag)sSci-Hub</a> にあるかもしれません。"

#~ msgid "page.doi.results.text"
#~ msgstr "データベースで該当するファイル:"

#~ msgid "page.doi.results.none"
#~ msgstr "データベースに該当するファイルはありません。"

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr "今日の分の<strong>🚀高速ダウンロード</strong>は使い果たされました。もしメンバーシップをアップグレードすることに興味があるなら%(email)sにてアンナにご連絡ください。"

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "今日の分の高速ダウンロードを使い果たしました。もしメンバーシップのアップグレードに興味があるなら%(email)sにてアンナにご連絡ください。"

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>他の方法で貢献することはできますか？</div>はい！<a href=\"/about\">アバウトページ</a>の\"支援方法\"をご覧ください。"

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>「収益化」をしているアンナのアーカイブが気に入らない！</div>私たちのプロジェクトの運営方法が気に入らないなら、自分の影の図書館を運営すればいい！私たちのコードとデータはすべてオープンソースなのだから、あなたを止めるものは何もない。(^_-)"

#~ msgid "page.request.title"
#~ msgstr "本をリクエスト"

#~ msgid "page.request.text1"
#~ msgstr "<a %(a_forum)s>Libgen.rs forum</a>でリクエストをお願いしていただけませんか？アカウントを作成し、これらのスレッドにリクエストを投稿してください。"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>電子書籍は<a %(a_ebook)s>こちら</a>をご利用ください.</li><li %(li_item)s>電子書籍化されてない場合は<a %(a_regular)s>こちら</a>のスレッドをご利用ください。</li>"

#~ msgid "page.request.text3"
#~ msgstr "いずれの場合でも、スレッドに記載されているルールに必ず従ってください。"

#~ msgid "page.upload.title"
#~ msgstr "アップロード"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "当サイトについて"

#~ msgid "page.about.header"
#~ msgstr "当サイトについて"

#~ msgid "page.home.search.header"
#~ msgstr "検索"

#~ msgid "page.home.search.intro"
#~ msgstr "影の図書館のカタログを検索。"

#~ msgid "page.home.random_book.header"
#~ msgstr "ランダムな本"

#~ msgid "page.home.random_book.intro"
#~ msgstr "カタログからランダムな本を取り寄せる。"

#~ msgid "page.home.random_book.submit"
#~ msgstr "ランダムな本"

#~ msgid "page.about.text1"
#~ msgstr "アンナのアーカイブは、非営利のオープンソースによる“<a href=\"https://en.wikipedia.org/wiki/Shadow_library\">影の図書館</a>”の検索エンジンです。書籍、論文、コミック、雑誌などの資料を一か所にまとめて検索できる場所が必要だと感じた<a href=\"http://annas-blog.org\">アンナ</a>が作ったものです。"

#~ msgid "page.about.text4"
#~ msgstr "もし有効なDMCA苦情がある場合は、このページの下部を参照するか、%(email)sまでご連絡ください。"

#~ msgid "page.home.explore.header"
#~ msgstr "書籍を探索"

#~ msgid "page.home.explore.intro"
#~ msgstr "一般的な書籍と、影の図書館やデジタル保存の世界では特別な意味を持つ書籍を組み合わせています。"

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "当サイトについて"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "携帯アプリ"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "非公式WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "本をリクエスト"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "アップロード"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "支援方法"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "<a %(a_account)s>このAripayアカウントを使用して%(total)sを寄付"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "今月だけ！"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hubは新しい論文のアップロードを<a %(a_closed)s>一時停止</a>しました。"

#~ msgid "page.donate.payment.intro"
#~ msgstr "支払い方法を選択してください。暗号通貨%(bitcoin_icon)sでのお支払いには手数料が(かなり)少ないため割引を適用しております。"

#~ msgid "page.donate.payment.intro2"
#~ msgstr "支払いオプションを選択してください。現在、暗号ベースの支払い%(bitcoin_icon)sのみ取り扱っております。"

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "銀行が私たちと取引したがらないため、クレジット/デビットカードを直接サポートすることはできません。 :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "しかし、他の支払い方法を使用してクレジット/デビットカードを使用する方法はいくつかあります："

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢低速もしくは外部からのダウンロード"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "ダウンロード"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "もし暗号資産を初めて購入する場合、%(option1)s、%(option2)s、%(option3)sでビットコイン(最もよく使用される暗号通貨)を購入することをお勧めします。"

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "改善したレコードの30リンク。"

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100リンクまたはスクリーンショット。"

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "満たしたリクエストの30リンクまたはスクリーンショット。"

#~ msgid "page.datasets.intro.text1"
#~ msgstr "これらのデータセットを<a %(a_faq)s>アーカイブ</a>または<a %(a_llm)s>LLMトレーニング</a>の目的でミラーリングすることに興味がある場合は、お問い合わせください。"

#~ msgid "page.datasets.ia.intro"
#~ msgstr "このデータセットを<a %(a_archival)s>アーカイブ</a>または<a %(a_llm)s>LLMトレーニング</a>の目的でミラーリングすることに興味がある場合は、ご連絡ください。"

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "メインウェブサイト"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN国情報"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "このデータセットを<a %(a_archival)s>アーカイブ</a>または<a %(a_llm)s>LLMトレーニング</a>の目的でミラーリングすることに興味がある場合は、お問い合わせください。"

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "国際ISBN機関は、各国のISBN機関に割り当てた範囲を定期的に公開しています。これにより、このISBNがどの国、地域、または言語グループに属するかを導き出すことができます。現在、このデータは<a %(a_isbnlib)s>isbnlib</a> Pythonライブラリを通じて間接的に使用しています。"

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "リソース"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "最終更新日: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBNウェブサイト"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "メタデータ"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "「scimag」を除外"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "私たちがメタデータを収集するインスピレーションは、アーロン・シュワルツの「これまでに出版されたすべての本のための1つのウェブページ」という目標であり、彼はそのために<a %(a_openlib)s>Open Library</a>を作成しました。"

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "そのプロジェクトは成功していますが、私たちの独自の立場により、彼らが取得できないメタデータを取得することができます。"

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "もう一つのインスピレーションは、世界にどれだけの本があるのかを知りたいという私たちの願望であり、それにより、まだ保存すべき本の数を計算することができます。"

#~ msgid "page.partner_download.text1"
#~ msgstr "すべての人に無料でファイルをダウンロードする機会を提供するために、このファイルをダウンロードする前に<strong>%(wait_seconds)s秒</strong>待つ必要があります。"

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "ページを自動的に更新します。ダウンロードウィンドウを逃した場合、タイマーが再起動するため、自動更新をお勧めします。"

#~ msgid "page.partner_download.download_now"
#~ msgstr "今すぐダウンロード"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "変換：オンラインツールを使用してフォーマット間の変換を行います。例えば、epubとpdfの間で変換するには、<a %(a_cloudconvert)s>CloudConvert</a>を使用してください。"

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle：ファイルをダウンロード（pdfまたはepubがサポートされています）、次に<a %(a_kindle)s>ウェブ、アプリ、またはメールを使用してKindleに送信</a>します。便利なツール：<a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>。"

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "著者をサポート：これが気に入っていて、余裕がある場合は、オリジナルを購入するか、著者を直接サポートすることを検討してください。"

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "図書館をサポート：これが地元の図書館で利用可能な場合、そこで無料で借りることを検討してください。"

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s 直接大量には利用できず、ペイウォールの背後で半量のみ利用可能"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s アンナのアーカイブは<a %(isbndb)s>ISBNdbメタデータ</a>のコレクションを管理しています。"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdbは、さまざまなオンライン書店からISBNメタデータを収集する会社です。Anna’s ArchiveはISBNdbの書籍メタデータのバックアップを作成しています。このメタデータはAnna’s Archiveを通じて利用可能ですが、現在のところ検索には含まれていません（ISBN番号を明示的に検索した場合を除く）。"

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "技術的な詳細については、以下を参照してください。ある時点で、シャドウライブラリからまだ欠けている書籍を特定し、優先的に見つけたりスキャンしたりするために使用できます。"

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "このデータに関するブログ記事"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdbスクレイプ"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "現在、4.4GBのgzip圧縮<a %(a_jsonl)s>JSON Lines</a>ファイル（解凍後20GB）を含む単一のトレントがあります：「isbndb_2022_09.jsonl.gz」。PostgreSQLに「.jsonl」ファイルをインポートするには、<a %(a_script)s>このスクリプト</a>のようなものを使用できます。直接パイプして、オンザフライで解凍することもできます。"

#~ msgid "page.donate.wait"
#~ msgstr "少なくとも<span %(span_hours)s>2時間</span>待ってから（このページをリフレッシュして）ご連絡ください。"

#~ msgid "page.codes.search_archive"
#~ msgstr "「%(term)s」をAnna’s Archiveで検索"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "寄付にAlipay又はWeChatを使用します。次のページで選択が可能です。"

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Anna’s Archiveのことをソーシャルメディアやオンラインフォーラムで広める、AAで本やリストを推薦する、または質問に答える。"

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s フィクションコレクションは分岐しましたが、<a %(libgenli)s>トレント</a>はまだあります。ただし、2022年以降更新されていません（直接ダウンロードはあります）。"

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s アンナのアーカイブとLibgen.liは、<a %(comics)s>コミックブック</a>と<a %(magazines)s>雑誌</a>のコレクションを共同で管理しています。"

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s ロシアのフィクションと標準文書コレクションにはトレントがありません。"

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "追加コンテンツのトレントは利用できません。Libgen.liウェブサイトにあるトレントは、ここにリストされている他のトレントのミラーです。唯一の例外は、%(fiction_starting_point)sから始まるフィクショントレントです。コミックと雑誌のトレントは、Anna’s ArchiveとLibgen.liの共同リリースです。"

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "<a %(a_href)s><q>Bibliotheca Alexandrina</q></a>のコレクションから、正確な出所は不明です。部分的にthe-eye.euから、部分的に他のソースから。"

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

