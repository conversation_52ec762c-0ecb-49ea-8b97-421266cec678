��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b S  �d J  �f (   /h   Xh �  ei �  Dk k  m   �n T   �o u   �o B   np f   �p �  q K  �r �   
t �  u �   �v �  �w �  �y �  �{ �   m} �  >~ �  � A   �� �   �� [   ��   �    )� D  I� B   �� #   ч    ��    
� B   #�    f�    �� 4   �� #   Ȉ !   �    � N    � N  o�   �� T   ΋ 8   #� 
   \�    j�    v�    ��    ��    �� 	   ��    Č    ، #   ߌ    �    	� 	   #� 
   -�    ;�    G�    `�    � x  �� �  � �   �� (   A� L  j� �   ��   G� �   K�     � �   2�     ɖ �   � %   ��    ՗     �� �  � :   �� h   �    L� V  T� ]  �� �  	� '  ̟    �� 3   � ^   8� �   �� 3   N� 0   �� |   �� 6   0� I   g� 4   ��    � �   � �  n� 7   @� �   x� �    � F   Ч %  � +   =� �   i� �   h� �   �� S   �� �   � �   �� B   \� �   �� �   �� P   f� ;   �� v   �    j� �   v�   #� B   ?�    �� �   �� �   %� 	   �� y  � �   {�   � �  � �   �� '  �� !  ¼ D   � L   )� _   v� j   ־ `   A� a   �� 3   � �   8� W   �� L   � 
   l� t   z� U   �� �   E� �   ��    �� �   ��   �� Q  �� T  �� �   I� �   �� 0  �� �   �� �   �� �   B� f  �� �   Y� �   ��    �� �   �� �   �� �   Y� j  ,� �   �� �   � �   �� �  I� u    � D  v�   �� %   �� [   �� �   A� M   ��   � �   �� c   R� s   ��    *�   G� �   d� {  /�    �� f   �� �   ,� �   �� �   �� �   �� �  �� �  o� !   (� n  J� �  �� M  b� �  ��   L�   k� O   �� �   ��    h� �   q� 7  ,� �  d� �   �   �� �   ��    �� U  �� F  �� �   )�      �   &  ?  �  �      � �    Q   �    # r  0 [  �    � �   	   
 �   �   
 �  �
 V   x L   � �    5    
   < ;  J �   � *  s 0   � B   � �   e    {   v _   � �  R "   �    � i   �   u �   $ E   � �   � &   � �   � �   �    �    � "   � 7   � 6   $  /   [  K   �  h   �     @! '   O! 5   w! N   �! (   �! :   %" ,   `" �   �"    #    #   /# !  A$ �   c%   P& �   ]' �   �' $  �(    �) �   �) �   �* �   `+ S  �+ e  K- �  �. �  �0 �  U2 B   �3 <   )4     f4 �   �4   ?5 �  H7 +  �8 S  :   X; I  l< 4  �> '   �?   @ 7  *A [  bB    �C �   �C )  �D �   �E 3  UF �   �G    ]H #   lH W   �H    �H �  �H x  �K E  �L �   AO �   �O    �P �   �P �   �Q B   �R s   �R   pS �   �T }   �U }  V g   �W   �W    Y 1  Y �   M[ �  �[ �  �] �  v` �   qb    Ec 
  Mc �  Xd    �e [  f �  bg   *i   3k    Jm /  Kn B   {o �  �o �   ~q �   er <   �r W   .s 	   �s :   �s    �s '   �s +   t    ?t �   Vt 	  �t �   x !  �y �   |    �| �  } t  � o  e� �  Ճ j  q� r  ܇    O�   W�    q�    z� �  �� :  $� 
  _� e  m� 
   ӓ   � �  �� 	  ؗ 1  � �  � H  ՞ u  �    �� �   �� ^   �� �  � �  �� �   j� �   � �   �� Y  D�    �� �   �� D   `� Z   ��     � /   � <   M� m  �� �  �� �  �� 8   *� �  c� �   �    �� �   �� :   t� �   �� �   2� (   �� Z   '�    �� G  �� K  ڻ �  &� �  ѿ x   t�    �� c   �� %  b�    �� �   �� �  &� �   �� 7   f� ,   �� A  �� (   
� g  6� y  �� �  � %   �� z   #� �   �� =  *� �  h�    �   )� /   -� �   ]� �   0�   �� �   	� N   �� (   &� �   O� 9   A� �  {� J  � �   O� �   )� [   '� :   �� �   ��   l� s  �� �  �� �  �� /   �� �   ��    �� L  �� �   �� �  �� �  �� (   ,� �  U� '   %� �  M� V   �    X� 
  t�   � �  �� �   6�   1� �  K� #  � �   < �  � �  � �   � �   N  � 3   A
 {   u
 �   �
    s    � S  � �   � /   �
 !  �
 �  � )  p �  � �  \ �    �  � !  j !   � �   �    p �  y "   � !       @    F %   [    �    �    �    �    �    � 	   �    �    �    
 	   ( &   2    Y 	   ^    h 	   o �   y ]   G &   � 
   � '   � &    -   ) 0   W &   � 
   �    �    � '   �    �            '    ,    2 ;   L $   �    � !   � 8   � <   #  0   `     �  %   �  7   �  #   ! ]   &! 6   �!    �! d   �! G   &"    n"    �" "   �"    �"    �"    �"    �"    #    &#    .#    ?#    L#    b# 	   o# 
   y#    �# *   �#    �#    �# 	   �#    �# 	   �#    �#     $    $ 	   
$    $    '$    3$    L$    T$    q$    �$    �$ 	   �$ 	   �$ "   �$    �$    �$     �$    %    % 
   8%    C%    _%    f%    s% v   �% �   & W   �& �   ' �  �' �   �)    V* &   c*    �*    �*    �*    �*    �* $   �* Z   + 0   a+ f   �+     �+ 9   , 9   T, d   �, Z   �, �   N- V   8. =   �.     �.    �.    �.    /    / 
   /    )/    </    A/    P/ 	   Y/    c/    u/    y/    /    �/    �/    �/    �/    �/    �/    �/    �/    �/    0   *0    A1 	   E1    O1     U1    v1 H   }1 d   �1 %   +2 G   Q2 3   �2    �2    �2    �2 j   �2    K3    Q3 .   ^3 m   �3    �3    4 u   4 -   �4 �  �4    ]8 |   �8 �   Z9 �   : >   �: �   <; �   /<   �< �   �=    �>    �> L   �> P   5? V   �? ^   �? R   <@ L   �@ P   �@ "   -A ,   PA    }A 
   �A S   �A *   �A    B    B    +B    :B y   SB 
   �B /   �B X   C    dC    }C V   �C `   �C l  MD    �E    �E �   �E    �F    �F 	   �F    �F    �F +   �F Z  G    gH    �H    �H 
   �H /  �H "   �I    J 	   J �   J    �J    �J &   �J    �J    K '   K �   BK    �K 
   �K P   �K $   JL    oL    �L    �L 0   �L `   �L 
   5M .   CM �   rM i   "N ]   �N    �N �   �N K   �O    8P .   JP    yP �   �P �   AQ    �Q J   R �   `R �   S    �S !   �S    
T �  T =   �U $   �U    V    !V    AV �   ]V    �V #   W &   7W ?   ^W    �W #   �W $   �W ,   �W ]  X ]  yZ   �[ ?   �] >   ^    Y^ 4   f^   �^ �   �_ �   �` !   :a �   \a �  Yb *   d #   /d   Sd B  cf    �g    �g 7   �g    �g /  h    4i N  Ji �  �j �   ;l    2m s   Jm /   �m $   �m e   n   yn   �o �   �p �  /q l   �r 
  s N   )t �   xt �   "u S   �u �   ,v +   �v $   w    )w    :w    Cw &   Vw    }w H   �w 4   �w 	   x )   "x $  Lx "   qy �   �y �   `z �   �z #   �{    �{    �{    �{ %   �{    | '   6|    ^| �   }| "   R} �   u}    6~ �   L~ �    ~   � /  � �   3� )   �    � 	   ��   �� �   ��    6� �  E�    �    ��    �     � $   9�    ^�    g� ?   m� �   �� �   V�    8�    A�    H� �   d� ;  &� �   b� �   �    ��    ��    ��    Ӌ    �    ��    � B   � 1   `� �  �� M   W�    �� j   �� w   #� F   �� d   � K   G� F   ��    ڐ ^   � @   A� �   �� L   � @   f�    �� �   �� ^   �� ?   � d   A� N   �� 9   ��    /� :   8� q   s� �   � ;   � �   ��    j� !  p� R   �� S   � �   9�    ř �  Ι �   ��    ��    �� 
   ʜ    ՜   ܜ +   �� m   !� �   �� �   \� �    � �   �� �   �� �   0� �   � ^   ��   � n   � i  �� �   �� �   �� 
   �� 
   �� 
   �� �   ƨ 
   T� 
   b� G   p� X   �� �   � 
   � v   � C   i� r   �� 7    � p   X� ^   ɬ 
   (� �   6� 
   ŭ 
   ӭ 
   � %  � �   � �  ��    @� 	   Q� 
   [� �   f�    � ,   +� �   X� �   R� %   �    
�    � 8   8� :   q� -   �� "   ڵ "   �� �    �    � �   �   �� �   �� V   � �   n� �   � �  � B  � �   .� �   �� �   S�    �� k  ��    j� a  �� �   ��   �� �   �� =  a� �   �� �   d� v   �� 7   o� >   ��    �� +   ��    (�    4�    M� �   ^�    M� q   V� 1   �� 	   ��    �    �    � z   6� O   �� C   � �   E� 0   B�    s� 
   �� "   �� ,   ��    ��    �� 	   ��    � 
   �    �    +�    7�    D� �   c�    ��    �    �    )� 
   9�    G�    W�    f�    v�    �� &   �� ^   ��    (� 7   <� Z   t� �   �� �   q� �   %�   �� �   �� d  �� #   � s   4� 6   �� H   �� C   (� �   l� �   &� V   �� y   F�    �� m   �� z   I� �   ��    S�     Z�    {�    ��    �� $   ��    �� 1   ��    �    �    8� "   S�    v�    ��    ��    ��    ��    ��    ��    ��    � &   � +   9� �   e� �   �� $   {� %   �� n   �� ^   5� u   �� 5   
� 4   @� 4   u� <   �� H   �� 1   0� �   b�   )� '  ?�    g� F   �� �   �� 0   b� �   �� �   �� �   v� T   �    V� �   o� �   � z   �� )   � v   C� �   ��    �� 0   ��    �� @   
� r   K� q   ��    0�    8�    A� #   H� �   l� A   �� (   >� ;   g� %   �� !   �� %   �� D   �    V� v   m� ;   �� �    � V   � L   ^� �   �� @   S� #   ��     �� #   �� !   �� #   � "   C� #   f� 5   �� 0   �� P  �� B   B� :   �� A   �� 0   � 	   3� }   =� r   �� �   .� �   �� 
   �� �   �� '   C�    k� �   ��    e� 6   |� C   �� A   �� e   9� V   �� U   ��    L�     c� �   �� ?      !   `  J   �    �  B   � s    n   � Q    j   T (   � B   � ~   +    � f   �    + �   G 9   � C   .    r #   � �   � B   � Y   � �   4 ^   � T   	 �   s	 �   �	    �
    �
 ^   �
 B       G    Z    u    �    � -   � �   � |   h    �    � &   
 !   ?
 )   a
 |   �
 >    y   G S   �      s   6 �   � 2   � U   �     
 R   + =   ~ !   � w   � p   V /   � N   �    F 6   e W   �    �     ,   �    � .   � l   � �   e @   ?    �    � @   �    � V    7   ] �   � }   ?    � E   � �       � �   � M   � !   � J   � 
  G    R    X    Z    \ )   q E   � P   � %   2    X    k #   z 9   � 6   �     P    E   h    � '   � 0   �        7 ]   I   � D   �  
   �     �  �   ! A  �! W   # 
   ]# B  h# �  �$ 0   N& �   & "   '   .' )   N) i   x)    �) 
   �) �  *    �+ g   �+ {   O, �   �, j   S-    �- \   �- 6   4.    k. }   �. B   / +   K/ Y   w/ .   �/ ]    0 �   ^0 �   �0 ,   �1 �   �1 �  �2 �   4 %   �4 N  �4 �   36 �   7 >  8 �   F9 +   :    7: �   T: ,   �:   '; ~   += E   �=    �=    > �  ">    �? �   �?   �@ 1  �A   �B A   D H   MD U   �D %   �D /   E L   BE j   �E    �E    F >    F    _F    zF A   �F c   �F <   4G 
   qG n   |G �   �G �   �H    oI    �I    �I G   �I [   �I N   =J �   �J i   QK    �K &   �K �   �K 
   �L �   �L �  �M �   =P T   =Q $   �Q    �Q 	   �Q    �Q F   �Q 7   R �   JR 
  �R h   �S    QT    eT &   yT    �T w   �T    4U ?   CU    �U /   �U '   �U    �U    �U [   V    ]V    dV %   xV    �V    �V s   �V �   5W ^   �W ]   \X _   �X �   Y    �Y    �Y �   	Z �   �Z �   �[    9\ r   E\ X   �\ H   ] a   Z] j   �] T   '^    |^ W   �^    �^    _    _    -_    E_    b_    {_    �_    �_    �_ *   �_ *   �_ *   `    ?` /   ]` (   �`     �`    �` 
   �` =   a !   Aa    ca 5   ka -   �a ]   �a ,   -b    Zb    pb .   �b (   �b    �b `   �b <   Tc N   �c 	  �c    �d    e    e    ;e 6   Qe 	   �e    �e +   �e i   �e    =f    Yf    yf 
   �f 	   �f 9   �f    �f �   �f    �g     �g    h %   ,h "   Rh    uh !   �h $   �h '   �h ,   i *   1i    \i e   ci (   �i    �i    
j 8   j G   Tj !   �j *   �j    �j i   k f   pk X   �k    0l    8l 	   Wl    al    wl    �l �  �l �   Sn    �n    �n '   �n    o (   .o    Wo    \o o   no ?   �o �   p    �p !   �p �   q !   �q    �q !   �q $   �q 1   r #   Nr    rr 
   �r >   �r    �r "   �r 8   s =  Ss G   �t E   �t W   u    wu �   �u $   "v    Gv G   Wv    �v )   �v    �v <   �v G   4w *   |w �   �w    Nx    bx !   ux    �x    �x    �x     �x    �x    y _   *y �   �y     �z "   �z �   �z �   �{    \| $   v|    �|    �|    �|    �|    �|    }    }    *}    =}    L} 
   ]}    k}    z}    �}    �}    �} +   �} �   �} �   �~ `  � (  � �  � �   Є �  ��    M�   b�    g� �   z� �   w� �  t� �   � e  � B   G� �   �� >   [�    �� O   �� Y   � d   b� h   ǐ �   0� �   � �   �� M  I�    �� �   �� �   o� ^   M� �   ��    Y� �   m� y  M� �   ǚ �   ��    m� \   y� g   ֜ �   >� �   '� i   �� �   #�    �� !   ß    � B   �� 1   <�    n� �   w� @   � �   ]� (   � �   � �   �� @   K� Q   �� V   ޣ k   5� [   �� P   �� i   N� j   �� �   #� �   ��    T� '   \�    �� U   �� 4   ��    /�    8� H   @�    ��    ��    �� =   �� 4   �� 6   /�    f�    �� 	   ��    �� 	   �� Y   �� u   � J   w� 1   ª    �� &   �� 4   #�    X�    e� 	   q�    {�    ��    ��    ��    ��    ��    ��    ��    ƫ 
   ګ    �    ��    �    "� 	   .� 
   8�    C� 2   I� #   |�    �� �   ��    C� c   X� �   �� 	   z�    �� 
   ��    ��    ��    ��    �� �   ® �   [� P   �    7�    F� u   a�    װ u   � �   a� '   �    )� v   E� �   �� X   �� �   � ~   �� .   @� �   o�    ��    � W   3� n   ��    �� �   � �   ķ �   I� U   �    C�    ]�    d� 	   |�    ��    ��    �� #   �� �   ڹ �   j� �   �� �   �� �   z� d   S� E   �� �  �� u  Ŀ   :� �   <� '  �� M  ��    A� �   I� %  � �   *� k  �� �  J� �   � t  ��    �� ?   � �   O� q  �� I   c� �   ��    ��    ��    �� �   �� E   8� q   ~� 6   �� `   '� D   �� [   �� )   )� �   S� A   � 1   P� I   �� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: cs
Language-Team: cs <<EMAIL>>
Plural-Forms: nplurals=3; plural=((n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library je populární (a nelegální) knihovna. Vzali sbírku Library Genesis a udělali ji snadno vyhledatelnou. Navíc se stali velmi efektivními v získávání nových příspěvků knih tím, že motivují přispívající uživatele různými výhodami. V současné době tyto nové knihy nevracejí zpět do Library Genesis. A na rozdíl od Library Genesis, neumožňují snadné zrcadlení své sbírky, což brání širokému uchování. To je důležité pro jejich obchodní model, protože účtují peníze za přístup k jejich sbírce ve velkém (více než 10 knih denně). Neděláme morální soudy o vybírání peněz za hromadný přístup k nelegální sbírce knih. Je nepochybné, že Z-Library byla úspěšná v rozšiřování přístupu k vědomostem a získávání více knih. Jsme tu jednoduše proto, abychom udělali svou část: zajistili dlouhodobé uchování této soukromé sbírky. - Anna a tým (<a %(reddit)s>Reddit</a>) V původním vydání Zrcadlení pirátské knihovny (EDIT: přesunuto do <a %(wikipedia_annas_archive)s>Annin archiv</a>), jsme vytvořili zrcadlení Z-Library, velké nelegální sbírky knih. Jako připomínku, toto jsme napsali v původním blogovém příspěvku: Tato sbírka pochází z poloviny roku 2021. Mezitím Z-Library rostla ohromujícím tempem: přidali asi 3,8 milionu nových knih. Jsou tam jistě nějaké duplikáty, ale většina z nich se zdá být skutečně nové knihy nebo kvalitnější skeny dříve odeslaných knih. To je z velké části díky zvýšenému počtu dobrovolných moderátorů v Z-Library a jejich systému hromadného nahrávání s deduplikací. Rádi bychom jim k těmto úspěchům poblahopřáli. S potěšením oznamujeme, že jsme získali všechny knihy, které byly přidány do Z-Library mezi naším posledním zrcadlením a srpnem 2022. Také jsme se vrátili a stáhli některé knihy, které jsme poprvé přehlédli. Celkově je tato nová sbírka asi 24TB, což je mnohem větší než ta poslední (7TB). Naše zrcadlení nyní činí celkem 31TB. Opět jsme deduplikovali proti Library Genesis, protože pro tuto sbírku jsou již dostupné torrenty. Prosím, navštivte Pirate Library Mirror, abyste si prohlédli novou sbírku (EDIT: přesunuto na <a %(wikipedia_annas_archive)s>Annin archiv</a>). Tam je více informací o tom, jak jsou soubory strukturovány a co se od minule změnilo. Odkaz na to zde nebudeme uvádět, protože toto je jen blogová stránka, která nehostí žádné nelegální materiály. Samozřejmě, seedování je také skvělý způsob, jak nám pomoci. Děkujeme všem, kteří seedují naši předchozí sadu torrentů. Jsme vděční za pozitivní odezvu a rádi, že je tolik lidí, kteří se starají o uchování vědomostí a kultury tímto neobvyklým způsobem. 3x nové knihy přidány do Zrcadlení pirátské knihovny (+24TB, 3,8 milionu knih) Přečtěte si doprovodné články od TorrentFreak: <a %(torrentfreak)s>první</a>, <a %(torrentfreak_2)s>druhý</a> - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) doprovodné články od TorrentFreak: <a %(torrentfreak)s>první</a>, <a %(torrentfreak_2)s>druhý</a> Nedávno byly „stínové knihovny“ na pokraji zániku. Sci-Hub, obrovský nelegální archiv akademických článků, přestal přijímat nová díla kvůli soudním sporům. „Z-Library“, největší nelegální knihovna knih, viděla své údajné tvůrce zatčené na základě obvinění z porušení autorských práv. Neuvěřitelně se jim podařilo uniknout zatčení, ale jejich knihovna je stále ohrožena. Některé země již dělají verzi tohoto. TorrentFreak <a %(torrentfreak)s>uvedl</a>, že Čína a Japonsko zavedly výjimky pro AI do svých autorských zákonů. Není nám jasné, jak to interaguje s mezinárodními smlouvami, ale určitě to poskytuje krytí jejich domácím společnostem, což vysvětluje, co jsme viděli. Pokud jde o Annin archiv — budeme pokračovat v naší podzemní práci zakořeněné v morálním přesvědčení. Naším největším přáním je však vstoupit na světlo a legálně zesílit náš dopad. Prosím, reformujte autorská práva. Když Z-Library čelila uzavření, už jsem měl zálohovanou celou její knihovnu a hledal jsem platformu, kde ji umístit. To byla moje motivace pro založení Annina archivu: pokračování mise těchto dřívějších iniciativ. Od té doby jsme se stali největší stínovou knihovnou na světě, hostující více než 140 milionů chráněných textů v různých formátech — knihy, akademické články, časopisy, noviny a další. Můj tým a já jsme ideologové. Věříme, že uchovávání a hostování těchto souborů je morálně správné. Knihovny po celém světě čelí škrtům ve financování a nemůžeme ani důvěřovat korporacím s dědictvím lidstva. Pak přišla AI. Prakticky všechny velké společnosti budující LLM nás kontaktovaly, aby trénovaly na našich datech. Většina (ale ne všechny!) amerických společností si to rozmyslela, jakmile si uvědomily nelegální povahu naší práce. Naopak čínské firmy nadšeně přijaly naši sbírku, zjevně neznepokojené její legálností. To je pozoruhodné vzhledem k tomu, že Čína je signatářem téměř všech hlavních mezinárodních smluv o autorských právech. Poskytli jsme vysokorychlostní přístup asi 30 společnostem. Většina z nich jsou společnosti LLM a některé jsou datoví makléři, kteří budou naši sbírku dále prodávat. Většina je čínská, ale spolupracovali jsme také se společnostmi z USA, Evropy, Ruska, Jižní Koreje a Japonska. DeepSeek <a %(arxiv)s>přiznal</a>, že dřívější verze byla trénována na části naší sbírky, ačkoli o svém nejnovějším modelu mlčí (pravděpodobně také trénovaném na našich datech). Pokud chce Západ zůstat v čele závodu LLM a nakonec AGI, musí přehodnotit svůj postoj k autorským právům, a to brzy. Ať už s námi souhlasíte nebo ne v naší morální argumentaci, nyní se to stává otázkou ekonomiky a dokonce národní bezpečnosti. Všechny mocenské bloky budují umělé super-vědce, super-hackery a super-armády. Svoboda informací se stává otázkou přežití pro tyto země — dokonce otázkou národní bezpečnosti. Náš tým je z celého světa a nemáme žádné konkrétní zaměření. Ale povzbudili bychom země se silnými autorskými zákony, aby tuto existenční hrozbu využily k jejich reformě. Co tedy dělat? Naše první doporučení je jednoduché: zkrátit dobu trvání autorských práv. V USA jsou autorská práva udělována na 70 let po smrti autora. To je absurdní. Můžeme to sladit s patenty, které jsou udělovány na 20 let po podání. To by mělo být více než dost času pro autory knih, článků, hudby, umění a dalších tvůrčích děl, aby byli plně odměněni za své úsilí (včetně dlouhodobějších projektů, jako jsou filmové adaptace). Pak by měli zákonodárci alespoň zahrnout výjimky pro masové uchovávání a šíření textů. Pokud je hlavní obavou ztráta příjmů od jednotlivých zákazníků, osobní distribuce by mohla zůstat zakázána. Na oplátku by ty, kteří jsou schopni spravovat rozsáhlé archivy — společnosti trénující LLM, spolu s knihovnami a dalšími archivy — pokrývaly tyto výjimky. Reforma autorského práva je nezbytná pro národní bezpečnost Ve zkratce: Čínské LLM (včetně DeepSeek) jsou trénovány na mém nelegálním archivu knih a článků — největším na světě. Západ musí přepracovat autorské právo jako otázku národní bezpečnosti. Podívejte se na <a %(all_isbns)s>původní příspěvek na blogu</a> pro více informací. Vyhlásili jsme výzvu k vylepšení tohoto projektu. Udělili bychom odměnu za první místo ve výši 6 000 $, za druhé místo 3 000 $ a za třetí místo 1 000 $. Kvůli ohromující odezvě a neuvěřitelným příspěvkům jsme se rozhodli mírně navýšit výherní fond a udělit čtyři třetí místa po 500 $. Vítězové jsou uvedeni níže, ale nezapomeňte se podívat na všechny příspěvky <a %(annas_archive)s>zde</a> nebo si stáhnout náš <a %(a_2025_01_isbn_visualization_files)s>kombinovaný torrent</a>. První místo 6 000 $: phiresky Tento <a %(phiresky_github)s>příspěvek</a> (<a %(annas_archive_note_2951)s>komentář na Gitlabu</a>) je prostě všechno, co jsme chtěli, a ještě víc! Obzvláště se nám líbily neuvěřitelně flexibilní možnosti vizualizace (dokonce s podporou vlastních shaderů), ale s komplexním seznamem přednastavení. Také se nám líbilo, jak je vše rychlé a plynulé, jednoduchá implementace (která ani nemá backend), chytrá minimapa a rozsáhlé vysvětlení v jejich <a %(phiresky_github)s>příspěvku na blogu</a>. Neuvěřitelná práce a zasloužený vítěz! - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Naše srdce jsou plná vděčnosti. Pozoruhodné nápady Mrakodrapy pro vzácnost Spousta posuvníků pro porovnání datasetů, jako byste byli DJ. Měřítko s počtem knih. Pěkné štítky. Skvělá výchozí barevná schéma a tepelná mapa. Unikátní zobrazení mapy a filtry Anotace a také živé statistiky Živé statistiky Některé další nápady a implementace, které se nám obzvláště líbily: Mohli bychom pokračovat ještě chvíli, ale zastavme se zde. Nezapomeňte se podívat na všechny příspěvky <a %(annas_archive)s>zde</a>, nebo si stáhněte náš <a %(a_2025_01_isbn_visualization_files)s>kombinovaný torrent</a>. Tolik příspěvků a každý přináší jedinečnou perspektivu, ať už v UI nebo implementaci. Alespoň první místo začleníme do naší hlavní webové stránky, a možná i některé další. Také jsme začali přemýšlet o tom, jak organizovat proces identifikace, potvrzování a následného archivování nejvzácnějších knih. Více informací přijde. Děkujeme všem, kteří se zúčastnili. Je úžasné, že tolik lidí se zajímá. Snadné přepínání datasetů pro rychlé porovnání. Všechny ISBN CADAL SSNOs Únik dat CERLALC DuXiu SSIDy EBSCOhostův eBook Index Google Books Goodreads Internetový archiv ISBNdb ISBN Globální registr vydavatelů Libby Soubory v Annině archivu Nexus/STC OCLC/Worldcat OpenLibrary Ruská státní knihovna Imperiální knihovna Trantoru Druhé místo 3 000 $: hypha „Zatímco dokonalé čtverce a obdélníky jsou matematicky příjemné, neposkytují v kontextu mapování lepší lokalitu. Věřím, že asymetrie inherentní v těchto Hilbertových nebo klasických Mortonových křivkách není chybou, ale vlastností. Stejně jako je Itálie díky svému proslulému tvaru boty okamžitě rozpoznatelná na mapě, mohou jedinečné „zvláštnosti“ těchto křivek sloužit jako kognitivní orientační body. Tato jedinečnost může zlepšit prostorovou paměť a pomoci uživatelům se orientovat, což může usnadnit lokalizaci konkrétních oblastí nebo rozpoznávání vzorů.“ Další neuvěřitelný <a %(annas_archive_note_2913)s>příspěvek</a>. Není tak flexibilní jako první místo, ale ve skutečnosti jsme preferovali jeho makroúrovňovou vizualizaci před prvním místem (křivka vyplňující prostor, hranice, označování, zvýrazňování, posouvání a přibližování). <a %(annas_archive_note_2971)s>Komentář</a> od Joea Davise s námi rezonoval: A stále spousta možností pro vizualizaci a vykreslování, stejně jako neuvěřitelně plynulé a intuitivní uživatelské rozhraní. Pevné druhé místo! - Anna a tým (<a %(reddit)s>Reddit</a>) Před několika měsíci jsme vyhlásili <a %(all_isbns)s>odměnu 10 000 $</a> za vytvoření nejlepší možné vizualizace našich dat zobrazujících prostor ISBN. Zdůraznili jsme zobrazení, které soubory jsme již archivovali/nearchivovali, a později dataset popisující, kolik knihoven drží ISBN (měřítko vzácnosti). Byli jsme ohromeni reakcí. Bylo zde tolik kreativity. Velké díky všem, kteří se zúčastnili: vaše energie a nadšení jsou nakažlivé! Nakonec jsme chtěli odpovědět na následující otázky: <strong>které knihy existují na světě, kolik jsme jich již archivovali a na které knihy bychom se měli zaměřit dále?</strong> Je skvělé vidět, že tolik lidí se o tyto otázky zajímá. Začali jsme s jednoduchou vizualizací sami. Na méně než 300 kb tento obrázek stručně představuje největší plně otevřený „seznam knih“, který byl kdy v historii lidstva sestaven: Třetí místo 500 $ #1: maxlion V tomto <a %(annas_archive_note_2940)s>příspěvku</a> se nám opravdu líbily různé druhy pohledů, zejména srovnávací a vydavatelské pohledy. Třetí místo 500 $ #2: abetusk I když není uživatelské rozhraní nejvíce vyleštěné, tento <a %(annas_archive_note_2917)s>příspěvek</a> splňuje mnoho kritérií. Obzvláště se nám líbila jeho srovnávací funkce. Třetí místo 500 $ #3: conundrumer0 Stejně jako první místo, tento <a %(annas_archive_note_2975)s>příspěvek</a> nás ohromil svou flexibilitou. Nakonec je to to, co dělá skvělý nástroj pro vizualizaci: maximální flexibilita pro pokročilé uživatele, zatímco zůstává jednoduchý pro průměrné uživatele. Třetí místo 500 $ #4: charelf Poslední <a %(annas_archive_note_2947)s>příspěvek</a>, který získal odměnu, je docela základní, ale má některé jedinečné funkce, které se nám opravdu líbily. Líbilo se nám, jak ukazují, kolik datasetů pokrývá konkrétní ISBN jako měřítko popularity/spolehlivosti. Také se nám opravdu líbila jednoduchost, ale účinnost použití posuvníku průhlednosti pro srovnání. Vítězové odměny za vizualizaci ISBN ve výši 10 000 $ Ve zkratce: Dostali jsme neuvěřitelné příspěvky na odměnu za vizualizaci ISBN ve výši 10 000 $. Pozadí Jak může Annin archiv dosáhnout svého cíle zálohovat veškeré lidské vědění, aniž by věděl, které knihy jsou stále k dispozici? Potřebujeme seznam úkolů. Jedním ze způsobů, jak to zmapovat, je prostřednictvím čísel ISBN, která jsou od 70. let 20. století přidělována každé vydané knize (ve většině zemí). Neexistuje žádná centrální autorita, která by znala všechna přidělení ISBN. Místo toho je to distribuovaný systém, kde země dostávají rozsahy čísel, které pak přidělují menší rozsahy hlavním vydavatelům, kteří mohou dále rozdělovat rozsahy menším vydavatelům. Nakonec jsou jednotlivá čísla přidělována knihám. Začali jsme mapovat ISBN <a %(blog)s>před dvěma lety</a> s naším skenováním ISBNdb. Od té doby jsme skenovali mnoho dalších zdrojů metadata, jako jsou <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby a další. Úplný seznam lze nalézt na stránkách „Datasets“ a „Torrents“ na Annině archivu. Nyní máme zdaleka největší plně otevřenou, snadno stahovatelnou sbírku knižních metadata (a tedy ISBN) na světě. <a %(blog)s>Podrobně jsme psali</a> o tom, proč nám záleží na uchovávání, a proč se nyní nacházíme v kritickém období. Musíme nyní identifikovat vzácné, opomíjené a jedinečně ohrožené knihy a uchovat je. Mít dobrá metadata o všech knihách na světě k tomu pomáhá. Odměna $10,000 Silně se bude zohledňovat použitelnost a vzhled. Zobrazte skutečná metadata pro jednotlivé ISBN při přiblížení, jako je název a autor. Lepší křivka vyplňující prostor. Např. cik-cak, jdoucí od 0 do 4 na prvním řádku a pak zpět (v opačném směru) od 5 do 9 na druhém řádku — rekurzivně aplikováno. Různé nebo přizpůsobitelné barevné schémata. Speciální pohledy pro porovnávání datasets. Způsoby, jak ladit problémy, jako například jiná metadata, která se neshodují dobře (např. velmi odlišné názvy). Anotace obrázků s komentáři k ISBN nebo rozsahům. Jakékoliv heuristiky pro identifikaci vzácných nebo ohrožených knih. Jakékoliv kreativní nápady, které vás napadnou! Kód Kód pro generování těchto obrázků, stejně jako další příklady, lze nalézt v <a %(annas_archive)s>tomto adresáři</a>. Přišli jsme s kompaktním datovým formátem, s nímž všechny potřebné informace o ISBN zabírají asi 75 MB (komprimováno). Popis datového formátu a kód pro jeho generování lze nalézt <a %(annas_archive_l1244_1319)s>zde</a>. Pro odměnu není nutné tento formát používat, ale je to pravděpodobně nejpohodlnější formát pro začátek. Můžete transformovat naše metadata jakkoliv chcete (i když veškerý váš kód musí být open source). Nemůžeme se dočkat, co vymyslíte. Hodně štěstí! Forkněte toto repo a upravte tento HTML blogový příspěvek (nejsou povoleny žádné jiné backendy kromě našeho Flask backendu). Udělejte výše uvedený obrázek plynule přibližitelný, abyste mohli přiblížit až na jednotlivé ISBN. Kliknutí na ISBN by mělo vést na stránku s metadata nebo vyhledávání na Annině archivu. Musíte být stále schopni přepínat mezi všemi různými datasets. Rozsahy zemí a vydavatelů by měly být zvýrazněny při najetí myší. Můžete použít např. <a %(github_xlcnd_isbnlib)s>data4info.py v isbnlib</a> pro informace o zemích a náš „isbngrp“ sken pro vydavatele (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Musí dobře fungovat na desktopu i mobilu. Je zde mnoho k prozkoumání, proto oznamujeme odměnu za zlepšení výše uvedené vizualizace. Na rozdíl od většiny našich odměn je tato časově omezená. Musíte <a %(annas_archive)s>odeslat</a> svůj open source kód do 31.01.2025 (23:59 UTC). Nejlepší příspěvek získá $6,000, druhé místo $3,000 a třetí místo $1,000. Všechny odměny budou vyplaceny pomocí Monero (XMR). Níže jsou uvedena minimální kritéria. Pokud žádný příspěvek nesplní kritéria, můžeme přesto udělit některé odměny, ale to bude na našem uvážení. Pro bonusové body (to jsou jen nápady — nechte svou kreativitu volně plynout): MŮŽETE se zcela odchýlit od minimálních kritérií a vytvořit zcela jinou vizualizaci. Pokud bude opravdu spektakulární, pak to kvalifikuje pro odměnu, ale podle našeho uvážení. Podávejte příspěvky přidáním komentáře k <a %(annas_archive)s>tomuto problému</a> s odkazem na váš forkovaný repozitář, žádost o sloučení nebo rozdíl. - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Tento obrázek má rozměry 1000×800 pixelů. Každý pixel představuje 2 500 ISBN. Pokud máme soubor pro ISBN, uděláme ten pixel zelenější. Pokud víme, že ISBN bylo vydáno, ale nemáme odpovídající soubor, uděláme ho červenější. V méně než 300 kb tento obrázek stručně představuje největší plně otevřený „seznam knih“, jaký kdy byl v historii lidstva sestaven (několik stovek GB komprimovaných v plné velikosti). Ukazuje také: je ještě hodně práce na zálohování knih (máme pouze 16%). Vizualizace všech ISBN — odměna 10 000 $ do 31. 1. 2025 Tento obrázek představuje největší plně otevřený „seznam knih“, jaký kdy byl v historii lidstva sestaven. Vizualizace Kromě přehledového obrázku se můžeme podívat i na jednotlivé datasets, které jsme získali. Použijte rozbalovací nabídku a tlačítka k přepínání mezi nimi. V těchto obrázcích je vidět mnoho zajímavých vzorů. Proč je zde určitá pravidelnost čar a bloků, která se zdá být na různých měřítkách? Co jsou prázdné oblasti? Proč jsou některé datasets tak shlukované? Tyto otázky necháme jako cvičení pro čtenáře. - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Závěr S tímto standardem můžeme vydávat data postupněji a snadněji přidávat nové zdroje dat. Už máme několik vzrušujících vydání v přípravě! Také doufáme, že se stane snazším pro jiné stínové knihovny zrcadlit naše kolekce. Koneckonců, naším cílem je uchovat lidské znalosti a kulturu navždy, takže čím více redundance, tím lépe. Příklad Podívejme se na náš nedávný release Z-Library jako na příklad. Skládá se ze dvou kolekcí: “<span style="background: #fffaa3">zlib3_records</span>” a “<span style="background: #ffd6fe">zlib3_files</span>”. To nám umožňuje odděleně zpracovávat a vydávat záznamy metadata od skutečných souborů knih. Proto jsme vydali dva torrenty se soubory metadata: Také jsme vydali řadu torrentů se složkami binárních dat, ale pouze pro kolekci “<span style="background: #ffd6fe">zlib3_files</span>”, celkem 62: Spuštěním <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> můžeme vidět, co je uvnitř: V tomto případě se jedná o metadata knihy, jak je uvádí Z-Library. Na nejvyšší úrovni máme pouze “aacid” a “metadata”, ale žádnou “data_folder”, protože neexistují odpovídající binární data. AACID obsahuje “22430000” jako primární ID, které vidíme, že je převzato z “zlibrary_id”. Můžeme očekávat, že ostatní AAC v této kolekci budou mít stejnou strukturu. Nyní spusťme <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Jedná se o mnohem menší metadata AAC, i když většina tohoto AAC se nachází jinde v binárním souboru! Koneckonců, tentokrát máme “data_folder”, takže můžeme očekávat, že odpovídající binární data se nacházejí na <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” obsahuje “zlibrary_id”, takže jej můžeme snadno spojit s odpovídajícím AAC v kolekci “zlib_records”. Mohli jsme to spojit různými způsoby, např. přes AACID — standard to nepředepisuje. Všimněte si, že není nutné, aby pole “metadata” samo o sobě bylo JSON. Může to být řetězec obsahující XML nebo jakýkoli jiný datový formát. Můžete dokonce uložit informace o metadata v přidruženém binárním bloku, např. pokud se jedná o velké množství dat. Heterogenní soubory a metadata, co nejblíže původnímu formátu. Binární data mohou být přímo obsluhována webovými servery jako Nginx. Heterogenní identifikátory ve zdrojových knihovnách, nebo dokonce absence identifikátorů. Samostatná vydání metadat vs. dat souborů, nebo vydání pouze metadat (např. naše vydání ISBNdb). Distribuce prostřednictvím torrentů, ale s možností jiných metod distribuce (např. IPFS). Neměnné záznamy, protože bychom měli předpokládat, že naše torrenty budou žít navždy. Inkrementální vydání / připojitelná vydání. Strojově čitelné a zapisovatelné, pohodlně a rychle, zejména pro náš stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Poměrně snadná lidská kontrola, i když to je sekundární k strojové čitelnosti. Snadné zasetí našich sbírek pomocí standardního pronajatého seedboxu. Cíle návrhu Nezajímá nás, aby soubory byly snadno procházetelné ručně na disku, nebo vyhledatelné bez předzpracování. Nezajímá nás, aby byly přímo kompatibilní s existujícím knihovním softwarem. I když by mělo být snadné pro kohokoli zaset naši sbírku pomocí torrentů, neočekáváme, že soubory budou použitelné bez značných technických znalostí a závazku. Naším primárním případem použití je distribuce souborů a souvisejících metadat z různých existujících sbírek. Naše nejdůležitější úvahy jsou: Některé ne-cíle: Protože Annin archiv je open source, chceme náš formát přímo používat. Když obnovujeme náš vyhledávací index, přistupujeme pouze k veřejně dostupným cestám, aby kdokoli, kdo forkuje naši knihovnu, mohl rychle začít. <strong>AAC.</strong> AAC (Annin archiv Container) je jedna položka skládající se z <strong>metadata</strong> a případně <strong>binárních dat</strong>, obojí je neměnné. Má globálně unikátní identifikátor, nazývaný <strong>AACID</strong>. <strong>AACID.</strong> Formát AACID je následující: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Například skutečný AACID, který jsme vydali, je <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Rozsah AACID.</strong> Protože AACID obsahují monotónně narůstající časová razítka, můžeme to použít k označení rozsahů v rámci konkrétní kolekce. Používáme tento formát: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, kde časová razítka jsou včetně. To je v souladu s notací ISO 8601. Rozsahy jsou spojité a mohou se překrývat, ale v případě překrytí musí obsahovat identické záznamy jako ty, které byly dříve vydány v dané kolekci (protože AAC jsou neměnné). Chybějící záznamy nejsou povoleny. <code>{collection}</code>: název kolekce, který může obsahovat ASCII písmena, čísla a podtržítka (ale ne dvojitá podtržítka). <code>{collection-specific ID}</code>: kolekčně specifický identifikátor, pokud je to relevantní, např. ID Z-Library. Může být vynechán nebo zkrácen. Musí být vynechán nebo zkrácen, pokud by AACID jinak přesáhl 150 znaků. <code>{ISO 8601 timestamp}</code>: zkrácená verze ISO 8601, vždy v UTC, např. <code>20220723T194746Z</code>. Toto číslo musí monotónně narůstat při každém vydání, i když jeho přesná sémantika se může lišit podle kolekce. Doporučujeme použít čas stahování nebo generování ID. <code>{shortuuid}</code>: UUID, ale komprimovaný do ASCII, např. pomocí base57. V současnosti používáme Python knihovnu <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Složka s binárními daty.</strong> Složka s binárními daty rozsahu AAC, pro jednu konkrétní kolekci. Tyto mají následující vlastnosti: Adresář musí obsahovat datové soubory pro všechny AAC v rámci specifikovaného rozsahu. Každý datový soubor musí mít jako název souboru svůj AACID (bez přípon). Název adresáře musí být rozsah AACID, předponou <code style="color: green">annas_archive_data__</code>, a bez přípony. Například jeden z našich skutečných vydání má adresář nazvaný<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Doporučuje se, aby tyto složky byly v rozumné velikosti, např. ne větší než 100GB-1TB každá, i když se toto doporučení může časem změnit. <strong>Kolekce.</strong> Každé AAC patří do kolekce, která je podle definice seznamem AAC, jež jsou sémanticky konzistentní. To znamená, že pokud provedete významnou změnu ve formátu metadata, musíte vytvořit novou kolekci. Standard <strong>Soubor metadata.</strong> Soubor metadata obsahuje metadata rozsahu AAC, pro jednu konkrétní kolekci. Tyto mají následující vlastnosti: <code>data_folder</code> je volitelný a je to název složky s binárními daty, která obsahuje odpovídající binární data. Název souboru odpovídajících binárních dat v této složce je AACID záznamu. Každý JSON objekt musí obsahovat následující pole na nejvyšší úrovni: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (volitelné). Žádná jiná pole nejsou povolena. Název souboru musí být rozsah AACID, předponou <code style="color: red">annas_archive_meta__</code> a následován <code>.jsonl.zstd</code>. Například jedno z našich vydání se nazývá<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Jak naznačuje přípona souboru, typ souboru je <a %(jsonlines)s>JSON Lines</a> komprimovaný pomocí <a %(zstd)s>Zstandard</a>. <code>metadata</code> jsou libovolná metadata, podle sémantiky kolekce. Musí být sémanticky konzistentní v rámci kolekce. Předpona <code style="color: red">annas_archive_meta__</code> může být přizpůsobena názvu vaší instituce, např. <code style="color: red">my_institute_meta__</code>. <strong>Kolekce „záznamů“ a „souborů“.</strong> Podle konvence je často výhodné vydávat „záznamy“ a „soubory“ jako různé kolekce, aby mohly být vydávány v různých termínech, např. na základě rychlosti stahování. „Záznam“ je kolekce pouze s metadata, obsahující informace jako názvy knih, autoři, ISBN atd., zatímco „soubory“ jsou kolekce, které obsahují skutečné soubory (pdf, epub). Nakonec jsme se rozhodli pro relativně jednoduchý standard. Je poměrně volný, nenormativní a stále ve vývoji. <strong>Torrenty.</strong> Soubory s metadata a složky s binárními daty mohou být sdruženy v torrentech, s jedním torrentem na soubor s metadata nebo jedním torrentem na složku s binárními daty. Torrenty musí mít původní název souboru/adresáře plus příponu <code>.torrent</code> jako svůj název souboru. <a %(wikipedia_annas_archive)s>Annin archiv</a> se stal zdaleka největší stínovou knihovnou na světě a jedinou stínovou knihovnou svého rozsahu, která je plně open-source a open-data. Níže je tabulka z naší stránky Datasets (mírně upravená): Tohoto jsme dosáhli třemi způsoby: Zrcadlením existujících open-data stínových knihoven (jako Sci-Hub a Library Genesis). Pomocí stínovým knihovnám, které chtějí být otevřenější, ale neměly čas nebo zdroje to udělat (jako sbírka komiksů Libgen). Scrapováním knihoven, které nechtějí sdílet hromadně (jako Z-Library). Pro (2) a (3) nyní sami spravujeme značnou sbírku torrentů (stovky TB). Dosud jsme k těmto sbírkám přistupovali jako k jednorázovým projektům, což znamená, že pro každou sbírku máme na míru vytvořenou infrastrukturu a organizaci dat. To přidává každému vydání značnou režii a činí to obzvláště obtížným provádět více inkrementálních vydání. Proto jsme se rozhodli standardizovat naše vydání. Toto je technický blogový příspěvek, ve kterém představujeme náš standard: <strong>Annin archivní kontejner</strong>. Kontejnery Annina archivu (AAC): standardizace vydání z největší stínové knihovny na světě Annin archiv se stal největší stínovou knihovnou na světě, což nás přimělo standardizovat naše vydání. 300GB+ obalů knih uvolněno Nakonec jsme rádi, že můžeme oznámit malé vydání. Ve spolupráci s lidmi, kteří provozují fork Libgen.rs, sdílíme všechny jejich obaly knih prostřednictvím torrentů a IPFS. To rozloží zátěž při prohlížení obalů mezi více strojů a lépe je zachová. V mnoha (ale ne ve všech) případech jsou obaly knih zahrnuty přímo v souborech, takže se jedná o jakási „odvozená data“. Ale mít je v IPFS je stále velmi užitečné pro každodenní provoz jak Annina archivu, tak různých forků Library Genesis. Jako obvykle můžete toto vydání najít v Pirate Library Mirror (EDIT: přesunuto do <a %(wikipedia_annas_archive)s>Annina archivu</a>). Nebudeme na něj zde odkazovat, ale můžete ho snadno najít. Doufejme, že můžeme trochu zpomalit, nyní když máme slušnou alternativu k Z-Library. Tato pracovní zátěž není zvlášť udržitelná. Pokud máte zájem pomoci s programováním, provozem serverů nebo prací na zachování, určitě nás kontaktujte. Stále je tu hodně <a %(annas_archive)s>práce, kterou je třeba udělat</a>. Děkujeme za váš zájem a podporu. Přechod na ElasticSearch Některé dotazy trvaly velmi dlouho, až do té míry, že zabíraly všechny otevřené připojení. Ve výchozím nastavení má MySQL minimální délku slova, nebo váš index může být opravdu velký. Lidé hlásili, že nemohou vyhledávat „Ben Hur“. Vyhledávání bylo jen částečně rychlé, když bylo plně nahráno do paměti, což vyžadovalo, abychom získali dražší stroj pro jeho provoz, plus některé příkazy pro předběžné načtení indexu při spuštění. Nebyl by to snadný úkol rozšířit ho o nové funkce, jako je lepší <a %(wikipedia_cjk_characters)s>tokenizace pro jazyky bez mezer</a>, filtrování/faceting, třídění, návrhy „měl jste na mysli“, automatické doplňování a podobně. Jeden z našich <a %(annas_archive)s>ticketů</a> byl sbírkou problémů s naším vyhledávacím systémem. Používali jsme MySQL full-textové vyhledávání, protože jsme měli všechna naše data stejně v MySQL. Ale mělo to své limity: Po konzultaci s řadou odborníků jsme se rozhodli pro ElasticSearch. Nebylo to dokonalé (jejich výchozí návrhy „měl jste na mysli“ a funkce automatického doplňování jsou špatné), ale celkově je to mnohem lepší než MySQL pro vyhledávání. Stále nejsme <a %(youtube)s>příliš nadšení</a> z jeho použití pro jakákoli kritická data (i když udělali hodně <a %(elastic_co)s>pokroku</a>), ale celkově jsme s přechodem spokojeni. Prozatím jsme implementovali mnohem rychlejší vyhledávání, lepší podporu jazyků, lepší třídění podle relevance, různé možnosti třídění a filtrování podle jazyka/typu knihy/typu souboru. Pokud vás zajímá, jak to funguje, <a %(annas_archive_l140)s>podívejte se</a> <a %(annas_archive_l1115)s>na</a> <a %(annas_archive_l1635)s>to</a>. Je to docela přístupné, i když by to mohlo potřebovat více komentářů… Annin archiv je plně open source Věříme, že informace by měly být volně dostupné, a náš vlastní kód není výjimkou. Vydali jsme veškerý náš kód na naší soukromě hostované instanci Gitlabu: <a %(annas_archive)s>Annin software</a>. Také používáme sledovač problémů k organizaci naší práce. Pokud se chcete zapojit do našeho vývoje, je to skvělé místo, kde začít. Abychom vám dali ochutnat, na čem pracujeme, podívejte se na naši nedávnou práci na zlepšení výkonu na straně klienta. Protože jsme ještě neimplementovali stránkování, často bychom vraceli velmi dlouhé stránky s výsledky vyhledávání, s 100-200 výsledky. Nechtěli jsme příliš brzy omezit výsledky vyhledávání, ale to znamenalo, že by to zpomalilo některá zařízení. Pro to jsme implementovali malý trik: většinu výsledků vyhledávání jsme zabalili do HTML komentářů (<code><!-- --></code>), a poté jsme napsali malý Javascript, který by detekoval, kdy by měl být výsledek viditelný, v tom okamžiku bychom rozbalili komentář: DOM „virtualizace“ implementována ve 23 řádcích, není potřeba žádných složitých knihoven! Toto je druh rychlého pragmatického kódu, který vzniká, když máte omezený čas a skutečné problémy, které je třeba vyřešit. Bylo hlášeno, že naše vyhledávání nyní funguje dobře na pomalých zařízeních! Dalším velkým úsilím bylo automatizovat vytváření databáze. Když jsme začínali, jednoduše jsme nahodile spojovali různé zdroje. Nyní je chceme udržovat aktuální, takže jsme napsali několik skriptů pro stahování nových metadat z obou forků Library Genesis a jejich integraci. Cílem je nejen učinit to užitečným pro náš archiv, ale také usnadnit práci komukoli, kdo si chce pohrát s metadaty stínové knihovny. Cílem by byl Jupyter notebook, který by měl k dispozici všechny možné zajímavé metadata, abychom mohli provádět další výzkum, jako je zjišťování, jaké <a %(blog)s>procento ISBN je zachováno navždy</a>. Nakonec jsme přepracovali náš systém darování. Nyní můžete použít kreditní kartu k přímému vkladu peněz do našich kryptopeněženek, aniž byste museli opravdu něco vědět o kryptoměnách. Budeme sledovat, jak dobře to funguje v praxi, ale je to velký krok vpřed. S pádem Z-Library a zatčením jejích (údajných) zakladatelů jsme pracovali nepřetržitě, abychom poskytli dobrou alternativu s Anniným archivem (nebudeme zde na něj odkazovat, ale můžete si ho vygooglit). Zde jsou některé z věcí, kterých jsme nedávno dosáhli. Annina aktualizace: plně open source archiv, ElasticSearch, 300GB+ obalů knih Pracovali jsme nepřetržitě, abychom poskytli dobrou alternativu s Anniným archivem. Zde jsou některé z věcí, kterých jsme nedávno dosáhli. Analýza Sémantické duplicity (různé skeny stejné knihy) lze teoreticky odfiltrovat, ale je to složité. Při ručním procházení komiksů jsme našli příliš mnoho falešných pozitiv. Existují některé duplicity čistě podle MD5, což je relativně plýtvání, ale jejich odfiltrování by nám přineslo jen asi 1% in úsporu. V tomto měřítku je to stále asi 1TB, ale také, v tomto měřítku 1TB opravdu nezáleží. Raději bychom neriskovali náhodné zničení dat v tomto procesu. Našli jsme spoustu dat, která nejsou knihami, jako jsou filmy založené na komiksových knihách. To se také zdá být plýtvání, protože tyto jsou již široce dostupné jinými prostředky. Uvědomili jsme si však, že nemůžeme jednoduše odfiltrovat filmové soubory, protože existují také <em>interaktivní komiksové knihy</em>, které byly vydány na počítači, které někdo nahrál a uložil jako filmy. Nakonec, cokoliv bychom mohli smazat ze sbírky, by ušetřilo jen pár procent. Pak jsme si vzpomněli, že jsme sběratelé dat, a lidé, kteří to budou zrcadlit, jsou také sběratelé dat, a tak, „COŽE, SMAZAT?!“ :) Když dostanete 95TB do svého úložného clusteru, snažíte se pochopit, co tam vlastně je… Provedli jsme analýzu, abychom zjistili, zda bychom mohli trochu zmenšit velikost, například odstraněním duplicit. Zde jsou některé z našich zjištění: Proto vám představujeme plnou, neupravenou sbírku. Je to hodně dat, ale doufáme, že dost lidí se o to postará a bude to sdílet. Spolupráce Vzhledem k její velikosti byla tato sbírka dlouho na našem seznamu přání, takže po našem úspěchu se zálohováním Z-Library jsme se zaměřili na tuto sbírku. Nejprve jsme ji přímo stahovali, což byla docela výzva, protože jejich server nebyl v nejlepším stavu. Tímto způsobem jsme získali asi 15TB, ale šlo to pomalu. Naštěstí se nám podařilo spojit se s provozovatelem knihovny, který souhlasil, že nám všechna data pošle přímo, což bylo mnohem rychlejší. Přesto trvalo více než půl roku, než jsme všechna data přenesli a zpracovali, a málem jsme o ně přišli kvůli poškození disku, což by znamenalo začít znovu. Tato zkušenost nás přiměla věřit, že je důležité dostat tato data ven co nejrychleji, aby mohla být zrcadlena široko daleko. Jsme jen jeden nebo dva nešťastně načasované incidenty od toho, abychom tuto sbírku navždy ztratili! Sbírka Rychlý postup znamená, že sbírka je trochu neorganizovaná… Podívejme se na to. Představte si, že máme souborový systém (který ve skutečnosti rozdělujeme mezi torrenty): První adresář, <code>/repository</code>, je strukturovanější částí tohoto. Tento adresář obsahuje takzvané „tisícové adresáře“: adresáře, z nichž každý má tisíc souborů, které jsou v databázi postupně číslovány. Adresář <code>0</code> obsahuje soubory s comic_id 0–999 a tak dále. Toto je stejný schéma, které Library Genesis používá pro své sbírky beletrie a naučné literatury. Myšlenka je, že každý „tisícový adresář“ se automaticky změní na torrent, jakmile je naplněn. Provozovatel Libgen.li však nikdy pro tuto sbírku nevytvořil torrenty, a tak se tisícové adresáře pravděpodobně staly nepohodlnými a ustoupily „neuspořádaným adresářům“. Tyto jsou <code>/comics0</code> až <code>/comics4</code>. Všechny obsahují jedinečné struktury adresářů, které pravděpodobně dávaly smysl pro sběr souborů, ale nyní nám příliš smysl nedávají. Naštěstí metadata stále přímo odkazují na všechny tyto soubory, takže jejich organizace na disku vlastně nezáleží! Metadata jsou k dispozici ve formě MySQL databáze. Ta může být stažena přímo z webu Libgen.li, ale také ji zpřístupníme v torrentu, spolu s naší vlastní tabulkou se všemi MD5 hashi. <q>Dr. Barbara Gordon se snaží ztratit se v obyčejném světě knihovny…</q> Libgen forky Nejprve trochu pozadí. Možná znáte Library Genesis pro jejich epickou sbírku knih. Méně lidí ví, že dobrovolníci z Library Genesis vytvořili i další projekty, jako je rozsáhlá sbírka časopisů a standardních dokumentů, kompletní záloha Sci-Hub (ve spolupráci se zakladatelkou Sci-Hub, Alexandrou Elbakyan), a skutečně, masivní sbírka komiksů. V určitém okamžiku se různí provozovatelé zrcadel Library Genesis vydali vlastními cestami, což vedlo k současné situaci, kdy existuje několik různých „forků“, které stále nesou název Library Genesis. Fork Libgen.li má jedinečně tuto sbírku komiksů, stejně jako rozsáhlou sbírku časopisů (na které také pracujeme). Sbírka na podporu Tato data uvolňujeme v několika velkých částech. První torrent je z <code>/comics0</code>, který jsme vložili do jednoho obrovského 12TB .tar souboru. To je lepší pro váš pevný disk a torrentový software než nespočet menších souborů. V rámci tohoto vydání pořádáme sbírku. Snažíme se vybrat 20 000 dolarů na pokrytí provozních a smluvních nákladů na tuto sbírku, stejně jako na umožnění probíhajících a budoucích projektů. Máme v plánu některé <em>obrovské</em> projekty. <em>Koho podporuji svým darem?</em> Stručně: zálohujeme veškeré znalosti a kulturu lidstva a zpřístupňujeme je snadno. Veškerý náš kód a data jsou open source, jsme zcela dobrovolnický projekt a dosud jsme zachránili 125TB knih (kromě stávajících torrentů Libgen a Scihub). Nakonec budujeme setrvačník, který umožňuje a motivuje lidi k nalezení, skenování a zálohování všech knih na světě. O našem hlavním plánu napíšeme v budoucím příspěvku. :) Pokud přispějete na 12měsíční členství „Amazing Archivist“ (780 dolarů), můžete <strong>„adoptovat torrent“</strong>, což znamená, že vaše uživatelské jméno nebo zprávu umístíme do názvu jednoho z torrentů! Můžete přispět tím, že navštívíte <a %(wikipedia_annas_archive)s>Annin archiv</a> a kliknete na tlačítko „Přispět“. Také hledáme více dobrovolníků: softwarové inženýry, výzkumníky bezpečnosti, odborníky na anonymní obchod a překladatele. Můžete nás také podpořit poskytováním hostingových služeb. A samozřejmě, prosím, sdílejte naše torrenty! Děkujeme všem, kteří nás již tak štědře podpořili! Opravdu děláte rozdíl. Zde jsou torrenty, které byly dosud vydány (stále zpracováváme zbytek): Všechny torrenty lze nalézt na <a %(wikipedia_annas_archive)s>Annin archiv</a> pod „Datasets“ (neodkazujeme tam přímo, aby odkazy na tento blog nebyly odstraněny z Redditu, Twitteru atd.). Odtud následujte odkaz na web Tor. <a %(news_ycombinator)s>Diskutujte na Hacker News</a> Co bude dál? Spousta torrentů je skvělá pro dlouhodobé uchování, ale ne tolik pro každodenní přístup. Budeme spolupracovat s hostingovými partnery na zpřístupnění všech těchto dat na webu (protože Annin archiv nic přímo nehostuje). Samozřejmě budete moci najít tyto odkazy ke stažení na Annině archivu. Také zveme všechny, aby s těmito daty něco udělali! Pomozte nám je lépe analyzovat, deduplikovat, umístit na IPFS, remixovat, trénovat vaše AI modely s nimi a tak dále. Jsou vaše a nemůžeme se dočkat, co s nimi uděláte. Nakonec, jak bylo řečeno dříve, máme stále v plánu některé obrovské vydání (pokud by <em>někdo</em> mohl <em>náhodou</em> poslat nám výpis z <em>určité</em> databáze ACS4, víte, kde nás najít…), stejně jako budování setrvačníku pro zálohování všech knih na světě. Takže zůstaňte naladěni, teprve začínáme. - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Největší stínová knihovna komiksů je pravděpodobně ta z určitého forku Library Genesis: Libgen.li. Jeden administrátor provozující tuto stránku dokázal shromáždit neuvěřitelnou sbírku komiksů o více než 2 milionech souborů, celkem přes 95TB. Na rozdíl od jiných sbírek Library Genesis však tato nebyla dostupná hromadně prostřednictvím torrentů. Tyto komiksy jste mohli přistupovat pouze jednotlivě přes jeho pomalý osobní server — jedno slabé místo. Až do dneška! V tomto příspěvku vám povíme více o této sbírce a o naší sbírce na podporu další práce. Annin archiv zálohoval největší stínovou knihovnu komiksů na světě (95TB) — můžete pomoci s jejím seedováním Největší stínová knihovna komiksů na světě měla jedno slabé místo... až do dneška. Upozornění: tento blogový příspěvek byl zastaralý. Rozhodli jsme se, že IPFS ještě není připraveno na hlavní čas. Stále budeme odkazovat na soubory na IPFS z Annina archivu, pokud to bude možné, ale nebudeme je již sami hostovat, ani nedoporučujeme ostatním zrcadlit pomocí IPFS. Pokud chcete pomoci uchovat naši sbírku, podívejte se na naši stránku Torrenty. Umístění 5 998 794 knih na IPFS Rozmnožení kopií Zpět k naší původní otázce: jak můžeme tvrdit, že naše sbírky uchováme navždy? Hlavním problémem zde je, že naše sbírka <a %(torrents_stats)s>rychle roste</a>, díky scrapování a open-sourcingu některých masivních sbírek (navíc k úžasné práci, kterou již vykonaly jiné open-data stínové knihovny jako Sci-Hub a Library Genesis). Tento růst dat ztěžuje zrcadlení sbírek po celém světě. Ukládání dat je drahé! Ale jsme optimističtí, zejména při pozorování následujících tří trendů. <a %(annas_archive_stats)s>Celková velikost</a> našich sbírek za posledních několik měsíců, rozdělená podle počtu torrentových seedů. Trendy cen HDD z různých zdrojů (klikněte pro zobrazení studie). <a %(critical_window_chinese)s>Čínská verze 中文版</a>, diskutujte na <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Natrhali jsme nízko visící ovoce Toto přímo vyplývá z našich výše diskutovaných priorit. Preferujeme nejprve pracovat na osvobození velkých sbírek. Nyní, když jsme zajistili některé z největších sbírek na světě, očekáváme, že náš růst bude mnohem pomalejší. Stále existuje dlouhý ocas menších sbírek a nové knihy se skenují nebo publikují každý den, ale tempo bude pravděpodobně mnohem pomalejší. Můžeme se stále zdvojnásobit nebo dokonce ztrojnásobit, ale během delšího časového období. Zlepšení OCR. Priority Vědecký a inženýrský software Fiktivní nebo zábavní verze všech výše uvedených Geografická data (např. mapy, geologické průzkumy) Interní data od korporací nebo vlád (úniky) Měřicí data jako vědecká měření, ekonomická data, firemní zprávy Záznamy metadata obecně (naučné a beletrie; jiných médií, umění, lidí atd.; včetně recenzí) Naučné knihy Naučné časopisy, noviny, příručky Naučné přepisy přednášek, dokumentů, podcastů Organická data jako sekvence DNA, rostlinná semena nebo mikrobiální vzorky Akademické články, časopisy, zprávy Vědecké a inženýrské webové stránky, online diskuse Přepisy právních nebo soudních řízení Jedinečně ohrožená zničením (např. válkou, škrty ve financování, soudními spory nebo politickým pronásledováním) Vzácná Jedinečně opomíjená Proč nám tolik záleží na článcích a knihách? Odložme stranou naši základní víru v uchovávání obecně — možná o tom napíšeme další příspěvek. Tak proč konkrétně články a knihy? Odpověď je jednoduchá: <strong>informační hustota</strong>. Na megabajt úložiště ukládá psaný text nejvíce informací ze všech médií. Zatímco nám záleží na obou, vědění i kultuře, více nám záleží na tom prvním. Celkově nacházíme hierarchii informační hustoty a důležitosti uchovávání, která vypadá zhruba takto: Pořadí v tomto seznamu je poněkud libovolné — několik položek je na stejné úrovni nebo existují neshody v našem týmu — a pravděpodobně zapomínáme na některé důležité kategorie. Ale zhruba takto upřednostňujeme. Některé z těchto položek jsou příliš odlišné od ostatních, abychom se o ně starali (nebo jsou již zajištěny jinými institucemi), jako jsou organická data nebo geografická data. Ale většina položek v tomto seznamu je pro nás skutečně důležitá. Dalším velkým faktorem v našem upřednostňování je, jak moc je určité dílo ohroženo. Raději se zaměřujeme na díla, která jsou: Nakonec nám záleží na rozsahu. Máme omezený čas a peníze, takže bychom raději strávili měsíc záchranou 10 000 knih než 1 000 knih — pokud jsou přibližně stejně cenné a ohrožené. <em><q>Ztracené nelze obnovit; ale zachraňme, co zůstává: ne trezory a zámky, které je chrání před veřejným pohledem a užitím, čímž je odsuzujeme k zániku času, ale takovým rozmnožením kopií, které je postaví mimo dosah náhody.</q></em><br>— Thomas Jefferson, 1791 Stínové knihovny Kód může být open source na Githubu, ale Github jako celek nelze snadno zrcadlit a tím pádem uchovat (i když v tomto konkrétním případě existují dostatečně distribuované kopie většiny repozitářů kódu) Záznamy metadata lze volně prohlížet na webu Worldcat, ale nelze je hromadně stahovat (dokud jsme je <a %(worldcat_scrape)s>nescrapovali</a>) Reddit je zdarma k použití, ale nedávno zavedl přísná opatření proti scrapování, v důsledku hladového tréninku LLM (o tom více později) Existuje mnoho organizací, které mají podobné poslání a podobné priority. Skutečně, existují knihovny, archivy, laboratoře, muzea a další instituce pověřené uchováváním tohoto druhu. Mnohé z nich jsou dobře financovány vládami, jednotlivci nebo korporacemi. Ale mají jedno obrovské slepé místo: právní systém. Zde spočívá jedinečná role stínových knihoven a důvod, proč existuje Annin archiv. Můžeme dělat věci, které jiné instituce nemohou. Není to (často) o tom, že bychom mohli archivovat materiály, které je jinde nezákonné uchovávat. Ne, v mnoha místech je legální vytvořit archiv s jakýmikoli knihami, články, časopisy a podobně. Ale co často chybí právním archivům, je <strong>redundance a dlouhověkost</strong>. Existují knihy, z nichž existuje pouze jedna kopie v nějaké fyzické knihovně někde. Existují záznamy metadata, které střeží jediná korporace. Existují noviny, které jsou uchovány pouze na mikrofilmu v jediném archivu. Knihovny mohou přijít o financování, korporace mohou zkrachovat, archivy mohou být bombardovány a spáleny na popel. To není hypotetické — to se děje neustále. To, co můžeme v Annině archivu jedinečně udělat, je ukládat mnoho kopií děl ve velkém měřítku. Můžeme sbírat články, knihy, časopisy a další a distribuovat je hromadně. V současné době to děláme prostřednictvím torrentů, ale přesné technologie nejsou důležité a budou se časem měnit. Důležitá je distribuce mnoha kopií po celém světě. Tento citát starý přes 200 let je stále pravdivý: Krátká poznámka o veřejné doméně. Vzhledem k tomu, že se Annin archiv jedinečně zaměřuje na činnosti, které jsou na mnoha místech světa nelegální, nezabýváme se široce dostupnými sbírkami, jako jsou knihy ve veřejné doméně. Právní subjekty se o to často již dobře starají. Existují však úvahy, které nás někdy vedou k práci na veřejně dostupných sbírkách: - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Náklady na úložiště nadále exponenciálně klesají 3. Zlepšení hustoty informací V současné době ukládáme knihy v surových formátech, ve kterých nám byly poskytnuty. Jistě, jsou komprimované, ale často se jedná o velké skeny nebo fotografie stránek. Doposud byly jedinými možnostmi, jak zmenšit celkovou velikost naší sbírky, agresivnější komprese nebo deduplikace. Nicméně, pro dosažení dostatečně významných úspor jsou obě metody příliš ztrátové pro naše potřeby. Silná komprese fotografií může způsobit, že text je sotva čitelný. A deduplikace vyžaduje vysokou jistotu, že knihy jsou přesně stejné, což je často příliš nepřesné, zejména pokud je obsah stejný, ale skeny byly pořízeny při různých příležitostech. Vždy existovala třetí možnost, ale její kvalita byla tak mizerná, že jsme ji nikdy nezvažovali: <strong>OCR, neboli optické rozpoznávání znaků</strong>. Jedná se o proces převodu fotografií na prostý text pomocí AI, která detekuje znaky na fotografiích. Nástroje pro to existují již dlouho a byly docela slušné, ale „docela slušné“ nestačí pro účely uchovávání. Nicméně, nedávné multimodální modely hlubokého učení dosáhly extrémně rychlého pokroku, i když stále za vysoké náklady. Očekáváme, že přesnost i náklady se v nadcházejících letech dramaticky zlepší, až do bodu, kdy bude realistické aplikovat je na celou naši knihovnu. Když k tomu dojde, pravděpodobně stále zachováme původní soubory, ale navíc bychom mohli mít mnohem menší verzi naší knihovny, kterou většina lidí bude chtít zrcadlit. Klíčové je, že samotný surový text se komprimuje ještě lépe a je mnohem snazší ho deduplikovat, což nám přináší ještě větší úspory. Celkově není nereálné očekávat alespoň 5-10násobné zmenšení celkové velikosti souborů, možná i více. I při konzervativním 5násobném zmenšení bychom se dívali na <strong>1 000–3 000 dolarů za 10 let, i kdyby se naše knihovna ztrojnásobila</strong>. V době psaní tohoto textu jsou <a %(diskprices)s>ceny disků</a> za TB kolem 12 dolarů za nové disky, 8 dolarů za použité disky a 4 dolary za pásku. Pokud budeme konzervativní a podíváme se pouze na nové disky, znamená to, že uložení petabajtu stojí asi 12 000 dolarů. Pokud předpokládáme, že naše knihovna se ztrojnásobí z 900 TB na 2,7 PB, znamenalo by to 32 400 dolarů na zrcadlení celé naší knihovny. Přidáním elektřiny, nákladů na další hardware a tak dále, zaokrouhlujme to na 40 000 dolarů. Nebo s páskou spíše 15 000–20 000 dolarů. Na jedné straně <strong>15 000–40 000 dolarů za souhrn veškerého lidského vědění je výhodná cena</strong>. Na druhé straně je trochu strmé očekávat tuny plných kopií, zejména pokud bychom také chtěli, aby tito lidé pokračovali v seedování svých torrentů pro prospěch ostatních. To je dnes. Ale pokrok kráčí vpřed: Náklady na pevné disky za TB byly zhruba sníženy na třetinu za posledních 10 let a pravděpodobně budou nadále klesat podobným tempem. Zdá se, že páska je na podobné trajektorii. Ceny SSD klesají ještě rychleji a mohly by do konce desetiletí převzít ceny HDD. Pokud to vydrží, pak za 10 let bychom se mohli dívat na pouhých 5 000–13 000 dolarů na zrcadlení celé naší sbírky (1/3), nebo ještě méně, pokud porosteme méně. I když je to stále hodně peněz, bude to dosažitelné pro mnoho lidí. A mohlo by to být ještě lepší díky dalšímu bodu… V Annině archivu se nás často ptají, jak můžeme tvrdit, že uchováváme naše sbírky navždy, když celková velikost již dosahuje téměř 1 Petabytu (1000 TB) a stále roste. V tomto článku se podíváme na naši filozofii a zjistíme, proč je příští desetiletí kritické pro naši misi uchování lidského vědění a kultury. Kritické okno Pokud jsou tyto prognózy přesné, <strong>stačí nám počkat pár let</strong>, než bude naše celá sbírka široce zrcadlena. Takže, slovy Thomase Jeffersona, „umístěna mimo dosah náhody“. Bohužel, nástup LLM a jejich datově náročného tréninku přiměl mnoho držitelů autorských práv k obraně. Ještě více než dříve. Mnoho webových stránek ztěžuje scraping a archivaci, soudní spory létají kolem a mezitím jsou fyzické knihovny a archivy nadále zanedbávány. Můžeme očekávat, že tyto trendy se budou nadále zhoršovat a mnoho děl bude ztraceno dávno předtím, než vstoupí do veřejné domény. <strong>Jsme na prahu revoluce v uchovávání, ale <q>ztracené nelze obnovit.</q></strong> Máme kritické okno asi 5-10 let, během kterého je stále poměrně drahé provozovat stínovou knihovnu a vytvářet mnoho zrcadlení po celém světě, a během kterého přístup ještě nebyl zcela uzavřen. Pokud dokážeme překlenout toto okno, pak skutečně uchováme lidské znalosti a kulturu navždy. Neměli bychom nechat tento čas promarnit. Neměli bychom nechat toto kritické okno zavřít se před námi. Pojďme na to. Kritické okno stínových knihoven Jak můžeme tvrdit, že uchováváme naše sbírky navždy, když již dosahují 1 PB? Sbírka Několik dalších informací o sbírce. <a %(duxiu)s>Duxiu</a> je obrovská databáze skenovaných knih, vytvořená <a %(chaoxing)s>SuperStar Digital Library Group</a>. Většinou se jedná o akademické knihy, které byly skenovány, aby byly digitálně dostupné univerzitám a knihovnám. Pro naše anglicky mluvící publikum mají <a %(library_princeton)s>Princeton</a> a <a %(guides_lib_uw)s>University of Washington</a> dobré přehledy. Existuje také vynikající článek, který poskytuje více informací: <a %(doi)s>„Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (vyhledejte v Annině archivu). Knihy z Duxiu byly dlouho pirátovány na čínském internetu. Obvykle jsou prodávány za méně než dolar prodejci. Obvykle jsou distribuovány pomocí čínského ekvivalentu Google Drive, který byl často hacknut, aby umožnil více úložného prostoru. Některé technické detaily lze nalézt <a %(github_duty_machine)s>zde</a> a <a %(github_821_github_io)s>zde</a>. Ačkoli byly knihy poloveřejně distribuovány, je poměrně obtížné je získat ve velkém. Měli jsme to vysoko na našem seznamu úkolů a vyčlenili jsme na to několik měsíců plné práce. Nicméně, nedávno se nám ozval neuvěřitelný, úžasný a talentovaný dobrovolník, který nám řekl, že už tuto práci udělal — za velké náklady. Sdílel s námi celou sbírku, aniž by očekával cokoliv na oplátku, kromě záruky dlouhodobého uchování. Opravdu pozoruhodné. Souhlasil s tím, že požádá o pomoc tímto způsobem, aby byla sbírka OCRována. Sbírka obsahuje 7 543 702 souborů. To je více než Library Genesis non-fiction (asi 5,3 milionu). Celková velikost souborů je asi 359TB (326TiB) v současné podobě. Jsme otevřeni dalším návrhům a nápadům. Stačí nás kontaktovat. Podívejte se na Annin archiv pro více informací o našich sbírkách, úsilí o uchování a jak můžete pomoci. Děkujeme! Ukázkové stránky Abyste nám dokázali, že máte dobrý pipeline, zde jsou některé ukázkové stránky, na kterých můžete začít, z knihy o supravodičích. Váš pipeline by měl správně zpracovat matematiku, tabulky, grafy, poznámky pod čarou a podobně. Pošlete své zpracované stránky na náš e-mail. Pokud budou vypadat dobře, pošleme vám další soukromě a očekáváme, že budete schopni rychle spustit svůj pipeline i na těchto. Jakmile budeme spokojeni, můžeme uzavřít dohodu. - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Čínská verze 中文版</a>, <a %(news_ycombinator)s>Diskutovat na Hacker News</a> Toto je krátký blogový příspěvek. Hledáme nějakou společnost nebo instituci, která by nám pomohla s OCR a extrakcí textu pro obrovskou sbírku, kterou jsme získali, výměnou za exkluzivní předčasný přístup. Po uplynutí embarga samozřejmě uvolníme celou sbírku. Vysoce kvalitní akademické texty jsou nesmírně užitečné pro trénink LLM. I když je naše sbírka čínská, měla by být užitečná i pro trénink anglických LLM: modely se zdají kódovat koncepty a znalosti bez ohledu na zdrojový jazyk. Pro to je třeba text extrahovat ze skenů. Co z toho má Annin archiv? Fulltextové vyhledávání knih pro své uživatele. Protože naše cíle se shodují s cíli vývojářů LLM, hledáme spolupracovníka. Jsme ochotni vám poskytnout <strong>exkluzivní předčasný přístup k této sbírce ve velkém na 1 rok</strong>, pokud dokážete provést správné OCR a extrakci textu. Pokud jste ochotni s námi sdílet celý kód vašeho pipeline, byli bychom ochotni sbírku embargovat na delší dobu. Exkluzivní přístup pro společnosti LLM k největší sbírce čínských naučných knih na světě <em><strong>Ve zkratce:</strong> Annin archiv získal unikátní sbírku 7,5 milionu / 350TB čínských naučných knih — větší než Library Genesis. Jsme ochotni poskytnout společnosti LLM exkluzivní přístup výměnou za vysoce kvalitní OCR a extrakci textu.</em> Systémová architektura Takže řekněme, že jste našli nějaké společnosti, které jsou ochotny hostovat váš web, aniž by vás vypnuly — nazvěme je „poskytovatelé milující svobodu“ 😄. Rychle zjistíte, že hostování všeho u nich je poměrně drahé, takže možná budete chtít najít nějaké „levné poskytovatele“ a skutečné hostování provádět tam, přičemž budete procházet přes poskytovatele milující svobodu. Pokud to uděláte správně, levní poskytovatelé nikdy nebudou vědět, co hostujete, a nikdy neobdrží žádné stížnosti. U všech těchto poskytovatelů existuje riziko, že vás stejně vypnou, takže potřebujete také redundanci. Potřebujeme to na všech úrovních naší sady. Jedna poněkud svobodomyslná společnost, která se postavila do zajímavé pozice, je Cloudflare. Tvrdili, že nejsou poskytovatelem hostingu, ale utilitou, jako je ISP. Proto nepodléhají DMCA nebo jiným žádostem o odstranění a přeposílají jakékoli žádosti vašemu skutečnému poskytovateli hostingu. Dokonce šli tak daleko, že šli k soudu, aby tuto strukturu ochránili. Můžeme je tedy použít jako další vrstvu ukládání do mezipaměti a ochrany. Cloudflare nepřijímá anonymní platby, takže můžeme použít pouze jejich bezplatný plán. To znamená, že nemůžeme používat jejich funkce vyvažování zátěže nebo přepínání při selhání. Proto jsme to <a %(annas_archive_l255)s>implementovali sami</a> na úrovni domény. Při načítání stránky prohlížeč zkontroluje, zda je aktuální doména stále dostupná, a pokud ne, přepíše všechny URL na jinou doménu. Protože Cloudflare ukládá mnoho stránek do mezipaměti, znamená to, že uživatel může přistát na naší hlavní doméně, i když je proxy server mimo provoz, a poté při dalším kliknutí být přesměrován na jinou doménu. Stále se také musíme zabývat běžnými provozními záležitostmi, jako je monitorování zdraví serveru, zaznamenávání chyb na backendu a frontendu a podobně. Naše architektura přepínání při selhání umožňuje větší robustnost i v této oblasti, například spuštěním zcela jiné sady serverů na jedné z domén. Můžeme dokonce spustit starší verze kódu a datasets na této samostatné doméně, pro případ, že by kritická chyba v hlavní verzi zůstala bez povšimnutí. Můžeme se také pojistit proti tomu, že se Cloudflare obrátí proti nám, tím, že ho odstraníme z jedné z domén, například z této samostatné domény. Různé permutace těchto nápadů jsou možné. Závěr Byla to zajímavá zkušenost naučit se, jak nastavit robustní a odolný vyhledávač stínové knihovny. Existuje spousta dalších podrobností, které se podělíme v pozdějších příspěvcích, takže dejte vědět, o čem byste se chtěli dozvědět více! Jako vždy hledáme dary na podporu této práce, takže se určitě podívejte na stránku Darovat na Annině archivu. Hledáme také jiné typy podpory, jako jsou granty, dlouhodobí sponzoři, poskytovatelé plateb s vysokým rizikem, možná i (vkusné!) reklamy. A pokud chcete přispět svým časem a dovednostmi, vždy hledáme vývojáře, překladatele a podobně. Děkujeme za váš zájem a podporu. Inovační tokeny Začněme naší technologickou sadou. Je záměrně nudná. Používáme Flask, MariaDB a ElasticSearch. To je doslova vše. Vyhledávání je z velké části vyřešený problém a nemáme v úmyslu ho znovu objevovat. Kromě toho musíme utratit naše <a %(mcfunley)s>inovační tokeny</a> na něco jiného: abychom nebyli odstraněni úřady. Jak legální nebo nelegální je vlastně Annin archiv? To závisí především na právní jurisdikci. Většina zemí věří v nějakou formu autorského práva, což znamená, že lidem nebo společnostem je přidělen exkluzivní monopol na určité typy děl po určitou dobu. Mimochodem, v Annině archivu věříme, že i když existují určité výhody, celkově je autorské právo pro společnost negativní — ale to je příběh na jindy. Tento exkluzivní monopol na určitá díla znamená, že je nelegální, aby kdokoli mimo tento monopol přímo distribuoval tato díla — včetně nás. Ale Annin archiv je vyhledávač, který tato díla přímo nedistribuuje (alespoň ne na našem clearnet webu), takže bychom měli být v pořádku, že? Ne tak docela. V mnoha jurisdikcích je nelegální nejen distribuovat díla chráněná autorským právem, ale také odkazovat na místa, která to dělají. Klasickým příkladem je americký zákon DMCA. To je nejpřísnější konec spektra. Na druhém konci spektra by teoreticky mohly existovat země bez jakýchkoli autorských zákonů, ale ty ve skutečnosti neexistují. Prakticky každá země má nějakou formu autorského práva v zákonech. Vymáhání je jiný příběh. Existuje mnoho zemí s vládami, které se nestarají o vymáhání autorského práva. Existují také země mezi těmito dvěma extrémy, které zakazují distribuci děl chráněných autorským právem, ale nezakazují odkazování na taková díla. Dalším faktorem je úroveň společnosti. Pokud společnost působí v jurisdikci, která se nestará o autorská práva, ale samotná společnost není ochotna podstoupit žádné riziko, pak mohou váš web vypnout, jakmile si na něj někdo stěžuje. Nakonec je velkým faktorem platby. Protože musíme zůstat anonymní, nemůžeme používat tradiční platební metody. To nás nechává s kryptoměnami, a pouze malá část společností je podporuje (existují virtuální debetní karty placené kryptoměnami, ale ty často nejsou přijímány). - Anna a tým (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Provozuji <a %(wikipedia_annas_archive)s>Annin archiv</a>, největší open-source neziskový vyhledávač na světě pro <a %(wikipedia_shadow_library)s>stínové knihovny</a>, jako je Sci-Hub, Library Genesis a Z-Library. Naším cílem je zpřístupnit znalosti a kulturu a nakonec vybudovat komunitu lidí, kteří společně archivují a uchovávají <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>všechny knihy na světě</a>. V tomto článku ukážu, jak provozujeme tuto webovou stránku a jedinečné výzvy, které přináší provozování webové stránky s pochybným právním statusem, protože neexistuje žádný „AWS pro stínové charity“. <em>Podívejte se také na sesterský článek <a %(blog_how_to_become_a_pirate_archivist)s>Jak se stát pirátským archivářem</a>.</em> Jak provozovat stínovou knihovnu: operace v Annině archivu Neexistuje žádný <q>AWS pro stínové charity,</q> tak jak provozujeme Annin archiv? Nástroje Aplikační server: Flask, MariaDB, ElasticSearch, Docker. Vývoj: Gitlab, Weblate, Zulip. Správa serveru: Ansible, Checkmk, UFW. Statické hostování na Onion: Tor, Nginx. Proxy server: Varnish. Podívejme se, jaké nástroje používáme k dosažení všeho tohoto. To se velmi vyvíjí, jak narážíme na nové problémy a nacházíme nová řešení. Existují některá rozhodnutí, u kterých jsme se několikrát vrátili zpět. Jedním z nich je komunikace mezi servery: dříve jsme k tomu používali Wireguard, ale zjistili jsme, že občas přestane přenášet data, nebo přenáší data pouze jedním směrem. To se stalo u několika různých nastavení Wireguard, které jsme vyzkoušeli, jako například <a %(github_costela_wesher)s>wesher</a> a <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Také jsme zkoušeli tunelovat porty přes SSH, pomocí autossh a sshuttle, ale narazili jsme na <a %(github_sshuttle)s>problémy tam</a> (i když mi stále není jasné, zda autossh trpí problémy TCP-over-TCP nebo ne — prostě mi to připadá jako nešikovné řešení, ale možná je to vlastně v pořádku?). Místo toho jsme se vrátili k přímým spojením mezi servery, skrývajíc, že server běží na levných poskytovatelích pomocí IP-filtrace s UFW. To má nevýhodu, že Docker nefunguje dobře s UFW, pokud nepoužijete <code>network_mode: "host"</code>. Všechny tyto kroky jsou trochu náchylnější k chybám, protože vystavíte svůj server internetu jen s malou chybnou konfigurací. Možná bychom se měli vrátit k autossh — zpětná vazba by zde byla velmi vítaná. Také jsme se několikrát vrátili k otázce Varnish vs. Nginx. Momentálně preferujeme Varnish, ale má své zvláštnosti a hrubé hrany. Totéž platí pro Checkmk: nemilujeme ho, ale zatím funguje. Weblate bylo v pořádku, ale ne úžasné — někdy se obávám, že ztratí moje data, kdykoli se pokusím synchronizovat s naším git repozitářem. Flask byl celkově dobrý, ale má některé podivné zvláštnosti, které stály hodně času na ladění, jako je konfigurace vlastních domén nebo problémy s integrací SqlAlchemy. Doposud byly ostatní nástroje skvělé: nemáme žádné vážné stížnosti na MariaDB, ElasticSearch, Gitlab, Zulip, Docker a Tor. Všechny tyto nástroje měly nějaké problémy, ale nic příliš vážného nebo časově náročného. Komunita První výzva může být překvapivá. Není to technický problém ani právní problém. Je to psychologický problém: dělat tuto práci ve stínu může být neuvěřitelně osamělé. V závislosti na tom, co plánujete dělat, a vašem modelu hrozeb, možná budete muset být velmi opatrní. Na jednom konci spektra máme lidi jako Alexandra Elbakyan*, zakladatelku Sci-Hub, která je velmi otevřená ohledně svých aktivit. Ale je ve vysokém riziku zatčení, pokud by v tuto chvíli navštívila západní zemi, a mohla by čelit desetiletím vězení. Je to riziko, které byste byli ochotni podstoupit? My jsme na druhém konci spektra; jsme velmi opatrní, abychom nezanechali žádné stopy, a máme silnou operační bezpečnost. * Jak bylo zmíněno na HN uživatelem "ynno", Alexandra zpočátku nechtěla být známá: "Její servery byly nastaveny tak, aby vydávaly podrobné chybové zprávy z PHP, včetně úplné cesty k chybovému zdrojovému souboru, který byl pod adresářem /home/<USER>" Takže používejte náhodná uživatelská jména na počítačích, které používáte pro tyto věci, pro případ, že byste něco špatně nakonfigurovali. Tato tajemnost však přichází s psychologickými náklady. Většina lidí miluje být uznávána za práci, kterou dělají, a přesto za to nemůžete v reálném životě získat žádné uznání. I jednoduché věci mohou být náročné, jako když se vás přátelé ptají, co jste dělali (v určitém okamžiku "hrát si s mým NAS / homelab" se omrzí). Proto je tak důležité najít nějakou komunitu. Můžete se vzdát části operační bezpečnosti tím, že se svěříte některým velmi blízkým přátelům, o kterých víte, že jim můžete hluboce důvěřovat. I tehdy buďte opatrní, abyste nic nedávali písemně, pro případ, že by museli předat své e-maily úřadům, nebo pokud by jejich zařízení byla kompromitována jiným způsobem. Ještě lepší je najít nějaké kolegy piráty. Pokud vaši blízcí přátelé mají zájem se k vám připojit, skvělé! Jinak byste mohli najít ostatní online. Bohužel je to stále okrajová komunita. Zatím jsme našli jen hrstku dalších, kteří jsou v této oblasti aktivní. Dobré výchozí body se zdají být fóra Library Genesis a r/DataHoarder. Tým Archive Team má také podobně smýšlející jednotlivce, i když operují v rámci zákona (i když v některých šedých oblastech zákona). Tradiční "warez" a pirátské scény mají také lidi, kteří přemýšlejí podobným způsobem. Jsme otevřeni nápadům, jak podpořit komunitu a prozkoumat nové myšlenky. Neváhejte nám poslat zprávu na Twitteru nebo Redditu. Možná bychom mohli uspořádat nějaké fórum nebo chatovou skupinu. Jednou z výzev je, že to může být snadno cenzurováno při používání běžných platforem, takže bychom to museli hostovat sami. Existuje také kompromis mezi tím, zda mít tyto diskuse zcela veřejné (větší potenciální zapojení) nebo je udělat soukromé (neupozornit potenciální „cíle“, že je hodláme zrcadlit). Budeme o tom muset přemýšlet. Dejte nám vědět, pokud máte o to zájem! Závěr Doufáme, že to bude užitečné pro nově začínající pirátské archiváře. Jsme nadšeni, že vás můžeme přivítat v tomto světě, takže neváhejte se ozvat. Pojďme zachovat co nejvíce světového poznání a kultury, jak jen můžeme, a zrcadlit to široko daleko. Projekty 4. Výběr dat Často můžete použít metadata k určení rozumné podmnožiny dat ke stažení. I když nakonec chcete stáhnout všechna data, může být užitečné upřednostnit nejdůležitější položky, pokud byste byli odhaleni a obrana byla vylepšena, nebo protože byste potřebovali koupit více disků, nebo jednoduše proto, že se ve vašem životě objeví něco jiného, než budete moci stáhnout vše. Například sbírka může mít více vydání stejného základního zdroje (jako je kniha nebo film), kde jedno je označeno jako nejlepší kvalita. Uložení těchto vydání jako první by dávalo velký smysl. Možná budete chtít nakonec uložit všechna vydání, protože v některých případech mohou být metadata nesprávně označena, nebo mohou existovat neznámé kompromisy mezi vydáními (například "nejlepší vydání" může být nejlepší ve většině ohledů, ale horší v jiných, jako je film s vyšším rozlišením, ale bez titulků). Můžete také prohledávat svou databázi metadat a najít zajímavé věci. Jaký je největší soubor, který je hostován, a proč je tak velký? Jaký je nejmenší soubor? Existují zajímavé nebo neočekávané vzory, pokud jde o určité kategorie, jazyky a podobně? Existují duplicitní nebo velmi podobné tituly? Existují vzory, kdy byla data přidána, například jeden den, kdy bylo přidáno mnoho souborů najednou? Často se můžete hodně naučit tím, že se na dataset podíváte z různých úhlů. V našem případě jsme deduplikovali knihy ze Z-Library proti md5 hashům v Library Genesis, čímž jsme ušetřili spoustu času na stahování a místo na disku. To je ale docela unikátní situace. Ve většině případů neexistují komplexní databáze, které soubory jsou již řádně zachovány ostatními piráty. To samo o sobě je obrovská příležitost pro někoho tam venku. Bylo by skvělé mít pravidelně aktualizovaný přehled věcí, jako je hudba a filmy, které jsou již široce sdíleny na torrentových webech, a proto mají nižší prioritu pro zahrnutí do pirátských zrcadel. 6. Distribuce Máte data, čímž získáváte vlastnictví prvního pirátského zrcadla vašeho cíle (s největší pravděpodobností). V mnoha ohledech je nejtěžší část za vámi, ale nejrizikovější část je stále před vámi. Koneckonců, dosud jste byli nenápadní; létali jste pod radarem. Vše, co jste museli udělat, bylo používat dobré VPN po celou dobu, nevyplňovat své osobní údaje v žádných formulářích (samozřejmě) a možná používat speciální relaci prohlížeče (nebo dokonce jiný počítač). Nyní musíte data distribuovat. V našem případě jsme nejprve chtěli přispět knihami zpět do Library Genesis, ale pak jsme rychle objevili obtíže s tím spojené (třídění fikce vs. non-fikce). Takže jsme se rozhodli pro distribuci pomocí torrentů ve stylu Library Genesis. Pokud máte příležitost přispět do existujícího projektu, mohlo by vám to ušetřit spoustu času. Nicméně, v současné době není mnoho dobře organizovaných pirátských zrcadel. Takže řekněme, že se rozhodnete distribuovat torrenty sami. Snažte se udržet tyto soubory malé, aby bylo snadné je zrcadlit na jiných webových stránkách. Poté budete muset seedovat torrenty sami, přičemž zůstanete anonymní. Můžete použít VPN (s přesměrováním portů nebo bez něj), nebo zaplatit za Seedbox pomocí promíchaných Bitcoinů. Pokud nevíte, co některé z těchto termínů znamenají, budete mít hodně čtení, protože je důležité, abyste pochopili rizika a kompromisy zde. Můžete hostovat samotné torrentové soubory na existujících torrentových webových stránkách. V našem případě jsme se rozhodli skutečně hostovat webovou stránku, protože jsme také chtěli šířit naši filozofii jasným způsobem. Můžete to udělat sami podobným způsobem (používáme Njalla pro naše domény a hosting, placené promíchanými Bitcoiny), ale také nás neváhejte kontaktovat, abychom mohli hostovat vaše torrenty. Snažíme se časem vybudovat komplexní index pirátských zrcadlení, pokud se tato myšlenka uchytí. Co se týče výběru VPN, bylo o tom již hodně napsáno, takže jen zopakujeme obecnou radu vybírat podle reputace. Skutečné soudně ověřené zásady bez logů s dlouhou historií ochrany soukromí jsou podle nás nejnižší rizikovou možností. Upozorňujeme, že i když uděláte vše správně, nikdy se nedostanete na nulové riziko. Například při seedování vašich torrentů může vysoce motivovaný státní aktér pravděpodobně sledovat příchozí a odchozí datové toky pro VPN servery a odvodit, kdo jste. Nebo se můžete jednoduše nějak splést. Pravděpodobně jsme už udělali chybu a uděláme ji znovu. Naštěstí státy se o pirátství <em>tolik</em> nestarají. Jedním z rozhodnutí, které je třeba učinit pro každý projekt, je, zda ho publikovat pod stejnou identitou jako dříve, nebo ne. Pokud budete používat stejné jméno, pak chyby v operační bezpečnosti z dřívějších projektů by se mohly vrátit a ublížit vám. Ale publikování pod různými jmény znamená, že si nebudujete dlouhodobou reputaci. Rozhodli jsme se mít silnou operační bezpečnost od začátku, abychom mohli používat stejnou identitu, ale nebudeme váhat publikovat pod jiným jménem, pokud uděláme chybu nebo pokud to okolnosti vyžadují. Dostat slovo ven může být složité. Jak jsme řekli, toto je stále okrajová komunita. Původně jsme zveřejnili na Redditu, ale skutečně jsme získali pozornost na Hacker News. Prozatím doporučujeme zveřejnit to na několika místech a sledovat, co se stane. A znovu, kontaktujte nás. Rádi bychom šířili slovo o dalších pirátských archivních snahách. 1. Výběr domény / filozofie Není nedostatek znalostí a kulturního dědictví, které je třeba zachránit, což může být ohromující. Proto je často užitečné si na chvíli sednout a přemýšlet o tom, jaký může být váš příspěvek. Každý o tom přemýšlí jinak, ale zde je několik otázek, které byste si mohli položit: V našem případě nám obzvláště záleželo na dlouhodobém uchování vědy. Věděli jsme o Library Genesis a o tom, jak byla mnohokrát plně zrcadlena pomocí torrentů. Ten nápad se nám líbil. Pak jednoho dne se jeden z nás pokusil najít nějaké vědecké učebnice na Library Genesis, ale nemohl je najít, což zpochybnilo, jak kompletní to opravdu bylo. Poté jsme tyto učebnice hledali online a našli je na jiných místech, což zaselo semínko pro náš projekt. Ještě předtím, než jsme věděli o Z-Library, jsme měli myšlenku nesnažit se sbírat všechny ty knihy ručně, ale zaměřit se na zrcadlení existujících sbírek a přispívat je zpět do Library Genesis. Jaké dovednosti máte, které můžete využít ve svůj prospěch? Například pokud jste odborník na online bezpečnost, můžete najít způsoby, jak překonat blokování IP pro bezpečné cíle. Pokud jste skvělí v organizování komunit, pak možná můžete shromáždit nějaké lidi kolem cíle. Je však užitečné znát nějaké programování, i kdyby jen pro udržení dobré provozní bezpečnosti během tohoto procesu. Na co by bylo výhodné se zaměřit? Pokud hodláte strávit X hodin pirátským archivováním, jak můžete získat největší „hodnotu za své peníze“? Jaké jsou jedinečné způsoby, jak o tom přemýšlíte? Možná máte nějaké zajímavé nápady nebo přístupy, které ostatní mohli přehlédnout. Kolik času na to máte? Naše rada by byla začít v malém a dělat větší projekty, jakmile se do toho dostanete, ale může to být vše pohlcující. Proč vás to zajímá? Co vás nadchne? Pokud se nám podaří získat skupinu lidí, kteří archivují věci, na kterých jim konkrétně záleží, pokrylo by to hodně! Budete vědět mnohem více než průměrný člověk o své vášni, jako co je důležitá data k uložení, jaké jsou nejlepší sbírky a online komunity a tak dále. 3. Stahování metadat Datum přidání/změny: abyste se mohli později vrátit a stáhnout soubory, které jste dříve nestáhli (i když k tomu často můžete použít také ID nebo hash). Hash (md5, sha1): pro potvrzení, že jste soubor správně stáhli. ID: může to být nějaké interní ID, ale ID jako ISBN nebo DOI jsou také užitečné. Název souboru / umístění Popis, kategorie, štítky, autoři, jazyk atd. Velikost: pro výpočet, kolik místa na disku potřebujete. Pojďme se trochu více ponořit do technických detailů. Pro skutečné stahování metadat z webových stránek jsme to udrželi poměrně jednoduché. Používáme Python skripty, někdy curl, a MySQL databázi pro ukládání výsledků. Nepoužili jsme žádný sofistikovaný software pro stahování, který by mohl mapovat složité webové stránky, protože zatím jsme potřebovali stahovat pouze jeden nebo dva druhy stránek pouhým procházením id a parsováním HTML. Pokud nejsou snadno procházené stránky, pak možná budete potřebovat správný crawler, který se pokusí najít všechny stránky. Než začnete stahovat celý web, zkuste to chvíli dělat ručně. Projděte si sami několik desítek stránek, abyste získali představu o tom, jak to funguje. Někdy se tímto způsobem již setkáte s blokováním IP nebo jiným zajímavým chováním. Totéž platí pro stahování dat: než se příliš ponoříte do tohoto cíle, ujistěte se, že můžete skutečně efektivně stahovat jeho data. Pro obcházení omezení můžete vyzkoušet několik věcí. Existují nějaké jiné IP adresy nebo servery, které hostují stejná data, ale nemají stejná omezení? Existují nějaké API koncové body, které nemají omezení, zatímco jiné ano? Při jaké rychlosti stahování je vaše IP blokována a na jak dlouho? Nebo nejste blokováni, ale zpomaleni? Co když si vytvoříte uživatelský účet, jak se pak věci změní? Můžete použít HTTP/2 k udržení otevřených spojení a zvyšuje to rychlost, jakou můžete požadovat stránky? Existují stránky, které uvádějí více souborů najednou, a jsou tam uvedené informace dostatečné? Věci, které pravděpodobně chcete uložit, zahrnují: Obvykle to děláme ve dvou fázích. Nejprve stáhneme surové HTML soubory, obvykle přímo do MySQL (abychom se vyhnuli spoustě malých souborů, o čemž si povíme více níže). Poté, v samostatném kroku, projdeme tyto HTML soubory a zpracujeme je do skutečných MySQL tabulek. Tímto způsobem nemusíte znovu stahovat vše od začátku, pokud objevíte chybu ve svém kódu pro zpracování, protože můžete jednoduše znovu zpracovat HTML soubory s novým kódem. Je také často snazší paralelizovat krok zpracování, čímž ušetříte nějaký čas (a můžete psát kód pro zpracování, zatímco stahování běží, místo toho, abyste museli psát oba kroky najednou). Nakonec si všimněte, že pro některé cíle je metadata scraping vše, co existuje. Existují obrovské sbírky metadat, které nejsou řádně zachovány. Název Výběr domény / filozofie: Na co se chcete zhruba zaměřit a proč? Jaké jsou vaše jedinečné vášně, dovednosti a okolnosti, které můžete využít ve svůj prospěch? Výběr cíle: Kterou konkrétní sbírku budete zrcadlit? Scraping metadat: Katalogizace informací o souborech, aniž byste skutečně stahovali samotné (často mnohem větší) soubory. Výběr dat: Na základě metadat zúžení, která data jsou nyní nejrelevantnější k archivaci. Může to být všechno, ale často existuje rozumný způsob, jak ušetřit místo a šířku pásma. Scraping dat: Skutečné získání dat. Distribuce: Zabalení do torrentů, oznámení někde, získání lidí, aby to šířili. 5. Scraping dat Nyní jste připraveni skutečně stáhnout data ve velkém. Jak bylo zmíněno dříve, v tomto bodě byste již měli ručně stáhnout několik souborů, abyste lépe porozuměli chování a omezením cíle. Nicméně, stále vás čekají překvapení, jakmile se skutečně dostanete k stahování mnoha souborů najednou. Naše rada zde je hlavně udržet to jednoduché. Začněte tím, že stáhnete několik souborů. Můžete použít Python a poté rozšířit na více vláken. Ale někdy je ještě jednodušší generovat Bash soubory přímo z databáze a poté spustit několik z nich v několika terminálových oknech, abyste zvýšili měřítko. Rychlý technický trik, který stojí za zmínku, je použití OUTFILE v MySQL, které můžete napsat kamkoli, pokud deaktivujete "secure_file_priv" v mysqld.cnf (a ujistěte se, že také deaktivujete/přepíšete AppArmor, pokud jste na Linuxu). Data ukládáme na jednoduché pevné disky. Začněte s tím, co máte, a pomalu rozšiřujte. Může být ohromující přemýšlet o ukládání stovek TB dat. Pokud je to situace, které čelíte, nejprve zveřejněte dobrou podmnožinu a ve svém oznámení požádejte o pomoc s uložením zbytku. Pokud si chcete pořídit více pevných disků sami, pak r/DataHoarder má dobré zdroje na získání dobrých nabídek. Snažte se příliš nezabývat složitými souborovými systémy. Je snadné spadnout do králičí nory nastavování věcí jako ZFS. Jedním technickým detailem, na který je třeba si dát pozor, je, že mnoho souborových systémů si neporadí dobře s velkým množstvím souborů. Zjistili jsme, že jednoduchým řešením je vytvořit více adresářů, např. pro různé rozsahy ID nebo předpony hashů. Po stažení dat se ujistěte, že zkontrolujete integritu souborů pomocí hashů v metadatech, pokud jsou k dispozici. 2. Výběr cíle Přístupná: nepoužívá spoustu vrstev ochrany, aby zabránila stahování jejich metadat a dat. Speciální vhled: máte nějaké speciální informace o tomto cíli, například máte nějaký speciální přístup k této sbírce, nebo jste přišli na to, jak překonat jejich obranu. To není nutné (náš nadcházející projekt nedělá nic speciálního), ale určitě to pomáhá! Velká Takže máme naši oblast, na kterou se zaměřujeme, teď kterou konkrétní sbírku zrcadlit? Existuje několik věcí, které dělají dobrý cíl: Když jsme našli naše učebnice vědy na jiných webových stránkách než Library Genesis, snažili jsme se zjistit, jak se dostaly na internet. Poté jsme objevili Z-Library a uvědomili si, že zatímco většina knih se tam neobjeví jako první, nakonec tam skončí. Dozvěděli jsme se o jeho vztahu k Library Genesis a (finanční) motivační struktuře a vynikajícím uživatelském rozhraní, které z něj činí mnohem úplnější sbírku. Poté jsme provedli předběžné stahování metadat a dat a uvědomili si, že můžeme obejít jejich limity stahování IP, využívajíc speciální přístup jednoho z našich členů k mnoha proxy serverům. Při zkoumání různých cílů je již důležité skrýt své stopy pomocí VPN a jednorázových e-mailových adres, o kterých si povíme více později. Unikátní: není již dobře pokryta jinými projekty. Když děláme projekt, má několik fází: Tyto fáze nejsou zcela nezávislé a často vás poznatky z pozdější fáze vrátí do dřívější fáze. Například během scraping metadat si můžete uvědomit, že cíl, který jste vybrali, má obranné mechanismy nad vaší úrovní dovedností (jako blokování IP), takže se vrátíte a najdete jiný cíl. - Anna a tým (<a %(reddit)s>Reddit</a>) Celé knihy mohou být napsány o <em>proč</em> digitálního uchovávání obecně a pirátského archivářství zvláště, ale pojďme si dát rychlý úvod pro ty, kteří nejsou příliš obeznámeni. Svět produkuje více vědomostí a kultury než kdy předtím, ale také více z toho je ztraceno než kdy předtím. Lidstvo většinou svěřuje tuto dědictví korporacím jako akademickým vydavatelům, streamovacím službám a společnostem sociálních médií, a ty se často neukázaly jako skvělí správci. Podívejte se na dokument Digital Amnesia nebo na jakoukoli přednášku Jasona Scotta. Existují některé instituce, které dělají dobrou práci při archivaci co nejvíce, ale jsou vázány zákonem. Jako piráti jsme v jedinečné pozici archivovat sbírky, kterých se nemohou dotknout kvůli vymáhání autorských práv nebo jiným omezením. Můžeme také zrcadlit sbírky mnohokrát po celém světě, čímž zvyšujeme šance na správné uchování. Prozatím se nebudeme pouštět do diskusí o výhodách a nevýhodách duševního vlastnictví, morálce porušování zákona, úvahách o cenzuře nebo otázce přístupu k vědomostem a kultuře. S tím vším stranou, pojďme se ponořit do <em>jak</em>. Podělíme se o to, jak se náš tým stal pirátskými archiváři, a o lekce, které jsme se po cestě naučili. Existuje mnoho výzev, když se vydáte na tuto cestu, a doufejme, že vám s některými z nich pomůžeme. Jak se stát pirátským archivářem První výzva může být překvapivá. Není to technický problém ani právní problém. Je to psychologický problém. Než se do toho ponoříme, dvě aktualizace o Pirate Library Mirror (EDIT: přesunuto na <a %(wikipedia_annas_archive)s>Annin archiv</a>): Dostali jsme velmi štědré dary. První byl 10 000 $ od anonymního jednotlivce, který také podporoval "bookwarrior", původního zakladatele Library Genesis. Zvláštní poděkování patří bookwarrior za zprostředkování tohoto daru. Druhý byl další 10 000 $ od anonymního dárce, který se s námi spojil po našem posledním vydání a byl inspirován k pomoci. Měli jsme také několik menších darů. Děkujeme moc za vaši štědrou podporu. Máme v plánu několik vzrušujících nových projektů, které toto podpoří, takže zůstaňte naladěni. Měli jsme nějaké technické potíže s velikostí našeho druhého vydání, ale naše torrenty jsou nyní nahoře a seedují. Také jsme dostali štědrou nabídku od anonymního jednotlivce, aby seedoval naši sbírku na jejich velmi rychlých serverech, takže provádíme speciální nahrávání na jejich stroje, po kterém by všichni ostatní, kteří stahují sbírku, měli zaznamenat velké zlepšení rychlosti. Blogové příspěvky Ahoj, jsem Anna. Vytvořila jsem <a %(wikipedia_annas_archive)s>Annin archiv</a>, největší stínovou knihovnu na světě. Toto je můj osobní blog, na kterém já a moji spolupracovníci píšeme o pirátství, digitální archivaci a dalších tématech. Spojte se se mnou na <a %(reddit)s>Redditu</a>. Vezměte na vědomí, že tato webová stránka je pouze blog. Hostujeme zde pouze naše vlastní slova. Žádné torrenty ani jiné soubory chráněné autorskými právy zde nejsou hostovány ani odkazovány. <strong>Knihovna</strong> - Stejně jako většina knihoven se primárně zaměřujeme na psané materiály, jako jsou knihy. Možná se v budoucnu rozšíříme i na jiné typy médií. <strong>Zrcadlení</strong> - Jsme striktně zrcadlením existujících knihoven. Zaměřujeme se na zachování, ne na to, aby byly knihy snadno vyhledatelné a stahovatelné (přístup) nebo na vytváření velké komunity lidí, kteří přispívají novými knihami (zdrojování). <strong>Pirátský</strong> - Záměrně porušujeme autorské právo ve většině zemí. To nám umožňuje dělat něco, co legální subjekty nemohou: zajistit, aby knihy byly zrcadleny široko daleko. <em>Na tomto blogu neodkazujeme na soubory. Prosím, najděte si je sami.</em> - Anna a tým (<a %(reddit)s>Reddit</a>) Tento projekt (EDIT: přesunuto na <a %(wikipedia_annas_archive)s>Annin archiv</a>) si klade za cíl přispět k zachování a osvobození lidského vědění. Děláme náš malý a skromný příspěvek, ve stopách velikánů před námi. Zaměření tohoto projektu je ilustrováno jeho názvem: První knihovna, kterou jsme zrcadlili, je Z-Library. Jedná se o populární (a nelegální) knihovnu. Vzali kolekci Library Genesis a udělali ji snadno vyhledatelnou. Navíc se stali velmi efektivními v získávání nových příspěvků knih tím, že motivují přispívající uživatele různými výhodami. V současné době tyto nové knihy nevracejí zpět do Library Genesis. A na rozdíl od Library Genesis neumožňují snadné zrcadlení své kolekce, což brání širokému zachování. To je důležité pro jejich obchodní model, protože účtují peníze za přístup k jejich kolekci ve velkém (více než 10 knih denně). Neděláme morální soudy o vybírání peněz za hromadný přístup k nelegální sbírce knih. Je nepochybné, že Z-Library byla úspěšná v rozšiřování přístupu k vědomostem a získávání více knih. Jsme tu jednoduše proto, abychom udělali svou část: zajistili dlouhodobé uchování této soukromé sbírky. Rádi bychom vás pozvali, abyste pomohli zachovat a osvobodit lidské poznání stahováním a sdílením našich torrentů. Podívejte se na stránku projektu pro více informací o tom, jak jsou data organizována. Také bychom vás velmi rádi vyzvali, abyste přispěli svými nápady, které kolekce zrcadlit dále a jak na to. Společně můžeme dosáhnout mnohého. Toto je jen malý příspěvek mezi nesčetnými dalšími. Děkujeme vám za vše, co děláte. Představujeme Pirátské knihovní zrcadlo: Zachování 7TB knih (které nejsou v Libgenu) 10% of lidského písemného dědictví zachováno navždy <strong>Google.</strong> Koneckonců, provedli tento výzkum pro Google Books. Nicméně, jejich metadata nejsou přístupná hromadně a je poměrně obtížné je získat. <strong>Různé individuální knihovní systémy a archivy.</strong> Existují knihovny a archivy, které nebyly indexovány a agregovány žádným z výše uvedených, často proto, že jsou podfinancované, nebo z jiných důvodů nechtějí sdílet svá data s Open Library, OCLC, Googlem a podobně. Mnoho z nich má digitální záznamy přístupné přes internet a často nejsou dobře chráněny, takže pokud chcete pomoci a zároveň se pobavit učením o podivných knihovních systémech, jsou to skvělé výchozí body. <strong>ISBNdb.</strong> Toto je téma tohoto blogového příspěvku. ISBNdb shromažďuje data o knihách z různých webových stránek, zejména cenová data, která poté prodávají knihkupcům, aby mohli své knihy cenově srovnat s trhem. Vzhledem k tomu, že ISBN jsou dnes poměrně univerzální, efektivně vytvořili „webovou stránku pro každou knihu“. <strong>Open Library.</strong> Jak bylo zmíněno dříve, toto je jejich celá mise. Získali obrovské množství knihovních dat od spolupracujících knihoven a národních archivů a pokračují v tom. Mají také dobrovolné knihovníky a technický tým, který se snaží deduplikovat záznamy a označit je všemi druhy metadata. Nejlepší ze všeho je, že jejich dataset je zcela otevřený. Můžete si ho jednoduše <a %(openlibrary)s>stáhnout</a>. <strong>WorldCat.</strong> Toto je webová stránka provozovaná neziskovou organizací OCLC, která prodává systémy pro správu knihoven. Shromažďují metadata knih z mnoha knihoven a zpřístupňují je prostřednictvím webu WorldCat. Nicméně, také vydělávají peníze prodejem těchto dat, takže nejsou k dispozici pro hromadné stažení. Mají k dispozici některé omezenější hromadné datasets ke stažení ve spolupráci s konkrétními knihovnami. 1. Pro nějakou rozumnou definici "navždy". ;) 2. Samozřejmě, písemné dědictví lidstva je mnohem více než knihy, zejména v dnešní době. Pro účely tohoto příspěvku a našich nedávných vydání se zaměřujeme na knihy, ale naše zájmy sahají dále. 3. O Aaronu Swartzovi lze říci mnohem více, ale chtěli jsme ho jen stručně zmínit, protože hraje klíčovou roli v tomto příběhu. Jak čas plyne, více lidí může narazit na jeho jméno poprvé a následně se ponořit do králičí nory sami. <strong>Fyzické kopie.</strong> Samozřejmě to není příliš užitečné, protože jsou to jen duplikáty stejného materiálu. Bylo by skvělé, kdybychom mohli zachovat všechny poznámky, které lidé dělají v knihách, jako jsou Fermatovy slavné „čmáranice na okrajích“. Ale bohužel, to zůstane snem archiváře. <strong>„Vydání“.</strong> Zde počítáte každou jedinečnou verzi knihy. Pokud je na ní něco jiného, jako jiný obal nebo jiná předmluva, počítá se jako jiné vydání. <strong>Soubory.</strong> Při práci se stínovými knihovnami jako Library Genesis, Sci-Hub nebo Z-Library je třeba zvážit další aspekt. Může existovat více skenů stejného vydání. A lidé mohou vytvářet lepší verze existujících souborů, skenováním textu pomocí OCR nebo opravou stránek, které byly skenovány pod úhlem. Chceme tyto soubory počítat jako jedno vydání, což by vyžadovalo dobré metadata nebo deduplikaci pomocí měření podobnosti dokumentů. <strong>„Díla“.</strong> Například „Harry Potter a Tajemná komnata“ jako logický koncept, zahrnující všechny jeho verze, jako různé překlady a dotisky. To je docela užitečná definice, ale může být těžké stanovit, co se počítá. Například pravděpodobně chceme zachovat různé překlady, i když dotisky s pouze drobnými rozdíly nemusí být tak důležité. - Anna a tým (<a %(reddit)s>Reddit</a>) S Pirátskou knihovní zrcadlením (EDIT: přesunuto na <a %(wikipedia_annas_archive)s>Annin archiv</a>) je naším cílem vzít všechny knihy na světě a zachovat je navždy.<sup>1</sup> Mezi našimi torrenty Z-Library a původními torrenty Library Genesis máme 11 783 153 souborů. Ale kolik to vlastně je? Pokud bychom tyto soubory správně deduplikovali, jaké procento všech knih na světě jsme zachovali? Opravdu bychom chtěli mít něco takového: Začněme s několika hrubými čísly: V obou Z-Library/Libgen a Open Library je mnohem více knih než unikátních ISBN. Znamená to, že mnoho z těchto knih nemá ISBN, nebo metadata ISBN prostě chybí? Pravděpodobně můžeme na tuto otázku odpovědět kombinací automatizovaného párování na základě jiných atributů (název, autor, vydavatel atd.), přidáním dalších zdrojů dat a extrakcí ISBN z vlastních skenů knih (v případě Z-Library/Libgen). Kolik z těchto ISBN je unikátních? To je nejlépe ilustrováno Vennovým diagramem: Abychom byli přesnější: Byli jsme překvapeni, jak málo překryvů existuje! ISBNdb má obrovské množství ISBN, které se neobjevují ani v Z-Library, ani v Open Library, a totéž platí (v menší, ale stále podstatné míře) pro ty další dvě. To vyvolává mnoho nových otázek. Jak moc by automatizované párování pomohlo při označování knih, které nebyly označeny ISBN? Bylo by mnoho shod a tím pádem zvýšený překryv? Také, co by se stalo, kdybychom přidali 4. nebo 5. dataset? Kolik překryvů bychom pak viděli? To nám dává výchozí bod. Nyní se můžeme podívat na všechny ISBN, které nebyly v datasetu Z-Library, a které se neshodují ani s poli název/autor. To nám může poskytnout nástroj pro zachování všech knih na světě: nejprve shromážděním skenů z internetu, poté vyrazit do reálného života a skenovat knihy. To druhé by mohlo být dokonce financováno z davu, nebo poháněno „odměnami“ od lidí, kteří by rádi viděli určité knihy digitalizované. To vše je příběh na jinou dobu. Pokud chcete pomoci s některou z těchto činností — další analýza; shromažďování více metadata; hledání dalších knih; OCR knih; provádění této činnosti pro jiné oblasti (např. články, audioknihy, filmy, televizní pořady, časopisy) nebo dokonce zpřístupnění některých z těchto dat pro věci jako ML / trénink velkých jazykových modelů — prosím kontaktujte mě (<a %(reddit)s>Reddit</a>). Pokud vás konkrétně zajímá analýza dat, pracujeme na tom, abychom naše Datasets a skripty zpřístupnili ve formátu, který je snadněji použitelný. Bylo by skvělé, kdybyste si mohli jednoduše forknout notebook a začít si s tím hrát. A nakonec, pokud chcete tuto práci podpořit, zvažte prosím darování. Jedná se o zcela dobrovolnickou operaci a váš příspěvek má obrovský význam. Každý kousek pomáhá. Prozatím přijímáme dary v kryptoměnách; podívejte se na stránku Darovat na Annin archiv. Procento vyžaduje jmenovatele: celkový počet knih, které kdy byly vydány.<sup>2</sup> Před zánikem Google Books se inženýr projektu, Leonid Taycher, <a %(booksearch_blogspot)s>pokoušel odhadnout</a> toto číslo. Přišel — s nadsázkou — s číslem 129 864 880 („alespoň do neděle“). Toto číslo odhadl vytvořením sjednocené databáze všech knih na světě. K tomu shromáždil různé datasets a poté je různými způsoby sloučil. Jako rychlou odbočku zmíníme další osobu, která se pokusila katalogizovat všechny knihy na světě: Aaron Swartz, zesnulý digitální aktivista a spoluzakladatel Redditu.<sup>3</sup> <a %(youtube)s>Založil Open Library</a> s cílem „jedna webová stránka pro každou knihu, která kdy byla vydána“, kombinující data z mnoha různých zdrojů. Nakonec zaplatil nejvyšší cenu za svou práci na digitální ochraně, když byl stíhán za hromadné stahování akademických prací, což vedlo k jeho sebevraždě. Není třeba říkat, že to je jeden z důvodů, proč je naše skupina pseudonymní a proč jsme velmi opatrní. Open Library je stále hrdinsky provozována lidmi z Internet Archive, pokračující v Aaronově odkazu. K tomu se vrátíme později v tomto příspěvku. V příspěvku na blogu Google Taycher popisuje některé z výzev při odhadování tohoto čísla. Nejprve, co představuje knihu? Existuje několik možných definic: „Vydání“ se zdají být nejpraktičtější definicí toho, co jsou „knihy“. Pohodlně se tato definice také používá pro přiřazování jedinečných čísel ISBN. ISBN, nebo Mezinárodní standardní číslo knihy, se běžně používá pro mezinárodní obchod, protože je integrováno s mezinárodním systémem čárových kódů („Mezinárodní číslo článku“). Pokud chcete prodávat knihu v obchodech, potřebuje čárový kód, takže získáte ISBN. Taycherův blogový příspěvek zmiňuje, že zatímco ISBN jsou užitečné, nejsou univerzální, protože byly skutečně přijaty až v polovině sedmdesátých let a ne všude po světě. Přesto je ISBN pravděpodobně nejpoužívanějším identifikátorem knižních vydání, takže je to náš nejlepší výchozí bod. Pokud bychom mohli najít všechny ISBN na světě, získali bychom užitečný seznam knih, které je ještě třeba zachovat. Takže, odkud získáváme data? Existuje několik stávajících snah, které se snaží sestavit seznam všech knih na světě: V tomto příspěvku s radostí oznamujeme malé vydání (ve srovnání s našimi předchozími vydáními Z-Library). Shromáždili jsme většinu ISBNdb a zpřístupnili data ke stažení na webu Pirate Library Mirror (EDIT: přesunuto na <a %(wikipedia_annas_archive)s>Annin archiv</a>; nebudeme to zde přímo odkazovat, stačí to vyhledat). Jedná se o přibližně 30,9 milionu záznamů (20 GB jako <a %(jsonlines)s>JSON Lines</a>; 4,4 GB gzipped). Na jejich webu tvrdí, že mají skutečně 32,6 milionu záznamů, takže jsme možná nějaké vynechali, nebo <em>oni</em> dělají něco špatně. V každém případě, prozatím nebudeme sdílet, jak jsme to udělali — to necháme jako cvičení pro čtenáře. ;-) Co sdílíme, je nějaká předběžná analýza, abychom se pokusili přiblížit odhadu počtu knih na světě. Podívali jsme se na tři datasets: tento nový dataset ISBNdb, naše původní vydání metadata, které jsme shromáždili ze stínové knihovny Z-Library (která zahrnuje Library Genesis), a datový dump Open Library. ISBNdb dump, nebo Kolik knih je zachováno navždy? Pokud bychom správně deduplikovali soubory ze stínových knihoven, jaké procento všech knih na světě jsme zachovali? Aktualizace o <a %(wikipedia_annas_archive)s>Annině archivu</a>, největší skutečně otevřené knihovně v historii lidstva. <em>Redesign WorldCat</em> Data <strong>Formát?</strong> <a %(blog)s>Kontejnery Annina archivu (AAC)</a>, což je v podstatě <a %(jsonlines)s>JSON Lines</a> komprimovaný pomocí <a %(zstd)s>Zstandard</a>, plus některé standardizované sémantiky. Tyto kontejnery obalují různé typy záznamů, založené na různých scrapovacích metodách, které jsme nasadili. Před rokem jsme se <a %(blog)s>pustili</a> do zodpovězení této otázky: <strong>Jaké procento knih bylo trvale zachováno stínovými knihovnami?</strong> Podívejme se na základní informace o datech: Jakmile se kniha dostane do stínové knihovny s otevřenými daty, jako je <a %(wikipedia_library_genesis)s>Library Genesis</a>, a nyní <a %(wikipedia_annas_archive)s>Annin archiv</a>, je zrcadlena po celém světě (prostřednictvím torrentů), čímž je prakticky zachována navždy. Abychom odpověděli na otázku, jaké procento knih bylo zachováno, potřebujeme znát jmenovatel: kolik knih existuje celkem? A ideálně nemáme jen číslo, ale skutečná metadata. Pak je můžeme nejen porovnat se stínovými knihovnami, ale také <strong>vytvořit seznam knih, které je třeba zachovat!</strong> Mohli bychom dokonce začít snít o crowdsourcovaném úsilí projít tento seznam. Scrapovali jsme <a %(wikipedia_isbndb_com)s>ISBNdb</a> a stáhli dataset <a %(openlibrary)s>Open Library</a>, ale výsledky byly neuspokojivé. Hlavním problémem bylo, že se ISBN příliš nepřekrývaly. Podívejte se na tento Vennův diagram z <a %(blog)s>našeho blogového příspěvku</a>: Byli jsme velmi překvapeni, jak málo se ISBNdb a Open Library překrývají, přestože oba liberálně zahrnují data z různých zdrojů, jako jsou webové scrapes a knihovní záznamy. Pokud by obě dobře nacházely většinu ISBN, jejich kruhy by se jistě výrazně překrývaly, nebo by jeden byl podmnožinou druhého. Přimělo nás to přemýšlet, kolik knih se nachází <em>zcela mimo tyto kruhy</em>? Potřebujeme větší databázi. To je, když jsme zaměřili naše úsilí na největší databázi knih na světě: <a %(wikipedia_worldcat)s>WorldCat</a>. Jedná se o proprietární databázi neziskové organizace <a %(wikipedia_oclc)s>OCLC</a>, která agreguje metadata záznamy z knihoven po celém světě výměnou za to, že těmto knihovnám poskytuje přístup k celému datasetu a zajišťuje, že se objeví ve výsledcích vyhledávání koncových uživatelů. I když je OCLC nezisková organizace, jejich obchodní model vyžaduje ochranu jejich databáze. No, je nám líto, přátelé z OCLC, ale my to všechno rozdáváme. :-) Během uplynulého roku jsme pečlivě scrapovali všechny záznamy WorldCat. Nejprve jsme měli štěstí. WorldCat právě zaváděl kompletní redesign svých webových stránek (v srpnu 2022). To zahrnovalo podstatnou revizi jejich backendových systémů, což vedlo k mnoha bezpečnostním chybám. Okamžitě jsme využili příležitosti a byli schopni scrapovat stovky milionů (!) záznamů během několika dní. Poté byly bezpečnostní chyby pomalu opravovány jedna po druhé, až byla poslední, kterou jsme našli, opravena asi před měsícem. Do té doby jsme měli téměř všechny záznamy a šli jsme jen po mírně kvalitnějších záznamech. Takže jsme cítili, že je čas na vydání! 1,3 miliardy záznamů z WorldCat <em><strong>Ve zkratce:</strong> Annin archiv stáhl všechny záznamy z WorldCat (největší světové sbírky knihovních metadat), aby vytvořil seznam knih, které je třeba zachovat.</em> WorldCat Upozornění: tento blogový příspěvek byl zastaralý. Rozhodli jsme se, že IPFS ještě není připraveno na hlavní čas. Stále budeme odkazovat na soubory na IPFS z Annina archivu, pokud to bude možné, ale nebudeme je již sami hostovat, ani nedoporučujeme ostatním zrcadlit pomocí IPFS. Pokud chcete pomoci uchovat naši sbírku, podívejte se na naši stránku Torrenty. Pomozte seedovat Z-Library na IPFS Stažení z partnerského serveru SciDB Externí výpůjčka Externí výpůjčka (tisk zakázán) Externí stažení Prohlížet metadata Obsaženo v torrentech Zpět  (+%(num)s bonus) nezaplaceno zaplaceno zrušeno vypršela platnost čekání na potvrzení Annou neplatný Text níže pokračuje v angličtině. Jít Resetovat Vpřed Poslední Pokud vaše emailová adresa na Libgen fórech nefunguje, doporučujeme použít <a %(a_mail)s>Proton Mail</a> (zdarma). Je též možné <a %(a_manual)s>manuálně zažádat</a> o aktivaci vašeho účtu. (neomezené stahování — může vyžadovat <a %(a_browser)s>ověření prohlížeče</a> ) Rychlý partnerský server #%(number)s (doporučeno) (o něco rychlejší, ale s waitlistem) (nevyžaduje ověření prohlížeče) (bez ověření prohlížeče, či waitlistu) (bez waitlistu, může však být velmi pomalý) Pomalý partnerský server #%(number)s Audiokniha Komiks Kniha (beletrie) Kniha (populárně naučná literatura) Kniha (neurčeno) Odborný článek Časopis Noty Jiné Standardizační dokument Některé stránky nebylo možné převést do formátu PDF Označeno jako rozbité na Libgen.li Nelze nalézt na Libgen.li Beletrie nedostupná na Libgen.rs Populárně naučná literatura nedostupná na Libgen.rs Nástroj exiftool selhal při zpracovávání tohoto souboru Označeno jako „špatný soubor“ v Z-Library Chybí v Z-Library Označeno jako „spam“ v Z-Library Soubor nelze otevřít (např. poškozený soubor, DRM) Uplatnit nárok na autorské právo Problémy se stahováním (např. nelze se připojit, chybové hlášky, pomalé stahování) Nesprávná metadata (např. název, popisek, obálka) Jiné Špatná kvalita (např. problémy s formátováním, nízká kvalita skenů, chybějící stránky) Spam / soubor by měl být odstraněn (např. reklama, nevhodný obsah) %(amount)s (%(amount_usd)s) %(amount)s celkem %(amount)s (%(amount_usd)s) celkem Brilantní knihomol Šťastný knihovník Oslnivý datakupič Úžasný archivář Bonusová stažení Cerlalc Česká metadata DuXiu 读秀 EBSCOhost eBook Index Google Knihy Goodreads HathiTrust IA IA Kontrolované digitální půjčování ISBNdb ISBN GRP Libgen.li Kromě „scimag“ Libgen.rs Non-Fiction a Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Ruská státní knihovna Sci-Hub Přes Libgen.li „scimag“ Sci-Hub / Libgen „scimag“ Trantor Nahrát na Annin Archiv Z-Library Z-Library Název, autor, DOI, ISBN, MD5, … Vyhledat Autor Popis a komentáře k metadatům Vydání Původní název souboru Nakladatel (zvolte vyhledávací pole) Název Rok vydání Technické podrobnosti Tato kryptoměna má mnohem větší minimum než je obvyklé. Prosíme, zvolte jiné trvání nebo jinou kryptoměnu. Požadavek nebylo možné dokončit, za pár minut to prosím zkuste znovu. Pokud se bude tento problém opakovat, napište nám na %(email)s a přiložte snímek obrazovky. Nastala neznámá chyba. Prosíme, kontaktujte nás na %(email)s se snímkem obrazovky. Chyba při zpracování platby. Počkejte prosím chvíli a zkuste to znovu. Pokud problém přetrvává déle než 24 hodin, kontaktujte nás prosím na %(email)s a zašlete nám snímek obrazovky. Pořádáme finanční sbírku na <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">zálohování</a> největší stínové knihovny komiksů na světě. Jsme vám velice vděční za vaši podporu! <a href="/donate">Přispějte.</a>Pokud nejste schopni přispět, podpořte nás tak, že o nás řeknete svým přátelům a že nás budete sledovat na <a href="https://www.reddit.com/r/Annas_Archive">Redditu</a> a <a href="https://t.me/annasarchiveorg">Telegramu</a>. Prosíme, neposílejte nám emaily s <a %(a_request)s>žádostmi o knihy</a><br>nebo malým (<10k) <a %(a_upload)s>nahráváním souborů</a>. Annin archiv DMCA / nárokování autorských práv Buďte v kontaktu Reddit Alternativní domény SLUM (%(unaffiliated)s) nepřidružený Annin archiv potřebuje vaši pomoc! Pokud darujete nyní, získáte <strong>dvojnásobný</strong> počet rychlých stažení. Mnozí se nás snaží zničit, ale my bojujeme. Pokud tento měsíc přispějete, získáte <strong>dvojnásobný</strong> počet rychlých stažení. Platí do konce tohoto měsíce. Záchrana lidských znalostí: skvělý dárek k svátku! Členství budou odpovídajícím způsobem prodloužena. Partnerské servery nejsou dostupné kvůli uzavření hostingu. Měly by být brzy opět v provozu. Abychom zvýšili odolnost Annina archivu, hledáme dobrovolníky k provozování zrcadel. Je dostupná nová platební metoda pro darování: %(method_name)s. Prosím zvažte%(donate_link_open_tag)szaslání daru</a> —provozovat tuto stránku není levné a vaše dary nám skutečně pomohou. Jsme vám velice vděční. Pozvěte kamaráda a oba dva získáte %(percentage)s bonusových rychlých stažení! Překvapte někoho blízkého, dejte mu účet s členstvím. Perfektní Valentýnský dárek! Zjistit více… Účet Aktivita Pokročilé Annin blog↗ Annin software ↗ beta Codes Explorer Datasety Přispět Stažené soubory FAQ Domů Zlepšit metadata Data pro LLM Přihlásit / Registrovat Mé dary Veřejný profil Vyhledat Bezpečnost Torrenty Překlad ↗ Dobrovolnictví a odměny Nedávno staženo: 📚&nbsp;Nejvějtší open-source open-data knihovna na světě. ⭐️&nbsp;Zrcadlíme Sci-Hub, Library Genesis, Z-Library a další. 📈&nbsp;%(book_any)s knih, %(journal_article)s odborných článků, %(book_comic)s komiksů, %(magazine)s časopisů — uchováno navždy.  a  a další DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Největší skutečně otevřená knihovna v historii lidstva. 📈&nbsp;%(book_count)s&nbsp;knih, %(paper_count)s&nbsp;odborných článků — uchováno navždy. ⭐️&nbsp;Zrcadlíme %(libraries)s. Scrapujeme data ze %(scraped)s a následně je otevíráme veřejnosti. Veškerý náš kód a data jsou zcela open source. OpenLib Sci-Hub ,  📚Největší open-source open-data knihovna na světě. <br>⭐Zrcadlí Scihub, Libgen, Zlib a další. Z-Lib Annin archiv Neplatný požadavek. Navštivte %(websites)s. Největší světová open-source open-data knihovna. Zrcadlí Sci-Hub, Library Genesis, Z-Library a další. Prohledat Annin archiv Annin archiv Obnovte stránku a zkuste to znovu. Pokud problém přetrvává několik hodin, <a %(a_contact)s>kontaktuje nás</a>. 🔥 Problémy s načítáním této stránky <li>1. Sledujte nás na <a href="https://www.reddit.com/r/Annas_Archive/">Redditu</a> nebo <a href="https://t.me/annasarchiveorg">Telegramu</a>.</li><li>2. Šiřte povědomí o Annině archivu na Twitteru, Redditu, Tiktoku, Instagramu, ve vaší oblíbené hospodě, kavárně, knihovně nebo kdekoliv jinde! Nevěříme v blokování volného přístupu k informacím — pokud nás zablokují, prostě se znovu objevíme někde jinde, protože všechen náš kód a data jsou plně open-source.</li><li>3. Pokud můžete, zvažte <a href="/donate">finanční podporu</a> tohoto projektu.</li><li>4. Pomozte s <a href="https://translate.annas-software.org/">překladem</a> naší webové stránky do jiných jazyků.</li><li>5. Pokud máte zkušenosti s programováním, můžete přispět do našeho <a href="https://annas-software.org/">open-source kódu</a>, nebo seedovat naše <a href="/datasets">torrenty</a>.</li> 10. Vytvořte nebo doplňujte stránku Annina archivu na WIkipedii, ať už v češtině, angličtině, nebo jiných jazycích. 11. Máme zájem o umístění drobných, nerušivých reklam na naši stránku. Pokud byste měli zájem, kontaktujte nás. 6. Pokud máte zkušenosti s bezpečnostním výzkumem, měli bychom využití pro vaše dovednosti, jak pro útok, tak pro obranu. Viz záložka <a %(a_security)s>Bezpečnost</a>. 7. Hledáme specialisty na platby u anonymních obchodníků. Můžete nám pomoci přidat pohodlnější metody darování (PayPal, WeChat, dárkové karty)? Pokud máte zájem nebo nás můžete s někým spojit, prosím napište nám. 8. Stále se snažíme rozšiřovat kapacity našich serverů. 9. Podpořit nás můžete také nahlašováním problémů se soubory, komentováním, a vytvářením seznamů. Pomoct nám můžete i <a %(a_upload)s>nahráváním knih</a> nebo opravováním problémů se soubory a jejich formátováním. Pro podrobnější informace o tom, jak se stát dobrovolníkem, navštivte naši stránku <a %(a_volunteering)s>Dobrovolnictví a odměny</a>. Pevně věříme ve volný pohyb informací a v uchovávání vědění a kultury. Hluboce si vážíme práce těch, kteří vytvořili zde uchované stínové knihovny, a doufáme, že náš projekt zvýší jejich dosah. Tento vyhledávač stojí na ramenou obrů. Pokud chcete být informováni o našich pokrocích, sledujte Annu na <a href="https://www.reddit.com/r/Annas_Archive/">Redditu</a> nebo <a href="https://t.me/annasarchiveorg">Telegramu</a>. Dotazy a zpětnou vazbu posílejte Anně na %(email)s. ID účtu: %(account_id)s Odhlásit se ❌ Něco se pokazilo. Prosím, obnovte stránku a zkuste to ještě jednou. ✅ Nyní jste odhlášeni. Pro další přihlášení znovu načtěte stránku. Využití rychlého stahování (last 24 hours): <strong>%(used)s / %(total)s</strong> Členství: <strong>%(tier_name)s</strong> do %(until_date)s <a %(a_extend)s>(prodloužit)</a> Můžete kombinovat více členství (rychlé stahování za 24 hodin se sečtou). Členství: <strong>žádné</strong> <a %(a_become)s>(stát se členem)</a> Pokud chcete zvýšit úroveň svého členství, kontaktujte Annu na %(email)s. Veřejný profil: %(profile_link)s Tajný klíč (nesdílejte!): %(secret_key)s ukázat Připojte se! Přejděte na <a %(a_tier)s>vyšší úroveň</a> a připojte se k naší skupině. Exkluzivní skupina na Telegramu: %(link)s Účet která stažení? Přihlášení Neztraťte svůj klíč! Neplatný tajný klíč. Ověřte, že je váš klíč zadán správně a zkuste to znovu, nebo si založte nový účet. Tajný klíč Pro přihlášení vložte svůj tajný klíč: Starý účet s emailovým přihlašováním? Zadejte svůj <a %(a_open)s>email zde</a>. Registrovat nový účet Ještě nemáte účet? Registrace byla úspěšná! Váš tajný klíč je: <span %(span_key)s>%(key)s</span> Pečlivě si tento klíč uložte. Pokud o něj přijdete, ztratíte přístup k vašemu účtu. <li %(li_item)s><strong>Záložka.</strong> Tuto stránku si můžete přidat do záložek a uložit tak svůj klíč.</li><li %(li_item)s><strong>Stáhnout.</strong> Kliknutím na <a %(a_download)s>tento odkaz</a> si stáhnete váš klíč.</li><li %(li_item)s><strong>Správce hesel.</strong> Při zadání klíče použijte k jeho uložení správce hesel.</li> Přihlášení / Registrace Ověření prohlížeče Varování: kód obsahuje nesprávné znaky Unicode a může se chovat nesprávně v různých situacích. Surový binární kód lze dekódovat z base64 reprezentace v URL. Popis Štítek Předpona URL pro konkrétní kód Webová stránka Kódy začínající “%(prefix_label)s” Prosím, nescrapujte tyto stránky. Místo toho doporučujeme <a %(a_import)s>generování</a> nebo <a %(a_download)s>stahování</a> našich databází ElasticSearch a MariaDB a spuštění našeho <a %(a_software)s>open source kódu</a>. Surová data lze ručně prozkoumat prostřednictvím JSON souborů, jako je <a %(a_json_file)s>tento</a>. Méně než %(count)s záznamů Obecná URL Průzkumník kódů Rejstřík Prozkoumejte kódy, kterými jsou záznamy označeny, podle předpony. Sloupec „záznamy“ ukazuje počet záznamů označených kódy s danou předponou, jak je vidět ve vyhledávači (včetně záznamů pouze s metadata). Sloupec „kódy“ ukazuje, kolik skutečných kódů má danou předponu. Známý prefix kódu “%(key)s” Více… Předpona %(count)s záznam odpovídající “%(prefix_label)s” %(count)s záznamy odpovídající “%(prefix_label)s” %(count)s záznamů odpovídajících “%(prefix_label)s” kódy záznamy “%%s” bude nahrazen hodnotou kódu Vyhledat Annin archiv Kódy URL pro konkrétní kód: “%(url)s” Tato stránka může trvat déle na vygenerování, proto vyžaduje Cloudflare captcha. <a %(a_donate)s>Členové</a> mohou captcha přeskočit. Zneužití nahlášeno: Lepší verze Chcete nahlásit tohoto uživatele za zneužívající nebo nevhodné chování? Problém se souborem: %(file_issue)s skrytý komentář Odpovědět Nahlásit zneužití Nahlásili jste tohoto uživatele za zneužití. Nároky na autorská práva v tomto e-mailu budou ignorovány; místo toho použijte formulář. Ukázat email Velmi uvítáme vaši zpětnou vazbu a dotazy! Vzhledem k množství spamu a nesmyslných e-mailů, které dostáváme, však prosím zaškrtněte políčka a potvrďte, že rozumíte těmto podmínkám pro kontaktování. Jakékoli jiné způsoby kontaktování ohledně nároků na autorská práva budou automaticky smazány. Pro DMCA / nárokování autorských práv použijte <a %(a_copyright)s>tento formulář</a>. kontaktní email URL adresy na Annině archivu (povinné). Jedna na řádek. Zahrňte prosím pouze URL adresy, které popisují přesně stejnou edici knihy. Pokud chcete uplatnit nárok na více knih nebo více edic, odešlete tento formulář vícekrát. Nároky, které zahrnují více knih nebo edic dohromady, budou zamítnuty. Adresa (povinné) Jasný popis zdrojového materiálu (povinné) E-mail (povinné) URL adresy zdrojového materiálu, jedna na řádek (povinné). Zahrňte prosím co nejvíce, aby nám to pomohlo ověřit váš nárok (např. Amazon, WorldCat, Google Books, DOI). ISBN zdrojového materiálu (pokud je to relevantní). Jeden na řádek. Zahrňte prosím pouze ty, které přesně odpovídají edici, pro kterou uplatňujete nárok na autorská práva. Vaše jméno (povinné) ❌ Něco se pokazilo. Prosím znovu načtěte stránku a zkuste to znovu. ✅ Děkujeme za podání vašeho nároku na autorská práva. Přezkoumáme jej co nejdříve. Pro podání dalšího nároku prosím znovu načtěte stránku. <a %(a_openlib)s>Open Library</a> URL adresy zdrojového materiálu, jedna na řádek. Věnujte prosím chvíli hledání zdrojového materiálu v Open Library. To nám pomůže ověřit váš nárok. Telefonní číslo (povinné) Prohlášení a podpis (povinné) Odeslat nárok Pokud máte nárok na autorská práva nebo jiný nárok podle DMCA, vyplňte prosím tento formulář co nejpřesněji. Pokud narazíte na jakékoli problémy, kontaktujte nás na naší vyhrazené adrese pro DMCA: %(email)s. Upozorňujeme, že nároky zaslané na tuto adresu nebudou zpracovány, slouží pouze pro dotazy. Pro podání nároků použijte prosím níže uvedený formulář. Formulář pro uplatnění nároku na autorská práva / DMCA Ukázkový záznam v Annině archivu Torrenty od Annina archivu Formát Annin archiv Containers Skripty pro import metadata Pokud máte zájem o zrcadlení této databáze pro <a %(a_archival)s>archivní</a> nebo <a %(a_llm)s>LLM tréninkové</a> účely, kontaktujte nás. Poslední aktualizace: %(date)s Hlavní %(source)s webová stránka Dokumentace metadata (většina polí) Soubory zrcadlené Anniným archivem: %(count)s (%(percent)s%%) Zdroje Celkový počet souborů: %(count)s Celková velikost souborů: %(size)s Náš blogový příspěvek o těchto datech <a %(duxiu_link)s>Duxiu</a> je obrovská databáze naskenovaných knih, vytvořená <a %(superstar_link)s>SuperStar Digital Library Group</a>. Většinou se jedná o akademické knihy, které byly naskenovány, aby byly digitálně dostupné univerzitám a knihovnám. Pro naše anglicky mluvící publikum mají <a %(princeton_link)s>Princeton</a> a <a %(uw_link)s>University of Washington</a> dobré přehledy. Existuje také vynikající článek, který poskytuje více informací: <a %(article_link)s>„Digitalizace čínských knih: Případová studie vyhledávače SuperStar DuXiu Scholar“</a>. Knihy z Duxiu byly dlouho pirátěny na čínském internetu. Obvykle jsou prodávány za méně než dolar přeprodejci. Obvykle jsou distribuovány pomocí čínského ekvivalentu Google Drive, který byl často hacknut, aby umožnil více úložného prostoru. Některé technické detaily lze nalézt <a %(link1)s>zde</a> a <a %(link2)s>zde</a>. Ačkoli byly knihy poloveřejně distribuovány, je poměrně obtížné je získat ve velkém množství. Měli jsme to vysoko na našem seznamu úkolů a vyčlenili jsme na to několik měsíců plné práce. Nicméně, na konci roku 2023 nás oslovil neuvěřitelný, úžasný a talentovaný dobrovolník, který nám řekl, že už tuto práci udělal — za velké náklady. Sdílel s námi celou sbírku, aniž by očekával cokoli na oplátku, kromě záruky dlouhodobého uchování. Opravdu pozoruhodné. Více informací od našich dobrovolníků (surové poznámky): Převzato z našeho <a %(a_href)s>blogového příspěvku</a>. DuXiu 读秀 %(count)s soubor %(count)s soubory %(count)s soubory Tento dataset úzce souvisí s <a %(a_datasets_openlib)s>datasetem Open Library</a>. Obsahuje scrape všech metadata a velkou část souborů z IA’s Controlled Digital Lending Library. Aktualizace jsou vydávány ve <a %(a_aac)s>formátu Anna’s Archive Containers</a>. Tyto záznamy jsou přímo odkazovány z datasetu Open Library, ale také obsahují záznamy, které nejsou v Open Library. Máme také řadu datových souborů, které byly v průběhu let seškrábány členy komunity. Sbírka se skládá ze dvou částí. Potřebujete obě části, abyste získali všechna data (kromě nahrazených torrentů, které jsou na stránce torrentů přeškrtnuty). Digitální výpůjční knihovna naše první vydání, předtím než jsme standardizovali na formát <a %(a_aac)s>Annin archiv Containers (AAC)</a>. Obsahuje metadata (ve formátech json a xml), pdf soubory (z digitálních výpůjčních systémů acsm a lcpdf) a miniatury obálek. inkrementální nová vydání, používající AAC. Obsahuje pouze metadata s časovými razítky po 2023-01-01, protože zbytek je již pokryt „ia“. Také všechny pdf soubory, tentokrát z výpůjčních systémů acsm a „bookreader“ (webový čtečka IA). Navzdory tomu, že název není úplně správný, stále zařazujeme soubory bookreader do kolekce ia2_acsmpdf_files, protože jsou vzájemně vylučující. IA Kontrolované digitální půjčování 98%%+ souborů je prohledatelných. Naším posláním je archivovat všechny knihy na světě (stejně jako články, časopisy atd.) a zpřístupnit je široké veřejnosti. Věříme, že všechny knihy by měly být široce zrcadleny, aby byla zajištěna redundance a odolnost. Proto shromažďujeme soubory z různých zdrojů. Některé zdroje jsou zcela otevřené a mohou být hromadně zrcadleny (jako Sci-Hub). Jiné jsou uzavřené a chráněné, takže se je snažíme seškrábat, abychom „osvobodili“ jejich knihy. Další spadají někam mezi. Všechna naše data lze <a %(a_torrents)s>stahovat přes torrent</a> a všechna naše metadata lze <a %(a_anna_software)s>generovat</a> nebo <a %(a_elasticsearch)s>stahovat</a> jako databáze ElasticSearch a MariaDB. Surová data lze ručně prozkoumat prostřednictvím JSON souborů, jako je <a %(a_dbrecord)s>tento</a>. Metadata Webová stránka ISBN Poslední aktualizace: %(isbn_country_date)s (%(link)s) Zdroje Mezinárodní agentura ISBN pravidelně zveřejňuje rozsahy, které přidělila národním agenturám ISBN. Z toho můžeme odvodit, do jaké země, regionu nebo jazykové skupiny ISBN patří. Tato data aktuálně používáme nepřímo, prostřednictvím Python knihovny <a %(a_isbnlib)s>isbnlib</a>. Informace o zemi ISBN Toto je výpis mnoha volání na isbndb.com během září 2022. Snažili jsme se pokrýt všechny rozsahy ISBN. Jedná se o přibližně 30,9 milionu záznamů. Na jejich webových stránkách tvrdí, že mají skutečně 32,6 milionu záznamů, takže jsme možná nějaké zmeškali, nebo <em>oni</em> mohli udělat něco špatně. Odpovědi JSON jsou v podstatě surové z jejich serveru. Jeden problém s kvalitou dat, který jsme zaznamenali, je, že pro čísla ISBN-13, která začínají jiným předponou než „978-“, stále zahrnují pole „isbn“, které je jednoduše číslo ISBN-13 s prvními 3 čísly odříznutými (a kontrolní číslice přepočítána). To je zjevně špatně, ale tak to dělají, takže jsme to nezměnili. Další potenciální problém, na který můžete narazit, je skutečnost, že pole „isbn13“ má duplikáty, takže jej nemůžete použít jako primární klíč v databázi. Kombinace polí „isbn13“+„isbn“ se zdá být jedinečná. Vydání 1 (2022-10-31) Fiction torrenty jsou pozadu (i když ID ~4-6M nejsou torrenty, protože se překrývají s našimi Zlib torrenty). Náš blogový příspěvek o vydání komiksů Torrenty komiksů na Annině archivu Pro příběh o různých forkech Library Genesis, viz stránku pro <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li obsahuje většinu stejného obsahu a metadat jako Libgen.rs, ale má navíc některé kolekce, konkrétně komiksy, časopisy a standardní dokumenty. Také integroval <a %(a_scihub)s>Sci-Hub</a> do své databáze a vyhledávače, což používáme pro naši databázi. Metadata pro tuto knihovnu jsou volně dostupná <a %(a_libgen_li)s>na libgen.li</a>. Tento server je však pomalý a nepodporuje obnovení přerušených připojení. Stejné soubory jsou také dostupné na <a %(a_ftp)s>FTP serveru</a>, který funguje lépe. Zdá se, že i literatura faktu se odklonila, ale bez nových torrentů. Zdá se, že k tomu došlo od začátku roku 2022, i když jsme to neověřili. Podle administrátora Libgen.li by sbírka „fiction_rus“ (ruská beletrie) měla být pokryta pravidelně vydávanými torrenty z <a %(a_booktracker)s>booktracker.org</a>, zejména torrenty <a %(a_flibusta)s>flibusta</a> a <a %(a_librusec)s>lib.rus.ec</a> (které zrcadlíme <a %(a_torrents)s>zde</a>, i když jsme dosud nezjistili, které torrenty odpovídají kterým souborům). Sbírka beletrie má své vlastní torrenty (odlišné od <a %(a_href)s>Libgen.rs</a>) počínaje %(start)s. Určité rozsahy bez torrentů (jako rozsahy beletrie f_3463000 až f_4260000) jsou pravděpodobně soubory Z-Library (nebo jiné duplikáty), i když bychom mohli chtít provést deduplikaci a vytvořit torrenty pro soubory unikátní pro lgli v těchto rozsazích. Statistiky pro všechny sbírky lze nalézt <a %(a_href)s>na webu libgenu</a>. Torrenty jsou dostupné pro většinu dalšího obsahu, zejména torrenty pro komiksy, časopisy a standardní dokumenty byly vydány ve spolupráci s Anniným archivem. Všimněte si, že torrentové soubory odkazující na „libgen.is“ jsou výslovně zrcadlením <a %(a_libgen)s>Libgen.rs</a> („.is“ je jiná doména používaná Libgen.rs). Užitečným zdrojem pro používání metadata je <a %(a_href)s>tato stránka</a>. %(icon)s Jejich sbírka „fiction_rus“ (ruská beletrie) nemá vlastní torrenty, ale je pokryta torrenty od jiných, a my udržujeme <a %(fiction_rus)s>zrcadlení</a>. Torrenty ruské beletrie na Annině archivu Torrenty beletrie na Annině archivu Diskusní fórum Metadata Metadata přes FTP Torrenty časopisů na Annině archivu Informace o polích metadata Zrcadlení jiných torrentů (a unikátní torrenty beletrie a komiksů) Torrenty standardních dokumentů na Annině archivu Libgen.li Torrenty od Annina archivu (obálky knih) Library Genesis je známá tím, že již velkoryse zpřístupňuje svá data hromadně prostřednictvím torrentů. Naše sbírka Libgen se skládá z pomocných dat, která přímo neuvolňují, ve spolupráci s nimi. Velké díky všem zapojeným do Library Genesis za spolupráci s námi! Náš blog o vydání obálek knih Tato stránka je o verzi „.rs“. Je známá tím, že konzistentně publikuje jak svá metadata, tak plný obsah svého katalogu knih. Její sbírka knih je rozdělena na část beletrie a nebeletrie. Užitečným zdrojem pro používání metadata je <a %(a_metadata)s>tato stránka</a> (blokuje IP rozsahy, může být vyžadováno použití VPN). Od března 2024 jsou nové torrenty zveřejňovány v <a %(a_href)s>tomto vláknu fóra</a> (blokuje rozsahy IP, může být vyžadován VPN). Fiction torrenty na Annině archivu Fiction torrenty Libgen.rs Diskusní fórum Libgen.rs Libgen.rs Metadata Informace o polích metadat Libgen.rs Non-fiction torrenty Libgen.rs Non-fiction torrenty na Annině archivu %(example)s pro fiction knihu. Toto <a %(blog_post)s>první vydání</a> je poměrně malé: asi 300 GB obálek knih z forku Libgen.rs, jak fiction, tak non-fiction. Jsou organizovány stejným způsobem, jak se objevují na libgen.rs, např.: %(example)s pro non-fiction knihu. Stejně jako u sbírky Z-Library jsme je všechny umístili do velkého .tar souboru, který lze připojit pomocí <a %(a_ratarmount)s>ratarmount</a>, pokud chcete soubory přímo servírovat. Vydání 1 (%(date)s) Rychlý příběh o různých větvích Library Genesis (nebo „Libgen“) je, že postupem času se různí lidé zapojení do Library Genesis pohádali a šli každý svou cestou. Podle tohoto <a %(a_mhut)s>příspěvku na fóru</a> byl Libgen.li původně hostován na „http://free-books.dontexist.com“. Verze „.fun“ byla vytvořena původním zakladatelem. Je přepracovávána ve prospěch nové, více distribuované verze. <a %(a_li)s>Verze „.li“</a> má obrovskou sbírku komiksů, stejně jako další obsah, který (zatím) není k dispozici ke stažení ve velkých torrentových balíčcích. Má samostatnou torrentovou sbírku beletristických knih a obsahuje metadata <a %(a_scihub)s>Sci-Hub</a> ve své databázi. Verze „.rs“ má velmi podobná data a nejkonzistentněji vydává svou sbírku ve velkých torrentových balíčcích. Je zhruba rozdělena na sekci „beletrie“ a „nebeletrie“. Původně na „http://gen.lib.rus.ec“. <a %(a_zlib)s>Z-Library</a> je v jistém smyslu také větví Library Genesis, i když pro svůj projekt použili jiné jméno. Libgen.rs Také obohacujeme naši sbírku o zdroje pouze s metadata, které můžeme přiřadit k souborům, např. pomocí ISBN čísel nebo jiných polí. Níže je přehled těchto zdrojů. Opět, některé z těchto zdrojů jsou zcela otevřené, zatímco jiné musíme skenovat. Všimněte si, že při vyhledávání v metadata zobrazujeme původní záznamy. Neprovádíme žádné slučování záznamů. Pouze metadata Open Library je open source projekt Internet Archive, jehož cílem je katalogizovat každou knihu na světě. Má jednu z největších operací skenování knih na světě a mnoho knih je k dispozici pro digitální půjčování. Jeho katalog metadat knih je volně dostupný ke stažení a je zahrnut v Annině archivu (i když aktuálně není v hledání, kromě případů, kdy explicitně hledáte Open Library ID). Open Library Bez duplicit Naposledy aktualizováno Procenta počtu souborů %% zrcadleno AA / torrenty dostupné Velikost Zdroj Níže je rychlý přehled zdrojů souborů na Annině archivu. Protože stínové knihovny často synchronizují data mezi sebou, dochází k značnému překryvu mezi knihovnami. Proto se čísla nesčítají do celkového počtu. Procento „zrcadleno a seedováno Anniným archivem“ ukazuje, kolik souborů zrcadlíme sami. Tyto soubory seedujeme hromadně prostřednictvím torrentů a zpřístupňujeme je ke stažení přímo přes partnerské weby. Přehled Celkem Torrenty na Annině archivu Pro více informací o Sci-Hub navštivte jeho <a %(a_scihub)s>oficiální webové stránky</a>, <a %(a_wikipedia)s>stránku na Wikipedii</a> a tento <a %(a_radiolab)s>podcastový rozhovor</a>. Všimněte si, že Sci-Hub byl <a %(a_reddit)s>zmrazen od roku 2021</a>. Byl zmrazen již dříve, ale v roce 2021 bylo přidáno několik milionů článků. Přesto se do kolekcí „scimag“ na Libgen stále přidává omezený počet článků, ale ne dost na to, aby to ospravedlnilo nové hromadné torrenty. Používáme metadata Sci-Hub, jak je poskytuje <a %(a_libgen_li)s>Libgen.li</a> ve své kolekci „scimag“. Také používáme dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Všimněte si, že „smarch“ torrenty jsou <a %(a_smarch)s>zastaralé</a> a proto nejsou zahrnuty v našem seznamu torrentů. Torrenty na Libgen.li Torrenty na Libgen.rs Metadata a torrenty Aktualizace na Redditu Podcastový rozhovor Stránka na Wikipedii Sci-Hub Sci-Hub: zmrazeno od roku 2021; většina dostupná přes torrenty Libgen.li: menší přírůstky od té doby</div> Některé zdrojové knihovny podporují hromadné sdílení svých dat prostřednictvím torrentů, zatímco jiné svou sbírku snadno nesdílejí. V druhém případě se Annin archiv snaží jejich sbírky skenovat a zpřístupnit (viz naši stránku <a %(a_torrents)s>Torrenty</a>). Existují také mezistavy, například když jsou zdrojové knihovny ochotné sdílet, ale nemají na to prostředky. V těchto případech se také snažíme pomoci. Níže je přehled toho, jak komunikujeme s různými zdrojovými knihovnami. Zdrojové knihovny %(icon)s Různé databáze souborů roztroušené po čínském internetu; často však placené databáze %(icon)s Většina souborů je přístupná pouze pomocí prémiových účtů BaiduYun; pomalé rychlosti stahování. %(icon)s Annin archiv spravuje sbírku <a %(duxiu)s>DuXiu souborů</a> %(icon)s Různé metadata databáze roztroušené po čínském internetu; často placené databáze %(icon)s Žádné snadno dostupné metadata dumpy pro celou jejich sbírku. %(icon)s Annin archiv spravuje sbírku <a %(duxiu)s>DuXiu metadata</a> Soubory %(icon)s Soubory jsou dostupné k půjčení pouze omezeně, s různými omezeními přístupu %(icon)s Annin archiv spravuje sbírku <a %(ia)s>IA souborů</a> %(icon)s Některá metadata jsou dostupná prostřednictvím <a %(openlib)s>Open Library výpisů databáze</a>, ale ty nepokrývají celou sbírku IA %(icon)s Žádné snadno dostupné výpisy metadata pro celou jejich sbírku %(icon)s Annin archiv spravuje sbírku <a %(ia)s>IA metadata</a> Naposledy aktualizováno %(icon)s Annin archiv a Libgen.li společně spravují sbírky <a %(comics)s>komiksů</a>, <a %(magazines)s>časopisů</a>, <a %(standarts)s>standardních dokumentů</a> a <a %(fiction)s>beletrie (odlišné od Libgen.rs)</a>. %(icon)s Non-fiction torrenty jsou sdíleny s Libgen.rs (a zrcadleny <a %(libgenli)s>zde</a>). %(icon)s Čtvrtletní <a %(dbdumps)s>HTTP výpisy databáze</a> %(icon)s Automatizované torrenty pro <a %(nonfiction)s>non-fiction</a> a <a %(fiction)s>fiction</a> %(icon)s Annin archiv spravuje sbírku <a %(covers)s>torrenty obálek knih</a> %(icon)s Denní <a %(dbdumps)s>HTTP výpisy databáze</a> Metadata %(icon)s Měsíční <a %(dbdumps)s>databázové dumpy</a> %(icon)s Datové torrenty jsou dostupné <a %(scihub1)s>zde</a>, <a %(scihub2)s>zde</a> a <a %(libgenli)s>zde</a> %(icon)s Některé nové soubory jsou <a %(libgenrs)s>přidávány</a> do Libgen’s „scimag“, ale ne dost na to, aby to ospravedlnilo nové torrenty %(icon)s Sci-Hub od roku 2021 nezveřejňuje nové soubory. %(icon)s Metadata výpisy jsou dostupné <a %(scihub1)s>zde</a> a <a %(scihub2)s>zde</a>, stejně jako součást <a %(libgenli)s>Libgen.li databáze</a> (kterou používáme) Zdroj %(icon)s Různé menší nebo jednorázové zdroje. Doporučujeme lidem nahrávat nejprve do jiných stínových knihoven, ale někdy mají lidé sbírky, které jsou příliš velké na to, aby je ostatní mohli procházet, ale ne dost velké na to, aby si zasloužily vlastní kategorii. %(icon)s Není dostupné přímo ve velkém množství, chráněné proti scraping %(icon)s Annin archiv spravuje sbírku <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Annin archiv a Z-Library společně spravují sbírku <a %(metadata)s>Z-Library metadata</a> a <a %(files)s>Z-Library souborů</a> Datasets Kombinujeme všechny výše uvedené zdroje do jedné sjednocené databáze, kterou používáme k provozu této webové stránky. Tato sjednocená databáze není přímo dostupná, ale protože Annin archiv je plně open source, může být poměrně snadno <a %(a_generated)s>vygenerována</a> nebo <a %(a_downloaded)s>stažena</a> jako databáze ElasticSearch a MariaDB. Skripty na této stránce automaticky stáhnou všechna potřebná metadata z výše uvedených zdrojů. Pokud byste chtěli prozkoumat naše data před spuštěním těchto skriptů lokálně, můžete se podívat na naše JSON soubory, které odkazují na další JSON soubory. <a %(a_json)s>Tento soubor</a> je dobrým výchozím bodem. Sjednocená databáze Torrenty od Annina archivu procházet hledat Různé menší nebo jednorázové zdroje. Doporučujeme lidem nahrávat nejprve do jiných stínových knihoven, ale někdy mají lidé sbírky, které jsou příliš velké na to, aby je ostatní mohli procházet, ale ne dost velké na to, aby si zasloužily vlastní kategorii. Přehled z <a %(a1)s>stránky datasets</a>. Z <a %(a_href)s>aaaaarg.fail</a>. Zdá se, že je poměrně kompletní. Od našeho dobrovolníka „cgiym“. Z <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrentu. Má poměrně vysoký překryv s existujícími sbírkami článků, ale velmi málo shod MD5, takže jsme se rozhodli ponechat ji kompletní. Scrape z <q>iRead eBooks</q> (= foneticky <q>ai rit i-books</q>; airitibooks.com), od dobrovolníka <q>j</q>. Odpovídá <q>airitibooks</q> metadata v <a %(a1)s><q>Jiné metadata scrapes</q></a>. Z kolekce <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Částečně z původního zdroje, částečně z the-eye.eu, částečně z jiných zrcadel. Z privátního webu s torrentovými knihami, <a %(a_href)s>Bibliotik</a> (často označovaného jako „Bib“), jehož knihy byly seskupeny do torrentů podle jména (A.torrent, B.torrent) a distribuovány přes the-eye.eu. Od našeho dobrovolníka „bpb9v“. Pro více informací o <a %(a_href)s>CADAL</a> viz poznámky na naší <a %(a_duxiu)s>stránce datasetu DuXiu</a>. Další od našeho dobrovolníka „bpb9v“, převážně soubory DuXiu, stejně jako složky „WenQu“ a „SuperStar_Journals“ (SuperStar je společnost stojící za DuXiu). Od našeho dobrovolníka „cgiym“, čínské texty z různých zdrojů (reprezentované jako podadresáře), včetně z <a %(a_href)s>China Machine Press</a> (významný čínský vydavatel). Nečínské sbírky (reprezentované jako podadresáře) od našeho dobrovolníka „cgiym“. Scrape knih o čínské architektuře, od dobrovolníka <q>cm</q>: <q>Získal jsem to využitím zranitelnosti sítě v nakladatelství, ale tato mezera byla od té doby uzavřena</q>. Odpovídá <q>chinese_architecture</q> metadata v <a %(a1)s><q>Jiné metadata scrapes</q></a>. Knihy z akademického vydavatelství <a %(a_href)s>De Gruyter</a>, sesbírané z několika velkých torrentů. Scrape z <a %(a_href)s>docer.pl</a>, polského webu pro sdílení souborů zaměřeného na knihy a další písemné práce. Scrape provedl dobrovolník „p“ koncem roku 2023. Nemáme dobrá metadata z původního webu (ani přípony souborů), ale filtrovali jsme soubory podobné knihám a často jsme byli schopni extrahovat metadata přímo ze souborů. DuXiu epuby, přímo z DuXiu, sesbírané dobrovolníkem „w“. Pouze nedávné knihy DuXiu jsou dostupné přímo přes e-knihy, takže většina z nich musí být nedávná. Zbývající soubory DuXiu od dobrovolníka „m“, které nebyly ve vlastním formátu PDG DuXiu (hlavní <a %(a_href)s>dataset DuXiu</a>). Sesbírané z mnoha původních zdrojů, bohužel bez zachování těchto zdrojů v cestě souboru. <span></span> <span></span> <span></span> Scrape erotických knih, od dobrovolníka <q>do no harm</q>. Odpovídá <q>hentai</q> metadata v <a %(a1)s><q>Jiné metadata scrapes</q></a>. <span></span> <span></span> Sbírka scrape z japonského vydavatele Manga od dobrovolníka „t“. <a %(a_href)s>Vybrané soudní archivy Longquan</a>, poskytnuté dobrovolníkem „c“. Scrape z <a %(a_href)s>magzdb.org</a>, spojence Library Genesis (je propojen na domovské stránce libgen.rs), ale který nechtěl poskytnout své soubory přímo. Získal dobrovolník „p“ koncem roku 2023. <span></span> Různé malé nahrávky, příliš malé na to, aby tvořily vlastní podsbírku, ale reprezentované jako adresáře. E-knihy z AvaxHome, ruské webové stránky pro sdílení souborů. Archiv novin a časopisů. Odpovídá <q>newsarch_magz</q> metadata v <a %(a1)s><q>Jiné metadata scrapes</q></a>. Scrape z <a %(a1)s>Philosophy Documentation Center</a>. Sbírka dobrovolníka „o“, který sesbíral polské knihy přímo z původních release („scene“) webů. Kombinované sbírky <a %(a_href)s>shuge.org</a> od dobrovolníků „cgiym“ a „woz9ts“. <span></span> <a %(a_href)s>„Imperiální knihovna Trantoru“</a> (pojmenovaná po fiktivní knihovně), scrape provedl v roce 2022 dobrovolník „t“. <span></span> <span></span> <span></span> Pod-pod-sbírky (reprezentované jako adresáře) od dobrovolníka „woz9ts“: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (od <a %(a_sikuquanshu)s>Dizhi(迪志)</a> na Tchaj-wanu), mebook (mebook.cc, 我的小书屋, můj malý knihovní pokoj — woz9ts: „Tento web se zaměřuje hlavně na sdílení vysoce kvalitních e-knih, z nichž některé jsou vysázeny samotným majitelem. Majitel byl <a %(a_arrested)s>zatčen</a> v roce 2019 a někdo vytvořil sbírku souborů, které sdílel.“). Zbývající soubory DuXiu od dobrovolníka „woz9ts“, které nebyly ve vlastním formátu PDG DuXiu (ještě je třeba převést na PDF). Sbírka „nahrávky“ je rozdělena do menších podsbírek, které jsou označeny v AACID a názvech torrentů. Všechny podsbírky byly nejprve deduplikovány proti hlavní sbírce, i když metadata „upload_records“ JSON soubory stále obsahují mnoho odkazů na původní soubory. Nepublikované soubory byly také odstraněny z většiny podsbírek a obvykle nejsou uvedeny v „upload_records“ JSON. Podsbírky jsou: Poznámky Podkolekce Mnoho podsbírek samotných se skládá z pod-podsbírek (např. z různých původních zdrojů), které jsou reprezentovány jako adresáře v polích „filepath“. Nahrávky do Annina archivu Náš blogový příspěvek o těchto datech <a %(a_worldcat)s>WorldCat</a> je proprietární databáze neziskové organizace <a %(a_oclc)s>OCLC</a>, která agreguje záznamy metadata z knihoven po celém světě. Pravděpodobně se jedná o největší sbírku knihovních metadata na světě. V říjnu 2023 jsme <a %(a_scrape)s>vydali</a> komplexní scrape databáze OCLC (WorldCat) ve formátu <a %(a_aac)s>Anna’s Archive Containers</a>. Říjen 2023, počáteční vydání: OCLC (WorldCat) Torrenty od Annina archivu Ukázkový záznam v Annině archivu (původní sbírka) Ukázkový záznam v Annině archivu (sbírka „zlib3“) Torrenty od Annina archivu (metadata + obsah) Blogový příspěvek o vydání 1 Blogový příspěvek o vydání 2 Koncem roku 2022 byli údajní zakladatelé Z-Library zatčeni a domény byly zabaveny americkými úřady. Od té doby se web pomalu vrací online. Není známo, kdo jej v současnosti provozuje. Aktualizace k únoru 2023. Z-Library má své kořeny v komunitě <a %(a_href)s>Library Genesis</a> a původně byla založena s jejich daty. Od té doby se značně profesionalizovala a má mnohem modernější rozhraní. Díky tomu jsou schopni získat mnohem více darů, jak finančních na další vylepšování jejich webu, tak i darů nových knih. Nashromáždili velkou sbírku navíc k Library Genesis. Sbírka se skládá ze tří částí. Původní popisné stránky pro první dvě části jsou zachovány níže. Potřebujete všechny tři části, abyste získali všechna data (kromě nahrazených torrentů, které jsou přeškrtnuté na stránce torrentů). %(title)s: naše první vydání. Toto bylo úplně první vydání toho, co se tehdy nazývalo „Pirate Library Mirror“ („pilimi“). %(title)s: druhé vydání, tentokrát se všemi soubory zabalenými v .tar souborech. %(title)s: inkrementální nová vydání, používající <a %(a_href)s>formát Anna’s Archive Containers (AAC)</a>, nyní vydávaná ve spolupráci s týmem Z-Library. Počáteční zrcadlení bylo pečlivě získáváno v průběhu let 2021 a 2022. V tuto chvíli je mírně zastaralé: odráží stav sbírky v červnu 2021. V budoucnu to aktualizujeme. Právě teď se soustředíme na vydání tohoto prvního vydání. Protože je Library Genesis již zachována pomocí veřejných torrentů a je zahrnuta v Z-Library, provedli jsme základní deduplikaci proti Library Genesis v červnu 2022. K tomu jsme použili MD5 hashe. Pravděpodobně je v knihovně mnohem více duplicitního obsahu, jako jsou různé formáty souborů se stejnou knihou. To je těžké přesně detekovat, takže to neděláme. Po deduplikaci nám zůstalo přes 2 miliony souborů, celkem těsně pod 7TB. Kolekce se skládá ze dvou částí: MySQL „.sql.gz“ dumpu metadat a 72 torrentových souborů o velikosti kolem 50-100GB každý. Metadata obsahují data, jak je uvádí web Z-Library (název, autor, popis, typ souboru), stejně jako skutečnou velikost souboru a md5sum, které jsme pozorovali, protože někdy se tyto údaje neshodují. Zdá se, že existují rozsahy souborů, pro které má samotná Z-Library nesprávná metadata. V některých izolovaných případech jsme také mohli stáhnout soubory nesprávně, což se pokusíme v budoucnu detekovat a opravit. Velké torrentové soubory obsahují skutečná data knih, s ID Z-Library jako názvem souboru. Přípony souborů lze rekonstruovat pomocí dumpu metadat. Kolekce je směsicí obsahu beletrie a naučné literatury (není oddělena jako v Library Genesis). Kvalita je také velmi různorodá. Toto první vydání je nyní plně dostupné. Upozorňujeme, že torrentové soubory jsou dostupné pouze prostřednictvím našeho Tor zrcadlení. Vydání 1 (%(date)s) Jedná se o jediný extra torrentový soubor. Neobsahuje žádné nové informace, ale má v sobě některá data, jejichž výpočet může chvíli trvat. To je výhodné mít, protože stažení tohoto torrentu je často rychlejší než jeho výpočet od začátku. Zejména obsahuje indexy SQLite pro tar soubory, pro použití s <a %(a_href)s>ratarmount</a>. Dodatek k vydání 2 (%(date)s) Získali jsme všechny knihy, které byly přidány do Z-Library mezi naším posledním zrcadlením a srpnem 2022. Také jsme se vrátili a stáhli některé knihy, které jsme poprvé vynechali. Celkově je tato nová kolekce asi 24TB. Opět je tato kolekce deduplikována proti Library Genesis, protože pro tuto kolekci jsou již dostupné torrenty. Data jsou organizována podobně jako v prvním vydání. Je zde MySQL „.sql.gz“ dump metadat, který také zahrnuje všechna metadata z prvního vydání, čímž jej nahrazuje. Přidali jsme také několik nových sloupců: Zmínili jsme to posledně, ale jen pro upřesnění: „filename“ a „md5“ jsou skutečné vlastnosti souboru, zatímco „filename_reported“ a „md5_reported“ jsou to, co jsme stáhli ze Z-Library. Někdy se tyto dvě neshodují, takže jsme zahrnuli obě. Pro toto vydání jsme změnili kolaci na „utf8mb4_unicode_ci“, což by mělo být kompatibilní se staršími verzemi MySQL. Datové soubory jsou podobné jako minule, i když jsou mnohem větší. Prostě jsme se neobtěžovali vytvářet spoustu menších torrentových souborů. „pilimi-zlib2-0-14679999-extra.torrent“ obsahuje všechny soubory, které jsme minule vynechali, zatímco ostatní torrenty jsou všechny nové rozsahy ID.  <strong>Aktualizace %(date)s:</strong> Vytvořili jsme většinu našich torrentů příliš velkých, což způsobilo problémy torrentovým klientům. Odstranili jsme je a vydali nové torrenty. <strong>Aktualizace %(date)s:</strong> Stále bylo příliš mnoho souborů, takže jsme je zabalili do tar souborů a znovu vydali nové torrenty. %(key)s: zda je tento soubor již v Library Genesis, buď v beletrii nebo naučné literatuře (shoduje se podle md5). %(key)s: ve kterém torrentu se tento soubor nachází. %(key)s: nastaveno, když jsme nebyli schopni stáhnout knihu. Vydání 2 (%(date)s) Zlib vydání (původní popisné stránky) Tor doména Hlavní webová stránka Z-Library scrape Sbírka „čínských“ knih v Z-Library se zdá být stejná jako naše sbírka DuXiu, ale s různými MD5. Tyto soubory vylučujeme z torrentů, abychom se vyhnuli duplicitám, ale stále je zobrazujeme v našem vyhledávacím indexu. Metadata Dostáváte %(percentage)s%% bonusových rychlých stažení, protože jste byli doporučeni od %(profile_link)s. Toto se vztahuje na celé období předplatného. Přispět Připojit se Vybráno slevy až %(percentage)s %% Alipay podporuje mezinárodní kreditní/debetní karty. Více informací naleznete v <a %(a_alipay)s>tomto průvodci</a>. Pošlete nám dárkové karty Amazon.com pomocí své kreditní/debetní karty. Kryptoměny můžete nakoupit pomocí kreditních/debetních karet. WeChat (Weixin Pay) podporuje mezinárodní kreditní/debetní karty. V aplikaci WeChat přejděte do „Me => Services => Wallet => Add a Card“. Pokud ji nevidíte, povolte ji pomocí „Me => Settings => General => Tools => Weixin Pay => Enable“. (použijte při odesílání Etherea z Coinbase) zkopírováno! kopírovat (nejnižší minimální částka) (upozornění: vysoká minimální částka) -%(percentage)s%% 12 měsíců 1 měsíc 24 měsíců 3 měsíce 48 měsíců 6 měsíců 96 měsíců Vyberte délku předplatného. <div %(div_monthly_cost)s></div><div %(div_after)s>po <span %(span_discount)s></span> slevě</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% za 12 měsíců za 1 měsíc za 24 měsíců za 3 měsíce za 48 měsíců za 6 měsíců za 96 měsíců %(monthly_cost)s / měsíc kontaktujte nás Přímé <strong>SFTP</strong> servery Dary na podnikové úrovni nebo výměna za nové sbírky (např. nové skeny, OCR datasetů). Expertní přístup <strong>Neomezený</strong> vysokorychlostní přístup <div %(div_question)s>Mohu upgradovat své členství nebo získat více členství?</div> <div %(div_question)s>Můžu přispět, aniž bych se stal členem?</div> Jistě. Na této Monero adrese (XMR) přijímáme dary v jakékoli výši: %(address)s. <div %(div_question)s>Co znamenají rozsahy za měsíc?</div> Na nižší stranu rozsahu se můžete dostat použitím všech slev, jako je výběr období delšího než měsíc. <div %(div_question)s>Obnovuje se členství automaticky?</div> Členství se automaticky <strong>neobnovuje</strong>. Členy budete pouze po dobu, kterou si sami zvolíte. <div %(div_question)s>Na co jsou dary použity?</div> 100%% darů jde na uchovávání a zpřístupňování celosvětových znalostí a kultury. V současnosti je většina použita na financování serverů, úložišť a přenosu dat. Členové našeho týmu nijak placeni nejsou. <div %(div_question)s>Je možné darovat velký obnos peněz?</div> To by bylo skvělé! Pokud plánujete darovat více než několik tisíc dolarů, kontaktujte nás prosím na %(email)s. <div %(div_question)s>Přijímáte jiné způsoby platby?</div> V současnosti bohužel ne. Různé skupiny lidí se snaží, aby archivy, jako je ten náš, neexistovaly, a tak musíme být velmi opatrní. Pokud byste byli ochotní nám pomoci s bezpečným použitím jiných (pohodlnějších) způsobů platby, neváhejte nás kontaktovat na %(email)s. Nejčastěji kladené dotazy - dary Máte <a %(a_donation)s>nedokončený dar</a>. Prosím, dokončete nebo zrušte tento dar před posláním nového. <a %(a_all_donations)s>Zobrazit všechny moje dary</a> Pro dary nad 5000 dolarů nás prosím kontaktujte přímo na %(email)s. Vítáme velké dary od velkorysých jednotlivců nebo institucí.  Upozorňujeme, že i když jsou členství na této stránce „za měsíc“, jedná se o jednorázové dary (neopakující se). Viz <a %(faq)s>Nejčastěji kladené dotazy - dary</a>. Annin archiv je neziskový, open-source, open-data projekt. Příspěvkem a členstvím podporujete náš provoz a rozvoj. Všem našim členům: děkujeme, že Annin archiv udržujete v chodu! ❤️ Další informace naleznete v <a %(a_donate)s>častých dotazech k přispívání</a>. Chcete-li se stát členem, prosíme, <a %(a_login)s>přihlaste se nebo se zaregistrujte</a>. Děkujeme za vaši podporu! %(cost)s dolarů / měsíc Pokud jste udělali chybu během platby, peníze vám vrátit nedokážeme. Pokusíme se však vše napravit. V aplikaci nebo na webu PayPal najděte záložku "Kryptoměny" ("Crypto"). Ta většinou bývá pod záložkou "Finance". V aplikaci nebo na webu PayPal přejděte na záložku "Bitcoin". Klikněte na tlačítko "Přesunout" %(transfer_icon)s, a poté na "Poslat". Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s dárková karta Platební karta Platební karta (využití aplikace) Binance Kreditní karta/debetní karta/Apple/Google (BMC) Cash App Kreditní/debetní karta Kreditní/debetní karta 2 Kreditní/debetní karta (záloha) Krypto %(bitcoin_icon)s Karta / PayPal / Venmo PayPal (USA)%(bitcoin_icon)s PayPal PayPal (běžný) Pix (Brazílie) Revolut (dočasně nedostupné) WeChat Vyberte svou preferovanou kryptoměnu: Přispět pomocí dárkových karet Amazon. <strong>DŮLEŽITÉ:</strong> Tato možnost je pro %(amazon)s. Pokud chcete použít jinou webovou stránku Amazonu, vyberte ji výše. <strong>DŮLEŽITÉ:</strong> Podporujeme pouze Amazon.com, nikoliv jiné stránky Amazonu. Například .de, .co.uk, .ca NEJSOU podporovány. Nepište, prosím, vlastní zprávu. Zadejte přesnou částku: %(amount)s Berte v potaz, že musíme zaokrouhlit částky na hodnoty příjmané našimi prodejci (minimum %(minimum)s). Darujte pomocí kreditní/debetní karty prostřednictvím aplikace Alipay (snadné nastavit). Nainstalujte aplikaci Alipay z <a %(a_app_store)s>Apple App Store</a> nebo <a %(a_play_store)s>Google Play Store</a>. Zaregistrujte se pomocí svého telefonního čísla. Nejsou vyžadovány žádné další osobní údaje. <span %(style)s>1</span>Nainstalujte aplikaci Alipay Podporováno: Visa, MasterCard, JCB, Diners Club a Discover. Pro více informací si přečtěte <a %(a_alipay)s>tento průvodce</a>. <span %(style)s>2</span>Přidejte bankovní kartu Na Binance nakoupíte bitcoiny kreditní/debetní kartou nebo z bankovního účtu a pak nám je darujete. Tímto způsobem můžeme při přijímání vašeho daru zůstat v bezpečí a anonymitě. Binance je dostupná téměř ve všech zemích a podporuje většinu bank a kreditních/debetních karet. V současné chvíli je to naše primární doporučení. Vážíme si, že jste si udělali čas a naučili se darovat touto metodou, neboť nám to nesmírně pomáhá. Pro kreditní a debetní karty, Apple Pay a Google Pay používáme službu „Buy Me a Coffee“ (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). V jejich systému se jedna „káva“ rovná 5 dolarům, takže váš dar bude zaokrouhlen na nejbližší násobek 5. Přispět pomocí Cash App. Pokud máte Cash App, toto je nejjednodušší způsob, jak přispět! Vezměte na vědomí, že při transakcích pod %(amount)s vám může Cash App účtovat poplatky %(fee)s. Pro %(amount)s a více je transakce zdarma! Přispět pomocí kreditní nebo debetní karty. Tato metoda využívá poskytovatele kryptoměn jako zprostředkovatele konverze. To může být trochu matoucí, proto tuto metodu používejte pouze v případě, že jiné platební metody nefungují. Tato metoda nemusí fungovat ve všech zemích. Nemůžeme přímo podporovat kreditní/debetní karty, protože banky s námi nechtějí spolupracovat. ☹ Nicméně existuje několik způsobů, jak použít kreditní/debetní karty prostřednictvím jiných platebních metod: Pomocí kryptoměn můžete přispívat pomocí BTC, ETH, XMR a SOL. Tuto možnost použijte, pokud jste s kryptoměnami již obeznámeni. Pomocí kryptoměn můžete přispívat prostřednictvím BTC, ETH, XMR a dalších. Krypto expresní služby Pokud platíte kryptoměnami poprvé, doporučujeme k nákupu a darování Bitcoinu (původní a nejpoužívanější kryptoměna) použít %(options)s. Upozorňujeme, že u malých darů mohou poplatky za kreditní kartu eliminovat naši slevu %(discount)s%%, proto doporučujeme delší předplatné. Přispějte pomocí kreditní/debetní karty, služby PayPal nebo Venmo. Na další stránce si mezi nimi můžete vybrat. Fungovat může i Google Pay a Apple Pay. Upozorňujeme, že v případě malých příspěvků jsou poplatky vysoké, proto doporučujeme delší předplatné. Pro dary pomocí služby PayPal používáme službu PayPal Crypto, která nám umožňuje zůstat v anonymitě. Vážíme si toho, že jste si našli čas a naučili se, jak přispívat touto metodou, protože nám to velmi pomáhá. Přispět pomocí PayPal. Darujte pomocí svého běžného účtu PayPal. Darujte pomocí Revolutu. Pokud máte Revolut, je to nejjednodušší způsob jak darovat! Tento způsob platby dovoluje čásku maximálně %(amount)s. Prosíme vyberte jiné trvání nebo způsob platby. Tento způsob platby vyžaduje čásku nejméně %(amount)s. Prosíme vyberte jiné trvání nebo způsob platby. Binance Coinbase Kraken Prosím, vyberte si způsob platby. „Adoptuj si torrent“: vaše uživatelské jméno nebo zpráva v názvu torrentu <div %(div_months)s>každých 12 měsíců členství</div> Vaše uživatelské jméno nebo anonymní zmínka v poděkování Předčasný přístup k novým funkcím Přístup do exkluzivního Telegramu s updaty ze zákulisí %(number)s rychlých stažení za den pokud tento měsíc přispějete! přístup k <a %(a_api)s>JSON API</a> Legendární status v zachovávání lidských vědomostí a kultury Předchozí výhody a: Získejte <strong>%(percentage)s%% bonusových stažení</strong><a %(a_refer)s> doporučením vašim přátelům</a>. SciDB články <strong>neomezeně</strong> a bez ověření K dotazům týkajícím se účtu nebo darů přidejte ID účtu, snímky obrazovky, účtenky a co nejvíce informací. E-mail kontrolujeme pouze jednou za 1-2 týdny a neuvedení těchto informací zdrží případné řešení. Chcete-li získat ještě více stažení, <a %(a_refer)s>pozvěte své přátele</a>! Jsme malý tým dobrovolníku. Může nám trvat 1-2 týdny, než odpovíme. Přestože název účtu a jeho ikona mohou vypadat zvláštně, nebojte se, naše účty nebyly napadeny. Tyto účty jsou spravovány našimi dárcovskými partnery. Darovat <span %(span_cost)s></span> <span %(span_label)s></span> za 12 měsíců “%(tier_name)s” za 1 měsíc “%(tier_name)s” za 24 měsíců “%(tier_name)s” za 3 měsíce “%(tier_name)s” za 48 měsíců “%(tier_name)s” za 6 měsíců “%(tier_name)s” za 96 měsíců “%(tier_name)s” Poslání daru můžete stále zrušit během platby. Pro dokončení klikněte na tlačítko Darovat. <strong>Důležitá poznámka:</strong> Ceny kryptoměn mohou prudce kolísat, někdy i o 20%% během několika minut. To je stále méně než poplatky, které nám vznikají u mnoha poskytovatelů plateb, kteří si často účtují 50-60%% za spolupráci se „stínovou charitou“, jako jsme my. <u>Pokud nám zašlete účtenku s původní cenou, kterou jste zaplatili, stále vám vybrané členství připíšeme na účet</u> (pokud účtenka není starší více než několik hodin). Opravdu si vážíme toho, že jste ochotni strpět takové věci, abyste nás podpořili! ❤️ ❌ Něco se pokazilo. Znovu načtěte stránku a zkuste to znovu. <span %(span_circle)s>1</span>Zakupte Bitcoin skrze Paypal <span %(span_circle)s>2</span>Převeďte Bitcoiny na naši adresu ✅ Přesměrování na stránku dárcovství… Přispět Předtím, než nás kontaktujete, počkejte prosím alespoň <span %(span_hours)s>24 hodin</span> (a obnovte tuto stránku). Chcete-li přispět (jakoukoli částkou) bez členství, můžete použít tuto adresu Monero (XMR): %(address)s. Až pošlete svou dárkovou kartu, náš automatický systém ji potvrdí během několika minut. Pokud to nebude fungovat, zkuste dárkovou kartu znovu poslat (<a %(a_instr)s>instrukce</a>). Pokud to stále nefunguje, prosíme pošlete nám email a Anna ji ručně ověří (toto může trvat několik dní) a nezapomeňte zmínit, jestli jste se ji pokusili poslat znovu. Příklad: Prosíme použijte <a %(a_form)s>oficiální Amazon.com formulář</a>k zaslání dárkové karty o hodnotě %(amount)s na emailovou adresu níže. "Pro" příjemce emailu ve formuláři: Dárkové karty Amazon Nejsme schopni přijmout jiné způsoby nebo dárkové karty <strong>než poslané přímo z oficiálního formuláře na Amazon.com</strong>. Vaše dárkové karty vám nemůžeme vrátit, pokud tento formulář nepoužijete. Použít pouze jednou. Jedinečná pro váš účet, s nikým ji nesdílejte. Čekáme na dárkovou kartu… (Aktualizujte stránku pro kontrolu) Otevřete <a %(a_href)s>stránku darování pomocí QR kódu</a>. Naskenujte QR kód pomocí aplikace Alipay nebo stiskněte tlačítko pro otevření aplikace Alipay. Prosím, buďte trpěliví; stránka se může načítat déle, protože je v Číně. <span %(style)s>3</span>Proveďte dar (naskenujte QR kód nebo stiskněte tlačítko) Koupit PYUSD na PayPal Koupit Bitcoin (BTC) na Cash App Kupte o něco více (doporučujeme %(more)s více) než částku, kterou darujete (%(amount)s), abyste pokryli transakční poplatky. Zbytek si ponecháte. Přejděte na stránku „Bitcoin“ (BTC) v aplikaci Cash App. Převést Bitcoin na naši adresu Pro malé dary (pod $25) možná budete muset použít Rush nebo Priority. Klikněte na tlačítko „Odeslat bitcoin“ pro provedení „výběru“. Přepněte z dolarů na BTC stisknutím ikony %(icon)s. Zadejte níže uvedenou částku BTC a klikněte na „Odeslat“. Pokud se zaseknete, podívejte se na <a %(help_video)s>toto video</a>. Expresní služby jsou pohodlné, ale účtují vyšší poplatky. Můžete je použít místo krypto burzy, pokud chcete rychle provést větší dar a nevadí vám poplatek 5-10 $. Ujistěte se, že posíláte přesnou částku kryptoměny uvedenou na stránce darů, nikoli částku v $USD. Jinak bude poplatek odečten a nemůžeme automaticky zpracovat vaše členství. Někdy může potvrzení trvat až 24 hodin, proto nezapomeňte obnovit tuto stránku (i když vypršela). Instrukce pro kreditní / debetní kartu Přispějte pomocí naší stránky pro kreditní / debetní karty Některé kroky zmiňují peněženky pro kryptoměny, nemějte strach, nic o kryptoměnách se kvůli tomuto učit nemusíte. Instrukce k %(coin_name)s Prohledejte tento QR kód pomocí aplikace Crypto Peněženky, abyste rychle vyplnili platební údaje Skenujte QR kód a zaplatit Podporujeme pouze standardní verze kryptoměn, žádné exotické sítě nebo verze kryptoměn. Potvrzení může trvat až hodinu v závislosti na použité kryptoměně. Přispět %(amount)s na <a %(a_page)s>této stránce</a>. Tento příspěvek vypršel. Prosíme zrušte ho a vytvořte nový. Pokud jste již zaplatili: Ano, poslal jsem potvrzení emailem Pokud se směnný kurz kryptoměn v průběhu transakce výrazně měnil, přiložte prosím potvrzení s původním kurzem. Jsme vám vděční, že i přes všechny komplikace darujete v kryptoměnách, velice nám to pomáhá! ❌ Něco se pokazilo. Znovu načtěte stránku a zkuste to znovu. <span %(span_circle)s>%(circle_number)s</span> Potvrzení o platbě nám pošlete emailem Pokud narazíte na jakoukoliv chybu, prosíme kontaktujte nás na %(email)s a připojte co nejvíce informací (např. snímky obrazovky). ✅ Děkujeme za váš dar! Anna v průběhu několika dní ručně aktivuje vaše členství. Pošlete si potvrzení o platbě nebo snímek obrazovky na svou ověřovací adresu: Po zaslání platebního potvrzení klikněte na toto tlačítko, aby Anna mohla platbu zkontrolovat (což může trvat několik dní): Zašlete potvrzení nebo screenshot na vaši osobní ověřovací adresu. Nepoužívejte tuto e-mailovou adresu pro váš dar přes PayPal. Zrušit Ano, prosím, zrušit Jste si jistí, že chcete proces darování zrušit? Nerušte jej, pokud jste již zaplatili. ❌ Něco se pokazilo. Znovu načtěte stránku a zkuste to znovu. Provést nový dar ✅ Váš dar byl zrušen. Datum: %(date)s Identifikátor: %(id)s Obnovit Status: <span %(span_label)s>%(label)s</span> Celková částka: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / měsíc po dobu %(duration)s měsíců s %(discounts)s%% slevou)</span> Celková částka: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / měsíc po dobu %(duration)s měsíců)</span> 1. Zadejte svůj email. 2. Vyberte způsob platby. 3. Znovu vyberte svůj způsob platby. Zvolte peněženku "Self-hosted". 5. Klikněte na "Potvrzuji vlastnictví". 6. Účtenku byste měli obdržet emailem. Tu nám prosím zašlete a my potvrdíme váš dar co nejdříve to bude možné. (možná budete chtít zrušit a vytvořit nový příspěvek) Tyto pokyny k platbě již nejsou aktuální. Pokud chcete zaslat další dar, klikněte nejprve na tlačítko "Obnovit". Již jste zaplatili. Pokud i přesto chcete vidět pokyny k platbě, klikněte sem: Zobrazit staré pokyny k platbě Pokud je stránka s dary zablokována, zkuste jiné internetové připojení (např. VPN nebo internet v telefonu). Stránka Alipay je bohužel často přístupná pouze z <strong>pevninské Číny</strong>. Je možné, že budete muset dočasně deaktivovat svou VPN nebo použít VPN z pevninské Číny, popřípadě Hongkongu. <span %(span_circle)s>1</span>Darovat přes Alipay Darovat celkovou částku %(total)s pomocí <a %(a_account)s>tohoto účtu Alipay</a> Instrukce k platbě přes Alipay <span %(span_circle)s>1</span>Převést na jeden z našich účtů s kryptoměnami Darujte celkovou částku %(total)s na jednu z těchto adres: Instrukce k daru v kryptoměnách Následujte instrukce na zakoupení Bitcoinu (BTC). Stačí zakoupit množství, které chcete darovat, tedy %(total)s. Pro zaslání vašeho daru %(total)s zadejte naši Bitcoin (BTC) adresu jako příjemce a následujte instrukce: <span %(span_circle)s>1</span>Darovat skrze Pix Darujte celkovou částku %(total)s pomocí <a %(a_account)s>tohoto Pix účtu Instrukce pro platbu skrze Pix <span %(span_circle)s>1</span>Přispět pomocí WeChat Přispět celkovou částku %(total)s pomocí <a %(a_account)s>tohoto účtu WeChat</a> Pokyny k WeChat Použijte některou z následujících expresních služeb „kreditní karta na Bitcoin“, které trvají jen několik minut: BTC / Bitcoin adresa (externí peněženka): Částka BTC / Bitcoin: Vyplňte následující údaje ve formuláři: Pokud jsou některé z těchto informací zastaralé, napište nám prosím e-mail, abyste nás informovali. Použijte prosím tuto <span %(underline)s>přesnou částku</span>. Vaše celkové náklady mohou být vyšší kvůli poplatkům za kreditní kartu. U malých částek to může být bohužel více než naše sleva. (minimum: %(minimum)s, žádné ověření pro první transakci) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, žádné ověření pro první transakci) (minimum: %(minimum)s) (minimum: %(minimum)s v závislosti na zemi, žádné ověření pro první transakci) Následujte instrukce, jak zakoupit PYUSD (PayPal USD). Kupte o trochu více (my doporučujeme o %(more)s více) než se chystáte přispět (%(amount)s) k pokrytí poplatků za transakci. Všechen přebytek si ponecháte vy. Jděte na "PYUSD" stránku ve vaší PayPal aplikaci nebo na stránce. Klepněte na "Převod" %(icon)s a nakonec na "Poslat". Aktualizovat stav Chcete-li obnovit časovač, jednoduše vytvořte nový příspěvek. Ujistěte se, že použijete níže uvedenou částku v BTC, <em>NE</em> v eurech nebo dolarech, jinak neobdržíme správnou částku a nemůžeme automaticky potvrdit vaše členství. Koupit Bitcoin (BTC) na Revolut Kupte o něco více (doporučujeme %(more)s více) než částku, kterou darujete (%(amount)s), abyste pokryli transakční poplatky. Zbytek si ponecháte. Přejděte na stránku „Crypto“ v aplikaci Revolut a kupte Bitcoin (BTC). Převést Bitcoin na naši adresu Pro malé dary (pod $25) možná budete muset použít Rush nebo Priority. Klikněte na tlačítko „Odeslat bitcoin“ pro provedení „výběru“. Přepněte z eur na BTC stisknutím ikony %(icon)s. Zadejte níže uvedenou částku BTC a klikněte na „Odeslat“. Pokud se zaseknete, podívejte se na <a %(help_video)s>toto video</a>. Stav: 1 2 Návod krok po kroku Viz níže uvedený návod krok po kroku. V opačném případě by mohlo dojít k zablokování tohoto účtu! Pokud jste tak ještě neučinili, zapište si tajný klíč pro přihlášení: Děkujeme vám za váš příspěvek! Zbývající čas: Finanční dar Převést %(amount)s na %(account)s Čekáme na potvrzení (obnovte stránku pro kontrolu)… Čekáme na převod (obnovte stránku pro kontrolu)… Dříve Rychlá stahování v posledních 24 hodinách se počítají k dennímu limitu. Stažení z rychlých partnerských serverů jsou označeny %(icon)s. Posledních 18 hodin Zatím jste nestáhli žádné soubory. Stažené soubory nejsou veřejně přístupné. Všechny časy jsou v UTC. Stažené soubory Pokud jste si stáhli soubor pomocí rychlého i pomalého stahování, zobrazí se dvakrát. Nedělejte si příliš velké starosti, z námi odkazovaných webových stránek stahují spousty lidí a do problémů se dostanete jen velmi zřídka. Pro vyšší bezpečnost však doporučujeme využít VPN (placené), nebo <a %(a_tor)s>Tor</a> (zdarma). Stáhl jsem si 1984 od George Orwella, zaklepe mi na dveře policie? Vy jste Anna! Kdo je Anna? Pro naše podporovatele máme jedno stabilní API JSON k získání URL pro rychlé stahování: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentace v samotném JSON). Pro další případy použití, jako je iterace skrze všechny naše soubory, vytváření vlastního vyhledávání apod., doporučujeme <a %(a_generate)s>generovat</a> nebo <a %(a_download)s>stáhnout</a> naše databáze ElasticSearch a MariaDB. Raw data lze ručně prozkoumat <a %(a_explore)s>skrze soubory JSON</a>. Náš seznam raw torrentů si můžete stáhnout také jako <a %(a_torrents)s>JSON</a>. Máte API? Nehostujeme žádné materiály chráněné autorskými právy. Jsme vyhledávač, a proto indexujeme pouze metadata, která jsou již veřejně přístupná. V rámci stahování z externích zdrojů bychom doporučovali zkontrolovat zákony ve vaší jurisdikci. Nejsme zodpovědní za obsah hostovaný jinými subjekty. Pokud máte výhrady k obsahu Annina archivu, obraťte se nejlépe na původní webovou stránku, neboť jejich obsah pravidelně stahujeme do naší databáze. Pokud si však opravdu myslíte, že máte platnou stížnost DMCA, na kterou bychom měli reagovat, vyplňte prosím formulář <a %(a_copyright)s>DMCA / nárokování autorských práv</a>. Vaše stížnosti bereme vážně a ozveme se vám co nejdříve. Jak mohu nahlásit porušení autorských práv? Níže uvádíme některé knihy, které mají zvláštní význam pro svět stínových knihoven a uchovávání digitálních dokumentů: Jaké jsou vaše oblíbené knihy? Rádi bychom všem připomněli, že veškerý náš kód a data jsou zcela open source. To je u podobných projektů unikátní. Nevíme o žádném jiném projektu s podobně rozsáhlým katalogem, který by byl také plně open source. Každého, kdo si myslí, že náš projekt vedeme špatně, nabádáme, aby převzal náš kód, data a založil si vlastní stínovou knihovnu! Neříkáme to ze zlomyslnosti... opravdu věříme, že by to bylo skvělé, neboť by to pro všechny zvedlo laťku a lépe by se zachovával odkaz lidstva. Nelíbí se mi, jak tento projekt vedete! Byli bychom rádi, kdyby si lidé zřídili <a %(a_mirrors)s>zrcadla</a>, a my to finančně podpoříme. Jak mohu pomoci? Samozřejmě. Naší inspirací pro sběr metadata je cíl Aarona Swartze „jedna webová stránka pro každou knihu, která kdy byla vydána“, pro který vytvořil <a %(a_openlib)s>Open Library</a>. Tento projekt si vede dobře, ale naše jedinečná pozice nám umožňuje získat metadata, která oni nemohou. Další inspirací byla naše touha vědět <a %(a_blog)s>kolik knih je na světě</a>, abychom mohli spočítat, kolik knih nám ještě zbývá zachránit. Shromažďujete metadata? Vemte na vědomí, že mhut.org blokuje určité rozsahy IP adres, může tak být nutné využít VPN. <strong>Android:</strong> Klikněte na nabídku se třemi tečkami a vyberte možnost „Přidat na domovskou obrazovku“. <strong>iOS:</strong> Klikněte na tlačítko „Sdílet“ ve spodní části a vyberte možnost „Přidat na domovskou obrazovku“. Oficiální mobilní aplikaci nemáme, můžete si však tuto webovou stránku nainstalovat jako aplikaci. Máte mobilní aplikaci? Prosím, pošlete je do <a %(a_archive)s>Internet Archive</a>. Budou je řádně uchovávat. Jak mohu darovat knihy nebo jiné fyzické materiály? Jak si mohu požádat o knihy? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — pravidelně aktualizováno <a %(a_software)s>Anna’s Software</a> — náš open source kód <a %(a_datasets)s>Datasety</a> — o datech <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativní domény Kde najdu další informace o Annině archivu? <a %(a_translate)s>Překládejte na Anna's Software</a> — našem překladatelském systému <a %(a_wikipedia)s>Wikipedie</a> — více o nás (pomozte prosím udržovat tuto stránku aktualizovanou, nebo vytvořte stránku pro svůj jazyk!) Vyberte příslušné parametry, nechte vyhledávací pole prázdné, klikněte na tlačítko „Hledat“ a poté si stránku přidejte do záložek v prohlížeči. Jak uložím své nastavení vyhledávání? Vítáme výzkumníky z oblasti bezpečnosti, hledající zranitelnosti v našich systémech. Jsme velkými zastánci zodpovědného zveřejňování informací. Kontaktujte nás <a %(a_contact)s>zde</a>. V současné době nemůžeme poskytovat odměny za odhalení chyb, s výjimkou zranitelností, které mají <a %(a_link)s >potenciál ohrozit naši anonymitu</a>, za které nabízíme odměny v rozmezí 10-50 tisíc dolarů. V budoucnu bychom však rádi nabídli širší rozsah odměn! Vezměte prosím na vědomí, že útoky sociálního inženýrství do této oblasti nespadají. Pokud se zajímáte o ofenzivní kyberbezpečnost a chcete pomoci archivovat světové znalosti a kulturu, kontaktujte nás. Existuje mnoho cest, jak můžete pomoci. Máte responsible disclosure program? Nemáme dostatek prostředků abychom všem na světě poskytli vysokorychlostní stahování, i když bychom si to přáli. Pokud by se našel bohatý mecenáš, který by nám tuto možnost poskytl, bylo by to úžasné... do té doby se však snažíme dělat, co můžeme. Jsme neziskový projekt, který se sotva uživí z darů. Proto jsme s našimi partnery zavedli dva systémy pro bezplatné stahování: sdílené servery s pomalým stahováním a o něco rychlejší servery s čekací listinou, aby se snížil počet lidí, kteří stahují najednou. V případě pomalého stahování využíváme také <a %(a_verification)s>ověřování prohlížeče</a>, v opačném případě by je zneužívali boti a scrapeři, což by legitimním uživatelům stahování ještě více zpomalilo. Všimněte si, že při používání prohlížeče Tor Browser možná budete muset upravit své bezpečnostní nastavení. Na nejnižší z možností, nazvané „Standard“, výzva Cloudflare turnstile uspěje. Na vyšších možnostech, nazvaných „Bezpečnější“ a „Nejbezpečnější“, výzva selže. Pro velké soubory může někdy dojít k přerušení stahování uprostřed. Doporučujeme použít správce stahování (například JDownloader), který automaticky obnoví velká stahování. Proč jsou pomalá stahování tak pomalá? Často kladené dotazy (FAQ) Pomocí <a %(a_list)s>generátoru seznamu torrentů</a> si můžete vygenerovat seznam torrentů, které nejlépe vyhovují vašim požadavkům na úložný prostor. Ano, viz stránka <a %(a_llm)s>LLM data</a>. Většina torrentů obsahuje soubory přímo, takže je možné skrze nastavení torrent klientů stahovat pouze požadované soubory. Pro určení konkrétních souborů ke stažení můžete <a %(a_generate)s>generovat</a> naše metadata nebo <a %(a_download)s>stáhnout</a> naše databáze ElasticSearch a MariaDB. Bohužel, řada torrentů obsahuje v kořenovém adresáři soubory .zip nebo .tar, v takovém případě je třeba stáhnout celý torrent a teprve poté je možné procházet jednotlivé soubory. Žádné snadno použitelné nástroje pro filtrování torrentů zatím nejsou k dispozici, ale uvítáme vaše příspěvky. (Máme však <a %(a_ideas)s>nějaké nápady</a> pro tento případ.) Dlouhá odpověď: Krátká odpověď: ne snadno. Snažíme se, aby se torrenty v tomto seznamu co nejméně opakovaly nebo překrývaly, ne vždy je to však možné a do značné míry to závisí na pravidlech zdrojových knihoven. U knihoven, které vydávají své vlastní torrenty, to nemůžeme ovlivnit. U torrentů vydávaných Anniným archivem deduplikujeme pouze na základě hashe MD5, což znamená, že různé verze stejné knihy se nededuplikují. Ano. Ve skutečnosti se jedná o soubory PDF a EPUB, jen v mnoha našich torrentech nemají příponu. Existují dvě místa, na kterých můžete najít metadata torrentových souborů, včetně typů souborů/přípon: Každá sbírka nebo vydání má svá vlastní metadata. Například <a %(a_libgen_nonfic)s>torrenty z Libgen.rs</a> mají odpovídající databázi metadat umístěnou právě na Libgen.rs. Zpravidla odkazujeme na příslušné zdroje metadat daného <a %(a_datasets)s>datasetu</a>. 2. Doporučujeme <a %(a_generate)s>generovat</a> nebo <a %(a_download)s>stáhnout</a> naše databáze ElasticSearch a MariaDB. Ty zahrnují přiřazení každého záznamu v Annině archivu k odpovídajícím torrentovým souborům (pokud jsou k dispozici) v položce "torrent_paths" v ElasticSearch JSON. Některé torrent klienty nepodporují velké velikosti částí, které má mnoho našich torrentů (u novějších to už neděláme — i když je to podle specifikací platné!). Pokud na to narazíte, zkuste jiného klienta, nebo si stěžujte výrobcům vašeho torrent klienta. Rád bych pomohl se seedováním, ale nemám moc místa na disku. Torrenty jsou příliš pomalé; mohu si data stáhnout přímo od vás? Mohu si stáhnout pouze část souborů, například pouze určitý jazyk nebo téma? Jak řešíte duplicity v torrentech? Mohu získat seznam torrentů ve formátu JSON? V torrentech nevidím PDF ani EPUB, pouze binární soubory? Co mám dělat? Proč můj torrent klient nemůže otevřít některé z vašich torrent souborů / magnetických odkazů? Torrenty FAQ Jak nahraji nové knihy? Podívejte se na <a %(a_href)s>tento vynikající projekt</a>. Máte monitor dostupnosti? Co je Annin archiv? Abyste mohli používat rychlé stahování, musíte být členy. Nyní podporujeme dárkové karty Amazon, kreditní a debetní karty, kryptoměny, Alipay a WeChat. Pro dnešek jste vyčerpali možnosti rychlého stahování. Dostupnost Počet stažení za hodinu za posledních 30 dní. Hodinový průměr: %(hourly)s. Denní průměr: %(daily)s. S našimi partnery pracujeme na tom, aby byla naše kolekce snadno a zdarma dostupná komukoliv. Věříme, že všichni mají právo na přístup ke kolektivnímu vědění lidstva. <a %(a_search)s>Ne však na úkor autorů</a>. Datasety využívané v Annině archivu jsou zcela volně dostupné a mohou být hromadně zrcadleny pomocí torrentů <a %(a_datasets)s>Zjistit více…</a> Dlouhodobý archiv Celá databáze Hledat Knihy, dokumenty, časopisy, komiksy, záznamy z knihoven, metadata,… Veškerý náš <a %(a_code)s>kód</a> a <a %(a_datasets)s>data</a> jsou zcela open source. <span %(span_anna)s>Annin archiv</span> je neziskový projekt se dvěma cíli: <li><strong>Uchovávání:</strong> Uložení veškerého lidského vědění a kultury.</li><li><strong>Dostupnost:</strong> Zpřístupnění tohoto vědění a kultury komukoliv na světě.</li> Máme největší světovou kolekci textových dat ve vysoké kvalitě. <a %(a_llm)s>Zjistit více…</a> Trénovací data pro LLM 🪩 Zrcadla: výzva pro dobrovolníky Pokud provozujete rizikový anonymní procesor plateb, kontaktujte nás, prosím. Hledáme také zájemce o umístění malé vkusné reklamy. Veškerý výtěžek půjde na naše úsilí o zachování lidského vědění a kultury. Uchovávání Odhadujeme, že jsme uchovali asi <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% světových knih</a>. Uchováváme knihy, vědecké publikace, komiksy, časopisy a další shromážděním těchto souborů z různých <a href="https://en.wikipedia.org/wiki/Shadow_library">stínových knihoven</a>, oficiálních knihoven a dalších kolekcí na jedno místo. Všechna tato data jsou navždy zachována tím, že je lze snadno duplikovat — za pomoci torrentů — což vede k mnoha kopiím po celém světě. Některé stínové knihovny toto praktikují samy (např. Sci-Hub nebo Library Genesis), zatímco Annin archiv „osvobozuje“ jiné knihovny, které nenabízejí velkoobjemovou distribuci (např. Z-Library) nebo vůbec nejsou stínové knihovny (např. Internet Archive, DuXiu). Tato celosvětová distribuce společně s otevřeným kódem zajišťuje naší stránce odolnost vůči smazaní a zajišťuje dlouhodobé zachování lidských vědomostí a lidské kultury. Dověďte se více o <a href="/datasets">našem datasetu</a>. Pokud jste <a %(a_member)s>členem</a>, není vyžadováno ověření prohlížeče. 🧬&nbsp;SciDB navazuje na Sci-Hub. SciDB Otevřít DOI Sci-Hub <a %(a_paused)s>pozastavil</a> nahrávání nových článků. Přímý přístup k %(count)s akademickým dokumentům 🧬&nbsp;SciDB navazuje na Sci-Hub s podobným rozhraním a možností přímého prohlížení PDF dokumentů. Pro prohlížení zadejte DOI. Máme k dispozici kompletní sbírku Sci-Hub včetně nových dokumentů. Většinu z nich je možné přímo prohlížet pomocí rozhraní připomínající Sci-Hub. Některé dokumenty lze stáhnout z externích zdrojů, v takovém případě na ně uvádíme odkazy. Můžete nám neskutečně pomoci seedováním našich torrentů. <a %(a_torrents)s>Zjistit více...</a> >%(count)s seederů <%(count)s seedeři %(count_min)s–%(count_max)s seederů 🤝 Hledáme dobrovolníky Jako neziskový projekt s otevřeným zdrojovým kódem neustále hledáme dobrovolníky, kteří by nám mohli pomoci. IPFS stažení Seznam od %(by)s, vytvořen <span %(span_time)s>%(time)s</span> Uložit ❌ Něco se pokazilo. Zkuste to prosím znovu. ✅ Uloženo. Obnovte prosím stránku. Seznam je prázdný. editovat Přidat/odebrat soubor z tohoto seznamu je možné v záložce "Seznamy" u daného souboru. Seznam Jak můžeme pomoci Odstranění překryvů (deduplikace) Extrahování textu a metadata OCR Jsme schopni poskytnout vysokorychlostní přístup k našim plným sbírkám, stejně jako k nevydaným sbírkám. Toto je přístup na úrovni podniku, který můžeme poskytnout za dary v řádu desítek tisíc USD. Jsme také ochotni vyměnit tento přístup za vysoce kvalitní sbírky, které ještě nemáme. Můžeme vám vrátit peníze, pokud nám poskytnete obohacení našich dat, jako například: Podpořte dlouhodobou archivaci lidského vědění a získejte lepší data pro váš model! <a %(a_contact)s>Kontaktujte nás</a>, abychom mohli prodiskutovat, jak můžeme spolupracovat. Je dobře známo, že LLM prosperují na vysoce kvalitních datech. Máme největší sbírku knih, článků, časopisů atd. na světě, což jsou některé z nejkvalitnějších textových zdrojů. LLM data Jedinečný rozsah a škála Naše sbírka obsahuje přes sto milionů souborů, včetně akademických časopisů, učebnic a časopisů. Tohoto rozsahu dosahujeme kombinací velkých existujících úložišť. Některé z našich zdrojových sbírek jsou již dostupné ve velkém množství (Sci-Hub a části Libgen). Jiné zdroje jsme osvobodili sami. <a %(a_datasets)s>Datasets</a> ukazuje úplný přehled. Naše sbírka zahrnuje miliony knih, článků a časopisů z doby před érou e-knih. Velké části této sbírky již byly OCRovány a mají jen malý vnitřní překryv. Pokračovat Pokud jste ztratili svůj klíč, <a %(a_contact)s>kontaktujte nás</a> a poskytněte nám co nejvíce informací. Možná si budete muset dočasně vytvořit nový účet, abyste nás mohli kontaktovat. Pro zobrazení této stránky se prosím <a %(a_account)s>přihlaste</a> Abychom zamezili vytváření účtů spamboty, potřebujeme nejprve ověřit váš prohlížeč. Pokud se zaseknete v nekonečné smyčce, měla by pomoci instalace <a %(a_privacypass)s>Privacy Pass</a>. Může také pomoci vypnout blokovače reklam a další rozšíření prohlížeče. Přihlásit / Registrovat Annin archiv je dočasně nedostupný z důvodu údržby. Vraťte se prosím za hodinu. Alternativní autor Alternativní popis Alternativní vydání Alternativní přípona Alternativní název souboru Alternativní nakladatel Alternativní název datum otevření zdroje Číst více… popis Hledat CADAL SSNO number v Annině archivu Hledat DuXiu SSID number v Annině archivu Hledat DuXiu DXID number v Annině archivu Hledat ISBN v Annině archivu Hledat OCLC (WorldCat) number v Annině archivu Hledat Open Library ID v Annině archivu Online prohlížeč Annin archiv %(count)s ovlivněných stran Po stažení: Lepší verze tohoto souboru je možná dostupná na %(link)s Stažení skrze hromadný torrent sbírka Použijte online nástroje pro převod mezi formáty. Doporučené nástroje pro převod: %(links)s Pro velké soubory doporučujeme použít správce stahování, aby nedošlo k přerušením. Doporučení správci stahování: %(links)s EBSCOhost eBook Index (pouze pro zkušené) (klikněte také na "GET" nahoře na stránce) (klikněte na "GET" nahoře na stránce) Externí stahování Dnes vám zbývá ještě %(remaining)s rychlých stažení. Děkujeme, že jste členem! ❤️ Pro dnešek jste vyčerpali možnosti rychlého stahování. Tento soubor jste nedávno stáhli. Odkaz zůstává po určitou dobu platný. <strong>🚀 Rychlé stahování</strong> Staňte se <a %(a_membership)s>členem</a> a podpořte dlouhodobé uchovávání knih, odborných článků, a dalších materiálů. Jako naše díky za vaši podporu dostanete přístup k rychlejšímu stahování. ❤️ 🚀 Rychlé stahování 🐢 Pomalé stahování Vypůjčeno z Internet Archive IPFS Gateway #%(num)d (stažení skrze IPFS může vyžadovat více pokusů) Libgen.li Libgen.rs (beletrie) Libgen.rs (populárně naučná literatura) jejich reklamy mohou obsahovat škodlivý software, proto používejte adblock nebo na reklamy neklikejte Amazon „Send to Kindle“ djazz „Send to Kobo/Kindle“ MagzDB ManualsLib Nexus/STC (Soubory Nexus/STC mohou být nespolehlivé ke stažení) Nenalezena žádná stažení. Všechny odkazy vedou na stejný soubor a měly by být bezpečné. Přesto buďte při stahování opatrní, obzvláště ze stránek mimo Annin archiv. Například se ujistěte, že je software na vašem zařízení aktualizovaný. (bez přesměrování) Otevřít v našem prohlížeči (otevřít v prohlížeči) Varianta #%(num)d: %(link)s %(extra)s Vyhledat původní záznam v CADAL Ruční vyhledávání na DuXiu Najít původní záznam v ISBNdb Najít původní záznam na WorldCat Najít původní záznam v Open Library Prohledat další různé databáze pro ISBN (pouze pro uživatele s poruchami čtení) PubMed K otevření souboru budete potřebovat čtečku ebooků nebo PDF, v závislosti na formátu souboru. Doporučené čtečky ebooků: %(links)s Annin archiv 🧬 SciDB Sci-Hub: %(doi)s (odpovídající DOI nemusí být na Sci-Hubu dostupné) Můžete posílat soubory PDF i EPUB na svůj Kindle nebo Kobo eReader. Doporučené nástroje: %(links)s Více informací ve <a %(a_slow)s>FAQ</a>. Podporujte autory a knihovny Pokud se vám to líbí a můžete si to dovolit, zvažte koupi originálu nebo přímou podporu autorů. Pokud je tato kniha dostupná ve vaší místní knihovně, zvažte její bezplatné zapůjčení tam. Možnost stahování z partnerských serverů je pro tento soubor dočasně nedostupné. torrent od důvěryhodných partnerů. Z-Library Z-Library (přes Tor) (vyžaduje prohlížeč Tor) zobrazit externí stahování <span class="font-bold">❌ Tento soubor může mít problémy, a byl tak skryt ze zdrojové knihovny.</span> Někdy se tak stane na žádost držitele autorských práv nebo kvůli dostupnosti lepší alternativy, někdy to ovšem je kvůli problémům se souborem samotným. Soubor tak může být možné bez problémů stáhnout, doporučujeme se ale nejdříve podívat po jeho alternativách. Více informací: Pokud i přesto chcete tento soubor stáhnout, ujistěte se, že k jeho otevření používáte důvěryhodný a aktualizovaný software. metadata komentáře AA: Hledat v Annině archivu „%(name)s“ Codes Explorer: Zobrazit v Codes Explorer “%(name)s” URL: Webová stránka: Máte-li tento soubor a ještě není v Annině archivu dostupný, zvažte <a %(a_request)s>jeho nahrání</a>. Internet Archive Controlled Digital Lending soubor “%(id)s” Jedná se o záznam souboru z Internet Archive, nikoli o soubor ke stažení. Můžete si knihu půjčit (odkaz níže) nebo použít tuto URL při <a %(a_request)s>žádání o soubor</a>. Zlepšit metadata Záznam metadat CADAL SSNO %(id)s Jedná se o záznam metadat, nikoli o soubor ke stažení. Můžete použít tuto URL při <a %(a_request)s>žádání o soubor</a>. Záznam metadat DuXiu SSID %(id)s ISBNdb %(id)s záznam metadat MagzDB ID %(id)s metadata záznam Nexus/STC ID %(id)s metadata záznam záznam metadat s OCLC (WorldCat) číslem %(id)s Open Library %(id)s záznam metadat Sci-Hub soubor “%(id)s” Nenalezeno “%(md5_input)s” nebylo možné v naší databázi nalézt. Přidat komentář (%(count)s) MD5 můžete získat z URL, např. MD5 lepší verze tohoto souboru (pokud je k dispozici). Vyplňte toto, pokud existuje jiný soubor, který se těsně shoduje s tímto souborem (stejné vydání, stejná přípona souboru, pokud ji můžete najít), který by lidé měli použít místo tohoto souboru. Pokud víte o lepší verzi tohoto souboru mimo Annin archiv, prosím <a %(a_upload)s>nahrajte ji</a>. Něco se pokazilo. Prosím, načtěte stránku znovu a zkuste to znovu. Zanechali jste komentář. Může to chvíli trvat, než se zobrazí. Použijte prosím <a %(a_copyright)s>formulář pro nárok DMCA / autorská práva</a>. Popište problém (povinné) Pokud má tento soubor skvělou kvalitu, můžete o něm diskutovat zde! Pokud ne, použijte tlačítko „Nahlásit problém se souborem“. Skvělá kvalita souboru (%(count)s) Kvalita souboru Naučte se <a %(a_metadata)s>zlepšit metadata</a> tohoto souboru sami. Popis problému Prosím <a %(a_login)s>přihlaste se</a>. Tato kniha se mi moc líbila! Pomozte komunitě nahlášením kvality tohoto souboru! 🙌 Něco se pokazilo. Prosím, načtěte stránku znovu a zkuste to znovu. Nahlásit problém se souborem (%(count)s) Děkujeme za odeslání vašeho hlášení. Bude zobrazeno na této stránce a také ručně zkontrolováno Annou (dokud nebudeme mít řádný systém moderování). Zanechat komentář Odeslat hlášení Co je s tímto souborem špatně? Vypůjčení (%(count)s) Komentáře (%(count)s) Stažení (%(count)s) Prohlížet metadata (%(count)s) Seznamy (%(count)s) Statistiky (%(count)s) Pro informace o tomto konkrétním souboru si prohlédněte jeho <a %(a_href)s>JSON soubor</a>. Toto je soubor spravovaný knihovnou <a %(a_ia)s>IA’s Controlled Digital Lending</a> a indexovaný Anniným archivem pro vyhledávání. Pro informace o různých datasets, které jsme sestavili, navštivte <a %(a_datasets)s>stránku Datasets</a>. Metadata z propojeného záznamu Vylepšit metadata na Open Library „MD5 souboru“ je hash, který se vypočítá z obsahu souboru a je na základě tohoto obsahu poměrně jedinečný. Všechny stínové knihovny, které jsme zde indexovali, primárně používají MD5 k identifikaci souborů. Soubor se může objevit v několika stínových knihovnách. Pro informace o různých datasets, které jsme sestavili, navštivte <a %(a_datasets)s>stránku Datasets</a>. Nahlásit kvalitu souboru Celkový počet stažení: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Česká metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Upozornění: více propojených záznamů: Když se podíváte na knihu na Annině archivu, můžete vidět různé položky: název, autor, vydavatel, vydání, rok, popis, název souboru a další. Všechny tyto informace se nazývají <em>metadata</em>. Protože sdružujeme knihy z různých <em>zdrojových knihoven</em>, zobrazujeme metadata pocházející z dané zdrojové knihovny. Například u knihy z Library Genesis, zobrazíme název právě z jejich databáze. Někdy je kniha přítomna ve <em>více</em> zdrojových knihovnách, které mohou mít odlišná metadata. V takovém případě jednoduše zobrazíme nejdelší verzi každého pole, neboť pravděpodobně obsahuje nejvíce užitečných informací! Ostatní pole zobrazíme pod popisem, např. jako „alternativní název“ (pokud jsou odlišná). Také extrahujeme <em>kódy</em>, jako jsou identifikátory a klasifikátory, ze zdrojové knihovny. <em>Identifikátory</em> jednoznačně reprezentují konkrétní vydání knihy; příklady jsou ISBN, DOI, Open Library ID, Google Books ID nebo Amazon ID. <em>Klasifikátory</em> seskupují více podobných knih; příklady jsou Deweyho desetinné třídění (DCC), UDC, LCC, RVK nebo GOST. Někdy jsou tyto kódy explicitně propojeny ve zdrojových knihovnách a někdy je můžeme extrahovat z názvu souboru nebo popisu (primárně ISBN a DOI). Můžeme použít identifikátory k nalezení záznamů v <em>kolekcích pouze s metadata</em>, jako jsou OpenLibrary, ISBNdb nebo WorldCat/OCLC. V našem vyhledávači je specifická <em>záložka metadata</em>, pokud byste chtěli procházet tyto kolekce. Používáme odpovídající záznamy k doplnění chybějících metadat (např. pokud chybí název), nebo např. jako „alternativní název“ (pokud již existuje název). Chcete-li přesně vidět, odkud metadata knihy pocházejí, podívejte se na <em>záložku „Technické detaily“</em> na stránce knihy. Obsahuje odkaz na surový JSON pro tuto knihu s odkazy na surový JSON původních záznamů. Pro více informací si přečtěte následující stránky: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Vyhledávání (záložka metadata)</a>, <a %(a_codes)s>Průzkumník kódů</a> a <a %(a_example)s>Příklad metadata JSON</a>. Nakonec všechna naše metadata mohou být <a %(a_generated)s>generována</a> nebo <a %(a_downloaded)s>stažena</a> jako databáze ElasticSearch a MariaDB. Základní informace Zlepšením metadat můžete přispět k uchování knih! Nejprve si přečtěte sekci základní informace o metadatech a poté se dozvíte, jak vylepšit metadata prostřednictvím propojení s Open Library a získat bezplatné členství na Annině archivu. Vylepšit metadata Takže pokud narazíte na soubor se špatnými metadaty, jak byste je měli opravit? Můžete jít do zdrojové knihovny a postupovat podle jejích postupů pro opravu metadat, ale co dělat, pokud je soubor přítomen ve více zdrojových knihovnách? Existuje jeden identifikátor, který je na Annině archivu považován za speciální. <strong>Pole annas_archive md5 na Open Library vždy přepisuje všechna ostatní metadata!</strong> Nejprve se ale vraťme zpět a naučme se něco o Open Library. Open Library byla založena v roce 2006 Aaronem Swartzem s cílem „jedna webová stránka pro každou knihu, která kdy byla vydána“. Je to něco jako Wikipedia pro metadata knih: každý ji může upravovat, je volně licencovaná a může být stažena hromadně. Je to databáze knih, která je nejvíce v souladu s naší misí — ve skutečnosti byl Annin archiv inspirován vizí a životem Aarona Swartze. Místo toho, abychom znovu vynalézali kolo, rozhodli jsme se přesměrovat naše dobrovolníky na Open Library. Pokud vidíte knihu s nesprávnými metadaty, můžete pomoci následujícím způsobem: Všimněte si, že to funguje pouze pro knihy, ne pro akademické články nebo jiné typy souborů. Pro jiné typy souborů stále doporučujeme najít zdrojovou knihovnu. Může trvat několik týdnů, než budou změny zahrnuty do Annina archivu, protože musíme stáhnout nejnovější data z Open Library a znovu vygenerovat náš vyhledávací index.  Přejděte na <a %(a_openlib)s>webovou stránku Open Library</a>. Najděte odpovídající záznam knihy. <strong>UPOZORNĚNÍ:</strong> ujistěte se, že jste vybrali správné <strong>vydání</strong>. V Open Library máte „díla“ (works) a „vydání“ (editions). „Dílo“ by mohlo být „Harry Potter a Kámen mudrců“. „Vydání“ by mohlo být: První vydání z roku 1997 vydané nakladatelstvím Bloomsbery s 256 stranami. Brožované vydání z roku 2003 vydané nakladatelstvím Raincoast Books s 223 stranami. Polský překlad od Media Rodzina z roku 2000 „Harry Potter I Kamie Filozoficzn“ s 328 stranami. Všechna tato vydání mají odlišná ISBN i obsah, takže se ujistěte, že jste volíte to správné! Upravte záznam (nebo jej vytvořte, pokud žádný neexistuje) a přidejte co nejvíce užitečných informací! Když už jste tady, tak můžete udělat záznam perfektním. V části „ID numbers“ vyberte „Anna’s Archive“ a přidejte MD5 knihy z Annina archivu. To je ten dlouhý řetězec písmen a čísel za „/md5/“ v URL. Pokuste se v Annině archivu najít další soubory, které také odpovídají tomuto záznamu, a přidejte je také. V budoucnu je můžeme seskupit jako duplikáty na stránce vyhledávání. Až budete hotovi, zapište si URL, které jste právě aktualizovali. Jakmile aktualizujete alespoň 30 záznamů s MD5 z Annina archivu, pošlete nám <a %(a_contact)s>email</a> a pošlete nám seznam. Dostanete od nás bezplatné členství do Annina archivu, abyste mohli tuto práci snadněji vykonávat (a jako poděkování za vaši pomoc). Tyto úpravy musí být vysoce kvalitní a přidávat značné množství informací, jinak bude vaše žádost zamítnuta. Vaše žádost bude také zamítnuta, pokud některé z úprav budou vráceny nebo opraveny moderátory Open Library. Propojení s Open Library Pokud se významně zapojíte do vývoje a provozu naší práce, můžeme diskutovat o sdílení větší části darovaných prostředků s vámi, abyste je mohli použít podle potřeby. Za hosting zaplatíme pouze tehdy, když budete mít vše nastaveno a prokážete, že jste schopni udržovat archiv aktuální s aktualizacemi. To znamená, že první 1-2 měsíce budete muset zaplatit z vlastní kapsy. Váš čas nebude kompenzován (a ani náš), protože se jedná o čistě dobrovolnou práci. Jsme ochotni pokrýt náklady na hosting a VPN, zpočátku až do výše 200 $ měsíčně. To je dostatečné pro základní vyhledávací server a proxy chráněný DMCA. Náklady na hosting Prosím <strong>nekontaktujte nás</strong> s žádostí o povolení nebo s základními otázkami. Činy mluví hlasitěji než slova! Všechny informace jsou k dispozici, takže se pusťte do nastavení svého zrcadlení. Neváhejte zveřejňovat tikety nebo žádosti o sloučení na našem Gitlabu, když narazíte na problémy. Možná budeme muset s vámi vytvořit některé specifické funkce pro zrcadlení, jako je rebranding z „Annin archiv“ na název vašeho webu, (zpočátku) deaktivace uživatelských účtů nebo odkazování zpět na naši hlavní stránku z knižních stránek. Jakmile budete mít své zrcadlení v provozu, prosím kontaktujte nás. Rádi bychom zkontrolovali vaši bezpečnostní operaci a jakmile bude solidní, odkážeme na vaše zrcadlení a začneme s vámi úžeji spolupracovat. Děkujeme předem všem, kteří jsou ochotni přispět tímto způsobem! Není to pro slabé povahy, ale posílí to dlouhověkost největší skutečně otevřené knihovny v lidské historii. Začínáme Abychom zvýšili odolnost Annina archivu, hledáme dobrovolníky pro provozování zrcadel. Vaše verze je jasně odlišena jako zrcadlení, např. „Bobův archiv, zrcadlení Annina archivu“. Jste ochotni přijmout rizika spojená s touto prací, která jsou značná. Máte hluboké porozumění pro potřebnou provozní bezpečnost. Obsah <a %(a_shadow)s>těchto</a> <a %(a_pirate)s>příspěvků</a> je vám samozřejmý. Zpočátku vám neposkytneme přístup k našim partnerským serverovým stahováním, ale pokud vše půjde dobře, můžeme to s vámi sdílet. Provozujete open source kódovou základnu Annina archivu a pravidelně aktualizujete jak kód, tak data. Jste ochotni přispět do naší <a %(a_codebase)s>kódové základny</a> — ve spolupráci s naším týmem — aby se to stalo skutečností. Hledáme toto: Zrcadla: výzva pro dobrovolníky Poslat další dar. Zatím jste nic nedarovali. <a %(a_donate)s>Poslat první dar.</a> Informace o darech nejsou veřejně přístupné. Mé dary 📡 Pokud chcete hromadně zrcadlit naši kolekci, více informací naleznete na stránkách <a %(a_datasets)s>Datasety</a> a <a %(a_torrents)s>Torrenty</a> pages. Stažení z vaší IP adresy za posledních 24 hodin: %(count)s. 🚀 Pokud chcete získat přístup k rychlejšímu stahování a vyhnout se kontrolám prohlížeče, <a %(a_membership)s>staňte se členem</a>. Stažení z partnerské webové stránky Během čekání můžete pokračovat v prohlížení Annina archivu v jiné kartě prohlížeče (pokud váš prohlížeč podporuje obnovování karet na pozadí). Není problém čekat na načtení více stránek ke stažení najednou (z jednoho serveru však stahujte simultánně pouze jeden soubor). Jakmile získáte odkaz ke stažení, je platný několik hodin. Děkujeme za trpělivost, díky ní je web přístupný zdarma pro všechny! 😊 🔗 Všechny odkazy na stažení souboru: <a %(a_main)s>Hlavní stránka souboru</a>. ❌ Pomalé stahování není dostupné prostřednictvím VPN Cloudflare ani z jiných IP adres Cloudflare. ❌ Pomalé stahování je dostupné pouze přes oficiální web. Návštěva %(websites)s. 📚 Ke stažení použijte následující URL: <a %(a_download)s>Stáhnout</a>. Abychom všem poskytli možnost stahovat soubory zdarma, musíte před stažením tohoto souboru počkat. Počkejte prosím <span %(span_countdown)s>%(wait_seconds)s</span> sekund, než si stáhnete tento soubor. Upozornění: za posledních 24 hodin došlo k velkému množství stahování z vaší IP adresy. Stahování může být pomalejší než obvykle. Toto varování se zobrazuje například kvůli používání VPN, sdílenému připojení k internetu nebo váš poskytovatel internetu sdílí IP adresy. Uložit ❌ Něco se pokazilo. Zkuste to znovu. ✅ Uloženo. Obnovte stránku. Změní vaše uživatelské jméno. Váš identifikátor (text za "#") změnit nelze. Profil vytvořen <span %(span_time)s>%(time)s</span> editovat Seznamy Nový seznam lze vytvořit v záložce "Seznamy" u vyhledaného souboru. Nenalezen žádný seznam Profil nenalezen. Profil V současné době nemůžeme vyhovět požadavkům na knihy. Neposílejte nám své požadavky na knihy e-mailem. Žádosti podávejte na fórech Z-Library nebo Libgen. Záznam v Annině archivu DOI: %(doi)s Stáhnout SciDB Nexus/STC Náhled ještě není dostupný. Stáhněte si soubor z <a %(a_path)s>Annina Archivu</a>. Chcete-li podpořit dostupnost a dlouhodobé uchování lidských vědomostí, staňte se <a %(a_donate)s>členy</a>. Jako bonus 🧬&nbsp; se SciDB členům načítá rychleji a bez omezení. Nefunguje? Zkuste <a %(a_refresh)s>obnovení</a>. Sci-Hub Přidat specifické vyhledávací pole Vyhledávat v popisech a komentářích v metadatech Rok vydání Pokročilé Přístup Obsah Zobrazit Seznam Tabulka Typ souboru Jazyk Seřadit podle Největší Nejrelevantnější Nejnovější (velikost souboru) (otevřeno veřejnosti) (rok vydání) Nejstarší Náhodné Nejmenší Zdroj data získána a zpřístupněna Anniným archivem Digitální výpůjčka (%(count)s) Odborné články (%(count)s) Odpovídající soubor byl nalezen na: %(in)s.Pokud budete <a %(a_request)s>žádat o tento soubor </a>, použijte danou URL adresu. Metadata (%(count)s) Chcete-li prozkoumat index vyhledávání podle kódů, využijte <a %(a_href)s>Codes Explorer</a>. Index vyhledávání je aktualizován jednou měsíčně. Poslední aktualizace: %(last_data_refresh_date)s. Více technických informací najdete na stránce %(link_open_tag)sDatasety</a>. Vyloučit Zahrnout pouze Nezaškrtnuto více… Další … předchozí Tento vyhledávací index obsahuje metadata z Internet Archive’s Controlled Digital Lending library. <a %(a_datasets)s>Více o našich datasetech</a>. Pro více knihoven s digitální výpůjčkou se podívejte na <a %(a_wikipedia)s>Wikipedii</a> a <a %(a_mobileread)s>MobileRead Wiki</a>. Pro DMCA / nárokování autorských práv <a %(a_copyright)s>klikněte sem</a>. Čas stažení Chyba při vyhledávání. Zkuste <a %(a_reload)s>obnovit stránku</a>. Pokud problém přetrvává, zašlete nám, prosím, email na %(email)s. Rychlé stahování Kdokoli může pomoci zachovat tyto soubory seedováním našeho <a %(a_torrents)s>jednotného seznamu torrentů</a>. ➡️ Někdy k tomu dochází nesprávně, když je vyhledávací server pomalý. V takových případech může pomoci <a %(a_attrs)s>obnovení stránky</a>. ❌ Tento soubor může mít problémy. Hledáte odborné články? Tento vyhledávací index obsahuje metadata z ISBNid a Open Library. <a %(a_datasets)s>Více o našich datasetech</a>. Na světě existuje opravdu mnoho zdrojů metadat pro písemná díla. <a %(a_wikipedia)s>Tato stránka na Wikipedii</a> je dobrý začátek, pokud ale víte o dalších dobrých seznamech, dejte nám, prosím, vědět. U metadat zobrazujeme původní záznamy. Neprovádíme žádné slučování záznamů. V současné době máme nejrozsáhlejší volně dostupný katalog knih, článků a dalších písemných prací na světě. Zrcadlíme Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>a další</a>. <span %(classname)s>Nebyly nalezeny žádné soubory.</span> Zkuste použít méně nebo jiné vyhledávací výrazy a filtry. Výsledky %(from)s-%(to)s (%(total)s celkově) Pokud objevíte další "stínovou knihovnu", kterou bychom měli zrcadlit, nebo pokud máte nějaké otázky, napište nám na %(email)s. %(num)d částečných shod %(num)d+ částečných shod Zadejte text do pole pro vyhledání souborů v knihovnách s digitální výpůjčkou. Procházejte náš katalog %(count)s souborů ke stažení, které <a %(a_preserve)s>uchováváme navždy</a>. Zadejte text pro vyhledání. Začněte psát do pole k prohledání našeho katalogu %(count)s akademických dokumentů a odborných článků, ze kterých<a %(a_preserve)s> zachováváme navždy</a>. Zadejte text do pole pro vyhledání metadat z knihoven. Toto může být užitečné při <a %(a_request)s>žádání o soubor</a>. Tip: používejte klávesové zkratky “/” (přejít na vyhledávací pole), “enter” (vyhledat), “j” (nahoru), “k” (dolů) pro rychlejší navigaci. Jedná se o záznamy metadat, <span %(classname)s>nikoli</span> soubory ke stažení. Nastavení vyhledávání Hledat Digitální výpůjčka Stažení Odborné články Metadata Nové vyhledávání %(search_input)s — Vyhledávání Vyhledávání trvalo příliš dlouho a je možné, že uvidíte nepřesné výsledky. Občas pomáhá <a %(a_reload)s>obnovení</a> stránky. Vyhledávání trvalo příliš dlouho (pro nespecifické vyhledávání je toto normální). Vypsaný počet výsledků nemusí být přesný. Pro nahrání velkého množství souborů (10 000 a více), které Libgen nebo Z-Library neakceptují, nás prosím kontaktujte na %(a_email)s. Pro Libgen.li se ujistěte, že jste se nejprve přihlásili na <a %(a_forum)s>jejich fórum</a> s uživatelským jménem %(username)s a heslem %(password)s, a poté se vraťte na jejich <a %(a_upload_page)s>stránku pro nahrávání</a>. Prozatím doporučujeme nahrávat nové knihy na fork Library Genesis. Návod, jak to provést, najdete <a %(a_guide)s>zde</a>. Oba forky uvedené na této stránce čerpají data ze stejného nahrávacího systému. Pro malé nahrávky (až 10 000 souborů) je prosím nahrajte na obě místa %(first)s a %(second)s. Případně je můžete nahrát na Z-library <a %(a_upload)s>zde</a>. Chcete-li nahrát akademické dokumenty, nahrajte je také (kromě Library Genesis) na <a %(a_stc_nexus)s>STC Nexus</a>. Jsou nejlepší stínovou knihovnou pro nové akademické dokumenty. Prozatím jsme je neintegrovali, do budoucna to však plánujeme. Pro nahrání můžete využít jejich <a %(a_telegram)s> uploadovacího bota na Telegramu</a>. V případě příliš mnoha souborů můžete využít adresu uvedenou v jejich připnuté zprávě. <span %(label)s>Intenzivní dobrovolnická práce (odměny USD$50-USD$5,000):</span> pokud můžete věnovat hodně času a/nebo zdrojů naší misi, rádi bychom s vámi úzce spolupracovali. Nakonec se můžete připojit k vnitřnímu týmu. I když máme omezený rozpočet, můžeme za nejintenzivnější práci udělovat <span %(bold)s>💰 peněžní odměny</span>. <span %(label)s>Lehká dobrovolnická práce:</span> pokud můžete věnovat jen pár hodin sem a tam, stále existuje spousta způsobů, jak můžete pomoci. Konzistentní dobrovolníky odměňujeme <span %(bold)s>🤝 členstvím v Annině archivu</span>. Annin archiv se spoléhá na dobrovolníky jako jste vy. Vítáme všechny úrovně závazků a hledáme pomoc ve dvou hlavních kategoriích: Pokud nemůžete věnovat svůj čas dobrovolnictví, můžete nám stále hodně pomoci <a %(a_donate)s>darováním peněz</a>, <a %(a_torrents)s>sdílením našich torrentů</a>, <a %(a_uploading)s>nahráváním knih</a> nebo <a %(a_help)s>informováním svých přátel o Annině archivu</a>. <span %(bold)s>Firmy:</span> nabízíme vysokorychlostní přímý přístup k našim sbírkám výměnou za podnikové dary nebo výměnou za nové sbírky (např. nové skeny, OCR datasets, obohacení našich dat). <a %(a_contact)s>Kontaktujte nás</a>, pokud jste to vy. Podívejte se také na naši <a %(a_llm)s>stránku LLM</a>. Odměny Stále hledáme lidi s pevnými programátorskými nebo ofenzivními bezpečnostními dovednostmi, kteří by se zapojili. Můžete významně přispět k zachování dědictví lidstva. Jako poděkování nabízíme členství za solidní příspěvky. Jako velké poděkování nabízíme peněžní odměny za zvláště důležité a obtížné úkoly. Nemělo by to být vnímáno jako náhrada za práci, ale je to další pobídka a může pomoci s vynaloženými náklady. Většina našeho kódu je open source a budeme to požadovat i od vašeho kódu při udělování odměny. Existují některé výjimky, které můžeme projednat individuálně. Odměny jsou udělovány první osobě, která úkol dokončí. Neváhejte komentovat odměnový tiket, aby ostatní věděli, že na něčem pracujete, aby mohli počkat nebo vás kontaktovat pro spolupráci. Ale mějte na paměti, že ostatní jsou stále volní pracovat na tom také a pokusit se vás předběhnout. Nicméně, neudělujeme odměny za nedbalou práci. Pokud jsou dvě kvalitní podání učiněna blízko sebe (během jednoho nebo dvou dnů), můžeme se rozhodnout udělit odměny oběma, podle našeho uvážení, například 100%% za první podání a 50%% za druhé podání (tedy celkem 150%%). U větších odměn (zejména odměn za scraping) nás prosím kontaktujte, když dokončíte ~5%% z toho, a jste si jisti, že vaše metoda bude škálovat na celý milník. Budete muset sdílet svou metodu s námi, abychom mohli poskytnout zpětnou vazbu. Také tímto způsobem můžeme rozhodnout, co dělat, pokud se k odměně blíží více lidí, například potenciálně ji udělit více lidem, povzbudit lidi ke spolupráci atd. UPOZORNĚNÍ: úkoly s vysokou odměnou jsou <span %(bold)s>obtížné</span> — může být moudré začít s jednoduššími. Přejděte na náš <a %(a_gitlab)s>seznam problémů na Gitlabu</a> a seřaďte podle „Priorita štítku“. To zhruba ukazuje pořadí úkolů, na kterých nám záleží. Úkoly bez explicitních odměn jsou stále způsobilé pro členství, zejména ty označené „Přijato“ a „Oblíbené Anny“. Možná budete chtít začít s „Startovacím projektem“. Lehké dobrovolnictví Nyní máme také synchronizovaný kanál Matrix na %(matrix)s. Pokud máte pár hodin volného času, můžete pomoci různými způsoby. Nezapomeňte se připojit k <a %(a_telegram)s>dobrovolnickému chatu na Telegramu</a>. Jako projev vděčnosti obvykle rozdáváme 6 měsíců členství „Šťastný knihovník“ za základní milníky a více za pokračující dobrovolnickou práci. Všechny milníky vyžadují vysoce kvalitní práci — nedbalá práce nám škodí více, než pomáhá, a odmítneme ji. Prosím <a %(a_contact)s>pošlete nám e-mail</a>, když dosáhnete milníku. Odkazy nebo snímky obrazovky žádostí, které jste splnili: %(links)s. Plnění žádostí o knihy (nebo články atd.) na fórech Z-Library nebo Library Genesis. Nemáme vlastní systém žádostí o knihy, ale zrcadlíme tyto knihovny, takže jejich zlepšení zlepšuje i Annin archiv. Milník Úkol Záleží na úkolu. Malé úkoly zveřejněné v našem <a %(a_telegram)s>dobrovolnickém chatu na Telegramu</a>. Obvykle za členství, někdy za malé odměny. Malé úkoly zveřejněné v naší dobrovolnické chatové skupině. Ujistěte se, že zanecháte komentář k problémům, které opravíte, aby ostatní neduplikovali vaši práci. Odkazy na záznamy, které jste vylepšili: %(links)s. Můžete použít <a %(a_list)s>seznam náhodných problémů s metadata</a> jako výchozí bod. Zlepšete metadata <a %(a_metadata)s>propojením</a> s Open Library. Tyto by měly ukázat, že někomu dáváte vědět o Annině archivu, a oni vám děkují. Odkazy nebo snímky obrazovky: %(links)s. Šíření povědomí o Annině archivu. Například doporučováním knih na AA, odkazováním na naše blogové příspěvky nebo obecně směrováním lidí na naše webové stránky. Kompletní překlad jazyka (pokud již nebyl téměř dokončen). <a %(a_translate)s>Překlad</a> webové stránky. Odkaz na historii úprav, který ukazuje, že jste významně přispěli. Vylepšete stránku Wikipedie pro Annin archiv ve vašem jazyce. Zahrňte informace z Wikipedie AA v jiných jazycích a z našeho webu a blogu. Přidejte odkazy na AA na dalších relevantních stránkách. Dobrovolnictví a odměny 