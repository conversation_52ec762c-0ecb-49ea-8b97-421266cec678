#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Requesta invalide. Visita %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " e "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "e plus"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Nos reflecte %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Nos scrapa e open-source %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Tote nostre codice e datos es completemente open source."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Le plus grande bibliotheca vermente aperte in le historia human."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;articulos — preservate pro semper."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Le plus grande bibliotheca de datos aperte e de codice aperte del mundo. ⭐️&nbsp;Reflecte Sci-Hub, Library Genesis, Z-Library, e plus. 📈&nbsp;%(book_any)s libros, %(journal_article)s articulos, %(book_comic)s comics, %(magazine)s revistas — preservate pro semper."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Le plus grande bibliotheca de datos aperte e de codice aperte del mundo.<br>⭐️ Reflecte Scihub, Libgen, Zlib, e plus."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata incorrecte (ex. titulo, description, imagine de copertura)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Problemas de descargo (p.ex. non pote connecter, message de error, multo lente)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "File non pote esser aperite (p.ex. file corrumpite, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Qualitate basse (p.ex. problemas de formatation, qualitate de scansion basse, paginas mancante)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / file debe esser removite (p.ex. publicitate, contento abusive)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Reclamation de derectos de autor"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Altere"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Descargas de bonus"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Brillante Bibliophilo"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Fortunose Bibliothecario"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Deslumbrante Colector de Datos"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Incredibile Archivista"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s total"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) total"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "non pagate"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "pagate"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "cancellate"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "expirate"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "expectante confirmation de Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "invalid"

#, fuzzy
msgid "page.donate.title"
msgstr "Donar"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Tu ha un <a %(a_donation)s>donation existente</a> in progresso. Per favor, fini o cancella ille donation ante de facer un nove donation."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Vider tote mi donationes</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Le Archivo de Anna es un projecto sin scopo de lucro, de codice aperte, e de datos aperte. Donante e deveniente membro, tu supporta nostre operationes e disveloppamento. A tote nostre membros: gratias pro mantener nos in functionamento! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Pro plus informationes, consulta le <a %(a_donate)s>FAQ de Donationes</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Pro obtener ancora plus de discargas, <a %(a_refer)s>refere tu amicos</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Tu recipe %(percentage)s%% discargas rapide bonus, proque tu esseva referite per le usator %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Isto es applicabile al integre periodo de membresa."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s discargas rapide per die"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "si vos dona iste mense!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / mense"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Unir-se"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selectate"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "usque a %(percentage)s%% reductiones"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "Articulos de SciDB <strong>illimitate</strong> sin verification"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Accesso a <a %(a_api)s>API JSON</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Gania <strong>%(percentage)s%% discargas bonus</strong> per <a %(a_refer)s>referente amicos</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Tu nomine de usator o mention anonyme in le creditos"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Beneficios previe, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Accesso anticipate a nove functiones"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram exclusive con actualisationes detra le scena"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adopta un torrent”: tu nomine de usator o message in un nomine de file torrent <div %(div_months)s>una vice cata 12 menses de membro</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Stato legendarie in preservation del cognoscentia e cultura del humanitate"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Accesso de Experto"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "contacta nos"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Nos es un parve equipa de voluntarios. Il pote prender 1-2 septimanas pro responder."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Accesso</strong> illimitate a alte velocitate"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Servitores directes <strong>SFTP</strong>"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donation o cambio a nivello de enterprise pro nove collectiones (ex. nove scannos, datasets OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Nos accogni grande donationes de individuos ric o institutiones. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Pro donationes de plus de $5000, per favor contacta nos directemente a %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Sia conscie que durante le adhesiones in iste pagina es “per mense”, illos es donationes unic (non-recorrente). Vide le <a %(faq)s>FAQ de Donationes</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Si tu vole facer un donation (qualque summa) sin membro, senti te libere de usar iste adresse de Monero (XMR): %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Per favor selige un methodo de pagamento."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(temporarimente non disponibile)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s carta de dono"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Carta bancari (usante app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Carta de credito/debito"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regular)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Carta / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Credito/debito/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Carta bancari"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Carta de credito/debito (reserva)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Carta de credito/debito 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Con crypto vos pote donar usante BTC, ETH, XMR, e SOL. Usa iste option si tu es ja familiar con cryptocurrency."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Con crypto vos pote donar usante BTC, ETH, XMR, e plus."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Si vos usa crypto pro le prime vice, nos suggere usar %(options)s pro comprar e donar Bitcoin (le original e le plus usate cryptocurrency)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Pro donar usante PayPal US, nos va usar PayPal Crypto, que nos permitte remaner anonyme. Nos aprecia que tu prende le tempore pro apprender como donar usante iste methodo, pois que illo nos adjuta multo."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donar usante PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donar usante Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Si tu ha Cash App, isto es le maniera le plus facile de donar!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Nota que pro transactiones sub %(amount)s, Cash App pote imponer un %(fee)s taxa. Pro %(amount)s o plus, illo es gratuite!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Dona per medio de Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Si tu ha Revolut, isto es le maniera plus facile de donar!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donar con un carta de credito o debito."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay e Apple Pay poterea functionar anque."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Nota que pro donationes parve le taxas de carta de credito pote eliminar nostre disconto de %(discount)s%%, assi nos recommenda subscriptiones plus long."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Nota que pro donationes parve le taxas es alte, assi nos recommenda subscriptiones plus long."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Con Binance, tu compra Bitcoin con un carta de credito/debito o conto bancari, e postea dona iste Bitcoin a nos. In iste maniera nos pote remaner secur e anonyme quando accepta tu donation."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance es disponibile in quasi omne paises, e supporta le major parte de bancas e cartas de credito/debito. Iste es actualmente nostre recommendation principal. Nos aprecia que tu prende le tempore pro apprender como donar usante iste methodo, pois que illo nos adjuta multo."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Dona per medio de tu conto regular de PayPal."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Dona usante un carta de credito/debito, PayPal, o Venmo. Tu pote seliger inter iste optiones in le proxime pagina."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Dona usante un carta de dono de Amazon."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Nota que nos debe approximar a importos acceptate per nostre revendedores (minimo %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> Nos supporta solmente Amazon.com, non altere sitos de Amazon. Per exemplo, .de, .co.uk, .ca, NON es supportate."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>IMPORTANT: </strong> Iste option es pro %(amazon)s. Si tu vole usar un altere sito de Amazon, selige lo supra."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Iste methodo usa un fornitore de cryptomoneta como conversion intermedie. Isto pote esser un poco confusente, assi per favor usa iste methodo solmente si altere methodos de pagamento non functiona. Illo anque non functiona in omne paises."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Dona usante un carta de credito/debito, per le app Alipay (super facile a configurar)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installa app Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installa le app Alipay ab le <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registra te usante tu numero de telephono."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Nulle altere detalios personal es requirite."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Adde carta bancari"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Supportate: Visa, MasterCard, JCB, Diners Club e Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Vide <a %(a_alipay)s>iste guida</a> pro plus informationes."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Nos non pote supportar cartas de credito/debito directemente, proque le bancas non vole laborar con nos. ☹ Tamen, il ha plure manieras de usar cartas de credito/debito de altere methodos de pagamento:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Carta de Dono Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Invia nos cartas de dono de Amazon.com usante tu carta de credito/debito."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay supporta cartas de credito/debito international. Vide <a %(a_alipay)s>iste guida</a> pro plus information."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) supporta cartas de credito/debito international. In le app de WeChat, vade a “Io => Servicos => Portafolio => Adder un Carta”. Si tu non vide isto, activa lo usante “Io => Configurationes => General => Instrumentos => Weixin Pay => Activar”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Tu pote comprar crypto usante cartas de credito/debito."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Servicios crypto express"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Servicios express es convenibile, ma carga taxas plus alte."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Tu pote usar isto in loco de un cambio crypto si tu vole facer rapidemente un donation plus grande e non importa un taxa de $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Assecura te de inviar le exacto quantitate crypto monstrate in le pagina de donation, non le quantitate in $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Altrimenti le taxa essera subtrahite e nos non pote processar automaticamente tu adhesion."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimo: %(minimum)s dependente del pais, sin verification pro le prime transaction)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimo: %(minimum)s, sin verification pro le prime transaction)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimo: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimo: %(minimum)s, sin verification pro le prime transaction)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Si alcun de iste information es obsolete, per favor invia nos un email pro informar nos."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Pro cartas de credito, cartas de debito, Apple Pay, e Google Pay, nos usa “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In lor systema, un “cafe” es equal a $5, assi tu donation essera approximate al multiple de 5 plus proxime."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Selige pro quanto tempore tu vole subscribite."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 mense"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 menses"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 menses"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 menses"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 menses"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 menses"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 menses"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>post <span %(span_discount)s></span> discontos</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Iste methodo de pagamento require un minimo de %(amount)s. Per favor selige un different duration o methodo de pagamento."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donar"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Iste methodo de pagamento permitte solmente un maximo de %(amount)s. Per favor selige un different duration o methodo de pagamento."

#, fuzzy
msgid "page.donate.login2"
msgstr "Pro devenir un membro, per favor <a %(a_login)s>Aperi session o Registra te</a>. Gratias pro tu supporto!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Selige tu crypto moneta preferite:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(minimo importo le plus basse)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(usa quando invia Ethereum ab Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(advertimento: alte minimo importo)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Clicca le button donar pro confirmar iste donation."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donar <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Tu pote ancora cancellar le donation durante le checkout."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Redirigente al pagina de donation…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Alique iva mal. Per favor recarga le pagina e reproba."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / mense"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "pro 1 mense"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "pro 3 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "pro 6 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "pro 12 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "pro 24 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "pro 48 menses"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "pro 96 menses"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "pro 1 mense “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "pro 3 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "pro 6 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "pro 12 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "pro 24 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "pro 48 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "pro 96 menses “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donation"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Data: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mense pro %(duration)s menses, includente %(discounts)s%% disconto)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mense pro %(duration)s menses)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identificator: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Cancella"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Esque vos es secur de voler cancellar? Non cancellar si vos ha ja pagate."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Si, per favor cancellar"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Tu donation ha essite cancellate."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Facer un nove donation"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Altereva errava. Per favor recargar le pagina e reprobar."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Reordinar"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Tu ha ja pagate. Si tu vole revisar le instructiones de pagamento de omne modo, clicca hic:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Monstrar vetule instructiones de pagamento"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Gratias pro tu donation!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Si tu non ha ja facite, scribe tu clave secrete pro acceder:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Altrimenti tu poterea esser blocate de iste conto!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Le instructiones de pagamento es ora obsolete. Si tu vole facer un altere donation, usa le button “Reordinar” supra."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Nota importante:</strong> Le precios de crypto monetas pote fluctuar multo, a vices mesmo usque a 20%% in alcun minutas. Isto es ancora minus que le taxas que nos incurre con multe fornitore de pagamento, qui sovente demanda 50-60%% pro laborar con un “caritate in le umbra” como nos. <u>Si tu nos invia le recibo con le precio original que tu pagava, nos ancora accreditará tu conto pro le membro eligite</u> (a condition que le recibo non es plus vetule que alcun horas). Nos vermente aprecia que tu es disposte a tolerar cosas como isto pro supportar nos! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Iste donation ha expirate. Per favor cancellar e crear un nove."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Instructiones pro crypto"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Transferer a un de nostre contos crypto"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donar le summa total de %(total)s a un de iste adresses:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Comprar Bitcoin in Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Trova le pagina “Crypto” in tu app o sito web de PayPal. Isto es typicamente sub “Finantias”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Seque le instructiones pro comprar Bitcoin (BTC). Tu solmente debe comprar le quantitate que tu vole donar, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfere le Bitcoin a nostre adresse"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Vade al pagina “Bitcoin” in tu app o sito web de PayPal. Preme le button “Transfer”, %(transfer_icon)s, e postea “Inviar”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Entra nostre adresse de Bitcoin (BTC) como le recipiente, e seque le instructiones pro inviar tu donation de %(total)s:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Instructiones pro carta de credito / debito"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Dona per nostre pagina de carta de credito / debito"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Dona %(amount)s in <a %(a_page)s>iste pagina</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Vide le guida passo a passo infra."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Stato:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Expectante confirmation (refresca le pagina pro verificar)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Expectante transferentia (refresca le pagina pro verificar)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tempore restante:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(tu poterea voler cancellar e crear un nove donation)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Pro reinitialisar le temporisator, simplemente crea un nove donation."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Actualisar stato"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "Si tu incontra qualcun problemas, per favor contacta nos a %(email)s e include tanto informationes como possibile (como capturas de schermo)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Si vos ha ja pagate:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "A vices, le confirmation pote prender usque 24 horas, assi assecurate vos de refrescar iste pagina (mesmo si illo ha expirate)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Compra moneta PYUSD in PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Seque le instructiones pro comprar moneta PYUSD (PayPal USD)."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Compra un poco plus (nos recommenda %(more)s plus) que le quantitate que tu dona (%(amount)s), pro coperir taxas de transaction. Tu retenera quidque que remane."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Va al pagina “PYUSD” in tu app o sito web de PayPal. Preme le button “Transfer” %(icon)s, e postea “Send”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Transfere %(amount)s a %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Compra Bitcoin (BTC) in Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Vade al pagina “Bitcoin” (BTC) in Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Compra un poco plus (nos recommendamos %(more)s plus) que le summa que tu dona (%(amount)s), pro coperir le taxas de transaction. Tu retine lo que resta."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Transfere le Bitcoin a nostre adresse"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Clicca le button “Inviar bitcoin” pro facer un “retraction”. Cambia de dollars a BTC per premer le icone %(icon)s. Insere le summa de BTC infra e clicca “Inviar”. Vide <a %(help_video)s>iste video</a> si tu ha difficultate."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Pro parve donationes (sub $25), tu poterea necessitar usar Rush o Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Compra Bitcoin (BTC) in Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Vade al pagina “Crypto” in Revolut pro comprar Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Compra un poco plus (nos recommendamos %(more)s plus) que le summa que tu dona (%(amount)s), pro coperir le taxas de transaction. Tu retine lo que resta."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Transfere le Bitcoin a nostre adresse"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Clicca le button “Inviar bitcoin” pro facer un “retraction”. Cambia de euros a BTC per premer le icone %(icon)s. Insere le summa de BTC infra e clicca “Inviar”. Vide <a %(help_video)s>iste video</a> si tu ha difficultate."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Assecura te usar le quantitate de BTC infra, <em>NON</em> euros o dollares, alteremente nos non recipera le quantitate correcte e non potera confirmar automaticamente tu adhesion."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Pro parve donationes (sub $25) tu poterea necessitar usar Rush o Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Usa qualcunque del sequente servitios “carta de credito a Bitcoin” express, que prende solmente alcun minutas:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Completa le sequente detalios in le formulario:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Quantitate de Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Per favor usa iste <span %(underline)s>quantitate exacte</span>. Tu costo total poterea esser plus alte a causa de taxas de carta de credito. Pro parve quantitates isto poterea esser plus que nostre disconto, malfortunatemente."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Adresse de Bitcoin (portafolio externe):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instructiones"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Nos supporta solmente le version standard de monetas crypto, non redes o versiones exotic de monetas. Il pote prender usque un hora pro confirmar le transaction, dependente del moneta."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scannar le codice QR pro pagar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scanna iste codice QR con tu application de portamoneta crypto pro rapidemente completar le detalios de pagamento"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Carta de dono Amazon"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Per favor usa le <a %(a_form)s>formulario official de Amazon.com</a> pro inviar nos un carta de dono de %(amount)s al adresse de email infra."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Nos non pote acceptar altere methodos de cartas de dono, <strong>solmente inviate directemente del formulario official in Amazon.com</strong>. Nos non pote retornar tu carta de dono si tu non usa iste formulario."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Entra le summa exacte: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Per favor NON scribe tu proprie message."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "Email del recipiente “A” in le formulario:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unic a tu conto, non divider."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Usa solmente un vice."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Expectante le carta de dono… (refresca le pagina pro verificar)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Post inviar tu carta de dono, nostre systema automate lo confirmara intra alcun minutas. Si isto non functiona, prova reinviar tu carta de dono (<a %(a_instr)s>instructiones</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "Si isto ancora non functiona, per favor invia nos un email e Anna lo revisara manualmente (isto pote prender alcun dies), e sia secur de mentionar si tu ha ja probate reinviar."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Exemplo:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Nota que le nomine del conto o photo pote parer estranie. Non te preoccupa! Iste contos es gestionate per nostre partenarios de donation. Nostre contos non ha essite hackate."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Instructiones pro Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span> Dona in Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Dona le total de %(total)s usante <a %(a_account)s>iste conto de Alipay</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Si le pagina de donation es blocate, prova un connexion de internet differente (p.ex. VPN o internet de telephono)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Infelicemente, le pagina de Alipay es sovente solmente accessibile de <strong>China continental</strong>. Tu pote haber necessitate de disactivar temporarimente tu VPN, o usar un VPN a China continental (o Hong Kong functiona a vices tamben)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Face donation (scanna codice QR o preme button)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Aperi le <a %(a_href)s>pagina de donation per codice QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scanna le codice QR con le app Alipay, o preme le button pro aperir le app Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Per favor, sia patiente; le pagina pote prender un poco de tempore pro cargar pois que illo es in China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Instructiones pro WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donar per WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donar le summa de %(total)s usante <a %(a_account)s>iste conto de WeChat</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Instructiones pro Pix"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donar per Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donar le summa de %(total)s usante <a %(a_account)s>iste conto de Pix</a>"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Inviar nos le recibo per email"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Invia un recipto o screenshot a tu adresse personal de verification. NON usa iste adresse de email pro tu donation PayPal."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Inviar un recibo o screenshot a tu adresse personal de verification:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "Si le taxa de cambio crypto fluctuava durante le transaction, assecurar de includer le recibo monstrante le taxa de cambio original. Nos vermente aprecia que tu prende le effortio de usar crypto, illo nos adjuta multo!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Quando tu ha inviate tu recibo per email, clicca iste button, assi Anna pote revisar lo manualmente (isto pote prender alcun dies):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Si, io ha inviate mi recibo per email"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Gratias pro tu donation! Anna activara manualmente tu adhesion intra alcun dies."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Alicun cosa errava. Per favor recarga le pagina e reproba."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Guida passo a passo"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Alcun del passos mentiona portafolios crypto, ma non te preoccupa, tu non debe apprender nihil super crypto pro isto."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Insere tu email."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Selige tu methodo de pagamento."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selige tu methodo de pagamento de novo."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Selige “Portafolio auto-hospitate”."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Clicca “Io confirma proprietate”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "Vos debe reciper un recipto per email. Per favor invia lo a nos, e nos confirmara vostre donation tan tosto como possibile."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Per favor attende al minus <span %(span_hours)s>24 horas</span> (e refresca iste pagina) ante de contactar nos."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Si vos faceva un error durante le pagamento, nos non pote facer restitutiones, ma nos essayara de corriger lo."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Mi donationes"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Le detalios del donationes non es publicamente visibile."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nulle donationes ancora. <a %(a_donate)s>Face mi prime donation.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Face un altere donation."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Files discargate"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Discargas ab Servitores Partner Rapid es marcate per %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Si vos discargava un file con ambe discargas rapide e lente, illo apparera duo vices."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Discargas rapide in le ultime 24 horas conta verso le limite quotidian."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Tote le horas es in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Files discargate non es publicamente visibile."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Nulle files discargate ancora."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Ultime 18 horas"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Plus tosto"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Conto"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Aperir session / Registrar"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID del conto: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Profilo public: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Clave secret (non divider!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "monstrar"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Adhesion: <strong>%(tier_name)s</strong> usque %(until_date)s <a %(a_extend)s>(extender)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Adhesion: <strong>Nulle</strong> <a %(a_become)s>(devenir membro)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Downloads rapide usate (ultime 24 horas): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "qual downloads?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Grupo exclusive in Telegram: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Junge nos hic!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Actualiza a un <a %(a_tier)s>nivel superior</a> pro junger nostre gruppo."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Contacta Anna a %(email)s si tu es interessate in actualisar tu membro a un nivel superior."

#, fuzzy
msgid "page.contact.title"
msgstr "Email de contacto"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Tu pote combinar multiple membros (downloads rapide per 24 horas essera addite insimul)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profilo public"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Files discargate"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Mi donationes"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Disconnexion"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Tu es ora disconectate. Recarica le pagina pro reconnecter te."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Alique errava. Per favor recarica le pagina e reproba."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registration succedite! Tu clave secret es: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Salva iste clave con cura. Si tu lo perde, tu perdera accesso a tu conto."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Marcapagina.</strong> Tu pote marcar iste pagina pro recuperar tu clave.</li><li %(li_item)s><strong>Discarga.</strong> Clicca <a %(a_download)s>iste ligamine</a> pro discargar tu clave.</li><li %(li_item)s><strong>Manager de contrasignos.</strong> Usa un manager de contrasignos pro salvar le clave quando tu lo entra infra.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Entra tu clave secret pro connecter te:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Clave secret"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Connexion"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Clave secret invalide. Verifica tu clave e reproba, o alternativemente registra un nove conto infra."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Non perde tu clave!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Non ha ancora un conto?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registrar nove conto"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Si tu perdeva tu clave, per favor <a %(a_contact)s>contacta nos</a> e provide tanto information como possibile."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Tu poterea deber temporarimente crear un nove conto pro contactar nos."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Vetule conto basate in email? Entra tu <a %(a_open)s>email hic</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Lista"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "editar"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Salveguardar"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Salveguardate. Per favor recarga le pagina."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Algo errava. Per favor reproba."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Lista per %(by)s, create <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Lista es vacue."

#, fuzzy
msgid "page.list.new_item"
msgstr "Adder o remover de iste lista per trovar un file e aperir le scheda “Listas”."

#, fuzzy
msgid "page.profile.title"
msgstr "Profilo"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profilo non trovate."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "editar"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Cambia tu nomine de visualisation. Tu identificator (le parte post “#”) non pote esser cambiate."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Salveguardar"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Salveguardate. Per favor recarga le pagina."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Algo errava. Per favor reproba."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profilo create <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Listas"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Nulle listas ancora"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Crear un nove lista per trovar un file e aperir le scheda “Listas”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Reforma del derecto de autor es necesse pro securitate national"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: LLMs chinese (incluse DeepSeek) es educate super mi archivo illegal de libros e documentos — le plus grande del mundo. Le West debe reformar le derecto de autor como un questione de securitate national."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "articulos de compania per TorrentFreak: <a %(torrentfreak)s>prime</a>, <a %(torrentfreak_2)s>secunde</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Non multo tempore retro, le “bibliothecas de umbra” moriva. Sci-Hub, le massive archivo illegal de documentos academic, habeva cessate de acceptar nove obras, a causa de actiones legal. “Z-Library”, le plus grande bibliotheca illegal de libros, videva su allegate creatores arrestate pro accusationes criminal de derecto de autor. Illes incredibilemente succedeva a escappar de lor arresto, ma lor bibliotheca non es minus sub menacia."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Quando Z-Library affrontava clausura, io habeva ja facite un copia de securitate de su integre bibliotheca e cercava un platforma pro hospitar lo. Isto esseva mi motivation pro comenciar le Archivo de Anna: un continuation del mission detra de illos initiativas anterior. Nos ha crescite desde alora pro devenir le plus grande bibliotheca de umbra del mundo, hospitante plus de 140 milliones de textos sub derecto de autor in numerose formatos — libros, documentos academic, revistas, jornales, e ultra."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mi equipa e io es ideologos. Nos crede que preservar e hospitar iste files es moralmente juste. Bibliothecas circa le mundo vide reductiones de financiation, e nos non pote confider le hereditage del humanitate a corporationes."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Tunc veniva le IA. Praticamente tote le companias major que construe LLMs nos contactava pro educar super nostre datos. Le majoritate (ma non tote!) del companias basate in le US repensava quando illes realisava le natura illegal de nostre obra. In contrasto, firmas chinese ha enthusiastemente adoptate nostre collection, apparentemente non preoccupate per su legalitate. Isto es notabile date le rolo de China como signatario a quasi tote le tractatos international de derecto de autor."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Nos ha date accesso a alte velocitate a circa 30 companias. Le majoritate de illes es companias de LLM, e alcunes es intermediarios de datos, qui revendera nostre collection. Le majoritate es chinese, ben que nos ha etiam collaborate con companias del US, Europa, Russia, Corea del Sud, e Japon. DeepSeek <a %(arxiv)s>admitteva</a> que un version anterior esseva educate super parte de nostre collection, ben que illes es reticente super lor ultime modello (probabilemente etiam educate super nostre datos tamen)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Si le West vole remaner in le avante in le curso de LLMs, e ultimamente, AGI, illo debe reconsiderar su position super le derecto de autor, e presto. Que vos sia de accordo con nos o non super nostre caso moral, isto deveni ora un caso de economia, e mesmo de securitate national. Tote le blocos de poter sta construente super-scientificos artificial, super-hackers, e super-militares. Le libertate de information deveni un questione de supraviventia pro iste paises — mesmo un questione de securitate national."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Nostre equipa es de tote le mundo, e nos non ha un alignment particular. Ma nos incoragiaria paises con forte leges de derecto de autor a usar iste menacia existential pro reformar los. Alora que facer?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Nostre prime recommendation es simple: accurtar le termino de derecto de autor. In le US, le derecto de autor es concedite pro 70 annos post le morte del autor. Isto es absurd. Nos pote alignar isto con patentes, que es concedite pro 20 annos post le deposito. Isto deberea esser plus que sufficiente tempore pro autores de libros, documentos, musica, arte, e altere obras creative, pro esser plenmente compensate pro lor effortios (incluse projectos a longe termino como adaptationes cinematographic)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Alora, al minimo, legislatores deberea includer exceptiones pro le preservation e dissemination in massa de textos. Si le perdita de entrata ab clientes individual es le principal preoccupation, le distribution a nivello personal poterea remaner prohibite. In vice, illes capabile de gestionar vaste repositorios — companias que educa LLMs, insimul con bibliothecas e altere archivos — esserea coperite per iste exceptiones."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Alcun paises ja face un version de isto. TorrentFreak <a %(torrentfreak)s>reportava</a> que China e Japon ha introducite exceptiones de IA a lor leges de derecto de autor. Es incert pro nos como isto interage con tractatos international, ma illo certemente da cobertura a lor companias domestic, lo que explica lo que nos ha vidite."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Quanto al Archivo de Anna — nos continuara nostre labor clandestin radicate in conviction moral. Tamen nostre plus grande desiro es entrar in le lumine, e amplificar nostre impacto legalmente. Per favor reformar le derecto de autor."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Leger le articulos de compania per TorrentFreak: <a %(torrentfreak)s>prime</a>, <a %(torrentfreak_2)s>secunde</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vincitores del premio de $10,000 pro visualisation de ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Nos recipeva alcun submissiones incredibile pro le premio de $10,000 pro visualisation de ISBN."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Alcun menses retro nos annunciava un <a %(all_isbns)s>premio de $10,000</a> pro facer le melior visualisation possibile de nostre datos monstrante le spatio de ISBN. Nos emphatisava monstrar qual files nos ha/nos non ha ja archiviate, e nos plus tarde un dataset describente quante bibliothecas contine ISBNs (un mesura de raritate)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Nos ha essite submergite per le responsa. Il habeva tanto creativitate. Un grande gratias a omnes qui ha participate: vostre energia e enthusiasmo es contagiose!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "In ultime, nos voleva responder al sequente questiones: <strong>qual libros existe in le mundo, quanto nos ha jam archivate, e a qual libros nos deberea prestar attention postea?</strong> Es meraviliose vider tanto personas interessar se de iste questiones."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Nos comenciava con un visualisation basic nos mesme. In minus de 300kb, iste imagine representa succintemente le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tote le ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Files in le Archivo de Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Fuga de datos de CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indice de eBooks de EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Archivo de Internet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Registro Global de Editores de ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Bibliotheca Estatal Russe"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Bibliotheca Imperial de Trantor"

#, fuzzy
msgid "common.back"
msgstr "Retro"

#, fuzzy
msgid "common.forward"
msgstr "Avanti"

#, fuzzy
msgid "common.last"
msgstr "Ultime"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Per favor, vide le <a %(all_isbns)s>poste original del blog</a> pro plus information."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Nos emittiva un desafio pro meliorar isto. Nos haberea premiate un prime loco con $6,000, un secunde loco con $3,000, e un tertie loco con $1,000. A causa del responsa travolgente e del submissiones incredibile, nos ha decidite augmentar un poco le fundo de premios, e premiar un tertie loco quadruple de $500 cata uno. Le ganatores es sub, ma assecurate de reguardar tote le submissiones <a %(annas_archive)s>hic</a>, o discargar nostre <a %(a_2025_01_isbn_visualization_files)s>torrent combinate</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Prime loco $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Iste <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>commento in Gitlab</a>) es simplemente toto lo que nos voleva, e plus! Nos specialmente apprecia le optiones de visualisation incredibilemente flexible (mesmo supportante shaders personalisate), ma con un lista comprensive de presets. Nos etiam apprecia quanto rapide e fluide toto es, le implementation simple (que non ha mesmo un backend), le minimappa ingeniose, e le explication extensive in lor <a %(phiresky_github)s>poste de blog</a>. Laboro incredibile, e le ganator ben meritate!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Secunde loco $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Un altere <a %(annas_archive_note_2913)s>submission</a> incredibile. Non tanto flexible como le prime loco, ma nos de facto prefereva su visualisation a nivello macro super le prime loco (curva de plenamento de spatio, margines, etichettas, evidentiation, panoramica, e zoom). Un <a %(annas_archive_note_2971)s>commento</a> per Joe Davis resonava con nos:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Durante que quadratos e rectangulos perfecte es mathematicamente agradabile, illos non provide superior localitate in un contexto de mappatura. Io crede que le asymmetria inherente in iste Hilbert o Morton classic non es un defecto ma un characteristica. Justo como le outline famose in forma de bota de Italia lo rende immediatemente recognoscibile in un mappa, le \"peculiaritates\" unic de iste curvas pote servir como punctos de referencia cognoscitive. Iste distinctivitate pote meliorar le memoria spatial e adjutar usatores a orientar se, potentialmente rendente plus facile localizar regiones specific o notar patronos.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "E ancora multe optiones pro visualisar e renderisar, assi como un UI incredibilemente fluide e intuitive. Un solide secunde loco!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tertie loco $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In iste <a %(annas_archive_note_2940)s>submission</a> nos vermente apprecia le differentes typos de vistas, in particular le vistas de comparation e de editor."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tertie loco $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Durante que non le UI le plus polite, iste <a %(annas_archive_note_2917)s>submission</a> verifica multe del cassetta. Nos apprecia specialmente su function de comparation."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tertie loco $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Como le prime loco, iste <a %(annas_archive_note_2975)s>submission</a> nos impressionava con su flexibilitate. In ultime, isto es lo que face un grande instrumento de visualisation: flexibilitate maximal pro usatores potente, durante que mantene cosas simple pro usatores medie."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tertie loco $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Le ultime <a %(annas_archive_note_2947)s>submission</a> a reciper un premio es bastante basic, ma ha alcun characteristicas unic que nos vermente apprecia. Nos apprecia como illes monstra quante datasets coperi un particular ISBN como un mesura de popularitate/affidabilitate. Nos etiam vermente apprecia le simplicitate ma efficacia de usar un cursor de opacitate pro comparationes."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ideas notabile"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Alcun altere ideas e implementationes que nos apprecia specialmente:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Grattacielos pro raritate"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statisticas in directo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotationes, e etiam statisticas in directo"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Vista unic de mappa e filtros"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Schema de colores fresc e mappa de calor."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Facile alternar de datasets pro comparationes rapide."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Etiquetas belle."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Barra de scala con numero de libros."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Multo de cursors pro comparar datasets, como si vos es un DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Nos poterea continuar per un tempore, ma lassos nos arrestar hic. Assecura te de reguardar tote le submissiones <a %(annas_archive)s>hic</a>, o discargar nostre <a %(a_2025_01_isbn_visualization_files)s>torrent combinate</a>. Tanto submissiones, e cata uno apporta un perspectiva unic, sia in UI o in implementation."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Nos al minus incorporara le submission de prime loco in nostre sito web principal, e forsan alcunes alteres. Nos ha etiam comenciate a pensar super como organisar le processo de identificar, confirmar, e tunc archivar le libros le plus rare. Plus a venir in iste fronto."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Gratias a omnes qui participava. Es stupefaciente que tanto personas se importa."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Nostre cordes es plen de gratia."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisar Tote le ISBNs — $10,000 recompensa per 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Iste imagine representa le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Iste imagine es 1000×800 pixels. Cata pixel representa 2,500 ISBNs. Si nos ha un file pro un ISBN, nos face ille pixel plus verde. Si nos sape que un ISBN ha essite emittite, ma nos non ha un file correspondente, nos lo face plus rubie."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In minus de 300kb, iste imagine representa succintemente le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate (alcun centena de GB compresse in plen)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ille etiam monstra: il ha multo de labor remanente in backupar libros (nos solmente ha 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Fundo"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Como pote le Archivo de Anna attinger su mission de facer un copia de securitate de tote le cognoscentia del humanitate, sin saper qual libros es ancora ibi? Nos necessita un lista de cosas a facer. Un maniera de mappar isto es per le numeros ISBN, que desde le annos 1970 ha essite assignate a cata libro publicate (in le major parte del paises)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Il non ha un autoritate central que sape tote le assignationes de ISBN. In vice, es un systema distribuite, ubi le paises recipe intervallos de numeros, que alora assigna intervallos plus parve a editoras major, qui poterea ulteriormente subdivider intervallos a editoras minor. Finalmente, numeros individual es assignate a libros."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Nos comenciava a mappar ISBNs <a %(blog)s>duo annos retro</a> con nostre extraction de ISBNdb. Desde alora, nos ha extrahite multe plus de fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e plus. Un lista complete pote esser trovate in le paginas “Datasets” e “Torrents” in le Archivo de Anna. Nos ha ora de longe le plus grande collection completemente aperte e facilemente discargabile de metadata de libros (e dunque ISBNs) in le mundo."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Nos ha <a %(blog)s>scribite extensivemente</a> super le rationes proque nos importa le preservation, e proque nos es actualmente in un fenestra critic. Nos debe ora identificar libros rare, subfocalisate, e unicmente a risco e preservar los. Haver bon metadata super tote le libros in le mundo adjuta con isto."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisar"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "A parte del imagine de vista general, nos pote etiam regardar datasets individual que nos ha acquirite. Usa le menu a cascada e le buttones pro cambiar inter illos."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Il ha multe patronos interessante a vider in iste imagines. Proque il ha un regularitate de lineas e blocos, que pare occurrer a differentes scales? Que es le areas vacue? Proque certe datasets es tanto agglomerate? Nos lassara iste questiones como un exercitio pro le lector."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 recompensa"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Il ha multo a explorar hic, assi nos annuncia un recompensa pro meliorar le visualisation supra. A differente de multe de nostre recompensas, iste es limitate in tempore. Tu debe <a %(annas_archive)s>submitter</a> tu codice open source ante le 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Le melior submission recipera $6,000, le secunde loco es $3,000, e le tertie loco es $1,000. Tote le recompensas essera distribuite usante Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Infra es le criterios minimal. Si nulle submission attinge le criterios, nos poterea ancora assignar alcun recompensas, ma isto essera a nostre discretion."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forka iste repo, e edita iste post de blog HTML (nulle altere backendes a parte de nostre backend Flask es permittite)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Face que le imagine supra sia suavemente zoomabile, assi tu pote zoomar tote le via usque a ISBNs individual. Cliccar ISBNs debe portar te a un pagina de metadata o recerca in le Archivo de Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Tu debe ancora poter cambiar inter tote le differente datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Intervalles de paises e editoras debe esser sublineate al passar le cursor. Tu pote usar p.ex. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> pro information de paises, e nostre extraction “isbngrp” pro editoras (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Il debe functionar ben sur desktop e mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Pro punctos extra (iste es solmente ideas — lassa tu creativitate correr libere):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Consideration forte essera date al usabilitate e como bon illo pare."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Monstra ver metadata pro ISBNs individual quando zoomante, como titulo e autor."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Melior curva de plenamento de spatio. P.ex. un zig-zag, vadente de 0 a 4 in le prime fila e alora retro (in reverso) de 5 a 9 in le secunde fila — applicate recursivemente."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Schemas de color differente o personalisabile."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Visiones special pro comparar datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Modos de debuggar problemas, como altere metadata que non concorda ben (p.ex. titulos multo differente)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotar imagines con commentos super ISBNs o intervallos."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Qualque heuristicas pro identificar libros rare o a risco."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Qualque ideas creative que tu pote imaginar!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Tu POTE deviar completemente del criterios minimal, e facer un visualisation completemente differente. Si illo es vermente spectacular, alora illo qualifica pro le recompensa, ma a nostre discretion."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Face submissiones per postar un commento a <a %(annas_archive)s>iste questione</a> con un ligamine a tu repo bifurcate, requesta de fusion, o diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Codice"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Le codice pro generar iste imagines, assi como altere exemplos, pote esser trovate in <a %(annas_archive)s>iste directorio</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Nos ha create un formato de datos compacte, con le qual tote le information ISBN requirite es circa 75MB (compresite). Le description del formato de datos e le codice pro generar lo pote esser trovate <a %(annas_archive_l1244_1319)s>hic</a>. Pro le recompensa tu non es requirite usar isto, ma illo es probabilemente le formato le plus convenibile pro comenciar. Tu pote transformar nostre metadata como tu vole (ben que tote tu codice debe esser open source)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Nos non pote expectar a vider lo que tu crea. Bon fortuna!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Contenitores del Archivo de Anna (AAC): standardisar le publicationes del plus grande bibliotheca umbra del mundo"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Le Archivo de Anna ha devenite le plus grande bibliotheca umbra del mundo, requirente nos a standardisar nostre publicationes."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Le Archivo de Anna</a> ha devenite de longe le plus grande bibliotheca umbra del mundo, e le sol bibliotheca umbra de su scala que es completemente open-source e open-data. Infra es un tabula de nostre pagina de Datasets (legiermente modificate):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Nos ha realisate isto in tres modos:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Reflecter bibliothecas umbra open-data existente (como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Adjutar bibliothecas umbra que vole esser plus aperte, ma non habeva le tempore o recursos pro facer lo (como le collection de comics de Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Raspar bibliothecas que non desira compartir in massa (como Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Pro (2) e (3) nos administra ora un collection considerabile de torrents nos mesme (centos de TBs). Usque ora nos ha approchate iste collectiones como unicas, significante infrastructura e organisation de datos personalisate pro cata collection. Isto adde un carga significative a cata publication, e rende specialmente difficile facer publicationes plus incremental."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Es pro isto que nos ha decidite standardisar nostre publicationes. Isto es un articulo technic in le qual nos introduce nostre standard: <strong>Contenitores de Archivo de Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Objectivos de designo"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Nostre caso de uso primari es le distribution de files e metadata associate ab differente collectiones existente. Nostre considerationes le plus importante es:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Files e metadata heterogenee, in un formato tanto proxime al original como possibile."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Identificatores heterogenee in le bibliothecas fonte, o mesmo le absentia de identificatores."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Publicationes separate de metadata contra datos de files, o publicationes solmente de metadata (p.ex. nostre publication de ISBNdb)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribution via torrents, ben que con le possibilitate de altere methodos de distribution (p.ex. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Registros immutabile, pois nos debe supponer que nostre torrents vivera pro semper."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Publicationes incremental / publicationes appendibile."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Legibile e scribibile per machina, convenientemente e rapidemente, specialmente pro nostre pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Inspection human un tanto facile, ben que isto es secundari al legibilitate per machina."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Facile de semenar nostre collectiones con un seedbox standard locabile."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Datos binari pote esser servite directemente per servitores web como Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Alcunos non-objectivos:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Nos non importa que le files sia facile a navigar manualmente in disco, o cercabile sin pre-processamento."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Nos non importa esser directemente compatibile con software de bibliotheca existente."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Durante que illo debe esser facile pro alicuno semenar nostre collection usante torrents, nos non expecta que le files sia usabile sin cognoscentia technic significative e compromisso."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Como le Archivo de Anna es open source, nos vole usar nostre formato directemente. Quando nos refresca nostre indice de recerca, nos solmente accede camminos publicamente disponibile, assi que alicuno qui bifurca nostre bibliotheca pote comenciar rapidemente."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Le standard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Finalmente, nos decidemos por un estándar relativamente simple. Es bastante flexible, no normativo y un trabajo en progreso."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Contenitor del Archivo de Anna) es un ítem único que consiste en <strong>metadata</strong>, y opcionalmente <strong>datos binarios</strong>, ambos inmutables. Tiene un identificador único global, llamado <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collection.</strong> Cada AAC pertenece a una colección, que por definición es una lista de AACs que son semánticamente consistentes. Esto significa que si haces un cambio significativo en el formato de los metadata, entonces debes crear una nueva colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” y “files” collections.</strong> Por convención, a menudo es conveniente lanzar “records” y “files” como colecciones diferentes, para que puedan ser lanzadas en diferentes horarios, por ejemplo, basados en tasas de scraping. Un “record” es una colección solo de metadata, que contiene información como títulos de libros, autores, ISBNs, etc., mientras que “files” son las colecciones que contienen los archivos reales (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> El formato de AACID es este: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por ejemplo, un AACID real que hemos lanzado es <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: el nombre de la colección, que puede contener letras ASCII, números y guiones bajos (pero no dobles guiones bajos)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: una versión corta del ISO 8601, siempre en UTC, por ejemplo, <code>20220723T194746Z</code>. Este número debe aumentar de manera monótona para cada lanzamiento, aunque su semántica exacta puede diferir por colección. Sugerimos usar el tiempo de scraping o de generación del ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: un identificador específico de la colección, si es aplicable, por ejemplo, el ID de Z-Library. Puede ser omitido o truncado. Debe ser omitido o truncado si el AACID excedería de otro modo los 150 caracteres."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por ejemplo, usando base57. Actualmente usamos la biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID range.</strong> Dado que los AACIDs contienen timestamps que aumentan de manera monótona, podemos usar eso para denotar rangos dentro de una colección particular. Usamos este formato: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, donde los timestamps son inclusivos. Esto es consistente con la notación ISO 8601. Los rangos son continuos y pueden superponerse, pero en caso de superposición deben contener registros idénticos a los previamente lanzados en esa colección (ya que los AACs son inmutables). No se permiten registros faltantes."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata file.</strong> Un archivo de metadata contiene los metadata de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "El nombre del archivo debe ser un rango de AACID, precedido por <code style=\"color: red\">annas_archive_meta__</code> y seguido por <code>.jsonl.zstd</code>. Por ejemplo, uno de nuestros lanzamientos se llama<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Como indica la extensión del archivo, el tipo de archivo es <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Cada objeto JSON debe contener los siguientes campos en el nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). No se permiten otros campos."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> es metadata arbitrario, según la semántica de la colección. Debe ser semánticamente consistente dentro de la colección."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> es opcional, y es el nombre de la carpeta de datos binarios que contiene los datos binarios correspondientes. El nombre del archivo de los datos binarios correspondientes dentro de esa carpeta es el AACID del registro."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "El prefijo <code style=\"color: red\">annas_archive_meta__</code> puede adaptarse al nombre de su institución, por ejemplo, <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binary data folder.</strong> Una carpeta con los datos binarios de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "El nombre del directorio debe ser un rango de AACID, precedido por <code style=\"color: green\">annas_archive_data__</code>, y sin sufijo. Por ejemplo, uno de nuestros lanzamientos reales tiene un directorio llamado<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "El directorio debe contener archivos de datos para todos los AACs dentro del rango especificado. Cada archivo de datos debe tener su AACID como el nombre del archivo (sin extensiones)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Es es recommendate facer iste folders un poco gestionabile in grandor, p.ex. non plus grande que 100GB-1TB cata uno, ben que iste recommendation pote cambiar con le tempore."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Le files de metadata e le folders de datos binari pote esser includite in torrents, con un torrent per file de metadata, o un torrent per folder de datos binari. Le torrents debe haber le nomine original del file/directorio plus un suffixo <code>.torrent</code> como lor nomine de file."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exemplo"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Vamos considerar nostre recente publication de Z-Library como un exemplo. Illo consiste de duo collectiones: “<span style=\"background: #fffaa3\">zlib3_records</span>” e “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Isto nos permitte raspar e publicar separatamente le registros de metadata del files de libros actual. Assi, nos publicava duo torrents con files de metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Nos tamben publicava un serie de torrents con folders de datos binari, ma solmente pro le collection “<span style=\"background: #ffd6fe\">zlib3_files</span>”, 62 in total:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Per executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> nos pote vider lo que es intra:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In iste caso, es metadata de un libro como reportate per Z-Library. Al nivello superior nos ha solmente “aacid” e “metadata”, ma non “data_folder”, pois que il non ha datos binari correspondente. Le AACID contine “22430000” como le ID primari, que nos pote vider es prendite de “zlibrary_id”. Nos pote expectar que altere AACs in iste collection ha le mesme structura."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Ora vamos executar <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Isto es un metadata de AAC multo plus parve, ben que le major parte de iste AAC es situate alicubi altere in un file binari! Finalmente, nos ha un “data_folder” iste vice, assi nos pote expectar que le datos binari correspondente es situate a <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Le “metadata” contine le “zlibrary_id”, assi nos pote facilemente associar lo con le AAC correspondente in le collection “zlib_records”. Nos poterea haber associate in un numero de manieras differente, p.ex. per AACID — le standard non prescribe isto."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Nota que non es necessari que le campo “metadata” ipse sia JSON. Illo poterea esser un catena de characteres continente XML o qualcunque altere formato de datos. Tu poterea mesmo immagazinar information de metadata in le blob binari associate, p.ex. si illo es multo datos."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Conclusion"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Con iste standard, nos pote facer publicationes plus incrementalmente, e plus facilemente adder nove fontes de datos. Nos ja ha alcun publicationes excitante in le pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Nos tamben spera que illo deveni plus facile pro altere bibliothecas umbra reflecter nostre collectiones. Finalmente, nostre objectivo es preservar le cognoscentia e cultura human pro semper, assi plus de redundantia es melior."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Le Actualisation de Anna: archivo totalmente open source, ElasticSearch, 300GB+ de coperturas de libros"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Nos ha laborate sin pausa pro provider un bon alternative con le Archivo de Anna. Ecce alcun del cosas que nos ha attingite recentemente."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Con Z-Library que cadeva e su (presumite) fundatores que esseva arrestate, nos ha laborate sin pausa pro provider un bon alternative con le Archivo de Anna (nos non lo ligara hic, ma tu pote cercar lo in Google). Ecce alcun del cosas que nos ha attingite recentemente."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Le Archivo de Anna es totalmente open source"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Nos crede que le information debe esser libere, e nostre proprie codice non es un exception. Nos ha publicate tote nostre codice in nostre istantia de Gitlab hospitate privatemente: <a %(annas_archive)s>Le Software de Anna</a>. Nos tamben usa le tracker de questiones pro organisar nostre labor. Si tu vole ingagiar te con nostre disveloppamento, isto es un bon loco pro comenciar."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Pro dar te un gustate del cosas que nos labora super, considera nostre labor recente super meliorationes de performantia del latere del cliente. Pois que nos non ha ancora implementate pagination, nos sovente retornava paginas de recerca multo longe, con 100-200 resultatos. Nos non voleva truncar le resultatos de recerca troppo tosto, ma isto significava que illo retardava alcun apparatos. Pro isto, nos implementava un parve astutia: nos envolteva le major parte del resultatos de recerca in commentos HTML (<code><!-- --></code>), e pois scribeva un parve Javascript que detectava quando un resultato deberea devenir visibile, al qual momento nos disenvolveva le commento:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisation\" implementate in 23 lineas, sin necessitate de bibliothecas elaborate! Isto es le sorta de codice pragmatico rapide que tu obtene quando tu ha tempore limitate e problemas real que necessita esser resolvite. Il ha essite reportate que nostre cerca ora functiona ben sur dispositivos lente!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Un altere grande effortio esseva automatizar le construction del base de datos. Quando nos lanciate, nos simplemente colligite differentes fontes de maniera casual. Ora nos vole mantener los actualisate, assi nos scribite un serie de scriptos pro discargar nove metadata ab le duo forcas de Library Genesis, e integrar los. Le objectivo es non solmente facer isto utile pro nostre archivo, ma etiam facer le cosas facile pro alicuno qui vole experimentar con metadata de bibliotheca umbra. Le objectivo esserea un notebook Jupyter que ha omne sorta de metadata interessante disponibile, assi nos pote facer plus de recerca como determinar qual <a %(blog)s>percento de ISBNs es preservate pro semper</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Finalmente, nos renovava nostre systema de donation. Tu pote ora usar un carta de credito pro depositar moneta directemente in nostre portafolios crypto, sin realmente necessitar saper nihil super cryptocurrencies. Nos continuara monitorar quanto ben isto functiona in practica, ma isto es un grande passo."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Commutar a ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Un de nostre <a %(annas_archive)s>tickets</a> esseva un sacco de problemas con nostre systema de cerca. Nos usava le cerca de texto complete de MySQL, pois que nos habeva tote nostre datos in MySQL de omne modo. Ma illo habeva su limites:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Alcun consultas prendeva multissimo tempore, al puncto que illos monopolizava tote le connexiones aperte."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Per defecto MySQL ha un longitud de parola minim, o tu indice pote devenir vermente grande. Personas reportava non poter cercar \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Le cerca esseva solmente un poco rapide quando totalmente cargate in memoria, lo que requireva nos obtener un machina plus costose pro executar isto, plus alcun commandos pro pre-cargar le indice al initio."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Nos non haberea potite extender lo facilemente pro construir nove functionalitates, como melior <a %(wikipedia_cjk_characters)s>tokenisation pro linguas sin spatio blanc</a>, filtrage/faceting, ordinamento, suggestiones de \"voleva dicer\", autocompletion, etc."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Post parlar con un gruppo de expertos, nos optava pro ElasticSearch. Illo non ha essite perfecte (lor suggestiones de \"voleva dicer\" e functionalitates de autocompletion predefinite es mal), ma in general illo ha essite multo melior que MySQL pro le cerca. Nos ancora non es <a %(youtube)s>troppo entusiasmate</a> de usar lo pro datos critic (ben que illes ha facite multe <a %(elastic_co)s>progressos</a>), ma in general nos es bastante felice con le commutation."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Pro ora, nos ha implementate un cerca multo plus rapide, melior supporto de linguas, melior ordinamento de relevancia, differentes optiones de ordinamento, e filtrage per lingua/typo de libro/typo de file. Si tu es curiose de como illo functiona, <a %(annas_archive_l140)s>da</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>reguardo</a>. Illo es bastante accessibile, ben que illo poterea usar un poco plus de commentos…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ de coperturas de libros publicate"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Finalmente, nos es felice de annunciar un parve publication. In collaboration con le personas qui opera le forca Libgen.rs, nos es partagiante tote lor coperturas de libros via torrents e IPFS. Isto distribuera le carga de vider le coperturas inter plus de machinas, e los preservara melior. In multe (ma non tote) casos, le coperturas de libros es includite in le files mesme, assi isto es un sorta de \"datos derivate\". Ma haber lo in IPFS es ancora multo utile pro le operation quotidian de ambe le Archivo de Anna e le varie forcas de Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Como de costume, tu pote trovar iste publication al Speculo de Bibliotheca Pirata (EDIT: transferite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Nos non ligara a illo hic, ma tu pote facilemente trovar lo."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Sperabilemente nos pote relaxar nostre ritmo un poco, ora que nos ha un alternative decente a Z-Library. Iste carga de travalio non es particularmente sustainable. Si tu es interessate in adjutar con programmation, operationes de servitor, o travalio de preservation, contacta nos. Il ha ancora multo de <a %(annas_archive)s>travalio a facer</a>. Gratias pro tu interesse e supporto."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Le Archivo de Anna ha salvate le plus grande bibliotheca umbra de comics del mundo (95TB) — tu pote adjutar a semenar lo"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Le plus grande bibliotheca umbra de libros de comics del mundo habeva un puncto unic de defecte... usque hodie."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Discuter sur Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Le plus grande bibliotheca umbra de libros de comics es probabilemente celle de un forca particular de Library Genesis: Libgen.li. Le un sol administrator qui gestiva ille sito succedeva a colliger un collection de comics insani de plus de 2 milliones de files, totalisante plus de 95TB. Tamen, a differente de altere collectiones de Library Genesis, iste non esseva disponibile in massa via torrents. Tu poteva solmente acceder a iste comics individualmente via su servitor personal lente — un puncto unic de defecte. Usque hodie!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In iste posto nos va contar plus sur iste collection, e sur nostre campania de fund-raising pro supportar plus de iste labor."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon tenta perder se in le mundo mundan del bibliotheca…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Bifurcationes de Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Prime, un poco de contexto. Forsan vos cognosce Library Genesis pro lor collection epic de libros. Minus personas sape que le voluntarios de Library Genesis ha create altere projectos, como un collection considerabile de revistas e documentos standard, un copia de securitate complete de Sci-Hub (in collaboration con le fundator de Sci-Hub, Alexandra Elbakyan), e vermente, un collection massive de comics."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "A un certe puncto, differente operadores de speculos de Library Genesis se separava, lo que resultava in le situation actual de haber un numero de differente “bifurcationes”, tote ancora portante le nomine Library Genesis. Le bifurcation Libgen.li unicmente ha iste collection de comics, assi como un collection considerabile de revistas (que nos es etiam laborante super)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Collaboration"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Date su magnitude, iste collection ha essite longemente in nostre lista de desiros, assi post nostre successo con facer un copia de securitate de Z-Library, nos fixava nostre objectivos super iste collection. Al initio nos lo rascava directemente, lo que esseva un ver desafio, pois que lor servitor non esseva in le melior condition. Nos obteneva circa 15TB in iste maniera, ma illo esseva lente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Felicemente, nos succedeva a entrar in contacto con le operador del bibliotheca, qui consentiva a inviar nos tote le datos directemente, lo que esseva multo plus rapide. Il ancora prendeva plus de medietate de un anno pro transferer e processar tote le datos, e nos quasi perdeva tote illo a causa de corruption de disco, lo que haberea significate recommenciar ab initio."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Iste experientia nos ha facite creder que es importante render iste datos disponibile tan rapide como possibile, assi illo pote esser mirroreate largemente. Nos es solmente un o duo incidentes mal temporisate distante de perder iste collection pro semper!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Le collection"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Mover se rapide significa que le collection es un poco disorganisate… Vamos a dar un oculo. Imagina que nos ha un systema de files (que in realitate nos es dividente trans torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Le prime directorio, <code>/repository</code>, es le parte plus structurate de isto. Iste directorio contine le assi nominate “mille dirs”: directorios cata uno con mille files, que es numerate incrementalmente in le base de datos. Directorio <code>0</code> contine files con comic_id 0–999, e assi de sequente."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Isto es le mesme schema que Library Genesis ha usate pro su collections de fiction e non-fiction. Le idea es que cata “mille dir” es automaticamente convertite in un torrent tan tosto como illo es plenate."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tamen, le operador de Libgen.li nunquam faceva torrents pro iste collection, e assi le mille dirs probabilemente deveniva inconveniente, e cedeva a “dirs non ordinate”. Iste es <code>/comics0</code> a <code>/comics4</code>. Illos tote contine structuras de directorios unic, que probabilemente faceva senso pro colliger le files, ma non face multo senso pro nos ora. Felicemente, le metadata ancora refere directemente a tote iste files, assi lor organisation de immagazinamento sur disco non importa vermente!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Le metadata es disponibile in le forma de un base de datos MySQL. Isto pote esser discargate directemente del sito web de Libgen.li, ma nos lo rendera etiam disponibile in un torrent, insimul a nostre proprie tabula con tote le hashes MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyses"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Quando vos recipe 95TB versate in vostre cluster de immagazinamento, vos tenta comprender lo que es mesmo ibi… Nos faceva alcun analyses pro vider si nos poterea reducer un poco le dimension, como per remover duplicatos. Ecce alcun de nostre constatationes:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Duplicatos semantic (differente scannos del mesme libro) pote theoreticamente esser filtrate, ma es complicate. Quando nos examinava manualmente le comics, nos trovava tropo multe false positivos."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Il ha alcun duplicatos purmente per MD5, lo que es relativemente dispendiose, ma filtrar los solmente nos haberea date circa 1% in de economias. A iste scala, illo es ancora circa 1TB, ma etiam, a iste scala 1TB non importa vermente. Nos prefererea non riscar accidentalmente destruer datos in iste processo."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Nos trovava un gruppo de datos non-librari, como filmes basate super libros de comics. Isto etiam pare dispendiose, pois que illos es ja largemente disponibile per altere medios. Tamen, nos realisava que nos non poterea simplemente filtrar files de filmes, pois que il ha etiam <em>libros de comics interactive</em> que esseva publicate sur le computator, que alicuno registrava e salvava como filmes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "In ultime, omne cosa que nos poterea deler del collection solmente salvarea un pauc percento. Alora nos rememorava que nos es accumulatores de datos, e le personas qui va specular isto es etiam accumulatores de datos, e assi, “QUE VOS VUOLE DICER, DELER?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Nos presenta dunque a vos, le collection integre e non modificate. Es multo datos, ma nos spera que abbastanza personas se interessara a propagar lo de omne maniera."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Raccolta de fundos"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Nos libera iste datos in alcun grande pecias. Le prime torrent es de <code>/comics0</code>, que nos poneva in un enorme file .tar de 12TB. Isto es melior pro vostre disco dur e software de torrent que un multitude de files plus parve."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Como parte de iste liberation, nos face un raccolta de fundos. Nos cerca colliger $20,000 pro coperir le costos operational e de contracto pro iste collection, e etiam pro permitter projectos continuante e futur. Nos ha alcun <em>massive</em> in le labor."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Qui io supporta con mi donation?</em> In breve: nos sustene tote le cognoscentia e cultura del humanitate, e lo rende facilemente accessibile. Tote nostre codice e datos es open source, nos es un projecto completemente gerite per voluntarios, e nos ha salvate 125TB de libros usque nunc (in addition a le torrents existente de Libgen e Scihub). In ultime, nos construe un volante que permitte e incita personas a trovar, escannar, e salvar tote le libros del mundo. Nos scribera super nostre plano magistral in un post futur. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Si vos dona pro un adhesion de 12 menses “Amazing Archivist” ($780), vos pote <strong>“adopter un torrent”</strong>, lo que significa que nos ponera vostre nomine de usator o message in le nomine de un del torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Vos pote donar visitando <a %(wikipedia_annas_archive)s>le Archivo de Anna</a> e cliccando le button “Donar”. Nos cerca etiam plus voluntarios: ingenieros de software, investigadores de securitate, expertos mercantiles anonyme, e traductores. Vos pote etiam supportar nos forniendo servicios de hospitio. E naturalmente, per favor propaga nostre torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Gratias a omnes qui nos ha supportate tanto generosemente! Vos vermente face un differentia."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Ecce le torrents liberate usque nunc (nos ancora processa le resto):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tote le torrents pote esser trovate in <a %(wikipedia_annas_archive)s>le Archivo de Anna</a> sub “Datasets” (nos non liga directemente ibi, assi ligamines a iste blog non es removite de Reddit, Twitter, etc.). De ibi, seque le ligamine al sito web de Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Que es le sequente?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Un gruppo de torrents es excellente pro preservation a longe termino, ma non tanto pro accesso quotidian. Nos laborara con partnarios de hospitio pro poner tote iste datos in le web (como le Archivo de Anna non hospita nihil directemente). Naturalmente vos potera trovar iste ligamines de download in le Archivo de Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Nos invita etiam omnes a facer cosas con iste datos! Adjuta nos a melior analizar lo, deduplicar lo, poner lo in IPFS, remixar lo, trainar vostre modelos de IA con illo, e assi via. Es tote vostre, e nos non pote attender vider lo que vos face con illo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Finalmente, como dicite antea, nos ancora ha alcun massive liberationes imminente (si <em>alguem</em> poterea <em>accidentalmente</em> inviar nos un dump de un <em>certe</em> base de datos ACS4, vos sape ubi trovar nos…), e etiam construer le volante pro salvar tote le libros del mundo."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Assi remane in contacto, nos solmente comencia."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nove libros addite al Speculo del Bibliotheca Pirata (+24TB, 3.8 milliones de libros)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In le liberation original del Speculo del Bibliotheca Pirata (EDIT: movite a <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>), nos faceva un speculo de Z-Library, un grande collection illegal de libros. Como un rememoration, isto es lo que nos scribeva in ille post original de blog:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library es un bibliotheca popular (e illegal). Illes ha prendite le collection de Library Genesis e lo ha rendite facilemente cercabile. In ultra, illes ha devenite multo effective a sollicitar nove contributiones de libros, incitante usatores contributores con varie privilegios. Illes actualmente non contribue iste nove libros retro a Library Genesis. E a differente de Library Genesis, illes non rende lor collection facilemente speculabile, lo que impedi un large preservation. Isto es importante pro lor modello de negocio, pois que illes demanda moneta pro acceder a lor collection in massa (plus que 10 libros per die)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Nos non face judicios moral super demandar moneta pro accesso in massa a un collection illegal de libros. Es sin dubita que le Z-Library ha habite successo in expander accesso al cognoscentia, e in obtener plus libros. Nos es simplemente hic pro facer nostre parte: garantir le preservation a longe termino de iste collection private."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Iste collection data de medio-2021. Intertanto, le Z-Library ha crescite a un ritmo stupefaciente: illes ha addite circa 3.8 milliones de nove libros. Il ha alcun duplicatos ibi, certe, ma le majoritate de illo pare esser libros vermente nove, o scansiones de melior qualitate de libros submitite anteriormente. Isto es in grande parte debite al numero augmentate de moderatores voluntari al Z-Library, e lor systema de carga in massa con deduplication. Nos vole felicitar les pro iste realisationes."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Nos es felice de annunciar que nos ha obtenite tote le libros que esseva addite al Z-Library inter nostre ultime speculo e Augusto 2022. Nos ha etiam retrocedite e raspat alcun libros que nos mancava le prime vice. In total, iste nove collection es circa 24TB, que es multo plus grande que le ultime (7TB). Nostre speculo es ora 31TB in total. De novo, nos deduplicava contra Library Genesis, pois que il ha ja torrents disponibile pro iste collection."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Per favor vade al Speculo de Bibliotheca Pirata pro explorar le nove collection (EDIT: movite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Il ha plus information ibi super como le files es structurate, e lo que ha cambiate desde le ultime vice. Nos non ligara a illo ab hic, pois que isto es solmente un sito de blog que non hospita materiales illegal."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Naturalmente, semination es etiam un excellente maniera de adjutar nos. Gratias a omnes qui sta seminante nostre previe serie de torrents. Nos es gratemente pro le responsa positive, e felice que il ha tanto personas qui se importa del preservation del cognoscentia e cultura in iste maniera inusual."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Como devenir un archivista pirata"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Le prime desafio pote esser un surprendente. Il non es un problema technic, o un problema legal. Il es un problema psychologic."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Ante que nos immerge, duo actualisationes super le Speculo de Bibliotheca Pirata (EDIT: movite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Nos recipeva alcun donationes extrememente generose. Le prime esseva $10k de un individuo anonymo qui ha etiam supportate \"bookwarrior\", le fundator original de Library Genesis. Gratias special a bookwarrior pro facilitar iste donation. Le secunde esseva un altere $10k de un donator anonymo, qui entrava in contacto post nostre ultime emission, e esseva inspirate a adjutar. Nos etiam recipeva un numero de donationes minor. Gratias multissimo pro tote vostre supporto generose. Nos ha alcun excitante nove projectos in le pipeline que isto supportara, dunque remane alert."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Nos habeva alcun difficultates technic con le dimension de nostre secunde emission, ma nostre torrents es ora active e seminante. Nos etiam recipeva un offerta generose de un individuo anonymo pro seminar nostre collection in lor servitores de velocitate multo alte, dunque nos face un carga special a lor machinas, post le qual omnes altere qui sta discargante le collection deberea vider un grande melioration in velocitate."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Libros integre pote esser scribite super le <em>proque</em> del preservation digital in general, e del archivismo pirata in particular, ma permitte nos dar un breve introduction pro illes qui non es multo familiar. Le mundo produce plus cognoscentia e cultura que jammais antea, ma etiam plus de illo es perdite que jammais antea. Le humanitate confide in grande parte a corporationes como editoras academic, servitores de diffusion, e companias de medios social con iste hereditage, e illes sovente non ha probite esser grande custodes. Verifica le documental Amnesia Digital, o vermente qualcunque presentation per Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Il ha alcun institutiones que face un bon labor archivar tanto como illes pote, ma illes es ligate per le lege. Como piratas, nos es in un position unic pro archivar collectiones que illes non pote tanger, a causa del application del derectos de autor o altere restrictiones. Nos pote etiam specular collectiones multe vices, trans le mundo, augmentante assi le chances de un preservation appropriate."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Pro ora, nos non entrara in discussiones super le pros e contras del proprietate intellectual, le moralitate de infringer le lege, reflexiones super le censura, o le questione de accesso al cognoscentia e cultura. Con toto isto foras del cammino, vamos immerger in le <em>como</em>. Nos compartira como nostre equipa deveniva archivistas pirata, e le lectiones que nos apprendeva al longo del cammino. Il ha multe desafios quando on se embarca in iste viage, e sperabilemente nos pote adjutar vos a transversar alcunos de illos."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Communitate"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Le prime desafio pote esser un surprendente. Il non es un problema technic, o un problema legal. Il es un problema psychologic: facer iste labor in le umbras pote esser incredibilemente solitari. Dependente de lo que vos plana facer, e vostre modello de menacia, vos poterea deber esser multo cautelose. A un extremitate del spectro nos ha personas como Alexandra Elbakyan*, le fundator de Sci-Hub, qui es multo aperte super su activitates. Ma illa es a alte risco de esser arrestate si illa visitarea un pais occidental a iste puncto, e poterea affrontar decennios de tempore in prision. Es isto un risco que vos volerea prender? Nos es al altere extremitate del spectro; essente multo cautelose de non lassar ulle tracia, e habente forte securitate operational."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Como mentionate in HN per \"ynno\", Alexandra initialmente non voleva esser cognoscite: \"Su servitores esseva configurate pro emitter messages de error detalite de PHP, includente le cammino complete del file de origine fallente, que esseva sub le directorio /home/<USER>" Assi, usa nomines de usator aleatori in le computatores que vos usa pro iste cosas, in caso que vos configura mal alcun cosa."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Tamen, iste secretessa veni con un costo psychologic. Le majoritate del personas ama esser recognoscite pro le labor que illes face, e tamen vos non pote prender ulle credito pro isto in vita real. Mesmo cosas simple pote esser un desafio, como amicos demandante vos que vos ha facite recentemente (a un certe puncto \"jocar con mi NAS / homelab\" deveni vetule)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Es pro isto que es tanto importante trovar un communitate. Vos pote renunciar a un poco de securitate operational confidante in alcun amicos multo proxime, qui vos sape que vos pote confider profundemente. Mesmo alora sia cautelose de non poner alique in scriptura, in caso que illes debe ceder lor emails al autoritates, o si lor dispositivos es compromise in alicun altere maniera."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Melior ancora es trovar alcun altere piratas. Si vostre amicos proxime es interessate in junger se a vos, excellente! Altrimenti, vos poterea trovar alteres online. Tristemente isto es ancora un communitate niche. Usque ora nos ha trovate solmente un pauc de alteres qui es active in iste spatio. Bon locos de initio pare esser le foros de Library Genesis, e r/DataHoarder. Le Equipa de Archivo ha etiam individuos con mentalitate simile, ben que illes opera intra le lege (mesmo si in alcun areas gris del lege). Le scenas traditional de \"warez\" e piratage ha etiam personas qui pensa in manieras simile."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Nos es aperte a ideas de como fomentar communitate e explorar ideas. Senti te libere de inviar nos un message in Twitter o Reddit. Forsan nos poterea hospitar un sorta de foro o gruppo de chat. Un desafio es que isto pote facilemente esser censurate quando usante plataformas commun, assi nos deberea hospitar lo nos mesme. Il ha etiam un compromisso inter haber iste discussion totalmente public (plus potential de engagement) versus facer lo private (non lassar potential \"objectivos\" saper que nos es sur le puncto de raspar los). Nos deberea pensar super isto. Lassa nos saper si tu es interessate in isto!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projectos"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Quando nos face un projecto, illo ha un par de phases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Selection de dominio / philosophia: Ubi tu approximativemente vole focalisar, e proque? Qual es tu passiones, habilitates, e circumstantias unic que tu pote usar a tu beneficio?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Selection de objectivo: Qual collection specific tu mirara?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Raspar metadata: Catalogar information super le files, sin vermente discargar le files (spesso multo plus grande) mesme."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Selection de datos: Basate super le metadata, restringer qual datos es le plus relevante a archivar ora. Poterea esser toto, ma spesso il ha un maniera rationabile de salvar spatio e banda."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Raspar datos: Vermente obtener le datos."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution: Emballar lo in torrents, annunciar lo alicubi, facer personas diffunder lo."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Iste non es phases completemente independente, e spesso perceptiones de un phase ulterior te retorna a un phase anterior. Per exemplo, durante le raspar de metadata tu poterea realisar que le objectivo que tu ha seligite ha mechanismos de defensa ultra tu nivello de habilitate (como blocos de IP), assi tu retorna e trova un objectivo differente."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Selection de dominio / philosophia"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Il non ha penuria de cognoscentia e hereditage cultural a salvar, lo que pote esser opprimente. Es pro isto que il es spesso utile prender un momento e pensar super qual pote esser tu contribution."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Cata persona ha un maniera differente de pensar super isto, ma hic es alcun questiones que tu poterea demandar a te mesme:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Proque es tu interessate in isto? De que es tu appassionate? Si nos pote haber un gruppo de personas que omnes archiva le sorta de cosas que illes specificamente se importa, illo coperirea multo! Tu sapera multo plus que le persona medie super tu passion, como qual es le datos importante a salvar, qual es le melior collectiones e communitates online, e assi via."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Qual habilitates tu ha que tu pote usar a tu beneficio? Per exemplo, si tu es un experto in securitate online, tu pote trovar manieras de superar blocos de IP pro objectivos secur. Si tu es excellente a organisar communitates, alora forsitan tu pote rallyar alcun personas insimul circa un objectivo. Es utile saper un poco de programmation tamen, mesmo si solmente pro mantener bon securitate operational durante iste processo."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Quanto tempore tu ha pro isto? Nostre consilio esserea de comenciar parve e facer projectos plus grande como tu prende le habito, ma illo pote devenir toto-consumante."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Qual esserea un area de alte-leva a focalisar? Si tu va passar X horas in archivar pirata, alora como tu pote obtener le plus grande \"bang pro tu buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Qual es manieras unic que tu pensa super isto? Tu poterea haber alcun ideas o approches interessante que alteres poterea haber mancate."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In nostre caso, nos se importava in particular super le preservation a longe termino del scientia. Nos sapeva super Library Genesis, e como illo esseva plenmente mirate multe vices usante torrents. Nos amava iste idea. Alora un die, un de nos essayava trovar alcun libros scientific in Library Genesis, ma non poteva trovar los, ponente in dubita quanto complete illo vermente esseva. Nos alora cercava iste libros online, e trovava los in altere locos, lo que plantava le semine pro nostre projecto. Mesmo ante que nos sapeva super le Z-Library, nos habeva le idea de non tentar colliger tote iste libros manualmente, ma de focalisar super mirroring de collectiones existente, e contributor los retro a Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Selection de objectivo"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Allora, nos ha nostre area que nos es examinante, ora qual collection specific nos debe reflecter? Il ha alcun cosas que face un bon objectivo:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grande"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unique: non ja ben coperite per altere projectos."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Accessibile: non usa multe stratos de protection pro impedir te de raspar lor metadata e datos."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Insight special: tu ha alcun information special super iste objectivo, como si tu ha accesso special a iste collection, o tu ha discoperite como superar lor defension. Isto non es requirite (nostre projecto imminente non face alique special), ma illo certemente adjuta!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Quando nos trovava nostre libros de texto scientific in sitos web altere que Library Genesis, nos essayava de comprender como illos faceva lor via al internet. Nos trovava alora le Z-Library, e realisava que durante que le major parte del libros non appare primo ibi, illos finalmente fini ibi. Nos apprendeva super su relation a Library Genesis, e le structura de incentivo (financial) e le interfacie de usator superior, ambes faceva illo un collection multo plus complete. Nos faceva alora alcun raspamento preliminar de metadata e datos, e realisava que nos poteva circumvenir lor limites de download IP, utilisante le accesso special de un de nostre membros a multe servitores proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Durante que tu explora differentes objectivos, es ja importante de celar tu tracias per usar VPNs e adressas de email jettable, del qual nos parlara plus tarde."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Raspamento de metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Vamos devenir un poco plus technic ci. Pro vermente raspar le metadata de sitos web, nos ha mantenite cosas bastante simple. Nos usa scriptos Python, a vices curl, e un base de datos MySQL pro immagazinar le resultatos. Nos non ha usate alicun software de raspamento sofisticate que pote cartographiar sitos web complexe, pois que usque ora nos solmente necessitava raspar un o duo typos de paginas per simplemente enumerar per id e analysar le HTML. Si il non ha paginas facilemente enumerabile, alora tu poterea necessitar un ver rastrellator que tenta trovar tote le paginas."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Ante que tu comencia a raspar un integre sito web, essaya de facer lo manualmente per un poco. Percurre un pauc de paginas tu mesme, pro obtener un senso de como illo functiona. A vices tu ja incontrara blocos IP o altere comportamentos interessante in iste maniera. Le mesme vale pro raspamento de datos: ante de plonger troppo profunde in iste objectivo, assecurate que tu pote vermente discargar su datos efficacemente."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Pro circumvenir restrictiones, il ha alcun cosas que tu pote essayar. Esque il ha altere adressas IP o servitores que hospita le mesme datos ma non ha le mesme restrictiones? Esque il ha punctos de accesso API que non ha restrictiones, durante que alteres ha? A qual velocitate de discargamento tu IP es blocate, e pro quanto tempore? O esque tu non es blocate ma retardate? Que si tu crea un conto de usator, como cambia le cosas alora? Pote tu usar HTTP/2 pro mantener le connexiones aperte, e isto augmenta le velocitate al qual tu pote requestar paginas? Esque il ha paginas que lista multiple files a un vice, e le information listate ibi es sufficiente?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Cosas que tu probabilemente vole immagazinar include:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titulo"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nomine de file / location"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: pote esser un ID interne, ma IDs como ISBN o DOI es utile tamben."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Dimension: pro calcular quanto spatio de disco tu necessita."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): pro confirmar que tu ha discargate le file correctemente."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Data addite/modificate: assi tu pote retornar plus tarde e discargar files que tu non discargava antea (ben que tu pote sovente tamben usar le ID o hash pro isto)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Description, categoria, etiquettas, autores, lingua, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Nos typicamente face isto in duo stages. Primo nos discarga le files HTML crude, usualmente directemente in MySQL (pro evitar multe files parve, del qual nos parla plus infra). Alora, in un passo separate, nos percurre ille files HTML e los analysa in tabulas MySQL real. In iste maniera tu non debe re-discargar toto ab initio si tu discoperi un error in tu codice de analysa, pois que tu pote simplemente reprocessar le files HTML con le nove codice. Es tamben sovente plus facile de parallelisar le passo de processar, assi salvante un poco de tempore (e tu pote scriber le codice de processar durante que le raspamento es in curso, in vice de deber scriber ambe passos a un vice)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Finalmente, nota que pro alcun objectivos, le extraction de metadata es toto lo que il ha. Il ha alcun collectiones massive de metadata que non es preservate adequatemente."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Selection de datos"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Sovente tu pote usar le metadata pro determinar un subgrupo rationabile de datos a discargar. Mesmo si tu eventualmente vole discargar tote le datos, il pote esser utile prioritizar le elementos le plus importante primo, in caso que tu es detectate e le defension es meliorate, o proque tu haberea besonio de comprar plus discos, o simplemente proque alicun altere cosa occurre in tu vita ante que tu pote discargar toto."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Per exemplo, un collection pote haber multiple editiones del mesme recurso de base (como un libro o un film), ubi un es marcate como essente de melior qualitate. Salvar iste editiones primo haberea multo senso. Tu eventualmente poterea voler salvar tote le editiones, pois que in alcun casos le metadata poterea esser incorrectemente etiquettate, o poterea haber compromissos incognite inter editiones (per exemplo, le \"melior edition\" poterea esser le melior in multe manieras ma pejor in altere, como un film habente un resolution plus alte ma sin subtitulos)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Tu pote etiam cercar in tu base de datos de metadata pro trovar cosas interessante. Qual es le file le plus grande que es hospitate, e proque es illo tanto grande? Qual es le file le plus parve? Esque il ha patronos interessante o inexpectate quando il se tracta de certe categorias, linguas, etc.? Esque il ha titulos duplicate o multo simile? Esque il ha patronos de quando le datos esseva addite, como un die in le qual multe files esseva addite a un vice? Tu sovente pote apprender multo per regardar le dataset in differentes manieras."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In nostre caso, nos deduplicava libros de Z-Library contra le hashes md5 in Library Genesis, salvante assi multo tempore de discargamento e spatio de disco. Isto es un situation bastante unic tamen. In le major parte del casos, il non ha bases de datos comprensive de qual files es ja preservate adequatemente per altere piratas. Isto in se mesme es un grande opportunitate pro alicuno ibi. Seria fantastic haber un vista general regularmente actualisate de cosas como musica e filmes que es ja largemente seminate in sitos de torrent, e es ergo de minor prioritate a includer in speculos pirate."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Extraction de datos"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Ora tu es preste a discargar realmente le datos in massa. Como mentionate antea, a iste puncto tu deberea ja haber discargate manualmente un gruppo de files, pro melior comprender le comportamento e restrictiones del objectivo. Tamen, il habera ancora surprisas in reserva pro te quando tu realmente comencia a discargar multe files a un vice."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nostre consilio hic es principalmente mantener lo simple. Comencia per simplemente discargar un gruppo de files. Tu pote usar Python, e postea extender a multiple filamentos. Ma a vices mesmo plus simple es generar files Bash directemente del base de datos, e postea executar multiple de illos in multiple fenestras de terminal pro escalar. Un rapide astutia technic que vale mentionar hic es usar OUTFILE in MySQL, que tu pote scriber ubique si tu disactiva \"secure_file_priv\" in mysqld.cnf (e assecurar te de etiam disactivar/superar AppArmor si tu es in Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Nos immagazina le datos in discos dur simple. Comencia con lo que tu ha, e expande lentemente. Il pote esser opprimente pensar a immagazinar centos de TBs de datos. Si isto es le situation que tu affronta, simplemente publica un bon subgrupo primo, e in tu annuncio pete adjuta pro immagazinar le resto. Si tu vole obtener plus discos dur te mesme, alora r/DataHoarder ha alcun bon recursos pro obtener bon ofertas."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Prova non preoccupante te troppo super systemas de files sofisticate. Il es facile cader in le cuniculo de configurar cosas como ZFS. Un detalio technic a esser conscie tamen, es que multe systemas de files non gestiona ben multe files. Nos ha trovate que un simple solution es crear multiple directorios, p.ex. pro differente intervallos de ID o prefixos de hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Post discargar le datos, assecurar te de verificar le integritate del files usante hashes in le metadata, si disponibile."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Tu ha le datos, assi te dando possession del prime speculo pirate del mundo de tu objectivo (probabilemente). In multe manieras le parte le plus difficile es finite, ma le parte le plus riscante es ancora ante te. Post toto, usque ora tu ha essite furtive; volante sub le radar. Tote lo que tu debeva facer esseva usar un bon VPN durante, non completar tu detalios personal in alcun formularios (duh), e forsan usar un session de navigator special (o mesmo un computator differente)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Ora tu debe distribuir le datos. In nostre caso nos primo voleva contribuir le libros retro a Library Genesis, ma postea discoperiva rapidemente le difficultates in illo (fiction versus non-fiction classification). Assi nos decideva super distribution usante torrents in le stilo de Library Genesis. Si tu ha le opportunitate de contribuir a un projecto existente, alora illo poterea salvar te multo tempore. Tamen, il non ha multe speculos pirate ben organisate ibi actualmente."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Assi vamos dicer que tu decide super distribuir torrents te mesme. Prova mantener iste files parve, assi illos es facile a specular in altere sitos web. Tu deberea alora seminate le torrents te mesme, durante que ancora resta anonyme. Tu pote usar un VPN (con o sin port forwarding), o pagar con Bitcoins mesclate pro un Seedbox. Si tu non sape lo que alcun de iste terminos significa, tu habera un gruppo de lectura a facer, pois que es importante que tu comprende le compromissos de risco hic."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Tu pote hospitar le files de torrent mesme in sitos de torrent existente. In nostre caso, nos seligeva realmente hospitar un sito web, pois que nos voleva etiam diffunder nostre philosophia in un maniera clar. Tu pote facer isto te mesme in un maniera simile (nos usa Njalla pro nostre dominios e hospitation, pagate con Bitcoins mesclate), ma etiam senti te libere de contactar nos pro que nos hospita tu torrents. Nos es cercante a construir un indice comprensive de speculos pirate con le tempore, si iste idea prende."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Quanto al selection de VPN, multo ha essite scribite super isto ja, assi nos simplemente repetera le consilio general de seliger per reputation. Politicas de non-log testate in tribunal con longos historicos de proteger le privatia es le option de minor risco, in nostre opinion. Nota que mesmo quando tu face tote lo correcte, tu nunquam pote arrivar a zero risco. Per exemplo, quando tu semina tu torrents, un actor de stato-nation multo motivate poterea probabilemente regardar le fluxos de datos entrante e sortiente pro servitores VPN, e deducer qui tu es. O tu poterea simplemente facer un error in alicun maniera. Nos probabilemente ja ha facite, e lo facera de novo. Fortunatemente, le statos-nation non se importa <em>tanto</em> super pirateria."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Un decision a facer pro cata projecto, es si publicar lo usante le mesme identitate como antea, o non. Si tu continua usar le mesme nomine, alora errores in securitate operational de projectos anterior poterea retornar a morder te. Ma publicar sub nomines differente significa que tu non construe un reputation plus durabile. Nos seligeva haber forte securitate operational ab le initio assi nos pote continuar usar le mesme identitate, ma nos non hesitara a publicar sub un nomine differente si nos face un error o si le circumstantias lo require."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Diffunder le parola pote esser difficile. Como nos diceva, isto es ancora un communitate niche. Nos originalmente postava in Reddit, ma realmente obteneva traction in Hacker News. Pro ora nostre recommendation es postar lo in alcun locos e vider lo que occurre. E de novo, contacta nos. Nos amarea diffunder le parola de plus effortios de archivismo pirate."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Sperante que isto es utile pro archivistas pirata que comencia. Nos es excite de dar te le benvenita a iste mundo, assi non hesita a contactar nos. Vamos preservar tanto del cognoscentia e cultura del mundo como nos pote, e replicar lo largemente."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Presentante le Speculo del Bibliotheca Pirata: Preservante 7TB de libros (que non es in Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Iste projecto (EDIT: transferite a <a %(wikipedia_annas_archive)s>Le Archivo de Anna</a>) ha como scopo contribuir al preservation e liberation del cognoscentia human. Nos face nostre parve e humile contribution, sequente le passos del grandes ante nos."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Le foco de iste projecto es illustrate per su nomine:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirata</strong> - Nos delibera violar le lege de copyright in le major parte del paises. Isto nos permitte facer qualcosa que entitates legal non pote facer: assecurar que libros es replicate largemente."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotheca</strong> - Como le major parte del bibliothecas, nos nos concentra primarimente super materiales scribite como libros. Nos poterea expander in altere typos de media in le futuro."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Speculo</strong> - Nos es strictemente un speculo de bibliothecas existente. Nos nos concentra super preservation, non super facer libros facilemente cercabile e descargabile (accesso) o fomentar un grande communitate de personas que contribue nove libros (sourcing)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Le prime bibliotheca que nos ha speculate es Z-Library. Isto es un bibliotheca popular (e illegal). Illes ha prendite le collection de Library Genesis e facite lo facilemente cercabile. In ultra, illes ha devenite multo effective a sollicitar nove contributiones de libros, per incentivar usatores contributive con varie beneficios. Illes actualmente non contribue iste nove libros retro a Library Genesis. E a differente de Library Genesis, illes non face lor collection facilemente replicabile, lo que impedi un large preservation. Isto es importante pro lor modello de negocio, pois que illes demanda pecunia pro acceder a lor collection in massa (plus de 10 libros per die)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Nos non face judicios moral super demandar moneta pro accesso in massa a un collection illegal de libros. Es sin dubita que le Z-Library ha habite successo in expander accesso al cognoscentia, e in obtener plus libros. Nos es simplemente hic pro facer nostre parte: garantir le preservation a longe termino de iste collection private."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Nos volerea invitar te a adjutar a preservar e liberar le cognoscentia human per discargar e semenar nostre torrents. Vide le pagina del projecto pro plus information super como le datos es organisate."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Nos etiam multo te invita a contribuir tu ideas pro qual collectiones specular in le futuro, e como proceder. Inseme nos pote attinger multo. Isto es solmente un parve contribution inter inumerabile alteres. Gratias, pro toto lo que tu face."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Nos non liga al files ab iste blog. Per favor trova lo tu mesme.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dump de ISBNdb, o Quante Libros Es Preservate Pro Sempre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Si nos deberea deduplicar propriemente le files de bibliothecas umbra, qual percento de tote le libros in le mundo ha nos preservate?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Con le Speculo del Bibliotheca Pirata (EDIT: transferite a <a %(wikipedia_annas_archive)s>Le Archivo de Anna</a>), nostre scopo es prender tote le libros in le mundo, e preservar los pro semper.<sup>1</sup> Inter nostre torrents de Z-Library, e le torrents original de Library Genesis, nos ha 11,783,153 files. Ma quante es isto, vermente? Si nos deduplicava propriemente iste files, qual percento de tote le libros in le mundo ha nos preservate? Nos vermente volerea haber qualcosa como isto:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of le hereditage scribite del humanitate preservate pro semper"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Pro un percento, nos necessita un denominatore: le numero total de libros jammais publicate.<sup>2</sup> Ante le fin de Google Books, un ingeniero del projecto, Leonid Taycher, <a %(booksearch_blogspot)s>essayava estimar</a> iste numero. Ille arrivava — con humor — a 129,864,880 (“al minus usque a dominica”). Ille estimava iste numero per construir un base de datos unificate de tote le libros in le mundo. Pro isto, ille colligava differente datasets e postea los fusionava in varie manieras."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Como un breve aparte, hay otra persona que intentó catalogar todos los libros del mundo: Aaron Swartz, el fallecido activista digital y cofundador de Reddit.<sup>3</sup> Él <a %(youtube)s>inició Open Library</a> con el objetivo de “una página web para cada libro jamás publicado”, combinando datos de muchas fuentes diferentes. Terminó pagando el precio más alto por su trabajo de preservación digital cuando fue procesado por descargar masivamente artículos académicos, lo que llevó a su suicidio. No hace falta decir que esta es una de las razones por las que nuestro grupo es seudónimo y por qué estamos siendo muy cuidadosos. Open Library sigue siendo heroicamente gestionada por personas en el Internet Archive, continuando el legado de Aaron. Volveremos a esto más adelante en esta publicación."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "En la publicación del blog de Google, Taycher describe algunos de los desafíos al estimar este número. Primero, ¿qué constituye un libro? Hay algunas definiciones posibles:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Copias físicas.</strong> Obviamente esto no es muy útil, ya que son solo duplicados del mismo material. Sería genial si pudiéramos preservar todas las anotaciones que las personas hacen en los libros, como los famosos “garabatos en los márgenes” de Fermat. Pero, por desgracia, eso seguirá siendo un sueño de archivista."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Obras”.</strong> Por ejemplo, “Harry Potter y la Cámara Secreta” como un concepto lógico, abarcando todas sus versiones, como diferentes traducciones y reimpresiones. Esta es una definición algo útil, pero puede ser difícil trazar la línea de lo que cuenta. Por ejemplo, probablemente queramos preservar diferentes traducciones, aunque las reimpresiones con solo diferencias menores podrían no ser tan importantes."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Ediciones”.</strong> Aquí cuentas cada versión única de un libro. Si algo sobre él es diferente, como una portada diferente o un prólogo diferente, cuenta como una edición diferente."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Archivos.</strong> Al trabajar con bibliotecas en la sombra como Library Genesis, Sci-Hub o Z-Library, hay una consideración adicional. Puede haber múltiples escaneos de la misma edición. Y las personas pueden hacer mejores versiones de archivos existentes, escaneando el texto usando OCR o corrigiendo páginas que fueron escaneadas en ángulo. Queremos contar estos archivos solo como una edición, lo que requeriría un buen metadata o deduplicación usando medidas de similitud de documentos."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "Las “Ediciones” parecen la definición más práctica de lo que son los “libros”. Convenientemente, esta definición también se utiliza para asignar números ISBN únicos. Un ISBN, o Número Estándar Internacional de Libros, se utiliza comúnmente para el comercio internacional, ya que está integrado con el sistema internacional de códigos de barras (“Número Internacional de Artículo”). Si deseas vender un libro en tiendas, necesita un código de barras, por lo que obtienes un ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "La publicación del blog de Taycher menciona que, si bien los ISBN son útiles, no son universales, ya que solo se adoptaron realmente a mediados de los setenta, y no en todo el mundo. Aun así, el ISBN es probablemente el identificador más utilizado de ediciones de libros, por lo que es nuestro mejor punto de partida. Si podemos encontrar todos los ISBN del mundo, obtenemos una lista útil de qué libros aún necesitan ser preservados."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Entonces, ¿de dónde obtenemos los datos? Hay varios esfuerzos existentes que están tratando de compilar una lista de todos los libros del mundo:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Después de todo, hicieron esta investigación para Google Books. Sin embargo, su metadata no es accesible en masa y es bastante difícil de extraer."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Como se mencionó antes, esta es toda su misión. Han obtenido enormes cantidades de datos de bibliotecas de bibliotecas cooperantes y archivos nacionales, y continúan haciéndolo. También tienen bibliotecarios voluntarios y un equipo técnico que están tratando de deduplicar registros y etiquetarlos con todo tipo de metadata. Lo mejor de todo es que su conjunto de datos es completamente abierto. Puedes simplemente <a %(openlibrary)s>descargarlo</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Este es un sitio web administrado por la organización sin fines de lucro OCLC, que vende sistemas de gestión de bibliotecas. Agregan metadata de libros de muchas bibliotecas y la ponen a disposición a través del sitio web de WorldCat. Sin embargo, también ganan dinero vendiendo estos datos, por lo que no están disponibles para descarga masiva. Tienen algunos conjuntos de datos masivos más limitados disponibles para descargar, en cooperación con bibliotecas específicas."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Este es el tema de esta publicación de blog. ISBNdb extrae datos de varios sitios web para metadata de libros, en particular datos de precios, que luego venden a libreros, para que puedan fijar el precio de sus libros de acuerdo con el resto del mercado. Dado que los ISBN son bastante universales hoy en día, efectivamente construyeron una “página web para cada libro”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Varios sistemas de bibliotecas individuales y archivos.</strong> Hay bibliotecas y archivos que no han sido indexados y agregados por ninguno de los anteriores, a menudo porque están subfinanciados, o por otras razones no quieren compartir sus datos con Open Library, OCLC, Google, etc. Muchas de estas tienen registros digitales accesibles a través de internet, y a menudo no están muy bien protegidos, por lo que si deseas ayudar y divertirte aprendiendo sobre sistemas de bibliotecas extraños, estos son excelentes puntos de partida."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "En esta publicación, nos complace anunciar un pequeño lanzamiento (en comparación con nuestros lanzamientos anteriores de Z-Library). Extraímos la mayor parte de ISBNdb y pusimos los datos a disposición para torrenting en el sitio web de Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>; no lo enlazaremos aquí directamente, solo búscalo). Estos son alrededor de 30.9 millones de registros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4.4GB comprimidos). En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que podríamos haber perdido algunos de alguna manera, o <em>ellos</em> podrían estar haciendo algo mal. En cualquier caso, por ahora no compartiremos exactamente cómo lo hicimos — dejaremos eso como un ejercicio para el lector. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Lo que compartiremos es un análisis preliminar, para tratar de acercarnos a estimar el número de libros en el mundo. Observamos tres conjuntos de datos: este nuevo conjunto de datos de ISBNdb, nuestra publicación original de metadata que extraímos de la biblioteca en la sombra Z-Library (que incluye Library Genesis), y el volcado de datos de Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Comencemos con algunos números aproximados:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "En tanto Z-Library/Libgen como Open Library hay muchos más libros que ISBN únicos. ¿Significa eso que muchos de esos libros no tienen ISBN, o simplemente falta el metadata de ISBN? Probablemente podamos responder a esta pregunta con una combinación de coincidencia automatizada basada en otros atributos (título, autor, editor, etc.), incorporando más fuentes de datos y extrayendo ISBN de los propios escaneos de libros (en el caso de Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "¿Cuántos de esos ISBN son únicos? Esto se ilustra mejor con un diagrama de Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Para ser más precisos:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Nos surprendeva quanto pauc overlap existe! ISBNdb ha un grande numero de ISBNs que non appare ni in Z-Library ni in Open Library, e lo mesme es ver (a un grado minor ma ancora substantial) pro le altere duo. Isto leva a multe nove questiones. Quanto poterea le correspondentia automatizate adjutar in etiquettar le libros que non esseva etiquettate con ISBNs? Haberea multe correspondencias e dunque un augmento de overlap? Anque, que occurre si nos introduce un 4te o 5te dataset? Quanto overlap viderem nos alora?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Isto nos da un puncto de initio. Nos pote ora examinar tote le ISBNs que non esseva in le dataset de Z-Library, e que non corresponde al campos de titulo/autor. Isto pote dar nos un manico pro preservar tote le libros del mundo: primo per raspar le internet pro scans, alora per sortir in le vita real pro scannar libros. Le ultime poterea mesmo esser financiato per le multitudine, o guidate per \"recompensas\" de personas que volerea vider libros particular digitalisate. Tote isto es un historia pro un altere tempore."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Si vos vole adjutar con alicuno de isto — ulteriore analyse; raspar plus de metadata; trovar plus de libros; OCR de libros; facer isto pro altere dominios (p.ex. articulos, audiolibros, filmes, emissiones de television, revistas) o mesmo facer disponibile alicun de iste datos pro cosas como le entrainamento de modelos de linguage large / ML — per favor contacta me (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Si vos es specificamente interessate in le analyse de datos, nos labora pro facer nostre Datasets e scriptos disponibile in un formato plus facile a usar. Seria fantastic si vos poterea simplemente forcar un notebook e comenciar a luder con isto."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Finalmente, si vos vole supportar iste labor, per favor considera facer un donation. Isto es un operation completemente gestionate per voluntarios, e vostre contribution face un grande differentia. Cata poco adjuta. Pro ora nos accepta donationes in crypto; vide le pagina de Donationes in le Archivo de Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Pro alcun definition rationabile de \"sempre\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Naturalmente, le hereditage scribite del humanitate es multo plus que libros, specialmente hodie. Pro le scopo de iste articulo e nostre publicationes recente nos nos concentra super libros, ma nostre interesses se extende plus longe."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Il ha multo plus que pote esser dicite super Aaron Swartz, ma nos voleva solmente mentionar le brevemente, pois que ille joca un parte pivotal in iste historia. Como le tempore passa, plus de personas poterea incontrar su nomine pro le prime vice, e poterea subsequente plonjar in le cuniculo de conilio per se mesme."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Le fenestra critic de bibliothecas de umbra"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Como pote nos pretender preservar nostre collectiones in perpetuitate, quando illos ja se approximante a 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Version chinese 中文版</a>, discute in <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "In le Archivo de Anna, nos es sovente demandate como nos pote pretender preservar nostre collectiones in perpetuitate, quando le grandor total ja se approximante a 1 Petabyte (1000 TB), e ancora cresce. In iste articulo nos va examinar nostre philosophia, e vider proque le proxime decennio es critic pro nostre mission de preservar le cognoscentia e cultura del humanitate."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Le <a %(annas_archive_stats)s>grandor total</a> de nostre collectiones, super le ultime menses, disaggregate per numero de seminator de torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioritates"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Proque nos importa tanto super articulos e libros? Vamos poner de latere nostre credentia fundamental in preservation in general — nos poterea scriber un altere articulo super isto. Alora proque articulos e libros specificamente? Le responsa es simple: <strong>densitate de information</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte de immagazinamento, texto scribite immagazina le plus de information de tote le medios. Dum nos importa de ambe cognoscentia e cultura, nos importa plus del prime. In general, nos trova un hierarchia de densitate de information e importantia de preservation que appare approximativemente assi:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Articulos academic, jornales, reportos"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Datos organic como sequencias de DNA, semines de plantas, o campiones microbial"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Libros de non-fiction"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Codice de software de scientia e ingenieria"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Datos de mensuration como mensurationes scientific, datos economic, reportos corporative"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Sitos web de scientia e ingenieria, discussiones online"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Revistas non-fiction, jornales, manuales"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transcriptiones non-fiction de discursos, documentarios, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Datos interne de corporationes o governamentos (fugas)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Registros de metadata generalmente (de non-fiction e fiction; de altere media, arte, personas, etc.; includente recensiones)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Datos geographic (ex. mapas, investigationes geologic)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transcriptiones de proceduras legal o judicial"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versiones fictional o de divertimento de tote le supra"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Le classification in iste lista es un poco arbitrari — plure elementos es ligate o ha disaccordos intra nostre equipa — e probabilemente nos oblida alcun categorias importante. Ma isto es approximativemente como nos prioritiza."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Alcun de iste elementos es troppo differente del alteres pro nos preoccupar (o es ja prendite in cura per altere institutiones), como datos organic o datos geographic. Ma le majoritate del elementos in iste lista es vermente importante pro nos."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Un altere grande factor in nostre prioritisation es quanto a risco un certe obra es. Nos prefere concentrar nos super obras que es:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Rare"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unicamente subfocalisate"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unicamente a risco de destruction (ex. per guerra, reductiones de fundos, actiones legal, o persecution politic)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Finalmente, nos importa le scala. Nos ha tempore e moneta limitate, assi nos prefererea passar un mense salvante 10,000 libros que 1,000 libros — si illos es de valor e a risco equal."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Bibliothecas umbra"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Il ha multe organisationes que ha missiones simile, e prioritates simile. In effecto, il ha bibliothecas, archivos, laboratorios, museos, e altere institutiones incaricate del preservation de iste sorta. Multes de illos es ben-financiate, per governamentos, individuos, o corporationes. Ma illos ha un massive puncto cec: le systema legal."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hic reside le rolo unic de bibliothecas umbra, e le ration pro le existentia de le Archivo de Anna. Nos pote facer cosas que altere institutiones non es permittite facer. Ora, non es (frequentemente) que nos pote archivar materiales que es illegal preservar alibi. No, es legal in multe locos construir un archivo con omne libros, papiros, revistas, e assi via."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Ma lo que archivos legal sovente manca es <strong>redundantia e longevitá</strong>. Il ha libros del qual solmente un copia existe in alcun bibliotheca physic alicubi. Il ha registros de metadata custodite per un sol corporation. Il ha jornales solmente preservate in microfilmo in un sol archivo. Bibliothecas pote reciper reductiones de fundos, corporationes pote devenir bankrupta, archivos pote esser bombardate e comburite al sol. Isto non es hypothetic — isto eveni tote le tempore."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Le cosa que nos pote facer unicmente in le Archivo de Anna es immagazinar multe copias de obras, a scala. Nos pote colliger articulos, libros, revistas, e plus, e distribuer los in massa. Nos face isto actualmente via torrents, ma le technologias exacto non importa e cambiara con le tempore. Le parte importante es obtener multe copias distribuite trans le mundo. Isto citation de plus de 200 annos retro ancora sona ver:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Le perdite non pote esser recuperate; ma lassar nos salvar lo que remane: non per cryptas e serraturas que los defende del oculos public e uso, consignante los al dispendio del tempore, ma per tal un multiplication de copias, que los pon ultra le alcance de accidente.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Un nota rapide super le dominio public. Desde que le Archivo de Anna se concentra unicmente super activitates que es illegal in multe locos circum le mundo, nos non nos preoccupa con collectiones largemente disponibile, como libros de dominio public. Entitates legal sovente ja se occupa ben de illo. Tamen, il ha considerationes que nos face a vices laborar super collectiones publicamente disponibile:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Registros de metadata pote esser liberemente vidite in le sito web de Worldcat, ma non discargate in massa (usque nos <a %(worldcat_scrape)s>raspava</a> los)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Codice pote esser open source in Github, ma Github como un integro non pote esser facilemente mirate e assi preservate (ben que in iste caso particular il ha copias sufficientemente distribuite de le major parte del repositorios de codice)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit es libere a usar, ma recentemente ha instaurate medidas anti-raspamento stricte, in le sequela de LLM famelic de datos (plus super isto plus tarde)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Un multiplication de copias"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Retornante a nostre question original: como pote nos pretender preservar nostre collectiones in perpetuitate? Le problema principal hic es que nostre collection ha <a %(torrents_stats)s>crescite</a> a un passo rapide, per raspamento e open-sourcing de alcun collectiones massive (super le labor fantastic ja facite per altere bibliothecas de datos aperte como Sci-Hub e Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Iste crescentia in datos rende plus difficile que le collectiones sia mirate circum le mundo. Le immagazinamento de datos es costose! Ma nos es optimista, specialmente quando observante le sequente tres tendentias."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Nos ha colligite le fructos a basse altitude"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Isto seque directemente de nostre prioritates discutite supra. Nos prefere laborar a liberar grande collectiones primo. Ora que nos ha securate alcun del plus grande collectiones in le mundo, nos expecta que nostre crescentia sia multo plus lente."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Il ha ancora un cauda longe de collectiones plus parve, e nove libros es scannate o publicate cata die, ma le taxa essera probabilemente multo plus lente. Nos poterea ancora duplicar o mesmo triplicar in grandor, ma super un periodo de tempore plus longe."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Le costos de immagazinamento continua a decrescer exponentialmente"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Al tempore de scriber, <a %(diskprices)s>precios de discos</a> per TB es circa $12 pro discos nove, $8 pro discos usate, e $4 pro tape. Si nos es conservative e considera solmente discos nove, isto significa que immagazinar un petabyte costa circa $12,000. Si nos assume que nostre bibliotheca triplicara de 900TB a 2.7PB, isto significarea $32,400 pro mirrar nostre integre bibliotheca. Addente electricitate, costo de altere hardware, e assi via, lassar nos rotundar lo a $40,000. O con tape plus como $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "De un latere <strong>$15,000–$40,000 pro le summa de tote le cognoscentia human es un furto</strong>. De altere latere, es un poco abrupto expectar toneladas de copias complete, specialmente si nos etiam volerea que illes continua a semenar lor torrents pro le beneficio de alteres."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Isto es hodie. Ma le progresso marcha in avantia:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Le costos de discos dur per TB ha essite circa dividite in tertios super le ultime 10 annos, e probabilemente continuara a decrescer a un passo simile. Tape pare esser in un trajectoria simile. Le precios de SSD cade mesmo plus rapidemente, e poterea superar le precios de HDD al fin del decennio."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Tendentias de precios de HDD de differentes fontes (clicca pro vider le studio)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Si isto se mantene, alora in 10 annos nos poterea considerar solmente $5,000–$13,000 pro mirrar nostre integre collection (1/3), o mesmo minus si nos cresce minus in grandor. Durante que ancora multo de moneta, isto essera attingibile pro multe personas. E poterea esser mesmo melior a causa del puncto sequente…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Meliorationes in densitate informationis"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Nos actualmente immagazina libros in le formatos brut que nos es date. Certo, illos es compresse, ma sovente illos es ancora grande scansiones o photographias de paginas."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Usque nunc, le sol optiones pro reducer le grandor total de nostre collection ha essite per medio de un compression plus aggressive, o deduplication. Tamen, pro obtener economias significative, ambe es troppo lossy pro nostre gusto. Compression forte de photographias pote render le texto apenas legibile. E deduplication require alta confidentia que libros es exactemente le mesme, lo que sovente es troppo inaccurate, specialmente si le contento es le mesme ma le scansiones es facite in differentes occasion."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Il ha semper essite un tertie option, ma su qualitate ha essite tanto abysmal que nos nunquam lo considerava: <strong>OCR, o Recognition Optical de Characteres</strong>. Isto es le processo de converter photographias in texto simple, per medio de AI pro detectar le characteres in le photographias. Instrumentos pro isto ha existite desde longe, e ha essite bastante decente, ma “bastante decente” non es sufficiente pro fin de preservation."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tamen, recente modelos de apprendimento profunde multi-modal ha facite progressos extrememente rapide, ben que ancora a alte costos. Nos expecta que ambe le accuratezza e le costos meliorara dramaticamente in le annos veniente, al puncto que illo devenira realistic applicar a nostre integre bibliotheca."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Meliorationes de OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Quando isto occurre, nos probabilemente ancora preservara le files original, ma in addition nos poterea haber un version multo plus parve de nostre bibliotheca que le major parte del personas volera specular. Le puncto es que le texto brut ipse se comprime mesmo melior, e es multo plus facile deduplicar, nos dando mesmo plus de economias."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "In general, non es irrealistic expectar al minus un reduction de 5-10x in le grandor total del file, forsan mesmo plus. Mesmo con un reduction conservative de 5x, nos considerarea <strong>$1,000–$3,000 in 10 annos mesmo si nostre bibliotheca triplica in grandor</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Fenestra critic"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Si iste previsiones es accurate, nos <strong>solmente debe attender un par de annos</strong> ante que nostre integre collection essera largemente speculate. Assi, in le parolas de Thomas Jefferson, “ponite ultra le alcance de accidente.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Infelicemente, le advento de LLMs, e lor formation avide de datos, ha ponite multe detentores de copyright in le defensive. Mesmo plus que illes ja era. Multes sitos web rende plus difficile raspar e archivar, litigios vola circa, e durante tote isto bibliothecas e archivos physic continua esser neglectate."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Nos pote solmente expectar que iste tendencias continua a aggravar, e multe obras a esser perdite ben ante que illos entra in le dominio public."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Nos es al eve de un revolution in preservation, ma <q>le perdite non pote esser recuperate.</q></strong> Nos ha un fenestra critic de circa 5-10 annos durante le qual es ancora bastante costose operar un bibliotheca umbra e crear multe speculos circa le mundo, e durante le qual accesso non ha essite completemente clausurate ancora."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Si nos pote transversar iste fenestra, alora nos vermente habera preservate le cognoscentia e cultura del humanitate in perpetuitate. Nos non deberea lassar iste tempore esser vane. Nos non deberea lassar iste fenestra critic clauder super nos."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Vamos."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Accesso exclusive pro companias de LLM al plus grande collection de libros non-fictional chinese in le mundo"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Version chinese 中文版</a>, <a %(news_ycombinator)s>Discuter in Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Le Archivo de Anna ha acquirite un collection unic de 7.5 milliones / 350TB de libros non-fictional chinese — plus grande que Library Genesis. Nos es disposte a dar a un compania de LLM accesso exclusive, in cambio de OCR de alte qualitate e extraction de texto.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Isto es un breve articulo de blog. Nos cerca un compania o institution pro adjutar nos con OCR e extraction de texto pro un massive collection que nos ha acquirite, in cambio de accesso exclusive anticipate. Post le periodo de embargo, nos naturalmente publicara le integre collection."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Texto academic de alta qualitate es extrememente utile pro le entrainamento de LLMs. Mentre nostre collection es in chinese, isto debe esser mesmo utile pro le entrainamento de LLMs in anglese: le modelos pare encodar conceptos e cognoscentia sin regardo al lingua fonte."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Pro isto, le texto debe esser extrahite del scannos. Que obtene le Archivo de Anna de isto? Recerca de texto integre del libros pro su usatores."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Proque nostre objectivos se aligna con illos del disveloppatores de LLM, nos cerca un collaborator. Nos es disposte a dar te <strong>accesso exclusive anticipate a iste collection in massa pro 1 anno</strong>, si tu pote facer OCR e extraction de texto adequatemente. Si tu es disposte a compartir con nos le integre codice de tu pipeline, nos serea disposte a embargar le collection plus longe."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Paginas de exemplo"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Pro demonstrar a nos que tu ha un bon pipeline, hic es alcun paginas de exemplo pro comenciar, ex un libro super superconductores. Tu pipeline debe tractar adequatemente le mathematica, tabulas, graphicos, notas a pede, etc."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Invia tu paginas processate a nostre email. Si illos pare bon, nos te inviara plus in private, e nos expecta que tu sia capace de exequer tu pipeline rapidemente super illos tamben. Una vice que nos es satisfacite, nos pote facer un accordo."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Collection"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Alcun information additional super le collection. <a %(duxiu)s>Duxiu</a> es un massive base de datos de libros scannate, create per le <a %(chaoxing)s>SuperStar Digital Library Group</a>. Le majoritate es libros academic, scannate pro render los disponibile digitalmente a universitates e bibliothecas. Pro nostre publico anglo-parlante, <a %(library_princeton)s>Princeton</a> e le <a %(guides_lib_uw)s>Universitate de Washington</a> ha bon summarios. Il ha tamben un excellente articulo que da plus de contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercar lo in le Archivo de Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Le libros de Duxiu ha essite longemente pirateate in le internet chinese. Usualmente illos es vendite pro minus de un dollar per revendedores. Illos es typicamente distribuite usante le equivalent chinese de Google Drive, que ha essite sovente hackate pro permitter plus de spatio de immagazinamento. Alcun detalios technic pote esser trovate <a %(github_duty_machine)s>hic</a> e <a %(github_821_github_io)s>hic</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Ben que le libros ha essite distribuite semi-publicamente, es bastante difficile obtener los in massa. Nos habeva isto alte in nostre lista de cosas a facer, e allocava plure menses de labor a plen tempore pro isto. Tamen, recentemente un voluntario incredibile, stupefaciente, e talentose nos contactava, dicente que illes habeva facite tote iste labor ja — a grande costo. Illes partagiva le integre collection con nos, sin expectar nihil in retorno, excepte le garantia de preservation a longe termino. Vermente remarcabile. Illes consentiva a peter adjuta in iste maniera pro obtener le collection OCR'ate."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Le collection es 7,543,702 files. Isto es plus que le non-fiction de Library Genesis (circa 5.3 milliones). Le dimension total del files es circa 359TB (326TiB) in su forma actual."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Nos es aperte a altere propositiones e ideas. Simplemente contacta nos. Consulta le Archivo de Anna pro plus information super nostre collections, effortios de preservation, e como tu pote adjutar. Gratias!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Advertimento: iste articulo de blog ha essite deprecate. Nos ha decidite que IPFS non es ancora preste pro le prime tempore. Nos ancora ligara a files in IPFS ab le Archivo de Anna quando possibile, ma nos non lo hostara plus nos mesme, ni recommendara a alteres de specular usante IPFS. Per favor vide nostre pagina de Torrentes si tu vole adjutar a preservar nostre collection."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Adjuta a semenar Z-Library in IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Como operar un bibliotheca umbra: operationes al Archivo de Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Il non ha <q>AWS pro caritates umbra,</q> dunque como nos opera le Archivo de Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Io opera <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, le motor de recerca sin scopo lucrative open-source le plus grande del mundo pro <a %(wikipedia_shadow_library)s>bibliothecas umbra</a>, como Sci-Hub, Library Genesis, e Z-Library. Nostre objectivo es render le cognoscentia e cultura facilemente accessibile, e ultimamente a construir un communitate de personas qui insimul archiva e preserva <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tote le libros del mundo</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In iste articulo io monstrara como nos opera iste sito web, e le desafios unic que veni con operar un sito web con stato legal dubitabile, proque il non ha “AWS pro caritates umbra”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Consulta tamben le articulo soror <a %(blog_how_to_become_a_pirate_archivist)s>Como devenir un archivista pirata</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Tokens de innovation"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Comencemos con nostre pila technologic. Illo es deliberatemente tediose. Nos usa Flask, MariaDB, e ElasticSearch. Literalmente es toto. Le recerca es in grande parte un problema resolvite, e nos non intende reinventa lo. In ultra, nos debe expender nostre <a %(mcfunley)s>tokens de innovation</a> in altere cosas: non esser abattite per le autoritates."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Quanto legal o illegal es exactemente le Archivo de Anna? Isto depende principalmente del jurisdiction legal. Le major parte del paises crede in un forma de derecto de autor, lo que significa que personas o companias es assignate un monopolio exclusive super certe typos de obras pro un certe periodo de tempore. Como un parentehese, in le Archivo de Anna nos crede que, ben que il ha alcun beneficios, in general le derecto de autor es un negative net pro le societate — ma isto es un historia pro un altere occasion."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Iste monopolio exclusive super certe obras significa que es illegal pro qualcunque foras de iste monopolio distribuer directemente iste obras — includente nos. Ma le Archivo de Anna es un motor de recerca que non distribue directemente iste obras (al minus non in nostre sito web clearnet), dunque nos deberea esser okay, ver? Non exactemente. In multe jurisdictiones non es solmente illegal distribuer obras con derecto de autor, ma etiam ligar a locos que lo face. Un exemplo classic de isto es le lege DMCA del Statos Unite."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Isto es le extremitate plus stricta del spectro. Al altere extremitate del spectro poterea theoreticamente haber paises sin leges de derecto de autor, ma iste vermente non existe. Praticamente omne pais ha un forma de lege de derecto de autor in le libros. Le application es un altere historia. Il ha multe paises con governamentos que non se importa de applicar le lege de derecto de autor. Il ha etiam paises inter le duo extremos, que prohibe distribuer obras con derecto de autor, ma non prohibe ligar a tal obras."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Un altere consideration es al nivello de compania. Si un compania opera in un jurisdiction que non se importa de derecto de autor, ma le compania mesme non es disposta a prender alcun risco, alora illos poterea clauder vostre sito web tan tosto como qualcunque se plange de illo."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Finalmente, un grande consideration es le pagamentos. Pois que nos debe remaner anonyme, nos non pote usar methodos traditional de pagamento. Isto nos lassa con cryptocurrencies, e solmente un parve subset de companias supporta illos (il ha cartas de debito virtual pagate per crypto, ma illos es sovente non acceptate)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Architectura de systema"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Assi, supponamos que vos trovava alcun companias que es disposte a hospitar vostre sito web sin clauder lo — vamos a appellar los “fornitores amante de libertate” 😄. Vos rapidemente discoperira que hospitar toto con illos es bastante costose, assi vos poterea voler trovar alcun “fornitores bon mercato” e facer le hospitation real ibi, proxyante per le fornitores amante de libertate. Si vos lo face correctemente, le fornitores bon mercato nunquam sapera lo que vos hospita, e nunquam recipera alcun plangas."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Con tote iste fornitores il ha un risco que illos vos claude de omne modo, assi vos necessita etiam redundancia. Nos necessita isto a tote nivellos de nostre pila."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Un compania un poco amante de libertate que se ha ponite in un position interessante es Cloudflare. Illos ha <a %(blog_cloudflare)s>arguite</a> que illos non es un fornitor de hospitation, ma un utilitate, como un ISP. Illos non es dunque subjecte a DMCA o altere requestas de abattimento, e transmitta omne requestas a vostre ver fornitor de hospitation. Illos ha mesmo vadite al tribunal pro proteger iste structura. Nos pote dunque usar los como un altere strata de cache e protection."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare non accepta pagamentos anonyme, assi nos pote solmente usar lor plano gratuite. Isto significa que nos non pote usar lor functiones de balanceamento de carga o failover. Nos dunque <a %(annas_archive_l255)s>implementava isto nos mesme</a> al nivello de dominio. Al carga del pagina, le navigator verificara si le dominio currente es ancora disponibile, e si non, illo re-scribe tote le URLs a un altere dominio. Pois que Cloudflare cache multe paginas, isto significa que un usator pote atterrar in nostre dominio principal, mesmo si le servitor proxy es giu, e alora al proxime clic esser transferite a un altere dominio."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Nos ancora ha etiam concernas operational normal a tractar, como monitorar le sanitate del servitores, registrar errores de backend e frontend, e assi via. Nostre architectura de failover permitte plus de robustessa in iste fronto etiam, per exemplo per executar un setto completemente differente de servitores in un del dominios. Nos pote mesmo executar versiones plus vetule del codice e datasets in iste dominio separate, in caso que un bug critic in le version principal passa inobservate."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Nos pote etiam proteger nos contra Cloudflare se revoltar contra nos, per remover lo de un del dominios, como iste dominio separate. Differente permutationes de iste ideas es possibile."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Instrumentos"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Vamos vider qual instrumentos nos usa pro accomplir toto isto. Isto es multo in evolution como nos incontrara nove problemas e trova nove solutiones."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Servitor de application: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Servitor proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Gestion de servitores: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Developmento: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hospedage static de Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Il ha alcun decisiones que nos ha reconsiderate plure vices. Un de illos es le communication inter servitores: nos usava Wireguard pro isto, ma trovava que illo occasionalmente cessa de transmitter alcun datos, o solmente transmitti datos in un direction. Isto occurreva con plure differentes configurationes de Wireguard que nos essayava, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Nos etiam essayava tunnelar portos via SSH, usante autossh e sshuttle, ma incontrava <a %(github_sshuttle)s>problemas ibi</a> (quamquam il es ancora non clar pro me si autossh suffre de problemas de TCP-super-TCP o non — illo solmente me pare un solution instabile ma forsan illo es vermente ben?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "In vice, nos retornava a connexiones directe inter servitores, celante que un servitor es in function sur le fornitore a bon mercato usante IP-filtration con UFW. Isto ha le disavantage que Docker non functiona ben con UFW, a minus que vos usa <code>network_mode: \"host\"</code>. Tote isto es un poco plus propense a errores, proque vos expose vostre servitor al internet con solmente un parve misconfiguration. Forsan nos deberea retornar a autossh — retroaction esserea multo benvenite hic."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Nos ha etiam reconsiderate Varnish contra Nginx. Nos actualmente prefere Varnish, ma illo ha su particularitates e margines aspre. Le mesme se applica a Checkmk: nos non lo ama, ma illo functiona pro ora. Weblate ha essite acceptabile ma non incredibile — io a vices time que illo perdera mi datos quando io tenta synchronisar lo con nostre repo git. Flask ha essite bon in general, ma illo ha alcun particularitates estranie que ha costate multo tempore pro debuggar, como configurar domines personalisate, o problemas con su integration de SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Usque ora le altere instrumentos ha essite excellente: nos non ha reclamos seriose super MariaDB, ElasticSearch, Gitlab, Zulip, Docker, e Tor. Tote iste ha habite alcun problemas, ma nihil troppo seriose o que consuma multo tempore."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Conclusion"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Il ha essite un experientia interessante apprender como establir un motor de recerca pro bibliotheca umbra robuste e resilient. Il ha multe plus de detalios a divider in postages futur, dunque face me saper lo que vos volerea apprender plus!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Como semper, nos cerca donationes pro supportar iste labor, dunque assecurate vos de visitar le pagina de Donationes in le Archivo de Anna. Nos etiam cerca altere typos de supporto, como subventiones, sponsores a longe termino, fornitore de pagamento a alte risco, forsan mesmo annuncios (de bon gusto!). E si vos vole contribuer vostre tempore e habilitates, nos sempre cerca disveloppatores, traductores, etc. Gratias pro vostre interesse e supporto."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Salute, io es Anna. Io creava <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, le plus grande bibliotheca umbra del mundo. Isto es mi blog personal, in le qual io e mi collegas scribe super pirateria, preservation digital, e plus."

#, fuzzy
msgid "blog.index.text2"
msgstr "Connecte con me sur <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Nota que iste sito web es solmente un blog. Nos solmente hospeda nostre proprie parolas hic. Nulle torrents o altere files con derectos de autor es hospitate o ligate hic."

#, fuzzy
msgid "blog.index.heading"
msgstr "Postages de blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B Raspamento de WorldCat"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Metter 5,998,794 libros sur IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Advertimento: iste articulo de blog ha essite deprecate. Nos ha decidite que IPFS non es ancora preste pro le prime tempore. Nos ancora ligara a files in IPFS ab le Archivo de Anna quando possibile, ma nos non lo hostara plus nos mesme, ni recommendara a alteres de specular usante IPFS. Per favor vide nostre pagina de Torrentes si tu vole adjutar a preservar nostre collection."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Le Archivo de Anna raspava tote WorldCat (le plus grande collection de metadata de bibliotheca del mundo) pro facer un lista de libros que necessita esser preservate.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Un anno retro, nos <a %(blog)s>comenciava</a> a responder a iste question: <strong>Qual es le percento de libros que ha essite permanentemente preservate per bibliothecas umbra?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Un vice que un libro entra in un bibliotheca umbra de datos aperte como <a %(wikipedia_library_genesis)s>Library Genesis</a>, e ora <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, illo es reflectite per tote le mundo (via torrents), preservante lo practicemente pro semper."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Pro responder al question de qual percento de libros ha essite preservate, nos necessita saper le denominatore: quanto libros existe in total? E idealmente nos non solmente ha un numero, ma vermente metadata. Alora nos pote non solmente comparar los contra bibliothecas umbra, ma etiam <strong>crear un lista de libros restante a preservar!</strong> Nos poterea mesmo comenciar a soniar de un effortio de collaboration pro ir a basso iste lista."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Nos rascavamos <a %(wikipedia_isbndb_com)s>ISBNdb</a>, e discargava le <a %(openlibrary)s>dataset de Open Library</a>, ma le resultatos esseva insatisfactori. Le problema principal esseva que il non habeva multe superposition de ISBNs. Vide iste diagramma de Venn de <a %(blog)s>nostre articulo de blog</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Nos esseva multo surprendite per quanto pauc superposition il habeva inter ISBNdb e Open Library, ambes del quales include liberalmente datos de varie fontes, como rascamentos web e registros de bibliothecas. Si ambes face un bon labor a trovar le major parte del ISBNs existente, lor circulos certemente haberea un substantial superposition, o un esserea un subgrupo del altere. Isto nos faceva demandar, quante libros cade <em>completemente foras de iste circulos</em>? Nos necessita un base de datos plus grande."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Es quando nos fixava nostre vistas sur le plus grande base de datos de libros in le mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Isto es un base de datos proprietari per le organisation non-profit <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliothecas de tote le mundo, in cambio de dar a illas accesso al dataset complete, e facer los apparer in le resultatos de recerca del usatores final."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Mesmo si OCLC es un non-profit, lor modello de negocio require proteger lor base de datos. Ben, nos es dispiacite a dicer, amicos de OCLC, nos lo da via completemente. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Durante le ultime anno, nos ha rascate meticulosamente tote le registros de WorldCat. Al initio, nos habeva un colpo de fortuna. WorldCat justo comenciava a displicar lor complete redesign de sito web (in aug 2022). Isto includeva un substantial renovation de lor systemas de backend, introducente multe fallas de securitate. Nos immediatemente seiziava le opportunitate, e esseva capace de rascar centos de milliones (!) de registros in solmente alcun dies."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Redesign de WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Postea, le fallas de securitate esseva lentemente fixate un per un, usque al ultime que nos trovava esseva corrigite circa un mense retro. A ille tempore nos habeva quasi tote le registros, e solmente cercava pro registros de qualitate un poco plus alte. Assi nos sentiva que es tempore de liberar los!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Vamos reguardar alcun information basic super le datos:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formato?</strong> <a %(blog)s>Contenitores de Archivo de Anna (AAC)</a>, que es essentialmente <a %(jsonlines)s>Lineas JSON</a> compresse con <a %(zstd)s>Zstandard</a>, plus alcun semanticas standardisate. Iste contenitores involucra varie typos de registros, basate super le differente rascamentos que nos displicava."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Datos"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "Un error incognite occurreva. Per favor contacta nos a %(email)s con un screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Iste moneta ha un minimo plus alte que usual. Per favor selige un differente duration o un differente moneta."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Le requesta non poteva esser completate. Per favor reproba in alcun minutas, e si illo continua a occurrer contacta nos a %(email)s con un screenshot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Error in le processament de pagamento. Per favor attende un momento e reproba. Si le problema persiste per plus de 24 horas, per favor contacta nos a %(email)s con un screenshot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "commento celate"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Problema de file: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Melior version"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Desira vos reportar iste usator pro comportamento abusive o inappropriate?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Reportar abuso"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Abuso reportate:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Vos ha reportate iste usator pro abuso."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Responder"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Por favor, <a %(a_login)s>inicie sesión</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Tu lassava un commento. Illo poterea prender un minuta pro apparer."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Alcose errava. Per favor recarga le pagina e reproba."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s paginas affectate"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Non visibile in Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Non visibile in Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Non visibile in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marcate como rupte in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Absent de Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Marcate como “spam” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Marcate como “mal file” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Non tote le paginas poteva esser convertite a PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Execution de exiftool falliva sur iste file"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Libro (incognite)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Libro (non-fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Libro (fiction)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Articulo de jornal"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Documento de standards"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Revista"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Libro de comic"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Partitura musical"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audiolibro"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Altere"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Descarga de servidor partner"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Descarga externe"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Presta externe"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Presta externe (disabilitate pro impression)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Explora metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Contenite in torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploades a Anna’s Archive"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Indice de eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadatos chec"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Libros"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Bibliotheca Estatal Russe"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titulo"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Autor"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Editor"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edition"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Anno publicate"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nomine original del file"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Description e commentos de metadatos"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Downloads del Partner Server temporarimente non disponibile pro iste file."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Servidor Partner Rapide #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(recommendate)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(nulle verification de navigator o listas de attesa)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Servidor Partner Lente #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(un poco plus rapide ma con lista de attesa)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(nulle lista de attesa, ma pote esser multo lente)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiction"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(anque clicca “GET” in alto)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(clicca “GET” in alto)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "loros annuncios es cognoscite pro continer software maligne, dunque usa un bloqueator de annuncios o non clicca annuncios"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Les files Nexus/STC pote esser inaffidable pro discargar)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Bibliotheca in Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(requere le Navigator Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Presta ab le Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(solmente pro patronos con disabilitate de impression)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI associate poterea non esser disponibile in Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "collection"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Downloads de torrent in massa"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(solmente pro expertos)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Cerca in le Archivo de Anna pro ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Cercar in varie altere bases de datos pro ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Trovar le registro original in ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Cercar in Anna’s Archive pro ID de Open Library"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Trovar le registro original in Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Cercar in Anna’s Archive pro numero OCLC (WorldCat)"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Trovar le registro original in WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Cercar in Anna’s Archive pro numero SSID de DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Cercar manualmente in DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Cercar in Anna’s Archive pro numero SSNO de CADAL"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Trovar le registro original in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Cercar in Anna’s Archive pro numero DXID de DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Indice de eBook EBSCOhost"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(nulle verification de navigator requirite)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata chec %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Libros %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadatos"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "description"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nomine alternative"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Titulo alternative"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Autor alternative"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Editor alternative"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edition alternative"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Extension alternative"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "commentos de metadatos"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Description alternative"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "data de apertura al fonte libere"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending file “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Isto es un registro de un file del Internet Archive, non un file directemente discargabile. Tu pote provar prender le libro in prestito (ligamine infra), o usar iste URL quando <a %(a_request)s>requestar un file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Si tu ha iste file e illo non es ancora disponibile in Anna’s Archive, considera <a %(a_request)s>incarcar lo</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "Registro de metadata de ISBNdb %(id)s"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Registro de metadata de Open Library %(id)s"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "Registro de metadata de numero OCLC (WorldCat) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s registro de metadata"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s registro de metadata"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s recordo de metadata"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s recordo de metadata"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Isto es un registro de metadata, non un file descargabile. Tu pote usar iste URL quando <a %(a_request)s>requestante un file</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadatos del registro vinculado"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Mejorar metadatos en Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Advertencia: múltiples registros vinculados:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Meliorar metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Reportar calidad del archivo"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Tempore de discargar"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Sito web:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Cercar in le Archivo de Anna pro “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Explorator de Codices:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Vider in le Explorator de Codices “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Leger plus…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Descargas (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Prestar (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Explorar metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Commentos (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listas (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statisticas (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Detalios technic"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Iste file pote haber problemas, e ha essite occultate ab un bibliotheca fonte.</span> A vices isto es per requesta de un detentor de copyright, a vices es proque un melior alternative es disponibile, ma a vices es proque il ha un problema con le file mesme. Il pote ancora esser ben a discargar, ma nos recommenda primo cercar un file alternative. Plus de detalios:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Un melior version de iste file pote esser disponibile a %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Si tu ancora vole discargar iste file, assecurar te de solmente usar software de fide, actualisate pro aperir lo."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Descargas rápidas"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Discargas rapide</strong> Deveni un <a %(a_membership)s>membro</a> pro supportar le preservation a longe termino de libros, documentos, e plus. Pro monstrar nostre gratia pro vostre supporto, vos recipe discargas rapide. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Si tu dona iste mense, tu recipe <strong>duple</strong> le numero de downloads rapide."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Discargas rapide</strong> Vos ha %(remaining)s restante hodie. Gratias pro esser un membro! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Discargas rapide</strong> Vos ha exhaurite vostre discargas rapide pro hodie."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Discargas rapide</strong> Vos ha discargate iste file recentemente. Ligamines remane valide pro un tempore."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Option #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(nulle redirection)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(aperir in visor)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Refera un amico, e ambe vos e vostre amico recipe %(percentage)s%% discargas rapide de bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Sape plus…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Discargas lente"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "De partnarios de fide."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Plus information in le <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(pote requirer <a %(a_browser)s>verification de navigator</a> — discargas illimitate!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Post discargar:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Aperir in nostre visor"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "mostrar descargas externas"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Discargas externe"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Nulle discargas trovate."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Tote le optiones de discarga ha le mesme file, e deberea esser secur a usar. Dicer isto, sempre sia cautelose quando discargante files del internet, specialmente de sitos externe a Anna’s Archive. Per exemplo, sia secur de mantener vostre dispositivos actualisate."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Pro grande files, nos recommenda usar un gestor de descargas pro prevenir interruptiones."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Gestores de descargas recommendate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Tu habera besonio de un lector de ebook o PDF pro aperir le file, dependente del formato del file."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Lectores de ebook recommendate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Visor online de le Archivo de Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Usa instrumentos online pro converter inter formatos."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Instrumentos de conversion recommendate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Tu pote inviar ambe files PDF e EPUB a tu Kindle o Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Instrumentos recommendate: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon‘s “Inviar a Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz‘s “Inviar a Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Supporta autores e bibliothecas"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Si tu ama isto e pote permitter lo, considera comprar le original, o supportar le autores directemente."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Si isto es disponibile a vostre bibliotheca local, considera prender lo in prestito ibi gratuitemente."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Calidad del archivo"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "¡Ayude a la comunidad reportando la calidad de este archivo! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Reportar problema del archivo (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Gran calidad del archivo (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Agregar comentario (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "¿Qué está mal con este archivo?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Por favor, use el <a %(a_copyright)s>formulario de reclamo de DMCA / Derechos de Autor</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Describa el problema (requerido)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Descripción del problema"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 de una mejor versión de este archivo (si aplica)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Complete esto si hay otro archivo que coincida estrechamente con este archivo (misma edición, misma extensión de archivo si puede encontrar uno), que las personas deberían usar en lugar de este archivo. Si conoce una mejor versión de este archivo fuera de Anna’s Archive, por favor <a %(a_upload)s>cárguela</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Puede obtener el md5 de la URL, por ejemplo,"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Enviar reporte"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Impara a <a %(a_metadata)s>meliorar le metadata</a> pro iste file tu mesme."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Gratias pro submitter tu reporto. Illo essera monstrate in iste pagina, e etiam essera revistate manualmente per Anna (usque nos ha un systema de moderation appropriate)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Alcose errava. Per favor recarga le pagina e reproba."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Si iste file ha grande qualitate, tu pote discuter alcun cosa super illo hic! Si non, per favor usa le button “Reportar problema de file”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Io amava iste libro!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lassa un commento"

#, fuzzy
msgid "common.english_only"
msgstr "Le texto infra continua in anglese."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Total de downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "Un “file MD5” es un hash que es computate ab le contento del file, e es rationabilemente unic basate super ille contento. Tote le bibliothecas umbra que nos ha indicite hic usa primarimente MD5s pro identificar files."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Un file poterea apparer in multiple bibliothecas umbra. Pro information super le varie datasets que nos ha compilate, vide le <a %(a_datasets)s>pagina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Iste es un file gestionate per le <a %(a_ia)s>bibliotheca de Prestate Digital Controlate de IA</a>, e indicite per le Archivo de Anna pro recerca. Pro information super le varie datasets que nos ha compilate, vide le <a %(a_datasets)s>pagina de Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Pro information super iste file particular, consulta su <a %(a_href)s>file JSON</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problema cargante iste pagina"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Per favor refrasca pro provar de novo. <a %(a_contact)s>Contacta nos</a> si le problema persiste per plure horas."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Non trovate"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” non esseva trovate in nostre base de datos."

#, fuzzy
msgid "page.login.title"
msgstr "Aperir session / Registrar se"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Verification del navigator"

#, fuzzy
msgid "page.login.text1"
msgstr "Pro prevenir que spam-bots crea multe contos, nos debe primo verificar tu navigator."

#, fuzzy
msgid "page.login.text2"
msgstr "Si tu es capturate in un bucla infinite, nos recommenda installar <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Il pote etiam adjutar disactivar bloqueatores de annuncios e altere extensiones del navigator."

#, fuzzy
msgid "page.codes.title"
msgstr "Codices"

#, fuzzy
msgid "page.codes.heading"
msgstr "Explorator de Codices"

#, fuzzy
msgid "page.codes.intro"
msgstr "Explora le codices con le quales le registros es etiquettate, per prefixo. Le columna “registros” monstra le numero de registros etiquettate con codices con le prefixo date, como vidite in le motor de recerca (incluse registros solmente de metadatos). Le columna “codices” monstra quante codices actual ha un prefixo date."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Iste pagina pote prender un poco de tempore pro generar, lo que es le ration proque illo require un captcha de Cloudflare. <a %(a_donate)s>Membros</a> pote saltar le captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Per favor non raspar iste paginas. In vice, nos recommenda <a %(a_import)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB, e executar nostre <a %(a_software)s>codice open source</a>. Le datos brutos pote esser explorate manualmente per files JSON como <a %(a_json_file)s>iste</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefixo"

#, fuzzy
msgid "common.form.go"
msgstr "Ir"

#, fuzzy
msgid "common.form.reset"
msgstr "Reinitialisar"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Cercar in le Archivo de Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Advertentia: le codice ha characteres Unicode incorrecte, e pote comportar se incorrectemente in varie situationes. Le binario bruto pote esser decodificate ab le representation base64 in le URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Prefixo de codice cognoscite “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefixo"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etichetta"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Description"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL pro un codice specific"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” essera substitute con le valor del codice"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL generic"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Sito web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s registro correspondente a “%(prefix_label)s”"
msgstr[1] "%(count)s registros correspondente a “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL pro codice specific: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Plus…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Codices que comencia con “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indice de"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "registros"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "codices"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Menos de %(count)s registros"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Pro reclamos de DMCA / derectos de autor, usa <a %(a_copyright)s>iste formulario</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Altere vias de contactar nos super reclamos de derectos de autor essera automaticamente delite."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Nos multo apprecia tu retroaction e questiones!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Tamen, a causa del quantitate de spam e emails sin senso que nos recipe, per favor marca le cassetta pro confirmar que tu comprende iste conditiones pro contactar nos."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Reclamos de derectos de autor a iste email essera ignorate; usa le formulario in vice."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Servitores de partner es indisponibile a causa de clausuras de hospitage. Illos deberea esser de novo disponibile tosto."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Le abonnementos essera extendite correspondentemente."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Non invia nos emails pro <a %(a_request)s>requerer libros</a><br>o parve (<10k) <a %(a_upload)s>incargamentos</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Quando tu face questiones super contos o donationes, adjunge tu ID de conto, capturas de schermo, receptas, tanto information como possibile. Nos solmente verifica nostre email cata 1-2 septimanas, assi non includer iste information retardara omne resolution."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Monstrar email"

#, fuzzy
msgid "page.copyright.title"
msgstr "Formulario de reclamo de DMCA / Derechos de autor"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Si tiene un reclamo de DMCA u otro reclamo de derechos de autor, por favor complete este formulario con la mayor precisión posible. Si encuentra algún problema, contáctenos en nuestra dirección dedicada a DMCA: %(email)s. Tenga en cuenta que los reclamos enviados por correo electrónico a esta dirección no serán procesados, es solo para preguntas. Por favor, use el formulario a continuación para enviar sus reclamos."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs en el Archivo de Anna (requerido). Una por línea. Por favor, solo incluya URLs que describan exactamente la misma edición de un libro. Si desea hacer un reclamo para múltiples libros o múltiples ediciones, por favor envíe este formulario varias veces."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Los reclamos que agrupen múltiples libros o ediciones serán rechazados."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Su nombre (requerido)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Dirección (requerido)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Número de teléfono (requerido)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "Correo electrónico (requerido)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Descripción clara del material fuente (requerido)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBNs del material fuente (si aplica). Uno por línea. Por favor, solo incluya aquellos que coincidan exactamente con la edición para la cual está reportando un reclamo de derechos de autor."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs del material fuente, uno por línea. Por favor, tómese un momento para buscar su material fuente en Open Library. Esto nos ayudará a verificar su reclamo."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs del material fuente, una por línea (requerido). Por favor, incluya tantas como sea posible, para ayudarnos a verificar su reclamo (por ejemplo, Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Declaración y firma (requerido)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Enviar reclamo"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Gracias por enviar su reclamo de derechos de autor. Lo revisaremos lo antes posible. Por favor, recargue la página para enviar otro."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Algo salió mal. Por favor, recargue la página e intente nuevamente."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Si vos es interessate in mirroring iste dataset pro <a %(a_archival)s>archivage</a> o pro <a %(a_llm)s>scopo de entramento de LLM</a>, per favor contacta nos."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Nostre mission es archivar tote le libros del mundo (como etiam articulos, revistas, etc.), e facer los largemente accessibile. Nos crede que tote le libros deberea esser mirrorrate largemente, pro garantir redundantia e resiliencia. Isto es proque nos colligente files ab un varietate de fontes. Alcun fontes es completemente aperte e pote esser mirrorrate in massa (como Sci-Hub). Altere es claudite e protecte, assi nos essaya raspar los pro “liberar” lor libros. Altere cade alicubi intermedie."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Tote nostre datos pote esser <a %(a_torrents)s>torrente</a>, e tote nostre metadata pote esser <a %(a_anna_software)s>generate</a> o <a %(a_elasticsearch)s>discargate</a> como bases de datos ElasticSearch e MariaDB. Le datos crude pote esser manualmente explorate per files JSON como <a %(a_dbrecord)s>isto</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Vista general"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Infra es un vista general rapide del fontes del files in le Archivo de Anna."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Dimension"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% reflectite per AA / torrents disponibile"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Percentages del numero de files"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Ultime actualisation"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-Fiction e Fiction"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s file"
msgstr[1] "%(count)s files"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: congelate desde 2021; le major parte disponibile per torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: additiones minor desde alora</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Excludente “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrents de fiction es in retard (ben que IDs ~4-6M non torrenteate desde que illos se superpone con nostre torrents de Zlib)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Le collection “Chinese” in Z-Library pare esser le mesme que nostre collection DuXiu, ma con differente MD5s. Nos exclue iste files de torrents pro evitar duplication, ma ancora los monstra in nostre indice de recerca."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Prunte Digital Controlate"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ de files es recercabile."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Total"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Excludente duplicatos"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Como le bibliothecas de umbra sovente synchronisa datos de unes al alteres, il ha considerable superposition inter le bibliothecas. Es pro isto que le numeros non sume al total."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Le percentage “reflectite e seminate per Anna’s Archive” monstra quante files nos reflecte nos mesme. Nos semina iste files in massa per torrents, e los rende disponibile pro download directe per sitos web de partenarios."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bibliothecas fonte"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Alcuni bibliothecas fonte promove le compartimento massive de lor datos per medio de torrents, durante que alteres non comparti lor collectiones facilmente. In iste ultime caso, le Archivo de Anna tenta raspar lor collectiones e render los disponibile (vide nostre pagina de <a %(a_torrents)s>Torrents</a>). Il ha etiam situationes intermedie, per exemplo, ubi le bibliothecas fonte es disposte a compartir, ma non ha le recursos pro facer lo. In iste casos, nos etiam tenta adjutar."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Infra es un vista general de como nos interfacia con le differente bibliothecas fonte."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Files"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dumpes quotidian de base de datos <a %(dbdumps)s>HTTP</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrents automate pro <a %(nonfiction)s>Non-Fiction</a> e <a %(fiction)s>Fiction</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(covers)s>torrentes de copertura de libros</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub ha congelate nove files desde 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dumpes de metadata disponibile <a %(scihub1)s>ci</a> e <a %(scihub2)s>ci</a>, como parte del <a %(libgenli)s>base de datos de Libgen.li</a> (que nos usa)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrentes de datos disponibile <a %(scihub1)s>ci</a>, <a %(scihub2)s>ci</a>, e <a %(libgenli)s>ci</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Alcun nove files es <a %(libgenrs)s>essente</a> <a %(libgenli)s>addite</a> al “scimag” de Libgen, ma non bastante pro justificar nove torrentes"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dumpes de base de datos HTTP trimestral <a %(dbdumps)s>ci</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrentes de non-fiction es partagiate con Libgen.rs (e mirroreate <a %(libgenli)s>ci</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Le Archivo de Anna e Libgen.li gerenti collaborative collectiones de <a %(comics)s>libros comic</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos standard</a>, e <a %(fiction)s>fiction (divergite de Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Lor collection “fiction_rus” (fiction russe) non ha torrents dedicate, ma es coperite per torrents de alteres, e nos mantene un <a %(fiction_rus)s>speculo</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Le Archivo de Anna e Z-Library gestiona collaborative un collection de <a %(metadata)s>metadata de Z-Library</a> e <a %(files)s>files de Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Alcun metadata disponibile per <a %(openlib)s>dumpes de base de datos de Open Library</a>, ma illos non coperi le integre collection de IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Nulle dumpes de metadata facilemente accessibile disponibile pro lor integre collection"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(ia)s>metadata de IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Files solmente disponibile pro prestar in un maniera limitate, con varie restrictiones de accesso"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(ia)s>files de IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Variate bases de datos de metadata dispergite in le internet chinese; ben que sovente bases de datos pagate"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Nulle dumpes de metadata facilemente accessibile disponibile pro lor integre collection."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(duxiu)s>metadata de DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Vari variate bases de datos de files disperse in internet chines; ben que sovente bases de datos pagate"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Le major parte del files es solmente accessibile con contos premium de BaiduYun; velocitates de discargamento lente."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(duxiu)s>files DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Variate fontes minor o unic. Nos incoragia personas a cargar a altere bibliothecas umbra primo, ma a vices personas ha collectiones que es troppo grande pro alteres a ordinar, ben que non bastante grande pro justificar lor proprie categoria."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Fontes de solmente metadatos"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Nos etiam arricchisce nostre collection con fontes de solmente metadatos, que nos pote associar a files, p. ex. usante numeros ISBN o altere campos. Infra es un vista general de illos. De novo, alcun de iste fontes es completemente aperte, durante que pro alteres nos debe raspar los."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Nostre inspiration pro colliger metadata es le objectivo de Aaron Swartz de “un pagina web pro cata libro jammais publicate”, pro le qual ille creava <a %(a_openlib)s>Open Library</a>. Iste projecto ha succedite ben, ma nostre position unic nos permitte obtener metadata que illes non pote. Un altere inspiration esseva nostre desiro de saper <a %(a_blog)s>quanto libros il ha in le mundo</a>, pro que nos pote calcular quanto libros nos ancora ha a salvar."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Nota que in le recerca de metadatos, nos monstra le registros original. Nos non face alcun fusion de registros."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Ultime actualisation"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Dumpes de base de datos mensual <a %(dbdumps)s> </a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Non disponibile directemente in massa, protegite contra raspatio"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Base de datos unificate"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Nos combina tote le fontes supra in un sol base de datos unificate que nos usa pro servir iste sito web. Iste base de datos unificate non es disponibile directemente, ma pois que le Archivo de Anna es completemente open source, illo pote esser <a %(a_generated)s>generate</a> o <a %(a_downloaded)s>discargate</a> como bases de datos ElasticSearch e MariaDB. Le scriptos in ille pagina discargara automaticamente tote le metadatos necessari ab le fontes mentionate supra."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Si vos vole explorar nostre datos ante executar iste scriptos localmente, vos pote regardar nostre files JSON, que liga ulteriormente a altere files JSON. <a %(a_json)s>Iste file</a> es un bon puncto de initio."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Adaptate de nostre <a %(a_href)s>poste de blog</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> es un massive base de datos de libros scannate, create per le <a %(superstar_link)s>SuperStar Digital Library Group</a>. Le major parte es libros academic, scannate pro render los disponibile digitalmente a universitates e bibliothecas. Pro nostre publico anglo-parlante, <a %(princeton_link)s>Princeton</a> e le <a %(uw_link)s>Universitate de Washington</a> ha bon summarios. Il ha etiam un excellente articulo que da plus de contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Le libros de Duxiu ha essite longemente pirate in le internet chinese. Usualmente illos es vendite per minus de un dollar per revendedores. Illos es typicamente distribuite usante le equivalente chinese de Google Drive, que sovente ha essite hackate pro permitter plus de spatio de immagazinage. Alcun detalios technic se trova <a %(link1)s>hic</a> e <a %(link2)s>hic</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Ben que le libros ha essite semi-publicamente distribuite, es bastante difficile obtener los in massa. Nos habeva isto alte in nostre lista de cosas a facer, e allocava plure menses de labor a plen tempore pro isto. Tamen, in le fin de 2023 un voluntario incredibile, stupefaciente, e talentose nos contactava, dicente que ille habeva ja facite tote iste labor — a grande costo. Ille partiva le collection integre con nos, sin expectar nihil in retorno, excepte le garantia de preservation a longe termino. Vermente remarcabile."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resources"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Total de files: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Dimension total de files: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Files mirroreate per Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Ultime actualisation: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrentes per Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exemplo de registro in Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Nostre poste de blog super iste datos"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Scriptos pro importar metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Formato de Contenitores de Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Plus information de nostre voluntarios (notationes crude):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Este conjunto de datos está estrechamente relacionado con el <a %(a_datasets_openlib)s>conjunto de datos de Open Library</a>. Contiene una recopilación de todos los metadatos y una gran parte de los archivos de la Biblioteca de Préstamo Digital Controlado de IA. Las actualizaciones se publican en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Estos registros se refieren directamente al conjunto de datos de Open Library, pero también contienen registros que no están en Open Library. También tenemos varios archivos de datos recopilados por miembros de la comunidad a lo largo de los años."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "La colección consta de dos partes. Necesita ambas partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "nostre prime publication, ante que nos standardisava in le <a %(a_aac)s>Formato de Contenitores de Anna’s Archive (AAC)</a>. Contine metadata (como json e xml), pdfs (ab systemas de prestito digital acsm e lcpdf), e miniaturas de coperaturas."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "nove publicationes incremental, usante AAC. Contine solmente metadata con marcas temporal post 2023-01-01, pois le resto es ja coperite per “ia”. Anque tote le files pdf, iste vice ab le systemas de prestito acsm e “bookreader” (le lector web de IA). Nonobstante le nomine non es exactemente correcte, nos ancora populava files de bookreader in le collection ia2_acsmpdf_files, pois illos es mutualemente exclusive."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Sito principal %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Bibliotheca Digital de Prestito"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Documentation de Metadata (le major parte de campos)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Information de paises de ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Le International ISBN Agency publica regularmente le intervallos que illo ha allocate a agencias national de ISBN. De isto nos pote derivar a qual pais, region, o gruppo de linguas pertine iste ISBN. Nos actualmente usa iste datos indirectemente, per le bibliotheca Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resursos"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Ultime actualisation: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Sito web de ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadatos"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Pro le historia del differente forcos de Library Genesis, vide le pagina pro <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Le Libgen.li contine le majoritate del mesme contento e metadatos como le Libgen.rs, ma ha alcun collectiones in plus de isto, nominatemente comics, revistas, e documentos standard. Illo ha tamben integrate <a %(a_scihub)s>Sci-Hub</a> in su metadatos e motor de recerca, que es lo que nos usa pro nostre base de datos."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Le metadatos pro iste bibliotheca es libere disponibile <a %(a_libgen_li)s>a libgen.li</a>. Tamen, iste servitor es lente e non supporta le resumition de connexiones interrumpite. Le mesme files es tamben disponibile in <a %(a_ftp)s>un servitor FTP</a>, que functiona melior."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents es disponibile pro le major parte del contento addicional, notabilemente torrents pro libros comic, revistas, e documentos standard ha essite publicate in collaboration con le Archivo de Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Le collection de fiction ha su proprie torrents (divergente de <a %(a_href)s>Libgen.rs</a>) comenciante a %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Secundo le administrator de Libgen.li, le collection “fiction_rus” (fiction russe) deberea esser coperite per torrents regularmente publicate de <a %(a_booktracker)s>booktracker.org</a>, notabilemente le torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que nos specula <a %(a_torrents)s>hic</a>, ben que nos non ha ancora determinate quales torrents corresponde a quales files)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statisticas pro tote le collectiones pote esser trovate <a %(a_href)s>in le sito de libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fiction pare sembla diverger, ma sin nove torrents. Illo pare que isto ha occurrite desde le initio de 2022, ben que nos non ha verificate isto."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Certe intervallos sin torrents (como le intervallos de fiction f_3463000 a f_4260000) es probabilemente files de Z-Library (o altere duplicatos), ben que nos poterea voler facer un deduplication e crear torrents pro files unice a lgli in iste intervallos."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Nota que le files de torrent referente a “libgen.is” es explicitemente speculos de <a %(a_libgen)s>Libgen.rs</a> (“.is” es un dominio differente usate per Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Un recurso utile pro usar le metadata es <a %(a_href)s>iste pagina</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrentos de fiction in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrentos de comics in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrentos de revistas in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrents de documentos standard in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrents de fiction russe in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Information de campo de metadata"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Speculo de altere torrentos (e torrentos unic de fiction e comics)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Foro de discussion"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Nostre articulo de blog super le publication de libros de comics"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Le breve historia del differente forcos de Library Genesis (o “Libgen”), es que con le tempore, le differente personas implicate con Library Genesis habeva un disaccordo, e se separava."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Le version “.fun” esseva create per le fundador original. Illo es essente renovate in favor de un nove version plus distribuite."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Le version “.rs” ha datos multo simile, e publica su collection in torrentos massive de maniera consistente. Illo es approximativemente dividite in un section de “fiction” e un section de “non-fiction”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Originalmente a “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Le <a %(a_li)s>version “.li”</a> ha un massive collection de comics, assi como altere contento, que non es (ancora) disponibile pro download massive via torrentos. Illo ha un collection de torrentos separate de libros de fiction, e contine le metadata de <a %(a_scihub)s>Sci-Hub</a> in su base de datos."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Secundo iste <a %(a_mhut)s>poste in le foro</a>, Libgen.li esseva originalmente hospitate a “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> in alcun senso es etiam un forco de Library Genesis, ben que illes usava un nomine differente pro lor projecto."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Iste pagina es super le version “.rs”. Illo es cognoscite pro publicar consistentemente tanto su metadata como le contento integre de su catalogo de libros. Su collection de libros es dividite inter un parte de fiction e un parte de non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Un recurso utile pro usar le metadata es <a %(a_metadata)s>iste pagina</a> (bloca intervallos de IP, VPN pote esser requirite)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "A partir de 2024-03, nove torrents es publicate in <a %(a_href)s>iste filo de foro</a> (bloca intervallos de IP, VPN pote esser necessari)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrents de non-fiction in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrents de fiction in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadata de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Information de campos de metadata de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrents de non-fiction de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrents de fiction de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Foro de discussion de Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents per le Archivo de Anna (coperturas de libros)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Nostre blog super le publication de coperturas de libros"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis es cognoscite pro ja generose facer lor datos disponibile in massa via torrents. Nostre collection de Libgen consiste de datos auxiliar que illes non publica directemente, in collaboration con illes. Multo gratias a omnes implicate con Library Genesis pro laborar con nos!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Publication 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Iste <a %(blog_post)s>prime publication</a> es bastante parve: circa 300GB de coperturas de libros del bifurcation Libgen.rs, tanto fiction como non-fiction. Illos es organisate in le mesme maniera que illos appare in libgen.rs, p. ex.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s pro un libro de non-fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s pro un libro de fiction."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Exactemente como con le collection de Z-Library, nos los pone tote in un grande file .tar, que pote esser montate usante <a %(a_ratarmount)s>ratarmount</a> si vos vole servir le files directemente."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> es un base de datos proprietari per le organisation non-profit <a %(a_oclc)s>OCLC</a>, que agrega registros de metadata de bibliothecas de tote le mundo. Il es probabilemente le plus grande collection de metadata de bibliotheca in le mundo."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Octobre 2023, version initial:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "In octobre 2023 nos <a %(a_scrape)s>publicava</a> un raspamento comprensive del base de datos OCLC (WorldCat), in le <a %(a_aac)s>Formato de Contenitores del Archivo de Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents per le Archivo de Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Nostre articulo de blog super iste datos"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library es un projecto open source per le Internet Archive pro catalogar cata libro in le mundo. Illo ha un del plus grande operationes de scannar libros in le mundo, e ha multe libros disponibile pro prestito digital. Su catalogo de metadata de libros es liberemente disponibile pro download, e es includite in le Archivo de Anna (ben que non actualmente in le recerca, excepte si vos explicitemente cerca un ID de Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Version 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Isto es un dump de multe appellos a isbndb.com durante septembre 2022. Nos essayava coperir tote le intervallos de ISBN. Iste es circa 30.9 milliones de registros. In lor sito web illes asserta que illes ha actualemente 32.6 milliones de registros, assi nos poterea haber mancate alcunes, o <em>illes</em> poterea facer qualcosa incorrecte."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Le responsas JSON es quasi crude ab lor servitor. Un problema de qualitate de datos que nos notava, es que pro numeros ISBN-13 que comencia con un prefixo differente de “978-”, illes ancora include un campo “isbn” que simplemente es le numero ISBN-13 con le prime 3 numeros removite (e le digito de controlo recalculato). Isto es obviemente incorrecte, ma isto es como illes sembla facer lo, assi nos non lo alterava."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Un altere problema potential que vos poterea incontrar, es le facto que le campo “isbn13” ha duplicatos, assi vos non pote usar lo como un clave primari in un base de datos. Le campos “isbn13”+“isbn” combinate pare esser unic."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Pro le informationes de fundo super Sci-Hub, per favor consulta su <a %(a_scihub)s>sito official</a>, <a %(a_wikipedia)s>pagina de Wikipedia</a>, e iste <a %(a_radiolab)s>intervista in podcast</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Nota que Sci-Hub ha essite <a %(a_reddit)s>congelate desde 2021</a>. Illo esseva congelate antea, ma in 2021 un pauc de milliones de articulos esseva addite. Totevia, un numero limitate de articulos es ancora addite al collectiones “scimag” de Libgen, ben que non bastante pro justificar nove torrents in massa."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Nos usa le metadata de Sci-Hub como fornite per <a %(a_libgen_li)s>Libgen.li</a> in su collection “scimag”. Nos tamben usa le dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Nota que le torrents “smarch” es <a %(a_smarch)s>deprecate</a> e ergo non includite in nostre lista de torrents."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents in le Archivo de Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata e torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents in Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents in Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Actualisationes in Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Pagina de Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Intervista in podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Carga a Archivo de Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Vista general desde la <a %(a1)s>página de datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Diverse fontes minor o unice. Nos incoragia personas a cargar a altere bibliothecas umbra primo, ma a vices personas ha collectiones que es troppo grande pro alteres a ordinar, ben que non bastante grande pro justificar lor proprie categoria."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Le collection “upload” es dividite in subcollectiones plus parve, que es indicate in le AACIDs e nomines de torrent. Tote le subcollectiones esseva primo deduplicate contra le collection principal, ben que le files JSON de metadata “upload_records” ancora contine multe referentias al files original. Files non-librari tamben esseva removite de multe subcollectiones, e es typicamente <em>non</em> notate in le “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Multes subcollectiones mesme es componite de sub-sub-collectiones (p.ex. de differente fontes original), que es representate como directorios in le campos “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Le subcollectiones es:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subcolección"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notas"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "navigar"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cercar"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "De <a %(a_href)s>aaaaarg.fail</a>. Pare esser bastante complete. De nostre voluntario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Ex un torrent de <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Ha un superposition bastante alte con collectiones de articulos existente, ma multo pauc correspondencias de MD5, assi nos decideva retener lo completemente."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Raspado de <q>iRead eBooks</q> (= fonéticamente <q>ai rit i-books</q>; airitibooks.com), por el voluntario <q>j</q>. Corresponde a la metadata de <q>airitibooks</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "De una colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte de la fuente original, parte de the-eye.eu, parte de otros espejos."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Ex un sito web private de torrent de libros, <a %(a_href)s>Bibliotik</a> (spisse referite como “Bib”), del qual libros esseva combinate in torrents per nomine (A.torrent, B.torrent) e distribuite per the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Ex nostre voluntario “bpb9v”. Pro plus information super <a %(a_href)s>CADAL</a>, vide le notas in nostre <a %(a_duxiu)s>pagina de datos de DuXiu</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Plus de nostre voluntario “bpb9v”, major parte files de DuXiu, assi como un dossier “WenQu” e “SuperStar_Journals” (SuperStar es le compania detra DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Ex nostre voluntario “cgiym”, textos chinese de diverse fontes (representate como subdirectorias), includente de <a %(a_href)s>China Machine Press</a> (un major editor chinese)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Collectiones non-chinese (representate como subdirectorios) de nostre voluntario “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Raspado de libros sobre arquitectura china, por el voluntario <q>cm</q>: <q>Lo obtuve explotando una vulnerabilidad de red en la editorial, pero esa brecha ya ha sido cerrada</q>. Corresponde a la metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Libros de casa editorial academic <a %(a_href)s>De Gruyter</a>, colligite de alcun grande torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Raspamento de <a %(a_href)s>docer.pl</a>, un sito polonese de compartimento de files focalisate in libros e altere obras scribite. Raspate in le fin de 2023 per voluntario “p”. Nos non ha bon metadata del sito original (nec mesmo extensiones de file), ma nos filtrava pro files simile a libros e sovente poteva extraher metadata del files mesme."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, directemente de DuXiu, colligite per voluntario “w”. Solmente libros recente de DuXiu es disponibile directemente via ebooks, assi que le major parte de iste debe esser recente."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Files DuXiu restante de voluntario “m”, que non esseva in le formato proprietari PDG de DuXiu (le principal <a %(a_href)s>dataset DuXiu</a>). Colligite de multe fontes original, malfortunatemente sin preservar ille fontes in le filepath."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Raspado de libros eróticos, por el voluntario <q>do no harm</q>. Corresponde a la metadata de <q>hentai</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Collection raspat de un editor de Manga japonese per voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Archivos judiciales selecte de Longquan</a>, fornite per voluntario “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Raspatio de <a %(a_href)s>magzdb.org</a>, un alliate de Library Genesis (illo es ligate in le pagina initial de libgen.rs) ma qui non voleva provider lor files directemente. Obtenite per voluntario “p” in le fin de 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Variate parve cargas, troppo parve pro esser lor proprie subcollection, ma representate como directorios."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks de AvaxHome, un sitio web ruso para compartir archivos."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Archivo de periódicos y revistas. Corresponde a la metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Raspado del <a %(a1)s>Centro de Documentación de Filosofía</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Collection de voluntario “o” qui colligiva libros polonese directemente de sitos de publication original (“scena”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Collectiones combinate de <a %(a_href)s>shuge.org</a> per voluntarios “cgiym” e “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Bibliotheca Imperial de Trantor”</a> (nominate post le bibliotheca fictional), raspate in 2022 per voluntario “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-collectiones (representate como directorios) de voluntario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (per <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, mi parve libreria — woz9ts: “Iste sito se concentra principalmente in compartir files de ebooks de alte qualitate, alcunes del quales es compaginate per le proprietario mesme. Le proprietario esseva <a %(a_arrested)s>arrestate</a> in 2019 e alicuno faceva un collection de files que ille compartiva.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Files restante de DuXiu de voluntario “woz9ts”, que non esseva in le formato proprietari PDG de DuXiu (ancora a converter a PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrentes per Archivo de Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Raspamento de Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library ha su radices in le communitate de <a %(a_href)s>Library Genesis</a>, e originalmente se initiava con lor datos. Desde alora, illo se ha professionalisate considerabilemente, e ha un interfacie multo plus moderne. Illes es ergo capabile de reciper multe plus donationes, tanto pecuniari pro continuar meliorar lor sito web, como donationes de nove libros. Illes ha accumulate un grande collection in addition a Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Actualisation de februario 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "In le fin de 2022, le presunte fundatores de Z-Library esseva arrestate, e dominios esseva confiscate per autoritates del Statos Unite. Desde alora le sito web ha lentemente reemergite online. Es incognite qui lo opera actualmente."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Le collection consiste de tres partes. Le paginas de description original pro le prime duo partes es preservate infra. Tu necessita tote tres partes pro obtener tote le datos (excepto torrents superate, que es barrate in le pagina de torrents)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: nostre prime publication. Isto esseva le prime publication de lo que tunc esseva appellate le “Speculo de Bibliotheca Pirata” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: secunde publication, iste vice con tote le files involvite in files .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: nove publicationes incremental, usante le <a %(a_href)s>Formato de Contenitores de Archivo de Anna (AAC)</a>, ora publicate in collaboration con le equipa de Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrentes per Archivo de Anna (metadata + contento)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exemplo de registro in Archivo de Anna (collection original)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exemplo de registro in Archivo de Anna (collection “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Sito principal"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Dominio Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Posta de blog super le Lancemento 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Posto de blog super le Lancemento 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Lancementos de Zlib (paginas de description original)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Lancemento 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Le speculo initial esseva obtenite con grande efforto durante le curso de 2021 e 2022. A iste puncto illo es un poco obsolete: illo reflecte le stato del collection in junio 2021. Nos actualisara isto in le futuro. Ora nos es focalisate in liberar iste prime version."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Como Library Genesis es ja preservate con torrents public, e es includite in le Z-Library, nos faceva un deduplication basic contra Library Genesis in junio 2022. Pro isto nos usava hashes MD5. Il es probabile que il ha multo plus contento duplicate in le bibliotheca, como multiple formatos de file con le mesme libro. Isto es difficile a detectar con precision, assi nos non lo face. Post le deduplication nos remane con plus de 2 milliones de files, totalisante justo sub 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Le collection consiste de duo partes: un dump MySQL “.sql.gz” del metadata, e le 72 files torrent de circa 50-100GB cata uno. Le metadata contine le datos como reportate per le sito de Z-Library (titulo, autor, description, typologia de file), assi como le dimension real del file e le md5sum que nos observava, pois a vices iste non concorda. Il pare que il ha intervallos de files pro le quales le mesme Z-Library ha metadata incorrecte. Nos poterea etiam haber files incorrectemente discargate in alcun casos isolate, que nos va tentar de detectar e corriger in le futuro."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Le grande files torrent contine le datos real del libros, con le ID de Z-Library como le nomine del file. Le extension de file pote esser reconstruite usante le dump de metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Le collection es un mixtura de contento non-fiction e fiction (non separate como in Library Genesis). Le qualitate es etiam multo variabile."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Iste prime lancemento es ora totalmente disponibile. Nota que le files torrent es solmente disponibile via nostre speculo Tor."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Lancemento 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Nos ha obtenite tote le libros que esseva addite al Z-Library inter nostre ultime speculo e augusto 2022. Nos ha etiam retrocedite e raspat alcun libros que nos mancava le prime vice. In total, iste nove collection es circa 24TB. De novo, iste collection es deduplicate contra Library Genesis, pois il ha ja torrents disponibile pro ille collection."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Le datos es organisate similarmente al prime lancemento. Il ha un dump MySQL “.sql.gz” del metadata, que etiam include tote le metadata del prime lancemento, superandolo assi. Nos etiam addite alcun nove columnas:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: si iste file es ja in Library Genesis, in le collection non-fiction o fiction (correspondite per md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: in qual torrent iste file es."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: indicate quando nos non poteva discargar le libro."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Nos mentionava isto le ultime vice, ma solmente pro clarificar: “filename” e “md5” es le proprietates actual del file, durante que “filename_reported” e “md5_reported” es lo que nos raspava de Z-Library. A vices iste duo non concorda, assi que nos includeva ambes."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Pro iste version, nos cambiava le collocation a “utf8mb4_unicode_ci”, que deberea esser compatibile con versiones plus vetule de MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Le files de datos es simile al ultime vice, ben que illos es multo plus grande. Nos simplemente non poteva esser molestate a crear multe files de torrent plus parve. “pilimi-zlib2-0-14679999-extra.torrent” contine tote le files que nos mancava in le ultime version, durante que le altere torrents es tote nove intervallos de ID. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Actualisation %(date)s:</strong> Nos faceva le major parte de nostre torrents troppo grande, causante difficultates pro le clientes de torrent. Nos los ha removite e publicate nove torrents."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Actualisation %(date)s:</strong> Il habeva ancora tropo multe files, assi nos los envolvite in files tar e publicate nove torrents de novo."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Addendum al Lancemento 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Isto es un sol file torrent extra. Il non contine nove information, ma il ha alcun datos in illo que pote prender tempore a calcular. Isto lo rende convenibile a haber, pois discargar iste torrent es sovente plus rapide que calcular lo ab initio. In particular, il contine indices SQLite pro le files tar, pro uso con <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Questiones Frequente (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Que es le Archivo de Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Le Archivo de Anna</span> es un projecto non-profit con duo objectivos:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservation:</strong> Salvagardar tote le cognoscentia e cultura del humanitate.</li><li><strong>Acceso:</strong> Render iste cognoscentia e cultura disponibile a qualcunque persona in le mundo.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Tote nostre <a %(a_code)s>codice</a> e <a %(a_datasets)s>datos</a> es completemente open source."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Preservation"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Nos preserva libros, articulos, comicos, revistas, e plus, per portar iste materiales ab varie <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">bibliothecas umbra</a>, bibliothecas official, e altere collectiones in un sol loco. Tote iste datos es preservate pro semper per facer los facile a duplicar in massa — usante torrents — resultante in multe copias circa le mundo. Alcun bibliothecas umbra ja face isto mesme (p.ex. Sci-Hub, Library Genesis), durante que le Archivo de Anna “libera” altere bibliothecas que non offere distribution in massa (p.ex. Z-Library) o que non es bibliothecas umbra del toto (p.ex. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Iste larga distribution, combinate con codice open source, rende nostre sito web resistente a clausuras, e assecurar le preservation a longe termino del cognoscentia e cultura del humanitate. Discoperi plus super <a href=\"/datasets\">nostre datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Nos estima que nos ha preservate circa <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% del libros del mundo</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Acceso"

#, fuzzy
msgid "page.home.access.text"
msgstr "Nos collabora con socios pro render nostre collectiones facilemente e libere accessibile a qualcunque persona. Nos crede que omne persona ha le derecto al sapientia collectivate del humanitate. E <a %(a_search)s>non al expensas del autores</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Descargas horari in le ultime 30 dies. Medie horari: %(hourly)s. Medie quotidian: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Nos crede fortemente in le fluxo libere de information, e in le preservation del cognoscentia e cultura. Con iste motor de recerca, nos construye super le spatulas de gigantes. Nos ha un profunde respecto pro le duro labor del personas qui ha create le varie bibliothecas umbra, e nos spera que iste motor de recerca ampliara lor alcance."

#, fuzzy
msgid "page.about.text3"
msgstr "Pro remaner actualisate super nostre progresso, seque Anna in <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Pro questiones e retroaction, per favor contacta Anna a %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Como pote io adjutar?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Seque nos in <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Diffunde le parola super le Archivo de Anna in Twitter, Reddit, Tiktok, Instagram, in tu cafe o bibliotheca local, o ubi tu vade! Nos non crede in le custodia de portales — si nos es clausurate, nos simplemente reappare alicubi altere, pois tote nostre codice e datos es completemente open source.</li><li>3. Si tu pote, considera <a href=\"/donate\">donar</a>.</li><li>4. Adjuta <a href=\"https://translate.annas-software.org/\">traducer</a> nostre sito web in differente linguas.</li><li>5. Si tu es un ingeniero de software, considera contribuir a nostre <a href=\"https://annas-software.org/\">open source</a>, o semenar nostre <a href=\"/datasets\">torrents</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Nos ha ora anque un canal Matrix syncronisate a %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Si tu es un ricercator de securitate, nos pote usar tu habilitates pro ambe offensa e defension. Verifica nostre pagina de <a %(a_security)s>Securitate</a>."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Nos cerca expertos in pagos pro mercantes anonyme. Pote tu adjutar nos adder plus de manieras convenibile pro donar? PayPal, WeChat, cartas de dono. Si tu cognosce alicuno, per favor contacta nos."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Nos es semper in cerca de plus capacitate de servitores."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Vos pote adjutar reportante problemas de files, lassante commentarios, e creando listas directemente in iste sito web. Vos pote etiam adjutar <a %(a_upload)s>incargante plus libros</a>, o corrigente problemas de files o formattation de libros existente."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Crea o adjuta a mantener le pagina de Wikipedia pro Anna’s Archive in tu lingua."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Nos cerca placer parve, tasteful annuncios. Si vos vole annunciar in Anna’s Archive, per favor informa nos."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Nos amarea que personas installa <a %(a_mirrors)s>mirrors</a>, e nos supportara isto financialmente."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Pro informationes plus extensive super como voluntariar, vide nostre pagina <a %(a_volunteering)s>Voluntariato & Recompensas</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Perque le descargas lente es tanto lente?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Nos literalemente non ha bastante recursos pro dar a omnes in le mundo descargas a alte velocitate, tanto como nos volerea. Si un benefactor ric volerea emerger e provider isto pro nos, illo esserea incredibile, ma usque alora, nos face nostre melior. Nos es un projecto non-lucrative que apenas pote sustener se mesme per donationes."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Es pro isto que nos implementeva duo systemas pro descargas gratuite, con nostre socios: servitores partite con descargas lente, e servitores un poco plus rapide con un lista de attesa (pro reducer le numero de personas que descarga al mesme tempore)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Nos ha etiam <a %(a_verification)s>verification de navigator</a> pro nostre descargas lente, proque alteremente bots e scrapers los abusarea, rendente le cosas mesmo plus lente pro usatores legitime."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Nota que, quando usante le Tor Browser, vos poterea necessitar ajustar vostre configurationes de securitate. Al plus basse del optiones, appellate “Standard”, le desafio de turnstile de Cloudflare succede. Al optiones plus alte, appellate “Safer” e “Safest”, le desafio falle."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "A causa del grandor del files, a vices le descargas lente pote interrumper se in le medio. Nos recommenda usar un gestor de descargas (como JDownloader) pro reprender automaticamente descargas grande."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "FAQ de Donationes"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Le adhesiones se renova automaticamente?</div> Le adhesiones <strong>non</strong> se renova automaticamente. Vos pote junger pro tanto tempore o tanto breve como vos vole."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Pote io meliorar mi adhesion o obtener multiple adhesiones?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Vos ha altere methodos de pagamento?</div> Actualmente non. Multes non vole que archivos como iste existe, assi nos debe esser cautelose. Si vos pote adjutar nos a establir altere methodos de pagamento (plus convenibile) in maniera secur, per favor contacta nos a %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Que significa le intervallos per mense?</div> Tu pote attinger le latere inferior de un intervallo applicando tote le discontos, como seliger un periodo plus long que un mense."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>In que vos dispendi le donationes?</div> 100%% va a preservar e render accessibile le cognoscentia e cultura del mundo. Actualmente nos lo dispendi majormente in servitores, immagazinamento, e banda. Nulle moneta va personalemente a membros del equipa."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Pote io facer un grande donation?</div> Isto esserea stupende! Pro donationes de plus que alcun milles de dollares, per favor contacta nos directemente a %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Pote io facer un donation sin devenir membro?</div> Certemente. Nos accepta donationes de qualcunque quantitate a iste adresse de Monero (XMR): %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Como io incarga nove libros?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativemente, vos pote incargar los a Z-Library <a %(a_upload)s>ci</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Pro parve cargas (usque a 10,000 files) per favor carga los a ambe %(first)s e %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Pro ora, nos suggere incargar nove libros al forks de Library Genesis. Ecce un <a %(a_guide)s>guida utile</a>. Nota que ambe forks que nos indexa in iste sito web tira de iste mesme systema de incargamento."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Pro Libgen.li, assecurate vos de primo aperir session in <a %(a_forum)s >lor foro</a> con nomine de usator %(username)s e contrasigno %(password)s, e postea retornar a lor <a %(a_upload_page)s >pagina de carga</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Si tu adresse de email non functiona in le foros de Libgen, nos recommenda usar <a %(a_mail)s>Proton Mail</a> (gratuite). Vos pote etiam <a %(a_manual)s>requestar manualmente</a> que tu conto sia activate."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Nota que mhut.org bloca certe intervallos de IP, assi un VPN poterea esser requirite."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Pro grande incargamentos (plus de 10,000 files) que non es acceptate per Libgen o Z-Library, per favor contacta nos a %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Pro incargar documentos academic, per favor etiam (in addition a Library Genesis) incarga los a <a %(a_stc_nexus)s>STC Nexus</a>. Illes es le melior biblioteca umbra pro nove documentos. Nos non los ha integrate ancora, ma nos lo facera a un certe puncto. Vos pote usar lor <a %(a_telegram)s>bot de incargamento in Telegram</a>, o contactar le adresse listate in lor message fixate si vos ha troppo files pro incargar in iste maniera."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Como io pote requestar libros?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "A iste tempore, nos non pote accomodar requestas de libros."

#, fuzzy
msgid "page.request.forums"
msgstr "Per favor face tu requestas in le foros de Z-Library o Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Non invia nos requestas de libros per email."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Collecta vos metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Nos lo face vermente."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Io discargava 1984 per George Orwell, le policia venira a mi porta?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Non preoccupa te troppo, il ha multe personas discargante de sitos web ligate per nos, e es extrememente rar haber problemas. Tamen, pro remaner secur nos recommenda usar un VPN (pagate), o <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Como io pote salvar mi configurationes de recerca?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selige le configurationes que tu prefere, lassa le quadro de recerca vacue, clicca “Recerca”, e postea marca le pagina con le function de marcatores de tu navigator."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Habes vos un app mobile?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Nos non ha un app mobile official, ma tu pote installar iste sito web como un app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Clicca le menu a tres punctos in le angulo superior dextere, e selige “Adder a Schermo Principal”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Clicca le button “Compartir” in le fundo, e selige “Adder a Schermo Principal”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Habes vos un API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Nos ha un API JSON stabile pro membros, pro obtener un URL de discarga rapide: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation intra le JSON mesme)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Pro altere casos de uso, como iterar per tote nostre files, construir recercas personalisate, e assi via, nos recommenda <a %(a_generate)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Le datos brutos pote esser explorate manualmente <a %(a_explore)s>per files JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Nostre lista de torrents brutos pote esser discargate como <a %(a_torrents)s>JSON</a> etiam."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Io volerea adjutar a semenar, ma io non ha multo spatio de disco."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Usa le <a %(a_list)s>generatore de lista de torrent</a> pro generar un lista de torrents que es le plus in necessitate de torrentar, intra le limites de tu spatio de immagazinamento."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Le torrents es troppo lente; pote io discargar le datos directemente de vos?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Si, vide le pagina de <a %(a_llm)s>datos de LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Pote io discargar solmente un subset de files, como solmente un lingua particular o un thema specific?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Responsa curte: non facilemente."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Responsa longe:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Le majoritate del torrents contine le files directemente, lo que significa que tu pote instruer le clientes de torrent a discargar solmente le files requirite. Pro determinar qual files discargar, tu pote <a %(a_generate)s>generar</a> nostre metadata, o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Malheureusement, un numero de collectiones de torrent contine files .zip o .tar al radice, in tal caso tu debe discargar le integre torrent ante de poter seliger files individual."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Nos ha <a %(a_ideas)s>alcun ideas</a> pro le ultime caso tamen.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Nulle facile utensiles pro filtrar torrents es disponibile ancora, ma nos invita contributos."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Como gestiona vos le duplicatos in le torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Nos tenta mantener le duplication o superposition al minimo inter le torrents in iste lista, ma isto non pote sempre esser attingite, e depende multo del politicas del bibliothecas fonte. Pro bibliothecas que emitte lor proprie torrents, isto es foras de nostre controlo. Pro torrents publicate per Anna’s Archive, nos deduplica solmente basate super le hash MD5, lo que significa que differente versiones del mesme libro non es deduplicate."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Pote io obtener le lista de torrents como JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Si."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Io non vide PDFs o EPUBs in le torrents, solmente files binari? Que debe io facer?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Iste es in realitate PDFs e EPUBs, illos solmente non ha un extension in multe de nostre torrents. Il ha duo locos ubi tu pote trovar le metadata pro files de torrent, includente le typos/extensions de file:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Cata collection o emission ha su proprie metadata. Per exemplo, le <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> ha un base de datos de metadata correspondente hospitate in le sito web de Libgen.rs. Nos typicamente liga a recursos de metadata relevante ab cata pagina de <a %(a_datasets)s>dataset</a> del collection."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Nos recommenda <a %(a_generate)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Iste contine un mappatura pro cata registro in Anna’s Archive a su files de torrent correspondente (si disponibile), sub “torrent_paths” in le JSON de ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Quare mi cliente de torrent non pote aperir alcun de vostre files de torrent / ligamines magnet?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Alcun clientes de torrent non supporta grandia dimensiones de pecias, que multe de nostre torrents ha (pro le plus nove nos non face isto plus — mesmo si es valide secundo le specificationes!). Assi, essaya un cliente differente si tu incontra isto, o plange al creatores de tu cliente de torrent."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Ha vos un programma de divulgation responsabile?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Nos accogni con favor le investigadores de securitate pro cercar vulnerabilitates in nostre systemas. Nos es grande defensores de divulgation responsabile. Contacta nos <a %(a_contact)s>ci</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Nos es actualmente incapace de offerer recompensas pro errores, excepte pro vulnerabilitates que ha le <a %(a_link)s>potential de compromitter nostre anonymitate</a>, pro le quales nos offere recompensas in le intervallo de $10k-50k. Nos volerea offerer un plus large gamma de recompensas pro errores in le futuro! Per favor nota que attaccos de ingenieria social es foras de scopo."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Si tu es interessate in securitate offensive, e vole adjutar a archivar le cognoscentia e cultura del mundo, assecurate de contactar nos. Il ha multe manieras in le quales tu pote adjutar."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Ha il plus de recursos super Anna’s Archive?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualisationes regular"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Software de Anna</a> — nostre codice open source"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Traducer in le Software de Anna</a> — nostre systema de traduction"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — super le datos"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternative"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — plus super nos (per favor adjuta mantener iste pagina actualisate, o crea un pro tu proprie lingua!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Como reportar un violation de copyright?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Nos non hospita alcun material copyrightate hic. Nos es un motor de recerca, e como tal solmente indexa metadata que es ja publicamente disponibile. Quando tu discarga ab iste fontes externe, nos suggererea verificar le leges in tu jurisdiction respecto a lo que es permittite. Nos non es responsabile pro contento hospitate per alteres."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Si tu ha reclamos super lo que tu vide hic, tu melior option es contactar le sito web original. Nos regularimente incorpora lor cambios in nostre base de datos. Si tu vermente pensa que tu ha un reclamo DMCA valide al qual nos deberea responder, per favor completa le <a %(a_copyright)s>Formulario de Reclamo DMCA / Copyright</a>. Nos prende tu reclamos seriemente, e respondera te tan tosto como possibile."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Io odia como tu gestiona iste projecto!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Nos etiam vole rememorar a omnes que tote nostre codice e datos es completemente open source. Isto es unic pro projectos como le nostre — nos non es conscie de altere projecto con un catalogo similarmente massive que es etiam completemente open source. Nos multo benveni a quicunque pensa que nos gestiona mal nostre projecto a prender nostre codice e datos e establir lor proprie bibliotheca umbra! Nos non dice isto per spite o qualcosa simile — nos vermente pensa que isto esserea fantastic pois que illo elevarea le standard pro omnes, e preservarea meliormente le legato del humanitate."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Habe vos un monitor de uptime?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Per favor vide <a %(a_href)s>iste excellente projecto</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Como io pote donar libros o altere materiales physic?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Per favor, invia los al <a %(a_archive)s>Internet Archive</a>. Illes los preservara adequatemente."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Qui es Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Tu es Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Quales es tu libros favorite?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Ecce alcun libros que ha un significantia special pro le mundo de bibliothecas umbra e preservation digital:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Tu ha exhaurite tu descargas rapide hodie."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Deveni un membro pro usar descargas rapide."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Nos supporta ora cartas de dono de Amazon, cartas de credito e debito, crypto, Alipay, e WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Base de datos complete"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Libros, articulos, revistas, comicos, registros de bibliotheca, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Cercar"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub ha <a %(a_paused)s>suspendite</a> le carga de nove articulos."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB es un continuation de Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Accesso directe a %(count)s papers academic"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Aperte"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Si vos es un <a %(a_member)s>membro</a>, verification de navigator non es requirite."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Archivo a longe termino"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Le datasets usate in le Archivo de Anna es completemente aperte, e pote esser mirroreate in massa usante torrents. <a %(a_datasets)s>Sape plus…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Tu pote adjutar enormemente seminante torrents. <a %(a_torrents)s>Sape plus…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seminatores"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seminatores"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seminatores"

#, fuzzy
msgid "page.home.llm.header"
msgstr "Datos de entramento de LLM"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Nos ha le plus grande collection del mundo de datos textual de alte qualitate. <a %(a_llm)s>Sape plus…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Mirros: appello pro voluntarios"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Cercante voluntarios"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Como un projecto non-profit, open-source, nos sempre cerca personas pro adjutar."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Si tu opera un processator de pagamento anonyme de alte risco, per favor contacta nos. Nos tamben cerca personas pro placer annuncios parve e de bon gusto. Tote le proventos vade a nostre effortios de preservation."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog de Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Descargas IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Tote le ligamines de descarga pro iste file: <a %(a_main)s>Pagina principal del file</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(Tu poterea deber probar multiple vices con IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Pro obtener descargas plus rapide e evitar le verificationes del navigator, <a %(a_membership)s>deveni un membro</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Pro mirroring massive de nostre collection, verifica le paginas de <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Datos de LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Es ben comprendite que le LLMs prospera con datos de alte qualitate. Nos ha le plus grande collection de libros, articulos, revistas, etc. in le mundo, que es alcun del fontes de texto de plus alte qualitate."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Scala e gamma unic"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Nostre collection contine plus de cento milliones de files, includente jornales academic, libros de texto, e revistas. Nos attinge iste scala combinante grande repositorios existente."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Alcun de nostre collectiones de fonte es ja disponibile in massa (Sci-Hub, e partes de Libgen). Altere fontes nos liberava nos mesme. <a %(a_datasets)s>Datasets</a> monstra un vista complete."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Nostre collection include milliones de libros, articulos, e revistas de ante le era del e-libros. Grande partes de iste collection ha ja essite OCRate, e ha ja pauc superposition interne."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Como nos pote adjutar"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Nos es capabile de provider accesso a alte velocitate a nostre collectiones complete, assi como a collectiones non publicate."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Isto es accesso a nivello de enterprise que nos pote provider pro donationes in le gamma de dece milles USD. Nos es etiam disposte a cambiar isto pro collectiones de alte qualitate que nos non ha ancora."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Nos pote reembolsar vos si vos pote provider nos con enrichmento de nostre datos, como:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Remover superposition (deduplication)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Extraction de texto e metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Supporta le archivo a longe termino del cognoscentia human, durante que obtene melior datos pro vostre modello!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Contacta nos</a> pro discuter como nos pote collaborar."

#, fuzzy
msgid "page.login.continue"
msgstr "Continua"

#, fuzzy
msgid "page.login.please"
msgstr "Per favor <a %(a_account)s>aperi un session</a> pro vider iste pagina.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Le Archivo de Anna es temporarimente indisponibile pro manutention. Per favor retorna in un hora."

#, fuzzy
msgid "page.metadata.header"
msgstr "Meliorar metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Vos pote adjutar le preservation de libros per meliorar metadata! Primo, lege le information de fundo super metadata in le Archivo de Anna, e postea apprende como meliorar metadata per ligar con Open Library, e ganiar un abonnement gratuite in le Archivo de Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Fundo"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Quando vos reguarda un libro in le Archivo de Anna, vos pote vider varie campos: titulo, autor, editor, edition, anno, description, nomine de file, e plus. Tote iste pecias de information es appellate <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Como nos combina libros de varie <em>bibliothecas fonte</em>, nos monstra qualsecunque metadata es disponibile in ille bibliotheca fonte. Per exemplo, pro un libro que nos obteneva de Library Genesis, nos monstrara le titulo del base de datos de Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "A vices un libro es presente in <em>multiple</em> bibliothecas fonte, que poterea haber differente campos de metadata. In ille caso, nos simplemente monstra le version le plus longe de cata campo, pois que illo sperabilemente contine le information le plus utile! Nos ancora monstrara le altere campos sub le description, p.ex. como ”titulo alternative” (ma solmente si illos es differente)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Nos tamben extrahe <em>codices</em> como identificatores e classificatores del bibliotheca fonte. <em>Identificatores</em> representa unicmente un particular edition de un libro; exemplos es ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classificatores</em> gruppa insimul multiple libros simile; exemplos es Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. A vices iste codices es explicitemente ligate in bibliothecas fonte, e a vices nos pote extraher los del nomine de file o description (primarimente ISBN e DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Nos pote usar identificatores pro trovar registros in <em>collectiones de metadata solmente</em>, como OpenLibrary, ISBNdb, o WorldCat/OCLC. Il ha un specific <em>tab de metadata</em> in nostre motor de recerca si vos vole navigar iste collectiones. Nos usa registros correspondentes pro completar campos de metadata mancante (p.ex. si un titulo manca), o p.ex. como “titulo alternative” (si il ha un titulo existente)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Pro vider exactemente de ubi le metadata de un libro proveniva, vide le <em>“Detalios technic” tab</em> in un pagina de libro. Illo ha un ligamine al JSON brut pro ille libro, con indicatores al JSON brut del registros original."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Pro plus information, vide le sequente paginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Recerca (tab de metadata)</a>, <a %(a_codes)s>Explorator de Codices</a>, e <a %(a_example)s>Exemplo de metadata JSON</a>. Finalmente, tote nostre metadata pote esser <a %(a_generated)s>generate</a> o <a %(a_downloaded)s>discargate</a> como bases de datos ElasticSearch e MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Ligamine con Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Assi si vos incontra un file con mal metadata, como debera vos reparar lo? Vos pote ir al bibliotheca fonte e sequer su proceduras pro reparar metadata, ma que facer si un file es presente in multiple bibliothecas fonte?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Il ha un identificator que es tractate specialmente in le Archivo de Anna. <strong>Le campo annas_archive md5 in Open Library sempre prevale super tote le altere metadata!</strong> Vamos retroceder un poco e apprender super Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library esseva fundate in 2006 per Aaron Swartz con le objectivo de “un pagina web pro cata libro jammais publicate”. Illo es un sorta de Wikipedia pro metadata de libros: omnes pote modificar lo, illo es libere licentiate, e pote esser discargate in massa. Es un base de datos de libros que es le plus alignate con nostre mission — in facto, le Archivo de Anna ha essite inspirate per le vision e vita de Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "In vice de reinventa le rota, nos decideva rediriger nostre voluntarios verso Open Library. Si vos vide un libro que ha metadata incorrecte, vos pote adjutar in le sequente maniera:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Vade al <a %(a_openlib)s>sitio web de Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Trova le correcte registro de libro. <strong>ATTENTION:</strong> assecurar vos de seliger le correcte <strong>edition</strong>. In Open Library, vos ha “opere” e “editiones”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Un “opere” poterea esser “Harry Potter and the Philosopher's Stone”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Un “edition” poterea esser:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Le prime edition de 1997 publicate per Bloomsbery con 256 paginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Le edition de 2003 in formato de libro de papiro publicate per Raincoast Books con 223 paginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Le traduction polonese de 2000 “Harry Potter I Kamie Filozoficzn” per Media Rodzina con 328 paginas."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Tote iste editiones ha differente ISBNs e differente contentos, assi sia secur de seliger le correcte!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edita le registro (o crea lo si nulle existe), e adde tanto information utile como possibile! Tu es hic ora de omne modo, assi face le registro vermente fantastic."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Sub “Numeros de ID” selige “Anna’s Archive” e adde le MD5 del libro ab Anna’s Archive. Isto es le longe serie de litteras e numeros post “/md5/” in le URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Proba trovar altere files in Anna’s Archive que tamben corresponde a iste registro, e adde los tamben. In le futuro nos potera gruppar los como duplicatos in le pagina de recerca de Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Quando tu ha finite, scribe le URL que tu ha justo actualisate. Una vice que tu ha actualisate al minus 30 registros con MD5s de Anna’s Archive, invia nos un <a %(a_contact)s>email</a> e invia nos le lista. Nos te dara un abonnemento gratuite pro Anna’s Archive, assi tu potera facer iste labor plus facilemente (e como un gratia pro tu adjuta). Iste debe esser modificationes de alte qualitate que adde substantial quantitates de information, alteremente tu requesta essera rejectate. Tu requesta essera tamben rejectate si alcun del modificationes es revertite o corrigite per moderatores de Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Nota que isto functiona solmente pro libros, non pro documentos academic o altere typos de files. Pro altere typos de files nos ancora recommenda trovar le bibliotheca fonte. Il poterea prender alcun septimanas pro que le cambios sia includite in Anna’s Archive, pois que nos debe discargar le ultime dump de datos de Open Library, e regenerar nostre indice de recerca."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Mirrors: appello pro voluntarios"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Pro augmentar le resiliencia de le Archivo de Anna, nos cerca voluntarios pro operar mirrors."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Nos cerca isto:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Tu executa le codice open source de Anna’s Archive, e tu actualisa regularmente tanto le codice como le datos."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Tu version es clarmente distinguite como un speculo, p. ex. “Le Archivo de Bob, un speculo de Anna’s Archive”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Vos es preste a prender le riscos associate con iste labor, que es significative. Tu ha un comprension profunde del securitate operational requirite. Le contento de <a %(a_shadow)s>iste</a> <a %(a_pirate)s>postages</a> es auto-evidente pro te."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Tu es preste a contribuir a nostre <a %(a_codebase)s>base de codice</a> — in collaboration con nostre equipa — pro facer isto succeder."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Initialmente nos non te dara accesso a nostre downloads del servitor de partenarios, ma si le cosas va ben, nos pote divider isto con te."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Depensas de hospitage"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Nos es preste a coperir le depensas de hospitage e VPN, initialmente usque a $200 per mense. Isto es sufficiente pro un servitor de recerca basic e un proxy protegite per DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Nos solmente pagara pro le hospitage un vice que tu ha totos le cosas installate, e ha demonstrate que tu pote mantener le archivo actualisate con actualisationes. Isto significa que tu habera que pagar pro le prime 1-2 menses de tu proprie pecunia."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Tu tempore non essera compensate (e nec le nostre), pois que isto es labor purmente voluntari."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Si tu te involve significativemente in le disveloppamento e operationes de nostre labor, nos pote discuter de divider plus del entrata de donationes con te, pro que tu lo deploya como necessari."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Comenciar"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Per favor <strong>non nos contacta</strong> pro peter permission, o pro questiones basic. Actiones parla plus forte que parolas! Tote le information es ibi, assi solmente procede con establir tu speculo."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Senti te libere de postar tickets o requestas de fusion a nostre Gitlab quando tu incontra problemas. Nos poterea haber necessitate de construir alcun functiones specific al speculo con te, como rebranding de “Anna’s Archive” a tu nomine de sito web, (initialmente) disactivar contos de usatores, o ligar retro a nostre sito principal ab paginas de libros."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Una vice que tu ha tu speculo functionante, per favor contacta nos. Nos amarea revisar tu OpSec, e una vice que illo es solide, nos ligara a tu speculo, e comenciara a laborar plus proxime con te."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Gratias in avantia a qualcunque qui es preste a contribuir in iste maniera! Isto non es pro le timide, ma illo solidificarea le longevitá del plus grande vermente bibliotheca aperte in le historia human."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Discargar ab sito de partner"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Descargas lente es solmente disponibile via le sito official. Visita %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Descargas lente non es disponibile via VPNs de Cloudflare o alteremente ab adresses IP de Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Per favor attende <span %(span_countdown)s>%(wait_seconds)s</span> secundas pro discargar iste file."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Usa le sequente URL pro discargar: <a %(a_download)s>Discargar ora</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Gratias pro attender, isto mantene le sito accessibile gratis pro omnes! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Advertentia: il ha essite multe descargas ab tu adresse IP in le ultime 24 horas. Descargas poterea esser plus lente que usual."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Descargas ab tu adresse IP in le ultime 24 horas: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Si tu usa un VPN, connexion de internet partite, o tu ISP parte adresses IP, iste advertentia poterea esser causate per isto."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Pro dar a omne un opportunitate de discargar files gratuitemente, vos debe attender ante que vos pote discargar iste file."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Senti te libere de continuar navigar in le Archivo de Anna in un differente scheda durante que tu attende (si tu navigator supporta refrescar scheda in fundo)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Senti te libere de attender pro multiple paginas de download cargar al mesme tempore (ma per favor solmente discarga un file al mesme tempore per servitor)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Una vice que tu recipe un ligamine de download, illo es valide pro plure horas."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Le Archivo de Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Registro in le Archivo de Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Discargar"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Pro supportar le accessibilitate e preservation a longe termino del cognoscentia human, deveni un <a %(a_donate)s>membro</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Como un bonus, 🧬&nbsp;SciDB se carga plus rapidemente pro membros, sin alicun limites."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Non functiona? Proba <a %(a_refresh)s>refrescar</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Nulle previe ancora disponibile. Discarga le file ab <a %(a_path)s>le Archivo de Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB es un continuation de Sci-Hub, con su interface familiar e visualisation directe de PDFs. Entra tu DOI pro vider."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Nos ha le collection complete de Sci-Hub, assi como nove articulos. Le major parte pote esser visualisate directemente con un interface familiar, simile a Sci-Hub. Alcunes pote esser discargate via fontes externe, in tal caso nos monstra ligamines a illos."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Cercar"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nove recerca"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Includer solmente"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Excluder"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Non verificate"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Discargar"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Articulos de jornales"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Prestitos Digital"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadatos"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titulo, autor, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Cercar"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Configurationes de recerca"

#, fuzzy
msgid "page.search.submit"
msgstr "Cercar"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Le recerca prendeva troppo tempore, lo que es commun pro questiones general. Le contos de filtro pote non esser accurate."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Le recerca prendeva troppo tempore, lo que significa que tu pote vider resultatos inaccurate. A vices <a %(a_reload)s>recharger</a> le pagina adjuta."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Monstrar"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabella"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avantiate"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Cercar descriptiones e commentos de metadatos"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Adder campo specific de recerca"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(cercar campo specific)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Anno de publication"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Contento"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Tipo de file"

#, fuzzy
msgid "page.search.more"
msgstr "plus…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Acceso"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Fonte"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "scrappate e open-source per AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Lingua"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Ordinar per"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Le plus relevante"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Le plus nove"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(anno de publication)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Le plus vetule"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Le plus grande"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(dimension de file)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Le plus parve"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(codigo aperte)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Aleatori"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Le indice de recerca es actualisate mensual. Illo actualmente include entratas usque a %(last_data_refresh_date)s. Pro plus information technic, vide le pagina de %(link_open_tag)sdatasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Pro explorar le indice de recerca per codices, usa le <a %(a_href)s>Explorator de Codices</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Scribe in le quadro pro cercar in nostre catalogo de %(count)s files directemente discargabile, que nos <a %(a_preserve)s>preserva pro semper</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "In facto, qualcunque persona pote adjutar a preservar iste files per semination de nostre <a %(a_torrents)s>lista unificate de torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Nos actualmente ha le catalogo aperte le plus comprensive del mundo de libros, articulos, e altere obras scribite. Nos reflecte Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e plus</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Si tu trova altere “bibliothecas umbra” que nos deberea reflecter, o si tu ha qualcunque questiones, per favor contacta nos a %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Pro reclamos de DMCA / copyright <a %(a_copyright)s>clicca hic</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Consilio: usa le shortcuts de claviero “/” (focus de recerca), “enter” (recerca), “j” (su), “k” (giu), “<” (pagina previe), “>” (pagina sequente) pro navigation plus rapide."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Cercar articulos academic?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Scribe in le quadro pro cercar in nostre catalogo de %(count)s articulos academic e de jornales, que nos <a %(a_preserve)s>preserva pro semper</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Scribe in le quadro pro cercar files in bibliothecas de prestito digital."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Iste indice de recerca actualmente include metadata del bibliotheca de Prestito Digital Controlate del Internet Archive. <a %(a_datasets)s>Plus information super nostre datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Pro plus bibliothecas de prestito digital, vide <a %(a_wikipedia)s>Wikipedia</a> e le <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Scribe in le quadro pro cercar metadata de bibliothecas. Isto pote esser utile quando <a %(a_request)s>requestar un file</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Iste indice de recerca actualmente include metadata de varie fontes de metadata. <a %(a_datasets)s>Plus information super nostre datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Pro metadata, nos monstra le registros original. Nos non face ulle fusion de registros."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Il ha multe, multe fontes de metadata pro obras scribite in le mundo. <a %(a_wikipedia)s>Iste pagina de Wikipedia</a> es un bon initio, ma si tu cognosce altere bon listas, per favor informa nos."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Scribe in le quadro pro cercar."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Iste es registros de metadata, <span %(classname)s>non</span> files descargabile."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Error durante le recerca."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Proba <a %(a_reload)s>recharger le pagina</a>. Si le problema persiste, per favor invia nos un email a %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Nulle files trovate.</span> Proba con minus o differente terminos de recerca e filtros."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ A vices isto occurre incorrectemente quando le servitor de recerca es lente. In tal casos, <a %(a_attrs)s>recarregar</a> pote adjutar."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Nos ha trovate correspondencias in: %(in)s. Tu pote referer al URL trovate ibi quando <a %(a_request)s>requesta un file</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Articulos de Jurnales (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Preste Digital (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadatos (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultatos %(from)s-%(to)s (%(total)s total)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ correspondencias partial"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d correspondencias partial"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Voluntariato & Premios"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Le Archivo de Anna depende de voluntarios como te. Nos accogna omne nivellos de compromisso, e ha duo categorias principal de adjuta que nos cerca:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Labor voluntari lige:</span> si tu pote solmente sparniar alcun horas hic e ibi, ancora il ha multe manieras in que tu pote adjutar. Nos recompensa voluntarios consistente con <span %(bold)s>🤝 membrosias al Archivo de Anna</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Labor voluntari pesante (bounties de USD$50-USD$5,000):</span> si vos pote dedicar multo de tempore e/o recursos a nostre mission, nos amarea colaborar plus de vicino con vos. Eventualmente vos pote junger al equipa interne. Ben que nos ha un budgeto strict, nos pote recompensar con <span %(bold)s>💰 bounties monetari</span> pro le labor plus intense."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Si vos non pote voluntariar vostre tempore, vos pote ancora adjutar nos multo per <a %(a_donate)s>donar moneta</a>, <a %(a_torrents)s>seminar nostre torrents</a>, <a %(a_uploading)s>incarcar libros</a>, o <a %(a_help)s>dicer a vostre amicos super le Archivo de Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Companias:</span> nos offere accesso directe a alte velocitate a nostre collectiones in cambio de donation a nivello de enterprise o in cambio de nove collectiones (p.ex. nove scansiones, datasets OCR, arricchir nostre datos). <a %(a_contact)s>Contacta nos</a> si isto es vos. Vide etiam nostre <a %(a_llm)s>pagina de LLM</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Voluntariato legier"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Si vos ha alcun horas libere, vos pote adjutar in plure manieras. Assecura te de junger le <a %(a_telegram)s>chat de voluntarios in Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Como signo de appreciation, nos normalmente offere 6 menses de “Bibliothecario Fortunose” pro milestonos basic, e plus pro labor continuate de voluntariato. Tote milestonos require labor de alte qualitate — labor negligente nos face plus de damno que adjuta e nos lo rejectara. Per favor <a %(a_contact)s>invia nos un email</a> quando vos attinge un milestono."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tarea"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Milestono"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Diffunder le parola de le Archivo de Anna. Per exemplo, per recommendar libros in AA, ligar a nostre articulos de blog, o generalmente diriger personas a nostre sito web."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s ligamines o capturas de schermo."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Iste deberea monstrar te informante qualcun super le Archivo de Anna, e illes gratias te."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Meliorar metadata per <a %(a_metadata)s>linkar</a> con Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Tu pote usar le <a %(a_list)s >lista de problemas de metadata aleatori</a> como puncto de initio."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Assecura te de lassar un commento super le problemas que tu repara, assi alteres non duplicara tu labor."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s ligamines de registros que vos meliorava."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Traducer</a> le sito web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Traducer completemente un lingua (si illo non esseva quasi completate ja)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Meliorar le pagina de Wikipedia pro le Archivo de Anna in vostre lingua. Include information de pagina de Wikipedia de AA in altere linguas, e de nostre sito web e blog. Adde referentias a AA in altere paginas relevante."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Ligamine al historia de modificationes monstrante que vos ha facite contributiones significative."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Satisfacer requestas de libros (o papiros, etc.) in le foros de Z-Library o Library Genesis. Nos non ha nostre proprie systema de requesta de libros, ma nos reflecte iste bibliothecas, assi facer los melior face le Archivo de Anna melior tamben."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s ligamines o capturas de schermo de requestas que vos compliva."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Parve tareas publicate in nostre <a %(a_telegram)s>chat de voluntarios in Telegram</a>. Usualmente pro membrosia, a vices pro parve bounties."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Parve cargas publicate in nostre gruppo de chat de voluntarios."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Depende del tarea."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Recompensas"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Nos semper cerca personas con solidas habilitates de programmation o securitate offensive pro implicar se. Tu pote facer un contribution significative a preservar le legato del humanitate."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Como gratia, nos offere membrosia pro contributiones solide. Como un grande gratia, nos offere recompensas monetari pro tareas particularmente importante e difficile. Isto non debe esser considerate como un substitution pro un empleo, ma es un incentivo extra e pote adjutar con costos incurrite."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Le major parte de nostre codice es open source, e nos va peter lo mesme de tu codice quando nos accorda le recompensa. Il ha alcun exceptiones que nos pote discuter individualmente."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Recompensas es accordate al prime persona que completa un tarea. Senti te libere de comentar super un ticket de recompensa pro informar alteres que tu labora super alicun cosa, assi alteres pote attender o contactar te pro collaborar. Ma sia conscie que alteres es ancora libere de laborar super illo e tentar batter te. Tamen, nos non accorda recompensas pro labor mal facite. Si duo submissiones de alte qualitate es facite proxime la un al altere (intra un die o duo), nos pote decider accordar recompensas a ambes, a nostre discretion, per exemplo 100%% pro le prime submission e 50%% pro le secunde submission (assimulante 150%% in total)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Pro le recompensas plus grande (specialmente recompensas de scraping), per favor contacta nos quando tu ha completate ~5%% de illo, e tu es confidente que tu methodo va escalar al objectivo complete. Tu habera que divider tu methodo con nos assi nos pote dar retroaction. Assi, nos pote decider que facer si il ha multiple personas que se approxima a un recompensa, como potentialmente accordar lo a multiple personas, incoragiar personas a collaborar, etc."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "ATTENTION: le tareas con alte recompensas es <span %(bold)s>difficile</span> — il poterea esser sapiente comenciar con los plus facile."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Vade a nostre <a %(a_gitlab)s>lista de problemas in Gitlab</a> e ordina per “Prioritate de etichetta”. Isto monstra approximativemente le ordine de tareas que nos importa. Tareas sin recompensas explicite es ancora eligibile pro membrosia, specialmente los marcate “Acceptate” e “Favorita de Anna”. Tu poterea voler comenciar con un “Projecto de initio”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Actualisationes super <a %(wikipedia_annas_archive)s>Archivo de Anna</a>, le plus grande bibliotheca vermente aperte in le historia human."

#, fuzzy
msgid "layout.index.title"
msgstr "Le Archivo de Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Le plus grande bibliotheca de datos aperte e de fonte aperte del mundo. Reflecte Sci-Hub, Library Genesis, Z-Library, e plus."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Cerca in le Archivo de Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Le Archivo de Anna necessita tu adjuta!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Multes prova de nos abatter, ma nos lucta de retorno."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Si tu dona ora, tu recipe <strong>duple</strong> le numero de descargas rapide."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Valide usque al fin de iste mense."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Dona"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Salveguardar le cognoscentia human: un grande dono de ferias!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Surprende un car persona, da les un conto con adhesion."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Pro augmentar le resilientia del Archivo de Anna, nos cerca voluntarios pro operar reflectores."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Le regalo perfecte pro San Valentino!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Nos ha un nove methodo de donation disponibile: %(method_name)s. Per favor considera %(donate_link_open_tag)sdonar</a> — mantener iste sito web non es cheap, e tu donation vermente face un differentia. Multo gratias."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Nos es currentemente facente un collecte de fundos pro <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">sustener</a> le plus grande bibliotheca de comics in le mundo. Gratias pro tu supporto! <a href=\"/donate\">Dona.</a> Si tu non pote donar, considera supportar nos per parlar a tu amicos, e sequer nos in <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, o <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Descargas recente:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Cercar"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Meliorar metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Voluntariato & Recompensas"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrentes"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Activitate"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Explorator de Codices"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Datos de LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Domo"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Le Software de Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Traducer ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Aperir session / Registrar"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Conto"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Le Archivo de Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Resta in contacto"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / reclamos de copyright"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avansate"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Securitate"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativas"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "non-affiliate"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Iste file pote haber problemas."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Discarga rapide"

#, fuzzy
msgid "page.donate.copy"
msgstr "copia"

#, fuzzy
msgid "page.donate.copied"
msgstr "copiate!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Previe"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Sequente"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "solmente iste mense!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub ha <a %(a_closed)s>suspendite</a> le carga de nove articulos."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selige un optione de pagamento. Nos offere discontos pro pagamentos basate in crypto %(bitcoin_icon)s, proque nos incurre (multo) minus taxas."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Selige un optione de pagamento. Nos actualmente ha solmente pagamentos basate in crypto %(bitcoin_icon)s, pois que le processores de pagamento traditional refusa collaborar con nos."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Nos non pote supportar cartas de credito/debito directemente, proque le bancas non vole collaborar con nos. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tamen, il ha plure manieras de usar cartas de credito/debito de qualcunque maniera, usante nostre altere methodos de pagamento:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Discargas lente & externe"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Discargas"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Si tu usa crypto pro le prime vice, nos suggere usar %(option1)s, %(option2)s, o %(option3)s pro comprar e donar Bitcoin (le cryptocurrency original e le plus usate)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 ligamines de registros que vos ha meliorate."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 ligamines o capturas de schermo."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 ligamines o capturas de schermo de requestas que vos ha satisfacite."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Si tu es interessate in mirroring iste datasets pro <a %(a_faq)s>archivage</a> o pro <a %(a_llm)s>scopos de training de LLM</a>, per favor contacta nos."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Si está interesado en replicar este conjunto de datos para <a %(a_archival)s>archivos</a> o para <a %(a_llm)s>entrenamiento de LLM</a>, por favor contáctenos."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Sito web principal"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Information de paises de ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Si vos es interessate in specular iste dataset pro fin de <a %(a_archival)s>archivage</a> o <a %(a_llm)s>training de LLM</a>, per favor contacta nos."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Le Agencia International de ISBN regularmente publica le intervallos que illo ha allocate a agencias national de ISBN. Ab isto nos pote derivar a qual pais, region, o gruppo linguistic pertine iste ISBN. Nos actualmente usa iste datos indirectemente, per medio del bibliotheca Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resources"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Ultime actualisation: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Sito web de ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadatos"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Excludente “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Nostre inspiration pro colliger metadata es le objectivo de Aaron Swartz de “un pagina web pro cata libro jammais publicate”, pro le qual ille creava <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Iste projecto ha facite ben, ma nostre position unic nos permitte obtener metadata que illes non pote."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Un altere inspiration esseva nostre desiro de saper <a %(a_blog)s>quante libros existe in le mundo</a>, assi nos pote calcular quante libros nos ancora debe salvar."

#~ msgid "page.partner_download.text1"
#~ msgstr "Pro dar a omnes un opportunitate de discargar files gratis, tu debe attender <strong>%(wait_seconds)s secundas</strong> ante que tu pote discargar iste file."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Refresca automaticamente le pagina. Si tu manca le fenestra de download, le temporisator recommenciara, assi refrescar automaticamente es recommendate."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Discargar ora"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Converter: usa instrumentos online pro converter inter formatos. Per exemplo, pro converter inter epub e pdf, usa <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: discarga le file (pdf o epub es supportate), alora <a %(a_kindle)s>invia lo a Kindle</a> usante web, app, o email. Instrumentos utile: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Supporta autores: Si vos ama isto e pote permitter lo, considera comprar le original, o supportar le autores directemente."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Supporta bibliothecas: Si isto es disponibile in vostre bibliotheca local, considera prender lo in prestito ibi gratis."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Non disponibile directemente in massa, solmente in semi-massa detra un muro de pagamento"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Le Archivo de Anna gestiona un collection de <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb es un compania que scruta varie librerias online pro trovar metadatos de ISBN. Le Archivo de Anna ha facite copias de securitate del metadatos de libros de ISBNdb. Iste metadatos es disponibile per le Archivo de Anna (ben que actualmente non in le recerca, excepte si vos explicitemente cerca un numero de ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Pro detalios technic, vide infra. A un certe puncto nos pote usar lo pro determinar qual libros ancora manca in bibliothecas de umbra, pro prioritisar qual libros trovar e/o scannar."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Nostre articulo de blog super iste datos"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Scrutation de ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Actualmente nos ha un sol torrent, que contine un file de 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> (20GB non-comprimite): “isbndb_2022_09.jsonl.gz”. Pro importar un file “.jsonl” in PostgreSQL, vos pote usar qualcosa como <a %(a_script)s>iste script</a>. Vos pote mesmo pipear lo directemente usante qualcosa como %(example_code)s assi illo se decomprime in tempo real."

#~ msgid "page.donate.wait"
#~ msgstr "Per favor attende al minus <span %(span_hours)s>duo horas</span> (e refresca iste pagina) ante contactar nos."

#~ msgid "page.codes.search_archive"
#~ msgstr "Cercar in le Archivo de Anna pro “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Dona usante Alipay o WeChat. Tu pote seliger inter iste optiones in le proxime pagina."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Diffunder le parola del Archivo de Anna in le medios social e foros online, per recomendar libros o listas in AA, o responder questiones."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Collection de fiction ha divergete ma ancora ha <a %(libgenli)s>torrentes</a>, ben que non actualisate desde 2022 (nos ha downloades directe)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Le Archivo de Anna e Libgen.li gestiona collaborative collections de <a %(comics)s>libros comic</a> e <a %(magazines)s>revistas</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Nulle torrentes pro fiction russe e collections de documentos standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Non existe torrentos disponibile pro le contento addicional. Le torrentos que es in le sito web Libgen.li es speculos de altere torrentos listate hic. Le un exception es le torrentos de fiction comenciante a %(fiction_starting_point)s. Le torrentos de comics e revistas es publicate como un collaboration inter le Archivo de Anna e Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "De un collection <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> origine exacte incognite. Partialmente de the-eye.eu, partialmente de altere fontes."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

