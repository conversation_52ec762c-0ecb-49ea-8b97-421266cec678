��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b u  sd N  �f -   8h !  fh �  �i �  }k j  Bm ,  �n X   �o q   3p G   �p h   �p �  Vq L  s �   _t �  Ju �   Dw �  (x   z   | �   ~ �  �~ �  ׀ ?   �� �   Ă U   �� �  �    � 3  � G   5�     }�    ��    �� =   ǈ $   �    *� )   ;�    e� ,   ��    �� D   ǉ <  �   I� P   X� 5   �� 
   ߌ    �    ��    �    �    <� 	   I�    S�    g� #   n�    ��    �� 	   �� 
   ��    ̍    ؍    �    � w  -� c  �� �   	� -   �� M  �� �   �   �� �   ��    �� �   ��    B� �   _� !   �   -�    D�   a� :   � f   �    �� [  �� L  � �  2� 6  �    M� D   `� O   �� �   �� .   �� '   Ӣ h   �� 9   d� :   �� ,   ٣    �    
� �  �� :   Z� w   �� �   
� ?   ѧ (  � ,   :�   g� �   s� �   � S   �� �   �� �   Ĭ G   X� �   �� �   �� \   [� >   �� �   �� 
   {� �   ��   +� G   @� 
   �� �   �� �   A�    %� �  -� �   ��   b� �  e� �   � Y  ҹ   ,� U   B� K   �� ]   � �   B� h   ǿ S   0� 6   �� �   �� X   W� G   ��    �� j   � U   y� �   �� �   ��    (�   @�   D� H  F� Y  �� �   �� �   z� =  t� �   �� �   i� �   � t  �� �   =�   ��    �� �   � �   �� �   �� x  i� �   �� �   i� �   �� �  �� }   �� 9  �   @� $   V� R   {� �   �� G   `� o  �� �   � q   �� ~   E� (   �� *  �� �   �   ��    q� i   �� �   �� �   �   N� �   R� �  A� �  � ,   �� }  �� �  u� 5  � �  Q� 2  �   D� g   Q� �   ��    C� �   L� 4  � �  F�   ��   �� �   �� 
   �� �  �� t  &  �   � 
   � �   � =  b �   �   r �   u O   e    � �  � y  e
    � �   � �   �   �
 �   � g  � \   : D   �   � 4   �     @  ) �   j !  h /   � G   �    }    z   � o    {  �     �     �  : �   � �   � P   &  �   w  /   ! �   2! �   *"    *#    @# +   L# 6   x# 6   �# 6   �# X   $ |   v$    �$ (   	% A   2% O   t% &   �% 7   �% .   #& p   R&    �&    �& )  �& 2  ( �   >) �   &* �   + �   �+ :  Y,    �- �   �- �   �. �   5/ S  �/ i  $1 �  �2 �  z4 �  !6 G   �7 E   �7 +   C8 �   o8 �  9 �  ; 0  �< T  	>   ^? 
  p@   {B 1   �C )  �C <  �D v  1F    �G �   �G 4  �H �   �I U  pJ �   �K    �L +   �L n   �L 
   ]M �  hM �  �O d  �Q �   �S �   �T    U �   �U �   sV G   eW q   �W   X   =Y �   MZ �  �Z l   j\ )  �\    ^   ^ �   %` �  �` y  �b �  ,e �   g 
   �g �   �g �  �h    �j `  �j   l   n   'p   .r @  Fs G   �t �  �t �   �v ~   |w @   �w S   <x    �x ?   �x %   �x -   y &   1y    Xy �   qy �  z �  �| +  �~ �   �    � �  �� ;  � i  0�   �� ]  � b  x� 
   ێ �   � 	   ޏ    � �  �� 2  ��   ד T  ��    I� �  Y� �  =� �  � 	  
� �  � $  
� e  /� %   �� �   �� z   �� �  �� �  ͪ �   z� �   � �   �� l  C�    �� �   ʯ K   n� E   ��     � 9   � <   T� B  �� �  Գ �  {� 5   � �  E� �   �    �� �   �� ;   X� x   �� �   
� (   ˽ Y   ��    N� W  e� 4  �� �  �� l  �� y   ��    y� _   �� 
  ��    � �   � �  �� �   I� 1   �� 6   � [  S� -   �� u  �� �  S�   �� !   ��    � �   �� >  .� �  m�    � �   )� -   � �   D� �   ��   �� �   �� I   �� -   �� �   � 5   � �  K� N  �� �   A� �   � _   �� B   ]� �   �� $  O� �  t� �  � �  �� 3   �� �   "� @  � S  Q� �   �� �  o� �  k� -   "� �  P� ,   >� �  k� S   8�    ��   ��   �� �  �� �   A� 5  8  �  n 3  f �   � �  L �  F	 �     3  � i  � 9   2 �   l �   �    }    � F  � �   � 7   �   � �  � 3  �   � �  � �   � �  : .      4 �   P      {  !  "   �!    �!    �!    �! ,   �!    "    0"    A"    W"    ]" 
   o"    z" 
   �"    �"    �"    �" #   �"    �" 
   �"    �"    �" �    # X   �# #   '$    K$ ,   Z$ +   �$ 4   �$ 2   �$ "   % 
   >%    I%    X%    h%    |%    �%    �%    �%    �%    �% 1   �%    &    +& !   E& %   g& +   �& (   �&    �& $   �& 8   '     T' O   u' C   �'    	( _   ( E   p(    �(    �( !   �(    )    )    4)    S)    j)    })    �)    �)    �) 
   �) 	   �) 
   �)    �)    �)    �)    �) 	   *    * 	   )*    3*    I*    O* 	   V*    `*    p*    |*    �*    �*    �*    �*    �* 	   �*    + "   +    :+    A+ $   G+    l+    t+    �+    �+    �+    �+    �+ m   �+ �   A, S   �, �   ,- �  �- r   �/    0    /0    L0    ^0    e0    r0 
   �0 '   �0 O   �0 5   1 V   F1 "   �1 =   �1 5   �1 x   42 _   �2 �   
3 ]   �3 7   F4 %   ~4    �4    �4 
   �4    �4    �4    �4    �4    �4    5    5    5    .5    25    75    I5    V5 
   q5    5    �5 
   �5 	   �5    �5    �5    �5   �5     7    7    7     7    27 J   97 _   �7 '   �7 %   8 8   28    k8    s8    {8 ~   ~8    �8    9 '   9 }   >9    �9    �9 q   �9 "   ]: .  �: V   �= q   > �   x> �   ? ;   �?    @ �   A R  �A �   �B    �C    D :   D B   LD O   �D \   �D X   <E E   �E [   �E     7F +   XF    �F    �F I   �F %   �F    G    G 	   "G    ,G d   @G    �G '   �G C   �G    H    3H M   KH I   �H b  �H    FJ    aJ �   |J    @K 	   LK    VK    ^K    yK /   �K E  �K    �L    M    !M 	   7M J  AM *   �N    �N    �N v   �N    >O 	   FO 3   PO    �O    �O &   �O �   �O    P    �P J   �P     �P    Q 	   Q    %Q '   4Q V   \Q    �Q /   �Q �   �Q _   �R U   �R    PS   bS I   hT    �T 2   �T    �T �   U �   �U    �V I   �V �   �V �   �W     GX     hX    �X �  �X 1   CZ '   uZ    �Z +   �Z    �Z �   [    �[    �[ 4   �[ @   \ 	   U\    _\ "   y\ %   �\ C  �\ t  _   {` :   �b 3   �b    �b    
c m  )c �   �d �   �e    >f �   ^f �  Sg    �h    i �  8i 7  /k 	   gl    ql 6   �l    �l .  �l    �m T  n �  do �   q    �q ~   r @   �r )   �r n   �r >  ms   �t �   �u �  Uv t   �w �   gx _   gy �   �y �   �z G   @{ �   �{ /   5| *   e|    �|    �|    �| +   �|     �| B   
} 5   M} 	   �} 6   �}    �} 8   �~ �       � �   �� )   $�     N�    o�    �� .   �� $   ԁ -   �� $   '� �   L� (   9� �   b�    (� �   A� �   �� �   �� 3  � �   ;� ,   � �   ?� 	   ˈ   Ո o   �    b� �  �    3�    @�    V�    k� +   �� 	   ��    �� L   ǌ �   � �   ƍ 
   ��    ��    �� �   ݎ ;  �� �   ߐ t   ��    �    �    0�    D�    ^�    t�    �� F   �� -   ג �  � V   �    @� p   S� }   ĕ R   B� t   �� a   
� X   l�    ŗ j   ˗ O   6� �   �� `   � R   {�    Ι �   � d   њ F   6� ]   }� h   ۛ B   D� 	   �� =   �� n   Ϝ �   >� 4   ۝ �   �    �� �   �� I   �� b   �� �   a�    � �  
� �   �    ��    ̣    �    � �   �� 9   � b   &� �   �� �   k� �   E� �   ϧ �   �� �   B� �   � ]   ��    �� c   � ]  �� �   � �   �� 
   �� 
   �� 
   �� �   �� 
   `� 
   n� H   |� Z   Ű �    � 
   � i   � >   y� �   �� A   A� {   �� a   �� 
   a� �   o� 
   �� 
   � 
   � >  #� �   b� �  �    ��    �� 
   ù �   ѹ    |� (   ��   �� �   ͻ    ��    ��    �� <   ϼ ?   � 3   L� #   �� #   �� �   Ƚ     �� �  Ѿ �   �� �   |� T   � �   n�   "� �  .� C  � �   R� �   � ~   ��    � [  )� #   �� ]  �� �   �   �� �   �� M  �� �   �� �   �� o   /� &   �� ;   ��    � 5   �    P�    \�    k� �   ��    b� l   k� 3   ��    �    � 	   � $   $� q   I� I   �� 7   � 
  =� '   K�    s�    |�    �� #   ��    �� 	   ��    �� 	   ��    �� 	   ��    � 	   � .   � �   H�    �� 
   ��    � 
   �    � 
   +�    9� 
   F�    T�    m� )   z� a   ��    � 5   � W   O� �   �� �   Z� �   !�   �� �   �� (  ��    �� �   �� 3   u� Q   �� A   �� �   =� �   �� J   �� i   �    �� n   �� e   � �   k�    ��     ��    �    /� 
   H�    V�    q� !   y�    ��    ��    �� !   ��    ��    �    '�    D�    K�    \�    i�     q�    �� "   �� '   �� v   �� �   [� (   �� !   � _   2� V   �� p   �� *   Z� ,   �� +   �� :   �� ;   � *   U� �   ��   >�   S�    q� >   �� z   �� '   B� �   j� �   Y� o   $� 7   ��    �� �   �� �   q� r   � 0   ~� ]   �� �   
�    �� -   ��    � :   7� �   r� y   ��    p�    x�    �� )   �� �   �� 4   B� $   w� 5   �� #   ��    �� #   � J   2�    }� `   �� ?   ��   7� J   ;� T   �� �   �� >   �� !   ��    �� !   �     -� !   N�     p� !   �� 9   �� 3   �� B  !� :   d� 7   �� C   �� (   �    D� o   J� {   �� �   6  �   �     � �   � .   2    a �   v    K    a A    <   � R   � h   Q G   �           �   A /   � %    K   1 �   } ;   i �   � p   ( Z   �    � +   t	 3   �	 u   �	    J
 q   f
    �
 �   �
 1   � ?   �    " %   7 �   ] >   8
 L   w
 �   �
 T   R D   � �   � z   p    �    � I    =   V    � %   �    �    � 	   � -     �   . g   �    # "   7 *   Z *   � (   � {   � 5   U x   � [    *   ` s   � �   � -   � K        l E   � 8   �     v    w   � +    I   8    � .   � L   �     r   - .   �    � /   � X    �   u @   Y    �    � @   �     T    =   r �   � v   Q    � E   � �       � �   � A   � %   � J   � �   @     *!    1!    3!    5! "   I! 2   l! <   �!    �!    �!    " "   " =   3" >   q" 
   �" G   �" >   #    B#    R# .   q#    �#    �# U   �# �    $ C   �$    A%    M% �   Z% =  & \   C'    �' Q  �' �  ) (   �* l   �*    2+ S  P+ '   �- d   �-    1.    G. �  ].    +0 U   B0 �   �0 m   1 R   �1    �1 b   �1 5   V2    �2 y   �2 D   %3 1   j3 X   �3 .   �3 W   $4 �   |4 �   5 2   �5 �   �5 ~  �6 �   $8 0   �8 N  9 �   a: �   ];    %< �   F= )   >    9> �   T> 4   ?    @? ]   AA A   �A    �A     �A �  B    �C �   �C B  �D ,  �E +  G A   >H L   �H f   �H /   4I /   dI R   �I `   �I    HJ    UJ :   rJ    �J    �J +   �J `   K *   tK    �K \   �K �   L �   �L    �M    �M    �M M   �M `   N [   sN �   �N l   �O    P $   ,P �   QP    (Q �   5Q �  �Q �   eT T   cU .   �U    �U    �U    �U E   �U +   >V �   jV    �V R   �W    @X    WX )   nX    �X P   �X    Y <   Y    OY #   \Y /   �Y    �Y    �Y Q   �Y    Z    Z %   5Z    [Z    zZ }   ~Z �   �Z W   �[ o    \ H   �\ �   �\    �]    �] �   �] �   �^ �   B_    �_ o   ` F   w` J   �` T   	a h   ^a ^   �a    &b a   Db    �b    �b    �b    �b    �b    
c     c     3c 
   Tc    bc 3   nc 3   �c 3   �c $   
d 5   /d 1   ed "   �d    �d    �d @   �d    'e 
   Ee 5   Pe 2   �e Y   �e -   f    Af    [f     sf    �f    �f o   �f Z   1g x   �g �   h    �h    i    #i    Ai 1   Wi 	   �i    �i    �i y   �i     5j $   Vj    {j 
   �j 	   �j :   �j    �j 
  �j    �k    
l    !l #   3l $   Wl    |l %   �l '   �l +   �l .   m 6   Am    xm b   m )   �m    n    (n 8   9n @   rn $   �n -   �n    o g   &o f   �o J   �o    @p    Hp 	   _p    ip    ~p    �p �  �p q   >r    �r    �r /   �r    �r 0   s    Cs 	   Hs t   Rs =   �s �   t    �t &   �t �   u &   �u %   �u $   �u '   v 5   3v +   iv    �v    �v ?   �v    �v ,   w 6   Gw >  ~w 5   �x C   �x [   7y     �y �   �y $   Cz    hz K   |z    �z -   �z    { B   %{ 5   h{ )   �{ �   �{    s|    �| "   �|    �|    �|    �|    �|    }    (} T   @}   �}     �~ !   �~ �   �~ �   �    a�    ~�    ��    ��    ��    Հ    �    �    �    )�    <�    K� 
   \�    j�    y�    ��    ��    �� -   �� �   �   �� �  Ń   Q� �  c� �   � �  �    u�   {�    �� �   �� �   r� �  `� �   � s  Ő 8   9� �   r� L   )�    v� B   �� _   ٓ h   9� f   �� �   	� �   �� �   Y� c  #�    �� �   �� �   d� ^   ^� �   ��    n� �   �� j  P� �   �� �   �� 	   M� ]   W� t   �� �   *� �   � p   �� �   �    ��     ��    ֣ D   � 8   4� 
   m� �   {� <   � z   A�    �� �   ٥ �   y� O   � M   f� ^   �� i   � V   }� L   Ԩ z   !� d   ��    � }   ��    �� #   � /   0� d   `� 2   ū    ��    �� G   �    N�    b�    w� ;   � ,   �� >   �    '�    F� 	   S�    ]� 	   c� Z   m� }   ȭ Y   F� 4   ��    ծ    ݮ -   ��    +� 	   ?�    I�    P�    Y�    b�    h�    p�    }�    ��    ��    ��    ��    ��    ү    �    ��    � 
   �    �    $�    C� !   ^� |   ��    �� ^   � �   r�    4�    =�    O�    ^�    f�    o�    s� �   z� ~   1� C   ��    ��    	� q   #�    �� �   �� �   2� #   ��    � �   �� �   �� W   R� �   �� o   m� ,   ݸ �   
�     �� !   �� I   ڹ �   $�    �� �   ׺ }   k� �   � Q   ��    �    �    #� 	   5�    ?� 	   U�    _�    l� �   �� y   � �   �� �   � �   � Z   �� K   � �  f� s  � �   �� �   ��   � U  *�    �� �   �� (  I� �   r� �  (� �  �� �   w� r  �    t� ;   �� �   �� n  V� H   �� �   � 	   �    �    � �   '� ?   �� h   �� 3   ^� a   �� D   �� Y   9� *   �� �   �� J   i� ,   �� a   �� �   C�     �  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ia
Language-Team: ia <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library es un bibliotheca popular (e illegal). Illes ha prendite le collection de Library Genesis e lo ha rendite facilemente cercabile. In ultra, illes ha devenite multo effective a sollicitar nove contributiones de libros, incitante usatores contributores con varie privilegios. Illes actualmente non contribue iste nove libros retro a Library Genesis. E a differente de Library Genesis, illes non rende lor collection facilemente speculabile, lo que impedi un large preservation. Isto es importante pro lor modello de negocio, pois que illes demanda moneta pro acceder a lor collection in massa (plus que 10 libros per die). Nos non face judicios moral super demandar moneta pro accesso in massa a un collection illegal de libros. Es sin dubita que le Z-Library ha habite successo in expander accesso al cognoscentia, e in obtener plus libros. Nos es simplemente hic pro facer nostre parte: garantir le preservation a longe termino de iste collection private. - Anna e le equipa (<a %(reddit)s>Reddit</a>) In le liberation original del Speculo del Bibliotheca Pirata (EDIT: movite a <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>), nos faceva un speculo de Z-Library, un grande collection illegal de libros. Como un rememoration, isto es lo que nos scribeva in ille post original de blog: Iste collection data de medio-2021. Intertanto, le Z-Library ha crescite a un ritmo stupefaciente: illes ha addite circa 3.8 milliones de nove libros. Il ha alcun duplicatos ibi, certe, ma le majoritate de illo pare esser libros vermente nove, o scansiones de melior qualitate de libros submitite anteriormente. Isto es in grande parte debite al numero augmentate de moderatores voluntari al Z-Library, e lor systema de carga in massa con deduplication. Nos vole felicitar les pro iste realisationes. Nos es felice de annunciar que nos ha obtenite tote le libros que esseva addite al Z-Library inter nostre ultime speculo e Augusto 2022. Nos ha etiam retrocedite e raspat alcun libros que nos mancava le prime vice. In total, iste nove collection es circa 24TB, que es multo plus grande que le ultime (7TB). Nostre speculo es ora 31TB in total. De novo, nos deduplicava contra Library Genesis, pois que il ha ja torrents disponibile pro iste collection. Per favor vade al Speculo de Bibliotheca Pirata pro explorar le nove collection (EDIT: movite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Il ha plus information ibi super como le files es structurate, e lo que ha cambiate desde le ultime vice. Nos non ligara a illo ab hic, pois que isto es solmente un sito de blog que non hospita materiales illegal. Naturalmente, semination es etiam un excellente maniera de adjutar nos. Gratias a omnes qui sta seminante nostre previe serie de torrents. Nos es gratemente pro le responsa positive, e felice que il ha tanto personas qui se importa del preservation del cognoscentia e cultura in iste maniera inusual. 3x nove libros addite al Speculo del Bibliotheca Pirata (+24TB, 3.8 milliones de libros) Leger le articulos de compania per TorrentFreak: <a %(torrentfreak)s>prime</a>, <a %(torrentfreak_2)s>secunde</a> - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) articulos de compania per TorrentFreak: <a %(torrentfreak)s>prime</a>, <a %(torrentfreak_2)s>secunde</a> Non multo tempore retro, le “bibliothecas de umbra” moriva. Sci-Hub, le massive archivo illegal de documentos academic, habeva cessate de acceptar nove obras, a causa de actiones legal. “Z-Library”, le plus grande bibliotheca illegal de libros, videva su allegate creatores arrestate pro accusationes criminal de derecto de autor. Illes incredibilemente succedeva a escappar de lor arresto, ma lor bibliotheca non es minus sub menacia. Alcun paises ja face un version de isto. TorrentFreak <a %(torrentfreak)s>reportava</a> que China e Japon ha introducite exceptiones de IA a lor leges de derecto de autor. Es incert pro nos como isto interage con tractatos international, ma illo certemente da cobertura a lor companias domestic, lo que explica lo que nos ha vidite. Quanto al Archivo de Anna — nos continuara nostre labor clandestin radicate in conviction moral. Tamen nostre plus grande desiro es entrar in le lumine, e amplificar nostre impacto legalmente. Per favor reformar le derecto de autor. Quando Z-Library affrontava clausura, io habeva ja facite un copia de securitate de su integre bibliotheca e cercava un platforma pro hospitar lo. Isto esseva mi motivation pro comenciar le Archivo de Anna: un continuation del mission detra de illos initiativas anterior. Nos ha crescite desde alora pro devenir le plus grande bibliotheca de umbra del mundo, hospitante plus de 140 milliones de textos sub derecto de autor in numerose formatos — libros, documentos academic, revistas, jornales, e ultra. Mi equipa e io es ideologos. Nos crede que preservar e hospitar iste files es moralmente juste. Bibliothecas circa le mundo vide reductiones de financiation, e nos non pote confider le hereditage del humanitate a corporationes. Tunc veniva le IA. Praticamente tote le companias major que construe LLMs nos contactava pro educar super nostre datos. Le majoritate (ma non tote!) del companias basate in le US repensava quando illes realisava le natura illegal de nostre obra. In contrasto, firmas chinese ha enthusiastemente adoptate nostre collection, apparentemente non preoccupate per su legalitate. Isto es notabile date le rolo de China como signatario a quasi tote le tractatos international de derecto de autor. Nos ha date accesso a alte velocitate a circa 30 companias. Le majoritate de illes es companias de LLM, e alcunes es intermediarios de datos, qui revendera nostre collection. Le majoritate es chinese, ben que nos ha etiam collaborate con companias del US, Europa, Russia, Corea del Sud, e Japon. DeepSeek <a %(arxiv)s>admitteva</a> que un version anterior esseva educate super parte de nostre collection, ben que illes es reticente super lor ultime modello (probabilemente etiam educate super nostre datos tamen). Si le West vole remaner in le avante in le curso de LLMs, e ultimamente, AGI, illo debe reconsiderar su position super le derecto de autor, e presto. Que vos sia de accordo con nos o non super nostre caso moral, isto deveni ora un caso de economia, e mesmo de securitate national. Tote le blocos de poter sta construente super-scientificos artificial, super-hackers, e super-militares. Le libertate de information deveni un questione de supraviventia pro iste paises — mesmo un questione de securitate national. Nostre equipa es de tote le mundo, e nos non ha un alignment particular. Ma nos incoragiaria paises con forte leges de derecto de autor a usar iste menacia existential pro reformar los. Alora que facer? Nostre prime recommendation es simple: accurtar le termino de derecto de autor. In le US, le derecto de autor es concedite pro 70 annos post le morte del autor. Isto es absurd. Nos pote alignar isto con patentes, que es concedite pro 20 annos post le deposito. Isto deberea esser plus que sufficiente tempore pro autores de libros, documentos, musica, arte, e altere obras creative, pro esser plenmente compensate pro lor effortios (incluse projectos a longe termino como adaptationes cinematographic). Alora, al minimo, legislatores deberea includer exceptiones pro le preservation e dissemination in massa de textos. Si le perdita de entrata ab clientes individual es le principal preoccupation, le distribution a nivello personal poterea remaner prohibite. In vice, illes capabile de gestionar vaste repositorios — companias que educa LLMs, insimul con bibliothecas e altere archivos — esserea coperite per iste exceptiones. Reforma del derecto de autor es necesse pro securitate national TL;DR: LLMs chinese (incluse DeepSeek) es educate super mi archivo illegal de libros e documentos — le plus grande del mundo. Le West debe reformar le derecto de autor como un questione de securitate national. Per favor, vide le <a %(all_isbns)s>poste original del blog</a> pro plus information. Nos emittiva un desafio pro meliorar isto. Nos haberea premiate un prime loco con $6,000, un secunde loco con $3,000, e un tertie loco con $1,000. A causa del responsa travolgente e del submissiones incredibile, nos ha decidite augmentar un poco le fundo de premios, e premiar un tertie loco quadruple de $500 cata uno. Le ganatores es sub, ma assecurate de reguardar tote le submissiones <a %(annas_archive)s>hic</a>, o discargar nostre <a %(a_2025_01_isbn_visualization_files)s>torrent combinate</a>. Prime loco $6,000: phiresky Iste <a %(phiresky_github)s>submission</a> (<a %(annas_archive_note_2951)s>commento in Gitlab</a>) es simplemente toto lo que nos voleva, e plus! Nos specialmente apprecia le optiones de visualisation incredibilemente flexible (mesmo supportante shaders personalisate), ma con un lista comprensive de presets. Nos etiam apprecia quanto rapide e fluide toto es, le implementation simple (que non ha mesmo un backend), le minimappa ingeniose, e le explication extensive in lor <a %(phiresky_github)s>poste de blog</a>. Laboro incredibile, e le ganator ben meritate! - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Nostre cordes es plen de gratia. Ideas notabile Grattacielos pro raritate Multo de cursors pro comparar datasets, como si vos es un DJ. Barra de scala con numero de libros. Etiquetas belle. Schema de colores fresc e mappa de calor. Vista unic de mappa e filtros Annotationes, e etiam statisticas in directo Statisticas in directo Alcun altere ideas e implementationes que nos apprecia specialmente: Nos poterea continuar per un tempore, ma lassos nos arrestar hic. Assecura te de reguardar tote le submissiones <a %(annas_archive)s>hic</a>, o discargar nostre <a %(a_2025_01_isbn_visualization_files)s>torrent combinate</a>. Tanto submissiones, e cata uno apporta un perspectiva unic, sia in UI o in implementation. Nos al minus incorporara le submission de prime loco in nostre sito web principal, e forsan alcunes alteres. Nos ha etiam comenciate a pensar super como organisar le processo de identificar, confirmar, e tunc archivar le libros le plus rare. Plus a venir in iste fronto. Gratias a omnes qui participava. Es stupefaciente que tanto personas se importa. Facile alternar de datasets pro comparationes rapide. Tote le ISBNs CADAL SSNOs Fuga de datos de CERLALC DuXiu SSIDs Indice de eBooks de EBSCOhost Google Books Goodreads Archivo de Internet ISBNdb Registro Global de Editores de ISBN Libby Files in le Archivo de Anna Nexus/STC OCLC/Worldcat OpenLibrary Bibliotheca Estatal Russe Bibliotheca Imperial de Trantor Secunde loco $3,000: hypha “Durante que quadratos e rectangulos perfecte es mathematicamente agradabile, illos non provide superior localitate in un contexto de mappatura. Io crede que le asymmetria inherente in iste Hilbert o Morton classic non es un defecto ma un characteristica. Justo como le outline famose in forma de bota de Italia lo rende immediatemente recognoscibile in un mappa, le "peculiaritates" unic de iste curvas pote servir como punctos de referencia cognoscitive. Iste distinctivitate pote meliorar le memoria spatial e adjutar usatores a orientar se, potentialmente rendente plus facile localizar regiones specific o notar patronos.” Un altere <a %(annas_archive_note_2913)s>submission</a> incredibile. Non tanto flexible como le prime loco, ma nos de facto prefereva su visualisation a nivello macro super le prime loco (curva de plenamento de spatio, margines, etichettas, evidentiation, panoramica, e zoom). Un <a %(annas_archive_note_2971)s>commento</a> per Joe Davis resonava con nos: E ancora multe optiones pro visualisar e renderisar, assi como un UI incredibilemente fluide e intuitive. Un solide secunde loco! - Anna e le equipa (<a %(reddit)s>Reddit</a>) Alcun menses retro nos annunciava un <a %(all_isbns)s>premio de $10,000</a> pro facer le melior visualisation possibile de nostre datos monstrante le spatio de ISBN. Nos emphatisava monstrar qual files nos ha/nos non ha ja archiviate, e nos plus tarde un dataset describente quante bibliothecas contine ISBNs (un mesura de raritate). Nos ha essite submergite per le responsa. Il habeva tanto creativitate. Un grande gratias a omnes qui ha participate: vostre energia e enthusiasmo es contagiose! In ultime, nos voleva responder al sequente questiones: <strong>qual libros existe in le mundo, quanto nos ha jam archivate, e a qual libros nos deberea prestar attention postea?</strong> Es meraviliose vider tanto personas interessar se de iste questiones. Nos comenciava con un visualisation basic nos mesme. In minus de 300kb, iste imagine representa succintemente le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate: Tertie loco $500 #1: maxlion In iste <a %(annas_archive_note_2940)s>submission</a> nos vermente apprecia le differentes typos de vistas, in particular le vistas de comparation e de editor. Tertie loco $500 #2: abetusk Durante que non le UI le plus polite, iste <a %(annas_archive_note_2917)s>submission</a> verifica multe del cassetta. Nos apprecia specialmente su function de comparation. Tertie loco $500 #3: conundrumer0 Como le prime loco, iste <a %(annas_archive_note_2975)s>submission</a> nos impressionava con su flexibilitate. In ultime, isto es lo que face un grande instrumento de visualisation: flexibilitate maximal pro usatores potente, durante que mantene cosas simple pro usatores medie. Tertie loco $500 #4: charelf Le ultime <a %(annas_archive_note_2947)s>submission</a> a reciper un premio es bastante basic, ma ha alcun characteristicas unic que nos vermente apprecia. Nos apprecia como illes monstra quante datasets coperi un particular ISBN como un mesura de popularitate/affidabilitate. Nos etiam vermente apprecia le simplicitate ma efficacia de usar un cursor de opacitate pro comparationes. Vincitores del premio de $10,000 pro visualisation de ISBN TL;DR: Nos recipeva alcun submissiones incredibile pro le premio de $10,000 pro visualisation de ISBN. Fundo Como pote le Archivo de Anna attinger su mission de facer un copia de securitate de tote le cognoscentia del humanitate, sin saper qual libros es ancora ibi? Nos necessita un lista de cosas a facer. Un maniera de mappar isto es per le numeros ISBN, que desde le annos 1970 ha essite assignate a cata libro publicate (in le major parte del paises). Il non ha un autoritate central que sape tote le assignationes de ISBN. In vice, es un systema distribuite, ubi le paises recipe intervallos de numeros, que alora assigna intervallos plus parve a editoras major, qui poterea ulteriormente subdivider intervallos a editoras minor. Finalmente, numeros individual es assignate a libros. Nos comenciava a mappar ISBNs <a %(blog)s>duo annos retro</a> con nostre extraction de ISBNdb. Desde alora, nos ha extrahite multe plus de fontes de metadata, como <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, e plus. Un lista complete pote esser trovate in le paginas “Datasets” e “Torrents” in le Archivo de Anna. Nos ha ora de longe le plus grande collection completemente aperte e facilemente discargabile de metadata de libros (e dunque ISBNs) in le mundo. Nos ha <a %(blog)s>scribite extensivemente</a> super le rationes proque nos importa le preservation, e proque nos es actualmente in un fenestra critic. Nos debe ora identificar libros rare, subfocalisate, e unicmente a risco e preservar los. Haver bon metadata super tote le libros in le mundo adjuta con isto. $10,000 recompensa Consideration forte essera date al usabilitate e como bon illo pare. Monstra ver metadata pro ISBNs individual quando zoomante, como titulo e autor. Melior curva de plenamento de spatio. P.ex. un zig-zag, vadente de 0 a 4 in le prime fila e alora retro (in reverso) de 5 a 9 in le secunde fila — applicate recursivemente. Schemas de color differente o personalisabile. Visiones special pro comparar datasets. Modos de debuggar problemas, como altere metadata que non concorda ben (p.ex. titulos multo differente). Annotar imagines con commentos super ISBNs o intervallos. Qualque heuristicas pro identificar libros rare o a risco. Qualque ideas creative que tu pote imaginar! Codice Le codice pro generar iste imagines, assi como altere exemplos, pote esser trovate in <a %(annas_archive)s>iste directorio</a>. Nos ha create un formato de datos compacte, con le qual tote le information ISBN requirite es circa 75MB (compresite). Le description del formato de datos e le codice pro generar lo pote esser trovate <a %(annas_archive_l1244_1319)s>hic</a>. Pro le recompensa tu non es requirite usar isto, ma illo es probabilemente le formato le plus convenibile pro comenciar. Tu pote transformar nostre metadata como tu vole (ben que tote tu codice debe esser open source). Nos non pote expectar a vider lo que tu crea. Bon fortuna! Forka iste repo, e edita iste post de blog HTML (nulle altere backendes a parte de nostre backend Flask es permittite). Face que le imagine supra sia suavemente zoomabile, assi tu pote zoomar tote le via usque a ISBNs individual. Cliccar ISBNs debe portar te a un pagina de metadata o recerca in le Archivo de Anna. Tu debe ancora poter cambiar inter tote le differente datasets. Intervalles de paises e editoras debe esser sublineate al passar le cursor. Tu pote usar p.ex. <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a> pro information de paises, e nostre extraction “isbngrp” pro editoras (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Il debe functionar ben sur desktop e mobile. Il ha multo a explorar hic, assi nos annuncia un recompensa pro meliorar le visualisation supra. A differente de multe de nostre recompensas, iste es limitate in tempore. Tu debe <a %(annas_archive)s>submitter</a> tu codice open source ante le 2025-01-31 (23:59 UTC). Le melior submission recipera $6,000, le secunde loco es $3,000, e le tertie loco es $1,000. Tote le recompensas essera distribuite usante Monero (XMR). Infra es le criterios minimal. Si nulle submission attinge le criterios, nos poterea ancora assignar alcun recompensas, ma isto essera a nostre discretion. Pro punctos extra (iste es solmente ideas — lassa tu creativitate correr libere): Tu POTE deviar completemente del criterios minimal, e facer un visualisation completemente differente. Si illo es vermente spectacular, alora illo qualifica pro le recompensa, ma a nostre discretion. Face submissiones per postar un commento a <a %(annas_archive)s>iste questione</a> con un ligamine a tu repo bifurcate, requesta de fusion, o diff. - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Iste imagine es 1000×800 pixels. Cata pixel representa 2,500 ISBNs. Si nos ha un file pro un ISBN, nos face ille pixel plus verde. Si nos sape que un ISBN ha essite emittite, ma nos non ha un file correspondente, nos lo face plus rubie. In minus de 300kb, iste imagine representa succintemente le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate (alcun centena de GB compresse in plen). Ille etiam monstra: il ha multo de labor remanente in backupar libros (nos solmente ha 16%). Visualisar Tote le ISBNs — $10,000 recompensa per 2025-01-31 Iste imagine representa le plus grande “lista de libros” completemente aperte jammais assemblate in le historia del humanitate. Visualisar A parte del imagine de vista general, nos pote etiam regardar datasets individual que nos ha acquirite. Usa le menu a cascada e le buttones pro cambiar inter illos. Il ha multe patronos interessante a vider in iste imagines. Proque il ha un regularitate de lineas e blocos, que pare occurrer a differentes scales? Que es le areas vacue? Proque certe datasets es tanto agglomerate? Nos lassara iste questiones como un exercitio pro le lector. - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Conclusion Con iste standard, nos pote facer publicationes plus incrementalmente, e plus facilemente adder nove fontes de datos. Nos ja ha alcun publicationes excitante in le pipeline! Nos tamben spera que illo deveni plus facile pro altere bibliothecas umbra reflecter nostre collectiones. Finalmente, nostre objectivo es preservar le cognoscentia e cultura human pro semper, assi plus de redundantia es melior. Exemplo Vamos considerar nostre recente publication de Z-Library como un exemplo. Illo consiste de duo collectiones: “<span style="background: #fffaa3">zlib3_records</span>” e “<span style="background: #ffd6fe">zlib3_files</span>”. Isto nos permitte raspar e publicar separatamente le registros de metadata del files de libros actual. Assi, nos publicava duo torrents con files de metadata: Nos tamben publicava un serie de torrents con folders de datos binari, ma solmente pro le collection “<span style="background: #ffd6fe">zlib3_files</span>”, 62 in total: Per executar <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> nos pote vider lo que es intra: In iste caso, es metadata de un libro como reportate per Z-Library. Al nivello superior nos ha solmente “aacid” e “metadata”, ma non “data_folder”, pois que il non ha datos binari correspondente. Le AACID contine “22430000” como le ID primari, que nos pote vider es prendite de “zlibrary_id”. Nos pote expectar que altere AACs in iste collection ha le mesme structura. Ora vamos executar <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Isto es un metadata de AAC multo plus parve, ben que le major parte de iste AAC es situate alicubi altere in un file binari! Finalmente, nos ha un “data_folder” iste vice, assi nos pote expectar que le datos binari correspondente es situate a <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Le “metadata” contine le “zlibrary_id”, assi nos pote facilemente associar lo con le AAC correspondente in le collection “zlib_records”. Nos poterea haber associate in un numero de manieras differente, p.ex. per AACID — le standard non prescribe isto. Nota que non es necessari que le campo “metadata” ipse sia JSON. Illo poterea esser un catena de characteres continente XML o qualcunque altere formato de datos. Tu poterea mesmo immagazinar information de metadata in le blob binari associate, p.ex. si illo es multo datos. Files e metadata heterogenee, in un formato tanto proxime al original como possibile. Datos binari pote esser servite directemente per servitores web como Nginx. Identificatores heterogenee in le bibliothecas fonte, o mesmo le absentia de identificatores. Publicationes separate de metadata contra datos de files, o publicationes solmente de metadata (p.ex. nostre publication de ISBNdb). Distribution via torrents, ben que con le possibilitate de altere methodos de distribution (p.ex. IPFS). Registros immutabile, pois nos debe supponer que nostre torrents vivera pro semper. Publicationes incremental / publicationes appendibile. Legibile e scribibile per machina, convenientemente e rapidemente, specialmente pro nostre pila (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Inspection human un tanto facile, ben que isto es secundari al legibilitate per machina. Facile de semenar nostre collectiones con un seedbox standard locabile. Objectivos de designo Nos non importa que le files sia facile a navigar manualmente in disco, o cercabile sin pre-processamento. Nos non importa esser directemente compatibile con software de bibliotheca existente. Durante que illo debe esser facile pro alicuno semenar nostre collection usante torrents, nos non expecta que le files sia usabile sin cognoscentia technic significative e compromisso. Nostre caso de uso primari es le distribution de files e metadata associate ab differente collectiones existente. Nostre considerationes le plus importante es: Alcunos non-objectivos: Como le Archivo de Anna es open source, nos vole usar nostre formato directemente. Quando nos refresca nostre indice de recerca, nos solmente accede camminos publicamente disponibile, assi que alicuno qui bifurca nostre bibliotheca pote comenciar rapidemente. <strong>AAC.</strong> AAC (Contenitor del Archivo de Anna) es un ítem único que consiste en <strong>metadata</strong>, y opcionalmente <strong>datos binarios</strong>, ambos inmutables. Tiene un identificador único global, llamado <strong>AACID</strong>. <strong>AACID.</strong> El formato de AACID es este: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Por ejemplo, un AACID real que hemos lanzado es <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID range.</strong> Dado que los AACIDs contienen timestamps que aumentan de manera monótona, podemos usar eso para denotar rangos dentro de una colección particular. Usamos este formato: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, donde los timestamps son inclusivos. Esto es consistente con la notación ISO 8601. Los rangos son continuos y pueden superponerse, pero en caso de superposición deben contener registros idénticos a los previamente lanzados en esa colección (ya que los AACs son inmutables). No se permiten registros faltantes. <code>{collection}</code>: el nombre de la colección, que puede contener letras ASCII, números y guiones bajos (pero no dobles guiones bajos). <code>{collection-specific ID}</code>: un identificador específico de la colección, si es aplicable, por ejemplo, el ID de Z-Library. Puede ser omitido o truncado. Debe ser omitido o truncado si el AACID excedería de otro modo los 150 caracteres. <code>{ISO 8601 timestamp}</code>: una versión corta del ISO 8601, siempre en UTC, por ejemplo, <code>20220723T194746Z</code>. Este número debe aumentar de manera monótona para cada lanzamiento, aunque su semántica exacta puede diferir por colección. Sugerimos usar el tiempo de scraping o de generación del ID. <code>{shortuuid}</code>: un UUID pero comprimido a ASCII, por ejemplo, usando base57. Actualmente usamos la biblioteca de Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Binary data folder.</strong> Una carpeta con los datos binarios de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades: El directorio debe contener archivos de datos para todos los AACs dentro del rango especificado. Cada archivo de datos debe tener su AACID como el nombre del archivo (sin extensiones). El nombre del directorio debe ser un rango de AACID, precedido por <code style="color: green">annas_archive_data__</code>, y sin sufijo. Por ejemplo, uno de nuestros lanzamientos reales tiene un directorio llamado<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Es es recommendate facer iste folders un poco gestionabile in grandor, p.ex. non plus grande que 100GB-1TB cata uno, ben que iste recommendation pote cambiar con le tempore. <strong>Collection.</strong> Cada AAC pertenece a una colección, que por definición es una lista de AACs que son semánticamente consistentes. Esto significa que si haces un cambio significativo en el formato de los metadata, entonces debes crear una nueva colección. Le standard <strong>Metadata file.</strong> Un archivo de metadata contiene los metadata de un rango de AACs, para una colección particular. Estos tienen las siguientes propiedades: <code>data_folder</code> es opcional, y es el nombre de la carpeta de datos binarios que contiene los datos binarios correspondientes. El nombre del archivo de los datos binarios correspondientes dentro de esa carpeta es el AACID del registro. Cada objeto JSON debe contener los siguientes campos en el nivel superior: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opcional). No se permiten otros campos. El nombre del archivo debe ser un rango de AACID, precedido por <code style="color: red">annas_archive_meta__</code> y seguido por <code>.jsonl.zstd</code>. Por ejemplo, uno de nuestros lanzamientos se llama<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Como indica la extensión del archivo, el tipo de archivo es <a %(jsonlines)s>JSON Lines</a> comprimido con <a %(zstd)s>Zstandard</a>. <code>metadata</code> es metadata arbitrario, según la semántica de la colección. Debe ser semánticamente consistente dentro de la colección. El prefijo <code style="color: red">annas_archive_meta__</code> puede adaptarse al nombre de su institución, por ejemplo, <code style="color: red">my_institute_meta__</code>. <strong>“records” y “files” collections.</strong> Por convención, a menudo es conveniente lanzar “records” y “files” como colecciones diferentes, para que puedan ser lanzadas en diferentes horarios, por ejemplo, basados en tasas de scraping. Un “record” es una colección solo de metadata, que contiene información como títulos de libros, autores, ISBNs, etc., mientras que “files” son las colecciones que contienen los archivos reales (pdf, epub). Finalmente, nos decidemos por un estándar relativamente simple. Es bastante flexible, no normativo y un trabajo en progreso. <strong>Torrents.</strong> Le files de metadata e le folders de datos binari pote esser includite in torrents, con un torrent per file de metadata, o un torrent per folder de datos binari. Le torrents debe haber le nomine original del file/directorio plus un suffixo <code>.torrent</code> como lor nomine de file. <a %(wikipedia_annas_archive)s>Le Archivo de Anna</a> ha devenite de longe le plus grande bibliotheca umbra del mundo, e le sol bibliotheca umbra de su scala que es completemente open-source e open-data. Infra es un tabula de nostre pagina de Datasets (legiermente modificate): Nos ha realisate isto in tres modos: Reflecter bibliothecas umbra open-data existente (como Sci-Hub e Library Genesis). Adjutar bibliothecas umbra que vole esser plus aperte, ma non habeva le tempore o recursos pro facer lo (como le collection de comics de Libgen). Raspar bibliothecas que non desira compartir in massa (como Z-Library). Pro (2) e (3) nos administra ora un collection considerabile de torrents nos mesme (centos de TBs). Usque ora nos ha approchate iste collectiones como unicas, significante infrastructura e organisation de datos personalisate pro cata collection. Isto adde un carga significative a cata publication, e rende specialmente difficile facer publicationes plus incremental. Es pro isto que nos ha decidite standardisar nostre publicationes. Isto es un articulo technic in le qual nos introduce nostre standard: <strong>Contenitores de Archivo de Anna</strong>. Contenitores del Archivo de Anna (AAC): standardisar le publicationes del plus grande bibliotheca umbra del mundo Le Archivo de Anna ha devenite le plus grande bibliotheca umbra del mundo, requirente nos a standardisar nostre publicationes. 300GB+ de coperturas de libros publicate Finalmente, nos es felice de annunciar un parve publication. In collaboration con le personas qui opera le forca Libgen.rs, nos es partagiante tote lor coperturas de libros via torrents e IPFS. Isto distribuera le carga de vider le coperturas inter plus de machinas, e los preservara melior. In multe (ma non tote) casos, le coperturas de libros es includite in le files mesme, assi isto es un sorta de "datos derivate". Ma haber lo in IPFS es ancora multo utile pro le operation quotidian de ambe le Archivo de Anna e le varie forcas de Library Genesis. Como de costume, tu pote trovar iste publication al Speculo de Bibliotheca Pirata (EDIT: transferite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>). Nos non ligara a illo hic, ma tu pote facilemente trovar lo. Sperabilemente nos pote relaxar nostre ritmo un poco, ora que nos ha un alternative decente a Z-Library. Iste carga de travalio non es particularmente sustainable. Si tu es interessate in adjutar con programmation, operationes de servitor, o travalio de preservation, contacta nos. Il ha ancora multo de <a %(annas_archive)s>travalio a facer</a>. Gratias pro tu interesse e supporto. Commutar a ElasticSearch Alcun consultas prendeva multissimo tempore, al puncto que illos monopolizava tote le connexiones aperte. Per defecto MySQL ha un longitud de parola minim, o tu indice pote devenir vermente grande. Personas reportava non poter cercar "Ben Hur". Le cerca esseva solmente un poco rapide quando totalmente cargate in memoria, lo que requireva nos obtener un machina plus costose pro executar isto, plus alcun commandos pro pre-cargar le indice al initio. Nos non haberea potite extender lo facilemente pro construir nove functionalitates, como melior <a %(wikipedia_cjk_characters)s>tokenisation pro linguas sin spatio blanc</a>, filtrage/faceting, ordinamento, suggestiones de "voleva dicer", autocompletion, etc. Un de nostre <a %(annas_archive)s>tickets</a> esseva un sacco de problemas con nostre systema de cerca. Nos usava le cerca de texto complete de MySQL, pois que nos habeva tote nostre datos in MySQL de omne modo. Ma illo habeva su limites: Post parlar con un gruppo de expertos, nos optava pro ElasticSearch. Illo non ha essite perfecte (lor suggestiones de "voleva dicer" e functionalitates de autocompletion predefinite es mal), ma in general illo ha essite multo melior que MySQL pro le cerca. Nos ancora non es <a %(youtube)s>troppo entusiasmate</a> de usar lo pro datos critic (ben que illes ha facite multe <a %(elastic_co)s>progressos</a>), ma in general nos es bastante felice con le commutation. Pro ora, nos ha implementate un cerca multo plus rapide, melior supporto de linguas, melior ordinamento de relevancia, differentes optiones de ordinamento, e filtrage per lingua/typo de libro/typo de file. Si tu es curiose de como illo functiona, <a %(annas_archive_l140)s>da</a> <a %(annas_archive_l1115)s>un</a> <a %(annas_archive_l1635)s>reguardo</a>. Illo es bastante accessibile, ben que illo poterea usar un poco plus de commentos… Le Archivo de Anna es totalmente open source Nos crede que le information debe esser libere, e nostre proprie codice non es un exception. Nos ha publicate tote nostre codice in nostre istantia de Gitlab hospitate privatemente: <a %(annas_archive)s>Le Software de Anna</a>. Nos tamben usa le tracker de questiones pro organisar nostre labor. Si tu vole ingagiar te con nostre disveloppamento, isto es un bon loco pro comenciar. Pro dar te un gustate del cosas que nos labora super, considera nostre labor recente super meliorationes de performantia del latere del cliente. Pois que nos non ha ancora implementate pagination, nos sovente retornava paginas de recerca multo longe, con 100-200 resultatos. Nos non voleva truncar le resultatos de recerca troppo tosto, ma isto significava que illo retardava alcun apparatos. Pro isto, nos implementava un parve astutia: nos envolteva le major parte del resultatos de recerca in commentos HTML (<code><!-- --></code>), e pois scribeva un parve Javascript que detectava quando un resultato deberea devenir visibile, al qual momento nos disenvolveva le commento: DOM "virtualisation" implementate in 23 lineas, sin necessitate de bibliothecas elaborate! Isto es le sorta de codice pragmatico rapide que tu obtene quando tu ha tempore limitate e problemas real que necessita esser resolvite. Il ha essite reportate que nostre cerca ora functiona ben sur dispositivos lente! Un altere grande effortio esseva automatizar le construction del base de datos. Quando nos lanciate, nos simplemente colligite differentes fontes de maniera casual. Ora nos vole mantener los actualisate, assi nos scribite un serie de scriptos pro discargar nove metadata ab le duo forcas de Library Genesis, e integrar los. Le objectivo es non solmente facer isto utile pro nostre archivo, ma etiam facer le cosas facile pro alicuno qui vole experimentar con metadata de bibliotheca umbra. Le objectivo esserea un notebook Jupyter que ha omne sorta de metadata interessante disponibile, assi nos pote facer plus de recerca como determinar qual <a %(blog)s>percento de ISBNs es preservate pro semper</a>. Finalmente, nos renovava nostre systema de donation. Tu pote ora usar un carta de credito pro depositar moneta directemente in nostre portafolios crypto, sin realmente necessitar saper nihil super cryptocurrencies. Nos continuara monitorar quanto ben isto functiona in practica, ma isto es un grande passo. Con Z-Library que cadeva e su (presumite) fundatores que esseva arrestate, nos ha laborate sin pausa pro provider un bon alternative con le Archivo de Anna (nos non lo ligara hic, ma tu pote cercar lo in Google). Ecce alcun del cosas que nos ha attingite recentemente. Le Actualisation de Anna: archivo totalmente open source, ElasticSearch, 300GB+ de coperturas de libros Nos ha laborate sin pausa pro provider un bon alternative con le Archivo de Anna. Ecce alcun del cosas que nos ha attingite recentemente. Analyses Duplicatos semantic (differente scannos del mesme libro) pote theoreticamente esser filtrate, ma es complicate. Quando nos examinava manualmente le comics, nos trovava tropo multe false positivos. Il ha alcun duplicatos purmente per MD5, lo que es relativemente dispendiose, ma filtrar los solmente nos haberea date circa 1% in de economias. A iste scala, illo es ancora circa 1TB, ma etiam, a iste scala 1TB non importa vermente. Nos prefererea non riscar accidentalmente destruer datos in iste processo. Nos trovava un gruppo de datos non-librari, como filmes basate super libros de comics. Isto etiam pare dispendiose, pois que illos es ja largemente disponibile per altere medios. Tamen, nos realisava que nos non poterea simplemente filtrar files de filmes, pois que il ha etiam <em>libros de comics interactive</em> que esseva publicate sur le computator, que alicuno registrava e salvava como filmes. In ultime, omne cosa que nos poterea deler del collection solmente salvarea un pauc percento. Alora nos rememorava que nos es accumulatores de datos, e le personas qui va specular isto es etiam accumulatores de datos, e assi, “QUE VOS VUOLE DICER, DELER?!” :) Quando vos recipe 95TB versate in vostre cluster de immagazinamento, vos tenta comprender lo que es mesmo ibi… Nos faceva alcun analyses pro vider si nos poterea reducer un poco le dimension, como per remover duplicatos. Ecce alcun de nostre constatationes: Nos presenta dunque a vos, le collection integre e non modificate. Es multo datos, ma nos spera que abbastanza personas se interessara a propagar lo de omne maniera. Collaboration Date su magnitude, iste collection ha essite longemente in nostre lista de desiros, assi post nostre successo con facer un copia de securitate de Z-Library, nos fixava nostre objectivos super iste collection. Al initio nos lo rascava directemente, lo que esseva un ver desafio, pois que lor servitor non esseva in le melior condition. Nos obteneva circa 15TB in iste maniera, ma illo esseva lente. Felicemente, nos succedeva a entrar in contacto con le operador del bibliotheca, qui consentiva a inviar nos tote le datos directemente, lo que esseva multo plus rapide. Il ancora prendeva plus de medietate de un anno pro transferer e processar tote le datos, e nos quasi perdeva tote illo a causa de corruption de disco, lo que haberea significate recommenciar ab initio. Iste experientia nos ha facite creder que es importante render iste datos disponibile tan rapide como possibile, assi illo pote esser mirroreate largemente. Nos es solmente un o duo incidentes mal temporisate distante de perder iste collection pro semper! Le collection Mover se rapide significa que le collection es un poco disorganisate… Vamos a dar un oculo. Imagina que nos ha un systema de files (que in realitate nos es dividente trans torrents): Le prime directorio, <code>/repository</code>, es le parte plus structurate de isto. Iste directorio contine le assi nominate “mille dirs”: directorios cata uno con mille files, que es numerate incrementalmente in le base de datos. Directorio <code>0</code> contine files con comic_id 0–999, e assi de sequente. Isto es le mesme schema que Library Genesis ha usate pro su collections de fiction e non-fiction. Le idea es que cata “mille dir” es automaticamente convertite in un torrent tan tosto como illo es plenate. Tamen, le operador de Libgen.li nunquam faceva torrents pro iste collection, e assi le mille dirs probabilemente deveniva inconveniente, e cedeva a “dirs non ordinate”. Iste es <code>/comics0</code> a <code>/comics4</code>. Illos tote contine structuras de directorios unic, que probabilemente faceva senso pro colliger le files, ma non face multo senso pro nos ora. Felicemente, le metadata ancora refere directemente a tote iste files, assi lor organisation de immagazinamento sur disco non importa vermente! Le metadata es disponibile in le forma de un base de datos MySQL. Isto pote esser discargate directemente del sito web de Libgen.li, ma nos lo rendera etiam disponibile in un torrent, insimul a nostre proprie tabula con tote le hashes MD5. <q>Dr. Barbara Gordon tenta perder se in le mundo mundan del bibliotheca…</q> Bifurcationes de Libgen Prime, un poco de contexto. Forsan vos cognosce Library Genesis pro lor collection epic de libros. Minus personas sape que le voluntarios de Library Genesis ha create altere projectos, como un collection considerabile de revistas e documentos standard, un copia de securitate complete de Sci-Hub (in collaboration con le fundator de Sci-Hub, Alexandra Elbakyan), e vermente, un collection massive de comics. A un certe puncto, differente operadores de speculos de Library Genesis se separava, lo que resultava in le situation actual de haber un numero de differente “bifurcationes”, tote ancora portante le nomine Library Genesis. Le bifurcation Libgen.li unicmente ha iste collection de comics, assi como un collection considerabile de revistas (que nos es etiam laborante super). Raccolta de fundos Nos libera iste datos in alcun grande pecias. Le prime torrent es de <code>/comics0</code>, que nos poneva in un enorme file .tar de 12TB. Isto es melior pro vostre disco dur e software de torrent que un multitude de files plus parve. Como parte de iste liberation, nos face un raccolta de fundos. Nos cerca colliger $20,000 pro coperir le costos operational e de contracto pro iste collection, e etiam pro permitter projectos continuante e futur. Nos ha alcun <em>massive</em> in le labor. <em>Qui io supporta con mi donation?</em> In breve: nos sustene tote le cognoscentia e cultura del humanitate, e lo rende facilemente accessibile. Tote nostre codice e datos es open source, nos es un projecto completemente gerite per voluntarios, e nos ha salvate 125TB de libros usque nunc (in addition a le torrents existente de Libgen e Scihub). In ultime, nos construe un volante que permitte e incita personas a trovar, escannar, e salvar tote le libros del mundo. Nos scribera super nostre plano magistral in un post futur. :) Si vos dona pro un adhesion de 12 menses “Amazing Archivist” ($780), vos pote <strong>“adopter un torrent”</strong>, lo que significa que nos ponera vostre nomine de usator o message in le nomine de un del torrents! Vos pote donar visitando <a %(wikipedia_annas_archive)s>le Archivo de Anna</a> e cliccando le button “Donar”. Nos cerca etiam plus voluntarios: ingenieros de software, investigadores de securitate, expertos mercantiles anonyme, e traductores. Vos pote etiam supportar nos forniendo servicios de hospitio. E naturalmente, per favor propaga nostre torrents! Gratias a omnes qui nos ha supportate tanto generosemente! Vos vermente face un differentia. Ecce le torrents liberate usque nunc (nos ancora processa le resto): Tote le torrents pote esser trovate in <a %(wikipedia_annas_archive)s>le Archivo de Anna</a> sub “Datasets” (nos non liga directemente ibi, assi ligamines a iste blog non es removite de Reddit, Twitter, etc.). De ibi, seque le ligamine al sito web de Tor. <a %(news_ycombinator)s>Discuter sur Hacker News</a> Que es le sequente? Un gruppo de torrents es excellente pro preservation a longe termino, ma non tanto pro accesso quotidian. Nos laborara con partnarios de hospitio pro poner tote iste datos in le web (como le Archivo de Anna non hospita nihil directemente). Naturalmente vos potera trovar iste ligamines de download in le Archivo de Anna. Nos invita etiam omnes a facer cosas con iste datos! Adjuta nos a melior analizar lo, deduplicar lo, poner lo in IPFS, remixar lo, trainar vostre modelos de IA con illo, e assi via. Es tote vostre, e nos non pote attender vider lo que vos face con illo. Finalmente, como dicite antea, nos ancora ha alcun massive liberationes imminente (si <em>alguem</em> poterea <em>accidentalmente</em> inviar nos un dump de un <em>certe</em> base de datos ACS4, vos sape ubi trovar nos…), e etiam construer le volante pro salvar tote le libros del mundo. Assi remane in contacto, nos solmente comencia. - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Le plus grande bibliotheca umbra de libros de comics es probabilemente celle de un forca particular de Library Genesis: Libgen.li. Le un sol administrator qui gestiva ille sito succedeva a colliger un collection de comics insani de plus de 2 milliones de files, totalisante plus de 95TB. Tamen, a differente de altere collectiones de Library Genesis, iste non esseva disponibile in massa via torrents. Tu poteva solmente acceder a iste comics individualmente via su servitor personal lente — un puncto unic de defecte. Usque hodie! In iste posto nos va contar plus sur iste collection, e sur nostre campania de fund-raising pro supportar plus de iste labor. Le Archivo de Anna ha salvate le plus grande bibliotheca umbra de comics del mundo (95TB) — tu pote adjutar a semenar lo Le plus grande bibliotheca umbra de libros de comics del mundo habeva un puncto unic de defecte... usque hodie. Advertimento: iste articulo de blog ha essite deprecate. Nos ha decidite que IPFS non es ancora preste pro le prime tempore. Nos ancora ligara a files in IPFS ab le Archivo de Anna quando possibile, ma nos non lo hostara plus nos mesme, ni recommendara a alteres de specular usante IPFS. Per favor vide nostre pagina de Torrentes si tu vole adjutar a preservar nostre collection. Metter 5,998,794 libros sur IPFS Un multiplication de copias Retornante a nostre question original: como pote nos pretender preservar nostre collectiones in perpetuitate? Le problema principal hic es que nostre collection ha <a %(torrents_stats)s>crescite</a> a un passo rapide, per raspamento e open-sourcing de alcun collectiones massive (super le labor fantastic ja facite per altere bibliothecas de datos aperte como Sci-Hub e Library Genesis). Iste crescentia in datos rende plus difficile que le collectiones sia mirate circum le mundo. Le immagazinamento de datos es costose! Ma nos es optimista, specialmente quando observante le sequente tres tendentias. Le <a %(annas_archive_stats)s>grandor total</a> de nostre collectiones, super le ultime menses, disaggregate per numero de seminator de torrent. Tendentias de precios de HDD de differentes fontes (clicca pro vider le studio). <a %(critical_window_chinese)s>Version chinese 中文版</a>, discute in <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Nos ha colligite le fructos a basse altitude Isto seque directemente de nostre prioritates discutite supra. Nos prefere laborar a liberar grande collectiones primo. Ora que nos ha securate alcun del plus grande collectiones in le mundo, nos expecta que nostre crescentia sia multo plus lente. Il ha ancora un cauda longe de collectiones plus parve, e nove libros es scannate o publicate cata die, ma le taxa essera probabilemente multo plus lente. Nos poterea ancora duplicar o mesmo triplicar in grandor, ma super un periodo de tempore plus longe. Meliorationes de OCR. Prioritates Codice de software de scientia e ingenieria Versiones fictional o de divertimento de tote le supra Datos geographic (ex. mapas, investigationes geologic) Datos interne de corporationes o governamentos (fugas) Datos de mensuration como mensurationes scientific, datos economic, reportos corporative Registros de metadata generalmente (de non-fiction e fiction; de altere media, arte, personas, etc.; includente recensiones) Libros de non-fiction Revistas non-fiction, jornales, manuales Transcriptiones non-fiction de discursos, documentarios, podcasts Datos organic como sequencias de DNA, semines de plantas, o campiones microbial Articulos academic, jornales, reportos Sitos web de scientia e ingenieria, discussiones online Transcriptiones de proceduras legal o judicial Unicamente a risco de destruction (ex. per guerra, reductiones de fundos, actiones legal, o persecution politic) Rare Unicamente subfocalisate Proque nos importa tanto super articulos e libros? Vamos poner de latere nostre credentia fundamental in preservation in general — nos poterea scriber un altere articulo super isto. Alora proque articulos e libros specificamente? Le responsa es simple: <strong>densitate de information</strong>. Per megabyte de immagazinamento, texto scribite immagazina le plus de information de tote le medios. Dum nos importa de ambe cognoscentia e cultura, nos importa plus del prime. In general, nos trova un hierarchia de densitate de information e importantia de preservation que appare approximativemente assi: Le classification in iste lista es un poco arbitrari — plure elementos es ligate o ha disaccordos intra nostre equipa — e probabilemente nos oblida alcun categorias importante. Ma isto es approximativemente como nos prioritiza. Alcun de iste elementos es troppo differente del alteres pro nos preoccupar (o es ja prendite in cura per altere institutiones), como datos organic o datos geographic. Ma le majoritate del elementos in iste lista es vermente importante pro nos. Un altere grande factor in nostre prioritisation es quanto a risco un certe obra es. Nos prefere concentrar nos super obras que es: Finalmente, nos importa le scala. Nos ha tempore e moneta limitate, assi nos prefererea passar un mense salvante 10,000 libros que 1,000 libros — si illos es de valor e a risco equal. <em><q>Le perdite non pote esser recuperate; ma lassar nos salvar lo que remane: non per cryptas e serraturas que los defende del oculos public e uso, consignante los al dispendio del tempore, ma per tal un multiplication de copias, que los pon ultra le alcance de accidente.</q></em><br>— Thomas Jefferson, 1791 Bibliothecas umbra Codice pote esser open source in Github, ma Github como un integro non pote esser facilemente mirate e assi preservate (ben que in iste caso particular il ha copias sufficientemente distribuite de le major parte del repositorios de codice) Registros de metadata pote esser liberemente vidite in le sito web de Worldcat, ma non discargate in massa (usque nos <a %(worldcat_scrape)s>raspava</a> los) Reddit es libere a usar, ma recentemente ha instaurate medidas anti-raspamento stricte, in le sequela de LLM famelic de datos (plus super isto plus tarde) Il ha multe organisationes que ha missiones simile, e prioritates simile. In effecto, il ha bibliothecas, archivos, laboratorios, museos, e altere institutiones incaricate del preservation de iste sorta. Multes de illos es ben-financiate, per governamentos, individuos, o corporationes. Ma illos ha un massive puncto cec: le systema legal. Hic reside le rolo unic de bibliothecas umbra, e le ration pro le existentia de le Archivo de Anna. Nos pote facer cosas que altere institutiones non es permittite facer. Ora, non es (frequentemente) que nos pote archivar materiales que es illegal preservar alibi. No, es legal in multe locos construir un archivo con omne libros, papiros, revistas, e assi via. Ma lo que archivos legal sovente manca es <strong>redundantia e longevitá</strong>. Il ha libros del qual solmente un copia existe in alcun bibliotheca physic alicubi. Il ha registros de metadata custodite per un sol corporation. Il ha jornales solmente preservate in microfilmo in un sol archivo. Bibliothecas pote reciper reductiones de fundos, corporationes pote devenir bankrupta, archivos pote esser bombardate e comburite al sol. Isto non es hypothetic — isto eveni tote le tempore. Le cosa que nos pote facer unicmente in le Archivo de Anna es immagazinar multe copias de obras, a scala. Nos pote colliger articulos, libros, revistas, e plus, e distribuer los in massa. Nos face isto actualmente via torrents, ma le technologias exacto non importa e cambiara con le tempore. Le parte importante es obtener multe copias distribuite trans le mundo. Isto citation de plus de 200 annos retro ancora sona ver: Un nota rapide super le dominio public. Desde que le Archivo de Anna se concentra unicmente super activitates que es illegal in multe locos circum le mundo, nos non nos preoccupa con collectiones largemente disponibile, como libros de dominio public. Entitates legal sovente ja se occupa ben de illo. Tamen, il ha considerationes que nos face a vices laborar super collectiones publicamente disponibile: - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Le costos de immagazinamento continua a decrescer exponentialmente 3. Meliorationes in densitate informationis Nos actualmente immagazina libros in le formatos brut que nos es date. Certo, illos es compresse, ma sovente illos es ancora grande scansiones o photographias de paginas. Usque nunc, le sol optiones pro reducer le grandor total de nostre collection ha essite per medio de un compression plus aggressive, o deduplication. Tamen, pro obtener economias significative, ambe es troppo lossy pro nostre gusto. Compression forte de photographias pote render le texto apenas legibile. E deduplication require alta confidentia que libros es exactemente le mesme, lo que sovente es troppo inaccurate, specialmente si le contento es le mesme ma le scansiones es facite in differentes occasion. Il ha semper essite un tertie option, ma su qualitate ha essite tanto abysmal que nos nunquam lo considerava: <strong>OCR, o Recognition Optical de Characteres</strong>. Isto es le processo de converter photographias in texto simple, per medio de AI pro detectar le characteres in le photographias. Instrumentos pro isto ha existite desde longe, e ha essite bastante decente, ma “bastante decente” non es sufficiente pro fin de preservation. Tamen, recente modelos de apprendimento profunde multi-modal ha facite progressos extrememente rapide, ben que ancora a alte costos. Nos expecta que ambe le accuratezza e le costos meliorara dramaticamente in le annos veniente, al puncto que illo devenira realistic applicar a nostre integre bibliotheca. Quando isto occurre, nos probabilemente ancora preservara le files original, ma in addition nos poterea haber un version multo plus parve de nostre bibliotheca que le major parte del personas volera specular. Le puncto es que le texto brut ipse se comprime mesmo melior, e es multo plus facile deduplicar, nos dando mesmo plus de economias. In general, non es irrealistic expectar al minus un reduction de 5-10x in le grandor total del file, forsan mesmo plus. Mesmo con un reduction conservative de 5x, nos considerarea <strong>$1,000–$3,000 in 10 annos mesmo si nostre bibliotheca triplica in grandor</strong>. Al tempore de scriber, <a %(diskprices)s>precios de discos</a> per TB es circa $12 pro discos nove, $8 pro discos usate, e $4 pro tape. Si nos es conservative e considera solmente discos nove, isto significa que immagazinar un petabyte costa circa $12,000. Si nos assume que nostre bibliotheca triplicara de 900TB a 2.7PB, isto significarea $32,400 pro mirrar nostre integre bibliotheca. Addente electricitate, costo de altere hardware, e assi via, lassar nos rotundar lo a $40,000. O con tape plus como $15,000–$20,000. De un latere <strong>$15,000–$40,000 pro le summa de tote le cognoscentia human es un furto</strong>. De altere latere, es un poco abrupto expectar toneladas de copias complete, specialmente si nos etiam volerea que illes continua a semenar lor torrents pro le beneficio de alteres. Isto es hodie. Ma le progresso marcha in avantia: Le costos de discos dur per TB ha essite circa dividite in tertios super le ultime 10 annos, e probabilemente continuara a decrescer a un passo simile. Tape pare esser in un trajectoria simile. Le precios de SSD cade mesmo plus rapidemente, e poterea superar le precios de HDD al fin del decennio. Si isto se mantene, alora in 10 annos nos poterea considerar solmente $5,000–$13,000 pro mirrar nostre integre collection (1/3), o mesmo minus si nos cresce minus in grandor. Durante que ancora multo de moneta, isto essera attingibile pro multe personas. E poterea esser mesmo melior a causa del puncto sequente… In le Archivo de Anna, nos es sovente demandate como nos pote pretender preservar nostre collectiones in perpetuitate, quando le grandor total ja se approximante a 1 Petabyte (1000 TB), e ancora cresce. In iste articulo nos va examinar nostre philosophia, e vider proque le proxime decennio es critic pro nostre mission de preservar le cognoscentia e cultura del humanitate. Fenestra critic Si iste previsiones es accurate, nos <strong>solmente debe attender un par de annos</strong> ante que nostre integre collection essera largemente speculate. Assi, in le parolas de Thomas Jefferson, “ponite ultra le alcance de accidente.” Infelicemente, le advento de LLMs, e lor formation avide de datos, ha ponite multe detentores de copyright in le defensive. Mesmo plus que illes ja era. Multes sitos web rende plus difficile raspar e archivar, litigios vola circa, e durante tote isto bibliothecas e archivos physic continua esser neglectate. Nos pote solmente expectar que iste tendencias continua a aggravar, e multe obras a esser perdite ben ante que illos entra in le dominio public. <strong>Nos es al eve de un revolution in preservation, ma <q>le perdite non pote esser recuperate.</q></strong> Nos ha un fenestra critic de circa 5-10 annos durante le qual es ancora bastante costose operar un bibliotheca umbra e crear multe speculos circa le mundo, e durante le qual accesso non ha essite completemente clausurate ancora. Si nos pote transversar iste fenestra, alora nos vermente habera preservate le cognoscentia e cultura del humanitate in perpetuitate. Nos non deberea lassar iste tempore esser vane. Nos non deberea lassar iste fenestra critic clauder super nos. Vamos. Le fenestra critic de bibliothecas de umbra Como pote nos pretender preservar nostre collectiones in perpetuitate, quando illos ja se approximante a 1 PB? Collection Alcun information additional super le collection. <a %(duxiu)s>Duxiu</a> es un massive base de datos de libros scannate, create per le <a %(chaoxing)s>SuperStar Digital Library Group</a>. Le majoritate es libros academic, scannate pro render los disponibile digitalmente a universitates e bibliothecas. Pro nostre publico anglo-parlante, <a %(library_princeton)s>Princeton</a> e le <a %(guides_lib_uw)s>Universitate de Washington</a> ha bon summarios. Il ha tamben un excellente articulo que da plus de contexto: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cercar lo in le Archivo de Anna). Le libros de Duxiu ha essite longemente pirateate in le internet chinese. Usualmente illos es vendite pro minus de un dollar per revendedores. Illos es typicamente distribuite usante le equivalent chinese de Google Drive, que ha essite sovente hackate pro permitter plus de spatio de immagazinamento. Alcun detalios technic pote esser trovate <a %(github_duty_machine)s>hic</a> e <a %(github_821_github_io)s>hic</a>. Ben que le libros ha essite distribuite semi-publicamente, es bastante difficile obtener los in massa. Nos habeva isto alte in nostre lista de cosas a facer, e allocava plure menses de labor a plen tempore pro isto. Tamen, recentemente un voluntario incredibile, stupefaciente, e talentose nos contactava, dicente que illes habeva facite tote iste labor ja — a grande costo. Illes partagiva le integre collection con nos, sin expectar nihil in retorno, excepte le garantia de preservation a longe termino. Vermente remarcabile. Illes consentiva a peter adjuta in iste maniera pro obtener le collection OCR'ate. Le collection es 7,543,702 files. Isto es plus que le non-fiction de Library Genesis (circa 5.3 milliones). Le dimension total del files es circa 359TB (326TiB) in su forma actual. Nos es aperte a altere propositiones e ideas. Simplemente contacta nos. Consulta le Archivo de Anna pro plus information super nostre collections, effortios de preservation, e como tu pote adjutar. Gratias! Paginas de exemplo Pro demonstrar a nos que tu ha un bon pipeline, hic es alcun paginas de exemplo pro comenciar, ex un libro super superconductores. Tu pipeline debe tractar adequatemente le mathematica, tabulas, graphicos, notas a pede, etc. Invia tu paginas processate a nostre email. Si illos pare bon, nos te inviara plus in private, e nos expecta que tu sia capace de exequer tu pipeline rapidemente super illos tamben. Una vice que nos es satisfacite, nos pote facer un accordo. - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Version chinese 中文版</a>, <a %(news_ycombinator)s>Discuter in Hacker News</a> Isto es un breve articulo de blog. Nos cerca un compania o institution pro adjutar nos con OCR e extraction de texto pro un massive collection que nos ha acquirite, in cambio de accesso exclusive anticipate. Post le periodo de embargo, nos naturalmente publicara le integre collection. Texto academic de alta qualitate es extrememente utile pro le entrainamento de LLMs. Mentre nostre collection es in chinese, isto debe esser mesmo utile pro le entrainamento de LLMs in anglese: le modelos pare encodar conceptos e cognoscentia sin regardo al lingua fonte. Pro isto, le texto debe esser extrahite del scannos. Que obtene le Archivo de Anna de isto? Recerca de texto integre del libros pro su usatores. Proque nostre objectivos se aligna con illos del disveloppatores de LLM, nos cerca un collaborator. Nos es disposte a dar te <strong>accesso exclusive anticipate a iste collection in massa pro 1 anno</strong>, si tu pote facer OCR e extraction de texto adequatemente. Si tu es disposte a compartir con nos le integre codice de tu pipeline, nos serea disposte a embargar le collection plus longe. Accesso exclusive pro companias de LLM al plus grande collection de libros non-fictional chinese in le mundo <em><strong>TL;DR:</strong> Le Archivo de Anna ha acquirite un collection unic de 7.5 milliones / 350TB de libros non-fictional chinese — plus grande que Library Genesis. Nos es disposte a dar a un compania de LLM accesso exclusive, in cambio de OCR de alte qualitate e extraction de texto.</em> Architectura de systema Assi, supponamos que vos trovava alcun companias que es disposte a hospitar vostre sito web sin clauder lo — vamos a appellar los “fornitores amante de libertate” 😄. Vos rapidemente discoperira que hospitar toto con illos es bastante costose, assi vos poterea voler trovar alcun “fornitores bon mercato” e facer le hospitation real ibi, proxyante per le fornitores amante de libertate. Si vos lo face correctemente, le fornitores bon mercato nunquam sapera lo que vos hospita, e nunquam recipera alcun plangas. Con tote iste fornitores il ha un risco que illos vos claude de omne modo, assi vos necessita etiam redundancia. Nos necessita isto a tote nivellos de nostre pila. Un compania un poco amante de libertate que se ha ponite in un position interessante es Cloudflare. Illos ha <a %(blog_cloudflare)s>arguite</a> que illos non es un fornitor de hospitation, ma un utilitate, como un ISP. Illos non es dunque subjecte a DMCA o altere requestas de abattimento, e transmitta omne requestas a vostre ver fornitor de hospitation. Illos ha mesmo vadite al tribunal pro proteger iste structura. Nos pote dunque usar los como un altere strata de cache e protection. Cloudflare non accepta pagamentos anonyme, assi nos pote solmente usar lor plano gratuite. Isto significa que nos non pote usar lor functiones de balanceamento de carga o failover. Nos dunque <a %(annas_archive_l255)s>implementava isto nos mesme</a> al nivello de dominio. Al carga del pagina, le navigator verificara si le dominio currente es ancora disponibile, e si non, illo re-scribe tote le URLs a un altere dominio. Pois que Cloudflare cache multe paginas, isto significa que un usator pote atterrar in nostre dominio principal, mesmo si le servitor proxy es giu, e alora al proxime clic esser transferite a un altere dominio. Nos ancora ha etiam concernas operational normal a tractar, como monitorar le sanitate del servitores, registrar errores de backend e frontend, e assi via. Nostre architectura de failover permitte plus de robustessa in iste fronto etiam, per exemplo per executar un setto completemente differente de servitores in un del dominios. Nos pote mesmo executar versiones plus vetule del codice e datasets in iste dominio separate, in caso que un bug critic in le version principal passa inobservate. Nos pote etiam proteger nos contra Cloudflare se revoltar contra nos, per remover lo de un del dominios, como iste dominio separate. Differente permutationes de iste ideas es possibile. Conclusion Il ha essite un experientia interessante apprender como establir un motor de recerca pro bibliotheca umbra robuste e resilient. Il ha multe plus de detalios a divider in postages futur, dunque face me saper lo que vos volerea apprender plus! Como semper, nos cerca donationes pro supportar iste labor, dunque assecurate vos de visitar le pagina de Donationes in le Archivo de Anna. Nos etiam cerca altere typos de supporto, como subventiones, sponsores a longe termino, fornitore de pagamento a alte risco, forsan mesmo annuncios (de bon gusto!). E si vos vole contribuer vostre tempore e habilitates, nos sempre cerca disveloppatores, traductores, etc. Gratias pro vostre interesse e supporto. Tokens de innovation Comencemos con nostre pila technologic. Illo es deliberatemente tediose. Nos usa Flask, MariaDB, e ElasticSearch. Literalmente es toto. Le recerca es in grande parte un problema resolvite, e nos non intende reinventa lo. In ultra, nos debe expender nostre <a %(mcfunley)s>tokens de innovation</a> in altere cosas: non esser abattite per le autoritates. Quanto legal o illegal es exactemente le Archivo de Anna? Isto depende principalmente del jurisdiction legal. Le major parte del paises crede in un forma de derecto de autor, lo que significa que personas o companias es assignate un monopolio exclusive super certe typos de obras pro un certe periodo de tempore. Como un parentehese, in le Archivo de Anna nos crede que, ben que il ha alcun beneficios, in general le derecto de autor es un negative net pro le societate — ma isto es un historia pro un altere occasion. Iste monopolio exclusive super certe obras significa que es illegal pro qualcunque foras de iste monopolio distribuer directemente iste obras — includente nos. Ma le Archivo de Anna es un motor de recerca que non distribue directemente iste obras (al minus non in nostre sito web clearnet), dunque nos deberea esser okay, ver? Non exactemente. In multe jurisdictiones non es solmente illegal distribuer obras con derecto de autor, ma etiam ligar a locos que lo face. Un exemplo classic de isto es le lege DMCA del Statos Unite. Isto es le extremitate plus stricta del spectro. Al altere extremitate del spectro poterea theoreticamente haber paises sin leges de derecto de autor, ma iste vermente non existe. Praticamente omne pais ha un forma de lege de derecto de autor in le libros. Le application es un altere historia. Il ha multe paises con governamentos que non se importa de applicar le lege de derecto de autor. Il ha etiam paises inter le duo extremos, que prohibe distribuer obras con derecto de autor, ma non prohibe ligar a tal obras. Un altere consideration es al nivello de compania. Si un compania opera in un jurisdiction que non se importa de derecto de autor, ma le compania mesme non es disposta a prender alcun risco, alora illos poterea clauder vostre sito web tan tosto como qualcunque se plange de illo. Finalmente, un grande consideration es le pagamentos. Pois que nos debe remaner anonyme, nos non pote usar methodos traditional de pagamento. Isto nos lassa con cryptocurrencies, e solmente un parve subset de companias supporta illos (il ha cartas de debito virtual pagate per crypto, ma illos es sovente non acceptate). - Anna e le equipa (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Io opera <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, le motor de recerca sin scopo lucrative open-source le plus grande del mundo pro <a %(wikipedia_shadow_library)s>bibliothecas umbra</a>, como Sci-Hub, Library Genesis, e Z-Library. Nostre objectivo es render le cognoscentia e cultura facilemente accessibile, e ultimamente a construir un communitate de personas qui insimul archiva e preserva <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tote le libros del mundo</a>. In iste articulo io monstrara como nos opera iste sito web, e le desafios unic que veni con operar un sito web con stato legal dubitabile, proque il non ha “AWS pro caritates umbra”. <em>Consulta tamben le articulo soror <a %(blog_how_to_become_a_pirate_archivist)s>Como devenir un archivista pirata</a>.</em> Como operar un bibliotheca umbra: operationes al Archivo de Anna Il non ha <q>AWS pro caritates umbra,</q> dunque como nos opera le Archivo de Anna? Instrumentos Servitor de application: Flask, MariaDB, ElasticSearch, Docker. Developmento: Gitlab, Weblate, Zulip. Gestion de servitores: Ansible, Checkmk, UFW. Hospedage static de Onion: Tor, Nginx. Servitor proxy: Varnish. Vamos vider qual instrumentos nos usa pro accomplir toto isto. Isto es multo in evolution como nos incontrara nove problemas e trova nove solutiones. Il ha alcun decisiones que nos ha reconsiderate plure vices. Un de illos es le communication inter servitores: nos usava Wireguard pro isto, ma trovava que illo occasionalmente cessa de transmitter alcun datos, o solmente transmitti datos in un direction. Isto occurreva con plure differentes configurationes de Wireguard que nos essayava, como <a %(github_costela_wesher)s>wesher</a> e <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Nos etiam essayava tunnelar portos via SSH, usante autossh e sshuttle, ma incontrava <a %(github_sshuttle)s>problemas ibi</a> (quamquam il es ancora non clar pro me si autossh suffre de problemas de TCP-super-TCP o non — illo solmente me pare un solution instabile ma forsan illo es vermente ben?). In vice, nos retornava a connexiones directe inter servitores, celante que un servitor es in function sur le fornitore a bon mercato usante IP-filtration con UFW. Isto ha le disavantage que Docker non functiona ben con UFW, a minus que vos usa <code>network_mode: "host"</code>. Tote isto es un poco plus propense a errores, proque vos expose vostre servitor al internet con solmente un parve misconfiguration. Forsan nos deberea retornar a autossh — retroaction esserea multo benvenite hic. Nos ha etiam reconsiderate Varnish contra Nginx. Nos actualmente prefere Varnish, ma illo ha su particularitates e margines aspre. Le mesme se applica a Checkmk: nos non lo ama, ma illo functiona pro ora. Weblate ha essite acceptabile ma non incredibile — io a vices time que illo perdera mi datos quando io tenta synchronisar lo con nostre repo git. Flask ha essite bon in general, ma illo ha alcun particularitates estranie que ha costate multo tempore pro debuggar, como configurar domines personalisate, o problemas con su integration de SqlAlchemy. Usque ora le altere instrumentos ha essite excellente: nos non ha reclamos seriose super MariaDB, ElasticSearch, Gitlab, Zulip, Docker, e Tor. Tote iste ha habite alcun problemas, ma nihil troppo seriose o que consuma multo tempore. Communitate Le prime desafio pote esser un surprendente. Il non es un problema technic, o un problema legal. Il es un problema psychologic: facer iste labor in le umbras pote esser incredibilemente solitari. Dependente de lo que vos plana facer, e vostre modello de menacia, vos poterea deber esser multo cautelose. A un extremitate del spectro nos ha personas como Alexandra Elbakyan*, le fundator de Sci-Hub, qui es multo aperte super su activitates. Ma illa es a alte risco de esser arrestate si illa visitarea un pais occidental a iste puncto, e poterea affrontar decennios de tempore in prision. Es isto un risco que vos volerea prender? Nos es al altere extremitate del spectro; essente multo cautelose de non lassar ulle tracia, e habente forte securitate operational. * Como mentionate in HN per "ynno", Alexandra initialmente non voleva esser cognoscite: "Su servitores esseva configurate pro emitter messages de error detalite de PHP, includente le cammino complete del file de origine fallente, que esseva sub le directorio /home/<USER>" Assi, usa nomines de usator aleatori in le computatores que vos usa pro iste cosas, in caso que vos configura mal alcun cosa. Tamen, iste secretessa veni con un costo psychologic. Le majoritate del personas ama esser recognoscite pro le labor que illes face, e tamen vos non pote prender ulle credito pro isto in vita real. Mesmo cosas simple pote esser un desafio, como amicos demandante vos que vos ha facite recentemente (a un certe puncto "jocar con mi NAS / homelab" deveni vetule). Es pro isto que es tanto importante trovar un communitate. Vos pote renunciar a un poco de securitate operational confidante in alcun amicos multo proxime, qui vos sape que vos pote confider profundemente. Mesmo alora sia cautelose de non poner alique in scriptura, in caso que illes debe ceder lor emails al autoritates, o si lor dispositivos es compromise in alicun altere maniera. Melior ancora es trovar alcun altere piratas. Si vostre amicos proxime es interessate in junger se a vos, excellente! Altrimenti, vos poterea trovar alteres online. Tristemente isto es ancora un communitate niche. Usque ora nos ha trovate solmente un pauc de alteres qui es active in iste spatio. Bon locos de initio pare esser le foros de Library Genesis, e r/DataHoarder. Le Equipa de Archivo ha etiam individuos con mentalitate simile, ben que illes opera intra le lege (mesmo si in alcun areas gris del lege). Le scenas traditional de "warez" e piratage ha etiam personas qui pensa in manieras simile. Nos es aperte a ideas de como fomentar communitate e explorar ideas. Senti te libere de inviar nos un message in Twitter o Reddit. Forsan nos poterea hospitar un sorta de foro o gruppo de chat. Un desafio es que isto pote facilemente esser censurate quando usante plataformas commun, assi nos deberea hospitar lo nos mesme. Il ha etiam un compromisso inter haber iste discussion totalmente public (plus potential de engagement) versus facer lo private (non lassar potential "objectivos" saper que nos es sur le puncto de raspar los). Nos deberea pensar super isto. Lassa nos saper si tu es interessate in isto! Conclusion Sperante que isto es utile pro archivistas pirata que comencia. Nos es excite de dar te le benvenita a iste mundo, assi non hesita a contactar nos. Vamos preservar tanto del cognoscentia e cultura del mundo como nos pote, e replicar lo largemente. Projectos 4. Selection de datos Sovente tu pote usar le metadata pro determinar un subgrupo rationabile de datos a discargar. Mesmo si tu eventualmente vole discargar tote le datos, il pote esser utile prioritizar le elementos le plus importante primo, in caso que tu es detectate e le defension es meliorate, o proque tu haberea besonio de comprar plus discos, o simplemente proque alicun altere cosa occurre in tu vita ante que tu pote discargar toto. Per exemplo, un collection pote haber multiple editiones del mesme recurso de base (como un libro o un film), ubi un es marcate como essente de melior qualitate. Salvar iste editiones primo haberea multo senso. Tu eventualmente poterea voler salvar tote le editiones, pois que in alcun casos le metadata poterea esser incorrectemente etiquettate, o poterea haber compromissos incognite inter editiones (per exemplo, le "melior edition" poterea esser le melior in multe manieras ma pejor in altere, como un film habente un resolution plus alte ma sin subtitulos). Tu pote etiam cercar in tu base de datos de metadata pro trovar cosas interessante. Qual es le file le plus grande que es hospitate, e proque es illo tanto grande? Qual es le file le plus parve? Esque il ha patronos interessante o inexpectate quando il se tracta de certe categorias, linguas, etc.? Esque il ha titulos duplicate o multo simile? Esque il ha patronos de quando le datos esseva addite, como un die in le qual multe files esseva addite a un vice? Tu sovente pote apprender multo per regardar le dataset in differentes manieras. In nostre caso, nos deduplicava libros de Z-Library contra le hashes md5 in Library Genesis, salvante assi multo tempore de discargamento e spatio de disco. Isto es un situation bastante unic tamen. In le major parte del casos, il non ha bases de datos comprensive de qual files es ja preservate adequatemente per altere piratas. Isto in se mesme es un grande opportunitate pro alicuno ibi. Seria fantastic haber un vista general regularmente actualisate de cosas como musica e filmes que es ja largemente seminate in sitos de torrent, e es ergo de minor prioritate a includer in speculos pirate. 6. Distribution Tu ha le datos, assi te dando possession del prime speculo pirate del mundo de tu objectivo (probabilemente). In multe manieras le parte le plus difficile es finite, ma le parte le plus riscante es ancora ante te. Post toto, usque ora tu ha essite furtive; volante sub le radar. Tote lo que tu debeva facer esseva usar un bon VPN durante, non completar tu detalios personal in alcun formularios (duh), e forsan usar un session de navigator special (o mesmo un computator differente). Ora tu debe distribuir le datos. In nostre caso nos primo voleva contribuir le libros retro a Library Genesis, ma postea discoperiva rapidemente le difficultates in illo (fiction versus non-fiction classification). Assi nos decideva super distribution usante torrents in le stilo de Library Genesis. Si tu ha le opportunitate de contribuir a un projecto existente, alora illo poterea salvar te multo tempore. Tamen, il non ha multe speculos pirate ben organisate ibi actualmente. Assi vamos dicer que tu decide super distribuir torrents te mesme. Prova mantener iste files parve, assi illos es facile a specular in altere sitos web. Tu deberea alora seminate le torrents te mesme, durante que ancora resta anonyme. Tu pote usar un VPN (con o sin port forwarding), o pagar con Bitcoins mesclate pro un Seedbox. Si tu non sape lo que alcun de iste terminos significa, tu habera un gruppo de lectura a facer, pois que es importante que tu comprende le compromissos de risco hic. Tu pote hospitar le files de torrent mesme in sitos de torrent existente. In nostre caso, nos seligeva realmente hospitar un sito web, pois que nos voleva etiam diffunder nostre philosophia in un maniera clar. Tu pote facer isto te mesme in un maniera simile (nos usa Njalla pro nostre dominios e hospitation, pagate con Bitcoins mesclate), ma etiam senti te libere de contactar nos pro que nos hospita tu torrents. Nos es cercante a construir un indice comprensive de speculos pirate con le tempore, si iste idea prende. Quanto al selection de VPN, multo ha essite scribite super isto ja, assi nos simplemente repetera le consilio general de seliger per reputation. Politicas de non-log testate in tribunal con longos historicos de proteger le privatia es le option de minor risco, in nostre opinion. Nota que mesmo quando tu face tote lo correcte, tu nunquam pote arrivar a zero risco. Per exemplo, quando tu semina tu torrents, un actor de stato-nation multo motivate poterea probabilemente regardar le fluxos de datos entrante e sortiente pro servitores VPN, e deducer qui tu es. O tu poterea simplemente facer un error in alicun maniera. Nos probabilemente ja ha facite, e lo facera de novo. Fortunatemente, le statos-nation non se importa <em>tanto</em> super pirateria. Un decision a facer pro cata projecto, es si publicar lo usante le mesme identitate como antea, o non. Si tu continua usar le mesme nomine, alora errores in securitate operational de projectos anterior poterea retornar a morder te. Ma publicar sub nomines differente significa que tu non construe un reputation plus durabile. Nos seligeva haber forte securitate operational ab le initio assi nos pote continuar usar le mesme identitate, ma nos non hesitara a publicar sub un nomine differente si nos face un error o si le circumstantias lo require. Diffunder le parola pote esser difficile. Como nos diceva, isto es ancora un communitate niche. Nos originalmente postava in Reddit, ma realmente obteneva traction in Hacker News. Pro ora nostre recommendation es postar lo in alcun locos e vider lo que occurre. E de novo, contacta nos. Nos amarea diffunder le parola de plus effortios de archivismo pirate. 1. Selection de dominio / philosophia Il non ha penuria de cognoscentia e hereditage cultural a salvar, lo que pote esser opprimente. Es pro isto que il es spesso utile prender un momento e pensar super qual pote esser tu contribution. Cata persona ha un maniera differente de pensar super isto, ma hic es alcun questiones que tu poterea demandar a te mesme: In nostre caso, nos se importava in particular super le preservation a longe termino del scientia. Nos sapeva super Library Genesis, e como illo esseva plenmente mirate multe vices usante torrents. Nos amava iste idea. Alora un die, un de nos essayava trovar alcun libros scientific in Library Genesis, ma non poteva trovar los, ponente in dubita quanto complete illo vermente esseva. Nos alora cercava iste libros online, e trovava los in altere locos, lo que plantava le semine pro nostre projecto. Mesmo ante que nos sapeva super le Z-Library, nos habeva le idea de non tentar colliger tote iste libros manualmente, ma de focalisar super mirroring de collectiones existente, e contributor los retro a Library Genesis. Qual habilitates tu ha que tu pote usar a tu beneficio? Per exemplo, si tu es un experto in securitate online, tu pote trovar manieras de superar blocos de IP pro objectivos secur. Si tu es excellente a organisar communitates, alora forsitan tu pote rallyar alcun personas insimul circa un objectivo. Es utile saper un poco de programmation tamen, mesmo si solmente pro mantener bon securitate operational durante iste processo. Qual esserea un area de alte-leva a focalisar? Si tu va passar X horas in archivar pirata, alora como tu pote obtener le plus grande "bang pro tu buck"? Qual es manieras unic que tu pensa super isto? Tu poterea haber alcun ideas o approches interessante que alteres poterea haber mancate. Quanto tempore tu ha pro isto? Nostre consilio esserea de comenciar parve e facer projectos plus grande como tu prende le habito, ma illo pote devenir toto-consumante. Proque es tu interessate in isto? De que es tu appassionate? Si nos pote haber un gruppo de personas que omnes archiva le sorta de cosas que illes specificamente se importa, illo coperirea multo! Tu sapera multo plus que le persona medie super tu passion, como qual es le datos importante a salvar, qual es le melior collectiones e communitates online, e assi via. 3. Raspamento de metadata Data addite/modificate: assi tu pote retornar plus tarde e discargar files que tu non discargava antea (ben que tu pote sovente tamben usar le ID o hash pro isto). Hash (md5, sha1): pro confirmar que tu ha discargate le file correctemente. ID: pote esser un ID interne, ma IDs como ISBN o DOI es utile tamben. Nomine de file / location Description, categoria, etiquettas, autores, lingua, etc. Dimension: pro calcular quanto spatio de disco tu necessita. Vamos devenir un poco plus technic ci. Pro vermente raspar le metadata de sitos web, nos ha mantenite cosas bastante simple. Nos usa scriptos Python, a vices curl, e un base de datos MySQL pro immagazinar le resultatos. Nos non ha usate alicun software de raspamento sofisticate que pote cartographiar sitos web complexe, pois que usque ora nos solmente necessitava raspar un o duo typos de paginas per simplemente enumerar per id e analysar le HTML. Si il non ha paginas facilemente enumerabile, alora tu poterea necessitar un ver rastrellator que tenta trovar tote le paginas. Ante que tu comencia a raspar un integre sito web, essaya de facer lo manualmente per un poco. Percurre un pauc de paginas tu mesme, pro obtener un senso de como illo functiona. A vices tu ja incontrara blocos IP o altere comportamentos interessante in iste maniera. Le mesme vale pro raspamento de datos: ante de plonger troppo profunde in iste objectivo, assecurate que tu pote vermente discargar su datos efficacemente. Pro circumvenir restrictiones, il ha alcun cosas que tu pote essayar. Esque il ha altere adressas IP o servitores que hospita le mesme datos ma non ha le mesme restrictiones? Esque il ha punctos de accesso API que non ha restrictiones, durante que alteres ha? A qual velocitate de discargamento tu IP es blocate, e pro quanto tempore? O esque tu non es blocate ma retardate? Que si tu crea un conto de usator, como cambia le cosas alora? Pote tu usar HTTP/2 pro mantener le connexiones aperte, e isto augmenta le velocitate al qual tu pote requestar paginas? Esque il ha paginas que lista multiple files a un vice, e le information listate ibi es sufficiente? Cosas que tu probabilemente vole immagazinar include: Nos typicamente face isto in duo stages. Primo nos discarga le files HTML crude, usualmente directemente in MySQL (pro evitar multe files parve, del qual nos parla plus infra). Alora, in un passo separate, nos percurre ille files HTML e los analysa in tabulas MySQL real. In iste maniera tu non debe re-discargar toto ab initio si tu discoperi un error in tu codice de analysa, pois que tu pote simplemente reprocessar le files HTML con le nove codice. Es tamben sovente plus facile de parallelisar le passo de processar, assi salvante un poco de tempore (e tu pote scriber le codice de processar durante que le raspamento es in curso, in vice de deber scriber ambe passos a un vice). Finalmente, nota que pro alcun objectivos, le extraction de metadata es toto lo que il ha. Il ha alcun collectiones massive de metadata que non es preservate adequatemente. Titulo Selection de dominio / philosophia: Ubi tu approximativemente vole focalisar, e proque? Qual es tu passiones, habilitates, e circumstantias unic que tu pote usar a tu beneficio? Selection de objectivo: Qual collection specific tu mirara? Raspar metadata: Catalogar information super le files, sin vermente discargar le files (spesso multo plus grande) mesme. Selection de datos: Basate super le metadata, restringer qual datos es le plus relevante a archivar ora. Poterea esser toto, ma spesso il ha un maniera rationabile de salvar spatio e banda. Raspar datos: Vermente obtener le datos. Distribution: Emballar lo in torrents, annunciar lo alicubi, facer personas diffunder lo. 5. Extraction de datos Ora tu es preste a discargar realmente le datos in massa. Como mentionate antea, a iste puncto tu deberea ja haber discargate manualmente un gruppo de files, pro melior comprender le comportamento e restrictiones del objectivo. Tamen, il habera ancora surprisas in reserva pro te quando tu realmente comencia a discargar multe files a un vice. Nostre consilio hic es principalmente mantener lo simple. Comencia per simplemente discargar un gruppo de files. Tu pote usar Python, e postea extender a multiple filamentos. Ma a vices mesmo plus simple es generar files Bash directemente del base de datos, e postea executar multiple de illos in multiple fenestras de terminal pro escalar. Un rapide astutia technic que vale mentionar hic es usar OUTFILE in MySQL, que tu pote scriber ubique si tu disactiva "secure_file_priv" in mysqld.cnf (e assecurar te de etiam disactivar/superar AppArmor si tu es in Linux). Nos immagazina le datos in discos dur simple. Comencia con lo que tu ha, e expande lentemente. Il pote esser opprimente pensar a immagazinar centos de TBs de datos. Si isto es le situation que tu affronta, simplemente publica un bon subgrupo primo, e in tu annuncio pete adjuta pro immagazinar le resto. Si tu vole obtener plus discos dur te mesme, alora r/DataHoarder ha alcun bon recursos pro obtener bon ofertas. Prova non preoccupante te troppo super systemas de files sofisticate. Il es facile cader in le cuniculo de configurar cosas como ZFS. Un detalio technic a esser conscie tamen, es que multe systemas de files non gestiona ben multe files. Nos ha trovate que un simple solution es crear multiple directorios, p.ex. pro differente intervallos de ID o prefixos de hash. Post discargar le datos, assecurar te de verificar le integritate del files usante hashes in le metadata, si disponibile. 2. Selection de objectivo Accessibile: non usa multe stratos de protection pro impedir te de raspar lor metadata e datos. Insight special: tu ha alcun information special super iste objectivo, como si tu ha accesso special a iste collection, o tu ha discoperite como superar lor defension. Isto non es requirite (nostre projecto imminente non face alique special), ma illo certemente adjuta! Grande Allora, nos ha nostre area que nos es examinante, ora qual collection specific nos debe reflecter? Il ha alcun cosas que face un bon objectivo: Quando nos trovava nostre libros de texto scientific in sitos web altere que Library Genesis, nos essayava de comprender como illos faceva lor via al internet. Nos trovava alora le Z-Library, e realisava que durante que le major parte del libros non appare primo ibi, illos finalmente fini ibi. Nos apprendeva super su relation a Library Genesis, e le structura de incentivo (financial) e le interfacie de usator superior, ambes faceva illo un collection multo plus complete. Nos faceva alora alcun raspamento preliminar de metadata e datos, e realisava que nos poteva circumvenir lor limites de download IP, utilisante le accesso special de un de nostre membros a multe servitores proxy. Durante que tu explora differentes objectivos, es ja importante de celar tu tracias per usar VPNs e adressas de email jettable, del qual nos parlara plus tarde. Unique: non ja ben coperite per altere projectos. Quando nos face un projecto, illo ha un par de phases: Iste non es phases completemente independente, e spesso perceptiones de un phase ulterior te retorna a un phase anterior. Per exemplo, durante le raspar de metadata tu poterea realisar que le objectivo que tu ha seligite ha mechanismos de defensa ultra tu nivello de habilitate (como blocos de IP), assi tu retorna e trova un objectivo differente. - Anna e le equipa (<a %(reddit)s>Reddit</a>) Libros integre pote esser scribite super le <em>proque</em> del preservation digital in general, e del archivismo pirata in particular, ma permitte nos dar un breve introduction pro illes qui non es multo familiar. Le mundo produce plus cognoscentia e cultura que jammais antea, ma etiam plus de illo es perdite que jammais antea. Le humanitate confide in grande parte a corporationes como editoras academic, servitores de diffusion, e companias de medios social con iste hereditage, e illes sovente non ha probite esser grande custodes. Verifica le documental Amnesia Digital, o vermente qualcunque presentation per Jason Scott. Il ha alcun institutiones que face un bon labor archivar tanto como illes pote, ma illes es ligate per le lege. Como piratas, nos es in un position unic pro archivar collectiones que illes non pote tanger, a causa del application del derectos de autor o altere restrictiones. Nos pote etiam specular collectiones multe vices, trans le mundo, augmentante assi le chances de un preservation appropriate. Pro ora, nos non entrara in discussiones super le pros e contras del proprietate intellectual, le moralitate de infringer le lege, reflexiones super le censura, o le questione de accesso al cognoscentia e cultura. Con toto isto foras del cammino, vamos immerger in le <em>como</em>. Nos compartira como nostre equipa deveniva archivistas pirata, e le lectiones que nos apprendeva al longo del cammino. Il ha multe desafios quando on se embarca in iste viage, e sperabilemente nos pote adjutar vos a transversar alcunos de illos. Como devenir un archivista pirata Le prime desafio pote esser un surprendente. Il non es un problema technic, o un problema legal. Il es un problema psychologic. Ante que nos immerge, duo actualisationes super le Speculo de Bibliotheca Pirata (EDIT: movite a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>): Nos recipeva alcun donationes extrememente generose. Le prime esseva $10k de un individuo anonymo qui ha etiam supportate "bookwarrior", le fundator original de Library Genesis. Gratias special a bookwarrior pro facilitar iste donation. Le secunde esseva un altere $10k de un donator anonymo, qui entrava in contacto post nostre ultime emission, e esseva inspirate a adjutar. Nos etiam recipeva un numero de donationes minor. Gratias multissimo pro tote vostre supporto generose. Nos ha alcun excitante nove projectos in le pipeline que isto supportara, dunque remane alert. Nos habeva alcun difficultates technic con le dimension de nostre secunde emission, ma nostre torrents es ora active e seminante. Nos etiam recipeva un offerta generose de un individuo anonymo pro seminar nostre collection in lor servitores de velocitate multo alte, dunque nos face un carga special a lor machinas, post le qual omnes altere qui sta discargante le collection deberea vider un grande melioration in velocitate. Postages de blog Salute, io es Anna. Io creava <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, le plus grande bibliotheca umbra del mundo. Isto es mi blog personal, in le qual io e mi collegas scribe super pirateria, preservation digital, e plus. Connecte con me sur <a %(reddit)s>Reddit</a>. Nota que iste sito web es solmente un blog. Nos solmente hospeda nostre proprie parolas hic. Nulle torrents o altere files con derectos de autor es hospitate o ligate hic. <strong>Bibliotheca</strong> - Como le major parte del bibliothecas, nos nos concentra primarimente super materiales scribite como libros. Nos poterea expander in altere typos de media in le futuro. <strong>Speculo</strong> - Nos es strictemente un speculo de bibliothecas existente. Nos nos concentra super preservation, non super facer libros facilemente cercabile e descargabile (accesso) o fomentar un grande communitate de personas que contribue nove libros (sourcing). <strong>Pirata</strong> - Nos delibera violar le lege de copyright in le major parte del paises. Isto nos permitte facer qualcosa que entitates legal non pote facer: assecurar que libros es replicate largemente. <em>Nos non liga al files ab iste blog. Per favor trova lo tu mesme.</em> - Anna e le equipa (<a %(reddit)s>Reddit</a>) Iste projecto (EDIT: transferite a <a %(wikipedia_annas_archive)s>Le Archivo de Anna</a>) ha como scopo contribuir al preservation e liberation del cognoscentia human. Nos face nostre parve e humile contribution, sequente le passos del grandes ante nos. Le foco de iste projecto es illustrate per su nomine: Le prime bibliotheca que nos ha speculate es Z-Library. Isto es un bibliotheca popular (e illegal). Illes ha prendite le collection de Library Genesis e facite lo facilemente cercabile. In ultra, illes ha devenite multo effective a sollicitar nove contributiones de libros, per incentivar usatores contributive con varie beneficios. Illes actualmente non contribue iste nove libros retro a Library Genesis. E a differente de Library Genesis, illes non face lor collection facilemente replicabile, lo que impedi un large preservation. Isto es importante pro lor modello de negocio, pois que illes demanda pecunia pro acceder a lor collection in massa (plus de 10 libros per die). Nos non face judicios moral super demandar moneta pro accesso in massa a un collection illegal de libros. Es sin dubita que le Z-Library ha habite successo in expander accesso al cognoscentia, e in obtener plus libros. Nos es simplemente hic pro facer nostre parte: garantir le preservation a longe termino de iste collection private. Nos volerea invitar te a adjutar a preservar e liberar le cognoscentia human per discargar e semenar nostre torrents. Vide le pagina del projecto pro plus information super como le datos es organisate. Nos etiam multo te invita a contribuir tu ideas pro qual collectiones specular in le futuro, e como proceder. Inseme nos pote attinger multo. Isto es solmente un parve contribution inter inumerabile alteres. Gratias, pro toto lo que tu face. Presentante le Speculo del Bibliotheca Pirata: Preservante 7TB de libros (que non es in Libgen) 10% of le hereditage scribite del humanitate preservate pro semper <strong>Google.</strong> Después de todo, hicieron esta investigación para Google Books. Sin embargo, su metadata no es accesible en masa y es bastante difícil de extraer. <strong>Varios sistemas de bibliotecas individuales y archivos.</strong> Hay bibliotecas y archivos que no han sido indexados y agregados por ninguno de los anteriores, a menudo porque están subfinanciados, o por otras razones no quieren compartir sus datos con Open Library, OCLC, Google, etc. Muchas de estas tienen registros digitales accesibles a través de internet, y a menudo no están muy bien protegidos, por lo que si deseas ayudar y divertirte aprendiendo sobre sistemas de bibliotecas extraños, estos son excelentes puntos de partida. <strong>ISBNdb.</strong> Este es el tema de esta publicación de blog. ISBNdb extrae datos de varios sitios web para metadata de libros, en particular datos de precios, que luego venden a libreros, para que puedan fijar el precio de sus libros de acuerdo con el resto del mercado. Dado que los ISBN son bastante universales hoy en día, efectivamente construyeron una “página web para cada libro”. <strong>Open Library.</strong> Como se mencionó antes, esta es toda su misión. Han obtenido enormes cantidades de datos de bibliotecas de bibliotecas cooperantes y archivos nacionales, y continúan haciéndolo. También tienen bibliotecarios voluntarios y un equipo técnico que están tratando de deduplicar registros y etiquetarlos con todo tipo de metadata. Lo mejor de todo es que su conjunto de datos es completamente abierto. Puedes simplemente <a %(openlibrary)s>descargarlo</a>. <strong>WorldCat.</strong> Este es un sitio web administrado por la organización sin fines de lucro OCLC, que vende sistemas de gestión de bibliotecas. Agregan metadata de libros de muchas bibliotecas y la ponen a disposición a través del sitio web de WorldCat. Sin embargo, también ganan dinero vendiendo estos datos, por lo que no están disponibles para descarga masiva. Tienen algunos conjuntos de datos masivos más limitados disponibles para descargar, en cooperación con bibliotecas específicas. 1. Pro alcun definition rationabile de "sempre". ;) 2. Naturalmente, le hereditage scribite del humanitate es multo plus que libros, specialmente hodie. Pro le scopo de iste articulo e nostre publicationes recente nos nos concentra super libros, ma nostre interesses se extende plus longe. 3. Il ha multo plus que pote esser dicite super Aaron Swartz, ma nos voleva solmente mentionar le brevemente, pois que ille joca un parte pivotal in iste historia. Como le tempore passa, plus de personas poterea incontrar su nomine pro le prime vice, e poterea subsequente plonjar in le cuniculo de conilio per se mesme. <strong>Copias físicas.</strong> Obviamente esto no es muy útil, ya que son solo duplicados del mismo material. Sería genial si pudiéramos preservar todas las anotaciones que las personas hacen en los libros, como los famosos “garabatos en los márgenes” de Fermat. Pero, por desgracia, eso seguirá siendo un sueño de archivista. <strong>“Ediciones”.</strong> Aquí cuentas cada versión única de un libro. Si algo sobre él es diferente, como una portada diferente o un prólogo diferente, cuenta como una edición diferente. <strong>Archivos.</strong> Al trabajar con bibliotecas en la sombra como Library Genesis, Sci-Hub o Z-Library, hay una consideración adicional. Puede haber múltiples escaneos de la misma edición. Y las personas pueden hacer mejores versiones de archivos existentes, escaneando el texto usando OCR o corrigiendo páginas que fueron escaneadas en ángulo. Queremos contar estos archivos solo como una edición, lo que requeriría un buen metadata o deduplicación usando medidas de similitud de documentos. <strong>“Obras”.</strong> Por ejemplo, “Harry Potter y la Cámara Secreta” como un concepto lógico, abarcando todas sus versiones, como diferentes traducciones y reimpresiones. Esta es una definición algo útil, pero puede ser difícil trazar la línea de lo que cuenta. Por ejemplo, probablemente queramos preservar diferentes traducciones, aunque las reimpresiones con solo diferencias menores podrían no ser tan importantes. - Anna e le equipa (<a %(reddit)s>Reddit</a>) Con le Speculo del Bibliotheca Pirata (EDIT: transferite a <a %(wikipedia_annas_archive)s>Le Archivo de Anna</a>), nostre scopo es prender tote le libros in le mundo, e preservar los pro semper.<sup>1</sup> Inter nostre torrents de Z-Library, e le torrents original de Library Genesis, nos ha 11,783,153 files. Ma quante es isto, vermente? Si nos deduplicava propriemente iste files, qual percento de tote le libros in le mundo ha nos preservate? Nos vermente volerea haber qualcosa como isto: Comencemos con algunos números aproximados: En tanto Z-Library/Libgen como Open Library hay muchos más libros que ISBN únicos. ¿Significa eso que muchos de esos libros no tienen ISBN, o simplemente falta el metadata de ISBN? Probablemente podamos responder a esta pregunta con una combinación de coincidencia automatizada basada en otros atributos (título, autor, editor, etc.), incorporando más fuentes de datos y extrayendo ISBN de los propios escaneos de libros (en el caso de Z-Library/Libgen). ¿Cuántos de esos ISBN son únicos? Esto se ilustra mejor con un diagrama de Venn: Para ser más precisos: Nos surprendeva quanto pauc overlap existe! ISBNdb ha un grande numero de ISBNs que non appare ni in Z-Library ni in Open Library, e lo mesme es ver (a un grado minor ma ancora substantial) pro le altere duo. Isto leva a multe nove questiones. Quanto poterea le correspondentia automatizate adjutar in etiquettar le libros que non esseva etiquettate con ISBNs? Haberea multe correspondencias e dunque un augmento de overlap? Anque, que occurre si nos introduce un 4te o 5te dataset? Quanto overlap viderem nos alora? Isto nos da un puncto de initio. Nos pote ora examinar tote le ISBNs que non esseva in le dataset de Z-Library, e que non corresponde al campos de titulo/autor. Isto pote dar nos un manico pro preservar tote le libros del mundo: primo per raspar le internet pro scans, alora per sortir in le vita real pro scannar libros. Le ultime poterea mesmo esser financiato per le multitudine, o guidate per "recompensas" de personas que volerea vider libros particular digitalisate. Tote isto es un historia pro un altere tempore. Si vos vole adjutar con alicuno de isto — ulteriore analyse; raspar plus de metadata; trovar plus de libros; OCR de libros; facer isto pro altere dominios (p.ex. articulos, audiolibros, filmes, emissiones de television, revistas) o mesmo facer disponibile alicun de iste datos pro cosas como le entrainamento de modelos de linguage large / ML — per favor contacta me (<a %(reddit)s>Reddit</a>). Si vos es specificamente interessate in le analyse de datos, nos labora pro facer nostre Datasets e scriptos disponibile in un formato plus facile a usar. Seria fantastic si vos poterea simplemente forcar un notebook e comenciar a luder con isto. Finalmente, si vos vole supportar iste labor, per favor considera facer un donation. Isto es un operation completemente gestionate per voluntarios, e vostre contribution face un grande differentia. Cata poco adjuta. Pro ora nos accepta donationes in crypto; vide le pagina de Donationes in le Archivo de Anna. Pro un percento, nos necessita un denominatore: le numero total de libros jammais publicate.<sup>2</sup> Ante le fin de Google Books, un ingeniero del projecto, Leonid Taycher, <a %(booksearch_blogspot)s>essayava estimar</a> iste numero. Ille arrivava — con humor — a 129,864,880 (“al minus usque a dominica”). Ille estimava iste numero per construir un base de datos unificate de tote le libros in le mundo. Pro isto, ille colligava differente datasets e postea los fusionava in varie manieras. Como un breve aparte, hay otra persona que intentó catalogar todos los libros del mundo: Aaron Swartz, el fallecido activista digital y cofundador de Reddit.<sup>3</sup> Él <a %(youtube)s>inició Open Library</a> con el objetivo de “una página web para cada libro jamás publicado”, combinando datos de muchas fuentes diferentes. Terminó pagando el precio más alto por su trabajo de preservación digital cuando fue procesado por descargar masivamente artículos académicos, lo que llevó a su suicidio. No hace falta decir que esta es una de las razones por las que nuestro grupo es seudónimo y por qué estamos siendo muy cuidadosos. Open Library sigue siendo heroicamente gestionada por personas en el Internet Archive, continuando el legado de Aaron. Volveremos a esto más adelante en esta publicación. En la publicación del blog de Google, Taycher describe algunos de los desafíos al estimar este número. Primero, ¿qué constituye un libro? Hay algunas definiciones posibles: Las “Ediciones” parecen la definición más práctica de lo que son los “libros”. Convenientemente, esta definición también se utiliza para asignar números ISBN únicos. Un ISBN, o Número Estándar Internacional de Libros, se utiliza comúnmente para el comercio internacional, ya que está integrado con el sistema internacional de códigos de barras (“Número Internacional de Artículo”). Si deseas vender un libro en tiendas, necesita un código de barras, por lo que obtienes un ISBN. La publicación del blog de Taycher menciona que, si bien los ISBN son útiles, no son universales, ya que solo se adoptaron realmente a mediados de los setenta, y no en todo el mundo. Aun así, el ISBN es probablemente el identificador más utilizado de ediciones de libros, por lo que es nuestro mejor punto de partida. Si podemos encontrar todos los ISBN del mundo, obtenemos una lista útil de qué libros aún necesitan ser preservados. Entonces, ¿de dónde obtenemos los datos? Hay varios esfuerzos existentes que están tratando de compilar una lista de todos los libros del mundo: En esta publicación, nos complace anunciar un pequeño lanzamiento (en comparación con nuestros lanzamientos anteriores de Z-Library). Extraímos la mayor parte de ISBNdb y pusimos los datos a disposición para torrenting en el sitio web de Pirate Library Mirror (EDIT: movido a <a %(wikipedia_annas_archive)s>Archivo de Anna</a>; no lo enlazaremos aquí directamente, solo búscalo). Estos son alrededor de 30.9 millones de registros (20GB como <a %(jsonlines)s>JSON Lines</a>; 4.4GB comprimidos). En su sitio web afirman que en realidad tienen 32.6 millones de registros, por lo que podríamos haber perdido algunos de alguna manera, o <em>ellos</em> podrían estar haciendo algo mal. En cualquier caso, por ahora no compartiremos exactamente cómo lo hicimos — dejaremos eso como un ejercicio para el lector. ;-) Lo que compartiremos es un análisis preliminar, para tratar de acercarnos a estimar el número de libros en el mundo. Observamos tres conjuntos de datos: este nuevo conjunto de datos de ISBNdb, nuestra publicación original de metadata que extraímos de la biblioteca en la sombra Z-Library (que incluye Library Genesis), y el volcado de datos de Open Library. Dump de ISBNdb, o Quante Libros Es Preservate Pro Sempre? Si nos deberea deduplicar propriemente le files de bibliothecas umbra, qual percento de tote le libros in le mundo ha nos preservate? Actualisationes super <a %(wikipedia_annas_archive)s>Archivo de Anna</a>, le plus grande bibliotheca vermente aperte in le historia human. <em>Redesign de WorldCat</em> Datos <strong>Formato?</strong> <a %(blog)s>Contenitores de Archivo de Anna (AAC)</a>, que es essentialmente <a %(jsonlines)s>Lineas JSON</a> compresse con <a %(zstd)s>Zstandard</a>, plus alcun semanticas standardisate. Iste contenitores involucra varie typos de registros, basate super le differente rascamentos que nos displicava. Un anno retro, nos <a %(blog)s>comenciava</a> a responder a iste question: <strong>Qual es le percento de libros que ha essite permanentemente preservate per bibliothecas umbra?</strong> Vamos reguardar alcun information basic super le datos: Un vice que un libro entra in un bibliotheca umbra de datos aperte como <a %(wikipedia_library_genesis)s>Library Genesis</a>, e ora <a %(wikipedia_annas_archive)s>le Archivo de Anna</a>, illo es reflectite per tote le mundo (via torrents), preservante lo practicemente pro semper. Pro responder al question de qual percento de libros ha essite preservate, nos necessita saper le denominatore: quanto libros existe in total? E idealmente nos non solmente ha un numero, ma vermente metadata. Alora nos pote non solmente comparar los contra bibliothecas umbra, ma etiam <strong>crear un lista de libros restante a preservar!</strong> Nos poterea mesmo comenciar a soniar de un effortio de collaboration pro ir a basso iste lista. Nos rascavamos <a %(wikipedia_isbndb_com)s>ISBNdb</a>, e discargava le <a %(openlibrary)s>dataset de Open Library</a>, ma le resultatos esseva insatisfactori. Le problema principal esseva que il non habeva multe superposition de ISBNs. Vide iste diagramma de Venn de <a %(blog)s>nostre articulo de blog</a>: Nos esseva multo surprendite per quanto pauc superposition il habeva inter ISBNdb e Open Library, ambes del quales include liberalmente datos de varie fontes, como rascamentos web e registros de bibliothecas. Si ambes face un bon labor a trovar le major parte del ISBNs existente, lor circulos certemente haberea un substantial superposition, o un esserea un subgrupo del altere. Isto nos faceva demandar, quante libros cade <em>completemente foras de iste circulos</em>? Nos necessita un base de datos plus grande. Es quando nos fixava nostre vistas sur le plus grande base de datos de libros in le mundo: <a %(wikipedia_worldcat)s>WorldCat</a>. Isto es un base de datos proprietari per le organisation non-profit <a %(wikipedia_oclc)s>OCLC</a>, que agrega registros de metadata de bibliothecas de tote le mundo, in cambio de dar a illas accesso al dataset complete, e facer los apparer in le resultatos de recerca del usatores final. Mesmo si OCLC es un non-profit, lor modello de negocio require proteger lor base de datos. Ben, nos es dispiacite a dicer, amicos de OCLC, nos lo da via completemente. :-) Durante le ultime anno, nos ha rascate meticulosamente tote le registros de WorldCat. Al initio, nos habeva un colpo de fortuna. WorldCat justo comenciava a displicar lor complete redesign de sito web (in aug 2022). Isto includeva un substantial renovation de lor systemas de backend, introducente multe fallas de securitate. Nos immediatemente seiziava le opportunitate, e esseva capace de rascar centos de milliones (!) de registros in solmente alcun dies. Postea, le fallas de securitate esseva lentemente fixate un per un, usque al ultime que nos trovava esseva corrigite circa un mense retro. A ille tempore nos habeva quasi tote le registros, e solmente cercava pro registros de qualitate un poco plus alte. Assi nos sentiva que es tempore de liberar los! 1.3B Raspamento de WorldCat <em><strong>TL;DR:</strong> Le Archivo de Anna raspava tote WorldCat (le plus grande collection de metadata de bibliotheca del mundo) pro facer un lista de libros que necessita esser preservate.</em> WorldCat Advertimento: iste articulo de blog ha essite deprecate. Nos ha decidite que IPFS non es ancora preste pro le prime tempore. Nos ancora ligara a files in IPFS ab le Archivo de Anna quando possibile, ma nos non lo hostara plus nos mesme, ni recommendara a alteres de specular usante IPFS. Per favor vide nostre pagina de Torrentes si tu vole adjutar a preservar nostre collection. Adjuta a semenar Z-Library in IPFS Descarga de servidor partner SciDB Presta externe Presta externe (disabilitate pro impression) Descarga externe Explora metadata Contenite in torrents Retro  (+%(num)s bonus) non pagate pagate cancellate expirate expectante confirmation de Anna invalid Le texto infra continua in anglese. Ir Reinitialisar Avanti Ultime Si tu adresse de email non functiona in le foros de Libgen, nos recommenda usar <a %(a_mail)s>Proton Mail</a> (gratuite). Vos pote etiam <a %(a_manual)s>requestar manualmente</a> que tu conto sia activate. (pote requirer <a %(a_browser)s>verification de navigator</a> — discargas illimitate!) Servidor Partner Rapide #%(number)s (recommendate) (un poco plus rapide ma con lista de attesa) (nulle verification de navigator requirite) (nulle verification de navigator o listas de attesa) (nulle lista de attesa, ma pote esser multo lente) Servidor Partner Lente #%(number)s Audiolibro Libro de comic Libro (fiction) Libro (non-fiction) Libro (incognite) Articulo de jornal Revista Partitura musical Altere Documento de standards Non tote le paginas poteva esser convertite a PDF Marcate como rupte in Libgen.li Non visibile in Libgen.li Non visibile in Libgen.rs Fiction Non visibile in Libgen.rs Non-Fiction Execution de exiftool falliva sur iste file Marcate como “mal file” in Z-Library Absent de Z-Library Marcate como “spam” in Z-Library File non pote esser aperite (p.ex. file corrumpite, DRM) Reclamation de derectos de autor Problemas de descargo (p.ex. non pote connecter, message de error, multo lente) Metadata incorrecte (ex. titulo, description, imagine de copertura) Altere Qualitate basse (p.ex. problemas de formatation, qualitate de scansion basse, paginas mancante) Spam / file debe esser removite (p.ex. publicitate, contento abusive) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brillante Bibliophilo Fortunose Bibliothecario Deslumbrante Colector de Datos Incredibile Archivista Descargas de bonus Cerlalc Metadatos chec DuXiu Indice de eBook EBSCOhost Google Libros Goodreads HathiTrust IA IA Prunte Digital Controlate ISBNdb ISBN GRP Libgen.li Excludente “scimag” Libgen.rs Non-Fiction e Fiction Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Bibliotheca Estatal Russe Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploades a Anna’s Archive Z-Library Z-Library Chinese Titulo, autor, DOI, ISBN, MD5, … Cercar Autor Description e commentos de metadatos Edition Nomine original del file Editor (cercar campo specific) Titulo Anno publicate Detalios technic Iste moneta ha un minimo plus alte que usual. Per favor selige un differente duration o un differente moneta. Le requesta non poteva esser completate. Per favor reproba in alcun minutas, e si illo continua a occurrer contacta nos a %(email)s con un screenshot. Un error incognite occurreva. Per favor contacta nos a %(email)s con un screenshot. Error in le processament de pagamento. Per favor attende un momento e reproba. Si le problema persiste per plus de 24 horas, per favor contacta nos a %(email)s con un screenshot. Nos es currentemente facente un collecte de fundos pro <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">sustener</a> le plus grande bibliotheca de comics in le mundo. Gratias pro tu supporto! <a href="/donate">Dona.</a> Si tu non pote donar, considera supportar nos per parlar a tu amicos, e sequer nos in <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>. Non invia nos emails pro <a %(a_request)s>requerer libros</a><br>o parve (<10k) <a %(a_upload)s>incargamentos</a>. Le Archivo de Anna DMCA / reclamos de copyright Resta in contacto Reddit Alternativas SLUM (%(unaffiliated)s) non-affiliate Le Archivo de Anna necessita tu adjuta! Si tu dona ora, tu recipe <strong>duple</strong> le numero de descargas rapide. Multes prova de nos abatter, ma nos lucta de retorno. Si tu dona iste mense, tu recipe <strong>duple</strong> le numero de downloads rapide. Valide usque al fin de iste mense. Salveguardar le cognoscentia human: un grande dono de ferias! Le abonnementos essera extendite correspondentemente. Servitores de partner es indisponibile a causa de clausuras de hospitage. Illos deberea esser de novo disponibile tosto. Pro augmentar le resilientia del Archivo de Anna, nos cerca voluntarios pro operar reflectores. Nos ha un nove methodo de donation disponibile: %(method_name)s. Per favor considera %(donate_link_open_tag)sdonar</a> — mantener iste sito web non es cheap, e tu donation vermente face un differentia. Multo gratias. Refera un amico, e ambe vos e vostre amico recipe %(percentage)s%% discargas rapide de bonus! Surprende un car persona, da les un conto con adhesion. Le regalo perfecte pro San Valentino! Sape plus… Conto Activitate Avansate Blog de Anna ↗ Le Software de Anna ↗ beta Explorator de Codices Datasets Dona Files discargate FAQ Domo Meliorar metadata Datos de LLM Aperir session / Registrar Mi donationes Profilo public Cercar Securitate Torrentes Traducer ↗ Voluntariato & Recompensas Descargas recente: 📚&nbsp;Le plus grande bibliotheca de datos aperte e de codice aperte del mundo. ⭐️&nbsp;Reflecte Sci-Hub, Library Genesis, Z-Library, e plus. 📈&nbsp;%(book_any)s libros, %(journal_article)s articulos, %(book_comic)s comics, %(magazine)s revistas — preservate pro semper.  e  e plus DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Le plus grande bibliotheca vermente aperte in le historia human. 📈&nbsp;%(book_count)s&nbsp;libros, %(paper_count)s&nbsp;articulos — preservate pro semper. ⭐️&nbsp;Nos reflecte %(libraries)s. Nos scrapa e open-source %(scraped)s. Tote nostre codice e datos es completemente open source. OpenLib Sci-Hub ,  📚 Le plus grande bibliotheca de datos aperte e de codice aperte del mundo.<br>⭐️ Reflecte Scihub, Libgen, Zlib, e plus. Z-Lib Le Archivo de Anna Requesta invalide. Visita %(websites)s. Le plus grande bibliotheca de datos aperte e de fonte aperte del mundo. Reflecte Sci-Hub, Library Genesis, Z-Library, e plus. Cerca in le Archivo de Anna Le Archivo de Anna Per favor refrasca pro provar de novo. <a %(a_contact)s>Contacta nos</a> si le problema persiste per plure horas. 🔥 Problema cargante iste pagina <li>1. Seque nos in <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, o <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Diffunde le parola super le Archivo de Anna in Twitter, Reddit, Tiktok, Instagram, in tu cafe o bibliotheca local, o ubi tu vade! Nos non crede in le custodia de portales — si nos es clausurate, nos simplemente reappare alicubi altere, pois tote nostre codice e datos es completemente open source.</li><li>3. Si tu pote, considera <a href="/donate">donar</a>.</li><li>4. Adjuta <a href="https://translate.annas-software.org/">traducer</a> nostre sito web in differente linguas.</li><li>5. Si tu es un ingeniero de software, considera contribuir a nostre <a href="https://annas-software.org/">open source</a>, o semenar nostre <a href="/datasets">torrents</a>.</li> 10. Crea o adjuta a mantener le pagina de Wikipedia pro Anna’s Archive in tu lingua. 11. Nos cerca placer parve, tasteful annuncios. Si vos vole annunciar in Anna’s Archive, per favor informa nos. 6. Si tu es un ricercator de securitate, nos pote usar tu habilitates pro ambe offensa e defension. Verifica nostre pagina de <a %(a_security)s>Securitate</a>. 7. Nos cerca expertos in pagos pro mercantes anonyme. Pote tu adjutar nos adder plus de manieras convenibile pro donar? PayPal, WeChat, cartas de dono. Si tu cognosce alicuno, per favor contacta nos. 8. Nos es semper in cerca de plus capacitate de servitores. 9. Vos pote adjutar reportante problemas de files, lassante commentarios, e creando listas directemente in iste sito web. Vos pote etiam adjutar <a %(a_upload)s>incargante plus libros</a>, o corrigente problemas de files o formattation de libros existente. Pro informationes plus extensive super como voluntariar, vide nostre pagina <a %(a_volunteering)s>Voluntariato & Recompensas</a>. Nos crede fortemente in le fluxo libere de information, e in le preservation del cognoscentia e cultura. Con iste motor de recerca, nos construye super le spatulas de gigantes. Nos ha un profunde respecto pro le duro labor del personas qui ha create le varie bibliothecas umbra, e nos spera que iste motor de recerca ampliara lor alcance. Pro remaner actualisate super nostre progresso, seque Anna in <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> o <a href="https://t.me/annasarchiveorg">Telegram</a>. Pro questiones e retroaction, per favor contacta Anna a %(email)s. ID del conto: %(account_id)s Disconnexion ❌ Alique errava. Per favor recarica le pagina e reproba. ✅ Tu es ora disconectate. Recarica le pagina pro reconnecter te. Downloads rapide usate (ultime 24 horas): <strong>%(used)s / %(total)s</strong> Adhesion: <strong>%(tier_name)s</strong> usque %(until_date)s <a %(a_extend)s>(extender)</a> Tu pote combinar multiple membros (downloads rapide per 24 horas essera addite insimul). Adhesion: <strong>Nulle</strong> <a %(a_become)s>(devenir membro)</a> Contacta Anna a %(email)s si tu es interessate in actualisar tu membro a un nivel superior. Profilo public: %(profile_link)s Clave secret (non divider!): %(secret_key)s monstrar Junge nos hic! Actualiza a un <a %(a_tier)s>nivel superior</a> pro junger nostre gruppo. Grupo exclusive in Telegram: %(link)s Conto qual downloads? Connexion Non perde tu clave! Clave secret invalide. Verifica tu clave e reproba, o alternativemente registra un nove conto infra. Clave secret Entra tu clave secret pro connecter te: Vetule conto basate in email? Entra tu <a %(a_open)s>email hic</a>. Registrar nove conto Non ha ancora un conto? Registration succedite! Tu clave secret es: <span %(span_key)s>%(key)s</span> Salva iste clave con cura. Si tu lo perde, tu perdera accesso a tu conto. <li %(li_item)s><strong>Marcapagina.</strong> Tu pote marcar iste pagina pro recuperar tu clave.</li><li %(li_item)s><strong>Discarga.</strong> Clicca <a %(a_download)s>iste ligamine</a> pro discargar tu clave.</li><li %(li_item)s><strong>Manager de contrasignos.</strong> Usa un manager de contrasignos pro salvar le clave quando tu lo entra infra.</li> Aperir session / Registrar Verification del navigator Advertentia: le codice ha characteres Unicode incorrecte, e pote comportar se incorrectemente in varie situationes. Le binario bruto pote esser decodificate ab le representation base64 in le URL. Description Etichetta Prefixo URL pro un codice specific Sito web Codices que comencia con “%(prefix_label)s” Per favor non raspar iste paginas. In vice, nos recommenda <a %(a_import)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB, e executar nostre <a %(a_software)s>codice open source</a>. Le datos brutos pote esser explorate manualmente per files JSON como <a %(a_json_file)s>iste</a>. Menos de %(count)s registros URL generic Explorator de Codices Indice de Explora le codices con le quales le registros es etiquettate, per prefixo. Le columna “registros” monstra le numero de registros etiquettate con codices con le prefixo date, como vidite in le motor de recerca (incluse registros solmente de metadatos). Le columna “codices” monstra quante codices actual ha un prefixo date. Prefixo de codice cognoscite “%(key)s” Plus… Prefixo %(count)s registro correspondente a “%(prefix_label)s” %(count)s registros correspondente a “%(prefix_label)s” codices registros “%%s” essera substitute con le valor del codice Cercar in le Archivo de Anna Codices URL pro codice specific: “%(url)s” Iste pagina pote prender un poco de tempore pro generar, lo que es le ration proque illo require un captcha de Cloudflare. <a %(a_donate)s>Membros</a> pote saltar le captcha. Abuso reportate: Melior version Desira vos reportar iste usator pro comportamento abusive o inappropriate? Problema de file: %(file_issue)s commento celate Responder Reportar abuso Vos ha reportate iste usator pro abuso. Reclamos de derectos de autor a iste email essera ignorate; usa le formulario in vice. Monstrar email Nos multo apprecia tu retroaction e questiones! Tamen, a causa del quantitate de spam e emails sin senso que nos recipe, per favor marca le cassetta pro confirmar que tu comprende iste conditiones pro contactar nos. Altere vias de contactar nos super reclamos de derectos de autor essera automaticamente delite. Pro reclamos de DMCA / derectos de autor, usa <a %(a_copyright)s>iste formulario</a>. Email de contacto URLs en el Archivo de Anna (requerido). Una por línea. Por favor, solo incluya URLs que describan exactamente la misma edición de un libro. Si desea hacer un reclamo para múltiples libros o múltiples ediciones, por favor envíe este formulario varias veces. Los reclamos que agrupen múltiples libros o ediciones serán rechazados. Dirección (requerido) Descripción clara del material fuente (requerido) Correo electrónico (requerido) URLs del material fuente, una por línea (requerido). Por favor, incluya tantas como sea posible, para ayudarnos a verificar su reclamo (por ejemplo, Amazon, WorldCat, Google Books, DOI). ISBNs del material fuente (si aplica). Uno por línea. Por favor, solo incluya aquellos que coincidan exactamente con la edición para la cual está reportando un reclamo de derechos de autor. Su nombre (requerido) ❌ Algo salió mal. Por favor, recargue la página e intente nuevamente. ✅ Gracias por enviar su reclamo de derechos de autor. Lo revisaremos lo antes posible. Por favor, recargue la página para enviar otro. <a %(a_openlib)s>Open Library</a> URLs del material fuente, uno por línea. Por favor, tómese un momento para buscar su material fuente en Open Library. Esto nos ayudará a verificar su reclamo. Número de teléfono (requerido) Declaración y firma (requerido) Enviar reclamo Si tiene un reclamo de DMCA u otro reclamo de derechos de autor, por favor complete este formulario con la mayor precisión posible. Si encuentra algún problema, contáctenos en nuestra dirección dedicada a DMCA: %(email)s. Tenga en cuenta que los reclamos enviados por correo electrónico a esta dirección no serán procesados, es solo para preguntas. Por favor, use el formulario a continuación para enviar sus reclamos. Formulario de reclamo de DMCA / Derechos de autor Exemplo de registro in Anna’s Archive Torrentes per Anna’s Archive Formato de Contenitores de Anna’s Archive Scriptos pro importar metadata Si vos es interessate in mirroring iste dataset pro <a %(a_archival)s>archivage</a> o pro <a %(a_llm)s>scopo de entramento de LLM</a>, per favor contacta nos. Ultime actualisation: %(date)s Sito principal %(source)s Documentation de Metadata (le major parte de campos) Files mirroreate per Anna’s Archive: %(count)s (%(percent)s%%) Resources Total de files: %(count)s Dimension total de files: %(size)s Nostre poste de blog super iste datos <a %(duxiu_link)s>Duxiu</a> es un massive base de datos de libros scannate, create per le <a %(superstar_link)s>SuperStar Digital Library Group</a>. Le major parte es libros academic, scannate pro render los disponibile digitalmente a universitates e bibliothecas. Pro nostre publico anglo-parlante, <a %(princeton_link)s>Princeton</a> e le <a %(uw_link)s>Universitate de Washington</a> ha bon summarios. Il ha etiam un excellente articulo que da plus de contexto: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Le libros de Duxiu ha essite longemente pirate in le internet chinese. Usualmente illos es vendite per minus de un dollar per revendedores. Illos es typicamente distribuite usante le equivalente chinese de Google Drive, que sovente ha essite hackate pro permitter plus de spatio de immagazinage. Alcun detalios technic se trova <a %(link1)s>hic</a> e <a %(link2)s>hic</a>. Ben que le libros ha essite semi-publicamente distribuite, es bastante difficile obtener los in massa. Nos habeva isto alte in nostre lista de cosas a facer, e allocava plure menses de labor a plen tempore pro isto. Tamen, in le fin de 2023 un voluntario incredibile, stupefaciente, e talentose nos contactava, dicente que ille habeva ja facite tote iste labor — a grande costo. Ille partiva le collection integre con nos, sin expectar nihil in retorno, excepte le garantia de preservation a longe termino. Vermente remarcabile. Plus information de nostre voluntarios (notationes crude): Adaptate de nostre <a %(a_href)s>poste de blog</a>. DuXiu 读秀 %(count)s file %(count)s files Este conjunto de datos está estrechamente relacionado con el <a %(a_datasets_openlib)s>conjunto de datos de Open Library</a>. Contiene una recopilación de todos los metadatos y una gran parte de los archivos de la Biblioteca de Préstamo Digital Controlado de IA. Las actualizaciones se publican en el <a %(a_aac)s>formato de Contenedores del Archivo de Anna</a>. Estos registros se refieren directamente al conjunto de datos de Open Library, pero también contienen registros que no están en Open Library. También tenemos varios archivos de datos recopilados por miembros de la comunidad a lo largo de los años. La colección consta de dos partes. Necesita ambas partes para obtener todos los datos (excepto los torrents reemplazados, que están tachados en la página de torrents). Bibliotheca Digital de Prestito nostre prime publication, ante que nos standardisava in le <a %(a_aac)s>Formato de Contenitores de Anna’s Archive (AAC)</a>. Contine metadata (como json e xml), pdfs (ab systemas de prestito digital acsm e lcpdf), e miniaturas de coperaturas. nove publicationes incremental, usante AAC. Contine solmente metadata con marcas temporal post 2023-01-01, pois le resto es ja coperite per “ia”. Anque tote le files pdf, iste vice ab le systemas de prestito acsm e “bookreader” (le lector web de IA). Nonobstante le nomine non es exactemente correcte, nos ancora populava files de bookreader in le collection ia2_acsmpdf_files, pois illos es mutualemente exclusive. IA Controlled Digital Lending 98%%+ de files es recercabile. Nostre mission es archivar tote le libros del mundo (como etiam articulos, revistas, etc.), e facer los largemente accessibile. Nos crede que tote le libros deberea esser mirrorrate largemente, pro garantir redundantia e resiliencia. Isto es proque nos colligente files ab un varietate de fontes. Alcun fontes es completemente aperte e pote esser mirrorrate in massa (como Sci-Hub). Altere es claudite e protecte, assi nos essaya raspar los pro “liberar” lor libros. Altere cade alicubi intermedie. Tote nostre datos pote esser <a %(a_torrents)s>torrente</a>, e tote nostre metadata pote esser <a %(a_anna_software)s>generate</a> o <a %(a_elasticsearch)s>discargate</a> como bases de datos ElasticSearch e MariaDB. Le datos crude pote esser manualmente explorate per files JSON como <a %(a_dbrecord)s>isto</a>. Metadatos Sito web de ISBN Ultime actualisation: %(isbn_country_date)s (%(link)s) Resursos Le International ISBN Agency publica regularmente le intervallos que illo ha allocate a agencias national de ISBN. De isto nos pote derivar a qual pais, region, o gruppo de linguas pertine iste ISBN. Nos actualmente usa iste datos indirectemente, per le bibliotheca Python <a %(a_isbnlib)s>isbnlib</a>. Information de paises de ISBN Isto es un dump de multe appellos a isbndb.com durante septembre 2022. Nos essayava coperir tote le intervallos de ISBN. Iste es circa 30.9 milliones de registros. In lor sito web illes asserta que illes ha actualemente 32.6 milliones de registros, assi nos poterea haber mancate alcunes, o <em>illes</em> poterea facer qualcosa incorrecte. Le responsas JSON es quasi crude ab lor servitor. Un problema de qualitate de datos que nos notava, es que pro numeros ISBN-13 que comencia con un prefixo differente de “978-”, illes ancora include un campo “isbn” que simplemente es le numero ISBN-13 con le prime 3 numeros removite (e le digito de controlo recalculato). Isto es obviemente incorrecte, ma isto es como illes sembla facer lo, assi nos non lo alterava. Un altere problema potential que vos poterea incontrar, es le facto que le campo “isbn13” ha duplicatos, assi vos non pote usar lo como un clave primari in un base de datos. Le campos “isbn13”+“isbn” combinate pare esser unic. Version 1 (2022-10-31) Torrents de fiction es in retard (ben que IDs ~4-6M non torrenteate desde que illos se superpone con nostre torrents de Zlib). Nostre articulo de blog super le publication de libros de comics Torrentos de comics in le Archivo de Anna Pro le historia del differente forcos de Library Genesis, vide le pagina pro <a %(a_libgen_rs)s>Libgen.rs</a>. Le Libgen.li contine le majoritate del mesme contento e metadatos como le Libgen.rs, ma ha alcun collectiones in plus de isto, nominatemente comics, revistas, e documentos standard. Illo ha tamben integrate <a %(a_scihub)s>Sci-Hub</a> in su metadatos e motor de recerca, que es lo que nos usa pro nostre base de datos. Le metadatos pro iste bibliotheca es libere disponibile <a %(a_libgen_li)s>a libgen.li</a>. Tamen, iste servitor es lente e non supporta le resumition de connexiones interrumpite. Le mesme files es tamben disponibile in <a %(a_ftp)s>un servitor FTP</a>, que functiona melior. Non-fiction pare sembla diverger, ma sin nove torrents. Illo pare que isto ha occurrite desde le initio de 2022, ben que nos non ha verificate isto. Secundo le administrator de Libgen.li, le collection “fiction_rus” (fiction russe) deberea esser coperite per torrents regularmente publicate de <a %(a_booktracker)s>booktracker.org</a>, notabilemente le torrents de <a %(a_flibusta)s>flibusta</a> e <a %(a_librusec)s>lib.rus.ec</a> (que nos specula <a %(a_torrents)s>hic</a>, ben que nos non ha ancora determinate quales torrents corresponde a quales files). Le collection de fiction ha su proprie torrents (divergente de <a %(a_href)s>Libgen.rs</a>) comenciante a %(start)s. Certe intervallos sin torrents (como le intervallos de fiction f_3463000 a f_4260000) es probabilemente files de Z-Library (o altere duplicatos), ben que nos poterea voler facer un deduplication e crear torrents pro files unice a lgli in iste intervallos. Statisticas pro tote le collectiones pote esser trovate <a %(a_href)s>in le sito de libgen</a>. Torrents es disponibile pro le major parte del contento addicional, notabilemente torrents pro libros comic, revistas, e documentos standard ha essite publicate in collaboration con le Archivo de Anna. Nota que le files de torrent referente a “libgen.is” es explicitemente speculos de <a %(a_libgen)s>Libgen.rs</a> (“.is” es un dominio differente usate per Libgen.rs). Un recurso utile pro usar le metadata es <a %(a_href)s>iste pagina</a>. %(icon)s Lor collection “fiction_rus” (fiction russe) non ha torrents dedicate, ma es coperite per torrents de alteres, e nos mantene un <a %(fiction_rus)s>speculo</a>. Torrents de fiction russe in le Archivo de Anna Torrentos de fiction in le Archivo de Anna Foro de discussion Metadata Metadata via FTP Torrentos de revistas in le Archivo de Anna Information de campo de metadata Speculo de altere torrentos (e torrentos unic de fiction e comics) Torrents de documentos standard in le Archivo de Anna Libgen.li Torrents per le Archivo de Anna (coperturas de libros) Library Genesis es cognoscite pro ja generose facer lor datos disponibile in massa via torrents. Nostre collection de Libgen consiste de datos auxiliar que illes non publica directemente, in collaboration con illes. Multo gratias a omnes implicate con Library Genesis pro laborar con nos! Nostre blog super le publication de coperturas de libros Iste pagina es super le version “.rs”. Illo es cognoscite pro publicar consistentemente tanto su metadata como le contento integre de su catalogo de libros. Su collection de libros es dividite inter un parte de fiction e un parte de non-fiction. Un recurso utile pro usar le metadata es <a %(a_metadata)s>iste pagina</a> (bloca intervallos de IP, VPN pote esser requirite). A partir de 2024-03, nove torrents es publicate in <a %(a_href)s>iste filo de foro</a> (bloca intervallos de IP, VPN pote esser necessari). Torrents de fiction in le Archivo de Anna Torrents de fiction de Libgen.rs Foro de discussion de Libgen.rs Metadata de Libgen.rs Information de campos de metadata de Libgen.rs Torrents de non-fiction de Libgen.rs Torrents de non-fiction in le Archivo de Anna %(example)s pro un libro de fiction. Iste <a %(blog_post)s>prime publication</a> es bastante parve: circa 300GB de coperturas de libros del bifurcation Libgen.rs, tanto fiction como non-fiction. Illos es organisate in le mesme maniera que illos appare in libgen.rs, p. ex.: %(example)s pro un libro de non-fiction. Exactemente como con le collection de Z-Library, nos los pone tote in un grande file .tar, que pote esser montate usante <a %(a_ratarmount)s>ratarmount</a> si vos vole servir le files directemente. Publication 1 (%(date)s) Le breve historia del differente forcos de Library Genesis (o “Libgen”), es que con le tempore, le differente personas implicate con Library Genesis habeva un disaccordo, e se separava. Secundo iste <a %(a_mhut)s>poste in le foro</a>, Libgen.li esseva originalmente hospitate a “http://free-books.dontexist.com”. Le version “.fun” esseva create per le fundador original. Illo es essente renovate in favor de un nove version plus distribuite. Le <a %(a_li)s>version “.li”</a> ha un massive collection de comics, assi como altere contento, que non es (ancora) disponibile pro download massive via torrentos. Illo ha un collection de torrentos separate de libros de fiction, e contine le metadata de <a %(a_scihub)s>Sci-Hub</a> in su base de datos. Le version “.rs” ha datos multo simile, e publica su collection in torrentos massive de maniera consistente. Illo es approximativemente dividite in un section de “fiction” e un section de “non-fiction”. Originalmente a “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> in alcun senso es etiam un forco de Library Genesis, ben que illes usava un nomine differente pro lor projecto. Libgen.rs Nos etiam arricchisce nostre collection con fontes de solmente metadatos, que nos pote associar a files, p. ex. usante numeros ISBN o altere campos. Infra es un vista general de illos. De novo, alcun de iste fontes es completemente aperte, durante que pro alteres nos debe raspar los. Nota que in le recerca de metadatos, nos monstra le registros original. Nos non face alcun fusion de registros. Fontes de solmente metadatos Open Library es un projecto open source per le Internet Archive pro catalogar cata libro in le mundo. Illo ha un del plus grande operationes de scannar libros in le mundo, e ha multe libros disponibile pro prestito digital. Su catalogo de metadata de libros es liberemente disponibile pro download, e es includite in le Archivo de Anna (ben que non actualmente in le recerca, excepte si vos explicitemente cerca un ID de Open Library). Open Library Excludente duplicatos Ultime actualisation Percentages del numero de files %% reflectite per AA / torrents disponibile Dimension Fonte Infra es un vista general rapide del fontes del files in le Archivo de Anna. Como le bibliothecas de umbra sovente synchronisa datos de unes al alteres, il ha considerable superposition inter le bibliothecas. Es pro isto que le numeros non sume al total. Le percentage “reflectite e seminate per Anna’s Archive” monstra quante files nos reflecte nos mesme. Nos semina iste files in massa per torrents, e los rende disponibile pro download directe per sitos web de partenarios. Vista general Total Torrents in le Archivo de Anna Pro le informationes de fundo super Sci-Hub, per favor consulta su <a %(a_scihub)s>sito official</a>, <a %(a_wikipedia)s>pagina de Wikipedia</a>, e iste <a %(a_radiolab)s>intervista in podcast</a>. Nota que Sci-Hub ha essite <a %(a_reddit)s>congelate desde 2021</a>. Illo esseva congelate antea, ma in 2021 un pauc de milliones de articulos esseva addite. Totevia, un numero limitate de articulos es ancora addite al collectiones “scimag” de Libgen, ben que non bastante pro justificar nove torrents in massa. Nos usa le metadata de Sci-Hub como fornite per <a %(a_libgen_li)s>Libgen.li</a> in su collection “scimag”. Nos tamben usa le dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Nota que le torrents “smarch” es <a %(a_smarch)s>deprecate</a> e ergo non includite in nostre lista de torrents. Torrents in Libgen.li Torrents in Libgen.rs Metadata e torrents Actualisationes in Reddit Intervista in podcast Pagina de Wikipedia Sci-Hub Sci-Hub: congelate desde 2021; le major parte disponibile per torrents Libgen.li: additiones minor desde alora</div> Alcuni bibliothecas fonte promove le compartimento massive de lor datos per medio de torrents, durante que alteres non comparti lor collectiones facilmente. In iste ultime caso, le Archivo de Anna tenta raspar lor collectiones e render los disponibile (vide nostre pagina de <a %(a_torrents)s>Torrents</a>). Il ha etiam situationes intermedie, per exemplo, ubi le bibliothecas fonte es disposte a compartir, ma non ha le recursos pro facer lo. In iste casos, nos etiam tenta adjutar. Infra es un vista general de como nos interfacia con le differente bibliothecas fonte. Bibliothecas fonte %(icon)s Vari variate bases de datos de files disperse in internet chines; ben que sovente bases de datos pagate %(icon)s Le major parte del files es solmente accessibile con contos premium de BaiduYun; velocitates de discargamento lente. %(icon)s Le Archivo de Anna gestiona un collection de <a %(duxiu)s>files DuXiu</a> %(icon)s Variate bases de datos de metadata dispergite in le internet chinese; ben que sovente bases de datos pagate %(icon)s Nulle dumpes de metadata facilemente accessibile disponibile pro lor integre collection. %(icon)s Le Archivo de Anna gestiona un collection de <a %(duxiu)s>metadata de DuXiu</a> Files %(icon)s Files solmente disponibile pro prestar in un maniera limitate, con varie restrictiones de accesso %(icon)s Le Archivo de Anna gestiona un collection de <a %(ia)s>files de IA</a> %(icon)s Alcun metadata disponibile per <a %(openlib)s>dumpes de base de datos de Open Library</a>, ma illos non coperi le integre collection de IA %(icon)s Nulle dumpes de metadata facilemente accessibile disponibile pro lor integre collection %(icon)s Le Archivo de Anna gestiona un collection de <a %(ia)s>metadata de IA</a> Ultime actualisation %(icon)s Le Archivo de Anna e Libgen.li gerenti collaborative collectiones de <a %(comics)s>libros comic</a>, <a %(magazines)s>revistas</a>, <a %(standarts)s>documentos standard</a>, e <a %(fiction)s>fiction (divergite de Libgen.rs)</a>. %(icon)s Torrentes de non-fiction es partagiate con Libgen.rs (e mirroreate <a %(libgenli)s>ci</a>). %(icon)s Dumpes de base de datos HTTP trimestral <a %(dbdumps)s>ci</a> %(icon)s Torrents automate pro <a %(nonfiction)s>Non-Fiction</a> e <a %(fiction)s>Fiction</a> %(icon)s Le Archivo de Anna gestiona un collection de <a %(covers)s>torrentes de copertura de libros</a> %(icon)s Dumpes quotidian de base de datos <a %(dbdumps)s>HTTP</a> Metadatos %(icon)s Dumpes de base de datos mensual <a %(dbdumps)s> </a> %(icon)s Torrentes de datos disponibile <a %(scihub1)s>ci</a>, <a %(scihub2)s>ci</a>, e <a %(libgenli)s>ci</a> %(icon)s Alcun nove files es <a %(libgenrs)s>essente</a> <a %(libgenli)s>addite</a> al “scimag” de Libgen, ma non bastante pro justificar nove torrentes %(icon)s Sci-Hub ha congelate nove files desde 2021. %(icon)s Dumpes de metadata disponibile <a %(scihub1)s>ci</a> e <a %(scihub2)s>ci</a>, como parte del <a %(libgenli)s>base de datos de Libgen.li</a> (que nos usa) Fonte %(icon)s Variate fontes minor o unic. Nos incoragia personas a cargar a altere bibliothecas umbra primo, ma a vices personas ha collectiones que es troppo grande pro alteres a ordinar, ben que non bastante grande pro justificar lor proprie categoria. %(icon)s Non disponibile directemente in massa, protegite contra raspatio %(icon)s Le Archivo de Anna gestiona un collection de <a %(worldcat)s>metadata OCLC (WorldCat)</a> %(icon)s Le Archivo de Anna e Z-Library gestiona collaborative un collection de <a %(metadata)s>metadata de Z-Library</a> e <a %(files)s>files de Z-Library</a> Datasets Nos combina tote le fontes supra in un sol base de datos unificate que nos usa pro servir iste sito web. Iste base de datos unificate non es disponibile directemente, ma pois que le Archivo de Anna es completemente open source, illo pote esser <a %(a_generated)s>generate</a> o <a %(a_downloaded)s>discargate</a> como bases de datos ElasticSearch e MariaDB. Le scriptos in ille pagina discargara automaticamente tote le metadatos necessari ab le fontes mentionate supra. Si vos vole explorar nostre datos ante executar iste scriptos localmente, vos pote regardar nostre files JSON, que liga ulteriormente a altere files JSON. <a %(a_json)s>Iste file</a> es un bon puncto de initio. Base de datos unificate Torrentes per Archivo de Anna navigar cercar Diverse fontes minor o unice. Nos incoragia personas a cargar a altere bibliothecas umbra primo, ma a vices personas ha collectiones que es troppo grande pro alteres a ordinar, ben que non bastante grande pro justificar lor proprie categoria. Vista general desde la <a %(a1)s>página de datasets</a>. De <a %(a_href)s>aaaaarg.fail</a>. Pare esser bastante complete. De nostre voluntario “cgiym”. Ex un torrent de <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Ha un superposition bastante alte con collectiones de articulos existente, ma multo pauc correspondencias de MD5, assi nos decideva retener lo completemente. Raspado de <q>iRead eBooks</q> (= fonéticamente <q>ai rit i-books</q>; airitibooks.com), por el voluntario <q>j</q>. Corresponde a la metadata de <q>airitibooks</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>. De una colección <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Parte de la fuente original, parte de the-eye.eu, parte de otros espejos. Ex un sito web private de torrent de libros, <a %(a_href)s>Bibliotik</a> (spisse referite como “Bib”), del qual libros esseva combinate in torrents per nomine (A.torrent, B.torrent) e distribuite per the-eye.eu. Ex nostre voluntario “bpb9v”. Pro plus information super <a %(a_href)s>CADAL</a>, vide le notas in nostre <a %(a_duxiu)s>pagina de datos de DuXiu</a>. Plus de nostre voluntario “bpb9v”, major parte files de DuXiu, assi como un dossier “WenQu” e “SuperStar_Journals” (SuperStar es le compania detra DuXiu). Ex nostre voluntario “cgiym”, textos chinese de diverse fontes (representate como subdirectorias), includente de <a %(a_href)s>China Machine Press</a> (un major editor chinese). Collectiones non-chinese (representate como subdirectorios) de nostre voluntario “cgiym”. Raspado de libros sobre arquitectura china, por el voluntario <q>cm</q>: <q>Lo obtuve explotando una vulnerabilidad de red en la editorial, pero esa brecha ya ha sido cerrada</q>. Corresponde a la metadata de <q>chinese_architecture</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>. Libros de casa editorial academic <a %(a_href)s>De Gruyter</a>, colligite de alcun grande torrents. Raspamento de <a %(a_href)s>docer.pl</a>, un sito polonese de compartimento de files focalisate in libros e altere obras scribite. Raspate in le fin de 2023 per voluntario “p”. Nos non ha bon metadata del sito original (nec mesmo extensiones de file), ma nos filtrava pro files simile a libros e sovente poteva extraher metadata del files mesme. DuXiu epubs, directemente de DuXiu, colligite per voluntario “w”. Solmente libros recente de DuXiu es disponibile directemente via ebooks, assi que le major parte de iste debe esser recente. Files DuXiu restante de voluntario “m”, que non esseva in le formato proprietari PDG de DuXiu (le principal <a %(a_href)s>dataset DuXiu</a>). Colligite de multe fontes original, malfortunatemente sin preservar ille fontes in le filepath. <span></span> <span></span> <span></span> Raspado de libros eróticos, por el voluntario <q>do no harm</q>. Corresponde a la metadata de <q>hentai</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>. <span></span> <span></span> Collection raspat de un editor de Manga japonese per voluntario “t”. <a %(a_href)s>Archivos judiciales selecte de Longquan</a>, fornite per voluntario “c”. Raspatio de <a %(a_href)s>magzdb.org</a>, un alliate de Library Genesis (illo es ligate in le pagina initial de libgen.rs) ma qui non voleva provider lor files directemente. Obtenite per voluntario “p” in le fin de 2023. <span></span> Variate parve cargas, troppo parve pro esser lor proprie subcollection, ma representate como directorios. Ebooks de AvaxHome, un sitio web ruso para compartir archivos. Archivo de periódicos y revistas. Corresponde a la metadata de <q>newsarch_magz</q> en <a %(a1)s><q>Otros raspados de metadata</q></a>. Raspado del <a %(a1)s>Centro de Documentación de Filosofía</a>. Collection de voluntario “o” qui colligiva libros polonese directemente de sitos de publication original (“scena”). Collectiones combinate de <a %(a_href)s>shuge.org</a> per voluntarios “cgiym” e “woz9ts”. <span></span> <a %(a_href)s>“Bibliotheca Imperial de Trantor”</a> (nominate post le bibliotheca fictional), raspate in 2022 per voluntario “t”. <span></span> <span></span> <span></span> Sub-sub-collectiones (representate como directorios) de voluntario “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (per <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, mi parve libreria — woz9ts: “Iste sito se concentra principalmente in compartir files de ebooks de alte qualitate, alcunes del quales es compaginate per le proprietario mesme. Le proprietario esseva <a %(a_arrested)s>arrestate</a> in 2019 e alicuno faceva un collection de files que ille compartiva.”). Files restante de DuXiu de voluntario “woz9ts”, que non esseva in le formato proprietari PDG de DuXiu (ancora a converter a PDF). Le collection “upload” es dividite in subcollectiones plus parve, que es indicate in le AACIDs e nomines de torrent. Tote le subcollectiones esseva primo deduplicate contra le collection principal, ben que le files JSON de metadata “upload_records” ancora contine multe referentias al files original. Files non-librari tamben esseva removite de multe subcollectiones, e es typicamente <em>non</em> notate in le “upload_records” JSON. Le subcollectiones es: Notas Subcolección Multes subcollectiones mesme es componite de sub-sub-collectiones (p.ex. de differente fontes original), que es representate como directorios in le campos “filepath”. Carga a Archivo de Anna Nostre articulo de blog super iste datos <a %(a_worldcat)s>WorldCat</a> es un base de datos proprietari per le organisation non-profit <a %(a_oclc)s>OCLC</a>, que agrega registros de metadata de bibliothecas de tote le mundo. Il es probabilemente le plus grande collection de metadata de bibliotheca in le mundo. In octobre 2023 nos <a %(a_scrape)s>publicava</a> un raspamento comprensive del base de datos OCLC (WorldCat), in le <a %(a_aac)s>Formato de Contenitores del Archivo de Anna</a>. Octobre 2023, version initial: OCLC (WorldCat) Torrents per le Archivo de Anna Exemplo de registro in Archivo de Anna (collection original) Exemplo de registro in Archivo de Anna (collection “zlib3”) Torrentes per Archivo de Anna (metadata + contento) Posta de blog super le Lancemento 1 Posto de blog super le Lancemento 2 In le fin de 2022, le presunte fundatores de Z-Library esseva arrestate, e dominios esseva confiscate per autoritates del Statos Unite. Desde alora le sito web ha lentemente reemergite online. Es incognite qui lo opera actualmente. Actualisation de februario 2023. Z-Library ha su radices in le communitate de <a %(a_href)s>Library Genesis</a>, e originalmente se initiava con lor datos. Desde alora, illo se ha professionalisate considerabilemente, e ha un interfacie multo plus moderne. Illes es ergo capabile de reciper multe plus donationes, tanto pecuniari pro continuar meliorar lor sito web, como donationes de nove libros. Illes ha accumulate un grande collection in addition a Library Genesis. Le collection consiste de tres partes. Le paginas de description original pro le prime duo partes es preservate infra. Tu necessita tote tres partes pro obtener tote le datos (excepto torrents superate, que es barrate in le pagina de torrents). %(title)s: nostre prime publication. Isto esseva le prime publication de lo que tunc esseva appellate le “Speculo de Bibliotheca Pirata” (“pilimi”). %(title)s: secunde publication, iste vice con tote le files involvite in files .tar. %(title)s: nove publicationes incremental, usante le <a %(a_href)s>Formato de Contenitores de Archivo de Anna (AAC)</a>, ora publicate in collaboration con le equipa de Z-Library. Le speculo initial esseva obtenite con grande efforto durante le curso de 2021 e 2022. A iste puncto illo es un poco obsolete: illo reflecte le stato del collection in junio 2021. Nos actualisara isto in le futuro. Ora nos es focalisate in liberar iste prime version. Como Library Genesis es ja preservate con torrents public, e es includite in le Z-Library, nos faceva un deduplication basic contra Library Genesis in junio 2022. Pro isto nos usava hashes MD5. Il es probabile que il ha multo plus contento duplicate in le bibliotheca, como multiple formatos de file con le mesme libro. Isto es difficile a detectar con precision, assi nos non lo face. Post le deduplication nos remane con plus de 2 milliones de files, totalisante justo sub 7TB. Le collection consiste de duo partes: un dump MySQL “.sql.gz” del metadata, e le 72 files torrent de circa 50-100GB cata uno. Le metadata contine le datos como reportate per le sito de Z-Library (titulo, autor, description, typologia de file), assi como le dimension real del file e le md5sum que nos observava, pois a vices iste non concorda. Il pare que il ha intervallos de files pro le quales le mesme Z-Library ha metadata incorrecte. Nos poterea etiam haber files incorrectemente discargate in alcun casos isolate, que nos va tentar de detectar e corriger in le futuro. Le grande files torrent contine le datos real del libros, con le ID de Z-Library como le nomine del file. Le extension de file pote esser reconstruite usante le dump de metadata. Le collection es un mixtura de contento non-fiction e fiction (non separate como in Library Genesis). Le qualitate es etiam multo variabile. Iste prime lancemento es ora totalmente disponibile. Nota que le files torrent es solmente disponibile via nostre speculo Tor. Lancemento 1 (%(date)s) Isto es un sol file torrent extra. Il non contine nove information, ma il ha alcun datos in illo que pote prender tempore a calcular. Isto lo rende convenibile a haber, pois discargar iste torrent es sovente plus rapide que calcular lo ab initio. In particular, il contine indices SQLite pro le files tar, pro uso con <a %(a_href)s>ratarmount</a>. Addendum al Lancemento 2 (%(date)s) Nos ha obtenite tote le libros que esseva addite al Z-Library inter nostre ultime speculo e augusto 2022. Nos ha etiam retrocedite e raspat alcun libros que nos mancava le prime vice. In total, iste nove collection es circa 24TB. De novo, iste collection es deduplicate contra Library Genesis, pois il ha ja torrents disponibile pro ille collection. Le datos es organisate similarmente al prime lancemento. Il ha un dump MySQL “.sql.gz” del metadata, que etiam include tote le metadata del prime lancemento, superandolo assi. Nos etiam addite alcun nove columnas: Nos mentionava isto le ultime vice, ma solmente pro clarificar: “filename” e “md5” es le proprietates actual del file, durante que “filename_reported” e “md5_reported” es lo que nos raspava de Z-Library. A vices iste duo non concorda, assi que nos includeva ambes. Pro iste version, nos cambiava le collocation a “utf8mb4_unicode_ci”, que deberea esser compatibile con versiones plus vetule de MySQL. Le files de datos es simile al ultime vice, ben que illos es multo plus grande. Nos simplemente non poteva esser molestate a crear multe files de torrent plus parve. “pilimi-zlib2-0-14679999-extra.torrent” contine tote le files que nos mancava in le ultime version, durante que le altere torrents es tote nove intervallos de ID.  <strong>Actualisation %(date)s:</strong> Nos faceva le major parte de nostre torrents troppo grande, causante difficultates pro le clientes de torrent. Nos los ha removite e publicate nove torrents. <strong>Actualisation %(date)s:</strong> Il habeva ancora tropo multe files, assi nos los envolvite in files tar e publicate nove torrents de novo. %(key)s: si iste file es ja in Library Genesis, in le collection non-fiction o fiction (correspondite per md5). %(key)s: in qual torrent iste file es. %(key)s: indicate quando nos non poteva discargar le libro. Lancemento 2 (%(date)s) Lancementos de Zlib (paginas de description original) Dominio Tor Sito principal Raspamento de Z-Library Le collection “Chinese” in Z-Library pare esser le mesme que nostre collection DuXiu, ma con differente MD5s. Nos exclue iste files de torrents pro evitar duplication, ma ancora los monstra in nostre indice de recerca. Metadata Tu recipe %(percentage)s%% discargas rapide bonus, proque tu esseva referite per le usator %(profile_link)s. Isto es applicabile al integre periodo de membresa. Donar Unir-se Selectate usque a %(percentage)s%% reductiones Alipay supporta cartas de credito/debito international. Vide <a %(a_alipay)s>iste guida</a> pro plus information. Invia nos cartas de dono de Amazon.com usante tu carta de credito/debito. Tu pote comprar crypto usante cartas de credito/debito. WeChat (Weixin Pay) supporta cartas de credito/debito international. In le app de WeChat, vade a “Io => Servicos => Portafolio => Adder un Carta”. Si tu non vide isto, activa lo usante “Io => Configurationes => General => Instrumentos => Weixin Pay => Activar”. (usa quando invia Ethereum ab Coinbase) copiate! copia (minimo importo le plus basse) (advertimento: alte minimo importo) -%(percentage)s%% 12 menses 1 mense 24 menses 3 menses 48 menses 6 menses 96 menses Selige pro quanto tempore tu vole subscribite. <div %(div_monthly_cost)s></div><div %(div_after)s>post <span %(span_discount)s></span> discontos</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% pro 12 menses pro 1 mense pro 24 menses pro 3 menses pro 48 menses pro 6 menses pro 96 menses %(monthly_cost)s / mense contacta nos Servitores directes <strong>SFTP</strong> Donation o cambio a nivello de enterprise pro nove collectiones (ex. nove scannos, datasets OCR). Accesso de Experto <strong>Accesso</strong> illimitate a alte velocitate <div %(div_question)s>Pote io meliorar mi adhesion o obtener multiple adhesiones?</div> <div %(div_question)s>Pote io facer un donation sin devenir membro?</div> Certemente. Nos accepta donationes de qualcunque quantitate a iste adresse de Monero (XMR): %(address)s. <div %(div_question)s>Que significa le intervallos per mense?</div> Tu pote attinger le latere inferior de un intervallo applicando tote le discontos, como seliger un periodo plus long que un mense. <div %(div_question)s>Le adhesiones se renova automaticamente?</div> Le adhesiones <strong>non</strong> se renova automaticamente. Vos pote junger pro tanto tempore o tanto breve como vos vole. <div %(div_question)s>In que vos dispendi le donationes?</div> 100%% va a preservar e render accessibile le cognoscentia e cultura del mundo. Actualmente nos lo dispendi majormente in servitores, immagazinamento, e banda. Nulle moneta va personalemente a membros del equipa. <div %(div_question)s>Pote io facer un grande donation?</div> Isto esserea stupende! Pro donationes de plus que alcun milles de dollares, per favor contacta nos directemente a %(email)s. <div %(div_question)s>Vos ha altere methodos de pagamento?</div> Actualmente non. Multes non vole que archivos como iste existe, assi nos debe esser cautelose. Si vos pote adjutar nos a establir altere methodos de pagamento (plus convenibile) in maniera secur, per favor contacta nos a %(email)s. FAQ de Donationes Tu ha un <a %(a_donation)s>donation existente</a> in progresso. Per favor, fini o cancella ille donation ante de facer un nove donation. <a %(a_all_donations)s>Vider tote mi donationes</a> Pro donationes de plus de $5000, per favor contacta nos directemente a %(email)s. Nos accogni grande donationes de individuos ric o institutiones.  Sia conscie que durante le adhesiones in iste pagina es “per mense”, illos es donationes unic (non-recorrente). Vide le <a %(faq)s>FAQ de Donationes</a>. Le Archivo de Anna es un projecto sin scopo de lucro, de codice aperte, e de datos aperte. Donante e deveniente membro, tu supporta nostre operationes e disveloppamento. A tote nostre membros: gratias pro mantener nos in functionamento! ❤️ Pro plus informationes, consulta le <a %(a_donate)s>FAQ de Donationes</a>. Pro devenir un membro, per favor <a %(a_login)s>Aperi session o Registra te</a>. Gratias pro tu supporto! $%(cost)s / mense Si vos faceva un error durante le pagamento, nos non pote facer restitutiones, ma nos essayara de corriger lo. Trova le pagina “Crypto” in tu app o sito web de PayPal. Isto es typicamente sub “Finantias”. Vade al pagina “Bitcoin” in tu app o sito web de PayPal. Preme le button “Transfer”, %(transfer_icon)s, e postea “Inviar”. Alipay Alipay 支付宝 / WeChat 微信 Carta de Dono Amazon %(amazon)s carta de dono Carta bancari Carta bancari (usante app) Binance Credito/debito/Apple/Google (BMC) Cash App Carta de credito/debito Carta de credito/debito 2 Carta de credito/debito (reserva) Crypto %(bitcoin_icon)s Carta / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regular) Pix (Brazil) Revolut (temporarimente non disponibile) WeChat Selige tu crypto moneta preferite: Dona usante un carta de dono de Amazon. <strong>IMPORTANT: </strong> Iste option es pro %(amazon)s. Si tu vole usar un altere sito de Amazon, selige lo supra. <strong>IMPORTANT:</strong> Nos supporta solmente Amazon.com, non altere sitos de Amazon. Per exemplo, .de, .co.uk, .ca, NON es supportate. Per favor NON scribe tu proprie message. Entra le summa exacte: %(amount)s Nota que nos debe approximar a importos acceptate per nostre revendedores (minimo %(minimum)s). Dona usante un carta de credito/debito, per le app Alipay (super facile a configurar). Installa le app Alipay ab le <a %(a_app_store)s>Apple App Store</a> o <a %(a_play_store)s>Google Play Store</a>. Registra te usante tu numero de telephono. Nulle altere detalios personal es requirite. <span %(style)s>1</span>Installa app Alipay Supportate: Visa, MasterCard, JCB, Diners Club e Discover. Vide <a %(a_alipay)s>iste guida</a> pro plus informationes. <span %(style)s>2</span>Adde carta bancari Con Binance, tu compra Bitcoin con un carta de credito/debito o conto bancari, e postea dona iste Bitcoin a nos. In iste maniera nos pote remaner secur e anonyme quando accepta tu donation. Binance es disponibile in quasi omne paises, e supporta le major parte de bancas e cartas de credito/debito. Iste es actualmente nostre recommendation principal. Nos aprecia que tu prende le tempore pro apprender como donar usante iste methodo, pois que illo nos adjuta multo. Pro cartas de credito, cartas de debito, Apple Pay, e Google Pay, nos usa “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). In lor systema, un “cafe” es equal a $5, assi tu donation essera approximate al multiple de 5 plus proxime. Donar usante Cash App. Si tu ha Cash App, isto es le maniera le plus facile de donar! Nota que pro transactiones sub %(amount)s, Cash App pote imponer un %(fee)s taxa. Pro %(amount)s o plus, illo es gratuite! Donar con un carta de credito o debito. Iste methodo usa un fornitore de cryptomoneta como conversion intermedie. Isto pote esser un poco confusente, assi per favor usa iste methodo solmente si altere methodos de pagamento non functiona. Illo anque non functiona in omne paises. Nos non pote supportar cartas de credito/debito directemente, proque le bancas non vole laborar con nos. ☹ Tamen, il ha plure manieras de usar cartas de credito/debito de altere methodos de pagamento: Con crypto vos pote donar usante BTC, ETH, XMR, e SOL. Usa iste option si tu es ja familiar con cryptocurrency. Con crypto vos pote donar usante BTC, ETH, XMR, e plus. Servicios crypto express Si vos usa crypto pro le prime vice, nos suggere usar %(options)s pro comprar e donar Bitcoin (le original e le plus usate cryptocurrency). Nota que pro donationes parve le taxas de carta de credito pote eliminar nostre disconto de %(discount)s%%, assi nos recommenda subscriptiones plus long. Dona usante un carta de credito/debito, PayPal, o Venmo. Tu pote seliger inter iste optiones in le proxime pagina. Google Pay e Apple Pay poterea functionar anque. Nota que pro donationes parve le taxas es alte, assi nos recommenda subscriptiones plus long. Pro donar usante PayPal US, nos va usar PayPal Crypto, que nos permitte remaner anonyme. Nos aprecia que tu prende le tempore pro apprender como donar usante iste methodo, pois que illo nos adjuta multo. Donar usante PayPal. Dona per medio de tu conto regular de PayPal. Dona per medio de Revolut. Si tu ha Revolut, isto es le maniera plus facile de donar! Iste methodo de pagamento permitte solmente un maximo de %(amount)s. Per favor selige un different duration o methodo de pagamento. Iste methodo de pagamento require un minimo de %(amount)s. Per favor selige un different duration o methodo de pagamento. Binance Coinbase Kraken Per favor selige un methodo de pagamento. “Adopta un torrent”: tu nomine de usator o message in un nomine de file torrent <div %(div_months)s>una vice cata 12 menses de membro</div> Tu nomine de usator o mention anonyme in le creditos Accesso anticipate a nove functiones Telegram exclusive con actualisationes detra le scena %(number)s discargas rapide per die si vos dona iste mense! Accesso a <a %(a_api)s>API JSON</a> Stato legendarie in preservation del cognoscentia e cultura del humanitate Beneficios previe, plus: Gania <strong>%(percentage)s%% discargas bonus</strong> per <a %(a_refer)s>referente amicos</a>. Articulos de SciDB <strong>illimitate</strong> sin verification Quando tu face questiones super contos o donationes, adjunge tu ID de conto, capturas de schermo, receptas, tanto information como possibile. Nos solmente verifica nostre email cata 1-2 septimanas, assi non includer iste information retardara omne resolution. Pro obtener ancora plus de discargas, <a %(a_refer)s>refere tu amicos</a>! Nos es un parve equipa de voluntarios. Il pote prender 1-2 septimanas pro responder. Nota que le nomine del conto o photo pote parer estranie. Non te preoccupa! Iste contos es gestionate per nostre partenarios de donation. Nostre contos non ha essite hackate. Donar <span %(span_cost)s></span> <span %(span_label)s></span> pro 12 menses “%(tier_name)s” pro 1 mense “%(tier_name)s” pro 24 menses “%(tier_name)s” pro 3 menses “%(tier_name)s” pro 48 menses “%(tier_name)s” pro 6 menses “%(tier_name)s” pro 96 menses “%(tier_name)s” Tu pote ancora cancellar le donation durante le checkout. Clicca le button donar pro confirmar iste donation. <strong>Nota importante:</strong> Le precios de crypto monetas pote fluctuar multo, a vices mesmo usque a 20%% in alcun minutas. Isto es ancora minus que le taxas que nos incurre con multe fornitore de pagamento, qui sovente demanda 50-60%% pro laborar con un “caritate in le umbra” como nos. <u>Si tu nos invia le recibo con le precio original que tu pagava, nos ancora accreditará tu conto pro le membro eligite</u> (a condition que le recibo non es plus vetule que alcun horas). Nos vermente aprecia que tu es disposte a tolerar cosas como isto pro supportar nos! ❤️ ❌ Alique iva mal. Per favor recarga le pagina e reproba. <span %(span_circle)s>1</span>Comprar Bitcoin in Paypal <span %(span_circle)s>2</span>Transfere le Bitcoin a nostre adresse ✅ Redirigente al pagina de donation… Donar Per favor attende al minus <span %(span_hours)s>24 horas</span> (e refresca iste pagina) ante de contactar nos. Si tu vole facer un donation (qualque summa) sin membro, senti te libere de usar iste adresse de Monero (XMR): %(address)s. Post inviar tu carta de dono, nostre systema automate lo confirmara intra alcun minutas. Si isto non functiona, prova reinviar tu carta de dono (<a %(a_instr)s>instructiones</a>). Si isto ancora non functiona, per favor invia nos un email e Anna lo revisara manualmente (isto pote prender alcun dies), e sia secur de mentionar si tu ha ja probate reinviar. Exemplo: Per favor usa le <a %(a_form)s>formulario official de Amazon.com</a> pro inviar nos un carta de dono de %(amount)s al adresse de email infra. Email del recipiente “A” in le formulario: Carta de dono Amazon Nos non pote acceptar altere methodos de cartas de dono, <strong>solmente inviate directemente del formulario official in Amazon.com</strong>. Nos non pote retornar tu carta de dono si tu non usa iste formulario. Usa solmente un vice. Unic a tu conto, non divider. Expectante le carta de dono… (refresca le pagina pro verificar) Aperi le <a %(a_href)s>pagina de donation per codice QR</a>. Scanna le codice QR con le app Alipay, o preme le button pro aperir le app Alipay. Per favor, sia patiente; le pagina pote prender un poco de tempore pro cargar pois que illo es in China. <span %(style)s>3</span>Face donation (scanna codice QR o preme button) Compra moneta PYUSD in PayPal Compra Bitcoin (BTC) in Cash App Compra un poco plus (nos recommendamos %(more)s plus) que le summa que tu dona (%(amount)s), pro coperir le taxas de transaction. Tu retine lo que resta. Vade al pagina “Bitcoin” (BTC) in Cash App. Transfere le Bitcoin a nostre adresse Pro parve donationes (sub $25), tu poterea necessitar usar Rush o Priority. Clicca le button “Inviar bitcoin” pro facer un “retraction”. Cambia de dollars a BTC per premer le icone %(icon)s. Insere le summa de BTC infra e clicca “Inviar”. Vide <a %(help_video)s>iste video</a> si tu ha difficultate. Servicios express es convenibile, ma carga taxas plus alte. Tu pote usar isto in loco de un cambio crypto si tu vole facer rapidemente un donation plus grande e non importa un taxa de $5-10. Assecura te de inviar le exacto quantitate crypto monstrate in le pagina de donation, non le quantitate in $USD. Altrimenti le taxa essera subtrahite e nos non pote processar automaticamente tu adhesion. A vices, le confirmation pote prender usque 24 horas, assi assecurate vos de refrescar iste pagina (mesmo si illo ha expirate). Instructiones pro carta de credito / debito Dona per nostre pagina de carta de credito / debito Alcun del passos mentiona portafolios crypto, ma non te preoccupa, tu non debe apprender nihil super crypto pro isto. %(coin_name)s instructiones Scanna iste codice QR con tu application de portamoneta crypto pro rapidemente completar le detalios de pagamento Scannar le codice QR pro pagar Nos supporta solmente le version standard de monetas crypto, non redes o versiones exotic de monetas. Il pote prender usque un hora pro confirmar le transaction, dependente del moneta. Dona %(amount)s in <a %(a_page)s>iste pagina</a>. Iste donation ha expirate. Per favor cancellar e crear un nove. Si vos ha ja pagate: Si, io ha inviate mi recibo per email Si le taxa de cambio crypto fluctuava durante le transaction, assecurar de includer le recibo monstrante le taxa de cambio original. Nos vermente aprecia que tu prende le effortio de usar crypto, illo nos adjuta multo! ❌ Alicun cosa errava. Per favor recarga le pagina e reproba. <span %(span_circle)s>%(circle_number)s</span>Inviar nos le recibo per email Si tu incontra qualcun problemas, per favor contacta nos a %(email)s e include tanto informationes como possibile (como capturas de schermo). ✅ Gratias pro tu donation! Anna activara manualmente tu adhesion intra alcun dies. Inviar un recibo o screenshot a tu adresse personal de verification: Quando tu ha inviate tu recibo per email, clicca iste button, assi Anna pote revisar lo manualmente (isto pote prender alcun dies): Invia un recipto o screenshot a tu adresse personal de verification. NON usa iste adresse de email pro tu donation PayPal. Cancella Si, per favor cancellar Esque vos es secur de voler cancellar? Non cancellar si vos ha ja pagate. ❌ Altereva errava. Per favor recargar le pagina e reprobar. Facer un nove donation ✅ Tu donation ha essite cancellate. Data: %(date)s Identificator: %(id)s Reordinar Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mense pro %(duration)s menses, includente %(discounts)s%% disconto)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / mense pro %(duration)s menses)</span> 1. Insere tu email. 2. Selige tu methodo de pagamento. 3. Selige tu methodo de pagamento de novo. 4. Selige “Portafolio auto-hospitate”. 5. Clicca “Io confirma proprietate”. Vos debe reciper un recipto per email. Per favor invia lo a nos, e nos confirmara vostre donation tan tosto como possibile. (tu poterea voler cancellar e crear un nove donation) Le instructiones de pagamento es ora obsolete. Si tu vole facer un altere donation, usa le button “Reordinar” supra. Tu ha ja pagate. Si tu vole revisar le instructiones de pagamento de omne modo, clicca hic: Monstrar vetule instructiones de pagamento Si le pagina de donation es blocate, prova un connexion de internet differente (p.ex. VPN o internet de telephono). Infelicemente, le pagina de Alipay es sovente solmente accessibile de <strong>China continental</strong>. Tu pote haber necessitate de disactivar temporarimente tu VPN, o usar un VPN a China continental (o Hong Kong functiona a vices tamben). <span %(span_circle)s>1</span> Dona in Alipay Dona le total de %(total)s usante <a %(a_account)s>iste conto de Alipay</a> Instructiones pro Alipay <span %(span_circle)s>1</span>Transferer a un de nostre contos crypto Donar le summa total de %(total)s a un de iste adresses: Instructiones pro crypto Seque le instructiones pro comprar Bitcoin (BTC). Tu solmente debe comprar le quantitate que tu vole donar, %(total)s. Entra nostre adresse de Bitcoin (BTC) como le recipiente, e seque le instructiones pro inviar tu donation de %(total)s: <span %(span_circle)s>1</span>Donar per Pix Donar le summa de %(total)s usante <a %(a_account)s>iste conto de Pix</a> Instructiones pro Pix <span %(span_circle)s>1</span>Donar per WeChat Donar le summa de %(total)s usante <a %(a_account)s>iste conto de WeChat</a> Instructiones pro WeChat Usa qualcunque del sequente servitios “carta de credito a Bitcoin” express, que prende solmente alcun minutas: BTC / Adresse de Bitcoin (portafolio externe): BTC / Quantitate de Bitcoin: Completa le sequente detalios in le formulario: Si alcun de iste information es obsolete, per favor invia nos un email pro informar nos. Per favor usa iste <span %(underline)s>quantitate exacte</span>. Tu costo total poterea esser plus alte a causa de taxas de carta de credito. Pro parve quantitates isto poterea esser plus que nostre disconto, malfortunatemente. (minimo: %(minimum)s, sin verification pro le prime transaction) (minimo: %(minimum)s) (minimo: %(minimum)s) (minimo: %(minimum)s, sin verification pro le prime transaction) (minimo: %(minimum)s) (minimo: %(minimum)s dependente del pais, sin verification pro le prime transaction) Seque le instructiones pro comprar moneta PYUSD (PayPal USD). Compra un poco plus (nos recommenda %(more)s plus) que le quantitate que tu dona (%(amount)s), pro coperir taxas de transaction. Tu retenera quidque que remane. Va al pagina “PYUSD” in tu app o sito web de PayPal. Preme le button “Transfer” %(icon)s, e postea “Send”. Actualisar stato Pro reinitialisar le temporisator, simplemente crea un nove donation. Assecura te usar le quantitate de BTC infra, <em>NON</em> euros o dollares, alteremente nos non recipera le quantitate correcte e non potera confirmar automaticamente tu adhesion. Compra Bitcoin (BTC) in Revolut Compra un poco plus (nos recommendamos %(more)s plus) que le summa que tu dona (%(amount)s), pro coperir le taxas de transaction. Tu retine lo que resta. Vade al pagina “Crypto” in Revolut pro comprar Bitcoin (BTC). Transfere le Bitcoin a nostre adresse Pro parve donationes (sub $25) tu poterea necessitar usar Rush o Priority. Clicca le button “Inviar bitcoin” pro facer un “retraction”. Cambia de euros a BTC per premer le icone %(icon)s. Insere le summa de BTC infra e clicca “Inviar”. Vide <a %(help_video)s>iste video</a> si tu ha difficultate. Stato: 1 2 Guida passo a passo Vide le guida passo a passo infra. Altrimenti tu poterea esser blocate de iste conto! Si tu non ha ja facite, scribe tu clave secrete pro acceder: Gratias pro tu donation! Tempore restante: Donation Transfere %(amount)s a %(account)s Expectante confirmation (refresca le pagina pro verificar)… Expectante transferentia (refresca le pagina pro verificar)… Plus tosto Discargas rapide in le ultime 24 horas conta verso le limite quotidian. Discargas ab Servitores Partner Rapid es marcate per %(icon)s. Ultime 18 horas Nulle files discargate ancora. Files discargate non es publicamente visibile. Tote le horas es in UTC. Files discargate Si vos discargava un file con ambe discargas rapide e lente, illo apparera duo vices. Non preoccupa te troppo, il ha multe personas discargante de sitos web ligate per nos, e es extrememente rar haber problemas. Tamen, pro remaner secur nos recommenda usar un VPN (pagate), o <a %(a_tor)s>Tor</a> (gratis). Io discargava 1984 per George Orwell, le policia venira a mi porta? Tu es Anna! Qui es Anna? Nos ha un API JSON stabile pro membros, pro obtener un URL de discarga rapide: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (documentation intra le JSON mesme). Pro altere casos de uso, como iterar per tote nostre files, construir recercas personalisate, e assi via, nos recommenda <a %(a_generate)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Le datos brutos pote esser explorate manualmente <a %(a_explore)s>per files JSON</a>. Nostre lista de torrents brutos pote esser discargate como <a %(a_torrents)s>JSON</a> etiam. Habes vos un API? Nos non hospita alcun material copyrightate hic. Nos es un motor de recerca, e como tal solmente indexa metadata que es ja publicamente disponibile. Quando tu discarga ab iste fontes externe, nos suggererea verificar le leges in tu jurisdiction respecto a lo que es permittite. Nos non es responsabile pro contento hospitate per alteres. Si tu ha reclamos super lo que tu vide hic, tu melior option es contactar le sito web original. Nos regularimente incorpora lor cambios in nostre base de datos. Si tu vermente pensa que tu ha un reclamo DMCA valide al qual nos deberea responder, per favor completa le <a %(a_copyright)s>Formulario de Reclamo DMCA / Copyright</a>. Nos prende tu reclamos seriemente, e respondera te tan tosto como possibile. Como reportar un violation de copyright? Ecce alcun libros que ha un significantia special pro le mundo de bibliothecas umbra e preservation digital: Quales es tu libros favorite? Nos etiam vole rememorar a omnes que tote nostre codice e datos es completemente open source. Isto es unic pro projectos como le nostre — nos non es conscie de altere projecto con un catalogo similarmente massive que es etiam completemente open source. Nos multo benveni a quicunque pensa que nos gestiona mal nostre projecto a prender nostre codice e datos e establir lor proprie bibliotheca umbra! Nos non dice isto per spite o qualcosa simile — nos vermente pensa que isto esserea fantastic pois que illo elevarea le standard pro omnes, e preservarea meliormente le legato del humanitate. Io odia como tu gestiona iste projecto! Nos amarea que personas installa <a %(a_mirrors)s>mirrors</a>, e nos supportara isto financialmente. Como pote io adjutar? Nos lo face vermente. Nostre inspiration pro colliger metadata es le objectivo de Aaron Swartz de “un pagina web pro cata libro jammais publicate”, pro le qual ille creava <a %(a_openlib)s>Open Library</a>. Iste projecto ha succedite ben, ma nostre position unic nos permitte obtener metadata que illes non pote. Un altere inspiration esseva nostre desiro de saper <a %(a_blog)s>quanto libros il ha in le mundo</a>, pro que nos pote calcular quanto libros nos ancora ha a salvar. Collecta vos metadata? Nota que mhut.org bloca certe intervallos de IP, assi un VPN poterea esser requirite. <strong>Android:</strong> Clicca le menu a tres punctos in le angulo superior dextere, e selige “Adder a Schermo Principal”. <strong>iOS:</strong> Clicca le button “Compartir” in le fundo, e selige “Adder a Schermo Principal”. Nos non ha un app mobile official, ma tu pote installar iste sito web como un app. Habes vos un app mobile? Per favor, invia los al <a %(a_archive)s>Internet Archive</a>. Illes los preservara adequatemente. Como io pote donar libros o altere materiales physic? Como io pote requestar libros? <a %(a_blog)s>Blog de Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — actualisationes regular <a %(a_software)s>Software de Anna</a> — nostre codice open source <a %(a_datasets)s>Datasets</a> — super le datos <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — dominios alternative Ha il plus de recursos super Anna’s Archive? <a %(a_translate)s>Traducer in le Software de Anna</a> — nostre systema de traduction <a %(a_wikipedia)s>Wikipedia</a> — plus super nos (per favor adjuta mantener iste pagina actualisate, o crea un pro tu proprie lingua!) Selige le configurationes que tu prefere, lassa le quadro de recerca vacue, clicca “Recerca”, e postea marca le pagina con le function de marcatores de tu navigator. Como io pote salvar mi configurationes de recerca? Nos accogni con favor le investigadores de securitate pro cercar vulnerabilitates in nostre systemas. Nos es grande defensores de divulgation responsabile. Contacta nos <a %(a_contact)s>ci</a>. Nos es actualmente incapace de offerer recompensas pro errores, excepte pro vulnerabilitates que ha le <a %(a_link)s>potential de compromitter nostre anonymitate</a>, pro le quales nos offere recompensas in le intervallo de $10k-50k. Nos volerea offerer un plus large gamma de recompensas pro errores in le futuro! Per favor nota que attaccos de ingenieria social es foras de scopo. Si tu es interessate in securitate offensive, e vole adjutar a archivar le cognoscentia e cultura del mundo, assecurate de contactar nos. Il ha multe manieras in le quales tu pote adjutar. Ha vos un programma de divulgation responsabile? Nos literalemente non ha bastante recursos pro dar a omnes in le mundo descargas a alte velocitate, tanto como nos volerea. Si un benefactor ric volerea emerger e provider isto pro nos, illo esserea incredibile, ma usque alora, nos face nostre melior. Nos es un projecto non-lucrative que apenas pote sustener se mesme per donationes. Es pro isto que nos implementeva duo systemas pro descargas gratuite, con nostre socios: servitores partite con descargas lente, e servitores un poco plus rapide con un lista de attesa (pro reducer le numero de personas que descarga al mesme tempore). Nos ha etiam <a %(a_verification)s>verification de navigator</a> pro nostre descargas lente, proque alteremente bots e scrapers los abusarea, rendente le cosas mesmo plus lente pro usatores legitime. Nota que, quando usante le Tor Browser, vos poterea necessitar ajustar vostre configurationes de securitate. Al plus basse del optiones, appellate “Standard”, le desafio de turnstile de Cloudflare succede. Al optiones plus alte, appellate “Safer” e “Safest”, le desafio falle. A causa del grandor del files, a vices le descargas lente pote interrumper se in le medio. Nos recommenda usar un gestor de descargas (como JDownloader) pro reprender automaticamente descargas grande. Perque le descargas lente es tanto lente? Questiones Frequente (FAQ) Usa le <a %(a_list)s>generatore de lista de torrent</a> pro generar un lista de torrents que es le plus in necessitate de torrentar, intra le limites de tu spatio de immagazinamento. Si, vide le pagina de <a %(a_llm)s>datos de LLM</a>. Le majoritate del torrents contine le files directemente, lo que significa que tu pote instruer le clientes de torrent a discargar solmente le files requirite. Pro determinar qual files discargar, tu pote <a %(a_generate)s>generar</a> nostre metadata, o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Malheureusement, un numero de collectiones de torrent contine files .zip o .tar al radice, in tal caso tu debe discargar le integre torrent ante de poter seliger files individual. Nulle facile utensiles pro filtrar torrents es disponibile ancora, ma nos invita contributos. (Nos ha <a %(a_ideas)s>alcun ideas</a> pro le ultime caso tamen.) Responsa longe: Responsa curte: non facilemente. Nos tenta mantener le duplication o superposition al minimo inter le torrents in iste lista, ma isto non pote sempre esser attingite, e depende multo del politicas del bibliothecas fonte. Pro bibliothecas que emitte lor proprie torrents, isto es foras de nostre controlo. Pro torrents publicate per Anna’s Archive, nos deduplica solmente basate super le hash MD5, lo que significa que differente versiones del mesme libro non es deduplicate. Si. Iste es in realitate PDFs e EPUBs, illos solmente non ha un extension in multe de nostre torrents. Il ha duo locos ubi tu pote trovar le metadata pro files de torrent, includente le typos/extensions de file: 1. Cata collection o emission ha su proprie metadata. Per exemplo, le <a %(a_libgen_nonfic)s>torrents de Libgen.rs</a> ha un base de datos de metadata correspondente hospitate in le sito web de Libgen.rs. Nos typicamente liga a recursos de metadata relevante ab cata pagina de <a %(a_datasets)s>dataset</a> del collection. 2. Nos recommenda <a %(a_generate)s>generar</a> o <a %(a_download)s>discargar</a> nostre bases de datos ElasticSearch e MariaDB. Iste contine un mappatura pro cata registro in Anna’s Archive a su files de torrent correspondente (si disponibile), sub “torrent_paths” in le JSON de ElasticSearch. Alcun clientes de torrent non supporta grandia dimensiones de pecias, que multe de nostre torrents ha (pro le plus nove nos non face isto plus — mesmo si es valide secundo le specificationes!). Assi, essaya un cliente differente si tu incontra isto, o plange al creatores de tu cliente de torrent. Io volerea adjutar a semenar, ma io non ha multo spatio de disco. Le torrents es troppo lente; pote io discargar le datos directemente de vos? Pote io discargar solmente un subset de files, como solmente un lingua particular o un thema specific? Como gestiona vos le duplicatos in le torrents? Pote io obtener le lista de torrents como JSON? Io non vide PDFs o EPUBs in le torrents, solmente files binari? Que debe io facer? Quare mi cliente de torrent non pote aperir alcun de vostre files de torrent / ligamines magnet? Torrents FAQ Como io incarga nove libros? Per favor vide <a %(a_href)s>iste excellente projecto</a>. Habe vos un monitor de uptime? Que es le Archivo de Anna? Deveni un membro pro usar descargas rapide. Nos supporta ora cartas de dono de Amazon, cartas de credito e debito, crypto, Alipay, e WeChat. Tu ha exhaurite tu descargas rapide hodie. Acceso Descargas horari in le ultime 30 dies. Medie horari: %(hourly)s. Medie quotidian: %(daily)s. Nos collabora con socios pro render nostre collectiones facilemente e libere accessibile a qualcunque persona. Nos crede que omne persona ha le derecto al sapientia collectivate del humanitate. E <a %(a_search)s>non al expensas del autores</a>. Le datasets usate in le Archivo de Anna es completemente aperte, e pote esser mirroreate in massa usante torrents. <a %(a_datasets)s>Sape plus…</a> Archivo a longe termino Base de datos complete Cercar Libros, articulos, revistas, comicos, registros de bibliotheca, metadata, … Tote nostre <a %(a_code)s>codice</a> e <a %(a_datasets)s>datos</a> es completemente open source. <span %(span_anna)s>Le Archivo de Anna</span> es un projecto non-profit con duo objectivos: <li><strong>Preservation:</strong> Salvagardar tote le cognoscentia e cultura del humanitate.</li><li><strong>Acceso:</strong> Render iste cognoscentia e cultura disponibile a qualcunque persona in le mundo.</li> Nos ha le plus grande collection del mundo de datos textual de alte qualitate. <a %(a_llm)s>Sape plus…</a> Datos de entramento de LLM 🪩 Mirros: appello pro voluntarios Si tu opera un processator de pagamento anonyme de alte risco, per favor contacta nos. Nos tamben cerca personas pro placer annuncios parve e de bon gusto. Tote le proventos vade a nostre effortios de preservation. Preservation Nos estima que nos ha preservate circa <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% del libros del mundo</a>. Nos preserva libros, articulos, comicos, revistas, e plus, per portar iste materiales ab varie <a href="https://en.wikipedia.org/wiki/Shadow_library">bibliothecas umbra</a>, bibliothecas official, e altere collectiones in un sol loco. Tote iste datos es preservate pro semper per facer los facile a duplicar in massa — usante torrents — resultante in multe copias circa le mundo. Alcun bibliothecas umbra ja face isto mesme (p.ex. Sci-Hub, Library Genesis), durante que le Archivo de Anna “libera” altere bibliothecas que non offere distribution in massa (p.ex. Z-Library) o que non es bibliothecas umbra del toto (p.ex. Internet Archive, DuXiu). Iste larga distribution, combinate con codice open source, rende nostre sito web resistente a clausuras, e assecurar le preservation a longe termino del cognoscentia e cultura del humanitate. Discoperi plus super <a href="/datasets">nostre datasets</a>. Si vos es un <a %(a_member)s>membro</a>, verification de navigator non es requirite. 🧬&nbsp;SciDB es un continuation de Sci-Hub. SciDB Aperte DOI Sci-Hub ha <a %(a_paused)s>suspendite</a> le carga de nove articulos. Accesso directe a %(count)s papers academic 🧬&nbsp;SciDB es un continuation de Sci-Hub, con su interface familiar e visualisation directe de PDFs. Entra tu DOI pro vider. Nos ha le collection complete de Sci-Hub, assi como nove articulos. Le major parte pote esser visualisate directemente con un interface familiar, simile a Sci-Hub. Alcunes pote esser discargate via fontes externe, in tal caso nos monstra ligamines a illos. Tu pote adjutar enormemente seminante torrents. <a %(a_torrents)s>Sape plus…</a> >%(count)s seminatores <%(count)s seminatores %(count_min)s–%(count_max)s seminatores 🤝 Cercante voluntarios Como un projecto non-profit, open-source, nos sempre cerca personas pro adjutar. Descargas IPFS Lista per %(by)s, create <span %(span_time)s>%(time)s</span> Salveguardar ❌ Algo errava. Per favor reproba. ✅ Salveguardate. Per favor recarga le pagina. Lista es vacue. editar Adder o remover de iste lista per trovar un file e aperir le scheda “Listas”. Lista Como nos pote adjutar Remover superposition (deduplication) Extraction de texto e metadata OCR Nos es capabile de provider accesso a alte velocitate a nostre collectiones complete, assi como a collectiones non publicate. Isto es accesso a nivello de enterprise que nos pote provider pro donationes in le gamma de dece milles USD. Nos es etiam disposte a cambiar isto pro collectiones de alte qualitate que nos non ha ancora. Nos pote reembolsar vos si vos pote provider nos con enrichmento de nostre datos, como: Supporta le archivo a longe termino del cognoscentia human, durante que obtene melior datos pro vostre modello! <a %(a_contact)s>Contacta nos</a> pro discuter como nos pote collaborar. Es ben comprendite que le LLMs prospera con datos de alte qualitate. Nos ha le plus grande collection de libros, articulos, revistas, etc. in le mundo, que es alcun del fontes de texto de plus alte qualitate. Datos de LLM Scala e gamma unic Nostre collection contine plus de cento milliones de files, includente jornales academic, libros de texto, e revistas. Nos attinge iste scala combinante grande repositorios existente. Alcun de nostre collectiones de fonte es ja disponibile in massa (Sci-Hub, e partes de Libgen). Altere fontes nos liberava nos mesme. <a %(a_datasets)s>Datasets</a> monstra un vista complete. Nostre collection include milliones de libros, articulos, e revistas de ante le era del e-libros. Grande partes de iste collection ha ja essite OCRate, e ha ja pauc superposition interne. Continua Si tu perdeva tu clave, per favor <a %(a_contact)s>contacta nos</a> e provide tanto information como possibile. Tu poterea deber temporarimente crear un nove conto pro contactar nos. Per favor <a %(a_account)s>aperi un session</a> pro vider iste pagina.</a> Pro prevenir que spam-bots crea multe contos, nos debe primo verificar tu navigator. Si tu es capturate in un bucla infinite, nos recommenda installar <a %(a_privacypass)s>Privacy Pass</a>. Il pote etiam adjutar disactivar bloqueatores de annuncios e altere extensiones del navigator. Aperir session / Registrar se Le Archivo de Anna es temporarimente indisponibile pro manutention. Per favor retorna in un hora. Autor alternative Description alternative Edition alternative Extension alternative Nomine alternative Editor alternative Titulo alternative data de apertura al fonte libere Leger plus… description Cercar in Anna’s Archive pro numero SSNO de CADAL Cercar in Anna’s Archive pro numero SSID de DuXiu Cercar in Anna’s Archive pro numero DXID de DuXiu Cerca in le Archivo de Anna pro ISBN Cercar in Anna’s Archive pro numero OCLC (WorldCat) Cercar in Anna’s Archive pro ID de Open Library Visor online de le Archivo de Anna %(count)s paginas affectate Post discargar: Un melior version de iste file pote esser disponibile a %(link)s Downloads de torrent in massa collection Usa instrumentos online pro converter inter formatos. Instrumentos de conversion recommendate: %(links)s Pro grande files, nos recommenda usar un gestor de descargas pro prevenir interruptiones. Gestores de descargas recommendate: %(links)s Indice de eBook EBSCOhost (solmente pro expertos) (anque clicca “GET” in alto) (clicca “GET” in alto) Discargas externe <strong>🚀 Discargas rapide</strong> Vos ha %(remaining)s restante hodie. Gratias pro esser un membro! ❤️ <strong>🚀 Discargas rapide</strong> Vos ha exhaurite vostre discargas rapide pro hodie. <strong>🚀 Discargas rapide</strong> Vos ha discargate iste file recentemente. Ligamines remane valide pro un tempore. <strong>🚀 Discargas rapide</strong> Deveni un <a %(a_membership)s>membro</a> pro supportar le preservation a longe termino de libros, documentos, e plus. Pro monstrar nostre gratia pro vostre supporto, vos recipe discargas rapide. ❤️ 🚀 Descargas rápidas 🐢 Discargas lente Presta ab le Internet Archive IPFS Gateway #%(num)d (Tu poterea deber probar multiple vices con IPFS) Libgen.li Libgen.rs Fiction Libgen.rs Non-Fiction loros annuncios es cognoscite pro continer software maligne, dunque usa un bloqueator de annuncios o non clicca annuncios Amazon‘s “Inviar a Kindle” djazz‘s “Inviar a Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Les files Nexus/STC pote esser inaffidable pro discargar) Nulle discargas trovate. Tote le optiones de discarga ha le mesme file, e deberea esser secur a usar. Dicer isto, sempre sia cautelose quando discargante files del internet, specialmente de sitos externe a Anna’s Archive. Per exemplo, sia secur de mantener vostre dispositivos actualisate. (nulle redirection) Aperir in nostre visor (aperir in visor) Option #%(num)d: %(link)s %(extra)s Trovar le registro original in CADAL Cercar manualmente in DuXiu Trovar le registro original in ISBNdb Trovar le registro original in WorldCat Trovar le registro original in Open Library Cercar in varie altere bases de datos pro ISBN (solmente pro patronos con disabilitate de impression) PubMed Tu habera besonio de un lector de ebook o PDF pro aperir le file, dependente del formato del file. Lectores de ebook recommendate: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (DOI associate poterea non esser disponibile in Sci-Hub) Tu pote inviar ambe files PDF e EPUB a tu Kindle o Kobo eReader. Instrumentos recommendate: %(links)s Plus information in le <a %(a_slow)s>FAQ</a>. Supporta autores e bibliothecas Si tu ama isto e pote permitter lo, considera comprar le original, o supportar le autores directemente. Si isto es disponibile a vostre bibliotheca local, considera prender lo in prestito ibi gratuitemente. Downloads del Partner Server temporarimente non disponibile pro iste file. torrent De partnarios de fide. Z-Library Z-Bibliotheca in Tor (requere le Navigator Tor) mostrar descargas externas <span class="font-bold">❌ Iste file pote haber problemas, e ha essite occultate ab un bibliotheca fonte.</span> A vices isto es per requesta de un detentor de copyright, a vices es proque un melior alternative es disponibile, ma a vices es proque il ha un problema con le file mesme. Il pote ancora esser ben a discargar, ma nos recommenda primo cercar un file alternative. Plus de detalios: Si tu ancora vole discargar iste file, assecurar te de solmente usar software de fide, actualisate pro aperir lo. commentos de metadatos AA: Cercar in le Archivo de Anna pro “%(name)s” Explorator de Codices: Vider in le Explorator de Codices “%(name)s” URL: Sito web: Si tu ha iste file e illo non es ancora disponibile in Anna’s Archive, considera <a %(a_request)s>incarcar lo</a>. Internet Archive Controlled Digital Lending file “%(id)s” Isto es un registro de un file del Internet Archive, non un file directemente discargabile. Tu pote provar prender le libro in prestito (ligamine infra), o usar iste URL quando <a %(a_request)s>requestar un file</a>. Meliorar metadata CADAL SSNO %(id)s registro de metadata Isto es un registro de metadata, non un file descargabile. Tu pote usar iste URL quando <a %(a_request)s>requestante un file</a>. DuXiu SSID %(id)s registro de metadata Registro de metadata de ISBNdb %(id)s MagzDB ID %(id)s recordo de metadata Nexus/STC ID %(id)s recordo de metadata Registro de metadata de numero OCLC (WorldCat) %(id)s Registro de metadata de Open Library %(id)s Sci-Hub file “%(id)s” Non trovate “%(md5_input)s” non esseva trovate in nostre base de datos. Agregar comentario (%(count)s) Puede obtener el md5 de la URL, por ejemplo, MD5 de una mejor versión de este archivo (si aplica). Complete esto si hay otro archivo que coincida estrechamente con este archivo (misma edición, misma extensión de archivo si puede encontrar uno), que las personas deberían usar en lugar de este archivo. Si conoce una mejor versión de este archivo fuera de Anna’s Archive, por favor <a %(a_upload)s>cárguela</a>. Alcose errava. Per favor recarga le pagina e reproba. Tu lassava un commento. Illo poterea prender un minuta pro apparer. Por favor, use el <a %(a_copyright)s>formulario de reclamo de DMCA / Derechos de Autor</a>. Describa el problema (requerido) Si iste file ha grande qualitate, tu pote discuter alcun cosa super illo hic! Si non, per favor usa le button “Reportar problema de file”. Gran calidad del archivo (%(count)s) Calidad del archivo Impara a <a %(a_metadata)s>meliorar le metadata</a> pro iste file tu mesme. Descripción del problema Por favor, <a %(a_login)s>inicie sesión</a>. Io amava iste libro! ¡Ayude a la comunidad reportando la calidad de este archivo! 🙌 Alcose errava. Per favor recarga le pagina e reproba. Reportar problema del archivo (%(count)s) Gratias pro submitter tu reporto. Illo essera monstrate in iste pagina, e etiam essera revistate manualmente per Anna (usque nos ha un systema de moderation appropriate). Lassa un commento Enviar reporte ¿Qué está mal con este archivo? Prestar (%(count)s) Commentos (%(count)s) Descargas (%(count)s) Explorar metadata (%(count)s) Listas (%(count)s) Statisticas (%(count)s) Pro information super iste file particular, consulta su <a %(a_href)s>file JSON</a>. Iste es un file gestionate per le <a %(a_ia)s>bibliotheca de Prestate Digital Controlate de IA</a>, e indicite per le Archivo de Anna pro recerca. Pro information super le varie datasets que nos ha compilate, vide le <a %(a_datasets)s>pagina de Datasets</a>. Metadatos del registro vinculado Mejorar metadatos en Open Library Un “file MD5” es un hash que es computate ab le contento del file, e es rationabilemente unic basate super ille contento. Tote le bibliothecas umbra que nos ha indicite hic usa primarimente MD5s pro identificar files. Un file poterea apparer in multiple bibliothecas umbra. Pro information super le varie datasets que nos ha compilate, vide le <a %(a_datasets)s>pagina de Datasets</a>. Reportar calidad del archivo Total de downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadata chec %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Libros %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Advertencia: múltiples registros vinculados: Quando vos reguarda un libro in le Archivo de Anna, vos pote vider varie campos: titulo, autor, editor, edition, anno, description, nomine de file, e plus. Tote iste pecias de information es appellate <em>metadata</em>. Como nos combina libros de varie <em>bibliothecas fonte</em>, nos monstra qualsecunque metadata es disponibile in ille bibliotheca fonte. Per exemplo, pro un libro que nos obteneva de Library Genesis, nos monstrara le titulo del base de datos de Library Genesis. A vices un libro es presente in <em>multiple</em> bibliothecas fonte, que poterea haber differente campos de metadata. In ille caso, nos simplemente monstra le version le plus longe de cata campo, pois que illo sperabilemente contine le information le plus utile! Nos ancora monstrara le altere campos sub le description, p.ex. como ”titulo alternative” (ma solmente si illos es differente). Nos tamben extrahe <em>codices</em> como identificatores e classificatores del bibliotheca fonte. <em>Identificatores</em> representa unicmente un particular edition de un libro; exemplos es ISBN, DOI, Open Library ID, Google Books ID, o Amazon ID. <em>Classificatores</em> gruppa insimul multiple libros simile; exemplos es Dewey Decimal (DCC), UDC, LCC, RVK, o GOST. A vices iste codices es explicitemente ligate in bibliothecas fonte, e a vices nos pote extraher los del nomine de file o description (primarimente ISBN e DOI). Nos pote usar identificatores pro trovar registros in <em>collectiones de metadata solmente</em>, como OpenLibrary, ISBNdb, o WorldCat/OCLC. Il ha un specific <em>tab de metadata</em> in nostre motor de recerca si vos vole navigar iste collectiones. Nos usa registros correspondentes pro completar campos de metadata mancante (p.ex. si un titulo manca), o p.ex. como “titulo alternative” (si il ha un titulo existente). Pro vider exactemente de ubi le metadata de un libro proveniva, vide le <em>“Detalios technic” tab</em> in un pagina de libro. Illo ha un ligamine al JSON brut pro ille libro, con indicatores al JSON brut del registros original. Pro plus information, vide le sequente paginas: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Recerca (tab de metadata)</a>, <a %(a_codes)s>Explorator de Codices</a>, e <a %(a_example)s>Exemplo de metadata JSON</a>. Finalmente, tote nostre metadata pote esser <a %(a_generated)s>generate</a> o <a %(a_downloaded)s>discargate</a> como bases de datos ElasticSearch e MariaDB. Fundo Vos pote adjutar le preservation de libros per meliorar metadata! Primo, lege le information de fundo super metadata in le Archivo de Anna, e postea apprende como meliorar metadata per ligar con Open Library, e ganiar un abonnement gratuite in le Archivo de Anna. Meliorar metadata Assi si vos incontra un file con mal metadata, como debera vos reparar lo? Vos pote ir al bibliotheca fonte e sequer su proceduras pro reparar metadata, ma que facer si un file es presente in multiple bibliothecas fonte? Il ha un identificator que es tractate specialmente in le Archivo de Anna. <strong>Le campo annas_archive md5 in Open Library sempre prevale super tote le altere metadata!</strong> Vamos retroceder un poco e apprender super Open Library. Open Library esseva fundate in 2006 per Aaron Swartz con le objectivo de “un pagina web pro cata libro jammais publicate”. Illo es un sorta de Wikipedia pro metadata de libros: omnes pote modificar lo, illo es libere licentiate, e pote esser discargate in massa. Es un base de datos de libros que es le plus alignate con nostre mission — in facto, le Archivo de Anna ha essite inspirate per le vision e vita de Aaron Swartz. In vice de reinventa le rota, nos decideva rediriger nostre voluntarios verso Open Library. Si vos vide un libro que ha metadata incorrecte, vos pote adjutar in le sequente maniera: Nota que isto functiona solmente pro libros, non pro documentos academic o altere typos de files. Pro altere typos de files nos ancora recommenda trovar le bibliotheca fonte. Il poterea prender alcun septimanas pro que le cambios sia includite in Anna’s Archive, pois que nos debe discargar le ultime dump de datos de Open Library, e regenerar nostre indice de recerca.  Vade al <a %(a_openlib)s>sitio web de Open Library</a>. Trova le correcte registro de libro. <strong>ATTENTION:</strong> assecurar vos de seliger le correcte <strong>edition</strong>. In Open Library, vos ha “opere” e “editiones”. Un “opere” poterea esser “Harry Potter and the Philosopher's Stone”. Un “edition” poterea esser: Le prime edition de 1997 publicate per Bloomsbery con 256 paginas. Le edition de 2003 in formato de libro de papiro publicate per Raincoast Books con 223 paginas. Le traduction polonese de 2000 “Harry Potter I Kamie Filozoficzn” per Media Rodzina con 328 paginas. Tote iste editiones ha differente ISBNs e differente contentos, assi sia secur de seliger le correcte! Edita le registro (o crea lo si nulle existe), e adde tanto information utile como possibile! Tu es hic ora de omne modo, assi face le registro vermente fantastic. Sub “Numeros de ID” selige “Anna’s Archive” e adde le MD5 del libro ab Anna’s Archive. Isto es le longe serie de litteras e numeros post “/md5/” in le URL. Proba trovar altere files in Anna’s Archive que tamben corresponde a iste registro, e adde los tamben. In le futuro nos potera gruppar los como duplicatos in le pagina de recerca de Anna’s Archive. Quando tu ha finite, scribe le URL que tu ha justo actualisate. Una vice que tu ha actualisate al minus 30 registros con MD5s de Anna’s Archive, invia nos un <a %(a_contact)s>email</a> e invia nos le lista. Nos te dara un abonnemento gratuite pro Anna’s Archive, assi tu potera facer iste labor plus facilemente (e como un gratia pro tu adjuta). Iste debe esser modificationes de alte qualitate que adde substantial quantitates de information, alteremente tu requesta essera rejectate. Tu requesta essera tamben rejectate si alcun del modificationes es revertite o corrigite per moderatores de Open Library. Ligamine con Open Library Si tu te involve significativemente in le disveloppamento e operationes de nostre labor, nos pote discuter de divider plus del entrata de donationes con te, pro que tu lo deploya como necessari. Nos solmente pagara pro le hospitage un vice que tu ha totos le cosas installate, e ha demonstrate que tu pote mantener le archivo actualisate con actualisationes. Isto significa que tu habera que pagar pro le prime 1-2 menses de tu proprie pecunia. Tu tempore non essera compensate (e nec le nostre), pois que isto es labor purmente voluntari. Nos es preste a coperir le depensas de hospitage e VPN, initialmente usque a $200 per mense. Isto es sufficiente pro un servitor de recerca basic e un proxy protegite per DMCA. Depensas de hospitage Per favor <strong>non nos contacta</strong> pro peter permission, o pro questiones basic. Actiones parla plus forte que parolas! Tote le information es ibi, assi solmente procede con establir tu speculo. Senti te libere de postar tickets o requestas de fusion a nostre Gitlab quando tu incontra problemas. Nos poterea haber necessitate de construir alcun functiones specific al speculo con te, como rebranding de “Anna’s Archive” a tu nomine de sito web, (initialmente) disactivar contos de usatores, o ligar retro a nostre sito principal ab paginas de libros. Una vice que tu ha tu speculo functionante, per favor contacta nos. Nos amarea revisar tu OpSec, e una vice que illo es solide, nos ligara a tu speculo, e comenciara a laborar plus proxime con te. Gratias in avantia a qualcunque qui es preste a contribuir in iste maniera! Isto non es pro le timide, ma illo solidificarea le longevitá del plus grande vermente bibliotheca aperte in le historia human. Comenciar Pro augmentar le resiliencia de le Archivo de Anna, nos cerca voluntarios pro operar mirrors. Tu version es clarmente distinguite como un speculo, p. ex. “Le Archivo de Bob, un speculo de Anna’s Archive”. Vos es preste a prender le riscos associate con iste labor, que es significative. Tu ha un comprension profunde del securitate operational requirite. Le contento de <a %(a_shadow)s>iste</a> <a %(a_pirate)s>postages</a> es auto-evidente pro te. Initialmente nos non te dara accesso a nostre downloads del servitor de partenarios, ma si le cosas va ben, nos pote divider isto con te. Tu executa le codice open source de Anna’s Archive, e tu actualisa regularmente tanto le codice como le datos. Tu es preste a contribuir a nostre <a %(a_codebase)s>base de codice</a> — in collaboration con nostre equipa — pro facer isto succeder. Nos cerca isto: Mirrors: appello pro voluntarios Face un altere donation. Nulle donationes ancora. <a %(a_donate)s>Face mi prime donation.</a> Le detalios del donationes non es publicamente visibile. Mi donationes 📡 Pro mirroring massive de nostre collection, verifica le paginas de <a %(a_datasets)s>Datasets</a> e <a %(a_torrents)s>Torrents</a>. Descargas ab tu adresse IP in le ultime 24 horas: %(count)s. 🚀 Pro obtener descargas plus rapide e evitar le verificationes del navigator, <a %(a_membership)s>deveni un membro</a>. Discargar ab sito de partner Senti te libere de continuar navigar in le Archivo de Anna in un differente scheda durante que tu attende (si tu navigator supporta refrescar scheda in fundo). Senti te libere de attender pro multiple paginas de download cargar al mesme tempore (ma per favor solmente discarga un file al mesme tempore per servitor). Una vice que tu recipe un ligamine de download, illo es valide pro plure horas. Gratias pro attender, isto mantene le sito accessibile gratis pro omnes! 😊 🔗 Tote le ligamines de descarga pro iste file: <a %(a_main)s>Pagina principal del file</a>. ❌ Descargas lente non es disponibile via VPNs de Cloudflare o alteremente ab adresses IP de Cloudflare. ❌ Descargas lente es solmente disponibile via le sito official. Visita %(websites)s. 📚 Usa le sequente URL pro discargar: <a %(a_download)s>Discargar ora</a>. Pro dar a omne un opportunitate de discargar files gratuitemente, vos debe attender ante que vos pote discargar iste file. Per favor attende <span %(span_countdown)s>%(wait_seconds)s</span> secundas pro discargar iste file. Advertentia: il ha essite multe descargas ab tu adresse IP in le ultime 24 horas. Descargas poterea esser plus lente que usual. Si tu usa un VPN, connexion de internet partite, o tu ISP parte adresses IP, iste advertentia poterea esser causate per isto. Salveguardar ❌ Algo errava. Per favor reproba. ✅ Salveguardate. Per favor recarga le pagina. Cambia tu nomine de visualisation. Tu identificator (le parte post “#”) non pote esser cambiate. Profilo create <span %(span_time)s>%(time)s</span> editar Listas Crear un nove lista per trovar un file e aperir le scheda “Listas”. Nulle listas ancora Profilo non trovate. Profilo A iste tempore, nos non pote accomodar requestas de libros. Non invia nos requestas de libros per email. Per favor face tu requestas in le foros de Z-Library o Libgen. Registro in le Archivo de Anna DOI: %(doi)s Discargar SciDB Nexus/STC Nulle previe ancora disponibile. Discarga le file ab <a %(a_path)s>le Archivo de Anna</a>. Pro supportar le accessibilitate e preservation a longe termino del cognoscentia human, deveni un <a %(a_donate)s>membro</a>. Como un bonus, 🧬&nbsp;SciDB se carga plus rapidemente pro membros, sin alicun limites. Non functiona? Proba <a %(a_refresh)s>refrescar</a>. Sci-Hub Adder campo specific de recerca Cercar descriptiones e commentos de metadatos Anno de publication Avantiate Acceso Contento Monstrar Lista Tabella Tipo de file Lingua Ordinar per Le plus grande Le plus relevante Le plus nove (dimension de file) (codigo aperte) (anno de publication) Le plus vetule Aleatori Le plus parve Fonte scrappate e open-source per AA Preste Digital (%(count)s) Articulos de Jurnales (%(count)s) Nos ha trovate correspondencias in: %(in)s. Tu pote referer al URL trovate ibi quando <a %(a_request)s>requesta un file</a>. Metadatos (%(count)s) Pro explorar le indice de recerca per codices, usa le <a %(a_href)s>Explorator de Codices</a>. Le indice de recerca es actualisate mensual. Illo actualmente include entratas usque a %(last_data_refresh_date)s. Pro plus information technic, vide le pagina de %(link_open_tag)sdatasets</a>. Excluder Includer solmente Non verificate plus… Sequente … Previe Iste indice de recerca actualmente include metadata del bibliotheca de Prestito Digital Controlate del Internet Archive. <a %(a_datasets)s>Plus information super nostre datasets</a>. Pro plus bibliothecas de prestito digital, vide <a %(a_wikipedia)s>Wikipedia</a> e le <a %(a_mobileread)s>MobileRead Wiki</a>. Pro reclamos de DMCA / copyright <a %(a_copyright)s>clicca hic</a>. Tempore de discargar Error durante le recerca. Proba <a %(a_reload)s>recharger le pagina</a>. Si le problema persiste, per favor invia nos un email a %(email)s. Discarga rapide In facto, qualcunque persona pote adjutar a preservar iste files per semination de nostre <a %(a_torrents)s>lista unificate de torrents</a>. ➡️ A vices isto occurre incorrectemente quando le servitor de recerca es lente. In tal casos, <a %(a_attrs)s>recarregar</a> pote adjutar. ❌ Iste file pote haber problemas. Cercar articulos academic? Iste indice de recerca actualmente include metadata de varie fontes de metadata. <a %(a_datasets)s>Plus information super nostre datasets</a>. Il ha multe, multe fontes de metadata pro obras scribite in le mundo. <a %(a_wikipedia)s>Iste pagina de Wikipedia</a> es un bon initio, ma si tu cognosce altere bon listas, per favor informa nos. Pro metadata, nos monstra le registros original. Nos non face ulle fusion de registros. Nos actualmente ha le catalogo aperte le plus comprensive del mundo de libros, articulos, e altere obras scribite. Nos reflecte Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>e plus</a>. <span class="font-bold">Nulle files trovate.</span> Proba con minus o differente terminos de recerca e filtros. Resultatos %(from)s-%(to)s (%(total)s total) Si tu trova altere “bibliothecas umbra” que nos deberea reflecter, o si tu ha qualcunque questiones, per favor contacta nos a %(email)s. %(num)d correspondencias partial %(num)d+ correspondencias partial Scribe in le quadro pro cercar files in bibliothecas de prestito digital. Scribe in le quadro pro cercar in nostre catalogo de %(count)s files directemente discargabile, que nos <a %(a_preserve)s>preserva pro semper</a>. Scribe in le quadro pro cercar. Scribe in le quadro pro cercar in nostre catalogo de %(count)s articulos academic e de jornales, que nos <a %(a_preserve)s>preserva pro semper</a>. Scribe in le quadro pro cercar metadata de bibliothecas. Isto pote esser utile quando <a %(a_request)s>requestar un file</a>. Consilio: usa le shortcuts de claviero “/” (focus de recerca), “enter” (recerca), “j” (su), “k” (giu), “<” (pagina previe), “>” (pagina sequente) pro navigation plus rapide. Iste es registros de metadata, <span %(classname)s>non</span> files descargabile. Configurationes de recerca Cercar Prestitos Digital Discargar Articulos de jornales Metadatos Nove recerca %(search_input)s - Cercar Le recerca prendeva troppo tempore, lo que significa que tu pote vider resultatos inaccurate. A vices <a %(a_reload)s>recharger</a> le pagina adjuta. Le recerca prendeva troppo tempore, lo que es commun pro questiones general. Le contos de filtro pote non esser accurate. Pro grande incargamentos (plus de 10,000 files) que non es acceptate per Libgen o Z-Library, per favor contacta nos a %(a_email)s. Pro Libgen.li, assecurate vos de primo aperir session in <a %(a_forum)s >lor foro</a> con nomine de usator %(username)s e contrasigno %(password)s, e postea retornar a lor <a %(a_upload_page)s >pagina de carga</a>. Pro ora, nos suggere incargar nove libros al forks de Library Genesis. Ecce un <a %(a_guide)s>guida utile</a>. Nota que ambe forks que nos indexa in iste sito web tira de iste mesme systema de incargamento. Pro parve cargas (usque a 10,000 files) per favor carga los a ambe %(first)s e %(second)s. Alternativemente, vos pote incargar los a Z-Library <a %(a_upload)s>ci</a>. Pro incargar documentos academic, per favor etiam (in addition a Library Genesis) incarga los a <a %(a_stc_nexus)s>STC Nexus</a>. Illes es le melior biblioteca umbra pro nove documentos. Nos non los ha integrate ancora, ma nos lo facera a un certe puncto. Vos pote usar lor <a %(a_telegram)s>bot de incargamento in Telegram</a>, o contactar le adresse listate in lor message fixate si vos ha troppo files pro incargar in iste maniera. <span %(label)s>Labor voluntari pesante (bounties de USD$50-USD$5,000):</span> si vos pote dedicar multo de tempore e/o recursos a nostre mission, nos amarea colaborar plus de vicino con vos. Eventualmente vos pote junger al equipa interne. Ben que nos ha un budgeto strict, nos pote recompensar con <span %(bold)s>💰 bounties monetari</span> pro le labor plus intense. <span %(label)s>Labor voluntari lige:</span> si tu pote solmente sparniar alcun horas hic e ibi, ancora il ha multe manieras in que tu pote adjutar. Nos recompensa voluntarios consistente con <span %(bold)s>🤝 membrosias al Archivo de Anna</span>. Le Archivo de Anna depende de voluntarios como te. Nos accogna omne nivellos de compromisso, e ha duo categorias principal de adjuta que nos cerca: Si vos non pote voluntariar vostre tempore, vos pote ancora adjutar nos multo per <a %(a_donate)s>donar moneta</a>, <a %(a_torrents)s>seminar nostre torrents</a>, <a %(a_uploading)s>incarcar libros</a>, o <a %(a_help)s>dicer a vostre amicos super le Archivo de Anna</a>. <span %(bold)s>Companias:</span> nos offere accesso directe a alte velocitate a nostre collectiones in cambio de donation a nivello de enterprise o in cambio de nove collectiones (p.ex. nove scansiones, datasets OCR, arricchir nostre datos). <a %(a_contact)s>Contacta nos</a> si isto es vos. Vide etiam nostre <a %(a_llm)s>pagina de LLM</a>. Recompensas Nos semper cerca personas con solidas habilitates de programmation o securitate offensive pro implicar se. Tu pote facer un contribution significative a preservar le legato del humanitate. Como gratia, nos offere membrosia pro contributiones solide. Como un grande gratia, nos offere recompensas monetari pro tareas particularmente importante e difficile. Isto non debe esser considerate como un substitution pro un empleo, ma es un incentivo extra e pote adjutar con costos incurrite. Le major parte de nostre codice es open source, e nos va peter lo mesme de tu codice quando nos accorda le recompensa. Il ha alcun exceptiones que nos pote discuter individualmente. Recompensas es accordate al prime persona que completa un tarea. Senti te libere de comentar super un ticket de recompensa pro informar alteres que tu labora super alicun cosa, assi alteres pote attender o contactar te pro collaborar. Ma sia conscie que alteres es ancora libere de laborar super illo e tentar batter te. Tamen, nos non accorda recompensas pro labor mal facite. Si duo submissiones de alte qualitate es facite proxime la un al altere (intra un die o duo), nos pote decider accordar recompensas a ambes, a nostre discretion, per exemplo 100%% pro le prime submission e 50%% pro le secunde submission (assimulante 150%% in total). Pro le recompensas plus grande (specialmente recompensas de scraping), per favor contacta nos quando tu ha completate ~5%% de illo, e tu es confidente que tu methodo va escalar al objectivo complete. Tu habera que divider tu methodo con nos assi nos pote dar retroaction. Assi, nos pote decider que facer si il ha multiple personas que se approxima a un recompensa, como potentialmente accordar lo a multiple personas, incoragiar personas a collaborar, etc. ATTENTION: le tareas con alte recompensas es <span %(bold)s>difficile</span> — il poterea esser sapiente comenciar con los plus facile. Vade a nostre <a %(a_gitlab)s>lista de problemas in Gitlab</a> e ordina per “Prioritate de etichetta”. Isto monstra approximativemente le ordine de tareas que nos importa. Tareas sin recompensas explicite es ancora eligibile pro membrosia, specialmente los marcate “Acceptate” e “Favorita de Anna”. Tu poterea voler comenciar con un “Projecto de initio”. Voluntariato legier Nos ha ora anque un canal Matrix syncronisate a %(matrix)s. Si vos ha alcun horas libere, vos pote adjutar in plure manieras. Assecura te de junger le <a %(a_telegram)s>chat de voluntarios in Telegram</a>. Como signo de appreciation, nos normalmente offere 6 menses de “Bibliothecario Fortunose” pro milestonos basic, e plus pro labor continuate de voluntariato. Tote milestonos require labor de alte qualitate — labor negligente nos face plus de damno que adjuta e nos lo rejectara. Per favor <a %(a_contact)s>invia nos un email</a> quando vos attinge un milestono. %(links)s ligamines o capturas de schermo de requestas que vos compliva. Satisfacer requestas de libros (o papiros, etc.) in le foros de Z-Library o Library Genesis. Nos non ha nostre proprie systema de requesta de libros, ma nos reflecte iste bibliothecas, assi facer los melior face le Archivo de Anna melior tamben. Milestono Tarea Depende del tarea. Parve tareas publicate in nostre <a %(a_telegram)s>chat de voluntarios in Telegram</a>. Usualmente pro membrosia, a vices pro parve bounties. Parve cargas publicate in nostre gruppo de chat de voluntarios. Assecura te de lassar un commento super le problemas que tu repara, assi alteres non duplicara tu labor. %(links)s ligamines de registros que vos meliorava. Tu pote usar le <a %(a_list)s >lista de problemas de metadata aleatori</a> como puncto de initio. Meliorar metadata per <a %(a_metadata)s>linkar</a> con Open Library. Iste deberea monstrar te informante qualcun super le Archivo de Anna, e illes gratias te. %(links)s ligamines o capturas de schermo. Diffunder le parola de le Archivo de Anna. Per exemplo, per recommendar libros in AA, ligar a nostre articulos de blog, o generalmente diriger personas a nostre sito web. Traducer completemente un lingua (si illo non esseva quasi completate ja). <a %(a_translate)s>Traducer</a> le sito web. Ligamine al historia de modificationes monstrante que vos ha facite contributiones significative. Meliorar le pagina de Wikipedia pro le Archivo de Anna in vostre lingua. Include information de pagina de Wikipedia de AA in altere linguas, e de nostre sito web e blog. Adde referentias a AA in altere paginas relevante. Voluntariato & Premios 