��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b Z  sd d  �h 7   3k }  kk H  �l �  2p 1  s �  @u X   �v �   Ow Q   �w �   Ox �  �x }  �{ �  ~ �  � �  �� �  -� U  Շ �  +� �  �� z  h� �  � �   ٖ �  j� r   � �  x� $   A� Z  f� Q   �� P   �    d� c   �� P   � 4   9�    n� g   �� Y   �� <   O�    �� k   �� �  � �  �� �   �� �   *�    ��    ȧ -   ԧ    �    � 
   +� 	   9�    C�    _� :   f�    �� 3   �� 	   ۨ 
   �    � )   �� <   )� +   f� 0  ��   í �   ү 7   k�   �� �   �� �  �� 6  \� 0   ��   Ķ 0   ȷ   �� 5   � �  8� 0   � �  H� A   
� �   O�    � �  � �  �� }  �� �  H�    4� �   I� �   ��   �� U   �� ;   �� �   '� j   �� d   G� G   ��    �� �   �� �  �� �   i� �   ��   �� s   �� �  E� d   � �  � �   *� *  �� �   #� �   �� �   �� Q   �� �  �� K  l� k   �� P   $� �   u�    X�   g� �  t� Q   T�    �� [  �� �  � 
   �� :  �� �   �� )  �� ^  �� �   V�   I� �  h� o   � z   �� �   � �   �� �   `� �   �� Y   �� �   �� w   �� {   8� #   �� �   �� �   �� @  y�   �� %   �� �  �� �  �� g  O  �  � �   Z n  ) �  � �   ]	 �   D
 �   ! �   U   f  [    � �   � -  � )  � �   �   � �   � �   x �  i �   S �  G �   E   � �       �  |   �! �  o" %  :% �   `& �   "' /    ( �  0( Y  �+ S  D- !   �/ �   �/   Q0 �  g1 �  �2   �4 
  )6 f  79 W   �; Q  �;   H>   gB X  kD �  �H �  �J �   yL �   �L    �M 9  �M    (O �  )Q �  �S �  �U K  WW    �X J  �X T  [ |  c]    �^ P  �^ �  D` Q  (b �  zc i  g �   nh    i �  -i "  �k    
n �  "n �  �o �  �q �  5u �  �v �   ay �   z �  �z A   +|    m| f  �| �  �~ �  ƀ `   �� Q   !� �  s� �   6� �   � �   �� }  �� <   0� +   m� �  �� K  D� �   �� �   o� �   � A   �� �  � �  ��    j� %   �� ^   �� z   � r   �� j   �� �   d� �   	�    � D   �� [   >� y   �� P   � m   e� Y   ӛ �   -� 
   � ,   �� �  )� �  � �  � �  �� 6  Τ �  � �  �� *   �� �  ��   B� �   I� W  F� �  �� G  "�   j� �  r� Q   <� U   �� A   � +  &� �  R� �  ��   �� *  �� �  �� �  �� �  K� T   �� �  ;� �  6� 6  %�    \� �  p� J  � �   L�   <� #  O�    s� >   �� �   ��    j�   }� �  �� g  S� )  �� �  ��    x� �  �� �  7� Q   #� �   u� �  � �  ��   ]� x  _� �   �� �  t� '   
� �  5� �   2�   � O  3 O  � &  �	    �
 �  	 4  �    ! ;  A `  } �  � y  � �   i   Q   k! �  �! �  o$ �   & �   �& �   i'    0( G   E( -   �( =   �( .   �( #   () =  L) �  �* #  G/ �  k2 ^  ,6    �7 �  �7 �  �< U  �@ n  �B n  EE \  �I    N �   N    P    P   1P   HS (  _W �  �Z    ?^   Q^ F  Ta �  �d �  =h l  l �  uq �  Cu 1   �w   x �   #y   �y *  �~ G  �� �   G� '  :� w  b�    ڇ #  � h   � }   ~� $   �� U   !� V   w� �  Ί �  �� -  v� ;   �� �  ��   |�    �� I  �� }   � �   e� =  <� F   z� �   ��    d� P  �� �  Ң �  �� �  �� �   � "   Ԭ �   �� �  ��    �� �   �� M  �� '  � f   � R   y� �  ̶ 7   [� V  �� �  �   �� K   �� �   � �   �� �  �� �  �� #   E� �  i� H   � Y  f� -  �� �  �� �  �� �   C� 7   �� �   � a   �� N  �� d  2� o  �� �  � �   �� Y   ��   �� �  ��   �� ]  (�   �� P   �� �  �� �  �� Y  F� @  �� �  �� �  l� 7   &� �  ^� E   8  �  ~  �   ? 3   � >  * 4  i �  �
 �  C
 �  ! -   T  6 #  �   � �  � �   �    �!   �& O   �( �   �( �   �) "   �*    �*   �* �   �, Q   �- �  . �  �/ �  {2 �  4 �  �7 :  g: �  �;   D> &   Y@ $  �@    �A }  �A L   ,D &   yD    �D    �D 7   �D    �D (   E /   7E 
   gE    rE    �E    �E    �E    �E A   �E    F F   .F    uF    ~F    �F    �F =  �F �   �G 4   �H    �H [   �H J   HI h   �I a   �I 2   ^J    �J "   �J    �J    �J    �J 2   K    QK    `K 
   zK ,   �K J   �K ?   �K .   =L 6   lL :   �L R   �L D   1M 1   vM =   �M Z   �M 4   AN �   vN o   �N 
   kO    vO �   �O    �P    �P $   �P 0   �P    Q 8   /Q !   hQ    �Q    �Q    �Q    �Q    �Q 
   �Q 	   �Q 
   R    R ;   R    PR    WR 	   `R     jR 	   �R 1   �R    �R    �R 	   �R    �R    �R )   �R    $S 0   ,S    ]S    {S    �S    �S    �S 1   �S 
   �S    T D   T    YT    jT    �T &   �T    �T    �T :   �T �   0U   �U �   �V <  �W l  �X �   I[    \ A   "\ %   d\    �\    �\    �\    �\ R   �\ �   $] g   �] �   %^ 9   �^ [   _ O   ]_ �   �_ �   b` g  "a �   �b o   %c '   �c $   �c    �c    �c    d    d    <d    \d    ed    �d    �d $   �d #   �d    �d 0   e    Ee %   Xe    ~e    �e    �e    �e    �e    �e 4   	f +   >f �  jf    h    h    )h 7   /h    gh �   nh q   �h F   ci U   �i i    j    jj    rj    zj �   }j    `k    fk A   �k �   �k (   �l    �l �   �l E   �m �  n �   �r   �s   �t �  �u d   Sw �  �w �   Yy   ;z ]  H|    �} 
   �} j   �} {   <~ n   �~ s   ' �   � c    � �   �� /   %� Q   U�    ��    �� w   Ӂ *   K�    v�    �    �� /   �� �   ނ    �� N   �� ~   �� $   x� 6   �� f   Ԅ �   ;�   � %   � /   *�   Z�    w� 
   ��    ��    ��    É 2   ҉ C  � %   I�    o�    ��    ��   �� =   Ď    �    � �   %�    ��    ď 9   ׏ $   �    6� .   C� �   r� ,   C� '   p� �   �� +   �    K�    g� '   x� \   �� �   ��    �� d   ٓ B  >� �   �� �   <� "   ͖ �  � o   �� #   � T   )� &   ~� /  ��   ՚ *   � f   �   �� "  �� 4   �� D   �    0� �  P� L   �� >   B� 4   �� E   �� A   ��   >� )   [� &   �� S   �� k    �    l� "   {� ,   �� M   ˥ �  � �  �� �  )� z   ˯ [   F�    �� +   ��   ۰ �  � ;  s� 5   �� �  � �  �� ;   (� /   d� �  �� �  ��    ��    �� A   ��    �� �   �     �� A  � �  Z�   �    �� �   �� X   n� C   �� �   �   �� �  �� 3  m� E  �� �   �� �  �� |   |�   �� �   � w   �� 5  h� `   �� J   �� %   J�    p� 5   �� E   �� =   � �   B� f   �� 	   7� N   A� �  �� M   �� �  �� �   `� �   =� S   1� 8   �� /   ��    �� <   	� :   F� U   �� <   �� �  � 3   �� "  ��    �� )  � �   A� �   �� �  �� J  �� 9   	� �   C� 	   � 5  � �   F� B   � �  R�    � !   � #   3� $   W� .   |�    ��    �� �   �� $  W� �  |�    �    � -   &� &  T� �  {� �   Q� �   :�    ��    �� $   �� *   $� !   O�    q�    �� d   �� O   �� ?  N� �   � 3   , �   ` �   * z   � �   F �    �   �    > �   M s   �    Y �   Z q   	    s	 R  �	 �   �
 k   w �   � �   } a   
    r
 Q   �
 �   �
 �   � Y   |   �    � �  � n   � �   [ �   �    � X  � �  ' *   � 4   �    
     �   1    �   E    �    �        �   ! �   " 4  # �   7$ �  �$ �   �& 2  G' -  z)   �* 
   (, 
   6, 
   D, �   R, 
   2- 
   @- t   N- ~   �- G  B. 
   �/ �   �/ m   �0 �   �0 Z   �1 �   �1 �   �2 
   $3 �   23 
   4 
   -4 
   ;4 !  I4 �   k7   M8 #   V;    z;    �; D  �; 7   �< I   $= �  n=   ? E   @    K@ -   [@ S   �@ T   �@ W   2A :   �A :   �A �   B /   �C j  �C �  +F �   �G �   �H �   >I �  J   �K �  �N +  dR �   �S   zT     }U &  �U *   �W W  �W �  HZ �  �[ �   �] �  �^ +  D` �   pa �   Wb @   0c Y   qc    �c H   �c    2d    Cd    _d a  }d    �e �   �e R   xf    �f    �f    �f 0   �f �   0g k   �g e   dh X  �h D   #j    hj    �j ,   �j 7   �j     k 	   k    k 	   %k    /k 	   8k    Bk 	   Kk u   Uk �   �k    }l     �l    �l     �l    �l     m    1m     Qm    rm )   �m 1   �m �   �m #   �n L   �n �   &o �   �o !  �p 
  �q �  �r *  �t   �u 4   �w �   x H   �x ~   >y �   �y   Zz y  y{ �   �| �   �}    -~ �   ?~ �    �   �    ��     ��    �� "   ր    �� -   �    =� *   J�    u�     ~� "   �� +       �    �     &�    G�    N�    f�    y� 2   ��    �� H   �� D   � �   I� �   � I   � 1   -� �   _� �   )� �   �� G   :� D   �� 4   Ǉ M   �� q   J� 8   �� l  �� �  b� �   � .   �� }   �� �   4� H   � �  N� �  ؐ �   Z� }   X� ,   ֓ 2  �   6� �   >� G   �� �   ?� �  � ,   l� W   �� /   � �   !� �   �� �   Y�    �    �    &� 2   -� �   `� [   U� 5   �� S   � /   ;� <   k� (   �� l   ў 1   >� �   p� k   �� �  b� d   �� �   b� +  � N   � 4   h� 3   �� 4   Ѥ 3   � 4   :� 3   o� 4   �� T   إ w   -� G  �� j   � U   X� Z   �� D   	�    N� �   d� �   �� R  �� =  �    P� �   \� 8   )�    b� �  �� 3   *� J   ^� �   �� O   0� �   �� �   � }   ȴ H   F� ?   �� �   ϵ E   �� A   � �   F� I  � w   5�   �� �   �� �   R� �   �� ?   � o   1� �   �� &   o� �   �� <   #� P  `� O   �� v   � I   x� F   �� w  	� f   �� p   �� �   Y� �   @� �   �� �   �� �   j�    V�    c� �   �� h   <�    �� /   ��    ��    	�    )� 1   E� �   w� {   .� &   �� 5   �� =   � E   E� Y   �� �   �� Y   �� �    � �   �� =   �� �   �� �  �� E   *� g   p�    �� ]   �� j   V� %   �� �   �� �   �� B   m� d   ��    � E   2� g   x�    �� �    � 3   ��    �� G   � s   X� K  �� }   � &   �� &   �� }   �� &   b� �   �� [   ,�   �� �   ��    t� t   �� #  � >   )� �   h� r   W� A   �� �   � G  ��    ��    �    � &   	� B   0� Q   s� �   �� +   M�    y�    �� 8   �� �   �� �   j� 
   �� �   �� �   �    � J   � R   i� &   �� $   �� �   � h  �� �   �    ��    ��   �� �  �� �   ��    L� B  f� �  �� _   1� �   �� /   N� �  ~� \   T� �   ��    V� +   u�   �� 7   �� u   �� �   b� �   3� �   �� >   �  �   �  g   � )   � �    v   � @    ]   ^ H   � �    �   �   { I   � )  � !  � 2  !
 R   T .  � �  �
 V  � "   J  $ S   o )   �   � K   � L  A �   � ~   ;    � /   � -   	   / N  9 8  � �  �!   �# �   �% �   :& {   �& \   7' X   �' �   �' �   �( 6   `) :   �) I   �) /   * -   L* a   z* �   �* Y   �+    �+ �   �+ y  �, 
  	. $   / &   9/ 
   `/ |   k/ �   �/ ~   0   �0 �   2 %   �2 S   �2 �  43    �4 �   �4 J  �5 �  : �   �; >   J<    �<    �<    �< q   �< A   = �   P= �  > �   �?    g@    �@ .   �@ 4   �@ �   A    �A M   �A    
B >   B L   ]B )   �B 
   �B �   �B    oC 0   �C <   �C @   �C    /D �   3D M  E �   fF �   �F ~   �G �  H    �I +   �I 2  �I b  !K L  �L    �M �   �M �   �N o   BO �   �O �   jP �    Q %   �Q �   �Q    �R    �R    �R    �R    �R $   �R 
   "S <   0S $   mS    �S >   �S >   �S >   T ,   ^T C   �T 7   �T 3   U 1   ;U !   mU b   �U ;   �U    .V �   AV U   �V �   W V   �W    !X -   7X A   eX :   �X "   �X �   Y �   �Y �   DZ �   [    �\ %   �\ .   ]    B] a   X] 	   �]    �] *   �] J  ^ "   Z_ &   }_    �_ 
   �_ 	   �_ f   �_ )   '` �  Q`    Bb $   _b    �b +   �b /   �b $   �b 0   !c 2   Rc 6   �c L   �c G   	d    Qd �   Xd P   �d    Je    fe L   we z   �e B   ?f    �f D   g �   Gg �   h |   �h    Bi 0   Qi 	   �i     �i )   �i 2   �i �  
j �   �l *   �m    �m @   �m    �m =   n    Jn    On �   _n ]   o d  vo 0   �p :   q �   Gq :   r 8   Qr .   �r 1   �r L   �r >   8s    ws    �s P   �s )   �s C   #t f   gt �  �t j   �v �   �v    ww B   �w �   :x ,   )y    Vy �   ny )   �y ;   (z :   dz a   �z j   { :   l{ �   �{    �|    �| 2   �|     } "   }    <} 4   \} "   �}    �} �   �} �  q~ ?   1� H   q� }  ��   8� ,   O�     |�    ��    �� &   ��    �    ��    �    (�    :�    M�    \� 
   m�    {�    ��    ��    ��    �� I   ̄ W  � �  n� |  � "  �� �  �� �  t�   �    *� �  H� 0   !� i  R� f  �� �  #� =  � �   � G   ��   �� k   �    t� c   �� }   � �   o� �   � +  �� �   �� ]  ��   ��    �   (� T  F� �   �� "  G� !   j� p  �� �  �� b  �� �  	� 
   � �   �� �   �� �  b� �   �� �   � �   �� (   �� =   ��    � r   � f   �    � �   � n   ܾ �   K� 1   �� �   0� �   � w   �� �   m� z   	� s   �� �   ��    �� �   !� s   �� �   _�   7�    @� >   Q� N   �� �   �� G   �� 
   ��    �� r   �� *   e� "   ��    �� j   �� e   -� S   �� 1   ��    � 
   &�    1� 	   7� �   A� �   �� �   �� h    �    �� 3   �� W   ��    �    7�    P�    _�    n�    {�    ��    ��    ��    ��    �� %   ��    	�    !� &   =�    d�    ��    ��    ��    �� <   �� +   � 3   J� �   ~� #   C� �   g� ;  � 
   A�    L�    h�    {�    ��    �� 
   �� 5  �� �   �� }   ��    � 7   3� �   k�    G� �   _� �   L� ?   +� (   k�    �� w  �� �   -� b  �� �   � 0   ��   � -   � .   D� �   s�    �� D   ��   D� �   ]� G  -� �   u�    	�    #�    ,� 
   L� '   W�    �    ��    �� �   �� �   �� �   �� ?  �� z  �� �   ;� i   � �  k� �  � u  ��   � �  #� 8  ��    �    �   ?� J  F� K  ��   � �   � S  � )   !	 c   K	 �   �	 n  �
 t   

 �  
        ) (   < �   e l   e �   � I   { �   � h   ^ �   � L   � 4  � y    ;   | l   � C  % 4   i  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: mn
Language-Team: mn <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library бол алдартай (мөн хууль бус) номын сан юм. Тэд Library Genesis цуглуулгыг авч, хялбархан хайх боломжтой болгосон. Үүнээс гадна, тэд шинэ номын хувь нэмэр оруулахыг урамшуулж, хэрэглэгчдийг янз бүрийн давуу талуудаар урамшуулан, шинэ номын хувь нэмэр оруулахад маш үр дүнтэй болсон. Одоогоор тэд эдгээр шинэ номыг Library Genesis руу буцаан оруулдаггүй. Мөн Library Genesis-ээс ялгаатай нь, тэдний цуглуулгыг хялбархан толь бичиглэх боломжгүй болгосон нь өргөн хүрээний хадгалалтыг саатуулдаг. Энэ нь тэдний бизнесийн загварт чухал ач холбогдолтой, учир нь тэд цуглуулгаа бөөнөөр нь (өдөрт 10-аас дээш ном) хандахад мөнгө авдаг. Бид хууль бус номын цуглуулгад бөөнөөр хандахад мөнгө авах талаар ёс суртахууны дүгнэлт гаргадаггүй. Z-Library нь мэдлэгт хандах боломжийг өргөжүүлэх, илүү олон номыг эх сурвалж болгоход амжилттай байсан нь эргэлзээгүй. Бид зүгээр л өөрсдийн үүргээ гүйцэтгэхээр энд байна: энэ хувийн цуглуулгыг урт хугацаанд хадгалахыг баталгаажуулах. - Анна болон баг (<a %(reddit)s>Reddit</a>) Анхны Pirate Library Mirror хувилбарт (EDIT: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн), бид Z-Library, хууль бус томоохон номын цуглуулгын толь бичгийг бүтээсэн. Сануулахад, энэ бол бидний анхны блог нийтлэлд бичсэн зүйл: Энэ цуглуулга 2021 оны дунд үеэс эхэлсэн. Энэ хооронд Z-Library гайхалтай хурдтай өсч байна: тэд ойролцоогоор 3.8 сая шинэ ном нэмсэн. Тэнд зарим давхардал байгаа нь мэдээж, гэхдээ ихэнх нь үнэхээр шинэ ном эсвэл өмнө нь оруулсан номын илүү чанартай сканнерууд юм шиг санагдаж байна. Энэ нь ихэвчлэн Z-Library-ийн сайн дурын модераторуудын тоо нэмэгдсэн, мөн давхардлыг арилгах бөөнөөр нь оруулах системийн ачаар юм. Бид тэднийг эдгээр амжилтанд хүрсэнд баяр хүргэж байна. Бид Z-Library-д манай сүүлийн толь бичгээс 2022 оны наймдугаар сар хүртэл нэмэгдсэн бүх номыг авсан гэдгээ зарлахдаа баяртай байна. Мөн бид анх удаа алгассан зарим номыг дахин хуссан. Нийтдээ энэ шинэ цуглуулга 24TB орчим бөгөөд энэ нь өмнөхөөсөө (7TB) хамаагүй том юм. Манай толь бичиг одоо нийт 31TB байна. Дахин хэлэхэд, бид Library Genesis-тэй давхардлыг арилгасан, учир нь энэ цуглуулгын хувьд аль хэдийн torrents байгаа. Шинэ цуглуулгыг шалгахын тулд Pirate Library Mirror руу очно уу (EDIT: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн). Тэнд файлууд хэрхэн бүтэцлэгдсэн, өнгөрсөн удаагаас хойш юу өөрчлөгдсөн талаар илүү дэлгэрэнгүй мэдээлэл бий. Бид эндээс холбоос өгөхгүй, учир нь энэ бол хууль бус материал агуулаагүй блог вэбсайт юм. Мэдээжийн хэрэг, үржүүлэх нь бидэнд туслах гайхалтай арга юм. Манай өмнөх torrents багцыг үржүүлж байгаа бүх хүнд баярлалаа. Бид эерэг хариу үйлдэлд талархаж, мэдлэг, соёлыг энэ ер бусын аргаар хадгалахыг хүсдэг олон хүн байгаад баяртай байна. Pirate Library Mirror-д 3 шинэ ном нэмэгдлээ (+24TB, 3.8 сая ном) TorrentFreak-ийн хамтрагч нийтлэлүүдийг уншина уу: <a %(torrentfreak)s>нэгдүгээр</a>, <a %(torrentfreak_2)s>хоёрдугаар</a> - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) TorrentFreak-ийн хамтрагч нийтлэлүүд: <a %(torrentfreak)s>эхний</a>, <a %(torrentfreak_2)s>хоёр дахь</a> Тун удахгүй, “сүүдрийн номын сангууд” үхэж байсан. Шүүхийн нэхэмжлэлийн улмаас шинжлэх ухааны өгүүллийн асар том хууль бус архив болох Sci-Hub шинэ бүтээл хүлээн авахаа больсон. “Z-Library”, хамгийн том хууль бус номын сангийн үүсгэн байгуулагчид нь зохиогчийн эрхийн гэмт хэргийн хэргээр баривчлагдсан. Тэд баривчлагдахаас гайхалтайгаар зугтаж чадсан ч тэдний номын сан аюулд ороогүй. Зарим орнууд үүнийг аль хэдийн хэрэгжүүлж байна. TorrentFreak <a %(torrentfreak)s>мэдээлсэн</a> нь Хятад болон Япон улс AI-ийн онцгой зөвшөөрлийг зохиогчийн эрхийн хуулиндаа оруулсан байна. Энэ нь олон улсын гэрээтэй хэрхэн уялдаж байгаа нь бидэнд тодорхойгүй байгаа ч, энэ нь дотоодын компаниудад хамгаалалт өгч байгаа нь бидний харж буй зүйлийг тайлбарлаж байна. Аннагийн Архивын хувьд — бид ёс суртахууны итгэл үнэмшилд тулгуурлан нууцлаг ажлаа үргэлжлүүлнэ. Гэсэн хэдий ч бидний хамгийн их хүсэл бол гэрэлд гарч, нөлөөллөө хууль ёсны дагуу өргөжүүлэх явдал юм. Зохиогчийн эрхийг шинэчлэн сайжруулна уу. Z-Library хаагдах үед би түүний бүх номын санг нөөцөлж, байрлуулах платформ хайж байсан. Энэ нь Аннагийн Архивыг эхлүүлэх урам зориг болсон: өмнөх санаачлагуудын ар дахь эрхэм зорилгыг үргэлжлүүлэх. Бид түүнээс хойш дэлхийн хамгийн том сүүдрийн номын сан болж, ном, шинжлэх ухааны өгүүлэл, сэтгүүл, сонин болон бусад олон төрлийн 140 сая гаруй зохиогчийн эрхтэй текстийг байршуулсан. Миний баг болон би үзэл сурталчид. Эдгээр файлуудыг хадгалах, байрлуулах нь ёс зүйтэй гэж бид итгэдэг. Дэлхийн номын сангууд санхүүжилтийн хасалттай тулгарч байгаа бөгөөд бид хүн төрөлхтний өвийг корпорациудад даатгаж чадахгүй. Дараа нь хиймэл оюун ухаан гарч ирсэн. Бараг бүх томоохон LLM бүтээдэг компаниуд бидэнтэй холбоо барьж, манай өгөгдлөөр сургалт хийхийг хүссэн. АНУ-д төвтэй ихэнх (гэхдээ бүгд биш!) компаниуд манай ажлын хууль бус шинж чанарыг ойлгосны дараа дахин бодсон. Харин эсрэгээрээ, Хятадын компаниуд манай цуглуулгыг хууль ёсны байдалд нь санаа зоволгүйгээр баяртайгаар хүлээн авсан. Энэ нь Хятад улс бараг бүх томоохон олон улсын зохиогчийн эрхийн гэрээнд гарын үсэг зурсан үүрэгтэй гэдгийг харгалзан үзэхэд онцгой юм. Бид ойролцоогоор 30 компанид өндөр хурдны хандалт өгсөн. Тэдний ихэнх нь LLM компаниуд бөгөөд зарим нь манай цуглуулгыг дахин худалдах өгөгдлийн зуучлагчид юм. Ихэнх нь Хятадынх боловч бид АНУ, Европ, Орос, Өмнөд Солонгос, Японы компаниудтай хамтран ажилласан. DeepSeek <a %(arxiv)s>хүлээн зөвшөөрсөн</a> нь өмнөх хувилбар нь манай цуглуулгын хэсэг дээр сургалт хийсэн боловч тэдний хамгийн сүүлийн загварын талаар (магадгүй манай өгөгдлөөр сургалт хийсэн) нууцлаг хэвээр байна. Барууны орнууд LLM болон эцэст нь AGI-ийн уралдаанд тэргүүлэхийг хүсч байвал зохиогчийн эрхийн талаархи байр сууриа эргэн харах хэрэгтэй бөгөөд удахгүй. Та манай ёс зүйн асуудалтай санал нийлж байгаа эсэхээс үл хамааран энэ нь одоо эдийн засгийн, бүр үндэсний аюулгүй байдлын асуудал болж байна. Бүх хүчний блокууд хиймэл супер эрдэмтэд, супер хакерууд, супер цэргийн хүчийг бүтээж байна. Мэдээллийн эрх чөлөө эдгээр орнуудын хувьд амьд үлдэх асуудал болж байна — бүр үндэсний аюулгүй байдлын асуудал. Манай баг дэлхийн өнцөг булан бүрээс ирсэн бөгөөд бид тодорхой чиг баримжаа байхгүй. Гэхдээ бид зохиогчийн эрхийн хуулиуд хатуу байдаг орнуудад энэ оршихуйн аюулыг ашиглан тэдгээрийг шинэчлэхийг уриалж байна. Тэгэхээр юу хийх вэ? Манай анхны зөвлөмж энгийн: зохиогчийн эрхийн хугацааг богиносгох. АНУ-д зохиогчийн эрхийг зохиогчийн үхлээс хойш 70 жилийн хугацаанд олгодог. Энэ бол утгагүй зүйл. Бид үүнийг патенттай нийцүүлж, өргөдөл гаргаснаас хойш 20 жилийн хугацаанд олгох боломжтой. Энэ нь ном, өгүүлэл, хөгжим, урлаг болон бусад бүтээлч бүтээлийн зохиогчдод өөрсдийн хүчин чармайлтын төлөө (киноны дасан зохицол гэх мэт урт хугацааны төслүүдийг оруулаад) бүрэн нөхөн төлбөр авахад хангалттай хугацаа байх ёстой. Тэгвэл, бодлого боловсруулагчид хамгийн багадаа текстүүдийг олон нийтэд хадгалах, түгээх тусгай зөвшөөрлийг оруулах ёстой. Хувь хүний хэрэглэгчдээс алдагдах орлого гол асуудал бол хувийн түвшний түгээлтийг хориглосон хэвээр байж болно. Үүний оронд өргөн цар хүрээтэй архивыг удирдах чадвартай хүмүүс — LLM-уудыг сургаж буй компаниуд, номын сан болон бусад архивууд — эдгээр онцгой зөвшөөрлөөр хамрагдах болно. Зохиогчийн эрхийн шинэчлэл нь үндэсний аюулгүй байдалд зайлшгүй шаардлагатай Товчхондоо: Хятадын LLM-ууд (DeepSeek-ийг оруулаад) миний хууль бус ном, өгүүллийн архив дээр сургалт хийсэн — дэлхийн хамгийн том. Барууны орнууд үндэсний аюулгүй байдлын үүднээс зохиогчийн эрхийн хуулийг шинэчлэх шаардлагатай. Дэлгэрэнгүй мэдээллийг <a %(all_isbns)s>эх блогийн бичлэгт</a> үзнэ үү. Бид үүнийг сайжруулах сорилтыг гаргасан. Эхний байранд $6,000, хоёрдугаар байранд $3,000, гуравдугаар байранд $1,000 шагнал олгоно. Хариу үйлдэл маш их байсан тул бид шагналын санг бага зэрэг нэмэгдүүлж, дөрвөн гуравдугаар байранд тус бүр $500 олгохоор шийдлээ. Ялагчид доор байгаа бөгөөд бүх оролцогчдыг <a %(annas_archive)s>энд</a> үзэх эсвэл манай <a %(a_2025_01_isbn_visualization_files)s>нэгдсэн торрент</a>-ийг татаж аваарай. Эхний байр $6,000: phiresky Энэ <a %(phiresky_github)s>илгээмж</a> (<a %(annas_archive_note_2951)s>Gitlab сэтгэгдэл</a>) бол бидний хүссэн бүх зүйл, түүнээс ч илүү! Бид ялангуяа маш уян хатан дүрслэл сонголтуудыг (захиалгат шейдерүүдийг дэмждэг) дуртай байсан, гэхдээ урьдчилсан тохиргооны бүрэн жагсаалттай. Бид мөн бүх зүйл хэрхэн хурдан, жигд байгааг, энгийн хэрэгжилтийг (арын системгүй), ухаалаг мини газрын зургийг, тэдний <a %(phiresky_github)s>блог бичлэгт</a> өргөн тайлбарласан байдлыг үнэлсэн. Гайхалтай ажил, сайн зохистой ялагч! - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Бидний зүрх сэтгэл талархлаар дүүрэн байна. Онцлох санаанууд Ховор байдлыг илэрхийлэх тэнгэр баганадсан барилгууд Datasets-ийг харьцуулах олон гулсагч, яг л DJ шиг. Номын тоотой хэмжээсийн бар. Гоёмсог шошгууд. Сонирхолтой үндсэн өнгөний схем болон дулааны зураглал. Өвөрмөц газрын зураг харагдац болон шүүлтүүрүүд Тэмдэглэлүүд, мөн шууд статистик Шууд статистик Бидний онцгойлон таалагдсан зарим санаа, хэрэгжүүлэлтүүд: Бид хэсэг хугацаанд үргэлжлүүлж болох ч энд зогсоё. Бүх оруулгуудыг <a %(annas_archive)s>энд</a> үзэх эсвэл манай <a %(a_2025_01_isbn_visualization_files)s>нэгдсэн torrent</a>-ийг татаж аваарай. Маш олон оруулга, тус бүр нь UI эсвэл хэрэгжүүлэлтэд өвөрмөц өнцөг авчирдаг. Бид ядаж эхний байрын оруулгыг үндсэн вэбсайтдаа оруулах болно, магадгүй заримыг нь ч бас. Мөн хамгийн ховор номыг тодорхойлох, баталгаажуулах, дараа нь архивлах үйл явцыг хэрхэн зохион байгуулах талаар бодож эхэлсэн. Энэ талаар илүү ихийг хүлээж байгаарай. Оролцсон бүх хүнд баярлалаа. Ийм олон хүн санаа тавьж байгаад гайхалтай байна. Datasets-ийг хялбархан солих боломжтой, хурдан харьцуулалт хийхэд тохиромжтой. Бүх ISBN-үүд CADAL SSNOs CERLALC мэдээллийн алдагдал DuXiu SSIDs EBSCOhost-ийн eBook Index Google Ном Goodreads Интернет Архив ISBNdb ISBN Олон улсын хэвлэлийн бүртгэл Libby Аннагийн Архив дахь файлууд Nexus/STC OCLC/Worldcat OpenLibrary Оросын Улсын Номын Сан Транторын Эзэнт Гүрний Номын Сан Хоёрдугаар байр $3,000: hypha “Төгс дөрвөлжин болон тэгш өнцөгтүүд нь математик хувьд тааламжтай боловч газрын зурагт илүү сайн орон нутгийн байршлыг хангадаггүй. Эдгээр Хилберт эсвэл сонгодог Мортон дахь асимметри нь алдаа биш харин онцлог гэж би итгэдэг. Италийн алдартай гутал хэлбэртэй дүрс нь газрын зураг дээр даруй танигдах боломжийг олгодог шиг, эдгээр муруйн өвөрмөц "онцлог" нь танин мэдэхүйн тэмдэглэгээ болж болох юм. Энэ өвөрмөц байдал нь орон зайн ой санамжийг сайжруулж, хэрэглэгчдэд өөрсдийгөө чиглүүлэхэд тусалж, тодорхой бүс нутгийг олох эсвэл хэв маягийг анзаарахад хялбар болгох боломжтой.” Өөр нэг гайхалтай <a %(annas_archive_note_2913)s>илгээмж</a>. Эхний байраас уян хатан биш ч, бид үнэндээ түүний макро түвшний дүрслэлийг эхний байраас илүү дуртай байсан (орон зайг дүүргэх муруй, хил хязгаар, шошго, онцлох, панорама, томруулах). Жо Дэвисийн <a %(annas_archive_note_2971)s>сэтгэгдэл</a> бидэнд таалагдсан: Мөн дүрслэх, үзүүлэх олон сонголттой, маш жигд, ойлгомжтой UI. Хатуу хоёрдугаар байр! - Анна болон баг (<a %(reddit)s>Reddit</a>) Хэдэн сарын өмнө бид <a %(all_isbns)s>$10,000 шагнал</a> зарлаж, ISBN орон зайг харуулсан өгөгдлийн хамгийн сайн дүрслэлийг хийхийг хүссэн. Бид аль хэдийн архивласан файлуудыг харуулахыг онцолсон бөгөөд дараа нь хэдэн номын сан ISBN-ийг эзэмшдэгийг харуулсан өгөгдлийн багцыг оруулсан (ховор байдлын хэмжүүр). Бид хариу үйлдлээр дүүрэн байлаа. Маш их бүтээлч байдал гарсан. Оролцсон бүх хүнд баярлалаа: таны эрч хүч, урам зориг халдвартай! Эцэст нь бид дараах асуултуудад хариулахыг хүссэн: <strong>дэлхий дээр ямар номнууд байдаг, бид аль хэдийн хэдийг нь архивласан, дараагийн анхаарах номнууд юу вэ?</strong> Эдгээр асуултуудад олон хүн анхаарал тавьж байгааг харахад сайхан байна. Бид өөрсдөө үндсэн дүрслэлээс эхэлсэн. 300kb-аас бага хэмжээтэй энэ зураг нь хүн төрөлхтний түүхэнд хамгийн том, бүрэн нээлттэй “номын жагсаалт”-ыг товчхон харуулж байна: Гуравдугаар байр $500 #1: maxlion Энэ <a %(annas_archive_note_2940)s>илгээмжид</a> бид янз бүрийн төрлийн үзэл бодлыг үнэхээр дуртай байсан, ялангуяа харьцуулалт болон хэвлэгчийн үзэл бодол. Гуравдугаар байр $500 #2: abetusk Хамгийн боловсронгуй UI биш ч, энэ <a %(annas_archive_note_2917)s>илгээмж</a> олон шаардлагыг хангаж байна. Бид ялангуяа түүний харьцуулалтын онцлогийг үнэлсэн. Гуравдугаар байр $500 #3: conundrumer0 Эхний байрын адил, энэ <a %(annas_archive_note_2975)s>илгээмж</a> нь уян хатан байдлаараа биднийг гайхшруулсан. Эцэст нь энэ нь гайхалтай дүрслэл хэрэгсэл болгодог зүйл: хүчирхэг хэрэглэгчдэд зориулсан хамгийн их уян хатан байдал, дундаж хэрэглэгчдэд энгийн байдлыг хадгалах. Гуравдугаар байр $500 #4: charelf Шагнал авах сүүлийн <a %(annas_archive_note_2947)s>илгээмж</a> нь нэлээд энгийн боловч бидэнд үнэхээр таалагдсан зарим өвөрмөц онцлогтой. Тэд хэрхэн олон тооны өгөгдлийн багц нь тодорхой ISBN-ийг хамардаг болохыг харуулсан нь бидэнд таалагдсан бөгөөд энэ нь алдартай/найдвартай байдлын хэмжүүр юм. Мөн харьцуулалтад тунгалаг байдлын гулсагчийг ашиглах энгийн боловч үр дүнтэй байдлыг үнэхээр үнэлсэн. $10,000 ISBN дүрслэлийн шагналын ялагчид Товчхондоо: Бид $10,000 ISBN дүрслэлийн шагналд гайхалтай бүтээлүүдийг хүлээн авсан. Ар тал Аннагийн Архив хүн төрөлхтний бүх мэдлэгийг нөөцлөх зорилгоо хэрхэн биелүүлэх вэ, хэрэв ямар номнууд байгаа талаар мэдэхгүй бол? Бидэнд TODO жагсаалт хэрэгтэй. Үүнийг зураглах нэг арга бол 1970-аад оноос хойш ихэнх орнуудад хэвлэгдсэн ном бүрт оноосон ISBN дугаарууд юм. Бүх ISBN оноолтыг мэддэг төвлөрсөн эрх мэдэл байхгүй. Үүний оронд, энэ нь тархсан систем бөгөөд орнууд тооны хүрээг авч, дараа нь томоохон хэвлэн нийтлэгчдэд бага хүрээг оноож, цаашид бага хэвлэн нийтлэгчдэд хүрээг хуваарилж болно. Эцэст нь номонд тусгай дугааруудыг оноодог. Бид ISBN-ийг <a %(blog)s>хоёр жилийн өмнө</a> ISBNdb-ээс хусаж эхэлсэн. Түүнээс хойш бид <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby гэх мэт олон metadata эх сурвалжуудыг хуссан. Бүрэн жагсаалтыг Аннагийн Архивын “Datasets” болон “Torrents” хуудсуудаас олж болно. Одоо бид дэлхийн хамгийн том, бүрэн нээлттэй, хялбар татаж авах боломжтой номын metadata (тэгэхээр ISBN) цуглуулгатай болсон. Бид хадгалалтын талаар яагаад анхаарал тавьдаг, яагаад одоо чухал цонхонд байгаа талаар <a %(blog)s>өргөн бичсэн</a>. Одоо бид ховор, анхаарал хандуулаагүй, өвөрмөц эрсдэлтэй номнуудыг тодорхойлж, тэдгээрийг хадгалах ёстой. Дэлхийн бүх номын сайн metadata байх нь үүнд тусалдаг. $10,000 шагнал Хэрэглээний хялбар байдал болон хэрхэн сайхан харагдаж байгааг нь хүчтэй авч үзнэ. Тухайн ISBN-үүдийн метадата, жишээлбэл, гарчиг болон зохиогчийг томруулж харах үед харуулах. Илүү сайн зай дүүргэх муруй. Жишээлбэл, эхний мөрөнд 0-ээс 4 хүртэл, дараагийн мөрөнд 5-аас 9 хүртэл (урвуугаар) зиг-заг хэлбэрээр — дахин давтагдана. Өөр өөр эсвэл тохируулж болох өнгөний схемүүд. Datasets-ийг харьцуулах тусгай үзэл. Бусад метадата сайн нийцэхгүй байгаа асуудлуудыг засах арга замууд (жишээлбэл, маш өөр гарчигтай). ISBN эсвэл хүрээний талаар тайлбар бичиж зургийг тэмдэглэх. Ховор эсвэл эрсдэлтэй номыг таних ямар нэгэн эвристик. Таны гаргаж чадах ямар ч бүтээлч санаа! Код Эдгээр зургийг үүсгэх код болон бусад жишээг <a %(annas_archive)s>энэ сан</a>-д олох боломжтой. Бид 75MB (шахсан) орчим шаардлагатай ISBN мэдээллийг багтаасан нягт өгөгдлийн форматыг гаргаж ирсэн. Өгөгдлийн форматын тайлбар болон үүнийг үүсгэх кодыг <a %(annas_archive_l1244_1319)s>энд</a> олох боломжтой. Шагналд та үүнийг ашиглах шаардлагагүй, гэхдээ эхлэхэд хамгийн тохиромжтой формат байх магадлалтай. Та манай метадата-г хүссэнээрээ хувиргаж болно (гэхдээ таны бүх код нээлттэй эх сурвалжтай байх ёстой). Таны гаргаж ирэх зүйлсийг харахыг тэсэн ядан хүлээж байна. Амжилт хүсье! Энэ репог салаалж, энэ блог бичлэгийн HTML-ийг засварлаарай (манай Flask backend-ээс бусад backend-уудыг зөвшөөрөхгүй). Дээрх зургийг жигд томруулж, ISBN-үүд рүү бүхэлд нь томруулах боломжтой болго. ISBN-үүд дээр дарснаар Аннагийн Архив дээр metadata хуудас эсвэл хайлт руу очих ёстой. Та бүх өөр datasets-ийн хооронд шилжих боломжтой хэвээр байх ёстой. Улсын хүрээ болон хэвлэн нийтлэгчийн хүрээг хулганаар хөвөхөд тодруулах ёстой. Жишээ нь, <a %(github_xlcnd_isbnlib)s>isbnlib дахь data4info.py</a>-г улсын мэдээлэлд ашиглаж болно, мөн манай “isbngrp” хусалтыг хэвлэн нийтлэгчдэд ашиглаж болно (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Энэ нь ширээний болон гар утас дээр сайн ажиллах ёстой. Энд судлах зүйл их байгаа тул бид дээрх дүрслэлийг сайжруулахад зориулж шагнал зарлаж байна. Ихэнх шагналуудаас ялгаатай нь энэ нь цаг хугацаатай хязгаарлагдсан. Та нээлттэй эхийн кодоо 2025-01-31 (23:59 UTC) гэхэд <a %(annas_archive)s>илгээх</a> ёстой. Хамгийн сайн илгээмж $6,000, хоёрдугаар байр $3,000, гуравдугаар байр $1,000 авна. Бүх шагналуудыг Monero (XMR) ашиглан олгоно. Доор хамгийн бага шаардлагууд байна. Хэрэв ямар ч илгээмж шаардлагыг хангахгүй бол бид зарим шагналуудыг олгож магадгүй, гэхдээ энэ нь бидний үзэмжээр байх болно. Нэмэлт оноо авахын тулд (эдгээр нь зүгээр л санаанууд — таны бүтээлч байдал чөлөөтэй байг): Та хамгийн бага шаардлагаас бүрэн хазайж, огт өөр дүрслэл хийж болно. Хэрэв үнэхээр гайхалтай бол шагналд тэнцэх боловч бидний үзэмжээр. Өөрийн салаалсан репо, нэгдэх хүсэлт эсвэл diff-ийн холбоосыг <a %(annas_archive)s>энэ асуудалд</a> сэтгэгдэл бичиж илгээнэ үү. - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Энэ зураг 1000×800 пиксел. Нэг пиксел нь 2,500 ISBN-ийг илэрхийлнэ. Хэрэв бид ISBN-ийн файлтай бол тэр пикселийг илүү ногоон болгоно. Хэрэв бид ISBN гаргасан гэдгийг мэдэж байгаа ч тохирох файл байхгүй бол илүү улаан болгоно. 300kb-аас бага хэмжээтэй энэ зураг нь хүн төрөлхтний түүхэнд хэзээ ч бүтээгдэж байгаагүй хамгийн том нээлттэй “номын жагсаалт”-ыг товчхон илэрхийлж байна (бүрэн шахсан хэдэн зуун GB). Номын нөөцлөлтөд их ажил үлдсэн байна (бид зөвхөн 16% байна). Бүх ISBN-ийг дүрслэх — 2025-01-31 гэхэд $10,000 шагнал Энэ зураг нь хүн төрөлхтний түүхэнд хэзээ ч бүтээгдэж байгаагүй хамгийн том нээлттэй “номын жагсаалт”-ыг илэрхийлж байна. Дүрслэх Тойм зурагнаас гадна бид олж авсан тусгай datasets-ийг бас харж болно. Тэдгээрийн хооронд шилжихийн тулд уналттай цэс болон товчлууруудыг ашиглаарай. Эдгээр зургууд дээр олон сонирхолтой хэв маяг харагдана. Яагаад шугам, блокийн зарим тогтмол байдал байдаг вэ, өөр өөр хэмжээнд үүсдэг юм шиг? Хоосон талбайнууд юу вэ? Яагаад зарим datasets ийм бөөгнөрсөн байдаг вэ? Эдгээр асуултуудыг уншигчдад дасгал болгон үлдээе. - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Дүгнэлт Энэ стандартаар бид хувилбаруудыг илүү аажмаар гаргаж, шинэ өгөгдлийн эх үүсвэрийг илүү хялбархан нэмэх боломжтой болно. Бид аль хэдийн хэд хэдэн сонирхолтой хувилбаруудыг төлөвлөж байна! Бусад сүүдрийн номын сангууд манай цуглуулгуудыг толин тусгал болгоход хялбар болно гэж найдаж байна. Эцсийн эцэст, бидний зорилго бол хүний мэдлэг, соёлыг үүрд хадгалах явдал юм, тиймээс илүү их давхардал байх тусам сайн. Жишээ Манай сүүлийн үеийн Z-Library хувилбарыг жишээ болгон авч үзье. Энэ нь хоёр цуглуулгаас бүрдэнэ: “<span style="background: #fffaa3">zlib3_records</span>” болон “<span style="background: #ffd6fe">zlib3_files</span>”. Энэ нь бидэнд metadata бичлэгүүдийг бодит номын файлуудаас тусад нь хусах, гаргах боломжийг олгодог. Иймээс бид metadata файлуудтай хоёр торрент гаргасан: Мөн бид “<span style="background: #ffd6fe">zlib3_files</span>” цуглуулгад зориулж хоёртын өгөгдлийн хавтас бүхий олон торрент гаргасан, нийт 62: <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> ажиллуулснаар дотор нь юу байгааг харж болно: Энэ тохиолдолд, энэ нь Z-Library-аас мэдээлэгдсэн номын metadata юм. Дээд түвшинд бид зөвхөн “aacid” болон “metadata”-тай, гэхдээ “data_folder” байхгүй, учир нь тохирох хоёртын өгөгдөл байхгүй. AACID нь үндсэн ID болгон “22430000”-г агуулдаг бөгөөд энэ нь “zlibrary_id”-аас авсан болохыг харж болно. Энэ цуглуулгын бусад AAC-ууд ижил бүтэцтэй байхыг хүлээж болно. Одоо <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> ажиллуулъя: Энэ нь AAC-ийн metadata нь илүү жижиг боловч энэ AAC-ийн ихэнх хэсэг нь өөр газар хоёртын файлд байрладаг! Эцэст нь, энэ удаад бид “data_folder”-тай байгаа тул тохирох хоёртын өгөгдөл <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> байршилд байхыг хүлээж болно. “Metadata” нь “zlibrary_id”-г агуулдаг тул үүнийг “zlib_records” цуглуулгын тохирох AAC-тай хялбархан холбож болно. Бид AACID-ээр дамжуулан олон янзаар холбож болох байсан — стандарт нь үүнийг заагаагүй. “Metadata” талбар нь өөрөө JSON байх шаардлагагүй гэдгийг анхаарна уу. Энэ нь XML эсвэл бусад өгөгдлийн форматыг агуулсан мөр байж болно. Та metadata мэдээллийг холбогдох хоёртын блобт хадгалж болно, жишээлбэл, хэрэв энэ нь их хэмжээний өгөгдөл бол. Өөр өөр файлууд болон metadata, аль болох анхны форматтай ойрхон. Nginx зэрэг вэб серверүүдээр шууд үйлчлэх боломжтой хоёртын өгөгдөл. Эх сурвалжийн номын сангууд дахь өөр өөр танигчид, эсвэл танигч байхгүй байх. Metadata болон файлын өгөгдлийн тусдаа хувилбарууд, эсвэл зөвхөн metadata хувилбарууд (жишээ нь, бидний ISBNdb хувилбар). Торрентуудаар дамжуулан түгээх, гэхдээ бусад түгээх аргуудын боломжтой (жишээ нь, IPFS). Өөрчлөгдөшгүй бүртгэлүүд, учир нь бидний торрентууд үүрд амьдрах болно гэж үзэх ёстой. Нэмэлт хувилбарууд / нэмэгдэж болох хувилбарууд. Машин уншиж, бичиж болохуйц, тохиромжтой, хурдан, ялангуяа бидний стек (Python, MySQL, ElasticSearch, Transmission, Debian, ext4) хувьд. Хүн уншихад хялбар, гэхдээ энэ нь машин унших чадвараас хоёрдогч. Манай цуглуулгуудыг стандарт түрээсийн seedbox-оор үржүүлэхэд хялбар. Загварын зорилтууд Бид файлуудыг диск дээр гараар хялбархан навигацлах эсвэл урьдчилан боловсруулалтгүйгээр хайх боломжтой байх талаар санаа зовдоггүй. Бид одоо байгаа номын сангийн програм хангамжтай шууд нийцтэй байх талаар санаа зовдоггүй. Хэн ч манай цуглуулгыг торрент ашиглан үржүүлэхэд хялбар байх ёстой ч файлуудыг ихээхэн техникийн мэдлэг, үүрэг хариуцлагагүйгээр ашиглах боломжтой гэж бид хүлээхгүй байна. Бидний үндсэн хэрэглээний тохиолдол бол өөр өөр цуглуулгуудаас файлууд болон холбогдох metadata-г түгээх явдал юм. Бидний хамгийн чухал анхаарах зүйлс: Зарим зорилтууд биш: Аннагийн Архив нь нээлттэй эх сурвалжтай тул бид өөрсдийн форматыг шууд ашиглахыг хүсэж байна. Хайлтын индексээ шинэчлэхдээ зөвхөн олон нийтэд нээлттэй замуудыг ашигладаг тул манай номын санг салаалах хэн ч хурдан ажиллаж эхлэх боломжтой. <strong>AAC.</strong> AAC (Аннагийн Архив Контейнер) нь <strong>metadata</strong> болон сонголтоор <strong>binary data</strong> агуулсан нэгэн зүйл бөгөөд эдгээр нь өөрчлөгдөшгүй байдаг. Энэ нь дэлхий даяар өвөрмөц танигчтай бөгөөд үүнийг <strong>AACID</strong> гэж нэрлэдэг. <strong>AACID.</strong> AACID-ийн формат нь: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Жишээлбэл, бидний гаргасан жинхэнэ AACID нь <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID хүрээ.</strong> AACID-ууд монотон өсөх цагийн тэмдэгтүүдийг агуулдаг тул бид үүнийг тодорхой цуглуулгын хүрээнүүдийг тэмдэглэхэд ашиглаж болно. Бид энэ форматыг ашигладаг: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, энд цагийн тэмдэгтүүд нь багтаасан байдаг. Энэ нь ISO 8601 тэмдэглэгээтэй нийцдэг. Хүрээнүүд тасралтгүй бөгөөд давхцаж болно, гэхдээ давхцсан тохиолдолд тухайн цуглуулгад өмнө нь гаргасан бичлэгүүдтэй ижил байх ёстой (учир нь AAC-ууд өөрчлөгдөшгүй). Алга болсон бичлэгүүдийг зөвшөөрөхгүй. <code>{collection}</code>: цуглуулгын нэр, үүнд ASCII үсэг, тоо, доогуур зураас орж болно (гэхдээ давхар доогуур зураас байхгүй). <code>{collection-specific ID}</code>: цуглуулга тусгай танигч, хэрэв боломжтой бол, жишээ нь Z-Library ID. Хасагдах эсвэл богиносгогдож болно. Хэрэв AACID нь 150 тэмдэгтээс хэтрэх бол заавал хасагдах эсвэл богиносгогдох ёстой. <code>{ISO 8601 timestamp}</code>: ISO 8601-ийн богино хувилбар, үргэлж UTC-д, жишээ нь <code>20220723T194746Z</code>. Энэ тоо нь гаргах бүртээ монотон өсөх ёстой, гэхдээ түүний нарийн утга нь цуглуулга бүрт өөр байж болно. Бид хусах эсвэл ID үүсгэх цагийг ашиглахыг санал болгож байна. <code>{shortuuid}</code>: UUID боловч ASCII-д шахагдсан, жишээ нь base57 ашиглан. Бид одоогоор <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python санг ашиглаж байна. <strong>Binary data хавтас.</strong> Нэг тодорхой цуглуулгын AAC-уудын хүрээний binary data бүхий хавтас. Эдгээр нь дараах шинж чанаруудтай: Сан нь заасан хүрээний бүх AAC-ийн өгөгдлийн файлуудыг агуулсан байх ёстой. Өгөгдлийн файл бүрийн нэр нь AACID байх ёстой (өргөтгөлгүй). Сангийн нэр нь <code style="color: green">annas_archive_data__</code> гэсэн угтвар бүхий AACID хүрээ байх ёстой бөгөөд ямар ч төгсгөлгүй байх ёстой. Жишээлбэл, манай бодит хувилбаруудын нэг нь <br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> гэсэн санг агуулдаг. Эдгээр хавтаснуудыг хэмжээний хувьд зохицуулах боломжтой байлгахыг зөвлөж байна, жишээлбэл, тус бүр нь 100GB-1TB-ээс хэтрэхгүй байх, гэхдээ энэ зөвлөмж цаг хугацааны явцад өөрчлөгдөж болно. <strong>Цуглуулга.</strong> Бүх AAC нь цуглуулгад хамаарагддаг бөгөөд энэ нь утга зүйн хувьд нийцтэй AAC-уудын жагсаалт юм. Хэрэв та metadata-ийн форматыг ихээхэн өөрчлөх бол шинэ цуглуулга үүсгэх шаардлагатай. Стандарт <strong>Metadata файл.</strong> Metadata файл нь нэг тодорхой цуглуулгын AAC-уудын хүрээний metadata-г агуулдаг. Эдгээр нь дараах шинж чанаруудтай: <code>data_folder</code> нь сонголттой бөгөөд энэ нь холбогдох binary data-г агуулсан binary data хавтасны нэр юм. Тухайн хавтас доторх холбогдох binary data-ийн файлын нэр нь бичлэгийн AACID юм. Бүх JSON объектууд нь дээд түвшинд дараах талбаруудыг агуулсан байх ёстой: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (сонголттой). Бусад талбаруудыг зөвшөөрөхгүй. Файлын нэр нь <code style="color: red">annas_archive_meta__</code> гэсэн угтвартай, <code>.jsonl.zstd</code> гэсэн төгсгөлтэй AACID хүрээ байх ёстой. Жишээлбэл, манай гаргасан нэг нь<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> гэж нэрлэгддэг. Файлын өргөтгөлөөр заасанчлан, файлын төрөл нь <a %(jsonlines)s>JSON Lines</a> бөгөөд <a %(zstd)s>Zstandard</a>-аар шахагдсан байдаг. <code>metadata</code> нь цуглуулгын утга зүйн дагуу дурын metadata юм. Энэ нь цуглуулгын дотор утга зүйн хувьд нийцтэй байх ёстой. <code style="color: red">annas_archive_meta__</code> угтвар нь таны байгууллагын нэрэнд тохируулан өөрчлөгдөж болно, жишээ нь <code style="color: red">my_institute_meta__</code>. <strong>“бичлэг” болон “файл” цуглуулгууд.</strong> Заншлын дагуу “бичлэг” болон “файл”-ыг өөр өөр цуглуулга болгон гаргах нь тохиромжтой байдаг тул тэдгээрийг өөр өөр хуваарийн дагуу гаргах боломжтой, жишээ нь хусах хурд дээр үндэслэн. “Бичлэг” нь зөвхөн metadata агуулсан цуглуулга бөгөөд номын гарчиг, зохиогчид, ISBN гэх мэт мэдээллийг агуулдаг бол “файл” нь жинхэнэ файлуудыг (pdf, epub) агуулсан цуглуулгууд юм. Эцэст нь бид харьцангуй энгийн стандарт дээр тохиролцсон. Энэ нь нэлээд сул, хэм хэмжээ тогтоогоогүй, хөгжүүлэлтийн шатандаа байгаа. <strong>Торрентууд.</strong> Metadata файлууд болон хоёртын өгөгдлийн хавтаснуудыг торрентуудад багцалж болно, нэг metadata файлд нэг торрент, эсвэл нэг хоёртын өгөгдлийн хавтаст нэг торрент. Торрентууд нь анхны файл/сангийн нэр дээр <code>.torrent</code> төгсгөлтэй байх ёстой. <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> дэлхийн хамгийн том сүүдрийн номын сан болж, хэмжээгээрээ бүрэн нээлттэй эх сурвалж, нээлттэй өгөгдөлтэй цорын ганц сүүдрийн номын сан болсон. Доор манай Datasets хуудасны хүснэгт (бага зэрэг өөрчлөгдсөн) байна: Бид үүнийг гурван аргаар гүйцэтгэсэн: Одоогийн нээлттэй өгөгдөлтэй сүүдрийн номын сангуудыг (Sci-Hub болон Library Genesis гэх мэт) толин тусгал болгох. Сүүдрийн номын сангуудыг илүү нээлттэй болгохыг хүсдэг ч цаг хугацаа, нөөц бололцоо дутмаг байсан (Libgen комикс цуглуулга гэх мэт) номын сангуудад туслах. Бөөнөөр хуваалцахыг хүсдэггүй номын сангуудыг хусах (Z-Library гэх мэт). (2) болон (3)-д зориулж бид одоо өөрсдөө нэлээд их хэмжээний торрент цуглуулгыг (100 гаруй TB) удирдаж байна. Одоог хүртэл бид эдгээр цуглуулгуудыг нэг удаагийн байдлаар хандаж ирсэн бөгөөд энэ нь тус бүрийн цуглуулгад зориулсан тусгай дэд бүтэц, өгөгдлийн зохион байгуулалт гэсэн үг юм. Энэ нь тус бүрийн хувилбарт ихээхэн нэмэлт ачаалал үүсгэж, илүү нэмэлт хувилбаруудыг хийхэд хэцүү болгодог. Тиймээс бид хувилбаруудаа стандартчилахаар шийдсэн. Энэ бол бидний стандартыг танилцуулж буй техникийн блог нийтлэл юм: <strong>Аннагийн Архив Контейнерууд</strong>. Аннагийн Архив Контейнерууд (AAC): дэлхийн хамгийн том сүүдрийн номын сангийн хувилбаруудыг стандартчилах Аннагийн Архив дэлхийн хамгийн том сүүдрийн номын сан болж, биднийг хувилбаруудаа стандартчилах шаардлагатай болгосон. 300GB+ номын хавтас гаргасан Эцэст нь бид жижиг гаргалгаа зарлахдаа баяртай байна. Libgen.rs салааг ажиллуулдаг хүмүүстэй хамтран бид бүх номын хавтаснуудаа torrents болон IPFS-ээр хуваалцаж байна. Энэ нь хавтаснуудыг үзэх ачааллыг илүү олон машинд хуваарилж, тэдгээрийг илүү сайн хадгалах болно. Олон (гэхдээ бүгд биш) тохиолдолд номын хавтаснууд нь файлуудын өөрсдийнх нь дотор багтсан байдаг тул энэ нь "гаралтай өгөгдөл" юм. Гэхдээ үүнийг IPFS-д байлгах нь Аннагийн Архив болон Library Genesis-ийн янз бүрийн салаануудын өдөр тутмын үйл ажиллагаанд маш их хэрэгтэй. Ердийнх шигээ та энэ гаргалгааг Pirate Library Mirror дээрээс олж болно (ЗАСВАР: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн). Бид үүнийг энд холбоослохгүй, гэхдээ та үүнийг амархан олох боломжтой. Z-Library-ийн сайн хувилбартай болсноор бид хурдаа бага зэрэг сулруулж чадна гэж найдаж байна. Энэ ажлын ачаалал нь тийм ч тогтвортой биш юм. Хэрэв та програмчлал, серверийн ажиллагаа эсвэл хадгалалтын ажилд туслах сонирхолтой байгаа бол бидэнтэй холбогдоорой. Хийх <a %(annas_archive)s>ажил их байна</a>. Сонирхол, дэмжлэгт тань баярлалаа. ElasticSearch руу шилжих Зарим асуулга маш удаан үргэлжилж, нээлттэй холболтуудыг бүгдийг нь эзэлдэг байв. Анхдагч байдлаар MySQL нь хамгийн бага үгийн урттай байдаг эсвэл таны индекс үнэхээр том байж болно. Хүмүүс “Ben Hur”-ийг хайж чадахгүй байна гэж мэдээлсэн. Хайлт зөвхөн санах ойд бүрэн ачаалагдсан үед л хурдан байсан бөгөөд энэ нь үүнийг ажиллуулахын тулд илүү үнэтэй машин авах шаардлагатай болсон бөгөөд эхлүүлэх үед индексийг урьдчилан ачаалах зарим командыг шаарддаг. Бид үүнийг цагаан зайгүй хэлнүүдийн хувьд илүү сайн <a %(wikipedia_cjk_characters)s>токенжуулалт</a>, шүүлтүүр/фасет, эрэмбэлэх, "та үүнийг хэлэх гэсэн үү" санал, автоматаар бөглөх гэх мэт шинэ функцуудыг бүтээхэд хялбархан өргөжүүлэх боломжгүй байсан. Манай <a %(annas_archive)s>тасалбаруудын</a> нэг нь манай хайлтын системтэй холбоотой асуудлуудын багц байв. Бид MySQL бүрэн текст хайлтыг ашигласан, учир нь бид бүх өгөгдлөө MySQL-д хадгалсан. Гэхдээ үүнд хязгаарлалт байсан: Олон мэргэжилтнүүдтэй ярилцсаны дараа бид ElasticSearch дээр тохиролцсон. Энэ нь төгс биш байсан (тэдний анхдагч “та үүнийг хэлэх гэсэн үү” санал, автоматаар бөглөх функцууд нь муу), гэхдээ ерөнхийдөө хайлтын хувьд MySQL-ээс хамаагүй дээр байсан. Бид үүнийг ямар нэгэн чухал өгөгдөлд ашиглах талаар <a %(youtube)s>тэгтлээ дуртай биш</a> байгаа ч (тэд ихээхэн <a %(elastic_co)s>ахиц дэвшил</a> гаргасан ч), ерөнхийдөө шилжилтэндээ сэтгэл хангалуун байна. Одоогоор бид илүү хурдан хайлт, илүү сайн хэлний дэмжлэг, илүү сайн хамааралтай эрэмбэлэлт, өөр өөр эрэмбэлэх сонголтууд, хэл/номын төрөл/файлын төрлөөр шүүх боломжийг хэрэгжүүлсэн. Хэрэв энэ хэрхэн ажилладаг талаар сонирхож байвал <a %(annas_archive_l140)s>хараарай</a>. Энэ нь нэлээд хүртээмжтэй боловч илүү олон тайлбар хэрэгтэй байж магадгүй… Аннагийн Архив нь бүрэн нээлттэй эхийн архив юм Бид мэдээлэл үнэ төлбөргүй байх ёстой гэж үздэг бөгөөд манай код ч үүнээс үл хамаарахгүй. Бид бүх кодоо хувийн Gitlab дээрээ нийтэлсэн: <a %(annas_archive)s>Аннагийн Програм хангамж</a>. Мөн бид ажлаа зохион байгуулахын тулд асуудлын хянагчийг ашигладаг. Хэрэв та манай хөгжүүлэлтэд оролцохыг хүсвэл энэ бол эхлэхэд тохиромжтой газар юм. Бидний хийж буй ажлуудын амтыг танд өгөхийн тулд үйлчлүүлэгчийн талын гүйцэтгэлийг сайжруулах талаар хийсэн сүүлийн үеийн ажлыг авч үзээрэй. Бид хуудаслахыг хараахан хэрэгжүүлээгүй тул ихэвчлэн 100-200 үр дүн бүхий маш урт хайлтын хуудсуудыг буцаадаг байсан. Бид хайлтын үр дүнг хэтэрхий эрт таслахыг хүсээгүй ч энэ нь зарим төхөөрөмжийг удаашруулдаг гэсэн үг юм. Үүний тулд бид жижиг заль мэхийг хэрэгжүүлсэн: ихэнх хайлтын үр дүнг HTML сэтгэгдэлд ороож (<code><!-- --></code>), дараа нь үр дүн харагдах ёстой үед илрүүлэх жижиг Javascript бичсэн бөгөөд энэ мөчид бид сэтгэгдлийг задлах болно: DOM "виртуалчлал" 23 мөрөөр хэрэгжсэн, тансаг номын сангууд хэрэггүй! Энэ бол цаг хугацаа хязгаарлагдмал, шийдвэрлэх шаардлагатай бодит асуудлуудтай тулгарсан үед гарч ирдэг хурдан прагматик кодын төрөл юм. Манай хайлт одоо удаан төхөөрөмжүүд дээр сайн ажиллаж байгаа гэж мэдээлсэн! Өөр нэг том хүчин чармайлт бол мэдээллийн санг автоматаар бүтээх явдал байв. Бид эхлүүлсэн үедээ зүгээр л янз бүрийн эх сурвалжуудыг хамтад нь татаж авсан. Одоо бид тэдгээрийг шинэчлэхийг хүсч байгаа тул Library Genesis-ийн хоёр салаанаас шинэ metadata татаж авах, нэгтгэх хэд хэдэн скрипт бичсэн. Зорилго нь зөвхөн манай архивт ашигтай байхаас гадна сүүдрийн номын сангийн metadata-тай тоглохыг хүссэн хэн бүхэнд хялбар болгох явдал юм. Зорилго нь сонирхолтой metadata бүхий Jupyter тэмдэглэл байх бөгөөд ингэснээр бид <a %(blog)s>ISBN-ийн хэдэн хувь нь үүрд хадгалагддагийг</a> тодорхойлох гэх мэт илүү их судалгаа хийх боломжтой болно. Эцэст нь бид хандивын системээ шинэчилсэн. Та одоо зээлийн картаа ашиглан крипто түрийвч рүүгээ шууд мөнгө шилжүүлэх боломжтой бөгөөд криптовалютын талаар юу ч мэдэх шаардлагагүй. Бид үүнийг практикт хэр сайн ажиллаж байгааг хянаж байх болно, гэхдээ энэ бол том асуудал. Z-Library хаагдаж, түүний (гэж үзсэн) үүсгэн байгуулагчид баривчлагдсанаар бид Аннагийн Архивтай сайн хувилбар гаргахын тулд цаг наргүй ажиллаж байна (бид үүнийг энд холбохгүй, гэхдээ та Google-ээс хайж болно). Саяхан бидний хийсэн зарим зүйлс энд байна. Аннагийн шинэчлэлт: бүрэн нээлттэй эхийн архив, ElasticSearch, 300GB+ номын хавтас Бид Аннагийн Архивтай сайн хувилбар гаргахын тулд цаг наргүй ажиллаж байна. Саяхан бидний хийсэн зарим зүйлс энд байна. Шинжилгээ Семантик давхардлууд (нэг номын өөр өөр скан) онолын хувьд шүүгдэж болох боловч энэ нь төвөгтэй. Комик номыг гараар шалгах үед бид хэтэрхий олон хуурамч эерэгүүдийг олсон. Зарим давхардал зөвхөн MD5-аар гарч ирдэг нь харьцангуй үрэлгэн боловч тэдгээрийг шүүх нь бидэнд ойролцоогоор 1% in хэмнэлт өгөх болно. Энэ хэмжээнд 1TB орчим байгаа ч энэ хэмжээнд 1TB үнэхээр чухал биш юм. Бид энэ үйл явцад санамсаргүйгээр өгөгдөл устгах эрсдэлд орохыг хүсэхгүй байна. Бид комик номоос сэдэвлэсэн кино зэрэг ном биш өгөгдлүүдийг олсон. Энэ нь бас үрэлгэн санагдаж байна, учир нь эдгээр нь аль хэдийн өөр аргаар өргөнөөр ашиглагдаж байна. Гэсэн хэдий ч бид кино файлуудыг зүгээр л шүүж чадахгүй гэдгийг ойлгосон, учир нь компьютер дээр гаргасан <em>интерактив комик ном</em> бас байдаг бөгөөд хэн нэгэн тэдгээрийг бичиж, кино болгон хадгалсан байдаг. Эцэст нь, бид цуглуулгаас устгаж болох бүх зүйл зөвхөн хэдэн хувийг л хэмнэх болно. Тэгээд бид өөрсдийгөө өгөгдөл цуглуулагч гэдгээ санасан бөгөөд үүнийг толин тусгал болгох хүмүүс ч бас өгөгдөл цуглуулагчид юм, тиймээс “УСТГАХ ГЭЖ ЮУ ГЭСЭН ҮГ ВЭ?!” :) 95TB өгөгдлийг хадгалах кластертаа хаях үед та тэнд юу байгааг ойлгохыг хичээдэг... Бид давхардлыг арилгах гэх мэтээр хэмжээг бага зэрэг багасгаж чадах эсэхийг харахын тулд зарим шинжилгээ хийсэн. Энд бидний олж мэдсэн зарим зүйлс байна: Тиймээс бид танд бүрэн, өөрчлөгдөөгүй цуглуулгыг танилцуулж байна. Энэ нь их хэмжээний өгөгдөл боловч бид хангалттай олон хүн үүнийг үргэлжлүүлэн хуваалцах болно гэж найдаж байна. Хамтын ажиллагаа Энэ цуглуулгын хэмжээ нь бидний хүсэлтийн жагсаалтад удаан хугацаанд байсан тул Z-Library-г нөөцлөхөд амжилттай болсны дараа бид энэ цуглуулгыг зорьсон. Эхэндээ бид үүнийг шууд хусаж авсан нь нэлээд хэцүү байсан, учир нь тэдний сервер хамгийн сайн нөхцөлд байгаагүй. Бид энэ аргаар ойролцоогоор 15TB авсан боловч удаан явсан. Аз болоход, бид номын сангийн оператортой холбоо тогтоож, бүх өгөгдлийг шууд илгээхийг зөвшөөрсөн нь илүү хурдан байсан. Бүх өгөгдлийг шилжүүлэх, боловсруулахад хагас жилээс илүү хугацаа зарцуулсан бөгөөд бид бараг бүх зүйлийг дискний гэмтлээс болж алдах шахсан, энэ нь бүх зүйлийг дахин эхлүүлэх шаардлагатай болох байсан. Энэ туршлага нь бидэнд энэ өгөгдлийг аль болох хурдан гаргаж, өргөн хүрээнд толин тусгал болгох нь чухал гэдэгт итгүүлсэн. Бид энэ цуглуулгыг үүрд алдах аюултай нэг эсвэл хоёр азгүй тохиолдлоос холгүй байна! Цуглуулга Хурдан хөдөлж байгаа нь цуглуулга бага зэрэг эмх замбараагүй гэсэн үг юм... Хараад үзье. Бид файл системтэй гэж төсөөлөөд үзье (бодит байдал дээр бид үүнийг торрентуудаар хувааж байна): Эхний лавлах, <code>/repository</code>, энэ хэсгийн илүү бүтэцтэй хэсэг юм. Энэ лавлах нь "мянган лавлахууд" гэж нэрлэгддэг: тус бүр нь мянган файлуудтай, өгөгдлийн санд дараалан дугаарлагдсан лавлахуудыг агуулдаг. Лавлах <code>0</code> нь comic_id 0–999 бүхий файлуудыг агуулдаг, гэх мэт. Энэ нь Library Genesis-ийн уран зохиол болон уран зохиол бус цуглуулгад ашиглаж байсан ижил схем юм. Санаа нь "мянган лавлах" бүрийг дүүрэн болмогц автоматаар торрент болгон хувиргах явдал юм. Гэсэн хэдий ч, Libgen.li оператор энэ цуглуулгад зориулж торрентуудыг хэзээ ч хийгээгүй бөгөөд ингэснээр мянган лавлахууд нь тохиромжгүй болж, "эмх замбараагүй лавлахууд"-д зам тавьсан. Эдгээр нь <code>/comics0</code> -ээс <code>/comics4</code> хүртэл юм. Тэд бүгд өвөрмөц лавлах бүтэцтэй бөгөөд файлуудыг цуглуулахад утга учиртай байсан боловч одоо бидэнд тийм ч их утга учиргүй байна. Аз болоход, metadata нь эдгээр бүх файлуудыг шууд зааж байгаа тул тэдгээрийн диск дээрх хадгалалтын зохион байгуулалт үнэндээ хамаагүй! Metadata нь MySQL өгөгдлийн сангийн хэлбэрээр байдаг. Үүнийг Libgen.li вэбсайтаас шууд татаж авах боломжтой, гэхдээ бид үүнийг өөрсдийн бүх MD5 хэшүүдтэй хүснэгттэйгээ хамт торрент дээр бас боломжтой болгоно. <q>Доктор Барбара Гордон номын сангийн ердийн ертөнцөд өөрийгөө алдахыг оролдож байна...</q> Libgen салаанууд Эхлээд, зарим нэг суурь мэдээлэл. Та Library Genesis-ийг тэдний гайхалтай номын цуглуулгаар нь мэддэг байж магадгүй. Цөөн хүн Library Genesis-ийн сайн дурынхан бусад төслүүдийг, жишээлбэл, сэтгүүл болон стандарт баримт бичгийн томоохон цуглуулга, Sci-Hub-ийн бүрэн нөөц (Sci-Hub-ийн үүсгэн байгуулагч Александра Элбакянтай хамтран), мөн үнэхээр томоохон комик номын цуглуулгыг бүтээсэн гэдгийг мэддэг. Тодорхой нэг үед Library Genesis толин тусгалуудын өөр өөр операторууд тус тусдаа замаа хөөж, одоогийн Library Genesis нэрийг хадгалсан хэд хэдэн өөр "салаа"-г бий болгосон. Libgen.li салаа нь энэ комик номын цуглуулгыг онцгойлон агуулдаг бөгөөд мөн томоохон сэтгүүлийн цуглуулгатай (бид үүн дээр ч бас ажиллаж байна). Хандивын аян Бид энэ өгөгдлийг томоохон хэсгүүдээр гаргаж байна. Эхний торрент нь <code>/comics0</code> бөгөөд бид үүнийг 12TB хэмжээтэй .tar файлд нэгтгэсэн. Энэ нь таны хатуу диск болон торрент програм хангамжид олон жижиг файлуудаас илүү тохиромжтой. Энэ гаргалтын нэг хэсэг болгон бид хандивын аян зохион байгуулж байна. Бид энэ цуглуулгын үйл ажиллагааны болон гэрээний зардлыг нөхөх, мөн ирээдүйн төслүүдийг хэрэгжүүлэхэд зориулж 20,000 доллар цуглуулахыг зорьж байна. Бидэнд зарим <em>асар том</em> төслүүд хийгдэж байна. <em>Би хандив өгснөөр хэнийг дэмжиж байна вэ?</em> Товчхондоо: бид хүн төрөлхтний бүх мэдлэг, соёлыг нөөцөлж, хялбархан хандах боломжтой болгож байна. Манай бүх код, өгөгдөл нээлттэй эх сурвалжтай, бид бүрэн сайн дурын төсөл бөгөөд одоогоор 125TB хэмжээтэй номыг хадгалсан (Libgen болон Scihub-ийн одоо байгаа торрентуудын нэмэлтээр). Эцэст нь бид дэлхийн бүх номыг олох, сканнердах, нөөцлөхийг хүмүүст урамшуулах, дэмжих эргүүлэг бүтээж байна. Бид ирээдүйн нийтлэлдээ мастер төлөвлөгөөгөө бичих болно. :) Хэрэв та 12 сарын “Amazing Archivist” гишүүнчлэлд ($780) хандив өгвөл, та <strong>“торрент өргөж авах”</strong> боломжтой болно, энэ нь бид таны хэрэглэгчийн нэр эсвэл мессежийг нэг торрентын файлын нэрэнд оруулах болно гэсэн үг! Та <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу орж, “Хандив өгөх” товчийг дарж хандив өгөх боломжтой. Мөн бид илүү олон сайн дурын ажилтнуудыг хайж байна: програм хангамжийн инженерүүд, аюулгүй байдлын судлаачид, нэргүй худалдааны мэргэжилтнүүд, орчуулагчид. Та мөн биднийг хостингийн үйлчилгээ үзүүлж дэмжиж болно. Мөн мэдээж, манай торрентуудыг үргэлжлүүлэн хуваалцаарай! Биднийг аль хэдийн өгөөмөр дэмжсэн бүх хүмүүст баярлалаа! Та үнэхээр өөрчлөлт хийж байна. Энд одоогоор гаргасан торрентууд байна (бид бусдыг нь боловсруулж байна): Бүх торрентуудыг <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> дээр “Datasets” хэсэгтээс олж болно (бид тийшээ шууд холбоос тавьдаггүй тул энэ блогийн холбоосууд Reddit, Twitter гэх мэтээс устгагдахгүй). Тэндээс Tor вэбсайт руу холбоосыг дагана уу. <a %(news_ycombinator)s>Hacker News дээр хэлэлцэх</a> Дараа нь юу вэ? Олон тооны торрентууд урт хугацааны хадгалалтанд тохиромжтой боловч өдөр тутмын хандалтад тийм ч тохиромжтой биш. Бид энэ бүх өгөгдлийг вэб дээр байршуулахын тулд хостингийн түншүүдтэй хамтран ажиллах болно (Аннагийн Архив нь ямар ч зүйлийг шууд хостолдоггүй). Мэдээж та эдгээр татаж авах холбоосыг Аннагийн Архиваас олох боломжтой. Мөн бид бүх хүмүүст энэ өгөгдлөөр ямар нэгэн зүйл хийхийг урьж байна! Бидэнд үүнийг илүү сайн шинжлэх, давхардлыг арилгах, IPFS дээр байршуулах, өөрийн AI загваруудыг сургах гэх мэтэд туслаарай. Энэ бүгд таных бөгөөд бид таны юу хийхийг тэсэн ядан хүлээж байна. Эцэст нь, өмнө нь хэлсэнчлэн, бидэнд зарим асар том гаргалтууд хийгдэж байна (хэрэв <em>хэн нэгэн</em> бидэнд <em>санамсаргүйгээр</em> <em>тодорхой</em> ACS4 өгөгдлийн сангийн хаягдлыг илгээж чадвал, та биднийг хаанаас олохоо мэднэ...), мөн дэлхийн бүх номыг нөөцлөх эргүүлгийг бүтээж байна. Тиймээс хүлээцтэй байгаарай, бид дөнгөж эхэлж байна. - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Комик номын хамгийн том сүүдрийн номын сан нь магадгүй Library Genesis-ийн нэг салаа болох Libgen.li юм. Тус сайтыг ажиллуулдаг нэг администратор 2 сая гаруй файлыг цуглуулж, нийт 95TB-аас дээш хэмжээтэй комик номын гайхалтай цуглуулгыг бүрдүүлсэн. Гэсэн хэдий ч, бусад Library Genesis цуглуулгуудтай адил энэ нь бөөнөөрөө торрентуудаар дамжуулан авах боломжгүй байсан. Та эдгээр комик номыг зөвхөн түүний удаан хувийн серверээр дамжуулан нэг нэгээр нь авах боломжтой байсан — ганцхан алдаа гарвал бүх зүйл алдагдах байдалтай. Өнөөдрийг хүртэл! Энэ нийтлэлд бид танд энэ цуглуулгын талаар болон энэ ажлыг дэмжих санхүүжилтийн талаар илүү ихийг хэлэх болно. Аннагийн Архив дэлхийн хамгийн том комик номын сүүдрийн номын санг (95TB) нөөцөлсөн — та үүнийг үргэлжлүүлэн татаж авахад тусалж болно Дэлхийн хамгийн том комик номын сүүдрийн номын сан ганцхан алдааны цэгтэй байсан.. өнөөдрийг хүртэл. Анхааруулга: энэ блог нийтлэл хуучирсан. Бид IPFS нь одоогоор ашиглахад бэлэн биш гэж шийдсэн. Бид Аннагийн Архиваас боломжтой үед IPFS дээрх файлууд руу холбоос хийх болно, гэхдээ бид өөрсдөө үүнийг хостлохгүй, мөн бусдыг IPFS ашиглан толин тусгал хийхийг зөвлөхгүй. Манай Торрентуудын хуудсыг үзэж, манай цуглуулгыг хадгалахад туслахыг хүсвэл үзнэ үү. 5,998,794 номыг IPFS дээр байрлуулах нь Хуулбаруудын үржүүлэлт Анхны асуулт руугаа буцъя: бид цуглуулгаа үүрд хадгална гэж яаж баталж чадах вэ? Энд гол асуудал нь манай цуглуулга <a %(torrents_stats)s>хурдан</a> өсч байгаа явдал юм. Энэ нь зарим томоохон цуглуулгуудыг хусах, нээлттэй эх сурвалж болгох замаар (Sci-Hub болон Library Genesis зэрэг бусад нээлттэй өгөгдлийн сүүдрийн номын сангуудын аль хэдийн хийсэн гайхалтай ажлын дээр нэмээд) хийгдэж байна. Энэ өгөгдлийн өсөлт нь цуглуулгуудыг дэлхий даяар толин тусгал болгоход хэцүү болгодог. Өгөгдөл хадгалах нь үнэтэй! Гэхдээ бид дараах гурван чиг хандлагыг ажиглахдаа өөдрөг байна. Сүүлийн хэдэн сарын хугацаанд манай цуглуулгуудын <a %(annas_archive_stats)s>нийт хэмжээ</a>, торрент үржүүлэгчдийн тоогоор задарсан. Өөр өөр эх сурвалжаас авсан HDD үнийн чиг хандлага (судалгааг үзэхийн тулд дарна уу). <a %(critical_window_chinese)s>Хятад хувилбар 中文版</a>, <a %(reddit)s>Reddit</a> дээр хэлэлцэх, <a %(news_ycombinator)s>Hacker News</a> 1. Бид амархан олддог жимсийг түүсэн Энэ нь дээр дурдсан тэргүүлэх чиглэлүүдээс шууд хамаардаг. Бид эхлээд томоохон цуглуулгуудыг чөлөөлөх дээр ажиллахыг илүүд үздэг. Одоо бид дэлхийн хамгийн том цуглуулгуудыг баталгаажуулсан тул бидний өсөлт илүү удаан байх болно гэж хүлээж байна. Бага хэмжээний цуглуулгуудын урт сүүл байсаар байгаа бөгөөд өдөр бүр шинэ ном сканнердаж эсвэл хэвлэгддэг боловч хурд нь илүү удаан байх магадлалтай. Бидний хэмжээ хоёр дахин эсвэл бүр гурав дахин нэмэгдэж магадгүй ч илүү урт хугацаанд. OCR сайжруулалт. Тэргүүлэх чиглэлүүд Шинжлэх ухаан ба инженерийн програм хангамжийн код Дээрх бүх зүйлийн уран зохиолын эсвэл зугаа цэнгэлийн хувилбарууд Газарзүйн мэдээлэл (жишээ нь газрын зураг, геологийн судалгаа) Корпораци болон засгийн газрын дотоод мэдээлэл (алдагдал) Шинжлэх ухааны хэмжилт, эдийн засгийн өгөгдөл, компанийн тайлан зэрэг хэмжилтийн өгөгдөл Ерөнхийдөө metadata бичлэгүүд (баримтат болон уран зохиол; бусад хэвлэл мэдээлэл, урлаг, хүмүүс гэх мэт; үнэлгээ оруулсан) Баримтат ном Баримтат сэтгүүл, сонин, гарын авлага Яриа, баримтат кино, подкастын баримтат бичвэрүүд ДНХ-ийн дараалал, ургамлын үр, микробын дээж зэрэг органик өгөгдөл Эрдэм шинжилгээний өгүүлэл, сэтгүүл, тайлан Шинжлэх ухаан ба инженерийн вэбсайтууд, онлайн хэлэлцүүлэг Хууль эрх зүйн эсвэл шүүхийн үйл явцын бичлэгүүд Өвөрмөц устах эрсдэлтэй (жишээ нь дайн, санхүүгийн хасалт, шүүхийн зарга, эсвэл улс төрийн хавчлагаас болж) Ховор Өвөрмөц анхаарал татсан Бид яагаад судалгааны материал, номонд ингэж их анхаарал хандуулдаг вэ? Ерөнхийдөө хадгалалтын үндсэн итгэл үнэмшлээ түр хойш тавья — бид энэ талаар өөр нийтлэл бичиж магадгүй. Тэгвэл яагаад судалгааны материал, ном гэж? Хариулт нь энгийн: <strong>мэдээллийн нягтрал</strong>. Хадгалах нэг мегабайт тутамд бичгийн текст нь бүх медиагаас хамгийн их мэдээллийг хадгалдаг. Бид мэдлэг болон соёлыг хоёуланг нь чухалчилдаг ч, мэдлэгийг илүү чухалчилдаг. Ерөнхийдөө бид мэдээллийн нягтрал болон хадгалалтын чухал байдлын шатлал дараах байдлаар харагддаг: Энэ жагсаалтын эрэмбэ нь тодорхой хэмжээгээр дур зоргоороо байдаг — зарим зүйлс нь тэнцүү эсвэл манай багийн дотор санал зөрөлдөөнтэй байдаг — мөн бид зарим чухал ангиллуудыг мартаж байж магадгүй. Гэхдээ энэ нь бидний эрэмбэлэх арга барилыг ерөнхийд нь харуулж байна. Эдгээр зүйлсийн зарим нь бусдаасаа хэтэрхий өөр тул бид санаа зовох шаардлагагүй (эсвэл бусад байгууллагуудаар аль хэдийн хариуцсан байдаг), жишээ нь органик мэдээлэл эсвэл газарзүйн мэдээлэл. Гэхдээ энэ жагсаалтын ихэнх зүйлс бидэнд үнэхээр чухал юм. Манай эрэмбэлэхэд нөлөөлөх өөр нэг том хүчин зүйл бол тодорхой бүтээл хэр их эрсдэлд орсон байгаа явдал юм. Бид дараах бүтээлүүдэд анхаарлаа төвлөрүүлэхийг илүүд үздэг: Эцэст нь бид хэмжээг чухалчилдаг. Бидэнд хязгаарлагдмал цаг хугацаа, мөнгө байгаа тул 10,000 номыг аврахад нэг сар зарцуулах нь 1,000 номыг аврахад зарцуулахтай харьцуулахад илүү дээр — хэрэв тэдгээр нь ойролцоо үнэ цэнэтэй, эрсдэлтэй бол. <em><q>Алдагдсан зүйлийг сэргээж чадахгүй; гэхдээ үлдсэн зүйлийг аврая: олон нийтийн нүднээс болон ашиглалтаас хамгаалж, цаг хугацааны хаягдалд хаяхгүйгээр, харин олон хувийг бий болгож, тэдгээрийг ослоос ангид байлгах замаар.</q></em><br>— Томас Жефферсон, 1791 Сүүдрийн номын сангууд Кодыг Github дээр нээлттэй эх сурвалж болгон байршуулах боломжтой боловч Github-ийг бүхэлд нь амархан толин тусгал болгох, хадгалах боломжгүй (гэхдээ энэ тохиолдолд ихэнх кодын сангийн хангалттай тархсан хуулбарууд байдаг). Metadata бичлэгүүдийг Worldcat вэбсайтаас чөлөөтэй үзэх боломжтой, гэхдээ бөөнөөр нь татаж авах боломжгүй (бид <a %(worldcat_scrape)s>тэдгээрийг хусах хүртэл</a>) Reddit ашиглахад үнэгүй боловч сүүлийн үед өгөгдөлд шунасан LLM сургалтын улмаас хатуу хязгаарлалт тавьсан (энэ талаар дараа нь дэлгэрэнгүй). Ижил төстэй зорилго, ижил төстэй эрэмбэлэлтэй олон байгууллага байдаг. Үнэндээ, энэ төрлийн хадгалалтыг хариуцсан номын сан, архив, лаборатори, музей болон бусад байгууллагууд байдаг. Тэдний олонх нь засгийн газар, хувь хүмүүс эсвэл корпорациудаас сайн санхүүждэг. Гэхдээ тэдэнд нэг том сохор цэг байдаг: хууль эрх зүйн систем. Энд сүүдрийн номын сангуудын өвөрмөц үүрэг, Аннагийн Архив оршин байх шалтгаан оршдог. Бид бусад байгууллагуудад зөвшөөрөгдөөгүй зүйлийг хийж чадна. Одоо, бид бусад газарт хадгалах нь хууль бус материалуудыг архивлах боломжтой гэсэн үг биш (ихэвчлэн). Үгүй, олон газарт ямар ч ном, бичиг баримт, сэтгүүл гэх мэтээр архив байгуулах нь хууль ёсны байдаг. Гэхдээ хууль ёсны архивууд ихэвчлэн <strong>давхардал болон урт хугацааны тогтвортой байдал</strong> дутагддаг. Зарим физик номын санд зөвхөн нэг хувь байдаг номнууд байдаг. Зөвхөн нэг корпорациар хамгаалагдсан metadata бичлэгүүд байдаг. Зөвхөн нэг архивт микрофильм дээр хадгалагдсан сонингууд байдаг. Номын сангууд санхүүгийн хасалтанд орж болно, корпорациуд дампуурч болно, архивууд бөмбөгдөгдөж, шатаагдаж болно. Энэ нь таамаглал биш — энэ нь байнга тохиолддог. Аннагийн Архивт бидний өвөрмөц хийж чадах зүйл бол олон хувийг хадгалах явдал юм. Бид бичиг баримт, ном, сэтгүүл болон бусад зүйлийг цуглуулж, бөөнөөр нь тарааж чадна. Одоогоор бид үүнийг torrents-ээр дамжуулан хийж байгаа боловч яг ямар технологи ашиглах нь чухал биш бөгөөд цаг хугацааны явцад өөрчлөгдөх болно. Хамгийн чухал нь олон хувийг дэлхий даяар тараах явдал юм. 200 гаруй жилийн өмнөх энэ ишлэл одоо ч үнэн хэвээр байна: Олон нийтийн өмчийн талаар хурдан тэмдэглэл. Аннагийн Архив дэлхийн олон газарт хууль бус үйл ажиллагаанд онцгой анхаардаг тул бид олон нийтэд нээлттэй цуглуулгууд, жишээ нь олон нийтийн өмчийн номнуудтай хамааралгүй байдаг. Хууль ёсны байгууллагууд ихэвчлэн үүнийг сайн арчилдаг. Гэсэн хэдий ч, заримдаа олон нийтэд нээлттэй цуглуулгууд дээр ажиллахад хүргэдэг зарим асуудлууд байдаг: - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Хадгалах зардал экспоненциал буурсаар байна 3. Мэдээллийн нягтралын сайжруулалт Бид одоогоор номыг бидэнд өгсөн түүхий хэлбэрээр хадгалж байна. Мэдээж тэд шахагдсан боловч ихэвчлэн том хэмжээтэй сканнердсан эсвэл хуудасны зураг хэвээр байна. Одоог хүртэл манай цуглуулгын нийт хэмжээг багасгах цорын ганц сонголт нь илүү түрэмгий шахалт эсвэл давхардлыг арилгах явдал байв. Гэсэн хэдий ч хангалттай их хэмнэлт гаргахын тулд хоёулаа бидний хувьд хэтэрхий алдагдалтай байдаг. Зургийг хэт шахах нь текстийг уншихад бараг боломжгүй болгодог. Мөн давхардлыг арилгах нь номыг яг ижилхэн гэдэгт өндөр итгэлтэй байхыг шаарддаг бөгөөд энэ нь ихэвчлэн хэтэрхий нарийвчлалгүй байдаг, ялангуяа агуулга нь ижил боловч сканнердсан нь өөр өөр тохиолдолд. Гурав дахь сонголт үргэлж байсан боловч түүний чанар маш муу байсан тул бид хэзээ ч авч үзээгүй: <strong>OCR, эсвэл Оптик Тэмдэгт Таних</strong>. Энэ нь зургийг энгийн текст болгон хувиргах үйл явц бөгөөд үүнд AI-г ашиглан зурган дээрх тэмдэгтүүдийг илрүүлдэг. Үүний хэрэгслүүд удаан хугацаанд оршин байсан бөгөөд нэлээд сайн байсан ч "нэлээд сайн" нь хадгалах зорилгоор хангалтгүй юм. Гэсэн хэдий ч сүүлийн үеийн олон төрлийн гүнзгий сургалтын загварууд маш хурдан ахиц дэвшил гаргасан боловч өндөр зардалтай хэвээр байна. Бид нарийвчлал болон зардал хоёулаа ирэх жилүүдэд эрс сайжирна гэж найдаж байгаа бөгөөд ингэснээр манай бүх номын санд хэрэгжүүлэх нь бодитой болно. Ийм зүйл тохиолдох үед бид анхны файлуудыг хадгалах боловч нэмэлтээр ихэнх хүмүүс толин тусгал хийхийг хүсэх манай номын сангийн илүү жижиг хувилбарыг байлгах боломжтой болно. Хамгийн гол нь түүхий текст нь илүү сайн шахагдаж, давхардлыг арилгах нь илүү хялбар байдаг тул бидэнд илүү их хэмнэлт өгдөг. Ерөнхийдөө нийт файлын хэмжээг 5-10 дахин багасгах нь бодит бус зүйл биш бөгөөд магадгүй бүр илүү их байж болно. Хэдийгээр 5 дахин багасгасан ч гэсэн манай номын сангийн хэмжээ гурав дахин нэмэгдсэн ч гэсэн 10 жилийн дараа <strong>1,000–3,000 долларын үнэтэй байх болно</strong>. Энэ бичиж байх үеийн байдлаар, <a %(diskprices)s>дискний үнэ</a> TB тутамд шинэ дискийн хувьд $12, хуучин дискийн хувьд $8, соронзон хальсны хувьд $4 орчим байна. Хэрэв бид болгоомжтой байж зөвхөн шинэ дискийг харвал, нэг петабайт хадгалахад ойролцоогоор $12,000 болно. Хэрэв бидний номын сан 900TB-аас 2.7PB хүртэл гурав дахин нэмэгдэнэ гэж үзвэл, манай бүх номын санг толин тусгал болгоход $32,400 болно. Цахилгаан, бусад тоног төхөөрөмжийн зардлыг нэмээд, үүнийг $40,000 гэж дугуйлъя. Эсвэл соронзон хальстай бол $15,000–$20,000 орчим. Нэг талаас <strong>бүх хүний мэдлэгийн нийлбэр нь $15,000–$40,000 бол хямдхан</strong>. Нөгөө талаас, олон бүрэн хуулбаруудыг хүлээх нь жаахан өндөр, ялангуяа бусад хүмүүст зориулж тэдний торрентийг үргэлжлүүлэн үржүүлэхийг хүсч байвал. Энэ бол өнөөдөр. Гэхдээ дэвшил урагшилж байна: Сүүлийн 10 жилийн хугацаанд TB тутамд хатуу дискний зардал ойролцоогоор гуравны нэгээр буурсан бөгөөд ижил хурдаар буурсаар байх магадлалтай. Соронзон хальс ижил замаар явж байна. SSD-ийн үнэ бүр ч хурдан буурч байгаа бөгөөд энэ арван жилийн эцэс гэхэд HDD-ийн үнийг давж магадгүй. Хэрэв энэ хэвээр байвал 10 жилийн дараа бид бүх цуглуулгаа толин тусгал болгоход ердөө $5,000–$13,000 (1/3) эсвэл хэмжээгээр бага өсвөл бүр бага байхыг харах болно. Энэ нь их мөнгө хэвээр байгаа ч олон хүнд боломжтой болно. Мөн дараагийн цэгийн ачаар бүр ч илүү сайжирч магадгүй… Аннагийн Архивт биднээс цуглуулгуудыг үүрд хадгална гэж яаж баталж чадах вэ гэж их асуудаг, учир нь нийт хэмжээ нь аль хэдийн 1 Петабайт (1000 TB)-д ойртож, өссөөр байна. Энэ нийтлэлд бидний философийг авч үзэж, хүн төрөлхтний мэдлэг, соёлыг хадгалах бидний эрхэм зорилго ирэх арван жилд яагаад чухал болохыг харна. Чухал цонх Хэрэв эдгээр таамаглал үнэн бол бидний бүх цуглуулга өргөн хүрээнд толин тусгалтай болохоос өмнө <strong>хэдэн жил хүлээх хэрэгтэй</strong>. Тиймээс Томас Жефферсоны үгээр хэлбэл, “санамсаргүй байдлаас гадуур байрлуулсан.” Харамсалтай нь, LLM-ийн гарч ирэлт болон тэдний өгөгдөлд өлсгөлөн сургалт нь олон зохиогчийн эрх эзэмшигчдийг хамгаалалтанд оруулсан. Тэд аль хэдийн байсан ч илүү их. Олон вэбсайтууд хусах, архивлахыг улам хүндрүүлж, шүүхийн нэхэмжлэлүүд нисч байгаа бөгөөд энэ хооронд физик номын сан, архивууд үл тоомсорлогдсоор байна. Эдгээр чиг хандлагууд улам дордохыг бид зөвхөн хүлээж болох бөгөөд олон бүтээлүүд олон нийтийн өмчид орохоос өмнө алдагдах болно. <strong>Бид хадгалалтын хувьсгалын өмнөхөн байна, гэхдээ <q>алдагдсан зүйлсийг сэргээж чадахгүй.</q></strong> Бидэнд сүүдрийн номын сан ажиллуулах, дэлхий даяар олон толин тусгал үүсгэх нь одоо ч нэлээд үнэтэй байгаа 5-10 жилийн чухал цонх байгаа бөгөөд энэ хугацаанд хандалтыг бүрэн хаагаагүй байна. Хэрэв бид энэ цонхыг гаталж чадвал хүн төрөлхтний мэдлэг, соёлыг үүрд хадгалсан байх болно. Бид энэ цагийг дэмий үрэх ёсгүй. Бид энэ чухал цонхыг хаалгах ёсгүй. Явцгаая. Сүүдрийн номын сангийн чухал цонх Манай цуглуулгуудыг үүрд хадгална гэж яаж баталж чадах вэ, тэд аль хэдийн 1 PB-д ойртож байна? Цуглуулга Цуглуулгын талаар илүү дэлгэрэнгүй мэдээлэл. <a %(duxiu)s>Duxiu</a> бол <a %(chaoxing)s>SuperStar Digital Library Group</a>-аас бүтээсэн асар том сканнердсан номын мэдээллийн сан юм. Ихэнх нь их дээд сургууль, номын сангуудад цахим хэлбэрээр ашиглах боломжтой болгохын тулд сканнердсан академик ном юм. Манай англи хэлтэй үзэгчдэд зориулж, <a %(library_princeton)s>Princeton</a> болон <a %(guides_lib_uw)s>Вашингтоны их сургууль</a> нь сайн тоймтой байдаг. Мөн илүү дэлгэрэнгүй мэдээлэл өгөх маш сайн нийтлэл бий: <a %(doi)s>“Хятадын номыг цахимжуулах: SuperStar DuXiu Scholar хайлтын системийн кейс судалгаа”</a> (Аннагийн Архиваас хайж үзээрэй). Duxiu-ийн номнууд Хятадын интернетэд удаан хугацаанд хууль бусаар түгээгдсэн. Ихэвчлэн тэдгээрийг борлуулагчид нэг доллараас бага үнээр зардаг. Тэдгээрийг ихэвчлэн Google Drive-ийн Хятадын эквивалентаар түгээдэг бөгөөд энэ нь ихэвчлэн илүү их хадгалах зайтай болгохын тулд хакердсан байдаг. Зарим техникийн дэлгэрэнгүй мэдээллийг <a %(github_duty_machine)s>энд</a> болон <a %(github_821_github_io)s>энд</a> олж болно. Номнууд хагас нийтийн байдлаар түгээгдсэн ч, тэдгээрийг бөөнөөр нь авахад нэлээд хэцүү байдаг. Бид үүнийг хийх ажлын жагсаалтынхаа дээд хэсэгт тавьж, үүнд зориулж бүтэн цагаар ажиллах хэдэн сар зарцуулсан. Гэсэн хэдий ч саяхан гайхалтай, гайхамшигтай, авьяаслаг сайн дурын ажилтан бидэнтэй холбогдож, тэд энэ бүх ажлыг аль хэдийн хийсэн гэж хэлсэн — ихээхэн зардал гаргасан. Тэд бидэнд урт хугацааны хадгалалтыг баталгаажуулахыг хүссэнээс бусад тохиолдолд ямар ч хариу нэхэлгүйгээр бүтэн цуглуулгыг хуваалцсан. Үнэхээр гайхалтай. Тэд цуглуулгыг OCR хийхэд туслахын тулд ийм байдлаар тусламж хүсэхийг зөвшөөрсөн. Цуглуулга нь 7,543,702 файлтай. Энэ нь Library Genesis-ийн уран зохиолын бус номоос (ойролцоогоор 5.3 сая) илүү юм. Нийт файлын хэмжээ нь одоогийн хэлбэрээр 359TB (326TiB) орчим байна. Бид бусад санал, санаачлагад нээлттэй байна. Зүгээр л бидэнтэй холбоо бариарай. Манай цуглуулгууд, хадгалалтын хүчин чармайлтууд, хэрхэн туслах талаар илүү их мэдээлэл авахыг хүсвэл Аннагийн Архивыг үзээрэй. Баярлалаа! Жишээ хуудаснууд Танд сайн дамжуулах хоолой байгаа гэдгийг батлахын тулд эндээс эхлэх зарим жишээ хуудсууд байна, хэт дамжуулагчийн тухай номоос. Таны дамжуулах хоолой математик, хүснэгт, график, хөл тэмдэглэл гэх мэтийг зөв зохицуулах ёстой. Өөрийн боловсруулсан хуудсуудыг манай имэйл хаяг руу илгээнэ үү. Хэрэв тэд сайн харагдаж байвал бид танд хувийн байдлаар илүү ихийг илгээх болно, мөн та тэдгээрийг хурдан боловсруулж чадна гэж найдаж байна. Бид сэтгэл хангалуун болсны дараа бид тохиролцоо хийж болно. - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Хятад хувилбар 中文版</a>, <a %(news_ycombinator)s>Hacker News дээр хэлэлцэх</a> Энэ бол богино блог нийтлэл. Бид олж авсан асар их цуглуулгад OCR болон текст олборлолт хийхэд туслах компани эсвэл байгууллагыг хайж байна, онцгой эрт хандалтын хариуд. Хоригийн хугацааны дараа бид мэдээж бүх цуглуулгыг гаргах болно. Өндөр чанартай академик текст нь LLM-ийн сургалтад маш их хэрэгтэй. Манай цуглуулга Хятад хэл дээр байгаа ч энэ нь Англи хэлний LLM-ийн сургалтад ч хэрэгтэй байх ёстой: загварууд эх хэлээс үл хамааран ойлголт, мэдлэгийг кодчилдог бололтой. Үүний тулд текстийг сканнуудаас гаргаж авах шаардлагатай. Аннагийн Архив үүнээс юу авах вэ? Хэрэглэгчдэд зориулсан номын бүрэн текст хайлт. Манай зорилго LLM хөгжүүлэгчдийнхтэй нийцэж байгаа тул бид хамтрагч хайж байна. Хэрэв та зөв OCR болон текст олборлолт хийж чадвал бид танд <strong>энэ цуглуулгад 1 жилийн турш бөөнөөр нь онцгой эрт хандалт өгөхөд бэлэн байна</strong>. Хэрэв та бидэнтэй дамжуулах хоолойн бүх кодоо хуваалцахад бэлэн бол бид цуглуулгыг илүү удаан хугацаанд хориглох болно. Дэлхийн хамгийн том Хятадын баримтат номын цуглуулгад LLM компаниудад онцгой хандалт <em><strong>Товчхондоо:</strong> Аннагийн Архив 7.5 сая / 350TB Хятадын баримтат номын өвөрмөц цуглуулгыг олж авсан — Library Genesis-ээс том. Бид өндөр чанартай OCR болон текст олборлолтын хариуд LLM компанид онцгой хандалт өгөхөд бэлэн байна.</em> Системийн архитектур Тэгэхээр таны вэбсайтыг хаахгүйгээр хостлохыг хүсдэг зарим компаниудыг олсон гэж бодъё — эдгээрийг “эрх чөлөөг эрхэмлэгч үйлчилгээ үзүүлэгчид” гэж нэрлэе 😄. Та бүх зүйлийг тэдгээртэй хостлох нь нэлээд үнэтэй болохыг хурдан ойлгох болно, тиймээс та “хямд үйлчилгээ үзүүлэгчид”-ийг олохыг хүсэж магадгүй бөгөөд жинхэнэ хостингийг тэнд хийж, эрх чөлөөг эрхэмлэгч үйлчилгээ үзүүлэгчдээр дамжуулан прокси хийх боломжтой. Хэрэв та үүнийг зөв хийвэл хямд үйлчилгээ үзүүлэгчид таны хостлож буй зүйлийг хэзээ ч мэдэхгүй бөгөөд ямар ч гомдол хүлээн авахгүй. Эдгээр бүх үйлчилгээ үзүүлэгчид таныг хаах эрсдэлтэй тул танд давхардаж хэрэгтэй. Бидэнд энэ нь стекийн бүх түвшинд хэрэгтэй. Эрх чөлөөг бага зэрэг эрхэмлэдэг нэг компани бол Cloudflare юм. Тэд өөрсдийгөө хостинг үйлчилгээ үзүүлэгч биш, харин ISP шиг хэрэгсэл гэж <a %(blog_cloudflare)s>мэтгэлцсэн</a>. Тиймээс тэд DMCA эсвэл бусад татан буулгах хүсэлтэд хамаарахгүй бөгөөд таны жинхэнэ хостинг үйлчилгээ үзүүлэгчид хүсэлтийг дамжуулдаг. Тэд энэ бүтцийг хамгаалахын тулд шүүхэд хүртэл явсан. Тиймээс бид тэднийг кэшлэх болон хамгаалалтын өөр нэг давхарга болгон ашиглаж болно. Cloudflare нэрээ нууцалсан төлбөрийг хүлээн авдаггүй тул бид зөвхөн тэдний үнэгүй төлөвлөгөөг ашиглаж болно. Энэ нь бид тэдний ачаалал тэнцвэржүүлэх эсвэл алдаа засах боломжуудыг ашиглах боломжгүй гэсэн үг юм. Тиймээс бид <a %(annas_archive_l255)s>үүнийг өөрсдөө домэйн түвшинд хэрэгжүүлсэн</a>. Хуудас ачаалагдах үед хөтөч одоогийн домэйн одоо ч боломжтой эсэхийг шалгаж, хэрэв үгүй бол бүх URL-ийг өөр домэйн руу дахин бичдэг. Cloudflare олон хуудсыг кэшлэдэг тул энэ нь хэрэглэгч манай үндсэн домэйн дээр буух боломжтой гэсэн үг бөгөөд прокси сервер унтарсан байсан ч дараагийн товшилтоор өөр домэйн руу шилжих боломжтой. Бидэнд серверийн эрүүл мэндийг хянах, арын болон урд талын алдааг бүртгэх гэх мэт ердийн үйл ажиллагааны асуудлуудыг шийдвэрлэх шаардлагатай хэвээр байна. Манай алдаа засах архитектур нь энэ тал дээр илүү бат бөх байдлыг хангадаг, жишээлбэл, домэйнуудын нэг дээр огт өөр серверийн багцыг ажиллуулах замаар. Бид үндсэн хувилбар дээрх чухал алдаа анзаарагдахгүй тохиолдолд код болон өгөгдлийн багцын хуучин хувилбаруудыг энэ тусдаа домэйн дээр ажиллуулж болно. Бид Cloudflare-ийг эсрэгээр эргүүлэхээс сэргийлэхийн тулд энэ тусдаа домэйн гэх мэт нэг домэйноос нь устгаж болно. Эдгээр санаануудын өөр өөр хувилбарууд боломжтой. Дүгнэлт Энэ нь бат бөх, тэсвэртэй сүүдрийн номын сангийн хайлтын системийг хэрхэн тохируулах талаар суралцах сонирхолтой туршлага байлаа. Дараагийн нийтлэлүүдэд хуваалцах олон дэлгэрэнгүй мэдээлэл байгаа тул та юу илүү ихийг мэдэхийг хүсч байгаагаа надад мэдэгдээрэй! Үргэлжийн адил, бид энэ ажлыг дэмжих хандив хайж байгаа тул Аннагийн Архив дээрх Хандивлах хуудсыг шалгаарай. Бид мөн тэтгэлэг, урт хугацааны ивээн тэтгэгчид, өндөр эрсдэлтэй төлбөрийн үйлчилгээ үзүүлэгчид, магадгүй (гоё!) зар сурталчилгаа гэх мэт бусад төрлийн дэмжлэгийг хайж байна. Хэрэв та өөрийн цаг хугацаа, ур чадвараа хуваалцахыг хүсвэл бид үргэлж хөгжүүлэгчид, орчуулагчид гэх мэт хүмүүсийг хайж байдаг. Сонирхол, дэмжлэгт тань баярлалаа. Инновацийн жетон Манай технологийн стекээс эхэлье. Энэ нь зориудаар уйтгартай. Бид Flask, MariaDB, ElasticSearch ашигладаг. Энэ нь үнэхээр л тэр. Хайлт бол ихэвчлэн шийдэгдсэн асуудал бөгөөд бид үүнийг дахин бүтээх бодолгүй байна. Үүнээс гадна бид <a %(mcfunley)s>инновацийн жетон</a>-оо өөр зүйлд зарцуулах хэрэгтэй: эрх баригчдаар устгуулахгүй байх. Тэгэхээр Аннагийн Архив яг хэр хууль ёсны эсвэл хууль бус вэ? Энэ нь ихэвчлэн хууль эрх зүйн харьяаллаас хамаарна. Ихэнх улс орнууд зохиогчийн эрхийн ямар нэгэн хэлбэрт итгэдэг бөгөөд энэ нь хүмүүс эсвэл компаниудад тодорхой төрлийн бүтээлүүд дээр тодорхой хугацаанд онцгой монополь эрх олгодог гэсэн үг юм. Нэмэлт байдлаар, Аннагийн Архив дээр бид зарим ашиг тус байгаа ч, нийтдээ зохиогчийн эрх нь нийгэмд сөрөг нөлөөтэй гэж үздэг — гэхдээ энэ бол өөр цагт ярих түүх юм. Тодорхой бүтээлүүд дээрх энэ онцгой монополь эрх нь энэ монополь эрхээс гадуур хэн нэгэн эдгээр бүтээлүүдийг шууд түгээх нь хууль бус гэсэн үг юм — биднийг оруулаад. Гэхдээ Аннагийн Архив бол эдгээр бүтээлүүдийг шууд түгээдэггүй хайлтын систем (ялангуяа манай ил тод вэбсайт дээр биш), тиймээс бид зүгээр байх ёстой, тийм үү? Тийм биш. Олон харьяалалд зохиогчийн эрхтэй бүтээлүүдийг түгээх нь хууль бус төдийгүй тэдгээрийг түгээдэг газрууд руу холбоос хийх нь хууль бус байдаг. Үүний сонгодог жишээ бол АНУ-ын DMCA хууль юм. Энэ бол спектрийн хамгийн хатуу төгсгөл юм. Нөгөө төгсгөлд онолын хувьд огт зохиогчийн эрхийн хуульгүй орнууд байж болох ч эдгээр нь үнэхээр байдаггүй. Бараг бүх улс оронд зохиогчийн эрхийн хууль ямар нэгэн хэлбэрээр байдаг. Хэрэгжилт бол өөр түүх. Зохиогчийн эрхийн хуулийг хэрэгжүүлэхийг хүсдэггүй засгийн газартай олон орон байдаг. Мөн хоёр туйлын хооронд зохиогчийн эрхтэй бүтээлүүдийг түгээхийг хориглодог боловч ийм бүтээлүүд рүү холбоос хийхийг хориглодоггүй орнууд байдаг. Өөр нэг анхаарах зүйл бол компанийн түвшинд байдаг. Хэрэв компани зохиогчийн эрхийг үл тоомсорлодог эрх зүйн орчинд үйл ажиллагаа явуулдаг боловч компани өөрөө ямар ч эрсдэл хүлээхийг хүсэхгүй байгаа бол хэн нэгэн гомдол гаргасан даруйд таны вэбсайтыг хааж магадгүй. Эцэст нь, томоохон анхаарах зүйл бол төлбөр юм. Бид нэрээ нууцлах шаардлагатай тул уламжлалт төлбөрийн аргуудыг ашиглах боломжгүй. Энэ нь биднийг криптовалют руу хөтөлж байгаа бөгөөд зөвхөн цөөн хэдэн компаниуд тэдгээрийг дэмждэг (криптовалютаар төлбөр хийдэг виртуал дебит картууд байдаг ч тэдгээрийг ихэвчлэн хүлээн зөвшөөрдөггүй). - Анна болон баг (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Би <a %(wikipedia_annas_archive)s>Аннагийн Архив</a>-ыг ажиллуулдаг, энэ нь Sci-Hub, Library Genesis, Z-Library зэрэг сүүдрийн номын сангуудын дэлхийн хамгийн том нээлттэй эх сурвалж бүхий ашгийн бус хайлтын систем юм. Бидний зорилго бол мэдлэг, соёлыг хялбархан хүртээмжтэй болгох, эцэст нь дэлхийн <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>бүх номыг</a> архивлаж, хадгалах хүмүүсийн нийгэмлэгийг бий болгох явдал юм. Энэ нийтлэлд би энэ вэбсайтыг хэрхэн ажиллуулдаг, хууль эрх зүйн хувьд эргэлзээтэй статус бүхий вэбсайтыг ажиллуулахад тулгардаг өвөрмөц сорилтуудыг харуулах болно, учир нь сүүдрийн буяны байгууллагуудад зориулсан “AWS” байхгүй. <em>Мөн <a %(blog_how_to_become_a_pirate_archivist)s>Далайн дээрэмчин архивч болох арга зам</a> гэсэн ах дүү нийтлэлийг үзээрэй.</em> Сүүдрийн номын санг хэрхэн ажиллуулах вэ: Аннагийн Архив дахь үйл ажиллагаа Сүүдрийн буяны байгууллагуудад зориулсан <q>AWS байхгүй,</q> тэгвэл бид Аннагийн Архивыг хэрхэн ажиллуулдаг вэ? Хэрэгслүүд Програмын сервер: Flask, MariaDB, ElasticSearch, Docker. Хөгжүүлэлт: Gitlab, Weblate, Zulip. Серверийн удирдлага: Ansible, Checkmk, UFW. Onion статик хостинг: Tor, Nginx. Прокси сервер: Varnish. Эдгээр бүх зүйлийг хэрэгжүүлэхийн тулд бид ямар хэрэгслүүд ашиглаж байгааг харцгаая. Энэ нь бид шинэ асуудалтай тулгарч, шинэ шийдлүүдийг олохын хэрээр маш их хөгжиж байна. Бид эргэж буцаж шийдвэр гаргасан зарим шийдвэрүүд бий. Нэг нь серверүүдийн хоорондын харилцаа холбоо: бид үүнийг хийхийн тулд Wireguard ашигладаг байсан ч заримдаа ямар ч өгөгдөл дамжуулахгүй эсвэл зөвхөн нэг чиглэлд өгөгдөл дамжуулдаг болохыг олж мэдсэн. Бид <a %(github_costela_wesher)s>wesher</a> болон <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a> зэрэг хэд хэдэн өөр Wireguard тохиргоог туршиж үзсэн боловч энэ нь тохиолдсон. Мөн SSH-ээр портуудыг туннелдэж, autossh болон sshuttle ашиглан туршиж үзсэн боловч <a %(github_sshuttle)s>тэнд асуудалтай</a> тулгарсан (гэхдээ autossh TCP-оос TCP асуудалтай эсэх нь надад одоо ч тодорхойгүй байна — энэ нь надад эвгүй шийдэл мэт санагдаж байгаа ч магадгүй үнэхээр зүгээр байж магадгүй). Үүний оронд бид серверүүдийн хооронд шууд холболт руу буцаж, UFW ашиглан IP шүүлтүүрээр хямд үйлчилгээ үзүүлэгч дээр сервер ажиллаж байгааг нуусан. Энэ нь UFW-тэй Docker сайн ажилладаггүй сул талтай, хэрэв та <code>network_mode: "host"</code> ашиглахгүй бол. Энэ бүхэн нь илүү алдаатай байдаг, учир нь та зөвхөн жижиг тохиргооны алдаатайгаар серверээ интернетэд ил гаргах болно. Магадгүй бид autossh руу буцах хэрэгтэй байх — энд санал хүсэлт маш их хэрэгтэй байна. Бид Varnish болон Nginx-ийн хооронд эргэж буцсан. Одоогоор бид Varnish-д дуртай байгаа ч энэ нь өөрийн гэсэн онцлог, ширүүн ирмэгтэй. Checkmk-д мөн адил: бид үүнийг хайрладаггүй ч одоогоор ажиллаж байна. Weblate зүгээр байсан ч гайхалтай биш — би үүнийг манай git репотой синк хийх гэж оролдох бүртээ өгөгдлөө алдах вий гэж айдаг. Flask ерөнхийдөө сайн байсан ч өөрийн гэсэн хачин онцлогтой бөгөөд энэ нь ихээхэн цаг хугацаа зарцуулж, алдааг олж засварлахад хүргэсэн, жишээлбэл, захиалгат домэйнуудыг тохируулах эсвэл SqlAlchemy интеграцийн асуудлууд. Одоогоор бусад хэрэгслүүд маш сайн байсан: MariaDB, ElasticSearch, Gitlab, Zulip, Docker, Tor-ийн талаар ноцтой гомдол алга. Эдгээр бүгдэд зарим асуудал байсан ч ноцтой эсвэл цаг хугацаа их шаардсан зүйл байгаагүй. Нийгэмлэг Эхний сорилт нь гайхмаар байж магадгүй. Энэ нь техникийн асуудал эсвэл хууль эрх зүйн асуудал биш юм. Энэ бол сэтгэл зүйн асуудал: сүүдэрт энэ ажлыг хийх нь маш ганцаардмал байж болно. Та юу хийхээр төлөвлөж байгаагаас хамааран, аюул заналын загвараасаа хамааран маш болгоомжтой байх шаардлагатай байж магадгүй. Нэг талд Sci-Hub-ийн үүсгэн байгуулагч Александра Элбакян* гэх мэт үйл ажиллагаагаа нээлттэй илэрхийлдэг хүмүүс байдаг. Гэхдээ тэр барууны оронд зочлох юм бол баривчлагдах өндөр эрсдэлтэй бөгөөд хэдэн арван жилийн хорих ялтай тулгарах болно. Та ийм эрсдэл хүлээхэд бэлэн үү? Бид нөгөө талд нь байгаа; ямар ч ул мөр үлдээхгүй байхыг хичээж, хүчтэй үйл ажиллагааны аюулгүй байдлыг хангах. * HN дээр "ynno" нэрээр дурдсанчлан, Александра анхандаа танигдахыг хүсээгүй: "Түүний серверүүд PHP-ээс дэлгэрэнгүй алдааны мессежүүдийг гаргахаар тохируулагдсан байсан бөгөөд үүнд алдаатай эх файлын бүрэн зам, /home/<USER>" Тиймээс, та энэ зүйлд ашигладаг компьютер дээрээ санамсаргүй хэрэглэгчийн нэр ашиглаарай, хэрэв та ямар нэгэн зүйл буруу тохируулсан бол. Гэсэн хэдий ч энэ нууцлал нь сэтгэл зүйн зардалтай. Ихэнх хүмүүс хийсэн ажлынхаа төлөө хүлээн зөвшөөрөгдөх дуртай байдаг ч та бодит амьдрал дээр үүний төлөө ямар ч зээл авах боломжгүй. Найзууд тань танаас юу хийж байгааг асуух гэх мэт энгийн зүйлс ч хэцүү байж болно (тодорхой үед "NAS / homelab-тай оролдож байна" гэдэг нь хуучирдаг). Энэ нь яагаад ямар нэгэн хамт олныг олох нь маш чухал болохыг харуулж байна. Та итгэж болох маш ойрын найзууддаа итгэж, зарим үйл ажиллагааны аюулгүй байдлыг орхиж болно. Гэсэн хэдий ч тэдний имэйлийг эрх баригчдад өгөх шаардлагатай болсон эсвэл тэдний төхөөрөмжүүд өөр аргаар эвдэрсэн тохиолдолд бичгээр юу ч бичихгүй байхыг анхаараарай. Үүнээс ч илүү нь зарим далайн дээрэмчдийг олох явдал юм. Хэрэв таны ойрын найзууд тантай нэгдэх сонирхолтой бол гайхалтай! Үгүй бол та онлайнаар бусдыг олох боломжтой байж магадгүй. Харамсалтай нь энэ нь одоо ч гэсэн цөөнх хамт олон юм. Одоогоор бид энэ орон зайд идэвхтэй оролцдог цөөн хэдэн хүнийг л олсон. Library Genesis форумууд болон r/DataHoarder нь сайн эхлэл байж болох юм. Архивын баг мөн адил сэтгэлгээтэй хүмүүсийг агуулдаг боловч тэд хуулийн хүрээнд үйл ажиллагаа явуулдаг (хэдийгээр хуулийн зарим саарал хэсэгт байдаг ч). Уламжлалт "warez" болон далайн дээрэмчдийн үзэгдлүүд нь ижил төстэй сэтгэлгээтэй хүмүүсийг агуулдаг. Бид хамт олныг хэрхэн хөгжүүлэх, санааг судлах талаар санаа бодлыг нээлттэй хүлээн авдаг. Twitter эсвэл Reddit дээр бидэнд мессеж илгээгээрэй. Магадгүй бид ямар нэгэн форум эсвэл чат групп зохион байгуулж болох юм. Нэг сорилт бол энэ нь нийтлэг платформ ашиглах үед амархан цензурдэгддэг тул бид үүнийг өөрсдөө зохион байгуулах шаардлагатай болно. Эдгээр хэлэлцүүлгийг бүрэн нийтийн болгох (илүү их оролцоо) ба хувийн болгох (боломжит "бай" болох хүмүүсийг бид тэднийг хусах гэж байгаагаа мэдэгдэхгүй байх) хоорондын зөрчилдөөн бас бий. Үүнийг бодох хэрэгтэй болно. Хэрэв та үүнд сонирхолтой бол бидэнд мэдэгдээрэй! Дүгнэлт Шинээр эхэлж буй далайн дээрэмчдийн архивчдад энэ нь тус болно гэж найдаж байна. Бид таныг энэ ертөнцөд тавтай морилохыг тэсэн ядан хүлээж байна, тиймээс бидэнтэй холбоо барихаас бүү эргэлзээрэй. Дэлхийн мэдлэг, соёлыг аль болох ихээр хадгалж, алс хол түгээцгээе. Төслүүд 4. Өгөгдөл сонгох Ихэвчлэн та metadata-г ашиглан татаж авах боломжтой өгөгдлийн боломжийн дэд хэсгийг тодорхойлж болно. Эцэст нь бүх өгөгдлийг татаж авахыг хүссэн ч гэсэн хамгийн чухал зүйлсийг эхлээд эрэмбэлэх нь ашигтай байж болох юм, учир нь та илрэх магадлалтай бөгөөд хамгаалалтыг сайжруулах, эсвэл илүү олон диск худалдаж авах шаардлагатай болох, эсвэл зүгээр л амьдралд өөр зүйл тохиолдохоос өмнө бүх зүйлийг татаж авах боломжгүй байж магадгүй. Жишээлбэл, цуглуулгад ижил үндсэн нөөцийн (ном эсвэл кино гэх мэт) олон хэвлэл байж болох бөгөөд нэг нь хамгийн сайн чанартай гэж тэмдэглэгдсэн байдаг. Эдгээр хэвлэлүүдийг эхлээд хадгалах нь маш их утга учиртай байх болно. Эцэст нь бүх хэвлэлүүдийг хадгалахыг хүсэж магадгүй, учир нь зарим тохиолдолд metadata буруу тэмдэглэгдсэн байж болох, эсвэл хэвлэлүүдийн хооронд үл мэдэгдэх наймаа байж болох (жишээлбэл, "хамгийн сайн хэвлэл" нь ихэнх талаараа хамгийн сайн байж болох ч бусад талаараа муу байж болох, жишээлбэл, кино нь өндөр нягтралтай боловч хадмал орчуулгагүй байх). Та мөн metadata өгөгдлийн сангаа сонирхолтой зүйлсийг олохын тулд хайж болно. Хамгийн том файл юу вэ, яагаад ийм том байна вэ? Хамгийн жижиг файл юу вэ? Зарим ангилал, хэл гэх мэт зүйлсийн хувьд сонирхолтой эсвэл хүлээгдээгүй хэв маяг байна уу? Давхардсан эсвэл маш төстэй гарчиг байна уу? Өгөгдөл нэмэгдсэн үед хэв маяг байна уу, жишээлбэл, нэг өдөр олон файл нэг дор нэмэгдсэн үү? Өгөгдлийн багцыг өөр өөр аргаар харах замаар их зүйлийг сурч болно. Манай тохиолдолд бид Z-Library номнуудыг Library Genesis-ийн md5 хэшүүдтэй давхардалгүй болгосноор татаж авах цаг болон дискний зайг ихээхэн хэмнэсэн. Энэ бол нэлээд өвөрмөц нөхцөл байдал юм. Ихэнх тохиолдолд аль хэдийн зөв хадгалагдсан файлуудын бүрэн хэмжээний мэдээллийн сан байдаггүй. Энэ нь өөрөө хэн нэгэнд асар их боломж юм. Торрент вэбсайтуудад аль хэдийн өргөн тархсан хөгжмийн болон киноны талаар тогтмол шинэчлэгддэг тойм байвал гайхалтай байх болно, тиймээс пират толин тусгалд оруулах нь бага ач холбогдолтой болно. 6. Түгээлт Танд өгөгдөл байгаа тул дэлхийн анхны пират толин тусгалыг (ихэвчлэн) эзэмшиж байна. Олон талаараа хамгийн хэцүү хэсэг нь дууссан ч хамгийн эрсдэлтэй хэсэг нь таны өмнө байна. Эцсийн эцэст, одоог хүртэл та нууцлаг байсан; радарын доор нисч байсан. Та зүгээр л сайн VPN ашиглах, ямар ч маягтанд хувийн мэдээллээ оруулахгүй байх (мэдээж), магадгүй тусгай хөтөчийн сесс ашиглах (эсвэл бүр өөр компьютер ашиглах) хэрэгтэй байсан. Одоо та өгөгдлийг түгээх хэрэгтэй. Манай тохиолдолд бид эхлээд номыг Library Genesis-д буцааж оруулахыг хүссэн боловч (уран зохиол ба уран зохиолын бус ангилал) үүнд хэцүү байдлыг хурдан олж мэдсэн. Тиймээс бид Library Genesis загварын торрент ашиглан түгээхээр шийдсэн. Хэрэв та аль хэдийн байгаа төсөлд хувь нэмэр оруулах боломжтой бол энэ нь таны цагийг ихээхэн хэмнэх болно. Гэсэн хэдий ч одоогоор сайн зохион байгуулалттай пират толин тусгалууд тийм ч олон биш байна. Тиймээс та өөрөө торрент тараахаар шийдсэн гэж бодъё. Эдгээр файлуудыг жижиг байлгахыг хичээ, ингэснээр тэдгээрийг бусад вэбсайтуудад толин тусгал хийхэд хялбар болно. Та өөрөө торрентуудыг үргэлжлүүлэн үргэлжлүүлэх хэрэгтэй болно, гэхдээ нууцлалтай хэвээр байна. Та VPN ашиглаж болно (порт дамжуулалттай эсвэл үгүй), эсвэл Seedbox-д зориулж тумблер Биткойноор төлбөр төлж болно. Хэрэв та эдгээр нэр томъёоны заримыг нь мэдэхгүй бол унших зүйл ихтэй байх болно, учир нь энд эрсдлийн харьцааг ойлгох нь чухал юм. Та өөрөө торрент файлуудыг одоо байгаа торрент вэбсайтууд дээр байршуулах боломжтой. Манай тохиолдолд бид вэбсайт хостлохыг сонгосон, учир нь бид мөн өөрсдийн философийг тодорхой байдлаар түгээхийг хүссэн. Та үүнийг өөрөө ижил төстэй байдлаар хийж болно (бид домэйн болон хостингдоо Njalla-г ашигладаг, тумблер Биткойноор төлбөр төлдөг), гэхдээ бидэнтэй холбоо барьж, таны торрентуудыг хостлохыг хүсч болно. Хэрэв энэ санаа амжилттай болвол бид цаг хугацааны явцад пират толин тусгалуудын иж бүрэн индексийг бүтээхийг хүсч байна. VPN сонголтын хувьд энэ талаар аль хэдийн их бичсэн тул бид зүгээр л нэр хүндээр нь сонгох ерөнхий зөвлөгөөг давтана. Хувийн нууцыг хамгаалах урт хугацааны туршлагатай, шүүхээр шалгагдсан ямар ч бүртгэлгүй бодлого нь хамгийн бага эрсдэлтэй сонголт гэж бид үзэж байна. Та бүх зүйлийг зөв хийсэн ч гэсэн эрсдлийг тэг болгох боломжгүй гэдгийг анхаарна уу. Жишээлбэл, торрентуудыг үргэлжлүүлэх үед өндөр сэдэлтэй үндэстэн-улсын жүжигчин VPN серверүүдийн орж ирж буй болон гарч буй өгөгдлийн урсгалыг хараад таныг хэн болохыг тодорхойлох боломжтой. Эсвэл та зүгээр л ямар нэгэн байдлаар алдаа гаргаж болно. Бид аль хэдийн алдаа гаргасан байх магадлалтай бөгөөд дахин алдаа гаргах болно. Аз болоход, үндэстэн улсууд далайн дээрэмд тийм ч их анхаарал хандуулдаггүй. Төслийн хувьд нэг шийдвэр гаргах нь үүнийг өмнөхтэй ижил нэрээр нийтлэх эсэх эсвэл үгүй юу гэдэг юм. Хэрэв та ижил нэрийг ашигласаар байвал өмнөх төслүүдийн үйл ажиллагааны аюулгүй байдлын алдаанууд таныг хазахад эргэж ирж болно. Гэхдээ өөр өөр нэрээр нийтлэх нь урт хугацааны нэр хүндийг бий болгохгүй гэсэн үг юм. Бид эхнээс нь хүчтэй үйл ажиллагааны аюулгүй байдалтай байхыг сонгосон тул ижил нэрийг ашигласаар байх боломжтой, гэхдээ бид алдаа гаргавал эсвэл нөхцөл байдал шаардлагатай бол өөр нэрээр нийтлэхээс татгалзахгүй. Үгийг тараах нь хэцүү байж болно. Бидний хэлсэнчлэн энэ бол одоо ч гэсэн орон зайтай нийгэмлэг юм. Бид анх Reddit дээр нийтэлсэн боловч үнэхээр Hacker News дээр татагдсан. Одоогоор бидний зөвлөмж бол хэд хэдэн газарт нийтэлж, юу болохыг харах явдал юм. Мөн бидэнтэй холбоо бариарай. Бид далайн дээрэмчдийн архивын хүчин чармайлтын талаар илүү ихийг түгээхийг хүсч байна. 1. Салбар сонголт / философи Хадгалах мэдлэг, соёлын өв дутагдахгүй байгаа нь дарамттай байж болно. Тиймээс өөрийн хувь нэмрээ юу байж болох талаар бодох нь ихэвчлэн ашигтай байдаг. Хүн бүр үүнийг өөр өөрөөр боддог боловч өөрөөсөө асууж болох зарим асуултууд энд байна: Бидний хувьд шинжлэх ухааныг урт хугацаанд хадгалах нь чухал байлаа. Бид Library Genesis-ийн талаар мэддэг байсан бөгөөд энэ нь олон удаа torrent-оор бүрэн толин тусгалтай болсон гэдгийг мэддэг байв. Бидэнд энэ санаа маш их таалагдсан. Нэг өдөр бидний нэг нь Library Genesis дээрээс зарим шинжлэх ухааны сурах бичгүүдийг хайж үзсэн боловч олж чадаагүй бөгөөд энэ нь үнэхээр бүрэн дүүрэн эсэхэд эргэлзээ төрүүлсэн. Дараа нь бид эдгээр сурах бичгүүдийг онлайнаар хайж, өөр газруудаас олсон бөгөөд энэ нь бидний төслийн үрийг суулгасан юм. Z-Library-ийн талаар мэдэхээсээ өмнө бид эдгээр номыг гараар цуглуулахыг оролдохгүй, харин одоо байгаа цуглуулгуудыг толин тусгал болгох, тэдгээрийг Library Genesis-д буцаан оруулах санаатай байсан. Танд ямар ур чадвар байгаа бөгөөд үүнийг ашиг тусад ашиглаж болох вэ? Жишээлбэл, хэрэв та онлайн аюулгүй байдлын мэргэжилтэн бол аюулгүй зорилтот байг олохын тулд IP блокийг ялах арга замыг олох боломжтой. Хэрэв та хамт олныг зохион байгуулахад гарамгай бол магадгүй та зорилгын эргэн тойронд зарим хүмүүсийг цуглуулж чадна. Энэ үйл явцын туршид сайн үйл ажиллагааны аюулгүй байдлыг хангахын тулд зарим програмчлалыг мэдэх нь ашигтай байдаг. Анхаарлаа төвлөрүүлэх өндөр хөшүүрэгтэй талбар юу байх вэ? Хэрэв та далайн дээрэмчдийн архивт X цаг зарцуулах гэж байгаа бол "мөнгөнийхөө төлөө хамгийн их ашиг" -ыг хэрхэн авах вэ? Та үүнийг бодож байгаа өвөрмөц арга замууд юу вэ? Та бусад хүмүүсийн анзаараагүй сонирхолтой санаа эсвэл хандлагатай байж магадгүй. Үүнд хэр их цаг зарцуулах вэ? Манай зөвлөгөө бол эхлээд багаас эхэлж, үүнийг сурч авснаар томоохон төслүүдийг хийх явдал юм, гэхдээ энэ нь бүхэлдээ эзэмдэж болно. Та яагаад үүнд сонирхолтой байна вэ? Та юунд дуртай вэ? Хэрэв бид бүгд өөрсдийн сонирхдог зүйлсийг архивладаг хүмүүсийн бүлгийг олж чадвал энэ нь их зүйлийг хамрах болно! Та өөрийн хүсэл тэмүүллийн талаар дундаж хүнээс илүү ихийг мэдэх болно, жишээлбэл, хадгалах чухал өгөгдөл юу вэ, хамгийн сайн цуглуулгууд болон онлайн хамт олон юу вэ гэх мэт. 3. Metadata хусах Нэмэгдсэн/өөрчлөгдсөн огноо: ингэснээр та өмнө нь татаж аваагүй файлуудыг дараа нь татаж авах боломжтой (гэхдээ үүнийг ихэвчлэн ID эсвэл hash ашиглан хийж болно). Hash (md5, sha1): файлыг зөв татаж авсан эсэхийг баталгаажуулах. ID: зарим дотоод ID байж болох ч ISBN эсвэл DOI гэх мэт ID-ууд ч бас хэрэгтэй. Файлын нэр / байршил Тайлбар, ангилал, шошго, зохиогчид, хэл гэх мэт. Хэмжээ: хэр их дискний зай хэрэгтэйг тооцоолох. Энд жаахан техникийн зүйлс рүү орцгооё. Вэбсайтуудаас metadata-г үнэхээр хусахын тулд бид зүйлийг нэлээд энгийн байлгасан. Бид Python скриптүүд, заримдаа curl, мөн үр дүнг хадгалах MySQL өгөгдлийн санг ашигладаг. Одоогоор бид зөвхөн ID-уудыг тоолж, HTML-ийг задлах замаар нэг эсвэл хоёр төрлийн хуудсыг хусах шаардлагатай байсан тул нарийн төвөгтэй вэбсайтуудыг зураглах боломжтой нарийн хусах програм хангамж ашиглаагүй. Хэрэв хялбархан тоолж болох хуудсууд байхгүй бол бүх хуудсыг олохыг оролддог зөв мөлхөгч хэрэгтэй байж магадгүй. Бүхэл бүтэн вэбсайтыг хусаж эхлэхээсээ өмнө хэсэг хугацаанд гараар хийхийг оролдоорой. Энэ нь хэрхэн ажилладаг талаар ойлголт авахын тулд өөрөө хэдэн арван хуудсыг үзээрэй. Заримдаа та энэ аргаар аль хэдийн IP блок эсвэл бусад сонирхолтой зан үйлийг олж мэдэх болно. Өгөгдөл хусахад ч мөн адил: энэ зорилтот зүйлд хэт гүнзгий орохоосоо өмнө түүний өгөгдлийг үр дүнтэй татаж авах боломжтой эсэхээ шалгаарай. Хязгаарлалтыг тойрч гарахын тулд та хэд хэдэн зүйлийг туршиж үзэж болно. Өөр хязгаарлалтгүйгээр ижил өгөгдлийг байршуулсан өөр IP хаяг эсвэл серверүүд байгаа юу? Зарим API төгсгөлүүд хязгаарлалтгүй байхад бусад нь хязгаарлалттай байдаг уу? Таны IP ямар хурдтайгаар татаж авахад блоклогддог вэ, хэр удаан? Эсвэл та блоклогдоогүй ч хурд нь буурсан уу? Хэрэв та хэрэглэгчийн бүртгэл үүсгэвэл байдал хэрхэн өөрчлөгдөх вэ? HTTP/2-г ашиглан холболтыг нээлттэй байлгаж, хуудсыг хүсэх хурдыг нэмэгдүүлэх үү? Нэг дор олон файлыг жагсаасан хуудсууд байгаа юу, тэнд жагсаасан мэдээлэл хангалттай юу? Та хадгалахыг хүсэж болох зүйлс: Бид үүнийг ихэвчлэн хоёр үе шаттай хийдэг. Эхлээд бид түүхий HTML файлуудыг ихэвчлэн шууд MySQL руу татаж авдаг (олон жижиг файлуудаас зайлсхийхийн тулд, үүнийг доор дэлгэрэнгүй ярилцана). Дараа нь, тусдаа алхамд, бид эдгээр HTML файлуудыг MySQL хүснэгтүүдэд задлан шинжилдэг. Ингэснээр та задлах коддоо алдаа олсон тохиолдолд бүх зүйлийг эхнээс нь дахин татаж авах шаардлагагүй болно, учир нь та зүгээр л шинэ кодоор HTML файлуудыг дахин боловсруулж болно. Мөн боловсруулах алхамыг зэрэгцүүлэн хийх нь ихэвчлэн хялбар байдаг тул цаг хэмнэх боломжтой (мөн та хусах ажиллаж байх үед боловсруулах кодыг бичиж болно, хоёр алхамыг нэг дор бичих шаардлагагүй). Эцэст нь, зарим зорилтот зүйлсийн хувьд metadata хусах нь бүх зүйл гэдгийг анхаарна уу. Тэнд зөв зохистой хадгалагдаагүй асар том metadata цуглуулгууд байдаг. Гарчиг Салбар сонголт / философи: Та ерөнхийдөө хаана анхаарлаа төвлөрүүлэхийг хүсэж байна вэ, яагаад? Таны ашиг тусад ашиглаж болох өвөрмөц хүсэл тэмүүлэл, ур чадвар, нөхцөл байдал юу вэ? Зорилтот сонголт: Та аль тодорхой цуглуулгыг толин тусгал болгох вэ? Metadata хусах: Файлуудын талаархи мэдээллийг каталогжуулах, ихэвчлэн илүү том файлуудыг өөрсдийг нь татаж авахгүйгээр. Өгөгдлийн сонголт: Metadata дээр үндэслэн одоо архивлахад хамгийн чухал өгөгдлийг нарийсгах. Бүх зүйл байж болох ч ихэвчлэн зай, зурвасын өргөнийг хэмнэх боломжтой арга байдаг. Өгөгдөл хусах: Үнэхээр өгөгдлийг авах. Түгээлт: Үүнийг торрент хэлбэрээр багцлах, хаа нэгтээ зарлах, хүмүүсийг тараахад хүргэх. 5. Өгөгдөл хусах Одоо та үнэхээр их хэмжээний өгөгдөл татаж авахад бэлэн боллоо. Өмнө дурдсанчлан, энэ үед та аль хэдийн хэд хэдэн файлыг гар аргаар татаж авсан байх ёстой, ингэснээр зорилтот объектын зан байдал, хязгаарлалтыг илүү сайн ойлгох болно. Гэсэн хэдий ч та олон файлыг нэг дор татаж авах үед гэнэтийн зүйлс таныг хүлээж байх болно. Энд бидний зөвлөгөө бол голчлон энгийн байлгах явдал юм. Зүгээр л хэд хэдэн файлыг татаж авахаас эхэл. Та Python ашиглаж болно, дараа нь олон утас руу өргөжүүлнэ. Гэхдээ заримдаа бүр энгийн нь өгөгдлийн сангаас шууд Bash файлуудыг үүсгэж, дараа нь олон терминал цонхонд олон удаа ажиллуулах явдал юм. Энд дурьдах нь зүйтэй хурдан техникийн арга бол MySQL-д OUTFILE ашиглах явдал юм, үүнийг та mysqld.cnf-д "secure_file_priv"-ийг идэвхгүй болгосон тохиолдолд хаана ч бичиж болно (мөн хэрэв та Linux дээр байгаа бол AppArmor-ыг идэвхгүй болгох/давхцахаа мартуузай). Бид өгөгдлийг энгийн хатуу диск дээр хадгалдаг. Юу байгаагаас эхэлж, аажмаар өргөжүүлнэ үү. Зуун терабайт өгөгдлийг хадгалах талаар бодоход хэцүү байж болно. Хэрэв та ийм нөхцөл байдалтай тулгарч байгаа бол эхлээд сайн дэд хэсгийг гаргаж, зарлалдаа үлдсэнийг хадгалахад туслахыг хүсээрэй. Хэрэв та өөрөө илүү их хатуу диск авахыг хүсч байвал r/DataHoarder нь сайн наймаа хийхэд зориулсан сайн эх сурвалжуудтай. Тансаг файлын системүүдийн талаар хэт их санаа зовохгүй байхыг хичээ. ZFS гэх мэт зүйлсийг тохируулах нүхэнд орох нь амархан. Гэхдээ мэдэж байх ёстой нэг техникийн нарийн зүйл бол олон файлын системүүд олон файлуудтай сайн харьцдаггүй явдал юм. Бидний олж мэдсэн энгийн шийдэл бол өөр өөр ID хүрээ эсвэл хэш угтваруудын хувьд олон лавлах үүсгэх явдал юм. Өгөгдлийг татаж авсны дараа, боломжтой бол metadata дахь хэшүүдийг ашиглан файлуудын бүрэн бүтэн байдлыг шалгаарай. 2. Зорилтот сонголт Хүртээмжтэй: metadata болон өгөгдлийг хусахаас сэргийлэх олон давхар хамгаалалт ашигладаггүй. Тусгай ойлголт: энэ зорилтот талаар тусгай мэдээлэлтэй байх, жишээлбэл, энэ цуглуулгад тусгай хандалттай байх, эсвэл тэдний хамгаалалтыг хэрхэн даван туулахыг олж мэдсэн байх. Энэ нь шаардлагагүй (манай ирэх төсөл ямар нэгэн онцгой зүйл хийхгүй), гэхдээ энэ нь мэдээж тусалдаг! Том Тэгэхээр бидний хайж буй талбар байгаа, одоо аль тодорхой цуглуулгыг толин тусгал болгох вэ? Сайн зорилтот болгох хэд хэдэн зүйл байдаг: Бид шинжлэх ухааны сурах бичгүүдээ Library Genesis-ээс бусад вэбсайтаас олж, тэдгээр нь хэрхэн интернетэд байршуулсан болохыг ойлгохыг хичээсэн. Тэгээд бид Z-Library-г олж, ихэнх ном эхлээд тэнд гарч ирдэггүй ч эцэстээ тэнд байрладаг болохыг ойлгосон. Бид түүний Library Genesis-тэй харилцаа, (санхүүгийн) урамшууллын бүтэц болон хэрэглэгчийн интерфэйсийн давуу талуудыг мэдэж авсан бөгөөд энэ нь илүү бүрэн цуглуулга болсон юм. Дараа нь бид урьдчилсан metadata болон өгөгдөл хусах ажлыг хийж, манай гишүүдийн нэгийн олон прокси серверт тусгай хандалтыг ашиглан IP татаж авах хязгаарлалтыг тойрч гарах боломжтойг ойлгосон. Өөр өөр зорилтуудыг судалж байхдаа VPN болон түр зуурын имэйл хаягуудыг ашиглан мөрөө нуух нь аль хэдийн чухал юм, үүнийг бид дараа нь илүү дэлгэрэнгүй ярих болно. Өвөрмөц: бусад төслүүдээр аль хэдийн сайн хамрагдаагүй. Бид төсөл хийхдээ хэд хэдэн үе шаттай байдаг: Эдгээр нь бүрэн бие даасан үе шатууд биш бөгөөд ихэвчлэн дараагийн үе шатнаас олж авсан ойлголтууд таныг өмнөх үе шат руу буцааж илгээдэг. Жишээлбэл, metadata хусах явцад та сонгосон зорилтот бай нь таны ур чадварын түвшингээс давсан хамгаалалтын механизмуудтай (жишээ нь IP блок) гэдгийг ойлгож магадгүй тул та буцаж очоод өөр зорилтот байг олох хэрэгтэй болно. - Анна болон баг (<a %(reddit)s>Reddit</a>) Дижитал хадгалалтын тухай ерөнхийдөө, ялангуяа далайн дээрэмчдийн архивын тухай <em>яагаад</em> гэдэг талаар бүхэл бүтэн ном бичиж болно, гэхдээ бид энэ талаар сайн мэдэхгүй хүмүүст хурдан танилцуулга өгье. Дэлхий урьд өмнө хэзээ ч байгаагүй их мэдлэг, соёлыг бүтээж байгаа боловч урьд өмнө хэзээ ч байгаагүй ихийг нь алдаж байна. Хүн төрөлхтөн энэ өвийг академийн хэвлэн нийтлэгчид, урсгал үйлчилгээ, нийгмийн мэдээллийн компаниуд гэх мэт корпорациудад ихэвчлэн даатгадаг бөгөөд тэд ихэвчлэн агуу удирдагчид гэдгээ нотолж чадаагүй байдаг. Digital Amnesia баримтат киног эсвэл Жейсон Скоттын ямар ч яриаг үзээрэй. Тэдний чаддаг бүхнийг архивлах сайн ажил хийдэг зарим байгууллагууд байдаг ч тэд хуулиар хязгаарлагддаг. Далайн дээрэмчид бид зохиогчийн эрхийн хэрэгжилт эсвэл бусад хязгаарлалтын улмаас тэдний хүрч чадахгүй цуглуулгуудыг архивлах өвөрмөц байр суурьтай байдаг. Мөн бид цуглуулгуудыг дэлхий даяар олон удаа толь бичиглэж, зохих ёсоор хадгалагдах магадлалыг нэмэгдүүлэх боломжтой. Одоохондоо бид оюуны өмчийн давуу болон сул талууд, хуулийг зөрчих ёс суртахуун, цензурын талаархи бодол, мэдлэг, соёлд хандах асуудлын талаар хэлэлцэхгүй. Энэ бүхнийг ардаа орхиод, <em>хэрхэн</em> рүү шумбая. Манай баг хэрхэн далайн дээрэмчдийн архивч болсон, энэ замд сурсан сургамжуудаа хуваалцах болно. Та энэ аялалд гарахдаа олон сорилттой тулгардаг бөгөөд бид тэдгээрийн заримыг даван туулахад тань тусална гэж найдаж байна. Далайн дээрэмчдийн архивч болох арга зам Эхний сорилт нь гайхмаар байж магадгүй. Энэ нь техникийн асуудал эсвэл хууль эрх зүйн асуудал биш юм. Энэ бол сэтгэл зүйн асуудал юм. Бид эхлэхээсээ өмнө Pirate Library Mirror-ийн хоёр шинэчлэлтийг (EDIT: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн) хүргэе: Бид маш өгөөмөр хандив авсан. Эхнийх нь Library Genesis-ийн анхны үүсгэн байгуулагч "bookwarrior"-ийг дэмжиж байсан нэрээ нууцалсан хувь хүнээс 10 мянган доллар байсан. Энэхүү хандивыг хөнгөвчилсөнд bookwarrior-д тусгайлан талархал илэрхийлье. Хоёр дахь нь манай сүүлийн хувилбарын дараа холбоо барьж, туслах урам зориг авсан нэрээ нууцалсан хандивлагчийн 10 мянган доллар байсан. Мөн хэд хэдэн жижиг хандив байсан. Та бүхний өгөөмөр дэмжлэгт маш их баярлалаа. Бидэнд энэ дэмжлэгээр хэрэгжүүлэх сонирхолтой шинэ төслүүд байгаа тул хүлээж байгаарай. Манай хоёр дахь хувилбарын хэмжээтэй холбоотой техникийн бэрхшээлүүд байсан ч манай torrents одоо үржиж байна. Мөн нэрээ нууцалсан хувь хүнээс манай цуглуулгыг маш өндөр хурдтай серверүүд дээр үржүүлэх өгөөмөр санал ирсэн тул бид тэдний машин руу тусгайлан байршуулах ажлыг хийж байна, үүний дараа цуглуулгыг татаж авах бусад бүх хүн хурдны томоохон сайжруулалтыг харах болно. Блогийн нийтлэлүүд Сайн байна уу, би Анна байна. Би <a %(wikipedia_annas_archive)s>Аннагийн Архив</a>-ыг, дэлхийн хамгийн том сүүдрийн номын санг үүсгэсэн. Энэ бол миний хувийн блог бөгөөд би болон миний багийнхан далайн дээрэм, дижитал хадгалалт болон бусад сэдвээр бичдэг. Надтай <a %(reddit)s>Reddit</a> дээр холбогдоорой. Энэ вэбсайт нь зөвхөн блог гэдгийг анхаарна уу. Бид зөвхөн өөрсдийн үгсийг энд байршуулдаг. Энд ямар ч торрент эсвэл бусад зохиогчийн эрхтэй файлууд байршуулдаггүй эсвэл холбоос өгдөггүй. <strong>Номын сан</strong> - Ихэнх номын сангийн адил бид голчлон ном гэх мэт бичгийн материалд анхаарлаа хандуулдаг. Ирээдүйд бид бусад төрлийн медиа руу өргөжиж магадгүй. <strong>Толин тусгал</strong> - Бид зөвхөн одоо байгаа номын сангуудын толин тусгал юм. Бид хадгалалтанд анхаардаг бөгөөд номыг хялбархан хайж олох, татаж авах (хандах) эсвэл шинэ ном нэмэхэд хувь нэмэр оруулдаг томоохон олон нийтийг дэмжихэд анхаардаггүй. <strong>Далайн дээрэмчин</strong> - Бид ихэнх улс оронд зохиогчийн эрхийн хуулийг зориуд зөрчдөг. Энэ нь бидэнд хууль ёсны байгууллагууд хийж чадахгүй зүйлийг хийх боломжийг олгодог: номыг өргөн хүрээнд толин тусгал болгох. <em>Бид энэ блог дээрээс файлууд руу холбоос тавьдаггүй. Өөрөө олоорой.</em> - Анна болон баг (<a %(reddit)s>Reddit</a>) Энэ төсөл (ЗАСВАР: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн) нь хүний мэдлэгийг хадгалах, чөлөөлөхөд хувь нэмэр оруулах зорилготой. Бид өмнөх агуу хүмүүсийн мөрөөр жижиг, даруухан хувь нэмрээ оруулж байна. Энэ төслийн гол зорилго нь нэрээрээ тодорхойлогддог: Бидний толин тусгал болгосон анхны номын сан бол Z-Library юм. Энэ бол алдартай (хууль бус) номын сан юм. Тэд Library Genesis цуглуулгыг авч, хялбархан хайж олох боломжтой болгосон. Үүнээс гадна, тэд шинэ номын хувь нэмрийг урамшуулан дэмжих замаар маш үр дүнтэй болсон. Одоогоор тэд эдгээр шинэ номыг Library Genesis-д буцааж оруулдаггүй. Мөн Library Genesis-ээс ялгаатай нь, тэд цуглуулгаа хялбархан толин тусгал болгох боломжийг олгодоггүй бөгөөд энэ нь өргөн хүрээнд хадгалагдахаас сэргийлдэг. Энэ нь тэдний бизнесийн загварт чухал ач холбогдолтой, учир нь тэд цуглуулгаа бөөнөөр нь (өдөрт 10-аас дээш ном) хандахад мөнгө авдаг. Бид хууль бус номын цуглуулгад бөөнөөр хандахад мөнгө авах талаар ёс суртахууны дүгнэлт гаргадаггүй. Z-Library нь мэдлэгт хандах боломжийг өргөжүүлэх, илүү олон номыг эх сурвалж болгоход амжилттай байсан нь эргэлзээгүй. Бид зүгээр л өөрсдийн үүргээ гүйцэтгэхээр энд байна: энэ хувийн цуглуулгыг урт хугацаанд хадгалахыг баталгаажуулах. Бид таныг манай torrents-ийг татаж, үргэлжлүүлэн хүний мэдлэгийг хадгалж, чөлөөлөхөд туслахыг урьж байна. Өгөгдлийг хэрхэн зохион байгуулсан талаар илүү их мэдээлэл авахыг хүсвэл төслийн хуудсыг үзнэ үү. Мөн бид таныг дараагийн толин тусгал болгох цуглуулгуудын талаар болон үүнийг хэрхэн хийх талаар санаагаа хуваалцахыг урьж байна. Хамтдаа бид ихийг хийж чадна. Энэ бол тоо томшгүй олон хүмүүсийн дунд хийж буй жижигхэн хувь нэмэр юм. Таны хийж буй бүх зүйлд баярлалаа. Далайн дээрэмчдийн номын сангийн толин тусгал: Libgen-д байхгүй 7TB номыг хадгалах нь 10% oф хүн төрөлхтний бичгийн өвийг үүрд хадгалсан <strong>Google.</strong> Эцсийн эцэст, тэд Google Books-ийн судалгааг хийсэн. Гэсэн хэдий ч, тэдний metadata нь бөөнөөр хандах боломжгүй бөгөөд хусахад нэлээд хэцүү байдаг. <strong>Төрөл бүрийн бие даасан номын сангийн системүүд болон архивууд.</strong> Дээрхүүдээс индексжүүлж, нэгтгээгүй номын сан, архивууд байдаг бөгөөд ихэвчлэн санхүүжилт муутай эсвэл бусад шалтгаанаар Open Library, OCLC, Google гэх мэттэй өгөгдлөө хуваалцахыг хүсдэггүй. Эдгээрийн олонх нь интернетээр дамжуулан хандах боломжтой дижитал бичлэгүүдтэй бөгөөд тэдгээр нь ихэвчлэн сайн хамгаалагдаагүй байдаг тул хэрэв та туслахыг хүсэж, хачин номын сангийн системүүдийн талаар суралцахыг хүсвэл эдгээр нь гайхалтай эхлэл цэгүүд юм. <strong>ISBNdb.</strong> Энэ бол энэ блог нийтлэлийн сэдэв юм. ISBNdb нь янз бүрийн вэбсайтуудаас номын metadata-г, ялангуяа үнийн мэдээллийг хусаж, дараа нь номын худалдаачдад зарж, тэдгээрийг зах зээлийн бусад хэсэгтэй нийцүүлэн номынхоо үнийг тогтоох боломжийг олгодог. Өнөө үед ISBN нь нэлээд түгээмэл байдаг тул тэд үр дүнтэйгээр “ном бүрийн вэб хуудас” бүтээсэн. <strong>Open Library.</strong> Өмнө дурдсанчлан, энэ нь тэдний бүхэл бүтэн эрхэм зорилго юм. Тэд хамтран ажилладаг номын сан, үндэсний архивуудаас асар их хэмжээний номын сангийн өгөгдлийг цуглуулсан бөгөөд үргэлжлүүлэн хийж байна. Тэд мөн сайн дурын номын санчид болон бичлэгүүдийг давхардуулан арилгах, төрөл бүрийн metadata-аар тэмдэглэхийг оролдож буй техникийн багтай. Хамгийн сайхан нь, тэдний өгөгдлийн багц нь бүрэн нээлттэй байдаг. Та зүгээр л <a %(openlibrary)s>татаж авах</a> боломжтой. <strong>WorldCat.</strong> Энэ бол ашгийн бус OCLC-ийн удирддаг вэбсайт бөгөөд номын сангийн удирдлагын системийг зардаг. Тэд олон номын сангаас номын metadata-г цуглуулж, WorldCat вэбсайтаар дамжуулан ашиглах боломжтой болгодог. Гэсэн хэдий ч, тэд энэ өгөгдлийг зарж мөнгө олдог тул бөөнөөр татаж авах боломжгүй. Тэд тодорхой номын сангуудтай хамтран татаж авах боломжтой хязгаарлагдмал хэмжээний бөөнөөр татаж авах боломжтой өгөгдлийн багцуудтай. "Үүрд" гэсэн үгийн боломжийн тодорхойлолт. ;) Мэдээж, хүн төрөлхтний бичгийн өв нь номоос илүү их зүйл бөгөөд ялангуяа өнөө үед. Энэ нийтлэл болон манай сүүлийн үеийн гаргалтуудын хувьд бид номонд анхаарлаа хандуулж байгаа боловч бидний сонирхол цаашид өргөжиж байна. Аарон Шварцын талаар илүү ихийг хэлж болох ч бид түүнийг товчхон дурдахыг хүссэн юм, учир нь тэр энэ түүхэнд чухал үүрэг гүйцэтгэдэг. Цаг хугацаа өнгөрөхөд илүү олон хүн түүний нэрийг анх удаа сонсож, улмаар өөрсдөө энэ сэдэвт гүнзгийрч орж болно. <strong>Физик хуулбарууд.</strong> Мэдээжийн хэрэг, эдгээр нь зүгээр л ижил материалын хуулбарууд тул энэ нь тийм ч их тус болохгүй. Хүмүүсийн номонд хийсэн бүх тайлбарыг, жишээлбэл, Ферматын алдартай “захын тэмдэглэлүүд” гэх мэт хадгалж чадвал гайхалтай байх болно. Гэхдээ харамсалтай нь энэ нь архивчдын мөрөөдөл хэвээр үлдэх болно. <strong>“Хувилбарууд”.</strong> Энд та номын бүх өвөрмөц хувилбарыг тоолдог. Хэрэв ямар нэг зүйл өөр байвал, жишээлбэл, өөр хавтас эсвэл өөр оршил, энэ нь өөр хувилбар гэж тооцогддог. <strong>Файлууд.</strong> Library Genesis, Sci-Hub, эсвэл Z-Library зэрэг сүүдрийн номын сангуудтай ажиллах үед нэмэлт анхаарал шаардлагатай. Нэг хэвлэлийн олон хувилбарын скан байж болно. Мөн хүмүүс OCR ашиглан текстийг скан хийх, эсвэл өнцөгт скан хийсэн хуудсуудыг засварлах замаар одоо байгаа файлуудын илүү сайн хувилбаруудыг хийж болно. Бид эдгээр файлуудыг нэг хэвлэл гэж тооцохыг хүсэж байгаа бөгөөд үүний тулд сайн metadata эсвэл баримт бичгийн ижил төстэй байдлыг ашиглан давхардлыг арилгах шаардлагатай болно. <strong>“Бүтээлүүд”.</strong> Жишээлбэл, “Харри Поттер ба Нууцын өрөө” нь өөр өөр орчуулга, дахин хэвлэл зэрэг бүх хувилбарыг багтаасан логик ойлголт юм. Энэ нь ашигтай тодорхойлолт боловч юуг тооцохыг тодорхойлоход хэцүү байж болно. Жишээлбэл, бид магадгүй өөр өөр орчуулгыг хадгалахыг хүсч байгаа боловч зөвхөн бага зэргийн ялгаатай дахин хэвлэлүүд нь тийм ч чухал биш байж магадгүй. - Анна болон баг (<a %(reddit)s>Reddit</a>) Далайн дээрэмчдийн номын сангийн толин тусгал (EDIT: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн) ашиглан бид дэлхийн бүх номыг авч, үүрд хадгалахыг зорьж байна.<sup>1</sup> Манай Z-Library torrents болон анхны Library Genesis torrents-ийн хооронд бид 11,783,153 файлтай. Гэхдээ энэ нь үнэхээр хэд вэ? Хэрэв бид эдгээр файлуудыг зөв хасвал дэлхийн бүх номын хэдэн хувийг бид хадгалсан бэ? Бид үнэхээр ийм зүйлтэй байхыг хүсч байна: Зарим бүдүүн тойм тоонуудыг эхлүүлье: Z-Library/Libgen болон Open Library-д өвөрмөц ISBN-ээс илүү олон ном байдаг. Энэ нь олон ном ISBN-гүй гэсэн үг үү, эсвэл ISBN metadata зүгээр л алга байна уу? Бид энэ асуултад бусад шинж чанарууд (гарчиг, зохиогч, хэвлэн нийтлэгч гэх мэт) дээр суурилсан автомат тохиролцооны хослол, илүү олон өгөгдлийн эх сурвалжийг татах, ISBN-ийг номын скануудаас өөрсдөөс нь гаргаж авах замаар хариулж чадна (Z-Library/Libgen-ийн хувьд). Тэдгээр ISBN-ээс хичнээн нь өвөрмөц вэ? Үүнийг Веннийн диаграмаар хамгийн сайн дүрслэн харуулж болно: Илүү нарийвчлалтай хэлэхэд: Бид хэрхэн бага давхцалтай байгаад гайхсан! ISBNdb нь Z-Library эсвэл Open Library-д аль алинд нь гарч ирдэггүй асар их хэмжээний ISBN-үүдтэй бөгөөд бусад хоёрын хувьд ч мөн адил (бага боловч бас ихээхэн хэмжээгээр) байна. Энэ нь олон шинэ асуултыг бий болгодог. ISBN-гүй номыг шошголохдоо автомат тохиролцоо хэр их тус болох вэ? Олон тохиролцоо гарч, улмаар давхцал нэмэгдэх үү? Мөн 4 дэх эсвэл 5 дахь өгөгдлийн багцыг оруулбал юу болох вэ? Тэр үед бид хэр их давхцал харах вэ? Энэ нь бидэнд эхлэх цэг өгдөг. Бид одоо Z-Library өгөгдлийн багцад ороогүй, гарчиг/зохиогчийн талбаруудтай тохирохгүй бүх ISBN-ийг харж болно. Энэ нь дэлхийн бүх номыг хадгалахад туслах боломжийг олгоно: эхлээд интернетээс скан хийгээд, дараа нь бодит амьдрал дээр номыг скан хийх. Сүүлийнх нь бүр олон нийтийн санхүүжилтээр эсвэл тодорхой номыг дижитал хэлбэрт оруулахыг хүсдэг хүмүүсийн “шагнал”-аар хийгдэж болно. Энэ бүхэн бол өөр цаг үеийн түүх юм. Хэрэв та үүнтэй холбоотой ямар нэгэн зүйлд туслахыг хүсвэл — цаашдын шинжилгээ; илүү их metadata хусах; илүү олон ном олох; номын OCR хийх; үүнийг бусад домэйнд хийх (жишээ нь, эрдэм шинжилгээний өгүүлэл, аудио ном, кино, телевизийн шоу, сэтгүүл) эсвэл бүр ML / том хэлний загвар сургалт зэрэг зүйлд энэ өгөгдлийг ашиглах боломжтой болгох — надтай холбоо барина уу (<a %(reddit)s>Reddit</a>). Хэрэв та өгөгдлийн шинжилгээнд сонирхолтой байгаа бол бид өгөгдлийн багц болон скриптүүдээ илүү хялбар ашиглах боломжтой хэлбэрээр гаргах дээр ажиллаж байна. Та зүгээр л тэмдэглэлийн дэвтэрийг салаалж, үүнтэй тоглож эхлэх боломжтой бол гайхалтай байх болно. Эцэст нь, хэрэв та энэ ажлыг дэмжихийг хүсвэл хандив өгөхийг бодоорой. Энэ бол бүрэн сайн дурын ажиллагаа бөгөөд таны хувь нэмэр асар их өөрчлөлт авчирдаг. Бүх зүйл тусалдаг. Одоогоор бид крипто хэлбэрээр хандив авдаг; Аннагийн Архивын Хандив хуудас дээрээс үзнэ үү. Хувь хэмжээг гаргахын тулд бидэнд хуваарь хэрэгтэй: нийт хэвлэгдсэн номын тоо.<sup>2</sup> Google Books-ийн төгсгөлөөс өмнө төслийн инженер Леонид Тайчер энэ тоог <a %(booksearch_blogspot)s>тооцоолохыг оролдсон</a>. Тэрээр 129,864,880 (“ямар ч байсан Ням гараг хүртэл”) гэсэн тоог хошигнон гаргаж ирсэн. Тэрээр дэлхийн бүх номын нэгдсэн мэдээллийн санг бүтээж энэ тоог тооцоолсон. Үүний тулд тэрээр янз бүрийн өгөгдлийн багцыг нэгтгэж, дараа нь янз бүрийн аргаар нэгтгэсэн. Түр зуурын хажуугаар, дэлхийн бүх номыг каталоглохыг оролдсон өөр нэг хүн бий: хожуу дижитал идэвхтэн, Reddit-ийн хамтран үүсгэн байгуулагч Аарон Шварц.<sup>3</sup> Тэрээр олон янзын эх сурвалжаас өгөгдлийг нэгтгэн “хэзээ нэгэн цагт хэвлэгдсэн ном бүрийн нэг вэб хуудас” зорилготой <a %(youtube)s>Open Library</a>-г эхлүүлсэн. Тэрээр академик өгүүллэлийг бөөнөөр нь татаж авахад яллагдаж, амиа хорлоход хүргэсэн нь түүний дижитал хадгалалтын ажлын төлөө эцсийн төлөөсийг төлсөн. Хэлэх шаардлагагүй, энэ бол манай бүлэг нууц нэртэй байгаа шалтгаануудын нэг бөгөөд бид маш болгоомжтой хандаж байна. Open Library нь Интернет Архивын хүмүүсийн удирдлага дор Аароны өвийг үргэлжлүүлэн баатарлаг байдлаар ажиллаж байна. Бид энэ талаар дараа нь энэ нийтлэлд эргэн орох болно. Google-ийн блог нийтлэлд Тайчер энэ тоог тооцоолоход тулгардаг зарим бэрхшээлийг тайлбарласан. Нэгдүгээрт, ном гэж юу вэ? Хэд хэдэн боломжит тодорхойлолт байдаг: “Хэвлэлүүд” нь “ном” гэж юу болохыг хамгийн практик тодорхойлолт юм шиг санагдаж байна. Аз болоход, энэ тодорхойлолт нь өвөрмөц ISBN дугааруудыг хуваарилахад ашиглагддаг. ISBN буюу Олон улсын стандарт номын дугаар нь олон улсын худалдаанд түгээмэл ашиглагддаг, учир нь энэ нь олон улсын баркод системтэй (”Олон улсын барааны дугаар”) нэгдсэн байдаг. Хэрэв та дэлгүүрт ном зарахыг хүсвэл баркод хэрэгтэй, тиймээс та ISBN авдаг. Тайчерийн блог нийтлэлд ISBN нь ашигтай боловч бүх нийтийн биш гэдгийг дурдсан байдаг, учир нь тэдгээрийг зөвхөн дал наяад оны үед л нэвтрүүлсэн бөгөөд дэлхийн хаа сайгүй биш юм. Гэсэн хэдий ч, ISBN нь номын хэвлэлийн хамгийн өргөн хэрэглэгддэг таних тэмдэг байх магадлалтай тул энэ нь бидний хамгийн сайн эхлэл цэг юм. Хэрэв бид дэлхийн бүх ISBN-ийг олж чадвал, хадгалах шаардлагатай номын жагсаалтыг авах болно. Тэгэхээр, бид хаанаас өгөгдлийг авах вэ? Дэлхийн бүх номын жагсаалтыг бүрдүүлэхийг оролдож буй хэд хэдэн хүчин чармайлт байдаг: Энэ нийтлэлд бид бага хэмжээний хувилбарыг (өмнөх Z-Library хувилбаруудтай харьцуулахад) гаргаж байгаадаа баяртай байна. Бид ISBNdb-ийн ихэнхийг хусаж, Pirate Library Mirror вэбсайт дээр torrent-ээр дамжуулан өгөгдлийг ашиглах боломжтой болгосон (ЗАСВАР: <a %(wikipedia_annas_archive)s>Аннагийн Архив</a> руу шилжсэн; бид үүнийг энд шууд холбоосоор өгөхгүй, зүгээр л хайгаарай). Эдгээр нь ойролцоогоор 30.9 сая бичлэг (20GB <a %(jsonlines)s>JSON Lines</a> хэлбэрээр; 4.4GB шахсан) юм. Тэдний вэбсайт дээр тэд үнэндээ 32.6 сая бичлэгтэй гэж мэдэгддэг тул бид яагаад ч юм заримыг нь алдаж магадгүй, эсвэл <em>тэд</em> ямар нэгэн зүйл буруу хийж байж магадгүй. Ямар ч байсан, одоогоор бид үүнийг хэрхэн хийснээ яг нарийн хуваалцахгүй — үүнийг уншигчдад дасгал болгон үлдээе. ;-) Бидний хуваалцах зүйл бол дэлхийн номын тоог тооцоолохтой ойртохын тулд зарим урьдчилсан шинжилгээ юм. Бид гурван өгөгдлийн багцыг үзсэн: энэ шинэ ISBNdb өгөгдлийн багц, Z-Library сүүдрийн номын сангаас хуссан metadata-ийн анхны хувилбар (Library Genesis-ийг багтаасан), болон Open Library өгөгдлийн хаягдал. ISBNdb dump, эсвэл Хэдэн ном үүрд хадгалагдах вэ? Хэрэв бид сүүдрийн номын сангийн файлуудыг зөв хасвал дэлхийн бүх номын хэдэн хувийг бид хадгалсан бэ? Хүн төрөлхтний түүхэн дэх хамгийн том үнэхээр нээлттэй номын сан болох <a %(wikipedia_annas_archive)s>Аннагийн Архив</a>-ын тухай шинэчлэлтүүд. <em>WorldCat шинэчлэл</em> Өгөгдөл <strong>Формат?</strong> <a %(blog)s>Аннагийн Архивын Контейнерууд (AAC)</a>, үндсэндээ <a %(jsonlines)s>JSON Lines</a>-ийг <a %(zstd)s>Zstandard</a>-аар шахаж, зарим стандартчилсан семантикуудыг нэмсэн. Эдгээр контейнерууд нь бидний ашигласан өөр өөр хусалтын төрлүүд дээр үндэслэн янз бүрийн төрлийн бүртгэлийг багтаадаг. Жилийн өмнө бид энэ асуултад хариулахыг <a %(blog)s>зорьсон</a>: <strong>Сүүдрийн номын сангуудаар хэдэн хувийн ном байнга хадгалагдсан бэ?</strong> Өгөгдлийн зарим үндсэн мэдээллийг харцгаая: Ном <a %(wikipedia_library_genesis)s>Library Genesis</a> гэх мэт нээлттэй өгөгдлийн сүүдрийн номын санд, одоо <a %(wikipedia_annas_archive)s>Аннагийн Архив</a>-д ормогц, дэлхий даяар (торрентуудаар дамжуулан) толин тусгал болж, үүрд хадгалагдах болно. Номын хэдэн хувь нь хадгалагдсан болохыг мэдэхийн тулд бид хуваарьлагчийг мэдэх хэрэгтэй: нийт хэдэн ном байдаг вэ? Мөн бид зөвхөн тоо биш, харин бодит metadata-тай байх нь зүйтэй. Тэгвэл бид тэдгээрийг сүүдрийн номын сангуудтай тааруулж, хадгалах шаардлагатай үлдсэн номын TODO жагсаалтыг <strong>үүсгэж</strong> чадна! Бид бүр энэ TODO жагсаалтыг давах олон нийтийн хүчин чармайлтын талаар мөрөөдөж эхэлж болно. Бид <a %(wikipedia_isbndb_com)s>ISBNdb</a>-ийг хусаж, <a %(openlibrary)s>Open Library dataset</a>-ийг татаж авсан боловч үр дүн нь хангалтгүй байв. Гол асуудал нь ISBN-уудын давхцал их байгаагүйд оршино. Манай <a %(blog)s>блогийн нийтлэлээс</a> энэ Венн диаграммыг үзнэ үү: ISBNdb болон Open Library хоёрын хооронд маш бага давхцал байгаад бид маш их гайхсан. Эдгээр нь хоёулаа вэб хусалт болон номын сангийн бүртгэл зэрэг янз бүрийн эх сурвалжаас өгөгдлийг чөлөөтэй оруулдаг. Хэрэв тэд хоёулаа ихэнх ISBN-уудыг олоход сайн ажилладаг бол тэдний тойргууд мэдээж ихээхэн давхцалтай байх байсан, эсвэл нэг нь нөгөөгийн дэд хэсэг байх байсан. Энэ нь биднийг эдгээр тойргуудаас <em>бүрэн гадуур</em> хичнээн ном унадаг вэ гэдэгт гайхахад хүргэсэн. Бидэнд илүү том мэдээллийн сан хэрэгтэй. Тэр үед бид дэлхийн хамгийн том номын мэдээллийн сан болох <a %(wikipedia_worldcat)s>WorldCat</a>-д анхаарлаа хандуулсан. Энэ бол ашгийн бус <a %(wikipedia_oclc)s>OCLC</a>-ийн эзэмшдэг өмчийн мэдээллийн сан бөгөөд дэлхийн өнцөг булан бүрээс номын сангийн metadata бүртгэлийг цуглуулж, эдгээр номын сангуудад бүрэн өгөгдлийн санд нэвтрэх боломжийг олгож, эцсийн хэрэглэгчдийн хайлтын үр дүнд гарч ирэх боломжийг олгодог. OCLC ашгийн бус байгууллага хэдий ч, тэдний бизнесийн загвар нь тэдний мэдээллийн санг хамгаалахыг шаарддаг. За, OCLC-ийн найзууд аа, бид бүгдийг нь тарааж байгаад уучлаарай. :-) Өнгөрсөн жил бид WorldCat-ийн бүх бүртгэлийг нарийн нягтлан хуссан. Эхэндээ бид азтай тохиолдолтой учирсан. WorldCat нь өөрсдийн вэбсайтын бүрэн шинэчлэлтийг (2022 оны 8-р сард) эхлүүлж байсан. Энэ нь тэдний арын системүүдийг ихээхэн шинэчилж, олон аюулгүй байдлын алдааг оруулсан. Бид тэр даруй боломжийг ашиглаж, хэдхэн хоногийн дотор хэдэн зуун сая (!) бүртгэлийг хусаж чадсан. Үүний дараа аюулгүй байдлын алдаануудыг нэг нэгээр нь аажмаар зассан бөгөөд бидний олсон сүүлийн алдаа нь нэг сарын өмнө засагдсан. Тэр үед бид бараг бүх бүртгэлийг авсан байсан бөгөөд зөвхөн бага зэрэг өндөр чанартай бүртгэлүүдийг л хайж байсан. Тиймээс бид гаргах цаг нь болсон гэж үзсэн! 1.3 тэрбум WorldCat хусалт <em><strong>Товчхон:</strong> Аннагийн Архив бүх WorldCat (дэлхийн хамгийн том номын сангийн metadata цуглуулга)-ыг хусаж, хадгалах шаардлагатай номын TODO жагсаалтыг гаргасан.</em> WorldCat Анхааруулга: энэ блог нийтлэл хуучирсан. Бид IPFS нь одоогоор ашиглахад бэлэн биш гэж шийдсэн. Бид Аннагийн Архиваас боломжтой үед IPFS дээрх файлууд руу холбоос хийх болно, гэхдээ бид өөрсдөө үүнийг хостлохгүй, мөн бусдыг IPFS ашиглан толин тусгал хийхийг зөвлөхгүй. Манай Торрентуудын хуудсыг үзэж, манай цуглуулгыг хадгалахад туслахыг хүсвэл үзнэ үү. Z-Номын санг IPFS дээр үржүүлэхэд тусална уу Түнш серверээс татах SciDB Гадаад зээл Гадаад зээл (хэвлэх боломжгүй) Гадаад таталт Мета өгөгдлийг судлах Торрентуудад агуулагдсан Буцах  (+%(num)s бонус) төлөөгүй төлсөн цуцлагдсан хугацаа дууссан Анна баталгаажуулахыг хүлээж байна хууль бус Доорх текст Англи хэл дээр үргэлжилнэ. Явах Дахин тохируулах Урагшлах Сүүлчийн Хэрэв таны имэйл хаяг Libgen форум дээр ажиллахгүй бол бид <a %(a_mail)s>Proton Mail</a> (үнэгүй) ашиглахыг зөвлөж байна. Мөн та <a %(a_manual)s>гараар хүсэлт гаргаж</a> дансаа идэвхжүүлэх боломжтой. (магадгүй <a %(a_browser)s>хөтөчийн баталгаажуулалт</a> шаардагдана — хязгааргүй татан авалт!) Хурдан түншийн сервер #%(number)s (зөвлөж байна) (бага зэрэг хурдан боловч хүлээлгийн жагсаалттай) (хөтөчийн баталгаажуулалт шаардлагагүй) (хөтөчийн баталгаажуулалт эсвэл хүлээлгийн жагсаалтгүй) (хүлээлгийн жагсаалтгүй, гэхдээ маш удаан байж болно) Удаан түншийн сервер #%(number)s Аудио ном Хүүхдийн комик ном Ном (уран зохиол) Ном (баримтат) Ном (үл мэдэгдэх) Эрдэм шинжилгээний өгүүлэл Сэтгүүл Хөгжмийн ноот Бусад Стандартын баримт бичиг Бүх хуудсыг PDF болгон хөрвүүлж чадсангүй Libgen.li-д эвдэрсэн гэж тэмдэглэгдсэн Libgen.li-д харагдахгүй байна Libgen.rs Fiction-д харагдахгүй байна Libgen.rs Non-Fiction-д харагдахгүй байна Энэ файл дээр exiftool ажиллуулахад алдаа гарлаа Z-Library-д “муу файл” гэж тэмдэглэгдсэн Z-Номын сангаас алга болсон Z-Library-д “спам” гэж тэмдэглэгдсэн Файл нээгдэхгүй байна (жишээ нь: эвдэрсэн файл, DRM) Зохиогчийн эрхийн нэхэмжлэл Татах асуудал (жишээ нь: холбогдож чадахгүй, алдааны мэдэгдэл, маш удаан) Буруу мета өгөгдөл (жишээ нь: гарчиг, тайлбар, хавтасны зураг) Бусад Муу чанар (жишээ нь: форматлах асуудал, муу сканны чанар, хуудас дутуу) Спам / файл устгах шаардлагатай (жишээ нь: зар сурталчилгаа, доромжилсон агуулга) %(amount)s (%(amount_usd)s) %(amount)s нийт %(amount)s (%(amount_usd)s) нийт Гайхалтай Номын Хорхойтон Азтай Номын Санч Гайхамшигт Өгөгдөл Цуглуулагч Гайхамшигт Архивч Нэмэлт таталт Cerlalc Чех мета өгөгдөл DuXiu 读秀 EBSCOhost eBook Index Google Ном Goodreads HathiTrust IA IA Хянагдсан Дижитал Зээлдүүлэлт ISBNdb ISBN GRP Libgen.li “scimag”-ийг хассан Libgen.rs Баримтат болон Уран зохиол Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Оросын Улсын Номын Сан Sci-Hub Libgen.li “scimag”-аар дамжуулан Sci-Hub / Libgen “scimag” Трантор AA-д оруулах Z-Номын сан Z-Library Хятад Гарчиг, зохиогч, DOI, ISBN, MD5, … Хайлт Зохиогч Тайлбар болон мета өгөгдлийн тайлбар Хувилбар Анхны файлын нэр Хэвлэн нийтлэгч (тусгай талбарт хайх) Гарчиг Хэвлэгдсэн он Техникийн дэлгэрэнгүй мэдээлэл Энэ зоосны хамгийн бага хэмжээ ердийнхөөс өндөр байна. Өөр хугацаа эсвэл өөр зоос сонгоно уу. Хүсэлтийг гүйцэтгэж чадсангүй. Хэдэн минутын дараа дахин оролдоно уу, хэрэв дахин давтагдвал бидэнтэй %(email)s хаягаар дэлгэцийн зурагтайгаа холбоо барина уу. Тодорхойгүй алдаа гарлаа. Бидэнтэй %(email)s хаягаар дэлгэцийн зурагтайгаа холбоо барина уу. Төлбөр боловсруулахад алдаа гарлаа. Түр хүлээгээд дахин оролдоно уу. Хэрэв асуудал 24 цагаас дээш үргэлжилбэл бидэнтэй %(email)s хаягаар дэлгэцийн зурагтайгаа холбоо барина уу. Бид дэлхийн хамгийн том комиксийн сүүдрийн номын санг <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">нөөцлөх</a> хандивын аян зохион байгуулж байна. Таны дэмжлэгт баярлалаа! <a href="/donate">Хандив өргөх.</a> Хэрэв та хандив өргөж чадахгүй бол найз нөхөддөө хэлж, <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, эсвэл <a href="https://t.me/annasarchiveorg">Telegram</a>-д дагаж дэмжээрэй. Бидэнд <a %(a_request)s>ном хүсэх</a><br>эсвэл жижиг (<10k) <a %(a_upload)s>байршуулалт</a> хийх талаар имэйл бичих хэрэггүй. Аннагийн Архив DMCA / зохиогчийн эрхийн нэхэмжлэлүүд Холбоотой байгаарай Reddit Сонголтууд SLUM (%(unaffiliated)s) холбоогүй Аннагийн Архивт таны тусламж хэрэгтэй байна! Хэрэв та одоо хандив өгвөл, <strong>хурдан татан авалтын</strong> тоо хоёр дахин нэмэгдэнэ. Олон хүн биднийг устгахыг оролддог ч бид тэмцсээр байна. Хэрэв та энэ сард хандив өргөвөл, <strong>хоёр дахин</strong> хурдан татаж авах боломжтой болно. Энэ сарын сүүл хүртэл хүчинтэй. Хүний мэдлэгийг хадгалах: гайхалтай баярын бэлэг! Гишүүнчлэлүүдийг үүний дагуу сунгах болно. Түнш серверүүд хостинг хаагдсаны улмаас ашиглах боломжгүй байна. Тэд удахгүй дахин ажиллах болно. Аннагийн Архивын тогтвортой байдлыг нэмэгдүүлэхийн тулд бид толь ажиллуулах сайн дурынхныг хайж байна. Бидэнд шинэ хандивын арга бий болсон: %(method_name)s. Та %(donate_link_open_tag)sхандив өргөхийг</a> бодолцоно уу — энэ вэбсайтыг ажиллуулахад хямд биш бөгөөд таны хандив үнэхээр их нөлөө үзүүлдэг. Маш их баярлалаа. Найздаа санал болгоод, та болон таны найз %(percentage)s%% бонус хурдан татан авалт аваарай! Хайртдаа гэнэтийн бэлэг барьж, тэдэнд гишүүнчлэлийн данс өг. Төгс Валентины бэлэг! Дэлгэрэнгүйг үзэх… Данс Үйл ажиллагаа Нарийвчилсан Аннагийн Блог ↗ Аннагийн Архив ↗ бета Кодын хайгуулч Өгөгдлийн багц Хандив өргөх Татаж авсан файлууд Түгээмэл асуултууд Нүүр хуудас Мета өгөгдлийг сайжруулах LLM өгөгдөл Нэвтрэх / Бүртгүүлэх Миний хандивууд Нийтийн профайл Хайх Аюулгүй байдал Торрентууд Орчуулах ↗ Сайн дурын ажил ба шагналууд Сүүлийн татан авалтууд: 📚&nbsp;Дэлхийн хамгийн том нээлттэй эх сурвалжтай, нээлттэй өгөгдөлтэй номын сан. ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library болон бусад толин тусгалууд. 📈&nbsp;%(book_any)s ном, %(journal_article)s өгүүлэл, %(book_comic)s комикс, %(magazine)s сэтгүүл — үүрд хадгалагдана.  болон  болон бусад DuXiu Internet Archive Зээлдүүлэх Номын Сан LibGen 📚&nbsp;Хүн төрөлхтний түүхэн дэх хамгийн том үнэхээр нээлттэй номын сан. 📈&nbsp;%(book_count)s&nbsp;ном, %(paper_count)s&nbsp; өгүүлэл — үүрд хадгалагдана. ⭐️&nbsp;Бид %(libraries)s толин тусгал хийдэг. Бид %(scraped)s хусаж, нээлттэй эх сурвалж болгодог. Манай бүх код болон өгөгдөл бүрэн нээлттэй эх сурвалжтай. OpenLib Sci-Hub ,  📚 Дэлхийн хамгийн том нээлттэй эх сурвалжтай, нээлттэй өгөгдөлтэй номын сан.<br>⭐️ Scihub, Libgen, Zlib болон бусад толин тусгалууд. Z-Lib Аннагийн Архив Буруу хүсэлт. %(websites)s руу зочлоно уу. Дэлхийн хамгийн том нээлттэй эх сурвалжтай, нээлттэй өгөгдлийн номын сан. Sci-Hub, Library Genesis, Z-Library болон бусад сайтуудын толь. Аннагийн Архивыг хайх Аннагийн Архив Дахин оролдоход хуудсыг шинэчилнэ үү. Хэрэв асуудал хэдэн цагийн турш үргэлжилбэл <a %(a_contact)s>бидэнтэй холбогдоорой</a>. 🔥 Энэ хуудсыг ачаалахад алдаа гарлаа <li>1. Биднийг <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, эсвэл <a href="https://t.me/annasarchiveorg">Telegram</a> дээр дагаарай.</li><li>2. Аннагийн Архивын талаар Twitter, Reddit, Tiktok, Instagram, орон нутгийн кафе эсвэл номын санд, хаана ч явсан түгээгээрэй! Бид хаалттай байдалд итгэдэггүй — хэрэв биднийг устгавал бидний бүх код, өгөгдөл бүрэн нээлттэй эх сурвалжтай тул өөр газар дахин гарч ирнэ.</li><li>3. Хэрэв боломжтой бол <a href="/donate">хандив өргөхийг</a> бодоорой.</li><li>4. Манай вэбсайтыг өөр хэл рүү <a href="https://translate.annas-software.org/">орчуулахад</a> туслаарай.</li><li>5. Хэрэв та програм хангамжийн инженер бол манай <a href="https://annas-software.org/">нээлттэй эх сурвалжид</a> хувь нэмэр оруулах эсвэл манай <a href="/datasets">торрентийг</a> үргэлжлүүлэн татаж авахад туслаарай.</li> 10. Өөрийн хэл дээр Anna’s Archive-ийн Wikipedia хуудсыг үүсгэх эсвэл хадгалахад туслаарай. 11. Бид жижиг, тохиромжтой зар сурталчилгаа байршуулахыг хүсэж байна. Хэрэв та Anna’s Archive дээр зар сурталчилгаа байршуулахыг хүсвэл бидэнд мэдэгдээрэй. 6. Хэрэв та аюулгүй байдлын судлаач бол бид таны ур чадварыг довтолгоо болон хамгаалалтад ашиглаж чадна. Манай <a %(a_security)s>Аюулгүй байдал</a> хуудсыг шалгана уу. 7. Бид нэргүй худалдаачдын төлбөрийн мэргэжилтнүүдийг хайж байна. Илүү тохиромжтой хандив өгөх аргуудыг нэмэхэд бидэнд тусалж чадах уу? PayPal, WeChat, бэлгийн картууд. Хэрэв та хэн нэгнийг мэддэг бол бидэнтэй холбоо барина уу. 8. Бид үргэлж илүү их серверийн хүчин чадал хайж байдаг. 9. Та энэ вэбсайт дээр файлтай холбоотой асуудлыг мэдээлэх, сэтгэгдэл үлдээх, жагсаалт үүсгэх замаар тусалж болно. Мөн та <a %(a_upload)s>илүү олон ном оруулах</a>, эсвэл одоо байгаа номын файлын асуудал, формат засах замаар тусалж болно. Сайн дурын ажил хийх талаар илүү дэлгэрэнгүй мэдээллийг манай <a %(a_volunteering)s>Сайн дурын ажил ба Шагналууд</a> хуудсаас үзнэ үү. Бид мэдээллийн чөлөөт урсгал, мэдлэг, соёлыг хадгалахыг тууштай дэмждэг. Энэ хайлтын системээр бид аваргуудын мөрөн дээр зогсож байна. Бид янз бүрийн сүүдрийн номын санг бүтээсэн хүмүүсийн хичээл зүтгэлийг гүнээ хүндэтгэж, энэ хайлтын систем тэдний хүрээг өргөжүүлнэ гэж найдаж байна. Манай ахиц дэвшлийн талаар мэдээлэл авахын тулд Аннаг <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> эсвэл <a href="https://t.me/annasarchiveorg">Telegram</a> дээр дагаарай. Асуулт, санал хүсэлтээ Аннад %(email)s хаягаар илгээнэ үү. Дансны ID: %(account_id)s Гарах ❌ Алдаа гарлаа. Хуудсыг дахин ачааллаж, дахин оролдоно уу. ✅ Та одоо гарлаа. Дахин нэвтрэхийн тулд хуудсыг дахин ачааллана уу. Хурдан таталт ашигласан (сүүлийн 24 цаг): <strong>%(used)s / %(total)s</strong> Гишүүнчлэл: <strong>%(tier_name)s</strong> хүртэл %(until_date)s <a %(a_extend)s>(сунгах)</a> Олон гишүүнчлэлийг нэгтгэж болно (24 цагт хурдан таталтын тоо нэмэгдэнэ). Гишүүнчлэл: <strong>Байхгүй</strong> <a %(a_become)s>(гишүүн болох)</a> Гишүүнчлэлийн түвшингээ өндөрсгөх сонирхолтой бол %(email)s хаягаар Аннатай холбогдоорой. Нийтийн профайл: %(profile_link)s Нууц түлхүүр (бусадтай бүү хуваалц!): %(secret_key)s харуулах Энд нэгдээрэй! Манай группт нэгдэхийн тулд <a %(a_tier)s>өндөр түвшинд</a> шинэчлээрэй. Онцгой Telegram групп: %(link)s Данс аль таталтууд вэ? Нэвтрэх Түлхүүрээ алдаж болохгүй! Нууц түлхүүр буруу байна. Түлхүүрээ шалгаад дахин оролдоно уу, эсвэл доор шинэ бүртгэл үүсгэнэ үү. Нууц түлхүүр Нэвтрэхийн тулд нууц түлхүүрээ оруулна уу: Хуучин имэйлд суурилсан бүртгэл үү? <a %(a_open)s>имэйлээ энд оруулна уу</a>. Шинэ бүртгэл үүсгэх Одоогоор бүртгэлгүй байна уу? Бүртгэл амжилттай! Таны нууц түлхүүр: <span %(span_key)s>%(key)s</span> Энэ түлхүүрийг анхааралтай хадгална уу. Хэрэв та үүнийг алдвал, дансандаа нэвтрэх боломжгүй болно. <li %(li_item)s><strong>Хавчуурга.</strong> Энэ хуудсыг хавчуурга болгон хадгалж, түлхүүрээ сэргээж болно.</li><li %(li_item)s><strong>Татах.</strong> Түлхүүрээ татахын тулд <a %(a_download)s>энэ холбоос дээр</a> дарна уу.</li><li %(li_item)s><strong>Нууц үгийн менежер.</strong> Доор түлхүүрээ оруулахдаа нууц үгийн менежер ашиглан хадгална уу.</li> Нэвтрэх / Бүртгүүлэх Хөтөчийн баталгаажуулалт Анхааруулга: код нь буруу Unicode тэмдэгтүүдтэй бөгөөд янз бүрийн нөхцөлд буруу ажиллах магадлалтай. Түүхий бинар нь URL дахь base64 төлөөллөөс декодлогдож болно. Тайлбар Шошго Угтвар үг Тодорхой кодын URL Вэбсайт “%(prefix_label)s” эхэлсэн кодууд Эдгээр хуудсыг хусахгүй байхыг хүсч байна. Үүний оронд бидний <a %(a_import)s>өгөгдлийг үүсгэх</a> эсвэл <a %(a_download)s>татаж авах</a> ElasticSearch болон MariaDB мэдээллийн санг ашиглахыг зөвлөж байна, мөн бидний <a %(a_software)s>нээлттэй эхийн кодыг</a> ажиллуулна уу. Түүхий өгөгдлийг <a %(a_json_file)s>энэ файл</a> зэрэг JSON файлуудаар гараар судалж болно. %(count)s бичлэгээс бага Ерөнхий URL Кодуудын судлаач Индекс Бичлэгүүдийг тэмдэглэсэн кодуудыг угтвар үгээр нь судлаарай. “Бичлэгүүд” багана нь хайлтын системд (мета өгөгдөлтэй бичлэгүүдийг оруулаад) өгөгдсөн угтвар үгтэй кодуудаар тэмдэглэгдсэн бичлэгүүдийн тоог харуулна. “Кодууд” багана нь өгөгдсөн угтвар үгтэй бодит кодуудын тоог харуулна. Мэдэгдэж буй кодын угтвар “%(key)s” Цааш нь… Угтвар үг %(count)s бичлэг “%(prefix_label)s” тохирч байна %(count)s бичлэгүүд “%(prefix_label)s” тохирч байна кодууд бичлэгүүд “%%s” нь кодын утгаар солигдоно Аннагийн Архив хайх Кодууд Тодорхой кодын URL: “%(url)s” Энэ хуудсыг үүсгэхэд хэсэг хугацаа шаардагдах тул Cloudflare captcha шаардлагатай. <a %(a_donate)s>Гишүүд</a> captcha-г алгасаж болно. Доромжлол мэдээлэгдсэн: Сайжруулсан хувилбар Энэ хэрэглэгчийг доромжлол эсвэл зохисгүй үйлдэл хийсэн гэж мэдээлэх үү? Файлын асуудал: %(file_issue)s далд сэтгэгдэл Хариулах Доромжлолыг мэдээлэх Та энэ хэрэглэгчийг доромжлолын талаар мэдээлсэн. Энэ имэйлээр ирүүлсэн зохиогчийн эрхийн нэхэмжлэлийг үл тоомсорлох болно; оронд нь маягтыг ашиглана уу. Имэйл харуулах Бид таны санал хүсэлт, асуултыг маш ихээр хүлээн авдаг! Гэсэн хэдий ч, бидэнд ирдэг спам болон утгагүй имэйлүүдийн тооноос шалтгаалан, бидэнтэй холбоо барих нөхцөлийг ойлгож байгаагаа баталгаажуулахын тулд хайрцгуудыг шалгана уу. Зохиогчийн эрхийн нэхэмжлэлийн талаар бидэнтэй холбоо барих бусад аргуудыг автоматаар устгах болно. DMCA / зохиогчийн эрхийн нэхэмжлэлийн хувьд <a %(a_copyright)s>энэ маягтыг</a> ашиглана уу. Холбоо барих имэйл Аннагийн Архив дээрх URL-ууд (шаардлагатай). Нэг мөрөнд нэг URL. Номын яг ижил хэвлэлийн тухай тайлбарласан URL-уудыг л оруулна уу. Хэрэв та олон ном эсвэл олон хэвлэлийн хувьд нэхэмжлэл гаргахыг хүсвэл энэ маягтыг олон удаа илгээнэ үү. Ном, хэвлэлүүдийг багцалсан нэхэмжлэлүүдийг хүлээн авахгүй. Хаяг (шаардлагатай) Эх материалын тодорхой тайлбар (шаардлагатай) И-мэйл (шаардлагатай) Эх материалын URL-ууд, нэг мөрөнд нэг (шаардлагатай). Таны нэхэмжлэлийг баталгаажуулахад туслахын тулд аль болох олон URL-уудыг оруулна уу (жишээ нь: Amazon, WorldCat, Google Books, DOI). Эх материалын ISBN-ууд (хэрэв боломжтой бол). Нэг мөрөнд нэг. Зөвхөн таны зохиогчийн эрхийн нэхэмжлэлд хамаарах хэвлэлтэй яг таарч байгаа ISBN-уудыг оруулна уу. Таны нэр (шаардлагатай) ❌ Алдаа гарлаа. Хуудсыг дахин ачаалж, дахин оролдоно уу. ✅ Зохиогчийн эрхийн нэхэмжлэлээ илгээсэнд баярлалаа. Бид үүнийг аль болох хурдан шалгах болно. Өөр нэгийг илгээхийн тулд хуудсыг дахин ачаална уу. Эх материалын <a %(a_openlib)s>Open Library</a> URL-ууд, нэг мөрөнд нэг. Эх материалыг Open Library-ээс хайж олоход цаг гаргана уу. Энэ нь таны нэхэмжлэлийг баталгаажуулахад тусална. Утасны дугаар (шаардлагатай) Мэдэгдэл ба гарын үсэг (шаардлагатай) Нэхэмжлэл илгээх Хэрэв танд DCMA эсвэл бусад зохиогчийн эрхийн нэхэмжлэл байгаа бол энэ маягтыг аль болох нарийвчлан бөглөнө үү. Хэрэв та ямар нэгэн асуудалтай тулгарвал манай тусгай DMCA хаягаар бидэнтэй холбоо барина уу: %(email)s. Энэ хаягаар илгээсэн нэхэмжлэлүүдийг боловсруулахаас татгалзах бөгөөд зөвхөн асуултанд хариулахад зориулагдсан болно. Нэхэмжлэлээ доорх маягтаар илгээнэ үү. DMCA / Зохиогчийн эрхийн нэхэмжлэлийн маягт Аннагийн Архив дээрх жишээ бичлэг Аннагийн Архивын торрентууд Аннагийн Архив Контейнеруудын формат Мета өгөгдлийг импортлох скриптүүд Хэрэв та энэ өгөгдлийн багцыг <a %(a_archival)s>архивлах</a> эсвэл <a %(a_llm)s>LLM сургалт</a> зорилгоор толин тусгал хийх сонирхолтой байгаа бол бидэнтэй холбоо барина уу. Сүүлд шинэчилсэн: %(date)s Үндсэн %(source)s вэбсайт Мета өгөгдлийн баримт бичиг (ихэнх талбарууд) Аннагийн Архиваар толин тусгалдсан файлууд: %(count)s (%(percent)s%%) Нөөцүүд Нийт файлууд: %(count)s Нийт файлын хэмжээ: %(size)s Энэ өгөгдлийн талаар манай блогийн бичлэг <a %(duxiu_link)s>Duxiu</a> бол <a %(superstar_link)s>SuperStar Digital Library Group</a>-аас бүтээсэн асар том сканнердсан номын мэдээллийн сан юм. Ихэнх нь их дээд сургууль, номын сангуудад цахим хэлбэрээр ашиглах боломжтой болгохын тулд сканнердсан академик ном юм. Манай англи хэлтэй үзэгчдэд зориулж <a %(princeton_link)s>Принстон</a> болон <a %(uw_link)s>Вашингтоны их сургууль</a> сайн тоймтой. Мөн илүү дэлгэрэнгүй мэдээлэл өгөх маш сайн нийтлэл бий: <a %(article_link)s>“Хятадын номыг дижиталжуулах: SuperStar DuXiu Scholar хайлтын системийн кейс судалгаа”</a>. Duxiu-ийн номнууд Хятадын интернетэд удаан хугацаанд хууль бусаар тархсан. Ихэвчлэн тэдгээрийг борлуулагчид нэг доллараас бага үнээр зардаг. Эдгээрийг ихэвчлэн Google Drive-ийн хятад хувилбараар тараадаг бөгөөд ихэвчлэн илүү их хадгалах зайтай болгохын тулд хакердсан байдаг. Зарим техникийн дэлгэрэнгүй мэдээллийг <a %(link1)s>энд</a> болон <a %(link2)s>энд</a> олж болно. Номнууд хагас нийтийн байдлаар тараагдсан ч, тэдгээрийг бөөнөөр нь авахад нэлээд хэцүү байдаг. Бид үүнийг хийх ажлын жагсаалтынхаа дээд хэсэгт тавьж, бүтэн цагаар ажиллах хэдэн сар зарцуулсан. Гэсэн хэдий ч 2023 оны сүүлээр гайхалтай, гайхамшигтай, авьяаслаг сайн дурын ажилтан бидэнтэй холбогдож, энэ ажлыг аль хэдийн хийсэн гэж хэлсэн — ихээхэн зардал гаргасан. Тэд бидэнд бүх цуглуулгыг урт хугацааны хадгалалтыг баталгаажуулахыг хүссэнээс бусад ямар ч хариу нэхэлгүйгээр хуваалцсан. Үнэхээр гайхалтай. Манай сайн дурынхны илүү дэлгэрэнгүй мэдээлэл (түүхийн тэмдэглэл): Манай <a %(a_href)s>блогийн бичлэгээс</a> дасан зохицсон. DuXiu 读秀 %(count)s файл %(count)s файлууд Энэ өгөгдлийн багц нь <a %(a_datasets_openlib)s>Open Library өгөгдлийн багц</a>-тай нягт холбоотой. Энэ нь IA-ийн Хяналттай Дижитал Зээлдүүлэх Номын сангаас бүх мета өгөгдөл болон файлуудын ихэнх хэсгийг хуссан өгөгдлийг агуулдаг. Шинэчлэлтүүд нь <a %(a_aac)s>Аннагийн Архив Контейнеруудын формат</a>-аар гардаг. Эдгээр бичлэгүүд нь Open Library өгөгдлийн багцаас шууд иш татагдаж байгаа боловч Open Library-д байхгүй бичлэгүүдийг ч агуулдаг. Мөн олон жилийн турш олон нийтийн гишүүдийн хуссан хэд хэдэн өгөгдлийн файлуудыг бидэнд байгаа. Энэ цуглуулга нь хоёр хэсгээс бүрдэнэ. Бүх өгөгдлийг авахын тулд та хоёр хэсгийг хоёуланг нь хэрэгтэй (торрентуудын хуудсан дээр дарсан хуучирсан торрентуудыг эс тооцвол). Дижитал Зээлдүүлэх Номын сан Манай анхны хувилбар, бид <a %(a_aac)s>Аннагийн Архив Контейнерууд (AAC) формат</a>-ыг стандартчилхаас өмнө. Мета өгөгдөл (json болон xml хэлбэрээр), pdf-үүд (acsm болон lcpdf дижитал зээлдүүлэх системүүдээс), болон хавтасны жижиг зургуудыг агуулдаг. AAC ашиглан шинэ хувилбаруудыг нэмэгдүүлэн гаргаж байна. 2023-01-01-ээс хойшхи цагийн тэмдэгтэй мета өгөгдлийг л агуулдаг, учир нь үлдсэн хэсгийг “ia” аль хэдийн хамарсан. Мөн бүх pdf файлууд, энэ удаад acsm болон “bookreader” (IA-ийн вэб уншигч) зээлдүүлэх системүүдээс. Нэр нь яг зөв биш ч гэсэн, бид bookreader файлуудыг ia2_acsmpdf_files цуглуулгад оруулдаг, учир нь тэд харилцан хамааралгүй. IA Хянагдсан Дижитал Зээлдүүлэлт 98%%+ файлууд хайлтанд орно. Бидний эрхэм зорилго бол дэлхийн бүх ном (мөн өгүүлэл, сэтгүүл гэх мэт)-ыг архивлаж, өргөн хүрээнд хүртээмжтэй болгох явдал юм. Бид бүх номыг өргөн хүрээнд толин тусгал хийх ёстой гэж үздэг бөгөөд ингэснээр давхардал, уян хатан байдлыг хангах болно. Энэ шалтгааны улмаас бид янз бүрийн эх сурвалжаас файлуудыг цуглуулж байна. Зарим эх сурвалжууд бүрэн нээлттэй бөгөөд бөөнөөр нь толин тусгал хийх боломжтой (жишээ нь Sci-Hub). Бусад нь хаалттай, хамгаалалттай тул бид тэдгээрийн номыг “чөлөөлөх” зорилгоор хусахыг хичээдэг. Бусад нь дундын байдалд ордог. Манай бүх өгөгдлийг <a %(a_torrents)s>торрент</a> хийж болох бөгөөд манай бүх метадата-г <a %(a_anna_software)s>үүсгэж</a> эсвэл <a %(a_elasticsearch)s>татаж авах</a> боломжтой ElasticSearch болон MariaDB мэдээллийн сангууд болгон ашиглаж болно. Түүхий өгөгдлийг <a %(a_dbrecord)s>энэ</a> мэт JSON файлуудаар гар аргаар судалж болно. Мета өгөгдөл ISBN вэбсайт Сүүлд шинэчилсэн: %(isbn_country_date)s (%(link)s) Нөөцүүд Олон улсын ISBN агентлаг нь үндэсний ISBN агентлагуудад хуваарилсан хүрээг тогтмол гаргадаг. Үүнээс бид энэ ISBN аль улс, бүс нутаг, эсвэл хэлний бүлэгт хамаарахыг тодорхойлж чадна. Одоогоор бид энэ өгөгдлийг шууд бус байдлаар, <a %(a_isbnlib)s>isbnlib</a> Python сангаар дамжуулан ашиглаж байна. ISBN улсын мэдээлэл Энэ бол 2022 оны 9-р сард isbndb.com руу олон дуудлага хийсэн dump юм. Бид бүх ISBN хүрээг хамрахыг хичээсэн. Эдгээр нь ойролцоогоор 30.9 сая бичлэг юм. Тэдний вэбсайт дээр тэд үнэндээ 32.6 сая бичлэгтэй гэж мэдэгдэж байгаа тул бид яагаад ч юм заримыг нь алгассан байж магадгүй, эсвэл <em>тэд</em> ямар нэгэн зүйл буруу хийж байж магадгүй. JSON хариултууд нь тэдний серверээс бараг түүхий хэлбэрээр ирдэг. Бидний анзаарсан нэг өгөгдлийн чанарын асуудал бол “978-” гэсэн угтваргүйгээр эхэлсэн ISBN-13 дугааруудад тэд “isbn” талбарыг оруулсан байдаг бөгөөд энэ нь зүгээр л эхний 3 тоог хассан (мөн шалгах цифрийг дахин тооцоолсон) ISBN-13 дугаар юм. Энэ нь мэдээж буруу боловч тэд ингэж хийдэг бололтой, тиймээс бид үүнийг өөрчлөөгүй. Таны тулгарч болох өөр нэг боломжит асуудал бол “isbn13” талбар нь давхардсан байдаг тул та үүнийг өгөгдлийн санд анхдагч түлхүүр болгон ашиглаж чадахгүй. “isbn13”+“isbn” талбаруудыг хослуулсан нь өвөрмөц байдаг. Гаргалт 1 (2022-10-31) Уран зохиолын torrents хойшлогдсон (гэхдээ ID ~4-6M нь манай Zlib torrents-тэй давхцаж байгаа тул torrents-ээр ороогүй). Манай комикс номын гаргалтын тухай блог нийтлэл Anna’s Archive дээрх комиксийн торрентууд Өөр өөр Library Genesis салаануудын түүхийг <a %(a_libgen_rs)s>Libgen.rs</a> хуудсанд үзнэ үү. Libgen.li нь ихэнхдээ Libgen.rs-ийн агуулга болон мета өгөгдлийг агуулдаг боловч үүн дээр комикс, сэтгүүл, стандарт баримт бичгүүдийн цуглуулгууд нэмэгдсэн байдаг. Мөн <a %(a_scihub)s>Sci-Hub</a>-ийг мета өгөгдөл болон хайлтын системдээ нэгтгэсэн бөгөөд бид үүнийг өөрсдийн өгөгдлийн санд ашиглаж байна. Энэ номын сангийн мета өгөгдөл нь <a %(a_libgen_li)s>libgen.li</a> дээр үнэгүй ашиглах боломжтой. Гэсэн хэдий ч энэ сервер удаан бөгөөд тасарсан холболтыг сэргээхийг дэмждэггүй. Ижил файлуудыг <a %(a_ftp)s>FTP сервер</a> дээрээс авах боломжтой бөгөөд энэ нь илүү сайн ажилладаг. Баримтат уран зохиол мөн өөрчлөгдсөн бололтой, гэхдээ шинэ torrent үүсээгүй байна. Энэ нь 2022 оны эхэн үеэс хойш болсон бололтой, гэхдээ бид үүнийг баталгаажуулаагүй байна. Libgen.li администраторын хэлснээр, “fiction_rus” (Оросын уран зохиол) цуглуулга нь <a %(a_booktracker)s>booktracker.org</a>-оос тогтмол гаргасан торрентоор хамрагдах ёстой бөгөөд ялангуяа <a %(a_flibusta)s>flibusta</a> болон <a %(a_librusec)s>lib.rus.ec</a> торрентууд (бид <a %(a_torrents)s>энд</a> тольдсон боловч аль торрентууд аль файлд тохирохыг хараахан тогтоогоогүй байна). Уран зохиолын цуглуулга нь өөрийн гэсэн торрентуудтай ( <a %(a_href)s>Libgen.rs</a>-ээс салсан) бөгөөд %(start)s эхэлдэг. Торрентгүй зарим хүрээ (жишээ нь уран зохиолын хүрээ f_3463000-аас f_4260000 хүртэл) нь магадгүй Z-Номын сан (эсвэл бусад давхардсан) файлууд байж болох бөгөөд бид эдгээр хүрээнд lgli-өвөрмөц файлуудын хувьд зарим давхардлыг арилгаж, торрент хийхийг хүсэж магадгүй юм. Бүх цуглуулгын статистикийг <a %(a_href)s>libgen-ийн вэбсайтаас</a> олж болно. Ихэнх нэмэлт контентын хувьд торрентууд байдаг бөгөөд ялангуяа комик, сэтгүүл, стандарт баримт бичгийн торрентууд Аннагийн Архивтай хамтран гаргасан. “libgen.is” гэсэн торрент файлууд нь <a %(a_libgen)s>Libgen.rs</a>-ийн толь гэдгийг анхаарна уу (“.is” нь Libgen.rs-ийн ашигладаг өөр домэйн юм). Мета өгөгдлийг ашиглахад туслах нөөц бол <a %(a_href)s>энэ хуудас</a> юм. %(icon)s Тэдний “fiction_rus” цуглуулга (Оросын уран зохиол) тусгай зориулалтын торрентгүй боловч бусад хүмүүсийн торрентоор хамрагдсан бөгөөд бид <a %(fiction_rus)s>толь</a> хадгалдаг. Аннагийн Архив дахь Оросын уран зохиолын торрентууд Anna’s Archive дээрх уран зохиолын торрентууд Хэлэлцүүлгийн форум Мета өгөгдөл FTP-ээр дамжуулан мета өгөгдөл Anna’s Archive дээрх сэтгүүлийн торрентууд Мета өгөгдлийн талбарын мэдээлэл Бусад торрентуудын толь (мөн өвөрмөц уран зохиол болон комиксийн торрентууд) Аннагийн Архив дахь стандарт баримт бичгийн торрентууд Libgen.li Аннагийн Архивын торрентийг (номын хавтас) Library Genesis нь өөрсдийн өгөгдлийг бөөнөөр нь торрентоор өгөхдөө аль хэдийнээ өгөөмөр гэдгээрээ алдартай. Манай Libgen цуглуулга нь тэдний шууд гаргадаггүй нэмэлт өгөгдлөөс бүрддэг бөгөөд тэдэнтэй хамтран ажилладаг. Library Genesis-тэй хамтран ажиллаж буй бүх хүмүүст маш их баярлалаа! Номын хавтасны гаргалтын тухай манай блог Энэ хуудас нь “.rs” хувилбарын тухай юм. Энэ нь мета өгөгдөл болон номын каталогийн бүрэн агуулгыг тогтмол нийтэлдэг гэдгээрээ алдартай. Түүний номын цуглуулга нь уран зохиол болон уран зохиол бус хэсэгт хуваагддаг. Мета өгөгдлийг ашиглахад туслах нөөц бол <a %(a_metadata)s>энэ хуудас</a> (IP хаягийн хүрээг хаадаг, VPN шаардлагатай байж магадгүй). 2024-03 оны байдлаар шинэ торрентийг <a %(a_href)s>энэ форумын сэдэвт</a> нийтэлж байна (IP хаягийн хүрээг хаадаг, VPN шаардлагатай байж магадгүй). Аннагийн Архив дахь уран зохиолын торрентийг Libgen.rs Уран зохиолын торрентийг Libgen.rs Хэлэлцүүлгийн форум Libgen.rs Метадата Libgen.rs метадата талбарын мэдээлэл Libgen.rs Баримтат номын торрентийг Аннагийн Архив дахь баримтат номын торрентийг %(example)s уран зохиолын номын хувьд. Энэ <a %(blog_post)s>анхны гаргалт</a> нь нэлээд жижиг: Libgen.rs салаанаас авсан ойролцоогоор 300GB номын хавтас, уран зохиол болон баримтат номын аль аль нь. Тэдгээрийг libgen.rs дээр хэрхэн харагддагтай адил зохион байгуулсан, жишээ нь: %(example)s баримтат номын хувьд. Z-Library цуглуулгын адил, бид тэдгээрийг бүгдийг нь том .tar файлд хийсэн бөгөөд хэрэв та файлуудыг шууд үйлчлэхийг хүсвэл <a %(a_ratarmount)s>ratarmount</a> ашиглан холбож болно. Гаргалт 1 (%(date)s) Library Genesis (эсвэл “Libgen”) салбаруудын түүх нь цаг хугацааны явцад Library Genesis-т оролцсон хүмүүсийн хооронд зөрчилдөөн гарч, тус тусдаа замналаа үргэлжлүүлсэн явдал юм. Энэ <a %(a_mhut)s>форумын бичлэг</a>-ийн дагуу, Libgen.li анх “http://free-books.dontexist.com” дээр байрлаж байсан. “.fun” хувилбарыг анхны үүсгэн байгуулагч бүтээсэн. Энэ нь шинэ, илүү тархсан хувилбарыг дэмжихийн тулд шинэчлэгдэж байна. <a %(a_li)s>“.li” хувилбар</a> нь асар их хэмжээний комиксийн цуглуулгатай бөгөөд бусад контентуудыг агуулдаг, одоогоор бөөнөөр татаж авах боломжгүй. Энэ нь уран зохиолын номын тусдаа торрент цуглуулгатай бөгөөд <a %(a_scihub)s>Sci-Hub</a>-ийн мета өгөгдлийг өөрийн мэдээллийн санд агуулдаг. “.rs” хувилбар нь маш төстэй өгөгдөлтэй бөгөөд ихэвчлэн цуглуулгаа бөөнөөр нь торрентуудаар гаргадаг. Энэ нь ерөнхийдөө “уран зохиол” болон “уран зохиол бус” хэсэгт хуваагддаг. Анх “http://gen.lib.rus.ec” дээр байсан. <a %(a_zlib)s>Z-Library</a> нь зарим утгаараа Library Genesis-ийн салбар гэж үзэж болох ч тэд өөрийн төслийн нэрийг өөрчилсөн. Libgen.rs Мөн бид зөвхөн мета өгөгдлийн эх сурвалжуудаар цуглуулгаа баяжуулдаг бөгөөд эдгээрийг файлуудтай тааруулж болно, жишээ нь ISBN дугаар эсвэл бусад талбаруудыг ашиглан. Доор эдгээрийн тойм байна. Дахин хэлэхэд, эдгээр эх сурвалжуудын зарим нь бүрэн нээлттэй байдаг бол заримыг нь бид хусах шаардлагатай болдог. Мета өгөгдлийн хайлтад бид анхны бичлэгүүдийг харуулдаг гэдгийг анхаарна уу. Бид бичлэгүүдийг нэгтгэдэггүй. Зөвхөн мета өгөгдлийн эх сурвалжууд Open Library нь дэлхийн бүх номыг каталогжуулах зорилготой Internet Archive-ийн нээлттэй эхийн төсөл юм. Энэ нь дэлхийн хамгийн том ном сканнердах үйл ажиллагааны нэг бөгөөд олон номыг дижитал зээлдүүлэх боломжтой. Түүний номын метадата каталог нь үнэгүй татаж авах боломжтой бөгөөд Аннагийн Архив дээр багтсан (гэхдээ одоогоор хайлтад ороогүй, зөвхөн Open Library ID-г тодорхой хайх үед л орно). Open Library Давхардлыг хассан Сүүлд шинэчлэгдсэн Файлын тооны хувиар %% AA / torrents-ээр толилуулсан Хэмжээ Эх сурвалж Доор Аннагийн Архив дахь файлуудын эх сурвалжуудын хурдан тоймыг харуулав. Сүүдрийн номын сангууд ихэвчлэн бие биенээсээ өгөгдөл синк хийдэг тул номын сангуудын хооронд ихээхэн давхцал байдаг. Тиймээс тоонууд нийтэд нийцэхгүй байна. “Anna’s Archive-аар толин тусгалд орж, үржүүлсэн” хувь нь бид өөрсдөө толин тусгалд оруулсан файлуудын тоог харуулдаг. Бид эдгээр файлуудыг торрентоор бөөнөөр нь үржүүлж, түнш вэбсайтуудаар шууд татаж авах боломжтой болгодог. Тойм Нийт Аннагийн Архив дахь torrents Sci-Hub-ийн талаарх дэлгэрэнгүй мэдээллийг түүний <a %(a_scihub)s>албан ёсны вэбсайт</a>, <a %(a_wikipedia)s>Википедиа хуудас</a>, болон энэ <a %(a_radiolab)s>подкаст ярилцлага</a>-аас үзнэ үү. Sci-Hub нь <a %(a_reddit)s>2021 оноос хойш хөлдсөн</a> гэдгийг анхаарна уу. Өмнө нь хөлдсөн байсан ч 2021 онд хэдэн сая өгүүлэл нэмэгдсэн. Гэсэн хэдий ч, Libgen-ийн “scimag” цуглуулгад хязгаарлагдмал тооны өгүүлэл нэмэгдэж байгаа боловч шинэ бөөн torrents үүсгэхэд хангалтгүй байна. Бид Sci-Hub-ийн метадата-г <a %(a_libgen_li)s>Libgen.li</a>-ийн “scimag” цуглуулгаас авдаг. Мөн бид <a %(a_dois)s>dois-2022-02-12.7z</a> өгөгдлийн багцыг ашигладаг. “smarch” torrents нь <a %(a_smarch)s>хуучирсан</a> тул манай torrents жагсаалтад ороогүй болно. Libgen.li дахь torrents Libgen.rs дахь torrents Метадата болон torrents Reddit дээрх шинэчлэлтүүд Подкаст ярилцлага Википедиа хуудас Sci-Hub Sci-Hub: 2021 оноос хойш хөлдсөн; ихэнх нь torrents-ээр боломжтой Libgen.li: түүнээс хойш бага зэрэг нэмэгдсэн</div> Зарим эх сурвалжийн номын сангууд өөрсдийн өгөгдлийг торрентоор их хэмжээгээр хуваалцахыг дэмждэг бол зарим нь цуглуулгаа амархан хуваалцдаггүй. Сүүлийн тохиолдолд, Аннагийн Архив тэдний цуглуулгыг хусаж, тэдгээрийг ашиглах боломжтой болгодог (манай <a %(a_torrents)s>Торрентууд</a> хуудсыг үзнэ үү). Мөн зарим эх сурвалжийн номын сангууд хуваалцах хүсэлтэй байдаг ч нөөц бололцоо нь хүрэлцдэггүй тохиолдлууд байдаг. Ийм тохиолдолд бид мөн туслахыг хичээдэг. Доор бидний янз бүрийн эх сурвалжийн номын сантай хэрхэн харилцдаг талаар тойм байна. Эх сурвалжийн номын сангууд %(icon)s Хятадын интернетэд тархсан янз бүрийн файлын мэдээллийн сангууд; ихэвчлэн төлбөртэй мэдээллийн сангууд %(icon)s Ихэнх файлууд зөвхөн BaiduYun-ийн премиум дансаар нэвтрэх боломжтой; татах хурд удаан. %(icon)s Аннагийн Архив нь <a %(duxiu)s>DuXiu файлууд</a>-ын цуглуулгыг удирддаг %(icon)s Хятадын интернетэд тархсан янз бүрийн метадата өгөгдлийн сангууд; ихэвчлэн төлбөртэй өгөгдлийн сангууд %(icon)s Тэдний бүх цуглуулгад зориулсан хялбархан хандах боломжтой метадата хаягдал байхгүй. %(icon)s Аннагийн Архив нь <a %(duxiu)s>DuXiu мета өгөгдөл</a>-ийн цуглуулгыг удирддаг Файлууд %(icon)s Файлуудыг зээлэх боломж хязгаарлагдмал, янз бүрийн хандалтын хязгаарлалттай %(icon)s Аннагийн Архив нь <a %(ia)s>IA файлуудын</a> цуглуулгыг удирддаг %(icon)s Зарим метадата <a %(openlib)s>Open Library өгөгдлийн сангийн хаягдлаар</a> дамжуулан авах боломжтой, гэхдээ тэдгээр нь бүх IA цуглуулгыг хамардаггүй %(icon)s Тэдний бүх цуглуулгад зориулсан хялбархан хандах боломжтой метадата хаягдал байхгүй %(icon)s Аннагийн Архив нь <a %(ia)s>IA метадата</a> цуглуулгыг удирддаг Сүүлд шинэчилсэн %(icon)s Аннагийн Архив болон Libgen.li хамтран <a %(comics)s>комик ном</a>, <a %(magazines)s>сэтгүүл</a>, <a %(standarts)s>стандарт баримт бичиг</a>, болон <a %(fiction)s>уран зохиол (Libgen.rs-ээс салсан)</a> цуглуулгуудыг удирддаг. %(icon)s Баримтат торрентийг Libgen.rs-тэй хуваалцдаг (мөн <a %(libgenli)s>энд</a> толилуулсан). %(icon)s Улирал бүрийн <a %(dbdumps)s>HTTP өгөгдлийн сангийн хаягдал</a> %(icon)s <a %(nonfiction)s>Баримтат</a> болон <a %(fiction)s>Уран зохиолын</a> автоматжуулсан торрентууд %(icon)s Аннагийн Архив нь <a %(covers)s>номын хавтасны торрентийн</a> цуглуулгыг удирддаг %(icon)s Өдөр бүр <a %(dbdumps)s>HTTP өгөгдлийн сангийн хаягдал</a> Мета өгөгдөл %(icon)s Сар бүрийн <a %(dbdumps)s>өгөгдлийн дампууд</a> %(icon)s Өгөгдлийн торрентийг <a %(scihub1)s>энд</a>, <a %(scihub2)s>энд</a>, болон <a %(libgenli)s>энд</a> авах боломжтой %(icon)s Зарим шинэ файлууд <a %(libgenrs)s>Libgen-ийн</a> <a %(libgenli)s>“scimag”</a> руу нэмэгдэж байгаа боловч шинэ торрентийг үүсгэхэд хангалттай биш байна %(icon)s Sci-Hub 2021 оноос хойш шинэ файлуудыг зогсоосон. %(icon)s Метадата хаягдлуудыг <a %(scihub1)s>энд</a> болон <a %(scihub2)s>энд</a> авах боломжтой, мөн <a %(libgenli)s>Libgen.li өгөгдлийн сангийн</a> нэг хэсэг (бид ашигладаг) Эх сурвалж %(icon)s Янз бүрийн жижиг эсвэл нэг удаагийн эх үүсвэрүүд. Бид хүмүүсийг эхлээд бусад сүүдрийн номын сангууд руу байршуулахыг уриалдаг, гэхдээ заримдаа хүмүүсийн цуглуулга нь бусад хүмүүсийн ангилахад хэтэрхий том, гэхдээ өөрийн гэсэн ангилалд оруулахад хангалттай том биш байдаг. %(icon)s Шууд олноор нь авах боломжгүй, хусалтаас хамгаалагдсан %(icon)s Аннагийн Архив нь <a %(worldcat)s>OCLC (WorldCat) мета өгөгдөл</a>-ийн цуглуулгыг удирддаг %(icon)s Аннагийн Архив болон Z-Library хамтран <a %(metadata)s>Z-Library метадата</a> болон <a %(files)s>Z-Library файлуудын</a> цуглуулгыг удирддаг Datasets Бид дээрх бүх эх сурвалжуудыг нэг нэгдсэн мэдээллийн санд нэгтгэдэг бөгөөд энэ вэбсайтыг үйлчлэхэд ашигладаг. Энэ нэгдсэн мэдээллийн сан шууд ашиглах боломжгүй боловч Аннагийн Архив бүрэн нээлттэй эх сурвалж тул үүнийг <a %(a_generated)s>үүсгэх</a> эсвэл <a %(a_downloaded)s>татаж авах</a> боломжтой ElasticSearch болон MariaDB мэдээллийн сангууд болгон хялбархан үүсгэж болно. Тэр хуудсан дээрх скриптүүд нь дээр дурдсан эх сурвалжуудаас шаардлагатай бүх мета өгөгдлийг автоматаар татаж авна. Хэрэв та эдгээр скриптүүдийг орон нутагтаа ажиллуулахаас өмнө манай өгөгдлийг судлахыг хүсвэл манай JSON файлуудыг үзэж болно, эдгээр нь цаашид бусад JSON файлуудтай холбогддог. <a %(a_json)s>Энэ файл</a> нь сайн эхлэл юм. Нэгдсэн мэдээллийн сан Аннагийн Архивын торрентууд хайх эрэх Янз бүрийн жижиг эсвэл нэг удаагийн эх үүсвэрүүд. Бид хүмүүсийг эхлээд бусад сүүдрийн номын сангууд руу байршуулахыг уриалдаг, гэхдээ заримдаа хүмүүсийн цуглуулга нь бусад хүмүүсийн ангилахад хэтэрхий том, гэхдээ өөрийн гэсэн ангилалд оруулахад хангалттай том биш байдаг. <a %(a1)s>Datasets хуудасны</a> тойм. <a %(a_href)s>aaaaarg.fail</a>-аас. Нэлээд бүрэн гүйцэд харагдаж байна. Манай сайн дурын “cgiym”-аас. <a %(a_href)s><q>ACM Digital Library 2020</q></a> торрентээс. Одоогийн цуглуулгатай нэлээд давхцаж байгаа боловч MD5 тохирох зүйл маш цөөн тул бид үүнийг бүрэн хадгалахаар шийдсэн. <iRead eBooks</q> (дуудлагаар <q>ai rit i-books</q>; airitibooks.com) сайтын хусалт, сайн дурын <q>j</q> гишүүний хийсэн. <a %(a1)s><q>Бусад metadata хусалт</q></a> дахь <q>airitibooks</q> metadata-тай тохирно. <a %(a1)s><q>Александрины номын сан</q></a> цуглуулгаас. Хэсэгчлэн эх сурвалжаас, хэсэгчлэн the-eye.eu сайтаас, хэсэгчлэн бусад толь бичгээс. Хувийн номын торрент вэбсайтаас, <a %(a_href)s>Bibliotik</a> (ихэвчлэн “Bib” гэж нэрлэдэг), номнуудыг нэрээр нь (A.torrent, B.torrent) багцалж, the-eye.eu-ээр дамжуулан тараасан. Манай сайн дурын “bpb9v”-аас. <a %(a_href)s>CADAL</a>-ийн талаар дэлгэрэнгүй мэдээллийг манай <a %(a_duxiu)s>DuXiu өгөгдлийн багцын хуудас</a>-наас үзнэ үү. Манай сайн дурын “bpb9v”-аас илүү их, ихэвчлэн DuXiu файлууд, мөн “WenQu” болон “SuperStar_Journals” хавтаснууд (SuperStar нь DuXiu-ийн ард байгаа компани). Манай сайн дурын “cgiym”-аас, янз бүрийн эх сурвалжаас авсан Хятадын текстүүд (дэд хавтаснууд байдлаар), үүнд <a %(a_href)s>China Machine Press</a> (Хятадын томоохон хэвлэлийн газар) орно. Манай сайн дурын “cgiym”-аас, Хятадын бус цуглуулгууд (дэд хавтаснууд байдлаар). Хятадын архитектурын тухай номнуудын хусалт, сайн дурын <q>cm</q> гишүүний хийсэн: <q>Би хэвлэлийн газрын сүлжээний эмзэг байдлыг ашиглан үүнийг авсан, гэхдээ тэр цоорхойг одоо хаасан</q>. <a %(a1)s><q>Бусад metadata хусалт</q></a> дахь <q>chinese_architecture</q> metadata-тай тохирно. Академик хэвлэлийн газар <a %(a_href)s>De Gruyter</a>-ийн номнууд, хэд хэдэн томоохон торрентуудаас цуглуулсан. <a %(a_href)s>docer.pl</a>-ийн хусалт, ном болон бусад бичгийн бүтээлүүдэд төвлөрсөн Польшийн файл хуваалцах вэбсайт. 2023 оны сүүлээр сайн дурын “p” хуссан. Бидэнд анхны вэбсайтаас сайн мета өгөгдөл байхгүй (файлын өргөтгөлүүд ч байхгүй), гэхдээ ном шиг файлуудыг шүүж, ихэвчлэн файлуудаас мета өгөгдлийг гаргаж чадсан. DuXiu epub-ууд, шууд DuXiu-ээс, сайн дурын “w” цуглуулсан. Зөвхөн сүүлийн үеийн DuXiu номнууд шууд цахим номоор авах боломжтой тул эдгээрийн ихэнх нь сүүлийн үеийн байх ёстой. Сайн дурын “m”-ээс ирсэн үлдсэн DuXiu файлууд, DuXiu-ийн өмчийн PDG форматаар биш (гол <a %(a_href)s>DuXiu өгөгдлийн багц</a>). Олон анхны эх сурвалжаас цуглуулсан, харамсалтай нь эдгээр эх сурвалжуудыг файлын замд хадгалаагүй. <span></span> <span></span> <span></span> Эротик номнуудын хусалт, сайн дурын <q>do no harm</q> гишүүний хийсэн. <a %(a1)s><q>Бусад metadata хусалт</q></a> дахь <q>hentai</q> metadata-тай тохирно. <span></span> <span></span> Сайн дурын “t” Японы манга хэвлэлийн газраас хуссан цуглуулга. <a %(a_href)s>Лонгкуаны сонгосон шүүхийн архивууд</a>, сайн дурын “c” өгсөн. <a %(a_href)s>magzdb.org</a>-ийн хусалт, Library Genesis-ийн холбоотон (энэ нь libgen.rs-ийн нүүр хуудсанд холбоослогдсон) боловч файлуудаа шууд өгөхийг хүсээгүй. 2023 оны сүүлээр сайн дурын "p" хүнээс авсан. <span></span> Янз бүрийн жижиг байршилтууд, өөрийн гэсэн дэд цуглуулга болохоор хангалттай том биш, гэхдээ директори хэлбэрээр дүрслэгдсэн. AvaxHome, Оросын файл хуваалцах вэбсайтаас авсан цахим номнууд. Сонин, сэтгүүлийн архив. <a %(a1)s><q>Бусад metadata хусалт</q></a> дахь <q>newsarch_magz</q> metadata-тай тохирно. <a %(a1)s>Философийн баримт бичгийн төв</a>-ийн хусалт. Польшийн номыг анхны гаргалтын ("scene") вэбсайтуудаас шууд цуглуулсан сайн дурын "o"-ийн цуглуулга. Сайн дурын "cgiym" болон "woz9ts" нарын хамтарсан <a %(a_href)s>shuge.org</a>-ийн цуглуулгууд. <span></span> <a %(a_href)s>“Транторын Эзэнт Гүрний Номын Сан”</a> (уран зохиолын номын сангийн нэрээр нэрлэгдсэн), 2022 онд сайн дурын "t" хүнээс хуссан. <span></span> <span></span> <span></span> Сайн дурын "woz9ts"-ийн дэд-дэд цуглуулгууд (директори хэлбэрээр дүрслэгдсэн): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (Тайванийн <a %(a_sikuquanshu)s>Dizhi(迪志)</a>-ээс), mebook (mebook.cc, 我的小书屋, миний жижиг номын өрөө — woz9ts: “Энэ сайт нь голчлон өндөр чанартай цахим номын файлуудыг хуваалцахад төвлөрдөг, заримыг нь эзэн өөрөө форматласан. Эзэн нь 2019 онд <a %(a_arrested)s>баривчлагдсан</a> бөгөөд хэн нэгэн түүний хуваалцсан файлуудын цуглуулгыг хийсэн.”). Сайн дурын "woz9ts"-ийн DuXiu-ийн үлдсэн файлууд, эдгээр нь DuXiu-ийн өмчийн PDG форматад байгаагүй (PDF болгон хөрвүүлэх шаардлагатай). "байршуулах" цуглуулга нь жижиг дэд цуглуулгууд болгон хуваагдсан бөгөөд эдгээр нь AACID болон торрент нэрнүүдэд заагдсан байдаг. Бүх дэд цуглуулгуудыг эхлээд үндсэн цуглуулгатай давхардсан эсэхийг шалгасан боловч "upload_records" мета өгөгдлийн JSON файлууд нь анхны файлуудтай холбоотой олон лавлагааг агуулсан хэвээр байна. Ном биш файлуудыг ихэнх дэд цуглуулгуудаас хассан бөгөөд ихэвчлэн "upload_records" JSON-д тэмдэглэгдээгүй байдаг. Дэд цуглуулгууд нь: Тэмдэглэл Дэд цуглуулга Олон дэд цуглуулгууд нь өөрсдөө дэд-дэд цуглуулгууд (жишээ нь, өөр өөр анхны эх үүсвэрүүдээс) бүрддэг бөгөөд эдгээр нь "filepath" талбаруудад директори хэлбэрээр дүрслэгдсэн байдаг. Аннагийн Архив руу байршуулах Энэ өгөгдлийн талаар манай блог нийтлэл <a %(a_worldcat)s>WorldCat</a> нь ашгийн бус <a %(a_oclc)s>OCLC</a>-ийн эзэмшдэг, дэлхийн номын сангуудаас метадата бичлэгүүдийг цуглуулдаг хувийн мэдээллийн сан юм. Энэ нь дэлхийн хамгийн том номын сангийн метадата цуглуулга байх магадлалтай. 2023 оны аравдугаар сард бид OCLC (WorldCat) мэдээллийн сангийн бүрэн хусалтыг <a %(a_scrape)s>гаргасан</a>, <a %(a_aac)s>Аннагийн Архив Контейнеруудын формат</a>-д. 2023 оны аравдугаар сар, анхны хувилбар: OCLC (WorldCat) Аннагийн Архив дахь torrents Anna’s Archive дээрх жишээ бичлэг (анхны цуглуулга) Anna’s Archive дээрх жишээ бичлэг (“zlib3” цуглуулга) Anna’s Archive-ийн торрентийг (мета өгөгдөл + агуулга) 1-р хувилбарын тухай блог бичлэг 2-р хувилбарын тухай блог бичлэг 2022 оны сүүлээр Z-Library-ийн сэжигтэн үүсгэн байгуулагчдыг баривчилж, домэйнуудыг АНУ-ын эрх баригчид хураан авсан. Түүнээс хойш вэбсайт аажмаар дахин онлайн болж байна. Одоогоор хэн ажиллуулж байгаа нь тодорхойгүй байна. 2023 оны 2-р сарын шинэчлэлт. Z-Library нь <a %(a_href)s>Library Genesis</a> нийгэмлэгээс үүсэлтэй бөгөөд анх тэдний өгөгдлөөр эхэлсэн. Түүнээс хойш мэргэжлийн түвшинд хөгжиж, илүү орчин үеийн интерфэйстэй болсон. Тиймээс тэд вэбсайтаа сайжруулахад мөнгөн хандив, шинэ номын хандив аль алиныг нь илүү ихээр авах боломжтой болсон. Тэд Library Genesis-ийн нэмэлтээр том цуглуулга хуримтлуулсан. Цуглуулга нь гурван хэсгээс бүрдэнэ. Эхний хоёр хэсгийн анхны тайлбар хуудсуудыг доор хадгалсан болно. Бүх өгөгдлийг авахын тулд гурван хэсгийг бүгдийг нь авах шаардлагатай (хэтэрсэн торрентийг оруулахгүй, торрентийн хуудсанд дарж тэмдэглэсэн). %(title)s: манай анхны хувилбар. Энэ нь тухайн үед “Pirate Library Mirror” (“pilimi”) гэж нэрлэгдэж байсан анхны хувилбар юм. %(title)s: хоёр дахь хувилбар, энэ удаад бүх файлуудыг .tar файлуудад багтаасан. %(title)s: нэмэлт шинэ хувилбарууд, одоо <a %(a_href)s>Anna’s Archive Containers (AAC) формат</a>-ыг ашиглан Z-Library багтай хамтран гаргаж байна. Эхний толь 2021 болон 2022 оны туршид хичээнгүйлэн олж авсан. Энэ үед бага зэрэг хуучирсан: 2021 оны 6-р сарын цуглуулгын байдлыг тусгасан. Бид үүнийг ирээдүйд шинэчлэх болно. Одоогоор бид энэ анхны хувилбарыг гаргахад анхаарч байна. Library Genesis нь олон нийтийн торрентоор аль хэдийн хадгалагдсан бөгөөд Z-Library-д багтсан тул бид 2022 оны 6-р сард Library Genesis-тэй үндсэн давхардлыг арилгасан. Үүний тулд бид MD5 хэшүүдийг ашигласан. Номын сан дотор олон давхардсан агуулга байж магадгүй, жишээ нь нэг номын олон файл форматууд. Үүнийг нарийн илрүүлэхэд хэцүү тул бид үүнийг хийдэггүй. Давхардлыг арилгасны дараа бид 2 сая гаруй файлтай, нийт 7TB-аас бага хэмжээтэй үлдсэн. Цуглуулга нь хоёр хэсгээс бүрдэнэ: мета өгөгдлийн MySQL “.sql.gz” дамп болон 50-100GB орчим 72 торрентийн файл. Мета өгөгдөл нь Z-Library вэбсайтаас мэдээлэгдсэн өгөгдлийг (гарчиг, зохиогч, тайлбар, файл төрөл) агуулдаг бөгөөд бидний ажигласан бодит файлын хэмжээ болон md5sum-ийг агуулдаг, учир нь заримдаа эдгээр нь тохирохгүй байдаг. Z-Library өөрөө буруу мета өгөгдөлтэй файлуудын хүрээ байж магадгүй. Бид бас зарим тусгаарлагдсан тохиолдолд буруу татсан файлууд байж магадгүй бөгөөд үүнийг ирээдүйд илрүүлж засахыг хичээх болно. Том торрентийн файлууд нь бодит номын өгөгдлийг агуулдаг бөгөөд Z-Library ID-г файлын нэр болгон ашигладаг. Файлын өргөтгөлүүдийг мета өгөгдлийн дампаар сэргээж болно. Цуглуулга нь уран зохиолын бус болон уран зохиолын агуулгын холимог (Library Genesis шиг тусгаарлагдаагүй). Чанар нь мөн янз бүр байна. Энэхүү анхны хувилбар одоо бүрэн ашиглах боломжтой боллоо. Торрент файлууд зөвхөн манай Tor толин тусгал дээрээс авах боломжтойг анхаарна уу. 1-р хувилбар (%(date)s) Энэ нь нэг нэмэлт торрент файл юм. Энэ нь ямар ч шинэ мэдээлэл агуулаагүй боловч тооцоолох хугацаа ихтэй зарим өгөгдлийг агуулдаг. Энэ нь татаж авахад хурдан байдаг тул ашиглахад тохиромжтой. Тодруулбал, энэ нь tar файлуудын SQLite индексүүдийг агуулдаг бөгөөд <a %(a_href)s>ratarmount</a>-тай ашиглахад зориулагдсан. Хувилбар 2 нэмэлт (%(date)s) Бид сүүлийн толин тусгал болон 2022 оны наймдугаар сарын хооронд Z-Library-д нэмэгдсэн бүх номыг авсан. Мөн бид анх удаа алгассан зарим номыг дахин татаж авсан. Нийтдээ энэ шинэ цуглуулга ойролцоогоор 24TB хэмжээтэй. Дахин хэлэхэд, энэ цуглуулга Library Genesis-тэй давхардсан байгаа, учир нь тэр цуглуулгын торрентууд аль хэдийн бэлэн байгаа. Мэдээлэл нь анхны хувилбартай адил зохион байгуулагдсан. Метадата нь MySQL “.sql.gz” дамп хэлбэрээр байгаа бөгөөд үүнд анхны хувилбарын бүх метадата багтсан тул үүнийг орлож байна. Мөн бид зарим шинэ багануудыг нэмсэн: Бид үүнийг өнгөрсөн удаа дурдсан боловч тодруулахын тулд: “filename” болон “md5” нь файлын бодит шинж чанарууд, харин “filename_reported” болон “md5_reported” нь Z-Library-ээс татаж авсан зүйлс юм. Заримдаа эдгээр хоёр нь хоорондоо тохирохгүй байдаг тул бид хоёуланг нь оруулсан. Энэ хувилбарт бид collation-ийг “utf8mb4_unicode_ci” болгон өөрчилсөн бөгөөд энэ нь MySQL-ийн хуучин хувилбаруудтай нийцтэй байх ёстой. Мэдээллийн файлууд нь өмнөхтэй адил боловч илүү том хэмжээтэй. Бид олон жижиг торрент файлуудыг үүсгэхэд төвөгшөөсөн. “pilimi-zlib2-0-14679999-extra.torrent” нь өмнөх хувилбарт алгассан бүх файлуудыг агуулсан бол бусад торрентууд нь шинэ ID хүрээнүүд юм.  <strong>Шинэчлэлт %(date)s:</strong> Бидний ихэнх торрентууд хэтэрхий том байсан тул торрент клиентүүдэд асуудал үүсгэсэн. Бид тэдгээрийг устгаж, шинэ торрентуудыг гаргасан. <strong>Шинэчлэлт %(date)s:</strong> Файлууд хэтэрхий олон байсан тул бид тэдгээрийг tar файлуудад боож, дахин шинэ торрентуудыг гаргасан. %(key)s: энэ файл аль хэдийн Library Genesis-д байгаа эсэх, аль ч уран зохиолын эсвэл уран зохиолын бус цуглуулгад (md5-аар таарсан). %(key)s: энэ файл аль торрентод байгаа. %(key)s: номыг татаж авах боломжгүй үед тохируулсан. Хувилбар 2 (%(date)s) Zlib хувилбарууд (анхны тайлбар хуудсууд) Tor домэйн Үндсэн вэбсайт Z-Library-ийн хусалт “Хятад” цуглуулга Z-Library-д манай DuXiu цуглуулгатай адилхан харагдаж байна, гэхдээ өөр MD5-тай. Бид эдгээр файлуудыг давхардлаас зайлсхийхийн тулд торрентоос хасдаг ч хайлтын индексдээ харуулдаг. Метадата Таныг хэрэглэгч %(profile_link)s урьсан тул та %(percentage)s%% бонус хурдан таталт авна. Энэ нь бүх гишүүнчлэлийн хугацаанд хүчинтэй. Хандив өгөх Нэгдэх Сонгосон %(percentage)s%% хүртэл хөнгөлөлт Alipay олон улсын кредит/дебит картуудыг дэмждэг. Дэлгэрэнгүй мэдээллийг <a %(a_alipay)s>энэ гарын авлагаас</a> үзнэ үү. Кредит/дебит картаа ашиглан Amazon.com бэлгийн карт илгээнэ үү. Та кредит/дебит карт ашиглан крипто худалдан авч болно. WeChat (Weixin Pay) олон улсын кредит/дебит картуудыг дэмждэг. WeChat апп-д “Me => Services => Wallet => Add a Card” руу орно уу. Хэрэв энэ нь харагдахгүй бол “Me => Settings => General => Tools => Weixin Pay => Enable” ашиглан идэвхжүүлнэ үү. (Coinbase-ээс Ethereum илгээх үед ашиглана уу) хуулбарлагдсан! хуулбарлах (хамгийн бага хэмжээтэй) (анхааруулга: өндөр хэмжээтэй) -%(percentage)s%% 12 сар 1 сар 24 сар 3 сар 48 сар 6 сар 96 сар Та хэр удаан хугацаагаар захиалахыг хүсэж байгаагаа сонгоно уу. <div %(div_monthly_cost)s></div><div %(div_after)s>хөнгөлөлтийн дараа <span %(span_discount)s></span></div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 сарын хугацаанд 1 сарын хугацаанд 24 сарын хугацаанд 3 сарын хугацаанд 48 сарын хугацаанд 6 сарын хугацаанд 96 сарын хугацаанд %(monthly_cost)s / сар бидэнтэй холбогдоорой Шууд <strong>SFTP</strong> серверүүд Шинэ цуглуулгуудын (жишээ нь, шинэ скан, OCR хийсэн датасетууд) төлөө байгууллагын түвшний хандив эсвэл солилцоо. Мэргэжлийн хандалт <strong>Хязгааргүй</strong> өндөр хурдны хандалт <div %(div_question)s>Би гишүүнчлэлээ сайжруулах эсвэл олон гишүүнчлэл авах боломжтой юу?</div> <div %(div_question)s>Би гишүүн болохгүйгээр хандив өгч болох уу?</div> Мэдээж. Бид энэ Monero (XMR) хаягаар ямар ч хэмжээний хандив хүлээн авдаг: %(address)s. <div %(div_question)s>Сар бүрийн хүрээ юу гэсэн үг вэ?</div> Та бүх хөнгөлөлтийг ашигласнаар хүрээний доод талд хүрч болно, жишээ нь нэг сараас урт хугацааг сонгох гэх мэт. <div %(div_question)s>Гишүүнчлэлүүд автоматаар сунгагддаг уу?</div> Гишүүнчлэлүүд <strong>автоматаар</strong> сунгагддаггүй. Та хүссэн хугацаагаар нэгдэх боломжтой. <div %(div_question)s>Та хандивыг юунд зарцуулдаг вэ?</div> 100%% дэлхийн мэдлэг, соёлыг хадгалах, хүртээмжтэй болгоход зориулагддаг. Одоогоор бид ихэвчлэн серверүүд, хадгалах сан, өргөн зурваст зарцуулдаг. Багийн гишүүдэд хувийн мөнгө огт очдоггүй. <div %(div_question)s>Би их хэмжээний хандив өгч болох уу?</div> Энэ гайхалтай байх болно! Хэдэн мянган доллараас дээш хандив өгөх бол %(email)s хаягаар шууд бидэнтэй холбогдоорой. <div %(div_question)s>Танд өөр төлбөрийн аргууд байгаа юу?</div> Одоогоор байхгүй. Олон хүмүүс ийм архивуудыг оршин байлгахыг хүсдэггүй тул бид болгоомжтой байх хэрэгтэй. Хэрэв та бидэнд өөр (илүү тохиромжтой) төлбөрийн аргуудыг аюулгүйгээр тохируулахад тусалж чадах бол %(email)s хаягаар бидэнтэй холбогдоорой. Хандивын Түгээмэл Асуултууд Танд <a %(a_donation)s>одоо байгаа хандив</a> үргэлжилж байна. Шинэ хандив өгөхөөс өмнө энэ хандивыг дуусгах эсвэл цуцлах хэрэгтэй. <a %(a_all_donations)s>Миний бүх хандивыг харах</a> $5000-аас дээш хандивын хувьд бидэнтэй шууд %(email)s хаягаар холбогдоорой. Бид чинээлэг хувь хүмүүс эсвэл байгууллагуудаас их хэмжээний хандивыг хүлээн авдаг.  Энэ хуудсан дээрх гишүүнчлэлүүд нь “сараар” боловч нэг удаагийн хандив (давтагдахгүй) гэдгийг анхаарна уу. <a %(faq)s>Хандивын Түгээмэл Асуултууд</a>-ыг үзнэ үү. Аннагийн Архив нь ашгийн бус, нээлттэй эх сурвалж, нээлттэй өгөгдлийн төсөл юм. Хандив өгч, гишүүн болсноор та манай үйл ажиллагаа, хөгжлийг дэмжиж байна. Бүх гишүүддээ: биднийг дэмжиж байгаад баярлалаа! ❤️ Дэлгэрэнгүй мэдээллийг <a %(a_donate)s>Хандивын Түгээмэл Асуултууд</a> хэсгээс үзнэ үү. Гишүүн болохын тулд <a %(a_login)s>Нэвтрэх эсвэл Бүртгүүлэх</a> хэрэгтэй. Таны дэмжлэгт баярлалаа! %(cost)s / сар Хэрэв та төлбөрийн явцад алдаа гаргасан бол бид буцаан олголт хийх боломжгүй, гэхдээ бид үүнийг засахыг хичээх болно. PayPal апп эсвэл вэбсайт дээр “Crypto” хуудсыг олоорой. Энэ нь ихэвчлэн “Finances” хэсэгт байдаг. PayPal апп эсвэл вэбсайт дээр “Bitcoin” хуудсанд очно уу. “Transfer” товчийг дарж %(transfer_icon)s, дараа нь “Send” товчийг дарна уу. Alipay Alipay 支付宝 / WeChat 微信 Amazon Бэлгийн Карт %(amazon)s бэлгийн карт Банкны карт Банкны карт (апп ашиглан) Бинанс Кредит/дебит/Apple/Google (BMC) Cash App Кредит/дебит карт Кредит/дебит карт 2 Кредит/дебит карт (нөөц) Крипто %(bitcoin_icon)s Карт / PayPal / Venmo PayPal (АНУ) %(bitcoin_icon)s PayPal PayPal (байнгын) Pix (Бразил) Revolut (түр хугацаагаар боломжгүй) WeChat Өөрийн хүссэн крипто зоосыг сонгоно уу: Amazon бэлгийн карт ашиглан хандив өгөх. <strong>ЧУХАЛ:</strong> Энэ сонголт нь %(amazon)s зориулсан. Хэрэв та өөр Amazon вэбсайт ашиглахыг хүсвэл дээрээс сонгоно уу. <strong>ЧУХАЛ:</strong> Бид зөвхөн Amazon.com-ыг дэмждэг, бусад Amazon вэбсайтуудыг дэмждэггүй. Жишээлбэл, .de, .co.uk, .ca, дэмжигдэхгүй. Өөрийн мессежийг бичихгүй байхыг хүсье. Яг хэмжээ оруулна уу: %(amount)s Манай борлуулагчдын хүлээн зөвшөөрсөн хэмжээнд (хамгийн багадаа %(minimum)s) дүйцүүлэх шаардлагатайг анхаарна уу. Alipay апп ашиглан кредит/дебит картаар хандив өргөх (суулгахад маш хялбар). Alipay аппыг <a %(a_app_store)s>Apple App Store</a> эсвэл <a %(a_play_store)s>Google Play Store</a>-оос суулгаарай. Утасны дугаараа ашиглан бүртгүүлнэ үү. Цаашид хувийн мэдээлэл шаардлагагүй. <span %(style)s>1</span>Alipay апп суулгах Дэмжигдсэн: Visa, MasterCard, JCB, Diners Club болон Discover. Дэлгэрэнгүй мэдээллийг <a %(a_alipay)s>энэ гарын авлагаас</a> үзнэ үү. <span %(style)s>2</span>Банкны карт нэмэх Binance ашиглан та кредит/дебит карт эсвэл банкны дансаар Bitcoin худалдан авч, дараа нь тэр Bitcoin-ийг бидэнд хандивлана. Энэ нь биднийг таны хандивыг хүлээн авах үед аюулгүй, нэрээ нууцлах боломжийг олгодог. Binance нь бараг бүх улсад байдаг бөгөөд ихэнх банкууд болон кредит/дебит картыг дэмждэг. Энэ нь одоогоор бидний гол зөвлөмж юм. Энэ аргыг ашиглан хандив өгөх талаар суралцахад цаг гаргасанд баярлалаа, энэ нь бидэнд их тус болдог. Кредит карт, дебит карт, Apple Pay, болон Google Pay-д зориулан бид “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) ашигладаг. Тэдний системд нэг “кофе” нь $5-тэй тэнцүү тул таны хандив 5-ын ойролцоо тоонд дугуйлагдана. Cash App ашиглан хандив өгөх. Хэрэв танд Cash App байгаа бол энэ нь хандив өгөх хамгийн хялбар арга юм! %(amount)s-аас доош гүйлгээний хувьд Cash App %(fee)s төлбөр авах боломжтойг анхаарна уу. %(amount)s болон түүнээс дээш бол үнэгүй! Кредит эсвэл дебит картаар хандив өгөх. Энэ арга нь криптовалютын үйлчилгээ үзүүлэгчийг завсрын хөрвүүлэгч болгон ашигладаг. Энэ нь бага зэрэг төвөгтэй байж болох тул бусад төлбөрийн аргууд ажиллахгүй бол энэ аргыг ашиглана уу. Мөн бүх улсад ажиллахгүй. Бид кредит/дебит картуудыг шууд дэмжих боломжгүй, учир нь банкууд бидэнтэй хамтран ажиллахыг хүсэхгүй байна. ☹ Гэсэн хэдий ч, бусад төлбөрийн аргуудыг ашиглан кредит/дебит картуудыг ашиглах хэд хэдэн арга бий: Крипто ашиглан та BTC, ETH, XMR, SOL ашиглан хандив өгөх боломжтой. Хэрэв та криптовалюттай аль хэдийн танил болсон бол энэ сонголтыг ашиглана уу. Крипто ашиглан та BTC, ETH, XMR болон бусад ашиглан хандив өгөх боломжтой. Крипто түргэн үйлчилгээ Хэрэв та криптог анх удаа ашиглаж байгаа бол Bitcoin (анхны болон хамгийн их ашиглагддаг криптовалют)-ыг худалдаж авах, хандивлахын тулд %(options)s-ийг ашиглахыг зөвлөж байна. Бага хэмжээний хандивын хувьд кредит картын төлбөр нь бидний %(discount)s%% хөнгөлөлтийг арилгаж болзошгүй тул урт хугацааны захиалгыг зөвлөж байна. Кредит/дебит карт, PayPal, эсвэл Venmo ашиглан хандив өгөх. Дараагийн хуудсанд эдгээрээс сонгох боломжтой. Google Pay болон Apple Pay мөн ажиллаж магадгүй. Бага хэмжээний хандивын хувьд төлбөр өндөр байдаг тул урт хугацааны захиалгыг зөвлөж байна. PayPal US ашиглан хандив өгөхийн тулд бид PayPal Crypto-г ашиглах болно, энэ нь биднийг нэрээ нууцлах боломжийг олгодог. Энэ аргыг ашиглан хандив өгөх талаар суралцахад цаг гаргасанд баярлалаа, энэ нь бидэнд их тус болдог. PayPal ашиглан хандив өгөх. Өөрийн байнгын PayPal дансаа ашиглан хандив өргөх. Revolut ашиглан хандив өргөх. Хэрэв та Revolut ашигладаг бол энэ нь хандив өргөх хамгийн хялбар арга юм! Энэ төлбөрийн арга нь хамгийн ихдээ %(amount)s зөвшөөрдөг. Өөр хугацаа эсвэл төлбөрийн аргыг сонгоно уу. Энэ төлбөрийн арга нь хамгийн багадаа %(amount)s шаарддаг. Өөр хугацаа эсвэл төлбөрийн аргыг сонгоно уу. Бинанс Coinbase Kraken Төлбөрийн аргыг сонгоно уу. “Торрент өргөж авах”: таны хэрэглэгчийн нэр эсвэл мессеж торрент файлын нэрэнд <div %(div_months)s>жилд нэг удаа гишүүнчлэлийн хугацаанд</div> Таны хэрэглэгчийн нэр эсвэл нэргүйгээр дурдагдах Шинэ боломжуудыг эрт ашиглах Онцгой Telegram сувгаар арын албаны шинэчлэлтүүд %(number)s хурдан таталт өдөрт Хэрэв та энэ сард хандив өргөвөл! <a %(a_api)s>JSON API</a> хандалт Хүн төрөлхтний мэдлэг, соёлыг хадгалахад домогт байр суурь Өмнөх давуу талууд, нэмээд: <a %(a_refer)s>найзуудаа урих</a> замаар <strong>%(percentage)s%% бонус таталт</strong> аваарай. SciDB өгүүллүүд <strong>хязгааргүй</strong> баталгаажуулалтгүйгээр Данс эсвэл хандивын асуулт асуухдаа, дансны ID, дэлгэцийн зураг, баримт, аль болох их мэдээллийг оруулна уу. Бид имэйлээ 1-2 долоо хоногт нэг удаа шалгадаг тул энэ мэдээллийг оруулаагүй тохиолдолд шийдвэрлэх хугацаа удааширна. Илүү олон таталт авахын тулд <a %(a_refer)s>найзуудаа урих</a>! Бид сайн дурын жижиг баг. Хариу өгөхөд 1-2 долоо хоног зарцуулагдаж магадгүй. Анхаарна уу, дансны нэр эсвэл зураг хачин харагдаж магадгүй. Санаа зовох хэрэггүй! Эдгээр дансуудыг манай хандивын түншүүд удирддаг. Манай дансууд хакердуулаагүй. Хандив өгөх <span %(span_cost)s></span> <span %(span_label)s></span> 12 сарын хугацаанд “%(tier_name)s” 1 сарын хугацаанд “%(tier_name)s” 24 сарын хугацаанд “%(tier_name)s” 3 сарын хугацаанд “%(tier_name)s” 48 сарын хугацаанд “%(tier_name)s” 6 сарын хугацаанд “%(tier_name)s” 96 сарын хугацаанд “%(tier_name)s” Та төлбөрийн явцад хандивыг цуцлах боломжтой. Энэ хандивыг баталгаажуулахын тулд хандив өгөх товчийг дарна уу. <strong>Чухал тэмдэглэл:</strong> Крипто үнэ огцом хэлбэлзэж болно, заримдаа хэдхэн минутын дотор 20%% хүртэл хэлбэлздэг. Энэ нь олон төлбөрийн үйлчилгээ үзүүлэгчдийн хураамжаас бага бөгөөд тэд ихэвчлэн 50-60%% хүртэл хураамж авдаг. <u>Хэрэв та бидэнд анхны төлсөн үнийн баримтаа илгээвэл, бид таны дансыг сонгосон гишүүнчлэлээрээ бүртгэх болно</u> (баримт хэдхэн цагийн дотор байх ёстой). Биднийг дэмжихийн тулд ийм зүйлсийг тэвчиж байгаад бид үнэхээр талархаж байна! ❤️ ❌ Алдаа гарлаа. Хуудсыг дахин ачааллаж, дахин оролдоно уу. <span %(span_circle)s>1</span>Paypal дээрээс Bitcoin худалдан авах <span %(span_circle)s>2</span>Bitcoin-ийг манай хаяг руу шилжүүлэх ✅ Хандивын хуудсанд шилжүүлж байна… Хандив өгөх Бидэнтэй холбогдохоос өмнө <span %(span_hours)s>24 цаг</span> хүлээж (энэ хуудсыг шинэчилнэ үү). Хэрэв та гишүүнчлэлгүйгээр (ямар ч хэмжээтэй) хандив өгөхийг хүсвэл энэ Monero (XMR) хаягийг ашиглана уу: %(address)s. Бэлгийн картыг илгээсний дараа манай автомат систем хэдхэн минутын дотор баталгаажуулна. Хэрэв энэ ажиллахгүй бол бэлгийн картыг дахин илгээхийг оролдоно уу (<a %(a_instr)s>зааварчилгаа</a>). Хэрэв энэ нь бас ажиллахгүй бол бидэнд имэйл илгээнэ үү, Анна үүнийг гараар шалгана (энэ нь хэдэн өдөр шаардагдаж магадгүй), мөн та дахин илгээхийг оролдсон эсэхээ дурдаарай. Жишээ: Бидэнд %(amount)s бэлгийн картыг доорх имэйл хаяг руу илгээхийн тулд <a %(a_form)s>албан ёсны Amazon.com маягтыг</a> ашиглана уу. “Хэнд” хүлээн авагчийн имэйл: Amazon бэлгийн карт Бид өөр аргаар илгээсэн бэлгийн картыг хүлээн авах боломжгүй, <strong>зөвхөн Amazon.com дээрх албан ёсны маягтаар шууд илгээсэн тохиолдолд хүлээн авна</strong>. Хэрэв та энэ маягтыг ашиглахгүй бол бид таны бэлгийн картыг буцааж өгөх боломжгүй. Зөвхөн нэг удаа ашиглана уу. Таны дансанд өвөрмөц, хуваалцахгүй байх. Бэлгийн картыг хүлээж байна… (шинэчлэхийн тулд хуудсыг дахин ачаална уу) <a %(a_href)s>QR код хандивын хуудас</a>-ыг нээнэ үү. Alipay аппликейшнээр QR кодыг сканнердах эсвэл Alipay аппликейшн нээх товчлуурыг дарна уу. Тэвчээртэй байгаарай; хуудас Хятадад байгаа тул ачаалахад хэсэг хугацаа шаардагдаж магадгүй. <span %(style)s>3</span>Хандив өгөх (QR кодыг сканнердах эсвэл товчлуурыг дарах) PYUSD койныг PayPal дээрээс худалдаж аваарай Cash App ашиглан Bitcoin (BTC) худалдаж авах Гүйлгээний шимтгэлийг нөхөхийн тулд хандивлаж буй дүнгээсээ (%(amount)s) %(more)s илүү худалдаж аваарай. Үлдсэн мөнгө таны дансанд үлдэнэ. Cash App дахь “Bitcoin” (BTC) хуудсанд очно уу. Bitcoin-ийг манай хаяг руу шилжүүлнэ үү Жижиг хандив (25 доллараас доош) хийх бол Rush эсвэл Priority ашиглах шаардлагатай байж магадгүй. “Send bitcoin” товчийг дарж “withdrawal” хийнэ үү. %(icon)s дүрсийг дарж доллароос BTC руу шилжүүлнэ үү. Доорх BTC дүнг оруулаад “Send” товчийг дарна уу. Хэрэв гацвал <a %(help_video)s>энэ видеог</a> үзнэ үү. Түргэн үйлчилгээ нь тохиромжтой боловч илүү өндөр хураамж авдаг. Хэрэв та хурдан том хэмжээний хандив өгөхийг хүсэж байгаа бөгөөд $5-10 хураамж төлөхөд дургүйцэхгүй бол крипто солилцооны оронд үүнийг ашиглаж болно. Хандивын хуудсанд харуулсан крипто хэмжээтэй яг тэнцүүг илгээнэ үү, $USD дүнг биш. Үгүй бол хураамж хасагдаж, бид таны гишүүнчлэлийг автоматаар боловсруулах боломжгүй болно. Заримдаа баталгаажуулалт 24 цаг хүртэл хугацаа шаардагддаг тул энэ хуудсыг дахин ачаалж байгаарай (хэдий хугацаа нь дууссан байсан ч). Кредит / дебит картын зааварчилгаа Манай кредит / дебит картын хуудсаар дамжуулан хандивлана уу Зарим алхамууд крипто түрийвчийг дурдсан боловч санаа зовох хэрэггүй, та крипто талаар юу ч сурах шаардлагагүй. %(coin_name)s зааварчилгаа Төлбөрийн мэдээллийг хурдан бөглөхийн тулд энэ QR кодыг SCRY кодоор сканнердах QR кодыг төлөхийн тулд сканнердах Бид зөвхөн крипто койны стандарт хувилбарыг дэмждэг, ховор сүлжээ эсвэл хувилбаруудыг дэмжихгүй. Гүйлгээг баталгаажуулахад койноос хамааран нэг цаг хүртэл хугацаа шаардагдаж болно. <a %(a_page)s>энэ хуудсанд</a> %(amount)s хандивлана уу. Энэ хандивын хугацаа дууссан байна. Та цуцалж, шинээр үүсгэнэ үү. Хэрэв та аль хэдийн төлбөрөө төлсөн бол: Тийм ээ, би баримтаа имэйлээр илгээсэн Хэрэв крипто солилцооны ханш гүйлгээний явцад өөрчлөгдсөн бол анхны солилцооны ханшийг харуулсан баримтыг заавал оруулна уу. Крипто ашиглахад тань бид үнэхээр талархаж байна, энэ нь бидэнд их тус болдог! ❌ Алдаа гарлаа. Хуудсыг дахин ачаалж, дахин оролдоно уу. <span %(span_circle)s>%(circle_number)s</span>Бидэнд баримтаа имэйлээр илгээнэ үү Хэрэв ямар нэгэн асуудал гарвал %(email)s хаягаар бидэнтэй холбогдож, аль болох их мэдээлэл (жишээ нь, дэлгэцийн зураг) оруулна уу. ✅ Хандив өргөсөнд баярлалаа! Анна таны гишүүнчлэлийг хэдэн өдрийн дотор гар аргаар идэвхжүүлэх болно. Баримт эсвэл дэлгэцийн агшинг хувийн баталгаажуулах хаяг руу илгээнэ үү: Баримтаа имэйлээр илгээсний дараа энэ товчийг дарж, Анна гар аргаар шалгах боломжтой (энэ нь хэдэн өдөр шаардагдаж магадгүй): Хувийн баталгаажуулалтын хаяг руу баримт эсвэл дэлгэцийн агшинг илгээнэ үү. Энэ имэйл хаягийг PayPal хандивынхаа хувьд БҮҮ ашигла. Цуцлах Тийм, цуцлаарай Та цуцлахдаа итгэлтэй байна уу? Хэрэв та аль хэдийн төлбөрөө төлсөн бол цуцлахгүй байхыг зөвлөж байна. ❌ Алдаа гарлаа. Хуудас дахин ачааллаж, дахин оролдоно уу. Шинэ хандив хийх ✅ Таны хандив цуцлагдлаа. Огноо: %(date)s Таних тэмдэг: %(id)s Дахин захиалах Төлөв: <span %(span_label)s>%(label)s</span> Нийт: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / сар бүр %(duration)s сарын турш, %(discounts)s%% хөнгөлөлтийг оруулсан)</span> Нийт: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / сар бүр %(duration)s сарын турш)</span> 1. Имэйлээ оруулна уу. 2. Төлбөрийн аргаа сонгоно уу. Төлбөрийн аргаа дахин сонгоно уу. "Өөрөө хостлох" түрийвчийг сонгоно уу. "Би эзэмшлийг баталгаажуулж байна" дээр дарна уу. Танд имэйл хүлээн авах ёстой. Бидэнд илгээнэ үү, бид таны хандивыг аль болох хурдан баталгаажуулах болно. (та цуцалж, шинэ хандив үүсгэхийг хүсэж магадгүй) Төлбөрийн заавар одоо хуучирсан байна. Хэрэв та дахин хандив өгөхийг хүсвэл дээрх “Дахин захиалах” товчийг ашиглана уу. Та аль хэдийн төлбөрөө төлсөн байна. Хэрэв та төлбөрийн зааврыг дахин харахыг хүсвэл энд дарна уу: Хуучин төлбөрийн зааврыг үзүүлэх Хандивын хуудас хаагдсан бол өөр интернет холболт (жишээ нь VPN эсвэл утасны интернет) ашиглаарай. Харамсалтай нь, Alipay хуудас ихэвчлэн зөвхөн <strong>Хятадын эх газар</strong>-аас нэвтрэх боломжтой байдаг. Та түр хугацаанд VPN-ээ унтраах эсвэл Хятадын эх газар руу (заримдаа Хонг Конг ч бас ажилладаг) VPN ашиглах шаардлагатай байж магадгүй. <span %(span_circle)s>1</span>Alipay дээр хандив өргөх %(total)s нийт дүнг <a %(a_account)s>энэ Alipay данс</a>-аар хандивлаарай Alipay зааварчилгаа <span %(span_circle)s>1</span>Манай крипто данс руу шилжүүлнэ үү %(total)s нийт дүнг дараах хаягуудын аль нэгэнд хандивлана уу: Крипто зааварчилгаа Bitcoin (BTC) худалдан авах зааврыг дагана уу. Та зөвхөн хандивлахыг хүссэн хэмжээгээ худалдан авахад хангалттай, %(total)s. Манай Bitcoin (BTC) хаягийг хүлээн авагчийн хаяг болгон оруулж, %(total)s хандивыг илгээх зааврыг дагана уу: <span %(span_circle)s>1</span>Pix дээр хандив өргөх %(total)s нийт дүнг <a %(a_account)s>энэ Pix данс</a>-аар хандивлаарай Pix зааварчилгаа <span %(span_circle)s>1</span>WeChat дээр хандив өргөх %(total)s нийт дүнг <a %(a_account)s>энэ WeChat данс</a>-аар хандивлаарай WeChat зааварчилгаа Дараах “кредит картаас Bitcoin руу” шуурхай үйлчилгээнүүдийг ашиглаж болно, ердөө хэдхэн минут зарцуулна: BTC / Bitcoin хаяг (гадны түрийвч): BTC / Bitcoin хэмжээ: Маягтанд дараах мэдээллийг бөглөнө үү: Хэрэв энэ мэдээлэл хуучирсан бол бидэнд имэйлээр мэдэгдэнэ үү. Энэ <span %(underline)s>яг хэмжээ</span>-г ашиглана уу. Таны нийт зардал кредит картын шимтгэлийн улмаас өндөр байж магадгүй. Жижиг хэмжээний хувьд энэ нь манай хөнгөлөлтөөс илүү байж магадгүй. (хамгийн бага: %(minimum)s, анхны гүйлгээнд баталгаажуулалт шаардлагагүй) (хамгийн бага: %(minimum)s) (хамгийн бага: %(minimum)s) (хамгийн бага: %(minimum)s, анхны гүйлгээнд баталгаажуулалт шаардлагагүй) (хамгийн бага: %(minimum)s) (хамгийн бага: %(minimum)s улс орноос хамааран, анхны гүйлгээнд баталгаажуулалт шаардлагагүй) PYUSD койныг (PayPal USD) худалдаж авах зааврыг дагана уу. Гүйлгээний шимтгэлийг нөхөхийн тулд хандивлаж буй хэмжээнээсээ (%(amount)s) %(more)s илүүг худалдаж авахыг зөвлөж байна. Үлдсэн мөнгө таны дансанд үлдэнэ. PayPal апп эсвэл вэбсайт дахь “PYUSD” хуудсанд очно уу. “Шилжүүлэх” товчийг %(icon)s дараад, дараа нь “Илгээх” товчийг дарна уу. Төлөв шинэчлэх Цагийг дахин тохируулахын тулд зүгээр л шинэ хандив үүсгэнэ үү. Доорх BTC хэмжээг ашиглаарай, <em>Евро эсвэл доллар</em> биш, эс тэгвээс бид зөв хэмжээ хүлээн авахгүй бөгөөд таны гишүүнчлэлийг автоматаар баталгаажуулж чадахгүй. Revolut ашиглан Bitcoin (BTC) худалдаж авах Гүйлгээний шимтгэлийг нөхөхийн тулд хандивлаж буй дүнгээсээ (%(amount)s) %(more)s илүү худалдаж аваарай. Үлдсэн мөнгө таны дансанд үлдэнэ. Bitcoin (BTC) худалдаж авахын тулд Revolut дахь “Crypto” хуудсанд очно уу. Bitcoin-ийг манай хаяг руу шилжүүлнэ үү Жижиг хандив (25 доллараас доош) хийх бол Rush эсвэл Priority ашиглах шаардлагатай байж магадгүй. “Send bitcoin” товчийг дарж “withdrawal” хийнэ үү. %(icon)s дүрсийг дарж евроноос BTC руу шилжүүлнэ үү. Доорх BTC дүнг оруулаад “Send” товчийг дарна уу. Хэрэв гацвал <a %(help_video)s>энэ видеог</a> үзнэ үү. Төлөв: 1 2 Алхам алхмаар заавар Доорх алхам алхмаар зааврыг үзнэ үү. Үгүй бол та энэ данснаас түгжигдэж магадгүй! Хэрэв та үүнийг хараахан хийгээгүй бол, нууц түлхүүрээ тэмдэглэж аваарай: Таны хандивт баярлалаа! Үлдсэн хугацаа: Хандив %(amount)s-г %(account)s руу шилжүүлнэ үү Баталгаажуулалтыг хүлээж байна (шинэчлэхийн тулд хуудсыг дахин ачаална уу)… Шилжүүлгийг хүлээж байна (шинэчлэхийн тулд хуудсыг дахин ачаална уу)… Өмнө нь Сүүлийн 24 цагийн хурдан татан авалтууд өдөр тутмын хязгаарт тооцогдоно. Хурдан түнш серверүүдээс татаж авсан файлууд %(icon)s тэмдэглэгээтэй байна. Сүүлийн 18 цаг Одоогоор татаж авсан файл байхгүй байна. Татаж авсан файлууд олон нийтэд харагдахгүй. Бүх цагууд UTC-д байна. Татаж авсан файлууд Хэрэв та хурдан болон удаан татан авалттай файлыг татаж авсан бол энэ нь хоёр удаа харагдана. Санаа зоволтгүй, манай холбоосоор дамжуулан олон хүн татаж авдаг бөгөөд асуудалд орох нь маш ховор. Гэсэн хэдий ч аюулгүй байхын тулд VPN (төлбөртэй) эсвэл <a %(a_tor)s>Tor</a> (үнэгүй) ашиглахыг зөвлөж байна. Би Жорж Орвеллийн "1984" номыг татаж авсан, цагдаа нар миний хаалган дээр ирэх үү? Та Анна! Анна гэж хэн бэ? Бидэнд гишүүдэд зориулсан нэг тогтвортой JSON API байдаг бөгөөд хурдан татаж авах URL авах боломжтой: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON доторх баримт бичиг). Бусад хэрэглээний хувьд, жишээ нь бүх файлуудыг давтах, өөрийн хайлтыг бүтээх гэх мэт, бид <a %(a_generate)s>ElasticSearch</a> болон <a %(a_download)s>MariaDB</a> өгөгдлийн санг үүсгэх эсвэл татаж авахыг зөвлөж байна. Түүхий өгөгдлийг <a %(a_explore)s>JSON файлуудаар</a> гараар судалж болно. Манай түүхий торрент жагсаалтыг <a %(a_torrents)s>JSON</a> хэлбэрээр татаж авах боломжтой. Танд API бий юу? Бид энд ямар ч зохиогчийн эрхтэй материалыг байршуулаагүй. Бид хайлтын систем бөгөөд зөвхөн олон нийтэд нээлттэй байгаа мета өгөгдлийг индексжүүлдэг. Эдгээр гадаад эх сурвалжаас татаж авахдаа өөрийн оршин суугаа газрын хуулийг шалгахыг зөвлөж байна. Бусад хүмүүсийн байршуулах агуулгад бид хариуцлага хүлээхгүй. Хэрэв та энд үзсэн зүйлдээ гомдолтой байгаа бол эх вэбсайтад хандах нь хамгийн сайн арга юм. Бид тэдний өөрчлөлтийг тогтмол мэдээллийн санд оруулдаг. Хэрэв та үнэхээр хүчинтэй DMCA гомдол гаргах ёстой гэж үзэж байвал <a %(a_copyright)s>DMCA / Зохиогчийн эрхийн нэхэмжлэлийн маягт</a>-ыг бөглөнө үү. Бид таны гомдлыг нухацтай авч үзэж, аль болох хурдан хариу өгөх болно. Зохиогчийн эрх зөрчигдсөн тухай хэрхэн мэдээлэх вэ? Энд сүүдрийн номын сан болон дижитал хадгалалтын ертөнцөд онцгой ач холбогдолтой зарим номнууд байна: Таны дуртай номнууд юу вэ? Мөн бидний бүх код, өгөгдөл бүрэн нээлттэй эхийн гэдгийг хүн бүрт сануулахыг хүсч байна. Манай төслүүдийн хувьд энэ нь онцгой юм — бидний мэдэж байгаагаар ийм том каталогтой, бүрэн нээлттэй эхийн өөр төсөл байхгүй. Бид төслийг маань муу удирдаж байна гэж бодож байгаа хэн бүхнийг манай код, өгөгдлийг ашиглан өөрийн сүүдрийн номын санг байгуулахыг урьж байна! Бид үүнийг хорсол эсвэл ямар нэгэн зүйлээс болж хэлж байгаа юм биш — энэ нь хүн бүрийн түвшинг дээшлүүлж, хүн төрөлхтний өвийг илүү сайн хадгалах болно гэж үнэхээр бодож байна. Таны энэ төслийг хэрхэн удирдаж байгаад би дургүй! Бид <a %(a_mirrors)s>толь</a> үүсгэхийг хүсэж байна, мөн бид үүнд санхүүгийн дэмжлэг үзүүлэх болно. Би яаж туслах вэ? Бид үнэхээр цуглуулдаг. Манай метадата цуглуулах урам зориг нь Аарон Шварцийн “хэзээ нэгэн цагт хэвлэгдсэн бүх номын нэг вэб хуудас” зорилго юм, үүний тулд тэрээр <a %(a_openlib)s>Open Library</a>-г бүтээсэн. Тэр төсөл сайн явж байгаа ч манай өвөрмөц байр суурь бидэнд тэдний авч чадахгүй метадатаг авах боломжийг олгодог. Өөр нэг урам зориг нь дэлхий дээр хичнээн ном байгааг мэдэх хүсэл байсан бөгөөд ингэснээр бид хичнээн номыг аврах үлдсэн болохыг тооцоолж чадна. Та мета өгөгдөл цуглуулдаг уу? Mhut.org тодорхой IP хүрээг хаадаг тул VPN шаардлагатай байж магадгүй. <strong>Android:</strong> Дээд баруун буланд байгаа гурван цэгтэй цэсийг дарж, “Нүүр хуудас руу нэмэх” сонголтыг сонгоно уу. <strong>iOS:</strong> Доод талд байгаа “Хуваалцах” товчийг дарж, “Нүүр хуудас руу нэмэх” сонголтыг сонгоно уу. Бидэнд албан ёсны гар утасны аппликейшн байхгүй, гэхдээ та энэ вэбсайтыг аппликейшн болгон суулгаж болно. Танд гар утасны аппликейшн бий юу? Тэдгээрийг <a %(a_archive)s>Интернет Архив</a>-д илгээнэ үү. Тэдгээрийг зөв хадгалах болно. Би ном эсвэл бусад физик материалыг хэрхэн хандивлах вэ? Би ном хэрхэн хүсэх вэ? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — тогтмол шинэчлэлтүүд <a %(a_software)s>Аннагийн Програм хангамж</a> — манай нээлттэй эхийн код <a %(a_datasets)s>Datasets</a> — өгөгдлийн тухай <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — өөр домэйнууд Anna’s Archive-ийн талаар илүү их нөөц бий юу? <a %(a_translate)s>Аннагийн Програм хангамж дээр орчуулах</a> — манай орчуулгын систем <a %(a_wikipedia)s>Wikipedia</a> — бидний тухай дэлгэрэнгүй (энэ хуудсыг шинэчилж байхад туслаарай, эсвэл өөрийн хэл дээр нэгийг үүсгээрэй!) Тохиргоогоо сонгоод, хайлтын хайрцгийг хоосон байлгаад, “Хайх” товчийг дарж, дараа нь хөтчийнхөө хавчуурга функцээр хуудсыг хавчуурга болгоно уу. Хайлтын тохиргоогоо хэрхэн хадгалах вэ? Бид аюулгүй байдлын судлаачдыг манай системд эмзэг байдлыг хайж олоход урьж байна. Бид хариуцлагатай мэдээлэхийг дэмждэг. Бидэнтэй <a %(a_contact)s>энд</a> холбогдоно уу. Бид одоогоор алдааны шагнал олгох боломжгүй байгаа ч манай нэрээ нууцлах боломжийг алдагдуулах <a %(a_link)s>боломжтой эмзэг байдалд</a> $10k-50k хооронд шагнал санал болгодог. Ирээдүйд алдааны шагналын хүрээг өргөжүүлэхийг хүсэж байна! Нийгмийн инженерчлэлийн халдлагууд хамаарахгүй гэдгийг анхаарна уу. Хэрэв та довтолгооны аюулгүй байдалд сонирхолтой бөгөөд дэлхийн мэдлэг, соёлыг архивлахад туслахыг хүсэж байвал бидэнтэй холбогдоорой. Танд туслах олон арга зам бий. Танд хариуцлагатай мэдээлэх хөтөлбөр бий юу? Бид дэлхийн бүх хүмүүст өндөр хурдны татан авалт өгөх хангалттай нөөц байхгүй, бид үүнийг хүсэж байгаа ч. Хэрэв баян ивээн тэтгэгч бидэнд үүнийг хангаж өгвөл гайхалтай байх болно, гэхдээ тэр болтол бид чадах бүхнээ хийж байна. Бид хандивын тусламжтайгаар арай ядан өөрийгөө тэтгэж буй ашгийн бус төсөл юм. Энэ шалтгааны улмаас бид түншүүдтэйгээ хамт үнэгүй татан авалтын хоёр системийг хэрэгжүүлсэн: удаан татан авалттай хуваалцсан серверүүд, мөн хүлээлгийн жагсаалттай арай хурдан серверүүд (нэгэн зэрэг татан авалт хийж буй хүмүүсийн тоог багасгахын тулд). Мөн бид удаан татан авалтанд зориулж <a %(a_verification)s>хөтөчийн баталгаажуулалт</a> хийсэн, учир нь эс бөгөөс бот болон скрапперууд үүнийг ашиглаж, жинхэнэ хэрэглэгчдэд илүү удаан болгох болно. Tor Browser ашиглах үед та аюулгүй байдлын тохиргоогоо тохируулах шаардлагатай байж магадгүй гэдгийг анхаарна уу. “Стандарт” гэж нэрлэгддэг хамгийн доод сонголт дээр Cloudflare turnstile сорил амжилттай болдог. “Аюулгүй” болон “Хамгийн аюулгүй” гэж нэрлэгддэг өндөр сонголтууд дээр сорил амжилтгүй болдог. Том файлуудын хувьд заримдаа удаан таталт дундаас нь тасрах боломжтой. Бид том файлуудыг автоматаар үргэлжлүүлэхийн тулд таталтын менежер (жишээ нь JDownloader) ашиглахыг зөвлөж байна. Яагаад удаан татаж авах нь ийм удаан байна вэ? Түгээмэл асуултууд (FAQ) <a %(a_list)s>торрент жагсаалт үүсгэгчийг</a> ашиглан хадгалах зайны хязгаар дотор хамгийн их шаардлагатай байгаа торрентуудын жагсаалтыг үүсгээрэй. Тийм, <a %(a_llm)s>LLM өгөгдөл</a> хуудсыг үзнэ үү. Ихэнх торрентууд файлуудыг шууд агуулдаг тул та торрент клиентүүдэд зөвхөн шаардлагатай файлуудыг татаж авахыг зааж өгч болно. Ямар файлуудыг татаж авахыг тодорхойлохын тулд та манай мета өгөгдлийг <a %(a_generate)s>үүсгэж</a> эсвэл манай ElasticSearch болон MariaDB өгөгдлийн санг <a %(a_download)s>татаж авч</a> болно. Харамсалтай нь, зарим торрент цуглуулгууд үндсэндээ .zip эсвэл .tar файлуудыг агуулдаг тул та бүхэл торрентыг татаж авсны дараа л тус тусад нь файлуудыг сонгох боломжтой. Торрент шүүхэд хялбар хэрэгсэл одоогоор байхгүй байгаа ч бид хувь нэмэр оруулахыг урьж байна. (Гэхдээ бидэнд сүүлийн тохиолдолд <a %(a_ideas)s>зарим санаанууд</a> байгаа.) Урт хариулт: Товч хариулт: амархан биш. Бид энэ жагсаалт дахь торрентуудын хоорондох давхардал, давхцалыг хамгийн бага байлгахыг хичээдэг ч энэ нь үргэлж биелэгдэх боломжгүй бөгөөд эх сурвалжийн номын сангийн бодлогоос ихээхэн хамаардаг. Өөрсдийн торрентуудыг гаргадаг номын сангуудын хувьд энэ нь бидний хяналтаас гадуур байдаг. Anna’s Archive-аас гаргасан торрентуудын хувьд бид зөвхөн MD5 хэш дээр үндэслэн давхардлыг арилгадаг тул нэг номын өөр өөр хувилбарууд давхардалгүй үлддэг. Тийм. Эдгээр нь үнэндээ PDF болон EPUB-ууд бөгөөд манай олон торрентууд дээр өргөтгөлгүй байдаг. Торрент файлуудын мета өгөгдлийг олох хоёр газар байдаг, үүнд файлын төрлүүд/өргөтгөлүүд орно: 1. Тус бүрийн цуглуулга эсвэл хувилбар нь өөрийн мета өгөгдлийг агуулдаг. Жишээлбэл, <a %(a_libgen_nonfic)s>Libgen.rs торрентууд</a> нь Libgen.rs вэбсайт дээр байрлуулсан мета өгөгдлийн санг агуулдаг. Бид ихэвчлэн тус бүрийн цуглуулгын <a %(a_datasets)s>өгөгдлийн багцын хуудаснаас</a> холбогдох мета өгөгдлийн нөөцүүд рүү холбоос тавьдаг. 2. Бидний ElasticSearch болон MariaDB өгөгдлийн санг <a %(a_generate)s>үүсгэх</a> эсвэл <a %(a_download)s>татаж авахыг</a> зөвлөж байна. Эдгээр нь Anna’s Archive дахь бүртгэл бүрийг түүний холбогдох торрент файлуудтай (боломжтой бол) “torrent_paths” дотор ElasticSearch JSON-д харгалзуулан холбосон зураглалыг агуулдаг. Зарим торрент клиентүүд том хэсгийн хэмжээг дэмждэггүй бөгөөд манай олон торрентууд ийм байдаг (шинэчлэхдээ бид үүнийг хийхээ больсон — техникийн үзүүлэлтээр зөв ч гэсэн!). Хэрэв та үүнтэй тулгарвал өөр клиент туршаад үзээрэй эсвэл торрент клиент үйлдвэрлэгчдэд гомдол гаргаарай. Би туслахыг хүсэж байна, гэхдээ надад их хэмжээний дискний зай байхгүй. Торрентууд хэтэрхий удаан байна; би өгөгдлийг шууд татаж авч болох уу? Би зөвхөн тодорхой хэл эсвэл сэдэвтэй файлуудыг татаж авч болох уу? Торрентууд дахь давхардлыг хэрхэн зохицуулдаг вэ? Би торрент жагсаалтыг JSON хэлбэрээр авч болох уу? Би торрентууд дотор PDF эсвэл EPUB-уудыг харахгүй байна, зөвхөн бинар файлууд байна уу? Би яах ёстой вэ? Яагаад миний торрент клиент таны зарим торрент файлууд / соронзон холбоосуудыг нээж чадахгүй байна вэ? Торрентын Түгээмэл Асуултууд Би шинэ номыг хэрхэн оруулах вэ? <a %(a_href)s>Энэ гайхалтай төслийг</a> үзнэ үү. Танд uptime хянагч байгаа юу? Аннагийн Архив гэж юу вэ? Хурдан татан авалтыг ашиглахын тулд гишүүн болоорой. Бид одоо Amazon бэлгийн картууд, кредит болон дебит картууд, крипто, Alipay, WeChat-ийг дэмжиж байна. Та өнөөдөр хурдан татан авалтаа дуусгасан байна. Хүртээмж Сүүлийн 30 хоногийн цаг тутмын татан авалт. Цаг тутмын дундаж: %(hourly)s. Өдрийн дундаж: %(daily)s. Бид хамтрагчидтайгаа хамтран манай цуглуулгуудыг хэн бүхэнд хялбар, үнэгүй хүртээмжтэй болгодог. Бид хүн бүрт хүн төрөлхтний хамтын ухааныг хүртэх эрхтэй гэж үздэг. Мөн <a %(a_search)s>зохиолчдын зардлаар биш</a>. Аннагийн Архивт ашиглагддаг датасетууд бүрэн нээлттэй бөгөөд торрентоор бөөнөөр нь толин тусгалд хадгалах боломжтой. <a %(a_datasets)s>Дэлгэрэнгүй…</a> Урт хугацааны архив Бүрэн мэдээллийн сан Хайлт Ном, өгүүлэл, сэтгүүл, комикс, номын сангийн бүртгэл, мета өгөгдөл, … Манай бүх <a %(a_code)s>код</a> болон <a %(a_datasets)s>өгөгдөл</a> нь бүрэн нээлттэй эх сурвалжтай. <span %(span_anna)s>Аннагийн Архив</span> нь хоёр зорилготой ашгийн бус төсөл юм: <li><strong>Хадгалалт:</strong> Хүн төрөлхтний бүх мэдлэг, соёлыг нөөцлөх.</li><li><strong>Хүртээмж:</strong> Энэ мэдлэг, соёлыг дэлхийн хэн бүхэнд хүртээмжтэй болгох.</li> Бид дэлхийн хамгийн том өндөр чанартай текст өгөгдлийн цуглуулгатай. <a %(a_llm)s>Дэлгэрэнгүй…</a> LLM сургалтын өгөгдөл 🪩 Толин тусгалууд: сайн дурынхныг урьж байна Хэрэв та өндөр эрсдэлтэй нэргүй төлбөрийн процессор ажиллуулдаг бол бидэнтэй холбоо барина уу. Мөн жижиг зар сурталчилгаа байрлуулах сонирхолтой хүмүүсийг хайж байна. Бүх орлого манай хадгалалтын хүчин чармайлтад зориулагдана. Хадгалалт Бид дэлхийн номын <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% орчим хувийг хадгалсан гэж тооцоолж байна</a>. Бид ном, эрдэм шинжилгээний өгүүлэл, комикс, сэтгүүл болон бусад материалыг янз бүрийн <a href="https://en.wikipedia.org/wiki/Shadow_library">сүүдрийн номын сангууд</a>, албан ёсны номын сангууд болон бусад цуглуулгуудын материалаас нэг дор цуглуулж хадгалдаг. Энэ бүх өгөгдлийг олон хувьтай болгохын тулд — торрентоор дамжуулан — бөөнөөр нь хуулахад хялбар болгож, дэлхий даяар олон хувийг хадгалдаг. Зарим сүүдрийн номын сангууд үүнийг өөрсдөө хийдэг (жишээ нь, Sci-Hub, Library Genesis), харин Аннагийн Архив бөөнөөр тараах боломжгүй номын сангуудыг “чөлөөлдөг” (жишээ нь, Z-Library) эсвэл сүүдрийн номын сан биш (жишээ нь, Internet Archive, DuXiu). Энэ өргөн тархалт, нээлттэй эх сурвалжийн кодтой хослуулан манай вэбсайтыг устгахад тэсвэртэй болгож, хүн төрөлхтний мэдлэг, соёлыг урт хугацаанд хадгалах боломжийг олгодог. Манай <a href="/datasets">өгөгдлийн багцын</a> талаар дэлгэрэнгүй уншина уу. Хэрэв та <a %(a_member)s>гишүүн</a> бол хөтөчийн баталгаажуулалт шаардлагагүй. 🧬&nbsp;SciDB бол Sci-Hub-ийн үргэлжлэл юм. SciDB Нээх DOI Sci-Hub шинэ өгүүллүүдийг байршуулахыг <a %(a_paused)s>түр зогсоосон</a>. %(count)s академик өгүүлэлд шууд хандах 🧬&nbsp;SciDB бол Sci-Hub-ийн үргэлжлэл бөгөөд танил интерфэйс, PDF-ийг шууд үзэх боломжтой. Үзэхийн тулд DOI-оо оруулна уу. Бид Sci-Hub-ийн бүрэн цуглуулга болон шинэ өгүүллүүдийг агуулдаг. Ихэнхийг нь Sci-Hub-тэй төстэй танил интерфэйсээр шууд үзэх боломжтой. Заримыг нь гадаад эх сурвалжаас татаж авах боломжтой бөгөөд бид тэдгээрийн холбоосыг харуулдаг. Та торрентийг үргэлжлүүлэн татаж байснаар ихээхэн тус болж чадна. <a %(a_torrents)s>Дэлгэрэнгүй…</a> >%(count)s татагчид <%(count)s татагчид %(count_min)s–%(count_max)s татагчид 🤝 Сайн дурынхныг хайж байна Ашгийн бус, нээлттэй эхийн төсөл болохын хувьд бид үргэлж туслах хүмүүсийг хайж байдаг. IPFS татан авалт %(by)s жагсаалт, үүсгэсэн <span %(span_time)s>%(time)s</span> Хадгалах ❌ Алдаа гарлаа. Дахин оролдоно уу. ✅ Хадгалагдлаа. Хуудсыг дахин ачаална уу. Жагсаалт хоосон байна. засах Файл олоод “Жагсаалт” табыг нээж энэ жагсаалтад нэмэх эсвэл хасах боломжтой. Жагсаалт Бид хэрхэн тусалж чадах вэ Давхардлыг арилгах (дедупликаци) Текст болон мета өгөгдөл олборлолт OCR Бид өөрсдийн бүрэн цуглуулгад өндөр хурдтай хандах боломжийг олгохоос гадна гаргаагүй цуглуулгуудыг ч мөн олгох боломжтой. Энэ бол бид арван мянган ам.долларын хандивын хүрээнд олгож чадах аж ахуйн нэгжийн түвшний хандалт юм. Мөн бидэнд одоогоор байхгүй өндөр чанартай цуглуулгуудтай солилцох боломжтой. Бид таны өгөгдлийг баяжуулж өгч чадвал танд буцаан олгох боломжтой, үүнд: Хүний мэдлэгийг урт хугацаанд хадгалахыг дэмжиж, таны загварт илүү сайн өгөгдөл авах боломжтой! <a %(a_contact)s>Бидэнтэй холбоо бариарай</a> хамтран ажиллах талаар ярилцах. LLM-ууд өндөр чанартай өгөгдөл дээр амжилттай ажилладаг нь сайн ойлгогдсон зүйл. Бид дэлхийн хамгийн том ном, өгүүлэл, сэтгүүл гэх мэт цуглуулгатай бөгөөд эдгээр нь хамгийн өндөр чанартай текстийн эх үүсвэрүүдийн нэг юм. LLM өгөгдөл Өвөрмөц хэмжээ ба хүрээ Манай цуглуулга нь академик сэтгүүл, сурах бичиг, сэтгүүл гэх мэт зуун сая гаруй файлыг агуулдаг. Бид энэ хэмжээг томоохон одоо байгаа сангуудыг нэгтгэснээр олж авдаг. Манай зарим эх үүсвэрийн цуглуулгууд аль хэдийн бөөнөөрөө ашиглах боломжтой (Sci-Hub, болон Libgen-ийн хэсгүүд). Бусад эх үүсвэрүүдийг бид өөрсдөө чөлөөлсөн. <a %(a_datasets)s>Datasets</a> нь бүрэн тоймыг харуулна. Манай цуглуулга нь цахим номын эрин үеэс өмнөх сая сая ном, өгүүлэл, сэтгүүлүүдийг агуулдаг. Энэ цуглуулгын ихэнх хэсэг нь аль хэдийн OCR хийгдсэн бөгөөд дотоод давхардал бага байдаг. Үргэлжлүүлэх Хэрэв түлхүүрээ алдсан бол <a %(a_contact)s>бидэнтэй холбогдоно уу</a> болон аль болох их мэдээлэл өгнө үү. Бидэнтэй холбогдохын тулд түр хугацаанд шинэ бүртгэл үүсгэх шаардлагатай байж магадгүй. Энэ хуудсыг үзэхийн тулд <a %(a_account)s>нэвтрэх</a> шаардлагатай.</a> Спам-ботууд олон данс үүсгэхээс сэргийлэхийн тулд бид эхлээд таны хөтчийг баталгаажуулах хэрэгтэй. Хэрэв та хязгааргүй давталтад орвол, <a %(a_privacypass)s>Privacy Pass</a> суулгахыг зөвлөж байна. Мөн зар сурталчилгааны блоклогч болон бусад хөтөчийн өргөтгөлүүдийг унтраах нь тусалж магадгүй. Нэвтрэх / Бүртгүүлэх Аннагийн Архив түр хугацаагаар засвар үйлчилгээ хийгдэж байна. Нэг цагийн дараа дахин орж ирнэ үү. Өөр зохиогч Өөр тайлбар Өөр хэвлэл Өөр өргөтгөл Өөр файл нэр Өөр хэвлэн нийтлэгч Өөр нэр нээлттэй эх сурвалж болсон огноо Дэлгэрэнгүй унших… тайлбар CADAL SSNO дугаараар Anna’s Archive-ээс хайх DuXiu SSID дугаараар Anna’s Archive-ээс хайх DuXiu DXID дугаараар Anna’s Archive-ээс хайх ISBN-ээр Anna’s Archive-ээс хайх OCLC (WorldCat) дугаараар Anna’s Archive-ээс хайх Open Library ID-ээр Anna’s Archive-ээс хайх Аннагийн Архив онлайн үзэгч %(count)s нөлөөлсөн хуудаснууд Татаж авсны дараа: Энэ файлын илүү сайн хувилбар %(link)s дээр байж магадгүй Торрент файлуудыг бөөнөөр татах цуглуулга Форматуудын хооронд хөрвүүлэхийн тулд онлайн хэрэгслүүдийг ашиглаарай. Зөвлөмж болгож буй хөрвүүлэх хэрэгслүүд: %(links)s Том файлуудын хувьд, тасалдалгүй татаж авахын тулд татаж авах менежер ашиглахыг зөвлөж байна. Зөвлөмж болгож буй татаж авах менежерүүд: %(links)s EBSCOhost eBook Index (зөвхөн мэргэжилтнүүдэд) (мөн дээрх “АВАХ” товчийг дарна уу) (дээрх “АВАХ” товчийг дарна уу) Гадаад татан авалт <strong>🚀 Хурдан татаж авах</strong> Та өнөөдөр %(remaining)s үлдсэн байна. Гишүүн болсонд баярлалаа! ❤️ <strong>🚀 Хурдан татаж авах</strong> Та өнөөдөр хурдан татаж авах эрхээ дуусгасан байна. <strong>🚀 Хурдан татаж авах</strong> Та энэ файлыг саяхан татаж авсан байна. Холбоосууд хэсэг хугацаанд хүчинтэй хэвээр байна. <strong>🚀 Хурдан татаж авах</strong> Ном, өгүүлэл болон бусад зүйлсийг урт хугацаанд хадгалахад дэмжлэг үзүүлэхийн тулд <a %(a_membership)s>гишүүн</a> болоорой. Таны дэмжлэгт талархал илэрхийлэхийн тулд бид танд хурдан татаж авах боломжийг олгоно. ❤️ 🚀 Хурдан татах 🐢 Удаан татан авалт Интернет Архиваас зээлэх IPFS Gateway #%(num)d (IPFS-ээр олон удаа оролдох шаардлагатай байж магадгүй) Libgen.li Libgen.rs Уран зохиол Libgen.rs Шинжлэх Ухаан Бус тэдний зар сурталчилгаанд хортой програм хангамж агуулагдсан байж болзошгүй тул зар сурталчилгааг хаах програм ашиглах эсвэл зар сурталчилгаан дээр дарахгүй байхыг зөвлөж байна Amazon-ийн “Send to Kindle” djazz-ийн “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC файлуудыг татаж авахад найдвартай биш байж болно) Татан авалт олдсонгүй. Бүх татан авалтын сонголтууд ижил файлыг агуулдаг бөгөөд ашиглахад аюулгүй байх ёстой. Гэсэн хэдий ч, интернетээс файлууд татаж авахдаа, ялангуяа Anna’s Archive-ээс гадуурх сайтуудаас татаж авахдаа үргэлж болгоомжтой байгаарай. Жишээлбэл, төхөөрөмжүүдээ шинэчилж байгаарай. (шилжүүлэлтгүй) Манай үзэгчээр нээх (үзэгчээр нээх) Сонголт #%(num)d: %(link)s %(extra)s CADAL-д анхны бичлэгийг олох DuXiu дээр гараар хайх ISBNdb-д анхны бичлэгийг олох WorldCat-д анхны бичлэгийг олох Open Library-д анхны бичлэгийг олох ISBN-ээр бусад олон мэдээллийн сангаас хайх (зөвхөн хэвлэх боломжгүй хэрэглэгчдэд) PubMed Файлын форматаас хамааран файлыг нээхийн тулд цахим ном эсвэл PDF уншигч хэрэгтэй болно. Зөвлөмж болгож буй цахим ном уншигчид: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (холбогдох DOI Sci-Hub-д байхгүй байж магадгүй) Та PDF болон EPUB файлуудыг Kindle эсвэл Kobo цахим уншигч руу илгээж болно. Зөвлөмж болгож буй хэрэгслүүд: %(links)s Дэлгэрэнгүй мэдээллийг <a %(a_slow)s>Түгээмэл асуултууд</a> хэсгээс үзнэ үү. Зохиолчид болон номын санг дэмжээрэй Хэрэв танд энэ таалагдаж, боломжтой бол эх хувийг нь худалдан авах эсвэл зохиолчдыг шууд дэмжих талаар бодоорой. Хэрэв энэ нь таны орон нутгийн номын санд байгаа бол тэндээс үнэгүй зээлж авах талаар бодоорой. Түнш серверийн татан авалт энэ файлд түр хугацаанд боломжгүй байна. торрент Итгэмжлэгдсэн түншүүдээс. Z-Library Z-Номын сан Tor дээр (Tor хөтөч шаардлагатай) гадны таталтуудыг харуулах <span class="font-bold">❌ Энэ файлд асуудал байж магадгүй бөгөөд эх сурвалжийн номын сангаас нуусан байна.</span> Заримдаа энэ нь зохиогчийн эрх эзэмшигчийн хүсэлтээр, заримдаа илүү сайн хувилбар байгаа учраас, заримдаа файлд өөрт нь асуудал байгаа учраас болдог. Татаж авахад зүгээр байж магадгүй ч, эхлээд өөр хувилбарыг хайхыг зөвлөж байна. Дэлгэрэнгүй мэдээлэл: Хэрэв та энэ файлыг татаж авахыг хүсэж байвал, зөвхөн итгэмжлэгдсэн, шинэчлэгдсэн програм хангамжийг ашиглан нээхийг анхаарна уу. мета өгөгдлийн тайлбар AA: “Аннагийн Архив”-аас “%(name)s” хайх Кодын Судлаач: Кодын Судлаач дахь “%(name)s”-г үзэх URL: Вэбсайт: Хэрэв танд энэ файл байгаа бөгөөд Anna’s Archive-д хараахан байхгүй бол <a %(a_request)s>байршуулахыг</a> бодоорой. Internet Archive Хянагдсан Дижитал Зээлдүүлэх файл “%(id)s” Энэ бол Internet Archive-аас авсан файлын бүртгэл бөгөөд шууд татаж авах файл биш юм. Та номыг зээлж үзэхийг оролдож болно (доорх холбоос), эсвэл энэ URL-г ашиглан <a %(a_request)s>файл хүсэх</a> үедээ ашиглаж болно. Мета өгөгдлийг сайжруулах CADAL SSNO %(id)s мета өгөгдлийн бичлэг Энэ бол мета өгөгдлийн бичлэг бөгөөд татаж авах файл биш юм. Та энэ URL-ийг <a %(a_request)s>файл хүсэхдээ</a> ашиглаж болно. DuXiu SSID %(id)s мета өгөгдлийн бичлэг ISBNdb %(id)s мета өгөгдлийн бүртгэл MagzDB ID %(id)s метадата бичлэг Nexus/STC ID %(id)s метадата бичлэг OCLC (WorldCat) дугаар %(id)s мета өгөгдлийн бичлэг Open Library %(id)s мета өгөгдлийн бүртгэл Sci-Hub файл “%(id)s” Олдсонгүй “%(md5_input)s” манай мэдээллийн санд олдсонгүй. Сэтгэгдэл нэмэх (%(count)s) Та URL-ээс md5-г авах боломжтой, жишээ нь Энэ файлын илүү сайн хувилбарын MD5 (хэрэв боломжтой бол). Хэрэв энэ файлтай ойролцоо тохирох өөр файл (нэг хэвлэл, нэг файл өргөтгөлтэй) байгаа бол, хүмүүс энэ файлыг оронд нь ашиглах ёстой. Хэрэв та Anna’s Archive-аас гадуур энэ файлын илүү сайн хувилбарыг мэдэж байгаа бол, <a %(a_upload)s>үүнийг оруулна уу</a>. Юу ч буруу боллоо. Хуудсыг дахин ачаалж, дахин оролдоно уу. Та сэтгэгдэл үлдээсэн. Энэ нь харагдахад нэг минут зарцуулагдаж магадгүй. <a %(a_copyright)s>DMCA / Зохиогчийн эрхийн нэхэмжлэлийн маягт</a>-ыг ашиглана уу. Асуудлыг тодорхойлох (шаардлагатай) Хэрэв энэ файл маш сайн чанартай бол та энд юу ч ярилцаж болно! Хэрэв тийм биш бол “Файлын асуудлыг мэдээлэх” товчийг ашиглана уу. Сайн файлын чанар (%(count)s) Файлын чанар Энэ файлын метадата-г өөрөө хэрхэн <a %(a_metadata)s>сайжруулах</a> талаар суралцаарай. Асуудлын тодорхойлолт <a %(a_login)s>Нэвтрэх</a> шаардлагатай. Надад энэ ном маш их таалагдсан! Энэ файлын чанарыг мэдээлж, олон нийтэд туслаарай! 🙌 Юу ч буруу боллоо. Хуудсыг дахин ачаалж, дахин оролдоно уу. Файлын асуудлыг мэдээлэх (%(count)s) Таны илгээсэн тайланг хүлээн авлаа. Энэ хуудсанд харагдах бөгөөд Анна гар аргаар хянах болно (зохих хяналтын системтэй болтол). Сэтгэгдэл үлдээх Тайланг илгээх Энэ файлд юу буруу байна вэ? Зээлэх (%(count)s) Сэтгэгдлүүд (%(count)s) Татаж авах (%(count)s) Мета өгөгдлийг судлах (%(count)s) Жагсаалтууд (%(count)s) Статистик (%(count)s) Энэ тодорхой файлын талаар мэдээлэл авахыг хүсвэл түүний <a %(a_href)s>JSON файл</a>-ыг үзнэ үү. Энэ бол <a %(a_ia)s>IA-ийн Хяналттай Дижитал Зээлдүүлгийн</a> номын сангаар удирдагддаг файл бөгөөд хайлтад зориулж Аннагийн Архиваар индексэлсэн. Бидний цуглуулсан янз бүрийн datasets-ийн талаар мэдээлэл авахыг хүсвэл <a %(a_datasets)s>Datasets хуудас</a>-ыг үзнэ үү. Холбогдсон бичлэгийн мета өгөгдөл Open Library дээрх мета өгөгдлийг сайжруулах “Файлын MD5” гэдэг нь файлын агуулгаас тооцоолсон хэш бөгөөд тухайн агуулгад үндэслэн харьцангуй өвөрмөц байдаг. Бидний энд индексэлсэн бүх сүүдрийн номын сангууд голчлон MD5-ийг файлуудыг танихад ашигладаг. Файл олон сүүдрийн номын санд гарч ирж магадгүй. Бидний цуглуулсан янз бүрийн datasets-ийн талаар мэдээлэл авахыг хүсвэл <a %(a_datasets)s>Datasets хуудас</a>-ыг үзнэ үү. Файлын чанарыг мэдээлэх Нийт таталт: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Чех мета өгөгдөл %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Трантор %(id)s} Анхааруулга: олон холбогдсон бичлэгүүд: Аннагийн Архив дээр ном харахад та янз бүрийн талбаруудыг харж болно: гарчиг, зохиогч, хэвлэгч, хэвлэл, он, тайлбар, файлын нэр гэх мэт. Эдгээр бүх мэдээллийг <em>мета өгөгдөл</em> гэж нэрлэдэг. Бид номыг янз бүрийн <em>эх сурвалжийн номын сангаас</em> нэгтгэдэг тул тухайн эх сурвалжийн номын санд байгаа мета өгөгдлийг харуулдаг. Жишээлбэл, Library Genesis-ээс авсан номын хувьд бид Library Genesis-ийн мэдээллийн сангаас гарчгийг харуулна. Заримдаа ном <em>олон</em> эх сурвалжийн номын санд байдаг бөгөөд эдгээр нь өөр өөр мета өгөгдлийн талбаруудтай байж болно. Энэ тохиолдолд бид хамгийн урт хувилбарыг харуулдаг, учир нь энэ нь хамгийн их хэрэгтэй мэдээллийг агуулсан байх магадлалтай! Бид бусад талбаруудыг тайлбарын доор харуулна, жишээлбэл, "өөр гарчиг" гэж (гэхдээ зөвхөн өөр байвал). Бид мөн эх сурвалжийн номын сангаас <em>код</em> болох танигч болон ангилагчдыг гаргаж авдаг. <em>Танигчид</em> нь номын тодорхой хэвлэлийг өвөрмөцөөр илэрхийлдэг; жишээ нь ISBN, DOI, Open Library ID, Google Books ID, эсвэл Amazon ID. <em>Ангилагчид</em> нь ижил төстэй олон номыг бүлэглэдэг; жишээ нь Dewey Decimal (DCC), UDC, LCC, RVK, эсвэл GOST. Эдгээр кодыг заримдаа эх сурвалжийн номын санд шууд холбодог бөгөөд заримдаа бид файлын нэр эсвэл тайлбараас гаргаж авдаг (голчлон ISBN болон DOI). Бид танигчдыг ашиглан <em>зөвхөн мета өгөгдлийн цуглуулгуудын</em> бүртгэлийг олох боломжтой, жишээлбэл OpenLibrary, ISBNdb, эсвэл WorldCat/OCLC. Манай хайлтын системд эдгээр цуглуулгуудыг үзэх тусгай <em>мета өгөгдлийн таб</em> байдаг. Бид тохирох бүртгэлийг ашиглан дутуу мета өгөгдлийн талбаруудыг бөглөдөг (жишээлбэл, гарчиг дутуу байвал), эсвэл жишээлбэл "өөр гарчиг" гэж (хэрэв байгаа гарчиг байвал). Номын мета өгөгдөл хаанаас ирснийг яг таг харахын тулд номын хуудасны <em>“Техникийн дэлгэрэнгүй” таб</em>-ыг үзнэ үү. Энэ нь тухайн номын түүхий JSON руу холбосон холбоосыг агуулдаг бөгөөд анхны бүртгэлийн түүхий JSON руу заадаг. Дэлгэрэнгүй мэдээллийг дараах хуудсуудаас үзнэ үү: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a>, болон <a %(a_example)s>Example metadata JSON</a>. Эцэст нь, манай бүх мета өгөгдлийг <a %(a_generated)s>генератордсон</a> эсвэл <a %(a_downloaded)s>татаж авах боломжтой</a> ElasticSearch болон MariaDB мэдээллийн сангууд болгон. Үндсэн мэдээлэл Та мета өгөгдлийг сайжруулснаар номын хадгалалтыг дэмжиж чадна! Эхлээд Аннагийн Архив дээрх мета өгөгдлийн талаархи мэдээллийг уншаад, Open Library-тэй холбох замаар мета өгөгдлийг хэрхэн сайжруулахыг сурч, Аннагийн Архив дээр үнэгүй гишүүнчлэл авах боломжтой. Мета өгөгдлийг сайжруулах Тэгэхээр хэрэв та муу мета өгөгдөлтэй файлтай таарвал яах вэ? Та эх сурвалжийн номын санд очиж мета өгөгдлийг засах журмыг дагаж болно, гэхдээ хэрэв файл олон эх сурвалжийн номын санд байвал яах вэ? Аннагийн Архив дээр нэг танигчийг онцгойлон авч үздэг. <strong>Open Library дээрх annas_archive md5 талбар нь бусад бүх мета өгөгдлийг үргэлж давж гардаг!</strong> Эхлээд Open Library-ийн талаар бага зэрэг мэдээлэл авцгаая. Open Library-г 2006 онд Аарон Шварц үүсгэн байгуулсан бөгөөд зорилго нь “хэвлэгдсэн бүх номын нэг вэб хуудас” байв. Энэ нь номын мета өгөгдлийн Wikipedia шиг: хүн бүр засварлах боломжтой, чөлөөтэй лицензтэй, бөөнөөр татаж авах боломжтой. Энэ нь манай эрхэм зорилготой хамгийн их нийцдэг номын мэдээллийн сан юм — үнэндээ Аннагийн Архив нь Аарон Шварцийн алсын хараа, амьдралаас урам зориг авсан. Дугуйг дахин зохион бүтээхийн оронд бид сайн дурын ажилтнуудаа Open Library руу чиглүүлэхээр шийдсэн. Хэрэв та буруу мета өгөгдөлтэй ном харвал дараах байдлаар туслах боломжтой: Энэ нь зөвхөн номонд л хамаарна, академик өгүүлэл эсвэл бусад төрлийн файлуудад хамаарахгүй. Бусад төрлийн файлуудад эх сурвалжийн номын санг олохыг зөвлөж байна. Anna’s Archive-д өөрчлөлтүүдийг оруулахад хэдэн долоо хоног шаардагдаж магадгүй, учир нь бид Open Library-ийн хамгийн сүүлийн үеийн өгөгдлийн санг татаж авах, хайлтын индексээ дахин үүсгэх шаардлагатай.  <a %(a_openlib)s>Open Library вэбсайт</a> руу очно уу. Зөв номын бүртгэлийг олоорой. <strong>АНХААРУУЛГА:</strong> зөв <strong>хэвлэл</strong>-ийг сонгохоо мартуузай. Open Library-д “бүтээлүүд” болон “хэвлэлүүд” гэж байдаг. “Бүтээл” нь “Харри Поттер ба Шидтэний Чулуу” байж болно. “Хэвлэл” нь: 1997 онд Bloomsbery-аас хэвлэгдсэн анхны хэвлэл, 256 хуудастай. 2003 онд Raincoast Books-аас хэвлэгдсэн зөөлөн хавтастай хэвлэл, 223 хуудастай. 2000 онд Media Rodzina-аас хэвлэгдсэн Польш орчуулга “Harry Potter I Kamie Filozoficzn”, 328 хуудастай. Эдгээр бүх хэвлэлүүд өөр өөр ISBN болон агуулгатай тул зөвийг нь сонгоорой! Бичлэгийг засах (эсвэл байхгүй бол шинээр үүсгэх) ба аль болох их хэрэгтэй мэдээллийг нэмээрэй! Та одоо энд байгаа тул бичлэгийг үнэхээр гайхалтай болгох хэрэгтэй. “ID Numbers” хэсэгт “Anna’s Archive”-ийг сонгоод, Anna’s Archive-аас номын MD5-ийг нэмээрэй. Энэ нь URL дахь “/md5/” дараах урт үсэг, тооны цуваа юм. Anna’s Archive-д энэ бичлэгтэй таарах бусад файлуудыг олохыг хичээгээрэй, мөн тэдгээрийг нэмээрэй. Ирээдүйд бид эдгээрийг Anna’s Archive хайлтын хуудсанд давхардсан байдлаар бүлэглэх боломжтой болно. Дууссаны дараа, та саяхан шинэчилсэн URL-ийг бичээрэй. Anna’s Archive MD5-үүдтэй дор хаяж 30 бичлэгийг шинэчилсний дараа бидэнд <a %(a_contact)s>имэйл</a> илгээгээрэй, мөн жагсаалтыг илгээгээрэй. Бид танд Anna’s Archive-ийн үнэгүй гишүүнчлэлийг өгөх болно, ингэснээр та энэ ажлыг илүү хялбар хийх боломжтой болно (мөн таны тусламжид талархал илэрхийлэх болно). Эдгээр нь их хэмжээний мэдээлэл нэмсэн өндөр чанартай засварууд байх ёстой, эс тэгвээс таны хүсэлтийг хүлээн авахгүй. Хэрэв засваруудыг Open Library-ийн модераторууд буцаах эсвэл засварлах тохиолдолд таны хүсэлтийг мөн хүлээн авахгүй. Open Library холболт Хэрэв та манай ажлын хөгжүүлэлт болон үйл ажиллагаанд ихээхэн оролцвол бид танд шаардлагатай үед ашиглах хандивын орлогыг хуваалцах талаар ярилцаж болно. Бид зөвхөн бүх зүйлийг тохируулж, архивыг шинэчлэлтээр шинэчилж чадахыг харуулсны дараа хостингийн төлбөрийг төлнө. Энэ нь эхний 1-2 сарын зардлыг өөрөөсөө гаргах шаардлагатай гэсэн үг. Таны цаг хугацаа нөхөн төлөгдөхгүй (манайх ч мөн адил), учир нь энэ нь цэвэр сайн дурын ажил юм. Бид хостинг болон VPN зардлыг эхэндээ сард $200 хүртэл хариуцахад бэлэн байна. Энэ нь үндсэн хайлтын сервер болон DMCA-аас хамгаалагдсан прокси серверт хангалттай. Хостингийн зардал Биднээс зөвшөөрөл хүсэх, эсвэл үндсэн асуултууд асуух гэж <strong>бидэнтэй холбоо барих</strong> хэрэггүй. Үйлдэл үгнээс илүү хүчтэй! Бүх мэдээлэл тэнд байгаа тул толин тусгалаа тохируулах ажлаа эхлүүлээрэй. Асуудал тулгарвал манай Gitlab дээр тасалбар эсвэл нэгтгэх хүсэлт оруулахад чөлөөтэй байна уу. Бид тантай хамтран ажиллаж, “Аннагийн Архив”-аас таны вэбсайтын нэр рүү дахин брэндлэх, (анхандаа) хэрэглэгчийн дансыг идэвхгүй болгох, эсвэл номын хуудсуудаас манай үндсэн сайт руу холбоос хийх зэрэг толин тусгалд зориулсан зарим онцлогийг бүтээх шаардлагатай байж магадгүй. Толин тусгалаа ажиллуулсны дараа бидэнтэй холбоо барина уу. Бид таны OpSec-ийг хянах дуртай бөгөөд бат бөх болсны дараа бид таны толин тусгалыг холбоно, мөн тантай илүү ойр хамтран ажиллаж эхэлнэ. Энэ байдлаар хувь нэмэр оруулахыг хүссэн хэн бүхэнд урьдчилан талархал илэрхийлье! Энэ нь зүрх сэтгэлгүй хүмүүст зориулагдаагүй боловч хүн төрөлхтний түүхэн дэх хамгийн том үнэхээр нээлттэй номын сангийн урт хугацааны тогтвортой байдлыг баталгаажуулах болно. Эхлэх Anna’s Archive-ийн тогтвортой байдлыг нэмэгдүүлэхийн тулд бид толь ажиллуулах сайн дурынхныг хайж байна. Таны хувилбар нь тодорхой толин тусгал гэж ялгагдана, жишээ нь “Bob’s Archive, Anna’s Archive толин тусгал”. Та энэ ажлын холбоотой эрсдлийг хүлээн зөвшөөрөхөд бэлэн байна, эдгээр нь ихээхэн эрсдэлтэй. Та үйл ажиллагааны аюулгүй байдлын талаар гүнзгий ойлголттой. <a %(a_shadow)s>Эдгээр</a> <a %(a_pirate)s>бичлэгүүдийн</a> агуулга танд ойлгомжтой. Эхэндээ бид танд түнш серверийн татан авалтад хандах эрх өгөхгүй, гэхдээ хэрэв бүх зүйл сайн явбал бид үүнийг тантай хуваалцаж болно. Та Anna’s Archive нээлттэй эхийн кодын санг ажиллуулж, код болон өгөгдлийг тогтмол шинэчилдэг. Та манай <a %(a_codebase)s>кодын сан</a>-д хамтран ажиллахад бэлэн байна — манай багтай хамтран ажиллах замаар — үүнийг хэрэгжүүлэхийн тулд. Бид үүнийг хайж байна: Толь: сайн дурынхныг уриалж байна Өөр хандив хийх. Одоогоор хандив байхгүй байна. <a %(a_donate)s>Эхний хандивыг хийх.</a> Хандивын дэлгэрэнгүй мэдээлэл олон нийтэд харагдахгүй. Миний хандивууд 📡 Манай цуглуулгыг бөөнөөр нь толин тусгал болгохын тулд <a %(a_datasets)s>Datasets</a> болон <a %(a_torrents)s>Torrents</a> хуудсуудыг үзнэ үү. Сүүлийн 24 цагийн дотор таны IP хаягаас хийгдсэн таталт: %(count)s. 🚀 Хурдан татан авалт хийх болон хөтөчийн шалгалтыг алгасахын тулд <a %(a_membership)s>гишүүн болоорой</a>. Түнш сайтынхаас татаж авах Хүлээж байхдаа өөр таб дээр Аннагийн Архивыг үргэлжлүүлэн үзэж болно (хэрэв таны хөтөч арын табуудыг шинэчлэхийг дэмждэг бол). Олон таталтын хуудсуудыг нэгэн зэрэг ачааллахыг хүлээж болно (гэхдээ нэг серверээс нэг файл татаж авахыг зөвшөөрнө үү). Таталтын холбоосыг авсны дараа хэдэн цагийн турш хүчинтэй байна. Хүлээсэнд баярлалаа, энэ нь вэбсайтыг бүх хүнд үнэгүй ашиглах боломжтой болгодог! 😊 🔗 Энэ файлын бүх татах холбоосууд: <a %(a_main)s>Файлын үндсэн хуудас</a>. ❌ Удаан таталт Cloudflare VPN-ээр эсвэл Cloudflare IP хаягуудаас боломжгүй. ❌ Удаан таталт зөвхөн албан ёсны вэбсайтаар дамжуулан боломжтой. %(websites)s хаягаар зочлоорой. 📚 Татахын тулд дараах URL-ийг ашиглана уу: <a %(a_download)s>Одоо татаж авах</a>. Бүх хүнд файлуудыг үнэгүй татаж авах боломж олгохын тулд та энэ файлыг татаж авахын өмнө хүлээх шаардлагатай. Татаж авахын тулд <span %(span_countdown)s>%(wait_seconds)s</span> секунд хүлээнэ үү. Анхааруулга: Сүүлийн 24 цагийн дотор таны IP хаягаас олон таталт хийгдсэн байна. Таталт ердийнхөөс удаан байж магадгүй. Хэрэв та VPN, хуваалцсан интернет холболт ашиглаж байгаа эсвэл таны ISP IP хаягуудыг хуваалцдаг бол энэ анхааруулга үүнтэй холбоотой байж магадгүй. Хадгалах ❌ Алдаа гарлаа. Дахин оролдоно уу. ✅ Хадгалагдлаа. Хуудас дахин ачааллана уу. Өөрийн дэлгэцийн нэрийг өөрчилнө үү. Таны танигч (”#” тэмдгийн дараах хэсэг) өөрчлөгдөхгүй. Профайл үүсгэгдсэн <span %(span_time)s>%(time)s</span> засах Жагсаалтууд Файл олоод “Жагсаалтууд” табыг нээж шинэ жагсаалт үүсгээрэй. Одоогоор жагсаалт алга Профайл олдсонгүй. Профайл Одоогоор бид номын хүсэлтийг хүлээн авах боломжгүй байна. Номын хүсэлтээ бидэнд имэйлээр илгээхгүй байхыг хүсье. Та хүсэлтээ Z-Library эсвэл Libgen форумд оруулна уу. Аннагийн Архив дахь бичлэг DOI: %(doi)s Татах SciDB Nexus/STC Одоогоор урьдчилан харах боломжгүй байна. Файлыг <a %(a_path)s>Аннагийн Архив</a>-аас татаж авна уу. Хүний мэдлэгийг хүртээмжтэй, урт хугацаанд хадгалахыг дэмжихийн тулд <a %(a_donate)s>гишүүн</a> болоорой. Бонус болгон, 🧬&nbsp;SciDB гишүүдэд хурдан ачааллагдаж, ямар ч хязгааргүй. Ажиллахгүй байна уу? <a %(a_refresh)s>шинэчлэх</a> гэж оролдоорой. Sci-Hub Тусгай хайлтын талбар нэмэх Тайлбар болон мета өгөгдлийн тайлбаруудыг хайх Хэвлэгдсэн он Нарийвчилсан Хандалт Агуулга Дэлгэц Жагсаалт Хүснэгт Файлын төрөл Хэл Эрэмбэлэх Хамгийн том Хамгийн хамааралтай Хамгийн шинэ (файлын хэмжээ) (нээлттэй эх сурвалж) (хэвлэгдсэн он) Хамгийн хуучин Санамсаргүй Хамгийн жижиг Эх сурвалж AA-аас хусаж, нээлттэй эх болгосон Цахим зээлдүүлэг (%(count)s) Сэтгүүлийн өгүүллүүд (%(count)s) Бид дараах зүйлсэд тохирох үр дүнг олсон: %(in)s. <a %(a_request)s>Файл хүсэхдээ</a> тэндээс олдсон URL-ийг ашиглаж болно. Мета өгөгдөл (%(count)s) Кодуудаар хайлтын индексийг судлахын тулд <a %(a_href)s>Кодуудын Судлаач</a>-ийг ашиглаарай. Хайлтын индекс сар бүр шинэчлэгддэг. Одоогоор %(last_data_refresh_date)s хүртэлх бичлэгүүдийг агуулж байна. Илүү техникийн мэдээллийг %(link_open_tag)sмэдээллийн багцын хуудас</a>-аас үзнэ үү. Хасах Зөвхөн оруулах Шалгаагүй илүү… Дараагийнх … Өмнөх Энэ хайлтын индекс одоогоор Internet Archive-ийн Хяналттай Цахим Зээлдүүлэх номын сангийн мета өгөгдлийг агуулж байна. <a %(a_datasets)s>Манай мэдээллийн багцын талаар дэлгэрэнгүй</a>. Илүү олон цахим зээлдүүлэх номын сангуудыг <a %(a_wikipedia)s>Wikipedia</a> болон <a %(a_mobileread)s>MobileRead Wiki</a>-аас үзнэ үү. DMCA / зохиогчийн эрхийн нэхэмжлэлийн талаар <a %(a_copyright)s>энд дарна уу</a>. Татах хугацаа Хайлт хийх явцад алдаа гарлаа. <a %(a_reload)s>Хуудас дахин ачаалахыг</a> оролдоно уу. Хэрэв асуудал үргэлжилсээр байвал бидэнд %(email)s хаягаар имэйл илгээнэ үү. Хурдан татах Үнэндээ, хэн ч эдгээр файлуудыг хадгалахад туслахын тулд манай <a %(a_torrents)s>нэгдсэн торрент жагсаалтыг</a> үргэлжлүүлэн татаж болно. ➡️ Заримдаа хайлтын сервер удаан байхад энэ буруу ажилладаг. Ийм тохиолдолд <a %(a_attrs)s>дахин ачаалах</a> нь тусалж магадгүй. ❌ Энэ файлд асуудал байж магадгүй. Өгүүлэл хайж байна уу? Энэ хайлтын индекс одоогоор янз бүрийн мета өгөгдлийн эх сурвалжуудаас мета өгөгдлийг агуулж байна. <a %(a_datasets)s>Манай мэдээллийн багцын талаар дэлгэрэнгүй</a>. Дэлхий даяар бичгийн бүтээлүүдийн мета өгөгдлийн олон, олон эх сурвалжууд байдаг. <a %(a_wikipedia)s>Энэ Википедиа хуудас</a> нь сайн эхлэл боловч, хэрэв та бусад сайн жагсаалтуудыг мэдэж байвал бидэнд мэдэгдээрэй. Бид мета өгөгдлийн эх бичвэрүүдийг харуулдаг. Бичлэгүүдийг нэгтгэхгүй. Бид одоогоор дэлхийн хамгийн өргөн хүрээтэй нээлттэй ном, өгүүлэл болон бусад бичгийн ажлын каталогийг эзэмшиж байна. Бид Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>болон бусад</a>-ыг толин тусгалд оруулдаг. <span class="font-bold">Файл олдсонгүй.</span> Хайлт хийх үгс болон шүүлтүүрүүдийг багасгаж эсвэл өөрчилж оролдоно уу. Үр дүн %(from)s-%(to)s (%(total)s нийт) Хэрэв та бидний толин тусгалд оруулах ёстой өөр “сүүдрийн номын сангууд”-ыг олсон бол, эсвэл асуулт байвал %(email)s хаягаар бидэнтэй холбоо барина уу. %(num)d хэсэгчилсэн тохирол %(num)d+ хэсэгчилсэн тохирол Цахим зээлдүүлэх номын сангуудаас файлуудыг хайхын тулд хайрцагт бичнэ үү. Манай %(count)s шууд татаж авах боломжтой файлуудын каталогоос хайлт хийхийн тулд хайрцагт бичнэ үү, бид эдгээрийг <a %(a_preserve)s>мөнхөд хадгална</a>. Хайлт хийхийн тулд хайрцагт бичнэ үү. Манай %(count)s академик өгүүлэл болон сэтгүүлийн нийтлэлүүдийн каталогоос хайлт хийхийн тулд хайрцагт бичнэ үү, бид эдгээрийг <a %(a_preserve)s>мөнхөд хадгална</a>. Номын сангийн мета өгөгдлийг хайхын тулд хайрцагт бичнэ үү. Энэ нь <a %(a_request)s>файл хүсэх</a> үед хэрэгтэй байж болно. Зөвлөгөө: хурдан навигац хийхийн тулд “/” (хайлтанд анхаарлаа хандуулах), “enter” (хайлт), “j” (дээш), “k” (доош), “<” (өмнөх хуудас), “>” (дараагийн хуудас) товчлууруудыг ашиглаарай. Эдгээр нь мета өгөгдлийн бичлэгүүд бөгөөд <span %(classname)s>татаж авах файлууд биш</span>. Хайх тохиргоо Хайх Цахим зээлдүүлэг Татах Сэтгүүлийн өгүүллүүд Мета өгөгдөл Шинэ хайлт %(search_input)s - Хайлт Хайлт хэтэрхий удаан үргэлжилсэн тул үр дүн нь үнэн зөв биш байж магадгүй. Заримдаа хуудсыг <a %(a_reload)s>дахин ачаалах</a> нь тусалдаг. Хайлт хэтэрхий удаан үргэлжилсэн тул өргөн хүрээний асуултуудад нийтлэг тохиолддог. Шүүлтүүрийн тоо үнэн зөв байж магадгүй. Libgen эсвэл Z-Library-д хүлээн авагдаагүй 10,000-аас дээш файлын томоохон оруулгуудын хувьд %(a_email)s хаягаар бидэнтэй холбоо барина уу. Libgen.li-д зориулж, эхлээд <a %(a_forum)s >тэдний форумд</a> хэрэглэгчийн нэр %(username)s болон нууц үг %(password)s ашиглан нэвтэрч, дараа нь тэдний <a %(a_upload_page)s >оруулгын хуудас</a> руу буцаж очно уу. Одоогоор бид Library Genesis-ийн салаа руу шинэ ном оруулахыг зөвлөж байна. Энд <a %(a_guide)s>туслах гарын авлага</a> байна. Энэ вэбсайт дээр индексжүүлсэн хоёр салаа энэ ижил оруулах системээс татдаг гэдгийг анхаарна уу. Жижиг хэмжээний оруулгуудын хувьд (10,000 файл хүртэл) тэдгээрийг %(first)s болон %(second)s руу хоёуланд нь оруулна уу. Эсвэл та тэдгээрийг Z-Library руу <a %(a_upload)s>энд</a> оруулж болно. Академик өгүүллүүдийг оруулахын тулд, Library Genesis-ээс гадна <a %(a_stc_nexus)s>STC Nexus</a> руу оруулна уу. Тэд шинэ өгүүллүүдийн хамгийн сайн сүүдрийн номын сан юм. Бид тэднийг хараахан нэгтгээгүй байгаа ч, удахгүй нэгтгэнэ. Та тэдний <a %(a_telegram)s>Telegram дээрх оруулах бот</a>-ыг ашиглаж болно, эсвэл хэт олон файлыг энэ аргаар оруулах бол тэдний зүүсэн зурваст байгаа хаягаар холбоо барина уу. <span %(label)s>Идэвхтэй сайн дурын ажил (50-5,000 ам.долларын шагнал):</span> хэрэв та манай зорилгод ихээхэн цаг хугацаа болон/эсвэл нөөцөө зориулах боломжтой бол бид тантай илүү ойр хамтран ажиллахыг хүсэж байна. Эцэст нь та дотоод багт нэгдэх боломжтой. Бидний төсөв хязгаарлагдмал ч гэсэн хамгийн их ачаалалтай ажилд <span %(bold)s>💰 мөнгөн шагнал</span> олгох боломжтой. <span %(label)s>Хөнгөн сайн дурын ажил:</span> хэрэв та энд тэнд хэдэн цаг зарцуулах боломжтой бол туслах олон арга зам бий. Бид тогтмол сайн дурын ажилтнуудад <span %(bold)s>🤝 Аннагийн Архивын гишүүнчлэлээр шагнана</span>. Аннагийн Архив таны мэтийн сайн дурын ажилтнуудаас хамаардаг. Бид бүх түвшний оролцоог хүлээн авч, дараах хоёр үндсэн тусламжийн ангиллыг хайж байна: Хэрэв та цаг заваа зориулах боломжгүй бол <a %(a_donate)s>мөнгө хандивлах</a>, <a %(a_torrents)s>манай торрентийг үргэлжлүүлэх</a>, <a %(a_uploading)s>ном оруулах</a>, эсвэл <a %(a_help)s>найз нөхөддөө Anna’s Archive-ийн талаар ярих</a> зэргээр бидэнд ихээхэн туслах боломжтой. <span %(bold)s>Компаниуд:</span> бидний цуглуулгад өндөр хурдтай шууд хандах боломжийг байгууллагын түвшний хандив эсвэл шинэ цуглуулга (жишээ нь, шинэ скан, OCR хийсэн datasets, манай өгөгдлийг баяжуулах) солилцоогоор санал болгодог. Хэрэв та энэ бол <a %(a_contact)s>бидэнтэй холбогдоорой</a>. Мөн манай <a %(a_llm)s>LLM хуудас</a>-ыг үзнэ үү. Шагналууд Бид үргэлж сайн програмчлалын болон хамгаалалтын ур чадвартай хүмүүсийг хайж байдаг. Та хүн төрөлхтний өвийг хадгалахад чухал хувь нэмэр оруулах боломжтой. Талархлын илэрхийлэл болгон бид бат бөх хувь нэмэр оруулсан хүмүүст гишүүнчлэл олгодог. Маш их талархлын илэрхийлэл болгон бид онцгой чухал, хэцүү даалгавруудад мөнгөн шагнал олгодог. Энэ нь ажлын оронд үзэх ёсгүй боловч нэмэлт урамшуулал болж, гарсан зардлыг нөхөхөд тусалж болно. Манай ихэнх код нээлттэй эх сурвалжтай бөгөөд шагнал олгох үед таны кодыг ч мөн адил нээлттэй эх сурвалжтай байхыг хүснэ. Зарим онцгой тохиолдлуудыг бид тус тусад нь ярилцаж болно. Шагналуудыг даалгаврыг анх гүйцэтгэсэн хүнд олгодог. Шагналын тасалбар дээр сэтгэгдэл бичиж, бусдад та ямар нэгэн зүйл дээр ажиллаж байгаагаа мэдэгдэж, бусад хүмүүсийг хүлээх эсвэл тантай хамтран ажиллахыг уриалах боломжтой. Гэхдээ бусад хүмүүс ч бас үүнийг ажиллаж, танаас түрүүлж дуусгах эрхтэй гэдгийг анхаараарай. Гэсэн хэдий ч бид муу гүйцэтгэсэн ажилд шагнал олгодоггүй. Хоёр өндөр чанартай оруулга ойрхон (нэг эсвэл хоёр хоногийн дотор) хийгдсэн тохиолдолд бид өөрсдийн үзэмжээр хоёуланд нь шагнал олгох боломжтой, жишээлбэл эхний оруулгад 100%%, хоёр дахь оруулгад 50%% (нийт 150%%) олгох гэх мэт. Илүү том шагналуудын хувьд (ялангуяа scraping шагналууд), та үүний ~5%% хувийг гүйцэтгэсэн үедээ бидэнтэй холбоо барьж, таны арга барил бүрэн хэмжээнд хүрэхэд итгэлтэй байгаарай. Та бидэнд өөрийн арга барилаа хуваалцах ёстой бөгөөд бид санал хүсэлт өгөх болно. Мөн энэ аргаар бид шагналд ойртож буй олон хүн байвал юу хийхээ шийдэж, олон хүнд шагнал олгох, хүмүүсийг хамтран ажиллахыг уриалах гэх мэт зүйлсийг зохицуулж болно. АНХААРУУЛГА: өндөр шагналтай даалгаврууд <span %(bold)s>хэцүү</span> байдаг — эхлээд хялбар даалгавруудаас эхлэх нь зүйтэй байж магадгүй. Манай <a %(a_gitlab)s>Gitlab асуудлын жагсаалт</a> руу орж, “Label priority” гэж эрэмбэлээрэй. Энэ нь бидний анхаарч буй даалгавруудын ерөнхий дарааллыг харуулна. Тодорхой шагналгүй даалгаврууд ч гишүүнчлэлд хамрагдах боломжтой, ялангуяа “Accepted” болон “Anna’s favorite” гэж тэмдэглэгдсэн даалгаврууд. Та “Starter project”-оос эхлэхийг хүсэж магадгүй. Хөнгөн сайн дурын ажил Бид одоо %(matrix)s дээр синк хийгдсэн Matrix сувагтай болсон. Хэрэв танд хэдэн цагийн зав гарвал, олон төрлийн аргаар туслах боломжтой. <a %(a_telegram)s>Telegram дээрх сайн дурынхны чатаар</a> нэгдээрэй. Талархлын тэмдэг болгон, бид ихэвчлэн үндсэн амжилтуудын хувьд 6 сарын “Азтай номын санч” өгдөг бөгөөд үргэлжлүүлэн сайн дурын ажил хийвэл илүү ихийг өгдөг. Бүх амжилтууд өндөр чанартай ажил шаарддаг — хайнга ажил бидэнд илүү их хор хөнөөл учруулдаг тул бид үүнийг хүлээн авахгүй. Амжилтад хүрэх үедээ <a %(a_contact)s>бидэнд имэйл илгээнэ үү</a>. %(links)s биелүүлсэн хүсэлтийн холбоосууд эсвэл дэлгэцийн агшнууд. Z-Library эсвэл Library Genesis форумуудад ном (эсвэл өгүүлэл гэх мэт) хүсэлтийг биелүүлэх. Бидэнд өөрсдийн номын хүсэлтийн систем байхгүй ч эдгээр номын санг толин тусгал болгодог тул тэдгээрийг сайжруулах нь Anna’s Archive-ийг сайжруулдаг. Амжилт Даалгавар Даалгавраас хамаарна. Манай <a %(a_telegram)s>Telegram дээрх сайн дурынхны чатаар</a> нийтэлсэн жижиг даалгаврууд. Ихэвчлэн гишүүнчлэлд, заримдаа жижиг шагналд зориулагдсан. Манай сайн дурын чат бүлэгт жижиг даалгаврууд нийтлэгдсэн. Зассан асуудлууд дээрээ сэтгэгдэл үлдээж, бусад нь таны ажлыг давтахгүй байхыг анхаарна уу. %(links)s сайжруулсан бичлэгийн холбоосууд. Эхлэх цэг болгон <a %(a_list)s >санамсаргүй metadata асуудлуудын жагсаалтыг</a> ашиглаж болно. Open Library-тэй <a %(a_metadata)s>холбож</a> мета өгөгдлийг сайжруулах. Эдгээр нь танд Аннагийн Архивын тухай хэн нэгэнд мэдэгдэж, тэд танд талархаж байгааг харуулах ёстой. %(links)s холбоосууд эсвэл дэлгэцийн агшнууд. Аннагийн Архивын тухай үгийг түгээх. Жишээлбэл, AA дээр ном санал болгох, манай блогийн нийтлэлүүдэд холбоос өгөх, эсвэл ерөнхийдөө манай вэбсайтад хүмүүсийг чиглүүлэх. Нэг хэл бүрэн орчуулах (хэрэв өмнө нь бараг дууссан байгаагүй бол). <a %(a_translate)s>Вэбсайтыг орчуулах</a>. Таны ихээхэн хувь нэмэр оруулсан засварын түүхийн холбоос. Таны хэл дээрх Anna’s Archive-ийн Wikipedia хуудсыг сайжруулах. Бусад хэл дээрх AA-ийн Wikipedia хуудсаас, мөн манай вэбсайт болон блогийн мэдээллийг оруулах. AA-г бусад холбогдох хуудсанд дурд. Сайн дурын ажил ба шагналууд 