��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b ^  sd _  �f ,   2h   _h �  ei �  bk z  )m   �n P   �o s   	p F   }p i   �p �  .q L  �r �   t �  u �   �v �  �w �  �y �  n{ �   n} �  N~ z  � <   �� �   ȁ M   �� �  ��    Ԅ 2  � F   &� '   m�    ��    �� A   Ç    �    "� )   2�    \� %   s�    �� A   �� @  �   +� G   F� 5   �� 	   ċ    ΋    ڋ 
   �    ��    � 	   �    (�    8� "   ?�    b�    h� 	   |� 
   ��    ��    ��    ��    ׌ y  � R  k� �   �� ,   U� U  �� �   ؒ   }� �   �    Z� �   x�    � �   #� "   ߖ "  �    %� �  C� ,   ș V   ��    L� =  U� B  �� �  ֜ +  ��    �� G   ˟ ^   � �   r� )   +� +   U� n   �� :   � L   +� +   x�    �� i   �� �  � <   Ƥ x   � �   |� >   .�   m� @   ��   ç �   ۨ �   m� I   � �   h� �   &� F   ī �   � �   � c   �� =    � �   ^� 
   � �   �   �� F   ��    � �   �� �   ��    V� x  ^� �   ׳   u� �  z� �   	� *  �   � G   6� 9   ~� Z   �� i   � ]   }� \   ۽ +   8� �   d� W   � ;   E� 
   �� l   �� S   �� �   M� �   �    �� �   �� �   �� P  �� F  � �   L� �   �� ,  �� �   �� �   �� �   )� p  �� �   ,� �   �� 
   �� �   �� �   g� �   0� n  �� v   ^� �   �� �   V� �  �� y   ��   4� 
  N� !   Y� T   {� }   �� ;   N� i  �� �   �� `   �� u   �    ~�   �� �   �� �  p�    �� `   � �   q� �   � �   �� �   �� �  �� �  |� $   � P  <� t  ��   � �   � ,  �� 
  �� Q   �� �   E�    �� �   �� O  �� p  �� �   M� �   4� �   &� 	   �� ^  �� P  8� �   �� 	   �� �   �� 7  =� �   u� �  G� �   E \   '    � n  � Q   	   Z �   d �   B   7 �   L	 g  >
 W   � M   � �   L 5   >
    t
 8  �
 
  �   � '   � F    �  \ |   6 }   � R   1 �  � "       / f  L �   � �   p A   � �   > .   �   �       %    = '   L ;   t ;   � 6   � J   # v   n    � "   � :    E   R +   � ;   � :     �   ; 
   �    �   �   �  �   	" �   �" z   �# �   Y$ Y  &%    �& �   �& �   b' �   �' [  �( q  �) �  O+ �  5- |  �. F   l0 ;   �0 )   �0 �   1 �  �1 �  �3 =  a5 F  �6   �7    �8 "  ; *   ><   i< A  |= h  �>    '@ �   9@ .  
A �   <B \  �B �   @D    $E *   <E ]   gE    �E m  �E �  ;H F  �I �   L �   �L    yM �   �M �   ^N F   KO t   �O   P �   Q �   R r  �R \   T   sT    �U ;  �U �   �W   sX �  yZ �  �\ �   �^    �_ �   �_ �  �`    5b r  Gb �  �c   �e )  �g �   �i 9  �j F   l �  Hl �   	n x   �n =   _o N   �o    �o :   �o #   /p '   Sp "   {p    �p �   �p �  Pq �  8t M  /v �   }x 	   by �  ly   W| S  q~ �  � C  U� �  ��    '� �   0�    /� 
   7� �  B�   Ԉ �  � A  ڌ    � �  ,� �  � �  ݒ �  ٔ �  і (  �� j  Û    .� �   F� a   �� �  a� �  *� �   ̢ �   u� �   �� b  ��    �� �   � F   Ǧ S   �    b� 8   r� >   ��    � �  � �  �� ,   I� �  v� �   �    �� �   �� 6   e� �   �� �    � /   ڳ Z   
�    e� _  v� 0  ֵ �  � f  �� �   � 
   �� n   �� 1  �    :� �   ?� �  Ž �   w� .   � /   M� 4  }� ,   �� Y  �� �  9� �  ��    �� �   �� �   y� =  �� ~  ;�    �� �   �� ,   �� �   �� �   ��   7� �   ?� T   � ,   a� �   �� 2   }� �  �� _  >� �   �� �   ]� V   Z� 6   �� �   ��   �� e  �� �  �� �  �� 5   �� �   ��   �� E  �� �   �� �  �� �  w� ,   (� �  U� )   1� �  [� Q   +�    }�    ��   �� }  �� �   T�   Q� 
  ]� 6  k� �   �� �  D� �  � �   �� �  8� O  ' ?   w �   � �   8    �    � /  � �    <   �   � �  �   �	 �  �
 {  � �   8 �  �   l    � �   �    P �  Y (   �    
    #    ) )   :    d    w    �    �    �    �    �    � 	   � "   �    � &       -    1    =    E �   L W       p    � #   � $   � 0   � 1    "   L    o    w    �    �    �    � 	   �    �    �    � *   �    "    B '   Z &   � /   � )   �     #    /   >    n Q   � :   �     S    ?   k    �    � "   �    �        )    >    W    j    r    �    �    � 	   � 
   �    � !   �    �    � 	        
 	   ! "   +    N    T 	   [    e    u    �    �    �    �    �    � 	   �     '       =    B #   N    r    z    �    �    � 
   �    � c   � �   8 Z   � �    �  � x   �!    �!    "    ""    2" 
   9"    D" 	   \"     f" R   �" ;   �" \   # $   s# 7   �# 7   �# f   $ b   o$ �   �$ a   �% 7   & *   ?&    j&    v& 	   |& 	   �&    �&    �&    �& 
   �&    �&    �&    �&    �&    �&    �&    
'    ,'    F'    V'    g' 	   m' 	   w'    �'    �'    �'   �'    �(    �(    �(     �(    ) N   ) _   f) %   �) '   �) 6   *    K*    S*    [* y   ^*    �*    �* &   �* i   +    {+    �+ u   �+ %   , <  6, ]   s/ i   �/ �   ;0 �   �0 -   �1   �1 }   �2 $  U3 �   z4    m5    �5 @   �5 >   �5 Z   6 ^   m6 ]   �6 C   *7 i   n7 "   �7 *   �7    &8    +8 P   =8 !   �8    �8    �8    �8    �8 n   �8 
   _9 *   m9 D   �9    �9    �9 P   
: k   ^: k  �:    6<    L< �   b<    
=    =    =    %= 	   >= ,   H= 8  u=    �>    �> 
   �>    �>   �>    @    ,@    3@ e   :@    �@    �@ 0   �@    �@    �@ $   �@ �   A    �A    �A K   �A    +B    FB    UB    [B +   kB e   �B    �B 6   	C �   @C a   �C R   ?D 
   �D �   �D M   �E    �E 5   F    7F �   NF �   G    �G @   �G �   H �   �H    I &   �I    �I v  �I +   NK    zK    �K    �K "   �K �   �K    �L    �L (   �L 8   �L    %M    .M    LM     gM 7  �M k  �O �  ,Q <   *S 4   gS    �S    �S   �S �   �T �   �U    CV �   [V �  AW !   �X    �X �  Y   [    -\    6\ 3   E\    y\ 1  �\    �] %  �] �  �^ �   �`    za ~   �a +   b &   <b m   cb -  �b   �c �   e �  �e v   0g �   �g Q   �h �   �h �   �i Q   Vj �   �j .   Xk (   �k    �k    �k    �k #   �k    �k O   l )   ll 	   �l $   �l   �l     �m �   �m    �n ~   Yo )   �o #   p    &p    Ap +   Tp "   �p (   �p '   �p �   �p &   �q �   �q    �r �   �r t   ys ~   �s 1  mt �   �u *   ^v �   �v 	   w   &w m   5x    �x �  �x    \z    iz    ~z    �z )   �z    �z    �z J   �z �   2{ �   �{ 	   �|    �|    �| �   �|   �} �   �~ u   z    �    �     �    7�    P�    `�    o� ?   w� +   �� �  � R   ��    � ]   �� r   [� H   ΃ b   � U   z� K   Є    � i   "� B   �� ~   υ T   N� E   ��    � �   �� \   � <   G� r   �� Q   �� 6   I�    �� 4   �� t   �� �   3� 0   ̊ �   ��    ��    �� ?   �� X   � �   H�    ؍ �  � �   ��    }�    ��    ��    �� �   �� -   �� `   ڑ �   ;� �   � �   ғ �   m� �   2� �   Ǖ �   b� ^   !�   �� i   �� \  � �   c� �   � 
   � 
   � 
   "� �   0� 
   Ü 
   ќ D   ߜ ^   $� �   �� 
   `� l   n� 7   ۞ ~   � ;   �� m   Ο c   <� 
   �� �   �� 
   2� 
   @� 
   N� 
  \� �   g� �  �    ~�    ��    �� �   ��    X�     w� �   �� �   ��    "�    B�    R� 1   k� 1   �� .   Ϩ    ��    � �   2�     � �  <� �   Ы �   �� L   D� �   �� 	  +� �  5� +  �� �   !� �   �� {   G�    ô b  ش    ;� i  Y� �   ÷ $  ¸ �   � $  m� �   �� �   K� �   ټ *   [� 1   ��    �� .   ͽ 
   ��    �    � �   *�    � o   
� (   }�    ��    ��    ��     �� k   ۿ C   G� *   �� (  �� 2   �� 	   �    �    "�    9�    W�    i�    u�    ~� 
   ��    �� 
   ��    �� .   �� �   ��    �� 
   �� 
   �� 
   ��    �� 
   ��    �� 
   ��    ��    � %   � n   ?�    �� ,   �� Z   �� �   F� �   �� �   ��   \� �   l� %  �    <� �   Z� 4   �� L   &� O   s� �   �� �   _� A   F� d   ��    �� {    � l   |� �   ��    p�     w�    ��    ��    ��    ��    ��    ��    �    �     �    3�    M�    e�    {�    ��    ��    ��    ��    ��    �� $   �� 2   � ~   ?� }   �� *   <�    g� n   �� P   �� u   E� &   �� (   �� /   � 8   ;� 8   t� +   �� �   ��   ��   ��    �� B   �� �   ,� '   �� �   �� �   �� }   �� D   �    `� �   y� �   "� ]   �� *   � f   >� �   ��    }� %   ��    �� <   �� x   � f   ��    ��    ��    � #   	� �   -� :   �� &   �� :   � '   Z�     �� "   �� E   ��    � g   &� >   ��   �� O   �� T   "� �   w� ?   $�    d�    ��    ��    �� !   ��    �� !   � 5   9� :   o� I  �� >   �� 5   3� B   i� &   ��    �� o   �� v   J� �   �� �   ��    R� �   [� '   ��    � �   *�    � !   � A   A� /   �� W   �� D   � H   P�    ��    �� �   �� .   q� $   �� S   �� �   � ;   
� �   F� l   �� U   H� u   �� $   � '   9� w   a�    �� d   ��    Z� �   y� 3   .� >   b�    ��    �� �   �� @   �� ?   � �   D� `   �� M   6� �   �� �   �    �� 
   �� G   �� >   ��    ,�    @�    `�    p� 
   �� -   �� �   �� i   N�    ��    �� "   �� %   � -   9� s   g� 1   �� q   
� R   � "   �� a   �� �   W� /     O   O     �  H   �  <   �     : p   N r   � ,   2 H   _    � /   � M   �    8 h   M '   �    � )   � T    �   s    :    Q    h D       � V   � =   2 �   p �       � ?   � �   �    � �   � C   C	 $   �	 S   �	 �    
    �
    �
    �
    �
     -   0 M   ^    � 	   �    � %   � B   � B   @    � S   � F   �    '
    ;
 '   X
    �
    �
 j   �
 �    O       d 
   q �    <  , L   i    � m  � �  4 *   � Z   �    V -  u &   � l   �    7    Q �  h    = M   Q s   � c    R   w    � j   � 5   I     z   � =    *   P X   { '   � Q   � �   N �   � '   k �   � N  B  �   �! 1   N" t  �" �   �# �   �$    �% �   �& 9   �'    �' �   ( ,   �( �  �( h   �* F   +    d+    q+ �  �+    )- �   -- (  �- '  / !  E0 L   g1 H   �1 X   �1 )   V2 #   �2 S   �2 U   �2    N3    j3 7   �3    �3    �3 2   �3 T   '4 *   |4    �4 j   �4 �   5 �   �5    |6    �6    �6 I   �6 W   �6 M   G7 �   �7 Z   Z8    �8 (   �8 �   �8 	   �9 �   �9 �  ^: �   �< L   �= 1   0>    b>    h>    o> E   s> 3   �> �   �> �   }? V   P@    �@    �@ %   �@    �@ ]   A    nA ;   �A    �A #   �A %   �A    
B    B a   &B    �B    �B %   �B    �B    �B r   �B �   ZC O   &D f   vD J   �D �   (E    �E    �E �   F �   �F �   �G 	   :H t   DH G   �H C   I n   EI k   �I T    J    uJ V   �J    �J    �J    K    #K    :K    NK    bK    sK    �K    �K *   �K *   �K *   �K    "L /   @L (   pL    �L    �L    �L 7   �L    M    6M 8   >M .   wM a   �M /   N    8N    NN #   `N +   �N    �N n   �N ^   5O s   �O �   P    �P     Q    Q    ;Q ,   QQ 	   ~Q    �Q    �Q �   �Q     =R $   ^R    �R 
   �R 	   �R 4   �R    �R �   �R    �S    �S    �S '   �S    T    T    6T    TT #   tT '   �T (   �T    �T X   �T '   IU    qU    �U 7   �U K   �U !   V (   ?V    hV b   �V \   �V I   HW    �W    �W 	   �W    �W    �W    �W �  X �   �Y    $Z    8Z '   <Z    dZ #   sZ    �Z 
   �Z }   �Z <   %[ �   b[    +\    @\ ~   `\    �\    �\    ] !   :] +   \] !   �]    �] 
   �] 1   �]     ^ "   $^ 9   G^    �^ <   �_ >   �_ Q   ` !   p` �   �`     a    5a R   Aa    �a &   �a    �a F   �a <   3b !   pb �   �b    1c    Bc    Qc    lc    ~c    �c    �c    �c    �c R   �c �   Jd    :e %   Ve �   |e �   Pf    �f    �f    g    1g    Ag    \g    og    �g    �g    �g    �g    �g 
   �g    �g    h    h     h    ,h    <h �   \h �   i a  j �  qk �  om �   o g  �o    Cq �   Lq    Ir �   ^r �   6s {  t �   �u b  Xv 7   �w �   �w =   �x    �x C   �x F   ,y m   sy U   �y �   7z �   �z �   �{ J  Y|    �~ �   �~ �   y d   Q� �   ��    b� �   q� `  Q� �   �� �   ��    Y� c   f� c   ʅ �   .� �   ,� `   �� �   �    �� &   ��    Ԉ C   � (   -�    V� �   f� J   � l   3�     �� �   �� r   K� @   �� W   �� Q   W� j   �� i   � N   ~� m   ͍ h   ;� �   �� q   (�    �� #   �� %   ď S   � 1   >�    p�    y� D   ��    Ő    א    � ;   �� 3   2� E   f�    ��    ��    ̑    ؑ 	   ޑ _   � u   H� Y   �� ?   �    X�    `� *   � 
   �� 	   �� 	    	   ̓    ֓    ۓ    �    �    � 
   ��    �    
�    �    #�    0�    ?�    Q�    X�    e�    l� !   s�    ��    �� {   Ҕ    N� R   c� �   �� 	   q�    {� 	   ��    ��    ��    ��    �� �   �� s   X� E   ̗    �    0� g   D�    �� �   �� ~   N� "   ͙    � ~   	� �   �� X   [� �   �� g   t� +   ܜ q   �    z�    �� C   �� �   �� !   � �   �� �   =� �   �� S   q�    Š    ؠ 
   ݠ    �    ��    
�    �    � �   7� o   ġ �   4� �   �� �   �� l   �� I   � �  8� h  � �   u� �   o�   �� R  ��    R� �   ^�   � �   4� �  � �  |� u   T� e  ʳ    0� >   F� �   �� j  � I   ~� �   ȷ 	   ��    ĸ    ̸ �   � 6   o� a   �� /   � _   8� M   �� S   � %   :� �   `� Q   � /   T� G   �� �   ̼    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: sv
Language-Team: sv <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library är ett populärt (och olagligt) bibliotek. De har tagit Library Genesis-samlingen och gjort den lättsökbar. Utöver det har de blivit mycket effektiva på att uppmuntra nya bokbidrag, genom att belöna bidragande användare med olika förmåner. De bidrar för närvarande inte med dessa nya böcker tillbaka till Library Genesis. Och till skillnad från Library Genesis gör de inte sin samling lätt spegelbar, vilket förhindrar bred bevarande. Detta är viktigt för deras affärsmodell, eftersom de tar betalt för att få tillgång till deras samling i bulk (mer än 10 böcker per dag). Vi gör inga moraliska bedömningar om att ta betalt för massåtkomst till en illegal boksamling. Det råder ingen tvekan om att Z-Library har varit framgångsrik i att utöka tillgången till kunskap och att skaffa fler böcker. Vi är helt enkelt här för att göra vår del: att säkerställa det långsiktiga bevarandet av denna privata samling. - Anna och teamet (<a %(reddit)s>Reddit</a>) I den ursprungliga releasen av Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), gjorde vi en spegel av Z-Library, en stor illegal boksamling. Som en påminnelse, detta är vad vi skrev i det ursprungliga blogginlägget: Den samlingen daterades tillbaka till mitten av 2021. Under tiden har Z-Library vuxit i en häpnadsväckande takt: de har lagt till cirka 3,8 miljoner nya böcker. Det finns några dubbletter där, visst, men majoriteten verkar vara genuint nya böcker, eller högkvalitativa skanningar av tidigare inskickade böcker. Detta beror till stor del på det ökade antalet frivilliga moderatorer på Z-Library och deras system för massuppladdning med deduplicering. Vi vill gratulera dem till dessa prestationer. Vi är glada att meddela att vi har fått alla böcker som lades till Z-Library mellan vår senaste spegel och augusti 2022. Vi har också gått tillbaka och hämtat några böcker som vi missade första gången. Sammantaget är denna nya samling cirka 24TB, vilket är mycket större än den förra (7TB). Vår spegel är nu totalt 31TB. Återigen deduplicerade vi mot Library Genesis, eftersom det redan finns torrents tillgängliga för den samlingen. Vänligen gå till Pirate Library Mirror för att kolla in den nya samlingen (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Där finns mer information om hur filerna är strukturerade och vad som har förändrats sedan sist. Vi kommer inte att länka till det härifrån, eftersom detta bara är en bloggwebbplats som inte värdar något olagligt material. Självklart är seedning också ett bra sätt att hjälpa oss. Tack till alla som seedar vårt tidigare set av torrents. Vi är tacksamma för det positiva gensvaret och glada att det finns så många som bryr sig om bevarandet av kunskap och kultur på detta ovanliga sätt. 3x nya böcker tillagda till Pirate Library Mirror (+24TB, 3,8 miljoner böcker) Läs de kompletterande artiklarna av TorrentFreak: <a %(torrentfreak)s>första</a>, <a %(torrentfreak_2)s>andra</a> - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) kompletterande artiklar av TorrentFreak: <a %(torrentfreak)s>första</a>, <a %(torrentfreak_2)s>andra</a> För inte så länge sedan var "skuggbibliotek" på väg att försvinna. Sci-Hub, det massiva olagliga arkivet av akademiska artiklar, hade slutat ta emot nya verk på grund av rättsprocesser. "Z-Library", det största olagliga biblioteket av böcker, såg sina påstådda skapare arresterade för brott mot upphovsrätten. De lyckades otroligt nog undkomma sin arrestering, men deras bibliotek är inte mindre hotat. Vissa länder gör redan en version av detta. TorrentFreak <a %(torrentfreak)s>rapporterade</a> att Kina och Japan har infört AI-undantag i sina upphovsrättslagar. Det är oklart för oss hur detta interagerar med internationella avtal, men det ger definitivt skydd för deras inhemska företag, vilket förklarar vad vi har sett. Vad gäller Annas Arkiv — vi kommer att fortsätta vårt underjordiska arbete rotat i moralisk övertygelse. Men vår största önskan är att träda fram i ljuset och förstärka vår påverkan lagligt. Snälla reformera upphovsrätten. När Z-Library stod inför nedläggning hade jag redan säkerhetskopierat hela dess bibliotek och letade efter en plattform att hysa det. Det var min motivation för att starta Annas Arkiv: en fortsättning på uppdraget bakom de tidigare initiativen. Vi har sedan dess vuxit till att bli det största skuggbiblioteket i världen, med över 140 miljoner upphovsrättsskyddade texter i olika format — böcker, akademiska artiklar, tidskrifter, tidningar och mer. Mitt team och jag är ideologer. Vi tror att det är moraliskt rätt att bevara och hysa dessa filer. Bibliotek runt om i världen ser sina anslag minskas, och vi kan inte heller lita på att mänsklighetens arv bevaras av företag. Sedan kom AI. Praktiskt taget alla stora företag som bygger LLM:er kontaktade oss för att träna på vår data. De flesta (men inte alla!) amerikanska företag omprövade när de insåg den olagliga naturen av vårt arbete. Däremot har kinesiska företag entusiastiskt omfamnat vår samling, till synes obekymrade över dess laglighet. Detta är anmärkningsvärt med tanke på Kinas roll som undertecknare av nästan alla stora internationella upphovsrättsavtal. Vi har gett höghastighetsåtkomst till cirka 30 företag. De flesta av dem är LLM-företag, och några är datamäklare, som kommer att sälja vår samling vidare. De flesta är kinesiska, men vi har också arbetat med företag från USA, Europa, Ryssland, Sydkorea och Japan. DeepSeek <a %(arxiv)s>medgav</a> att en tidigare version tränades på en del av vår samling, även om de är förtegna om sin senaste modell (förmodligen också tränad på vår data). Om västvärlden vill ligga i framkant i loppet om LLM:er, och i slutändan AGI, behöver den ompröva sin inställning till upphovsrätt, och det snart. Oavsett om du håller med oss eller inte om vår moraliska ståndpunkt, blir detta nu en fråga om ekonomi, och till och med om nationell säkerhet. Alla maktblock bygger artificiella supervetenskapsmän, superhackare och supermilitärer. Informationsfrihet blir en fråga om överlevnad för dessa länder — till och med en fråga om nationell säkerhet. Vårt team kommer från hela världen, och vi har ingen särskild inriktning. Men vi skulle uppmuntra länder med starka upphovsrättslagar att använda detta existentiella hot för att reformera dem. Så vad ska man göra? Vår första rekommendation är enkel: förkorta upphovsrättstiden. I USA beviljas upphovsrätt i 70 år efter författarens död. Detta är absurt. Vi kan anpassa detta till patent, som beviljas i 20 år efter ansökan. Detta borde vara mer än tillräckligt med tid för författare av böcker, artiklar, musik, konst och andra kreativa verk att få full ersättning för sina ansträngningar (inklusive långsiktiga projekt som filmatiseringar). Sedan bör lagstiftare åtminstone inkludera undantag för massbevarande och spridning av texter. Om förlorade intäkter från enskilda kunder är den största oron, kan distribution på personlig nivå förbli förbjuden. I sin tur skulle de som kan hantera stora arkiv — företag som tränar LLM:er, tillsammans med bibliotek och andra arkiv — omfattas av dessa undantag. Upphovsrättsreform är nödvändig för nationell säkerhet Sammanfattning: Kinesiska LLM:er (inklusive DeepSeek) är tränade på mitt olagliga arkiv av böcker och artiklar — det största i världen. Västvärlden behöver omarbeta upphovsrättslagen som en fråga om nationell säkerhet. Se det <a %(all_isbns)s>ursprungliga blogginlägget</a> för mer information. Vi utmanade att förbättra detta. Vi skulle belöna en förstaplats med $6,000, andraplats med $3,000 och tredjeplats med $1,000. På grund av det överväldigande gensvaret och otroliga bidrag har vi beslutat att öka prispotten något och belöna en fyrvägs tredjeplats med $500 vardera. Vinnarna är nedan, men se till att titta på alla bidrag <a %(annas_archive)s>här</a>, eller ladda ner vår <a %(a_2025_01_isbn_visualization_files)s>kombinerade torrent</a>. Första plats $6,000: phiresky Detta <a %(phiresky_github)s>bidrag</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) är helt enkelt allt vi ville ha, och mer! Vi gillade särskilt de otroligt flexibla visualiseringsalternativen (som till och med stöder anpassade shaders), men med en omfattande lista av förinställningar. Vi gillade också hur snabbt och smidigt allt är, den enkla implementeringen (som inte ens har en backend), den smarta minikartan och den omfattande förklaringen i deras <a %(phiresky_github)s>blogginlägg</a>. Otroligt arbete och en välförtjänt vinnare! - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Våra hjärtan är fulla av tacksamhet. Noterbara idéer Skyskrapor för sällsynthet Många reglage för att jämföra datasets, som om du vore en DJ. Skalstock med antal böcker. Fina etiketter. Snygg standardfärgskala och värmekarta. Unik kartvy och filter Anteckningar och även live-statistik Live-statistik Några fler idéer och implementeringar som vi särskilt gillade: Vi skulle kunna fortsätta ett tag, men låt oss stanna här. Var noga med att titta på alla bidrag <a %(annas_archive)s>här</a>, eller ladda ner vår <a %(a_2025_01_isbn_visualization_files)s>kombinerade torrent</a>. Så många bidrag, och varje ger ett unikt perspektiv, oavsett om det är i UI eller implementering. Vi kommer åtminstone att integrera det förstaplacerade bidraget på vår huvudsida, och kanske några andra. Vi har också börjat tänka på hur vi ska organisera processen för att identifiera, bekräfta och sedan arkivera de sällsyntaste böckerna. Mer kommer på denna front. Tack till alla som deltog. Det är fantastiskt att så många bryr sig. Enkel växling av datasets för snabba jämförelser. Alla ISBN CADAL SSNOs CERLALC dataläcka DuXiu SSID EBSCOhosts eBook Index Google Böcker Goodreads Internetarkivet ISBNdb ISBN Global Register of Publishers Libby Filer i Annas Arkiv Nexus/STC OCLC/Worldcat OpenLibrary Ryska statliga biblioteket Imperial Library of Trantor Andra plats $3,000: hypha ”Även om perfekta kvadrater och rektanglar är matematiskt tilltalande, ger de inte överlägsen lokalitet i ett kartläggningssammanhang. Jag tror att asymmetrin som är inneboende i dessa Hilbert eller klassiska Morton inte är en brist utan en funktion. Precis som Italiens berömda stövelformade kontur gör det omedelbart igenkännligt på en karta, kan de unika "egenheterna" hos dessa kurvor fungera som kognitiva landmärken. Denna distinktivitet kan förbättra det rumsliga minnet och hjälpa användare att orientera sig, vilket potentiellt gör det lättare att lokalisera specifika regioner eller märka mönster.” Ett annat otroligt <a %(annas_archive_note_2913)s>bidrag</a>. Inte lika flexibelt som förstaplatsen, men vi föredrog faktiskt dess makronivåvisualisering över förstaplatsen (space-filling curve, gränser, märkning, markering, panorering och zoomning). En <a %(annas_archive_note_2971)s>kommentar</a> av Joe Davis resonerade med oss: Och fortfarande massor av alternativ för visualisering och rendering, samt en otroligt smidig och intuitiv användargränssnitt. En solid andraplats! - Anna och teamet (<a %(reddit)s>Reddit</a>) För några månader sedan utlyste vi ett <a %(all_isbns)s>$10,000 pris</a> för att skapa den bästa möjliga visualiseringen av vår data som visar ISBN-rymden. Vi betonade att visa vilka filer vi redan har/inte har arkiverat, och vi lade senare till en dataset som beskriver hur många bibliotek som har ISBN (ett mått på sällsynthet). Vi har blivit överväldigade av responsen. Det har funnits så mycket kreativitet. Ett stort tack till alla som har deltagit: er energi och entusiasm är smittsam! I slutändan ville vi besvara följande frågor: <strong>vilka böcker finns i världen, hur många har vi redan arkiverat, och vilka böcker bör vi fokusera på härnäst?</strong> Det är fantastiskt att se så många människor bry sig om dessa frågor. Vi började med en grundläggande visualisering själva. På mindre än 300kb representerar denna bild kortfattat den största helt öppna "listan över böcker" som någonsin sammanställts i mänsklighetens historia: Tredje plats $500 #1: maxlion I detta <a %(annas_archive_note_2940)s>bidrag</a> gillade vi verkligen de olika typerna av vyer, särskilt jämförelse- och förlagsvyerna. Tredje plats $500 #2: abetusk Även om det inte är det mest polerade användargränssnittet, uppfyller detta <a %(annas_archive_note_2917)s>bidrag</a> många av kraven. Vi gillade särskilt dess jämförelsefunktion. Tredje plats $500 #3: conundrumer0 Precis som förstaplatsen imponerade detta <a %(annas_archive_note_2975)s>bidrag</a> oss med sin flexibilitet. I slutändan är det detta som gör ett bra visualiseringsverktyg: maximal flexibilitet för avancerade användare, samtidigt som det hålls enkelt för genomsnittliga användare. Tredje plats $500 #4: charelf Det sista <a %(annas_archive_note_2947)s>bidraget</a> som får en belöning är ganska grundläggande, men har några unika funktioner som vi verkligen gillade. Vi gillade hur de visar hur många datasets som täcker ett visst ISBN som ett mått på popularitet/pålitlighet. Vi gillade också verkligen enkelheten men effektiviteten i att använda en opacitetsreglage för jämförelser. Vinnare av $10,000 ISBN-visualiseringspriset Sammanfattning: Vi fick några otroliga bidrag till $10,000 ISBN-visualiseringspriset. Bakgrund Hur kan Annas Arkiv uppnå sitt mål att säkerhetskopiera all mänsklighetens kunskap, utan att veta vilka böcker som fortfarande finns där ute? Vi behöver en ATT GÖRA-lista. Ett sätt att kartlägga detta är genom ISBN-nummer, som sedan 1970-talet har tilldelats varje bok som publiceras (i de flesta länder). Det finns ingen central myndighet som känner till alla ISBN-tilldelningar. Istället är det ett distribuerat system, där länder får nummerintervall, som sedan tilldelar mindre intervall till stora förlag, som i sin tur kan dela upp intervall till mindre förlag. Slutligen tilldelas individuella nummer till böcker. Vi började kartlägga ISBNs <a %(blog)s>för två år sedan</a> med vår skrapning av ISBNdb. Sedan dess har vi skrapat många fler metadata-källor, såsom <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby och fler. En fullständig lista finns på sidorna "Datasets" och "Torrents" på Annas Arkiv. Vi har nu den överlägset största helt öppna, lätt nedladdningsbara samlingen av bokmetadata (och därmed ISBNs) i världen. Vi har <a %(blog)s>skrivit utförligt</a> om varför vi bryr oss om bevarande, och varför vi just nu befinner oss i ett kritiskt fönster. Vi måste nu identifiera sällsynta, underfokuserade och unikt utsatta böcker och bevara dem. Att ha bra metadata om alla böcker i världen hjälper med det. $10,000 belöning Stark hänsyn kommer att tas till användbarhet och hur bra det ser ut. Visa faktisk metadata för individuella ISBNs när du zoomar in, såsom titel och författare. Bättre rymdfyllningskurva. T.ex. en sicksack, som går från 0 till 4 på första raden och sedan tillbaka (i omvänd ordning) från 5 till 9 på andra raden — tillämpas rekursivt. Olika eller anpassningsbara färgscheman. Speciella vyer för att jämföra datasets. Sätt att felsöka problem, såsom annan metadata som inte stämmer överens väl (t.ex. mycket olika titlar). Kommentera bilder med kommentarer om ISBN eller intervall. Eventuella heuristiker för att identifiera sällsynta eller hotade böcker. Vilka kreativa idéer du än kan komma på! Kod Koden för att generera dessa bilder, samt andra exempel, finns i <a %(annas_archive)s>denna katalog</a>. Vi kom på ett kompakt dataformat, med vilket all nödvändig ISBN-information är cirka 75MB (komprimerad). Beskrivningen av dataformatet och koden för att generera det finns <a %(annas_archive_l1244_1319)s>här</a>. För belöningen behöver du inte använda detta, men det är förmodligen det mest praktiska formatet att börja med. Du kan transformera vår metadata hur du vill (även om all din kod måste vara öppen källkod). Vi kan inte vänta på att se vad du kommer på. Lycka till! Forka detta repo, och redigera detta blogginläggs HTML (inga andra backends förutom vår Flask-backend är tillåtna). Gör bilden ovan smidigt zoombar, så att du kan zooma hela vägen till individuella ISBNs. Att klicka på ISBNs ska ta dig till en metadata-sida eller sökning på Annas Arkiv. Du måste fortfarande kunna växla mellan alla olika datasets. Landsintervall och förlagsintervall ska markeras vid hovring. Du kan använda t.ex. <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> för landsinformation, och vår "isbngrp" skrapning för förlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Det måste fungera bra på både stationära och mobila enheter. Det finns mycket att utforska här, så vi utlyser en belöning för att förbättra visualiseringen ovan. Till skillnad från de flesta av våra belöningar är denna tidsbegränsad. Du måste <a %(annas_archive)s>skicka in</a> din öppen källkod senast 2025-01-31 (23:59 UTC). Den bästa inskickningen får $6,000, andra plats är $3,000, och tredje plats är $1,000. Alla belöningar kommer att delas ut med Monero (XMR). Nedan finns de minimala kriterierna. Om ingen inskickning uppfyller kriterierna kan vi fortfarande dela ut några belöningar, men det kommer att vara efter vårt gottfinnande. För bonuspoäng (detta är bara idéer — låt din kreativitet flöda): Du KAN helt avvika från de minimala kriterierna och göra en helt annan visualisering. Om den är riktigt spektakulär, kvalificerar den sig för belöningen, men efter vårt gottfinnande. Gör inlämningar genom att posta en kommentar till <a %(annas_archive)s>detta ärende</a> med en länk till ditt forkade repo, merge-förfrågan eller diff. - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Denna bild är 1000×800 pixlar. Varje pixel representerar 2 500 ISBN. Om vi har en fil för ett ISBN gör vi den pixeln mer grön. Om vi vet att ett ISBN har utfärdats, men vi inte har en matchande fil, gör vi den mer röd. På mindre än 300kb representerar denna bild kortfattat den största helt öppna "listan över böcker" som någonsin sammanställts i mänsklighetens historia (några hundra GB komprimerad i sin helhet). Den visar också: det finns mycket arbete kvar med att säkerhetskopiera böcker (vi har bara 16%). Visualisera alla ISBN — $10,000 belöning senast 2025-01-31 Denna bild representerar den största helt öppna "listan över böcker" som någonsin sammanställts i mänsklighetens historia. Visualisering Förutom översiktsbilden kan vi också titta på individuella datasets vi har förvärvat. Använd rullgardinsmenyn och knapparna för att växla mellan dem. Det finns många intressanta mönster att se i dessa bilder. Varför finns det en viss regelbundenhet av linjer och block, som verkar ske i olika skalor? Vad är de tomma områdena? Varför är vissa datasets så klustrade? Vi lämnar dessa frågor som en övning för läsaren. - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Slutsats Med denna standard kan vi göra utgåvor mer stegvis och lättare lägga till nya datakällor. Vi har redan några spännande utgåvor på gång! Vi hoppas också att det blir lättare för andra skuggbibliotek att spegla våra samlingar. Trots allt är vårt mål att bevara mänsklig kunskap och kultur för alltid, så ju mer redundans desto bättre. Exempel Låt oss titta på vår senaste Z-Library-utgåva som ett exempel. Den består av två samlingar: ”<span style="background: #fffaa3">zlib3_records</span>” och ”<span style="background: #ffd6fe">zlib3_files</span>”. Detta gör att vi kan skrapa och släppa metadataregister separat från de faktiska bokfilerna. Som sådant släppte vi två torrenter med metadatafiler: Vi släppte också en mängd torrenter med binärdatamappar, men endast för ”<span style="background: #ffd6fe">zlib3_files</span>”-samlingen, totalt 62: Genom att köra <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se vad som finns inuti: I detta fall är det metadata för en bok som rapporterats av Z-Library. På toppnivån har vi bara ”aacid” och ”metadata”, men ingen ”data_folder”, eftersom det inte finns någon motsvarande binärdata. AACID innehåller ”22430000” som det primära ID:t, vilket vi kan se är taget från ”zlibrary_id”. Vi kan förvänta oss att andra AAC:er i denna samling har samma struktur. Nu låt oss köra <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Detta är en mycket mindre AAC-metadata, även om huvuddelen av denna AAC finns någon annanstans i en binär fil! Trots allt har vi en ”data_folder” denna gång, så vi kan förvänta oss att motsvarande binärdata finns på <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. ”Metadata” innehåller ”zlibrary_id”, så vi kan enkelt associera det med motsvarande AAC i ”zlib_records”-samlingen. Vi kunde ha associerat på ett antal olika sätt, t.ex. genom AACID — standarden föreskriver inte det. Observera att det inte heller är nödvändigt för ”metadata”-fältet att i sig vara JSON. Det kan vara en sträng som innehåller XML eller något annat dataformat. Du kan till och med lagra metadata-information i den associerade binära blobben, t.ex. om det är mycket data. Heterogena filer och metadata, så nära originalformatet som möjligt. Binär data kan serveras direkt av webbservrar som Nginx. Heterogena identifierare i källbiblioteken, eller till och med avsaknad av identifierare. Separata utgåvor av metadata kontra fildata, eller endast metadata-utgåvor (t.ex. vår ISBNdb-utgåva). Distribution genom torrents, men med möjlighet till andra distributionsmetoder (t.ex. IPFS). Oföränderliga poster, eftersom vi bör anta att våra torrents kommer att leva för evigt. Inkrementella utgåvor / tilläggsutgåvor. Maskinläsbara och skrivbara, bekvämt och snabbt, särskilt för vår stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Något enkel mänsklig inspektion, även om detta är sekundärt till maskinläsbarhet. Enkelt att så våra samlingar med en standardhyrd seedbox. Designmål Vi bryr oss inte om att filer är lätta att navigera manuellt på disk, eller sökbara utan förbehandling. Vi bryr oss inte om att vara direkt kompatibla med befintlig biblioteksprogramvara. Även om det ska vara enkelt för vem som helst att så vår samling med hjälp av torrents, förväntar vi oss inte att filerna ska vara användbara utan betydande teknisk kunskap och engagemang. Vårt primära användningsfall är distributionen av filer och tillhörande metadata från olika befintliga samlingar. Våra viktigaste överväganden är: Några icke-mål: Eftersom Annas Arkiv är öppen källkod vill vi använda vårt format direkt. När vi uppdaterar vårt sökindex, får vi endast tillgång till offentligt tillgängliga vägar, så att alla som forkar vårt bibliotek snabbt kan komma igång. <strong>AAC.</strong> AAC (Annas Arkiv Container) är ett enskilt objekt bestående av <strong>metadata</strong>, och eventuellt <strong>binär data</strong>, båda är oföränderliga. Det har en globalt unik identifierare, kallad <strong>AACID</strong>. <strong>AACID.</strong> Formatet för AACID är detta: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Till exempel, en faktisk AACID som vi har släppt är <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID-intervall.</strong> Eftersom AACIDs innehåller monotoniskt ökande tidsstämplar, kan vi använda det för att ange intervall inom en viss samling. Vi använder detta format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, där tidsstämplarna är inkluderande. Detta är konsekvent med ISO 8601-notation. Intervall är kontinuerliga och kan överlappa, men vid överlappning måste de innehålla identiska poster som den tidigare släppta i den samlingen (eftersom AACs är oföränderliga). Saknade poster är inte tillåtna. <code>{collection}</code>: samlingsnamnet, som kan innehålla ASCII-bokstäver, siffror och understreck (men inga dubbla understreck). <code>{collection-specific ID}</code>: en samlingsspecifik identifierare, om tillämpligt, t.ex. Z-Library ID. Kan utelämnas eller förkortas. Måste utelämnas eller förkortas om AACID annars skulle överstiga 150 tecken. <code>{ISO 8601 timestamp}</code>: en kort version av ISO 8601, alltid i UTC, t.ex. <code>20220723T194746Z</code>. Detta nummer måste öka monotoniskt för varje släpp, även om dess exakta semantik kan skilja sig per samling. Vi föreslår att använda tiden för skrapning eller generering av ID. <code>{shortuuid}</code>: en UUID men komprimerad till ASCII, t.ex. med base57. Vi använder för närvarande <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket. <strong>Binär datamapp.</strong> En mapp med den binära datan för ett intervall av AACs, för en viss samling. Dessa har följande egenskaper: Katalogen måste innehålla datafiler för alla AACs inom det angivna intervallet. Varje datafil måste ha sin AACID som filnamn (inga tillägg). Katalognamnet måste vara ett AACID-intervall, försett med prefixet <code style="color: green">annas_archive_data__</code>, och inget suffix. Till exempel, en av våra faktiska släpp har en katalog som heter<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Det rekommenderas att göra dessa mappar någorlunda hanterbara i storlek, t.ex. inte större än 100GB-1TB vardera, även om denna rekommendation kan ändras över tid. <strong>Samling.</strong> Varje AAC tillhör en samling, som per definition är en lista över AACs som är semantiskt konsekventa. Det betyder att om du gör en betydande förändring av formatet på metadata, måste du skapa en ny samling. Standarden <strong>Metadatafil.</strong> En metadatafil innehåller metadata för ett intervall av AACs, för en viss samling. Dessa har följande egenskaper: <code>data_folder</code> är valfritt, och är namnet på den binära datamappen som innehåller motsvarande binär data. Filnamnet på den motsvarande binära datan inom den mappen är postens AACID. Varje JSON-objekt måste innehålla följande fält på toppnivå: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valfritt). Inga andra fält är tillåtna. Filnamnet måste vara ett AACID-intervall, försett med prefixet <code style="color: red">annas_archive_meta__</code> och följt av <code>.jsonl.zstd</code>. Till exempel, en av våra släpp kallas<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Som indikeras av filändelsen, är filtypen <a %(jsonlines)s>JSON Lines</a> komprimerad med <a %(zstd)s>Zstandard</a>. <code>metadata</code> är godtycklig metadata, enligt samlingens semantik. Den måste vara semantiskt konsekvent inom samlingen. Prefixet <code style="color: red">annas_archive_meta__</code> kan anpassas till namnet på din institution, t.ex. <code style="color: red">my_institute_meta__</code>. <strong>“records” och “files” samlingar.</strong> Enligt konvention är det ofta bekvämt att släppa “records” och “files” som olika samlingar, så att de kan släppas vid olika tidpunkter, t.ex. baserat på skrapningshastigheter. En “record” är en samling endast med metadata, innehållande information som boktitlar, författare, ISBN, etc., medan “files” är samlingarna som innehåller själva filerna (pdf, epub). Slutligen bestämde vi oss för en relativt enkel standard. Den är ganska lös, icke-normativ och ett pågående arbete. <strong>Torrenter.</strong> Metadatafilerna och mapparna med binärdata kan paketeras i torrenter, med en torrent per metadatafil eller en torrent per binärdatamapp. Torrenterna måste ha det ursprungliga fil-/katalognamnet plus ett <code>.torrent</code>-suffix som deras filnamn. <a %(wikipedia_annas_archive)s>Annas Arkiv</a> har blivit det överlägset största skuggbiblioteket i världen, och det enda skuggbiblioteket i sin skala som är helt öppen källkod och öppen data. Nedan är en tabell från vår Datasets-sida (något modifierad): Vi uppnådde detta på tre sätt: Spegling av befintliga öppna data-skuggbibliotek (som Sci-Hub och Library Genesis). Hjälpa skuggbibliotek som vill vara mer öppna, men inte hade tid eller resurser att göra det (som Libgen seriekollektion). Skrapa bibliotek som inte vill dela i bulk (som Z-Library). För (2) och (3) hanterar vi nu en betydande samling av torrents själva (100-tals TB). Hittills har vi behandlat dessa samlingar som engångsföreteelser, vilket innebär skräddarsydd infrastruktur och dataorganisation för varje samling. Detta tillför betydande overhead till varje utgåva och gör det särskilt svårt att göra mer inkrementella utgåvor. Det är därför vi har beslutat att standardisera våra utgåvor. Detta är ett tekniskt blogginlägg där vi introducerar vår standard: <strong>Annas Arkivbehållare</strong>. Annas Arkivbehållare (AAC): standardisering av utgåvor från världens största skuggbibliotek Annas Arkiv har blivit det största skuggbiblioteket i världen, vilket kräver att vi standardiserar våra utgåvor. 300GB+ av bokomslag släppta Slutligen är vi glada att tillkännage en liten release. I samarbete med de som driver Libgen.rs-forken delar vi alla deras bokomslag via torrents och IPFS. Detta kommer att fördela belastningen av att visa omslagen bland fler maskiner och bevara dem bättre. I många (men inte alla) fall är bokomslagen inkluderade i filerna själva, så detta är en slags "härledd data". Men att ha det i IPFS är fortfarande mycket användbart för den dagliga driften av både Annas Arkiv och de olika Library Genesis-forkarna. Som vanligt kan du hitta denna release på Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi kommer inte att länka till det här, men du kan enkelt hitta det. Förhoppningsvis kan vi sakta ner vårt tempo lite nu när vi har ett anständigt alternativ till Z-Library. Denna arbetsbelastning är inte särskilt hållbar. Om du är intresserad av att hjälpa till med programmering, serverdrift eller bevarandearbete, tveka inte att kontakta oss. Det finns fortfarande mycket <a %(annas_archive)s>arbete att göra</a>. Tack för ditt intresse och stöd. Byt till ElasticSearch Vissa sökningar tog superlång tid, till den grad att de skulle uppta alla öppna anslutningar. Som standard har MySQL en minsta ordlängd, annars kan ditt index bli riktigt stort. Folk rapporterade att de inte kunde söka efter "Ben Hur". Sökningen var bara något snabb när den var helt laddad i minnet, vilket krävde att vi skaffade en dyrare maskin för att köra detta på, plus några kommandon för att förladda indexet vid start. Vi skulle inte ha kunnat utöka det enkelt för att bygga nya funktioner, som bättre <a %(wikipedia_cjk_characters)s>tokenisering för språk utan mellanslag</a>, filtrering/facettering, sortering, "menade du"-förslag, autokomplettering och så vidare. En av våra <a %(annas_archive)s>biljetter</a> var en blandning av problem med vårt söksystem. Vi använde MySQL fulltextsökning, eftersom vi ändå hade all vår data i MySQL. Men det hade sina begränsningar: Efter att ha pratat med en mängd experter bestämde vi oss för ElasticSearch. Det har inte varit perfekt (deras standard "menade du"-förslag och autokompletteringsfunktioner är dåliga), men överlag har det varit mycket bättre än MySQL för sökning. Vi är fortfarande inte <a %(youtube)s>för entusiastiska</a> över att använda det för någon kritisk data (även om de har gjort mycket <a %(elastic_co)s>framsteg</a>), men överlag är vi ganska nöjda med bytet. För tillfället har vi implementerat mycket snabbare sökning, bättre språksupport, bättre relevanssortering, olika sorteringsalternativ och filtrering på språk/boktyp/filtyp. Om du är nyfiken på hur det fungerar, <a %(annas_archive_l140)s>ta</a> <a %(annas_archive_l1115)s>en</a> <a %(annas_archive_l1635)s>titt</a>. Det är ganska tillgängligt, även om det skulle behöva några fler kommentarer… Annas Arkiv är helt öppen källkod Vi tror att information ska vara fri, och vår egen kod är inget undantag. Vi har släppt all vår kod på vår privat hostade Gitlab-instans: <a %(annas_archive)s>Annas Programvara</a>. Vi använder också ärendehanteraren för att organisera vårt arbete. Om du vill engagera dig i vår utveckling är detta en bra plats att börja. För att ge dig en smak av de saker vi arbetar med, ta vårt senaste arbete med prestandaförbättringar på klientsidan. Eftersom vi ännu inte har implementerat paginering, skulle vi ofta returnera mycket långa söksidor, med 100-200 resultat. Vi ville inte avbryta sökresultaten för tidigt, men detta innebar att det skulle sakta ner vissa enheter. För detta implementerade vi ett litet trick: vi omslöt de flesta sökresultaten i HTML-kommentarer (<code><!-- --></code>), och skrev sedan en liten Javascript som skulle upptäcka när ett resultat skulle bli synligt, vid vilken tidpunkt vi skulle avlägsna kommentaren: DOM "virtualisering" implementerad i 23 rader, inget behov av avancerade bibliotek! Detta är den typ av snabb pragmatisk kod som du får när du har begränsad tid och verkliga problem som behöver lösas. Det har rapporterats att vår sökning nu fungerar bra på långsamma enheter! En annan stor insats var att automatisera byggandet av databasen. När vi lanserade, drog vi bara ihop olika källor slumpmässigt. Nu vill vi hålla dem uppdaterade, så vi skrev en mängd skript för att ladda ner ny metadata från de två Library Genesis-forkarna och integrera dem. Målet är inte bara att göra detta användbart för vårt arkiv, utan att göra det enkelt för alla som vill experimentera med skuggbibliotekets metadata. Målet skulle vara en Jupyter-notebook som har alla möjliga intressanta metadata tillgängliga, så vi kan göra mer forskning som att ta reda på vilken <a %(blog)s>procentandel av ISBN:er som bevaras för alltid</a>. Slutligen har vi omarbetat vårt donationssystem. Du kan nu använda ett kreditkort för att direkt sätta in pengar på våra kryptoplånböcker, utan att egentligen behöva veta något om kryptovalutor. Vi kommer att fortsätta övervaka hur bra detta fungerar i praktiken, men det är en stor sak. Med Z-Library som stängs ner och dess (påstådda) grundare som arresteras, har vi arbetat dygnet runt för att erbjuda ett bra alternativ med Annas Arkiv (vi kommer inte att länka det här, men du kan Googla det). Här är några av de saker vi nyligen har uppnått. Annas Uppdatering: helt öppen källkod arkiv, ElasticSearch, 300GB+ av bokomslag Vi har arbetat dygnet runt för att erbjuda ett bra alternativ med Annas Arkiv. Här är några av de saker vi nyligen har uppnått. Analys Semantiska dubbletter (olika skanningar av samma bok) kan teoretiskt filtreras bort, men det är knepigt. När vi manuellt tittade igenom serierna hittade vi för många falska positiva. Det finns några dubbletter enbart genom MD5, vilket är relativt slösaktigt, men att filtrera bort dem skulle bara ge oss ungefär 1% in besparingar. I denna skala är det fortfarande ungefär 1TB, men också, i denna skala spelar 1TB egentligen ingen roll. Vi vill hellre inte riskera att av misstag förstöra data i denna process. Vi hittade en massa icke-bokdata, såsom filmer baserade på serietidningar. Det verkar också slösaktigt, eftersom dessa redan är allmänt tillgängliga genom andra medel. Men vi insåg att vi inte bara kunde filtrera bort filmfiler, eftersom det också finns <em>interaktiva serieböcker</em> som släpptes på datorn, som någon spelade in och sparade som filmer. I slutändan skulle allt vi kunde ta bort från samlingen bara spara några procent. Sedan kom vi ihåg att vi är datahamstrare, och de som kommer att spegla detta är också datahamstrare, så, "VAD MENAR DU MED ATT RADERA?!" :) När du får 95TB dumpat i ditt lagringskluster försöker du förstå vad som ens finns där… Vi gjorde en del analys för att se om vi kunde minska storleken lite, till exempel genom att ta bort dubbletter. Här är några av våra fynd: Vi presenterar därför för er, den fullständiga, omodifierade samlingen. Det är mycket data, men vi hoppas att tillräckligt många bryr sig om att dela den ändå. Samarbete Med tanke på dess storlek har denna samling länge funnits på vår önskelista, så efter vår framgång med att säkerhetskopiera Z-Library, satte vi siktet på denna samling. Först skrapade vi den direkt, vilket var en riktig utmaning, eftersom deras server inte var i bästa skick. Vi fick ungefär 15TB på detta sätt, men det gick långsamt. Lyckligtvis lyckades vi få kontakt med operatören av biblioteket, som gick med på att skicka oss all data direkt, vilket gick mycket snabbare. Det tog fortfarande mer än ett halvår att överföra och bearbeta all data, och vi var nära att förlora allt på grund av diskfel, vilket skulle ha inneburit att börja om från början. Denna erfarenhet har fått oss att tro att det är viktigt att få ut denna data så snabbt som möjligt, så att den kan speglas brett och vitt. Vi är bara en eller två olyckligt tajmade incidenter ifrån att förlora denna samling för alltid! Samlingen Att röra sig snabbt innebär att samlingen är lite oorganiserad… Låt oss ta en titt. Föreställ dig att vi har ett filsystem (som i verkligheten delas upp över torrents): Den första katalogen, <code>/repository</code>, är den mer strukturerade delen av detta. Denna katalog innehåller så kallade "tusen kataloger": kataloger var och en med tusen filer, som är inkrementellt numrerade i databasen. Katalogen <code>0</code> innehåller filer med comic_id 0–999, och så vidare. Detta är samma schema som Library Genesis har använt för sina skönlitterära och facklitterära samlingar. Idén är att varje "tusen katalog" automatiskt omvandlas till en torrent så snart den är fylld. Dock gjorde Libgen.li-operatören aldrig torrents för denna samling, och därför blev tusen katalogerna troligen obekväma, och gav plats åt "osorterade kataloger". Dessa är <code>/comics0</code> till <code>/comics4</code>. De innehåller alla unika katalogstrukturer, som troligen var logiska för att samla in filerna, men som inte är så logiska för oss nu. Lyckligtvis hänvisar metadata fortfarande direkt till alla dessa filer, så deras lagringsorganisation på disken spelar faktiskt ingen roll! Metadatan är tillgänglig i form av en MySQL-databas. Den kan laddas ner direkt från Libgen.li-webbplatsen, men vi kommer också att göra den tillgänglig i en torrent, tillsammans med vår egen tabell med alla MD5-hashar. <q>Dr. Barbara Gordon försöker förlora sig själv i bibliotekets vardagliga värld…</q> Libgen-förgreningar Först lite bakgrund. Du kanske känner till Library Genesis för deras episka boksamling. Färre människor vet att Library Genesis-volontärer har skapat andra projekt, såsom en stor samling av tidskrifter och standarddokument, en fullständig backup av Sci-Hub (i samarbete med grundaren av Sci-Hub, Alexandra Elbakyan), och faktiskt en massiv samling av serier. Vid något tillfälle gick olika operatörer av Library Genesis-spegelservrar skilda vägar, vilket ledde till den nuvarande situationen med ett antal olika "förgreningar", som alla fortfarande bär namnet Library Genesis. Libgen.li-förgreningen har unikt denna seriesamling, samt en stor tidskriftssamling (som vi också arbetar med). Insamling Vi släpper denna data i några stora bitar. Den första torrenten är av <code>/comics0</code>, som vi lade i en enorm 12TB .tar-fil. Det är bättre för din hårddisk och torrentprogramvara än en miljard mindre filer. Som en del av denna release gör vi en insamling. Vi siktar på att samla in $20,000 för att täcka drifts- och kontraktskostnader för denna samling, samt möjliggöra pågående och framtida projekt. Vi har några <em>massiva</em> på gång. <em>Vem stödjer jag med min donation?</em> Kort sagt: vi säkerhetskopierar all mänsklighetens kunskap och kultur och gör den lättillgänglig. All vår kod och data är öppen källkod, vi är ett helt volontärdrivet projekt, och vi har sparat 125TB böcker hittills (utöver Libgen och Scihubs befintliga torrents). I slutändan bygger vi ett svänghjul som möjliggör och uppmuntrar människor att hitta, skanna och säkerhetskopiera alla böcker i världen. Vi kommer att skriva om vår huvudplan i ett framtida inlägg. :) Om du donerar för ett 12 månaders "Amazing Archivist"-medlemskap ($780), får du <strong>“adoptera en torrent”</strong>, vilket innebär att vi kommer att sätta ditt användarnamn eller meddelande i filnamnet på en av torrentfilerna! Du kan donera genom att gå till <a %(wikipedia_annas_archive)s>Annas Arkiv</a> och klicka på "Donera"-knappen. Vi söker också fler volontärer: mjukvaruingenjörer, säkerhetsforskare, experter på anonyma betalningar och översättare. Du kan också stödja oss genom att tillhandahålla hostingtjänster. Och naturligtvis, vänligen dela våra torrents! Tack till alla som redan har stöttat oss så generöst! Ni gör verkligen en skillnad. Här är de torrents som släppts hittills (vi bearbetar fortfarande resten): Alla torrents kan hittas på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under "Datasets" (vi länkar inte direkt dit, så länkar till denna blogg inte tas bort från Reddit, Twitter, etc). Därifrån, följ länken till Tor-webbplatsen. <a %(news_ycombinator)s>Diskutera på Hacker News</a> Vad är nästa steg? En massa torrents är bra för långsiktig bevarande, men inte så mycket för daglig åtkomst. Vi kommer att arbeta med hostingpartners för att få upp all denna data på webben (eftersom Annas Arkiv inte värdar något direkt). Naturligtvis kommer du att kunna hitta dessa nedladdningslänkar på Annas Arkiv. Vi bjuder också in alla att göra saker med denna data! Hjälp oss att bättre analysera den, deduplicera den, lägga den på IPFS, remixa den, träna dina AI-modeller med den, och så vidare. Den är helt din, och vi kan inte vänta på att se vad du gör med den. Slutligen, som sagt tidigare, har vi fortfarande några massiva releaser på gång (om <em>någon</em> kunde <em>av misstag</em> skicka oss en dump av en <em>viss</em> ACS4-databas, vet du var du hittar oss...), samt bygga svänghjulet för att säkerhetskopiera alla böcker i världen. Så håll utkik, vi har precis börjat. - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Det största skuggbiblioteket för serier är troligen det av en särskild Library Genesis-fork: Libgen.li. Den enda administratören som driver den webbplatsen lyckades samla en galen samling serier på över 2 miljoner filer, totalt över 95TB. Men till skillnad från andra Library Genesis-samlingar var denna inte tillgänglig i bulk via torrents. Du kunde bara komma åt dessa serier individuellt via hans långsamma personliga server — en enda felpunkt. Tills idag! I det här inlägget kommer vi att berätta mer om denna samling och om vår insamling för att stödja mer av detta arbete. Annas Arkiv har säkerhetskopierat världens största skuggbibliotek för serier (95TB) — du kan hjälpa till att seeda det Världens största skuggbibliotek för serier hade en enda felpunkt... tills idag. Varning: detta blogginlägg har blivit föråldrat. Vi har beslutat att IPFS ännu inte är redo för prime time. Vi kommer fortfarande att länka till filer på IPFS från Annas Arkiv när det är möjligt, men vi kommer inte längre att vara värd för det själva, och vi rekommenderar inte andra att spegla med IPFS. Se vår Torrents-sida om du vill hjälpa till att bevara vår samling. Lägger 5 998 794 böcker på IPFS En mångfaldigande av kopior Tillbaka till vår ursprungliga fråga: hur kan vi påstå att vi bevarar våra samlingar för evigt? Huvudproblemet här är att vår samling har <a %(torrents_stats)s>vuxit</a> snabbt, genom att skrapa och öppna några massiva samlingar (utöver det fantastiska arbete som redan gjorts av andra öppna data-skuggbibliotek som Sci-Hub och Library Genesis). Denna tillväxt i data gör det svårare för samlingarna att speglas runt om i världen. Datastorage är dyrt! Men vi är optimistiska, särskilt när vi observerar följande tre trender. Den <a %(annas_archive_stats)s>totala storleken</a> på våra samlingar, under de senaste månaderna, uppdelad efter antal torrent-seedare. HDD-pristrender från olika källor (klicka för att se studien). <a %(critical_window_chinese)s>Kinesisk version 中文版</a>, diskutera på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Vi har plockat de lågt hängande frukterna Detta följer direkt från våra prioriteringar som diskuterats ovan. Vi föredrar att arbeta med att befria stora samlingar först. Nu när vi har säkrat några av de största samlingarna i världen, förväntar vi oss att vår tillväxt kommer att vara mycket långsammare. Det finns fortfarande en lång svans av mindre samlingar, och nya böcker skannas eller publiceras varje dag, men takten kommer sannolikt att vara mycket långsammare. Vi kanske fortfarande fördubblar eller till och med tredubblar i storlek, men över en längre tidsperiod. Förbättringar av OCR. Prioriteringar Vetenskaplig och teknisk programvarukod Fiktiva eller underhållningsversioner av allt ovanstående Geografiska data (t.ex. kartor, geologiska undersökningar) Interna data från företag eller regeringar (läckor) Mätdata som vetenskapliga mätningar, ekonomiska data, företagsrapporter Metadata-poster generellt (av fack- och skönlitteratur; av andra medier, konst, personer, etc; inklusive recensioner) Facklitteratur Facktidningar, tidningar, manualer Facktranskriptioner av föredrag, dokumentärer, podcaster Organiska data som DNA-sekvenser, växtfrön eller mikrobiella prover Akademiska artiklar, tidskrifter, rapporter Vetenskapliga och tekniska webbplatser, online-diskussioner Transkriptioner av juridiska eller rättsliga förfaranden Unikt i riskzonen för förstörelse (t.ex. genom krig, nedskärningar i finansiering, rättstvister eller politisk förföljelse) Sällsynta Unikt underfokuserade Varför bryr vi oss så mycket om artiklar och böcker? Låt oss lägga åt sidan vår grundläggande tro på bevarande i allmänhet — vi kanske skriver ett annat inlägg om det. Så varför artiklar och böcker specifikt? Svaret är enkelt: <strong>informationsdensitet</strong>. Per megabyte lagring lagrar skriven text mest information av alla medier. Medan vi bryr oss om både kunskap och kultur, bryr vi oss mer om det förstnämnda. Sammantaget finner vi en hierarki av informationsdensitet och vikten av bevarande som ser ungefär ut så här: Rankningen i denna lista är något godtycklig — flera punkter är oavgjorda eller har oenigheter inom vårt team — och vi glömmer förmodligen några viktiga kategorier. Men det här är ungefär hur vi prioriterar. Några av dessa punkter är för olika från de andra för att vi ska oroa oss (eller tas redan om hand av andra institutioner), såsom organiska data eller geografiska data. Men de flesta av punkterna i denna lista är faktiskt viktiga för oss. En annan stor faktor i vår prioritering är hur mycket risk en viss verk är. Vi föredrar att fokusera på verk som är: Slutligen bryr vi oss om skala. Vi har begränsad tid och pengar, så vi spenderar hellre en månad på att rädda 10 000 böcker än 1 000 böcker — om de är ungefär lika värdefulla och i riskzonen. <em><q>Det förlorade kan inte återfås; men låt oss rädda det som återstår: inte genom valv och lås som skyddar dem från allmänhetens ögon och användning, genom att överlämna dem till tidens avfall, utan genom en sådan mångfaldigande av kopior, som ska placera dem bortom olyckans räckvidd.</q></em><br>— Thomas Jefferson, 1791 Skuggbibliotek Kod kan vara öppen källkod på Github, men Github som helhet kan inte enkelt speglas och därmed bevaras (även om det i detta specifika fall finns tillräckligt distribuerade kopior av de flesta kodförråd) Metadataregister kan fritt ses på Worldcat-webbplatsen, men inte laddas ner i bulk (tills vi <a %(worldcat_scrape)s>skrapade</a> dem) Reddit är gratis att använda, men har nyligen infört strikta anti-scraping-åtgärder, i kölvattnet av datahungrig LLM-träning (mer om det senare) Det finns många organisationer som har liknande uppdrag och liknande prioriteringar. Faktum är att det finns bibliotek, arkiv, laboratorier, museer och andra institutioner som har i uppdrag att bevara av detta slag. Många av dessa är välfinansierade, av regeringar, individer eller företag. Men de har en massiv blind fläck: rättssystemet. Här ligger den unika rollen för skuggbibliotek, och anledningen till att Annas Arkiv finns. Vi kan göra saker som andra institutioner inte får göra. Nu är det inte (ofta) så att vi kan arkivera material som är olagliga att bevara någon annanstans. Nej, det är lagligt på många platser att bygga ett arkiv med vilka böcker, papper, tidningar och så vidare. Men vad juridiska arkiv ofta saknar är <strong>redundans och långvarighet</strong>. Det finns böcker av vilka endast ett exemplar existerar i något fysiskt bibliotek någonstans. Det finns metadataregister som skyddas av ett enda företag. Det finns tidningar som endast bevaras på mikrofilm i ett enda arkiv. Bibliotek kan få budgetnedskärningar, företag kan gå i konkurs, arkiv kan bombas och brännas ner till grunden. Detta är inte hypotetiskt — det händer hela tiden. Det vi unikt kan göra på Annas Arkiv är att lagra många kopior av verk, i stor skala. Vi kan samla in artiklar, böcker, tidskrifter och mer, och distribuera dem i bulk. Vi gör detta för närvarande genom torrents, men de exakta teknologierna spelar ingen roll och kommer att förändras över tid. Det viktiga är att få många kopior distribuerade över hela världen. Detta citat från över 200 år sedan är fortfarande aktuellt: En snabb notering om public domain. Eftersom Annas Arkiv unikt fokuserar på aktiviteter som är olagliga på många platser runt om i världen, bryr vi oss inte om allmänt tillgängliga samlingar, såsom public domain-böcker. Juridiska enheter tar ofta redan väl hand om det. Det finns dock överväganden som gör att vi ibland arbetar med offentligt tillgängliga samlingar: - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Lagringskostnaderna fortsätter att sjunka exponentiellt 3. Förbättringar i informationsdensitet Vi lagrar för närvarande böcker i de råformat som de ges till oss. Visst, de är komprimerade, men ofta är de fortfarande stora skanningar eller fotografier av sidor. Fram tills nu har de enda alternativen för att minska den totala storleken på vår samling varit genom mer aggressiv komprimering eller deduplicering. Men för att få tillräckligt stora besparingar är båda för förlustfyllda för vår smak. Kraftig komprimering av foton kan göra texten knappt läsbar. Och deduplicering kräver hög säkerhet om att böckerna är exakt desamma, vilket ofta är för osäkert, särskilt om innehållet är detsamma men skanningarna är gjorda vid olika tillfällen. Det har alltid funnits ett tredje alternativ, men dess kvalitet har varit så usel att vi aldrig övervägt det: <strong>OCR, eller optisk teckenigenkänning</strong>. Detta är processen att konvertera foton till vanlig text genom att använda AI för att identifiera tecknen i fotona. Verktyg för detta har funnits länge och har varit ganska bra, men "ganska bra" är inte tillräckligt för bevarandeändamål. Men nyligen har multimodala djupinlärningsmodeller gjort extremt snabba framsteg, även om de fortfarande är kostsamma. Vi förväntar oss att både noggrannhet och kostnader kommer att förbättras dramatiskt under de kommande åren, till den punkt där det blir realistiskt att tillämpa på hela vårt bibliotek. När det händer kommer vi troligen fortfarande att bevara de ursprungliga filerna, men dessutom kan vi ha en mycket mindre version av vårt bibliotek som de flesta kommer att vilja spegla. Poängen är att råtext i sig komprimeras ännu bättre och är mycket lättare att deduplicera, vilket ger oss ännu fler besparingar. Sammantaget är det inte orealistiskt att förvänta sig minst en 5-10x minskning av den totala filstorleken, kanske ännu mer. Även med en konservativ 5x minskning skulle vi titta på <strong>$1,000–$3,000 om 10 år även om vårt bibliotek tredubblas i storlek</strong>. Vid tidpunkten för skrivandet är <a %(diskprices)s>diskpriser</a> per TB cirka $12 för nya diskar, $8 för begagnade diskar och $4 för band. Om vi är konservativa och bara tittar på nya diskar, betyder det att lagra en petabyte kostar cirka $12,000. Om vi antar att vårt bibliotek kommer att tredubblas från 900TB till 2,7PB, skulle det innebära $32,400 för att spegla hela vårt bibliotek. Lägger vi till el, kostnad för annan hårdvara och så vidare, låt oss runda upp det till $40,000. Eller med band mer som $15,000–$20,000. Å ena sidan är <strong>$15,000–$40,000 för summan av all mänsklig kunskap ett kap</strong>. Å andra sidan är det lite brant att förvänta sig massor av fullständiga kopior, särskilt om vi också vill att dessa personer ska fortsätta seeda sina torrents till förmån för andra. Det är idag. Men framstegen går framåt: Hårddiskkostnader per TB har ungefär delats i tredjedelar under de senaste 10 åren, och kommer sannolikt att fortsätta sjunka i liknande takt. Band verkar vara på en liknande bana. SSD-priserna sjunker ännu snabbare och kan ta över HDD-priserna i slutet av decenniet. Om detta håller, kan vi om 10 år kanske se på endast $5,000–$13,000 för att spegla hela vår samling (1/3), eller ännu mindre om vi växer mindre i storlek. Även om det fortfarande är mycket pengar, kommer detta att vara möjligt för många människor. Och det kan bli ännu bättre på grund av nästa punkt… På Annas Arkiv får vi ofta frågan hur vi kan påstå att vi bevarar våra samlingar för evigt, när den totala storleken redan närmar sig 1 Petabyte (1000 TB) och fortfarande växer. I denna artikel kommer vi att titta på vår filosofi och se varför det kommande decenniet är avgörande för vårt uppdrag att bevara mänsklighetens kunskap och kultur. Kritiskt fönster Om dessa prognoser är korrekta, behöver vi <strong>bara vänta ett par år</strong> innan hela vår samling kommer att speglas brett. Således, med Thomas Jeffersons ord, "placerad bortom olyckans räckhåll." Tyvärr har framväxten av LLM:er och deras datahungriga träning satt många upphovsrättsinnehavare på defensiven. Ännu mer än de redan var. Många webbplatser gör det svårare att skrapa och arkivera, stämningar flyger runt, och samtidigt fortsätter fysiska bibliotek och arkiv att försummas. Vi kan bara förvänta oss att dessa trender fortsätter att förvärras, och många verk kommer att gå förlorade långt innan de går in i det offentliga området. <strong>Vi står på tröskeln till en revolution inom bevarande, men <q>det förlorade kan inte återvinnas.</q></strong> Vi har ett kritiskt fönster på cirka 5-10 år under vilket det fortfarande är ganska dyrt att driva ett skuggbibliotek och skapa många speglar runt om i världen, och under vilket tillgången ännu inte har stängts helt. Om vi kan överbrygga detta fönster, så kommer vi verkligen ha bevarat mänsklighetens kunskap och kultur för evigt. Vi bör inte låta denna tid gå till spillo. Vi bör inte låta detta kritiska fönster stängas för oss. Låt oss sätta igång. Det kritiska fönstret för skuggbibliotek Hur kan vi påstå att vi bevarar våra samlingar för evigt, när de redan närmar sig 1 PB? Samling Lite mer information om samlingen. <a %(duxiu)s>Duxiu</a> är en massiv databas med skannade böcker, skapad av <a %(chaoxing)s>SuperStar Digital Library Group</a>. De flesta är akademiska böcker, skannade för att göra dem tillgängliga digitalt för universitet och bibliotek. För vår engelsktalande publik har <a %(library_princeton)s>Princeton</a> och <a %(guides_lib_uw)s>University of Washington</a> bra översikter. Det finns också en utmärkt artikel som ger mer bakgrund: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (sök upp den i Annas Arkiv). Böckerna från Duxiu har länge piratkopierats på det kinesiska internet. Vanligtvis säljs de för mindre än en dollar av återförsäljare. De distribueras vanligtvis med den kinesiska motsvarigheten till Google Drive, som ofta har hackats för att tillåta mer lagringsutrymme. Några tekniska detaljer kan hittas <a %(github_duty_machine)s>här</a> och <a %(github_821_github_io)s>här</a>. Även om böckerna har distribuerats halv-offentligt, är det ganska svårt att få tag på dem i bulk. Vi hade detta högt på vår TODO-lista och avsatte flera månader av heltidsarbete för det. Men nyligen kontaktade en otrolig, fantastisk och talangfull volontär oss och berättade att de redan hade gjort allt detta arbete — till stor kostnad. De delade hela samlingen med oss, utan att förvänta sig något i gengäld, förutom garantin om långsiktig bevarande. Verkligen anmärkningsvärt. De gick med på att be om hjälp på detta sätt för att få samlingen OCR:ad. Samlingen består av 7 543 702 filer. Detta är mer än Library Genesis facklitteratur (cirka 5,3 miljoner). Den totala filstorleken är cirka 359TB (326TiB) i dess nuvarande form. Vi är öppna för andra förslag och idéer. Kontakta oss bara. Kolla in Annas Arkiv för mer information om våra samlingar, bevarandeinsatser och hur du kan hjälpa till. Tack! Exempelsidor För att bevisa för oss att du har en bra pipeline, här är några exempelsidor att börja med, från en bok om supraledare. Din pipeline bör hantera matematik, tabeller, diagram, fotnoter och så vidare korrekt. Skicka dina bearbetade sidor till vår e-post. Om de ser bra ut, kommer vi att skicka fler till dig privat, och vi förväntar oss att du snabbt kan köra din pipeline på dem också. När vi är nöjda kan vi göra en överenskommelse. - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Kinesisk version 中文版</a>, <a %(news_ycombinator)s>Diskutera på Hacker News</a> Detta är ett kort blogginlägg. Vi letar efter ett företag eller en institution som kan hjälpa oss med OCR och textutvinning för en massiv samling vi förvärvat, i utbyte mot exklusiv tidig tillgång. Efter embargoperioden kommer vi naturligtvis att släppa hela samlingen. Akademiska texter av hög kvalitet är extremt användbara för träning av LLM. Även om vår samling är kinesisk, bör den vara användbar för träning av engelska LLM:er, eftersom modeller verkar koda koncept och kunskap oavsett källspråk. För detta behöver texten extraheras från skanningarna. Vad får Annas Arkiv ut av det? Fulltextsökning av böckerna för sina användare. Eftersom våra mål överensstämmer med LLM-utvecklares, letar vi efter en samarbetspartner. Vi är villiga att ge dig <strong>exklusiv tidig tillgång till denna samling i bulk under 1 år</strong>, om du kan utföra korrekt OCR och textextraktion. Om du är villig att dela hela koden för din pipeline med oss, skulle vi vara villiga att embargera samlingen längre. Exklusiv tillgång för LLM-företag till världens största kinesiska facklitteratursamling <em><strong>TL;DR:</strong> Annas Arkiv förvärvade en unik samling av 7,5 miljoner / 350TB kinesiska facklitteraturböcker — större än Library Genesis. Vi är villiga att ge ett LLM-företag exklusiv tillgång, i utbyte mot högkvalitativ OCR och textutvinning.</em> Systemarkitektur Så låt oss säga att du hittade några företag som är villiga att vara värd för din webbplats utan att stänga ner dig — låt oss kalla dessa “frihetsälskande leverantörer” 😄. Du kommer snabbt att upptäcka att det är ganska dyrt att vara värd för allt hos dem, så du kanske vill hitta några “billiga leverantörer” och göra den faktiska värdskapet där, genom att proxyera genom de frihetsälskande leverantörerna. Om du gör det rätt kommer de billiga leverantörerna aldrig att veta vad du är värd för, och aldrig få några klagomål. Med alla dessa leverantörer finns det en risk att de stänger ner dig ändå, så du behöver också redundans. Vi behöver detta på alla nivåer i vår stack. Ett något frihetsälskande företag som har satt sig i en intressant position är Cloudflare. De har <a %(blog_cloudflare)s>argumenterat</a> att de inte är en värdleverantör, utan en tjänst, som en ISP. De är därför inte föremål för DMCA eller andra nedtagningsförfrågningar, och vidarebefordrar alla förfrågningar till din faktiska värdleverantör. De har gått så långt som att gå till domstol för att skydda denna struktur. Vi kan därför använda dem som ett annat lager av caching och skydd. Cloudflare accepterar inte anonyma betalningar, så vi kan bara använda deras gratisplan. Detta innebär att vi inte kan använda deras lastbalansering eller failover-funktioner. Vi har därför <a %(annas_archive_l255)s>implementerat detta själva</a> på domännivå. Vid sidladdning kommer webbläsaren att kontrollera om den aktuella domänen fortfarande är tillgänglig, och om inte, skriver den om alla URL:er till en annan domän. Eftersom Cloudflare cachelagrar många sidor, innebär detta att en användare kan landa på vår huvuddomän, även om proxyservern är nere, och sedan vid nästa klick flyttas över till en annan domän. Vi har fortfarande också vanliga operativa bekymmer att hantera, såsom övervakning av serverhälsa, loggning av backend- och frontend-fel, och så vidare. Vår failover-arkitektur tillåter mer robusthet även på denna front, till exempel genom att köra en helt annan uppsättning servrar på en av domänerna. Vi kan till och med köra äldre versioner av koden och datasets på denna separata domän, ifall en kritisk bugg i huvudversionen går obemärkt förbi. Vi kan också skydda oss mot att Cloudflare vänder sig mot oss, genom att ta bort det från en av domänerna, såsom denna separata domän. Olika permutationer av dessa idéer är möjliga. Slutsats Det har varit en intressant upplevelse att lära sig hur man sätter upp en robust och motståndskraftig skuggbibliotekssökmotor. Det finns massor av fler detaljer att dela i senare inlägg, så låt mig veta vad du vill veta mer om! Som alltid söker vi donationer för att stödja detta arbete, så se till att kolla in Donationssidan på Annas Arkiv. Vi söker också andra typer av stöd, såsom bidrag, långsiktiga sponsorer, högriskbetalningsleverantörer, kanske till och med (smakfulla!) annonser. Och om du vill bidra med din tid och dina färdigheter, letar vi alltid efter utvecklare, översättare och så vidare. Tack för ditt intresse och stöd. Innovationstokens Låt oss börja med vår teknikstack. Den är medvetet tråkig. Vi använder Flask, MariaDB och ElasticSearch. Det är bokstavligen allt. Sökning är i stort sett ett löst problem, och vi har inte för avsikt att uppfinna det på nytt. Dessutom måste vi spendera våra <a %(mcfunley)s>innovationstokens</a> på något annat: att inte bli nedstängda av myndigheterna. Så hur laglig eller olaglig är egentligen Annas Arkiv? Det beror mest på den juridiska jurisdiktionen. De flesta länder tror på någon form av upphovsrätt, vilket innebär att personer eller företag tilldelas ett exklusivt monopol på vissa typer av verk under en viss tidsperiod. Som en parentes, på Annas Arkiv anser vi att även om det finns vissa fördelar, är upphovsrätt överlag en netto-negativ för samhället — men det är en historia för en annan gång. Detta exklusiva monopol på vissa verk innebär att det är olagligt för någon utanför detta monopol att direkt distribuera dessa verk — inklusive oss. Men Annas Arkiv är en sökmotor som inte direkt distribuerar dessa verk (åtminstone inte på vår clearnet-webbplats), så vi borde vara okej, eller hur? Inte riktigt. I många jurisdiktioner är det inte bara olagligt att distribuera upphovsrättsskyddade verk, utan också att länka till platser som gör det. Ett klassiskt exempel på detta är USA:s DMCA-lag. Det är den striktaste änden av spektrumet. I andra änden av spektrumet kan det teoretiskt finnas länder utan några upphovsrättslagar alls, men dessa existerar egentligen inte. I stort sett alla länder har någon form av upphovsrättslagstiftning. Tillämpningen är en annan historia. Det finns gott om länder med regeringar som inte bryr sig om att upprätthålla upphovsrättslagar. Det finns också länder mellan de två extremerna, som förbjuder distribution av upphovsrättsskyddade verk, men inte förbjuder att länka till sådana verk. En annan övervägning är på företagsnivå. Om ett företag verkar i en jurisdiktion som inte bryr sig om upphovsrätt, men företaget självt inte är villigt att ta någon risk, kan de stänga ner din webbplats så snart någon klagar på den. Slutligen är en stor övervägning betalningar. Eftersom vi behöver förbli anonyma kan vi inte använda traditionella betalningsmetoder. Detta lämnar oss med kryptovalutor, och endast en liten delmängd av företag stöder dessa (det finns virtuella betalkort betalda med krypto, men de accepteras ofta inte). - Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Jag driver <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, världens största open-source ideella sökmotor för <a %(wikipedia_shadow_library)s>skuggbibliotek</a>, som Sci-Hub, Library Genesis och Z-Library. Vårt mål är att göra kunskap och kultur lättillgänglig, och i slutändan bygga en gemenskap av människor som tillsammans arkiverar och bevarar <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alla böcker i världen</a>. I denna artikel kommer jag att visa hur vi driver denna webbplats, och de unika utmaningar som följer med att driva en webbplats med tveksam juridisk status, eftersom det inte finns någon “AWS för skuggbibliotek”. <em>Kolla också in systerartikeln <a %(blog_how_to_become_a_pirate_archivist)s>Hur man blir en piratarkivarie</a>.</em> Hur man driver ett skuggbibliotek: verksamhet på Annas Arkiv Det finns ingen <q>AWS för skuggbibliotek,</q> så hur driver vi Annas Arkiv? Verktyg Applikationsserver: Flask, MariaDB, ElasticSearch, Docker. Utveckling: Gitlab, Weblate, Zulip. Serverhantering: Ansible, Checkmk, UFW. Onion statisk hosting: Tor, Nginx. Proxyserver: Varnish. Låt oss titta på vilka verktyg vi använder för att uppnå allt detta. Detta utvecklas mycket när vi stöter på nya problem och hittar nya lösningar. Det finns några beslut som vi har gått fram och tillbaka med. Ett är kommunikationen mellan servrar: vi brukade använda Wireguard för detta, men upptäckte att det ibland slutar att överföra data, eller bara överför data i en riktning. Detta hände med flera olika Wireguard-inställningar som vi provade, såsom <a %(github_costela_wesher)s>wesher</a> och <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi försökte också tunnla portar över SSH, med hjälp av autossh och sshuttle, men stötte på <a %(github_sshuttle)s>problem där</a> (även om det fortfarande inte är klart för mig om autossh lider av TCP-over-TCP-problem eller inte — det känns bara som en skakig lösning för mig men kanske är det faktiskt okej?). Istället återgick vi till direkta anslutningar mellan servrar, och dolde att en server körs på de billiga leverantörerna genom att använda IP-filtrering med UFW. Detta har nackdelen att Docker inte fungerar bra med UFW, om du inte använder <code>network_mode: "host"</code>. Allt detta är lite mer felbenäget, eftersom du kommer att exponera din server för internet med bara en liten felkonfiguration. Kanske borde vi gå tillbaka till autossh — feedback skulle vara mycket välkommet här. Vi har också gått fram och tillbaka mellan Varnish och Nginx. Vi gillar för närvarande Varnish, men det har sina egenheter och skarpa kanter. Detsamma gäller för Checkmk: vi älskar det inte, men det fungerar för tillfället. Weblate har varit okej men inte otroligt — jag fruktar ibland att det kommer att förlora mina data när jag försöker synkronisera det med vårt git-repo. Flask har varit bra överlag, men det har några konstiga egenheter som har kostat mycket tid att felsöka, såsom att konfigurera anpassade domäner, eller problem med dess SqlAlchemy-integration. Hittills har de andra verktygen varit fantastiska: vi har inga allvarliga klagomål om MariaDB, ElasticSearch, Gitlab, Zulip, Docker och Tor. Alla dessa har haft några problem, men inget alltför allvarligt eller tidskrävande. Gemenskap Den första utmaningen kan vara en överraskande sådan. Det är inte ett tekniskt problem eller ett juridiskt problem. Det är ett psykologiskt problem: att göra detta arbete i skuggorna kan vara otroligt ensamt. Beroende på vad du planerar att göra och din hotmodell kan du behöva vara mycket försiktig. I ena änden av spektrumet har vi personer som Alexandra Elbakyan*, grundaren av Sci-Hub, som är mycket öppen med sina aktiviteter. Men hon löper stor risk att bli arresterad om hon skulle besöka ett västerländskt land vid denna tidpunkt och kan riskera decennier i fängelse. Är det en risk du är villig att ta? Vi är i andra änden av spektrumet; mycket noga med att inte lämna några spår och ha stark operativ säkerhet. * Som nämnts på HN av "ynno", ville Alexandra initialt inte bli känd: "Hennes servrar var inställda på att avge detaljerade felmeddelanden från PHP, inklusive fullständig sökväg till den felande källfilen, som fanns under katalogen /home/<USER>" Så, använd slumpmässiga användarnamn på de datorer du använder för detta, ifall du felkonfigurerar något. Denna hemlighet kommer dock med en psykologisk kostnad. De flesta älskar att bli erkända för det arbete de gör, och ändå kan du inte ta någon kredit för detta i verkliga livet. Även enkla saker kan vara utmanande, som när vänner frågar vad du har haft för dig (vid något tillfälle blir "leka med min NAS / homelab" gammalt). Det är därför det är så viktigt att hitta en gemenskap. Du kan ge upp en del operativ säkerhet genom att anförtro dig åt några mycket nära vänner, som du vet att du kan lita djupt på. Även då bör du vara försiktig med att inte skriva ner något, ifall de måste överlämna sina e-postmeddelanden till myndigheterna, eller om deras enheter är komprometterade på något annat sätt. Ännu bättre är att hitta några andra pirater. Om dina nära vänner är intresserade av att gå med dig, fantastiskt! Annars kanske du kan hitta andra online. Tyvärr är detta fortfarande en nischgemenskap. Hittills har vi bara hittat ett fåtal andra som är aktiva inom detta område. Bra startplatser verkar vara Library Genesis-forumen och r/DataHoarder. Archive Team har också likasinnade individer, även om de verkar inom lagen (även om det är i vissa gråzoner av lagen). De traditionella "warez"- och piratscenerna har också folk som tänker på liknande sätt. Vi är öppna för idéer om hur vi kan främja gemenskap och utforska idéer. Känn dig fri att skicka ett meddelande till oss på Twitter eller Reddit. Kanske skulle vi kunna arrangera någon form av forum eller chattgrupp. En utmaning är att detta lätt kan bli censurerat när man använder vanliga plattformar, så vi skulle behöva vara värdar för det själva. Det finns också en avvägning mellan att ha dessa diskussioner helt offentliga (mer potentiellt engagemang) kontra att göra dem privata (inte låta potentiella "mål" veta att vi är på väg att skrapa dem). Vi måste fundera på det. Låt oss veta om du är intresserad av detta! Slutsats Förhoppningsvis är detta till hjälp för nyblivna piratarkivarier. Vi är glada att välkomna dig till denna värld, så tveka inte att höra av dig. Låt oss bevara så mycket av världens kunskap och kultur som vi kan, och spegla det vida och brett. Projekt 4. Dataval Ofta kan du använda metadata för att lista ut en rimlig delmängd av data att ladda ner. Även om du till slut vill ladda ner all data, kan det vara användbart att prioritera de viktigaste objekten först, ifall du blir upptäckt och försvar förbättras, eller för att du skulle behöva köpa fler diskar, eller helt enkelt för att något annat dyker upp i ditt liv innan du kan ladda ner allt. Till exempel kan en samling ha flera utgåvor av samma underliggande resurs (som en bok eller en film), där en är markerad som den bästa kvaliteten. Att spara dessa utgåvor först skulle vara mycket logiskt. Du kanske till slut vill spara alla utgåvor, eftersom metadata i vissa fall kan vara felaktigt taggad, eller det kan finnas okända kompromisser mellan utgåvor (till exempel kan "bästa utgåvan" vara bäst på de flesta sätt men sämre på andra sätt, som en film med högre upplösning men utan undertexter). Du kan också söka i din metadata-databas för att hitta intressanta saker. Vad är den största filen som är värd, och varför är den så stor? Vad är den minsta filen? Finns det intressanta eller oväntade mönster när det gäller vissa kategorier, språk och så vidare? Finns det dubbletter eller mycket liknande titlar? Finns det mönster för när data lades till, som en dag då många filer lades till samtidigt? Du kan ofta lära dig mycket genom att titta på datasetet på olika sätt. I vårt fall deduplicerade vi Z-Library-böcker mot md5-hasharna i Library Genesis, vilket sparade mycket nedladdningstid och diskutrymme. Detta är dock en ganska unik situation. I de flesta fall finns det inga omfattande databaser över vilka filer som redan är ordentligt bevarade av andra pirater. Detta i sig är en stor möjlighet för någon där ute. Det skulle vara fantastiskt att ha en regelbundet uppdaterad översikt över saker som musik och filmer som redan är allmänt seedade på torrentsajter, och därför är lägre prioritet att inkludera i piratspeglar. 6. Distribution Du har datan, vilket ger dig världens första piratspegel av ditt mål (troligtvis). På många sätt är den svåraste delen över, men den mest riskfyllda delen ligger fortfarande framför dig. Trots allt har du hittills varit diskret; flugit under radarn. Allt du behövde göra var att använda en bra VPN hela tiden, inte fylla i dina personliga uppgifter i några formulär (självklart), och kanske använda en speciell webbläsarsession (eller till och med en annan dator). Nu måste du distribuera datan. I vårt fall ville vi först bidra med böckerna tillbaka till Library Genesis, men upptäckte snabbt svårigheterna med det (skönlitteratur vs facklitteratur sortering). Så vi bestämde oss för distribution med hjälp av Library Genesis-stil torrents. Om du har möjlighet att bidra till ett befintligt projekt, kan det spara dig mycket tid. Det finns dock inte många välorganiserade piratspeglar där ute för närvarande. Så låt oss säga att du bestämmer dig för att distribuera torrents själv. Försök att hålla dessa filer små, så att de är lätta att spegla på andra webbplatser. Du måste sedan seeda torrents själv, samtidigt som du förblir anonym. Du kan använda en VPN (med eller utan port forwarding), eller betala med tvättade Bitcoins för en Seedbox. Om du inte vet vad några av dessa termer betyder, har du en del läsning att göra, eftersom det är viktigt att du förstår riskavvägningarna här. Du kan hosta torrentfilerna själva på befintliga torrentsajter. I vårt fall valde vi att faktiskt hosta en webbplats, eftersom vi också ville sprida vår filosofi på ett tydligt sätt. Du kan göra detta själv på ett liknande sätt (vi använder Njalla för våra domäner och hosting, betalat med tvättade Bitcoins), men tveka inte att kontakta oss för att låta oss hosta dina torrents. Vi strävar efter att bygga ett omfattande index över piratspeglar över tid, om denna idé får fäste. När det gäller VPN-val har mycket redan skrivits om detta, så vi upprepar bara det allmänna rådet att välja efter rykte. Faktiska domstolstestade no-log-policies med långa spår av att skydda integritet är det lägsta riskalternativet, enligt vår mening. Notera att även när du gör allt rätt, kan du aldrig nå noll risk. Till exempel, när du seedar dina torrents, kan en mycket motiverad statlig aktör troligen titta på inkommande och utgående dataflöden för VPN-servrar och dra slutsatser om vem du är. Eller så kan du helt enkelt göra ett misstag. Vi har förmodligen redan gjort det, och kommer att göra det igen. Lyckligtvis bryr sig inte stater <em>så</em> mycket om piratkopiering. Ett beslut att fatta för varje projekt är om du ska publicera det med samma identitet som tidigare eller inte. Om du fortsätter att använda samma namn, kan misstag i operativ säkerhet från tidigare projekt komma tillbaka och bita dig. Men att publicera under olika namn innebär att du inte bygger ett mer varaktigt rykte. Vi valde att ha stark operativ säkerhet från början så att vi kan fortsätta använda samma identitet, men vi tvekar inte att publicera under ett annat namn om vi gör ett misstag eller om omständigheterna kräver det. Att sprida ordet kan vara knepigt. Som vi sa, detta är fortfarande en nischad gemenskap. Vi postade ursprungligen på Reddit, men fick verkligen genomslag på Hacker News. För närvarande är vår rekommendation att posta det på några ställen och se vad som händer. Och återigen, kontakta oss. Vi skulle älska att sprida ordet om fler piratarkivinsatser. 1. Domänval / filosofi Det finns ingen brist på kunskap och kulturarv att rädda, vilket kan vara överväldigande. Därför är det ofta användbart att ta en stund och tänka på vad ditt bidrag kan vara. Alla har olika sätt att tänka på detta, men här är några frågor du kan ställa dig själv: I vårt fall brydde vi oss särskilt om det långsiktiga bevarandet av vetenskap. Vi kände till Library Genesis, och hur det speglades helt många gånger med hjälp av torrents. Vi älskade den idén. Sedan en dag försökte en av oss hitta några vetenskapliga läroböcker på Library Genesis, men kunde inte hitta dem, vilket ifrågasatte hur komplett det verkligen var. Vi sökte sedan efter dessa läroböcker online och hittade dem på andra ställen, vilket planterade fröet för vårt projekt. Även innan vi kände till Z-Library hade vi idén att inte försöka samla alla dessa böcker manuellt, utan att fokusera på att spegla befintliga samlingar och bidra med dem tillbaka till Library Genesis. Vilka färdigheter har du som du kan använda till din fördel? Till exempel, om du är en expert på online-säkerhet, kan du hitta sätt att övervinna IP-blockeringar för säkra mål. Om du är bra på att organisera gemenskaper, kanske du kan samla några människor kring ett mål. Det är dock användbart att kunna lite programmering, om inte annat för att hålla god operativ säkerhet genom hela processen. Vad skulle vara ett högavkastande område att fokusera på? Om du ska spendera X timmar på piratarkivering, hur kan du då få största möjliga "bang for your buck"? Vilka unika sätt tänker du på detta? Du kanske har några intressanta idéer eller tillvägagångssätt som andra kan ha missat. Hur mycket tid har du för detta? Vårt råd skulle vara att börja smått och göra större projekt när du får kläm på det, men det kan bli allt uppslukande. Varför är du intresserad av detta? Vad brinner du för? Om vi kan få en massa människor som alla arkiverar de saker de specifikt bryr sig om, skulle det täcka mycket! Du kommer att veta mycket mer än genomsnittspersonen om din passion, som vilken viktig data som ska sparas, vilka de bästa samlingarna och online-gemenskaperna är, och så vidare. 3. Metadata-skrapning Datum tillagt/ändrat: så att du kan komma tillbaka senare och ladda ner filer som du inte laddade ner tidigare (även om du ofta också kan använda ID eller hash för detta). Hash (md5, sha1): för att bekräfta att du laddade ner filen korrekt. ID: kan vara något internt ID, men ID:n som ISBN eller DOI är också användbara. Filnamn / plats Beskrivning, kategori, taggar, författare, språk, etc. Storlek: för att beräkna hur mycket diskutrymme du behöver. Låt oss bli lite mer tekniska här. För att faktiskt skrapa metadata från webbplatser har vi hållit det ganska enkelt. Vi använder Python-skript, ibland curl, och en MySQL-databas för att lagra resultaten i. Vi har inte använt någon avancerad skrapningsprogramvara som kan kartlägga komplexa webbplatser, eftersom vi hittills bara behövde skrapa en eller två typer av sidor genom att bara numrera genom id:n och analysera HTML. Om det inte finns lätt numrerade sidor kan du behöva en riktig crawler som försöker hitta alla sidor. Innan du börjar skrapa en hel webbplats, försök göra det manuellt ett tag. Gå igenom några dussin sidor själv för att få en känsla för hur det fungerar. Ibland kommer du redan att stöta på IP-blockeringar eller annat intressant beteende på detta sätt. Detsamma gäller för dataskrapning: innan du går för djupt in i detta mål, se till att du faktiskt kan ladda ner dess data effektivt. För att kringgå begränsningar finns det några saker du kan prova. Finns det några andra IP-adresser eller servrar som är värd för samma data men inte har samma begränsningar? Finns det några API-slutpunkter som inte har begränsningar, medan andra har det? Vid vilken nedladdningshastighet blir din IP blockerad, och hur länge? Eller blir du inte blockerad utan nedströpt? Vad händer om du skapar ett användarkonto, hur förändras saker då? Kan du använda HTTP/2 för att hålla anslutningar öppna, och ökar det hastigheten med vilken du kan begära sidor? Finns det sidor som listar flera filer på en gång, och är informationen som listas där tillräcklig? Saker du förmodligen vill spara inkluderar: Vi gör vanligtvis detta i två steg. Först laddar vi ner de råa HTML-filerna, vanligtvis direkt in i MySQL (för att undvika massor av små filer, vilket vi pratar mer om nedan). Sedan, i ett separat steg, går vi igenom dessa HTML-filer och analyserar dem till faktiska MySQL-tabeller. På detta sätt behöver du inte ladda ner allt från början om du upptäcker ett misstag i din analyskod, eftersom du bara kan bearbeta HTML-filerna med den nya koden. Det är också ofta lättare att parallellisera bearbetningssteget, vilket sparar tid (och du kan skriva bearbetningskoden medan skrapningen körs, istället för att behöva skriva båda stegen på en gång). Slutligen, notera att för vissa mål är metadata-scraping allt som finns. Det finns några enorma metadata-samlingar där ute som inte är ordentligt bevarade. Titel Domänval / filosofi: Var vill du ungefär fokusera, och varför? Vilka är dina unika passioner, färdigheter och omständigheter som du kan använda till din fördel? Målval: Vilken specifik samling kommer du att spegla? Metadata-skrapning: Katalogisering av information om filerna, utan att faktiskt ladda ner de (ofta mycket större) filerna själva. Dataval: Baserat på metadata, begränsa vilken data som är mest relevant att arkivera just nu. Det kan vara allt, men ofta finns det ett rimligt sätt att spara utrymme och bandbredd. Data-skrapning: Att faktiskt få tag på datan. Distribution: Paketera det i torrents, annonsera det någonstans, få folk att sprida det. 5. Data-scraping Nu är du redo att faktiskt ladda ner data i bulk. Som nämnts tidigare, vid denna tidpunkt bör du redan manuellt ha laddat ner en mängd filer, för att bättre förstå beteendet och begränsningarna hos målet. Men det kommer fortfarande att finnas överraskningar i beredskap för dig när du faktiskt börjar ladda ner många filer på en gång. Vårt råd här är främst att hålla det enkelt. Börja med att bara ladda ner en mängd filer. Du kan använda Python och sedan expandera till flera trådar. Men ibland är det ännu enklare att generera Bash-filer direkt från databasen och sedan köra flera av dem i flera terminalfönster för att skala upp. Ett snabbt tekniskt trick värt att nämna här är att använda OUTFILE i MySQL, vilket du kan skriva var som helst om du inaktiverar "secure_file_priv" i mysqld.cnf (och se till att också inaktivera/överstyra AppArmor om du använder Linux). Vi lagrar data på enkla hårddiskar. Börja med vad du har och expandera långsamt. Det kan vara överväldigande att tänka på att lagra hundratals TB data. Om det är situationen du står inför, sätt bara ut en bra delmängd först, och i ditt tillkännagivande be om hjälp med att lagra resten. Om du vill skaffa fler hårddiskar själv, har r/DataHoarder några bra resurser för att få bra erbjudanden. Försök att inte oroa dig för mycket över avancerade filsystem. Det är lätt att falla ner i kaninhålet av att sätta upp saker som ZFS. En teknisk detalj att vara medveten om är dock att många filsystem inte hanterar många filer bra. Vi har funnit att en enkel lösning är att skapa flera kataloger, t.ex. för olika ID-intervall eller hash-prefix. Efter att ha laddat ner data, se till att kontrollera filernas integritet med hjälp av hashvärden i metadata, om tillgängligt. 2. Målval Tillgänglig: använder inte massor av skyddslager för att förhindra att du skrapar deras metadata och data. Speciell insikt: du har någon speciell information om detta mål, som att du på något sätt har särskild tillgång till denna samling, eller att du har listat ut hur man övervinner deras försvar. Detta är inte nödvändigt (vårt kommande projekt gör inget speciellt), men det hjälper definitivt! Stor Så, vi har vårt område som vi tittar på, nu vilken specifik samling ska vi spegla? Det finns ett par saker som gör ett bra mål: När vi hittade våra vetenskapliga läroböcker på andra webbplatser än Library Genesis, försökte vi lista ut hur de hamnade på internet. Vi hittade sedan Z-Library och insåg att även om de flesta böcker inte först dyker upp där, så hamnar de så småningom där. Vi lärde oss om dess relation till Library Genesis och den (ekonomiska) incitamentsstrukturen och överlägsna användargränssnittet, som båda gjorde det till en mycket mer komplett samling. Vi gjorde sedan en preliminär metadata- och dataskrapning och insåg att vi kunde kringgå deras IP-nedladdningsbegränsningar genom att utnyttja en av våra medlemmars särskilda tillgång till massor av proxyservrar. När du utforskar olika mål är det redan viktigt att dölja dina spår genom att använda VPN och engångs-e-postadresser, vilket vi kommer att prata mer om senare. Unik: inte redan väl täckt av andra projekt. När vi gör ett projekt har det ett par faser: Dessa är inte helt oberoende faser, och ofta skickar insikter från en senare fas dig tillbaka till en tidigare fas. Till exempel, under metadata-skrapning kan du inse att målet du valt har försvarsmekanismer bortom din kompetensnivå (som IP-blockeringar), så du går tillbaka och hittar ett annat mål. - Anna och teamet (<a %(reddit)s>Reddit</a>) Hela böcker kan skrivas om <em>varför</em> digitalt bevarande i allmänhet, och piratarkivism i synnerhet, men låt oss ge en snabb introduktion för dem som inte är så bekanta. Världen producerar mer kunskap och kultur än någonsin tidigare, men också mer av det går förlorat än någonsin tidigare. Mänskligheten litar i stor utsträckning på företag som akademiska förlag, streamingtjänster och sociala medieföretag med detta arv, och de har ofta inte visat sig vara bra förvaltare. Kolla in dokumentären Digital Amnesia, eller egentligen vilket föredrag som helst av Jason Scott. Det finns några institutioner som gör ett bra jobb med att arkivera så mycket de kan, men de är bundna av lagen. Som pirater är vi i en unik position att arkivera samlingar som de inte kan röra, på grund av upphovsrättsverkställighet eller andra begränsningar. Vi kan också spegla samlingar många gånger över, över hela världen, vilket ökar chanserna för korrekt bevarande. För tillfället kommer vi inte att gå in i diskussioner om för- och nackdelar med immateriella rättigheter, moralen i att bryta mot lagen, funderingar om censur eller frågan om tillgång till kunskap och kultur. Med allt detta ur vägen, låt oss dyka in i <em>hur</em>. Vi kommer att dela med oss av hur vårt team blev piratarkivister och de lärdomar vi lärde oss längs vägen. Det finns många utmaningar när du ger dig ut på denna resa, och förhoppningsvis kan vi hjälpa dig genom några av dem. Hur man blir en piratarkivist Den första utmaningen kan vara en överraskande sådan. Det är inte ett tekniskt problem eller ett juridiskt problem. Det är ett psykologiskt problem. Innan vi dyker in, två uppdateringar om Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>): Vi fick några extremt generösa donationer. Den första var $10k från den anonyma individen som också har stöttat "bookwarrior", den ursprungliga grundaren av Library Genesis. Speciellt tack till bookwarrior för att ha underlättat denna donation. Den andra var ytterligare $10k från en anonym givare, som tog kontakt efter vår senaste release och blev inspirerad att hjälpa till. Vi hade också ett antal mindre donationer. Tack så mycket för allt ert generösa stöd. Vi har några spännande nya projekt på gång som detta kommer att stödja, så håll utkik. Vi hade några tekniska svårigheter med storleken på vår andra release, men våra torrents är nu uppe och seedar. Vi fick också ett generöst erbjudande från en anonym individ att seeda vår samling på deras mycket snabba servrar, så vi gör en speciell uppladdning till deras maskiner, varefter alla andra som laddar ner samlingen bör se en stor förbättring i hastighet. Blogginlägg Hej, jag är Anna. Jag skapade <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, världens största skuggbibliotek. Detta är min personliga blogg, där jag och mina lagkamrater skriver om piratkopiering, digital bevarande och mer. Anslut med mig på <a %(reddit)s>Reddit</a>. Observera att denna webbplats bara är en blogg. Vi är värdar för våra egna ord här. Inga torrents eller andra upphovsrättsskyddade filer är värdar eller länkade här. <strong>Bibliotek</strong> - Likt de flesta bibliotek fokuserar vi främst på skriftligt material som böcker. Vi kanske expanderar till andra typer av media i framtiden. <strong>Spegel</strong> - Vi är strikt en spegel av befintliga bibliotek. Vi fokuserar på bevarande, inte på att göra böcker lättsökbara och nedladdningsbara (åtkomst) eller att främja en stor gemenskap av människor som bidrar med nya böcker (källor). <strong>Pirat</strong> - Vi bryter medvetet mot upphovsrättslagen i de flesta länder. Detta gör att vi kan göra något som juridiska enheter inte kan göra: se till att böcker speglas vida och brett. <em>Vi länkar inte till filerna från denna blogg. Vänligen hitta dem själv.</em> - Anna och teamet (<a %(reddit)s>Reddit</a>) Detta projekt (REDIGERAT: flyttat till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) syftar till att bidra till bevarandet och befrielsen av mänsklig kunskap. Vi gör vårt lilla och ödmjuka bidrag, i fotspåren av de stora före oss. Fokus för detta projekt illustreras av dess namn: Det första biblioteket vi har speglat är Z-Library. Detta är ett populärt (och olagligt) bibliotek. De har tagit Library Genesis-samlingen och gjort den lättsökbar. Dessutom har de blivit mycket effektiva på att uppmuntra nya bokbidrag genom att belöna bidragande användare med olika förmåner. De bidrar för närvarande inte med dessa nya böcker tillbaka till Library Genesis. Och till skillnad från Library Genesis gör de inte sin samling lätt spegelbar, vilket förhindrar brett bevarande. Detta är viktigt för deras affärsmodell, eftersom de tar betalt för att få tillgång till deras samling i bulk (mer än 10 böcker per dag). Vi gör inga moraliska bedömningar om att ta betalt för massåtkomst till en illegal boksamling. Det råder ingen tvekan om att Z-Library har varit framgångsrik i att utöka tillgången till kunskap och att skaffa fler böcker. Vi är helt enkelt här för att göra vår del: att säkerställa det långsiktiga bevarandet av denna privata samling. Vi vill bjuda in dig att hjälpa till att bevara och befria mänsklig kunskap genom att ladda ner och seeda våra torrents. Se projektsidan för mer information om hur datan är organiserad. Vi vill också gärna bjuda in dig att bidra med dina idéer om vilka samlingar som ska speglas härnäst, och hur vi ska gå tillväga. Tillsammans kan vi åstadkomma mycket. Detta är bara ett litet bidrag bland otaliga andra. Tack för allt du gör. Vi presenterar Pirate Library Mirror: Bevarar 7TB av böcker (som inte finns i Libgen) 10% of mänsklighetens skrivna arv bevarat för alltid <strong>Google.</strong> Trots allt gjorde de denna forskning för Google Books. Men deras metadata är inte tillgänglig i bulk och ganska svår att skrapa. <strong>Olika individuella bibliotekssystem och arkiv.</strong> Det finns bibliotek och arkiv som inte har indexerats och aggregerats av någon av de ovanstående, ofta för att de är underfinansierade, eller av andra skäl inte vill dela sin data med Open Library, OCLC, Google, och så vidare. Många av dessa har digitala poster tillgängliga via internet, och de är ofta inte särskilt väl skyddade, så om du vill hjälpa till och ha lite kul med att lära dig om konstiga bibliotekssystem, är dessa bra utgångspunkter. <strong>ISBNdb.</strong> Detta är ämnet för detta blogginlägg. ISBNdb skrapar olika webbplatser för bokmetadata, särskilt prisdata, som de sedan säljer till bokhandlare, så att de kan prissätta sina böcker i enlighet med resten av marknaden. Eftersom ISBN är ganska universella nuförtiden har de effektivt byggt en ”webbsida för varje bok”. <strong>Open Library.</strong> Som nämnts tidigare är detta deras hela uppdrag. De har hämtat enorma mängder biblioteksdata från samarbetande bibliotek och nationella arkiv, och fortsätter att göra det. De har också frivilliga bibliotekarier och ett tekniskt team som försöker deduplicera poster och märka dem med alla möjliga metadata. Bäst av allt, deras dataset är helt öppet. Du kan enkelt <a %(openlibrary)s>ladda ner det</a>. <strong>WorldCat.</strong> Detta är en webbplats som drivs av den ideella organisationen OCLC, som säljer bibliotekshanteringssystem. De samlar in bokmetadata från många bibliotek och gör det tillgängligt via WorldCat-webbplatsen. Men de tjänar också pengar på att sälja denna data, så den är inte tillgänglig för bulk-nedladdning. De har dock några mer begränsade bulk-datasets tillgängliga för nedladdning, i samarbete med specifika bibliotek. 1. För någon rimlig definition av "för alltid". ;) 2. Självklart är mänsklighetens skriftliga arv mycket mer än böcker, särskilt nuförtiden. För denna posts och våra senaste släpps skull fokuserar vi på böcker, men våra intressen sträcker sig längre. 3. Det finns mycket mer att säga om Aaron Swartz, men vi ville bara nämna honom kort, eftersom han spelar en avgörande roll i denna berättelse. Med tiden kan fler människor stöta på hans namn för första gången och sedan själva dyka ner i kaninhålet. <strong>Fysiska kopior.</strong> Självklart är detta inte särskilt hjälpsamt, eftersom de bara är dubbletter av samma material. Det skulle vara häftigt om vi kunde bevara alla anteckningar som folk gör i böcker, som Fermats berömda ”klotter i marginalerna”. Men tyvärr kommer det att förbli en arkivists dröm. <strong>”Upplagor”.</strong> Här räknar du varje unik version av en bok. Om något är annorlunda, som ett annat omslag eller ett annat förord, räknas det som en annan upplaga. <strong>Filer.</strong> När man arbetar med skuggbibliotek som Library Genesis, Sci-Hub eller Z-Library finns det en ytterligare övervägning. Det kan finnas flera skanningar av samma upplaga. Och folk kan göra bättre versioner av befintliga filer, genom att skanna texten med OCR, eller rätta till sidor som skannats i vinkel. Vi vill bara räkna dessa filer som en upplaga, vilket skulle kräva bra metadata, eller deduplicering med hjälp av dokumentlikhetsmått. <strong>”Verk”.</strong> Till exempel ”Harry Potter och Hemligheternas kammare” som ett logiskt koncept, som omfattar alla versioner av den, som olika översättningar och nytryck. Detta är en slags användbar definition, men det kan vara svårt att dra gränsen för vad som räknas. Till exempel vill vi förmodligen bevara olika översättningar, även om nytryck med bara mindre skillnader kanske inte är lika viktiga. - Anna och teamet (<a %(reddit)s>Reddit</a>) Med Pirate Library Mirror (REDIGERAT: flyttat till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) är vårt mål att ta alla böcker i världen och bevara dem för alltid.<sup>1</sup> Mellan våra Z-Library-torrents och de ursprungliga Library Genesis-torrents har vi 11 783 153 filer. Men hur många är det egentligen? Om vi deduplicerade dessa filer ordentligt, vilken procentandel av alla böcker i världen har vi bevarat? Vi skulle verkligen vilja ha något som detta: Låt oss börja med några grova siffror: I både Z-Library/Libgen och Open Library finns det många fler böcker än unika ISBN. Betyder det att många av dessa böcker inte har ISBN, eller saknas ISBN-metadata helt enkelt? Vi kan förmodligen besvara denna fråga med en kombination av automatiserad matchning baserad på andra attribut (titel, författare, förlag, etc), genom att hämta in fler datakällor och extrahera ISBN från de faktiska bokskanningarna själva (i fallet med Z-Library/Libgen). Hur många av dessa ISBN är unika? Detta illustreras bäst med ett Venn-diagram: För att vara mer exakt: Vi blev förvånade över hur lite överlapp det finns! ISBNdb har en enorm mängd ISBN som inte dyker upp i vare sig Z-Library eller Open Library, och detsamma gäller (i mindre men fortfarande betydande grad) för de andra två. Detta väcker många nya frågor. Hur mycket skulle automatiserad matchning hjälpa till med att tagga böcker som inte var taggade med ISBN? Skulle det finnas många matchningar och därmed ökat överlapp? Och vad skulle hända om vi tar in en fjärde eller femte dataset? Hur mycket överlapp skulle vi se då? Detta ger oss en startpunkt. Vi kan nu titta på alla ISBN som inte fanns i Z-Library-datasetet, och som inte heller matchar titel-/författarfält. Det kan ge oss en möjlighet att bevara alla böcker i världen: först genom att skrapa internet efter skanningar, sedan genom att gå ut i verkliga livet för att skanna böcker. Det senare skulle till och med kunna finansieras av allmänheten, eller drivas av "belöningar" från personer som skulle vilja se specifika böcker digitaliserade. Allt detta är en historia för en annan gång. Om du vill hjälpa till med något av detta — vidare analys; skrapa mer metadata; hitta fler böcker; OCR:a böcker; göra detta för andra områden (t.ex. artiklar, ljudböcker, filmer, tv-program, tidskrifter) eller till och med göra en del av dessa data tillgängliga för saker som ML / träning av stora språkmodeller — vänligen kontakta mig (<a %(reddit)s>Reddit</a>). Om du är särskilt intresserad av dataanalys, arbetar vi med att göra våra datasets och skript tillgängliga i ett mer användarvänligt format. Det skulle vara fantastiskt om du bara kunde forka en anteckningsbok och börja experimentera med detta. Slutligen, om du vill stödja detta arbete, överväg att göra en donation. Detta är en helt frivillig verksamhet, och ditt bidrag gör en enorm skillnad. Varje liten bit hjälper. För närvarande tar vi emot donationer i krypto; se donationssidan på Annas Arkiv. För en procentandel behöver vi en nämnare: det totala antalet böcker som någonsin publicerats.<sup>2</sup> Innan Google Books upphörde, försökte en ingenjör på projektet, Leonid Taycher, <a %(booksearch_blogspot)s>att uppskatta</a> detta antal. Han kom fram till — med glimten i ögat — 129 864 880 ("åtminstone fram till söndag"). Han uppskattade detta antal genom att bygga en enhetlig databas över alla böcker i världen. För detta sammanställde han olika datasets och sammanfogade dem på olika sätt. Som en snabb parentes finns det en annan person som försökte katalogisera alla böcker i världen: Aaron Swartz, den avlidne digitala aktivisten och Reddit-medgrundaren.<sup>3</sup> Han <a %(youtube)s>startade Open Library</a> med målet att skapa ”en webbsida för varje bok som någonsin publicerats”, genom att kombinera data från många olika källor. Han fick betala det yttersta priset för sitt arbete med digital bevarande när han åtalades för massnedladdning av akademiska artiklar, vilket ledde till hans självmord. Det är onödigt att säga att detta är en av anledningarna till att vår grupp är pseudonym och varför vi är mycket försiktiga. Open Library drivs fortfarande heroiskt av folk på Internet Archive, som fortsätter Aarons arv. Vi kommer tillbaka till detta senare i detta inlägg. I Googles blogginlägg beskriver Taycher några av utmaningarna med att uppskatta detta antal. Först, vad utgör en bok? Det finns några möjliga definitioner: ”Upplagor” verkar vara den mest praktiska definitionen av vad ”böcker” är. Bekvämt nog används denna definition också för att tilldela unika ISBN-nummer. Ett ISBN, eller International Standard Book Number, används ofta för internationell handel, eftersom det är integrerat med det internationella streckkodssystemet (”International Article Number”). Om du vill sälja en bok i butiker behöver den en streckkod, så du får ett ISBN. Taychers blogginlägg nämner att även om ISBN är användbara, är de inte universella, eftersom de bara verkligen antogs i mitten av sjuttiotalet, och inte överallt i världen. Ändå är ISBN förmodligen den mest använda identifieraren av bokupplagor, så det är vår bästa utgångspunkt. Om vi kan hitta alla ISBN i världen får vi en användbar lista över vilka böcker som fortfarande behöver bevaras. Så, var får vi datan ifrån? Det finns ett antal befintliga insatser som försöker sammanställa en lista över alla böcker i världen: I detta inlägg är vi glada att tillkännage en liten release (jämfört med våra tidigare Z-Library-releaser). Vi skrapade det mesta av ISBNdb och gjorde datan tillgänglig för torrenting på Pirate Library Mirrors webbplats (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>; vi kommer inte att länka den här direkt, bara sök efter den). Dessa är cirka 30,9 miljoner poster (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippad). På deras webbplats hävdar de att de faktiskt har 32,6 miljoner poster, så vi kan på något sätt ha missat några, eller <em>de</em> kan göra något fel. I vilket fall som helst, för nu kommer vi inte att dela exakt hur vi gjorde det — vi lämnar det som en övning för läsaren. ;-) Vad vi kommer att dela är en preliminär analys, för att försöka komma närmare en uppskattning av antalet böcker i världen. Vi tittade på tre datasets: denna nya ISBNdb-dataset, vår ursprungliga release av metadata som vi skrapade från Z-Library-skuggbiblioteket (som inkluderar Library Genesis), och Open Library-datadumpen. ISBNdb-dump, eller Hur Många Böcker Är Bevarade För Alltid? Om vi skulle deduplicera filerna från skuggbibliotek ordentligt, vilken procentandel av alla böcker i världen har vi bevarat? Uppdateringar om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det största verkligt öppna biblioteket i mänsklighetens historia. <em>WorldCat redesign</em> Data <strong>Format?</strong> <a %(blog)s>Annas Arkiv Containers (AAC)</a>, som i huvudsak är <a %(jsonlines)s>JSON Lines</a> komprimerade med <a %(zstd)s>Zstandard</a>, plus några standardiserade semantiker. Dessa containers omsluter olika typer av poster, baserat på de olika skrapningar vi genomförde. För ett år sedan <a %(blog)s>började vi</a> att besvara denna fråga: <strong>Vilken procentandel av böcker har permanent bevarats av skuggbibliotek?</strong> Låt oss titta på lite grundläggande information om datan: När en bok hamnar i ett öppet data-skuggbibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, och nu <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, speglas den över hela världen (genom torrents), vilket praktiskt taget bevarar den för alltid. För att besvara frågan om vilken procentandel av böcker som har bevarats, behöver vi veta nämnaren: hur många böcker finns det totalt? Och helst har vi inte bara ett nummer, utan faktisk metadata. Då kan vi inte bara matcha dem mot skuggbibliotek, utan också <strong>skapa en TODO-lista över återstående böcker att bevara!</strong> Vi kan till och med börja drömma om en crowdsourcad insats för att gå igenom denna TODO-lista. Vi skrapade <a %(wikipedia_isbndb_com)s>ISBNdb</a> och laddade ner <a %(openlibrary)s>Open Library dataset</a>, men resultaten var otillfredsställande. Huvudproblemet var att det inte fanns mycket överlappning av ISBN. Se detta Venn-diagram från <a %(blog)s>vårt blogginlägg</a>: Vi blev mycket förvånade över hur lite överlappning det fanns mellan ISBNdb och Open Library, som båda inkluderar data från olika källor, såsom webbsökningar och biblioteksregister. Om de båda gör ett bra jobb med att hitta de flesta ISBN där ute, skulle deras cirklar säkert ha betydande överlappning, eller så skulle den ena vara en delmängd av den andra. Det fick oss att undra, hur många böcker faller <em>helt utanför dessa cirklar</em>? Vi behöver en större databas. Det var då vi riktade våra blickar mot världens största bokdatabas: <a %(wikipedia_worldcat)s>WorldCat</a>. Detta är en proprietär databas av den ideella organisationen <a %(wikipedia_oclc)s>OCLC</a>, som samlar metadata från bibliotek över hela världen, i utbyte mot att ge dessa bibliotek tillgång till hela datasetet och att de visas i slutanvändarnas sökresultat. Även om OCLC är en ideell organisation, kräver deras affärsmodell att de skyddar sin databas. Tja, vi är ledsna att säga, vänner på OCLC, vi ger bort allt. :-) Under det senaste året har vi noggrant skrapat alla WorldCat-poster. Till en början hade vi tur. WorldCat rullade precis ut sin kompletta webbplatsomdesign (i augusti 2022). Detta inkluderade en omfattande översyn av deras backend-system, vilket introducerade många säkerhetsbrister. Vi tog omedelbart tillfället i akt och kunde skrapa hundratals miljoner (!) poster på bara några dagar. Efter det åtgärdades säkerhetsbristerna långsamt en efter en, tills den sista vi hittade blev åtgärdad för ungefär en månad sedan. Vid den tiden hade vi i princip alla poster och gick bara efter något högre kvalitetsposter. Så vi kände att det var dags att släppa! 1,3B WorldCat-skrapning <em><strong>TL;DR:</strong> Annas Arkiv skrapade hela WorldCat (världens största bibliotek metadata-samling) för att skapa en TODO-lista över böcker som behöver bevaras.</em> WorldCat Varning: detta blogginlägg har blivit föråldrat. Vi har beslutat att IPFS ännu inte är redo för prime time. Vi kommer fortfarande att länka till filer på IPFS från Annas Arkiv när det är möjligt, men vi kommer inte längre att vara värd för det själva, och vi rekommenderar inte andra att spegla med IPFS. Se vår Torrents-sida om du vill hjälpa till att bevara vår samling. Hjälp till att seeda Z-Library på IPFS partnerservernedladdning SciDB Extern utlåning Extern utlåning (för funktionshindrade) Extern nedladdning Utforska metadata Innehåll i torrents Tillbaka  (+%(num)s bonus) obetald betald avbruten utgången väntar på att Anna ska bekräfta ogiltig Texten nedan fortsätter på engelska. Gå Återställ Framåt Senast Om din e-postadress inte fungerar på Libgen-forumen rekommenderar vi att du använder <a %(a_mail)s>Proton Mail</a> (gratis). Du kan också <a %(a_manual)s>manuellt begära</a> att ditt konto aktiveras. (kan kräva <a %(a_browser)s>webbläsarverifiering</a> — obegränsade nedladdningar!) Snabb partnerserver #%(number)s (rekommenderad) (lite snabbare men med väntelista) (ingen webbläsarverifiering krävs) (ingen webbläsarverifiering eller väntelistor) (ingen väntelista, men kan vara mycket långsam) Långsam partnerserver #%(number)s Ljudbok Serietidning Bok (skönlitteratur) Bok (facklitteratur) Bok (okänd) Tidningsartikel Tidskrift Notblad Övrigt Standarddokument Inte alla sidor kunde konverteras till PDF Markerad som trasig i Libgen.li Inte synlig i Libgen.li Inte synlig i Libgen.rs Skönlitteratur Inte synlig i Libgen.rs Facklitteratur Körning av exiftool misslyckades på denna fil Markerad som ”dålig fil” i Z-Library Saknas från Z-library Markerad som ”spam” i Z-Library Filen kan inte öppnas (t.ex. korrupt fil, DRM) Upphovsrättsanspråk Nerladdningsproblem (t.ex. kan inte koppla upp, felmeddelande, väldigt långsam) Inkorrekt metadata (till exempel titel, beskrivning, bild) Annat Dålig kvalitet (t.ex. formateringsproblem, låg inscanningskvalitet, sidor saknas) Spam / filen borde tas bort (t.ex. reklam, stötande innehåll) %(amount)s (%(amount_usd)s) %(amount)s totalt %(amount)s (%(amount_usd)s) totalt Bedårande Bokmal Lycklig Litteraturälskare Duktig Datahamstrare Allenastående Arkivarie Bonusnedladdningar Cerlalc Tjeckisk metadata DuXiu 读秀 EBSCOhost eBook Index Google Böcker Goodreads HathiTrust IA IA Kontrollerad Digital Utlåning ISBNdb ISBN GRP Libgen.li Exklusive “scimag” Libgen.rs Facklitteratur och skönlitteratur Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Ryska statsbiblioteket Sci-Hub Via Libgen.li ”scimag” Sci-Hub / Libgen ”scimag” Trantor Uppladdningar till AA Z-Library Z-Library Kinesiska Titel, författare, DOI, ISBN, MD5, … Sök Författare Beskrivning och metadatakommentarer Upplaga Ursprungligt filnamn Förlag (sök specifikt fält) Titel Utgivningsår Tekniska detaljer Denna mynt har en högre minimum än vanligt. Vänligen välj en annan längd eller ett annat mynt. Begäran kunde inte slutföras. Försök igen om några minuter, och om problemet kvarstår, kontakta oss på %(email)s med en skärmdump. Ett okänt fel inträffade. Vänligen kontakta oss på %(email)s och bifoga en skärmdump. Fel vid betalningshanteringf. Vänta en stund och försök igen. Om problemet kvarstår i mer än 24 timmar, vänligen kontakta oss på %(email)s med en skärmdump. Vi håller i en insamling för att <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">backa upo</a> det största skuggbiblioteket för serier i världen. Tack för ditt stöd! <a href="/donate">Donate.</a> Om du inte kan donera, berätta gärna för dina vänner istället och följ oss på <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>. Mejla oss inte för att <a %(a_request)s>efterfråga böcker</a><br>eller små (<10k) <a %(a_upload)s>uppladdningar</a>. Annas arkiv DMCA / upphovsrättsanspråk Håll kontakten Reddit Alternativ SLUM (%(unaffiliated)s) oberoende Annas Arkiv behöver din hjälp! Om du donerar nu får du <strong>dubbelt</strong> så många snabba nedladdningar. Många försöker stänga ner oss, men vi kämpar tillbaka. Om du donerar denna månad får du <strong>dubbelt</strong> så många snabba nedladdningar. Giltigt till slutet av denna månad. Spara mänsklig kunskap: en fantastisk semesterpresent! Medlemskap kommer att förlängas i enlighet med detta. Partner-servrar är otillgängliga på grund av stängningar av hosting. De bör vara uppe igen snart. För att öka motståndskraften hos Annas Arkiv söker vi volontärer för att driva spegelsajter. Det finns ett nytt sött att donera: %(method_name)s. Överväg gärna att %(donate_link_open_tag)sdonera</a> – det är inte billigt att driva hemsidan, och din donation gör verkligen nytta. Tack så mycket. Rekommendera en vän, och både du och din vän får %(percentage)s%% bonus snabba nedladdningar! Överraska en älskad, ge dem ett konto med medlemskap. Den perfekta Alla hjärtans dag-presenten! Läs mer… Konto Aktivitet Avancerat Annas blogg ↗ Annas mjukvara ↗ beta Kodutforskare Datauppsätningar Donera Nerladdade filer FAQ Hem Förbättra metadata Data för stora språkmodeller Logga in / Registrera dig Mina donationer Offentlig profil Söka Säkerhet Torrentar Översätta ↗ Volontärarbete & Belöningar Senaste nerladdningar: 📚&nbsp;Världens största bibliotek med öppen källkod och öppna data. ⭐️&nbsp;Speglar Sci-Hub, Library Genesis, Z-Library och mer. 📈&nbsp;%(book_any)s böcker, %(journal_article)s artiklar, %(book_comic)s serier, %(magazine)s tidskrifter — bevarade för alltid.  och  och mer DuXiu Internet Archives lånebibliotek LibGen 📚&nbsp;Det största verkligt öppna biblioteket i mänsklighetens historia. 📈&nbsp;%(book_count)s&nbsp;böcker, %(paper_count)s&nbsp;uppsatser — bevarade för alltid. ⭐️&nbsp;Vi speglar %(libraries)s. Vi skrapar och open-source %(scraped)s. All vår kod och data bygger helt på öppen källkod. OpenLib Sci-Hub ,  📚Världens största bibliotek med öppen källkod och öppen data. <br> ⭐️ Speglar Scihub, Libgen, Zlib, och fler. Z-Lib Annas arkiv Ogiltig begäran. Besök %(websites)s. Världens största bibliotek med öppen källkod och öppen data. Speglar Scihub, Libgen, Zlib, och fler. Sök i Annas Arkiv Annas Arkiv Vänligen uppdatera för att försöka igen. <a %(a_contact)s>Kontakta oss</a> om problemet kvarstår i flera timmar. 🔥 Problem med att ladda denna sida <li>1. Följ oss på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, eller <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Sprid ordet om Annas Arkiv på Twitter, Reddit, Tiktok, Instagram, på ditt lokala café eller bibliotek, eller var du än går! Vi tror inte på att hålla saker hemliga — om vi blir nedtagna kommer vi bara att dyka upp någon annanstans, eftersom all vår kod och data är helt öppen källkod.</li><li>3. Om du kan, överväg att <a href="/donate">donera</a>.</li><li>4. Hjälp till att <a href="https://translate.annas-software.org/">översätta</a> vår webbplats till olika språk.</li><li>5. Om du är mjukvaruingenjör, överväg att bidra till vår <a href="https://annas-software.org/">öppna källkod</a>, eller seed våra <a href="/datasets">torrenter</a>.</li> 10. Skapa eller hjälp till att underhålla Wikipedia-sidan för Annas Arkiv på ditt språk. 11. Vi letar efter små, smakfulla annonser. Om du vill annonsera på Annas Arkiv, vänligen meddela oss. 6. Om du är en säkerhetsforskare kan vi använda dina färdigheter både för offensiv och defensiv. Kolla in vår <a %(a_security)s>Säkerhet</a>-sida. 7. Vi söker experter på betalningar för anonyma handlare. Kan du hjälpa oss att lägga till fler bekväma sätt att donera? PayPal, WeChat, presentkort. Om du känner någon, vänligen kontakta oss. 8. Vi letar alltid efter mer serverkapacitet. 9. Du kan hjälpa till genom att rapportera filproblem, lämna kommentarer och skapa listor direkt på denna webbplats. Du kan också hjälpa till genom att <a %(a_upload)s>ladda upp fler böcker</a>, eller fixa filproblem eller formatering av befintliga böcker. För mer omfattande information om hur du kan bli volontär, se vår <a %(a_volunteering)s>Volontär- och Belöningssida</a>. Vi är hängivna anhängare av ett fritt informationsflöde, och av att bevara kunskap samt kultur. Med den här sökmotorn bygger vi vidare på andras stordåd. Vi beundrar det hårda arbete som andra skapare av skuggbibliotek lagt ner, och vi hoppas att sökmotorn kan öka deras räckvidd. För att hålla dig uppdaterad om arbetet, följ Anna på <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> eller på <a href="https://t.me/annasarchiveorg">Telegram</a>.. Om du har frågor och feedback kontakta Anna på %(email)s. Kontonummer: %(account_id)s Logga ut ❌ Något gick fel. Vänligen ladda om sidan och försök igen. ✅ Du är nu utloggad. Ladda om sidan för att logga in igen. Snabba nedladdningar använda (senaste 24 timmarna): <strong>%(used)s / %(total)s</strong> Medlemskap: <strong>%(tier_name)s</strong> till %(until_date)s <a %(a_extend)s>(förläng)</a> Du kan kombinera flera medlemskap (snabba nedladdningar per 24 timmar kommer att kombineras). Medlemskap: <strong>Inget</strong> <a %(a_become)s>(bli medlem)</a> Kontakta Anna på %(email)s om du är intresserad av att uppgradera ditt medlemskap till en högre nivå. Offentlig profil: %(profile_link)s Hemlig nyckel (dela inte!): %(secret_key)s visa Gå med oss här! Uppgradera till en <a %(a_tier)s>högre nivå</a> för att gå med i vår grupp. Exklusiv Telegram-grupp: %(link)s Konto vilka nedladdningar? Logga in Tappa inte bort din nyckel! Felaktig hemlig nyckel. Dubbelkolla din nyckel och försök igen, alternativt registrera ett nytt konto nedan. Hemlig nyckel Ange din hemliga nyckel för att logga in: Gammalt e-postbaserat konto? Ange din <a %(a_open)s>e-post här</a>. Registrera nytt konto Har du inget konto ännu? Registrering lyckades! Din hemliga nyckel är: <span %(span_key)s>%(key)s</span> Spara denna nyckel på ett säkert ställe. Om du tappar bort den, förlorar du åtkomsten till ditt konto. <li %(li_item)s><strong>Bokmärke.</strong> Du kan bokmärka denna sida för att hämta din nyckel.</li><li %(li_item)s><strong>Nedladdning.</strong> Klicka <a %(a_download)s>denna länk</a> för att ladda ner din nyckel.</li><li %(li_item)s><strong>Lösenordshanterare.</strong> Använd en lösenordshanterare för att spara nyckeln när du anger den nedan.</li> Logga in / Registrera Webbläsarverifiering Varning: koden har felaktiga Unicode-tecken i sig och kan bete sig felaktigt i olika situationer. Den råa binären kan avkodas från base64-representationen i URL:en. Beskrivning Etikett Prefix URL för en specifik kod Webbplats Koder som börjar med “%(prefix_label)s” Vänligen skrapa inte dessa sidor. Istället rekommenderar vi att <a %(a_import)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser, och köra vår <a %(a_software)s>öppna källkod</a>. Rådata kan manuellt utforskas genom JSON-filer som <a %(a_json_file)s>denna</a>. Färre än %(count)s poster Generisk URL Kodutforskare Index av Utforska koderna som poster är taggade med, efter prefix. Kolumnen ”poster” visar antalet poster som är taggade med koder med det givna prefixet, som ses i sökmotorn (inklusive metadata-endast poster). Kolumnen ”koder” visar hur många faktiska koder som har ett givet prefix. Känt kodprefix ”%(key)s” Mer… Prefix %(count)s post som matchar “%(prefix_label)s” %(count)s poster som matchar “%(prefix_label)s” koder poster ”%%s” kommer att ersättas med kodens värde Sök Annas Arkiv Koder URL för specifik kod: “%(url)s” Denna sida kan ta ett tag att generera, vilket är anledningen till att den kräver en Cloudflare captcha. <a %(a_donate)s>Medlemmar</a> kan hoppa över captchan. Missbruk anmält: Bättre version Vill du anmäla denna användare för kränkande eller olämpligt beteende? Filproblem: %(file_issue)s dolt kommentar Svara Anmäl missbruk Du anmälde denna användare för missbruk. Upphovsrättsanspråk på detta e-postmeddelande kommer att ignoreras; använd istället formuläret. Visa e-post Vi välkomnar verkligen din feedback och dina frågor! På grund av mängden skräppost och nonsensmejl vi får, vänligen markera rutorna för att bekräfta att du förstår dessa villkor för att kontakta oss. Alla andra sätt att kontakta oss angående upphovsrättsanspråk kommer att raderas automatiskt. För DMCA / upphovsrättsanspråk, använd <a %(a_copyright)s>detta formulär</a>. E-postkontakt URL:er på Annas Arkiv (obligatoriskt). En per rad. Vänligen inkludera endast URL:er som beskriver exakt samma utgåva av en bok. Om du vill göra en ansökan för flera böcker eller flera utgåvor, vänligen skicka in detta formulär flera gånger. Ansökningar som buntar ihop flera böcker eller utgåvor kommer att avvisas. Adress (obligatoriskt) Tydlig beskrivning av källmaterialet (obligatoriskt) E-post (obligatoriskt) URL:er till källmaterialet, en per rad (obligatoriskt). Vänligen inkludera så många som möjligt för att hjälpa oss att verifiera ditt anspråk (t.ex. Amazon, WorldCat, Google Books, DOI). ISBN-nummer för källmaterialet (om tillämpligt). Ett per rad. Vänligen inkludera endast de som exakt matchar den utgåva för vilken du rapporterar ett upphovsrättsanspråk. Ditt namn (obligatoriskt) ❌ Något gick fel. Vänligen ladda om sidan och försök igen. ✅ Tack för att du skickade in ditt upphovsrättsanspråk. Vi kommer att granska det så snart som möjligt. Vänligen ladda om sidan för att skicka in ett nytt. <a %(a_openlib)s>Open Library</a> URL:er för källmaterialet, en per rad. Ta dig tid att söka efter ditt källmaterial på Open Library. Detta hjälper oss att verifiera ditt anspråk. Telefonnummer (obligatoriskt) Uttalande och signatur (obligatoriskt) Skicka in anspråk Om du har ett DMCA- eller annat upphovsrättsanspråk, vänligen fyll i detta formulär så noggrant som möjligt. Om du stöter på några problem, kontakta oss på vår dedikerade DMCA-adress: %(email)s. Observera att anspråk som skickas till denna adress inte kommer att behandlas, den är endast för frågor. Använd formuläret nedan för att skicka in dina anspråk. DMCA / Formulär för upphovsrättsanspråk Exempelpost på Annas Arkiv Torrenter av Annas Arkiv Annas Arkiv Containers-format Skript för att importera metadata Om du är intresserad av att spegla denna datamängd för <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-träning</a>, vänligen kontakta oss. Senast uppdaterad: %(date)s Huvudsida %(source)s webbplats Metadata-dokumentation (de flesta fält) Filer speglade av Annas Arkiv: %(count)s (%(percent)s%%) Resurser Totalt antal filer: %(count)s Total filstorlek: %(size)s Vårt blogginlägg om dessa data <a %(duxiu_link)s>Duxiu</a> är en enorm databas med skannade böcker, skapad av <a %(superstar_link)s>SuperStar Digital Library Group</a>. De flesta är akademiska böcker, skannade för att göra dem tillgängliga digitalt för universitet och bibliotek. För vår engelsktalande publik har <a %(princeton_link)s>Princeton</a> och <a %(uw_link)s>University of Washington</a> bra översikter. Det finns också en utmärkt artikel som ger mer bakgrund: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Böckerna från Duxiu har länge piratkopierats på det kinesiska internet. Vanligtvis säljs de för mindre än en dollar av återförsäljare. De distribueras vanligtvis med den kinesiska motsvarigheten till Google Drive, som ofta har hackats för att tillåta mer lagringsutrymme. Några tekniska detaljer finns <a %(link1)s>här</a> och <a %(link2)s>här</a>. Även om böckerna har distribuerats semi-offentligt, är det ganska svårt att få tag på dem i bulk. Vi hade detta högt på vår TODO-lista och avsatte flera månader av heltidsarbete för det. Men i slutet av 2023 kontaktade en otrolig, fantastisk och talangfull volontär oss och berättade att de redan hade gjort allt detta arbete — till stor kostnad. De delade hela samlingen med oss utan att förvänta sig något i gengäld, förutom garantin om långsiktig bevaring. Verkligen anmärkningsvärt. Mer information från våra volontärer (råa anteckningar): Anpassad från vårt <a %(a_href)s>blogginlägg</a>. DuXiu 读秀 %(count)s fil %(count)s filer Denna dataset är nära relaterad till <a %(a_datasets_openlib)s>Open Library dataset</a>. Den innehåller en skrapning av all metadata och en stor del av filer från IA:s Controlled Digital Lending Library. Uppdateringar släpps i <a %(a_aac)s>Anna’s Archive Containers-format</a>. Dessa poster hänvisas direkt från Open Library dataset, men innehåller också poster som inte finns i Open Library. Vi har också ett antal datafiler som skrapats av community-medlemmar genom åren. Samlingen består av två delar. Du behöver båda delarna för att få all data (förutom ersatta torrents, som är överstrukna på torrentsidan). Digitalt lånebibliotek vår första utgåva, innan vi standardiserade på <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Innehåller metadata (som json och xml), pdf:er (från acsm och lcpdf digitala utlåningssystem) och omslagsminiatyrer. inkrementella nya utgåvor, med användning av AAC. Innehåller endast metadata med tidsstämplar efter 2023-01-01, eftersom resten redan täcks av "ia". Även alla pdf-filer, denna gång från acsm och "bookreader" (IA:s webbläsare) utlåningssystem. Trots att namnet inte är helt korrekt, lägger vi fortfarande in bookreader-filer i ia2_acsmpdf_files-samlingen, eftersom de är ömsesidigt uteslutande. IA Kontrollerad Digital Utlåning 98%%+ av filer är sökbara. Vårt uppdrag är att arkivera alla böcker i världen (samt artiklar, tidskrifter, etc.) och göra dem allmänt tillgängliga. Vi tror att alla böcker bör speglas brett för att säkerställa redundans och motståndskraft. Det är därför vi samlar filer från en mängd olika källor. Vissa källor är helt öppna och kan speglas i bulk (som Sci-Hub). Andra är stängda och skyddande, så vi försöker skrapa dem för att ”befria” deras böcker. Ytterligare andra faller någonstans däremellan. All vår data kan <a %(a_torrents)s>torrentas</a>, och all vår metadata kan <a %(a_anna_software)s>genereras</a> eller <a %(a_elasticsearch)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser. Rådata kan manuellt utforskas genom JSON-filer som <a %(a_dbrecord)s>denna</a>. Metadata ISBN-webbplats Senast uppdaterad: %(isbn_country_date)s (%(link)s) Resurser Den internationella ISBN-byrån släpper regelbundet de intervall som den har tilldelat nationella ISBN-byråer. Från detta kan vi härleda vilket land, region eller språkgrupp detta ISBN tillhör. Vi använder för närvarande dessa data indirekt, genom <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket. ISBN landsinformation Detta är en dump av många anrop till isbndb.com under september 2022. Vi försökte täcka alla ISBN-intervall. Dessa är cirka 30,9 miljoner poster. På deras webbplats hävdar de att de faktiskt har 32,6 miljoner poster, så vi kan ha missat några, eller <em>de</em> kan göra något fel. JSON-svaren är i stort sett råa från deras server. Ett datakvalitetsproblem som vi märkte är att för ISBN-13-nummer som börjar med ett annat prefix än ”978-”, inkluderar de fortfarande ett ”isbn”-fält som helt enkelt är ISBN-13-numret med de första 3 siffrorna borttagna (och kontrollsiffran omberäknad). Detta är uppenbarligen fel, men det är så de verkar göra det, så vi ändrade det inte. Ett annat potentiellt problem som du kan stöta på är att fältet ”isbn13” har dubbletter, så du kan inte använda det som en primärnyckel i en databas. Kombinationen av fälten ”isbn13”+”isbn” verkar dock vara unik. Utgåva 1 (2022-10-31) Skönlitterära torrents ligger efter (även om ID ~4-6M inte har torrentats eftersom de överlappar med våra Zlib-torrents). Vårt blogginlägg om serietidningsutgåvan Serietidnings-torrents på Annas Arkiv För bakgrundshistorien om de olika Library Genesis-forkarna, se sidan för <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li innehåller det mesta av samma innehåll och metadata som Libgen.rs, men har några samlingar utöver detta, nämligen serier, tidskrifter och standarddokument. Den har också integrerat <a %(a_scihub)s>Sci-Hub</a> i sin metadata och sökmotor, vilket är vad vi använder för vår databas. Metadatan för detta bibliotek är fritt tillgänglig <a %(a_libgen_li)s>på libgen.li</a>. Denna server är dock långsam och stöder inte återupptagning av brutna anslutningar. Samma filer finns också tillgängliga på <a %(a_ftp)s>en FTP-server</a>, som fungerar bättre. Facklitteratur verkar också ha avvikit, men utan nya torrents. Det verkar ha hänt sedan början av 2022, även om vi inte har verifierat detta. Enligt administratören för Libgen.li bör "fiction_rus" (ryska skönlitteratur) samlingen täckas av regelbundet släppta torrents från <a %(a_booktracker)s>booktracker.org</a>, särskilt <a %(a_flibusta)s>flibusta</a> och <a %(a_librusec)s>lib.rus.ec</a> torrents (som vi speglar <a %(a_torrents)s>här</a>, även om vi ännu inte har fastställt vilka torrents som motsvarar vilka filer). Skönlitteratursamlingen har sina egna torrents (avvikande från <a %(a_href)s>Libgen.rs</a>) med start vid %(start)s. Vissa områden utan torrents (såsom skönlitteraturområden f_3463000 till f_4260000) är troligen Z-Library (eller andra dubbletter) filer, även om vi kanske vill göra en deduplicering och skapa torrents för lgli-unika filer i dessa områden. Statistik för alla samlingar kan hittas <a %(a_href)s>på libgens webbplats</a>. Torrents är tillgängliga för det mesta av det extra innehållet, särskilt torrents för serietidningar, magasin och standarddokument har släppts i samarbete med Annas Arkiv. Observera att torrentfilerna som hänvisar till ”libgen.is” är explicita speglar av <a %(a_libgen)s>Libgen.rs</a> (”.is” är en annan domän som används av Libgen.rs). En användbar resurs för att använda metadata är <a %(a_href)s>denna sida</a>. %(icon)s Deras "fiction_rus"-samling (ryska skönlitteratur) har inga dedikerade torrents, men täcks av torrents från andra, och vi håller en <a %(fiction_rus)s>spegel</a>. Ryska skönlitteratur torrents på Annas Arkiv Skönlitterära torrents på Annas Arkiv Diskussionsforum Metadata Metadata via FTP Tidskrifts-torrents på Annas Arkiv Information om metadatafält Spegel av andra torrents (och unika skönlitterära och serietidnings-torrents) Standarddokument torrents på Annas Arkiv Libgen.li Torrenter av Annas Arkiv (bokomslag) Library Genesis är känt för att redan generöst göra sina data tillgängliga i bulk via torrenter. Vår Libgen-samling består av hjälpdata som de inte släpper direkt, i samarbete med dem. Stort tack till alla inblandade i Library Genesis för att ni arbetar med oss! Vår blogg om bokomslagsutgåvan Denna sida handlar om ”.rs”-versionen. Den är känd för att konsekvent publicera både sin metadata och hela innehållet i sin bokkatalog. Dess boksamling är uppdelad mellan en skönlitterär och en facklitterär del. En användbar resurs för att använda metadata är <a %(a_metadata)s>denna sida</a> (blockerar IP-intervall, VPN kan krävas). Från och med 2024-03, publiceras nya torrents i <a %(a_href)s>denna forumtråd</a> (blockerar IP-intervall, VPN kan krävas). Skönlitteratur-torrenter på Annas Arkiv Libgen.rs Skönlitteratur-torrenter Libgen.rs Diskussionsforum Libgen.rs Metadata Information om metadatafält för Libgen.rs Libgen.rs Facklitteratur-torrenter Facklitteratur-torrenter på Annas Arkiv %(example)s för en skönlitterär bok. Denna <a %(blog_post)s>första utgåva</a> är ganska liten: cirka 300GB av bokomslag från Libgen.rs-forken, både skönlitteratur och facklitteratur. De är organiserade på samma sätt som de visas på libgen.rs, t.ex.: %(example)s för en facklitterär bok. Precis som med Z-Library-samlingen har vi lagt dem alla i en stor .tar-fil, som kan monteras med <a %(a_ratarmount)s>ratarmount</a> om du vill servera filerna direkt. Utgåva 1 (%(date)s) Den korta historien om de olika Library Genesis (eller ”Libgen”) förgreningarna är att över tid, de olika personerna involverade i Library Genesis hade en konflikt och gick skilda vägar. Enligt detta <a %(a_mhut)s>foruminlägg</a> var Libgen.li ursprungligen värd på "http://free-books.dontexist.com". ”.fun”-versionen skapades av den ursprungliga grundaren. Den omarbetas till förmån för en ny, mer distribuerad version. <a %(a_li)s>”.li”-versionen</a> har en massiv samling serietidningar, samt annat innehåll, som ännu inte är tillgängligt för bulk-nedladdning via torrents. Den har en separat torrent-samling av skönlitterära böcker, och den innehåller metadata från <a %(a_scihub)s>Sci-Hub</a> i sin databas. ”.rs”-versionen har mycket liknande data och släpper mest konsekvent sin samling i bulk-torrents. Den är ungefär uppdelad i en ”skönlitterär” och en ”facklitterär” sektion. Ursprungligen på "http://gen.lib.rus.ec". <a %(a_zlib)s>Z-Library</a> är på sätt och vis också en förgrening av Library Genesis, även om de använde ett annat namn för sitt projekt. Libgen.rs Vi berikar också vår samling med endast metadata-källor, som vi kan matcha till filer, t.ex. med hjälp av ISBN-nummer eller andra fält. Nedan följer en översikt över dessa. Återigen, vissa av dessa källor är helt öppna, medan för andra måste vi skrapa dem. Observera att i metadatasökningen visar vi de ursprungliga posterna. Vi gör ingen sammanslagning av poster. Endast metadata-källor Open Library är ett open source-projekt av Internet Archive för att katalogisera varje bok i världen. Det har en av världens största bokskanningsoperationer och har många böcker tillgängliga för digital utlåning. Dess bokmetadatakatalog är fritt tillgänglig för nedladdning och ingår i Annas Arkiv (dock inte för närvarande i sökningen, förutom om du uttryckligen söker efter ett Open Library-ID). Open Library Exklusive dubbletter Senast uppdaterad Procentandelar av antal filer %% speglad av AA / torrents tillgängliga Storlek Källa Nedan är en snabb översikt över källorna till filerna på Annas Arkiv. Eftersom skuggbiblioteken ofta synkroniserar data från varandra, finns det betydande överlappning mellan biblioteken. Det är därför siffrorna inte summerar till totalen. Procentandelen ”speglad och seedad av Annas Arkiv” visar hur många filer vi speglar själva. Vi seedar dessa filer i bulk genom torrents och gör dem tillgängliga för direkt nedladdning via partnerwebbplatser. Översikt Totalt Torrenter på Annas Arkiv För bakgrundsinformation om Sci-Hub, vänligen hänvisa till dess <a %(a_scihub)s>officiella webbplats</a>, <a %(a_wikipedia)s>Wikipedia-sida</a> och denna <a %(a_radiolab)s>podcastintervju</a>. Observera att Sci-Hub har varit <a %(a_reddit)s>fryst sedan 2021</a>. Det var fryst tidigare, men 2021 lades några miljoner artiklar till. Fortfarande läggs ett begränsat antal artiklar till i Libgen "scimag"-samlingarna, men inte tillräckligt för att motivera nya bulk-torrenter. Vi använder Sci-Hub metadata som tillhandahålls av <a %(a_libgen_li)s>Libgen.li</a> i dess "scimag"-samling. Vi använder också datasetet <a %(a_dois)s>dois-2022-02-12.7z</a>. Observera att "smarch"-torrenterna är <a %(a_smarch)s>föråldrade</a> och därför inte ingår i vår torrentlista. Torrenter på Libgen.li Torrenter på Libgen.rs Metadata och torrenter Uppdateringar på Reddit Podcastintervju Wikipedia-sida Sci-Hub Sci-Hub: fryst sedan 2021; de flesta tillgängliga via torrents Libgen.li: mindre tillägg sedan dess</div> Vissa källbibliotek främjar bulkdelning av sina data genom torrents, medan andra inte delar sin samling så lätt. I det senare fallet försöker Annas Arkiv att skrapa deras samlingar och göra dem tillgängliga (se vår <a %(a_torrents)s>Torrents</a>-sida). Det finns också mellansituationer, till exempel där källbibliotek är villiga att dela, men inte har resurserna att göra det. I dessa fall försöker vi också hjälpa till. Nedan följer en översikt över hur vi interagerar med de olika källbiblioteken. Källbibliotek %(icon)s Olika fil-databaser utspridda över det kinesiska internet; dock ofta betaldatabaser %(icon)s De flesta filer är endast tillgängliga med premium BaiduYun-konton; långsamma nedladdningshastigheter. %(icon)s Annas Arkiv hanterar en samling av <a %(duxiu)s>DuXiu-filer</a> %(icon)s Olika metadata-databaser utspridda över det kinesiska internet; dock ofta betaldatabaser %(icon)s Inga lättillgängliga metadatadumpar tillgängliga för hela deras samling. %(icon)s Annas Arkiv hanterar en samling av <a %(duxiu)s>DuXiu-metadata</a> Filer %(icon)s Filer endast tillgängliga för utlåning på begränsad basis, med olika åtkomstbegränsningar %(icon)s Annas Arkiv hanterar en samling av <a %(ia)s>IA-filer</a> %(icon)s Viss metadata tillgänglig genom <a %(openlib)s>Open Library-databasdumpar</a>, men de täcker inte hela IA-samlingen %(icon)s Inga lättillgängliga metadatadumpar tillgängliga för hela deras samling %(icon)s Annas Arkiv hanterar en samling av <a %(ia)s>IA metadata</a> Senast uppdaterad %(icon)s Annas Arkiv och Libgen.li hanterar tillsammans samlingar av <a %(comics)s>serietidningar</a>, <a %(magazines)s>magasin</a>, <a %(standarts)s>standarddokument</a> och <a %(fiction)s>skönlitteratur (avvikande från Libgen.rs)</a>. %(icon)s Facklitteraturtorrenter delas med Libgen.rs (och speglas <a %(libgenli)s>här</a>). %(icon)s Kvartalsvisa <a %(dbdumps)s>HTTP-databasedumpar</a> %(icon)s Automatiserade torrenter för <a %(nonfiction)s>Facklitteratur</a> och <a %(fiction)s>Skönlitteratur</a> %(icon)s Annas Arkiv hanterar en samling av <a %(covers)s>bokomslagstorrenter</a> %(icon)s Dagliga <a %(dbdumps)s>HTTP-databasdumpar</a> Metadata %(icon)s Månatliga <a %(dbdumps)s>databasdumpar</a> %(icon)s Data-torrenter tillgängliga <a %(scihub1)s>här</a>, <a %(scihub2)s>här</a>, och <a %(libgenli)s>här</a> %(icon)s Några nya filer <a %(libgenrs)s>läggs</a> <a %(libgenli)s>till</a> i Libgens "scimag", men inte tillräckligt för att motivera nya torrenter %(icon)s Sci-Hub har fryst nya filer sedan 2021. %(icon)s Metadata-dumpar tillgängliga <a %(scihub1)s>här</a> och <a %(scihub2)s>här</a>, samt som en del av <a %(libgenli)s>Libgen.li-databasen</a> (som vi använder) Källa %(icon)s Olika mindre eller enstaka källor. Vi uppmuntrar folk att ladda upp till andra skuggbibliotek först, men ibland har folk samlingar som är för stora för andra att sortera igenom, men inte tillräckligt stora för att motivera en egen kategori. %(icon)s Inte tillgänglig direkt i bulk, skyddad mot skrapning %(icon)s Annas Arkiv hanterar en samling av <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Annas Arkiv och Z-Library hanterar gemensamt en samling av <a %(metadata)s>Z-Library metadata</a> och <a %(files)s>Z-Library filer</a> Datasets Vi kombinerar alla ovanstående källor till en enhetlig databas som vi använder för att driva denna webbplats. Denna enhetliga databas är inte direkt tillgänglig, men eftersom Anna’s Arkiv är helt öppen källkod, kan den ganska enkelt <a %(a_generated)s>genereras</a> eller <a %(a_downloaded)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser. Skripten på den sidan kommer automatiskt att ladda ner all nödvändig metadata från de ovan nämnda källorna. Om du vill utforska våra data innan du kör dessa skript lokalt, kan du titta på våra JSON-filer, som länkar vidare till andra JSON-filer. <a %(a_json)s>Denna fil</a> är en bra startpunkt. Enhetlig databas Torrents av Annas Arkiv bläddra sök Olika mindre eller enstaka källor. Vi uppmuntrar folk att ladda upp till andra skuggbibliotek först, men ibland har folk samlingar som är för stora för andra att sortera igenom, men inte tillräckligt stora för att motivera en egen kategori. Översikt från <a %(a1)s>datasets-sidan</a>. Från <a %(a_href)s>aaaaarg.fail</a>. Verkar vara ganska komplett. Från vår volontär "cgiym". Från en <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ganska hög överlappning med befintliga papperssamlingar, men mycket få MD5-matcher, så vi bestämde oss för att behålla den helt. Skrapning av <q>iRead eBooks</q> (= fonetiskt <q>ai rit i-books</q>; airitibooks.com), av volontär <q>j</q>. Motsvarar <q>airitibooks</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>. Från en samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvis från den ursprungliga källan, delvis från the-eye.eu, delvis från andra speglar. Från en privat boktorrentwebbplats, <a %(a_href)s>Bibliotik</a> (ofta kallad “Bib”), vars böcker paketerades i torrents efter namn (A.torrent, B.torrent) och distribuerades genom the-eye.eu. Från vår volontär “bpb9v”. För mer information om <a %(a_href)s>CADAL</a>, se anteckningarna på vår <a %(a_duxiu)s>DuXiu dataset-sida</a>. Mer från vår volontär “bpb9v”, mestadels DuXiu-filer, samt en mapp “WenQu” och “SuperStar_Journals” (SuperStar är företaget bakom DuXiu). Från vår volontär "cgiym", kinesiska texter från olika källor (representerade som underkataloger), inklusive från <a %(a_href)s>China Machine Press</a> (en stor kinesisk förläggare). Icke-kinesiska samlingar (representerade som underkataloger) från vår volontär “cgiym”. Skrapning av böcker om kinesisk arkitektur, av volontär <q>cm</q>: <q>Jag fick det genom att utnyttja en nätverkssårbarhet på förlaget, men den luckan har sedan dess stängts</q>. Motsvarar <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>. Böcker från det akademiska förlaget <a %(a_href)s>De Gruyter</a>, samlade från några stora torrents. Skrapning av <a %(a_href)s>docer.pl</a>, en polsk fildelningswebbplats fokuserad på böcker och andra skriftliga verk. Skrapad i slutet av 2023 av volontär “p”. Vi har inte bra metadata från den ursprungliga webbplatsen (inte ens filtillägg), men vi filtrerade för bokliknande filer och kunde ofta extrahera metadata från filerna själva. DuXiu-epubs, direkt från DuXiu, samlade av volontär "w". Endast nyligen utgivna DuXiu-böcker är tillgängliga direkt genom e-böcker, så de flesta av dessa måste vara nya. Återstående DuXiu-filer från volontär "m", som inte var i DuXius proprietära PDG-format (den huvudsakliga <a %(a_href)s>DuXiu-datasetet</a>). Samlade från många ursprungliga källor, tyvärr utan att bevara dessa källor i filvägen. <span></span> <span></span> <span></span> Skrapning av erotiska böcker, av volontär <q>do no harm</q>. Motsvarar <q>hentai</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>. <span></span> <span></span> Samling skrapad från en japansk mangautgivare av volontär “t”. <a %(a_href)s>Utvalda rättsarkiv från Longquan</a>, tillhandahållna av volontären ”c”. Skrapning av <a %(a_href)s>magzdb.org</a>, en allierad till Library Genesis (den är länkad på libgen.rs hemsida) men som inte ville tillhandahålla sina filer direkt. Erhållen av volontären ”p” i slutet av 2023. <span></span> Olika små uppladdningar, för små för att vara en egen underkollektion, men representerade som kataloger. E-böcker från AvaxHome, en rysk fildelningswebbplats. Arkiv av tidningar och tidskrifter. Motsvarar <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>. Skrapning av <a %(a1)s>Philosophy Documentation Center</a>. Samling av volontären “o” som samlade polska böcker direkt från originalutgivningssidor (“scene”). Kombinerade samlingar av <a %(a_href)s>shuge.org</a> av volontärerna “cgiym” och “woz9ts”. <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (namngiven efter det fiktiva biblioteket), skrapad 2022 av volontären “t”. <span></span> <span></span> <span></span> Under-under-kollektioner (representerade som kataloger) från volontären “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (av <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mitt lilla bokrum — woz9ts: “Denna sida fokuserar främst på att dela högkvalitativa e-bokfiler, varav några är sätts av ägaren själv. Ägaren blev <a %(a_arrested)s>arresterad</a> 2019 och någon gjorde en samling av filer han delade.”). Återstående DuXiu-filer från volontär "woz9ts", som inte var i DuXius proprietära PDG-format (ska fortfarande konverteras till PDF). "Upload"-samlingen är uppdelad i mindre delsamlingar, som anges i AACIDs och torrentnamn. Alla delsamlingar deduplicerades först mot huvudsamlingen, även om metadatafilerna "upload_records" JSON fortfarande innehåller många referenser till de ursprungliga filerna. Icke-bokfiler togs också bort från de flesta delsamlingar och är vanligtvis <em>inte</em> noterade i "upload_records" JSON. Underkollektionerna är: Anteckningar Underkollektion Många underkollektioner består själva av under-underkollektioner (t.ex. från olika ursprungskällor), som representeras som kataloger i fältet “sökväg”. Uppladdningar till Annas Arkiv Vårt blogginlägg om dessa data <a %(a_worldcat)s>WorldCat</a> är en proprietär databas av den ideella organisationen <a %(a_oclc)s>OCLC</a>, som samlar metadata från bibliotek över hela världen. Det är troligen den största samlingen av biblioteksmetadata i världen. I oktober 2023 <a %(a_scrape)s>släppte</a> vi en omfattande skrapning av OCLC (WorldCat)-databasen, i <a %(a_aac)s>Annas Arkiv Containers-format</a>. Oktober 2023, första utgåvan: OCLC (WorldCat) Torrenter av Annas Arkiv Exempelpost på Annas Arkiv (ursprunglig samling) Exempelpost på Annas Arkiv (”zlib3” samling) Torrents av Annas Arkiv (metadata + innehåll) Blogginlägg om Utgåva 1 Blogginlägg om Utgåva 2 I slutet av 2022 arresterades de påstådda grundarna av Z-Library, och domäner beslagtogs av amerikanska myndigheter. Sedan dess har webbplatsen långsamt kommit tillbaka online. Det är okänt vem som för närvarande driver den. Uppdatering från februari 2023. Z-Library har sina rötter i <a %(a_href)s>Library Genesis</a>-gemenskapen och startade ursprungligen med deras data. Sedan dess har det professionaliserats avsevärt och har ett mycket modernare gränssnitt. De kan därför få många fler donationer, både ekonomiskt för att fortsätta förbättra sin webbplats, samt donationer av nya böcker. De har samlat en stor samling utöver Library Genesis. Samlingen består av tre delar. De ursprungliga beskrivningssidorna för de två första delarna är bevarade nedan. Du behöver alla tre delar för att få all data (förutom ersatta torrents, som är överstrukna på torrentsidan). %(title)s: vår första utgåva. Detta var den allra första utgåvan av det som då kallades ”Pirate Library Mirror” (”pilimi”). %(title)s: andra utgåvan, denna gång med alla filer inslagna i .tar-filer. %(title)s: inkrementella nya utgåvor, med hjälp av <a %(a_href)s>Annas Arkivs Containers (AAC) format</a>, nu utgivna i samarbete med Z-Library-teamet. Den ursprungliga spegeln erhölls mödosamt under 2021 och 2022. Vid denna tidpunkt är den något föråldrad: den återspeglar samlingens tillstånd i juni 2021. Vi kommer att uppdatera detta i framtiden. Just nu fokuserar vi på att få ut denna första utgåva. Eftersom Library Genesis redan är bevarad med offentliga torrents och ingår i Z-Library, gjorde vi en grundläggande deduplicering mot Library Genesis i juni 2022. För detta använde vi MD5-hashar. Det finns sannolikt mycket mer duplicerat innehåll i biblioteket, såsom flera filformat med samma bok. Detta är svårt att upptäcka exakt, så vi gör det inte. Efter dedupliceringen har vi över 2 miljoner filer kvar, totalt strax under 7TB. Samlingen består av två delar: en MySQL ”.sql.gz” dump av metadata, och de 72 torrentfilerna på runt 50-100GB vardera. Metadatan innehåller data som rapporterats av Z-Library-webbplatsen (titel, författare, beskrivning, filtyp), samt den faktiska filstorleken och md5sum som vi observerade, eftersom dessa ibland inte stämmer överens. Det verkar finnas filer för vilka Z-Library själv har felaktig metadata. Vi kan också ha laddat ner filer felaktigt i vissa enskilda fall, vilket vi kommer att försöka upptäcka och åtgärda i framtiden. De stora torrentfilerna innehåller den faktiska bokdatan, med Z-Library ID som filnamn. Filändelserna kan rekonstrueras med hjälp av metadata-dumpen. Samlingen är en blandning av facklitteratur och skönlitteratur (inte separerade som i Library Genesis). Kvaliteten varierar också mycket. Denna första utgåva är nu fullt tillgänglig. Observera att torrentfilerna endast är tillgängliga via vår Tor-spegel. Utgåva 1 (%(date)s) Detta är en enda extra torrentfil. Den innehåller ingen ny information, men den har viss data som kan ta tid att beräkna. Det gör det bekvämt att ha, eftersom nedladdning av denna torrent ofta är snabbare än att beräkna den från grunden. Speciellt innehåller den SQLite-index för tar-filerna, för användning med <a %(a_href)s>ratarmount</a>. Utgåva 2 tillägg (%(date)s) Vi har fått alla böcker som lades till i Z-Library mellan vår senaste spegel och augusti 2022. Vi har också gått tillbaka och skrapat några böcker som vi missade första gången. Sammantaget är denna nya samling cirka 24TB. Återigen är denna samling deduplicerad mot Library Genesis, eftersom det redan finns torrents tillgängliga för den samlingen. Datan är organiserad på liknande sätt som den första utgåvan. Det finns en MySQL “.sql.gz”-dump av metadata, som även inkluderar all metadata från den första utgåvan, vilket därmed ersätter den. Vi har också lagt till några nya kolumner: Vi nämnde detta förra gången, men bara för att klargöra: “filnamn” och “md5” är de faktiska egenskaperna hos filen, medan “filnamn_rapporterat” och “md5_rapporterat” är vad vi skrapade från Z-Library. Ibland stämmer dessa två inte överens, så vi inkluderade båda. För denna utgåva ändrade vi sorteringen till “utf8mb4_unicode_ci”, vilket bör vara kompatibelt med äldre versioner av MySQL. Datafilerna är liknande som förra gången, men de är mycket större. Vi orkade helt enkelt inte skapa massor av mindre torrentfiler. "pilimi-zlib2-0-14679999-extra.torrent" innehåller alla filer som vi missade i den senaste utgåvan, medan de andra torrenterna är alla nya ID-intervall.  <strong>Uppdatering %(date)s:</strong> Vi gjorde de flesta av våra torrenter för stora, vilket orsakade problem för torrentklienter. Vi har tagit bort dem och släppt nya torrenter. <strong>Uppdatering %(date)s:</strong> Det var fortfarande för många filer, så vi packade dem i tar-filer och släppte nya torrenter igen. %(key)s: om denna fil redan finns i Library Genesis, antingen i facklitteratur- eller skönlitteratursamlingen (matchad med md5). %(key)s: vilken torrent denna fil finns i. %(key)s: satt när vi inte kunde ladda ner boken. Utgåva 2 (%(date)s) Zlib-utgåvor (ursprungliga beskrivningssidor) Tor-domän Huvudwebbplats Z-Library skrapning Samlingen ”Kinesiska” i Z-Library verkar vara densamma som vår DuXiu-samling, men med olika MD5s. Vi exkluderar dessa filer från torrents för att undvika duplicering, men visar dem fortfarande i vår sökindex. Metadata Du får %(percentage)s%% bonus snabba nedladdningar, eftersom du blev hänvisad av användare %(profile_link)s. Detta gäller för hela medlemsperioden. Donera Kom med Vald upp till %(percentage)s%% rabatt Alipay stödjer internationella kredit-/debetkort. Se <a %(a_alipay)s>denna guide</a> för mer information. Skicka oss presentkort från Amazon.com med ditt kredit-/betalkort. Du kan köpa krypto med kredit-/betalkort. WeChat (Weixin Pay) stöder internationella kredit-/betalkort. I WeChat-appen, gå till ”Jag →Tjänster → Plånbok → Lägg till ett kort”. Om du inte ser det alternativet, aktivera det genom att gå till ”Jag → Inställningar → Allmänt → Verktyg → Weixin Pay → Aktivera”. (används när du skickar Ethereum från Coinbase) kopierad! kopia (lägsta minimibelopp) (varning: högt minimibelopp) -%(percentage)s%% 12 månader 1 månad 24 månader 3 månader 48 månader 6 månader 96 månader Välj hur länge din prenumeration ska gälla. <div %(div_monthly_cost)s></div><div %(div_after)s>efter <span %(span_discount)s></span> rabatter</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% i 12 månader i 1 månad i 24 månader i 3 månader i 48 månader i 6 månader i 96 månader %(monthly_cost)s / månad kontakta oss Direkta <strong>SFTP</strong> servrar Donationer på företagsnivå eller utbyte för nya samlingar (t.ex. nya inskanningar, OCR:ade datasamlingar). Experttillgång <strong>Obegränsad</strong> snabb tillgång <div %(div_question)s>Kan jag uppgradera mitt medlemskap eller få flera medlemskap?</div> <div %(div_question)s>Kan jag göra en donation utan att bli medlem?</div> Självklart. Vi accepterar donationer av valfri summa på denna Monero (XMR) adress: %(address)s. <div %(div_question)s>Vad betyder intervallen per månad?</div> Du kan nå den lägre sidan av ett intervall genom att tillämpa alla rabatter, såsom att välja en period längre än en månad. <div %(div_question)s>Förnyas medlemskap automatiskt?</div> Medlemskap <strong>förnyas inte</strong> automatiskt. Du kan gå med så länge eller kort som du vill. <div %(div_question)s>Vad används donationerna till?</div> 100%% går till att bevara och göra världens kunskap och kultur tillgänglig. För närvarande spenderar vi mestadels på servrar, lagring och bandbredd. Inga pengar går till några teammedlemmar personligen. <div %(div_question)s>Kan jag göra en stor donation?</div> Det skulle vara fantastiskt! För donationer över några tusen dollar, vänligen kontakta oss på %(email)s. <div %(div_question)s>Har ni andra betalmetoder?</div> För närvarande inte. Det finns många personer som inte vill att arkiv som detta ska existera, så vi måste vara försiktiga. Om du kan hjälpa oss att säkert anordna (mer praktiska) betalmetoder, vänligen kontakta oss på %(email)s. Vanliga frågor om donationer Du har en <a %(a_donation)s>nuvarande donation</a> under behandling. Var god färdigställ eller avbryt den donationen innan du skapar en ny donation. <a %(a_all_donations)s>Visa alla mina donationer</a> För donationer över 5000 dollar var god kontakta oss direkt på %(email)s. Vi välkomnar stora donationer från förmögna individer eller institutioner.  Var medveten om att även om medlemskapen på denna sida är "per månad", är de engångsdonationer (icke-återkommande). Se <a %(faq)s>Donations-FAQ</a>. Annas arkiv är ett ideellt projekt som är open-source och använder öppen data. Genom att donera och bli medlem, stödjer du vår verksamhet och utveckling. Till alla våra medlemmar: tack för att ni håller oss igång! ❤️ För mer information, kolla in <a %(a_donate)s>Donations-FAQ</a>. För att bli medlem, var god <a %(a_login)s>logga in eller registrera dig</a>. Tack för ditt stöd! $%(cost)s / månad Om du gjorde ett misstag under betalningen kan vi inte göra återbetalningar, men vi kommer att försöka rätta till det. Hitta “Krypto”-sidan i din PayPal-app eller på webbplatsen. Detta finns vanligtvis under “Ekonomi”. Gå till "Bitcoin"-sidan i din PayPal app eller på hemsidan. Klicka på knappen "Överför"%(transfer_icon)s, och sedan på "Skicka". Alipay Alipay 支付宝 / WeChat 微信 Amazon presentkort %(amazon)s presentkort Bankkort Bankkort (via app) Binance Kredit/debet/Apple/Google (BMC) Cash App Kredit/debitkort Kredit/debitkort 2 Kredit/debitkort (backup) Krypto %(bitcoin_icon)s Kort / PayPal / Venmo PayPal (USA) %(bitcoin_icon)s PayPal PayPal (vanlig) Pix (Brasilien) Revolut (just nu otillgänglig) WeChat Välj den kryptovaluta du föredrar: Donera med hjälp av ett presentkort från Amazon. <strong>VIKTIGT:</strong> Detta alternativ är för %(amazon)s. Om du vill använda en annan Amazon-webbplats, välj den ovan. <strong>VIKTIGT:</strong> Vi stödjer bara Amazon.com, inte andra amazonwebbsidor. Exempelvis, .se, .de, co.uk fungerar INTE. Vänligen skriv INTE ditt eget meddelande. Ange exakt belopp: %(amount)s Observera att vi måste runda upp till summor som godkänns av våra återförsäljares (minimum %(minimum)s). Donera med ett kredit-/betalkort, via Alipay-appen (superlätt att konfigurera). Installera Alipay-appen från <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>. Registrera dig med ditt telefonnummer. Inga ytterligare personuppgifter krävs. <span %(style)s>1</span>Installera Alipay-appen Stöds: Visa, MasterCard, JCB, Diners Club och Discover. Se <a %(a_alipay)s>denna guide</a> för mer information. <span %(style)s>2</span>Lägg till bankkort Med Binance köper du Bitcoin med ett kredit-/betalkort eller bankkonto och donerar sedan den bitcoinen till oss. På så sätt kan vi förbli säkra och anonyma när vi tar emot din donation. Binance är tillgängligt i nästan alla länder och fungerar med de flesta banker och kredit-/betalkort. Detta är för närvarande vår huvudsakliga rekommendation. Vi uppskattar att du tar dig tid att lära dig hur du donerar med denna metod, eftersom det hjälper oss mycket. För kreditkort, betalkort, Apple Pay och Google Pay använder vi “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). I deras system är en “kaffe” lika med $5, så din donation kommer att avrundas till närmaste multipel av 5. Donera med hjälp av Cash App. Om du har Cash App, så är detta det enklaste sättet att donera! Lägg märke till att för transaktioner under %(amount)s kan CashApp debitera en %(fee)s avgift. För %(amount)s eller högre är det gratis! Donera med ett kredit- eller debitkort. Denna metod använder en kryptovalutaleverantör som en mellanliggande konvertering. Detta kan vara lite förvirrande, så använd endast denna metod om andra betalningsmetoder inte fungerar. Det fungerar inte heller i alla länder. Vi kan inte stödja direktbetalningar med kredit-/debetkort, eftersom bankerna inte vill samarbeta med oss. ☹ Det finns dock flera sätt att använda kredit-/debetkort ändå, genom andra betalningsmetoder: Med krypto kan du donera genom att använda BTC, ETH, XMR, och SOL. Använd detta alternativ om du är van vid kryptovalutor. Med krypto kan du donera genom att använda BTC, ETH, XMR med flera. Krypto express-tjänster Om du använder krypto för första gången, föreslår vi att du använder %(options)s för att köpa och donera Bitcoin (den första och mest använda kryptovalutan). Observera att för små donationer så kan kreditkortavgifterna äta upp vår %(discount)s%% rabatt, så vi rekommenderar längre prenumerationer. Donera med kredit-/betalkort, PayPal eller Venmo. Du kan välja mellan dessa på nästa sida. Google Pay och Apple Pay kan också funka. Observera att avgifterna är höga för små donationer, så vi rekommenderar längre prenumerationer. För att donera med hjölp av PayPal US, so kommer vi använda PayPal Krypto, vilket möjliggör anonymitet. Vi uppskattar att du tar tid på dig att lära dig denna donationsmetod, eftersom den hjälper oss mycket. Donera med hjälp av PayPal. Donera med ditt vanliga PayPal-konto. Donera med Revolut. Om du har Revolut är detta det enklaste sättet att donera! Denna betalningsmetod tillåter endast ett maximum av %(amount)s. Vänligen välj en annan längd eller betalningsmetod. Denna betalningsmetod kräver minst %(amount)s. Vänligen välj en annan längd eller betalningsmetod. Binance Coinbase Kraken Vänligen välj en betalningsmetod. "Adoptera en torrent": ditt användarnamn eller meddelande i ett torrentfilnamn <div %(div_months)s> en gång per 12 månaders medlemskap</div> Ditt användarnamn eller anonyma omnämnande på tacksidan Förhandstillgång till nya funktioner Exklusiv Telegram med uppdateringar från bakom kulisserna %(number)s snabba nerladdningar per dag om du donerar den här månaden! <a %(a_api)s>JSON API</a>-åtkomst Legendarisk status i bevarandet av mänsklighetens kunskap och kultur Tidigare fördelar, plus: Få <strong>%(percentage)s%% bonusnedladdningar</strong> genom att <a %(a_refer)s>bjuda in vänner</a>. SciDB-uppsatser <strong>obegränsat</strong> utan verifikation När du ställer frågor om konto eller donationer, inkludera ditt kontonummer, skärmdumpar, kvitton, så mycket information som möjligt. Vi kontrollerar vår e-post var 1-2 vecka, så att inte inkludera denna information kommer att försena någon lösning. För att få ännu fler nedladdningar, <a %(a_refer)s>bjud in dina vänner</a>! Vi är ett litet gäng som jobbar ideellt. Det kan ta 1-2 veckor för oss att svara. Observera att kontots namn eller bild kan se konstigt ut. Du behöver inte oroa dig! Dessa konton hanteras av våra donationspartners. Våra konton har inte blivit hackade. Donera <span %(span_cost)s></span> <span %(span_label)s></span> i 12 månader "%(tier_name)s" i 1 månad "%(tier_name)s" i 24 månader "%(tier_name)s" i 3 månader "%(tier_name)s" i 48 månader “%(tier_name)s” i 6 månader "%(tier_name)s" i 96 månader “%(tier_name)s” Du kan fortfarande avbryta donationen under checkout. Klicka på donationsknappen för att godkänna donationen. <strong>Viktigt meddelande:</strong> Kryptopriser kan fluktuera kraftigt, ibland så mycket som 20%% på några minuter. Detta är fortfarande mindre än de avgifter vi ådrar oss hos betalningsleverantörer, som ofta tar 50-60%% för att arbeta med en "ideell skuggförening" som oss. <u>Om du skickar oss kvittot med det ursprungliga beloppet du betalade, kommer vi ändå att kreditera ditt konto för det valda medlemskapet</u> (så länge kvittot inte är äldre än några timmar). Vi uppskattar verkligen att du är villig att stå ut med sånt här för att stödja oss! ❤️ ❌ Något gick fel. Var god ladda om sidan och försök igen. <span %(span_circle)s>1</span>Köp Bitcoin på Paypal <span %(span_circle)s>2</span>Överför Bitcoinen till vår adress ✅ Omdirigerar till donationssidan… Donera Vänligen vänta minst <span %(span_hours)s>24 timmar</span> (och uppdatera denna sida) innan du kontaktar oss. Om du vill göra en donation (valfritt belopp) utan medlemskap, använd gärna denna Monero (XMR)-adress: %(address)s. Efter att du skickat ditt presentkort kommer vårt automatiserade system att bekräfta det inom några minuter. Om detta inte fungerar, försök att skicka ditt presentkort igen (<a %(a_instr)s>instruktioner</a>). Om det fortfarande inte fungerar, vänligen mejla oss så kommer Anna att manuellt granska det (det kan ta några dagar), och var noga med att nämna om du redan har försökt skicka om. Exempel: Vänligen använd <a %(a_form)s>officiella Amazon.com-formuläret</a> för att skicka oss ett presentkort på %(amount)s till e-postadressen nedan. “Mottagarens” e-post i formuläret: Amazon-presentkort Vi kan inte acceptera andra metoder för presentkort, <strong>endast skickade direkt från det officiella formuläret på Amazon.com</strong>. Vi kan inte returnera ditt presentkort om du inte använder detta formulär. Använd endast en gång. Unikt för ditt konto, dela inte. Väntar på presentkort… (uppdatera sidan för att kontrollera) Öppna <a %(a_href)s>QR-kod donationssidan</a>. Skanna QR-koden med Alipay-appen, eller tryck på knappen för att öppna Alipay-appen. Ha tålamod; sidan kan ta ett tag att ladda eftersom den är i Kina. <span %(style)s>3</span>Donera (skanna QR-koden eller tryck på knappen) Köp PYUSD-mynt via PayPal Köp Bitcoin (BTC) på Cash App Köp lite mer (vi rekommenderar %(more)s mer) än det belopp du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över. Gå till sidan ”Bitcoin” (BTC) i Cash App. Överför Bitcoinen till vår adress För små donationer (under 25 dollar) kan du behöva använda Rush eller Priority. Klicka på knappen ”Skicka bitcoin” för att göra ett ”uttag”. Byt från dollar till BTC genom att trycka på %(icon)s-ikonen. Ange BTC-beloppet nedan och klicka på ”Skicka”. Se <a %(help_video)s>denna video</a> om du fastnar. Express-tjänster är bekväma, men tar ut högre avgifter. Du kan använda detta istället för en krypto-börs om du vill göra en större donation snabbt och inte har något emot en avgift på 5-10 dollar. Se till att skicka exakt det krypto-belopp som visas på donationssidan, inte beloppet i amerikanska dollar. Annars kommer avgiften att dras utan att vi kan behandla ditt medlemskap automatiskt. Ibland kan bekräftelsen ta upp till 24 timmar, så se till att uppdatera den här sidan (även om den har gått ut). Instruktioner för kredit-/betalkort Donera via vår kredit- / betalkortsida Några av stegen nämner kryptoplånböcker, men oroa dig inte, du behöver inte lära dig något om krypto för detta. %(coin_name)s instruktioner Skanna den här QR -koden med din Crypto Wallet -app för att snabbt fylla i betalningsinformationen Skanna QR -kod för att betala Vi stöder endast standardversionen av kryptovalutor, inga exotiska nätverk eller versioner av mynt. Det kan ta upp till en timme att bekräfta transaktionen, beroende på myntet. Donera %(amount)s på <a %(a_page)s>denna sida</a>. Denna donation har gått ut. Vänligen avbryt och skapa en ny. Om du redan har betalat: Ja, jag har mejlat mitt kvitto Om växelkursen för kryptovalutan fluktuerade under transaktionen, se till att inkludera kvittot som visar den ursprungliga växelkursen. Vi uppskattar verkligen att du tar dig besväret att använda krypto, det hjälper oss mycket! ❌ Något gick fel. Vänligen ladda om sidan och försök igen. <span %(span_circle)s>%(circle_number)s</span>Maila oss kvittot Om du stöter på några problem, vänligen kontakta oss på %(email)s och skicka med så mycket information som möjligt (såsom skärmdumpar). ✅ Tack för din donation! Anna kommer att aktivera ditt medlemskap manuellt inom några dagar. Skicka ett kvitto eller en skärmdump till din personliga verifieringsadress: När du har skickat ditt kvitto via e-post, klicka på denna knapp, så att Anna kan granska det manuellt (detta kan ta några dagar): Skicka ett kvitto eller en skärmdump till din personliga verifieringsadress. Använd INTE denna e-postadress för din PayPal-donation. Avbryt Ja, avbryt Är du säker på att du vill avbryta? Avbryt inte om du redan betalat. ❌ Något gick fel. Var god ladda om sidan och försök igen. Gör en ny donation ✅ Din donation har avbrutits. Datum: %(date)s Identifierare: %(id)s Beställ igen Status: <span %(span_label)s>%(label)s</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad i %(duration)s månader, inklusive %(discounts)s%% rabatt)</span> Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad i %(duration)s månader)</span> 1. Ange din e-postadress. 2. Välj din betalningsmetod. 3. Välj din betalningsmetod igen. 4. Välj “Självhostad” plånbok. 5. Klicka på “Jag bekräftar ägarskap”. Du bör få ett e-postkvitto. Vänligen skicka det till oss, så bekräftar vi din donation så snart som möjligt. (du kanske vill avbryta och skapa en ny donation) Betalningsinstruktionerna har nu gått ut. Om du vill göra en ny donation, använd knappen "Beställ igen" ovan. Du har redan betalat. Vill du se betalningsinstruktionerna iallafall, klicka här: Visa gamla betalningsinstruktioner Om donationssidan blockeras, prova en annan internetanslutning (t.ex. VPN eller mobilt internet). Tyvärr är Alipay-sidan ofta endast tillgänglig från <strong>Kina</strong>. Du kan behöva tillfälligt inaktivera din VPN, eller använda en VPN till Kina (eller Hongkong fungerar ibland också). <span %(span_circle)s>1</span>Donera via Alipay Donera totalbeloppet av %(total)s genom <a %(a_account)s>detta Alipay-konto</a> Alipay-instruktioner <span %(span_circle)s>1</span>Överför till en utav våra krypto-konton Donera hela beloppet av %(total)s till en av dessa adresser: Kryptoinstruktioner Följ instruktionerna för att köpa Bitcoin (BTC). Du behöver bara köpa det belopp du vill donera, %(total)s. Ange vår Bitcoin (BTC)-adress som mottagare och följ instruktionerna för att skicka din donation på %(total)s: <span %(span_circle)s>1</span>Donera på Pix Donera hela beloppet av %(total)s genom <a %(a_account)s>detta Pix-konto Pix-instruktioner <span %(span_circle)s>1</span>Donera på WeChat Donera totalbeloppet av %(total)s med <a %(a_account)s>detta WeChat-konto</a> WeChat-instruktioner Använd någon av följande expresstjänster från kreditkort till Bitcoin, som bara tar några minuter: BTC / Bitcoin-adress (extern plånbok): BTC / Bitcoin-belopp: Fyll i följande uppgifter i formuläret: Om någon del av denna information är inaktuell, vänligen mejla oss och säg till. Vänligen använd detta <span %(underline)s>exakta belopp</span>. Din totala kostnad kan bli högre på grund av kreditkortsavgifter. För små donationer kan detta tyvärr vara mer än vår rabatt. (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, ingen verifiering för första transaktionen) (minimum: %(minimum)s) (minimum: %(minimum)s beroende på land, ingen verifiering för första transaktionen) Följ instruktionerna för att köpa PYUSD-mynt (PayPal USD). Köp lite mer (vi rekommenderar %(more)s mer) än beloppet du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över. Gå till “PYUSD”-sidan i din PayPal-app eller på webbplatsen. Tryck på “Överför”-knappen %(icon)s, och sedan “Skicka”. Uppdatera status För att återställa timern, skapa helt enkelt en ny donation. Var noga med att använda BTC-beloppet nedan, <em>INTE</em> euro eller dollar, annars får vi inte rätt belopp och kan inte automatiskt bekräfta ditt medlemskap. Köp Bitcoin (BTC) på Revolut Köp lite mer (vi rekommenderar %(more)s mer) än det belopp du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över. Gå till sidan ”Crypto” i Revolut för att köpa Bitcoin (BTC). Överför bitcoinen till vår adress För små donationer (under 25 dollar) kan du behöva använda Rush eller Priority. Klicka på knappen ”Skicka bitcoin” för att göra ett ”uttag”. Byt från euro till BTC genom att trycka på %(icon)s-ikonen. Ange BTC-beloppet nedan och klicka på ”Skicka”. Se <a %(help_video)s>denna video</a> om du fastnar. Status: 1 2 Steg-för-steg-guide Se steg-för-steg-guiden nedan. Annars kan du bli utelåst från detta konto! Om du inte redan har gjort det, skriv ner din hemliga nyckel för inloggning: Tack för din donation! Tid kvar: Donation Överför %(amount)s till %(account)s Väntar på bekräftelse (uppdatera sidan för att kontrollera)… Väntar på överföring (uppdatera sidan för att kontrollera)… Tidigare Snabba nedladdningar under de senaste 24 timmarna räknas mot den dagliga gränsen. Nedladdningar från Snabba Partner Servrar är markerade med %(icon)s. Senaste 18 timmarna Inga filer nedladdade ännu. Nedladdade filer visas inte offentligt. Alla tider anges i UTC. Nedladdade filer Om du laddade ner en fil med både snabba och långsamma nedladdningar, kommer den att visas två gånger. Oroa dig inte för mycket, det är många som laddar ner från webbplatser som vi länkar till, och det är extremt sällsynt att hamna i trubbel. Men för att vara säker rekommenderar vi att använda en VPN (betald), eller <a %(a_tor)s>Tor</a> (gratis). Jag laddade ner 1984 av George Orwell, kommer polisen att komma till min dörr? Du är Anna! Vem är Anna? Vi har ett stabilt JSON API för medlemmar, för att få en snabb nedladdnings-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentation inom JSON själv). För andra användningsfall, som att iterera genom alla våra filer, bygga anpassade sökningar, och så vidare, rekommenderar vi att <a %(a_generate)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Rådata kan manuellt utforskas <a %(a_explore)s>genom JSON-filer</a>. Vår råa torrentlista kan laddas ner som <a %(a_torrents)s>JSON</a> också. Har ni ett API? Vi är inte värdar för något upphovsrättsskyddat material här. Vi är en sökmotor och indexerar därför endast metadata som redan är offentligt tillgänglig. När du laddar ner från dessa externa källor föreslår vi att du kontrollerar lagarna i din jurisdiktion angående vad som är tillåtet. Vi är inte ansvariga för innehåll som värdas av andra. Om du har klagomål om vad du ser här, är ditt bästa alternativ att kontakta den ursprungliga webbplatsen. Vi uppdaterar regelbundet deras ändringar i vår databas. Om du verkligen tror att du har ett giltigt DMCA-klagomål som vi bör svara på, fyll i <a %(a_copyright)s>DMCA / Copyright-anspråksformuläret</a>. Vi tar dina klagomål på allvar och kommer att återkomma till dig så snart som möjligt. Hur rapporterar jag upphovsrättsintrång? Här är några böcker som har särskild betydelse för skuggarkiv och digital bevarande: Vilka är dina favoritböcker? Vi vill också påminna alla om att all vår kod och data är helt öppen källkod. Detta är unikt för projekt som vårt — vi känner inte till något annat projekt med en lika massiv katalog som också är helt öppen källkod. Vi välkomnar verkligen alla som tycker att vi driver vårt projekt dåligt att ta vår kod och data och sätta upp sitt eget skuggarkiv! Vi säger inte detta av illvilja eller något — vi tycker verkligen att detta skulle vara fantastiskt eftersom det skulle höja ribban för alla och bättre bevara mänsklighetens arv. Jag hatar hur ni driver detta projekt! Vi skulle älska om folk satte upp <a %(a_mirrors)s>speglar</a>, och vi kommer att stödja detta ekonomiskt. Hur kan jag hjälpa till? Det gör vi verkligen. Vår inspiration för att samla metadata är Aaron Swartz’ mål om “en webbsida för varje bok som någonsin publicerats”, för vilket han skapade <a %(a_openlib)s>Open Library</a>. Det projektet har gått bra, men vår unika position gör att vi kan få metadata som de inte kan. En annan inspiration var vår önskan att veta <a %(a_blog)s>hur många böcker det finns i världen</a>, så vi kan räkna ut hur många böcker vi fortfarande har kvar att rädda. Samlar ni metadata? Observera att mhut.org blockerar vissa IP-intervall, så en VPN kan behövas. <strong>Android:</strong> Klicka på menyn med tre punkter uppe till höger och välj "Lägg till på hemskärmen". <strong>iOS:</strong> Klicka på "Dela"-knappen längst ner och välj "Lägg till på hemskärmen". Vi har ingen officiell mobilapp, men du kan installera denna webbplats som en app. Har ni en mobilapp? Vänligen skicka dem till <a %(a_archive)s>Internet Archive</a>. De kommer att bevara dem på rätt sätt. Hur donerar jag böcker eller annat fysiskt material? Hur begär jag böcker? <a %(a_blog)s>Annas Blogg</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelbundna uppdateringar <a %(a_software)s>Annas Mjukvara</a> — vår öppen källkod <a %(a_datasets)s>Datasets</a> — om data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativa domäner Finns det fler resurser om Annas Arkiv? <a %(a_translate)s>Översätt på Annas Arkiv</a> — vårt översättningssystem <a %(a_wikipedia)s>Wikipedia</a> — mer om oss (hjälp gärna till att hålla denna sida uppdaterad, eller skapa en för ditt eget språk!) Välj de inställningar du gillar, håll sökrutan tom, klicka på “Sök” och bokmärk sedan sidan med din webbläsares bokmärkesfunktion. Hur sparar jag mina sökinställningar? Vi välkomnar säkerhetsforskare att söka efter sårbarheter i våra system. Vi är stora förespråkare av ansvarsfull rapportering. Kontakta oss <a %(a_contact)s>här</a>. Vi kan för närvarande inte erbjuda bug bounties, förutom för sårbarheter som har <a %(a_link)s>potential att kompromettera vår anonymitet</a>, för vilka vi erbjuder bounties i intervallet $10k-50k. Vi skulle vilja erbjuda bredare scope för bug bounties i framtiden! Observera att sociala ingenjörsattacker är utanför scope. Om du är intresserad av offensiv säkerhet och vill hjälpa till att arkivera världens kunskap och kultur, se till att kontakta oss. Det finns många sätt på vilka du kan hjälpa till. Har ni ett program för ansvarsfull rapportering? Vi har bokstavligen inte tillräckligt med resurser för att ge alla i världen höguppladdningshastigheter, hur mycket vi än skulle vilja. Om en rik välgörare skulle vilja stiga fram och tillhandahålla detta för oss, skulle det vara otroligt, men tills dess gör vi vårt bästa. Vi är ett ideellt projekt som knappt kan upprätthålla sig självt genom donationer. Det är därför vi har implementerat två system för gratis nedladdningar, med våra partners: delade servrar med långsamma nedladdningar, och något snabbare servrar med en väntelista (för att minska antalet personer som laddar ner samtidigt). Vi har också <a %(a_verification)s>webbläsarverifiering</a> för våra långsamma nedladdningar, eftersom bots och scrapers annars skulle missbruka dem, vilket gör det ännu långsammare för legitima användare. Observera att när du använder Tor Browser kan du behöva justera dina säkerhetsinställningar. På den lägsta av alternativen, kallad ”Standard”, lyckas Cloudflare turnstile-utmaningen. På de högre alternativen, kallade ”Säkrare” och ”Säkraste”, misslyckas utmaningen. För stora filer kan ibland långsamma nedladdningar avbrytas i mitten. Vi rekommenderar att du använder en nedladdningshanterare (som JDownloader) för att automatiskt återuppta stora nedladdningar. Varför är de långsamma nedladdningarna så långsamma? Vanliga frågor (FAQ) Använd <a %(a_list)s>torrentlistgeneratorn</a> för att skapa en lista över torrents som mest behöver seedas, inom dina lagringsgränser. Ja, se sidan för <a %(a_llm)s>LLM-data</a>. De flesta torrents innehåller filerna direkt, vilket innebär att du kan instruera torrentklienter att endast ladda ner de nödvändiga filerna. För att bestämma vilka filer som ska laddas ner kan du <a %(a_generate)s>generera</a> vår metadata, eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Tyvärr innehåller ett antal torrentkollektioner .zip- eller .tar-filer i roten, vilket innebär att du måste ladda ner hela torrenten innan du kan välja enskilda filer. Inga lättanvända verktyg för att filtrera torrents är tillgängliga ännu, men vi välkomnar bidrag. (Vi har dock <a %(a_ideas)s>några idéer</a> för det senare fallet.) Långt svar: Kort svar: inte enkelt. Vi försöker hålla minimal duplicering eller överlappning mellan torrenterna i denna lista, men detta kan inte alltid uppnås och beror mycket på källbibliotekens policyer. För bibliotek som släpper sina egna torrenter ligger det utanför vår kontroll. För torrenter som släpps av Annas Arkiv deduplicerar vi endast baserat på MD5-hash, vilket innebär att olika versioner av samma bok inte dedupliceras. Ja. Dessa är faktiskt PDF- och EPUB-filer, de har bara ingen filändelse i många av våra torrenter. Det finns två ställen där du kan hitta metadata för torrentfiler, inklusive filtyper/ändelser: 1. Varje samling eller utgåva har sin egen metadata. Till exempel har <a %(a_libgen_nonfic)s>Libgen.rs-torrenter</a> en motsvarande metadatabas som är värd på Libgen.rs-webbplatsen. Vi länkar vanligtvis till relevanta metadataresurser från varje samlings <a %(a_datasets)s>datasettsida</a>. 2. Vi rekommenderar att <a %(a_generate)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Dessa innehåller en mappning för varje post i Annas Arkiv till dess motsvarande torrentfiler (om tillgängliga), under "torrent_paths" i ElasticSearch JSON. Vissa torrentklienter stöder inte stora bitstorlekar, vilket många av våra torrents har (för nyare gör vi inte detta längre — även om det är giltigt enligt specifikationerna!). Så prova en annan klient om du stöter på detta, eller klaga till tillverkarna av din torrentklient. Jag vill gärna hjälpa till att seeda, men jag har inte mycket diskutrymme. Torrenterna är för långsamma; kan jag ladda ner data direkt från er? Kan jag ladda ner endast en delmängd av filerna, som bara ett visst språk eller ämne? Hur hanterar ni dubbletter i torrenterna? Kan jag få torrentlistan som JSON? Jag ser inga PDF- eller EPUB-filer i torrenterna, bara binära filer? Vad gör jag? Varför kan min torrentklient inte öppna några av era torrentfiler / magnetlänkar? Vanliga frågor om torrents Hur laddar jag upp nya böcker? Vänligen se <a %(a_href)s>detta utmärkta projekt</a>. Har du en upptidövervakare? Vad är Annas Arkiv? Bli medlem för att använda snabba nedladdningar. Vi stödjer nu Amazon-presentkort, kredit- och betalkort, krypto, Alipay och WeChat. Du har slut på snabba nedladdningar idag. Åtkomst Timvisa nedladdningar de senaste 30 dagarna. Timvis genomsnitt: %(hourly)s. Dagligt genomsnitt: %(daily)s. Vi arbetar med partners för att göra våra samlingar lättillgängliga och gratis för alla. Vi tror att alla har rätt till mänsklighetens samlade visdom. Och <a %(a_search)s>inte på bekostnad av författarna</a>. Dataseten som används i Annas Arkiv är helt öppna och kan speglas i bulk med hjälp av torrents. <a %(a_datasets)s>Läs mer…</a> Långtidsarkiv Fullständig databas Sök Böcker, artiklar, tidskrifter, serier, biblioteksregister, metadata, … All vår <a %(a_code)s>kod</a> och <a %(a_datasets)s>data</a> är helt öppen källkod. <span %(span_anna)s>Annas Arkiv</span> är ett ideellt projekt med två mål: <li><strong>Bevarande:</strong> Säkerhetskopiera all mänsklighetens kunskap och kultur.</li><li><strong>Tillgång:</strong> Göra denna kunskap och kultur tillgänglig för alla i världen.</li> Vi har världens största samling av högkvalitativ textdata. <a %(a_llm)s>Läs mer…</a> LLM training data 🪩 Speglar: uppmaning till volontärer Om du driver en högrisk anonym betalningsprocessor, vänligen kontakta oss. Vi söker också personer som vill placera smakfulla små annonser. Alla intäkter går till våra bevarandeinsatser. Bevarande Vi uppskattar att vi har bevarat cirka <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% av världens böcker</a>. Vi bevarar böcker, artiklar, serier, tidskrifter och mer, genom att samla dessa material från olika <a href="https://en.wikipedia.org/wiki/Shadow_library">skuggbibliotek</a>, officiella bibliotek och andra samlingar på ett ställe. All denna data bevaras för alltid genom att göra det enkelt att duplicera den i bulk — med hjälp av torrents — vilket resulterar i många kopior runt om i världen. Vissa skuggbibliotek gör redan detta själva (t.ex. Sci-Hub, Library Genesis), medan Annas Arkiv ”befriar” andra bibliotek som inte erbjuder bulkdistribution (t.ex. Z-Library) eller inte är skuggbibliotek alls (t.ex. Internet Archive, DuXiu). Denna breda distribution, kombinerad med öppen källkod, gör vår webbplats motståndskraftig mot nedtagningar och säkerställer långsiktig bevarande av mänsklighetens kunskap och kultur. Läs mer om <a href="/datasets">våra datasets</a>. Om du är en <a %(a_member)s>medlem</a>, krävs ingen webbläsarverifiering. 🧬&nbsp;SciDB är en fortsättning på Sci-Hub. SciDB Öppna DOI Sci-Hub har <a %(a_paused)s>pausat</a> uppladdningen av nya artiklar. Direkt tillgång till %(count)s akademiska artiklar 🧬&nbsp;SciDB är en fortsättning på Sci-Hub, med dess välbekanta gränssnitt och direkt visning av PDF-filer. Ange din DOI för att visa. Vi har hela Sci-Hub-samlingen, samt nya artiklar. De flesta kan ses direkt med ett välbekant gränssnitt, liknande Sci-Hub. Vissa kan laddas ner via externa källor, i sådana fall visar vi länkar till dessa. Du kan hjälpa till enormt genom att seeda torrents. <a %(a_torrents)s>Läs mer…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Letar efter volontärer Som ett ideellt, öppen källkodsprojekt letar vi alltid efter personer som kan hjälpa till. IPFS-nedladdningar Lista av %(by)s, skapad <span %(span_time)s>%(time)s</span> Spara ❌ Något gick fel. Försök igen. ✅ Sparad. Vänligen ladda om sidan. Listan är tom. redigera Lägg till eller ta bort från denna lista genom att hitta en fil och öppna fliken ”Listor”. Lista Hur vi kan hjälpa Ta bort överlappning (deduplicering) Text- och metadatautvinning OCR Vi kan tillhandahålla höghastighetsåtkomst till våra fullständiga samlingar, samt till oavslöjade samlingar. Detta är företagsnivååtkomst som vi kan tillhandahålla för donationer i storleksordningen tiotusentals USD. Vi är också villiga att byta detta mot högkvalitativa samlingar som vi ännu inte har. Vi kan återbetala dig om du kan förse oss med berikning av vår data, såsom: Stöd långsiktig arkivering av mänsklig kunskap, samtidigt som du får bättre data för din modell! <a %(a_contact)s>Kontakta oss</a> för att diskutera hur vi kan samarbeta. Det är välkänt att LLM:er trivs med högkvalitativ data. Vi har världens största samling av böcker, artiklar, tidskrifter, etc., som är några av de högsta kvalitetstextkällorna. LLM-data Unik skala och räckvidd Vår samling innehåller över hundra miljoner filer, inklusive akademiska tidskrifter, läroböcker och tidskrifter. Vi uppnår denna skala genom att kombinera stora befintliga arkiv. Några av våra källsamlingar är redan tillgängliga i bulk (Sci-Hub och delar av Libgen). Andra källor har vi befriat själva. <a %(a_datasets)s>Datasets</a> visar en fullständig översikt. Vår samling inkluderar miljontals böcker, artiklar och tidskrifter från tiden före e-boken. Stora delar av denna samling har redan OCR:ats och har redan lite intern överlappning. Fortsätt Om du har tappat din nyckel, vänligen <a %(a_contact)s>kontakta oss</a> och ge så mycket information som möjligt. Du kan behöva skapa ett nytt tillfälligt konto för att kontakta oss. Vänligen <a %(a_account)s>logga in</a> för att se denna sida.</a> För att förhindra spam-botar från att skapa massor av konton, behöver vi först verifiera din webbläsare. Om du fastnar i en oändlig loop rekommenderar vi att du installerar <a %(a_privacypass)s>Privacy Pass</a>. Det kan också hjälpa att stänga av annonsblockerare och andra webbläsartillägg. Logga in / Registrera Annas Arkiv är tillfälligt nere för underhåll. Vänligen kom tillbaka om en timme. Alternativ författare Alternativ beskrivning Alternativ utgåva Alternativ filändelse Alternativt filnamn Alternativt förlag Alternativ titel datum öppen källkod Läs mer… beskrivning Sök i Annas Arkiv efter CADAL SSNO-nummer Sök i Annas Arkiv efter DuXiu SSID-nummer Sök i Annas Arkiv efter DuXiu DXID nummer Sök i Annas Arkiv efter ISBN Sök i Annas Arkiv efter OCLC (WorldCat) nummer Sök i Annas Arkiv efter Open Library ID Annas Arkiv onlinevisare %(count)s påverkade sidor Efter nedladdning: En bättre version av denna fil kan finnas på %(link)s Massnedladdningar via torrent samling Använd onlineverktyg för att konvertera mellan format. Rekommenderade konverteringsverktyg: %(links)s För stora filer rekommenderar vi att använda en nedladdningshanterare för att undvika avbrott. Rekommenderade nedladdningshanterare: %(links)s EBSCOhost eBook Index (endast experter) (klicka även på "GET" högst upp) (klicka på “GET” högst upp på sidan) Externa nedladdningar <strong>🚀 Snabba nedladdningar</strong> Du har %(remaining)s kvar idag. Tack för att du är medlem! ❤️ <strong>🚀 Snabba nedladdningar</strong> Du har använt alla snabba nedladdningar för idag. <strong>🚀 Snabba nedladdningar</strong> Du har nyligen laddat ner denna fil. Länkarna förblir giltiga ett tag. <strong>🚀 Snabba nedladdningar</strong> Bli en <a %(a_membership)s>medlem</a> för att stödja det långsiktiga bevarandet av böcker, artiklar och mer. Som ett tack för ditt stöd får du snabba nedladdningar. ❤️ 🚀 Snabba nedladdningar 🐢 Långsamma nedladdningar Låna från Internet Archive IPFS Gateway #%(num)d (du kan behöva prova fler gånger med IPFS) Libgen.li Libgen.rs Skönlitteratur Libgen.rs Facklitteratur deras annonser är kända för att innehålla skadlig programvara, så använd en annonsblockerare eller klicka inte på annonser Amazons ”Skicka till Kindle” djazzs ”Skicka till Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Nexus/STC-filer kan vara opålitliga att ladda ner) Inga nedladdningar hittades. Alla nedladdningsalternativ bör vara säkra att använda. Var dock alltid försiktig när du laddar ner filer från internet. Se till att hålla dina enheter uppdaterade. (ingen omdirigering) Öppna i vår visare (öppna i visare) Alternativ #%(num)d: %(link)s %(extra)s Hitta originalposten i CADAL Sök manuellt på DuXiu Hitta originalposten i ISBNdb Hitta originalposten i WorldCat Hitta originalposten i Open Library Sök i olika andra databaser efter ISBN (utskriftsbegränsade användare endast) PubMed Du behöver en e-bok- eller PDF-läsare för att öppna filen, beroende på filformatet. Rekommenderade e-boksläsare: %(links)s Annas Arkiv 🧬 SciDB Sci-Hub: %(doi)s (associerad DOI är kanske inte tillgänglig i Sci-Hub) Du kan skicka både PDF- och EPUB-filer till din Kindle eller Kobo eReader. Rekommenderade verktyg: %(links)s Mer information i <a %(a_slow)s>FAQ</a>. Stöd författare och bibliotek Om du gillar detta och har råd, överväg att köpa originalet eller stödja författarna direkt. Om detta finns tillgängligt på ditt lokala bibliotek, överväg att låna det gratis där. Partnerservernedladdningar är just nu inte tillgängliga för denna fil. torrent Från betrodda partners. Z-Library Z-Library på Tor (kräver webbläsaren Tor) visa externa nedladdningar <span class="font-bold">❌ Denna fil kan ha problem, och har dolts från ett källbibliotek.</span> Ibland är detta på begäran av en upphovsrättsinnehavare, ibland beror det på att det finns ett bättre alternativ, men ibland är det på grund av ett problem med själva filen. Det kan fortfarande gå bra att ladda ner, men vi rekommenderar att du först söker efter en alternativ fil. Mer detaljer: Om du fortfarande vill ladda ner den här filen, se till att endast använda pålitlig och uppdaterad programvara för att öppna den. metadatakommentarer AA: Sök i Annas Arkiv efter “%(name)s” Kodutforskare: Visa i Kodutforskare ”%(name)s” URL: Webbplats: Om du har den här filen och den ännu inte är tillgänglig i Annas Arkiv, överväg att <a %(a_request)s>ladda upp den</a>. Internet Archive Controlled Digital Lending fil “%(id)s” Detta är en post av en fil från Internet Archive, inte en direkt nedladdningsbar fil. Du kan försöka låna boken (länk nedan), eller använda denna URL när du <a %(a_request)s>begär en fil</a>. Förbättra metadata CADAL SSNO %(id)s metadata post Detta är en metadata-post, inte en nedladdningsbar fil. Du kan använda denna URL när du <a %(a_request)s>begär en fil</a>. DuXiu SSID %(id)s metadata post ISBNdb %(id)s metadata post MagzDB ID %(id)s metadata post Nexus/STC ID %(id)s metadata post OCLC (WorldCat) nummer %(id)s metadata post Open Library %(id)s metadata post Sci-Hub fil “%(id)s” Hittades inte “%(md5_input)s” hittades inte i vår databas. Lägg till kommentar (%(count)s) Du kan få md5 från URL:en, t.ex. MD5 av en bättre version av denna fil (om tillämpligt). Fyll i detta om det finns en annan fil som nära matchar denna fil (samma upplaga, samma filändelse om du kan hitta en), som folk bör använda istället för denna fil. Om du känner till en bättre version av denna fil utanför Annas Arkiv, vänligen <a %(a_upload)s>ladda upp den</a>. Något gick fel. Vänligen ladda om sidan och försök igen. Du lämnade en kommentar. Det kan ta en minut innan den visas. Vänligen använd <a %(a_copyright)s>DMCA / Copyright-ansökningsformuläret</a>. Beskriv problemet (obligatoriskt) Om denna fil har hög kvalitet kan du diskutera allt om den här! Om inte, vänligen använd knappen ”Rapportera filproblem”. Utmärkt filkvalitet (%(count)s) Filkvalitet Lär dig hur du <a %(a_metadata)s>förbättrar metadata</a> för denna fil själv. Problem beskrivning Vänligen <a %(a_login)s>logga in</a>. Jag älskade den här boken! Hjälp gemenskapen genom att rapportera kvaliteten på denna fil! 🙌 Något gick fel. Vänligen ladda om sidan och försök igen. Rapportera filproblem (%(count)s) Tack för att du skickade in din rapport. Den kommer att visas på denna sida, samt granskas manuellt av Anna (tills vi har ett ordentligt modereringssystem). Lämna kommentar Skicka rapport Vad är fel med denna fil? Låna (%(count)s) Kommentarer (%(count)s) Nedladdningar (%(count)s) Utforska metadata (%(count)s) Listor (%(count)s) Statistik (%(count)s) För information om denna specifika fil, kolla in dess <a %(a_href)s>JSON-fil</a>. Detta är en fil som hanteras av <a %(a_ia)s>IA:s Controlled Digital Lending</a>-bibliotek och indexeras av Annas Arkiv för sökning. För information om de olika datasets som vi har sammanställt, se <a %(a_datasets)s>Datasets-sidan</a>. Metadata från länkad post Förbättra metadata på Open Library En ”fil MD5” är en hash som beräknas från filens innehåll och är rimligt unik baserat på det innehållet. Alla skuggbibliotek som vi har indexerat här använder främst MD5 för att identifiera filer. En fil kan förekomma i flera skuggbibliotek. För information om de olika datasets som vi har sammanställt, se <a %(a_datasets)s>Datasets-sidan</a>. Rapportera filkvalitet Totala nedladdningar: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tjeckiska metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Böcker %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Varning: flera länkade poster: När du tittar på en bok på Annas Arkiv kan du se olika fält: titel, författare, förlag, upplaga, år, beskrivning, filnamn och mer. All denna information kallas <em>metadata</em>. Eftersom vi kombinerar böcker från olika <em>källbibliotek</em>, visar vi den metadata som finns tillgänglig i det källbiblioteket. Till exempel, för en bok som vi fått från Library Genesis, visar vi titeln från Library Genesis’ databas. Ibland finns en bok i <em>flera</em> källbibliotek, som kan ha olika metadatafält. I så fall visar vi helt enkelt den längsta versionen av varje fält, eftersom den förhoppningsvis innehåller den mest användbara informationen! Vi visar fortfarande de andra fälten under beskrivningen, t.ex. som ”alternativ titel” (men bara om de är olika). Vi extraherar också <em>koder</em> som identifierare och klassificerare från källbiblioteket. <em>Identifierare</em> representerar unikt en viss upplaga av en bok; exempel är ISBN, DOI, Open Library ID, Google Books ID eller Amazon ID. <em>Klassificerare</em> grupperar flera liknande böcker; exempel är Dewey Decimal (DCC), UDC, LCC, RVK eller GOST. Ibland är dessa koder explicit länkade i källbiblioteken, och ibland kan vi extrahera dem från filnamnet eller beskrivningen (främst ISBN och DOI). Vi kan använda identifierare för att hitta poster i <em>metadata-samlingar</em>, såsom OpenLibrary, ISBNdb eller WorldCat/OCLC. Det finns en specifik <em>metadata-flik</em> i vår sökmotor om du vill bläddra i dessa samlingar. Vi använder matchande poster för att fylla i saknade metadatafält (t.ex. om en titel saknas), eller t.ex. som ”alternativ titel” (om det finns en befintlig titel). För att se exakt var metadata för en bok kom ifrån, se fliken <em>“Tekniska detaljer”</em> på en boksida. Den har en länk till rå JSON för den boken, med pekare till rå JSON för de ursprungliga posterna. För mer information, se följande sidor: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Sök (metadata-flik)</a>, <a %(a_codes)s>Koder Explorer</a>, och <a %(a_example)s>Exempel på metadata JSON</a>. Slutligen kan all vår metadata <a %(a_generated)s>genereras</a> eller <a %(a_downloaded)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser. Bakgrund Du kan hjälpa till med att bevara böcker genom att förbättra metadata! Läs först bakgrunden om metadata på Annas Arkiv, och lär dig sedan hur du förbättrar metadata genom att länka med Open Library, och få gratis medlemskap på Annas Arkiv. Förbättra metadata Så om du stöter på en fil med dålig metadata, hur ska du fixa det? Du kan gå till källbiblioteket och följa dess procedurer för att fixa metadata, men vad ska du göra om en fil finns i flera källbibliotek? Det finns en identifierare som behandlas speciellt på Annas Arkiv. <strong>Fältet annas_archive md5 på Open Library åsidosätter alltid all annan metadata!</strong> Låt oss backa lite först och lära oss om Open Library. Open Library grundades 2006 av Aaron Swartz med målet att ”en webbsida för varje bok som någonsin publicerats”. Det är som en Wikipedia för bokmetadata: alla kan redigera den, den är fritt licensierad och kan laddas ner i bulk. Det är en bokdatabas som mest överensstämmer med vårt uppdrag — faktiskt har Annas Arkiv inspirerats av Aaron Swartz’ vision och liv. Istället för att uppfinna hjulet på nytt, beslutade vi att omdirigera våra volontärer till Open Library. Om du ser en bok som har felaktig metadata, kan du hjälpa till på följande sätt: Observera att detta endast fungerar för böcker, inte akademiska artiklar eller andra typer av filer. För andra typer av filer rekommenderar vi fortfarande att hitta källbiblioteket. Det kan ta några veckor för ändringar att inkluderas i Annas Arkiv, eftersom vi behöver ladda ner den senaste Open Library-datadumpen och återskapa vår sökindex.  Gå till <a %(a_openlib)s>Open Librarys webbplats</a>. Hitta rätt bokpost. <strong>VARNING:</strong> var noga med att välja rätt <strong>upplaga</strong>. I Open Library har du ”verk” och ”upplagor”. Ett ”verk” kan vara ”Harry Potter och de vises sten”. En ”upplaga” kan vara: Första utgåvan från 1997 publicerad av Bloomsbery med 256 sidor. Pocketutgåvan från 2003 publicerad av Raincoast Books med 223 sidor. Den polska översättningen från 2000 ”Harry Potter I Kamie Filozoficzn” av Media Rodzina med 328 sidor. Alla dessa utgåvor har olika ISBN och olika innehåll, så se till att välja rätt! Redigera posten (eller skapa den om ingen finns), och lägg till så mycket användbar information som möjligt! Du är ju här nu ändå, så du kan lika gärna göra posten riktigt fantastisk. Under ”ID-nummer” välj ”Annas Arkiv” och lägg till bokens MD5 från Annas Arkiv. Detta är den långa strängen av bokstäver och siffror efter ”/md5/” i URL:en. Försök hitta andra filer i Annas Arkiv som också matchar denna post, och lägg till dem också. I framtiden kan vi gruppera dessa som dubbletter på Annas Arkivs söksida. När du är klar, skriv ner URL:en som du just uppdaterade. När du har uppdaterat minst 30 poster med Annas Arkivs MD5:or, skicka oss ett <a %(a_contact)s>email</a> och skicka oss listan. Vi ger dig ett gratis medlemskap för Annas Arkiv, så att du lättare kan göra detta arbete (och som ett tack för din hjälp). Dessa måste vara högkvalitativa redigeringar som lägger till betydande mängder information, annars kommer din begäran att avvisas. Din begäran kommer också att avvisas om någon av redigeringarna blir återkallade eller korrigerade av Open Library-moderatorer. Länkning till Open Library Om du blir betydligt involverad i utvecklingen och driften av vårt arbete, kan vi diskutera att dela mer av donationsintäkterna med dig, för att du ska kunna använda dem vid behov. Vi kommer endast att betala för värd när du har allt uppsatt och har visat att du kan hålla arkivet uppdaterat med uppdateringar. Detta innebär att du måste betala för de första 1-2 månaderna ur egen ficka. Din tid kommer inte att kompenseras (och inte heller vår), eftersom detta är rent volontärarbete. Vi är villiga att täcka värd- och VPN-kostnader, initialt upp till $200 per månad. Detta är tillräckligt för en grundläggande sökserver och en DMCA-skyddad proxy. Värdkostnader Vänligen <strong>kontakta oss inte</strong> för att be om tillstånd eller för grundläggande frågor. Handlingar talar högre än ord! All information finns där ute, så sätt bara igång med att sätta upp din spegel. Känn dig fri att posta biljetter eller sammanslagningsförfrågningar till vår Gitlab när du stöter på problem. Vi kan behöva bygga några spegelspecifika funktioner med dig, såsom omprofilering från ”Anna’s Archive” till ditt webbplatsnamn, (initialt) inaktivera användarkonton, eller länka tillbaka till vår huvudsida från boksidor. När du har din spegel igång, vänligen kontakta oss. Vi skulle gärna vilja granska din OpSec, och när den är solid, kommer vi att länka till din spegel och börja arbeta närmare tillsammans med dig. Tack på förhand till alla som är villiga att bidra på detta sätt! Det är inte för de svaghjärtade, men det skulle stärka livslängden för det största verkligt öppna biblioteket i mänsklighetens historia. Komma igång För att öka motståndskraften hos Annas Arkiv söker vi volontärer för att driva spegelservrar. Din version är tydligt utmärkt som en spegel, t.ex. ”Bobs Arkiv, en Anna’s Archive-spegel”. Du är villig att ta de risker som är förknippade med detta arbete, vilka är betydande. Du har en djup förståelse för den operativa säkerhet som krävs. Innehållet i <a %(a_shadow)s>dessa</a> <a %(a_pirate)s>inlägg</a> är självklara för dig. Inledningsvis kommer vi inte att ge dig tillgång till våra partnerservers nedladdningar, men om allt går bra kan vi dela det med dig. Du driver Anna’s Archive öppen källkod, och du uppdaterar regelbundet både koden och datan. Du är villig att bidra till vår <a %(a_codebase)s>kodbas</a> — i samarbete med vårt team — för att göra detta möjligt. Vi letar efter detta: Spegelservrar: upprop för volontärer Gör en ny donation. Inga donationer än. <a %(a_donate)s>Gör min första donation.</a> Donationsdetaljer visas inte offentligt. Mina donationer 📡 För bulk-spegling av vår samling, kolla in sidorna för <a %(a_datasets)s>Datasets</a> och <a %(a_torrents)s>Torrents</a>. Nedladdningar från din IP-adress under de senaste 24 timmarna: %(count)s. 🚀 Om du vill ha snabbare nedladdningar och slippa webbläsarkontroll, <a %(a_membership)s>bli medlem</a>. Ladda ner från partnerwebbplats Fortsätt gärna att bläddra i Annas Arkiv i en annan flik medan du väntar (om din webbläsare stöder uppdatering av bakgrundsflikar). Vänta gärna på att flera nedladdningssidor laddas samtidigt (men ladda bara ner en fil åt gången per server). När du får en nedladdningslänk är den giltig i flera timmar. Tack för att du väntar, detta håller webbplatsen tillgänglig gratis för alla! 😊 🔗 Alla nedladdningslänkar för denna fil: <a %(a_main)s>Filens huvudsida</a>. ❌ Långsamma nedladdningar är inte tillgängliga via Cloudflare VPN eller från Cloudflare IP-adresser. ❌ Långsamma nedladdningar är enbart tillgängliga genom den officiella hemsidan. Besök %(websites)s. 📚 Använd denna URL för att ladda ner: <a %(a_download)s>Ladda ner nu</a>. För att ge alla en möjlighet att ladda ner filer gratis, måste du vänta innan du kan ladda ner denna fil. Vänligen vänta <span %(span_countdown)s>%(wait_seconds)s</span> sekunder för att ladda ner denna fil. Varning: det har gjorts många nedladdningar från din IP-adress det senaste dygnet. Det kan därför gå långsammare än vanligt. Om du använder en VPN, delad internetanslutning eller din ISP delar IP-adresser, kan denna varning bero på det. Spara ❌ Något gick fel. Försök igen. ✅ Sparad. Vänligen ladda om sidan. Ändra ditt visningsnamn. Din identifierare (delen efter ”#”) kan inte ändras. Profil skapad <span %(span_time)s>%(time)s</span> redigera Listor Skapa en ny lista genom att hitta en fil och öppna fliken "Listor". Inga listor ännu Profilen hittades inte. Profil För närvarande kan vi inte tillgodose bokförfrågningar. Skicka inte bokförfrågningar via e-post till oss. Vänligen gör dina förfrågningar på Z-Library eller Libgen-forum. Post i Annas Arkiv DOI: %(doi)s Nerladdning SciDB Nexus/STC Ingen förhandsvisning tillgänglig ännu. Ladda ner filen från <a %(a_path)s>Annas Arkiv</a>. För att stödja tillgängligheten och långsiktig bevarande av mänsklig kunskap, bli en <a %(a_donate)s>medlem</a>. Som en bonus, 🧬&nbsp;SciDB laddar snabbare för medlemmar, utan några begränsningar. Funkar det inte? Testa att <a %(a_refresh)s>ladda om sidan</a>. Sci-Hub Lägg till specifikt sökfält Sökbeskrivningar och metadata-kommentarer Utgivningsår Avancerad Tillgång Innehåll Visa Lista Tabell Filtyp Språk Sortera efter Största Mest relevanta Nyaste (filstorlek) (open sourced) (publiceringsår) Äldst Slumpmässig Minsta Källa skrapad och öppen källkod av AA Digital utlåning (%(count)s) Tidskriftsartiklar (%(count)s) Vi har hittat matchningar i: %(in)s. Du kan hänvisa till URL:en som finns där när du <a %(a_request)s>begär en fil</a>. Metadata (%(count)s) För att utforska sökindexet med koder, använd <a %(a_href)s>Codes Explorer</a>. Sökindexet uppdateras varje månad. Den innehåller för närvarande poster upp till %(last_data_refresh_date)s. Mer tekniska detaljer finns på %(link_open_tag)sdatauppsättningar</a>. Exkludera Inkludera endast Omarkerad mer… Nästa … Föregående Denna sökindex inkluderar för närvarande metadata från Internet Archive’s Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Mer om våra datasets</a>. För fler digitala lånebibliotek, se <a %(a_wikipedia)s>Wikipedia</a> och <a %(a_mobileread)s>MobileRead Wiki</a>. För DMCA / upphovsrättsanspråk <a %(a_copyright)s>klicka här</a>. Tidsåtgång för nerladdning Fel under sökning. Försök <a %(a_reload)s>ladda om sidan</a>. Om problemet kvarstår, vänligen mejla oss på %(email)s. Snabb nerladdning Faktum är att vem som helst kan hjälpa till att bevara dessa filer genom att seeda vår <a %(a_torrents)s>enhetliga lista över torrents</a>. ➡️ Ibland händer detta felaktigt när sökservern är långsam. I sådana fall kan <a %(a_attrs)s>omladdning</a> hjälpa. ❌ Den här filen kan ha problem. Letar du efter artiklar? Denna sökindex inkluderar för närvarande metadata från olika metadatakällor. <a %(a_datasets)s>Mer om våra datasets</a>. Det finns många, många källor till metadata för skrivna verk runt om i världen. <a %(a_wikipedia)s>Denna Wikipedia-sida</a> är en bra start, men om du känner till andra bra listor, vänligen meddela oss. För metadata visar vi de ursprungliga posterna. Vi gör ingen sammanslagning av poster. Vi har för närvarande världens mest omfattande öppna katalog av böcker, artiklar och andra skriftliga verk. Vi speglar Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>och mer</a>. <span class="font-bold">Inga filer hittades.</span> Prova med färre eller olika söktermer och filter. Resultat %(from)s-%(to)s (%(total)s totalt) Om du hittar andra “skuggbibliotek” som vi borde spegla, eller om du har frågor, kontakta oss på %(email)s. %(num)d delvisa träffar %(num)d+ delvisa matchningar Skriv i rutan för att söka efter filer i digitala lånebibliotek. Skriv i rutan för att söka i vår katalog med %(count)s direkt nedladdningsbara filer, som vi <a %(a_preserve)s>bevarar för alltid</a>. Skriv i sökrutan för att söka. Skriv i rutan för att söka i vår katalog av %(count)s akademiska uppsatser och vetenskapliga artiklar, som vi <a %(a_preserve)s>bevarar för alltid</a>. Skriv i rutan för att söka efter metadata från bibliotek. Detta kan vara användbart när du <a %(a_request)s>begär en fil</a>. Tips: använd kortkommandon “/” (sökfokus), “enter” (sök), “j” (upp), “k” (ner), “<” (föregående sida), “>” (nästa sida) för snabbare navigering. Detta är metadataregister, <span %(classname)s>inte</span> nedladdningsbara filer. Sökinställningar Sök Digitala lån Nerladdning Tidskriftsartiklar Metadata Ny sökning %(search_input)s - Sök Sökningen tog för lång tid, vilket innebär att du kan se felaktiga resultat. Ibland kan <a %(a_reload)s>omladdning</a> av sidan hjälpa. Sökningen tog för lång tid, vilket är vanligt för breda sökningar. Filterräkningarna kan vara felaktiga. För stora uppladdningar (över 10 000 filer) som inte accepteras av Libgen eller Z-Library, vänligen kontakta oss på %(a_email)s. För Libgen.li, se till att först logga in på <a %(a_forum)s >deras forum</a> med användarnamn %(username)s och lösenord %(password)s, och återvänd sedan till deras <a %(a_upload_page)s >uppladdningssida</a>. För tillfället föreslår vi att ladda upp nya böcker till Library Genesis forks. Här är en <a %(a_guide)s>praktisk guide</a>. Observera att båda forks som vi indexerar på denna webbplats hämtar från detta samma uppladdningssystem. För små uppladdningar (upp till 10 000 filer) vänligen ladda upp dem till både %(first)s och %(second)s. Alternativt kan du ladda upp dem till Z-Library <a %(a_upload)s>här</a>. För att ladda upp akademiska artiklar, vänligen ladda upp dem till <a %(a_stc_nexus)s>STC Nexus</a> (förutom till Library Genesis). De är det bästa skuggarkivet för nya artiklar. Vi har inte integrerat dem ännu, men vi kommer att göra det vid något tillfälle. Du kan använda deras <a %(a_telegram)s>uppladdningsbot på Telegram</a>, eller kontakta adressen som anges i deras fästa meddelande om du har för många filer för att ladda upp på detta sätt. <span %(label)s>Tungt volontärarbete (USD$50-USD$5,000 belöningar):</span> om du kan ägna mycket tid och/eller resurser till vårt uppdrag, skulle vi gärna arbeta närmare med dig. Så småningom kan du gå med i kärnteamet. Även om vi har en stram budget, kan vi tilldela <span %(bold)s>💰 monetära belöningar</span> för det mest intensiva arbetet. <span %(label)s>Lätt volontärarbete:</span> om du bara kan avvara några timmar här och där, finns det fortfarande många sätt du kan hjälpa till på. Vi belönar konsekventa volontärer med <span %(bold)s>🤝 medlemskap i Annas Arkiv</span>. Annas Arkiv förlitar sig på volontärer som du. Vi välkomnar alla engagemangsnivåer och har två huvudkategorier av hjälp vi söker: Om du inte kan volontärarbeta, kan du ändå hjälpa oss mycket genom att <a %(a_donate)s>donera pengar</a>, <a %(a_torrents)s>seeda våra torrents</a>, <a %(a_uploading)s>ladda upp böcker</a> eller <a %(a_help)s>berätta för dina vänner om Annas Arkiv</a>. <span %(bold)s>Företag:</span> vi erbjuder höghastighetsdirektåtkomst till våra samlingar i utbyte mot donationer på företagsnivå eller utbyte mot nya samlingar (t.ex. nya skanningar, OCR:ade dataset, berikning av våra data). <a %(a_contact)s>Kontakta oss</a> om detta är något för dig. Se även vår <a %(a_llm)s>LLM-sida</a>. Belöningar Vi letar alltid efter personer med gedigna programmerings- eller offensiva säkerhetskunskaper som vill engagera sig. Du kan göra en betydande insats för att bevara mänsklighetens arv. Som tack ger vi bort medlemskap för gedigna bidrag. Som ett stort tack ger vi bort monetära belöningar för särskilt viktiga och svåra uppgifter. Detta bör inte ses som en ersättning för ett jobb, men det är en extra incitament och kan hjälpa till med uppkomna kostnader. De flesta av våra koder är öppen källkod, och vi kommer att be om detsamma av din kod när vi tilldelar belöningen. Det finns vissa undantag som vi kan diskutera individuellt. Belöningar tilldelas den första personen som slutför en uppgift. Kommentera gärna på en belöningsbiljett för att låta andra veta att du arbetar med något, så att andra kan hålla sig tillbaka eller kontakta dig för att samarbeta. Men var medveten om att andra fortfarande är fria att arbeta på det och försöka slå dig. Vi tilldelar dock inte belöningar för slarvigt arbete. Om två högkvalitativa inlämningar görs nära varandra (inom en dag eller två), kan vi välja att tilldela belöningar till båda, efter vårt gottfinnande, till exempel 100%% för den första inlämningen och 50%% för den andra inlämningen (så totalt 150%%). För de större belöningarna (särskilt skrapningsbelöningar), vänligen kontakta oss när du har slutfört ~5%% av det, och du är säker på att din metod kommer att skala till hela milstolpen. Du måste dela din metod med oss så att vi kan ge feedback. På detta sätt kan vi också bestämma vad vi ska göra om det finns flera personer som närmar sig en belöning, såsom att potentiellt tilldela den till flera personer, uppmuntra människor att samarbeta, etc. VARNING: de högbelönade uppgifterna är <span %(bold)s>svåra</span> — det kan vara klokt att börja med enklare. Gå till vår <a %(a_gitlab)s>Gitlab-ärendelista</a> och sortera efter ”Etikettprioritet”. Detta visar ungefär i vilken ordning vi bryr oss om uppgifterna. Uppgifter utan explicita belöningar är fortfarande berättigade till medlemskap, särskilt de märkta ”Accepterad” och ”Annas favorit”. Du kanske vill börja med ett ”Startprojekt”. Lätt volontärarbete Vi har nu också en synkroniserad Matrix-kanal på %(matrix)s. Om du har några timmar över kan du hjälpa till på flera sätt. Se till att gå med i <a %(a_telegram)s>volontärchatten på Telegram</a>. Som ett tecken på uppskattning ger vi vanligtvis ut 6 månader av “Lycklig Bibliotekarie” för grundläggande milstolpar, och mer för fortsatt volontärarbete. Alla milstolpar kräver högkvalitativt arbete — slarvigt arbete skadar oss mer än det hjälper och vi kommer att avvisa det. Vänligen <a %(a_contact)s>mejla oss</a> när du når en milstolpe. %(links)s länkar eller skärmdumpar av förfrågningar som du har fixat. Uppfylla bok- (eller uppsatser, etc.) förfrågningar på Z-Librarys forum eller Library Genesis forum. Vi har inte vårt eget bokförfrågningssystem, men vi speglar dessa bibliotek, så att göra dem bättre gör även Annas Arkiv bättre. Milstolpe Uppgift Beror på uppgiften. Små uppgifter som postas i vår <a %(a_telegram)s>volontärchatt på Telegram</a>. Vanligtvis för medlemskap, ibland för små belöningar. Små uppgifter publicerade i vår volontärchattgrupp. Se till att lämna en kommentar på problem du löser, så att andra inte duplicerar ditt arbete. %(links)s länkar till poster du förbättrade. Du kan använda <a %(a_list)s>listan över slumpmässiga metadatafrågor</a> som en startpunkt. Förbättra metadata genom att <a %(a_metadata)s>länka</a> med Open Library. Dessa bör visa att du berättar för någon om Annas Arkiv, och att de tackar dig. %(links)s länkar eller skärmdumpar. Sprida ordet om Annas Arkiv. Till exempel genom att rekommendera böcker på AA, länka till våra blogginlägg eller allmänt hänvisa folk till vår webbplats. Fullständigt översätta ett språk (om det inte redan var nära att bli klart). <a %(a_translate)s>Översätta</a> webbplatsen. Länk till redigeringshistorik som visar att du gjort betydande bidrag. Förbättra Wikipedia-sidan för Annas Arkiv på ditt språk. Inkludera information från AA:s Wikipedia-sida på andra språk, och från vår webbplats och blogg. Lägg till referenser till AA på andra relevanta sidor. Volontärarbete & Belöningar 