msgid "layout.index.invalid_request"
msgstr "Ogiltig begäran. Besök %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archives lånebibliotek"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " och "

msgid "layout.index.header.tagline_and_more"
msgstr "och mer"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Vi speglar %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Vi skrapar och open-source %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "All vår kod och data bygger helt på öppen källkod."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Det största verkligt öppna biblioteket i mänsklighetens historia."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;böcker, %(paper_count)s&nbsp;uppsatser — bevarade för alltid."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Världens största bibliotek med öppen källkod och öppna data. ⭐️&nbsp;Speglar Sci-Hub, Library Genesis, Z-Library och mer. 📈&nbsp;%(book_any)s böcker, %(journal_article)s artiklar, %(book_comic)s serier, %(magazine)s tidskrifter — bevarade för alltid."

msgid "layout.index.header.tagline_short"
msgstr "📚Världens största bibliotek med öppen källkod och öppen data. <br> ⭐️ Speglar Scihub, Libgen, Zlib, och fler."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Inkorrekt metadata (till exempel titel, beskrivning, bild)"

msgid "common.md5_report_type_mapping.download"
msgstr "Nerladdningsproblem (t.ex. kan inte koppla upp, felmeddelande, väldigt långsam)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Filen kan inte öppnas (t.ex. korrupt fil, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Dålig kvalitet (t.ex. formateringsproblem, låg inscanningskvalitet, sidor saknas)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / filen borde tas bort (t.ex. reklam, stötande innehåll)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Upphovsrättsanspråk"

msgid "common.md5_report_type_mapping.other"
msgstr "Annat"

msgid "common.membership.tier_name.bonus"
msgstr "Bonusnedladdningar"

msgid "common.membership.tier_name.2"
msgstr "Bedårande Bokmal"

msgid "common.membership.tier_name.3"
msgstr "Lycklig Litteraturälskare"

msgid "common.membership.tier_name.4"
msgstr "Duktig Datahamstrare"

msgid "common.membership.tier_name.5"
msgstr "Allenastående Arkivarie"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s totalt"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totalt"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "obetald"

msgid "common.donation.order_processing_status_labels.1"
msgstr "betald"

msgid "common.donation.order_processing_status_labels.2"
msgstr "avbruten"

msgid "common.donation.order_processing_status_labels.3"
msgstr "utgången"

msgid "common.donation.order_processing_status_labels.4"
msgstr "väntar på att Anna ska bekräfta"

msgid "common.donation.order_processing_status_labels.5"
msgstr "ogiltig"

msgid "page.donate.title"
msgstr "Donera"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Du har en <a %(a_donation)s>nuvarande donation</a> under behandling. Var god färdigställ eller avbryt den donationen innan du skapar en ny donation."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Visa alla mina donationer</a>"

msgid "page.donate.header.text1"
msgstr "Annas arkiv är ett ideellt projekt som är open-source och använder öppen data. Genom att donera och bli medlem, stödjer du vår verksamhet och utveckling. Till alla våra medlemmar: tack för att ni håller oss igång! ❤️"

msgid "page.donate.header.text2"
msgstr "För mer information, kolla in <a %(a_donate)s>Donations-FAQ</a>."

msgid "page.donate.refer.text1"
msgstr "För att få ännu fler nedladdningar, <a %(a_refer)s>bjud in dina vänner</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Du får %(percentage)s%% bonus snabba nedladdningar, eftersom du blev hänvisad av användare %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Detta gäller för hela medlemsperioden."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s snabba nerladdningar per dag"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "om du donerar den här månaden!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / månad"

msgid "page.donate.buttons.join"
msgstr "Kom med"

msgid "page.donate.buttons.selected"
msgstr "Vald"

msgid "page.donate.buttons.up_to_discounts"
msgstr "upp till %(percentage)s%% rabatt"

msgid "page.donate.perks.scidb"
msgstr "SciDB-uppsatser <strong>obegränsat</strong> utan verifikation"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a>-åtkomst"

msgid "page.donate.perks.refer"
msgstr "Få <strong>%(percentage)s%% bonusnedladdningar</strong> genom att <a %(a_refer)s>bjuda in vänner</a>."

msgid "page.donate.perks.credits"
msgstr "Ditt användarnamn eller anonyma omnämnande på tacksidan"

msgid "page.donate.perks.previous_plus"
msgstr "Tidigare fördelar, plus:"

msgid "page.donate.perks.early_access"
msgstr "Förhandstillgång till nya funktioner"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Exklusiv Telegram med uppdateringar från bakom kulisserna"

msgid "page.donate.perks.adopt"
msgstr "\"Adoptera en torrent\": ditt användarnamn eller meddelande i ett torrentfilnamn <div %(div_months)s> en gång per 12 månaders medlemskap</div>"

msgid "page.donate.perks.legendary"
msgstr "Legendarisk status i bevarandet av mänsklighetens kunskap och kultur"

msgid "page.donate.expert.title"
msgstr "Experttillgång"

msgid "page.donate.expert.contact_us"
msgstr "kontakta oss"

msgid "page.donate.small_team"
msgstr "Vi är ett litet gäng som jobbar ideellt. Det kan ta 1-2 veckor för oss att svara."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Obegränsad</strong> snabb tillgång"

msgid "page.donate.expert.direct_sftp"
msgstr "Direkta <strong>SFTP</strong> servrar"

msgid "page.donate.expert.enterprise_donation"
msgstr "Donationer på företagsnivå eller utbyte för nya samlingar (t.ex. nya inskanningar, OCR:ade datasamlingar)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Vi välkomnar stora donationer från förmögna individer eller institutioner. "

msgid "page.donate.header.large_donations"
msgstr "För donationer över 5000 dollar var god kontakta oss direkt på %(email)s."

msgid "page.donate.header.recurring"
msgstr "Var medveten om att även om medlemskapen på denna sida är \"per månad\", är de engångsdonationer (icke-återkommande). Se <a %(faq)s>Donations-FAQ</a>."

msgid "page.donate.without_membership"
msgstr "Om du vill göra en donation (valfritt belopp) utan medlemskap, använd gärna denna Monero (XMR)-adress: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Vänligen välj en betalningsmetod."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(just nu otillgänglig)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s presentkort"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkort (via app)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Krypto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredit/debitkort"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (USA) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (vanlig)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Kort / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit/debet/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasilien)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkort"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredit/debitkort (backup)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredit/debitkort 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Med krypto kan du donera genom att använda BTC, ETH, XMR, och SOL. Använd detta alternativ om du är van vid kryptovalutor."

msgid "page.donate.payment.desc.crypto2"
msgstr "Med krypto kan du donera genom att använda BTC, ETH, XMR med flera."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Om du använder krypto för första gången, föreslår vi att du använder %(options)s för att köpa och donera Bitcoin (den första och mest använda kryptovalutan)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "För att donera med hjölp av PayPal US, so kommer vi använda PayPal Krypto, vilket möjliggör anonymitet. Vi uppskattar att du tar tid på dig att lära dig denna donationsmetod, eftersom den hjälper oss mycket."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Donera med hjälp av PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Donera med hjälp av Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Om du har Cash App, så är detta det enklaste sättet att donera!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Lägg märke till att för transaktioner under %(amount)s kan CashApp debitera en %(fee)s avgift. För %(amount)s eller högre är det gratis!"

msgid "page.donate.payment.desc.revolut"
msgstr "Donera med Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Om du har Revolut är detta det enklaste sättet att donera!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Donera med ett kredit- eller debitkort."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay och Apple Pay kan också funka."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Observera att för små donationer så kan kreditkortavgifterna äta upp vår %(discount)s%% rabatt, så vi rekommenderar längre prenumerationer."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Observera att avgifterna är höga för små donationer, så vi rekommenderar längre prenumerationer."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Med Binance köper du Bitcoin med ett kredit-/betalkort eller bankkonto och donerar sedan den bitcoinen till oss. På så sätt kan vi förbli säkra och anonyma när vi tar emot din donation."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance är tillgängligt i nästan alla länder och fungerar med de flesta banker och kredit-/betalkort. Detta är för närvarande vår huvudsakliga rekommendation. Vi uppskattar att du tar dig tid att lära dig hur du donerar med denna metod, eftersom det hjälper oss mycket."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Donera med ditt vanliga PayPal-konto."

msgid "page.donate.payment.desc.givebutter"
msgstr "Donera med kredit-/betalkort, PayPal eller Venmo. Du kan välja mellan dessa på nästa sida."

msgid "page.donate.payment.desc.amazon"
msgstr "Donera med hjälp av ett presentkort från Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Observera att vi måste runda upp till summor som godkänns av våra återförsäljares (minimum %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>VIKTIGT:</strong> Vi stödjer bara Amazon.com, inte andra amazonwebbsidor. Exempelvis, .se, .de, co.uk fungerar INTE."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>VIKTIGT:</strong> Detta alternativ är för %(amazon)s. Om du vill använda en annan Amazon-webbplats, välj den ovan."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Denna metod använder en kryptovalutaleverantör som en mellanliggande konvertering. Detta kan vara lite förvirrande, så använd endast denna metod om andra betalningsmetoder inte fungerar. Det fungerar inte heller i alla länder."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donera med ett kredit-/betalkort, via Alipay-appen (superlätt att konfigurera)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installera Alipay-appen"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installera Alipay-appen från <a %(a_app_store)s>Apple App Store</a> eller <a %(a_play_store)s>Google Play Store</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrera dig med ditt telefonnummer."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Inga ytterligare personuppgifter krävs."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Lägg till bankkort"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Stöds: Visa, MasterCard, JCB, Diners Club och Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Se <a %(a_alipay)s>denna guide</a> för mer information."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Vi kan inte stödja direktbetalningar med kredit-/debetkort, eftersom bankerna inte vill samarbeta med oss. ☹ Det finns dock flera sätt att använda kredit-/debetkort ändå, genom andra betalningsmetoder:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon presentkort"

msgid "page.donate.ccexp.amazon_com"
msgstr "Skicka oss presentkort från Amazon.com med ditt kredit-/betalkort."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay stödjer internationella kredit-/debetkort. Se <a %(a_alipay)s>denna guide</a> för mer information."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) stöder internationella kredit-/betalkort. I WeChat-appen, gå till ”Jag →Tjänster → Plånbok → Lägg till ett kort”. Om du inte ser det alternativet, aktivera det genom att gå till ”Jag → Inställningar → Allmänt → Verktyg → Weixin Pay → Aktivera”."

msgid "page.donate.ccexp.crypto"
msgstr "Du kan köpa krypto med kredit-/betalkort."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Krypto express-tjänster"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Express-tjänster är bekväma, men tar ut högre avgifter."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Du kan använda detta istället för en krypto-börs om du vill göra en större donation snabbt och inte har något emot en avgift på 5-10 dollar."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Se till att skicka exakt det krypto-belopp som visas på donationssidan, inte beloppet i amerikanska dollar."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Annars kommer avgiften att dras utan att vi kan behandla ditt medlemskap automatiskt."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s beroende på land, ingen verifiering för första transaktionen)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, ingen verifiering för första transaktionen)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Om någon del av denna information är inaktuell, vänligen mejla oss och säg till."

msgid "page.donate.payment.desc.bmc"
msgstr "För kreditkort, betalkort, Apple Pay och Google Pay använder vi “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). I deras system är en “kaffe” lika med $5, så din donation kommer att avrundas till närmaste multipel av 5."

msgid "page.donate.duration.intro"
msgstr "Välj hur länge din prenumeration ska gälla."

msgid "page.donate.duration.1_mo"
msgstr "1 månad"

msgid "page.donate.duration.3_mo"
msgstr "3 månader"

msgid "page.donate.duration.6_mo"
msgstr "6 månader"

msgid "page.donate.duration.12_mo"
msgstr "12 månader"

msgid "page.donate.duration.24_mo"
msgstr "24 månader"

msgid "page.donate.duration.48_mo"
msgstr "48 månader"

msgid "page.donate.duration.96_mo"
msgstr "96 månader"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>efter <span %(span_discount)s></span> rabatter</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Denna betalningsmetod kräver minst %(amount)s. Vänligen välj en annan längd eller betalningsmetod."

msgid "page.donate.buttons.donate"
msgstr "Donera"

msgid "page.donate.payment.maximum_method"
msgstr "Denna betalningsmetod tillåter endast ett maximum av %(amount)s. Vänligen välj en annan längd eller betalningsmetod."

msgid "page.donate.login2"
msgstr "För att bli medlem, var god <a %(a_login)s>logga in eller registrera dig</a>. Tack för ditt stöd!"

msgid "page.donate.payment.crypto_select"
msgstr "Välj den kryptovaluta du föredrar:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(lägsta minimibelopp)"

msgid "page.donate.coinbase_eth"
msgstr "(används när du skickar Ethereum från Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(varning: högt minimibelopp)"

msgid "page.donate.submit.confirm"
msgstr "Klicka på donationsknappen för att godkänna donationen."

msgid "page.donate.submit.button"
msgstr "Donera <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Du kan fortfarande avbryta donationen under checkout."

msgid "page.donate.submit.success"
msgstr "✅ Omdirigerar till donationssidan…"

msgid "page.donate.submit.failure"
msgstr "❌ Något gick fel. Var god ladda om sidan och försök igen."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / månad"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "i 1 månad"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "i 3 månader"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "i 6 månader"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "i 12 månader"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "i 24 månader"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "i 48 månader"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "i 96 månader"

msgid "page.donate.submit.button.label.1_mo"
msgstr "i 1 månad \"%(tier_name)s\""

msgid "page.donate.submit.button.label.3_mo"
msgstr "i 3 månader \"%(tier_name)s\""

msgid "page.donate.submit.button.label.6_mo"
msgstr "i 6 månader \"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "i 12 månader \"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "i 24 månader \"%(tier_name)s\""

msgid "page.donate.submit.button.label.48_mo"
msgstr "i 48 månader “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "i 96 månader “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Donation"

msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad i %(duration)s månader, inklusive %(discounts)s%% rabatt)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Totalt: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / månad i %(duration)s månader)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Identifierare: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Avbryt"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Är du säker på att du vill avbryta? Avbryt inte om du redan betalat."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, avbryt"

msgid "page.donation.header.cancel.success"
msgstr "✅ Din donation har avbrutits."

msgid "page.donation.header.cancel.new_donation"
msgstr "Gör en ny donation"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Något gick fel. Var god ladda om sidan och försök igen."

msgid "page.donation.header.reorder"
msgstr "Beställ igen"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Du har redan betalat. Vill du se betalningsinstruktionerna iallafall, klicka här:"

msgid "page.donation.old_instructions.show_button"
msgstr "Visa gamla betalningsinstruktioner"

msgid "page.donation.thank_you_donation"
msgstr "Tack för din donation!"

msgid "page.donation.thank_you.secret_key"
msgstr "Om du inte redan har gjort det, skriv ner din hemliga nyckel för inloggning:"

msgid "page.donation.thank_you.locked_out"
msgstr "Annars kan du bli utelåst från detta konto!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Betalningsinstruktionerna har nu gått ut. Om du vill göra en ny donation, använd knappen \"Beställ igen\" ovan."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Viktigt meddelande:</strong> Kryptopriser kan fluktuera kraftigt, ibland så mycket som 20%% på några minuter. Detta är fortfarande mindre än de avgifter vi ådrar oss hos betalningsleverantörer, som ofta tar 50-60%% för att arbeta med en \"ideell skuggförening\" som oss. <u>Om du skickar oss kvittot med det ursprungliga beloppet du betalade, kommer vi ändå att kreditera ditt konto för det valda medlemskapet</u> (så länge kvittot inte är äldre än några timmar). Vi uppskattar verkligen att du är villig att stå ut med sånt här för att stödja oss! ❤️"

msgid "page.donation.expired"
msgstr "Denna donation har gått ut. Vänligen avbryt och skapa en ny."

msgid "page.donation.payment.crypto.top_header"
msgstr "Kryptoinstruktioner"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Överför till en utav våra krypto-konton"

msgid "page.donation.payment.crypto.text1"
msgstr "Donera hela beloppet av %(total)s till en av dessa adresser:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Köp Bitcoin på Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Hitta “Krypto”-sidan i din PayPal-app eller på webbplatsen. Detta finns vanligtvis under “Ekonomi”."

msgid "page.donation.payment.paypal.text3"
msgstr "Följ instruktionerna för att köpa Bitcoin (BTC). Du behöver bara köpa det belopp du vill donera, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Överför Bitcoinen till vår adress"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gå till \"Bitcoin\"-sidan i din PayPal app eller på hemsidan. Klicka på knappen \"Överför\"%(transfer_icon)s, och sedan på \"Skicka\"."

msgid "page.donation.payment.paypal.text5"
msgstr "Ange vår Bitcoin (BTC)-adress som mottagare och följ instruktionerna för att skicka din donation på %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Instruktioner för kredit-/betalkort"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Donera via vår kredit- / betalkortsida"

msgid "page.donation.donate_on_this_page"
msgstr "Donera %(amount)s på <a %(a_page)s>denna sida</a>."

msgid "page.donation.stepbystep_below"
msgstr "Se steg-för-steg-guiden nedan."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Väntar på bekräftelse (uppdatera sidan för att kontrollera)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Väntar på överföring (uppdatera sidan för att kontrollera)…"

msgid "page.donation.time_left_header"
msgstr "Tid kvar:"

msgid "page.donation.might_want_to_cancel"
msgstr "(du kanske vill avbryta och skapa en ny donation)"

msgid "page.donation.reset_timer"
msgstr "För att återställa timern, skapa helt enkelt en ny donation."

msgid "page.donation.refresh_status"
msgstr "Uppdatera status"

msgid "page.donation.footer.issues_contact"
msgstr "Om du stöter på några problem, vänligen kontakta oss på %(email)s och skicka med så mycket information som möjligt (såsom skärmdumpar)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Om du redan har betalat:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Ibland kan bekräftelsen ta upp till 24 timmar, så se till att uppdatera den här sidan (även om den har gått ut)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Köp PYUSD-mynt via PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Följ instruktionerna för att köpa PYUSD-mynt (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Köp lite mer (vi rekommenderar %(more)s mer) än beloppet du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Gå till “PYUSD”-sidan i din PayPal-app eller på webbplatsen. Tryck på “Överför”-knappen %(icon)s, och sedan “Skicka”."

msgid "page.donation.transfer_amount_to"
msgstr "Överför %(amount)s till %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Köp Bitcoin (BTC) på Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gå till sidan ”Bitcoin” (BTC) i Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Köp lite mer (vi rekommenderar %(more)s mer) än det belopp du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över."

msgid "page.donation.cash_app_btc.step2"
msgstr "Överför Bitcoinen till vår adress"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klicka på knappen ”Skicka bitcoin” för att göra ett ”uttag”. Byt från dollar till BTC genom att trycka på %(icon)s-ikonen. Ange BTC-beloppet nedan och klicka på ”Skicka”. Se <a %(help_video)s>denna video</a> om du fastnar."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "För små donationer (under 25 dollar) kan du behöva använda Rush eller Priority."

msgid "page.donation.revolut.step1"
msgstr "Köp Bitcoin (BTC) på Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Gå till sidan ”Crypto” i Revolut för att köpa Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Köp lite mer (vi rekommenderar %(more)s mer) än det belopp du donerar (%(amount)s), för att täcka transaktionsavgifter. Du behåller det som blir över."

msgid "page.donation.revolut.step2"
msgstr "Överför bitcoinen till vår adress"

msgid "page.donation.revolut.step2.transfer"
msgstr "Klicka på knappen ”Skicka bitcoin” för att göra ett ”uttag”. Byt från euro till BTC genom att trycka på %(icon)s-ikonen. Ange BTC-beloppet nedan och klicka på ”Skicka”. Se <a %(help_video)s>denna video</a> om du fastnar."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Var noga med att använda BTC-beloppet nedan, <em>INTE</em> euro eller dollar, annars får vi inte rätt belopp och kan inte automatiskt bekräfta ditt medlemskap."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "För små donationer (under 25 dollar) kan du behöva använda Rush eller Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Använd någon av följande expresstjänster från kreditkort till Bitcoin, som bara tar några minuter:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Fyll i följande uppgifter i formuläret:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin-belopp:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Vänligen använd detta <span %(underline)s>exakta belopp</span>. Din totala kostnad kan bli högre på grund av kreditkortsavgifter. För små donationer kan detta tyvärr vara mer än vår rabatt."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin-adress (extern plånbok):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instruktioner"

msgid "page.donation.crypto_standard"
msgstr "Vi stöder endast standardversionen av kryptovalutor, inga exotiska nätverk eller versioner av mynt. Det kan ta upp till en timme att bekräfta transaktionen, beroende på myntet."

msgid "page.donation.crypto_qr_code_title"
msgstr "Skanna QR -kod för att betala"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Skanna den här QR -koden med din Crypto Wallet -app för att snabbt fylla i betalningsinformationen"

msgid "page.donation.amazon.header"
msgstr "Amazon-presentkort"

msgid "page.donation.amazon.form_instructions"
msgstr "Vänligen använd <a %(a_form)s>officiella Amazon.com-formuläret</a> för att skicka oss ett presentkort på %(amount)s till e-postadressen nedan."

msgid "page.donation.amazon.only_official"
msgstr "Vi kan inte acceptera andra metoder för presentkort, <strong>endast skickade direkt från det officiella formuläret på Amazon.com</strong>. Vi kan inte returnera ditt presentkort om du inte använder detta formulär."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Ange exakt belopp: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Vänligen skriv INTE ditt eget meddelande."

msgid "page.donation.amazon.form_to"
msgstr "“Mottagarens” e-post i formuläret:"

msgid "page.donation.amazon.unique"
msgstr "Unikt för ditt konto, dela inte."

msgid "page.donation.amazon.only_use_once"
msgstr "Använd endast en gång."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Väntar på presentkort… (uppdatera sidan för att kontrollera)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Efter att du skickat ditt presentkort kommer vårt automatiserade system att bekräfta det inom några minuter. Om detta inte fungerar, försök att skicka ditt presentkort igen (<a %(a_instr)s>instruktioner</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Om det fortfarande inte fungerar, vänligen mejla oss så kommer Anna att manuellt granska det (det kan ta några dagar), och var noga med att nämna om du redan har försökt skicka om."

msgid "page.donation.amazon.example"
msgstr "Exempel:"

msgid "page.donate.strange_account"
msgstr "Observera att kontots namn eller bild kan se konstigt ut. Du behöver inte oroa dig! Dessa konton hanteras av våra donationspartners. Våra konton har inte blivit hackade."

msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay-instruktioner"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donera via Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Donera totalbeloppet av %(total)s genom <a %(a_account)s>detta Alipay-konto</a>"

msgid "page.donation.page_blocked"
msgstr "Om donationssidan blockeras, prova en annan internetanslutning (t.ex. VPN eller mobilt internet)."

msgid "page.donation.payment.alipay.error"
msgstr "Tyvärr är Alipay-sidan ofta endast tillgänglig från <strong>Kina</strong>. Du kan behöva tillfälligt inaktivera din VPN, eller använda en VPN till Kina (eller Hongkong fungerar ibland också)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Donera (skanna QR-koden eller tryck på knappen)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Öppna <a %(a_href)s>QR-kod donationssidan</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Skanna QR-koden med Alipay-appen, eller tryck på knappen för att öppna Alipay-appen."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Ha tålamod; sidan kan ta ett tag att ladda eftersom den är i Kina."

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat-instruktioner"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donera på WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Donera totalbeloppet av %(total)s med <a %(a_account)s>detta WeChat-konto</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix-instruktioner"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donera på Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Donera hela beloppet av %(total)s genom <a %(a_account)s>detta Pix-konto"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Maila oss kvittot"

msgid "page.donation.footer.verification"
msgstr "Skicka ett kvitto eller en skärmdump till din personliga verifieringsadress. Använd INTE denna e-postadress för din PayPal-donation."

msgid "page.donation.footer.text1"
msgstr "Skicka ett kvitto eller en skärmdump till din personliga verifieringsadress:"

msgid "page.donation.footer.crypto_note"
msgstr "Om växelkursen för kryptovalutan fluktuerade under transaktionen, se till att inkludera kvittot som visar den ursprungliga växelkursen. Vi uppskattar verkligen att du tar dig besväret att använda krypto, det hjälper oss mycket!"

msgid "page.donation.footer.text2"
msgstr "När du har skickat ditt kvitto via e-post, klicka på denna knapp, så att Anna kan granska det manuellt (detta kan ta några dagar):"

msgid "page.donation.footer.button"
msgstr "Ja, jag har mejlat mitt kvitto"

msgid "page.donation.footer.success"
msgstr "✅ Tack för din donation! Anna kommer att aktivera ditt medlemskap manuellt inom några dagar."

msgid "page.donation.footer.failure"
msgstr "❌ Något gick fel. Vänligen ladda om sidan och försök igen."

msgid "page.donation.stepbystep"
msgstr "Steg-för-steg-guide"

msgid "page.donation.crypto_dont_worry"
msgstr "Några av stegen nämner kryptoplånböcker, men oroa dig inte, du behöver inte lära dig något om krypto för detta."

msgid "page.donation.hoodpay.step1"
msgstr "1. Ange din e-postadress."

msgid "page.donation.hoodpay.step2"
msgstr "2. Välj din betalningsmetod."

msgid "page.donation.hoodpay.step3"
msgstr "3. Välj din betalningsmetod igen."

msgid "page.donation.hoodpay.step4"
msgstr "4. Välj “Självhostad” plånbok."

msgid "page.donation.hoodpay.step5"
msgstr "5. Klicka på “Jag bekräftar ägarskap”."

msgid "page.donation.hoodpay.step6"
msgstr "Du bör få ett e-postkvitto. Vänligen skicka det till oss, så bekräftar vi din donation så snart som möjligt."

msgid "page.donate.wait_new"
msgstr "Vänligen vänta minst <span %(span_hours)s>24 timmar</span> (och uppdatera denna sida) innan du kontaktar oss."

msgid "page.donate.mistake"
msgstr "Om du gjorde ett misstag under betalningen kan vi inte göra återbetalningar, men vi kommer att försöka rätta till det."

msgid "page.my_donations.title"
msgstr "Mina donationer"

msgid "page.my_donations.not_shown"
msgstr "Donationsdetaljer visas inte offentligt."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Inga donationer än. <a %(a_donate)s>Gör min första donation.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Gör en ny donation."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Nedladdade filer"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Nedladdningar från Snabba Partner Servrar är markerade med %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Om du laddade ner en fil med både snabba och långsamma nedladdningar, kommer den att visas två gånger."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Snabba nedladdningar under de senaste 24 timmarna räknas mot den dagliga gränsen."

msgid "page.downloaded.times_utc"
msgstr "Alla tider anges i UTC."

msgid "page.downloaded.not_public"
msgstr "Nedladdade filer visas inte offentligt."

msgid "page.downloaded.no_files"
msgstr "Inga filer nedladdade ännu."

msgid "page.downloaded.last_18_hours"
msgstr "Senaste 18 timmarna"

msgid "page.downloaded.earlier"
msgstr "Tidigare"

msgid "page.account.logged_in.title"
msgstr "Konto"

msgid "page.account.logged_out.title"
msgstr "Logga in / Registrera"

msgid "page.account.logged_in.account_id"
msgstr "Kontonummer: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Offentlig profil: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Hemlig nyckel (dela inte!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "visa"

msgid "page.account.logged_in.membership_has_some"
msgstr "Medlemskap: <strong>%(tier_name)s</strong> till %(until_date)s <a %(a_extend)s>(förläng)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Medlemskap: <strong>Inget</strong> <a %(a_become)s>(bli medlem)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Snabba nedladdningar använda (senaste 24 timmarna): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "vilka nedladdningar?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Exklusiv Telegram-grupp: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Gå med oss här!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Uppgradera till en <a %(a_tier)s>högre nivå</a> för att gå med i vår grupp."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontakta Anna på %(email)s om du är intresserad av att uppgradera ditt medlemskap till en högre nivå."

msgid "page.contact.title"
msgstr "E-postkontakt"

msgid "page.account.logged_in.membership_multiple"
msgstr "Du kan kombinera flera medlemskap (snabba nedladdningar per 24 timmar kommer att kombineras)."

msgid "layout.index.header.nav.public_profile"
msgstr "Offentlig profil"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Nerladdade filer"

msgid "layout.index.header.nav.my_donations"
msgstr "Mina donationer"

msgid "page.account.logged_in.logout.button"
msgstr "Logga ut"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Du är nu utloggad. Ladda om sidan för att logga in igen."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Något gick fel. Vänligen ladda om sidan och försök igen."

msgid "page.account.logged_out.registered.text1"
msgstr "Registrering lyckades! Din hemliga nyckel är: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Spara denna nyckel på ett säkert ställe. Om du tappar bort den, förlorar du åtkomsten till ditt konto."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Bokmärke.</strong> Du kan bokmärka denna sida för att hämta din nyckel.</li><li %(li_item)s><strong>Nedladdning.</strong> Klicka <a %(a_download)s>denna länk</a> för att ladda ner din nyckel.</li><li %(li_item)s><strong>Lösenordshanterare.</strong> Använd en lösenordshanterare för att spara nyckeln när du anger den nedan.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Ange din hemliga nyckel för att logga in:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Hemlig nyckel"

msgid "page.account.logged_out.key_form.button"
msgstr "Logga in"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Felaktig hemlig nyckel. Dubbelkolla din nyckel och försök igen, alternativt registrera ett nytt konto nedan."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Tappa inte bort din nyckel!"

msgid "page.account.logged_out.register.header"
msgstr "Har du inget konto ännu?"

msgid "page.account.logged_out.register.button"
msgstr "Registrera nytt konto"

msgid "page.login.lost_key"
msgstr "Om du har tappat din nyckel, vänligen <a %(a_contact)s>kontakta oss</a> och ge så mycket information som möjligt."

msgid "page.login.lost_key_contact"
msgstr "Du kan behöva skapa ett nytt tillfälligt konto för att kontakta oss."

msgid "page.account.logged_out.old_email.button"
msgstr "Gammalt e-postbaserat konto? Ange din <a %(a_open)s>e-post här</a>."

msgid "page.list.title"
msgstr "Lista"

msgid "page.list.header.edit.link"
msgstr "redigera"

msgid "page.list.edit.button"
msgstr "Spara"

msgid "page.list.edit.success"
msgstr "✅ Sparad. Vänligen ladda om sidan."

msgid "page.list.edit.failure"
msgstr "❌ Något gick fel. Försök igen."

msgid "page.list.by_and_date"
msgstr "Lista av %(by)s, skapad <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Listan är tom."

msgid "page.list.new_item"
msgstr "Lägg till eller ta bort från denna lista genom att hitta en fil och öppna fliken ”Listor”."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profilen hittades inte."

msgid "page.profile.header.edit"
msgstr "redigera"

msgid "page.profile.change_display_name.text"
msgstr "Ändra ditt visningsnamn. Din identifierare (delen efter ”#”) kan inte ändras."

msgid "page.profile.change_display_name.button"
msgstr "Spara"

msgid "page.profile.change_display_name.success"
msgstr "✅ Sparad. Vänligen ladda om sidan."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Något gick fel. Försök igen."

msgid "page.profile.created_time"
msgstr "Profil skapad <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Listor"

msgid "page.profile.lists.no_lists"
msgstr "Inga listor ännu"

msgid "page.profile.lists.new_list"
msgstr "Skapa en ny lista genom att hitta en fil och öppna fliken \"Listor\"."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Upphovsrättsreform är nödvändig för nationell säkerhet"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Sammanfattning: Kinesiska LLM:er (inklusive DeepSeek) är tränade på mitt olagliga arkiv av böcker och artiklar — det största i världen. Västvärlden behöver omarbeta upphovsrättslagen som en fråga om nationell säkerhet."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "kompletterande artiklar av TorrentFreak: <a %(torrentfreak)s>första</a>, <a %(torrentfreak_2)s>andra</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "För inte så länge sedan var \"skuggbibliotek\" på väg att försvinna. Sci-Hub, det massiva olagliga arkivet av akademiska artiklar, hade slutat ta emot nya verk på grund av rättsprocesser. \"Z-Library\", det största olagliga biblioteket av böcker, såg sina påstådda skapare arresterade för brott mot upphovsrätten. De lyckades otroligt nog undkomma sin arrestering, men deras bibliotek är inte mindre hotat."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "När Z-Library stod inför nedläggning hade jag redan säkerhetskopierat hela dess bibliotek och letade efter en plattform att hysa det. Det var min motivation för att starta Annas Arkiv: en fortsättning på uppdraget bakom de tidigare initiativen. Vi har sedan dess vuxit till att bli det största skuggbiblioteket i världen, med över 140 miljoner upphovsrättsskyddade texter i olika format — böcker, akademiska artiklar, tidskrifter, tidningar och mer."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Mitt team och jag är ideologer. Vi tror att det är moraliskt rätt att bevara och hysa dessa filer. Bibliotek runt om i världen ser sina anslag minskas, och vi kan inte heller lita på att mänsklighetens arv bevaras av företag."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Sedan kom AI. Praktiskt taget alla stora företag som bygger LLM:er kontaktade oss för att träna på vår data. De flesta (men inte alla!) amerikanska företag omprövade när de insåg den olagliga naturen av vårt arbete. Däremot har kinesiska företag entusiastiskt omfamnat vår samling, till synes obekymrade över dess laglighet. Detta är anmärkningsvärt med tanke på Kinas roll som undertecknare av nästan alla stora internationella upphovsrättsavtal."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Vi har gett höghastighetsåtkomst till cirka 30 företag. De flesta av dem är LLM-företag, och några är datamäklare, som kommer att sälja vår samling vidare. De flesta är kinesiska, men vi har också arbetat med företag från USA, Europa, Ryssland, Sydkorea och Japan. DeepSeek <a %(arxiv)s>medgav</a> att en tidigare version tränades på en del av vår samling, även om de är förtegna om sin senaste modell (förmodligen också tränad på vår data)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Om västvärlden vill ligga i framkant i loppet om LLM:er, och i slutändan AGI, behöver den ompröva sin inställning till upphovsrätt, och det snart. Oavsett om du håller med oss eller inte om vår moraliska ståndpunkt, blir detta nu en fråga om ekonomi, och till och med om nationell säkerhet. Alla maktblock bygger artificiella supervetenskapsmän, superhackare och supermilitärer. Informationsfrihet blir en fråga om överlevnad för dessa länder — till och med en fråga om nationell säkerhet."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Vårt team kommer från hela världen, och vi har ingen särskild inriktning. Men vi skulle uppmuntra länder med starka upphovsrättslagar att använda detta existentiella hot för att reformera dem. Så vad ska man göra?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Vår första rekommendation är enkel: förkorta upphovsrättstiden. I USA beviljas upphovsrätt i 70 år efter författarens död. Detta är absurt. Vi kan anpassa detta till patent, som beviljas i 20 år efter ansökan. Detta borde vara mer än tillräckligt med tid för författare av böcker, artiklar, musik, konst och andra kreativa verk att få full ersättning för sina ansträngningar (inklusive långsiktiga projekt som filmatiseringar)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Sedan bör lagstiftare åtminstone inkludera undantag för massbevarande och spridning av texter. Om förlorade intäkter från enskilda kunder är den största oron, kan distribution på personlig nivå förbli förbjuden. I sin tur skulle de som kan hantera stora arkiv — företag som tränar LLM:er, tillsammans med bibliotek och andra arkiv — omfattas av dessa undantag."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Vissa länder gör redan en version av detta. TorrentFreak <a %(torrentfreak)s>rapporterade</a> att Kina och Japan har infört AI-undantag i sina upphovsrättslagar. Det är oklart för oss hur detta interagerar med internationella avtal, men det ger definitivt skydd för deras inhemska företag, vilket förklarar vad vi har sett."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Vad gäller Annas Arkiv — vi kommer att fortsätta vårt underjordiska arbete rotat i moralisk övertygelse. Men vår största önskan är att träda fram i ljuset och förstärka vår påverkan lagligt. Snälla reformera upphovsrätten."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Läs de kompletterande artiklarna av TorrentFreak: <a %(torrentfreak)s>första</a>, <a %(torrentfreak_2)s>andra</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Vinnare av $10,000 ISBN-visualiseringspriset"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Sammanfattning: Vi fick några otroliga bidrag till $10,000 ISBN-visualiseringspriset."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "För några månader sedan utlyste vi ett <a %(all_isbns)s>$10,000 pris</a> för att skapa den bästa möjliga visualiseringen av vår data som visar ISBN-rymden. Vi betonade att visa vilka filer vi redan har/inte har arkiverat, och vi lade senare till en dataset som beskriver hur många bibliotek som har ISBN (ett mått på sällsynthet)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Vi har blivit överväldigade av responsen. Det har funnits så mycket kreativitet. Ett stort tack till alla som har deltagit: er energi och entusiasm är smittsam!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "I slutändan ville vi besvara följande frågor: <strong>vilka böcker finns i världen, hur många har vi redan arkiverat, och vilka böcker bör vi fokusera på härnäst?</strong> Det är fantastiskt att se så många människor bry sig om dessa frågor."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Vi började med en grundläggande visualisering själva. På mindre än 300kb representerar denna bild kortfattat den största helt öppna \"listan över böcker\" som någonsin sammanställts i mänsklighetens historia:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alla ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Filer i Annas Arkiv"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC dataläcka"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSID"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhosts eBook Index"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Böcker"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internetarkivet"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register of Publishers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Ryska statliga biblioteket"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperial Library of Trantor"

#, fuzzy
msgid "common.back"
msgstr "Tillbaka"

#, fuzzy
msgid "common.forward"
msgstr "Framåt"

#, fuzzy
msgid "common.last"
msgstr "Senast"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Se det <a %(all_isbns)s>ursprungliga blogginlägget</a> för mer information."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Vi utmanade att förbättra detta. Vi skulle belöna en förstaplats med $6,000, andraplats med $3,000 och tredjeplats med $1,000. På grund av det överväldigande gensvaret och otroliga bidrag har vi beslutat att öka prispotten något och belöna en fyrvägs tredjeplats med $500 vardera. Vinnarna är nedan, men se till att titta på alla bidrag <a %(annas_archive)s>här</a>, eller ladda ner vår <a %(a_2025_01_isbn_visualization_files)s>kombinerade torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Första plats $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Detta <a %(phiresky_github)s>bidrag</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentar</a>) är helt enkelt allt vi ville ha, och mer! Vi gillade särskilt de otroligt flexibla visualiseringsalternativen (som till och med stöder anpassade shaders), men med en omfattande lista av förinställningar. Vi gillade också hur snabbt och smidigt allt är, den enkla implementeringen (som inte ens har en backend), den smarta minikartan och den omfattande förklaringen i deras <a %(phiresky_github)s>blogginlägg</a>. Otroligt arbete och en välförtjänt vinnare!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Andra plats $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Ett annat otroligt <a %(annas_archive_note_2913)s>bidrag</a>. Inte lika flexibelt som förstaplatsen, men vi föredrog faktiskt dess makronivåvisualisering över förstaplatsen (space-filling curve, gränser, märkning, markering, panorering och zoomning). En <a %(annas_archive_note_2971)s>kommentar</a> av Joe Davis resonerade med oss:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "”Även om perfekta kvadrater och rektanglar är matematiskt tilltalande, ger de inte överlägsen lokalitet i ett kartläggningssammanhang. Jag tror att asymmetrin som är inneboende i dessa Hilbert eller klassiska Morton inte är en brist utan en funktion. Precis som Italiens berömda stövelformade kontur gör det omedelbart igenkännligt på en karta, kan de unika \"egenheterna\" hos dessa kurvor fungera som kognitiva landmärken. Denna distinktivitet kan förbättra det rumsliga minnet och hjälpa användare att orientera sig, vilket potentiellt gör det lättare att lokalisera specifika regioner eller märka mönster.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Och fortfarande massor av alternativ för visualisering och rendering, samt en otroligt smidig och intuitiv användargränssnitt. En solid andraplats!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tredje plats $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "I detta <a %(annas_archive_note_2940)s>bidrag</a> gillade vi verkligen de olika typerna av vyer, särskilt jämförelse- och förlagsvyerna."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tredje plats $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Även om det inte är det mest polerade användargränssnittet, uppfyller detta <a %(annas_archive_note_2917)s>bidrag</a> många av kraven. Vi gillade särskilt dess jämförelsefunktion."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tredje plats $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Precis som förstaplatsen imponerade detta <a %(annas_archive_note_2975)s>bidrag</a> oss med sin flexibilitet. I slutändan är det detta som gör ett bra visualiseringsverktyg: maximal flexibilitet för avancerade användare, samtidigt som det hålls enkelt för genomsnittliga användare."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tredje plats $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Det sista <a %(annas_archive_note_2947)s>bidraget</a> som får en belöning är ganska grundläggande, men har några unika funktioner som vi verkligen gillade. Vi gillade hur de visar hur många datasets som täcker ett visst ISBN som ett mått på popularitet/pålitlighet. Vi gillade också verkligen enkelheten men effektiviteten i att använda en opacitetsreglage för jämförelser."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Noterbara idéer"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Några fler idéer och implementeringar som vi särskilt gillade:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Skyskrapor för sällsynthet"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live-statistik"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anteckningar och även live-statistik"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unik kartvy och filter"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Snygg standardfärgskala och värmekarta."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Enkel växling av datasets för snabba jämförelser."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Fina etiketter."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skalstock med antal böcker."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Många reglage för att jämföra datasets, som om du vore en DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Vi skulle kunna fortsätta ett tag, men låt oss stanna här. Var noga med att titta på alla bidrag <a %(annas_archive)s>här</a>, eller ladda ner vår <a %(a_2025_01_isbn_visualization_files)s>kombinerade torrent</a>. Så många bidrag, och varje ger ett unikt perspektiv, oavsett om det är i UI eller implementering."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Vi kommer åtminstone att integrera det förstaplacerade bidraget på vår huvudsida, och kanske några andra. Vi har också börjat tänka på hur vi ska organisera processen för att identifiera, bekräfta och sedan arkivera de sällsyntaste böckerna. Mer kommer på denna front."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Tack till alla som deltog. Det är fantastiskt att så många bryr sig."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Våra hjärtan är fulla av tacksamhet."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisera alla ISBN — $10,000 belöning senast 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Denna bild representerar den största helt öppna \"listan över böcker\" som någonsin sammanställts i mänsklighetens historia."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Denna bild är 1000×800 pixlar. Varje pixel representerar 2 500 ISBN. Om vi har en fil för ett ISBN gör vi den pixeln mer grön. Om vi vet att ett ISBN har utfärdats, men vi inte har en matchande fil, gör vi den mer röd."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "På mindre än 300kb representerar denna bild kortfattat den största helt öppna \"listan över böcker\" som någonsin sammanställts i mänsklighetens historia (några hundra GB komprimerad i sin helhet)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Den visar också: det finns mycket arbete kvar med att säkerhetskopiera böcker (vi har bara 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Bakgrund"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hur kan Annas Arkiv uppnå sitt mål att säkerhetskopiera all mänsklighetens kunskap, utan att veta vilka böcker som fortfarande finns där ute? Vi behöver en ATT GÖRA-lista. Ett sätt att kartlägga detta är genom ISBN-nummer, som sedan 1970-talet har tilldelats varje bok som publiceras (i de flesta länder)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Det finns ingen central myndighet som känner till alla ISBN-tilldelningar. Istället är det ett distribuerat system, där länder får nummerintervall, som sedan tilldelar mindre intervall till stora förlag, som i sin tur kan dela upp intervall till mindre förlag. Slutligen tilldelas individuella nummer till böcker."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Vi började kartlägga ISBNs <a %(blog)s>för två år sedan</a> med vår skrapning av ISBNdb. Sedan dess har vi skrapat många fler metadata-källor, såsom <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby och fler. En fullständig lista finns på sidorna \"Datasets\" och \"Torrents\" på Annas Arkiv. Vi har nu den överlägset största helt öppna, lätt nedladdningsbara samlingen av bokmetadata (och därmed ISBNs) i världen."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Vi har <a %(blog)s>skrivit utförligt</a> om varför vi bryr oss om bevarande, och varför vi just nu befinner oss i ett kritiskt fönster. Vi måste nu identifiera sällsynta, underfokuserade och unikt utsatta böcker och bevara dem. Att ha bra metadata om alla böcker i världen hjälper med det."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisering"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Förutom översiktsbilden kan vi också titta på individuella datasets vi har förvärvat. Använd rullgardinsmenyn och knapparna för att växla mellan dem."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Det finns många intressanta mönster att se i dessa bilder. Varför finns det en viss regelbundenhet av linjer och block, som verkar ske i olika skalor? Vad är de tomma områdena? Varför är vissa datasets så klustrade? Vi lämnar dessa frågor som en övning för läsaren."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 belöning"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Det finns mycket att utforska här, så vi utlyser en belöning för att förbättra visualiseringen ovan. Till skillnad från de flesta av våra belöningar är denna tidsbegränsad. Du måste <a %(annas_archive)s>skicka in</a> din öppen källkod senast 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Den bästa inskickningen får $6,000, andra plats är $3,000, och tredje plats är $1,000. Alla belöningar kommer att delas ut med Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Nedan finns de minimala kriterierna. Om ingen inskickning uppfyller kriterierna kan vi fortfarande dela ut några belöningar, men det kommer att vara efter vårt gottfinnande."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Forka detta repo, och redigera detta blogginläggs HTML (inga andra backends förutom vår Flask-backend är tillåtna)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Gör bilden ovan smidigt zoombar, så att du kan zooma hela vägen till individuella ISBNs. Att klicka på ISBNs ska ta dig till en metadata-sida eller sökning på Annas Arkiv."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Du måste fortfarande kunna växla mellan alla olika datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landsintervall och förlagsintervall ska markeras vid hovring. Du kan använda t.ex. <a %(github_xlcnd_isbnlib)s>data4info.py i isbnlib</a> för landsinformation, och vår \"isbngrp\" skrapning för förlag (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Det måste fungera bra på både stationära och mobila enheter."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "För bonuspoäng (detta är bara idéer — låt din kreativitet flöda):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Stark hänsyn kommer att tas till användbarhet och hur bra det ser ut."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Visa faktisk metadata för individuella ISBNs när du zoomar in, såsom titel och författare."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Bättre rymdfyllningskurva. T.ex. en sicksack, som går från 0 till 4 på första raden och sedan tillbaka (i omvänd ordning) från 5 till 9 på andra raden — tillämpas rekursivt."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Olika eller anpassningsbara färgscheman."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Speciella vyer för att jämföra datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Sätt att felsöka problem, såsom annan metadata som inte stämmer överens väl (t.ex. mycket olika titlar)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Kommentera bilder med kommentarer om ISBN eller intervall."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Eventuella heuristiker för att identifiera sällsynta eller hotade böcker."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Vilka kreativa idéer du än kan komma på!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Du KAN helt avvika från de minimala kriterierna och göra en helt annan visualisering. Om den är riktigt spektakulär, kvalificerar den sig för belöningen, men efter vårt gottfinnande."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Gör inlämningar genom att posta en kommentar till <a %(annas_archive)s>detta ärende</a> med en länk till ditt forkade repo, merge-förfrågan eller diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Koden för att generera dessa bilder, samt andra exempel, finns i <a %(annas_archive)s>denna katalog</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Vi kom på ett kompakt dataformat, med vilket all nödvändig ISBN-information är cirka 75MB (komprimerad). Beskrivningen av dataformatet och koden för att generera det finns <a %(annas_archive_l1244_1319)s>här</a>. För belöningen behöver du inte använda detta, men det är förmodligen det mest praktiska formatet att börja med. Du kan transformera vår metadata hur du vill (även om all din kod måste vara öppen källkod)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Vi kan inte vänta på att se vad du kommer på. Lycka till!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Annas Arkivbehållare (AAC): standardisering av utgåvor från världens största skuggbibliotek"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Annas Arkiv har blivit det största skuggbiblioteket i världen, vilket kräver att vi standardiserar våra utgåvor."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Annas Arkiv</a> har blivit det överlägset största skuggbiblioteket i världen, och det enda skuggbiblioteket i sin skala som är helt öppen källkod och öppen data. Nedan är en tabell från vår Datasets-sida (något modifierad):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Vi uppnådde detta på tre sätt:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Spegling av befintliga öppna data-skuggbibliotek (som Sci-Hub och Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Hjälpa skuggbibliotek som vill vara mer öppna, men inte hade tid eller resurser att göra det (som Libgen seriekollektion)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Skrapa bibliotek som inte vill dela i bulk (som Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "För (2) och (3) hanterar vi nu en betydande samling av torrents själva (100-tals TB). Hittills har vi behandlat dessa samlingar som engångsföreteelser, vilket innebär skräddarsydd infrastruktur och dataorganisation för varje samling. Detta tillför betydande overhead till varje utgåva och gör det särskilt svårt att göra mer inkrementella utgåvor."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Det är därför vi har beslutat att standardisera våra utgåvor. Detta är ett tekniskt blogginlägg där vi introducerar vår standard: <strong>Annas Arkivbehållare</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Designmål"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Vårt primära användningsfall är distributionen av filer och tillhörande metadata från olika befintliga samlingar. Våra viktigaste överväganden är:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogena filer och metadata, så nära originalformatet som möjligt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogena identifierare i källbiblioteken, eller till och med avsaknad av identifierare."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separata utgåvor av metadata kontra fildata, eller endast metadata-utgåvor (t.ex. vår ISBNdb-utgåva)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribution genom torrents, men med möjlighet till andra distributionsmetoder (t.ex. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Oföränderliga poster, eftersom vi bör anta att våra torrents kommer att leva för evigt."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementella utgåvor / tilläggsutgåvor."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Maskinläsbara och skrivbara, bekvämt och snabbt, särskilt för vår stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Något enkel mänsklig inspektion, även om detta är sekundärt till maskinläsbarhet."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Enkelt att så våra samlingar med en standardhyrd seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binär data kan serveras direkt av webbservrar som Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Några icke-mål:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Vi bryr oss inte om att filer är lätta att navigera manuellt på disk, eller sökbara utan förbehandling."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Vi bryr oss inte om att vara direkt kompatibla med befintlig biblioteksprogramvara."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Även om det ska vara enkelt för vem som helst att så vår samling med hjälp av torrents, förväntar vi oss inte att filerna ska vara användbara utan betydande teknisk kunskap och engagemang."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Eftersom Annas Arkiv är öppen källkod vill vi använda vårt format direkt. När vi uppdaterar vårt sökindex, får vi endast tillgång till offentligt tillgängliga vägar, så att alla som forkar vårt bibliotek snabbt kan komma igång."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Standarden"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Slutligen bestämde vi oss för en relativt enkel standard. Den är ganska lös, icke-normativ och ett pågående arbete."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Annas Arkiv Container) är ett enskilt objekt bestående av <strong>metadata</strong>, och eventuellt <strong>binär data</strong>, båda är oföränderliga. Det har en globalt unik identifierare, kallad <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Samling.</strong> Varje AAC tillhör en samling, som per definition är en lista över AACs som är semantiskt konsekventa. Det betyder att om du gör en betydande förändring av formatet på metadata, måste du skapa en ny samling."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” och “files” samlingar.</strong> Enligt konvention är det ofta bekvämt att släppa “records” och “files” som olika samlingar, så att de kan släppas vid olika tidpunkter, t.ex. baserat på skrapningshastigheter. En “record” är en samling endast med metadata, innehållande information som boktitlar, författare, ISBN, etc., medan “files” är samlingarna som innehåller själva filerna (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Formatet för AACID är detta: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Till exempel, en faktisk AACID som vi har släppt är <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: samlingsnamnet, som kan innehålla ASCII-bokstäver, siffror och understreck (men inga dubbla understreck)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: en kort version av ISO 8601, alltid i UTC, t.ex. <code>20220723T194746Z</code>. Detta nummer måste öka monotoniskt för varje släpp, även om dess exakta semantik kan skilja sig per samling. Vi föreslår att använda tiden för skrapning eller generering av ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: en samlingsspecifik identifierare, om tillämpligt, t.ex. Z-Library ID. Kan utelämnas eller förkortas. Måste utelämnas eller förkortas om AACID annars skulle överstiga 150 tecken."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: en UUID men komprimerad till ASCII, t.ex. med base57. Vi använder för närvarande <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteket."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID-intervall.</strong> Eftersom AACIDs innehåller monotoniskt ökande tidsstämplar, kan vi använda det för att ange intervall inom en viss samling. Vi använder detta format: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, där tidsstämplarna är inkluderande. Detta är konsekvent med ISO 8601-notation. Intervall är kontinuerliga och kan överlappa, men vid överlappning måste de innehålla identiska poster som den tidigare släppta i den samlingen (eftersom AACs är oföränderliga). Saknade poster är inte tillåtna."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadatafil.</strong> En metadatafil innehåller metadata för ett intervall av AACs, för en viss samling. Dessa har följande egenskaper:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Filnamnet måste vara ett AACID-intervall, försett med prefixet <code style=\"color: red\">annas_archive_meta__</code> och följt av <code>.jsonl.zstd</code>. Till exempel, en av våra släpp kallas<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Som indikeras av filändelsen, är filtypen <a %(jsonlines)s>JSON Lines</a> komprimerad med <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Varje JSON-objekt måste innehålla följande fält på toppnivå: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (valfritt). Inga andra fält är tillåtna."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> är godtycklig metadata, enligt samlingens semantik. Den måste vara semantiskt konsekvent inom samlingen."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> är valfritt, och är namnet på den binära datamappen som innehåller motsvarande binär data. Filnamnet på den motsvarande binära datan inom den mappen är postens AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Prefixet <code style=\"color: red\">annas_archive_meta__</code> kan anpassas till namnet på din institution, t.ex. <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binär datamapp.</strong> En mapp med den binära datan för ett intervall av AACs, för en viss samling. Dessa har följande egenskaper:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Katalognamnet måste vara ett AACID-intervall, försett med prefixet <code style=\"color: green\">annas_archive_data__</code>, och inget suffix. Till exempel, en av våra faktiska släpp har en katalog som heter<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Katalogen måste innehålla datafiler för alla AACs inom det angivna intervallet. Varje datafil måste ha sin AACID som filnamn (inga tillägg)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Det rekommenderas att göra dessa mappar någorlunda hanterbara i storlek, t.ex. inte större än 100GB-1TB vardera, även om denna rekommendation kan ändras över tid."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrenter.</strong> Metadatafilerna och mapparna med binärdata kan paketeras i torrenter, med en torrent per metadatafil eller en torrent per binärdatamapp. Torrenterna måste ha det ursprungliga fil-/katalognamnet plus ett <code>.torrent</code>-suffix som deras filnamn."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Exempel"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Låt oss titta på vår senaste Z-Library-utgåva som ett exempel. Den består av två samlingar: ”<span style=\"background: #fffaa3\">zlib3_records</span>” och ”<span style=\"background: #ffd6fe\">zlib3_files</span>”. Detta gör att vi kan skrapa och släppa metadataregister separat från de faktiska bokfilerna. Som sådant släppte vi två torrenter med metadatafiler:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Vi släppte också en mängd torrenter med binärdatamappar, men endast för ”<span style=\"background: #ffd6fe\">zlib3_files</span>”-samlingen, totalt 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Genom att köra <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kan vi se vad som finns inuti:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "I detta fall är det metadata för en bok som rapporterats av Z-Library. På toppnivån har vi bara ”aacid” och ”metadata”, men ingen ”data_folder”, eftersom det inte finns någon motsvarande binärdata. AACID innehåller ”22430000” som det primära ID:t, vilket vi kan se är taget från ”zlibrary_id”. Vi kan förvänta oss att andra AAC:er i denna samling har samma struktur."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nu låt oss köra <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Detta är en mycket mindre AAC-metadata, även om huvuddelen av denna AAC finns någon annanstans i en binär fil! Trots allt har vi en ”data_folder” denna gång, så vi kan förvänta oss att motsvarande binärdata finns på <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. ”Metadata” innehåller ”zlibrary_id”, så vi kan enkelt associera det med motsvarande AAC i ”zlib_records”-samlingen. Vi kunde ha associerat på ett antal olika sätt, t.ex. genom AACID — standarden föreskriver inte det."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Observera att det inte heller är nödvändigt för ”metadata”-fältet att i sig vara JSON. Det kan vara en sträng som innehåller XML eller något annat dataformat. Du kan till och med lagra metadata-information i den associerade binära blobben, t.ex. om det är mycket data."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Slutsats"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Med denna standard kan vi göra utgåvor mer stegvis och lättare lägga till nya datakällor. Vi har redan några spännande utgåvor på gång!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Vi hoppas också att det blir lättare för andra skuggbibliotek att spegla våra samlingar. Trots allt är vårt mål att bevara mänsklig kunskap och kultur för alltid, så ju mer redundans desto bättre."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Annas Uppdatering: helt öppen källkod arkiv, ElasticSearch, 300GB+ av bokomslag"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Vi har arbetat dygnet runt för att erbjuda ett bra alternativ med Annas Arkiv. Här är några av de saker vi nyligen har uppnått."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Med Z-Library som stängs ner och dess (påstådda) grundare som arresteras, har vi arbetat dygnet runt för att erbjuda ett bra alternativ med Annas Arkiv (vi kommer inte att länka det här, men du kan Googla det). Här är några av de saker vi nyligen har uppnått."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Annas Arkiv är helt öppen källkod"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Vi tror att information ska vara fri, och vår egen kod är inget undantag. Vi har släppt all vår kod på vår privat hostade Gitlab-instans: <a %(annas_archive)s>Annas Programvara</a>. Vi använder också ärendehanteraren för att organisera vårt arbete. Om du vill engagera dig i vår utveckling är detta en bra plats att börja."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "För att ge dig en smak av de saker vi arbetar med, ta vårt senaste arbete med prestandaförbättringar på klientsidan. Eftersom vi ännu inte har implementerat paginering, skulle vi ofta returnera mycket långa söksidor, med 100-200 resultat. Vi ville inte avbryta sökresultaten för tidigt, men detta innebar att det skulle sakta ner vissa enheter. För detta implementerade vi ett litet trick: vi omslöt de flesta sökresultaten i HTML-kommentarer (<code><!-- --></code>), och skrev sedan en liten Javascript som skulle upptäcka när ett resultat skulle bli synligt, vid vilken tidpunkt vi skulle avlägsna kommentaren:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisering\" implementerad i 23 rader, inget behov av avancerade bibliotek! Detta är den typ av snabb pragmatisk kod som du får när du har begränsad tid och verkliga problem som behöver lösas. Det har rapporterats att vår sökning nu fungerar bra på långsamma enheter!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "En annan stor insats var att automatisera byggandet av databasen. När vi lanserade, drog vi bara ihop olika källor slumpmässigt. Nu vill vi hålla dem uppdaterade, så vi skrev en mängd skript för att ladda ner ny metadata från de två Library Genesis-forkarna och integrera dem. Målet är inte bara att göra detta användbart för vårt arkiv, utan att göra det enkelt för alla som vill experimentera med skuggbibliotekets metadata. Målet skulle vara en Jupyter-notebook som har alla möjliga intressanta metadata tillgängliga, så vi kan göra mer forskning som att ta reda på vilken <a %(blog)s>procentandel av ISBN:er som bevaras för alltid</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Slutligen har vi omarbetat vårt donationssystem. Du kan nu använda ett kreditkort för att direkt sätta in pengar på våra kryptoplånböcker, utan att egentligen behöva veta något om kryptovalutor. Vi kommer att fortsätta övervaka hur bra detta fungerar i praktiken, men det är en stor sak."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Byt till ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "En av våra <a %(annas_archive)s>biljetter</a> var en blandning av problem med vårt söksystem. Vi använde MySQL fulltextsökning, eftersom vi ändå hade all vår data i MySQL. Men det hade sina begränsningar:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Vissa sökningar tog superlång tid, till den grad att de skulle uppta alla öppna anslutningar."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Som standard har MySQL en minsta ordlängd, annars kan ditt index bli riktigt stort. Folk rapporterade att de inte kunde söka efter \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Sökningen var bara något snabb när den var helt laddad i minnet, vilket krävde att vi skaffade en dyrare maskin för att köra detta på, plus några kommandon för att förladda indexet vid start."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Vi skulle inte ha kunnat utöka det enkelt för att bygga nya funktioner, som bättre <a %(wikipedia_cjk_characters)s>tokenisering för språk utan mellanslag</a>, filtrering/facettering, sortering, \"menade du\"-förslag, autokomplettering och så vidare."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Efter att ha pratat med en mängd experter bestämde vi oss för ElasticSearch. Det har inte varit perfekt (deras standard \"menade du\"-förslag och autokompletteringsfunktioner är dåliga), men överlag har det varit mycket bättre än MySQL för sökning. Vi är fortfarande inte <a %(youtube)s>för entusiastiska</a> över att använda det för någon kritisk data (även om de har gjort mycket <a %(elastic_co)s>framsteg</a>), men överlag är vi ganska nöjda med bytet."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "För tillfället har vi implementerat mycket snabbare sökning, bättre språksupport, bättre relevanssortering, olika sorteringsalternativ och filtrering på språk/boktyp/filtyp. Om du är nyfiken på hur det fungerar, <a %(annas_archive_l140)s>ta</a> <a %(annas_archive_l1115)s>en</a> <a %(annas_archive_l1635)s>titt</a>. Det är ganska tillgängligt, även om det skulle behöva några fler kommentarer…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ av bokomslag släppta"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Slutligen är vi glada att tillkännage en liten release. I samarbete med de som driver Libgen.rs-forken delar vi alla deras bokomslag via torrents och IPFS. Detta kommer att fördela belastningen av att visa omslagen bland fler maskiner och bevara dem bättre. I många (men inte alla) fall är bokomslagen inkluderade i filerna själva, så detta är en slags \"härledd data\". Men att ha det i IPFS är fortfarande mycket användbart för den dagliga driften av både Annas Arkiv och de olika Library Genesis-forkarna."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Som vanligt kan du hitta denna release på Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Vi kommer inte att länka till det här, men du kan enkelt hitta det."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Förhoppningsvis kan vi sakta ner vårt tempo lite nu när vi har ett anständigt alternativ till Z-Library. Denna arbetsbelastning är inte särskilt hållbar. Om du är intresserad av att hjälpa till med programmering, serverdrift eller bevarandearbete, tveka inte att kontakta oss. Det finns fortfarande mycket <a %(annas_archive)s>arbete att göra</a>. Tack för ditt intresse och stöd."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Annas Arkiv har säkerhetskopierat världens största skuggbibliotek för serier (95TB) — du kan hjälpa till att seeda det"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Världens största skuggbibliotek för serier hade en enda felpunkt... tills idag."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskutera på Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Det största skuggbiblioteket för serier är troligen det av en särskild Library Genesis-fork: Libgen.li. Den enda administratören som driver den webbplatsen lyckades samla en galen samling serier på över 2 miljoner filer, totalt över 95TB. Men till skillnad från andra Library Genesis-samlingar var denna inte tillgänglig i bulk via torrents. Du kunde bara komma åt dessa serier individuellt via hans långsamma personliga server — en enda felpunkt. Tills idag!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "I det här inlägget kommer vi att berätta mer om denna samling och om vår insamling för att stödja mer av detta arbete."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon försöker förlora sig själv i bibliotekets vardagliga värld…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-förgreningar"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Först lite bakgrund. Du kanske känner till Library Genesis för deras episka boksamling. Färre människor vet att Library Genesis-volontärer har skapat andra projekt, såsom en stor samling av tidskrifter och standarddokument, en fullständig backup av Sci-Hub (i samarbete med grundaren av Sci-Hub, Alexandra Elbakyan), och faktiskt en massiv samling av serier."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Vid något tillfälle gick olika operatörer av Library Genesis-spegelservrar skilda vägar, vilket ledde till den nuvarande situationen med ett antal olika \"förgreningar\", som alla fortfarande bär namnet Library Genesis. Libgen.li-förgreningen har unikt denna seriesamling, samt en stor tidskriftssamling (som vi också arbetar med)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Samarbete"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Med tanke på dess storlek har denna samling länge funnits på vår önskelista, så efter vår framgång med att säkerhetskopiera Z-Library, satte vi siktet på denna samling. Först skrapade vi den direkt, vilket var en riktig utmaning, eftersom deras server inte var i bästa skick. Vi fick ungefär 15TB på detta sätt, men det gick långsamt."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Lyckligtvis lyckades vi få kontakt med operatören av biblioteket, som gick med på att skicka oss all data direkt, vilket gick mycket snabbare. Det tog fortfarande mer än ett halvår att överföra och bearbeta all data, och vi var nära att förlora allt på grund av diskfel, vilket skulle ha inneburit att börja om från början."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Denna erfarenhet har fått oss att tro att det är viktigt att få ut denna data så snabbt som möjligt, så att den kan speglas brett och vitt. Vi är bara en eller två olyckligt tajmade incidenter ifrån att förlora denna samling för alltid!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Samlingen"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Att röra sig snabbt innebär att samlingen är lite oorganiserad… Låt oss ta en titt. Föreställ dig att vi har ett filsystem (som i verkligheten delas upp över torrents):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Den första katalogen, <code>/repository</code>, är den mer strukturerade delen av detta. Denna katalog innehåller så kallade \"tusen kataloger\": kataloger var och en med tusen filer, som är inkrementellt numrerade i databasen. Katalogen <code>0</code> innehåller filer med comic_id 0–999, och så vidare."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Detta är samma schema som Library Genesis har använt för sina skönlitterära och facklitterära samlingar. Idén är att varje \"tusen katalog\" automatiskt omvandlas till en torrent så snart den är fylld."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Dock gjorde Libgen.li-operatören aldrig torrents för denna samling, och därför blev tusen katalogerna troligen obekväma, och gav plats åt \"osorterade kataloger\". Dessa är <code>/comics0</code> till <code>/comics4</code>. De innehåller alla unika katalogstrukturer, som troligen var logiska för att samla in filerna, men som inte är så logiska för oss nu. Lyckligtvis hänvisar metadata fortfarande direkt till alla dessa filer, så deras lagringsorganisation på disken spelar faktiskt ingen roll!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadatan är tillgänglig i form av en MySQL-databas. Den kan laddas ner direkt från Libgen.li-webbplatsen, men vi kommer också att göra den tillgänglig i en torrent, tillsammans med vår egen tabell med alla MD5-hashar."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analys"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "När du får 95TB dumpat i ditt lagringskluster försöker du förstå vad som ens finns där… Vi gjorde en del analys för att se om vi kunde minska storleken lite, till exempel genom att ta bort dubbletter. Här är några av våra fynd:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantiska dubbletter (olika skanningar av samma bok) kan teoretiskt filtreras bort, men det är knepigt. När vi manuellt tittade igenom serierna hittade vi för många falska positiva."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Det finns några dubbletter enbart genom MD5, vilket är relativt slösaktigt, men att filtrera bort dem skulle bara ge oss ungefär 1% in besparingar. I denna skala är det fortfarande ungefär 1TB, men också, i denna skala spelar 1TB egentligen ingen roll. Vi vill hellre inte riskera att av misstag förstöra data i denna process."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Vi hittade en massa icke-bokdata, såsom filmer baserade på serietidningar. Det verkar också slösaktigt, eftersom dessa redan är allmänt tillgängliga genom andra medel. Men vi insåg att vi inte bara kunde filtrera bort filmfiler, eftersom det också finns <em>interaktiva serieböcker</em> som släpptes på datorn, som någon spelade in och sparade som filmer."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "I slutändan skulle allt vi kunde ta bort från samlingen bara spara några procent. Sedan kom vi ihåg att vi är datahamstrare, och de som kommer att spegla detta är också datahamstrare, så, \"VAD MENAR DU MED ATT RADERA?!\" :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Vi presenterar därför för er, den fullständiga, omodifierade samlingen. Det är mycket data, men vi hoppas att tillräckligt många bryr sig om att dela den ändå."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Insamling"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Vi släpper denna data i några stora bitar. Den första torrenten är av <code>/comics0</code>, som vi lade i en enorm 12TB .tar-fil. Det är bättre för din hårddisk och torrentprogramvara än en miljard mindre filer."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Som en del av denna release gör vi en insamling. Vi siktar på att samla in $20,000 för att täcka drifts- och kontraktskostnader för denna samling, samt möjliggöra pågående och framtida projekt. Vi har några <em>massiva</em> på gång."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Vem stödjer jag med min donation?</em> Kort sagt: vi säkerhetskopierar all mänsklighetens kunskap och kultur och gör den lättillgänglig. All vår kod och data är öppen källkod, vi är ett helt volontärdrivet projekt, och vi har sparat 125TB böcker hittills (utöver Libgen och Scihubs befintliga torrents). I slutändan bygger vi ett svänghjul som möjliggör och uppmuntrar människor att hitta, skanna och säkerhetskopiera alla böcker i världen. Vi kommer att skriva om vår huvudplan i ett framtida inlägg. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Om du donerar för ett 12 månaders \"Amazing Archivist\"-medlemskap ($780), får du <strong>“adoptera en torrent”</strong>, vilket innebär att vi kommer att sätta ditt användarnamn eller meddelande i filnamnet på en av torrentfilerna!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Du kan donera genom att gå till <a %(wikipedia_annas_archive)s>Annas Arkiv</a> och klicka på \"Donera\"-knappen. Vi söker också fler volontärer: mjukvaruingenjörer, säkerhetsforskare, experter på anonyma betalningar och översättare. Du kan också stödja oss genom att tillhandahålla hostingtjänster. Och naturligtvis, vänligen dela våra torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Tack till alla som redan har stöttat oss så generöst! Ni gör verkligen en skillnad."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Här är de torrents som släppts hittills (vi bearbetar fortfarande resten):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alla torrents kan hittas på <a %(wikipedia_annas_archive)s>Annas Arkiv</a> under \"Datasets\" (vi länkar inte direkt dit, så länkar till denna blogg inte tas bort från Reddit, Twitter, etc). Därifrån, följ länken till Tor-webbplatsen."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Vad är nästa steg?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "En massa torrents är bra för långsiktig bevarande, men inte så mycket för daglig åtkomst. Vi kommer att arbeta med hostingpartners för att få upp all denna data på webben (eftersom Annas Arkiv inte värdar något direkt). Naturligtvis kommer du att kunna hitta dessa nedladdningslänkar på Annas Arkiv."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Vi bjuder också in alla att göra saker med denna data! Hjälp oss att bättre analysera den, deduplicera den, lägga den på IPFS, remixa den, träna dina AI-modeller med den, och så vidare. Den är helt din, och vi kan inte vänta på att se vad du gör med den."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Slutligen, som sagt tidigare, har vi fortfarande några massiva releaser på gång (om <em>någon</em> kunde <em>av misstag</em> skicka oss en dump av en <em>viss</em> ACS4-databas, vet du var du hittar oss...), samt bygga svänghjulet för att säkerhetskopiera alla böcker i världen."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Så håll utkik, vi har precis börjat."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nya böcker tillagda till Pirate Library Mirror (+24TB, 3,8 miljoner böcker)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "I den ursprungliga releasen av Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>), gjorde vi en spegel av Z-Library, en stor illegal boksamling. Som en påminnelse, detta är vad vi skrev i det ursprungliga blogginlägget:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library är ett populärt (och olagligt) bibliotek. De har tagit Library Genesis-samlingen och gjort den lättsökbar. Utöver det har de blivit mycket effektiva på att uppmuntra nya bokbidrag, genom att belöna bidragande användare med olika förmåner. De bidrar för närvarande inte med dessa nya böcker tillbaka till Library Genesis. Och till skillnad från Library Genesis gör de inte sin samling lätt spegelbar, vilket förhindrar bred bevarande. Detta är viktigt för deras affärsmodell, eftersom de tar betalt för att få tillgång till deras samling i bulk (mer än 10 böcker per dag)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Vi gör inga moraliska bedömningar om att ta betalt för massåtkomst till en illegal boksamling. Det råder ingen tvekan om att Z-Library har varit framgångsrik i att utöka tillgången till kunskap och att skaffa fler böcker. Vi är helt enkelt här för att göra vår del: att säkerställa det långsiktiga bevarandet av denna privata samling."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Den samlingen daterades tillbaka till mitten av 2021. Under tiden har Z-Library vuxit i en häpnadsväckande takt: de har lagt till cirka 3,8 miljoner nya böcker. Det finns några dubbletter där, visst, men majoriteten verkar vara genuint nya böcker, eller högkvalitativa skanningar av tidigare inskickade böcker. Detta beror till stor del på det ökade antalet frivilliga moderatorer på Z-Library och deras system för massuppladdning med deduplicering. Vi vill gratulera dem till dessa prestationer."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Vi är glada att meddela att vi har fått alla böcker som lades till Z-Library mellan vår senaste spegel och augusti 2022. Vi har också gått tillbaka och hämtat några böcker som vi missade första gången. Sammantaget är denna nya samling cirka 24TB, vilket är mycket större än den förra (7TB). Vår spegel är nu totalt 31TB. Återigen deduplicerade vi mot Library Genesis, eftersom det redan finns torrents tillgängliga för den samlingen."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Vänligen gå till Pirate Library Mirror för att kolla in den nya samlingen (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>). Där finns mer information om hur filerna är strukturerade och vad som har förändrats sedan sist. Vi kommer inte att länka till det härifrån, eftersom detta bara är en bloggwebbplats som inte värdar något olagligt material."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Självklart är seedning också ett bra sätt att hjälpa oss. Tack till alla som seedar vårt tidigare set av torrents. Vi är tacksamma för det positiva gensvaret och glada att det finns så många som bryr sig om bevarandet av kunskap och kultur på detta ovanliga sätt."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Hur man blir en piratarkivist"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Den första utmaningen kan vara en överraskande sådan. Det är inte ett tekniskt problem eller ett juridiskt problem. Det är ett psykologiskt problem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Innan vi dyker in, två uppdateringar om Pirate Library Mirror (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Vi fick några extremt generösa donationer. Den första var $10k från den anonyma individen som också har stöttat \"bookwarrior\", den ursprungliga grundaren av Library Genesis. Speciellt tack till bookwarrior för att ha underlättat denna donation. Den andra var ytterligare $10k från en anonym givare, som tog kontakt efter vår senaste release och blev inspirerad att hjälpa till. Vi hade också ett antal mindre donationer. Tack så mycket för allt ert generösa stöd. Vi har några spännande nya projekt på gång som detta kommer att stödja, så håll utkik."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Vi hade några tekniska svårigheter med storleken på vår andra release, men våra torrents är nu uppe och seedar. Vi fick också ett generöst erbjudande från en anonym individ att seeda vår samling på deras mycket snabba servrar, så vi gör en speciell uppladdning till deras maskiner, varefter alla andra som laddar ner samlingen bör se en stor förbättring i hastighet."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Hela böcker kan skrivas om <em>varför</em> digitalt bevarande i allmänhet, och piratarkivism i synnerhet, men låt oss ge en snabb introduktion för dem som inte är så bekanta. Världen producerar mer kunskap och kultur än någonsin tidigare, men också mer av det går förlorat än någonsin tidigare. Mänskligheten litar i stor utsträckning på företag som akademiska förlag, streamingtjänster och sociala medieföretag med detta arv, och de har ofta inte visat sig vara bra förvaltare. Kolla in dokumentären Digital Amnesia, eller egentligen vilket föredrag som helst av Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Det finns några institutioner som gör ett bra jobb med att arkivera så mycket de kan, men de är bundna av lagen. Som pirater är vi i en unik position att arkivera samlingar som de inte kan röra, på grund av upphovsrättsverkställighet eller andra begränsningar. Vi kan också spegla samlingar många gånger över, över hela världen, vilket ökar chanserna för korrekt bevarande."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "För tillfället kommer vi inte att gå in i diskussioner om för- och nackdelar med immateriella rättigheter, moralen i att bryta mot lagen, funderingar om censur eller frågan om tillgång till kunskap och kultur. Med allt detta ur vägen, låt oss dyka in i <em>hur</em>. Vi kommer att dela med oss av hur vårt team blev piratarkivister och de lärdomar vi lärde oss längs vägen. Det finns många utmaningar när du ger dig ut på denna resa, och förhoppningsvis kan vi hjälpa dig genom några av dem."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Gemenskap"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Den första utmaningen kan vara en överraskande sådan. Det är inte ett tekniskt problem eller ett juridiskt problem. Det är ett psykologiskt problem: att göra detta arbete i skuggorna kan vara otroligt ensamt. Beroende på vad du planerar att göra och din hotmodell kan du behöva vara mycket försiktig. I ena änden av spektrumet har vi personer som Alexandra Elbakyan*, grundaren av Sci-Hub, som är mycket öppen med sina aktiviteter. Men hon löper stor risk att bli arresterad om hon skulle besöka ett västerländskt land vid denna tidpunkt och kan riskera decennier i fängelse. Är det en risk du är villig att ta? Vi är i andra änden av spektrumet; mycket noga med att inte lämna några spår och ha stark operativ säkerhet."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Som nämnts på HN av \"ynno\", ville Alexandra initialt inte bli känd: \"Hennes servrar var inställda på att avge detaljerade felmeddelanden från PHP, inklusive fullständig sökväg till den felande källfilen, som fanns under katalogen /home/<USER>" Så, använd slumpmässiga användarnamn på de datorer du använder för detta, ifall du felkonfigurerar något."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Denna hemlighet kommer dock med en psykologisk kostnad. De flesta älskar att bli erkända för det arbete de gör, och ändå kan du inte ta någon kredit för detta i verkliga livet. Även enkla saker kan vara utmanande, som när vänner frågar vad du har haft för dig (vid något tillfälle blir \"leka med min NAS / homelab\" gammalt)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Det är därför det är så viktigt att hitta en gemenskap. Du kan ge upp en del operativ säkerhet genom att anförtro dig åt några mycket nära vänner, som du vet att du kan lita djupt på. Även då bör du vara försiktig med att inte skriva ner något, ifall de måste överlämna sina e-postmeddelanden till myndigheterna, eller om deras enheter är komprometterade på något annat sätt."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Ännu bättre är att hitta några andra pirater. Om dina nära vänner är intresserade av att gå med dig, fantastiskt! Annars kanske du kan hitta andra online. Tyvärr är detta fortfarande en nischgemenskap. Hittills har vi bara hittat ett fåtal andra som är aktiva inom detta område. Bra startplatser verkar vara Library Genesis-forumen och r/DataHoarder. Archive Team har också likasinnade individer, även om de verkar inom lagen (även om det är i vissa gråzoner av lagen). De traditionella \"warez\"- och piratscenerna har också folk som tänker på liknande sätt."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Vi är öppna för idéer om hur vi kan främja gemenskap och utforska idéer. Känn dig fri att skicka ett meddelande till oss på Twitter eller Reddit. Kanske skulle vi kunna arrangera någon form av forum eller chattgrupp. En utmaning är att detta lätt kan bli censurerat när man använder vanliga plattformar, så vi skulle behöva vara värdar för det själva. Det finns också en avvägning mellan att ha dessa diskussioner helt offentliga (mer potentiellt engagemang) kontra att göra dem privata (inte låta potentiella \"mål\" veta att vi är på väg att skrapa dem). Vi måste fundera på det. Låt oss veta om du är intresserad av detta!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekt"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "När vi gör ett projekt har det ett par faser:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domänval / filosofi: Var vill du ungefär fokusera, och varför? Vilka är dina unika passioner, färdigheter och omständigheter som du kan använda till din fördel?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Målval: Vilken specifik samling kommer du att spegla?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata-skrapning: Katalogisering av information om filerna, utan att faktiskt ladda ner de (ofta mycket större) filerna själva."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Dataval: Baserat på metadata, begränsa vilken data som är mest relevant att arkivera just nu. Det kan vara allt, men ofta finns det ett rimligt sätt att spara utrymme och bandbredd."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Data-skrapning: Att faktiskt få tag på datan."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribution: Paketera det i torrents, annonsera det någonstans, få folk att sprida det."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Dessa är inte helt oberoende faser, och ofta skickar insikter från en senare fas dig tillbaka till en tidigare fas. Till exempel, under metadata-skrapning kan du inse att målet du valt har försvarsmekanismer bortom din kompetensnivå (som IP-blockeringar), så du går tillbaka och hittar ett annat mål."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domänval / filosofi"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Det finns ingen brist på kunskap och kulturarv att rädda, vilket kan vara överväldigande. Därför är det ofta användbart att ta en stund och tänka på vad ditt bidrag kan vara."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Alla har olika sätt att tänka på detta, men här är några frågor du kan ställa dig själv:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Varför är du intresserad av detta? Vad brinner du för? Om vi kan få en massa människor som alla arkiverar de saker de specifikt bryr sig om, skulle det täcka mycket! Du kommer att veta mycket mer än genomsnittspersonen om din passion, som vilken viktig data som ska sparas, vilka de bästa samlingarna och online-gemenskaperna är, och så vidare."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Vilka färdigheter har du som du kan använda till din fördel? Till exempel, om du är en expert på online-säkerhet, kan du hitta sätt att övervinna IP-blockeringar för säkra mål. Om du är bra på att organisera gemenskaper, kanske du kan samla några människor kring ett mål. Det är dock användbart att kunna lite programmering, om inte annat för att hålla god operativ säkerhet genom hela processen."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hur mycket tid har du för detta? Vårt råd skulle vara att börja smått och göra större projekt när du får kläm på det, men det kan bli allt uppslukande."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Vad skulle vara ett högavkastande område att fokusera på? Om du ska spendera X timmar på piratarkivering, hur kan du då få största möjliga \"bang for your buck\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Vilka unika sätt tänker du på detta? Du kanske har några intressanta idéer eller tillvägagångssätt som andra kan ha missat."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "I vårt fall brydde vi oss särskilt om det långsiktiga bevarandet av vetenskap. Vi kände till Library Genesis, och hur det speglades helt många gånger med hjälp av torrents. Vi älskade den idén. Sedan en dag försökte en av oss hitta några vetenskapliga läroböcker på Library Genesis, men kunde inte hitta dem, vilket ifrågasatte hur komplett det verkligen var. Vi sökte sedan efter dessa läroböcker online och hittade dem på andra ställen, vilket planterade fröet för vårt projekt. Även innan vi kände till Z-Library hade vi idén att inte försöka samla alla dessa böcker manuellt, utan att fokusera på att spegla befintliga samlingar och bidra med dem tillbaka till Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Målval"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Så, vi har vårt område som vi tittar på, nu vilken specifik samling ska vi spegla? Det finns ett par saker som gör ett bra mål:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Stor"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unik: inte redan väl täckt av andra projekt."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Tillgänglig: använder inte massor av skyddslager för att förhindra att du skrapar deras metadata och data."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Speciell insikt: du har någon speciell information om detta mål, som att du på något sätt har särskild tillgång till denna samling, eller att du har listat ut hur man övervinner deras försvar. Detta är inte nödvändigt (vårt kommande projekt gör inget speciellt), men det hjälper definitivt!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "När vi hittade våra vetenskapliga läroböcker på andra webbplatser än Library Genesis, försökte vi lista ut hur de hamnade på internet. Vi hittade sedan Z-Library och insåg att även om de flesta böcker inte först dyker upp där, så hamnar de så småningom där. Vi lärde oss om dess relation till Library Genesis och den (ekonomiska) incitamentsstrukturen och överlägsna användargränssnittet, som båda gjorde det till en mycket mer komplett samling. Vi gjorde sedan en preliminär metadata- och dataskrapning och insåg att vi kunde kringgå deras IP-nedladdningsbegränsningar genom att utnyttja en av våra medlemmars särskilda tillgång till massor av proxyservrar."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "När du utforskar olika mål är det redan viktigt att dölja dina spår genom att använda VPN och engångs-e-postadresser, vilket vi kommer att prata mer om senare."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata-skrapning"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Låt oss bli lite mer tekniska här. För att faktiskt skrapa metadata från webbplatser har vi hållit det ganska enkelt. Vi använder Python-skript, ibland curl, och en MySQL-databas för att lagra resultaten i. Vi har inte använt någon avancerad skrapningsprogramvara som kan kartlägga komplexa webbplatser, eftersom vi hittills bara behövde skrapa en eller två typer av sidor genom att bara numrera genom id:n och analysera HTML. Om det inte finns lätt numrerade sidor kan du behöva en riktig crawler som försöker hitta alla sidor."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Innan du börjar skrapa en hel webbplats, försök göra det manuellt ett tag. Gå igenom några dussin sidor själv för att få en känsla för hur det fungerar. Ibland kommer du redan att stöta på IP-blockeringar eller annat intressant beteende på detta sätt. Detsamma gäller för dataskrapning: innan du går för djupt in i detta mål, se till att du faktiskt kan ladda ner dess data effektivt."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "För att kringgå begränsningar finns det några saker du kan prova. Finns det några andra IP-adresser eller servrar som är värd för samma data men inte har samma begränsningar? Finns det några API-slutpunkter som inte har begränsningar, medan andra har det? Vid vilken nedladdningshastighet blir din IP blockerad, och hur länge? Eller blir du inte blockerad utan nedströpt? Vad händer om du skapar ett användarkonto, hur förändras saker då? Kan du använda HTTP/2 för att hålla anslutningar öppna, och ökar det hastigheten med vilken du kan begära sidor? Finns det sidor som listar flera filer på en gång, och är informationen som listas där tillräcklig?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Saker du förmodligen vill spara inkluderar:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Filnamn / plats"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kan vara något internt ID, men ID:n som ISBN eller DOI är också användbara."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Storlek: för att beräkna hur mycket diskutrymme du behöver."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): för att bekräfta att du laddade ner filen korrekt."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum tillagt/ändrat: så att du kan komma tillbaka senare och ladda ner filer som du inte laddade ner tidigare (även om du ofta också kan använda ID eller hash för detta)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beskrivning, kategori, taggar, författare, språk, etc."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Vi gör vanligtvis detta i två steg. Först laddar vi ner de råa HTML-filerna, vanligtvis direkt in i MySQL (för att undvika massor av små filer, vilket vi pratar mer om nedan). Sedan, i ett separat steg, går vi igenom dessa HTML-filer och analyserar dem till faktiska MySQL-tabeller. På detta sätt behöver du inte ladda ner allt från början om du upptäcker ett misstag i din analyskod, eftersom du bara kan bearbeta HTML-filerna med den nya koden. Det är också ofta lättare att parallellisera bearbetningssteget, vilket sparar tid (och du kan skriva bearbetningskoden medan skrapningen körs, istället för att behöva skriva båda stegen på en gång)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Slutligen, notera att för vissa mål är metadata-scraping allt som finns. Det finns några enorma metadata-samlingar där ute som inte är ordentligt bevarade."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Dataval"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Ofta kan du använda metadata för att lista ut en rimlig delmängd av data att ladda ner. Även om du till slut vill ladda ner all data, kan det vara användbart att prioritera de viktigaste objekten först, ifall du blir upptäckt och försvar förbättras, eller för att du skulle behöva köpa fler diskar, eller helt enkelt för att något annat dyker upp i ditt liv innan du kan ladda ner allt."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Till exempel kan en samling ha flera utgåvor av samma underliggande resurs (som en bok eller en film), där en är markerad som den bästa kvaliteten. Att spara dessa utgåvor först skulle vara mycket logiskt. Du kanske till slut vill spara alla utgåvor, eftersom metadata i vissa fall kan vara felaktigt taggad, eller det kan finnas okända kompromisser mellan utgåvor (till exempel kan \"bästa utgåvan\" vara bäst på de flesta sätt men sämre på andra sätt, som en film med högre upplösning men utan undertexter)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Du kan också söka i din metadata-databas för att hitta intressanta saker. Vad är den största filen som är värd, och varför är den så stor? Vad är den minsta filen? Finns det intressanta eller oväntade mönster när det gäller vissa kategorier, språk och så vidare? Finns det dubbletter eller mycket liknande titlar? Finns det mönster för när data lades till, som en dag då många filer lades till samtidigt? Du kan ofta lära dig mycket genom att titta på datasetet på olika sätt."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "I vårt fall deduplicerade vi Z-Library-böcker mot md5-hasharna i Library Genesis, vilket sparade mycket nedladdningstid och diskutrymme. Detta är dock en ganska unik situation. I de flesta fall finns det inga omfattande databaser över vilka filer som redan är ordentligt bevarade av andra pirater. Detta i sig är en stor möjlighet för någon där ute. Det skulle vara fantastiskt att ha en regelbundet uppdaterad översikt över saker som musik och filmer som redan är allmänt seedade på torrentsajter, och därför är lägre prioritet att inkludera i piratspeglar."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Data-scraping"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nu är du redo att faktiskt ladda ner data i bulk. Som nämnts tidigare, vid denna tidpunkt bör du redan manuellt ha laddat ner en mängd filer, för att bättre förstå beteendet och begränsningarna hos målet. Men det kommer fortfarande att finnas överraskningar i beredskap för dig när du faktiskt börjar ladda ner många filer på en gång."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Vårt råd här är främst att hålla det enkelt. Börja med att bara ladda ner en mängd filer. Du kan använda Python och sedan expandera till flera trådar. Men ibland är det ännu enklare att generera Bash-filer direkt från databasen och sedan köra flera av dem i flera terminalfönster för att skala upp. Ett snabbt tekniskt trick värt att nämna här är att använda OUTFILE i MySQL, vilket du kan skriva var som helst om du inaktiverar \"secure_file_priv\" i mysqld.cnf (och se till att också inaktivera/överstyra AppArmor om du använder Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Vi lagrar data på enkla hårddiskar. Börja med vad du har och expandera långsamt. Det kan vara överväldigande att tänka på att lagra hundratals TB data. Om det är situationen du står inför, sätt bara ut en bra delmängd först, och i ditt tillkännagivande be om hjälp med att lagra resten. Om du vill skaffa fler hårddiskar själv, har r/DataHoarder några bra resurser för att få bra erbjudanden."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Försök att inte oroa dig för mycket över avancerade filsystem. Det är lätt att falla ner i kaninhålet av att sätta upp saker som ZFS. En teknisk detalj att vara medveten om är dock att många filsystem inte hanterar många filer bra. Vi har funnit att en enkel lösning är att skapa flera kataloger, t.ex. för olika ID-intervall eller hash-prefix."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Efter att ha laddat ner data, se till att kontrollera filernas integritet med hjälp av hashvärden i metadata, om tillgängligt."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribution"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Du har datan, vilket ger dig världens första piratspegel av ditt mål (troligtvis). På många sätt är den svåraste delen över, men den mest riskfyllda delen ligger fortfarande framför dig. Trots allt har du hittills varit diskret; flugit under radarn. Allt du behövde göra var att använda en bra VPN hela tiden, inte fylla i dina personliga uppgifter i några formulär (självklart), och kanske använda en speciell webbläsarsession (eller till och med en annan dator)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nu måste du distribuera datan. I vårt fall ville vi först bidra med böckerna tillbaka till Library Genesis, men upptäckte snabbt svårigheterna med det (skönlitteratur vs facklitteratur sortering). Så vi bestämde oss för distribution med hjälp av Library Genesis-stil torrents. Om du har möjlighet att bidra till ett befintligt projekt, kan det spara dig mycket tid. Det finns dock inte många välorganiserade piratspeglar där ute för närvarande."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Så låt oss säga att du bestämmer dig för att distribuera torrents själv. Försök att hålla dessa filer små, så att de är lätta att spegla på andra webbplatser. Du måste sedan seeda torrents själv, samtidigt som du förblir anonym. Du kan använda en VPN (med eller utan port forwarding), eller betala med tvättade Bitcoins för en Seedbox. Om du inte vet vad några av dessa termer betyder, har du en del läsning att göra, eftersom det är viktigt att du förstår riskavvägningarna här."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Du kan hosta torrentfilerna själva på befintliga torrentsajter. I vårt fall valde vi att faktiskt hosta en webbplats, eftersom vi också ville sprida vår filosofi på ett tydligt sätt. Du kan göra detta själv på ett liknande sätt (vi använder Njalla för våra domäner och hosting, betalat med tvättade Bitcoins), men tveka inte att kontakta oss för att låta oss hosta dina torrents. Vi strävar efter att bygga ett omfattande index över piratspeglar över tid, om denna idé får fäste."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "När det gäller VPN-val har mycket redan skrivits om detta, så vi upprepar bara det allmänna rådet att välja efter rykte. Faktiska domstolstestade no-log-policies med långa spår av att skydda integritet är det lägsta riskalternativet, enligt vår mening. Notera att även när du gör allt rätt, kan du aldrig nå noll risk. Till exempel, när du seedar dina torrents, kan en mycket motiverad statlig aktör troligen titta på inkommande och utgående dataflöden för VPN-servrar och dra slutsatser om vem du är. Eller så kan du helt enkelt göra ett misstag. Vi har förmodligen redan gjort det, och kommer att göra det igen. Lyckligtvis bryr sig inte stater <em>så</em> mycket om piratkopiering."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Ett beslut att fatta för varje projekt är om du ska publicera det med samma identitet som tidigare eller inte. Om du fortsätter att använda samma namn, kan misstag i operativ säkerhet från tidigare projekt komma tillbaka och bita dig. Men att publicera under olika namn innebär att du inte bygger ett mer varaktigt rykte. Vi valde att ha stark operativ säkerhet från början så att vi kan fortsätta använda samma identitet, men vi tvekar inte att publicera under ett annat namn om vi gör ett misstag eller om omständigheterna kräver det."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Att sprida ordet kan vara knepigt. Som vi sa, detta är fortfarande en nischad gemenskap. Vi postade ursprungligen på Reddit, men fick verkligen genomslag på Hacker News. För närvarande är vår rekommendation att posta det på några ställen och se vad som händer. Och återigen, kontakta oss. Vi skulle älska att sprida ordet om fler piratarkivinsatser."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Slutsats"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Förhoppningsvis är detta till hjälp för nyblivna piratarkivarier. Vi är glada att välkomna dig till denna värld, så tveka inte att höra av dig. Låt oss bevara så mycket av världens kunskap och kultur som vi kan, och spegla det vida och brett."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Vi presenterar Pirate Library Mirror: Bevarar 7TB av böcker (som inte finns i Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Detta projekt (REDIGERAT: flyttat till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) syftar till att bidra till bevarandet och befrielsen av mänsklig kunskap. Vi gör vårt lilla och ödmjuka bidrag, i fotspåren av de stora före oss."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Fokus för detta projekt illustreras av dess namn:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Pirat</strong> - Vi bryter medvetet mot upphovsrättslagen i de flesta länder. Detta gör att vi kan göra något som juridiska enheter inte kan göra: se till att böcker speglas vida och brett."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibliotek</strong> - Likt de flesta bibliotek fokuserar vi främst på skriftligt material som böcker. Vi kanske expanderar till andra typer av media i framtiden."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spegel</strong> - Vi är strikt en spegel av befintliga bibliotek. Vi fokuserar på bevarande, inte på att göra böcker lättsökbara och nedladdningsbara (åtkomst) eller att främja en stor gemenskap av människor som bidrar med nya böcker (källor)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Det första biblioteket vi har speglat är Z-Library. Detta är ett populärt (och olagligt) bibliotek. De har tagit Library Genesis-samlingen och gjort den lättsökbar. Dessutom har de blivit mycket effektiva på att uppmuntra nya bokbidrag genom att belöna bidragande användare med olika förmåner. De bidrar för närvarande inte med dessa nya böcker tillbaka till Library Genesis. Och till skillnad från Library Genesis gör de inte sin samling lätt spegelbar, vilket förhindrar brett bevarande. Detta är viktigt för deras affärsmodell, eftersom de tar betalt för att få tillgång till deras samling i bulk (mer än 10 böcker per dag)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Vi gör inga moraliska bedömningar om att ta betalt för massåtkomst till en illegal boksamling. Det råder ingen tvekan om att Z-Library har varit framgångsrik i att utöka tillgången till kunskap och att skaffa fler böcker. Vi är helt enkelt här för att göra vår del: att säkerställa det långsiktiga bevarandet av denna privata samling."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Vi vill bjuda in dig att hjälpa till att bevara och befria mänsklig kunskap genom att ladda ner och seeda våra torrents. Se projektsidan för mer information om hur datan är organiserad."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Vi vill också gärna bjuda in dig att bidra med dina idéer om vilka samlingar som ska speglas härnäst, och hur vi ska gå tillväga. Tillsammans kan vi åstadkomma mycket. Detta är bara ett litet bidrag bland otaliga andra. Tack för allt du gör."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Vi länkar inte till filerna från denna blogg. Vänligen hitta dem själv.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb-dump, eller Hur Många Böcker Är Bevarade För Alltid?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Om vi skulle deduplicera filerna från skuggbibliotek ordentligt, vilken procentandel av alla böcker i världen har vi bevarat?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Med Pirate Library Mirror (REDIGERAT: flyttat till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>) är vårt mål att ta alla böcker i världen och bevara dem för alltid.<sup>1</sup> Mellan våra Z-Library-torrents och de ursprungliga Library Genesis-torrents har vi 11 783 153 filer. Men hur många är det egentligen? Om vi deduplicerade dessa filer ordentligt, vilken procentandel av alla böcker i världen har vi bevarat? Vi skulle verkligen vilja ha något som detta:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of mänsklighetens skrivna arv bevarat för alltid"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "För en procentandel behöver vi en nämnare: det totala antalet böcker som någonsin publicerats.<sup>2</sup> Innan Google Books upphörde, försökte en ingenjör på projektet, Leonid Taycher, <a %(booksearch_blogspot)s>att uppskatta</a> detta antal. Han kom fram till — med glimten i ögat — 129 864 880 (\"åtminstone fram till söndag\"). Han uppskattade detta antal genom att bygga en enhetlig databas över alla böcker i världen. För detta sammanställde han olika datasets och sammanfogade dem på olika sätt."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Som en snabb parentes finns det en annan person som försökte katalogisera alla böcker i världen: Aaron Swartz, den avlidne digitala aktivisten och Reddit-medgrundaren.<sup>3</sup> Han <a %(youtube)s>startade Open Library</a> med målet att skapa ”en webbsida för varje bok som någonsin publicerats”, genom att kombinera data från många olika källor. Han fick betala det yttersta priset för sitt arbete med digital bevarande när han åtalades för massnedladdning av akademiska artiklar, vilket ledde till hans självmord. Det är onödigt att säga att detta är en av anledningarna till att vår grupp är pseudonym och varför vi är mycket försiktiga. Open Library drivs fortfarande heroiskt av folk på Internet Archive, som fortsätter Aarons arv. Vi kommer tillbaka till detta senare i detta inlägg."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "I Googles blogginlägg beskriver Taycher några av utmaningarna med att uppskatta detta antal. Först, vad utgör en bok? Det finns några möjliga definitioner:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fysiska kopior.</strong> Självklart är detta inte särskilt hjälpsamt, eftersom de bara är dubbletter av samma material. Det skulle vara häftigt om vi kunde bevara alla anteckningar som folk gör i böcker, som Fermats berömda ”klotter i marginalerna”. Men tyvärr kommer det att förbli en arkivists dröm."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>”Verk”.</strong> Till exempel ”Harry Potter och Hemligheternas kammare” som ett logiskt koncept, som omfattar alla versioner av den, som olika översättningar och nytryck. Detta är en slags användbar definition, men det kan vara svårt att dra gränsen för vad som räknas. Till exempel vill vi förmodligen bevara olika översättningar, även om nytryck med bara mindre skillnader kanske inte är lika viktiga."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>”Upplagor”.</strong> Här räknar du varje unik version av en bok. Om något är annorlunda, som ett annat omslag eller ett annat förord, räknas det som en annan upplaga."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Filer.</strong> När man arbetar med skuggbibliotek som Library Genesis, Sci-Hub eller Z-Library finns det en ytterligare övervägning. Det kan finnas flera skanningar av samma upplaga. Och folk kan göra bättre versioner av befintliga filer, genom att skanna texten med OCR, eller rätta till sidor som skannats i vinkel. Vi vill bara räkna dessa filer som en upplaga, vilket skulle kräva bra metadata, eller deduplicering med hjälp av dokumentlikhetsmått."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "”Upplagor” verkar vara den mest praktiska definitionen av vad ”böcker” är. Bekvämt nog används denna definition också för att tilldela unika ISBN-nummer. Ett ISBN, eller International Standard Book Number, används ofta för internationell handel, eftersom det är integrerat med det internationella streckkodssystemet (”International Article Number”). Om du vill sälja en bok i butiker behöver den en streckkod, så du får ett ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taychers blogginlägg nämner att även om ISBN är användbara, är de inte universella, eftersom de bara verkligen antogs i mitten av sjuttiotalet, och inte överallt i världen. Ändå är ISBN förmodligen den mest använda identifieraren av bokupplagor, så det är vår bästa utgångspunkt. Om vi kan hitta alla ISBN i världen får vi en användbar lista över vilka böcker som fortfarande behöver bevaras."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Så, var får vi datan ifrån? Det finns ett antal befintliga insatser som försöker sammanställa en lista över alla böcker i världen:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Trots allt gjorde de denna forskning för Google Books. Men deras metadata är inte tillgänglig i bulk och ganska svår att skrapa."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Som nämnts tidigare är detta deras hela uppdrag. De har hämtat enorma mängder biblioteksdata från samarbetande bibliotek och nationella arkiv, och fortsätter att göra det. De har också frivilliga bibliotekarier och ett tekniskt team som försöker deduplicera poster och märka dem med alla möjliga metadata. Bäst av allt, deras dataset är helt öppet. Du kan enkelt <a %(openlibrary)s>ladda ner det</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Detta är en webbplats som drivs av den ideella organisationen OCLC, som säljer bibliotekshanteringssystem. De samlar in bokmetadata från många bibliotek och gör det tillgängligt via WorldCat-webbplatsen. Men de tjänar också pengar på att sälja denna data, så den är inte tillgänglig för bulk-nedladdning. De har dock några mer begränsade bulk-datasets tillgängliga för nedladdning, i samarbete med specifika bibliotek."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Detta är ämnet för detta blogginlägg. ISBNdb skrapar olika webbplatser för bokmetadata, särskilt prisdata, som de sedan säljer till bokhandlare, så att de kan prissätta sina böcker i enlighet med resten av marknaden. Eftersom ISBN är ganska universella nuförtiden har de effektivt byggt en ”webbsida för varje bok”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Olika individuella bibliotekssystem och arkiv.</strong> Det finns bibliotek och arkiv som inte har indexerats och aggregerats av någon av de ovanstående, ofta för att de är underfinansierade, eller av andra skäl inte vill dela sin data med Open Library, OCLC, Google, och så vidare. Många av dessa har digitala poster tillgängliga via internet, och de är ofta inte särskilt väl skyddade, så om du vill hjälpa till och ha lite kul med att lära dig om konstiga bibliotekssystem, är dessa bra utgångspunkter."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "I detta inlägg är vi glada att tillkännage en liten release (jämfört med våra tidigare Z-Library-releaser). Vi skrapade det mesta av ISBNdb och gjorde datan tillgänglig för torrenting på Pirate Library Mirrors webbplats (EDIT: flyttad till <a %(wikipedia_annas_archive)s>Annas Arkiv</a>; vi kommer inte att länka den här direkt, bara sök efter den). Dessa är cirka 30,9 miljoner poster (20GB som <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzippad). På deras webbplats hävdar de att de faktiskt har 32,6 miljoner poster, så vi kan på något sätt ha missat några, eller <em>de</em> kan göra något fel. I vilket fall som helst, för nu kommer vi inte att dela exakt hur vi gjorde det — vi lämnar det som en övning för läsaren. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Vad vi kommer att dela är en preliminär analys, för att försöka komma närmare en uppskattning av antalet böcker i världen. Vi tittade på tre datasets: denna nya ISBNdb-dataset, vår ursprungliga release av metadata som vi skrapade från Z-Library-skuggbiblioteket (som inkluderar Library Genesis), och Open Library-datadumpen."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Låt oss börja med några grova siffror:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "I både Z-Library/Libgen och Open Library finns det många fler böcker än unika ISBN. Betyder det att många av dessa böcker inte har ISBN, eller saknas ISBN-metadata helt enkelt? Vi kan förmodligen besvara denna fråga med en kombination av automatiserad matchning baserad på andra attribut (titel, författare, förlag, etc), genom att hämta in fler datakällor och extrahera ISBN från de faktiska bokskanningarna själva (i fallet med Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Hur många av dessa ISBN är unika? Detta illustreras bäst med ett Venn-diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "För att vara mer exakt:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Vi blev förvånade över hur lite överlapp det finns! ISBNdb har en enorm mängd ISBN som inte dyker upp i vare sig Z-Library eller Open Library, och detsamma gäller (i mindre men fortfarande betydande grad) för de andra två. Detta väcker många nya frågor. Hur mycket skulle automatiserad matchning hjälpa till med att tagga böcker som inte var taggade med ISBN? Skulle det finnas många matchningar och därmed ökat överlapp? Och vad skulle hända om vi tar in en fjärde eller femte dataset? Hur mycket överlapp skulle vi se då?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Detta ger oss en startpunkt. Vi kan nu titta på alla ISBN som inte fanns i Z-Library-datasetet, och som inte heller matchar titel-/författarfält. Det kan ge oss en möjlighet att bevara alla böcker i världen: först genom att skrapa internet efter skanningar, sedan genom att gå ut i verkliga livet för att skanna böcker. Det senare skulle till och med kunna finansieras av allmänheten, eller drivas av \"belöningar\" från personer som skulle vilja se specifika böcker digitaliserade. Allt detta är en historia för en annan gång."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Om du vill hjälpa till med något av detta — vidare analys; skrapa mer metadata; hitta fler böcker; OCR:a böcker; göra detta för andra områden (t.ex. artiklar, ljudböcker, filmer, tv-program, tidskrifter) eller till och med göra en del av dessa data tillgängliga för saker som ML / träning av stora språkmodeller — vänligen kontakta mig (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Om du är särskilt intresserad av dataanalys, arbetar vi med att göra våra datasets och skript tillgängliga i ett mer användarvänligt format. Det skulle vara fantastiskt om du bara kunde forka en anteckningsbok och börja experimentera med detta."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Slutligen, om du vill stödja detta arbete, överväg att göra en donation. Detta är en helt frivillig verksamhet, och ditt bidrag gör en enorm skillnad. Varje liten bit hjälper. För närvarande tar vi emot donationer i krypto; se donationssidan på Annas Arkiv."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. För någon rimlig definition av \"för alltid\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Självklart är mänsklighetens skriftliga arv mycket mer än böcker, särskilt nuförtiden. För denna posts och våra senaste släpps skull fokuserar vi på böcker, men våra intressen sträcker sig längre."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Det finns mycket mer att säga om Aaron Swartz, men vi ville bara nämna honom kort, eftersom han spelar en avgörande roll i denna berättelse. Med tiden kan fler människor stöta på hans namn för första gången och sedan själva dyka ner i kaninhålet."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Det kritiska fönstret för skuggbibliotek"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Hur kan vi påstå att vi bevarar våra samlingar för evigt, när de redan närmar sig 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Kinesisk version 中文版</a>, diskutera på <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "På Annas Arkiv får vi ofta frågan hur vi kan påstå att vi bevarar våra samlingar för evigt, när den totala storleken redan närmar sig 1 Petabyte (1000 TB) och fortfarande växer. I denna artikel kommer vi att titta på vår filosofi och se varför det kommande decenniet är avgörande för vårt uppdrag att bevara mänsklighetens kunskap och kultur."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Den <a %(annas_archive_stats)s>totala storleken</a> på våra samlingar, under de senaste månaderna, uppdelad efter antal torrent-seedare."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioriteringar"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Varför bryr vi oss så mycket om artiklar och böcker? Låt oss lägga åt sidan vår grundläggande tro på bevarande i allmänhet — vi kanske skriver ett annat inlägg om det. Så varför artiklar och böcker specifikt? Svaret är enkelt: <strong>informationsdensitet</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte lagring lagrar skriven text mest information av alla medier. Medan vi bryr oss om både kunskap och kultur, bryr vi oss mer om det förstnämnda. Sammantaget finner vi en hierarki av informationsdensitet och vikten av bevarande som ser ungefär ut så här:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademiska artiklar, tidskrifter, rapporter"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organiska data som DNA-sekvenser, växtfrön eller mikrobiella prover"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Facklitteratur"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Vetenskaplig och teknisk programvarukod"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Mätdata som vetenskapliga mätningar, ekonomiska data, företagsrapporter"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Vetenskapliga och tekniska webbplatser, online-diskussioner"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Facktidningar, tidningar, manualer"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Facktranskriptioner av föredrag, dokumentärer, podcaster"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Interna data från företag eller regeringar (läckor)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata-poster generellt (av fack- och skönlitteratur; av andra medier, konst, personer, etc; inklusive recensioner)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografiska data (t.ex. kartor, geologiska undersökningar)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkriptioner av juridiska eller rättsliga förfaranden"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiktiva eller underhållningsversioner av allt ovanstående"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Rankningen i denna lista är något godtycklig — flera punkter är oavgjorda eller har oenigheter inom vårt team — och vi glömmer förmodligen några viktiga kategorier. Men det här är ungefär hur vi prioriterar."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Några av dessa punkter är för olika från de andra för att vi ska oroa oss (eller tas redan om hand av andra institutioner), såsom organiska data eller geografiska data. Men de flesta av punkterna i denna lista är faktiskt viktiga för oss."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "En annan stor faktor i vår prioritering är hur mycket risk en viss verk är. Vi föredrar att fokusera på verk som är:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Sällsynta"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unikt underfokuserade"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unikt i riskzonen för förstörelse (t.ex. genom krig, nedskärningar i finansiering, rättstvister eller politisk förföljelse)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Slutligen bryr vi oss om skala. Vi har begränsad tid och pengar, så vi spenderar hellre en månad på att rädda 10 000 böcker än 1 000 böcker — om de är ungefär lika värdefulla och i riskzonen."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Skuggbibliotek"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Det finns många organisationer som har liknande uppdrag och liknande prioriteringar. Faktum är att det finns bibliotek, arkiv, laboratorier, museer och andra institutioner som har i uppdrag att bevara av detta slag. Många av dessa är välfinansierade, av regeringar, individer eller företag. Men de har en massiv blind fläck: rättssystemet."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Här ligger den unika rollen för skuggbibliotek, och anledningen till att Annas Arkiv finns. Vi kan göra saker som andra institutioner inte får göra. Nu är det inte (ofta) så att vi kan arkivera material som är olagliga att bevara någon annanstans. Nej, det är lagligt på många platser att bygga ett arkiv med vilka böcker, papper, tidningar och så vidare."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Men vad juridiska arkiv ofta saknar är <strong>redundans och långvarighet</strong>. Det finns böcker av vilka endast ett exemplar existerar i något fysiskt bibliotek någonstans. Det finns metadataregister som skyddas av ett enda företag. Det finns tidningar som endast bevaras på mikrofilm i ett enda arkiv. Bibliotek kan få budgetnedskärningar, företag kan gå i konkurs, arkiv kan bombas och brännas ner till grunden. Detta är inte hypotetiskt — det händer hela tiden."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Det vi unikt kan göra på Annas Arkiv är att lagra många kopior av verk, i stor skala. Vi kan samla in artiklar, böcker, tidskrifter och mer, och distribuera dem i bulk. Vi gör detta för närvarande genom torrents, men de exakta teknologierna spelar ingen roll och kommer att förändras över tid. Det viktiga är att få många kopior distribuerade över hela världen. Detta citat från över 200 år sedan är fortfarande aktuellt:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Det förlorade kan inte återfås; men låt oss rädda det som återstår: inte genom valv och lås som skyddar dem från allmänhetens ögon och användning, genom att överlämna dem till tidens avfall, utan genom en sådan mångfaldigande av kopior, som ska placera dem bortom olyckans räckvidd.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "En snabb notering om public domain. Eftersom Annas Arkiv unikt fokuserar på aktiviteter som är olagliga på många platser runt om i världen, bryr vi oss inte om allmänt tillgängliga samlingar, såsom public domain-böcker. Juridiska enheter tar ofta redan väl hand om det. Det finns dock överväganden som gör att vi ibland arbetar med offentligt tillgängliga samlingar:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadataregister kan fritt ses på Worldcat-webbplatsen, men inte laddas ner i bulk (tills vi <a %(worldcat_scrape)s>skrapade</a> dem)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kod kan vara öppen källkod på Github, men Github som helhet kan inte enkelt speglas och därmed bevaras (även om det i detta specifika fall finns tillräckligt distribuerade kopior av de flesta kodförråd)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit är gratis att använda, men har nyligen infört strikta anti-scraping-åtgärder, i kölvattnet av datahungrig LLM-träning (mer om det senare)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "En mångfaldigande av kopior"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Tillbaka till vår ursprungliga fråga: hur kan vi påstå att vi bevarar våra samlingar för evigt? Huvudproblemet här är att vår samling har <a %(torrents_stats)s>vuxit</a> snabbt, genom att skrapa och öppna några massiva samlingar (utöver det fantastiska arbete som redan gjorts av andra öppna data-skuggbibliotek som Sci-Hub och Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Denna tillväxt i data gör det svårare för samlingarna att speglas runt om i världen. Datastorage är dyrt! Men vi är optimistiska, särskilt när vi observerar följande tre trender."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Vi har plockat de lågt hängande frukterna"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Detta följer direkt från våra prioriteringar som diskuterats ovan. Vi föredrar att arbeta med att befria stora samlingar först. Nu när vi har säkrat några av de största samlingarna i världen, förväntar vi oss att vår tillväxt kommer att vara mycket långsammare."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Det finns fortfarande en lång svans av mindre samlingar, och nya böcker skannas eller publiceras varje dag, men takten kommer sannolikt att vara mycket långsammare. Vi kanske fortfarande fördubblar eller till och med tredubblar i storlek, men över en längre tidsperiod."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Lagringskostnaderna fortsätter att sjunka exponentiellt"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Vid tidpunkten för skrivandet är <a %(diskprices)s>diskpriser</a> per TB cirka $12 för nya diskar, $8 för begagnade diskar och $4 för band. Om vi är konservativa och bara tittar på nya diskar, betyder det att lagra en petabyte kostar cirka $12,000. Om vi antar att vårt bibliotek kommer att tredubblas från 900TB till 2,7PB, skulle det innebära $32,400 för att spegla hela vårt bibliotek. Lägger vi till el, kostnad för annan hårdvara och så vidare, låt oss runda upp det till $40,000. Eller med band mer som $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Å ena sidan är <strong>$15,000–$40,000 för summan av all mänsklig kunskap ett kap</strong>. Å andra sidan är det lite brant att förvänta sig massor av fullständiga kopior, särskilt om vi också vill att dessa personer ska fortsätta seeda sina torrents till förmån för andra."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Det är idag. Men framstegen går framåt:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Hårddiskkostnader per TB har ungefär delats i tredjedelar under de senaste 10 åren, och kommer sannolikt att fortsätta sjunka i liknande takt. Band verkar vara på en liknande bana. SSD-priserna sjunker ännu snabbare och kan ta över HDD-priserna i slutet av decenniet."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-pristrender från olika källor (klicka för att se studien)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Om detta håller, kan vi om 10 år kanske se på endast $5,000–$13,000 för att spegla hela vår samling (1/3), eller ännu mindre om vi växer mindre i storlek. Även om det fortfarande är mycket pengar, kommer detta att vara möjligt för många människor. Och det kan bli ännu bättre på grund av nästa punkt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Förbättringar i informationsdensitet"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Vi lagrar för närvarande böcker i de råformat som de ges till oss. Visst, de är komprimerade, men ofta är de fortfarande stora skanningar eller fotografier av sidor."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Fram tills nu har de enda alternativen för att minska den totala storleken på vår samling varit genom mer aggressiv komprimering eller deduplicering. Men för att få tillräckligt stora besparingar är båda för förlustfyllda för vår smak. Kraftig komprimering av foton kan göra texten knappt läsbar. Och deduplicering kräver hög säkerhet om att böckerna är exakt desamma, vilket ofta är för osäkert, särskilt om innehållet är detsamma men skanningarna är gjorda vid olika tillfällen."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Det har alltid funnits ett tredje alternativ, men dess kvalitet har varit så usel att vi aldrig övervägt det: <strong>OCR, eller optisk teckenigenkänning</strong>. Detta är processen att konvertera foton till vanlig text genom att använda AI för att identifiera tecknen i fotona. Verktyg för detta har funnits länge och har varit ganska bra, men \"ganska bra\" är inte tillräckligt för bevarandeändamål."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Men nyligen har multimodala djupinlärningsmodeller gjort extremt snabba framsteg, även om de fortfarande är kostsamma. Vi förväntar oss att både noggrannhet och kostnader kommer att förbättras dramatiskt under de kommande åren, till den punkt där det blir realistiskt att tillämpa på hela vårt bibliotek."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Förbättringar av OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "När det händer kommer vi troligen fortfarande att bevara de ursprungliga filerna, men dessutom kan vi ha en mycket mindre version av vårt bibliotek som de flesta kommer att vilja spegla. Poängen är att råtext i sig komprimeras ännu bättre och är mycket lättare att deduplicera, vilket ger oss ännu fler besparingar."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Sammantaget är det inte orealistiskt att förvänta sig minst en 5-10x minskning av den totala filstorleken, kanske ännu mer. Även med en konservativ 5x minskning skulle vi titta på <strong>$1,000–$3,000 om 10 år även om vårt bibliotek tredubblas i storlek</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritiskt fönster"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Om dessa prognoser är korrekta, behöver vi <strong>bara vänta ett par år</strong> innan hela vår samling kommer att speglas brett. Således, med Thomas Jeffersons ord, \"placerad bortom olyckans räckhåll.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Tyvärr har framväxten av LLM:er och deras datahungriga träning satt många upphovsrättsinnehavare på defensiven. Ännu mer än de redan var. Många webbplatser gör det svårare att skrapa och arkivera, stämningar flyger runt, och samtidigt fortsätter fysiska bibliotek och arkiv att försummas."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Vi kan bara förvänta oss att dessa trender fortsätter att förvärras, och många verk kommer att gå förlorade långt innan de går in i det offentliga området."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Vi står på tröskeln till en revolution inom bevarande, men <q>det förlorade kan inte återvinnas.</q></strong> Vi har ett kritiskt fönster på cirka 5-10 år under vilket det fortfarande är ganska dyrt att driva ett skuggbibliotek och skapa många speglar runt om i världen, och under vilket tillgången ännu inte har stängts helt."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Om vi kan överbrygga detta fönster, så kommer vi verkligen ha bevarat mänsklighetens kunskap och kultur för evigt. Vi bör inte låta denna tid gå till spillo. Vi bör inte låta detta kritiska fönster stängas för oss."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Låt oss sätta igång."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Exklusiv tillgång för LLM-företag till världens största kinesiska facklitteratursamling"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Kinesisk version 中文版</a>, <a %(news_ycombinator)s>Diskutera på Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Annas Arkiv förvärvade en unik samling av 7,5 miljoner / 350TB kinesiska facklitteraturböcker — större än Library Genesis. Vi är villiga att ge ett LLM-företag exklusiv tillgång, i utbyte mot högkvalitativ OCR och textutvinning.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Detta är ett kort blogginlägg. Vi letar efter ett företag eller en institution som kan hjälpa oss med OCR och textutvinning för en massiv samling vi förvärvat, i utbyte mot exklusiv tidig tillgång. Efter embargoperioden kommer vi naturligtvis att släppa hela samlingen."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Akademiska texter av hög kvalitet är extremt användbara för träning av LLM. Även om vår samling är kinesisk, bör den vara användbar för träning av engelska LLM:er, eftersom modeller verkar koda koncept och kunskap oavsett källspråk."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "För detta behöver texten extraheras från skanningarna. Vad får Annas Arkiv ut av det? Fulltextsökning av böckerna för sina användare."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Eftersom våra mål överensstämmer med LLM-utvecklares, letar vi efter en samarbetspartner. Vi är villiga att ge dig <strong>exklusiv tidig tillgång till denna samling i bulk under 1 år</strong>, om du kan utföra korrekt OCR och textextraktion. Om du är villig att dela hela koden för din pipeline med oss, skulle vi vara villiga att embargera samlingen längre."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Exempelsidor"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "För att bevisa för oss att du har en bra pipeline, här är några exempelsidor att börja med, från en bok om supraledare. Din pipeline bör hantera matematik, tabeller, diagram, fotnoter och så vidare korrekt."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Skicka dina bearbetade sidor till vår e-post. Om de ser bra ut, kommer vi att skicka fler till dig privat, och vi förväntar oss att du snabbt kan köra din pipeline på dem också. När vi är nöjda kan vi göra en överenskommelse."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Samling"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Lite mer information om samlingen. <a %(duxiu)s>Duxiu</a> är en massiv databas med skannade böcker, skapad av <a %(chaoxing)s>SuperStar Digital Library Group</a>. De flesta är akademiska böcker, skannade för att göra dem tillgängliga digitalt för universitet och bibliotek. För vår engelsktalande publik har <a %(library_princeton)s>Princeton</a> och <a %(guides_lib_uw)s>University of Washington</a> bra översikter. Det finns också en utmärkt artikel som ger mer bakgrund: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (sök upp den i Annas Arkiv)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Böckerna från Duxiu har länge piratkopierats på det kinesiska internet. Vanligtvis säljs de för mindre än en dollar av återförsäljare. De distribueras vanligtvis med den kinesiska motsvarigheten till Google Drive, som ofta har hackats för att tillåta mer lagringsutrymme. Några tekniska detaljer kan hittas <a %(github_duty_machine)s>här</a> och <a %(github_821_github_io)s>här</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Även om böckerna har distribuerats halv-offentligt, är det ganska svårt att få tag på dem i bulk. Vi hade detta högt på vår TODO-lista och avsatte flera månader av heltidsarbete för det. Men nyligen kontaktade en otrolig, fantastisk och talangfull volontär oss och berättade att de redan hade gjort allt detta arbete — till stor kostnad. De delade hela samlingen med oss, utan att förvänta sig något i gengäld, förutom garantin om långsiktig bevarande. Verkligen anmärkningsvärt. De gick med på att be om hjälp på detta sätt för att få samlingen OCR:ad."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Samlingen består av 7 543 702 filer. Detta är mer än Library Genesis facklitteratur (cirka 5,3 miljoner). Den totala filstorleken är cirka 359TB (326TiB) i dess nuvarande form."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Vi är öppna för andra förslag och idéer. Kontakta oss bara. Kolla in Annas Arkiv för mer information om våra samlingar, bevarandeinsatser och hur du kan hjälpa till. Tack!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Varning: detta blogginlägg har blivit föråldrat. Vi har beslutat att IPFS ännu inte är redo för prime time. Vi kommer fortfarande att länka till filer på IPFS från Annas Arkiv när det är möjligt, men vi kommer inte längre att vara värd för det själva, och vi rekommenderar inte andra att spegla med IPFS. Se vår Torrents-sida om du vill hjälpa till att bevara vår samling."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Hjälp till att seeda Z-Library på IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Hur man driver ett skuggbibliotek: verksamhet på Annas Arkiv"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Det finns ingen <q>AWS för skuggbibliotek,</q> så hur driver vi Annas Arkiv?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Jag driver <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, världens största open-source ideella sökmotor för <a %(wikipedia_shadow_library)s>skuggbibliotek</a>, som Sci-Hub, Library Genesis och Z-Library. Vårt mål är att göra kunskap och kultur lättillgänglig, och i slutändan bygga en gemenskap av människor som tillsammans arkiverar och bevarar <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alla böcker i världen</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "I denna artikel kommer jag att visa hur vi driver denna webbplats, och de unika utmaningar som följer med att driva en webbplats med tveksam juridisk status, eftersom det inte finns någon “AWS för skuggbibliotek”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Kolla också in systerartikeln <a %(blog_how_to_become_a_pirate_archivist)s>Hur man blir en piratarkivarie</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovationstokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Låt oss börja med vår teknikstack. Den är medvetet tråkig. Vi använder Flask, MariaDB och ElasticSearch. Det är bokstavligen allt. Sökning är i stort sett ett löst problem, och vi har inte för avsikt att uppfinna det på nytt. Dessutom måste vi spendera våra <a %(mcfunley)s>innovationstokens</a> på något annat: att inte bli nedstängda av myndigheterna."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Så hur laglig eller olaglig är egentligen Annas Arkiv? Det beror mest på den juridiska jurisdiktionen. De flesta länder tror på någon form av upphovsrätt, vilket innebär att personer eller företag tilldelas ett exklusivt monopol på vissa typer av verk under en viss tidsperiod. Som en parentes, på Annas Arkiv anser vi att även om det finns vissa fördelar, är upphovsrätt överlag en netto-negativ för samhället — men det är en historia för en annan gång."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Detta exklusiva monopol på vissa verk innebär att det är olagligt för någon utanför detta monopol att direkt distribuera dessa verk — inklusive oss. Men Annas Arkiv är en sökmotor som inte direkt distribuerar dessa verk (åtminstone inte på vår clearnet-webbplats), så vi borde vara okej, eller hur? Inte riktigt. I många jurisdiktioner är det inte bara olagligt att distribuera upphovsrättsskyddade verk, utan också att länka till platser som gör det. Ett klassiskt exempel på detta är USA:s DMCA-lag."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Det är den striktaste änden av spektrumet. I andra änden av spektrumet kan det teoretiskt finnas länder utan några upphovsrättslagar alls, men dessa existerar egentligen inte. I stort sett alla länder har någon form av upphovsrättslagstiftning. Tillämpningen är en annan historia. Det finns gott om länder med regeringar som inte bryr sig om att upprätthålla upphovsrättslagar. Det finns också länder mellan de två extremerna, som förbjuder distribution av upphovsrättsskyddade verk, men inte förbjuder att länka till sådana verk."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "En annan övervägning är på företagsnivå. Om ett företag verkar i en jurisdiktion som inte bryr sig om upphovsrätt, men företaget självt inte är villigt att ta någon risk, kan de stänga ner din webbplats så snart någon klagar på den."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Slutligen är en stor övervägning betalningar. Eftersom vi behöver förbli anonyma kan vi inte använda traditionella betalningsmetoder. Detta lämnar oss med kryptovalutor, och endast en liten delmängd av företag stöder dessa (det finns virtuella betalkort betalda med krypto, men de accepteras ofta inte)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systemarkitektur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Så låt oss säga att du hittade några företag som är villiga att vara värd för din webbplats utan att stänga ner dig — låt oss kalla dessa “frihetsälskande leverantörer” 😄. Du kommer snabbt att upptäcka att det är ganska dyrt att vara värd för allt hos dem, så du kanske vill hitta några “billiga leverantörer” och göra den faktiska värdskapet där, genom att proxyera genom de frihetsälskande leverantörerna. Om du gör det rätt kommer de billiga leverantörerna aldrig att veta vad du är värd för, och aldrig få några klagomål."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Med alla dessa leverantörer finns det en risk att de stänger ner dig ändå, så du behöver också redundans. Vi behöver detta på alla nivåer i vår stack."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Ett något frihetsälskande företag som har satt sig i en intressant position är Cloudflare. De har <a %(blog_cloudflare)s>argumenterat</a> att de inte är en värdleverantör, utan en tjänst, som en ISP. De är därför inte föremål för DMCA eller andra nedtagningsförfrågningar, och vidarebefordrar alla förfrågningar till din faktiska värdleverantör. De har gått så långt som att gå till domstol för att skydda denna struktur. Vi kan därför använda dem som ett annat lager av caching och skydd."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare accepterar inte anonyma betalningar, så vi kan bara använda deras gratisplan. Detta innebär att vi inte kan använda deras lastbalansering eller failover-funktioner. Vi har därför <a %(annas_archive_l255)s>implementerat detta själva</a> på domännivå. Vid sidladdning kommer webbläsaren att kontrollera om den aktuella domänen fortfarande är tillgänglig, och om inte, skriver den om alla URL:er till en annan domän. Eftersom Cloudflare cachelagrar många sidor, innebär detta att en användare kan landa på vår huvuddomän, även om proxyservern är nere, och sedan vid nästa klick flyttas över till en annan domän."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Vi har fortfarande också vanliga operativa bekymmer att hantera, såsom övervakning av serverhälsa, loggning av backend- och frontend-fel, och så vidare. Vår failover-arkitektur tillåter mer robusthet även på denna front, till exempel genom att köra en helt annan uppsättning servrar på en av domänerna. Vi kan till och med köra äldre versioner av koden och datasets på denna separata domän, ifall en kritisk bugg i huvudversionen går obemärkt förbi."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Vi kan också skydda oss mot att Cloudflare vänder sig mot oss, genom att ta bort det från en av domänerna, såsom denna separata domän. Olika permutationer av dessa idéer är möjliga."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Verktyg"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Låt oss titta på vilka verktyg vi använder för att uppnå allt detta. Detta utvecklas mycket när vi stöter på nya problem och hittar nya lösningar."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Applikationsserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxyserver: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serverhantering: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Utveckling: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion statisk hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Det finns några beslut som vi har gått fram och tillbaka med. Ett är kommunikationen mellan servrar: vi brukade använda Wireguard för detta, men upptäckte att det ibland slutar att överföra data, eller bara överför data i en riktning. Detta hände med flera olika Wireguard-inställningar som vi provade, såsom <a %(github_costela_wesher)s>wesher</a> och <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Vi försökte också tunnla portar över SSH, med hjälp av autossh och sshuttle, men stötte på <a %(github_sshuttle)s>problem där</a> (även om det fortfarande inte är klart för mig om autossh lider av TCP-over-TCP-problem eller inte — det känns bara som en skakig lösning för mig men kanske är det faktiskt okej?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Istället återgick vi till direkta anslutningar mellan servrar, och dolde att en server körs på de billiga leverantörerna genom att använda IP-filtrering med UFW. Detta har nackdelen att Docker inte fungerar bra med UFW, om du inte använder <code>network_mode: \"host\"</code>. Allt detta är lite mer felbenäget, eftersom du kommer att exponera din server för internet med bara en liten felkonfiguration. Kanske borde vi gå tillbaka till autossh — feedback skulle vara mycket välkommet här."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Vi har också gått fram och tillbaka mellan Varnish och Nginx. Vi gillar för närvarande Varnish, men det har sina egenheter och skarpa kanter. Detsamma gäller för Checkmk: vi älskar det inte, men det fungerar för tillfället. Weblate har varit okej men inte otroligt — jag fruktar ibland att det kommer att förlora mina data när jag försöker synkronisera det med vårt git-repo. Flask har varit bra överlag, men det har några konstiga egenheter som har kostat mycket tid att felsöka, såsom att konfigurera anpassade domäner, eller problem med dess SqlAlchemy-integration."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Hittills har de andra verktygen varit fantastiska: vi har inga allvarliga klagomål om MariaDB, ElasticSearch, Gitlab, Zulip, Docker och Tor. Alla dessa har haft några problem, men inget alltför allvarligt eller tidskrävande."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Slutsats"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Det har varit en intressant upplevelse att lära sig hur man sätter upp en robust och motståndskraftig skuggbibliotekssökmotor. Det finns massor av fler detaljer att dela i senare inlägg, så låt mig veta vad du vill veta mer om!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Som alltid söker vi donationer för att stödja detta arbete, så se till att kolla in Donationssidan på Annas Arkiv. Vi söker också andra typer av stöd, såsom bidrag, långsiktiga sponsorer, högriskbetalningsleverantörer, kanske till och med (smakfulla!) annonser. Och om du vill bidra med din tid och dina färdigheter, letar vi alltid efter utvecklare, översättare och så vidare. Tack för ditt intresse och stöd."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna och teamet (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hej, jag är Anna. Jag skapade <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, världens största skuggbibliotek. Detta är min personliga blogg, där jag och mina lagkamrater skriver om piratkopiering, digital bevarande och mer."

#, fuzzy
msgid "blog.index.text2"
msgstr "Anslut med mig på <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Observera att denna webbplats bara är en blogg. Vi är värdar för våra egna ord här. Inga torrents eller andra upphovsrättsskyddade filer är värdar eller länkade här."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogginlägg"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3B WorldCat-skrapning"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Lägger 5 998 794 böcker på IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Varning: detta blogginlägg har blivit föråldrat. Vi har beslutat att IPFS ännu inte är redo för prime time. Vi kommer fortfarande att länka till filer på IPFS från Annas Arkiv när det är möjligt, men vi kommer inte längre att vara värd för det själva, och vi rekommenderar inte andra att spegla med IPFS. Se vår Torrents-sida om du vill hjälpa till att bevara vår samling."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Annas Arkiv skrapade hela WorldCat (världens största bibliotek metadata-samling) för att skapa en TODO-lista över böcker som behöver bevaras.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "För ett år sedan <a %(blog)s>började vi</a> att besvara denna fråga: <strong>Vilken procentandel av böcker har permanent bevarats av skuggbibliotek?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "När en bok hamnar i ett öppet data-skuggbibliotek som <a %(wikipedia_library_genesis)s>Library Genesis</a>, och nu <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, speglas den över hela världen (genom torrents), vilket praktiskt taget bevarar den för alltid."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "För att besvara frågan om vilken procentandel av böcker som har bevarats, behöver vi veta nämnaren: hur många böcker finns det totalt? Och helst har vi inte bara ett nummer, utan faktisk metadata. Då kan vi inte bara matcha dem mot skuggbibliotek, utan också <strong>skapa en TODO-lista över återstående böcker att bevara!</strong> Vi kan till och med börja drömma om en crowdsourcad insats för att gå igenom denna TODO-lista."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Vi skrapade <a %(wikipedia_isbndb_com)s>ISBNdb</a> och laddade ner <a %(openlibrary)s>Open Library dataset</a>, men resultaten var otillfredsställande. Huvudproblemet var att det inte fanns mycket överlappning av ISBN. Se detta Venn-diagram från <a %(blog)s>vårt blogginlägg</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Vi blev mycket förvånade över hur lite överlappning det fanns mellan ISBNdb och Open Library, som båda inkluderar data från olika källor, såsom webbsökningar och biblioteksregister. Om de båda gör ett bra jobb med att hitta de flesta ISBN där ute, skulle deras cirklar säkert ha betydande överlappning, eller så skulle den ena vara en delmängd av den andra. Det fick oss att undra, hur många böcker faller <em>helt utanför dessa cirklar</em>? Vi behöver en större databas."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Det var då vi riktade våra blickar mot världens största bokdatabas: <a %(wikipedia_worldcat)s>WorldCat</a>. Detta är en proprietär databas av den ideella organisationen <a %(wikipedia_oclc)s>OCLC</a>, som samlar metadata från bibliotek över hela världen, i utbyte mot att ge dessa bibliotek tillgång till hela datasetet och att de visas i slutanvändarnas sökresultat."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Även om OCLC är en ideell organisation, kräver deras affärsmodell att de skyddar sin databas. Tja, vi är ledsna att säga, vänner på OCLC, vi ger bort allt. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Under det senaste året har vi noggrant skrapat alla WorldCat-poster. Till en början hade vi tur. WorldCat rullade precis ut sin kompletta webbplatsomdesign (i augusti 2022). Detta inkluderade en omfattande översyn av deras backend-system, vilket introducerade många säkerhetsbrister. Vi tog omedelbart tillfället i akt och kunde skrapa hundratals miljoner (!) poster på bara några dagar."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat redesign</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Efter det åtgärdades säkerhetsbristerna långsamt en efter en, tills den sista vi hittade blev åtgärdad för ungefär en månad sedan. Vid den tiden hade vi i princip alla poster och gick bara efter något högre kvalitetsposter. Så vi kände att det var dags att släppa!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Låt oss titta på lite grundläggande information om datan:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Annas Arkiv Containers (AAC)</a>, som i huvudsak är <a %(jsonlines)s>JSON Lines</a> komprimerade med <a %(zstd)s>Zstandard</a>, plus några standardiserade semantiker. Dessa containers omsluter olika typer av poster, baserat på de olika skrapningar vi genomförde."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

msgid "dyn.buy_membership.error.unknown"
msgstr "Ett okänt fel inträffade. Vänligen kontakta oss på %(email)s och bifoga en skärmdump."

msgid "dyn.buy_membership.error.minimum"
msgstr "Denna mynt har en högre minimum än vanligt. Vänligen välj en annan längd eller ett annat mynt."

msgid "dyn.buy_membership.error.try_again"
msgstr "Begäran kunde inte slutföras. Försök igen om några minuter, och om problemet kvarstår, kontakta oss på %(email)s med en skärmdump."

msgid "dyn.buy_membership.error.wait"
msgstr "Fel vid betalningshanteringf. Vänta en stund och försök igen. Om problemet kvarstår i mer än 24 timmar, vänligen kontakta oss på %(email)s med en skärmdump."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "dolt kommentar"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Filproblem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Bättre version"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Vill du anmäla denna användare för kränkande eller olämpligt beteende?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Anmäl missbruk"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Missbruk anmält:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Du anmälde denna användare för missbruk."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Svara"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Vänligen <a %(a_login)s>logga in</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Du lämnade en kommentar. Det kan ta en minut innan den visas."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Något gick fel. Vänligen ladda om sidan och försök igen."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s påverkade sidor"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Inte synlig i Libgen.rs Facklitteratur"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Inte synlig i Libgen.rs Skönlitteratur"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Inte synlig i Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Markerad som trasig i Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Saknas från Z-library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Markerad som ”spam” i Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Markerad som ”dålig fil” i Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Inte alla sidor kunde konverteras till PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Körning av exiftool misslyckades på denna fil"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Bok (okänd)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Bok (facklitteratur)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Bok (skönlitteratur)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Tidningsartikel"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standarddokument"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Tidskrift"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Serietidning"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Notblad"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Ljudbok"

msgid "common.md5_content_type_mapping.other"
msgstr "Övrigt"

msgid "common.access_types_mapping.aa_download"
msgstr "partnerservernedladdning"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Extern nedladdning"

msgid "common.access_types_mapping.external_borrow"
msgstr "Extern utlåning"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Extern utlåning (för funktionshindrade)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Utforska metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Innehåll i torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Kinesiska"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Uppladdningar till AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tjeckisk metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Böcker"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Ryska statsbiblioteket"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Titel"

msgid "common.specific_search_fields.author"
msgstr "Författare"

msgid "common.specific_search_fields.publisher"
msgstr "Förlag"

msgid "common.specific_search_fields.edition_varia"
msgstr "Upplaga"

msgid "common.specific_search_fields.year"
msgstr "Utgivningsår"

msgid "common.specific_search_fields.original_filename"
msgstr "Ursprungligt filnamn"

msgid "common.specific_search_fields.description_comments"
msgstr "Beskrivning och metadatakommentarer"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partnerservernedladdningar är just nu inte tillgängliga för denna fil."

msgid "common.md5.servers.fast_partner"
msgstr "Snabb partnerserver #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(rekommenderad)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(ingen webbläsarverifiering eller väntelistor)"

msgid "common.md5.servers.slow_partner"
msgstr "Långsam partnerserver #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(lite snabbare men med väntelista)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(ingen väntelista, men kan vara mycket långsam)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Facklitteratur"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Skönlitteratur"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klicka även på \"GET\" högst upp)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(klicka på “GET” högst upp på sidan)"

msgid "page.md5.box.download.libgen_ads"
msgstr "deras annonser är kända för att innehålla skadlig programvara, så använd en annonsblockerare eller klicka inte på annonser"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC-filer kan vara opålitliga att ladda ner)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library på Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(kräver webbläsaren Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Låna från Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(utskriftsbegränsade användare endast)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(associerad DOI är kanske inte tillgänglig i Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "samling"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Massnedladdningar via torrent"

msgid "page.md5.box.download.experts_only"
msgstr "(endast experter)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Sök i Annas Arkiv efter ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Sök i olika andra databaser efter ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Hitta originalposten i ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Sök i Annas Arkiv efter Open Library ID"

msgid "page.md5.box.download.original_openlib"
msgstr "Hitta originalposten i Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Sök i Annas Arkiv efter OCLC (WorldCat) nummer"

msgid "page.md5.box.download.original_oclc"
msgstr "Hitta originalposten i WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Sök i Annas Arkiv efter DuXiu SSID-nummer"

msgid "page.md5.box.download.original_duxiu"
msgstr "Sök manuellt på DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Sök i Annas Arkiv efter CADAL SSNO-nummer"

msgid "page.md5.box.download.original_cadal"
msgstr "Hitta originalposten i CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Sök i Annas Arkiv efter DuXiu DXID nummer"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Index"

msgid "page.md5.box.download.scidb"
msgstr "Annas Arkiv 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(ingen webbläsarverifiering krävs)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tjeckiska metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Böcker %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

msgid "page.md5.box.descr_title"
msgstr "beskrivning"

msgid "page.md5.box.alternative_filename"
msgstr "Alternativt filnamn"

msgid "page.md5.box.alternative_title"
msgstr "Alternativ titel"

msgid "page.md5.box.alternative_author"
msgstr "Alternativ författare"

msgid "page.md5.box.alternative_publisher"
msgstr "Alternativt förlag"

msgid "page.md5.box.alternative_edition"
msgstr "Alternativ utgåva"

msgid "page.md5.box.alternative_extension"
msgstr "Alternativ filändelse"

msgid "page.md5.box.metadata_comments_title"
msgstr "metadatakommentarer"

msgid "page.md5.box.alternative_description"
msgstr "Alternativ beskrivning"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "datum öppen källkod"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub fil “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending fil “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Detta är en post av en fil från Internet Archive, inte en direkt nedladdningsbar fil. Du kan försöka låna boken (länk nedan), eller använda denna URL när du <a %(a_request)s>begär en fil</a>."

msgid "page.md5.header.consider_upload"
msgstr "Om du har den här filen och den ännu inte är tillgänglig i Annas Arkiv, överväg att <a %(a_request)s>ladda upp den</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nummer %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata post"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Detta är en metadata-post, inte en nedladdningsbar fil. Du kan använda denna URL när du <a %(a_request)s>begär en fil</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata från länkad post"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Förbättra metadata på Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Varning: flera länkade poster:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Förbättra metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Rapportera filkvalitet"

msgid "page.search.results.download_time"
msgstr "Tidsåtgång för nerladdning"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Webbplats:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Sök i Annas Arkiv efter “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Kodutforskare:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Visa i Kodutforskare ”%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Läs mer…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Nedladdningar (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Låna (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Utforska metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Kommentarer (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listor (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistik (%(count)s)"

msgid "common.tech_details"
msgstr "Tekniska detaljer"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Denna fil kan ha problem, och har dolts från ett källbibliotek.</span> Ibland är detta på begäran av en upphovsrättsinnehavare, ibland beror det på att det finns ett bättre alternativ, men ibland är det på grund av ett problem med själva filen. Det kan fortfarande gå bra att ladda ner, men vi rekommenderar att du först söker efter en alternativ fil. Mer detaljer:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "En bättre version av denna fil kan finnas på %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Om du fortfarande vill ladda ner den här filen, se till att endast använda pålitlig och uppdaterad programvara för att öppna den."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Snabba nedladdningar"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Snabba nedladdningar</strong> Bli en <a %(a_membership)s>medlem</a> för att stödja det långsiktiga bevarandet av böcker, artiklar och mer. Som ett tack för ditt stöd får du snabba nedladdningar. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Om du donerar denna månad får du <strong>dubbelt</strong> så många snabba nedladdningar."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Snabba nedladdningar</strong> Du har %(remaining)s kvar idag. Tack för att du är medlem! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Snabba nedladdningar</strong> Du har använt alla snabba nedladdningar för idag."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Snabba nedladdningar</strong> Du har nyligen laddat ner denna fil. Länkarna förblir giltiga ett tag."

msgid "page.md5.box.download.option"
msgstr "Alternativ #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(ingen omdirigering)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(öppna i visare)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Rekommendera en vän, och både du och din vän får %(percentage)s%% bonus snabba nedladdningar!"

msgid "layout.index.header.learn_more"
msgstr "Läs mer…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Långsamma nedladdningar"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Från betrodda partners."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mer information i <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kan kräva <a %(a_browser)s>webbläsarverifiering</a> — obegränsade nedladdningar!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Efter nedladdning:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Öppna i vår visare"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "visa externa nedladdningar"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Externa nedladdningar"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Inga nedladdningar hittades."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Alla nedladdningsalternativ bör vara säkra att använda. Var dock alltid försiktig när du laddar ner filer från internet. Se till att hålla dina enheter uppdaterade."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "För stora filer rekommenderar vi att använda en nedladdningshanterare för att undvika avbrott."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Rekommenderade nedladdningshanterare: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Du behöver en e-bok- eller PDF-läsare för att öppna filen, beroende på filformatet."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Rekommenderade e-boksläsare: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Annas Arkiv onlinevisare"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Använd onlineverktyg för att konvertera mellan format."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Rekommenderade konverteringsverktyg: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Du kan skicka både PDF- och EPUB-filer till din Kindle eller Kobo eReader."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Rekommenderade verktyg: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazons ”Skicka till Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazzs ”Skicka till Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Stöd författare och bibliotek"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Om du gillar detta och har råd, överväg att köpa originalet eller stödja författarna direkt."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Om detta finns tillgängligt på ditt lokala bibliotek, överväg att låna det gratis där."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Filkvalitet"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Hjälp gemenskapen genom att rapportera kvaliteten på denna fil! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Rapportera filproblem (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Utmärkt filkvalitet (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Lägg till kommentar (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Vad är fel med denna fil?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Vänligen använd <a %(a_copyright)s>DMCA / Copyright-ansökningsformuläret</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Beskriv problemet (obligatoriskt)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Problem beskrivning"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 av en bättre version av denna fil (om tillämpligt)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Fyll i detta om det finns en annan fil som nära matchar denna fil (samma upplaga, samma filändelse om du kan hitta en), som folk bör använda istället för denna fil. Om du känner till en bättre version av denna fil utanför Annas Arkiv, vänligen <a %(a_upload)s>ladda upp den</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Du kan få md5 från URL:en, t.ex."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Skicka rapport"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Lär dig hur du <a %(a_metadata)s>förbättrar metadata</a> för denna fil själv."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Tack för att du skickade in din rapport. Den kommer att visas på denna sida, samt granskas manuellt av Anna (tills vi har ett ordentligt modereringssystem)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Något gick fel. Vänligen ladda om sidan och försök igen."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Om denna fil har hög kvalitet kan du diskutera allt om den här! Om inte, vänligen använd knappen ”Rapportera filproblem”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Jag älskade den här boken!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lämna kommentar"

#, fuzzy
msgid "common.english_only"
msgstr "Texten nedan fortsätter på engelska."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totala nedladdningar: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "En ”fil MD5” är en hash som beräknas från filens innehåll och är rimligt unik baserat på det innehållet. Alla skuggbibliotek som vi har indexerat här använder främst MD5 för att identifiera filer."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "En fil kan förekomma i flera skuggbibliotek. För information om de olika datasets som vi har sammanställt, se <a %(a_datasets)s>Datasets-sidan</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Detta är en fil som hanteras av <a %(a_ia)s>IA:s Controlled Digital Lending</a>-bibliotek och indexeras av Annas Arkiv för sökning. För information om de olika datasets som vi har sammanställt, se <a %(a_datasets)s>Datasets-sidan</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "För information om denna specifika fil, kolla in dess <a %(a_href)s>JSON-fil</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Problem med att ladda denna sida"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Vänligen uppdatera för att försöka igen. <a %(a_contact)s>Kontakta oss</a> om problemet kvarstår i flera timmar."

msgid "page.md5.invalid.header"
msgstr "Hittades inte"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” hittades inte i vår databas."

#, fuzzy
msgid "page.login.title"
msgstr "Logga in / Registrera"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Webbläsarverifiering"

#, fuzzy
msgid "page.login.text1"
msgstr "För att förhindra spam-botar från att skapa massor av konton, behöver vi först verifiera din webbläsare."

#, fuzzy
msgid "page.login.text2"
msgstr "Om du fastnar i en oändlig loop rekommenderar vi att du installerar <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Det kan också hjälpa att stänga av annonsblockerare och andra webbläsartillägg."

#, fuzzy
msgid "page.codes.title"
msgstr "Koder"

#, fuzzy
msgid "page.codes.heading"
msgstr "Kodutforskare"

#, fuzzy
msgid "page.codes.intro"
msgstr "Utforska koderna som poster är taggade med, efter prefix. Kolumnen ”poster” visar antalet poster som är taggade med koder med det givna prefixet, som ses i sökmotorn (inklusive metadata-endast poster). Kolumnen ”koder” visar hur många faktiska koder som har ett givet prefix."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Denna sida kan ta ett tag att generera, vilket är anledningen till att den kräver en Cloudflare captcha. <a %(a_donate)s>Medlemmar</a> kan hoppa över captchan."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Vänligen skrapa inte dessa sidor. Istället rekommenderar vi att <a %(a_import)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser, och köra vår <a %(a_software)s>öppna källkod</a>. Rådata kan manuellt utforskas genom JSON-filer som <a %(a_json_file)s>denna</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefix"

#, fuzzy
msgid "common.form.go"
msgstr "Gå"

#, fuzzy
msgid "common.form.reset"
msgstr "Återställ"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Sök Annas Arkiv"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Varning: koden har felaktiga Unicode-tecken i sig och kan bete sig felaktigt i olika situationer. Den råa binären kan avkodas från base64-representationen i URL:en."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Känt kodprefix ”%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Prefix"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etikett"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Beskrivning"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL för en specifik kod"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "”%%s” kommer att ersättas med kodens värde"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generisk URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Webbplats"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s post som matchar “%(prefix_label)s”"
msgstr[1] "%(count)s poster som matchar “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL för specifik kod: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mer…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Koder som börjar med “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Index av"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "poster"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "koder"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Färre än %(count)s poster"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "För DMCA / upphovsrättsanspråk, använd <a %(a_copyright)s>detta formulär</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Alla andra sätt att kontakta oss angående upphovsrättsanspråk kommer att raderas automatiskt."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Vi välkomnar verkligen din feedback och dina frågor!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "På grund av mängden skräppost och nonsensmejl vi får, vänligen markera rutorna för att bekräfta att du förstår dessa villkor för att kontakta oss."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Upphovsrättsanspråk på detta e-postmeddelande kommer att ignoreras; använd istället formuläret."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner-servrar är otillgängliga på grund av stängningar av hosting. De bör vara uppe igen snart."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Medlemskap kommer att förlängas i enlighet med detta."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Mejla oss inte för att <a %(a_request)s>efterfråga böcker</a><br>eller små (<10k) <a %(a_upload)s>uppladdningar</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "När du ställer frågor om konto eller donationer, inkludera ditt kontonummer, skärmdumpar, kvitton, så mycket information som möjligt. Vi kontrollerar vår e-post var 1-2 vecka, så att inte inkludera denna information kommer att försena någon lösning."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Visa e-post"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Formulär för upphovsrättsanspråk"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Om du har ett DMCA- eller annat upphovsrättsanspråk, vänligen fyll i detta formulär så noggrant som möjligt. Om du stöter på några problem, kontakta oss på vår dedikerade DMCA-adress: %(email)s. Observera att anspråk som skickas till denna adress inte kommer att behandlas, den är endast för frågor. Använd formuläret nedan för att skicka in dina anspråk."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL:er på Annas Arkiv (obligatoriskt). En per rad. Vänligen inkludera endast URL:er som beskriver exakt samma utgåva av en bok. Om du vill göra en ansökan för flera böcker eller flera utgåvor, vänligen skicka in detta formulär flera gånger."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Ansökningar som buntar ihop flera böcker eller utgåvor kommer att avvisas."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Ditt namn (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adress (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefonnummer (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-post (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Tydlig beskrivning av källmaterialet (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN-nummer för källmaterialet (om tillämpligt). Ett per rad. Vänligen inkludera endast de som exakt matchar den utgåva för vilken du rapporterar ett upphovsrättsanspråk."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL:er för källmaterialet, en per rad. Ta dig tid att söka efter ditt källmaterial på Open Library. Detta hjälper oss att verifiera ditt anspråk."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL:er till källmaterialet, en per rad (obligatoriskt). Vänligen inkludera så många som möjligt för att hjälpa oss att verifiera ditt anspråk (t.ex. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Uttalande och signatur (obligatoriskt)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Skicka in anspråk"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Tack för att du skickade in ditt upphovsrättsanspråk. Vi kommer att granska det så snart som möjligt. Vänligen ladda om sidan för att skicka in ett nytt."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Något gick fel. Vänligen ladda om sidan och försök igen."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Om du är intresserad av att spegla denna datamängd för <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-träning</a>, vänligen kontakta oss."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Vårt uppdrag är att arkivera alla böcker i världen (samt artiklar, tidskrifter, etc.) och göra dem allmänt tillgängliga. Vi tror att alla böcker bör speglas brett för att säkerställa redundans och motståndskraft. Det är därför vi samlar filer från en mängd olika källor. Vissa källor är helt öppna och kan speglas i bulk (som Sci-Hub). Andra är stängda och skyddande, så vi försöker skrapa dem för att ”befria” deras böcker. Ytterligare andra faller någonstans däremellan."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "All vår data kan <a %(a_torrents)s>torrentas</a>, och all vår metadata kan <a %(a_anna_software)s>genereras</a> eller <a %(a_elasticsearch)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser. Rådata kan manuellt utforskas genom JSON-filer som <a %(a_dbrecord)s>denna</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Översikt"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Nedan är en snabb översikt över källorna till filerna på Annas Arkiv."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Källa"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Storlek"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% speglad av AA / torrents tillgängliga"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Procentandelar av antal filer"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Senast uppdaterad"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Facklitteratur och skönlitteratur"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s fil"
msgstr[1] "%(count)s filer"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li ”scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: fryst sedan 2021; de flesta tillgängliga via torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: mindre tillägg sedan dess</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Exklusive “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Skönlitterära torrents ligger efter (även om ID ~4-6M inte har torrentats eftersom de överlappar med våra Zlib-torrents)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Samlingen ”Kinesiska” i Z-Library verkar vara densamma som vår DuXiu-samling, men med olika MD5s. Vi exkluderar dessa filer från torrents för att undvika duplicering, men visar dem fortfarande i vår sökindex."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontrollerad Digital Utlåning"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ av filer är sökbara."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totalt"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Exklusive dubbletter"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Eftersom skuggbiblioteken ofta synkroniserar data från varandra, finns det betydande överlappning mellan biblioteken. Det är därför siffrorna inte summerar till totalen."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Procentandelen ”speglad och seedad av Annas Arkiv” visar hur många filer vi speglar själva. Vi seedar dessa filer i bulk genom torrents och gör dem tillgängliga för direkt nedladdning via partnerwebbplatser."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Källbibliotek"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Vissa källbibliotek främjar bulkdelning av sina data genom torrents, medan andra inte delar sin samling så lätt. I det senare fallet försöker Annas Arkiv att skrapa deras samlingar och göra dem tillgängliga (se vår <a %(a_torrents)s>Torrents</a>-sida). Det finns också mellansituationer, till exempel där källbibliotek är villiga att dela, men inte har resurserna att göra det. I dessa fall försöker vi också hjälpa till."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Nedan följer en översikt över hur vi interagerar med de olika källbiblioteken."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Källa"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Filer"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Dagliga <a %(dbdumps)s>HTTP-databasdumpar</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatiserade torrenter för <a %(nonfiction)s>Facklitteratur</a> och <a %(fiction)s>Skönlitteratur</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(covers)s>bokomslagstorrenter</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen ”scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub har fryst nya filer sedan 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata-dumpar tillgängliga <a %(scihub1)s>här</a> och <a %(scihub2)s>här</a>, samt som en del av <a %(libgenli)s>Libgen.li-databasen</a> (som vi använder)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Data-torrenter tillgängliga <a %(scihub1)s>här</a>, <a %(scihub2)s>här</a>, och <a %(libgenli)s>här</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Några nya filer <a %(libgenrs)s>läggs</a> <a %(libgenli)s>till</a> i Libgens \"scimag\", men inte tillräckligt för att motivera nya torrenter"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kvartalsvisa <a %(dbdumps)s>HTTP-databasedumpar</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Facklitteraturtorrenter delas med Libgen.rs (och speglas <a %(libgenli)s>här</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Annas Arkiv och Libgen.li hanterar tillsammans samlingar av <a %(comics)s>serietidningar</a>, <a %(magazines)s>magasin</a>, <a %(standarts)s>standarddokument</a> och <a %(fiction)s>skönlitteratur (avvikande från Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Deras \"fiction_rus\"-samling (ryska skönlitteratur) har inga dedikerade torrents, men täcks av torrents från andra, och vi håller en <a %(fiction_rus)s>spegel</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Annas Arkiv och Z-Library hanterar gemensamt en samling av <a %(metadata)s>Z-Library metadata</a> och <a %(files)s>Z-Library filer</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Viss metadata tillgänglig genom <a %(openlib)s>Open Library-databasdumpar</a>, men de täcker inte hela IA-samlingen"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Inga lättillgängliga metadatadumpar tillgängliga för hela deras samling"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Filer endast tillgängliga för utlåning på begränsad basis, med olika åtkomstbegränsningar"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(ia)s>IA-filer</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Olika metadata-databaser utspridda över det kinesiska internet; dock ofta betaldatabaser"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Inga lättillgängliga metadatadumpar tillgängliga för hela deras samling."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(duxiu)s>DuXiu-metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Olika fil-databaser utspridda över det kinesiska internet; dock ofta betaldatabaser"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s De flesta filer är endast tillgängliga med premium BaiduYun-konton; långsamma nedladdningshastigheter."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(duxiu)s>DuXiu-filer</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Olika mindre eller enstaka källor. Vi uppmuntrar folk att ladda upp till andra skuggbibliotek först, men ibland har folk samlingar som är för stora för andra att sortera igenom, men inte tillräckligt stora för att motivera en egen kategori."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Endast metadata-källor"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Vi berikar också vår samling med endast metadata-källor, som vi kan matcha till filer, t.ex. med hjälp av ISBN-nummer eller andra fält. Nedan följer en översikt över dessa. Återigen, vissa av dessa källor är helt öppna, medan för andra måste vi skrapa dem."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Vår inspiration för att samla metadata är Aaron Swartz’ mål om “en webbsida för varje bok som någonsin publicerats”, för vilket han skapade <a %(a_openlib)s>Open Library</a>. Det projektet har gått bra, men vår unika position gör att vi kan få metadata som de inte kan. En annan inspiration var vår önskan att veta <a %(a_blog)s>hur många böcker det finns i världen</a>, så vi kan räkna ut hur många böcker vi fortfarande har kvar att rädda."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Observera att i metadatasökningen visar vi de ursprungliga posterna. Vi gör ingen sammanslagning av poster."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Senast uppdaterad"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Månatliga <a %(dbdumps)s>databasdumpar</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Inte tillgänglig direkt i bulk, skyddad mot skrapning"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Enhetlig databas"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Vi kombinerar alla ovanstående källor till en enhetlig databas som vi använder för att driva denna webbplats. Denna enhetliga databas är inte direkt tillgänglig, men eftersom Anna’s Arkiv är helt öppen källkod, kan den ganska enkelt <a %(a_generated)s>genereras</a> eller <a %(a_downloaded)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser. Skripten på den sidan kommer automatiskt att ladda ner all nödvändig metadata från de ovan nämnda källorna."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Om du vill utforska våra data innan du kör dessa skript lokalt, kan du titta på våra JSON-filer, som länkar vidare till andra JSON-filer. <a %(a_json)s>Denna fil</a> är en bra startpunkt."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Anpassad från vårt <a %(a_href)s>blogginlägg</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> är en enorm databas med skannade böcker, skapad av <a %(superstar_link)s>SuperStar Digital Library Group</a>. De flesta är akademiska böcker, skannade för att göra dem tillgängliga digitalt för universitet och bibliotek. För vår engelsktalande publik har <a %(princeton_link)s>Princeton</a> och <a %(uw_link)s>University of Washington</a> bra översikter. Det finns också en utmärkt artikel som ger mer bakgrund: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Böckerna från Duxiu har länge piratkopierats på det kinesiska internet. Vanligtvis säljs de för mindre än en dollar av återförsäljare. De distribueras vanligtvis med den kinesiska motsvarigheten till Google Drive, som ofta har hackats för att tillåta mer lagringsutrymme. Några tekniska detaljer finns <a %(link1)s>här</a> och <a %(link2)s>här</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Även om böckerna har distribuerats semi-offentligt, är det ganska svårt att få tag på dem i bulk. Vi hade detta högt på vår TODO-lista och avsatte flera månader av heltidsarbete för det. Men i slutet av 2023 kontaktade en otrolig, fantastisk och talangfull volontär oss och berättade att de redan hade gjort allt detta arbete — till stor kostnad. De delade hela samlingen med oss utan att förvänta sig något i gengäld, förutom garantin om långsiktig bevaring. Verkligen anmärkningsvärt."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Resurser"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totalt antal filer: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Total filstorlek: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Filer speglade av Annas Arkiv: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Senast uppdaterad: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrenter av Annas Arkiv"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Exempelpost på Annas Arkiv"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Vårt blogginlägg om dessa data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skript för att importera metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Annas Arkiv Containers-format"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mer information från våra volontärer (råa anteckningar):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Kontrollerad Digital Utlåning"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Denna dataset är nära relaterad till <a %(a_datasets_openlib)s>Open Library dataset</a>. Den innehåller en skrapning av all metadata och en stor del av filer från IA:s Controlled Digital Lending Library. Uppdateringar släpps i <a %(a_aac)s>Anna’s Archive Containers-format</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Dessa poster hänvisas direkt från Open Library dataset, men innehåller också poster som inte finns i Open Library. Vi har också ett antal datafiler som skrapats av community-medlemmar genom åren."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Samlingen består av två delar. Du behöver båda delarna för att få all data (förutom ersatta torrents, som är överstrukna på torrentsidan)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "vår första utgåva, innan vi standardiserade på <a %(a_aac)s>Anna’s Archive Containers (AAC) format</a>. Innehåller metadata (som json och xml), pdf:er (från acsm och lcpdf digitala utlåningssystem) och omslagsminiatyrer."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementella nya utgåvor, med användning av AAC. Innehåller endast metadata med tidsstämplar efter 2023-01-01, eftersom resten redan täcks av \"ia\". Även alla pdf-filer, denna gång från acsm och \"bookreader\" (IA:s webbläsare) utlåningssystem. Trots att namnet inte är helt korrekt, lägger vi fortfarande in bookreader-filer i ia2_acsmpdf_files-samlingen, eftersom de är ömsesidigt uteslutande."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Huvudsida %(source)s webbplats"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitalt lånebibliotek"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata-dokumentation (de flesta fält)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN landsinformation"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Den internationella ISBN-byrån släpper regelbundet de intervall som den har tilldelat nationella ISBN-byråer. Från detta kan vi härleda vilket land, region eller språkgrupp detta ISBN tillhör. Vi använder för närvarande dessa data indirekt, genom <a %(a_isbnlib)s>isbnlib</a> Python-biblioteket."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Resurser"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Senast uppdaterad: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN-webbplats"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "För bakgrundshistorien om de olika Library Genesis-forkarna, se sidan för <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li innehåller det mesta av samma innehåll och metadata som Libgen.rs, men har några samlingar utöver detta, nämligen serier, tidskrifter och standarddokument. Den har också integrerat <a %(a_scihub)s>Sci-Hub</a> i sin metadata och sökmotor, vilket är vad vi använder för vår databas."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadatan för detta bibliotek är fritt tillgänglig <a %(a_libgen_li)s>på libgen.li</a>. Denna server är dock långsam och stöder inte återupptagning av brutna anslutningar. Samma filer finns också tillgängliga på <a %(a_ftp)s>en FTP-server</a>, som fungerar bättre."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents är tillgängliga för det mesta av det extra innehållet, särskilt torrents för serietidningar, magasin och standarddokument har släppts i samarbete med Annas Arkiv."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Skönlitteratursamlingen har sina egna torrents (avvikande från <a %(a_href)s>Libgen.rs</a>) med start vid %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Enligt administratören för Libgen.li bör \"fiction_rus\" (ryska skönlitteratur) samlingen täckas av regelbundet släppta torrents från <a %(a_booktracker)s>booktracker.org</a>, särskilt <a %(a_flibusta)s>flibusta</a> och <a %(a_librusec)s>lib.rus.ec</a> torrents (som vi speglar <a %(a_torrents)s>här</a>, även om vi ännu inte har fastställt vilka torrents som motsvarar vilka filer)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistik för alla samlingar kan hittas <a %(a_href)s>på libgens webbplats</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Facklitteratur verkar också ha avvikit, men utan nya torrents. Det verkar ha hänt sedan början av 2022, även om vi inte har verifierat detta."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Vissa områden utan torrents (såsom skönlitteraturområden f_3463000 till f_4260000) är troligen Z-Library (eller andra dubbletter) filer, även om vi kanske vill göra en deduplicering och skapa torrents för lgli-unika filer i dessa områden."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Observera att torrentfilerna som hänvisar till ”libgen.is” är explicita speglar av <a %(a_libgen)s>Libgen.rs</a> (”.is” är en annan domän som används av Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "En användbar resurs för att använda metadata är <a %(a_href)s>denna sida</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Skönlitterära torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Serietidnings-torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Tidskrifts-torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standarddokument torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Ryska skönlitteratur torrents på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Information om metadatafält"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Spegel av andra torrents (och unika skönlitterära och serietidnings-torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Vårt blogginlägg om serietidningsutgåvan"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Den korta historien om de olika Library Genesis (eller ”Libgen”) förgreningarna är att över tid, de olika personerna involverade i Library Genesis hade en konflikt och gick skilda vägar."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "”.fun”-versionen skapades av den ursprungliga grundaren. Den omarbetas till förmån för en ny, mer distribuerad version."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "”.rs”-versionen har mycket liknande data och släpper mest konsekvent sin samling i bulk-torrents. Den är ungefär uppdelad i en ”skönlitterär” och en ”facklitterär” sektion."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Ursprungligen på \"http://gen.lib.rus.ec\"."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>”.li”-versionen</a> har en massiv samling serietidningar, samt annat innehåll, som ännu inte är tillgängligt för bulk-nedladdning via torrents. Den har en separat torrent-samling av skönlitterära böcker, och den innehåller metadata från <a %(a_scihub)s>Sci-Hub</a> i sin databas."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Enligt detta <a %(a_mhut)s>foruminlägg</a> var Libgen.li ursprungligen värd på \"http://free-books.dontexist.com\"."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> är på sätt och vis också en förgrening av Library Genesis, även om de använde ett annat namn för sitt projekt."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Denna sida handlar om ”.rs”-versionen. Den är känd för att konsekvent publicera både sin metadata och hela innehållet i sin bokkatalog. Dess boksamling är uppdelad mellan en skönlitterär och en facklitterär del."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "En användbar resurs för att använda metadata är <a %(a_metadata)s>denna sida</a> (blockerar IP-intervall, VPN kan krävas)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Från och med 2024-03, publiceras nya torrents i <a %(a_href)s>denna forumtråd</a> (blockerar IP-intervall, VPN kan krävas)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Facklitteratur-torrenter på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Skönlitteratur-torrenter på Annas Arkiv"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Information om metadatafält för Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Facklitteratur-torrenter"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Skönlitteratur-torrenter"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskussionsforum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrenter av Annas Arkiv (bokomslag)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Vår blogg om bokomslagsutgåvan"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis är känt för att redan generöst göra sina data tillgängliga i bulk via torrenter. Vår Libgen-samling består av hjälpdata som de inte släpper direkt, i samarbete med dem. Stort tack till alla inblandade i Library Genesis för att ni arbetar med oss!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Utgåva 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Denna <a %(blog_post)s>första utgåva</a> är ganska liten: cirka 300GB av bokomslag från Libgen.rs-forken, både skönlitteratur och facklitteratur. De är organiserade på samma sätt som de visas på libgen.rs, t.ex.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s för en facklitterär bok."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s för en skönlitterär bok."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Precis som med Z-Library-samlingen har vi lagt dem alla i en stor .tar-fil, som kan monteras med <a %(a_ratarmount)s>ratarmount</a> om du vill servera filerna direkt."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> är en proprietär databas av den ideella organisationen <a %(a_oclc)s>OCLC</a>, som samlar metadata från bibliotek över hela världen. Det är troligen den största samlingen av biblioteksmetadata i världen."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, första utgåvan:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "I oktober 2023 <a %(a_scrape)s>släppte</a> vi en omfattande skrapning av OCLC (WorldCat)-databasen, i <a %(a_aac)s>Annas Arkiv Containers-format</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrenter av Annas Arkiv"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Vårt blogginlägg om dessa data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library är ett open source-projekt av Internet Archive för att katalogisera varje bok i världen. Det har en av världens största bokskanningsoperationer och har många böcker tillgängliga för digital utlåning. Dess bokmetadatakatalog är fritt tillgänglig för nedladdning och ingår i Annas Arkiv (dock inte för närvarande i sökningen, förutom om du uttryckligen söker efter ett Open Library-ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Utgåva 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Detta är en dump av många anrop till isbndb.com under september 2022. Vi försökte täcka alla ISBN-intervall. Dessa är cirka 30,9 miljoner poster. På deras webbplats hävdar de att de faktiskt har 32,6 miljoner poster, så vi kan ha missat några, eller <em>de</em> kan göra något fel."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON-svaren är i stort sett råa från deras server. Ett datakvalitetsproblem som vi märkte är att för ISBN-13-nummer som börjar med ett annat prefix än ”978-”, inkluderar de fortfarande ett ”isbn”-fält som helt enkelt är ISBN-13-numret med de första 3 siffrorna borttagna (och kontrollsiffran omberäknad). Detta är uppenbarligen fel, men det är så de verkar göra det, så vi ändrade det inte."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Ett annat potentiellt problem som du kan stöta på är att fältet ”isbn13” har dubbletter, så du kan inte använda det som en primärnyckel i en databas. Kombinationen av fälten ”isbn13”+”isbn” verkar dock vara unik."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "För bakgrundsinformation om Sci-Hub, vänligen hänvisa till dess <a %(a_scihub)s>officiella webbplats</a>, <a %(a_wikipedia)s>Wikipedia-sida</a> och denna <a %(a_radiolab)s>podcastintervju</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Observera att Sci-Hub har varit <a %(a_reddit)s>fryst sedan 2021</a>. Det var fryst tidigare, men 2021 lades några miljoner artiklar till. Fortfarande läggs ett begränsat antal artiklar till i Libgen \"scimag\"-samlingarna, men inte tillräckligt för att motivera nya bulk-torrenter."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Vi använder Sci-Hub metadata som tillhandahålls av <a %(a_libgen_li)s>Libgen.li</a> i dess \"scimag\"-samling. Vi använder också datasetet <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Observera att \"smarch\"-torrenterna är <a %(a_smarch)s>föråldrade</a> och därför inte ingår i vår torrentlista."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrenter på Annas Arkiv"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata och torrenter"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrenter på Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrenter på Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Uppdateringar på Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-sida"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcastintervju"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uppladdningar till Annas Arkiv"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Översikt från <a %(a1)s>datasets-sidan</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Olika mindre eller enstaka källor. Vi uppmuntrar folk att ladda upp till andra skuggbibliotek först, men ibland har folk samlingar som är för stora för andra att sortera igenom, men inte tillräckligt stora för att motivera en egen kategori."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "\"Upload\"-samlingen är uppdelad i mindre delsamlingar, som anges i AACIDs och torrentnamn. Alla delsamlingar deduplicerades först mot huvudsamlingen, även om metadatafilerna \"upload_records\" JSON fortfarande innehåller många referenser till de ursprungliga filerna. Icke-bokfiler togs också bort från de flesta delsamlingar och är vanligtvis <em>inte</em> noterade i \"upload_records\" JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Många underkollektioner består själva av under-underkollektioner (t.ex. från olika ursprungskällor), som representeras som kataloger i fältet “sökväg”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Underkollektionerna är:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Underkollektion"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Anteckningar"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "bläddra"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "sök"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Från <a %(a_href)s>aaaaarg.fail</a>. Verkar vara ganska komplett. Från vår volontär \"cgiym\"."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Från en <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Har ganska hög överlappning med befintliga papperssamlingar, men mycket få MD5-matcher, så vi bestämde oss för att behålla den helt."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Skrapning av <q>iRead eBooks</q> (= fonetiskt <q>ai rit i-books</q>; airitibooks.com), av volontär <q>j</q>. Motsvarar <q>airitibooks</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Från en samling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Delvis från den ursprungliga källan, delvis från the-eye.eu, delvis från andra speglar."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Från en privat boktorrentwebbplats, <a %(a_href)s>Bibliotik</a> (ofta kallad “Bib”), vars böcker paketerades i torrents efter namn (A.torrent, B.torrent) och distribuerades genom the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Från vår volontär “bpb9v”. För mer information om <a %(a_href)s>CADAL</a>, se anteckningarna på vår <a %(a_duxiu)s>DuXiu dataset-sida</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mer från vår volontär “bpb9v”, mestadels DuXiu-filer, samt en mapp “WenQu” och “SuperStar_Journals” (SuperStar är företaget bakom DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Från vår volontär \"cgiym\", kinesiska texter från olika källor (representerade som underkataloger), inklusive från <a %(a_href)s>China Machine Press</a> (en stor kinesisk förläggare)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Icke-kinesiska samlingar (representerade som underkataloger) från vår volontär “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Skrapning av böcker om kinesisk arkitektur, av volontär <q>cm</q>: <q>Jag fick det genom att utnyttja en nätverkssårbarhet på förlaget, men den luckan har sedan dess stängts</q>. Motsvarar <q>chinese_architecture</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Böcker från det akademiska förlaget <a %(a_href)s>De Gruyter</a>, samlade från några stora torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Skrapning av <a %(a_href)s>docer.pl</a>, en polsk fildelningswebbplats fokuserad på böcker och andra skriftliga verk. Skrapad i slutet av 2023 av volontär “p”. Vi har inte bra metadata från den ursprungliga webbplatsen (inte ens filtillägg), men vi filtrerade för bokliknande filer och kunde ofta extrahera metadata från filerna själva."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu-epubs, direkt från DuXiu, samlade av volontär \"w\". Endast nyligen utgivna DuXiu-böcker är tillgängliga direkt genom e-böcker, så de flesta av dessa måste vara nya."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Återstående DuXiu-filer från volontär \"m\", som inte var i DuXius proprietära PDG-format (den huvudsakliga <a %(a_href)s>DuXiu-datasetet</a>). Samlade från många ursprungliga källor, tyvärr utan att bevara dessa källor i filvägen."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Skrapning av erotiska böcker, av volontär <q>do no harm</q>. Motsvarar <q>hentai</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Samling skrapad från en japansk mangautgivare av volontär “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Utvalda rättsarkiv från Longquan</a>, tillhandahållna av volontären ”c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Skrapning av <a %(a_href)s>magzdb.org</a>, en allierad till Library Genesis (den är länkad på libgen.rs hemsida) men som inte ville tillhandahålla sina filer direkt. Erhållen av volontären ”p” i slutet av 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Olika små uppladdningar, för små för att vara en egen underkollektion, men representerade som kataloger."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-böcker från AvaxHome, en rysk fildelningswebbplats."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arkiv av tidningar och tidskrifter. Motsvarar <q>newsarch_magz</q> metadata i <a %(a1)s><q>Andra metadata-skrapningar</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Skrapning av <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Samling av volontären “o” som samlade polska böcker direkt från originalutgivningssidor (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Kombinerade samlingar av <a %(a_href)s>shuge.org</a> av volontärerna “cgiym” och “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (namngiven efter det fiktiva biblioteket), skrapad 2022 av volontären “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Under-under-kollektioner (representerade som kataloger) från volontären “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (av <a %(a_sikuquanshu)s>Dizhi(迪志)</a> i Taiwan), mebook (mebook.cc, 我的小书屋, mitt lilla bokrum — woz9ts: “Denna sida fokuserar främst på att dela högkvalitativa e-bokfiler, varav några är sätts av ägaren själv. Ägaren blev <a %(a_arrested)s>arresterad</a> 2019 och någon gjorde en samling av filer han delade.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Återstående DuXiu-filer från volontär \"woz9ts\", som inte var i DuXius proprietära PDG-format (ska fortfarande konverteras till PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents av Annas Arkiv"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library skrapning"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library har sina rötter i <a %(a_href)s>Library Genesis</a>-gemenskapen och startade ursprungligen med deras data. Sedan dess har det professionaliserats avsevärt och har ett mycket modernare gränssnitt. De kan därför få många fler donationer, både ekonomiskt för att fortsätta förbättra sin webbplats, samt donationer av nya böcker. De har samlat en stor samling utöver Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Uppdatering från februari 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "I slutet av 2022 arresterades de påstådda grundarna av Z-Library, och domäner beslagtogs av amerikanska myndigheter. Sedan dess har webbplatsen långsamt kommit tillbaka online. Det är okänt vem som för närvarande driver den."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Samlingen består av tre delar. De ursprungliga beskrivningssidorna för de två första delarna är bevarade nedan. Du behöver alla tre delar för att få all data (förutom ersatta torrents, som är överstrukna på torrentsidan)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: vår första utgåva. Detta var den allra första utgåvan av det som då kallades ”Pirate Library Mirror” (”pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: andra utgåvan, denna gång med alla filer inslagna i .tar-filer."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementella nya utgåvor, med hjälp av <a %(a_href)s>Annas Arkivs Containers (AAC) format</a>, nu utgivna i samarbete med Z-Library-teamet."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents av Annas Arkiv (metadata + innehåll)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Exempelpost på Annas Arkiv (ursprunglig samling)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Exempelpost på Annas Arkiv (”zlib3” samling)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Huvudwebbplats"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domän"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blogginlägg om Utgåva 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blogginlägg om Utgåva 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-utgåvor (ursprungliga beskrivningssidor)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Utgåva 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Den ursprungliga spegeln erhölls mödosamt under 2021 och 2022. Vid denna tidpunkt är den något föråldrad: den återspeglar samlingens tillstånd i juni 2021. Vi kommer att uppdatera detta i framtiden. Just nu fokuserar vi på att få ut denna första utgåva."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Eftersom Library Genesis redan är bevarad med offentliga torrents och ingår i Z-Library, gjorde vi en grundläggande deduplicering mot Library Genesis i juni 2022. För detta använde vi MD5-hashar. Det finns sannolikt mycket mer duplicerat innehåll i biblioteket, såsom flera filformat med samma bok. Detta är svårt att upptäcka exakt, så vi gör det inte. Efter dedupliceringen har vi över 2 miljoner filer kvar, totalt strax under 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Samlingen består av två delar: en MySQL ”.sql.gz” dump av metadata, och de 72 torrentfilerna på runt 50-100GB vardera. Metadatan innehåller data som rapporterats av Z-Library-webbplatsen (titel, författare, beskrivning, filtyp), samt den faktiska filstorleken och md5sum som vi observerade, eftersom dessa ibland inte stämmer överens. Det verkar finnas filer för vilka Z-Library själv har felaktig metadata. Vi kan också ha laddat ner filer felaktigt i vissa enskilda fall, vilket vi kommer att försöka upptäcka och åtgärda i framtiden."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "De stora torrentfilerna innehåller den faktiska bokdatan, med Z-Library ID som filnamn. Filändelserna kan rekonstrueras med hjälp av metadata-dumpen."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Samlingen är en blandning av facklitteratur och skönlitteratur (inte separerade som i Library Genesis). Kvaliteten varierar också mycket."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Denna första utgåva är nu fullt tillgänglig. Observera att torrentfilerna endast är tillgängliga via vår Tor-spegel."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Utgåva 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Vi har fått alla böcker som lades till i Z-Library mellan vår senaste spegel och augusti 2022. Vi har också gått tillbaka och skrapat några böcker som vi missade första gången. Sammantaget är denna nya samling cirka 24TB. Återigen är denna samling deduplicerad mot Library Genesis, eftersom det redan finns torrents tillgängliga för den samlingen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Datan är organiserad på liknande sätt som den första utgåvan. Det finns en MySQL “.sql.gz”-dump av metadata, som även inkluderar all metadata från den första utgåvan, vilket därmed ersätter den. Vi har också lagt till några nya kolumner:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: om denna fil redan finns i Library Genesis, antingen i facklitteratur- eller skönlitteratursamlingen (matchad med md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: vilken torrent denna fil finns i."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: satt när vi inte kunde ladda ner boken."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Vi nämnde detta förra gången, men bara för att klargöra: “filnamn” och “md5” är de faktiska egenskaperna hos filen, medan “filnamn_rapporterat” och “md5_rapporterat” är vad vi skrapade från Z-Library. Ibland stämmer dessa två inte överens, så vi inkluderade båda."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "För denna utgåva ändrade vi sorteringen till “utf8mb4_unicode_ci”, vilket bör vara kompatibelt med äldre versioner av MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Datafilerna är liknande som förra gången, men de är mycket större. Vi orkade helt enkelt inte skapa massor av mindre torrentfiler. \"pilimi-zlib2-0-14679999-extra.torrent\" innehåller alla filer som vi missade i den senaste utgåvan, medan de andra torrenterna är alla nya ID-intervall. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Uppdatering %(date)s:</strong> Vi gjorde de flesta av våra torrenter för stora, vilket orsakade problem för torrentklienter. Vi har tagit bort dem och släppt nya torrenter."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Uppdatering %(date)s:</strong> Det var fortfarande för många filer, så vi packade dem i tar-filer och släppte nya torrenter igen."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Utgåva 2 tillägg (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Detta är en enda extra torrentfil. Den innehåller ingen ny information, men den har viss data som kan ta tid att beräkna. Det gör det bekvämt att ha, eftersom nedladdning av denna torrent ofta är snabbare än att beräkna den från grunden. Speciellt innehåller den SQLite-index för tar-filerna, för användning med <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Vanliga frågor (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Vad är Annas Arkiv?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Annas Arkiv</span> är ett ideellt projekt med två mål:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Bevarande:</strong> Säkerhetskopiera all mänsklighetens kunskap och kultur.</li><li><strong>Tillgång:</strong> Göra denna kunskap och kultur tillgänglig för alla i världen.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "All vår <a %(a_code)s>kod</a> och <a %(a_datasets)s>data</a> är helt öppen källkod."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Bevarande"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Vi bevarar böcker, artiklar, serier, tidskrifter och mer, genom att samla dessa material från olika <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">skuggbibliotek</a>, officiella bibliotek och andra samlingar på ett ställe. All denna data bevaras för alltid genom att göra det enkelt att duplicera den i bulk — med hjälp av torrents — vilket resulterar i många kopior runt om i världen. Vissa skuggbibliotek gör redan detta själva (t.ex. Sci-Hub, Library Genesis), medan Annas Arkiv ”befriar” andra bibliotek som inte erbjuder bulkdistribution (t.ex. Z-Library) eller inte är skuggbibliotek alls (t.ex. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Denna breda distribution, kombinerad med öppen källkod, gör vår webbplats motståndskraftig mot nedtagningar och säkerställer långsiktig bevarande av mänsklighetens kunskap och kultur. Läs mer om <a href=\"/datasets\">våra datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Vi uppskattar att vi har bevarat cirka <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% av världens böcker</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Åtkomst"

#, fuzzy
msgid "page.home.access.text"
msgstr "Vi arbetar med partners för att göra våra samlingar lättillgängliga och gratis för alla. Vi tror att alla har rätt till mänsklighetens samlade visdom. Och <a %(a_search)s>inte på bekostnad av författarna</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Timvisa nedladdningar de senaste 30 dagarna. Timvis genomsnitt: %(hourly)s. Dagligt genomsnitt: %(daily)s."

msgid "page.about.text2"
msgstr "Vi är hängivna anhängare av ett fritt informationsflöde, och av att bevara kunskap samt kultur. Med den här sökmotorn bygger vi vidare på andras stordåd. Vi beundrar det hårda arbete som andra skapare av skuggbibliotek lagt ner, och vi hoppas att sökmotorn kan öka deras räckvidd."

msgid "page.about.text3"
msgstr "För att hålla dig uppdaterad om arbetet, följ Anna på <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> eller på <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.. Om du har frågor och feedback kontakta Anna på %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Hur kan jag hjälpa till?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Följ oss på <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, eller <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Sprid ordet om Annas Arkiv på Twitter, Reddit, Tiktok, Instagram, på ditt lokala café eller bibliotek, eller var du än går! Vi tror inte på att hålla saker hemliga — om vi blir nedtagna kommer vi bara att dyka upp någon annanstans, eftersom all vår kod och data är helt öppen källkod.</li><li>3. Om du kan, överväg att <a href=\"/donate\">donera</a>.</li><li>4. Hjälp till att <a href=\"https://translate.annas-software.org/\">översätta</a> vår webbplats till olika språk.</li><li>5. Om du är mjukvaruingenjör, överväg att bidra till vår <a href=\"https://annas-software.org/\">öppna källkod</a>, eller seed våra <a href=\"/datasets\">torrenter</a>.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Vi har nu också en synkroniserad Matrix-kanal på %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Om du är en säkerhetsforskare kan vi använda dina färdigheter både för offensiv och defensiv. Kolla in vår <a %(a_security)s>Säkerhet</a>-sida."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Vi söker experter på betalningar för anonyma handlare. Kan du hjälpa oss att lägga till fler bekväma sätt att donera? PayPal, WeChat, presentkort. Om du känner någon, vänligen kontakta oss."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Vi letar alltid efter mer serverkapacitet."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Du kan hjälpa till genom att rapportera filproblem, lämna kommentarer och skapa listor direkt på denna webbplats. Du kan också hjälpa till genom att <a %(a_upload)s>ladda upp fler böcker</a>, eller fixa filproblem eller formatering av befintliga böcker."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Skapa eller hjälp till att underhålla Wikipedia-sidan för Annas Arkiv på ditt språk."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Vi letar efter små, smakfulla annonser. Om du vill annonsera på Annas Arkiv, vänligen meddela oss."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Vi skulle älska om folk satte upp <a %(a_mirrors)s>speglar</a>, och vi kommer att stödja detta ekonomiskt."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "För mer omfattande information om hur du kan bli volontär, se vår <a %(a_volunteering)s>Volontär- och Belöningssida</a>."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Varför är de långsamma nedladdningarna så långsamma?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Vi har bokstavligen inte tillräckligt med resurser för att ge alla i världen höguppladdningshastigheter, hur mycket vi än skulle vilja. Om en rik välgörare skulle vilja stiga fram och tillhandahålla detta för oss, skulle det vara otroligt, men tills dess gör vi vårt bästa. Vi är ett ideellt projekt som knappt kan upprätthålla sig självt genom donationer."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Det är därför vi har implementerat två system för gratis nedladdningar, med våra partners: delade servrar med långsamma nedladdningar, och något snabbare servrar med en väntelista (för att minska antalet personer som laddar ner samtidigt)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Vi har också <a %(a_verification)s>webbläsarverifiering</a> för våra långsamma nedladdningar, eftersom bots och scrapers annars skulle missbruka dem, vilket gör det ännu långsammare för legitima användare."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Observera att när du använder Tor Browser kan du behöva justera dina säkerhetsinställningar. På den lägsta av alternativen, kallad ”Standard”, lyckas Cloudflare turnstile-utmaningen. På de högre alternativen, kallade ”Säkrare” och ”Säkraste”, misslyckas utmaningen."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "För stora filer kan ibland långsamma nedladdningar avbrytas i mitten. Vi rekommenderar att du använder en nedladdningshanterare (som JDownloader) för att automatiskt återuppta stora nedladdningar."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Vanliga frågor om donationer"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Förnyas medlemskap automatiskt?</div> Medlemskap <strong>förnyas inte</strong> automatiskt. Du kan gå med så länge eller kort som du vill."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kan jag uppgradera mitt medlemskap eller få flera medlemskap?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Har ni andra betalmetoder?</div> För närvarande inte. Det finns många personer som inte vill att arkiv som detta ska existera, så vi måste vara försiktiga. Om du kan hjälpa oss att säkert anordna (mer praktiska) betalmetoder, vänligen kontakta oss på %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Vad betyder intervallen per månad?</div> Du kan nå den lägre sidan av ett intervall genom att tillämpa alla rabatter, såsom att välja en period längre än en månad."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Vad används donationerna till?</div> 100%% går till att bevara och göra världens kunskap och kultur tillgänglig. För närvarande spenderar vi mestadels på servrar, lagring och bandbredd. Inga pengar går till några teammedlemmar personligen."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kan jag göra en stor donation?</div> Det skulle vara fantastiskt! För donationer över några tusen dollar, vänligen kontakta oss på %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kan jag göra en donation utan att bli medlem?</div> Självklart. Vi accepterar donationer av valfri summa på denna Monero (XMR) adress: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Hur laddar jag upp nya böcker?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternativt kan du ladda upp dem till Z-Library <a %(a_upload)s>här</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "För små uppladdningar (upp till 10 000 filer) vänligen ladda upp dem till både %(first)s och %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "För tillfället föreslår vi att ladda upp nya böcker till Library Genesis forks. Här är en <a %(a_guide)s>praktisk guide</a>. Observera att båda forks som vi indexerar på denna webbplats hämtar från detta samma uppladdningssystem."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "För Libgen.li, se till att först logga in på <a %(a_forum)s >deras forum</a> med användarnamn %(username)s och lösenord %(password)s, och återvänd sedan till deras <a %(a_upload_page)s >uppladdningssida</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "Om din e-postadress inte fungerar på Libgen-forumen rekommenderar vi att du använder <a %(a_mail)s>Proton Mail</a> (gratis). Du kan också <a %(a_manual)s>manuellt begära</a> att ditt konto aktiveras."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Observera att mhut.org blockerar vissa IP-intervall, så en VPN kan behövas."

#, fuzzy
msgid "page.upload.large.text"
msgstr "För stora uppladdningar (över 10 000 filer) som inte accepteras av Libgen eller Z-Library, vänligen kontakta oss på %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "För att ladda upp akademiska artiklar, vänligen ladda upp dem till <a %(a_stc_nexus)s>STC Nexus</a> (förutom till Library Genesis). De är det bästa skuggarkivet för nya artiklar. Vi har inte integrerat dem ännu, men vi kommer att göra det vid något tillfälle. Du kan använda deras <a %(a_telegram)s>uppladdningsbot på Telegram</a>, eller kontakta adressen som anges i deras fästa meddelande om du har för många filer för att ladda upp på detta sätt."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Hur begär jag böcker?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "För närvarande kan vi inte tillgodose bokförfrågningar."

#, fuzzy
msgid "page.request.forums"
msgstr "Vänligen gör dina förfrågningar på Z-Library eller Libgen-forum."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Skicka inte bokförfrågningar via e-post till oss."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Samlar ni metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Det gör vi verkligen."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Jag laddade ner 1984 av George Orwell, kommer polisen att komma till min dörr?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Oroa dig inte för mycket, det är många som laddar ner från webbplatser som vi länkar till, och det är extremt sällsynt att hamna i trubbel. Men för att vara säker rekommenderar vi att använda en VPN (betald), eller <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Hur sparar jag mina sökinställningar?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Välj de inställningar du gillar, håll sökrutan tom, klicka på “Sök” och bokmärk sedan sidan med din webbläsares bokmärkesfunktion."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Har ni en mobilapp?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Vi har ingen officiell mobilapp, men du kan installera denna webbplats som en app."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klicka på menyn med tre punkter uppe till höger och välj \"Lägg till på hemskärmen\"."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klicka på \"Dela\"-knappen längst ner och välj \"Lägg till på hemskärmen\"."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Har ni ett API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Vi har ett stabilt JSON API för medlemmar, för att få en snabb nedladdnings-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentation inom JSON själv)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "För andra användningsfall, som att iterera genom alla våra filer, bygga anpassade sökningar, och så vidare, rekommenderar vi att <a %(a_generate)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Rådata kan manuellt utforskas <a %(a_explore)s>genom JSON-filer</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Vår råa torrentlista kan laddas ner som <a %(a_torrents)s>JSON</a> också."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Vanliga frågor om torrents"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Jag vill gärna hjälpa till att seeda, men jag har inte mycket diskutrymme."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Använd <a %(a_list)s>torrentlistgeneratorn</a> för att skapa en lista över torrents som mest behöver seedas, inom dina lagringsgränser."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrenterna är för långsamma; kan jag ladda ner data direkt från er?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ja, se sidan för <a %(a_llm)s>LLM-data</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Kan jag ladda ner endast en delmängd av filerna, som bara ett visst språk eller ämne?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kort svar: inte enkelt."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Långt svar:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "De flesta torrents innehåller filerna direkt, vilket innebär att du kan instruera torrentklienter att endast ladda ner de nödvändiga filerna. För att bestämma vilka filer som ska laddas ner kan du <a %(a_generate)s>generera</a> vår metadata, eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Tyvärr innehåller ett antal torrentkollektioner .zip- eller .tar-filer i roten, vilket innebär att du måste ladda ner hela torrenten innan du kan välja enskilda filer."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Vi har dock <a %(a_ideas)s>några idéer</a> för det senare fallet.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Inga lättanvända verktyg för att filtrera torrents är tillgängliga ännu, men vi välkomnar bidrag."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Hur hanterar ni dubbletter i torrenterna?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Vi försöker hålla minimal duplicering eller överlappning mellan torrenterna i denna lista, men detta kan inte alltid uppnås och beror mycket på källbibliotekens policyer. För bibliotek som släpper sina egna torrenter ligger det utanför vår kontroll. För torrenter som släpps av Annas Arkiv deduplicerar vi endast baserat på MD5-hash, vilket innebär att olika versioner av samma bok inte dedupliceras."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Kan jag få torrentlistan som JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ja."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Jag ser inga PDF- eller EPUB-filer i torrenterna, bara binära filer? Vad gör jag?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Dessa är faktiskt PDF- och EPUB-filer, de har bara ingen filändelse i många av våra torrenter. Det finns två ställen där du kan hitta metadata för torrentfiler, inklusive filtyper/ändelser:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Varje samling eller utgåva har sin egen metadata. Till exempel har <a %(a_libgen_nonfic)s>Libgen.rs-torrenter</a> en motsvarande metadatabas som är värd på Libgen.rs-webbplatsen. Vi länkar vanligtvis till relevanta metadataresurser från varje samlings <a %(a_datasets)s>datasettsida</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Vi rekommenderar att <a %(a_generate)s>generera</a> eller <a %(a_download)s>ladda ner</a> våra ElasticSearch- och MariaDB-databaser. Dessa innehåller en mappning för varje post i Annas Arkiv till dess motsvarande torrentfiler (om tillgängliga), under \"torrent_paths\" i ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Varför kan min torrentklient inte öppna några av era torrentfiler / magnetlänkar?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Vissa torrentklienter stöder inte stora bitstorlekar, vilket många av våra torrents har (för nyare gör vi inte detta längre — även om det är giltigt enligt specifikationerna!). Så prova en annan klient om du stöter på detta, eller klaga till tillverkarna av din torrentklient."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Har ni ett program för ansvarsfull rapportering?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Vi välkomnar säkerhetsforskare att söka efter sårbarheter i våra system. Vi är stora förespråkare av ansvarsfull rapportering. Kontakta oss <a %(a_contact)s>här</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Vi kan för närvarande inte erbjuda bug bounties, förutom för sårbarheter som har <a %(a_link)s>potential att kompromettera vår anonymitet</a>, för vilka vi erbjuder bounties i intervallet $10k-50k. Vi skulle vilja erbjuda bredare scope för bug bounties i framtiden! Observera att sociala ingenjörsattacker är utanför scope."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Om du är intresserad av offensiv säkerhet och vill hjälpa till att arkivera världens kunskap och kultur, se till att kontakta oss. Det finns många sätt på vilka du kan hjälpa till."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Finns det fler resurser om Annas Arkiv?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Annas Blogg</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — regelbundna uppdateringar"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Annas Mjukvara</a> — vår öppen källkod"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Översätt på Annas Arkiv</a> — vårt översättningssystem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — om data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativa domäner"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mer om oss (hjälp gärna till att hålla denna sida uppdaterad, eller skapa en för ditt eget språk!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Hur rapporterar jag upphovsrättsintrång?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Vi är inte värdar för något upphovsrättsskyddat material här. Vi är en sökmotor och indexerar därför endast metadata som redan är offentligt tillgänglig. När du laddar ner från dessa externa källor föreslår vi att du kontrollerar lagarna i din jurisdiktion angående vad som är tillåtet. Vi är inte ansvariga för innehåll som värdas av andra."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Om du har klagomål om vad du ser här, är ditt bästa alternativ att kontakta den ursprungliga webbplatsen. Vi uppdaterar regelbundet deras ändringar i vår databas. Om du verkligen tror att du har ett giltigt DMCA-klagomål som vi bör svara på, fyll i <a %(a_copyright)s>DMCA / Copyright-anspråksformuläret</a>. Vi tar dina klagomål på allvar och kommer att återkomma till dig så snart som möjligt."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Jag hatar hur ni driver detta projekt!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Vi vill också påminna alla om att all vår kod och data är helt öppen källkod. Detta är unikt för projekt som vårt — vi känner inte till något annat projekt med en lika massiv katalog som också är helt öppen källkod. Vi välkomnar verkligen alla som tycker att vi driver vårt projekt dåligt att ta vår kod och data och sätta upp sitt eget skuggarkiv! Vi säger inte detta av illvilja eller något — vi tycker verkligen att detta skulle vara fantastiskt eftersom det skulle höja ribban för alla och bättre bevara mänsklighetens arv."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Har du en upptidövervakare?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Vänligen se <a %(a_href)s>detta utmärkta projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Hur donerar jag böcker eller annat fysiskt material?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Vänligen skicka dem till <a %(a_archive)s>Internet Archive</a>. De kommer att bevara dem på rätt sätt."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Vem är Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Du är Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Vilka är dina favoritböcker?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Här är några böcker som har särskild betydelse för skuggarkiv och digital bevarande:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Du har slut på snabba nedladdningar idag."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Bli medlem för att använda snabba nedladdningar."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Vi stödjer nu Amazon-presentkort, kredit- och betalkort, krypto, Alipay och WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Fullständig databas"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Böcker, artiklar, tidskrifter, serier, biblioteksregister, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Sök"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub har <a %(a_paused)s>pausat</a> uppladdningen av nya artiklar."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB är en fortsättning på Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direkt tillgång till %(count)s akademiska artiklar"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Öppna"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Om du är en <a %(a_member)s>medlem</a>, krävs ingen webbläsarverifiering."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Långtidsarkiv"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Dataseten som används i Annas Arkiv är helt öppna och kan speglas i bulk med hjälp av torrents. <a %(a_datasets)s>Läs mer…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Du kan hjälpa till enormt genom att seeda torrents. <a %(a_torrents)s>Läs mer…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Vi har världens största samling av högkvalitativ textdata. <a %(a_llm)s>Läs mer…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Speglar: uppmaning till volontärer"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Letar efter volontärer"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Som ett ideellt, öppen källkodsprojekt letar vi alltid efter personer som kan hjälpa till."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Om du driver en högrisk anonym betalningsprocessor, vänligen kontakta oss. Vi söker också personer som vill placera smakfulla små annonser. Alla intäkter går till våra bevarandeinsatser."

msgid "layout.index.header.nav.annasblog"
msgstr "Annas blogg ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS-nedladdningar"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Alla nedladdningslänkar för denna fil: <a %(a_main)s>Filens huvudsida</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(du kan behöva prova fler gånger med IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Om du vill ha snabbare nedladdningar och slippa webbläsarkontroll, <a %(a_membership)s>bli medlem</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 För bulk-spegling av vår samling, kolla in sidorna för <a %(a_datasets)s>Datasets</a> och <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-data"

#, fuzzy
msgid "page.llm.intro"
msgstr "Det är välkänt att LLM:er trivs med högkvalitativ data. Vi har världens största samling av böcker, artiklar, tidskrifter, etc., som är några av de högsta kvalitetstextkällorna."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unik skala och räckvidd"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Vår samling innehåller över hundra miljoner filer, inklusive akademiska tidskrifter, läroböcker och tidskrifter. Vi uppnår denna skala genom att kombinera stora befintliga arkiv."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Några av våra källsamlingar är redan tillgängliga i bulk (Sci-Hub och delar av Libgen). Andra källor har vi befriat själva. <a %(a_datasets)s>Datasets</a> visar en fullständig översikt."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Vår samling inkluderar miljontals böcker, artiklar och tidskrifter från tiden före e-boken. Stora delar av denna samling har redan OCR:ats och har redan lite intern överlappning."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Hur vi kan hjälpa"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Vi kan tillhandahålla höghastighetsåtkomst till våra fullständiga samlingar, samt till oavslöjade samlingar."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Detta är företagsnivååtkomst som vi kan tillhandahålla för donationer i storleksordningen tiotusentals USD. Vi är också villiga att byta detta mot högkvalitativa samlingar som vi ännu inte har."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Vi kan återbetala dig om du kan förse oss med berikning av vår data, såsom:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Ta bort överlappning (deduplicering)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Text- och metadatautvinning"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Stöd långsiktig arkivering av mänsklig kunskap, samtidigt som du får bättre data för din modell!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontakta oss</a> för att diskutera hur vi kan samarbeta."

#, fuzzy
msgid "page.login.continue"
msgstr "Fortsätt"

#, fuzzy
msgid "page.login.please"
msgstr "Vänligen <a %(a_account)s>logga in</a> för att se denna sida.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Annas Arkiv är tillfälligt nere för underhåll. Vänligen kom tillbaka om en timme."

#, fuzzy
msgid "page.metadata.header"
msgstr "Förbättra metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Du kan hjälpa till med att bevara böcker genom att förbättra metadata! Läs först bakgrunden om metadata på Annas Arkiv, och lär dig sedan hur du förbättrar metadata genom att länka med Open Library, och få gratis medlemskap på Annas Arkiv."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Bakgrund"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "När du tittar på en bok på Annas Arkiv kan du se olika fält: titel, författare, förlag, upplaga, år, beskrivning, filnamn och mer. All denna information kallas <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Eftersom vi kombinerar böcker från olika <em>källbibliotek</em>, visar vi den metadata som finns tillgänglig i det källbiblioteket. Till exempel, för en bok som vi fått från Library Genesis, visar vi titeln från Library Genesis’ databas."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Ibland finns en bok i <em>flera</em> källbibliotek, som kan ha olika metadatafält. I så fall visar vi helt enkelt den längsta versionen av varje fält, eftersom den förhoppningsvis innehåller den mest användbara informationen! Vi visar fortfarande de andra fälten under beskrivningen, t.ex. som ”alternativ titel” (men bara om de är olika)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Vi extraherar också <em>koder</em> som identifierare och klassificerare från källbiblioteket. <em>Identifierare</em> representerar unikt en viss upplaga av en bok; exempel är ISBN, DOI, Open Library ID, Google Books ID eller Amazon ID. <em>Klassificerare</em> grupperar flera liknande böcker; exempel är Dewey Decimal (DCC), UDC, LCC, RVK eller GOST. Ibland är dessa koder explicit länkade i källbiblioteken, och ibland kan vi extrahera dem från filnamnet eller beskrivningen (främst ISBN och DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Vi kan använda identifierare för att hitta poster i <em>metadata-samlingar</em>, såsom OpenLibrary, ISBNdb eller WorldCat/OCLC. Det finns en specifik <em>metadata-flik</em> i vår sökmotor om du vill bläddra i dessa samlingar. Vi använder matchande poster för att fylla i saknade metadatafält (t.ex. om en titel saknas), eller t.ex. som ”alternativ titel” (om det finns en befintlig titel)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "För att se exakt var metadata för en bok kom ifrån, se fliken <em>“Tekniska detaljer”</em> på en boksida. Den har en länk till rå JSON för den boken, med pekare till rå JSON för de ursprungliga posterna."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "För mer information, se följande sidor: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Sök (metadata-flik)</a>, <a %(a_codes)s>Koder Explorer</a>, och <a %(a_example)s>Exempel på metadata JSON</a>. Slutligen kan all vår metadata <a %(a_generated)s>genereras</a> eller <a %(a_downloaded)s>laddas ner</a> som ElasticSearch- och MariaDB-databaser."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Länkning till Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Så om du stöter på en fil med dålig metadata, hur ska du fixa det? Du kan gå till källbiblioteket och följa dess procedurer för att fixa metadata, men vad ska du göra om en fil finns i flera källbibliotek?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Det finns en identifierare som behandlas speciellt på Annas Arkiv. <strong>Fältet annas_archive md5 på Open Library åsidosätter alltid all annan metadata!</strong> Låt oss backa lite först och lära oss om Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library grundades 2006 av Aaron Swartz med målet att ”en webbsida för varje bok som någonsin publicerats”. Det är som en Wikipedia för bokmetadata: alla kan redigera den, den är fritt licensierad och kan laddas ner i bulk. Det är en bokdatabas som mest överensstämmer med vårt uppdrag — faktiskt har Annas Arkiv inspirerats av Aaron Swartz’ vision och liv."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Istället för att uppfinna hjulet på nytt, beslutade vi att omdirigera våra volontärer till Open Library. Om du ser en bok som har felaktig metadata, kan du hjälpa till på följande sätt:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Gå till <a %(a_openlib)s>Open Librarys webbplats</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Hitta rätt bokpost. <strong>VARNING:</strong> var noga med att välja rätt <strong>upplaga</strong>. I Open Library har du ”verk” och ”upplagor”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Ett ”verk” kan vara ”Harry Potter och de vises sten”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "En ”upplaga” kan vara:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Första utgåvan från 1997 publicerad av Bloomsbery med 256 sidor."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Pocketutgåvan från 2003 publicerad av Raincoast Books med 223 sidor."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Den polska översättningen från 2000 ”Harry Potter I Kamie Filozoficzn” av Media Rodzina med 328 sidor."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Alla dessa utgåvor har olika ISBN och olika innehåll, så se till att välja rätt!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Redigera posten (eller skapa den om ingen finns), och lägg till så mycket användbar information som möjligt! Du är ju här nu ändå, så du kan lika gärna göra posten riktigt fantastisk."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Under ”ID-nummer” välj ”Annas Arkiv” och lägg till bokens MD5 från Annas Arkiv. Detta är den långa strängen av bokstäver och siffror efter ”/md5/” i URL:en."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Försök hitta andra filer i Annas Arkiv som också matchar denna post, och lägg till dem också. I framtiden kan vi gruppera dessa som dubbletter på Annas Arkivs söksida."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "När du är klar, skriv ner URL:en som du just uppdaterade. När du har uppdaterat minst 30 poster med Annas Arkivs MD5:or, skicka oss ett <a %(a_contact)s>email</a> och skicka oss listan. Vi ger dig ett gratis medlemskap för Annas Arkiv, så att du lättare kan göra detta arbete (och som ett tack för din hjälp). Dessa måste vara högkvalitativa redigeringar som lägger till betydande mängder information, annars kommer din begäran att avvisas. Din begäran kommer också att avvisas om någon av redigeringarna blir återkallade eller korrigerade av Open Library-moderatorer."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Observera att detta endast fungerar för böcker, inte akademiska artiklar eller andra typer av filer. För andra typer av filer rekommenderar vi fortfarande att hitta källbiblioteket. Det kan ta några veckor för ändringar att inkluderas i Annas Arkiv, eftersom vi behöver ladda ner den senaste Open Library-datadumpen och återskapa vår sökindex."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spegelservrar: upprop för volontärer"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "För att öka motståndskraften hos Annas Arkiv söker vi volontärer för att driva spegelservrar."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Vi letar efter detta:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Du driver Anna’s Archive öppen källkod, och du uppdaterar regelbundet både koden och datan."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Din version är tydligt utmärkt som en spegel, t.ex. ”Bobs Arkiv, en Anna’s Archive-spegel”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Du är villig att ta de risker som är förknippade med detta arbete, vilka är betydande. Du har en djup förståelse för den operativa säkerhet som krävs. Innehållet i <a %(a_shadow)s>dessa</a> <a %(a_pirate)s>inlägg</a> är självklara för dig."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Du är villig att bidra till vår <a %(a_codebase)s>kodbas</a> — i samarbete med vårt team — för att göra detta möjligt."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Inledningsvis kommer vi inte att ge dig tillgång till våra partnerservers nedladdningar, men om allt går bra kan vi dela det med dig."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Värdkostnader"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Vi är villiga att täcka värd- och VPN-kostnader, initialt upp till $200 per månad. Detta är tillräckligt för en grundläggande sökserver och en DMCA-skyddad proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Vi kommer endast att betala för värd när du har allt uppsatt och har visat att du kan hålla arkivet uppdaterat med uppdateringar. Detta innebär att du måste betala för de första 1-2 månaderna ur egen ficka."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Din tid kommer inte att kompenseras (och inte heller vår), eftersom detta är rent volontärarbete."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Om du blir betydligt involverad i utvecklingen och driften av vårt arbete, kan vi diskutera att dela mer av donationsintäkterna med dig, för att du ska kunna använda dem vid behov."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Komma igång"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Vänligen <strong>kontakta oss inte</strong> för att be om tillstånd eller för grundläggande frågor. Handlingar talar högre än ord! All information finns där ute, så sätt bara igång med att sätta upp din spegel."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Känn dig fri att posta biljetter eller sammanslagningsförfrågningar till vår Gitlab när du stöter på problem. Vi kan behöva bygga några spegelspecifika funktioner med dig, såsom omprofilering från ”Anna’s Archive” till ditt webbplatsnamn, (initialt) inaktivera användarkonton, eller länka tillbaka till vår huvudsida från boksidor."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "När du har din spegel igång, vänligen kontakta oss. Vi skulle gärna vilja granska din OpSec, och när den är solid, kommer vi att länka till din spegel och börja arbeta närmare tillsammans med dig."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Tack på förhand till alla som är villiga att bidra på detta sätt! Det är inte för de svaghjärtade, men det skulle stärka livslängden för det största verkligt öppna biblioteket i mänsklighetens historia."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Ladda ner från partnerwebbplats"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Långsamma nedladdningar är enbart tillgängliga genom den officiella hemsidan. Besök %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Långsamma nedladdningar är inte tillgängliga via Cloudflare VPN eller från Cloudflare IP-adresser."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Vänligen vänta <span %(span_countdown)s>%(wait_seconds)s</span> sekunder för att ladda ner denna fil."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Använd denna URL för att ladda ner: <a %(a_download)s>Ladda ner nu</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Tack för att du väntar, detta håller webbplatsen tillgänglig gratis för alla! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Varning: det har gjorts många nedladdningar från din IP-adress det senaste dygnet. Det kan därför gå långsammare än vanligt."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Nedladdningar från din IP-adress under de senaste 24 timmarna: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Om du använder en VPN, delad internetanslutning eller din ISP delar IP-adresser, kan denna varning bero på det."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "För att ge alla en möjlighet att ladda ner filer gratis, måste du vänta innan du kan ladda ner denna fil."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Fortsätt gärna att bläddra i Annas Arkiv i en annan flik medan du väntar (om din webbläsare stöder uppdatering av bakgrundsflikar)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Vänta gärna på att flera nedladdningssidor laddas samtidigt (men ladda bara ner en fil åt gången per server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "När du får en nedladdningslänk är den giltig i flera timmar."

msgid "layout.index.header.title"
msgstr "Annas arkiv"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Post i Annas Arkiv"

msgid "page.scidb.download"
msgstr "Nerladdning"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "För att stödja tillgängligheten och långsiktig bevarande av mänsklig kunskap, bli en <a %(a_donate)s>medlem</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Som en bonus, 🧬&nbsp;SciDB laddar snabbare för medlemmar, utan några begränsningar."

msgid "page.scidb.refresh"
msgstr "Funkar det inte? Testa att <a %(a_refresh)s>ladda om sidan</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Ingen förhandsvisning tillgänglig ännu. Ladda ner filen från <a %(a_path)s>Annas Arkiv</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB är en fortsättning på Sci-Hub, med dess välbekanta gränssnitt och direkt visning av PDF-filer. Ange din DOI för att visa."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Vi har hela Sci-Hub-samlingen, samt nya artiklar. De flesta kan ses direkt med ett välbekant gränssnitt, liknande Sci-Hub. Vissa kan laddas ner via externa källor, i sådana fall visar vi länkar till dessa."

msgid "page.search.title.results"
msgstr "%(search_input)s - Sök"

msgid "page.search.title.new"
msgstr "Ny sökning"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Inkludera endast"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Exkludera"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Omarkerad"

msgid "page.search.tabs.download"
msgstr "Nerladdning"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Tidskriftsartiklar"

msgid "page.search.tabs.digital_lending"
msgstr "Digitala lån"

msgid "page.search.tabs.metadata"
msgstr "Metadata"

msgid "common.search.placeholder"
msgstr "Titel, författare, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "Sök"

msgid "page.search.search_settings"
msgstr "Sökinställningar"

msgid "page.search.submit"
msgstr "Sök"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Sökningen tog för lång tid, vilket är vanligt för breda sökningar. Filterräkningarna kan vara felaktiga."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Sökningen tog för lång tid, vilket innebär att du kan se felaktiga resultat. Ibland kan <a %(a_reload)s>omladdning</a> av sidan hjälpa."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Visa"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lista"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabell"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avancerad"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Sökbeskrivningar och metadata-kommentarer"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Lägg till specifikt sökfält"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(sök specifikt fält)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Utgivningsår"

msgid "page.search.filters.content.header"
msgstr "Innehåll"

msgid "page.search.filters.filetype.header"
msgstr "Filtyp"

msgid "page.search.more"
msgstr "mer…"

msgid "page.search.filters.access.header"
msgstr "Tillgång"

msgid "page.search.filters.source.header"
msgstr "Källa"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "skrapad och öppen källkod av AA"

msgid "page.search.filters.language.header"
msgstr "Språk"

msgid "page.search.filters.order_by.header"
msgstr "Sortera efter"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Mest relevanta"

msgid "page.search.filters.sorting.newest"
msgstr "Nyaste"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publiceringsår)"

msgid "page.search.filters.sorting.oldest"
msgstr "Äldst"

msgid "page.search.filters.sorting.largest"
msgstr "Största"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(filstorlek)"

msgid "page.search.filters.sorting.smallest"
msgstr "Minsta"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(open sourced)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Slumpmässig"

msgid "page.search.header.update_info"
msgstr "Sökindexet uppdateras varje månad. Den innehåller för närvarande poster upp till %(last_data_refresh_date)s. Mer tekniska detaljer finns på %(link_open_tag)sdatauppsättningar</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "För att utforska sökindexet med koder, använd <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Skriv i rutan för att söka i vår katalog med %(count)s direkt nedladdningsbara filer, som vi <a %(a_preserve)s>bevarar för alltid</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Faktum är att vem som helst kan hjälpa till att bevara dessa filer genom att seeda vår <a %(a_torrents)s>enhetliga lista över torrents</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Vi har för närvarande världens mest omfattande öppna katalog av böcker, artiklar och andra skriftliga verk. Vi speglar Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>och mer</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Om du hittar andra “skuggbibliotek” som vi borde spegla, eller om du har frågor, kontakta oss på %(email)s."

msgid "page.search.results.dmca"
msgstr "För DMCA / upphovsrättsanspråk <a %(a_copyright)s>klicka här</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tips: använd kortkommandon “/” (sökfokus), “enter” (sök), “j” (upp), “k” (ner), “<” (föregående sida), “>” (nästa sida) för snabbare navigering."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Letar du efter artiklar?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Skriv i rutan för att söka i vår katalog av %(count)s akademiska uppsatser och vetenskapliga artiklar, som vi <a %(a_preserve)s>bevarar för alltid</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Skriv i rutan för att söka efter filer i digitala lånebibliotek."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Denna sökindex inkluderar för närvarande metadata från Internet Archive’s Controlled Digital Lending-bibliotek. <a %(a_datasets)s>Mer om våra datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "För fler digitala lånebibliotek, se <a %(a_wikipedia)s>Wikipedia</a> och <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Skriv i rutan för att söka efter metadata från bibliotek. Detta kan vara användbart när du <a %(a_request)s>begär en fil</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Denna sökindex inkluderar för närvarande metadata från olika metadatakällor. <a %(a_datasets)s>Mer om våra datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "För metadata visar vi de ursprungliga posterna. Vi gör ingen sammanslagning av poster."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Det finns många, många källor till metadata för skrivna verk runt om i världen. <a %(a_wikipedia)s>Denna Wikipedia-sida</a> är en bra start, men om du känner till andra bra listor, vänligen meddela oss."

msgid "page.search.results.search_generic"
msgstr "Skriv i sökrutan för att söka."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Detta är metadataregister, <span %(classname)s>inte</span> nedladdningsbara filer."

msgid "page.search.results.error.header"
msgstr "Fel under sökning."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Försök <a %(a_reload)s>ladda om sidan</a>. Om problemet kvarstår, vänligen mejla oss på %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Inga filer hittades.</span> Prova med färre eller olika söktermer och filter."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Ibland händer detta felaktigt när sökservern är långsam. I sådana fall kan <a %(a_attrs)s>omladdning</a> hjälpa."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Vi har hittat matchningar i: %(in)s. Du kan hänvisa till URL:en som finns där när du <a %(a_request)s>begär en fil</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Tidskriftsartiklar (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digital utlåning (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultat %(from)s-%(to)s (%(total)s totalt)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ delvisa matchningar"

msgid "page.search.results.partial"
msgstr "%(num)d delvisa träffar"

msgid "page.volunteering.title"
msgstr "Volontärarbete & Belöningar"

msgid "page.volunteering.intro.text1"
msgstr "Annas Arkiv förlitar sig på volontärer som du. Vi välkomnar alla engagemangsnivåer och har två huvudkategorier av hjälp vi söker:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Lätt volontärarbete:</span> om du bara kan avvara några timmar här och där, finns det fortfarande många sätt du kan hjälpa till på. Vi belönar konsekventa volontärer med <span %(bold)s>🤝 medlemskap i Annas Arkiv</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Tungt volontärarbete (USD$50-USD$5,000 belöningar):</span> om du kan ägna mycket tid och/eller resurser till vårt uppdrag, skulle vi gärna arbeta närmare med dig. Så småningom kan du gå med i kärnteamet. Även om vi har en stram budget, kan vi tilldela <span %(bold)s>💰 monetära belöningar</span> för det mest intensiva arbetet."

msgid "page.volunteering.intro.text2"
msgstr "Om du inte kan volontärarbeta, kan du ändå hjälpa oss mycket genom att <a %(a_donate)s>donera pengar</a>, <a %(a_torrents)s>seeda våra torrents</a>, <a %(a_uploading)s>ladda upp böcker</a> eller <a %(a_help)s>berätta för dina vänner om Annas Arkiv</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Företag:</span> vi erbjuder höghastighetsdirektåtkomst till våra samlingar i utbyte mot donationer på företagsnivå eller utbyte mot nya samlingar (t.ex. nya skanningar, OCR:ade dataset, berikning av våra data). <a %(a_contact)s>Kontakta oss</a> om detta är något för dig. Se även vår <a %(a_llm)s>LLM-sida</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Lätt volontärarbete"

msgid "page.volunteering.section.light.text1"
msgstr "Om du har några timmar över kan du hjälpa till på flera sätt. Se till att gå med i <a %(a_telegram)s>volontärchatten på Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Som ett tecken på uppskattning ger vi vanligtvis ut 6 månader av “Lycklig Bibliotekarie” för grundläggande milstolpar, och mer för fortsatt volontärarbete. Alla milstolpar kräver högkvalitativt arbete — slarvigt arbete skadar oss mer än det hjälper och vi kommer att avvisa det. Vänligen <a %(a_contact)s>mejla oss</a> när du når en milstolpe."

msgid "page.volunteering.table.header.task"
msgstr "Uppgift"

msgid "page.volunteering.table.header.milestone"
msgstr "Milstolpe"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Sprida ordet om Annas Arkiv. Till exempel genom att rekommendera böcker på AA, länka till våra blogginlägg eller allmänt hänvisa folk till vår webbplats."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s länkar eller skärmdumpar."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Dessa bör visa att du berättar för någon om Annas Arkiv, och att de tackar dig."

msgid "page.volunteering.table.open_library.task"
msgstr "Förbättra metadata genom att <a %(a_metadata)s>länka</a> med Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Du kan använda <a %(a_list)s>listan över slumpmässiga metadatafrågor</a> som en startpunkt."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Se till att lämna en kommentar på problem du löser, så att andra inte duplicerar ditt arbete."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s länkar till poster du förbättrade."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Översätta</a> webbplatsen."

msgid "page.volunteering.table.translate.milestone"
msgstr "Fullständigt översätta ett språk (om det inte redan var nära att bli klart)."

msgid "page.volunteering.table.wikipedia.task"
msgstr "Förbättra Wikipedia-sidan för Annas Arkiv på ditt språk. Inkludera information från AA:s Wikipedia-sida på andra språk, och från vår webbplats och blogg. Lägg till referenser till AA på andra relevanta sidor."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Länk till redigeringshistorik som visar att du gjort betydande bidrag."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Uppfylla bok- (eller uppsatser, etc.) förfrågningar på Z-Librarys forum eller Library Genesis forum. Vi har inte vårt eget bokförfrågningssystem, men vi speglar dessa bibliotek, så att göra dem bättre gör även Annas Arkiv bättre."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s länkar eller skärmdumpar av förfrågningar som du har fixat."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Små uppgifter som postas i vår <a %(a_telegram)s>volontärchatt på Telegram</a>. Vanligtvis för medlemskap, ibland för små belöningar."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Små uppgifter publicerade i vår volontärchattgrupp."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Beror på uppgiften."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Belöningar"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Vi letar alltid efter personer med gedigna programmerings- eller offensiva säkerhetskunskaper som vill engagera sig. Du kan göra en betydande insats för att bevara mänsklighetens arv."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Som tack ger vi bort medlemskap för gedigna bidrag. Som ett stort tack ger vi bort monetära belöningar för särskilt viktiga och svåra uppgifter. Detta bör inte ses som en ersättning för ett jobb, men det är en extra incitament och kan hjälpa till med uppkomna kostnader."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "De flesta av våra koder är öppen källkod, och vi kommer att be om detsamma av din kod när vi tilldelar belöningen. Det finns vissa undantag som vi kan diskutera individuellt."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Belöningar tilldelas den första personen som slutför en uppgift. Kommentera gärna på en belöningsbiljett för att låta andra veta att du arbetar med något, så att andra kan hålla sig tillbaka eller kontakta dig för att samarbeta. Men var medveten om att andra fortfarande är fria att arbeta på det och försöka slå dig. Vi tilldelar dock inte belöningar för slarvigt arbete. Om två högkvalitativa inlämningar görs nära varandra (inom en dag eller två), kan vi välja att tilldela belöningar till båda, efter vårt gottfinnande, till exempel 100%% för den första inlämningen och 50%% för den andra inlämningen (så totalt 150%%)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "För de större belöningarna (särskilt skrapningsbelöningar), vänligen kontakta oss när du har slutfört ~5%% av det, och du är säker på att din metod kommer att skala till hela milstolpen. Du måste dela din metod med oss så att vi kan ge feedback. På detta sätt kan vi också bestämma vad vi ska göra om det finns flera personer som närmar sig en belöning, såsom att potentiellt tilldela den till flera personer, uppmuntra människor att samarbeta, etc."

msgid "page.volunteering.section.bounties.text6"
msgstr "VARNING: de högbelönade uppgifterna är <span %(bold)s>svåra</span> — det kan vara klokt att börja med enklare."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Gå till vår <a %(a_gitlab)s>Gitlab-ärendelista</a> och sortera efter ”Etikettprioritet”. Detta visar ungefär i vilken ordning vi bryr oss om uppgifterna. Uppgifter utan explicita belöningar är fortfarande berättigade till medlemskap, särskilt de märkta ”Accepterad” och ”Annas favorit”. Du kanske vill börja med ett ”Startprojekt”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Uppdateringar om <a %(wikipedia_annas_archive)s>Annas Arkiv</a>, det största verkligt öppna biblioteket i mänsklighetens historia."

msgid "layout.index.title"
msgstr "Annas Arkiv"

msgid "layout.index.meta.description"
msgstr "Världens största bibliotek med öppen källkod och öppen data. Speglar Scihub, Libgen, Zlib, och fler."

msgid "layout.index.meta.opensearch"
msgstr "Sök i Annas Arkiv"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Annas Arkiv behöver din hjälp!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Många försöker stänga ner oss, men vi kämpar tillbaka."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Om du donerar nu får du <strong>dubbelt</strong> så många snabba nedladdningar."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Giltigt till slutet av denna månad."

msgid "layout.index.header.nav.donate"
msgstr "Donera"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Spara mänsklig kunskap: en fantastisk semesterpresent!"

msgid "layout.index.header.banner.surprise"
msgstr "Överraska en älskad, ge dem ett konto med medlemskap."

msgid "layout.index.header.banner.mirrors"
msgstr "För att öka motståndskraften hos Annas Arkiv söker vi volontärer för att driva spegelsajter."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Den perfekta Alla hjärtans dag-presenten!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Det finns ett nytt sött att donera: %(method_name)s. Överväg gärna att %(donate_link_open_tag)sdonera</a> – det är inte billigt att driva hemsidan, och din donation gör verkligen nytta. Tack så mycket."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Vi håller i en insamling för att <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">backa upo</a> det största skuggbiblioteket för serier i världen. Tack för ditt stöd! <a href=\"/donate\">Donate.</a> Om du inte kan donera, berätta gärna för dina vänner istället och följ oss på <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, eller <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Senaste nerladdningar:"

msgid "layout.index.header.nav.search"
msgstr "Söka"

msgid "layout.index.header.nav.faq"
msgstr "FAQ"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Förbättra metadata"

msgid "layout.index.header.nav.volunteering"
msgstr "Volontärarbete & Belöningar"

msgid "layout.index.header.nav.datasets"
msgstr "Datauppsätningar"

msgid "layout.index.header.nav.torrents"
msgstr "Torrentar"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktivitet"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Kodutforskare"

msgid "layout.index.header.nav.llm_data"
msgstr "Data för stora språkmodeller"

msgid "layout.index.header.nav.home"
msgstr "Hem"

msgid "layout.index.header.nav.annassoftware"
msgstr "Annas mjukvara ↗"

msgid "layout.index.header.nav.translate"
msgstr "Översätta ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Logga in / Registrera dig"

msgid "layout.index.header.nav.account"
msgstr "Konto"

msgid "layout.index.footer.list1.header"
msgstr "Annas arkiv"

msgid "layout.index.footer.list2.header"
msgstr "Håll kontakten"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / upphovsrättsanspråk"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Avancerat"

msgid "layout.index.header.nav.security"
msgstr "Säkerhet"

msgid "layout.index.footer.list3.header"
msgstr "Alternativ"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "oberoende"

msgid "page.search.results.issues"
msgstr "❌ Den här filen kan ha problem."

msgid "page.search.results.fast_download"
msgstr "Snabb nerladdning"

msgid "page.donate.copy"
msgstr "kopia"

msgid "page.donate.copied"
msgstr "kopierad!"

msgid "page.search.pagination.prev"
msgstr "Föregående"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Nästa"

#~ msgid "layout.index.footer.list2.subreddit"
#~ msgstr "Subreddit"

#~ msgid "page.home.progress_bar.text"
#~ msgstr "5%% av mänsklighetens skrivna arv bevarat för alltid %(info_icon)s"

#~ msgid "page.md5.breadcrumbs"
#~ msgstr "Datauppsättningar ▶ Files ▶ MD5 %(md5_input)s"

#~ msgid "page.md5.box.download.text"
#~ msgstr "Ladda ner från:"

#~ msgid "page.md5.box.download.no_issues_download_options_explanation"
#~ msgstr "Vi har flera nedladdningsalternativ ifall en av dem är ur funktion. Dom har alla exakt samma fil."

#~ msgid "page.md5.box.download.no_hosting"
#~ msgstr "Observera att Anna’s Archive inte är värd för något av innehållet här. Vi länkar bara till andras webbsidor. Om du tror att du har ett giltigt DMCA-klagomål, se %(about_link)som oss</a>."

#~ msgid "page.md5.box.download.zlib_anon"
#~ msgstr ""

#~ msgid "page.donate.title"
#~ msgstr "Donera"

#~ msgid "page.donate.header"
#~ msgstr "Donera"

#~ msgid "page.donate.text1"
#~ msgstr ""
#~ "Anna’s Archive är ett ideellt projekt med öppen källkod, som drivs helt av volontärer. Vi tar emot donationer för att täcka våra kostnader,\n"
#~ "som inkluderar hosting, domännamn, utveckling och andra utgifter."

#~ msgid "page.donate.text2"
#~ msgstr "Med dina bidrag kan vi hålla denna webbsidan igång, förbättra dess funktioner, och bevara fler samlingar."

#~ msgid "page.donate.text3"
#~ msgstr "Senaste donationer: %(donations)s. Tack alla för er generositet. Vi uppskattar verkligen att ni litar på oss, med vilken summa du än kan avvara."

#~ msgid "page.donate.text4"
#~ msgstr "För att donera, välj din föredragna metod nedan. Om du stöter på något problem, vänligen kontakta oss på %(email)s."

#~ msgid "page.donate.nav.paypal"
#~ msgstr "Paypal"

#~ msgid "page.donate.nav.cc"
#~ msgstr "Kredit / Betalkort"

#~ msgid "page.donate.nav.crypto"
#~ msgstr "Krypto"

#~ msgid "page.donate.nav.alipay"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.nav.pix"
#~ msgstr "Pix"

#~ msgid "page.donate.nav.faq"
#~ msgstr "Frågor"

#~ msgid "page.donate.paypal.header"
#~ msgstr "Paypal"

#~ msgid "page.donate.paypal.text"
#~ msgstr "Gå till %(link_open_tag)sdenna sida</a> och följ instruktionerna, antingen genom att skanna QR-koden, eller att klicka på “paypal.me” länken. Om det inte fungerar, prova att uppdatera sidan, eftersom det kan ge dig ett annat konto."

#~ msgid "page.donate.cc.header"
#~ msgstr "Kredit / Betalkort"

#~ msgid "page.donate.cc.text1"
#~ msgstr "Vi använder Sendwyre för att sätta in pengar direkt i vårt Bitcoin (BTC) konto. Det tar cirka 5 minuter att slutföra."

#~ msgid "page.donate.cc.text2"
#~ msgstr "Denna metod har ett lägsta transaktionsbelopp på $30, och en avgift på cirka $5."

#~ msgid "page.donate.cc.steps.header"
#~ msgstr "Steg:"

#~ msgid "page.donate.cc.steps.list1"
#~ msgstr "1. Kopiera vår Bitcoin (BTC) kontoadress: %(address)s"

#~ msgid "page.donate.cc.steps.list2"
#~ msgstr "2. Gå till %(link_open_tag)sdenna sida</a> och klicka på \"buy crypto instantly\""

#~ msgid "page.donate.cc.steps.list3"
#~ msgstr "3. Klistra in våran kontoadress, och följ instruktionerna"

#~ msgid "page.donate.crypto.header"
#~ msgstr "Krypto"

#~ msgid "page.donate.crypto.btc_bch_note"
#~ msgstr "(fungerar även för BCH)"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.alipay.intro"
#~ msgstr "Använd detta %(link_open_tag)sAlipay-kontot</a> för att skicka din donation. Om det inte fungerar, prova att uppdatera sidan, eftersom det kan ge dig ett annat konto."

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.out_of_order"
#~ msgstr "Detta donationsalternativ är för närvarande ur funktion. Vänligen återkom senare. Tack för att du vill donera, vi uppskattar verkligen det!"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.pix.text"
#~ msgstr "Använd %(link_open_tag)sdenna Pix-sida</a> för att skicka din donation. Om det inte fungerar, prova att uppdatera sidan, eftersom det kan ge dig ett annat konto."

#~ msgid "page.donate.faq.header"
#~ msgstr "Vanliga frågor"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr "<span class=\"italic font-bold\">Anna’s Archive</span> är ett projekt som syftar till att katalogisera alla böcker som finns, igenom att samla data från olika källor. Vi spårar också mänsklighetens framsteg mot att göra alla dessa böcker lätt tillgängliga i digital form, genom <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">skuggbibliotek</a>. Lär dig mer <a href=\"/about\">om oss.</a>"

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Bok (alla)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr "Hem"

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr "Datauppsättningar ▶ ISBNs ▶ ISBN %(isbn_input)s"

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Hittades inte"

#~ msgid "page.isbn.invalid.text"
#~ msgstr "“%(isbn_input)s” är inte ett giltigt ISBN nummer. ISBN-nummer är 10 eller 13 tecken långa, bortsett från de valfria bindestrecken. Alla tecken måste vara siffror, förutom det sista, som kan vara “X”. Det sista tecknet är “kontrollsiffran”, som måste matcha en kontrollsumma som beräknas från de andra talen. Det måste också vara inom ett giltigt intervall, allokerat av den internationella ISBN byrån."

#~ msgid "page.isbn.results.text"
#~ msgstr "Matchande filer i vår databas:"

#~ msgid "page.isbn.results.none"
#~ msgstr "Inga matchande filer hittades i vår databas."

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Sök ▶ %(num)d+ resultat för <span class=\"italic\">%(search_input)s</span> (i skuggbibliotekens metadata)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr "Sök ▶ %(num)d resultat för <span class=\"italic\">%(search_input)s</span> (i skuggbibliotekens metadata)"

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr "Search ▶ Sökfel för <span class=\"italic\">%(search_input)s</span>"

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Search ▶ Ny sökning"

#~ msgid "page.donate.header.text3"
#~ msgstr ""

#~ msgid "page.donate.buttons.one_time"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Om dur redan har kryptopengar, så är detta våra adresser."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Tack så mycket för hjälpen! Detta projekt skulle inte vara möjligt utan dig."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr "Försök att <a href=\"javascript:location.reload()\">ladda om sidan</a>. Om problemet kvarstår, vänligen meddela oss på <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> eller<a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#~ msgid "page.donate.login"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Hem"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Om"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Donera"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Datauppsättningar"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Mobil app"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Annas blogg"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Annas mjukvara"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Översätta"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Speglar %(libraries)s, och mer."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr "Datauppsättningar ▶ DOIs ▶ DOI %(doi_input)s"

#~ msgid "page.doi.invalid.header"
#~ msgstr "Hittades inte"

#~ msgid "page.doi.invalid.text"
#~ msgstr "\"%(doi_input)s\" ser inte ut som en DOI. Den borde starta med \"10.\" och ha ett snedsträck i sig."

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "Kanonisk URL: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr "Denna fil kan vara i %(link_open_tag)sSci-Hub</a>."

#~ msgid "page.doi.results.text"
#~ msgstr "Matchande filer i vår databas:"

#~ msgid "page.doi.results.none"
#~ msgstr "Inga matchande filer hittades i vår databas."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Kan jag bidra på andra sätt?</div> Ja! Se <a href=\"/about\">Om</a> under \"Hur man hjälper till\"."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Om"

#~ msgid "page.about.header"
#~ msgstr "Om"

#~ msgid "page.home.search.header"
#~ msgstr "Sök"

#~ msgid "page.home.search.intro"
#~ msgstr "Sök igenom vår katalog av skuggbibliotek."

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr "Om du har ett giltigt DMCA-klagomål, läs längst ner på denna sida, eller kontakta oss på %(email)s."

#~ msgid "page.home.explore.header"
#~ msgstr "Utforska böcker"

#~ msgid "page.home.explore.intro"
#~ msgstr "Dessa är en kombination av populära böcker, och böcker som har särskild betydelse för världen av skuggbibliotek och digitalt bevarande."

#~ msgid "page.wechat.header"
#~ msgstr "Inofficiell WeChat"

#~ msgid "page.wechat.body"
#~ msgstr "Vi har en inofficiell WeChat-sida, som hålls igång av en sympatisör. Använd koden nedan för att få tillgång till sidan."

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Om"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Mobil app"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "Inofficiell WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Efterfråga böcker"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Ladda upp"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Hur man hjälper till"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr ""

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "endast denna månad!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub har <a %(a_closed)s>pausat</a> uppladdningen av nya artiklar."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Välj en betalningsmetod. Vi ger rabatt för kryptobetalningar %(bitcoin_icon)s, eftersom det ger oss (mycket) färre avgifter."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Välj en betalningsmetod. Vi erbjuder just nu bara kryptobetalningar %(bitcoin_icon)s, eftersom vanliga betalningstjänster vägrar att samarbeta med oss."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Vi kan inte stöd för direktbetalning med kredit-/betalkort, eftersom bankerna inte vill samarbeta med oss. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Det finns trots detta flera sätt att använda kredit-/betalkort, genom våra andra betalningsmetoder:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Långsamma & externa nedladdningar"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Nedladdningar"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Om du använder krypto för första gången, så föreslår vi att du använder %(option1)s, %(option2)s, eller %(option3)s för att köpa och donera Botcoin (den ursprungliga och mest använda kryptovalutan)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 länkar till poster du förbättrat."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 länkar eller skärmdumpar."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 länkar eller skärmdumpar av förfrågningar du uppfyllt."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Om du är intresserad av att spegla dessa datasets för <a %(a_faq)s>arkivering</a> eller <a %(a_llm)s>LLM-träning</a> ändamål, vänligen kontakta oss."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Om du är intresserad av att spegla denna dataset för <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-träning</a>, vänligen kontakta oss."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Huvudwebbplats"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN-landinformation"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Om du är intresserad av att spegla denna datamängd för <a %(a_archival)s>arkivering</a> eller <a %(a_llm)s>LLM-träning</a>, vänligen kontakta oss."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Den internationella ISBN-byrån släpper regelbundet de intervall som den har tilldelat nationella ISBN-byråer. Från detta kan vi härleda vilket land, region eller språkgrupp detta ISBN tillhör. Vi använder för närvarande dessa data indirekt, genom Python-biblioteket <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Resurser"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Senast uppdaterad: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN-webbplats"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Exklusive ”scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Vår inspiration för att samla metadata är Aaron Swartz’ mål om ”en webbsida för varje bok som någonsin publicerats”, för vilket han skapade <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Det projektet har gått bra, men vår unika position gör att vi kan få metadata som de inte kan."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "En annan inspiration var vår önskan att veta <a %(a_blog)s>hur många böcker det finns i världen</a>, så att vi kan räkna ut hur många böcker vi fortfarande har kvar att rädda."

#~ msgid "page.partner_download.text1"
#~ msgstr "För att ge alla möjlighet att ladda ner filer gratis, måste du vänta <strong>%(wait_seconds)s sekunder</strong> innan du kan ladda ner denna fil."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Uppdatera sidan automatiskt. Om du missar nedladdningsfönstret kommer timern att starta om, så automatisk uppdatering rekommenderas."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Ladda ner nu"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvertera: använd onlineverktyg för att konvertera mellan format. Till exempel, för att konvertera mellan epub och pdf, använd <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: ladda ner filen (pdf eller epub stöds), och <a %(a_kindle)s>skicka den till Kindle</a> via webben, appen eller e-post. Hjälpsamma verktyg: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Stöd författarna: Om du gillar detta och har råd, överväg att köpa originalet eller stödja författarna direkt."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Stöd bibliotek: Om detta finns tillgängligt på ditt lokala bibliotek, överväg att låna det gratis där."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Inte tillgänglig direkt i bulk, endast i semi-bulk bakom en betalvägg"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Annas Arkiv hanterar en samling av <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb är ett företag som skrapar olika nätbokhandlar för att hitta ISBN-metadata. Annas Arkiv har gjort säkerhetskopior av ISBNdb:s bokmetadata. Denna metadata är tillgänglig genom Annas Arkiv (dock för närvarande inte i sökningen, förutom om du uttryckligen söker efter ett ISBN-nummer)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "För tekniska detaljer, se nedan. Vid någon tidpunkt kan vi använda det för att avgöra vilka böcker som fortfarande saknas från skuggbibliotek, för att prioritera vilka böcker som ska hittas och/eller skannas."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Vårt blogginlägg om dessa data"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb-skrapning"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "För närvarande har vi en enda torrent, som innehåller en 4,4GB gzippad <a %(a_jsonl)s>JSON Lines</a>-fil (20GB uppzippad): ”isbndb_2022_09.jsonl.gz”. För att importera en ”.jsonl”-fil till PostgreSQL kan du använda något som <a %(a_script)s>detta skript</a>. Du kan till och med rörleda det direkt med något som %(example_code)s så att det dekomprimeras i farten."

#~ msgid "page.donate.wait"
#~ msgstr "Vänligen vänta minst <span %(span_hours)s>två timmar</span> (och uppdatera denna sida) innan du kontaktar oss."

#~ msgid "page.codes.search_archive"
#~ msgstr "Sök i Annas Arkiv efter “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donera genom Alipay eller WeChat. Du kan välja mellan dessa två i nästa steg."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Sprida ordet om Annas Arkiv på sociala medier och onlineforum, genom att rekommendera böcker eller listor på AA, eller svara på frågor."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Skönlitteratursamlingen har avvikit men har fortfarande <a %(libgenli)s>torrenter</a>, även om de inte har uppdaterats sedan 2022 (vi har direkta nedladdningar)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Annas Arkiv och Libgen.li hanterar gemensamt samlingar av <a %(comics)s>serietidningar</a> och <a %(magazines)s>magasin</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Inga torrents för rysk skönlitteratur och standarddokumentssamlingar."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Det finns inga torrents tillgängliga för det extra innehållet. De torrents som finns på Libgen.li-webbplatsen är speglar av andra torrents som listas här. Det enda undantaget är skönlitteraturstorrents som börjar vid %(fiction_starting_point)s. Serier och tidskrifter torrents släpps som ett samarbete mellan Annas Arkiv och Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Från en samling <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> exakt ursprung oklart. Delvis från the-eye.eu, delvis från andra källor."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

