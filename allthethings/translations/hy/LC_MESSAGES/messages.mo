��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ld �  �h 1   �k �  �k �  �m /  iq   �t +  w �   Ey �   �y K   bz �   �z 1  5{ �  g~ �  � L  ؂ �  %� �  � �  � �  �� �  .� y  � +  c� �   �� �  � �   ��   \� $   n� �  �� K   ?� @   �� %   ̥ I   � g   <� 3   �� !   ئ `   �� 8   [� Q   �� )   � t   �   ��   �� �   �� m   X�    ƭ    ߭ +   �    � ?   /�    o� 	   ��    ��    �� G   ��    �� *   �� 	   '� 
   1�    ?� 4   W� 6   �� #   ï h  � C  P� �   �� 1   s� �  �� (  (� �  Q� D  � $   _� �   �� $   p� �   �� )   �� �  �� $   �� �  �� M   �� �   ��    �� A  �� B  �� �  *� 
  ��    �� �   �� �   {� �   /� N   #� q   r� �   �� �   �� �   0� s   ��    .� �   5� �  �� w   �� �   d� F  � g   e� �  �� y   �� �  � �   �� I  �� �   � b  ��   /� K   >� �  �� 5  [� �   �� V   <� �   ��    N�   m� �  }� K   ^�    �� l  �� �  4�    �� [  ��   >� 7  T� �  �� �   +� t  (� �  �� w   R� �   �� �   t  �   * �   � �   � \   + �   � �   I t   � !   h �   � �   W T   )  l !   �	 �  �	 �  � �  *
   � �   � R  � �  �   �   � C    �  D K  & �  r    1   D l  [  1  �! �  �" �   �$ �   f% �   G& k  '' �   �* M  s+ �  �- G   �/ �   �/   v0 �   �1 �  %2 C  �4 �   (6 �   �6 3   �7 �  �7 J  �; �  �< )   �? �   �?   }@ z  �A �  �B �  �D *  sF �  �I E   �L �  �L x  bO �  �S t  �U   <Z �  Z\ �   ^ �   �^    p_ +  �_   �` �  �b �  |e �  ,g   �h $   j x  4j e  �l �  o    �p ?  �p $  r }  ?t �  �u �  �y �   ({    �{ �  �{ ]  r~ !   Ѐ �  �   �� �  �� �  T� �  � �   ƌ x   �� �  � ?   ��    � [  �� �  [�   L� Y   U� K   �� _  �� �   [� �   5� �   � �  �� 3   P� -   �� �  �� B  A� �   �� �   l� �   
� N   �� �  � �  Χ #   �� (   �� F   ݩ k   $� �   �� }   � �   �� �   V� *   N� T   y� �   έ �   c� Q    � `   r� h   ӯ �   <�    %� 2   :� �  m� P  <� �  �� �  V� 
  � }  &� B  �� )   � c  � �   u�   p� �  u� �  :� g  ��   Y�   h� K   �� n   �� K   @� $  �� w  �� �  )�   �� A   � �  B� �  �� �  �� _   h� �  ��   �� �  �� %   X� �  ~� b  � �   t� D  a� �  ��    F� S   \� �   ��    c� �  v� �  R� �  ��   �� �  �     r �  � �  * K   � �   3 �  � �  � �   �
 �  y �    �  � 5   � m  � 6  & O  ] �  � �  9 H  �     " �  %" �  �# )   �' b  �' \  * �  |- 9  h1 �  �5 B  �7 K   �9 �  : �  �< �   ]> z   ? �   |?    !@ E   4@ -   z@ =   �@ 0   �@ '   A   ?A �  WB >  7G 	  vJ a  �N    �O �  �O �  �T m  kX s  �Z �  M] �  Ma    
f �  *f    �g ,   �g   h 4  ,k �  ao R  �r    Hw   Zw &  pz ]  �} �  �� \   �  � <  � I   � V  h� �   �� �  ��   �� 1  ��   Л   � �  ��    �� C  �� q   � �   _� 1   �� r   .� s   �� �  � �  � �  �� k   �� �  �� �   ��    k� j  |� e   � �   M� �  2� Y   ̹ �   &� $   ߺ W  � �  \� ^  	� �  h� �   0� (   � �   4�   ��    �   � �  *�   �� T   �� k   "� w  �� 1   � R  8� E  �� �  �� 5   v� �   �� �   �� �  �� �  b�    H� �  h� <   �� j  '� G  �� �  �� �  �� �   '� 1   �� �  �� T   p� �  �� �  e� �  � �  �� �   l� f   �   n� �  �  �  c @   c  _
 K   �
 �   �  � %  � s  � �  .    � 1   �   � .   � +  ' �   S" 3   �" �  # �  �& �  e* �  �, �  �. �  R1 A  =4   9 2  �: ?  �= �   A �  �A   �F ^   �H �   <I �   J &   �J    K �  &K   M m   N �  �N �  HP �  @S �  �T �  [X 1  [   D\    \_ &   }a 2  �a    �b �  �b 7   �e 6   �e    �e '   �e _   #f #   �f 5   �f 4   �f    g    g    2g    ?g    Ng    dg 3   wg    �g ;   �g    �g    �g    h    h 7  (h �   `i 4   �i    *j M   Aj =   �j Y   �j \   'k 8   �k    �k    �k %   �k *   l    0l    Hl    dl /   sl    �l -   �l E   �l 8   m "   Wm \   zm W   �m L   /n E   |n &   �n >   �n U   (o 6   ~o �   �o |   Bp    �p �   �p �   Pq    �q    �q .   r    ?r '   ]r '   �r !   �r '   �r    �r '   �r    's <   4s    qs 	   �s 
   �s    �s C   �s    �s    �s 	   �s &   �s 	    t =   *t    ht    nt 	   ut    t    �t 4   �t    �t #   �t    �t    u $   "u 	   Gu !   Qu 5   su    �u    �u _   �u    %v (   Dv    mv $   �v    �v %   �v +   �v �   
w �   �w �   �x    ]y �  ~z �   9}    �} C   ~    R~    o~    v~    �~    �~ Q   �~ �    g   � �   � 7   �� i   � b   P� �   �� �   s� �  A� �   ؄ d   y� J   ޅ    )� 
   F�    Q�    p�    �� '   ��    ņ    Ά    �    �� !   �    .�    5� -   I�    w�    �� )   ��    ԇ    �    �    �    3� 0   J� (   {� s  ��    �    �    -� 2   3�    f� q   m� y   ߊ C   Y� J   �� O   �    8�    @�    H� �   K�    ��    �� =   � �   Q� *   ��    &� �   >� ;   �� �  8� �   )� �   �� ?  �� �  Җ d   q� �  ֘ �   �� ^  �� l  ��    M�    k� s   }� �   � �   |� �   �� �   �� m   E� �   �� 0   e� ;   ��    ң "   � �   � -   �� 
   �� #   ä    � +   �� �   +�    Υ _   � �   H�     Ц "   � o   � �   �� S  �    r� !   �� 
  ��    �� 
   ݫ    � $   ��    � D   '�   l� 7   ��    ��    Ю    � .  �� 2   ,�    _�    o�    �� 
   ��    �� 6   �� *   �� 
   "� 2   -�   `� .   d� #   �� �   �� &   @� -   g�    �� '   �� [   Ҵ �   .� &   ˵ [   � 9  N� �   �� �   Q� )   ޸ �  � �   ��    �� V   �� !   ��   � �  :� &   ľ y   � Q  e�   �� -   �� Q   �� %   C� �  i� D   �� 9   @� .   z� ?   �� B   ��   ,� +   2� "   ^� K   �� h   ��    6� *   K� 4   v� G   �� U  �� �  I� �  �� o   y� O   ��    9�    F� �  Y� �  I� 2  �� 4   -� �  b� �  � C   �� *   �� �  &� �  �    �� 
   � B   &�    i� P  ~� .   �� G  �� �  F� �  �� !   w� �   �� U   f� =   �� �   �� 6  }� �  �� %  R� �  x� �    � �  �� �   �� P  3�   ��    �� 9  	� w   C� P   ��    �    ,� .   E� ;   t� H   �� v   �� Z   p� 	   �� N   �� 1  $  O   V �  � �   3 �    K    7   [ )   � "   � R   � <   3 P   p <   � ~  � A   }	 B  �	     X  " �   { �   '
 
  �
 U  � 7   S �   � 	   Z (  d �   � :   c   �    � /   � !   � ,    N   .    }    � ~   � 8   �  K        ( 0   ; 2  l   � ;  �  �   �! #   �" #   �" .   �" &   # '   <#    d#    �# Y   �# R   �# $  5$ �   Z' %   �' �   "( �   �( s   �) �   * �    +    �+    , �   , m   �, �   >- �   6.    �. !   R/ x  t/ �   �0 j   �1 �   �1 �   �2 ^   ?3    �3 [   �3 �   4 �   �4 a   x5   �5    �6   7 �   9 �   �9 �   ":    ; N  ; |  ]> .   �? 0   	@ 
   :@    E@   R@ -   XB �   �B L  C 
  aD �   oE !  PF �   rG   lH !  oI �   �J �  1K �    M n  �M :  P �  TQ 
   �R 
   �R 
   S �   S 
   �S 
   T �   T �   �T g  7U 
   �V �   �V r   �W �   �W m   �X �   !Y �   �Y 
   nZ �   |Z 
   Z[ 
   h[ 
   v[ h  �[ �   �^ �  �_ )   �b    �b    �b �   �b 8   �c G   d �  Yd   �e N   �f    Cg .   Sg g   �g b   �g j   Mh <   �h <   �h �  2i L   �j    k �  n �   �o �   �p   <q �  Nr b  1t �  �w -   { �   N| �   5}    ~ �  3~ 0   �� o  � �  ^� �  � �   � 1  �� @  � �   '� �   � 7   � l   �    �� T   ��    ��    �    %� �  ?�    ُ �   � i   ��    	�    �    +� (   :� �   c� }   E� Y   Ò P  � M   n�    ��    Ӕ 6   � D   �    `�    r� 
   ~�    �� 
   ��    �� 
   ��    �� g   Õ �   +�    Ӗ 
   �    �    ��    � 
   0�    >�    K�    j�    �� 7   �� �   ٗ    �� V   ʘ �   !� $  ٙ B  �� V  A� �  �� ?  �� �  נ +   ΢   �� Z   � �   i� �   � "  �� �  Ӧ �   u� �   ��    ȩ �   ݩ �   �� �   D�    �     �    �    ,�    I� E   c�    �� 6   ��    � ,   �� "   #� E   F�    ��    ��     ĭ    �    �    �    � 5   '�    ]� E   d� @   �� �   � �   ˯ [   �� =   � �   *� �   � �   �� 7   � O   S� B   �� I   � v   0� D   �� O  � �  <� �  2� 8   Թ u   
� �   �� B   e� �  �� �  w� �   *�    � 4   �� 2  �� .  � �   1� O   �� �   0� �  � 6   �� Q   �� 0   A� ]   r� �   �� �   ��    W�    d�    m� >   t� �   �� [   �� G   � Z   K� ;   �� 5   �� 8   � �   Q� A   �� �   � b   �� �  #� �   �� �   �� M  � M   b� -   ��    �� -   ��     +�    L�     l� -   �� o   �� ~   +� 9  �� �   �� :   p� O   �� B   ��    >� �   S� �   �� .  �� 5  �    Q� �   `� T   !�    v� ;  �� 3   �� >   �� j   >� K   �� �   �� �   �� �   6� 1   �� $   ��   � 3   3� 1   g� �   �� \  7�    �� �   � �   � �   �� �   ]� I   '� V   q� �   �� "   �� �   �� 0   m� s  �� E   � �   X� .   �� >   � W  K� {   �� i   � �   �� �   �� |   Y� �   �� 	  ��    �� ,   �� }   �� {   v� 2   �� A   %�    g� )   ��    �� <   �� �   � �   �� 3   A� 4   u� ?   �� ;   �� N   &� �   u� X   j� �   �� �   �� >   :� �   y� �  -� >   �  �   �     � b   � h    !   q �   � �   l H   8 �   �     >    �   \    � �    ?   �     <   . �   k �   }   �	 &   	
 }   0
 }   �
 &   , �   S i   �   U �   s
 )   . �   X ?  � #      A S   _ 1   � �   � Z  �    �    �    � )   � C   ! h   e r   � N   A    �    � /   � a   � c   _    � �   � o   j    � 5   � T   '    | !   � �   � �  i �       �    �   � %  � w   �    ^ �  q �  *" e   % �   ~% 1   I& �  {& S   V* �   �* +   V+    �+ 
  �+ 9   �. �   �. �   �/ s   L0 �   �0 7   q1 �   �1 `   H2 8   �2 �   �2 e   {3 c   �3 q   E4 V   �4 |   5 �   �5   P6 T   g7 [  �7 �  9 d  < S   h= �  �= �  I@ T  B $  ]C ]  �E W   �F (   8G 4  aG ?   �H x  �H �   OL �   M    �M 9   �M ;  �M    4Q c  <Q 7  �R �  �T �  �V u   �X �   Y �   �Y b   FZ M   �Z �   �Z �   �[    $\ D   4\ ]   y\ 9   �\ '   ] c   9] �   �] Q   /^ 
   �^ �   �^ �  3_ !  �` #   b 0   )b    Zb �   gb �   �b �   �c J  d �   _e +   f 8   ?f �  xf    h �   1h �   i �  �m p   o C   }o    �o 
   �o    �o k   �o N   Bp �   �p �  nq �   s !   �s #   �s .   �s /   "t �   Rt    �t N   u    [u T   lu O   �u    v    .v �   ?v    �v )   �v L   w G   Xw    �w �   �w �  �x �   z �   �z �   �{ e  |    y} 1   �} A  �} V   R  Y�    �� �   �� �   �� p   4� �   �� �   '� �   Ȅ    i� �   �� +   @� 9   l� ;   �� 1   � 2   � 1   G� -   y� 5   ��    ݇    �� D   � D   \� D   �� 6   � E   � 9   c� 7   ��    Չ #   �� i   � >   ��     t   Պ M   J� �   �� U   T� <   �� 0   � 5   � '   N� )   v� �   �� �   H� �   Ԏ z  �� (   � ,   >� $   k�    �� g   �� 	   � $   � ?   =� �   }� *   u� .   ��    ϓ 
   ֓ 	   � l   � 0   X� �  ��    X� (   x� #   �� +   Ŗ 9   � &   +� :   R� <   �� @   ʗ d   � ^   p�    Ϙ �   ֘ d   �� "   �    � \   $� �   �� :   � .   K� E   z� �   �� �   �� �   !�    �� /   �� 	   �    �� #   � >   <� �  {� �   � ?   ��    8� =   <�     z� -   ��    ɢ 	   ΢ �   آ e   �� F  � -   9� ;   g� �   �� ;   x� 7   �� :   � =   '� K   e� =   ��    �    � U   � =   s� E   �� Y   �� �  Q� �   F� �   Ϋ �   Q� 2   ܬ �   � .   �    � �   ,� '   �� B   ծ %   � b   >� �   �� 4   )� .  ^� +   �� +   �� .   �    � 0   /� &   `� A   ��    ɲ *   � �   � �  �� :   :� A   u� �  �� �   z� 1   m� 9   ��    ٸ    � /   ��    ,�    ?�    X�    r�    ��    ��    �� 
   ��    Ź    Թ    �    �    �� N   � �  ^� �  � �  �� -  �� 9  �� w  �� �  g�    f� �  �� -   P� �  ~� M  � �  i� /  X� �  �� :   � +  R� j   ~� <   �� �   &� �   �� �   X� �   �� ]  �� �   � p  �   ��    �� �  �� s  U� �   ��   a�    g� �  �� ^  6� 8  �� u  �� 
   D� �   O� �   � �  �� 
  I� �   T� �   � $   �� 2   #� 9   V� �   �� v   � )   �� �   �� o   u� �   �� 4   �� �   �� �   �� b   �� �   $� �   �� �   I� �   �� �   q� �   �� �   �� �   e� �   a     @ l   Q O   � �    F   �    �     x   !    � "   �    � e   � q   M u   � .   5    d    q    � 	   � �   � �    �   � L   �    � 7   � x    %   �    � 
   �    �    �    	    
	    	 
   4	    ?	    Y	 "   l	    �	    �	    �	 '   �	    �	    
    
    -
 4   <
 1   q
 3   �
 �   �
 $   � �   � ,  c    �
    �
    �
    �
    �
    �
    �
   �
 �    �   � %   P G   v �   �    y �   � �   p E   Y *   � �   � R  � �   
 2  � �   � E   � �   � 1   � 2   
 �   = �   � C   �    �    @  	 �   J +   �    � #        ,  '   ?     g     �     �  �   �  �   �! �   �" 4  m# x  �$ �   & �   �& �  ;' �  $* �  �, =  S. �  �/ E  @1    �3 �  �3 )  !5 9  K7 �  �8 �  ,= �   �? h  �@ %   .C j   TC �   �C �  �D g   4G �  �G    fI    uI *   �I   �I �   �J �   LK `   L �   }L m   4M �   �M :   �N   �N b   �O 6   7P �   nP q  Q 0   �R  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: hy
Language-Team: hy <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Գրադարանը հայտնի (և անօրինական) գրադարան է։ Նրանք վերցրել են Library Genesis-ի հավաքածուն և այն հեշտությամբ որոնելի դարձրել։ Բացի այդ, նրանք շատ արդյունավետ են դարձել նոր գրքերի ներդրումներ ստանալու հարցում՝ խրախուսելով ներդրող օգտատերերին տարբեր արտոնություններով։ Ներկայումս նրանք չեն վերադարձնում այս նոր գրքերը Library Genesis-ին։ Եվ ի տարբերություն Library Genesis-ի, նրանք իրենց հավաքածուն հեշտությամբ հայելային դարձնելու հնարավորություն չեն տալիս, ինչը խանգարում է լայն պահպանմանը։ Սա կարևոր է նրանց բիզնես մոդելի համար, քանի որ նրանք գումար են գանձում իրենց հավաքածուին մեծ քանակությամբ (օրական 10-ից ավելի գրքեր) հասանելիություն ստանալու համար։ Մենք բարոյական դատողություններ չենք անում անօրինական գրքերի հավաքածուին մեծ քանակությամբ հասանելիության համար գումար գանձելու վերաբերյալ։ Անկասկած է, որ Z-Գրադարանը հաջողակ է եղել գիտելիքների հասանելիությունը ընդլայնելու և ավելի շատ գրքեր ձեռք բերելու հարցում։ Մենք պարզապես այստեղ ենք մեր մասը կատարելու համար՝ ապահովելով այս մասնավոր հավաքածուի երկարաժամկետ պահպանումը։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>) Պիրատական գրադարանի հայելու սկզբնական թողարկման ժամանակ (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի արխիվ</a>), մենք ստեղծեցինք Z-Գրադարանի հայելին, որը մեծ անօրինական գրքերի հավաքածու է։ Հիշեցման համար, սա այն է, ինչ մենք գրել էինք այդ սկզբնական բլոգային գրառման մեջ. Այդ հավաքածուն թվագրվում էր 2021 թվականի կեսերին։ Մինչդեռ, Z-Գրադարանը աճում է զարմանալի արագությամբ՝ նրանք ավելացրել են մոտ 3.8 միլիոն նոր գիրք։ Այստեղ որոշ կրկնօրինակներ կան, իհարկե, բայց մեծ մասը կարծես թե իսկապես նոր գրքեր են կամ ավելի բարձր որակի սկաներ նախկինում ներկայացված գրքերի։ Սա մեծ մասամբ պայմանավորված է Z-Գրադարանի կամավոր մոդերատորների թվի աճով և նրանց մեծ քանակությամբ ներբեռնման համակարգով՝ կրկնօրինակների հեռացմամբ։ Մենք ցանկանում ենք շնորհավորել նրանց այս ձեռքբերումների համար։ Մենք ուրախ ենք հայտարարել, որ ստացել ենք բոլոր գրքերը, որոնք ավելացվել են Z-Գրադարանին մեր վերջին հայելուց մինչև 2022 թվականի օգոստոս։ Մենք նաև վերադարձել ենք և քերել որոշ գրքեր, որոնք առաջին անգամ բաց ենք թողել։ Ընդհանուր առմամբ, այս նոր հավաքածուն մոտ 24TB է, ինչը շատ ավելի մեծ է, քան նախորդը (7TB)։ Մեր հայելին այժմ ընդհանուր առմամբ 31TB է։ Կրկին, մենք կրկնօրինակները հեռացրել ենք Library Genesis-ի դեմ, քանի որ այդ հավաքածուի համար արդեն հասանելի են տոռենտներ։ Խնդրում ենք այցելել Պիրատական գրադարանի հայելին՝ նոր հավաքածուն ստուգելու համար (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի արխիվ</a>)։ Այնտեղ ավելի շատ տեղեկություններ կան ֆայլերի կառուցվածքի մասին և ինչ է փոխվել նախորդ անգամից։ Մենք այստեղից հղում չենք կատարի, քանի որ սա պարզապես բլոգային կայք է, որը չի հյուրընկալում որևէ անօրինական նյութ։ Իհարկե, սերմանումը նույնպես հիանալի միջոց է մեզ օգնելու համար։ Շնորհակալություն բոլորին, ովքեր սերմանում են մեր նախորդ տոռենտների հավաքածուն։ Մենք երախտապարտ ենք դրական արձագանքի համար և ուրախ ենք, որ կան այդքան շատ մարդիկ, ովքեր հոգ են տանում գիտելիքների և մշակույթի պահպանման մասին այս անսովոր ձևով։ 3x նոր գրքեր ավելացվել են Ծովահեն Գրադարանի Հայելում (+24TB, 3.8 միլիոն գիրք) Կարդացեք ուղեկցող հոդվածները TorrentFreak-ից՝ <a %(torrentfreak)s>առաջին</a>, <a %(torrentfreak_2)s>երկրորդ</a> - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ուղեկցող հոդվածներ TorrentFreak-ից՝ <a %(torrentfreak)s>առաջին</a>, <a %(torrentfreak_2)s>երկրորդ</a> Ոչ շատ վաղուց, «ստվերային գրադարանները» մահանում էին։ Sci-Hub-ը՝ ակադեմիական հոդվածների հսկայական անօրինական արխիվը, դադարեցրել էր նոր աշխատանքների ընդունումը՝ դատական գործերի պատճառով։ «Z-Գրադարանը», գրքերի ամենամեծ անօրինական գրադարանը, տեսավ իր ենթադրյալ ստեղծողների ձերբակալությունը քրեական հեղինակային իրավունքի մեղադրանքներով։ Նրանք անհավանականորեն կարողացան փախչել իրենց ձերբակալությունից, բայց նրանց գրադարանը ոչ պակաս վտանգի տակ է։ Որոշ երկրներ արդեն անում են այս տարբերակը։ TorrentFreak-ը <a %(torrentfreak)s>հաղորդել է</a>, որ Չինաստանը և Ճապոնիան իրենց հեղինակային իրավունքի օրենքներում ներառել են ԱԲ բացառություններ։ Մեզ համար անհասկանալի է, թե ինչպես է սա փոխազդում միջազգային պայմանագրերի հետ, բայց դա անշուշտ ապահովում է իրենց ներքին ընկերություններին, ինչը բացատրում է այն, ինչ մենք տեսնում ենք։ Ինչ վերաբերում է Աննայի Արխիվին՝ մենք կշարունակենք մեր ստորգետնյա աշխատանքը՝ հիմնված բարոյական համոզմունքների վրա։ Սակայն մեր ամենամեծ ցանկությունն է դուրս գալ լույսի մեջ և օրինականորեն մեծացնել մեր ազդեցությունը։ Խնդրում ենք բարեփոխել հեղինակային իրավունքը։ Երբ Z-Գրադարանը կանգնեց փակման առաջ, ես արդեն պահուստավորել էի նրա ամբողջ գրադարանը և փնտրում էի հարթակ՝ այն տեղավորելու համար։ Դա էր իմ մոտիվացիան՝ սկսել Աննայի Արխիվը՝ շարունակելով այդ նախորդ նախաձեռնությունների առաքելությունը։ Մենք այդ ժամանակից ի վեր դարձել ենք աշխարհի ամենամեծ ստվերային գրադարանը՝ հյուրընկալելով ավելի քան 140 միլիոն հեղինակային իրավունքով պաշտպանված տեքստեր՝ տարբեր ձևաչափերով՝ գրքեր, ակադեմիական հոդվածներ, ամսագրեր, թերթեր և այլն։ Իմ թիմը և ես գաղափարախոսներ ենք։ Մենք հավատում ենք, որ այս ֆայլերի պահպանումն ու հյուրընկալումը բարոյապես ճիշտ է։ Աշխարհի գրադարանները ֆինանսավորման կրճատումներ են տեսնում, և մենք չենք կարող վստահել մարդկության ժառանգությունը կորպորացիաներին։ Այնուհետև եկավ ԱԲ-ն։ Գործնականում բոլոր խոշոր ընկերությունները, որոնք կառուցում են LLM-ներ, կապվեցին մեզ հետ՝ մեր տվյալների վրա վերապատրաստվելու համար։ Շատերը (բայց ոչ բոլորը) ԱՄՆ-ում հիմնված ընկերությունները վերանայեցին իրենց դիրքորոշումը, երբ հասկացան մեր աշխատանքի անօրինական բնույթը։ Ի հակադրություն, չինական ընկերությունները խանդավառությամբ ընդունել են մեր հավաքածուն՝ ակնհայտորեն չանհանգստանալով դրա օրինականությամբ։ Սա ուշագրավ է, հաշվի առնելով Չինաստանի դերը՝ որպես գրեթե բոլոր խոշոր միջազգային հեղինակային իրավունքի պայմանագրերի ստորագրող։ Մենք բարձր արագությամբ հասանելիություն ենք տվել մոտ 30 ընկերությունների։ Նրանց մեծ մասը LLM ընկերություններ են, իսկ ոմանք տվյալների բրոքերներ են, որոնք կվաճառեն մեր հավաքածուն։ Նրանց մեծ մասը չինական են, թեև մենք աշխատել ենք նաև ԱՄՆ-ի, Եվրոպայի, Ռուսաստանի, Հարավային Կորեայի և Ճապոնիայի ընկերությունների հետ։ DeepSeek-ը <a %(arxiv)s>ընդունել է</a>, որ ավելի վաղ տարբերակը վերապատրաստվել է մեր հավաքածուի մի մասի վրա, թեև նրանք լռում են իրենց վերջին մոդելի մասին (հավանաբար նույնպես վերապատրաստվել է մեր տվյալների վրա)։ Եթե Արևմուտքը ցանկանում է առաջ անցնել LLM-ների և, ի վերջո, ԱԲ-ի մրցավազքում, ապա պետք է վերանայի իր դիրքորոշումը հեղինակային իրավունքի վերաբերյալ, և շուտով։ Անկախ նրանից, թե համաձայն եք մեզ հետ մեր բարոյական դիրքորոշման հարցում, սա այժմ դառնում է տնտեսության և նույնիսկ ազգային անվտանգության հարց։ Բոլոր ուժային բլոկները կառուցում են արհեստական սուպեր-գիտնականներ, սուպեր-հաքերներ և սուպեր-բանակներ։ Տեղեկատվության ազատությունը դառնում է գոյատևման հարց այս երկրների համար՝ նույնիսկ ազգային անվտանգության հարց։ Մեր թիմը աշխարհի տարբեր մասերից է, և մենք չունենք հատուկ կողմնորոշում։ Բայց մենք կխրախուսենք այն երկրներին, որոնք ունեն ուժեղ հեղինակային իրավունքի օրենքներ, օգտագործել այս գոյաբանական սպառնալիքը՝ դրանք բարեփոխելու համար։ Ուրեմն ինչ անել։ Մեր առաջին առաջարկը պարզ է՝ կրճատել հեղինակային իրավունքի ժամկետը։ ԱՄՆ-ում հեղինակային իրավունքը տրվում է հեղինակի մահից հետո 70 տարի ժամկետով։ Սա անհեթեթ է։ Մենք կարող ենք սա համապատասխանեցնել արտոնագրերին, որոնք տրվում են հայտից հետո 20 տարի ժամկետով։ Սա պետք է բավարար լինի գրքերի, հոդվածների, երաժշտության, արվեստի և այլ ստեղծագործական աշխատանքների հեղինակներին իրենց ջանքերի համար լիովին փոխհատուցելու համար (ներառյալ երկարաժամկետ նախագծերը, ինչպիսիք են ֆիլմերի ադապտացիաները)։ Այնուհետև, նվազագույնը, քաղաքականություն մշակողները պետք է ներառեն բացառություններ տեքստերի զանգվածային պահպանման և տարածման համար։ Եթե անհատական հաճախորդներից եկամուտի կորուստը հիմնական մտահոգությունն է, ապա անձնական մակարդակի տարածումը կարող է մնալ արգելված։ Փոխարենը, նրանք, ովքեր ունակ են կառավարել հսկայական պահոցներ՝ LLM-ներ վերապատրաստող ընկերություններ, ինչպես նաև գրադարաններ և այլ արխիվներ, կներառվեն այս բացառությունների մեջ։ Հեղինակային իրավունքի բարեփոխումը անհրաժեշտ է ազգային անվտանգության համար։ Կարճ ասած՝ չինական LLM-ները (ներառյալ DeepSeek-ը) վերապատրաստվել են իմ անօրինական գրքերի և հոդվածների արխիվի վրա՝ աշխարհի ամենամեծը։ Արևմուտքը պետք է վերանայի հեղինակային իրավունքի օրենքը՝ որպես ազգային անվտանգության հարց։ Խնդրում ենք տեսնել <a %(all_isbns)s>բնօրինակ բլոգային գրառումը</a> լրացուցիչ տեղեկությունների համար։ Մենք մարտահրավեր ենք նետել այս բարելավմանը։ Մենք կշնորհեինք առաջին տեղին $6,000, երկրորդ տեղին՝ $3,000, և երրորդ տեղին՝ $1,000։ Հաշվի առնելով մեծ արձագանքը և անհավանական ներկայացումները, մենք որոշել ենք մի փոքր ավելացնել մրցանակային ֆոնդը և շնորհել չորս երրորդ տեղ՝ յուրաքանչյուրը $500։ Հաղթողները ստորև են, բայց համոզվեք, որ դիտեք բոլոր ներկայացումները <a %(annas_archive)s>այստեղ</a>, կամ ներբեռնեք մեր <a %(a_2025_01_isbn_visualization_files)s>համակցված տոռենտը</a>։ Առաջին տեղ $6,000: phiresky Այս <a %(phiresky_github)s>ներկայացումը</a> (<a %(annas_archive_note_2951)s>Gitlab մեկնաբանություն</a>) պարզապես այն ամենն է, ինչ մենք ցանկանում էինք, և ավելին։ Մեզ հատկապես դուր եկան անհավանական ճկուն վիզուալիզացիայի տարբերակները (նույնիսկ աջակցելով հարմարեցված շեյդերներին), բայց նաև նախադրվածների համապարփակ ցանկով։ Մեզ դուր եկավ նաև, թե որքան արագ և հարթ է ամեն ինչ, պարզ իրականացումը (որը նույնիսկ չունի backend), խելացի փոքր քարտեզը և նրանց <a %(phiresky_github)s>բլոգային գրառման</a> մեջ ընդգրկուն բացատրությունը։ Անհավանական աշխատանք և արժանի հաղթող։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Մեր սրտերը լի են երախտագիտությամբ։ Նշանավոր գաղափարներ Բարձրահարկ շենքեր հազվադեպության համար Շատ սլայդերներ datasets համեմատելու համար, կարծես դուք DJ եք։ Չափման գիծ՝ գրքերի քանակով։ Գեղեցիկ պիտակներ։ Հիասքանչ հիմնական գունային սխեմա և ջերմային քարտեզ։ Ունիկալ քարտեզի տեսք և ֆիլտրեր Մեկնաբանություններ և ուղիղ վիճակագրություն Ուղիղ վիճակագրություն Որոշ գաղափարներ և իրականացումներ, որոնք մեզ հատկապես դուր եկան. Կարող ենք շարունակել երկար ժամանակ, բայց եկեք կանգ առնենք այստեղ։ Համոզվեք, որ դիտեք բոլոր ներկայացումները <a %(annas_archive)s>այստեղ</a>, կամ ներբեռնեք մեր <a %(a_2025_01_isbn_visualization_files)s>համակցված torrent-ը</a>։ Շատ ներկայացումներ, և յուրաքանչյուրը բերում է յուրահատուկ տեսանկյուն, թե՛ UI-ում, թե՛ իրականացման մեջ։ Մենք առնվազն կներառենք առաջին տեղը զբաղեցրած ներկայացումը մեր հիմնական կայքում, և գուցե նաև որոշ այլ ներկայացումներ։ Մենք նաև սկսել ենք մտածել, թե ինչպես կազմակերպել ամենահազվագյուտ գրքերի նույնականացման, հաստատման և ապա արխիվացման գործընթացը։ Ավելի շատ նորություններ այս ուղղությամբ։ Շնորհակալություն բոլորին, ովքեր մասնակցեցին։ Հիասքանչ է, որ այսքան շատ մարդիկ հոգ են տանում։ Հեշտությամբ փոխարկվող datasets արագ համեմատությունների համար։ Բոլոր ISBN-ները CADAL SSNO-ներ CERLALC տվյալների արտահոսք DuXiu SSID-ներ EBSCOhost-ի էլեկտրոնային գրքերի ինդեքս Google Գրքեր Goodreads Ինտերնետ Արխիվ ISBNdb ISBN Հրատարակիչների Գլոբալ Գրանցամատյան Libby Ֆայլեր Աննայի Արխիվում Nexus/STC OCLC/Worldcat Բաց Գրադարան Ռուսաստանի Պետական Գրադարան Տրանտորի կայսերական գրադարան Երկրորդ տեղ $3,000: hypha «Թեև կատարյալ քառակուսիներն ու ուղղանկյունները մաթեմատիկորեն հաճելի են, դրանք չեն ապահովում գերազանց տեղայնություն քարտեզագրման համատեքստում։ Ես հավատում եմ, որ այս Հիլբերտի կամ դասական Մորտոնի մեջ առկա ասիմետրիան թերություն չէ, այլ առանձնահատկություն։ Ինչպես Իտալիայի հայտնի կոշիկաձև ուրվագիծը այն անմիջապես ճանաչելի է դարձնում քարտեզի վրա, այս կորերի յուրահատուկ «հատկությունները» կարող են ծառայել որպես ճանաչողական հենանիշեր։ Այս առանձնահատկությունը կարող է բարելավել տարածական հիշողությունը և օգնել օգտատերերին կողմնորոշվել, ինչը կարող է հեշտացնել որոշակի շրջանների տեղադրումը կամ նախշերի նկատումը»։ Մեկ այլ անհավանական <a %(annas_archive_note_2913)s>ներկայացում</a>։ Չնայած առաջին տեղից այնքան ճկուն չէ, բայց մենք իրականում նախընտրեցինք դրա մակրո մակարդակի վիզուալիզացիան առաջին տեղից (տարածք լցնող կոր, սահմաններ, պիտակավորում, ընդգծում, պանորամա և խոշորացում)։ Ջո Դևիսի <a %(annas_archive_note_2971)s>մեկնաբանությունը</a> մեզ հետ համահունչ էր. Եվ դեռ շատ տարբերակներ վիզուալիզացիայի և ռենդերինգի համար, ինչպես նաև անհավանական հարթ և ինտուիտիվ UI։ Ամուր երկրորդ տեղ։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>) Մի քանի ամիս առաջ մենք հայտարարեցինք <a %(all_isbns)s>10,000 դոլար մրցանակ</a>՝ մեր տվյալների լավագույն հնարավոր վիզուալիզացիան ստեղծելու համար, որը ցույց է տալիս ISBN տարածքը։ Մենք շեշտեցինք, թե որ ֆայլերն ենք արդեն արխիվացրել և որոնք՝ ոչ, և հետագայում ավելացրեցինք տվյալների հավաքածու, որը նկարագրում է, թե քանի գրադարաններ ունեն ISBN-ներ (հազվադեպության չափանիշ)։ Մենք հիացած ենք արձագանքով։ Այնքան շատ ստեղծագործականություն է եղել։ Մեծ շնորհակալություն բոլորին, ովքեր մասնակցել են. ձեր էներգիան և ոգևորությունը վարակիչ են։ Վերջապես մենք ցանկանում էինք պատասխանել հետևյալ հարցերին՝ <strong>որ գրքերն են գոյություն ունեն աշխարհում, քանիսն ենք արդեն արխիվացրել, և որ գրքերին պետք է կենտրոնանանք հաջորդիվ։</strong> Հիանալի է տեսնել, որ այսքան շատ մարդիկ հետաքրքրված են այս հարցերով։ Մենք սկսեցինք ինքներս հիմնական վիզուալիզացիայից։ 300 կբ-ից պակասում, այս պատկերը հակիրճ ներկայացնում է մարդկության պատմության մեջ երբևէ հավաքված ամենամեծ բաց «գրքերի ցուցակը»։ Երրորդ տեղ $500 #1: maxlion Այս <a %(annas_archive_note_2940)s>ներկայացման</a> մեջ մեզ շատ դուր եկան տարբեր տեսակետները, հատկապես համեմատական և հրատարակչական տեսակետները։ Երրորդ տեղ $500 #2: abetusk Թեև ոչ ամենաշքեղ UI-ն, այս <a %(annas_archive_note_2917)s>ներկայացումը</a> շատ պահանջներ է բավարարում։ Մեզ հատկապես դուր եկավ դրա համեմատական հատկությունը։ Երրորդ տեղ $500 #3: conundrumer0 Ինչպես առաջին տեղը, այս <a %(annas_archive_note_2975)s>ներկայացումը</a> տպավորեց մեզ իր ճկունությամբ։ Վերջիվերջո, սա է, որ դարձնում է մեծ վիզուալիզացիոն գործիք՝ առավելագույն ճկունություն հզոր օգտատերերի համար, միաժամանակ պահելով ամեն ինչ պարզ միջին օգտատերերի համար։ Երրորդ տեղ $500 #4: charelf Վերջին <a %(annas_archive_note_2947)s>ներկայացումը</a>, որը ստացավ պարգև, բավականին պարզ է, բայց ունի որոշ յուրահատուկ հատկություններ, որոնք մեզ շատ դուր եկան։ Մեզ դուր եկավ, թե ինչպես են նրանք ցույց տալիս, թե քանի datasets են ընդգրկում որոշակի ISBN որպես հանրաճանաչության/հուսալիության չափանիշ։ Մեզ նաև շատ դուր եկավ համեմատությունների համար թափանցիկության սահիկի օգտագործման պարզությունը, բայց արդյունավետությունը։ $10,000 ISBN պատկերավորման մրցանակի հաղթողները Կարճ ասած՝ մենք ստացել ենք անհավանական ներկայացումներ $10,000 ISBN պատկերավորման մրցանակի համար։ Նախապատմություն Ինչպե՞ս կարող է «Աննայի Արխիվը» հասնել իր առաքելությանը՝ պահուստավորել մարդկության ամբողջ գիտելիքը, առանց իմանալու, թե որ գրքերն են դեռևս առկա։ Մեզ անհրաժեշտ է TODO ցուցակ։ Դրա քարտեզագրումը կարելի է անել ISBN համարների միջոցով, որոնք 1970-ականներից ի վեր նշանակվում են յուրաքանչյուր հրատարակված գրքին (շատ երկրներում)։ Չկա կենտրոնական մարմին, որը գիտի բոլոր ISBN նշանակումները։ Փոխարենը, դա բաշխված համակարգ է, որտեղ երկրները ստանում են համարների տիրույթներ, որոնք այնուհետև նշանակվում են խոշոր հրատարակիչներին, որոնք կարող են հետագայում բաժանել տիրույթները փոքր հրատարակիչներին։ Վերջապես, անհատական համարները նշանակվում են գրքերին։ Մենք սկսեցինք քարտեզագրել ISBN-ները <a %(blog)s>երկու տարի առաջ</a>՝ մեր ISBNdb-ի քերծմամբ։ Այդ ժամանակից ի վեր, մենք քերծել ենք շատ ավելի metadata աղբյուրներ, ինչպիսիք են <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby և այլն։ Լրիվ ցուցակը կարելի է գտնել «Datasets» և «Torrents» էջերում «Աննայի Արխիվում»։ Այժմ մենք ունենք աշխարհի ամենամեծ բաց, հեշտությամբ ներբեռնվող գրքերի metadata հավաքածուն (և հետևաբար ISBN-ներ)։ Մենք <a %(blog)s>ընդարձակորեն գրել ենք</a> այն մասին, թե ինչու ենք կարևորում պահպանումը և ինչու ենք ներկայումս գտնվում կրիտիկական պատուհանում։ Մենք պետք է այժմ հայտնաբերենք հազվագյուտ, անտեսված և եզակի վտանգված գրքերը և պահպանենք դրանք։ Աշխարհի բոլոր գրքերի լավ metadata ունենալը օգնում է դրան։ $10,000 պարգև Ուժեղ ուշադրություն կդարձվի օգտագործելիությանը և որքան լավ է այն նայվում։ Ցուցադրել իրական մետադատան անհատական ISBN-ների համար, երբ մեծացնում եք, օրինակ՝ վերնագիր և հեղինակ։ Լավագույն տարածք-լրացնող կոր։ Օրինակ՝ զիգզագ, առաջին տողում 0-ից 4 գնալով, ապա հետ (հակառակ) երկրորդ տողում 5-ից 9 — ռեկուրսիվ կիրառված։ Տարբեր կամ հարմարեցվող գունային սխեմաներ։ Հատուկ դիտումներ տվյալների հավաքածուները համեմատելու համար։ Խնդիրների լուծման եղանակներ, օրինակ՝ այլ մետադատա, որոնք լավ չեն համընկնում (օրինակ՝ շատ տարբեր վերնագրեր)։ Պատկերների նշագրում մեկնաբանություններով՝ ISBN-ների կամ տիրույթների վրա։ Ցանկացած հեուրիստիկա հազվագյուտ կամ վտանգված գրքերը հայտնաբերելու համար։ Ցանկացած ստեղծագործական գաղափարներ, որոնք կարող եք առաջարկել։ Կոդ Այս պատկերները ստեղծելու կոդը, ինչպես նաև այլ օրինակներ, կարելի է գտնել <a %(annas_archive)s>այս թղթապանակում</a>։ Մենք մշակեցինք կոմպակտ տվյալների ձևաչափ, որի միջոցով բոլոր անհրաժեշտ ISBN տեղեկատվությունը կազմում է մոտ 75ՄԲ (սեղմված)։ Տվյալների ձևաչափի նկարագրությունը և այն ստեղծելու կոդը կարելի է գտնել <a %(annas_archive_l1244_1319)s>այստեղ</a>։ Պարգևի համար պարտադիր չէ դա օգտագործել, բայց դա հավանաբար ամենահարմար ձևաչափն է սկսելու համար։ Դուք կարող եք փոխակերպել մեր մետադատան ինչպես ցանկանում եք (թեև ձեր ամբողջ կոդը պետք է լինի բաց կոդով)։ Մենք անհամբեր սպասում ենք տեսնել, թե ինչ կստեղծեք։ Հաջողություն։ Fork արեք այս պահոցը և խմբագրեք այս բլոգի գրառման HTML-ը (մեր Flask backend-ից բացի այլ backend-ներ թույլատրված չեն)։ Պատկերը վերևում պետք է լինի հարթորեն խոշորացվող, որպեսզի կարողանաք խոշորացնել մինչև անհատական ISBN-ներ։ ISBN-ների վրա սեղմելը պետք է տանի դեպի metadata էջ կամ որոնում «Աննայի Արխիվում»։ Դուք դեռ պետք է կարողանաք անցնել բոլոր տարբեր datasets միջև։ Երկրների տիրույթները և հրատարակիչների տիրույթները պետք է ընդգծվեն մկնիկի վրա պահելիս։ Կարող եք օգտագործել օրինակ <a %(github_xlcnd_isbnlib)s>data4info.py in isbnlib</a>՝ երկրի տվյալների համար, և մեր «isbngrp» քերծումը՝ հրատարակիչների համար (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)։ Այն պետք է լավ աշխատի ինչպես դեսքթոփ, այնպես էլ բջջային սարքերում։ Այստեղ շատ բան կա ուսումնասիրելու, ուստի մենք հայտարարում ենք պարգև՝ վերոնշյալ տեսանելիությունը բարելավելու համար։ Ի տարբերություն մեր շատ պարգևների, սա ժամանակային սահմանափակում ունի։ Դուք պետք է <a %(annas_archive)s>ներկայացնեք</a> ձեր բաց կոդը մինչև 2025-01-31 (23:59 UTC)։ Լավագույն ներկայացումը կստանա $6,000, երկրորդ տեղը՝ $3,000, իսկ երրորդ տեղը՝ $1,000։ Բոլոր պարգևները կշնորհվեն Monero (XMR) միջոցով։ Ստորև ներկայացված են նվազագույն չափանիշները։ Եթե ոչ մի ներկայացում չի համապատասխանում չափանիշներին, մենք կարող ենք դեռևս որոշ պարգևներ շնորհել, բայց դա կլինի մեր հայեցողությամբ։ Լրացուցիչ միավորների համար (սրանք պարզապես գաղափարներ են՝ թողեք ձեր ստեղծագործությունը ազատ վազի): Դուք ԿԱՐՈՂ ԵՔ ամբողջությամբ շեղվել նվազագույն չափանիշներից և կատարել ամբողջովին այլ վիզուալիզացիա։ Եթե դա իսկապես տպավորիչ է, ապա դա կարող է որակավորվել պարգևի համար, բայց մեր հայեցողությամբ։ Կատարեք ներկայացումներ՝ մեկնաբանություն թողնելով <a %(annas_archive)s>այս խնդրի</a> վրա՝ ձեր պատճենված պահոցին, միացման հարցմանը կամ տարբերությանը հղումով։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Այս պատկերը 1000×800 պիքսել է։ Յուրաքանչյուր պիքսել ներկայացնում է 2,500 ISBN։ Եթե մենք ունենք ֆայլ որևէ ISBN-ի համար, մենք այդ պիքսելը դարձնում ենք ավելի կանաչ։ Եթե մենք գիտենք, որ ISBN թողարկվել է, բայց չունենք համապատասխան ֆայլ, մենք այն դարձնում ենք ավելի կարմիր։ 300 կբ-ից պակասում, այս պատկերը հակիրճ ներկայացնում է մարդկության պատմության մեջ երբևէ հավաքված ամենամեծ բաց «գրքերի ցուցակը» (մի քանի հարյուր ԳԲ սեղմված ամբողջությամբ)։ Այն նաև ցույց է տալիս՝ դեռ շատ աշխատանք կա գրքերի պահուստավորման համար (մենք ունենք միայն 16%)։ Բոլոր ISBN-ների պատկերացում — $10,000 պարգև 2025-01-31-ին Այս պատկերը ներկայացնում է մարդկության պատմության մեջ երբևէ հավաքված ամենամեծ բաց «գրքերի ցուցակը»։ Տեսանելիություն Բացի ընդհանուր պատկերից, մենք կարող ենք նաև դիտել առանձին datasets, որոնք ձեռք ենք բերել։ Օգտագործեք ընտրացանկը և կոճակները՝ դրանց միջև անցնելու համար։ Այս նկարներում կան շատ հետաքրքիր նախշեր։ Ինչո՞ւ է որոշակի գծերի և բլոկների կանոնավորություն, որը կարծես տեղի է ունենում տարբեր մասշտաբներում։ Ի՞նչ են դատարկ տարածքները։ Ինչո՞ւ են որոշ datasets այդքան խիտ։ Այս հարցերը թողնում ենք ընթերցողի համար որպես վարժություն։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Եզրակացություն Այս ստանդարտով մենք կարող ենք թողարկումները կատարել ավելի աստիճանաբար և ավելի հեշտությամբ ավելացնել նոր տվյալների աղբյուրներ։ Մենք արդեն ունենք մի քանի հետաքրքիր թողարկումներ պատրաստման փուլում։ Մենք նաև հույս ունենք, որ այլ ստվերային գրադարանների համար ավելի հեշտ կլինի արտացոլել մեր հավաքածուները։ Ի վերջո, մեր նպատակն է պահպանել մարդկային գիտելիքներն ու մշակույթը հավերժ, այնպես որ որքան շատ կրկնօրինակներ, այնքան լավ։ Օրինակ Եկեք դիտարկենք մեր վերջին Z-Library թողարկումը որպես օրինակ։ Այն բաղկացած է երկու հավաքածուներից՝ “<span style="background: #fffaa3">zlib3_records</span>” և “<span style="background: #ffd6fe">zlib3_files</span>”։ Սա թույլ է տալիս մեզ առանձին հավաքել և թողարկել մետատվյալների գրառումները իրական գրքերի ֆայլերից։ Այսպիսով, մենք թողարկել ենք երկու տորրենտ մետատվյալների ֆայլերով՝ Մենք նաև թողարկել ենք մի շարք տորրենտներ բինար տվյալների թղթապանակներով, բայց միայն “<span style="background: #ffd6fe">zlib3_files</span>” հավաքածուի համար, ընդհանուր 62 հատ։ Գործարկելով <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> մենք կարող ենք տեսնել, թե ինչ կա ներսում՝ Այս դեպքում, դա գիրք է, որի մետատվյալները հաղորդվում են Z-Library-ի կողմից։ Վերին մակարդակում մենք ունենք միայն “aacid” և “metadata”, բայց ոչ “data_folder”, քանի որ համապատասխան բինար տվյալներ չկան։ AACID-ը պարունակում է “22430000” որպես հիմնական ID, որը մենք կարող ենք տեսնել, որ վերցված է “zlibrary_id”-ից։ Մենք կարող ենք ակնկալել, որ այս հավաքածուի մյուս AAC-ները կունենան նույն կառուցվածքը։ Հիմա եկեք գործարկենք <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>՝ Սա շատ ավելի փոքր AAC մետատվյալ է, թեև այս AAC-ի հիմնական մասը գտնվում է այլ տեղում բինար ֆայլում։ Ի վերջո, այս անգամ մենք ունենք “data_folder”, այնպես որ մենք կարող ենք ակնկալել, որ համապատասխան բինար տվյալները գտնվում են <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>։ “Metadata”-ն պարունակում է “zlibrary_id”, այնպես որ մենք կարող ենք հեշտությամբ կապել այն համապատասխան AAC-ի հետ “zlib_records” հավաքածուում։ Մենք կարող էինք կապել մի շարք տարբեր ձևերով, օրինակ՝ AACID-ի միջոցով՝ ստանդարտը դա չի պահանջում։ Նշենք, որ “metadata” դաշտը պարտադիր չէ, որ լինի JSON։ Այն կարող է լինել XML կամ ցանկացած այլ տվյալների ձևաչափ պարունակող տող։ Դուք նույնիսկ կարող եք մետատվյալների տեղեկատվությունը պահել համապատասխան բինար բլոբում, օրինակ՝ եթե դա շատ տվյալներ է։ Հետերոգեն ֆայլեր և metadata, որքան հնարավոր է մոտ սկզբնական ձևաչափին: Բինարային տվյալները կարող են ուղղակիորեն մատուցվել վեբ սերվերների կողմից, ինչպիսիք են Nginx-ը: Հետերոգեն նույնացուցիչներ աղբյուր գրադարաններում, կամ նույնիսկ նույնացուցիչների բացակայություն: Metadata-ի առանձին թողարկումներ ընդդեմ ֆայլային տվյալների, կամ միայն metadata-ի թողարկումներ (օրինակ՝ մեր ISBNdb թողարկումը): Բաշխում տոռենտների միջոցով, սակայն այլ բաշխման մեթոդների հնարավորությամբ (օրինակ՝ IPFS): Անփոփոխ գրառումներ, քանի որ պետք է ենթադրենք, որ մեր տոռենտները հավերժ կապրեն: Ինկրեմենտալ թողարկումներ / հավելվող թողարկումներ: Մեքենայով ընթեռնելի և գրելի, հարմարավետ և արագ, հատկապես մեր ստեկի համար (Python, MySQL, ElasticSearch, Transmission, Debian, ext4): Մարդկային որոշ չափով հեշտ ստուգում, թեև սա երկրորդական է մեքենայով ընթեռնելիության համեմատ: Հեշտ է մեր հավաքածուները սերմանել ստանդարտ վարձակալված seedbox-ով: Դիզայնի նպատակներ Մենք չենք մտահոգվում, որ ֆայլերը հեշտ լինի ձեռքով նավարկելու սկավառակի վրա, կամ որոնելի առանց նախնական մշակման: Մենք չենք մտահոգվում, որ լինենք ուղղակիորեն համատեղելի գոյություն ունեցող գրադարանային ծրագրաշարի հետ: Թեև պետք է հեշտ լինի յուրաքանչյուրին սերմանել մեր հավաքածուն տոռենտների միջոցով, մենք չենք ակնկալում, որ ֆայլերը օգտագործելի կլինեն առանց զգալի տեխնիկական գիտելիքների և նվիրվածության: Մեր հիմնական օգտագործման դեպքը ֆայլերի և դրանց հետ կապված metadata-ի բաշխումն է տարբեր գոյություն ունեցող հավաքածուներից: Մեր ամենակարևոր նկատառումները հետևյալն են. Որոշ ոչ նպատակներ: Քանի որ Աննայի Արխիվը բաց կոդով է, մենք ցանկանում ենք անմիջապես օգտագործել մեր ձևաչափը։ Երբ մենք թարմացնում ենք մեր որոնման ինդեքսը, մենք մուտք ենք գործում միայն հանրային հասանելի ուղիներ, որպեսզի մեր գրադարանը պատճենող ցանկացած մեկը կարողանա արագ սկսել։ <strong>AAC.</strong> AAC (Աննայի Արխիվի Կոնտեյներ) միակ տարր է, որը բաղկացած է <strong>մետադատայից</strong>, և ըստ ցանկության <strong>բինար տվյալներից</strong>, որոնք երկուսն էլ անփոփոխ են։ Այն ունի գլոբալ եզակի նույնացուցիչ, որը կոչվում է <strong>AACID</strong>։ <strong>AACID.</strong> AACID-ի ձևաչափն այսպիսին է՝ <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>։ Օրինակ, իրական AACID, որը մենք թողարկել ենք, հետևյալն է՝ <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>։ <strong>AACID տիրույթ.</strong> Քանի որ AACID-ները պարունակում են մոնոտոնիկորեն աճող ժամանակագրություններ, մենք կարող ենք դա օգտագործել որոշակի հավաքածուի շրջանակներում տիրույթներ նշելու համար։ Մենք օգտագործում ենք այս ձևաչափը՝ <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, որտեղ ժամանակագրությունները ներառական են։ Սա համահունչ է ISO 8601 նշանավորմանը։ Տիրույթները շարունակական են և կարող են համընկնել, բայց համընկնման դեպքում պետք է պարունակեն նույնական գրառումներ, ինչպես նախորդ թողարկվածը տվյալ հավաքածուում (քանի որ AAC-ները անփոփոխ են)։ Բացակայող գրառումները թույլատրված չեն։ <code>{collection}</code>: հավաքածուի անունը, որը կարող է պարունակել ASCII տառեր, թվեր և ընդգծումներ (բայց ոչ կրկնակի ընդգծումներ)։ <code>{collection-specific ID}</code>: հավաքածուի հատուկ նույնացուցիչ, եթե կիրառելի է, օրինակ՝ Z-Library ID։ Կարող է բաց թողնվել կամ կրճատվել։ Պետք է բաց թողնվի կամ կրճատվի, եթե AACID-ը այլապես գերազանցի 150 նիշը։ <code>{ISO 8601 timestamp}</code>: ISO 8601-ի կարճ տարբերակը, միշտ UTC-ում, օրինակ՝ <code>20220723T194746Z</code>։ Այս թիվը պետք է մոնոտոնիկորեն աճի յուրաքանչյուր թողարկման համար, թեև դրա ճշգրիտ սեմանտիկան կարող է տարբեր լինել հավաքածուից։ Մենք առաջարկում ենք օգտագործել քերելու կամ ID-ի գեներացման ժամանակը։ <code>{shortuuid}</code>: UUID, բայց սեղմված ASCII-ի, օրինակ՝ base57-ի օգտագործմամբ։ Մենք ներկայումս օգտագործում ենք <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python գրադարանը։ <strong>Բինար տվյալների թղթապանակ.</strong> Թղթապանակ AAC-ների տիրույթի բինար տվյալներով, մեկ հատուկ հավաքածուի համար։ Դրանք ունեն հետևյալ հատկությունները: Թղթապանակը պետք է պարունակի տվյալների ֆայլեր բոլոր AAC-ների համար նշված տիրույթում։ Յուրաքանչյուր տվյալների ֆայլ պետք է ունենա իր AACID-ը որպես ֆայլի անուն (առանց ընդլայնումների)։ Տնօրենի անունը պետք է լինի AACID տիրույթ, որը նախորդված է <code style="color: green">annas_archive_data__</code> և չունի վերջածանց։ Օրինակ, մեր իրական թողարկումներից մեկը ունի հետևյալ անունով թղթապանակ՝<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>։ Խորհուրդ է տրվում այս թղթապանակները դարձնել որոշ չափով կառավարելի չափերով, օրինակ՝ ոչ ավելի մեծ քան 100GB-1TB յուրաքանչյուր, թեև այս առաջարկությունը կարող է ժամանակի ընթացքում փոխվել։ <strong>Հավաքածու.</strong> Յուրաքանչյուր AAC պատկանում է հավաքածուի, որը ըստ սահմանման AAC-ների ցուցակ է, որոնք սեմանտիկորեն համահունչ են։ Դա նշանակում է, որ եթե դուք էական փոփոխություն եք կատարում մետադատայի ձևաչափում, ապա պետք է ստեղծեք նոր հավաքածու։ Ստանդարտը <strong>Մետադատայի ֆայլ.</strong> Մետադատայի ֆայլը պարունակում է AAC-ների տիրույթի մետադատան, մեկ հատուկ հավաքածուի համար։ Դրանք ունեն հետևյալ հատկությունները. <code>data_folder</code> ըստ ցանկության է, և այն բինար տվյալների թղթապանակի անունն է, որը պարունակում է համապատասխան բինար տվյալները։ Համապատասխան բինար տվյալների ֆայլի անունը այդ թղթապանակում գրառման AACID-ն է։ Յուրաքանչյուր JSON օբյեկտ պետք է պարունակի հետևյալ դաշտերը վերին մակարդակում՝ <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (ըստ ցանկության)։ Այլ դաշտեր թույլատրված չեն։ Ֆայլի անունը պետք է լինի AACID տիրույթ, նախածանցված <code style="color: red">annas_archive_meta__</code> և հետևյալով <code>.jsonl.zstd</code>։ Օրինակ, մեր թողարկումներից մեկը կոչվում է<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>։ Ինչպես ցույց է տալիս ֆայլի ընդլայնումը, ֆայլի տեսակը <a %(jsonlines)s>JSON Lines</a> է, սեղմված <a %(zstd)s>Zstandard</a>-ով։ <code>metadata</code> կամայական մետադատա է, ըստ հավաքածուի սեմանտիկայի։ Այն պետք է սեմանտիկորեն համահունչ լինի հավաքածուի ներսում։ <code style="color: red">annas_archive_meta__</code> նախածանցը կարող է հարմարեցվել ձեր հաստատության անվանը, օրինակ՝ <code style="color: red">my_institute_meta__</code>։ <strong>«Գրառումներ» և «ֆայլեր» հավաքածուներ.</strong> Ըստ պայմանավորվածության, հաճախ հարմար է «գրառումներ» և «ֆայլեր» թողարկել որպես տարբեր հավաքածուներ, որպեսզի դրանք կարողանան թողարկվել տարբեր ժամանակացույցերով, օրինակ՝ հիմնված քերելու արագությունների վրա։ «Գրառումը» մետադատայի միայն հավաքածու է, որը պարունակում է տեղեկություններ, ինչպիսիք են գրքերի վերնագրերը, հեղինակները, ISBN-ները և այլն, մինչդեռ «ֆայլերը» այն հավաքածուներն են, որոնք պարունակում են իրական ֆայլերը (pdf, epub)։ Վերջապես, մենք որոշեցինք համեմատաբար պարզ ստանդարտի վրա։ Այն բավականին ազատ է, ոչ նորմատիվ և ընթացքի մեջ գտնվող աշխատանք։ <strong>Տորրենտներ։</strong> Մետատվյալների ֆայլերը և բինար տվյալների թղթապանակները կարող են միավորվել տորրենտներում, մեկ տորրենտ մեկ մետատվյալների ֆայլի համար, կամ մեկ տորրենտ մեկ բինար տվյալների թղթապանակի համար։ Տորրենտները պետք է ունենան սկզբնական ֆայլի/թղթապանակի անունը և <code>.torrent</code> վերջածանցը որպես իրենց ֆայլի անուն։ <a %(wikipedia_annas_archive)s>Աննայի Արխիվը</a> դարձել է աշխարհի ամենամեծ ստվերային գրադարանը, և միակ ստվերային գրադարանը իր մասշտաբով, որը լիովին բաց կոդով և բաց տվյալներով է։ Ստորև ներկայացված է աղյուսակ մեր Տվյալների հավաքածուների էջից (մի փոքր փոփոխված)։ Մենք դա իրականացրեցինք երեք եղանակով․ Արտացոլելով գոյություն ունեցող բաց տվյալների ստվերային գրադարանները (ինչպես Sci-Hub և Library Genesis)։ Օգնել ստվերային գրադարաններին, որոնք ցանկանում են ավելի բաց լինել, բայց չունեին ժամանակ կամ ռեսուրսներ դա անելու համար (ինչպես Libgen կոմիքսների հավաքածուն): Գրադարանների քերում, որոնք չեն ցանկանում կիսվել մեծ քանակությամբ (ինչպես Z-Library): (2) և (3) համար մենք այժմ ինքներս կառավարում ենք զգալի քանակությամբ տոռենտների հավաքածու (100-ավոր TB-ներ): Մինչ այժմ մենք մոտեցել ենք այս հավաքածուներին որպես մեկանգամյա, ինչը նշանակում է յուրահատուկ ենթակառուցվածք և տվյալների կազմակերպում յուրաքանչյուր հավաքածուի համար: Սա զգալի բեռ է ավելացնում յուրաքանչյուր թողարկմանը և հատկապես դժվարացնում է ավելի ինկրեմենտալ թողարկումներ անել: Ահա թե ինչու մենք որոշեցինք ստանդարտացնել մեր թողարկումները: Սա տեխնիկական բլոգային գրառում է, որտեղ մենք ներկայացնում ենք մեր ստանդարտը՝ <strong>Աննայի Արխիվի Կոնտեյներներ</strong>: Աննայի Արխիվի Կոնտեյներներ (AAC): ստանդարտացնելով աշխարհի ամենամեծ ստվերային գրադարանի թողարկումները Աննայի Արխիվը դարձել է աշխարհի ամենամեծ ստվերային գրադարանը, ինչը պահանջում է ստանդարտացնել մեր թողարկումները։ 300GB+ գրքի շապիկներ թողարկված Վերջապես, մենք ուրախ ենք հայտարարել փոքր թողարկման մասին։ Համագործակցելով Libgen.rs ճյուղի աշխատակիցների հետ, մենք կիսում ենք նրանց բոլոր գրքերի շապիկները տոռենտների և IPFS-ի միջոցով։ Սա կբաշխի շապիկների դիտման ծանրաբեռնվածությունը ավելի շատ մեքենաների միջև և կպահպանի դրանք ավելի լավ։ Շատ դեպքերում (բայց ոչ բոլորում), գրքերի շապիկները ներառված են հենց ֆայլերում, ուստի սա մի տեսակ "ծագած տվյալներ" է։ Բայց ունենալով այն IPFS-ում՝ այն դեռ շատ օգտակար է ինչպես Աննայի Արխիվի, այնպես էլ տարբեր Library Genesis ճյուղերի ամենօրյա աշխատանքի համար։ Ինչպես միշտ, այս թողարկումը կարող եք գտնել Pirate Library Mirror-ում (Խմբագրում՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի Արխիվ</a>)։ Մենք այստեղ հղում չենք տա, բայց կարող եք հեշտությամբ գտնել այն։ Հուսով ենք, որ կարող ենք մի փոքր թուլացնել մեր տեմպը, այժմ, երբ ունենք Z-Library-ի արժանապատիվ այլընտրանք։ Այս աշխատանքային ծանրաբեռնվածությունը հատկապես կայուն չէ։ Եթե հետաքրքրված եք ծրագրավորմամբ, սերվերային աշխատանքներով կամ պահպանման աշխատանքներով, անպայման կապվեք մեզ հետ։ Կա դեռ շատ <a %(annas_archive)s>աշխատանք անելու</a>։ Շնորհակալություն ձեր հետաքրքրության և աջակցության համար։ Փոխանցում դեպի ElasticSearch Որոշ հարցումներ շատ երկար էին տևում, այնքան, որ նրանք զբաղեցնում էին բոլոր բաց միացումները։ MySQL-ը լռելյայն ունի նվազագույն բառի երկարություն, կամ ձեր ինդեքսը կարող է շատ մեծանալ։ Մարդիկ հաղորդում էին, որ չեն կարողանում որոնել “Ben Hur”։ Որոնումը միայն որոշ չափով արագ էր, երբ ամբողջությամբ բեռնված էր հիշողության մեջ, ինչը պահանջում էր մեզ ավելի թանկ մեքենա՝ այս ամենը գործարկելու համար, ինչպես նաև որոշ հրամաններ՝ ինդեքսը նախաբեռնելու համար։ Մենք չէինք կարողանա հեշտությամբ ընդլայնել այն՝ նոր հնարավորություններ կառուցելու համար, ինչպես ավելի լավ <a %(wikipedia_cjk_characters)s>տոկենիզացիա ոչ-բացատային լեզուների համար</a>, ֆիլտրում/ֆասետավորում, դասակարգում, "դուք նկատի ունեիք" առաջարկներ, ավտոմատ լրացում և այլն։ Մեր <a %(annas_archive)s>տոմսերից</a> մեկը մեր որոնման համակարգի հետ կապված խնդիրների հավաքածու էր։ Մենք օգտագործում էինք MySQL ամբողջական տեքստային որոնում, քանի որ մեր բոլոր տվյալները MySQL-ում էին։ Բայց այն ուներ իր սահմանափակումները։ Խոսելով մի շարք փորձագետների հետ, մենք որոշեցինք օգտագործել ElasticSearch։ Այն կատարյալ չէ (նրանց լռելյայն "դուք նկատի ունեիք" առաջարկներն ու ավտոմատ լրացման հնարավորությունները վատն են), բայց ընդհանուր առմամբ այն շատ ավելի լավ է, քան MySQL-ը որոնման համար։ Մենք դեռ <a %(youtube)s>շատ չենք վստահում</a> այն օգտագործել որևէ կարևոր տվյալների համար (չնայած նրանք մեծ <a %(elastic_co)s>առաջընթաց</a> են գրանցել), բայց ընդհանուր առմամբ մենք բավականին գոհ ենք փոփոխությունից։ Ներկայումս մենք իրականացրել ենք շատ ավելի արագ որոնում, ավելի լավ լեզվական աջակցություն, ավելի լավ համապատասխանության դասակարգում, տարբեր դասակարգման տարբերակներ և ֆիլտրում լեզվի/գրքի տեսակի/ֆայլի տեսակի վրա։ Եթե հետաքրքրված եք, թե ինչպես է այն աշխատում, <a %(annas_archive_l140)s>նայեք</a> <a %(annas_archive_l1115)s>այստեղ</a> <a %(annas_archive_l1635)s>մանրամասներին</a>։ Այն բավականին հասանելի է, թեև կարող է ավելի շատ մեկնաբանություններ ունենալ… Աննայի Արխիվը ամբողջովին բաց կոդով է։ Մենք հավատում ենք, որ տեղեկատվությունը պետք է լինի ազատ, և մեր սեփական կոդը բացառություն չէ։ Մենք հրապարակել ենք մեր ամբողջ կոդը մեր մասնավոր հյուրընկալված Gitlab-ում՝ <a %(annas_archive)s>Աննայի Ծրագրակազմ</a>։ Մենք նաև օգտագործում ենք խնդիրների հետևողը՝ մեր աշխատանքը կազմակերպելու համար։ Եթե ցանկանում եք մասնակցել մեր զարգացմանը, սա հիանալի վայր է սկսելու համար։ Որպեսզի ձեզ ցույց տանք, թե ինչի վրա ենք աշխատում, ներկայացնում ենք մեր վերջերս կատարած աշխատանքը հաճախորդի կողմի կատարողականի բարելավման ուղղությամբ։ Քանի որ մենք դեռևս էջավորման մեխանիզմ չենք ներդրել, հաճախ վերադարձնում էինք շատ երկար որոնման էջեր՝ 100-200 արդյունքներով։ Մենք չէինք ցանկանում շատ շուտ կտրել որոնման արդյունքները, բայց դա նշանակում էր, որ որոշ սարքեր կդանդաղեին։ Դրա համար մենք կիրառեցինք մի փոքր հնարք՝ փաթաթեցինք որոնման արդյունքների մեծ մասը HTML մեկնաբանությունների մեջ (<code><!-- --></code>), և ապա գրեցինք մի փոքր Javascript, որը կճանաչեր, երբ արդյունքը պետք է տեսանելի դառնա, այդ պահին մենք կհանեինք մեկնաբանությունը։ DOM-ի "վիրտուալիզացիան" իրականացվել է 23 տողում, առանց բարդ գրադարանների անհրաժեշտության։ Սա այն արագ պրագմատիկ կոդն է, որը ստացվում է, երբ ժամանակը սահմանափակ է, և իրական խնդիրներ կան, որոնք պետք է լուծել։ Հաղորդվել է, որ մեր որոնումը այժմ լավ է աշխատում դանդաղ սարքերում։ Մեկ այլ մեծ ջանք էր տվյալների բազայի կառուցման ավտոմատացումը։ Երբ մենք գործարկեցինք, պարզապես տարբեր աղբյուրներ միացրեցինք։ Այժմ մենք ցանկանում ենք դրանք թարմացնել, ուստի գրեցինք մի շարք սցենարներ՝ նոր metadata ներբեռնելու համար երկու Library Genesis ճյուղերից և ինտեգրելու դրանք։ Նպատակն է ոչ միայն մեր արխիվի համար օգտակար դարձնել սա, այլ նաև հեշտացնել այն բոլորի համար, ովքեր ցանկանում են խաղալ ստվերային գրադարանի metadata-ով։ Նպատակը կլինի Jupyter նոթատետր, որը կունենա բոլոր տեսակի հետաքրքիր metadata, որպեսզի մենք կարողանանք ավելի շատ հետազոտություններ անել, ինչպես օրինակ՝ պարզել, թե <a %(blog)s>ISBN-ների քանի տոկոսն է պահպանվում ընդմիշտ</a>։ Վերջապես, մենք վերափոխեցինք մեր նվիրատվական համակարգը։ Այժմ կարող եք օգտագործել կրեդիտ քարտ՝ անմիջապես գումար փոխանցելու մեր կրիպտո դրամապանակներին՝ առանց իրականում կրիպտոարժույթների մասին որևէ բան իմանալու։ Մենք կշարունակենք հետևել, թե ինչպես է սա աշխատում գործնականում, բայց սա մեծ քայլ է։ Երբ Z-Library-ն փակվեց և նրա (ենթադրյալ) հիմնադիրները ձերբակալվեցին, մենք աշխատում ենք շուրջօրյա՝ ապահովելու լավ այլընտրանք Աննայի Արխիվով (մենք այստեղ հղում չենք դնի, բայց կարող եք որոնել Google-ում)։ Ահա որոշ բաներ, որոնք մենք վերջերս հասել ենք։ Աննայի Թարմացում՝ ամբողջովին բաց կոդով արխիվ, ElasticSearch, 300GB+ գրքերի շապիկներ Մենք աշխատում ենք շուրջօրյա՝ ապահովելու լավ այլընտրանք Աննայի Արխիվով։ Ահա որոշ բաներ, որոնք մենք վերջերս հասել ենք։ Վերլուծություն Սեմանտիկ կրկնօրինակները (նույն գրքի տարբեր սկաներ) տեսականորեն կարող են ֆիլտրվել, բայց դա բարդ է։ Երբ ձեռքով նայում էինք կոմիքսները, մենք գտանք շատ սխալ դրականներ։ Կան որոշ կրկնօրինակներ միայն MD5-ով, ինչը համեմատաբար վատնում է, բայց դրանց ֆիլտրումը մեզ կտա միայն մոտ 1% iն խնայողություն։ Այս մասշտաբով դա դեռ մոտ 1TB է, բայց նաև, այս մասշտաբով 1TB-ը իրականում նշանակություն չունի։ Մենք նախընտրում ենք չվտանգել տվյալների պատահական ոչնչացումը այս գործընթացում։ Մենք գտանք մի շարք ոչ գիրք տվյալներ, օրինակ՝ կոմիքսների հիման վրա նկարահանված ֆիլմեր։ Դա նույնպես վատնում է թվում, քանի որ դրանք արդեն լայնորեն հասանելի են այլ միջոցներով։ Սակայն, մենք հասկացանք, որ չենք կարող պարզապես ֆիլտրել ֆիլմերի ֆայլերը, քանի որ կան նաև <em>ինտերակտիվ կոմիքսներ</em>, որոնք թողարկվել են համակարգչի վրա, որոնք ինչ-որ մեկը ձայնագրել և պահպանել է որպես ֆիլմեր։ Վերջիվերջո, այն ամենը, ինչ մենք կարող էինք ջնջել հավաքածուից, միայն մի քանի տոկոս կխնայեր։ Հետո հիշեցինք, որ մենք տվյալների կուտակողներ ենք, և նրանք, ովքեր կարտացոլեն սա, նույնպես տվյալների կուտակողներ են, և այսպես, «ԻՆՉԻ՞ ՀԱՄԱՐ ՋՆՋԵԼ»! :) Երբ դուք ստանում եք 95TB ձեր պահեստային կլաստերում, փորձում եք հասկանալ, թե ինչ կա այնտեղ… Մենք որոշ վերլուծություն կատարեցինք՝ տեսնելու, արդյոք կարող ենք մի փոքր կրճատել չափը, օրինակ՝ կրկնօրինակները հեռացնելով։ Ահա մեր որոշ հայտնագործությունները. Ուստի մենք ձեզ ենք ներկայացնում ամբողջական, չփոփոխված հավաքածուն։ Դա շատ տվյալներ է, բայց մենք հույս ունենք, որ բավականաչափ մարդիկ կցանկանան այն սերմանել։ Համագործակցություն Հաշվի առնելով դրա չափը, այս հավաքածուն վաղուց մեր ցանկության ցուցակում էր, ուստի Z-Library-ի պահուստավորման հաջողությունից հետո մենք մեր հայացքը ուղղեցինք այս հավաքածուին։ Սկզբում մենք այն ուղղակիորեն քերում էինք, ինչը բավականին մարտահրավեր էր, քանի որ նրանց սերվերը լավագույն վիճակում չէր։ Այս կերպ մենք ստացանք մոտ 15TB, բայց դա դանդաղ էր ընթանում։ Բարեբախտաբար, մենք կարողացանք կապ հաստատել գրադարանի օպերատորի հետ, ով համաձայնեց ուղարկել մեզ բոլոր տվյալները ուղղակիորեն, ինչը շատ ավելի արագ էր։ Այնուամենայնիվ, ավելի քան կես տարի պահանջվեց բոլոր տվյալները փոխանցելու և մշակելու համար, և մենք գրեթե կորցրեցինք դրանք սկավառակի վնասման պատճառով, ինչը կնշանակեր սկսել ամեն ինչ նորից։ Այս փորձը մեզ ստիպեց հավատալ, որ կարևոր է այս տվյալները հնարավորինս արագ տարածել, որպեսզի դրանք կարողանան հայելավորվել ամենուր։ Մենք ընդամենը մեկ կամ երկու անհաջող ժամանակավորված միջադեպից հեռու ենք այս հավաքածուն ընդմիշտ կորցնելուց։ Հավաքածուն Արագ շարժվելը նշանակում է, որ հավաքածուն մի փոքր անկազմակերպ է… Եկեք նայենք։ Պատկերացրեք, որ մենք ունենք ֆայլային համակարգ (որը իրականում մենք բաժանում ենք տոռենտների միջև)։ Առաջին թղթապանակը, <code>/repository</code>, այս ամենի ավելի կառուցվածքային մասն է։ Այս թղթապանակը պարունակում է այսպես կոչված «հազար թղթապանակներ»՝ յուրաքանչյուրում հազար ֆայլերով, որոնք հերթականորեն համարակալված են տվյալների բազայում։ Թղթապանակը <code>0</code> պարունակում է ֆայլեր comic_id 0–999, և այդպես շարունակ։ Սա նույն սխեման է, որը Library Genesis-ը օգտագործել է իր գեղարվեստական և ոչ գեղարվեստական հավաքածուների համար։ Գաղափարն այն է, որ յուրաքանչյուր «հազար թղթապանակ» ավտոմատ կերպով վերածվում է տոռենտի, երբ այն լցվում է։ Սակայն, Libgen.li օպերատորը երբեք տոռենտներ չի ստեղծել այս հավաքածուի համար, և այդ պատճառով հազար թղթապանակները հավանաբար անհարմար դարձան և տեղ տվեցին «չդասավորված թղթապանակներին»։ Դրանք են <code>/comics0</code> մինչև <code>/comics4</code>։ Դրանք բոլորը պարունակում են եզակի թղթապանակային կառուցվածքներ, որոնք հավանաբար իմաստ ունեին ֆայլերը հավաքելու համար, բայց այժմ մեզ համար շատ իմաստ չունեն։ Բարեբախտաբար, metadata-ն դեռ ուղղակիորեն վերաբերում է այս բոլոր ֆայլերին, այնպես որ դրանց պահեստավորման կազմակերպումը սկավառակի վրա իրականում նշանակություն չունի։ Metadata-ն հասանելի է MySQL տվյալների բազայի ձևով։ Սա կարող է ներբեռնվել ուղղակիորեն Libgen.li կայքից, բայց մենք այն նույնպես հասանելի կդարձնենք տոռենտի միջոցով՝ մեր սեփական աղյուսակի հետ միասին, որը պարունակում է բոլոր MD5 հեշերը։ <q>Դոկտոր Բարբարա Գորդոնը փորձում է կորցնել իրեն գրադարանի առօրյա աշխարհում…</q> Libgen մասնաճյուղեր Նախ, մի փոքր նախապատմություն։ Դուք կարող եք ճանաչել Library Genesis-ը իրենց էպիկական գրքերի հավաքածուի համար։ Քիչ մարդիկ գիտեն, որ Library Genesis-ի կամավորները ստեղծել են այլ նախագծեր, ինչպիսիք են ամսագրերի և ստանդարտ փաստաթղթերի մեծ հավաքածուն, Sci-Hub-ի ամբողջական պահուստը (համագործակցելով Sci-Hub-ի հիմնադիր Ալեքսանդրա Էլբակյանի հետ) և, իհարկե, կոմիքսների հսկայական հավաքածուն։ Մի պահ Library Genesis-ի հայելիների տարբեր օպերատորները գնացին իրենց առանձին ճանապարհներով, ինչը հանգեցրեց ներկայիս իրավիճակին՝ ունենալով մի շարք տարբեր «մասնաճյուղեր», որոնք դեռ կրում են Library Genesis անունը։ Libgen.li մասնաճյուղը բացառիկ է այս կոմիքսների հավաքածուով, ինչպես նաև մեծ ամսագրերի հավաքածուով (որի վրա մենք նույնպես աշխատում ենք)։ Հավաքագրման արշավ Մենք այս տվյալները թողարկում ենք մեծ կտորներով։ Առաջին տոռենտը <code>/comics0</code>-ի է, որը մենք տեղադրել ենք մեկ հսկայական 12TB .tar ֆայլում։ Դա ավելի լավ է ձեր կոշտ սկավառակի և տոռենտ ծրագրակազմի համար, քան բազմաթիվ փոքր ֆայլեր։ Այս թողարկման շրջանակներում մենք կազմակերպում ենք հավաքագրման արշավ։ Մենք նպատակ ունենք հավաքել $20,000՝ այս հավաքածուի գործառնական և պայմանագրային ծախսերը ծածկելու, ինչպես նաև ընթացիկ և ապագա նախագծերը հնարավոր դարձնելու համար։ Մենք ունենք որոշ <em>հսկայական</em> նախագծեր ընթացքի մեջ։ <em>Ում եմ աջակցում իմ նվիրատվությամբ?</em> Կարճ ասած՝ մենք պահպանում ենք մարդկության բոլոր գիտելիքներն ու մշակույթը և դրանք հեշտությամբ հասանելի դարձնում։ Մեր բոլոր կոդերն ու տվյալները բաց կոդով են, մենք ամբողջությամբ կամավորների կողմից ղեկավարվող նախագիծ ենք, և մենք արդեն փրկել ենք 125TB գրքերի (Libgen-ի և Scihub-ի առկա տոռենտներից բացի)։ Վերջիվերջո, մենք կառուցում ենք մի մեխանիզմ, որը հնարավորություն է տալիս և խրախուսում մարդկանց գտնել, սկանավորել և պահել աշխարհի բոլոր գրքերը։ Մենք մեր գլխավոր պլանի մասին կգրենք ապագա գրառման մեջ։ :) Եթե դուք նվիրատվություն կատարեք 12 ամսվա «Հրաշալի Հավաքարար» անդամակցության համար ($780), դուք կարող եք <strong>«որդեգրել տոռենտ»</strong>, ինչը նշանակում է, որ մենք ձեր օգտանունը կամ հաղորդագրությունը կտեղադրենք տոռենտներից մեկի ֆայլի անվան մեջ։ Դուք կարող եք նվիրատվություն կատարել՝ անցնելով <a %(wikipedia_annas_archive)s>Աննայի Արխիվ</a> և սեղմելով «Նվիրաբերել» կոճակը։ Մենք նաև փնտրում ենք ավելի շատ կամավորներ՝ ծրագրային ապահովման ինժեներներ, անվտանգության հետազոտողներ, անանուն առևտրական փորձագետներ և թարգմանիչներ։ Դուք կարող եք նաև աջակցել մեզ՝ տրամադրելով հոստինգի ծառայություններ։ Եվ իհարկե, խնդրում ենք սերմանել մեր տոռենտները։ Շնորհակալություն բոլորին, ովքեր արդեն այդքան առատաձեռնորեն աջակցել են մեզ։ Դուք իսկապես տարբերություն եք ստեղծում։ Ահա մինչ այժմ թողարկված տոռենտները (մենք դեռ մշակում ենք մնացածը). Բոլոր տոռենտները կարելի է գտնել <a %(wikipedia_annas_archive)s>Աննայի Արխիվ</a>-ում «Datasets» բաժնի տակ (մենք այնտեղ ուղղակի հղում չենք տալիս, որպեսզի այս բլոգի հղումները չհեռացվեն Reddit-ից, Twitter-ից և այլն)։ Այնտեղից հետևեք հղմանը Tor կայք։ <a %(news_ycombinator)s>Քննարկել Hacker News-ում</a> Ի՞նչ է հաջորդը։ Տոռենտների մի խումբ հիանալի է երկարաժամկետ պահպանման համար, բայց ոչ այնքան ամենօրյա հասանելիության համար։ Մենք կաշխատենք հոստինգի գործընկերների հետ՝ այս բոլոր տվյալները վեբում տեղադրելու համար (քանի որ Աննայի Արխիվը ոչինչ ուղղակիորեն չի հյուրընկալում)։ Իհարկե, դուք կկարողանաք գտնել այս ներբեռնման հղումները Աննայի Արխիվում։ Մենք նաև հրավիրում ենք բոլորին՝ այս տվյալներով ինչ-որ բան անել։ Օգնեք մեզ ավելի լավ վերլուծել այն, կրկնօրինակել այն, տեղադրել IPFS-ում, վերամշակել այն, ձեր AI մոդելները դրա վրա մարզել և այլն։ Դա ամբողջությամբ ձերն է, և մենք անհամբեր սպասում ենք տեսնել, թե ինչ կկատարեք դրա հետ։ Վերջապես, ինչպես արդեն ասվել է, մենք դեռ ունենք որոշ հսկայական թողարկումներ (եթե <em>ինչ-որ մեկը</em> կարողանար <em>պատահաբար</em> մեզ ուղարկել <em>որոշակի</em> ACS4 տվյալների բազայի արտահոսք, դուք գիտեք, թե որտեղ գտնել մեզ...), ինչպես նաև կառուցում ենք մեխանիզմը՝ աշխարհի բոլոր գրքերը պահելու համար։ Ուրեմն մնացեք մեզ հետ, մենք միայն նոր ենք սկսում։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Կոմիքսների ամենամեծ ստվերային գրադարանը հավանաբար պատկանում է Library Genesis-ի մի մասնաճյուղին՝ Libgen.li-ին։ Այդ կայքի միակ ադմինիստրատորը կարողացել է հավաքել ավելի քան 2 միլիոն ֆայլերից բաղկացած կոմիքսների անհավանական հավաքածու, որը կազմում է ավելի քան 95TB։ Սակայն, ի տարբերություն այլ Library Genesis հավաքածուների, այս մեկը հասանելի չէր մեծածավալ տոռենտների միջոցով։ Դուք կարող էիք մուտք գործել այս կոմիքսները միայն նրա դանդաղ անձնական սերվերի միջոցով՝ միակ խոցելի կետ։ Մինչև այսօր! Այս գրառման մեջ մենք կպատմենք ձեզ ավելին այս հավաքածուի մասին և մեր դրամահավաքի մասին՝ այս աշխատանքը աջակցելու համար։ Աննայի Արխիվը պահուստավորել է աշխարհի ամենամեծ կոմիքսների ստվերային գրադարանը (95TB) — դուք կարող եք օգնել սերմանել այն Աշխարհի ամենամեծ կոմիքսների ստվերային գրադարանը ուներ մեկ խոցելի կետ.. մինչև այսօր։ Զգուշացում՝ այս բլոգային գրառումը հնացած է։ Մենք որոշել ենք, որ IPFS-ը դեռ պատրաստ չէ հիմնական օգտագործման համար։ Մենք դեռ կկապենք ֆայլերը IPFS-ի վրա Աննայի Արխիվից, երբ հնարավոր է, բայց մենք այլևս չենք հյուրընկալելու այն ինքներս, և չենք խորհուրդ տալիս ուրիշներին հայելային օգտագործել IPFS-ով։ Խնդրում ենք տեսնել մեր Torrents էջը, եթե ցանկանում եք օգնել մեր հավաքածուի պահպանմանը։ 5,998,794 գրքեր IPFS-ում տեղադրելը Օրինակների բազմապատկում Վերադառնալով մեր սկզբնական հարցին՝ ինչպես կարող ենք պնդել, որ մեր հավաքածուները հավերժ կպահպանվեն։ Հիմնական խնդիրը այստեղ այն է, որ մեր հավաքածուն <a %(torrents_stats)s>արագորեն աճում է</a>՝ քերծելով և բաց կոդով դարձնելով որոշ հսկայական հավաքածուներ (վերևում արդեն կատարված հիանալի աշխատանքի՝ այլ բաց տվյալների ստվերային գրադարանների, ինչպիսիք են Sci-Hub-ը և Library Genesis-ը): Այս տվյալների աճը դժվարացնում է հավաքածուների արտացոլումը ամբողջ աշխարհում։ Տվյալների պահպանումը թանկ է։ Բայց մենք լավատես ենք, հատկապես հետևյալ երեք միտումները դիտարկելիս։ Մեր հավաքածուների <a %(annas_archive_stats)s>ընդհանուր ծավալը</a>, վերջին մի քանի ամիսների ընթացքում, բաժանված ըստ տոռենտ սիդերների քանակի: ՏՀԴ գների միտումները տարբեր աղբյուրներից (սեղմեք՝ ուսումնասիրությունը դիտելու համար)։ <a %(critical_window_chinese)s>Չինական տարբերակ 中文版</a>, քննարկել <a %(reddit)s>Reddit</a>-ում, <a %(news_ycombinator)s>Hacker News</a>-ում 1. Մենք հավաքել ենք հեշտ հասանելի պտուղները Սա անմիջապես հետևում է մեր վերոնշյալ առաջնահերթություններից։ Մենք նախընտրում ենք աշխատել մեծ հավաքածուների ազատագրման վրա։ Այժմ, երբ մենք ապահովել ենք աշխարհի ամենամեծ հավաքածուներից մի քանիսը, ակնկալում ենք, որ մեր աճը կլինի շատ ավելի դանդաղ։ Դեռևս կա փոքր հավաքածուների երկար պոչ, և նոր գրքեր ամեն օր սկանավորվում կամ հրատարակվում են, բայց արագությունը հավանաբար կլինի շատ ավելի դանդաղ։ Մենք կարող ենք դեռ կրկնապատկվել կամ նույնիսկ եռապատկվել չափով, բայց ավելի երկար ժամանակահատվածում։ OCR-ի բարելավումներ։ Առաջնահերթություններ Գիտության և ինժեներիայի ծրագրային կոդ Վերոնշյալ բոլորի գեղարվեստական կամ զվարճանքի տարբերակներ Աշխարհագրական տվյալներ (օրինակ՝ քարտեզներ, երկրաբանական հետազոտություններ) Կորպորացիաների կամ կառավարությունների ներքին տվյալներ (արտահոսքեր) Չափման տվյալներ, ինչպիսիք են գիտական չափումները, տնտեսական տվյալները, կորպորատիվ հաշվետվությունները Մետատվյալների գրառումներ ընդհանուր առմամբ (ոչ գեղարվեստական և գեղարվեստական; այլ մեդիա, արվեստ, մարդիկ և այլն; ներառյալ վերանայումներ) Ոչ գեղարվեստական գրքեր Ոչ գեղարվեստական ամսագրեր, թերթեր, ձեռնարկներ Ոչ գեղարվեստական զրույցների, վավերագրական ֆիլմերի, փոդքաստների սղագրություններ Օրգանական տվյալներ, ինչպիսիք են ԴՆԹ հաջորդականությունները, բույսերի սերմերը կամ միկրոբային նմուշները Ակադեմիական հոդվածներ, ամսագրեր, զեկույցներ Գիտության և ինժեներիայի կայքեր, առցանց քննարկումներ Իրավական կամ դատարանային գործընթացների սղագրություններ Հատուկ վտանգված ոչնչացման (օրինակ՝ պատերազմի, ֆինանսավորման կրճատումների, դատական հայցերի կամ քաղաքական հետապնդման պատճառով) Հազվագյուտ Հատուկ ուշադրության արժանի Ինչու՞ ենք մենք այդքան կարևորում հոդվածներն ու գրքերը: Եկեք մի կողմ դնենք պահպանման նկատմամբ մեր հիմնարար հավատը՝ մենք կարող ենք այդ մասին այլ գրառում գրել: Ուրեմն ինչու՞ հատկապես հոդվածներ և գրքեր: Պատասխանը պարզ է՝ <strong>տեղեկատվության խտություն</strong>: Պահեստավորման մեկ մեգաբայթի համար, գրավոր տեքստը պահպանում է ամենաշատ տեղեկատվությունը բոլոր մեդիաներից: Թեև մենք կարևորում ենք թե՛ գիտելիքը, թե՛ մշակույթը, մենք ավելի շատ կարևորում ենք առաջինը: Ընդհանուր առմամբ, մենք գտնում ենք տեղեկատվության խտության և պահպանման կարևորության հիերարխիա, որը մոտավորապես այսպես է թվում՝ Այս ցուցակի դասակարգումը որոշ չափով կամայական է՝ մի քանի կետեր հավասար են կամ մեր թիմի ներսում կան անհամաձայնություններ, և մենք հավանաբար մոռանում ենք որոշ կարևոր կատեգորիաներ։ Բայց սա մոտավորապես այն է, թե ինչպես ենք մենք առաջնահերթություն տալիս։ Այս կետերից որոշները չափազանց տարբեր են մյուսներից, որպեսզի մենք անհանգստանանք (կամ արդեն հոգ են տանում այլ հաստատություններ), ինչպիսիք են օրգանական տվյալները կամ աշխարհագրական տվյալները։ Բայց այս ցուցակի մեծ մասը իրականում կարևոր է մեզ համար։ Մեր առաջնահերթության մեկ այլ մեծ գործոն այն է, թե որքան վտանգված է որոշակի աշխատանքը։ Մենք նախընտրում ենք կենտրոնանալ այն աշխատանքների վրա, որոնք՝ Վերջապես, մենք կարևորում ենք մասշտաբը։ Մենք ունենք սահմանափակ ժամանակ և գումար, ուստի նախընտրում ենք մեկ ամիս ծախսել 10,000 գիրք փրկելու վրա, քան 1,000 գիրք՝ եթե դրանք մոտավորապես նույնքան արժեքավոր և վտանգված են։ <em><q>Կորցրածը չի կարող վերականգնվել, բայց եկեք փրկենք այն, ինչ մնացել է. ոչ թե պահարաններով և կողպեքներով, որոնք նրանց հեռացնում են հանրության աչքից և օգտագործումից՝ նրանց ժամանակի վատնումին հանձնելով, այլ օրինակների բազմապատկմամբ, որը նրանց կդնի պատահականության հասանելիությունից դուրս:</q></em><br>— Թոմաս Ջեֆերսոն, 1791 Ստվերային գրադարաններ Կոդը կարող է լինել բաց կոդով Github-ում, բայց Github-ը ամբողջությամբ չի կարող հեշտությամբ արտացոլվել և այդպիսով պահպանվել (թեև այս դեպքում կան բավարար չափով տարածված օրինակներ կոդի մեծ մասի պահոցների) Metadata գրառումները կարող են ազատորեն դիտվել Worldcat կայքում, բայց ոչ մեծ քանակությամբ ներբեռնվել (մինչև մենք <a %(worldcat_scrape)s>չքերծեցինք</a> դրանք) Reddit-ը անվճար է օգտագործման համար, բայց վերջերս խստացրել է հակաքերծման միջոցները՝ տվյալների քաղցած LLM ուսուցման հետևանքով (այդ մասին ավելի ուշ) Կան բազմաթիվ կազմակերպություններ, որոնք ունեն նման առաքելություններ և նման առաջնահերթություններ։ Իրոք, կան գրադարաններ, արխիվներ, լաբորատորիաներ, թանգարաններ և այլ հաստատություններ, որոնք զբաղվում են այս տեսակի պահպանմամբ։ Նրանցից շատերը լավ ֆինանսավորվում են՝ կառավարությունների, անհատների կամ կորպորացիաների կողմից։ Բայց նրանք ունեն մեկ հսկայական կույր կետ՝ իրավական համակարգը։ Այստեղ է, որ ստվերային գրադարանները ունեն յուրահատուկ դեր, և հենց այդ պատճառով է գոյություն ունի Աննայի Արխիվը։ Մենք կարող ենք անել այնպիսի բաներ, որոնք այլ հաստատություններին թույլատրված չեն։ Այժմ, դա հաճախ չէ, որ մենք կարող ենք արխիվացնել նյութեր, որոնք այլուր պահպանելը անօրինական է։ Ոչ, շատ վայրերում օրինական է ստեղծել արխիվ ցանկացած գրքերով, հոդվածներով, ամսագրերով և այլն։ Բայց այն, ինչ հաճախ պակասում է օրինական արխիվներին, դա <strong>կրկնօրինակման և երկարակեցության</strong> պակասն է։ Կան գրքեր, որոնց միայն մեկ օրինակ կա ինչ-որ ֆիզիկական գրադարանում։ Կան metadata գրառումներ, որոնք պահպանում է միայն մեկ կորպորացիա։ Կան թերթեր, որոնք պահպանվում են միայն միկրոֆիլմի վրա մեկ արխիվում։ Գրադարանները կարող են ֆինանսավորման կրճատումներ ստանալ, կորպորացիաները կարող են սնանկանալ, արխիվները կարող են ռմբակոծվել և այրվել։ Սա հիպոթետիկ չէ՝ սա տեղի է ունենում միշտ։ Աննայի Արխիվում մենք կարող ենք յուրահատուկ կերպով պահել բազմաթիվ օրինակներ, մասշտաբով։ Մենք կարող ենք հավաքել հոդվածներ, գրքեր, ամսագրեր և ավելին, և դրանք տարածել մեծ քանակությամբ։ Ներկայումս մենք դա անում ենք տոռենտների միջոցով, բայց ճշգրիտ տեխնոլոգիաները կարևոր չեն և կփոխվեն ժամանակի ընթացքում։ Կարևորն այն է, որ բազմաթիվ օրինակներ տարածվեն ամբողջ աշխարհում։ Այս ավելի քան 200 տարի առաջվա մեջբերումը դեռևս ճշմարիտ է հնչում. Հանրային տիրույթի մասին արագ նշում։ Քանի որ Աննայի Արխիվը յուրահատուկ կերպով կենտրոնանում է այնպիսի գործունեությունների վրա, որոնք շատ վայրերում անօրինական են, մենք չենք զբաղվում լայնորեն հասանելի հավաքածուներով, ինչպիսիք են հանրային տիրույթի գրքերը։ Օրինական կազմակերպությունները հաճախ արդեն լավ հոգ են տանում դրա մասին։ Այնուամենայնիվ, կան նկատառումներ, որոնք երբեմն ստիպում են մեզ աշխատել հանրային հասանելի հավաքածուների վրա. - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Պահեստավորման ծախսերը շարունակում են էքսպոնենցիալ նվազել 3. Տեղեկատվության խտության բարելավումներ Ներկայումս մենք գրքերը պահում ենք այն հում ձևաչափերով, որոնք մեզ են տրվում։ Իհարկե, դրանք սեղմված են, բայց հաճախ դրանք դեռ մեծ սկաներ կամ էջերի լուսանկարներ են։ Մինչ այժմ մեր հավաքածուի ընդհանուր չափը փոքրացնելու միակ տարբերակները եղել են ավելի ագրեսիվ սեղմումը կամ կրկնօրինակումը։ Սակայն, զգալի խնայողություններ ստանալու համար երկուսն էլ չափազանց կորուստային են մեր ճաշակի համար։ Լուսանկարների ծանր սեղմումը կարող է տեքստը գրեթե անընթեռնելի դարձնել։ Իսկ կրկնօրինակումը պահանջում է բարձր վստահություն, որ գրքերը ճիշտ նույնն են, ինչը հաճախ չափազանց անճշտ է, հատկապես եթե բովանդակությունը նույնն է, բայց սկաները կատարվել են տարբեր առիթներով։ Միշտ եղել է երրորդ տարբերակ, բայց դրա որակը այնքան վատ է եղել, որ մենք երբեք չենք դիտարկել այն՝ <strong>OCR, կամ օպտիկական նիշերի ճանաչում</strong>։ Սա լուսանկարները պարզ տեքստի վերածելու գործընթացն է՝ օգտագործելով արհեստական բանականություն՝ լուսանկարներում նիշերը հայտնաբերելու համար։ Այս գործիքները վաղուց գոյություն ունեն և բավականին լավ են, բայց «բավականին լավ»ը բավարար չէ պահպանման նպատակներով։ Այնուամենայնիվ, վերջերս բազմամոդալ խորը ուսուցման մոդելները չափազանց արագ առաջընթաց են գրանցել, թեև դեռ բարձր ծախսերով։ Մենք ակնկալում ենք, որ առաջիկա տարիներին թե՛ ճշգրտությունը, թե՛ ծախսերը կբարելավվեն զգալիորեն, մինչև այն կդառնա իրատեսական՝ կիրառելու մեր ամբողջ գրադարանին։ Երբ դա տեղի ունենա, մենք հավանաբար դեռ կպահպանենք բնօրինակ ֆայլերը, բայց բացի այդ, կարող ենք ունենալ մեր գրադարանի շատ ավելի փոքր տարբերակը, որը շատերը կցանկանան կրկնօրինակել։ Հետաքրքիրն այն է, որ հում տեքստը ինքնին ավելի լավ է սեղմվում և շատ ավելի հեշտ է կրկնօրինակվում, ինչը մեզ ավելի շատ խնայողություններ է տալիս։ Ընդհանուր առմամբ, անիրատեսական չէ ակնկալել առնվազն 5-10 անգամ ընդհանուր ֆայլի չափի կրճատում, գուցե նույնիսկ ավելի։ Նույնիսկ պահպանողական 5 անգամ կրճատմամբ, մենք 10 տարի անց կծախսենք <strong>1,000–3,000 դոլար, նույնիսկ եթե մեր գրադարանը եռապատկվի</strong>։ Գրելու պահին, <a %(diskprices)s>դիսկերի գները</a> մեկ ՏԲ-ի համար կազմում են մոտ $12 նոր դիսկերի համար, $8 օգտագործված դիսկերի համար և $4 ժապավենի համար։ Եթե մենք պահպանողական լինենք և նայենք միայն նոր դիսկերին, դա նշանակում է, որ մեկ պետաբայթ պահելը արժե մոտ $12,000։ Եթե ենթադրենք, որ մեր գրադարանը կեռապատկվի 900ՏԲ-ից մինչև 2.7ՊԲ, դա կնշանակի $32,400 մեր ամբողջ գրադարանը արտացոլելու համար։ Ավելացնելով էլեկտրաէներգիան, այլ սարքավորումների արժեքը և այլն, եկեք կլորացնենք մինչև $40,000։ Կամ ժապավենով ավելի շատ $15,000–$20,000։ Մեկ կողմից <strong>$15,000–$40,000 ամբողջ մարդկային գիտելիքների համար գողություն է</strong>։ Մյուս կողմից, դա մի փոքր թանկ է ակնկալել ամբողջական օրինակների մեծ քանակություն, հատկապես եթե մենք ցանկանում ենք, որ այդ մարդիկ շարունակեն սերմանել իրենց տոռենտները մյուսների օգտին։ Դա այսօր է։ Բայց առաջընթացը շարունակում է առաջ գնալ. Կոշտ սկավառակների գները մեկ ՏԲ-ի համար մոտավորապես կրճատվել են մեկ երրորդով վերջին 10 տարիների ընթացքում և հավանաբար կշարունակեն նվազել նման տեմպերով։ Ժապավենը կարծես թե նման ուղիով է գնում։ SSD գները ավելի արագ են նվազում և կարող են գերազանցել HDD գները տասնամյակի վերջում։ Եթե սա ճիշտ է, ապա 10 տարի անց մենք կարող ենք ընդամենը 5,000–13,000 դոլար ծախսել մեր ամբողջ հավաքածուն (1/3-րդը) կրկնօրինակելու համար, կամ նույնիսկ ավելի քիչ, եթե մեր չափսը չաճի։ Թեև դա դեռ շատ գումար է, այն շատերի համար հասանելի կլինի։ Եվ դա կարող է նույնիսկ ավելի լավ լինել՝ շնորհիվ հաջորդ կետի… Աննայի Արխիվում հաճախ մեզ հարցնում են, թե ինչպես կարող ենք պնդել, որ մեր հավաքածուները հավերժ կպահպանվեն, երբ ընդհանուր ծավալը արդեն մոտենում է 1 Պետաբայթին (1000 ՏԲ) և դեռ աճում է: Այս հոդվածում մենք կանդրադառնանք մեր փիլիսոփայությանը և կտեսնենք, թե ինչու է հաջորդ տասնամյակը կրիտիկական մեր առաքելության համար՝ պահպանելու մարդկության գիտելիքներն ու մշակույթը: Կրիտիկական պատուհան Եթե այս կանխատեսումները ճշգրիտ են, մենք <strong>պարզապես պետք է սպասենք մի քանի տարի</strong>, մինչև մեր ամբողջ հավաքածուն լայնորեն կրկնօրինակվի։ Այսպիսով, Թոմաս Ջեֆերսոնի խոսքերով, «դրված է պատահարի հասանելիությունից դուրս»։ Ցավոք, LLM-ների ի հայտ գալը և դրանց տվյալների պահանջկոտ ուսուցումը շատ հեղինակային իրավունքների տերերին պաշտպանական դիրք է դրել։ Ավելի շատ, քան նրանք արդեն կային։ Շատ կայքեր ավելի դժվարացնում են տվյալների քերումը և արխիվացումը, դատական գործեր են ընթանում, և այդ ընթացքում ֆիզիկական գրադարաններն ու արխիվները շարունակում են անտեսվել։ Մենք կարող ենք միայն ակնկալել, որ այս միտումները կշարունակեն վատթարանալ, և շատ աշխատանքներ կկորչեն, նախքան հանրային դաշտ մտնելը։ <strong>Մենք պահպանման հեղափոխության նախաշեմին ենք, բայց <q>կորցրածը չի կարող վերականգնվել։</q></strong> Մենք ունենք մոտ 5-10 տարվա կրիտիկական պատուհան, որի ընթացքում դեռ բավականին թանկ է ստվերային գրադարան գործարկել և բազմաթիվ կրկնօրինակներ ստեղծել աշխարհով մեկ, և որի ընթացքում հասանելիությունը դեռ ամբողջությամբ չի փակվել։ Եթե մենք կարողանանք անցնել այս պատուհանը, ապա իսկապես կպահպանենք մարդկության գիտելիքներն ու մշակույթը հավերժ։ Մենք չպետք է թույլ տանք, որ այս ժամանակը վատնվի։ Մենք չպետք է թույլ տանք, որ այս կրիտիկական պատուհանը փակվի մեզ վրա։ Եկեք գնանք։ Ստվերային գրադարանների կրիտիկական պատուհանը Ինչպե՞ս կարող ենք պնդել, որ մեր հավաքածուները հավերժ կպահպանվեն, երբ դրանք արդեն մոտենում են 1 PB-ի: Հավաքածու Հավաքածուի մասին որոշ լրացուցիչ տեղեկություններ։ <a %(duxiu)s>Duxiu</a> հսկայական տվյալների բազա է սկանավորված գրքերի, որը ստեղծվել է <a %(chaoxing)s>SuperStar Digital Library Group</a>-ի կողմից։ Դրանք հիմնականում ակադեմիական գրքեր են, սկանավորված՝ համալսարաններին և գրադարաններին թվային հասանելիություն ապահովելու համար։ Մեր անգլախոս լսարանի համար <a %(library_princeton)s>Princeton</a>-ը և <a %(guides_lib_uw)s>University of Washington</a>-ը ունեն լավ ակնարկներ։ Կա նաև հիանալի հոդված, որը տալիս է ավելի շատ նախապատմություն՝ <a %(doi)s>«Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine»</a> (փնտրեք Աննայի Արխիվում)։ Duxiu-ի գրքերը վաղուց են պիրատացվել չինական ինտերնետում։ Դրանք սովորաբար վաճառվում են մեկ դոլարից պակաս գնով վերավաճառողների կողմից։ Դրանք սովորաբար տարածվում են Google Drive-ի չինական համարժեքի միջոցով, որը հաճախ կոտրվում է՝ ավելի շատ պահեստային տարածք ապահովելու համար։ Որոշ տեխնիկական մանրամասներ կարող եք գտնել <a %(github_duty_machine)s>այստեղ</a> և <a %(github_821_github_io)s>այստեղ</a>։ Թեև գրքերը կիսահանրային կերպով տարածվել են, բավականին դժվար է դրանք մեծածավալ ստանալ։ Մենք դա բարձր էինք դրել մեր TODO-ցուցակում և մի քանի ամիս ամբողջական աշխատանք էինք հատկացրել դրա համար։ Սակայն վերջերս մի անհավանական, զարմանալի և տաղանդավոր կամավոր կապ հաստատեց մեզ հետ՝ ասելով, որ նրանք արդեն արել են այս ամբողջ աշխատանքը՝ մեծ ծախսերով։ Նրանք կիսվեցին ամբողջ հավաքածուով մեզ հետ՝ առանց որևէ բանի ակնկալիքի, բացի երկարաժամկետ պահպանման երաշխիքից։ Իրոք զարմանալի։ Նրանք համաձայնեցին այս կերպ օգնություն խնդրել՝ հավաքածուն OCR-ով մշակելու համար։ Հավաքածուն ունի 7,543,702 ֆայլ։ Սա ավելի շատ է, քան Library Genesis-ի ոչ գեղարվեստական (մոտ 5.3 միլիոն)։ Ընդհանուր ֆայլի չափը մոտ 359TB (326TiB) է իր ներկայիս ձևով։ Մենք բաց ենք այլ առաջարկների և գաղափարների համար։ Պարզապես կապ հաստատեք մեզ հետ։ Տեսեք Աննայի Արխիվը՝ մեր հավաքածուների, պահպանման ջանքերի և ինչպես կարող եք օգնել մասին ավելի շատ տեղեկությունների համար։ Շնորհակալություն! Օրինակ էջեր Ձեզ ապացուցելու համար, որ ունեք լավ գործընթաց, ահա որոշ օրինակ էջեր, որոնցով կարող եք սկսել՝ գերհաղորդիչների մասին գրքից։ Ձեր գործընթացը պետք է ճիշտ աշխատի մաթեմատիկայի, աղյուսակների, գծապատկերների, ծանոթագրությունների և այլն։ Ուղարկեք ձեր մշակված էջերը մեր էլ. հասցեին։ Եթե դրանք լավ տեսք ունենան, մենք ձեզ ավելի շատ կուղարկենք մասնավոր կերպով, և մենք ակնկալում ենք, որ դուք արագ կկարողանաք ձեր գործընթացը կիրառել դրանց վրա։ Երբ մենք գոհ լինենք, կարող ենք գործարք կնքել։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Չինական տարբերակ 中文版</a>, <a %(news_ycombinator)s>Քննարկեք Hacker News-ում</a> Սա կարճ բլոգային գրառում է։ Մենք փնտրում ենք ընկերություն կամ հաստատություն, որը կօգնի մեզ OCR և տեքստի արդյունահանման հարցում մեր ձեռք բերած հսկայական հավաքածուի համար՝ բացառիկ վաղաժամ մուտքի դիմաց։ Էմբարգոյի ժամկետի ավարտից հետո, իհարկե, մենք կհրապարակենք ամբողջ հավաքածուն։ Բարձրորակ ակադեմիական տեքստը չափազանց օգտակար է LLM-ների ուսուցման համար։ Թեև մեր հավաքածուն չինական է, այն պետք է օգտակար լինի նաև անգլերեն LLM-ների ուսուցման համար. մոդելները կարծես թե կոդավորում են գաղափարներ և գիտելիքներ՝ անկախ աղբյուրի լեզվից։ Դրա համար տեքստը պետք է արդյունահանվի սկաներից։ Ի՞նչ է ստանում Աննայի Արխիվը։ Գրքերի ամբողջական տեքստի որոնում իր օգտատերերի համար։ Քանի որ մեր նպատակները համընկնում են LLM մշակողների նպատակների հետ, մենք փնտրում ենք համագործակից։ Մենք պատրաստ ենք ձեզ տալ <strong>բացառիկ վաղաժամ մուտք այս հավաքածուին մեծածավալ 1 տարով</strong>, եթե կարողանաք ճիշտ OCR և տեքստի արդյունահանում անել։ Եթե պատրաստ եք կիսվել ձեր ամբողջ կոդով մեզ հետ, մենք պատրաստ կլինենք ավելի երկար էմբարգո կիրառել հավաքածուի վրա։ Բացառիկ հասանելիություն LLM ընկերությունների համար՝ աշխարհի ամենամեծ չինական ոչ գեղարվեստական գրքերի հավաքածուին <em><strong>Հակիրճ՝</strong> Աննայի Արխիվը ձեռք է բերել 7.5 միլիոն / 350TB չինական ոչ գեղարվեստական գրքերի եզակի հավաքածու՝ ավելի մեծ, քան Library Genesis-ը։ Մենք պատրաստ ենք LLM ընկերությանը տալ բացառիկ մուտք՝ բարձրորակ OCR և տեքստի արդյունահանման դիմաց։</em> Համակարգի ճարտարապետություն Եկեք ասենք, որ դուք գտել եք որոշ ընկերություններ, որոնք պատրաստ են հյուրընկալել ձեր կայքը առանց այն փակելու՝ եկեք դրանք անվանենք «ազատասեր մատակարարներ» 😄։ Դուք արագ կգտնեք, որ ամեն ինչ նրանց հետ հյուրընկալելը բավականին թանկ է, այնպես որ գուցե ցանկանաք գտնել որոշ «էժան մատակարարներ» և իրական հյուրընկալումը կատարել այնտեղ՝ միջնորդելով ազատասեր մատակարարների միջոցով։ Եթե դա ճիշտ անեք, էժան մատակարարները երբեք չեն իմանա, թե ինչ եք հյուրընկալում, և երբեք չեն ստանա որևէ բողոք։ Այս բոլոր մատակարարների հետ կա այնուամենայնիվ ձեզ փակելու ռիսկ, ուստի ձեզ նույնպես անհրաժեշտ է ավելորդություն։ Մենք դա պետք է ունենանք մեր կույտի բոլոր մակարդակներում։ Մի քիչ ազատասեր ընկերություն, որը հետաքրքիր դիրք է զբաղեցրել, Cloudflare-ն է։ Նրանք <a %(blog_cloudflare)s>պնդել են</a>, որ իրենք հյուրընկալող մատակարար չեն, այլ օգտակարություն, ինչպես ISP-ն։ Հետևաբար, նրանք չեն ենթարկվում DMCA կամ այլ հեռացման պահանջներին և փոխանցում են ցանկացած պահանջ ձեր իրական հյուրընկալող մատակարարին։ Նրանք նույնիսկ դատարան են գնացել՝ այս կառուցվածքը պաշտպանելու համար։ Հետևաբար, մենք կարող ենք դրանք օգտագործել որպես քեշավորման և պաշտպանության ևս մեկ շերտ։ Cloudflare-ը չի ընդունում անանուն վճարումներ, ուստի մենք կարող ենք օգտագործել միայն նրանց անվճար պլանը։ Սա նշանակում է, որ մենք չենք կարող օգտագործել նրանց բեռնաբաշխման կամ խափանումների վերականգնման հնարավորությունները։ Հետևաբար, մենք <a %(annas_archive_l255)s>իրականացրել ենք սա ինքներս</a> դոմենի մակարդակում։ Էջի բեռնման ժամանակ զննարկիչը կստուգի, արդյոք ընթացիկ դոմենը դեռ հասանելի է, և եթե ոչ, այն վերաշարադրում է բոլոր URL-ները դեպի այլ դոմեն։ Քանի որ Cloudflare-ը քեշավորում է բազմաթիվ էջեր, սա նշանակում է, որ օգտատերը կարող է վայրէջք կատարել մեր հիմնական դոմենում, նույնիսկ եթե միջնորդ սերվերը անջատված է, և ապա հաջորդ սեղմումով տեղափոխվել այլ դոմեն։ Մենք դեռ ունենք նաև նորմալ գործառնական խնդիրներ, որոնց պետք է զբաղվել, ինչպիսիք են սերվերի առողջության մոնիտորինգը, հետին և առջևի սխալների գրանցումը և այլն։ Մեր խափանումների վերականգնման ճարտարապետությունը թույլ է տալիս ավելի մեծ ամրություն այս առումով, օրինակ՝ մեկ դոմենում ամբողջովին այլ սերվերների հավաքածու գործարկելով։ Մենք նույնիսկ կարող ենք գործարկել կոդի և տվյալների հավաքածուների հին տարբերակները այս առանձին դոմենում, եթե հիմնական տարբերակում որևէ կրիտիկական սխալ աննկատ մնա։ Մենք կարող ենք նաև ապահովագրել Cloudflare-ի դեմ, եթե այն մեզ դեմ դուրս գա, հեռացնելով այն մեկ դոմենից, օրինակ՝ այս առանձին դոմենից։ Այս գաղափարների տարբեր համակցություններ հնարավոր են։ Եզրակացություն Դա հետաքրքիր փորձառություն է եղել՝ սովորել, թե ինչպես ստեղծել ամուր և դիմացկուն ստվերային գրադարանի որոնման համակարգ։ Կան շատ ավելի մանրամասներ, որոնք կկիսվենք հետագա գրառումներում, այնպես որ տեղեկացրեք, թե ինչի մասին կցանկանայիք ավելին իմանալ։ Ինչպես միշտ, մենք փնտրում ենք նվիրատվություններ այս աշխատանքի աջակցման համար, այնպես որ համոզվեք, որ ստուգեք Նվիրատվությունների էջը Աննայի Արխիվում։ Մենք նաև փնտրում ենք այլ տեսակի աջակցություն, ինչպիսիք են դրամաշնորհները, երկարաժամկետ հովանավորները, բարձր ռիսկային վճարային մատակարարները, գուցե նույնիսկ (համեստ!) գովազդները։ Եվ եթե ցանկանում եք ներդնել ձեր ժամանակն ու հմտությունները, մենք միշտ փնտրում ենք ծրագրավորողների, թարգմանիչների և այլն։ Շնորհակալություն ձեր հետաքրքրության և աջակցության համար։ Նորարարության նշաններ Եկեք սկսենք մեր տեխնոլոգիական կույտից։ Այն դիտավորյալ ձանձրալի է։ Մենք օգտագործում ենք Flask, MariaDB և ElasticSearch։ Դա բառացիորեն այդպես է։ Որոնումը հիմնականում լուծված խնդիր է, և մենք չենք նախատեսում այն վերագտնել։ Բացի այդ, մենք պետք է մեր <a %(mcfunley)s>նորարարության նշանները</a> ծախսենք այլ բանի վրա՝ չհեռացվելու համար իշխանությունների կողմից։ Ուրեմն որքան օրինական կամ անօրինական է Աննայի Արխիվը։ Սա հիմնականում կախված է իրավական իրավասությունից։ Շատ երկրներ հավատում են հեղինակային իրավունքի ինչ-որ ձևի, ինչը նշանակում է, որ մարդկանց կամ ընկերություններին տրվում է որոշակի տեսակի աշխատանքների բացառիկ մենաշնորհ որոշակի ժամանակահատվածի համար։ Աննայի Արխիվում մենք հավատում ենք, որ որոշակի օգուտներ կան, բայց ընդհանուր առմամբ հեղինակային իրավունքը բացասական է հասարակության համար՝ բայց դա այլ պատմություն է։ Այս բացառիկ մենաշնորհը որոշակի աշխատանքների վրա նշանակում է, որ այս մենաշնորհից դուրս գտնվող որևէ մեկի համար անօրինական է այդ աշխատանքները ուղղակիորեն տարածելը՝ ներառյալ մեզ։ Բայց Աննայի Արխիվը որոնման համակարգ է, որը ուղղակիորեն չի տարածում այդ աշխատանքները (առնվազն մեր բաց ցանցային կայքում), այնպես որ մենք պետք է լավ լինենք, այնպես չէ՞։ Ոչ այնքան։ Շատ իրավասություններում ոչ միայն անօրինական է հեղինակային իրավունքով պաշտպանված աշխատանքները տարածելը, այլև հղումներ տալը այն վայրերին, որոնք դա անում են։ Դրա դասական օրինակն է ԱՄՆ-ի DMCA օրենքը։ Սա սպեկտրի ամենախիստ վերջն է։ Սպեկտրի մյուս ծայրում տեսականորեն կարող են լինել երկրներ, որոնք ընդհանրապես չունեն հեղինակային իրավունքի օրենքներ, բայց դրանք իրականում գոյություն չունեն։ Գործնականում յուրաքանչյուր երկրում կա հեղինակային իրավունքի օրենքի ինչ-որ ձև։ Կիրառումը այլ պատմություն է։ Կան բազմաթիվ երկրներ, որոնց կառավարությունները չեն ցանկանում կիրառել հեղինակային իրավունքի օրենքը։ Կան նաև երկրներ, որոնք գտնվում են երկու ծայրահեղությունների միջև, որոնք արգելում են հեղինակային իրավունքով պաշտպանված աշխատանքների տարածումը, բայց չեն արգելում նման աշխատանքների հղումները։ Մեկ այլ նկատառում է ընկերության մակարդակում։ Եթե ընկերությունը գործում է այնպիսի իրավասության մեջ, որը չի հետաքրքրվում հեղինակային իրավունքով, բայց ընկերությունը ինքնին չի ցանկանում որևէ ռիսկի դիմել, ապա նրանք կարող են փակել ձեր կայքը, երբ որևէ մեկը բողոքի դրա մասին։ Վերջապես, մեծ նկատառում է վճարումները։ Քանի որ մենք պետք է մնանք անանուն, մենք չենք կարող օգտագործել ավանդական վճարման մեթոդներ։ Սա մեզ թողնում է կրիպտոարժույթների հետ, և միայն փոքր թվով ընկերություններ են աջակցում դրանց (կան վիրտուալ դեբետային քարտեր, որոնք վճարվում են կրիպտոյով, բայց դրանք հաճախ չեն ընդունվում)։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ես վարում եմ <a %(wikipedia_annas_archive)s>Աննայի Արխիվը</a>, աշխարհի ամենամեծ բաց կոդով ոչ առևտրային որոնման համակարգը ստվերային գրադարանների համար, ինչպիսիք են Sci-Hub-ը, Library Genesis-ը և Z-Library-ը։ Մեր նպատակն է գիտելիքն ու մշակույթը հասանելի դարձնել, և ի վերջո ստեղծել մարդկանց համայնք, ովքեր միասին արխիվացնում և պահպանում են <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>աշխարհի բոլոր գրքերը</a>։ Այս հոդվածում ես ցույց կտամ, թե ինչպես ենք մենք վարում այս կայքը և այն յուրահատուկ մարտահրավերները, որոնք առաջանում են իրավական վիճելի կարգավիճակ ունեցող կայք գործարկելու հետ, քանի որ չկա «AWS ստվերային բարեգործությունների համար»։ <em>Նաև դիտեք քույր հոդվածը <a %(blog_how_to_become_a_pirate_archivist)s>Ինչպես դառնալ ծովահեն արխիվագետ</a>։</em> Ինչպես վարել ստվերային գրադարան՝ Աննայի Արխիվի գործողությունները Չկա <q>AWS ստվերային բարեգործությունների համար,</q> ապա ինչպես ենք մենք վարում Աննայի Արխիվը։ Գործիքներ Դիմումի սերվեր՝ Flask, MariaDB, ElasticSearch, Docker։ Զարգացում՝ Gitlab, Weblate, Zulip։ Սերվերի կառավարում՝ Ansible, Checkmk, UFW։ Onion ստատիկ հոսթինգ՝ Tor, Nginx։ Միջնորդ սերվեր՝ Varnish։ Եկեք տեսնենք, թե ինչ գործիքներ ենք օգտագործում այս ամենը իրականացնելու համար։ Սա շատ է զարգանում, երբ բախվում ենք նոր խնդիրների և գտնում նոր լուծումներ։ Կան որոշ որոշումներ, որոնց շուրջ մենք բազմիցս քննարկել ենք։ Մեկը սերվերների միջև հաղորդակցությունն է․ մենք նախկինում օգտագործում էինք Wireguard-ը դրա համար, բայց պարզեցինք, որ այն երբեմն դադարում է փոխանցել որևէ տվյալ, կամ միայն փոխանցում է տվյալներ մեկ ուղղությամբ։ Սա տեղի ունեցավ մի քանի տարբեր Wireguard կարգավորումների հետ, որոնք մենք փորձեցինք, ինչպիսիք են <a %(github_costela_wesher)s>wesher</a>-ը և <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>-ը։ Մենք նաև փորձեցինք SSH-ի միջոցով պորտերի թունելավորում, օգտագործելով autossh և sshuttle, բայց հանդիպեցինք <a %(github_sshuttle)s>խնդիրների</a> (չնայած ինձ դեռ պարզ չէ, արդյոք autossh-ը տառապում է TCP-over-TCP խնդիրներից, թե ոչ — ինձ թվում է, որ դա մի քիչ անհարմար լուծում է, բայց գուցե իրականում նորմալ է)։ Փոխարենը, մենք վերադարձանք սերվերների միջև ուղղակի կապերին, թաքցնելով, որ սերվերը աշխատում է էժան մատակարարների վրա՝ օգտագործելով IP-ֆիլտրավորում UFW-ով։ Սա ունի այն թերությունը, որ Docker-ը լավ չի աշխատում UFW-ի հետ, եթե չօգտագործեք <code>network_mode: "host"</code>։ Այս ամենը մի քիչ ավելի սխալների ենթակա է, քանի որ դուք կբացահայտեք ձեր սերվերը ինտերնետին ընդամենը մի փոքր սխալ կարգավորմամբ։ Գուցե մենք պետք է վերադառնանք autossh-ին — հետադարձ կապը շատ ողջունելի կլինի այստեղ։ Մենք նաև բազմիցս քննարկել ենք Varnish-ի և Nginx-ի միջև։ Ներկայումս մեզ դուր է գալիս Varnish-ը, բայց այն ունի իր առանձնահատկությունները և կոպիտ եզրերը։ Նույնը վերաբերում է Checkmk-ին՝ մենք այն չենք սիրում, բայց այն աշխատում է այս պահին։ Weblate-ը նորմալ է, բայց ոչ հիանալի — ես երբեմն վախենում եմ, որ այն կկորցնի իմ տվյալները, երբ փորձում եմ համաժամացնել այն մեր git պահոցով։ Flask-ը ընդհանուր առմամբ լավ է, բայց ունի որոշ տարօրինակ առանձնահատկություններ, որոնք շատ ժամանակ են պահանջել կարգավորելու համար, ինչպիսիք են հարմարեցված դոմեյնների կարգավորումը կամ դրա SqlAlchemy ինտեգրման խնդիրները։ Մինչ այժմ մյուս գործիքները հիանալի են եղել՝ մենք չունենք լուրջ բողոքներ MariaDB-ի, ElasticSearch-ի, Gitlab-ի, Zulip-ի, Docker-ի և Tor-ի վերաբերյալ։ Բոլորն էլ ունեցել են որոշ խնդիրներ, բայց ոչ մի լուրջ կամ ժամանակատար։ Համայնք Առաջին մարտահրավերը կարող է զարմանալի լինել։ Դա ոչ թե տեխնիկական խնդիր է, ոչ էլ իրավական խնդիր։ Դա հոգեբանական խնդիր է՝ այս աշխատանքը ստվերում կատարելը կարող է աներևակայելի միայնակ լինել։ Կախված այն բանից, թե ինչ եք պլանավորում անել, և ձեր սպառնալիքի մոդելից, դուք կարող եք շատ զգույշ լինել։ Մեկ ծայրում մենք ունենք մարդիկ, ինչպիսիք են Ալեքսանդրա Էլբակյանը*, Sci-Hub-ի հիմնադիրը, ով շատ բաց է իր գործունեության մասին։ Բայց նա մեծ ռիսկի տակ է, եթե այս պահին այցելեր արևմտյան երկիր, և կարող էր տասնամյակներ բանտարկվել։ Դա ռիսկ է, որը դուք պատրաստ կլինե՞ք վերցնել։ Մենք գտնվում ենք սպեկտրի մյուս ծայրում՝ շատ զգույշ լինելով չթողնել որևէ հետք և ունենալով ուժեղ օպերացիոն անվտանգություն։ * Ինչպես նշվել է HN-ում "ynno"-ի կողմից, Ալեքսանդրան սկզբում չէր ցանկանում հայտնի լինել. "Նրա սերվերները կարգավորված էին PHP-ից մանրամասն սխալի հաղորդագրություններ արձակելու համար, ներառյալ սխալված աղբյուրի ֆայլի ամբողջական ուղին, որը գտնվում էր /home/<USER>" Այսպիսով, օգտագործեք պատահական օգտանուններ այն համակարգիչների վրա, որոնք օգտագործում եք այս գործի համար, եթե սխալ կարգավորեք ինչ-որ բան։ Այդ գաղտնիությունը, սակայն, ունի հոգեբանական արժեք։ Շատ մարդիկ սիրում են ճանաչվել իրենց կատարած աշխատանքի համար, բայց դուք չեք կարող որևէ վարկ ստանալ դրա համար իրական կյանքում։ Նույնիսկ պարզ բաները կարող են մարտահրավեր լինել, ինչպես օրինակ ընկերները հարցնում են, թե ինչով եք զբաղվել (որոշ ժամանակ անց "խառնվելով իմ NAS / homelab-ի հետ" հնանում է)։ Ահա թե ինչու է այդքան կարևոր գտնել որոշ համայնք։ Դուք կարող եք որոշակի օպերացիոն անվտանգություն զիջել՝ վստահելով շատ մոտ ընկերներին, որոնց գիտեք, որ կարող եք խորապես վստահել։ Նույնիսկ այդ դեպքում զգույշ եղեք ոչինչ չգրել, եթե նրանք ստիպված լինեն իրենց էլեկտրոնային նամակները հանձնել իշխանություններին, կամ եթե նրանց սարքերը այլ կերպ վնասվեն։ Ավելի լավ է գտնել որոշ համախոհ ծովահեններ։ Եթե ձեր մոտ ընկերները հետաքրքրված են միանալ ձեզ, հիանալի է։ Հակառակ դեպքում, դուք կարող եք գտնել ուրիշներին առցանց։ Ցավոք, սա դեռ նեղ համայնք է։ Մինչ այժմ մենք գտել ենք միայն մի քանի այլ անձանց, ովքեր ակտիվ են այս ոլորտում։ Լավ մեկնարկային վայրեր են թվում Library Genesis ֆորումները և r/DataHoarder-ը։ Archive Team-ը նույնպես ունի համախոհ անհատներ, թեև նրանք գործում են օրենքի շրջանակներում (նույնիսկ եթե օրենքի որոշ մոխրագույն տարածքներում)։ Ավանդական "warez" և ծովահենության տեսարանները նույնպես ունեն մարդիկ, ովքեր մտածում են նման ձևով։ Մենք բաց ենք համայնք ձևավորելու և գաղափարներ ուսումնասիրելու վերաբերյալ առաջարկների համար։ Ազատ զգացեք մեզ հաղորդագրություն ուղարկել Twitter-ում կամ Reddit-ում։ Հնարավոր է, որ մենք կարողանանք հյուրընկալել ինչ-որ ֆորում կամ զրույցի խումբ։ Մեկ մարտահրավեր այն է, որ սա հեշտությամբ կարող է գրաքննվել, երբ օգտագործում ենք ընդհանուր հարթակներ, ուստի մենք ստիպված կլինենք ինքներս հյուրընկալել այն։ Կա նաև փոխզիջում՝ այս քննարկումները լիովին հանրային դարձնելու (ավելի մեծ ներգրավվածություն) և այն մասնավոր դարձնելու միջև (չթողնելով հնարավոր "թիրախներին" իմանալ, որ մենք պատրաստվում ենք դրանք քերել)։ Մենք պետք է մտածենք այդ մասին։ Տեղեկացրեք մեզ, եթե հետաքրքրված եք դրանով։ Եզրակացություն Հուսով ենք, որ սա օգտակար կլինի նոր սկսվող ծովահեն արխիվիստների համար։ Մենք ուրախ ենք ողջունել ձեզ այս աշխարհում, այնպես որ մի հապաղեք կապվել։ Եկեք պահպանենք աշխարհի գիտելիքներն ու մշակույթը որքան հնարավոր է, և տարածենք այն հեռու ու լայն։ Նախագծեր 4. Տվյալների ընտրություն Հաճախ կարող եք օգտագործել metadata-ն՝ պարզելու համար տվյալների ողջամիտ ենթաբազմություն ներբեռնելու համար։ Նույնիսկ եթե ի վերջո ցանկանում եք ներբեռնել բոլոր տվյալները, կարող է օգտակար լինել առաջնահերթություն տալ ամենակարևոր տարրերին, եթե հայտնաբերվեք և պաշտպանությունները բարելավվեն, կամ որովհետև ձեզ անհրաժեշտ կլինի ավելի շատ սկավառակներ գնել, կամ պարզապես որովհետև ձեր կյանքում ինչ-որ այլ բան կգա, նախքան ամեն ինչ ներբեռնելը։ Օրինակ, հավաքածուն կարող է ունենալ նույն հիմնական ռեսուրսի (օրինակ՝ գրքի կամ ֆիլմի) բազմաթիվ հրատարակություններ, որտեղ մեկը նշված է որպես լավագույն որակ։ Այդ հրատարակությունները նախ պահպանելը շատ իմաստ կունենա։ Դուք կարող եք ի վերջո ցանկանալ պահպանել բոլոր հրատարակությունները, քանի որ որոշ դեպքերում metadata-ն կարող է սխալ պիտակավորված լինել, կամ կարող են լինել անհայտ փոխզիջումներ հրատարակությունների միջև (օրինակ՝ «լավագույն հրատարակությունը» կարող է լավագույնը լինել շատ առումներով, բայց վատը՝ այլ առումներով, օրինակ՝ ֆիլմը ունենալով ավելի բարձր լուծաչափ, բայց բացակայող ենթագրեր)։ Դուք կարող եք նաև որոնել ձեր metadata տվյալների բազայում՝ հետաքրքիր բաներ գտնելու համար։ Ո՞րն է ամենամեծ ֆայլը, որը հյուրընկալվում է, և ինչու՞ է այն այդքան մեծ։ Ո՞րն է ամենափոքր ֆայլը։ Կա՞ն հետաքրքիր կամ անսպասելի օրինաչափություններ որոշ կատեգորիաների, լեզուների և այլն։ Կա՞ն կրկնօրինակ կամ շատ նման վերնագրեր։ Կա՞ն օրինաչափություններ, թե երբ են տվյալները ավելացվել, օրինակ՝ մի օր, երբ միանգամից շատ ֆայլեր են ավելացվել։ Դուք հաճախ կարող եք շատ բան սովորել՝ տվյալների հավաքածուն տարբեր ձևերով դիտելով։ Մեր դեպքում, մենք զտեցինք Z-Library գրքերը Library Genesis-ի md5 հեշերի դեմ, այդպիսով խնայելով շատ ներբեռնման ժամանակ և սկավառակի տարածք։ Սա բավականին յուրահատուկ իրավիճակ է, սակայն։ Շատ դեպքերում չկան համապարփակ տվյալների բազաներ, թե որ ֆայլերն արդեն պատշաճ կերպով պահպանված են գործընկեր ծովահենների կողմից։ Սա ինքնին հսկայական հնարավորություն է ինչ-որ մեկի համար։ Կլիներ հիանալի ունենալ պարբերաբար թարմացվող ակնարկ այնպիսի բաների մասին, ինչպիսիք են երաժշտությունը և ֆիլմերը, որոնք արդեն լայնորեն սերմանված են torrent կայքերում, և, հետևաբար, ավելի ցածր առաջնահերթություն ունեն ծովահեն հայելիներում ներառելու համար։ 6. Բաշխում Դուք ունեք տվյալները, այդպիսով տալով ձեզ աշխարհի առաջին ծովահեն հայելին ձեր թիրախի (հավանաբար)։ Շատ առումներով ամենադժվար մասը ավարտված է, բայց ամենավտանգավոր մասը դեռ առջևում է։ Ի վերջո, մինչ այժմ դուք եղել եք գաղտնի; թռչելով ռադարի տակ։ Ամեն ինչ, ինչ դուք պետք է անեիք, լավ VPN օգտագործելն էր ամբողջ ընթացքում, ոչ մի անձնական տվյալ չլրացնել որևէ ձևում (հա), և գուցե հատուկ զննարկիչի նստաշրջան օգտագործել (կամ նույնիսկ այլ համակարգիչ)։ Այժմ դուք պետք է բաշխեք տվյալները։ Մեր դեպքում մենք նախ ուզում էինք գրքերը վերադարձնել Library Genesis-ին, բայց հետո արագ հայտնաբերեցինք դրա դժվարությունները (գեղարվեստական և ոչ գեղարվեստական տեսակավորում)։ Ուստի մենք որոշեցինք բաշխում Library Genesis ոճի torrent-ներով։ Եթե դուք հնարավորություն ունեք մասնակցելու գոյություն ունեցող նախագծին, ապա դա կարող է խնայել ձեզ շատ ժամանակ։ Այնուամենայնիվ, ներկայումս շատ լավ կազմակերպված ծովահեն հայելիներ չկան։ Այսպիսով, ասենք, որ դուք որոշում եք ինքներդ բաշխել torrent-ները։ Փորձեք այդ ֆայլերը փոքր պահել, որպեսզի դրանք հեշտությամբ հայելվեն այլ կայքերում։ Դուք ստիպված կլինեք ինքներդ սերմանել torrent-ները, մինչդեռ դեռևս անանուն մնալով։ Կարող եք օգտագործել VPN (պորտի փոխանցմամբ կամ առանց դրա), կամ վճարել տմբլված Bitcoin-ներով Seedbox-ի համար։ Եթե չգիտեք, թե ինչ են նշանակում այդ տերմիններից մի քանիսը, ապա ձեզ շատ կարդալու բան կունենաք, քանի որ կարևոր է, որ հասկանաք ռիսկերի փոխզիջումները այստեղ։ Դուք կարող եք հյուրընկալել torrent ֆայլերը հենց գոյություն ունեցող torrent կայքերում։ Մեր դեպքում, մենք ընտրեցինք իրականում հյուրընկալել կայք, քանի որ մենք նաև ցանկանում էինք մեր փիլիսոփայությունը հստակ կերպով տարածել։ Դուք կարող եք դա ինքներդ անել նման կերպ (մենք օգտագործում ենք Njalla մեր դոմենների և հոստինգի համար, վճարելով տմբլված Bitcoin-ներով), բայց նաև ազատ զգացեք կապվել մեզ հետ, որպեսզի մենք հյուրընկալենք ձեր torrent-ները։ Մենք ցանկանում ենք ժամանակի ընթացքում կառուցել ծովահեն հայելիների համապարփակ ինդեքս, եթե այս գաղափարը տարածվի։ Ինչ վերաբերում է VPN-ի ընտրությանը, ապա այս մասին արդեն շատ է գրվել, ուստի մենք պարզապես կկրկնենք ընդհանուր խորհուրդը՝ ընտրել ըստ հեղինակության։ Իրական դատարանում փորձարկված ոչ-մուտքագրող քաղաքականությունները, որոնք երկար ժամանակ պաշտպանել են գաղտնիությունը, ամենացածր ռիսկային տարբերակն են, մեր կարծիքով։ Նշենք, որ նույնիսկ երբ դուք ամեն ինչ ճիշտ եք անում, երբեք չեք կարող հասնել զրոյական ռիսկի։ Օրինակ, երբ սերմանում եք ձեր torrent-ները, բարձր մոտիվացված պետության դերակատարը հավանաբար կարող է դիտել VPN սերվերների մուտքային և ելքային տվյալների հոսքերը և պարզել, թե ով եք դուք։ Կամ դուք պարզապես կարող եք ինչ-որ կերպ սխալվել։ Մենք հավանաբար արդեն սխալվել ենք, և նորից կսխալվենք։ Բարեբախտաբար, պետությունները այնքան էլ չեն հետաքրքրվում ծովահենությամբ։ Յուրաքանչյուր նախագծի համար որոշում կայացնելիս պետք է որոշել՝ այն հրապարակել նույն ինքնությամբ, թե ոչ։ Եթե շարունակեք օգտագործել նույն անունը, ապա նախորդ նախագծերի օպերացիոն անվտանգության սխալները կարող են հետապնդել ձեզ։ Բայց տարբեր անուններով հրապարակելը նշանակում է, որ դուք չեք կառուցում երկարատև համբավ։ Մենք ընտրեցինք ուժեղ օպերացիոն անվտանգություն ունենալ սկզբից, որպեսզի կարողանանք շարունակել օգտագործել նույն ինքնությունը, բայց չենք վարանի հրապարակել այլ անունով, եթե սխալվենք կամ եթե հանգամանքները պահանջեն։ Տեղեկացնելը կարող է դժվար լինել։ Ինչպես ասացինք, սա դեռ նիշային համայնք է։ Սկզբում տեղադրեցինք Reddit-ում, բայց իսկապես հաջողություն ունեցանք Hacker News-ում։ Մեր առաջարկն է՝ տեղադրել մի քանի վայրերում և տեսնել, թե ինչ է տեղի ունենում։ Եվ կրկին, կապվեք մեզ հետ։ Մենք կցանկանայինք տարածել ծովահեն արխիվիզմի ջանքերի մասին։ 1. Դոմենի ընտրություն / փիլիսոփայություն Գիտելիքների և մշակութային ժառանգության պակաս չկա, որը պետք է պահպանել, ինչը կարող է ճնշող լինել։ Ահա թե ինչու հաճախ օգտակար է մի պահ կանգ առնել և մտածել, թե ինչ կարող է լինել ձեր ներդրումը։ Յուրաքանչյուր ոք ունի այս մասին մտածելու տարբեր ձև, բայց ահա մի քանի հարցեր, որոնք կարող եք ինքներդ ձեզ տալ. Մեր դեպքում մենք հատկապես կարևորում էինք գիտության երկարաժամկետ պահպանությունը։ Մենք գիտեինք Library Genesis-ի մասին և ինչպես այն ամբողջությամբ կրկնօրինակվել էր բազմաթիվ անգամներ՝ օգտագործելով torrents։ Մենք սիրեցինք այդ գաղափարը։ Հետո մի օր, մերից մեկը փորձեց գտնել գիտական դասագրքեր Library Genesis-ում, բայց չկարողացավ գտնել դրանք, ինչը կասկածի տակ դրեց, թե որքան ամբողջական էր այն իրականում։ Մենք այնուհետև որոնեցինք այդ դասագրքերը առցանց և գտանք դրանք այլ վայրերում, ինչը հիմք դրեց մեր նախագծի համար։ Նույնիսկ մինչ մենք գիտեինք Z-Library-ի մասին, մենք ունեինք գաղափար՝ չփորձել հավաքել բոլոր այդ գրքերը ձեռքով, այլ կենտրոնանալ գոյություն ունեցող հավաքածուների կրկնօրինակման վրա և դրանք վերադարձնել Library Genesis-ին։ Ի՞նչ հմտություններ ունեք, որոնք կարող եք օգտագործել ձեր օգտին։ Օրինակ, եթե դուք առցանց անվտանգության փորձագետ եք, կարող եք գտնել IP արգելափակումները հաղթահարելու ուղիներ՝ ապահով թիրախների համար։ Եթե դուք հիանալի եք համայնքներ կազմակերպելու մեջ, ապա գուցե կարողանաք մի խումբ մարդկանց հավաքել որևէ նպատակի շուրջ։ Օգտակար է իմանալ որոշ ծրագրավորում, եթե ոչ միայն այս գործընթացի ընթացքում լավ օպերացիոն անվտանգություն պահպանելու համար։ Ո՞րն է բարձր ազդեցության ոլորտ, որի վրա պետք է կենտրոնանալ։ Եթե դուք պատրաստվում եք ծովահեն արխիվացման վրա ծախսել X ժամ, ապա ինչպես կարող եք ստանալ ամենամեծ "շահույթը"։ Որո՞նք են այս մասին մտածելու ձեր յուրահատուկ ձևերը։ Դուք կարող եք ունենալ հետաքրքիր գաղափարներ կամ մոտեցումներ, որոնք ուրիշները կարող են բաց թողած լինել։ Որքա՞ն ժամանակ ունեք դրա համար։ Մեր խորհուրդը կլինի սկսել փոքրից և ավելի մեծ նախագծեր անել, երբ դուք սովորեք, բայց դա կարող է ամբողջովին կլանել։ Ինչու՞ եք հետաքրքրված այս հարցով։ Ի՞նչն է ձեզ ոգեշնչում։ Եթե մենք կարողանանք հավաքել մի խումբ մարդկանց, ովքեր արխիվացնում են այնպիսի բաներ, որոնք նրանք հատուկ կարևորում են, դա շատ բան կներառի։ Դուք միջին մարդուց շատ ավելին կիմանաք ձեր կրքի մասին, ինչպիսիք են՝ ինչ կարևոր տվյալներ են պահպանելու համար, որոնք են լավագույն հավաքածուները և առցանց համայնքները և այլն։ 3. Metadata-ի քերում Ավելացման/փոփոխման ամսաթիվ՝ որպեսզի կարողանաք ավելի ուշ վերադառնալ և ներբեռնել ֆայլեր, որոնք նախկինում չեք ներբեռնել (թեև հաճախ կարող եք նաև օգտագործել ID-ն կամ հեշը դրա համար)։ Հեշ (md5, sha1)՝ հաստատելու համար, որ դուք ճիշտ ներբեռնել եք ֆայլը։ ID՝ կարող է լինել որոշ ներքին ID, բայց ID-ներ, ինչպիսիք են ISBN կամ DOI-ն, նույնպես օգտակար են։ Ֆայլի անուն / գտնվելու վայր Նկարագրություն, կատեգորիա, պիտակներ, հեղինակներ, լեզու և այլն։ Չափ՝ հաշվարկելու համար, թե որքան սկավառակի տարածք է անհրաժեշտ։ Եկեք մի փոքր ավելի տեխնիկական լինենք այստեղ։ Կայքերից իրականում metadata քերելու համար մենք պահել ենք ամեն ինչ բավականին պարզ։ Մենք օգտագործում ենք Python սցենարներ, երբեմն curl, և MySQL տվյալների բազա՝ արդյունքները պահելու համար։ Մենք չենք օգտագործել որևէ բարդ քերումի ծրագրակազմ, որը կարող է քարտեզագրել բարդ կայքեր, քանի որ մինչ այժմ մեզ անհրաժեշտ էր քերել միայն մեկ կամ երկու տեսակի էջեր՝ պարզապես id-ներով անցնելով և վերլուծելով HTML-ը։ Եթե չկան հեշտությամբ թվարկվող էջեր, ապա ձեզ կարող է անհրաժեշտ լինել ճիշտ crawler, որը փորձում է գտնել բոլոր էջերը։ Նախքան ամբողջ կայքը քերելը սկսելը, փորձեք դա անել ձեռքով մի փոքր։ Ինքներդ անցեք մի քանի տասնյակ էջերով՝ հասկանալու համար, թե ինչպես է դա աշխատում։ Երբեմն դուք արդեն այս կերպ կբախվեք IP արգելափակումների կամ այլ հետաքրքիր վարքագծի։ Նույնը վերաբերում է տվյալների քերումին՝ նախքան այս նպատակին շատ խորանալը, համոզվեք, որ կարող եք իրականում արդյունավետորեն ներբեռնել դրա տվյալները։ Սահմանափակումները շրջանցելու համար կան մի քանի բաներ, որոնք կարող եք փորձել։ Կա՞ն այլ IP հասցեներ կամ սերվերներ, որոնք հյուրընկալում են նույն տվյալները, բայց չունեն նույն սահմանափակումները։ Կա՞ն API վերջնակետեր, որոնք չունեն սահմանափակումներ, մինչդեռ մյուսները ունեն։ Ի՞նչ արագությամբ ներբեռնելու դեպքում ձեր IP-ն արգելափակվում է, և որքան ժամանակով։ Կամ դուք չեք արգելափակվում, այլ դանդաղեցվում եք։ Ի՞նչ կլինի, եթե ստեղծեք օգտատիրոջ հաշիվ, ինչպե՞ս են փոխվում բաները։ Կարո՞ղ եք օգտագործել HTTP/2՝ կապերը բաց պահելու համար, և արդյո՞ք դա մեծացնում է էջերի պահանջման արագությունը։ Կա՞ն էջեր, որոնք միանգամից ցուցադրում են բազմաթիվ ֆայլեր, և արդյո՞ք այնտեղ ցուցադրված տեղեկատվությունը բավարար է։ Բաներ, որոնք հավանաբար ցանկանում եք պահպանել, ներառում են՝ Սովորաբար մենք դա անում ենք երկու փուլով։ Նախ ներբեռնում ենք հում HTML ֆայլերը, սովորաբար անմիջապես MySQL-ի մեջ (որպեսզի խուսափենք փոքր ֆայլերի մեծ քանակից, որի մասին ավելի մանրամասն կխոսենք ներքևում)։ Հետո, առանձին քայլով, անցնում ենք այդ HTML ֆայլերով և դրանք վերլուծում իրական MySQL աղյուսակների մեջ։ Այս կերպ, եթե ձեր վերլուծման կոդում սխալ հայտնաբերեք, ստիպված չեք լինի ամեն ինչ նորից ներբեռնել, քանի որ կարող եք պարզապես վերամշակել HTML ֆայլերը նոր կոդով։ Հաճախ ավելի հեշտ է նաև զուգահեռացնել մշակման քայլը, այսպիսով խնայելով որոշ ժամանակ (և կարող եք մշակման կոդը գրել, մինչ քերծումը կատարվում է, փոխարենը երկու քայլը միաժամանակ գրելու)։ Վերջապես, նշեք, որ որոշ թիրախների համար metadata քերծումը ամեն ինչ է։ Կան մեծ metadata հավաքածուներ, որոնք պատշաճ կերպով չեն պահպանվում։ Վերնագիր Դոմենի ընտրություն / փիլիսոփայություն. Որտե՞ղ եք մոտավորապես ցանկանում կենտրոնանալ և ինչու՞։ Որո՞նք են ձեր յուրահատուկ կրքերը, հմտությունները և հանգամանքները, որոնք կարող եք օգտագործել ձեր օգտին։ Թիրախի ընտրություն. Ո՞ր կոնկրետ հավաքածուն եք հայելու։ Մետադատայի քերում. Ֆայլերի մասին տեղեկատվության կատալոգավորում՝ առանց իրականում (հաճախ շատ ավելի մեծ) ֆայլերը ներբեռնելու։ Տվյալների ընտրություն. Հիմնվելով մետադատայի վրա՝ նեղացնելով, թե որ տվյալներն են առավել համապատասխան արխիվացման համար հենց հիմա։ Կարող է լինել ամեն ինչ, բայց հաճախ կա ողջամիտ միջոց՝ տարածք և թողունակություն խնայելու համար։ Տվյալների քերում. Իրականում տվյալների ստացումը։ Բաշխում. Փաթեթավորել այն torrents-ով, հայտարարելով այն ինչ-որ տեղ, մարդկանց ներգրավելով տարածելու համար։ 5. Տվյալների քերծում Այժմ դուք պատրաստ եք իրականում ներբեռնել տվյալները մեծ քանակությամբ։ Ինչպես նշվեց նախկինում, այս պահին դուք արդեն պետք է ձեռքով ներբեռնած լինեք մի շարք ֆայլեր, որպեսզի ավելի լավ հասկանաք թիրախի վարքագիծը և սահմանափակումները։ Այնուամենայնիվ, դեռևս կլինեն անակնկալներ, երբ դուք իրականում սկսեք միանգամից շատ ֆայլեր ներբեռնել։ Մեր խորհուրդն այստեղ հիմնականում այն է, որ պահեք այն պարզ։ Սկսեք պարզապես ներբեռնելով մի շարք ֆայլեր։ Կարող եք օգտագործել Python, և հետո ընդլայնել մի քանի թելերի։ Բայց երբեմն նույնիսկ ավելի պարզ է՝ Bash ֆայլեր գեներացնել անմիջապես տվյալների բազայից, և հետո դրանք մի քանի տերմինալ պատուհաններում գործարկել՝ մասշտաբավորելու համար։ Արժե այստեղ նշել արագ տեխնիկական հնարք՝ օգտագործելով OUTFILE MySQL-ում, որը կարող եք գրել ցանկացած տեղ, եթե անջատեք "secure_file_priv" mysqld.cnf-ում (և համոզվեք, որ նաև անջատեք/վերացնեք AppArmor-ը, եթե Linux-ում եք)։ Մենք տվյալները պահում ենք պարզ կոշտ սկավառակների վրա։ Սկսեք այն ամենից, ինչ ունեք, և դանդաղ ընդլայնեք։ Հարյուրավոր ՏԲ տվյալներ պահելու մասին մտածելը կարող է ճնշող լինել։ Եթե դա այն իրավիճակն է, որին դուք բախվում եք, պարզապես լավ ենթաբազմություն դրեք առաջին հերթին, և ձեր հայտարարության մեջ խնդրեք օգնություն մնացածը պահելու համար։ Եթե դուք իսկապես ցանկանում եք ավելի շատ կոշտ սկավառակներ ձեռք բերել, ապա r/DataHoarder-ը ունի որոշ լավ ռեսուրսներ՝ լավ գործարքներ ստանալու համար։ Փորձեք շատ չանհանգստանալ շքեղ ֆայլային համակարգերի մասին։ Հեշտ է ընկնել ZFS-ի նման բաներ կարգավորելու ճագարների անցքի մեջ։ Այնուամենայնիվ, պետք է տեղյակ լինել մեկ տեխնիկական մանրամասնության մասին, այն է, որ շատ ֆայլային համակարգեր լավ չեն աշխատում շատ ֆայլերի հետ։ Մենք պարզել ենք, որ պարզ լուծումը մի քանի գրացուցակներ ստեղծելն է, օրինակ՝ տարբեր ID տիրույթների կամ հեշ նախածանցների համար։ Տվյալները ներբեռնելուց հետո համոզվեք, որ ստուգեք ֆայլերի ամբողջականությունը՝ օգտագործելով metadata-ի հեշերը, եթե առկա են։ 2. Նպատակի ընտրություն Հասանելի՝ չի օգտագործում բազմաթիվ պաշտպանական շերտեր, որոնք խանգարում են ձեզ քերել նրանց metadata-ն և տվյալները։ Հատուկ պատկերացում՝ դուք ունեք որոշակի հատուկ տեղեկատվություն այս նպատակի մասին, օրինակ՝ դուք ինչ-որ կերպ ունեք հատուկ մուտք այս հավաքածուին, կամ դուք պարզել եք, թե ինչպես հաղթահարել նրանց պաշտպանությունը։ Սա պարտադիր չէ (մեր առաջիկա նախագիծը ոչ մի հատուկ բան չի անում), բայց հաստատ օգնում է։ Մեծ Այսպիսով, մենք ունենք մեր ուսումնասիրության ոլորտը, հիմա ո՞ր կոնկրետ հավաքածուն ենք կրկնօրինակում։ Կան մի քանի բաներ, որոնք լավ նպատակ են դարձնում. Երբ մենք գտանք մեր գիտական դասագրքերը Library Genesis-ից բացի այլ կայքերում, մենք փորձեցինք պարզել, թե ինչպես են դրանք հայտնվել ինտերնետում։ Այնուհետև մենք գտանք Z-Library-ը և հասկացանք, որ թեև գրքերի մեծ մասը առաջին անգամ այնտեղ չի հայտնվում, բայց ի վերջո հայտնվում են։ Մենք իմացանք դրա կապը Library Genesis-ի հետ և (ֆինանսական) խթանման կառուցվածքը և գերազանց օգտատիրոջ միջերեսը, որոնք երկուսն էլ այն դարձնում էին շատ ավելի ամբողջական հավաքածու։ Մենք այնուհետև կատարեցինք նախնական metadata և տվյալների քերում և հասկացանք, որ կարող ենք շրջանցել նրանց IP ներբեռնման սահմանափակումները՝ օգտագործելով մեր անդամներից մեկի հատուկ մուտքը բազմաթիվ proxy սերվերների։ Երբ ուսումնասիրում եք տարբեր նպատակներ, արդեն կարևոր է թաքցնել ձեր հետքերը՝ օգտագործելով VPN-ներ և ժամանակավոր էլ. հասցեներ, որոնց մասին ավելի ուշ կխոսենք։ Ունիկալ՝ արդեն լավ ծածկված չէ այլ նախագծերով։ Երբ մենք իրականացնում ենք նախագիծ, այն ունի մի քանի փուլեր. Սրանք ամբողջովին անկախ փուլեր չեն, և հաճախ ավելի ուշ փուլի պատկերացումները ձեզ հետ են ուղարկում ավելի վաղ փուլ։ Օրինակ, մետադատայի քերումի ընթացքում կարող եք հասկանալ, որ ձեր ընտրած թիրախն ունի պաշտպանական մեխանիզմներ, որոնք գերազանցում են ձեր հմտությունների մակարդակը (օրինակ՝ IP արգելափակումներ), ուստի դուք վերադառնում եք և գտնում այլ թիրախ։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>) Ամբողջ գրքեր կարելի է գրել թվային պահպանման <em>ինչու</em>-ի մասին ընդհանրապես, և պիրատ արխիվիզմի մասին մասնավորապես, բայց եկեք տանք արագ ներածություն նրանց համար, ովքեր շատ ծանոթ չեն։ Աշխարհը ավելի շատ գիտելիքներ և մշակույթ է արտադրում, քան երբևէ, բայց նաև ավելի շատ է կորցնում, քան երբևէ։ Մարդկությունը հիմնականում վստահում է կորպորացիաներին, ինչպիսիք են ակադեմիական հրատարակիչները, հոսքային ծառայությունները և սոցիալական մեդիա ընկերությունները այս ժառանգության հետ, և նրանք հաճախ չեն ապացուցել, որ լավ պահապաններ են։ Դիտեք Digital Amnesia վավերագրական ֆիլմը կամ իրականում ցանկացած ելույթ Ջեյսոն Սքոթի կողմից։ Կան որոշ հաստատություններ, որոնք լավ աշխատանք են կատարում հնարավորինս շատ արխիվացնելու համար, բայց նրանք կապված են օրենքով։ Որպես պիրատներ, մենք ունենք եզակի հնարավորություն արխիվացնելու հավաքածուներ, որոնք նրանք չեն կարող դիպչել, հեղինակային իրավունքի պաշտպանության կամ այլ սահմանափակումների պատճառով։ Մենք կարող ենք նաև հայելային դարձնել հավաքածուները բազմաթիվ անգամներ, ամբողջ աշխարհում, այդպիսով մեծացնելով պատշաճ պահպանման հնարավորությունները։ Ներկայումս մենք չենք մտնի մտավոր սեփականության առավելությունների և թերությունների, օրենքի խախտման բարոյականության, գրաքննության մասին մտորումների կամ գիտելիքների և մշակույթի հասանելիության հարցերի քննարկման մեջ։ Այս ամենը մի կողմ թողնելով, եկեք խորանանք <em>ինչպես</em>-ի մեջ։ Մենք կկիսվենք, թե ինչպես մեր թիմը դարձավ պիրատ արխիվիստներ, և այն դասերը, որոնք մենք սովորեցինք ճանապարհին։ Շատ մարտահրավերներ կան, երբ սկսում եք այս ճանապարհորդությունը, և հուսով ենք, որ մենք կարող ենք օգնել ձեզ դրանցից մի քանիսով։ Ինչպես դառնալ պիրատ արխիվիստ Առաջին մարտահրավերը կարող է զարմանալի լինել։ Դա ոչ թե տեխնիկական խնդիր է, ոչ էլ իրավական խնդիր։ Դա հոգեբանական խնդիր է։ Մինչ մենք կխորանանք, երկու թարմացում Պիրատական գրադարանի հայելու վերաբերյալ (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի արխիվ</a>)։ Մենք ստացել ենք չափազանց առատաձեռն նվիրատվություններ։ Առաջինը $10k էր անանուն անհատից, ով նաև աջակցել է "bookwarrior"-ին, Library Genesis-ի սկզբնական հիմնադրին։ Հատուկ շնորհակալություն bookwarrior-ին այս նվիրատվությունը կազմակերպելու համար։ Երկրորդը ևս $10k էր անանուն նվիրատուից, ով կապ հաստատեց մեր վերջին թողարկումից հետո և ոգեշնչվեց օգնելու։ Մենք նաև ունեցել ենք մի շարք փոքր նվիրատվություններ։ Շատ շնորհակալություն ձեր առատաձեռն աջակցության համար։ Մենք ունենք մի քանի հետաքրքիր նոր նախագծեր, որոնք այս աջակցությունը կօգտագործեն, այնպես որ մնացեք մեզ հետ։ Մենք ունեցել ենք որոշ տեխնիկական դժվարություններ մեր երկրորդ թողարկման չափի հետ, բայց մեր տոռենտները այժմ հասանելի են և սերմանում են։ Մենք նաև ստացել ենք առատաձեռն առաջարկ անանուն անհատից՝ սերմանելու մեր հավաքածուն իրենց շատ բարձր արագությամբ սերվերների վրա, այնպես որ մենք հատուկ ներբեռնում ենք նրանց մեքենաներին, որից հետո հավաքածուն ներբեռնող բոլոր մյուսները պետք է զգան արագության մեծ բարելավում։ Բլոգի գրառումներ Բարև, ես Աննան եմ։ Ես ստեղծել եմ <a %(wikipedia_annas_archive)s>Աննայի Արխիվը</a>, աշխարհի ամենամեծ ստվերային գրադարանը։ Սա իմ անձնական բլոգն է, որտեղ ես և իմ թիմակիցները գրում ենք ծովահենության, թվային պահպանման և այլնի մասին։ Կապվեք ինձ հետ <a %(reddit)s>Reddit</a>-ում։ Նշենք, որ այս կայքը պարզապես բլոգ է։ Այստեղ մենք հյուրընկալում ենք միայն մեր սեփական խոսքերը։ Այստեղ չեն հյուրընկալվում կամ կապված որևէ տոռենտներ կամ այլ հեղինակային իրավունքով պաշտպանված ֆայլեր։ <strong>Գրադարան</strong> - Ինչպես շատ գրադարաններ, մենք հիմնականում կենտրոնանում ենք գրավոր նյութերի վրա, ինչպիսիք են գրքերը։ Հնարավոր է, որ ապագայում ընդլայնվենք այլ տեսակի մեդիաների։ <strong>Հայելի</strong> - Մենք խիստ հայելի ենք գոյություն ունեցող գրադարանների։ Մենք կենտրոնանում ենք պահպանման վրա, ոչ թե գրքերը հեշտությամբ որոնելի և ներբեռնելի դարձնելու (մուտք) կամ նոր գրքեր ներդնող մարդկանց մեծ համայնք ստեղծելու (աղբյուրավորում) վրա։ <strong>Ծովահեն</strong> - Մենք դիտավորյալ խախտում ենք հեղինակային իրավունքի օրենքը շատ երկրներում։ Սա մեզ թույլ է տալիս անել այն, ինչ իրավական սուբյեկտները չեն կարող անել՝ ապահովել, որ գրքերը հայելավորվեն հեռու և լայն։ <em>Մենք չենք հղում ֆայլերը այս բլոգից։ Խնդրում ենք գտնել այն ինքներդ։</em> - Աննա և թիմը (<a %(reddit)s>Reddit</a>) Այս նախագիծը (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի Արխիվ</a>) նպատակ ունի նպաստել մարդկային գիտելիքների պահպանմանը և ազատագրմանը։ Մենք կատարում ենք մեր փոքր և համեստ ներդրումը, մեր նախորդների մեծերի հետքերով։ Նախագծի ուշադրության կենտրոնում է դրա անունը. Առաջին գրադարանը, որը մենք հայելավորել ենք, Z-Library-ն է։ Սա հայտնի (և անօրինական) գրադարան է։ Նրանք վերցրել են Library Genesis-ի հավաքածուն և այն հեշտությամբ որոնելի դարձրել։ Բացի այդ, նրանք շատ արդյունավետ են դարձել նոր գրքերի ներդրումներ ստանալու հարցում՝ խրախուսելով ներդրող օգտատերերին տարբեր արտոնություններով։ Ներկայումս նրանք չեն ներդնում այս նոր գրքերը Library Genesis-ին։ Եվ ի տարբերություն Library Genesis-ի, նրանք իրենց հավաքածուն հեշտությամբ հայելավորելի չեն դարձնում, ինչը խանգարում է լայն պահպանմանը։ Սա կարևոր է նրանց բիզնես մոդելի համար, քանի որ նրանք գումար են գանձում իրենց հավաքածուին մեծ քանակությամբ (ավելի քան 10 գիրք օրական) մուտք գործելու համար։ Մենք բարոյական դատողություններ չենք անում անօրինական գրքերի հավաքածուին մեծ քանակությամբ հասանելիության համար գումար գանձելու վերաբերյալ։ Անկասկած է, որ Z-Գրադարանը հաջողակ է եղել գիտելիքների հասանելիությունը ընդլայնելու և ավելի շատ գրքեր ձեռք բերելու հարցում։ Մենք պարզապես այստեղ ենք մեր մասը կատարելու համար՝ ապահովելով այս մասնավոր հավաքածուի երկարաժամկետ պահպանումը։ Մենք կցանկանայինք հրավիրել ձեզ օգնելու պահպանել և ազատագրել մարդկային գիտելիքները՝ ներբեռնելով և սերմանելով մեր տոռենտները։ Տվյալների կազմակերպման մասին ավելի շատ տեղեկությունների համար տեսեք նախագծի էջը։ Մենք նաև շատ կցանկանայինք հրավիրել ձեզ ներդնել ձեր գաղափարները, թե որ հավաքածուները հայելավորել հաջորդը և ինչպես դա անել։ Միասին մենք կարող ենք շատ բան հասնել։ Սա միայն փոքր ներդրում է անհամար այլոց մեջ։ Շնորհակալություն, այն ամենի համար, ինչ անում եք։ Ներկայացնում ենք Ծովահեն Գրադարանի Հայելին՝ Պահպանելով 7TB գրքեր (որոնք չկան Libgen-ում) 10% o%-ը մարդկության գրավոր ժառանգության հավերժ պահպանված <strong>Google։</strong> Ի վերջո, նրանք այս հետազոտությունը կատարեցին Google Books-ի համար։ Այնուամենայնիվ, նրանց metadata-ն հասանելի չէ մեծածավալ և բավականին դժվար է քերել։ <strong>Տարբեր անհատական գրադարանային համակարգեր և արխիվներ։</strong> Կան գրադարաններ և արխիվներ, որոնք չեն ինդեքսավորվել և համախմբվել վերոնշյալների կողմից, հաճախ այն պատճառով, որ դրանք թերֆինանսավորված են, կամ այլ պատճառներով չեն ցանկանում կիսվել իրենց տվյալներով Open Library-ի, OCLC-ի, Google-ի և այլն։ Նրանցից շատերը ունեն թվային գրառումներ, որոնք հասանելի են ինտերնետի միջոցով, և դրանք հաճախ այնքան էլ լավ պաշտպանված չեն, ուստի եթե ցանկանում եք օգնել և զվարճանալ տարօրինակ գրադարանային համակարգերի մասին սովորելով, դրանք հիանալի մեկնարկային կետեր են։ <strong>ISBNdb։</strong> Սա այս բլոգային գրառման թեման է։ ISBNdb-ն քերում է տարբեր կայքեր գրքերի metadata-ի համար, մասնավորապես գնագոյացման տվյալների համար, որոնք նրանք այնուհետև վաճառում են գրքերի վաճառողներին, որպեսզի նրանք կարողանան իրենց գրքերը գնել շուկայի մնացած մասի հետ համահունչ։ Քանի որ ISBN-ները բավականին համընդհանուր են այսօր, նրանք փաստացի ստեղծել են «վեբ էջ յուրաքանչյուր գրքի համար»։ <strong>Open Library։</strong> Ինչպես նշվեց նախկինում, սա նրանց ամբողջ առաքելությունն է։ Նրանք հավաքել են մեծ քանակությամբ գրադարանային տվյալներ համագործակցող գրադարաններից և ազգային արխիվներից և շարունակում են դա անել։ Նրանք նաև ունեն կամավոր գրադարանավարներ և տեխնիկական թիմ, որոնք փորձում են հեռացնել կրկնօրինակները և պիտակավորել դրանք ամենատարբեր metadata-ներով։ Ամենալավն այն է, որ նրանց dataset-ը ամբողջովին բաց է։ Դուք կարող եք պարզապես <a %(openlibrary)s>ներբեռնել այն</a>։ <strong>WorldCat։</strong> Սա ոչ առևտրային OCLC-ի կողմից ղեկավարվող կայք է, որը վաճառում է գրադարանային կառավարման համակարգեր։ Նրանք հավաքում են գրքերի metadata բազմաթիվ գրադարաններից և այն հասանելի են դարձնում WorldCat կայքի միջոցով։ Այնուամենայնիվ, նրանք նաև գումար են վաստակում այս տվյալները վաճառելով, ուստի դրանք հասանելի չեն մեծածավալ ներբեռնման համար։ Նրանք ունեն որոշ ավելի սահմանափակ մեծածավալ datasets, որոնք հասանելի են ներբեռնման համար՝ որոշակի գրադարանների հետ համագործակցությամբ։ 1. Որոշ ողջամիտ սահմանման համար "հավերժ". ;) 2. Իհարկե, մարդկության գրավոր ժառանգությունը շատ ավելին է, քան գրքերը, հատկապես մեր օրերում: Այս գրառման և մեր վերջին թողարկումների համար մենք կենտրոնանում ենք գրքերի վրա, բայց մեր հետաքրքրությունները ավելի հեռուն են ձգվում: 3. Աարոն Սվարցի մասին կարելի է շատ ավելին ասել, բայց մենք պարզապես ուզում էինք նրան կարճ հիշատակել, քանի որ նա կարևոր դեր է խաղում այս պատմության մեջ: Ժամանակի ընթացքում ավելի շատ մարդիկ կարող են առաջին անգամ հանդիպել նրա անունին և հետագայում ինքնուրույն ուսումնասիրել: <strong>Ֆիզիկական օրինակներ։</strong> Ակնհայտ է, որ սա շատ օգտակար չէ, քանի որ դրանք պարզապես նույն նյութի կրկնօրինակներ են։ Կլիներ հիանալի, եթե մենք կարողանայինք պահպանել բոլոր նշումները, որոնք մարդիկ անում են գրքերում, ինչպես Ֆերմայի հայտնի «մարգինալ գրառումները»։ Բայց ցավոք, դա կմնա արխիվիստի երազանքը։ <strong>«Հրատարակություններ»։</strong> Այստեղ դուք հաշվում եք գրքի յուրաքանչյուր եզակի տարբերակը։ Եթե դրա մասին որևէ բան տարբեր է, ինչպես տարբեր կազմ կամ տարբեր նախաբան, դա համարվում է տարբեր հրատարակություն։ <strong>Ֆայլեր։</strong> Երբ աշխատում եք ստվերային գրադարանների հետ, ինչպիսիք են Library Genesis-ը, Sci-Hub-ը կամ Z-Library-ը, կա լրացուցիչ նկատառում։ Կարող են լինել նույն հրատարակության բազմաթիվ սկաներ։ Եվ մարդիկ կարող են ավելի լավ տարբերակներ ստեղծել գոյություն ունեցող ֆայլերից՝ տեքստը սկանավորելով OCR-ի միջոցով կամ ուղղելով անկյան տակ սկանավորված էջերը։ Մենք ցանկանում ենք հաշվել այս ֆայլերը որպես մեկ հրատարակություն, ինչը կպահանջի լավ metadata կամ կրկնօրինակների հեռացում՝ փաստաթղթերի նմանության չափումների միջոցով։ <strong>«Աշխատանքներ»։</strong> Օրինակ՝ «Հարի Փոթերը և Գաղտնիքների սենյակը» որպես տրամաբանական հասկացություն, ներառելով դրա բոլոր տարբերակները, ինչպես տարբեր թարգմանություններ և վերատպումներ։ Սա մի տեսակ օգտակար սահմանում է, բայց դժվար է որոշել, թե ինչն է համարվում։ Օրինակ, մենք հավանաբար ցանկանում ենք պահպանել տարբեր թարգմանությունները, թեև վերատպումները միայն փոքր տարբերություններով կարող են այնքան էլ կարևոր չլինել։ - Աննա և թիմը (<a %(reddit)s>Reddit</a>) Ծովահեն Գրադարանի Հայելիով (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի Արխիվ</a>), մեր նպատակն է վերցնել աշխարհի բոլոր գրքերը և պահպանել դրանք հավերժ։<sup>1</sup> Մեր Z-Library տոռենտների և բնօրինակ Library Genesis տոռենտների միջև մենք ունենք 11,783,153 ֆայլ։ Բայց դա իրականում քանի՞սն է։ Եթե մենք ճիշտ կերպով դեդուպլիկացնենք այդ ֆայլերը, աշխարհի բոլոր գրքերի քանի՞ տոկոսն ենք մենք պահպանել։ Մենք իսկապես կցանկանայինք ունենալ նման մի բան. Սկսենք որոշ կոպիտ թվերից. Ե՛վ Z-Library/Libgen-ում, և՛ Open Library-ում կան շատ ավելի գրքեր, քան եզակի ISBN-ներ։ Արդյո՞ք դա նշանակում է, որ այդ գրքերից շատերը չունեն ISBN-ներ, թե՞ պարզապես ISBN metadata-ն բացակայում է։ Մենք հավանաբար կարող ենք պատասխանել այս հարցին՝ օգտագործելով այլ հատկանիշների (վերնագիր, հեղինակ, հրատարակիչ և այլն) հիման վրա ավտոմատացված համընկնումների համադրություն, ավելի շատ տվյալների աղբյուրներ ներգրավելով և ISBN-ները հանելով իրական գրքերի սկաներից (Z-Library/Libgen-ի դեպքում)։ Քանի՞սն են այդ ISBN-ներից եզակի: Սա լավագույնս պատկերացվում է Վեննի դիագրամով. Ավելի ճշգրիտ լինելու համար՝ Մենք զարմացած էինք, թե որքան քիչ է համընկնումը: ISBNdb-ն ունի հսկայական քանակությամբ ISBN-ներ, որոնք չեն հայտնվում ոչ Z-Library-ում, ոչ էլ Open Library-ում, և նույնը վերաբերում է (փոքր, բայց դեռևս զգալի չափով) մյուս երկուսին: Սա առաջացնում է բազմաթիվ նոր հարցեր: Որքանո՞վ կօգնի ավտոմատացված համընկնումը այն գրքերի պիտակավորման մեջ, որոնք պիտակավորված չեն եղել ISBN-ներով: Կլինե՞ն շատ համընկնումներ և, հետևաբար, ավելացված համընկնում: Եվ ի՞նչ կլինի, եթե ներգրավենք 4-րդ կամ 5-րդ տվյալների հավաքածուն: Որքա՞ն համընկնում կտեսնենք այդ ժամանակ: Սա մեզ տալիս է մեկնարկային կետ: Այժմ մենք կարող ենք դիտարկել բոլոր այն ISBN-ները, որոնք չեն եղել Z-Library տվյալների հավաքածուում և որոնք չեն համընկնում վերնագիր/հեղինակ դաշտերի հետ: Դա կարող է մեզ հնարավորություն տալ պահպանել աշխարհի բոլոր գրքերը՝ նախ ինտերնետից սկաներ հավաքելով, ապա իրական կյանքում գրքեր սկանավորելով: Վերջինս կարող է նույնիսկ ֆինանսավորվել հանրության կողմից կամ ղեկավարվել «պարգևներով» այն մարդկանցից, ովքեր կցանկանային տեսնել որոշակի գրքեր թվայնացված: Այդ ամենը այլ ժամանակի պատմություն է: Եթե ցանկանում եք օգնել այս ամենում՝ հետագա վերլուծություն; ավելի metadata հավաքել; ավելի շատ գրքեր գտնել; գրքերի OCR անել; դա անել այլ ոլորտների համար (օրինակ՝ հոդվածներ, աուդիոգրքեր, ֆիլմեր, հեռուստաշոուներ, ամսագրեր) կամ նույնիսկ այս տվյալների մի մասը հասանելի դարձնել ML / մեծ լեզվական մոդելների ուսուցման համար — խնդրում եմ կապ հաստատել ինձ հետ (<a %(reddit)s>Reddit</a>): Եթե հատկապես հետաքրքրված եք տվյալների վերլուծությամբ, մենք աշխատում ենք մեր տվյալների հավաքածուներն ու սցենարները ավելի հեշտ օգտագործվող ձևաչափով հասանելի դարձնելու վրա: Հիանալի կլիներ, եթե կարողանայիք պարզապես պատճենել նոթատետրը և սկսել խաղալ դրա հետ: Վերջապես, եթե ցանկանում եք աջակցել այս աշխատանքին, խնդրում ենք հաշվի առնել նվիրատվություն կատարելը: Սա ամբողջությամբ կամավորների կողմից ղեկավարվող գործողություն է, և ձեր ներդրումը մեծ տարբերություն է ստեղծում: Ամեն մի փոքրիկ օգնություն կարևոր է: Ներկայումս մենք ընդունում ենք նվիրատվություններ կրիպտոարժույթով; տեսեք Նվիրատվության էջը Աննայի Արխիվում: Տոկոս ստանալու համար մեզ անհրաժեշտ է բաժանարար՝ երբևէ հրատարակված գրքերի ընդհանուր քանակը։<sup>2</sup> Google Books-ի փակվելուց առաջ, նախագծի ինժեներ Լեոնիդ Թայչերը <a %(booksearch_blogspot)s>փորձեց գնահատել</a> այս թիվը։ Նա հումորով ներկայացրեց 129,864,880 թիվը («առնվազն մինչև կիրակի»)։ Նա այս թիվը գնահատեց՝ ստեղծելով աշխարհի բոլոր գրքերի միասնական տվյալների բազա։ Դրա համար նա համախմբեց տարբեր datasets և միավորեց դրանք տարբեր ձևերով։ Համառոտ ասած, կա ևս մեկ մարդ, ով փորձեց կատալոգավորել աշխարհի բոլոր գրքերը՝ Աարոն Սվարցը, հանգուցյալ թվային ակտիվիստը և Reddit-ի համահիմնադիրը։<sup>3</sup> Նա <a %(youtube)s>սկսեց Open Library-ն</a>՝ նպատակ ունենալով «մեկ վեբ էջ յուրաքանչյուր երբևէ հրատարակված գրքի համար», համատեղելով տվյալներ բազմաթիվ տարբեր աղբյուրներից։ Նա վճարեց իր թվային պահպանման աշխատանքի համար, երբ նրան դատապարտեցին ակադեմիական հոդվածների զանգվածային ներբեռնումների համար, ինչը հանգեցրեց նրա ինքնասպանությանը։ Անհրաժեշտ է ասել, որ սա մեր խմբի կեղծանունային լինելու պատճառներից մեկն է, և ինչու ենք մենք շատ զգույշ։ Open Library-ն դեռ հերոսաբար ղեկավարվում է Ինտերնետ արխիվի աշխատակիցների կողմից՝ շարունակելով Աարոնի ժառանգությունը։ Մենք կվերադառնանք այս թեմային այս գրառման մեջ։ Google-ի բլոգային գրառման մեջ Թայչերը նկարագրում է այս թվի գնահատման որոշ մարտահրավերներ։ Նախ, ի՞նչ է համարվում գիրք։ Կան մի քանի հնարավոր սահմանումներ. «Հրատարակություններ»-ը թվում է, թե ամենաօգտակար սահմանումն է, թե ինչ են «գրքերը»։ Հարմար է, որ այս սահմանումը նույնպես օգտագործվում է եզակի ISBN համարներ նշանակելու համար։ ISBN-ը կամ Միջազգային ստանդարտ գրքի համարը սովորաբար օգտագործվում է միջազգային առևտրում, քանի որ այն ինտեգրված է միջազգային շտրիխ կոդի համակարգի հետ («Միջազգային հոդվածի համարը»)։ Եթե ցանկանում եք վաճառել գիրք խանութներում, այն պետք է ունենա շտրիխ կոդ, ուստի դուք ստանում եք ISBN։ Թայչերի բլոգային գրառումը նշում է, որ թեև ISBN-ները օգտակար են, դրանք համընդհանուր չեն, քանի որ դրանք իրականում ընդունվել են միայն յոթանասունականների կեսերին և ոչ ամենուր աշխարհում։ Այնուամենայնիվ, ISBN-ը հավանաբար ամենալայնորեն օգտագործվող նույնացուցիչն է գրքերի հրատարակությունների համար, ուստի դա մեր լավագույն մեկնարկային կետն է։ Եթե մենք կարողանանք գտնել աշխարհի բոլոր ISBN-ները, մենք կստանանք օգտակար ցուցակ, թե որ գրքերն են դեռ պահպանման կարիք ունեն։ Ուրեմն, որտեղի՞ց ենք ստանում տվյալները։ Կան մի շարք առկա ջանքեր, որոնք փորձում են կազմել աշխարհի բոլոր գրքերի ցուցակը. Այս գրառման մեջ մենք ուրախ ենք հայտարարել փոքր թողարկման մասին (համեմատած մեր նախորդ Z-Library թողարկումների հետ)։ Մենք քերեցինք ISBNdb-ի մեծ մասը և տվյալները հասանելի դարձրեցինք torrenting-ի համար Pirate Library Mirror-ի կայքում (ԽՄԲԱԳՐՈՒՄ՝ տեղափոխվել է <a %(wikipedia_annas_archive)s>Աննայի արխիվ</a>; մենք այստեղ ուղղակիորեն հղում չենք տա, պարզապես փնտրեք այն)։ Սրանք մոտ 30.9 միլիոն գրառումներ են (20GB որպես <a %(jsonlines)s>JSON Lines</a>; 4.4GB սեղմված)։ Նրանց կայքում նրանք պնդում են, որ իրականում ունեն 32.6 միլիոն գրառումներ, ուստի մենք գուցե ինչ-որ կերպ բաց ենք թողել որոշները, կամ <em>նրանք</em> կարող են ինչ-որ բան սխալ անել։ Ամեն դեպքում, առայժմ մենք չենք կիսվի, թե ինչպես ենք դա արել՝ մենք դա թողնում ենք ընթերցողի համար որպես վարժություն։ ;-) Ինչով կկիսվենք, դա որոշ նախնական վերլուծություն է, որպեսզի փորձենք մոտենալ աշխարհի գրքերի քանակի գնահատմանը։ Մենք դիտարկեցինք երեք datasets՝ այս նոր ISBNdb dataset-ը, մեր սկզբնական metadata-ի թողարկումը, որը քերեցինք Z-Library ստվերային գրադարանից (որը ներառում է Library Genesis-ը), և Open Library-ի տվյալների բեռնաթափումը։ ISBNdb-ի արտահանում, կամ Քանի՞ Գիրք Պահպանված Է Հավերժ: Եթե մենք ճիշտ կերպով դեդուպլիկացնենք ստվերային գրադարանների ֆայլերը, աշխարհի բոլոր գրքերի քանի՞ տոկոսն ենք մենք պահպանել: Թարմացումներ <a %(wikipedia_annas_archive)s>Աննայի Արխիվի</a> մասին, մարդկության պատմության մեջ ամենամեծ իսկապես բաց գրադարանը: <em>WorldCat վերափոխում</em> Տվյալներ <strong>Ձևաչափը՞</strong> <a %(blog)s>Աննայի Արխիվի Կոնտեյներներ (AAC)</a>, որը հիմնականում <a %(jsonlines)s>JSON Lines</a> է սեղմված <a %(zstd)s>Zstandard</a>-ով, գումարած որոշ ստանդարտացված սեմանտիկա: Այս կոնտեյներները փաթաթում են տարբեր տեսակի գրառումներ՝ հիմնված մեր կիրառած տարբեր քերումների վրա: Մեկ տարի առաջ մենք <a %(blog)s>սկսեցինք</a> պատասխանել այս հարցին․ <strong>Ո՞ր տոկոսն է գրքերի, որոնք մշտապես պահպանվել են ստվերային գրադարանների կողմից։</strong> Եկեք նայենք տվյալների մասին որոշ հիմնական տեղեկություններ. Երբ գիրքը հայտնվում է բաց տվյալների ստվերային գրադարանում, ինչպիսին է <a %(wikipedia_library_genesis)s>Library Genesis</a>-ը, և այժմ <a %(wikipedia_annas_archive)s>Աննայի Արխիվը</a>, այն ամբողջ աշխարհում արտացոլվում է (տորրենտների միջոցով), դրանով իսկ գործնականում պահպանելով այն հավերժ: Հարցին պատասխանելու համար, թե գրքերի քանի տոկոսն է պահպանվել, մենք պետք է իմանանք բաժանարարը՝ քանի գիրք կա ընդհանրապես: Եվ իդեալական կլինի, եթե մենք ունենանք ոչ միայն թիվ, այլ իրական metadata: Այնուհետև մենք կարող ենք ոչ միայն համեմատել դրանք ստվերային գրադարանների հետ, այլև <strong>ստեղծել պահպանման ենթակա մնացած գրքերի TODO ցուցակ:</strong> Մենք նույնիսկ կարող ենք սկսել երազել համատեղ ջանքերի մասին՝ անցնելու այս TODO ցուցակը: Մենք քերեցինք <a %(wikipedia_isbndb_com)s>ISBNdb</a>-ը և ներբեռնեցինք <a %(openlibrary)s>Open Library dataset</a>-ը, բայց արդյունքները անբավարար էին: Հիմնական խնդիրը այն էր, որ ISBN-ների մեծ համընկնում չկար: Տեսեք այս Վեննի դիագրամը <a %(blog)s>մեր բլոգի գրառումից</a>: Մենք շատ զարմացանք, թե որքան քիչ համընկնում կար ISBNdb-ի և Open Library-ի միջև, որոնք երկուսն էլ լայնորեն ներառում են տվյալներ տարբեր աղբյուրներից, ինչպիսիք են վեբ քերումները և գրադարանային գրառումները: Եթե նրանք երկուսն էլ լավ աշխատանք են կատարում այնտեղի մեծամասնության ISBN-ները գտնելու հարցում, նրանց շրջանները հաստատական համընկնում կունենային, կամ մեկը մյուսի ենթաբազմություն կլիներ: Դա մեզ ստիպեց մտածել, թե քանի գիրք է <em>ամբողջությամբ դուրս այս շրջաններից</em>: Մեզ ավելի մեծ տվյալների բազա է պետք: Այդ ժամանակ մենք մեր հայացքը ուղղեցինք աշխարհի ամենամեծ գրքերի տվյալների բազային՝ <a %(wikipedia_worldcat)s>WorldCat</a>: Սա ոչ առևտրային <a %(wikipedia_oclc)s>OCLC</a>-ի սեփական տվյալների բազա է, որը հավաքում է metadata գրառումներ գրադարաններից ամբողջ աշխարհում՝ փոխարենը տալով այդ գրադարաններին ամբողջական տվյալների բազային հասանելիություն և նրանց հայտնվելը վերջնական օգտագործողների որոնման արդյունքներում: Չնայած OCLC-ն ոչ առևտրային է, նրանց բիզնես մոդելը պահանջում է պաշտպանել իրենց տվյալների բազան: Դե, մենք ցավում ենք ասել, OCLC-ի ընկերներ, մենք այն ամբողջությամբ տալիս ենք: :-) Անցած տարվա ընթացքում մենք մանրակրկիտ քերեցինք բոլոր WorldCat գրառումները: Սկզբում մենք հաջողություն ունեցանք: WorldCat-ը հենց նոր էր ներկայացնում իրենց ամբողջական կայքի վերափոխումը (2022թ. օգոստոսին): Սա ներառում էր նրանց հետին համակարգերի զգալի վերանորոգում, որը ներկայացնում էր բազմաթիվ անվտանգության թերություններ: Մենք անմիջապես օգտվեցինք հնարավորությունից և կարողացանք քերել հարյուր միլիոնավոր (!) գրառումներ ընդամենը մի քանի օրում: Դրանից հետո անվտանգության թերությունները դանդաղորեն շտկվեցին մեկը մյուսի հետևից, մինչև որ վերջնականը, որը մենք գտանք, շտկվեց մոտ մեկ ամիս առաջ: Այդ ժամանակ մենք արդեն ունեինք գրեթե բոլոր գրառումները և միայն ձգտում էինք ավելի բարձր որակի գրառումների: Այսպիսով, մենք զգացինք, որ ժամանակն է թողարկել: 1.3 միլիարդ WorldCat քերել <em><strong>Կարճ՝</strong> Աննայի Արխիվը քերել է ամբողջ WorldCat-ը (աշխարհի ամենամեծ գրադարանային մետադատայի հավաքածուն)՝ կազմելու այն գրքերի TODO ցուցակը, որոնք պետք է պահպանվեն։</em> WorldCat Զգուշացում՝ այս բլոգային գրառումը հնացած է։ Մենք որոշել ենք, որ IPFS-ը դեռ պատրաստ չէ հիմնական օգտագործման համար։ Մենք դեռ կկապենք ֆայլերը IPFS-ի վրա Աննայի Արխիվից, երբ հնարավոր է, բայց մենք այլևս չենք հյուրընկալելու այն ինքներս, և չենք խորհուրդ տալիս ուրիշներին հայելային օգտագործել IPFS-ով։ Խնդրում ենք տեսնել մեր Torrents էջը, եթե ցանկանում եք օգնել մեր հավաքածուի պահպանմանը։ Օգնեք սերմանել Z-Library-ը IPFS-ի վրա Գործընկեր սերվերի ներբեռնում SciDB Արտաքին փոխառություն Արտաքին վարձակալություն (տպագրության արգելափակում) Արտաքին ներբեռնում Ուսումնասիրել մետատվյալները Պարունակվում է տոռենտներում Հետ  (+%(num)s բոնուս) անվճար վճարված չեղարկված է ժամկետանց սպասում է Աննայի հաստատմանը անվավեր Տեքստը շարունակվում է անգլերեն։ Գնալ Վերակայել Առաջ Վերջին Եթե ձեր էլ. փոստի հասցեն չի աշխատում Libgen ֆորումներում, խորհուրդ ենք տալիս օգտագործել <a %(a_mail)s>Proton Mail</a> (անվճար): Կարող եք նաև <a %(a_manual)s>ձեռքով խնդրել</a> ձեր հաշվի ակտիվացումը: (կարող է պահանջվել <a %(a_browser)s>զննարկչի հաստատում</a> — անսահմանափակ ներբեռնումներ!) Արագ գործընկեր սերվեր #%(number)s (առաջարկվող) (մի փոքր ավելի արագ, բայց սպասման ցուցակով) (զննարկչի ստուգում չի պահանջվում) (առանց զննարկչի ստուգման կամ սպասման ցուցակների) (առանց սպասացուցակի, բայց կարող է շատ դանդաղ լինել) Դանդաղ գործընկեր սերվեր #%(number)s Աուդիոգիրք Կոմիքս Գիրք (գեղարվեստական) Գիրք (ոչ գեղարվեստական) Գիրք (անհայտ) Գիտական հոդված Ամսագիր Երաժշտական նոտագրություն Այլ Ստանդարտների փաստաթուղթ Բոլոր էջերը հնարավոր չէր փոխարկել PDF-ի Նշված է որպես կոտրված Libgen.li-ում Չի երևում Libgen.li-ում Libgen.rs-ում տեսանելի չէ գեղարվեստական գրականություն Չի երևում Libgen.rs Գեղարվեստական գրականությունում Exiftool-ի գործարկումը ձախողվեց այս ֆայլի վրա Նշված է որպես «վատ ֆայլ» Z-Գրադարանում Բացակայում է Z-Library-ից Նշված է որպես «սպամ» Z-Գրադարանում Ֆայլը չի կարող բացվել (օրինակ՝ վնասված ֆայլ, DRM) Հեղինակային իրավունքի պահանջ Ներբեռնման խնդիրներ (օրինակ՝ չի միանում, սխալի հաղորդագրություն, շատ դանդաղ) Սխալ մետատվյալներ (օրինակ՝ վերնագիր, նկարագրություն, շապիկի պատկեր) Այլ Վատ որակ (օրինակ՝ ձևավորման խնդիրներ, վատ սկանավորման որակ, բացակայող էջեր) Սպամ / ֆայլը պետք է հեռացվի (օրինակ՝ գովազդ, վիրավորական բովանդակություն) %(amount)s (%(amount_usd)s) %(amount)s ընդհանուր %(amount)s (%(amount_usd)s) ընդհանուր Հրաշալի Գրքասեր Հաջողակ Գրադարանավար Հիասքանչ Տվյալահավաք Հրաշալի Հավաքարար Բոնուս ներբեռնումներ Cerlalc Չեխական մետատվյալներ DuXiu 读秀 EBSCOhost էլեկտրոնային գրքերի ինդեքս Google Գրքեր Goodreads HathiTrust IA IA Կառավարվող Թվային Վարձակալություն ISBNdb ISBN GRP Libgen.li Բացառությամբ «scimag»-ի Libgen.rs Գեղարվեստական և ոչ գեղարվեստական Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Ռուսաստանի պետական գրադարան Sci-Hub Միջոցով Libgen.li «scimag» Sci-Hub / Libgen “scimag” Trantor Վերբեռնումներ AA-ում Z-Library Z-Գրադարան Չինարեն Վերնագիր, հեղինակ, DOI, ISBN, MD5, … Որոնել Հեղինակ Նկարագրություն և մետատվյալների մեկնաբանություններ Հրատարակություն Բնօրինակ ֆայլի անունը Հրատարակիչ (որոնել հատուկ դաշտ) Վերնագիր Հրատարակման տարեթիվ Տեխնիկական մանրամասներ Այս մետաղադրամն ունի սովորականից բարձր նվազագույն։ Խնդրում ենք ընտրել այլ տևողություն կամ այլ մետաղադրամ։ Հարցումը չհաջողվեց։ Խնդրում ենք փորձել մի քանի րոպեից, և եթե շարունակվում է, կապ հաստատեք մեզ հետ %(email)s հասցեով՝ կցելով սքրինշոթ։ Անհայտ սխալ է տեղի ունեցել։ Խնդրում ենք կապ հաստատել մեզ հետ %(email)s և ուղարկել սքրինշոթ։ Վճարման մշակման սխալ։ Խնդրում ենք սպասել մի պահ և փորձել նորից։ Եթե խնդիրը շարունակվի ավելի քան 24 ժամ, խնդրում ենք կապ հաստատել մեզ հետ %(email)s-ում՝ սքրինշոթով։ Մենք դրամահավաք ենք անցկացնում աշխարհի ամենամեծ կոմիքսների ստվերային գրադարանը <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">պահուստավորելու</a> համար։ Շնորհակալություն ձեր աջակցությանը։ <a href="/donate">Նվիրաբերել.</a> Եթե չեք կարող նվիրաբերել, մտածեք մեզ աջակցելու մասին՝ պատմելով ձեր ընկերներին և հետևելով մեզ <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>-ում կամ <a href="https://t.me/annasarchiveorg">Telegram</a>-ում։ Մի՛ ուղարկեք մեզ էլ․ նամակ՝ <a %(a_request)s>գրքեր խնդրելու</a><br>կամ փոքր (<10k) <a %(a_upload)s>վերբեռնումների</a> համար։ Աննայի Արխիվ DMCA / հեղինակային իրավունքի պահանջներ Մնացեք կապի մեջ Reddit Այլընտրանքներ SLUM (%(unaffiliated)s) չկապակցված Աննայի Արխիվին անհրաժեշտ է ձեր օգնությունը։ Եթե հիմա նվիրաբերեք, դուք կստանաք <strong>կրկնակի</strong> արագ ներբեռնումների քանակը։ Շատերը փորձում են մեզ կործանել, բայց մենք պայքարում ենք։ Եթե դուք նվիրաբերեք այս ամիս, դուք կստանաք <strong>կրկնակի</strong> արագ ներբեռնումների քանակը։ Վավեր է մինչև այս ամսվա վերջը։ Մարդկային գիտելիքների պահպանումը՝ հիանալի տոնական նվեր։ Անդամակցությունները համապատասխանաբար կերկարաձգվեն։ Գործընկեր սերվերները անհասանելի են հյուրընկալման փակման պատճառով։ Դրանք շուտով կրկին հասանելի կլինեն։ Աննայի Արխիվի դիմացկունությունը բարձրացնելու համար մենք փնտրում ենք կամավորներ՝ հայելիներ գործարկելու համար։ Մենք ունենք նոր նվիրատվության մեթոդ՝ %(method_name)s։ Խնդրում ենք հաշվի առնել %(donate_link_open_tag)sնվիրատվություն</a> կատարելը՝ այս կայքը պահելը էժան չէ, և ձեր նվիրատվությունը իսկապես մեծ տարբերություն է ստեղծում։ Շատ շնորհակալություն։ Հրավիրեք ընկերոջը, և դուք և ձեր ընկերը կստանաք %(percentage)s%% բոնուսային արագ ներբեռնումներ։ Զարմացրեք սիրելիին, տվեք նրանց հաշիվ անդամագրությամբ։ Հիանալի նվեր Սուրբ Վալենտինի օրվա համար! Իմացեք ավելին… Հաշիվ Գործունեություն Առաջադեմ Աննայի բլոգը ↗ Աննայի ծրագրակազմը ↗ բետա Կոդերի հետազոտող Datasets Նվիրաբերել Ներբեռնված ֆայլեր ՀՏՀ Գլխավոր էջ Բարելավել մետատվյալները LLM տվյալներ Մուտք / Գրանցվել Իմ նվիրատվությունները Հանրային պրոֆիլ Որոնում Անվտանգություն Թորրենտներ Թարգմանել ↗ Կամավորություն և պարգևներ Վերջին ներբեռնումներ: 📚&nbsp;Աշխարհի ամենամեծ բաց կոդով բաց տվյալների գրադարանը։ ⭐️&nbsp;Հայելիներ Sci-Hub, Library Genesis, Z-Library և ավելին։ 📈&nbsp;%(book_any)s գրքեր, %(journal_article)s հոդվածներ, %(book_comic)s կոմիքսներ, %(magazine)s ամսագրեր — պահպանված ընդմիշտ։  և  և ավելին DuXiu Internet Archive Վարկային Գրադարան LibGen 📚&nbsp;Մարդկության պատմության ամենամեծ իսկապես բաց գրադարանը։ 📈&nbsp;%(book_count)s&nbsp;գրքեր, %(paper_count)s&nbsp;հոդվածներ — պահպանված ընդմիշտ։ ⭐️&nbsp;Մենք արտացոլում ենք %(libraries)s-ը։ Մենք քերում և բաց կոդով ենք անում %(scraped)s։ Մեր բոլոր կոդերն ու տվյալները բաց կոդով են։ OpenLib Sci-Hub ,  📚 Աշխարհի ամենամեծ բաց կոդով բաց տվյալների գրադարանը։<br>⭐️ Հայելիներ Scihub, Libgen, Zlib և ավելին։ Z-Lib Աննայի արխիվ Անվավեր հարցում։ Այցելեք %(websites)s։ Աշխարհի ամենամեծ բաց կոդով բաց տվյալների գրադարանը։ Հայելիներ Sci-Hub, Library Genesis, Z-Library և ավելին։ Փնտրեք Աննայի Արխիվում Աննայի արխիվ Խնդրում ենք թարմացնել և նորից փորձել։ <a %(a_contact)s>Կապվեք մեզ հետ</a>, եթե խնդիրը շարունակվում է մի քանի ժամ։ 🔥 Խնդիր այս էջի բեռնման ժամանակ <li>1. Հետևեք մեզ <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>-ում, կամ <a href="https://t.me/annasarchiveorg">Telegram</a>-ում։</li><li>2. Տարածեք տեղեկությունը Anna’s Archive-ի մասին Twitter-ում, Reddit-ում, Tiktok-ում, Instagram-ում, ձեր տեղական սրճարանում կամ գրադարանում, կամ որտեղ էլ որ գնաք։ Մենք չենք հավատում դարպասապահությանը — եթե մեզ հեռացնեն, մենք պարզապես նորից կհայտնվենք այլ տեղում, քանի որ մեր ամբողջ կոդը և տվյալները լիովին բաց աղբյուր են։</li><li>3. Եթե կարող եք, մտածեք <a href="/donate">նվիրատվություն</a> կատարելու մասին։</li><li>4. Օգնեք <a href="https://translate.annas-software.org/">թարգմանել</a> մեր կայքը տարբեր լեզուներով։</li><li>5. Եթե ծրագրավորող եք, մտածեք մեր <a href="https://annas-software.org/">բաց կոդով նախագծերին</a> մասնակցելու, կամ մեր <a href="/datasets">տոռենտները</a> սերմանելու մասին։</li> 10. Ստեղծեք կամ օգնեք պահպանել Աննայի Արխիվի Վիքիպեդիայի էջը ձեր լեզվով։ 11. Մենք փնտրում ենք տեղադրել փոքր, ճաշակով գովազդներ։ Եթե ցանկանում եք գովազդել Աննայի Արխիվում, խնդրում ենք տեղեկացնել մեզ։ 6. Եթե դուք անվտանգության հետազոտող եք, մենք կարող ենք օգտագործել ձեր հմտությունները ինչպես հարձակման, այնպես էլ պաշտպանության համար։ Տես մեր <a %(a_security)s>Անվտանգություն</a> էջը։ 7. Մենք փնտրում ենք մասնագետներ անանուն վաճառողների վճարումների ոլորտում։ Կարո՞ղ եք օգնել մեզ ավելացնել նվիրատվությունների ավելի հարմար եղանակներ։ PayPal, WeChat, նվեր քարտեր։ Եթե որևէ մեկին գիտեք, խնդրում ենք կապ հաստատել մեզ հետ։ 8. Մենք միշտ փնտրում ենք ավելի շատ սերվերի հզորություն։ 9. Դուք կարող եք օգնել՝ զեկուցելով ֆայլերի խնդիրների մասին, թողնելով մեկնաբանություններ և ստեղծելով ցուցակներ հենց այս կայքում։ Դուք կարող եք նաև օգնել՝ <a %(a_upload)s>ավելի շատ գրքեր վերբեռնելով</a> կամ ուղղելով առկա գրքերի ֆայլերի խնդիրները կամ ձևաչափումը։ Ավելի մանրամասն տեղեկությունների համար, թե ինչպես կամավոր դառնալ, տես մեր <a %(a_volunteering)s>Կամավորություն և Պարգևներ</a> էջը։ Մենք խորապես հավատում ենք տեղեկատվության ազատ հոսքին և գիտելիքի ու մշակույթի պահպանմանը։ Այս որոնման համակարգով մենք կառուցում ենք հսկաների ուսերին։ Մենք խորապես հարգում ենք այն մարդկանց քրտնաջան աշխատանքը, ովքեր ստեղծել են տարբեր ստվերային գրադարաններ, և հուսով ենք, որ այս որոնման համակարգը կբարելավի նրանց հասանելիությունը։ Մեր առաջընթացի մասին տեղեկանալու համար հետևեք Աննային <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>-ում կամ <a href="https://t.me/annasarchiveorg">Telegram</a>-ում։ Հարցերի և կարծիքների համար խնդրում ենք կապ հաստատել Աննայի հետ %(email)s։ Հաշվի ID: %(account_id)s Դուրս գալ ❌ Ինչ-որ բան սխալ է։ Խնդրում ենք վերաբեռնել էջը և փորձել կրկին։ ✅ Դուք դուրս եք եկել համակարգից։ Կրկին մուտք գործելու համար թարմացրեք էջը։ Արագ ներբեռնումներ օգտագործված (վերջին 24 ժամում): <strong>%(used)s / %(total)s</strong> Անդամակցություն՝ <strong>%(tier_name)s</strong> մինչև %(until_date)s <a %(a_extend)s>(երկարացնել)</a> Կարող եք համատեղել մի քանի անդամակցություններ (արագ ներբեռնումները 24 ժամվա ընթացքում կավելացվեն միասին)։ Անդամակցություն՝ <strong>Ոչ մեկը</strong> <a %(a_become)s>(դառնալ անդամ)</a> Կապվեք Աննայի հետ %(email)s եթե ցանկանում եք բարձրացնել ձեր անդամակցությունը ավելի բարձր մակարդակի։ Հանրային պրոֆիլ՝ %(profile_link)s Գաղտնի բանալի (չկիսվեք!): %(secret_key)s ցուցադրել Միացեք մեզ այստեղ։ Վերազինվեք <a %(a_tier)s>ավելի բարձր մակարդակի</a>՝ մեր խմբին միանալու համար։ Բացառիկ Telegram խումբ՝ %(link)s Հաշիվ որ ներբեռնումները՞ Մուտք գործել Մի կորցրեք ձեր բանալին։ Անվավեր գաղտնի բանալի։ Ստուգեք ձեր բանալին և փորձեք նորից, կամ գրանցվեք նոր հաշիվ ստորև։ Գաղտնի բանալի Մուտքագրեք ձեր գաղտնի բանալին մուտք գործելու համար: Հին էլ. փոստի վրա հիմնված հաշիվ? Մուտքագրեք ձեր <a %(a_open)s>էլ. փոստը այստեղ</a>։ Գրանցել նոր հաշիվ Դեռ չունե՞ք հաշիվ։ Գրանցումը հաջողվեց։ Ձեր գաղտնի բանալին է՝ <span %(span_key)s>%(key)s</span> Այս բանալին զգուշորեն պահեք։ Եթե այն կորցնեք, կկորցնեք ձեր հաշվի հասանելիությունը։ <li %(li_item)s><strong>Էջանշում:</strong> Դուք կարող եք էջանշել այս էջը՝ ձեր բանալին վերականգնելու համար:</li><li %(li_item)s><strong>Ներբեռնում:</strong> Սեղմեք <a %(a_download)s>այս հղումը</a>՝ ձեր բանալին ներբեռնելու համար:</li><li %(li_item)s><strong>Գաղտնաբառերի կառավարիչ:</strong> Օգտագործեք գաղտնաբառերի կառավարիչ՝ բանալին պահպանելու համար, երբ այն մուտքագրեք ներքևում:</li> Մուտք / Գրանցում Զննարկչի ստուգում Զգուշացում. կոդը պարունակում է սխալ Unicode նիշեր և կարող է սխալ գործել տարբեր իրավիճակներում։ Հում բինարը կարելի է վերծանել base64 ներկայացումից URL-ում։ Նկարագրություն Պիտակ Նախածանց URL հատուկ կոդի համար Կայք Կոդեր, որոնք սկսվում են «%(prefix_label)s»-ով Խնդրում ենք չքերծել այս էջերը։ Փոխարենը խորհուրդ ենք տալիս <a %(a_import)s>գեներացնել</a> կամ <a %(a_download)s>ներբեռնել</a> մեր ElasticSearch և MariaDB տվյալների բազաները և գործարկել մեր <a %(a_software)s>բաց կոդով ծրագիրը</a>։ Հում տվյալները կարելի է ձեռքով ուսումնասիրել JSON ֆայլերի միջոցով, ինչպիսին է <a %(a_json_file)s>այս մեկը</a>։ Ավելի քիչ քան %(count)s գրառումներ Ընդհանուր URL Կոդերի հետազոտող Ցուցակ Հետազոտեք կոդերը, որոնցով նշված են գրառումները, ըստ նախածանցի։ «Գրառումներ» սյունակը ցույց է տալիս, թե քանի գրառում է նշված տվյալ նախածանցով կոդերով, ինչպես երևում է որոնման համակարգում (ներառյալ միայն մետատվյալներով գրառումները)։ «Կոդեր» սյունակը ցույց է տալիս, թե քանի իրական կոդ ունի տվյալ նախածանցը։ Հայտնի կոդի նախածանց «%(key)s» Ավելին… Նախածանց page.codes.record_starting_with կոդեր գրառումներ «%%»-ը կփոխարինվի կոդի արժեքով Որոնել Աննայի արխիվում Կոդեր URL հատուկ կոդի համար՝ «%(url)s» Այս էջը կարող է որոշ ժամանակ պահանջել գեներացնելու համար, այդ պատճառով պահանջվում է Cloudflare captcha։ <a %(a_donate)s>Անդամները</a> կարող են շրջանցել captcha-ն։ Չարաշահումը զեկուցված է՝ Լավագույն տարբերակ Ցանկանու՞մ եք զեկուցել այս օգտատիրոջը չարաշահող կամ անպատշաճ վարքի համար: Ֆայլի խնդիր՝ %(file_issue)s թաքնված մեկնաբանություն Պատասխանել Զեկուցել չարաշահումը Դուք զեկուցել եք այս օգտատիրոջը չարաշահման համար: Հեղինակային իրավունքների պահանջները այս էլ. հասցեին կսահմանափակվեն. օգտագործեք ձևը։ Ցույց տալ էլ․ հասցեն Մենք շատ ենք ողջունում ձեր կարծիքներն ու հարցերը։ Այնուամենայնիվ, հաշվի առնելով սպամի և անիմաստ էլեկտրոնային նամակների քանակը, խնդրում ենք նշեք տուփերը՝ հաստատելու համար, որ հասկանում եք մեզ հետ կապվելու այս պայմանները։ Հեղինակային իրավունքների պահանջների վերաբերյալ մեզ հետ կապվելու ցանկացած այլ եղանակ ավտոմատ կերպով կջնջվի։ DMCA / հեղինակային իրավունքի պահանջների համար օգտագործեք <a %(a_copyright)s>այս ձևը</a>։ Կոնտակտային էլ․ հասցե URL-ներ Anna’s Archive-ում (պարտադիր): Մեկը մեկ տողով: Խնդրում ենք ներառել միայն այն URL-ները, որոնք նկարագրում են գրքի նույն հրատարակությունը: Եթե ցանկանում եք պահանջ ներկայացնել մի քանի գրքերի կամ մի քանի հրատարակությունների համար, խնդրում ենք այս ձևը ներկայացնել մի քանի անգամ: Պահանջները, որոնք միավորում են մի քանի գրքեր կամ հրատարակություններ, կմերժվեն: Հասցե (պարտադիր) Աղբյուր նյութի հստակ նկարագրություն (պարտադիր) Էլ. փոստ (պարտադիր) Աղբյուր նյութի URL-ներ, մեկը մեկ տողով (պարտադիր): Խնդրում ենք ներառել որքան հնարավոր է շատ, որպեսզի օգնենք հաստատել ձեր պահանջը (օրինակ՝ Amazon, WorldCat, Google Books, DOI): Աղբյուր նյութի ISBN-ներ (եթե կիրառելի է): Մեկը մեկ տողով: Խնդրում ենք ներառել միայն այն ISBN-ները, որոնք ամբողջությամբ համապատասխանում են այն հրատարակությանը, որի համար դուք ներկայացնում եք հեղինակային իրավունքի պահանջ: Ձեր անունը (պարտադիր) ❌ Ինչ-որ բան սխալ է: Խնդրում ենք վերալիցքավորել էջը և փորձել կրկին: ✅ Շնորհակալություն ձեր հեղինակային իրավունքի պահանջը ներկայացնելու համար: Մենք կվերանայենք այն հնարավորինս շուտ: Խնդրում ենք վերալիցքավորել էջը՝ մեկ այլ պահանջ ներկայացնելու համար: <a %(a_openlib)s>Open Library</a> աղբյուր նյութի URL-ներ, մեկը մեկ տողով: Խնդրում ենք մի պահ որոնել Open Library-ում ձեր աղբյուր նյութը: Սա կօգնի մեզ հաստատել ձեր պահանջը: Հեռախոսահամար (պարտադիր) Հայտարարություն և ստորագրություն (պարտադիր) Ներկայացնել պահանջը Եթե ունեք DCMA կամ այլ հեղինակային իրավունքի պահանջ, խնդրում ենք լրացնել այս ձևը հնարավորինս ճշգրիտ: Եթե խնդիրների հանդիպեք, խնդրում ենք կապ հաստատել մեզ հետ մեր հատուկ DMCA հասցեով՝ %(email)s: Նշենք, որ այս հասցեին ուղարկված պահանջները չեն մշակվի, այն նախատեսված է միայն հարցերի համար: Խնդրում ենք օգտագործել ստորև ներկայացված ձևը ձեր պահանջները ներկայացնելու համար: DMCA / Հեղինակային իրավունքի պահանջի ձև Օրինակ գրառում Աննայի Արխիվում Տոռենտներ Աննայի Արխիվից Աննայի Արխիվի Կոնտեյներներ ձևաչափ Մետատվյալների ներմուծման սցենարներ Եթե հետաքրքրված եք այս տվյալների հավաքածուն <a %(a_archival)s>արխիվացնելու</a> կամ <a %(a_llm)s>LLM ուսուցման</a> նպատակներով, խնդրում ենք կապ հաստատել մեզ հետ։ Վերջին թարմացումը: %(date)s Գլխավոր %(source)s կայք Մետատվյալների փաստաթղթավորում (մեծ մասը) Ֆայլերը հայելային են Աննայի Արխիվի կողմից: %(count)s (%(percent)s%%) Ռեսուրսներ Ընդհանուր ֆայլեր: %(count)s Ընդհանուր ֆայլերի չափը: %(size)s Մեր բլոգի գրառումը այս տվյալների մասին <a %(duxiu_link)s>Duxiu</a> հսկայական տվյալների բազա է սկանավորված գրքերի, ստեղծված <a %(superstar_link)s>SuperStar Digital Library Group</a>-ի կողմից։ Դրանք հիմնականում ակադեմիական գրքեր են, սկանավորված՝ համալսարաններին և գրադարաններին թվային հասանելիություն ապահովելու համար։ Մեր անգլախոս լսարանի համար <a %(princeton_link)s>Princeton</a> և <a %(uw_link)s>Washingtonի համալսարան</a> ունեն լավ ակնարկներ։ Կա նաև հիանալի հոդված, որը տալիս է ավելի շատ նախապատմություն՝ <a %(article_link)s>«Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine»</a>։ Duxiu-ի գրքերը երկար ժամանակ է, ինչ պիրատացված են չինական ինտերնետում։ Դրանք սովորաբար վաճառվում են մեկ դոլարից պակաս գնով վերավաճառողների կողմից։ Դրանք հիմնականում տարածվում են Google Drive-ի չինական համարժեքի միջոցով, որը հաճախ կոտրվում է՝ ավելի շատ պահեստային տարածք ապահովելու համար։ Որոշ տեխնիկական մանրամասներ կարող եք գտնել <a %(link1)s>այստեղ</a> և <a %(link2)s>այստեղ</a>։ Չնայած գրքերը կիսահանրային տարածված են, բավականին դժվար է դրանք մեծ քանակությամբ ձեռք բերել։ Մենք դա բարձր ենք պահել մեր TODO ցուցակում և հատկացրել ենք մի քանի ամիս ամբողջական աշխատանք դրա համար։ Սակայն, 2023 թվականի վերջում մի անհավանական, զարմանալի և տաղանդավոր կամավոր կապ հաստատեց մեզ հետ, ասելով, որ նրանք արդեն կատարել են այս ամբողջ աշխատանքը՝ մեծ ծախսերով։ Նրանք կիսեցին ամբողջ հավաքածուն մեզ հետ՝ առանց որևէ փոխհատուցման ակնկալիքի, բացառությամբ երկարաժամկետ պահպանման երաշխիքի։ Իրոք, զարմանալի է։ Լրացուցիչ տեղեկություններ մեր կամավորներից (հում նշումներ)։ Հարմարեցված մեր <a %(a_href)s>բլոգի գրառումից</a>։ DuXiu 读秀 page.datasets.file Այս տվյալների հավաքածուն սերտորեն կապված է <a %(a_datasets_openlib)s>Open Library տվյալների հավաքածուի</a> հետ: Այն պարունակում է բոլոր մետատվյալների և IA-ի Կառավարվող Թվային Վարձակալության Գրադարանի ֆայլերի մեծ մասի քերումը: Թարմացումները թողարկվում են <a %(a_aac)s>Anna’s Archive Containers ձևաչափով</a>: Այս գրառումները ուղղակիորեն վերցված են Open Library տվյալների հավաքածուից, բայց նաև պարունակում են գրառումներ, որոնք չկան Open Library-ում։ Մենք նաև ունենք մի շարք տվյալների ֆայլեր, որոնք հավաքվել են համայնքի անդամների կողմից տարիների ընթացքում։ Հավաքածուն բաղկացած է երկու մասից։ Ձեզ անհրաժեշտ են երկու մասերն էլ, որպեսզի ստանաք բոլոր տվյալները (բացառությամբ հնացած տոռենտների, որոնք նշված են տոռենտների էջում)։ Թվային Վարձակալման Գրադարան մեր առաջին թողարկումը, նախքան մենք ստանդարտացրեցինք <a %(a_aac)s>Աննայի Արխիվի Կոնտեյներներ (AAC) ձևաչափը</a>։ Պարունակում է մետատվյալներ (json և xml ձևաչափերով), pdf-ներ (acsm և lcpdf թվային վարձակալման համակարգերից) և շապիկների մանրապատկերներ։ հերթական նոր թողարկումներ, օգտագործելով AAC։ Պարունակում է միայն մետատվյալներ 2023-01-01-ից հետո ժամանակային նշումներով, քանի որ մնացածը արդեն ընդգրկված է “ia”-ում։ Նաեւ բոլոր pdf ֆայլերը, այս անգամ acsm և “bookreader” (IA-ի վեբ ընթերցիչ) վարձակալման համակարգերից։ Չնայած անվանումը ճիշտ չէ, մենք դեռևս տեղադրում ենք bookreader ֆայլերը ia2_acsmpdf_files հավաքածուում, քանի որ դրանք փոխադարձաբար բացառիկ են։ IA Կառավարվող Թվային Վարձակալություն 98%%+ ֆայլերը որոնելի են։ Մեր առաքելությունն է արխիվացնել աշխարհի բոլոր գրքերը (ինչպես նաև հոդվածները, ամսագրերը և այլն) և դրանք լայնորեն հասանելի դարձնել։ Մենք հավատում ենք, որ բոլոր գրքերը պետք է հայելապատվեն լայնորեն, որպեսզի ապահովվի կրկնօրինակումը և դիմացկունությունը։ Ահա թե ինչու մենք հավաքում ենք ֆայլեր տարբեր աղբյուրներից։ Որոշ աղբյուրներ ամբողջովին բաց են և կարող են հայելապատվել մեծ քանակությամբ (օրինակ՝ Sci-Hub)։ Մյուսները փակ են և պաշտպանված, ուստի մենք փորձում ենք քերծել դրանք՝ իրենց գրքերը «ազատելու» համար։ Մյուսները ընկնում են ինչ-որ տեղ մեջտեղում։ Մեր բոլոր տվյալները կարող են <a %(a_torrents)s>torrent-վել</a>, և մեր բոլոր մետատվյալները կարող են <a %(a_anna_software)s>ստեղծվել</a> կամ <a %(a_elasticsearch)s>ներբեռնվել</a> որպես ElasticSearch և MariaDB տվյալների բազաներ։ Հում տվյալները կարող են ձեռքով ուսումնասիրվել JSON ֆայլերի միջոցով, ինչպիսիք են <a %(a_dbrecord)s>այսը</a>։ Մետատվյալներ ISBN կայք Վերջին թարմացում՝ %(isbn_country_date)s (%(link)s) Ռեսուրսներ Միջազգային ISBN գործակալությունը պարբերաբար հրապարակում է այն միջակայքերը, որոնք հատկացրել է ազգային ISBN գործակալություններին։ Դրանից մենք կարող ենք որոշել, թե որ երկիր, տարածաշրջան կամ լեզվական խումբ է պատկանում այս ISBN-ը։ Ներկայումս մենք օգտագործում ենք այս տվյալները անուղղակիորեն՝ <a %(a_isbnlib)s>isbnlib</a> Python գրադարանի միջոցով։ ISBN երկրի տեղեկատվություն Սա զանգվածային զանգերի հավաքածու է isbndb.com-ին 2022 թվականի սեպտեմբերին։ Մենք փորձեցինք ընդգրկել բոլոր ISBN միջակայքերը։ Սրանք մոտ 30.9 միլիոն գրառումներ են։ Նրանց կայքում նրանք պնդում են, որ իրականում ունեն 32.6 միլիոն գրառումներ, այնպես որ մենք գուցե ինչ-որ կերպ բաց ենք թողել որոշները, կամ <em>նրանք</em> կարող են ինչ-որ սխալ անել։ JSON պատասխանները գրեթե հում են նրանց սերվերից։ Մեկ տվյալների որակի խնդիր, որը մենք նկատեցինք, այն է, որ ISBN-13 համարների համար, որոնք սկսվում են այլ նախածանցով, քան «978-», նրանք դեռ ներառում են «isbn» դաշտ, որը պարզապես ISBN-13 համարն է՝ առաջին 3 թվերը կտրած (և ստուգման թվանշանը վերանայված)։ Սա ակնհայտորեն սխալ է, բայց նրանք այսպես են անում, այնպես որ մենք այն չենք փոխել։ Մեկ այլ հնարավոր խնդիր, որի հետ կարող եք հանդիպել, այն է, որ «isbn13» դաշտը կրկնօրինակներ ունի, այնպես որ դուք չեք կարող այն օգտագործել որպես հիմնական բանալի տվյալների բազայում։ «isbn13»+«isbn» դաշտերի համադրությունը կարծես թե եզակի է։ Թողարկում 1 (2022-10-31) Գեղարվեստական torrents-ները հետ են մնում (չնայած IDs ~4-6M չեն torrents-վել, քանի որ դրանք համընկնում են մեր Zlib torrents-ների հետ) Մեր բլոգի գրառումը կոմիքսների թողարկման մասին Կոմիքսների torrent-ները Anna’s Archive-ում Տարբեր Library Genesis պատճենների պատմության համար տես <a %(a_libgen_rs)s>Libgen.rs</a> էջը։ Libgen.li-ն պարունակում է գրեթե նույն բովանդակությունն ու մետատվյալները, ինչ Libgen.rs-ը, բայց ունի որոշ լրացուցիչ հավաքածուներ, մասնավորապես կոմիքսներ, ամսագրեր և ստանդարտ փաստաթղթեր։ Այն նաև ինտեգրել է <a %(a_scihub)s>Sci-Hub</a>-ը իր մետատվյալների և որոնման համակարգի մեջ, ինչը մենք օգտագործում ենք մեր տվյալների բազայի համար։ Այս գրադարանի մետատվյալները հասանելի են անվճար <a %(a_libgen_li)s>libgen.li-ում</a>։ Սակայն, այս սերվերը դանդաղ է և չի աջակցում ընդհատված կապերի վերականգնմանը։ Նույն ֆայլերը հասանելի են նաև <a %(a_ftp)s>FTP սերվերի վրա</a>, որը ավելի լավ է աշխատում։ Թվով գեղարվեստական գրականությունը նույնպես կարծես թե շեղվել է, բայց առանց նոր հոսքերի։ Կարծես թե սա տեղի է ունեցել 2022 թվականի սկզբից, թեև մենք դա չենք հաստատել։ Libgen.li-ի ադմինիստրատորի համաձայն, «fiction_rus» (ռուսական գեղարվեստական գրականություն) հավաքածուն պետք է ընդգրկված լինի <a %(a_booktracker)s>booktracker.org</a>-ի կողմից պարբերաբար թողարկվող տոռենտներով, հատկապես <a %(a_flibusta)s>flibusta</a> և <a %(a_librusec)s>lib.rus.ec</a> տոռենտներով (որոնք մենք հայելում ենք <a %(a_torrents)s>այստեղ</a>, թեև դեռ չենք պարզել, թե որ տոռենտները համապատասխանում են որ ֆայլերին)։ Գեղարվեստական գրականության հավաքածուն ունի իր սեփական տոռենտները (առանձնացված <a %(a_href)s>Libgen.rs</a>-ից) սկսած %(start)s-ից։ Որոշ տիրույթներ առանց տոռենտների (օրինակ՝ գեղարվեստական տիրույթներ f_3463000-ից մինչև f_4260000) հավանաբար Z-Library (կամ այլ կրկնօրինակ) ֆայլեր են, թեև մենք կարող ենք ցանկանալ որոշակի կրկնօրինակում անել և տոռենտներ ստեղծել lgli-հատուկ ֆայլերի համար այս տիրույթներում։ Բոլոր հավաքածուների վիճակագրությունը կարելի է գտնել <a %(a_href)s>libgen-ի կայքում</a>։ Տոռենտները հասանելի են լրացուցիչ բովանդակության մեծ մասի համար, հատկապես կոմիքսների, ամսագրերի և ստանդարտ փաստաթղթերի տոռենտները թողարկվել են Աննայի արխիվի հետ համագործակցությամբ։ Նշենք, որ “libgen.is”-ին վերաբերող torrent ֆայլերը հստակորեն <a %(a_libgen)s>Libgen.rs</a>-ի հայելիներն են (“.is” տարբեր դոմեն է, որը օգտագործվում է Libgen.rs-ի կողմից)։ Մետատվյալների օգտագործման համար օգտակար ռեսուրս է <a %(a_href)s>այս էջը</a>։ %(icon)s Նրանց «fiction_rus» հավաքածուն (ռուսական գեղարվեստական գրականություն) չունի հատուկ տոռենտներ, բայց ընդգրկված է այլ տոռենտներով, և մենք պահպանում ենք <a %(fiction_rus)s>հայելին</a>։ Ռուսական գեղարվեստական գրականության տոռենտները Աննայի արխիվում Գեղարվեստական գրքերի torrent-ները Anna’s Archive-ում Քննարկման ֆորում Մետատվյալներ Մետատվյալներ FTP-ի միջոցով Ամսագրերի torrent-ները Anna’s Archive-ում Մետատվյալների դաշտերի տեղեկատվություն Այլ torrent-ների հայելի (և եզակի գեղարվեստական և կոմիքսների torrent-ներ) Ստանդարտ փաստաթղթերի տոռենտները Աննայի արխիվում Libgen.li Տոռենտներ Աննայի Արխիվից (գրքերի շապիկներ) Library Genesis-ը հայտնի է իր տվյալները մեծ քանակությամբ տոռենտների միջոցով առատաձեռնորեն հասանելի դարձնելու համար։ Մեր Libgen հավաքածուն բաղկացած է օժանդակ տվյալներից, որոնք նրանք ուղղակիորեն չեն թողարկում, համագործակցելով նրանց հետ։ Շնորհակալություն բոլորին, ովքեր ներգրավված են Library Genesis-ի հետ աշխատելու համար։ Մեր բլոգը գրքերի շապիկների թողարկման մասին Այս էջը վերաբերում է “.rs” տարբերակին։ Այն հայտնի է իր մետատվյալները և գրքերի կատալոգի ամբողջական բովանդակությունը մշտապես հրապարակելու համար։ Գրքերի հավաքածուն բաժանված է գեղարվեստական և ոչ գեղարվեստական մասերի։ Մետատվյալների օգտագործման համար օգտակար ռեսուրս է <a %(a_metadata)s>այս էջը</a> (արգելափակում է IP տիրույթները, հնարավոր է պահանջվի VPN)։ 2024-03-ի դրությամբ, նոր տոռենտները հրապարակվում են <a %(a_href)s>այս ֆորումի թեմայում</a> (արգելափակում է IP տիրույթները, հնարավոր է պահանջվի VPN)։ Գեղարվեստական տոռենտներ Աննայի Արխիվում Libgen.rs գեղարվեստական տոռենտներ Libgen.rs քննարկման ֆորում Libgen.rs Մետատվյալներ Libgen.rs մետատվյալների դաշտերի տեղեկություններ Libgen.rs ոչ գեղարվեստական տոռենտներ Ոչ գեղարվեստական տոռենտներ Աննայի Արխիվում %(example)s գեղարվեստական գրքի համար։ Այս <a %(blog_post)s>առաջին թողարկումը</a> բավականին փոքր է՝ մոտ 300GB գրքերի շապիկներ Libgen.rs-ի ֆորկից, ինչպես գեղարվեստական, այնպես էլ ոչ գեղարվեստական։ Դրանք կազմակերպված են այնպես, ինչպես հայտնվում են libgen.rs-ում, օրինակ՝ %(example)s ոչ գեղարվեստական գրքի համար։ Ինչպես Z-Library հավաքածուի դեպքում, մենք դրանք բոլորը տեղադրել ենք մեծ .tar ֆայլում, որը կարող է մոնտաժվել <a %(a_ratarmount)s>ratarmount</a>-ի միջոցով, եթե ցանկանում եք ֆայլերը անմիջապես մատուցել։ Թողարկում 1 (%(date)s) Library Genesis-ի (կամ “Libgen”) տարբեր ճյուղերի արագ պատմությունը այն է, որ ժամանակի ընթացքում Library Genesis-ի հետ կապված տարբեր մարդիկ տարաձայնություններ ունեցան և գնացին իրենց առանձին ճանապարհներով։ Ըստ այս <a %(a_mhut)s>ֆորումի գրառման</a>, Libgen.li-ն սկզբնապես հյուրընկալվել է “http://free-books.dontexist.com”-ում։ “.fun” տարբերակը ստեղծվել է սկզբնական հիմնադրի կողմից։ Այն վերափոխվում է նոր, ավելի բաշխված տարբերակի օգտին։ <a %(a_li)s>“.li” տարբերակը</a> ունի կոմիքսների հսկայական հավաքածու, ինչպես նաև այլ բովանդակություն, որը դեռևս հասանելի չէ մեծածավալ torrent-ներով ներբեռնելու համար։ Այն ունի առանձին torrent հավաքածու գեղարվեստական գրքերի համար և իր տվյալների բազայում պարունակում է <a %(a_scihub)s>Sci-Hub</a>-ի մետատվյալները։ “.rs” տարբերակը ունի շատ նման տվյալներ և ամենահաճախակիորեն թողարկում է իր հավաքածուն մեծածավալ torrent-ներով։ Այն մոտավորապես բաժանված է “գեղարվեստական” և “ոչ գեղարվեստական” բաժինների։ Սկզբնապես “http://gen.lib.rus.ec”-ում։ <a %(a_zlib)s>Z-Library</a>-ը որոշ առումով նույնպես Library Genesis-ի ճյուղ է, թեև նրանք իրենց նախագծի համար օգտագործել են այլ անուն։ Libgen.rs Մենք նաև հարստացնում ենք մեր հավաքածուն միայն մետատվյալների աղբյուրներով, որոնք կարող ենք համադրել ֆայլերի հետ, օրինակ՝ օգտագործելով ISBN համարներ կամ այլ դաշտեր: Ստորև ներկայացված է դրանց ակնարկը: Կրկին, այս աղբյուրներից որոշները ամբողջությամբ բաց են, մինչդեռ մյուսների համար մենք պետք է քերենք դրանք: Նշենք, որ մետատվյալների որոնման ժամանակ մենք ցույց ենք տալիս բնօրինակ գրառումները: Մենք չենք միավորում գրառումները: Միայն մետատվյալների աղբյուրներ Open Library-ն բաց կոդով նախագիծ է Internet Archive-ի կողմից՝ նպատակ ունենալով կատալոգավորել աշխարհի բոլոր գրքերը։ Այն ունի աշխարհի ամենամեծ գրքերի սկանավորման գործողություններից մեկը և ունի բազմաթիվ գրքեր, որոնք հասանելի են թվային վարձակալության համար։ Նրա գրքերի մետատվյալների կատալոգը ազատորեն հասանելի է ներբեռնման համար և ներառված է Աննայի Արխիվում (թեև ներկայումս որոնման մեջ չէ, բացառությամբ, եթե հատուկ որոնեք Open Library ID-ով)։ Open Library Բացառելով կրկնօրինակները Վերջին թարմացումը Ֆայլերի քանակի տոկոսներ %% արտացոլված է AA-ի կողմից / torrents հասանելի են Չափ Աղբյուր Ստորև ներկայացված է Աննայի Արխիվի ֆայլերի աղբյուրների արագ ակնարկը։ Քանի որ ստվերային գրադարանները հաճախ համաժամանակացնում են տվյալները միմյանցից, գրադարանների միջև զգալի համընկնում կա։ Ահա թե ինչու թվերը չեն համընկնում ընդհանուրի հետ։ «Anna-ի Արխիվի կողմից հայելապատված և սերմանված» տոկոսը ցույց է տալիս, թե քանի ֆայլ ենք հայելապատում մենք։ Մենք սերմանում ենք այդ ֆայլերը մեծ քանակությամբ torrent-ների միջոցով և դրանք հասանելի ենք դարձնում ուղղակի ներբեռնման համար գործընկեր կայքերի միջոցով։ Ակնարկ Ընդհանուր Տոռենտներ Աննայի Արխիվում Sci-Hub-ի մասին լրացուցիչ տեղեկությունների համար, խնդրում ենք այցելել նրա <a %(a_scihub)s>պաշտոնական կայք</a>, <a %(a_wikipedia)s>Վիքիպեդիայի էջ</a> և այս <a %(a_radiolab)s>փոդքաստի հարցազրույցը</a>: Նշենք, որ Sci-Hub-ը <a %(a_reddit)s>սառեցված է 2021 թվականից</a>: Այն նախկինում էլ սառեցված էր, բայց 2021 թվականին մի քանի միլիոն հոդված ավելացվեց: Այնուամենայնիվ, որոշ սահմանափակ քանակությամբ հոդվածներ ավելացվում են Libgen-ի “scimag” հավաքածուներին, թեև ոչ այնքան, որ արժե նոր զանգվածային տոռենտներ ստեղծել: Մենք օգտագործում ենք Sci-Hub-ի մետատվյալները, որոնք տրամադրվում են <a %(a_libgen_li)s>Libgen.li</a>-ի “scimag” հավաքածուում: Մենք նաև օգտագործում ենք <a %(a_dois)s>dois-2022-02-12.7z</a> տվյալների հավաքածուն: Նշենք, որ “smarch” տոռենտները <a %(a_smarch)s>հնացած են</a> և, հետևաբար, չեն ընդգրկվում մեր տոռենտների ցանկում: Տոռենտներ Libgen.li-ում Տոռենտներ Libgen.rs-ում Մետատվյալներ և տոռենտներ Թարմացումներ Reddit-ում Փոդքաստի հարցազրույց Վիքիպեդիայի էջ Sci-Hub Sci-Hub: սառեցված է 2021-ից; մեծ մասը հասանելի է torrents-ով Libgen.li: փոքր ավելացումներ այդ ժամանակվանից</div> Որոշ աղբյուր գրադարաններ խթանում են իրենց տվյալների զանգվածային փոխանակումը տոռենտների միջոցով, մինչդեռ մյուսները չեն կիսում իրենց հավաքածուն: Վերջին դեպքում, Anna’s Archive-ը փորձում է քերել նրանց հավաքածուները և հասանելի դարձնել դրանք (տես մեր <a %(a_torrents)s>Տոռենտներ</a> էջը): Կան նաև միջանկյալ իրավիճակներ, օրինակ, երբ աղբյուր գրադարանները պատրաստ են կիսվել, բայց չունեն ռեսուրսներ դա անելու համար: Այդ դեպքերում մենք նույնպես փորձում ենք օգնել: Ստորև ներկայացված է, թե ինչպես ենք մենք համագործակցում տարբեր աղբյուր գրադարանների հետ: Աղբյուր գրադարաններ %(icon)s Տարբեր ֆայլերի տվյալների բազաներ, որոնք ցրված են չինական ինտերնետում. չնայած հաճախ վճարովի տվյալների բազաներ են %(icon)s Շատ ֆայլեր հասանելի են միայն պրեմիում BaiduYun հաշիվներով. ներբեռնման արագությունը դանդաղ է։ %(icon)s Աննայի Արխիվը կառավարում է <a %(duxiu)s>DuXiu ֆայլերի</a> հավաքածու %(icon)s Տարբեր մետատվյալների տվյալների բազաներ, որոնք ցրված են չինական ինտերնետում. չնայած հաճախ վճարովի տվյալների բազաներ են %(icon)s Նրանց ամբողջ հավաքածուի համար հեշտ հասանելի մետատվյալների փաթեթներ չկան։ %(icon)s Աննայի արխիվը կառավարում է <a %(duxiu)s>DuXiu մետատվյալների</a> հավաքածու Ֆայլեր %(icon)s Ֆայլերը հասանելի են միայն սահմանափակ ժամանակով, տարբեր հասանելիության սահմանափակումներով %(icon)s Աննայի Արխիվը կառավարում է <a %(ia)s>IA ֆայլերի</a> հավաքածու %(icon)s Որոշ մետատվյալներ հասանելի են <a %(openlib)s>Open Library-ի տվյալների բազայի դամփերի</a> միջոցով, բայց դրանք չեն ընդգրկում ամբողջ IA հավաքածուն։ %(icon)s Նրանց ամբողջ հավաքածուի համար հեշտությամբ հասանելի մետատվյալների դամփեր չկան։ %(icon)s «Աննայի արխիվը» կառավարում է <a %(ia)s>IA մետատվյալների</a> հավաքածու։ Վերջին թարմացումը %(icon)s Աննայի արխիվը և Libgen.li-ն համատեղ կառավարում են <a %(comics)s>կոմիքսների</a>, <a %(magazines)s>ամսագրերի</a>, <a %(standarts)s>ստանդարտ փաստաթղթերի</a> և <a %(fiction)s>գեղարվեստական գրականության (առանձնացված Libgen.rs-ից)</a> հավաքածուները։ %(icon)s Ոչ-գեղարվեստական տոռենտները կիսվում են Libgen.rs-ի հետ (և արտացոլվում <a %(libgenli)s>այստեղ</a>)։ %(icon)s Եռամսյակային <a %(dbdumps)s>HTTP տվյալների բազայի փաթեթներ</a> %(icon)s Ավտոմատացված տորրենտներ <a %(nonfiction)s>Ոչ գեղարվեստական</a> և <a %(fiction)s>Գեղարվեստական</a> համար %(icon)s «Աննայի արխիվը» կառավարում է <a %(covers)s>գրքերի շապիկների տոռենտների</a> հավաքածու %(icon)s Օրական <a %(dbdumps)s>HTTP տվյալների բազայի թափոններ</a> Մետատվյալներ %(icon)s Ամսական <a %(dbdumps)s>տվյալների բազայի փաթեթներ</a> %(icon)s Տվյալների տոռենտները հասանելի են <a %(scihub1)s>այստեղ</a>, <a %(scihub2)s>այստեղ</a> և <a %(libgenli)s>այստեղ</a> %(icon)s Որոշ նոր ֆայլեր <a %(libgenrs)s>ավելացվում են</a> Libgen-ի «scimag»-ում, բայց բավարար չեն նոր տոռենտների համար։ %(icon)s Sci-Hub-ը 2021 թվականից ի վեր չի ավելացրել նոր ֆայլեր։ %(icon)s Մետատվյալների փաթեթները հասանելի են <a %(scihub1)s>այստեղ</a> և <a %(scihub2)s>այստեղ</a>, ինչպես նաև <a %(libgenli)s>Libgen.li տվյալների բազայի</a> մաս (որը մենք օգտագործում ենք) Աղբյուր %(icon)s Տարբեր փոքր կամ մեկանգամյա աղբյուրներ։ Մենք խրախուսում ենք մարդկանց նախ բեռնել այլ ստվերային գրադարաններ, բայց երբեմն մարդիկ ունեն հավաքածուներ, որոնք չափազանց մեծ են, որպեսզի ուրիշները կարողանան դրանք դասակարգել, բայց ոչ այնքան մեծ, որ արժանի լինեն իրենց սեփական կատեգորիայի։ %(icon)s Ուղղակիորեն մեծ քանակությամբ հասանելի չէ, պաշտպանված է քերծումից։ %(icon)s «Աննայի արխիվը» կառավարում է <a %(worldcat)s>OCLC (WorldCat) մետատվյալների</a> հավաքածու։ %(icon)s «Աննայի արխիվը» և Z-Library-ն համատեղ կառավարում են <a %(metadata)s>Z-Library-ի մետատվյալների</a> և <a %(files)s>Z-Library-ի ֆայլերի</a> հավաքածուն։ Datasets Մենք համադրում ենք վերոնշյալ բոլոր աղբյուրները մեկ միավորված տվյալների բազայի մեջ, որը մենք օգտագործում ենք այս կայքը սպասարկելու համար: Այս միավորված տվյալների բազան անմիջապես հասանելի չէ, բայց քանի որ Anna’s Archive-ը ամբողջությամբ բաց կոդով է, այն կարելի է բավականին հեշտությամբ <a %(a_generated)s>ստեղծել</a> կամ <a %(a_downloaded)s>ներբեռնել</a> որպես ElasticSearch և MariaDB տվյալների բազաներ: Այդ էջի սցենարները ավտոմատ կերպով կբեռնեն բոլոր անհրաժեշտ մետատվյալները վերոնշյալ աղբյուրներից: Եթե ցանկանում եք ուսումնասիրել մեր տվյալները նախքան այդ սցենարները տեղական մակարդակում գործարկելը, կարող եք դիտել մեր JSON ֆայլերը, որոնք կապում են այլ JSON ֆայլերի հետ: <a %(a_json)s>Այս ֆայլը</a> լավ մեկնարկային կետ է: Միավորված տվյալների բազա Տորրենտներ Աննայի արխիվից դիտել որոնել Տարբեր փոքր կամ մեկանգամյա աղբյուրներ։ Մենք խրախուսում ենք մարդկանց նախ վերբեռնել այլ ստվերային գրադարաններում, բայց երբեմն մարդիկ ունեն հավաքածուներ, որոնք չափազանց մեծ են, որպեսզի մյուսները կարողանան դասակարգել, չնայած այնքան մեծ չեն, որ արժանի լինեն իրենց սեփական կատեգորիայի։ Ակնարկ <a %(a1)s>datasets էջից</a>: <a %(a_href)s>aaaaarg.fail</a>-ից։ Թվում է, թե բավականին ամբողջական է։ Մեր կամավոր «cgiym»-ից։ <a %(a_href)s><q>ACM Digital Library 2020</q></a> տորրենտից։ Բավականին մեծ համընկնում ունի առկա հոդվածների հավաքածուների հետ, բայց շատ քիչ MD5 համընկնումներ, ուստի մենք որոշեցինք այն ամբողջությամբ պահել։ <i>iRead eBooks</i> (<i>ai rit i-books</i>; airitibooks.com) կայքի քերումը, կամավոր <i>j</i>-ի կողմից։ Համապատասխանում է <i>airitibooks</i> metadata-ին <a %(a1)s><i>Այլ metadata քերումներ</i></a> բաժնում։ Հավաքածուից <a %(a1)s><i>Ալեքսանդրիայի գրադարան</i></a>։ Մասամբ սկզբնական աղբյուրից, մասամբ the-eye.eu կայքից, մասամբ այլ հայելիներից։ Մասնավոր գրքերի տոռենտ կայքից՝ <a %(a_href)s>Bibliotik</a> (հաճախ կոչվում է «Bib»), որի գրքերը տոռենտների մեջ են հավաքվել անունով (A.torrent, B.torrent) և տարածվել են the-eye.eu-ի միջոցով։ Մեր կամավոր «bpb9v»-ից։ <a %(a_href)s>CADAL</a>-ի մասին ավելի շատ տեղեկությունների համար տեսեք մեր <a %(a_duxiu)s>DuXiu տվյալների հավաքածուի էջի</a> նշումները։ Ավելին մեր կամավոր «bpb9v»-ից, հիմնականում DuXiu ֆայլեր, ինչպես նաև «WenQu» և «SuperStar_Journals» թղթապանակներ (SuperStar-ը DuXiu-ի հետևում գտնվող ընկերությունն է)։ Մեր կամավոր «cgiym»-ից, չինական տեքստեր տարբեր աղբյուրներից (ներկայացված են որպես ենթադիրեկտորիաներ), ներառյալ <a %(a_href)s>China Machine Press</a>-ից (մեծ չինական հրատարակիչ)։ Ոչ չինական հավաքածուներ (ներկայացված են որպես ենթադիրեկտորիաներ) մեր կամավոր «cgiym»-ից։ Չինական ճարտարապետության մասին գրքերի քերումը, կամավոր <i>cm</i>-ի կողմից․ <i>Ստացա այն հրատարակչության ցանցային խոցելիությունը շահագործելով, բայց այդ բացը արդեն փակվել է</i>։ Համապատասխանում է <i>chinese_architecture</i> metadata-ին <a %(a1)s><i>Այլ metadata քերումներ</i></a> բաժնում։ Գրքեր ակադեմիական հրատարակչական տուն <a %(a_href)s>De Gruyter</a>-ից, հավաքված մի քանի մեծ տորրենտներից։ <a %(a_href)s>docer.pl</a>-ի քերումը, լեհական ֆայլերի փոխանակման կայք, որը կենտրոնացած է գրքերի և այլ գրավոր աշխատանքների վրա։ Քերված է 2023 թվականի վերջում կամավոր «p»-ի կողմից։ Մենք չունենք լավ մետատվյալներ սկզբնական կայքից (նույնիսկ ֆայլերի ընդարձակումներ), բայց մենք ֆիլտրեցինք գրքի նման ֆայլերը և հաճախ կարողացանք մետատվյալներ հանել հենց ֆայլերից։ DuXiu epub-ներ, ուղղակիորեն DuXiu-ից, հավաքված կամավոր «w»-ի կողմից։ Միայն վերջին DuXiu գրքերն են հասանելի ուղղակիորեն էլեկտրոնային գրքերով, ուստի դրանց մեծ մասը պետք է լինեն վերջին։ Մնացած DuXiu ֆայլերը կամավոր «m»-ից, որոնք չէին գտնվում DuXiu սեփական PDG ձևաչափում (հիմնական <a %(a_href)s>DuXiu տվյալների հավաքածու</a>)։ Հավաքված են բազմաթիվ սկզբնական աղբյուրներից, ցավոք առանց այդ աղբյուրները պահպանելու ֆայլի ուղու մեջ։ <span></span> <span></span> <span></span> Էրոտիկ գրքերի քերումը, կամավոր <i>do no harm</i>-ի կողմից։ Համապատասխանում է <i>hentai</i> metadata-ին <a %(a1)s><i>Այլ metadata քերումներ</i></a> բաժնում։ <span></span> <span></span> Հավաքածու, որը քաղված է ճապոնական մանգայի հրատարակչից կամավոր «t»-ի կողմից։ <a %(a_href)s>Լոնգքուանի ընտրված դատական արխիվներ</a>, տրամադրված կամավոր «c»-ի կողմից։ <a %(a_href)s>magzdb.org</a>-ի քերծում, որը Library Genesis-ի դաշնակից է (այն կապված է libgen.rs գլխավոր էջում), բայց որը չէր ցանկանում տրամադրել իրենց ֆայլերը ուղղակիորեն։ Ստացվել է կամավոր «p»-ի կողմից 2023 թվականի վերջում։ <span></span> Տարբեր փոքր բեռնումներ, որոնք չափազանց փոքր են որպես իրենց ենթահավաքածու, բայց ներկայացված են որպես թղթապանակներ։ Էլեկտրոնային գրքեր AvaxHome-ից, ռուսական ֆայլերի փոխանակման կայք։ Թերթերի և ամսագրերի արխիվ։ Համապատասխանում է <i>newsarch_magz</i> metadata-ին <a %(a1)s><i>Այլ metadata քերումներ</i></a> բաժնում։ <a %(a1)s>Փիլիսոփայության փաստաթղթավորման կենտրոնի</a> քերումը։ Կամավոր «o»-ի հավաքածուն, որը հավաքել է լեհական գրքեր ուղղակիորեն սկզբնական թողարկման («scene») կայքերից։ Կոմբինացված հավաքածուներ <a %(a_href)s>shuge.org</a>-ից կամավորներ «cgiym» և «woz9ts»-ի կողմից։ <span></span> <a %(a_href)s>«Տրանտորի կայսերական գրադարան»</a> (անվանված հորինված գրադարանի անունով), քաղված 2022 թվականին կամավոր «t»-ի կողմից։ <span></span> <span></span> <span></span> Ենթա-ենթահավաքածուներ (ներկայացված որպես թղթապանակներ) կամավոր «woz9ts»-ից՝ <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (կազմված <a %(a_sikuquanshu)s>Dizhi(迪志)</a>-ի կողմից Թայվանում), mebook (mebook.cc, 我的小书屋, իմ փոքրիկ գրադարան — woz9ts: «Այս կայքը հիմնականում կենտրոնանում է բարձր որակի էլեկտրոնային գրքերի ֆայլերի կիսման վրա, որոնցից որոշները տպագրված են սեփականատիրոջ կողմից։ Սեփականատերը <a %(a_arrested)s>ձերբակալվել</a> է 2019 թվականին, և ինչ-որ մեկը կազմել է ֆայլերի հավաքածու, որոնք նա կիսել է»)։ Մնացած DuXiu ֆայլերը կամավոր «woz9ts»-ից, որոնք DuXiu-ի սեփական PDG ձևաչափով չէին (դեռ պետք է փոխարկվեն PDF-ի): «Վերբեռնում» հավաքածուն բաժանված է փոքր ենթահավաքածուների, որոնք նշված են AACID-ներում և տորրենտների անուններում։ Բոլոր ենթահավաքածուները նախապես կրկնօրինակվել են հիմնական հավաքածուի դեմ, չնայած մետատվյալների «upload_records» JSON ֆայլերը դեռ պարունակում են բազմաթիվ հղումներ սկզբնական ֆայլերին։ Ոչ գրքային ֆայլերը նույնպես հեռացվել են ենթահավաքածուների մեծ մասից և սովորաբար <em>չեն</em> նշված «upload_records» JSON-ում։ Ենթահավաքածուներն են՝ Նշումներ Ենթահավաքածու Շատ ենթահավաքածուներ իրենցից ենթա-ենթահավաքածուներ են (օրինակ՝ տարբեր սկզբնաղբյուրներից), որոնք ներկայացված են որպես «filepath» դաշտերում։ Վերբեռնումներ Աննայի արխիվում Մեր բլոգի գրառումը այս տվյալների մասին <a %(a_worldcat)s>WorldCat</a>-ը ոչ առևտրային <a %(a_oclc)s>OCLC</a>-ի սեփականատիրական տվյալների բազա է, որը հավաքում է մետատվյալների գրառումներ աշխարհի գրադարաններից: Հավանաբար, դա աշխարհի ամենամեծ գրադարանային մետատվյալների հավաքածուն է: 2023 թվականի հոկտեմբերին մենք <a %(a_scrape)s>թողարկեցինք</a> OCLC (WorldCat) տվյալների բազայի համապարփակ քերումը, <a %(a_aac)s>Աննայի Արխիվի Կոնտեյներների ձևաչափով</a>: 2023 թվականի հոկտեմբեր, նախնական թողարկում․ OCLC (WorldCat) Տոռենտներ Աննայի Արխիվից Օրինակային գրառում Աննայի Արխիվում (բնօրինակ հավաքածու) Օրինակային գրառում Աննայի Արխիվում (“zlib3” հավաքածու) Տոռենտներ Աննայի Արխիվից (մետատվյալներ + բովանդակություն) Բլոգի գրառում Թողարկում 1-ի մասին Բլոգի գրառում Թողարկում 2-ի մասին 2022 թվականի վերջում Z-Library-ի ենթադրյալ հիմնադիրները ձերբակալվեցին, և դոմենները բռնագրավվեցին ԱՄՆ իշխանությունների կողմից։ Այդ ժամանակից ի վեր կայքը դանդաղորեն վերադառնում է առցանց։ Անհայտ է, թե ով է ներկայումս այն ղեկավարում։ Թարմացում 2023 թվականի փետրվարի դրությամբ։ Z-Library-ն իր արմատները ունի <a %(a_href)s>Library Genesis</a> համայնքում և սկզբում օգտագործել է նրանց տվյալները։ Այդ ժամանակից ի վեր այն զգալիորեն պրոֆեսիոնալացել է և ունի շատ ավելի ժամանակակից ինտերֆեյս։ Այդ պատճառով նրանք կարողանում են ստանալ շատ ավելի շատ նվիրատվություններ՝ ինչպես ֆինանսական, որպեսզի շարունակեն բարելավել իրենց կայքը, այնպես էլ նոր գրքերի նվիրատվություններ։ Նրանք հավաքել են մեծ հավաքածու՝ Library Genesis-ի հետ միասին։ Հավաքածուն բաղկացած է երեք մասից։ Առաջին երկու մասերի բնօրինակ նկարագրական էջերը պահպանված են ներքևում։ Ձեզ անհրաժեշտ են բոլոր երեք մասերը՝ բոլոր տվյալները ստանալու համար (բացառությամբ փոխարինված տոռենտների, որոնք ջնջված են տոռենտների էջում)։ %(title)s: մեր առաջին թողարկումը։ Սա այն ժամանակ «Ծովահեն գրադարանի հայելի» («pilimi») կոչվողի առաջին թողարկումն էր։ %(title)s: երկրորդ թողարկում, այս անգամ բոլոր ֆայլերը փաթեթավորված են .tar ֆայլերում։ %(title)s: նոր թողարկումների ավելացում, օգտագործելով <a %(a_href)s>Աննայի Արխիվի Կոնտեյներներ (AAC) ձևաչափը</a>, այժմ թողարկված Z-Library թիմի հետ համագործակցությամբ։ Սկզբնական հայելին մանրակրկիտ կերպով ստացվել է 2021 և 2022 թվականների ընթացքում։ Այս պահին այն մի փոքր հնացած է. այն արտացոլում է հավաքածուի վիճակը 2021 թվականի հունիսին։ Մենք կթարմացնենք սա ապագայում։ Հիմա մենք կենտրոնացած ենք այս առաջին թողարկումը դուրս բերելու վրա։ Քանի որ Library Genesis-ն արդեն պահպանված է հանրային տոռենտներով և ներառված է Z-Library-ում, մենք 2022 թվականի հունիսին կատարեցինք հիմնական կրկնօրինակման հեռացում Library Genesis-ի դեմ։ Դրա համար մենք օգտագործեցինք MD5 հեշեր։ Հավանաբար գրադարանում շատ ավելի շատ կրկնօրինակ բովանդակություն կա, ինչպիսիք են նույն գրքի տարբեր ֆայլային ձևաչափերը։ Սա դժվար է ճշգրիտ հայտնաբերել, ուստի մենք չենք անում։ Կրկնօրինակման հեռացումից հետո մենք մնացինք ավելի քան 2 միլիոն ֆայլերով, ընդհանուր առմամբ մոտ 7TB։ Հավաքածուն բաղկացած է երկու մասից՝ մետատվյալների MySQL “.sql.gz” պահոցից և մոտ 50-100GB չափի 72 տոռենտ ֆայլերից։ Մետատվյալները պարունակում են Z-Library կայքի կողմից հաղորդված տվյալները (վերնագիր, հեղինակ, նկարագրություն, ֆայլի տեսակ), ինչպես նաև իրական ֆայլի չափը և md5sum-ը, որը մենք նկատել ենք, քանի որ երբեմն դրանք չեն համընկնում։ Կան ֆայլերի շարքեր, որոնց համար Z-Library-ն ինքն ունի սխալ մետատվյալներ։ Մենք կարող ենք նաև որոշ առանձին դեպքերում սխալ ներբեռնել ֆայլեր, որոնք կփորձենք հայտնաբերել և շտկել ապագայում։ Մեծ տորրենտ ֆայլերը պարունակում են իրական գրքի տվյալները, Z-Library ID-ն որպես ֆայլի անուն։ Ֆայլի ընդլայնումները կարող են վերականգնվել մետատվյալների թափոնների միջոցով։ Հավաքածուն խառնուրդ է ոչ գեղարվեստական և գեղարվեստական բովանդակության (չբաժանված ինչպես Library Genesis-ում): Որակն էլ շատ տարբեր է։ Այս առաջին թողարկումը այժմ ամբողջությամբ հասանելի է։ Նշենք, որ տորրենտ ֆայլերը հասանելի են միայն մեր Tor հայելու միջոցով։ Թողարկում 1 (%(date)s) Սա լրացուցիչ torrent ֆայլ է: Այն չի պարունակում նոր տեղեկություններ, բայց ունի որոշ տվյալներ, որոնք կարող են որոշ ժամանակ պահանջել հաշվարկելու համար: Դա հարմար է դարձնում ունենալը, քանի որ այս torrent-ը ներբեռնելը հաճախ ավելի արագ է, քան այն զրոյից հաշվարկելը: Մասնավորապես, այն պարունակում է SQLite ինդեքսներ tar ֆայլերի համար, օգտագործման համար <a %(a_href)s>ratarmount</a>-ի հետ: Թողարկում 2 հավելված (%(date)s) Մենք ստացել ենք բոլոր գրքերը, որոնք ավելացվել են Z-Library-ում մեր վերջին հայելուց մինչև 2022 թվականի օգոստոս։ Մենք նաև վերադարձել ենք և հավաքել որոշ գրքեր, որոնք առաջին անգամ բաց թողեցինք։ Ընդհանուր առմամբ, այս նոր հավաքածուն մոտ 24TB է։ Կրկին, այս հավաքածուն կրկնօրինակված է Library Genesis-ի դեմ, քանի որ այդ հավաքածուի համար արդեն տոռենտներ հասանելի են։ Տվյալները կազմակերպված են առաջին թողարկման նման։ Կա մետատվյալների MySQL “.sql.gz” պահոց, որը ներառում է նաև առաջին թողարկման բոլոր մետատվյալները, այդպիսով փոխարինելով այն։ Մենք նաև ավելացրել ենք որոշ նոր սյունակներ. Մենք սա նշել ենք անցյալ անգամ, բայց պարզապես պարզաբանելու համար. “ֆայլի անուն” և “md5” ֆայլի իրական հատկություններն են, մինչդեռ “ֆայլի անունը հաղորդված” և “md5 հաղորդված” այն է, ինչ մենք հավաքել ենք Z-Library-ից։ Երբեմն այս երկուսը չեն համընկնում, ուստի մենք ներառել ենք երկուսն էլ։ Այս թողարկման համար մենք փոխեցինք կոլլացիան “utf8mb4_unicode_ci”-ի, որը պետք է համատեղելի լինի MySQL-ի հին տարբերակների հետ։ Տվյալների ֆայլերը նման են նախորդ անգամին, թեև դրանք շատ ավելի մեծ են։ Մենք պարզապես չէինք կարողանում զբաղվել ավելի փոքր տոռենտ ֆայլեր ստեղծելով։ “pilimi-zlib2-0-14679999-extra.torrent”-ը պարունակում է բոլոր այն ֆայլերը, որոնք մենք բաց թողեցինք նախորդ թողարկման ժամանակ, մինչդեռ մյուս տոռենտները բոլորն էլ նոր ID շարքեր են։  <strong>Թարմացում %(date)s:</strong> Մենք մեր տորրենտների մեծ մասը չափազանց մեծ ենք դարձրել, ինչը դժվարացնում է տորրենտ հաճախորդներին։ Մենք հեռացրել ենք դրանք և թողարկել նոր տորրենտներ։ <strong>Թարմացում %(date)s:</strong> Դեռ շատ ֆայլեր կային, ուստի մենք դրանք փաթեթավորեցինք tar ֆայլերի մեջ և նորից թողարկեցինք նոր torrent-ներ: %(key)s: արդյոք այս ֆայլը արդեն կա Library Genesis-ում, թե ոչ-գեղարվեստական, թե գեղարվեստական հավաքածուում (համընկնում է md5-ով)։ %(key)s: որ տոռենտում է այս ֆայլը։ %(key)s: սահմանված, երբ մենք չէինք կարողանում ներբեռնել գիրքը։ Թողարկում 2 (%(date)s) Zlib թողարկումներ (բնօրինակ նկարագրության էջեր) Tor դոմեն Հիմնական կայք Z-Library-ի քերուկ «Չինական» հավաքածուն Z-Library-ում կարծես թե նույնն է, ինչ մեր DuXiu հավաքածուն, բայց տարբեր MD5-ներով։ Մենք բացառում ենք այս ֆայլերը torrent-ներից՝ կրկնօրինակումը խուսափելու համար, բայց դեռ ցույց ենք տալիս դրանք մեր որոնման ինդեքսում։ Մետատվյալներ Դուք ստանում եք %(percentage)s%% բոնուս արագ ներբեռնումներ, քանի որ ձեզ ուղղորդել է օգտատեր %(profile_link)s։ Սա վերաբերում է ամբողջ անդամակցության ժամանակահատվածին։ Նվիրաբերել Միանալ Ընտրված մինչև %(percentage)s%% զեղչեր Alipay-ն աջակցում է միջազգային վարկային/դեբետային քարտերին։ Տես <a %(a_alipay)s>այս ուղեցույցը</a> լրացուցիչ տեղեկությունների համար։ Ուղարկեք մեզ Amazon.com նվեր քարտեր օգտագործելով ձեր կրեդիտ/դեբետ քարտը։ Դուք կարող եք գնել կրիպտո կրեդիտ/դեբետ քարտերով։ WeChat (Weixin Pay) աջակցում է միջազգային կրեդիտ/դեբետ քարտերին։ WeChat հավելվածում անցեք «Me => Services => Wallet => Add a Card»։ Եթե դա չեք տեսնում, միացրեք այն օգտագործելով «Me => Settings => General => Tools => Weixin Pay => Enable»։ (օգտագործեք, երբ Ethereum ուղարկում եք Coinbase-ից) պատճենված է! պատճենել (ամենացածր նվազագույն գումար) (զգուշացում: բարձր նվազագույն գումար) -%(percentage)s%% 12 ամիս 1 ամիս 24 ամիս 3 ամիս 48 ամիս 6 ամիս 96 ամիս Ընտրեք, թե որքան ժամանակով եք ցանկանում բաժանորդագրվել։ <div %(div_monthly_cost)s></div><div %(div_after)s>զեղչերից <span %(span_discount)s></span> հետո</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% 12 ամսով 1 ամսով 24 ամսվա համար 3 ամսվա համար 48 ամսով 6 ամսով 96 ամիսների համար %(monthly_cost)s / ամիս կապվեք մեզ հետ Ուղղակի <strong>SFTP</strong> սերվերներ Ձեռնարկության մակարդակի նվիրատվություն կամ փոխանակում նոր հավաքածուների համար (օրինակ՝ նոր սկաներ, OCR-ացված datasets): Փորձագետի մուտք <strong>Անսահմանափակ</strong> բարձր արագությամբ մուտք <div %(div_question)s>Կարո՞ղ եմ արդյոք բարելավել իմ անդամակցությունը կամ ունենալ մի քանի անդամակցություն:</div> <div %(div_question)s>Կարո՞ղ եմ նվիրատվություն կատարել առանց անդամ դառնալու։</div> Իհարկե։ Մենք ընդունում ենք ցանկացած չափի նվիրատվություններ այս Monero (XMR) հասցեով՝ %(address)s։ <div %(div_question)s>Ի՞նչ են նշանակում ամսական միջակայքերը:</div> Դուք կարող եք հասնել միջակայքի ստորին կողմին՝ կիրառելով բոլոր զեղչերը, օրինակ՝ ընտրելով մեկ ամսից ավելի ժամանակահատված։ <div %(div_question)s>Անդամագրությունները ավտոմատ կերպով չեն թարմացվում՞</div> Անդամագրությունները <strong>չեն</strong> ավտոմատ կերպով թարմացվում։ Դուք կարող եք միանալ այնքան ժամանակ, որքան ցանկանում եք։ <div %(div_question)s>Ի՞նչի վրա եք ծախսում նվիրատվությունները:</div> 100%% գնում է աշխարհի գիտելիքների և մշակույթի պահպանման և հասանելի դարձնելու համար։ Ներկայումս մենք այն հիմնականում ծախսում ենք սերվերների, պահեստավորման և թողունակության վրա։ Ոչ մի գումար չի գնում թիմի անդամներին անձամբ։ <div %(div_question)s>Կարո՞ղ եմ մեծ նվիրատվություն անել:</div> Դա հիանալի կլինի։ Մի քանի հազար դոլարից ավելի նվիրատվությունների համար, խնդրում ենք կապ հաստատել մեզ հետ ուղղակիորեն %(email)s։ <div %(div_question)s>Ունե՞ք այլ վճարման մեթոդներ:</div> Ներկայումս՝ ոչ։ Շատերը չեն ցանկանում, որ նման արխիվներ գոյություն ունենան, ուստի մենք պետք է զգույշ լինենք։ Եթե կարող եք օգնել մեզ ապահով կերպով ստեղծել այլ (ավելի հարմար) վճարման մեթոդներ, խնդրում ենք կապվել մեզ հետ %(email)s հասցեով։ Նվիրատվությունների ՀՏՀ Դուք ունեք <a %(a_donation)s>գործող նվիրատվություն</a> ընթացքի մեջ։ Խնդրում ենք ավարտել կամ չեղարկել այդ նվիրատվությունը, նախքան նոր նվիրատվություն կատարելը։ <a %(a_all_donations)s>Դիտել իմ բոլոր նվիրատվությունները</a> $5000-ից ավելի նվիրատվությունների համար խնդրում ենք կապ հաստատել մեզ հետ ուղղակիորեն %(email)s։ Մենք ողջունում ենք մեծ նվիրատվությունները հարուստ անհատներից կամ հաստատություններից։  Խնդրում ենք նկատի ունենալ, որ այս էջի անդամակցությունները «ամսական» են, սակայն դրանք միանվագ նվիրատվություններ են (ոչ կրկնվող): Տես <a %(faq)s>Նվիրատվության ՀՏՀ</a>: «Աննայի Արխիվը» ոչ առևտրային, բաց կոդով, բաց տվյալներով նախագիծ է։ Նվիրատվություն կատարելով և անդամ դառնալով՝ դուք աջակցում եք մեր գործունեությանը և զարգացմանը։ Մեր բոլոր անդամներին՝ շնորհակալություն մեզ աջակցելու համար։ ❤️ Լրացուցիչ տեղեկությունների համար այցելեք <a %(a_donate)s>Նվիրատվության ՀՏՀ</a>։ Անդամ դառնալու համար, խնդրում ենք <a %(a_login)s>Մուտք գործել կամ Գրանցվել</a>: Շնորհակալություն ձեր աջակցության համար! $%(cost)s / ամիս Եթե վճարման ընթացքում սխալ եք թույլ տվել, մենք չենք կարող վերադարձնել գումարը, բայց կփորձենք ուղղել։ Գտեք “Crypto” էջը ձեր PayPal հավելվածում կամ կայքում։ Սա սովորաբար գտնվում է “Ֆինանսներ” բաժնում։ Գնացեք ձեր PayPal հավելվածի կամ կայքի “Bitcoin” էջը։ Սեղմեք “Transfer” կոճակը %(transfer_icon)s, ապա “Send”։ Alipay Alipay 支付宝 / WeChat 微信 Amazon նվեր քարտ %(amazon)s նվեր քարտ Բանկային քարտ Բանկային քարտ (օգտագործելով հավելված) Բինանս Վարկային/դեբետային/Apple/Google (BMC) Cash App Վարկային/դեբետային քարտ Կրեդիտ/դեբիտ քարտ 2 Վարկային/դեբետային քարտ (պահուստային) Կրիպտո %(bitcoin_icon)s Քարտ / PayPal / Venmo PayPal (ԱՄՆ) %(bitcoin_icon)s PayPal PayPal (սովորական) Pix (Բրազիլիա) Revolut (ժամանակավորապես անհասանելի) WeChat Ընտրեք ձեր նախընտրած կրիպտոարժույթը՝ Նվիրաբերեք Amazon նվեր քարտի միջոցով։ <strong>ԿԱՐԵՎՈՐ՝</strong> Այս տարբերակը նախատեսված է %(amazon)s-ի համար։ Եթե ցանկանում եք օգտագործել այլ Amazon կայք, ընտրեք այն վերևում։ <strong>ԿԱՐԵՎՈՐ:</strong> Մենք աջակցում ենք միայն Amazon.com-ին, ոչ թե այլ Amazon կայքերին։ Օրինակ, .de, .co.uk, .ca, ՉԵՆ աջակցվում։ Խնդրում ենք ՉԳՐԵԼ ձեր սեփական հաղորդագրությունը։ Մուտքագրեք ճշգրիտ գումարը՝ %(amount)s Նշեք, որ մենք պետք է կլորացնենք մեր վերավաճառողների կողմից ընդունված գումարներին (նվազագույնը %(minimum)s)։ Նվիրաբերեք կրեդիտ/դեբետ քարտի միջոցով, օգտագործելով Alipay հավելվածը (շատ հեշտ է կարգավորել): Տեղադրեք Alipay հավելվածը <a %(a_app_store)s>Apple App Store</a>-ից կամ <a %(a_play_store)s>Google Play Store</a>-ից: Գրանցվեք ձեր հեռախոսահամարով: Լրացուցիչ անձնական տվյալներ անհրաժեշտ չեն: <span %(style)s>1</span>Տեղադրեք Alipay հավելվածը Աջակցվում են՝ Visa, MasterCard, JCB, Diners Club և Discover: Տես <a %(a_alipay)s>այս ուղեցույցը</a> լրացուցիչ տեղեկությունների համար: <span %(style)s>2</span>Ավելացրեք բանկային քարտ Binance-ի միջոցով դուք գնում եք Bitcoin կրեդիտ/դեբետ քարտով կամ բանկային հաշվով, և ապա նվիրում եք այդ Bitcoin-ը մեզ։ Այդպես մենք կարող ենք մնալ անվտանգ և անանուն՝ ընդունելով ձեր նվիրատվությունը։ Binance-ը հասանելի է գրեթե բոլոր երկրներում և աջակցում է մեծամասնության բանկերին և վարկային/դեբետային քարտերին։ Սա ներկայումս մեր հիմնական առաջարկությունն է։ Մենք գնահատում ենք, որ ժամանակ եք տրամադրում այս մեթոդով նվիրատվություն կատարելու համար, քանի որ դա մեզ շատ է օգնում։ Կրեդիտ քարտերի, դեբետ քարտերի, Apple Pay-ի և Google Pay-ի համար մենք օգտագործում ենք “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>)։ Նրանց համակարգում մեկ “սուրճ” հավասար է $5-ի, ուստի ձեր նվիրատվությունը կկլորացվի 5-ի բազմապատիկի։ Նվիրաբերեք օգտագործելով Cash App։ Եթե ունեք Cash App, սա նվիրատվություն կատարելու ամենահեշտ միջոցն է։ Նշեք, որ %(amount)s-ից ցածր գործարքների համար Cash App-ը կարող է գանձել %(fee)s վճար։ %(amount)s կամ ավելի բարձր գործարքների համար՝ անվճար է։ Նվիրաբերել կրեդիտ կամ դեբետ քարտով։ Այս մեթոդը օգտագործում է կրիպտոարժույթի մատակարար՝ որպես միջանկյալ փոխարկում։ Սա կարող է մի փոքր շփոթեցնող լինել, ուստի խնդրում ենք օգտագործել այս մեթոդը միայն այն դեպքում, եթե այլ վճարման մեթոդները չեն աշխատում։ Այն նաև չի աշխատում բոլոր երկրներում։ Մենք չենք կարող ուղղակիորեն աջակցել վարկային/դեբետային քարտերին, քանի որ բանկերը չեն ցանկանում աշխատել մեզ հետ։ ☹ Այնուամենայնիվ, կան մի քանի եղանակներ վարկային/դեբետային քարտեր օգտագործելու համար՝ օգտագործելով այլ վճարային մեթոդներ. Կրիպտոարժույթով դուք կարող եք նվիրաբերել BTC, ETH, XMR և SOL օգտագործելով։ Օգտագործեք այս տարբերակը, եթե արդեն ծանոթ եք կրիպտոարժույթին։ Կրիպտոարժույթով դուք կարող եք նվիրաբերել BTC, ETH, XMR և այլ արժույթներով։ Կրիպտո արագ ծառայություններ Եթե դուք առաջին անգամ եք օգտագործում կրիպտո, մենք առաջարկում ենք օգտագործել %(options)s՝ Bitcoin (բնօրինակ և ամենաշատ օգտագործվող կրիպտոարժույթը) գնելու և նվիրաբերելու համար։ Նշենք, որ փոքր նվիրատվությունների դեպքում վարկային քարտի վճարները կարող են վերացնել մեր %(discount)s%% զեղչը, ուստի խորհուրդ ենք տալիս ավելի երկար բաժանորդագրություններ։ Նվիրաբերեք կրեդիտ/դեբետ քարտով, PayPal-ով կամ Venmo-ով։ Դուք կարող եք ընտրել դրանց միջև հաջորդ էջում։ Google Pay-ն և Apple Pay-ն նույնպես կարող են աշխատել։ Նշենք, որ փոքր նվիրատվությունների դեպքում վճարները բարձր են, ուստի խորհուրդ ենք տալիս ավելի երկար բաժանորդագրություններ։ PayPal US-ի միջոցով նվիրատվություն կատարելու համար մենք օգտագործելու ենք PayPal Crypto, որը թույլ է տալիս մեզ մնալ անանուն։ Մենք գնահատում ենք, որ ժամանակ եք տրամադրում այս մեթոդով նվիրատվություն կատարելու համար, քանի որ դա մեզ շատ է օգնում։ Նվիրաբերեք օգտագործելով PayPal։ Նվիրաբերեք ձեր սովորական PayPal հաշվի միջոցով։ Նվիրաբերեք Revolut-ի միջոցով։ Եթե ունեք Revolut, սա նվիրաբերելու ամենահեշտ միջոցն է։ Այս վճարման մեթոդը թույլ է տալիս առավելագույնը %(amount)s։ Խնդրում ենք ընտրել այլ տևողություն կամ վճարման մեթոդ։ Այս վճարման մեթոդը պահանջում է նվազագույն %(amount)s։ Խնդրում ենք ընտրել այլ տևողություն կամ վճարման մեթոդ։ Բինանս Coinbase Kraken Խնդրում ենք ընտրել վճարման մեթոդ։ «Ընդունեք տոռենտը»: ձեր օգտանունը կամ հաղորդագրությունը տոռենտի ֆայլի անվան մեջ <div %(div_months)s>ամեն 12 ամիս անդամակցության ընթացքում</div> Ձեր օգտանունը կամ անանուն հիշատակում վարկանիշում Վաղ հասանելիություն նոր ֆունկցիաներին Բացառիկ Telegram՝ կուլիսների հետևում թարմացումներով %(number)s արագ ներբեռնումներ օրական եթե դուք նվիրաբերեք այս ամիս! <a %(a_api)s>JSON API</a> հասանելիություն Լեգենդար կարգավիճակ մարդկության գիտելիքների և մշակույթի պահպանման գործում Նախորդ արտոնությունները, գումարած՝ Վաստակեք <strong>%(percentage)s%% բոնուսային ներբեռնումներ</strong> <a %(a_refer)s>ընկերներին հրավիրելով</a>: SciDB հոդվածներ <strong>անսահմանափակ</strong> առանց վավերացման Երբ հարցնում եք հաշվի կամ նվիրատվության հարցեր, ավելացրեք ձեր հաշվի ID-ն, սքրինշոթներ, անդորրագրեր, հնարավորինս շատ տեղեկատվություն։ Մենք ստուգում ենք մեր էլ. փոստը յուրաքանչյուր 1-2 շաբաթը մեկ, ուստի այս տեղեկատվությունը չներառելը կհետաձգի ցանկացած լուծում։ Ավելի շատ ներբեռնումներ ստանալու համար, <a %(a_refer)s>հրավիրեք ձեր ընկերներին</a>։ Մենք փոքր կամավորների թիմ ենք։ Մեզ կարող է տևել 1-2 շաբաթ պատասխանելու համար։ Նշենք, որ հաշվի անունը կամ նկարը կարող են տարօրինակ թվալ։ Անհանգստանալու կարիք չկա։ Այս հաշիվները կառավարվում են մեր նվիրատվության գործընկերների կողմից։ Մեր հաշիվները չեն կոտրվել։ Նվիրաբերել <span %(span_cost)s></span> <span %(span_label)s></span> 12 ամիս “%(tier_name)s”-ի համար 1 ամսով «%(tier_name)s» 24 ամիս “%(tier_name)s”-ի համար 3 ամսով “%(tier_name)s” 48 ամիս “%(tier_name)s” 6 ամսով “%(tier_name)s” 96 ամիս “%(tier_name)s”-ի համար Դուք դեռ կարող եք չեղարկել նվիրատվությունը վճարման ժամանակ։ Սեղմեք նվիրատվության կոճակը՝ այս նվիրատվությունը հաստատելու համար։ <strong>Կարևոր նշում:</strong> Կրիպտոարժույթների գները կարող են վայրիվերում ունենալ, երբեմն նույնիսկ մինչև 20%% մի քանի րոպեների ընթացքում։ Սա դեռ պակաս է, քան այն վճարները, որոնք մենք կրում ենք շատ վճարային մատակարարների հետ աշխատելիս, որոնք հաճախ գանձում են 50-60%% «ստվերային բարեգործության» հետ աշխատելու համար։ <u>Եթե դուք մեզ ուղարկեք անդորրագիրը սկզբնական գնով, որը դուք վճարել եք, մենք դեռ կվարկավորենք ձեր հաշիվը ընտրված անդամակցության համար</u> (քանի դեռ անդորրագիրը մի քանի ժամից ավելի հին չէ)։ Մենք իսկապես գնահատում ենք, որ դուք պատրաստ եք նման բաների հետ գործ ունենալ մեզ աջակցելու համար։ ❤️ ❌ Ինչ-որ բան սխալ է տեղի ունեցել։ Խնդրում ենք վերաբեռնել էջը և փորձել կրկին։ <span %(span_circle)s>1</span>Գնեք Bitcoin Paypal-ով <span %(span_circle)s>2</span>Փոխանցեք Bitcoin-ը մեր հասցեին ✅ Վերահղում դեպի նվիրատվության էջ… Նվիրաբերել Խնդրում ենք սպասել առնվազն <span %(span_hours)s>24 ժամ</span> (և թարմացնել այս էջը) նախքան մեզ հետ կապվելը։ Եթե ցանկանում եք նվիրատվություն կատարել (ցանկացած գումարով) առանց անդամակցության, ազատորեն օգտագործեք այս Monero (XMR) հասցեն՝ %(address)s։ Ձեր նվեր քարտը ուղարկելուց հետո, մեր ավտոմատ համակարգը կհաստատի այն մի քանի րոպեում։ Եթե դա չի աշխատում, փորձեք նորից ուղարկել ձեր նվեր քարտը (<a %(a_instr)s>հրահանգներ</a>)։ Եթե դա դեռ չի աշխատում, խնդրում ենք գրել մեզ էլ. փոստով, և Աննան ձեռքով կվերանայի այն (սա կարող է մի քանի օր տևել), և համոզվեք, որ նշել եք, եթե արդեն փորձել եք կրկին ուղարկել։ Օրինակ՝ Խնդրում ենք օգտագործել <a %(a_form)s>պաշտոնական Amazon.com ձևը</a>՝ մեզ նվեր քարտ ուղարկելու համար %(amount)s էլ. հասցեին։ «Դեպի» ստացողի էլ. փոստի հասցեն՝ հետևյալ ձևով. Amazon նվեր քարտ Մենք չենք կարող ընդունել այլ մեթոդներով նվեր քարտեր, <strong>միայն ուղարկված Amazon.com-ի պաշտոնական ձևից</strong>։ Մենք չենք կարող վերադարձնել ձեր նվեր քարտը, եթե չօգտագործեք այս ձևը։ Օգտագործել միայն մեկ անգամ։ Հատուկ ձեր հաշվի համար, մի կիսվեք։ Սպասում ենք նվեր քարտին… (թարմացրեք էջը՝ ստուգելու համար) Բացեք <a %(a_href)s>QR կոդի նվիրատվության էջը</a>: Սկանավորեք QR կոդը Alipay հավելվածով, կամ սեղմեք կոճակը՝ բացելու Alipay հավելվածը: Խնդրում ենք համբերել; էջը կարող է որոշ ժամանակ պահանջել բեռնվելու համար, քանի որ այն Չինաստանում է։ <span %(style)s>3</span>Կատարեք նվիրատվություն (սկանավորեք QR կոդը կամ սեղմեք կոճակը) Գնեք PYUSD մետաղադրամը PayPal-ով Գնել Bitcoin (BTC) Cash App-ով Գնեք մի փոքր ավելին (մենք խորհուրդ ենք տալիս %(more)s ավելին) քան այն գումարը, որը նվիրաբերում եք (%(amount)s), որպեսզի ծածկեք գործարքի վճարները։ Դուք կպահեք մնացածը։ Գնացեք Cash App-ի «Bitcoin» (BTC) էջը։ Փոխանցեք Bitcoin-ը մեր հասցեին Փոքր նվիրատվությունների համար (մինչև $25), գուցե անհրաժեշտ լինի օգտագործել Rush կամ Priority։ Սեղմեք «Send bitcoin» կոճակը՝ «հետկանչ» կատարելու համար։ Փոխեք դոլարները BTC-ի՝ սեղմելով %(icon)s պատկերակը։ Մուտքագրեք BTC գումարը ներքևում և սեղմեք «Send»։ Տեսեք <a %(help_video)s>այս տեսանյութը</a> եթե խճճվեք։ Արագ ծառայությունները հարմար են, բայց գանձում են ավելի բարձր վճարներ: Դուք կարող եք օգտագործել սա կրիպտո փոխանակման փոխարեն, եթե ցանկանում եք արագ կատարել ավելի մեծ նվիրատվություն և չեք մտահոգվում $5-10 վճարով: Համոզվեք, որ ուղարկեք նվիրատվության էջում նշված ճշգրիտ կրիպտո գումարը, ոչ թե $USD գումարը: Հակառակ դեպքում վճարը կհանվի, և մենք չենք կարող ավտոմատ կերպով մշակել ձեր անդամակցությունը: Երբեմն հաստատումը կարող է տևել մինչև 24 ժամ, ուստի համոզվեք, որ թարմացնեք այս էջը (նույնիսկ եթե այն ժամկետանց է): Կրեդիտային / դեբետային քարտի հրահանգներ Նվիրաբերեք մեր կրեդիտ / դեբետ քարտի էջի միջոցով Որոշ քայլեր նշում են կրիպտո դրամապանակներ, բայց մի անհանգստացեք, ձեզ հարկավոր չէ սովորել որևէ բան կրիպտոյի մասին։ %(coin_name)s հրահանգներ Այս QR ծածկագիրը սկանավորեք ձեր Crypto Wallet հավելվածի հետ `վճարման մանրամասները արագ լրացնելու համար Սկան QR կոդը վճարելու համար Մենք աջակցում ենք միայն կրիպտո մետաղադրամների ստանդարտ տարբերակներին, ոչ էկզոտիկ ցանցերին կամ մետաղադրամների տարբերակներին։ Գործարքը հաստատելու համար կարող է տևել մինչև մեկ ժամ՝ կախված մետաղադրամից։ Նվիրաբերեք %(amount)s <a %(a_page)s>այս էջում</a>։ Այս նվիրատվության ժամկետը լրացել է։ Խնդրում ենք չեղարկել և ստեղծել նոր։ Եթե դուք արդեն վճարել եք՝ Այո, ես ուղարկել եմ իմ անդորրագիրը Եթե կրիպտո փոխարժեքը տատանվել է գործարքի ընթացքում, համոզվեք, որ կցեք բնօրինակ փոխարժեքը ցույց տվող անդորրագիրը։ Մենք շատ ենք գնահատում, որ դուք օգտագործում եք կրիպտո, դա մեզ շատ է օգնում! ❌ Ինչ-որ բան սխալ է։ Խնդրում ենք վերալիցքավորել էջը և փորձել կրկին։ <span %(span_circle)s>%(circle_number)s</span>Ուղարկեք մեզ անդորրագրի պատճենը Եթե հանդիպեք որևէ խնդրի, խնդրում ենք կապ հաստատել մեզ հետ %(email)s հասցեով և ներառել հնարավորինս շատ տեղեկատվություն (օրինակ՝ սքրինշոթներ)։ ✅ Շնորհակալություն ձեր նվիրատվության համար։ Աննան ձեռքով կակտիվացնի ձեր անդամակցությունը մի քանի օրվա ընթացքում։ Ուղարկեք անդորրագիրը կամ սքրինշոթը ձեր անձնական հաստատման հասցեին. Երբ ուղարկեք ձեր անդորրագիրը, սեղմեք այս կոճակը, որպեսզի Աննան կարողանա ձեռքով ստուգել այն (սա կարող է մի քանի օր տևել)։ Ուղարկեք անդորրագիր կամ սքրինշոթ ձեր անձնական հաստատման հասցեին։ Խնդրում ենք չօգտագործել այս էլեկտրոնային հասցեն ձեր PayPal նվիրատվության համար։ Չեղարկել Այո, խնդրում եմ չեղարկել Վստա՞հ եք, որ ցանկանում եք չեղարկել: Մի չեղարկեք, եթե արդեն վճարել եք: ❌ Ինչ-որ բան սխալ է։ Խնդրում ենք վերալիցքավորել էջը և նորից փորձել։ Կատարել նոր նվիրատվություն ✅ Ձեր նվիրատվությունը չեղարկվել է։ Ամսաթիվ՝ %(date)s Նույնականացուցիչ՝ %(id)s Վերադասավորել Կարգավիճակ՝ <span %(span_label)s>%(label)s</span> Ընդհանուր: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ամիս %(duration)s ամիսների համար, ներառյալ %(discounts)s%% զեղչ)</span> Ընդհանուր՝ %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ամիս %(duration)s ամիսների համար)</span> 1. Մուտքագրեք ձեր էլ․ փոստը։ 2. Ընտրեք ձեր վճարման մեթոդը։ 3. Կրկին ընտրեք ձեր վճարման մեթոդը։ 4. Ընտրեք «Ինքնահոս» դրամապանակ։ 5. Սեղմեք “Հաստատում եմ սեփականությունը”։ 6. Դուք պետք է ստանաք էլեկտրոնային նամակ-հաշիվ։ Խնդրում ենք ուղարկել այն մեզ, և մենք հնարավորինս շուտ կհաստատենք ձեր նվիրատվությունը։ (կարող եք չեղարկել և ստեղծել նոր նվիրատվություն) Վճարման հրահանգները այժմ հնացած են։ Եթե ցանկանում եք կատարել մեկ այլ նվիրատվություն, օգտագործեք վերևի “Reorder” կոճակը։ Դուք արդեն վճարել եք։ Եթե ցանկանում եք վերանայել վճարման հրահանգները, սեղմեք այստեղ՝ Ցույց տալ հին վճարման հրահանգները Եթե նվիրատվության էջը արգելափակվում է, փորձեք այլ ինտերնետ կապ (օրինակ՝ VPN կամ հեռախոսի ինտերնետ)։ Ցավոք, Alipay էջը հաճախ հասանելի է միայն <strong>Չինաստանի մայրցամաքից</strong>։ Հնարավոր է, որ անհրաժեշտ լինի ժամանակավորապես անջատել ձեր VPN-ը կամ օգտագործել VPN դեպի Չինաստանի մայրցամաք (կամ Հոնկոնգը նույնպես երբեմն աշխատում է)։ <span %(span_circle)s>1</span>Նվիրաբերել Alipay-ով Նվիրաբերեք ընդհանուր գումարը %(total)s օգտագործելով <a %(a_account)s>այս Alipay հաշիվը</a> Alipay-ի հրահանգներ <span %(span_circle)s>1</span>Փոխանցել մեր կրիպտո հաշիվներից մեկին Նվիրաբերեք ընդհանուր գումարը %(total)s այս հասցեներից մեկին: Կրիպտո հրահանգներ Հետևեք հրահանգներին՝ Bitcoin (BTC) գնելու համար։ Ձեզ անհրաժեշտ է գնել միայն այն գումարը, որը ցանկանում եք նվիրաբերել, %(total)s։ Մուտքագրեք մեր Bitcoin (BTC) հասցեն որպես ստացող, և հետևեք հրահանգներին՝ ձեր %(total)s նվիրատվությունը ուղարկելու համար: <span %(span_circle)s>1</span>Նվիրաբերել Pix-ի միջոցով Նվիրաբերել ընդհանուր %(total)s գումարը օգտագործելով <a %(a_account)s>այս Pix հաշիվը Pix հրահանգներ <span %(span_circle)s>1</span>Նվիրաբերել WeChat-ով Նվիրաբերեք ընդհանուր %(total)s գումարը օգտագործելով <a %(a_account)s>այս WeChat հաշիվը</a> WeChat-ի հրահանգներ Օգտագործեք հետևյալ «կրեդիտ քարտից Bitcoin» արագ ծառայություններից որևէ մեկը, որոնք տևում են ընդամենը մի քանի րոպե. BTC / Bitcoin հասցե (արտաքին դրամապանակ): BTC / Bitcoin գումար: Լրացրեք հետևյալ տվյալները ձևում. Եթե այս տեղեկատվությունը հնացած է, խնդրում ենք մեզ տեղեկացնել էլեկտրոնային փոստով։ Խնդրում ենք օգտագործել այս <span %(underline)s>ճշգրիտ գումարը</span>։ Ձեր ընդհանուր արժեքը կարող է ավելի բարձր լինել վարկային քարտի վճարների պատճառով։ Փոքր գումարների դեպքում սա կարող է ավելի շատ լինել, քան մեր զեղչը, ցավոք։ (նվազագույնը՝ %(minimum)s, առաջին գործարքի համար հաստատում չի պահանջվում) (նվազագույնը՝ %(minimum)s) (նվազագույնը՝ %(minimum)s, առաջին գործարքի համար հաստատում չի պահանջվում) (նվազագույնը՝ %(minimum)s, առաջին գործարքի համար հաստատում չի պահանջվում) (նվազագույնը՝ %(minimum)s) (նվազագույնը՝ %(minimum)s կախված երկրից, առաջին գործարքի համար հաստատում չի պահանջվում) Հետևեք հրահանգներին՝ PYUSD մետաղադրամ (PayPal USD) գնելու համար։ Գնեք մի փոքր ավելին (մենք խորհուրդ ենք տալիս %(more)s ավելին) քան այն գումարը, որը նվիրաբերում եք (%(amount)s), որպեսզի ծածկեք գործարքի վճարները։ Դուք կպահեք մնացածը։ Գնացեք “PYUSD” էջը ձեր PayPal հավելվածում կամ կայքում։ Սեղմեք “Փոխանցել” կոճակը %(icon)s, և ապա “Ուղարկել”։ Թարմացնել կարգավիճակը Ժամանակաչափը վերականգնելու համար պարզապես ստեղծեք նոր նվիրատվություն։ Խնդրում ենք օգտագործել ստորև նշված BTC գումարը, <em>ՈՉ</em> եվրո կամ դոլար, հակառակ դեպքում մենք չենք ստանա ճիշտ գումարը և չենք կարող ավտոմատ կերպով հաստատել ձեր անդամակցությունը։ Գնել Bitcoin (BTC) Revolut-ով Գնեք մի փոքր ավելին (մենք խորհուրդ ենք տալիս %(more)s ավելին) քան այն գումարը, որը նվիրաբերում եք (%(amount)s), որպեսզի ծածկեք գործարքի վճարները։ Դուք կպահեք մնացածը։ Գնացեք Revolut-ի «Crypto» էջը՝ Bitcoin (BTC) գնելու համար։ Փոխանցեք Bitcoin-ը մեր հասցեին Փոքր նվիրատվությունների համար (մինչև $25) գուցե անհրաժեշտ լինի օգտագործել Rush կամ Priority։ Սեղմեք «Send bitcoin» կոճակը՝ «հետկանչ» կատարելու համար։ Փոխեք եվրոները BTC-ի՝ սեղմելով %(icon)s պատկերակը։ Մուտքագրեք BTC գումարը ներքևում և սեղմեք «Send»։ Տեսեք <a %(help_video)s>այս տեսանյութը</a> եթե խճճվեք։ Կարգավիճակ: 1 2 Քայլ առ քայլ ուղեցույց Տեսեք քայլ առ քայլ ուղեցույցը ստորև։ Հակառակ դեպքում կարող եք արգելափակվել այս հաշվի մուտքից! Եթե դեռ չեք արել, գրեք ձեր գաղտնի բանալին մուտք գործելու համար: Շնորհակալություն ձեր նվիրատվության համար! Մնացած ժամանակը՝ Նվիրատվություն Փոխանցել %(amount)s-ը %(account)s-ին Սպասում է հաստատման (թարմացրեք էջը ստուգելու համար)… Սպասում է փոխանցմանը (թարմացրեք էջը ստուգելու համար)… Ավելի վաղ Վերջին 24 ժամվա ընթացքում արագ ներբեռնումները հաշվում են օրական սահմանաչափի մեջ։ Ներբեռնումները արագ գործընկեր սերվերներից նշված են %(icon)s-ով։ Վերջին 18 ժամ Դեռևս ֆայլեր չեն ներբեռնվել։ Ներբեռնված ֆայլերը հանրային չեն ցուցադրվում։ Բոլոր ժամերը՝ UTC։ Ներբեռնված ֆայլեր Եթե ներբեռնեք ֆայլը ինչպես արագ, այնպես էլ դանդաղ ներբեռնումներով, այն կհայտնվի երկու անգամ։ Մի անհանգստացեք շատ, կան շատ մարդիկ, ովքեր ներբեռնում են մեր կողմից հղված կայքերից, և շատ հազվադեպ է խնդիր առաջանում: Այնուամենայնիվ, անվտանգ լինելու համար մենք խորհուրդ ենք տալիս օգտագործել VPN (վճարովի), կամ <a %(a_tor)s>Tor</a> (անվճար): Ես ներբեռնել եմ Ջորջ Օրուելի «1984»-ը, ոստիկանությունը կգա՞ իմ դռան մոտ։ Դուք եք Աննան։ Ո՞վ է Աննան։ Մենք ունենք մեկ կայուն JSON API անդամների համար, արագ ներբեռնման URL ստանալու համար՝ <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (փաստաթղթավորումը JSON-ի ներսում)։ Այլ օգտագործման դեպքերի համար, ինչպիսիք են մեր բոլոր ֆայլերի միջով անցնելը, հարմարեցված որոնում կառուցելը և այլն, մենք խորհուրդ ենք տալիս <a %(a_generate)s>ստեղծել</a> կամ <a %(a_download)s>ներբեռնել</a> մեր ElasticSearch և MariaDB տվյալների բազաները։ Հում տվյալները կարելի է ձեռքով ուսումնասիրել <a %(a_explore)s>JSON ֆայլերի միջոցով</a>։ Մեր հում torrents-ի ցանկը կարող է ներբեռնվել նաև որպես <a %(a_torrents)s>JSON</a>: Ունե՞ք API։ Մենք այստեղ հեղինակային իրավունքով պաշտպանված նյութեր չենք հյուրընկալում։ Մենք որոնման համակարգ ենք և միայն ինդեքսավորում ենք արդեն հանրային հասանելի մետատվյալները։ Այս արտաքին աղբյուրներից ներբեռնելիս խորհուրդ ենք տալիս ստուգել ձեր իրավասության օրենքները՝ կապված թույլատրելիի հետ։ Մենք պատասխանատվություն չենք կրում ուրիշների կողմից հյուրընկալված բովանդակության համար։ Եթե բողոքներ ունեք այստեղ տեսածի վերաբերյալ, լավագույն տարբերակը կապ հաստատել է սկզբնական կայքի հետ։ Մենք պարբերաբար ներմուծում ենք նրանց փոփոխությունները մեր տվյալների բազայում։ Եթե իսկապես կարծում եք, որ ունեք վավեր DMCA բողոք, որը մենք պետք է արձագանքենք, խնդրում ենք լրացնել <a %(a_copyright)s>DMCA / Հեղինակային իրավունքի պահանջի ձևը</a>։ Մենք լրջորեն ենք վերաբերվում ձեր բողոքներին և կպատասխանենք հնարավորինս շուտ։ Ինչպե՞ս զեկուցել հեղինակային իրավունքի խախտման մասին։ Ահա մի քանի գրքեր, որոնք հատուկ նշանակություն ունեն ստվերային գրադարանների և թվային պահպանման աշխարհի համար․ Որոնք են ձեր սիրելի գրքերը? Մենք ցանկանում ենք հիշեցնել, որ մեր բոլոր կոդերն ու տվյալները բաց կոդով են։ Սա եզակի է նման նախագծերի համար՝ մենք տեղյակ չենք որևէ այլ նախագծի, որը նման մեծածավալ կատալոգով լիովին բաց կոդով է։ Մենք շատ ուրախ կլինենք, եթե որևէ մեկը, ով կարծում է, որ մենք վատ ենք վարում մեր նախագիծը, վերցնի մեր կոդերն ու տվյալները և ստեղծի իր սեփական ստվերային գրադարանը։ Մենք սա չենք ասում չարությամբ կամ ինչ-որ բանի համար՝ մենք իսկապես կարծում ենք, որ սա հիանալի կլինի, քանի որ դա կբարձրացնի բոլորի մակարդակը և ավելի լավ կպահպանի մարդկության ժառանգությունը։ Ես ատում եմ, թե ինչպես եք վարում այս նախագիծը։ Մենք կցանկանայինք, որ մարդիկ ստեղծեն <a %(a_mirrors)s>հայելիներ</a>, և մենք ֆինանսապես կաջակցենք դրան։ Ինչպե՞ս կարող եմ օգնել։ Այո, մենք ունենք։ Մեր մետատվյալների հավաքագրման ոգեշնչումը Աարոն Սվարցի նպատակը՝ «մեկ վեբ էջ յուրաքանչյուր երբևէ հրատարակված գրքի համար», որի համար նա ստեղծեց <a %(a_openlib)s>Open Library</a>-ը։ Այդ նախագիծը լավ է ընթանում, բայց մեր յուրահատուկ դիրքը թույլ է տալիս մեզ ստանալ մետատվյալներ, որոնք նրանք չեն կարող։ Մեկ այլ ոգեշնչում էր մեր ցանկությունը իմանալու <a %(a_blog)s>թե քանի գիրք կա աշխարհում</a>, որպեսզի կարողանանք հաշվարկել, թե քանի գիրք դեռ պետք է փրկենք։ Դուք հավաքում եք մետատվյալներ՞ Նշեք, որ mhut.org-ը արգելափակում է որոշ IP հասցեներ, ուստի հնարավոր է անհրաժեշտ լինի VPN։ <strong>Android:</strong> Սեղմեք վերևի աջ անկյունում գտնվող երեք կետանի մենյուի վրա և ընտրեք «Ավելացնել գլխավոր էկրանին»։ <strong>iOS:</strong> Սեղմեք «Share» կոճակը ներքևում և ընտրեք «Add to Home Screen»։ Մենք չունենք պաշտոնական բջջային հավելված, բայց դուք կարող եք տեղադրել այս կայքը որպես հավելված։ Դուք ունե՞ք բջջային հավելված։ Խնդրում ենք դրանք ուղարկել <a %(a_archive)s>Internet Archive</a>։ Նրանք դրանք պատշաճ կերպով կպահպանեն։ Ինչպե՞ս նվիրաբերել գրքեր կամ այլ ֆիզիկական նյութեր։ Ինչպե՞ս կարող եմ գրքեր խնդրել։ <a %(a_blog)s>Աննայի բլոգը</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — կանոնավոր թարմացումներ <a %(a_software)s>Աննայի Ծրագրակազմը</a> — մեր բաց կոդով ծրագիրը <a %(a_datasets)s>Տվյալների հավաքածուներ</a> — տվյալների մասին <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — այլընտրանքային դոմեններ Կա՞ն ավելի շատ ռեսուրսներ Աննայի արխիվի մասին։ <a %(a_translate)s>Թարգմանել Աննայի Ծրագրում</a> — մեր թարգմանական համակարգը <a %(a_wikipedia)s>Վիքիպեդիա</a> — ավելին մեր մասին (խնդրում ենք օգնել թարմացնել այս էջը կամ ստեղծել մեկը ձեր լեզվով!) Ընտրեք ձեր նախընտրած կարգավորումները, թողեք որոնման դաշտը դատարկ, սեղմեք «Որոնում», և ապա էջանիշ դրեք էջը ձեր զննարկչի էջանիշների գործառույթի միջոցով։ Ինչպե՞ս պահպանեմ իմ որոնման կարգավորումները։ Մենք ողջունում ենք անվտանգության հետազոտողներին՝ մեր համակարգերում խոցելիություններ որոնելու համար: Մենք մեծ կողմնակիցներ ենք պատասխանատու բացահայտման: Կապվեք մեզ հետ <a %(a_contact)s>այստեղ</a>: Ներկայումս մենք չենք կարող պարգևատրել սխալների հայտնաբերման համար, բացառությամբ այն խոցելիությունների, որոնք ունեն <a %(a_link)s>մեր անանունությունը վտանգելու պոտենցիալ</a>, որոնց համար մենք առաջարկում ենք $10k-50k միջակայքի պարգևներ։ Մենք կցանկանայինք ապագայում առաջարկել ավելի լայն շրջանակ սխալների հայտնաբերման համար։ Խնդրում ենք նկատի ունենալ, որ սոցիալական ինժեներիայի հարձակումները դուրս են մեր շրջանակից։ Եթե դուք հետաքրքրված եք հարձակողական անվտանգությամբ և ցանկանում եք օգնել արխիվացնել աշխարհի գիտելիքն ու մշակույթը, անպայման կապվեք մեզ հետ։ Կան բազմաթիվ եղանակներ, որոնցով դուք կարող եք օգնել։ Դուք ունե՞ք պատասխանատու բացահայտման ծրագիր: Մենք բառացիորեն չունենք բավարար ռեսուրսներ՝ աշխարհում բոլորին բարձր արագությամբ ներբեռնումներ տրամադրելու համար, որքան էլ որ ցանկանանք։ Եթե հարուստ բարերար կցանկանա աջակցել մեզ և ապահովել դա, դա անհավանական կլինի, բայց մինչ այդ, մենք փորձում ենք մեր լավագույնը։ Մենք ոչ առևտրային նախագիծ ենք, որը հազիվ է կարողանում գոյատևել նվիրատվությունների միջոցով։ Ահա թե ինչու մենք ներդրեցինք երկու համակարգ անվճար ներբեռնումների համար՝ մեր գործընկերների հետ. ընդհանուր սերվերներ դանդաղ ներբեռնումներով և մի փոքր ավելի արագ սերվերներ սպասման ցուցակով (նվազեցնելու համար միաժամանակ ներբեռնողների քանակը)։ Մենք նաև ունենք <a %(a_verification)s>զննարկչի ստուգում</a> մեր դանդաղ ներբեռնումների համար, քանի որ հակառակ դեպքում բոտերը և քերիչները կչարաշահեն դրանք, ինչը կդանդաղեցնի իրական օգտատերերի համար։ Նշենք, որ Tor Browser օգտագործելիս, հնարավոր է, որ անհրաժեշտ լինի կարգավորել ձեր անվտանգության կարգավորումները: Ամենացածր տարբերակում, որը կոչվում է «Ստանդարտ», Cloudflare turnstile մարտահրավերը հաջողվում է: Ավելի բարձր տարբերակներում, որոնք կոչվում են «Ավելի անվտանգ» և «Ամենաանվտանգ», մարտահրավերը ձախողվում է: Մեծ ֆայլերի դեպքում երբեմն դանդաղ ներբեռնումները կարող են ընդհատվել։ Մենք խորհուրդ ենք տալիս օգտագործել ներբեռնման կառավարիչ (օրինակ՝ JDownloader)՝ մեծ ներբեռնումները ավտոմատ վերսկսելու համար։ Ինչու՞ են դանդաղ ներբեռնումները այդքան դանդաղ։ Հաճախ տրվող հարցեր (FAQ) Օգտագործեք <a %(a_list)s>թորրենտների ցուցակի գեներատորը</a>՝ ստեղծելու համար թորրենտների ցուցակ, որոնք առավել կարիք ունեն թորրենտացման, ձեր պահեստային տարածքի սահմաններում։ Այո, տես <a %(a_llm)s>LLM տվյալների</a> էջը։ Շատ տոռենտներ պարունակում են ֆայլերը անմիջապես, ինչը նշանակում է, որ դուք կարող եք տոռենտ հաճախորդներին հրահանգել ներբեռնել միայն անհրաժեշտ ֆայլերը։ Որոշելու համար, թե որ ֆայլերը ներբեռնել, դուք կարող եք <a %(a_generate)s>ստեղծել</a> մեր մետատվյալները, կամ <a %(a_download)s>ներբեռնել</a> մեր ElasticSearch և MariaDB տվյալների բազաները։ Ցավոք, մի շարք տոռենտ հավաքածուներ պարունակում են .zip կամ .tar ֆայլեր արմատում, այս դեպքում դուք պետք է ներբեռնեք ամբողջ տոռենտը, նախքան առանձին ֆայլեր ընտրելու հնարավորությունը։ Դեռևս չկան հեշտ օգտագործվող գործիքներ տոռենտների ֆիլտրացման համար, բայց մենք ողջունում ենք ներդրումները։ (Այնուամենայնիվ, մենք ունենք <a %(a_ideas)s>որոշ գաղափարներ</a> վերջին դեպքի համար։) Երկար պատասխան․ Կարճ պատասխան՝ ոչ հեշտությամբ։ Մենք փորձում ենք նվազագույնի հասցնել կրկնօրինակումը կամ համընկնումը այս ցուցակի տոռենտների միջև, բայց դա միշտ չէ, որ հնարավոր է, և մեծապես կախված է աղբյուր գրադարանների քաղաքականությունից։ Գրադարանների համար, որոնք իրենց տոռենտներն են թողարկում, դա մեր վերահսկողությունից դուրս է։ Աննայի արխիվի կողմից թողարկված տոռենտների համար մենք կրկնօրինակում ենք միայն MD5 հեշի հիման վրա, ինչը նշանակում է, որ նույն գրքի տարբեր տարբերակները չեն կրկնօրինակվում։ Այո: Սրանք իրականում PDF և EPUB ֆայլեր են, պարզապես շատ թորրենտներում չունեն ընդլայնում։ Կան երկու տեղեր, որտեղ կարող եք գտնել թորրենտ ֆայլերի մետատվյալները, ներառյալ ֆայլերի տեսակները/ընդլայնումները՝ 1. Յուրաքանչյուր հավաքածու կամ թողարկում ունի իր սեփական մետատվյալները։ Օրինակ, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a>-ները ունեն համապատասխան մետատվյալների բազա, որը հյուրընկալված է Libgen.rs կայքում։ Մենք սովորաբար հղում ենք կատարում համապատասխան մետատվյալների ռեսուրսներին յուրաքանչյուր հավաքածուի <a %(a_datasets)s>dataset էջից</a>։ 2. Մենք խորհուրդ ենք տալիս <a %(a_generate)s>ստեղծել</a> կամ <a %(a_download)s>ներբեռնել</a> մեր ElasticSearch և MariaDB տվյալների բազաները։ Դրանք պարունակում են յուրաքանչյուր գրառման քարտեզագրումը Anna’s Archive-ում իր համապատասխան տոռենտ ֆայլերին (եթե առկա է), “torrent_paths” դաշտում ElasticSearch JSON-ում։ Որոշ տոռենտ հաճախորդներ չեն աջակցում մեծ կտորների չափսերին, որոնք ունեն մեր շատ տոռենտներ (նորերի համար մենք այլևս դա չենք անում՝ չնայած դա վավեր է ըստ բնութագրերի)։ Այսպիսով, փորձեք այլ հաճախորդ, եթե հանդիպեք այս խնդրին, կամ բողոքեք ձեր տոռենտ հաճախորդի ստեղծողներին։ Ես կցանկանայի օգնել սերմանել, բայց չունեմ շատ սկավառակի տարածք։ Տոռենտները շատ դանդաղ են. կարո՞ղ եմ տվյալները ուղղակիորեն ներբեռնել ձեզնից։ Կարո՞ղ եմ ներբեռնել միայն ֆայլերի մի ենթաբաժինը, օրինակ՝ միայն որոշակի լեզու կամ թեմա։ Ինչպե՞ս եք կարգավորում կրկնօրինակները թոռենտներում։ Կարո՞ղ եմ ստանալ torrent ցուցակը JSON ձևաչափով։ Ես չեմ տեսնում PDF-ներ կամ EPUB-ներ տոռենտներում, միայն բինար ֆայլեր։ Ի՞նչ անեմ։ Ինչո՞ւ իմ տոռենտ հաճախորդը չի կարող բացել ձեր որոշ տոռենտ ֆայլեր / մագնիսային հղումներ։ Torrents ՀՏՀ Ինչպե՞ս կարող եմ նոր գրքեր վերբեռնել: Խնդրում ենք տեսնել <a %(a_href)s>այս հիանալի նախագիծը</a>։ Դուք ունե՞ք աշխատանքի մոնիտոր։ Ի՞նչ է Աննայի Արխիվը։ Դարձեք անդամ՝ արագ ներբեռնումներ օգտագործելու համար։ Մենք այժմ աջակցում ենք Amazon նվեր քարտեր, կրեդիտ և դեբիտ քարտեր, կրիպտո, Alipay և WeChat։ Այսօր արագ ներբեռնումների քանակը սպառված է։ Մուտք Վերջին 30 օրվա ընթացքում ժամային ներբեռնումներ։ Ժամային միջին՝ %(hourly)s։ Օրական միջին՝ %(daily)s։ Մենք աշխատում ենք գործընկերների հետ, որպեսզի մեր հավաքածուները հեշտությամբ և անվճար հասանելի լինեն բոլորին։ Մենք հավատում ենք, որ յուրաքանչյուրն ունի իրավունք մարդկության հավաքական իմաստությանը։ Եվ <a %(a_search)s>ոչ հեղինակների հաշվին</a>։ Աննայի Արխիվում օգտագործվող տվյալների հավաքածուները ամբողջությամբ բաց են և կարող են զանգվածաբար արտացոլվել տոռենտների միջոցով։ <a %(a_datasets)s>Իմացեք ավելին…</a> Երկարաժամկետ արխիվ Ամբողջական տվյալների բազա Փնտրել Գրքեր, հոդվածներ, ամսագրեր, կոմիքսներ, գրադարանային գրառումներ, մետատվյալներ, … Մեր բոլոր <a %(a_code)s>ծածկագիրը</a> և <a %(a_datasets)s>տվյալները</a> ամբողջությամբ բաց կոդով են։ <span %(span_anna)s>Աննայի Արխիվը</span> ոչ առևտրային նախագիծ է երկու նպատակներով՝ <li><strong>Պահպանում․</strong> Մարդկության բոլոր գիտելիքների և մշակույթի պահպանում։</li><li><strong>Մուտք․</strong> Այս գիտելիքների և մշակույթի հասանելի դարձնելը աշխարհի ցանկացած մարդու համար։</li> Մենք ունենք աշխարհի ամենամեծ բարձրորակ տեքստային տվյալների հավաքածուն։ <a %(a_llm)s>Իմացեք ավելին…</a> LLM ուսումնական տվյալներ 🪩 Հայելիներ․ կամավորների կոչ Եթե դուք վարում եք բարձր ռիսկային անանուն վճարային պրոցեսոր, խնդրում ենք կապ հաստատել մեզ հետ։ Մենք նաև փնտրում ենք մարդկանց, ովքեր ցանկանում են տեղադրել նրբագեղ փոքր գովազդներ։ Բոլոր եկամուտները գնում են մեր պահպանման ջանքերին։ Պահպանում Մենք գնահատում ենք, որ մենք պահպանել ենք աշխարհի գրքերի մոտ <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%%։ Մենք պահպանում ենք գրքեր, հոդվածներ, կոմիքսներ, ամսագրեր և ավելին՝ հավաքելով այս նյութերը տարբեր <a href="https://en.wikipedia.org/wiki/Shadow_library">ստվերային գրադարաններից</a>, պաշտոնական գրադարաններից և այլ հավաքածուներից մեկ վայրում։ Այս բոլոր տվյալները պահպանվում են հավերժ՝ հեշտացնելով դրանց զանգվածային կրկնօրինակումը՝ օգտագործելով տոռենտներ, ինչը հանգեցնում է բազմաթիվ պատճենների ամբողջ աշխարհում։ Որոշ ստվերային գրադարաններ արդեն իսկ անում են դա (օրինակ՝ Sci-Hub, Library Genesis), մինչդեռ Anna’s Archive-ը «ազատում» է այլ գրադարաններ, որոնք զանգվածային բաշխում չեն առաջարկում (օրինակ՝ Z-Library) կամ ընդհանրապես ստվերային գրադարաններ չեն (օրինակ՝ Internet Archive, DuXiu)։ Այս լայն տարածումը, համակցված բաց կոդով, մեր կայքը դարձնում է կայուն և ապահովում է մարդկության գիտելիքների և մշակույթի երկարաժամկետ պահպանությունը։ Իմացեք ավելին <a href="/datasets">մեր տվյալների հավաքածուների</a> մասին։ Եթե դուք <a %(a_member)s>անդամ եք</a>, զննարկչի ստուգումը պարտադիր չէ։ 🧬&nbsp;SciDB-ը Sci-Hub-ի շարունակությունն է։ SciDB Բացել DOI Sci-Hub-ը <a %(a_paused)s>դադարեցրել է</a> նոր հոդվածների վերբեռնումը։ Ուղղակի մուտք դեպի %(count)s գիտական հոդվածներ 🧬&nbsp;SciDB-ն Sci-Hub-ի շարունակությունն է՝ իր ծանոթ ինտերֆեյսով և PDF-ների անմիջական դիտմամբ: Մուտքագրեք ձեր DOI-ն դիտելու համար։ Մենք ունենք Sci-Hub-ի ամբողջ հավաքածուն, ինչպես նաև նոր հոդվածներ։ Շատերը կարող են դիտվել անմիջապես ծանոթ ինտերֆեյսով, որը նման է Sci-Hub-ին։ Որոշները կարող են ներբեռնվել արտաքին աղբյուրներից, այդ դեպքում մենք ցույց ենք տալիս հղումներ դրանց։ Դուք կարող եք մեծապես օգնել սերմանելով տոռենտներ։ <a %(a_torrents)s>Իմացեք ավելին…</a> >%(count)s սերմացուներ <%(count)s սերմացնողներ %(count_min)s–%(count_max)s սիդերներ 🤝 Փնտրում ենք կամավորներ Որպես ոչ առևտրային, բաց կոդով նախագիծ, մենք միշտ փնտրում ենք մարդկանց, ովքեր կօգնեն։ IPFS ներբեռնումներ Ցուցակ %(by)s-ով, ստեղծված <span %(span_time)s>%(time)s</span> Պահպանել ❌ Ինչ-որ բան սխալ է։ Խնդրում ենք փորձել կրկին։ ✅ Պահպանված է։ Խնդրում ենք վերաբեռնել էջը։ Ցանկը դատարկ է։ խմբագրել Ավելացրեք կամ հեռացրեք այս ցուցակից՝ գտնելով ֆայլը և բացելով «Ցուցակներ» ներդիրը։ Ցանկ Ինչպես կարող ենք օգնել Կրկնությունների հեռացում (դեդուպլիկացիա) Տեքստի և մետատվյալների արդյունահանում OCR Մենք կարող ենք ապահովել բարձր արագությամբ մուտք մեր ամբողջական հավաքածուներին, ինչպես նաև չհրապարակված հավաքածուներին։ Սա ձեռնարկության մակարդակի մուտք է, որը մենք կարող ենք տրամադրել տասնյակ հազարավոր ԱՄՆ դոլարների նվիրատվությունների դիմաց։ Մենք նաև պատրաստ ենք փոխանակել սա բարձրորակ հավաքածուների հետ, որոնք մենք դեռ չունենք։ Մենք կարող ենք վերադարձնել ձեզ, եթե կարողանաք մեզ տրամադրել մեր տվյալների հարստացում, օրինակ՝ Աջակցեք մարդկային գիտելիքների երկարաժամկետ արխիվացմանը՝ միաժամանակ ստանալով ավելի լավ տվյալներ ձեր մոդելի համար։ <a %(a_contact)s>Կապվեք մեզ հետ</a>՝ քննարկելու, թե ինչպես կարող ենք համագործակցել։ Լավ հասկացված է, որ LLM-ները զարգանում են բարձրորակ տվյալների վրա։ Մենք ունենք աշխարհի ամենամեծ գրքերի, հոդվածների, ամսագրերի և այլնի հավաքածուն, որոնք ամենաբարձր որակի տեքստային աղբյուրներից են։ LLM տվյալներ Ունիկալ մասշտաբ և տեսականի Մեր հավաքածուն ներառում է ավելի քան հարյուր միլիոն ֆայլեր, ներառյալ ակադեմիական ամսագրեր, դասագրքեր և ամսագրեր։ Մենք այս մասշտաբին հասնում ենք մեծ առկա պահոցները համադրելով։ Մեր աղբյուրների որոշ հավաքածուներ արդեն հասանելի են մեծ քանակությամբ (Sci-Hub և Libgen-ի մասեր)։ Մյուս աղբյուրները մենք ինքներս ազատագրել ենք։ <a %(a_datasets)s>Datasets</a> ցույց է տալիս ամբողջական ակնարկ։ Մեր հավաքածուն ներառում է միլիոնավոր գրքեր, հոդվածներ և ամսագրեր էլեկտրոնային գրքերի դարաշրջանից առաջ։ Այս հավաքածուի մեծ մասը արդեն OCR-ացվել է և արդեն քիչ ներքին կրկնություններ ունի։ Շարունակել Եթե դուք կորցրել եք ձեր բանալին, խնդրում ենք <a %(a_contact)s>կապ հաստատել մեզ հետ</a> և տրամադրել հնարավորինս շատ տեղեկություններ։ Դուք կարող եք ժամանակավորապես ստեղծել նոր հաշիվ՝ մեզ հետ կապ հաստատելու համար։ Խնդրում ենք <a %(a_account)s>մուտք գործել</a> այս էջը դիտելու համար։</a> Սպամ-բոտերից խուսափելու համար, մենք պետք է նախ ստուգենք ձեր զննարկիչը։ Եթե դուք հայտնվում եք անվերջ ցիկլում, խորհուրդ ենք տալիս տեղադրել <a %(a_privacypass)s>Privacy Pass</a>։ Կարող է նաև օգնել անջատել գովազդային արգելափակումները և այլ զննարկչի ընդարձակումները։ Մուտք / Գրանցում Աննայի արխիվը ժամանակավորապես փակ է տեխնիկական սպասարկման համար։ Խնդրում ենք վերադառնալ մեկ ժամից։ Այլընտրանքային հեղինակ Այլընտրանքային նկարագրություն Այլընտրանքային հրատարակություն Այլընտրանքային ընդլայնում Այլընտրանքային ֆայլի անուն Այլընտրանքային հրատարակիչ Այլընտրանքային վերնագիր բաց կոդով թողարկման ամսաթիվը Կարդալ ավելին… նկարագրություն Որոնել Աննայի Արխիվում CADAL SSNO համարով Որոնել Աննայի Արխիվում DuXiu SSID համարով Որոնել Աննայի Արխիվում DuXiu DXID համարով Փնտրեք Աննայի Արխիվում ըստ ISBN Որոնել Աննայի Արխիվը OCLC (WorldCat) համարով Փնտրել Anna’s Archive-ում Open Library ID-ով Աննայի արխիվի առցանց դիտարկիչ %(count)s ազդված էջեր Ներբեռնումից հետո՝ Այս ֆայլի ավելի լավ տարբերակը կարող է հասանելի լինել %(link)s Զանգվածային տոռենտ ներբեռնումներ հավաքածու Օգտագործեք առցանց գործիքներ՝ ձևաչափերի միջև փոխարկելու համար։ Խորհուրդ տրվող փոխարկման գործիքներ՝ %(links)s Մեծ ֆայլերի համար խորհուրդ ենք տալիս օգտագործել ներբեռնման կառավարիչ՝ ընդհատումները կանխելու համար։ Խորհուրդ տրվող ներբեռնման կառավարիչներ՝ %(links)s EBSCOhost էլեկտրոնային գրքերի ինդեքս (միայն փորձագետների համար) (նաև սեղմեք «ՍՏԱՆԱԼ» վերևում) (սեղմեք “GET” վերևում) Արտաքին ներբեռնումներ <strong>🚀 Արագ ներբեռնումներ</strong> Դուք ունեք %(remaining)s այսօր։ Շնորհակալություն, որ անդամ եք! ❤️ <strong>🚀 Արագ ներբեռնումներ</strong> Դուք սպառել եք այսօրվա արագ ներբեռնումները։ <strong>🚀 Արագ ներբեռնումներ</strong> Դուք վերջերս ներբեռնել եք այս ֆայլը։ Հղումները մնում են վավեր որոշ ժամանակ։ <strong>🚀 Արագ ներբեռնումներ</strong> Դարձեք <a %(a_membership)s>անդամ</a>՝ գրքերի, հոդվածների և այլնի երկարաժամկետ պահպանմանը աջակցելու համար։ Մեր երախտագիտությունը ցույց տալու համար դուք կստանաք արագ ներբեռնումներ։ ❤️ 🚀 Արագ ներբեռնումներ 🐢 Դանդաղ ներբեռնումներ Վերցրեք Internet Archive-ից IPFS Gateway #%(num)d (հնարավոր է, որ անհրաժեշտ լինի մի քանի անգամ փորձել IPFS-ով) Libgen.li Libgen.rs Գեղարվեստական Libgen.rs Գեղարվեստական գրականություն նրանց գովազդները հայտնի են վնասակար ծրագրեր պարունակելու համար, ուստի օգտագործեք գովազդային արգելափակիչ կամ մի սեղմեք գովազդների վրա Amazon-ի «Ուղարկել Kindle-ին» djazz-ի «Ուղարկել Kobo/Kindle-ին» MagzDB ManualsLib Nexus/STC (Nexus/STC ֆայլերը կարող են անվստահելի լինել ներբեռնելու համար) Ներբեռնումներ չեն գտնվել։ Բոլոր ներբեռնման տարբերակները պարունակում են նույն ֆայլը և պետք է անվտանգ լինեն օգտագործման համար։ Այնուամենայնիվ, միշտ զգույշ եղեք ինտերնետից ֆայլեր ներբեռնելիս, հատկապես Աննայի Արխիվից դուրս կայքերից։ Օրինակ, համոզվեք, որ ձեր սարքերը թարմացված են։ (առանց վերահղման) Բացել մեր դիտարկիչում (բացել դիտարկիչում) Ընտրանք #%(num)d: %(link)s %(extra)s Գտնել բնօրինակ գրառումը CADAL-ում Ձեռքով որոնել DuXiu-ում Գտնել բնօրինակ գրառումը ISBNdb-ում Գտնել բնօրինակ գրառումը WorldCat-ում Գտնել բնօրինակ գրառումը Open Library-ում Որոնել տարբեր այլ տվյալների շտեմարաններում ISBN համարով (միայն տպագրության սահմանափակված օգտատերերի համար) PubMed Ֆայլը բացելու համար ձեզ անհրաժեշտ կլինի էլեկտրոնային գրքի կամ PDF ընթերցիչ՝ կախված ֆայլի ձևաչափից։ Խորհուրդ տրվող էլեկտրոնային գրքերի ընթերցիչներ՝ %(links)s Աննայի Արխիվ 🧬 SciDB Sci-Hub: %(doi)s (համապատասխան DOI-ը կարող է հասանելի չլինել Sci-Hub-ում) Դուք կարող եք ուղարկել PDF և EPUB ֆայլերը ձեր Kindle կամ Kobo էլեկտրոնային ընթերցիչին։ Խորհուրդ տրվող գործիքներ՝ %(links)s Ավելին <a %(a_slow)s>ՀՏՀ-ում</a>։ Աջակցեք հեղինակներին և գրադարաններին Եթե ձեզ դուր է գալիս սա և կարող եք թույլ տալ, մտածեք գնել բնօրինակը կամ ուղղակիորեն աջակցել հեղինակներին։ Եթե սա հասանելի է ձեր տեղական գրադարանում, մտածեք այն անվճար վերցնելու մասին այնտեղից։ Գործընկեր Սերվերի ներբեռնումները ժամանակավորապես անհասանելի են այս ֆայլի համար։ torrent Վստահելի գործընկերներից։ Z-Library Z-Գրադարան Tor-ում (պահանջում է Tor Browser) ցուցադրել արտաքին ներբեռնումները <span class="font-bold">❌ Այս ֆայլը կարող է խնդիրներ ունենալ և թաքցվել է աղբյուր գրադարանից։</span> Երբեմն դա հեղինակային իրավունքի տիրոջ խնդրանքով է, երբեմն՝ ավելի լավ այլընտրանքի առկայության պատճառով, բայց երբեմն էլ՝ հենց ֆայլի խնդրի պատճառով։ Այն կարող է դեռևս ներբեռնման համար հարմար լինել, բայց մենք խորհուրդ ենք տալիս նախ փնտրել այլընտրանքային ֆայլ։ Ավելի մանրամասն՝ Եթե դեռ ցանկանում եք ներբեռնել այս ֆայլը, համոզվեք, որ օգտագործում եք միայն վստահելի, թարմացված ծրագրակազմ այն բացելու համար։ մետատվյալների մեկնաբանություններ AA: Որոնել «Աննայի Արխիվում» “%(name)s” Կոդերի հետազոտող: Դիտել Codes Explorer-ում «%(name)s» URL: Կայք: Եթե ունեք այս ֆայլը և այն դեռ հասանելի չէ «Աննայի Արխիվում», մտածեք <a %(a_request)s>վերբեռնելու</a> մասին։ Internet Archive Կառավարվող Թվային Վարձակալության ֆայլ “%(id)s” Սա ֆայլի գրառում է Internet Archive-ից, ոչ թե անմիջապես ներբեռնվող ֆայլ։ Դուք կարող եք փորձել վարձակալել գիրքը (հղումը ներքևում), կամ օգտագործել այս URL-ը, երբ <a %(a_request)s>ֆայլ եք պահանջում</a>։ Բարելավել մետատվյալները CADAL SSNO %(id)s մետատվյալների գրառում Սա մետատվյալների գրառում է, ոչ թե ներբեռնվող ֆայլ։ Դուք կարող եք օգտագործել այս URL-ը, երբ <a %(a_request)s>հարցում եք ֆայլ</a>։ DuXiu SSID %(id)s մետատվյալների գրառում ISBNdb %(id)s մետատվյալների գրառում MagzDB ID %(id)s մետատվյալների գրառում Nexus/STC ID %(id)s մետատվյալների գրառում OCLC (WorldCat) համար %(id)s մետատվյալների գրառում Open Library %(id)s մետատվյալների գրառում Sci-Hub ֆայլ «%(id)s» Չի գտնվել “%(md5_input)s”-ը մեր տվյալների բազայում չի գտնվել։ Ավելացնել մեկնաբանություն (%(count)s) Դուք կարող եք ստանալ md5-ը URL-ից, օրինակ՝ Այս ֆայլի ավելի լավ տարբերակի MD5 (եթե կիրառելի է)։ Լրացրեք սա, եթե կա մեկ այլ ֆայլ, որը մոտ է այս ֆայլին (նույն հրատարակություն, նույն ֆայլի ընդլայնում, եթե կարող եք գտնել), որը մարդիկ պետք է օգտագործեն այս ֆայլի փոխարեն։ Եթե գիտեք այս ֆայլի ավելի լավ տարբերակի մասին Anna’s Archive-ից դուրս, ապա խնդրում ենք <a %(a_upload)s>վերբեռնել այն</a>։ Ինչ-որ բան սխալ է տեղի ունեցել։ Խնդրում ենք վերաբեռնել էջը և փորձել կրկին։ Դուք թողեցիք մեկնաբանություն։ Հնարավոր է, որ այն ցուցադրվի մեկ րոպեից։ Խնդրում ենք օգտագործել <a %(a_copyright)s>DMCA / Հեղինակային իրավունքի պահանջի ձևը</a>։ Նկարագրեք խնդիրը (պարտադիր) Եթե այս ֆայլը բարձր որակի է, կարող եք քննարկել այն այստեղ։ Եթե ոչ, խնդրում ենք օգտագործել «Ֆայլի խնդրի զեկույց» կոճակը։ Հիանալի ֆայլի որակ (%(count)s) Ֆայլի որակ Իմացեք ինչպես <a %(a_metadata)s>բարելավել այս ֆայլի մետատվյալները</a> ինքներդ։ Խնդրի նկարագրություն Խնդրում ենք <a %(a_login)s>մուտք գործել</a>։ Ես սիրեցի այս գիրքը։ Օգնեք համայնքին՝ հաղորդելով այս ֆայլի որակի մասին! 🙌 Ինչ-որ բան սխալ է տեղի ունեցել։ Խնդրում ենք վերաբեռնել էջը և փորձել կրկին։ Հաղորդել ֆայլի խնդիրը (%(count)s) Շնորհակալություն ձեր զեկույցը ներկայացնելու համար։ Այն կցուցադրվի այս էջում, ինչպես նաև կվերանայվի Աննայի կողմից (մինչև մենք ունենանք պատշաճ մոդերացիոն համակարգ)։ Թողնել մեկնաբանություն Ներկայացնել հաղորդումը Ի՞նչն է սխալ այս ֆայլում։ Վերցնել (%(count)s) Մեկնաբանություններ (%(count)s) Ներբեռնումներ (%(count)s) Ուսումնասիրել մետատվյալները (%(count)s) Ցուցակներ (%(count)s) Վիճակագրություն (%(count)s) Այս հատուկ ֆայլի մասին տեղեկությունների համար ստուգեք նրա <a %(a_href)s>JSON ֆայլը</a>։ Սա ֆայլ է, որը կառավարվում է <a %(a_ia)s>IA-ի Կառավարվող Թվային Վարձակալության</a> գրադարանի կողմից և ինդեքսավորվում է Աննայի Արխիվի կողմից որոնման համար։ Տարբեր datasets-ների մասին տեղեկությունների համար տեսեք <a %(a_datasets)s>Datasets էջը</a>։ Կապակցված գրառման մետատվյալներ Բարելավել մետատվյալները Open Library-ում «Ֆայլի MD5»-ը հեշ է, որը հաշվարկվում է ֆայլի բովանդակությունից և բավականին եզակի է այդ բովանդակության հիման վրա։ Բոլոր ստվերային գրադարանները, որոնք մենք ինդեքսավորել ենք այստեղ, հիմնականում օգտագործում են MD5-ները ֆայլերը նույնականացնելու համար։ Ֆայլը կարող է հայտնվել մի քանի ստվերային գրադարաններում։ Տարբեր datasets-ների մասին տեղեկությունների համար տեսեք <a %(a_datasets)s>Datasets էջը</a>։ Հաղորդել ֆայլի որակի մասին Ընդհանուր ներբեռնումներ՝ %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Չեխական մետատվյալներ %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Գրքեր %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Զգուշացում՝ բազմակի կապակցված գրառումներ. Երբ դուք նայում եք գրքին Anna’s Archive-ում, կարող եք տեսնել տարբեր դաշտեր՝ վերնագիր, հեղինակ, հրատարակիչ, հրատարակություն, տարի, նկարագրություն, ֆայլի անուն և այլն։ Այդ բոլոր տեղեկությունները կոչվում են <em>մետատվյալներ</em>։ Քանի որ մենք համադրում ենք գրքերը տարբեր <em>աղբյուր գրադարաններից</em>, մենք ցույց ենք տալիս այն մետատվյալները, որոնք հասանելի են այդ աղբյուր գրադարանում։ Օրինակ, եթե գիրքը ստացել ենք Library Genesis-ից, մենք ցույց կտանք վերնագիրը Library Genesis-ի տվյալների բազայից։ Երբեմն գիրքը առկա է <em>բազմաթիվ</em> աղբյուր գրադարաններում, որոնք կարող են ունենալ տարբեր մետատվյալ դաշտեր։ Այդ դեպքում մենք պարզապես ցույց ենք տալիս յուրաքանչյուր դաշտի ամենաերկար տարբերակը, քանի որ այն, հուսով ենք, պարունակում է ամենաօգտակար տեղեկությունները։ Մենք դեռ ցույց կտանք մյուս դաշտերը նկարագրության ներքո, օրինակ՝ որպես «այլընտրանքային վերնագիր» (բայց միայն եթե դրանք տարբեր են)։ Մենք նաև հանում ենք <em>կոդեր</em>, ինչպիսիք են նույնացուցիչներն ու դասակարգիչները աղբյուր գրադարանից։ <em>Նույնացուցիչները</em> եզակիորեն ներկայացնում են գրքի որոշակի հրատարակությունը. օրինակներ են ISBN, DOI, Open Library ID, Google Books ID կամ Amazon ID։ <em>Դասակարգիչները</em> խմբավորում են մի քանի նմանատիպ գրքեր. օրինակներ են Դյուիի տասնորդական դասակարգումը (DCC), Համընդհանուր տասնորդական դասակարգումը (UDC), Կոնգրեսի գրադարանի դասակարգումը (LCC), RVK կամ GOST։ Երբեմն այս կոդերը հստակորեն կապված են աղբյուր գրադարաններում, և երբեմն մենք կարող ենք դրանք հանել ֆայլի անունից կամ նկարագրությունից (հիմնականում ISBN և DOI)։ Մենք կարող ենք օգտագործել նույնացուցիչները՝ գրառումներ գտնելու համար <em>միայն մետատվյալների հավաքածուներում</em>, ինչպիսիք են OpenLibrary, ISBNdb կամ WorldCat/OCLC։ Մեր որոնման համակարգում կա հատուկ <em>մետատվյալների ներդիր</em>, եթե ցանկանում եք թերթել այդ հավաքածուները։ Մենք օգտագործում ենք համապատասխան գրառումները՝ բացակա մետատվյալ դաշտերը լրացնելու համար (օրինակ՝ եթե վերնագիրը բացակայում է), կամ օրինակ՝ որպես «այլընտրանքային վերնագիր» (եթե կա առկա վերնագիր)։ Գրքի մետատվյալների աղբյուրը տեսնելու համար տեսեք <em>«Տեխնիկական մանրամասներ» ներդիրը</em> գրքի էջում։ Այն ունի հղում դեպի այդ գրքի հում JSON-ը, որը պարունակում է հղումներ դեպի սկզբնական գրառումների հում JSON-ը։ Լրացուցիչ տեղեկությունների համար տեսեք հետևյալ էջերը՝ <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Search (metadata tab)</a>, <a %(a_codes)s>Codes Explorer</a> և <a %(a_example)s>Example metadata JSON</a>։ Վերջապես, մեր բոլոր մետատվյալները կարող են <a %(a_generated)s>ստեղծվել</a> կամ <a %(a_downloaded)s>ներբեռնվել</a> որպես ElasticSearch և MariaDB տվյալների բազաներ։ Նախապատմություն Դուք կարող եք օգնել գրքերի պահպանմանը՝ բարելավելով մետատվյալները։ Նախ, կարդացեք մետատվյալների մասին նախապատմությունը Anna’s Archive-ում, ապա սովորեք, թե ինչպես բարելավել մետատվյալները՝ կապելով Open Library-ի հետ, և ստացեք անվճար անդամակցություն Anna’s Archive-ում։ Բարելավել մետատվյալները Այսպիսով, եթե հանդիպեք վատ մետատվյալներով ֆայլ, ինչպես պետք է այն շտկեք։ Դուք կարող եք գնալ աղբյուր գրադարան և հետևել դրա մետատվյալների շտկման ընթացակարգերին, բայց ի՞նչ անել, եթե ֆայլը առկա է բազմաթիվ աղբյուր գրադարաններում։ Anna’s Archive-ում կա մեկ նույնացուցիչ, որը հատուկ է։ <strong>Open Library-ի annas_archive md5 դաշտը միշտ գերազանցում է բոլոր մյուս մետատվյալներին։</strong> Եկեք նախ մի փոքր հետ գնանք և սովորենք Open Library-ի մասին։ Open Library-ն հիմնադրվել է 2006 թվականին Աարոն Սվարցի կողմից՝ «յուրաքանչյուր երբևէ հրատարակված գրքի համար մեկ վեբ էջ» նպատակով։ Դա մի տեսակ Վիքիպեդիա է գրքերի մետատվյալների համար. բոլորը կարող են խմբագրել այն, այն ազատորեն լիցենզավորված է և կարող է ներբեռնվել մեծ քանակությամբ։ Դա գրքերի տվյալների բազա է, որը առավել համահունչ է մեր առաքելությանը. փաստորեն, Anna’s Archive-ը ոգեշնչվել է Աարոն Սվարցի տեսլականից և կյանքից։ Փոխարենը՝ անիվը նորից հորինելու, մենք որոշեցինք մեր կամավորներին ուղղորդել դեպի Open Library։ Եթե տեսնում եք գիրք, որը սխալ մետատվյալներ ունի, կարող եք օգնել հետևյալ կերպ. Նշենք, որ սա գործում է միայն գրքերի համար, ոչ թե գիտական հոդվածների կամ այլ տեսակի ֆայլերի համար։ Այլ տեսակի ֆայլերի համար մենք դեռ խորհուրդ ենք տալիս գտնել աղբյուրի գրադարանը։ Կարող է տևել մի քանի շաբաթ, որպեսզի փոփոխությունները ներառվեն Anna’s Archive-ում, քանի որ մենք պետք է ներբեռնենք վերջին Open Library տվյալների բեռնաթափումը և վերականգնենք մեր որոնման ինդեքսը։  Գնացեք <a %(a_openlib)s>Open Library կայք</a>։ Գտեք ճիշտ գրքի գրառումը։ <strong>ԶԳՈՒՇԱՑՈՒՄ:</strong> համոզվեք, որ ընտրել եք ճիշտ <strong>հրատարակությունը</strong>։ Open Library-ում դուք ունեք «աշխատանքներ» և «հրատարակություններ»։ «Աշխատանք» կարող է լինել «Հարի Փոթերը և Փիլիսոփայի քարը»։ «Հրատարակություն» կարող է լինել: 1997 թվականի առաջին հրատարակությունը, որը հրատարակվել է Bloomsbery-ի կողմից՝ 256 էջով։ 2003 թվականի փափուկ կազմով հրատարակությունը, որը հրատարակվել է Raincoast Books-ի կողմից՝ 223 էջով։ 2000 թվականի լեհերեն թարգմանությունը՝ «Harry Potter I Kamie Filozoficzn», Media Rodzina-ի կողմից՝ 328 էջով։ Բոլոր այդ հրատարակությունները ունեն տարբեր ISBN-ներ և տարբեր բովանդակություն, ուստի համոզվեք, որ ընտրում եք ճիշտը։ Խմբագրեք գրառումը (կամ ստեղծեք այն, եթե գոյություն չունի) և ավելացրեք որքան հնարավոր է շատ օգտակար տեղեկատվություն։ Դուք արդեն այստեղ եք, այնպես որ կարող եք գրառումը իսկապես հիանալի դարձնել։ «ID Numbers» բաժնում ընտրեք «Anna’s Archive» և ավելացրեք գրքի MD5-ը Anna’s Archive-ից։ Սա երկար տող է տառերով և թվերով, որը գտնվում է URL-ի «/md5/» հատվածից հետո։ Փորձեք գտնել այլ ֆայլեր Anna’s Archive-ում, որոնք նույնպես համապատասխանում են այս գրառմանը, և ավելացրեք դրանք նույնպես։ Ապագայում մենք կարող ենք դրանք խմբավորել որպես կրկնօրինակներ Anna’s Archive որոնման էջում։ Երբ ավարտեք, գրեք այն URL-ը, որը հենց նոր թարմացրել եք։ Երբ թարմացնեք առնվազն 30 գրառում Anna’s Archive MD5-ներով, ուղարկեք մեզ <a %(a_contact)s>էլ. նամակ</a> և ուղարկեք մեզ ցուցակը։ Մենք ձեզ կտանք անվճար անդամակցություն Anna’s Archive-ի համար, որպեսզի կարողանաք ավելի հեշտությամբ կատարել այս աշխատանքը (և որպես շնորհակալություն ձեր օգնության համար)։ Սրանք պետք է լինեն բարձր որակի խմբագրումներ, որոնք ավելացնում են զգալի քանակությամբ տեղեկատվություն, հակառակ դեպքում ձեր խնդրանքը կմերժվի։ Ձեր խնդրանքը նույնպես կմերժվի, եթե որևէ խմբագրում վերացվի կամ ուղղվի Open Library-ի մոդերատորների կողմից։ Open Library-ի կապում Եթե դուք զգալիորեն ներգրավվեք մեր աշխատանքի զարգացման և գործառնությունների մեջ, մենք կարող ենք քննարկել ավելի շատ նվիրատվությունների եկամուտների կիսումը ձեզ հետ, որպեսզի դուք կարողանաք անհրաժեշտության դեպքում օգտագործել։ Մենք կվճարենք հոսթինգի համար միայն այն ժամանակ, երբ դուք ամեն ինչ կարգավորեք և ցույց տաք, որ կարող եք արխիվը թարմացնել թարմացումներով։ Սա նշանակում է, որ դուք պետք է վճարեք առաջին 1-2 ամիսները ձեր գրպանից։ Ձեր ժամանակը չի փոխհատուցվի (և ոչ էլ մեր), քանի որ սա մաքուր կամավորական աշխատանք է։ Մենք պատրաստ ենք ծածկել հոսթինգի և VPN-ի ծախսերը, սկզբում մինչև $200 ամսական։ Սա բավարար է հիմնական որոնման սերվերի և DMCA-պաշտպանված պրոքսիի համար։ Հոսթինգի ծախսեր Խնդրում ենք <strong>մեզ չկապվել</strong> թույլտվություն հարցնելու կամ հիմնական հարցերի համար։ Գործողությունները խոսում են ավելի բարձր, քան խոսքերը։ Բոլոր տեղեկությունները հասանելի են, այնպես որ պարզապես շարունակեք ձեր հայելու կարգավորումը։ Խնդրում ենք ազատորեն տեղադրել տոմսեր կամ միաձուլման հարցումներ մեր Gitlab-ում, երբ բախվեք խնդիրների։ Մենք կարող ենք անհրաժեշտություն ունենալ կառուցել որոշ հայելային հատուկ ֆունկցիաներ ձեզ հետ, ինչպիսիք են «Anna’s Archive»-ից ձեր կայքի անվանափոխումը, (սկզբում) օգտատերերի հաշիվների անջատումը կամ գրքերի էջերից մեր հիմնական կայքին հղումը։ Երբ ձեր հայելին աշխատի, խնդրում ենք կապվել մեզ հետ։ Մենք կցանկանայինք վերանայել ձեր OpSec-ը, և երբ դա ամուր լինի, մենք կհղենք ձեր հայելին և կսկսենք ավելի սերտ աշխատել ձեզ հետ։ Նախապես շնորհակալություն բոլոր նրանց, ովքեր պատրաստ են այս կերպ ներդրում ունենալ։ Սա թույլ սրտերի համար չէ, բայց դա կկայունացնի մարդկության պատմության ամենամեծ իսկապես բաց գրադարանի երկարակեցությունը։ Սկսել Anna’s Archive-ի դիմացկունությունը բարձրացնելու համար մենք փնտրում ենք կամավորներ՝ հայելիներ գործարկելու համար։ Ձեր տարբերակը հստակ առանձնացված է որպես հայելի, օրինակ՝ «Բոբի արխիվ, Anna’s Archive-ի հայելի»։ Դուք պատրաստ եք ընդունել այս աշխատանքի հետ կապված ռիսկերը, որոնք նշանակալի են։ Դուք խորը հասկանում եք գործառնական անվտանգության պահանջները։ <a %(a_shadow)s>այս</a> <a %(a_pirate)s>հոդվածների</a> բովանդակությունը ձեզ համար ակնհայտ է։ Սկզբում մենք ձեզ չենք տրամադրի մեր գործընկեր սերվերի ներբեռնումների հասանելիությունը, բայց եթե ամեն ինչ լավ ընթանա, մենք կարող ենք կիսվել այդով։ Դուք վարում եք Anna’s Archive-ի բաց կոդով բազան և պարբերաբար թարմացնում եք ինչպես կոդը, այնպես էլ տվյալները։ Դուք պատրաստ եք ներդրում ունենալ մեր <a %(a_codebase)s>կոդային բազայում</a>՝ համագործակցելով մեր թիմի հետ՝ այս ամենը իրականացնելու համար։ Մենք փնտրում ենք սա. Հայելիներ՝ կամավորների կոչ Կատարեք ևս մեկ նվիրատվություն։ Դեռ նվիրատվություններ չկան։ <a %(a_donate)s>Կատարել իմ առաջին նվիրատվությունը։</a> Նվիրատվությունների մանրամասները հրապարակային չեն ցուցադրվում։ Իմ նվիրատվությունները 📡 Մեր հավաքածուի զանգվածային հայելապատման համար այցելեք <a %(a_datasets)s>Datasets</a> և <a %(a_torrents)s>Torrents</a> էջերը։ Ներբեռնումներ ձեր IP հասցեից վերջին 24 ժամվա ընթացքում՝ %(count)s։ 🚀 Ավելի արագ ներբեռնումների և զննարկչի ստուգումները շրջանցելու համար <a %(a_membership)s>դարձեք անդամ</a>։ Ներբեռնում գործընկեր կայքից Անհոգ շարունակեք թերթել «Աննայի արխիվը» այլ ներդիրում՝ սպասելու ընթացքում (եթե ձեր զննարկիչը աջակցում է ֆոնային ներդիրների թարմացմանը)։ Ազատ զգացեք սպասել մի քանի ներբեռնման էջերի բեռնմանը միաժամանակ (բայց խնդրում ենք ներբեռնել միայն մեկ ֆայլ միաժամանակ մեկ սերվերից)։ Երբ ստանաք ներբեռնման հղումը, այն վավեր է մի քանի ժամ։ Շնորհակալություն սպասելու համար, սա պահում է կայքը անվճար հասանելի բոլորի համար։ 😊 🔗 Բոլոր ներբեռնման հղումները այս ֆայլի համար՝ <a %(a_main)s>Ֆայլի գլխավոր էջ</a>։ ❌ Դանդաղ ներբեռնումները հասանելի չեն Cloudflare VPN-ների կամ Cloudflare IP հասցեներից։ ❌ Դանդաղ ներբեռնումները հասանելի են միայն պաշտոնական կայքի միջոցով։ Այցելեք %(websites)s։ 📚 Օգտագործեք հետևյալ URL-ը ներբեռնելու համար՝ <a %(a_download)s>Ներբեռնել հիմա</a>։ Որպեսզի բոլորին հնարավորություն տրվի անվճար ներբեռնել ֆայլերը, անհրաժեշտ է սպասել նախքան այս ֆայլը ներբեռնելը։ Խնդրում ենք սպասել <span %(span_countdown)s>%(wait_seconds)s</span> վայրկյան՝ այս ֆայլը ներբեռնելու համար։ Զգուշացում. վերջին 24 ժամվա ընթացքում ձեր IP հասցեից շատ ներբեռնումներ են եղել։ Ներբեռնումները կարող են ավելի դանդաղ լինել, քան սովորաբար։ Եթե օգտագործում եք VPN, ընդհանուր ինտերնետ կապ, կամ ձեր ISP-ն կիսում է IP-ները, այս նախազգուշացումը կարող է դրա պատճառով լինել։ Պահպանել ❌ Ինչ-որ բան սխալ է տեղի ունեցել։ Խնդրում ենք կրկին փորձել։ ✅ Պահպանված է։ Խնդրում ենք վերաբեռնել էջը։ Փոխեք ձեր ցուցադրվող անունը։ Ձեր նույնացուցիչը (այն մասը, որը «#»-ից հետո է) չի կարող փոխվել։ Պրոֆիլը ստեղծվել է <span %(span_time)s>%(time)s</span> խմբագրել Ցուցակներ Ստեղծեք նոր ցուցակ՝ գտնելով ֆայլը և բացելով «Ցուցակներ» ներդիրը։ Ցանկեր դեռ չկան Պրոֆիլը չի գտնվել։ Պրոֆիլ Այս պահին մենք չենք կարող բավարարել գրքերի պահանջները։ Մի՛ ուղարկեք մեզ ձեր գրքերի հարցումները էլեկտրոնային փոստով։ Խնդրում ենք ձեր հարցումները կատարել Z-Library կամ Libgen ֆորումներում։ Գրանցում Աննայի Արխիվում DOI: %(doi)s Ներբեռնել SciDB Nexus/STC Նախադիտումը դեռ հասանելի չէ։ Ներբեռնեք ֆայլը <a %(a_path)s>Աննայի Արխիվից</a>։ Մարդկային գիտելիքների հասանելիությունն ու երկարաժամկետ պահպանությունը աջակցելու համար դառնալ <a %(a_donate)s>անդամ</a>։ Բոնուսով, 🧬&nbsp;SciDB-ն բեռնվում է ավելի արագ անդամների համար, առանց սահմանափակումների։ Չի աշխատում? Փորձեք <a %(a_refresh)s>թարմացնել</a>: Sci-Hub Ավելացնել հատուկ որոնման դաշտ Որոնել նկարագրություններն ու մետատվյալների մեկնաբանությունները Հրատարակման տարեթիվ Ընդլայնված Մուտք Բովանդակություն Ցուցադրել Ցանկ Աղյուսակ Ֆայլի տեսակ Լեզու Դասավորել ըստ Ամենամեծը Ամենահամապատասխան Նորագույն (ֆայլի չափս) (բաց կոդով) (հրատարակման տարեթիվ) Ամենահինը Պատահական Ամենափոքր Աղբյուր քերված և բաց կոդով AA-ի կողմից Թվային փոխառություն (%(count)s) Հոդվածներ ամսագրերից (%(count)s) Մենք գտել ենք համընկնումներ՝ %(in)s-ում։ Դուք կարող եք հղում կատարել այնտեղ գտնված URL-ին, երբ <a %(a_request)s>հարցում եք ֆայլ</a>։ Մետատվյալներ (%(count)s) Կոդերով որոնման ինդեքսը ուսումնասիրելու համար օգտագործեք <a %(a_href)s>Codes Explorer</a>։ Որոնման ինդեքսը թարմացվում է ամսական։ Ներկայումս այն ներառում է գրառումներ մինչև %(last_data_refresh_date)s։ Ավելի տեխնիկական տեղեկությունների համար տես %(link_open_tag)sdatasets էջը</a>: Բացառել Միայն ներառել Չստուգված ավելին… Հաջորդը … Նախորդ Այս որոնման ինդեքսը ներկայումս ներառում է մետատվյալներ Internet Archive-ի Կառավարվող Թվային Վարձակալության գրադարանից։ <a %(a_datasets)s>Ավելին մեր datasets-ի մասին</a>: Լրացուցիչ թվային վարկային գրադարանների համար տես <a %(a_wikipedia)s>Վիքիպեդիա</a> և <a %(a_mobileread)s>MobileRead Վիքի</a>։ DMCA / հեղինակային իրավունքների պահանջների համար <a %(a_copyright)s>սեղմեք այստեղ</a>։ Ներբեռնման ժամանակը Որոնման ընթացքում սխալ է տեղի ունեցել։ Փորձեք <a %(a_reload)s>վերաբեռնել էջը</a>: Եթե խնդիրը շարունակվում է, խնդրում ենք ուղարկել նամակ %(email)s հասցեին: Արագ ներբեռնում Իրականում, յուրաքանչյուրը կարող է օգնել պահպանել այս ֆայլերը՝ սերմանելով մեր <a %(a_torrents)s>միավորված տոռենտների ցանկը</a>: ➡️ Երբեմն սա սխալ է տեղի ունենում, երբ որոնման սերվերը դանդաղ է աշխատում: Նման դեպքերում <a %(a_attrs)s>վերաբեռնումը</a> կարող է օգնել: ❌ Այս ֆայլը կարող է խնդիրներ ունենալ։ Փնտրու՞մ եք հոդվածներ։ Այս որոնման ինդեքսը ներկայումս ներառում է մետատվյալներ տարբեր մետատվյալների աղբյուրներից։ <a %(a_datasets)s>Ավելին մեր datasets-ի մասին</a>։ Գրավոր աշխատանքների մետատվյալների բազմաթիվ աղբյուրներ կան ամբողջ աշխարհում։ <a %(a_wikipedia)s>Այս Վիքիպեդիայի էջը</a> լավ սկիզբ է, բայց եթե գիտեք այլ լավ ցուցակներ, խնդրում ենք տեղեկացնել մեզ։ Մետատվյալների համար մենք ցույց ենք տալիս բնօրինակ գրառումները։ Մենք չենք միավորում գրառումները։ Մենք ներկայումս ունենք աշխարհի ամենալայն բաց կատալոգը գրքերի, հոդվածների և այլ գրավոր աշխատանքների համար։ Մենք հայելում ենք Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>և ավելին</a>։ <span class="font-bold">Ֆայլեր չեն գտնվել։</span> Փորձեք ավելի քիչ կամ այլ որոնման տերմիններ և ֆիլտրեր։ Արդյունքներ %(from)s-%(to)s (%(total)s ընդհանուր) Եթե գտնեք այլ «ստվերային գրադարաններ», որոնք մենք պետք է հայելինենք, կամ եթե ունեք հարցեր, խնդրում ենք կապ հաստատել մեզ հետ %(email)s հասցեով։ %(num)d մասնակի համընկնումներ %(num)d+ մասնակի համընկնումներ Մուտքագրեք տուփում՝ թվային վարկավորման գրադարաններում ֆայլեր որոնելու համար։ Մուտքագրեք տուփում՝ մեր կատալոգում %(count)s անմիջապես ներբեռնվող ֆայլեր որոնելու համար, որոնք մենք <a %(a_preserve)s>պահպանում ենք ընդմիշտ</a>։ Մուտքագրեք տուփում՝ որոնելու համար։ Մուտքագրեք տուփում՝ մեր %(count)s ակադեմիական հոդվածների և ամսագրերի հոդվածների կատալոգում որոնելու համար, որոնք մենք <a %(a_preserve)s>պահպանում ենք ընդմիշտ</a>։ Մուտքագրեք տուփում՝ գրադարաններից մետատվյալներ որոնելու համար։ Սա կարող է օգտակար լինել, երբ <a %(a_request)s>ֆայլի հարցում եք անում</a>։ Խորհուրդ: օգտագործեք ստեղնաշարի կարճուղիներ՝ “/” (որոնման կենտրոնացում), “enter” (որոնում), “j” (վերև), “k” (ներքև), “<” (նախորդ էջ), “>” (հաջորդ էջ)՝ ավելի արագ նավարկելու համար։ Սրանք մետատվյալների գրառումներ են, <span %(classname)s>ոչ</span> ներբեռնվող ֆայլեր։ Որոնման կարգավորումներ Որոնել Թվային վարկավորում Ներբեռնել Հոդվածներ ամսագրերից Մետատվյալներ Նոր որոնում %(search_input)s - Որոնել Որոնումը շատ երկար տևեց, ինչը նշանակում է, որ դուք կարող եք տեսնել անճշտ արդյունքներ։ Երբեմն <a %(a_reload)s>էջը վերաբեռնելը</a> օգնում է։ Որոնումը շատ երկար տևեց, ինչը սովորական է լայն հարցումների համար։ Ֆիլտրի հաշվարկները կարող են անճշտություններ ունենալ։ Մեծ վերբեռնումների համար (ավելի քան 10,000 ֆայլ) որոնք չեն ընդունվում Libgen կամ Z-Library-ի կողմից, խնդրում ենք կապ հաստատել մեզ հետ %(a_email)s։ Libgen.li-ի համար համոզվեք, որ նախ մուտք գործեք <a %(a_forum)s >նրանց ֆորում</a>՝ օգտագործելով օգտանուն %(username)s և գաղտնաբառ %(password)s, ապա վերադարձեք նրանց <a %(a_upload_page)s >բեռնումների էջ</a>: Ներկայումս մենք առաջարկում ենք նոր գրքեր վերբեռնել Library Genesis-ի պատճեններին։ Ահա <a %(a_guide)s>օգտակար ուղեցույց</a>։ Նշենք, որ այս կայքում ինդեքսավորվող երկու պատճեններն էլ քաշում են նույն վերբեռնման համակարգից։ Փոքր բեռնումների համար (մինչև 10,000 ֆայլ) խնդրում ենք բեռնել դրանք և՛ %(first)s, և՛ %(second)s: Այլընտրանքային տարբերակ, կարող եք դրանք վերբեռնել Z-Library <a %(a_upload)s>այստեղ</a>: Ակադեմիական հոդվածներ վերբեռնելու համար, խնդրում ենք (Library Genesis-ից բացի) վերբեռնել նաև <a %(a_stc_nexus)s>STC Nexus</a>-ում։ Նրանք լավագույն ստվերային գրադարանն են նոր հոդվածների համար։ Մենք դեռ չենք ինտեգրվել նրանց հետ, բայց կարվի ինչ-որ պահի։ Կարող եք օգտագործել նրանց <a %(a_telegram)s>վերբեռնման բոտը Telegram-ում</a>, կամ կապ հաստատել հասցեով, որը նշված է նրանց ամրացված հաղորդագրության մեջ, եթե ունեք շատ ֆայլեր վերբեռնելու համար։ <span %(label)s>Ծանր կամավորական աշխատանք (50-5000 ԱՄՆ դոլար պարգևներ)</span>՝ եթե կարող եք շատ ժամանակ և/կամ ռեսուրսներ տրամադրել մեր առաքելությանը, մենք կցանկանայինք ավելի սերտ համագործակցել ձեզ հետ։ Ի վերջո, դուք կարող եք միանալ ներքին թիմին։ Թեև մենք ունենք խիստ բյուջե, մենք կարող ենք պարգևատրել <span %(bold)s>💰 դրամական պարգևներ</span> ամենածանր աշխատանքների համար։ <span %(label)s>Թեթև կամավորական աշխատանք․</span> եթե կարող եք տրամադրել ընդամենը մի քանի ժամ, դեռևս կան բազմաթիվ եղանակներ, որոնցով կարող եք օգնել։ Մենք պարգևատրում ենք հետևողական կամավորներին <span %(bold)s>🤝 անդամակցությամբ Աննայի արխիվին</span>։ Anna’s Archive-ը հիմնվում է ձեր նման կամավորների վրա։ Մենք ողջունում ենք բոլոր նվիրվածության մակարդակները և ունենք օգնության երկու հիմնական կատեգորիաներ, որոնք մենք փնտրում ենք. Եթե չեք կարող կամավոր ժամանակ տրամադրել, դուք դեռ կարող եք շատ օգնել մեզ՝ <a %(a_donate)s>նվիրաբերելով գումար</a>, <a %(a_torrents)s>մեր տոռենտները սերմանելով</a>, <a %(a_uploading)s>գրքեր վերբեռնելով</a> կամ <a %(a_help)s>ձեր ընկերներին պատմելով Աննայի Արխիվի մասին</a>։ <span %(bold)s>Ընկերություններ</span>՝ մենք առաջարկում ենք բարձր արագությամբ ուղղակի մուտք մեր հավաքածուներին՝ ձեռնարկության մակարդակի նվիրատվության դիմաց կամ նոր հավաքածուների փոխանակման դիմաց (օրինակ՝ նոր սկաներ, OCR-ացված datasets, մեր տվյալների հարստացում)։ <a %(a_contact)s>Կապվեք մեզ հետ</a>, եթե դուք եք։ Տես նաև մեր <a %(a_llm)s>LLM էջը</a>։ Պարգևներ Մենք միշտ փնտրում ենք մարդկանց, ովքեր ունեն ամուր ծրագրավորման կամ հարձակողական անվտանգության հմտություններ՝ ներգրավվելու համար։ Դուք կարող եք լուրջ ներդրում ունենալ մարդկության ժառանգության պահպանման գործում։ Որպես շնորհակալություն, մենք տալիս ենք անդամակցություն ամուր ներդրումների համար։ Որպես մեծ շնորհակալություն, մենք տալիս ենք դրամական պարգևներ հատկապես կարևոր և դժվարին առաջադրանքների համար։ Սա չպետք է դիտվի որպես աշխատանքի փոխարինում, բայց դա լրացուցիչ խթան է և կարող է օգնել ծախսերի փոխհատուցմանը։ Մեր կոդի մեծ մասը բաց կոդով է, և մենք կխնդրենք, որ ձեր կոդն էլ լինի այդպիսին, երբ պարգևը շնորհվի։ Կան որոշ բացառություններ, որոնք կարող ենք քննարկել անհատական հիմունքներով։ Պարգևները շնորհվում են առաջին անձին, ով ավարտում է առաջադրանքը։ Ազատորեն մեկնաբանեք պարգևի տոմսը, որպեսզի մյուսները իմանան, որ դուք ինչ-որ բանի վրա աշխատում եք, որպեսզի մյուսները կարողանան սպասել կամ կապվել ձեզ հետ՝ թիմ կազմելու համար։ Բայց տեղյակ եղեք, որ մյուսները նույնպես ազատ են աշխատելու դրա վրա և փորձելու ձեզ հաղթել։ Այնուամենայնիվ, մենք չենք շնորհում պարգևներ անփույթ աշխատանքի համար։ Եթե երկու բարձրորակ ներկայացումներ կատարվեն միմյանց մոտ (մեկ կամ երկու օրվա ընթացքում), մենք կարող ենք որոշել պարգևներ շնորհել երկուսին էլ՝ մեր հայեցողությամբ, օրինակ՝ 100%% առաջին ներկայացման համար և 50%% երկրորդ ներկայացման համար (այսպիսով՝ 150%% ընդհանուր)։ Մեծ պարգևների համար (հատկապես scraping պարգևների), խնդրում ենք կապվել մեզ հետ, երբ ավարտեք ~5%% և վստահ եք, որ ձեր մեթոդը կկարողանա հասնել ամբողջական նպատակին։ Դուք պետք է կիսվեք ձեր մեթոդով մեզ հետ, որպեսզի մենք կարողանանք արձագանքել։ Նաեւ, այս կերպ մենք կարող ենք որոշել, թե ինչ անել, եթե մի քանի մարդ մոտենում է պարգևին, օրինակ՝ պարգևը մի քանի մարդու շնորհելը, մարդկանց խրախուսելը թիմ կազմելու և այլն։ ԶԳՈՒՇԱՑՈՒՄ. բարձր պարգևների առաջադրանքները <span %(bold)s>դժվար</span> են՝ գուցե իմաստուն լինի սկսել ավելի հեշտերից։ Գնացեք մեր <a %(a_gitlab)s>Gitlab խնդիրների ցուցակին</a> և դասավորեք ըստ «Label priority»։ Սա ցույց է տալիս մոտավորապես այն առաջադրանքների հերթականությունը, որոնք մեզ համար կարևոր են։ Առանց հստակ պարգևների առաջադրանքները նույնպես իրավասու են անդամակցության համար, հատկապես նրանք, որոնք նշված են «Accepted» և «Anna’s favorite»։ Գուցե ցանկանաք սկսել «Starter project»-ից։ Թեթև կամավորություն Մենք այժմ ունենք նաև համաժամանակացված Matrix ալիք %(matrix)s-ում։ Եթե մի քանի ժամ ունեք, կարող եք օգնել մի շարք ձևերով։ Համոզվեք, որ միացել եք <a %(a_telegram)s>կամավորների զրույցին Telegram-ում</a>։ Որպես երախտագիտության նշան, մենք սովորաբար տալիս ենք 6 ամիս «Հաջողակ Գրադարանավար» հիմնական նվաճումների համար, և ավելին՝ շարունակական կամավորական աշխատանքի համար։ Բոլոր նվաճումները պահանջում են բարձր որակի աշխատանք՝ անփույթ աշխատանքը մեզ ավելի շատ վնասում է, քան օգնում, և մենք այն մերժելու ենք։ Խնդրում ենք <a %(a_contact)s>էլ. նամակ ուղարկել մեզ</a>, երբ հասնեք նվաճման։ %(links)s հղումներ կամ սքրինշոթներ ձեր կատարած հարցումների։ Գրքերի (կամ հոդվածների և այլն) պահանջների կատարում Z-Library կամ Library Genesis ֆորումներում։ Մենք չունենք մեր սեփական գրքերի պահանջների համակարգը, բայց մենք հայելում ենք այդ գրադարանները, ուստի դրանք ավելի լավ դարձնելը նաև Աննայի Արխիվը ավելի լավ է դարձնում։ Նվաճում Առաջադրանք Կախված է առաջադրանքից։ Փոքր առաջադրանքներ, որոնք տեղադրված են մեր <a %(a_telegram)s>կամավորների զրույցում Telegram-ում</a>։ Սովորաբար անդամակցության համար, երբեմն փոքր պարգևների համար։ Փոքր առաջադրանքներ, որոնք տեղադրված են մեր կամավորների զրույցի խմբում։ Համոզվեք, որ մեկնաբանություն թողեք այն խնդիրների վրա, որոնք լուծում եք, որպեսզի մյուսները չկրկնեն ձեր աշխատանքը։ %(links)s գրառումների հղումներ, որոնք դուք բարելավել եք։ Դուք կարող եք օգտագործել <a %(a_list)s>պատահական մետատվյալների խնդիրների ցանկը</a> որպես մեկնարկային կետ։ Բարելավել մետատվյալները՝ <a %(a_metadata)s>կապելով</a> Open Library-ի հետ։ Սրանք պետք է ցույց տան, որ դուք ինչ-որ մեկին տեղեկացնում եք Աննայի Արխիվի մասին, և նրանք շնորհակալություն են հայտնում ձեզ։ %(links)s հղումներ կամ սքրինշոթներ։ Աննայի Արխիվի մասին խոսքը տարածելը։ Օրինակ՝ առաջարկելով գրքեր AA-ում, հղում տալով մեր բլոգի գրառումներին կամ ուղղակիորեն մարդկանց ուղղորդելով մեր կայք։ Լրիվ թարգմանել լեզուն (եթե այն արդեն մոտ չէր ավարտին)։ <a %(a_translate)s>Թարգմանել</a> կայքը։ Հղում խմբագրման պատմությանը, որը ցույց է տալիս, որ դուք կատարել եք նշանակալի ներդրումներ։ Բարելավել Աննայի Արխիվի Վիքիպեդիայի էջը ձեր լեզվով։ Ներառեք տեղեկատվություն AA-ի Վիքիպեդիայի էջից այլ լեզուներով, ինչպես նաև մեր կայքից և բլոգից։ Ավելացրեք հղումներ AA-ի մասին այլ համապատասխան էջերում։ Կամավորություն և պարգևներ 