��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b 
  sd �  ~g 0   i <  Ai j  ~j    �l �  
o b  �p q   r �   �r J   s s   _s "  �s �  �u B  �w R  �x ,  '{ �  T| '  �~ ~  �   �� �  ��   7� M   <�   �� ^   �� Y  � $   a� �  �� J   0� &   {�    �� $   �� V   � 4   :�    o� G   �� /   Α 0   ��    /� H   M� �  �� A  ,� c   n� <   ҕ    �     � $   ;�    `� ?   {�    ��    ϖ    ޖ    �� 1   �    5� %   >� 	   d� 
   n� !   |� -   �� /   ̗    �� /  � �  J� �   �� 0   �� �  � �   �� N  \� '  �� $   Ӣ �   �� $   �� �   ʣ )   �� N  ֤ $   %� �  J� 8   � e   $�    �� y  �� �  �   ӫ [  ܭ    8� H   L� t   �� �   
� <   �� 4   7� �   l� T   �� G   O� G   ��    ߲ �   �   �� S   �� �   � �   �� `   f� x  Ƿ T   @� ;  �� �   Ѻ �   �� m   d� �   Ҽ �   Ž J   y� L  ľ �   � c   � B   h� �   �� 
   O� �   Z� l  *� J   �� 
   �� �   �� ,  �� 
   � �  � �   ��   �� �  �� �   V� e  ?� S  �� d   �� �   ^� _   �� �   @� i   �� �   P� I   �� �   � j   �� T   H�    �� �   �� c   M�    �� �   ��    g� t  �� L  �� U  G� �  �� �   }� �   0� �  � �   �� �   i� �   <� �  � �   ��   {�    �� �   �� @  V� �   �� �  �� �   � �   �� �   X�   � �   5� �  �� =  �� (   �� s   �� �   f� h   2� �  �� �   .� �    � �   �� ;   8� �  t� �   F� �  >�     �     �   � �   t H  p (  �   � �    1   �	 �  
   � h  � +  G u  s .  � z    �   � 
   , �   7 h  3 �  � *  w G  � �   �    � �  � �  k  D  �! 
   C# �   N# �  )$ �   �% �  �& �   5) s   *    �* �  �* �  �,    N. @  b. @  �/ �  �0 
  �3 �  �4 b   Z6 d   �6 >  "7 3   a8    �8 �  �8 ~  7: {  �; C   2= J   v=   �= �   A@ �   �@ �   lA �  �A '   �C    �C �  �C �   �E �   �F U   �G �   �G .   �H L  �H [   J    \K    nK +   K Q   �K R   �K M   PL p   �L �   M    �M D   �M k   N r   {N 6   �N N   %O F   tO �   �O    YP "   fP Q  �P w  �Q ;  SS ?  �T �   �U �   V �  |W    Y �   -Y �   +Z �   �Z �  �[ �  r] �  h_ �  -b (  �d J   �f X   )g (   �g �   �g �  �h �  ik �  8m �  �n ]  mp i  �q A  5t ?   wu `  �u �  w �  �x    �z   �z �  �{ �   j} o  ~    �    �� 6   �� �   ��    g�   p� �  w� �  n� �   C�   �    �   #� !  A� J   c� u   �� (  $� M  M� �   ��   Q� w   `� N  ؓ    '� A  G� �   �� Q  e� 9  �� i  �   [� 
   d�   o� ]  ��    � �  � v  Ц �  G� �  E� A  � �  V� J   4� A  � �   �� �   �� V   F� s   ��    � =   � #   X� -   |� ,   ��    ׷ �   �� >  Ҹ 6  � �  H�   !� 
   '� �  2� �  �� �  ��   ��   ��   �� 
   �� K  ��    $�    5�   P�   g� �  �� �  S�    E� �  Q� I  �� `  ,� �  �� �  ?� �  � �  �� +   �� �   �� �   �� �  -�   �� �   �� �   �� �   �� �  ]�    4� �   K� J   8� �   ��    � F   � A   f� �  �� �  � :  u 9   � �  � �   � 
   B �   M =   /
 �   m
    <    ~   W    � �  � �  � K  N �  � �   w      �    :  �    � �   � C  � �   � O   � F     �  ]  0   �! �  /" �  % 4  �& /   ') �   W) �   �) �  �* �  S-    D/ )  \/ 2   �0 �   �0 �   �1 7  x2 $  �3 i   �4 0   ?5   p5 5   �6 <  �6 �  �9 �   �; /  �< m   �= I    > �   j> �  6? �  
B I  �C L  $F @   qH 0  �H z  �I �  ^K �   �L �  �M   JP 0   PR \  �R 6   �T A  U k   WW *   �W _  �W �  NZ "  ] >  (_ h  g` �  �a   jd �   �h   @i .  Yk �   �m �  :n �  �q @   Rs �   �s �   ?t *   �t    u w  *u �   �v H   dw J  �w 7  �x d  0{ �  �| �  !   � �  � ;  
�    I� �   b�    L� �  U� ,   � +   G�    s�    y� ;   ��    ψ    �    �    �    %�    <�    L�    U�    ^� )   e� 
   �� .   ��    ̉ 
   щ 
   ܉ 
   � �   � q   � &   W�    ~� <   �� %   ȋ ;   � J   *� &   u� 
   ��    ��    ��    Ҍ    �    �� 
   �    !�    ?�    F� 3   ^� "   �� '   �� 3   ݍ 3   � /   E� +   u�    �� "   �� ;   ؎ "   � Y   7� G   ��    ُ t   �� ]   U�    ��    ϐ '   � "   �    1�    F�    Z�    v�    ��    ��    �� ;   ��    �� 	   �    �    "� =   %�    c�    j� 	   s�    }� 	   ��    ��    ��    �� 	   Ò    ͒    ݒ -   �    �    �    =�    [�    c� 	   v�    �� )   �� 
   ��    ʓ '   ӓ    ��    �    !�     5� 
   V�    a�    s� i   �� �   �� f   �� �   �   ֖ �   �    z� -   ��    Ù    ؙ    ߙ    �    � 3   � j   H� U   �� h   	� )   r� 9   �� )   ֛     � }   �� �   �� h   �� @   a� -   ��    О 
   �    � 
   ��    
�    %�    B�    I�    ]�    f�    o�    �� 
   ��    ��    Ɵ    ՟    �    � 
   !� 
   ,�    7�    F�    S�    o� E  ��    ҡ    ס    � /   �    � `   � a   ~� 4   � G   � N   ]�    ��    ��    �� �   ��    U�    [� 1   v� �   �� !   >�    `� �   {� (   ��   (� p   .� �   �� �   ;� "  �� ?   � b  Y� �   �� �  H�   � #   	�    -� N   <� [   �� q   � _   Y� m   �� K   '� �   s� +   �� 0   '�    X�    _� _   � *   ߵ 
   
�    � 
   ,� %   7� �   ]�    � A    � h   B�    �� $   ȷ _   � d   M� �  ��    i�    �� �   �� 
   �� 
   ��    �� $   ��    ɻ 2   ߻ �  � #   ��    ս    �    � �  � ,   �� 	   �    � i   �� 
   d�    o� /   |� %   �� 
   �� /   �� �   
�    ��    �� r   �� #   Z�    ~�    ��    �� 8   �� v   ��    h� C   y� �   �� {   �� a   '�    �� Z  �� V   �    Y� ;   o�    �� �   �� �   ��    �� N   �� �   �� �   ��    �� "   ��    �� �  � 9   �� -   6� .   d� 2   �� -   �� �   �� #   �� #   �� .   �� T   .�    ��    �� $   �� 7   �� �  
� �  �� ~  �� J   +� >   v�    ��    �� �  ��   l� �   }� *   f� F  �� �  �� ;   �� /   �� �  ,� |  ��    j�    z� ;   ��    �� v  ��    F� v  f� �  �� !  ��    �� �   � F   �� 6   � �   <� �  �� '  Q� �   y� �  %� w   �� Z  v� f   �� �   8� �   %� ^   �� �   T� D   6� C   {�    ��    ��    �� 8   � (   ;� y   d� N   �� 	   -� H   7� �  �� =   � 9  Y� �   �� �   ,� B   �� 2   � (   O�    x� 7   �� 2   �� B   � '   E�   m� %   �  �   �     � �   � ~   � �    v  �   * *   E �   p 	    }   �   � %   ;	   a	    u    �    � '   � <   �        $ Y   - �   � $  u
    �    � )   � �   � Z  � �    �   �    �    �     �    �        !    ? F   G /   � �  � h   L    � �   � �   g Z   � �   R c   � f   Z 
   � m   � T   : �   � l   E `   �     (  - �   V R   � r   4 x   � N        o >    �   � �   H  >   �  �   1!    " h  " W   y# s   �# �   E$    �$ i  % -  q'     �( .   �(    �( 
   �( _  ) 3   c* X   �* �   �* �   �+ �   �, �   i- �   S. �   �. �   �/ f   ~0 a  �0 �   G2 �  �2 �   �4   �5 
   �6 
   �6 
   �6 �   �6 
   �7 
   �7 ^   �7 �   8 �   �8 
   �9 s   �9 S   : �   [: I   �: ~   A; n   �; 
   /< �   =< 
   �< 
   �< 
   
= m  = �   �? �  @    B 
   %B 
   0B �   >B '   �B 5   %C K  [C �   �D -   cE    �E )   �E C   �E @   F K   PF %   �F %   �F �   �F (   �G �  H K  �I �   1K [   �K �   3L   M K  !N �  mP �   �R �   �S �   �T    %U �  =U "   W �  AW !  �X Y  
Z �   d[ �  	\ �   �] �   w^ �   $_ :   �_ J   �_    1` 4   K`    �`    �`    �`   �`    �a �   �a 3   eb 
   �b 
   �b    �b    �b �   �b k   `c Q   �c $  d 9   Ce    }e    �e 3   �e .   �e    �e    f    $f "   -f    Pf    hf    xf    �f :   �f �   �f    wg #   �g    �g +   �g    �g    h     h    ;h    Th 
   ph -   ~h �   �h    Bi I   Zi r   �i �   j �   �j �   �k @  �l �   �m �  �n <   (p �   ep <   	q G   Fq [   �q �   �q +  �r t   �s k   qt    �t �   �t �   vu �   �u    �v     �v    �v    �v    w 3   !w    Uw &   ]w    �w )   �w +   �w +   �w    x    -x )   Gx    qx    ~x    �x    �x    �x    �x :   �x 5   y �   Ay �   �y -   �z /   �z �   �z v   h{ �   �{ ;   �| 7   �| C   �| 9   :} ?   t} :   �}   �} J  �~ B  J� !   �� F   �� �   �� G   ��    �� 4  � �   N� L   �� &   K� �   r� �   _� �   9� <   ƈ �   � D  �� %   ފ 3   �     8� G   Y�    �� n   !�    ��    ��    �� $   �� �   ݌ L   �� 0   ٍ C   
� -   N�    |� %   �� L   �� (   � �   4� V   �� 6  � \   C� i   �� �   
� A   � 9   J� %   �� ?   �� 0   � ,   � 0   H� ,   y� ?   �� B   � �  )� V   � ?   8� R   x� *   ˘    �� �   �� �   �� �   #� �   �    � �   � &   ��    �   � "   
� 2   -� P   `� H   �� �   �� t   }� ^   � &   Q� 5   x� �   �� ?   � 8   �� j   �� $  c� ^   �� �   � �   �� o    � �   �� O   � 9   j� �   �� #   A� �   e�    � �   
� 2   �� @   )�    j� /   ��   �� K   Ū K   � �   ]� o    � W   p� �   Ȭ �   t� 
    �    +� U   C� P   ��    �    �    $�    9�    J� 1   Z� �   �� w   (� 2   ��     Ӱ '   �� !   � '   >� �   f� 2   �� �   *� h   �� +   )� y   U� !  ϳ 4   � m   &� %   �� X   �� F   �    Z� �   t� �   � 9   �� [   � $   =� 0   b� m   ��    � �   � 8   ��    � 1   � h   6� $  �� U   Ļ    �    8� U   V�    �� k   ʼ T   6� �   �� �   N�    ׾ >   � �   .� &   � �   :� O   
� 8   ]� i   ��     �    !�    -�    /�    1� 0   Q� ?   �� d   ��    '�    C� 
   V� #   a� C   �� J   ��    � l   (� V   ��    �� &   � ;   2�    n�    �� i   �� 1  � _   A�    ��    �� �   �� �  �� p   *�    �� �  �� 9  a� E   �� �   �� ,   h�   �� H   �� l   ��    b�    �� 6  �� +   �� x   �� �   i� z   � y   �� /   � h   7� L   �� $   �� �   � O   �� <   �� c   '� C   �� Z   �� �   *� �   �� ?   �� �   �� �  �� �   _� 6   M� �  �� A  $� �   f� w  Y� �   �� <   ��    �� �   � >   �� �  &� �   �� O   >�    �� %   �� b  ��    .� 
  4� o  B� c  �� �  � Y   �� k   � x   |� A   �� B   7� p   z� �   �� +   q� /   �� H   �� 4   � "   K� F   n� �   �� 9   :�    t� ~   }� )  �� �   &�      �    !� 
   >� g   I� v   �� |   (� �   �� �   ~�     � ,   � /  G� 
   w  �   �  ^  ' 4  � P   � -       :    @    I L   M <   � �   � 2  k {   �    	    2	 *   J	     u	 v   �	    
 F   
    f
 0   o
 3   �
    �
    �
 t   �
 
   i '   t ,   � %   �    � �   � �   ~ }   v
 �   �
 W   � 
  �    � +   � �   ( �   ! 
      ! ~   * d   � K    {   Z }   � i   T    � j   �    E    Y    o    �    �    �    �    �     
    E   * E   p E   � 2   � C   / 7   s 0   � %   �     Z    -   t    � H   � -   � u   " 1   � ;   �      '   '    O    l �   � i    �   v           =    \    w B   � 	   �    �    � �        � $   �    � 
   � 	     K        [  u  y     �!    "    " )   2" 2   \"    �" /   �" 5   �" 5   # A   F# E   �#    �# �   �# D   d$ %   �$    �$ B   �$ T   #% "   x% ?   �% (   �% �   & f   �& W   ' 
   `'    k' 	   �'    �'    �' $   �' �  �' �   �)    S*    n* ;   r*    �* +   �*    �*    �* �   + T   �+ �   ,    �, 1   - �   @- 1   �- -   . 0   6. 3   g. ?   �. 3   �.    / 
   ,/ 6   :/    q/ ;   �/ G   �/ �  0 N   �1 O   �1 a   C2 !   �2 �   �2 *   l3    �3 h   �3    4 %   ,4     R4 T   s4 N   �4 -   5 �   E5    �5 
   6 %   6    @6    W6    p6 &   �6    �6     �6 V   �6 9  A7 )   {8 &   �8 	  �8 �   �9 #   �: #   �:    �:    �:    �:    ;    /;    H;    ];    o;    �;    �; 
   �;    �;    �;    �;    �;    �; 1   < %  5< 3  [= �  �> �  j@   �B *  E �  6F    �G b  �G    SI 9  lI 
  �J   �K �   �M �  �N 4   �P �   �P U   �Q )   R n   IR |   �R �   5S ~   �S �   AT �   U �   V �  W    �Y �   �Y 	  �Z y   �[ �   S\    ] �   '] �  &^ �   �_ �   �`    �a }   �a �   ]b ,  �b �   d �   �d �   ge    f '   -f    Uf c   uf ;   �f    g �   +g T   �g �   h    �h �   �h �   �i Y   1j \   �j i   �j w   Rk e   �k ^   0l �   �l n   !m �   �m �   :n    �n 0   o 1   2o s   do 9   �o    p    p `   (p    �p    �p    �p I   �p :   q T   Vq %   �q    �q 
   �q    �q 	   �q }   �q �   wr Y   �r 6   Xs    �s '   �s 8   �s    �s 
   
t    t    t    't 
   .t    9t    Bt    Rt 
   Yt    gt    }t    �t    �t    �t    �t 
   �t 
   �t    �t    u )   u '   Au "   iu �   �u    v |   2v �   �v 
   �w    �w    �w 	   �w    �w    �w 
   �w �   �w �   �x W   Iy    �y #   �y �   �y    _z �   uz �   '{ 3   �{    | �   | %  �| �   �} �   q~ �   h 0   � �   � !   ŀ "   � `   
� �   k� (   � �   >� �   �� �   �� b   �    � 
   ��    � 
   !�    ,�    E�    U�    g� �   �� �   8� �   φ �   o� �   O� s   ;� Y   �� /  	�   9� $  O� �   t� a  M� �  �� 
   Q�   \� l  b� �   ϕ J  �� 6  �� �   6� �  ל    �� E   �� �   � �  �� O   c� 1  �� 
   � 
   �    �� �   � _   ͣ �   -� 8   �� q   � X   [� v   �� 2   +� �   ^� [   K� +   �� g   ӧ   ;�    N�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: he
Language-Team: he <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library היא ספרייה פופולרית (ולא חוקית). הם לקחו את אוסף Library Genesis והפכו אותו לחיפוש בקלות. בנוסף לכך, הם הפכו ליעילים מאוד בגיוס תרומות ספרים חדשים, על ידי תמרוץ משתמשים תורמים עם הטבות שונות. נכון לעכשיו הם לא תורמים את הספרים החדשים האלה בחזרה ל-Library Genesis. ובניגוד ל-Library Genesis, הם לא הופכים את האוסף שלהם למראה בקלות, מה שמונע שימור רחב. זה חשוב למודל העסקי שלהם, מכיוון שהם גובים כסף עבור גישה לאוסף שלהם בכמות גדולה (יותר מ-10 ספרים ביום). איננו מביעים שיפוט מוסרי על גביית כסף עבור גישה בכמות גדולה לאוסף ספרים לא חוקי. אין ספק ש-Z-Library הצליחו להרחיב את הגישה לידע ולהשיג יותר ספרים. אנו כאן פשוט כדי לעשות את חלקנו: להבטיח את השימור לטווח ארוך של האוסף הפרטי הזה. - אנה והצוות (<a %(reddit)s>Reddit</a>) בשחרור המקורי של מראה ספריית הפיראטים (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון אנה</a>), יצרנו מראה של Z-Library, אוסף ספרים לא חוקי גדול. כתזכורת, זה מה שכתבנו בפוסט הבלוג המקורי ההוא: האוסף הזה מתוארך לאמצע 2021. בינתיים, Z-Library גדל בקצב מדהים: הם הוסיפו כ-3.8 מיליון ספרים חדשים. יש שם כמה כפילויות, כמובן, אבל הרוב נראה כספרים חדשים לגיטימיים, או סריקות באיכות גבוהה יותר של ספרים שהוגשו בעבר. זה בעיקר בזכות העלייה במספר המנחים המתנדבים ב-Z-Library, ומערכת ההעלאה בכמות גדולה שלהם עם הסרת כפילויות. אנו רוצים לברך אותם על ההישגים הללו. אנו שמחים להודיע שקיבלנו את כל הספרים שנוספו ל-Z-Library בין המראה האחרון שלנו לאוגוסט 2022. גם חזרנו ואספנו כמה ספרים שפספסנו בפעם הראשונה. בסך הכל, האוסף החדש הזה הוא כ-24TB, שהוא הרבה יותר גדול מהקודם (7TB). המראה שלנו הוא כעת 31TB בסך הכל. שוב, הסרנו כפילויות מול Library Genesis, שכן כבר קיימים טורנטים זמינים לאוסף הזה. אנא גשו למראה ספריית הפיראטים כדי לבדוק את האוסף החדש (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון של אנה</a>). יש שם מידע נוסף על איך הקבצים מאורגנים, ומה השתנה מאז הפעם האחרונה. לא נקשר אליו מכאן, שכן זהו רק אתר בלוגים שאינו מארח חומרים לא חוקיים. כמובן, שיתוף הוא גם דרך מצוינת לעזור לנו. תודה לכל מי שמשתף את סט הטורנטים הקודם שלנו. אנו אסירי תודה על התגובה החיובית, ושמחים שיש כל כך הרבה אנשים שאכפת להם משימור הידע והתרבות בדרך יוצאת דופן זו. 3x ספרים חדשים נוספו למראה ספריית הפיראטים (+24TB, 3.8 מיליון ספרים) קראו את המאמרים הנלווים מאת TorrentFreak: <a %(torrentfreak)s>ראשון</a>, <a %(torrentfreak_2)s>שני</a> - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) מאמרים נלווים מאת TorrentFreak: <a %(torrentfreak)s>ראשון</a>, <a %(torrentfreak_2)s>שני</a> לא מזמן, "ספריות צללים" היו גוססות. Sci-Hub, הארכיון הבלתי חוקי העצום של מאמרים אקדמיים, הפסיק לקבל עבודות חדשות, בשל תביעות משפטיות. "Z-Library", הספרייה הבלתי חוקית הגדולה ביותר של ספרים, ראתה את יוצריה לכאורה נעצרים באשמות של הפרת זכויות יוצרים פלילית. הם הצליחו להימלט ממעצרם, אך ספרייתם עדיין נמצאת תחת איום. חלק מהמדינות כבר עושות גרסה של זה. TorrentFreak <a %(torrentfreak)s>דיווח</a> שסין ויפן הציגו חריגות AI לחוקי זכויות היוצרים שלהן. לא ברור לנו כיצד זה מתקשר עם אמנות בינלאומיות, אך זה בהחלט נותן כיסוי לחברות המקומיות שלהן, מה שמסביר את מה שראינו. לגבי ארכיון אנה — נמשיך בעבודתנו המחתרתית המושרשת באמונה מוסרית. עם זאת, המשאלה הגדולה ביותר שלנו היא להיכנס לאור, ולהגביר את השפעתנו באופן חוקי. אנא בצעו רפורמה בזכויות יוצרים. כאשר Z-Library עמדה בפני סגירה, כבר גיביתי את כל הספרייה שלה וחיפשתי פלטפורמה לאחסן אותה. זו הייתה המוטיבציה שלי להתחיל את ארכיון אנה: המשך המשימה מאחורי היוזמות הקודמות. מאז גדלנו להיות הספרייה הצללית הגדולה בעולם, המאחסנת יותר מ-140 מיליון טקסטים מוגנים בזכויות יוצרים בפורמטים שונים — ספרים, מאמרים אקדמיים, מגזינים, עיתונים ועוד. הצוות שלי ואני אידיאולוגים. אנו מאמינים כי שמירה ואחסון של קבצים אלו הוא מוסרי. ספריות ברחבי העולם רואות קיצוצים במימון, ואיננו יכולים לסמוך על מורשת האנושות לחברות. ואז הגיעה הבינה המלאכותית. כמעט כל החברות הגדולות הבונות LLMs פנו אלינו כדי להתאמן על הנתונים שלנו. רוב החברות מבוססות ארה"ב (אך לא כולן!) שינו את דעתן לאחר שהבינו את האופי הבלתי חוקי של עבודתנו. לעומת זאת, חברות סיניות אימצו בהתלהבות את האוסף שלנו, כנראה ללא דאגה לחוקיותו. זה בולט בהתחשב בתפקיד של סין כחתומה על כמעט כל האמנות הבינלאומיות הגדולות לזכויות יוצרים. נתנו גישה מהירה לכ-30 חברות. רובם הם חברות LLM, וחלקם הם מתווכי נתונים, שימכרו מחדש את האוסף שלנו. רובם סיניים, אם כי עבדנו גם עם חברות מארה"ב, אירופה, רוסיה, דרום קוריאה ויפן. DeepSeek <a %(arxiv)s>הודתה</a> שגרסה קודמת אומנה על חלק מהאוסף שלנו, אם כי הם שומרים על שתיקה לגבי הדגם האחרון שלהם (כנראה גם אומן על הנתונים שלנו). אם המערב רוצה להישאר קדימה במרוץ ה-LLMs, ובסופו של דבר, ה-AGI, עליו לשקול מחדש את עמדתו על זכויות יוצרים, ובקרוב. בין אם אתם מסכימים איתנו או לא על המקרה המוסרי שלנו, זה הופך כעת למקרה של כלכלה, ואפילו של ביטחון לאומי. כל גושי הכוח בונים מדענים-על מלאכותיים, האקרים-על, וצבאות-על. חופש המידע הופך לעניין של הישרדות עבור מדינות אלו — אפילו לעניין של ביטחון לאומי. הצוות שלנו מגיע מכל רחבי העולם, ואין לנו יישור מסוים. אבל היינו מעודדים מדינות עם חוקי זכויות יוצרים חזקים להשתמש באיום הקיומי הזה כדי לשנות אותם. אז מה לעשות? ההמלצה הראשונה שלנו היא פשוטה: לקצר את תקופת זכויות היוצרים. בארה"ב, זכויות יוצרים מוענקות ל-70 שנה לאחר מותו של המחבר. זה אבסורדי. אנו יכולים להביא זאת בקנה אחד עם פטנטים, המוענקים ל-20 שנה לאחר ההגשה. זה צריך להיות יותר ממספיק זמן למחברים של ספרים, מאמרים, מוזיקה, אמנות ועבודות יצירתיות אחרות, לקבל פיצוי מלא על מאמציהם (כולל פרויקטים ארוכי טווח כמו עיבודים לסרטים). לאחר מכן, לכל הפחות, על קובעי המדיניות לכלול חריגות לשימור המוני והפצה של טקסטים. אם אובדן הכנסות מלקוחות פרטיים הוא הדאגה העיקרית, הפצה ברמה האישית יכולה להישאר אסורה. בתמורה, אלו המסוגלים לנהל מאגרים עצומים — חברות המאמנות LLMs, יחד עם ספריות וארכיונים אחרים — יכוסו על ידי חריגות אלו. רפורמת זכויות יוצרים נחוצה לביטחון הלאומי בקצרה: LLMs סיניים (כולל DeepSeek) מאומנים על הארכיון הבלתי חוקי שלי של ספרים ומאמרים — הגדול בעולם. המערב צריך לשנות את חוקי זכויות היוצרים כעניין של ביטחון לאומי. אנא עיינו ב<a %(all_isbns)s>פוסט המקורי בבלוג</a> למידע נוסף. הצבנו אתגר לשפר את זה. היינו מעניקים פרס ראשון של $6,000, פרס שני של $3,000, ופרס שלישי של $1,000. בשל התגובה המדהימה וההגשות המדהימות, החלטנו להגדיל מעט את מאגר הפרסים, ולהעניק פרס שלישי לארבעה משתתפים בסך $500 כל אחד. הזוכים מופיעים למטה, אך הקפידו להסתכל על כל ההגשות <a %(annas_archive)s>כאן</a>, או להוריד את ה<a %(a_2025_01_isbn_visualization_files)s>טורנט המשולב שלנו</a>. מקום ראשון $6,000: phiresky ה<a %(phiresky_github)s>הגשה</a> הזו (<a %(annas_archive_note_2951)s>תגובה ב-Gitlab</a>) היא פשוט כל מה שרצינו, ועוד! במיוחד אהבנו את אפשרויות הוויזואליזציה הגמישות להפליא (אפילו תומכות ב-shaders מותאמים אישית), אך עם רשימה מקיפה של הגדרות קבועות מראש. אהבנו גם כמה הכל מהיר וחלק, היישום הפשוט (שאפילו אין לו backend), המפה המינימלית החכמה, וההסבר המפורט ב<a %(phiresky_github)s>פוסט בבלוג</a> שלהם. עבודה מדהימה, וזוכה ראוי בהחלט! - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ליבנו מלא בהכרת תודה. רעיונות בולטים גורדי שחקים לנדירות הרבה סליידרים להשוואת Datasets, כאילו אתם תקליטנים. סרגל קנה מידה עם מספר הספרים. תוויות יפות. ערכת צבעים מגניבה כברירת מחדל ומפת חום. תצוגת מפה ייחודית ומסננים הערות, וגם סטטיסטיקות חיות סטטיסטיקות חיות עוד כמה רעיונות ויישומים שאהבנו במיוחד: יכולנו להמשיך עוד זמן מה, אבל נעצור כאן. הקפידו להסתכל על כל ההגשות <a %(annas_archive)s>כאן</a>, או להוריד את ה-<a %(a_2025_01_isbn_visualization_files)s>טורנט המשולב שלנו</a>. כל כך הרבה הגשות, וכל אחת מביאה פרספקטיבה ייחודית, בין אם בממשק המשתמש או ביישום. נשלב לפחות את ההגשה במקום הראשון באתר הראשי שלנו, ואולי גם אחרות. התחלנו גם לחשוב על איך לארגן את תהליך הזיהוי, האישור ולאחר מכן הארכוב של הספרים הנדירים ביותר. עוד יבוא בתחום זה. תודה לכל מי שהשתתף. זה מדהים שכל כך הרבה אנשים אכפת להם. מעבר קל בין Datasets להשוואות מהירות. כל ה-ISBNים מספרי SSNO של CADAL דליפת נתונים של CERLALC מספרי SSID של DuXiu אינדקס הספרים האלקטרוניים של EBSCOhost גוגל ספרים גודרידס ארכיון האינטרנט ISBNdb רישום המו"לים הגלובלי של ISBN ליבי קבצים בארכיון של אנה Nexus/STC OCLC/Worldcat אופנ-לייברי(OpenLibary) הספרייה הלאומית של רוסיה הספרייה הקיסרית של טרנטור מקום שני $3,000: hypha “בעוד ריבועים ומלבנים מושלמים הם נעימים מבחינה מתמטית, הם לא מספקים מקומיות עליונה בהקשר של מיפוי. אני מאמין שהאסימטריה הטבועה בעקומות הילברט או מורטון הקלאסיות אינה פגם אלא תכונה. כמו קו המתאר המפורסם בצורת מגף של איטליה שהופך אותה למוכרת מיד על מפה, ה"מאפיינים" הייחודיים של עקומות אלו עשויים לשמש כנקודות ציון קוגניטיביות. ייחודיות זו יכולה לשפר את הזיכרון המרחבי ולעזור למשתמשים להתמצא, מה שעשוי להקל על איתור אזורים ספציפיים או זיהוי דפוסים.” עוד <a %(annas_archive_note_2913)s>הגשה</a> מדהימה. לא גמישה כמו המקום הראשון, אבל למעשה העדפנו את הוויזואליזציה ברמת המאקרו שלה על פני המקום הראשון (עקומת מילוי חלל, גבולות, תיוג, הדגשה, תנועה והתקרבות). <a %(annas_archive_note_2971)s>תגובה</a> של ג'ו דייוויס הדהדה אצלנו: ועדיין יש הרבה אפשרויות לוויזואליזציה והדמיה, כמו גם ממשק משתמש חלק ואינטואיטיבי להפליא. מקום שני מוצק! - אנה והצוות (<a %(reddit)s>Reddit</a>) לפני כמה חודשים הכרזנו על <a %(all_isbns)s>פרס בסך 10,000 דולר</a> ליצירת ההדמיה הטובה ביותר האפשרית של הנתונים שלנו המציגה את מרחב ה-ISBN. הדגשנו להראות אילו קבצים כבר ארכנו/לא ארכנו, ובהמשך הוספנו מערך נתונים המתאר כמה ספריות מחזיקות ב-ISBNs (מדד של נדירות). התגובה הייתה מדהימה. הייתה כל כך הרבה יצירתיות. תודה גדולה לכל מי שהשתתף: האנרגיה וההתלהבות שלכם מדבקות! בסופו של דבר רצינו לענות על השאלות הבאות: <strong>אילו ספרים קיימים בעולם, כמה מהם כבר ארכנו, ועל אילו ספרים עלינו להתמקד בהמשך?</strong> זה נהדר לראות כל כך הרבה אנשים שמעניין אותם השאלות הללו. התחלנו עם ויזואליזציה בסיסית בעצמנו. בפחות מ-300 קילובייט, התמונה הזו מייצגת בצורה תמציתית את "רשימת הספרים" הפתוחה הגדולה ביותר שנאספה אי פעם בהיסטוריה של האנושות: מקום שלישי $500 #1: maxlion ב<a %(annas_archive_note_2940)s>הגשה</a> זו אהבנו מאוד את סוגי התצוגות השונים, במיוחד את תצוגות ההשוואה והמו"ל. מקום שלישי $500 #2: abetusk למרות שממשק המשתמש אינו המלוטש ביותר, ה<a %(annas_archive_note_2917)s>הגשה</a> הזו עונה על הרבה מהדרישות. במיוחד אהבנו את תכונת ההשוואה שלה. מקום שלישי $500 #3: conundrumer0 כמו המקום הראשון, ה<a %(annas_archive_note_2975)s>הגשה</a> הזו הרשימה אותנו בגמישותה. בסופו של דבר זה מה שהופך כלי ויזואליזציה נהדר: גמישות מרבית למשתמשים מתקדמים, תוך שמירה על פשטות למשתמשים ממוצעים. מקום שלישי $500 #4: charelf ה<a %(annas_archive_note_2947)s>הגשה</a> האחרונה שזכתה בפרס היא די בסיסית, אך יש לה כמה תכונות ייחודיות שאהבנו מאוד. אהבנו איך הם מראים כמה Datasets מכסים ISBN מסוים כמדד לפופולריות/אמינות. אהבנו גם את הפשטות אך היעילות של שימוש במחוון שקיפות להשוואות. זוכי פרס הדמיית ISBN בסך 10,000 דולר בקצרה: קיבלנו הגשות מדהימות לפרס הדמיית ISBN בסך 10,000 דולר. רקע כיצד ארכיון אנה יכול להשיג את משימתו לגבות את כל הידע האנושי, מבלי לדעת אילו ספרים עדיין קיימים? אנו זקוקים לרשימת TODO. דרך אחת למפות זאת היא באמצעות מספרי ISBN, אשר מאז שנות ה-70 הוקצו לכל ספר שפורסם (ברוב המדינות). אין רשות מרכזית שיודעת את כל ההקצאות של ISBN. במקום זאת, זהו מערכת מבוזרת, שבה מדינות מקבלות טווחי מספרים, שמוקצים לאחר מכן לטווחים קטנים יותר למוציאים לאור גדולים, שיכולים לחלק עוד את הטווחים למוציאים לאור קטנים יותר. לבסוף, מספרים בודדים מוקצים לספרים. התחלנו למפות ISBNs <a %(blog)s>לפני שנתיים</a> עם הגרידה שלנו של ISBNdb. מאז, גרדנו מקורות metadata רבים נוספים, כגון <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, ועוד. רשימה מלאה ניתן למצוא בעמודי "Datasets" ו-"Torrents" בארכיון אנה. כעת יש לנו את האוסף הפתוח והניתן להורדה בקלות הגדול ביותר בעולם של metadata של ספרים (ולכן גם ISBNs). <a %(blog)s>כתבנו בהרחבה</a> על מדוע אכפת לנו משימור, ומדוע אנו נמצאים כעת בחלון קריטי. עלינו לזהות כעת ספרים נדירים, לא ממוקדים וייחודיים בסיכון ולשמר אותם. קיום metadata טוב על כל הספרים בעולם עוזר בכך. פרס של $10,000 תינתן התחשבות רבה לשימושיות ולמראה טוב. הציגו metadata בפועל עבור ISBNs בודדים בעת זום פנימה, כגון כותרת ומחבר. עקומת מילוי חלל טובה יותר. לדוגמה, זיג-זג, שעובר מ-0 ל-4 בשורה הראשונה ואז חזרה (בכיוון הפוך) מ-5 ל-9 בשורה השנייה — מיושם באופן רקורסיבי. סכמות צבע שונות או מותאמות אישית. תצוגות מיוחדות להשוואת Datasets. דרכים לניפוי בעיות, כמו metadata אחרות שאינן תואמות היטב (למשל, כותרות שונות מאוד). הוספת הערות לתמונות עם תגובות על ISBNs או טווחים. כל שיטות לזיהוי ספרים נדירים או בסיכון. כל רעיון יצירתי שתוכלו להעלות על דעתכם! קוד הקוד ליצירת התמונות הללו, כמו גם דוגמאות נוספות, ניתן למצוא ב<a %(annas_archive)s>ספרייה זו</a>. פיתחנו פורמט נתונים קומפקטי, שבו כל המידע הנדרש על ISBN הוא כ-75MB (דחוס). תיאור פורמט הנתונים והקוד ליצירתו ניתן למצוא <a %(annas_archive_l1244_1319)s>כאן</a>. עבור הפרס אינכם נדרשים להשתמש בזה, אך זהו כנראה הפורמט הנוח ביותר להתחיל איתו. אתם יכולים לשנות את ה-metadata שלנו איך שתרצו (אם כי כל הקוד שלכם חייב להיות קוד פתוח). אנחנו לא יכולים לחכות לראות מה תמציאו. בהצלחה! פצלו את הריפו הזה, וערכו את פוסט הבלוג HTML הזה (לא מותרים שום backends אחרים מלבד ה- Flask backend שלנו). הפכו את התמונה לעיל לזום חלק, כך שתוכלו לזום עד ל-ISBNs בודדים. לחיצה על ISBNs צריכה להוביל לדף metadata או חיפוש בארכיון אנה. עליכם עדיין להיות מסוגלים לעבור בין כל ה-Datasets השונים. טווחי מדינות וטווחי מוציאים לאור צריכים להיות מודגשים בעת ריחוף. תוכלו להשתמש לדוגמה ב-<a %(github_xlcnd_isbnlib)s>data4info.py ב-isbnlib</a> למידע על מדינות, ובגרידת "isbngrp" שלנו למוציאים לאור (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). זה חייב לעבוד היטב על מחשבים שולחניים וניידים. יש הרבה מה לחקור כאן, ולכן אנו מכריזים על פרס לשיפור ההדמיה לעיל. בניגוד לרוב הפרסים שלנו, זהו פרס מוגבל בזמן. עליכם <a %(annas_archive)s>להגיש</a> את קוד המקור הפתוח שלכם עד 2025-01-31 (23:59 UTC). ההגשה הטובה ביותר תקבל $6,000, המקום השני יקבל $3,000, והמקום השלישי יקבל $1,000. כל הפרסים יוענקו באמצעות Monero (XMR). להלן הקריטריונים המינימליים. אם אף הגשה לא תעמוד בקריטריונים, ייתכן שנעניק פרסים מסוימים, אך זה יהיה לפי שיקול דעתנו. לנקודות בונוס (אלו רק רעיונות — תנו ליצירתיות שלכם להשתולל): אתם יכולים לסטות לחלוטין מהקריטריונים המינימליים ולעשות ויזואליזציה שונה לחלוטין. אם זה באמת מרהיב, זה יזכה בפרס, אך לפי שיקול דעתנו. בצעו הגשות על ידי פרסום תגובה ל<a %(annas_archive)s>בעיה זו</a> עם קישור למאגר המפוצל שלכם, בקשת מיזוג או הבדל. - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) תמונה זו היא בגודל 1000×800 פיקסלים. כל פיקסל מייצג 2,500 ISBNs. אם יש לנו קובץ עבור ISBN, אנו הופכים את הפיקסל לירוק יותר. אם אנו יודעים ש-ISBN הונפק, אך אין לנו קובץ תואם, אנו הופכים אותו לאדום יותר. בפחות מ-300kb, תמונה זו מייצגת בקצרה את "רשימת הספרים" הפתוחה הגדולה ביותר שאי פעם הורכבה בהיסטוריה של האנושות (כמה מאות GB דחוסים במלואם). זה גם מראה: יש עוד הרבה עבודה לגיבוי ספרים (יש לנו רק 16%). הדמיית כל ה-ISBNs — פרס של $10,000 עד 2025-01-31 תמונה זו מייצגת את "רשימת הספרים" הפתוחה הגדולה ביותר שאי פעם הורכבה בהיסטוריה של האנושות. הדמיה מלבד תמונת המבט הכללי, אנו יכולים גם להסתכל על Datasets בודדים שרכשנו. השתמשו בתפריט הנפתח ובכפתורים כדי לעבור ביניהם. יש הרבה דפוסים מעניינים לראות בתמונות אלו. מדוע ישנה סדירות של קווים ובלוקים, שנראה כי היא מתרחשת בקני מידה שונים? מהן האזורים הריקים? מדוע Datasets מסוימים כל כך מקובצים? נשאיר את השאלות הללו כתרגיל לקורא. - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) סיכום עם התקן הזה, נוכל לבצע שחרורים בצורה יותר הדרגתית, ולהוסיף מקורות נתונים חדשים בקלות רבה יותר. כבר יש לנו כמה שחרורים מרגשים בצנרת! אנו גם מקווים שיהיה קל יותר לספריות צללים אחרות לשקף את האוספים שלנו. אחרי הכל, המטרה שלנו היא לשמר את הידע והתרבות האנושיים לנצח, כך שככל שיש יותר יתירות, כך טוב יותר. דוגמה בואו נבחן את השחרור האחרון שלנו של Z-Library כדוגמה. הוא מורכב משתי אוספים: "<span style="background: #fffaa3">zlib3_records</span>" ו-"<span style="background: #ffd6fe">zlib3_files</span>". זה מאפשר לנו לגרד ולשחרר בנפרד רשומות מטאדאטה מקבצי הספרים עצמם. לכן, שחררנו שני טורנטים עם קבצי מטאדאטה: שחררנו גם מספר טורנטים עם תיקיות נתונים בינאריים, אך רק עבור אוסף "<span style="background: #ffd6fe">zlib3_files</span>", סך הכל 62: על ידי הרצת <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> נוכל לראות מה בפנים: במקרה זה, מדובר במטאדאטה של ספר כפי שדווח על ידי Z-Library. ברמה העליונה יש לנו רק "aacid" ו-"metadata", אך אין "data_folder", מכיוון שאין נתונים בינאריים תואמים. ה-AACID מכיל "22430000" כזיהוי ראשי, שנראה שנלקח מ-"zlibrary_id". אנו יכולים לצפות ש-AACs אחרים באוסף זה יהיו בעלי מבנה דומה. עכשיו נריץ <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: זהו מטאדאטה AAC קטן בהרבה, אם כי עיקר ה-AAC הזה ממוקם במקום אחר בקובץ בינארי! אחרי הכל, יש לנו "data_folder" הפעם, כך שנוכל לצפות שהנתונים הבינאריים התואמים ימוקמו ב-<code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. ה-"metadata" מכיל את ה-"zlibrary_id", כך שנוכל בקלות לשייך אותו ל-AAC התואם באוסף "zlib_records". יכולנו לשייך בדרכים שונות, למשל דרך AACID — התקן לא מחייב זאת. שימו לב שאין הכרח שהשדה "metadata" יהיה בעצמו JSON. הוא יכול להיות מחרוזת המכילה XML או כל פורמט נתונים אחר. ניתן אפילו לאחסן מידע מטאדאטה בבלוב הבינארי המשויך, למשל אם מדובר בכמות גדולה של נתונים. קבצים ו-metadata הטרוגניים, קרובים ככל האפשר לפורמט המקורי. נתונים בינאריים יכולים להיות מוגשים ישירות על ידי שרתי אינטרנט כמו Nginx. מזהים הטרוגניים בספריות המקור, או אפילו היעדר מזהים. שחרורים נפרדים של metadata לעומת נתוני קבצים, או שחרורים של metadata בלבד (למשל, השחרור שלנו של ISBNdb). הפצה דרך טורנטים, אך עם אפשרות לשיטות הפצה אחרות (למשל, IPFS). רשומות בלתי ניתנות לשינוי, מכיוון שעלינו להניח שהטורנטים שלנו יחיו לנצח. שחרורים מצטברים / שחרורים ניתנים להוספה. קריא וניתן לכתיבה על ידי מכונה, בנוחות ובמהירות, במיוחד עבור הסטאק שלנו (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). בדיקה אנושית קלה במידה מסוימת, אם כי זה משני לקריאות מכונה. קל לזרוע את האוספים שלנו עם seedbox סטנדרטי מושכר. מטרות עיצוב לא אכפת לנו שהקבצים יהיו קלים לניווט ידני על הדיסק, או ניתנים לחיפוש ללא עיבוד מקדים. לא אכפת לנו להיות תואמים ישירות לתוכנות ספרייה קיימות. למרות שזה צריך להיות קל לכל אחד לזרוע את האוסף שלנו באמצעות טורנטים, אנחנו לא מצפים שהקבצים יהיו ניתנים לשימוש ללא ידע טכני משמעותי ומחויבות. המקרה העיקרי שלנו הוא הפצת קבצים ו-metadata נלווה מאוספים קיימים שונים. השיקולים החשובים ביותר שלנו הם: כמה מטרות שאינן: מכיוון שארכיון אנה הוא קוד פתוח, אנו רוצים להשתמש בפורמט שלנו ישירות. כאשר אנו מרעננים את אינדקס החיפוש שלנו, אנו ניגשים רק לנתיבים הזמינים לציבור, כך שכל מי שמפצל את הספרייה שלנו יוכל להתחיל לפעול במהירות. <strong>AAC.</strong> AAC (מיכל ארכיון אנה) הוא פריט יחיד המורכב מ-<strong>metadata</strong>, ובאופן אופציונלי <strong>נתונים בינאריים</strong>, שניהם בלתי ניתנים לשינוי. יש לו מזהה ייחודי גלובלי, הנקרא <strong>AACID</strong>. <strong>AACID.</strong> הפורמט של AACID הוא כך: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. לדוגמה, AACID אמיתי ששחררנו הוא <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>טווח AACID.</strong> מכיוון ש-AACIDs מכילים חותמות זמן גדלות מונוטונית, אנו יכולים להשתמש בזה כדי לציין טווחים בתוך אוסף מסוים. אנו משתמשים בפורמט זה: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, כאשר חותמות הזמן כוללות. זה עקבי עם סימון ISO 8601. טווחים הם רציפים, ויכולים לחפוף, אך במקרה של חפיפה חייבים להכיל רשומות זהות לאלו ששוחררו בעבר באותו אוסף (מכיוון ש-AACs הם בלתי ניתנים לשינוי). רשומות חסרות אינן מותרות. <code>{collection}</code>: שם האוסף, שיכול להכיל אותיות ASCII, מספרים וקווים תחתונים (אך לא קווים תחתונים כפולים). <code>{collection-specific ID}</code>: מזהה ייחודי לאוסף, אם יש, לדוגמה מזהה Z-Library. ניתן להשמיט או לקצר. חייבים להשמיט או לקצר אם ה-AACID יעלה על 150 תווים. <code>{ISO 8601 timestamp}</code>: גרסה קצרה של ISO 8601, תמיד ב-UTC, לדוגמה <code>20220723T194746Z</code>. מספר זה חייב לגדול באופן מונוטוני עבור כל שחרור, אם כי המשמעות המדויקת שלו יכולה להשתנות לפי אוסף. אנו מציעים להשתמש בזמן הגירוד או יצירת המזהה. <code>{shortuuid}</code>: UUID אך דחוס ל-ASCII, לדוגמה באמצעות base57. אנו משתמשים כרגע בספריית Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>תיקיית נתונים בינאריים.</strong> תיקייה עם הנתונים הבינאריים של טווח AACs, עבור אוסף מסוים. יש להם את המאפיינים הבאים: התיקייה חייבת להכיל קבצי נתונים עבור כל ה-AACs בטווח המוגדר. כל קובץ נתונים חייב להיות עם ה-AACID שלו כשם הקובץ (ללא סיומות). שם התיקייה חייב להיות טווח AACID, עם קידומת <code style="color: green">annas_archive_data__</code>, וללא סיומת. לדוגמה, אחד מהשחרורים האמיתיים שלנו יש תיקייה שנקראת<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. מומלץ להפוך את התיקיות הללו לנוחות לניהול בגודל, למשל לא גדולות מ-100GB-1TB כל אחת, אם כי המלצה זו עשויה להשתנות עם הזמן. <strong>אוסף.</strong> כל AAC שייך לאוסף, שהוא לפי הגדרה רשימה של AACs שהם עקביים מבחינה סמנטית. זה אומר שאם אתה מבצע שינוי משמעותי בפורמט של ה-metadata, אז עליך ליצור אוסף חדש. התקן <strong>קובץ metadata.</strong> קובץ metadata מכיל את ה-metadata של טווח AACs, עבור אוסף מסוים. יש להם את המאפיינים הבאים: <code>data_folder</code> הוא אופציונלי, והוא שם התיקייה של הנתונים הבינאריים שמכילה את הנתונים הבינאריים המתאימים. שם הקובץ של הנתונים הבינאריים המתאימים בתוך התיקייה הוא ה-AACID של הרשומה. כל אובייקט JSON חייב להכיל את השדות הבאים ברמה העליונה: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (אופציונלי). לא מותרים שדות אחרים. שם הקובץ חייב להיות טווח AACID, עם קידומת <code style="color: red">annas_archive_meta__</code> ומסתיים ב-<code>.jsonl.zstd</code>. לדוגמה, אחד מהשחרורים שלנו נקרא<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. כפי שמצוין על ידי סיומת הקובץ, סוג הקובץ הוא <a %(jsonlines)s>JSON Lines</a> דחוס עם <a %(zstd)s>Zstandard</a>. <code>metadata</code> הוא metadata שרירותי, לפי המשמעות של האוסף. הוא חייב להיות עקבי מבחינה סמנטית בתוך האוסף. הקידומת <code style="color: red">annas_archive_meta__</code> יכולה להיות מותאמת לשם המוסד שלך, לדוגמה <code style="color: red">my_institute_meta__</code>. <strong>אוספי "רשומות" ו"קבצים".</strong> לפי מוסכמה, זה לעיתים קרובות נוח לשחרר "רשומות" ו"קבצים" כאוספים שונים, כך שניתן לשחרר אותם בלוחות זמנים שונים, למשל בהתבסס על קצבי גירוד. "רשומה" היא אוסף של metadata בלבד, המכיל מידע כמו כותרות ספרים, מחברים, ISBNs, וכו', בעוד "קבצים" הם האוספים שמכילים את הקבצים עצמם (pdf, epub). בסופו של דבר, החלטנו על תקן פשוט יחסית. הוא די גמיש, לא נורמטיבי, ועבודה בתהליך. <strong>טורנטים.</strong> קבצי המטאדאטה ותיקיות הנתונים הבינאריים עשויים להיות מאוגדים בטורנטים, עם טורנט אחד לכל קובץ מטאדאטה, או טורנט אחד לכל תיקיית נתונים בינאריים. הטורנטים חייבים לכלול את שם הקובץ/תיקייה המקורי בתוספת סיומת <code>.torrent</code> כשם הקובץ שלהם. <a %(wikipedia_annas_archive)s>ארכיון אנה</a> הפך ללא ספק לספריית הצללים הגדולה ביותר בעולם, והיחידה בקנה מידה כזה שהיא קוד פתוח ונתונים פתוחים לחלוטין. להלן טבלה מעמוד ה-Datasets שלנו (שונה מעט): השגנו זאת בשלוש דרכים: שיקוף ספריות צללים קיימות עם נתונים פתוחים (כמו Sci-Hub ו-Library Genesis). סיוע לספריות צללים שרוצות להיות יותר פתוחות, אך לא היה להן את הזמן או המשאבים לעשות זאת (כמו אוסף הקומיקס של Libgen). גרידת ספריות שאינן מעוניינות לשתף בכמות גדולה (כמו Z-Library). (2) ו-(3) אנו מנהלים כעת אוסף נרחב של טורנטים בעצמנו (מאות TBs). עד כה התייחסנו לאוספים אלו כחד-פעמיים, כלומר תשתית מותאמת אישית וארגון נתונים לכל אוסף. זה מוסיף עומס משמעותי לכל שחרור, ומקשה במיוחד על ביצוע שחרורים מצטברים יותר. זו הסיבה שהחלטנו לתקנן את השחרורים שלנו. זהו פוסט טכני בבלוג שבו אנו מציגים את התקן שלנו: <strong>מיכלי ארכיון אנה</strong>. מכולות ארכיון אנה (AAC): סטנדרטיזציה של שחרורים מהספרייה הגדולה ביותר בעולם ארכיון אנה הפך לספריית הצללים הגדולה ביותר בעולם, מה שמחייב אותנו לסטנדרטיזציה של השחרורים שלנו. שוחררו יותר מ-300GB של כריכות ספרים לבסוף, אנו שמחים להכריז על שחרור קטן. בשיתוף פעולה עם האנשים שמפעילים את הפורק Libgen.rs, אנו משתפים את כל כריכות הספרים שלהם דרך טורנטים ו-IPFS. זה יפיץ את העומס של הצגת הכריכות בין יותר מכונות, וישמר אותן טוב יותר. במקרים רבים (אך לא בכולם), כריכות הספרים כלולות בקבצים עצמם, כך שזה סוג של "נתונים נגזרים". אבל להחזיק את זה ב-IPFS עדיין מאוד שימושי לפעילות היומית של גם ארכיון אנה וגם הפורקים השונים של Library Genesis. כרגיל, תוכלו למצוא את השחרור הזה ב-Pirate Library Mirror (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון אנה</a>). לא נקשר אליו כאן, אבל תוכלו למצוא אותו בקלות. מקווים שנוכל להאט את הקצב קצת, עכשיו שיש לנו אלטרנטיבה ראויה ל-Z-Library. עומס העבודה הזה לא ממש בר קיימא. אם אתם מעוניינים לעזור בתכנות, תפעול שרתים, או עבודה על שימור, בהחלט צרו קשר איתנו. יש עדיין הרבה <a %(annas_archive)s>עבודה לעשות</a>. תודה על העניין והתמיכה שלכם. מעבר ל-ElasticSearch חלק מהשאילתות לקחו זמן רב מאוד, עד כדי כך שהן תפסו את כל החיבורים הפתוחים. בברירת מחדל ל-MySQL יש אורך מילה מינימלי, או שהאינדקס שלך יכול להיות גדול מאוד. אנשים דיווחו שלא יכלו לחפש את "בן חור". החיפוש היה מהיר רק כאשר היה טעון במלואו בזיכרון, מה שדרש מאיתנו להשיג מכונה יקרה יותר להריץ את זה, בנוסף לכמה פקודות לטעינת האינדקס בהפעלה. לא היינו יכולים להרחיב אותו בקלות כדי לבנות תכונות חדשות, כמו <a %(wikipedia_cjk_characters)s>טוקניזציה טובה יותר לשפות ללא רווחים</a>, סינון/פיצול, מיון, הצעות "האם התכוונת", השלמה אוטומטית, וכדומה. אחד מה<a %(annas_archive)s>כרטיסים</a> שלנו היה אוסף של בעיות עם מערכת החיפוש שלנו. השתמשנו בחיפוש טקסט מלא של MySQL, מכיוון שכל הנתונים שלנו היו ב-MySQL בכל מקרה. אבל היו לו מגבלות: לאחר שיחה עם מספר מומחים, החלטנו על ElasticSearch. זה לא היה מושלם (ההצעות "האם התכוונת" והשלמה אוטומטית שלהם לא טובות), אבל בסך הכל זה היה הרבה יותר טוב מ-MySQL לחיפוש. אנחנו עדיין לא <a %(youtube)s>מאוד נלהבים</a> להשתמש בו לכל נתונים קריטיים (למרות שהם עשו הרבה <a %(elastic_co)s>התקדמות</a>), אבל בסך הכל אנחנו די מרוצים מהמעבר. לעת עתה, יישמנו חיפוש מהיר יותר, תמיכה טובה יותר בשפות, מיון רלוונטיות טוב יותר, אפשרויות מיון שונות, וסינון לפי שפה/סוג ספר/סוג קובץ. אם אתם סקרנים איך זה עובד, <a %(annas_archive_l140)s>תסתכלו</a> <a %(annas_archive_l1115)s>על</a> <a %(annas_archive_l1635)s>זה</a>. זה די נגיש, אם כי זה יכול להשתמש בעוד כמה הערות… ארכיון אנה הוא קוד פתוח מלא אנו מאמינים שמידע צריך להיות חופשי, והקוד שלנו אינו יוצא דופן. שחררנו את כל הקוד שלנו במערכת Gitlab הפרטית שלנו: <a %(annas_archive)s>התוכנה של אנה</a>. אנו גם משתמשים במעקב בעיות כדי לארגן את העבודה שלנו. אם אתם רוצים להשתתף בפיתוח שלנו, זהו מקום מצוין להתחיל בו. כדי לתת לכם טעימה מהדברים עליהם אנו עובדים, קחו את העבודה האחרונה שלנו על שיפורי ביצועים בצד הלקוח. מכיוון שעדיין לא יישמנו עימוד, לעיתים קרובות היינו מחזירים דפי חיפוש ארוכים מאוד, עם 100-200 תוצאות. לא רצינו לחתוך את תוצאות החיפוש מוקדם מדי, אך זה אכן האט מכשירים מסוימים. לשם כך, יישמנו טריק קטן: עטפנו את רוב תוצאות החיפוש בהערות HTML (<code><!-- --></code>), ואז כתבנו מעט Javascript שיזהה מתי תוצאה צריכה להפוך לגלויה, ובאותו רגע נפתח את ההערה: מימוש "וירטואליזציה" של DOM ב-23 שורות בלבד, אין צורך בספריות מפוארות! זהו סוג הקוד הפרגמטי המהיר שמתקבל כשיש לך זמן מוגבל ובעיות אמיתיות שצריך לפתור. דווח כי החיפוש שלנו כעת עובד היטב על מכשירים איטיים! מאמץ גדול נוסף היה לאוטומט את בניית מסד הנתונים. כשיצאנו לדרך, פשוט חיברנו מקורות שונים באופן אקראי. כעת אנו רוצים לשמור אותם מעודכנים, ולכן כתבנו מספר סקריפטים להורדת metadata חדשים משני הפורקים של Library Genesis, ולשלב אותם. המטרה היא לא רק להפוך את זה לשימושי עבור הארכיון שלנו, אלא להקל על כל מי שרוצה לשחק עם metadata של ספריות צללים. המטרה תהיה מחברת Jupyter שתכיל כל מיני metadata מעניינים, כך שנוכל לבצע מחקרים נוספים כמו להבין איזה <a %(blog)s>אחוז של ISBNs נשמרים לנצח</a>. לבסוף, שיפרנו את מערכת התרומות שלנו. כעת ניתן להשתמש בכרטיס אשראי להפקדת כסף ישירות לארנקי הקריפטו שלנו, מבלי באמת לדעת דבר על מטבעות קריפטוגרפיים. נמשיך לעקוב אחר האופן שבו זה עובד בפועל, אבל זה עניין גדול. עם סגירת Z-Library ומעצרם (לכאורה) של המייסדים, עבדנו מסביב לשעון כדי לספק חלופה טובה עם ארכיון אנה (לא נצרף קישור כאן, אבל תוכלו לחפש בגוגל). הנה כמה מהדברים שהשגנו לאחרונה. עדכון של אנה: ארכיון קוד פתוח מלא, ElasticSearch, יותר מ-300GB של כריכות ספרים עבדנו מסביב לשעון כדי לספק חלופה טובה עם ארכיון אנה. הנה כמה מהדברים שהשגנו לאחרונה. ניתוח כפילויות סמנטיות (סריקות שונות של אותו ספר) ניתן תיאורטית לסנן, אך זה מסובך. כשבדקנו ידנית את הקומיקס מצאנו יותר מדי תוצאות חיוביות שגויות. ישנן כמה כפילויות רק לפי MD5, שזה יחסית בזבזני, אך סינון שלהן היה נותן לנו רק כ-1% in חיסכון. בקנה מידה זה זה עדיין כ-1TB, אך גם, בקנה מידה זה 1TB לא באמת משנה. אנחנו מעדיפים לא לסכן השמדת נתונים בטעות בתהליך זה. מצאנו חבורה של נתונים שאינם ספרים, כמו סרטים המבוססים על ספרי קומיקס. זה גם נראה בזבזני, שכן אלו כבר זמינים באופן נרחב באמצעים אחרים. עם זאת, הבנו שלא נוכל פשוט לסנן קבצי סרטים, שכן ישנם גם <em>ספרי קומיקס אינטראקטיביים</em> ששוחררו על המחשב, שמישהו הקליט ושמר כסרטים. בסופו של דבר, כל דבר שנוכל למחוק מהאוסף יחסוך רק כמה אחוזים. ואז נזכרנו שאנחנו אוגרי נתונים, וגם האנשים שימשיכו לשקף את זה הם אוגרי נתונים, ולכן, "מה זאת אומרת, למחוק?!" כשאתם מקבלים 95TB שנשפכים לתוך אשכול האחסון שלכם, אתם מנסים להבין מה בכלל יש שם... עשינו קצת ניתוח כדי לראות אם נוכל להקטין את הגודל קצת, כמו על ידי הסרת כפילויות. הנה כמה מהממצאים שלנו: לכן אנו מציגים בפניכם את האוסף המלא והלא-משתנה. זה הרבה נתונים, אבל אנו מקווים שמספיק אנשים יטפחו אותו בכל מקרה. שיתוף פעולה בשל גודלו, האוסף הזה היה זמן רב ברשימת המשאלות שלנו, ולכן לאחר הצלחתנו בגיבוי Z-Library, שמנו את עינינו על האוסף הזה. בתחילה גירדנו אותו ישירות, מה שהיה אתגר לא קטן, שכן השרת שלהם לא היה במצב הטוב ביותר. כך השגנו כ-15TB, אך זה היה תהליך איטי. למזלנו, הצלחנו ליצור קשר עם מפעיל הספרייה, שהסכים לשלוח לנו את כל הנתונים ישירות, מה שהיה הרבה יותר מהיר. עדיין לקח יותר מחצי שנה להעביר ולעבד את כל הנתונים, וכמעט איבדנו את הכל בגלל שחיתות דיסק, מה שהיה אומר להתחיל הכל מחדש. החוויה הזו גרמה לנו להאמין שחשוב להוציא את הנתונים האלה כמה שיותר מהר, כדי שניתן יהיה לשקף אותם רחוק ורחב. אנחנו רק אחד או שניים מאירועים לא מוצלחים בזמן מלהפסיד את האוסף הזה לנצח! האוסף המהירות אכן אומרת שהאוסף קצת לא מאורגן... בואו נסתכל. דמיינו שיש לנו מערכת קבצים (שבמציאות אנחנו מחלקים אותה בין טורנטים): הספרייה הראשונה, <code>/repository</code>, היא החלק המאורגן יותר של זה. ספרייה זו מכילה את מה שנקרא "אלפי ספריות": ספריות שכל אחת מהן מכילה אלף קבצים, שממוספרים באופן אינקרמנטלי במסד הנתונים. ספרייה <code>0</code> מכילה קבצים עם comic_id 0–999, וכן הלאה. זהו אותו סכמה ש-Library Genesis משתמשת בו עבור אוספי הספרות והעיון שלה. הרעיון הוא שכל "אלף ספריות" הופכת אוטומטית לטורנט ברגע שהיא מתמלאת. עם זאת, מפעיל Libgen.li מעולם לא יצר טורנטים עבור האוסף הזה, ולכן אלפי הספריות כנראה הפכו ללא נוחות, ונתנו מקום ל"ספריות לא מסודרות". אלו הן <code>/comics0</code> עד <code>/comics4</code>. כולן מכילות מבני ספריות ייחודיים, שכנראה היו הגיוניים לאיסוף הקבצים, אך לא כל כך הגיוניים לנו כעת. למרבה המזל, ה-metadata עדיין מתייחסת ישירות לכל הקבצים האלה, כך שהארגון שלהם על הדיסק לא באמת משנה! ה-metadata זמינה בצורת מסד נתונים של MySQL. ניתן להוריד אותו ישירות מאתר Libgen.li, אך גם נעשה אותו זמין בטורנט, לצד הטבלה שלנו עם כל ה-MD5 hashes. <q>ד"ר ברברה גורדון מנסה לאבד את עצמה בעולם השגרתי של הספרייה...</q> מזלגות של Libgen קודם כל, קצת רקע. ייתכן שאתם מכירים את Library Genesis בזכות אוסף הספרים האפי שלהם. פחות אנשים יודעים שמתנדבי Library Genesis יצרו פרויקטים נוספים, כמו אוסף גדול של מגזינים ומסמכים סטנדרטיים, גיבוי מלא של Sci-Hub (בשיתוף פעולה עם מייסדת Sci-Hub, אלכסנדרה אלבקיאן), ואכן, אוסף עצום של קומיקס. בשלב מסוים, מפעילים שונים של מראות Library Genesis הלכו בדרכים נפרדות, מה שהוביל למצב הנוכחי שבו יש מספר "מזלגות" שונים, שכולם עדיין נושאים את השם Library Genesis. המזלג של Libgen.li מכיל באופן ייחודי את אוסף הקומיקס הזה, כמו גם אוסף מגזינים גדול (שגם עליו אנו עובדים). גיוס כספים אנו משחררים את הנתונים האלה בכמה חלקים גדולים. הטורנט הראשון הוא של <code>/comics0</code>, שאנו שמנו בקובץ .tar ענק של 12TB. זה טוב יותר לכונן הקשיח ולתוכנת הטורנט שלך מאשר מיליון קבצים קטנים. כחלק מהשחרור הזה, אנו עורכים גיוס כספים. אנו מחפשים לגייס 20,000 דולר לכיסוי עלויות תפעול וחוזים עבור האוסף הזה, וכן לאפשר פרויקטים מתמשכים ועתידיים. יש לנו כמה <em>ענקיים</em> בעבודה. <em>את מי אני תומך בתרומה שלי?</em> בקצרה: אנו מגבים את כל הידע והתרבות של האנושות, והופכים אותם לנגישים בקלות. כל הקוד והנתונים שלנו הם קוד פתוח, אנו פרויקט שמנוהל כולו על ידי מתנדבים, והצלחנו לשמור 125TB של ספרים עד כה (בנוסף לטורנטים הקיימים של Libgen ו-Scihub). בסופו של דבר אנו בונים גלגל תנופה שמאפשר ומעודד אנשים למצוא, לסרוק ולגבות את כל הספרים בעולם. נכתוב על התוכנית הראשית שלנו בפוסט עתידי. :) אם תתרמו לחברות "Amazing Archivist" למשך 12 חודשים (780 דולר), תוכלו <strong>“לאמץ טורנט”</strong>, כלומר שנשים את שם המשתמש או ההודעה שלכם בשם הקובץ של אחד מהטורנטים! ניתן לתרום על ידי כניסה ל<a %(wikipedia_annas_archive)s>ארכיון אנה</a> ולחיצה על כפתור "תרום". אנו גם מחפשים עוד מתנדבים: מהנדסי תוכנה, חוקרי אבטחה, מומחי מסחר אנונימיים, ומתרגמים. ניתן גם לתמוך בנו על ידי מתן שירותי אירוח. וכמובן, אנא טפחו את הטורנטים שלנו! תודה לכל מי שכבר תמך בנו בנדיבות! אתם באמת עושים שינוי. הנה הטורנטים ששוחררו עד כה (אנחנו עדיין מעבדים את השאר): כל הטורנטים ניתן למצוא ב<a %(wikipedia_annas_archive)s>ארכיון אנה</a> תחת "Datasets" (אנחנו לא מקשרים לשם ישירות, כדי שהקישורים לבלוג הזה לא יוסרו מרדיט, טוויטר וכו'). משם, עקבו אחרי הקישור לאתר ה-Tor. <a %(news_ycombinator)s>דונו ב-Hacker News</a> מה הלאה? קבוצה של טורנטים מצוינת לשימור לטווח ארוך, אבל לא כל כך לגישה יומיומית. נעבוד עם שותפי אירוח כדי להעלות את כל הנתונים האלה לרשת (מאחר שארכיון אנה לא מארח שום דבר ישירות). כמובן שתוכלו למצוא את קישורי ההורדה האלה בארכיון אנה. אנו גם מזמינים את כולם לעשות דברים עם הנתונים האלה! עזרו לנו לנתח אותם טוב יותר, להסיר כפילויות, לשים אותם ב-IPFS, לערבב אותם, לאמן את מודלי ה-AI שלכם איתם, וכך הלאה. הכל שלכם, ואנחנו לא יכולים לחכות לראות מה תעשו עם זה. לבסוף, כפי שנאמר קודם, עדיין יש לנו כמה שחרורים ענקיים בדרך (אם <em>מישהו</em> יכול <em>בטעות</em> לשלוח לנו דמפ של <em>מסד נתונים מסוים</em> של ACS4, אתם יודעים איפה למצוא אותנו...), וכן בניית גלגל תנופה לגיבוי כל הספרים בעולם. אז הישארו מעודכנים, אנחנו רק מתחילים. - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) ספריית הצללים הגדולה ביותר של קומיקס היא ככל הנראה של פורק מסוים של Library Genesis: Libgen.li. המנהל היחיד שמפעיל את האתר הזה הצליח לאסוף אוסף קומיקס מטורף של מעל 2 מיליון קבצים, בסך הכל מעל 95TB. עם זאת, בניגוד לאוספים אחרים של Library Genesis, זה לא היה זמין בכמות גדולה דרך טורנטים. יכולתם לגשת לקומיקס הללו רק באופן פרטני דרך השרת האישי האיטי שלו — נקודת כשל יחידה. עד היום! בפוסט זה נספר לכם יותר על האוסף הזה, ועל גיוס הכספים שלנו לתמיכה בעבודה זו. ארכיון אנה גיבה את ספריית הצללים הגדולה בעולם של קומיקס (95TB) — אתם יכולים לעזור להפיץ אותה ספריית הצללים הגדולה ביותר של קומיקס בעולם הייתה נקודת כשל יחידה.. עד היום. אזהרה: פוסט הבלוג הזה הוצא משימוש. החלטנו ש-IPFS עדיין לא מוכן לשימוש רחב. עדיין נקשר לקבצים ב-IPFS מהארכיון של אנה כשאפשר, אבל לא נאחסן אותו בעצמנו יותר, וגם לא נמליץ לאחרים לשקף באמצעות IPFS. אנא ראו את דף הטורנטים שלנו אם אתם רוצים לעזור לשמר את האוסף שלנו. העלאת 5,998,794 ספרים ל-IPFS ריבוי עותקים חזרה לשאלה המקורית שלנו: איך אנחנו יכולים לטעון שאנחנו משמרים את האוספים שלנו לנצח? הבעיה העיקרית כאן היא שהאוסף שלנו <a %(torrents_stats)s>גדל</a> בקצב מהיר, על ידי גרידה ופתיחת קוד של כמה אוספים עצומים (בנוסף לעבודה המדהימה שכבר נעשתה על ידי ספריות צללים של נתונים פתוחים כמו Sci-Hub ו-Library Genesis). הגידול הזה בנתונים מקשה על שיקוף האוספים ברחבי העולם. אחסון נתונים יקר! אבל אנחנו אופטימיים, במיוחד כשמתבוננים בשלושת המגמות הבאות. ה<a %(annas_archive_stats)s>גודל הכולל</a> של האוספים שלנו, במהלך החודשים האחרונים, מפורק לפי מספר משתפי הטורנטים. מגמות מחירי HDD ממקורות שונים (לחץ לצפייה במחקר). <a %(critical_window_chinese)s>גרסה סינית 中文版</a>, לדון ב-<a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. קטפנו את הפירות הנמוכים זה נובע ישירות מהעדיפויות שלנו שנדונו לעיל. אנחנו מעדיפים לעבוד על שחרור אוספים גדולים תחילה. עכשיו כשאבטחנו כמה מהאוספים הגדולים בעולם, אנחנו מצפים שהגידול שלנו יהיה הרבה יותר איטי. עדיין יש זנב ארוך של אוספים קטנים יותר, וספרים חדשים נסרקים או מתפרסמים כל יום, אבל הקצב יהיה כנראה הרבה יותר איטי. אנחנו עשויים עדיין להכפיל או אפילו לשלש את הגודל, אבל על פני תקופה ארוכה יותר. שיפורי OCR. עדיפויות קוד תוכנה למדעים והנדסה גרסאות בדיוניות או בידוריות של כל האמור לעיל נתונים גאוגרפיים (למשל מפות, סקרים גאולוגיים) נתונים פנימיים מתאגידים או ממשלות (הדלפות) נתוני מדידה כמו מדידות מדעיות, נתונים כלכליים, דוחות תאגידיים רשומות מטאדאטה באופן כללי (של לא-בדיוני ובדיוני; של מדיה אחרת, אמנות, אנשים וכו'; כולל ביקורות) ספרי עיון מגזינים לא-בדיוניים, עיתונים, מדריכים תמלולים לא-בדיוניים של שיחות, סרטים דוקומנטריים, פודקאסטים נתונים אורגניים כמו רצפי DNA, זרעי צמחים, או דגימות מיקרוביאליות מאמרים אקדמיים, כתבי עת, דוחות אתרי אינטרנט למדעים והנדסה, דיונים מקוונים תמלולים של הליכים משפטיים או בבית משפט בסיכון ייחודי להשמדה (למשל על ידי מלחמה, קיצוצי תקציב, תביעות משפטיות או רדיפה פוליטית) נדירות בעלות מיקוד ייחודי מדוע אכפת לנו כל כך ממאמרים וספרים? בואו נשים בצד את האמונה הבסיסית שלנו בשימור באופן כללי — אולי נכתוב פוסט נוסף על כך. אז מדוע מאמרים וספרים במיוחד? התשובה פשוטה: <strong>צפיפות המידע</strong>. לכל מגהבייט של אחסון, טקסט כתוב מאחסן את כמות המידע הגדולה ביותר מכל המדיה. בעוד שאכפת לנו גם מהידע וגם מהתרבות, אכפת לנו יותר מהראשון. בסך הכל, אנו מוצאים היררכיה של צפיפות מידע וחשיבות השימור שנראית בערך כך: הדירוג ברשימה זו הוא במידה מסוימת שרירותי — כמה פריטים הם תיקו או שיש מחלוקות בתוך הצוות שלנו — ואנחנו כנראה שוכחים כמה קטגוריות חשובות. אבל זה בערך איך אנחנו נותנים עדיפות. חלק מהפריטים הללו שונים מדי מהאחרים מכדי שנדאג להם (או שכבר מטופלים על ידי מוסדות אחרים), כמו נתונים אורגניים או נתונים גאוגרפיים. אבל רוב הפריטים ברשימה זו הם למעשה חשובים לנו. גורם גדול נוסף בהעדפות שלנו הוא כמה בסיכון עבודה מסוימת נמצאת. אנחנו מעדיפים להתמקד בעבודות שהן: לבסוף, אנחנו דואגים להיקף. יש לנו זמן וכסף מוגבלים, ולכן נעדיף להקדיש חודש להצלת 10,000 ספרים מאשר 1,000 ספרים — אם הם בערך בעלי ערך וסיכון שווים. <em><q>מה שאבד לא ניתן לשחזור; אבל הבה נשמור על מה שנותר: לא באמצעות כספות ומנעולים שמגנים עליהם מעיני הציבור ושימושו, בהעברת אותם לבזבוז הזמן, אלא באמצעות ריבוי עותקים, שימקם אותם מעבר להישג יד של תאונה.</q></em><br>— תומאס ג'פרסון, 1791 ספריות צללים קוד יכול להיות קוד פתוח ב-Github, אבל Github כשלם לא יכול להיות משוכפל בקלות ולכן לא נשמר (למרות שבמקרה זה יש עותקים מופצים מספיק של רוב מאגרי הקוד) רישומי מטאדאטה ניתנים לצפייה חופשית באתר Worldcat, אך לא להורדה בכמויות גדולות (עד ש<a %(worldcat_scrape)s>גרדנו</a> אותם) Reddit חופשי לשימוש, אך לאחרונה הציב אמצעי מניעה מחמירים נגד גרידה, בעקבות אימון LLM רעב לנתונים (עוד על כך בהמשך) ישנם ארגונים רבים שיש להם משימות דומות, וקדימויות דומות. אכן, ישנן ספריות, ארכיונים, מעבדות, מוזיאונים ומוסדות אחרים המופקדים על שימור מסוג זה. רבים מהם ממומנים היטב, על ידי ממשלות, יחידים או תאגידים. אבל יש להם נקודת עיוור ענקית אחת: המערכת המשפטית. כאן טמון התפקיד הייחודי של ספריות הצללים, והסיבה לקיומה של ארכיון אנה. אנחנו יכולים לעשות דברים שמוסדות אחרים אינם מורשים לעשות. עכשיו, זה לא (לעיתים קרובות) שאנחנו יכולים לארכב חומרים שאסור לשמר במקומות אחרים. לא, זה חוקי במקומות רבים לבנות ארכיון עם כל ספר, מאמר, מגזין וכדומה. אבל מה שלעתים קרובות חסר בארכיונים משפטיים הוא <strong>יתירות ואריכות ימים</strong>. קיימים ספרים שרק עותק אחד מהם קיים באיזושהי ספרייה פיזית במקום כלשהו. קיימים רישומי מטאדאטה שמוגנים על ידי תאגיד יחיד. קיימים עיתונים שנשמרים רק על מיקרופילם בארכיון יחיד. ספריות יכולות לסבול מקיצוצי תקציב, תאגידים יכולים לפשוט רגל, ארכיונים יכולים להיות מופצצים ונשרפים עד היסוד. זה לא היפותטי - זה קורה כל הזמן. הדבר שאנחנו יכולים לעשות באופן ייחודי בארכיון של אנה הוא לאחסן עותקים רבים של יצירות, בקנה מידה רחב. אנחנו יכולים לאסוף מאמרים, ספרים, מגזינים ועוד, ולהפיץ אותם בכמויות גדולות. נכון לעכשיו אנחנו עושים זאת באמצעות טורנטים, אבל הטכנולוגיות המדויקות לא משנות וישתנו עם הזמן. החלק החשוב הוא הפצת עותקים רבים ברחבי העולם. הציטוט הזה מלפני יותר מ-200 שנה עדיין נכון: הערה מהירה על נחלת הכלל. מכיוון שארכיון של אנה מתמקד באופן ייחודי בפעילויות שהן בלתי חוקיות במקומות רבים ברחבי העולם, אנחנו לא טורחים עם אוספים זמינים באופן נרחב, כמו ספרים בנחלת הכלל. ישויות משפטיות לעיתים קרובות כבר מטפלות בזה היטב. עם זאת, ישנם שיקולים שגורמים לנו לפעמים לעבוד על אוספים זמינים לציבור: - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. עלויות האחסון ממשיכות לרדת באופן אקספוננציאלי שיפורים בצפיפות המידע כיום אנו מאחסנים ספרים בפורמטים הגולמיים שבהם הם ניתנים לנו. אמנם הם דחוסים, אך לעיתים קרובות הם עדיין סריקות גדולות או צילומים של עמודים. עד כה, האפשרויות היחידות להקטין את הגודל הכולל של האוסף שלנו היו באמצעות דחיסה אגרסיבית יותר או הסרת כפילויות. עם זאת, כדי להשיג חיסכון משמעותי מספיק, שתיהן מאבדות יותר מדי איכות לטעמנו. דחיסה כבדה של תמונות יכולה להפוך את הטקסט לכמעט בלתי קריא. והסרת כפילויות דורשת ביטחון גבוה שהספרים זהים בדיוק, מה שלעיתים קרובות אינו מדויק מספיק, במיוחד אם התכנים זהים אך הסריקות נעשו בהזדמנויות שונות. תמיד הייתה אפשרות שלישית, אך איכותה הייתה כה גרועה שמעולם לא שקלנו אותה: <strong>OCR, או זיהוי תווים אופטי</strong>. זהו התהליך של המרת תמונות לטקסט פשוט, באמצעות AI לזיהוי התווים בתמונות. כלים לכך קיימים כבר זמן רב, והיו די טובים, אך "די טובים" אינו מספיק למטרות שימור. עם זאת, מודלים של למידה עמוקה רב-מודאלית עשו התקדמות מהירה ביותר לאחרונה, אם כי עדיין בעלויות גבוהות. אנו מצפים שהן הדיוק והן העלויות ישתפרו באופן דרמטי בשנים הקרובות, עד לנקודה שבה יהיה ריאלי ליישם זאת על כל הספרייה שלנו. כאשר זה יקרה, סביר להניח שנשמור עדיין את הקבצים המקוריים, אך בנוסף נוכל להחזיק גרסה קטנה בהרבה של הספרייה שלנו שרוב האנשים ירצו לשקף. הקטע הוא שהטקסט הגולמי עצמו נדחס אפילו טוב יותר, וקל יותר להסיר כפילויות, מה שנותן לנו חיסכון נוסף. בסך הכל, זה לא בלתי ריאלי לצפות לפחות להפחתה של פי 5-10 בגודל הקובץ הכולל, אולי אפילו יותר. אפילו עם הפחתה שמרנית של פי 5, נסתכל על <strong>1,000–3,000 דולר בעוד 10 שנים, גם אם הספרייה שלנו תשלש את גודלה</strong>. נכון למועד הכתיבה, <a %(diskprices)s>מחירי הדיסקים</a> לכל TB הם בסביבות $12 לדיסקים חדשים, $8 לדיסקים משומשים, ו-$4 לקלטת. אם נהיה שמרנים ונביט רק על דיסקים חדשים, זה אומר שאחסון פטה-בייט עולה כ-$12,000. אם נניח שהספרייה שלנו תשלש מ-900TB ל-2.7PB, זה יעלה $32,400 לשקף את כל הספרייה שלנו. בהוספת חשמל, עלות חומרה אחרת, וכדומה, נעלה את זה ל-$40,000. או עם קלטת יותר כמו $15,000–$20,000. מצד אחד <strong>$15,000–$40,000 עבור סך כל הידע האנושי זה מציאה</strong>. מצד שני, זה קצת תלול לצפות להמון עותקים מלאים, במיוחד אם נרצה גם שאותם אנשים ימשיכו לזרוע את הטורנטים שלהם לטובת אחרים. זה היום. אבל ההתקדמות ממשיכה קדימה: עלויות כוננים קשיחים לכל TB נחתכו בערך בשליש בעשור האחרון, וסביר להניח שימשיכו לרדת בקצב דומה. נראה כי קלטת נמצאת במסלול דומה. מחירי SSD יורדים אפילו מהר יותר, ועשויים לעקוף את מחירי HDD עד סוף העשור. אם זה יימשך, אז בעוד 10 שנים אנחנו עשויים להסתכל על רק $5,000–$13,000 לשקף את כל האוסף שלנו (1/3), או אפילו פחות אם נגדל פחות בגודל. למרות שזה עדיין הרבה כסף, זה יהיה בר השגה עבור אנשים רבים. וזה עשוי להיות אפילו טוב יותר בגלל הנקודה הבאה… בארכיון של אנה, לעיתים קרובות שואלים אותנו כיצד אנו יכולים לטעון שאנו משמרים את האוספים שלנו לנצח, כאשר הגודל הכולל כבר מתקרב ל-1 פטהבייט (1000 TB), ועדיין גדל. במאמר זה נבחן את הפילוסופיה שלנו, ונראה מדוע העשור הבא הוא קריטי למשימתנו לשימור הידע והתרבות של האנושות. חלון קריטי אם תחזיות אלו מדויקות, אנו <strong>רק צריכים להמתין כמה שנים</strong> לפני שכל האוסף שלנו ישוקף באופן נרחב. כך, במילותיו של תומאס ג'פרסון, "יוצב מעבר להישג יד של תאונה". למרבה הצער, הופעת ה-LLM, והאימון הרעב לנתונים שלהם, גרמה להרבה בעלי זכויות יוצרים להיות במגננה. אפילו יותר ממה שהם כבר היו. אתרים רבים מקשים על גירוד וארכוב, תביעות משפטיות מתעופפות, ובינתיים ספריות וארכיונים פיזיים ממשיכים להיות מוזנחים. אנו יכולים רק לצפות שהמגמות הללו ימשיכו להחמיר, ורבים מהעבודות יאבדו הרבה לפני שייכנסו לנחלת הכלל. <strong>אנו על סף מהפכה בשימור, אך <q>האבוד לא ניתן לשחזור.</q></strong> יש לנו חלון קריטי של כ-5-10 שנים שבמהלכו עדיין יקר למדי להפעיל ספריית צללים וליצור מראות רבות ברחבי העולם, ובמהלכו הגישה לא נסגרה לחלוטין עדיין. אם נוכל לגשר על חלון זה, אז אכן נשמר את הידע והתרבות של האנושות לנצח. אסור לנו לתת לזמן הזה להתבזבז. אסור לנו לתת לחלון הקריטי הזה להיסגר עלינו. בואו נצא לדרך. החלון הקריטי של ספריות הצללים כיצד נוכל לטעון שאנו משמרים את האוספים שלנו לנצח, כאשר הם כבר מתקרבים ל-1 PB? אוסף עוד מידע על האוסף. <a %(duxiu)s>Duxiu</a> הוא מאגר עצום של ספרים סרוקים, שנוצר על ידי <a %(chaoxing)s>קבוצת הספרייה הדיגיטלית SuperStar</a>. רובם ספרים אקדמיים, שנסרקו כדי להפוך אותם לזמינים דיגיטלית לאוניברסיטאות וספריות. עבור הקהל דובר האנגלית שלנו, <a %(library_princeton)s>פרינסטון</a> ו<a %(guides_lib_uw)s>אוניברסיטת וושינגטון</a> מציעים סקירות טובות. יש גם מאמר מצוין שנותן רקע נוסף: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (חפשו אותו בארכיון של אנה). הספרים מ-Duxiu כבר זמן רב מופצים באופן פיראטי באינטרנט הסיני. בדרך כלל הם נמכרים בפחות מדולר על ידי משווקים. הם מופצים בדרך כלל באמצעות המקבילה הסינית של Google Drive, שלעיתים קרובות נפרצה כדי לאפשר יותר שטח אחסון. ניתן למצוא כמה פרטים טכניים <a %(github_duty_machine)s>כאן</a> ו<a %(github_821_github_io)s>כאן</a>. למרות שהספרים הופצו באופן חצי-ציבורי, קשה מאוד להשיג אותם בכמות גדולה. זה היה גבוה ברשימת המטלות שלנו, והקצנו מספר חודשים של עבודה במשרה מלאה לכך. עם זאת, לאחרונה מתנדב מדהים, מדהים ומוכשר פנה אלינו, ואמר לנו שהוא כבר עשה את כל העבודה הזו — בהוצאה גדולה. הם שיתפו את כל האוסף איתנו, מבלי לצפות לשום דבר בתמורה, מלבד הבטחת שימור לטווח ארוך. באמת יוצא דופן. הם הסכימו לבקש עזרה בדרך זו כדי לקבל את האוסף OCR'ed. האוסף כולל 7,543,702 קבצים. זה יותר מ-Library Genesis ספרי עיון (כ-5.3 מיליון). גודל הקובץ הכולל הוא כ-359TB (326TiB) בצורתו הנוכחית. אנחנו פתוחים להצעות ורעיונות אחרים. פשוט צרו קשר איתנו. בדקו את הארכיון של אנה למידע נוסף על האוספים שלנו, מאמצי השימור, וכיצד תוכלו לעזור. תודה! דפי דוגמה כדי להוכיח לנו שיש לך צינור טוב, הנה כמה דפי דוגמה להתחיל מהם, מתוך ספר על מוליכי על. הצינור שלך צריך לטפל כראוי במתמטיקה, טבלאות, תרשימים, הערות שוליים וכדומה. שלח את הדפים המעובדים שלך למייל שלנו. אם הם ייראו טוב, נשלח לך עוד בפרטי, ואנו מצפים שתוכל להפעיל את הצינור שלך עליהם במהירות. ברגע שנהיה מרוצים, נוכל לעשות עסקה. - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>גרסה סינית 中文版</a>, <a %(news_ycombinator)s>דונו ב-Hacker News</a> זהו פוסט בלוג קצר. אנו מחפשים חברה או מוסד שיעזרו לנו עם OCR והפקת טקסט עבור אוסף עצום שרכשנו, בתמורה לגישה מוקדמת בלעדית. לאחר תקופת האמברגו, נשחרר כמובן את כל האוסף. טקסט אקדמי באיכות גבוהה הוא שימושי ביותר לאימון של LLMs. למרות שהאוסף שלנו הוא בסינית, זה יכול להיות אפילו שימושי לאימון LLMs באנגלית: נראה שהמודלים מקודדים מושגים וידע ללא קשר לשפת המקור. לשם כך, יש לחלץ טקסט מהסריקות. מה יוצא לארכיון של אנה מזה? חיפוש טקסט מלא של הספרים עבור המשתמשים שלו. מכיוון שהמטרות שלנו תואמות לאלו של מפתחי LLM, אנחנו מחפשים שותף לשיתוף פעולה. אנחנו מוכנים לתת לך <strong>גישה מוקדמת בלעדית לאוסף זה בכמות גדולה למשך שנה</strong>, אם תוכל לבצע OCR וחילוץ טקסט כראוי. אם תהיה מוכן לשתף את כל הקוד של הצינור שלך איתנו, נהיה מוכנים להטיל אמברגו על האוסף למשך זמן ארוך יותר. גישה בלעדית לחברות LLM לאוסף הספרים הלא-בדיוניים הסיני הגדול בעולם <em><strong>בקצרה:</strong> ארכיון אנה רכש אוסף ייחודי של 7.5 מיליון / 350TB ספרים לא-בדיוניים סיניים — גדול יותר מ-Library Genesis. אנו מוכנים לתת לחברת LLM גישה בלעדית, בתמורה ל-OCR והפקת טקסט באיכות גבוהה.</em> ארכיטקטורת מערכת אז נניח שמצאתם כמה חברות שמוכנות לארח את האתר שלכם מבלי לסגור אתכם — נקרא להן "ספקים אוהבי חופש" 😄. תגלו במהירות שאירוח הכל איתם הוא די יקר, אז אולי תרצו למצוא כמה "ספקים זולים" ולעשות את האירוח בפועל שם, תוך תיווך דרך הספקים אוהבי החופש. אם תעשו זאת נכון, הספקים הזולים לעולם לא ידעו מה אתם מארחים, ולעולם לא יקבלו תלונות. עם כל הספקים הללו יש סיכון שהם יסגרו אתכם בכל מקרה, ולכן אתם גם צריכים יתירות. אנחנו צריכים את זה בכל הרמות של הערימה שלנו. חברה אחת שאוהבת חופש במידה מסוימת ושמה את עצמה במצב מעניין היא Cloudflare. הם <a %(blog_cloudflare)s>טענו</a> שהם לא ספק אירוח, אלא שירות, כמו ספק אינטרנט. לכן הם אינם כפופים ל-DMCA או לבקשות הסרה אחרות, ומעבירים כל בקשה לספק האירוח האמיתי שלכם. הם הלכו עד כדי כך שהלכו לבית המשפט כדי להגן על מבנה זה. לכן אנו יכולים להשתמש בהם כשכבת מטמון והגנה נוספת. Cloudflare אינה מקבלת תשלומים אנונימיים, ולכן אנו יכולים להשתמש רק בתוכנית החינמית שלהם. זה אומר שאנחנו לא יכולים להשתמש בתכונות האיזון עומסים או הכשל שלהם. לכן <a %(annas_archive_l255)s>יישמנו זאת בעצמנו</a> ברמת הדומיין. בעת טעינת הדף, הדפדפן יבדוק אם הדומיין הנוכחי עדיין זמין, ואם לא, הוא ישכתב את כל כתובות ה-URL לדומיין אחר. מכיוון ש-Cloudflare מאחסנת במטמון דפים רבים, זה אומר שמשתמש יכול לנחות על הדומיין הראשי שלנו, גם אם שרת הפרוקסי מושבת, ואז בלחיצה הבאה לעבור לדומיין אחר. עדיין יש לנו גם דאגות תפעוליות רגילות להתמודד איתן, כגון ניטור בריאות השרת, רישום שגיאות אחוריות וקדמיות, וכדומה. ארכיטקטורת הכשל שלנו מאפשרת יותר חוסן גם בחזית זו, למשל על ידי הפעלת סט שרתים שונה לחלוטין על אחד מהדומיינים. אנחנו יכולים אפילו להפעיל גרסאות ישנות יותר של הקוד וה-Datasets על דומיין נפרד זה, למקרה שבאג קריטי בגרסה הראשית לא יזוהה. אנחנו יכולים גם להגן על עצמנו מפני Cloudflare שתפנה נגדנו, על ידי הסרתה מאחד מהדומיינים, כמו הדומיין הנפרד הזה. אפשריות פרמוטציות שונות של רעיונות אלו. סיכום זו הייתה חוויה מעניינת ללמוד כיצד להקים מנוע חיפוש לספרייה מוצלת חזק ועמיד. יש עוד המון פרטים לשתף בפוסטים הבאים, אז תודיעו לי על מה הייתם רוצים ללמוד יותר! כמו תמיד, אנחנו מחפשים תרומות לתמיכה בעבודה זו, אז הקפידו לבדוק את דף התרומות בארכיון של אנה. אנחנו גם מחפשים סוגים אחרים של תמיכה, כמו מענקים, ספונסרים לטווח ארוך, ספקי תשלומים בסיכון גבוה, ואולי אפילו פרסומות (בטעם טוב!). ואם אתם רוצים לתרום את הזמן והכישורים שלכם, אנחנו תמיד מחפשים מפתחים, מתרגמים וכדומה. תודה על העניין והתמיכה שלכם. אסימוני חדשנות בואו נתחיל עם ערימת הטכנולוגיה שלנו. היא בכוונה משעממת. אנחנו משתמשים ב-Flask, MariaDB ו-ElasticSearch. זהו זה. חיפוש הוא בעיה שנפתרה במידה רבה, ואין בכוונתנו להמציא אותה מחדש. בנוסף, עלינו להוציא את <a %(mcfunley)s>אסימוני החדשנות</a> שלנו על משהו אחר: לא להיסגר על ידי הרשויות. אז עד כמה הארכיון של אנה חוקי או לא חוקי בדיוק? זה תלוי בעיקר בתחום השיפוט המשפטי. רוב המדינות מאמינות בצורה כלשהי של זכויות יוצרים, מה שאומר שאנשים או חברות מקבלים מונופול בלעדי על סוגים מסוימים של יצירות לתקופה מסוימת. כהערת צד, בארכיון של אנה אנו מאמינים שבעוד שיש כמה יתרונות, בסך הכל זכויות יוצרים הן שליליות נטו לחברה — אבל זהו סיפור לפעם אחרת. מונופול בלעדי זה על יצירות מסוימות אומר שזה לא חוקי עבור כל מי שמחוץ למונופול זה להפיץ ישירות את היצירות הללו — כולל אותנו. אבל הארכיון של אנה הוא מנוע חיפוש שאינו מפיץ ישירות את היצירות הללו (לפחות לא באתר האינטרנט שלנו ברשת הפתוחה), אז אנחנו אמורים להיות בסדר, נכון? לא בדיוק. בתחומי שיפוט רבים זה לא רק לא חוקי להפיץ יצירות מוגנות בזכויות יוצרים, אלא גם לקשר למקומות שעושים זאת. דוגמה קלאסית לכך היא חוק ה-DMCA של ארצות הברית. זהו הקצה המחמיר ביותר של הספקטרום. בקצה השני של הספקטרום תיאורטית יכולות להיות מדינות ללא חוקי זכויות יוצרים כלל, אבל אלה לא באמת קיימות. כמעט בכל מדינה יש צורה כלשהי של חוק זכויות יוצרים בספרים. האכיפה היא סיפור אחר. ישנן מדינות רבות עם ממשלות שלא אכפת להן לאכוף את חוק זכויות היוצרים. יש גם מדינות בין שני הקצוות, שאוסרות על הפצת יצירות מוגנות בזכויות יוצרים, אך אינן אוסרות על קישור ליצירות כאלה. שיקול נוסף הוא ברמת החברה. אם חברה פועלת בתחום שיפוט שלא אכפת לו מזכויות יוצרים, אך החברה עצמה אינה מוכנה לקחת סיכון כלשהו, אז הם עשויים לסגור את האתר שלך ברגע שמישהו מתלונן עליו. לבסוף, שיקול גדול הוא תשלומים. מכיוון שעלינו להישאר אנונימיים, איננו יכולים להשתמש בשיטות תשלום מסורתיות. זה משאיר אותנו עם מטבעות קריפטוגרפיים, ורק תת-קבוצה קטנה של חברות תומכת בהם (ישנם כרטיסי חיוב וירטואליים המשולמים בקריפטו, אך לעיתים קרובות הם אינם מתקבלים). - אנה והצוות (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) אני מנהל את <a %(wikipedia_annas_archive)s>הארכיון של אנה</a>, מנוע החיפוש הפתוח הגדול בעולם ללא מטרות רווח עבור <a %(wikipedia_shadow_library)s>ספריות צללים</a>, כמו Sci-Hub, Library Genesis ו-Z-Library. המטרה שלנו היא להפוך את הידע והתרבות לנגישים בקלות, ובסופו של דבר לבנות קהילה של אנשים שיחדיו יארכבו וישמרו <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>את כל הספרים בעולם</a>. במאמר זה אראה כיצד אנו מפעילים את האתר הזה, והאתגרים הייחודיים שמגיעים עם הפעלת אתר במעמד משפטי מפוקפק, מכיוון שאין "AWS לעמותות צללים". <em>בדקו גם את המאמר האח <a %(blog_how_to_become_a_pirate_archivist)s>כיצד להפוך לארכיבאי פיראטי</a>.</em> כיצד להפעיל ספריית צללים: פעולות בארכיון של אנה אין <q>AWS לעמותות צללים,</q> אז איך אנחנו מפעילים את הארכיון של אנה? כלים שרת יישומים: Flask, MariaDB, ElasticSearch, Docker. פיתוח: Gitlab, Weblate, Zulip. ניהול שרתים: Ansible, Checkmk, UFW. אירוח סטטי של בצל: Tor, Nginx. שרת פרוקסי: Varnish. בואו נסתכל על הכלים שבהם אנו משתמשים כדי להשיג את כל זה. זה מאוד מתפתח ככל שאנו נתקלים בבעיות חדשות ומוצאים פתרונות חדשים. ישנן כמה החלטות שהתלבטנו לגביהן. אחת מהן היא התקשורת בין השרתים: בעבר השתמשנו ב-Wireguard לשם כך, אך גילינו שלפעמים הוא מפסיק לשדר נתונים, או משדר נתונים רק בכיוון אחד. זה קרה עם כמה הגדרות שונות של Wireguard שניסינו, כמו <a %(github_costela_wesher)s>wesher</a> ו-<a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. ניסינו גם להעביר פורטים דרך SSH, באמצעות autossh ו-sshuttle, אך נתקלנו ב-<a %(github_sshuttle)s>בעיות שם</a> (למרות שעדיין לא ברור לי אם autossh סובל מבעיות TCP-over-TCP או לא — זה פשוט מרגיש לי כמו פתרון לא יציב, אבל אולי זה בסדר?). במקום זאת, חזרנו לחיבורים ישירים בין השרתים, תוך הסתרת העובדה ששרת פועל על ספקים זולים באמצעות סינון IP עם UFW. יש לזה חיסרון ש-Docker לא עובד טוב עם UFW, אלא אם כן משתמשים ב-<code>network_mode: "host"</code>. כל זה קצת יותר נוטה לטעויות, כי תחשוף את השרת שלך לאינטרנט עם רק טעות קטנה בהגדרות. אולי כדאי לנו לחזור ל-autossh — נשמח לקבל משוב כאן. גם התלבטנו בין Varnish ל-Nginx. כרגע אנחנו מעדיפים את Varnish, אבל יש לו את המוזרויות והחספוסים שלו. אותו דבר לגבי Checkmk: אנחנו לא אוהבים אותו, אבל הוא עובד כרגע. Weblate היה בסדר אבל לא מדהים — לפעמים אני חושש שהוא יאבד את הנתונים שלי בכל פעם שאני מנסה לסנכרן אותו עם מאגר ה-git שלנו. Flask היה טוב בסך הכל, אבל יש לו כמה מוזרויות מוזרות שגזלו הרבה זמן לפתרון, כמו הגדרת דומיינים מותאמים אישית, או בעיות עם האינטגרציה של SqlAlchemy. עד כה הכלים האחרים היו מצוינים: אין לנו תלונות רציניות על MariaDB, ElasticSearch, Gitlab, Zulip, Docker ו-Tor. לכולם היו כמה בעיות, אבל שום דבר רציני או גוזל זמן במיוחד. קהילה האתגר הראשון עשוי להיות מפתיע. זה לא בעיה טכנית, או בעיה משפטית. זו בעיה פסיכולוגית: עשיית עבודה זו בצללים יכולה להיות בודדה להפליא. תלוי במה שאתם מתכננים לעשות, ובמודל האיום שלכם, ייתכן שתצטרכו להיות זהירים מאוד. בקצה אחד של הספקטרום יש אנשים כמו אלכסנדרה אלבקיאן*, מייסדת Sci-Hub, שהיא מאוד פתוחה לגבי פעילויותיה. אבל היא בסיכון גבוה להיעצר אם תבקר במדינה מערבית בשלב זה, ועלולה לעמוד בפני עשרות שנים של מאסר. האם זה סיכון שהייתם מוכנים לקחת? אנחנו בקצה השני של הספקטרום; נזהרים מאוד לא להשאיר עקבות, ויש לנו אבטחה תפעולית חזקה. * כפי שצוין ב-HN על ידי "ynno", אלכסנדרה בתחילה לא רצתה להיות ידועה: "השרתים שלה הוגדרו לפלוט הודעות שגיאה מפורטות מ-PHP, כולל הנתיב המלא של קובץ המקור הפגום, שהיה תחת ספריית /home/<USER>" אז, השתמשו בשמות משתמש אקראיים במחשבים שאתם משתמשים בהם לדברים האלה, למקרה שתגדירו משהו לא נכון. הסודיות הזו, עם זאת, מגיעה עם עלות פסיכולוגית. רוב האנשים אוהבים להיות מוכרים על העבודה שהם עושים, ובכל זאת אינכם יכולים לקחת שום קרדיט על כך בחיים האמיתיים. אפילו דברים פשוטים יכולים להיות מאתגרים, כמו חברים ששואלים אתכם מה עשיתם לאחרונה (בשלב מסוים "משחק עם ה-NAS / homelab שלי" מתיישן). זו הסיבה שחשוב כל כך למצוא איזושהי קהילה. אתם יכולים לוותר על קצת אבטחה תפעולית על ידי שיתוף עם כמה חברים קרובים מאוד, שאתם יודעים שאתם יכולים לסמוך עליהם עמוקות. גם אז היו זהירים לא לשים שום דבר בכתב, למקרה שהם יצטרכו למסור את המיילים שלהם לרשויות, או אם המכשירים שלהם נפרצים בדרך כלשהי. עדיף למצוא כמה פיראטים עמיתים. אם החברים הקרובים שלכם מעוניינים להצטרף אליכם, נהדר! אחרת, ייתכן שתוכלו למצוא אחרים באינטרנט. למרבה הצער זו עדיין קהילה נישתית. עד כה מצאנו רק קומץ אחרים שפעילים בתחום זה. מקומות התחלה טובים נראים כפורומים של Library Genesis, ו-r/DataHoarder. צוות הארכיון כולל גם אנשים בעלי דעות דומות, אם כי הם פועלים במסגרת החוק (גם אם באזורים אפורים של החוק). הסצנות המסורתיות של "warez" ופיראטיות כוללות גם אנשים שחושבים בדרכים דומות. אנחנו פתוחים לרעיונות על איך לטפח קהילה ולחקור רעיונות. אתם מוזמנים לשלוח לנו הודעה בטוויטר או ברדיט. אולי נוכל לארח סוג של פורום או קבוצת צ'אט. אתגר אחד הוא שזה יכול להיצנזר בקלות כשמשתמשים בפלטפורמות נפוצות, ולכן נצטרך לארח את זה בעצמנו. יש גם איזון בין קיום הדיונים האלה בפומבי (יותר מעורבות פוטנציאלית) לבין הפיכתם לפרטיים (לא לתת ל"יעדים" פוטנציאליים לדעת שאנחנו עומדים לגרד אותם). נצטרך לחשוב על זה. תודיעו לנו אם אתם מעוניינים בזה! סיכום אנו מקווים שזה יעזור לארכיבאים פיראטיים המתחילים את דרכם. אנו נרגשים לקבל את פניכם לעולם זה, אז אל תהססו לפנות אלינו. בואו נשמר כמה שיותר מהידע והתרבות של העולם, ונשכפל אותו רחוק ורחב. פרויקטים 4. בחירת נתונים לעיתים קרובות ניתן להשתמש ב-metadata כדי להבין תת-קבוצה סבירה של נתונים להורדה. גם אם בסופו של דבר תרצו להוריד את כל הנתונים, זה יכול להיות מועיל לתת עדיפות לפריטים החשובים ביותר תחילה, למקרה שתזוהו וההגנות ישופרו, או כי תצטרכו לקנות עוד דיסקים, או פשוט כי משהו אחר יקרה בחייכם לפני שתוכלו להוריד הכל. לדוגמה, אוסף עשוי לכלול מספר מהדורות של אותו משאב בסיסי (כמו ספר או סרט), כאשר אחת מהן מסומנת כאיכות הטובה ביותר. שמירת מהדורות אלו תחילה תהיה הגיונית מאוד. ייתכן שתרצו בסופו של דבר לשמור את כל המהדורות, שכן במקרים מסוימים ה-metadata עשוי להיות מתויג באופן שגוי, או עשויים להיות פשרות לא ידועות בין המהדורות (לדוגמה, "המהדורה הטובה ביותר" עשויה להיות הטובה ביותר ברוב הדרכים אך גרועה בדרכים אחרות, כמו סרט בעל רזולוציה גבוהה יותר אך ללא כתוביות). ניתן גם לחפש במסד הנתונים של ה-metadata כדי למצוא דברים מעניינים. מהו הקובץ הגדול ביותר שמתארח, ומדוע הוא כל כך גדול? מהו הקובץ הקטן ביותר? האם יש דפוסים מעניינים או בלתי צפויים כשמדובר בקטגוריות מסוימות, שפות וכדומה? האם יש כותרים כפולים או דומים מאוד? האם יש דפוסים לזמן שבו נוספו נתונים, כמו יום אחד שבו נוספו הרבה קבצים בבת אחת? לעיתים קרובות ניתן ללמוד הרבה על ידי התבוננות במאגר הנתונים בדרכים שונות. במקרה שלנו, הסרנו כפילויות של ספרי Z-Library מול ה-md5 hashes ב-Library Genesis, ובכך חסכנו הרבה זמן הורדה ומקום בדיסק. זו סיטואציה די ייחודית. ברוב המקרים אין מאגרי מידע מקיפים של אילו קבצים כבר נשמרו כראוי על ידי פיראטים אחרים. זה כשלעצמו הוא הזדמנות עצומה למישהו שם בחוץ. יהיה נהדר לקבל סקירה מעודכנת באופן קבוע של דברים כמו מוזיקה וסרטים שכבר מופצים באופן נרחב באתרים של טורנטים, ולכן הם בעדיפות נמוכה יותר להכללה במראות פיראטיות. 6. הפצה יש לכם את הנתונים, ובכך יש לכם את המראה הפיראטי הראשון בעולם של המטרה שלכם (כנראה). במובנים רבים החלק הקשה ביותר מאחוריכם, אבל החלק המסוכן ביותר עדיין לפניכם. אחרי הכל, עד כה הייתם חמקמקים; טסים מתחת לרדאר. כל מה שהייתם צריכים לעשות זה להשתמש ב-VPN טוב לאורך כל הדרך, לא למלא את הפרטים האישיים שלכם בטפסים כלשהם (ברור), ואולי להשתמש במושב דפדפן מיוחד (או אפילו מחשב אחר). עכשיו עליכם להפיץ את הנתונים. במקרה שלנו, רצינו תחילה לתרום את הספרים בחזרה ל-Library Genesis, אך אז גילינו במהירות את הקשיים בכך (מיון בדיוני מול לא בדיוני). אז החלטנו על הפצה באמצעות טורנטים בסגנון Library Genesis. אם יש לכם הזדמנות לתרום לפרויקט קיים, זה יכול לחסוך לכם הרבה זמן. עם זאת, אין הרבה מראות פיראטיות מאורגנות היטב שם בחוץ כרגע. אז נניח שאתם מחליטים להפיץ טורנטים בעצמכם. נסו לשמור על הקבצים קטנים, כך שיהיה קל לשקף אותם באתרים אחרים. תצטרכו אז לזרוע את הטורנטים בעצמכם, תוך שמירה על אנונימיות. ניתן להשתמש ב-VPN (עם או בלי העברת פורטים), או לשלם עם ביטקוins מעורבלים עבור Seedbox. אם אינכם יודעים מה חלק מהמונחים הללו אומר, תצטרכו לקרוא הרבה, שכן חשוב שתבינו את הסיכונים כאן. ניתן לארח את קבצי הטורנט עצמם באתרים קיימים של טורנטים. במקרה שלנו, בחרנו לארח אתר בפועל, שכן רצינו גם להפיץ את הפילוסופיה שלנו בצורה ברורה. ניתן לעשות זאת בעצמכם באופן דומה (אנו משתמשים ב-Njalla עבור הדומיינים והאירוח שלנו, בתשלום עם ביטקוins מעורבלים), אך גם אל תהססו לפנות אלינו כדי שנארח את הטורנטים שלכם. אנו מחפשים לבנות אינדקס מקיף של מראות פיראטיות לאורך זמן, אם הרעיון הזה יתפוס. לגבי בחירת VPN, נכתב על כך הרבה כבר, אז פשוט נחזור על העצה הכללית של בחירה לפי מוניטין. מדיניות ללא לוגים שנבדקה בבית משפט עם היסטוריה ארוכה של הגנה על פרטיות היא האפשרות בעלת הסיכון הנמוך ביותר, לדעתנו. שימו לב שגם כשאתם עושים הכל נכון, לעולם לא תוכלו להגיע לאפס סיכון. לדוגמה, כאשר אתם זורעים את הטורנטים שלכם, שחקן מדינה בעל מוטיבציה גבוהה יכול כנראה להסתכל על זרימות הנתונים הנכנסות והיוצאות לשרתי VPN, ולהסיק מי אתם. או שאתם יכולים פשוט לטעות איכשהו. כנראה שכבר עשינו זאת, ונעשה זאת שוב. למרבה המזל, מדינות לא כל כך אכפת להן <em>ש</em> הרבה על פיראטיות. החלטה אחת שיש לקבל עבור כל פרויקט היא האם לפרסם אותו באמצעות אותה זהות כמו קודם, או לא. אם תמשיכו להשתמש באותו שם, אז טעויות באבטחת הפעולה מפרויקטים קודמים עלולות לחזור לנשוך אתכם. אבל פרסום תחת שמות שונים אומר שאתם לא בונים מוניטין מתמשך. בחרנו לקיים אבטחת פעולה חזקה מההתחלה כדי שנוכל להמשיך להשתמש באותה זהות, אבל לא נהסס לפרסם תחת שם שונה אם נטעה או אם הנסיבות ידרשו זאת. להפיץ את המידע יכול להיות מסובך. כפי שאמרנו, זו עדיין קהילה נישתית. במקור פרסמנו ב-Reddit, אבל באמת קיבלנו תשומת לב ב-Hacker News. לעת עתה ההמלצה שלנו היא לפרסם בכמה מקומות ולראות מה קורה. ושוב, צרו קשר איתנו. נשמח להפיץ את המילה על מאמצי ארכיבאות פיראטיים נוספים. 1. בחירת תחום / פילוסופיה אין מחסור בידע ומורשת תרבותית שצריך לשמר, וזה יכול להיות מכריע. לכן לעיתים קרובות כדאי לקחת רגע ולחשוב על מה התרומה שלכם יכולה להיות. לכל אחד יש דרך שונה לחשוב על זה, אבל הנה כמה שאלות שאתם יכולים לשאול את עצמכם: במקרה שלנו, היה לנו אכפת במיוחד מהשימור לטווח ארוך של המדע. ידענו על Library Genesis, ואיך שהוא שוקף במלואו פעמים רבות באמצעות טורנטים. אהבנו את הרעיון הזה. ואז יום אחד, אחד מאיתנו ניסה למצוא כמה ספרי לימוד מדעיים ב-Library Genesis, אבל לא הצליח למצוא אותם, מה שהעלה ספקות לגבי כמה הוא באמת שלם. חיפשנו אז את הספרים האלה באינטרנט, ומצאנו אותם במקומות אחרים, מה ששתל את הזרע לפרויקט שלנו. אפילו לפני שידענו על Z-Library, הייתה לנו הרעיון לא לנסות לאסוף את כל הספרים האלה ידנית, אלא להתמקד בשיקוף אוספים קיימים, ולתרום אותם בחזרה ל-Library Genesis. אילו כישורים יש לכם שאתם יכולים לנצל לטובתכם? לדוגמה, אם אתם מומחים לאבטחת מידע מקוונת, תוכלו למצוא דרכים להתגבר על חסימות IP ליעדים מאובטחים. אם אתם מצוינים בארגון קהילות, אז אולי תוכלו לאסוף כמה אנשים סביב מטרה. זה מועיל לדעת קצת תכנות, אפילו רק כדי לשמור על אבטחת פעולה טובה לאורך התהליך הזה. מה יהיה תחום בעל השפעה גבוהה להתמקד בו? אם אתם הולכים להקדיש X שעות לארכיון פיראטי, אז איך תוכלו לקבל את ה"תמורה הטובה ביותר לכסף שלכם"? מהן הדרכים הייחודיות שבהן אתם חושבים על זה? ייתכן שיש לכם רעיונות או גישות מעניינות שאחרים אולי פספסו. כמה זמן יש לכם לזה? העצה שלנו תהיה להתחיל בקטן ולעשות פרויקטים גדולים יותר ככל שתתרגלו לזה, אבל זה יכול להיות משתלט. למה אתם מתעניינים בזה? מה התשוקה שלכם? אם נוכל לאסוף קבוצה של אנשים שכולם מארכבים את הדברים שהם ספציפית אכפת להם מהם, זה יכסה הרבה! תדעו הרבה יותר מהאדם הממוצע על התשוקה שלכם, כמו מה הם הנתונים החשובים לשמור, מה הם האוספים והקהילות המקוונות הטובות ביותר, וכדומה. 3. גרידת metadata תאריך הוספה/שינוי: כך שתוכל לחזור מאוחר יותר ולהוריד קבצים שלא הורדת קודם (למרות שלעתים קרובות תוכל גם להשתמש במזהה או ב-hash לשם כך). Hash (md5, sha1): כדי לאשר שהורדת את הקובץ כראוי. מזהה: יכול להיות מזהה פנימי כלשהו, אבל מזהים כמו ISBN או DOI שימושיים גם כן. שם קובץ / מיקום תיאור, קטגוריה, תגיות, מחברים, שפה, וכו'. גודל: כדי לחשב כמה שטח דיסק אתה צריך. בואו נהיה קצת יותר טכניים כאן. כדי לגרד את ה-metadata מאתרים, שמרנו על הדברים פשוטים למדי. אנחנו משתמשים בסקריפטים של Python, לפעמים curl, ובמסד נתונים MySQL כדי לאחסן את התוצאות. לא השתמשנו בתוכנות גרידה מתוחכמות שיכולות למפות אתרים מורכבים, מכיוון שעד כה היינו צריכים לגרד רק סוג אחד או שניים של דפים על ידי פשוט מיון דרך מזהים וניתוח ה-HTML. אם אין דפים שניתן למיין בקלות, אז ייתכן שתצטרך זחלן מתאים שמנסה למצוא את כל הדפים. לפני שתתחיל לגרד אתר שלם, נסה לעשות זאת ידנית לזמן מה. עבור דרך כמה עשרות דפים בעצמך, כדי לקבל תחושה איך זה עובד. לפעמים כבר תיתקל בחסימות IP או בהתנהגות מעניינת אחרת בדרך זו. אותו דבר לגבי גרידת נתונים: לפני שתעמיק מדי במטרה זו, ודא שאתה יכול להוריד את הנתונים שלה ביעילות. כדי לעקוף מגבלות, יש כמה דברים שאתה יכול לנסות. האם יש כתובות IP או שרתים אחרים שמארחים את אותם נתונים אך אין להם את אותן מגבלות? האם יש נקודות קצה של API שאין להן מגבלות, בעוד שאחרות כן? באיזה קצב הורדה ה-IP שלך נחסם, ולכמה זמן? או שאתה לא נחסם אלא מואט? מה אם תיצור חשבון משתמש, איך הדברים משתנים אז? האם אתה יכול להשתמש ב-HTTP/2 כדי לשמור על חיבורים פתוחים, והאם זה מגדיל את הקצב שבו אתה יכול לבקש דפים? האם יש דפים שמפרטים מספר קבצים בבת אחת, והאם המידע המפורט שם מספיק? דברים שכנראה תרצה לשמור כוללים: בדרך כלל אנחנו עושים זאת בשני שלבים. קודם כל אנחנו מורידים את קבצי ה-HTML הגולמיים, בדרך כלל ישירות ל-MySQL (כדי להימנע מהרבה קבצים קטנים, עליהם נדבר יותר בהמשך). לאחר מכן, בשלב נפרד, אנחנו עוברים על קבצי ה-HTML האלה ומפרשים אותם לטבלאות MySQL אמיתיות. כך אתה לא צריך להוריד הכל מחדש אם אתה מגלה טעות בקוד הפירוש שלך, מכיוון שאתה יכול פשוט לעבד מחדש את קבצי ה-HTML עם הקוד החדש. זה גם לעיתים קרובות קל יותר להקביל את שלב העיבוד, ובכך לחסוך זמן (ואתה יכול לכתוב את קוד העיבוד בזמן שהגרידה פועלת, במקום לכתוב את שני השלבים בבת אחת). לבסוף, שימו לב שבחלק מהמטרות, גירוד metadata הוא כל מה שיש. ישנם אוספי metadata עצומים שם בחוץ שאינם נשמרים כראוי. כותרת בחירת תחום / פילוסופיה: על מה אתם רוצים להתמקד, ולמה? מהן התשוקות, הכישורים והנסיבות הייחודיות שלכם שאתם יכולים לנצל לטובתכם? בחירת יעד: איזו אוסף ספציפי תשקפו? גרידת metadata: קטלוג מידע על הקבצים, מבלי להוריד את הקבצים עצמם (שלעיתים קרובות גדולים בהרבה). בחירת נתונים: בהתבסס על ה-metadata, צמצום הנתונים הרלוונטיים ביותר לארכיון כרגע. זה יכול להיות הכל, אבל לעיתים קרובות יש דרך סבירה לחסוך מקום ורוחב פס. גרידת נתונים: קבלת הנתונים בפועל. הפצה: אריזת הנתונים בטורנטים, הכרזה עליהם איפשהו, והפצתם על ידי אנשים. 5. גירוד נתונים עכשיו אתם מוכנים להוריד את הנתונים בכמות גדולה. כפי שהוזכר קודם, בשלב זה עליכם כבר להוריד ידנית מספר קבצים, כדי להבין טוב יותר את ההתנהגות והמגבלות של המטרה. עם זאת, עדיין יהיו הפתעות שמחכות לכם ברגע שתתחילו להוריד הרבה קבצים בבת אחת. העצה שלנו כאן היא בעיקר לשמור על זה פשוט. התחילו פשוט בהורדת מספר קבצים. ניתן להשתמש ב-Python, ואז להתרחב למספר תהליכים. אבל לפעמים אפילו פשוט יותר הוא ליצור קבצי Bash ישירות ממסד הנתונים, ואז להריץ מספר מהם במספר חלונות טרמינל כדי להתרחב. טריק טכני מהיר שכדאי להזכיר כאן הוא שימוש ב-OUTFILE ב-MySQL, שניתן לכתוב בכל מקום אם מבטלים את "secure_file_priv" ב-mysqld.cnf (וודאו גם לבטל/לעקוף את AppArmor אם אתם על לינוקס). אנו מאחסנים את הנתונים על דיסקים קשיחים פשוטים. התחילו עם מה שיש לכם, והתרחבו לאט. זה יכול להיות מכריע לחשוב על אחסון מאות TBs של נתונים. אם זה המצב שאתם מתמודדים איתו, פשוט שימו תחילה תת-קבוצה טובה, ובהודעתכם בקשו עזרה באחסון השאר. אם אתם רוצים להשיג עוד כוננים קשיחים בעצמכם, אז ל-r/DataHoarder יש כמה משאבים טובים להשגת עסקאות טובות. נסו לא לדאוג יותר מדי לגבי מערכות קבצים מתוחכמות. קל ליפול למלכודת של הגדרת דברים כמו ZFS. פרט טכני אחד שכדאי להיות מודעים אליו הוא שמערכות קבצים רבות לא מתמודדות טוב עם הרבה קבצים. מצאנו שפיתרון פשוט הוא ליצור מספר ספריות, למשל עבור טווחי מזהים שונים או קידומות hash. לאחר הורדת הנתונים, וודאו לבדוק את שלמות הקבצים באמצעות hashes ב-metadata, אם זמין. 2. בחירת יעד נגיש: לא משתמש בהרבה שכבות הגנה כדי למנוע ממך לגרד את ה-metadata והנתונים שלהם. תובנה מיוחדת: יש לך מידע מיוחד על המטרה הזו, כמו גישה מיוחדת לאוסף הזה, או שהבנת איך להתגבר על ההגנות שלהם. זה לא נדרש (הפרויקט הקרוב שלנו לא עושה משהו מיוחד), אבל זה בהחלט עוזר! גדול אז, יש לנו את האזור שאנחנו מתמקדים בו, עכשיו איזו אוסף ספציפי אנחנו משכפלים? יש כמה דברים שהופכים למטרה טובה: כשמצאנו את ספרי הלימוד המדעיים שלנו באתרים אחרים מאשר Library Genesis, ניסינו להבין איך הם הגיעו לאינטרנט. אז מצאנו את Z-Library, והבנו שבעוד שרוב הספרים לא מופיעים שם לראשונה, הם בסופו של דבר מגיעים לשם. למדנו על הקשר שלו ל-Library Genesis, ועל מבנה התמריצים (הכספיים) והממשק המשתמש המעולה, ששניהם הפכו אותו לאוסף הרבה יותר שלם. אז עשינו קצת גרידת metadata ונתונים ראשונית, והבנו שאנחנו יכולים לעקוף את מגבלות ההורדה של ה-IP שלהם, תוך ניצול הגישה המיוחדת של אחד מחברינו להרבה שרתי פרוקסי. כשאתם חוקרים מטרות שונות, כבר חשוב להסתיר את העקבות שלכם על ידי שימוש ב-VPNs וכתובות אימייל חד פעמיות, עליהם נדבר יותר מאוחר. ייחודי: לא מכוסה היטב על ידי פרויקטים אחרים. כשאנחנו מבצעים פרויקט, יש לו כמה שלבים: אלה לא שלבים עצמאיים לחלוטין, ולעיתים תובנות משלב מאוחר יותר מחזירות אתכם לשלב מוקדם יותר. לדוגמה, במהלך גרידת metadata אתם עשויים לגלות שהיעד שבחרתם יש לו מנגנוני הגנה מעבר לרמת הכישורים שלכם (כמו חסימות IP), ולכן תחזרו ותמצאו יעד אחר. - אנה והצוות (<a %(reddit)s>Reddit</a>) ניתן לכתוב ספרים שלמים על ה"למה" של שימור דיגיטלי בכלל, וארכיבאות פיראטית בפרט, אבל בואו ניתן הקדמה קצרה למי שלא מכיר. העולם מייצר יותר ידע ותרבות מאי פעם, אבל גם יותר מזה הולך לאיבוד מאי פעם. האנושות מפקידה בעיקר את המורשת הזו בידי תאגידים כמו מוציאים לאור אקדמיים, שירותי סטרימינג וחברות מדיה חברתית, ולעיתים קרובות הם לא הוכיחו את עצמם כמשמרים טובים. בדקו את הסרט התיעודי Digital Amnesia, או כל הרצאה של ג'ייסון סקוט. ישנם מוסדות שעושים עבודה טובה בארכוב ככל שהם יכולים, אבל הם כפופים לחוק. כפיראטים, אנו במצב ייחודי לארכב אוספים שהם לא יכולים לגעת בהם, בגלל אכיפת זכויות יוצרים או מגבלות אחרות. אנו יכולים גם לשקף אוספים פעמים רבות, ברחבי העולם, ובכך להגדיל את הסיכויים לשימור נכון. לעת עתה, לא ניכנס לדיונים על היתרונות והחסרונות של קניין רוחני, המוסריות של הפרת החוק, הרהורים על צנזורה, או נושא הגישה לידע ותרבות. עם כל זה מחוץ לדרך, בואו נצלול ל"איך". נשתף איך הצוות שלנו הפך לארכיבאים פיראטיים, והלקחים שלמדנו בדרך. ישנם אתגרים רבים כאשר מתחילים במסע הזה, וכולי תקווה שנוכל לעזור לכם עם חלק מהם. איך להפוך לארכיבאי פיראטי האתגר הראשון עשוי להיות מפתיע. זה לא בעיה טכנית, או בעיה משפטית. זו בעיה פסיכולוגית. לפני שנצלול פנימה, שני עדכונים על מראה ספריית הפיראטים (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון של אנה</a>): קיבלנו כמה תרומות נדיבות במיוחד. הראשונה הייתה 10,000 דולר מאדם אנונימי שגם תמך ב"bookwarrior", המייסד המקורי של Library Genesis. תודה מיוחדת ל-bookwarrior על סיוע בתרומה זו. השנייה הייתה עוד 10,000 דולר מתורם אנונימי, שיצר קשר לאחר השחרור האחרון שלנו, והושפע לעזור. היו לנו גם מספר תרומות קטנות יותר. תודה רבה על כל התמיכה הנדיבה שלכם. יש לנו כמה פרויקטים מרגשים בצנרת שזה יתמוך בהם, אז הישארו מעודכנים. היו לנו כמה קשיים טכניים עם גודל השחרור השני שלנו, אבל הטורנטים שלנו פעילים ומשותפים כעת. גם קיבלנו הצעה נדיבה מאדם אנונימי לשתף את האוסף שלנו על השרתים המהירים מאוד שלו, אז אנחנו עושים העלאה מיוחדת למכונות שלהם, ולאחר מכן כל מי שמוריד את האוסף אמור לראות שיפור גדול במהירות. פוסטים בבלוג שלום, אני אנה. יצרתי את <a %(wikipedia_annas_archive)s>הארכיון של אנה</a>, הספרייה המוצלת הגדולה בעולם. זהו הבלוג האישי שלי, בו אני והצוות שלי כותבים על פיראטיות, שימור דיגיטלי ועוד. התחברו איתי ב-<a %(reddit)s>Reddit</a>. שימו לב שהאתר הזה הוא רק בלוג. אנחנו מארחים כאן רק את המילים שלנו. לא מתארחים כאן או מקושרים קבצים מוגנים בזכויות יוצרים או טורנטים. <strong>ספרייה</strong> - כמו רוב הספריות, אנו מתמקדים בעיקר בחומרים כתובים כמו ספרים. ייתכן שנרחיב לסוגי מדיה אחרים בעתיד. <strong>מראה</strong> - אנו אך ורק מראה של ספריות קיימות. אנו מתמקדים בשימור, לא בהפיכת הספרים לחיפושיים ולהורדה קלה (גישה) או בטיפוח קהילה גדולה של אנשים שתורמים ספרים חדשים (מקור). <strong>פיראט</strong> - אנו מפרים בכוונה את חוקי זכויות היוצרים ברוב המדינות. זה מאפשר לנו לעשות משהו שגופים חוקיים אינם יכולים לעשות: לוודא שהספרים משוכפלים רחוק ורחב. <em>איננו מקשרים לקבצים מהבלוג הזה. אנא מצאו אותם בעצמכם.</em> - אנה והצוות (<a %(reddit)s>Reddit</a>) פרויקט זה (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון אנה</a>) שואף לתרום לשימור ושחרור הידע האנושי. אנו עושים את תרומתנו הקטנה והצנועה, בעקבות הגדולים לפנינו. מיקוד הפרויקט הזה מודגם בשמו: הספרייה הראשונה ששכפלנו היא Z-Library. זו ספרייה פופולרית (ולא חוקית). הם לקחו את אוסף Library Genesis והפכו אותו לחיפוש קל. בנוסף לכך, הם הפכו ליעילים מאוד בגיוס תרומות ספרים חדשות, על ידי תמרוץ משתמשים תורמים עם הטבות שונות. נכון לעכשיו, הם אינם תורמים את הספרים החדשים הללו בחזרה ל-Library Genesis. ובניגוד ל-Library Genesis, הם אינם הופכים את האוסף שלהם לשכפול קל, מה שמונע שימור רחב. זה חשוב למודל העסקי שלהם, מכיוון שהם גובים כסף עבור גישה לאוסף שלהם בכמות גדולה (יותר מ-10 ספרים ביום). איננו מביעים שיפוט מוסרי על גביית כסף עבור גישה בכמות גדולה לאוסף ספרים לא חוקי. אין ספק ש-Z-Library הצליחו להרחיב את הגישה לידע ולהשיג יותר ספרים. אנו כאן פשוט כדי לעשות את חלקנו: להבטיח את השימור לטווח ארוך של האוסף הפרטי הזה. נשמח להזמין אתכם לעזור לשמר ולשחרר את הידע האנושי על ידי הורדה והפצת הטורנטים שלנו. ראו את דף הפרויקט למידע נוסף על איך הנתונים מאורגנים. נשמח גם להזמין אתכם לתרום את רעיונותיכם לגבי אילו אוספים לשכפל בהמשך, וכיצד לעשות זאת. יחד נוכל להשיג הרבה. זו רק תרומה קטנה בין אינספור אחרות. תודה, על כל מה שאתם עושים. הצגת מראה הספרייה הפיראטית: שמירה על 7TB של ספרים (שאינם ב-Libgen) 10% o מהמורשת הכתובה של האנושות נשמרת לנצח <strong>גוגל.</strong> אחרי הכל, הם עשו את המחקר הזה עבור Google Books. עם זאת, ה-metadata שלהם אינו נגיש בכמות גדולה וקשה לגרד אותו. <strong>מערכות ספריות וארכיונים בודדים שונים.</strong> ישנן ספריות וארכיונים שלא נכללו ואוגדו על ידי אף אחד מהנ"ל, לעיתים קרובות מכיוון שהם חסרי מימון, או מסיבות אחרות אינם רוצים לשתף את הנתונים שלהם עם אופנ-לייברי, OCLC, גוגל, וכדומה. הרבה מהם אכן יש להם רשומות דיגיטליות נגישות דרך האינטרנט, והן לעיתים קרובות לא מאוד מוגנות, אז אם אתה רוצה לעזור וליהנות מללמוד על מערכות ספריות מוזרות, אלה נקודות התחלה נהדרות. <strong>ISBNdb.</strong> זהו נושא הפוסט בבלוג הזה. ISBNdb מגרד אתרים שונים עבור metadata של ספרים, במיוחד נתוני תמחור, אותם הם מוכרים לאחר מכן למוכרי ספרים, כך שהם יכולים לתמחר את ספריהם בהתאם לשאר השוק. מכיוון ש-ISBNs הם די אוניברסליים בימינו, הם למעשה בנו "דף אינטרנט לכל ספר". <strong>אופנ-לייברי.</strong> כפי שהוזכר קודם, זו כל המשימה שלהם. הם אספו כמויות עצומות של נתוני ספריות מספריות משתפות פעולה וארכיונים לאומיים, וממשיכים לעשות זאת. יש להם גם ספרנים מתנדבים וצוות טכני שמנסים להסיר כפילויות ברשומות, ולתייג אותן עם כל מיני metadata. הכי טוב, ה-dataset שלהם פתוח לחלוטין. אתה יכול פשוט <a %(openlibrary)s>להוריד אותו</a>. <strong>WorldCat.</strong> זהו אתר שמנוהל על ידי הארגון ללא מטרות רווח OCLC, שמוכר מערכות ניהול ספריות. הם מאגדים metadata של ספרים מספריות רבות, ומעמידים אותו לרשות הציבור דרך אתר WorldCat. עם זאת, הם גם מרוויחים כסף ממכירת נתונים אלה, ולכן הם אינם זמינים להורדה בכמות גדולה. יש להם כמה datasets בכמות מוגבלת יותר להורדה, בשיתוף פעולה עם ספריות מסוימות. 1. עבור הגדרה סבירה כלשהי של "לנצח". ;) 2. כמובן, המורשת הכתובה של האנושות היא הרבה יותר מספרים, במיוחד בימינו. למען הפוסט הזה וההוצאות האחרונות שלנו אנו מתמקדים בספרים, אך תחומי העניין שלנו מתפרשים רחוק יותר. 3. יש הרבה יותר שניתן לומר על אהרון שוורץ, אבל רצינו רק להזכיר אותו בקצרה, שכן הוא משחק תפקיד מרכזי בסיפור הזה. ככל שהזמן עובר, יותר אנשים עשויים להיתקל בשמו לראשונה, ויכולים לאחר מכן לצלול לתוך החור הארנב בעצמם. <strong>עותקים פיזיים.</strong> ברור שזה לא מאוד מועיל, שכן הם רק כפילויות של אותו חומר. זה יהיה מגניב אם נוכל לשמר את כל ההערות שאנשים עושים בספרים, כמו "השרבוטים בשוליים" המפורסמים של פרמה. אבל אבוי, זה יישאר חלום של ארכיונאי. <strong>“מהדורות”.</strong> כאן סופרים כל גרסה ייחודית של ספר. אם משהו בו שונה, כמו כריכה שונה או הקדמה שונה, זה נחשב כמהדורה שונה. <strong>קבצים.</strong> כאשר עובדים עם ספריות צללים כמו Library Genesis, Sci-Hub או Z-Library, יש שיקול נוסף. יכולות להיות מספר סריקות של אותה מהדורה. ואנשים יכולים ליצור גרסאות טובות יותר של קבצים קיימים, על ידי סריקת הטקסט באמצעות OCR, או תיקון עמודים שנסרקו בזווית. אנחנו רוצים לספור את הקבצים האלה כמהדורה אחת בלבד, מה שידרוש metadata טוב, או הסרת כפילויות באמצעות מדדי דמיון מסמכים. <strong>“יצירות”.</strong> לדוגמה, "הארי פוטר וחדר הסודות" כרעיון לוגי, הכולל את כל הגרסאות שלו, כמו תרגומים והדפסות חוזרות שונות. זו הגדרה די שימושית, אבל יכול להיות קשה לקבוע מה נחשב. לדוגמה, כנראה שנרצה לשמר תרגומים שונים, אם כי הדפסות חוזרות עם הבדלים מינוריים בלבד עשויות להיות פחות חשובות. - אנה והצוות (<a %(reddit)s>Reddit</a>) עם מראה הספרייה הפיראטית (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון אנה</a>), מטרתנו היא לקחת את כל הספרים בעולם ולשמר אותם לנצח.<sup>1</sup> בין הטורנטים של Z-Library והטורנטים המקוריים של Library Genesis, יש לנו 11,783,153 קבצים. אבל כמה זה באמת? אם היינו מבצעים דה-דופליקציה נכונה של הקבצים הללו, איזה אחוז מכל הספרים בעולם שמרנו? היינו רוצים שיהיה לנו משהו כזה: בואו נתחיל עם כמה מספרים גסים: ב-Z-Library/Libgen ובאופנ-לייברי יש הרבה יותר ספרים מאשר ISBNs ייחודיים. האם זה אומר שהרבה מהספרים האלה אין להם ISBNs, או שה-metadata של ה-ISBN פשוט חסר? כנראה נוכל לענות על שאלה זו עם שילוב של התאמה אוטומטית על בסיס תכונות אחרות (כותרת, מחבר, מוציא לאור, וכו'), משיכת מקורות נתונים נוספים, והפקת ISBNs מהסריקות של הספרים עצמם (במקרה של Z-Library/Libgen). כמה מה-ISBNs האלה הם ייחודיים? זה הכי טוב להמחיש עם דיאגרמת ון: כדי להיות מדויקים יותר: הופתענו מכמה מעט חפיפה יש! ל-ISBNdb יש כמות עצומה של ISBNs שלא מופיעים לא ב-Z-Library ולא ב-Open Library, והדבר נכון (במידה קטנה אך עדיין משמעותית) גם לשניים האחרים. זה מעלה הרבה שאלות חדשות. כמה תעזור התאמה אוטומטית בתיוג הספרים שלא תויגו עם ISBNs? האם יהיו הרבה התאמות ולכן חפיפה מוגברת? בנוסף, מה יקרה אם נכניס מאגר נתונים רביעי או חמישי? כמה חפיפה נראה אז? זה נותן לנו נקודת התחלה. כעת נוכל להסתכל על כל ה-ISBNs שלא היו במאגר הנתונים של Z-Library, ושלא תואמים גם את שדות הכותרת/מחבר. זה יכול לתת לנו ידית לשימור כל הספרים בעולם: תחילה על ידי סריקת האינטרנט לסריקות, ואז על ידי יציאה לחיים האמיתיים לסרוק ספרים. האחרון יכול אפילו להיות ממומן על ידי הציבור, או מונע על ידי "פרסים" מאנשים שהיו רוצים לראות ספרים מסוימים דיגיטליים. כל זה הוא סיפור לזמן אחר. אם ברצונכם לעזור בכל אחד מהנושאים הללו — ניתוח נוסף; סריקת metadata נוספת; מציאת ספרים נוספים; OCR של ספרים; עשיית זאת עבור תחומים אחרים (למשל מאמרים, ספרי שמע, סרטים, תוכניות טלוויזיה, מגזינים) או אפילו הפיכת חלק מהנתונים הללו לזמינים לדברים כמו ML / אימון מודלים של שפה גדולה — אנא צרו קשר איתי (<a %(reddit)s>Reddit</a>). אם אתם מעוניינים במיוחד בניתוח הנתונים, אנו עובדים על הפיכת מאגרי הנתונים והסקריפטים שלנו לזמינים בפורמט קל יותר לשימוש. יהיה נהדר אם תוכלו פשוט לפצל מחברת ולהתחיל לשחק עם זה. לבסוף, אם ברצונכם לתמוך בעבודה זו, אנא שקלו לתרום. זו פעולה המנוהלת כולה על ידי מתנדבים, והתרומה שלכם עושה הבדל עצום. כל תרומה עוזרת. לעת עתה אנו מקבלים תרומות בקריפטו; ראו את דף התרומות בארכיון של אנה. כדי לקבל אחוז, אנו זקוקים למכנה: המספר הכולל של הספרים שפורסמו אי פעם.<sup>2</sup> לפני סיום פרויקט Google Books, מהנדס בפרויקט, ליאוניד טייצ'ר, <a %(booksearch_blogspot)s>ניסה להעריך</a> את המספר הזה. הוא הגיע — בלשון צינית — ל-129,864,880 ("לפחות עד יום ראשון"). הוא העריך את המספר הזה על ידי בניית מסד נתונים מאוחד של כל הספרים בעולם. לשם כך, הוא אסף יחד מערכות נתונים שונות ואז מיזג אותן בדרכים שונות. כמאמר מוסגר, יש אדם נוסף שניסה לקטלג את כל הספרים בעולם: אהרון שוורץ, הפעיל הדיגיטלי המנוח ומייסד שותף של Reddit.<sup>3</sup> הוא <a %(youtube)s>הקים את אופנ-לייברי</a> במטרה ליצור "דף אינטרנט לכל ספר שפורסם אי פעם", תוך שילוב נתונים ממקורות רבים ושונים. הוא שילם את המחיר האולטימטיבי על עבודתו לשימור דיגיטלי כאשר הועמד לדין על הורדת מאמרים אקדמיים בכמות גדולה, מה שהוביל להתאבדותו. אין צורך לומר, זו אחת הסיבות לכך שהקבוצה שלנו פועלת באופן פסאודונימי, ולמה אנחנו נזהרים מאוד. אופנ-לייברי עדיין מנוהל בגבורה על ידי אנשים בארכיון האינטרנט, וממשיך את מורשתו של אהרון. נחזור לזה מאוחר יותר בפוסט הזה. בפוסט בבלוג של גוגל, טייצ'ר מתאר כמה מהאתגרים בהערכת המספר הזה. ראשית, מה נחשב לספר? יש כמה הגדרות אפשריות: “מהדורות” נראות כהגדרה הפרקטית ביותר למה הם "ספרים". בנוחות, הגדרה זו משמשת גם להקצאת מספרי ISBN ייחודיים. ISBN, או מספר ספר סטנדרטי בינלאומי, משמש בדרך כלל למסחר בינלאומי, שכן הוא משולב עם מערכת הברקודים הבינלאומית ("מספר פריט בינלאומי"). אם אתה רוצה למכור ספר בחנויות, הוא צריך ברקוד, ולכן אתה מקבל ISBN. הפוסט בבלוג של טייצ'ר מזכיר שבעוד ש-ISBNs הם שימושיים, הם לא אוניברסליים, שכן הם אומצו באמת רק באמצע שנות השבעים, ולא בכל מקום בעולם. עדיין, ISBN הוא כנראה המזהה הנפוץ ביותר של מהדורות ספרים, ולכן זהו נקודת ההתחלה הטובה ביותר שלנו. אם נוכל למצוא את כל ה-ISBNs בעולם, נקבל רשימה שימושית של אילו ספרים עדיין צריכים להישמר. אז, מאיפה אנחנו מקבלים את הנתונים? ישנם מספר מאמצים קיימים שמנסים להרכיב רשימה של כל הספרים בעולם: בפוסט הזה, אנו שמחים להכריז על שחרור קטן (בהשוואה לשחרורים הקודמים שלנו של Z-Library). גרדנו את רוב ISBNdb, והפכנו את הנתונים לזמינים להורדה בטורנט באתר של מראת הספרייה הפיראטית (עריכה: הועבר ל<a %(wikipedia_annas_archive)s>ארכיון של אנה</a>; לא נקשר אותו כאן ישירות, פשוט חפשו אותו). מדובר בכ-30.9 מיליון רשומות (20GB כ<a %(jsonlines)s>JSON Lines</a>; 4.4GB דחוס). באתר שלהם הם טוענים שיש להם למעשה 32.6 מיליון רשומות, אז אולי איכשהו פספסנו כמה, או <em>הם</em> עשויים לעשות משהו לא נכון. בכל מקרה, לעת עתה לא נשתף בדיוק איך עשינו את זה — נשאיר את זה כתרגיל לקורא. ;-) מה שנשתף הוא ניתוח ראשוני, כדי לנסות להתקרב להערכת מספר הספרים בעולם. הסתכלנו על שלושה datasets: ה-dataset החדש של ISBNdb, השחרור המקורי שלנו של metadata שגרדנו מספריית הצללים Z-Library (שכולל את Library Genesis), ו-dump הנתונים של אופנ-לייברי. השלכת ISBNdb, או כמה ספרים נשמרים לנצח? אם היינו מבצעים דה-דופליקציה נכונה של הקבצים מספריות הצללים, איזה אחוז מכל הספרים בעולם שמרנו? עדכונים על <a %(wikipedia_annas_archive)s>הארכיון של אנה</a>, הספרייה הפתוחה באמת הגדולה ביותר בהיסטוריה האנושית. <em>עיצוב מחדש של WorldCat</em> נתונים <strong>פורמט?</strong> <a %(blog)s>מיכלי הארכיון של אנה (AAC)</a>, שהם בעצם <a %(jsonlines)s>JSON Lines</a> דחוסים עם <a %(zstd)s>Zstandard</a>, בתוספת כמה סמנטיקות סטנדרטיות. מיכלים אלו עוטפים סוגים שונים של רשומות, בהתבסס על הסריקות השונות שביצענו. לפני שנה, <a %(blog)s>יצאנו</a> לענות על השאלה הזו: <strong>איזה אחוז מהספרים נשמרו לצמיתות על ידי ספריות מוצלות?</strong> בואו נסתכל על כמה מידע בסיסי על הנתונים: ברגע שספר נכנס לספרייה מוצלת עם נתונים פתוחים כמו <a %(wikipedia_library_genesis)s>Library Genesis</a>, ועכשיו <a %(wikipedia_annas_archive)s>הארכיון של אנה</a>, הוא משוכפל בכל העולם (באמצעות טורנטים), ובכך נשמר למעשה לנצח. כדי לענות על השאלה איזה אחוז מהספרים נשמר, אנחנו צריכים לדעת את המכנה: כמה ספרים קיימים בסך הכל? ובאופן אידיאלי לא רק שיהיה לנו מספר, אלא גם מטאדאטה אמיתית. כך נוכל לא רק להתאים אותם לספריות מוצלות, אלא גם <strong>ליצור רשימת TODO של הספרים שנותרו לשימור!</strong> נוכל אפילו להתחיל לחלום על מאמץ המוני לעבור על רשימת ה-TODO הזו. אספנו <a %(wikipedia_isbndb_com)s>ISBNdb</a>, והורדנו את <a %(openlibrary)s>מאגר הנתונים של Open Library</a>, אך התוצאות לא היו מספקות. הבעיה העיקרית הייתה שלא היה הרבה חפיפה של ISBNs. ראו את הדיאגרמת ון הזו מ<a %(blog)s>הפוסט בבלוג שלנו</a>: היינו מאוד מופתעים מכמה מעט חפיפה הייתה בין ISBNdb ו-Open Library, שניהם כוללים בנדיבות נתונים ממקורות שונים, כמו סריקות רשת ורשומות ספרייה. אם שניהם עושים עבודה טובה במציאת רוב ה-ISBNs הקיימים, המעגלים שלהם בוודאי היו חופפים באופן משמעותי, או שאחד היה תת-קבוצה של השני. זה גרם לנו לתהות, כמה ספרים נופלים <em>לחלוטין מחוץ למעגלים האלה</em>? אנחנו צריכים מאגר נתונים גדול יותר. זה היה הרגע שבו שמנו את עינינו על מאגר הספרים הגדול ביותר בעולם: <a %(wikipedia_worldcat)s>WorldCat</a>. זהו מאגר נתונים קנייני של הארגון ללא מטרות רווח <a %(wikipedia_oclc)s>OCLC</a>, שמאגד רשומות metadata מספריות מכל רחבי העולם, בתמורה למתן גישה לספריות למאגר המלא, ולהופעתן בתוצאות החיפוש של משתמשי הקצה. למרות ש-OCLC הוא ארגון ללא מטרות רווח, המודל העסקי שלהם דורש הגנה על מאגר הנתונים שלהם. ובכן, אנחנו מצטערים לומר, חברים ב-OCLC, אנחנו נותנים את הכל בחינם. :-) במהלך השנה האחרונה, אספנו בקפידה את כל הרשומות של WorldCat. בתחילה, היה לנו מזל. WorldCat בדיוק השיקו את עיצוב האתר החדש שלהם (באוגוסט 2022). זה כלל שדרוג משמעותי של מערכות הבק-אנד שלהם, שהציגו פגמי אבטחה רבים. מיד ניצלנו את ההזדמנות, והצלחנו לאסוף מאות מיליוני (!) רשומות בתוך ימים ספורים. לאחר מכן, פגמי האבטחה תוקנו לאט לאט אחד אחד, עד שהאחרון שמצאנו תוקן לפני כחודש. עד אז היו לנו כמעט כל הרשומות, ורק חיפשנו רשומות באיכות מעט גבוהה יותר. אז הרגשנו שזה הזמן לשחרר! 1.3B גירוד WorldCat <em><strong>בקצרה:</strong> הארכיון של אנה גירד את כל WorldCat (אוסף המטאדאטה של הספריות הגדול בעולם) כדי ליצור רשימת TODO של ספרים שצריך לשמר.</em> WorldCat אזהרה: פוסט הבלוג הזה הוצא משימוש. החלטנו ש-IPFS עדיין לא מוכן לשימוש רחב. עדיין נקשר לקבצים ב-IPFS מהארכיון של אנה כשאפשר, אבל לא נאחסן אותו בעצמנו יותר, וגם לא נמליץ לאחרים לשקף באמצעות IPFS. אנא ראו את דף הטורנטים שלנו אם אתם רוצים לעזור לשמר את האוסף שלנו. עזרו לזרוע את Z-Library על IPFS הורדה באמצעות שרת חברים SciDB השאלה חיצונית השאלה חיצונית (ספר מודפס לא זמין) הורדה חיצונית חקור מטא-דאטה מכיל בטורנטים חזרה  (+%(num)s בונוס) לא משולם שולם בוטל פקע ממתינים לאישורה של אנה לא חוקי הטקסט למטה ממשיך באנגלית. לך איפוס קדימה אחרון אם האימייל שלך לא עובד ב Libgen forums, אנו ממליצים על שימוש ב-<a %(a_mail)s>Proton Mail</a> (חינם). תוכלו גם <a %(a_manual)s>לבקש באופן ידני</a> להפעיל חשבון מסוים. (יתכן שיהיה צורך ב<a %(a_browser)s>אימות דפדפן</a> — הורדות לא מוגבלות!) שרת חברים מהיר #%(number)s (מומלץ) (מהיר יותר במעט אך עם רשימת המתנה) (לא נדרש אימות דפדפן) (ללא אימות דפדפן או רשימות המתנה) (ללא רשימת המתנה, אך יכול להיות איטי מאוד) שרת חברים איטי #%(number)s ספר שמע ספר קומיקס ספר (פרוזה) ספר (עיון) ספר (בלתי ידוע) מאמר בכתב עת מגזין תווים מוזיקליים אחר מסמך סטנדרטי לא כל הדפים ניתנים להמרה ל-PDF מסומן כשבור ב-Libgen.li לא זמין לצפייה ב-Libgen.li לא זמין לצפייה ב-Libgen.rs Non-Fiction לא זמין לצפייה ב-Libgen.rs Non-Fiction הרצת exiftool נכשלה על קובץ זה סומן כ"קובץ גרוע" ב-Z-Library חסר ב-Z-Library סומן כ"ספאם" ב-Z-Library הקובץ לא נפתח (למשל: קובץ פגום, DRM) טענת זכויות יוצרים בעיות הורדה (למשל: לא מתחבר, הודעה שגיאה, איטי מאד) מטה-דאטה שגוי (למשל: כותרת, תיאור, עטיפה) אחר איכות ירודה (למשל: בעיות בפורמט, איכות סריקה ירודה, עמודים חסרים) ספאם/יש להסיר את הקובץ (למשל: פרסומת, תוכן בלתי הולם) %(amount)s (%(amount_usd)s) %(amount)s סך הכל %(amount)s (%(amount_usd)s) סך הכל תולעת ספרים מבריקה ספרן בר מזל אגרן מדהים ארכיונאי מדהים הורדות בונוס Cerlalc מטה-דאטה צ'כי DuXiu 读秀 אינדקס ספרים אלקטרוניים של EBSCOhost Google Books Goodreads האתי טראסט IA השאלת ספרים דיגיטליים מבוקרת של IA ISBNdb ISBN GRP Libgen.li למעט "scimag" Libgen.rs עיון וספרות יפה Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary הספרייה הלאומית של רוסיה Sci-Hub דרך Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor העלאות ל-AA Z-Library Z-Library סינית כותרת, מחבר, DOI, ISBN, MD5, … חיפוש מחבר תיאור והערות מטה-דאטה מהדורה שם קובץ מקורי מוציא לאור (חיפוש שדה ספציפי) כותרת שנת פרסום פרטים טכניים מטבע זה גבוה מהמינימום הרגיל. אנא בחר דרך אחרת או מטבע אחר. הבקשה לא הושלמה. אנא נסה שוב בעוד מספר דקות, ואם זה ממשיך לקרות צור איתנו קשר ב-%(email)s עם צילום מסך. שגיאה בלתי ידועה. אנא צור קשר עם צילום המסך בכתובת:%(email)s . שגיאה בעיבוד התשלום. אנא המתינו רגע ונסו שוב. אם הבעיה נמשכת יותר מ-24 שעות, אנא צרו קשר איתנו ב-%(email)s עם צילום מסך. אנו מנהלים גיוס כספים עבור <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">גיבוי</a> ספריית הקומיקס הגדולה ביותר בעולם. תודה על תמיכתכם! <a href="/donate">תרמו.</a> אם אינכם יכולים לתרום, שקלו לתמוך בנו על ידי שיתוף עם חברים, ומעקב אחרינו ב-<a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, או <a href="https://t.me/annasarchiveorg">Telegram</a>. אל תשלחו לנו דוא"ל ל<a %(a_request)s>בקשת ספרים</a><br>או <a %(a_upload)s>העלאות</a> קטנות (פחות מ-10k). הארכיון של אנה תביעות DMCA / זכויות יוצרים שמרו על קשר Reddit חלופות SLUM (%(unaffiliated)s) לא משויך הארכיון של אנה זקוק לעזרתכם! אם תתרמו עכשיו, תקבלו <strong>כפול</strong> ממספר ההורדות המהירות. רבים מנסים להפיל אותנו, אבל אנחנו נלחמים בחזרה. אם תתרמו החודש, תקבלו <strong>כפול</strong> מספר ההורדות המהירות. בתוקף עד סוף החודש הזה. שמירת ידע אנושי: מתנה נהדרת לחג! החברויות יוארכו בהתאם. שרתים שותפים אינם זמינים עקב סגירת אירוח. הם אמורים לחזור לפעול בקרוב. כדי להגדיל את החוסן של ארכיון אנה, אנחנו מחפשים מתנדבים להפעיל מראות. יש לנו שיטת תרומה חדשה זמינה: %(method_name)s. אנא שקלו %(donate_link_open_tag)sלתרום</a> — זה לא זול להפעיל את האתר הזה, והתרומה שלכם באמת עושה הבדל. תודה רבה. הפנה חבר, ואתה וחברך תקבלו %(percentage)s%% הורדות מהירות נוספות! הפתיעו אדם אהוב, תנו לו חשבון חברות. המתנה המושלמת לוולנטיין! למידע נוסף… חשבון פעילות מתקדם הבלוג של אנה ↗ התוכנה של אנה ↗ בטא חוקר קודים Datasets תרום קבצים שהורדו שאלות נפוצות דף הבית שפרו מטה-דאטה נתוני LLM התחברות / הרשמה התרומות שלי פרופיל ציבורי חיפוש אבטחה טורנטים תרגם ↗ התנדבות ופרסים הורדות אחרונות: 📚&nbsp;הספרייה הגדולה בעולם בקוד פתוח ובנתונים פתוחים. ⭐️&nbsp;משקפת את Sci-Hub, Library Genesis, Z-Library, ועוד. 📈&nbsp;%(book_any)s ספרים, %(journal_article)s מאמרים, %(book_comic)s קומיקס, %(magazine)s מגזינים — נשמרים לנצח.  ו  ועוד DuXiu ספריית ההשאלה של Internet Archive LibGen 📚&nbsp;הספרייה הפתוחה הגדולה ביותר בהיסטוריה האנושית. 📈&nbsp;%(book_count)s&nbsp;ספרים, %(paper_count)s&nbsp;ניירות— נשמר לנצח. ⭐️&nbsp;אנחנו שרת מראה%(libraries)s. אנחנו מגרדים ומפרסמים בקוד פתוח %(scraped)s. כל הקוד והנתונים שלנו הם בקוד פתוח לחלוטין. OpenLib Sci-Hub ,  📚 הספרייה הגדולה בעולם בקוד פתוח ובנתונים פתוחים.<br>⭐️ משקפת את Scihub, Libgen, Zlib ועוד. Z-Lib הארכיון של אנה בקשה לא חוקית. בקר ב-%(websites)s. הספרייה הגדולה בעולם בקוד פתוח ובנתונים פתוחים. משקפת את Sci-Hub, Library Genesis, Z-Library ועוד. חפש בארכיון של אנה הארכיון של אנה אנא רענן כדי לנסות שוב. <a %(a_contact)s>צור קשר</a> אם הבעיה נמשכת במשך מספר שעות. 🔥 בעיה בטעינת הדף הזה <li>1. עקבו אחרינו ב-<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, או ב-<a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. הפיצו את הבשורה על הארכיון של אנה בטוויטר, Reddit, Tiktok, אינסטגרם, בבית הקפה המקומי שלכם או בספרייה, או בכל מקום שאתם הולכים! אנחנו לא מאמינים בשמירת סודות — אם יפילו אותנו, פשוט נצוץ מחדש במקום אחר, מכיוון שכל הקוד והנתונים שלנו הם קוד פתוח לחלוטין.</li><li>3. אם אתם יכולים, שקלו <a href="/donate">לתרום</a>.</li><li>4. עזרו <a href="https://translate.annas-software.org/">לתרגם</a> את האתר שלנו לשפות שונות.</li><li>5. אם אתם מהנדסי תוכנה, שקלו לתרום לקוד הפתוח שלנו <a href="https://annas-software.org/">, או לשתף את הטורנטים שלנו <a href="/datasets">.</a>.</li> 10. צרו או עזרו לתחזק את דף הוויקיפדיה של הארכיון של אנה בשפתכם. 11. אנו מחפשים למקם פרסומות קטנות וטעימות. אם תרצו לפרסם בארכיון של אנה, אנא הודיעו לנו. 6. אם אתם חוקרי אבטחה, נוכל להשתמש בכישורים שלכם הן להתקפה והן להגנה. בדקו את דף ה<a %(a_security)s>אבטחה</a> שלנו. 7. אנו מחפשים מומחים לתשלומים עבור סוחרים אנונימיים. האם תוכלו לעזור לנו להוסיף דרכים נוחות יותר לתרום? פייפאל, WeChat, כרטיסי מתנה. אם אתם מכירים מישהו, אנא צרו קשר. 8. אנו תמיד מחפשים עוד קיבולת שרתים. 9. אתם יכולים לעזור על ידי דיווח על בעיות בקבצים, השארת תגובות ויצירת רשימות ישירות באתר זה. אתם יכולים גם לעזור על ידי <a %(a_upload)s>העלאת ספרים נוספים</a>, או תיקון בעיות בקבצים או עיצוב של ספרים קיימים. למידע נרחב יותר על איך להתנדב, ראו את <a %(a_volunteering)s>דף ההתנדבות והפרסים</a> שלנו. אנו מאמינים מאוד בזרימה חופשית של מידע ובשימור הידע והתרבות. עם מנוע החיפוש הזה, אנו בונים על כתפי ענקים. אנו מכבדים עמוקות את העבודה הקשה של האנשים שיצרו את ספריות הצללים השונות, ואנו מקווים שמנוע החיפוש הזה ירחיב את טווח ההגעה שלהן. כדי להישאר מעודכנים בהתקדמות שלנו, עקבו אחרי אנה ב-<a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> או <a href="https://t.me/annasarchiveorg">Telegram</a>. לשאלות ומשוב אנא צרו קשר עם אנה ב-%(email)s. מזהה חשבון: %(account_id)s התנתקות ❌ משהו השתבש. אנא טענו מחדש את הדף ונסו שוב. ✅ אתם כעת מנותקים. טענו את הדף מחדש כדי להתחבר שוב. הורדות מהירות שנוצלו (ב-24 השעות האחרונות): <strong>%(used)s / %(total)s</strong> חברות: <strong>%(tier_name)s</strong> עד %(until_date)s <a %(a_extend)s>(הארכה)</a> ניתן לשלב מספר מנויים (ההורדות המהירות ל-24 שעות יתווספו יחד). מנוי: <strong>אין</strong> <a %(a_become)s>(הפוך למנוי)</a> צרו קשר עם אנה ב-%(email)s אם אתם מעוניינים לשדרג את המנוי שלכם לרמה גבוהה יותר. פרופיל ציבורי: %(profile_link)s קוד סודי (לא לשתף!): %(secret_key)s הצג הצטרפו אלינו כאן! שדרגו ל<a %(a_tier)s>רמה גבוהה יותר</a> כדי להצטרף לקבוצתנו. קבוצת Telegram בלעדית: %(link)s חשבון אילו הורדות? התחבר אל תאבד את המפתח שלך! מפתח סודי לא תקין. נא לאמת את המפתח ולנסות שוב, או לחלופין להירשם לחשבון חדש למטה. קוד סודי הזינו את הקוד הסודי שלכם כדי להתחבר: חשבון ישן מבוסס דוא"ל? הכניסו את <a %(a_open)s>הדוא"ל שלכם כאן</a>. רישום חשבון חדש אין לכם חשבון עדיין? ההרשמה הצליחה! הקוד הסודי שלך הוא: <span %(span_key)s>%(key)s</span> שמרו קוד זה בזהירות. אם תאבדו אותו, תאבדו גישה לחשבונכם. <li %(li_item)s><strong>סימניה.</strong> ניתן לסמן דף זה כדי לשחזר את הקוד שלכם.</li><li %(li_item)s><strong>הורדה.</strong> לחצו <a %(a_download)s>על קישור זה</a> כדי להוריד את הקוד שלכם.</li><li %(li_item)s><strong>מנהל סיסמאות.</strong> השתמשו במנהל סיסמאות כדי לשמור את הקוד כאשר תזינו אותו למטה.</li> התחברות / הרשמה אימות דפדפן אזהרה: הקוד מכיל תווים לא נכונים ב-Unicode, ועלול להתנהג בצורה לא נכונה במצבים שונים. ניתן לפענח את הבינארי הגולמי מהייצוג base64 ב-URL. תיאור תווית קידומת כתובת URL לקוד ספציפי אתר אינטרנט קודים שמתחילים ב- "%(prefix_label)s" אנא אל תגרדו דפים אלו. במקום זאת אנו ממליצים <a %(a_import)s>ליצור</a> או <a %(a_download)s>להוריד</a> את מסדי הנתונים שלנו של ElasticSearch ו-MariaDB, ולהריץ את <a %(a_software)s>הקוד הפתוח שלנו</a>. ניתן לחקור את הנתונים הגולמיים ידנית דרך קבצי JSON כמו <a %(a_json_file)s>זה</a>. פחות מ- %(count)s רשומות כתובת URL כללית חוקר הקודים אינדקס של חקור את הקודים שהרשומות מתויגות בהם, לפי קידומת. עמודת ה"רשומות" מציגה את מספר הרשומות המתויגות בקודים עם הקידומת הנתונה, כפי שנראה במנוע החיפוש (כולל רשומות מטה-דאטה בלבד). עמודת ה"קודים" מציגה כמה קודים בפועל יש להם קידומת נתונה. קידומת קוד ידועה “%(key)s” עוד… קידומת %(count)s רשומה תואמת “%(prefix_label)s” 2 רשומות תואמות “%(prefix_label)s” קודים רשומות הקוד "%%" יוחלף בערך של הקוד חיפוש בארכיון של אנה קודים כתובת URL לקוד ספציפי: "%(url)s" דף זה עשוי לקחת זמן ליצירה, ולכן הוא דורש captcha של Cloudflare. <a %(a_donate)s>חברים</a> יכולים לדלג על ה-captcha. התעללות דווחה: גרסה משופרת האם ברצונכם לדווח על משתמש זה על התנהגות פוגענית או בלתי הולמת? בעיה בקובץ: %(file_issue)s הערה מוסתרת השב דווח על התעללות דיווחתם על משתמש זה על התעללות. תביעות זכויות יוצרים לדוא"ל זה יזכו להתעלמות; השתמשו בטופס במקום. הצג דוא"ל נשמח מאוד לקבל את המשוב והשאלות שלכם! עם זאת, בשל כמות הספאם והודעות הדוא"ל הלא רלוונטיות שאנו מקבלים, אנא סמנו את התיבות כדי לאשר שאתם מבינים את התנאים ליצירת קשר איתנו. כל דרכי יצירת קשר אחרות בנוגע לטענות זכויות יוצרים יימחקו אוטומטית. לטענות DMCA / זכויות יוצרים, השתמש ב<a %(a_copyright)s>טופס זה</a>. דוא"ל ליצירת קשר כתובות URL בארכיון של אנה (נדרש). אחת בכל שורה. אנא כללו רק כתובות URL שמתארות את אותה מהדורה בדיוק של ספר. אם ברצונכם להגיש תביעה עבור מספר ספרים או מספר מהדורות, אנא הגישו את הטופס הזה מספר פעמים. תביעות שמאגדות מספר ספרים או מהדורות יחד יידחו. כתובת (נדרש) תיאור ברור של החומר המקורי (נדרש) אימייל (נדרש) כתובות URL לחומר המקורי, אחת בכל שורה (נדרש). אנא כללו כמה שיותר, כדי לעזור לנו לאמת את התביעה שלכם (לדוגמה: Amazon, WorldCat, Google Books, DOI). מספרי ISBN של החומר המקורי (אם רלוונטי). אחד בכל שורה. אנא כללו רק את אלו שתואמים בדיוק למהדורה שעליה אתם מדווחים תביעה על זכויות יוצרים. שמך (נדרש) ❌ משהו השתבש. אנא טענו מחדש את הדף ונסו שוב. ✅ תודה על הגשת תביעת זכויות היוצרים שלכם. נבדוק אותה בהקדם האפשרי. אנא טענו מחדש את הדף כדי להגיש תביעה נוספת. <a %(a_openlib)s>Open Library</a> כתובות URL של החומר המקורי, אחת בכל שורה. אנא הקדישו רגע לחיפוש החומר המקורי ב-Open Library. זה יעזור לנו לאמת את התביעה שלכם. מספר טלפון (נדרש) הצהרה וחתימה (נדרש) הגשת תביעה אם יש לכם תביעה על זכויות יוצרים או DMCA, אנא מלאו את הטופס הזה בצורה המדויקת ביותר האפשרית. אם נתקלתם בבעיות, אנא צרו איתנו קשר בכתובת ה-DMCA הייעודית שלנו: %(email)s. שימו לב שתביעות שנשלחות לכתובת זו לא יטופלו, היא מיועדת לשאלות בלבד. אנא השתמשו בטופס למטה כדי להגיש את התביעות שלכם. טופס תביעה על זכויות יוצרים / DMCA רשומה לדוגמה בארכיון אנה טורנטים על ידי ארכיון אנה פורמט מכולות הארכיון של אנה סקריפטים לייבוא מטה-דאטה אם אתם מעוניינים לשקף את מאגר הנתונים הזה לצורכי <a %(a_archival)s>ארכיון</a> או <a %(a_llm)s>אימון LLM</a>, אנא צרו איתנו קשר. עודכן לאחרונה: %(date)s האתר הראשי של %(source)s תיעוד מטה-דאטה (רוב השדות) קבצים משוקפים על ידי ארכיון אנה: %(count)s (%(percent)s%%) משאבים סך הקבצים: %(count)s סך גודל הקבצים: %(size)s הפוסט בבלוג שלנו על נתונים אלו <a %(duxiu_link)s>Duxiu</a> הוא מאגר עצום של ספרים סרוקים, שנוצר על ידי <a %(superstar_link)s>קבוצת הספרייה הדיגיטלית SuperStar</a>. רובם ספרים אקדמיים, שנסרקו כדי להפוך אותם לזמינים דיגיטלית לאוניברסיטאות וספריות. עבור הקהל דובר האנגלית שלנו, <a %(princeton_link)s>פרינסטון</a> ו<a %(uw_link)s>אוניברסיטת וושינגטון</a> מציעים סקירות טובות. יש גם מאמר מצוין שנותן רקע נוסף: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. הספרים מ-Duxiu כבר זמן רב מופצים באופן פיראטי באינטרנט הסיני. בדרך כלל הם נמכרים בפחות מדולר על ידי משווקים. הם מופצים בדרך כלל באמצעות המקבילה הסינית של Google Drive, שלעתים קרובות נפרצה כדי לאפשר יותר שטח אחסון. ניתן למצוא פרטים טכניים מסוימים <a %(link1)s>כאן</a> ו<a %(link2)s>כאן</a>. למרות שהספרים הופצו באופן חצי-ציבורי, קשה מאוד להשיג אותם בכמויות גדולות. זה היה גבוה ברשימת המטלות שלנו, והקצנו לכך מספר חודשים של עבודה במשרה מלאה. עם זאת, בסוף 2023 מתנדב מדהים, מדהים ומוכשר פנה אלינו, וסיפר לנו שהוא כבר עשה את כל העבודה הזו — בהוצאה גדולה. הוא שיתף איתנו את האוסף המלא, מבלי לצפות לדבר בתמורה, מלבד הבטחת שימור לטווח ארוך. באמת יוצא דופן. מידע נוסף מהמתנדבים שלנו (הערות גולמיות): מותאם מה<a %(a_href)s>פוסט בבלוג</a> שלנו. DuXiu 读秀 קובץ %(count)s 2 קבצים מאגר הנתונים הזה קשור באופן הדוק ל-<a %(a_datasets_openlib)s>מאגר הנתונים של Open Library</a>. הוא מכיל גירוד של כל המטה-דאטה וחלק גדול מהקבצים מספריית ההשאלה הדיגיטלית המבוקרת של IA. עדכונים משתחררים בפורמט <a %(a_aac)s>מיכלי ארכיון של אנה</a>. רשומות אלו מופנות ישירות ממאגר הנתונים של Open Library, אך גם מכילות רשומות שאינן ב-Open Library. יש לנו גם מספר קבצי נתונים שנגרדו על ידי חברי הקהילה לאורך השנים. האוסף מורכב משני חלקים. אתם צריכים את שני החלקים כדי לקבל את כל הנתונים (למעט טורנטים שהוחלפו, שמסומנים בקו חוצה בעמוד הטורנטים). ספרייה להשאלה דיגיטלית המהדורה הראשונה שלנו, לפני שהתקננו את <a %(a_aac)s>פורמט מכולות הארכיון של אנה (AAC)</a>. מכיל מטה-דאטה (בפורמטים json ו-xml), קבצי pdf (ממערכות השאלה דיגיטליות acsm ו-lcpdf), ותמונות ממוזערות של כריכות. מהדורות חדשות מצטברות, באמצעות AAC. מכיל רק מטה-דאטה עם חותמות זמן אחרי 2023-01-01, מכיוון שהשאר כבר מכוסה על ידי "ia". כמו כן, כל קבצי ה-pdf, הפעם ממערכות ההשאלה acsm ו-"bookreader" (קורא הרשת של IA). למרות שהשם לא מדויק, אנו עדיין מאכלסים קבצי bookreader באוסף ia2_acsmpdf_files, מכיוון שהם בלעדיים זה לזה. השאלת ספרים דיגיטלית מבוקרת של IA 98%%+ מהקבצים ניתנים לחיפוש. המשימה שלנו היא לארכב את כל הספרים בעולם (כמו גם מאמרים, מגזינים וכו'), ולהפוך אותם לנגישים באופן רחב. אנו מאמינים שכל הספרים צריכים להיות משוקפים באופן נרחב, כדי להבטיח יתירות ועמידות. זו הסיבה שאנו מאגדים קבצים ממגוון מקורות. חלק מהמקורות פתוחים לחלוטין וניתן לשקף אותם בכמות גדולה (כמו Sci-Hub). אחרים סגורים ומגוננים, ולכן אנו מנסים לגרד אותם כדי "לשחרר" את ספריהם. אחרים נמצאים איפשהו באמצע. כל הנתונים שלנו ניתנים ל-<a %(a_torrents)s>הורדה ב-torrent</a>, וכל המטה-דאטה שלנו ניתן ל-<a %(a_anna_software)s>יצירה</a> או <a %(a_elasticsearch)s>הורדה</a> כמאגרי ElasticSearch ו-MariaDB. הנתונים הגולמיים ניתנים לחקירה ידנית דרך קבצי JSON כמו <a %(a_dbrecord)s>זה</a>. מטה-דאטה אתר ISBN עודכן לאחרונה: %(isbn_country_date)s (%(link)s) משאבים הסוכנות הבינלאומית ל-ISBN משחררת באופן קבוע את הטווחים שהוקצו לסוכנויות ISBN לאומיות. מכך ניתן להסיק לאיזו מדינה, אזור או קבוצת שפה שייך ה-ISBN. אנו משתמשים בנתונים אלו באופן עקיף, דרך ספריית ה-Python <a %(a_isbnlib)s>isbnlib</a>. מידע על מדינות ISBN זהו דמפ של הרבה קריאות ל-isbndb.com במהלך ספטמבר 2022. ניסינו לכסות את כל טווחי ה-ISBN. מדובר בכ-30.9 מיליון רשומות. באתר שלהם הם טוענים שיש להם למעשה 32.6 מיליון רשומות, אז ייתכן שפספסנו כמה, או <em>שהם</em> עושים משהו לא נכון. התשובות ב-JSON הן כמעט גולמיות מהשרת שלהם. בעיית איכות נתונים אחת ששמנו לב אליה, היא שלמספרי ISBN-13 שמתחילים עם קידומת שונה מ-"978-", הם עדיין כוללים שדה "isbn" שהוא פשוט מספר ה-ISBN-13 עם שלושת הספרות הראשונות שנחתכו (והספרה הבודקת חושבה מחדש). זה כמובן שגוי, אבל כך הם עושים זאת, אז לא שינינו את זה. בעיה פוטנציאלית נוספת שאתם עשויים להיתקל בה, היא העובדה שלשדה "isbn13" יש כפילויות, כך שלא ניתן להשתמש בו כמפתח ראשי במסד נתונים. השדות "isbn13"+"isbn" יחד נראים ייחודיים. שחרור 1 (2022-10-31) טורנטים של ספרות יפה מאחור (למרות ש-IDs ~4-6M לא טורנטו מכיוון שהם חופפים עם הטורנטים שלנו מ-Zlib). הפוסט בבלוג שלנו על שחרור ספרי הקומיקס טורנטים של קומיקס בארכיון אנה לסיפור הרקע של הפיצולים השונים של Library Genesis, ראו את הדף עבור <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li מכיל את רוב התוכן והמטה-דאטה כמו Libgen.rs, אך יש לו כמה אוספים נוספים, כלומר קומיקס, מגזינים ומסמכים סטנדרטיים. הוא גם שילב את <a %(a_scihub)s>Sci-Hub</a> במטה-דאטה ובמנוע החיפוש שלו, וזה מה שאנו משתמשים בו עבור מסד הנתונים שלנו. המטה-דאטה עבור ספרייה זו זמין בחינם <a %(a_libgen_li)s>ב-libgen.li</a>. עם זאת, השרת הזה איטי ולא תומך בחידוש חיבורים שנשברו. אותם קבצים זמינים גם ב-<a %(a_ftp)s>שרת FTP</a>, שעובד טוב יותר. נראה שגם ספרות עיון התפצלה, אך ללא טורנטים חדשים. נראה שזה קרה מאז תחילת 2022, אם כי לא אימתנו זאת. לפי מנהל Libgen.li, אוסף "fiction_rus" (ספרות רוסית) אמור להיות מכוסה על ידי טורנטים שמשוחררים באופן קבוע מ-<a %(a_booktracker)s>booktracker.org</a>, במיוחד הטורנטים של <a %(a_flibusta)s>flibusta</a> ו-<a %(a_librusec)s>lib.rus.ec</a> (שאנחנו משקפים <a %(a_torrents)s>כאן</a>, אם כי עדיין לא קבענו אילו טורנטים תואמים לאילו קבצים). לאוסף הספרותי יש טורנטים משלו (שונים מ-<a %(a_href)s>Libgen.rs</a>) החל מ-%(start)s. טווחים מסוימים ללא טורנטים (כגון טווחי ספרות f_3463000 עד f_4260000) הם ככל הנראה קבצים של Z-Library (או כפילויות אחרות), אם כי ייתכן שנרצה לבצע דה-דופליקציה וליצור טורנטים לקבצים ייחודיים ל-lgli בטווחים אלו. סטטיסטיקות לכל האוספים ניתן למצוא <a %(a_href)s>באתר של libgen</a>. טורנטים זמינים עבור רוב התוכן הנוסף, במיוחד טורנטים עבור קומיקס, מגזינים ומסמכים סטנדרטיים שוחררו בשיתוף פעולה עם הארכיון של אנה. שימו לב שקבצי הטורנט המתייחסים ל-“libgen.is” הם במפורש מראות של <a %(a_libgen)s>Libgen.rs</a> (“.is” הוא דומיין שונה המשמש את Libgen.rs). משאב מועיל לשימוש במטה-דאטה הוא <a %(a_href)s>העמוד הזה</a>. %(icon)s לאוסף "fiction_rus" שלהם (ספרות רוסית) אין טורנטים ייעודיים, אך הוא מכוסה על ידי טורנטים מאחרים, ואנו שומרים <a %(fiction_rus)s>מראה</a>. טורנטים של ספרות רוסית בארכיון של אנה טורנטים של ספרות בדיונית בארכיון אנה פורום דיונים מטה-דאטה מטה-דאטה דרך FTP טורנטים של מגזינים בארכיון אנה מידע על שדות המטה-דאטה מראה של טורנטים אחרים (וטורנטים ייחודיים של ספרות בדיונית וקומיקס) טורנטים של מסמכים סטנדרטיים בארכיון של אנה Libgen.li טורנטים של "ארכיון של אנה" (עטיפות ספרים) Library Genesis ידוע בכך שהוא כבר עושה את הנתונים שלו זמינים בנדיבות בכמויות גדולות דרך טורנטים. האוסף שלנו מ-Libgen מורכב מנתונים עזריים שהם לא משחררים ישירות, בשיתוף פעולה איתם. תודה רבה לכל המעורבים ב-Library Genesis על העבודה המשותפת איתנו! הבלוג שלנו על שחרור עטיפות הספרים העמוד הזה עוסק בגרסה “.rs”. היא ידועה בפרסום עקבי של המטה-דאטה שלה ושל התוכן המלא של קטלוג הספרים שלה. אוסף הספרים שלה מחולק בין חלק של ספרות בדיונית וחלק של ספרות לא בדיונית. משאב מועיל לשימוש במטה-דאטה הוא <a %(a_metadata)s>העמוד הזה</a> (חוסם טווחי IP, ייתכן שיידרש VPN). נכון ל-2024-03, טורנטים חדשים מתפרסמים ב-<a %(a_href)s>אשכול הפורום הזה</a> (חוסם טווחי IP, ייתכן שתצטרכו VPN). טורנטים של ספרות יפה ב"ארכיון של אנה" טורנטים של ספרות יפה ב-Libgen.rs פורום הדיונים של Libgen.rs מטה-דאטה של Libgen.rs מידע על שדות המטה-דאטה של Libgen.rs טורנטים של ספרי עיון ב-Libgen.rs טורנטים של ספרי עיון ב"ארכיון של אנה" %(example)s עבור ספרות יפה. השחרור <a %(blog_post)s>הראשון הזה</a> די קטן: כ-300GB של עטיפות ספרים מהפיצול של Libgen.rs, גם ספרות יפה וגם ספרי עיון. הם מאורגנים באותו אופן שבו הם מופיעים ב-libgen.rs, לדוגמה: %(example)s עבור ספר עיון. בדיוק כמו באוסף של Z-Library, שמנו את כולם בקובץ .tar גדול, שניתן להרכיב באמצעות <a %(a_ratarmount)s>ratarmount</a> אם תרצו לשרת את הקבצים ישירות. שחרור 1 (%(date)s) הסיפור הקצר של הפיצולים השונים של Library Genesis (או “Libgen”) הוא שבמשך הזמן, האנשים השונים שהיו מעורבים ב-Library Genesis הסתכסכו והלכו כל אחד לדרכו. לפי <a %(a_mhut)s>הפוסט בפורום</a> הזה, Libgen.li היה במקור ב-“http://free-books.dontexist.com”. הגרסה “.fun” נוצרה על ידי המייסד המקורי. היא עוברת שדרוג לטובת גרסה חדשה ומבוזרת יותר. ל<a %(a_li)s>גרסה “.li”</a> יש אוסף עצום של קומיקס, כמו גם תוכן אחר, שאינו (עדיין) זמין להורדה בכמויות דרך טורנטים. יש לה אוסף טורנטים נפרד של ספרות בדיונית, והיא מכילה את המטה-דאטה של <a %(a_scihub)s>Sci-Hub</a> במסד הנתונים שלה. לגרסה “.rs” יש נתונים דומים מאוד, והיא משחררת את האוסף שלה בטורנטים גדולים באופן עקבי. היא מחולקת בערך לחלק של “ספרות בדיונית” וחלק של “ספרות לא בדיונית”. במקור ב-“http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> במובן מסוים הוא גם פיצול של Library Genesis, אם כי הם השתמשו בשם שונה לפרויקט שלהם. Libgen.rs אנו גם מעשירים את האוסף שלנו עם מקורות מטה-דאטה בלבד, אותם אנו יכולים להתאים לקבצים, למשל באמצעות מספרי ISBN או שדות אחרים. להלן סקירה של אלו. שוב, חלק מהמקורות הללו פתוחים לחלוטין, בעוד שאחרים אנו צריכים לגרד אותם. שימו לב שבחיפוש מטה-דאטה, אנו מציגים את הרשומות המקוריות. אנו לא מבצעים מיזוג של רשומות. מקורות מטה-דאטה בלבד Open Library הוא פרויקט קוד פתוח של Internet Archive שמטרתו לקטלג כל ספר בעולם. יש לו אחת מהפעולות הגדולות ביותר בעולם לסריקת ספרים, ויש לו הרבה ספרים זמינים להשאלה דיגיטלית. קטלוג המטה-דאטה של הספרים שלו זמין להורדה בחינם, והוא כלול ב"ארכיון של אנה" (אם כי כרגע לא בחיפוש, אלא אם תחפשו במפורש לפי מזהה Open Library). Open Library למעט כפילויות עודכן לאחרונה אחוזים של מספר הקבצים %% משוכפל על ידי AA / טורנטים זמינים גודל מקור להלן סקירה מהירה של מקורות הקבצים בארכיון של אנה. מכיוון שספריות הצללים מסנכרנות לעיתים קרובות נתונים זו מזו, יש חפיפה משמעותית בין הספריות. זו הסיבה שהמספרים אינם מסתכמים לסך הכל. אחוז "משוכפל ומופץ על ידי ארכיון של אנה" מראה כמה קבצים אנו משכפלים בעצמנו. אנו מפיצים את הקבצים בכמויות גדולות דרך טורנטים, ומאפשרים הורדה ישירה דרך אתרי שותפים. סקירה כללית סה"כ טורנטים בארכיון של אנה למידע נוסף על Sci-Hub, אנא עיין ב<a %(a_scihub)s>אתר הרשמי</a>, <a %(a_wikipedia)s>עמוד הוויקיפדיה</a>, וב<a %(a_radiolab)s>ראיון הפודקאסט</a> הזה. שימו לב ש-Sci-Hub הוקפא מאז <a %(a_reddit)s>2021</a>. הוא הוקפא בעבר, אך ב-2021 נוספו כמה מיליוני מאמרים. עדיין, מספר מוגבל של מאמרים מתווספים לאוספי "scimag" של Libgen, אך לא מספיק כדי להצדיק טורנטים בתפוצה רחבה חדשה. אנו משתמשים במטה-דאטה של Sci-Hub כפי שסופק על ידי <a %(a_libgen_li)s>Libgen.li</a> באוסף "scimag" שלו. אנו גם משתמשים במאגר הנתונים <a %(a_dois)s>dois-2022-02-12.7z</a>. שימו לב שהטורנטים של "smarch" הם <a %(a_smarch)s>מיושנים</a> ולכן אינם כלולים ברשימת הטורנטים שלנו. טורנטים ב-Libgen.li טורנטים ב-Libgen.rs מטה-דאטה וטורנטים עדכונים ב-Reddit ראיון פודקאסט עמוד הוויקיפדיה Sci-Hub Sci-Hub: קפוא מאז 2021; רובו זמין דרך טורנטים Libgen.li: תוספות קטנות מאז</div> חלק מהספריות המקוריות מקדמות את השיתוף ההמוני של הנתונים שלהן דרך טורנטים, בעוד שאחרות אינן משתפות את האוסף שלהן בקלות. במקרה האחרון, הארכיון של אנה מנסה לגרד את האוספים שלהן ולהפוך אותם לזמינים (ראו את עמוד ה-<a %(a_torrents)s>טורנטים</a> שלנו). ישנם גם מצבים ביניים, למשל, כאשר הספריות המקוריות מוכנות לשתף, אך אין להן את המשאבים לעשות זאת. במקרים אלו, אנו גם מנסים לעזור. להלן סקירה של איך אנו מתקשרים עם הספריות המקוריות השונות. ספריות מקור %(icon)s מאגרי קבצים שונים מפוזרים ברחבי האינטרנט הסיני; לעיתים קרובות מאגרי מידע בתשלום %(icon)s רוב הקבצים נגישים רק באמצעות חשבונות BaiduYun פרימיום; מהירויות הורדה איטיות. %(icon)s הארכיון של אנה מנהל אוסף של <a %(duxiu)s>קבצי DuXiu</a> %(icon)s מאגרי מטה-דאטה שונים מפוזרים ברחבי האינטרנט הסיני; אם כי לעיתים קרובות מאגרים בתשלום %(icon)s אין מטה-דאטה זמין להורדה בקלות עבור כל האוסף שלהם. %(icon)s הארכיון של אנה מנהל אוסף של <a %(duxiu)s>מטה-דאטה של DuXiu</a> קבצים %(icon)s קבצים זמינים להשאלה על בסיס מוגבל, עם מגבלות גישה שונות %(icon)s הארכיון של אנה מנהל אוסף של <a %(ia)s>קבצי IA</a> %(icon)s מטה-דאטה מסוימת זמינה דרך <a %(openlib)s>השלכות מסד הנתונים של Open Library</a>, אך אלו לא מכסות את כל אוסף ה-IA %(icon)s אין השלכות מטה-דאטה נגישות בקלות זמינות לכל האוסף שלהם %(icon)s הארכיון של אנה מנהל אוסף של <a %(ia)s>מטה-דאטה של IA</a> עודכן לאחרונה %(icon)s הארכיון של אנה ו-Libgen.li מנהלים במשותף אוספים של <a %(comics)s>ספרי קומיקס</a>, <a %(magazines)s>מגזינים</a>, <a %(standarts)s>מסמכים סטנדרטיים</a>, ו<a %(fiction)s>ספרות בדיונית (שונה מ-Libgen.rs)</a>. %(icon)s טורנטים של ספרות לא-בדיונית משותפים עם Libgen.rs (ומשוכפלים <a %(libgenli)s>כאן</a>). %(icon)s <a %(dbdumps)s>השלכות מסד נתונים HTTP</a> רבעוניות %(icon)s טורנטים אוטומטיים עבור <a %(nonfiction)s>עיון</a> ו<a %(fiction)s>ספרות</a> %(icon)s הארכיון של אנה מנהל אוסף של <a %(covers)s>טורנטים של כריכות ספרים</a> %(icon)s <a %(dbdumps)s>השלכות מסד נתונים HTTP</a> יומיות מטה-דאטה %(icon)s <a %(dbdumps)s>מאגרי מידע חודשיים</a> %(icon)s טורנטים של נתונים זמינים <a %(scihub1)s>כאן</a>, <a %(scihub2)s>כאן</a>, ו<a %(libgenli)s>כאן</a> %(icon)s קבצים חדשים מסוימים <a %(libgenrs)s>מוספים</a> ל"scimag" של Libgen, אך לא מספיק כדי להצדיק טורנטים חדשים %(icon)s Sci-Hub הקפיא קבצים חדשים מאז 2021. %(icon)s מטה-דאטה זמינה להורדה <a %(scihub1)s>כאן</a> ו<a %(scihub2)s>כאן</a>, וגם כחלק מ<a %(libgenli)s>מסד הנתונים של Libgen.li</a> (שאנחנו משתמשים בו) מקור %(icon)s מקורות קטנים או חד-פעמיים שונים. אנו מעודדים אנשים להעלות קודם כל לספריות צללים אחרות, אך לפעמים יש לאנשים אוספים גדולים מדי מכדי שאחרים יוכלו למיין, אך לא מספיק גדולים כדי להצדיק קטגוריה משלהם. %(icon)s לא זמין ישירות בכמות גדולה, מוגן מפני גרידה %(icon)s הארכיון של אנה מנהל אוסף של <a %(worldcat)s>מטה-דאטה של OCLC (WorldCat)</a> %(icon)s הארכיון של אנה ו-Z-Library מנהלים במשותף אוסף של <a %(metadata)s>מטה-דאטה של Z-Library</a> ו<a %(files)s>קבצים של Z-Library</a> Datasets אנו משלבים את כל המקורות הנ"ל למאגר נתונים מאוחד שאנו משתמשים בו כדי לשרת את האתר הזה. מאגר הנתונים המאוחד הזה אינו זמין ישירות, אך מכיוון שהארכיון של אנה הוא קוד פתוח לחלוטין, ניתן <a %(a_generated)s>ליצור</a> או <a %(a_downloaded)s>להוריד</a> אותו בקלות יחסית כמאגרי ElasticSearch ו-MariaDB. הסקריפטים בעמוד זה יורידו אוטומטית את כל המטה-דאטה הנדרש מהמקורות שהוזכרו לעיל. אם תרצו לחקור את הנתונים שלנו לפני הרצת הסקריפטים הללו באופן מקומי, תוכלו להסתכל על קבצי ה-JSON שלנו, המקשרים הלאה לקבצי JSON אחרים. <a %(a_json)s>קובץ זה</a> הוא נקודת התחלה טובה. מאגר נתונים מאוחד טורנטים על ידי ארכיון אנה עיון חיפוש מקורות קטנים או חד-פעמיים שונים. אנו מעודדים אנשים להעלות קודם כל לספריות צללים אחרות, אך לפעמים יש לאנשים אוספים גדולים מדי מכדי שאחרים יוכלו למיין, אך לא מספיק גדולים כדי להצדיק קטגוריה משלהם. סקירה מתוך <a %(a1)s>דף ה-Datasets</a>. מ<a %(a_href)s>aaaaarg.fail</a>. נראה די שלם. מהמתנדב שלנו "cgiym". מ<a %(a_href)s><q>ACM Digital Library 2020</q></a> טורנט. יש חפיפה גבוהה יחסית עם אוספי מאמרים קיימים, אך מעט מאוד התאמות MD5, ולכן החלטנו לשמור אותו במלואו. סריקה של <q>iRead eBooks</q> (פונטית <q>ai rit i-books</q>; airitibooks.com), על ידי מתנדב <q>j</q>. תואם ל-metadata של <q>airitibooks</q> ב-<a %(a1)s><q>סריקות metadata אחרות</q></a>. מתוך אוסף <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. חלקית מהמקור המקורי, חלקית מה-the-eye.eu, חלקית ממראות אחרות. מאתר טורנטים פרטי לספרים, <a %(a_href)s>Bibliotik</a> (לעיתים קרובות מכונה "Bib"), אשר ספריו נארזו לטורנטים לפי שם (A.torrent, B.torrent) והופצו דרך the-eye.eu. מהמתנדב שלנו "bpb9v". למידע נוסף על <a %(a_href)s>CADAL</a>, ראו את ההערות בדף <a %(a_duxiu)s>מאגר הנתונים של DuXiu</a>. עוד מהמתנדב שלנו "bpb9v", בעיקר קבצי DuXiu, וכן תיקיות "WenQu" ו-"SuperStar_Journals" (SuperStar היא החברה מאחורי DuXiu). מהמתנדב שלנו "cgiym", טקסטים סיניים ממקורות שונים (מוצגים כתיקיות משנה), כולל מ-<a %(a_href)s>China Machine Press</a> (מוציא לאור סיני גדול). אוספים לא סיניים (מוצגים כתיקיות משנה) מהמתנדב שלנו "cgiym". סריקה של ספרים על אדריכלות סינית, על ידי מתנדב <q>cm</q>: <q>השגתי את זה על ידי ניצול פגיעות רשת בבית ההוצאה לאור, אבל הפרצה הזו נסגרה מאז</q>. תואם ל-metadata של <q>chinese_architecture</q> ב-<a %(a1)s><q>סריקות metadata אחרות</q></a>. ספרים מהוצאת הספרים האקדמית <a %(a_href)s>De Gruyter</a>, שנאספו מכמה טורנטים גדולים. גרידה של <a %(a_href)s>docer.pl</a>, אתר שיתוף קבצים פולני המתמקד בספרים ועבודות כתובות אחרות. נגרד בסוף 2023 על ידי המתנדב "p". אין לנו מטה-דאטה טוב מהאתר המקורי (אפילו לא סיומות קבצים), אבל סיננו קבצים שנראים כמו ספרים ולעיתים קרובות הצלחנו לחלץ מטה-דאטה מהקבצים עצמם. קבצי epub של DuXiu, ישירות מ-DuXiu, שנאספו על ידי המתנדב "w". רק ספרי DuXiu עדכניים זמינים ישירות דרך ספרים אלקטרוניים, כך שרובם חייבים להיות עדכניים. קבצי DuXiu הנותרים מהמתנדב "m", שלא היו בפורמט PDG הקנייני של DuXiu (המאגר הראשי של <a %(a_href)s>DuXiu</a>). נאספו ממקורות רבים, למרבה הצער מבלי לשמר את המקורות הללו בנתיב הקובץ. <span></span> <span></span> <span></span> סריקה של ספרים אירוטיים, על ידי מתנדב <q>do no harm</q>. תואם ל-metadata של <q>hentai</q> ב-<a %(a1)s><q>סריקות metadata אחרות</q></a>. <span></span> <span></span> אוסף שנגרד ממוציא לאור יפני של מנגה על ידי המתנדב "t". <a %(a_href)s>ארכיונים משפטיים נבחרים של לונגצ'ואן</a>, שסופקו על ידי המתנדב "c". גרידה של <a %(a_href)s>magzdb.org</a>, בן ברית של Library Genesis (מקושר בדף הבית של libgen.rs) אך שלא רצה לספק את הקבצים שלו ישירות. התקבל על ידי המתנדב "p" בסוף 2023. <span></span> העלאות קטנות שונות, קטנות מדי כתת-אוסף משלהן, אך מוצגות כתיקיות. ספרים אלקטרוניים מ-AvaxHome, אתר שיתוף קבצים רוסי. ארכיון של עיתונים ומגזינים. תואם ל-metadata של <q>newsarch_magz</q> ב-<a %(a1)s><q>סריקות metadata אחרות</q></a>. סריקה של <a %(a1)s>מרכז התיעוד לפילוסופיה</a>. אוסף של המתנדב "o" שאסף ספרים פולניים ישירות מאתרי שחרור מקוריים ("scene"). אוספים משולבים של <a %(a_href)s>shuge.org</a> על ידי המתנדבים "cgiym" ו-"woz9ts". <span></span> <a %(a_href)s>“הספרייה הקיסרית של טרנטור”</a> (נקראת על שם הספרייה הבדיונית), נגרדה ב-2022 על ידי המתנדב "t". <span></span> <span></span> <span></span> תת-ת-אוספים (מוצגים כתיקיות) מהמתנדב "woz9ts": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (על ידי <a %(a_sikuquanshu)s>Dizhi(迪志)</a> בטייוואן), mebook (mebook.cc, 我的小书屋, חדר הספרים הקטן שלי — woz9ts: "האתר הזה מתמקד בעיקר בשיתוף קבצי ספרים אלקטרוניים באיכות גבוהה, חלקם מעוצבים על ידי הבעלים עצמו. הבעלים <a %(a_arrested)s>נעצר</a> ב-2019 ומישהו יצר אוסף של הקבצים שהוא שיתף."). קבצי DuXiu הנותרים מהמתנדב "woz9ts", שלא היו בפורמט PDG הקנייני של DuXiu (עדיין יש להמיר ל-PDF). אוסף ה"העלאות" מחולק לתת-אוספים קטנים יותר, המצוינים ב-AACIDs ובשמות הטורנטים. כל תת-האוספים נבדקו תחילה מול האוסף הראשי, אם כי קבצי ה-JSON של "upload_records" עדיין מכילים הרבה הפניות לקבצים המקוריים. קבצים שאינם ספרים הוסרו גם הם מרוב תת-האוספים, ובדרך כלל <em>לא</em> מצוינים ב-"upload_records" JSON. תת-האוספים הם: הערות תת-אוסף רבים מתת-האוספים עצמם מורכבים מתת-תת-אוספים (למשל ממקורות מקוריים שונים), המיוצגים כספריות בשדות ה-"filepath". העלאות לארכיון של אנה פוסט הבלוג שלנו על נתונים אלו <a %(a_worldcat)s>WorldCat</a> הוא מאגר נתונים קנייני של הארגון ללא מטרות רווח <a %(a_oclc)s>OCLC</a>, שמאגד רשומות מטה-דאטה מספריות מכל רחבי העולם. סביר להניח שזהו אוסף המטה-דאטה הספרייתי הגדול ביותר בעולם. באוקטובר 2023 <a %(a_scrape)s>שחררנו</a> גרסה מקיפה של מאגר הנתונים OCLC (WorldCat), בפורמט <a %(a_aac)s>מיכלי ארכיון של אנה</a>. אוקטובר 2023, שחרור ראשוני: OCLC (WorldCat) טורנטים בארכיון של אנה רשומה לדוגמה בארכיון אנה (אוסף מקורי) רשומה לדוגמה בארכיון אנה (אוסף "zlib3") טורנטים על ידי ארכיון אנה (מטה-דאטה + תוכן) פוסט בבלוג על שחרור 1 פוסט בבלוג על שחרור 2 בסוף 2022, נעצרו המייסדים המיוחסים של Z-Library, והדומיינים נתפסו על ידי רשויות ארצות הברית. מאז האתר חוזר לאט לאט לפעול. לא ידוע מי מנהל אותו כיום. עדכון נכון לפברואר 2023. ל-Z-Library יש שורשים בקהילת <a %(a_href)s>Library Genesis</a>, והיא התחילה במקור עם הנתונים שלהם. מאז, היא התמקצעה באופן משמעותי ויש לה ממשק מודרני הרבה יותר. לכן הם מסוגלים לקבל הרבה יותר תרומות, הן כספיות לשיפור האתר והן תרומות של ספרים חדשים. הם צברו אוסף גדול בנוסף ל-Library Genesis. האוסף מורכב משלושה חלקים. דפי התיאור המקוריים לשני החלקים הראשונים נשמרים למטה. יש צורך בכל שלושת החלקים כדי לקבל את כל הנתונים (למעט טורנטים שהוחלפו, שמסומנים בקו חוצה בדף הטורנטים). %(title)s: השחרור הראשון שלנו. זה היה השחרור הראשון של מה שנקרא אז "מראת הספרייה הפיראטית" ("pilimi"). %(title)s: שחרור שני, הפעם עם כל הקבצים עטופים בקבצי .tar. %(title)s: שחרורים חדשים מצטברים, באמצעות <a %(a_href)s>פורמט מיכלי ארכיון אנה (AAC)</a>, כעת משוחררים בשיתוף פעולה עם צוות Z-Library. המראה הראשונית הושגה בעמל רב במהלך 2021 ו-2022. בשלב זה היא מעט מיושנת: היא משקפת את מצב האוסף ביוני 2021. נעדכן זאת בעתיד. כרגע אנו מתמקדים בהוצאת השחרור הראשון הזה. מאחר ש-Library Genesis כבר נשמר עם טורנטים ציבוריים, והוא כלול ב-Z-Library, ביצענו דה-דופליקציה בסיסית מול Library Genesis ביוני 2022. לשם כך השתמשנו ב-MD5 hashes. סביר להניח שיש הרבה יותר תוכן כפול בספרייה, כמו פורמטים שונים של אותו הספר. זה קשה לזיהוי מדויק, ולכן איננו עושים זאת. לאחר הדה-דופליקציה נותרנו עם יותר מ-2 מיליון קבצים, בסך הכל קצת פחות מ-7TB. האוסף מורכב משני חלקים: dump של MySQL “.sql.gz” של המטה-דאטה, ו-72 קבצי טורנט של כ-50-100GB כל אחד. המטה-דאטה מכיל את הנתונים כפי שדווחו על ידי אתר Z-Library (כותרת, מחבר, תיאור, סוג קובץ), וכן את גודל הקובץ וה-md5sum שזיהינו, מכיוון שלפעמים אלו אינם תואמים. נראה שיש טווחים של קבצים של-Z-Library עצמו יש מטה-דאטה שגוי. ייתכן שגם הורדנו קבצים שגויים במקרים מבודדים, שננסה לזהות ולתקן בעתיד. קבצי הטורנט הגדולים מכילים את נתוני הספרים בפועל, עם מזהה ה-Z-Library כשם הקובץ. ניתן לשחזר את סיומות הקבצים באמצעות ה-dump של המטה-דאטה. האוסף הוא תערובת של תוכן עיוני ובדיוני (לא מופרד כמו ב-Library Genesis). האיכות גם היא משתנה במידה רבה. המהדורה הראשונה זמינה כעת במלואה. שימו לב שקבצי הטורנט זמינים רק דרך המראה שלנו ב-Tor. שחרור 1 (%(date)s) זהו קובץ טורנט נוסף יחיד. הוא אינו מכיל מידע חדש, אך יש בו נתונים שיכולים לקחת זמן רב לחישוב. זה הופך אותו לנוח להחזיק, מכיוון שהורדת הטורנט הזה היא לעיתים קרובות מהירה יותר מאשר לחשב אותו מאפס. במיוחד, הוא מכיל אינדקסים של SQLite עבור קבצי ה-tar, לשימוש עם <a %(a_href)s>ratarmount</a>. מהדורה 2 נספח (%(date)s) קיבלנו את כל הספרים שנוספו ל-Z-Library בין המראה האחרון שלנו לאוגוסט 2022. חזרנו גם ואספנו כמה ספרים שפספסנו בפעם הראשונה. בסך הכל, האוסף החדש הזה הוא כ-24TB. שוב, האוסף הזה עבר דה-דופליקציה מול Library Genesis, מכיוון שכבר יש טורנטים זמינים לאוסף הזה. הנתונים מאורגנים בדומה למהדורה הראשונה. יש dump של MySQL “.sql.gz” של המטה-דאטה, הכולל גם את כל המטה-דאטה מהמהדורה הראשונה, ובכך מחליף אותה. הוספנו גם כמה עמודות חדשות: הזכרנו זאת בפעם הקודמת, אך רק כדי להבהיר: “filename” ו-“md5” הם התכונות האמיתיות של הקובץ, בעוד “filename_reported” ו-“md5_reported” הם מה שגרדנו מ-Z-Library. לפעמים השניים אינם תואמים זה לזה, ולכן כללנו את שניהם. למהדורה זו, שינינו את הקולציה ל-“utf8mb4_unicode_ci”, שאמורה להיות תואמת לגרסאות ישנות יותר של MySQL. קבצי הנתונים דומים לפעם הקודמת, אם כי הם הרבה יותר גדולים. פשוט לא היה לנו כוח ליצור המון קבצי טורנט קטנים יותר. “pilimi-zlib2-0-14679999-extra.torrent” מכיל את כל הקבצים שפספסנו במהדורה הקודמת, בעוד שאר הטורנטים הם טווחי מזהים חדשים.  <strong>עדכון %(date)s:</strong> יצרנו את רוב הטורנטים שלנו גדולים מדי, מה שגרם ללקוחות הטורנט להתקשות. הסרנו אותם ושחררנו טורנטים חדשים. <strong>עדכון %(date)s:</strong> עדיין היו יותר מדי קבצים, אז עטפנו אותם בקבצי tar ושחררנו טורנטים חדשים שוב. %(key)s: האם קובץ זה כבר נמצא ב-Library Genesis, באוסף העיוני או הבדיוני (מותאם לפי md5). %(key)s: באיזה טורנט נמצא הקובץ הזה. %(key)s: מוגדר כאשר לא הצלחנו להוריד את הספר. מהדורה 2 (%(date)s) שחרורי Zlib (דפי תיאור מקוריים) דומיין Tor אתר ראשי גרידת Z-Library אוסף ה”סיני” ב-Z-Library נראה זהה לאוסף שלנו מ-DuXiu, אך עם MD5s שונים. אנו מוציאים קבצים אלו מהטורנטים כדי למנוע כפילויות, אך עדיין מציגים אותם באינדקס החיפוש שלנו. מטה-דאטה אתה מקבל %(percentage)s%% הורדות מהירות בונוס, כי הופנית על ידי המשתמש %(profile_link)s. זה חל על כל התקופה בה מנויים. תרומה הצטרף נבחר עד %(percentage)s%% הנחה Alipay תומך בכרטיסי אשראי/חיוב בינלאומיים. ראו <a %(a_alipay)s>מדריך זה</a> למידע נוסף. שלח לנו כרטיסי מתנה של Amazon.com באמצעות כרטיס האשראי/חיוב שלך. ניתן לקנות קריפטו באמצעות כרטיסי אשראי/חיוב. WeChat (Weixin Pay) תומך בכרטיסי אשראי/חיוב בינלאומיים. באפליקציית WeChat, עבור ל-"Me => Services => Wallet => Add a Card". אם אינך רואה זאת, הפעל זאת באמצעות "Me => Settings => General => Tools => Weixin Pay => Enable". (השתמשו כאשר שולחים Ethereum מ-Coinbase) הועתק! העתק (הסכום המינימלי הנמוך ביותר) (אזהרה: סכום מינימום גבוה) -%(percentage)s%% שנים עשר חודשים חודש עשרים וארבע חודשים שלושה חודשים 48 חודשים ששה חודשים 96 חודשים בחר מה משך הזמן אליותרצה להירשם. <div %(div_monthly_cost)s></div><div %(div_after)s>אחרי <span %(span_discount)s></span> הנחה</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% עבור שנים עשר חדשים עבור חודש עבור עשרים וארבעה חדשים עבור שלשה חדשים למשך 48 חודשים עבור ששה חדשים למשך 96 חודשים %(monthly_cost)s / חודש צרו קשר שרתים ישירים <strong>SFTP</strong> תרומה ברמת ארגון או החלפה עבור אוספים חדשים (למשל סריקות חדשות, מאגרי נתונים עם OCR). גישה למומחים <strong>גישה בלתי מוגבלת</strong> במהירות גבוהה <div %(div_question)s>האם ניתן לשדרג את החברות שלי או לקבל מספר חברות?</div> <div %(div_question)s>האם אני יכול לתרום מבלי להיות חבר?</div> בהחלט. אנו מקבלים תרומות בכל סכום בכתובת Monero (XMR) זו: %(address)s. <div %(div_question)s>מה המשמעות של הטווחים לחודש?</div> ניתן להגיע לצד הנמוך של טווח על ידי יישום כל ההנחות, כמו בחירת תקופה ארוכה מחודש. <div %(div_question)s>האם החברות מתחדשת אוטומטית? ?</div> חברות <strong>לא</strong> מתחדשת אוטומטית. תוכלו להצטרף לזמן קצר או ארוך ככל שתרצו. <div %(div_question)s>על מה אתם מוציאים תרומות?</div> 100%% הולך לשימור והנגשת הידע והתרבות העולמית. כרגע אנו מוציאים את רוב הכסף על שרתים, אחסון ורוחב פס. אף כסף לא הולך לחברי הצוות באופן אישי. <div %(div_question)s>האם אני יכול לתרום סכום גדול?</div> זה יהיה מדהים! לתרומות מעל כמה אלפי דולרים, אנא צרו קשר ישירות ב-%(email)s. <div %(div_question)s>האם יש לכם שיטות תשלום נוספות?</div> כרגע לא. הרבה אנשים לא רוצים שארכיונים כאלה יתקיימו, ולכן עלינו להיות זהירים. אם תוכלו לעזור לנו להקים שיטות תשלום נוספות (ונוחות יותר) בצורה בטוחה, אנא צרו קשר ב-%(email)s. שאלות ותשובות נפוצות בנושא תרומה ישנה תרומה על שמך בסך <a %(a_donation)s> </a> בתהליך. אנא סיים או בטל את תהליך התרומה לפני שתתרום שוב. <a %(a_all_donations)s>הצג את התרומות שלי</a> לתרומה מעל $5000 אנא צור קשר בכתובת: %(email)s. אנו מקבלים תרומות גדולות מאנשים עשירים או מוסדות.  שימו לב כי למרות שהחברויות בעמוד זה הן "לחודש", הן תרומות חד-פעמיות (לא חוזרות). ראו את <a %(faq)s>שאלות ותשובות נפוצות בנושא תרומה</a>. הארכיון של אנה הוא ארגון ללא מטרות רווח, מיזם קוד פתוח ומידע חופשי. בתרומה ובחבירה לארגון אתם תומכים באופרציה ובפיתוחה. לכל חברינו: תודה שאתם שומרים עלינו פתוחים!❤️ למידע נוסף, עיינו ב-<a %(a_donate)s>שאלות ותשובות נפוצות בנושא תרומה</a>. כדי להפוך לחבר, אנא <a %(a_login)s>הכנס או התחבר</a>. תודה על תמיכתך! $%(cost)s / חודש אם עשיתם טעות במהלך התשלום, לא נוכל להחזיר את הכסף, אבל ננסה לתקן את המצב. חפש את הדף "קריפטו" באפליקציית או אתר פייפאל. הוא בדרך כלל נמצא תחת “Finances”. גשו אל העמוד "ביטקוין" באתר או אפליקציית פייפאל. לחצו על כפתור "העבר"%(transfer_icon)s, ואז לחצו "שלח". Alipay Alipay 支付宝 / WeChat 微信 אמאזון גיפט קארד כרטיס מתנה %(amazon)s כרטיס אשראי כרטיס בנק (באמצעות אפליקציה) Binance אשראי/חיוב/Apple/Google (BMC) Cash App כרטיס אשראי/חיוב מיידי כרטיס אשראי/חיוב מיידי 2 כרטיס אשראי/חיוב (גיבוי) קריפטו %(bitcoin_icon)s אשראי /PayPal/ Venmo פייפאל (ארה"ב) %(bitcoin_icon)s פייפאל PayPal (רגיל) פיקס (ברזיל) Revolut (לא זמין זמנית) WeChat בחר את מטבע הקריפטו המועדף עליך: תרום באמצעות אמזון גיפט קארד. <strong>חשוב:</strong> אפשרות זו היא עבור %(amazon)s. אם ברצונך להשתמש באתר אמזון אחר, בחר אותו למעלה. <strong>חשוב:</strong> אנו תומכים רק ב- Amazon.com, ולא באתרי Amazom אחרים. לדוגמא, de, .co.uk, .il .ca, אינם נתמכים. אנא אל תכתבו הודעה משלכם. הזן את הסכום המדויק: %(amount)s שימו לב שעלינו לעגל לסכומים שמתקבלים על ידי המפיצים שלנו (מינימום %(minimum)s). תרמו באמצעות כרטיס אשראי/דביט, דרך אפליקציית Alipay (קל מאוד להגדיר). התקינו את אפליקציית Alipay מ-<a %(a_app_store)s>חנות האפליקציות של אפל</a> או <a %(a_play_store)s>חנות Google Play</a>. הירשמו באמצעות מספר הטלפון שלכם. אין צורך בפרטים אישיים נוספים. <span %(style)s>1</span>התקינו את אפליקציית Alipay נתמך: Visa, MasterCard, JCB, Diners Club ו-Discover. ראו <a %(a_alipay)s>מדריך זה</a> למידע נוסף. <span %(style)s>2</span>הוסיפו כרטיס אשראי עם Binance, אתם קונים ביטקוין עם כרטיס אשראי/חיוב או חשבון בנק, ואז תורמים את הביטקוין לנו. כך אנו יכולים להישאר מאובטחים ואנונימיים בעת קבלת התרומה שלכם. Binance זמין כמעט בכל מדינה, ותומך ברוב הבנקים וכרטיסי האשראי/חיוב. זו ההמלצה העיקרית שלנו כרגע. אנו מעריכים את הזמן שאתם משקיעים בלמידת אופן התרומה באמצעות שיטה זו, שכן זה עוזר לנו מאוד. לכרטיסי אשראי, כרטיסי חיוב, Apple Pay ו-Google Pay, אנו משתמשים ב-"Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). במערכת שלהם, "קפה" אחד שווה ל-$5, כך שהתרומה שלך תעוגל למכפלה הקרובה של 5. תרום באמצעות Cash App. אם יש לך Cash App, זו הדרך הקלה ביותר לתרום! לתשומת ליבך, על העברה באמצעות %(amount)s, Cash App עשויה להיגבות %(fee)s עמלה. בסכום של %(amount)s או יותר, ההעברה בחינם! תרום באמצעות כרטיס אשראי או חיוב מיידי. שיטה זו משתמשת בספק מטבעות קריפטוגרפיים כהמרת ביניים. זה יכול להיות קצת מבלבל, אז אנא השתמשו בשיטה זו רק אם שיטות תשלום אחרות לא עובדות. זה גם לא עובד בכל מדינה. איננו יכולים לתמוך בכרטיסי אשראי/חיוב ישירות, מכיוון שהבנקים לא רוצים לעבוד איתנו. ☹ עם זאת, ישנן מספר דרכים להשתמש בכרטיסי אשראי/חיוב בכל זאת, באמצעות שיטות תשלום אחרות: עם קריפטו אתם יכולים לתרום באמצעות BTC, ETH, XMR, ו-SOL. השתמשו באופציה זו אם יש לכם נסיון בשיטת תשלום זו. באמצעות קריפטו תוכל לתרום עם BTC, ETH, XMR ועוד. שירותי קריפטו מהירים אם אתם משתמשים בקריפטו בפעם הראשונה, אנו ממליצים להשתמש ב-%(options)s כדי לקנות ולתרום ביטקוין (המטבע הקריפטוגרפי המקורי והנפוץ ביותר). לתשומת ליבך, יתכן שעבור תרומות קטנות עמלות כרטיס האשראי ינטרלו את %(discount)s%% ההנחה, כך שאנו ממליצים על הרשמה למשך זמן ארוך. תרום באמצעות כרטיס אשראי/חיוב, PayPal, או Venmo. תוכל לבחור בין האפשרויות בעמוד הבא. Google Pay או Apple Pay יכולים לעבוד גם הם. לתשומת ליבך, העמלות על תרומות נמוכות הן גבוהות, כך שאנו ממליצים על מינוי ארוך טווח. כדי לתרום באמצעות פייפאל ארה"ב, אנו עומדים להשתמש בפייפאל קריפטו, שמאפשר לנו להשאר אנוניימיים. אנו מעריכים את הזמן שהקדשת כדי ללמוד כיצד לתרום לנו באופן זה, מכיון שזה עוזר לנו מאד. תרום באמצעות פייפאל. תרמו באמצעות חשבון PayPal רגיל. תרמו באמצעות Revolut. אם יש לכם Revolut, זו הדרך הקלה ביותר לתרום! שיטת התשלום הזו מאפשרת תשלום מקסימום של%(amount)s. אנא בחר שיטת תשלום שונה. שיטת התשלום מצריכה מינימום של %(amount)s. אנא בחר שיטת תשלום שונה. בינאנס קוין בייס קרקן אנא בחרו שיטת תשלום. “אמץ טורנט”: שם המשתמש שלך או הודעה בקובץ הטורנט <div %(div_months)s>פעם בכל שנים עשר חודשים של חברות</div> שם המשתמש או האזכור האנונימי שלך בקרדיטים גישה מוקדמת לפיצ'רים חדשים טלגרם בלעדי עם עדכונים מאחורי הקלעים %(number)s הורדות מהירות ליום אם תתרמו החודש! <a %(a_api)s>גישה ל-JSON API</a> סטטוס אגדי בשימור הידע והתרבות של האנושות ההטבות הקודמות, בנוסף: הרוויחו <strong>%(percentage)s%% הורדות בונוס</strong> על ידי <a %(a_refer)s>הפניית חברים</a>. המאמרים של SciDB <strong>לא מוגבלים </strong> גם ללא אימות כאשר שואלים שאלות על חשבון או תרומות, הוסיפו את מזהה החשבון שלכם, צילומי מסך, קבלות, כמה שיותר מידע. אנו בודקים את הדוא"ל שלנו כל 1-2 שבועות, כך שאי הכללת מידע זה תעכב כל פתרון. כדי לקבל עוד יותר הורדות, <a %(a_refer)s>הפנו את חבריכם</a>! אנחנו צוות קטן של מתנדבים. ייתכן שייקח לנו 1-2 שבועות להגיב. שימו לב ששם החשבון או התמונה עשויים להיראות מוזרים. אין סיבה לחשוש! החשבונות האלא מנוהלים על ידי שותפי התרומות שלנו. החשבונות שלנו לא נפרצו. תרום <span %(span_cost)s></span> <span %(span_label)s></span> עבור שנים עשר חודשים “%(tier_name)s” עבור חודש “%(tier_name)s” עבור עשרים וארבעה חדשים “%(tier_name)s” עבור שלשה חדשים “%(tier_name)s” למשך 48 חודשים “%(tier_name)s” עבור ששה חודשים “%(tier_name)s” למשך 96 חודשים “%(tier_name)s” תוכל לבטל את התרומה במהלך הצ'קאאוט. לחץ על כפתור 'תרום' כדי לאשר תרומה זו. <strong>לתשומת לבך:</strong> מחירי הקריפטו נעים בטווח רחב, לעתים אף בכ- 20%% בכמה דקות. זה עדיין פחות מהעמלות שאנו גובים באמצעי התשלום האחרים, שגובים בדרך כלל 50-60%% כדי לעבוד עם "תרומת צללים" כמו ארגוננו. <u>אם תשלח לנו את הקבלה עם הסכום המדויק ששילמת נזכה אותך בעבור מסלול החברות הנבחרר</u> (כל עוד הקבלה אינה ישנה מדי - יותר מכמה שעות). אנו מעריכים מאד את הסכמתך להסתבך עם אמצעי התשלום כל כך עבורנו!❤️ ❌ משהו השתבש. בבקשה טען את העמוד מחדש ונסה שנית. <span %(span_circle)s>1</span>רכוש ביטקוין ב-Paypal <span %(span_circle)s>2</span>העבר את הביטקוין לכתובת שלנו ✅ מפנה אל עמוד התרומה… תרום אנא המתינו לפחות <span %(span_hours)s>24 שעות</span> (ורעננו את הדף הזה) לפני שתיצרו איתנו קשר. אם ברצונכם לתרום (כל סכום) ללא חברות, אתם מוזמנים להשתמש בכתובת Monero (XMR) זו: %(address)s. לאחר שליחת הגיפט קארד, המערכת האוטומטית שלנו תאשר אותו תוך מספר דקות. אם זה לא עובד, נסה לשלוח בשנית את הגיפט קארד (<a %(a_instr)s>instructions</a>). אם זה עדיין לא עובד, שלח לנו אימייל ואנה תטפל בזה ידנית (התהליך עשוי לקחת מספר ימים), ושים לב שאתה מזכיר במייל שניסית כבר לשלוח בשנית. דוגמא: אנא השתמשו בטופס %(a_form)s אתר אמזון.קום הרשמי</a> כדי לשלוח לנו גיפט קארד של %(amount)s לכתובת האימייל להלן. "אל" מקבל המייל בטופס: אמזון גיפט קארד אין באפשרותנו לקבל גיפט קארד אחר,<strong>אנא שלח ישירות מהטופס באתר אמאזון.קום</strong>. אנו לא יכולים להחזיר את הגיפט קארד ששלחת אם לא תשתמש בטופס הזה. השתמש פעם אחת בלבד. ייחודי עבור חשבונך, אל תשתף. ממתין עבור גיפט קארד...(רענן את הדף כדי לבדוק) פתחו את <a %(a_href)s>עמוד התרומה עם קוד ה-QR</a>. סרקו את קוד ה-QR עם אפליקציית Alipay, או לחצו על הכפתור לפתיחת אפליקציית Alipay. אנא תאזרו בסבלנות; ייתכן שהעמוד ייקח זמן לטעון מכיוון שהוא בסין. <span %(style)s>3</span>בצעו תרומה (סרקו קוד QR או לחצו על כפתור) רכוש מטבע PYUSD בפייפאל קנו ביטקוין (BTC) באפליקציית Cash קנו קצת יותר (אנו ממליצים על %(more)s יותר) מהסכום שאתם תורמים (%(amount)s), כדי לכסות את עמלות העסקה. כל מה שנותר יישאר אצלכם. גשו לדף "ביטקוין" (BTC) באפליקציית Cash. העבירו את הביטקוין לכתובת שלנו לתרומות קטנות (מתחת ל-$25), ייתכן שתצטרכו להשתמש ב-Rush או Priority. לחצו על כפתור "שלח ביטקוין" כדי לבצע "משיכה". עברו מדולרים ל-BTC על ידי לחיצה על סמל %(icon)s. הזינו את סכום ה-BTC למטה ולחצו על "שלח". ראו <a %(help_video)s>סרטון זה</a> אם אתם נתקעים. שירותים מהירים הם נוחים, אך גובים עמלות גבוהות יותר. ניתן להשתמש בזה במקום חילופי קריפטו אם אתם מחפשים לבצע תרומה גדולה במהירות ולא אכפת לכם מעמלה של $5-10. ודאו לשלוח את סכום הקריפטו המדויק המוצג בעמוד התרומה, ולא את הסכום ב-$USD. אחרת העמלה תנוכה ולא נוכל להשלים את המנוי שלכם באופן אוטומטי. לפעמים אישור יכול לקחת עד 24 שעות, אז ודאו לרענן את הדף הזה (גם אם הוא פג תוקף). הנחיות עבור העברה עם כרטיס אשראי / חיוב מידי תרום דרך כרטיס אשראי / חיוב מידי חלק מהשלבים מזכירים ארנק קריפטו, אך אל דאגה, אינך חייב ללמוד דבר אודות קריפטו בשביל זה. הנחיות עבור %(coin_name)s םולשתה יטרפ תא תוריהמב אלמל ידכ ךלש וטפירקה קנרא תייצקילפא תועצמאב הזה QR -ה ד םלשל QR דוק קורס אנו תומכים רק בגרסה הסטנדרטית של מטבעות קריפטו, לא ברשתות או גרסאות אקזוטיות של מטבעות. אישור העסקה עשוי לקחת עד שעה, תלוי במטבע. תרום%(amount)s ב <a %(a_page)s>דף זה</a>. תרומתך פקעה. אנא בטלה וצור אחת חדשה. אם כבר שילמתם: כן, שלחתי את הקבלה באימייל אם שער החליפין של הקריפטו משתנה תוך כדי ההעברה, שימו לב שאתם מצרפים את הקבלה עם השער המקורי. אנו מאד מעריכים שהסתבכתם עם שימוש בקריפטו, זה עוזר לנו מאד! ❌ משהו השתבש. בבקשה רענן את הדף ונסה שנית. <span %(span_circle)s>%(circle_number)s</span>שלח לנו את הקבלה אם אתה נתקל בבעיות, אנא פנה אלינו דרך %(email)s וכלול כמה שיותר מידע אפשרי (כמו למשל צילום מסך). ✅ תודה על תרומתך! אנה תפעיל ידנית את החברות שלך תוך מספר ימים. שלח צילום מסך של הקבלה לכתובת האימות האישית שלך: לכשתשלחו את הקבלה במייל, לחצו על כפתור זה, כך אנה תוכל לסקור אותה ידנית (זה עשוי לקחת מספר ימים): שלחו קבלה או צילום מסך לכתובת האימות האישית שלכם. אל תשתמשו בכתובת דוא"ל זו לתרומת פייפאל שלכם. ביטול כן, בטל בבקשה האם אתה בטוח שברצונך לבטל? אל תבטל אם כבר שילמת. ❌ משהו השתבש. אנא טען מחדש את האתר ונסה שנית. תרמו תרומה חדשה ✅ תרומתך בוטלה. תאריך: %(date)s מזהה: %(id)s סדר מחדש סטטוס: <span %(span_label)s>%(label)s</span> סך הכל: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / חדש עבור: %(duration)s חדשים, כולל %(discounts)s%% הנחה)</span> סך הכל: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / חדש עבור %(duration)s חדשים)</span> 1. הכנס את כתובת האימייל שלך. 2. בחר אמצעי תשלום. 3. בחר שיטת תרומה שנית. 4. בחר ארנק "Self-hosted". 5. לחץ "אני מאשר בעלות". 6. אתה תקבל קבלה במייל. בבקשה שלח אותה אלינו, ואנו נאשר את תרומתך מוקדם ככל הניתן. (אולי תרצה לבטל ולתרום מחדש) ההסברים על דרכי התשלום אינם בתוקף. אם תרצה לתרום שנית, אנא השתמש בכפתור "הזמן שנית". שילמת כבר. אם ברצונך לבחון את דרכי התרומה בכל זאת, לחץ כאן: הצג הנחיות תרומות ישנות אם דף התרומה נחסם, נסו חיבור אינטרנט אחר (למשל VPN או אינטרנט סלולרי). למרבה הצער, דף Alipay נגיש לעיתים קרובות רק מ-<strong>סין היבשתית</strong>. ייתכן שתצטרכו להשבית זמנית את ה-VPN שלכם, או להשתמש ב-VPN לסין היבשתית (או הונג קונג גם עובד לפעמים). <span %(span_circle)s>1</span>תרום דרך Alipay תרום את הסכום הכולל של %(total)s באמצעות <a %(a_account)s>חשבון Alipay זה</a> הנחיות לתרומה עם Alipay <span %(span_circle)s>1</span>העבר לאחד מחשבונות הקריפטו שלנו תרום סכום של %(total)s לאחת מהכתובות הבאות: הנחיות קריפטו עקבו אחר ההנחיות לרכישת ביטקוין (BTC). תצטרכו לרכוש את הסכום אותו תרצו לתרום בלבד%(total)s. הכניסו את כתובת הביטקוין (BTC) שלכם, ועקבו אחרי ההנחיות כדי לשלוח את תרומתכם בסך %(total)s: <span %(span_circle)s>1</span>תרום באמצעות Pix תרום סכום כולל של %(total)s דרך <a %(a_account)s> חשבון ה-PIX הזה הנחיות לתרומה דרך Pix <span %(span_circle)s>1</span>תרמו ב-WeChat תרמו את הסכום הכולל של %(total)s באמצעות <a %(a_account)s>חשבון WeChat זה</a> הוראות WeChat השתמשו באחד מהשירותים הבאים של "כרטיס אשראי לביטקוין" המהירים, שלוקחים רק כמה דקות: כתובת BTC / ביטקוין (ארנק חיצוני): סכום BTC / ביטקוין: מלאו את הפרטים הבאים בטופס: אם מידע זה אינו מעודכן, אנא שלחו לנו דוא"ל כדי ליידע אותנו. אנא השתמשו ב<span %(underline)s>סכום המדויק</span> הזה. סך כל התשלום עשוי להיות גבוה יותר בגלל עמלות כרטיסי האשראי. עבור סכומים קטנים זה עשוי להיות יותר מההנחה שלנו, לצערינו. (מינימום: %(minimum)s, אין צורך באימות לעסקה הראשונה) (מינימום: %(minimum)s) (מינימום: %(minimum)s) (מינימום: %(minimum)s, אין צורך באימות לעסקה הראשונה) (מינימום: %(minimum)s) (מינימום: %(minimum)s תלוי במדינה, אין צורך באימות לעסקה הראשונה) עקוב אחר ההנחיות לרכישת מטבע PYUSD (פייפאל ארה"ב). רכשו מעט יותר (אנו ממליצים על %(more)s יותר) ממה שאתם תורמים (%(amount)s), כדי לכסות את עמלת ההעברה. היתרה תישמר אצלכם. גש אל העמוד "PYUSD" באפליקציית או אתר פייפאל. לחץ על כפתור "העבר" %(icon)s, ואז "שלח". סטטוס מעודכן כדי לאפס את הטיימר, צור תרומה חדשה. הקפידו להשתמש בסכום ה-BTC למטה, <em>ולא</em> ביורו או דולרים, אחרת לא נקבל את הסכום הנכון ולא נוכל לאשר את החברות שלכם באופן אוטומטי. קנו ביטקוין (BTC) ב-Revolut קנו קצת יותר (אנו ממליצים על %(more)s יותר) מהסכום שאתם תורמים (%(amount)s), כדי לכסות את עמלות העסקה. כל מה שיישאר יישאר אצלכם. גשו לדף "קריפטו" ב-Revolut כדי לקנות ביטקוין (BTC). העבירו את הביטקוין לכתובת שלנו לתרומות קטנות (מתחת ל-$25) ייתכן שתצטרכו להשתמש ב-Rush או Priority. לחצו על כפתור "שלח ביטקוין" כדי לבצע "משיכה". עברו מאירו ל-BTC על ידי לחיצה על סמל %(icon)s. הזינו את סכום ה-BTC למטה ולחצו על "שלח". ראו <a %(help_video)s>סרטון זה</a> אם אתם נתקעים. סטטוס: 1 2 מדריך צעד אחר צעד ראה מדריך צעד אחר צעד להלן. אחרת אתה עלול להנעל מחוץ לחשבון זה! אם עדיין לא עשיתם זאת, רשמו את הקוד הסודי שלכם להתחברות: תודה על תרומתך! זמן שנותר: תרומה העבר %(amount)s אל%(account)s ממתין לאישור (רענן את הדף כדי לבדוק)… ממתין להעברה (טען מחדש את הדף כדי לבדוק)… מוקדם יותר הורדות מהירות ב-24 השעות האחרונות נספרות כלפי המגבלה היומית. הורדות משרתי שותפים מהירים מסומנות על ידי %(icon)s. 18 השעות האחרונות לא הורדו קבצים עדיין. קבצים שהורדו אינם מוצגים לציבור. כל הזמנים הם ב-UTC. קבצים שהורדו אם הורדתם קובץ עם הורדות מהירות ואיטיות, הוא יופיע פעמיים. אל תדאגו יותר מדי, יש הרבה אנשים שמורידים מאתרים המקושרים על ידינו, וזה נדיר מאוד להסתבך. עם זאת, כדי להישאר בטוחים אנו ממליצים להשתמש ב-VPN (בתשלום), או ב-<a %(a_tor)s>Tor</a> (חינם). הורדתי את "1984" של ג'ורג' אורוול, האם המשטרה תבוא לדלתי? אתה אנה! מי זאת אנה? יש לנו API JSON יציב לחברים, לקבלת כתובת URL להורדה מהירה: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (תיעוד בתוך ה-JSON עצמו). לשימושים אחרים, כמו מעבר על כל הקבצים שלנו, בניית חיפוש מותאם אישית, וכדומה, אנו ממליצים <a %(a_generate)s>ליצור</a> או <a %(a_download)s>להוריד</a> את מסדי הנתונים ElasticSearch ו-MariaDB שלנו. ניתן לחקור את הנתונים הגולמיים באופן ידני <a %(a_explore)s>באמצעות קבצי JSON</a>. רשימת הטורנטים הגולמיים שלנו ניתנת להורדה גם כ<a %(a_torrents)s>JSON</a>. האם יש לכם API? איננו מארחים כאן חומרים המוגנים בזכויות יוצרים. אנו מנוע חיפוש, ולכן רק מאנדקסים מטה-דאטה שכבר זמין לציבור. כאשר אתם מורידים ממקורות חיצוניים אלו, אנו ממליצים לבדוק את החוקים באזורכם לגבי מה שמותר. איננו אחראים לתוכן שמתארח על ידי אחרים. אם יש לכם תלונות על מה שאתם רואים כאן, האפשרות הטובה ביותר היא לפנות לאתר המקורי. אנו מעדכנים את השינויים שלהם באופן קבוע במאגר הנתונים שלנו. אם אתם באמת חושבים שיש לכם תלונת DMCA תקפה שעלינו להגיב עליה, אנא מלאו את <a %(a_copyright)s>טופס תלונת DMCA / זכויות יוצרים</a>. אנו מתייחסים לתלונותיכם ברצינות, ונחזור אליכם בהקדם האפשרי. כיצד ניתן לדווח על הפרת זכויות יוצרים? הנה כמה ספרים שנושאים משמעות מיוחדת לעולם של ספריות צללים ושימור דיגיטלי: מהם הספרים האהובים עליך? אנו רוצים להזכיר לכולם שכל הקוד והנתונים שלנו הם קוד פתוח לחלוטין. זה ייחודי לפרויקטים כמו שלנו — אנחנו לא מכירים שום פרויקט אחר עם קטלוג עצום דומה שהוא גם קוד פתוח לחלוטין. אנו מאוד מקדמים בברכה כל מי שחושב שאנו מנהלים את הפרויקט שלנו בצורה גרועה לקחת את הקוד והנתונים שלנו ולהקים ספריית צללים משלו! אנחנו לא אומרים זאת מתוך טינה או משהו כזה — אנחנו באמת חושבים שזה יהיה מדהים כי זה יעלה את הרף לכולם וישמר טוב יותר את המורשת של האנושות. אני שונא איך שאתם מנהלים את הפרויקט הזה! נשמח שאנשים יגדירו <a %(a_mirrors)s>מראות</a>, ואנו נתמוך בכך כלכלית. כיצד אוכל לעזור? אכן כן. ההשראה שלנו לאיסוף מטה-דאטה היא המטרה של אהרון שוורץ ל"דף אינטרנט אחד לכל ספר שפורסם אי פעם", עבורו הוא יצר את <a %(a_openlib)s>Open Library</a>. הפרויקט הזה הצליח, אך המיקום הייחודי שלנו מאפשר לנו להשיג מטה-דאטה שהם לא יכולים. השראה נוספת הייתה הרצון שלנו לדעת <a %(a_blog)s>כמה ספרים יש בעולם</a>, כדי שנוכל לחשב כמה ספרים נותר לנו להציל. האם אתם אוספים מטה-דאטה? שימו לב ש-mhut.org חוסם טווחי IP מסוימים, ולכן ייתכן שתצטרכו להשתמש ב-VPN. <strong>אנדרואיד:</strong> לחצו על תפריט שלוש הנקודות בפינה הימנית העליונה, ובחרו “הוסף למסך הבית”. <strong>iOS:</strong> לחצו על כפתור “שיתוף” בתחתית, ובחרו “הוסף למסך הבית”. אין לנו אפליקציה רשמית לנייד, אך ניתן להתקין את האתר הזה כאפליקציה. האם יש לכם אפליקציה לנייד? אנא שלחו אותם ל<a %(a_archive)s>Internet Archive</a>. הם ישמרו עליהם כראוי. איך אני תורם ספרים או חומרים פיזיים אחרים? כיצד אני מבקש ספרים? <a %(a_blog)s>הבלוג של אנה</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — עדכונים שוטפים <a %(a_software)s>התוכנה של אנה</a> — הקוד הפתוח שלנו <a %(a_datasets)s>Datasets</a> — אודות הנתונים <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — דומיינים חלופיים האם ישנם משאבים נוספים על ארכיון אנה? <a %(a_translate)s>תרגום בתוכנת אנה</a> — מערכת התרגום שלנו <a %(a_wikipedia)s>ויקיפדיה</a> — עוד עלינו (אנא עזרו לשמור על דף זה מעודכן, או צרו אחד בשפה שלכם!) בחרו את ההגדרות שאתם אוהבים, השאירו את תיבת החיפוש ריקה, לחצו על "חיפוש", ואז סמנו את הדף באמצעות תכונת הסימניות של הדפדפן שלכם. כיצד אני שומר את הגדרות החיפוש שלי? אנו מקדמים בברכה חוקרי אבטחה לחפש פגיעויות במערכות שלנו. אנו תומכים גדולים בגילוי אחראי. צרו קשר <a %(a_contact)s>כאן</a>. כרגע איננו יכולים להעניק פרסי באגים, למעט פגיעויות שיש להן <a %(a_link)s>פוטנציאל לפגוע באנונימיות שלנו</a>, עבורן אנו מציעים פרסים בטווח של $10k-50k. נשמח להציע טווח רחב יותר של פרסי באגים בעתיד! שימו לב שמתקפות הנדסה חברתית אינן כלולות בטווח. אם אתם מתעניינים באבטחה התקפית, ורוצים לעזור בארכוב הידע והתרבות של העולם, הקפידו ליצור קשר איתנו. ישנן דרכים רבות בהן תוכלו לעזור. האם יש לכם תוכנית גילוי אחראי? אין לנו מספיק משאבים כדי לספק לכל אחד בעולם הורדות במהירות גבוהה, כמה שהיינו רוצים. אם תורם עשיר ירצה להתגייס ולספק זאת עבורנו, זה יהיה מדהים, אבל עד אז, אנחנו מנסים כמיטב יכולתנו. אנחנו פרויקט ללא מטרות רווח שמתקיים בקושי מתרומות. זו הסיבה שהטמענו שני מערכות להורדות חינמיות, עם השותפים שלנו: שרתים משותפים עם הורדות איטיות, ושרתים מעט מהירים יותר עם רשימת המתנה (כדי להפחית את מספר האנשים שמורידים בו זמנית). יש לנו גם <a %(a_verification)s>אימות דפדפן</a> להורדות האיטיות שלנו, כי אחרת בוטים ומגרדים ינצלו אותם, מה שיגרום להאטה נוספת למשתמשים לגיטימיים. שימו לב, כאשר משתמשים בדפדפן Tor, ייתכן שתצטרכו להתאים את הגדרות האבטחה שלכם. באפשרות הנמוכה ביותר, הנקראת "סטנדרטית", אתגר ה-Cloudflare turnstile מצליח. באפשרויות הגבוהות יותר, הנקראות "בטוח יותר" ו"הכי בטוח", האתגר נכשל. לעיתים, הורדות של קבצים גדולים עלולות להישבר באמצע. אנו ממליצים להשתמש במנהל הורדות (כגון JDownloader) כדי לחדש אוטומטית הורדות גדולות. למה ההורדות האיטיות כל כך איטיות? שאלות נפוצות (FAQ) השתמשו ב-<a %(a_list)s>מחולל רשימת הטורנטים</a> כדי ליצור רשימה של טורנטים שהכי זקוקים לשיתוף, במסגרת מגבלות האחסון שלכם. כן, ראו את עמוד <a %(a_llm)s>נתוני ה-LLM</a>. רוב הטורנטים מכילים את הקבצים ישירות, מה שאומר שניתן להורות ללקוחות הטורנטים להוריד רק את הקבצים הנדרשים. כדי לקבוע אילו קבצים להוריד, ניתן <a %(a_generate)s>ליצור</a> את המטה-דאטה שלנו, או <a %(a_download)s>להוריד</a> את מסדי הנתונים של ElasticSearch ו-MariaDB שלנו. למרבה הצער, מספר אוספי טורנטים מכילים קבצי .zip או .tar בשורש, ובמקרה זה יש להוריד את כל הטורנט לפני שניתן לבחור קבצים בודדים. אין כלים קלים לשימוש לסינון טורנטים זמינים עדיין, אבל אנו מקדמים בברכה תרומות. (יש לנו <a %(a_ideas)s>כמה רעיונות</a> למקרה האחרון.) תשובה ארוכה: תשובה קצרה: לא בקלות. אנו מנסים לשמור על מינימום כפילויות או חפיפות בין הטורנטים ברשימה זו, אך לא תמיד ניתן להשיג זאת, וזה תלוי מאוד במדיניות של הספריות המקוריות. עבור ספריות שמפרסמות את הטורנטים שלהן, זה לא בשליטתנו. עבור טורנטים שפורסמו על ידי ארכיון אנה, אנו מבצעים דה-דופליקציה רק בהתבסס על ה-MD5 hash, מה שאומר שגרסאות שונות של אותו ספר לא עוברות דה-דופליקציה. כן. אלו למעשה קבצי PDF ו-EPUB, פשוט אין להם סיומת בהרבה מהטורנטים שלנו. ישנם שני מקומות בהם ניתן למצוא את המטה-דאטה עבור קבצי הטורנט, כולל סוגי הקבצים/סיומות: 1. לכל אוסף או שחרור יש מטה-דאטה משלו. לדוגמה, <a %(a_libgen_nonfic)s>טורנטים של Libgen.rs</a> יש להם מסד נתונים של מטה-דאטה המתארח באתר Libgen.rs. אנו בדרך כלל מקשרים למשאבי מטה-דאטה רלוונטיים מדף ה-<a %(a_datasets)s>Datasets</a> של כל אוסף. 2. אנו ממליצים <a %(a_generate)s>ליצור</a> או <a %(a_download)s>להוריד</a> את מסדי הנתונים של ElasticSearch ו-MariaDB שלנו. אלה מכילים מיפוי לכל רשומה בארכיון אנה לקבצי הטורנט המתאימים לה (אם קיימים), תחת "torrent_paths" ב-JSON של ElasticSearch. חלק מלקוחות הטורנטים לא תומכים בגדלי חלקים גדולים, שלרבים מהטורנטים שלנו יש (לטורנטים חדשים יותר אנחנו כבר לא עושים זאת — למרות שזה תקין לפי המפרט!). אז נסו לקוח אחר אם אתם נתקלים בבעיה זו, או התלוננו בפני יוצרי לקוח הטורנטים שלכם. אני רוצה לעזור בשיתוף, אבל אין לי הרבה מקום בדיסק. הטורנטים איטיים מדי; האם אפשר להוריד את הנתונים ישירות מכם? האם אני יכול להוריד רק תת-קבוצה של הקבצים, כמו רק שפה או נושא מסוים? כיצד אתם מטפלים בכפילויות בטורנטים? האם אפשר לקבל את רשימת הטורנטים כ-JSON? אני לא רואה קבצי PDF או EPUB בטורנטים, רק קבצים בינאריים? מה לעשות? מדוע לקוח הטורנטים שלי לא יכול לפתוח חלק מקבצי הטורנט / קישורי המגנט שלכם? שאלות נפוצות על טורנטים כיצד אני מעלה ספרים חדשים? אנא ראה <a %(a_href)s>את הפרויקט המצוין הזה</a>. האם יש לך מוניטור לזמן פעולה? מהו הארכיון של אנה? הפכו לחברים כדי להשתמש בהורדות מהירות. אנו תומכים כעת בכרטיסי מתנה של Amazon, כרטיסי אשראי ודביט, קריפטו, Alipay, ו-WeChat. נגמרו לך ההורדות המהירות להיום. גישה הורדות לפי שעה ב-30 הימים האחרונים. ממוצע שעתי: %(hourly)s. ממוצע יומי: %(daily)s. אנו עובדים עם שותפים כדי להפוך את האוספים שלנו לנגישים וחופשיים לכל אחד. אנו מאמינים שלכל אחד יש זכות לחוכמה הקולקטיבית של האנושות. ו<a %(a_search)s>לא על חשבון המחברים</a>. ה-Datasets המשמשים בארכיון של אנה פתוחים לחלוטין, וניתן לשכפל אותם בכמויות גדולות באמצעות טורנטים. <a %(a_datasets)s>למידע נוסף…</a> ארכיון לטווח ארוך מאגר נתונים מלא חיפוש ספרים, מאמרים, מגזינים, קומיקס, רשומות ספרייה, מטה-דאטה, … כל ה<a %(a_code)s>קוד</a> וה<a %(a_datasets)s>נתונים</a> שלנו הם בקוד פתוח לחלוטין. <span %(span_anna)s>הארכיון של אנה</span> הוא פרויקט ללא מטרות רווח עם שני יעדים: <li><strong>שימור:</strong> גיבוי כל הידע והתרבות של האנושות.</li><li><strong>גישה:</strong> הפיכת הידע והתרבות הזו לזמינים לכל אחד בעולם.</li> יש לנו את אוסף הטקסטים האיכותי הגדול ביותר בעולם. <a %(a_llm)s>למידע נוסף…</a> נתוני אימון LLM 🪩 מראות: קריאה למתנדבים אם אתם מפעילים מעבד תשלומים אנונימי בסיכון גבוה, אנא צרו קשר איתנו. אנחנו גם מחפשים אנשים המעוניינים לפרסם מודעות קטנות בטוב טעם. כל ההכנסות הולכות למאמצי השימור שלנו. שימור אנו מעריכים ששמרנו כ-<a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% מהספרים בעולם</a>. אנו משמרים ספרים, מאמרים, קומיקס, מגזינים ועוד, על ידי הבאת חומרים אלה ממגוון <a href="https://en.wikipedia.org/wiki/Shadow_library">ספריות צללים</a>, ספריות רשמיות ואוספים אחרים למקום אחד. כל הנתונים האלה נשמרים לנצח על ידי הקלה על שכפולם בכמויות גדולות — באמצעות טורנטים — מה שמוביל להעתקים רבים ברחבי העולם. חלק מספריות הצללים כבר עושות זאת בעצמן (למשל Sci-Hub, Library Genesis), בעוד שהארכיון של אנה "משחרר" ספריות אחרות שאינן מציעות הפצה בכמויות גדולות (למשל Z-Library) או שאינן ספריות צללים כלל (למשל Internet Archive, DuXiu). הפצה רחבה זו, בשילוב עם קוד פתוח, הופכת את האתר שלנו לעמיד בפני הורדות ומבטיחה את השימור לטווח הארוך של הידע והתרבות האנושית. למדו עוד על <a href="/datasets">המאגרי הנתונים שלנו</a>. אם אתה <a %(a_member)s>חבר</a>, אין צורך באימות דפדפן. 🧬&nbsp;SciDB הוא המשך של Sci-Hub. SciDB פתוח DOI Sci-Hub <a %(a_paused)s>הפסיק</a> להעלות מאמרים חדשים. גישה ישירה ל-%(count)s מאמרים אקדמיים 🧬&nbsp;SciDB הוא המשך של Sci-Hub, עם הממשק המוכר וצפייה ישירה ב-PDFים. הכניסו את ה-DOI לצפייה. יש לנו את כל אוסף Sci-Hub, כמו גם מאמרים חדשים. רובם ניתנים לצפייה ישירה עם הממשק המוכר, בדומה ל-Sci-Hub. חלקם ניתנים להורדה ממקורות חיצוניים, ובמקרה זה אנו מציגים קישורים אליהם. אתם יכולים לעזור מאוד על ידי שיתוף טורנטים. <a %(a_torrents)s>למידע נוסף…</a> >%(count)s משתפים <%(count)s משתפים %(count_min)s–%(count_max)s משתפים 🤝 מחפשים מתנדבים כפרויקט ללא מטרות רווח וקוד פתוח, אנחנו תמיד מחפשים אנשים שיעזרו. הורדות IPFS רשימה לפי %(by)s, נוצר <span %(span_time)s>%(time)s</span> שמור ❌ משהו השתבש. נא לנסות שוב. ✅ נשמר. נא לטעון מחדש את הדף. הרשימה ריקה. ערוך הוסף או הסר מרשימה זו על ידי מציאת קובץ ופתיחת כרטיסיית "רשימות". רשימה כיצד אנו יכולים לעזור הסרת חפיפות (דדופליקציה) חילוץ טקסט ומטה-דאטה OCR אנו מסוגלים לספק גישה מהירה לאוספים המלאים שלנו, כמו גם לאוספים שטרם פורסמו. זו גישה ברמת ארגון שאנו יכולים לספק בתמורה לתרומות בטווח של עשרות אלפי דולרים. אנו גם מוכנים להחליף זאת באוספים איכותיים שעדיין אין לנו. אנו יכולים להחזיר לך כסף אם תוכל לספק לנו העשרה של הנתונים שלנו, כגון: תמוך בארכיון ארוך טווח של הידע האנושי, תוך קבלת נתונים טובים יותר עבור המודל שלך! <a %(a_contact)s>צור קשר</a> כדי לדון כיצד נוכל לעבוד יחד. מובן היטב ש-LLM משגשגים על נתונים באיכות גבוהה. יש לנו את האוסף הגדול ביותר של ספרים, מאמרים, מגזינים וכו' בעולם, שהם חלק ממקורות הטקסט האיכותיים ביותר. נתוני LLM קנה מידה וטווח ייחודיים האוסף שלנו מכיל מעל מאה מיליון קבצים, כולל כתבי עת אקדמיים, ספרי לימוד ומגזינים. אנו משיגים קנה מידה זה על ידי שילוב מאגרים קיימים גדולים. חלק מהאוספים המקוריים שלנו כבר זמינים בכמויות גדולות (Sci-Hub, וחלקים מ-Libgen). מקורות אחרים שחררנו בעצמנו. <a %(a_datasets)s>Datasets</a> מציג סקירה מלאה. האוסף שלנו כולל מיליוני ספרים, מאמרים ומגזינים מתקופת טרום הספרים האלקטרוניים. חלקים גדולים מהאוסף הזה כבר עברו OCR, וכבר יש להם מעט חפיפות פנימיות. המשך אם איבדתם את המפתח, אנא <a %(a_contact)s>צרו קשר איתנו</a> וספקו כמה שיותר מידע. ייתכן שתצטרכו ליצור חשבון חדש זמני כדי ליצור איתנו קשר. אנא <a %(a_account)s>התחברו</a> כדי לצפות בדף זה.</a> כדי למנוע מבוטים ליצור חשבונות רבים, עלינו לאמת את הדפדפן שלך תחילה. אם אתה נתקע בלולאה אינסופית, אנו ממליצים להתקין <a %(a_privacypass)s>Privacy Pass</a>. ייתכן שגם יעזור לכבות חוסמי פרסומות ותוספים אחרים לדפדפן. התחברות / הרשמה הארכיון של אנה מושבת זמנית לצורך תחזוקה. אנא חזרו בעוד שעה. מחבר חלופי תיאור חלופי מהדורה חלופית סיומת חלופית שם קובץ חלופי מוציא לאור חלופי כותרת חלופית תאריך קוד פתוח קראו עוד… תיאור חפשו ב"ארכיון של אנה" לפי מספר SSNO של CADAL חפשו ב"ארכיון של אנה" לפי מספר SSID של DuXiu חפשו ב"ארכיון של אנה" לפי מספר DXID של DuXiu חפש בארכיון של אנה מסת"ב (ISBN) חפשו בארכיון של אנה לפי מספר OCLC (WorldCat) חפש בארכיון של אנה מזהה Open Library הצופה המקוון של ארכיון אנה %(count)s עמודים מושפעים לאחר ההורדה: גרסה טובה יותר של קובץ זה עשויה להיות זמינה ב-%(link)s הורדות טורנט בכמות גדולה אוסף השתמשו בכלים מקוונים להמרה בין פורמטים. מומלצים כלים להמרה: %(links)s לקבצים גדולים, אנו ממליצים להשתמש במנהל הורדות כדי למנוע הפרעות. מומלצים מנהלי הורדות: %(links)s אינדקס ספרים אלקטרוניים של EBSCOhost (מומחים לדבר בלבד) (בנוסף לחץ "קבל" למעלה) (לחץ "קבל" למעלה) הורדות חיצוניות <strong>🚀 הורדות מהירות</strong> נותרו לך %(remaining)s להיום. תודה על היותך חבר! ❤️ <strong>🚀 הורדות מהירות</strong> נגמרו לך ההורדות המהירות להיום. <strong>🚀 הורדות מהירות</strong> הורדת קובץ זה לאחרונה. הקישורים נשארים תקפים לזמן מה. <strong>🚀 הורדות מהירות</strong> הפוך ל<a %(a_membership)s>חבר</a> כדי לתמוך בשימור ארוך טווח של ספרים, מאמרים ועוד. כדי להראות את תודתנו על תמיכתך, תקבל הורדות מהירות. ❤️ 🚀 הורדות מהירות 🐢 הורדות איטיות שאל מ-Internet Archive IPFS שער #%(num)d (יתכן שתצטרכו לנסות מספר פעמים עם IPFS) Libgen.li Libgen.rs סיפורת Libgen.rs עיון הפרסומות שלהם ידועות ככוללות תוכנות זדוניות, לכן השתמשו בחוסם פרסומות או אל תלחצו על פרסומות “Send to Kindle” של Amazon “Send to Kobo/Kindle” של djazz MagzDB ManualsLib Nexus/STC (קבצי Nexus/STC עשויים להיות לא אמינים להורדה) לא נמצאו הורדות. כל אפשרויות ההורדה מכילות את אותו הקובץ, והן אמורות להיות בטוחות לשימוש. עם זאת, תמיד יש לנקוט בזהירות בעת הורדת קבצים מהאינטרנט, במיוחד מאתרים חיצוניים לארכיון של אנה. לדוגמה, ודאו שהמכשירים שלכם מעודכנים. (ללא הפניה) פתח בצופה שלנו (פתח בצופה) אפשרות #%(num)d: %(link)s %(extra)s מצאו את הרשומה המקורית ב-CADAL חפש ידנית ב-DuXiu מצא את התיעוד המקורי ב ISBNdb מצאו את הרשומה המקורית ב-WorldCat מצא את התיעוד המקורי ב-Open Library חפש מסת"ב (ISBN) במגוון מסדי מידע אחרים (מיועד לפטרונים עם מוגבלות הדפסה בלבד) PubMed תצטרכו קורא ספרים אלקטרוניים או קורא PDF כדי לפתוח את הקובץ, בהתאם לפורמט הקובץ. מומלצים קוראי ספרים אלקטרוניים: %(links)s הארכיון של אנה 🧬 SciDB Sci-Hub: %(doi)s (ייתכן ש-DOI המשויך לא יהיה זמין ב-Sci-Hub) ניתן לשלוח קבצי PDF ו-EPUB גם לקינדל או לקובו שלכם. מומלצים כלים: %(links)s מידע נוסף ב-<a %(a_slow)s>שאלות נפוצות</a>. תמכו בסופרים ובספריות אם אהבתם את זה ואתם יכולים להרשות לעצמכם, שקלו לקנות את המקור, או לתמוך בסופרים ישירות. אם זה זמין בספרייה המקומית שלך, שקול לשאול אותו בחינם שם. הורדות משרת שותף אינן זמינות זמנית עבור קובץ זה. טורנט משותפים מהימנים. Z-Library Z-Library ב-Tor (דורש את דפדפן Tor) הצג הורדות חיצוניות <span class="font-bold">❌ קובץ זה עשוי להיות בעייתי, והוא הוסתר מספריית מקור.</span> לפעמים זה נעשה לפי בקשת בעל זכויות היוצרים, לפעמים בגלל שיש חלופה טובה יותר, אך לפעמים זה בגלל בעיה בקובץ עצמו. ייתכן שעדיין ניתן להוריד אותו, אך אנו ממליצים קודם לחפש קובץ חלופי. פרטים נוספים: אם עדיין ברצונך להוריד קובץ זה, הקפד להשתמש רק בתוכנה מהימנה ומעודכנת לפתיחתו. הערות מטה-דאטה AA: חפשו בארכיון של אנה עבור “%(name)s” חוקר קודים: צפה בחוקר קודים “%(name)s” כתובת URL: אתר אינטרנט: אם יש לך את הקובץ הזה והוא עדיין לא זמין בארכיון של אנה, שקול <a %(a_request)s>להעלות אותו</a>. קובץ השאלה דיגיטלית מבוקרת של Internet Archive “%(id)s” זהו רישום של קובץ מ-Internet Archive, לא קובץ להורדה ישירה. ניתן לנסות לשאול את הספר (קישור למטה), או להשתמש ב-URL זה כאשר <a %(a_request)s>מבקשים קובץ</a>. שפר מטה-דאטה רשומת מטה-דאטה של CADAL SSNO %(id)s זהו רישום מטה-דאטה, לא קובץ להורדה. ניתן להשתמש ב-URL זה כאשר <a %(a_request)s>מבקשים קובץ</a>. רישום מטה-דאטה של DuXiu SSID %(id)s רישום מטה-דאטה של ISBNdb %(id)s רשומת מטה-דאטה של MagzDB ID %(id)s רשומת מטה-דאטה של Nexus/STC ID %(id)s רשומת מטה-דאטה של מספר OCLC (WorldCat) %(id)s רשומת מטה-דאטה של Open Library %(id)s Sci-Hub קובץ“%(id)s” לא נמצא “%(md5_input)s” לא נמצא במאגר שלנו. הוסף תגובה (%(count)s) אתה יכול לקבל את ה-md5 מה-URL, לדוגמה MD5 של גרסה טובה יותר של הקובץ הזה (אם יש). מלא את זה אם יש קובץ אחר שדומה מאוד לקובץ הזה (אותה מהדורה, אותה סיומת קובץ אם תוכל למצוא), שאנשים צריכים להשתמש בו במקום הקובץ הזה. אם אתה יודע על גרסה טובה יותר של הקובץ הזה מחוץ לארכיון של אנה, אז אנא <a %(a_upload)s>העלה אותה</a>. משהו השתבש. נא לטעון את הדף מחדש ולנסות שוב. השארתם תגובה. ייתכן שייקח דקה עד שהיא תופיע. אנא השתמש ב-<a %(a_copyright)s>טופס תביעת DMCA / זכויות יוצרים</a>. תאר את הבעיה (חובה) אם לקובץ זה יש איכות גבוהה, תוכלו לדון עליו כאן! אם לא, נא להשתמש בכפתור "דווח על בעיית קובץ". איכות קובץ מעולה (%(count)s) איכות הקובץ למדו כיצד <a %(a_metadata)s>לשפר את המטה-דאטה</a> של קובץ זה בעצמכם. תיאור הבעיה אנא <a %(a_login)s>התחבר</a>. אהבתי את הספר הזה! עזור לקהילה על ידי דיווח על איכות הקובץ הזה! 🙌 משהו השתבש. נא לטעון את הדף מחדש ולנסות שוב. דווח על בעיה בקובץ (%(count)s) תודה על הגשת הדוח שלכם. הוא יוצג בעמוד זה, וייבדק ידנית על ידי אנה (עד שיהיה לנו מערכת ניהול מתאימה). השאר תגובה שלח דוח מה לא בסדר בקובץ הזה? השאלה (%(count)s) תגובות (%(count)s) הורדות (%(count)s) חקירת מטה-דאטה (%(count)s) רשימות (%(count)s) סטטיסטיקות (%(count)s) לפרטים על קובץ זה, עיינו ב-<a %(a_href)s>קובץ JSON</a> שלו. זהו קובץ המנוהל על ידי <a %(a_ia)s>ספריית ההשאלה הדיגיטלית המבוקרת של IA</a>, ואונדקס על ידי הארכיון של אנה לחיפוש. למידע על ה-Datasets השונים שאנו הרכבנו, ראו את <a %(a_datasets)s>עמוד ה-Datasets</a>. מטה-דאטה מרשומה מקושרת שפר מטה-דאטה ב-Open Library "MD5 של קובץ" הוא hash שמחושב מתוכן הקובץ, והוא ייחודי במידה סבירה על בסיס תוכן זה. כל הספריות הצללים שאנו אינדקסנו כאן משתמשות בעיקר ב-MD5s לזיהוי קבצים. קובץ עשוי להופיע במספר ספריות צללים. למידע על ה-Datasets השונים שאנו הרכבנו, ראו את <a %(a_datasets)s>עמוד ה-Datasets</a>. דווח על איכות הקובץ סך כל ההורדות: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} מטה-דאטה צ'כי %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} האתי טראסט %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} אזהרה: מספר רשומות מקושרות: כשאתם מסתכלים על ספר בארכיון של אנה, אתם יכולים לראות שדות שונים: כותרת, מחבר, מוציא לאור, מהדורה, שנה, תיאור, שם קובץ ועוד. כל פיסות המידע הללו נקראות <em>מטה-דאטה</em>. מכיוון שאנו משלבים ספרים מספריות מקור שונות <em>, אנו מציגים את המטה-דאטה הזמין בספריית המקור. לדוגמה, עבור ספר שקיבלנו מ-Library Genesis, נציג את הכותרת ממאגר הנתונים של Library Genesis. לפעמים ספר נמצא במספר <em>ספריות מקור</em>, שיכולות להכיל שדות מטה-דאטה שונים. במקרה כזה, אנו פשוט מציגים את הגרסה הארוכה ביותר של כל שדה, מכיוון שזו כנראה מכילה את המידע השימושי ביותר! עדיין נציג את השדות האחרים מתחת לתיאור, למשל כ”כותרת חלופית” (אבל רק אם הם שונים). אנו גם מחלצים <em>קודים</em> כמו מזהים ומסווגים מספריית המקור. <em>מזהים</em> מייצגים באופן ייחודי מהדורה מסוימת של ספר; דוגמאות הן ISBN, DOI, מזהה Open Library, מזהה Google Books או מזהה Amazon. <em>מסווגים</em> מקבצים יחד מספר ספרים דומים; דוגמאות הן Dewey Decimal (DCC), UDC, LCC, RVK או GOST. לפעמים קודים אלו מקושרים במפורש בספריות המקור, ולפעמים אנו יכולים לחלץ אותם משם הקובץ או התיאור (בעיקר ISBN ו-DOI). אנו יכולים להשתמש במזהים כדי למצוא רשומות ב-<em>אוספים של מטה-דאטה בלבד</em>, כמו OpenLibrary, ISBNdb או WorldCat/OCLC. יש לשונית <em>מטה-דאטה</em> ספציפית במנוע החיפוש שלנו אם תרצו לעיין באוספים אלו. אנו משתמשים ברשומות תואמות כדי למלא שדות מטה-דאטה חסרים (למשל אם חסרה כותרת), או למשל כ”כותרת חלופית” (אם יש כותרת קיימת). כדי לראות בדיוק מאיפה הגיע המטה-דאטה של ספר, ראו את לשונית <em>“פרטים טכניים”</em> בעמוד הספר. יש שם קישור ל-JSON הגולמי של הספר, עם הפניות ל-JSON הגולמי של הרשומות המקוריות. למידע נוסף, ראו את הדפים הבאים: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>חיפוש (לשונית מטה-דאטה)</a>, <a %(a_codes)s>חוקר קודים</a>, ו-<a %(a_example)s>דוגמת JSON של מטה-דאטה</a>. לבסוף, כל המטה-דאטה שלנו יכול להיות <a %(a_generated)s>נוצר</a> או <a %(a_downloaded)s>הורד</a> כמאגרי ElasticSearch ו-MariaDB. רקע אתם יכולים לעזור בשימור ספרים על ידי שיפור המטה-דאטה! קודם כל, קראו את הרקע על מטה-דאטה בארכיון של אנה, ולאחר מכן למדו כיצד לשפר מטה-דאטה באמצעות קישור עם Open Library, והרוויחו חברות חינם בארכיון של אנה. שפרו מטה-דאטה אז אם אתם נתקלים בקובץ עם מטה-דאטה גרוע, איך עליכם לתקן אותו? אתם יכולים ללכת לספריית המקור ולעקוב אחר הנהלים שלה לתיקון מטה-דאטה, אבל מה לעשות אם קובץ נמצא במספר ספריות מקור? יש מזהה אחד שמטופל באופן מיוחד בארכיון של אנה. <strong>שדה ה-md5 של annas_archive ב-Open Library תמיד גובר על כל מטה-דאטה אחר!</strong> בואו נחזור קצת אחורה ונלמד על Open Library. Open Library נוסדה בשנת 2006 על ידי אהרון שוורץ במטרה של “דף אינטרנט אחד לכל ספר שפורסם אי פעם”. זהו סוג של ויקיפדיה למטה-דאטה של ספרים: כל אחד יכול לערוך אותו, הוא ברישיון חופשי, וניתן להוריד אותו בכמויות גדולות. זהו מאגר ספרים שהכי מתאים למשימתנו — למעשה, הארכיון של אנה הושפע מחזונו וחייו של אהרון שוורץ. במקום להמציא את הגלגל מחדש, החלטנו להפנות את המתנדבים שלנו ל-Open Library. אם אתם רואים ספר עם מטה-דאטה שגוי, אתם יכולים לעזור בדרך הבאה: שימו לב שזה עובד רק עבור ספרים, לא עבור מאמרים אקדמיים או סוגי קבצים אחרים. עבור סוגי קבצים אחרים אנו עדיין ממליצים למצוא את ספריית המקור. ייתכן שייקח כמה שבועות עד שהשינויים ייכללו בארכיון של אנה, מכיוון שעלינו להוריד את הדאמפ האחרון של Open Library וליצור מחדש את אינדקס החיפוש שלנו.  לכו ל-<a %(a_openlib)s>אתר Open Library</a>. מצאו את רשומת הספר הנכונה. <strong>אזהרה:</strong> הקפידו לבחור את <strong>המהדורה</strong> הנכונה. ב-Open Library, יש לכם “יצירות” ו”מהדורות”. “יצירה” יכולה להיות “הארי פוטר ואבן החכמים”. “מהדורה” יכולה להיות: המהדורה הראשונה משנת 1997 שפורסמה על ידי בלומסברי עם 256 עמודים. המהדורה בכריכה רכה משנת 2003 שפורסמה על ידי ריינקוסט בוקס עם 223 עמודים. התרגום הפולני משנת 2000 "הארי פוטר ואבן החכמים" על ידי מדיה רודזינה עם 328 עמודים. לכל המהדורות הללו יש מספרי ISBN ותכנים שונים, אז הקפידו לבחור את הנכונה! ערכו את הרשומה (או צרו אותה אם אינה קיימת), והוסיפו כמה שיותר מידע מועיל! אתם כבר כאן, אז כדאי להפוך את הרשומה למדהימה באמת. תחת "מספרי זיהוי" בחרו "הארכיון של אנה" והוסיפו את ה-MD5 של הספר מהארכיון של אנה. זהו המחרוזת הארוכה של אותיות ומספרים אחרי "/md5/" ב-URL. נסו למצוא קבצים נוספים בארכיון של אנה שתואמים גם הם לרשומה זו, והוסיפו גם אותם. בעתיד נוכל לקבץ אותם ככפילויות בדף החיפוש של הארכיון של אנה. כשאתם מסיימים, רשמו את ה-URL שעדכנתם זה עתה. לאחר שתעדכנו לפחות 30 רשומות עם MD5 מהארכיון של אנה, שלחו לנו <a %(a_contact)s>אימייל</a> ושלחו לנו את הרשימה. אנו ניתן לכם חברות חינם בארכיון של אנה, כך שתוכלו לבצע עבודה זו ביתר קלות (וכתודה על עזרתכם). אלו חייבות להיות עריכות איכותיות שמוסיפות כמויות משמעותיות של מידע, אחרת בקשתכם תידחה. בקשתכם תידחה גם אם אחת מהעריכות תבוטל או תתוקן על ידי המנהלים של Open Library. קישור ל-Open Library אם תהיו מעורבים באופן משמעותי בפיתוח ותפעול העבודה שלנו, נוכל לדון בשיתוף יותר מהכנסות התרומות איתכם, כדי שתוכלו לפרוס לפי הצורך. נשלם עבור האירוח רק לאחר שתגדירו הכל ותוכיחו שאתם מסוגלים לשמור על הארכיון מעודכן עם עדכונים. זה אומר שתצטרכו לשלם מכיסכם עבור החודשיים הראשונים. הזמן שלכם לא יפוצה (וגם שלנו לא), מכיוון שזהו עבודה התנדבותית טהורה. אנחנו מוכנים לכסות את הוצאות האירוח וה-VPN, בהתחלה עד $200 לחודש. זה מספיק לשרת חיפוש בסיסי ופרוקסי מוגן DMCA. הוצאות אירוח אנא <strong>אל תיצרו איתנו קשר</strong> לבקש רשות, או לשאלות בסיסיות. מעשים מדברים חזק יותר ממילים! כל המידע נמצא שם, אז פשוט תתחילו בהקמת המראה שלכם. אל תהססו לפרסם כרטיסים או בקשות מיזוג ל-Gitlab שלנו כאשר אתם נתקלים בבעיות. ייתכן שנצטרך לבנות תכונות ספציפיות למראה יחד איתכם, כמו מיתוג מחדש מ"ארכיון של אנה" לשם האתר שלכם, (בהתחלה) השבתת חשבונות משתמשים, או קישור חזרה לאתר הראשי שלנו מדפי הספרים. לאחר שהמראה שלכם פועל, אנא צרו איתנו קשר. נשמח לבדוק את אבטחת הפעולה שלכם, וברגע שזה יהיה מוצק, נקשר למראה שלכם ונתחיל לעבוד קרוב יותר איתכם. תודה מראש לכל מי שמוכן לתרום בדרך זו! זה לא לבעלי לב חלש, אבל זה יחזק את אריכות הימים של הספרייה הפתוחה הגדולה ביותר בהיסטוריה האנושית. מתחילים כדי להגדיל את העמידות של ארכיון אנה, אנו מחפשים מתנדבים להפעיל מראות. הגרסה שלכם מובחנת בבירור כמראה, לדוגמה "הארכיון של בוב, מראה של ארכיון של אנה". אתם מוכנים לקחת את הסיכונים הכרוכים בעבודה זו, שהם משמעותיים. יש לכם הבנה עמוקה של האבטחה התפעולית הנדרשת. התוכן של <a %(a_shadow)s>הפוסטים</a> <a %(a_pirate)s>האלה</a> ברור לכם מאליו. בהתחלה לא ניתן לכם גישה להורדות משרת השותפים שלנו, אבל אם הכל ילך כשורה, נוכל לשתף אתכם בזה. אתם מפעילים את בסיס הקוד הפתוח של ארכיון של אנה, ומעדכנים באופן קבוע הן את הקוד והן את הנתונים. אתם מוזמנים לתרום ל<a %(a_codebase)s>קוד שלנו</a> — בשיתוף פעולה עם הצוות שלנו — כדי לגרום לזה לקרות. אנו מחפשים את זה: מראות: קריאה למתנדבים בצעו תרומה נוספת. עדיין אין תרומות. <a %(a_donate)s>בצע את התרומה הראשונה שלי.</a> פרטי התרומות אינם מוצגים לציבור. התרומות שלי 📡 להעתקה המונית של האוסף שלנו, בדקו את דפי ה-<a %(a_datasets)s>Datasets</a> וה-<a %(a_torrents)s>Torrents</a>. הורדות מכתובת ה-IP שלך ב-24 השעות האחרונות: %(count)s. 🚀 כדי לקבל הורדות מהירות יותר ולדלג על בדיקות הדפדפן, <a %(a_membership)s>הפוך לחבר</a>. הורדה מאתר שותף אתם מוזמנים להמשיך לגלוש בארכיון של אנה בכרטיסייה אחרת בזמן ההמתנה (אם הדפדפן שלכם תומך ברענון כרטיסיות ברקע). אתם מוזמנים להמתין לטעינת מספר דפי הורדה בו זמנית (אך אנא הורידו רק קובץ אחד בכל פעם לכל שרת). ברגע שתקבלו קישור להורדה הוא יהיה תקף למספר שעות. תודה על ההמתנה, זה שומר על האתר נגיש בחינם לכולם! 😊 🔗 כל קישורי ההורדה עבור קובץ זה: <a %(a_main)s>דף הקובץ הראשי</a>. ❌ הורדות איטיות אינן זמינות דרך VPNs של Cloudflare או מכתובות IP של Cloudflare. ❌ הורדות איטיות זמינות רק דרך האתר הרשמי. בקרו ב-%(websites)s. 📚 השתמשו בקישור הבא להורדה: <a %(a_download)s>הורד עכשיו</a>. כדי לתת לכולם הזדמנות להוריד קבצים בחינם, עליך להמתין לפני שתוכל להוריד קובץ זה. אנא המתן <span %(span_countdown)s>%(wait_seconds)s</span> שניות כדי להוריד קובץ זה. אזהרה: היו הרבה הורדות מכתובת ה-IP שלכם ב-24 השעות האחרונות. ההורדות עשויות להיות איטיות מהרגיל. אם אתם משתמשים ב-VPN, חיבור אינטרנט משותף, או שספק האינטרנט שלכם משתף כתובות IP, אזהרה זו עשויה להיות בשל כך. שמור ❌ משהו השתבש. אנא נסה שנית. ✅ נשמר. אנא טען מחדש את הדף. שנה את שם התצוגה. המזהה שלך (החלק שלאחר הסולמית) לא יכול להשתנות. פרופיל נוצר <span %(span_time)s>%(time)s</span> ערוך רשימות צור רשימה חדשה על ידי מציאת קובץ ובחירת הטאב "רשימות". אין רשימות עדיין פרופיל לא נמצא. פרופיל כרגע, איננו יכולים להיענות לבקשות ספרים. אל תשלחו לנו בקשות לספרים במייל. אנא הגישו את בקשותיכם בפורומים של Z-Library או Libgen. רשומה בארכיון של אנה DOI: %(doi)s הורדה SciDB Nexus/STC תצוגה מקדימה אינה זמינה עדיין. הורידו את הקובץ מ<a %(a_path)s>ארכיון אנה</a>. כדי לתמוך בנגישות ושימור ארוך טווח של הידע האנושי, הפכו ל<a %(a_donate)s>חברים</a>. כבונוס, 🧬&nbsp;SciDB נטען מהר יותר לחברים, ללא מגבלות. לא עובד? נסו <a %(a_refresh)s>לרענן</a>. Sci-Hub הוסף שדה חיפוש ספציפי חיפוש תיאורים ותגובות מטה-דאטה שנת פרסום מתקדם גישה תוכן הצג רשימה טבלה סוג קובץ שפה סדר לפי הגדול ביותר הכי רלוונטי החדש ביותר (גודל קובץ) (קוד פתוח) (שנת פרסום) הכי ישן אקראי הקטן ביותר מקור נאסף ונפתח קוד על ידי AA השאלה דיגיטלית (%(count)s) מאמרי כתב עת (%(count)s) מצאנו התאמות ב: %(in)s. תוכלו להתייחס ל-URL שנמצא שם כאשר <a %(a_request)s>מבקשים קובץ</a>. מטה-דאטה (%(count)s) כדי לחקור את אינדקס החיפוש לפי קודים, השתמשו ב-<a %(a_href)s>חוקר הקודים</a>. אינדקס החיפוש מתעדכן מדי חודש. הוא כולל כרגע ערכים עד %(last_data_refresh_date)s. למידע טכני נוסף, ראו את <a %(link_open_tag)s>דף מערכי הנתונים</a>. לא כולל כולל בלבד לא מסומן עוד… הבא … הקודם אינדקס החיפוש הזה כולל כרגע מטה-דאטה מספריית ההשאלה הדיגיטלית המבוקרת של Internet Archive. <a %(a_datasets)s>עוד על ה-Datasets שלנו</a>. לספריות השאלה דיגיטליות נוספות, ראו <a %(a_wikipedia)s>ויקיפדיה</a> ואת <a %(a_mobileread)s>MobileRead Wiki</a>. לתביעות DMCA / זכויות יוצרים <a %(a_copyright)s>לחצו כאן</a>. זמן הורדה שגיאה במהלך החיפוש. נסו <a %(a_reload)s>לרענן את הדף</a>. אם הבעיה נמשכת, אנא שלחו לנו דוא"ל לכתובת %(email)s. הורדה מהירה למעשה, כל אחד יכול לעזור לשמר את הקבצים הללו על ידי שיתוף <a %(a_torrents)s>רשימת הטורנטים המאוחדת שלנו</a>. ➡️ לפעמים זה קורה באופן שגוי כאשר שרת החיפוש איטי. במקרים כאלה, <a %(a_attrs)s>רענון</a> יכול לעזור. ❌ ייתכן שקובץ זה מכיל בעיות. מחפשים מאמרים? אינדקס החיפוש הזה כולל כרגע מטה-דאטה ממקורות מטה-דאטה שונים. <a %(a_datasets)s>עוד על ה-Datasets שלנו</a>. ישנם מקורות רבים מאוד של מטה-דאטה ליצירות כתובות ברחבי העולם. <a %(a_wikipedia)s>דף הוויקיפדיה הזה</a> הוא התחלה טובה, אבל אם אתם מכירים רשימות טובות נוספות, אנא הודיעו לנו. למטה-דאטה, אנו מציגים את הרשומות המקוריות. אנחנו לא מבצעים מיזוג של רשומות. כיום יש לנו את הקטלוג הפתוח המקיף ביותר בעולם של ספרים, מאמרים ועבודות כתובות אחרות. אנו משקפים את Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>ועוד</a>. <span class="font-bold">לא נמצאו קבצים.</span> נסו מונחי חיפוש ומסננים שונים או פחותים. תוצאות %(from)s-%(to)s (%(total)s סה"כ) אם אתם מוצאים "ספריות צללים" נוספות שעלינו לשקף, או אם יש לכם שאלות, אנא צרו קשר איתנו ב-%(email)s. %(num)d התאמות חלקיות %(num)d+ התאמות חלקיות הקלידו בתיבה כדי לחפש קבצים בספריות השאלה דיגיטליות. הקלד בתיבה כדי לחפש בקטלוג שלנו של %(count)s קבצים להורדה ישירה, אותם אנו <a %(a_preserve)s>משמרים לנצח</a>. הקלידו בתיבה כדי לחפש. הקלד בתיבה כדי לחפש בקטלוג שלנו של %(count)s מאמרים אקדמיים ומאמרי כתבי עת, אותם אנו <a %(a_preserve)s>משמרים לנצח</a>. הקלידו בתיבה כדי לחפש מטה-דאטה מספריות. זה יכול להיות שימושי כאשר <a %(a_request)s>מבקשים קובץ</a>. טיפ: השתמשו בקיצורי מקלדת “/” (חיפוש), “enter” (חיפוש), “j” (למעלה), “k” (למטה), “<” (עמוד קודם), “>” (עמוד הבא) לניווט מהיר יותר. אלו הם רשומות מטה-דאטה, <span %(classname)s>לא</span> קבצים להורדה. הגדרות חיפוש חיפוש השאלה דיגיטלית הורדה מאמרי כתבי עת מטה-דאטה חיפוש חדש %(search_input)s - חיפוש החיפוש לקח יותר מדי זמן, מה שאומר שייתכן שתראו תוצאות לא מדויקות. לפעמים <a %(a_reload)s>רענון</a> הדף עוזר. החיפוש לקח יותר מדי זמן, דבר שכיח בשאילתות רחבות. ייתכן שמספרי הסינון אינם מדויקים. להעלאות גדולות (מעל 10,000 קבצים) שלא מתקבלות על ידי Libgen או Z-Library, אנא צרו קשר איתנו ב-%(a_email)s. ל-Libgen.li, ודאו קודם להיכנס ל-<a %(a_forum)s >הפורום שלהם</a> עם שם משתמש %(username)s וסיסמה %(password)s, ואז חזרו ל-<a %(a_upload_page)s >דף ההעלאה שלהם</a>. לעת עתה, אנו מציעים להעלות ספרים חדשים לספריית ג'נסיס. זהו ה<a %(a_guide)s>מדריך הפשוט</a>. שים לב ששני הקישורים יוצאים מאותה מערכת העלאות. להעלאות קטנות (עד 10,000 קבצים) אנא העלו אותם גם ל-%(first)s וגם ל-%(second)s. לחלופין, תוכלו להעלות אותם ל-Z-Library <a %(a_upload)s>כאן</a>. כדי להעלות מאמרים אקדמיים, אנא העלו גם (בנוסף ל-Library Genesis) ל-<a %(a_stc_nexus)s>STC Nexus</a>. הם הספרייה הצללית הטובה ביותר למאמרים חדשים. עדיין לא שילבנו אותם, אבל נעשה זאת בשלב כלשהו. תוכלו להשתמש ב-<a %(a_telegram)s>בוט ההעלאה שלהם ב-Telegram</a>, או ליצור קשר עם הכתובת המופיעה בהודעה המוצמדת שלהם אם יש לכם יותר מדי קבצים להעלות בדרך זו. <span %(label)s>עבודת התנדבות כבדה (פרסים כספיים של 50-5,000 דולר):</span> אם אתם יכולים להקדיש הרבה זמן ו/או משאבים למשימה שלנו, נשמח לעבוד איתכם בצורה קרובה יותר. בסופו של דבר תוכלו להצטרף לצוות הפנימי. למרות שיש לנו תקציב מוגבל, אנחנו יכולים להעניק <span %(bold)s>💰 פרסים כספיים</span> עבור העבודה האינטנסיבית ביותר. <span %(label)s>עבודת התנדבות קלה:</span> אם יש לך רק כמה שעות פה ושם, עדיין יש הרבה דרכים שבהן תוכל לעזור. אנו מתגמלים מתנדבים עקביים עם <span %(bold)s>🤝 חברות בארכיון של אנה</span>. הארכיון של אנה מסתמך על מתנדבים כמוך. אנו מקבלים בברכה כל רמת מחויבות, ויש לנו שתי קטגוריות עיקריות של עזרה שאנו מחפשים: אם אינכם יכולים להתנדב בזמן שלכם, אתם עדיין יכולים לעזור לנו מאוד על ידי <a %(a_donate)s>תרומת כסף</a>, <a %(a_torrents)s>העלאת טורנטים</a>, <a %(a_uploading)s>העלאת ספרים</a>, או <a %(a_help)s>סיפור לחברים שלכם על הארכיון של אנה</a>. <span %(bold)s>חברות:</span> אנו מציעים גישה ישירה ומהירה לאוספים שלנו בתמורה לתרומה ברמת הארגון או בתמורה לאוספים חדשים (למשל סריקות חדשות, Datasets OCR’ed, העשרת הנתונים שלנו). <a %(a_contact)s>צרו קשר</a> אם זה מתאים לכם. ראו גם את <a %(a_llm)s>עמוד ה-LLM שלנו</a>. פרסים אנחנו תמיד מחפשים אנשים עם כישורי תכנות מוצקים או כישורי אבטחת מידע התקפית כדי להשתתף. אתם יכולים לעשות שינוי משמעותי בשימור המורשת של האנושות. כתודה, אנחנו מעניקים חברות על תרומות משמעותיות. כתודה ענקית, אנחנו מעניקים פרסים כספיים עבור משימות חשובות וקשות במיוחד. זה לא אמור להיחשב כתחליף לעבודה, אבל זה תמריץ נוסף ויכול לעזור עם עלויות שנגרמו. רוב הקוד שלנו הוא קוד פתוח, ונבקש שגם הקוד שלכם יהיה כזה כאשר נעניק את הפרס. ישנם כמה יוצאים מן הכלל שנוכל לדון בהם באופן פרטני. פרסים מוענקים לאדם הראשון שמסיים משימה. אתם מוזמנים להגיב על כרטיס פרס כדי ליידע אחרים שאתם עובדים על משהו, כך שאחרים יוכלו להמתין או ליצור איתכם קשר כדי לשתף פעולה. אבל היו מודעים לכך שאחרים עדיין חופשיים לעבוד על זה גם ולנסות להקדים אתכם. עם זאת, אנחנו לא מעניקים פרסים עבור עבודה מרושלת. אם יוגשו שתי עבודות איכותיות קרוב זו לזו (בתוך יום או יומיים), ייתכן שנבחר להעניק פרסים לשתיהן, לפי שיקול דעתנו, לדוגמה 100%% עבור ההגשה הראשונה ו-50%% עבור ההגשה השנייה (כך שסה"כ 150%%). עבור הפרסים הגדולים יותר (במיוחד פרסי גירוד נתונים), אנא צרו איתנו קשר כאשר סיימתם כ-5%% מהמשימה, ואתם בטוחים שהשיטה שלכם תוכל להתרחב למלוא היעד. תצטרכו לשתף אותנו בשיטה שלכם כדי שנוכל לתת משוב. בנוסף, כך נוכל להחליט מה לעשות אם ישנם מספר אנשים שמתקרבים לפרס, כמו להעניק אותו למספר אנשים, לעודד אנשים לשתף פעולה, וכו'. אזהרה: המשימות עם הפרסים הגבוהים הן <span %(bold)s>קשות</span> — ייתכן שכדאי להתחיל עם הקלות יותר. גשו ל<a %(a_gitlab)s>רשימת הבעיות שלנו ב-Gitlab</a> ומיינו לפי "עדיפות תווית". זה מראה בערך את סדר המשימות שחשובות לנו. משימות ללא פרסים מפורשים עדיין זכאיות לחברות, במיוחד אלו המסומנות "Accepted" ו-"המועדפות של אנה". ייתכן שתרצו להתחיל עם "פרויקט למתחילים". התנדבות קלה כעת יש לנו גם ערוץ Matrix מסונכרן ב-%(matrix)s. אם יש לכם כמה שעות פנויות, אתם יכולים לעזור במגוון דרכים. הקפידו להצטרף ל<a %(a_telegram)s>צ'אט המתנדבים בטלגרם</a>. כאות הערכה, אנו בדרך כלל מעניקים 6 חודשים של "ספרן בר מזל" עבור אבני דרך בסיסיות, ויותר עבור עבודה מתמשכת. כל אבן דרך דורשת עבודה באיכות גבוהה — עבודה מרושלת פוגעת בנו יותר משהיא עוזרת ואנו נדחה אותה. אנא <a %(a_contact)s>שלחו לנו מייל</a> כאשר תגיעו לאבן דרך. %(links)s קישורים או צילומי מסך של בקשות שמילאת. מילוי בקשות לספרים (או מאמרים וכו') בפורומים של Z-Library או Library Genesis. אין לנו מערכת בקשות לספרים משלנו, אבל אנחנו משקפים את הספריות האלה, כך ששיפורן משפר גם את הארכיון של אנה. אבן דרך משימה תלוי במשימה. משימות קטנות שפורסמו ב<a %(a_telegram)s>צ'אט המתנדבים בטלגרם</a>. בדרך כלל עבור חברות, לפעמים עבור פרסים קטנים. משימות קטנות מפורסמות בקבוצת הצ'אט של המתנדבים שלנו. ודאו להשאיר תגובה על בעיות שאתם מתקנים, כדי שאחרים לא יכפילו את עבודתכם. %(links)s קישורים של רשומות ששיפרת. ניתן להשתמש ב<a %(a_list)s >רשימת בעיות metadata אקראיות</a> כנקודת התחלה. שפרו מטה-דאטה על ידי <a %(a_metadata)s>קישור</a> עם Open Library. אלה צריכים להראות לך מודיע למישהו על הארכיון של אנה, והם מודים לך. %(links)s קישורים או צילומי מסך. להפיץ את הבשורה על הארכיון של אנה. לדוגמה, על ידי המלצה על ספרים ב-AA, קישור לפוסטים בבלוג שלנו, או באופן כללי להפנות אנשים לאתר שלנו. תרגום מלא של שפה (אם היא לא הייתה קרובה להשלמה כבר). <a %(a_translate)s>תרגום</a> האתר. קישור להיסטוריית העריכה המראה שביצעתם תרומות משמעותיות. שיפור דף הוויקיפדיה של הארכיון של אנה בשפה שלכם. כללו מידע מדף הוויקיפדיה של AA בשפות אחרות, ומהאתר והבלוג שלנו. הוסיפו הפניות ל-AA בדפים רלוונטיים אחרים. התנדבות ופרסים 