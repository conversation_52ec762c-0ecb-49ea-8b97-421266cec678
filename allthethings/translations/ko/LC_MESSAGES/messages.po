msgid "layout.index.invalid_request"
msgstr "잘못된 요청입니다. %(websites)s를 방문하십시오."

msgid "layout.index.header.tagline_scihub"
msgstr "사이-허브 (<PERSON>i-<PERSON><PERSON>)"

msgid "layout.index.header.tagline_libgen"
msgstr "립진 (LibGen)"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive 대출 도서관"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " 그리고 "

msgid "layout.index.header.tagline_and_more"
msgstr "등등"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;우리는 %(libraries)s를 미러링합니다."

msgid "layout.index.header.tagline_newnew2b"
msgstr "우리는 %(scraped)s을 스크랩하고 오픈 소스로 제공합니다."

msgid "layout.index.header.tagline_open_source"
msgstr "우리의 모든 코드와 데이터는 완전히 오픈 소스입니다."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;인류 역사상 가장 큰 규모의 진정한 열린 도서관."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;책, %(paper_count)s&nbsp;논문 — 영원히 보존됨."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;세계에서 가장 큰 오픈 소스 오픈 데이터 라이브러리 ⭐️&nbsp;Sci-Hub, Library Genesis, Z-Library 등의 미러입니다. 📈&nbsp;%(book_any)s권의 책, %(journal_article)s장의 문서, %(book_comic)s권의 만화, %(magazine)s권의 잡지를 영원히 보존합니다."

msgid "layout.index.header.tagline_short"
msgstr "📚 세계 최대의 오픈 소스 오픈 데이터 라이브러리. Mirrors Scihub, Libgen, Zlib 등."

msgid "common.md5_report_type_mapping.metadata"
msgstr "잘못된 메타데이터 (예: 제목, 설명, 표지 이미지)"

msgid "common.md5_report_type_mapping.download"
msgstr "파일을 열 수 없습니다(예: 파일이 손상됨, DRM 해제 안됨)"

msgid "common.md5_report_type_mapping.broken"
msgstr "파일을 열 수 없습니다(예시:파일이 손상됨, DRM 해제 안됨)"

msgid "common.md5_report_type_mapping.pages"
msgstr "품질 불량(예: 포맷 문제, 스캔 품질 불량, 페이지 누락)"

msgid "common.md5_report_type_mapping.spam"
msgstr "스팸 / 파일 제거 필요 (예: 광고, 모욕적 내용)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "저작권 청구"

msgid "common.md5_report_type_mapping.other"
msgstr "기타"

msgid "common.membership.tier_name.bonus"
msgstr "보너스 다운로드"

msgid "common.membership.tier_name.2"
msgstr "명석한 책벌레"

msgid "common.membership.tier_name.3"
msgstr "복덩이 사서"

msgid "common.membership.tier_name.4"
msgstr "광나는 데이터광"

msgid "common.membership.tier_name.5"
msgstr "어메이징 아키비스트"

msgid "common.membership.format_currency.total"
msgstr "총 %(amount)s"

msgid "common.membership.format_currency.total_with_usd"
msgstr "총 %(amount)s (%(amount_usd)s)"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s 보너스)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "무급의"

msgid "common.donation.order_processing_status_labels.1"
msgstr "결제된"

msgid "common.donation.order_processing_status_labels.2"
msgstr "취소된"

msgid "common.donation.order_processing_status_labels.3"
msgstr "만료된"

msgid "common.donation.order_processing_status_labels.4"
msgstr "안나가 확인하기 까지 기다림"

msgid "common.donation.order_processing_status_labels.5"
msgstr "유효하지 않은(무효한)"

msgid "page.donate.title"
msgstr "기부"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "%(a_donation)s기부가 이미 진행중입니다. 다른 기부를 하기 전에 이 기부를 완료하거나 취소하세요."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "%(a_all_donations)s나의 모든 기부 보기"

msgid "page.donate.header.text1"
msgstr "안나의 아카이브는 비영리, 오픈 소스, 오픈 데이터 프로젝트입니다. 기부하고 회원이 됨으로써 우리의 운영과 개발을 지원하게 됩니다. 모든 회원들에게: 우리를 지켜주셔서 감사합니다! ❤️"

msgid "page.donate.header.text2"
msgstr "자세한 내용은 <a %(a_donate)s>기부FAQ(질문)</a>를 참조하세요."

msgid "page.donate.refer.text1"
msgstr "더 많은 다운로드를 받으려면, <a %(a_refer)s>친구에게 추천하세요</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "사용자 %(profile_link)s의 추천으로 %(percentage)s%% 보너스 고속 다운로드를 받았습니다."

msgid "page.donate.bonus_downloads.period"
msgstr "이것은 전체 회원 기간에 적용됩니다."

msgid "page.donate.perks.fast_downloads"
msgstr "하루마다 %(number)s번의 고속 다운로드"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "이번 달에 기부하시면!"

msgid "page.donate.membership_per_month"
msgstr "%(cost)s / 월"

msgid "page.donate.buttons.join"
msgstr "참가"

msgid "page.donate.buttons.selected"
msgstr "선택된"

msgid "page.donate.buttons.up_to_discounts"
msgstr "%(percentage)s까지의 할인"

msgid "page.donate.perks.scidb"
msgstr "인증 없이 <strong>무제한</strong> SciDB 논문들"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> 접근"

msgid "page.donate.perks.refer"
msgstr "<a %(a_refer)s>친구 추천</a>으로 <strong>%(percentage)s%% 보너스 다운로드</strong>를 획득하세요."

msgid "page.donate.perks.credits"
msgstr "크레딧에 귀하의 유저네임 또는 익명 언급"

msgid "page.donate.perks.previous_plus"
msgstr "이전 특전과 더불어:"

msgid "page.donate.perks.early_access"
msgstr "새로운 기능에 대한 조기 액세스"

msgid "page.donate.perks.exclusive_telegram"
msgstr "비하인드 스토리 업데이트가 포함된 독점 텔레그램"

msgid "page.donate.perks.adopt"
msgstr "\"Adopt a torrent\": 사용자 이름 또는 메시지를 토렌트 파일 이름에 넣기 <div %(div_months)s> 멤버쉽 12달 마다 한번씩 </div>"

msgid "page.donate.perks.legendary"
msgstr "인류의 지식과 문화를 보존하는 전설적인 업적"

msgid "page.donate.expert.title"
msgstr "고급 액세스"

msgid "page.donate.expert.contact_us"
msgstr "문의하기"

msgid "page.donate.small_team"
msgstr "저희는 소규모 자원봉사자 팀입니다. 응답하는 데 1-2주가 걸릴 수도 있습니다."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>무제한</strong> 고속 다운로드"

msgid "page.donate.expert.direct_sftp"
msgstr "다이렉트 <strong>SFTP</strong> 서비스"

msgid "page.donate.expert.enterprise_donation"
msgstr "새로운 컬렉션(예: 새로운 스캔, OCR'ed 데이터셋)에 대한 기업 수준의 기부 또는 교환."

msgid "page.donate.header.large_donations_wealthy"
msgstr "부유한 개인이나 기관의 큰 기부를 환영합니다. "

msgid "page.donate.header.large_donations"
msgstr "$5,000달러 이상의 기부에 대해서는 %(email)s로 직접 연락 바랍니다."

msgid "page.donate.header.recurring"
msgstr "이 페이지의 멤버십은 월 단위이지만, 일회성 기부(반복되지 않음)입니다. <a %(faq)s>기부FAQ(질문)</a>를 참조하세요."

msgid "page.donate.without_membership"
msgstr "멤버십 없이 기부(어떤 금액이든)를 원하시면, 이 Monero (XMR) 주소를 사용하세요: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "결제 방법을 선택해 주세요."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(일시적으로 사용 불가)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s 기프트 카드"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "은행 카드 (앱 사용)"

msgid "page.donate.payment.buttons.crypto"
msgstr "암호화폐 %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "캐시 앱"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "신용/직불 카드"

msgid "page.donate.payment.buttons.paypal"
msgstr "페이팔 (US) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal(페이팔) (정기)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "카드 / PayPal(페이팔) / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "신용/직불/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "알리페이"

msgid "page.donate.payment.buttons.pix"
msgstr "픽스 (브라질)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "페이팔"

msgid "page.donate.payment.buttons.bank_card"
msgstr "은행 카드"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "신용/직불 카드 (백업)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "신용/직불 카드 2"

msgid "page.donate.payment.buttons.binance"
msgstr "바이낸스"

msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat(위챗)"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "알리페이 / 위챗"

msgid "page.donate.payment.desc.crypto"
msgstr "암호화폐를 사용하면 BTC, ETH, XMR 및 SOL을 사용하여 기부할 수 있습니다. 암호화폐에 이미 익숙한 경우 이 옵션을 사용하세요."

msgid "page.donate.payment.desc.crypto2"
msgstr "암호화폐로는 BTC, ETH, XMR 등을 사용하여 기부하실 수 있습니다."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "암호화폐를 처음 사용하신다면, %(options)s를 사용하여 비트코인(원조이자 가장 많이 사용되는 암호화폐)을 구매하고 기부하는 것을 추천합니다."

msgid "page.donate.payment.processor.binance"
msgstr "바이낸스"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "PayPal US를 사용하여 기부하려면 익명을 유지할 수 있는 PayPal Crypto를 사용할 것입니다. 시간을 내어 이 방법을 사용하여 기부하는 방법을 배워 주신 것에 감사드립니다. 이는 저희에게 많은 도움이 됩니다."

msgid "page.donate.payment.desc.paypal_short"
msgstr "페이팔을 사용하여 기부하세요."

msgid "page.donate.payment.desc.cashapp"
msgstr "Cash App을 사용하여 기부하세요."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Cash App을 가지고 계시다면, 이게 가장 간편한 기부 방법입니다!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "%(amount)s 이하의 거래에 대해서는, Cash App이 %(fee)s의 수수료를 청구할 수 있다는 점을 알아두세요. %(amount)s 이상은 무료입니다!"

msgid "page.donate.payment.desc.revolut"
msgstr "Revolut을 사용하여 기부하세요."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Revolut을 가지고 계시다면, 이것이 가장 쉬운 기부 방법입니다!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "신용 카드나 직불 카드를 사용하여 기부하세요."

msgid "page.donate.payment.desc.google_apple"
msgstr "구글 페이나 애플 페이도 작동할 수 있습니다."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "적은 기부에 대해서는 신용 카드의 수수료가 저희의 %(discount)s%% 할인을 없앨 수 있으니, 더 긴 구독을 추천합니다."

msgid "page.donate.payment.desc.longer_subs"
msgstr "적은 기부는 수수료가 높으므로, 더 긴 구독을 추천합니다."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Binance를 사용하면 신용/직불 카드나 은행 계좌로 비트코인을 구매한 후 그 비트코인을 우리에게 기부할 수 있습니다. 이렇게 하면 기부를 받을 때 보안과 익명성을 유지할 수 있습니다."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance는 거의 모든 국가에서 이용 가능하며, 대부분의 은행 및 신용/직불 카드를 지원합니다. 현재 저희의 주요 추천 방법입니다. 이 방법을 통해 기부하는 방법을 시간을 내어 배워주셔서 감사합니다. 이는 저희에게 큰 도움이 됩니다."

msgid "page.donate.payment.desc.paypalreg"
msgstr "일반 PayPal(페이팔) 계정을 사용하여 기부하세요."

msgid "page.donate.payment.desc.givebutter"
msgstr "신용/직불 카드, PayPal(페이팔), 또는 Venmo를 사용하여 기부할 수 있습니다. 다음 페이지에서 이 중 하나를 선택할 수 있습니다."

msgid "page.donate.payment.desc.amazon"
msgstr "아마존 기프트 카드를 사용하여 기부하세요."

msgid "page.donate.payment.desc.amazon_round"
msgstr "리셀러가 수락하는 금액(최소 %(minimum)s)으로 반올림해야 한다는 점을 주의해주세요."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>중요:</strong> 저희는 다른 아마존 웹사이트가 아닌 amazon.com만을 지원합니다. 예시로, .de, .co.uk, .ca는 지원되지 않습니다."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>중요:</strong> 이 옵션은 %(amazon)s용입니다. 다른 Amazon 웹사이트를 사용하려면 위에서 선택하세요."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "이 방법은 암호화폐 제공자를 중간 변환으로 사용합니다. 복잡할 수 있음으로 다른 결제 방법이 작동하지 않을 때만 이 방법을 사용하세요. 이것은 또한 모든 국가에서 가능하지 않습니다."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "알리페이 앱을 통해 신용/직불카드로 기부하세요 (설정이 매우 간단합니다)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>알리페이 앱 설치"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "<a %(a_app_store)s>Apple App Store</a> 또는 <a %(a_play_store)s>Google Play Store</a>에서 알리페이 앱을 설치하세요."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "휴대폰 번호로 등록하세요."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "추가적인 개인 정보는 필요하지 않습니다."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>은행 카드 추가"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "지원 카드: Visa, MasterCard, JCB, Diners Club 및 Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "자세한 내용은 <a %(a_alipay)s>이 가이드</a>를 참조하세요."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "은행들이 우리와 협력하기를 원하지 않기 때문에 신용/직불 카드를 직접 지원할 수 없습니다. ☹ 하지만 다른 결제 방법을 사용하는, 신용/직불 카드를 사용할 수 있는 여러 가지 방법이 있습니다:"

msgid "page.donate.payment.buttons.amazon"
msgstr "아마존 기프트카드"

msgid "page.donate.ccexp.amazon_com"
msgstr "신용/직불 카드를 사용하여 Amazon.com 기프트 카드를 보내주세요."

msgid "page.donate.ccexp.alipay"
msgstr "알리페이는 국제 신용/직불 카드를 지원합니다. 자세한 내용은 <a %(a_alipay)s>이 가이드</a>를 참조하세요."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay)는 국제 신용/직불 카드를 지원합니다. WeChat 앱에서 “나 => 서비스 => 지갑 => 카드 추가”로 이동하십시오. 해당 옵션이 보이지 않으면 “나 => 설정 => 일반 => 도구 => Weixin Pay => 활성화”를 통해 활성화하십시오."

msgid "page.donate.ccexp.crypto"
msgstr "신용/직불 카드를 사용하여 암호화폐를 구매할 수 있습니다."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "암호화폐 익스프레스 서비스"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "익스프레스 서비스는 편리하지만 수수료가 더 높습니다."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "더 큰 금액을 빠르게 기부하고 싶고 $5-10의 수수료를 개의치 않는다면 암호화폐 거래소 대신 이를 사용할 수 있습니다."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "꼭 기부 페이지에 표시된 정확한 암호화폐 금액을 보내야 합니다. $USD 금액을 보내지 마세요."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "그렇지 않으면 수수료가 차감되어 자동으로 멤버십을 처리할 수 없습니다."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(최소: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(최소: %(minimum)s, 국가에 따라 다름, 첫 거래는 인증 불필요)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(최소: %(minimum)s, 첫 거래는 인증 불필요)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(최소: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(최소: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(최소: %(minimum)s)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "이 정보가 최신이 아닌 경우, 이메일로 알려주시기 바랍니다."

msgid "page.donate.payment.desc.bmc"
msgstr "신용카드, 직불카드, Apple Pay 및 Google Pay의 경우, “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>)를 사용합니다. 그들의 시스템에서 한 “커피”는 $5와 같으므로, 귀하의 기부는 5의 배수로 반올림됩니다."

msgid "page.donate.duration.intro"
msgstr "구독할 기간을 선택합니다."

msgid "page.donate.duration.1_mo"
msgstr "한 달"

msgid "page.donate.duration.3_mo"
msgstr "세 달"

msgid "page.donate.duration.6_mo"
msgstr "여섯 달"

msgid "page.donate.duration.12_mo"
msgstr "열두 달"

msgid "page.donate.duration.24_mo"
msgstr "24개월"

msgid "page.donate.duration.48_mo"
msgstr "48개월"

msgid "page.donate.duration.96_mo"
msgstr "96개월"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s><span %(span_discount)s></span> 할인 후</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "이 결제 방법은 최소 %(amount)s를 필요로 합니다. 다른 기간이나 결제 방법을 선택해 주세요."

msgid "page.donate.buttons.donate"
msgstr "기부하기"

msgid "page.donate.payment.maximum_method"
msgstr "이 결제 방법은 최대 %(amount)s까지 사용 가능합니다. 다른 기간이나 결제 방법을 선택해 주세요."

msgid "page.donate.login2"
msgstr "회원이 되시려면 <%(a_login)s> 로그인 또는 등록 </a>를 부탁드립니다. 응원해주셔서 감사합니다!"

msgid "page.donate.payment.crypto_select"
msgstr "선호하는 암호화폐 선택:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(최소 금액)"

msgid "page.donate.coinbase_eth"
msgstr "(Coinbase에서 이더리움을 보낼 때 사용)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(경고: 높은 최소 금액)"

msgid "page.donate.submit.confirm"
msgstr "기부를 확정하기 위해 기부 버튼을 눌러 주세요."

msgid "page.donate.submit.button"
msgstr "<span %(span_cost)s></span> <span %(span_label)s></span> 기부하기"

msgid "page.donate.submit.cancel_note"
msgstr "결제 중에도 구독을 취소하실 수 있습니다."

msgid "page.donate.submit.success"
msgstr "✅ 기부 페이지로 리다이렉팅…"

msgid "page.donate.submit.failure"
msgstr "❌ 무언가 잘못되었습니다. 페이지를 새로고침하고 다시 시도해 주세요."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / 개월"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "1개월"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "3개월간"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "6개월간"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "12개월간"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "24개월간"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "48개월 동안"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "96개월 동안"

msgid "page.donate.submit.button.label.1_mo"
msgstr "1개월 \"%(tier_name)s\""

msgid "page.donate.submit.button.label.3_mo"
msgstr "3개월 \"%(tier_name)s\""

msgid "page.donate.submit.button.label.6_mo"
msgstr "6개월 \"%(tier_name)s\""

msgid "page.donate.submit.button.label.12_mo"
msgstr "12개월 \"%(tier_name)s\""

msgid "page.donate.submit.button.label.24_mo"
msgstr "24개월 \"%(tier_name)s\""

msgid "page.donate.submit.button.label.48_mo"
msgstr "48개월 동안 “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "96개월 동안 “%(tier_name)s”"

msgid "page.donation.title"
msgstr "기부"

msgid "page.donation.header.date"
msgstr "날짜: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "%(discounts)s합계: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / 월 %(duration)s%% discount)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "총합: %(total)s <span %(span_details)s>(%(duration)s 개월 간 월 %(monthly_amount_usd)s )</span>"

msgid "page.donation.header.status"
msgstr "상태: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "식별자: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "취소"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "정말로 취소하시겠습니까? 이미 결제하셨다면 취소하지 마십시오."

msgid "page.donation.header.cancel.confirm.button"
msgstr "예, 취소하기"

msgid "page.donation.header.cancel.success"
msgstr "✅ 기부가 취소되었습니다."

msgid "page.donation.header.cancel.new_donation"
msgstr "새로 기부하기"

msgid "page.donation.header.cancel.failure"
msgstr "❌오류가 발생했습니다. 페이지를 새로고침하고 다시 시도해 주세요."

msgid "page.donation.header.reorder"
msgstr "재주문"

msgid "page.donation.old_instructions.intro_paid"
msgstr "이미 결제하셨습니다. 결제 방법을 다시 확인하고 싶으시다면 클릭하세요:"

msgid "page.donation.old_instructions.show_button"
msgstr "이전의 결제 방법 보이기"

msgid "page.donation.thank_you_donation"
msgstr "기부에 감사드립니다!"

msgid "page.donation.thank_you.secret_key"
msgstr "아직 로그인하지 않은 경우, 로그인하기 위한 비밀 키를 적어 주세요:"

msgid "page.donation.thank_you.locked_out"
msgstr "그렇지 않으면 이 계정이 잠길 수 있습니다!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "이 결제 방법은 오래되었습니다. 새로 기부를 하고 싶으시다면 위의 \"재주문\" 버튼을 사용하세요."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>중요:</strong> 암호화폐 가격은 심하게 변동할 수 있읍니다. 몇 분만에 20%%까지도요. 그러나 이것은 우리가 많은 결제 제공자들에게서 청구받는수수료보다 적습니다. 이들은 우리 같은 \"어둠의 자선단체\"와 함께하는데 흔히 50~60%%를 청구합니다. <u>결제하신 원 가격의 영수증을 보내주신다면 저희는 선택하신 멤버십을 드릴 것입니다.</u> (영수증이 몇 시간보다 오래되지 않았다면) 저희를 지원하기 위해 노력해 주셔서 진심으로 감사드립니다! ❤️"

msgid "page.donation.expired"
msgstr "이 기부는 만료되었습니다. 취소하고 새로 기부해 주세요."

msgid "page.donation.payment.crypto.top_header"
msgstr "암호화폐 방법"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>암호화 계정 중 하나로 전송"

msgid "page.donation.payment.crypto.text1"
msgstr "다음 주소 중 하나에 총 %(total)s 금액을 기부합니다:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Paypal에서 비트코인 구매"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "PayPal 앱이나 웹사이트에서 “Crypto” 페이지를 찾으십시오. 일반적으로 “Finances” 아래에 있습니다."

msgid "page.donation.payment.paypal.text3"
msgstr "비트코인(BTC)을 구매하려면 지침을 따르세요. 기부하고자 하는 금액만 구매하면 됩니다, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span> 비트코인을 우리의 지갑 주소로 전송하세요"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "PayPal 앱 또는 웹사이트에서 “Bitcoin” 페이지로 이동하세요. “Transfer” 버튼 %(transfer_icon)s을 누른 다음 “Send”를 누르세요."

msgid "page.donation.payment.paypal.text5"
msgstr "수신자로 우리의 비트코인 (BTC) 주소를 입력하고, 기부금 %(total)s을 보내기 위한 지침을 따르세요:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "신용 / 직불 카드 이용방법"

msgid "page.donation.credit_debit_card_our_page"
msgstr "신용 / 직불 카드 페이지를 통해 기부하기"

msgid "page.donation.donate_on_this_page"
msgstr "<a %(a_page)s>이 페이지</a>에서 %(amount)s 기부하기."

msgid "page.donation.stepbystep_below"
msgstr "아래의 단계별 가이드를 참조하세요."

msgid "page.donation.status_header"
msgstr "상태:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "확인 대기 중 (확인하려면 페이지를 새로 고침하세요)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "송금 대기 중 (페이지를 새로 고침하여 확인하세요)…"

msgid "page.donation.time_left_header"
msgstr "남은 시간:"

msgid "page.donation.might_want_to_cancel"
msgstr "(취소하고 새 기부를 생성하는 것이 낫습니다)"

msgid "page.donation.reset_timer"
msgstr "타이머를 재설정하려면 새 기부금을 만들기만 하면 됩니다."

msgid "page.donation.refresh_status"
msgstr "상태 업데이트"

msgid "page.donation.footer.issues_contact"
msgstr "문제가 발생하면 %(email)s로 연락해 주시고 가능한 한 많은 정보를 포함해 주세요 (예: 스크린샷)."

msgid "page.donation.expired_already_paid"
msgstr "이미 결제하셨다면:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "결제 확인 하는데에 최대 24시간 걸릴 수 있으니, 확인을 위해 페이지를 새로 고침하세요 (만료되었더라도)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "PayPal(페이팔)에서 PYUSD 코인 구매하기"

msgid "page.donation.pyusd.instructions"
msgstr "PYUSD 코인 (PayPal USD) 구매 지침을 따르세요."

msgid "page.donation.pyusd.more"
msgstr "거래 수수료를 고려하여 기부하는 금액(%(amount)s)보다 약간 더 많이 구매하는 것이 좋습니다(%(more)s 더 추천). 남은 금액은 그대로 유지됩니다."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "PayPal 앱 또는 웹사이트에서 “PYUSD” 페이지로 이동하세요. “전송” 버튼 %(icon)s을 누르고, “보내기”를 선택하세요."

msgid "page.donation.transfer_amount_to"
msgstr "%(amount)s을(를) %(account)s로 전송"

msgid "page.donation.cash_app_btc.step1"
msgstr "Cash App에서 비트코인 (BTC) 구매하기"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Cash App에서 “비트코인” (BTC) 페이지로 이동하세요."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "거래 수수료를 고려하여 기부하는 금액(%(amount)s)보다 약간 더 많이 구매하는 것이 좋습니다(%(more)s 더 추천). 남은 금액은 그대로 유지됩니다."

msgid "page.donation.cash_app_btc.step2"
msgstr "비트코인을 우리 주소로 전송하기"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "“비트코인 보내기” 버튼을 클릭하여 “출금”을 진행하세요. %(icon)s 아이콘을 눌러 달러에서 BTC로 전환하세요. 아래에 BTC 금액을 입력하고 “보내기”를 클릭하세요. 문제가 발생하면 <a %(help_video)s>이 비디오</a>를 참조하세요."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "소액 기부 (25달러 미만)의 경우 Rush 또는 Priority를 사용해야 할 수 있습니다."

msgid "page.donation.revolut.step1"
msgstr "Revolut에서 비트코인 (BTC) 구매하기"

msgid "page.donation.revolut.step1.text1"
msgstr "Revolut에서 “Crypto” 페이지로 이동하여 비트코인 (BTC)을 구매하세요."

msgid "page.donation.revolut.step1.more"
msgstr "거래 수수료를 고려하여 기부하는 금액(%(amount)s)보다 약간 더 많이 구매하는 것이 좋습니다(%(more)s 더 추천). 남은 금액은 그대로 유지됩니다."

msgid "page.donation.revolut.step2"
msgstr "비트코인을 우리 주소로 전송하기"

msgid "page.donation.revolut.step2.transfer"
msgstr "“비트코인 보내기” 버튼을 클릭하여 “출금”을 진행하세요. %(icon)s 아이콘을 눌러 유로에서 BTC로 전환하세요. 아래에 BTC 금액을 입력하고 “보내기”를 클릭하세요. 문제가 발생하면 <a %(help_video)s>이 비디오</a>를 참조하세요."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "꼭 유로나 달러가 <em>아닌</em> 아래의 BTC 액수를 사용하세요, 그렇지 않으면 정확한 액수를 받지 못해 멤버십을 자동으로 처리 할 수 없습니다."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "소액 기부 (25달러 미만)의 경우 Rush 또는 Priority를 사용해야 할 수 있습니다."

msgid "page.donation.payment2cc.cc2btc"
msgstr "다음의 “신용카드로 비트코인 구매” 익스프레스 서비스 중 하나를 사용하세요. 몇 분밖에 걸리지 않습니다:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "양식에 다음 세부 정보를 입력하십시오:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / 비트코인 금액:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "이 <span %(underline)s>정확한 금액</span>을 사용하십시오. 신용카드 수수료 때문에 총 비용이 더 높을 수 있습니다. 아쉽지만 소액의 경우, 우리의 할인을 초과할 수 있습니다."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / 비트코인 주소 (외부 지갑):"

msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s 가이드"

msgid "page.donation.crypto_standard"
msgstr "저희는 표준 버전의 암호화폐만 지원하며, 변종 네트워크나 변종 코인은 지원하지 않습니다. 코인에 따라 거래 확인이 최대 한 시간까지 걸릴 수 있습니다."

msgid "page.donation.crypto_qr_code_title"
msgstr "지불하려면 QR 코드를 스캔합니다"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Crypto 지갑 앱 으로이 QR 코드를 스캔하여 지불 세부 정보를 신속하게 작성하십시오."

msgid "page.donation.amazon.header"
msgstr "아마존 기프트 카드"

msgid "page.donation.amazon.form_instructions"
msgstr "아래 이메일 주소로 %(amount)s의 기프트 카드를 보내려면 <a %(a_form)s>공식 Amazon.com 양식</a>을 사용해 주세요."

msgid "page.donation.amazon.only_official"
msgstr "다른 방법의 기프트 카드는 받을 수 없습니다, <strong>Amazon.com의 공식 양식을 통해 직접 발송된 경우에만 가능합니다</strong>. 이 양식을 사용하지 않으면 기프트 카드를 반환할 수 없습니다."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "정확한 금액을 입력하세요: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "어떤 변형이나 추가도 하지 마세요."

msgid "page.donation.amazon.form_to"
msgstr "“받는 사람” 이메일 양식:"

msgid "page.donation.amazon.unique"
msgstr "계정에 고유한 정보로, 공유하지 마세요."

msgid "page.donation.amazon.only_use_once"
msgstr "한 번만 사용하세요."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "기프트 카드를 기다리는 중… (페이지를 새로 고침하여 확인하세요)"

msgid "page.donation.amazon.confirm_automated"
msgstr "기프트 카드를 보낸 후, 자동 시스템이 몇 분 내에 확인합니다. 작동하지 않으면 기프트 카드를 다시 보내보세요 (<a %(a_instr)s>설명서</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "그래도 작동하지 않으면 이메일을 보내주시면 Anna가 수동으로 검토할 것입니다 (수 일이 걸릴 수 있습니다). 이미 재전송을 시도한 경우 이를 언급하는 것을 잊지 마세요."

msgid "page.donation.amazon.example"
msgstr "예시:"

msgid "page.donate.strange_account"
msgstr "계정 이름이나 사진이 이상해 보일 수 있습니다. 걱정하지 마세요! 계정들은 우리의 기부 파트너들이 관리합니다. 우리의 계정이 해킹된 것은 아닙니다."

msgid "page.donation.payment.alipay.top_header"
msgstr "알리페이 지침"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>알리페이로 기부하기"

msgid "page.donation.payment.alipay.text1_new"
msgstr "<a %(a_account)s>이 알리페이 계정</a>을 사용하여 총 %(total)s을 기부하세요"

msgid "page.donation.page_blocked"
msgstr "기부 페이지가 차단돼있다면, 다른 인터넷 연결(예: VPN 또는 모바일 데이터)을 시도해 보세요."

msgid "page.donation.payment.alipay.error"
msgstr "불행히도, 알리페이는 종종 <strong>중국 본토</strong>에서만 접근 가능합니다. VPN을 일시적으로 비활성화하거나 중국 본토(또는 때때로 홍콩도 작동합니다)로의 VPN을 사용해야 할 수 있습니다."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>기부하기 (QR 코드 스캔 또는 버튼 누르기)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "<a %(a_href)s>QR 코드 기부 페이지</a>를 여세요."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "알리페이 앱으로 QR 코드를 스캔하거나 버튼을 눌러 알리페이 앱을 여세요."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "중국에 있기 때문에 페이지 로딩에 시간이 걸릴 수 있습니다. 양해 부탁드립니다."

msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat 사용 지침"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>WeChat으로 기부하기"

msgid "page.donation.payment.wechat.text1"
msgstr "총 %(total)s 금액을 <a %(a_account)s>의 WeChat 계정</a>을 사용하여 기부하세요"

msgid "page.donation.payment.pix.top_header"
msgstr "Pix 지침"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Pix로 기부하기"

msgid "page.donation.payment.pix.text1"
msgstr "<a %(a_account)s>이 Pix 계정</a>을 사용하여 총 %(total)s 을 기부하세요"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>영수증을 이메일로 보내주세요"

msgid "page.donation.footer.verification"
msgstr "영수증이나 스크린샷을 개인 인증 주소로 보내세요. 이 이메일 주소를 PayPal(페이팔) 기부에 사용하면 안됩니다."

msgid "page.donation.footer.text1"
msgstr "영수증이나 스크린샷을 개인 인증 주소로 보내세요:"

msgid "page.donation.footer.crypto_note"
msgstr "거래 중 암호화폐 환율이 변동된 경우, 원래 환율을 보여주는 영수증을 반드시 포함해 주세요. 암호화폐를 사용해 주셔서 정말 감사합니다. 저희에게 큰 도움이 됩니다!"

msgid "page.donation.footer.text2"
msgstr "영수증을 이메일로 보내신 후, 이 버튼을 클릭하여 Anna가 수동으로 검토할 수 있도록 하세요 (며칠이 걸릴 수 있습니다):"

msgid "page.donation.footer.button"
msgstr "네, 영수증을 이메일로 보냈습니다"

msgid "page.donation.footer.success"
msgstr "✅ 기부해 주셔서 감사합니다! Anna가 며칠 내에 수동으로 회원 자격을 활성화할 것입니다."

msgid "page.donation.footer.failure"
msgstr "❌ 문제가 발생했습니다. 페이지를 새로 고침하고 다시 시도하세요."

msgid "page.donation.stepbystep"
msgstr "단계별 가이드"

msgid "page.donation.crypto_dont_worry"
msgstr "일부 단계에서는 암호화폐 지갑을 언급하지만, 걱정하지 마세요. 암호화폐에 대해 배우지 않아도 가능합니다."

msgid "page.donation.hoodpay.step1"
msgstr "1. 이메일을 입력하세요."

msgid "page.donation.hoodpay.step2"
msgstr "2. 결제 방법을 선택하세요."

msgid "page.donation.hoodpay.step3"
msgstr "3. 결제 방법을 다시 선택하세요."

msgid "page.donation.hoodpay.step4"
msgstr "4. “Self hosted” 지갑을 선택하세요."

msgid "page.donation.hoodpay.step5"
msgstr "5. \"I confirm ownership\"을 클릭하세요."

msgid "page.donation.hoodpay.step6"
msgstr "이메일 영수증을 받으셔야 합니다. 영수증을 저희에게 보내주시면 가능한 한 빨리 기부를 확인해드리겠습니다."

msgid "page.donate.wait_new"
msgstr "우리에게 연락을 하기 전 최소 <span %(span_hours)s>24시간</span>을 기다려주세요(새로고침도 해보세요)."

msgid "page.donate.mistake"
msgstr "결제 중 실수를 했다면 환불은 불가능하지만, 문제를 해결하기 위해 최선을 다하겠습니다."

msgid "page.my_donations.title"
msgstr "내 기부"

msgid "page.my_donations.not_shown"
msgstr "기부 세부 사항은 공개되지 않습니다."

msgid "page.my_donations.no_donations"
msgstr "아직 기부하지 않았습니다. <a %(a_donate)s>첫 기부하기.</a>"

msgid "page.my_donations.make_another"
msgstr "추가 기부 하기."

msgid "page.downloaded.title"
msgstr "다운로드된 파일"

msgid "page.downloaded.fast_partner_star"
msgstr "빠른 파트너 서버에서 다운로드는 %(icon)s로 표시됩니다."

msgid "page.downloaded.twice"
msgstr "파일을 고속 다운로드와 저속 다운로드로 각기 두번 다운로드 한 경우, 두 번 표시됩니다."

msgid "page.downloaded.fast_download_time"
msgstr "지난 24시간 동안의 고속 다운로드는 일일 제한에 포함됩니다."

msgid "page.downloaded.times_utc"
msgstr "모든 시간은 UTC 기준입니다."

msgid "page.downloaded.not_public"
msgstr "다운로드된 파일은 공개되지 않습니다."

msgid "page.downloaded.no_files"
msgstr "아직 다운로드된 파일이 없습니다."

msgid "page.downloaded.last_18_hours"
msgstr "지난 18시간"

msgid "page.downloaded.earlier"
msgstr "이전"

msgid "page.account.logged_in.title"
msgstr "계정"

msgid "page.account.logged_out.title"
msgstr "로그인 / 회원가입"

msgid "page.account.logged_in.account_id"
msgstr "계정 ID: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "공개 프로필: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "비밀 키 (공유하지 마세요!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "보기"

msgid "page.account.logged_in.membership_has_some"
msgstr "회원권: <strong>%(tier_name)s</strong> %(until_date)s까지 <a %(a_extend)s>(연장)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "멤버십: <strong>없음</strong> <a %(a_become)s>(회원이 되세요)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "고속 다운로드 사용 (지난 24시간): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "어떤 다운로드?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "독점 Telegram 그룹: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "여기에서 가입하세요!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "우리 그룹에 가입하려면 <a %(a_tier)s>상위 등급</a>으로 업그레이드하세요."

msgid "page.account.logged_in.membership_upgrade"
msgstr "회원 등급을 상위 등급으로 업그레이드하는 데 관심이 있다면 %(email)s의 Anna에게 연락하세요."

msgid "page.contact.title"
msgstr "연락 이메일"

msgid "page.account.logged_in.membership_multiple"
msgstr "여러 멤버십을 결합할 수 있습니다 (24시간 동안의 고속 다운로드가 합산됩니다)."

msgid "layout.index.header.nav.public_profile"
msgstr "공개 프로필"

msgid "layout.index.header.nav.downloaded_files"
msgstr "다운로드된 파일"

msgid "layout.index.header.nav.my_donations"
msgstr "내 기부"

msgid "page.account.logged_in.logout.button"
msgstr "로그아웃"

msgid "page.account.logged_in.logout.success"
msgstr "✅ 로그아웃되었습니다. 다시 로그인하려면 페이지를 새로고침하세요."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ 문제가 발생했습니다. 페이지를 새로고침하고 다시 시도해 주세요."

msgid "page.account.logged_out.registered.text1"
msgstr "등록 성공! 귀하의 비밀 키는: <span %(span_key)s>%(key)s</span> 입니다"

msgid "page.account.logged_out.registered.text2"
msgstr "이 키를 신중하게 보관하세요. 잃어버리면 계정에 접근할 수 없게 됩니다."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>북마크.</strong> 이 페이지를 북마크하여 키를 검색할 수 있습니다.</li><li %(li_item)s><strong>다운로드.</strong> 키를 다운로드하려면 <a %(a_download)s>이 링크</a>를 클릭하세요.</li><li %(li_item)s><strong>비밀번호 관리자.</strong> 아래에 키를 입력할 때 비밀번호 관리자를 사용하여 키를 저장하세요.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "로그인하려면 비밀 키를 입력하세요:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "비밀 키"

msgid "page.account.logged_out.key_form.button"
msgstr "로그인"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "비밀 키가 유효하지 않습니다. 키를 확인하고 다시 시도하거나 아래에서 새 계정을 등록하세요."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "키를 잃어버리지 마세요!"

msgid "page.account.logged_out.register.header"
msgstr "아직 계정이 없으신가요?"

msgid "page.account.logged_out.register.button"
msgstr "새 계정 등록"

msgid "page.login.lost_key"
msgstr "키를 분실하셨다면, <a %(a_contact)s>저희에게 연락</a>하시고 가능한 많은 정보를 제공해 주세요."

msgid "page.login.lost_key_contact"
msgstr "저희에게 연락하려면 일시적으로 새 계정을 만들어야 할 수도 있습니다."

msgid "page.account.logged_out.old_email.button"
msgstr "이메일 기반의 오래된 계정인가요? <a %(a_open)s>여기에 이메일을 입력하세요</a>."

msgid "page.list.title"
msgstr "목록"

msgid "page.list.header.edit.link"
msgstr "편집"

msgid "page.list.edit.button"
msgstr "저장"

msgid "page.list.edit.success"
msgstr "✅ 저장되었습니다. 페이지를 새로 고침하세요."

msgid "page.list.edit.failure"
msgstr "❌ 문제가 발생했습니다. 다시 시도해 주세요."

msgid "page.list.by_and_date"
msgstr "%(by)s별 목록, <span %(span_time)s>%(time)s</span>에 생성됨"

msgid "page.list.empty"
msgstr "목록이 비어 있습니다."

msgid "page.list.new_item"
msgstr "파일을 찾아 \"목록\" 탭을 열어 이 목록에 추가하거나 제거하세요."

msgid "page.profile.title"
msgstr "프로필"

msgid "page.profile.not_found"
msgstr "프로필을 찾을 수 없습니다."

msgid "page.profile.header.edit"
msgstr "편집"

msgid "page.profile.change_display_name.text"
msgstr "표시명을 변경하세요. 식별자(“#” 뒤의 부분)는 변경할 수 없습니다."

msgid "page.profile.change_display_name.button"
msgstr "저장"

msgid "page.profile.change_display_name.success"
msgstr "✅ 저장되었습니다. 페이지를 새로 고침하세요."

msgid "page.profile.change_display_name.failure"
msgstr "❌ 문제가 발생했습니다. 다시 시도해 주세요."

msgid "page.profile.created_time"
msgstr "프로필 생성됨 <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "목록"

msgid "page.profile.lists.no_lists"
msgstr "아직 목록이 없습니다"

msgid "page.profile.lists.new_list"
msgstr "파일을 찾아 “목록” 탭을 열어 새 목록을 만드세요."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "저작권 개혁은 국가 안보에 필요합니다"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "요약: 중국의 LLM(DeepSeek 포함)은 세계 최대의 불법 도서 및 논문 아카이브인 제 아카이브에서 훈련되었습니다. 서방은 국가 안보 문제로 저작권 법을 개정해야 합니다."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "TorrentFreak의 동반 기사: <a %(torrentfreak)s>첫 번째</a>, <a %(torrentfreak_2)s>두 번째</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "얼마 전까지만 해도, “섀도우 라이브러리”는 사라져가고 있었습니다. 학술 논문의 대규모 불법 아카이브인 Sci-Hub는 소송으로 인해 새로운 작품을 수집하는 것을 중단했습니다. “Z-Library”, 세계 최대의 불법 도서관은 창작자들이 저작권 위반 혐의로 체포되었습니다. 그들은 체포를 피할 수 있었지만, 그들의 도서관은 여전히 위협을 받고 있습니다."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Z-Library가 폐쇄 위기에 처했을 때, 저는 이미 그들의 전체 도서관을 백업했고, 이를 수용할 플랫폼을 찾고 있었습니다. 그것이 안나의 아카이브를 시작하게 된 동기였습니다. 우리는 이후 세계 최대의 섀도우 라이브러리로 성장하여, 다양한 형식의 1억 4천만 개 이상의 저작권 텍스트를 호스팅하고 있습니다 — 도서, 학술 논문, 잡지, 신문 등을 포함하여."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "저와 제 팀은 이념가입니다. 우리는 이러한 파일을 보존하고 호스팅하는 것이 도덕적으로 옳다고 믿습니다. 전 세계의 도서관은 예산 삭감을 겪고 있으며, 인류의 유산을 기업에 맡길 수 없습니다."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "그 후 AI가 등장했습니다. 거의 모든 주요 LLM 개발 회사들이 우리 데이터를 훈련에 사용하기 위해 연락을 해왔습니다. 대부분의 (하지만 전부는 아닙니다!) 미국 기반 회사들은 우리의 작업이 불법임을 깨닫고 재고했습니다. 반면, 중국 기업들은 우리의 컬렉션을 열정적으로 받아들였으며, 그 합법성에 대해 별로 신경 쓰지 않는 것 같습니다. 이는 중국이 거의 모든 주요 국제 저작권 조약에 서명한 국가라는 점에서 주목할 만합니다."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "우리는 약 30개 회사에 고속 액세스를 제공했습니다. 대부분은 LLM 회사이며, 일부는 우리의 컬렉션을 재판매할 데이터 브로커입니다. 대부분은 중국 회사이지만, 미국, 유럽, 러시아, 한국, 일본의 회사들과도 협력했습니다. DeepSeek는 이전 버전이 우리의 컬렉션 일부로 훈련되었다고 <a %(arxiv)s>인정</a>했지만, 최신 모델에 대해서는 입을 다물고 있습니다 (아마도 최신 모델도 우리의 데이터를 사용했을 것입니다)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "서방이 LLM 및 궁극적으로 AGI 경쟁에서 앞서고 싶다면, 저작권에 대한 입장을 재고해야 하며, 그것도 빨리 해야 합니다. 우리의 도덕적 주장에 동의하든 안 하든, 이는 이제 경제적 문제, 심지어 국가 안보 문제로 발전하고 있습니다. 모든 권력 블록은 인공 초과학자, 초해커, 초군대를 구축하고 있습니다. 정보의 자유는 이러한 국가들에게 생존의 문제, 심지어 국가 안보의 문제가 되고 있습니다."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "우리 팀은 전 세계에서 왔으며, 특정한 정렬을 가지고 있지 않습니다. 하지만 우리는 강력한 저작권 법을 가진 국가들이 이 실존적 위협을 이용해 개혁할 것을 권장합니다. 그렇다면 어떻게 해야 할까요?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "우리의 첫 번째 권고는 간단합니다: 저작권 기간을 단축하십시오. 미국에서는 저작권이 저자의 사망 후 70년 동안 부여됩니다. 이는 터무니없습니다. 우리는 이를 특허와 일치시킬 수 있습니다. 특허는 제출 후 20년 동안 부여됩니다. 이는 책, 논문, 음악, 예술 및 기타 창작물의 저자들이 그들의 노력에 대해 충분히 보상받기에 충분한 시간이어야 합니다 (영화 각색과 같은 장기 프로젝트를 포함하여)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "그런 다음, 최소한 정책 입안자들은 대량 보존 및 텍스트 배포를 위한 예외를 포함해야 합니다. 개별 고객으로부터의 수익 손실이 주요 우려 사항이라면, 개인 수준의 배포는 금지된 상태로 남을 수 있습니다. 대신, LLM을 훈련하는 회사와 도서관 및 기타 아카이브와 같은 방대한 저장소를 관리할 수 있는 사람들은 이러한 예외의 적용을 받을 수 있습니다."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "일부 국가는 이미 이러한 버전을 시행하고 있습니다. TorrentFreak는 중국과 일본이 저작권 법에 AI 예외를 도입했다고 <a %(torrentfreak)s>보도</a>했습니다. 이것이 국제 조약과 어떻게 상호작용하는지는 불분명하지만, 이는 확실히 그들의 국내 기업들에게 보호를 제공하며, 우리가 목격한 것을 설명합니다."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "안나의 아카이브에 관해서는 — 우리는 도덕적 신념에 뿌리를 둔 지하 작업을 계속할 것입니다. 그러나 우리의 가장 큰 바람은 빛 속으로 나와 우리의 영향을 합법적으로 증대시키는 것입니다. 저작권을 개혁해 주십시오."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "TorrentFreak의 동반 기사 읽기: <a %(torrentfreak)s>첫 번째</a>, <a %(torrentfreak_2)s>두 번째</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "$10,000 ISBN 시각화 현상금 수상자"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "요약: $10,000 ISBN 시각화 현상금에 놀라운 제출물이 있었습니다."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "몇 달 전, 우리는 ISBN 공간을 보여주는 데이터의 최상의 시각화를 만들기 위해 <a %(all_isbns)s>$10,000 현상금</a>을 발표했습니다. 우리는 이미 보관한 파일과 보관하지 않은 파일을 보여주는 것에 중점을 두었고, 나중에 ISBN을 보유한 도서관의 수를 설명하는 데이터셋(희귀성의 척도)을 추가했습니다."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "우리는 반응에 압도되었습니다. 창의성이 넘쳤습니다. 참여해주신 모든 분들께 큰 감사를 드립니다: 여러분의 에너지와 열정은 전염성이 있습니다!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "궁극적으로 우리는 다음 질문에 답하고자 했습니다: <strong>세계에 존재하는 책은 무엇이며, 우리는 이미 얼마나 많은 책을 보관했으며, 다음에 어떤 책에 집중해야 할까요?</strong> 많은 사람들이 이 질문에 관심을 갖고 있는 것을 보니 기쁩니다."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "우리는 기본적인 시각화로 시작했습니다. 300kb 미만의 이 그림은 인류 역사상 가장 큰 완전 공개 \"도서 목록\"을 간결하게 나타냅니다:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "모든 ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "안나의 아카이브에 있는 파일"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC 데이터 유출"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost의 eBook 인덱스"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "구글 도서"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "굿리즈"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "인터넷 아카이브"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN 글로벌 출판사 등록"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "리비"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "러시아 국립 도서관"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "트랜토르 제국 도서관"

#, fuzzy
msgid "common.back"
msgstr "뒤로"

#, fuzzy
msgid "common.forward"
msgstr "앞으로"

#, fuzzy
msgid "common.last"
msgstr "마지막"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "자세한 내용은 <a %(all_isbns)s>원본 블로그 게시물</a>을 참조하세요."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "이를 개선하기 위해 도전 과제를 제시했습니다. 1등 상금 $6,000, 2등 $3,000, 3등 $1,000를 수여할 예정이었습니다. 압도적인 반응과 놀라운 제출물로 인해 상금 풀을 약간 늘려 3등을 네 명에게 각각 $500씩 수여하기로 결정했습니다. 수상자는 아래에 나와 있으며, 모든 제출물을 <a %(annas_archive)s>여기</a>에서 확인하거나 <a %(a_2025_01_isbn_visualization_files)s>통합 토렌트</a>를 다운로드하세요."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "1등 $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "이 <a %(phiresky_github)s>제출물</a> (<a %(annas_archive_note_2951)s>Gitlab 댓글</a>)은 우리가 원했던 모든 것 이상입니다! 특히 사용자 정의 셰이더까지 지원하는 놀라운 유연한 시각화 옵션과 포괄적인 프리셋 목록이 마음에 들었습니다. 또한 모든 것이 빠르고 매끄럽게 작동하며, 백엔드가 없는 간단한 구현, 영리한 미니맵, 그리고 <a %(phiresky_github)s>블로그 게시물</a>에 대한 광범위한 설명도 좋았습니다. 놀라운 작업이며, 잘 자격을 갖춘 수상자입니다!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "2등 $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "또 다른 놀라운 <a %(annas_archive_note_2913)s>제출물</a>입니다. 1등만큼 유연하지는 않지만, 실제로는 1등보다 매크로 수준의 시각화가 더 마음에 들었습니다(공간 채우기 곡선, 경계, 레이블링, 강조 표시, 팬 및 줌). Joe Davis의 <a %(annas_archive_note_2971)s>댓글</a>이 우리에게 공감을 주었습니다:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“완벽한 정사각형과 직사각형은 수학적으로 매력적이지만, 매핑 맥락에서 우수한 지역성을 제공하지는 않습니다. 이러한 힐버트 또는 고전적인 모턴의 비대칭성은 결함이 아니라 기능이라고 믿습니다. 이탈리아의 유명한 부츠 모양의 윤곽선이 지도에서 즉시 인식할 수 있게 만드는 것처럼, 이러한 곡선의 독특한 '특징'은 인지적 랜드마크로 작용할 수 있습니다. 이 독특함은 공간 기억을 향상시키고 사용자가 자신을 정위하는 데 도움을 줄 수 있으며, 특정 지역을 찾거나 패턴을 인식하는 것을 더 쉽게 만들 수 있습니다.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "그리고 여전히 시각화 및 렌더링을 위한 많은 옵션과 믿을 수 없을 정도로 매끄럽고 직관적인 UI가 있습니다. 확고한 2등입니다!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "3등 $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "이 <a %(annas_archive_note_2940)s>제출물</a>에서는 특히 비교 및 출판사 뷰와 같은 다양한 종류의 뷰가 마음에 들었습니다."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "3등 $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "가장 세련된 UI는 아니지만, 이 <a %(annas_archive_note_2917)s>제출물</a>은 많은 항목을 충족합니다. 특히 비교 기능이 마음에 들었습니다."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "3등 $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "첫 번째 <a %(annas_archive_note_2975)s>제출물</a>처럼, 이 제출물은 유연성으로 우리를 감명시켰습니다. 궁극적으로 훌륭한 시각화 도구를 만드는 것은 파워 유저에게는 최대한의 유연성을 제공하면서, 일반 사용자에게는 간단함을 유지하는 것입니다."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "3위 $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "현상금을 받게 된 마지막 <a %(annas_archive_note_2947)s>제출물</a>은 꽤 기본적이지만, 우리가 정말 좋아했던 독특한 기능들이 있습니다. 특정 ISBN을 얼마나 많은 데이터셋이 다루고 있는지를 인기도/신뢰도의 척도로 보여주는 방식이 마음에 들었습니다. 또한 비교를 위한 불투명도 슬라이더를 사용한 단순하지만 효과적인 방법도 정말 좋았습니다."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "주목할 만한 아이디어"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "우리가 특히 좋아했던 몇 가지 아이디어와 구현:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "희귀성을 위한 마천루"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "실시간 통계"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "주석, 그리고 실시간 통계"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "독특한 지도 보기와 필터"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "멋진 기본 색상 구성과 히트맵."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "빠른 비교를 위한 데이터셋의 쉬운 전환."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "예쁜 라벨."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "책의 수를 나타내는 스케일 바."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "마치 DJ처럼 데이터셋을 비교할 수 있는 많은 슬라이더."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "우리는 계속할 수 있지만, 여기서 멈추겠습니다. 모든 제출물을 <a %(annas_archive)s>여기</a>에서 확인하거나, 우리의 <a %(a_2025_01_isbn_visualization_files)s>통합 토렌트</a>를 다운로드하세요. 많은 제출물들이 있으며, 각각은 UI나 구현에서 독특한 관점을 제공합니다."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "최소한 1위 제출물은 우리의 메인 웹사이트에 통합할 것이며, 아마도 다른 것들도 포함할 것입니다. 또한 가장 희귀한 책을 식별하고 확인한 후 아카이브하는 과정을 어떻게 조직할지에 대해 생각하기 시작했습니다. 이 부분에 대한 더 많은 내용이 곧 나올 것입니다."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "참여해주신 모든 분들께 감사드립니다. 이렇게 많은 사람들이 관심을 가져주셔서 놀랍습니다."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "감사로 가득 찬 우리의 마음."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "모든 ISBN 시각화 — 2025-01-31까지 $10,000 현상금"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "이 그림은 인류 역사상 가장 큰 완전 공개된 “도서 목록”을 나타냅니다."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "이 그림은 1000×800 픽셀입니다. 각 픽셀은 2,500개의 ISBN을 나타냅니다. ISBN에 대한 파일이 있으면 해당 픽셀을 더 초록색으로 만듭니다. ISBN이 발행된 것을 알고 있지만 일치하는 파일이 없으면 더 빨간색으로 만듭니다."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "300kb 미만의 크기로, 이 그림은 인류 역사상 가장 큰 완전 공개된 “도서 목록”을 간결하게 나타냅니다 (전체 압축 시 수백 GB)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "또한 보여줍니다: 책을 백업하는 데 많은 작업이 남아 있습니다 (우리는 단지 16%만 가지고 있습니다)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "배경"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "안나의 아카이브가 인류의 모든 지식을 백업하는 임무를 어떻게 달성할 수 있을까요, 아직 남아 있는 책들을 모른다면? 우리는 할 일 목록이 필요합니다. 이를 매핑하는 한 가지 방법은 ISBN 번호를 통해서입니다. 1970년대 이후로 대부분의 국가에서 출판된 모든 책에 할당되었습니다."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "모든 ISBN 할당을 아는 중앙 권한은 없습니다. 대신, 이는 분산 시스템으로, 국가들이 번호 범위를 받고, 그 후 주요 출판사들에게 더 작은 범위를 할당하며, 소규모 출판사들에게 더 세분화할 수 있습니다. 마지막으로 개별 번호가 책에 할당됩니다."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "우리는 <a %(blog)s>2년 전</a> ISBNdb를 스크랩하면서 ISBN 매핑을 시작했습니다. 그 이후로, 우리는 <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby 등과 같은 많은 메타데이터 소스를 스크랩했습니다. 전체 목록은 안나의 아카이브의 “Datasets” 및 “Torrents” 페이지에서 찾을 수 있습니다. 우리는 이제 세계에서 가장 큰 완전 공개되고 쉽게 다운로드 가능한 도서 메타데이터 (따라서 ISBN) 컬렉션을 보유하고 있습니다."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "우리는 보존에 대해 <a %(blog)s>광범위하게 작성했습니다</a>, 그리고 왜 현재 중요한 시기에 있는지에 대해 설명했습니다. 이제 희귀하고, 주목받지 못하며, 독특하게 위험에 처한 책들을 식별하고 보존해야 합니다. 세계의 모든 책에 대한 좋은 메타데이터를 갖는 것이 도움이 됩니다."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "시각화"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "개요 이미지 외에도, 우리가 획득한 개별 데이터셋을 볼 수 있습니다. 드롭다운과 버튼을 사용하여 전환하세요."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "이 그림들에는 많은 흥미로운 패턴이 있습니다. 왜 선과 블록의 규칙성이 다른 규모에서 발생하는 것처럼 보일까요? 빈 영역은 무엇일까요? 왜 특정 데이터셋이 이렇게 밀집되어 있을까요? 이러한 질문들은 독자에게 연습 문제로 남겨두겠습니다."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 현상금"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "여기에는 탐구할 것이 많으므로, 위의 시각화를 개선하기 위한 현상금을 발표합니다. 대부분의 현상금과 달리, 이번 것은 시간 제한이 있습니다. 2025-01-31 (23:59 UTC)까지 오픈 소스 코드를 <a %(annas_archive)s>제출</a>해야 합니다."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "최고의 제출물은 $6,000, 2위는 $3,000, 3위는 $1,000를 받게 됩니다. 모든 현상금은 Monero (XMR)로 지급됩니다."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "아래는 최소 기준입니다. 기준을 충족하는 제출물이 없으면, 여전히 일부 현상금을 수여할 수 있지만, 이는 우리의 재량에 따릅니다."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "이 저장소를 포크하고, 이 블로그 게시물 HTML을 편집하세요 (우리의 Flask 백엔드 외에는 다른 백엔드는 허용되지 않습니다)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "위의 그림을 부드럽게 확대할 수 있도록 만들어, 개별 ISBN까지 확대할 수 있어야 합니다. ISBN을 클릭하면 안나의 아카이브에서 메타데이터 페이지나 검색으로 이동해야 합니다."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "여전히 모든 다른 데이터셋 간에 전환할 수 있어야 합니다."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "국가 범위와 출판사 범위는 마우스를 올리면 강조 표시되어야 합니다. 예를 들어 <a %(github_xlcnd_isbnlib)s>isbnlib의 data4info.py</a>를 국가 정보에 사용하고, 우리의 “isbngrp” 스크랩을 출판사에 사용하세요 (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "데스크톱과 모바일에서 잘 작동해야 합니다."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "보너스 포인트를 위해 (이것들은 단지 아이디어입니다 — 창의력을 마음껏 발휘하세요):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "사용성 및 외관에 대한 강력한 고려가 이루어질 것입니다."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "확대할 때 개별 ISBN에 대한 실제 metadata를 표시하세요, 예를 들어 제목과 저자."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "더 나은 공간 채우기 곡선. 예: 첫 번째 행에서 0에서 4로, 두 번째 행에서 5에서 9로 (역순으로) 재귀적으로 적용되는 지그재그."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "다양하거나 사용자 정의 가능한 색상 테마."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Datasets를 비교하기 위한 특별한 보기."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "다른 metadata와 잘 맞지 않는 문제를 디버그하는 방법 (예: 매우 다른 제목)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "ISBN 또는 범위에 대한 주석을 이미지에 추가하기."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "희귀하거나 위험에 처한 책을 식별하기 위한 어떤 휴리스틱."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "여러분이 생각해낼 수 있는 창의적인 아이디어!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "최소 기준에서 완전히 벗어나 완전히 다른 시각화를 할 수도 있습니다. 정말로 멋지다면, 그것은 보상 자격이 있지만, 우리의 재량에 따릅니다."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "<a %(annas_archive)s>이 이슈</a>에 댓글로 포크된 저장소, 병합 요청 또는 차이에 대한 링크를 게시하여 제출하세요."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "코드"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "이 이미지를 생성하는 코드와 다른 예제는 <a %(annas_archive)s>이 디렉토리</a>에서 찾을 수 있습니다."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "우리는 약 75MB(압축된)로 모든 필요한 ISBN 정보를 포함하는 압축 데이터 형식을 고안했습니다. 데이터 형식의 설명과 이를 생성하는 코드는 <a %(annas_archive_l1244_1319)s>여기</a>에서 찾을 수 있습니다. 보상을 위해 이를 사용할 필요는 없지만, 시작하기에 가장 편리한 형식일 것입니다. 우리의 metadata를 원하는 대로 변환할 수 있습니다 (단, 모든 코드는 오픈 소스여야 합니다)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "여러분이 무엇을 만들어낼지 기대됩니다. 행운을 빕니다!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "안나의 아카이브 컨테이너(AAC): 세계 최대의 섀도우 라이브러리에서 릴리스를 표준화하기"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "안나의 아카이브는 세계 최대의 섀도우 라이브러리가 되었으며, 우리의 릴리스를 표준화해야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>안나의 아카이브</a>는 세계에서 가장 큰 섀도우 라이브러리가 되었으며, 그 규모로는 유일하게 완전한 오픈 소스와 오픈 데이터로 운영되는 섀도우 라이브러리입니다. 아래는 우리의 Datasets 페이지에서 가져온 표입니다(약간 수정됨):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "우리는 세 가지 방법으로 이를 달성했습니다:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "기존의 오픈 데이터 섀도우 라이브러리(예: Sci-Hub 및 Library Genesis)를 미러링합니다."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "더 개방적이기를 원하지만 시간이나 자원이 부족한 섀도우 라이브러리를 지원합니다(예: Libgen 만화 컬렉션)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "대량 공유를 원하지 않는 라이브러리를 스크래핑합니다(예: Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "(2)와 (3)의 경우, 우리는 현재 상당한 양의 토렌트를 직접 관리하고 있습니다(수백 TB). 지금까지 우리는 이러한 컬렉션을 일회성으로 접근해왔으며, 이는 각 컬렉션에 맞춤형 인프라와 데이터 조직을 의미합니다. 이는 각 릴리스에 상당한 오버헤드를 추가하며, 점진적인 릴리스를 더 어렵게 만듭니다."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "그래서 우리는 우리의 릴리스를 표준화하기로 결정했습니다. 이것은 우리의 표준을 소개하는 기술 블로그 게시물입니다: <strong>안나의 아카이브 컨테이너</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "디자인 목표"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "우리의 주요 사용 사례는 다양한 기존 컬렉션에서 파일과 관련 metadata를 배포하는 것입니다. 우리의 가장 중요한 고려 사항은 다음과 같습니다:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "가능한 한 원본 형식에 가깝게 이질적인 파일과 metadata."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "소스 라이브러리에서의 이질적인 식별자, 또는 식별자의 부재."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "metadata와 파일 데이터의 별도 릴리스, 또는 metadata 전용 릴리스(예: 우리의 ISBNdb 릴리스)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "토렌트를 통한 배포, 그러나 다른 배포 방법의 가능성도 있음(예: IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "변경 불가능한 기록, 우리의 토렌트가 영원히 존재할 것이라고 가정해야 하기 때문입니다."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "점진적 릴리스 / 추가 가능한 릴리스."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "기계가 읽고 쓸 수 있으며, 특히 우리의 스택(Python, MySQL, ElasticSearch, Transmission, Debian, ext4)에 대해 편리하고 빠르게."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "어느 정도 쉬운 인간의 검사, 그러나 이는 기계 가독성보다 부차적입니다."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "표준 임대 시드박스로 우리의 컬렉션을 쉽게 시드할 수 있음."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Nginx와 같은 웹 서버에 의해 이진 데이터를 직접 제공할 수 있음."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "비목표:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "디스크에서 수동으로 탐색하기 쉽거나 사전 처리 없이 검색할 수 있는 파일에 대해서는 신경 쓰지 않습니다."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "기존 도서관 소프트웨어와 직접 호환되는 것에 대해서는 신경 쓰지 않습니다."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "누구나 토렌트를 사용하여 우리의 컬렉션을 시드하는 것이 쉬워야 하지만, 파일을 사용하려면 상당한 기술적 지식과 헌신이 필요하다고 기대합니다."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Anna’s Archive는 오픈 소스이기 때문에, 우리는 우리의 형식을 직접 사용하고자 합니다. 검색 인덱스를 새로 고칠 때, 우리는 공개적으로 사용 가능한 경로만 접근하여, 누구든지 우리의 도서관을 포크하여 빠르게 시작할 수 있도록 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "표준"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "궁극적으로, 우리는 비교적 간단한 표준에 정착했습니다. 이는 상당히 느슨하고, 비규범적이며, 진행 중인 작업입니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archive Container)는 <strong>metadata</strong>와 선택적으로 <strong>binary data</strong>로 구성된 단일 항목으로, 둘 다 변경할 수 없습니다. 이는 <strong>AACID</strong>라고 불리는 전역적으로 고유한 식별자를 가지고 있습니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Collection.</strong> 각 AAC는 컬렉션에 속하며, 이는 정의상 의미적으로 일관된 AAC 목록입니다. 메타데이터의 형식에 중대한 변경을 가하면 새로운 컬렉션을 만들어야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” and “files” collections.</strong> 관례상, “기록”과 “파일”을 다른 컬렉션으로 출시하는 것이 종종 편리합니다. 이렇게 하면 스크래핑 속도에 따라 다른 일정으로 출시할 수 있습니다. “기록”은 도서 제목, 저자, ISBN 등과 같은 정보를 포함하는 메타데이터 전용 컬렉션이며, “파일”은 실제 파일 자체(pdf, epub)를 포함하는 컬렉션입니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> AACID의 형식은 다음과 같습니다: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. 예를 들어, 우리가 출시한 실제 AACID는 <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>입니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: 컬렉션 이름으로, ASCII 문자, 숫자 및 밑줄을 포함할 수 있습니다(그러나 이중 밑줄은 포함할 수 없습니다)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: 항상 UTC로 된 ISO 8601의 짧은 버전, 예: <code>20220723T194746Z</code>. 이 숫자는 각 릴리스마다 단조롭게 증가해야 하며, 그 정확한 의미는 컬렉션마다 다를 수 있습니다. 스크래핑 시간이나 ID 생성 시간을 사용하는 것을 권장합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: 컬렉션별 식별자, 예: Z-Library ID. 생략하거나 잘라낼 수 있습니다. AACID가 150자를 초과할 경우 생략하거나 잘라내야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: ASCII로 압축된 UUID, 예: base57을 사용. 현재 우리는 <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python 라이브러리를 사용합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID range.</strong> AACID에는 단조롭게 증가하는 타임스탬프가 포함되어 있으므로, 이를 사용하여 특정 컬렉션 내의 범위를 나타낼 수 있습니다. 우리는 이 형식을 사용합니다: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, 여기서 타임스탬프는 포함됩니다. 이는 ISO 8601 표기법과 일치합니다. 범위는 연속적이며, 겹칠 수 있지만, 겹치는 경우 해당 컬렉션에서 이전에 출시된 것과 동일한 기록을 포함해야 합니다(AAC는 변경할 수 없기 때문입니다). 누락된 기록은 허용되지 않습니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata file.</strong> 메타데이터 파일은 특정 컬렉션의 AAC 범위에 대한 메타데이터를 포함합니다. 이러한 파일은 다음과 같은 속성을 가집니다:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "파일 이름은 <code style=\"color: red\">annas_archive_meta__</code>로 시작하고 <code>.jsonl.zstd</code>로 끝나는 AACID 범위여야 합니다. 예를 들어, 우리의 릴리스 중 하나는 다음과 같이 불립니다:<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "파일 확장자가 나타내듯이, 파일 유형은 <a %(jsonlines)s>JSON Lines</a>이며 <a %(zstd)s>Zstandard</a>로 압축됩니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "각 JSON 객체는 최상위 수준에서 다음 필드를 포함해야 합니다: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (선택 사항). 다른 필드는 허용되지 않습니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code>는 컬렉션의 의미에 따른 임의의 메타데이터입니다. 컬렉션 내에서 의미적으로 일관되어야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code>는 선택 사항이며, 해당 이진 데이터가 포함된 이진 데이터 폴더의 이름입니다. 해당 폴더 내의 이진 데이터 파일명은 기록의 AACID입니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "<code style=\"color: red\">annas_archive_meta__</code> 접두사는 귀하의 기관 이름에 맞게 조정할 수 있습니다. 예: <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>이진 데이터 폴더.</strong> 특정 컬렉션의 AAC 범위에 대한 이진 데이터가 포함된 폴더입니다. 이 폴더는 다음과 같은 속성을 가집니다:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "디렉토리 이름은 <code style=\"color: green\">annas_archive_data__</code>로 시작하고 접미사가 없는 AACID 범위여야 합니다. 예를 들어, 실제 릴리스 중 하나는 다음과 같은 디렉토리를 가지고 있습니다:<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "디렉토리는 지정된 범위 내의 모든 AAC에 대한 데이터 파일을 포함해야 합니다. 각 데이터 파일은 파일명으로 AACID를 가져야 하며 확장자는 없어야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "이 폴더의 크기를 어느 정도 관리 가능하게 유지하는 것이 좋습니다. 예를 들어, 각 폴더가 100GB-1TB를 넘지 않도록 하십시오. 그러나 이 권장 사항은 시간이 지남에 따라 변경될 수 있습니다."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>토렌트.</strong> metadata 파일과 이진 데이터 폴더는 토렌트로 묶일 수 있으며, metadata 파일당 하나의 토렌트 또는 이진 데이터 폴더당 하나의 토렌트가 가능합니다. 토렌트는 원래 파일/디렉토리 이름에 <code>.torrent</code> 접미사를 추가하여 파일명을 가져야 합니다."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "예시"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "최근 Z-Library 릴리스를 예로 들어 보겠습니다. 이는 두 개의 컬렉션으로 구성되어 있습니다: “<span style=\"background: #fffaa3\">zlib3_records</span>”와 “<span style=\"background: #ffd6fe\">zlib3_files</span>”. 이를 통해 실제 도서 파일에서 metadata 기록을 별도로 스크랩하고 릴리스할 수 있습니다. 따라서 우리는 metadata 파일이 포함된 두 개의 토렌트를 릴리스했습니다:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "우리는 또한 “<span style=\"background: #ffd6fe\">zlib3_files</span>” 컬렉션에 대해서만 이진 데이터 폴더가 포함된 여러 개의 토렌트를 릴리스했으며, 총 62개입니다:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "<code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code>를 실행하면 내부 내용을 확인할 수 있습니다:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "이 경우, 이는 Z-Library에서 보고한 도서의 metadata입니다. 최상위 수준에서는 “aacid”와 “metadata”만 있으며, 해당 이진 데이터가 없기 때문에 “data_folder”는 없습니다. AACID는 기본 ID로 “22430000”을 포함하고 있으며, 이는 “zlibrary_id”에서 가져온 것임을 알 수 있습니다. 이 컬렉션의 다른 AAC도 동일한 구조를 가질 것으로 예상됩니다."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "이제 <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>를 실행해 보겠습니다:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "이는 훨씬 작은 AAC metadata이지만, 이 AAC의 대부분은 다른 곳에 있는 이진 파일에 위치해 있습니다! 결국, 이번에는 “data_folder”가 있으므로 해당 이진 데이터가 <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>에 위치할 것으로 예상할 수 있습니다. “metadata”는 “zlibrary_id”를 포함하고 있으므로, 이를 “zlib_records” 컬렉션의 해당 AAC와 쉽게 연결할 수 있습니다. 여러 가지 방법으로 연결할 수 있으며, 예를 들어 AACID를 통해 연결할 수 있습니다 — 표준에서는 이를 규정하지 않습니다."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "“metadata” 필드가 자체적으로 JSON일 필요는 없다는 점에 유의하십시오. XML 또는 다른 데이터 형식을 포함하는 문자열일 수도 있습니다. 심지어 관련 이진 블롭에 metadata 정보를 저장할 수도 있습니다. 예를 들어, 데이터가 많은 경우에 말입니다."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "결론"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "이 표준을 통해 릴리스를 점진적으로 수행할 수 있으며, 새로운 데이터 소스를 더 쉽게 추가할 수 있습니다. 우리는 이미 몇 가지 흥미로운 릴리스를 준비 중에 있습니다!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "다른 섀도우 라이브러리가 우리의 컬렉션을 미러링하기가 더 쉬워지기를 바랍니다. 결국, 우리의 목표는 인류의 지식과 문화를 영원히 보존하는 것이므로, 중복이 많을수록 좋습니다."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "안나의 업데이트: 완전한 오픈 소스 아카이브, ElasticSearch, 300GB 이상의 도서 표지"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "저희는 Anna’s Archive와 함께 좋은 대안을 제공하기 위해 밤낮으로 노력해왔습니다. 최근에 달성한 몇 가지 성과를 소개합니다."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Z-Library가 중단되고 (추정되는) 설립자들이 체포되면서, 저희는 Anna’s Archive와 함께 좋은 대안을 제공하기 위해 밤낮으로 노력해왔습니다 (여기서는 링크를 제공하지 않지만, 구글에서 검색할 수 있습니다). 최근에 달성한 몇 가지 성과를 소개합니다."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna’s Archive는 완전히 오픈 소스입니다."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "저희는 정보가 자유로워야 한다고 믿으며, 저희의 코드도 예외가 아닙니다. 저희는 모든 코드를 개인적으로 호스팅하는 Gitlab 인스턴스에 공개했습니다: <a %(annas_archive)s>Anna’s Software</a>. 또한, 작업을 조직하기 위해 이슈 트래커를 사용합니다. 개발에 참여하고 싶다면, 이곳이 시작하기에 좋은 장소입니다."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "저희가 작업 중인 것들을 맛보기로 보여드리자면, 최근 클라이언트 측 성능 개선 작업이 있습니다. 아직 페이지 매김을 구현하지 않았기 때문에, 종종 100-200개의 결과가 있는 매우 긴 검색 페이지를 반환하곤 했습니다. 검색 결과를 너무 빨리 잘라내고 싶지 않았지만, 이는 일부 기기를 느리게 만들었습니다. 이를 위해 작은 트릭을 구현했습니다: 대부분의 검색 결과를 HTML 주석으로 감싸고 (<code><!-- --></code>), 결과가 표시되어야 할 때 이를 감지하는 작은 자바스크립트를 작성했습니다. 그 순간 주석을 해제합니다:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"가상화\"가 23줄로 구현되어, 복잡한 라이브러리가 필요 없습니다! 이는 제한된 시간과 해결해야 할 실제 문제가 있을 때 얻게 되는 빠르고 실용적인 코드의 예입니다. 이제 느린 기기에서도 검색이 잘 작동한다고 보고되었습니다!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "또 다른 큰 노력은 데이터베이스 구축을 자동화하는 것이었습니다. 처음 시작할 때는 여러 소스를 무작위로 모았습니다. 이제는 이를 업데이트하고 싶어서, 두 개의 Library Genesis 포크에서 새로운 metadata를 다운로드하고 통합하는 여러 스크립트를 작성했습니다. 목표는 저희 아카이브에 유용할 뿐만 아니라, 섀도우 라이브러리 metadata를 가지고 놀고 싶은 사람들에게도 쉽게 만드는 것입니다. 목표는 모든 종류의 흥미로운 metadata가 포함된 Jupyter 노트북을 만들어, <a %(blog)s>ISBN의 몇 퍼센트가 영원히 보존되는지</a>와 같은 연구를 더 많이 할 수 있도록 하는 것입니다."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "마지막으로, 기부 시스템을 개편했습니다. 이제 신용카드를 사용하여 암호화폐 지갑에 직접 돈을 입금할 수 있으며, 암호화폐에 대해 잘 알 필요가 없습니다. 실제로 얼마나 잘 작동하는지 계속 모니터링할 것이지만, 이는 큰 진전입니다."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "ElasticSearch로 전환"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "저희의 <a %(annas_archive)s>티켓</a> 중 하나는 검색 시스템과 관련된 여러 문제를 포함하고 있었습니다. 모든 데이터를 MySQL에 보유하고 있었기 때문에 MySQL의 전체 텍스트 검색을 사용했습니다. 하지만 한계가 있었습니다:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "일부 쿼리는 너무 오래 걸려서 모든 열린 연결을 차지할 정도였습니다."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "기본적으로 MySQL은 최소 단어 길이를 가지고 있거나, 인덱스가 매우 커질 수 있습니다. 사람들이 “Ben Hur”를 검색할 수 없다고 보고했습니다."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "검색은 메모리에 완전히 로드되었을 때만 다소 빨랐으며, 이를 실행하기 위해 더 비싼 기계를 구입하고 시작 시 인덱스를 미리 로드하는 몇 가지 명령이 필요했습니다."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "새로운 기능을 쉽게 확장할 수 없었습니다. 예를 들어, 더 나은 <a %(wikipedia_cjk_characters)s>비공백 언어에 대한 토큰화</a>, 필터링/페이싱, 정렬, \"혹시 이런 뜻인가요\" 제안, 자동 완성 등을 구축할 수 없었습니다."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "여러 전문가와 논의한 후, ElasticSearch로 결정했습니다. 완벽하지는 않았습니다 (기본 “혹시 이런 뜻인가요” 제안과 자동 완성 기능은 별로였습니다), 하지만 전반적으로 MySQL보다 검색에 훨씬 나았습니다. 여전히 <a %(youtube)s>중요한 데이터</a>에 사용하는 것에 대해 <a %(elastic_co)s>조금은</a> 꺼려지지만, 전반적으로 전환에 만족하고 있습니다."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "현재로서는 훨씬 빠른 검색, 더 나은 언어 지원, 더 나은 관련성 정렬, 다양한 정렬 옵션, 언어/책 유형/파일 유형에 대한 필터링을 구현했습니다. 어떻게 작동하는지 궁금하다면, <a %(annas_archive_l140)s>한번</a> <a %(annas_archive_l1115)s>살펴보세요</a>. 꽤 접근성이 좋지만, 더 많은 주석이 필요할 수도 있습니다…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB 이상의 책 표지 공개"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "마지막으로, 작은 발표를 하게 되어 기쁩니다. Libgen.rs 포크를 운영하는 사람들과 협력하여 모든 책 표지를 토렌트와 IPFS를 통해 공유하고 있습니다. 이는 표지를 보는 부하를 더 많은 기기에 분산시키고, 더 잘 보존할 것입니다. 많은 경우 (하지만 모두는 아님), 책 표지가 파일 자체에 포함되어 있어, 이는 일종의 “파생 데이터”입니다. 하지만 IPFS에 있는 것은 Anna’s Archive와 다양한 Library Genesis 포크의 일상 운영에 여전히 매우 유용합니다."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "평소와 같이, 이 릴리스는 Pirate Library Mirror에서 찾을 수 있습니다 (편집: <a %(wikipedia_annas_archive)s>Anna’s Archive</a>로 이동). 여기서는 링크를 제공하지 않지만, 쉽게 찾을 수 있습니다."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "이제 Z-Library에 대한 괜찮은 대안을 마련했으니, 조금은 속도를 늦출 수 있기를 바랍니다. 이 작업량은 특히 지속 가능하지 않습니다. 프로그래밍, 서버 운영, 보존 작업에 관심이 있다면, 꼭 저희에게 연락해 주세요. 아직 <a %(annas_archive)s>할 일이 많습니다</a>. 관심과 지원에 감사드립니다."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "안나의 아카이브는 세계 최대의 만화 섀도우 라이브러리(95TB)를 백업했습니다 — 여러분도 시드하는 데 도움을 줄 수 있습니다"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "세계 최대의 만화책 섀도우 라이브러리는 단일 실패 지점을 가지고 있었습니다.. 오늘까지는요."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Hacker News에서 토론하기</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "세계 최대의 만화책 섀도우 라이브러리는 특정 Library Genesis 포크인 Libgen.li의 것일 가능성이 큽니다. 그 사이트를 운영하는 한 관리자는 2백만 개 이상의 파일을 수집하여 총 95TB가 넘는 엄청난 만화 컬렉션을 모았습니다. 그러나 다른 Library Genesis 컬렉션과 달리, 이 컬렉션은 토렌트를 통해 대량으로 제공되지 않았습니다. 그의 느린 개인 서버를 통해서만 개별적으로 접근할 수 있었습니다 — 단일 실패 지점이었죠. 오늘까지는요!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "이 게시물에서는 이 컬렉션에 대해 더 많은 정보를 제공하고, 이 작업을 지원하기 위한 모금 활동에 대해 알려드리겠습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>바바라 고든 박사는 도서관의 평범한 세계에서 자신을 잃으려 합니다…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen 포크"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "먼저, 배경 설명을 드리겠습니다. 여러분은 아마도 Library Genesis의 방대한 도서 컬렉션을 알고 계실 것입니다. Library Genesis 자원봉사자들이 잡지와 표준 문서의 상당한 컬렉션, Sci-Hub의 전체 백업(창립자 알렉산드라 엘바캰과 협력하여), 그리고 정말로 방대한 만화 컬렉션을 만든 다른 프로젝트도 있다는 것을 아는 사람은 적습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "어느 시점에서 Library Genesis 미러의 다른 운영자들이 각자의 길을 가게 되었고, 이는 현재 여러 다른 \"포크\"가 존재하게 된 상황을 초래했습니다. 이들은 여전히 Library Genesis라는 이름을 사용하고 있습니다. Libgen.li 포크는 이 만화 컬렉션과 상당한 잡지 컬렉션을 독특하게 보유하고 있습니다(저희도 이 작업을 진행 중입니다)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "협력"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "그 크기 때문에 이 컬렉션은 오랫동안 저희의 위시리스트에 있었습니다. 그래서 Z-Library 백업에 성공한 후, 이 컬렉션에 주목하게 되었습니다. 처음에는 직접 스크래핑을 했는데, 그들의 서버 상태가 좋지 않아 꽤 도전적이었습니다. 이 방법으로 약 15TB를 얻었지만, 진행 속도는 느렸습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "다행히도, 저희는 도서관 운영자와 연락이 닿았고, 그가 모든 데이터를 직접 보내주기로 동의했습니다. 이 방법은 훨씬 빨랐습니다. 그러나 모든 데이터를 전송하고 처리하는 데 여전히 반년 이상이 걸렸고, 디스크 손상으로 인해 모든 데이터를 잃을 뻔했습니다. 이는 처음부터 다시 시작해야 했을 것입니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "이 경험을 통해 저희는 이 데이터를 가능한 한 빨리 공개하여 널리 미러링하는 것이 중요하다고 믿게 되었습니다. 저희는 이 컬렉션을 영원히 잃을 수 있는 한두 번의 불운한 사건에서 멀지 않습니다!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "컬렉션"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "빠르게 진행하는 것은 컬렉션이 약간 정리되지 않았다는 것을 의미합니다… 한번 살펴보겠습니다. 파일 시스템이 있다고 상상해 보세요(실제로는 토렌트로 나누고 있습니다):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "첫 번째 디렉토리, <code>/repository</code>,는 이 중 더 구조화된 부분입니다. 이 디렉토리는 소위 \"천 디렉토리\"를 포함하고 있습니다: 각각 천 개의 파일이 있는 디렉토리로, 데이터베이스에서 순차적으로 번호가 매겨져 있습니다. 디렉토리 <code>0</code>는 comic_id 0–999의 파일을 포함하고 있으며, 계속됩니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "이는 Library Genesis가 소설 및 비소설 컬렉션에 사용해 온 동일한 방식입니다. 아이디어는 모든 \"천 디렉토리\"가 가득 차면 자동으로 토렌트로 변환된다는 것입니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "그러나 Libgen.li 운영자는 이 컬렉션에 대해 토렌트를 만들지 않았고, 그래서 천 디렉토리는 불편해졌고 \"정렬되지 않은 디렉토리\"로 대체되었습니다. 이들은 <code>/comics0</code>에서 <code>/comics4</code>까지입니다. 이들은 모두 파일을 수집하는 데는 아마도 의미가 있었겠지만, 지금은 우리에게는 별로 의미가 없습니다. 다행히도, metadata는 여전히 이 모든 파일을 직접 참조하고 있으므로, 디스크에 저장된 조직 방식은 실제로 중요하지 않습니다!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "metadata는 MySQL 데이터베이스 형태로 제공됩니다. 이는 Libgen.li 웹사이트에서 직접 다운로드할 수 있지만, 저희는 MD5 해시가 포함된 자체 테이블과 함께 토렌트로도 제공할 것입니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "분석"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "95TB의 데이터를 저장 클러스터에 덤프받으면, 그 안에 무엇이 있는지 이해하려고 노력하게 됩니다… 우리는 중복을 제거하는 등의 방법으로 크기를 줄일 수 있는지 분석을 했습니다. 다음은 우리의 발견 사항입니다:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "의미적으로 중복된 데이터(같은 책의 다른 스캔본)는 이론적으로 필터링할 수 있지만, 이는 까다롭습니다. 만화책을 수동으로 살펴보았을 때 너무 많은 오탐지를 발견했습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "MD5로만 중복된 데이터가 일부 있으며, 이는 상대적으로 낭비적이지만, 이를 필터링해도 약 1% in의 절감만 가능합니다. 이 규모에서는 여전히 약 1TB이지만, 이 규모에서는 1TB가 크게 중요하지 않습니다. 이 과정에서 데이터를 실수로 파괴할 위험을 감수하고 싶지 않습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "만화책을 기반으로 한 영화와 같은 비도서 데이터도 다수 발견했습니다. 이는 이미 다른 방법으로 널리 이용 가능하기 때문에 낭비적으로 보입니다. 그러나 컴퓨터에서 출시된 <em>인터랙티브 만화책</em>을 누군가가 녹화하여 영화로 저장한 경우도 있어 영화 파일을 단순히 필터링할 수 없다는 것을 깨달았습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "결국, 컬렉션에서 삭제할 수 있는 것은 몇 퍼센트만 절약할 수 있습니다. 그러다 우리는 데이터 수집가라는 것을 기억했고, 이를 미러링할 사람들도 데이터 수집가라는 것을 기억했습니다. 그래서, “삭제라니, 무슨 말씀이세요?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "따라서 우리는 전체, 수정되지 않은 컬렉션을 여러분께 제공합니다. 데이터가 많지만, 많은 사람들이 이를 시드하는 데 관심을 가질 것이라고 희망합니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "모금 활동"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "우리는 이 데이터를 몇 개의 큰 덩어리로 공개하고 있습니다. 첫 번째 토렌트는 <code>/comics0</code>이며, 하나의 거대한 12TB .tar 파일로 묶었습니다. 이는 수많은 작은 파일보다 하드 드라이브와 토렌트 소프트웨어에 더 좋습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "이 릴리스의 일환으로 모금 활동을 진행하고 있습니다. 이 컬렉션의 운영 및 계약 비용을 충당하고, 지속적이고 미래의 프로젝트를 가능하게 하기 위해 $20,000를 모금하려고 합니다. 우리는 몇 가지 <em>대규모</em> 프로젝트를 진행 중입니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>내 기부로 누구를 지원하게 되나요?</em> 간단히 말해: 우리는 인류의 모든 지식과 문화를 백업하고 쉽게 접근할 수 있도록 하고 있습니다. 우리의 모든 코드와 데이터는 오픈 소스이며, 완전히 자원봉사로 운영되는 프로젝트입니다. 지금까지 125TB의 책을 저장했습니다 (Libgen과 Scihub의 기존 토렌트 외에도). 궁극적으로 우리는 세계의 모든 책을 찾고, 스캔하고, 백업할 수 있도록 하는 플라이휠을 구축하고 있습니다. 우리의 마스터 플랜에 대해서는 나중에 포스팅할 예정입니다. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "12개월 “Amazing Archivist” 멤버십($780)을 기부하시면 <strong>“토렌트 입양”</strong>을 하실 수 있습니다. 이는 여러분의 사용자 이름이나 메시지를 토렌트 파일명에 넣는 것을 의미합니다!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "<a %(wikipedia_annas_archive)s>Anna’s Archive</a>에 가서 “기부” 버튼을 클릭하여 기부할 수 있습니다. 우리는 또한 소프트웨어 엔지니어, 보안 연구원, 익명의 상인 전문가, 번역가와 같은 더 많은 자원봉사자를 찾고 있습니다. 호스팅 서비스를 제공하여 우리를 지원할 수도 있습니다. 그리고 물론, 우리의 토렌트를 시드해 주세요!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "이미 우리를 관대하게 지원해 주신 모든 분들께 감사드립니다! 여러분은 진정한 변화를 만들고 있습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "지금까지 공개된 토렌트는 다음과 같습니다 (나머지는 아직 처리 중입니다):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "모든 토렌트는 <a %(wikipedia_annas_archive)s>Anna’s Archive</a>의 “Datasets”에서 찾을 수 있습니다 (직접 링크하지 않으므로 Reddit, Twitter 등에서 이 블로그로의 링크가 제거되지 않습니다). 거기서 Tor 웹사이트로의 링크를 따라가세요."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "다음은 무엇인가요?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "많은 토렌트는 장기 보존에 좋지만, 일상적인 접근에는 그다지 적합하지 않습니다. 우리는 이 모든 데이터를 웹에 올리기 위해 호스팅 파트너와 협력할 것입니다 (Anna’s Archive는 직접 호스팅하지 않기 때문입니다). 물론 Anna’s Archive에서 이러한 다운로드 링크를 찾을 수 있을 것입니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "우리는 또한 모든 사람들이 이 데이터를 활용하도록 초대합니다! 더 나은 분석, 중복 제거, IPFS에 올리기, AI 모델 훈련 등 다양한 작업을 도와주세요. 이 모든 것은 여러분의 것이며, 여러분이 무엇을 할지 기대됩니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "마지막으로, 앞서 말했듯이, 우리는 여전히 몇 가지 대규모 릴리스를 준비 중입니다 (<em>누군가</em>가 <em>우연히</em> 특정 ACS4 데이터베이스의 덤프를 보내주신다면, 어디서 찾을 수 있는지 아시죠…), 그리고 세계의 모든 책을 백업하기 위한 플라이휠을 구축하고 있습니다."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "계속 지켜봐 주세요, 이제 막 시작했으니까요."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "해적 도서관 미러에 3배의 새로운 책이 추가되었습니다 (+24TB, 380만 권의 책)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "해적 도서관 미러의 원래 릴리스에서 (수정: <a %(wikipedia_annas_archive)s>안나의 아카이브</a>로 이동), 우리는 Z-Library라는 대규모 불법 도서 컬렉션의 미러를 만들었습니다. 참고로, 원래 블로그 게시물에서 이렇게 썼습니다:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library는 인기 있는 (그리고 불법적인) 도서관입니다. 그들은 Library Genesis 컬렉션을 가져와 쉽게 검색할 수 있도록 만들었습니다. 게다가, 그들은 다양한 혜택을 제공하여 기여하는 사용자들을 유도함으로써 새로운 책 기여를 매우 효과적으로 받고 있습니다. 현재 그들은 이러한 새로운 책들을 Library Genesis에 다시 기여하지 않고 있습니다. 그리고 Library Genesis와 달리, 그들은 그들의 컬렉션을 쉽게 미러링할 수 없게 하여 널리 보존되는 것을 방지하고 있습니다. 이는 그들의 비즈니스 모델에 중요합니다. 왜냐하면 그들은 하루에 10권 이상의 책에 접근하기 위해 돈을 청구하기 때문입니다."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "우리는 불법적인 책 컬렉션에 대량 접근하는 데에 요금을 부과하는 것에 대해 도덕적 판단을 하지 않습니다. Z-Library가 지식에 대한 접근을 확장하고 더 많은 책을 소싱하는 데 성공했다는 것은 의심의 여지가 없습니다. 우리는 단지 우리의 역할을 수행하고자 합니다: 이 개인 컬렉션의 장기적인 보존을 보장하는 것입니다."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "그 컬렉션은 2021년 중반으로 거슬러 올라갑니다. 그동안 Z-Library는 놀라운 속도로 성장해 왔습니다: 약 380만 권의 새로운 책이 추가되었습니다. 물론 중복된 것도 있지만, 대부분은 실제로 새로운 책이거나 이전에 제출된 책의 더 높은 품질의 스캔본인 것 같습니다. 이는 주로 Z-Library의 자원봉사자 관리자 수가 증가하고, 중복 제거가 가능한 대량 업로드 시스템 덕분입니다. 우리는 그들의 이러한 성과를 축하하고 싶습니다."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "우리는 마지막 미러 이후부터 2022년 8월까지 Z-Library에 추가된 모든 책을 확보하게 되어 기쁩니다. 또한 처음에 놓친 책들도 다시 수집했습니다. 전체적으로 이 새로운 컬렉션은 약 24TB로, 이전 것보다 훨씬 큽니다 (7TB). 우리의 미러는 이제 총 31TB입니다. 다시 한 번, Library Genesis와 중복 제거를 했습니다. 그 컬렉션에 대한 토렌트가 이미 제공되고 있기 때문입니다."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "새로운 컬렉션을 확인하려면 해적 도서관 미러로 이동하세요 (수정: <a %(wikipedia_annas_archive)s>안나의 아카이브</a>로 이동). 파일이 어떻게 구성되어 있는지, 지난번 이후로 무엇이 변경되었는지에 대한 더 많은 정보가 있습니다. 여기는 단순한 블로그 웹사이트로 불법 자료를 호스팅하지 않기 때문에 여기서 링크를 제공하지는 않습니다."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "물론, 시딩도 우리를 돕는 좋은 방법입니다. 이전 토렌트 세트를 시딩해 주신 모든 분들께 감사드립니다. 긍정적인 반응에 감사드리며, 이 독특한 방식으로 지식과 문화를 보존하는 것에 관심을 가져주시는 많은 분들이 계셔서 기쁩니다."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "해적 아카이비스트가 되는 방법"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "첫 번째 도전은 놀라운 것일 수 있습니다. 그것은 기술적인 문제도, 법적인 문제도 아닙니다. 그것은 심리적인 문제입니다."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "들어가기 전에, 해적 도서관 미러에 대한 두 가지 업데이트가 있습니다 (수정: <a %(wikipedia_annas_archive)s>안나의 아카이브</a>로 이동):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "우리는 매우 관대한 기부를 받았습니다. 첫 번째는 Library Genesis의 창립자인 \"bookwarrior\"를 지원해 온 익명의 개인으로부터 받은 1만 달러였습니다. 이 기부를 주선해 주신 bookwarrior에게 특별히 감사드립니다. 두 번째는 우리의 마지막 릴리스 이후 연락을 주신 또 다른 익명의 기부자로부터 받은 1만 달러였습니다. 또한 여러 소액 기부도 있었습니다. 여러분의 관대한 지원에 진심으로 감사드립니다. 이를 지원할 흥미로운 새로운 프로젝트들이 준비 중이니 계속 지켜봐 주세요."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "두 번째 릴리스의 크기 때문에 기술적인 어려움이 있었지만, 현재 우리의 토렌트는 업로드되고 시딩 중입니다. 또한 익명의 개인으로부터 매우 고속 서버에 우리의 컬렉션을 시딩하겠다는 관대한 제안을 받았으며, 그들의 기계에 특별 업로드를 진행하고 있습니다. 이후 컬렉션을 다운로드하는 모든 사람들은 속도의 큰 향상을 경험할 수 있을 것입니다."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "디지털 보존의 <em>이유</em>에 대해, 특히 해적 아카이브에 대해 전체 책을 쓸 수 있지만, 잘 모르는 분들을 위해 간단한 입문서를 제공하겠습니다. 세계는 그 어느 때보다 더 많은 지식과 문화를 생산하고 있지만, 그 어느 때보다 더 많은 것이 사라지고 있습니다. 인류는 주로 학술 출판사, 스트리밍 서비스, 소셜 미디어 회사와 같은 기업에 이 유산을 맡기고 있으며, 이들은 종종 훌륭한 관리자로 입증되지 않았습니다. 다큐멘터리 Digital Amnesia나 Jason Scott의 강연을 확인해 보세요."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "일부 기관은 가능한 한 많은 것을 아카이브하는 데 좋은 성과를 내고 있지만, 법에 구속됩니다. 해적으로서 우리는 저작권 집행이나 기타 제한 때문에 그들이 손댈 수 없는 컬렉션을 아카이브할 수 있는 독특한 위치에 있습니다. 또한 전 세계적으로 컬렉션을 여러 번 미러링하여 적절한 보존 가능성을 높일 수 있습니다."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "지금은 지적 재산권의 장단점, 법을 어기는 것의 도덕성, 검열에 대한 고찰, 지식과 문화에 대한 접근 문제에 대한 논의는 하지 않겠습니다. 이 모든 것을 제쳐두고, <em>방법</em>에 대해 알아보겠습니다. 우리 팀이 어떻게 해적 아카이브가 되었는지, 그리고 그 과정에서 배운 교훈을 공유하겠습니다. 이 여정을 시작할 때 많은 도전이 있으며, 그 중 일부를 통해 여러분을 도울 수 있기를 바랍니다."

#, fuzzy
msgid "blog.how-to.community"
msgstr "커뮤니티"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "첫 번째 도전은 놀라운 것일 수 있습니다. 그것은 기술적인 문제도, 법적인 문제도 아닙니다. 그것은 심리적인 문제입니다: 그림자 속에서 이 작업을 수행하는 것은 매우 외로울 수 있습니다. 당신이 무엇을 계획하고 있는지, 그리고 당신의 위협 모델에 따라 매우 조심해야 할 수도 있습니다. 한쪽 끝에는 Sci-Hub의 창립자인 Alexandra Elbakyan*과 같은 사람들이 있습니다. 그녀는 자신의 활동에 대해 매우 공개적입니다. 그러나 그녀는 현재 서구 국가를 방문할 경우 체포될 위험이 높으며, 수십 년의 징역형을 받을 수 있습니다. 그것이 당신이 감수할 수 있는 위험입니까? 우리는 스펙트럼의 다른 쪽 끝에 있습니다. 흔적을 남기지 않기 위해 매우 조심하고, 강력한 운영 보안을 유지하고 있습니다."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* HN에서 \"ynno\"가 언급한 바와 같이, Alexandra는 처음에는 알려지기를 원하지 않았습니다: \"그녀의 서버는 PHP의 자세한 오류 메시지를 방출하도록 설정되어 있었고, 이는 /home/<USER>" 따라서 이 작업을 위해 사용하는 컴퓨터에서 무작위 사용자 이름을 사용하세요. 잘못 구성할 경우를 대비하여."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "그러나 그러한 비밀은 심리적인 비용을 수반합니다. 대부분의 사람들은 자신이 하는 일에 대해 인정받는 것을 좋아하지만, 실제 생활에서는 이에 대한 공로를 인정받을 수 없습니다. 친구들이 당신이 무엇을 하고 있었는지 물어보는 것과 같은 간단한 일조차도 도전이 될 수 있습니다 (어느 시점에서는 \"내 NAS / 홈랩을 만지고 있었어\"라는 말이 지겨워집니다)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "이것이 바로 커뮤니티를 찾는 것이 중요한 이유입니다. 깊이 신뢰할 수 있는 매우 가까운 친구들에게 비밀을 털어놓음으로써 일부 운영 보안을 포기할 수 있습니다. 그럼에도 불구하고 그들이 당국에 이메일을 제출해야 하거나, 그들의 장치가 다른 방식으로 손상될 경우를 대비하여 어떤 것도 서면으로 남기지 않도록 주의하세요."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "더 나은 방법은 동료 해적을 찾는 것입니다. 가까운 친구들이 당신과 함께 하기를 원한다면, 좋습니다! 그렇지 않다면 온라인에서 다른 사람들을 찾을 수 있을지도 모릅니다. 안타깝게도 이것은 여전히 틈새 커뮤니티입니다. 지금까지 우리는 이 분야에서 활동하는 소수의 사람들만을 발견했습니다. 좋은 출발점은 Library Genesis 포럼과 r/DataHoarder인 것 같습니다. Archive Team도 같은 생각을 가진 사람들이 있지만, 그들은 법의 테두리 안에서 활동합니다 (비록 법의 회색 영역에 있을지라도). 전통적인 \"warez\"와 해적 장면에도 비슷한 생각을 가진 사람들이 있습니다."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "우리는 커뮤니티를 육성하고 아이디어를 탐구하는 방법에 대한 아이디어를 열어두고 있습니다. Twitter나 Reddit에서 메시지를 보내주세요. 아마도 어떤 형태의 포럼이나 채팅 그룹을 주최할 수 있을 것입니다. 하나의 도전은 일반적인 플랫폼을 사용할 때 쉽게 검열될 수 있다는 점이므로, 우리가 직접 호스팅해야 할 것입니다. 이러한 논의를 완전히 공개적으로 진행할 것인지 (더 많은 잠재적 참여) 아니면 비공개로 진행할 것인지 (우리가 그들을 스크랩하려고 한다는 것을 잠재적 \"대상\"이 알지 못하게 하는 것) 사이의 균형도 있습니다. 이에 대해 생각해 보겠습니다. 이에 관심이 있다면 알려주세요!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "프로젝트"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "우리가 프로젝트를 수행할 때, 몇 가지 단계가 있습니다:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "도메인 선택 / 철학: 대략 어디에 집중하고 싶으며, 왜 그런가요? 당신의 독특한 열정, 기술, 상황을 어떻게 활용할 수 있나요?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "대상 선택: 어떤 특정 컬렉션을 미러링할 것인가요?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata 스크래핑: 실제로 (종종 훨씬 더 큰) 파일을 다운로드하지 않고 파일에 대한 정보를 카탈로그화합니다."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "데이터 선택: Metadata를 기반으로 지금 아카이브할 데이터 중 가장 관련성이 높은 것을 좁힙니다. 모든 것이 될 수도 있지만, 종종 공간과 대역폭을 절약할 수 있는 합리적인 방법이 있습니다."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "데이터 스크래핑: 실제로 데이터를 가져옵니다."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "토렌트로 패키징하여 배포하고, 어딘가에 발표하며, 사람들이 이를 퍼뜨리도록 합니다."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "이들은 완전히 독립적인 단계가 아니며, 종종 나중 단계에서 얻은 통찰이 이전 단계로 돌아가게 만듭니다. 예를 들어, metadata 스크래핑 중에 선택한 대상이 당신의 기술 수준을 넘어서는 방어 메커니즘(예: IP 차단)을 가지고 있다는 것을 깨달을 수 있으며, 그러면 다른 대상을 찾아야 합니다."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. 도메인 선택 / 철학"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "구해야 할 지식과 문화 유산은 부족하지 않으며, 이는 압도적일 수 있습니다. 그렇기 때문에 잠시 시간을 내어 자신의 기여가 무엇일 수 있는지 생각해 보는 것이 종종 유용합니다."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "모두가 이에 대해 생각하는 방식이 다르지만, 스스로에게 물어볼 수 있는 몇 가지 질문이 있습니다:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "왜 이것에 관심이 있습니까? 무엇에 열정을 가지고 있습니까? 만약 우리가 각자 자신이 특별히 관심을 가지는 것들을 아카이브하는 사람들을 모을 수 있다면, 많은 것을 커버할 수 있을 것입니다! 당신은 당신의 열정에 대해 평균적인 사람보다 훨씬 더 많이 알 것입니다. 예를 들어, 어떤 데이터가 저장할 가치가 있는지, 최고의 컬렉션과 온라인 커뮤니티는 무엇인지 등입니다."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "어떤 기술을 가지고 있으며, 이를 어떻게 활용할 수 있습니까? 예를 들어, 온라인 보안 전문가라면, 안전한 대상을 위한 IP 차단을 무력화하는 방법을 찾을 수 있습니다. 커뮤니티를 조직하는 데 뛰어나다면, 목표를 중심으로 사람들을 모을 수 있을 것입니다. 이 과정에서 좋은 운영 보안을 유지하기 위해서라도 프로그래밍을 조금 아는 것이 유용합니다."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "이 작업에 얼마나 많은 시간을 할애할 수 있습니까? 우리의 조언은 작게 시작하고 익숙해지면서 더 큰 프로젝트를 진행하는 것이지만, 이는 모든 것을 소비할 수 있습니다."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "어디에 집중하는 것이 높은 레버리지 영역이 될까요? 해적 아카이빙에 X 시간을 쓸 예정이라면, 어떻게 하면 가장 큰 \"가성비\"를 얻을 수 있을까요?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "이것에 대해 생각하는 독특한 방법은 무엇입니까? 다른 사람들이 놓쳤을 수 있는 흥미로운 아이디어나 접근 방식을 가지고 있을 수 있습니다."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "우리의 경우, 우리는 과학의 장기 보존에 특히 관심이 있었습니다. 우리는 Library Genesis에 대해 알고 있었고, 그것이 토렌트를 사용하여 여러 번 완전히 미러링되었다는 것을 알고 있었습니다. 우리는 그 아이디어를 좋아했습니다. 그러던 어느 날, 우리 중 한 명이 Library Genesis에서 과학 교과서를 찾으려 했지만 찾을 수 없었고, 그것이 얼마나 완전한지 의문을 품게 되었습니다. 우리는 그 교과서를 온라인에서 검색했고, 다른 곳에서 찾았으며, 이것이 우리의 프로젝트의 씨앗을 심었습니다. Z-Library에 대해 알기 전에도, 우리는 모든 책을 수동으로 수집하려는 것이 아니라 기존 컬렉션을 미러링하고 이를 Library Genesis에 다시 기여하는 아이디어를 가지고 있었습니다."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. 대상 선택"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "그래서, 우리가 주목하고 있는 영역이 있다면, 이제 어떤 특정 컬렉션을 미러링할까요? 좋은 대상을 만드는 몇 가지 요소가 있습니다:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "대규모"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "독특함: 다른 프로젝트에 의해 이미 잘 커버되지 않은 것."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "접근성: metadata와 데이터를 스크래핑하지 못하도록 많은 보호 계층을 사용하지 않는 것."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "특별한 통찰력: 이 대상에 대해 특별한 정보를 가지고 있거나, 이 컬렉션에 특별히 접근할 수 있거나, 그들의 방어를 무력화하는 방법을 알아낸 경우. 이는 필수는 아니지만(우리의 다가오는 프로젝트는 특별한 것을 하지 않습니다), 확실히 도움이 됩니다!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "우리가 Library Genesis 외의 웹사이트에서 과학 교과서를 찾았을 때, 우리는 그것들이 어떻게 인터넷에 올라왔는지 알아내려고 했습니다. 그러다 Z-Library를 발견했고, 대부분의 책이 처음에는 거기에 나타나지 않지만 결국에는 거기에 도달한다는 것을 깨달았습니다. 우리는 그것의 Library Genesis와의 관계, (재정적) 인센티브 구조 및 우수한 사용자 인터페이스에 대해 알게 되었으며, 이는 훨씬 더 완전한 컬렉션을 만들었습니다. 우리는 초기 metadata와 데이터 스크래핑을 수행했으며, 많은 프록시 서버에 대한 우리 멤버 중 한 명의 특별한 접근을 활용하여 그들의 IP 다운로드 제한을 우회할 수 있음을 깨달았습니다."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "다양한 대상을 탐색하는 동안, VPN과 일회용 이메일 주소를 사용하여 흔적을 숨기는 것이 이미 중요합니다. 이에 대해서는 나중에 더 이야기하겠습니다."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata 스크래핑"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "웹사이트에서 metadata를 스크래핑하는 것에 대해 좀 더 기술적으로 접근해 보겠습니다. 우리는 Python 스크립트, 때로는 curl, 그리고 MySQL 데이터베이스를 사용하여 결과를 저장하는 방식으로 간단하게 유지했습니다. 복잡한 웹사이트를 매핑할 수 있는 고급 스크래핑 소프트웨어는 사용하지 않았습니다. 지금까지는 ID를 열거하고 HTML을 파싱하여 한두 종류의 페이지만 스크래핑하면 되었기 때문입니다. 쉽게 열거할 수 없는 페이지가 있다면, 모든 페이지를 찾으려는 적절한 크롤러가 필요할 수 있습니다."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "전체 웹사이트를 스크래핑하기 전에, 수동으로 잠시 시도해 보세요. 몇십 페이지를 직접 살펴보면서 어떻게 작동하는지 감을 잡아보세요. 이렇게 하면 이미 IP 차단이나 다른 흥미로운 행동을 경험할 수 있습니다. 데이터 스크래핑도 마찬가지입니다. 이 대상에 깊이 들어가기 전에 데이터를 효과적으로 다운로드할 수 있는지 확인하세요."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "제한을 우회하기 위해 시도할 수 있는 몇 가지 방법이 있습니다. 동일한 데이터를 호스팅하지만 동일한 제한이 없는 다른 IP 주소나 서버가 있습니까? 제한이 없는 API 엔드포인트가 있는지 확인해 보세요. 다운로드 속도가 어느 정도일 때 IP가 차단되며, 차단 기간은 얼마나 됩니까? 차단되지 않고 속도가 제한되는 경우는 어떻습니까? 사용자 계정을 생성하면 어떻게 변합니까? HTTP/2를 사용하여 연결을 유지할 수 있으며, 이를 통해 페이지 요청 속도가 증가합니까? 여러 파일을 한 번에 나열하는 페이지가 있으며, 그곳에 나열된 정보가 충분합니까?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "저장하고 싶은 항목은 다음과 같습니다:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "제목"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "파일명 / 위치"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: 내부 ID일 수 있지만, ISBN이나 DOI 같은 ID도 유용합니다."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "크기: 필요한 디스크 공간을 계산하기 위해."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "해시 (md5, sha1): 파일을 올바르게 다운로드했는지 확인하기 위해."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "추가/수정 날짜: 나중에 돌아와서 이전에 다운로드하지 않은 파일을 다운로드할 수 있도록 (하지만 종종 ID나 해시를 사용할 수도 있습니다)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "설명, 카테고리, 태그, 저자, 언어 등."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "우리는 일반적으로 두 단계로 진행합니다. 먼저 미가공 HTML 파일을 다운로드하여 보통 MySQL에 직접 저장합니다 (많은 작은 파일을 피하기 위해, 아래에서 더 설명합니다). 그런 다음 별도의 단계에서 이러한 HTML 파일을 실제 MySQL 테이블로 파싱합니다. 이렇게 하면 파싱 코드에서 실수를 발견했을 때 모든 것을 처음부터 다시 다운로드할 필요가 없으며, 새로운 코드로 HTML 파일을 다시 처리할 수 있습니다. 또한 처리 단계를 병렬화하여 시간을 절약할 수 있으며, 스크래핑이 실행되는 동안 처리 코드를 작성할 수 있습니다."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "마지막으로, 일부 대상의 경우 metadata 스크래핑이 전부일 수 있습니다. 제대로 보존되지 않은 거대한 metadata 컬렉션이 존재합니다."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. 데이터 선택"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "종종 metadata를 사용하여 다운로드할 데이터의 합리적인 하위 집합을 파악할 수 있습니다. 결국 모든 데이터를 다운로드하고 싶더라도, 가장 중요한 항목을 우선적으로 다운로드하는 것이 유용할 수 있습니다. 탐지되어 방어가 강화되거나, 더 많은 디스크를 구매해야 하거나, 모든 것을 다운로드하기 전에 삶에서 다른 일이 발생할 수 있기 때문입니다."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "예를 들어, 컬렉션에는 동일한 기본 리소스(책이나 영화와 같은)의 여러 판이 있을 수 있으며, 그 중 하나가 최고의 품질로 표시될 수 있습니다. 이러한 판을 먼저 저장하는 것이 합리적입니다. 결국 모든 판을 저장하고 싶을 수 있습니다. 경우에 따라 metadata가 잘못 태그되었거나, 판 사이에 알려지지 않은 절충점이 있을 수 있기 때문입니다 (예를 들어, \"최고의 판\"이 대부분의 면에서 최고일 수 있지만, 다른 면에서는 더 나쁠 수 있습니다. 예를 들어, 영화의 해상도가 높지만 자막이 없는 경우)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "metadata 데이터베이스를 검색하여 흥미로운 것을 찾을 수도 있습니다. 호스팅된 가장 큰 파일은 무엇이며, 왜 그렇게 큰가요? 가장 작은 파일은 무엇인가요? 특정 카테고리, 언어 등에 관해 흥미롭거나 예상치 못한 패턴이 있나요? 중복되거나 매우 유사한 제목이 있나요? 데이터가 추가된 시점에 패턴이 있나요, 예를 들어 한 날에 많은 파일이 한꺼번에 추가된 경우? 데이터셋을 다양한 방식으로 살펴보면 많은 것을 배울 수 있습니다."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "우리의 경우, Z-Library 책을 Library Genesis의 md5 해시와 중복 제거하여 많은 다운로드 시간과 디스크 공간을 절약했습니다. 그러나 이는 꽤 독특한 상황입니다. 대부분의 경우, 동료 해적들에 의해 이미 제대로 보존된 파일이 어떤 것인지에 대한 포괄적인 데이터베이스는 없습니다. 이것 자체가 누군가에게는 큰 기회입니다. 음악과 영화와 같이 이미 토렌트 웹사이트에 널리 시드된 항목에 대한 정기적으로 업데이트된 개요가 있다면 좋을 것입니다. 따라서 해적 미러에 포함할 우선순위가 낮습니다."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. 데이터 스크래핑"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "이제 데이터를 대량으로 실제로 다운로드할 준비가 되었습니다. 앞서 언급했듯이, 이 시점에서는 이미 수동으로 여러 파일을 다운로드하여 대상의 행동과 제한을 더 잘 이해해야 합니다. 그러나 실제로 많은 파일을 한꺼번에 다운로드하게 되면 여전히 놀라운 일이 발생할 것입니다."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "여기서의 조언은 주로 간단하게 유지하는 것입니다. 우선 여러 파일을 다운로드하는 것부터 시작하세요. Python을 사용할 수 있으며, 그런 다음 여러 스레드로 확장할 수 있습니다. 하지만 때로는 데이터베이스에서 직접 Bash 파일을 생성하고 여러 터미널 창에서 여러 개를 실행하여 확장하는 것이 더 간단합니다. 여기서 언급할 가치가 있는 빠른 기술적 트릭은 MySQL에서 OUTFILE을 사용하는 것입니다. 이를 사용하려면 mysqld.cnf에서 \"secure_file_priv\"를 비활성화해야 하며, Linux를 사용하는 경우 AppArmor도 비활성화/무시해야 합니다."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "우리는 데이터를 간단한 하드 디스크에 저장합니다. 가지고 있는 것으로 시작하고 천천히 확장하세요. 수백 TB의 데이터를 저장하는 것을 생각하면 압도적일 수 있습니다. 그런 상황에 직면했다면 우선 좋은 하위 집합을 내놓고, 나머지를 저장하는 데 도움을 요청하는 공지를 하세요. 만약 직접 더 많은 하드 드라이브를 구입하고 싶다면, r/DataHoarder에는 좋은 거래를 얻는 데 도움이 되는 자원이 있습니다."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "너무 복잡한 파일 시스템에 대해 걱정하지 않도록 하세요. ZFS와 같은 것을 설정하는 토끼굴에 빠지기 쉽습니다. 그러나 알아야 할 기술적 세부 사항 중 하나는 많은 파일을 다루지 못하는 파일 시스템이 많다는 것입니다. 우리가 발견한 간단한 해결책은 여러 디렉토리를 만드는 것입니다. 예를 들어, 다른 ID 범위나 해시 접두사에 대해 말입니다."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "데이터를 다운로드한 후에는 가능한 경우 metadata의 해시를 사용하여 파일의 무결성을 확인하세요."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. 배포"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "데이터를 보유하고 있으므로 세계 최초의 해적 미러를 소유하게 됩니다(대부분의 경우). 여러 면에서 가장 어려운 부분은 끝났지만, 가장 위험한 부분은 아직 남아 있습니다. 지금까지는 스텔스 모드로 레이더 아래에서 비행해 왔습니다. 좋은 VPN을 사용하고, 어떤 양식에도 개인 정보를 입력하지 않고(당연히), 특별한 브라우저 세션(또는 다른 컴퓨터)을 사용하는 것만 하면 되었습니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "이제 데이터를 배포해야 합니다. 우리의 경우 처음에는 도서를 Library Genesis에 다시 기여하고자 했지만, 그 과정에서 (소설 대 비소설 정렬의) 어려움을 빠르게 발견했습니다. 그래서 Library Genesis 스타일의 토렌트를 사용하여 배포하기로 결정했습니다. 기존 프로젝트에 기여할 기회가 있다면 많은 시간을 절약할 수 있습니다. 그러나 현재 잘 조직된 해적 미러는 많지 않습니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "그래서 토렌트를 직접 배포하기로 결정했다고 가정해 봅시다. 파일을 작게 유지하여 다른 웹사이트에서 쉽게 미러링할 수 있도록 하세요. 그런 다음 익명성을 유지하면서 토렌트를 직접 시딩해야 합니다. VPN을 사용할 수 있으며(포트 포워딩 유무에 관계없이), 시드박스를 위해 혼합된 비트코인으로 결제할 수 있습니다. 이러한 용어 중 일부가 무엇을 의미하는지 모른다면, 위험의 균형을 이해하는 것이 중요하므로 읽어야 할 것이 많습니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "토렌트 파일 자체를 기존 토렌트 웹사이트에 호스팅할 수 있습니다. 우리의 경우, 철학을 명확하게 전파하고자 웹사이트를 실제로 호스팅하기로 결정했습니다. 당신도 비슷한 방식으로 할 수 있습니다(우리는 도메인과 호스팅을 위해 Njalla를 사용하며, 혼합된 비트코인으로 결제합니다). 그러나 우리에게 연락하여 당신의 토렌트를 호스팅하도록 요청해도 좋습니다. 이 아이디어가 인기를 끌면 시간이 지남에 따라 해적 미러의 포괄적인 인덱스를 구축하려고 합니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "VPN 선택에 관해서는 이미 많은 글이 작성되었으므로, 평판에 따라 선택하라는 일반적인 조언을 반복하겠습니다. 법원에서 테스트된 무로그 정책과 오랜 기간 동안 프라이버시를 보호한 기록이 있는 것이 가장 낮은 위험 옵션이라고 생각합니다. 모든 것을 올바르게 수행하더라도 위험을 완전히 없앨 수는 없습니다. 예를 들어, 토렌트를 시딩할 때, 매우 동기 부여된 국가 행위자는 VPN 서버의 들어오고 나가는 데이터 흐름을 살펴보고 당신이 누구인지 추론할 수 있습니다. 또는 단순히 실수를 저지를 수도 있습니다. 아마도 우리는 이미 실수를 했고, 다시 할 것입니다. 다행히도, 국가들은 <em>그렇게</em> 해적 행위에 대해 신경 쓰지 않습니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "각 프로젝트에 대해 동일한 신원을 사용하여 게시할지 여부를 결정해야 합니다. 동일한 이름을 계속 사용하면 이전 프로젝트의 운영 보안 실수가 다시 문제를 일으킬 수 있습니다. 그러나 다른 이름으로 게시하면 더 오래 지속되는 평판을 쌓을 수 없습니다. 우리는 처음부터 강력한 운영 보안을 갖추어 동일한 신원을 계속 사용할 수 있도록 했지만, 실수를 하거나 상황이 요구할 경우 다른 이름으로 게시하는 것을 주저하지 않을 것입니다."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "소문을 퍼뜨리는 것은 까다로울 수 있습니다. 말씀드렸듯이, 이것은 여전히 틈새 커뮤니티입니다. 우리는 원래 Reddit에 게시했지만, 실제로는 Hacker News에서 주목을 받았습니다. 현재로서는 몇 군데에 게시하고 무슨 일이 일어나는지 지켜보는 것을 추천합니다. 그리고 다시, 우리에게 연락하세요. 우리는 더 많은 해적 아카이브 노력을 알리는 것을 기쁘게 생각합니다."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "결론"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "이것이 새로 시작하는 해적 아카이브 관리자에게 도움이 되기를 바랍니다. 이 세계에 오신 것을 환영하며, 주저하지 말고 연락하세요. 가능한 한 많은 세계의 지식과 문화를 보존하고, 널리 미러링합시다."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "해적 도서관 미러 소개: Libgen에 없는 7TB의 도서 보존"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "이 프로젝트(편집: <a %(wikipedia_annas_archive)s>안나의 아카이브</a>로 이동)는 인간 지식의 보존과 해방에 기여하는 것을 목표로 합니다. 우리는 앞서간 위대한 이들의 발자취를 따라 작고 겸손한 기여를 합니다."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "이 프로젝트의 초점은 그 이름에 의해 설명됩니다:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>해적</strong> - 우리는 대부분의 국가에서 저작권법을 의도적으로 위반합니다. 이는 법적 기관이 할 수 없는 일을 할 수 있게 해줍니다: 책이 널리 미러링되도록 보장하는 것입니다."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>도서관</strong> - 대부분의 도서관처럼, 우리는 주로 책과 같은 서면 자료에 중점을 둡니다. 우리는 미래에 다른 유형의 미디어로 확장할 수도 있습니다."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>미러</strong> - 우리는 기존 라이브러리의 미러 역할만을 엄격히 수행합니다. 우리는 보존에 중점을 두며, 책을 쉽게 검색하고 다운로드할 수 있도록 하거나(접근성) 새로운 책을 기여하는 큰 커뮤니티를 육성하는 것(소싱)에 중점을 두지 않습니다."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "우리가 처음으로 미러링한 라이브러리는 Z-Library입니다. 이는 인기 있는 (그리고 불법적인) 라이브러리입니다. 그들은 Library Genesis 컬렉션을 가져와 쉽게 검색할 수 있도록 만들었습니다. 게다가, 그들은 다양한 혜택을 제공하여 기여하는 사용자들을 유도함으로써 새로운 책 기여를 매우 효과적으로 하고 있습니다. 현재 그들은 이러한 새로운 책들을 Library Genesis에 다시 기여하지 않습니다. 그리고 Library Genesis와 달리, 그들은 그들의 컬렉션을 쉽게 미러링할 수 있도록 하지 않으며, 이는 넓은 보존을 방해합니다. 이는 그들의 비즈니스 모델에 중요하며, 그들은 하루에 10권 이상의 책을 대량으로 접근하는 데에 대해 요금을 부과합니다."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "우리는 불법적인 책 컬렉션에 대량 접근하는 데에 요금을 부과하는 것에 대해 도덕적 판단을 하지 않습니다. Z-Library가 지식에 대한 접근을 확장하고 더 많은 책을 소싱하는 데 성공했다는 것은 의심의 여지가 없습니다. 우리는 단지 우리의 역할을 수행하고자 합니다: 이 개인 컬렉션의 장기적인 보존을 보장하는 것입니다."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "우리는 여러분이 우리의 토렌트를 다운로드하고 시딩하여 인류의 지식을 보존하고 해방하는 데 도움을 주시기를 초대합니다. 데이터가 어떻게 구성되어 있는지에 대한 자세한 정보는 프로젝트 페이지를 참조하세요."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "또한, 다음에 어떤 컬렉션을 미러링할지, 그리고 어떻게 진행할지에 대한 여러분의 아이디어를 기여해 주시기를 매우 환영합니다. 함께라면 많은 것을 이룰 수 있습니다. 이것은 수많은 기여 중 작은 부분일 뿐입니다. 여러분이 하시는 모든 일에 감사드립니다."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>우리는 이 블로그에서 파일에 대한 링크를 제공하지 않습니다. 직접 찾아보세요.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb 덤프, 또는 얼마나 많은 책이 영원히 보존되는가?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "섀도우 라이브러리에서 파일을 제대로 중복 제거한다면, 세계의 모든 책 중 몇 퍼센트를 보존했을까요?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "해적 도서관 미러(편집: <a %(wikipedia_annas_archive)s>Anna의 아카이브</a>로 이동)와 함께, 우리의 목표는 세계의 모든 책을 영원히 보존하는 것입니다.<sup>1</sup> 우리의 Z-Library 토렌트와 원래의 Library Genesis 토렌트 사이에서, 우리는 11,783,153개의 파일을 보유하고 있습니다. 하지만 그것이 실제로 얼마나 많은 것일까요? 만약 우리가 그 파일들을 제대로 중복 제거한다면, 세계의 모든 책 중 몇 퍼센트를 보존했을까요? 우리는 정말로 이런 것을 가지고 싶습니다:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "인류의 서면 유산의 10% o가 영원히 보존됨"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "퍼센트를 계산하려면 분모가 필요합니다: 지금까지 출판된 모든 책의 총 수.<sup>2</sup> 구글 북스의 종말 이전에, 프로젝트의 엔지니어인 Leonid Taycher는 <a %(booksearch_blogspot)s>이 숫자를 추정하려고 했습니다</a>. 그는 농담으로 129,864,880이라는 숫자를 제시했습니다(“적어도 일요일까지는”). 그는 세계의 모든 책의 통합 데이터베이스를 구축하여 이 숫자를 추정했습니다. 이를 위해 그는 다양한 Datasets를 모아 여러 방식으로 병합했습니다."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "간단히 말해, 세계의 모든 책을 카탈로그하려고 시도한 또 다른 사람이 있습니다: 고인이 된 디지털 활동가이자 Reddit 공동 창립자인 Aaron Swartz입니다.<sup>3</sup> 그는 <a %(youtube)s>Open Library를 시작했습니다</a> “지금까지 출판된 모든 책에 대한 웹 페이지 하나”라는 목표로, 다양한 출처의 데이터를 결합했습니다. 그는 학술 논문을 대량 다운로드한 혐의로 기소되어 자살에 이르게 되면서 그의 디지털 보존 작업에 대한 대가를 치렀습니다. 말할 필요도 없이, 이것이 우리 그룹이 가명을 사용하는 이유 중 하나이며, 우리가 매우 조심하는 이유입니다. Open Library는 여전히 Internet Archive의 사람들이 영웅적으로 운영하고 있으며, Aaron의 유산을 이어가고 있습니다. 우리는 이 게시물의 후반부에서 다시 이 주제로 돌아올 것입니다."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "구글 블로그 게시물에서 Taycher는 이 숫자를 추정하는 데 있어 몇 가지 도전 과제를 설명합니다. 첫째, 책이란 무엇인가? 몇 가지 가능한 정의가 있습니다:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>물리적 사본.</strong> 분명히 이것은 그다지 도움이 되지 않습니다, 왜냐하면 그것들은 단지 동일한 자료의 복제본일 뿐이기 때문입니다. 사람들이 책에 남긴 모든 주석을 보존할 수 있다면 멋질 것입니다, 예를 들어 페르마의 유명한 “여백에 낙서”처럼요. 하지만 아쉽게도, 그것은 아카이브 관리자의 꿈으로 남을 것입니다."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“작품”.</strong> 예를 들어 “해리 포터와 비밀의 방”이라는 논리적 개념으로, 다양한 번역과 재판을 포함하는 것입니다. 이것은 어느 정도 유용한 정의이지만, 무엇이 포함되는지 경계를 그리기가 어려울 수 있습니다. 예를 들어, 우리는 아마도 다른 번역을 보존하고 싶을 것입니다, 하지만 사소한 차이만 있는 재판은 그다지 중요하지 않을 수 있습니다."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“판”.</strong> 여기서는 책의 모든 고유한 버전을 셉니다. 표지나 서문이 다르면 다른 판으로 간주됩니다."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>파일.</strong> Library Genesis, Sci-Hub, 또는 Z-Library와 같은 섀도우 라이브러리와 작업할 때 추가적인 고려 사항이 있습니다. 동일한 판의 여러 스캔본이 있을 수 있습니다. 그리고 사람들은 OCR을 사용하여 텍스트를 스캔하거나 각도로 스캔된 페이지를 수정하여 기존 파일의 더 나은 버전을 만들 수 있습니다. 우리는 이러한 파일을 하나의 판으로만 계산하고 싶으며, 이는 좋은 metadata가 필요하거나 문서 유사성 측정을 사용한 중복 제거가 필요합니다."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“판”은 “책”이 무엇인지에 대한 가장 실용적인 정의로 보입니다. 편리하게도, 이 정의는 고유한 ISBN 번호를 할당하는 데에도 사용됩니다. ISBN, 즉 국제 표준 도서 번호는 국제 바코드 시스템(“국제 상품 번호”)과 통합되어 국제 상거래에 일반적으로 사용됩니다. 상점에서 책을 판매하려면 바코드가 필요하므로 ISBN을 받게 됩니다."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher의 블로그 게시물은 ISBN이 유용하지만 보편적이지 않다고 언급합니다, 왜냐하면 그것은 70년대 중반에야 실제로 채택되었고 전 세계적으로 사용되지 않았기 때문입니다. 그럼에도 불구하고, ISBN은 아마도 책 판의 가장 널리 사용되는 식별자일 것이며, 따라서 우리의 가장 좋은 출발점입니다. 세계의 모든 ISBN을 찾을 수 있다면, 우리는 여전히 보존해야 할 책의 유용한 목록을 얻을 수 있습니다."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "그래서, 우리는 데이터를 어디서 얻을까요? 전 세계의 모든 책 목록을 작성하려는 여러 기존 노력이 있습니다:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> 결국, 그들은 Google Books를 위해 이 연구를 수행했습니다. 그러나 그들의 metadata는 대량으로 접근할 수 없으며 스크랩하기도 어렵습니다."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> 앞서 언급했듯이, 이것이 그들의 전체 임무입니다. 그들은 협력 도서관과 국가 아카이브에서 방대한 양의 도서관 데이터를 수집했으며, 계속해서 그렇게 하고 있습니다. 그들은 또한 자원봉사 사서와 기술 팀이 기록을 중복 제거하고 다양한 metadata로 태그를 붙이려고 노력하고 있습니다. 무엇보다도, 그들의 데이터셋은 완전히 개방되어 있습니다. <a %(openlibrary)s>다운로드</a>할 수 있습니다."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> 이는 비영리 OCLC가 운영하는 웹사이트로, 도서관 관리 시스템을 판매합니다. 그들은 많은 도서관에서 책 metadata를 수집하여 WorldCat 웹사이트를 통해 제공하고 있습니다. 그러나 그들은 이 데이터를 판매하여 수익을 창출하기 때문에 대량 다운로드는 불가능합니다. 특정 도서관과 협력하여 다운로드 가능한 제한된 대량 데이터셋을 제공하기도 합니다."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> 이것이 이 블로그 게시물의 주제입니다. ISBNdb는 다양한 웹사이트에서 책 metadata, 특히 가격 데이터를 스크랩하여 이를 서적 판매자에게 판매합니다. 서적 판매자들은 이를 통해 시장의 다른 책들과 비교하여 가격을 책정할 수 있습니다. 요즘 ISBN은 상당히 보편적이기 때문에, 그들은 효과적으로 \"모든 책에 대한 웹 페이지\"를 구축했습니다."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>다양한 개별 도서관 시스템 및 아카이브.</strong> 위의 어느 곳에서도 인덱싱 및 집계되지 않은 도서관 및 아카이브가 있으며, 종종 자금 부족이나 다른 이유로 Open Library, OCLC, Google 등과 데이터를 공유하지 않으려 합니다. 이러한 곳들 중 많은 곳은 인터넷을 통해 접근 가능한 디지털 기록을 가지고 있으며, 종종 잘 보호되지 않기 때문에, 이상한 도서관 시스템에 대해 배우며 재미를 느끼고 싶다면 좋은 출발점이 될 수 있습니다."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "이 게시물에서는 이전 Z-Library 릴리스에 비해 작은 릴리스를 발표하게 되어 기쁩니다. 우리는 ISBNdb의 대부분을 스크랩하여 해적 도서관 미러 웹사이트에서 토렌트로 데이터를 제공했습니다 (편집: <a %(wikipedia_annas_archive)s>Anna’s Archive</a>로 이동; 여기서 직접 링크하지는 않겠습니다, 검색해 보세요). 이는 약 3,090만 개의 기록입니다 (20GB의 <a %(jsonlines)s>JSON Lines</a>; 4.4GB 압축됨). 그들의 웹사이트에서는 실제로 3,260만 개의 기록이 있다고 주장하므로, 우리가 일부를 놓쳤거나 <em>그들</em>이 뭔가 잘못하고 있을 수 있습니다. 어쨌든, 지금은 우리가 어떻게 했는지 정확히 공유하지 않을 것입니다 — 독자에게 연습으로 남겨두겠습니다. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "우리가 공유할 것은 세계의 책 수를 추정하기 위해 더 가까이 다가가려는 일부 예비 분석입니다. 우리는 세 가지 데이터셋을 살펴보았습니다: 이 새로운 ISBNdb 데이터셋, Z-Library 섀도우 라이브러리에서 스크랩한 metadata의 원래 릴리스 (Library Genesis 포함), 그리고 Open Library 데이터 덤프입니다."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "대략적인 숫자로 시작해 봅시다:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Z-Library/Libgen과 Open Library 모두에서 고유한 ISBN보다 더 많은 책이 있습니다. 이는 많은 책들이 ISBN이 없다는 것을 의미하는 것일까요, 아니면 ISBN metadata가 단순히 누락된 것일까요? 우리는 아마도 다른 속성 (제목, 저자, 출판사 등)을 기반으로 한 자동 매칭, 더 많은 데이터 소스의 도입, 실제 책 스캔에서 ISBN 추출 (Z-Library/Libgen의 경우)로 이 질문에 답할 수 있을 것입니다."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "그 ISBN 중 얼마나 많은 것이 고유한가요? 이는 벤 다이어그램으로 가장 잘 설명됩니다:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "좀 더 정확히 말하자면:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "우리는 겹치는 부분이 얼마나 적은지에 놀랐습니다! ISBNdb에는 Z-Library나 Open Library에 나타나지 않는 엄청난 양의 ISBN이 있으며, 다른 두 곳에서도 (작지만 여전히 상당한 정도로) 동일합니다. 이는 많은 새로운 질문을 제기합니다. 자동 매칭이 ISBN으로 태그되지 않은 책을 태그하는 데 얼마나 도움이 될까요? 많은 매칭이 발생하여 겹침이 증가할까요? 또한, 4번째 또는 5번째 데이터셋을 도입하면 어떻게 될까요? 그때는 얼마나 많은 겹침이 있을까요?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "이것은 우리에게 출발점을 제공합니다. 이제 Z-Library 데이터셋에 없고 제목/저자 필드와도 일치하지 않는 모든 ISBN을 살펴볼 수 있습니다. 이는 전 세계의 모든 책을 보존하는 데 도움이 될 수 있습니다: 먼저 인터넷에서 스캔을 스크랩하고, 그런 다음 실제로 나가서 책을 스캔하는 것입니다. 후자는 심지어 크라우드 펀딩으로, 또는 특정 책의 디지털화를 원하는 사람들의 \"현상금\"에 의해 추진될 수도 있습니다. 이 모든 것은 다른 시간에 대한 이야기입니다."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "이 작업에 도움을 주고 싶다면 — 추가 분석; 더 많은 metadata 스크랩; 더 많은 책 찾기; 책의 OCR 처리; 다른 도메인 (예: 논문, 오디오북, 영화, TV 쇼, 잡지)에 대해 이 작업을 수행하거나 심지어 ML / 대형 언어 모델 훈련을 위한 데이터 제공 — 저에게 연락해 주세요 (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "데이터 분석에 특히 관심이 있다면, 우리는 데이터셋과 스크립트를 더 쉽게 사용할 수 있는 형식으로 제공하기 위해 작업 중입니다. 노트북을 포크하여 시작할 수 있다면 좋겠습니다."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "마지막으로, 이 작업을 지원하고 싶다면 기부를 고려해 주세요. 이는 전적으로 자원봉사로 운영되는 작업이며, 여러분의 기여는 큰 차이를 만듭니다. 작은 도움도 큰 도움이 됩니다. 현재는 암호화폐로 기부를 받고 있습니다; Anna’s Archive의 기부 페이지를 참조하세요."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. \"영원히\"의 합리적인 정의에 따라. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. 물론, 인류의 서면 유산은 특히 요즘 책보다 훨씬 더 많습니다. 이 게시물과 최근 릴리스에서는 책에 초점을 맞추고 있지만, 우리의 관심은 더 넓습니다."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "아론 스워츠에 대해 더 많은 이야기를 할 수 있지만, 이 이야기에서 중요한 역할을 하기 때문에 간단히 언급하고자 합니다. 시간이 지나면서 더 많은 사람들이 그의 이름을 처음 접하게 될 것이며, 그들 스스로도 깊이 탐구할 수 있을 것입니다."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "섀도우 라이브러리의 중요한 시기"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "이미 1PB에 가까워지고 있는 컬렉션을 영구히 보존할 수 있다고 어떻게 주장할 수 있을까요?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>중국어 버전 中文版</a>, <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>에서 토론하기"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "안나의 아카이브에서 우리는 종종 컬렉션의 총 크기가 이미 1 페타바이트(1000 TB)에 가까워지고 있으며 여전히 증가하고 있는 상황에서 어떻게 영구히 보존할 수 있다고 주장할 수 있는지에 대한 질문을 받습니다. 이 기사에서는 우리의 철학을 살펴보고, 인류의 지식과 문화를 보존하는 우리의 사명에 있어 다음 10년이 왜 중요한지 알아보겠습니다."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "지난 몇 달 동안 <a %(annas_archive_stats)s>컬렉션의 총 크기</a>를 토렌트 시더 수로 나누어 본 것입니다."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "우선순위"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "우리는 왜 논문과 책에 그렇게 많은 관심을 가질까요? 일반적인 보존에 대한 우리의 근본적인 믿음을 제쳐두고 — 이에 대해 다른 글을 쓸 수도 있습니다. 그렇다면 왜 논문과 책일까요? 답은 간단합니다: <strong>정보 밀도</strong>입니다."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "저장 공간 메가바이트당, 작성된 텍스트는 모든 미디어 중 가장 많은 정보를 저장합니다. 우리는 지식과 문화 모두에 관심이 있지만, 전자에 더 많은 관심을 가지고 있습니다. 전반적으로, 우리는 정보 밀도와 보존의 중요성에 대한 계층 구조를 다음과 같이 대략적으로 찾습니다:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "학술 논문, 저널, 보고서"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "DNA 서열, 식물 씨앗, 미생물 샘플과 같은 유기 데이터"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "논픽션 도서"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "과학 및 공학 소프트웨어 코드"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "과학적 측정, 경제 데이터, 기업 보고서와 같은 측정 데이터"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "과학 및 공학 웹사이트, 온라인 토론"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "논픽션 잡지, 신문, 매뉴얼"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "강연, 다큐멘터리, 팟캐스트의 논픽션 전사"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "기업 또는 정부의 내부 데이터(유출)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "일반적으로 metadata 기록(논픽션 및 픽션; 다른 미디어, 예술, 사람 등; 리뷰 포함)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "지리 데이터(예: 지도, 지질 조사)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "법적 또는 법원 절차의 기록"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "위의 모든 것의 허구적 또는 오락적 버전"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "이 목록의 순위는 다소 임의적입니다. 여러 항목이 동점이거나 팀 내에서 의견이 다를 수 있으며, 중요한 카테고리를 잊고 있을 수도 있습니다. 하지만 대략적으로 이렇게 우선순위를 정합니다."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "이 목록의 일부 항목은 다른 것들과 너무 달라서 우리가 걱정할 필요가 없거나(또는 이미 다른 기관에서 처리하고 있는) 유기적 데이터나 지리적 데이터와 같은 것들입니다. 하지만 이 목록의 대부분의 항목은 실제로 우리에게 중요합니다."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "우선순위를 정하는 또 다른 큰 요인은 특정 작품이 얼마나 위험에 처해 있는가입니다. 우리는 다음과 같은 작품에 집중하는 것을 선호합니다:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "희귀한"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "독특하게 주목받지 못하는"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "파괴의 위험이 독특하게 높은 (예: 전쟁, 자금 삭감, 소송, 정치적 박해 등으로 인해)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "마지막으로, 우리는 규모를 중요하게 생각합니다. 우리는 제한된 시간과 자원을 가지고 있기 때문에, 가치와 위험이 비슷하다면 1,000권의 책보다 10,000권의 책을 구하는 데 한 달을 쓰는 것이 낫습니다."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "섀도우 라이브러리"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "비슷한 임무와 우선순위를 가진 많은 조직이 있습니다. 실제로, 이러한 보존을 담당하는 도서관, 아카이브, 연구소, 박물관 및 기타 기관이 있습니다. 그들 중 많은 곳은 정부, 개인 또는 기업에 의해 잘 지원받고 있습니다. 그러나 그들은 하나의 큰 맹점을 가지고 있습니다: 법률 시스템입니다."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "여기에 섀도우 라이브러리의 독특한 역할과 Anna’s Archive가 존재하는 이유가 있습니다. 우리는 다른 기관이 할 수 없는 일을 할 수 있습니다. 이제, 우리가 다른 곳에서 보존할 수 없는 자료를 아카이브할 수 있다는 것은 아닙니다. 아니요, 많은 곳에서 책, 논문, 잡지 등을 포함한 아카이브를 구축하는 것은 합법적입니다."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "하지만 법적 아카이브가 종종 부족한 것은 <strong>중복성과 장기성</strong>입니다. 어떤 물리적 도서관에 단 한 권만 존재하는 책이 있습니다. 단일 기업이 보호하는 metadata 기록이 있습니다. 단일 아카이브에 마이크로필름으로만 보존된 신문이 있습니다. 도서관은 자금 삭감을 받을 수 있고, 기업은 파산할 수 있으며, 아카이브는 폭격을 당하거나 불타 없어질 수 있습니다. 이는 가상의 상황이 아닙니다. 이는 항상 일어나는 일입니다."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Anna’s Archive에서 우리가 독특하게 할 수 있는 일은 많은 작품의 복사본을 대규모로 저장하는 것입니다. 우리는 논문, 책, 잡지 등을 수집하고 대량으로 배포할 수 있습니다. 현재 우리는 토렌트를 통해 이를 수행하고 있지만, 정확한 기술은 중요하지 않으며 시간이 지나면서 변할 것입니다. 중요한 부분은 전 세계에 많은 복사본을 배포하는 것입니다. 200년이 넘은 이 인용문은 여전히 진실합니다:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>잃어버린 것은 회복할 수 없지만, 남은 것을 구합시다: 대중의 눈과 사용으로부터 그들을 막는 금고와 자물쇠로 그들을 시간의 낭비로 보내는 것이 아니라, 사고의 범위를 넘어설 수 있는 복사본의 증식을 통해서입니다.</q></em><br>— 토마스 제퍼슨, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "공공 도메인에 대한 간단한 메모입니다. Anna’s Archive는 전 세계 많은 곳에서 불법인 활동에 독특하게 집중하기 때문에, 공공 도메인 책과 같은 널리 이용 가능한 컬렉션에는 신경 쓰지 않습니다. 법적 기관은 종종 그것을 잘 관리합니다. 그러나 때때로 우리가 공개적으로 이용 가능한 컬렉션에 작업하게 만드는 고려 사항이 있습니다:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata 기록은 Worldcat 웹사이트에서 자유롭게 볼 수 있지만 대량으로 다운로드할 수는 없습니다 (우리가 <a %(worldcat_scrape)s>스크랩</a>하기 전까지는)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "코드는 Github에서 오픈 소스로 제공될 수 있지만, Github 전체는 쉽게 미러링되어 보존될 수 없습니다 (하지만 이 특정 경우에는 대부분의 코드 저장소에 충분히 분산된 복사본이 있습니다)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit은 무료로 사용할 수 있지만, 최근 데이터에 굶주린 LLM 훈련의 여파로 엄격한 스크래핑 방지 조치를 도입했습니다 (자세한 내용은 나중에)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "복사본의 증식"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "우리의 원래 질문으로 돌아가서: 어떻게 우리의 컬렉션을 영구히 보존할 수 있다고 주장할 수 있을까요? 여기서 주요 문제는 우리의 컬렉션이 <a %(torrents_stats)s>빠르게 성장</a>하고 있다는 것입니다. 이는 일부 대규모 컬렉션을 스크래핑하고 오픈 소싱함으로써 이루어졌습니다 (이미 Sci-Hub과 Library Genesis 같은 다른 오픈 데이터 섀도우 라이브러리들이 수행한 놀라운 작업 위에 추가된 것입니다)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "이 데이터의 성장은 전 세계적으로 컬렉션을 미러링하는 것을 더 어렵게 만듭니다. 데이터 저장은 비용이 많이 듭니다! 하지만 우리는 다음 세 가지 추세를 관찰할 때 낙관적입니다."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. 우리는 쉽게 얻을 수 있는 것을 먼저 수확했습니다"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "이것은 위에서 논의한 우리의 우선순위에서 직접적으로 따릅니다. 우리는 대규모 컬렉션을 먼저 해방시키는 작업을 선호합니다. 이제 세계에서 가장 큰 컬렉션 중 일부를 확보했으므로 우리의 성장이 훨씬 더 느려질 것으로 예상합니다."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "여전히 더 작은 컬렉션의 긴 꼬리가 남아 있으며, 매일 새로운 책이 스캔되거나 출판되지만, 그 속도는 아마도 훨씬 느려질 것입니다. 우리는 여전히 크기가 두 배 또는 세 배로 늘어날 수 있지만, 더 긴 시간 동안에 걸쳐 이루어질 것입니다."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. 저장 비용은 계속해서 기하급수적으로 감소하고 있습니다"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "작성 시점에서, <a %(diskprices)s>디스크 가격</a>은 새 디스크의 경우 TB당 약 $12, 중고 디스크의 경우 $8, 테이프의 경우 $4입니다. 보수적으로 새 디스크만 본다면, 페타바이트를 저장하는 데 약 $12,000가 듭니다. 우리의 도서관이 900TB에서 2.7PB로 세 배로 늘어난다고 가정하면, 우리의 전체 도서관을 미러링하는 데 $32,400가 필요합니다. 전기, 기타 하드웨어 비용 등을 추가하면, 이를 $40,000로 올려잡을 수 있습니다. 또는 테이프의 경우 $15,000–$20,000 정도입니다."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "한편으로 <strong>모든 인류 지식의 합계에 대해 $15,000–$40,000는 매우 저렴합니다</strong>. 다른 한편으로는, 특히 다른 사람들을 위해 토렌트를 계속 시딩해주기를 바란다면, 전체 복사본을 기대하는 것은 다소 부담스럽습니다."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "그것이 오늘날입니다. 하지만 진보는 계속됩니다:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "지난 10년 동안 하드 드라이브의 TB당 비용은 대략 3분의 1로 줄어들었으며, 비슷한 속도로 계속 감소할 가능성이 큽니다. 테이프도 비슷한 경로를 따르는 것으로 보입니다. SSD 가격은 더욱 빠르게 하락하고 있으며, 10년 말까지 HDD 가격을 넘어설 수 있습니다."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "다양한 출처의 HDD 가격 추세 (연구 보기 클릭)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "이 추세가 유지된다면, 10년 후에는 우리의 전체 컬렉션을 미러링하는 데 $5,000–$13,000 (1/3)만 필요할 수 있으며, 크기가 덜 증가한다면 더 적을 수도 있습니다. 여전히 많은 돈이지만, 이는 많은 사람들이 감당할 수 있는 수준이 될 것입니다. 그리고 다음 포인트 덕분에 상황이 더 나아질 수도 있습니다…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. 정보 밀도의 개선"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "우리는 현재 책을 제공받은 미가공 형식으로 저장하고 있습니다. 물론 압축되어 있지만, 종종 여전히 페이지의 큰 스캔본이나 사진입니다."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "지금까지 우리의 컬렉션의 총 크기를 줄이는 유일한 옵션은 더 공격적인 압축이나 중복 제거를 통해서였습니다. 그러나 충분히 큰 절감을 얻기 위해서는 둘 다 우리의 취향에 비해 손실이 너무 큽니다. 사진의 강한 압축은 텍스트를 거의 읽을 수 없게 만들 수 있습니다. 그리고 중복 제거는 책이 정확히 동일하다는 높은 신뢰도를 요구하는데, 이는 종종 부정확합니다. 특히 내용이 동일하지만 스캔이 다른 시점에 이루어진 경우에는 더욱 그렇습니다."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "항상 세 번째 옵션이 있었지만, 그 품질이 너무 형편없어서 고려하지 않았습니다: <strong>OCR, 즉 광학 문자 인식</strong>. 이는 AI를 사용하여 사진의 문자를 감지하여 사진을 일반 텍스트로 변환하는 과정입니다. 이를 위한 도구는 오래전부터 존재해왔고, 꽤 괜찮았지만, \"꽤 괜찮다\"는 보존 목적에는 충분하지 않았습니다."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "그러나 최근의 다중 모달 딥러닝 모델은 매우 빠르게 발전하고 있으며, 여전히 높은 비용이 들지만, 향후 몇 년 동안 정확도와 비용이 크게 개선될 것으로 기대됩니다. 이는 우리의 전체 도서관에 적용하는 것이 현실적이 될 정도로 발전할 것입니다."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR 개선."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "그렇게 되면, 우리는 여전히 원본 파일을 보존하겠지만, 대부분의 사람들이 미러링하고 싶어할 훨씬 작은 버전의 도서관을 가질 수 있을 것입니다. 중요한 점은 미가공 텍스트 자체가 더 잘 압축되고, 중복 제거가 훨씬 쉬워져 더 많은 절감을 제공한다는 것입니다."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "전반적으로 총 파일 크기가 최소 5-10배 감소할 것으로 기대하는 것이 비현실적이지 않으며, 아마도 그 이상일 것입니다. 보수적으로 5배 감소를 가정하더라도, 우리의 도서관이 세 배로 늘어난다고 해도 10년 후에는 <strong>$1,000–$3,000 정도가 될 것입니다</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "중요한 시기"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "이 예측이 정확하다면, 우리는 <strong>몇 년만 기다리면</strong> 우리의 전체 컬렉션이 널리 미러링될 것입니다. 따라서, 토마스 제퍼슨의 말처럼 \"사고의 범위를 벗어나게\" 될 것입니다."

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "불행히도, LLM의 출현과 그들의 데이터 갈망 훈련은 많은 저작권 소유자들을 방어적으로 만들었습니다. 이미 그랬던 것보다 더 많이. 많은 웹사이트들이 스크래핑과 아카이빙을 더 어렵게 만들고 있으며, 소송이 난무하고 있는 동안 물리적 도서관과 아카이브는 계속해서 방치되고 있습니다."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "이러한 추세가 계속 악화될 것으로 예상할 수 있으며, 많은 작품들이 공공 도메인에 들어가기 훨씬 전에 사라질 것입니다."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>우리는 보존의 혁명 직전에 있지만, <q>잃어버린 것은 회복할 수 없습니다.</q></strong> 섀도우 라이브러리를 운영하고 전 세계에 많은 미러를 만드는 것이 여전히 상당히 비싼 약 5-10년의 중요한 시기가 있으며, 이 기간 동안 접근이 완전히 차단되지 않았습니다."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "이 시기를 넘길 수 있다면, 우리는 인류의 지식과 문화를 영구히 보존할 수 있을 것입니다. 이 시간을 낭비해서는 안 됩니다. 이 중요한 시기가 우리에게 닫히지 않도록 해야 합니다."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "갑시다."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "세계 최대의 중국 비소설 도서 컬렉션에 대한 LLM 회사의 독점 접근"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>중국어 버전 中文版</a>, <a %(news_ycombinator)s>Hacker News에서 토론하기</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>요약:</strong> 안나의 아카이브는 750만 권 / 350TB의 중국 비소설 도서라는 독특한 컬렉션을 획득했습니다 — Library Genesis보다 큽니다. 우리는 고품질의 OCR 및 텍스트 추출을 대가로 LLM 회사에 독점 접근을 제공할 의향이 있습니다.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "이것은 짧은 블로그 게시물입니다. 우리는 우리가 획득한 방대한 컬렉션에 대해 OCR 및 텍스트 추출을 도와줄 회사나 기관을 찾고 있으며, 독점적인 초기 접근을 대가로 제공할 것입니다. 금지 기간이 끝난 후, 우리는 물론 전체 컬렉션을 공개할 것입니다."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "고품질의 학술 텍스트는 LLM의 훈련에 매우 유용합니다. 우리의 컬렉션은 중국어이지만, 이는 영어 LLM의 훈련에도 유용할 것입니다: 모델은 출처 언어와 상관없이 개념과 지식을 인코딩하는 것으로 보입니다."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "이를 위해, 스캔에서 텍스트를 추출해야 합니다. 안나의 아카이브가 얻는 것은 무엇일까요? 사용자들을 위한 도서의 전체 텍스트 검색입니다."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "우리의 목표가 LLM 개발자와 일치하기 때문에, 우리는 협력자를 찾고 있습니다. 당신이 적절한 OCR 및 텍스트 추출을 할 수 있다면, 우리는 <strong>이 컬렉션에 대한 독점적인 초기 접근을 1년 동안 대량으로 제공할 의향이 있습니다</strong>. 만약 당신이 파이프라인의 전체 코드를 우리와 공유할 의향이 있다면, 우리는 컬렉션의 금지 기간을 더 길게 할 의향이 있습니다."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "예제 페이지"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "당신이 좋은 파이프라인을 가지고 있음을 증명하기 위해, 초전도체에 관한 책에서 시작할 수 있는 몇 가지 예제 페이지가 있습니다. 당신의 파이프라인은 수학, 표, 차트, 각주 등을 적절히 처리해야 합니다."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "처리된 페이지를 우리의 이메일로 보내주세요. 그것들이 좋게 보이면, 우리는 더 많은 것을 비공개로 보내드릴 것이며, 당신이 그것들에도 빠르게 파이프라인을 실행할 수 있기를 기대합니다. 우리가 만족하면, 우리는 거래를 할 수 있습니다."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "컬렉션"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "컬렉션에 대한 추가 정보입니다. <a %(duxiu)s>Duxiu</a>는 <a %(chaoxing)s>SuperStar Digital Library Group</a>에 의해 만들어진 방대한 스캔 도서 데이터베이스입니다. 대부분은 학술 도서로, 대학과 도서관에 디지털로 제공하기 위해 스캔되었습니다. 영어를 사용하는 청중을 위해, <a %(library_princeton)s>프린스턴</a>과 <a %(guides_lib_uw)s>워싱턴 대학교</a>가 좋은 개요를 제공합니다. 또한 배경을 더 잘 이해할 수 있는 훌륭한 기사도 있습니다: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (안나의 아카이브에서 찾아보세요)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "중국 인터넷에서는 Duxiu의 책들이 오랫동안 불법 복제되어 왔습니다. 보통 재판매자들에 의해 1달러 미만으로 판매되고 있습니다. 이들은 주로 구글 드라이브의 중국 버전을 사용하여 배포되며, 종종 더 많은 저장 공간을 허용하기 위해 해킹되기도 합니다. 몇 가지 기술적인 세부 사항은 <a %(github_duty_machine)s>여기</a>와 <a %(github_821_github_io)s>여기</a>에서 확인할 수 있습니다."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "비록 책들이 반공개적으로 배포되었지만, 대량으로 얻는 것은 꽤 어렵습니다. 우리는 이것을 할 일 목록의 상위에 두고, 이를 위해 여러 달의 전일제 작업을 할당했습니다. 그러나 최근에 믿을 수 없을 정도로 놀랍고 재능 있는 자원봉사자가 우리에게 연락하여, 그들이 이미 이 모든 작업을 큰 비용을 들여 완료했다고 알려주었습니다. 그들은 장기 보존을 보장받는 것 외에는 아무것도 기대하지 않고 전체 컬렉션을 우리와 공유했습니다. 정말 놀랍습니다. 그들은 이 컬렉션을 OCR 처리하기 위해 도움을 요청하는 방식에 동의했습니다."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "컬렉션은 7,543,702개의 파일로 구성되어 있습니다. 이는 Library Genesis의 논픽션(약 530만 권)보다 많습니다. 현재 형태의 총 파일 크기는 약 359TB(326TiB)입니다."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "다른 제안과 아이디어도 열려 있습니다. 그냥 저희에게 연락하세요. 저희 컬렉션, 보존 노력, 그리고 어떻게 도울 수 있는지에 대한 더 많은 정보를 보려면 안나의 아카이브를 확인하세요. 감사합니다!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "경고: 이 블로그 게시물은 더 이상 사용되지 않습니다. 우리는 IPFS가 아직 주류로 사용되기에는 준비가 되지 않았다고 결정했습니다. 우리는 여전히 안나의 아카이브에서 가능한 경우 IPFS의 파일에 링크할 것이지만, 더 이상 자체적으로 호스팅하지 않으며, 다른 사람들이 IPFS를 사용하여 미러링하는 것을 권장하지 않습니다. 컬렉션 보존을 돕고 싶다면 토렌트 페이지를 참조하세요."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "IPFS에서 Z-Library 시드하기"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "섀도우 라이브러리 운영: 안나의 아카이브에서의 운영"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "섀도우 자선단체를 위한 <q>AWS는 없습니다,</q> 그렇다면 우리는 안나의 아카이브를 어떻게 운영할까요?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "저는 <a %(wikipedia_annas_archive)s>안나의 아카이브</a>를 운영하고 있으며, 이는 Sci-Hub, Library Genesis, Z-Library와 같은 <a %(wikipedia_shadow_library)s>섀도우 라이브러리</a>를 위한 세계 최대의 오픈 소스 비영리 검색 엔진입니다. 우리의 목표는 지식과 문화를 쉽게 접근할 수 있도록 하고, 궁극적으로는 <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>전 세계의 모든 책</a>을 아카이브하고 보존하는 사람들의 커뮤니티를 구축하는 것입니다."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "이 기사에서는 이 웹사이트를 어떻게 운영하는지, 그리고 섀도우 자선단체를 위한 \"AWS\"가 없기 때문에 법적 지위가 의심스러운 웹사이트를 운영하는 데 따르는 독특한 도전 과제에 대해 설명하겠습니다."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>자매 기사 <a %(blog_how_to_become_a_pirate_archivist)s>해적 아카이비스트가 되는 방법</a>도 확인하세요.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "혁신 토큰"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "우리의 기술 스택부터 시작해 봅시다. 그것은 의도적으로 지루합니다. 우리는 Flask, MariaDB, ElasticSearch를 사용합니다. 그게 전부입니다. 검색은 대체로 해결된 문제이며, 우리는 그것을 재발명할 의도가 없습니다. 게다가 우리는 <a %(mcfunley)s>혁신 토큰</a>을 다른 것에 사용해야 합니다: 당국에 의해 차단되지 않는 것."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "그렇다면 안나의 아카이브는 정확히 얼마나 합법적이거나 불법적인가요? 이는 주로 법적 관할권에 따라 다릅니다. 대부분의 국가는 어떤 형태로든 저작권을 믿고 있으며, 이는 특정 기간 동안 특정 유형의 작품에 대해 사람이나 회사에 독점적인 독점권을 부여한다는 것을 의미합니다. 참고로, 안나의 아카이브에서는 일부 이점이 있긴 하지만, 전반적으로 저작권은 사회에 부정적인 영향을 미친다고 믿고 있습니다 — 하지만 그것은 다른 이야기입니다."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "특정 작품에 대한 이 독점적인 독점권은 이 독점권 외부의 누구도 그 작품을 직접 배포하는 것이 불법임을 의미합니다 — 우리를 포함해서요. 하지만 안나의 아카이브는 직접적으로 그 작품을 배포하지 않는 검색 엔진이기 때문에 (적어도 우리의 클리어넷 웹사이트에서는) 괜찮을까요? 꼭 그렇지는 않습니다. 많은 관할권에서는 저작권이 있는 작품을 배포하는 것이 불법일 뿐만 아니라, 그러한 작품을 배포하는 곳에 링크를 거는 것도 불법입니다. 이의 고전적인 예는 미국의 DMCA 법입니다."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "이는 스펙트럼의 가장 엄격한 끝입니다. 스펙트럼의 다른 끝에는 이론적으로 저작권 법이 전혀 없는 국가가 있을 수 있지만, 이러한 국가는 실제로 존재하지 않습니다. 거의 모든 국가에는 어떤 형태로든 저작권 법이 존재합니다. 집행은 다른 이야기입니다. 저작권 법을 집행하는 데 관심이 없는 정부가 있는 국가도 많습니다. 두 극단 사이에는 저작권이 있는 작품을 배포하는 것을 금지하지만, 그러한 작품에 링크를 거는 것은 금지하지 않는 국가도 있습니다."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "또 다른 고려 사항은 회사 수준입니다. 만약 회사가 저작권에 신경 쓰지 않는 관할권에서 운영되지만, 회사 자체가 어떤 위험도 감수하지 않으려 한다면, 누군가가 불만을 제기하자마자 웹사이트를 폐쇄할 수 있습니다."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "마지막으로 큰 고려 사항은 결제입니다. 우리는 익명성을 유지해야 하기 때문에 전통적인 결제 방법을 사용할 수 없습니다. 이는 우리를 암호화폐로 남겨두며, 이를 지원하는 회사는 소수에 불과합니다 (암호화폐로 결제되는 가상 직불카드가 있지만, 종종 받아들여지지 않습니다)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "시스템 아키텍처"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "그래서 당신의 웹사이트를 종료시키지 않고 호스팅할 수 있는 회사를 찾았다고 가정해 봅시다 — 이를 \"자유를 사랑하는 제공자\"라고 부르겠습니다 😄. 그들과 함께 모든 것을 호스팅하는 것이 꽤 비싸다는 것을 금방 알게 될 것이므로, \"저렴한 제공자\"를 찾아 실제 호스팅을 하고, 자유를 사랑하는 제공자를 통해 프록시를 설정할 수 있습니다. 제대로 한다면, 저렴한 제공자는 당신이 무엇을 호스팅하는지 절대 알지 못하고, 불만을 받지도 않을 것입니다."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "이 모든 제공자들과 함께, 그들이 어쨌든 당신을 종료시킬 위험이 있으므로, 중복성을 확보해야 합니다. 우리는 스택의 모든 수준에서 이것이 필요합니다."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "다소 자유를 사랑하는 회사 중 하나는 Cloudflare로, 흥미로운 위치에 있습니다. 그들은 <a %(blog_cloudflare)s>자신들이</a> 호스팅 제공자가 아니라 ISP와 같은 유틸리티라고 주장했습니다. 따라서 DMCA나 다른 삭제 요청의 대상이 아니며, 모든 요청을 실제 호스팅 제공자에게 전달합니다. 이 구조를 보호하기 위해 법정까지 간 적도 있습니다. 따라서 우리는 그들을 또 다른 캐싱 및 보호 계층으로 사용할 수 있습니다."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare는 익명 결제를 허용하지 않으므로, 우리는 그들의 무료 플랜만 사용할 수 있습니다. 이는 로드 밸런싱이나 장애 조치 기능을 사용할 수 없다는 것을 의미합니다. 따라서 우리는 도메인 수준에서 <a %(annas_archive_l255)s>이를 자체적으로 구현했습니다</a>. 페이지 로드 시, 브라우저는 현재 도메인이 여전히 사용 가능한지 확인하고, 그렇지 않으면 모든 URL을 다른 도메인으로 다시 씁니다. Cloudflare가 많은 페이지를 캐시하기 때문에, 사용자는 프록시 서버가 다운되더라도 우리의 메인 도메인에 도착할 수 있으며, 다음 클릭 시 다른 도메인으로 이동할 수 있습니다."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "우리는 여전히 서버 상태 모니터링, 백엔드 및 프론트엔드 오류 로깅 등과 같은 일반적인 운영 문제를 처리해야 합니다. 우리의 장애 조치 아키텍처는 이 측면에서도 더 많은 견고성을 제공합니다. 예를 들어, 도메인 중 하나에서 완전히 다른 서버 세트를 실행함으로써 가능합니다. 심지어 메인 버전에서 중요한 버그가 발견되지 않을 경우를 대비해, 이 별도의 도메인에서 이전 버전의 코드와 데이터를 실행할 수도 있습니다."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Cloudflare가 우리에게 등을 돌릴 가능성에 대비하여, 이 별도의 도메인과 같은 도메인 중 하나에서 Cloudflare를 제거할 수 있습니다. 이러한 아이디어의 다양한 조합이 가능합니다."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "도구"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "이 모든 것을 달성하기 위해 사용하는 도구를 살펴봅시다. 이는 새로운 문제에 직면하고 새로운 해결책을 찾으면서 매우 진화하고 있습니다."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "애플리케이션 서버: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "프록시 서버: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "서버 관리: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "개발: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion 정적 호스팅: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "우리가 여러 번 고민했던 결정들이 있습니다. 하나는 서버 간의 통신입니다: 우리는 이를 위해 Wireguard를 사용했지만, 때때로 데이터 전송이 중단되거나 한 방향으로만 전송되는 것을 발견했습니다. <a %(github_costela_wesher)s>wesher</a>와 <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>와 같은 여러 Wireguard 설정에서 이러한 문제가 발생했습니다. 또한 autossh와 sshuttle을 사용하여 SSH를 통해 포트를 터널링하려고 했지만, <a %(github_sshuttle)s>문제에 직면했습니다</a> (autossh가 TCP-over-TCP 문제를 겪는지 여부는 아직 명확하지 않지만, 저에게는 불안정한 솔루션처럼 느껴지지만 실제로는 괜찮을 수도 있습니다)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "대신, 우리는 서버 간의 직접 연결로 되돌아갔으며, UFW를 사용하여 IP 필터링을 통해 저렴한 제공자에서 서버가 실행 중임을 숨겼습니다. 이는 <code>network_mode: \"host\"</code>를 사용하지 않으면 Docker가 UFW와 잘 작동하지 않는다는 단점이 있습니다. 이 모든 것은 약간 더 오류가 발생하기 쉬운데, 작은 잘못된 구성으로 서버를 인터넷에 노출시킬 수 있기 때문입니다. 아마도 우리는 autossh로 돌아가야 할 것입니다 — 여기에 대한 피드백을 환영합니다."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "우리는 Varnish와 Nginx 사이에서도 여러 번 고민했습니다. 현재는 Varnish를 선호하지만, 몇 가지 특이점과 거친 부분이 있습니다. Checkmk도 마찬가지입니다: 우리는 그것을 좋아하지 않지만, 현재로서는 작동합니다. Weblate는 괜찮았지만 놀랍지는 않았습니다 — git 저장소와 동기화할 때마다 데이터가 손실될까 두렵습니다. Flask는 전반적으로 좋았지만, 사용자 정의 도메인 구성이나 SqlAlchemy 통합 문제와 같은 이상한 특이점이 있어 디버깅에 많은 시간이 소요되었습니다."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "지금까지 다른 도구들은 훌륭했습니다: MariaDB, ElasticSearch, Gitlab, Zulip, Docker, Tor에 대해 심각한 불만은 없습니다. 이들 모두 약간의 문제가 있었지만, 심각하거나 시간이 많이 소요되는 문제는 없었습니다."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "결론"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "견고하고 회복력 있는 섀도우 라이브러리 검색 엔진을 설정하는 방법을 배우는 것은 흥미로운 경험이었습니다. 나중에 공유할 더 많은 세부 사항이 있으니, 더 알고 싶은 것이 있으면 알려주세요!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "항상 그렇듯이, 이 작업을 지원하기 위해 기부를 찾고 있으니, Anna’s Archive의 기부 페이지를 꼭 확인해 보세요. 우리는 또한 보조금, 장기 후원자, 고위험 결제 제공자, 어쩌면 (세련된!) 광고와 같은 다른 유형의 지원도 찾고 있습니다. 시간과 기술을 기여하고 싶다면, 우리는 항상 개발자, 번역가 등을 찾고 있습니다. 관심과 지원에 감사드립니다."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- 안나와 팀 (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "안녕하세요, 저는 안나입니다. 저는 세계에서 가장 큰 섀도우 라이브러리인 <a %(wikipedia_annas_archive)s>안나의 아카이브</a>를 만들었습니다. 이곳은 저와 제 팀원들이 해적판, 디지털 보존 등에 대해 글을 쓰는 개인 블로그입니다."

#, fuzzy
msgid "blog.index.text2"
msgstr "<a %(reddit)s>Reddit</a>에서 저와 연결하세요."

#, fuzzy
msgid "blog.index.text3"
msgstr "이 웹사이트는 단지 블로그일 뿐입니다. 우리는 여기에서 우리의 글만을 호스팅합니다. 토렌트나 다른 저작권 파일은 호스팅하거나 링크하지 않습니다."

#, fuzzy
msgid "blog.index.heading"
msgstr "블로그 게시물"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat 스크레이프"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5,998,794권의 책을 IPFS에 올리기"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "경고: 이 블로그 게시물은 더 이상 사용되지 않습니다. 우리는 IPFS가 아직 주류로 사용되기에는 준비가 되지 않았다고 결정했습니다. 우리는 여전히 안나의 아카이브에서 가능한 경우 IPFS의 파일에 링크할 것이지만, 더 이상 자체적으로 호스팅하지 않으며, 다른 사람들이 IPFS를 사용하여 미러링하는 것을 권장하지 않습니다. 컬렉션 보존을 돕고 싶다면 토렌트 페이지를 참조하세요."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>요약:</strong> 안나의 아카이브는 보존이 필요한 책의 TODO 리스트를 만들기 위해 WorldCat(세계 최대의 도서 metadata 컬렉션)을 모두 스크레이프했습니다.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "1년 전, 우리는 이 질문에 답하기 위해 <a %(blog)s>시작했습니다</a>: <strong>섀도우 라이브러리에 의해 영구적으로 보존된 책의 비율은 얼마일까요?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "책이 <a %(wikipedia_library_genesis)s>Library Genesis</a>와 이제 <a %(wikipedia_annas_archive)s>안나의 아카이브</a> 같은 오픈 데이터 섀도우 라이브러리에 들어가면, 전 세계에 (토렌트를 통해) 미러링되어 사실상 영원히 보존됩니다."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "책이 얼마나 보존되었는지에 대한 질문에 답하기 위해서는 분모를 알아야 합니다: 총 몇 권의 책이 존재하는가? 이상적으로는 단순한 숫자가 아니라 실제 metadata가 필요합니다. 그러면 섀도우 라이브러리와 대조할 수 있을 뿐만 아니라 <strong>보존해야 할 남은 책의 TODO 리스트를 만들 수 있습니다!</strong> 우리는 이 TODO 리스트를 따라가는 크라우드소싱 노력을 꿈꿀 수도 있습니다."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "우리는 <a %(wikipedia_isbndb_com)s>ISBNdb</a>를 스크레이프하고 <a %(openlibrary)s>Open Library 데이터셋</a>을 다운로드했지만, 결과는 만족스럽지 않았습니다. 주요 문제는 ISBN의 중복이 많지 않았다는 것입니다. <a %(blog)s>우리의 블로그 게시물</a>에서 이 벤 다이어그램을 보세요:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "우리는 ISBNdb와 Open Library 간의 중복이 얼마나 적은지에 매우 놀랐습니다. 두 곳 모두 다양한 출처, 예를 들어 웹 스크레이프와 도서관 기록에서 데이터를 자유롭게 포함하고 있습니다. 만약 그들이 대부분의 ISBN을 잘 찾았다면, 그들의 원은 분명히 상당한 중복이 있거나 하나가 다른 하나의 부분집합이었을 것입니다. 우리는 얼마나 많은 책이 <em>이 원들 밖에 완전히 있는지</em> 궁금해졌습니다. 더 큰 데이터베이스가 필요합니다."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "그때 우리는 세계에서 가장 큰 도서 데이터베이스인 <a %(wikipedia_worldcat)s>WorldCat</a>에 주목했습니다. 이는 비영리 단체 <a %(wikipedia_oclc)s>OCLC</a>가 소유한 독점 데이터베이스로, 전 세계 도서관의 metadata 기록을 수집하여, 그 도서관들이 전체 데이터셋에 접근하고 최종 사용자의 검색 결과에 나타나도록 합니다."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "OCLC가 비영리 단체임에도 불구하고, 그들의 비즈니스 모델은 데이터베이스를 보호해야 합니다. OCLC의 친구들, 죄송하지만, 우리는 모든 것을 공개합니다. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "지난 1년 동안 우리는 WorldCat의 모든 기록을 꼼꼼히 스크레이프했습니다. 처음에는 운이 좋았습니다. WorldCat이 웹사이트를 완전히 개편하는 중이었습니다 (2022년 8월). 이는 백엔드 시스템의 대대적인 개편을 포함했으며, 많은 보안 결함을 도입했습니다. 우리는 즉시 기회를 잡아 수백만 (!) 개의 기록을 며칠 만에 스크레이프할 수 있었습니다."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat 개편</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "그 후, 보안 결함이 하나씩 천천히 수정되었고, 마지막으로 발견한 결함은 약 한 달 전에 패치되었습니다. 그때까지 우리는 거의 모든 기록을 가지고 있었고, 약간 더 높은 품질의 기록만을 목표로 하고 있었습니다. 그래서 우리는 공개할 때가 되었다고 느꼈습니다!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "데이터에 대한 기본 정보를 살펴보겠습니다:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>형식?</strong> <a %(blog)s>안나의 아카이브 컨테이너(AAC)</a>, 이는 본질적으로 <a %(jsonlines)s>JSON Lines</a>를 <a %(zstd)s>Zstandard</a>로 압축한 것이며, 일부 표준화된 의미론이 추가된 것입니다. 이러한 컨테이너는 우리가 배포한 다양한 스크랩에 기반하여 다양한 유형의 기록을 포장합니다."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "데이터"

msgid "dyn.buy_membership.error.unknown"
msgstr "알 수 없는 오류가 발생했습니다. 스크린샷과 함께 %(email)s로 문의하십시오."

msgid "dyn.buy_membership.error.minimum"
msgstr "이 코인은 평소보다 높은 최소 금액을 가지고 있습니다. 다른 기간 또는 다른 코인을 선택하세요."

msgid "dyn.buy_membership.error.try_again"
msgstr "요청을 완료할 수 없습니다. 몇 분 후에 다시 시도해 주세요. 계속 문제가 발생하면 %(email)s로 스크린샷과 함께 연락해 주세요."

msgid "dyn.buy_membership.error.wait"
msgstr "결제 처리 오류. 잠시 후 다시 시도해 주세요. 문제가 24시간 이상 지속되면, 스크린샷과 함께 %(email)s로 연락해 주세요."

msgid "page.comments.hidden_comment"
msgstr "숨겨진 댓글"

msgid "page.comments.file_issue"
msgstr "파일 문제: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "더 나은 버전"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "이 사용자를 어뷰징 또는 부적절한 행동으로 신고하시겠습니까?"

msgid "page.comments.report_abuse"
msgstr "부적절한 행위 신고"

msgid "page.comments.abuse_reported"
msgstr "신고된 부적절한 행위:"

msgid "page.comments.reported_abuse_this_user"
msgstr "이 사용자를 부적절한 행위로 신고했습니다."

msgid "page.comments.reply_button"
msgstr "답글"

msgid "page.md5.quality.logged_out_login"
msgstr "<a %(a_login)s>로그인</a>해 주세요."

msgid "page.md5.quality.comment_thanks"
msgstr "댓글을 남기셨습니다. 표시되기까지 약간의 시간이 걸릴 수 있습니다."

msgid "page.md5.quality.comment_error"
msgstr "문제가 발생했습니다. 페이지를 새로고침하고 다시 시도해 주세요."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s 영향을 받은 페이지"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Libgen.rs 비소설에 표시되지 않음"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Libgen.rs Fiction에서 보이지 않음"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Libgen.li에서 보이지 않음"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Libgen.li에서 손상된 것으로 표시됨"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Z-Library에서 누락됨"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Z-Library에서 \"스팸\"으로 표시됨"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Z-Library에서 \"불량 파일\"로 표시됨"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "모든 페이지가 PDF로 변환이 가능하지는 않습니다"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "이 파일에 exiftool 실행 실패"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "책 (불명)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "책 (논픽션)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "책 (소설)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "학술 논문(저널기사)"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "표준 문서"

msgid "common.md5_content_type_mapping.magazine"
msgstr "잡지"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "만화책"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "악보"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "오디오북"

msgid "common.md5_content_type_mapping.other"
msgstr "기타"

msgid "common.access_types_mapping.aa_download"
msgstr "파트너 서버에서 다운로드(예: libgen, sci-hub, z-lib 등)"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "외부 다운로드"

msgid "common.access_types_mapping.external_borrow"
msgstr "외부 대여"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "외부 대여(인쇄 비활성화)"

msgid "common.access_types_mapping.meta_explore"
msgstr "메타데이터 탐색"

msgid "common.access_types_mapping.torrents_available"
msgstr "토렌트에 포함됨"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library 중국어"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀(독수)"

msgid "common.record_sources_mapping.uploads"
msgstr "AA에 업로드"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook 인덱스"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "체코 메타데이터"

msgid "common.record_sources_mapping.gbooks"
msgstr "구글 북스"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "러시아 국립 도서관"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "제목"

msgid "common.specific_search_fields.author"
msgstr "저자"

msgid "common.specific_search_fields.publisher"
msgstr "출판사"

msgid "common.specific_search_fields.edition_varia"
msgstr "판"

msgid "common.specific_search_fields.year"
msgstr "출판 연도"

msgid "common.specific_search_fields.original_filename"
msgstr "원본 파일명"

msgid "common.specific_search_fields.description_comments"
msgstr "설명 및 메타데이터 댓글"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "이 파일에 대해 파트너 서버 다운로드가 일시적으로 불가능합니다."

msgid "common.md5.servers.fast_partner"
msgstr "고속 파트너 서버 #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(추천)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(브라우저 인증이나 대기 없음)"

msgid "common.md5.servers.slow_partner"
msgstr "저속 파트너 서버 #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(약간 빠르지만 대기 필요)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(대기열 없음, 하지만 매우 느릴 수 있음)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs 비소설"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs 소설"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(상단의 “GET”을 클릭하세요)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(상단의 “GET” 클릭)"

msgid "page.md5.box.download.libgen_ads"
msgstr "그들의 광고에는 악성 소프트웨어가 포함되어 있을 수 있으므로 광고 차단기를 사용하거나 광고를 클릭하지 마세요"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC 파일은 다운로드가 불안정할 수 있습니다)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Tor네트워크상 Z-Library"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(Tor 브라우저 필요)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Internet Archive에서 대여하기"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(인쇄가 불가능한 이용자 전용)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(관련 DOI는 Sci-Hub에서 사용할 수 없을 수 있습니다)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "컬렉션"

msgid "page.md5.box.download.torrent"
msgstr "토렌트"

msgid "page.md5.box.download.bulk_torrents"
msgstr "대량 토렌트 다운로드"

msgid "page.md5.box.download.experts_only"
msgstr "(전문가 전용)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Anna’s Archive에서 ISBN 검색"

msgid "page.md5.box.download.other_isbn"
msgstr "ISBN에서 다양한 다른 데이터베이스 검색"

msgid "page.md5.box.download.original_isbndb"
msgstr "ISBNdb에서 원본 기록 찾기"

msgid "page.md5.box.download.aa_openlib"
msgstr "Open Library ID로 Anna’s Archive 검색"

msgid "page.md5.box.download.original_openlib"
msgstr "Open Library에서 원본 기록 찾기"

msgid "page.md5.box.download.aa_oclc"
msgstr "OCLC (WorldCat) 번호를 위해 안나의 아카이브 검색"

msgid "page.md5.box.download.original_oclc"
msgstr "WorldCat에서 원본 기록 찾기"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Anna’s Archive 에서 DuXiu SSID 번호 검색"

msgid "page.md5.box.download.original_duxiu"
msgstr "DuXiu에서 수동으로 검색하기"

msgid "page.md5.box.download.aa_cadal"
msgstr "Anna’s Archive에서 CADAL SSNO 번호 검색"

msgid "page.md5.box.download.original_cadal"
msgstr "CADAL에서 원본 기록 찾기"

msgid "page.md5.box.download.aa_dxid"
msgstr "Anna’s Archive에서 DuXiu DXID 번호 검색"

msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook 색인"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(브라우저 인증 필요 없음)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "체코 메타데이터 %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "구글 북스 %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "메타데이터"

msgid "page.md5.box.descr_title"
msgstr "설명"

msgid "page.md5.box.alternative_filename"
msgstr "대체 파일명"

msgid "page.md5.box.alternative_title"
msgstr "대체 제목"

msgid "page.md5.box.alternative_author"
msgstr "대체 저자"

msgid "page.md5.box.alternative_publisher"
msgstr "대체 출판사"

msgid "page.md5.box.alternative_edition"
msgstr "대체 판본"

msgid "page.md5.box.alternative_extension"
msgstr "대체 확장자"

msgid "page.md5.box.metadata_comments_title"
msgstr "메타데이터 댓글"

msgid "page.md5.box.alternative_description"
msgstr "대체 설명"

msgid "page.md5.box.date_open_sourced_title"
msgstr "오픈 소스된 날짜"

msgid "page.md5.header.scihub"
msgstr "Sci-Hub 파일 “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive 디지털 대출 파일 “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "이것은 Internet Archive에서 가져온 파일의 기록이며, 직접 다운로드 가능한 파일이 아닙니다. 책을 대여하려면 (아래 링크를 사용) 시도하거나, <a %(a_request)s>파일 요청</a> 시 이 URL을 사용하세요."

msgid "page.md5.header.consider_upload"
msgstr "이 파일을 가지고 있고 Anna’s Archive에 아직 없다면, <a %(a_request)s>업로드</a>를 고려해보세요."

msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) 번호 %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s 메타데이터 기록"

msgid "page.md5.header.meta_desc"
msgstr "이것은 메타데이터 기록이며, 다운로드 가능한 파일이 아닙니다. <a %(a_request)s>파일 요청</a> 시 이 URL을 사용할 수 있습니다."

msgid "page.md5.text.linked_metadata"
msgstr "연결된 기록의 메타데이터"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Open Library에서 메타데이터 개선하기"

msgid "page.md5.warning.multiple_links"
msgstr "경고: 여러 개의 연결된 기록:"

msgid "page.md5.header.improve_metadata"
msgstr "메타데이터 개선"

msgid "page.md5.text.report_quality"
msgstr "파일 품질 신고"

msgid "page.search.results.download_time"
msgstr "다운로드 시간"

msgid "page.md5.codes.url"
msgstr "URL:"

msgid "page.md5.codes.website"
msgstr "웹사이트:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Anna’s Archive에서 “%(name)s” 검색"

msgid "page.md5.codes.code_explorer"
msgstr "코드 탐색기:"

msgid "page.md5.codes.code_search"
msgstr "코드 탐색기에서 보기 “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "더 읽기…"

msgid "page.md5.tabs.downloads"
msgstr "다운로드 (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "대출 (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "메타데이터 탐색 (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "댓글 (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "목록 (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "통계 (%(count)s)"

msgid "common.tech_details"
msgstr "기술 세부 사항"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ 이 파일에 문제가 있을 수 있으며, 소스 라이브러리에서 숨겨졌습니다.</span> 저작권 소유자의 요청으로 숨겨지거나 더 나은 대안 파일이 있을 때 숨겨질 수 있지만, 파일 자체에 문제가 있을 때도 있습니다. 다운로드는 가능할 수 있지만, 먼저 다른 파일을 검색하는 것을 권장합니다. 자세한 내용:"

msgid "page.md5.box.download.better_file"
msgstr "이 파일의 더 나은 버전이 %(link)s에 있을 수 있습니다"

msgid "page.md5.box.issues.text2"
msgstr "이 파일을 여전히 다운로드하려면, 파일을 여는 소프트웨어가 신뢰할 수 있고 최신 업데이트가 돼있는지 확인하세요."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 고속 다운로드"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 고속 다운로드</strong> <a %(a_membership)s>회원</a>이 되어 책, 논문 등을 장기적으로 보존하는 데 도움을 주세요. 여러분의 지원에 감사드리기 위해 빠른 다운로드를 제공합니다. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "이번 달에 기부하시면, 고속 다운로드 횟수가 <strong>두 배</strong>로 증가합니다."

msgid "page.md5.box.download.header_fast_member"
msgstr "오늘 %(remaining)s회 남았습니다. 회원이 되어 주셔서 감사합니다! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "오늘의 고속 다운로드 한도를 초과했습니다."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "이 파일을 최근에 다운로드했습니다. 링크는 한동안 유효합니다."

msgid "page.md5.box.download.option"
msgstr "옵션 #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(리디렉션 없음)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(뷰어로 열기)"

msgid "layout.index.header.banner.refer"
msgstr "친구를 추천하면, 당신과 친구 모두 %(percentage)s%% 보너스 고속 다운로드를 받습니다!"

msgid "layout.index.header.learn_more"
msgstr "자세히 알아보기…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 저속 다운로드"

msgid "page.md5.box.download.trusted_partners"
msgstr "신뢰할 수 있는 파트너로부터."

msgid "page.md5.box.download.slow_faq"
msgstr "<a %(a_slow)s>FAQ</a>에서 더 많은 정보를 확인하세요."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(<a %(a_browser)s> 브라우저 확인이 필요할 수 있습니다</a> - 무제한 다운로드!)"

msgid "page.md5.box.download.after_downloading"
msgstr "다운로드 후:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "내부 뷰어로 열기"

msgid "page.md5.box.external_downloads"
msgstr "외부 다운로드 보기"

msgid "page.md5.box.download.header_external"
msgstr "외부 다운로드"

msgid "page.md5.box.download.no_found"
msgstr "다운로드를 찾을 수 없습니다."

msgid "page.md5.box.download.no_issues_notice"
msgstr "모든 다운로드 옵션은 동일한 파일을 제공하며, 사용하기에 안전합니다. 그렇지만 인터넷에서 파일을 다운로드할 때, 특히 Anna’s Archive가 아닌 외부 사이트에서 다운로드할 때는 항상 주의하십시오. 예를 들어, 기기가 최신 업데이트가 돼있는지 확인하세요."

msgid "page.md5.box.download.dl_managers"
msgstr "대용량 파일의 경우, 중도에 멈추는 것을 방지하기 위해 다운로드 매니저를 사용하는 것을 권장합니다."

msgid "page.md5.box.download.dl_managers.links"
msgstr "추천 다운로드 매니저: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "파일 형식에 따라 파일을 열기 위해 전자책 또는 PDF 리더가 필요합니다."

msgid "page.md5.box.download.readers.links"
msgstr "추천 전자책 리더: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "안나의 아카이브 온라인 뷰어"

msgid "page.md5.box.download.conversion"
msgstr "포맷 간 변환을 위해 온라인 도구를 사용하세요."

msgid "page.md5.box.download.conversion.links"
msgstr "추천 변환 도구: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "PDF 및 EPUB 파일을 Kindle 또는 Kobo eReader로 보낼 수 있습니다."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "추천 도구: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon의 “Send to Kindle”"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz의 “Send to Kobo/Kindle”"

msgid "page.md5.box.download.support"
msgstr "작가와 도서관을 지원하세요"

msgid "page.md5.box.download.support.authors"
msgstr "이것이 마음에 들고 여유가 있다면, 원본을 구매하거나 작가를 직접 지원하는 것을 고려해보세요."

msgid "page.md5.box.download.support.libraries"
msgstr "이 책이 지역 도서관에 있다면, 무료로 대출해 보세요."

msgid "page.md5.quality.header"
msgstr "파일 품질"

msgid "page.md5.quality.report"
msgstr "이 파일의 품질을 신고하여 커뮤니티를 도와주세요! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "파일 문제 신고 (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "훌륭한 파일 품질 (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "댓글 추가 (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "이 파일에 어떤 문제가 있나요?"

msgid "page.md5.quality.copyright"
msgstr "<a %(a_copyright)s>DMCA / 저작권 클레임 양식</a>을 사용해 주세요."

msgid "page.md5.quality.describe_the_issue"
msgstr "문제를 설명해 주세요 (필수)"

msgid "page.md5.quality.issue_description"
msgstr "문제 설명"

msgid "page.md5.quality.better_md5.text1"
msgstr "이 파일의 더 나은 버전의 MD5 (해당되는 경우)."

msgid "page.md5.quality.better_md5.text2"
msgstr "이 파일과 매우 유사한 다른 파일(같은 판, 같은 파일 확장자)을 찾을 수 있다면, 사람들이 이 파일 대신 사용할 수 있도록 여기에 입력해 주세요. Anna’s Archive 외부에 더 나은 버전의 파일이 있다면, <a %(a_upload)s>업로드</a>해 주세요."

msgid "page.md5.quality.better_md5.line1"
msgstr "URL에서 md5를 얻을 수 있습니다, e.g."

msgid "page.md5.quality.submit_report"
msgstr "신고 제출"

msgid "page.md5.quality.improve_the_metadata"
msgstr "<a %(a_metadata)s>이 파일의 메타데이터를 개선</a>하는 방법을 배우세요."

msgid "page.md5.quality.report_thanks"
msgstr "신고 해주셔서 감사합니다. 신고는 (적절한 관리 시스템이 마련될 때까지는) Anna가 수동으로 검토할 것입니다."

msgid "page.md5.quality.report_error"
msgstr "문제가 발생했습니다. 페이지를 새로고침하고 다시 시도해 주세요."

msgid "page.md5.quality.great.summary"
msgstr "이 파일의 품질이 뛰어나다면, 여기에서 무엇이든 논의할 수 있습니다! 그렇지 않다면, \"파일 문제 신고\" 버튼을 사용해 주세요."

msgid "page.md5.quality.loved_the_book"
msgstr "이 책을 정말 좋아했습니다!"

msgid "page.md5.quality.submit_comment"
msgstr "댓글 남기기"

msgid "common.english_only"
msgstr "텍스트는 아래에서 영어로 이어집니다."

msgid "page.md5.text.stats.total_downloads"
msgstr "총 다운로드 수: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "\"파일 MD5\"는 파일 내용을 바탕으로 계산된 해시로, 파일 내용에 고유합니다. 우리가 여기에서 색인화한 모든 섀도우 라이브러리들은 주로 MD5를 사용하여 파일을 식별합니다."

msgid "page.md5.text.md5_info.text2"
msgstr "파일은 여러 섀도우 라이브러리에 나타날 수 있습니다. 우리가 컴파일한 다양한 Datasets에 대한 정보는 <a %(a_datasets)s>Datasets 페이지</a>를 참조하세요."

msgid "page.md5.text.ia_info.text1"
msgstr "이 파일은 <a %(a_ia)s>IA의 디지털 대출</a> 라이브러리에서 관리되며, 검색을 위해 Anna’s Archive에 색인되었습니다. 우리가 컴파일한 다양한 Datasets에 대한 정보는 <a %(a_datasets)s>Datasets 페이지</a>를 참조하세요."

msgid "page.md5.text.file_info.text1"
msgstr "이 특정 파일에 대한 정보는 <a %(a_href)s>JSON 파일</a>을 확인하세요."

msgid "page.aarecord_issue.title"
msgstr "🔥 페이지 로딩 문제"

msgid "page.aarecord_issue.text"
msgstr "다시 시도하려면 새로고침하세요. 문제가 여러 시간 동안 지속되면 <a %(a_contact)s>문의하기</a>를 이용하세요."

msgid "page.md5.invalid.header"
msgstr "찾을 수 없음"

msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s”이(가) 데이터베이스에서 찾을 수 없습니다."

msgid "page.login.title"
msgstr "로그인 / 회원가입"

msgid "page.browserverification.header"
msgstr "브라우저 인증"

msgid "page.login.text1"
msgstr "스팸 봇이 다수의 계정을 생성하는 것을 방지하기 위해, 먼저 브라우저를 확인해야 합니다."

msgid "page.login.text2"
msgstr "무한 루프에 걸리면 <a %(a_privacypass)s>Privacy Pass</a>를 설치하는 것을 권장합니다."

msgid "page.login.text3"
msgstr "광고 차단기 및 기타 브라우저 확장 기능을 끄는 것도 도움이 될 수 있습니다."

msgid "page.codes.title"
msgstr "코드"

msgid "page.codes.heading"
msgstr "코드 탐색기"

#, fuzzy
msgid "page.codes.intro"
msgstr "레코드에 태그된 코드를 접두사별로 탐색하세요. \"레코드\" 열은 검색 엔진에서 주어진 접두사를 가진 코드로 태그된 레코드 수를 보여줍니다(메타데이터 전용 레코드 포함). \"코드\" 열은 주어진 접두사를 가진 실제 코드의 수를 보여줍니다."

msgid "page.codes.why_cloudflare"
msgstr "이 페이지는 생성하는 데 적지않은 시간이 걸리므로, Cloudflare 캡차가 필요합니다. <a %(a_donate)s>회원</a>은 캡차를 건너뛸 수 있습니다."

msgid "page.codes.dont_scrape"
msgstr "이 페이지를 스크랩하지 마세요. ElasticSearch 및 MariaDB 데이터베이스를 <a %(a_import)s>생성</a>하거나 <a %(a_download)s>다운로드</a>하여 사용하고, 우리의 <a %(a_software)s>오픈 소스 코드</a>를 실행하는 것을 대신 권장합니다. 원시 데이터(Raw data)는 <a %(a_json_file)s>이 파일</a>과 같은 JSON 파일을 통해 수동으로 탐색할 수 있습니다."

msgid "page.codes.prefix"
msgstr "접두사"

msgid "common.form.go"
msgstr "이동"

msgid "common.form.reset"
msgstr "재설정"

msgid "page.codes.search_archive_start"
msgstr "안나의 아카이브 검색"

msgid "page.codes.bad_unicode"
msgstr "경고: 코드에 잘못된 유니코드 문자가 포함되어 있어 다양한 상황에서 올바르게 작동하지 않을 수 있습니다. URL의 base64 표현에서 미가공 바이너리를 디코딩할 수 있습니다."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "알려진 코드 접두사 “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "접두사"

msgid "page.codes.code_label"
msgstr "라벨"

msgid "page.codes.code_description"
msgstr "설명"

msgid "page.codes.code_url"
msgstr "특정 코드의 URL"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s”는 코드의 값으로 대체됩니다"

msgid "page.codes.generic_url"
msgstr "일반 URL"

msgid "page.codes.code_website"
msgstr "웹사이트"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s개의 기록이 “%(prefix_label)s”와(과) 일치합니다"

msgid "page.codes.url_link"
msgstr "특정 코드의 URL: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "더 보기…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "“%(prefix_label)s”로 시작하는 코드"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "색인"

msgid "page.codes.records_prefix"
msgstr "기록"

msgid "page.codes.records_codes"
msgstr "코드"

msgid "page.codes.fewer_than"
msgstr "%(count)s개 미만의 기록"

msgid "page.contact.dmca.form"
msgstr "DMCA / 저작권 클레임은 <a %(a_copyright)s>이 양식</a>을 사용하세요."

msgid "page.contact.dmca.delete"
msgstr "다른 연락 수단을 통한 저작권 클레임은 자동으로 삭제됩니다."

msgid "page.contact.checkboxes.text1"
msgstr "여러분의 피드백과 질문을 환영합니다!"

msgid "page.contact.checkboxes.text2"
msgstr "그러나 스팸 및 불필요한 이메일이 많기 때문에, 저희에게 연락하는 조건을 이해했음을 확인하는 체크박스들을 체크해 주세요."

msgid "page.contact.checkboxes.copyright"
msgstr "이 이메일로 오는 저작권 클레임은 무시됩니다. 양식을 사용하세요."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "파트너 서버는 호스팅 종료로 인해 사용할 수 없습니다. 곧 다시 사용할 수 있을 것입니다."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "멤버십은 해당 상황을 반영하여 연장될 것입니다."

msgid "layout.index.footer.dont_email"
msgstr "<a %(a_request)s>도서 요청</a>이나 소량의 (<10k) <a %(a_upload)s>업로드</a>를 위해 이메일을 보내지 마세요."

msgid "page.donate.please_include"
msgstr "계정 또는 기부 관련 질문을 할 때 계정 ID, 스크린샷, 영수증 등 가능한 한 많은 정보를 추가해 주세요. 저희는 이메일을 1-2주에 한 번만 확인하므로, 이 정보를 포함하지 않으면 해결이 지연될 수 있습니다."

msgid "page.contact.checkboxes.show_email_button"
msgstr "이메일 주소 확인"

msgid "page.copyright.title"
msgstr "DMCA / 저작권 클레임 양식"

msgid "page.copyright.intro"
msgstr "DMCA 또는 기타 저작권 클레임이 있는 경우, 가능한 한 정확하게 이 양식을 작성해 주십시오. 문제가 발생하면 전용 DMCA 주소 %(email)s로 문의해 주십시오. 이 주소로 이메일을 보내는 클레임은 처리되지 않으며, 질문만 받습니다. 클레임을 제출하려면 아래 양식을 사용해 주십시오."

msgid "page.copyright.form.aa_urls"
msgstr "안나의 아카이브의 URL(필수). 한 줄에 하나씩. 동일한 책의 동일한 판본을 설명하는 URL만 포함해 주십시오. 여러 책이나 여러 판본에 대한 클레임을 제출하려면 이 양식을 여러 번 제출해 주십시오."

msgid "page.copyright.form.aa_urls.note"
msgstr "여러 책이나 판본을 묶어서 제출한 클레임은 거부됩니다."

msgid "page.copyright.form.name"
msgstr "이름 (필수)"

msgid "page.copyright.form.address"
msgstr "주소 (필수)"

msgid "page.copyright.form.phone"
msgstr "전화번호 (필수)"

msgid "page.copyright.form.email"
msgstr "이메일 (필수)"

msgid "page.copyright.form.description"
msgstr "출처 자료에 대한 명확한 설명 (필수)"

msgid "page.copyright.form.isbns"
msgstr "출처 자료의 ISBN(해당되는 경우). 한 줄에 하나씩. 저작권 클레임을 보고하는 판본과 정확히 일치하는 것만 포함해 주십시오."

msgid "page.copyright.form.openlib_urls"
msgstr "출처 자료의 <a %(a_openlib)s>Open Library</a> URL, 한 줄에 하나씩. 출처 자료를 찾기 위해 Open Library를 검색해 주십시오. 이는 클레임을 확인하는 데 도움이 됩니다."

msgid "page.copyright.form.external_urls"
msgstr "출처 자료의 URL, 한 줄에 하나씩 (필수). 가능한 많이 포함해 주십시오. 클레임을 확인하는 데 도움이 됩니다 (예: Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "진술 및 서명 (필수)"

msgid "page.copyright.form.submit_claim"
msgstr "클레임 제출"

msgid "page.copyright.form.on_success"
msgstr "✅ 저작권 클레임을 제출해 주셔서 감사합니다. 가능한 한 빨리 검토하겠습니다. 다른 클레임을 제출하려면 페이지를 새로 고침해 주십시오."

msgid "page.copyright.form.on_failure"
msgstr "❌ 문제가 발생했습니다. 페이지를 새로 고침하고 다시 시도해 주십시오."

msgid "page.datasets.title"
msgstr "Datasets"

msgid "page.datasets.common.intro"
msgstr "이 데이터셋을 <a %(a_archival)s>보관</a> 또는 <a %(a_llm)s>LLM 훈련</a> 목적으로 미러링하는 데 관심이 있으시면, 저희에게 연락해 주세요."

msgid "page.datasets.intro.text2"
msgstr "우리의 사명은 전 세계의 모든 책(논문, 잡지 등 포함)을 아카이브하고 널리 접근 가능하게 만드는 것입니다. 모든 책은 되찾을 수 있는 회복력을 보장하기 위해 널리 미러링되어야 한다고 믿습니다. 이것이 우리가 다양한 출처에서 파일을 모으는 이유입니다. 일부 출처는 완전히 개방되어 대량으로 미러링할 수 있습니다(예: Sci-Hub). 다른 출처는 폐쇄적이고 보호적이어서, 그들의 책을 \"해방\"하기 위해 스크래핑을 시도합니다. 또 다른 출처는 그 중간 어디쯤에 위치합니다."

msgid "page.datasets.intro.text3"
msgstr "우리의 모든 데이터는 <a %(a_torrents)s>토렌트</a>로 다운로드할 수 있으며, 모든 메타데이터는 ElasticSearch 및 MariaDB 데이터베이스로 <a %(a_anna_software)s>생성</a>하거나 <a %(a_elasticsearch)s>다운로드</a>할 수 있습니다. 원시 데이터는 <a %(a_dbrecord)s>이와 같은</a> JSON 파일을 통해 수동으로 탐색할 수 있습니다."

msgid "page.datasets.overview.title"
msgstr "개요"

msgid "page.datasets.overview.text1"
msgstr "아래는 안나의 아카이브에 있는 파일들의 출처에 대한 간략한 개요입니다."

msgid "page.datasets.overview.source.header"
msgstr "출처"

msgid "page.datasets.overview.size.header"
msgstr "크기"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% AA에 의해 미러링됨 / 토렌트 사용 가능"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "파일 수의 백분율"

msgid "page.datasets.overview.last_updated.header"
msgstr "마지막 업데이트"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "논픽션 및 픽션"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s개의 파일"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Libgen.li “scimag”을 통해"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: 2021년 이후 동결; 대부분 토렌트를 통해 이용 가능"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: 그 이후로 소규모 추가</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "“scimag” 제외"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "픽션 토렌트는 뒤처져 있습니다 (ID ~4-6M은 Zlib 토렌트와 겹치기 때문에 토렌트되지 않았습니다)."

msgid "page.datasets.zlibzh.searchable"
msgstr "Z-Library의 “중국어” 컬렉션은 DuXiu 컬렉션과 동일한 것으로 보이지만, MD5가 다릅니다. 중복을 피하기 위해 이러한 파일을 토렌트에서 제외하지만, 여전히 검색 인덱스에 표시합니다."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA 디지털 대출"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+의 파일이 검색 가능합니다."

msgid "page.datasets.overview.total"
msgstr "총"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "중복 제외"

msgid "page.datasets.overview.text4"
msgstr "섀도우 라이브러리는 종종 서로 데이터를 동기화하기 때문에 라이브러리 간에 상당한 중복이 있습니다. 그래서 숫자가 총계와 일치하지 않습니다."

msgid "page.datasets.overview.text5"
msgstr "“안나의 아카이브에서 미러링 및 시드된” 비율은 우리가 직접 미러링하는 파일의 수를 보여줍니다. 우리는 이러한 파일을 토렌트를 통해 대량으로 시드하고, 파트너 웹사이트를 통해 직접 다운로드할 수 있도록 제공합니다."

msgid "page.datasets.source_libraries.title"
msgstr "출처 도서관"

msgid "page.datasets.source_libraries.text1"
msgstr "일부 소스 도서관은 토렌트를 통해 데이터를 대량으로 공유하는 것을 장려하는 반면, 다른 도서관은 컬렉션을 쉽게 공유하지 않습니다. 후자의 경우, 안나의 아카이브는 그들의 컬렉션을 스크랩하여 제공하려고 합니다 (자세한 내용은 <a %(a_torrents)s>토렌트</a> 페이지를 참조하세요). 또한, 소스 도서관이 공유할 의사는 있지만 자원이 부족한 경우와 같은 애매한 상황들도 있습니다. 이러한 경우에도 저희는 도움을 주려고 노력합니다."

msgid "page.datasets.source_libraries.text2"
msgstr "아래는 다양한 소스 도서관과 우리의 관계에 대한 간략한 개요입니다."

msgid "page.datasets.sources.source.header"
msgstr "소스"

msgid "page.datasets.sources.files.header"
msgstr "파일"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s 일간 <a %(dbdumps)s>HTTP 데이터베이스 덤프</a>"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s <a %(nonfiction)s>논픽션</a> 및 <a %(fiction)s>픽션</a>을 위한 자동화된 토렌트"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s 안나의 아카이브는 <a %(covers)s>책 표지 토렌트</a> 컬렉션을 관리합니다"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub은 2021년 이후로 새로운 파일을 동결했습니다."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s 메타데이터 덤프는 <a %(scihub1)s>여기</a>와 <a %(scihub2)s>여기</a>에서 사용할 수 있으며, (우리가 사용하는) <a %(libgenli)s>Libgen.li 데이터베이스</a>의 일부로도 제공됩니다"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s 데이터 토렌트는 <a %(scihub1)s>여기</a>, <a %(scihub2)s>여기</a>, 그리고 <a %(libgenli)s>여기</a>에서 사용할 수 있습니다"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s 일부 새로운 파일은 Libgen의 “scimag”에 <a %(libgenrs)s>추가되고</a> <a %(libgenli)s>있지만</a>, 새로운 토렌트를 만들 만큼 충분하지는 않습니다"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s 분기별 <a %(dbdumps)s>HTTP 데이터베이스 덤프</a>"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s 논픽션 토렌트는 Libgen.rs와 공유되며 (그리고 <a %(libgenli)s>여기</a>에 미러링됨)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s 안나의 아카이브와 Libgen.li는 <a %(comics)s>만화책</a>, <a %(magazines)s>잡지</a>, <a %(standarts)s>표준 문서</a>, 그리고 <a %(fiction)s>소설 (Libgen.rs에서 분리됨)</a> 컬렉션을 공동으로 관리합니다."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s 그들의 \"fiction_rus\" 컬렉션(러시아 소설)은 전용 토렌트가 없지만, 다른 사람들의 토렌트로 커버되며, 우리는 <a %(fiction_rus)s>미러</a>를 유지합니다."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s 안나의 아카이브와 Z-Library는 <a %(metadata)s>Z-Library 메타데이터</a> 및 <a %(files)s>Z-Library 파일</a> 컬렉션을 공동으로 관리합니다"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s 일부 메타데이터는 <a %(openlib)s>Open Library 데이터베이스 덤프</a>를 통해 사용할 수 있지만, 전체 IA 컬렉션을 포함하지는 않습니다"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s 전체 컬렉션에 대해 쉽게 접근할 수 있는 메타데이터 덤프가 없습니다"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s 안나의 아카이브는 <a %(ia)s>IA 메타데이터</a> 컬렉션을 관리합니다"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s 파일은 제한된 방식으로만 대여 가능하며, 다양한 접근 제한들이 있습니다"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s 안나의 아카이브는 <a %(ia)s>IA 파일</a> 컬렉션을 관리합니다"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s 중국 인터넷에 흩어져 있는 다양한 메타데이터 데이터베이스; 많은 경우 유료 데이터베이스임"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s 전체 컬렉션에 대해 쉽게 접근할 수 있는 메타데이터 덤프가 없습니다."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s 안나의 아카이브는 <a %(duxiu)s>DuXiu 메타데이터</a> 컬렉션을 관리합니다"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s 중국 인터넷에 흩어져 있는 다양한 파일 데이터베이스; 많은 경우 유료 데이터베이스임"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s 대부분의 파일은 프리미엄 BaiduYun 계정을 사용해야 접근 가능하며, 다운로드 속도가 느립니다."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s 안나의 아카이브는 <a %(duxiu)s>DuXiu 파일</a> 컬렉션을 관리합니다"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s 다양한 소규모 또는 일회성 소스. 우리는 사람들이 다른 쉐도우 라이브러리에 먼저 업로드하도록 권장하지만, 때로는 다른 사람들이 정리하기에는 너무 큰 컬렉션을 가지고 있지만, 자체 카테고리를 만들기에는 충분하지 않은 경우가 있습니다."

msgid "page.datasets.metadata_only_sources.title"
msgstr "메타데이터 전용 소스"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "저희는 또한 ISBN 번호나 기타 필드를 사용하여 파일과 일치시킬 수 있는 메타데이터 전용 소스로 컬렉션을 풍부화 합니다. 아래는 그 개요입니다. 다시 말하지만, 이러한 소스 중 일부는 완전히 개방되어 있는 반면, 다른 소스는 스크랩해야 합니다."

msgid "page.faq.metadata.inspiration"
msgstr "메타데이터를 수집하는 우리의 영감은 Aaron Swartz가 “출판된 모든 책 한 권 당 웹 페이지 하나”라는 목표로 만든 <a %(a_openlib)s>Open Library</a>에서 비롯되었습니다. 그 프로젝트는 잘 진행되고 있지만, 우리의 독특한 포지션은 그들이 얻을 수 없는 메타데이터를 얻을 수 있게 합니다. 또 다른 영감은 <a %(a_blog)s>세계에 얼마나 많은 책이 있는지</a>를 알고 싶어하는 우리의 욕구에서 비롯되었습니다. 이를 통해 우리가 아직 구해야 할 책이 얼마나 남았는지 계산할 수 있습니다."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "메타데이터 검색에서는 원본 기록을 표시합니다. 기록을 병합하지 않습니다."

msgid "page.datasets.sources.last_updated.header"
msgstr "마지막 업데이트"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s 월간 <a %(dbdumps)s>데이터베이스 덤프</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s 대량으로 직접 제공되지 않으며, 스크래핑 방지 기능이 있습니다"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s 안나의 아카이브는 <a %(worldcat)s>OCLC (WorldCat) 메타데이터</a> 컬렉션을 관리합니다"

msgid "page.datasets.unified_database.title"
msgstr "통합 데이터베이스"

msgid "page.datasets.unified_database.text1"
msgstr "우리는 위의 모든 소스를 하나의 통합 데이터베이스로 결합하여 이 웹사이트를 제공합니다. 이 통합 데이터베이스는 직접적으로 제공되지 않지만, 안나의 아카이브는 완전히 오픈 소스이므로 ElasticSearch 및 MariaDB 데이터베이스로 <a %(a_generated)s>생성</a>하거나 <a %(a_downloaded)s>다운로드</a>할 수 있습니다. 해당 페이지의 스크립트는 위에서 언급한 소스에서 필요한 모든 메타데이터를 자동으로 다운로드합니다."

msgid "page.datasets.unified_database.text2"
msgstr "로컬에서 스크립트를 실행하기 전에 데이터를 탐색하고 싶다면, 다른 JSON 파일로 연결되는 저희 JSON 파일을 확인할 수 있습니다. <a %(a_json)s>이 파일</a>이 좋은 시작점입니다."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "저희의 <a %(a_href)s>블로그 게시물</a>에서 발췌했습니다."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a>는 <a %(superstar_link)s>SuperStar Digital Library Group</a>에서 만든 방대한 스캔 도서 데이터베이스입니다. 대부분은 대학과 도서관에 디지털로 제공하기 위해 스캔된 학술 도서입니다. 영어를 사용하는 독자를 위해 <a %(princeton_link)s>프린스턴</a>과 <a %(uw_link)s>워싱턴 대학교</a>에서 좋은 개요를 제공하고 있습니다. 또한 배경 정보를 제공하는 훌륭한 기사도 있습니다: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Duxiu의 도서들은 오랫동안 중국 인터넷에서 불법 복제되어 왔습니다. 보통 재판매자들이 1달러 이하로 판매하고 있습니다. 이들은 종종 더 많은 저장 공간을 허용하도록 해킹된 중국판 구글 드라이브같은 서비스를 사용하여 배포됩니다. 몇 가지 기술적인 세부 사항은 <a %(link1)s>여기</a>와 <a %(link2)s>여기</a>에서 찾을 수 있습니다."

msgid "page.datasets.duxiu.description3"
msgstr "비록 도서들이 반공개적으로 배포되었지만, 대량으로 얻는 것은 매우 어렵습니다. 우리는 이를 TODO 목록의 상위에 두고, 여러 달 동안 전담 작업을 할당했습니다. 그러나 2023년 말에 놀랍고, 놀라운 재능을 가진 자원봉사자가 우리에게 연락하여 이미 이 모든 작업을 큰 비용을 들여 완료했다고 알려주었습니다. 그들은 장기 보존을 약속하는 것 외에는 아무것도 기대하지 않고 전체 컬렉션을 우리와 공유했습니다. 정말로 놀라운 일입니다."

msgid "page.datasets.common.resources"
msgstr "자원"

msgid "page.datasets.common.total_files"
msgstr "총 파일 수: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "총 파일 크기: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Anna’s Archive에서 미러링된 파일: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "마지막 업데이트: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "안나의 아카이브의 토렌트"

msgid "page.datasets.common.aa_example_record"
msgstr "안나의 아카이브의 예시 기록"

msgid "page.datasets.duxiu.blog_post"
msgstr "이 데이터에 대한 우리의 블로그 게시물"

msgid "page.datasets.common.import_scripts"
msgstr "메타데이터 가져오기 스크립트"

msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers 형식"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "자원봉사자들로부터의 추가 정보 (미가공 노트):"

msgid "page.datasets.ia.title"
msgstr "IA 디지털 대출"

msgid "page.datasets.ia.description"
msgstr "이 데이터셋은 <a %(a_datasets_openlib)s>Open Library 데이터셋</a>과 밀접하게 관련되어 있습니다. 모든 메타데이터와 IA의 디지털 대출 도서관의 많은 파일을 스크랩한 내용을 포함하고 있습니다. 업데이트는 <a %(a_aac)s>Anna’s Archive Container 형식</a>으로 릴리스됩니다."

msgid "page.datasets.ia.description2"
msgstr "이 기록은 Open Library 데이터셋에서 직접 참조되지만, Open Library에 없는 기록도 포함되어 있습니다. 또한, 커뮤니티 회원들이 수년간 스크랩한 여러 데이터 파일도 보유하고 있습니다."

msgid "page.datasets.ia.description3"
msgstr "이 컬렉션은 두 부분으로 구성되어 있습니다. 모든 데이터를 얻으려면 두 부분이 모두 필요합니다 (토렌트 페이지에서 삭제된 토렌트는 제외)."

msgid "page.datasets.ia.part1"
msgstr "첫 번째 릴리스로, <a %(a_aac)s>Anna’s Archive Containers (AAC) 형식</a>으로 표준화하기 전입니다. 메타데이터(json 및 xml 형식), pdf(ACSM 및 lcpdf 디지털 대출 시스템에서 가져옴), 표지 썸네일을 포함하고 있습니다."

msgid "page.datasets.ia.part2"
msgstr "AAC를 사용한 점진적 신규 릴리스. 2023-01-01 이후의 타임스탬프가 있는 메타데이터만 포함되며, 나머지는 이미 \"ia\"에 의해 다루어졌습니다. 또한 이번에는 acsm 및 \"bookreader\" (IA의 웹 리더) 대출 시스템에서 가져온 모든 pdf 파일을 포함합니다. 이름이 정확하지 않음에도 불구하고, bookreader 파일을 ia2_acsmpdf_files 컬렉션에 채워넣습니다. 이들은 상호 배타적이기 때문입니다."

msgid "page.datasets.common.main_website"
msgstr "메인 %(source)s 웹사이트"

msgid "page.datasets.ia.ia_lending"
msgstr "디지털 대출 도서관"

msgid "page.datasets.common.metadata_docs"
msgstr "메타데이터 문서화 (대부분의 필드)"

msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN 국가 정보"

msgid "page.datasets.isbn_ranges.text1"
msgstr "국제 ISBN 기관은 정기적으로 국가 ISBN 기관에 할당한 범위를 발표합니다. 이를 통해 이 ISBN이 속한 국가, 지역 또는 언어 그룹을 유추할 수 있습니다. 현재 우리는 <a %(a_isbnlib)s>isbnlib</a> Python 라이브러리를 통해 간접적으로 이 데이터를 사용하고 있습니다."

msgid "page.datasets.isbn_ranges.resources"
msgstr "자료"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "마지막 업데이트: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN 웹사이트"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "메타데이터"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "다양한 Library Genesis 포크의 배경 이야기는 <a %(a_libgen_rs)s>Libgen.rs</a> 페이지를 참조하십시오."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li는 대부분 Libgen.rs와 동일한 콘텐츠와 메타데이터를 포함하고 있지만, 만화, 잡지 및 표준 문서와 같은 일부 컬렉션이 추가로 포함되어 있습니다. 또한 <a %(a_scihub)s>Sci-Hub</a>를 메타데이터와 검색 엔진에 통합하여 우리의 데이터베이스에 사용하고 있습니다."

msgid "page.datasets.libgen_li.description3"
msgstr "이 도서관의 메타데이터는 <a %(a_libgen_li)s>libgen.li에서</a> 무료로 이용할 수 있습니다. 그러나 이 서버는 느리고 중단된 연결을 재개할 수 없습니다. 동일한 파일은 <a %(a_ftp)s>FTP 서버</a>에서도 이용할 수 있으며, 이쪽이 더 잘 작동합니다."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "대부분의 추가 콘텐츠에 대한 토렌트가 제공되며, 특히 만화, 잡지, 표준 문서에 대한 토렌트는 안나의 아카이브와의 협력으로 출시되었습니다."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "소설 컬렉션은 %(start)s에서 시작하며, <a %(a_href)s>Libgen.rs</a>와는 다른 자체 토렌트를 가지고 있습니다."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Libgen.li 관리자에 따르면, \"fiction_rus\" (러시아 소설) 컬렉션은 <a %(a_booktracker)s>booktracker.org</a>에서 정기적으로 출시되는 토렌트, 특히 <a %(a_flibusta)s>flibusta</a>와 <a %(a_librusec)s>lib.rus.ec</a> 토렌트로 커버가 될 것입니다. (우리는 <a %(a_torrents)s>여기</a>에서 미러링하지만, 어떤 토렌트가 어떤 파일에 해당하는지는 아직 확인하지 않았습니다)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "모든 컬렉션에 대한 통계는 <a %(a_href)s>libgen의 웹사이트</a>에서 찾을 수 있습니다."

msgid "page.datasets.libgen_li.description4.1"
msgstr "논픽션도 새로운 토렌트 없이 분기된 것으로 보입니다. 확실하지는 않지만, 이는 2022년 초부터 발생한 것으로 보입니다."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "토렌트가 없는 특정 범위(예: 소설 범위 f_3463000에서 f_4260000)는 Z-Library(또는 다른 사본) 파일일 가능성이 높습니다. 하지만 우리는 중복 제거를 하고 이 범위의 lgli-고유 파일에 대한 토렌트를 만드려 할 수 있습니다."

msgid "page.datasets.libgen_li.description5"
msgstr "“libgen.is”를 참조하는 토렌트 파일은 <a %(a_libgen)s>Libgen.rs</a>의 미러임을 명시적으로 나타냅니다 (“.is”는 Libgen.rs에서 사용하는 다른 도메인입니다)."

msgid "page.datasets.libgen_li.description6"
msgstr "메타데이터 사용에 유용한 리소스는 <a %(a_href)s>이 페이지</a>입니다."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "안나의 아카이브의 소설 토렌트"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "안나의 아카이브의 만화 토렌트"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "안나의 아카이브의 잡지 토렌트"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "안나의 아카이브의 표준 문서 토렌트"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "안나의 아카이브의 러시아 소설 토렌트"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "메타데이터"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "FTP를 통한 메타데이터"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "메타데이터 필드 정보"

msgid "page.datasets.libgen_li.mirrors"
msgstr "다른 토렌트의 미러 (및 고유한 소설 및 만화 토렌트)"

msgid "page.datasets.libgen_li.forum"
msgstr "토론 포럼"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "만화책 릴리스 관련 블로그 게시물"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "다양한 Library Genesis (또는 “Libgen”) 포크의 이야기를 간단하게 요약하자면, 시간이 지남에 따라 Library Genesis에 참여한 사람들이 의견 차이로 인해 각자의 길을 갔다는 것입니다."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "“.fun” 버전은 원래 창립자에 의해 만들어졌습니다. 새로운, 더 분산된 버전을 위해 개편되고 있습니다."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "“.rs” 버전은 매우 유사한 데이터를 가지고 있으며, 대부분의 경우 컬렉션을 대량 토렌트로 일관되게 릴리스합니다. 대략 “소설”과 “비소설” 섹션으로 나뉩니다."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "원본은 \"http://gen.lib.rus.ec\"에 있음."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "<a %(a_li)s>“.li” 버전</a>은 대량 다운로드를 통한 토렌트로 아직 제공되지 않는 방대한 만화 컬렉션과 기타 콘텐츠를 보유하고 있습니다. 별도의 소설 책 토렌트 컬렉션이 있으며, 데이터베이스에 <a %(a_scihub)s>Sci-Hub</a>의 메타데이터를 포함하고 있습니다."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "이 <a %(a_mhut)s>포럼 게시물</a>에 따르면, Libgen.li는 원래 \"http://free-books.dontexist.com\"에 호스팅되었습니다."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a>또한, 다른 이름을 사용하기는 하지만, Library Genesis의 포크입니다."

msgid "page.datasets.libgen_rs.description.about"
msgstr "이 페이지는 “.rs” 버전에 관한 것입니다. 이 버전은 메타데이터와 도서 카탈로그의 전체 내용을 일관되게 게시하는 것으로 알려져 있습니다. 도서 컬렉션은 소설과 비소설 부분으로 나뉩니다."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "메타데이터 사용에 관련한 유용한 리소스는 <a %(a_metadata)s>이 페이지</a>입니다 (IP 범위를 차단하므로 VPN이 필요할 수 있습니다)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "2024년 3월 현재, 새로운 토렌트는 <a %(a_href)s>이 포럼 스레드</a>에 게시되고 있습니다 (IP 범위를 차단하므로 VPN이 필요할 수 있습니다)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "안나의 아카이브의 논픽션 토렌트"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "안나의 아카이브의 픽션 토렌트"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs 메타데이터"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs 메타데이터 필드 정보"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs 논픽션 토렌트"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs 픽션 토렌트"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs 토론 포럼"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "안나의 아카이브의 토렌트 (책 표지)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "책 표지 릴리스 관련 블로그"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis는 이미 토렌트를 통해 데이터를 대량으로 제공하는 것으로 잘 알려져 있습니다. 우리의 Libgen 컬렉션은 그들이 직접 공개하지 않는 보조 데이터를 포함하며, 그들과의 협력으로 이루어졌습니다. Library Genesis와 협력해 주신 모든 분들께 감사드립니다!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "릴리스 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "이 <a %(blog_post)s>첫 번째 릴리스</a>는 꽤 작습니다: 약 300GB의 Libgen.rs 포크에서 가져온 책 표지, 픽션과 논픽션들이 모두 포함되어 있습니다. 이들은 libgen.rs에 나타나는 방식과 동일하게 정리되어 있습니다. 예시:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "논픽션: %(example)s."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "픽션: %(example)s."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Z-Library 컬렉션과 마찬가지로, 우리는 모든 파일을 큰 .tar 파일에 넣었으며, 파일을 직접 제공하려면 <a %(a_ratarmount)s>ratarmount</a>를 사용하여 마운트할 수 있습니다."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a>은 비영리 단체인 <a %(a_oclc)s>OCLC</a>가 소유한 데이터베이스로, 전 세계 도서관의 메타데이터 기록을 집계합니다. 이는 아마도 세계에서 가장 큰 도서관 메타데이터 컬렉션일 것입니다."

msgid "page.datasets.worldcat.description2.label"
msgstr "2023년 10월, 첫 릴리즈:"

msgid "page.datasets.worldcat.description2"
msgstr "2023년 10월에 우리는 <a %(a_scrape)s>OCLC (WorldCat) 데이터베이스의 포괄적인 스크랩</a>을 <a %(a_aac)s>Anna’s Archive Containers 형식</a>으로 공개했습니다."

msgid "page.datasets.worldcat.torrents"
msgstr "안나의 아카이브의 토렌트"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "이 데이터에 대한 우리의 블로그 게시물"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library는 전 세계의 모든 책을 카탈로그화하기 위해 Internet Archive에서 만든 오픈 소스 프로젝트입니다. 세계에서 가장 큰 책 스캔 작업 중 하나를 보유하고 있으며, 많은 책을 디지털 대출로 제공하고 있습니다. 그 책 메타데이터 카탈로그는 무료로 다운로드할 수 있으며, 안나의 아카이브에 포함되어 있습니다 (현재 검색에는 포함되지 않지만, Open Library ID로 명시적으로 검색할 경우 제외)."

msgid "page.datesets.openlib.link_metadata"
msgstr "메타데이터"

msgid "page.datasets.isbndb.release1.title"
msgstr "릴리스 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "이것은 2022년 9월 동안 isbndb.com에 대한 많은 호출의 덤프입니다. 모든 ISBN 범위를 다루려고 했습니다. 약 3,090만 개의 기록이 있습니다. 그들의 웹사이트에서는 실제로 3,260만 개의 기록이 있다고 주장하므로, 우리가 일부를 놓쳤거나 <em>그들</em>이 뭔가 잘못하고 있을 수 있습니다."

msgid "page.datasets.isbndb.release1.text2"
msgstr "JSON 응답은 거의 그들의 서버에서 원시 상태로 가져온 것입니다. 우리가 발견한 데이터 품질 문제 중 하나는, \"978-\"로 시작하지 않는 ISBN-13 번호에 대해 여전히 \"isbn\" 필드를 포함하고 있으며, 이는 단순히 첫 3자리 숫자를 잘라낸 ISBN-13 번호(그리고 체크 디지트를 재계산한 것)라는 점입니다. 이것은 명백히 잘못된 것이지만, 그들이 그렇게 하고 있는 것 같아서 우리는 이를 변경하지 않았습니다."

msgid "page.datasets.isbndb.release1.text3"
msgstr "또 다른 잠재적인 문제는 \"isbn13\" 필드에 중복이 있다는 사실입니다. 따라서 데이터베이스의 기본 키로 사용할 수 없습니다. \"isbn13\" + \"isbn\" 필드를 결합하면 고유한 것으로 보입니다."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Sci-Hub에 대한 배경 정보는 <a %(a_scihub)s>공식 웹사이트</a>, <a %(a_wikipedia)s>위키백과 페이지</a>, 그리고 이 <a %(a_radiolab)s>팟캐스트 인터뷰</a>를 참조하세요."

msgid "page.datasets.scihub.description2"
msgstr "Sci-Hub이 <a %(a_reddit)s>2021년부터 동결</a>되었음을 유의하세요. 이전에도 동결된 적이 있지만, 2021년에 몇 백만 개의 논문이 추가되었습니다. 여전히 제한된 수의 논문이 Libgen의 “scimag” 컬렉션에 추가되지만, 새로운 대량 토렌트를 만들 만큼 충분하지는 않습니다."

msgid "page.datasets.scihub.description3"
msgstr "우리는 <a %(a_libgen_li)s>Libgen.li</a>의 “scimag” 컬렉션에서 제공하는 Sci-Hub 메타데이터를 사용합니다. 또한 <a %(a_dois)s>dois-2022-02-12.7z</a> 데이터셋도 사용합니다."

msgid "page.datasets.scihub.description4"
msgstr "“smarch” 토렌트는 <a %(a_smarch)s>더 이상 사용되지 않기때문에,</a> 우리의 토렌트 목록에 포함되지 않습니다."

msgid "page.datasets.scihub.aa_torrents"
msgstr "안나의 아카이브의 토렌트"

msgid "page.datasets.scihub.link_metadata"
msgstr "메타데이터와 토렌트"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Libgen.rs의 토렌트"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Libgen.li의 토렌트"

msgid "page.datasets.scihub.link_paused"
msgstr "Reddit에서의 업데이트 내역"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "위키백과 페이지"

msgid "page.datasets.scihub.link_podcast"
msgstr "팟캐스트 인터뷰"

msgid "page.datasets.upload.title"
msgstr "안나의 아카이브에의 업로드"

msgid "page.datasets.upload.overview"
msgstr "<a %(a1)s>데이터셋 페이지</a> 개요."

msgid "page.datasets.upload.description"
msgstr "다양한 소규모 또는 일회성 소스들입니다. 우리는 사람들이 먼저 다른 쉐도우 라이브러리에 업로드하도록 권장하지만, 때로는 다른 사람들이 분류하기에는 너무 크지만 자체 카테고리를 만들기에는 충분하지 않은 컬렉션을 가진 사람들이 있습니다."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "</q><q>업로드</q> 컬렉션은 더 작은 하위 컬렉션으로 나뉘며, 이는 AACID와 토렌트 이름에 표시됩니다. 모든 하위 컬렉션은 먼저 메인 컬렉션과 중복 항목들이 제거되었지만, 메타데이터 <q>upload_records</q> JSON 파일에는 여전히 원본 파일에 대한 많은 참조가 포함되어 있습니다. 대부분의 하위 컬렉션에서 비(非)서적 파일도 제거되었으며, 일반적으로 <q>upload_records</q> JSON에 기록되어있지 <em>않습니다</em> ."

msgid "page.datasets.upload.subsubcollections"
msgstr "많은 하위 컬렉션 자체가 또 다른 하위 컬렉션(예: 다른 원본 소스에서 온 것)으로 구성되어 있으며, 이는 <q>filepath</q> 필드에 디렉토리로 표시됩니다."

msgid "page.datasets.upload.subs.heading"
msgstr "하위 컬렉션은 다음과 같습니다:"

msgid "page.datasets.upload.subs.subcollection"
msgstr "하위 컬렉션"

msgid "page.datasets.upload.subs.notes"
msgstr "노트"

msgid "page.datasets.upload.action.browse"
msgstr "둘러보기"

msgid "page.datasets.upload.action.search"
msgstr "검색"

msgid "page.datasets.upload.source.aaaaarg"
msgstr "<a %(a_href)s>aaaaarg.fail</a>에서 가져왔습니다. 비교적 완전한 것으로 보입니다. 자원봉사자 <q>cgiym</q>이 제공했습니다."

msgid "page.datasets.upload.source.acm"
msgstr "<a %(a_href)s><q>ACM Digital Library 2020</q></a> 토렌트에서 가져왔습니다. 기존 논문 컬렉션과 상당히 겹치지만, MD5 일치 항목이 거의 없어서 완전히 보관하기로 했습니다."

msgid "page.datasets.upload.source.airitibooks"
msgstr "자원봉사자 <q>j</q>에 의해 수집된 <q>iRead eBooks</q> (<q>ai rit i-books</q>; airitibooks.com)의 스크랩. <a %(a1)s><q>기타 메타데이터 스크랩</q></a>의 <q>airitibooks</q> 메타데이터에 해당합니다."

msgid "page.datasets.upload.source.alexandrina"
msgstr "<a %(a1)s><q>Bibliotheca Alexandrina</q></a> 컬렉션. 일부는 원본 소스에서, 일부는 the-eye.eu에서, 일부는 다른 미러에서 가져왔습니다."

msgid "page.datasets.upload.source.bibliotik"
msgstr "개인 도서 토렌트 웹사이트 <a %(a_href)s>Bibliotik</a> (종종 <q>Bib</q>이라고 불림)에서 가져왔습니다. 도서들은 이름별로 토렌트로 묶여 (A.torrent, B.torrent) the-eye.eu를 통해 배포되었습니다."

msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "자원봉사자 <q>bpb9v</q>가 제공했습니다. <a %(a_href)s>CADAL</a>에 대한 자세한 정보는 <a %(a_duxiu)s>DuXiu 데이터셋 페이지</a>의 노트를 참조하세요."

msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "자원봉사자 <q>bpb9v</q>가 제공한 추가 자료로, 대부분 DuXiu 파일이며 <q>WenQu</q>와 <q>SuperStar_Journals</q> 폴더도 포함되어 있습니다 (SuperStar는 DuXiu의 모회사입니다)."

msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "자원봉사자 <q>cgiym</q>이 제공한 중국어 텍스트로, 다양한 출처에서 가져왔습니다 (하위 디렉토리로 표시됨). 여기에는 대형 중국 출판사인 <a %(a_href)s>China Machine Press</a>의 자료도 포함됩니다."

msgid "page.datasets.upload.source.cgiym_more"
msgstr "자원봉사자 <q>cgiym</q>이 제공한 비중국어 컬렉션 (하위 디렉토리로 표시됨)."

msgid "page.datasets.upload.source.chinese_architecture"
msgstr "자원봉사자 <q>cm</q>에 의해 수집된 중국 건축에 관한 책들: <q>출판사의 네트워크 취약점을 이용하여 얻었지만, 해당 취약점은 이제 막혔습니다</q>. <a %(a1)s><q>기타 메타데이터 스크랩</q></a>의 <q>chinese_architecture</q> 메타데이터에 해당합니다."

msgid "page.datasets.upload.source.degruyter"
msgstr "학술 출판사 <a %(a_href)s>De Gruyter</a>의 도서로, 몇 개의 대형 토렌트에서 수집되었습니다."

msgid "page.datasets.upload.source.docer"
msgstr "<a %(a_href)s>docer.pl</a>의 스크랩 자료로, 도서 및 기타 서면 작업에 중점을 둔 폴란드 파일 공유 웹사이트입니다. 자원봉사자 <q>p</q>가 2023년 말에 스크랩했습니다. 원본 웹사이트에서 좋은 메타데이터를 얻지 못했지만 (파일 확장자조차 없었습니다), 도서와 유사한 파일을 필터링하고 파일 자체에서 메타데이터를 추출할 수 있었습니다."

msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs로, DuXiu에서 직접 수집되었으며 자원봉사자 <q>w</q>가 수집했습니다. 최신 DuXiu 도서만 전자책으로 직접 제공되므로, 대부분 최신 도서일 것입니다."

msgid "page.datasets.upload.source.duxiu_main"
msgstr "자원봉사자 <q>m</q>이 제공한 나머지 DuXiu 파일로, DuXiu 고유의 PDG 형식이 아닌 파일들입니다 (주요 <a %(a_href)s>DuXiu 데이터셋</a>). 많은 원본 소스에서 수집되었으나, 불행히도 파일 경로에 소스를 보존하지 않았습니다."

msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

msgid "page.datasets.upload.source.hentai"
msgstr "자원봉사자 <q>do no harm</q>에 의해 수집된 성인서적들. <a %(a1)s><q>기타 메타데이터 스크랩</q></a>의 <q>hentai</q> 메타데이터에 해당합니다."

msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

msgid "page.datasets.upload.source.japanese_manga"
msgstr "자원봉사자 <q>t</q>가 일본 만화 출판사에서 스크랩한 컬렉션입니다."

msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>룽취안시(市)의 선별된 사법 기록</a>으로, 자원봉사자 <q>c</q>가 제공했습니다."

msgid "page.datasets.upload.source.magzdb"
msgstr "<a %(a_href)s>magzdb.org</a>의 스크랩 자료로, Library Genesis의 동맹 (libgen.rs 홈페이지에 링크됨)이나 파일을 직접 제공하기를 원치 않았습니다. 자원봉사자 <q>p</q>가 2023년 말에 얻었습니다."

msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

msgid "page.datasets.upload.source.misc"
msgstr "다양한 소규모 업로드로, 자체 하위 컬렉션으로 나뉘기에는 너무 작아, 디렉토리로 표시됩니다. <q>oo42hcksBxZYAOjqwGWu</q> 디렉토리는 <a %(a1)s>기타 메타데이터 스크랩</a>에서 <q>czech_oo42hcks</q>에 해당합니다."

msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "러시아 파일 공유 웹사이트 AvaxHome의 전자책."

msgid "page.datasets.upload.source.newsarch_magz"
msgstr "신문과 잡지의 아카이브. <a %(a1)s><q>기타 메타데이터 스크랩</q></a>의 <q>newsarch_magz</q> 메타데이터에 해당합니다."

msgid "page.datasets.upload.source.pdcnet_org"
msgstr "<a %(a1)s>Philosophy Documentation Center</a>의 스크랩."

msgid "page.datasets.upload.source.polish"
msgstr "폴란드 도서들을 원본 릴리스 (<q>scene</q>) 웹사이트에서 직접 수집한 자원봉사자 <q>o</q>의 컬렉션입니다."

msgid "page.datasets.upload.source.shuge"
msgstr "자원봉사자 </p>cgiym</p>과 <p>woz9ts</p>가 수집한 <a %(a_href)s>shuge.org</a>의 컬렉션입니다."

msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“트란토르 제국 도서관(Imperial Library of Trantor)”</a> (가상의 도서관에서 이름을 따옴), 2022년 자원봉사자 <q>t</q>에 의해 스크래핑됨. <a %(a1)s><q>기타 메타데이터 스크랩</q></a> 의 <q>trantor</q>에 해당됩니다."

msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "자원봉사자 <p>woz9ts<p>의 하위-하위 컬렉션(디렉토리로 표시됨): <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (대만의 <a %(a_sikuquanshu)s>Dizhi(迪志)</a>에 의해), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: <p>주로 고품질 전자책 파일을 공유하는 데 중점을 두고 있으며, 일부는 소유자가 직접 조판한 것입니다. 소유자는 2019년에 <a %(a_arrested)s>체포</a>되었고 누군가가 그가 공유한 파일을 모아 컬렉션을 만들었습니다</p>."

msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "자원봉사자 <q>woz9ts</q>가 제공한 DuXiu 파일 중 DuXiu 독점 PDG 형식이 아닌 파일들 (PDF로 변환돼야함)."

msgid "page.datasets.upload.aa_torrents"
msgstr "안나의 아카이브 토렌트"

msgid "page.datasets.zlib.title"
msgstr "Z-Library 스크레이프"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library는 <a %(a_href)s>Library Genesis</a> 커뮤니티에서 시작되었으며, 처음에는 그들의 데이터를 기반으로 시작되었습니다. 그 이후로 상당히 전문화되었고, 훨씬 더 현대적인 인터페이스를 갖추게 되었습니다. 따라서 웹사이트를 계속 개선하기 위한 금전적 기부와 새로운 책 기부를 많이 받을 수 있게 되었습니다. Library Genesis 컬렉션 외에도 방대한 컬렉션을 축적했습니다."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "2023년 2월 업데이트."

msgid "page.datasets.zlib.description.allegations"
msgstr "2022년 말, Z-Library의 창립자로 추정되는 사람들이 체포되고 도메인이 미국 당국에 의해 압수되었습니다. 그 이후로 웹사이트는 천천히 다시 온라인으로 돌아오고 있지만, 현재 누가 운영하고 있는지는 알려지지 않았습니다."

msgid "page.datasets.zlib.description.three_parts"
msgstr "컬렉션은 세 부분으로 구성되어 있습니다. 첫 두 부분의 원래 설명 페이지는 아래에 보존되어 있습니다. 모든 데이터를 얻으려면 세 부분이 모두 필요합니다 (토렌트 페이지에서 교체된 토렌트는 제외)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: 첫 번째 릴리스. 당시 “Pirate Library Mirror” (“pilimi”)라고 불리던 첫 번째 릴리스였습니다."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: 두 번째 릴리스, 이번에는 모든 파일이 .tar 파일로 포장되었습니다."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: 점진적인 새로운 릴리스, 이제 <a %(a_href)s>안나의 아카이브 컨테이너 (AAC) 형식</a>을 사용해, Z-Library 팀과 협력하여 릴리스되었습니다."

msgid "page.datasets.zlib.aa_torrents"
msgstr "안나의 아카이브 토렌트 (메타데이터 + 콘텐츠)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "안나의 아카이브 예시 기록 (원래 컬렉션)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "안나의 아카이브 예시 기록 (“zlib3” 컬렉션)"

msgid "page.datasets.zlib.link.zlib"
msgstr "메인 웹사이트"

msgid "page.datasets.zlib.link.onion"
msgstr "Tor 도메인"

msgid "page.datasets.zlib.blog.release1"
msgstr "릴리스 1에 대한 블로그 게시물"

msgid "page.datasets.zlib.blog.release2"
msgstr "릴리스 2에 대한 블로그 게시물"

msgid "page.datasets.zlib.historical.title"
msgstr "Zlib 릴리스 (기존 설명 페이지)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "릴리스 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "초기 미러는 2021년과 2022년에 걸쳐 아주 힘들게 얻어졌습니다. 현재는 약간 구식입니다: 2021년 6월의 컬렉션 상태를 반영합니다. 우리는 미래에 이를 업데이트할 것입니다. 지금은 이 첫 번째 릴리스를 내보내는 데 집중하고 있습니다."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Library Genesis는 이미 공개 토렌트로 보존되어 있으며, Z-Library에 포함되어 있기 때문에, 2022년 6월에 Library Genesis에 대해 기본적인 중복 제거를 수행했습니다. 이를 위해 MD5 해시를 사용했습니다. 라이브러리에는 동일한 책의 여러 파일 형식과 같은 더 많은 중복 콘텐츠가 있을 가능성이 큽니다. 이를 정확하게 감지하기는 어렵기 때문에 하지 않습니다. 중복 제거 후에는 2백만 개 이상의 파일이 남아 있으며, 총 용량은 약 7TB입니다."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "컬렉션은 두 부분으로 구성됩니다: 메타데이터의 MySQL “.sql.gz” 덤프와 약 50-100GB의 72개의 토렌트 파일입니다. 메타데이터에는 Z-Library 웹사이트에서 보고된 데이터(제목, 저자, 설명, 파일 형식)뿐만 아니라, 우리가 관찰한 실제 파일 크기와 md5sum이 포함되어 있습니다. 이는 때때로 일치하지 않기 때문입니다. Z-Library 자체에 잘못된 메타데이터가 있는 파일 범위가 있는 것 같습니다. 또한, 일부 고립된 경우에 잘못 다운로드된 파일이 있을 수 있으며, 이는 향후 감지하고 수정하려고 합니다."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "대형 토렌트 파일에는 실제 책 데이터가 포함되어 있으며, 파일 이름으로 Z-Library ID가 사용됩니다. 파일 확장자는 메타데이터 덤프를 사용하여 재구성할 수 있습니다."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "컬렉션은 비소설과 소설 콘텐츠가 혼합되어 있으며(Library Genesis와 같이 분리되지 않음), 품질도 다양합니다."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "이 첫 번째 릴리스는 이제 완전히 이용 가능합니다. 토렌트 파일은 우리 Tor 미러를 통해서만 이용할 수 있습니다."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "릴리스 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "우리는 마지막 미러와 2022년 8월 사이에 Z-Library에 추가된 모든 책을 얻었습니다. 또한 처음에 놓친 일부 책도 다시 긁어모았습니다. 전체적으로 이 새로운 컬렉션은 약 24TB입니다. 이 컬렉션도 Library Genesis와 중복 제거되었습니다. 해당 컬렉션에 대한 토렌트가 이미 제공되고 있기 때문입니다."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "데이터는 첫 번째 릴리스와 유사하게 구성되어 있습니다. 메타데이터의 MySQL “.sql.gz” 덤프가 있으며, 첫 번째 릴리스의 모든 메타데이터도 포함되어 있어 이를 대체합니다. 또한 몇 가지 새로운 열을 추가했습니다:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: 이 파일이 Library Genesis의 비소설 또는 소설 컬렉션에 이미 있는지 여부(md5로 확인)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: 이 파일이 포함된 토렌트."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: 책을 다운로드할 수 없을 때 설정됨."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "지난번에 언급했지만, 다시 한 번 명확히 하자면: “filename”과 “md5”는 파일의 실제 속성이고, “filename_reported”와 “md5_reported”는 Z-Library에서 스크레이핑 한 것입니다. 때때로 이 두 가지가 일치하지 않기 때문에 둘 다 포함했습니다."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "이번 릴리스에서는 collation을 “utf8mb4_unicode_ci”로 변경했으며, 이는 MySQL의 이전 버전들과 호환이 될겁니다."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "데이터 파일은 지난번과 유사하지만 훨씬 큽니다. 다름이 아니라 그냥 단순하게 작은 토렌트 파일들을 만드는게 우리에게는 매우 번거롭습니다. “pilimi-zlib2-0-14679999-extra.torrent”에는 지난 릴리스에서 놓친 모든 파일이 포함되어 있으며, 다른 토렌트는 모두 새로운 ID 범위입니다. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>업데이트 %(date)s:</strong> 우리는 대부분의 토렌트를 너무 크게 만들어 토렌트 클라이언트가 어려움을 겪었습니다. 이를 제거하고 새로운 토렌트를 릴리스했습니다."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>업데이트 %(date)s:</strong> 여전히 파일이 너무 많아서 tar 파일로 묶어 다시 새로운 토렌트를 릴리스했습니다."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "릴리스 2 부록 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "이것은 단일 추가 토렌트 파일입니다. 새로운 정보는 포함되어 있지 않지만, 계산하는 데 시간이 걸릴 수 있는 일부 데이터가 포함되어 있습니다. 따라서 이 토렌트를 다운로드하는 것이 처음부터 계산하는 것보다 빠른 경우가 많아 편리합니다. 특히, tar 파일용 SQLite 인덱스가 포함되어 있으며, <a %(a_href)s>ratarmount</a>와 함께 사용할 수 있습니다."

msgid "page.faq.title"
msgstr "자주 묻는 질문 (FAQ)"

msgid "page.faq.what_is.title"
msgstr "안나의 아카이브란 무엇인가요?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>안나의 아카이브</span>는 두 가지 목표를 가진 비영리 프로젝트입니다:"

msgid "page.home.intro.text2"
msgstr "<li><strong>보존:</strong> 인류의 모든 지식과 문화를 보존합니다.</li><li><strong>접근:</strong> 이 지식과 문화를 전 세계 누구나 이용할 수 있도록 합니다.</li>"

msgid "page.home.intro.open_source"
msgstr "저희의 모든 <a %(a_code)s>코드</a>와 <a %(a_datasets)s>데이터</a>는 완전히 오픈 소스입니다."

msgid "page.home.preservation.header"
msgstr "보존"

msgid "page.home.preservation.text1"
msgstr "우리는 다양한 <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">섀도우 라이브러리</a>, 공식 도서관 및 기타 컬렉션에서 이 자료들을 한 곳에 모아 책, 논문, 만화, 잡지 등을 보존합니다. 이 모든 데이터는 토렌트를 사용하여 대량으로 복제하기 쉽게 만들어 전 세계에 많은 복사본을 보유함으로써 영구적으로 보존됩니다. 일부 섀도우 라이브러리는 이미 자체적으로 이를 수행하고 있습니다 (예: Sci-Hub, Library Genesis), 반면 안나의 아카이브는 대량 배포를 제공하지 않는 다른 도서관 (예: Z-Library) 또는 전혀 섀도우 라이브러리가 아닌 도서관 (예: Internet Archive, DuXiu)을 “해방”시킵니다."

msgid "page.home.preservation.text2"
msgstr "이 광범위한 배포와 오픈 소스 코드는 우리 웹사이트를 차단이나 폐쇄로부터 내성을 가지도록 하고, 인류의 지식과 문화를 장기적으로 보존할 수 있게 합니다. <a href=\"/datasets\">우리의 데이터셋</a>에 대해 더 알아보세요."

msgid "page.home.preservation.label"
msgstr "우리는 전 세계의 책 중 약 <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%%를 보존했다고 추정합니다</a>."

msgid "page.home.access.header"
msgstr "접근"

msgid "page.home.access.text"
msgstr "저희는 파트너와 협력하여 누구나 쉽게 무료로 컬렉션에 접근할 수 있도록 합니다. 우리는 모든 사람이 인류의 집단 지혜에 접근할 권리가 있다고 믿습니다. 그리고 <a %(a_search)s>저자에게 피해를 주지 않고</a> 말입니다."

msgid "page.home.access.label"
msgstr "지난 30일간 시간별 다운로드. 시간별 평균: %(hourly)s. 일일 평균: %(daily)s."

msgid "page.about.text2"
msgstr "우리는 정보의 자유로운 흐름과 지식 및 문화의 보존을 강력히 믿습니다. 이 검색엔진은 많은 거인들의 어깨 위에 쌓아졌습니다. 다양한 섀도우 라이브러리를 만든 사람들의 노고를 깊이 존경하며, 이 검색 엔진이 그들의 범위를 넓히기를 바랍니다."

msgid "page.about.text3"
msgstr "진행 상황에 대한 업데이트를 받으려면 <a href=\"https://www.reddit.com/r/Annas_Archive/\">레딧</a> 또는 <a href=\"https://t.me/annasarchiveorg\">텔레그램</a>에서 안나를 팔로우하세요. 질문이나 피드백이 있으시면 %(email)s에서 안나에게 연락해 주세요."

msgid "page.faq.help.title"
msgstr "어떻게 도울 수 있나요?"

msgid "page.about.help.text"
msgstr "<li>1. <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> 또는 <a href=\"https://t.me/annasarchiveorg\">텔레그램</a>에서 저희를 팔로우하세요.</li><li>2. 트위터, 레딧, 틱톡, 인스타그램, 지역 카페나 도서관 등 어디서든 안나의 아카이브에 대해 알려주세요! 저희는 정보 독점을 믿지 않습니다 — 만약 저희가 차단되더라도 모든 코드와 데이터가 완전히 오픈 소스이기 때문에 다른 곳에서 다시 나타날 것입니다.</li><li>3. 가능하다면 <a href=\"/donate\">기부</a>를 고려해 주세요.</li><li>4. 저희 웹사이트를 다른 언어로 <a href=\"https://translate.annas-software.org/\">번역</a>하는 데 도움을 주세요.</li><li>5. 소프트웨어 엔지니어라면 저희 <a href=\"https://annas-software.org/\">오픈 소스</a>에 기여하거나 저희 <a href=\"/datasets\">토렌트</a>를 시딩하는 것을 고려해 주세요.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "이제 %(matrix)s에서 동기화되는 Matrix 채널도 운영하고 있습니다."

msgid "page.about.help.text6"
msgstr "6. 보안 연구자라면, 공격과 방어 모두에서 귀하의 기술을 사용할 수 있습니다. 저희 <a %(a_security)s>보안</a> 페이지를 확인하세요."

msgid "page.about.help.text7"
msgstr "7. 익명 상인을 위한 결제 전문가를 찾고 있습니다. 더 편리한 기부 방법을 추가하는 데 도움을 주실 수 있나요? PayPal(페이팔), WeChat, 기프트 카드 등. 아는 사람이 있다면 저희에게 연락해 주세요."

msgid "page.about.help.text8"
msgstr "8. 저희는 항상 더 많은 서버 수용능력을 구하고 있습니다."

msgid "page.about.help.text9"
msgstr "9. 파일 문제를 보고하고, 댓글을 남기고, 이 웹사이트에서 목록을 작성하여 도울 수 있습니다. 또한 <a %(a_upload)s>더 많은 책을 업로드</a>하거나 기존 책의 파일 문제나 형식을 수정하여 도울 수 있습니다."

msgid "page.about.help.text10"
msgstr "10. 귀하의 언어로 안나의 아카이브의 위키백과 페이지를 만들거나 유지 관리하는 데 도움을 주세요."

msgid "page.about.help.text11"
msgstr "11. 우리는 작지만 세련된 광고를 게재하려고 합니다. 안나의 아카이브에 광고를 게재하고 싶다면 알려주세요."

msgid "page.faq.help.mirrors"
msgstr "우리는 사람들이<a %(a_mirrors)s>미러</a>를 만들길 희망하고, 우리는 이를 재정적으로 지원할 것입니다."

msgid "page.about.help.volunteer"
msgstr "자원봉사에 대한 더 자세한 정보는 <a %(a_volunteering)s>자원봉사 및 현상금</a> 페이지를 참조하십시오."

msgid "page.faq.slow.title"
msgstr "저속 다운로드는 왜이렇게 느린가요?"

msgid "page.faq.slow.text1"
msgstr "우리는 전 세계 모든 사람에게 고속 다운로드를 제공할 충분한 자원이 없습니다. 부유한 후원자가 나서서 이를 제공해 준다면 정말 놀라운 일이겠지만, 그때까지는 최선을 다하고 있습니다. 우리는 기부를 통해 간신히 유지되는 비영리 프로젝트입니다."

msgid "page.faq.slow.text2"
msgstr "이것이 우리가 파트너와 함께 무료 다운로드를 위해 두 가지 시스템을 구현한 이유입니다: 저속 다운로드가 가능한 공유 서버와 대기열이 있는 약간 더 빠른 서버(동시에 다운로드하는 사람 수를 줄이기 위해)."

msgid "page.faq.slow.text3"
msgstr "우리는 저속 다운로드를 위해 <a %(a_verification)s>브라우저 인증</a>도 제공하고 있습니다. 그렇지 않으면 봇과 크롤러가 이를 악용하여 일반 사용자의 속도를 더욱 느리게 만듭니다."

msgid "page.faq.slow.text4"
msgstr "Tor 브라우저를 사용할 때 보안 설정을 조정해야 할 수도 있습니다. \"표준\"이라고 불리는 가장 낮은 옵션에서는 Cloudflare Turnstile 챌린지가 성공합니다. \"더 안전함\" 및 \"가장 안전함\"이라고 불리는 더 높은 옵션에서는 챌린지가 실패합니다."

msgid "page.faq.slow.text5"
msgstr "큰 파일의 경우 다운로드가 중간에 끊길 수 있습니다. 대용량 다운로드를 자동으로 재개할 수 있는 다운로드 매니저(JDownloader 등)를 사용하는 것을 권장합니다."

msgid "page.donate.faq.title"
msgstr "기부 FAQ"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>멤버십이 자동으로 갱신되나요?</div> 멤버십은 <strong>자동으로 갱신되지 않습니다</strong>. 원하는 기간 동안 가입할 수 있습니다."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>멤버십을 업그레이드하거나 여러 멤버십을 가질 수 있나요?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>다른 결제 방법이 있나요?</div> 현재는 없습니다. 많은 사람들이 이런 아카이브의 존재를 원하지 않기 때문에 조심해야 합니다. 더 안전하게 다른 (더 편리한) 결제 방법을 설정하는 데 도움을 주실 수 있다면 %(email)s로 연락해 주세요."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>월별 범위는 무엇을 의미하나요?</div> 한 달보다 긴 기간을 선택하는 등 모든 할인들을 적용하여 멤버십에 필요한 기부 금액을 낮출 수 있습니다."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>기부금은 어디에 사용되나요?</div> 기부금의 100%%가 세계의 지식과 문화를 보존하고 접근 가능하게 하는 데 사용됩니다. 현재 주로 서버, 저장소 및 대역폭에 사용되고 있습니다. 팀 구성원 개인에게는 돈이 지급되지 않습니다."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>큰 금액을 기부할 수 있나요?</div> 그건 정말 엄청날겁니다! 수 천 달러 이상의 기부를 원하시면, %(email)s로 직접 연락해 주세요."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>회원이 되지 않고 기부할 수 있나요?</div> 물론입니다. 이 Monero (XMR) 주소로 얼마든지 기부를 받습니다: %(address)s."

msgid "page.faq.upload.title"
msgstr "새 책을 어떻게 업로드하나요?"

msgid "page.upload.zlib.text1"
msgstr "또는 Z-Library에 <a %(a_upload)s>여기</a>에 업로드할 수 있습니다."

msgid "page.upload.upload_to_both"
msgstr "작은 업로드(최대 10,000개 파일)는 %(first)s와 %(second)s 에 업로드해 주세요."

msgid "page.upload.text1"
msgstr "지금은 Library Genesis 포크에 새 책을 업로드하는 것을 권장합니다. <a %(a_guide)s>유용한 가이드</a>가 있습니다. 이 웹사이트에서 인덱싱하는 두 포크 모두 동일한 업로드 시스템을 사용합니다."

msgid "page.upload.libgenli_login_instructions"
msgstr "Libgen.li의 경우, 우선 <a %(a_forum)s>Libgen.li의 포럼</a>에 사용자 이름 %(username)s과 비밀번호 %(password)s로 로그인한 후, Libgen.li의 <a %(a_upload_page)s>업로드 페이지</a>로 돌아가세요."

msgid "common.libgen.email"
msgstr "Libgen 포럼에서 이메일 주소가 작동하지 않으면, <a %(a_mail)s>Proton Mail</a>(무료)을 사용하는 것을 권장합니다. 또한 <a %(a_manual)s>수동으로 요청</a>하여 계정을 활성화할 수 있습니다."

msgid "page.faq.mhut_upload"
msgstr "mhut.org는 특정 IP 범위를 차단하므로, VPN이 필요할 수 있습니다."

msgid "page.upload.large.text"
msgstr "Libgen 또는 Z-Library에서 수락되지 않는 대용량 업로드(10,000개 이상의 파일)에 대해서는 %(a_email)s로 문의하십시오."

msgid "page.upload.zlib.text2"
msgstr "학술 논문을 업로드하려면 Library Genesis 외에도 <a %(a_stc_nexus)s>STC Nexus</a>에 업로드해 주세요. 이곳은 새로운 논문을 위한 최고의 쉐도우 라이브러리입니다. 아직 통합되지 않았지만, 언젠가는 통합할 예정입니다. <a %(a_telegram)s>텔레그램의 업로드 봇</a>을 사용하거나, 너무 많은 파일을 업로드해야 하는 경우 고정된 메시지에 나와 있는 주소로 연락할 수 있습니다."

msgid "page.faq.request.title"
msgstr "책을 어떻게 요청하나요?"

msgid "page.request.cannot_accomodate"
msgstr "현재는 도서 요청을 받을 수 없습니다."

msgid "page.request.forums"
msgstr "Z-Library 또는 Libgen 포럼에 요청을 해주세요."

msgid "page.request.dont_email"
msgstr "책 요청을 이메일로 보내지 마세요."

msgid "page.faq.metadata.title"
msgstr "메타데이터를 수집하시나요?"

msgid "page.faq.metadata.indeed"
msgstr "물론 그렇습니다."

msgid "page.faq.1984.title"
msgstr "조지 오웰의 1984를 다운로드했는데, 경찰이 집에 찾아 올까요?"

msgid "page.faq.1984.text"
msgstr "너무 걱정하지 마세요, 우리와 연결된 웹사이트에서 다운로드하는 아주 많은 사람들이 있으며, 문제가 발생하는 경우는 매우 드뭅니다. 그러나 안전을 위해 VPN(유료) 또는 <a %(a_tor)s>Tor</a>(무료)를 사용하는 것을 권장합니다."

msgid "page.faq.save_search.title"
msgstr "검색 설정을 어떻게 저장하나요?"

msgid "page.faq.save_search.text1"
msgstr "원하는 설정을 선택하고, 검색 상자를 비워둔 채로 “검색”을 클릭한 다음, 브라우저의 북마크 기능을 사용하여 페이지를 북마크하세요."

msgid "page.faq.mobile.title"
msgstr "모바일 앱이 있나요?"

msgid "page.faq.mobile.text1"
msgstr "공식 모바일 앱은 없지만, 이 웹사이트를 앱으로 설치할 수 있습니다."

msgid "page.faq.mobile.android"
msgstr "<strong>안드로이드:</strong> 오른쪽 상단의 세 점 메뉴를 클릭하고, “홈 화면에 추가”를 선택하세요."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> 하단의 “공유” 버튼을 클릭하고 “홈 화면에 추가”를 선택하십시오."

msgid "page.faq.api.title"
msgstr "API가 있나요?"

msgid "page.faq.api.text1"
msgstr "회원 전용으로 빠른 다운로드 URL을 제공하는 안정적인 JSON API가 있습니다: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (JSON 내 문서 포함)."

msgid "page.faq.api.text2"
msgstr "다른 사용 사례, 예를 들어 모든 파일을 반복하거나 맞춤 검색을 구축하는 등의 경우에는 <a %(a_generate)s>생성</a> 또는 <a %(a_download)s>다운로드</a>하여 ElasticSearch 및 MariaDB 데이터베이스를 사용하는 것을 권장합니다. 원시 데이터는 <a %(a_explore)s>JSON 파일을 통해</a> 수동으로 탐색할 수 있습니다."

msgid "page.faq.api.text3"
msgstr "우리의 미가공 토렌트 목록은 <a %(a_torrents)s>JSON</a> 형식으로도 다운로드할 수 있습니다."

msgid "page.faq.torrents.title"
msgstr "토렌트 FAQ"

msgid "page.faq.torrents.q1"
msgstr "시딩을 돕고 싶지만 디스크 공간이 많지 않습니다."

msgid "page.faq.torrents.a1"
msgstr "<a %(a_list)s>토렌트 목록 생성기</a>를 사용하여 저장 공간 한도 내에서 토렌트가 가장 필요한 목록을 생성하세요."

msgid "page.faq.torrents.q2"
msgstr "토렌트가 너무 느립니다. 데이터를 직접 다운로드할 수 있나요?"

msgid "page.faq.torrents.a2"
msgstr "네, <a %(a_llm)s>LLM 데이터</a> 페이지를 참조하세요."

msgid "page.faq.torrents.q3"
msgstr "특정 언어나 주제와 같은 파일의 하위 집합만 골라서 다운로드할 수 있나요?"

msgid "page.faq.torrents.a3_short_answer"
msgstr "짧은 답변: 쉽지 않습니다."

msgid "page.faq.torrents.a3_long_answer_start"
msgstr "긴 답변:"

msgid "page.faq.torrents.a3"
msgstr "대부분의 토렌트는 파일을 직접 포함하고 있어, 토렌트 클라이언트에게 필요한 파일만 다운로드하도록 지시할 수 있습니다. 어떤 파일을 다운로드할지 결정하기 위해 <a %(a_generate)s>메타데이터를 생성</a>하거나 <a %(a_download)s>ElasticSearch 및 MariaDB 데이터베이스를 다운로드</a>할 수 있습니다. 아쉽게도, 일부 토렌트 컬렉션은 루트에 .zip 또는 .tar 파일을 포함하고 있어 개별 파일을 선택하기 전에 전체 토렌트를 다운로드해야 합니다."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(하지만 후자의 경우 <a %(a_ideas)s>몇 가지 아이디어</a>는 있습니다.)"

msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "토렌트를 필터링할 수 있는 쉬운 도구는 아직 없지만, 기여를 환영합니다."

msgid "page.faq.torrents.q4"
msgstr "토렌트의 중복 파일은 어떻게 처리하나요?"

msgid "page.faq.torrents.a4"
msgstr "이 목록의 토렌트 간 중복이나 겹침을 최소화하려고 노력하지만, 항상 달성할 수는 없으며, 소스 라이브러리의 정책에 크게 좌우됩니다. 자체 토렌트를 배포하는 라이브러리에 대해서는 저희가 관여할 수 없습니다. 안나의 아카이브에서 배포하는 토렌트의 경우, MD5 해시를 기준으로 중복 제거를 수행하므로 동일한 책의 다른 버전은 중복 제거되지 않습니다."

msgid "page.faq.torrents.q5"
msgstr "토렌트 목록을 JSON 형식으로 받을 수 있나요?"

msgid "page.faq.torrents.a5"
msgstr "네."

msgid "page.faq.torrents.q6"
msgstr "토렌트에서 PDF나 EPUB이 보이지 않고 바이너리 파일만 보입니다. 어떻게 해야 하나요?"

msgid "page.faq.torrents.a6"
msgstr "이것들은 실제로 PDF와 EPUB 파일이며, 많은 토렌트에서 확장자가 없을 뿐입니다. 파일 유형/확장자를 포함하여 토렌트 파일의 메타데이터를 찾을 수 있는 두 곳이 있습니다:"

msgid "page.faq.torrents.a6.li1"
msgstr "각 컬렉션이나 릴리스는 고유한 메타데이터를 가지고 있습니다. 예를 들어, <a %(a_libgen_nonfic)s>Libgen.rs 토렌트</a>는 Libgen.rs 웹사이트에 호스팅된 해당 메타데이터 데이터베이스를 가지고 있습니다. 우리는 일반적으로 각 컬렉션의 <a %(a_datasets)s>데이터셋 페이지</a>에서 관련 메타데이터 리소스로 링크를 제공합니다."

msgid "page.faq.torrents.a6.li2"
msgstr "2. 저희 ElasticSearch 및 MariaDB 데이터베이스를 <a %(a_generate)s>생성</a>하거나 <a %(a_download)s>다운로드</a>하는 것을 권장합니다. 이 데이터베이스는 안나의 아카이브의 각 기록을 해당 토렌트 파일(가능한 경우)과 매핑하는 정보를 포함하고 있으며, ElasticSearch JSON의 “torrent_paths”에 있습니다."

msgid "page.faq.torrents.q7"
msgstr "왜 내 토렌트 클라이언트가 일부 토렌트 파일/마그넷 링크를 열 수 없나요?"

msgid "page.faq.torrents.a7"
msgstr "일부 토렌트 클라이언트는 큰 조각 크기를 지원하지 않으며, 우리 토렌트에 이런 경우가 많습니다 (새로운 파일들은 더 이상 이렇게 하지 않습니다 — 기존의 방법도 토렌트 기준상 문제는 없지만요!). 이 문제를 겪는다면 다른 클라이언트를 시도하거나, 당신의 토렌트 클라이언트 제작자에게 불만을 제기하세요."

msgid "page.faq.security.title"
msgstr "보안 취약점 책임 공개 프로그램이 있나요?"

msgid "page.faq.security.text1"
msgstr "우리는 보안 연구자들이 우리 시스템의 취약점을 찾는 것을 환영합니다. 우리는 책임 있는 취약점 공개를 강력히 지지합니다. <a %(a_contact)s>여기</a>로 연락하세요."

msgid "page.faq.security.text2"
msgstr "현재 익명성을 위협할 수 있는 <a %(a_link)s >취약점을 제외하고는 버그 바운티를 제공할 수 없습니다.</a> 이러한 취약점에 대해서는 $10,000에서 $50,000 범위의 보상을 제공합니다. 앞으로 더 넓은 범위의 버그 바운티를 제공하고자 합니다! 사회 공학적 공격은 범위에서 제외된다는 점을 유의해 주세요."

msgid "page.faq.security.text3"
msgstr "공격적 보안에 관심이 있고 세계의 지식과 문화를 아카이브하는 데 도움을 주고 싶다면, 꼭 저희에게 연락하세요. 도울 수 있는 많은 방법이 있습니다."

msgid "page.faq.resources.title"
msgstr "Anna’s Archive에 대한 더 많은 자료가 있나요?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>안나의 블로그</a>, <a %(a_reddit_u)s>레딧</a>, <a %(a_reddit_r)s>서브레딧</a> — 정기적 업데이트들"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>안나의 소프트웨어</a> — 우리의 오픈 소스 코드"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>안나의 소프트웨어에서 번역하기</a> — 우리의 번역 시스템"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>데이터셋</a> — 데이터에 대해"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — 대체 도메인"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>위키백과</a> — 우리에 대해 더 알아보기 (이 페이지를 업데이트하거나 자신의 언어로 새로 만들어 주세요!)"

msgid "page.faq.copyright.title"
msgstr "저작권 침해를 어떻게 신고하나요?"

msgid "page.faq.copyright.text1"
msgstr "저희는 여기에서 저작권이 있는 자료를 호스팅하지 않습니다. 저희는 검색 엔진으로서 이미 공개된 메타데이터만 색인화합니다. 이러한 외부 소스에서 다운로드할 때는 귀하의 관할 구역에서 허용되는지 법률을 확인하는 것이 좋습니다. 저희는 다른 사람이 호스팅하는 콘텐츠에 대해 책임지지 않습니다."

msgid "page.faq.copyright.text2"
msgstr "여기에서 본 내용에 대해 불만이 있다면, 원본 웹사이트에 연락하는 것이 가장 좋습니다. 우리는 정기적으로 그들의 변경 사항을 데이터베이스에 반영합니다. 정말로 유효한 DMCA 컴플레인이 있다고 생각하신다면, <a %(a_copyright)s>DMCA / 저작권 클레임 양식</a>을 작성해 주세요. 우리는 귀하의 컴플레인을 진지하게 생각하며, 가능한 한 빨리 답변드리겠습니다."

msgid "page.faq.hate.title"
msgstr "이 프로젝트를 운영하는 방식이 맘에 안 들어요!"

msgid "page.faq.hate.text1"
msgstr "또한, 저희의 모든 코드와 데이터는 완전히 오픈 소스임을 상기시켜 드리고 싶습니다. 이는 저희와 같은 프로젝트에서는 유일무이한 일입니다 — 우리가 아는 한 이렇게 방대한 카탈로그를 제공하면서 완전히 오픈소스로 운영하는건 저희가 유일합니다. 저희 프로젝트가 제대로 운영되지 않는다고 생각하는 분들은 누구든지 저희의 코드와 데이터를 가져가서 자신만의 섀도우 라이브러리를 운영하는 것을 환영합니다! 이건 비꼬려는게 아니라, 정말로 모두에게 이 사명의 품질 기준을 높이고 인류의 유산을 더 잘 보존할 수 있을 것이라고 생각하기 때문입니다."

msgid "page.faq.uptime.title"
msgstr "업타임 모니터가 있습니까?"

msgid "page.faq.uptime.text1"
msgstr "<a %(a_href)s>이 훌륭한 프로젝트</a>를 참조하세요."

msgid "page.faq.physical.title"
msgstr "책이나 다른 물리적 자료를 기부하려면 어떻게 해야 하나요?"

msgid "page.faq.physical.text1"
msgstr "<a %(a_archive)s>Internet Archive</a>로 보내주세요. 그들이 적절히 보존할 것입니다."

msgid "page.faq.anna.title"
msgstr "안나는 누구인가요?"

msgid "page.faq.anna.text1"
msgstr "당신이 안나입니다!"

msgid "page.faq.favorite.title"
msgstr "가장 좋아하는 책은 무엇인가요?"

msgid "page.faq.favorite.text1"
msgstr "섀도우 라이브러리와 디지털 보존의 세계에 특별한 의미를 지닌 책들이 몇가지 있습니다:"

msgid "page.fast_downloads.no_more_new"
msgstr "오늘 고속 다운로드 한도를 초과했습니다."

msgid "page.fast_downloads.no_member"
msgstr "고속 다운로드를 사용하려면 회원이 되세요."

msgid "page.fast_downloads.no_member_2"
msgstr "우리는 이제 아마존 기프트 카드, 신용카드 및 직불카드, 암호화폐, 알리페이, 그리고 위챗을 지원합니다."

msgid "page.home.full_database.header"
msgstr "전체 데이터베이스"

msgid "page.home.full_database.subtitle"
msgstr "책, 논문, 잡지, 만화, 도서관 기록, 메타데이터, …"

msgid "page.home.full_database.search"
msgstr "검색"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "베타"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub가 새로운 논문의 업로드를 <a %(a_paused)s>일시 중단</a>했습니다."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB는 Sci-Hub의 후신입니다."

msgid "page.home.scidb.subtitle"
msgstr "%(count)s개의 학술 논문에 직접 접근"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "열기"

msgid "page.home.scidb.browser_verification"
msgstr "<a %(a_member)s>회원</a>이라면 브라우저 인증이 필요하지 않습니다."

msgid "page.home.archive.header"
msgstr "장기 아카이브"

msgid "page.home.archive.body"
msgstr "안나의 아카이브에서 사용된 데이터셋은 완전히 공개되어 있으며, 토렌트를 통해 대량으로 미러링할 수 있습니다. <a %(a_datasets)s>자세히 알아보기…</a>"

msgid "page.home.torrents.body"
msgstr "토렌트를 시딩하여 아주 많은 도움을 줄 수 있습니다. <a %(a_torrents)s>자세히 알아보기…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s 시더"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s 시더"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s 시더"

msgid "page.home.llm.header"
msgstr "LLM 트레이닝 데이터"

msgid "page.home.llm.body"
msgstr "우리는 세계에서 가장 큰 고품질 텍스트 데이터 컬렉션을 보유하고 있습니다. <a %(a_llm)s>더 알아보기…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 미러: 자원봉사자 모집"

msgid "page.home.volunteering.header"
msgstr "🤝 자원봉사자 모집 중"

msgid "page.home.volunteering.help_out"
msgstr "비영리 오픈 소스 프로젝트로서, 우리는 항상 도움을 줄 사람들을 찾고 있습니다."

msgid "page.home.payment_processor.body"
msgstr "고위험 익명 결제 프로세서를 운영중인 경우, 저희에게 연락해 주세요. 또한, 세련된 소형 광고를 게재할 사람을 찾고 있습니다. 모든 수익은 우리의 보존 노력에 사용됩니다."

msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s 블로그 ↗"

msgid "page.ipfs_downloads.title"
msgstr "IPFS 다운로드"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>이 파일의 모든 다운로드 링크</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS 게이트웨이 #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(IPFS에서 여러 번 시도해야 할 수도 있습니다)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 더 빠른 다운로드와 브라우저 검사를 건너뛰려면, <a %(a_membership)s>회원이 되세요</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 우리의 컬렉션을 대량으로 미러링하려면 <a %(a_datasets)s>Datasets</a> 및 <a %(a_torrents)s>Torrents</a> 페이지를 확인하세요."

msgid "page.llm.title"
msgstr "LLM 데이터"

msgid "page.llm.intro"
msgstr "LLM이 고품질 데이터에서 번성한다는 것은 잘 알려져 있습니다. 우리는 세계에서 가장 큰 책, 논문, 잡지 등의 컬렉션을 보유하고 있으며, 이는 최고 품질의 텍스트 소스 중 일부입니다."

msgid "page.llm.unique_scale"
msgstr "유일무이한 규모와 범위"

msgid "page.llm.unique_scale.text1"
msgstr "우리의 컬렉션은 학술 저널, 교과서, 잡지를 포함하여 1억 개 이상의 파일을 포함하고 있습니다. 우리는 기존의 대형 저장소를 결합하여 이 규모를 달성합니다."

msgid "page.llm.unique_scale.text2"
msgstr "우리의 일부 소스 컬렉션은 이미 대량으로 제공되고 있습니다(Sci-Hub 및 Libgen의 일부). 다른 소스는 우리가 직접 공개했습니다. <a %(a_datasets)s>데이터셋</a>에서 전체 개요를 확인할 수 있습니다."

msgid "page.llm.unique_scale.text3"
msgstr "우리의 컬렉션에는 전자책 시대 이전의 수백만 권의 책, 논문, 잡지가 포함되어 있습니다. 이 컬렉션의 많은 부분은 이미 OCR 처리가 되었으며, 내부 중복이 거의 없습니다."

msgid "page.llm.how_we_can_help"
msgstr "우리가 도울 수 있는 방법"

msgid "page.llm.how_we_can_help.text1"
msgstr "우리는 전체 컬렉션뿐만 아니라 공개되지 않은 컬렉션에 대한 고속 액세스를 제공할 수 있습니다."

msgid "page.llm.how_we_can_help.text2"
msgstr "이는 수만 달러 범위의 기부로 제공할 수 있는 기업 수준의 액세스입니다. 우리는 또한 우리가 아직 보유하지 않은 고품질 컬렉션과 교환할 의향이 있습니다."

msgid "page.llm.how_we_can_help.text3"
msgstr "데이터 풍부화에 도움을 주실 수 있다면 환불해 드릴 수 있습니다. 예를 들어:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "중복 제거"

msgid "page.llm.how_we_can_help.extraction"
msgstr "텍스트 및 메타데이터 추출"

msgid "page.llm.how_we_can_help.text4"
msgstr "인류 지식의 장기 보존을 지원하면서 모델에 더 나은 데이터를 제공하세요!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>문의하기</a>를 통해 협력 방법을 논의합시다."

msgid "page.login.continue"
msgstr "계속하기"

msgid "page.login.please"
msgstr "이 페이지를 보려면 <a %(a_account)s>로그인</a>해 주세요.</a>"

msgid "page.maintenance.header"
msgstr "안나의 아카이브는 현재 유지 보수를 위해 일시적으로 중단되었습니다. 한 시간 후에 다시 방문해 주세요."

msgid "page.metadata.header"
msgstr "메타데이터 개선"

msgid "page.metadata.body1"
msgstr "메타데이터를 개선하여 도서 보존에 기여할 수 있습니다! 먼저 안나의 아카이브에서 메타데이터에 대한 배경을 읽고, Open Library와의 연계를 통해 메타데이터를 개선하는 방법을 배우고, 안나의 아카이브에서 무료 멤버십을 얻으세요."

msgid "page.metadata.background.title"
msgstr "배경"

msgid "page.metadata.background.body1"
msgstr "안나의 아카이브에 있는 책들을 보면, 제목, 저자, 출판사, 판, 연도, 설명, 파일명 등 다양한 필드를 볼 수 있습니다. 이 모든 정보 조각들을 <em>메타데이터</em>라고 합니다."

msgid "page.metadata.background.body2"
msgstr "우리는 다양한 <em>소스 라이브러리</em>에서 책을 결합하기 때문에 해당 소스 라이브러리에서 제공하는 메타데이터를 표시합니다. 예를 들어, Library Genesis에서 가져온 책의 경우, Library Genesis 데이터베이스의 제목을 표시합니다."

msgid "page.metadata.background.body3"
msgstr "때때로 책이 <em>여러</em> 소스 라이브러리에 존재할 수 있으며, 이 경우 메타데이터 필드가 다를 수 있습니다. 이 경우, 우리는 각 필드의 가장 긴 버전을 표시합니다. 왜냐하면 그 버전이 가장 유용한 정보를 포함하고 있을 가능성이 높기 때문입니다! 우리는 여전히 설명 아래에 다른 필드를 \"대체 제목\"으로 표시합니다 (단, 다를 경우에만)."

msgid "page.metadata.background.body4"
msgstr "우리는 또한 소스 라이브러리에서 식별자(identifiers) 및 분류자(classifiers)와 같은 <em>코드</em>들을 추출합니다. <em>식별자</em>는 특정 판의 책을 고유하게 나타내며, 예로는 ISBN, DOI, Open Library ID, 구글 북스 ID, 또는 Amazon ID가 있습니다. <em>분류자</em>는 유사한 여러 책을 그룹화합니다. 예로는 듀이 십진분류법(Dewey Decimal Classification, DCC), UDC, LCC, RVK, 또는 GOST가 있습니다. 때때로 이러한 코드는 소스 라이브러리에 명시적으로 연결되어 있으며, 때로는 파일명이나 설명에서 추출할 수 있습니다 (주로 ISBN 및 DOI)."

msgid "page.metadata.background.body5"
msgstr "우리는 식별자를 사용하여 OpenLibrary, ISBNdb, 또는 WorldCat/OCLC와 같은 <em>메타데이터 전용 컬렉션</em>에서 기록을 찾을 수 있습니다. 검색 엔진에는 이러한 컬렉션을 탐색할 수 있는 특정 <em>메타데이터 탭</em>이 있습니다. 우리는 일치하는 기록을 사용하여 누락된 메타데이터 필드를 채우거나 (예: 제목이 누락된 경우), \"대체 제목\"으로 사용합니다 (기존 제목이 있는 경우)."

msgid "page.metadata.background.body6"
msgstr "책의 메타데이터가 어디에서 왔는지 정확히 보려면, 책 페이지의 <em>“기술 세부 사항” 탭</em>을 참조하세요. 해당 책의 미가공 JSON에 대한 링크가 있으며, 원본 기록의 미가공 JSON에 대한 포인터가 포함되어 있습니다."

msgid "page.metadata.background.body7"
msgstr "자세한 내용은 다음 페이지를 참조하세요: <a %(a_datasets)s>데이터셋</a>, <a %(a_search_metadata)s>검색 (메타데이터 탭)</a>, <a %(a_codes)s>코드 탐색기</a>, 및 <a %(a_example)s>예제 메타데이터 JSON</a>. 마지막으로, 모든 메타데이터는 <a %(a_generated)s>생성</a>되거나 <a %(a_downloaded)s>다운로드</a>할 수 있으며, ElasticSearch 및 MariaDB 데이터베이스로 제공됩니다."

msgid "page.metadata.openlib.title"
msgstr "Open Library 연계"

msgid "page.metadata.openlib.body1"
msgstr "메타데이터가 잘못된 파일을 발견하면 어떻게 수정해야 할까요? 소스 라이브러리에 가서 메타데이터를 수정하는 절차를 따를 수 있지만, 파일이 여러 소스 라이브러리에 존재하는 경우는 어떻게 해야 할까요?"

msgid "page.metadata.openlib.body2"
msgstr "안나의 아카이브에서 특별하게 취급되는 식별자가 하나 있습니다. <strong>Open Library의 annas_archive md5 필드는 항상 다른 모든 메타데이터를 무시합니다!</strong> 먼저 조금 뒤로 돌아가서 Open Library에 대해 알아봅시다."

msgid "page.metadata.openlib.body3"
msgstr "Open Library는 2006년 Aaron Swartz에 의해 설립되었으며, 목표는 “출판된 모든 책 하나당 하나의 웹 페이지”를 만드는 것입니다. 이는 책 메타데이터를 위한 일종의 위키피디아로, 누구나 편집할 수 있고, 자유롭게 라이선스가 부여되며, 대량으로 다운로드할 수 있습니다. 이는 우리의 사명과 가장 일치하는 책 데이터베이스입니다. 사실, 안나의 아카이브는 Aaron Swartz의 비전과 삶에서 영감을 받았습니다."

msgid "page.metadata.openlib.body4"
msgstr "우리는 바퀴를 다시 발명하는 대신, 우리의 자원봉사자들이 Open Library를 사용하도록 하기로 결정했습니다. 메타데이터가 잘못된 책을 발견하면 다음과 같은 방법으로 도울 수 있습니다:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " <a %(a_openlib)s>Open Library 웹사이트</a>로 이동하세요."

msgid "page.metadata.openlib.howto.item.2"
msgstr "올바른 책 기록을 찾으세요. <strong>경고:</strong> 올바른 <strong>판</strong>을 선택했는지 확인하세요. Open Library에서는 “작품”과 “판”이 있습니다."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "“작품”은 “해리 포터와 마법사의 돌”일 수 있습니다."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "“판”은 다음과 같습니다:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "1997년 블룸즈버리에서 출판된 초판, 256페이지."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "2003년 Raincoast Books에서 출판된 페이퍼백 판, 223페이지."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "2000년 Media Rodzina에서 출판된 폴란드어 번역본 “Harry Potter I Kamie Filozoficzn”, 328페이지."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "이 모든 판본은 서로 다른 ISBN과 내용을 가지고 있으므로, 올바른 것을 선택하세요!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "기록을 편집하거나 (존재하지 않는 경우 새로 생성하여) 가능한 많은 유용한 정보를 추가하세요! 당신이 어차피 지금 여기 있는만큼, 기록을 정말 멋지게 만들어 보는것도 좋잖아요."

msgid "page.metadata.openlib.howto.item.4"
msgstr "“ID 번호” 아래에서 “Anna's Archive”를 선택하고 안나의 아카이브에서 책의 MD5를 추가하세요. 이는 URL에서 “/md5/” 뒤에 나오는 긴 문자열입니다."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "안나의 아카이브에서 이 기록과 일치하는 다른 파일을 찾아 추가하세요. 나중에 안나의 아카이브 검색 페이지에서 중복으로 그룹화할 수 있습니다."

msgid "page.metadata.openlib.howto.item.5"
msgstr "작업이 끝나면 방금 업데이트한 URL을 적어두세요. Anna’s Archive MD5로 최소 30개의 기록을 업데이트한 후, <a %(a_contact)s>이메일</a>을 보내 목록을 보내주세요. 이 작업을 더 쉽게 할 수 있도록 (그리고 도움에 대한 감사의 표시로) 안나의 아카이브의 무료 멤버십을 드리겠습니다.이 작업은 많은 양의 정보를 추가하는 고품질 편집이어야 하며, 그렇지 않으면 요청이 거부됩니다. 또한 Open Library 관리자에 의해 편집이 되돌려지거나 수정된 경우에도 요청이 거부됩니다."

msgid "page.metadata.openlib.body5"
msgstr "이 작업은 책에만 해당되며, 학술 논문이나 다른 유형의 파일에는 해당되지 않습니다. 다른 유형의 파일에 대해서는 여전히 소스 라이브러리를 찾는 것을 권장합니다. 최신 Open Library 데이터 덤프를 다운로드하고 검색 인덱스를 재생성해야 하므로 변경 사항이 안나의 아카이브에 포함되기까지 몇 주가 걸릴 수 있습니다."

msgid "page.mirrors.title"
msgstr "미러: 자원봉사자 모집"

msgid "page.mirrors.intro"
msgstr "안나의 아카이브의 회복력을 높이기 위해 미러를 운영할 자원봉사자를 찾고 있습니다."

msgid "page.mirrors.text1"
msgstr "저희가 찾고 있는 것은 다음과 같습니다:"

msgid "page.mirrors.list.run_anna"
msgstr "안나의 아카이브 오픈 소스 코드베이스를 실행하고, 코드와 데이터를 정기적으로 업데이트합니다."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "귀하의 버전은 명확하게 미러로 구분됩니다. 예: “철수의 아카이브, 안나의 아카이브 미러”."

msgid "page.mirrors.list.know_the_risks"
msgstr "당신은 이 작업과 관련된 상당한 위험을 감수할 의향이 있습니다. 당신은 필요한 Opsec에 대한 깊은 이해가 있습니다. <a %(a_shadow)s>이</a> <a %(a_pirate)s>게시물</a>의 내용이 뻔하게 읽힙니다."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "이를 실현하기 위해 우리 팀과 협력하여 <a %(a_codebase)s>코드베이스</a>에 기여할 의향이 있습니다."

msgid "page.mirrors.list.maybe_partner"
msgstr "처음에는 파트너 서버 다운로드에 대한 접근 권한을 드리지 않지만, 상황이 잘 진행되면 공유할 수 있습니다."

msgid "page.mirrors.expenses.title"
msgstr "호스팅 비용"

msgid "page.mirrors.expenses.text1"
msgstr "우리는 초기에는 월 $200까지 호스팅 및 VPN 비용을 부담할 의향이 있습니다. 이는 기본 검색 서버와 DMCA-보호를 받는 프록시를 충당하는 데에 충분합니다."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "모든 설정을 완료하고 업데이트를 통해 아카이브를 최신 상태로 유지할 수 있음을 입증한 후에만 호스팅 비용을 지불합니다. 이는 처음 1-2개월 동안은 본인이 비용을 부담해야 함을 의미합니다."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "귀하의 시간은 금전적 보상을 받지 못합니다(우리도 마찬가지입니다). 이는 순수한 자원봉사 작업이기 때문입니다."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "당신이 우리 작업의 개발 및 운영에 상당히 관여하게 되면, 당신이 필요한 데에 사용 할 수 있도록 더 많은 기부 수익을 공유하는 것에 대해 논의할 수 있습니다."

msgid "page.mirrors.getting_started.title"
msgstr "시작하기"

msgid "page.mirrors.getting_started.text1"
msgstr "허가를 요청하거나 기본적인 질문을 하기 위해 <strong>연락하지 마십시오</strong>. 말보다 행동입니다! 모든 정보는 이미 나와 있으니, 미러 설정을 바로 시작하십시오."

msgid "page.mirrors.getting_started.text2"
msgstr "문제가 발생하면 티켓이나 Gitlab에 Merge 요청을 게시하는 것은 자유입니다. “안나의 아카이브”에서 귀하의 웹사이트 이름으로 리브랜딩, (초기) 사용자 계정 비활성화, 또는 도서 페이지에서 본 사이트로 링크 연결과 같은 미러 전용 기능을 귀하와 함께 구축해야 할 수도 있습니다."

msgid "page.mirrors.getting_started.text3"
msgstr "미러를 성공적으로 돌리게되면 저희에게 연락해 주세요. 귀하의 OpSec을 검토하고, 안정적이라면 귀하의 미러에 링크를 걸고 더 긴밀히 협력하겠습니다."

msgid "page.mirrors.getting_started.text4"
msgstr "이와 같은 방식으로 기여해 주실 분들께 미리 감사드립니다! 이는 쉬운 일이 아니지만, 인류 역사상 가장 큰 진정한 오픈 도서관의 장기적인 지속 가능성을 강화할 것입니다."

msgid "page.partner_download.header"
msgstr "파트너 웹사이트에서 다운로드"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ 저속 다운로드는 공식 웹사이트를 통해서만 가능합니다. %(websites)s를 방문하세요."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ 저속 다운로드는 Cloudflare VPN 또는 기타 Cloudflare IP 주소를 통해 사용할 수 없습니다."

msgid "page.partner_download.wait_banner"
msgstr "이 파일을 다운로드하려면 <span %(span_countdown)s>%(wait_seconds)s</span>초 기다려 주세요."

msgid "page.partner_download.url"
msgstr "📚 다운로드하려면 다음 URL을 사용하세요: <a %(a_download)s>지금 다운로드</a>"

msgid "page.partner_download.li4"
msgstr "기다려 주셔서 감사합니다, 이는 모든 사람이 무료로 웹사이트에 접근할 수 있도록 합니다! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "경고: 지난 24시간 동안 귀하의 IP 주소에서 많은 다운로드가 있었습니다. 다운로드 속도가 평소보다 느릴 수 있습니다."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "지난 24시간 동안 귀하의 IP 주소에서 다운로드한 횟수: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "VPN, 공유 인터넷 연결을 사용 중이거나 ISP가 IP를 공유하는 경우, 이 경고가 발생할 수 있습니다."

msgid "page.partner_download.wait"
msgstr "모두가 파일을 무료로 다운로드할 수 있는 기회를 제공하기 위해, 이 파일을 다운로드하기 전에 잠시 기다려야 합니다."

msgid "page.partner_download.li1"
msgstr "기다리는 동안 다른 탭에서 안나의 아카이브를 계속 탐색하세요 (브라우저가 백그라운드 탭 새로고침을 지원하는 경우)."

msgid "page.partner_download.li2"
msgstr "여러 다운로드 페이지가 동시에 로드될 때까지 기다려도 됩니다 (하지만 서버당 한 번에 하나의 파일만 다운로드해 주세요)."

msgid "page.partner_download.li3"
msgstr "다운로드 링크를 받으면 몇 시간 동안 유효합니다."

msgid "layout.index.header.title"
msgstr "안나의 아카이브"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "안나의 아카이브의 기록"

msgid "page.scidb.download"
msgstr "다운로드"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "인간 지식의 접근성과 장기 보존을 지원하려면 <a %(a_donate)s>회원</a>이 되세요."

msgid "page.scidb.please_donate_bonus"
msgstr "보너스로, 🧬&nbsp;SciDB는 회원에게 더 빠르게 로드되며, 제한이 없습니다."

msgid "page.scidb.refresh"
msgstr "작동하지 않나요? <a %(a_refresh)s>새로 고침</a>을 시도하세요."

msgid "page.scidb.no_preview_new"
msgstr "아직 미리보기를 사용할 수 없습니다. <a %(a_path)s>안나의 아카이브</a>에서 파일을 다운로드하세요."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB는 Sci-Hub의 후계자로, 익숙한 인터페이스와 PDF 직접 보기 기능을 제공합니다. DOI를 입력하여 보세요."

msgid "page.home.scidb.text3"
msgstr "저희는 Sci-Hub 컬렉션 전체와 새로운 논문들을 보유하고 있습니다. 대부분은 Sci-Hub와 유사한 친숙한 인터페이스로 직접 볼 수 있습니다. 일부는 외부 소스를 통해 다운로드할 수 있으며, 이 경우 해당 링크를 제공합니다."

msgid "page.search.title.results"
msgstr "%(search_input)s - 검색"

msgid "page.search.title.new"
msgstr "새 검색"

msgid "page.search.icon.include_only"
msgstr "이것만 포함"

msgid "page.search.icon.exclude"
msgstr "제외"

msgid "page.search.icon.unchecked"
msgstr "체크 해제됨"

msgid "page.search.tabs.download"
msgstr "다운로드"

msgid "page.search.tabs.journals"
msgstr "저널 기사"

msgid "page.search.tabs.digital_lending"
msgstr "디지털 대출"

msgid "page.search.tabs.metadata"
msgstr "메타데이터"

msgid "common.search.placeholder"
msgstr "제목, 저자, DOI, ISBN, MD5, …"

msgid "common.search.submit"
msgstr "검색"

msgid "page.search.search_settings"
msgstr "검색 설정"

msgid "page.search.submit"
msgstr "검색"

msgid "page.search.too_long_broad_query"
msgstr "검색 시간이 너무 오래 걸렸습니다. 이는 광범위한 쿼리에서 흔히 발생합니다. 필터 수치가 정확하지 않을 수 있습니다."

msgid "page.search.too_inaccurate"
msgstr "검색 시간이 너무 오래 걸려 부정확한 결과가 표시될 수 있습니다. 때때로 <a %(a_reload)s>페이지를 새로 고침</a>하면 도움이 됩니다."

msgid "page.search.filters.display.header"
msgstr "화면(디스플레이)"

msgid "page.search.filters.display.list"
msgstr "목록"

msgid "page.search.filters.display.table"
msgstr "(작업)테이블"

msgid "page.search.advanced.header"
msgstr "고급"

msgid "page.search.advanced.description_comments"
msgstr "설명 및 메타데이터 댓글 검색"

msgid "page.search.advanced.add_specific"
msgstr "특정 검색 필드 추가"

msgid "common.specific_search_fields.select"
msgstr "(특정 필드 검색)"

msgid "page.search.advanced.field.year_published"
msgstr "출판 연도"

msgid "page.search.filters.content.header"
msgstr "콘텐츠"

msgid "page.search.filters.filetype.header"
msgstr "파일 형식"

msgid "page.search.more"
msgstr "더 보기…"

msgid "page.search.filters.access.header"
msgstr "접근"

msgid "page.search.filters.source.header"
msgstr "출처"

msgid "page.search.filters.source.scraped"
msgstr "AA에 의해 스크랩되고 오픈 소스화됨"

msgid "page.search.filters.language.header"
msgstr "언어"

msgid "page.search.filters.order_by.header"
msgstr "정렬 기준"

msgid "page.search.filters.sorting.most_relevant"
msgstr "가장 관련성 높은"

msgid "page.search.filters.sorting.newest"
msgstr "최신"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(출판 연도)"

msgid "page.search.filters.sorting.oldest"
msgstr "가장 오래된"

msgid "page.search.filters.sorting.largest"
msgstr "가장 큰"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(파일 크기)"

msgid "page.search.filters.sorting.smallest"
msgstr "가장 작은"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(오픈 소스)"

msgid "page.search.filters.sorting.random"
msgstr "무작위"

msgid "page.search.header.update_info"
msgstr "검색 인덱스는 매월 업데이트됩니다. 현재 %(last_data_refresh_date)s까지의 항목이 포함되어 있습니다. 더 기술적인 정보는 %(link_open_tag)s데이터셋 페이지</a>를 참조하세요."

msgid "page.search.header.codes_explorer"
msgstr "코드로 검색 인덱스를 탐색하려면 <a %(a_href)s>코드 탐색기</a>를 사용하세요."

msgid "page.search.results.search_downloads"
msgstr "입력 상자에 입력하여 %(count)s 직접 다운로드 가능한 파일들의 카탈로그에서 검색해보세요. 저희는 이를 <a %(a_preserve)s>영원히 보존합니다</a>."

msgid "page.search.results.help_preserve"
msgstr "사실, 누구든지 우리의 <a %(a_torrents)s>통합 토렌트 목록</a>을 시딩하여 이 파일들을 보존하는 데 도움을 줄 수 있습니다."

msgid "page.search.results.most_comprehensive"
msgstr "우리는 현재 세계에서 가장 포괄적인 도서, 논문 및 기타 서면 작업의 열린 카탈로그를 보유하고 있습니다. 우리는 Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>등을 미러링합니다</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "우리가 미러링해야 할 다른 \"섀도우 라이브러리\"를 찾거나 질문이 있으면 %(email)s로 연락해 주세요."

msgid "page.search.results.dmca"
msgstr "DMCA / 저작권 클레임에 대해서는 <a %(a_copyright)s>여기를 클릭</a>하세요."

msgid "page.search.results.shortcuts"
msgstr "팁: 더 빠른 탐색을 위해 키보드 단축키 “/” (검색 포커스), “enter” (검색), “j” (위로), “k” (아래로), “<” (이전 페이지), “>” (다음 페이지)를 사용하세요."

msgid "page.search.results.looking_for_papers"
msgstr "논문을 찾고 계신가요?"

msgid "page.search.results.search_journals"
msgstr "%(count)s 학술 논문 및 저널 기사를 검색하려면 상자에 입력하세요. 우리는 이를 <a %(a_preserve)s>영구히 보존합니다</a>."

msgid "page.search.results.search_digital_lending"
msgstr "디지털 대출 도서관에서 파일을 검색하려면 상자에 입력하세요."

msgid "page.search.results.digital_lending_info"
msgstr "이 검색 인덱스에는 현재 Internet Archive의 디지털 대출 도서관의 메타데이터가 포함되어 있습니다. <a %(a_datasets)s>우리의 데이터셋에 대해 더 알아보기</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "더 많은 디지털 대출 도서관을 보려면 <a %(a_wikipedia)s>위키백과</a>와 <a %(a_mobileread)s>MobileRead Wiki</a>를 참조하세요."

msgid "page.search.results.search_metadata"
msgstr "도서관에서 메타데이터를 검색하려면 입력 상자에 입력하세요. 이는 <a %(a_request)s>파일 요청</a> 시 유용할 수 있습니다."

msgid "page.search.results.metadata_info"
msgstr "이 검색 인덱스에는 다양한 메타데이터 소스의 메타데이터가 포함되어 있습니다. <a %(a_datasets)s>우리의 데이터셋에 대해 더 알아보기</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "메타데이터의 경우, 원본 기록을 표시합니다. 기록 병합은 하지 않습니다."

msgid "page.search.results.metadata_info_more"
msgstr "전 세계에는 서면 작업에 대한 메타데이터의 출처가 매우 많습니다. <a %(a_wikipedia)s>이 위키백과 페이지</a>가 좋은 시작점이지만, 다른 좋은 목록을 알고 계신다면 알려주세요."

msgid "page.search.results.search_generic"
msgstr "검색하려면 상자에 입력하세요."

msgid "page.search.results.these_are_records"
msgstr "이것들은 메타데이터 기록이며, <span %(classname)s>다운로드 가능한 파일이 아닙니다</span>."

msgid "page.search.results.error.header"
msgstr "검색 중 오류가 발생했습니다."

msgid "page.search.results.error.unknown"
msgstr "<a %(a_reload)s>페이지를 다시 로드</a>해 보세요. 문제가 지속되면 %(email)s로 이메일을 보내주세요."

msgid "page.search.results.none"
msgstr "<span %(classname)s>파일을 찾을 수 없습니다.</span> 더 적은 검색어를 사용하거나, 다른 검색어와 필터를 시도해 사용해보세요."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ 검색 서버가 느릴 때 가끔 문제가 발생합니다. 이런 경우, <a %(a_attrs)s>새로고침</a>이 도움이 될 수 있습니다."

msgid "page.search.found_matches.main"
msgstr "다음에서 일치하는 항목을 찾았습니다: %(in)s. <a %(a_request)s>파일 요청</a> 시 해당 URL을 참조할 수 있습니다."

msgid "page.search.found_matches.journals"
msgstr "저널 아티클 (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "디지털 대출 (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "메타데이터 (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "결과 %(from)s-%(to)s (%(total)s 전체)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ 부분 일치"

msgid "page.search.results.partial"
msgstr "%(num)d 부분 일치"

msgid "page.volunteering.title"
msgstr "자원봉사 & 현상금"

msgid "page.volunteering.intro.text1"
msgstr "안나의 아카이브는 귀하와 같은 자원봉사자들에게 의존합니다. 모든 수준의 헌신을 환영하며, 저희가 찾고 있는 도움의 주요 카테고리는 두 가지입니다:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>가벼운 자원봉사 작업:</span> 여기저기 몇 시간만 할애할 수 있다면 도울 수 있는 많은 방법이 있습니다. 일관된 자원봉사자에게는 <span %(bold)s>🤝 안나의 아카이브 멤버십</span>을 보상합니다."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>중요한 자원봉사 작업 (USD$50-USD$5,000 보상금):</span> 우리의 사명에 많은 시간과/또는 자원을 할애할 수 있다면, 더 긴밀히 협력하고 싶습니다. 언젠간 내부 팀에 합류할 수도 있습니다. 예산이 빠듯하지만, 가장 중요한 작업들에 대해 <span %(bold)s>💰 금전적 보상</span>을 제공할 수 있습니다."

msgid "page.volunteering.intro.text2"
msgstr "자원봉사를 할 시간이 없다면, <a %(a_donate)s>기부</a>, <a %(a_torrents)s>토렌트 시딩</a>, <a %(a_uploading)s>책 업로드</a> 또는 <a %(a_help)s>친구에게 안나의 아카이브를 소개</a>함으로써 많은 도움을 줄 수 있습니다."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>기업:</span> 기업 수준의 기부나 새로운 컬렉션(예: 새로운 스캔, OCR된 데이터셋, 데이터 강화)과 교환하여 컬렉션에 대한 고속 직접 액세스를 제공합니다. <a %(a_contact)s>문의</a>해 주세요. 또한 우리의 <a %(a_llm)s>LLM 페이지</a>를 참조하세요."

msgid "page.volunteering.section.light.heading"
msgstr "가벼운 자원봉사"

msgid "page.volunteering.section.light.text1"
msgstr "몇 시간의 여유가 있다면 여러 가지 방법으로 도울 수 있습니다. <a %(a_telegram)s>텔레그램의 자원봉사자 채팅</a>에 꼭 가입하세요."

msgid "page.volunteering.section.light.text2"
msgstr "감사의 표시로, 기본 마일스톤에 대해 일반적으로 6개월의 “복덩이 사서” 멤버십을 제공하며, 지속적인 자원봉사 작업에 대해 더 많은 혜택을 드립니다. 모든 마일스톤은 높은 품질의 작업이 필요합니다 — 대충한 작업은 도움이 되기보다 해가 되므로 거부될 것입니다. 마일스톤에 도달하면 <a %(a_contact)s>이메일</a>을 보내주세요."

msgid "page.volunteering.table.header.task"
msgstr "작업"

msgid "page.volunteering.table.header.milestone"
msgstr "이정표"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "안나의 아카이브를 알리기. 예를 들어, AA에서 책을 추천하거나, 블로그 게시물에 링크를 걸거나, 일반적으로 사람들을 우리 웹사이트로 안내하는 것입니다."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s개의 링크 또는 스크린샷."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "이것들은 안나의 아카이브에 대해 누군가에게 알려주고, 그들이 감사하는 모습을 보여주어야 합니다."

msgid "page.volunteering.table.open_library.task"
msgstr "Open Library와 <a %(a_metadata)s>연결</a>하여 메타데이터 개선."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "<a %(a_list)s>무작위 메타데이터 문제 목록</a>을 시작점으로 사용할 수 있습니다."

msgid "page.volunteering.table.open_library.leave_comment"
msgstr "해결하려는 이슈에 댓글을 남겨 다른 사람들이 중복 작업을 하지 않도록 하세요."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s개 기록의 링크를 개선."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>웹사이트 번역</a>."

msgid "page.volunteering.table.translate.milestone"
msgstr "한 언어로 완전히 번역 (이미 거의 완료되지 않은 경우)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "자신의 언어로 된 안나의 아카이브의 위키백과 페이지 개선. 다른 언어의 안나의 아카이브 위키백과 페이지에 우리 웹사이트 및 블로그의 정보 추가하기. 관련 페이지에 안나의 아카이브에 대한 참조 추가."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "현저한 기여를 한 편집 기록의 링크."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Z-Library 또는 Library Genesis 포럼에서 책(또는 논문 등) 요청을 충족. 자체 책 요청 시스템은 없지만, 해당 도서관을 미러링하므로 그들을 개선하면 안나의 아카이브도 개선됩니다."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s개의 요청을 완료한 링크 또는 스크린샷."

msgid "page.volunteering.table.misc.task"
msgstr "<a %(a_telegram)s>Telegram의 자원봉사자 채팅</a>에 게시된 작은 작업들. 보통 멤버십을 보상으로 제공하지만, 가끔은 작은 현상금을 제공합니다."

msgid "page.volunteering.table.misc.task2"
msgstr "자원봉사자 채팅 그룹에 게시된 작은 작업들."

msgid "page.volunteering.table.misc.milestone"
msgstr "작업에 따라 상이."

msgid "page.volunteering.section.bounties.heading"
msgstr "현상금"

msgid "page.volunteering.section.bounties.text1"
msgstr "저희는 항상 탄탄한 프로그래밍 또는 공격 보안 기술을 가진 사람들이 참여하기를 원합니다. 인류의 유산을 보존하는 데 큰 기여를 할 수 있습니다."

msgid "page.volunteering.section.bounties.text2"
msgstr "감사의 표시로, 중요한 기여를 해주신 분들께 멤버십을 드립니다. 특히 중요한 어려운 작업을 수행해주신 분들께는 금전적 보상도 드립니다. 이를 직업으로 보지는 마세요. 이것은 발생한 비용을 보충하는 데 도움이 될 수 있는 추가적인 인센티브입니다."

msgid "page.volunteering.section.bounties.text3"
msgstr "저희의 대부분의 코드는 오픈 소스이며, 현상금을 수여할 때 귀하의 코드도 오픈 소스로 제공해 주시길 요청드립니다. 일부 예외 사항은 개별적으로 논의할 수 있습니다."

msgid "page.volunteering.section.bounties.text4"
msgstr "현상금은 작업을 처음 완료한 사람에게 수여됩니다. 다른 사람들에게 작업 중임을 알리기 위해 현상금 티켓에 댓글을 남겨주시면, 다른 사람들이 잠시 멈추거나 팀을 이루기 위해 연락할 수 있습니다. 그러나 다른 사람들도 여전히 작업을 진행하고 먼저 완료하려고 할 수 있습니다. 하지만 부실한 작업에는 현상금을 수여하지 않습니다. 두 개의 고품질 제출물이 가까운 시일 내에 제출된 경우(하루나 이틀 이내), 저희 재량에 따라 두 제출물 모두에게 현상금을 수여할 수 있습니다. 예를 들어 첫 번째 제출물에 100%%, 두 번째 제출물에 50%%을 수여할 수 있습니다(총 150%%)."

msgid "page.volunteering.section.bounties.text5"
msgstr "더 큰 현상금(특히 스크래핑 현상금)의 경우, 약 5%%를 완료했을 때 저희에게 연락해 주시고, 귀하의 방법이 전체 마일스톤에 적용될 수 있다고 확신하시면 됩니다. 귀하의 방법을 저희와 공유해 주셔야 피드백을 드릴 수 있습니다. 또한, 이를 통해 여러 사람이 현상금에 가까워질 경우 어떻게 할지 결정할 수 있습니다. 예를 들어, 여러 사람에게 현상금을 수여하거나, 팀을 이루도록 권장하는 등의 조치를 취할 수 있습니다."

msgid "page.volunteering.section.bounties.text6"
msgstr "경고: 높은 현상금 작업들은 <span %(bold)s>어렵습니다</span> — 더 쉬운 작업부터 시작하는 것이 현명할 수 있습니다."

msgid "page.volunteering.section.bounties.text7"
msgstr "저희의 <a %(a_gitlab)s>Gitlab 이슈 목록</a>으로 이동하여 \"Label priority\"로 정렬하세요. 이는 저희가 중요하게 생각하는 작업의 대략적인 순서를 보여줍니다. 명시적인 현상금이 없는 작업도 여전히 멤버십 자격이 있으며, 특히 \"Accepted\"와 \"Anna’s favorite\"로 표시된 작업이 그렇습니다. \"Starter project\"로 시작하는 것이 좋을 수 있습니다."

#, fuzzy
msgid "blog.template.subheading"
msgstr "<a %(wikipedia_annas_archive)s>안나의 아카이브</a>, 인류 역사상 가장 개방된 도서관에 대한 업데이트입니다."

msgid "layout.index.title"
msgstr "안나의 아카이브"

msgid "layout.index.meta.description"
msgstr "세계 최대의 오픈 소스 오픈 데이터 도서관. Sci-Hub, Library Genesis, Z-Library 등을 미러링합니다."

msgid "layout.index.meta.opensearch"
msgstr "안나의 아카이브 검색"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "안나의 아카이브가 여러분의 도움이 필요합니다!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "많은 사람들이 우리를 무너뜨리려 하지만, 우리는 맞서 싸웁니다."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "지금 기부하시면 고속 다운로드 수가 <strong>두 배</strong>로 증가합니다."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "이번 달 말까지 유효합니다."

msgid "layout.index.header.nav.donate"
msgstr "기부하기"

msgid "layout.index.header.banner.holiday_gift"
msgstr "인류의 지식을 지켜내는 것: 훌륭한 명절 선물입니다!"

msgid "layout.index.header.banner.surprise"
msgstr "사랑하는 사람을 놀라게 하고 멤버십 계정을 제공하세요."

msgid "layout.index.header.banner.mirrors"
msgstr "안나의 아카이브의 회복력을 높이기 위해, 미러 사이트를 운영할 자원봉사자를 찾고 있습니다."

msgid "layout.index.header.banner.valentine_gift"
msgstr "완벽한 발렌타인 선물!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "새로운 기부 방법이 생겼습니다: %(method_name)s. %(donate_link_open_tag)s기부</a>를 고려해 주세요 — 이 웹사이트를 운영하는 데 비용이 많이 들며, 여러분의 기부는 정말로 큰 도움이 됩니다. 감사합니다."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "우리는 세계에서 가장 큰 만화 쉐도우 라이브러리를 백업하기 위해 <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">모금 활동을 진행하고 있습니다</a>. 도움을 주셔서 감사합니다! <a href=\"/donate\">기부하기.</a> 기부할 수 없다면, 친구들에게 알리고 <a href=\"https://www.reddit.com/r/Annas_Archive\">레딧</a> 또는 <a href=\"https://t.me/annasarchiveorg\">텔레그램</a>에서 우리를 팔로우하여 지원해 주세요."

msgid "layout.index.header.recent_downloads"
msgstr "최근 다운로드:"

msgid "layout.index.header.nav.search"
msgstr "검색"

msgid "layout.index.header.nav.faq"
msgstr "자주 묻는 질문"

msgid "layout.index.header.nav.improve_metadata"
msgstr "메타데이터 개선"

msgid "layout.index.header.nav.volunteering"
msgstr "자원봉사 & 현상금"

msgid "layout.index.header.nav.datasets"
msgstr "데이터 세트"

msgid "layout.index.header.nav.torrents"
msgstr "토렌트(torrent)"

msgid "layout.index.header.nav.activity"
msgstr "활동"

msgid "layout.index.header.nav.codes"
msgstr "코드 탐험가"

msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

msgid "layout.index.header.nav.home"
msgstr "홈"

msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

msgid "layout.index.header.nav.translate"
msgstr "번역 ↗"

msgid "layout.index.header.nav.login_register"
msgstr "“로그인 / 회원가입"

msgid "layout.index.header.nav.account"
msgstr "계정"

msgid "layout.index.footer.list1.header"
msgstr "안나의 아카이브"

msgid "layout.index.footer.list2.header"
msgstr "연락하기"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / 저작권 청구"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit(레딧)"

msgid "layout.index.header.nav.advanced"
msgstr "고급"

msgid "layout.index.header.nav.security"
msgstr "보안"

msgid "layout.index.footer.list3.header"
msgstr "대안"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "비제휴"

msgid "page.search.results.issues"
msgstr "❌ 이 파일에 문제가 있을 수 있습니다."

msgid "page.search.results.fast_download"
msgstr "고속 다운로드"

msgid "page.donate.copy"
msgstr "복사"

msgid "page.donate.copied"
msgstr "복사했습니다!"

msgid "page.search.pagination.prev"
msgstr "이전"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "다음"

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr ""

#~ msgid "layout.index.header.nav.home"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_tor"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_tor_extra"
#~ msgstr ""

#~ msgid "page.isbn.title"
#~ msgstr ""

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr ""

#~ msgid "page.isbn.invalid.header"
#~ msgstr ""

#~ msgid "page.isbn.invalid.text"
#~ msgstr ""

#~ msgid "page.isbn.results.text"
#~ msgstr ""

#~ msgid "page.isbn.results.none"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr ""

#~ msgid "page.donate.header.text3"
#~ msgstr "계정을 만들지 않고도 기부할 수 있습니다:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "일회성 기부(특전 없음)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr ""

#~ msgid "page.donate.crypto.intro"
#~ msgstr ""

#~ msgid "page.donate.text_thank_you"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr ""

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr ""

#~ msgid "page.donate.login"
#~ msgstr "회원이 되려면 <a href=\"/login\">로그인하거나 등록</a>하십시오. 계정을 만들지 않으려면 위의 \"일회성 익명 기부\"를 선택하십시오. 지원해 주셔서 감사합니다!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.about"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr ""

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.software"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr ""

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr ""

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr ""

#~ msgid "page.doi.breadcrumbs"
#~ msgstr ""

#~ msgid "page.doi.invalid.header"
#~ msgstr ""

#~ msgid "page.doi.invalid.text"
#~ msgstr ""

#~ msgid "page.doi.box.header"
#~ msgstr ""

#~ msgid "page.doi.box.canonical_url"
#~ msgstr ""

#~ msgid "page.doi.box.scihub"
#~ msgstr ""

#~ msgid "page.doi.results.text"
#~ msgstr ""

#~ msgid "page.doi.results.none"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr ""

#~ msgid "page.donate.faq.monetizing"
#~ msgstr ""

#~ msgid "page.request.title"
#~ msgstr ""

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr ""

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr ""

#~ msgid "page.about.header"
#~ msgstr ""

#~ msgid "page.home.search.header"
#~ msgstr ""

#~ msgid "page.home.search.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr ""

#~ msgid "page.home.explore.header"
#~ msgstr ""

#~ msgid "page.home.explore.intro"
#~ msgstr ""

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "모바일 앱"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr "비공식 WeChat"

#~ msgid "layout.index.header.nav.request"
#~ msgstr "도서 요청"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "업로드"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr "친구 추천"

#~ msgid "page.about.help.header"
#~ msgstr ""

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr ""

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "미러사이트들: 자원봉사자들 부탁"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "이번 달에만!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub이 새로운 논문의 업로드를 <a %(a_closed)s>일시 중단</a>했습니다."

#~ msgid "page.donate.payment.intro"
#~ msgstr "결제 옵션을 선택하세요. 수수료가 (훨씬) 적기 때문에 암호화 기반 결제 %(bitcoin_icon)s에 대해 할인을 제공합니다."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "결제 옵션을 선택하세요. 기존 결제 프로세서가 우리와 협력하기를 거부하기 때문에 현재는 암호화 기반 결제 %(bitcoin_icon)s만 있습니다."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "은행들이 우리와 거래하기를 원하지 않기 때문에 신용/직불 카드를 직접 지원할 수 없습니다. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "그러나, 다른 결제 방법을 사용하여 신용/직불 카드를 사용할 수 있는 여러 가지 방법이 있습니다:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 느리고 외부 다운로드"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "다운로드"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "암호화폐를 처음 사용하신다면, 비트코인(가장 오래되고 많이 사용되는 암호화폐)을 구매하고 기부하는데 %(option1)s,%(option2)s, 또는 %(option3)s 을 사용하기를 추천합니다."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "개선한 기록 30개 링크."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100개의 링크 또는 스크린샷."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "충족한 요청 30개의 링크 또는 스크린샷."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "<a %(a_faq)s>아카이브</a> 또는 <a %(a_llm)s>LLM 훈련</a> 목적으로 이 Datasets를 미러링하는 데 관심이 있다면, 저희에게 연락해 주세요."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "이 데이터를 <a %(a_archival)s>아카이브</a> 또는 <a %(a_llm)s>LLM 교육</a> 목적으로 미러링하는 데 관심이 있으시면 저희에게 연락해 주십시오."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "메인 웹사이트"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN 국가 정보"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "이 데이터셋을 <a %(a_archival)s>아카이브</a>하거나 <a %(a_llm)s>LLM 훈련</a> 목적으로 미러링하는 데 관심이 있다면, 저희에게 연락해 주세요."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "국제 ISBN 기관은 정기적으로 국가 ISBN 기관에 할당한 범위를 발표합니다. 이를 통해 이 ISBN이 속한 국가, 지역 또는 언어 그룹을 유추할 수 있습니다. 현재 저희는 이 데이터를 <a %(a_isbnlib)s>isbnlib</a> Python 라이브러리를 통해 간접적으로 사용하고 있습니다."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "리소스"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "마지막 업데이트: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN 웹사이트"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "메타데이터"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "“scimag” 제외"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "메타데이터 수집에 대한 우리의 영감은 Aaron Swartz가 만든 <a %(a_openlib)s>Open Library</a>의 “출판된 모든 책에 대한 웹 페이지 하나”라는 목표에서 비롯되었습니다."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "그 프로젝트는 잘 진행되었지만, 우리의 독특한 위치 덕분에 그들이 얻을 수 없는 메타데이터를 얻을 수 있습니다."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "또 다른 영감은 <a %(a_blog)s>세계에 얼마나 많은 책이 있는지</a> 알고 싶다는 우리의 열망이었습니다. 이를 통해 아직 구해야 할 책의 수를 계산할 수 있습니다."

#~ msgid "page.partner_download.text1"
#~ msgstr "모든 사람이 파일을 무료로 다운로드할 수 있도록 하기 위해, 이 파일을 다운로드하기 전에 <strong>%(wait_seconds)s 초</strong>를 기다려야 합니다."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "페이지를 자동으로 새로 고침합니다. 다운로드 창을 놓치면 타이머가 다시 시작되므로 자동 새로 고침을 권장합니다."

#~ msgid "page.partner_download.download_now"
#~ msgstr "지금 다운로드"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "변환: 온라인 도구를 사용하여 형식 간 변환을 수행하세요. 예를 들어, epub과 pdf 간 변환을 위해 <a %(a_cloudconvert)s>CloudConvert</a>를 사용하세요."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: 파일을 다운로드하세요 (pdf 또는 epub 지원), 그런 다음 웹, 앱 또는 이메일을 사용하여 <a %(a_kindle)s>Kindle로 보내기</a>. 유용한 도구: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "저자 지원: 이 책이 마음에 들고 여유가 되신다면, 원본을 구매하거나 저자를 직접 지원하는 것을 고려해보세요."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "도서관 지원: 이 책이 지역 도서관에 있다면, 무료로 대여하는 것을 고려해 보세요."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s 대량으로 직접 제공되지 않으며, 유료 벽 뒤에서 반대량으로만 제공됩니다."

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive는 <a %(isbndb)s>ISBNdb 메타데이터</a> 컬렉션을 관리합니다."

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb는 다양한 온라인 서점을 스크래핑하여 ISBN 메타데이터를 찾는 회사입니다. Anna’s Archive는 ISBNdb 책 메타데이터의 백업을 만들어 왔습니다. 이 메타데이터는 Anna’s Archive를 통해 이용할 수 있습니다 (현재 검색에서는 사용할 수 없지만, ISBN 번호를 명시적으로 검색하면 가능합니다)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "기술적인 세부 사항은 아래를 참조하십시오. 어느 시점에서 우리는 이 데이터를 사용하여 섀도우 라이브러리에 아직 없는 책을 결정하고, 어떤 책을 찾거나 스캔할지 우선순위를 정할 수 있습니다."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "이 데이터에 대한 우리의 블로그 게시물"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb 스크래핑"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "현재 우리는 4.4GB의 gzipped <a %(a_jsonl)s>JSON Lines</a> 파일(압축 해제 시 20GB)을 포함하는 단일 토렌트를 보유하고 있습니다: \"isbndb_2022_09.jsonl.gz\". PostgreSQL에 \".jsonl\" 파일을 가져오려면 <a %(a_script)s>이 스크립트</a>와 같은 것을 사용할 수 있습니다. %(example_code)s와 같은 것을 사용하여 직접 파이핑하여 실시간으로 압축을 해제할 수도 있습니다."

#~ msgid "page.donate.wait"
#~ msgstr "저희에게 연락하기 전에 최소 <span %(span_hours)s>두 시간</span> 정도를 기다렸다가 이 페이지를 새로 고침하세요."

#~ msgid "page.codes.search_archive"
#~ msgstr "안나의 아카이브에서 “%(term)s” 검색"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "알리페이나 위챗을 사용하여 기부하세요. 다음 페이지에서 선택할 수 있습니다."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "소셜 미디어 및 온라인 포럼에서 안나의 아카이브를 추천하거나 질문에 답변하여 홍보."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s 픽션 컬렉션은 분화되었고, 2022년 이후로 업데이트되지 않았지만 여전히 <a %(libgenli)s>토렌트</a>가 있습니다 (직접 다운로드도 가능합니다)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s 안나의 아카이브와 Libgen.li는 <a %(comics)s>만화책</a>과 <a %(magazines)s>잡지</a> 컬렉션을 공동으로 관리합니다."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s 러시아어 픽션 및 표준 문서 컬렉션에 대한 토렌트는 없습니다."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "추가 콘텐츠에 대한 토렌트는 제공되지 않습니다. Libgen.li 웹사이트에 있는 토렌트는 여기 나열된 다른 토렌트의 미러입니다. 한 가지 예외는 %(fiction_starting_point)s부터 시작하는 소설 토렌트입니다. 만화와 잡지 토렌트는 안나의 아카이브와 Libgen.li의 협력으로 출시됩니다."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "<a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> 컬렉션에서 가져왔으며, 정확한 출처는 불분명합니다. 일부는 the-eye.eu에서, 일부는 다른 소스에서 가져왔습니다."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram(텔레그램)"

