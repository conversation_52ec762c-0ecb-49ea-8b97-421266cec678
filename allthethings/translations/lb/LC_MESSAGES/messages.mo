��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b _  sd l  �f +   @h 4  lh 
  �i �  �k �  �m    o R   @p v   �p G   
q k   Rq �  �q a  �s   �t 
  �u   x �  
y �  �z �  �| �   �~ �  � �  �� =   l� �   �� Q   �� (  �    
� L  +� G   x� %   ��    �    �� E   �    `�    }� '   ��     �� &   ي     � H   � O  Z� 8  �� _   � ;   C� 
   �    ��    ��    ��    ��    ю 	   ގ    �    �� "    �    #�    )� 	   C� 
   M�    [�    g� "   ��    �� �  �� q  W� �   ɓ +   M� |  y� �   �� *  �� �   ̗    �� �   Ș    `� �   }� !   2� )  T�    ~� �  �� 2   7� `   j�    ˝ [  ם C  3� �  w� F  >�    �� Q   �� X   � �   B� 1   � .   !� ~   P� A   ϥ G   � 0   Y�    �� w   �� �  � C   � m   $� �   �� K   8� #  �� 0   �� &  ٫ �    � �   �� R   D� �   �� �   i� G   � �   N� �   N� d   (� F   �� �   Բ    e� �   v� 6  � G   R�    �� �   �� �   g�    @� �  I� �   Ӹ   �� �  �� �   +� m  �   �� L   �� G   �� ^   ,� �   �� a   � \   w� B   �� �   � k   �� F   
�    Q� �   ]� [   �� �   ?� �   ��    �� �   ��   �� d  �� \  � �   {� �   � @  � �   U� �   � �   �� t  P� �   ��   |�    �� �   �� �   J� �   +� x  �� �   j� �   �� �   ~� �  0� �   � ,  �� #  �� (   �� Y   � �   u� L   � t  c� �   �� �   �� �   � &   �� H  �� �   � �  ��    �� o   �� �   "� �   �� 	  �� �   �� �  z� �  Y�     
� k  .� �  �� -  >� �  l� B  ?�   �� M   �� �   ��    �� �   �� X  \� �  �� �   M  2  L �       # ^  4 �  �    * 
   K �   V :  ! �   \	 )  3
 �   ] j   D
 
   �
 �  �
 |  D    � �   �   � /  � �    j  � h   [ \   � �   ! 7       V &  n *  � 6  � +   � G   #   k �   � �     [   �  v  �     e"    �" �  �" �   2$ �   �$ L   �% �   �% 6   n& .  �& %  �'    �( 
   ) (   ) @   H) =   �) /   �) W   �) u   O*    �* 5   �* C   + E   S+ 0   �+ <   �+ 2   , r   :,    �,    �, ,  �, K  �- �   J/   C0 �   T1 �   �1 s  �2    A4 �   W4 �   -5 �   �5 v  {6 k  �7   ^9 �  w; �  2= G   �> .   "? +   Q? �   }?    &@ �  'B G  �C o  E $  |F -  �G .  �I -   �J @  ,K @  mL e  �M    O �   &O G  P �   XQ j  �Q   `S    cT /   tT _   �T    U �  
U �  �W �  %Y �   �[ �   �\    >] �   O] �   3^ G   _ y   __ '  �_   a �   b �  �b k   )d *  �d    �e �  �e �   �g �  kh t  fj �  �l �   �n    so   o �  �p    Ar p  Sr �  �s   �u �  �w 
  �y L  �z G   | �  a| �   =~ y   / H   � W   �    J� ;   P� %   �� (   �� #   ۀ    �� �   �   �� 	  ̄ _  ֆ �   6� 
   � �  !� L   � ~  m� �  � Z  �� }  �    c�   o�    ��    �� �  �� g  d�   ̜ l  � 
   Y� �  g� �  \�   B� !  I�   k� �  t� �  �    �� �   İ r   �� �  �� �  Դ �   �� �   ]� �   � �  ��    � �   -� T   � L   H�    �� 6   �� ;   � X  � �  u� �  	� >   �� �  �� �   ��    �� �   �� >   V� �   �� �   � 2   �� `   %�    �� R  �� w  �� �  b� �  F� �   ��    u� u   �� <  ��    7� �   >� �  �� �   �� 9   X� 1   �� h  �� +   -� ^  Y� �  �� �  x�     o� �   �� �   "� P  �� �  �� 
   �� �   �� 3   �� �   �� �   ��   c� �   ~� I   l� +   �� �   �� B   �� �  $� l  �� �   (�   � X   &� J   � �   �� :  y� f  �� �  � �  �� 6   �� �   1�   � J  :� �   ��   I� �  O  +    �  ? -   + �  Y l       � I  �   � �     � 8  �
 )  � b  � �   ` �   �  � �   � >  G `  � ;   � �   # �   �    / 
   O M  Z �   �  @   W!   �! �  �" -  �$ �  �% �  �' �   v) �  ,* X  �+    2- �   G-    . v  . $   �/    �/    �/    �/ #   �/    0    !0    50    I0    P0    b0    n0    v0    �0     �0 	   �0 .   �0    �0    �0    �0    �0 �   �0 Z   �1 #   52    Y2 +   k2 (   �2 0   �2 .   �2     3    @3 	   I3    S3    b3    u3    �3 
   �3 
   �3    �3    �3 0   �3 "   4    (4 !   B4 %   d4 )   �4 1   �4    �4 &   �4 7   $5    \5 C   s5 6   �5    �5 ]   �5 E   S6    �6    �6 !   �6    �6    �6    7    &7    ?7    O7    W7    m7    z7    �7 	   �7 
   �7    �7    �7    �7    �7 	   �7    �7 	   �7    8     8    &8 	   -8    78    G8    S8    o8    w8    �8    �8 
   �8 	   �8    �8 "   �8    9    
9 $   9    99    B9    U9    \9    s9    y9    �9 d   �9 �   : d   �: �   !; �  �; t   �=    S>    d>    �>    �>    �>    �>    �> $   �> T   �> =   G? X   �?    �? ;   �? .   7@ h   f@ j   �@ �   :A S   &B H   zB '   �B    �B    �B    C 
   C    C    -C    CC    HC    WC    `C    hC    }C 
   �C    �C 	   �C    �C    �C    �C    �C    �C    �C    D !   D    8D   JD    aE    fE    sE     yE    �E Q   �E \   �E '   PF &   xF 2   �F    �F    �F    �F v   �F    \G    bG %   sG k   �G    H    H x   ,H (   �H J  �H ^   L y   xL �   �L �   �M /   zN   �N �   �O H  SP �   �Q    �R    �R N   �R C    S S   DS b   �S f   �S E   bT s   �T %   U 0   BU    sU    zU S   �U !   �U    V    V     V    &V �   FV    �V /   �V F   W    fW    W ^   �W j   �W �  bX    �Y     Z �   Z    �Z    �Z    �Z    �Z    [ .   [ Z  K[ #   �\ 
   �\ 
   �\ 	   �\ ;  �\ #   ,^    P^    X^ t   `^    �^ 
   �^ /   �^    _    +_ '   1_ �   Y_    �_    ` J   "`    m`    �` 
   �`    �` 0   �` Q   �` 
   Aa ;   Oa �   �a _   Nb O   �b    �b   
c J   d    ad 4   xd    �d �   �d �   �e    0f W   Kf �   �f �   Gg    "h '   Ah    ih �  yh $   j "   7j    Zj "   xj '   �j �   �j    kk    �k )   �k A   �k 
   l    l    5l #   Sl f  wl r  �n &  Qp :   xr 1   �r    �r !   �r 2  s �   Gt �   Au    �u �   v �  �v %   �x (   �x �  �x <  �z 	   | 
   | 7   '| 
   _| G  j|    �} m  �} �  8   �     � u   � 4   ��    �� |   ւ )  S� +  }� �   �� �  P� q   ��   o� T   �� �   ߉ �   �� X   ]� �   �� *   m� !   ��    ��    ̌    Ռ "   �    	� H   $� +   m� 	   �� (   �� 9  ̍ 7   � �   >� �   ;� �   Ґ     T�    u�    ��    �� &   ��    � $   �    +� �   K� #   G� �   k�    0� �   O� y   1� �   �� -  ?� �   m� -   6� �   d� 	   ��   �� �   �    ��   ��    9�    F�    ^� (   t� )   ��    ǜ    М Z   ֜ �   1� �   �    Þ    Ϟ    ՞ �   � U  �� �   � �   ¡    F�    \�    r�    ��    ��    ��    �� D   Ģ -   	� �  7� `   	�    j� v   ~� v   �� U   l� r   ¦ W   5� U   ��    � g   � F   S� �   �� S   !� H   u�    �� �   ԩ `   �� A   "� e   d� S   ʫ 7   � 	   V� 6   `� l   �� �   � A   �� �   ۭ    ��   �� B   �� b   � �   N�    � �  � �   Ȳ    ��    ��    γ    ޳   � 1   �� k   +� �   �� �   s� �   >� �   ۷ �   �� �   L� �   �� g   ƺ >  .� b   m� z  м �   K� �   �� 
   � 
   �� 
   � �   � 
   �� 
   �� O   �� q   � �   �� 
   y� f   �� <   �� {   +� 9   �� �   �� e   j� 
   �� �   �� 
   l� 
   z� 
   �� Y  �� �   �� �  ��    b�    ~�    �� �   ��    P� #   o�   �� �   �� (   P�    y�    �� 9   �� 8   �� 2   � #   M�    q� �   ��    ~� �  ��   Z� �   c� \   � �   _� 7  �   I� ~  O� �   �� �   ~� �   	�    �� �  ��    :� ~  X�   �� ;  �� �   +� h  �� �   #� �   �� }   � -   �� =   +�    i� 8   ~� 
   �� 
   ��    �� �   �� 	   �� k   �� /   Q�    �� 
   �� 
   ��    �� w   �� C   7� 1   {� �   �� 0   �� 
   �� 
   ��    ��    �    +� 	   =�    G� 	   O�    Y� 	   b�    l� 	   u� (   � �   ��    @� 
   Q�    _� 
   k�    y� 
   ��    �� 
   ��    ��    �� %   �� f    �    g� 5   v� a   �� �   � �   �� �   ��   e� �   y� '  3� 
   [� �   i� 5   � E   9� I   � �   �� �   n� P   H� s   ��    
� �   � f   �� �   �    ��     ��    ��    �� 	   ��    ��    � %   �    @�    I�    \�    q�    ��    ��    ��    ��    ��    ��    ��    �    "� "   )� %   L� �   r� �   �� $   �� &   �� o   �� U   D� y   �� ,   � 5   A� 2   w� >   �� >   �� 1   (� �   Z� 
  "�   -�    F  C   \  �   �  *   * �   U �   = ~    :   �    � �   � �   � k   / 5   � l   � �   >     (   0    Y B   n j   � e       �    �    �    � �   � 3   E	 *   y	 3   �	 #   �	    �	 !   
 K   :
    �
 o   �
 A   
   O O   [ X   � �   
 @   �
 !   �
     !   ,     N !   o     � !   � A   � >    |  U P   � 5   # E   Y "   �    � r   � |   = �   � �   � 	   � �   � )   5    _ �   r    W '   o N   � 6   � ^    Q   | Q   �         < �   \ 2    (   C M   l   � C   � �   
 t   � X   " �   { '   � ,   & ~   S    � U   �    E �   b 3   <  3   p     �  .   �  �   �  U   �! N   /" �   ~" `   # M   h# �   �# �   M$ 
   �$    �$ X   �$ N   M%    �%    �%    �%    �% 
   �% -   & �   4& g   �&    %'    <' *   W'    �' )   �' }   �' <   I( y   �( c    )    d) q   �) �   �) /   �* P   +    \+ I   r+ @   �+    �+ �   , w   �, ,   
- K   :-    �- /   �- P   �-    . p   0. +   �.    �. 6   �. e   / �   �/ H   T0    �0    �0 H   �0    1 [   +1 A   �1 �   �1 �   {2     3 >   3 �   V3    4 �   14 E   �4 (   +5 L   T5 	  �5    �6    �6    �6    �6 2   �6 1   7 \   87    �7    �7    �7 (   �7 L   �7 H   =8    �8 F   �8 G   �8    9    /9 2   O9    �9    �9 g   �9 �   : T   ;    m;    |; �   �; X  ?< U   �=    �= c   > �  d? .   +A }   ZA    �A X  �A )   OD ~   yD    �D    E �  &E    G ]   +G r   �G l   �G W   iH    �H `   �H <   :I    wI y   �I B   J 5   NJ W   �J /   �J T   K �   aK �   �K *   �L �   �L u  �M �   O 3   �O 5  
P �   @Q �   7R 6  �R �   (T *   �T    U �   1U 2   �U   
V d   $X E   �X    �X    �X �  �X    �Z �   �Z <  �[ ?  �\ <  ;^ I   x_ J   �_ e   
` -   s` ,   �` `   �` ^   /a    �a     �a 6   �a    �a    b -   )b ^   Wb )   �b    �b ]   �b �   Fc �   Ad    �d 
   �d    �d O   �d ^   Me S   �e �    f n   �f    5g (   Hg �   qg    >h �   Fh �  �h �   �k X   �l /   #m    Sm    Ym    \m H   `m 3   �m �   �m �   fn [   bo    �o    �o &   �o    
p ]   )p    �p @   �p    �p ;   �p ,   q    Lq    _q p   hq    �q    �q +   �q    'r    Dr t   Hr �   �r w   �s u   t \   �t �   �t 	   �u    �u �   �u �   �v �   kw    2x }   9x O   �x G   y m   Oy |   �y R   :z    �z W   �z    �z    {    *{    >{    T{    k{    {    �{    �{    �{ -   �{ -   �{ -   $|     R| 2   s| +   �|    �|    �|    } I   }    f}    }} ?   �} .   �} f   �} &   _~    �~    �~    �~    �~    �~ l   �~ T   e }   � �   8�    ,�    C�    W�    w� 8   �� 	   Ɓ    Ё    � z   ��     u� $   ��    �� 
    	   ͂ A   ׂ    �   /�    C�    T�    n� $   �� %   ��    τ &   � (   � 0   5� /   f� ,   ��    Å Z   ʅ "   %�    H�    d� :   u� Y   ��    
� -   &� (   T� r   }� \   �� A   M�    ��    �� 	   ��    ��    ˈ    � �   � �   ��    :�    P� *   T�    � (   ��    �� 	   �� {   ǋ >   C� �   ��    `� #   v� �   �� #   "�    F�     f� #   �� /   �� %   ێ    � 	   � 5   &� #   \� ,   �� E   �� I  � J   =� Q   �� P   ڑ (   +� �   T� %   �    � [    �    |� (   ��    �� O   ѓ J   !�    l� �   ��    (�    @�     O�    p�    ��    ��    ��    ֕    � Z   � +  _�     �� )   �� �   ֗ �   ̘    ��    ��    ��    ϙ    ߙ    ��    �    )�    >�    P�    c�    r� 
   ��    ��    ��    ��    ��    ˚ !   ۚ �   ��   ֛   � <  b� �  �� �   R� ~  >�    �� 4  ɤ    ��   � �   � �  � �   �� ~  �� 7   � �   >� A   �    0� K   L� N   �� l   � �   T� �   ֮ �   �� �   T� z  (�    �� �   �� �   �� _   {� �   ۵    q� �   �� z  l� �   � �   ��    �� g   �� h   �   ~� }   �� �   � �   ��    #� #   9�    ]� J   y� .   ľ    � �    � C   �� o   ƿ "   6� �   Y� �   � >   �� S   �� S   � b   s� `   �� U   7� }   �� c   �    o� �   ��    p� ;   |� ,   �� _   �� 3   E�    y� 	   �� U   ��    ��    ��    � /   
� -   :� >   h�    ��    �� 
   ��    �� 	   �� N   �� �   5� M   �� @   �    E� "   M� +   p�    �� 
   ��    ��    ��    ��    ��    ��    ��    ��    �� 
   �    �    �    $� 
   4�    B�    V� 
   ^� 	   i�    s� (   y�    �� "   �� s   ��    W� V   m� �   ��    ��    ��    ��    ��    ��    ��    �� �   �� �   j� @   ��    .�    ?� �   S�    �� �   �� ~   q� %   ��    � }   .� �   �� k   � �   �� i   �� 0   � �   I�    ��    �� R   � �   X� !   �� �   � �   �� �   4� W   ��    C�    S�    Z� 
   l�    w� 	   ��    ��    �� �   �� h   S� �   �� �   E� �   "� _   � I   y� �  �� �  �� 
  J� �   U� $  �� h  
�    s� �   z� +  :� �   f� �  � �  �� �   �� h  W�    �� @   �� �   � s  �� E   A� �   �� 	   Y�    c�    j� �   �� B   �    \� =   �� d   � N   � i   �� "   8� �   [� E   � 4   I� N   ~� �   �� !   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: lb
Language-Team: lb <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library ass eng populär (an illegal) Bibliothéik. Si hunn d'Library Genesis Kollektioun geholl an et einfach duerchsichbar gemaach. Doriwwer eraus si se ganz effektiv ginn, nei Bicherbäiträg ze solicitéieren, andeems se d'Benotzer mat verschiddene Virdeeler belounen. Si droen dës nei Bicher de Moment net zréck bei Library Genesis bäi. An am Géigesaz zu Library Genesis maachen si hir Kollektioun net einfach spigelbar, wat eng breet Erhaalung verhënnert. Dëst ass wichteg fir hire Geschäftsmodell, well si Sue fir de Bulk-Zougang zu hirer Kollektioun (méi wéi 10 Bicher pro Dag) berechnen. Mir maache keng moralesch Uerteeler doriwwer, Suen fir de Massezougang zu enger illegaler Bicherkollektioun ze froen. Et ass onbestridden, datt d'Z-Library erfollegräich war, den Zougang zu Wëssen ze erweideren an méi Bicher ze beschafen. Mir sinn einfach hei fir eise Bäitrag ze leeschten: d'laangfristeg Erhalung vun dëser privater Kollektioun ze sécheren. - Anna an d'Team (<a %(reddit)s>Reddit</a>) An der ursprénglecher Verëffentlechung vum Pirate Library Mirror (EDIT: op <a %(wikipedia_annas_archive)s>Annas Archiv</a> geplënnert), hu mir e Spigel vun der Z-Library gemaach, enger grousser illegaler Bicherkollektioun. Als Erënnerung, dat ass wat mir an deem urspréngleche Blogpost geschriwwen hunn: Dës Kollektioun geet zréck op Mëtt 2021. Mëttlerweil ass d'Z-Library mat engem beandrockende Tempo gewuess: si hunn ongeféier 3,8 Millioune nei Bicher derbäigesat. Et ginn do e puer Duplikater, sécher, mee de Gros schéngt legitim nei Bicher ze sinn, oder héichwäerteg Scans vu scho virdru agereecht Bicher. Dëst ass gréisstendeels wéinst der erhéichter Zuel vu fräiwëllege Moderatoren an der Z-Library an hirem Masse-Upload-System mat Duplikatentfernung. Mir wëllen hinnen zu dësen Erfolleger gratuléieren. Mir si frou unzekënnegen, datt mir all Bicher kritt hunn, déi tëscht eisem leschte Spigel an August 2022 an d'Z-Library derbäigesat goufen. Mir hunn och zréckgekuckt a puer Bicher gescrapet, déi mir déi éischte Kéier verpasst hunn. Alles an allem ass dës nei Kollektioun ongeféier 24TB, wat vill méi grouss ass wéi déi lescht (7TB). Eise Spigel ass elo insgesamt 31TB. Erëm hu mir géint Library Genesis deduplizéiert, well et scho Torrents fir déi Kollektioun gëtt. Gitt w.e.g. op de Pirate Library Mirror fir déi nei Kollektioun ze kucken (EDIT: geplënnert op <a %(wikipedia_annas_archive)s>Annas Archiv</a>). Do gëtt et méi Informatiounen doriwwer, wéi d'Dateien strukturéiert sinn, a wat zënter der leschter Kéier geännert huet. Mir wäerten net vun hei aus dohi verlinken, well dëst just eng Blog-Websäit ass, déi keng illegal Materialien host. Natierlech ass Seeden och eng super Manéier, fir eis ze hëllefen. Merci un all déi, déi eis vireg Set vu Torrents seeden. Mir si dankbar fir d'positiv Reaktioun a frou, datt et sou vill Leit gëtt, déi sech op dës ongewéinlech Manéier ëm d'Erhalung vu Wëssen a Kultur këmmeren. 3x nei Bicher derbäigesat zum Pirate Library Mirror (+24TB, 3,8 Millioune Bicher) Liest déi begleedent Artikelen vun TorrentFreak: <a %(torrentfreak)s>éischten</a>, <a %(torrentfreak_2)s>zweeten</a> - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) begleedent Artikelen vun TorrentFreak: <a %(torrentfreak)s>éischten</a>, <a %(torrentfreak_2)s>zweeten</a> Net ze laang hier waren "Schatten-Bibliothéiken" um Ausstierwen. Sci-Hub, den enorme illegalen Archiv vun akademesche Pabeieren, hat opgehalen nei Wierker opzehuelen wéinst Prozesser. "Z-Library", déi gréissten illegal Bibliothéik vu Bicher, huet gesinn, wéi hir vermeintlech Schëpfer wéinst kriminellen Copyright-Verstéiss verhaft goufen. Si hunn et onheemlech fäerdeg bruecht, hir Verhaftung ze entkommen, mee hir Bibliothéik ass net manner a Gefor. E puer Länner maachen schonn eng Versioun dovun. TorrentFreak <a %(torrentfreak)s>huet gemellt</a>, datt China a Japan AI-Ausnamen an hir Copyright-Gesetzer agefouert hunn. Et ass onsécher fir eis, wéi dëst mat internationalen Verträg interagéiert, mee et gëtt sécherlech Ofdeckung fir hir inlännesch Firmen, wat erkläert, wat mir gesinn hunn. Wat Anna’s Archiv ugeet — mir wäerten eis ënnerierdesch Aarbecht weiderféieren, déi op moralescher Iwwerzeegung baséiert. Awer eist gréisste Wonsch ass, an d’Liicht ze trieden, an eisen Impakt legal ze verstäerken. W.e.g. reforméiert d’Copyright. Wéi Z-Library mat der Schléissung konfrontéiert war, hat ech schonn hir ganz Bibliothéik geséchert an hunn no enger Plattform gesicht, fir se ze hiewen. Dat war meng Motivatioun fir d’Anna’s Archiv ze grënnen: eng Fortsetzung vun der Missioun hannert deene fréiere Initiativen. Mir sinn zënterhier gewuess, fir déi gréissten Schatten-Bibliothéik op der Welt ze ginn, mat méi wéi 140 Millioune copyrightgeschützte Texter a ville Formater — Bicher, akademesch Pabeieren, Zäitschrëften, Zeitungen, an méi. Mäi Team an ech si Ideologen. Mir gleewen, datt d’Erhalen an d’Hosten vun dëse Fichieren moralesch richteg ass. Bibliothéiken ronderëm d’Welt gesinn Budgetskierzungen, a mir kënnen d’Mënschheet hir Ierfschaft och net un d’Korporatiounen uvertrauen. Du koum d’AI. Praktesch all grouss Firmen, déi LLMs bauen, hunn eis kontaktéiert, fir op eise Daten ze trainéieren. Déi meescht (awer net all!) US-baséiert Firmen hunn hir Meenung geännert, wéi se d’illegal Natur vun eiser Aarbecht erkannt hunn. Am Géigesaz dozou hunn chinesesch Firmen eis Sammlung begeeschtert ugeholl, anscheinend net gestéiert vun hirer Legalitéit. Dëst ass bemierkenswäert, well China e Signataire vun bal all groussen internationalen Copyright-Verträg ass. Mir hunn ongeféier 30 Firmen héich-Vitesse Zougang ginn. Déi meescht vun hinnen sinn LLM-Firmen, an e puer sinn Datebroker, déi eis Sammlung weiderverkafen. Déi meescht sinn chinesesch, obwuel mir och mat Firmen aus den USA, Europa, Russland, Südkorea, a Japan geschafft hunn. DeepSeek <a %(arxiv)s>huet zouginn</a>, datt eng fréier Versioun op engem Deel vun eiser Sammlung trainéiert gouf, obwuel se iwwer hiert neit Modell zréckhalend sinn (wahrscheinlech och op eise Daten trainéiert). Wann de Westen wëll vir bleiwen am Rennen vun LLMs, an ultimativ AGI, muss et seng Positioun zum Copyright iwwerdenken, an dat séier. Egal ob Dir mat eis iwwer eise moralesche Fall averstane sidd oder net, dëst gëtt elo e Fall vun Ekonomie, an esouguer vun nationaler Sécherheet. All Muechtbléck bauen kënschtlech Super-Wëssenschaftler, Super-Hacker, a Super-Militärs. D’Fräiheet vun Informatioun gëtt eng Iwwerliewensfro fir dës Länner — esouguer eng Fro vun nationaler Sécherheet. Eis Team kënnt aus der ganzer Welt, a mir hunn keng speziell Ausriichtung. Mee mir géifen d’Länner mat staarke Copyright-Gesetzer encouragéieren, dës existenziell Bedroung ze notzen, fir se ze reforméieren. Also, wat maachen? Eis éischt Empfehlung ass einfach: verkierzt d’Copyright-Dauer. An den USA gëtt Copyright fir 70 Joer nom Doud vum Auteur gewährt. Dëst ass absurd. Mir kënnen dëst a Linnen mat Patenter bréngen, déi fir 20 Joer nom Aschécken gewährt ginn. Dëst sollt méi wéi genuch Zäit sinn fir Auteuren vu Bicher, Pabeieren, Musek, Konscht, an aner kreativ Wierker, fir voll fir hir Efforten kompenséiert ze ginn (inklusiv méi laangfristeg Projeten wéi Filmadaptatiounen). Dann, op d’mannst, sollten d’Politiker Ausnamen fir d’Mass-Erhalen an d’Verbreedung vun Texter abauen. Wann verluerene Recetten vun eenzelne Clienten d’Haaptbesuergnis sinn, kéint d’Verdeelung op perséinlechem Niveau verbuede bleiwen. Am Géigenzuch géifen déi, déi fäeg sinn, grouss Sammlungen ze managen — Firmen, déi LLMs trainéieren, zesumme mat Bibliothéiken an aneren Archiven — vun dësen Ausnamen ofgedeckt ginn. Eng Copyright-Reform ass néideg fir d’national Sécherheet TL;DR: Chinesesch LLMs (inklusiv DeepSeek) gi mat mengem illegalen Archiv vu Bicher a Pabeieren trainéiert — dem gréissten op der Welt. De Westen muss d’Copyright-Gesetzer iwwerschaffen als eng Fro vun nationaler Sécherheet. Kuckt w.e.g. den <a %(all_isbns)s>originale Blogpost</a> fir méi Informatiounen. Mir hunn eng Erausfuerderung gestallt fir dëst ze verbesseren. Mir géifen eng éischt Plaz Präisgeld vun $6,000, eng zweet Plaz vun $3,000, an eng drëtt Plaz vun $1,000 verginn. Wéinst der iwwerwältegender Resonanz an den onheemlechen Areechungen hu mir decidéiert de Präispool liicht ze erhéijen an eng véierfach drëtt Plaz vun $500 all ze verginn. D'Gewënner sinn hei ënnendrënner, mee kuckt sécher all Areechungen <a %(annas_archive)s>hei</a>, oder lued eisen <a %(a_2025_01_isbn_visualization_files)s>kombinéierten Torrent</a> erof. Éischt Plaz $6,000: phiresky Dës <a %(phiresky_github)s>Areechung</a> (<a %(annas_archive_note_2951)s>Gitlab Kommentar</a>) ass einfach alles wat mir wollten, an nach méi! Mir hunn besonnesch déi onheemlech flexibel Visualiséierungsoptiounen gären (souguer mat Ënnerstëtzung fir personaliséiert Shaderen), awer mat enger ëmfaassender Lëscht vu Presets. Mir hunn och gären wéi séier a glat alles ass, déi einfach Implementatioun (déi net emol e Backend huet), déi clever Minimap, an déi extensiv Erklärung an hirem <a %(phiresky_github)s>Blogpost</a>. Onheemlech Aarbecht, an de verdéngte Gewënner! - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Eis Häerzer si voll mat Dankbarkeet. Bemierkenswäert Iddien Wolkenkratzer fir Raritéit Vill Schieber fir Datasets ze vergläichen, wéi wann Dir e DJ wiert. Skalebar mat Zuel vu Bicher. Schéin Etiketten. Cool Standardfaarfschema a Wärmekaart. Eenaarteg Kaartesicht a Filteren Annotatiounen, an och Live Statistiken Live Statistiken E puer méi Iddien an Implementatiounen déi mir besonnesch gären hunn: Mir kéinten nach eng Zäit weiderfueren, mee loosst eis hei ophalen. Kuckt sécher all d'Aschreiwungen <a %(annas_archive)s>hei</a>, oder lued eisen <a %(a_2025_01_isbn_visualization_files)s>kombinéierten Torrent</a> erof. Sou vill Aschreiwungen, an all een bréngt eng eenzegaarteg Perspektiv, egal ob an der UI oder der Ëmsetzung. Mir wäerten op d'mannst d'Aschreiwung vum éischte Plaz an eis Haaptwebsäit integréieren, an eventuell nach e puer anerer. Mir hunn och ugefaang ze denken, wéi mir de Prozess vun der Identifikatioun, Bestätegung an dann d'Archivéierung vun de rareste Bicher organiséieren. Méi Informatiounen kommen nach. Merci un all déi, déi deelgeholl hunn. Et ass erstaunlech, datt sou vill Leit sech këmmeren. Einfaches Ëmschalten vun Datasets fir séier Vergläicher. All ISBNen CADAL SSNOen CERLALC Dateleak DuXiu SSIDen EBSCOhost säin eBook Index Google Books Goodreads Internet Archive ISBNdb ISBN Global Register of Publishers Libby Fichieren an Annas Archiv Nexus/STC OCLC/Worldcat OpenLibrary Russesch Staatsbibliothéik Kaiserlech Bibliothéik vu Trantor Zweet Plaz $3,000: hypha “Obwuel perfekt Quadrater a Rechtecker mathematesch gefälleg sinn, bidden se keng iwwerleeën Lokalitéit an engem Mapping Kontext. Ech gleewen datt d'Asymmetrie inherent an dësen Hilbert oder klassesche Morton keng Schwächt ass, mee eng Feature. Genausou wéi Italien seng berühmt stiwelfërmeg Kontur et direkt erkennbar op enger Kaart mécht, kënnen d'eenzegaarteg "Besonderheeten" vun dëse Kéiere als kognitiv Landmarken déngen. Dës Eenzegaartegkeet kann d'raimlech Erënnerung verbesseren an de Benotzer hëllefen sech ze orientéieren, wat potenziell d'Lokaliséierung vu spezifesche Regiounen oder d'Erkennung vu Musteren méi einfach mécht.” Eng aner onheemlech <a %(annas_archive_note_2913)s>Areechung</a>. Net esou flexibel wéi déi éischt Plaz, mee mir hunn eigentlech hir Makro-Niveau Visualiséierung iwwer déi éischt Plaz virgezunn (Raumfëllend Kéier, Grenzen, Beschëlderung, Highlighting, Panning, an Zooming). E <a %(annas_archive_note_2971)s>Kommentar</a> vum Joe Davis huet mat eis resonéiert: An nach ëmmer vill Optiounen fir Visualiséierung an Rendering, souwéi en onheemlech glat an intuitivt UI. Eng zolidd zweet Plaz! - Anna an d'Team (<a %(reddit)s>Reddit</a>) E puer Méint hier hu mir eng <a %(all_isbns)s>$10,000 Präis</a> annoncéiert, fir déi beschtméiglech Visualiséierung vun eisen Daten ze maachen, déi de ISBN Raum weisen. Mir hunn betount, ze weisen, wéi eng Fichieren mir schonn archivéiert hunn/hunn net, an mir hunn spéider e Set vun Daten, déi beschreiwen, wéi vill Bibliothéiken ISBNs halen (e Mooss fir Raritéit). Mir si vun der Reaktioun iwwerwältegt ginn. Et gouf sou vill Kreativitéit. E grousse Merci un all déi, déi deelgeholl hunn: Är Energie an Enthusiasmus si ustiechend! Schlussendlech wollte mir déi folgend Froen beäntweren: <strong>wéi eng Bicher existéieren op der Welt, wéi vill hu mir schonn archivéiert, a op wéi eng Bicher solle mir eis als nächst konzentréieren?</strong> Et ass flott ze gesinn, datt sou vill Leit sech fir dës Froen interesséieren. Mir hunn selwer mat enger einfacher Visualiséierung ugefaangen. An ënner 300kb stellt dëst Bild déi gréisst voll oppen "Lëscht vu Bicher" duer, déi jeemools an der Geschicht vun der Mënschheet zesummegestallt gouf: Drëtt Plaz $500 #1: maxlion An dëser <a %(annas_archive_note_2940)s>Areechung</a> hu mir wierklech déi verschidde Zorte vu Vue gären, besonnesch d'Vergläichs- an d'Verlagsvue. Drëtt Plaz $500 #2: abetusk Obwuel net dat am meeschte poléiert UI, kontrolléiert dës <a %(annas_archive_note_2917)s>Areechung</a> vill vun de Këschten. Mir hunn besonnesch seng Vergläichsfeature gären. Drëtt Plaz $500 #3: conundrumer0 Wéi déi éischt Plaz, huet dës <a %(annas_archive_note_2975)s>Areechung</a> eis mat hirer Flexibilitéit beandrockt. Schlussendlech ass dat wat e super Visualiséierungstool ausmécht: maximal Flexibilitéit fir Power-Benotzer, wärend d'Saachen einfach fir duerchschnëttlech Benotzer bleiwen. Drëtt Plaz $500 #4: charelf Déi lescht <a %(annas_archive_note_2947)s>Areechung</a> fir e Präisgeld ze kréien ass zimlech einfach, awer huet e puer eenzegaarteg Features déi mir wierklech gären hunn. Mir hunn gären wéi se weisen wéi vill Datasets e spezifeschen ISBN ofdecken als Mooss fir Popularitéit/Zouverlässegkeet. Mir hunn och wierklech d'Einfacheit awer Effektivitéit vun engem Opazitéitsslider fir Vergläicher gären. Gewënner vum $10,000 ISBN Visualiséierungspräis TL;DR: Mir hunn e puer onheemlech Areechungen fir de $10,000 ISBN Visualiséierungspräis kritt. Hannergrond Wéi kann Annas Archiv seng Missioun erreechen, all d’Wëssen vun der Mënschheet ze sécheren, ouni ze wëssen, wéi eng Bicher nach dobaussen sinn? Mir brauchen eng TODO-Lëscht. Eng Méiglechkeet, dëst ze kartéieren, ass duerch ISBN-Nummeren, déi zënter den 1970er Joren un all publizéiert Buch zougewise goufen (an de meeschte Länner). Et gëtt keng zentral Autoritéit, déi all ISBN-Zouweisungen kennt. Amplaz ass et e verdeelt System, wou Länner Nummerberäicher kréien, déi dann méi kleng Beräicher un grouss Verleger zouweisen, déi eventuell weider Beräicher un kleng Verleger ënnerdeelen. Schlussendlech ginn eenzel Nummeren un Bicher zougewise. Mir hunn ugefaang, ISBNen <a %(blog)s>virun zwee Joer</a> mat eisem Scrape vun ISBNdb ze kartéieren. Zënterhier hu mir vill méi Metadatenquellen gescraped, wéi <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, an méi. Eng komplett Lëscht fannt Dir op de Säiten “Datasets” an “Torrents” op Annas Archiv. Mir hunn elo bei wäitem déi gréisst voll oppe, einfach erofluedbar Sammlung vu Buchmetadaten (an domat ISBNen) op der Welt. Mir hunn <a %(blog)s>ausféierlech geschriwwen</a> iwwer firwat mir eis ëm d’Erhale këmmeren, a firwat mir aktuell an engem kritesche Fënster sinn. Mir mussen elo rar, ënnerfokusséiert an eenzegaarteg geféierlech Bicher identifizéieren an erhalen. Gutt Metadaten iwwer all Bicher op der Welt ze hunn, hëlleft dobäi. $10,000 Belounung Staark Berécksiichtegung gëtt der Benotzerfrëndlechkeet an dem Ausgesinn ginn. Weist tatsächlech Metadaten fir eenzel ISBNen wann Dir eranzoomt, wéi Titel an Auteur. Besser Raumfëllungskurve. Z.B. eng Zickzack, déi vun 0 bis 4 an der éischter Rei geet an dann zréck (am Réckgang) vun 5 bis 9 an der zweeter Rei — rekursiv ugewannt. Verschidde oder personaliséierbar Faarfschemaen. Spezialvisiounen fir Datasets ze vergläichen. Méiglechkeeten fir Problemer ze debuggen, wéi aner metadata déi net gutt iwwereneestëmmen (z.B. ganz verschidden Titelen). Biller mat Kommentaren iwwer ISBNen oder Beräicher annotéieren. All Heuristiken fir selten oder geféierlech Bicher z'identifizéieren. All kreativ Iddien déi Dir kënnt entwéckelen! Code De Code fir dës Biller ze generéieren, souwéi aner Beispiller, fannt Dir an <a %(annas_archive)s>dësem Dossier</a>. Mir hunn e kompakten Dateformat entwéckelt, mat deem all déi erfuerderlech ISBN-Informatioun ongeféier 75MB (kompriméiert) ass. D'Beschreiwung vum Dateformat an de Code fir et ze generéieren fannt Dir <a %(annas_archive_l1244_1319)s>hei</a>. Fir de Bounty sidd Dir net verflicht dëst ze benotzen, awer et ass wahrscheinlech dat prakteschst Format fir unzefänken. Dir kënnt eis metadata transforméieren wéi Dir wëllt (obwuel all Äre Code open source muss sinn). Mir kënnen net waarden ze gesinn wat Dir entwéckelt. Vill Gléck! Fork dëse Repo, an ännert dëse Blog Post HTML (keng aner Backends ausser eise Flask Backend sinn erlaabt). Maacht d’Bild hei uewen glat zoombar, sou datt Dir bis op eenzel ISBNen zoomt. Klickt op ISBNen soll Iech op eng Metadatensäit oder Sich op Annas Archiv féieren. Dir musst nach ëmmer tëscht all verschiddenen Datasets wiesselen kënnen. Lännerberäicher a Verlegerberäicher solle beim Hover markéiert ginn. Dir kënnt z.B. <a %(github_xlcnd_isbnlib)s>data4info.py an isbnlib</a> fir Lännerinformatioun benotzen, an eis “isbngrp” Scrape fir Verleger (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Et muss gutt op Desktop a mobil funktionéieren. Et gëtt vill ze entdecken hei, dofir annoncéiere mir eng Belounung fir d’Verbesserung vun der Visualiséierung hei uewen. Am Géigesaz zu de meeschte vun eise Belounungen ass dës Zäitgebonnen. Dir musst Ären Open Source Code bis den 2025-01-31 (23:59 UTC) <a %(annas_archive)s>aginn</a>. Déi bescht Areechung kritt $6,000, déi zweet Plaz $3,000, an déi drëtt Plaz $1,000. All Beloununge ginn mat Monero (XMR) ausgezeechent. Hei drënner sinn déi minimal Critèren. Wann keng Areechung d’Critèren erfëllt, kënne mir nach ëmmer e puer Belounungen auszeechnen, awer dat wäert op eiser Diskretioun sinn. Fir Bonuspunkten (dëst sinn just Iddien — loosst Är Kreativitéit fräilafen): Dir DÜRFT komplett vun de minimale Critèren ofwäichen, an eng komplett aner Visualiséierung maachen. Wann et wierklech spektakulär ass, da qualifizéiert dat sech fir de Bounty, awer no eiser Diskretioun. Maacht Är Areechungen andeems Dir e Kommentar op <a %(annas_archive)s>dësem Thema</a> postt mat engem Link op Äre geforkte Repo, Merge Request oder Diff. - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dëst Bild ass 1000×800 Pixel. All Pixel representéiert 2,500 ISBNen. Wann mir eng Datei fir en ISBN hunn, maachen mir dee Pixel méi gréng. Wann mir wëssen, datt en ISBN ausgeginn gouf, mee mir hunn keng entspriechend Datei, maachen mir et méi rout. An manner wéi 300kb representéiert dëst Bild präzis déi gréisst voll oppe "Lëscht vu Bicher", déi jeemools an der Geschicht vun der Mënschheet zesummegestallt gouf (e puer honnert GB kompriméiert am Ganzen). Et weist och: et gëtt nach vill Aarbecht ze maachen fir Bicher ze sécheren (mir hunn nëmmen 16%). Visualiséierung vun allen ISBNen — $10,000 Belounung bis 2025-01-31 Dëst Bild representéiert déi gréisst voll oppe "Lëscht vu Bicher", déi jeemools an der Geschicht vun der Mënschheet zesummegestallt gouf. Visualiséierung Nieft dem Iwwersiichtsbild kënne mir och eenzel Datasets kucken, déi mir gesammelt hunn. Benotzt den Dropdown an d’Knäppercher fir tëscht hinnen ze wiesselen. Et ginn vill interessant Musteren an dëse Biller ze gesinn. Firwat ass et eng gewëssen Regelméissegkeet vu Linnen a Blöcken, déi op verschiddene Skalen ze geschéien schéngt? Wat sinn déi eidel Beräicher? Firwat sinn verschidden Datasets sou geklummert? Mir loossen dës Froen als Übung fir de Lieser. - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklusioun Mat dësem Standard kënne mir Verëffentlechungen méi inkrementell maachen an méi einfach nei Datequellen derbäisetzen. Mir hunn schonn e puer spannend Verëffentlechungen an der Pipeline! Mir hoffen och, datt et méi einfach gëtt fir aner Schiedsbibliothéiken eis Kollektiounen ze spigelen. Schliisslech ass eist Zil, mënschlecht Wëssen a Kultur fir ëmmer ze erhalen, also ass méi Redundanz besser. Beispill Loosst eis eist rezent Z-Library Release als Beispill kucken. Et besteet aus zwou Kollektiounen: “<span style="background: #fffaa3">zlib3_records</span>” an “<span style="background: #ffd6fe">zlib3_files</span>”. Dëst erlaabt eis, Metadata-Records separat vun den eigentleche Buchdateien ze scrape an ze verëffentlechen. Sou hu mir zwee Torrents mat Metadata-Dateien verëffentlecht: Mir hunn och eng Rei Torrents mat binären Daten-Dossieren verëffentlecht, awer nëmmen fir d'Kollektioun “<span style="background: #ffd6fe">zlib3_files</span>”, insgesamt 62: Wann mir <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> lafen, kënne mir gesinn, wat dran ass: An dësem Fall ass et d'Metadata vun engem Buch, wéi vun Z-Library gemellt. Op der ieweschter Ebene hu mir nëmmen “aacid” an “metadata”, awer kee “data_folder”, well et keng entspriechend binär Daten gëtt. Den AACID enthält “22430000” als primär ID, déi mir gesinn, ass aus “zlibrary_id” geholl. Mir kënnen erwaarden, datt aner AACs an dëser Kollektioun déi selwecht Struktur hunn. Loosst eis elo <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> lafen: Dëst ass eng vill méi kleng AAC Metadata, obwuel de Gros vun dësem AAC an enger anerer Plaz an enger binärer Datei läit! Schliisslech hu mir dës Kéier e “data_folder”, sou datt mir erwaarden, datt déi entspriechend binär Daten bei <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> lokaliséiert sinn. D'Metadata enthält den “zlibrary_id”, sou datt mir et einfach mam entspriechenden AAC an der “zlib_records” Kollektioun associéieren kënnen. Mir hätten et op verschidde Manéiere kënne associéieren, z. B. duerch AACID — de Standard schreift dat net vir. Notéiert datt et och net néideg ass, datt d'“metadata” Feld selwer JSON ass. Et kéint eng String sinn, déi XML oder all aner Datenformat enthält. Dir kéint souguer Metadata-Informatioun an der assoziéierter binärer Blob späicheren, z. B. wann et vill Daten sinn. Heterogen Fichieren a Metadata, sou no wéi méiglech un den Originalformat. Binär Daten kënnen direkt vun Webserveren wéi Nginx servéiert ginn. Heterogen Identifizéierer an de Quellbibliothéiken, oder souguer Mangel un Identifizéierer. Separat Verëffentlechungen vu Metadata vs Fichierendaten, oder nëmmen Metadata-Verëffentlechungen (z.B. eis ISBNdb Verëffentlechung). Verdeelung duerch Torrents, awer mat der Méiglechkeet vun anere Verdeelungsmethoden (z.B. IPFS). Onverännerbar Opzeechnungen, well mir unhuelen sollten datt eis Torrents fir ëmmer liewen. Inkrementell Verëffentlechungen / ergänzbar Verëffentlechungen. Maschinnliesbar a schreiwbar, praktesch a séier, besonnesch fir eis Stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). E bësse einfach fir mënschlech Inspektioun, obwuel dëst sekundär zu der Liesbarkeet fir Maschinnen ass. Einfach eis Sammlungen mat engem standard gelounten Seedbox ze séien. Designziler Mir këmmeren eis net drëm datt Fichieren einfach manuell um Disk navigéiert kënne ginn, oder sichbar sinn ouni Virveraarbechtung. Mir këmmeren eis net drëm direkt kompatibel mat bestehender Bibliothéiksoftware ze sinn. Och wann et einfach soll sinn fir jiddereen eis Sammlung mat Torrents ze séien, erwaarden mir net datt d’Fichieren benotzbar sinn ouni bedeitend technesch Kenntnisser an Engagement. Eis primär Notzungsfall ass d’Verdeelung vu Fichieren an assoziéierte Metadata aus verschiddene bestehende Sammlungen. Eis wichtegst Iwwerleeungen sinn: E puer net-Ziler: Well Anna’s Archive open source ass, wëlle mir eise Format direkt benotzen. Wann mir eisen Sichindex aktualiséieren, gräifen mir nëmmen op ëffentlech verfügbar Weeër zou, sou datt jiddereen, deen eis Bibliothéik forkt, séier ufänke kann. <strong>AAC.</strong> AAC (Anna’s Archive Container) ass en eenzelt Element, dat aus <strong>Metadata</strong> besteet, an optional <strong>binär Daten</strong>, déi béid onverännerbar sinn. Et huet en global eenzegaartegen Identifizéierer, genannt <strong>AACID</strong>. <strong>AACID.</strong> De Format vun AACID ass dëst: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Zum Beispill, en tatsächlechen AACID, deen mir verëffentlecht hunn, ass <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID Beräich.</strong> Well AACIDs monoton eropgoend Timestamps enthalen, kënne mir dat benotzen, fir Beräicher bannent enger bestëmmter Kollektioun unzeginn. Mir benotzen dëse Format: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wou d'Timestamps inklusiv sinn. Dëst ass konsequent mat der ISO 8601 Notatioun. Beräicher si kontinuéierlech a kënnen iwwerlappen, awer am Fall vun Iwwerlappung mussen identesch Records enthalen wéi déi virdru verëffentlecht an där Kollektioun (well AACs onverännerbar sinn). Feeler Records sinn net erlaabt. <code>{collection}</code>: den Numm vun der Kollektioun, déi ASCII Buschtawen, Zuelen, an Ënnersträicher enthalen däerf (awer keng duebel Ënnersträicher). <code>{collection-specific ID}</code>: en kollektiounsspezifeschen Identifizéierer, wann uwendbar, z.B. den Z-Library ID. Kann ewechgelooss oder gekierzt ginn. Muss ewechgelooss oder gekierzt ginn, wann den AACID soss méi wéi 150 Zeechen hätt. <code>{ISO 8601 timestamp}</code>: eng kuerz Versioun vum ISO 8601, ëmmer an UTC, z.B. <code>20220723T194746Z</code>. Dës Zuel muss fir all Verëffentlechung monoton eropgoen, obwuel seng exakt Semantik pro Kollektioun ënnerscheede kann. Mir proposéieren d'Zäit vum Scraping oder vum Generéiere vum ID ze benotzen. <code>{shortuuid}</code>: e UUID awer kompriméiert op ASCII, z.B. mat base57. Mir benotzen de Moment d'<a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python Bibliothéik. <strong>Binär Daten Dossier.</strong> En Dossier mat de binären Daten vun engem Beräich vun AACs, fir eng bestëmmte Kollektioun. Dës hunn déi folgend Eegeschaften: Den Dossier muss Daten Dateien fir all AACs bannent dem spezifizéierte Beräich enthalen. All Dateid muss säin AACID als Dateinumm hunn (ouni Erweiderungen). Den Dossiernumm muss e AACID Beräich sinn, virgesat mat <code style="color: green">annas_archive_data__</code>, an ouni Suffix. Zum Beispill, eng vun eise tatsächleche Verëffentlechungen huet en Dossier genannt<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Et ass recommandéiert, dës Dossieren e bësse verwaltbar an der Gréisst ze halen, z. B. net méi grouss wéi 100GB-1TB all, obwuel dës Empfehlung sech mat der Zäit ännere kann. <strong>Kollektioun.</strong> All AAC gehéiert zu enger Kollektioun, déi per Definitioun eng Lëscht vun AACs ass, déi semantesch konsequent sinn. Dat bedeit, datt wann Dir eng wesentlech Ännerung am Format vun der Metadata maacht, da musst Dir eng nei Kollektioun erstellen. De Standard <strong>Metadata Datei.</strong> Eng Metadata Datei enthält d'Metadata vun engem Beräich vun AACs, fir eng bestëmmte Kollektioun. Dës hunn déi folgend Eegeschaften: <code>data_folder</code> ass optional, an ass den Numm vum binären Daten Dossier, deen déi entspriechend binär Daten enthält. Den Dateinumm vun den entspriechenden binären Daten an deem Dossier ass den AACID vum Record. All JSON Objet muss déi folgend Felder op der ieweschter Niveau enthalen: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (optional). Keng aner Felder sinn erlaabt. Den Dateinumm muss e AACID Beräich sinn, virgesat mat <code style="color: red">annas_archive_meta__</code> an ofgeschloss mat <code>.jsonl.zstd</code>. Zum Beispill, eng vun eise Verëffentlechungen heescht<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Wéi duerch d'Dateierweiderung uginn, ass den Dateityp <a %(jsonlines)s>JSON Lines</a> kompriméiert mat <a %(zstd)s>Zstandard</a>. <code>metadata</code> ass arbiträr Metadata, no de Semantiken vun der Kollektioun. Et muss semantesch konsequent bannent der Kollektioun sinn. Den <code style="color: red">annas_archive_meta__</code> Präfix kann un den Numm vun Ärer Institutioun ugepasst ginn, z.B. <code style="color: red">my_institute_meta__</code>. <strong>“records” an “files” Kollektiounen.</strong> No Konventioun ass et dacks praktesch, “records” an “files” als verschidde Kollektiounen erauszeginn, sou datt se zu verschiddenen Zäiten erausgi kënne ginn, z.B. baséiert op Scraping-Raten. E “record” ass eng Kollektioun nëmme mat Metadata, déi Informatiounen wéi Buchtitelen, Auteuren, ISBNs, etc. enthält, während “files” d'Kollektiounen sinn, déi déi tatsächlech Dateien selwer enthalen (pdf, epub). Schlussendlech hu mir eis op e relativ einfachen Standard gëeenegt. Et ass zimlech locker, net-normativ, an e Wierk a Fortschrëtt. <strong>Torrents.</strong> D'Metadata-Dateien an d'binär Daten-Dossieren kënnen an Torrents gebündelt ginn, mat engem Torrent pro Metadata-Datei oder engem Torrent pro binär Daten-Dossier. D'Torrents mussen den originalen Datei-/Dossiernumm plus e <code>.torrent</code> Suffix als Dateinumm hunn. <a %(wikipedia_annas_archive)s>Anna säin Archiv</a> ass bei wäitem déi gréisst Schiedbibliothéik vun der Welt ginn, an déi eenzeg Schiedbibliothéik vun dëser Gréisst déi voll open-source an open-data ass. Hei drënner ass eng Tabell vun eiser Datasets-Säit (liicht modifizéiert): Mir hunn dëst op dräi Weeër erreecht: Spigelung vun existente open-data Schiedbibliothéiken (wéi Sci-Hub an Library Genesis). Ënnerstëtzung vu Schiedbibliothéiken déi méi oppe wëlle sinn, awer net d'Zäit oder d'Ressourcen haten fir dat ze maachen (wéi d'Libgen Comics Sammlung). Scraping vu Bibliothéiken déi net wëllen a Masse deelen (wéi Z-Library). Fir (2) an (3) verwalten mir elo eng bedeitend Sammlung vu Torrents selwer (100er vu TBs). Bis elo hu mir dës Sammlungen als eenzel behandelt, dat heescht speziell Infrastruktur an Datenorganisatioun fir all Sammlung. Dëst füügt bedeitend Iwwerleeung zu all Verëffentlechung bäi, an et mécht et besonnesch schwéier méi inkrementell Verëffentlechungen ze maachen. Dofir hu mir decidéiert eis Verëffentlechungen ze standardiséieren. Dëst ass e technesche Blogpost an deem mir eisen Standard virstellen: <strong>Anna’s Archive Containeren</strong>. Anna seng Archivcontaineren (AAC): Standardiséierung vun de Verëffentlechungen aus der gréisster Schiedbibliothéik vun der Welt Anna säin Archiv ass déi gréisst Schiedbibliothéik vun der Welt ginn, wat eis verlaangt eis Verëffentlechungen ze standardiséieren. 300GB+ vu Buchdeckelen verëffentlecht Endlech si mir frou, eng kleng Verëffentlechung unzekënnegen. An Zesummenaarbecht mat de Leit, déi de Libgen.rs Fork bedreiwen, deele mir all hir Buchdeckelen iwwer Torrents an IPFS. Dëst wäert d'Belaaschtung vum Uweisen vun den Deckelen op méi Maschinnen verdeelen an se besser erhalen. A ville (awer net all) Fäll sinn d'Buchdeckelen an de Dateien selwer abegraff, sou datt dëst eng Aart "ofgeleet Daten" ass. Mee se an IPFS ze hunn ass nach ëmmer ganz nëtzlech fir den alldeeglechen Betrib vun souwuel dem Anna’s Archive wéi och de verschiddene Library Genesis Forken. Wéi gewinnt kënnt Dir dës Verëffentlechung bei der Pirate Library Mirror fannen (EDIT: op <a %(wikipedia_annas_archive)s>Anna’s Archive</a> verluecht). Mir wäerten hei net dorop verlinken, mee Dir kënnt et einfach fannen. Hoffentlech kënne mir eise Tempo e bëssen entspanen, elo wou mir eng uerdentlech Alternativ zu Z-Library hunn. Dës Aarbechtsbelaaschtung ass net besonnesch nohalteg. Wann Dir interesséiert sidd, bei der Programméierung, Serverbetrib oder Erhalungsaarbecht ze hëllefen, kontaktéiert eis definitiv. Et gëtt nach vill <a %(annas_archive)s>Aarbecht ze maachen</a>. Merci fir Ären Interessi an Ënnerstëtzung. Wiesselt op ElasticSearch E puer Ufroen hunn extrem laang gedauert, bis zu dem Punkt, wou se all déi oppe Verbindungen blockéiert hunn. Standardméisseg huet MySQL eng Mindestwuertlängt, oder Ären Index kann wierklech grouss ginn. Leit hu gemellt, datt se net no "Ben Hur" sichen konnten. D'Sich war nëmme relativ séier, wann se komplett am Gediechtnes gelueden war, wat eis verlaangt huet, eng méi deier Maschinn ze kréien, fir dëst ze bedreiwen, plus e puer Kommandoen, fir den Index beim Start virzelueden. Mir hätten et net einfach kënne verlängeren, fir nei Funktiounen ze bauen, wéi besser <a %(wikipedia_cjk_characters)s>Tokeniséierung fir net-wäissraum Sproochen</a>, Filteren/Facettéieren, Sortéieren, "meent Dir" Suggestiounen, Autocomplete, an esou weider. Ee vun eisen <a %(annas_archive)s>Tickets</a> war eng Sammlung vu Problemer mat eisem Sichsystem. Mir hunn MySQL Volltext-Sich benotzt, well mir all eis Daten souwisou an MySQL haten. Mee et hat seng Grenzen: No Gespréicher mat enger Rei vun Experten hu mir eis op ElasticSearch festgeluecht. Et war net perfekt (hir Standard "meent Dir" Suggestiounen an Autocomplete Funktiounen si schlecht), mee am Allgemengen ass et vill besser wéi MySQL fir d'Sich. Mir sinn nach ëmmer net <a %(youtube)s>ze begeeschtert</a> et fir all missionskritesch Daten ze benotzen (obwuel se vill <a %(elastic_co)s>Fortschrëtter</a> gemaach hunn), mee am Allgemengen si mir zimmlech zefridden mam Wiessel. Fir de Moment hu mir vill méi séier Sich ëmgesat, besser Sproochënnerstëtzung, besser Relevanz-Sortéierung, verschidde Sortéierungsoptiounen, a Filteren op Sprooch/Buchtyp/Dateityp. Wann Dir virwëtzeg sidd, wéi et funktionéiert, <a %(annas_archive_l140)s>kuckt</a> <a %(annas_archive_l1115)s>e</a> <a %(annas_archive_l1635)s>Bléck</a> drop. Et ass zimlech zougänglech, obwuel et e puer méi Kommentaren kéint gebrauchen… Annas Archiv ass voll oppe Quell Mir gleewen, datt Informatioun fräi soll sinn, an eise Code ass keng Ausnahm. Mir hunn all eise Code op eiser privat gehosteter Gitlab Instanz verëffentlecht: <a %(annas_archive)s>Annas Software</a>. Mir benotzen och den Issue Tracker fir eis Aarbecht ze organiséieren. Wann Dir un eiser Entwécklung deelhuele wëllt, ass dëst eng super Plaz fir unzefänken. Fir Iech e Goût vun de Saachen ze ginn, un deene mir schaffen, kuckt eis rezent Aarbecht un der Verbesserung vun der Client-Säit Performance. Well mir nach keng Paginatioun implementéiert hunn, géife mir dacks ganz laang Sichsäiten zréckginn, mat 100-200 Resultater. Mir wollten d'Sichresultater net ze fréi ofschneiden, awer dëst huet bedeit, datt et e puer Apparater verlangsamt huet. Dofir hu mir e klenge Trick implementéiert: mir hunn déi meescht Sichresultater an HTML Kommentaren agewéckelt (<code><!-- --></code>), an dann e bësse Javascript geschriwwen, dat erkennt, wann e Resultat sichtbar soll ginn, an deem Moment géife mir de Kommentar auswickelen: DOM-"Virtualiséierung" an 23 Zeilen ëmgesat, keng fancy Bibliothéiken néideg! Dëst ass déi Aart vu séier pragmateschem Code, déi Dir kritt, wann Dir limitéiert Zäit hutt an reell Problemer, déi geléist musse ginn. Et gouf gemellt, datt eis Sich elo gutt op luesen Apparater funktionéiert! En aneren grousse Effort war d'Automatiséierung vum Opbau vun der Datebank. Wéi mir gestart sinn, hu mir einfach verschidde Quelle zesummegezunn. Elo wëlle mir se aktualiséiert halen, dofir hu mir eng Rei Skripter geschriwwen, fir nei Metadata vun deenen zwee Library Genesis Forken erofzelueden an ze integréieren. D'Zil ass net nëmmen dëst nëtzlech fir eisen Archiv ze maachen, mee et och einfach ze maachen fir jiddereen, deen sech mat Schattenbibliothéik-Metadata beschäftege wëll. D'Zil wier e Jupyter Notizbuch, dat all Zorte vu interessante Metadata verfügbar huet, sou datt mir méi Fuerschung maache kënnen, wéi erauszefannen, wéi <a %(blog)s>vill Prozent vun den ISBNs fir ëmmer erhalen</a> sinn. Endlech hu mir eist Donatiounssystem iwwerschafft. Dir kënnt elo eng Kreditkaart benotzen, fir direkt Suen op eis Krypto-Wallets ze deposéieren, ouni wierklech eppes iwwer Kryptowärungen wëssen ze mussen. Mir wäerten weider iwwerwaachen, wéi gutt dëst an der Praxis funktionéiert, mee dëst ass e grousse Schrëtt. Mat Z-Library, déi erofgeet an hir (angeblech) Grënner verhaft ginn, hu mir ronderëm d'Auer geschafft, fir eng gutt Alternativ mat Annas Archiv ze bidden (mir wäerten et hei net verlinken, awer Dir kënnt et googlen). Hei sinn e puer vun de Saachen, déi mir rezent erreecht hunn. Anna’s Update: voll oppe Quellarchiv, ElasticSearch, 300GB+ vu Buchdeckelen Mir hunn ronderëm d'Auer geschafft, fir eng gutt Alternativ mat Annas Archiv ze bidden. Hei sinn e puer vun de Saachen, déi mir rezent erreecht hunn. Analys Semantesch Duplikater (verschidde Scans vum selwechte Buch) kënnen theoretesch erausgefiltert ginn, awer et ass komplizéiert. Beim manuelle Duerchkucken vun de Comics hu mir ze vill falsch Positiver fonnt. Et ginn e puer Duplikater reng duerch MD5, wat relativ verschwenderesch ass, awer dës erauszehuelen géif eis nëmmen ongeféier 1% in Spueren ginn. Op dëser Skala ass dat nach ëmmer ongeféier 1TB, awer och, op dëser Skala ass 1TB net wierklech wichteg. Mir wëllen net riskéieren, zoufälleg Donnéeën an dësem Prozess ze zerstéieren. Mir hunn eng Rei vun net-Buch Daten fonnt, wéi Filmer baséiert op Comic Bicher. Dat schéngt och verschwenderesch, well dës scho wäit duerch aner Mëttelen verfügbar sinn. Allerdéngs hu mir realiséiert, datt mir net einfach Filmdateien erausfiltere konnten, well et och <em>interaktiv Comic Bicher</em> ginn, déi um Computer verëffentlecht goufen, déi een opgeholl an als Filmer gespäichert huet. Schlussendlech, alles wat mir aus der Kollektioun kéinte läschen, géif nëmmen e puer Prozent spueren. Dunn hu mir eis erënnert, datt mir Date-Sammler sinn, an d'Leit, déi dat spigelen, sinn och Date-Sammler, an dofir, "WAT MENGT DIR, LÄSCHEN?!" :) Wann Dir 95TB an Äre Späichercluster gespäichert kritt, probéiert Dir ze verstoen, wat iwwerhaapt dran ass… Mir hunn e puer Analysen gemaach, fir ze kucken, ob mir d'Gréisst e bëssen reduzéiere kéinten, wéi zum Beispill duerch d'Entfernung vu Duplikater. Hei sinn e puer vun eisen Erkenntnisser: Mir presentéieren Iech dofir déi voll, onverännert Kollektioun. Et ass vill Daten, mee mir hoffen, datt genuch Leit sech drëm këmmeren, et trotzdem ze séien. Zesummenaarbecht Wéinst senger Gréisst war dës Sammlung laang op eiser Wonschlëscht, also no eisem Erfolleg mam Backup vun Z-Library, hu mir eis op dës Sammlung konzentréiert. Ufanks hu mir se direkt ofgeschrauft, wat eng Erausfuerderung war, well hire Server net an de beschte Konditioune war. Mir hunn op dës Manéier ongeféier 15TB kritt, awer et war lues. Glécklecherweis hu mir et fäerdeg bruecht, mam Operateur vun der Bibliothéik a Kontakt ze kommen, deen zougestëmmt huet, eis all d'Donnéeën direkt ze schécken, wat vill méi séier war. Et huet nach ëmmer méi wéi en halleft Joer gedauert fir all d'Donnéeën ze transferéieren an ze verschaffen, an mir hunn bal alles duerch Festplattkorruptioun verluer, wat bedeit hätt, vun Ufank unzefänken. Dës Erfarung huet eis gleewen gelooss, datt et wichteg ass, dës Donnéeën esou séier wéi méiglech erauszekréien, sou datt se wäit a breet gespigelt kënne ginn. Mir sinn nëmmen een oder zwee onglécklech getimten Tëschefäll ewech dovun, dës Sammlung fir ëmmer ze verléieren! D'Sammlung Séier ze beweegen bedeit, datt d'Sammlung e bëssen onorganiséiert ass… Loosst eis e Bléck drop werfen. Stellt Iech vir, mir hunn e Dateisystem (deen mir an der Realitéit iwwer Torrents opdeelen): Den éischten Dossier, <code>/repository</code>, ass den méi strukturéierten Deel dovun. Dësen Dossier enthält sougenannte "dausend Dirs": Dossieren, jidderee mat dausend Dateien, déi inkrementell an der Datebank nummeréiert sinn. Dossier <code>0</code> enthält Dateien mat comic_id 0–999, an esou weider. Dëst ass datselwecht Schema, dat Library Genesis fir seng Fiktioun- an Net-Fiktiounssammlungen benotzt huet. D'Iddi ass, datt all "dausend Dir" automatesch an en Torrent ëmgewandelt gëtt, soubal et gefëllt ass. Allerdéngs huet den Libgen.li Operateur ni Torrents fir dës Sammlung gemaach, an dofir sinn d'Dausend Dirs wahrscheinlech onpraktesch ginn, an hunn Plaz fir "onsortéiert Dirs" gemaach. Dës sinn <code>/comics0</code> bis <code>/comics4</code>. Si all enthalen eenzegaarteg Dossierstrukturen, déi wahrscheinlech Sënn gemaach hunn fir d'Dateien ze sammelen, awer maachen elo net vill Sënn fir eis. Glécklecherweis bezitt d'Metadata nach ëmmer direkt op all dës Dateien, sou datt hir Lagerorganisatioun op der Festplack eigentlech net wichteg ass! D'Metadata ass an der Form vun enger MySQL Datebank verfügbar. Dëst kann direkt vun der Libgen.li Websäit erofgeluede ginn, awer mir maachen et och an engem Torrent verfügbar, nieft eiser eegener Tabell mat all den MD5 Hashen. <q>Dr. Barbara Gordon probéiert sech an der alldeeglecher Welt vun der Bibliothéik ze verléieren…</q> Libgen Forken Éischtens, e bësse Hannergrond. Dir kennt vläicht Library Genesis fir hir epesch Bicherkollektioun. Manner Leit wëssen, datt d'Library Genesis Fräiwëlleger aner Projeten erstallt hunn, wéi eng substantiell Sammlung vu Magaziner an Normdokumenter, e komplette Backup vu Sci-Hub (a Kollaboratioun mam Grënner vu Sci-Hub, Alexandra Elbakyan), an tatsächlech eng massiiv Comicsammlung. Zu engem gewëssen Zäitpunkt sinn verschidden Operateure vu Library Genesis Spigelen hir eegen Weeër gaangen, wat zur aktueller Situatioun gefouert huet, wou et eng Rei vu verschiddene "Forken" gëtt, déi all nach ëmmer den Numm Library Genesis droen. De Libgen.li Fork huet eenzegaarteg dës Comicsammlung, souwéi eng substantiell Magazinsammlung (un där mir och schaffen). Spendenaktioun Mir verëffentlechen dës Daten a grousse Stécker. Den éischten Torrent ass vun <code>/comics0</code>, deen mir an eng rieseg 12TB .tar-Datei gesat hunn. Dat ass besser fir Äre Festplack an Torrent-Software wéi eng Onmass vu klenge Fichieren. Als Deel vun dëser Verëffentlechung maache mir eng Spendenaktioun. Mir sichen $20,000 ze sammelen, fir d'Betribs- an d'Kontraktkäschte fir dës Kollektioun ze decken, souwéi lafend an zukünfteg Projeten z'erméiglechen. Mir hunn e puer <em>massiv</em> an der Maach. <em>Wien ënnerstëtzen ech mat menger Spende?</em> Kuerz gesot: mir sichen all Wëssen a Kultur vun der Mënschheet ze sécheren an et einfach zougänglech ze maachen. All eis Code an Daten sinn Open Source, mir sinn e komplett fräiwëllege Projet, a mir hunn bis elo 125TB u Bicher geséchert (zousätzlech zu de bestehende Libgen an Scihub Torrents). Schlussendlech bauen mir e Schwongrad, dat d'Leit erméiglecht an ureegt, all d'Bicher op der Welt ze fannen, ze scannen an ze sécheren. Mir schreiwen iwwer eise Masterplang an engem zukünftege Post. :) Wann Dir fir eng 12 Méint "Amazing Archivist" Memberschaft ($780) spendt, kënnt Dir <strong>“e Torrent adoptéieren”</strong>, dat heescht, datt mir Äre Benotzernumm oder Message am Dateinumm vun engem vun den Torrents setzen! Dir kënnt spenden andeems Dir op <a %(wikipedia_annas_archive)s>Annas Archiv</a> gitt an op de "Spenden" Knäppchen klickt. Mir sichen och no méi Fräiwëllegen: Software Ingenieuren, Sécherheetsfuerscher, anonymen Händlerexperten an Iwwersetzer. Dir kënnt eis och ënnerstëtzen andeems Dir Hosting Servicer ubitt. An natierlech, séit w.e.g. eis Torrents! Merci un all déi, déi eis scho sou generéis ënnerstëtzt hunn! Dir maacht wierklech en Ënnerscheed. Hei sinn d'Torrents, déi bis elo verëffentlecht goufen (mir veraarbechten nach de Rescht): All Torrents fannt Dir op <a %(wikipedia_annas_archive)s>Annas Archiv</a> ënner "Datasets" (mir verlinken net direkt dohin, sou datt Links op dëse Blog net vun Reddit, Twitter, etc. ewechgeholl ginn). Vun do aus, verfollegt de Link op d'Tor Websäit. <a %(news_ycombinator)s>Diskutéiert op Hacker News</a> Wat kënnt als nächst? Eng Rei vu Torrents si super fir laangfristeg Erhaalung, awer net sou vill fir den alldeeglechen Zougang. Mir wäerten mat Hosting-Partner zesummeschaffen, fir all dës Daten op d'Web ze kréien (well Annas Archiv näischt direkt host). Natierlech fannt Dir dës Download Links op Annas Archiv. Mir invitéieren och jiddereen, eppes mat dësen Daten ze maachen! Hëlleft eis se besser z'analyséieren, ze deduplizéieren, se op IPFS ze setzen, se ze remixen, Är AI Modeller domat ze trainéieren, an esou weider. Et ass alles Äert, a mir kënnen net waarden, ze gesinn, wat Dir domat maacht. Schlussendlech, wéi virdru gesot, hu mir nach e puer massiv Verëffentlechungen, déi kommen (wann <em>iemand</em> eis zoufälleg en Dump vun enger <em>bestëmmter</em> ACS4 Datebank schécke kéint, wësst Dir, wou Dir eis fannt...), souwéi de Schwongrad ze bauen, fir all d'Bicher op der Welt ze sécheren. Also bleift drun, mir fänken eréischt un. - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Déi gréisst Comics-Schattenbibliothéik ass wahrscheinlech déi vun engem bestëmmte Library Genesis Fork: Libgen.li. Den eenzege Administrator, deen dës Säit bedreiwt, huet et fäerdeg bruecht, eng enorm Comics-Kollektioun vu méi wéi 2 Milliounen Dateien ze sammelen, déi iwwer 95TB ausmaachen. Allerdéngs, am Géigesaz zu anere Library Genesis Kollektiounen, war dës net a Masse iwwer Torrents verfügbar. Dir konnt dës Comics nëmmen eenzel iwwer säi luesen perséinleche Server zougräifen — e eenzege Feelerpunkt. Bis haut! An dësem Post erzielen mir Iech méi iwwer dës Sammlung, an iwwer eis Spendekampagne fir méi vun dëser Aarbecht z'ënnerstëtzen. Den Anna’s Archive huet déi weltgréisst Comics-Schattenbibliothéik geséchert (95TB) — Dir kënnt hëllefen, se ze séieren Déi gréisst Comics-Schattenbibliothéik op der Welt hat e eenzege Feelerpunkt.. bis haut. Warnung: dëse Blogpost gouf ofgeschaf. Mir hunn decidéiert, datt IPFS nach net prett ass fir d’Primetime. Mir wäerten nach ëmmer op Dateien op IPFS vun Annas Archiv verlinken wann méiglech, awer mir wäerten et net méi selwer hosten, nach empfeelen mir aneren et mat IPFS ze spigelen. Kuckt w.e.g. eis Torrents Säit wann Dir hëllefe wëllt eis Sammlung ze erhalen. 5,998,794 Bicher op IPFS setzen Eng Vervielfältigung vu Kopien Zréck zu eiser ursprénglecher Fro: wéi kënne mir behaapten, eis Sammlungen fir ëmmer ze erhalen? De gréisste Problem hei ass, datt eis Sammlung <a %(torrents_stats)s>rapid gewuess</a> ass, duerch Scraping an Open-Sourcing vu massiven Sammlungen (zousätzlech zu der fantastescher Aarbecht, déi schonn duerch aner Open-Data-Schiedbibliothéiken wéi Sci-Hub a Library Genesis gemaach gouf). Dëse Wuesstem un Daten mécht et méi schwéier, d’Sammlungen weltwäit ze spigelen. Datenspeicherung ass deier! Mee mir sinn optimistesch, besonnesch wann mir déi folgend dräi Trends beobachten. D'<a %(annas_archive_stats)s>Gesamtgréisst</a> vun eise Sammlungen, iwwer déi lescht Méint, opgeschlësselt no der Unzuel vun Torrent Seeders. HDD-Präistrends aus verschiddene Quellen (klickt fir d’Studie ze gesinn). <a %(critical_window_chinese)s>Chinesesch Versioun 中文版</a>, diskutéiert op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Mir hunn déi niddreg hänkend Friichten gepfléckt Dëst follegt direkt aus eise Prioritéiten, déi hei uewen diskutéiert goufen. Mir preferéieren, un der Befreiung vu grousse Sammlungen als éischt ze schaffen. Elo, wou mir e puer vun de gréisste Sammlungen op der Welt geséchert hunn, erwaarden mir, datt eise Wuesstem vill méi lues wäert sinn. Et gëtt nach ëmmer e laangen Schwanz vu méi klenge Sammlungen, an nei Bicher ginn all Dag gescannt oder publizéiert, mee d’Rate wäert warscheinlech vill méi lues sinn. Mir kéinten nach ëmmer duebel oder souguer dräifach an der Gréisst wuessen, mee iwwer eng méi laang Zäitperiod. Verbesserungen am OCR. Prioritéiten Wëssenschafts- a Ingenieurssoftwarecode Fiktiounal oder Ënnerhalungsversiounen vun all deenen hei uewen Geographesch Daten (z.B. Kaarten, geologesch Ënnersichungen) Intern Daten vu Firmen oder Regierungen (Lecks) Moossdaten wéi wëssenschaftlech Moossungen, wirtschaftlech Daten, Geschäftsberichter Metadata records allgemeng (vu Net-fiktioun a Fiktioun; vun anere Medien, Konscht, Leit, asw.; inklusiv Rezensiounen) Non-Fiktioun Bicher Net-fiktional Zäitschrëften, Zeitungen, Handbücher Net-fiktional Transkriptioune vu Rieden, Dokumentarfilmer, Podcasts Organesch Daten wéi DNA-Sequenzen, Planzesamen oder mikrobial Proben Akademesch Pabeieren, Zäitschrëften, Berichter Wëssenschafts- a Ingenieurswebsäiten, Online-Diskussiounen Transkriptioune vu legale oder Geriichtsprozeduren Eenzegaarteg a Gefor vun Zerstéierung (z.B. duerch Krich, Budgetskierzungen, Prozesser oder politesch Verfolgung) Rar Eenzegaarteg ënnerfokusséiert Firwat këmmere mir eis sou vill ëm Pabeieren a Bicher? Loosst eis eis fundamental Iwwerzeegung vun der Erhaalung am Allgemengen op d'Säit leeën — mir kéinten en anere Post doriwwer schreiwen. Also firwat speziell Pabeieren a Bicher? D'Äntwert ass einfach: <strong>Informatiounsdicht</strong>. Pro Megabyte Späicherplatz späichert geschriwwenen Text déi meeschten Informatioun vun allen Medien. Wärend mir eis ëm béid, Wëssen a Kultur, këmmeren, këmmere mir eis méi ëm dat éischt. Am Allgemengen fannen mir eng Hierarchie vun Informatiounsdicht an d'Wichtegkeet vun der Erhaalung, déi ongeféier esou ausgesäit: D'Ranglëscht an dëser Lëscht ass e bëssen arbiträr — e puer Elementer sinn gläich oder et ginn Onstëmmegkeeten an eisem Team — a mir vergiessen wahrscheinlech e puer wichteg Kategorien. Awer dat ass ongeféier wéi mir prioritär setzen. E puer vun dësen Elementer sinn ze anescht wéi déi aner fir eis eis Suergen ze maachen (oder si gi scho vun aneren Institutiounen betreit), wéi organesch Daten oder geographesch Daten. Awer déi meescht vun den Elementer an dëser Lëscht si wierklech wichteg fir eis. En anere grousse Faktor an eiser Prioriséierung ass wéi vill e gewësst Wierk a Gefor ass. Mir preferéieren eis op Wierker ze konzentréieren déi: Schlussendlech këmmere mir eis ëm d'Gréisst. Mir hunn limitéiert Zäit a Suen, also géife mir léiwer e Mount verbréngen fir 10.000 Bicher ze retten wéi 1.000 Bicher — wann se ongeféier gläichwäerteg a Gefor sinn. <em><q>Dat Verlueren kann net erëmfonnt ginn; mee loosst eis retten, wat bleift: net duerch Tresoren a Schlässer, déi se vum ëffentlechen Aen a Gebrauch ofschiermen, andeems se se der Vergeblechkeet vun der Zäit iwwerginn, mee duerch sou eng Vervielfältigung vu Kopien, déi se iwwer d'Reechwäit vum Zoufall eraus placéiert.</q></em><br>— Thomas Jefferson, 1791 Schiedsbibliothéiken Code kann op Github open source sinn, mee Github als Ganzt kann net einfach gespigelt a soumat erhalen ginn (obwuel an dësem spezifesche Fall genuch verdeelt Kopien vun de meeschte Code-Repositoiren existéieren) Metadata-Rekorder kënnen op der Worldcat-Websäit fräi gekuckt ginn, mee net a Masse erofgeluede ginn (bis mir se <a %(worldcat_scrape)s>ausgeschraapt</a> hunn) Reddit ass gratis ze benotzen, mee huet viru kuerzem streng Anti-Scraping-Moossnamen agefouert, am Nofolleg vun datenhongeregen LLM-Trainingen (méi doriwwer méi spéit) Et gi vill Organisatiounen déi ähnlech Missiounen an ähnlech Prioritéiten hunn. Tatsächlech ginn et Bibliothéiken, Archiven, Laboratoiren, Muséeën an aner Institutiounen déi mat der Erhaalung vun dëser Aart beoptragt sinn. Vill vun dësen sinn gutt finanzéiert, vu Regierungen, Eenzelpersounen oder Firmen. Awer si hunn ee massiven blanne Fleck: d'Gesetzessystem. Hei läit d'eenzegaarteg Roll vu Schiedsbibliothéiken, an de Grond firwat Annas Archiv existéiert. Mir kënnen Saache maachen déi aner Institutiounen net dierfen. Elo, et ass net (oft) datt mir Material archivéieren kënnen dat soss illegal ze erhalen ass. Nee, et ass a ville Plazen legal en Archiv mat all Bicher, Pabeieren, Zäitschrëften, asw. opzebauen. Awer wat juristesch Archiver dacks feelen, ass <strong>Redundanz a Liewensdauer</strong>. Et ginn Bicher, vun deenen et nëmmen eng eenzeg Kopie an enger physescher Bibliothéik iergendwou gëtt. Et ginn Metadata-Rekorder, déi vun enger eenzeger Firma geschützt ginn. Et ginn Zeitungen, déi nëmmen op Mikrofilm an engem eenzegen Archiv erhalen sinn. Bibliothéiken kënnen Finanzéierungen verléieren, Firmen kënnen faillite goen, Archiver kënnen bombardéiert a verbrannt ginn. Dëst ass net hypothetesch – dat geschitt dacks. Wat mir eenzegaarteg bei Annas Archiv maache kënnen, ass vill Kopien vu Wierker ze späicheren, a groussem Ëmfang. Mir kënnen Pabeieren, Bicher, Zäitschrëften a méi sammelen a se a Masse verdeelen. Mir maachen dat aktuell duerch Torrents, mee déi exakt Technologien si net wichteg a wäerten sech mat der Zäit änneren. De wichtegen Deel ass, vill Kopien weltwäit ze verdeelen. Dëse Zitat vun iwwer 200 Joer ass nach ëmmer richteg: Eng séier Notiz iwwer den ëffentlechen Domaine. Well Annas Archiv eenzegaarteg op Aktivitéiten fokusséiert, déi a ville Plazen op der Welt illegal sinn, këmmeren mir eis net ëm wäit verfügbar Sammlungen, wéi Bicher am ëffentlechen Domaine. Juristesch Entitéite këmmeren sech dacks schonn gutt dorëms. Allerdéngs ginn et Iwwerleeungen, déi eis heiansdo un ëffentlech verfügbare Sammlungen schaffen loossen: - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Späicherkäschte falen weider exponentiell 3. Verbesserungen an der Informatiounsdicht Mir späicheren aktuell Bicher an de roude Formater, wéi se eis ginn. Sécher, si sinn kompriméiert, mee dacks sinn et nach ëmmer grouss Scans oder Fotoe vu Säiten. Bis elo waren déi eenzeg Optiounen, fir d'Gesamtgréisst vun eiser Sammlung ze reduzéieren, méi aggressiv Kompressioun oder Deduplizéierung. Allerdéngs sinn béid ze verlustreich fir eis Goût, fir genuch Erspuernisser ze kréien. Schwéier Kompressioun vu Fotoe kann den Text kaum liesbar maachen. An Deduplizéierung erfuerdert héich Vertrauen, datt d'Bicher genee d'selwecht sinn, wat dacks ze ongenau ass, besonnesch wann den Inhalt d'selwecht ass, awer d'Scans zu verschiddenen Zäiten gemaach goufen. Et gouf ëmmer eng drëtt Optioun, mee hir Qualitéit war sou schlecht, datt mir se ni berücksichtegt hunn: <strong>OCR, oder Optesch Zeechenerkennung</strong>. Dëst ass de Prozess, Fotoen an einfachen Text ëmzewandelen, andeems AI benotzt gëtt, fir d'Zeechen an de Fotoen z'erkennen. Tools dofir existéieren zënter laangem, an si waren zimlech gutt, mee "zimlech gutt" ass net genuch fir Erhalungszwecker. Allerdéngs hunn rezent multi-modal déif-Léiermodeller extrem séier Fortschrëtter gemaach, obwuel nach ëmmer zu héije Käschten. Mir erwaarden, datt souwuel d'Genauegkeet wéi och d'Käschten an de kommende Jore dramatesch verbesseren, bis zu engem Punkt, wou et realistesch gëtt, et op eis ganz Bibliothéik unzewenden. Wann dat geschitt, wäerte mir wahrscheinlech nach ëmmer déi original Dateien erhalen, mee zousätzlech kéinte mir eng vill méi kleng Versioun vun eiser Bibliothéik hunn, déi déi meescht Leit wëllen spigelen. De Clou ass, datt rouden Text selwer nach besser kompriméiert, an et ass vill méi einfach ze deduplizéieren, wat eis nach méi Erspuernisser gëtt. Insgesamt ass et net onrealistesch, op d'mannst eng 5-10x Reduktioun vun der Gesamtdateigréisst ze erwaarden, vläicht nach méi. Och mat enger konservativer 5x Reduktioun, géife mir op <strong>$1,000–$3,000 an 10 Joer kucken, och wann eis Bibliothéik dräimol sou grouss gëtt</strong>. Zum Zäitpunkt vum Schreiwen, <a %(diskprices)s>Diskpräisser</a> pro TB leien bei ronn $12 fir nei Disken, $8 fir gebrauchte Disken, an $4 fir Band. Wann mir konservativ sinn a just op nei Disken kucken, heescht dat, datt d’Späichere vun engem Petabyte ronn $12,000 kascht. Wann mir unhuelen, datt eis Bibliothéik vun 900TB op 2.7PB dräifach wäert wuessen, géif dat $32,400 bedeiten, fir eis ganz Bibliothéik ze spigelen. Mat Elektrizitéit, Käschte vun anerer Hardware, asw., ronne mir et op $40,000 op. Oder mat Band méi wéi $15,000–$20,000. Op der enger Säit <strong>$15,000–$40,000 fir d’Summe vun all mënschlechem Wëssen ass e Schnäppchen</strong>. Op der anerer Säit ass et e bëssen héich, fir vill voll Kopien ze erwaarden, besonnesch wann mir och gären hätten, datt déi Leit hir Torrents weider fir d’Wuel vun aneren seed. Dat ass haut. Mee de Fortschrëtt geet virun: Festplattpräisser pro TB goufen ongeféier an en Drëttel iwwer déi lescht 10 Joer geschnidden, a wäerten warscheinlech weider an engem ähnlechen Tempo falen. Band schéngt op enger ähnlecher Trajectoire ze sinn. SSD-Präisser falen nach méi séier, a kéinten d’HDD-Präisser bis Enn vum Joerzéngt iwwerhuelen. Wann dat hält, da kéinte mir an 10 Joer nëmmen $5,000–$13,000 brauchen, fir eis ganz Sammlung ze spigelen (1/3), oder souguer manner, wann mir manner an der Gréisst wuessen. Och wann et nach ëmmer vill Suen ass, wäert dat fir vill Leit erreechbar sinn. An et kéint nach besser sinn wéinst dem nächste Punkt… Am Annas Archiv gi mir dacks gefrot, wéi mir behaapte kënnen, eis Sammlungen fir ëmmer ze erhalen, wann d'Gesamtgréisst schonn 1 Petabyte (1000 TB) erreecht, an nach ëmmer wiisst. An dësem Artikel kucke mir eis Philosophie un, a gesinn, firwat dat nächst Joerzéngt kritesch fir eis Missioun ass, d'Wëssen an d'Kultur vun der Mënschheet ze erhalen. Kritesch Fënster Wann dës Prognosen korrekt sinn, mussen mir <strong>just e puer Joer waarden</strong>, bis eis ganz Sammlung wäit verbreet gespigelt gëtt. Sou, an de Wierder vum Thomas Jefferson, "ausserhalb vun der Reechwäit vum Zoufall gesat." Leider huet d'Entstoe vun LLMs, an hir datenhongereg Ausbildung, vill Copyright-Besëtzer an d'Verdeedegung gedréckt. Nach méi wéi se scho waren. Vill Websäite maachen et méi schwéier ze scrape an ze archivéieren, Prozesser fléien ronderëm, an all d'Zäit ginn physesch Bibliothéiken an Archive weider vernoléissegt. Mir kënnen nëmmen erwaarden, datt dës Trends weider verschlechteren, an datt vill Wierker verluer ginn, laang ier se an den ëffentlechen Domaine kommen. <strong>Mir stinn um Virabend vun enger Revolutioun an der Erhaalung, mee <q>dat Verlueren kann net erëmfonnt ginn.</q></strong> Mir hunn eng kritesch Fënster vun ongeféier 5-10 Joer, an där et nach zimlech deier ass, eng Schiedsbibliothéik ze bedreiwen an vill Spigelen ronderëm d'Welt ze schafen, an an där den Zougang nach net komplett zougemaach gouf. Wann mir dës Fënster iwwerbrécken kënnen, da wäerte mir tatsächlech d'Wëssen an d'Kultur vun der Mënschheet fir ëmmer erhalen hunn. Mir sollten dës Zäit net verschwenden. Mir sollten net erlaben, datt dës kritesch Fënster sech fir eis zoumécht. Loosst eis goen. D'kritesch Fënster vun de Schiedbibliothéiken Wéi kënne mir behaapten, eis Sammlungen fir ëmmer ze erhalen, wann se schonn 1 PB erreechen? Sammlung E puer méi Informatiounen iwwer d’Sammlung. <a %(duxiu)s>Duxiu</a> ass eng riseg Datebank vu gescannten Bicher, erstallt vun der <a %(chaoxing)s>SuperStar Digital Library Group</a>. Déi meescht si akademesch Bicher, gescannt fir se digital fir Universitéiten a Bibliothéiken verfügbar ze maachen. Fir eis engleschsproocheg Publikum hunn <a %(library_princeton)s>Princeton</a> an d’<a %(guides_lib_uw)s>University of Washington</a> gutt Iwwersiichten. Et gëtt och en exzellente Artikel, deen méi Hannergrond gëtt: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (kuckt et an Annas Archiv no). D’Bicher vun Duxiu goufen laang um chineseschen Internet piratéiert. Normalerweis gi se fir manner wéi en Dollar vun Händler verkaaft. Se ginn typesch iwwer den chineseschen Equivalent vu Google Drive verdeelt, deen dacks gehackt gouf fir méi Späicherplatz ze erlaben. E puer technesch Detailer fannt Dir <a %(github_duty_machine)s>hei</a> an <a %(github_821_github_io)s>hei</a>. Och wann d’Bicher semi-ëffentlech verdeelt goufen, ass et zimlech schwéier se am Bulk ze kréien. Mir haten dat héich op eiser TODO-Lëscht, a mir hunn e puer Méint vollzäiteg Aarbecht dofir zougewise. Allerdéngs huet sech viru kuerzem en onheemlechen, erstaunlechen a talentéierte Fräiwëllegen bei eis gemellt, deen eis gesot huet, datt hien all dës Aarbecht schonn gemaach huet — zu grousse Käschten. Hien huet d’ganz Sammlung mat eis gedeelt, ouni eppes dofir ze erwaarden, ausser der Garantie vun laangfristeger Erhaalung. Wierklech bemierkenswäert. Hien huet zougestëmmt op dës Manéier Hëllef ze froen fir d’Sammlung OCR ze maachen. D’Sammlung besteet aus 7.543.702 Dateien. Dat ass méi wéi Library Genesis Non-Fiktioun (ongeféier 5,3 Milliounen). D’Gesamtdateigréisst ass ongeféier 359TB (326TiB) an hirer aktueller Form. Mir si fir aner Proposen an Iddien op. Kontaktéiert eis einfach. Kuckt Annas Archiv fir méi Informatiounen iwwer eis Sammlungen, Erhalungsbeméiungen, a wéi Dir hëllefe kënnt. Merci! Beispill Säiten Fir eis ze beweisen, datt Dir eng gutt Pipeline hutt, hei sinn e puer Beispill Säiten fir unzefänken, aus engem Buch iwwer Supraleiter. Är Pipeline soll Mathematik, Tabellen, Diagrammer, Foussnouten, asw. richteg behandelen. Schéckt Är veraarbecht Säiten un eis E-Mail. Wann se gutt ausgesinn, schécken mir Iech méi privat, a mir erwaarden, datt Dir Är Pipeline och séier op déi lafe kënnt. Wann mir zefridden sinn, kënne mir en Deal maachen. - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Chinesesch Versioun 中文版</a>, <a %(news_ycombinator)s>Diskutéiert op Hacker News</a> Dëst ass e kuerze Blogpost. Mir sichen eng Firma oder Institutioun, déi eis mat OCR an Textextraktioun fir eng massiiv Sammlung, déi mir erworben hunn, hëllefe kann, am Austausch fir exklusiven fréien Zougang. No der Embargoperiod wäerte mir natierlech déi ganz Sammlung verëffentlechen. Héichwäerteg akademesch Texter si ganz nëtzlech fir d'Ausbildung vun LLMs. Och wann eis Sammlung chinesesch ass, sollt dat och nëtzlech sinn fir eng Ausbildung vun engleschen LLMs: Modeller schéngen Konzepter a Wëssen ze encodéieren, onofhängeg vun der Quellsprooch. Dofir muss den Text aus de Scans extrahéiert ginn. Wat kritt Annas Archiv dofir? Volltext-Sich vun de Bicher fir seng Benotzer. Well eis Ziler mat deenen vun LLM-Entwéckler iwwereneestëmmen, sichen mir no engem Kollaborateur. Mir si bereet Iech <strong>exklusiven fréizäitegen Zougang zu dëser Sammlung am Bulk fir 1 Joer</strong> ze ginn, wann Dir richteg OCR an Textextraktioun maache kënnt. Wann Dir bereet sidd de ganze Code vun Ärer Pipeline mat eis ze deelen, wiere mir bereet d’Sammlung méi laang ze embargéieren. Exklusiven Zougang fir LLM Firmen zu der gréisster Sammlung vu chinesesche Non-Fiktiounsbicher op der Welt <em><strong>TL;DR:</strong> Annas Archiv huet eng eenzegaarteg Sammlung vu 7,5 Milliounen / 350TB chinesesche Non-Fiktiounsbicher erworben — méi grouss wéi Library Genesis. Mir si bereet, enger LLM Firma exklusiven Zougang ze ginn, am Austausch fir héichqualitativ OCR an Textextraktioun.</em> Systemarchitektur Loosst eis soen, datt Dir e puer Firmen fonnt hutt, déi bereet sinn Är Websäit ze hosten ouni Iech ofzeschalten — loosst eis dës "fräiheetsléifend Ubidder" nennen 😄. Dir wäert séier feststellen, datt et zimlech deier ass alles bei hinnen ze hosten, also wëllt Dir vläicht e puer "bëlleg Ubidder" fannen an do d'aktuell Hosting maachen, duerch d'fräiheetsléifend Ubidder proxyen. Wann Dir et richteg maacht, wäerten d'bëlleg Ubidder ni wëssen wat Dir host, an ni Reklamatiounen kréien. Mat all dësen Ubidder gëtt et e Risiko, datt si Iech trotzdem ofschalten, also braucht Dir och Redundanz. Mir brauchen dat op all Niveauen vun eiser Stack. Eng e bësse fräiheetsléifend Firma, déi sech an eng interessant Positioun gesat huet, ass Cloudflare. Si hunn <a %(blog_cloudflare)s>argumentéiert</a>, datt si keen Hosting-Ubidder sinn, mee eng Utility, wéi en ISP. Si sinn dofir net dem DMCA oder anere Takedown-Ufroen ënnerworf, a leeden all Ufroen un Ären eigentlechen Hosting-Ubidder weider. Si sinn sou wäit gaangen, viru Geriicht ze goen fir dës Struktur ze schützen. Mir kënnen si dofir als eng aner Schicht vu Caching a Schutz benotzen. Cloudflare akzeptéiert keng anonym Bezuelungen, also kënne mir nëmmen hiren gratis Plang benotzen. Dëst bedeit, datt mir hir Load Balancing oder Failover Features net benotze kënnen. Mir hunn dofir <a %(annas_archive_l255)s>dëst selwer implementéiert</a> op der Domain-Niveau. Beim Säitelueden kontrolléiert de Browser ob d'aktuell Domain nach verfügbar ass, an wann net, schreift et all URLen op eng aner Domain ëm. Well Cloudflare vill Säiten cachet, bedeit dat, datt e Benotzer op eiser Haaptdomain landen kann, och wann de Proxy-Server erof ass, an dann beim nächste Klick op eng aner Domain weidergeleet gëtt. Mir hunn och nach normal operationell Suergen, wéi d'Iwwerwaachung vun der Servergesondheet, Logging vu Backend- a Frontend-Feeler, an esou weider. Eis Failover-Architektur erlaabt méi Robustheet op dëser Front, zum Beispill andeems mir e komplett anere Set vu Serveren op enger vun den Domänen lafen. Mir kënnen souguer méi al Versioune vum Code an Datasets op dëser separater Domain lafen, am Fall wou e kritesche Feeler an der Haaptversioun onbemierkt bleift. Mir kënnen och géint Cloudflare ofgeséchert sinn, andeems mir et vun enger vun den Domänen ewechhuelen, wéi dës separat Domain. Verschidde Permutatiounen vun dësen Iddien sinn méiglech. Konklusioun Et war eng interessant Erfarung, ze léieren, wéi een eng robust a resilient Schiedbibliothéik-Sichmaschinn opstellt. Et ginn nach vill méi Detailer, déi an zukünftege Posts gedeelt ginn, also loosst mech wëssen, wat Dir méi doriwwer wëllt léieren! Wéi ëmmer sichen mir no Donen, fir dës Aarbecht z'ënnerstëtzen, also kuckt w.e.g. op d'Donatiounssäit op Annas Archiv. Mir sichen och no anere Forme vun Ënnerstëtzung, wéi Subventiounen, laangfristeg Sponsoren, héichrisiko Bezuelungsanbieter, vläicht souguer (geschmackvoll!) Annoncen. An wann Dir Är Zäit a Fäegkeeten bäidroe wëllt, sichen mir ëmmer no Entwéckler, Iwwersetzer, asw. Merci fir Ären Interessi an Ënnerstëtzung. Innovatiounstoken Loosst eis mat eiser Tech-Stack ufänken. Si ass bewosst langweileg. Mir benotzen Flask, MariaDB, an ElasticSearch. Dat ass et wuertwiertlech. Sich ass gréisstendeels e geléist Problem, an mir hunn net d'Absicht et nei ze erfannen. Ausserdeem musse mir eis <a %(mcfunley)s>Innovatiounstoken</a> op eppes anescht ausginn: net vun den Autoritéiten ofgeschalt ze ginn. Wéi legal oder illegal ass Anna’s Archive genee? Dat hänkt haaptsächlech vun der legaler Juridictioun of. Déi meescht Länner gleewen un eng Form vu Copyright, wat bedeit, datt Leit oder Firmen e exklusivt Monopol op bestëmmte Wierker fir eng gewëssen Zäit zougewisen kréien. Als Niewebemierkung, bei Anna’s Archive gleewen mir, datt obwuel et e puer Virdeeler gëtt, Copyright am Allgemengen e Netto-Negativ fir d'Gesellschaft ass — mee dat ass eng Geschicht fir eng aner Kéier. Dëst exklusivt Monopol op bestëmmte Wierker bedeit, datt et illegal ass fir jiddereen ausserhalb vun dësem Monopol dës Wierker direkt ze verdeelen — och fir eis. Mee Anna’s Archive ass eng Sichmaschinn déi dës Wierker net direkt verdeelt (op d'mannst net op eiser Clearnet-Websäit), also solle mir okay sinn, richteg? Net ganz. A ville Juridictioune ass et net nëmmen illegal fir Copyright-Wierker ze verdeelen, mee och fir op Plazen ze verlinken déi dat maachen. E klassescht Beispill dofir ass d'amerikanesch DMCA-Gesetz. Dat ass d'strengst Enn vum Spektrum. Um aneren Enn vum Spektrum kéinten theoretesch Länner sinn, déi guer keng Copyright-Gesetzer hunn, mee dës existéieren net wierklech. Bal all Land huet eng Form vu Copyright-Gesetz. Duerchféierung ass eng aner Geschicht. Et gi vill Länner mat Regierungen, déi sech net drëm këmmeren, Copyright-Gesetzer duerchzesetzen. Et gi och Länner tëscht den zwou Extremer, déi d'Verdeelung vu Copyright-Wierker verbidden, mee net d'Verlinken op esou Wierker. Eng aner Iwwerleeung ass op der Firmenebene. Wann eng Firma an enger Juridictioun operéiert, déi sech net ëm Copyright këmmert, mee d'Firma selwer net bereet ass e Risiko anzegoen, da kéinten si Är Websäit zoumaachen, soubal iergendeen sech driwwer beschwéiert. Schlussendlech ass eng grouss Iwwerleeung d'Bezuelungen. Well mir anonym bleiwen mussen, kënne mir keng traditionell Bezuelmethoden benotzen. Dëst léisst eis mat Kryptowärungen, an nëmmen e klenge Set vu Firmen ënnerstëtzt dës (et ginn virtuell Debitkaarten déi mat Krypto bezuelt ginn, mee si ginn dacks net akzeptéiert). - Anna an d’Team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ech bedreiwen <a %(wikipedia_annas_archive)s>Annas Archiv</a>, déi weltgréisst Open-Source Non-Profit Sichmaschinn fir <a %(wikipedia_shadow_library)s>Schiedsbibliothéiken</a>, wéi Sci-Hub, Library Genesis, an Z-Library. Eist Zil ass et, Wëssen a Kultur einfach zougänglech ze maachen, an schliisslech eng Gemeinschaft vu Leit opzebauen, déi zesummen <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>all d’Bicher op der Welt</a> archivéieren an erhalen. An dësem Artikel weisen ech, wéi mir dës Websäit bedreiwen, an déi eenzegaarteg Erausfuerderungen, déi mat der Bedreiwung vun enger Websäit mat fragwürdegem legale Status kommen, well et keen “AWS fir Schiedswelthëllefen” gëtt. <em>Kuckt och de Schwësterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Wéi een e Piratarchivist gëtt</a>.</em> Wéi eng Schiedsbibliothéik ze bedreiwen: Operatiounen bei Annas Archiv Et gëtt keen <q>AWS fir Schiedswelthëllefen,</q> also wéi bedreiwe mir Annas Archiv? Tools Applikatiounsserver: Flask, MariaDB, ElasticSearch, Docker. Entwécklung: Gitlab, Weblate, Zulip. Servermanagement: Ansible, Checkmk, UFW. Onion statesch Hosting: Tor, Nginx. Proxy-Server: Varnish. Loosst eis kucken, wéi eng Tools mir benotzen fir all dëst z'erreechen. Dëst entwéckelt sech staark, wéi mir op nei Problemer stoussen an nei Léisunge fannen. Et ginn e puer Entscheedungen, iwwer déi mir hin an hier gaange sinn. Eng dovun ass d'Kommunikatioun tëscht Serveren: mir hunn virdru Wireguard dofir benotzt, mee hu festgestallt, datt et heiansdo ophält, Daten ze iwwerdroen, oder nëmmen an eng Richtung iwwerdroen. Dëst ass mat verschiddene Wireguard-Opstellungen geschitt, déi mir probéiert hunn, wéi <a %(github_costela_wesher)s>wesher</a> an <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Mir hunn och probéiert, Ports iwwer SSH ze tunnelen, mat autossh an sshuttle, mee si sinn op <a %(github_sshuttle)s>Problemer gestouss</a> (obwuel et nach ëmmer net kloer ass, ob autossh ënner TCP-iwwer-TCP-Problemer leit oder net — et fillt sech einfach wéi eng knaschteg Léisung un, mee vläicht ass et eigentlech gutt?). Stattdesse si mir zréck op direkt Verbindungen tëscht Serveren gaangen, an hunn verstoppt, datt e Server op de bëllege Provideren leeft, andeems mir IP-Filtering mat UFW benotzen. Dëst huet den Nodeel, datt Docker net gutt mat UFW funktionéiert, ausser Dir benotzt <code>network_mode: "host"</code>. All dëst ass e bësse méi fehleranfälleg, well Dir Äre Server mam Internet verbënnt mat nëmmen enger klenger Feelerkonfiguratioun. Vläicht solle mir zréck op autossh goen — Feedback wier hei ganz wëllkomm. Mir hunn och hin an hier gaange mat Varnish vs. Nginx. Mir gefalen eis de Moment Varnish, mee et huet seng Eegenheeten a rau Kanten. Dat selwecht gëllt fir Checkmk: mir si net begeeschtert, mee et funktionéiert fir de Moment. Weblate war okay, mee net onheemlech — ech fäerten heiansdo, datt et meng Daten verléiert, wann ech probéieren, et mat eisem git Repo ze synchroniséieren. Flask war insgesamt gutt, mee et huet e puer komesch Eegenheeten, déi vill Zäit kascht hunn, fir ze debuggen, wéi d'Konfiguratioun vu benutzerdefinéierte Domänen, oder Problemer mat senger SqlAlchemy-Integratioun. Bis elo waren déi aner Tools super: mir hunn keng seriö Reklamatiounen iwwer MariaDB, ElasticSearch, Gitlab, Zulip, Docker, an Tor. All dës haten e puer Problemer, mee näischt iwwerdréit serieux oder Zäitopwänneg. Communautéit Déi éischt Erausfuerderung kéint eng iwwerraschend sinn. Et ass kee technescht Problem oder e legale Problem. Et ass e psychologesche Problem: dës Aarbecht am Schiet ze maachen, kann onheemlech eleng sinn. Ofhängeg dovun, wat Dir plangt ze maachen, an Ärem Bedrohungsmodell, musst Dir vläicht ganz virsiichteg sinn. Op der enger Säit vum Spektrum hu mir Leit wéi Alexandra Elbakyan*, d'Grënnerin vu Sci-Hub, déi ganz oppe mat hirem Aktivitéiten ass. Mee si ass héich Risiko, verhaft ze ginn, wann si elo e westlecht Land besicht, a kéint Joerzéngte Prisongszäit riskéieren. Ass dat e Risiko, deen Dir bereet wier ze huelen? Mir sinn um aneren Enn vum Spektrum; ganz virsiichteg keng Spuer ze hannerloossen, a staark operationell Sécherheet ze hunn. * Wéi op HN vum "ynno" ernimmt, wollt Alexandra ursprénglech net bekannt sinn: "Hir Servere waren ageriicht, fir detailléiert Feeler Messagen vu PHP auszeginn, inklusiv de komplette Wee vun der fehlerhaften Quellendatei, déi ënner dem Verzeechnes /home/<USER>" Also, benotzt zoufälleg Benotzernimm op de Computeren, déi Dir fir dës Saachen benotzt, am Fall wou Dir eppes falsch konfiguréiere sollt. Dës Geheimhaltung kënnt awer mat engem psychologesche Käschtepunkt. Déi meescht Leit genéissen et, fir hir Aarbecht unerkannt ze ginn, an dach kënnt Dir dofir am richtege Liewen kee Kreditt huelen. Sogar einfach Saache kënnen erausfuerdernd sinn, wéi Frënn, déi Iech froen, wat Dir gemaach hutt (zu engem gewëssen Zäitpunkt gëtt "mat mengem NAS / Homelab fummelen" al). Dofir ass et sou wichteg, eng Gemeinschaft ze fannen. Dir kënnt e bësse vun der operationeller Sécherheet opginn, andeems Dir e puer ganz enke Frënn vertraut, vun deenen Dir wësst, datt Dir se déif vertraue kënnt. Och dann, sidd virsiichteg, näischt schrëftlech ze setzen, am Fall wou si hir E-Maile mussen un d'Autoritéiten iwwerginn, oder wann hir Apparater op eng aner Manéier kompromittéiert sinn. Nach besser ass et, e puer Matpiraten ze fannen. Wann Är enke Frënn interesséiert sinn, matzemaachen, super! Soss kënnt Dir vläicht anerer online fannen. Leider ass dëst nach ëmmer eng Nischgemeinschaft. Bis elo hu mir nëmmen eng Handvoll anerer fonnt, déi an dësem Beräich aktiv sinn. Gutt Startplazen schéngen d'Library Genesis Foren an r/DataHoarder ze sinn. D'Archive Team huet och gläichgesënnte Leit, obwuel si bannent dem Gesetz operéieren (och wann an e puer groen Beräicher vum Gesetz). Déi traditionell "warez" a Piratenszenen hunn och Leit, déi op ähnlech Manéier denken. Mir si fir Iddien oppe wéi mir eng Gemeinschaft fördere kënnen an Iddien entdecken. Fëllt Iech fräi eis op Twitter oder Reddit ze kontaktéieren. Vläicht kéinte mir eng Aart Forum oder Chatgrupp organiséieren. Eng Erausfuerderung ass datt dëst einfach zenséiert ka ginn wann een allgemeng Plattformen benotzt, dofir misste mir et selwer hosten. Et gëtt och en Ofwiessel tëscht dëse Gespréicher komplett ëffentlech ze maachen (méi potenziell Engagement) géint se privat ze halen (net potenziell "Ziler" wëssen ze loossen datt mir se schrauwen). Mir mussen driwwer nodenken. Loosst eis wëssen ob Dir interesséiert sidd! Konklusioun Hoffentlech ass dëst hëllefräich fir nei ufänkend Piratenarchivisten. Mir si begeeschtert, Iech an dëser Welt wëllkomm ze heeschen, also zéckt net, eis ze kontaktéieren. Loosst eis sou vill wéi méiglech vum Wëssen a Kultur vun der Welt erhalen a se wäit a breet spigelen. Projeten 4. Dateauswiel Oft kënnt Dir d'Metadata benotzen, fir e vernünftege Subset vun Daten erauszefannen, déi erofgeluede solle ginn. Och wann Dir schlussendlech all d'Daten erofluede wëllt, kann et nëtzlech sinn, déi wichtegst Elementer als éischt ze prioriséieren, am Fall wou Dir entdeckt gitt an d'Verdeedegungen verbessert ginn, oder well Dir méi Disken kafe musst, oder einfach well eppes anescht an Ärem Liewen opkënnt, ier Dir alles erofluede kënnt. Zum Beispill kéint eng Sammlung méi Editioune vun der selwechter Ressource (wéi e Buch oder e Film) hunn, wou eng als déi bescht Qualitéit markéiert ass. Dës Editiounen als éischt ze späicheren géif vill Sënn maachen. Dir kéint schlussendlech all Editiounen späicheren wëllen, well an e puer Fäll d'Metadata falsch markéiert sinn, oder et kéint onbekannte Kompromësser tëscht den Editiounen ginn (zum Beispill, déi "bescht Editioun" kéint an de meeschte Beräicher déi bescht sinn, awer an anere Beräicher méi schlecht, wéi e Film mat enger méi héijer Opléisung awer ouni Ënnertitelen). Dir kënnt och Är Metadata-Datenbank duerchsichen, fir interessant Saachen ze fannen. Wat ass déi gréisst Datei, déi gehost gëtt, a firwat ass se sou grouss? Wat ass déi klengst Datei? Ginn et interessant oder onerwaart Musteren wann et ëm bestëmmte Kategorien, Sproochen, asw. geet? Ginn et duplizéiert oder ganz ähnlech Titelen? Ginn et Musteren, wéini Daten derbäigesat goufen, wéi een Dag, un deem vill Dateien op eemol derbäigesat goufen? Dir kënnt dacks vill léieren, andeems Dir de Dataset op verschidde Manéiere kuckt. An eisem Fall hu mir Z-Library Bicher géint d'md5 Hashes an der Library Genesis deduplizéiert, an domat vill Downloadzäit a Späicherplaz gespuert. Dëst ass awer eng zimlech eenzegaarteg Situatioun. An de meeschte Fäll ginn et keng ëmfaassend Datebanken, déi weisen, wéi eng Dateien scho richteg vun anere Piraten erhalen sinn. Dëst ass u sech eng riseg Geleeënheet fir een dobaussen. Et wier super, eng reegelméisseg aktualiséiert Iwwersiicht vu Saachen wéi Musek a Filmer ze hunn, déi scho wäit op Torrent-Websäiten gesiedelt sinn, a si dofir manner Prioritéit fir a Piraten-Spigelen abegraff ze ginn. 6. Verdeelung Dir hutt d'Daten, an domat hutt Dir (méiglecherweis) de weltwäit éischte Piratenspiegel vun Ärem Zil. A ville Beräicher ass den haardsten Deel eriwwer, awer de riskantsten Deel steet nach virun Iech. Schliisslech sidd Dir bis elo verstoppelt bliwwen; ënner dem Radar geflunn. Alles wat Dir maache musst, war e gudde VPN ze benotzen, keng perséinlech Detailer an iergendeng Formulairen anzeginn (natierlech), a vläicht eng speziell Browser-Sessioun ze benotzen (oder souguer e anere Computer). Elo musst Dir d'Daten verdeelen. An eisem Fall wollte mir d'Bicher als éischt zréck an d'Library Genesis bäidroen, awer hu séier d'Déifgängkeeten an deem entdeckt (Fiktioun vs Non-Fiktioun Sortéierung). Also hu mir eis fir d'Verdeelung mat Library Genesis-Stil Torrents entscheet. Wann Dir d'Geleeënheet hutt, zu engem bestehende Projet bäizedroen, da kéint dat Iech vill Zäit spueren. Wéi och ëmmer, et ginn net vill gutt organiséiert Piratenspiegel dobaussen de Moment. Also loosst eis soen, Dir entscheet Iech selwer Torrents ze verdeelen. Probéiert dës Dateien kleng ze halen, sou datt se einfach op anere Websäiten gespigelt kënne ginn. Dir musst dann d'Torrents selwer séien, wärend Dir anonym bleift. Dir kënnt e VPN benotzen (mat oder ouni Port-Forwarding), oder mat gewäschene Bitcoins fir e Seedbox bezuelen. Wann Dir net wësst, wat e puer vun dëse Begrëffer bedeiten, hutt Dir e puer Liesen ze maachen, well et wichteg ass, datt Dir d'Risiko-Komprosmëss hei verstitt. Dir kënnt d'Torrent-Dateien selwer op bestehende Torrent-Websäiten hosten. An eisem Fall hu mir gewielt, tatsächlech eng Websäit ze hosten, well mir och eis Philosophie op eng kloer Manéier verbreede wollten. Dir kënnt dëst selwer op eng ähnlech Manéier maachen (mir benotzen Njalla fir eis Domänen an Hosting, bezuelt mat gewäschene Bitcoins), awer fillt Iech och fräi, eis ze kontaktéieren, fir datt mir Är Torrents hosten. Mir sichen, iwwer Zäit en ëmfaassenden Index vu Piratenspiegel opzebauen, wann dës Iddi ugeholl gëtt. Wat d'VPN-Auswiel ugeet, gouf scho vill doriwwer geschriwwen, also widderhuele mir just den allgemenge Rot, no Ruff ze wielen. Tatsächlech gerichtsgepréift No-Log-Politiken mat laange Streckrecorden fir d'Privatsphär ze schützen, ass an eiser Meenung no déi niddregst Risiko-Optioun. Notéiert datt och wann Dir alles richteg maacht, kënnt Dir ni op Null Risiko kommen. Zum Beispill, wann Dir Är Torrents séit, kann en héich motivéierten Natiounsstaats-Akteur wahrscheinlech d'kommend an ausgaangend Dateflëss fir VPN-Server kucken, a feststellen, wien Dir sidd. Oder Dir kënnt einfach iergendwéi e Feeler maachen. Mir hunn et wahrscheinlech schonn, a wäerten et erëm maachen. Glécklecherweis këmmeren sech Natiounsstaaten net <em>sou</em> vill ëm Piraterie. Eng Entscheedung, déi fir all Projet ze treffen ass, ass ob et mat der selwechter Identitéit wéi virdrun verëffentlecht gëtt oder net. Wann Dir weiderhin den selwechten Numm benotzt, da kéinten Feeler an der operationeller Sécherheet vun fréiere Projeten Iech nach verfollegen. Awer ënner verschiddenen Nimm ze verëffentlechen bedeit, datt Dir kee méi laang bestännege Ruff opbaut. Mir hunn gewielt, vun Ufank un eng staark operationell Sécherheet ze hunn, sou datt mir weiderhin déi selwecht Identitéit benotze kënnen, awer mir zécken net, ënner engem aneren Numm ze verëffentlechen, wann mir e Feeler maachen oder wann d'Ëmstänn et verlaangen. D'Wuert erauszekréien kann tricky sinn. Wéi mir gesot hunn, ass dëst nach ëmmer eng Nischgemeinschaft. Mir hunn ursprénglech op Reddit gepost, awer wierklech Traktioun op Hacker News kritt. Fir de Moment ass eis Empfehlung, et op e puer Plazen ze posten an ze kucken, wat geschitt. An nach eng Kéier, kontaktéiert eis. Mir géifen gären d'Wuert vun méi Piratenarchivismus-Beméiungen verbreeden. 1. Domain-Auswiel / Philosophie Et feelt net un Wëssen a kulturellem Ierwen dat gespäichert muss ginn, wat iwwerwältegend ka sinn. Dofir ass et dacks nëtzlech e Moment ze huelen an driwwer nozedenken wat Äre Bäitrag ka sinn. Jidderee huet eng aner Manéier driwwer nozedenken, awer hei sinn e puer Froen déi Dir Iech selwer stelle kéint: An eisem Fall hu mir besonnesch ëm d'laangfristeg Erhaalung vun der Wëssenschaft gekëmmert. Mir woussten iwwer Library Genesis, a wéi et vill Mol iwwer Torrents gespigelt gouf. Mir hunn déi Iddi gär. Dunn een Dag huet ee vun eis probéiert e puer wëssenschaftlech Léierbicher op Library Genesis ze fannen, konnt se awer net fannen, wat d'Fro opgeworf huet wéi komplett et wierklech war. Mir hunn dann déi Léierbicher online gesicht, a se op anere Plazen fonnt, wat de Somen fir eise Projet gepflanzt huet. Och ier mir iwwer d'Z-Library woussten, haten mir d'Iddi net all déi Bicher manuell ze sammelen, mee eis op d'Mirroring vun existente Sammlungen ze fokusséieren, a se zréck bei Library Genesis ze bréngen. Wat fir Fäegkeeten hutt Dir déi Dir zu Ärem Virdeel benotze kënnt? Zum Beispill, wann Dir en Online Sécherheetsexpert sidd, kënnt Dir Weeër fannen fir IP-Blocken fir sécher Ziler ze iwwerwannen. Wann Dir gutt am Organiséiere vu Gemeinschaften sidd, da kënnt Dir vläicht e puer Leit ronderëm e Zil zesummebréngen. Et ass nëtzlech e bësse Programméierung ze kennen, och wann et nëmmen ass fir gutt operationell Sécherheet während dësem Prozess ze halen. Wat wier e Beräich mat héijer Hebelwirkung fir sech ze fokusséieren? Wann Dir X Stonnen op Piratarchiv verbréngt, wéi kënnt Dir de gréisste "Bang fir Äre Buck" kréien? Wat sinn eenzegaarteg Weeër wéi Dir driwwer nodenkt? Dir kënnt e puer interessant Iddien oder Usätz hunn déi anerer vläicht verpasst hunn. Wéi vill Zäit hutt Dir dofir? Eise Rot wier kleng unzefänken an méi grouss Projeten ze maachen wéi Dir et beherrscht, awer et kann alles konsuméierend ginn. Firwat sidd Dir interesséiert un dësem? Wat ass Är Passioun? Wann mir eng Grupp vu Leit kréien déi all déi Saachen archivéieren déi hinnen speziell wichteg sinn, géif dat vill ofdecken! Dir wësst vill méi wéi déi duerchschnëttlech Persoun iwwer Är Passioun, wéi wat wichteg Daten ze späicheren sinn, wat déi bescht Sammlungen an Online Gemeinschaften sinn, an esou weider. 3. Metadata scraping Datum derbäigesat/geännert: sou datt Dir spéider zréckkomme kënnt an Fichieren eroflueden, déi Dir virdru net erofgelueden hutt (obwuel Dir dacks och d'ID oder den Hash dofir benotze kënnt). Hash (md5, sha1): fir ze bestätegen, datt Dir de Fichier richteg erofgelueden hutt. ID: kann eng intern ID sinn, awer IDs wéi ISBN oder DOI sinn och nëtzlech. Fichierenumm / Plaz Beschreiwung, Kategorie, Tags, Auteuren, Sprooch, etc. Gréisst: fir ze berechnen, wéi vill Festplaz Dir braucht. Loosst eis e bësse méi technesch ginn. Fir tatsächlech d'Metadata vun de Websäiten ze scrape, hu mir d'Saachen zimlech einfach gehalen. Mir benotzen Python Skripten, heiansdo curl, an eng MySQL Datebank fir d'Resultater ze späicheren. Mir hunn keng fantastesche scraping Software benotzt, déi komplex Websäiten ka kartéieren, well bis elo hu mir nëmmen eng oder zwou Aarte vu Säiten misse scrape andeems mir einfach duerch IDs enumeréieren an den HTML parsen. Wann et net einfach enumeréiert Säiten sinn, da braucht Dir vläicht e richtege Crawler, deen probéiert all Säiten ze fannen. Ier Dir ufänkt eng ganz Websäit ze scrape, probéiert et manuell fir e bëssen. Gitt duerch e puer Dosen Säiten selwer, fir e Gefill ze kréien, wéi dat funktionéiert. Heiansdo stéisst Dir schonn op IP Blockaden oder aner interessant Verhalen op dës Manéier. Dat selwecht gëllt fir Daten scraping: ier Dir ze déif an dëst Zil gitt, gitt sécher, datt Dir seng Daten effektiv erofluede kënnt. Fir Restriktiounen ze ëmgoen, ginn et e puer Saachen, déi Dir probéiere kënnt. Sinn et aner IP Adressen oder Serveren, déi déi selwecht Daten hosten, awer net déi selwecht Restriktiounen hunn? Sinn et API Endpunkten, déi keng Restriktiounen hunn, während anerer et maachen? Bei wéi engem Downloadrate gëtt Är IP blockéiert, a fir wéi laang? Oder sidd Dir net blockéiert, mee erofgesat? Wat wann Dir e Benotzerkonto erstellt, wéi ännert sech dunn d'Situatioun? Kënnt Dir HTTP/2 benotzen fir Verbindungen op ze halen, an erhéicht dat d'Rate, mat där Dir Säiten ufroe kënnt? Sinn et Säiten, déi méi Fichieren op eemol oplëschten, an ass d'Informatioun do genuch? Saachen, déi Dir wahrscheinlech wëllt späicheren, enthalen: Mir maachen dat typesch an zwou Etappen. Als éischt lueden mir déi rau HTML Fichieren erof, normalerweis direkt an MySQL (fir vill kleng Fichieren ze vermeiden, iwwer déi mir méi schwätzen hei drënner). Dunn, an engem separaten Schrëtt, gi mir duerch déi HTML Fichieren a parsen se an tatsächlech MySQL Tabellen. Op dës Manéier musst Dir net alles vun Ufank un eroflueden, wann Dir e Feeler an Ärem Parsing Code entdeckt, well Dir kënnt einfach d'HTML Fichieren mat dem neie Code nei veraarbechten. Et ass och dacks méi einfach de Veraarbechtungsschrëtt ze paralleliséieren, sou datt Dir e bëssen Zäit spuert (an Dir kënnt de Veraarbechtungscode schreiwen, während de scraping leeft, amplaz béid Schrëtt gläichzäiteg ze schreiwen). Schliisslech, notéiert datt fir e puer Ziler d'Metadata-Scraping alles ass wat et gëtt. Et ginn e puer riseg Metadata-Sammlungen dobaussen, déi net richteg erhalen sinn. Titel Domain-Auswiel / Philosophie: Wou wëllt Dir ongeféier fokusséieren, a firwat? Wat sinn Är eenzegaarteg Passiounen, Fäegkeeten a Ëmstänn déi Dir zu Ärem Virdeel benotze kënnt? Zil-Auswiel: Wéi eng spezifesch Sammlung wäert Dir spigelen? Metadata-Scraping: Katalogiséierung vun Informatiounen iwwer d'Dateien, ouni déi (oft vill méi grouss) Dateien selwer erofzelueden. Daten-Auswiel: Baséierend op der Metadata, d'Donnéeën erofschrauwen déi am relevantsten sinn fir elo ze archivéieren. Kéint alles sinn, awer dacks gëtt et e vernünftege Wee fir Plaz a Bandbreet ze spueren. Daten-Scraping: Tatsächlech d'Donnéeën kréien. Verdeelung: Et a Torrents verpacken, et iergendwou annoncéieren, Leit kréien et ze verbreeden. 5. Date-Scraping Elo sidd Dir prett, d'Daten a Masse erofzelueden. Wéi virdru scho gesot, sollt Dir zu dësem Zäitpunkt scho manuell eng Rei vu Dateien erofgelueden hunn, fir d'Verhalen an d'Restriktiounen vum Zil besser ze verstoen. Wéi och ëmmer, et ginn nach ëmmer Iwwerraschungen, wann Dir tatsächlech ufänkt vill Dateien op eemol erofzelueden. Eis Berodung hei ass haaptsächlech et einfach ze halen. Start andeems Dir einfach eng Rei vu Dateien erofluet. Dir kënnt Python benotzen, an dann op méi Threads ausbauen. Awer heiansdo ass et nach méi einfach, Bash-Dateien direkt aus der Datebank ze generéieren, an dann méi dovun an e puer Terminalfensteren lafen ze loossen, fir opzeskaléieren. E séieren technesche Trick, deen hei derwäert ass ze ernimmen, ass d'Benotzung vun OUTFILE an MySQL, déi Dir iwwerall schreiwe kënnt, wann Dir "secure_file_priv" an mysqld.cnf deaktivéiert (a sécherstellen, och AppArmor ze deaktivéieren/ëmgoen, wann Dir op Linux sidd). Mir späicheren d'Daten op einfache Festplacken. Start mat deem, wat Dir hutt, a baut lues aus. Et kann iwwerwältegend sinn, iwwer d'Lagerung vun Honnerte vu TBs un Daten nozedenken. Wann dat d'Situatioun ass, mat där Dir konfrontéiert sidd, setzt einfach eng gutt Ënnergrupp eraus, an an Ärer Ukënnegung froen no Hëllef fir de Rescht ze späicheren. Wann Dir selwer méi Festplacken wëllt kréien, dann huet r/DataHoarder e puer gutt Ressourcen, fir gutt Offeren ze kréien. Probéiert Iech net ze vill iwwer fantastesche Dateisystemer ze këmmeren. Et ass einfach, an d'Kanéngchenlach ze falen, fir Saachen wéi ZFS opzestellen. Ee techneschen Detail, deen ze beuechten ass, ass datt vill Dateisystemer net gutt mat villen Dateien ëmgoen. Mir hunn erausfonnt, datt eng einfach Ëmgehung ass, méi Verzeechnes ze kreéieren, z.B. fir verschidde ID-Bereicher oder Hash-Präfixen. Nom Erofluede vun den Daten, gitt sécher d'Integritéit vun den Dateien mat Hëllef vun den Hashes an der Metadata ze kontrolléieren, wann verfügbar. 2. Zil-Auswiel Zougänglech: benotzt net vill Schichten vu Schutz fir ze verhënneren, datt Dir hir Metadata an Daten scrape kënnt. Besonnesch Abléck: Dir hutt e puer speziell Informatiounen iwwer dëst Zil, wéi Dir iergendwéi speziell Zougang zu dëser Sammlung hutt, oder Dir hutt erausfonnt, wéi Dir hir Verdeedegung iwwerwanne kënnt. Dëst ass net erfuerderlech (eise kommende Projet mécht näischt Besonnesches), mee et hëlleft sécher! Grouss Also, mir hu eis Gebitt, dat mir kucken, elo wéi eng spezifesch Sammlung spigelen mir? Et ginn e puer Saachen, déi e gudden Zil ausmaachen: Wéi mir eis Wëssenschaftsbicher op anere Websäiten wéi Library Genesis fonnt hunn, hu mir probéiert erauszefannen, wéi se hire Wee op den Internet gemaach hunn. Mir hunn dunn d'Z-Library fonnt, a gemierkt, datt obwuel déi meescht Bicher net als éischt do optrieden, se schlussendlech do landen. Mir hunn iwwer seng Bezéiung zu Library Genesis geléiert, an d'(finanziell) Ureizstruktur an déi iwwerleeën Benotzerinterface, déi allebéid et zu enger vill méi kompletter Sammlung gemaach hunn. Mir hunn dunn e puer virleefeg Metadata an Daten scraping gemaach, a gemierkt, datt mir hir IP Downloadlimiten ëmgoe konnten, andeems mir ee vun eise Memberen hire spezielle Zougang zu villen Proxy Serveren genotzt hunn. Wéi Dir verschidde Ziler exploréiert, ass et scho wichteg Är Spuren ze verstoppen andeems Dir VPNs an ewechgeheit E-Mail Adressen benotzt, iwwer déi mir méi spéit schwätzen. Eenzegaarteg: net scho gutt ofgedeckt vun anere Projeten. Wann mir e Projet maachen, huet en e puer Phasen: Dëst sinn net komplett onofhängeg Phasen, an dacks schécken Abléck aus enger méi spéider Phase Iech zréck an eng méi fréi Phase. Zum Beispill, während der Metadata-Scraping kënnt Dir realiséieren datt d'Zil dat Dir gewielt hutt Verteidegungsmechanismen iwwer Ärem Fäegkeetsniveau huet (wéi IP-Blocken), also gitt Dir zréck a fannt en anert Zil. - Anna an d'Team (<a %(reddit)s>Reddit</a>) Ganz Bicher kéinte geschriwwe ginn iwwer de <em>firwat</em> vun der digitaler Erhalung am Allgemengen, a Pirat-Archivismus am Besonneschen, mee loosst eis e séieren Aféierung ginn fir déi, déi net ze vertraut sinn. D'Welt produzéiert méi Wëssen a Kultur wéi jee virdrun, mee och méi dovun geet verluer wéi jee virdrun. D'Mënschheet vertraut gréisstendeels op Firmen wéi akademesch Verleger, Streaming Servicer a sozial Medienfirmen fir dëst Ierwen, an si hunn dacks net bewisen, datt si grouss Verwalter sinn. Kuckt de Dokumentarfilm Digital Amnesia, oder wierklech all Ried vum Jason Scott. Et ginn e puer Institutiounen, déi eng gutt Aarbecht maachen, sou vill wéi méiglech ze archivéieren, mee si sinn duerch d'Gesetz gebonnen. Als Piraten si mir an enger eenzegaarteger Positioun, fir Kollektiounen ze archivéieren, déi si net beréieren kënnen, wéinst Copyright-Duerchsetzung oder anere Restriktiounen. Mir kënnen och Kollektiounen vill Mol iwwerall op der Welt spigelen, an domat d'Chancen op eng richteg Erhalung erhéijen. Fir de Moment wäerte mir net an Diskussiounen iwwer d'Virdeeler an Nodeeler vum intellektuellen Eegentum, d'Moral vum Gesetz briechen, Iwwerleeungen iwwer Zensur oder d'Fro vum Zougang zu Wëssen a Kultur agoen. Mat all deem aus dem Wee, loosst eis an de <em>wéi</em> dauchen. Mir deelen, wéi eis Equipe Pirat-Archivisten ginn ass, an d'Léier, déi mir ënnerwee geléiert hunn. Et gi vill Erausfuerderungen, wann Dir dës Rees ufänkt, an hoffentlech kënne mir Iech duerch e puer dovun hëllefen. Wéi een e Pirat-Archivist gëtt Déi éischt Erausfuerderung kéint eng iwwerraschend sinn. Et ass kee technescht Problem oder e legale Problem. Et ass e psychologesche Problem. Ier mir ufänken, zwee Updates iwwer de Pirate Library Mirror (EDIT: geplënnert op <a %(wikipedia_annas_archive)s>Annas Archiv</a>): Mir hunn e puer extrem generéis Donatioune kritt. Déi éischt war $10k vun enger anonymer Persoun, déi och "bookwarrior", de Grënner vu Library Genesis, ënnerstëtzt huet. Besonnesche Merci un bookwarrior fir dës Donatioun ze erméiglechen. Déi zweet war nach eng $10k vun engem anonyme Spender, deen no eiser leschter Verëffentlechung a Kontakt koum an inspiréiert war ze hëllefen. Mir haten och eng Rei méi kleng Donatioune. Villmools Merci fir all Är generéis Ënnerstëtzung. Mir hunn e puer spannend nei Projeten an der Pipeline, déi dëst ënnerstëtzen, also bleift drun. Mir haten e puer technesch Schwieregkeeten mat der Gréisst vun eiser zweeter Verëffentlechung, mee eis Torrents sinn elo eropgelueden a seeden. Mir hunn och eng generéis Offer vun enger anonymer Persoun kritt, fir eis Kollektioun op hire ganz héich-Vitesse Serveren ze seeden, also maachen mir en speziellen Upload op hir Maschinnen, no deem jiddereen aneren, deen d'Kollektioun erofluet, eng grouss Verbesserung an der Vitesse gesinn soll. Blog Posts Moien, ech sinn d'Anna. Ech hunn <a %(wikipedia_annas_archive)s>Annas Archiv</a> erstallt, déi weltgréisst Schiedbibliothéik. Dëst ass mäi perséinleche Blog, an deem ech an meng Teamkollegen iwwer Piraterie, digital Erhaalung, a méi schreiwen. Verbënnt Iech mat mir op <a %(reddit)s>Reddit</a>. Notéiert datt dës Websäit just e Blog ass. Mir hosten hei nëmmen eis eege Wierder. Keng Torrents oder aner urheberrechtlech geschützt Dateien ginn hei gehost oder verlinkt. <strong>Library</strong> - Wéi déi meescht Bibliothéiken konzentréiere mir eis haaptsächlech op schrëftlech Materialien wéi Bicher. Mir kéinten an Zukunft op aner Medien expandéieren. <strong>Mirror</strong> - Mir sinn strikt e Spigel vun existente Bibliothéiken. Mir konzentréiere eis op d'Erhalung, net op d'Méiglechkeet, Bicher einfach duerchsichen an erofzelueden (Zougang) oder eng grouss Gemeinschaft vu Leit ze fërderen, déi nei Bicher bäidroen (Quell). <strong>Pirate</strong> - Mir verstoussen bewosst géint d'Copyright-Gesetzer an de meeschte Länner. Dëst erlaabt eis eppes ze maachen, wat legal Entitéiten net kënne maachen: sécherstellen, datt Bicher wäit a breet gespigelt ginn. <em>Mir verlinken net op d'Dateien vun dësem Blog. Fannt se selwer.</em> - Anna an d'Team (<a %(reddit)s>Reddit</a>) Dëst Projet (EDIT: geplënnert op <a %(wikipedia_annas_archive)s>Annas Archiv</a>) zielt drop of, zur Erhalung an Befreiung vum mënschleche Wëssen bäizedroen. Mir maachen eise klenge bescheidenen Bäitrag, an de Foussspuren vun de Groussen virun eis. Den Fokus vun dësem Projet gëtt duerch säin Numm illustréiert: Déi éischt Bibliothéik, déi mir gespigelt hunn, ass Z-Library. Dëst ass eng populär (an illegal) Bibliothéik. Si hunn d'Library Genesis Sammlung geholl an se einfach duerchsichbar gemaach. Zousätzlech si se ganz effektiv ginn, nei Bicherbäiträg ze solicitéieren, andeems se d'Benotzer mat verschiddene Virdeeler belounen. Si droen dës nei Bicher de Moment net zréck bei Library Genesis bäi. An am Géigesaz zu Library Genesis maachen si hir Sammlung net einfach spigelbar, wat eng breet Erhalung verhënnert. Dëst ass wichteg fir hire Geschäftsmodell, well si Sue fir den Zougang zu hirer Sammlung am Bulk (méi wéi 10 Bicher pro Dag) berechnen. Mir maache keng moralesch Uerteeler doriwwer, Suen fir de Massezougang zu enger illegaler Bicherkollektioun ze froen. Et ass onbestridden, datt d'Z-Library erfollegräich war, den Zougang zu Wëssen ze erweideren an méi Bicher ze beschafen. Mir sinn einfach hei fir eise Bäitrag ze leeschten: d'laangfristeg Erhalung vun dëser privater Kollektioun ze sécheren. Mir géifen Iech gären invitéieren, beim Erhalen a Befreiung vum mënschleche Wëssen ze hëllefen, andeems Dir eis Torrents erofluet a seedt. Kuckt d'Projetssäit fir méi Informatiounen iwwer wéi d'Donnéeën organiséiert sinn. Mir géifen Iech och ganz gären invitéieren, Är Iddien bäizedroen, wéi eng Sammlungen als nächst gespigelt solle ginn, a wéi een dat ugeet. Zesumme kënne mir vill erreechen. Dëst ass nëmmen e klenge Bäitrag ënner onzielege aneren. Merci, fir alles wat Dir maacht. Aféierung vum Pirate Library Mirror: Erhalen vun 7TB u Bicher (déi net an Libgen sinn) 10% of vum schrëftleche Patrimoine vun der Mënschheet fir ëmmer erhalen <strong>Google.</strong> No allem hunn si dës Fuerschung fir Google Books gemaach. Wéi och ëmmer, hir metadata ass net a Masse zougänglech an zimlech schwéier ze scrape. <strong>Verschidde individuell Bibliothéiksystemer an Archiven.</strong> Et ginn Bibliothéiken an Archiven, déi net indexéiert an aggregéiert goufen vun engem vun deenen hei uewen, dacks well se ënnerfinanzéiert sinn, oder aus anere Grënn net wëllen hir Daten mat Open Library, OCLC, Google, asw. deelen. Vill vun dësen hunn digital Records, déi iwwer den Internet zougänglech sinn, an si sinn dacks net ganz gutt geschützt, also wann Dir wëllt hëllefen an e bësse Spaass hunn iwwer komesch Bibliothéiksystemer ze léieren, sinn dës super Startpunkten. <strong>ISBNdb.</strong> Dëst ass d'Thema vun dësem Blog Post. ISBNdb scrape verschidde Websäiten fir Buchmetadata, besonnesch Präisdaten, déi se dann un Bicherverkeefer verkafen, sou datt se hir Bicher am Aklang mam Rescht vum Maart präisse kënnen. Well ISBNs hautdesdaags zimlech universell sinn, hunn se effektiv eng "Websäit fir all Buch" gebaut. <strong>Open Library.</strong> Wéi virdru scho gesot, ass dat hir ganz Missioun. Si hunn massiiv Quantitéiten u Bibliothéiksdaten aus kooperéierende Bibliothéiken an nationale Archiven gesammelt, a maachen dat weiderhin. Si hunn och fräiwëlleg Bibliothekären an e technescht Team, dat probéiert Records ze deduplizéieren, an se mat alle méigleche metadata ze taggen. Am beschten ass, hiren Dataset ass komplett op. Dir kënnt et einfach <a %(openlibrary)s>eroflueden</a>. <strong>WorldCat.</strong> Dëst ass eng Websäit, déi vun der net-kommerzieller OCLC gefouert gëtt, déi Bibliothéiksmanagementsystemer verkeeft. Si aggregéieren Buchmetadata aus ville Bibliothéiken, a maachen et iwwer d'WorldCat Websäit zougänglech. Wéi och ëmmer, si verdéngen och Suen andeems se dës Daten verkafen, also sinn se net a Masse fir den Download verfügbar. Si hunn e puer méi limitéiert Masse-Datasets verfügbar fir den Download, a Kooperatioun mat spezifesche Bibliothéiken. 1. Fir eng vernünfteg Definitioun vu "fir ëmmer". ;) 2. Natierlech ass d'schrëftlech Ierfschaft vun der Mënschheet vill méi wéi Bicher, besonnesch hautdesdaags. Fir dëse Post an eis rezent Verëffentlechungen konzentréiere mir eis op Bicher, mee eis Interessen ginn nach méi wäit. 3. Et gëtt vill méi, wat iwwer Aaron Swartz gesot ka ginn, mee mir wollte hien nëmmen kuerz ernimmen, well hien eng entscheedend Roll an dëser Geschicht spillt. Mat der Zäit kéinten méi Leit säin Numm fir d'éischte Kéier begéinen, an da selwer an de Kanéngchenbau dauchen. <strong>Physesch Kopien.</strong> Natierlech ass dat net ganz hëllefräich, well se just Duplikater vum selwechte Material sinn. Et wier cool wann mir all d'Notizen, déi d'Leit an d'Bicher maachen, kéinte konservéieren, wéi Fermat seng berühmt "Kritzeleien an de Marginen". Mee leider bleift dat en Dram vun engem Archivist. <strong>“Editiounen”.</strong> Hei zielt Dir all eenzegaarteg Versioun vun engem Buch. Wann eppes drun anescht ass, wéi en anere Cover oder en anere Virwuert, zielt et als eng aner Editioun. <strong>Fichieren.</strong> Wann Dir mat Schiedbibliothéiken wéi Library Genesis, Sci-Hub oder Z-Library schafft, gëtt et eng zousätzlech Iwwerleeung. Et kann méi Scans vun der selwechter Editioun ginn. An d'Leit kënnen besser Versioune vun existente Fichieren maachen, andeems se den Text mat OCR scannen, oder Säiten korrigéieren, déi schif gescannt goufen. Mir wëllen dës Fichieren nëmmen als eng Editioun zielen, wat gutt metadata erfuerdert, oder d'Deduplikatioun mat Dokumentähnlechkeetsmoossnamen. <strong>“Wierker”.</strong> Zum Beispill "Harry Potter an d'Kummer vun de Geheimnisser" als logescht Konzept, dat all Versioune dovun ëmfaasst, wéi verschidden Iwwersetzungen an Neidréck. Dëst ass eng zimlech nëtzlech Definitioun, awer et kann schwéier sinn d'Linn ze zéien wat zielt. Zum Beispill, mir wëllen wahrscheinlech verschidden Iwwersetzungen erhalen, obwuel Neidréck mat nëmme klenge Differenzen vläicht net esou wichteg sinn. - Anna an d'Team (<a %(reddit)s>Reddit</a>) Mat dem Pirate Library Mirror (EDIT: geplënnert op <a %(wikipedia_annas_archive)s>Annas Archiv</a>), ass et eist Zil, all d'Bicher op der Welt ze huelen an se fir ëmmer ze erhalen.<sup>1</sup> Tëscht eise Z-Library Torrents an den originalen Library Genesis Torrents hu mir 11,783,153 Dateien. Awer wéi vill ass dat wierklech? Wann mir dës Dateien richteg deduplizéieren, wéi vill Prozent vun alle Bicher op der Welt hu mir erhalen? Mir géifen wierklech gären eppes wéi dëst hunn: Loosst eis mat e puer groben Zuelen ufänken: An béid Z-Library/Libgen an Open Library ginn et vill méi Bicher wéi eenzegaarteg ISBNs. Heescht dat datt vill vun dëse Bicher keng ISBNs hunn, oder feelt einfach d'ISBN metadata? Mir kënnen dës Fro wahrscheinlech mat enger Kombinatioun vun automateschem Matching baséiert op aneren Attributer (Titel, Auteur, Verlag, etc.), méi Datenquellen eranzéien, an ISBNs aus den eigentleche Buchscans selwer extrahéieren (am Fall vun Z-Library/Libgen). Wéi vill vun dësen ISBNs sinn eenzegaarteg? Dëst gëtt am beschten mat engem Venn Diagramm illustréiert: Fir méi präzis ze sinn: Mir war iwwerrascht, wéi wéineg Iwwerlappung et gëtt! ISBNdb huet eng riseg Unzuel u ISBNen, déi weder an der Z-Library nach an der Open Library optrieden, an dat selwecht gëllt (an engem méi klenge mee nach ëmmer substantiellen Mooss) fir déi aner zwee. Dëst werft vill nei Froen op. Wéi vill géif automatescht Matching hëllefen, d'Bicher ze taggen, déi net mat ISBNen getaggt goufen? Géif et vill Matcher ginn an dofir eng erhéicht Iwwerlappung? Och, wat géif geschéien, wann mir en 4. oder 5. Dataset derbäi bréngen? Wéi vill Iwwerlappung géife mir dann gesinn? Dëst gëtt eis e Startpunkt. Mir kënnen elo all d'ISBNen kucken, déi net am Z-Library Dataset waren, an déi och net mat Titel/Auteur Felder passen. Dat kann eis hëllefen, all d'Bicher op der Welt ze erhalen: als éischt andeems mir den Internet no Scans duerchsichen, dann andeems mir an d'Realitéit erausginn fir Bicher ze scannen. Dëst kéint souguer duerch Crowdfunding finanzéiert ginn, oder duerch "Bounties" vun de Leit, déi bestëmmte Bicher digitaliséiert gesinn wëllen. All dat ass eng Geschicht fir eng aner Zäit. Wann Dir bei dësem Projet wëllt hëllefen — weider Analysen; méi Metadata sammelen; méi Bicher fannen; Bicher OCRéieren; dëst fir aner Domänen maachen (z.B. Pabeieren, Audiobicher, Filmer, Fernsehsendungen, Zäitschrëften) oder souguer e puer vun dësen Daten fir Saachen wéi ML / grouss Sproochmodell Training verfügbar maachen — kontaktéiert mech w.e.g. (<a %(reddit)s>Reddit</a>). Wann Dir speziell un der Datenanalyse interesséiert sidd, schaffe mir drun, eis Datasets an d'Skripten an engem méi einfach ze benotzenden Format verfügbar ze maachen. Et wier super, wann Dir einfach e Notizbuch kéint forken an domat ufänken ze spillen. Schlussendlech, wann Dir dës Aarbecht ënnerstëtze wëllt, betruecht w.e.g. eng Spende ze maachen. Dëst ass eng komplett fräiwëlleg geleete Operatioun, an Ären Bäitrag mécht en enormen Ënnerscheed. All Bäitrag hëlleft. Fir de Moment huele mir Spenden a Krypto un; kuckt d'Donate Säit op Annas Archiv. Fir e Prozentsaz brauche mir en Nenner: d'Gesamtzuel vun de Bicher, déi jeemools publizéiert goufen.<sup>2</sup> Virum Enn vu Google Books huet en Ingenieur um Projet, Leonid Taycher, <a %(booksearch_blogspot)s>versicht dës Zuel ze schätzen</a>. Hien ass op — mat engem Augenzwinkeren — 129,864,880 komm (“op d'mannst bis Sonndeg”). Hien huet dës Zuel geschat, andeems hien eng vereenegt Datebank vun alle Bicher op der Welt opgebaut huet. Fir dëst huet hien verschidden Datasets zesummegezunn an se dann op verschidde Weeër fusionéiert. Als eng kuerz Ofwäichung, et gëtt eng aner Persoun, déi probéiert huet all d'Bicher op der Welt ze katalogiséieren: Aaron Swartz, de verstuerwene digitale Aktivist an Reddit Matgrënner.<sup>3</sup> Hien huet <a %(youtube)s>Open Library gestart</a> mam Zil vun "eng Websäit fir all Buch dat jeemools publizéiert gouf", andeems hien Daten aus ville verschiddene Quelle kombinéiert huet. Hien huet deultimative Präis fir seng digital Erhalungsaarbecht bezuelt, wéi hien ugeklot gouf fir d'Massendownloaden vun akademesche Pabeieren, wat zu sengem Suizid gefouert huet. Needless to say, dat ass ee vun de Grënn firwat eis Grupp pseudonym ass, a firwat mir ganz virsiichteg sinn. Open Library gëtt nach ëmmer heroesch vun de Leit beim Internet Archive gefouert, a setzt den Aaron säi Liewenswierk weider. Mir kommen spéider an dësem Post op dëst zréck. Am Google Blog Post beschreift Taycher e puer vun den Erausfuerderunge beim Schätzen vun dëser Zuel. Als éischt, wat stellt e Buch duer? Et ginn e puer méiglech Definitiounen: “Editiounen” schéngen déi prakteschst Definitioun vu wat “Bicher” sinn. Praktescherweis gëtt dës Definitioun och benotzt fir eenzegaarteg ISBN Nummeren zouzeschreiwen. Eng ISBN, oder International Standard Book Number, gëtt allgemeng fir den internationale Commerce benotzt, well se mam internationale Barcode System integréiert ass ("International Article Number"). Wann Dir e Buch an de Geschäfter verkafe wëllt, brauch et e Barcode, also kritt Dir eng ISBN. Taycher säi Blog Post ernimmt datt wärend ISBNs nëtzlech sinn, si net universell sinn, well se eréischt an de Mëtt-70er wierklech ugeholl goufen, an net iwwerall op der Welt. Trotzdem ass ISBN wahrscheinlech den am meeschte benotzten Identifizéierer vun Bucheditiounen, also ass et eise beschte Startpunkt. Wann mir all d'ISBNs op der Welt fannen, kréie mir eng nëtzlech Lëscht vu wéi eng Bicher nach musse konservéiert ginn. Also, wou kréie mir d'Donnéeën hier? Et ginn eng Rei vu bestehende Beméiungen, déi probéieren eng Lëscht vun all de Bicher op der Welt zesummenzestellen: An dësem Post si mir frou eng kleng Verëffentlechung unzekënnegen (am Verglach zu eise virdrun Z-Library Verëffentlechungen). Mir hunn de gréissten Deel vun ISBNdb gescraped, an d'Donnéeën fir Torrenting op der Websäit vun der Pirate Library Mirror verfügbar gemaach (EDIT: op <a %(wikipedia_annas_archive)s>Annas Archiv</a> geplënnert; mir wäerten et hei net direkt verlinken, sicht einfach no et). Dëst sinn ongeféier 30,9 Milliounen Records (20GB als <a %(jsonlines)s>JSON Lines</a>; 4,4GB gezippt). Op hirer Websäit behaapten se datt se tatsächlech 32,6 Milliounen Records hunn, also kënne mir iergendwéi e puer verpasst hunn, oder <em>si</em> kéinten eppes falsch maachen. An all Fall, fir de Moment wäerte mir net genau deelen wéi mir et gemaach hunn — mir loossen dat als eng Übung fir de Lieser. ;-) Wat mir deelen, ass eng virleefeg Analyse, fir ze probéieren méi no un d'Zuel vun de Bicher op der Welt ze schätzen. Mir hunn dräi Datasets gekuckt: dësen neien ISBNdb Dataset, eis originell Verëffentlechung vu metadata, déi mir vun der Z-Library Schiedbibliothéik gescraped hunn (déi Library Genesis enthält), an den Open Library Daten Dump. ISBNdb Dump, oder Wéi Vill Bicher Sinn Fir Éiweg Erhalen? Wann mir d'Dateien aus Schiedbibliothéiken richteg deduplizéieren, wéi vill Prozent vun alle Bicher op der Welt hu mir erhalen? Updates iwwer <a %(wikipedia_annas_archive)s>Annas Archiv</a>, déi gréisst wierklech oppe Bibliothéik an der mënschlecher Geschicht. <em>WorldCat Neigestaltung</em> Donnéeën <strong>Format?</strong> <a %(blog)s>Anna’s Archive Container (AAC)</a>, wat am Wesentlechen <a %(jsonlines)s>JSON Lines</a> ass, kompriméiert mat <a %(zstd)s>Zstandard</a>, plus e puer standardiséiert Semantiken. Dës Container ëmfaassen verschidden Aarte vu Records, baséiert op de verschiddene Scrapes, déi mir agesat hunn. Virun engem Joer hu mir <a %(blog)s>ugefaang</a>, dës Fro ze beäntweren: <strong>Wéi vill Prozent vun de Bicher goufen dauerhaft vun Schiedbibliothéiken erhalen?</strong> Loosst eis e puer Basisinformatiounen iwwer d'Donnéeën kucken: Wann e Buch an eng Open-Data-Schiedbibliothéik wéi <a %(wikipedia_library_genesis)s>Library Genesis</a> kënnt, an elo <a %(wikipedia_annas_archive)s>Annas Archiv</a>, gëtt et iwwerall op der Welt gespäichert (duerch Torrents), an domat praktesch fir ëmmer konservéiert. Fir d'Fro ze beäntweren, wéi vill Prozent vun de Bicher erhalen goufen, musse mir den Nenner wëssen: Wéi vill Bicher existéieren insgesamt? An idealerweis hu mir net nëmmen eng Zuel, mee tatsächlech Metadata. Da kënne mir se net nëmmen mat Schiedbibliothéiken vergläichen, mee och <strong>eng TODO-Lëscht vun de verbleiwende Bicher erstellen, déi musse konservéiert ginn!</strong> Mir kéinten souguer ufänken, vun engem crowdsourced Effort ze dreemen, fir dës TODO-Lëscht erofzegoen. Mir hu gescrapten <a %(wikipedia_isbndb_com)s>ISBNdb</a> an den <a %(openlibrary)s>Open Library dataset</a> erofgelueden, mee d'Resultater waren net zefriddestellend. De gréisste Problem war, datt et net vill Iwwerlappung vun ISBNen gouf. Kuckt dëse Venn-Diagramm aus <a %(blog)s>eisem Blogpost</a>: Mir waren ganz iwwerrascht, wéi wéineg Iwwerlappung et tëscht ISBNdb an Open Library gouf, déi béid fräiwëlleg Daten aus verschiddene Quelle wéi Webscrapes a Bibliothéikrecords abannen. Wann se béid gutt Aarbecht leeschten, déi meescht ISBNen dobaussen ze fannen, da géifen hir Kreeser sécherlech substantiell iwwerlappen, oder een wier en Ënnerdeel vum aneren. Et huet eis gefrot, wéi vill Bicher falen <em>komplett ausserhalb vun dëse Kreeser</em>? Mir brauchen eng méi grouss Datebank. Dat ass, wéi mir eis Ziler op déi gréisst Buchdatebank vun der Welt gesat hunn: <a %(wikipedia_worldcat)s>WorldCat</a>. Dëst ass eng proprietär Datebank vun der net-kommerzieller <a %(wikipedia_oclc)s>OCLC</a>, déi metadata Records aus Bibliothéiken aus der ganzer Welt aggregéiert, am Austausch fir dës Bibliothéiken Zougang zum komplette Dataset ze ginn, an se an de Sichresultater vun den Endbenotzer opzetauchen. Och wann OCLC eng net-kommerziell Organisatioun ass, erfuerdert hiren Geschäftsmodell de Schutz vun hirer Datebank. Gutt, mir musse soen, Frënn bei OCLC, mir ginn alles ewech. :-) Iwwer dat lescht Joer hu mir all WorldCat Records akribesch gescrapet. Ufanks haten mir e gléckleche Moment. WorldCat huet just hir komplett Websäit Neigestaltung agefouert (am Aug 2022). Dëst huet eng substantiell Iwwerarbechtung vun hire Backend-Systemer abegraff, déi vill Sécherheetsfeeler agefouert hunn. Mir hunn direkt d'Geleeënheet genotzt a konnten honnertdausende vu Millioune (!) Records an e puer Deeg scrapen. Duerno goufen d'Sécherheetsfeeler lues a lues een nom aneren gefléckt, bis dee leschten, deen mir fonnt hunn, virun ongeféier engem Mount gefléckt gouf. Zu där Zäit haten mir praktesch all Records, a mir waren nëmmen nach op der Sich no e bësse méi héichqualitativen Records. Also hu mir gefillt, et ass Zäit fir d'Verëffentlechung! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Annas Archiv huet de ganze WorldCat (déi weltgréisst Bibliothéik-Metadata-Sammlung) gescraped, fir eng TODO-Lëscht vu Bicher ze maachen, déi musse konservéiert ginn.</em> WorldCat Warnung: dëse Blogpost gouf ofgeschaf. Mir hunn decidéiert, datt IPFS nach net prett ass fir d’Primetime. Mir wäerten nach ëmmer op Dateien op IPFS vun Annas Archiv verlinken wann méiglech, awer mir wäerten et net méi selwer hosten, nach empfeelen mir aneren et mat IPFS ze spigelen. Kuckt w.e.g. eis Torrents Säit wann Dir hëllefe wëllt eis Sammlung ze erhalen. Hëlleft Z-Library op IPFS ze séien Partner Server eroflueden SciDB Extern ausléinen Extern ausléinen (drockbehënnert) Extern eroflueden Metadaten entdecken An Torrents enthale Zréck  (+%(num)s Bonus) onbezuelten bezuelt annuléiert ofgelaf waart op Anna fir ze bestätegen ongëlteg Den Text hei drënner geet op Englesch weider. Go Zrécksetzen Vir Lescht Wann Är E-Mail-Adress net op de Libgen Foren funktionéiert, recommandéiere mir <a %(a_mail)s>Proton Mail</a> (gratis) ze benotzen. Dir kënnt och <a %(a_manual)s>manuell ufroen</a>, datt Äre Kont aktivéiert gëtt. (kann <a %(a_browser)s>Browser Verifikatioun</a> erfuerderen — onlimitéiert Downloads!) Schnelle Partner Server #%(number)s (recommandéiert) (liicht méi séier awer mat Waardelëscht) (kee Browserverifikatioun erfuerderlech) (keen Browserverifikatioun oder Waardelëschten) (keen Waardelëscht, awer kann ganz lues sinn) Lues Partner Server #%(number)s Hörbuch Comicbuch Buch (Fiction) Buch (Non-Fiction) Buch (onbekannt) Wëssenschaftlechen Artikel Zäitschrëft Musekpartitur Aneres Normendokument Net all Säite konnten an PDF konvertéiert ginn Als defekt markéiert an Libgen.li Net sichtbar an Libgen.li Net sichtbar an Libgen.rs Fiction Net sichtbar an Libgen.rs Non-Fiction Exiftool ass bei dëser Datei gescheitert Als „schlechte Datei“ markéiert an Z-Library Feelt an der Z-Library Als „Spam“ markéiert an Z-Library Datei kann net opgemaach ginn (z.B. korrupt Datei, DRM) Copyright-Reklamatioun Downloadproblemer (z.B. keng Verbindung, Feeler Message, ganz lues) Falsch Metadaten (z.B. Titel, Beschreiwung, Coverbild) Aneres Schlecht Qualitéit (z.B. Formatéierungsproblemer, schlecht Scanqualitéit, feelend Säiten) Spam / Datei soll geläscht ginn (z.B. Reklammen, beleidegend Inhalt) %(amount)s (%(amount_usd)s) %(amount)s total %(amount)s (%(amount_usd)s) total Brillante Buchwurm Gléckleche Bibliothekar Stralende Sammler Atemberaubende Archivist Bonus Downloads Cerlalc Tschechesch Metadaten DuXiu 读秀 EBSCOhost eBook Index Google Books Goodreads HathiTrust IA IA Controlled Digital Lending ISBNdb ISBN GRP Libgen.li Ausser "scimag" Libgen.rs Net-Fiktioun an Fiktioun Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russesch Staatsbibliothéik Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads op AA Z-Library Z-Library Chinesesch Titel, Auteur, DOI, ISBN, MD5, … Sich Auteur Beschreiwung a Metadaten-Kommentaren Editioun Original Dateinumm Verlag (spezifescht Sichfeld) Titel Joer verëffentlecht Technesch Detailer Dës Mënz huet e méi héije Minimum wéi üblech. Wielt w.e.g. eng aner Dauer oder eng aner Mënz. D'Ufro konnt net ofgeschloss ginn. Probéiert et w.e.g. an e puer Minutten nach eng Kéier, an wann et weiderhi geschitt, kontaktéiert eis w.e.g. op %(email)s mat engem Screenshot. Et ass en onbekannte Feeler opgetrueden. Kontaktéiert eis w.e.g. op %(email)s mat engem Screenshot. Feeler bei der Bezuelveraarbechtung. Waart w.e.g. e Moment a probéiert et nach eng Kéier. Wann de Problem méi wéi 24 Stonnen unhale sollt, kontaktéiert eis w.e.g. op %(email)s mat engem Screenshot. Mir féieren eng Spendekampagne fir <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">déi gréisst Comics-Schattenbibliothéik op der Welt ze sécheren</a>. Merci fir Är Ënnerstëtzung! <a href="/donate">Spenden.</a> Wann Dir net spende kënnt, iwwerleet Iech w.e.g. eis z'ënnerstëtzen andeems Dir Äre Frënn erzielt, a eis op <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, oder <a href="https://t.me/annasarchiveorg">Telegram</a> verfollegt. Schéckt eis keng E-Mail fir <a %(a_request)s>Bicher unzefroen</a><br>oder kleng (<10k) <a %(a_upload)s>Uploads</a>. Anna’s Archive DMCA / Copyright-Reklamatiounen Bleift a Kontakt Reddit Alternativen SLUM (%(unaffiliated)s) onofhängeg Anna’s Archive brauch Är Hëllef! Wann Dir elo spendt, kritt Dir <strong>duebel</strong> d'Zuel vu schnelle Downloads. Vill probéieren eis erof ze huelen, mee mir kämpfen zréck. Wann Dir dëse Mount spend, kritt Dir <strong>duebel</strong> sou vill séier Downloads. Gëlteg bis Enn dëse Mount. Mënschlecht Wëssen retten: e super Kaddo fir d'Feierdeeg! Memberschaften ginn entspriechend verlängert. Partner Servere sinn net verfügbar wéinst Hosting-Schléissungen. Si sollten geschwënn erëm op sinn. Fir d'Resilienz vun Anna’s Archive ze erhéijen, sichen mir no Fräiwëlleger fir Spigelen ze bedreiwen. Mir hunn eng nei Spendemethod verfügbar: %(method_name)s. Iwwerleet Iech w.e.g. %(donate_link_open_tag)sze spenden</a> — et ass net bëlleg dës Websäit ze bedreiwen, an Är Spende mécht wierklech en Ënnerscheed. Villmools Merci. Empfeel engem Frënd, an Dir kritt allebéid %(percentage)s%% Bonus Fast Downloads! Iwwerrascht een deen Dir gär hutt, gitt hinnen e Kont mat Memberschaft. Dee perfekte Kaddo fir de Valentinsdag! Méi gewuer ginn… Kont Aktivitéit Fortgeschratt Annas Blog ↗ Anna’s Software ↗ beta Codes Explorer Datasets Spenden Erofgelueden Dateien FAQ Startsäit Metadaten verbesseren LLM Daten Aloggen / Registréieren Meng Donatiounen Öffentleche Profil Sichen Sécherheet Torrents Iwwersetzen ↗ Fräiwëllegenaarbecht & Bounties Rezent Downloads: 📚&nbsp;Déi weltwäit gréisst open-source open-data Bibliothéik. ⭐️&nbsp;Spigelt Sci-Hub, Library Genesis, Z-Library, an nach méi. 📈&nbsp;%(book_any)s Bicher, %(journal_article)s Pabeieren, %(book_comic)s Comics, %(magazine)s Zäitschrëften — fir ëmmer erhalen.  an  an nach méi DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Déi gréisst wierklech oppe Bibliothéik an der Mënschheetsgeschicht. 📈&nbsp;%(book_count)s&nbsp;Bicher, %(paper_count)s&nbsp;Pabeieren — fir ëmmer erhalen. ⭐️&nbsp;Mir spigelen %(libraries)s. Mir scrape an open-source %(scraped)s. All eisem Code an Daten sinn komplett open source. OpenLib Sci-Hub ,  📚 Déi weltwäit gréisst open-source open-data Bibliothéik.<br>⭐️ Spigelt Scihub, Libgen, Zlib, an nach méi. Z-Lib Anna’s Archive Ongëlteg Ufro. Besicht %(websites)s. Déi weltgréisst Open-Source Open-Data Bibliothéik. Spigelt Sci-Hub, Library Genesis, Z-Library, an méi. Sich Anna’s Archive Anna’s Archive Probéiert w.e.g. nach eng Kéier. <a %(a_contact)s>Kontaktéiert eis</a> wann de Problem fir méi Stonnen unhale sollt. 🔥 Problem beim Luede vun dëser Säit <li>1. Follegt eis op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, oder <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Verbreet d'Wuert iwwer Anna’s Archive op Twitter, Reddit, Tiktok, Instagram, an ärem lokale Café oder Bibliothéik, oder iwwerall wou dir gitt! Mir gleewen net un d'Ofschiermung — wann mir ofgeschalt ginn, poppe mir einfach soss anzwousch op, well all eise Code an Donnéeën komplett Open Source sinn.</li><li>3. Wann dir kënnt, betruecht <a href="/donate">ze spenden</a>.</li><li>4. Hëlleft <a href="https://translate.annas-software.org/">eis Websäit</a> an aner Sproochen ze iwwersetzen.</li><li>5. Wann dir Software Ingenieur sidd, betruecht bäizedroen zu eisem <a href="https://annas-software.org/">Open Source</a>, oder eis <a href="/datasets">Torrents</a> ze séeden.</li> 10. Erstellt oder hëlleft d'Wikipedia Säit fir Anna’s Archive an ärer Sprooch ze erhalen. 11. Mir sichen no klengen, dezenten Annoncen. Wann Dir op Anna’s Archive wëllt annoncéieren, sot eis w.e.g. Bescheed. 6. Wann dir Sécherheetsfuerscher sidd, kënne mir är Fäegkeeten souwuel fir Ugrëff wéi och Verdeedegung gebrauchen. Kuckt eis <a %(a_security)s>Sécherheets</a> Säit un. 7. Mir sichen Experten a Bezuelungen fir anonym Händler. Kënnt dir eis hëllefen méi praktesch Weeër derbäi ze maachen fir ze spenden? PayPal, WeChat, Cadeaukaarten. Wann dir een kennt, kontaktéiert eis w.e.g. 8. Mir sichen ëmmer no méi Serverkapazitéit. 9. Dir kënnt hëllefen andeems dir Dateiproblemer mellt, Kommentaren hannerloosst, a Lëschten op dëser Websäit erstellt. Dir kënnt och hëllefen andeems dir <a %(a_upload)s>méi Bicher eropluet</a>, oder Dateiproblemer oder Formatéierung vun existente Bicher fixéiert. Fir méi extensiv Informatiounen iwwer wéi een als Fräiwëllegen hëllefe kann, kuckt eis <a %(a_volunteering)s>Volunteering & Bounties</a> Säit. Mir gleewen staark un de fräie Floss vun Informatiounen an d'Erhale vu Wëssen a Kultur. Mat dëser Sichmaschinn bauen mir op d'Schëllere vu Risen. Mir respektéieren déif d'schwiereg Aarbecht vun de Leit, déi déi verschidde Schiedbibliothéiken erstallt hunn, a mir hoffen, datt dës Sichmaschinn hir Reechwäit erweidert. Fir um Lafenden ze bleiwen iwwer eise Fortschrëtt, folgt Anna op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> oder <a href="https://t.me/annasarchiveorg">Telegram</a>. Fir Froen a Feedback kontaktéiert w.e.g. Anna op %(email)s. Kont ID: %(account_id)s Logout ❌ Eppes ass schif gaangen. Lued d'Säit nei a probéiert et nach eng Kéier. ✅ Dir sidd elo ausgeloggt. Lued d'Säit nei fir erëm anzeloggen. Séier Downloads benotzt (lescht 24 Stonnen): <strong>%(used)s / %(total)s</strong> Memberschaft: <strong>%(tier_name)s</strong> bis %(until_date)s <a %(a_extend)s>(verlängeren)</a> Dir kënnt méi Memberschaften kombinéieren (schnell Downloads pro 24 Stonnen ginn zesummegerechent). Memberschaft: <strong>Keng</strong> <a %(a_become)s>(Member ginn)</a> Kontaktéiert d'Anna op %(email)s wann Dir interesséiert sidd Äre Memberschaft op en héijeren Niveau ze upgrade. Ëffentleche Profil: %(profile_link)s Geheime Schlëssel (net deelen!): %(secret_key)s weisen Kommt hei bei eis mat! Upgrade op en <a %(a_tier)s>méi héijen Niveau</a> fir an eis Grupp bäizetrieden. Exklusiv Telegram Grupp: %(link)s Kont wéi eng Downloads? Login Verléiert Äre Schlëssel net! Ongëltege geheime Schlëssel. Iwwerpréift Äre Schlëssel a probéiert et nach eng Kéier, oder registréiert alternativ en neie Kont hei drënner. Geheime Schlëssel Gitt Äre geheime Schlëssel an fir anzeloggen: Alen E-Mail-baséierte Kont? Gitt Är <a %(a_open)s>E-Mail hei an</a>. Neie Kont registréieren Hutt Dir nach keen Kont? Registréierung erfollegräich! Äre geheime Schlëssel ass: <span %(span_key)s>%(key)s</span> Späichert dëse Schlëssel virsiichteg. Wann Dir en verléiert, verléiert Dir den Zougang zu Ärem Kont. <li %(li_item)s><strong>Bookmark.</strong> Dir kënnt dës Säit markéieren fir Äre Schlëssel erëmzefannen.</li><li %(li_item)s><strong>Eroflueden.</strong> Klickt <a %(a_download)s>dëse Link</a> fir Äre Schlëssel erofzelueden.</li><li %(li_item)s><strong>Passwuert Manager.</strong> Benotzt e Passwuert Manager fir de Schlëssel ze späicheren wann Dir en hei drënner agitt.</li> Aloggen / Registréieren Browser Verifikatioun Warnung: de Code huet falsch Unicode-Zeichen dran, an et kéint a verschiddene Situatiounen net korrekt funktionéieren. D'Rohbinärdatei kann aus der base64-Darstellung an der URL dekodéiert ginn. Beschreiwung Label Präfix URL fir e spezifesche Code Websäit Coden déi mat “%(prefix_label)s” ufänken W.e.g. scrape dës Säiten net. Mir recommandéieren amplaz <a %(a_import)s>generéieren</a> oder <a %(a_download)s>eroflueden</a> vun eisen ElasticSearch an MariaDB Datebanken, an eisem <a %(a_software)s>open source Code</a> lafen ze loossen. D'Rohdaten kënnen manuell duerch JSON-Dateien wéi <a %(a_json_file)s>dës hei</a> exploréiert ginn. Manner wéi %(count)s Opzeechnungen Generesch URL Code Explorer Index vun Entdeckt d'Coden, mat deenen d'Records markéiert sinn, no Präfix. D'Kolonn „Records“ weist d'Zuel vun de Records, déi mat Coden mat dem uginnene Präfix markéiert sinn, wéi am Sichmotor gesinn (och nëmme Metadaten-Records). D'Kolonn „Coden“ weist wéi vill tatsächlech Coden en uginnene Präfix hunn. Bekannte Code-Präfix “%(key)s” Méi… Präfix %(count)s Opzeechnung entsprécht “%(prefix_label)s” %(count)s Opzeechnungen entspriechen “%(prefix_label)s” Coden Opzeechnungen „%%s“ gëtt duerch de Wäert vum Code ersat Sich Annas Archiv Coden URL fir spezifesche Code: “%(url)s” Dës Säit kann e bëssen daueren fir ze generéieren, dofir ass e Cloudflare Captcha erfuerderlech. <a %(a_donate)s>Memberen</a> kënnen de Captcha iwwersprangen. Mëssbrauch gemellt: Besser Versioun Wëllt Dir dëse Benotzer fir mëssbraucht oder onpassend Verhalen mellen? Dateiproblem: %(file_issue)s verstoppten Kommentar Äntwerten Mëssbrauch mellen Dir hutt dëse Benotzer fir Mëssbrauch gemellt. Copyright Uspréch un dës E-Mail ginn ignoréiert; benotzt amplaz de Formulaire. E-Mail weisen Mir begréissen Äre Feedback an Är Froen ganz häerzlech! Wéinst der grousser Unzuel u Spam an onsënnegen E-Mailen, déi mir kréien, markéiert w.e.g. d'Këschte fir ze bestätegen, datt Dir dës Konditioune fir eis ze kontaktéieren verstanen hutt. All aner Weeër fir eis iwwer Copyright Uspréch ze kontaktéieren, ginn automatesch geläscht. Fir DMCA / Copyright Uspréch, benotzt <a %(a_copyright)s>dëse Formulaire</a>. Kontakt E-Mail URLen op Anna’s Archive (erfuerderlech). Eng pro Zeil. W.e.g. nëmmen URLen abannen, déi déi exakt selwecht Editioun vun engem Buch beschreiwen. Wann Dir Uspréch fir méi Bicher oder méi Editiounen maache wëllt, schéckt w.e.g. dëse Formulaire méi Mol of. Uspréch, déi méi Bicher oder Editiounen zesummefaassen, ginn ofgeleent. Adress (erfuerderlech) Kloer Beschreiwung vum Quellmaterial (erfuerderlech) E-Mail (erfuerderlech) URLen zum Quellmaterial, eng pro Zeil (erfuerderlech). W.e.g. sou vill wéi méiglech abannen, fir eis ze hëllefen, Ären Uspréch ze verifizéieren (z.B. Amazon, WorldCat, Google Books, DOI). ISBNen vum Quellmaterial (wann zoutrëfft). Eng pro Zeil. W.e.g. nëmmen déi abannen, déi exakt mat der Editioun entspriechen, fir déi Dir e Copyright-Uspréch mellt. Ären Numm (erfuerderlech) ❌ Eppes ass schif gaangen. Lued w.e.g. d’Säit nei a probéiert et nach eng Kéier. ✅ Merci fir Ären Copyright-Uspréch anzereechen. Mir wäerten en esou séier wéi méiglech iwwerpréiwen. Lued w.e.g. d’Säit nei, fir en aneren anzereechen. <a %(a_openlib)s>Open Library</a> URLen vum Quellmaterial, eng pro Zeil. Hëlt Iech w.e.g. e Moment Zäit, fir an der Open Library no Ärem Quellmaterial ze sichen. Dëst hëlleft eis, Ären Uspréch ze verifizéieren. Telefonsnummer (erfuerderlech) Ausso an Ënnerschrëft (erfuerderlech) Uspréch ofginn Wann Dir eng DCMA oder eng aner Copyright-Uspréch hutt, fëllt w.e.g. dëse Formulaire esou präzis wéi méiglech aus. Wann Dir Problemer hutt, kontaktéiert eis w.e.g. op eiser spezialiséierter DMCA-Adress: %(email)s. Notéiert w.e.g., datt Uspréch, déi op dës Adress geschéckt ginn, net verschafft ginn, se ass nëmme fir Froen. Benotzt w.e.g. de Formulaire hei ënnen, fir Är Uspréch anzereechen. DMCA / Copyright-Uspréch-Formulaire Beispillrekord op Anna’s Archive Torrents vun Anna’s Archive Anna’s Archive Containers Format Skripter fir Metadaten ze importéieren Wann Dir interesséiert sidd, dësen Dataset fir <a %(a_archival)s>Archivéierungs</a> oder <a %(a_llm)s>LLM-Training</a> Zwecker ze spigelen, kontaktéiert eis w.e.g. Lescht aktualiséiert: %(date)s Haapt %(source)s Websäit Metadaten Dokumentatioun (meescht Felder) Dateien gespigelt vun Anna’s Archive: %(count)s (%(percent)s%%) Ressourcen Total Dateien: %(count)s Total Dateigréisst: %(size)s Eise Blogpost iwwer dës Donnéeën <a %(duxiu_link)s>Duxiu</a> ass eng riseg Datebank vu gescannten Bicher, erstallt vun der <a %(superstar_link)s>SuperStar Digital Library Group</a>. Déi meescht si wëssenschaftlech Bicher, déi gescannt goufen, fir se digital fir Universitéiten a Bibliothéiken zougänglech ze maachen. Fir eis engleschsproocheg Publikum hunn <a %(princeton_link)s>Princeton</a> an d'<a %(uw_link)s>University of Washington</a> gutt Iwwersiichten. Et gëtt och en exzellente Artikel, deen méi Hannergrond gëtt: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. D'Bicher vu Duxiu goufen zënter laangem um chineseschen Internet piratéiert. Normalerweis gi se fir manner wéi en Dollar vu Weiderverkeefer verkaaft. Si ginn typesch iwwer den chineseschen Equivalent vu Google Drive verdeelt, deen dacks gehackt gouf, fir méi Späicherplaz ze erlaben. E puer technesch Detailer fannt Dir <a %(link1)s>hei</a> an <a %(link2)s>hei</a>. Obwuel d'Bicher semi-ëffentlech verdeelt goufen, ass et zimlech schwéier, se a grousse Quantitéiten ze kréien. Mir haten dëst héich op eiser TODO-Lëscht a hu méi Méint vollzäiteg Aarbecht dofir zougewise. Awer Enn 2023 huet en onheemlechen, erstaunlechen an talentéierte Fräiwëlleger eis kontaktéiert a gesot, datt si all dës Aarbecht scho gemaach haten — mat groussem Opwand. Si hunn déi ganz Sammlung mat eis gedeelt, ouni eppes dofir ze erwaarden, ausser der Garantie vun der laangfristeger Erhaalung. Wierklech bemierkenswäert. Méi Informatioun vun eise Fräiwëlleger (rauwe Notizen): Adaptéiert vun eisem <a %(a_href)s>Blogpost</a>. DuXiu 读秀 %(count)s Datei %(count)s Dateien Dësen Dataset ass enk mat dem <a %(a_datasets_openlib)s>Open Library Dataset</a> verbonnen. En enthält en Scrape vun allen Metadaten an engem groussen Deel vu Fichieren aus der IA’s Controlled Digital Lending Library. Updates ginn am <a %(a_aac)s>Anna’s Archive Containers Format</a> verëffentlecht. Dës Opzeechnungen ginn direkt aus dem Open Library Dataset referenzéiert, enthalen awer och Opzeechnungen, déi net an der Open Library sinn. Mir hunn och eng Zuel vu Datefichieren, déi vun der Gemeinschaft iwwer d’Joren erausgeschrauft goufen. D’Sammlung besteet aus zwee Deeler. Dir braucht béid Deeler, fir all Daten ze kréien (ausser iwwerschriwwe Torrents, déi op der Torrents-Säit duerchgestrach sinn). Digital Verleessbibliothéik eis éischt Verëffentlechung, ier mir op de <a %(a_aac)s>Anna’s Archive Containers (AAC) Format</a> standardiséiert hunn. Enthält Metadaten (als json an xml), pdfen (vun acsm an lcpdf digitale Verleessystemer), an Cover-Thumbnails. inkrementell nei Verëffentlechungen, mat AAC. Enthält nëmmen Metadaten mat Zäitzonen no 2023-01-01, well de Rescht scho vun "ia" ofgedeckt ass. Och all pdf Dateien, dës Kéier vun den acsm an "bookreader" (IA’s Web Lieser) Verleessystemer. Trotz dem Numm net ganz richteg ass, befüllen mir nach ëmmer bookreader Dateien an d'ia2_acsmpdf_files Sammlung, well se géigesäiteg exklusiv sinn. IA Kontrolléiert Digital Verléinung 98%%+ vun de Fichieren si duerchsichbar. Eis Missioun ass all d'Bicher op der Welt (souwéi Pabeieren, Zäitschrëften, etc.) ze archivéieren an se breet zougänglech ze maachen. Mir gleewen datt all Bicher wäit a breet gespigelt solle ginn, fir Redundanz a Resilienz ze garantéieren. Dofir sammelen mir Dateien aus verschiddene Quelle. E puer Quelle sinn komplett oppe a kënnen a Masse gespigelt ginn (wéi Sci-Hub). Anerer sinn zou a schützend, also probéieren mir se ze scrape fir hir Bicher ze "befreien". Anerer falen iergendwou dertëscht. All eis Daten kënnen <a %(a_torrents)s>getorrent</a> ginn, an all eis Metadaten kënnen als ElasticSearch an MariaDB Datenbanken <a %(a_anna_software)s>generéiert</a> oder <a %(a_elasticsearch)s>erofgeluede</a> ginn. D'Rohdaten kënnen manuell duerch JSON Dateien wéi <a %(a_dbrecord)s>dës</a> exploréiert ginn. Metadaten ISBN Websäit Lescht aktualiséiert: %(isbn_country_date)s (%(link)s) Ressourcen D'International ISBN Agency verëffentlecht reegelméisseg d'Beräicher, déi se un national ISBN Agenturen zougewisen huet. Doraus kënne mir ofleeden, zu wéi engem Land, Regioun oder Sproochgrupp dësen ISBN gehéiert. Mir benotzen dës Donnéeën aktuell indirekt, duerch d'<a %(a_isbnlib)s>isbnlib</a> Python Bibliothéik. ISBN Landinformatiounen Dëst ass en Dump vun enger grousser Unzuel u Uruff un isbndb.com während dem September 2022. Mir hunn probéiert all ISBN Beräicher ofzedecken. Dëst sinn ongeféier 30,9 Millioune Records. Op hirer Websäit behaapten si, datt si tatsächlech 32,6 Millioune Records hunn, also kënne mir iergendwéi e puer verpasst hunn, oder <em>si</em> kéinte Feeler maachen. D'JSON Äntwerten sinn praktesch ro vun hirem Server. E Qualitéitsproblem, dat mir bemierkt hunn, ass datt fir ISBN-13 Nummeren, déi mat engem anere Präfix wéi "978-" ufänken, si nach ëmmer e "isbn" Feld enthalen, dat einfach d'ISBN-13 Nummer mat den éischten 3 Zuelen ofgeschnidden ass (an d'Kontrollziffer nei berechent). Dëst ass offensichtlech falsch, awer esou schéngen si et ze maachen, also hu mir et net geännert. En anere potenzielle Problem, deen Dir begéine kënnt, ass d'Tatsaach, datt d'"isbn13" Feld Duplikater huet, also kënnt Dir et net als primäre Schlëssel an enger Datebank benotzen. "isbn13"+"isbn" Felder kombinéiert schéngen awer eenzegaarteg ze sinn. Verëffentlechung 1 (2022-10-31) Fiktioun Torrents sinn hannendran (obwuel IDs ~4-6M net getorrent goufen well se mat eise Zlib Torrents iwwerlappen). Eise Blogpost iwwer d'Comic-Bicher-Verëffentlechung Comics-Torrents op Annas Archiv Fir d'Hannergrondgeschicht vun de verschiddene Library Genesis Forken, kuckt d'Säit fir d'<a %(a_libgen_rs)s>Libgen.rs</a>. D'Libgen.li enthält déi meescht vun de selwechte Inhalter a Metadaten wéi d'Libgen.rs, awer huet e puer Sammlungen uewen drop, nämlech Comics, Magaziner an Normdokumenter. Et huet och <a %(a_scihub)s>Sci-Hub</a> an seng Metadaten a Sichmaschinn integréiert, wat mir fir eis Datebank benotzen. D'Metadaten fir dës Bibliothéik sinn fräi verfügbar <a %(a_libgen_li)s>op libgen.li</a>. Allerdéngs ass dëse Server lues a ënnerstëtzt net d'Weiderféierung vu gebrachene Verbindungen. Déiselwecht Dateien sinn och op <a %(a_ftp)s>engem FTP Server</a> verfügbar, deen besser funktionéiert. Net-fiktioun schéngt och ofgewandert ze sinn, awer ouni nei Torrents. Et schéngt, datt dëst zënter Ufank 2022 geschitt ass, obwuel mir dat net verifizéiert hunn. Laut dem Libgen.li Administrator soll d'“fiction_rus” (russesch Fiktioun) Kollektioun duerch reegelméisseg verëffentlecht Torrents vun <a %(a_booktracker)s>booktracker.org</a> ofgedeckt ginn, besonnesch d'<a %(a_flibusta)s>flibusta</a> an <a %(a_librusec)s>lib.rus.ec</a> Torrents (déi mir <a %(a_torrents)s>hei</a> spigelen, obwuel mir nach net festgestallt hunn, wéi eng Torrents mat wéi enge Fichieren entspriechen). D'Fiktiounskollektioun huet hir eege Torrents (ofgeleet vun <a %(a_href)s>Libgen.rs</a>) ugefaange bei %(start)s. Bestëmmte Beräicher ouni Torrents (wéi Fiktiounsberäicher f_3463000 bis f_4260000) si wahrscheinlech Z-Library (oder aner duplizéiert) Fichieren, obwuel mir vläicht e puer Deduplizéierung maache wëllen an Torrents fir lgli-eenzegaarteg Fichieren an dëse Beräicher maachen. Statistiken fir all Kollektioune fannt Dir <a %(a_href)s>op der Libgen Websäit</a>. Torrents si fir déi meescht zousätzlech Inhalter verfügbar, besonnesch Torrents fir Comics, Zäitschrëften an Standarddokumenter goufen a Kollaboratioun mat Annas Archiv verëffentlecht. Notéiert datt d'Torrentdateien, déi op “libgen.is” verweisen, explizit Spigelen vun <a %(a_libgen)s>Libgen.rs</a> sinn (“.is” ass eng aner Domain, déi vu Libgen.rs benotzt gëtt). Eng hëllefräich Ressource fir d'Metadata ze benotzen ass <a %(a_href)s>dës Säit</a>. %(icon)s Hir “fiction_rus” Kollektioun (russesch Fiktioun) huet keng eegen Torrents, mee gëtt duerch Torrents vun aneren ofgedeckt, an mir halen e <a %(fiction_rus)s>Spigel</a>. Russesch Fiktioun Torrents op Annas Archiv Fiktiounstorrents op Annas Archiv Diskussiounsforum Metadata Metadata via FTP Magaziner-Torrents op Annas Archiv Metadatafeldinformatiounen Spigel vun aneren Torrents (an eenzegaarteg Fiktioun- a Comics-Torrents) Standarddokumenter Torrents op Annas Archiv Libgen.li Torrents vun Annas Archiv (Buchdeckelen) Library Genesis ass bekannt dofir, hir Donnéeën generéis a Bulk iwwer Torrents zur Verfügung ze stellen. Eis Libgen Sammlung besteet aus zousätzlechen Donnéeën, déi si net direkt verëffentlechen, an Zesummenaarbecht mat hinnen. Villmools Merci un all déi, déi mat Library Genesis zesummegeschafft hunn! Eise Blog iwwer d'Verëffentlechung vun de Buchdeckelen Dës Säit geet ëm déi “.rs” Versioun. Si ass bekannt dofir, konsequent souwuel hir Metadata wéi och den vollen Inhalt vun hirem Buchkatalog ze verëffentlechen. Hir Buchsammlung ass tëscht enger Fiktioun an enger Non-Fiktioun Deeler opgedeelt. Eng hëllefräich Ressource fir d'Metadata ze benotzen ass <a %(a_metadata)s>dës Säit</a> (IP-Bereicher blockéiert, VPN kéint erfuerderlech sinn). Ab 2024-03 ginn nei Torrents an <a %(a_href)s>dësem Forum Thread</a> gepost (IP-Bereicher blockéiert, VPN kéint néideg sinn). Fiction Torrents op Annas Archiv Libgen.rs Fiction Torrents Libgen.rs Diskussiounsforum Libgen.rs Metadaten Libgen.rs Metadatenfeld Informatiounen Libgen.rs Non-Fiction Torrents Non-Fiction Torrents op Annas Archiv %(example)s fir e Fiction Buch. Dës <a %(blog_post)s>éischt Verëffentlechung</a> ass zimlech kleng: ongeféier 300GB u Buchdeckelen aus dem Libgen.rs Fork, souwuel Fiction wéi och Non-Fiction. Si sinn op déiselwecht Manéier organiséiert wéi se op libgen.rs erschéngen, z.B.: %(example)s fir e Non-Fiction Buch. Genausou wéi bei der Z-Library Sammlung, hu mir se all an eng grouss .tar Datei gesat, déi mat <a %(a_ratarmount)s>ratarmount</a> montéiert ka ginn, wann Dir d'Dateien direkt zerwéiere wëllt. Verëffentlechung 1 (%(date)s) Déi kuerz Geschicht vun de verschiddene Library Genesis (oder “Libgen”) Forks ass, datt mat der Zäit déi verschidde Leit, déi mat Library Genesis bedeelegt waren, sech zerstridden hunn an hir eegen Weeër gaange sinn. Geméiss dësem <a %(a_mhut)s>Forumspost</a> war Libgen.li ursprénglech op „http://free-books.dontexist.com“ gehost. Déi “.fun” Versioun gouf vum urspréngleche Grënner erstallt. Si gëtt nei entwéckelt fir eng nei, méi verdeelt Versioun ze favoriséieren. Déi <a %(a_li)s>“.li” Versioun</a> huet eng riseg Sammlung vu Comics, souwéi aneren Inhalt, deen (nach) net fir Bulk-Download duerch Torrents verfügbar ass. Si huet awer eng separat Torrent-Sammlung vu Fiktiounsbicher, an et enthält d'Metadata vu <a %(a_scihub)s>Sci-Hub</a> an hirer Datebank. Déi “.rs” Versioun huet ganz ähnlech Daten a verëffentlecht hir Sammlung am konsequentsten a Bulk-Torrents. Si ass ongeféier an eng “Fiktioun” an eng “Non-Fiktioun” Sektioun opgedeelt. Ursprénglech op „http://gen.lib.rus.ec“. <a %(a_zlib)s>Z-Library</a> ass an engem gewëssene Sënn och e Fork vu Library Genesis, obwuel si en aneren Numm fir hiert Projet benotzt hunn. Libgen.rs Mir beräicheren eis Sammlung och mat nëmmen Metadaten Quellen, déi mir mat Dateien vergläiche kënnen, z.B. mat ISBN Nummeren oder anere Felder. Hei drënner ass en Iwwerbléck vun dësen. Erëm, e puer vun dëse Quellen sinn komplett oppe, während mir anerer scrape mussen. Notéiert datt mir bei der Metadaten-Sich déi originell Opzeechnungen weisen. Mir maachen keng Zesummeféierung vun den Opzeechnungen. Nëmmen Metadaten Quellen Open Library ass e Open Source Projet vum Internet Archive fir all Buch op der Welt ze katalogiséieren. Et huet eng vun de weltgréisste Buchscannoperatiounen a vill Bicher sinn digital ze léinen. Säi Buchmetadatenkatalog ass fräi verfügbar fir erofzelueden an ass op Annas Archiv abegraff (awer aktuell net an der Sich, ausser wann Dir explizit no enger Open Library ID sicht). Open Library Duplikater ausgeschloss Lescht aktualiséiert Prozentzuelen vun der Unzuel u Fichieren %% gespigelt vun AA / Torrents verfügbar Gréisst Quell Hei drënner ass eng séier Iwwersiicht vun de Quelle vun den Dateien op Anna’s Archive. Well d’Shadow-Bibliothéiken dacks Daten vuneneen synchroniséieren, gëtt et e wesentlechen Iwwerlapp tëscht de Bibliothéiken. Dofir addéieren d’Zuelen sech net op den Total. De Prozentsaz “gespigelt a geséit vun Anna’s Archive” weist, wéi vill Fichieren mir selwer spigelen. Mir séien dës Fichieren a Masse duerch Torrents, a maachen se duerch Partner-Websäiten direkt erofluedbar. Iwwersiicht Total Torrents op Anna’s Archive Fir en Hannergrond iwwer Sci-Hub, kuckt w.e.g. op seng <a %(a_scihub)s>offiziell Websäit</a>, <a %(a_wikipedia)s>Wikipedia-Säit</a>, an dësen <a %(a_radiolab)s>Podcast-Interview</a>. Notéiert w.e.g. datt Sci-Hub zënter <a %(a_reddit)s>2021 agefruer</a> ass. Et war virdrun och schonn agefruer, awer 2021 goufen e puer Millioune Pabeieren derbäigesat. Trotzdem ginn nach ëmmer e puer limitéiert Zuel vu Pabeieren an d'Libgen “scimag” Sammlungen derbäigesat, awer net genuch fir nei bulk Torrents ze rechtfäerdegen. Mir benotzen d'Sci-Hub Metadaten wéi se vun <a %(a_libgen_li)s>Libgen.li</a> an hirer “scimag” Sammlung geliwwert ginn. Mir benotzen och den <a %(a_dois)s>dois-2022-02-12.7z</a> Dataset. Notéiert w.e.g. datt d'“smarch” Torrents <a %(a_smarch)s>veralt</a> sinn an dofir net an eiser Torrents-Lëscht abegraff sinn. Torrents op Libgen.li Torrents op Libgen.rs Metadaten an Torrents Updates op Reddit Podcast-Interview Wikipedia-Säit Sci-Hub Sci-Hub: agefruer zënter 2021; meeschtens iwwer Torrents verfügbar Libgen.li: kleng Ergänzunge säit deem</div> E puer Quellbibliothéiken förderen d'Massendeelung vun hiren Donnéeën duerch Torrents, während anerer hir Sammlung net direkt deelen. Am leschte Fall probéiert Anna’s Archive hir Sammlungen ze scrape an se verfügbar ze maachen (kuckt eis <a %(a_torrents)s>Torrents</a> Säit). Et ginn och Zwëschefäll, zum Beispill, wou Quellbibliothéiken bereet sinn ze deelen, awer net d'Ressourcen hunn, dat ze maachen. An dëse Fäll probéiere mir och ze hëllefen. Hei drënner ass en Iwwerbléck wéi mir mat de verschiddene Quellbibliothéiken interagéieren. Quellbibliothéiken %(icon)s Verschidde Fichiersdatenbanken, déi iwwer den chineseschen Internet verdeelt sinn; oft bezuelte Datenbanken. %(icon)s Déi meescht Fichieren sinn nëmme mat Premium BaiduYun Konten zougänglech; lues Download-Geschwindegkeeten. %(icon)s Anna’s Archive verwalt eng Kollektioun vu <a %(duxiu)s>DuXiu Fichieren</a> %(icon)s Verschidde Metadaten Datebanken iwwer dat chinesescht Internet verdeelt; obwuel dacks bezuelte Datebanken %(icon)s Keng Metadaten-Dumps einfach zougänglech fir hir ganz Kollektioun verfügbar. %(icon)s Anna’s Archive verwalt eng Kollektioun vu <a %(duxiu)s>DuXiu Metadaten</a> Dateien %(icon)s Dateien nëmme fir eng limitéiert Zäit ze léinen, mat verschiddenen Zougangsbeschränkungen %(icon)s Annas Archiv verwalt eng Sammlung vu <a %(ia)s>IA Dateien</a> %(icon)s E puer Metadaten verfügbar duerch <a %(openlib)s>Open Library Datebank Dumps</a>, awer déi decken net déi ganz IA Sammlung %(icon)s Keng einfach zougänglech Metadaten Dumps verfügbar fir hir ganz Sammlung %(icon)s Annas Archiv verwalt eng Sammlung vu <a %(ia)s>IA Metadaten</a> Lescht aktualiséiert %(icon)s Annas Archiv an Libgen.li verwalten zesummen Kollektioune vu <a %(comics)s>Comicbicher</a>, <a %(magazines)s>Zäitschrëften</a>, <a %(standarts)s>Standarddokumenter</a>, an <a %(fiction)s>Fiktioun (ofgeleet vun Libgen.rs)</a>. %(icon)s Non-Fiction Torrents ginn mat Libgen.rs gedeelt (an gespigelt <a %(libgenli)s>hei</a>). %(icon)s Véiereljäerlech <a %(dbdumps)s>HTTP Datebank Dumps</a> %(icon)s Automatiséiert Torrents fir <a %(nonfiction)s>Non-Fiction</a> an <a %(fiction)s>Fiction</a> %(icon)s Annas Archiv verwalt eng Sammlung vu <a %(covers)s>Buchdeckel Torrents</a> %(icon)s All Dag <a %(dbdumps)s>HTTP Datebank Dumps</a> Metadaten %(icon)s Méintlech <a %(dbdumps)s>Datenbank-Dumps</a> %(icon)s Date Torrents verfügbar <a %(scihub1)s>hei</a>, <a %(scihub2)s>hei</a>, an <a %(libgenli)s>hei</a> %(icon)s E puer nei Dateien ginn <a %(libgenrs)s>derbäigesat</a> an de Libgen sengem "scimag", awer net genuch fir nei Torrents ze rechtfäerdegen. %(icon)s Sci-Hub huet zënter 2021 keng nei Dateien méi gefruer. %(icon)s Metadaten Dumps verfügbar <a %(scihub1)s>hei</a> an <a %(scihub2)s>hei</a>, souwéi als Deel vun der <a %(libgenli)s>Libgen.li Datebank</a> (déi mir benotzen) Quell %(icon)s Verschidde méi kleng oder eenzel Quelle. Mir encouragéieren d'Leit fir d'éischt an aner Schiedbibliothéiken eropzelueden, awer heiansdo hunn d'Leit Kollektiounen, déi ze grouss sinn fir anerer ze sortéieren, awer net grouss genuch fir eng eegen Kategorie ze verdéngen. %(icon)s Net direkt a Masse verfügbar, geschützt géint Scraping %(icon)s Anna’s Archive verwalt eng Kollektioun vu <a %(worldcat)s>OCLC (WorldCat) Metadaten</a> %(icon)s Annas Archiv an Z-Library verwalten zesummen eng Sammlung vu <a %(metadata)s>Z-Library Metadaten</a> an <a %(files)s>Z-Library Dateien</a>. Datasets Mir kombinéieren all déi uewe genannte Quellen an eng vereenegt Datebank, déi mir benotze fir dës Websäit ze bedéngen. Dës vereenegt Datebank ass net direkt verfügbar, awer well Anna’s Archive komplett Open Source ass, kann se relativ einfach <a %(a_generated)s>generéiert</a> oder <a %(a_downloaded)s>erofgeluede</a> ginn als ElasticSearch an MariaDB Datebanken. D'Skripter op där Säit lueden automatesch all déi néideg Metadaten vun de genannte Quellen erof. Wann Dir eis Donnéeë wëllt entdecken ier Dir dës Skripter lokal ausféiert, kënnt Dir eis JSON-Dateien kucken, déi weider op aner JSON-Dateien verlinken. <a %(a_json)s>Dës Datei</a> ass e gudde Startpunkt. Vereenegt Datebank Torrents vun Anna’s Archive duerchbliederen sichen Verschidde méi kleng oder eenzel Quelle. Mir encouragéieren d'Leit fir d'éischt an aner Schiedbibliothéiken eropzelueden, awer heiansdo hunn d'Leit Kollektiounen, déi ze grouss sinn fir anerer ze sortéieren, awer net grouss genuch fir eng eegen Kategorie ze verdéngen. Iwwersiicht vun der <a %(a1)s>Datasets Säit</a>. Vun <a %(a_href)s>aaaaarg.fail</a>. Schéngt zimlech komplett ze sinn. Vun eisem Fräiwëllege “cgiym”. Vun engem <a %(a_href)s><q>ACM Digital Library 2020</q></a> Torrent. Huet eng zimlech héich Iwwerlappung mat existente Pabeiersammlungen, awer ganz wéineg MD5-Matches, dofir hu mir decidéiert, se komplett ze behalen. Scrape vun <q>iRead eBooks</q> (= phonetesch <q>ai rit i-books</q>; airitibooks.com), vum Fräiwëllege <q>j</q>. Entsprécht <q>airitibooks</q> metadata an <a %(a1)s><q>Andere metadata scrapes</q></a>. Aus enger Kollektioun <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Deelweis vun der originaler Quell, deelweis vun the-eye.eu, deelweis vun anere Spigelen. Vun enger privater Bicher-Torrent-Websäit, <a %(a_href)s>Bibliotik</a> (oft als “Bib” bezeechent), vun där Bicher an Torrents no Numm gebündelt goufen (A.torrent, B.torrent) a verdeelt duerch the-eye.eu. Vun eisem Fräiwëllege “bpb9v”. Fir méi Informatiounen iwwer <a %(a_href)s>CADAL</a>, kuckt d'Notizen op eiser <a %(a_duxiu)s>DuXiu Datebank Säit</a>. Méi vun eisem Fräiwëllege “bpb9v”, haaptsächlech DuXiu-Dateien, souwéi e Verzeechnes “WenQu” an “SuperStar_Journals” (SuperStar ass d'Firma hannert DuXiu). Vun eisem Fräiwëllege “cgiym”, chinesesch Texter aus verschiddene Quellen (duerch Ënndirektorië representéiert), inklusiv vun <a %(a_href)s>China Machine Press</a> (e grousse chinesesche Verlag). Net-chinesesch Sammlungen (duerch Ënndirektorië representéiert) vun eisem Fräiwëllege “cgiym”. Scrape vu Bicher iwwer chinesesch Architektur, vum Fräiwëllege <q>cm</q>: <q>Ech hunn et kritt andeems ech eng Netzwierkschwächt an der Verlagshaus ausgenotzt hunn, mee déi Lück ass mëttlerweil zougemaach ginn</q>. Entsprécht <q>chinese_architecture</q> metadata an <a %(a1)s><q>Andere metadata scrapes</q></a>. Bicher vum akademesche Verlag <a %(a_href)s>De Gruyter</a>, gesammelt aus e puer grousse Torrents. Scrape vun <a %(a_href)s>docer.pl</a>, enger polnescher Dateie-Share-Websäit fokusséiert op Bicher an aner schrëftlech Wierker. Gescraped Enn 2023 vum Fräiwëllege “p”. Mir hunn net gutt Metadaten vun der originaler Websäit (net emol Dateiextensiounen), awer mir hunn no bicherähnlechen Dateien gefiltert a konnten dacks Metadaten aus den Dateien selwer extrahéieren. DuXiu epubs, direkt vun DuXiu, gesammelt vum Fräiwëllege “w”. Nëmmen rezent DuXiu Bicher sinn direkt duerch Ebooks verfügbar, also mussen déi meescht dovun rezent sinn. Reschtlech DuXiu-Dateien vum Fräiwëllege “m”, déi net am DuXiu proprietäre PDG-Format waren (déi Haapt <a %(a_href)s>DuXiu Datebank</a>). Gesammelt aus ville originalen Quellen, leider ouni dës Quellen am Dateipfad ze erhalen. <span></span> <span></span> <span></span> Scrape vun erotesche Bicher, vum Fräiwëllege <q>do no harm</q>. Entsprécht <q>hentai</q> metadata an <a %(a1)s><q>Andere metadata scrapes</q></a>. <span></span> <span></span> Sammlung gescraped vun engem japanesche Manga-Verlag vum Fräiwëllege “t”. <a %(a_href)s>Ausgewielte justiziell Archiven vu Longquan</a>, zur Verfügung gestallt vum Fräiwëllege “c”. Scrape vu <a %(a_href)s>magzdb.org</a>, en Alliéierte vu Library Genesis (et ass op der libgen.rs Homepage verlinkt) awer déi net hir Fichieren direkt zur Verfügung stelle wollten. Vun engem Fräiwëlleger “p” am spéiden 2023 kritt. <span></span> Verschidde kleng Eroplueden, ze kleng als eegen Ënnerkollektioun, awer als Dossieren representéiert. Ebooks vun AvaxHome, enger russescher Dateideelungswebsäit. Archiv vu Zeitungen a Magaziner. Entsprécht <q>newsarch_magz</q> metadata an <a %(a1)s><q>Andere metadata scrapes</q></a>. Scrape vum <a %(a1)s>Philosophy Documentation Center</a>. Kollektioun vu Fräiwëlleger "o" déi polnesch Bicher direkt vun den originalen Verëffentlechungs-Websäiten ("scene") gesammelt hunn. Kombinéiert Kollektiounen vun <a %(a_href)s>shuge.org</a> duerch Fräiwëlleger "cgiym" an "woz9ts". <span></span> <a %(a_href)s>“Imperial Library of Trantor”</a> (benannt no der fiktiver Bibliothéik), am Joer 2022 duerch Fräiwëlleger "t" gescraped. <span></span> <span></span> <span></span> Ënner-Ënner-Kollektiounen (duerch Dossieren representéiert) vum Fräiwëlleger "woz9ts": <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (vum <a %(a_sikuquanshu)s>Dizhi(迪志)</a> a Taiwan), mebook (mebook.cc, 我的小书屋, mäi klengt Bicherzëmmer — woz9ts: "Dës Säit konzentréiert sech haaptsächlech op d'Deelen vun héichqualitativen E-Book Dateien, vun deenen e puer vum Besëtzer selwer gesat goufen. De Besëtzer gouf <a %(a_arrested)s>verhaft</a> am Joer 2019 an een huet eng Kollektioun vun Dateien gemaach, déi hien gedeelt huet."). Reschtlech DuXiu Dateien vum Fräiwëlleger "woz9ts", déi net am DuXiu proprietären PDG Format waren (mussen nach an PDF konvertéiert ginn). D'“Upload” Kollektioun ass an méi kleng Ënnerkollektiounen opgedeelt, déi an den AACIDs an Torrent Nimm uginn sinn. All Ënnerkollektiounen goufen als éischt géint d'Haaptkollektioun deduplizéiert, obwuel d'Metadaten “upload_records” JSON Fichieren nach ëmmer vill Referenzen op déi originell Fichieren enthalen. Net-Buch Fichieren goufen och aus de meeschten Ënnerkollektiounen ewechgeholl, an sinn typesch <em>net</em> an den “upload_records” JSON notéiert. D'Ënnerkollektiounen sinn: Notizen Ënnerkollektioun Vill Ënnerkollektiounen bestinn selwer aus Ënner-Ënnerkollektiounen (z.B. vun verschiddenen originelle Quelle), déi als Dossieren an de “filepath” Felder representéiert sinn. Eroplueden op Anna’s Archive Eise Blogpost iwwer dës Donnéeën <a %(a_worldcat)s>WorldCat</a> ass eng proprietär Datebank vun der net-kommerzieller <a %(a_oclc)s>OCLC</a>, déi Metadaten-Opzeechnungen aus Bibliothéiken aus der ganzer Welt aggregéiert. Et ass wahrscheinlech déi gréisst Bibliothéik-Metadatensammlung op der Welt. Am Oktober 2023 hu mir en ëmfaassenden Scrape vun der OCLC (WorldCat) Datebank <a %(a_scrape)s>verëffentlecht</a>, am <a %(a_aac)s>Anna’s Archive Containers Format</a>. Oktober 2023, initial Verëffentlechung: OCLC (WorldCat) Torrents vun Anna’s Archive Beispillrekord op Anna’s Archive (original Kollektioun) Beispillrekord op Anna’s Archive ("zlib3" Kollektioun) Torrents vun Anna’s Archive (Metadaten + Inhalt) Blog Post iwwer Verëffentlechung 1 Blog Post iwwer Release 2 Géint Enn 2022 goufen déi angeblech Grënner vun Z-Library verhaft, an d'Domänen goufen duerch d'Autoritéiten vun den USA beschlagnahmt. Zënterhier huet d'Websäit lues a lues erëm online komm. Et ass onbekannt wien se aktuell bedreift. Update vum Februar 2023. Z-Library huet seng Wuerzelen an der <a %(a_href)s>Library Genesis</a> Gemeinschaft, an huet ursprénglech mat hiren Donnéeën ugefaangen. Zënterhier huet et sech wesentlech professionaliséiert an huet eng vill méi modern Interface. Si sinn dofir fäeg vill méi Donatioune ze kréien, souwuel finanziell fir hir Websäit weider ze verbesseren, wéi och Donatioune vun neie Bicher. Si hunn eng grouss Kollektioun nieft Library Genesis ugesammelt. D'Kollektioun besteet aus dräi Deeler. D'original Beschreiwungssäiten fir déi éischt zwee Deeler sinn hei ënnen erhalen. Dir braucht all dräi Deeler fir all Donnéeën ze kréien (ausser iwwerschratt Torrents, déi op der Torrents-Säit duerchgestrach sinn). %(title)s: eis éischt Verëffentlechung. Dëst war déi alleréischt Verëffentlechung vun deem wat deemools "Pirate Library Mirror" ("pilimi") genannt gouf. %(title)s: zweet Verëffentlechung, dës Kéier mat all Dateien an .tar Dateien agewéckelt. %(title)s: inkrementell nei Verëffentlechungen, mat dem <a %(a_href)s>Anna’s Archive Containers (AAC) Format</a>, elo a Kollaboratioun mat dem Z-Library Team verëffentlecht. Den initialen Spigel gouf mat vill Ustrengung iwwer d'Joeren 2021 an 2022 kritt. Zu dësem Zäitpunkt ass et e bësse veralt: et reflektéiert de Stand vun der Sammlung am Juni 2021. Mir wäerten dat an der Zukunft aktualiséieren. Elo konzentréiere mir eis drop, dës éischt Verëffentlechung erauszekréien. Well Library Genesis scho mat ëffentlechen Torrents erhalen ass, an an der Z-Library abegraff ass, hu mir eng Basis-Deduplikatioun géint Library Genesis am Juni 2022 gemaach. Fir dëst hu mir MD5 Hashes benotzt. Et ass wahrscheinlech datt et vill méi duplizéiert Inhalt an der Bibliothéik gëtt, wéi verschidde Dateiformater mam selwechte Buch. Dëst ass schwéier präzis ze detektéieren, also maachen mir et net. No der Deduplikatioun hu mir iwwer 2 Millioune Fichieren, déi insgesamt knapp ënner 7TB sinn. D'Sammlung besteet aus zwee Deeler: engem MySQL “.sql.gz” Dump vun de Metadaten, an de 72 Torrent Dateien vun ongeféier 50-100GB all. D'Metadaten enthalen d'Donnéeën wéi se vun der Z-Library Websäit gemellt goufen (Titel, Auteur, Beschreiwung, Dateityp), souwéi déi tatsächlech Dateigréisst an md5sum déi mir observéiert hunn, well heiansdo stëmmen dës net iwwereneen. Et schéngt, datt et Beräicher vu Fichieren ginn, fir déi d'Z-Library selwer falsch Metadaten huet. Mir kéinten och falsch erofgeluede Fichieren an e puer isoléierte Fäll hunn, déi mir an der Zukunft probéieren ze detektéieren an ze fixéieren. Déi grouss Torrent Dateien enthalen déi tatsächlech Buchdaten, mat der Z-Library ID als Dateinumm. D'Dateierweiterungen kënnen mat dem Metadaten Dump rekonstruéiert ginn. D'Sammlung ass eng Mëschung aus Non-Fiction an Fiction Inhalt (net getrennt wéi an Library Genesis). D'Qualitéit variéiert och staark. Dës éischt Verëffentlechung ass elo voll verfügbar. Notéiert datt d'Torrent Dateien nëmmen iwwer eisen Tor Spigel verfügbar sinn. Release 1 (%(date)s) Dëst ass eng eenzeg zousätzlech Torrent-Datei. Si enthält keng nei Informatiounen, mee et sinn Donnéeën dran, déi eng Zäit dauere kënnen, fir ze berechnen. Dat mécht et praktesch, se ze hunn, well den Download vun dësem Torrent oft méi séier ass wéi et vun Ufank un ze berechnen. Besonnesch enthält se SQLite Indexer fir d'Tar-Dateien, fir se mat <a %(a_href)s>ratarmount</a> ze benotzen. Release 2 Zousaz ( %(date)s ) Mir hunn all Bicher kritt, déi tëscht eisem leschte Spigel an August 2022 an d'Z-Library bäigefüügt goufen. Mir sinn och zréckgaangen an hunn e puer Bicher geschraapt, déi mir déi éischte Kéier verpasst hunn. Alles an allem ass dës nei Sammlung ongeféier 24TB. Erëm ass dës Sammlung géint Library Genesis deduplizéiert, well et scho Torrents fir déi Sammlung gëtt. D'Donnéeën sinn ähnlech wéi bei der éischter Verëffentlechung organiséiert. Et gëtt en MySQL “.sql.gz” Dump vun de Metadaten, déi och all d'Metadaten vun der éischter Verëffentlechung enthält, an domat iwwerschreift. Mir hunn och e puer nei Kolonnen derbäigesat: Mir hunn dat lescht Kéier erwähnt, awer just fir ze klären: “filename” an “md5” sinn déi tatsächlech Eegeschafte vun der Datei, wärend “filename_reported” an “md5_reported” dat sinn, wat mir vun Z-Library geschraapt hunn. Heiansdo stëmmen dës zwee net iwwereneen, also hu mir béid abegraff. Fir dës Verëffentlechung hu mir d'Kollatioun op “utf8mb4_unicode_ci” geännert, wat kompatibel mat eelere Versioune vu MySQL sollt sinn. D'Dateien sinn ähnlech wéi déi leschte Kéier, obwuel se vill méi grouss sinn. Mir konnten eis einfach net d'Méi maachen, Tonne vu méi klenge Torrent Dateien ze kreéieren. “pilimi-zlib2-0-14679999-extra.torrent” enthält all d'Dateien, déi mir an der leschter Verëffentlechung verpasst hunn, wärend déi aner Torrents all nei ID Beräicher sinn.  <strong>Update %(date)s:</strong> Mir hunn déi meescht vun eisen Torrents ze grouss gemaach, wat d'Torrent Clienten kämpfe gelooss huet. Mir hunn se ewechgeholl an nei Torrents verëffentlecht. <strong>Update %(date)s:</strong> Et waren nach ëmmer ze vill Dateien, also hu mir se an tar Dateien gewéckelt an nei Torrents erëm verëffentlecht. %(key)s: ob dës Datei scho bei Library Genesis ass, entweder an der Non-Fiction oder Fiction Sammlung (gematcht duerch md5). %(key)s: a wéi engem Torrent dës Datei ass. %(key)s: gesat wann mir net fäeg waren, d'Buch erofzelueden. Release 2 (%(date)s) Zlib Verëffentlechungen (original Beschreiwungssäiten) Tor Domain Haaptwebsäit Z-Library scrape D’“Chinese” Sammlung an der Z-Library schéngt déi selwecht ze sinn wéi eis DuXiu Sammlung, awer mat aneren MD5s. Mir schléissen dës Fichieren aus den Torrents aus fir Duplikater ze vermeiden, weisen se awer nach ëmmer an eisem Sichindex. Metadaten Dir kritt %(percentage)s%% Bonus séier Downloads, well Dir vum Benotzer %(profile_link)s referéiert gouf. Dëst gëllt fir déi ganz Memberschaftsperiod. Spenden Matmaachen Ausgewielt bis zu %(percentage)s%% Remisen Alipay ënnerstëtzt international Kredit-/Debitkaarten. Kuckt <a %(a_alipay)s>dëse Guide</a> fir méi Informatiounen. Schéckt eis Amazon.com Cadeaukaarten mat Ärer Kredit-/Debitkaart. Dir kënnt Krypto mat Kredit-/Debitkaarten kafen. WeChat (Weixin Pay) ënnerstëtzt international Kredit-/Debitkaarten. An der WeChat App, gitt op “Me => Services => Wallet => Add a Card”. Wann Dir dat net gesitt, aktivéiert et mat “Me => Settings => General => Tools => Weixin Pay => Enable”. (benotzt wann Dir Ethereum vu Coinbase schéckt) kopéiert! kopéieren (niddregst Mindestbetrag) (Warnung: héije Mindestbetrag) -%(percentage)s%% 12 Méint 1 Mount 24 Méint 3 Méint 48 Méint 6 Méint 96 Méint Wielt wéi laang Dir abonnéiere wëllt. <div %(div_monthly_cost)s></div><div %(div_after)s>no <span %(span_discount)s></span> Remise</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% fir 12 Méint fir 1 Mount fir 24 Méint fir 3 Méint fir 48 Méint fir 6 Méint fir 96 Méint %(monthly_cost)s / Mount kontaktéiert eis Direkt <strong>SFTP</strong> Serveren Spenden oder Austausch op Entreprise-Niveau fir nei Kollektiounen (z.B. nei Scans, OCR’ed Datasets). Expert Zougang <strong>Onlimitéiert</strong> Héich-Vitesse Zougang <div %(div_question)s>Kann ech meng Memberschaft upgraden oder méi Memberschaften kréien?</div> <div %(div_question)s>Kann ech eng Donatioun maachen ouni Member ze ginn?</div> Natierlech. Mir akzeptéieren Donatioune vun all Betrag op dëser Monero (XMR) Adress: %(address)s. <div %(div_question)s>Wat bedeiten d'Beräicher pro Mount?</div> Dir kënnt op déi ënnescht Säit vun engem Beräich kommen, andeems Dir all d'Reduktiounen uwend, wéi z.B. eng Period méi laang wéi ee Mount wielen. <div %(div_question)s>Ginn d'Memberschaften automatesch erneiert?</div> D'Memberschaften ginn <strong>net</strong> automatesch erneiert. Dir kënnt esou laang oder kuerz bäitrieden, wéi Dir wëllt. <div %(div_question)s>Woufir gitt Dir d'Donatioune aus?</div> 100%% ginn an d'Erhalen an d'Zougänglechmaache vum Wëssen a Kultur vun der Welt. Momentan gi mir se haaptsächlech fir Serveren, Späicher a Bandbreet aus. Kee Geld geet un iergendeng Team-Memberen perséinlech. <div %(div_question)s>Kann ech eng grouss Donatioun maachen?</div> Dat wier erstaunlech! Fir Donatioune vu méi wéi e puer dausend Dollar, kontaktéiert eis w.e.g. direkt op %(email)s. <div %(div_question)s>Hutt Dir aner Bezuelmethoden?</div> Momentan net. Vill Leit wëllen net, datt Archiver wéi dësen existéieren, dofir musse mir virsiichteg sinn. Wann Dir eis hëllefe kënnt, aner (méi praktesch) Bezuelmethoden sécher opzestellen, kontaktéiert eis w.e.g. op %(email)s. Donatioun FAQ Dir hutt eng <a %(a_donation)s>bestehend Donatioun</a> am Gaang. Schéckt w.e.g. dës Donatioun of oder annuléiert se, ier Dir eng nei Donatioun maacht. <a %(a_all_donations)s>All meng Donatioune weisen</a> Fir Spenden iwwer $5000 kontaktéiert eis w.e.g. direkt op %(email)s. Mir begréissen grouss Spenden vu räiche Persounen oder Institutiounen.  Sidd Dir bewosst, datt obwuel d'Memberschaften op dëser Säit "pro Mount" sinn, si si eemoleg Donatioune (net widderhuelend). Kuckt d'<a %(faq)s>Donatioun FAQ</a>. Anna’s Archive ass e Non-Profit, Open-Source, Open-Data Projet. Andeems Dir spendt an e Member gitt, ënnerstëtzt Dir eis Operatiounen an Entwécklung. Un all eis Memberen: Merci datt Dir eis um Lafen haalt! ❤️ Fir méi Informatiounen, kuckt w.e.g. an d’ <a %(a_donate)s>Donatioun FAQ</a>. Fir Member ze ginn, <a %(a_login)s>loggt Iech an oder registréiert Iech</a> w.e.g.. Merci fir Är Ënnerstëtzung! $%(cost)s / Mount Wann Dir e Feeler während der Bezuelung gemaach hutt, kënne mir keng Remboursementer maachen, mee mir probéieren et richteg ze maachen. Fannt d'“Krypto” Säit an Ärer PayPal App oder Websäit. Dëst ass typesch ënner “Finanzen”. Gitt op d'“Bitcoin” Säit an Ärer PayPal App oder Websäit. Dréckt op de “Transfer” Knäppchen %(transfer_icon)s, an dann “Senden”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Cadeaukaart %(amazon)s Kaddokaart Bankkaart Bankkaart (mat App) Binance Kredit-/Debitkaart/Apple/Google (BMC) Cash App Kredit-/Debitkaart Kredit-/Debitkaart 2 Kredit-/Debitkaart (Backup) Krypto %(bitcoin_icon)s Kaart / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regulär) Pix (Brazil) Revolut (temporär net verfügbar) WeChat Wielt Är bevorzugte Krypto-Mënz: Spenden mat enger Amazon Cadeaukaart. <strong>WICHTEG:</strong> Dës Optioun ass fir %(amazon)s. Wann Dir eng aner Amazon Websäit benotze wëllt, wielt se uewen aus. <strong>WICHTEG:</strong> Mir ënnerstëtzen nëmmen Amazon.com, net aner Amazon Websäiten. Zum Beispill, .de, .co.uk, .ca, ginn NET ënnerstëtzt. Schreift w.e.g. keng eegen Noriicht. Gitt de geneeën Betrag an: %(amount)s Notéiert datt mir op Montanten opronde mussen, déi vun eise Reseller akzeptéiert ginn (Minimum %(minimum)s). Spenden mat enger Kredit-/Debitkaart, iwwer d'Alipay App (super einfach opzestellen). Installéiert d'Alipay App aus dem <a %(a_app_store)s>Apple App Store</a> oder <a %(a_play_store)s>Google Play Store</a>. Registréiert Iech mat Ärer Telefonsnummer. Keng weider perséinlech Detailer sinn erfuerderlech. <span %(style)s>1</span>Installéiert d'Alipay App Ënnerstëtzt: Visa, MasterCard, JCB, Diners Club an Discover. Kuckt <a %(a_alipay)s>dëse Guide</a> fir méi Informatiounen. <span %(style)s>2</span>Füügt Bankkaart derbäi Mat Binance kënnt Dir Bitcoin mat enger Kredit-/Debitkaart oder engem Bankkonto kafen an dann dës Bitcoin un eis spenden. Sou kënne mir sécher a anonym bleiwen wann mir Är Spenden akzeptéieren. Binance ass an bal all Land verfügbar a ënnerstëtzt déi meescht Banken a Kredit-/Debitkaarten. Dëst ass aktuell eis Haaptempfehlung. Mir schätzen et, datt Dir Iech Zäit hëlt fir ze léieren wéi Dir mat dëser Method spende kënnt, well et eis vill hëlleft. Fir Kreditkaarten, Debitkaarten, Apple Pay, a Google Pay, benotze mir “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). An hirem System ass eng “coffee” gläich $5, also gëtt Är Spende op de nächste Multiple vu 5 opgeronnt. Spenden mat Cash App. Wann Dir Cash App hutt, ass dat déi einfachst Manéier ze spenden! Notéiert datt fir Transaktiounen ënner %(amount)s, Cash App e %(fee)s Käschte berechnen kann. Fir %(amount)s oder méi, ass et gratis! Spenden mat enger Kredit- oder Debitkaart. Dës Method benotzt e Kryptowährungsprovider als Zwëschenkonversioun. Dëst kann e bëssen duerchernee sinn, also benotzt dës Method nëmmen wann aner Bezuelmethoden net funktionéieren. Et funktionéiert och net an all Länner. Mir kënnen net direkt Kredit-/Debitkaarten ënnerstëtzen, well Banken net mat eis wëlle schaffen. ☹ Allerdéngs ginn et verschidde Weeër fir Kredit-/Debitkaarten ze benotzen, andeems aner Bezuelmethoden benotzt ginn: Mat Krypto kënnt Dir mat BTC, ETH, XMR an SOL spenden. Benotzt dës Optioun, wann Dir scho mat Kryptowärungen vertraut sidd. Mat Krypto kënnt Dir mat BTC, ETH, XMR an aneren spenden. Krypto Express Servicer Wann Dir fir d'éischt Kéier Krypto benotzt, proposéiere mir %(options)s fir Bitcoin (déi originell an am meeschte benotzt Kryptowärung) ze kafen an ze spenden. Notéiert datt fir kleng Spenden d'Kreditkaartkäschten eise %(discount)s%% Rabatt eliminéiere kënnen, dofir recommandéiere mir méi laang Abonnementer. Spenden mat Kredit-/Debitkaart, PayPal oder Venmo. Dir kënnt tëscht dësen op der nächster Säit wielen. Google Pay an Apple Pay kéinten och funktionéieren. Notéiert datt fir kleng Spenden d'Käschten héich sinn, dofir recommandéiere mir méi laang Abonnementer. Fir mat PayPal US ze spenden, benotze mir PayPal Crypto, wat eis erlaabt anonym ze bleiwen. Mir schätzen et, datt Dir Iech Zäit hëlt fir ze léieren, wéi Dir mat dëser Method spende kënnt, well et eis vill hëlleft. Spenden mat PayPal. Spenden mat Ärem reguläre PayPal-Kont. Spenden mat Revolut. Wann Dir Revolut hutt, ass dat déi einfachst Manéier ze spenden! Dës Bezuelmethod erlaabt nëmmen e Maximum vun %(amount)s. Wielt w.e.g. eng aner Dauer oder Bezuelmethod. Dës Bezuelmethod erfuerdert e Minimum vun %(amount)s. Wielt w.e.g. eng aner Dauer oder Bezuelmethod. Binance Coinbase Kraken Wielt w.e.g. eng Bezuelmethod. “Adoptéiert e Torrent”: Äre Benotzernumm oder Message an engem Torrent Dateinumm <div %(div_months)s>all 12 Méint Memberschaft</div> Äre Benotzernumm oder anonym Nennung an de Credits Fréizäitegen Zougang zu neie Funktiounen Exklusiven Telegram mat Updates hannert de Kulissen %(number)s séier Downloads pro Dag wann Dir dëse Mount spend! <a %(a_api)s>JSON API</a> Zougang Legendären Status an der Erhalung vum Wëssen a Kultur vun der Mënschheet Vireg Virdeeler, plus: Verdéngt <strong>%(percentage)s%% Bonus Downloads</strong> andeems Dir <a %(a_refer)s>Frënn referréiert</a>. SciDB Pabeieren <strong>onlimitéiert</strong> ouni Verifikatioun Wann Dir Froen zu Ärem Kont oder Spenden hutt, füügt Är Kont-ID, Screenshots, Quittungen, sou vill Informatioun wéi méiglech bäi. Mir kontrolléieren eis E-Mail nëmmen all 1-2 Wochen, also wäert d'Net-Abannen vun dëser Informatioun all Léisung verspéiten. Fir nach méi Downloads ze kréien, <a %(a_refer)s>referréiert Är Frënn</a>! Mir sinn e klengt Team vu Fräiwëlleger. Et kann 1-2 Wochen daueren, bis mir äntweren. Notéiert datt den Numm vum Kont oder d'Bild komesch ausgesi kënnen. Keng Suergen! Dës Konten ginn vun eise Spendenpartner verwalt. Eis Konten goufen net gehackt. Spenden <span %(span_cost)s></span> <span %(span_label)s></span> fir 12 Méint “%(tier_name)s” fir 1 Mount “%(tier_name)s” fir 24 Méint “%(tier_name)s” fir 3 Méint “%(tier_name)s” fir 48 Méint “%(tier_name)s” fir 6 Méint “%(tier_name)s” fir 96 Méint “%(tier_name)s” Dir kënnt d’Spende nach während dem Keesprozess annuléieren. Klickt op de Spenden-Knäppche fir dës Spende ze bestätegen. <strong>Wichteg Notiz:</strong> Krypto Präisser kënnen extrem variéieren, heiansdo souguer ëm 20%% an e puer Minutten. Dat ass nach ëmmer manner wéi d'Fraisen, déi mir mat ville Bezuelungsanbieter hunn, déi dacks 50-60%% fir d'Zesummenaarbecht mat enger "Schiedwohltätegkeet" wéi eis berechnen. <u>Wann Dir eis d'Recetten mat dem urspréngleche Präis schéckt, deen Dir bezuelt hutt, kreditéiere mir Äre Kont nach ëmmer fir d'gewielte Memberschaft</u> (soulaang d'Recetten net méi al wéi e puer Stonnen ass). Mir schätzen et wierklech, datt Dir bereet sidd, esou Saachen ze toleréieren fir eis z'ënnerstëtzen! ❤️ ❌ Eppes ass schif gaangen. Luet d’Säit nei a probéiert et nach eng Kéier. <span %(span_circle)s>1</span>Kaaft Bitcoin op Paypal <span %(span_circle)s>2</span>Transferéiert de Bitcoin op eis Adress ✅ Ëmleedung op d'Spendesäit… Spenden Waart w.e.g. op d'mannst <span %(span_hours)s>24 Stonnen</span> (a lued dës Säit nei) ier Dir eis kontaktéiert. Wann Dir eng Spende maache wëllt (all Betrag) ouni Memberschaft, kënnt Dir dës Monero (XMR) Adress benotzen: %(address)s. Nodeems Dir Äre Kaddoscheck geschéckt hutt, bestätegt eist automatiséiert System dësen bannent e puer Minutten. Wann dat net funktionéiert, probéiert Äre Kaddoscheck nach eng Kéier ze schécken (<a %(a_instr)s>Instruktiounen</a>). Wann dat nach ëmmer net funktionéiert, schéckt eis w.e.g. eng E-Mail an d'Anna wäert et manuell iwwerpréiwen (dëst kann e puer Deeg daueren), a gitt sécher unzeginn ob Dir scho probéiert hutt nach eng Kéier ze schécken. Beispill: Benotzt w.e.g. de <a %(a_form)s>offizielle Formulaire vun Amazon.com</a> fir eis e Kaddoscheck vu %(amount)s un d'E-Mail Adress hei drënner ze schécken. „To“ Empfänger E-Mail am Formulaire: Amazon Kaddoscheck Mir kënnen aner Methode vu Kaddoschecken net akzeptéieren, <strong>nëmmen direkt vum offizielle Formulaire op Amazon.com geschéckt</strong>. Mir kënnen Äre Kaddoscheck net zréckginn, wann Dir dëse Formulaire net benotzt. Nëmmen eemol benotzen. Eenzegaarteg fir Äre Kont, net deelen. Waarden op de Kaddoscheck… (aktualiséiert d’Säit fir ze kontrolléieren) Maacht d'<a %(a_href)s>QR-Code Donatiounssäit</a> op. Scannt de QR Code mat der Alipay App, oder dréckt de Knäppchen fir d'Alipay App opzemaachen. W.e.g. Gedold; d'Säit kann e bëssen daueren fir ze lueden, well se a China ass. <span %(style)s>3</span>Maacht Donatioun (scannt QR Code oder dréckt Knäppchen) Kaaft PYUSD Mënz op PayPal Kaaft Bitcoin (BTC) op Cash App Kaaft e bëssen méi (mir recommandéieren %(more)s méi) wéi de Betrag deen Dir spenden (%(amount)s), fir d’Transaktiounsfraise ze decken. Dir behält alles wat iwwreg bleift. Gitt op d’Säit “Bitcoin” (BTC) am Cash App. Transfertéiert de Bitcoin op eis Adress Fir kleng Donen (ënner $25), musst Dir vläicht Rush oder Priority benotzen. Klickt op de Knäppchen “Send bitcoin” fir eng “Réckzuch” ze maachen. Wiesselt vun Dollar op BTC andeems Dir op den %(icon)s Ikon dréckt. Gitt de BTC Betrag hei drënner an a klickt op “Send”. Kuckt <a %(help_video)s>dëst Video</a> wann Dir festhänkt. Express Servicer si praktesch, awer verrechnen méi héich Fraisen. Dir kënnt dëst amplaz vun engem Krypto Austausch benotzen wann Dir séier eng méi grouss Donatioun maache wëllt an Iech net un enger Frais vun $5-10 stéiert. Sidd sécher, de genee Krypto Betrag ze schécken, deen op der Donatiounssäit ugewise gëtt, net de Betrag an $USD. Soss gëtt d'Frais ofgezunn an mir kënnen Är Memberschaft net automatesch verschaffen. Heiansdo kann d'Bestätegung bis zu 24 Stonnen daueren, also gitt sécher dës Säit ze aktualiséieren (och wann se ofgelaf ass). Instruktiounen fir Kredit- / Debitkaart Spenden iwwer eis Kredit- / Debitkaart Säit E puer vun de Schrëtt erwähnen Krypto-Wallets, maacht Iech keng Suergen, Dir musst näischt iwwer Krypto léieren fir dëst. %(coin_name)s Instruktiounen Scan dësen QR Code mat Ärer Krypto Portemonnaie App fëllt d'Bezuelungsdetailer aus Scan QR Code fir ze bezuelen Mir ënnerstëtzen nëmmen déi Standard Versioun vu Krypto Mënzen, keng exotesch Netzwierker oder Versiounen vu Mënzen. Et kann bis zu enger Stonn daueren fir d'Transaktioun ze bestätegen, ofhängeg vun der Mënz. Spenden %(amount)s op <a %(a_page)s>dës Säit</a>. Dës Don ass ofgelaf. Annuléiert a maacht eng nei. Wann Dir scho bezuelt hutt: Jo, ech hunn mäi Rezept per E-Mail geschéckt Wann de Krypto-Wechselkurs während der Transaktioun geschwankt huet, gitt sécher d'Rezept mat dem originalen Wechselkurs ze inkludéieren. Mir schätzen et wierklech, datt Dir d'Méi maacht Krypto ze benotzen, et hëlleft eis vill! ❌ Eppes ass schif gaangen. Lued w.e.g. d'Säit nei a probéiert et nach eng Kéier. <span %(span_circle)s>%(circle_number)s</span>Schéckt eis d'Rezept per E-Mail Wann Dir op Problemer stéisst, kontaktéiert eis w.e.g. op %(email)s a gitt sou vill Informatioun wéi méiglech un (wéi Screenshots). ✅ Merci fir Är Spende! Anna wäert Är Memberschaft bannent e puer Deeg manuell aktivéieren. Schéckt d'Rezept oder e Screenshot op Är perséinlech Verifikatiounsadress: Wann Dir d'Rezept per E-Mail geschéckt hutt, klickt op dëse Knäppchen, sou datt Anna et manuell iwwerpréiwe kann (dëst kann e puer Deeg daueren): Schéckt eng Quittung oder Screenshot un Är perséinlech Verifikatiounsadress. Benotzt dës E-Mail Adress NET fir Är PayPal-Spende. Ofbriechen Jo, w.e.g. ofbriechen Sidd Dir sécher, datt Dir ofbrieche wëllt? Net ofbriechen, wann Dir scho bezuelt hutt. ❌ Eppes ass schif gaangen. Lued d'Säit nei a probéiert et nach eng Kéier. Maacht eng nei Don ✅ Är Don ass ofgebrach ginn. Datum: %(date)s Identifizéierer: %(id)s Nei bestellen Status: <span %(span_label)s>%(label)s</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Mount fir %(duration)s Méint, inklusiv %(discounts)s%% Rabatt)</span> Total: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / Mount fir %(duration)s Méint)</span> 1. Gitt Är E-Mail an. 2. Wielt Är Bezuelmethod. 3. Wielt Är Bezuelmethod nach eng Kéier. 4. Wielt "Self-hosted" Wallet. 5. Klickt "Ech bestätegen d'Besëtzung". 6. Dir sollt e Rezept per E-Mail kréien. Schéckt dat w.e.g. un eis, a mir bestätegen Är Spende sou séier wéi méiglech. (Dir kënnt wëlle stornéieren an eng nei Spende erstellen) D'Bezuelinstruktiounen sinn elo veralt. Wann Dir eng aner Don maache wëllt, benotzt de "Nei bestellen" Knäppchen uewen. Dir hutt schonn bezuelt. Wann Dir d'Bezuelinstruktiounen nach emol iwwerpréiwe wëllt, klickt hei: Al Bezuelinstruktiounen weisen Wann d'Donatiounssäit blockéiert gëtt, probéiert eng aner Internetverbindung (z.B. VPN oder Telefoninternet). Leider ass d'Alipay Säit dacks nëmmen aus <strong>Festland China</strong> zougänglech. Dir musst eventuell Äre VPN temporär desaktivéieren, oder e VPN op Festland China benotzen (oder Hong Kong funktionéiert och heiansdo). <span %(span_circle)s>1</span>Spenden op Alipay Spenden den Totalbetrag vu %(total)s mat <a %(a_account)s>dësem Alipay Kont</a> Alipay Instruktiounen <span %(span_circle)s>1</span>Transferéiert op ee vun eise Krypto Konten Spende den Totalbetrag vun %(total)s op eng vun dësen Adressen: Krypto Instruktiounen Follegt den Instruktiounen fir Bitcoin (BTC) ze kafen. Dir braucht nëmmen de Betrag ze kafen, deen Dir spende wëllt, %(total)s. Gitt eis Bitcoin (BTC) Adress als Empfänger an, a befollegt d'Instruktiounen fir Är Spende vu %(total)s ze schécken: <span %(span_circle)s>1</span>Spenden op Pix Spenden den Totalbetrag vun %(total)s mat <a %(a_account)s>dësem Pix Konto Pix Instruktiounen <span %(span_circle)s>1</span>Spenden op WeChat Spenden den Totalbetrag vu %(total)s mat <a %(a_account)s>dësem WeChat Kont</a> WeChat Instruktiounen Benotzt eng vun de folgende “Kreditkaart zu Bitcoin” Express Servicer, déi nëmmen e puer Minutten daueren: BTC / Bitcoin Adress (extern Portemonnaie): BTC / Bitcoin Betrag: Fëllt w.e.g. déi folgend Detailer am Formulaire aus: Wann dës Informatioun net méi aktuell ass, schéckt eis w.e.g. eng E-Mail fir eis Bescheed ze soen. Benotzt w.e.g. dëse <span %(underline)s>genauen Betrag</span>. Är total Käschte kéinten méi héich sinn wéinst Kreditkaartgebühren. Fir kleng Beträg kéint dëst leider méi héich sinn wéi eise Rabatt. (minimum: %(minimum)s, keng Verifikatioun fir déi éischt Transaktioun) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, keng Verifikatioun fir déi éischt Transaktioun) (minimum: %(minimum)s) (minimum: %(minimum)s ofhängeg vum Land, keng Verifikatioun fir déi éischt Transaktioun) Befollegt d'Instruktiounen fir PYUSD Mënz (PayPal USD) ze kafen. Kaaft e bëssen méi (mir recommandéieren %(more)s méi) wéi de Betrag deen Dir spenden ( %(amount)s ), fir Transaktiounsfraise ze decken. Dir behält alles wat iwwreg bleift. Gitt op d'“PYUSD” Säit an Ärer PayPal App oder Websäit. Dréckt op de “Transfer” Knäppchen %(icon)s, an dann “Send”. Status aktualiséieren Fir den Timer zréckzesetzen, erstellt einfach eng nei Spende. Sidd Dir sécher, datt Dir de BTC-Betrag hei drënner benotzt, <em>NET</em> Euro oder Dollar, soss kréie mir net de richtege Betrag a kënne Är Memberschaft net automatesch bestätegen. Kaaft Bitcoin (BTC) op Revolut Kaaft e bëssen méi (mir recommandéieren %(more)s méi) wéi de Betrag deen Dir spenden (%(amount)s), fir d’Transaktiounsfraise ze decken. Dir behält alles wat iwwreg bleift. Gitt op d’Säit “Crypto” am Revolut fir Bitcoin (BTC) ze kafen. Transfertéiert de Bitcoin op eis Adress Fir kleng Donen (ënner $25) musst Dir vläicht Rush oder Priority benotzen. Klickt op de Knäppchen “Send bitcoin” fir eng “Réckzuch” ze maachen. Wiesselt vun Euro op BTC andeems Dir op den %(icon)s Ikon dréckt. Gitt de BTC Betrag hei drënner an a klickt op “Send”. Kuckt <a %(help_video)s>dëst Video</a> wann Dir festhänkt. Status: 1 2 Schrëtt-fir-Schrëtt Guide Kuckt de Schrëtt-fir-Schrëtt Guide hei drënner. Soss kënnt Dir aus dësem Kont ausgespaart ginn! Wann Dir et nach net gemaach hutt, notéiert Äre geheime Schlëssel fir Iech anzeschreiwen: Merci fir Är Don! Zäit déi nach bleift: Spende Transferéiert %(amount)s un %(account)s Waarden op Bestätegung (aktualiséiert d’Säit fir ze kontrolléieren)… Waarden op Transfer (aktualiséiert d’Säit fir ze kontrolléieren)… Virdrun Séier Downloads an de leschten 24 Stonnen zielen zum deegleche Limit. Eroflueden vun de séiere Partner Servere sinn markéiert mat %(icon)s. Lescht 18 Stonnen Nach keng Dateien erofgelueden. Erofgelueden Dateien ginn net ëffentlech gewisen. All Zäiten sinn an UTC. Erofgelueden Dateien Wann Dir eng Datei mat béide séieren an luesen Downloads erofgelueden hutt, gëtt se zweemol ugewise. Maacht Iech keng Suergen, et ginn vill Leit, déi vun de Websäiten eroflueden, déi mir verlinken, an et ass extrem seelen, datt een a Problemer geréit. Fir sécher ze bleiwen, empfeele mir e VPN (bezuelt) oder <a %(a_tor)s>Tor</a> (gratis) ze benotzen. Ech hunn 1984 vum George Orwell erofgelueden, wäert d'Police bei mir doheem kommen? Dir sidd Anna! Wien ass Anna? Mir hunn eng stabil JSON API fir Memberen, fir eng séier Download-URL ze kréien: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (Dokumentatioun bannent dem JSON selwer). Fir aner Benotzungsfäll, wéi d'Duerchfuere vun all eise Fichieren, d'Erstelle vun enger personaliséierter Sich, asw., empfeele mir <a %(a_generate)s>d'Generéiere</a> oder <a %(a_download)s>d'Erofluede</a> vun eisen ElasticSearch an MariaDB Datebanken. D'Rohdaten kënnen manuell <a %(a_explore)s>duerch JSON-Fichieren</a> exploréiert ginn. Eis Lëscht vu Roh-Torrents kann och als <a %(a_torrents)s>JSON</a> erofgeluede ginn. Hutt Dir eng API? Mir hosten hei keng copyright-geschützt Material. Mir sinn eng Sichmaschinn, a mir indexéieren nëmmen Metadaten, déi scho ëffentlech verfügbar sinn. Wann Dir vun dësen externe Quellen erofluet, proposéiere mir Iech, d'Gesetzer an Ärer Juridictioun ze iwwerpréiwen, wat erlaabt ass. Mir sinn net verantwortlech fir den Inhalt, deen anerer hosten. Wann Dir Reklamatiounen iwwer dat hutt, wat Dir hei gesitt, ass et am beschten, de urspréngleche Site ze kontaktéieren. Mir zéien hir Ännerungen regelméisseg an eis Datebank. Wann Dir wierklech mengt, datt Dir eng valabel DMCA-Reklamatioun hutt, op déi mir reagéiere sollen, fëllt w.e.g. de <a %(a_copyright)s>DMCA / Copyright-Reklamatiounsformular</a> aus. Mir huelen Är Reklamatiounen eescht a wäerten esou séier wéi méiglech zréckkommen. Wéi kann ech eng Copyright-Verletzung mellen? Hei sinn e puer Bicher, déi eng besonnesch Bedeitung fir d'Welt vun de Schiedbibliothéiken an der digitaler Erhaalung hunn: Wat sinn Är Lieblingsbicher? Mir wëllen och jiddereen drun erënneren, datt all eis Code an Daten komplett open source sinn. Dëst ass eenzegaarteg fir Projeten wéi eisen — mir sinn net bewosst vun engem anere Projet mat engem ähnlech massiven Katalog, deen och voll open source ass. Mir begréissen jiddereen, deen mengt, datt mir eise Projet schlecht lafen, fir eisen Code an Daten ze huelen an hir eege Schiedbibliothéik opzestellen! Mir soen dat net aus Trotz oder sou eppes — mir mengen wierklech, datt dëst genial wier, well et d'Bar fir jiddereen héich setzt an d'Vermächtnis vun der Mënschheet besser erhalen. Ech haassen, wéi Dir dëse Projet leeft! Mir géifen et gär gesinn, wann d'Leit <a %(a_mirrors)s>Spigelen</a> opstellen, a mir wäerten dat finanziell ënnerstëtzen. Wéi kann ech hëllefen? Jo, dat maachen mir. Eis Inspiratioun fir Metadaten ze sammelen ass d'Zil vum Aaron Swartz vun "eng Websäit fir all Buch dat jeemools publizéiert gouf", fir déi hien <a %(a_openlib)s>Open Library</a> erstallt huet. Dëst Projet huet gutt gemaach, awer eis eenzegaarteg Positioun erlaabt eis Metadaten ze kréien, déi si net kënnen. Eng aner Inspiratioun war eise Wonsch ze wëssen <a %(a_blog)s>wéi vill Bicher et op der Welt ginn</a>, sou datt mir berechnen kënnen, wéi vill Bicher mir nach retten mussen. Sammelt Dir Metadaten? Notéiert datt mhut.org bestëmmte IP-Bereicher blockéiert, dofir kéint e VPN néideg sinn. <strong>Android:</strong> Klickt op d'Dréi-Punkt-Menü uewen riets, an wielt "Op Startbildschierm derbäisetzen". <strong>iOS:</strong> Klickt op de "Deelen" Knäppchen ënnen, an wielt "Op Startbildschierm derbäisetzen". Mir hunn keng offiziell mobil App, mee Dir kënnt dës Websäit als App installéieren. Hutt Dir eng mobil App? Schéckt se w.e.g. un den <a %(a_archive)s>Internet Archive</a>. Si wäerten se richteg erhalen. Wéi kann ech Bicher oder aner physesch Materialien spenden? Wéi froen ech Bicher un? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — reegelméisseg Updates <a %(a_software)s>Anna’s Software</a> — eisen Open Source Code <a %(a_datasets)s>Datasets</a> — iwwer d'Donnéeën <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternativ Domänen Ginn et méi Ressourcen iwwer Anna’s Archive? <a %(a_translate)s>Iwwersetzt op Anna’s Software</a> — eisen Iwwersetzungssystem <a %(a_wikipedia)s>Wikipedia</a> — méi iwwer eis (hëlleft w.e.g. dës Säit aktuell ze halen, oder erstellt eng an Ärer eegener Sprooch!) Wielt d'Astellungen déi Dir gär hutt, loosst d'Sichfeld eidel, klickt op "Sichen", an dann späichert d'Säit als Lesezeechen mat der Lesezeechen-Funktioun vun Ärem Browser. Wéi späicheren ech meng Sichastellungen? Mir begréissen Sécherheetsfuerscher fir Schwachstelle an eise Systemer ze sichen. Mir sinn grouss Verfechter vun der verantwortlecher Offenbarung. Kontaktéiert eis <a %(a_contact)s>hei</a>. Mir sinn de Moment net fäeg Bug Bounties unzebidden, ausser fir Schwachstelle, déi d'<a %(a_link)s>Potenzial hunn eis Anonymitéit ze kompromittéieren</a>, fir déi mir Bounties am Beräich vun $10k-50k ubidden. Mir géifen an der Zukunft gär e méi breede Spektrum fir Bug Bounties ubidden! Notéiert w.e.g. datt sozial Ingenieursattacken ausserhalb vum Beräich sinn. Wann Dir un offensiver Sécherheet interesséiert sidd, an hëllefe wëllt d'Wëssen an d'Kultur vun der Welt ze archivéieren, gitt sécher eis ze kontaktéieren. Et ginn vill Weeër, wéi Dir hëllefe kënnt. Hutt Dir e Programm fir verantwortlech Offenbarung? Mir hunn net genuch Ressourcen, fir jidderengem op der Welt héich-Vitesse Downloads ze ginn, och wann mir dat gär géifen. Wann e räiche Benefactor eis dat géif erméiglechen, wier dat fantastesch, mee bis dohin maache mir eist Bescht. Mir sinn e Non-Profit-Projet, dee sech kaum duerch Donen erhale kann. Dofir hu mir zwee Systemer fir gratis Downloads mat eise Partneren ëmgesat: gedeelt Servere mat luesen Downloads, an e bëssen méi séier Servere mat enger Waardelëscht (fir d'Zuel vun de Leit, déi gläichzäiteg eroflueden, ze reduzéieren). Mir hunn och <a %(a_verification)s>Browser-Verifikatioun</a> fir eis lues Downloads, well soss Bots a Scrapers se mëssbrauchen, wat d'Saache fir legitim Benotzer nach méi lues mécht. Notéiert datt Dir, wann Dir de Tor Browser benotzt, eventuell Är Sécherheetseinstellungen upasse musst. Op der niddregster Optioun, genannt “Standard”, klappt d'Cloudflare Turnstile Erausfuerderung. Op den hécheren Optiounen, genannt “Sécher” an “Am Séchersten”, klappt d'Erausfuerderung net. Fir grouss Dateien kënnen heiansdo lues Downloads an der Mëtt briechen. Mir recommandéieren e Download-Manager (wéi JDownloader) ze benotzen fir grouss Downloads automatesch weiderzefueren. Firwat sinn d'langsam Downloads esou lues? Dacks gestallte Froen (FAQ) Benotzt de <a %(a_list)s>Torrent-Lëscht-Generator</a> fir eng Lëscht vun Torrents ze generéieren, déi am meeschten Ufro hunn, bannent Äre Späicherplatzlimiten. Jo, kuckt op der <a %(a_llm)s>LLM Daten</a> Säit. Déi meescht Torrents enthalen d'Fichieren direkt, wat bedeit datt Dir Torrent-Clienten uweise kënnt, nëmmen déi erfuerderlech Fichieren erofzelueden. Fir ze bestëmmen, wéi eng Fichieren erofzelueden sinn, kënnt Dir <a %(a_generate)s>eis Metadaten generéieren</a>, oder <a %(a_download)s>eis ElasticSearch an MariaDB Datenbanken eroflueden</a>. Leider enthalen e puer Torrent-Kollektiounen .zip oder .tar Fichieren an der Root, an dësem Fall musst Dir den ganzen Torrent eroflueden, ier Dir individuell Fichieren auswiele kënnt. Et gi nach keng einfach ze benotzen Tools fir d'Torrents ze filteren, mee mir begréissen Bäiträg. (Mir hunn awer <a %(a_ideas)s>e puer Iddien</a> fir de leschte Fall.) Laang Äntwert: Kuerz Äntwert: net einfach. Mir probéieren d'Duplikatioun oder Iwwerlappung tëscht den Torrents op dëser Lëscht minimal ze halen, mee dat kann net ëmmer erreecht ginn an hänkt staark vun de Richtlinne vun de Quellbibliothéiken of. Fir Bibliothéiken, déi hir eege Torrents erausginn, ass et ausserhalb vun eiser Kontroll. Fir Torrents, déi vun Anna’s Archive verëffentlecht ginn, deduplizéiere mir nëmmen op Basis vum MD5 Hash, wat bedeit datt verschidde Versioune vum selwechte Buch net deduplizéiert ginn. Jo. Dëst sinn tatsächlech PDFs an EPUBs, si hunn just keng Extensioun an villen vun eisen Torrents. Et ginn zwou Plazen, wou Dir d'Metadaten fir Torrent-Fichieren fannt, inklusiv d'Fichier-Typen/Extensiounen: 1. All Kollektioun oder Verëffentlechung huet seng eege Metadaten. Zum Beispill, <a %(a_libgen_nonfic)s>Libgen.rs Torrents</a> hunn eng entspriechend Metadaten-Datenbank op der Libgen.rs Websäit. Mir verlinken typesch op relevant Metadaten-Ressourcen vun all Kollektioun senger <a %(a_datasets)s>Dataset-Säit</a>. 2. Mir recommandéieren <a %(a_generate)s>eis ElasticSearch an MariaDB Datenbanken ze generéieren</a> oder <a %(a_download)s>erofzelueden</a>. Dës enthalen eng Zuweisung fir all Opzeechnung an Anna’s Archive zu hiren entspriechenden Torrent-Fichieren (wann verfügbar), ënner "torrent_paths" am ElasticSearch JSON. E puer Torrent-Clients ënnerstëtzen keng grouss Stéckgréissten, déi vill vun eisen Torrents hunn (fir déi nei maachen mir dat net méi — och wann et laut Spezifikatioune gëlteg ass!). Probéiert e anere Client wann Dir dëst Problem hutt, oder beschwéiert Iech bei den Hiersteller vun Ärem Torrent-Client. Ech géif gär hëllefen ze seeden, mee ech hunn net vill Späicherplatz. D'Torrents sinn ze lues; kann ech d'Donnéeën direkt vun Iech eroflueden? Kann ech nëmmen en Deel vun de Fichieren eroflueden, wéi nëmmen eng bestëmmte Sprooch oder Thema? Wéi handhabt Dir Duplikater an den Torrents? Kann ech d'Torrent-Lëscht als JSON kréien? Ech gesinn keng PDFs oder EPUBs an den Torrents, nëmmen binär Fichieren? Wat soll ech maachen? Firwat kann mäi Torrent-Client net e puer vun ären Torrent-Dateien / Magnet-Links opmaachen? Torrents FAQ Wéi lueden ech nei Bicher erop? Kuckt w.e.g. <a %(a_href)s>dëst exzellent Projet</a>. Hutt Dir en Uptime-Monitor? Wat ass Anna’s Archive? Gitt Member fir séier Downloads ze benotzen. Mir ënnerstëtzen elo Amazon Cadeaukaarten, Kredit- a Bankkaarten, Krypto, Alipay, an WeChat. Dir hutt haut keng séier Downloads méi. Zougang Stonnendownloads an de leschten 30 Deeg. Stonnenmoyenne: %(hourly)s. Dagesmoyenne: %(daily)s. Mir schaffen mat Partner zesummen, fir eis Sammlungen einfach a fräi zougänglech fir jiddereen ze maachen. Mir gleewen, datt jiddereen e Recht op d'kollektiv Wëssen vun der Mënschheet huet. An <a %(a_search)s>net op Käschte vun den Auteuren</a>. D'Datasets, déi an Annas Archiv benotzt ginn, sinn komplett oppe a kënnen a Masse mat Torrents gespigelt ginn. <a %(a_datasets)s>Léiert méi…</a> Langzäitarchiv Voll Datebank Sichen Bicher, Pabeieren, Zäitschrëften, Comics, Bibliothéikrecords, Metadaten, … All eise <a %(a_code)s>Code</a> an <a %(a_datasets)s>Donnéeën</a> sinn komplett Open Source. <span %(span_anna)s>Anna’s Archive</span> ass e non-profit Projet mat zwou Ziler: <li><strong>Erhalen:</strong> All Wëssen a Kultur vun der Mënschheet ze sécheren.</li><li><strong>Zougang:</strong> Dëst Wëssen a Kultur fir jiddereen op der Welt zougänglech ze maachen.</li> Mir hunn déi weltgréisst Sammlung vu qualitativ héichwäertegen Textdaten. <a %(a_llm)s>Léiert méi…</a> LLM Training Daten 🪩 Spigelen: Opruff fir Fräiwëlleger Wann Dir e héichrisiko anonyme Bezuelprozessor bedreiwt, kontaktéiert eis w.e.g. Mir sichen och Leit, déi kleng, geschmackvoll Annoncen plazéiere wëllen. All Erléis geet un eis Erhalungsbeméiungen. Erhalen Mir schätzen, datt mir ongeféier <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% vun de Bicher op der Welt geséchert hunn</a>. Mir sécheren Bicher, Pabeieren, Comics, Zäitschrëften a méi, andeems mir dës Materialien aus verschiddene <a href="https://en.wikipedia.org/wiki/Shadow_library">Schiedbibliothéiken</a>, offiziellen Bibliothéiken an anere Sammlungen op enger Plaz zesummebréngen. All dës Donnéeën ginn fir ëmmer geséchert, andeems et einfach gemaach gëtt, se a grousse Quantitéiten ze duplizéieren — mat Torrents — wat zu ville Kopien ronderëm d'Welt féiert. E puer Schiedbibliothéiken maachen dat schonn selwer (z.B. Sci-Hub, Library Genesis), während Anna’s Archive aner Bibliothéiken "befreit", déi keng Bulk-Verdeelung ubidden (z.B. Z-Library) oder guer keng Schiedbibliothéiken sinn (z.B. Internet Archive, DuXiu). Dës breet Verdeelung, kombinéiert mat Open-Source Code, mécht eis Websäit resistent géint Ofschaltungen a séchert d'laangfristeg Erhalen vum Wëssen a Kultur vun der Mënschheet. Léiert méi iwwer <a href="/datasets">eis Donnéeësets</a>. Wann Dir e <a %(a_member)s>Member</a> sidd, ass keng Browserverifikatioun erfuerderlech. 🧬&nbsp;SciDB ass eng Fortsetzung vu Sci-Hub. SciDB Op DOI Sci-Hub huet <a %(a_paused)s>d'Uploaden</a> vun neie Pabeieren gestoppt. Direkten Zougang zu %(count)s akademesche Pabeieren 🧬&nbsp;SciDB ass eng Fortsetzung vu Sci-Hub, mat sengem vertraute Interface an direkter Vue op PDFs. Gitt Ären DOI an fir ze kucken. Mir hunn déi komplett Sci-Hub Sammlung, souwéi nei Pabeieren. Déi meescht kënnen direkt mat engem vertrauten Interface gekuckt ginn, ähnlech wéi Sci-Hub. E puer kënnen iwwer extern Quellen erofgeluede ginn, an deem Fall weisen mir Linken dozou. Dir kënnt enorm hëllefen andeems Dir Torrents seed. <a %(a_torrents)s>Léiert méi…</a> >%(count)s Seederen <%(count)s Seederen %(count_min)s–%(count_max)s Seederen 🤝 Fräiwëlleger gesicht Als Non-Profit, Open-Source Projet, si mir ëmmer op der Sich no Leit, déi hëllefe wëllen. IPFS Downloads Lëscht vun %(by)s, erstallt <span %(span_time)s>%(time)s</span> Späicheren ❌ Eppes ass schif gaangen. Probéiert et nach eng Kéier. ✅ Gespäichert. Lued w.e.g. d’Säit nei. Lëscht ass eidel. änneren Füügt Dateien an dës Lëscht bäi oder ewech andeems Dir eng Datei fannt an den Tab “Lëschten” opmaacht. Lëscht Wéi mir hëllefe kënnen Iwwerlappung ewechhuelen (Deduplizéierung) Text- a Metadatenextraktioun OCR Mir kënnen héich-Vitesse Zougang zu eise komplette Sammlungen ubidden, souwéi zu net verëffentlechte Sammlungen. Dëst ass Zougang op Entreprise-Niveau, deen mir fir Donatiounen am Beräich vun Zéngdausende USD ubidden kënnen. Mir sinn och bereet dëst géint héichqualitativ Sammlungen ze tauschen, déi mir nach net hunn. Mir kënnen Iech rembourséieren wann Dir eis mat der Beräicherung vun eisen Daten hëllefe kënnt, wéi zum Beispill: Ënnerstëtzt d'laangfristeg Archivéierung vum mënschleche Wëssen, wärend Dir besser Daten fir Äre Modell kritt! <a %(a_contact)s>Kontaktéiert eis</a> fir ze diskutéieren wéi mir zesummeschaffe kënnen. Et ass gutt verstanen datt LLMs op héichqualitativ Daten thrive. Mir hunn déi gréisste Sammlung vu Bicher, Pabeieren, Zäitschrëften, asw. op der Welt, déi e puer vun den héchste Qualitéitstextquellen sinn. LLM Daten Eenzegaarteg Skala a Reechwäit Eis Sammlung enthält iwwer honnert Millioune Dateien, inklusiv akademesch Zäitschrëften, Léierbicher, a Magaziner. Mir erreechen dës Skala duerch d'Kombinatioun vu grousse bestehende Repositiounen. E puer vun eise Quellsammlungen sinn scho bulk verfügbar (Sci-Hub, an Deeler vu Libgen). Aner Quelle hu mir selwer befreit. <a %(a_datasets)s>Datasets</a> weist en detailléierten Iwwerbléck. Eis Sammlung enthält Millioune vu Bicher, Pabeieren, a Magaziner aus der Zäit virun der E-Book Ära. Grouss Deeler vun dëser Sammlung sinn scho OCRéiert, a hunn scho wéineg intern Iwwerlappung. Weider Wann Dir Äre Schlëssel verluer hutt, <a %(a_contact)s>kontaktéiert eis</a> a gitt sou vill Informatioun wéi méiglech un. Dir musst eventuell temporär e neie Kont erstellen, fir eis ze kontaktéieren. W.e.g. <a %(a_account)s>loggt Iech an</a> fir dës Säit ze gesinn.</a> Fir ze verhënneren, datt Spam-Bots vill Konten erstellen, musse mir Äre Browser als éischt verifizéieren. Wann Dir an enger onendlecher Schleif hänke bleift, empfeele mir d'Installatioun vum <a %(a_privacypass)s>Privacy Pass</a>. Et kann och hëllefen, Annonceblocker an aner Browser-Erweiderungen auszeschalten. Aloggen / Registréieren Anna’s Archive ass temporär fir Ënnerhalt erof. Kommt w.e.g. an enger Stonn zréck. Alternativen Auteur Alternativ Beschreiwung Alternativ Editioun Alternativ Extensioun Alternativen Dateinumm Alternativen Verlag Alternativen Titel Datum open source gemaach Méi liesen… Beschreiwung Sich no Anna’s Archive no CADAL SSNO Nummer Sich no Anna’s Archive no DuXiu SSID Nummer Sich no Anna’s Archive no DuXiu DXID Nummer Sich op Anna’s Archive no ISBN Sich no Anna’s Archive no OCLC (WorldCat) Nummer Sich no Anna’s Archive no Open Library ID Anna’s Archive Online Viewer %(count)s betraffe Säiten Nom Eroflueden: Eng besser Versioun vun dësem Fichier kéint verfügbar sinn op %(link)s Bulk Torrent Downloads Kollektioun Benotzt Online-Tools, fir tëschent Formater ze konvertéieren. Empfohlene Konversiounsinstrumenter: %(links)s Fir grouss Dateien empfeele mir e Download-Manager ze benotzen, fir Ënnerbriechungen ze verhënneren. Empfohlene Download-Manager: %(links)s EBSCOhost eBook Index (nëmme fir Experten) (och op "GET" uewen klicken) (klickt op "GET" uewen) Extern Downloads <strong>🚀 Schnell Downloads</strong> Dir hutt nach %(remaining)s haut. Merci datt Dir Member sidd! ❤️ <strong>🚀 Schnell Downloads</strong> Dir sinn d'Fast Downloads fir haut ausgaang. <strong>🚀 Schnell Downloads</strong> Du hues dës Datei viru kuerzem erofgelueden. Linken bleiwen eng Zäit laang valabel. <strong>🚀 Schnell Downloads</strong> Gitt e <a %(a_membership)s>Member</a> fir d'laangfristeg Erhaalung vu Bicher, Pabeieren a méi z'ënnerstëtzen. Fir eis Dankbarkeet fir Är Ënnerstëtzung ze weisen, kritt Dir schnell Downloads. ❤️ 🚀 Schnell Downloads 🐢 Lues Downloads Ausléinen vum Internet Archive IPFS Gateway #%(num)d (et kann néideg sinn, méi Versich mat IPFS ze maachen) Libgen.li Libgen.rs Fiktioun Libgen.rs Non-Fiktioun hir Annoncen si bekannt dofir, schiedlech Software ze enthalen, benotzt also en Annonceblocker oder klickt net op Annoncen Amazon säi „Send to Kindle“ djazz säi „Send to Kobo/Kindle“ MagzDB ManualsLib Nexus/STC (Nexus/STC Dateien kënnen onzouverlässeg sinn fir erofzelueden) Keng Downloads fonnt. All Download-Optiounen hunn déi selwecht Datei a solle sécher sinn ze benotzen. Dat gesot, sidd ëmmer virsiichteg wann Dir Dateien vum Internet erofluet, besonnesch vu Siten ausserhalb vun Anna’s Archive. Zum Beispill, gitt sécher datt Är Geräter aktualiséiert sinn. (keen Umleedung) An eisem Viewer opmaachen (am Viewer opmaachen) Optioun #%(num)d: %(link)s %(extra)s Fannt den originalen Dossier am CADAL Sich manuell op DuXiu Fannt den originalen Dossier an ISBNdb Fannt den originalen Dossier am WorldCat Fannt den originalen Dossier an der Open Library Sich an verschiddenen aneren Datebanken no ISBN (nëmme fir Benotzer mat Dréckbehënnerung) PubMed Dir braucht en eBook- oder PDF-Reader, fir d'Datei opzemaachen, ofhängeg vum Dateiformat. Empfohlene eBook-Reader: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (assoziéierten DOI kéint net am Sci-Hub verfügbar sinn) Dir kënnt souwuel PDF- wéi och EPUB-Dateien op Äre Kindle oder Kobo eReader schécken. Empfohlene Tools: %(links)s Méi Informatiounen am <a %(a_slow)s>FAQ</a>. Ënnerstëtzt Auteuren an Bibliothéiken Wann Dir dëst gär hutt a leeschte kënnt, betruecht d'Original ze kafen oder d'Auteuren direkt z'ënnerstëtzen. Wann dëst an Ärer lokaler Bibliothéik verfügbar ass, betruecht et do gratis auszeborgen. Partner Server Downloads temporär net verfügbar fir dës Datei. Torrent Vun vertraute Partneren. Z-Library Z-Library op Tor (erfuerdert den Tor Browser) extern Downloads weisen <span class="font-bold">❌ Dëse Fichier kéint Problemer hunn a gouf aus enger Quellbibliothéik verstoppt.</span> Heiansdo ass dat op Ufro vun engem Copyright-Inhaber, heiansdo well eng besser Alternativ verfügbar ass, awer heiansdo wéinst engem Problem mam Fichier selwer. Et kéint nach ëmmer okay sinn, en erofzelueden, awer mir recommandéieren als éischt no enger alternativer Datei ze sichen. Méi Detailer: Wann Dir dëse Fichier nach ëmmer erofluede wëllt, gitt sécher nëmmen vertrauenswierdeg, aktualiséiert Software ze benotzen fir en opzemaachen. Metadaten-Kommentaren AA: Sich op Anna’s Archive no “%(name)s” Codes Explorer: An Codes Explorer uweisen “%(name)s” URL: Websäit: Wann Dir dës Datei hutt an se nach net an Anna’s Archive verfügbar ass, betruecht <a %(a_request)s>se eropzelueden</a>. Internet Archive Controlled Digital Lending Datei “%(id)s” Dëst ass en Dossier vun enger Datei vum Internet Archive, net eng direkt erofluedbar Datei. Dir kënnt probéieren d'Buch ze léinen (Link hei ënnen), oder benotzt dës URL wann Dir <a %(a_request)s>eng Datei ufrot</a>. Metadaten verbesseren CADAL SSNO %(id)s Metadaten Dossier Dëst ass e Metadaten-Datensatz, keen erofluedbare Fichier. Dir kënnt dës URL benotzen wann Dir <a %(a_request)s>e Fichier ufrot</a>. DuXiu SSID %(id)s Metadaten Dossier ISBNdb %(id)s Metadaten Dossier MagzDB ID %(id)s Metadatenrekord Nexus/STC ID %(id)s Metadatenrekord OCLC (WorldCat) Nummer %(id)s Metadaten Dossier Open Library %(id)s Metadaten Dossier Sci-Hub Datei “%(id)s” Net fonnt “%(md5_input)s” gouf net an eiser Datebank fonnt. Kommentar derbäisetzen (%(count)s) Dir kënnt den md5 vun der URL kréien, z.B. MD5 vun enger besserer Versioun vun dëser Datei (wann zougänglech). Fëllt dëst aus wann et eng aner Datei gëtt déi dës Datei no kënnt (selwecht Editioun, selwecht Dateierweiterung wann Dir eng fannt), déi d'Leit amplaz vun dëser Datei benotze sollten. Wann Dir vun enger besserer Versioun vun dëser Datei ausserhalb vun Anna’s Archive wësst, da w.e.g. <a %(a_upload)s>luet se erop</a>. Eppes ass schif gaangen. Luet d'Säit nei a probéiert et nach eng Kéier. Dir hutt e Kommentar hannerlooss. Et kann e Moment daueren, bis en ugewise gëtt. W.e.g. benotzt de <a %(a_copyright)s>DMCA / Copyright Reklamatiounsformular</a>. Beschreift d'Problematik (erfuerderlech) Wann dës Datei eng super Qualitéit huet, kënnt Dir alles doriwwer hei diskutéieren! Wann net, benotzt w.e.g. de Knäppchen "Dateiproblem mellen". Exzellent Dateiqualitéit (%(count)s) Dateiqualitéit Léiert wéi Dir <a %(a_metadata)s>d'Metadaten fir dës Datei selwer verbessere kënnt</a>. Problembeschreiwung W.e.g. <a %(a_login)s>loggt Iech an</a>. Ech hu dëst Buch gär! Hëlleft der Gemeinschaft andeems Dir d'Qualitéit vun dëser Datei mellt! 🙌 Eppes ass schif gaangen. Luet d'Säit nei a probéiert et nach eng Kéier. Dateiproblem mellen (%(count)s) Merci fir Äre Rapport anzereechen. En gëtt op dëser Säit gewisen an och manuell vun der Anna iwwerpréift (bis mir e richtege Moderatiounssystem hunn). Kommentar hannerloossen Rapport ofginn Wat ass mat dëser Datei falsch? Ausléinen (%(count)s) Kommentaren (%(count)s) Eroflueden (%(count)s) Metadaten entdecken (%(count)s) Lëschten (%(count)s) Statistiken (%(count)s) Fir Informatiounen iwwer dës spezifesch Datei, kuckt op hir <a %(a_href)s>JSON Datei</a>. Dëst ass eng Datei, déi vun der <a %(a_ia)s>IA’s Controlled Digital Lending</a> Bibliothéik verwalt gëtt, an vun der Anna’s Archive fir d'Sich indexéiert ass. Fir Informatiounen iwwer déi verschidden Datasets, déi mir zesummegestallt hunn, kuckt op d'<a %(a_datasets)s>Datasets Säit</a>. Metadaten aus verlinktem Dossier Metadaten op der Open Library verbesseren En "Datei MD5" ass en Hash, deen aus dem Dateiinhalt berechent gëtt, an ass vernünfteg eenzegaarteg baséiert op deem Inhalt. All Schiedbibliothéiken, déi mir hei indexéiert hunn, benotzen haaptsächlech MD5s fir Dateien z'identifizéieren. Eng Datei kann a multiple Schiedbibliothéiken optrieden. Fir Informatiounen iwwer déi verschidden Datasets, déi mir zesummegestallt hunn, kuckt op d'<a %(a_datasets)s>Datasets Säit</a>. Dateiqualitéit mellen Total Downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tschechesch Metadaten %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Warnung: méi verlinkt Dossieren: Wann Dir e Buch op Anna’s Archive kuckt, kënnt Dir verschidde Felder gesinn: Titel, Auteur, Verlag, Editioun, Joer, Beschreiwung, Dateinumm, an méi. All dës Informatiounsstécker ginn <em>Metadaten</em> genannt. Well mir Bicher aus verschiddene <em>Quellbibliothéiken</em> kombinéieren, weisen mir all Metadaten, déi an där Quellbibliothéik verfügbar sinn. Zum Beispill, fir e Buch, dat mir vun Library Genesis kruten, weisen mir den Titel aus der Library Genesis Datebank. Heiansdo ass e Buch an <em>multiple</em> Quellbibliothéiken präsent, déi eventuell verschidde Metadatenfelder hunn. An deem Fall weisen mir einfach déi längsten Versioun vun all Feld, well déi hoffentlech déi nëtzlechst Informatioun enthält! Mir weisen nach ëmmer déi aner Felder ënnert der Beschreiwung, z.B. als "alternativen Titel" (awer nëmmen wann se anescht sinn). Mir extrahéieren och <em>Coden</em> wéi Identifizéierer a Klassifizéierer aus der Quellbibliothéik. <em>Identifizéierer</em> representéieren eng spezifesch Editioun vun engem Buch; Beispiller sinn ISBN, DOI, Open Library ID, Google Books ID, oder Amazon ID. <em>Klassifizéierer</em> gruppéieren ähnlech Bicher zesummen; Beispiller sinn Dewey Decimal (DCC), UDC, LCC, RVK, oder GOST. Heiansdo sinn dës Coden explizit an de Quellbibliothéiken verlinkt, an heiansdo kënne mir se aus dem Dateinumm oder der Beschreiwung extrahéieren (haaptsächlech ISBN an DOI). Mir kënnen Identifizéierer benotzen, fir Records an <em>Metadaten-nëmmen Sammlungen</em> ze fannen, wéi OpenLibrary, ISBNdb, oder WorldCat/OCLC. Et gëtt en spezifeschen <em>Metadaten Tab</em> an eiser Sichmaschinn, wann Dir dës Sammlunge wëllt duerchsichen. Mir benotzen passend Records, fir fehlend Metadatenfelder auszefëllen (z.B. wann en Titel feelt), oder z.B. als "alternativen Titel" (wann et en existente Titel gëtt). Fir genee ze gesinn, wou d'Metadaten vun engem Buch hierkommen, kuckt den <em>“Technesch Detailer” Tab</em> op enger Buchsäit. Et huet e Link op de roude JSON fir dat Buch, mat Hiweiser op de roude JSON vun den originelle Records. Fir méi Informatiounen, kuckt déi folgend Säiten: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Sich (Metadaten Tab)</a>, <a %(a_codes)s>Codes Explorer</a>, an <a %(a_example)s>Beispill Metadaten JSON</a>. Schlussendlech kënnen all eis Metadaten als <a %(a_generated)s>ElasticSearch</a> an <a %(a_downloaded)s>MariaDB</a> Datebanken generéiert oder erofgeluede ginn. Hannergrond Dir kënnt hëllefen d'Erhale vu Bicher ze ënnerstëtzen andeems Dir Metadaten verbessert! Liest als éischt den Hannergrond iwwer Metadaten op Anna’s Archive, a léiert dann, wéi Dir Metadaten duerch Verlinkung mat der Open Library verbessere kënnt, a kritt eng gratis Memberschaft op Anna’s Archive. Metadaten verbesseren Also, wann Dir eng Datei mat schlechten Metadaten begéint, wéi sollt Dir se fixéieren? Dir kënnt op d'Quellbibliothéik goen an hir Prozeduren fir Metadaten ze fixéieren verfollegen, awer wat maache wann eng Datei a multiple Quellbibliothéiken präsent ass? Et gëtt een Identifizéierer, deen op Anna’s Archive speziell behandelt gëtt. <strong>D'anna’s_archive md5 Feld op Open Library iwwerschreift ëmmer all aner Metadaten!</strong> Loosst eis e bësse zréckgoen an iwwer Open Library léieren. Open Library gouf 2006 vum Aaron Swartz gegrënnt mam Zil "eng Websäit fir all Buch, dat jeemools publizéiert gouf". Et ass eng Aart Wikipedia fir Buchmetadaten: jidderee kann et änneren, et ass fräi lizenzéiert, a kann a Masse erofgeluede ginn. Et ass eng Buchdatebank, déi am meeschten mat eiser Missioun ausgeriicht ass — tatsächlech gouf Anna’s Archive vum Aaron Swartz senger Visioun a sengem Liewen inspiréiert. Anstatt d'Rad nei ze erfannen, hu mir decidéiert eis Fräiwëlleger op d'Open Library ëmzeleeden. Wann Dir e Buch gesitt, dat falsch Metadaten huet, kënnt Dir op déi folgend Manéier hëllefen: Notéiert datt dëst nëmme fir Bicher funktionéiert, net fir akademesch Aarbechten oder aner Aarte vu Dateien. Fir aner Aarte vu Dateien empfeele mir nach ëmmer d’Quellbibliothéik ze fannen. Et kann e puer Wochen daueren, bis d’Ännerungen an Anna’s Archive abegraff sinn, well mir mussen den neisten Open Library Daten Dump eroflueden an eisen Sichindex nei generéieren.  Gitt op d' <a %(a_openlib)s>Open Library Websäit</a>. Fannt de richtege Buchrecord. <strong>WARNUNG:</strong> sidd sécher, déi richteg <strong>Editioun</strong> ze wielen. An der Open Library hutt Dir "Wierker" an "Editiounen". E "Wierk" kéint "Harry Potter and the Philosopher's Stone" sinn. Eng "Editioun" kéint sinn: Déi éischt Editioun vun 1997, publizéiert vu Bloomsbery mat 256 Säiten. Déi 2003 Paperback Editioun, publizéiert vu Raincoast Books mat 223 Säiten. Déi polnesch Iwwersetzung vun 2000 “Harry Potter I Kamie Filozoficzn” vu Media Rodzina mat 328 Säiten. All dës Editiounen hunn ënnerschiddlech ISBNen an ënnerschiddlechen Inhalt, also gitt sécher, datt Dir déi richteg auswielt! Editéiert de Rekord (oder erstellt en, wann et keen gëtt), a füügt esou vill nëtzlech Informatiounen wéi méiglech derbäi! Dir sidd elo hei, also maacht de Rekord wierklech erstaunlech. Ënnert “ID Numbers” wielt “Anna’s Archive” a füügt den MD5 vum Buch vun Anna’s Archive derbäi. Dëst ass déi laang String vu Buschtawen a Zuelen no “/md5/” an der URL. Probéiert aner Dateien an Anna’s Archive ze fannen, déi och mat dësem Rekord passen, a füügt déi och derbäi. An der Zukunft kënne mir dës als Duplikater op der Anna’s Archive Sichsäit gruppéieren. Wann Dir fäerdeg sidd, schreift d’URL op, déi Dir just aktualiséiert hutt. Wann Dir op d’mannst 30 Rekorder mat Anna’s Archive MD5en aktualiséiert hutt, schéckt eis eng <a %(a_contact)s>E-Mail</a> a schéckt eis d’Lëscht. Mir ginn Iech eng gratis Memberschaft fir Anna’s Archive, sou datt Dir dës Aarbecht méi einfach maache kënnt (an als Merci fir Är Hëllef). Dëst mussen héichqualitativ Editiounen sinn, déi substantiell Informatiounen derbäi ginn, soss gëtt Är Ufro ofgeleent. Är Ufro gëtt och ofgeleent, wann eng vun den Editiounen vun den Open Library Moderatoren zréckgesat oder korrigéiert gëtt. Open Library Verlinkung Wann Dir wesentlech an d’Entwécklung an d’Operatiounen vun eiser Aarbecht involvéiert gitt, kënne mir diskutéieren, méi vun den Donatiounsakommes mat Iech ze deelen, fir datt Dir se no Bedarf asetzen kënnt. Mir bezuelen nëmmen fir Hosting, wann Dir alles ageriicht hutt, an gewisen hutt, datt Dir fäeg sidd, den Archiv mat Updates aktuell ze halen. Dëst bedeit, datt Dir déi éischt 1-2 Méint aus Ärer eegener Täsch bezuele musst. Är Zäit gëtt net kompenséiert (an eis och net), well dëst reng Fräiwëllegenaarbecht ass. Mir si bereet, Hosting- a VPN-Käschten ze decken, ufanks bis zu $200 pro Mount. Dëst ass genuch fir e Basis-Sichserver an e DMCA-geschützte Proxy. Hostingkäschten W.e.g. <strong>kontaktéiert eis net</strong> fir Erlaabnis ze froen oder fir Basisfroen. Aktiounen schwätze méi haart wéi Wierder! All d’Informatioun ass dobaussen, also maacht einfach weider mat der Astellung vun Ärem Spigel. Fëllt Iech fräi, Ticketen oder Merge-Requests op eisem Gitlab ze posten, wann Dir op Problemer stéisst. Mir mussen eventuell e puer spigel-spezifesch Funktiounen mat Iech bauen, wéi z.B. d’Rebranding vun “Anna’s Archive” op Ären Websäitennumm, (ufanks) d’Desaktivéierung vun de Benotzerkonten, oder d’Verlinkung zréck op eis Haaptsäit vun de Bicher-Säiten. Soubal Dir äre Spigel leeft, kontaktéiert eis w.e.g. Mir géifen gär Är OpSec iwwerpréiwen, an wann déi fest ass, verlinke mir op Äre Spigel a fänken un méi enk mat Iech zesummen ze schaffen. Villmools Merci am Viraus un all déi, déi bereet sinn, op dës Manéier bäizedroen! Et ass net fir déi mat schwaachem Häerz, mee et géif d'Liewensdauer vun der gréisster wierklech oppener Bibliothéik an der Mënschheetsgeschicht festegen. Ufank Fir d'Resilienz vum Anna’s Archive ze erhéijen, sichen mir Fräiwëlleger fir Spigelen ze bedreiwen. Är Versioun ass kloer als Spigel markéiert, z.B. “Bob’s Archive, e Spigel vun Anna’s Archive”. Dir sidd bereet, d’Risiken, déi mat dëser Aarbecht verbonne sinn, ze huelen, déi si wesentlech. Dir hutt e grëndlecht Verständnis vun der néideger operationeller Sécherheet. D’Inhalter vun <a %(a_shadow)s>dësen</a> <a %(a_pirate)s>Posts</a> si fir Iech selbstverständlech. Ufanks ginn mir Iech keen Zougang zu eise Partner-Server-Downloads, mee wann alles gutt geet, kënne mir dat mat Iech deelen. Dir bedreiwt de Codebase vun Anna’s Archive als Open Source, a aktualiséiert regelméisseg souwuel de Code wéi och d’Daten. Dir sidd bereet, zu eiser <a %(a_codebase)s>Codebase</a> bäizedroen — an Zesummenaarbecht mat eisem Team — fir dëst z'erreechen. Mir sichen no dësem: Spigelen: Opruff fir Fräiwëlleger Eng aner Donatioun maachen. Nach keng Donatiounen. <a %(a_donate)s>Meng éischt Donatioun maachen.</a> Spenden Detailer ginn net ëffentlech gewisen. Meng Spenden 📡 Fir bulk Spigelung vun eiser Sammlung, kuckt op d' <a %(a_datasets)s>Datasets</a> an <a %(a_torrents)s>Torrents</a> Säiten. Erofluede vun Ärer IP Adress an de leschten 24 Stonnen: %(count)s. 🚀 Fir méi séier Downloads a fir d'Browserkontrollen ze iwwersprangen, <a %(a_membership)s>gitt Member</a>. Eroflueden vun der Partnerwebsäit Fillt Iech fräi, Anna’s Archive an engem aneren Tab weider ze duerchsichen wärend Dir waart (wann Äre Browser d'Aktualiséierung vu Hannergrondtabs ënnerstëtzt). Fillt Iech fräi, op méi Erofluedsäiten gläichzäiteg ze waarden (awer w.e.g. nëmmen eng Datei gläichzäiteg pro Server eroflueden). Wann Dir en Erofluedlink kritt, ass en e puer Stonnen gëlteg. Merci fir d'Waarden, dëst hält d'Websäit gratis fir jiddereen zougänglech! 😊 🔗 All Downloadlinken fir dës Datei: <a %(a_main)s>Haaptsäit vun der Datei</a>. ❌ Lues Eroflueden sinn net iwwer Cloudflare VPNs oder soss vu Cloudflare IP Adressen verfügbar. ❌ Lues Eroflueden sinn nëmmen iwwer déi offiziell Websäit verfügbar. Besicht %(websites)s. 📚 Benotzt déi folgend URL fir erofzelueden: <a %(a_download)s>Elo eroflueden</a>. Fir jidderengem d'Méiglechkeet ze ginn, Dateien gratis erofzelueden, musst Dir waarden, ier Dir dës Datei erofluede kënnt. W.e.g. waart <span %(span_countdown)s>%(wait_seconds)s</span> Sekonnen fir dës Datei erofzelueden. Warnung: et goufen vill Erofluede vun Ärer IP Adress an de leschten 24 Stonnen. Erofluede kéinte méi lues sinn wéi gewinnt. Wann Dir e VPN, eng gedeelt Internetverbindung benotzt, oder Ären ISP IPs deelt, kéint dës Warnung doduerch verursaacht ginn. Späicheren ❌ Eppes ass schif gaangen. Probéiert et nach eng Kéier. ✅ Gespäichert. Lued w.e.g. d’Säit nei. Ännert Ären Affichnumm. Ären Identifizéierer (den Deel no “#”) kann net geännert ginn. Profil erstallt <span %(span_time)s>%(time)s</span> änneren Lëschten Erstellt eng nei Lëscht andeems Dir eng Datei fannt an den Tab "Lëschten" opmaacht. Keng Lëschten Profil net fonnt. Profil De Moment kënne mir keng Buchufroen erfëllen. Schéckt eis keng E-Mail mat Äre Buchufroen. Maacht w.e.g. Är Ufroen op de Foren vu Z-Library oder Libgen. Rekord an Annas Archiv DOI: %(doi)s Eroflueden SciDB Nexus/STC Keng Virschau verfügbar. Eroflueden Datei vun <a %(a_path)s>Annas Archiv</a>. Fir d'Zougänglechkeet an d'längerfristeg Erhaalung vum mënschleche Wëssen z'ënnerstëtzen, gitt <a %(a_donate)s>Member</a>. Als Bonus lued 🧬&nbsp;SciDB méi séier fir Memberen, ouni Aschränkungen. Net funktionnéiert? Probéiert <a %(a_refresh)s>nei lueden</a>. Sci-Hub Spezifescht Sichfeld derbäisetzen Sich Beschreiwungen a Metadaten Kommentaren Joer publizéiert Fortgeschratt Zougang Inhalt Weisen Lëscht Tabell Dateityp Sprooch Sortéieren no Gréissten Am relevantsten Neisten (Dateigréisst) (Open Source) (Publikatiounsjoer) Eelsten Zoufälleg Klengsten Quell geschraapt an open-source gemaach vun AA Digital Auslounen (%(count)s) Zäitschrëftartikelen (%(count)s) Mir hunn Treffere fonnt an: %(in)s. Dir kënnt op d'URL do verweisen wann Dir <a %(a_request)s>eng Datei ufrot</a>. Metadaten (%(count)s) Fir de Sichindex no Coden z’entdecken, benotzt den <a %(a_href)s>Codes Explorer</a>. Den Sichindex gëtt all Mount aktualiséiert. Et enthält aktuell Entréen bis %(last_data_refresh_date)s. Fir méi technesch Informatiounen, kuckt op der %(link_open_tag)sDatasets-Säit</a>. Ausschléissen Nëmmen abannen Ongepréift méi… Nächst … Vireg Dësen Sichindex enthält aktuell Metadaten aus der Controlled Digital Lending Bibliothéik vum Internet Archive. <a %(a_datasets)s>Méi iwwer eis Datasets</a>. Fir méi digital Verléinsbibliothéiken, kuckt op <a %(a_wikipedia)s>Wikipedia</a> an de <a %(a_mobileread)s>MobileRead Wiki</a>. Fir DMCA / Copyright Uspréch <a %(a_copyright)s>klickt hei</a>. Eroflueden Zäit Feeler beim Sichen. Probéiert <a %(a_reload)s>d'Säit nei ze lueden</a>. Wann de Problem bestoe bleift, schéckt eis w.e.g. eng E-Mail op %(email)s. Séier Eroflueden Tatsächlech kann jiddereen hëllefen dës Fichieren ze erhalen andeems en eis <a %(a_torrents)s>vereente Lëscht vun Torrents</a> seedt. ➡️ Heiansdo geschitt dëst falsch wann de Sichserver lues ass. An esou Fäll kann <a %(a_attrs)s>nei lueden</a> hëllefen. ❌ Dës Datei kéint Problemer hunn. Sicht Dir no Pabeieren? Dësen Sichindex enthält aktuell Metadaten aus verschiddene Metadatenquellen. <a %(a_datasets)s>Méi iwwer eis Datasets</a>. Et gi vill, vill Quelle vu Metadaten fir schrëftlech Wierker ronderëm d'Welt. <a %(a_wikipedia)s>Dës Wikipedia-Säit</a> ass e gudde Start, awer wann Dir aner gutt Lëschten kennt, loosst eis w.e.g. wëssen. Fir Metadaten weisen mir déi originell Opzeechnungen. Mir maachen keng Zesummeféierung vun Opzeechnungen. Mir hunn aktuell de weltwäit ëmfaassendsten oppene Katalog vu Bicher, Pabeieren an anere schrëftleche Wierker. Mir spigelen Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>an méi</a>. <span class="font-bold">Keng Dateien fonnt.</span> Probéiert manner oder aner Sichbegrëffer a Filteren. Resultater %(from)s-%(to)s (%(total)s insgesamt) Wann Dir aner "Schiedbibliothéiken" fannt, déi mir spigele sollten, oder wann Dir Froen hutt, kontaktéiert eis w.e.g. op %(email)s. %(num)d deelweis Treffere %(num)d+ deelweis Treffere Tippt an d'Këscht fir no Fichieren an digitale Verléinsbibliothéiken ze sichen. Tippt an d'Këscht fir an eisem Katalog vun %(count)s direkt erofluedbare Fichieren ze sichen, déi mir <a %(a_preserve)s>fir ëmmer erhalen</a>. Tippt an d'Këscht fir ze sichen. Tippt an d'Këscht fir an eisem Katalog vun %(count)s akademesche Pabeieren a Journalartikelen ze sichen, déi mir <a %(a_preserve)s>fir ëmmer erhalen</a>. Tippt an d'Këscht fir no Metadaten aus Bibliothéiken ze sichen. Dëst kann nëtzlech sinn wann Dir <a %(a_request)s>e Fichier ufrot</a>. Tipp: benotzt Tastaturkierzel “/” (Sichfokus), “enter” (sichen), “j” (rauf), “k” (runter), “<” (vireg Säit), “>” (nächst Säit) fir méi séier Navigatioun. Dëst sinn Metadaten-Opzeechnungen, <span %(classname)s>net</span> erofluedbar Dateien. Sichastellungen Sichen Digital Auslounen Eroflueden Zäitschrëftartikelen Metadaten Nei Sich %(search_input)s - Sich D'Sich huet ze laang gedauert, wat bedeit datt Dir eventuell net genee Resultater gesitt. Heiansdo hëlleft et d'Säit <a %(a_reload)s>nei ze lueden</a>. D'Sich huet ze laang gedauert, wat heefeg bei breede Ufroën ass. D'Filterzuelen kënnen net genee sinn. Fir grouss Uploads (iwwer 10,000 Dateien), déi net vu Libgen oder Z-Library akzeptéiert ginn, kontaktéiert eis w.e.g. op %(a_email)s. Fir Libgen.li, gitt sécher fir d'éischt op hirem <a %(a_forum)s>Forum</a> mat dem Benotzernumm %(username)s an dem Passwuert %(password)s unzemellen, an dann op hir <a %(a_upload_page)s>Upload-Säit</a> zréckzekommen. Fir de Moment proposéiere mir, nei Bicher op d'Library Genesis Forken eropzelueden. Hei ass e <a %(a_guide)s>praktesche Guide</a>. Notéiert datt béid Forken, déi mir op dëser Websäit indexéieren, aus dësem selwechten Upload-System zéien. Fir kleng Uploads (bis zu 10.000 Dateien) lued se w.e.g. op béid %(first)s an %(second)s erop. Alternativ kënnt Dir se op Z-Library <a %(a_upload)s>hei</a> eroplueden. Fir akademesch Pabeieren eropzelueden, lued se w.e.g. och (zousätzlech zu Library Genesis) op <a %(a_stc_nexus)s>STC Nexus</a> erop. Si sinn déi bescht Schiedbibliothéik fir nei Pabeieren. Mir hunn se nach net integréiert, mee mir wäerten dat zu engem gewëssen Zäitpunkt maachen. Dir kënnt hiren <a %(a_telegram)s>Upload-Bot op Telegram</a> benotzen, oder d'Adress kontaktéieren, déi an hirer gepinnten Noriicht opgelëscht ass, wann Dir ze vill Dateien hutt, fir se op dës Manéier eropzelueden. <span %(label)s>Schwéier Fräiwëllegenaarbecht (USD$50-USD$5,000 Belounungen):</span> wann Dir vill Zäit an/oder Ressourcen fir eis Missioun widmen kënnt, géife mir gären méi enk mat Iech zesummeschaffen. Schlussendlech kënnt Dir dem Kärteam bäitrieden. Och wann eise Budget knapp ass, kënne mir <span %(bold)s>💰 finanziell Belounungen</span> fir déi intensivst Aarbecht verginn. <span %(label)s>Liicht Fräiwëllegenaarbecht:</span> wann Dir nëmmen e puer Stonnen hei an do hutt, ginn et nach ëmmer vill Weeër, wéi Dir hëllefe kënnt. Mir belounen konsequent Fräiwëlleger mat <span %(bold)s>🤝 Memberschaften zu Anna’s Archive</span>. Anna’s Archive ass op Fräiwëlleger wéi Dir ugewisen. Mir begréissen all Engagementsniveauen a sichen no zwou Haaptkategorien vun Hëllef: Wann Dir net fäeg sidd Är Zäit fräiwëlleg ze widmen, kënnt Dir eis nach ëmmer vill hëllefen andeems Dir <a %(a_donate)s>Sue spendt</a>, <a %(a_torrents)s>eis Torrents seedt</a>, <a %(a_uploading)s>Bicher eropluet</a>, oder <a %(a_help)s>Äre Frënn iwwer Anna’s Archive erzielt</a>. <span %(bold)s>Firmen:</span> mir bidden Héich-Vitesse direkten Zougang zu eise Sammlungen am Austausch fir eng Spende op Entreprise-Niveau oder am Austausch fir nei Sammlungen (z.B. nei Scans, OCR’ed Datasets, Beräicherung vun eisen Donnéeën). <a %(a_contact)s>Kontaktéiert eis</a> wann dat op Iech zoutrëfft. Kuckt och eis <a %(a_llm)s>LLM Säit</a>. Primen Mir sichen ëmmer no Leit mat gudde Programméierungs- oder Sécherheetsfäegkeeten, déi sech bedeelege wëllen. Dir kënnt e wesentlechen Deel zur Erhalung vum Mënschheetsierwen bäidroen. Als Merci verschenke mir Memberschaften fir substantiell Bäiträg. Als groussen Merci verschenke mir finanziell Primen fir besonnesch wichteg an usprochsvoll Aufgaben. Dëst sollt net als Ersatz fir eng Aarbecht gesi ginn, mee et ass en zousätzlechen Ureiz a kann hëllefen, d'Käschten ze decken. De gréissten Deel vun eisem Code ass Open Source, an dat froe mir och vun Ärem Code, wann d'Prime verginn gëtt. Et ginn e puer Ausnamen, déi mir individuell diskutéiere kënnen. Primen ginn un déi éischt Persoun verginn, déi eng Aufgab ofschléisst. Fëllt Iech fräi, e Kommentar op en Prime-Ticket ze setzen, fir anerer wëssen ze loossen, datt Dir un eppes schafft, sou datt anerer ofwaarden oder Iech kontaktéiere kënnen, fir zesummenzeschaffen. Awer sidd bewosst, datt anerer nach ëmmer fräi sinn, drun ze schaffen an ze probéieren, Iech ze iwwerhuelen. Mir verginn awer keng Primen fir schlampereg Aarbecht. Wann zwou héichqualitativ Areechungen no beienee gemaach ginn (bannent engem Dag oder zwee), kënne mir entscheeden, Primen un allebéid ze verginn, no eisem Ermessen, zum Beispill 100%% fir déi éischt Areechung an 50%% fir déi zweet Areechung (sou datt 150%% insgesamt). Fir déi méi grouss Primen (besonnesch Scraping-Primen), kontaktéiert eis w.e.g., wann Dir ~5%% dovun ofgeschloss hutt, an Dir sidd zouversiichtlech, datt Är Method op de ganze Milestone skaléiert. Dir musst Är Method mat eis deelen, sou datt mir Feedback ginn. Och op dës Manéier kënne mir entscheeden, wat ze maachen ass, wann et méi Leit gëtt, déi no un enger Prime sinn, wéi zum Beispill d'Prime un méi Leit ze verginn, d'Leit ze encouragéieren, zesummenzeschaffen, etc. WARNUNG: d'Aufgaben mat héijer Prime si <span %(bold)s>schwéier</span> — et kéint weis sinn, mat méi einfache unzefänken. Gitt op eis <a %(a_gitlab)s>Gitlab-Issues-Lëscht</a> a sortéiert no "Label priority". Dëst weist ongeféier d'Uerdnung vun den Aufgaben, déi eis wichteg sinn. Aufgaben ouni explizit Primen si nach ëmmer berechtegt fir Memberschaft, besonnesch déi markéiert als "Accepted" an "Anna’s favorite". Dir wëllt vläicht mat engem "Starter project" ufänken. Liicht Fräiwëllegenaarbecht Mir hunn elo och e synchroniséierte Matrix-Kanal op %(matrix)s. Wann Dir e puer Stonnen Zäit hutt, kënnt Dir op verschidde Manéiere hëllefen. Gitt sécher, datt Dir dem <a %(a_telegram)s>Fräiwëllegen-Chat op Telegram</a> bäitritt. Als Zeeche vun eiser Unerkennung ginn mir typesch 6 Méint "Gléckleche Bibliothekar" fir Basis-Milestones, a méi fir weider Fräiwëllegenaarbecht. All Milestones erfuerderen héichqualitativ Aarbecht — schlampesch Aarbecht schued eis méi wéi se hëlleft a mir wäerten se ofleenen. Schéckt eis w.e.g. eng <a %(a_contact)s>E-Mail</a> wann Dir e Milestone erreecht. %(links)s Linken oder Screenshots vun Ufroen, déi Dir erfëllt hutt. Erfëllung vu Buch- (oder Pabeier, etc.) Ufroen op de Z-Library oder Library Genesis Foren. Mir hunn eegen Buchufro-System, awer mir spigelen dës Bibliothéiken, also mécht se besser Anna’s Archive besser. Milestone Aufgab Ofhängeg vun der Aufgab. Kleng Aufgaben gepost an eisem <a %(a_telegram)s>Fräiwëllegen-Chat op Telegram</a>. Normalerweis fir Memberschaft, heiansdo fir kleng Belounungen. Kleng Aufgaben, déi an eiser Fräiwëllege-Chatgrupp gepost ginn. Sidd sécher e Kommentar bei de Problemer ze hannerloossen, déi Dir fixéiert, sou datt anerer Är Aarbecht net duplizéieren. %(links)s Linken vun Opzeechnungen, déi Dir verbessert hutt. Dir kënnt d’<a %(a_list)s>Lëscht vun zoufällege Metadata-Problemer</a> als Startpunkt benotzen. Verbessert Metadaten duerch <a %(a_metadata)s>Verlinkung</a> mat Open Library. Dës sollten Iech weisen, wéi Dir een iwwer Annas Archiv informéiert, an datt si Iech dofir Merci soen. %(links)s Linken oder Screenshots. D'Wuert vun Annas Archiv verbreeden. Zum Beispill, andeems Dir Bicher op AA recommandéiert, op eis Blogposte verweist oder allgemeng Leit op eis Websäit dirigéiert. Eng Sprooch komplett iwwersetzen (wann se net scho bal fäerdeg war). <a %(a_translate)s>Iwwersetzen</a> vun der Websäit. Link zur Edit-Geschicht déi weist datt Dir wesentlech Bäiträg gemaach hutt. Verbessert d’Wikipedia-Säit fir Anna’s Archive an Ärer Sprooch. Enthält Informatiounen vun der Wikipedia-Säit vun AA an anere Sproochen, an vun eiser Websäit a Blog. Fügt Referenzen op AA op anere relevante Säiten derbäi. Fräiwëllegenaarbecht & Bounties 