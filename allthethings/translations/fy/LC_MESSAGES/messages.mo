��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b 7  sd Z  �f ,   h   3h �  Bi �  'k p  �l   in O   �o r   �o F   Dp j   �p �  �p S  �r �   	t �  �t �   �v �  �w �  �y �  �{ �   q} �  I~ �  ;� 8   � �   (� V   �   _�    h�   �� F   �� #   �    �    � @   7�    x� 
   �� -   ��    ҈ #   ��    � B   %� &  h�   �� V   �� 8   �� 
   5�    @�    L�    `�    l�    �� 	   ��    ��    �� "   ��    ٌ    ߌ 	   �� 
   �    �    �     8�    Y� T  t� P  ɏ �   � ,   �� f  ͑ �   4�   ؓ �   �    �� �   ԕ    s� �   �� "   <�   _�    w� y  �� 0   � c   @� 
   �� =  �� @  � �  .� -  ��    +� P   >� V   �� �   � )   �� 4   š u   �� =   p� F   �� ,   ��    "� w   (� �  �� C   d� i   �� �   � F   Ӧ   � )   3� �   ]� �   Z� �   � R   �� �   ߪ �   �� F   3� �   z� �   x� d   >� D   �� |   � 
   e� �   s� +  � F   @� 
   �� �   �� �   9�    
� n  � �   ��   ,� �  4� �   �� ;  �� �   � L   ۼ I   (� X   r� t   ˽ `   @� Y   �� ,   �� �   (� Y   �� C   � 
   I� m   W� M   �� �   � �   ��    a� �   r�   Q� O  ^� :  �� �   �� �   w� =  b� �   �� �   S� �   �� X  �� �   ��    ��    �� �   �� �   X� �   <� f  � �   i� �   �� �   � �  3� ~   � #  ��   �� &   �� Y   �� �   .� J   �� }  � �   �� m   8� v   �� "   � &  @� �   g� e  >�    �� _   �� �   #� �   �� �   v� �   n� �  0� �  � '   �� Y  �� o  � '  w� �  ��   C�   ^� T   {� �   ��    ]� �   e� D  #� n  h� �   �� �   �� �   ��    "� C  .� ]  r� �   ��    �� �   ��   �� �   �� �  d  �   a _   5    � z  � T      r �   � 	  j   t �   w
 K  Z P   � [   �   S
 7   _    �   �   � -  � .    F   2 �  y s   y z   � e   h q  �    @ "   _ s  � �   � �   � G   N �   � ,   &   S �   [    N    e /   r 9   � ;   � 4    T   M w   �     .   , ?   [ J   � )   � <     4   M  f   �     �     �    ! 
  " �   $# �   �# v   �$ �   U% J  &    d' �   u' �   H( �   �( l  u) _  �* �  B, �  *. �  �/ F   w1 +   �1 (   �1 �   2 �  �2 �  �4 +  6 ;  87   t8 $  �9   �; )   �<   �< '  > W  :?    �@ �   �@ @  wA �   �B I  NC �   �D    jE (   yE \   �E 	   �E x  	F �  �H D  J �   UL �   M 
   �M �   �M �   �N F   �O u   �O 	  iP   sQ �   uR p  �R a   mT   �T    �U �  �U �   �W �  qX a  oZ �  �\ �   �^ 
   z_ �   �_ �  o`    b l  +b �  �c   ue   �g �   �i %  rj F   �k �  �k �   �m x   qn >   �n O   )o    yo 9   }o %   �o $   �o #   p    &p �   <p �  �p �  �s =  �u �   �w    �x   �x ,  �{ ]  �} ~  J H  ɀ �  � 
   �� 
  �� 	   ��    �� �  ҆ +  t�   �� Q  ��    �� �  
� �  ֐ �  �� �  � �  s� J  L� w  ��    � �   -� f   � �  O� �  � �   �� �   F� �   ͣ �  �    � �   � K   æ I   �    Y� 2   r� >   �� 5  � �  � �  �� 2   G� �  z� �    �    �� �   �� ;   i� }   �� �   #� +   � `   �    }� 6  �� \  ŵ �  "� �  � �   ~�    � o   '� $  ��    �� �   �� �  P� �   �� ,   y� -   �� K  �� ,    � Y  M� �  �� �  9� !   � �   ?� �   ��   P� �  o�    �� �   � +   �� �   � �   ��   �� �   �� F   W� ,   �� �   �� <   �� m  �� Z  i� �   ��   �� a   �� >   � �   G� (  �� n  � �  w� �  H� 1    � �   2�   � H  � �   Q� �  �� �  �� ,   �� �  �� '   �� �  �� W   ��    ��   � �  
� n  �� �   j�   f� �  m� ;  V� �   �� �  2� �  �� �   |� �  
� R  � :   E �   � z           � 3  � �   � 7   �   � �  �   �	 �  �
 �  � �    �  �   c    � �   �    [ q  d !   �    �         $   #    H    Z    j    ~    �    �    � 
   � 	   �    �    � /   �             &    . �   4 S   �     O    p &    !   � )   � +   �      	   ? 	   I 
   S    a    s    � 	   �    �    �    � (   �    �     !    %   A )   g '   �    �     � ;   �    ( D   < ?   �    � Q   � J       b    ~ "   �    �    �    �    �    	             4    A    X 	   e 
   o    z    }    �    � 	   �    � 	   �    �    �    � 	   �            !    :    B    ]    {    � 	   �    � "   �    �    � #   �        	    %    .    E    K    \ Y   n �   � X   g �   � �  s u   C!    �!    �!    �!    �!    �!    "    " "   +" O   N" <   �" Y   �" %   5# =   [# &   �# _   �# _    $ �   �$ W   d% 4   �%    �%    &    & 
   !& 
   ,&    7&    I&    _&    d&    t&    }&    �&    �&    �&    �&    �&    �&    �&    �&    �& 	   �&    '    '    '    ;'   N'    [(    `(    h(     n(    �( J   �( [   �( &   =) %   d) 5   �)    �)    �)    �) q   �)    E*    K* *   Z* q   �*    �*    + �    + (   �+ (  �+ X   �. q   S/ �   �/ �   s0 ,   X1 �   �1 �   �2 6  3 �   F4    <5    W5 ;   `5 <   �5 P   �5 _   *6 a   �6 @   �6 s   -7 "   �7 )   �7    �7    �7 F   8 "   N8    q8    y8    �8    �8 k   �8    9 (   #9 E   L9    �9    �9 M   �9 U   : c  b:    �;    �; �   �;    �<    �<    �<    �<    �< /   �< M  =    i> 
   �>    �> 
   �>    �> &   �?    �?    �? q   @    w@    ~@ :   �@    �@    �@ '   �@ �   A    �A 
   �A H   �A     �A    B 
   /B 
   :B *   HB R   sB    �B .   �B �   C c   �C M   D 
   ZD �   hD E   SE    �E 7   �E    �E �   �E �   �F    FG ;   [G �   �G �   #H    �H '   I    *I d  :I     �J "   �J    �J #   K )   'K �   QK    �K    L *   'L C   RL    �L    �L    �L    �L ;  �L o  5O �  �P 6   �R +   �R    �R %   	S %  /S �   UT �   ,U    �U �   �U �  �V    dX )   �X �  �X 8  �Z    �[    �[ /   �[    \ 2  \    G] 8  \] �  �^ �   '`    a k   )a &   �a !   �a q   �a   Pb   nc �   zd �  e l   �f �   g ]   h �   lh �   &i R   �i �   )j *   �j !   k    )k    8k    Ak &   Rk    yk >   �k ,   �k 	   �k ,   	l   6l "   Om �   rm �   Nn ~   �n !   Oo    qo    �o    �o #   �o    �o %   �o     p �   @p $   q �   @q    r �   r u   �r �   Ks   �s �   �t ,   �u �   �u 	   pv   zv j   �w    �w �  x    �y    �y 
   �y %   �y (   z    -z    4z T   ;z �   �z �   /{    |    |    | �   5| &  �| �   ~ {   �~    <    R    h    }    �    �    � ;   � +   � �   � Q   ށ    0� e   B� p   �� T   � f   n� U   Ճ R   +� 	   ~� i   �� L   � {   ?� T   �� L   � 
   ]� �   k� ]   Q� 8   �� `   � Y   I� 8   ��    ܈ 4   � r   � �   �� A   � �   `�    �   
� >   � _   [� �   ��    Q� �  Z� �   �    �    ��    �    &�    ,� )   -� d   W� �   �� �   �� �   O� �   ܓ �   �� �   A� �   ە Y   ��    � d   � [  w� �   ә �   �� 
   �� 
   �� 
   �� �   �� 
   G� 
   U� M   c� d   �� �   � 
   � V   �� 9   N� ~   �� <   � x   D� g   �� 
   %� �   3� 
   �� 
   Ǡ 
   ՠ '  � �   � �  ��    U�    m�    v� �   ��    ,�    G� �   f� �   [�    �    %�    5� <   U� 4   �� 4   Ǩ    ��    � �   *�    !� �  @�   ߫ �   � R   r� �   ŭ 
  l� �  z� X  Z� �   �� �   e� |   �    e� }  z�    �� h  � �   �� 1  e� �   �� Z   � �   {� �   ,� k   �� +   +� 6   W�    �� 2   �� 
   ־    �    � �   ��    � h   � -   W�    ��    ��    ��     �� n   �� >   3� 0   r� �   �� 3   �� 
   �� 	   ��    �� $   ��     � 
   2�    =� 
   F� 	   Q� 
   [� 	   f� 
   p� *   {� �   ��    C�    T� 
   d�    r�    ��    ��    ��    ��    ��    �� %   �� d   �    |� /   �� `   �� �   � �   �� �   ��   4� �   :�   ��    
� �   � 2   �� Q   �� E   (� �   n� �   � F   �� [   5�    �� s   �� \   � �   u�    ��     �    #�    4� 	   I�    S�    g�    o�    ��    ��    ��    ��    ��    ��    �    (�    /�    A�    P�    X�    o� #   v� !   �� �   �� �   B� (   �� %   �� q   � V   �� v   �� #   T� /   x� /   �� 6   �� ;   � +   K� �   w� �   +�   (�    8� A   O� �   �� "   � �   A� �   � s   �� 8   J�    �� �   �� �   9� d   �� (   -� g   V� �   ��    �� )   ��    �� @   �� a   !� a   ��    ��    ��    ��    �� �   � 4   ��    �� 0   � #   4�    X�     w� K   ��    �� p   �� ?   o� �   �� H   �� V   �� �   O� A   �� #   1� !   U� #   w� "   �� #   �� "   �� #   � 8   )� 7   b� a  �� 8   �� 6   5� ?   l� +   ��    �� y   �� s   [� �   �� �   �� 	   X� �   b� '   ��    � �   .�    ��     � @   5� 4   v� T   �� I    � N   J�    ��    �� �   �� 1   t�     �� K   ��   � <   � �   T� n   �� V   \� �   ��     7� &   X� s   �    �� T   
�    b� �   � 4   ;� 9   p�    �� !   �� �   �� ;   �� Q   � �   d� [   �� N   B� {   �� �   
� 
   ��    �� L   �� >    �    ?�    X�    w�    �� 
   �� -   �� �   �� k   b�    ��    �� %   	� (   /� )   X� m   �� 9   ��    *� R   ��     �� h   � �   �� 0   [  T   �     �  H   �  D   =    � z   � t    -   � M   �     0    T   B    � l   � +       C .   Z W   � �   � @   �    �    � @       I T   ` ?   � �   � n   �     9    �   P    		 �   '	 E   �	     
 J   .
   y
    |    �    �    � (   � +   � I   �    ;    W    h "   q @   � @   �    
 H   
 @   f
    �
    �
 /   �
         [   4 
  � O   �    �    � �    Y  � P       b ]  s �  � !   T s   v    � ;   &   D r   k    �    � �      � P   �    < m   � V   *    � \   � 5   �    , t   G B   � 2   � X   2 *   � O   � {    �   � #     �   B  Z  ! �   `" 1   &# @  X# �   �$ �   y%   0& �   J' -   (    /( �   H( ,   �( 	  ) \   "+ G   + 
   �+    �+ �  �+    �- �   �- !  |. -  �/   �0 A   �1 I   2 d   g2 .   �2 $   �2 [    3 ]   |3    �3    �3 8   4    @4    \4 )   s4 V   �4 -   �4    "5 b   )5 �   �5 �   k6    7    7    )7 G   /7 ]   w7 R   �7 �   (8 i   �8    b9 &   t9 �   �9    p: �   x: �  ; �   �= J   �> /   �>    ?    $?    ,? G   0? /   x? �   �? �   +@ S   A    rA    �A %   �A    �A P   �A    -B <   <B    yB +   �B    �B    �B    �B f   �B    IC    NC !   cC    �C    �C d   �C �   D W   �D `   "E X   �E �   �E    �F    �F �   �F �   �G �   @H 	   �H x   �H S   xI D   �I e   J n   wJ R   �J    9K I   PK    �K    �K    �K    �K    �K    	L    L    0L    CL    PL +   \L +   �L -   �L !   �L 2   M )   7M !   aM    �M    �M A   �M    �M    N 5   N &   EN c   lN )   �N    �N    O '   /O $   WO    |O g   �O Q   �O t   IP �   �P    �Q    �Q    �Q    �Q 4   �Q 	   /R    9R    JR }   `R    �R    �R    S 
   S 	   (S ?   2S    rS �   �S    �T    �T    �T "   �T    �T    
U    $U !   DU %   fU )   �U '   �U    �U `   �U &   FV    mV    �V 4   �V L   �V    W -   9W    gW q   �W V   �W ?   NX    �X    �X 	   �X    �X    �X    �X �  �X }   �Z    [    [ ,   [    K[ $   ^[    �[    �[ {   �[ @   
\ �   N\    1] !   F] �   h] !   �]    ^     0^ #   Q^ -   u^ #   �^    �^    �^ 3   �^    !_ *   ?_ =   j_ 0  �_ =   �` M   a L   ea     �a |   �a &   Pb    wb Z   �b    �b *   �b    "c C   >c =   �c !   �c �   �c    �d    �d    �d    �d    �d    �d    e    +e    >e U   Ve   �e    �f $   �f �   �f �   �g    zh "   �h    �h    �h    �h    �h    i    i    3i    Ei    Xi    gi 
   xi    �i    �i    �i    �i    �i &   �i �   �i   �j g  �k    4m �  5o �   �p l  �q 
   +s 
  6s    Dt �   Yt �   Xu �  >v �   �w n  �x 7   z �   Cz <   �z    +{ A   B{ I   �{ h   �{ g   7| �   �| �   I} �   ~ @  �~    � �   � �   ؁ T   �� �   �� 
   �� �   �� ]  {� �   م �   ��    U� _   ^� l   �� 	  +�    5� v   �� �   ,� 
   �� !   ��    ފ A   �� *   7� 
   b� |   p� ;   � g   )�    �� �   �� �   G� E   ̍ S   � T   f� b   �� ]   � P   |� {   ͏ k   I� ~   �� m   4�    �� *   ��    Ց Z   � 3   M�    ��    �� X   ��    �    ��    � 3   � )   G� >   q�    ��    ʓ    ד    �� 	   � R   � n   C� F   �� 8   ��    2�    :� *   V�    �� 
   ��    ��    ��    ��    ��    ��    ��    ̕    ѕ    ޕ    �    ��    ��    
�    �    .�    5�    B�    I� &   P�    w�    �� �   ��    :� T   O� �   ��    _�    h�    z�    �� 	   ��    ��    �� �   �� y   J� >   ę    �    � t   "�    �� �   �� s   (� $   ��    �� x   ֛ �   O� Y   !� �   {� _   @� -   ��    Ξ    N�    k� G   �� �   ̟    W� �   u� �   � �   �� Q   K�    ��    ��    ��    Ģ    ͢    ߢ 
   �    �� �   � i   �� �   � �   �� �   o� b   I� I   �� �  �� `  ��   � �   � *  �� L  ۬ 	   (� �   2�   �� �   �� �  �� �  2� {   � c  ��    � =   �� �   <� \  ܷ N   9� �   ��    o�    w�    |� �   �� 4   � l   T� :   �� _   �� L   \� [   �� )   � �   /� 9   ׽ +   � U   =� �   ��    `�  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: fy
Language-Team: fy <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library is in populêre (en yllegale) bibleteek. Se hawwe de Library Genesis-kolleksje nommen en maklik trochsykber makke. Dêrneist binne se tige effektyf wurden yn it oanlûken fan nije boekbydragen, troch bydrage brûkers te stimulearjen mei ferskate foardielen. Se drage op it stuit dizze nije boeken net werom oan Library Genesis. En oars as Library Genesis, meitsje se har kolleksje net maklik spegelber, wat breed behâld foarkomt. Dit is wichtich foar har bedriuwsmodel, om't se jild freegje foar tagong ta har kolleksje yn bulk (mear as 10 boeken per dei). Wy meitsje gjin morele oardielen oer it yn rekken bringen fan jild foar bulk tagong ta in yllegale boekekolleksje. It is bûten mis dat de Z-Bibleteek suksesfol west hat yn it útwreidzjen fan tagong ta kennis, en mear boeken te finen. Wy binne hjir gewoan om ús diel te dwaan: it soargjen foar de lange termyn behâld fan dizze priveekolleksje. - Anna en it team (<a %(reddit)s>Reddit</a>) Yn de oarspronklike release fan de Pirate Library Mirror (EDIT: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>), makken wy in spegel fan Z-Library, in grutte yllegale boekekolleksje. As herinnering, dit is wat wy yn dy oarspronklike blogpost skreaunen: Dy kolleksje datearre út mids-2021. Yntusken is de Z-Bibleteek yn in ferbjusterjend tempo groeid: se hawwe sawat 3,8 miljoen nije boeken tafoege. Der binne wat duplikaten yn, wis, mar it meastepart liket legitime nij boeken te wêzen, of hegere kwaliteit scans fan earder ynstjoerde boeken. Dit is foar in grut part troch it tanommen oantal frijwillige moderators by de Z-Bibleteek, en harren bulk-upload systeem mei deduplikearring. Wy wolle harren lokwinskje mei dizze prestaasjes. Wy binne bliid om oan te kundigjen dat wy alle boeken krigen hawwe dy't tafoege waarden oan de Z-Bibleteek tusken ús lêste spegel en augustus 2022. Wy hawwe ek weromgien en guon boeken skraapt dy't wy de earste kear mist hawwe. Alles byinoar is dizze nije kolleksje sawat 24TB, wat folle grutter is as de lêste (7TB). Us spegel is no yn totaal 31TB. Op 'e nij hawwe wy deduplikearre tsjin Library Genesis, om't der al torrents beskikber binne foar dy kolleksje. Gean asjebleaft nei de Pirate Library Mirror om de nije kolleksje te besjen (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>). Dêr is mear ynformaasje oer hoe't de triemmen strukturearre binne, en wat feroare is sûnt de lêste kear. Wy sille der hjir net nei ferwize, om't dit gewoan in blogwebside is dy't gjin yllegale materialen hostet. Fansels is it seedjen ek in geweldige manier om ús te helpen. Tank oan elkenien dy't ús foarige set torrents seedet. Wy binne tankber foar de positive reaksje, en bliid dat der safolle minsken binne dy't soarchje oer it behâld fan kennis en kultuer op dizze ûngewoane manier. 3x nije boeken tafoege oan de Pirate Library Mirror (+24TB, 3,8 miljoen boeken) Lês de begeliedende artikels troch TorrentFreak: <a %(torrentfreak)s>earste</a>, <a %(torrentfreak_2)s>twadde</a> - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) begeliedende artikels troch TorrentFreak: <a %(torrentfreak)s>earste</a>, <a %(torrentfreak_2)s>twadde</a> Net sa lang lyn wiene "skaad-biblioteken" oan it ferdwinen. Sci-Hub, it grutte yllegale argyf fan akademyske papieren, hie ophâlden mei it opnimmen fan nije wurken fanwegen rjochtsaken. "Z-Library", de grutste yllegale biblioteek fan boeken, seach har beskuldige makkers arrestearre op kriminele auteursrjocht oanklachten. Se slaggen der op in ûnfoarstelbere wize yn om har arrestaasje te ûntkommen, mar har biblioteek is net minder yn gefaar. Guon lannen dogge al in ferzje fan dit. TorrentFreak <a %(torrentfreak)s>rapporteare</a> dat Sina en Japan AI-útsûnderings ynfierd hawwe yn harren auteursrjochtwetten. It is ús net dúdlik hoe't dit ynteraksje hat mei ynternasjonale ferdraggen, mar it jout wis dekking oan harren ynlânske bedriuwen, wat ferklearret wat wy sjoen hawwe. Wat Anna's Argyf oanbelanget — wy sille ús ûndergrûnske wurk trochsette, woartele yn morele oertsjûging. Mar ús grutste winsk is om yn it ljocht te kommen, en ús ynfloed legaal te fergrutsjen. Asjebleaft, reformearje it auteursrjocht. Doe't Z-Library foar sluting stie, hie ik al har hiele biblioteek bewarre en wie ik op syk nei in platfoarm om it te hûzjen. Dat wie myn motivaasje foar it begjinnen fan Anna's Argyf: in fuortsetting fan de misje efter dy eardere inisjativen. Wy binne sûnt útgroeid ta de grutste skaad-biblioteek yn 'e wrâld, mei mear as 140 miljoen auteursrjochtlik beskerme teksten yn ferskate formaten — boeken, akademyske papieren, tydskriften, kranten, en mear. Myn team en ik binne ideologen. Wy leauwe dat it bewarjen en hûzjen fan dizze bestannen moreel rjocht is. Biblioteken oer de hiele wrâld sjogge finansieringsbesunigings, en wy kinne it erfgoed fan 'e minskheid ek net fertrouwe oan bedriuwen. Doe kaam AI. Hast alle grutte bedriuwen dy't LLM's bouwe, hawwe kontakt mei ús opnommen om te trainen op ús gegevens. De measten (mar net allegear!) Amerikaanske bedriuwen hawwe harren beslút heroverwage doe't se de yllegale aard fan ús wurk beseften. Yn tsjinstelling, Sineeske bedriuwen hawwe ús kolleksje entûsjast omearme, blykber net benaud foar de legaliteit. Dit is opmerklik jûn de rol fan Sina as ûndertekenaar fan hast alle grutte ynternasjonale auteursrjochtferdraggen. Wy hawwe hege-snelheid tagong jûn oan sawat 30 bedriuwen. De measten fan harren binne LLM-bedriuwen, en guon binne gegevenshannelers, dy't ús kolleksje trochferkeapje sille. De measten binne Sineesk, hoewol't wy ek wurke hawwe mei bedriuwen út de FS, Jeropa, Ruslân, Súd-Koreä, en Japan. DeepSeek <a %(arxiv)s>joeche ta</a> dat in eardere ferzje traind waard op in diel fan ús kolleksje, hoewol't se tige geheimhâldend binne oer har lêste model (wêrschynlik ek traind op ús gegevens). As it Westen foarop bliuwe wol yn de race fan LLM's, en úteinlik, AGI, moat it syn posysje oer auteursrjocht op 'e nij besjen, en gau. Oft jo it no mei ús iens binne of net oer ús morele saak, dit wurdt no in kwestje fan ekonomy, en sels fan nasjonale feiligens. Alle machtsblokken bouwe keunstmjittige super-wittenskippers, super-hackers, en super-legers. Frijheid fan ynformaasje wurdt in kwestje fan oerlibjen foar dizze lannen — sels in kwestje fan nasjonale feiligens. Us team komt fan oer de hiele wrâld, en wy hawwe gjin spesifike ôfstimming. Mar wy soene lannen mei sterke auteursrjochtwetten oanmoedigje om dizze besteansbedriging te brûken om se te reformearjen. Wat te dwaan? Us earste oanbefelling is ienfâldich: ferkoart de auteursrjochttermyn. Yn 'e FS wurdt auteursrjocht ferliend foar 70 jier nei de dea fan 'e auteur. Dit is absurd. Wy kinne dit yn oerienstimming bringe mei patinten, dy't ferliend wurde foar 20 jier nei it yntsjinjen. Dit moat mear as genôch tiid wêze foar auteurs fan boeken, papieren, muzyk, keunst, en oare kreative wurken, om folslein kompensearre te wurden foar harren ynspanningen (ynklusyf langere-termyn projekten lykas filmadaptaasjes). Dan, op syn minst, moatte beliedsmakkers útsûnderings opnimme foar de massa-preservaasje en fersprieding fan teksten. As ferlern ynkommen fan yndividuele klanten de wichtichste soarch is, kin persoanlik-nivo distribúsje ferbean bliuwe. Op syn beurt soene dyjingen dy't by steat binne om grutte repositories te behearjen — bedriuwen dy't LLM's traine, tegearre mei biblioteken en oare argiven — ûnder dizze útsûnderings falle. Copyrightreformaasje is nedich foar nasjonale feiligens. TL;DR: Sineeske LLM's (ynklusyf DeepSeek) wurde traind op myn yllegale argyf fan boeken en papieren — it grutste yn 'e wrâld. It Westen moat it auteursrjocht wetjouwing opnij besjen as in kwestje fan nasjonale feiligens. Sjoch asjebleaft de <a %(all_isbns)s>oarspronklike blogpost</a> foar mear ynformaasje. Wy hawwe in útdaging útjûn om dit te ferbetterjen. Wy soene in earste priis fan $6,000 útrikke, in twadde priis fan $3,000, en in tredde priis fan $1,000. Fanwegen de oerweldigjende reaksje en ûnbidige ynstjoeringen, hawwe wy besletten om de priispot in bytsje te ferheegjen, en in fjouwer-wei tredde priis fan $500 elk út te rikken. De winners steane hjirûnder, mar sjoch wis nei alle ynstjoeringen <a %(annas_archive)s>hjir</a>, of download ús <a %(a_2025_01_isbn_visualization_files)s>kombinearre torrent</a>. Earste priis $6,000: phiresky Dizze <a %(phiresky_github)s>ynstjoering</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentaar</a>) is gewoan alles wat wy woenen, en mear! Wy fûnen benammen de ûnbidich fleksibele fisualisaasje-opsjes geweldich (sels mei stipe foar oanpaste shaders), mar mei in wiidweidige list fan presets. Wy fûnen ek hoe fluch en soepel alles is, de ienfâldige ymplemintaasje (dy’t net iens in backend hat), de tûke minimap, en de wiidweidige útlis yn harren <a %(phiresky_github)s>blogpost</a>. Ûnbidich wurk, en de terjochte winner! - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Us herten binne fol mei tankberens. Opfallende ideeën Wolkenkrabbers foar seldsumens In protte sliders om datasets te fergelykjen, as wiene jo in DJ. Skaalbalke mei oantal boeken. Moaie labels. Kâlde standert kleurenschema en hjittekaart. Unike kaartbesjoch en filters Annotaasjes, en ek live statistiken Live statistiken Guon mear ideeën en ymplemintaasjes dy't wy benammen moai fûnen: Wy koene noch in skoft trochgean, mar litte wy hjir stopje. Sjoch wis nei alle ynstjoeringen <a %(annas_archive)s>hjir</a>, of download ús <a %(a_2025_01_isbn_visualization_files)s>kombinearre torrent</a>. Sa'n soad ynstjoeringen, en elk bringt in unyk perspektyf, oft yn UI of ymplemintaasje. Wy sille op syn minst de earste plak ynstjoering yn ús haadwebside opnimme, en miskien guon oaren. Wy binne ek begûn te tinken oer hoe't wy it proses fan it identifisearjen, befestigjen, en dan argivearjen fan de seldsumste boeken organisearje kinne. Mear hjir oer komt noch. Tank oan elkenien dy't meidien hat. It is geweldich dat sa'n soad minsken derom jouwe. Maklik wikseljen fan datasets foar flugge fergelikingen. Alle ISBNs CADAL SSNOs CERLALC gegevenslek DuXiu SSIDs EBSCOhost’s eBook Yndeks Google Books Goodreads Internet Archive ISBNdb ISBN Global Register fan Utjouwers Libby Bestannen yn Anna’s Argyf Nexus/STC OCLC/Worldcat OpenLibrary Russyske Steatsbibleteek Keizerlike Bibleteek fan Trantor Twadde priis $3,000: hypha “Wylst perfekte fjouwerkanten en rjochthoeken wiskundich noflik binne, biede se gjin superieure lokaliteit yn in kaartkontekst. Ik leau dat de asymmetry ynherint yn dizze Hilbert of klassike Morton gjin flater is, mar in funksje. Krekt as de ferneamde laarsfoarmige omtrek fan Itaalje it daliks werkenber makket op in kaart, kinne de unike "kuriositeiten" fan dizze krommen tsjinje as kognitive landmarks. Dizze ûnderskiedendheid kin de romtlike ûnthâld ferbetterje en brûkers helpe harsels te oriïntearjen, wat it mooglik makket om spesifike regio's te finen of patroanen op te merken.” In oare ûnbidige <a %(annas_archive_note_2913)s>ynstjoering</a>. Net sa fleksibel as de earste priis, mar wy fûnen de makro-nivo fisualisaasje eins better as de earste priis (romte-follende kromme, grinzen, labeling, markearring, panning, en zooming). In <a %(annas_archive_note_2971)s>kommentaar</a> fan Joe Davis resonearre mei ús: En noch altyd in protte opsjes foar fisualisearjen en renderjen, lykas in ûnbidich soepele en yntuïtive UI. In solide twadde priis! - Anna en it team (<a %(reddit)s>Reddit</a>) In pear moannen lyn kundigen wy in <a %(all_isbns)s>$10,000 bounty</a> oan om de bêst mooglike visualisaasje fan ús gegevens te meitsjen dy't de ISBN-romte sjen lit. Wy leinen klam op it sjen litten hokker bestannen wy al/nog net argivearre hawwe, en wy levere letter in dataset dy't beskriuwt hoefolle biblioteken ISBN's hawwe (in mjitte fan seldsumheid). Wy binne oerweldige troch de reaksje. Der is sa folle kreativiteit west. In grutte tank oan elkenien dy't meidien hat: jo enerzjy en entûsjasme binne oansteeklik! Uteinlik woenen wy de folgjende fragen beantwurdzje: <strong>hokker boeken besteane der yn 'e wrâld, hoefolle hawwe wy al argivearre, en op hokker boeken moatte wy ús folgjende rjochtsje?</strong> It is prachtich om te sjen dat safolle minsken har ynteressearje foar dizze fragen. Wy begûnen sels mei in basis fisualisaasje. Yn minder as 300kb fertsjintwurdiget dizze ôfbylding koart it grutste folslein iepen "list fan boeken" ea gearstald yn 'e skiednis fan 'e minskheid: Tredde priis $500 #1: maxlion Yn dizze <a %(annas_archive_note_2940)s>ynstjoering</a> fûnen wy de ferskillende soarten werjeften echt moai, benammen de fergeliking en útjouwer werjeften. Tredde priis $500 #2: abetusk Hoewol net de meast poetse UI, kontrolearret dizze <a %(annas_archive_note_2917)s>ynstjoering</a> in protte fan de fakjes. Wy fûnen benammen de fergeliking funksje moai. Tredde priis $500 #3: conundrumer0 Lykas de earste priis, hat dizze <a %(annas_archive_note_2975)s>ynstjoering</a> ús yndruk makke mei syn fleksibiliteit. Uteinlik is dit wat in geweldige fisualisaasje-ark makket: maksimale fleksibiliteit foar krêftbrûkers, wylst it ienfâldich hâldt foar gemiddelde brûkers. Tredde priis $500 #4: charelf De lêste <a %(annas_archive_note_2947)s>ynstjoering</a> dy't in beleanning krijt is frij basis, mar hat wat unike funksjes dy't wy echt moai fûnen. Wy fûnen it moai hoe't se sjen litte hoefolle datasets in bepaald ISBN dekke as in mjitte fan populariteit/betrouberens. Wy fûnen ek de ienfâld mar effektyf fan it brûken fan in opasiteit-skuif foar fergelikingen echt moai. Winners fan de $10,000 ISBN visualisaasje bounty TL;DR: Wy hawwe wat ûnfoarstelbere ynstjoeringen krigen foar de $10,000 ISBN visualisaasje bounty. Eftergrûn Hoe kin Anna's Argiven syn missy fan it bewarjen fan alle kennis fan 'e minskheid berikke, sûnder te witten hokker boeken der noch binne? Wy hawwe in TODO-list nedich. Ien manier om dit yn kaart te bringen is troch ISBN-nûmers, dy't sûnt de jierren '70 oan elk publisearre boek tawiisd binne (yn de measte lannen). Der is gjin sintrale autoriteit dy't alle ISBN-tawiisings wit. Ynstee dêrfan is it in ferspraat systeem, wêrby't lannen nûmerrigen krije, dy't dan lytsere rigen oan grutte útjouwers tawize, dy't op har beurt de rigen fierder ferdiele kinne oan lytse útjouwers. Uteinlik wurde yndividuele nûmers oan boeken tawiisd. Wy binne twa jier lyn begûn mei it yn kaart bringen fan ISBNs <a %(blog)s>mei ús scrape fan ISBNdb</a>. Sûnt dy tiid hawwe wy in protte mear metadata-boarnen skraapt, lykas <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, en mear. In folsleine list is te finen op de siden “Datasets” en “Torrents” op Anna's Argiven. Wy hawwe no fierwei de grutste folslein iepen, maklik te downloaden kolleksje fan boekmetadata (en dus ISBNs) yn 'e wrâld. Wy hawwe <a %(blog)s>wiidweidich skreaun</a> oer wêrom't wy soarchje oer bewarjen, en wêrom't wy op it stuit yn in kritysk tiidrek sitte. Wy moatte no seldsume, ûnderfokusearre en unyk bedrige boeken identifisearje en bewarje. It hawwen fan goede metadata oer alle boeken yn 'e wrâld helpt dêrby. $10.000 beleanning Sterke oerwagings sille jûn wurde oan brûkberens en hoe goed it derút sjocht. Toan eigentlike metadata foar yndividuele ISBNs by it ynzoomen, lykas titel en auteur. Bêtere romte-follende kromme. Bygelyks in zigzag, dy't fan 0 nei 4 giet op de earste rigel en dan werom (yn omkearde rjochting) fan 5 nei 9 op de twadde rigel — rekursyf tapast. Ferskillende of oanpasbere kleurtsjema's. Spesjale werjeften foar it fergelykjen fan datasets. Manieren om problemen te debuggen, lykas oare metadata dy't net goed oerienkomme (bygelyks hiel ferskillende titels). Ofbyldings annotearje mei opmerkingen oer ISBN's of berikken. Elke heuristyk foar it identifisearjen fan seldsume of bedrige boeken. Hokker kreative ideeën jo ek betinke kinne! Koade De koade om dizze ôfbyldings te generearjen, lykas oare foarbylden, is te finen yn <a %(annas_archive)s>dizze map</a>. Wy hawwe in kompakt dataformaat betocht, wêrmei alle nedige ISBN-ynformaasje sawat 75MB (komprimearre) is. De beskriuwing fan it dataformaat en de koade om it te generearjen kinne jo <a %(annas_archive_l1244_1319)s>hjir</a> fine. Foar de bounty binne jo net ferplichte dit te brûken, mar it is wierskynlik it meast handige formaat om mei te begjinnen. Jo kinne ús metadata transformearje hoe't jo wolle (hoewol al jo koade iepen boarne wêze moat). Wy kinne net wachtsje om te sjen wat jo betinke. In protte súkses! Fork dizze repo, en bewurkje dizze blogpost HTML (gjin oare backends as ús Flask-backend binne tastien). Meitsje de ôfbylding hjirboppe soepel ynzoomber, sadat jo hielendal nei yndividuele ISBNs kinne ynzoome. It klikken op ISBNs moat jo nei in metadata-side of sykaksje op Anna's Argiven bringe. Jo moatte noch altyd kinne wikselje tusken alle ferskillende datasets. Lânrigen en útjouwer-rigen moatte markearre wurde by hover. Jo kinne bygelyks <a %(github_xlcnd_isbnlib)s>data4info.py yn isbnlib</a> brûke foar lânynformaasje, en ús “isbngrp” scrape foar útjouwers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). It moat goed wurkje op desktop en mobile. Der is hjir in protte te ûntdekken, dus wy kundigje in beleanning oan foar it ferbetterjen fan de visualisaasje hjirboppe. Oars as de measte fan ús beleannings, is dizze tiidbûn. Jo moatte jo iepen boarne koade yntsjinje foar 2025-01-31 (23:59 UTC). De bêste ynstjoering sil $6.000 krije, de twadde plak is $3.000, en it tredde plak is $1.000. Alle beleannings wurde útrikt mei Monero (XMR). Hjirûnder binne de minimale kritearia. As gjin ynstjoering oan de kritearia foldocht, kinne wy noch wat beleannings útrikke, mar dat sil nei ús ynsjoch wêze. Foar bonuspunten (dit binne allinnich ideeën — lit jo kreativiteit frij rinne): Jo MEI folslein ôfwike fan de minimale kritearia, en in folslein oare fisualisaasje meitsje. As it echt spektakulêr is, dan kwalifisearret dat foar de bounty, mar nei ús goedtinken. Meitsje ynstjoeringen troch in opmerking te pleatsen by <a %(annas_archive)s>dit probleem</a> mei in keppeling nei jo forked repo, merge request, of diff. - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Dizze ôfbylding is 1000×800 piksels. Elk piksel fertsjintwurdiget 2,500 ISBNs. As wy in bestân hawwe foar in ISBN, meitsje wy dat piksel mear grien. As wy witte dat in ISBN útjûn is, mar wy hawwe gjin oerienkommend bestân, meitsje wy it mear read. Yn minder as 300kb, fertsjintwurdiget dizze ôfbylding koart de grutste folslein iepen "list fan boeken" ea gearstald yn de skiednis fan 'e minskheid (in pear hûndert GB komprimearre yn folslein). It toant ek: der is noch in protte wurk te dwaan yn it bewarjen fan boeken (wy hawwe allinnich 16%). Visualisearjen fan Alle ISBNs — $10,000 beleanning foar 2025-01-31 Dizze ôfbylding fertsjintwurdiget de grutste folslein iepen "list fan boeken" ea gearstald yn de skiednis fan 'e minskheid. Visualisearje Njonken it oersjochôfbylding kinne wy ek nei yndividuele datasets sjen dy't wy sammele hawwe. Brûk it útklapmenu en de knoppen om tusken harren te wikseljen. Der binne in protte ynteressante patroanen te sjen yn dizze ôfbyldings. Wêrom is der wat regelmjittigens fan rigels en blokken, dy't op ferskate skalen liket te barren? Wat binne de lege gebieten? Wêrom binne bepaalde datasets sa klustere? Wy litte dizze fragen oer as in oefening foar de lêzer. - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Konklúzje Mei dizze standert kinne wy releases mear ynkrementeel meitsje, en makliker nije boarnen fan gegevens tafoegje. Wy hawwe al in pear spannende releases yn de pipeline! Wy hoopje ek dat it makliker wurdt foar oare skaadbiblioteken om ús kolleksjes te spegeljen. Uteinlik is ús doel om minsklike kennis en kultuer foar ivich te bewarjen, dus hoe mear redundânsje, hoe better. Foarbyld Litte wy ús resinte Z-Library release as in foarbyld besjen. It bestiet út twa kolleksjes: “<span style="background: #fffaa3">zlib3_records</span>” en “<span style="background: #ffd6fe">zlib3_files</span>”. Dit lit ús metadata-records apart skraapje en frijlitte fan de eigentlike boekbestannen. Sa hawwe wy twa torrents mei metadata-bestannen frijlitten: Wy hawwe ek in rige torrents mei binêre datamappen frijlitten, mar allinnich foar de “<span style="background: #ffd6fe">zlib3_files</span>” kolleksje, yn totaal 62: Troch <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> út te fieren kinne wy sjen wat der yn sit: Yn dit gefal is it metadata fan in boek sa't rapporteare troch Z-Library. Op it topnivo hawwe wy allinnich “aacid” en “metadata”, mar gjin “data_folder”, om't der gjin oerienkommende binêre data is. De AACID befettet “22430000” as de primêre ID, dy't wy sjogge is nommen fan “zlibrary_id”. Wy kinne ferwachtsje dat oare AAC's yn dizze kolleksje deselde struktuer hawwe. No litte wy <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> útfiere: Dit is in folle lytsere AAC metadata, hoewol it grutste part fan dizze AAC earne oars yn in binêr bestân leit! Uteinlik hawwe wy dizze kear in “data_folder”, dus wy kinne ferwachtsje dat de oerienkommende binêre data leit by <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. De “metadata” befettet de “zlibrary_id”, sadat wy it maklik kinne assosjearje mei de oerienkommende AAC yn de “zlib_records” kolleksje. Wy koene it op ferskate manieren assosjearje, bygelyks fia AACID — de standert skriuwt dat net foar. Merk op dat it ek net nedich is dat it “metadata” fjild sels JSON is. It kin in string wêze dy't XML of in oar dataformaat befettet. Jo kinne sels metadata-ynformaasje opslaan yn de assosjearre binêre blob, bygelyks as it in soad gegevens is. Heterogene bestannen en metadata, sa ticht mooglik by it orizjinele formaat. Binaire gegevens kinne direkt tsjinne wurde troch webservers lykas Nginx. Heterogene identifiers yn de boarne-biblioteken, of sels it ûntbrekken fan identifiers. Separate releases fan metadata tsjin bestânsgegevens, of allinnich metadata-releases (bygelyks ús ISBNdb release). Distribúsje fia torrents, mar mei de mooglikheid fan oare distribúsjemetoaden (bygelyks IPFS). Unferoarlike records, om't wy oannimme moatte dat ús torrents foar ivich bestean bliuwe. Ynkrementele releases / oanfolbere releases. Masine-lêsber en skriuwber, handich en fluch, benammen foar ús stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). In bytsje maklik foar minsklike ynspeksje, hoewol dit sekundêr is oan masine-lêsberens. Maklik om ús kolleksjes te seedjen mei in standert hierde seedbox. Untwerpdoelen Wy jouwe net om dat bestannen maklik manuell op skiif te navigearjen binne, of sykber sûnder foarferwurking. Wy jouwe net om direkt kompatibel te wêzen mei besteande biblioteeksoftware. Hoewol it maklik wêze moat foar elkenien om ús kolleksje te seedjen mei torrents, ferwachtsje wy net dat de bestannen brûkber binne sûnder signifikante technyske kennis en ynset. Us primêre gebrûksgefal is de distribúsje fan bestannen en assosjearre metadata út ferskate besteande kolleksjes. Us wichtichste oerwagings binne: Guon net-doelen: Om't Anna’s Argiven iepen boarne is, wolle wy ús formaat direkt brûke. As wy ús sykindeks fernije, tagongje wy allinnich iepenbier beskikbere paden, sadat elkenien dy't ús biblioteek fork kin fluch oan de slach gean. <strong>AAC.</strong> AAC (Anna’s Argiven Kontener) is in ienich item besteande út <strong>metadata</strong>, en opsjoneel <strong>binaire gegevens</strong>, dy't beide net feroare wurde kinne. It hat in wrâldwiid unyk identifikaasje, neamd <strong>AACID</strong>. <strong>AACID.</strong> It formaat fan AACID is dit: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Bygelyks, in eigentlike AACID dy't wy útjûn hawwe is <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>AACID berik.</strong> Om't AACIDs monotonyk tanimmende timestamps befetsje, kinne wy dat brûke om berikken oan te jaan binnen in bepaalde kolleksje. Wy brûke dit formaat: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wêrby't de timestamps ynklusyf binne. Dit is konsistint mei ISO 8601 notaasjes. Berikken binne trochgeand, en kinne oerlappe, mar yn gefal fan oerlap moatte se identike records befetsje as de earder útjûne yn dy kolleksje (om't AACs net feroare wurde kinne). Mankearjende records binne net tastien. <code>{collection}</code>: de namme fan de kolleksje, dy't ASCII letters, sifers, en underscores befetsje kin (mar gjin dûbele underscores). <code>{collection-specific ID}</code>: in kolleksje-spesifike identifikaasje, as fan tapassing, bygelyks de Z-Library ID. Mei weglitten of ynkoarte wurde. Moat weglitten of ynkoarte wurde as de AACID oars mear as 150 tekens soe wêze. <code>{ISO 8601 timestamp}</code>: in koarte ferzje fan de ISO 8601, altyd yn UTC, bygelyks <code>20220723T194746Z</code>. Dit nûmer moat monotonyk tanimme foar elke útjefte, hoewol't de krekte semantyk ferskille kin per kolleksje. Wy suggerearje it brûken fan de tiid fan scraping of fan it generearjen fan de ID. <code>{shortuuid}</code>: in UUID mar komprimearre nei ASCII, bygelyks mei base57. Wy brûke op it stuit de <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteek. <strong>Binaire gegevensmap.</strong> In map mei de binaire gegevens fan in berik fan AACs, foar ien bepaalde kolleksje. Dizze hawwe de folgjende eigenskippen: De map moat gegevensbestannen befetsje foar alle AACs binnen it spesifisearre berik. Elk gegevensbestân moat syn AACID as de bestânsnamme hawwe (gjin útwreidingen). Mapnamme moat in AACID berik wêze, foarôfgien troch <code style="color: green">annas_archive_data__</code>, en gjin suffix. Bygelyks, ien fan ús eigentlike útjeften hat in map neamd<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. It wurdt oanrikkemandearre om dizze mappen wat behearskber yn grutte te hâlden, bygelyks net grutter as 100GB-1TB elk, hoewol dizze oanrikkemandearring oer tiid feroarje kin. <strong>Kolleksje.</strong> Elke AAC heart by in kolleksje, dy't definiearre wurdt as in list fan AACs dy't semantysk konsistint binne. Dat betsjut dat as jo in wichtige feroaring meitsje oan it formaat fan de metadata, jo in nije kolleksje meitsje moatte. De standert <strong>Metadata-bestân.</strong> In metadata-bestân befettet de metadata fan in berik fan AACs, foar ien bepaalde kolleksje. Dizze hawwe de folgjende eigenskippen: <code>data_folder</code> is opsjoneel, en is de namme fan de binaire gegevensmap dy't de oerienkommende binaire gegevens befettet. De bestânsnamme fan de oerienkommende binaire gegevens binnen dy map is de AACID fan it record. Elk JSON-objekt moat de folgjende fjilden op it boppeste nivo befetsje: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsjoneel). Gjin oare fjilden binne tastien. Bestânsnamme moat in AACID berik wêze, foarôfgien troch <code style="color: red">annas_archive_meta__</code> en folge troch <code>.jsonl.zstd</code>. Bygelyks, ien fan ús útjeften hjit<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Sa't oanjûn troch de bestânsútwreiding, is it bestânstype <a %(jsonlines)s>JSON Lines</a> komprimearre mei <a %(zstd)s>Zstandard</a>. <code>metadata</code> is arbitraire metadata, neffens de semantyk fan de kolleksje. It moat semantysk konsistint wêze binnen de kolleksje. De <code style="color: red">annas_archive_meta__</code> foarheaksel kin oanpast wurde oan de namme fan jo ynstelling, bygelyks <code style="color: red">my_institute_meta__</code>. <strong>“records” en “files” kolleksjes.</strong> Neffens konvinsje is it faak handich om “records” en “files” as ferskillende kolleksjes út te jaan, sadat se op ferskillende skema's útjûn wurde kinne, bygelyks basearre op scraping-tariven. In “record” is in allinne-metadata kolleksje, mei ynformaasje lykas boek titels, auteurs, ISBNs, ensfh., wylst “files” de kolleksjes binne dy't de eigentlike bestannen sels befetsje (pdf, epub). Uteinlik hawwe wy ús fêstlein op in relatyf ienfâldige standert. It is frij los, net-normatyf, en in wurk yn ûntwikkeling. <strong>Torrents.</strong> De metadata-bestannen en binêre datamappen kinne yn torrents bûn wurde, mei ien torrent per metadata-bestân, of ien torrent per binêre datamap. De torrents moatte de orizjinele bestâns-/mapnamme plus in <code>.torrent</code> suffix as har bestânsnamme hawwe. <a %(wikipedia_annas_archive)s>Anna's Argiven</a> is fierwei de grutste skaadbibleteek yn 'e wrâld wurden, en de iennige skaadbibleteek fan dizze skaal dy't folslein iepen-boarne en iepen-data is. Hjirûnder is in tabel fan ús Datasets-side (in bytsje oanpast): Wy hawwe dit op trije manieren berikt: It spegeljen fan besteande iepen-data skaadbibleteken (lykas Sci-Hub en Library Genesis). It helpen fan skaadbibleteken dy't mear iepen wolle wêze, mar net de tiid of middels hiene om dat te dwaan (lykas de Libgen stripferzamelingen). It skrabjen fan bibleteken dy't net yn bulk diele wolle (lykas Z-Library). Foar (2) en (3) beheare wy no sels in grutte kolleksje fan torrents (hûnderten TB's). Oant no ta hawwe wy dizze kolleksjes as ienmalige projekten benadere, wat betsjut dat der foar elke kolleksje maatwurk ynfrastruktuer en gegevensorganisaasje nedich is. Dit foeget in signifikante overhead ta oan elke release, en makket it benammen dreech om mear ynkrementele releases te dwaan. Dêrom hawwe wy besletten om ús releases te standerdisearjen. Dit is in technyske blogpost wêryn wy ús standert yntrodusearje: <strong>Anna’s Argiven Konteners</strong>. Anna's Argiven Containers (AAC): it standerdisearjen fan releases fan 'e grutste skaadbibleteek fan 'e wrâld Anna's Argiven is de grutste skaadbibleteek yn 'e wrâld wurden, wat ús ferplichtet ús releases te standerdisearjen. 300GB+ oan boekomslagen frijlitten Uteinlik binne wy bliid om in lytse frijlitting oan te kundigjen. Yn gearwurking mei de minsken dy't de Libgen.rs fork beheare, diele wy al har boekomslagen fia torrents en IPFS. Dit sil de lading fan it besjen fan de omslaggen ferdiele oer mear masines, en se better bewarje. Yn in protte (mar net alle) gefallen binne de boekomslagen yn de bestannen sels opnommen, dus dit is in soarte fan "ôflaat gegevens". Mar it hawwen yn IPFS is noch hieltyd tige nuttich foar deistige operaasje fan sawol Anna’s Argief as de ferskate Library Genesis forks. Lykas gewoanlik kinne jo dizze frijlitting fine by de Pirate Library Mirror (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argief</a>). Wy sille hjir net nei ferwize, mar jo kinne it maklik fine. Hopelik kinne wy ús tempo wat ferleegje, no't wy in fatsoenlik alternatyf foar Z-Library hawwe. Dizze wurkdruk is net bysûnder duorsum. As jo ynteressearre binne om te helpen mei programmeren, serveroperaasjes, of bewaringswurk, nim dan wis kontakt mei ús op. Der is noch in protte <a %(annas_archive)s>wurk te dwaan</a>. Tank foar jo ynteresse en stipe. Oerskeakelje nei ElasticSearch Guon queries duorren super lang, oant it punt wêr't se alle iepen ferbinings yn beslach namen. Standert hat MySQL in minimale wurdlengte, of jo yndeks kin echt grut wurde. Minsken rapportearren dat se net koene sykje nei "Ben Hur". Sykjen wie allinich wat rap as it folslein yn it ûnthâld laden wie, wat ús twong om in djoerder masine te krijen om dit op út te fieren, plus wat kommando's om de yndeks by opstarten foar te laden. Wy soene it net maklik útwreidzje kinne om nije funksjes te bouwen, lykas bettere <a %(wikipedia_cjk_characters)s>tokenisaasje foar net-wytromte-talen</a>, filterjen/facettering, sortearjen, "bedoelde jo" suggestjes, autokomplete, ensafuorthinne. Ien fan ús <a %(annas_archive)s>kaarten</a> wie in samling fan problemen mei ús syksysteem. Wy brûkten MySQL full-text search, om't wy al ús gegevens yn MySQL hiene. Mar it hie syn grinzen: Nei petearen mei in rige saakkundigen hawwe wy keazen foar ElasticSearch. It hat net perfekt west (harren standert "bedoelde jo" suggestjes en autokomplete funksjes binne min), mar oer it algemien is it in stik better as MySQL foar sykjen. Wy binne noch net <a %(youtube)s>te entûsjast</a> oer it brûken foar misjekrityske gegevens (hoewol se in soad <a %(elastic_co)s>foarútgong</a> makke hawwe), mar oer it algemien binne wy tige tefreden mei de oerskeakeling. Foar no hawwe wy in folle rapper sykjen ymplemintearre, bettere taalstipe, bettere relevânsje-sortearring, ferskate sortearopsjes, en filterjen op taal/boektype/bestândype. As jo benijd binne hoe't it wurket, <a %(annas_archive_l140)s>sjoch</a> <a %(annas_archive_l1115)s>efkes</a> <a %(annas_archive_l1635)s>hjir</a>. It is frij tagonklik, hoewol it wat mear kommentaar brûke kin… Anna’s Argyf is folslein iepen boarne Wy leauwe dat ynformaasje frij wêze moat, en ús eigen koade is gjin útsûndering. Wy hawwe al ús koade frijlitten op ús privee hoste Gitlab-ynstânsje: <a %(annas_archive)s>Anna’s Software</a>. Wy brûke ek de issue tracker om ús wurk te organisearjen. As jo mei ús ûntwikkeling dwaande wolle, is dit in geweldige plak om te begjinnen. Om jo in idee te jaan fan de dingen dêr't wy oan wurkje, nim ús resinte wurk oan kliïnt-side prestaasjeferbetteringen. Om't wy noch gjin paginaasje ymplementearre hawwe, soene wy faak heul lange sykpagina's weromjaan, mei 100-200 resultaten. Wy woene de sykresultaten net te gau ôfbrekke, mar dit betsjutte wol dat it guon apparaten fertrage soe. Hjirfoar hawwe wy in lyts trúk útfierd: wy hawwe de measte sykresultaten yn HTML-kommentaren (<code><!-- --></code>) ynpakt, en doe in lyts Javascript skreaun dat soe detektearje wannear't in resultaat sichtber wurde moat, op dat momint soene wy it kommentaar ûntpakke: DOM "virtualisaasje" ymplementearre yn 23 rigels, gjin needsaak foar fancy biblioteken! Dit is it soarte fan rap pragmatyske koade dat jo einigje mei as jo beheinde tiid hawwe, en echte problemen dy't oplost wurde moatte. It is rapporteare dat ús sykfunksje no goed wurket op stadige apparaten! In oare grutte ynspanning wie it automatisearjen fan it bouwen fan de databank. Doe't wy lansearren, hawwe wy gewoan ferskate boarnen gearfoege. No wolle wy se bywurke hâlde, dus hawwe wy in rige skripts skreaun om nije metadata fan de twa Library Genesis forks te downloaden, en se te yntegrearjen. It doel is net allinich om dit nuttich te meitsjen foar ús argyf, mar ek om it maklik te meitsjen foar elkenien dy't mei skadubiblioteekmetadata boartsje wol. It doel soe in Jupyter-notebook wêze dy't alle soarten ynteressante metadata beskikber hat, sadat wy mear ûndersyk dwaan kinne lykas útfine hokker <a %(blog)s>persintaazje fan ISBNs foar ivich bewarre wurde</a>. Uteinlik hawwe wy ús donaasjesysteem opnij ûntwikkele. Jo kinne no in kredytkaart brûke om direkt jild yn ús crypto-wallets te deponearjen, sûnder echt wat oer cryptocurrencies te witten. Wy sille yn de gaten hâlde hoe goed dit yn de praktyk wurket, mar dit is in grutte stap. Mei Z-Library dy't delgiet en har (alleged) oprjochters dy't arrestearre wurde, hawwe wy rûn de klok wurke om in goed alternatyf te bieden mei Anna’s Argyf (wy sille it hjir net keppelje, mar jo kinne it op Google fine). Hjir binne guon fan de dingen dy't wy koartlyn berikt hawwe. Anna’s Update: folslein iepen boarne argyf, ElasticSearch, 300GB+ oan boekomslagen Wy hawwe rûn de klok wurke om in goed alternatyf te bieden mei Anna’s Argyf. Hjir binne guon fan de dingen dy't wy koartlyn berikt hawwe. Analyse Semantyske duplikaten (ferskillende scans fan itselde boek) kinne teoretysk útfiltere wurde, mar it is lestich. By it manuell trochsykjen fan 'e strips fûnen wy te folle falske posityven. Der binne guon duplikaten allinnich troch MD5, wat relatyf fergriemerich is, mar it útfiterjen fan dy soe ús mar sa'n 1% in besparring jaan. Op dizze skaal is dat noch altyd sa'n 1TB, mar ek, op dizze skaal makket 1TB net echt út. Wy wolle leaver net it risiko rinne om per ongelok gegevens te ferneatigjen yn dit proses. Wy fûnen in soad net-boekgegevens, lykas films basearre op stripboeken. Dat liket ek fergriemerich, om't dizze al breed beskikber binne fia oare middels. Lykwols, wy realisearren ús dat wy net gewoan filmtriemen útfiterje koene, om't der ek <em>ynteraktive stripboeken</em> binne dy't op 'e kompjûter útbrocht waarden, dy't immen opnaam en opslein hat as films. Uteinlik soe de kolleksje wiskje soe mar in pear prosint besparje. Doe betochten wy ús dat wy datahoarders binne, en de minsken dy't dit spegelje sille ek datahoarders binne, en dus, "WAT BEDOELSTO, WISKE?!" As jo 95TB yn jo opslachkluster dumpe, besykje jo te begripen wat der sels yn sit… Wy diene wat analyse om te sjen oft wy de grutte wat ferminderje koene, lykas troch duplikaten te ferwiderjen. Hjir binne guon fan ús befiningen: Wy presintearje jo dêrom de folsleine, net oanpaste kolleksje. It is in soad data, mar wy hoopje dat genôch minsken it hoe dan ek seedje sille. Gearwurking Jûn syn grutte, hat dizze kolleksje al lang op ús winskelist stien, dus nei ús súkses mei it bewarjen fan Z-Library, rjochten wy ús op dizze kolleksje. Earst skraapten wy it direkt, wat in aardige útdaging wie, om't harren server net yn 'e bêste kondysje wie. Wy krigen sa'n 15TB op dizze manier, mar it gie stadich. Gelokkich slagge it ús om yn kontakt te kommen mei de operator fan 'e bibleteek, dy't ynstimde om ús alle gegevens direkt te stjoeren, wat in stik flugger wie. It duorre noch mear as in heal jier om alle gegevens oer te bringen en te ferwurkjen, en wy hawwe hast alles ferlern troch skiifkorruptie, wat betsjutte soe dat wy opnij begjinne moasten. Dizze ûnderfining hat ús leauwe litten dat it wichtich is om dizze gegevens sa gau mooglik út te bringen, sadat it breed spegele wurde kin. Wy binne mar ien of twa ûngelokkich timed ynsidinten fuort fan it foar altyd ferliezen fan dizze kolleksje! De kolleksje Fluch bewege betsjut wol dat de kolleksje in bytsje ûnorganisearre is… Litte wy efkes sjen. Stel jo foar dat wy in bestânsysteem hawwe (dat wy yn werklikheid oer torrents ferdiele): De earste map, <code>/repository</code>, is it mear strukturearre diel hjirfan. Dizze map befettet saneamde "tûzen dirs": mappen elk mei tûzenen bestannen, dy't ynkrementaal nûmere binne yn 'e database. Map <code>0</code> befettet bestannen mei comic_id 0–999, en sa fierder. Dit is itselde skema dat Library Genesis brûkt hat foar syn fiksje- en non-fiksjekolleksjes. It idee is dat elke "tûzen dir" automatysk omset wurdt yn in torrent sa gau't it fol is. Lykwols, de Libgen.li operator hat nea torrents makke foar dizze kolleksje, en dus waarden de tûzen dirs wierskynlik ûngemaklik, en makken plak foar "ûnsoartearre dirs". Dizze binne <code>/comics0</code> troch <code>/comics4</code>. Se befetsje allegear unike mapstrukturen, dy't wierskynlik sin makken foar it sammeljen fan 'e bestannen, mar meitsje no net folle sin foar ús. Gelokkich ferwiist de metadata noch direkt nei al dizze bestannen, dus harren opslachorganisaasje op skiif makket eins net út! De metadata is beskikber yn 'e foarm fan in MySQL-database. Dit kin direkt fan 'e Libgen.li-webside downloade wurde, mar wy sille it ek beskikber meitsje yn in torrent, neist ús eigen tabel mei alle MD5-hashes. <q>Dr. Barbara Gordon besiket harsels te ferliezen yn 'e gewoane wrâld fan 'e bibleteek…</q> Libgen forks Earst, wat eftergrûn. Jo kinne Library Genesis kenne foar harren epyske boekekolleksje. Minder minsken witte dat Library Genesis-frijwilligers oare projekten makke hawwe, lykas in grutte kolleksje fan tydskriften en standertdokuminten, in folsleine backup fan Sci-Hub (yn gearwurking mei de oprjochter fan Sci-Hub, Alexandra Elbakyan), en yndie, in grutte kolleksje fan strips. Op in stuit giene ferskate operators fan Library Genesis-spegeltsjes harren eigen wei, wat late ta de hjoeddeistige situaasje fan it hawwen fan in oantal ferskillende "forks", dy't allegear noch de namme Library Genesis drage. De Libgen.li fork hat unyk dizze stripskolleksje, lykas in grutte tydskriftenkolleksje (wêr't wy ek oan wurkje). Jildynsammeling Wy bringe dizze data út yn grutte brokken. De earste torrent is fan <code>/comics0</code>, dy't wy yn ien grutte 12TB .tar-bestân dien hawwe. Dat is better foar jo hurde skiif en torrentsoftware as in gazillion lytsere bestannen. As diel fan dizze release dogge wy in jildynsammeling. Wy besykje $20,000 op te heljen om de operasjonele en kontraktkosten foar dizze kolleksje te dekken, en ek om trochgeande en takomstige projekten mooglik te meitsjen. Wy hawwe wat <em>massive</em> yn 'e wurken. <em>Wa stypje ik mei myn donaasje?</em> Koartsein: wy bewarje alle kennis en kultuer fan 'e minskheid, en meitsje it maklik tagonklik. Al ús koade en data binne iepen boarne, wy binne in folslein frijwilligersprojekt, en wy hawwe oant no ta 125TB oan boeken bewarre (neist de besteande torrents fan Libgen en Scihub). Uteinlik bouwe wy in flywheel dat minsken ynskeakelje en stimulearje om alle boeken yn 'e wrâld te finen, te scannen en te bewarjen. Wy sille yn in takomstige post oer ús masterplan skriuwe. :) As jo donearje foar in 12-moanne "Amazing Archivist" lidmaatskip ($780), kinne jo <strong>“in torrent adoptearje”</strong>, wat betsjut dat wy jo brûkersnamme of berjocht yn de bestânsnamme fan ien fan 'e torrents sette! Jo kinne donearje troch nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a> te gean en op de knop "Donearje" te klikken. Wy sykje ek mear frijwilligers: software-yngenieurs, befeiligingsûndersikers, anonyme hannelseksperts, en oersetters. Jo kinne ús ek stypje troch hostingtsjinsten te leverjen. En fansels, seed ús torrents! Tank oan elkenien dy't ús al sa genereus stipe hat! Jo meitsje echt in ferskil. Hjir binne de torrents dy't oant no ta útbrocht binne (wy binne noch dwaande mei de rest): Alle torrents kinne fûn wurde op <a %(wikipedia_annas_archive)s>Anna’s Argyf</a> ûnder "Datasets" (wy keppelje dêr net direkt, sadat keppelings nei dizze blog net fan Reddit, Twitter, ensfh. fuorthelle wurde). Fan dêrút, folgje de keppeling nei de Tor-webside. <a %(news_ycombinator)s>Diskusjearje op Hacker News</a> Wat komt dernei? In protte torrents binne geweldich foar langduorjende behâld, mar net sa folle foar deistige tagong. Wy sille wurkje mei hostingpartners om al dizze data op it web te krijen (om't Anna’s Argyf neat direkt hostet). Fansels kinne jo dizze downloadlinks fine op Anna’s Argyf. Wy noegje elkenien ek út om dingen mei dizze data te dwaan! Help ús it better te analysearjen, te deduplikearjen, op IPFS te setten, it te remixen, jo AI-modellen dermei te trainen, en sa fierder. It is allegear fan jo, en wy kinne net wachtsje om te sjen wat jo dermei dogge. Uteinlik, lykas earder sein, hawwe wy noch wat massive releases yn 'e pipeline (as <em>immen</em> ús per <em>ûngelok</em> in dump fan in <em>bepaalde</em> ACS4-database stjoere koe, jo witte wêr't jo ús fine kinne...), en ek it bouwen fan it flywheel foar it bewarjen fan alle boeken yn 'e wrâld. Dus bliuw op 'e hichte, wy binne krekt begûn. - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) De grutste skadubiblioteek foar stripboeken is wierskynlik dy fan in spesifike Library Genesis fork: Libgen.li. De ienige behearder dy't dy side rint, slagge deryn om in ûnfoarstelbere kolleksje fan strips te sammeljen fan mear as 2 miljoen bestannen, mei in totaal fan mear as 95TB. Mar, oars as oare Library Genesis-kolleksjes, wie dizze net yn bulk beskikber fia torrents. Jo koene dizze strips allinich yndividueel tagong krije fia syn stadige persoanlike server — in ienige mislearringspunt. Oant hjoed! Yn dizze post sille wy jo mear fertelle oer dizze kolleksje, en oer ús fundraiser om mear fan dit wurk te stypjen. Anna’s Argief hat de grutste skadubiblioteek foar strips yn 'e wrâld bewarre (95TB) — jo kinne helpe om it te seedjen De grutste skadubiblioteek foar stripboeken yn 'e wrâld hie in ienige mislearringspunt.. oant hjoed. Warskôging: dizze blogpost is ferâldere. Wy hawwe besletten dat IPFS noch net klear is foar de haadtiid. Wy sille noch altyd keppelings nei bestannen op IPFS fan Anna's Argiven jaan as mooglik, mar wy sille it net mear sels hostje, noch riede wy oaren oan om te spegeljen mei help fan IPFS. Sjoch asjebleaft ús Torrents-side as jo ús kolleksje helpe wolle behâlde. 5.998.794 boeken op IPFS sette In fermannichfâldiging fan kopyen Werom nei ús oarspronklike fraach: hoe kinne wy oanspraak meitsje op it bewarjen fan ús kolleksjes foar ivich? It haadprobleem hjir is dat ús kolleksje <a %(torrents_stats)s>rap groeit</a>, troch it skraapjen en iepenboarnen fan guon massale kolleksjes (boppe op it geweldige wurk dat al dien is troch oare iepen-data skaadbibleteken lykas Sci-Hub en Library Genesis). Dizze groei yn gegevens makket it dreger foar de kolleksjes om oer de hiele wrâld spegele te wurden. Gegevensopslach is djoer! Mar wy binne optimistysk, foaral as wy de folgjende trije trends observearje. De <a %(annas_archive_stats)s>totale grutte</a> fan ús kolleksjes, oer de lêste pear moannen, ferdield troch it oantal torrent seeders. HDD-prizen trends fan ferskate boarnen (klik om de stúdzje te besjen). <a %(critical_window_chinese)s>Sineeske ferzje 中文版</a>, diskusjearje op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Wy hawwe de leechhingjende fruchten plukt Dizze folget direkt út ús prioriteiten dy't hjirboppe besprutsen binne. Wy leaver wurkje oan it befrijen fan grutte kolleksjes earst. No't wy guon fan de grutste kolleksjes yn 'e wrâld feilich steld hawwe, ferwachtsje wy dat ús groei folle stadiger sil wêze. Der is noch in lange sturt fan lytsere kolleksjes, en nije boeken wurde elke dei skand of publisearre, mar it tempo sil wierskynlik folle stadiger wêze. Wy kinne noch dûbel of sels trijefâldich yn grutte wurde, mar oer in langere perioade. Ferbetteringen yn OCR. Prioriteiten Wittenskip & yngenieurswittenskip softwarekoade Fiksjonele of ferdivedaasjeferzjes fan al it boppesteande Geografyske gegevens (bgl. kaarten, geologyske ûndersiken) Ynterne gegevens fan bedriuwen of oerheden (lekkens) Mjitgegevens lykas wittenskiplike mjittingen, ekonomyske gegevens, bedriuwsrapporten Metadata records yn it algemien (fan non-fiksje en fiksje; fan oare media, keunst, minsken, ensfh.; ynklusyf resinsjes) Non-fiksje boeken Non-fiksje tydskriften, kranten, hantliedingen Non-fiksje transkripsjes fan lêzingen, dokumintêres, podcasts Organyske gegevens lykas DNA-sekwinsjes, plantesied, of mikrobiale samples Akademyske papers, tydskriften, rapporten Wittenskip & yngenieurswittenskip websiden, online diskusjes Transkripsjes fan juridyske of rjochtbankprosedueres Unyk yn gefaar fan ferneatiging (bgl. troch oarloch, besunigings, rjochtsaken, of politike ferfolging) Seldsum Unyk ûnderfokusearre Wêrom soargje wy sa folle oer papers en boeken? Litte wy ús fûnemintele leauwen yn bewarjen yn it algemien oan 'e kant sette — wy kinne dêr in oare post oer skriuwe. Dus wêrom spesifyk papers en boeken? It antwurd is simpel: <strong>ynformaasjetichtheid</strong>. Per megabyte opslach, bewarret skreaune tekst de measte ynformaasje fan alle media. Wylst wy soargje foar sawol kennis as kultuer, soargje wy mear foar it earste. Yn it algemien fine wy in hiërargy fan ynformaasjetichtheid en belang fan bewarjen dy't der sa útsjocht: De ranglist yn dizze list is wat arbitêr - ferskate items binne lyk of hawwe ûnienigens binnen ús team - en wy ferjitte wierskynlik guon wichtige kategoryen. Mar dit is rûchwei hoe't wy prioritearje. Guon fan dizze items binne te oars foar ús om ús soargen oer te meitsjen (of wurde al fersoarge troch oare ynstellingen), lykas organyske gegevens of geografyske gegevens. Mar de measte items yn dizze list binne eins wichtich foar ús. In oare grutte faktor yn ús priorisearring is hoefolle risiko in bepaald wurk rint. Wy leaver fokusje op wurken dy't: Uteinlik, wy soargje oer skaal. Wy hawwe beheinde tiid en jild, dus wy besteegje leaver in moanne oan it rêden fan 10.000 boeken dan 1.000 boeken - as se sawat like weardefol en yn gefaar binne. <em><q>It ferlerne kin net weromfûn wurde; mar lit ús bewarje wat oerbliuwt: net troch kluzen en slotten dy't se fan it publyk each en gebrûk ôfskermje, yn it oerjaan oan de slop fan 'e tiid, mar troch sa'n fermannichfâldiging fan kopyen, dat se bûten it berik fan tafal pleatst wurde.</q></em><br>— Thomas Jefferson, 1791 Skaadbiblioteken Koade kin iepen boarne wêze op Github, mar Github as gehiel kin net maklik spegele wurde en dus bewarre wurde (hoewol yn dit spesifike gefal der genôch ferspraat kopyen binne fan de measte koade repositories) Metadata-records kinne frij besjoen wurde op de Worldcat-webside, mar net yn bulk downloade wurde (oant wy se <a %(worldcat_scrape)s>skraapten</a>) Reddit is fergees te brûken, mar hat koartlyn strange anty-skraapmaatregels ynfierd, yn it ljocht fan data-hongerige LLM-training (mear dêroer letter) Der binne in protte organisaasjes dy't ferlykbere missys hawwe, en ferlykbere prioriteiten. Yndie, der binne biblioteken, argiven, laboratoaria, musea, en oare ynstellingen dy't belêstge binne mei it behâld fan dit soarte. In protte fan dy binne goed finansierd, troch oerheden, yndividuen, of bedriuwen. Mar se hawwe ien grutte bline flek: it juridyske systeem. Hjiryn leit de unike rol fan skaadbiblioteken, en de reden wêrom't Anna's Argive bestiet. Wy kinne dingen dwaan dy't oare ynstellingen net meie. No, it is net (faak) dat wy materialen argivearje kinne dy't yllegaal binne om oars te behâlden. Nee, it is yn in protte plakken legaal om in argyf te bouwen mei alle boeken, papieren, tydskriften, ensfh. Mar wat juridyske argiven faak misse is <strong>redundânsje en duorsumens</strong>. Der binne boeken wêrfan't mar ien eksimplaar bestiet yn in fysike bibleteek earne. Der binne metadata-records dy't troch ien inkele korporaasje bewarre wurde. Der binne kranten dy't allinnich op mikrofilm yn in inkele argyf bewarre wurde. Bibleteken kinne finansiering ferlieze, korporaasjes kinne fallyt gean, argiven kinne bombardearre en ferbaarnd wurde. Dit is net hypotetysk — dit bart hieltyd. It ding dat wy unyk dwaan kinne by Anna’s Argiven is in protte kopyen fan wurken opslaan, op grutte skaal. Wy kinne papieren, boeken, tydskriften en mear sammelje en se yn bulk ferspriede. Wy dogge dit op it stuit fia torrents, mar de krekte technologyen meitsje net út en sille oer tiid feroarje. It wichtichste diel is it fersprieden fan in protte kopyen oer de hiele wrâld. Dit sitaat fan mear as 200 jier lyn is noch altyd wier: In koarte opmerking oer it iepenbier domein. Om't Anna’s Argiven unyk rjochte is op aktiviteiten dy't yllegaal binne yn in protte plakken oer de hiele wrâld, meitsje wy ús gjin soargen oer breed beskikbere kolleksjes, lykas boeken yn it iepenbier domein. Juridyske entiteiten soargje dêr faak al goed foar. Lykwols, der binne oerwagings dy't ús soms meitsje wurkje oan iepenbier beskikbere kolleksjes: - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Opslachkosten bliuwe eksponentieel sakje 3. Ferbetteringen yn ynformaasjetichtens Wy bewarje op it stuit boeken yn de rauwe formaten dy't se oan ús jûn wurde. Wis, se binne komprimearre, mar faak binne it noch grutte scans of foto's fan siden. Oant no ta wiene de iennige opsjes om de totale grutte fan ús kolleksje te ferminderjen mear agressive kompresje of deduplikearring. Mar om genôch besparring te krijen, binne beide te ferliesryk foar ús smaak. Swiere kompresje fan foto's kin tekst amper lêsber meitsje. En deduplikearring fereasket hege fertrouwen dat boeken presys itselde binne, wat faak te ûnkrekt is, benammen as de ynhâld itselde is, mar de scans op ferskillende mominten makke binne. Der hat altyd in tredde opsje west, mar de kwaliteit wie sa min dat wy it nea beskôge hawwe: <strong>OCR, of Optyske Karaktererkenning</strong>. Dit is it proses fan it konvertearjen fan foto's nei gewoane tekst, troch AI te brûken om de karakters yn de foto's te detektearjen. Ark foar dit besteane al lang, en binne aardich goed, mar "aardich goed" is net genôch foar bewaringsdoelen. Lykwols hawwe resinte multi-modale djip-learmodellen ekstreem rappe foarútgong makke, hoewol noch tsjin hege kosten. Wy ferwachtsje dat sawol de krektens as de kosten dramatysk sille ferbetterje yn de kommende jierren, oant it punt dat it realistysk wurdt om it op ús hiele bibleteek ta te passen. As dat bart, sille wy wierskynlik noch de orizjinele bestannen bewarje, mar derneist kinne wy in folle lytsere ferzje fan ús bibleteek hawwe dy't de measte minsken spegelje wolle. It moaie is dat rauwe tekst sels noch better komprimearret, en folle makliker te deduplikearjen is, wat ús noch mear besparring jout. Yn it algemien is it net ûnrealistysk om teminsten in 5-10x reduksje yn totale bestânsgrutte te ferwachtsjen, miskien sels mear. Sels mei in konservative 5x reduksje, soene wy sjogge nei <strong>$1,000–$3,000 yn 10 jier sels as ús bibleteek trijefâldich groeit</strong>. Op it momint fan skriuwen binne <a %(diskprices)s>skiifprizen</a> per TB sawat $12 foar nije skiiven, $8 foar brûkte skiiven, en $4 foar tape. As wy konservatyf binne en allinnich nei nije skiiven sjogge, betsjut dat dat it opslaan fan in petabyte sawat $12,000 kostet. As wy oannimme dat ús bibleteek trijefâldich sil groeie fan 900TB nei 2.7PB, soe dat betsjutte $32,400 om ús hiele bibleteek te spegeljen. Mei elektrisiteit, kosten fan oare hardware, en sa fierder, litte wy it rûn meitsje op $40,000. Of mei tape mear as $15,000–$20,000. Oan de iene kant is <strong>$15,000–$40,000 foar de som fan alle minsklike kennis in koopje</strong>. Oan de oare kant is it in bytsje steil om tonnen folsleine kopyen te ferwachtsjen, foaral as wy ek wolle dat dy minsken har torrents trochgean te seedjen foar it foardiel fan oaren. Dat is hjoed. Mar foarútgong giet troch: Hurde skiifkosten per TB binne rûchwei yn trijeën dield oer de lêste 10 jier, en sille wierskynlik op in ferlykber tempo trochgean te sakjen. Tape liket op in ferlykbere trajekt te wêzen. SSD-prizen sakje noch flugger, en kinne oan 'e ein fan it desennium de HDD-prizen oernimme. As dit hâldt, dan kinne wy oer 10 jier mar $5,000–$13,000 nedich hawwe om ús hiele kolleksje te spegeljen (1/3e), of sels minder as wy minder yn grutte groeie. Wylst noch in protte jild, sil dit berikber wêze foar in protte minsken. En it kin sels better wêze fanwegen it folgjende punt… By Anna’s Argiven wurde wy faak frege hoe't wy kinne beweare ús kolleksjes foar ivich te bewarjen, wylst de totale grutte al tichtby 1 Petabyte (1000 TB) is, en noch groeit. Yn dit artikel sille wy nei ús filosofy sjen, en sjen wêrom't it folgjende desennium kritysk is foar ús missy om de kennis en kultuer fan 'e minskheid te bewarjen. Kritike finster As dizze prognosen krekt binne, moatte wy <strong>gewoan in pear jier wachtsje</strong> foardat ús hiele kolleksje breed spegele wurdt. Sa, yn de wurden fan Thomas Jefferson, "pleatst bûten it berik fan tafal." Spitigernôch hat de komst fan LLM's, en harren data-hongerige training, in protte auteursrjochtshâlders op de ferdigening set. Noch mear as se al wiene. In protte websiden meitsje it dreger om te skraapjen en te argivearjen, rjochtsaken fleane om, en ûnderwilens wurde fysike bibleteken en argiven trochgean negearre. Wy kinne allinnich ferwachtsje dat dizze trends trochgean te fersmoarjen, en in protte wurken ferlern gean lang foardat se it publike domein yngeane. <strong>Wy steane op de foarjûn fan in revolúsje yn bewarjen, mar <q>it ferlerne kin net weromfûn wurde.</q></strong> Wy hawwe in krityk finster fan sawat 5-10 jier wêryn it noch frij djoer is om in skaadbibleteek te operearjen en in protte spegels oer de wrâld te meitsjen, en wêryn tagong noch net folslein ôfsletten is. As wy dit finster kinne oerbrêgje, dan hawwe wy yndie de kennis en kultuer fan 'e minskheid foar ivich bewarre. Wy moatte dizze tiid net fergrieme. Wy moatte net tastean dat dit krityk finster foar ús slút. Litte wy gean. It krityske finster fan skaadbiblioteken Hoe kinne wy bewearje ús kolleksjes foar ivich te bewarjen, wylst se al tichtby 1 PB komme? Kolleksje Wat mear ynformaasje oer de kolleksje. <a %(duxiu)s>Duxiu</a> is in grutte databank fan skande boeken, makke troch de <a %(chaoxing)s>SuperStar Digital Library Group</a>. De measten binne akademyske boeken, skand om se digitaal beskikber te meitsjen foar universiteiten en biblioteken. Foar ús Ingelskpratende publyk hawwe <a %(library_princeton)s>Princeton</a> en de <a %(guides_lib_uw)s>Universiteit fan Washington</a> goede oersichten. Der is ek in treflik artikel dat mear eftergrûn jout: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (sykje it op yn Anna's Argiven). De boeken fan Duxiu binne al lang piratere op it Sineeske ynternet. Meastentiids wurde se ferkocht foar minder as in dollar troch opkeapers. Se wurde typysk ferspraat mei help fan it Sineeske ekwivalint fan Google Drive, dat faak hackt is om mear opslachromte mooglik te meitsjen. Guon technyske details kinne fûn wurde <a %(github_duty_machine)s>hjir</a> en <a %(github_821_github_io)s>hjir</a>. Hoewol de boeken semi-iepenbier ferspraat binne, is it frij dreech om se yn bulk te krijen. Wy hiene dit heech op ús TODO-list, en hawwe meardere moannen foltiids wurk derfoar ynpland. Lykwols, koartlyn hat in ynkringende, geweldige en talintearre frijwilliger ús berikt, dy't ús fertelde dat se al dit wurk al dien hiene — tsjin grutte kosten. Se dielden de folsleine kolleksje mei ús, sûnder wat werom te ferwachtsjen, útsein de garânsje fan langduorjende behâld. Wier bûtengewoan. Se stimden yn om op dizze manier om help te freegjen om de kolleksje OCR'e te krijen. De kolleksje bestiet út 7.543.702 bestannen. Dit is mear as Library Genesis non-fiksje (sawat 5,3 miljoen). De totale bestânsgrutte is sawat 359TB (326TiB) yn syn hjoeddeistige foarm. Wy steane iepen foar oare foarstellen en ideeën. Nim gewoan kontakt mei ús op. Besjoch Anna's Argiven foar mear ynformaasje oer ús kolleksjes, behâldsinspanningen, en hoe't jo helpe kinne. Tankewol! Foarbyldsiden Om ús te bewizen dat jo in goede pipeline hawwe, binne hjir wat foarbyldsiden om mei te begjinnen, út in boek oer supergeleiers. Jo pipeline moat goed omgean mei wiskunde, tabellen, grafiken, fuotnoaten, ensafuorthinne. Stjoer jo ferwurke siden nei ús e-mailadres. As se der goed útsjogge, sille wy jo mear yn privee stjoere, en wy ferwachtsje dat jo jo pipeline dêr ek fluch op rinne kinne. As wy tefreden binne, kinne wy in oerienkomst meitsje. - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Sineeske ferzje 中文版</a>, <a %(news_ycombinator)s>Diskusjearje op Hacker News</a> Dit is in koart blogberjocht. Wy sykje in bedriuw of ynstelling om ús te helpen mei OCR en tekstekstraksje foar in massale kolleksje dy't wy oernommen hawwe, yn ruil foar eksklusive iere tagong. Nei de embargo-perioade sille wy fansels de hiele kolleksje frijjaan. Heechweardich akademyske teksten binne tige nuttich foar de training fan LLMs. Hoewol ús kolleksje Sineesk is, kin dit sels nuttich wêze foar de training fan Ingelske LLMs: modellen lykje konsepten en kennis te enkodearjen ûnôfhinklik fan de boarnetaal. Hjirfoar moat tekst út de scans helle wurde. Wat krijt Anna's Argiven hjirút? Folsleine tekstsykjen fan de boeken foar har brûkers. Om't ús doelen oerienkomme mei dy fan LLM-ûntwikkelders, sykje wy in gearwurker. Wy binne ree om jo <strong>eksklusive betide tagong ta dizze kolleksje yn bulk foar 1 jier</strong> te jaan, as jo goede OCR en tekstekstraksje dwaan kinne. As jo ree binne om de hiele koade fan jo pipeline mei ús te dielen, soene wy ree wêze om de kolleksje langer te embargoearjen. Eksklusyf tagong foar LLM-bedriuwen ta de grutste Sineeske non-fiksje boekekolleksje yn 'e wrâld <em><strong>TL;DR:</strong> Anna's Argive hat in unike kolleksje fan 7,5 miljoen / 350TB Sineeske non-fiksje boeken oernommen — grutter as Library Genesis. Wy binne ree om in LLM-bedriuw eksklusive tagong te jaan, yn ruil foar heechweardige OCR en tekstekstraksje.</em> Systeemarsjitektuer Lit ús sizze dat jo wat bedriuwen fûn hawwe dy't ree binne om jo webside te hosten sûnder jo del te heljen — lit ús dizze "frijheidsleafhawwende oanbieders" neame 😄. Jo sille gau fernimme dat alles by harren hosten frij djoer is, dus jo wolle miskien wat "goedkeape oanbieders" fine en dêr it eigentlike hosten dwaan, troch de frijheidsleafhawwende oanbieders te proxyen. As jo it goed dogge, sille de goedkeape oanbieders nea witte wat jo hoste, en nea klachten ûntfange. Mei al dizze oanbieders is der in risiko dat se jo dochs delhelje, dus jo hawwe ek redundânsje nedich. Wy hawwe dit op alle nivo's fan ús stack nedich. Ien wat frijheidsleafhawwend bedriuw dat himsels yn in ynteressante posysje set hat, is Cloudflare. Se hawwe <a %(blog_cloudflare)s>argumintearre</a> dat se gjin hostingprovider binne, mar in nutsbedriuw, lykas in ISP. Se binne dêrom net ûnderwerp fan DMCA- of oare takedown-oanfragen, en stjoere alle oanfragen troch nei jo eigentlike hostingprovider. Se binne sa fier gien as nei de rjochtbank te gean om dizze struktuer te beskermjen. Wy kinne se dêrom brûke as in oare laach fan caching en beskerming. Cloudflare akseptearret gjin anonyme betellingen, dus wy kinne allinnich har fergese plan brûke. Dit betsjut dat wy har load balancing of failover-funksjes net kinne brûke. Wy hawwe dêrom <a %(annas_archive_l255)s>dit sels ymplemintearre</a> op domeinnivo. By it laden fan de side sil de browser kontrolearje oft it hjoeddeistige domein noch beskikber is, en as net, herskriuwt it alle URL's nei in oar domein. Om't Cloudflare in protte siden cachet, betsjut dit dat in brûker op ús haaddomein kin lânje, sels as de proxyserver del is, en dan by de folgjende klik nei in oar domein ferpleatst wurde kin. Wy hawwe ek noch normale operasjonele soargen om mei te dwaan, lykas it kontrolearjen fan de sûnens fan de server, it loggen fan backend- en frontend-flaters, ensafuorthinne. Us failover-arsjitektuer makket mear robuustens op dit front mooglik, bygelyks troch in folslein oare set servers op ien fan 'e domeinen te draaien. Wy kinne sels âldere ferzjes fan 'e koade en datasets op dit aparte domein draaie, foar it gefal dat in krityske bug yn 'e haadferzje ûngemurken bliuwt. Wy kinne ek in hedge meitsje tsjin Cloudflare dy't tsjin ús keart, troch it fan ien fan 'e domeinen te ferwiderjen, lykas dit aparte domein. Ferskillende permutaasjes fan dizze ideeën binne mooglik. Konklúzje It hat in ynteressante ûnderfining west om te learen hoe't jo in robúste en elastyske skaadbibleteek-sykmasine opsette. Der binne noch folle mear details te dielen yn lettere berjochten, lit my witte wêr't jo mear oer leare wolle! Lykas altyd sykje wy nei donaasjes om dit wurk te stypjen, dus soargje derfoar dat jo de Donear-side op Anna’s Argiven besjogge. Wy sykje ek nei oare soarten stipe, lykas subsydzjes, lange-termyn sponsors, heech-risiko betellingsproviders, miskien sels (smakfolle!) advertinsjes. En as jo jo tiid en feardichheden bydrage wolle, sykje wy altyd nei ûntwikkelders, oersetters, ensafuorthinne. Tank foar jo ynteresse en stipe. Ynnovaasjetokens Litte wy begjinne mei ús techstack. It is mei opsetsin saai. Wy brûke Flask, MariaDB, en ElasticSearch. Dat is it letterlik. Sykjen is foar it grutste part in oplost probleem, en wy binne net fan doel it op 'e nij út te finen. Boppedat moatte wy ús <a %(mcfunley)s>ynnovaasjetokens</a> oan wat oars besteegje: net út 'e loft helle wurde troch de autoriteiten. Hoe legaal of yllegaal is Anna’s Argiven krekt? Dit hinget foaral ôf fan de juridyske jurisdiksje. De measte lannen leauwe yn in foarm fan auteursrjocht, wat betsjut dat minsken of bedriuwen in eksklusyf monopolie krije op bepaalde soarten wurken foar in bepaalde perioade. As in sydnoat, by Anna’s Argiven leauwe wy dat, hoewol't der wat foardielen binne, auteursrjocht oer it algemien in netto-negatyf is foar de maatskippij — mar dat is in ferhaal foar in oare kear. Dit eksklusive monopolie op bepaalde wurken betsjut dat it yllegaal is foar elkenien bûten dit monopolie om dy wurken direkt te fersprieden — ynklusyf ús. Mar Anna’s Argiven is in sykmasine dy't dy wurken net direkt ferspriedt (teminsten net op ús clearnet-webside), dus wy soene goed wêze, toch? Net krekt. Yn in protte jurisdiksjes is it net allinnich yllegaal om auteursrjochtlik beskerme wurken te fersprieden, mar ek om te keppeljen nei plakken dy't dat dogge. In klassyk foarbyld hjirfan is de Amerikaanske DMCA-wet. Dat is it strangste ein fan it spektrum. Oan de oare kant fan it spektrum kinne der teoretysk lannen wêze sûnder auteursrjochtwetten, mar dy besteane eins net. Hast elk lân hat in foarm fan auteursrjochtwet op 'e boeken. Hanthavening is in oar ferhaal. Der binne genôch lannen mei oerheden dy't net ynteressearre binne yn it hanthavenjen fan auteursrjochtwetten. Der binne ek lannen tusken de twa ekstreemen, dy't it fersprieden fan auteursrjochtlik beskerme wurken ferbiede, mar net it keppeljen nei sokke wurken. In oare oerweging is op it bedriuwsnivo. As in bedriuw operearret yn in jurisdiksje dy't net om auteursrjocht jout, mar it bedriuw sels is net ree om risiko te nimmen, dan kinne se jo webside slute sa gau't immen deroer kleit. Uteinlik is in grutte oerweging betellingen. Om't wy anonym bliuwe moatte, kinne wy gjin tradisjonele betelmethoden brûke. Dit lit ús oer mei kryptomunten, en mar in lyts diel fan 'e bedriuwen stipet dy (der binne firtuele debitkaarten betelle mei krypto, mar dy wurde faak net akseptearre). - Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Ik rin <a %(wikipedia_annas_archive)s>Anna's Argiven</a>, de grutste iepen boarne non-profit sykmasine foar <a %(wikipedia_shadow_library)s>skaadbiblioteken</a>, lykas Sci-Hub, Library Genesis, en Z-Library. Us doel is om kennis en kultuer maklik tagonklik te meitsjen, en úteinlik in mienskip te bouwen fan minsken dy't tegearre <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle boeken yn 'e wrâld</a> argivearje en behâlde. Yn dit artikel sil ik sjen litte hoe't wy dizze webside rinne, en de unike útdagings dy't komme mei it operearjen fan in webside mei twifelachtige juridyske status, om't der gjin "AWS foar skaadgoede doelen" is. <em>Besjoch ek it susterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Hoe in piraat-argivaris te wurden</a>.</em> Hoe in skaadbiblioteek te rinnen: operaasjes by Anna's Argiven Der is gjin <q>AWS foar skaadgoede doelen,</q> dus hoe rinne wy Anna's Argiven? Ark Applikaasjeserver: Flask, MariaDB, ElasticSearch, Docker. Untwikkeling: Gitlab, Weblate, Zulip. Serverbehear: Ansible, Checkmk, UFW. Onion statyske hosting: Tor, Nginx. Proxyserver: Varnish. Litte wy sjen nei hokker ark wy brûke om dit alles te berikken. Dit is hieltyd yn ûntwikkeling as wy nije problemen tsjinkomme en nije oplossingen fine. Der binne guon besluten dêr't wy hinne en wer oer gien binne. Ien dêrfan is de kommunikaasje tusken servers: wy brûkten eartiids Wireguard hjirfoar, mar fûnen dat it soms ophâldt mei it ferstjoeren fan gegevens, of allinnich gegevens yn ien rjochting ferstjoert. Dit barde mei ferskate ferskillende Wireguard-ynstellingen dy't wy besocht hawwe, lykas <a %(github_costela_wesher)s>wesher</a> en <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Wy hawwe ek besocht om poarten oer SSH te tunneljen, mei autossh en sshuttle, mar kamen dêr <a %(github_sshuttle)s>problemen tsjin</a> (hoewol it my noch net dúdlik is oft autossh lêst hat fan TCP-oer-TCP problemen of net — it fielt gewoan as in janky oplossing foar my, mar miskien is it eins prima?). Ynstee dêrfan binne wy weromgien nei direkte ferbiningen tusken servers, en ferbergje wy dat in server draait op de goedkeape oanbieders mei IP-filtering mei UFW. Dit hat it neidiel dat Docker net goed wurket mei UFW, útsein as jo <code>network_mode: "host"</code> brûke. Dit alles is wat mear foutgefoelich, om't jo jo server bleatstelle oan it ynternet mei mar in lytse misconfiguraasje. Miskien moatte wy weromgean nei autossh — feedback hjir is tige wolkom. Wy hawwe ek hinne en wer gien oer Varnish vs. Nginx. Wy hâlde op it stuit fan Varnish, mar it hat syn eigenaardichheden en rûge rânen. Itselde jildt foar Checkmk: wy hâlde der net fan, mar it wurket foar no. Weblate hat goed west, mar net ynkringend — ik bin soms bang dat it myn gegevens ferliest as ik besykje it te syngronisearjen mei ús git repo. Flask hat oer it algemien goed west, mar it hat wat frjemde eigenaardichheden dy't in soad tiid koste hawwe om te debuggen, lykas it konfigurearjen fan oanpaste domeinen, of problemen mei syn SqlAlchemy-yntegraasje. Oant no ta binne de oare ark geweldich west: wy hawwe gjin serieuze klachten oer MariaDB, ElasticSearch, Gitlab, Zulip, Docker, en Tor. Al dizze hawwe wat problemen hân, mar neat te serieus of tiid-yntinsyf. Mienskip De earste útdaging kin in ferrassende wêze. It is gjin technysk probleem, of in juridysk probleem. It is in psychologysk probleem: dit wurk yn 'e skaad dwaan kin ongelooflijk iensum wêze. Ofhinklik fan wat jo fan plan binne te dwaan, en jo bedrigingsmodel, moatte jo miskien tige foarsichtich wêze. Oan 'e iene kant fan it spektrum hawwe wy minsken lykas Alexandra Elbakyan*, de oprjochter fan Sci-Hub, dy't tige iepen is oer har aktiviteiten. Mar se is op dit stuit yn heech risiko om arrestearre te wurden as se in westers lân soe besykje, en kin tsientallen jierren finzenisstraf krije. Is dat in risiko dat jo ree binne te nimmen? Wy binne oan 'e oare kant fan it spektrum; tige foarsichtich om gjin spoar efter te litten, en sterke operasjonele feiligens te hawwen. * Sa't neamd op HN troch "ynno", woe Alexandra yn it earstoan net bekend wêze: "Har servers wiene ynsteld om detaillearre flaterberjochten fan PHP út te stjoeren, ynklusyf folsleine paad fan de foutmakende boarnebestân, dat ûnder map /home/<USER>'t se online hie op in net-relatearre side, keppele oan har echte namme. Foardat dizze iepenbiering wie se anonym." Dus, brûk tafallige brûkersnammen op de kompjûters dy't jo foar dit spul brûke, foar it gefal dat jo wat ferkeard konfigurearje. Dy geheimhâlding, lykwols, komt mei in psychologyske kosten. De measte minsken hâlde fan erkend wurde foar it wurk dat se dogge, en dochs kinne jo gjin kredyt nimme foar dit yn it echte libben. Sels ienfâldige dingen kinne útdaagjend wêze, lykas freonen dy't freegje wat jo dien hawwe (op in stuit wurdt "omprutsen mei myn NAS / homelab" âld). Dêrom is it sa wichtich om wat mienskip te finen. Jo kinne wat operasjonele feiligens opjaan troch te fertrouwen yn guon tige tichte freonen, dy't jo witte dat jo djip fertrouwe kinne. Sels dan, wês foarsichtich om neat op skrift te setten, foar it gefal dat se harren e-mails oan de autoriteiten moatte oerdraachje, of as harren apparaten op in oare manier kompromittearre binne. Noch better is om wat oare piraten te finen. As jo tichte freonen ynteressearre binne om mei te dwaan, geweldich! Oars kinne jo miskien oaren online fine. Spitigernôch is dit noch in niche mienskip. Oant no ta hawwe wy mar in hantsjefol oaren fûn dy't aktyf binne yn dizze romte. Goede startplakken lykje de Library Genesis forums te wêzen, en r/DataHoarder. It Archive Team hat ek lykas-minded yndividuen, hoewol se binnen de wet operearje (sels as yn guon griis gebieten fan de wet). De tradisjonele "warez" en piraterij sênes hawwe ek minsken dy't op ferlykbere manieren tinke. Wy steane iepen foar ideeën oer hoe't wy mienskip kinne stimulearje en ideeën ferkenne. Fiel jo frij om ús in berjocht te stjoeren op Twitter of Reddit. Miskien koene wy in soarte fan forum of peteargroep hostje. Ien útdaging is dat dit maklik sensurearre wurde kin by it brûken fan mienskiplike platfoarms, dus wy soene it sels moatte hostje. Der is ek in ôfwaging tusken it folslein iepenbier meitsjen fan dizze diskusjes (mear potensjele belutsenens) tsjin it privee meitsjen (net litte potensjele "doelen" witte dat wy op it punt steane om se te skraapjen). Wy sille dêr oer neitinke moatte. Lit ús witte as jo hjir ynteressearre yn binne! Konklúzje Hjir hoopje wy dat dit nuttich is foar nij begjinnende piraten-argivisten. Wy binne entûsjast om jo yn dizze wrâld te ferwolkomjen, dus aarzel net om kontakt op te nimmen. Litte wy safolle mooglik fan 'e kennis en kultuer fan' e wrâld bewarje en it oeral ferspriede. Projekten 4. Data seleksje Faak kinne jo de metadata brûke om in ridlik subset fan gegevens te bepalen om te downloaden. Sels as jo úteinlik alle gegevens downloade wolle, kin it nuttich wêze om de wichtichste items earst te prioritearjen, foar it gefal dat jo ûntdutsen wurde en ferdigeningswurken ferbettere wurde, of om't jo mear skiifromte keapje moatte, of gewoan om't der wat oars yn jo libben opkomt foardat jo alles downloade kinne. Bygelyks, in kolleksje kin meardere edysjes hawwe fan deselde ûnderlizzende boarne (lykas in boek of in film), wêrby't ien markearre is as de bêste kwaliteit. It soe in soad sin meitsje om dy edysjes earst te bewarjen. Jo wolle úteinlik miskien alle edysjes bewarje, om't yn guon gefallen de metadata ferkeard tagge kin wêze, of der kinne ûnbekende kompromissen wêze tusken edysjes (bygelyks, de "bêste edysje" kin yn de measte opsichten it bêste wêze, mar minder yn oare opsichten, lykas in film mei hegere resolúsje mar sûnder ûndertitels). Jo kinne ek yn jo metadata-database sykje om ynteressante dingen te finen. Wat is it grutste bestân dat host wurdt, en wêrom is it sa grut? Wat is it lytste bestân? Binne der ynteressante of ûnferwachte patroanen as it giet om bepaalde kategoryen, talen, ensafuorthinne? Binne der dûbele of hiel ferlykbere titels? Binne der patroanen yn wannear't gegevens tafoege binne, lykas ien dei wêrop in protte bestannen tagelyk tafoege binne? Jo kinne faak in soad leare troch op ferskate manieren nei de dataset te sjen. Yn ús gefal hawwe wy Z-Library boeken deduplisearre tsjin de md5-hashes yn Library Genesis, en dêrmei in soad downloadtiid en skiifromte besparre. Dit is lykwols in frij unike situaasje. Yn de measte gefallen binne der gjin wiidweidige databases fan hokker bestannen al goed bewarre wurde troch oare piraten. Dit is op himsels in grutte kâns foar immen dêr bûten. It soe geweldich wêze om in regelmjittich bywurke oersjoch te hawwen fan dingen lykas muzyk en films dy't al breed ferspraat binne op torrent-websides, en dêrom fan legere prioriteit binne om op te nimmen yn piratemirrens. 6. Distribúsje Jo hawwe de gegevens, en dêrmei hawwe jo (meast wierskynlik) it earste piratemirror fan jo doel yn 'e wrâld. Op in protte manieren is it dreechste diel foarby, mar it riskantste diel leit noch foar jo. Nei alle gedachten hawwe jo oant no ta stealth west; ûnder de radar fleane. Alles wat jo dwaan moasten wie in goede VPN brûke, gjin persoanlike details ynfolje yn formulieren (duh), en miskien in spesjale browsersesje brûke (of sels in oar kompjûter). No moatte jo de gegevens distribuearje. Yn ús gefal woene wy earst de boeken werom bydrage oan Library Genesis, mar ûntdutsen doe gau de swierrichheden dêryn (fiksje tsjin non-fiksje sortearring). Dus besleaten wy om distribúsje te brûken mei Library Genesis-styl torrents. As jo de kâns hawwe om by te dragen oan in besteand projekt, dan kin dat jo in soad tiid besparje. Mar der binne net in protte goed organisearre piratemirrens op it stuit. Lit ús sizze dat jo beslute om sels torrents te distribuearjen. Besykje dy bestannen lyts te hâlden, sadat se maklik te spegeljen binne op oare websiden. Jo sille dan de torrents sels seedje moatte, wylst jo anonym bliuwe. Jo kinne in VPN brûke (mei of sûnder poart foarútstjoering), of betelje mei tumbled Bitcoins foar in Seedbox. As jo net witte wat guon fan dy termen betsjutte, sille jo in protte lêze moatte, om't it wichtich is dat jo de risiko-kompromissen hjir begripe. Jo kinne de torrentbestannen sels hoste op besteande torrentwebsiden. Yn ús gefal keazen wy om eins in webside te hosten, om't wy ek ús filosofy op in dúdlike manier ferspriede woene. Jo kinne dit sels op in ferlykbere manier dwaan (wy brûke Njalla foar ús domeinen en hosting, betelle mei tumbled Bitcoins), mar fiel jo frij om kontakt mei ús op te nimmen om ús jo torrents te hosten. Wy sjogge dernei út om oer tiid in wiidweidich yndeks fan piratemirrens te bouwen, as dit idee oanslacht. Wat VPN-seleksje oanbelanget, is hjir al in protte oer skreaun, dus wy sille gewoan it algemiene advys werhelje om te kiezen op reputaasje. Wierlike rjochtbank-testte gjin-log-belied mei lange track records fan privacybeskerming is de opsje mei it leechste risiko, yn ús miening. Merk op dat sels as jo alles goed dogge, kinne jo nea nei nul risiko komme. Bygelyks, by it seedjen fan jo torrents, kin in heech motivearre steatsakteur wierskynlik nei ynkommende en útgeande gegevensstreamen foar VPN-tsjinners sjen, en dedusearje wa't jo binne. Of jo kinne gewoan op ien of oare manier in flater meitsje. Wy hawwe dat wierskynlik al dien, en sille it wer dwaan. Gelokkich, steaten skele <em>dat</em> net sa folle oer piraterij. Ien beslút om te meitsjen foar elk projekt, is oft it te publisearjen mei deselde identiteit as earder, of net. As jo deselde namme bliuwe brûken, dan kinne flaters yn operasjonele feiligens fan eardere projekten weromkomme om jo te biten. Mar publisearje ûnder ferskillende nammen betsjut dat jo gjin langere reputaasje opbouwe. Wy keazen om sterke operasjonele feiligens fan it begjin ôf te hawwen, sadat wy deselde identiteit bliuwe kinne brûken, mar wy sille net twifelje om ûnder in oare namme te publisearjen as wy in flater meitsje of as de omstannichheden dêr om freegje. It wurd derút krije kin lestich wêze. Sa't wy seine, is dit noch in niche-mienskip. Wy hawwe oarspronklik op Reddit pleatst, mar krigen echt traksje op Hacker News. Foar no is ús oanbefelling om it op in pear plakken te pleatsen en te sjen wat der bart. En nochris, nim kontakt mei ús op. Wy soene graach it wurd ferspriede wolle fan mear piraten-archivisme-ynspanningen. 1. Domein seleksje / filosofy Der is gjin tekoart oan kennis en kultureel erfgoed om te rêden, wat oerweldigjend kin wêze. Dêrom is it faak nuttich om efkes stil te stean en te tinken oer wat jo bydrage kin wêze. Elkenien hat in oare manier fan tinken oer dit, mar hjir binne wat fragen dy't jo josels stelle kinne: Yn ús gefal soargen wy benammen oer de lange termyn bewarjen fan wittenskip. Wy wisten oer Library Genesis, en hoe't it folslein spegele waard mei torrents. Wy hâlden fan dat idee. Doe op in dei besocht ien fan ús wat wittenskiplike learboeken te finen op Library Genesis, mar koe se net fine, wat twifel brocht oer hoe folslein it echt wie. Wy sochten dy learboeken doe online, en fûnen se op oare plakken, wat it sied foar ús projekt plante. Sels foardat wy oer de Z-Library wisten, hiene wy it idee om net te besykjen al dy boeken manuell te sammeljen, mar te fokusjen op it spegeljen fan besteande kolleksjes, en se werom te dragen oan Library Genesis. Hokker feardichheden hawwe jo dy't jo yn jo foardiel brûke kinne? Bygelyks, as jo in online befeiligingsekspert binne, kinne jo manieren fine om IP-blokken foar feilige doelen te ferslaan. As jo goed binne yn it organisearjen fan mienskippen, dan kinne jo miskien wat minsken byinoar bringe om in doel hinne. It is lykwols nuttich om wat programmeren te kennen, al is it mar foar it hâlden fan goede operasjonele feiligens troch dit proses. Wat soe in hege-ynfloed gebiet wêze om op te fokusjen? As jo X oeren sille besteegje oan piratenargivearjen, hoe kinne jo dan de grutste "bang for your buck" krije? Wat binne unike manieren wêrop jo hjir oer tinke? Jo kinne wat ynteressante ideeën of oanpakken hawwe dy't oaren miskien mist hawwe. Hoefolle tiid hawwe jo hjirfoar? Us advys soe wêze om lyts te begjinnen en gruttere projekten te dwaan as jo der mear ûnderfining mei krije, mar it kin alles-omfiemjend wurde. Wêrom binne jo hjir ynteressearre yn? Wêr binne jo passy foar? As wy in groep minsken kinne krije dy't allegear de soarten dingen argivearje dy't se spesifyk soarch foar hawwe, soe dat in soad dekke! Jo sille folle mear witte as de gemiddelde persoan oer jo passy, lykas wat wichtige gegevens binne om te bewarjen, wat de bêste kolleksjes en online mienskippen binne, ensafuorthinne. 3. Metadata skraapjen Datum tafoege/feroare: sadat jo letter weromkomme kinne en bestannen downloade dy't jo earder net downloade hawwe (hoewol jo faak ek de ID of hash hjirfoar brûke kinne). Hash (md5, sha1): om te befestigjen dat jo it bestân goed downloade hawwe. ID: kin in ynterne ID wêze, mar ID's lykas ISBN of DOI binne ek nuttich. Bestânsnamme / lokaasje Beskriuwing, kategory, tags, auteurs, taal, ensfh. Grutte: om te berekkenjen hoefolle skiifromte jo nedich hawwe. Litte wy hjir wat technysker wurde. Foar it feitlik skraapjen fan de metadata fan websiden, hawwe wy dingen frij ienfâldich hâlden. Wy brûke Python-skripts, soms curl, en in MySQL-database om de resultaten yn op te slaan. Wy hawwe gjin fancy skraapsoftware brûkt dy't komplekse websiden kin mappe, om't wy oant no ta allinich ien of twa soarten siden hoege te skraapjen troch gewoan troch id's te enumeratearjen en de HTML te parsjen. As der gjin maklik te enumeratearjen siden binne, dan kinne jo in goede crawler nedich hawwe dy't besiket alle siden te finen. Foardat jo in hiele webside begjinne te skraapjen, besykje it earst in bytsje manuell te dwaan. Gean sels troch in pear tsientallen siden, om in gefoel te krijen foar hoe't dat wurket. Soms sille jo op dizze manier al tsjin IP-blokkearrings of oare ynteressante gedrach oanrinne. Itselde jildt foar gegevensskraapjen: foardat jo te djip yn dit doel geane, soargje derfoar dat jo syn gegevens effektyf kinne downloade. Om beheiningen te omgean, binne der in pear dingen dy't jo kinne besykje. Binne der oare IP-adressen of servers dy't deselde gegevens hoste mar net deselde beheiningen hawwe? Binne der API-einpunten dy't gjin beheiningen hawwe, wylst oaren dat wol hawwe? By hokker downloadrate wurdt jo IP blokkearre, en foar hoe lang? Of wurde jo net blokkearre mar fertrage? Wat as jo in brûkersakkount oanmeitsje, hoe feroarje dingen dan? Kinne jo HTTP/2 brûke om ferbiningen iepen te hâlden, en fergruttet dat de snelheid wêrmei jo siden kinne oanfreegje? Binne der siden dy't meardere bestannen tagelyk listje, en is de ynformaasje dy't dêr listet genôch? Dingen dy't jo wierskynlik wolle bewarje omfetsje: Wy dogge dit typysk yn twa stadia. Earst downloade wy de rauwe HTML-bestannen, meast direkt yn MySQL (om in protte lytse bestannen te foarkommen, dêr't wy hjirûnder mear oer prate). Dan, yn in aparte stap, geane wy troch dy HTML-bestannen en parsearje se yn eigentlike MySQL-tabellen. Op dizze manier hoege jo net alles fanôf it begjin opnij te downloaden as jo in flater yn jo parsearkode ûntdekke, om't jo gewoan de HTML-bestannen mei de nije kode opnij kinne ferwurkje. It is ek faak makliker om de ferwurkingsstap te parallelisearjen, dus besparret wat tiid (en jo kinne de ferwurkingskode skriuwe wylst de skraapjen rint, ynstee fan beide stappen tagelyk te skriuwen). Uteinlik, merk op dat foar guon doelen metadata-skrappen alles is wat der is. Der binne guon grutte metadata-kolleksjes dy't net goed bewarre wurde. Titel Domein seleksje / filosofy: Wêr wolle jo rûchwei op rjochtsje, en wêrom? Wat binne jo unike passys, feardichheden, en omstannichheden dy't jo yn jo foardiel brûke kinne? Doelseleksje: Hokker spesifike kolleksje sille jo spegelje? Metadata skraapjen: Ynformaasje katalogisearje oer de triemmen, sûnder de (faak folle gruttere) triemmen sels te downloaden. Gegevensseleksje: Op basis fan de metadata, bepale hokker gegevens it meast relevant binne om no te argivearjen. It kin alles wêze, mar faak is der in ridlike manier om romte en bandbreedte te besparjen. Gegevens skraapjen: De gegevens echt krije. Distribúsje: It ynpakken yn torrents, it oankundigje earne, minsken krije om it te fersprieden. 5. Data skrappen No binne jo klear om de gegevens yn bulk te downloaden. Sa't earder neamd, moatte jo op dit punt al in hantsjefol bestannen manuell downloade hawwe, om it gedrach en de beheiningen fan it doel better te begripen. Mar der sille noch ferrassingen foar jo wêze as jo ienris in protte bestannen tagelyk downloade. Us advys hjir is foaral om it simpel te hâlden. Begjin gewoan mei it downloaden fan in hantsjefol bestannen. Jo kinne Python brûke, en dan útwreidzje nei meardere threads. Mar soms is it sels ienfâldiger om Bash-bestannen direkt út de database te generearjen, en dan meardere fan harren yn meardere terminalfinsters útfiere om op te skalen. In rappe technyske trúk dy't hjir it neamen wurdich is, is it brûken fan OUTFILE yn MySQL, dy't jo oeral skriuwe kinne as jo "secure_file_priv" útskeakelje yn mysqld.cnf (en soargje derfoar dat jo ek AppArmor útskeakelje/overriden as jo op Linux binne). Wy bewarje de gegevens op ienfâldige hurde skiiven. Begjin mei wat jo hawwe, en breid stadichoan út. It kin oerweldigjend wêze om te tinken oer it bewarjen fan hûnderten TB's oan gegevens. As dat de situaasje is dêr't jo foar steane, set dan earst in goed subset út, en yn jo oankundiging freegje om help by it bewarjen fan de rest. As jo sels mear hurde skiiven wolle krije, dan hat r/DataHoarder guon goede boarnen foar it krijen fan goede deals. Besykje net te folle soargen te meitsjen oer fancy bestânsystemen. It is maklik om yn it kninegat te fallen fan it opsetten fan dingen lykas ZFS. Ien technysk detail om bewust fan te wêzen is lykwols dat in protte bestânsystemen net goed omgean mei in protte bestannen. Wy hawwe fûn dat in ienfâldige oplossing is om meardere mappen te meitsjen, bygelyks foar ferskillende ID-berik of hash-prefixen. Nei it downloaden fan de gegevens, soargje derfoar dat jo de yntegriteit fan de bestannen kontrolearje mei hashes yn de metadata, as dy beskikber binne. 2. Doelseleksje Tagonklik: brûkt net in protte lagen fan beskerming om te foarkommen dat jo har metadata en gegevens skraapje. Spesjale ynsjoch: jo hawwe wat spesjale ynformaasje oer dit doel, lykas dat jo op ien of oare manier spesjale tagong hawwe ta dizze kolleksje, of dat jo útfûn hawwe hoe't jo har ferdigening kinne oerwinne. Dit is net ferplichte (ús kommende projekt docht neat spesjaals), mar it helpt wis! Grut Dus, wy hawwe ús gebiet dat wy besjogge, no hokker spesifike kolleksje sille wy spegelje? Der binne in pear dingen dy't in goed doel meitsje: Doe't wy ús wittenskipsteksboeken op websiden oars as Library Genesis fûnen, besochten wy út te finen hoe't se op it ynternet kamen. Wy fûnen doe de Z-Library, en realisearren dat wylst de measte boeken net earst dêr ferskine, se úteinlik dêr einigje. Wy learden oer syn relaasje mei Library Genesis, en de (finansjele) stimulearstruktuer en superieure brûkersynterface, dy't beide it in folle folsleinere kolleksje makken. Wy diene doe wat foarriedige metadata en gegevensskraapjen, en realisearren dat wy om har IP-downloadlimiten hinne koene komme, troch ien fan ús leden syn spesjale tagong ta in protte proxyservers te brûken. As jo ferskate doelen ferkenne, is it al wichtich om jo spoaren te ferbergjen troch VPN's en wegwerp-e-postadressen te brûken, dêr't wy letter mear oer prate sille. Unyk: net al goed dekt troch oare projekten. As wy in projekt dogge, hat it in pear fazen: Dizze binne net folslein ûnôfhinklike fazen, en faak stjoere ynsjoggen út in lettere faze jo werom nei in eardere faze. Bygelyks, by it skraapjen fan metadata kinne jo realisearje dat it doel dat jo selektearre hawwe ferdigeningsmeganismen hat bûten jo feardigensnivo (lykas IP-blokken), dus geane jo werom en fine in oar doel. - Anna en it team (<a %(reddit)s>Reddit</a>) Hiele boeken kinne skreaun wurde oer de <em>wêrom</em> fan digitale behâld yn it algemien, en pirate-argivisme yn it bysûnder, mar lit ús in koarte yntroduksje jaan foar dyjingen dy't net te bekend binne. De wrâld produsearret mear kennis en kultuer as ea earder, mar ek mear dêrfan wurdt ferlern as ea earder. De minskheid fertrout foar in grut part op bedriuwen lykas akademyske útjouwers, streamingtsjinsten, en sosjale mediabedriuwen mei dit erfgoed, en se hawwe faak net bewiisd geweldige behearders te wêzen. Besjoch de dokumintêre Digital Amnesia, of eins elke lêzing fan Jason Scott. Der binne guon ynstellingen dy't in goede baan dogge mei it argivearjen fan safolle as se kinne, mar se binne bûn troch de wet. As piraten binne wy yn in unike posysje om kolleksjes te argivearjen dy't se net oanreitsje kinne, fanwegen auteursrjochtshandhaving of oare beheiningen. Wy kinne ek kolleksjes ferskate kearen spegelje, oer de hiele wrâld, en dêrmei de kânsen op goed behâld ferheegje. Foar no sille wy net yngean op diskusjes oer de foar- en neidielen fan yntellektueel eigendom, de moraliteit fan it brekken fan de wet, betinkingen oer sensuer, of de kwestje fan tagong ta kennis en kultuer. Mei dat alles út 'e wei, lit ús dûke yn de <em>hoe</em>. Wy sille diele hoe't ús team pirate-argivarissen waard, en de lessen dy't wy ûnderweis leard hawwe. Der binne in protte útdagings as jo oan dizze reis begjinne, en hooplik kinne wy jo troch guon fan harren helpe. Hoe in pirate-argivaris te wurden De earste útdaging kin in ferrassende wêze. It is gjin technysk probleem, of in juridysk probleem. It is in psychologysk probleem. Foardat wy begjinne, twa updates oer de Pirate Library Mirror (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>): Wy hawwe wat tige royale donaasjes krigen. De earste wie $10k fan de anonyme persoan dy't ek "bookwarrior" stipe hat, de oarspronklike oprjochter fan Library Genesis. Spesjale tank oan bookwarrior foar it fasilitearjen fan dizze donaasje. De twadde wie nochris $10k fan in anonyme donor, dy't nei ús lêste release kontakt opnaam en ynspirearre waard om te helpen. Wy hiene ek in oantal lytsere donaasjes. Tige tank foar al jimme royale stipe. Wy hawwe wat spannende nije projekten yn de pipeline dy't dit stypje sil, dus bliuw op 'e hichte. Wy hiene wat technyske swierrichheden mei de grutte fan ús twadde release, mar ús torrents binne no online en seedje. Wy krigen ek in royale oanbieding fan in anonyme persoan om ús kolleksje te seedjen op harren heul-hege-snelheid servers, dus wy dogge in spesjale upload nei harren masines, wêrnei't elkenien oars dy't de kolleksje downloadt in grutte ferbettering yn snelheid sjen moat. Blogberjochten Hoi, ik bin Anna. Ik haw <a %(wikipedia_annas_archive)s>Anna’s Argiven</a> makke, de grutste skaadbibleteek fan 'e wrâld. Dit is myn persoanlike blog, wêryn ik en myn teamgenoaten skriuwe oer piraterij, digitale behâld, en mear. Ferbine mei my op <a %(reddit)s>Reddit</a>. Merk op dat dizze webside allinnich in blog is. Wy hostje hjir allinnich ús eigen wurden. Gjin torrents of oare auteursrjochtlik beskerme bestannen wurde hjir host of keppele. <strong>Bibleteek</strong> - Lykas de measte bibleteken rjochtsje wy ús benammen op skreaune materialen lykas boeken. Wy kinne yn 'e takomst útwreidzje nei oare soarten media. <strong>Spegel</strong> - Wy binne strikt in spegel fan besteande bibleteken. Wy rjochtsje ús op bewarjen, net op it maklik trochsykber en downloadber meitsjen fan boeken (tagong) of it stimulearjen fan in grutte mienskip fan minsken dy't nije boeken bydrage (boarnen). <strong>Piraat</strong> - Wy skeine bewust de auteurswet yn de measte lannen. Dit stelt ús yn steat om wat te dwaan dat legale entiteiten net kinne: derfoar soargje dat boeken oeral ferspraat wurde. <em>Wy keppelje net nei de bestannen fan dizze blog. Fyn it sels.</em> - Anna en it team (<a %(reddit)s>Reddit</a>) Dit projekt (EDIT: ferhuze nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>) hat as doel by te dragen oan it bewarjen en befrijen fan minsklike kennis. Wy meitsje ús lytse en beskieden bydrage, yn de fuotleasten fan de grutten foar ús. De fokus fan dit projekt wurdt yllustrearre troch syn namme: De earste bibleteek dy't wy spegele hawwe is Z-Library. Dit is in populêre (en yllegale) bibleteek. Se hawwe de Library Genesis-kolleksje nommen en dy maklik trochsykber makke. Dêrneist binne se tige effektyf wurden yn it oanlûken fan nije boekbydragen, troch bydrage brûkers te stimulearjen mei ferskate foardielen. Se drage op it stuit dizze nije boeken net werom oan Library Genesis. En oars as Library Genesis, meitsje se har kolleksje net maklik spegelber, wat brede bewarjen foarkomt. Dit is wichtich foar har bedriuwsmodel, om't se jild freegje foar tagong ta har kolleksje yn bulk (mear as 10 boeken per dei). Wy meitsje gjin morele oardielen oer it yn rekken bringen fan jild foar bulk tagong ta in yllegale boekekolleksje. It is bûten mis dat de Z-Bibleteek suksesfol west hat yn it útwreidzjen fan tagong ta kennis, en mear boeken te finen. Wy binne hjir gewoan om ús diel te dwaan: it soargjen foar de lange termyn behâld fan dizze priveekolleksje. Wy wolle jo útnûgje om te helpen minsklike kennis te bewarjen en te befrijen troch ús torrents te downloaden en te seedjen. Sjoch de projektpagina foar mear ynformaasje oer hoe't de gegevens organisearre binne. Wy wolle jo ek graach útnûgje om jo ideeën by te dragen oer hokker kolleksjes as folgjende spegele wurde moatte, en hoe't wy dat oanpakke kinne. Tegearre kinne wy in soad berikke. Dit is mar in lytse bydrage ûnder tal fan oaren. Tankewol, foar alles wat jo dogge. Yntroduksje fan de Pirate Library Mirror: Bewarjen fan 7TB oan boeken (dy't net yn Libgen steane) 10% of it skreaune erfgoed fan 'e minskheid foar ivich bewarre <strong>Google.</strong> Uteinlik hawwe se dit ûndersyk dien foar Google Books. Mar har metadata is net tagonklik yn bulk en frij dreech te skraapjen. <strong>Ferskate yndividuele biblioteeksystemen en argiven.</strong> Der binne biblioteken en argiven dy't net yndeksearre en aggregearre binne troch ien fan 'e hjirboppe neamde, faak om't se ûnderfinansearre binne, of om oare redenen net harren gegevens diele wolle mei Open Library, OCLC, Google, ensafuorthinne. In protte fan dizze hawwe wol digitale records tagonklik fia it ynternet, en se binne faak net hiel goed beskerme, dus as jo wolle helpe en wat wille hawwe mei it learen oer frjemde biblioteeksystemen, binne dizze geweldige startpunten. <strong>ISBNdb.</strong> Dit is it ûnderwerp fan dizze blogpost. ISBNdb skraapt ferskate websiden foar boekmetadata, benammen priisgegevens, dy't se dan ferkeapje oan boekferkeapers, sadat se harren boeken kinne priisje yn oerienstimming mei de rest fan 'e merk. Om't ISBNs tsjintwurdich frij universeel binne, hawwe se effektyf in “webside foar elk boek” boud. <strong>Open Library.</strong> Sa't earder neamd, is dit harren hiele missy. Se hawwe grutte hoemannichten biblioteekgegevens fan meiwurkende biblioteken en nasjonale argiven boarne, en dogge dat noch altyd. Se hawwe ek frijwillige bibliotekaressen en in technysk team dat besiket records te deduplikearjen, en se te labeljen mei allerhanne metadata. It bêste fan alles is dat harren dataset folslein iepen is. Jo kinne it gewoan <a %(openlibrary)s>downloade</a>. <strong>WorldCat.</strong> Dit is in webside rûn troch de non-profit OCLC, dy't biblioteekbehearsystemen ferkeapet. Se aggregearje boekmetadata fan in protte biblioteken, en meitsje it beskikber fia de WorldCat-webside. Mar se fertsjinje ek jild troch dizze gegevens te ferkeapjen, dus it is net beskikber foar bulk download. Se hawwe wol guon mear beheinde bulk datasets beskikber foar download, yn gearwurking mei spesifike biblioteken. 1. Foar in ridlike definysje fan "foar ivich". ;) 2. Fansels is it skreaune erfgoed fan 'e minskheid folle mear as boeken, benammen tsjintwurdich. Foar it doel fan dizze post en ús resinte releases rjochtsje wy ús op boeken, mar ús ynteresses geane fierder. 3. Der is folle mear te sizzen oer Aaron Swartz, mar wy woene him gewoan koart neame, om't hy in krúsjale rol spilet yn dit ferhaal. As de tiid ferrint, kinne mear minsken syn namme foar it earst tsjinkomme, en kinne se dêrnei sels yn it konijnenhol dûke. <strong>Fysike eksimplaren.</strong> Fansels is dit net hiel nuttich, om't se gewoan duplikaat binne fan itselde materiaal. It soe moai wêze as wy alle annotaasjes dy't minsken yn boeken meitsje, lykas Fermat syn ferneamde “krabbels yn 'e marzjes”, koene bewarje. Mar spitigernôch sil dat in dream fan in argivaris bliuwe. <strong>“Edysjes”.</strong> Hjir telle jo elke unike ferzje fan in boek. As der wat oars oan is, lykas in oar omslach of in oare foarwurd, telt it as in oare edysje. <strong>Bestannen.</strong> By it wurkjen mei skaadbiblioteken lykas Library Genesis, Sci-Hub, of Z-Library, is der in ekstra oerweging. Der kinne meardere scans fan deselde edysje wêze. En minsken kinne bettere ferzjes meitsje fan besteande bestannen, troch de tekst te scannen mei OCR, of siden te korrizjearjen dy't ûnder in hoeke skand binne. Wy wolle dizze bestannen allinich telle as ien edysje, wat goede metadata fereasket, of deduplikearring mei help fan dokumintgelikensensmjittingen. <strong>“Wurken”.</strong> Bygelyks “Harry Potter en de Geheime Keamer” as in logysk konsept, dat alle ferzjes dêrfan omfettet, lykas ferskate oersettingen en opnijprintsjes. Dit is in soarte fan nuttige definysje, mar it kin dreech wêze om de line te lûken fan wat telt. Bygelyks, wy wolle wierskynlik ferskate oersettingen bewarje, hoewol opnijprintsjes mei allinich lytse ferskillen miskien net sa wichtich binne. - Anna en it team (<a %(reddit)s>Reddit</a>) Mei de Pirate Library Mirror (EDIT: ferhuze nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>), is ús doel om alle boeken yn 'e wrâld te nimmen en se foar ivich te bewarjen.<sup>1</sup> Tusken ús Z-Library torrents en de orizjinele Library Genesis torrents hawwe wy 11.783.153 bestannen. Mar hoefolle is dat eins? As wy dy bestannen goed deduplikearje soene, hokker persintaazje fan alle boeken yn 'e wrâld hawwe wy bewarre? Wy soene echt graach sa'n ding hawwe: Litte wy begjinne mei wat rûge sifers: Yn sawol Z-Library/Libgen as Open Library binne der folle mear boeken as unike ISBNs. Betsjut dit dat in protte fan dy boeken gjin ISBNs hawwe, of is de ISBN metadata gewoan ûntbrekkend? Wy kinne dizze fraach wierskynlik beantwurdzje mei in kombinaasje fan automatyske oerienkomst basearre op oare attributen (titel, auteur, útjouwer, ensfh.), mear gegevensboarnen ynlûke, en ISBNs út 'e eigentlike boekscans sels helje (yn it gefal fan Z-Library/Libgen). Hoefolle fan dy ISBNs binne unyk? Dit wurdt it bêste yllustrearre mei in Venn-diagram: Om mear presys te wêzen: Wy wiene ferrast troch hoefolle oerlaap der eins is! ISBNdb hat in enoarme hoemannichte ISBN's dy't net ferskine yn Z-Library of Open Library, en itselde jildt (yn in lytsere mar noch substansjele mjitte) foar de oare twa. Dit ropt in protte nije fragen op. Hoefolle soe automatyske oerienkomst helpe by it taggen fan de boeken dy't net mei ISBN's tagge wiene? Soe der in protte oerienkomsten wêze en dêrtroch mear oerlaap? Ek, wat soe der barre as wy in 4e of 5e dataset ynbringe? Hoefolle oerlaap soene wy dan sjen? Dit jout ús in startpunt. Wy kinne no sjen nei alle ISBN's dy't net yn de Z-Library dataset wiene, en dy't ek net oerienkomme mei titel/skriuwer fjilden. Dat kin ús in hânfet jaan op it bewarjen fan alle boeken yn 'e wrâld: earst troch it ynternet te skrabjen foar scans, dan troch yn it echte libben boeken te scannen. It lêste kin sels crowd-funded wurde, of oandreaun troch "bounties" fan minsken dy't graach spesifike boeken digitalisearre sjogge. Al dat is in ferhaal foar in oare kear. As jo mei ien fan dizze dingen helpe wolle — fierdere analyze; mear metadata skrabje; mear boeken fine; boeken OCR'e; dit dwaan foar oare domeinen (bygelyks papers, audioboeken, films, tv-shows, tydskriften) of sels guon fan dizze gegevens beskikber meitsje foar dingen lykas ML / grutte taalmodel training — nim dan kontakt mei my op (<a %(reddit)s>Reddit</a>). As jo spesifyk ynteressearre binne yn de data-analyze, wurkje wy oan it beskikber meitsjen fan ús datasets en skripts yn in makliker te brûken formaat. It soe geweldich wêze as jo gewoan in notysjeboek kinne forkje en hjirmei begjinne te boartsjen. Uteinlik, as jo dit wurk stypje wolle, beskôgje dan in donaasje te meitsjen. Dit is in folslein frijwilligersrûne operaasje, en jo bydrage makket in grut ferskil. Elke bytsje helpt. Foar no nimme wy donaasje yn crypto; sjoch de Donear-side op Anna’s Argiven. Foar in persintaazje hawwe wy in noemer nedich: it totale oantal boeken dat ea publisearre is.<sup>2</sup> Foar de ûndergong fan Google Books, besocht in yngenieur op it projekt, Leonid Taycher, <a %(booksearch_blogspot)s>dit oantal te skatten</a>. Hy kaam - mei in knypeach - út op 129.864.880 ("teminsten oant snein"). Hy skatte dit oantal troch in unifoarme databank fan alle boeken yn 'e wrâld te bouwen. Hjirfoar luts hy ferskate datasets gear en fusearre se op ferskate manieren. As in koarte sydnoat, is der in oar persoan dy't besocht hat om alle boeken yn 'e wrâld te katalogisearjen: Aaron Swartz, de ferstoarne digitale aktivist en Reddit mei-oprjochter.<sup>3</sup> Hy <a %(youtube)s>begûn Open Library</a> mei it doel fan “ien webside foar elk boek dat ea publisearre is”, troch gegevens fan ferskate boarnen te kombinearjen. Hy betelle úteinlik de ultime priis foar syn wurk oan digitale bewarjen doe't hy ferfolge waard foar it massaal downloaden fan akademyske papieren, wat late ta syn selsmoard. It is oerstallich te sizzen dat dit ien fan 'e redenen is wêrom't ús groep pseudonym is, en wêrom't wy tige foarsichtich binne. Open Library wurdt noch altyd heroïsk rûn troch minsken by it Internet Archive, dy't Aaron syn erfskip fuortsette. Wy komme hjir letter yn dizze post op werom. Yn de Google blogpost beskriuwt Taycher guon fan 'e útdagings by it skatten fan dit oantal. Earst, wat foarmet in boek? Der binne in pear mooglike definysjes: “Edysjes” lykje de meast praktyske definysje fan wat “boeken” binne. Gelokkich wurdt dizze definysje ek brûkt foar it tawizen fan unike ISBN-nûmers. In ISBN, of International Standard Book Number, wurdt faak brûkt foar ynternasjonale hannel, om't it yntegrearre is mei it ynternasjonale barkoade-systeem (”International Article Number”). As jo in boek yn winkels ferkeapje wolle, hat it in barkoade nedich, dus krije jo in ISBN. Taycher syn blogpost neamt dat wylst ISBNs nuttich binne, se net universeel binne, om't se pas echt yn 'e midden fan 'e santiger jierren oannommen waarden, en net oeral yn 'e wrâld. Dochs is ISBN wierskynlik de meast brûkte identifier fan boekedysjes, dus it is ús bêste startpunt. As wy alle ISBNs yn 'e wrâld fine kinne, krije wy in nuttige list fan hokker boeken noch bewarre wurde moatte. Dus, wêr krije wy de gegevens? Der binne in oantal besteande ynspanningen dy't besykje in list fan alle boeken yn 'e wrâld gear te stallen: Yn dizze post binne wy bliid om in lytse release oan te kundigjen (yn ferliking mei ús foarige Z-Library releases). Wy hawwe it measte fan ISBNdb skraapt, en de gegevens beskikber makke foar torrenting op 'e webside fan de Pirate Library Mirror (EDIT: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>; wy sille it hjir net direkt keppelje, sykje der gewoan nei). Dit binne sawat 30,9 miljoen records (20GB as <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzipped). Op harren webside beweare se dat se eins 32,6 miljoen records hawwe, dus wy kinne miskien wat mist hawwe, of <em>sy</em> kinne wat ferkeard dwaan. Hoe dan ek, foar no sille wy net krekt diele hoe't wy it dien hawwe — wy litte dat as in oefening foar de lêzer. ;-) Wat wy wol diele sille is wat foarriedige analyze, om tichterby te kommen by it skatten fan it oantal boeken yn 'e wrâld. Wy hawwe nei trije datasets sjoen: dizze nije ISBNdb dataset, ús orizjinele release fan metadata dy't wy skraapt hawwe fan 'e Z-Library skaadbiblioteek (dy't Library Genesis omfettet), en de Open Library data dump. ISBNdb dump, of Hoefolle Boeken Bliuwe Foar Ivich Bewarre? As wy de bestannen fan skaadbibleteken goed deduplikearje soene, hokker persintaazje fan alle boeken yn 'e wrâld hawwe wy bewarre? Updates oer <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>, de grutste echt iepen bibleteek yn 'e minsklike skiednis. <em>WorldCat-ûntwerp</em> Gegevens <strong>Formaat?</strong> <a %(blog)s>Anna’s Argiven Konteners (AAC)</a>, dat is yn wêzen <a %(jsonlines)s>JSON Lines</a> komprimearre mei <a %(zstd)s>Zstandard</a>, plus wat standerdisearre semantyk. Dizze konteners omfiemje ferskate soarten records, basearre op de ferskillende skrast dy't wy ynsetten. In jier lyn hawwe wy <a %(blog)s>ús ynset</a> om dizze fraach te beantwurdzjen: <strong>Hokker persintaazje boeken binne permanint bewarre troch skaadbibleteken?</strong> Litte wy nei wat basisynformaasje oer de gegevens sjen: Sadree't in boek yn in iepen-data skaadbibleteek lykas <a %(wikipedia_library_genesis)s>Library Genesis</a> komt, en no <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>, wurdt it oer de hiele wrâld spegele (troch torrents), en wurdt it dêrmei praktysk foar altyd bewarre. Om de fraach te beantwurdzjen hokker persintaazje boeken bewarre is, moatte wy de noemer witte: hoefolle boeken besteane der yn totaal? En ideaal hawwe wy net allinnich in getal, mar ek eigentlike metadata. Dan kinne wy se net allinnich fergelykje mei skaadbibleteken, mar ek <strong>in TODO-list fan oerbleaune boeken meitsje om te bewarjen!</strong> Wy koene sels begjinne te dreamen fan in mienskiplike ynspanning om dizze TODO-list del te gean. Wy hawwe <a %(wikipedia_isbndb_com)s>ISBNdb</a> skrast, en de <a %(openlibrary)s>Open Library dataset</a> downloade, mar de resultaten wiene net tefredenstellend. It haadprobleem wie dat der net in soad oerlap fan ISBNs wie. Sjoch dizze Venn-diagram út <a %(blog)s>ús blogpost</a>: Wy wiene tige ferrast troch hoe lyts de oerlap wie tusken ISBNdb en Open Library, dy't beide frijmoedich gegevens opnimme út ferskate boarnen, lykas webskrast en bibleteekrecords. As se beide in goede baan dogge by it finen fan de measte ISBNs dy't der binne, soene har sirkels grif substansjele oerlap hawwe, of soe ien in ûnderstel fan de oare wêze. It makke ús benijd, hoefolle boeken falle <em>kompleet bûten dizze sirkels</em>? Wy hawwe in gruttere databank nedich. Dat is wannear't wy ús each op de grutste boekdatabank yn 'e wrâld setten: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is in proprietêre databank fan de non-profit <a %(wikipedia_oclc)s>OCLC</a>, dy't metadata records fan bibleteken oer de hiele wrâld aggregearret, yn ruil foar it jaan fan dy bibleteken tagong ta de folsleine dataset, en se yn 'e sykresultaten fan ein-brûkers te sjen. Hoewol't OCLC in non-profit is, fereasket harren bedriuwsmodel it beskermjen fan harren databank. No, wy binne sorry te sizzen, freonen by OCLC, wy jouwe it allegear fuort. :-) Yn it ôfrûne jier hawwe wy alle WorldCat-records sekuer skrast. Yn it earstoan hiene wy in gelokkige brek. WorldCat wie krekt har folsleine webside-ûntwerp oan it útrôljen (yn augustus 2022). Dit omfette in substansjele oersjoch fan harren backend-systemen, dy't in protte befeiligingsflaters yntrodusearren. Wy grepen fuortendaliks de kâns, en koene hûnderten miljoenen (!) records yn in pear dagen skrasse. Dêrnei waarden befeiligingsflaters stadichoan ien foar ien reparearre, oant de lêste dy't wy fûnen sawat in moanne lyn patcht waard. By dy tiid hiene wy frijwol alle records, en wiene wy allinnich op syk nei wat hegere kwaliteit records. Dus wy fiele dat it tiid is om frij te jaan! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Anna’s Argiven hat alle WorldCat (de grutste bibleteekmetadata-kolleksje fan 'e wrâld) skrabbe om in TODO-list fan boeken te meitsjen dy't bewarre wurde moatte.</em> WorldCat Warskôging: dizze blogpost is ferâldere. Wy hawwe besletten dat IPFS noch net klear is foar de haadtiid. Wy sille noch altyd keppelings nei bestannen op IPFS fan Anna's Argiven jaan as mooglik, mar wy sille it net mear sels hostje, noch riede wy oaren oan om te spegeljen mei help fan IPFS. Sjoch asjebleaft ús Torrents-side as jo ús kolleksje helpe wolle behâlde. Help Z-Library op IPFS te seedjen Partner Server download SciDB Ekstern lien Eksterne liening (print útskeakele) Eksterne download Ferken metadata Befette yn torrents Werom  (+%(num)s bonus) ûnbeleanne betelle annulearre ferfallen wachtet op befestiging fan Anna ûnweardich De tekst hjirûnder giet fierder yn it Ingelsk. Gean Reset Foarút Lêst As jo e-mailadres net wurket op de Libgen-forums, riede wy oan om <a %(a_mail)s>Proton Mail</a> (fergees) te brûken. Jo kinne ek <a %(a_manual)s>manuell oanfreegje</a> om jo akkount te aktivearjen. (kin <a %(a_browser)s>browserferifikaasje</a> fereaskje — ûnbeheinde downloads!) Fluch Partner Server #%(number)s (oanrikkemand) (in bytsje flugger, mar mei wachtrige) (gjin browserferifikaasje nedich) (geen browserferifikaasje of wachtlisten) (gjin wachtlist, mar kin heul traach wêze) Trage Partner Server #%(number)s Audioboek Stripboek Boek (fiksje) Boek (non-fiksje) Boek (ûnbekend) Tydskriftartikel Tydskrift Muzykskoare Oar Standertdokumint Net alle siden koene omset wurde nei PDF Marked brutsen yn Libgen.li Net sichtber yn Libgen.li Net sichtber yn Libgen.rs Fiction Net sichtber yn Libgen.rs Non-Fiction Running exiftool mislearre op dit bestân Markearre as "min bestân" yn Z-Library Net fan Z-Library Markearre as "spam" yn Z-Library Bestân kin net iepene wurde (bgl. beskeadige bestân, DRM) Copyright-oanspraak Downloadproblemen (bgl. kin net ferbine, foutberjocht, hiel stadich) Ferkearde metadata (bgl. titel, beskriuwing, omslachôfbylding) Oar Minne kwaliteit (bgl. opmaakproblemen, minne scan kwaliteit, ûntbrekkende siden) Spam / bestân moat fuorthelle wurde (bgl. reklame, misledigjende ynhâld) %(amount)s (%(amount_usd)s) %(amount)s totaal %(amount)s (%(amount_usd)s) totaal Briljante Boekewjirm Lokkige Bibliotekaris Flinke Ferzamelaar Fantastyske Fersammeler Bonusdownloads Cerlalc Tsjechyske metadata DuXiu 读秀 EBSCOhost eBook Yndeks Google Books Goodreads HathiTrust IA IA Kontroleare Digitale Liening ISBNdb ISBN GRP Libgen.li “Scimag” útsletten Libgen.rs Non-fiksje en fiksje Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Russyske Steatsbibleteek Sci-Hub Via Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Uploads nei AA Z-Library Z-Library Sineesk Titel, auteur, DOI, ISBN, MD5, … Sykje Auteur Beskriuwing en metadata opmerkingen Edysje Oarspronklike bestânsnamme Utjouwer (sykje spesifyk fjild) Titel Jier publisearre Technyske details Dizze munt hat in hegere minimum dan gewoanlik. Selektearje in oare doer of in oare munt. Oanfraach koe net foltôge wurde. Besykje it oer in pear minuten noch ris, en as it trochgiet, nim dan kontakt mei ús op by %(email)s mei in skermôfbylding. In ûnbekende flater barde. Nim kontakt mei ús op fia %(email)s mei in skermôfbylding. Flater yn betellingsferwurking. Wachtsje in momint en besykje it opnij. As it probleem langer as 24 oeren duorret, nim dan kontakt mei ús op by %(email)s mei in skermôfbylding. Wy hâlde in fundraiser foar <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">it reservearjen</a> fan de grutste skadenbiblioteek foar strips yn 'e wrâld. Tank foar jo stipe! <a href="/donate">Donearje.</a> As jo net kinne donearje, beskôgje ús te stypjen troch jo freonen te fertellen, en ús te folgjen op <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, of <a href="https://t.me/annasarchiveorg">Telegram</a>. Stjoer ús gjin e-mail om <a %(a_request)s>boeken oan te freegjen</a><br>of lytse (<10k) <a %(a_upload)s>uploads</a>. Anna’s Argyf DMCA / auteurskipsaken Bliuw yn kontakt Reddit Alternativen SLUM (%(unaffiliated)s) net oansletten Anna’s Argyf hat jo help nedich! As jo no donearje, krije jo <strong>dûbel</strong> it oantal snelle downloads. In protte besykje ús del te heljen, mar wy fjochtsje werom. As jo dizze moanne donearje, krije jo <strong>dûbel</strong> it oantal snelle downloads. Jildich oant de ein fan dizze moanne. It bewarjen fan minsklike kennis: in prachtich fakânsjekado! Lidmaatskippen wurde dêrnei ferlinge. Partner servers binne net beskikber fanwegen hosting-slutingen. Se moatte gau wer online wêze. Om de wjerstân fan Anna’s Argyf te ferheegjen, sykje wy frijwilligers om spegels te draaien. Wy hawwe in nije donearmetoade beskikber: %(method_name)s. Oerwaach asjebleaft om %(donate_link_open_tag)ste donearjen</a> — it is net goedkeap om dizze webside te betsjinjen, en jo donaasje makket echt in ferskil. Tige tank. Ferwize in freon, en sawol jo as jo freon krije %(percentage)s%% bonus rappe downloads! Ferrass in leave, jou se in akkount mei lidmaatskip. It perfekte Falentynskado! Lear mear… Akkount Aktiviteit Avansearre Anna’s Blog ↗ Anna’s Software ↗ beta Koadesferkenner Datasets Donearje Downloadde bestannen FAQ Thús Ferbetterje metadata LLM-data Ynlogge / Registrearje Myn donaasje Iepenbier profyl Sykje Feiligens Torrents Oersette ↗ Frijwilligerswurk & Bounties Resinte downloads: 📚&nbsp;De grutste iepen-boarne iepen-data bibleteek fan de wrâld. ⭐️&nbsp;Spegelt Sci-Hub, Library Genesis, Z-Library, en mear. 📈&nbsp;%(book_any)s boeken, %(journal_article)s papieren, %(book_comic)s strips, %(magazine)s tydskriften — foar ivich bewarre.  en  en mear DuXiu Internet Archive Lending Library LibGen 📚&nbsp;De grutste echt iepen bibleteek yn de skiednis fan de minskheid. 📈&nbsp;%(book_count)s&nbsp;boeken, %(paper_count)s&nbsp;papieren — foar ivich bewarre. ⭐️&nbsp;Wy spegelje %(libraries)s. Wy scrape en iepenboarne %(scraped)s. Al ús koade en gegevens binne folslein iepen boarne. OpenLib Sci-Hub ,  📚 De grutste iepen boarne iepen data bibleteek fan 'e wrâld.<br>⭐️ Spegelt Scihub, Libgen, Zlib, en mear. Z-Lib Anna’s Argyf Ongeldige oanfraach. Besykje %(websites)s. De grutste iepen-boarne iepen-data bibleteek fan de wrâld. Spegelt Sci-Hub, Library Genesis, Z-Library, en mear. Sykje yn Anna’s Archive Anna’s Argyf Fernij asjebleaft om it nochris te besykjen. <a %(a_contact)s>Kontakt mei ús opnimme</a> as it probleem foar meardere oeren oanhâldt. 🔥 Probleem by it laden fan dizze side <li>1. Folgje ús op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, of <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Fertel oaren oer Anna’s Archive op Twitter, Reddit, Tiktok, Instagram, yn jo lokale kafee of biblioteek, of wêr't jo ek geane! Wy leauwe net yn poartebewaking — as wy offline helle wurde, komme wy gewoan werom, om't al ús koade en gegevens folslein iepen boarne binne.</li><li>3. As jo kinne, beskôgje dan <a href="/donate">te donearjen</a>.</li><li>4. Help ús webside <a href="https://translate.annas-software.org/">te oersetten</a> yn ferskate talen.</li><li>5. As jo in software-ûntwikkelder binne, beskôgje dan by te dragen oan ús <a href="https://annas-software.org/">iepen boarne</a>, of ús <a href="/datasets">torrents</a> te seedjen.</li> 10. Meitsje of help de Wikipedia-side foar Anna’s Archive yn jo taal te ûnderhâlden. 11. Wy sykje nei lytse, smaakfolle advertinsjes. As jo advertearje wolle op Anna’s Argyf, lit it ús dan witte. 6. As jo in befeiligingsûndersiker binne, kinne wy jo feardichheden brûke foar sawol oanfallende as ferdigenjende taken. Sjoch ús <a %(a_security)s>Befeiligings</a> side. 7. Wy sykje nei saakkundigen op it mêd fan betellingen foar anonyme keaplju. Kinne jo ús helpe om mear handige manieren ta te foegjen om te donearjen? PayPal, WeChat, kadokaarten. As jo immen kenne, nim dan kontakt mei ús op. 8. Wy sykje altyd nei mear serverkapasiteit. 9. Jo kinne helpe troch bestânsproblemen te melden, opmerkingen te litten, en listen te meitsjen op dizze webside. Jo kinne ek helpe troch <a %(a_upload)s>mear boeken te uploaden</a>, of bestânsproblemen of opmaak fan besteande boeken te ferbetterjen. Foar mear wiidweidige ynformaasje oer hoe't jo frijwilliger wurde kinne, sjoch ús <a %(a_volunteering)s>Frijwilligers & Bounties</a> side. Wy leauwe sterk yn de frije stream fan ynformaasje, en it behâld fan kennis en kultuer. Mei dizze sykmasine bouwe wy op de skouders fan reuzen. Wy hawwe djip respekt foar it hurde wurk fan de minsken dy't de ferskate skaadbiblioteken makke hawwe, en wy hoopje dat dizze sykmasine harren berik ferbreedzje sil. Om op 'e hichte te bliuwen fan ús foarútgong, folgje Anna op <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> of <a href="https://t.me/annasarchiveorg">Telegram</a>. Foar fragen en feedback nim dan kontakt op mei Anna by %(email)s. Akkount ID: %(account_id)s Útlogge ❌ Der is wat misgien. Fernij de side en besykje it opnij. ✅ Jo binne no útlogd. Fernij de side om wer yn te loggen. Flugge downloads brûkt (lêste 24 oeren): <strong>%(used)s / %(total)s</strong> Lidmaatskip: <strong>%(tier_name)s</strong> oant %(until_date)s <a %(a_extend)s>(ferlingje)</a> Jo kinne meardere lidmaatskippen kombinearje (fluch downloads per 24 oeren wurde byinoar opteld). Lidmaatskip: <strong>Gjin</strong> <a %(a_become)s>(wur lid)</a> Kontakt opnimme mei Anna op %(email)s as jo ynteressearre binne om jo lidmaatskip te ferheegjen nei in hegere tier. Iepenbier profyl: %(profile_link)s Geheime kaai (net diele!): %(secret_key)s toane Doch mei ús hjir! Upgrade nei in <a %(a_tier)s>heger nivo</a> om by ús groep te kommen. Eksklusyf Telegram-groep: %(link)s Akkount Hokker downloads? Oanmelde Ferlieze jo kaai net! Ungeldige geheime kaai. Ferifiearje jo kaai en besykje it opnij, of registrearje in nij akkount hjirûnder. Geheime kaai Fier jo geheime kaai yn om yn te loggen: Ald e-postbasearre akkount? Fier jo <a %(a_open)s>e-post hjir yn</a>. Nij akkount registrearje Noch gjin akkount? Registraasje suksesfol! Jo geheime kaai is: <span %(span_key)s>%(key)s</span> Bewarje dizze kaai foarsichtich. As jo it ferlieze, ferlieze jo tagong ta jo akkount. <li %(li_item)s><strong>Blêdwizer.</strong> Jo kinne dizze side blêdwize om jo kaai op te heljen.</li><li %(li_item)s><strong>Download.</strong> Klik <a %(a_download)s>op dizze link</a> om jo kaai te downloaden.</li><li %(li_item)s><strong>Wachtwurdbehearder.</strong> Brûk in wachtwurdbehearder om de kaai op te slaan as jo it hjirûnder ynfiere.</li> Ynlogge / Registrearje Blêderferifikaasje Warskôging: koade hat ferkearde Unicode-tekens en kin yn ferskate situaasjes ferkeard funksjonearje. De rauwe binêre kin dekodearre wurde fan 'e base64-fertsjintwurdiging yn de URL. Beskriuwing Label Foarheaksel URL foar in spesifike koade Webside Koades dy't begjinne mei “%(prefix_label)s” Asjebleaft, skraap dizze siden net. Ynstee riede wy oan <a %(a_import)s>te generearjen</a> of <a %(a_download)s>te downloaden</a> ús ElasticSearch en MariaDB databases, en ús <a %(a_software)s>iepen boarne koade</a> út te fieren. De rauwe gegevens kinne manuell ferkend wurde fia JSON-bestannen lykas <a %(a_json_file)s>dizze</a>. Minder as %(count)s records Algemiene URL Koadesferkenner Yndeks fan Ferken de koades wêrmei records tagge binne, per prefix. De kolom "records" lit it oantal records sjen dat tagge is mei koades mei de jûn prefix, sa't sjoen yn de sykmasine (ynklusyf allinich metadata-records). De kolom "koades" lit sjen hoefolle eigentlike koades in jûn prefix hawwe. Bekende koadefoarheaksel “%(key)s” Mear… Prefix %(count)s rekord komt oerien mei “%(prefix_label)s” %(count)s records komme oerien mei “%(prefix_label)s” koades records “%%s” sil ferfongen wurde troch de wearde fan de koade Sykje Anna’s Argyf Koades URL foar spesifike koade: “%(url)s” Dizze side kin in skoft duorje om te generearjen, dêrom is in Cloudflare captcha nedich. <a %(a_donate)s>Leden</a> kinne de captcha oerslaan. Misbrûk meld: Better ferzje Wolle jo dizze brûker melde foar misledigjend of ûnfatsoenlik gedrach? Bestânsprobleem: %(file_issue)s ferburgen opmerking Antwurdzje Meld misbrûk Jo hawwe dizze brûker meld foar misbrûk. Copyright-oanspraken op dit e-mailadres wurde negearre; brûk it formulier ynstee. Toan e-mail Wy stelle jo feedback en fragen tige op priis! Mar, fan it grutte tal spam en ûnsin-e-mails dy't wy krije, kontrolearje asjebleaft de fakjes om te befestigjen dat jo dizze betingsten foar kontakt mei ús begripe. Alle oare manieren om kontakt mei ús op te nimmen oer auteursrjochtkearnen wurde automatysk wiske. Foar DMCA / copyright-oanspraken, brûk <a %(a_copyright)s>dit formulier</a>. Kontakt email URLs op Anna’s Argiven (ferplicht). Ien per rigel. Nim allinnich URLs op dy't deselde edysje fan in boek beskriuwe. As jo in claim wolle yntsjinje foar meardere boeken of meardere edysjes, folje dit formulier dan meardere kearen yn. Claims dy't meardere boeken of edysjes bondelje sille ôfwiisd wurde. Adres (ferplicht) Dúdlike beskriuwing fan it boarnemateriaal (ferplicht) E-mail (ferplicht) URLs nei it boarnemateriaal, ien per rigel (ferplicht). Nim safolle mooglik op, om ús te helpen jo claim te ferifiearjen (bgl. Amazon, WorldCat, Google Books, DOI). ISBN's fan it boarnemateriaal (as fan tapassing). Ien per rigel. Nim allinnich dy op dy't krekt oerienkomme mei de edysje dêr't jo in copyright claim foar rapportearje. Jo namme (ferplicht) ❌ Der is wat misgien. Fernij de side en besykje it opnij. ✅ Tige tank foar it yntsjinjen fan jo copyright claim. Wy sille it sa gau mooglik besjen. Fernij de side om in nije claim yn te tsjinjen. <a %(a_openlib)s>Open Library</a> URLs fan it boarnemateriaal, ien per rigel. Nim efkes de tiid om yn Open Library te sykjen nei jo boarnemateriaal. Dit sil ús helpe om jo claim te ferifiearjen. Telefoannûmer (ferplicht) Ferklearring en hantekening (ferplicht) Claim yntsjinje As jo in DCMA of oare copyright claim hawwe, folje dan dit formulier sa krekt mooglik yn. As jo problemen tsjinkomme, nim dan kontakt mei ús op fia ús spesjale DMCA-adres: %(email)s. Tink derom dat claims dy't nei dit adres maild wurde net ferwurke wurde, it is allinnich foar fragen. Brûk asjebleaft it formulier hjirûnder om jo claims yn te tsjinjen. DMCA / Copyright claim formulier Foarbyldrekord op Anna’s Archive Torrents troch Anna’s Archive Anna’s Archive Containers formaat Skripts foar it ymportearjen fan metadata As jo ynteressearre binne yn it spegeljen fan dizze dataset foar <a %(a_archival)s>argyfwurk</a> of <a %(a_llm)s>LLM-training</a> doelen, nim dan kontakt mei ús op. Lêst bywurke: %(date)s Haad %(source)s webside Metadata-dokumintaasje (de measte fjilden) Bestannen spegele troch Anna’s Archive: %(count)s (%(percent)s%%) Boarnen Totale bestannen: %(count)s Totale bestânsgrutte: %(size)s Us blogpost oer dizze gegevens <a %(duxiu_link)s>Duxiu</a> is in enoarme databank fan skande boeken, makke troch de <a %(superstar_link)s>SuperStar Digital Library Group</a>. De measten binne akademyske boeken, skand om se digitaal beskikber te meitsjen foar universiteiten en biblioteken. Foar ús Ingelsktalige publyk hawwe <a %(princeton_link)s>Princeton</a> en de <a %(uw_link)s>Universiteit fan Washington</a> goede oersichten. Der is ek in treflik artikel dat mear eftergrûn jout: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. De boeken fan Duxiu binne al lang piraat op it Sineeske ynternet. Meastentiids wurde se ferkocht foar minder as in dollar troch ferkeapers. Se wurde typysk ferspraat mei help fan it Sineeske ekwivalint fan Google Drive, dat faak hackt is om mear opslachromte mooglik te meitsjen. Guon technyske details kinne fûn wurde <a %(link1)s>hjir</a> en <a %(link2)s>hjir</a>. Hoewol't de boeken semi-iepenbier ferspraat binne, is it frij dreech om se yn bulk te krijen. Wy hiene dit heech op ús TODO-list, en hawwe meardere moannen fan fulltime wurk derfoar tawiisd. Mar, yn let 2023 hat in yncredible, geweldige, en talintearre frijwilliger ús berikt, en fertelde ús dat se al dit wurk al dien hiene — tsjin grutte kosten. Se dielden de folsleine kolleksje mei ús, sûnder wat yn ruil te ferwachtsjen, útsein de garânsje fan lange-termyn bewarjen. Wier bûtengewoan. Mear ynformaasje fan ús frijwilligers (raw notysjes): Oanpast fan ús <a %(a_href)s>blogpost</a>. DuXiu 读秀 %(count)s bestân %(count)s bestannen Dizze dataset is nau besibbe oan de <a %(a_datasets_openlib)s>Open Library dataset</a>. It befettet in scrape fan alle metadata en in grut diel fan de triemmen fan de IA’s Controlled Digital Lending Library. Updates wurde útbrocht yn it <a %(a_aac)s>Anna’s Argiven Containers formaat</a>. Dizze records wurde direkt ferwiisd fan de Open Library dataset, mar befetsje ek records dy't net yn Open Library steane. Wy hawwe ek in oantal datafiles dy't troch mienskipsleden oer de jierren hinne skrapt binne. De kolleksje bestiet út twa dielen. Jo hawwe beide dielen nedich om alle gegevens te krijen (útsein ferâldere torrents, dy't trochkrúst binne op de torrentside). Digitale Útlieningsbibleteek ús earste release, foardat wy standerdisearren op it <a %(a_aac)s>Anna’s Archive Containers (AAC) formaat</a>. Bevat metadata (as json en xml), pdfs (fan acsm en lcpdf digitale útlieningssystemen), en omslach miniatueren. ynkrementele nije releases, mei help fan AAC. Bevat allinnich metadata mei tiidsstimpels nei 2023-01-01, om't de rest al dekt is troch “ia”. Ek alle pdf-bestannen, dizze kear fan de acsm en “bookreader” (IA’s web reader) útlieningssystemen. Hoewol't de namme net krekt goed is, folje wy noch altyd bookreader-bestannen yn de ia2_acsmpdf_files kolleksje, om't se ûnderling útslutend binne. IA Controlled Digital Lending 98%%+ fan de bestannen binne trochsykber. Us missy is om alle boeken yn 'e wrâld (lykas papieren, tydskriften, ensfh.) te argivearjen en breed tagonklik te meitsjen. Wy leauwe dat alle boeken breed spegele wurde moatte, om redundânsje en wjerstân te garandearjen. Dêrom sammelje wy bestannen fan ferskate boarnen. Guon boarnen binne folslein iepen en kinne yn bulk spegele wurde (lykas Sci-Hub). Oaren binne sletten en beskermjend, dus besykje wy se te skrassen om har boeken te "befrijen". Oaren falle earne tuskenyn. Al ús gegevens kinne <a %(a_torrents)s>torrente</a> wurde, en al ús metadata kinne <a %(a_anna_software)s>generearre</a> of <a %(a_elasticsearch)s>downloade</a> wurde as ElasticSearch en MariaDB databases. De rauwe gegevens kinne hânmjittich ferkend wurde troch JSON-bestannen lykas <a %(a_dbrecord)s>dit</a>. Metadata ISBN webside Lêst bywurke: %(isbn_country_date)s (%(link)s) Boarnen De Ynternasjonale ISBN Agintskip publisearret regelmjittich de berikken dy't it tawiisd hat oan nasjonale ISBN-agintskippen. Hjirút kinne wy ôfmeitsje ta hokker lân, regio, of taalgroep dit ISBN heart. Wy brûke dizze gegevens op it stuit yndirekt, fia de <a %(a_isbnlib)s>isbnlib</a> Python-biblioteek. ISBN lânynformaasje Dit is in dump fan in protte oproppen nei isbndb.com yn septimber 2022. Wy besochten alle ISBN-berikken te dekken. Dit binne sawat 30,9 miljoen records. Op har webside beweare se dat se eins 32,6 miljoen records hawwe, dus wy hawwe miskien op ien of oare manier wat mist, of <em>se</em> kinne wat ferkeard dwaan. De JSON-antwurden binne frijwat rau fan har server. Ien gegevenskwaliteitsprobleem dat wy opmurken, is dat foar ISBN-13-nûmers dy't begjinne mei in oar foarheaksel dan “978-”, se noch altyd in “isbn”-fjild opnimme dat gewoan it ISBN-13-nûmer is mei de earste 3 nûmers ôfsnien (en it kontrôlesifer opnij berekkene). Dit is fansels ferkeard, mar sa dogge se it, dus wy hawwe it net feroare. In oar potinsjeel probleem dat jo tsjinkomme kinne, is it feit dat it “isbn13”-fjild duplikaten hat, dus jo kinne it net brûke as primêre kaai yn in databank. “isbn13”+“isbn”-fjilden kombinearre lykje wol unyk te wêzen. Release 1 (2022-10-31) Fiksje-torrents binne efter (hoewol't IDs ~4-6M net torrente binne om't se oerlappe mei ús Zlib-torrents). Us blogpost oer de stripboekenútjefte Strips-torrents op Anna’s Argyf Foar de eftergrûn fan de ferskate Library Genesis forks, sjoch de side foar de <a %(a_libgen_rs)s>Libgen.rs</a>. De Libgen.li befettet it measte fan deselde ynhâld en metadata as de Libgen.rs, mar hat wat kolleksjes boppe-op dit, nammentlik strips, tydskriften, en standertdokuminten. It hat ek <a %(a_scihub)s>Sci-Hub</a> yntegrearre yn syn metadata en sykmasine, wat wy brûke foar ús databank. De metadata foar dizze bibleteek is fergees beskikber <a %(a_libgen_li)s>op libgen.li</a>. Lykwols, dizze server is traach en stipet gjin it werheljen fan brutsen ferbiningen. Deselde bestannen binne ek beskikber op <a %(a_ftp)s>in FTP-server</a>, dy't better wurket. Non-fiksje liket ek ôfwykt te wêzen, mar sûnder nije torrents. It liket derop dat dit sûnt begjin 2022 bard is, hoewol't wy dit net ferifiearre hawwe. Neffens de Libgen.li-behearder moat de “fiction_rus” (Russyske fiksje) kolleksje dekt wurde troch regelmjittich útbrochte torrents fan <a %(a_booktracker)s>booktracker.org</a>, benammen de <a %(a_flibusta)s>flibusta</a> en <a %(a_librusec)s>lib.rus.ec</a> torrents (dy't wy hjir <a %(a_torrents)s>spegelje</a>, hoewol't wy noch net fêststeld hawwe hokker torrents oerienkomme mei hokker bestannen). De fiksjekolleksje hat syn eigen torrents (ôfwykt fan <a %(a_href)s>Libgen.rs</a>) begjinnend by %(start)s. Bepaalde reeksen sûnder torrents (lykas fiksje reeksen f_3463000 oant f_4260000) binne wierskynlik Z-Library (of oare dûbele) bestannen, hoewol't wy miskien wat deduplikearje wolle en torrents meitsje foar lgli-unike bestannen yn dizze reeksen. Statistiken foar alle kolleksjes kinne fûn wurde <a %(a_href)s>op de webside fan libgen</a>. Torrents binne beskikber foar de measte fan de ekstra ynhâld, benammen torrents foar stripboeken, tydskriften, en standert dokuminten binne útbrocht yn gearwurking mei Anna’s Argyf. Merk op dat de torrentbestannen dy't ferwize nei “libgen.is” eksplisyt spegels binne fan <a %(a_libgen)s>Libgen.rs</a> (“.is” is in oar domein brûkt troch Libgen.rs). In nuttige boarne foar it brûken fan de metadata is <a %(a_href)s>dizze side</a>. %(icon)s Harren “fiction_rus” kolleksje (Russyske fiksje) hat gjin spesifike torrents, mar wurdt dekt troch torrents fan oaren, en wy hâlde in <a %(fiction_rus)s>spegel</a>. Russyske fiksje torrents op Anna’s Argyf Fiksje-torrents op Anna’s Argyf Diskusjefoarum Metadata Metadata fia FTP Tydskriften-torrents op Anna’s Argyf Metadata fjildynformaasje Spegel fan oare torrents (en unike fiksje- en strips-torrents) Standert dokumint torrents op Anna’s Argyf Libgen.li Torrents troch Anna’s Argyf (boekomslagen) Library Genesis is bekend om't se har gegevens al genereus yn bulk beskikber stelle fia torrents. Us Libgen-kolleksje bestiet út helpgegevens dy't se net direkt frijlitte, yn gearwurking mei harren. In protte tank oan elkenien belutsen by Library Genesis foar it wurkjen mei ús! Us blog oer de boekomslach release Dizze side giet oer de “.rs” ferzje. It is bekend om konsekwint sawol syn metadata as de folsleine ynhâld fan syn boekekatalogus te publisearjen. Syn boekekolleksje is ferdield tusken in fiksje- en non-fiksje-diel. In nuttige boarne foar it brûken fan de metadata is <a %(a_metadata)s>dizze side</a> (blokkeart IP-berik, VPN kin nedich wêze). Fan 2024-03 ôf, wurde nije torrents pleatst yn <a %(a_href)s>dizze forumdraad</a> (blokkeart IP-berik, VPN kin nedich wêze). Fiksje torrents op Anna’s Argyf Libgen.rs Fiksje torrents Libgen.rs Diskusje forum Libgen.rs Metadata Libgen.rs metadata fjildynformaasje Libgen.rs Non-fiksje torrents Non-fiksje torrents op Anna’s Argyf %(example)s foar in fiksje boek. Dizze <a %(blog_post)s>earste release</a> is frij lyts: sawat 300GB oan boekomslagen fan de Libgen.rs fork, sawol fiksje as non-fiksje. Se binne organisearre op deselde wize as hoe't se ferskine op libgen.rs, bygelyks: %(example)s foar in non-fiksje boek. Krekt as mei de Z-Library kolleksje, hawwe wy se allegear yn in grutte .tar-bestân set, dat montearre wurde kin mei <a %(a_ratarmount)s>ratarmount</a> as jo de bestannen direkt tsjinje wolle. Release 1 (%(date)s) It koarte ferhaal fan de ferskillende Library Genesis (of “Libgen”) forks, is dat oer tiid, de ferskillende minsken belutsen by Library Genesis in skeel hiene, en harren eigen wei giene. Neffens dizze <a %(a_mhut)s>forumpost</a> waard Libgen.li oarspronklik host op “http://free-books.dontexist.com”. De “.fun” ferzje waard makke troch de orizjinele oprjochter. It wurdt opnij ûntwikkele yn it foardiel fan in nije, mear fersprate ferzje. De <a %(a_li)s>“.li” ferzje</a> hat in enoarme kolleksje fan strips, lykas oare ynhâld, dy't (noch) net beskikber is foar bulk download fia torrents. It hat wol in aparte torrentkolleksje fan fiksjeboeken, en it befettet de metadata fan <a %(a_scihub)s>Sci-Hub</a> yn syn databank. De “.rs” ferzje hat heul ferlykbere gegevens, en publisearret meast konsekwint harren kolleksje yn bulk torrents. It is rûchwei ferdield yn in “fiksje” en in “non-fiksje” seksje. Oarspronklik op “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> is yn in bepaalde sin ek in fork fan Library Genesis, hoewol't se in oare namme brûkten foar harren projekt. Libgen.rs Wy ferheegje ús kolleksje ek mei allinnich-metadata boarnen, dy't wy kinne keppelje oan bestannen, bygelyks mei ISBN-nûmers of oare fjilden. Hjirûnder is in oersjoch fan dy boarnen. Op 'e nij, guon fan dizze boarnen binne folslein iepen, wylst wy oaren skraapje moatte. Merk op dat wy yn metadata-sykjen de orizjinele records sjen litte. Wy dogge gjin gearfoeging fan records. Allinnich-metadata boarnen Open Library is in iepen boarne projekt fan de Internet Archive om elk boek yn de wrâld te katalogisearjen. It hat ien fan de grutste boekskanoperaasjes yn de wrâld, en hat in protte boeken beskikber foar digitale liening. Har boekmetadata katalogus is fergees beskikber foar download, en is opnaam yn Anna’s Argyf (hoewol net op it stuit yn sykjen, útsein as jo eksplisyt sykje nei in Open Library ID). Open Library Útsûnderje dûbelingen Lêst bywurke Persintaazjes fan it oantal bestannen %% spegele troch AA / torrents beskikber Grutte Boarne Hjirûnder is in fluch oersjoch fan de boarnen fan de bestannen op Anna’s Archive. Om't de skaadbiblioteken faak gegevens fan inoar syngronisearje, is der in soad oerlap tusken de biblioteken. Dêrom komme de sifers net oerien mei it totaal. It persintaazje "spegele en siedde troch Anna’s Archive" lit sjen hoefolle bestannen wy sels spegelje. Wy siedde dy bestannen yn bulk fia torrents, en meitsje se beskikber foar direkte download fia partnerwebsiden. Oersjoch Totaal Torrents op Anna’s Archive Foar in eftergrûn oer Sci-Hub, ferwize wy jo nei syn <a %(a_scihub)s>offisjele webside</a>, <a %(a_wikipedia)s>Wikipedia-side</a>, en dizze <a %(a_radiolab)s>podcast-ynterview</a>. Merk op dat Sci-Hub sûnt 2021 <a %(a_reddit)s>beferzen is</a>. It wie earder beferzen, mar yn 2021 waarden in pear miljoen papieren tafoege. Noch altyd wurde in beheind oantal papieren tafoege oan de Libgen “scimag” kolleksjes, hoewol net genôch om nije bulk torrents te rjochtfeardigjen. Wy brûke de Sci-Hub metadata sa't levere troch <a %(a_libgen_li)s>Libgen.li</a> yn syn “scimag” kolleksje. Wy brûke ek de <a %(a_dois)s>dois-2022-02-12.7z</a> dataset. Merk op dat de “smarch” torrents <a %(a_smarch)s>ferâldere</a> binne en dêrom net opnommen binne yn ús torrentslist. Torrents op Libgen.li Torrents op Libgen.rs Metadata en torrents Updates op Reddit Podcast-ynterview Wikipedia-side Sci-Hub Sci-Hub: beferzen sûnt 2021; measte beskikber fia torrents Libgen.li: lytse tafoegings sûnt doe</div> Guon boarnbibleteken befoarderje it massaal dielen fan harren gegevens fia torrents, wylst oaren harren kolleksje net maklik diele. Yn it lêste gefal besiket Anna’s Archive harren kolleksjes te skraapjen en beskikber te meitsjen (sjoch ús <a %(a_torrents)s>Torrents</a> side). Der binne ek tuskenyn situaasjes, bygelyks wêr't boarnbibleteken wol diele wolle, mar net de middels hawwe om dat te dwaan. Yn dy gefallen besykje wy ek te helpen. Hjirûnder is in oersjoch fan hoe't wy ynterface mei de ferskate boarnbibleteken. Boarnebiblioteken %(icon)s Ferskate bestânsdatabases ferspraat oer it Sineeske ynternet; hoewol faak betelle databases %(icon)s De measte bestannen binne allinnich tagonklik mei premium BaiduYun-akkounts; stadige downloadsnelheden. %(icon)s Anna’s Argiven behearet in kolleksje fan <a %(duxiu)s>DuXiu-bestannen</a> %(icon)s Ferskate metadata-databases ferspraat oer it Sineeske ynternet; hoewol faak betelle databases %(icon)s Gjin maklik tagonklike metadata-dumps beskikber foar harren hiele kolleksje. %(icon)s Anna's Argiven behearret in kolleksje fan <a %(duxiu)s>DuXiu metadata</a> Bestannen %(icon)s Bestannen allinnich beskikber foar liening op in beheinde basis, mei ferskate tagongsbeheiningen %(icon)s Anna’s Argyf behearet in kolleksje fan <a %(ia)s>IA-bestannen</a> %(icon)s Guon metadata beskikber fia <a %(openlib)s>Open Library database dumps</a>, mar dy dekke net de hiele IA-kolleksje %(icon)s Gjin maklik tagonklike metadata dumps beskikber foar harren hiele kolleksje %(icon)s Anna's Argiven behearret in kolleksje fan <a %(ia)s>IA metadata</a> Lêst bywurke %(icon)s Anna’s Argyf en Libgen.li beheare tegearre kolleksjes fan <a %(comics)s>stripboeken</a>, <a %(magazines)s>tydskriften</a>, <a %(standarts)s>standert dokuminten</a>, en <a %(fiction)s>fiksje (ôfwykt fan Libgen.rs)</a>. %(icon)s Non-Fiksje torrents wurde dield mei Libgen.rs (en spegele <a %(libgenli)s>hjir</a>). %(icon)s Kwartaal <a %(dbdumps)s>HTTP-database dumps</a> %(icon)s Automatyske torrents foar <a %(nonfiction)s>Non-Fiksje</a> en <a %(fiction)s>Fiksje</a> %(icon)s Anna's Argiven behearret in kolleksje fan <a %(covers)s>boekomslach torrents</a> %(icon)s Deistige <a %(dbdumps)s>HTTP-database dumps</a> Metadata %(icon)s Moanlikse <a %(dbdumps)s>database dumps</a> %(icon)s Gegevens-torrents beskikber <a %(scihub1)s>hjir</a>, <a %(scihub2)s>hjir</a>, en <a %(libgenli)s>hjir</a> %(icon)s Guon nije bestannen wurde <a %(libgenrs)s>tafoege</a> oan Libgen’s “scimag”, mar net genôch om nije torrents te rjochtfeardigjen %(icon)s Sci-Hub hat sûnt 2021 gjin nije bestannen mear tafoege. %(icon)s Metadata-dumps beskikber <a %(scihub1)s>hjir</a> en <a %(scihub2)s>hjir</a>, en ek as diel fan de <a %(libgenli)s>Libgen.li-database</a> (dy't wy brûke) Boarne %(icon)s Ferskate lytsere of ienmalige boarnen. Wy moedigje minsken oan om earst nei oare skaadbiblioteken te uploaden, mar soms hawwe minsken kolleksjes dy't te grut binne foar oaren om troch te sortearjen, hoewol net grut genôch om in eigen kategory te rjochtfeardigjen. %(icon)s Net direkt yn bulk beskikber, beskerme tsjin scraping %(icon)s Anna's Argiven behearret in kolleksje fan <a %(worldcat)s>OCLC (WorldCat) metadata</a> %(icon)s Anna’s Argyf en Z-Library beheare tegearre in kolleksje fan <a %(metadata)s>Z-Library metadata</a> en <a %(files)s>Z-Library bestannen</a> Datasets Wy kombinearje alle boppesteande boarnen yn ien unified database dy't wy brûke om dizze webside te betsjinjen. Dizze unified database is net direkt beskikber, mar om't Anna’s Archive folslein iepen boarne is, kin it frij maklik <a %(a_generated)s>generearre</a> of <a %(a_downloaded)s>downloade</a> wurde as ElasticSearch en MariaDB databases. De skripts op dy side sille automatysk alle nedige metadata downloade fan de hjirboppe neamde boarnen. As jo ús gegevens wolle ferkenne foardat jo dy skripts lokaal útfiere, kinne jo ús JSON-bestannen besjen, dy't fierder keppelje nei oare JSON-bestannen. <a %(a_json)s>Dit bestân</a> is in goed startpunt. Unified database Torrents troch Anna’s Argiven blêdzje sykje Ferskate lytsere of ienmalige boarnen. Wy moedigje minsken oan om earst nei oare skaadbiblioteken te uploaden, mar soms hawwe minsken kolleksjes dy't te grut binne foar oaren om troch te sortearjen, mar net grut genôch om in eigen kategory te fertsjinjen. Oersjoch fan <a %(a1)s>datasets side</a>. Fan <a %(a_href)s>aaaaarg.fail</a>. Liket frij folslein te wêzen. Fan ús frijwilliger “cgiym”. Fan in <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Hat in frij hege oerlap mei besteande papieren kolleksjes, mar hiel pear MD5-oerienkomsten, dus wy besletten it folslein te hâlden. Scrape fan <q>iRead eBooks</q> (= fonetysk <q>ai rit i-books</q>; airitibooks.com), troch frijwilliger <q>j</q>. Komt oerien mei <q>airitibooks</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>. Fan in kolleksje <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Diels fan de orizjinele boarne, diels fan the-eye.eu, diels fan oare spegels. Fan in privee boeken torrent webside, <a %(a_href)s>Bibliotik</a> (faak oantsjut as “Bib”), wêrfan boeken yn torrents bûn waarden troch namme (A.torrent, B.torrent) en ferspraat waarden fia the-eye.eu. Fan ús frijwilliger “bpb9v”. Foar mear ynformaasje oer <a %(a_href)s>CADAL</a>, sjoch de notysjes op ús <a %(a_duxiu)s>DuXiu dataset side</a>. Mear fan ús frijwilliger “bpb9v”, meast DuXiu-bestannen, lykas in map “WenQu” en “SuperStar_Journals” (SuperStar is it bedriuw efter DuXiu). Fan ús frijwilliger “cgiym”, Sineeske teksten fan ferskate boarnen (fertsjintwurdige as submappen), ynklusyf fan <a %(a_href)s>China Machine Press</a> (in grutte Sineeske útjouwer). Net-Sineeske kolleksjes (fertsjintwurdige as submappen) fan ús frijwilliger “cgiym”. Scrape fan boeken oer Sineeske arsjitektuer, troch frijwilliger <q>cm</q>: <q>Ik krige it troch in netwurk kwetsberens by de útjouwerij te eksploitearjen, mar dat gat is sûnt sluten</q>. Komt oerien mei <q>chinese_architecture</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>. Boeken fan akademyske útjouwerij <a %(a_href)s>De Gruyter</a>, sammele út in pear grutte torrents. Scrape fan <a %(a_href)s>docer.pl</a>, in Poalske bestânsdielingswebside rjochte op boeken en oare skreaune wurken. Skraapt yn let 2023 troch frijwilliger “p”. Wy hawwe gjin goede metadata fan de orizjinele webside (net iens bestânsútwreidings), mar wy filteren foar boekachtige bestannen en koene faak metadata út de bestannen sels helje. DuXiu epubs, direkt fan DuXiu, sammele troch frijwilliger “w”. Allinnich resinte DuXiu boeken binne direkt beskikber fia ebooks, dus de measten fan dizze moatte resint wêze. Oerbleaune DuXiu-bestannen fan frijwilliger “m”, dy't net yn it DuXiu-eigendomlike PDG-formaat wiene (it haad <a %(a_href)s>DuXiu-dataset</a>). Sammele út in protte orizjinele boarnen, spitigernôch sûnder dy boarnen yn it bestânpaad te bewarjen. <span></span> <span></span> <span></span> Scrape fan eroatyske boeken, troch frijwilliger <q>do no harm</q>. Komt oerien mei <q>hentai</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>. <span></span> <span></span> Kolleksje skraapt fan in Japanske Manga útjouwer troch frijwilliger “t”. <a %(a_href)s>Selektearre rjochterlike argiven fan Longquan</a>, oanbean troch frijwilliger “c”. Scrape fan <a %(a_href)s>magzdb.org</a>, in bûnsgenoat fan Library Genesis (it is keppele op de libgen.rs thússide) mar dy't net har bestannen direkt jaan woene. Krigen troch frijwilliger “p” yn let 2023. <span></span> Ferskate lytse uploads, te lyts as eigen subkolleksje, mar fertsjintwurdige as mappen. Ebooks fan AvaxHome, in Russyske bestânsdielingswebside. Argyf fan kranten en tydskriften. Komt oerien mei <q>newsarch_magz</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>. Scrape fan de <a %(a1)s>Philosophy Documentation Center</a>. Kolleksje fan frijwilliger “o” dy't Poalske boeken direkt fan orizjinele release (“scene”) websiden sammele hat. Kombinearre kolleksjes fan <a %(a_href)s>shuge.org</a> troch frijwilligers “cgiym” en “woz9ts”. <span></span> <a %(a_href)s>“Imperiale Bibleteek fan Trantor”</a> (neamd nei de fiktive bibleteek), skraapt yn 2022 troch frijwilliger “t”. <span></span> <span></span> <span></span> Sub-sub-kolleksjes (fertsjintwurdige as mappen) fan frijwilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (troch <a %(a_sikuquanshu)s>Dizhi(迪志)</a> yn Taiwan), mebook (mebook.cc, 我的小书屋, myn lytse boekekamer — woz9ts: “Dizze side rjochtet him benammen op it dielen fan hege kwaliteit e-boekbestannen, guon dêrfan binne sels set troch de eigener. De eigener waard <a %(a_arrested)s>arrestearre</a> yn 2019 en immen makke in kolleksje fan bestannen dy't hy dielde.”). Oerbleaune DuXiu-bestannen fan frijwilliger “woz9ts”, dy't net yn it DuXiu proprietêre PDG-formaat wiene (moatte noch omset wurde nei PDF). De “upload” kolleksje is opdield yn lytsere subkolleksjes, dy't oanjûn wurde yn de AACIDs en torrent-nammen. Alle subkolleksjes waarden earst deduplikearre tsjin de haadkolleksje, hoewol de metadata “upload_records” JSON-bestannen noch in protte ferwizings nei de orizjinele bestannen befetsje. Net-boekbestannen waarden ek fuorthelle út de measte subkolleksjes, en wurde typysk <em>net</em> neamd yn de “upload_records” JSON. De subkolleksjes binne: Notysjes Subkolleksje In protte subkolleksjes besteane sels út sub-sub-kolleksjes (bygelyks fan ferskate orizjinele boarnen), dy't fertsjintwurdige wurde as mappen yn de "filepath" fjilden. Uploads nei Anna's Argiven Us blogpost oer dizze gegevens <a %(a_worldcat)s>WorldCat</a> is in proprietêre databank fan de non-profit <a %(a_oclc)s>OCLC</a>, dy't metadata records fan biblioteken oer de hiele wrâld aggregearret. It is wierskynlik de grutste biblioteekmetadata kolleksje yn 'e wrâld. Yn oktober 2023 hawwe wy in wiidweidige scrape fan de OCLC (WorldCat) databank <a %(a_scrape)s>frijlitten</a>, yn it <a %(a_aac)s>Anna’s Archive Containers formaat</a>. Oktober 2023, earste útjefte: OCLC (WorldCat) Torrents troch Anna’s Archive Foarbyldrekord op Anna’s Archive (oarspronklike kolleksje) Foarbyldrekord op Anna's Argiven ("zlib3" kolleksje) Torrents troch Anna’s Archive (metadata + ynhâld) Blogpost oer Release 1 Blogpost oer Release 2 Ein 2022 waarden de bewearde oprjochters fan Z-Library arrestearre, en domeinen waarden yn beslach nommen troch de Amerikaanske autoriteiten. Sûnt dy tiid is de webside stadichoan wer online kommen. It is ûnbekend wa't it op it stuit bestjoert. Bywurking fan febrewaris 2023. Z-Library hat syn woartels yn de <a %(a_href)s>Library Genesis</a> mienskip, en waard oarspronklik opstart mei harren gegevens. Sûnt dy tiid is it flink profesjonalisearre, en hat it in folle modernere ynterface. Se binne dêrom by steat om folle mear donaasjes te krijen, sawol finansjeel om harren webside te ferbetterjen, as donaasjes fan nije boeken. Se hawwe in grutte kolleksje opboud neist Library Genesis. De kolleksje bestiet út trije dielen. De oarspronklike beskriuwingspagina's foar de earste twa dielen binne hjirûnder bewarre bleaun. Jo hawwe alle trije dielen nedich om alle gegevens te krijen (útsein ferfongen torrents, dy't trochkrúst binne op de torrentside). %(title)s: ús earste release. Dit wie de aller earste release fan wat doe de “Pirate Library Mirror” (“pilimi”) neamd waard. %(title)s: twadde release, dizze kear mei alle bestannen yn .tar-bestannen ynpakt. %(title)s: ynkrementele nije releases, mei help fan it <a %(a_href)s>Anna’s Archive Containers (AAC) formaat</a>, no útbrocht yn gearwurking mei it Z-Library team. De earste spegel waard mei soarch krigen yn de rin fan 2021 en 2022. Op dit stuit is it wat ferâldere: it reflektearret de steat fan de kolleksje yn juny 2021. Wy sille dit yn de takomst bywurkje. Op it stuit rjochtsje wy ús op it útbringen fan dizze earste release. Om't Library Genesis al bewarre is mei iepenbiere torrents, en opnaam is yn de Z-Library, hawwe wy in basis deduplikearring dien tsjin Library Genesis yn juny 2022. Hjirfoar hawwe wy MD5-hashes brûkt. Der is wierskynlik folle mear duplikaat ynhâld yn de bibleteek, lykas meardere bestânsformaten mei itselde boek. Dit is dreech om krekt te detektearjen, dus dogge wy dat net. Nei de deduplikearring hawwe wy mear as 2 miljoen bestannen oer, mei in totaal fan krekt ûnder 7TB. De kolleksje bestiet út twa dielen: in MySQL ".sql.gz" dump fan de metadata, en de 72 torrentbestannen fan sawat 50-100GB elk. De metadata befettet de gegevens sa't se rapporteare binne troch de Z-Library webside (titel, auteur, beskriuwing, bestânstype), lykas de eigentlike bestânsgrutte en md5sum dy't wy observearre hawwe, om't dizze soms net oerienkomme. Der liket in oanbod fan bestannen te wêzen dêr't de Z-Library sels ferkearde metadata foar hat. Wy kinne ek yn guon isolearre gefallen ferkeard downloade bestannen hawwe, dy't wy yn de takomst besykje te detektearjen en te reparearjen. De grutte torrentbestannen befetsje de eigentlike boekgegevens, mei de Z-Library ID as de bestânsnamme. De bestânsútwreidings kinne rekonstruearre wurde mei de metadata dump. De kolleksje is in miks fan non-fiksje en fiksje ynhâld (net skieden lykas yn Library Genesis). De kwaliteit is ek tige fariabel. Dizze earste release is no folslein beskikber. Merk op dat de torrentbestannen allinnich beskikber binne fia ús Tor spegel. Release 1 (%(date)s) Dit is in ienkele ekstra torrent-bestân. It befettet gjin nije ynformaasje, mar it hat wat gegevens dy't in skoft duorje kinne om te berekkenjen. Dat makket it handich om te hawwen, om't it downloaden fan dizze torrent faak flugger is as it fanôf it begjin te berekkenjen. Benammen befettet it SQLite-yndeksen foar de tar-bestannen, foar gebrûk mei <a %(a_href)s>ratarmount</a>. Release 2 tafoeging (%(date)s) Wy hawwe alle boeken krigen dy't tafoege waarden oan de Z-Library tusken ús lêste spegel en augustus 2022. Wy hawwe ek weromgien en guon boeken skraapt dy't wy de earste kear mist hawwe. Alles byinoar is dizze nije kolleksje sawat 24TB. Op 'e nij is dizze kolleksje deduplikearre tsjin Library Genesis, om't der al torrents beskikber binne foar dy kolleksje. De gegevens binne ferlykber organisearre as de earste release. Der is in MySQL “.sql.gz” dump fan de metadata, dy't ek alle metadata fan de earste release omfettet, en dêrmei ferfangt. Wy hawwe ek wat nije kolommen tafoege: Wy hawwe dit de lêste kear neamd, mar om it te ferduidelikjen: "bestânsnamme" en "md5" binne de eigentlike eigenskippen fan it bestân, wylst "bestânsnamme_rapporteare" en "md5_rapporteare" binne wat wy fan Z-Library skraapt hawwe. Soms komme dizze twa net oerien mei elkoar, dus hawwe wy beide opnaam. Foar dizze release hawwe wy de kollaasjewize feroare nei "utf8mb4_unicode_ci", dy't kompatibel wêze moat mei âldere ferzjes fan MySQL. De gegevensbestannen binne ferlykber mei de lêste kear, hoewol se folle grutter binne. Wy koenen ús gewoan net dwaande hâlde mei it meitsjen fan in protte lytsere torrentbestannen. “pilimi-zlib2-0-14679999-extra.torrent” befettet alle bestannen dy't wy yn de lêste release mist hawwe, wylst de oare torrents allegear nije ID-berik binne.  <strong>Update %(date)s:</strong> Wy hawwe de measte fan ús torrents te grut makke, wêrtroch torrentkliïnten muoite hawwe. Wy hawwe se fuorthelle en nije torrents útbrocht. <strong>Update %(date)s:</strong> Der wiene noch te folle bestannen, dus hawwe wy se yn tar-bestannen ynpakt en nije torrents op 'e nij útbrocht. %(key)s: oft dit bestân al yn Library Genesis is, yn of de non-fiksje of fiksjekolleksje (matched by md5). %(key)s: yn hokker torrent dit bestân sit. %(key)s: ynsteld doe't wy it boek net downloade koene. Release 2 (%(date)s) Zlib releases (oarspronklike beskriuwingspagina's) Tor domein Haadwebside Z-Library skraap De “Sineeske” kolleksje yn Z-Library liket deselde te wêzen as ús DuXiu-kolleksje, mar mei ferskillende MD5s. Wy slute dizze bestannen út fan torrents om dûbeling te foarkommen, mar litte se noch wol sjen yn ús sykindeks. Metadata Jo krije %(percentage)s%% bonus snelle downloads, om't jo ferwiisd binne troch brûker %(profile_link)s. Dit jildt foar de hiele lidmaatskipsperioade. Donearje Meidwaan Selektearre oant %(percentage)s%% koartingen Alipay stipet ynternasjonale kredyt-/debitkaarten. Sjoch <a %(a_alipay)s>dizze gids</a> foar mear ynformaasje. Stjoer ús Amazon.com-cadeaukaarten mei jo kredyt-/debitkaart. Jo kinne crypto keapje mei kredyt-/debitkaarten. WeChat (Weixin Pay) stipet ynternasjonale kredyt-/debitkaarten. Yn de WeChat-app, gean nei “Me => Services => Wallet => Add a Card”. As jo dat net sjogge, skeakelje it yn mei “Me => Settings => General => Tools => Weixin Pay => Enable”. (brûk by it ferstjoeren fan Ethereum fan Coinbase) kopiearre! kopiearje (legste minimale bedrach) (warskôging: heech minimum bedrach) -%(percentage)s%% 12 moannen 1 moanne 24 moannen 3 moannen 48 moannen 6 moannen 96 moannen Selektearje hoelang't jo abonnearje wolle. <div %(div_monthly_cost)s></div><div %(div_after)s>nei <span %(span_discount)s></span> koartingen</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% foar 12 moannen foar 1 moanne foar 24 moannen foar 3 moannen foar 48 moannen foar 6 moannen foar 96 moannen %(monthly_cost)s / moanne nim kontakt mei ús op Direkte <strong>SFTP</strong> servers Donaasjes op bedriuwsnivo of útwikseling foar nije kolleksjes (bgl. nije scans, OCR’de datasets). Ekspert Tagong <strong>Unbeheind</strong> hege-snelheid tagong <div %(div_question)s>Kin ik myn lidmaatskip opwurdearje of meardere lidmaatskippen krije?</div> <div %(div_question)s>Kin ik in donaasje dwaan sûnder lid te wurden?</div> Wis. Wy akseptearje donaasjes fan elk bedrach op dit Monero (XMR) adres: %(address)s. <div %(div_question)s>Wat betsjutte de berik per moanne?</div> Jo kinne oan de legere kant fan in berik komme troch alle koartingen ta te passen, lykas it kiezen fan in perioade langer as in moanne. <div %(div_question)s>Fernije lidmaatskippen automatysk?</div> Lidmaatskippen <strong>dogge dat net</strong> automatysk. Jo kinne lid wurde foar sa lang of koart as jo wolle. <div %(div_question)s>Wêr besteegje jo donaasjes oan?</div> 100%% giet nei it bewarjen en tagonklik meitsjen fan 'e kennis en kultuer fan 'e wrâld. Op it stuit besteegje wy it meast oan servers, opslach en bandbreedte. Gjin jild giet nei teamleden persoanlik. <div %(div_question)s>Kin ik in grutte donaasje dwaan?</div> Dat soe geweldich wêze! Foar donaasjes fan mear as in pear tûzen dollar, nim dan direkt kontakt mei ús op by %(email)s. <div %(div_question)s>Hawwe jo oare betelmethoden?</div> Op it stuit net. In protte minsken wolle net dat argiven lykas dit bestean, dus wy moatte foarsichtich wêze. As jo ús kinne helpe om oare (mear handige) betelmethoden feilich yn te stellen, nim dan kontakt op mei %(email)s. Donaasje FAQ Jo hawwe in <a %(a_donation)s>besteande donaasje</a> yn proses. Foltôgje of annulearje dy donaasje foardat jo in nije donaasje meitsje. <a %(a_all_donations)s>Besjoch al myn donaasje</a> Foar donaasjes boppe $5000 nim asjebleaft direkt kontakt mei ús op by %(email)s. Wy ferwolkomje grutte donaasjes fan rike yndividuen of ynstellingen.  Wês derop alert dat hoewol't de lidmaatskippen op dizze side "per moanne" binne, it ienmalige donaasjes binne (net weromkommend). Sjoch de <a %(faq)s>FAQ oer donaasje</a>. Anna’s Archive is in non-profit, iepen-boarne, iepen-data projekt. Troch te donearjen en lid te wurden, stypje jo ús operaasjes en ûntwikkeling. Oan al ús leden: tank foar it hâlden fan ús geande! ❤️ Foar mear ynformaasje, sjoch de <a %(a_donate)s>FAQ oer Donaasjes</a>. Om lid te wurden, <a %(a_login)s>Log yn of Registrearje</a> asjebleaft. Tank foar jo stipe! $%(cost)s / moanne As jo in flater makke hawwe by de betelling, kinne wy gjin refunds dwaan, mar wy sille besykje it goed te meitsjen. Fyn de “Crypto”-side yn jo PayPal-app of webside. Dit is typysk ûnder “Finânsjes”. Gean nei de “Bitcoin” side yn jo PayPal app of webside. Druk op de “Oermeitsje” knop %(transfer_icon)s, en dan “Stjoere”. Alipay Alipay 支付宝 / WeChat 微信 Amazon Gift Card %(amazon)s kadokaart Bankkaart Bankkaart (mei app) Binance Kredyt/debit/Apple/Google (BMC) Cash App Kredyt-/debitkaart Kredyt-/debitkaart 2 Kredyt-/debitkaart (backup) Crypto %(bitcoin_icon)s Kaart / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (regulier) Pix (Brazylje) Revolut (tydlik net beskikber) WeChat Selektearje jo foarkar crypto munt: Donearje mei in Amazon gift card. <strong>BELANGRIJK:</strong> Dizze opsje is foar %(amazon)s. As jo in oare Amazon-webside brûke wolle, selektearje it dan hjirboppe. <strong>IMPORTANT:</strong> Wy stypje allinnich Amazon.com, net oare Amazon-websiden. Bygelyks, .de, .co.uk, .ca, wurde NET stipe. Skriuw asjebleaft NET jo eigen berjocht. Fier it krekte bedrach yn: %(amount)s Tink derom dat wy moatte ôfroune nei bedragen dy't akseptearre wurde troch ús ferkeapers (minimum %(minimum)s). Donearje mei in kredyt-/debitkaart, fia de Alipay-app (super maklik om yn te stellen). Ynstallearje de Alipay-app fan de <a %(a_app_store)s>Apple App Store</a> of <a %(a_play_store)s>Google Play Store</a>. Registrearje mei jo telefoannûmer. Gjin fierdere persoanlike details binne nedich. <span %(style)s>1</span>Ynstallearje Alipay-app Stipe: Visa, MasterCard, JCB, Diners Club en Discover. Sjoch <a %(a_alipay)s>dizze gids</a> foar mear ynformaasje. <span %(style)s>2</span>Foegje bankkaart ta Mei Binance keapje jo Bitcoin mei in kredyt-/debitkaart of bankrekken, en donearje dan dy Bitcoin oan ús. Sa kinne wy feilich en anonym bliuwe by it akseptearjen fan jo donaasje. Binance is beskikber yn hast elk lân, en stipet de measte banken en kredyt-/debitkaarten. Dit is op it stuit ús haad oanbefelling. Wy wurdearje it dat jo de tiid nimme om te learen hoe't jo mei dizze metoade donearje kinne, om't it ús in soad helpt. Foar kredytkaarten, pinpassen, Apple Pay en Google Pay brûke wy "Buy Me a Coffee" (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Yn harren systeem is ien "kofje" gelyk oan $5, dus jo donaasje wurdt ôfrûne nei it tichtste mearfâld fan 5. Donearje mei Cash App. As jo Cash App hawwe, is dit de maklikste manier om te donearjen! Merk op dat foar transaksjes ûnder %(amount)s, Cash App in %(fee)s fergoeding yn rekken bringe kin. Foar %(amount)s of mear, is it fergees! Donearje mei in kredyt- of pinpas. Dizze metoade brûkt in cryptocurrency provider as in tuskenlizzende konverzje. Dit kin wat betiizjend wêze, dus brûk dizze metoade allinnich as oare betelmethoden net wurkje. It wurket ek net yn alle lannen. Wy kinne kredyt-/debitkaarten net direkt stypje, om't banken net mei ús wurkje wolle. ☹ Lykwols, der binne ferskate manieren om kredyt-/debitkaarten dochs te brûken, mei oare betelmethoden: Mei crypto kinne jo donearje mei BTC, ETH, XMR, en SOL. Brûk dizze opsje as jo al bekend binne mei cryptocurrency. Mei crypto kinne jo donearje mei BTC, ETH, XMR, en mear. Crypto express tsjinsten As jo foar it earst krypto brûke, riede wy oan om %(options)s te brûken om Bitcoin te keapjen en te donearjen (de orizjinele en meast brûkte kryptomunt). Tink derom dat foar lytse donaasjes de kredytkaartkosten ús %(discount)s%% koarting kinne eliminearje, dus wy riede langere abonneminten oan. Donearje mei kredyt-/debitkaart, PayPal, of Venmo. Jo kinne tusken dizze kieze op de folgjende side. Google Pay en Apple Pay kinne ek wurkje. Tink derom dat foar lytse donaasjes de fergoedingen heech binne, dus wy riede langere abonneminten oan. Om te donearjen mei PayPal US, sille wy PayPal Crypto brûke, wat ús anonym bliuwt. Wy wurdearje it dat jo de tiid nimme om te learen hoe't jo mei dizze metoade donearje kinne, om't it ús in soad helpt. Donearje mei PayPal. Donearje mei jo reguliere PayPal-akkount. Donearje mei Revolut. As jo Revolut hawwe, is dit de maklikste manier om te donearjen! Dizze betelmetoade lit allinich in maksimum fan %(amount)s ta. Kies in oare doer of betelmetoade. Dizze betelmetoade fereasket in minimum fan %(amount)s. Selektearje in oare doer of betelmetoade. Binance Coinbase Kraken Kies in betellingsmetoade. “Adoptearje in torrent”: jo brûkersnamme of berjocht yn in torrent-bestânsnamme <div %(div_months)s>ien kear elke 12 moannen fan lidmaatskip</div> Jo brûkersnamme of anonyme fermelding yn de credits Iere tagong ta nije funksjes Eksklusive Telegram mei efter-de-skermen updates %(number)s snelle downloads per dei as jo dizze moanne donearrest! <a %(a_api)s>JSON API</a> tagong Legindaryske status yn it behâld fan 'e kennis en kultuer fan 'e minskheid Eardere foardielen, plus: Fertsjinje <strong>%(percentage)s%% bonus downloads</strong> troch <a %(a_refer)s>freonen troch te ferwizen</a>. SciDB papieren <strong>ûnbeheind</strong> sûnder ferifikaasje By it stellen fan fragen oer akkount of donaasje, foegje jo akkount-ID, skermôfbyldings, kwitânsjes, en safolle mooglik ynformaasje ta. Wy kontrolearje ús e-post mar elke 1-2 wiken, dus it net opnimmen fan dizze ynformaasje sil elke oplossing fertrage. Om noch mear downloads te krijen, <a %(a_refer)s>ferwize jo freonen</a>! Wy binne in lyts team fan frijwilligers. It kin ús 1-2 wiken duorje om te reagearjen. Merk op dat de akkountnamme of ôfbylding frjemd útsjen kin. Gjin soargen! Dizze akkounts wurde beheard troch ús donearpartners. Us akkounts binne net hackt. Donearje <span %(span_cost)s></span> <span %(span_label)s></span> foar 12 moannen “%(tier_name)s” foar 1 moanne “%(tier_name)s” foar 24 moannen “%(tier_name)s” foar 3 moannen “%(tier_name)s” foar 48 moannen “%(tier_name)s” foar 6 moannen “%(tier_name)s” foar 96 moannen “%(tier_name)s” Jo kinne de donaasje noch annulearje by it ôfrekkenjen. Klik op de donearknop om dizze donaasje te befestigjen. <strong>Belangrike opmerking:</strong> Crypto-prizen kinne wyld fluktuearje, soms sels sa folle as 20%% yn in pear minuten. Dit is noch altyd minder as de fergoedingen dy't wy hawwe mei in protte betellingsproviders, dy't faak 50-60%% rekkenje foar it wurkjen mei in "skaadgoedei" lykas ús. <u>As jo ús it ûntfangstbewiis stjoere mei de oarspronklike priis dy't jo betelle hawwe, sille wy jo akkount noch kreditearje foar it keazen lidmaatskip</u> (safolle't it ûntfangstbewiis net âlder is as in pear oeren). Wy wurdearje it echt dat jo ree binne om mei sokke dingen om te gean om ús te stypjen! ❤️ ❌ Der gie wat mis. Fernij de side en besykje it opnij. <span %(span_circle)s>1</span>Keapje Bitcoin op Paypal <span %(span_circle)s>2</span>Transfer de Bitcoin nei ús adres ✅ Trochferwizing nei de donaasjepagina… Donearje Wachtsje op syn minst <span %(span_hours)s>24 oeren</span> (en ferfarskje dizze side) foardat jo kontakt mei ús opnimme. As jo in donaasje (elk bedrach) sûnder lidmaatskip meitsje wolle, brûk dan dizze Monero (XMR) adres: %(address)s. Nei it ferstjoeren fan jo kadokaart sil ús automatisearre systeem it binnen in pear minuten befestigje. As dit net wurket, besykje jo kadokaart opnij te ferstjoeren (<a %(a_instr)s>ynstruksjes</a>). As dat noch net wurket, stjoer ús dan in e-mail en Anna sil it hânmjittich besjen (dit kin in pear dagen duorje), en soargje derfoar dat jo neame as jo al besocht hawwe it opnij te stjoeren. Foarbyld: Brûk asjebleaft it <a %(a_form)s>offisjele Amazon.com-formulier</a> om ús in kadokaart fan %(amount)s te stjoeren nei it e-mailadres hjirûnder. "To" ûntfanger e-post yn it formulier: Amazon kadokaart Wy kinne gjin oare metoaden fan kadokaarten akseptearje, <strong>allinne direkt stjoerd fan it offisjele formulier op Amazon.com</strong>. Wy kinne jo kadokaart net werombringe as jo dit formulier net brûke. Mar ien kear brûke. Unyk foar jo akkount, net diele. Wachtsje op kadokaart… (ferfarsje de side om te kontrolearjen) Iepenje de <a %(a_href)s>QR-koade donaasje side</a>. Scan de QR-koade mei de Alipay-app, of druk op de knop om de Alipay-app te iepenjen. Haw geduld; de side kin in skoftke duorje om te laden om't it yn Sina is. <span %(style)s>3</span>Doch in donaasje (scan de QR-koade of druk op de knop) Keapje PYUSD-munt op PayPal Keap Bitcoin (BTC) op Cash App Keap in bytsje mear (wy riede %(more)s mear oan) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt. Gean nei de “Bitcoin” (BTC) side yn Cash App. Ferfier de Bitcoin nei ús adres Foar lytse donaasjes (ûnder $25), kinne jo Rush of Priority brûke moatte. Klik op de “Stjoer bitcoin” knop om in “útbetelling” te meitsjen. Skeakelje fan dollars nei BTC troch op it %(icon)s ikoan te drukken. Fier it BTC-bedrach hjirûnder yn en klik op “Stjoer”. Sjoch <a %(help_video)s>dit fideo</a> as jo fêst sitte. Express tsjinsten binne handich, mar rekkenje hegere kosten. Jo kinne dit brûke ynstee fan in crypto-útwikseling as jo fluch in gruttere donaasje meitsje wolle en gjin probleem hawwe mei in fergoeding fan $5-10. Soargje derfoar dat jo it krekte crypto-bedrach stjoere dat op de donaasje-side stiet, net it bedrach yn $USD. Oars wurdt de fergoeding subtrakt en kinne wy jo lidmaatskip net automatysk ferwurkje. Soms kin it oant 24 oeren duorje foar't de befêstiging komt, soargje derfoar dat jo dizze side ferfarskje (sels as it ferrûn is). Ynliedings foar kredyt- / pinpas Donearje fia ús kredyt- / pinpas-side Guon fan 'e stappen neame krypto-wallets, mar meitsje jo gjin soargen, jo hoege hjirfoar neat oer krypto te learen. %(coin_name)s ynstruksjes Scan dizze QR-koade mei jo Crypto Wallet-app om de betellingsdetails fluch te foljen Scan QR-koade om te beteljen Wy stypje allinich de standertferzje fan kryptomunten, gjin eksoatyske netwurken of ferzjes fan munten. It kin oant in oere duorje om de transaksje te befestigjen, ôfhinklik fan de munt. Donearje %(amount)s op <a %(a_page)s>dizze side</a>. Dizze donaasje is ferrûn. Annulearje en meitsje in nije. As jo al betelle hawwe: Ja, ik haw myn kwitânsje e-maild As de crypto-wisselkoers skommele tidens de transaksje, soargje der dan foar dat jo it ûntfangstbewiis opnimme dat de oarspronklike wisselkoers toant. Wy wurdearje it echt dat jo de muoite nimme om crypto te brûken, it helpt ús in protte! ❌ Der gie wat mis. Ferfarsje de side en besykje it opnij. <span %(span_circle)s>%(circle_number)s</span>Stjoer ús de kwitânsje per e-mail As jo problemen tsjinkomme, nim dan kontakt mei ús op fia %(email)s en jou safolle mooglik ynformaasje (lykas skermôfbyldings). ✅ Tank foar jo donaasje! Anna sil jo lidmaatskip binnen in pear dagen manuell aktivearje. Stjoer in ûntfangst of skermôfbylding nei jo persoanlike ferifikaasje-adres: As jo jo kwitânsje e-maild hawwe, klik dan op dizze knop, sadat Anna it manuell besjen kin (dit kin in pear dagen duorje): Stjoer in kwitânsje of skermôfbylding nei jo persoanlike ferifikaasjeadres. Brûk dit e-mailadres NET foar jo PayPal-donaasje. Annulearje Ja, annulearje asjebleaft Binne jo wis dat jo wolle annulearje? Net annulearje as jo al betelle hawwe. ❌ Der gie wat mis. Laad de side opnij en besykje it nochris. Meitsje in nije donaasje ✅ Jo donaasje is annulearre. Datum: %(date)s Identifier: %(id)s Weromsette Status: <span %(span_label)s>%(label)s</span> Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moanne foar %(duration)s moannen, ynklusyf %(discounts)s%% koarting)</span> Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moanne foar %(duration)s moannen)</span> 1. Fier jo e-mailadres yn. 2. Selektearje jo betelmetoade. 3. Selektearje jo betelmetoade opnij. 4. Selektearje “Self-hosted” wallet. 5. Klik op “Ik befêstigje eigendom”. 6. Jo moatte in e-mail kwitânsje ûntfange. Stjoer dat nei ús, en wy befestigje jo donaasje sa gau mooglik. (jo wolle miskien annulearje en in nije donaasje meitsje) De betellingsynstruksjes binne no ferâldere. As jo in oare donaasje dwaan wolle, brûk de “Opnij bestelle” knop hjirboppe. Jo hawwe al betelle. As jo de betellingsynstruksjes dochs besjen wolle, klik hjir: Toan âlde betellingsynstruksjes As de donearside blokkearre wurdt, besykje in oare ynternetferbining (bygelyks VPN of telefoanynternet). Spitigernôch is de Alipay-side faak allinnich tagonklik fanôf <strong>fêstelân Sina</strong>. Jo moatte miskien jo VPN tydlik útskeakelje, of in VPN brûke nei fêstelân Sina (of Hong Kong wurket soms ek). <span %(span_circle)s>1</span>Donearje op Alipay Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit Alipay-akkount</a> Alipay ynstruksjes <span %(span_circle)s>1</span>Oermeitsje nei ien fan ús crypto-akkounts Donearje it totale bedrach fan %(total)s oan ien fan dizze adressen: Crypto-ynstruksjes Folchje de ynstruksjes om Bitcoin (BTC) te keapjen. Jo moatte allinich it bedrach keapje dat jo donearje wolle, %(total)s. Fier ús Bitcoin (BTC) adres yn as de ûntfanger, en folgje de ynstruksjes om jo donaasje fan %(total)s te stjoeren: <span %(span_circle)s>1</span>Donearje op Pix Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit Pix-akkount Pix ynstruksjes <span %(span_circle)s>1</span>Donearje op WeChat Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit WeChat-akkount</a> WeChat-ynstruksjes Brûk ien fan de folgjende “kredytkaart nei Bitcoin” ekspresstsjinsten, dy't mar in pear minuten duorje: BTC / Bitcoin adres (eksterne portemonnee): BTC / Bitcoin bedrach: Folje de folgjende details yn it formulier yn: As dizze ynformaasje ferâldere is, stjoer ús dan in e-mail om it ús witte te litten. Brûk dit <span %(underline)s>krekte bedrach</span>. Jo totale kosten kinne heger wêze fanwegen kredytkaartkosten. By lytse bedragen kin dit spitigernôch mear wêze as ús koarting. (minimum: %(minimum)s, gjin ferifikaasje foar earste transaksje) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, gjin ferifikaasje foar earste transaksje) (minimum: %(minimum)s) (minimum: %(minimum)s ôfhinklik fan lân, gjin ferifikaasje foar earste transaksje) Folgjende de ynstruksjes om PYUSD-munt (PayPal USD) te keapjen. Keapje in bytsje mear (wy riede oan %(more)s mear) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt. Gean nei de "PYUSD" side yn jo PayPal app of webside. Druk op de "Oermeitsje" knop %(icon)s, en dan "Stjoere". Status bywurkje Om de timer te resetten, meitsje gewoan in nije donaasje. Soargje derfoar dat jo it BTC-bedrach hjirûnder brûke, <em>NET</em> euro's of dollars, oars ûntfange wy it korrekte bedrach net en kinne wy jo lidmaatskip net automatysk befestigje. Keap Bitcoin (BTC) op Revolut Keap in bytsje mear (wy riede %(more)s mear oan) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt. Gean nei de “Crypto” side yn Revolut om Bitcoin (BTC) te keapjen. Ferfier de Bitcoin nei ús adres Foar lytse donaasjes (ûnder $25) kinne jo Rush of Priority brûke moatte. Klik op de “Stjoer bitcoin” knop om in “útbetelling” te meitsjen. Skeakelje fan euro's nei BTC troch op it %(icon)s ikoan te drukken. Fier it BTC-bedrach hjirûnder yn en klik op “Stjoer”. Sjoch <a %(help_video)s>dit fideo</a> as jo fêst sitte. Status: 1 2 Stap-foar-stap gids Sjoch de stap-foar-stap gids hjirûnder. Oars kinne jo út dit akkount sluten wurde! As jo it noch net dien hawwe, skriuw jo geheime kaai foar it ynloggen op: Tige tank foar jo donaasje! Oerbleaune tiid: Donearje Ferfier %(amount)s nei %(account)s Wachtsje op befêstiging (fernij de side om te kontrolearjen)… Wachtsje op oerdracht (ferfarsje de side om te kontrolearjen)… Earder Flugge downloads yn de lêste 24 oeren telle mei foar it deistige limyt. Downloads fan Fast Partner Servers binne markearre mei %(icon)s. Lêste 18 oeren Noch gjin bestannen downloade. Downloaded bestannen wurde net iepenbier toand. Alle tiden binne yn UTC. Downloadde bestannen As jo in bestân downloade mei sawol snelle as stadige downloads, sil it twa kear ferskine. Meitsje jo gjin soargen, der binne in protte minsken dy't downloade fan websiden dy't troch ús keppele binne, en it is tige seldsum om yn problemen te kommen. Mar om feilich te bliuwen, riede wy oan om in VPN (betelle) te brûken, of <a %(a_tor)s>Tor</a> (fergees). Ik haw 1984 fan George Orwell downloade, sil de plysje by my oan de doar komme? Jo binne Anna! Wa is Anna? Wy hawwe ien stabile JSON API foar leden, foar it krijen fan in snelle download-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentaasje binnen de JSON sels). Foar oare gebrûksgefallen, lykas it iterearjen troch al ús bestannen, it bouwen fan oanpaste sykfunksjes, ensafuorthinne, riede wy oan <a %(a_generate)s>it generearjen</a> of <a %(a_download)s>it downloaden</a> fan ús ElasticSearch en MariaDB databases. De rauwe gegevens kinne manuell ferkend wurde <a %(a_explore)s>troch JSON-bestannen</a>. Us list mei rauwe torrents kin ek downloade wurde as <a %(a_torrents)s>JSON</a>. Hawwe jo in API? Wy hostje hjir gjin auteursrjochtlik beskerme materialen. Wy binne in sykmasine, en sa indexearje wy allinnich metadata dy't al iepenbier beskikber is. By it downloaden fan dizze eksterne boarnen, advisearje wy om de wetten yn jo jurisdiksje te kontrolearjen mei respekt foar wat tastien is. Wy binne net ferantwurdlik foar ynhâld host troch oaren. As jo klachten hawwe oer wat jo hjir sjogge, is it bêste om kontakt op te nimmen mei de orizjinele webside. Wy helje har wizigingen regelmjittich yn ús databank. As jo echt tinke dat jo in jildige DMCA-klacht hawwe dêr't wy op reagearje moatte, folje dan it <a %(a_copyright)s>DMCA / Copyright claim form</a> yn. Wy nimme jo klachten serieus en sille sa gau mooglik op jo weromkomme. Hoe meld ik auteursrjochtferbrek? Hjir binne wat boeken dy't spesjale betsjutting hawwe foar de wrâld fan skaadbiblioteken en digitale preservaasje: Wat binne jo favorite boeken? Wy wolle elkenien ek graach derop wize dat al ús koade en gegevens folslein iepen boarne binne. Dit is unyk foar projekten lykas ús — wy binne net bewust fan in oar projekt mei in fergelykber massyf katalogus dat ek folslein iepen boarne is. Wy ferwolkomje elkenien dy't tinkt dat wy ús projekt min beheare om ús koade en gegevens te nimmen en harren eigen skaadbiblioteek op te setten! Wy sizze dit net út wrok of sa — wy tinke echt dat dit geweldich wêze soe, om't it de standert foar elkenien ferheegje soe, en de erfenis fan 'e minskheid better bewarje soe. Ik haatsje hoe't jo dit projekt rinne! Wy soene it geweldich fine as minsken <a %(a_mirrors)s>spegeltsjes</a> opsette, en wy sille dit finansjeel stypje. Hoe kin ik helpe? Dat dogge wy yndie. Us ynspiraasje foar it sammeljen fan metadata is Aaron Swartz’ doel fan “ien webside foar elk boek dat ea publisearre is”, dêr't hy <a %(a_openlib)s>Open Library</a> foar makke. Dat projekt hat it goed dien, mar ús unike posysje makket it mooglik om metadata te krijen dy't sy net kinne. In oare ynspiraasje wie ús winsk om te witten <a %(a_blog)s>hoefolle boeken der yn 'e wrâld binne</a>, sadat wy kinne berekkenje hoefolle boeken wy noch moatte rêde. Sammelje jo metadata? Merk op dat mhut.org bepaalde IP-berik blokkearret, dus in VPN kin nedich wêze. <strong>Android:</strong> Klik op it trijepuntsmenu yn de boppeste rjochterhoeke, en selektearje “Tafoegje oan startskerm”. <strong>iOS:</strong> Klik op de knop “Share” oan de ûnderkant, en selektearje “Add to Home Screen”. Wy hawwe gjin offisjele mobile app, mar jo kinne dizze webside as in app ynstallearje. Hawwe jo in mobile app? Stjoer se asjebleaft nei de <a %(a_archive)s>Internet Archive</a>. Sy sille se goed bewarje. Hoe kin ik boeken of oare fysike materialen donearje? Hoe freegje ik boeken oan? <a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — reguliere updates <a %(a_software)s>Anna’s Software</a> — ús iepen boarne koade <a %(a_datasets)s>Datasets</a> — oer de gegevens <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domeinen Binne der mear boarnen oer Anna’s Argyf? <a %(a_translate)s>Oersette op Anna’s Software</a> — ús oersettingssysteem <a %(a_wikipedia)s>Wikipedia</a> — mear oer ús (help asjebleaft dizze side bywurkje, of meitsje ien foar jo eigen taal!) Selektearje de ynstellings dy't jo leuk fine, hâld it sykfak leech, klik op “Sykje”, en markearje dan de side mei de blêdwizerfunksje fan jo browser. Hoe bewarje ik myn sykynstellingen? Wy ferwolkomje befeiligingsûndersikers om nei kwetsberens yn ús systemen te sykjen. Wy binne grutte foarstanners fan ferantwurde iepenbiering. Nim kontakt mei ús op <a %(a_contact)s>hjir</a>. Wy kinne op it stuit gjin bug bounties útrikke, útsein foar kwetsberens dy't de <a %(a_link)s >potinsje hawwe om ús anonimiteit te kompromittearjen</a>, dêrfoar biede wy bounties yn it berik fan $10k-50k. Wy wolle yn 'e takomst in bredere omfang foar bug bounties oanbiede! Tink derom dat sosjale engineering-oanfallen bûten de omfang falle. As jo ynteressearre binne yn offensive security, en wolle helpe om de kennis en kultuer fan 'e wrâld te argivearjen, nim dan kontakt mei ús op. D'r binne in protte manieren wêrop jo helpe kinne. Hawwe jo in ferantwurdlike iepenbieringprogramma? Wy hawwe letterlik net genôch middels om elkenien yn 'e wrâld hege-snelheid downloads te jaan, hoe graach wy dat ek wolle. As in rike beskermhear ús dit foar ús leverje wol, soe dat ynkringend wêze, mar oant dan besykje wy ús bêst. Wy binne in non-profit projekt dat amper troch donaasjes ûnderhâlden wurde kin. Dêrom hawwe wy twa systemen foar fergese downloads ymplemintearre, mei ús partners: dielde servers mei stadige downloads, en wat fluggere servers mei in wachtlist (om it tal minsken dat tagelyk downloadt te ferminderjen). Wy hawwe ek <a %(a_verification)s>browserferifikaasje</a> foar ús stadige downloads, om't oars bots en scrapers se misbrûke, wat dingen noch stadiger makket foar legitime brûkers. Merk op dat, by it brûken fan de Tor Browser, jo miskien jo feiligensynstellingen oanpasse moatte. Op de leechste fan de opsjes, neamd “Standert”, slagget de Cloudflare turnstile útdaging. Op de hegere opsjes, neamd “Feiliger” en “Feilichst”, mislearret de útdaging. Foar grutte bestannen kinne stadige downloads soms yn 'e midden brekke. Wy riede oan om in downloadmanager (lykas JDownloader) te brûken om automatysk grutte downloads te hervatten. Wêrom binne de stadige downloads sa stadich? Faak Stelde Fragen (FAQ) Brûk de <a %(a_list)s>torrentlistgenerator</a> om in list fan torrents te generearjen dy't it meast nedich binne om te torrenten, binnen jo opslachromte limiten. Ja, sjoch de <a %(a_llm)s>LLM data</a> side. De measte torrents befetsje de bestannen direkt, wat betsjut dat jo torrentkliïnten kinne ynstruearje om allinich de nedige bestannen te downloaden. Om te bepalen hokker bestannen te downloaden, kinne jo ús metadata <a %(a_generate)s>generearje</a>, of ús ElasticSearch- en MariaDB-databases <a %(a_download)s>downloaden</a>. Spitigernôch befetsje in oantal torrentkolleksjes .zip- of .tar-bestannen oan de woartel, yn dat gefal moatte jo de hiele torrent downloade foardat jo yndividuele bestannen selektearje kinne. Gjin maklike ark om torrents te filterjen binne noch beskikber, mar wy ferwolkomje bydragen. (Wy hawwe lykwols <a %(a_ideas)s>wat ideeën</a> foar it lêste gefal.) Lang antwurd: Koart antwurd: net maklik. Wy besykje minimale duplikaasje of oerlap tusken de torrents yn dizze list te hâlden, mar dit kin net altyd berikt wurde, en hinget sterk ôf fan it belied fan de boarnebiblioteken. Foar biblioteken dy't harren eigen torrents útbringe, is it bûten ús hannen. Foar torrents útbrocht troch Anna’s Argyf, deduplikearje wy allinnich basearre op MD5 hash, wat betsjut dat ferskillende ferzjes fan itselde boek net deduplikearre wurde. Ja. Dit binne eins PDFs en EPUBs, se hawwe gewoan gjin útwreiding yn in soad fan ús torrents. Der binne twa plakken wêr't jo de metadata foar torrentbestannen fine kinne, ynklusyf de bestânstypen/útwreidingen: 1. Elke kolleksje of release hat syn eigen metadata. Bygelyks, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> hawwe in oerienkommende metadata-database host op de Libgen.rs-webside. Wy keppelje typysk nei relevante metadata-boarnen fan elke kolleksje’s <a %(a_datasets)s>dataset-side</a>. 2. Wy riede oan om ús ElasticSearch- en MariaDB-databases te <a %(a_generate)s>generearjen</a> of te <a %(a_download)s>downloaden</a>. Dizze befetsje in mapping foar elk rekord yn Anna’s Archive nei de oerienkommende torrentbestannen (as beskikber), ûnder "torrent_paths" yn de ElasticSearch JSON. Guon torrent-kliïnten stypje gjin grutte stikgrutte, dy't in protte fan ús torrents hawwe (foar nijere dogge wy dit net mear - sels al is it neffens de spesifikaasjes!). Besykje in oare kliïnt as jo hjir tsjinoan rinne, of klagje by de makkers fan jo torrent-kliïnt. Ik wol graach helpe mei seedjen, mar ik haw net folle skiifromte. De torrents binne te stadich; kin ik de gegevens direkt fan jo downloade? Kin ik allinich in subset fan 'e bestannen downloade, lykas allinich in bepaalde taal of ûnderwerp? Hoe geane jo om mei duplikaten yn de torrents? Kin ik de torrentlist as JSON krije? Ik sjoch gjin PDFs of EPUBs yn de torrents, allinnich binêre bestannen? Wat moat ik dwaan? Wêrom kin myn torrent-kliïnt guon fan jo torrent-bestannen / magneetkeppelings net iepenje? Torrents FAQ Hoe kin ik nije boeken uploade? Sjoch asjebleaft <a %(a_href)s>dit treflike projekt</a>. Hawwe jo in uptime-monitor? Wat is Anna’s Argyf? Wurdt lid om snelle downloads te brûken. Wy stypje no Amazon-cadeaukaarten, kredyt- en debitkaarten, crypto, Alipay, en WeChat. Jo hawwe hjoed gjin rappe downloads mear oer. Tagong Oere downloads yn de lêste 30 dagen. Oere trochsneed: %(hourly)s. Deistige trochsneed: %(daily)s. Wy wurkje mei partners om ús samlingen maklik en fergees tagonklik te meitsjen foar elkenien. Wy leauwe dat elkenien rjocht hat op de kollektive wiisheid fan 'e minskheid. En <a %(a_search)s>net ten koste fan auteurs</a>. De datasets dy't brûkt wurde yn Anna’s Archive binne folslein iepen, en kinne yn bulk spegele wurde mei torrents. <a %(a_datasets)s>Lear mear…</a> Langduorjend argyf Folsleine databank Sykje Boeken, papieren, tydskriften, strips, biblioteekrecords, metadata, … Al ús <a %(a_code)s>koade</a> en <a %(a_datasets)s>gegevens</a> binne folslein iepen boarne. <span %(span_anna)s>Anna’s Argyf</span> is in non-profit projekt mei twa doelen: <li><strong>Preservaasje:</strong> It bewarjen fan alle kennis en kultuer fan 'e minskheid.</li><li><strong>Tagong:</strong> It beskikber meitsjen fan dizze kennis en kultuer foar elkenien yn 'e wrâld.</li> Wy hawwe de grutste kolleksje fan heechweardige tekstgegevens yn 'e wrâld. <a %(a_llm)s>Lear mear…</a> LLM training data 🪩 Spegels: oprop foar frijwilligers As jo in heech-risiko anonyme betellingsferwurker rinne, nim dan kontakt mei ús op. Wy sykje ek minsken dy't lytse smaakfolle advertinsjes wolle pleatse. Alle opbringsten geane nei ús preservaasje-ynspanningen. Behâld Wy skatte dat wy sawat <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% fan 'e wrâld syn boeken bewarre hawwe</a>. Wy bewarje boeken, papieren, strips, tydskriften en mear troch dizze materialen fan ferskate <a href="https://en.wikipedia.org/wiki/Shadow_library">skadelibraries</a>, offisjele biblioteken en oare kolleksjes byinoar te bringen op ien plak. Al dizze gegevens wurde foar altyd bewarre troch it maklik te meitsjen om it yn bulk te duplisearjen - mei torrents - wat resultearret yn in protte kopyen oer de hiele wrâld. Guon skadelibraries dogge dit sels al (bgl. Sci-Hub, Library Genesis), wylst Anna’s Archive oare biblioteken "bevrijdt" dy't gjin bulkdistribúsje oanbiede (bgl. Z-Library) of hielendal gjin skadelibraries binne (bgl. Internet Archive, DuXiu). Dizze brede distribúsje, kombinearre mei iepen-boarne koade, makket ús webside resistint tsjin sluting, en soarget foar de lange-termyn behâld fan 'e kennis en kultuer fan 'e minskheid. Lear mear oer <a href="/datasets">ús datasets</a>. As jo in <a %(a_member)s>lid</a> binne, is browserferifikaasje net nedich. 🧬&nbsp;SciDB is in fuortsetting fan Sci-Hub. SciDB Iepenje DOI Sci-Hub hat <a %(a_paused)s>it uploaden</a> fan nije papieren pauzeare. Direkte tagong ta %(count)s akademyske papieren 🧬&nbsp;SciDB is in fuortsetting fan Sci-Hub, mei syn bekende interface en direkte besjen fan PDFs. Fier jo DOI yn om te besjen. Wy hawwe de folsleine Sci-Hub kolleksje, en ek nije papieren. De measten kinne direkt besjoen wurde mei in fertroude ynterface, fergelykber mei Sci-Hub. Guon kinne downloade wurde fia eksterne boarnen, yn dat gefal toane wy keppelings nei dy. Jo kinne enoarm helpe troch torrents te seedjen. <a %(a_torrents)s>Lear mear…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Op syk nei frijwilligers As in non-profit, iepen-boarne projekt, sykje wy altyd nei minsken om te helpen. IPFS downloads List troch %(by)s, makke <span %(span_time)s>%(time)s</span> Bewarje ❌ Der is wat misgien. Besykje it nochris. ✅ Bewarre. Fernij de side. List is leech. bewurkje Foegje ta of ferwiderje fan dizze list troch in bestân te finen en it ljepblêd "Listen" te iepenjen. List Hoe't wy helpe kinne Oerlap ferwiderje (deduplikearje) Tekst- en metadata-ekstraksje OCR Wy kinne hege-snelheid tagong biede ta ús folsleine kolleksjes, lykas ta net-frijlitten kolleksjes. Dit is tagong op bedriuwsnivo dy't wy kinne leverje foar donaasjes yn it berik fan tsientûzenen USD. Wy binne ek ree om dit te ruiljen foar heechweardige kolleksjes dy't wy noch net hawwe. Wy kinne jo werombetelje as jo ús kinne foarsjen mei ferbettering fan ús data, lykas: Stypje langduorjende argyfering fan minsklike kennis, wylst jo bettere data krije foar jo model! <a %(a_contact)s>Kontakt mei ús opnimme</a> om te besprekken hoe't wy gearwurkje kinne. It is goed begrepen dat LLM's bloeie op heechweardige data. Wy hawwe de grutste kolleksje fan boeken, papieren, tydskriften, ensfh. yn 'e wrâld, dy't guon fan 'e heechste kwaliteit tekstboarnen binne. LLM-data Unike skaal en berik Us kolleksje befettet mear as hûndert miljoen bestannen, ynklusyf akademyske tydskriften, learboeken, en tydskriften. Wy berikke dizze skaal troch grutte besteande repositories te kombinearjen. Guon fan ús boarnekolleksjes binne al yn bulk beskikber (Sci-Hub, en dielen fan Libgen). Oare boarnen hawwe wy sels befrijd. <a %(a_datasets)s>Datasets</a> toant in folslein oersjoch. Us kolleksje omfettet miljoenen boeken, papieren, en tydskriften fan foar it e-boek tiidrek. Grutte dielen fan dizze kolleksje binne al OCR'd, en hawwe al in bytsje ynterne oerlap. Trochgean As jo jo kaai kwyt binne, nim dan asjebleaft <a %(a_contact)s>kontakt mei ús op</a> en jou safolle mooglik ynformaasje. Jo moatte miskien tydlik in nij akkount oanmeitsje om kontakt mei ús op te nimmen. Asjebleaft <a %(a_account)s>ynlogge</a> om dizze side te besjen.</a> Om te foarkommen dat spam-bots in protte akkounts oanmeitsje, moatte wy earst jo browser ferifiearje. As jo yn in einleaze loop fêst komme, riede wy oan om <a %(a_privacypass)s>Privacy Pass</a> te ynstallearjen. It kin ek helpe om advertinsjeblokkers en oare browser-útwreidings út te setten. Ynlogge / Registrearje Anna’s Argyf is tydlik offline foar ûnderhâld. Kom oer in oere werom. Alternative auteur Alternative beskriuwing Alternative edysje Alternative útwreiding Alternatyf bestânsnamme Alternative útjouwer Alternatyf titel datum iepen boarne Lês mear… beskriuwing Sykje Anna’s Argyf foar CADAL SSNO nûmer Sykje Anna’s Argyf foar DuXiu SSID nûmer Sykje Anna’s Archive foar DuXiu DXID-nûmer Sykje yn Anna’s Archive op ISBN Sykje yn Anna’s Argyf nei OCLC (WorldCat) nûmer Sykje Anna’s Argyf foar Open Library ID Anna’s Argiven online besjogger %(count)s troffen siden Nei it downloaden: In bettere ferzje fan dit bestân kin beskikber wêze by %(link)s Bulk torrent downloads samling Brûk online ark om tusken formaten te konvertearjen. Oanbefellende konverzje-ark: %(links)s Foar grutte triemmen riede wy oan in downloadmanager te brûken om ûnderbrekkingen foar te kommen. Oanbefellende downloadmanagers: %(links)s EBSCOhost eBook Yndeks (allinnich foar saakkundigen) (klik ek op “GET” oan de boppekant) (klik op “GET” oan de boppekant) Eksterne downloads <strong>🚀 Fluch downloads</strong> Jo hawwe %(remaining)s oer hjoed. Tank foar it lid wêzen! ❤️ <strong>🚀 Snelle downloads</strong> Jo hawwe hjoed gjin snelle downloads mear. <strong>🚀 Fluch downloads</strong> Jo hawwe dit bestân koartlyn downloade. Keppelings bliuwe in skoftke jildich. <strong>🚀 Fluch downloads</strong> Wur in <a %(a_membership)s>lid</a> om de langduorjende bewarjen fan boeken, papieren en mear te stypjen. Om ús tankberens foar jo stipe te toanen, krije jo flugge downloads. ❤️ 🚀 Fluch downloads 🐢 Stadige downloads Liene fan it Internet Archive IPFS Gateway #%(num)d (jo moatte miskien meardere kearen besykje mei IPFS) Libgen.li Libgen.rs Fiksje Libgen.rs Non-Fiction harren advertinsjes binne bekend om kweade software te befetsjen, dus brûk in advertinsjeblokker of klik net op advertinsjes Amazon's "Send to Kindle" djazz's "Send to Kobo/Kindle" MagzDB ManualsLib Nexus/STC (Nexus/STC-bestannen kinne ûnbetrouber wêze om te downloaden) Gjin downloads fûn. Alle downloadopsjes hawwe itselde bestân, en moatte feilich wêze om te brûken. Dat sein hawwende, wês altyd foarsichtich by it downloaden fan bestannen fan it ynternet, benammen fan siden bûten Anna’s Archive. Hâld bygelyks jo apparaten bywurke. (gjin trochferwizing) Iepenje yn ús besjogger (iepenje yn besjogger) Opsje #%(num)d: %(link)s %(extra)s Fyn orizjineel rekord yn CADAL Manueel sykje op DuXiu Fyn orizjineel rekord yn ISBNdb Fyn orizjineel rekord yn WorldCat Fyn orizjineel rekord yn Open Library Sykje yn ferskate oare databases nei ISBN (allinnich foar print-beheinde patrons) PubMed Jo hawwe in e-boek of PDF-lêzer nedich om it triem te iepenjen, ôfhinklik fan it triemformaat. Oanbefellende e-boeklêzers: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (assosjearre DOI kin net beskikber wêze yn Sci-Hub) Jo kinne sawol PDF- as EPUB-bestannen nei jo Kindle of Kobo eReader stjoere. Oanbefellende ark: %(links)s Mear ynformaasje yn de <a %(a_slow)s>FAQ</a>. Stypje auteurs en biblioteken As jo dit leuk fine en it kinne betelje, beskôgje dan it orizjineel te keapjen, of de auteurs direkt te stypjen. As dit beskikber is by jo lokale biblioteek, beskôgje it dan dêr fergees te lienjen. Partner Server downloads tydlik net beskikber foar dit bestân. torrent Fan fertroude partners. Z-Library Z-Bibleteek op Tor (fereasket de Tor Browser) toan eksterne downloads <span class="font-bold">❌ Dit bestân kin problemen hawwe, en is ferburgen fan in boarnebiblioteek.</span> Soms is dit op fersyk fan in auteursrjochteigner, soms is it om't in better alternatyf beskikber is, mar soms is it om't der in probleem is mei it bestân sels. It kin noch hieltyd goed wêze om te downloaden, mar wy riede oan earst te sykjen nei in alternatyf bestân. Mear details: As jo dit bestân noch downloade wolle, soargje derfoar dat jo allinich fertroude, bywurke software brûke om it te iepenjen. metadata opmerkingen AA: Sykje yn Anna’s Archive nei “%(name)s” Koade-ûndersiker: Besjoch yn Codes Explorer "%(name)s" URL: Webside: As jo dit bestân hawwe en it is noch net beskikber yn Anna’s Archive, beskôgje dan <a %(a_request)s>it te uploaden</a>. Internet Archive Controlled Digital Lending-bestân “%(id)s” Dit is in rekord fan in bestân fan de Internet Archive, net in direkt te downloaden bestân. Jo kinne besykje it boek te lienen (keppeling hjirûnder), of brûk dizze URL by it <a %(a_request)s>oanfreegjen fan in bestân</a>. Ferbetterje metadata CADAL SSNO %(id)s metadata rekord Dit is in metadata-record, gjin te downloaden bestân. Jo kinne dizze URL brûke by it <a %(a_request)s>oanfreegjen fan in bestân</a>. DuXiu SSID %(id)s metadata rekord ISBNdb %(id)s metadata rekord MagzDB ID %(id)s metadata record Nexus/STC ID %(id)s metadata record OCLC (WorldCat) nûmer %(id)s metadata record Open Library %(id)s metadata-record Sci-Hub-bestân “%(id)s” Net fûn “%(md5_input)s” waard net fûn yn ús databank. Foegje reaksje ta (%(count)s) Jo kinne de md5 fan de URL krije, bygelyks MD5 fan in bettere ferzje fan dit bestân (as fan tapassing). Folje dit yn as der in oar bestân is dat tichtby dit bestân komt (deselde edysje, deselde bestânsútwreiding as jo ien fine kinne), dat minsken ynstee fan dit bestân brûke moatte. As jo in bettere ferzje fan dit bestân bûten Anna’s Argyf kenne, upload it dan asjebleaft <a %(a_upload)s>hjir</a>. Der is wat misgien. Laad de side opnij en besykje it nochris. Jo hawwe in reaksje efterlitten. It kin in minút duorje foardat it ferskynt. Brûk asjebleaft it <a %(a_copyright)s>DMCA / Copyright claim formulier</a>. Beskriuw it probleem (ferplicht) As dit bestân fan hege kwaliteit is, kinne jo hjir alles deroer beprate! As net, brûk dan de knop "Meld bestânsprobleem". Prachtige bestânkwaliteit (%(count)s) Bestânkwaliteit Learje hoe't jo sels de <a %(a_metadata)s>metadata foar dit bestân ferbetterje</a> kinne. Probleembeskriuwing Asjebleaft <a %(a_login)s>meld jo oan</a>. Ik fûn dit boek geweldich! Help de mienskip troch de kwaliteit fan dit bestân te melden! 🙌 Der is wat misgien. Laad de side opnij en besykje it nochris. Meld bestânsprobleem (%(count)s) Tige tank foar it yntsjinjen fan jo rapport. It sil op dizze side toand wurde, en ek hânmjittich besjoen wurde troch Anna (oant wy in goed moderaasjesysteem hawwe). Lit in reaksje efter Stjoer rapport yn Wat is der mis mei dit bestân? Liene (%(count)s) Kommentaren (%(count)s) Downloads (%(count)s) Ferken metadata (%(count)s) Listen (%(count)s) Statistiken (%(count)s) Foar ynformaasje oer dit spesifike bestân, sjoch syn <a %(a_href)s>JSON-bestân</a>. Dit is in bestân beheard troch de <a %(a_ia)s>IA's Controlled Digital Lending</a> bibleteek, en yndeksearre troch Anna’s Archive foar sykjen. Foar ynformaasje oer de ferskate datasets dy't wy gearstald hawwe, sjoch de <a %(a_datasets)s>Datasets side</a>. Metadata fan keppele rekord Ferbetterje metadata op Open Library In "bestân MD5" is in hash dy't berekkene wurdt út de ynhâld fan it bestân, en is ridlik unyk basearre op dy ynhâld. Alle skaadbiblioteken dy't wy hjir yndeksearre hawwe brûke foaral MD5's om bestannen te identifisearjen. In bestân kin ferskine yn meardere skaadbiblioteken. Foar ynformaasje oer de ferskate datasets dy't wy gearstald hawwe, sjoch de <a %(a_datasets)s>Datasets side</a>. Meld bestânkwaliteit Totaal oantal downloads: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Tsjechyske metadata %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Warskôging: meardere keppele records: As jo in boek besjogge op Anna’s Argyf, kinne jo ferskate fjilden sjen: titel, auteur, útjouwer, edysje, jier, beskriuwing, bestânsnamme, en mear. Al dy stikjes ynformaasje wurde <em>metadata</em> neamd. Om't wy boeken kombinearje út ferskate <em>boarnenbiblioteken</em>, toane wy hokker metadata beskikber is yn dy boarnebiblioteek. Bygelyks, foar in boek dat wy krigen hawwe fan Library Genesis, sille wy de titel út de database fan Library Genesis sjen litte. Soms is in boek oanwêzich yn <em>meardere</em> boarnebiblioteken, dy't ferskillende metadatafjilden hawwe kinne. Yn dat gefal toane wy gewoan de langste ferzje fan elk fjild, om't dy hopelik de meast nuttige ynformaasje befettet! Wy sille de oare fjilden noch ûnder de beskriuwing sjen litte, bygelyks as "alternative titel" (mar allinich as se oars binne). Wy helje ek <em>koades</em> lykas identifiers en classifiers út de boarnebiblioteek. <em>Identifiers</em> fertsjintwurdigje unyk in bepaalde edysje fan in boek; foarbylden binne ISBN, DOI, Open Library ID, Google Books ID, of Amazon ID. <em>Classifiers</em> groepearje meardere ferlykbere boeken; foarbylden binne Dewey Decimal (DCC), UDC, LCC, RVK, of GOST. Soms binne dizze koades eksplisyt keppele yn boarnebiblioteken, en soms kinne wy se helje út de bestânsnamme of beskriuwing (foarnaamste ISBN en DOI). Wy kinne identifiers brûke om records te finen yn <em>metadata-allinnich kolleksjes</em>, lykas OpenLibrary, ISBNdb, of WorldCat/OCLC. Der is in spesifike <em>metadata ljepper</em> yn ús sykmasine as jo dy kolleksjes blêdzje wolle. Wy brûke oerienkommende records om ûntbrekkende metadatafjilden yn te foljen (bygelyks as in titel ûntbrekt), of bygelyks as "alternative titel" (as der in besteande titel is). Om krekt te sjen wêr't metadata fan in boek wei kaam, sjoch de <em>“Technyske details” ljepper</em> op in boekside. It hat in keppeling nei de rauwe JSON foar dat boek, mei oanwizings nei de rauwe JSON fan de orizjinele records. Foar mear ynformaasje, sjoch de folgjende siden: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Sykje (metadata ljepper)</a>, <a %(a_codes)s>Koades Explorer</a>, en <a %(a_example)s>Foarbyld metadata JSON</a>. Uteinlik kin al ús metadata <a %(a_generated)s>generearre</a> of <a %(a_downloaded)s>downloade</a> wurde as ElasticSearch en MariaDB databases. Eftergrûn Jo kinne helpe by it behâld fan boeken troch metadata te ferbetterjen! Lês earst de eftergrûn oer metadata op Anna’s Argyf, en lear dan hoe't jo metadata kinne ferbetterje troch te keppeljen mei Open Library, en fertsjinje in fergees lidmaatskip op Anna’s Argyf. Ferbetterje metadata Dus as jo in bestân tsjinkomme mei minne metadata, hoe moatte jo it reparearje? Jo kinne nei de boarnebiblioteek gean en har prosedueres folgje foar it reparearjen fan metadata, mar wat te dwaan as in bestân oanwêzich is yn meardere boarnebiblioteken? Der is ien identifier dy't spesjaal behannele wurdt op Anna’s Argyf. <strong>It annas_archive md5 fjild op Open Library oerskriuwt altyd alle oare metadata!</strong> Litte wy earst in bytsje weromgean en leare oer Open Library. Open Library waard oprjochte yn 2006 troch Aaron Swartz mei it doel fan "ien webside foar elk boek dat ea publisearre is". It is in soarte fan Wikipedia foar boekmetadata: elkenien kin it bewurkje, it is frij lisinsjeare, en kin yn bulk downloade wurde. It is in boekdatabase dy't it meast oerienkomt mei ús missy - yn feite is Anna’s Argyf ynspirearre troch de fisy en it libben fan Aaron Swartz. Ynstee fan it tsjil op 'e nij út te finen, hawwe wy besletten ús frijwilligers troch te stjoeren nei Open Library. As jo in boek sjogge dat ferkearde metadata hat, kinne jo op de folgjende manier helpe: Tink derom dat dit allinich wurket foar boeken, net foar akademyske papieren of oare soarten triemen. Foar oare soarten triemen riede wy noch altyd oan om de boarnebiblioteek te finen. It kin in pear wiken duorje foardat feroarings opnommen wurde yn Anna’s Archive, om't wy de lêste Open Library data dump moatte downloade en ús sykindex opnij generearje moatte.  Gean nei de <a %(a_openlib)s>Open Library webside</a>. Fyn it korrekte boekrecord. <strong>WARSKÔGING:</strong> wês wis dat jo de korrekte <strong>edysje</strong> selektearje. Yn Open Library hawwe jo "wurken" en "edysjes". In "wurk" kin wêze "Harry Potter en de Stien fan de Wizen". In "edysje" kin wêze: De earste edysje fan 1997 útjûn troch Bloomsbery mei 256 siden. De paperback edysje fan 2003 útjûn troch Raincoast Books mei 223 siden. De Poalske oersetting fan 2000 “Harry Potter I Kamie Filozoficzn” troch Media Rodzina mei 328 siden. Al dy edysjes hawwe ferskillende ISBN's en ferskillende ynhâld, soargje derfoar dat jo de juste kieze! Bewurkje it rekord (of meitsje it as it net bestiet), en foegje safolle nuttige ynformaasje ta as jo kinne! Jo binne hjir no dochs, meitsje it rekord mar echt geweldich. Under “ID-nûmers” selektearje “Anna’s Archive” en foegje de MD5 fan it boek ta fan Anna’s Archive. Dit is de lange string fan letters en sifers nei “/md5/” yn de URL. Besykje oare triemen te finen yn Anna’s Archive dy't ek oerienkomme mei dit rekord, en foegje dy ek ta. Yn de takomst kinne wy dy groepearje as duplikaten op de sykpagina fan Anna’s Archive. As jo klear binne, skriuw dan de URL op dy't jo krekt bywurke hawwe. As jo teminsten 30 records hawwe bywurke mei Anna’s Archive MD5's, stjoer ús dan in <a %(a_contact)s>e-mail</a> en stjoer ús de list. Wy jouwe jo in fergees lidmaatskip foar Anna’s Archive, sadat jo dit wurk makliker dwaan kinne (en as tank foar jo help). Dizze moatte heechweardige bewurkingen wêze dy't substansjele hoemannichten ynformaasje tafoegje, oars wurdt jo fersyk ôfwiisd. Jo fersyk wurdt ek ôfwiisd as ien fan de bewurkingen weromset of korrizjearre wurdt troch Open Library moderators. Open Library keppeling As jo signifikant belutsen reitsje by de ûntwikkeling en operaasjes fan ús wurk, kinne wy besprekke om mear fan 'e donaasje-ynkomsten mei jo te dielen, sadat jo se nedichs brûke kinne. Wy sille allinnich betelje foar hosting as jo alles opset hawwe, en hawwe oantoand dat jo it argyf bywurkje kinne mei updates. Dit betsjut dat jo de earste 1-2 moannen út eigen bûse betelje moatte. Jo tiid sil net fergoede wurde (en ús ek net), om't dit suver frijwilligerswurk is. Wy binne ree om hosting- en VPN-útjeften te dekken, yn earste ynstânsje oant $200 per moanne. Dit is genôch foar in basis sykserver en in DMCA-beskerme proxy. Hostingkosten Nim asjebleaft <strong>gjin kontakt mei ús op</strong> om tastimming te freegjen, of foar basisfragen. Daden sprekke lûder as wurden! Alle ynformaasje is der, dus gean gewoan troch mei it opsetten fan jo spegel. Fiel jo frij om tickets of merge requests te pleatsen op ús Gitlab as jo problemen tsjinkomme. Wy moatte miskien wat spegel-spesifike funksjes mei jo bouwe, lykas it rebranden fan “Anna’s Archive” nei de namme fan jo webside, (yn earste ynstânsje) it útskeakeljen fan brûkersakkounts, of it keppeljen werom nei ús haadside fan boekesiden. As jo jo spegel rinne hawwe, nim dan asjebleaft kontakt mei ús op. Wy soene graach jo OpSec besjen, en as dat solid is, sille wy nei jo spegel keppelje, en tichter mei jo gearwurkje. Tank yn it foar oan elkenien dy't op dizze manier bydrage wol! It is net foar de swakke fan hert, mar it soe de duorsumens fan de grutste echt iepen bibleteek yn de minsklike skiednis fersterkje. Begjinne Om de wjerstân fan Anna’s Argyf te ferheegjen, sykje wy frijwilligers om spegels te draaien. Jo ferzje is dúdlik ûnderskieden as in spegel, bygelyks “Bob’s Archive, in Anna’s Archive spegel”. Jo binne ree om de risiko's te nimmen dy't ferbûn binne mei dit wurk, dy't signifikant binne. Jo hawwe in djip begryp fan de operasjonele feiligens dy't nedich is. De ynhâld fan <a %(a_shadow)s>dizze</a> <a %(a_pirate)s>berjochten</a> is foar jo fanselssprekkend. Yn it begjin sille wy jo gjin tagong jaan ta ús partner server downloads, mar as dingen goed geane, kinne wy dat mei jo diele. Jo draaie de iepen boarne koadebasis fan Anna’s Archive, en jo bywurkje sawol de koade as de gegevens regelmjittich. Jo binne ree om by te dragen oan ús <a %(a_codebase)s>koadebasis</a> — yn gearwurking mei ús team — om dit te realisearjen. Wy sykje dit: Spegels: oprop foar frijwilligers Doch in oare donaasje. Noch gjin donaasje. <a %(a_donate)s>Doch myn earste donaasje.</a> Donaasjedetails wurde net iepenbier toand. Myn donaasjes 📡 Foar bulk spegeljen fan ús kolleksje, sjoch de <a %(a_datasets)s>Datasets</a> en <a %(a_torrents)s>Torrents</a> siden. Downloads fan jo IP-adres yn de lêste 24 oeren: %(count)s. 🚀 Om flugger te downloaden en de browserkontrôles oer te slaan, <a %(a_membership)s>wur in lid</a>. Download fan partnerwebside Fiel jo frij om fierder te blêdzjen yn Anna’s Argyf yn in oar ljepblêd wylst jo wachtsje (as jo browser it ferfarskjen fan eftergrûnljeppen stipet). Fiel jo frij om te wachtsjen oant meardere download siden tagelyk lade (mar download asjebleaft mar ien bestân tagelyk per server). Sadree't jo in downloadlink krije, is it jildich foar ferskate oeren. Tank foar it wachtsjen, dit hâldt de webside fergees tagonklik foar elkenien! 😊 🔗 Alle downloadlinks foar dit bestân: <a %(a_main)s>Haadside fan it bestân</a>. ❌ Stadige downloads binne net beskikber fia Cloudflare VPN's of oars fan Cloudflare IP-adressen. ❌ Trage downloads binne allinnich beskikber fia de offisjele webside. Besykje %(websites)s. 📚 Brûk de folgjende URL om te downloaden: <a %(a_download)s>Download no</a>. Om elkenien de kâns te jaan om bestannen fergees te downloaden, moatte jo wachtsje foardat jo dit bestân kinne downloade. Wachtsje asjebleaft <span %(span_countdown)s>%(wait_seconds)s</span> sekonden om dit bestân te downloaden. Warskôging: der binne in protte downloads fan jo IP-adres yn de lêste 24 oeren. Downloads kinne stadiger wêze as gewoanlik. As jo in VPN brûke, in dield ynternetferbining, of jo ISP dielt IP's, kin dizze warskôging dêrtroch komme. Bewarje ❌ Der is wat misgien. Besyk it noch ris. ✅ Bewarre. Fernij de side. Feroarje jo werjefte namme. Jo identifikaasje (it diel nei “#”) kin net feroare wurde. Profyl oanmakke <span %(span_time)s>%(time)s</span> bewurkje Listen Meitsje in nije list troch in bestân te finen en it ljepblêd “Listen” te iepenjen. Noch gjin listen Profyl net fûn. Profyl Op dit stuit kinne wy gjin boekoanfragen ferwurkje. Stjoer ús gjin boekoanfragen fia e-mail. Meitsje jo oanfragen asjebleaft op Z-Library of Libgen forums. Opnimme yn Anna’s Argyf DOI: %(doi)s Download SciDB Nexus/STC Gjin foarbyld beskikber. Download it bestân fan <a %(a_path)s>Anna’s Argyf</a>. Om de tagonklikens en langduorjende behâld fan minsklike kennis te stypjen, wurde in <a %(a_donate)s>lid</a>. As in bonus, 🧬&nbsp;SciDB ladet rapper foar leden, sûnder limiten. Net wurkje? Besykje <a %(a_refresh)s>te ferfarskjen</a>. Sci-Hub Foegje spesifyk sykfjild ta Sykje beskriuwings en metadata opmerkingen Jier publisearre Avansearre Tagong Ynhâld Display List Tabel Bestânstype Taal Sortearje op Grutste Meast relevant Nijste (bestânsgrutte) (iepen boarne) (publikaasjejier) Aldste Willekeurich Lytste Boarne skraapt en iepen boarne makke troch AA Digitale Liening (%(count)s) Tydskriftartikels (%(count)s) Wy hawwe oerienkomsten fûn yn: %(in)s. Jo kinne ferwize nei de URL dy't dêr fûn is as jo in bestân <a %(a_request)s>oanfreegje</a>. Metadata (%(count)s) Om de sykindex te ferkennen troch koades, brûk de <a %(a_href)s>Codes Explorer</a>. De sykyndeks wurdt moannebliks bywurke. It omfiemet op it stuit ynfieringen oant %(last_data_refresh_date)s. Foar mear technyske ynformaasje, sjoch de %(link_open_tag)sdatasets side</a>. Útslute Allinnich opnimme Net kontrolearre mear… Folgjende … Foarige Dizze sykindeks omfiemet op it stuit metadata fan de Controlled Digital Lending-biblioteek fan de Internet Archive. <a %(a_datasets)s>Mear oer ús datasets</a>. Foar mear digitale lienbiblioteken, sjoch <a %(a_wikipedia)s>Wikipedia</a> en de <a %(a_mobileread)s>MobileRead Wiki</a>. Foar DMCA / copyright claims <a %(a_copyright)s>klik hjir</a>. Downloadtiid Flater by sykjen. Besykje <a %(a_reload)s>de side opnij te laden</a>. As it probleem oanhâldt, stjoer ús dan in e-mail op %(email)s. Flugge download Yn feite kin elkenien helpe dizze triemmen te bewarjen troch ús <a %(a_torrents)s>ienfâldige list fan torrents</a> te seedjen. ➡️ Soms bart dit ferkeard as de sykserver traach is. Yn sokke gefallen kin <a %(a_attrs)s>opnij lade</a> helpe. ❌ Dit bestân kin problemen hawwe. Op syk nei papieren? Dizze sykindex omfiemet op it stuit metadata fan ferskate metadata-boarnen. <a %(a_datasets)s>Mear oer ús datasets</a>. Der binne in protte, protte boarnen fan metadata foar skreaune wurken oer de hiele wrâld. <a %(a_wikipedia)s>Dizze Wikipediaside</a> is in goed begjin, mar as jo oare goede listen witte, lit it ús dan witte. Foar metadata litte wy de orizjinele records sjen. Wy dogge gjin gearfoeging fan records. Wy hawwe op it stuit de meast wiidweidige iepen katalogus fan boeken, papieren en oare skreaune wurken yn 'e wrâld. Wy spegelje Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>en mear</a>. <span class="font-bold">Gjin bestannen fûn.</span> Besykje minder of oare sykterms en filters. Resultaten %(from)s-%(to)s (%(total)s totaal) As jo oare “skaadbiblioteken” fine dy't wy spegelje moatte, of as jo fragen hawwe, nim dan kontakt mei ús op by %(email)s. %(num)d dielde oerienkomsten %(num)d+ dielde treffers Typ yn it fakje om te sykjen nei bestannen yn digitale lienbiblioteken. Typ yn it fakje om ús katalogus fan %(count)s direkt te downloaden bestannen te sykjen, dy't wy <a %(a_preserve)s>foar ivich bewarje</a>. Typ yn it fakje om te sykjen. Typ yn it fak om ús katalogus fan %(count)s akademyske papieren en tydskriftartikels te sykjen, dy't wy <a %(a_preserve)s>foar ivich bewarje</a>. Typ yn it fak om te sykjen nei metadata út bibleteken. Dit kin nuttich wêze by <a %(a_request)s>it oanfreegjen fan in bestân</a>. Tip: brûk toetseboerdfluchtoetsen “/” (syk fokus), “enter” (sykje), “j” (omheech), “k” (omleech), “<” (foarige side), “>” (folgjende side) foar fluggere navigaasje. Dit binne metadata-records, <span %(classname)s>net</span> downloadbere triemmen. Sykynstellingen Sykje Digitale Liening Download Tydskriftartikels Metadata Nije syktocht %(search_input)s - Sykje De syktocht duorre te lang, wat betsjut dat jo miskien ûnkrekte resultaten sjogge. Soms helpt it <a %(a_reload)s>opnij laden</a> fan de side. De syktocht duorre te lang, wat gewoan is foar brede fragen. De filtertellingen kinne net akkuraat wêze. Foar grutte uploads (mear as 10.000 bestannen) dy't net akseptearre wurde troch Libgen of Z-Library, nim dan kontakt mei ús op fia %(a_email)s. Foar Libgen.li, soargje derfoar dat jo earst ynlogge op <a %(a_forum)s >harren forum</a> mei brûkersnamme %(username)s en wachtwurd %(password)s, en gean dan werom nei harren <a %(a_upload_page)s >uploadside</a>. Foar no riede wy oan om nije boeken te uploaden nei de Library Genesis forks. Hjir is in <a %(a_guide)s>hânige gids</a>. Merk op dat beide forks dy't wy op dizze webside yndeksearje, út ditselde uploadsysteem lûke. Foar lytse uploads (oant 10.000 bestannen) upload se asjebleaft nei sawol %(first)s as %(second)s. As alternatyf kinne jo se uploade nei Z-Library <a %(a_upload)s>hjir</a>. Om akademyske papieren te uploaden, upload se ek (njonken Library Genesis) nei <a %(a_stc_nexus)s>STC Nexus</a>. Sy binne de bêste skaadbiblioteek foar nije papieren. Wy hawwe se noch net yntegrearre, mar dat sille wy op in stuit dwaan. Jo kinne har <a %(a_telegram)s>upload bot op Telegram</a> brûke, of kontakt opnimme mei it adres dat yn harren fêstpinde berjocht stiet as jo te folle bestannen hawwe om op dizze manier te uploaden. <span %(label)s>Swierich frijwilligerswurk (USD$50-USD$5,000 beleannings):</span> as jo in soad tiid en/of middels oan ús misje wije kinne, wurkje wy graach tichter by jo. Uteinlik kinne jo diel útmeitsje fan it ynterne team. Hoewol't wy in strak budzjet hawwe, kinne wy <span %(bold)s>💰 monetêre beleannings</span> jaan foar it yntinsyfste wurk. <span %(label)s>Ljocht frijwilligerswurk:</span> as jo mar in pear oeren hjir en dêr frijmeitsje kinne, binne der noch in protte manieren wêrop jo helpe kinne. Wy beleanje konsistente frijwilligers mei <span %(bold)s>🤝 lidmaatskippen fan Anna’s Archive</span>. Anna’s Archive is ôfhinklik fan frijwilligers lykas jo. Wy ferwolkomje alle ynsetnivo's, en hawwe twa haadkategoryen fan help dêr't wy nei sykje: As jo net yn steat binne om jo tiid frijwillich te jaan, kinne jo ús noch hieltyd in soad helpe troch <a %(a_donate)s>jild te donearjen</a>, <a %(a_torrents)s>ús torrents te seedjen</a>, <a %(a_uploading)s>boeken te uploaden</a>, of <a %(a_help)s>jo freonen te fertellen oer Anna’s Archive</a>. <span %(bold)s>Bedriuwen:</span> wy biede hege-snelheid direkte tagong ta ús kolleksjes yn ruil foar in donaasje op bedriuwsnivo of yn ruil foar nije kolleksjes (bgl. nije scans, OCR’de datasets, it ferbetterjen fan ús gegevens). <a %(a_contact)s>Kontakt mei ús opnimme</a> as dit jo is. Sjoch ek ús <a %(a_llm)s>LLM-side</a>. Belonings Wy sykje altyd nei minsken mei solide programmeer- of offinsive feiligensfeardigens om belutsen te reitsjen. Jo kinne in serieuze bydrage leverje oan it bewarjen fan it erfskip fan de minskheid. As tankje jouwe wy lidmaatskip foar solide bydragen. As in grutte tankje jouwe wy monetêre belonings foar benammen wichtige en drege taken. Dit moat net sjoen wurde as in ferfanging foar in baan, mar it is in ekstra stimulâns en kin helpe mei makke kosten. Measte fan ús koade is iepen boarne, en wy freegje dat ek fan jo koade as wy de beloning útrikke. Der binne wat útsûnderingen dy't wy op yndividuele basis beprate kinne. Belonings wurde útrikt oan de earste persoan dy't in taak foltôget. Fiel jo frij om in reaksje te jaan op in beloningsticket om oaren te litten witte dat jo oan wat wurkje, sadat oaren wachtsje kinne of kontakt mei jo opnimme kinne om gear te wurkjen. Mar wês bewust dat oaren ek frij binne om der oan te wurkjen en besykje jo foar te wêzen. Wy rikke lykwols gjin belonings út foar slordich wurk. As twa heechweardige ynstjoeringen tichtby elkoar makke wurde (binnen in dei of twa), kinne wy kieze om belonings oan beide te jaan, nei ús ynsjoch, bygelyks 100%% foar de earste ynstjoering en 50%% foar de twadde ynstjoering (dus 150%% yn totaal). Foar de gruttere belonings (benammen scraping belonings), nim asjebleaft kontakt mei ús op as jo ~5%% derfan foltôge hawwe, en jo der wis fan binne dat jo metoade skale sil nei de folsleine mylpeal. Jo sille jo metoade mei ús diele moatte sadat wy feedback jaan kinne. Ek kinne wy op dizze manier beslute wat te dwaan as der meardere minsken tichtby in beloning komme, lykas it mooglik útrikken oan meardere minsken, minsken oanmoedigje om gear te wurkjen, ensfh. WARSKÔGING: de hege-beloningstaken binne <span %(bold)s>dreech</span> — it kin wiis wêze om mei maklikere te begjinnen. Gean nei ús <a %(a_gitlab)s>Gitlab-issueslist</a> en sortearje op “Label priority”. Dit toant rûchwei de folchoarder fan taken dy't wy wichtich fine. Taken sûnder eksplisite belonings binne noch altyd yn oanmerking foar lidmaatskip, benammen dy markearre as “Accepted” en “Anna’s favorite”. Jo kinne begjinne mei in “Starter project”. Licht frijwilligerswurk Wy hawwe no ek in syngronisearre Matrix-kanaal by %(matrix)s. As jo in pear oeren oer hawwe, kinne jo op ferskate manieren helpe. Soargje derfoar dat jo meidwaan oan de <a %(a_telegram)s>frijwilligerschat op Telegram</a>. As teken fan wurdearring jouwe wy typysk 6 moannen fan “Lokkige Bibliotekaris” foar basis mylpeallen, en mear foar trochgeand frijwilligerswurk. Alle mylpeallen fereaskje wurk fan hege kwaliteit — slordich wurk docht ús mear kwea as goed en wy sille it ôfwiize. Stjoer ús asjebleaft in <a %(a_contact)s>e-mail</a> as jo in mylpeal berikke. %(links)s keppelings of skermôfbyldings fan fersiken dy't jo folbrocht hawwe. Boek (of papier, ensfh.) oanfragen op de Z-Library of de Library Genesis forums ferfolje. Wy hawwe ús eigen boekoanfragesysteem net, mar wy spegelje dy biblioteken, dus it better meitsjen fan dy makket Anna’s Archive ek better. Mylpeal Taak Ofhinklik fan de taak. Lytse taken pleatst yn ús <a %(a_telegram)s>frijwilligerschat op Telegram</a>. Meastentiids foar lidmaatskip, soms foar lytse beleannings. Lytse taken pleatst yn ús frijwilligerspeteargroep. Soargje derfoar dat jo in reaksje efterlitte op problemen dy't jo oplosse, sadat oaren jo wurk net dûbelje. %(links)s keppelings fan records dy't jo ferbettere hawwe. Jo kinne de <a %(a_list)s >list fan willekeurige metadata-problemen</a> brûke as in startpunt. Ferbetterje metadata troch te <a %(a_metadata)s>linken</a> mei Open Library. Dizze moatte sjen litte dat jo immen ynformearje oer Anna’s Argiven, en dat se jo tankje. %(links)s keppelings of skermôfbyldings. It wurd fan Anna’s Argiven ferspriede. Bygelyks troch boeken oan te rieden op AA, te keppeljen nei ús blogberjochten, of algemien minsken nei ús webside te lieden. Folje in taal folslein oer (as it net al hast klear wie). <a %(a_translate)s>Oersette</a> de webside. Keppeling nei bewurkingsskiednis dy't sjen lit dat jo wichtige bydragen levere hawwe. Ferbetterje de Wikipediaside foar Anna’s Archive yn jo taal. Omfetsje ynformaasje fan AA’s Wikipediaside yn oare talen, en fan ús webside en blog. Foegje ferwizings ta nei AA op oare relevante siden. Frijwilligerswurk & Bounties 