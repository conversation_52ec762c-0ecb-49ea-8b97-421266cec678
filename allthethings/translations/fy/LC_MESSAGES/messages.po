#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Ongeldige oanfraach. Besykje %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " en "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "en mear"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;<PERSON><PERSON> s<PERSON> %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Wy scrape en iepenboarne %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Al ús koade en gegevens binne folslein iepen boarne."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;De grutste echt iepen bibleteek yn de skiednis fan de minskheid."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;boeken, %(paper_count)s&nbsp;papieren — foar ivich bewarre."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;De grutste iepen-boarne iepen-data bibleteek fan de wrâld. ⭐️&nbsp;Spegelt Sci-Hub, Library Genesis, Z-Library, en mear. 📈&nbsp;%(book_any)s boeken, %(journal_article)s papieren, %(book_comic)s strips, %(magazine)s tydskriften — foar ivich bewarre."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 De grutste iepen boarne iepen data bibleteek fan 'e wrâld.<br>⭐️ Spegelt Scihub, Libgen, Zlib, en mear."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Ferkearde metadata (bgl. titel, beskriuwing, omslachôfbylding)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Downloadproblemen (bgl. kin net ferbine, foutberjocht, hiel stadich)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Bestân kin net iepene wurde (bgl. beskeadige bestân, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Minne kwaliteit (bgl. opmaakproblemen, minne scan kwaliteit, ûntbrekkende siden)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / bestân moat fuorthelle wurde (bgl. reklame, misledigjende ynhâld)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Copyright-oanspraak"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Oar"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonusdownloads"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Briljante Boekewjirm"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Lokkige Bibliotekaris"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Flinke Ferzamelaar"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Fantastyske Fersammeler"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s totaal"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totaal"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "ûnbeleanne"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "betelle"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "annulearre"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "ferfallen"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "wachtet op befestiging fan Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "ûnweardich"

#, fuzzy
msgid "page.donate.title"
msgstr "Donearje"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Jo hawwe in <a %(a_donation)s>besteande donaasje</a> yn proses. Foltôgje of annulearje dy donaasje foardat jo in nije donaasje meitsje."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Besjoch al myn donaasje</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Anna’s Archive is in non-profit, iepen-boarne, iepen-data projekt. Troch te donearjen en lid te wurden, stypje jo ús operaasjes en ûntwikkeling. Oan al ús leden: tank foar it hâlden fan ús geande! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Foar mear ynformaasje, sjoch de <a %(a_donate)s>FAQ oer Donaasjes</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Om noch mear downloads te krijen, <a %(a_refer)s>ferwize jo freonen</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Jo krije %(percentage)s%% bonus snelle downloads, om't jo ferwiisd binne troch brûker %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Dit jildt foar de hiele lidmaatskipsperioade."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s snelle downloads per dei"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "as jo dizze moanne donearrest!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / moanne"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Meidwaan"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Selektearre"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "oant %(percentage)s%% koartingen"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB papieren <strong>ûnbeheind</strong> sûnder ferifikaasje"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> tagong"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Fertsjinje <strong>%(percentage)s%% bonus downloads</strong> troch <a %(a_refer)s>freonen troch te ferwizen</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Jo brûkersnamme of anonyme fermelding yn de credits"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Eardere foardielen, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Iere tagong ta nije funksjes"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Eksklusive Telegram mei efter-de-skermen updates"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Adoptearje in torrent”: jo brûkersnamme of berjocht yn in torrent-bestânsnamme <div %(div_months)s>ien kear elke 12 moannen fan lidmaatskip</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legindaryske status yn it behâld fan 'e kennis en kultuer fan 'e minskheid"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Ekspert Tagong"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "nim kontakt mei ús op"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Wy binne in lyts team fan frijwilligers. It kin ús 1-2 wiken duorje om te reagearjen."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Unbeheind</strong> hege-snelheid tagong"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direkte <strong>SFTP</strong> servers"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Donaasjes op bedriuwsnivo of útwikseling foar nije kolleksjes (bgl. nije scans, OCR’de datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Wy ferwolkomje grutte donaasjes fan rike yndividuen of ynstellingen. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Foar donaasjes boppe $5000 nim asjebleaft direkt kontakt mei ús op by %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Wês derop alert dat hoewol't de lidmaatskippen op dizze side \"per moanne\" binne, it ienmalige donaasjes binne (net weromkommend). Sjoch de <a %(faq)s>FAQ oer donaasje</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "As jo in donaasje (elk bedrach) sûnder lidmaatskip meitsje wolle, brûk dan dizze Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Kies in betellingsmetoade."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(tydlik net beskikber)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s kadokaart"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkaart (mei app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kredyt-/debitkaart"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (regulier)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kaart / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredyt/debit/Apple/Google (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazylje)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkaart"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kredyt-/debitkaart (backup)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kredyt-/debitkaart 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Mei crypto kinne jo donearje mei BTC, ETH, XMR, en SOL. Brûk dizze opsje as jo al bekend binne mei cryptocurrency."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Mei crypto kinne jo donearje mei BTC, ETH, XMR, en mear."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "As jo foar it earst krypto brûke, riede wy oan om %(options)s te brûken om Bitcoin te keapjen en te donearjen (de orizjinele en meast brûkte kryptomunt)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Om te donearjen mei PayPal US, sille wy PayPal Crypto brûke, wat ús anonym bliuwt. Wy wurdearje it dat jo de tiid nimme om te learen hoe't jo mei dizze metoade donearje kinne, om't it ús in soad helpt."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Donearje mei PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Donearje mei Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "As jo Cash App hawwe, is dit de maklikste manier om te donearjen!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Merk op dat foar transaksjes ûnder %(amount)s, Cash App in %(fee)s fergoeding yn rekken bringe kin. Foar %(amount)s of mear, is it fergees!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Donearje mei Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "As jo Revolut hawwe, is dit de maklikste manier om te donearjen!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Donearje mei in kredyt- of pinpas."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay en Apple Pay kinne ek wurkje."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Tink derom dat foar lytse donaasjes de kredytkaartkosten ús %(discount)s%% koarting kinne eliminearje, dus wy riede langere abonneminten oan."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Tink derom dat foar lytse donaasjes de fergoedingen heech binne, dus wy riede langere abonneminten oan."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Mei Binance keapje jo Bitcoin mei in kredyt-/debitkaart of bankrekken, en donearje dan dy Bitcoin oan ús. Sa kinne wy feilich en anonym bliuwe by it akseptearjen fan jo donaasje."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance is beskikber yn hast elk lân, en stipet de measte banken en kredyt-/debitkaarten. Dit is op it stuit ús haad oanbefelling. Wy wurdearje it dat jo de tiid nimme om te learen hoe't jo mei dizze metoade donearje kinne, om't it ús in soad helpt."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Donearje mei jo reguliere PayPal-akkount."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Donearje mei kredyt-/debitkaart, PayPal, of Venmo. Jo kinne tusken dizze kieze op de folgjende side."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Donearje mei in Amazon gift card."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Tink derom dat wy moatte ôfroune nei bedragen dy't akseptearre wurde troch ús ferkeapers (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>IMPORTANT:</strong> Wy stypje allinnich Amazon.com, net oare Amazon-websiden. Bygelyks, .de, .co.uk, .ca, wurde NET stipe."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>BELANGRIJK:</strong> Dizze opsje is foar %(amazon)s. As jo in oare Amazon-webside brûke wolle, selektearje it dan hjirboppe."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Dizze metoade brûkt in cryptocurrency provider as in tuskenlizzende konverzje. Dit kin wat betiizjend wêze, dus brûk dizze metoade allinnich as oare betelmethoden net wurkje. It wurket ek net yn alle lannen."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Donearje mei in kredyt-/debitkaart, fia de Alipay-app (super maklik om yn te stellen)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Ynstallearje Alipay-app"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Ynstallearje de Alipay-app fan de <a %(a_app_store)s>Apple App Store</a> of <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registrearje mei jo telefoannûmer."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Gjin fierdere persoanlike details binne nedich."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Foegje bankkaart ta"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Stipe: Visa, MasterCard, JCB, Diners Club en Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Sjoch <a %(a_alipay)s>dizze gids</a> foar mear ynformaasje."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Wy kinne kredyt-/debitkaarten net direkt stypje, om't banken net mei ús wurkje wolle. ☹ Lykwols, der binne ferskate manieren om kredyt-/debitkaarten dochs te brûken, mei oare betelmethoden:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Gift Card"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Stjoer ús Amazon.com-cadeaukaarten mei jo kredyt-/debitkaart."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay stipet ynternasjonale kredyt-/debitkaarten. Sjoch <a %(a_alipay)s>dizze gids</a> foar mear ynformaasje."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) stipet ynternasjonale kredyt-/debitkaarten. Yn de WeChat-app, gean nei “Me => Services => Wallet => Add a Card”. As jo dat net sjogge, skeakelje it yn mei “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Jo kinne crypto keapje mei kredyt-/debitkaarten."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Crypto express tsjinsten"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Express tsjinsten binne handich, mar rekkenje hegere kosten."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Jo kinne dit brûke ynstee fan in crypto-útwikseling as jo fluch in gruttere donaasje meitsje wolle en gjin probleem hawwe mei in fergoeding fan $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Soargje derfoar dat jo it krekte crypto-bedrach stjoere dat op de donaasje-side stiet, net it bedrach yn $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Oars wurdt de fergoeding subtrakt en kinne wy jo lidmaatskip net automatysk ferwurkje."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s ôfhinklik fan lân, gjin ferifikaasje foar earste transaksje)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, gjin ferifikaasje foar earste transaksje)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, gjin ferifikaasje foar earste transaksje)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "As dizze ynformaasje ferâldere is, stjoer ús dan in e-mail om it ús witte te litten."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Foar kredytkaarten, pinpassen, Apple Pay en Google Pay brûke wy \"Buy Me a Coffee\" (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Yn harren systeem is ien \"kofje\" gelyk oan $5, dus jo donaasje wurdt ôfrûne nei it tichtste mearfâld fan 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Selektearje hoelang't jo abonnearje wolle."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 moanne"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 moannen"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 moannen"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 moannen"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 moannen"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 moannen"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 moannen"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>nei <span %(span_discount)s></span> koartingen</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Dizze betelmetoade fereasket in minimum fan %(amount)s. Selektearje in oare doer of betelmetoade."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Donearje"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Dizze betelmetoade lit allinich in maksimum fan %(amount)s ta. Kies in oare doer of betelmetoade."

#, fuzzy
msgid "page.donate.login2"
msgstr "Om lid te wurden, <a %(a_login)s>Log yn of Registrearje</a> asjebleaft. Tank foar jo stipe!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Selektearje jo foarkar crypto munt:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(legste minimale bedrach)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(brûk by it ferstjoeren fan Ethereum fan Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(warskôging: heech minimum bedrach)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Klik op de donearknop om dizze donaasje te befestigjen."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Donearje <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Jo kinne de donaasje noch annulearje by it ôfrekkenjen."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Trochferwizing nei de donaasjepagina…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Der gie wat mis. Fernij de side en besykje it opnij."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / moanne"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "foar 1 moanne"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "foar 3 moannen"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "foar 6 moannen"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "foar 12 moannen"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "foar 24 moannen"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "foar 48 moannen"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "foar 96 moannen"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "foar 1 moanne “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "foar 3 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "foar 6 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "foar 12 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "foar 24 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "foar 48 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "foar 96 moannen “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donearje"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moanne foar %(duration)s moannen, ynklusyf %(discounts)s%% koarting)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / moanne foar %(duration)s moannen)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifier: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Annulearje"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Binne jo wis dat jo wolle annulearje? Net annulearje as jo al betelle hawwe."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, annulearje asjebleaft"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Jo donaasje is annulearre."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Meitsje in nije donaasje"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Der gie wat mis. Laad de side opnij en besykje it nochris."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Weromsette"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Jo hawwe al betelle. As jo de betellingsynstruksjes dochs besjen wolle, klik hjir:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Toan âlde betellingsynstruksjes"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Tige tank foar jo donaasje!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "As jo it noch net dien hawwe, skriuw jo geheime kaai foar it ynloggen op:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Oars kinne jo út dit akkount sluten wurde!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "De betellingsynstruksjes binne no ferâldere. As jo in oare donaasje dwaan wolle, brûk de “Opnij bestelle” knop hjirboppe."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Belangrike opmerking:</strong> Crypto-prizen kinne wyld fluktuearje, soms sels sa folle as 20%% yn in pear minuten. Dit is noch altyd minder as de fergoedingen dy't wy hawwe mei in protte betellingsproviders, dy't faak 50-60%% rekkenje foar it wurkjen mei in \"skaadgoedei\" lykas ús. <u>As jo ús it ûntfangstbewiis stjoere mei de oarspronklike priis dy't jo betelle hawwe, sille wy jo akkount noch kreditearje foar it keazen lidmaatskip</u> (safolle't it ûntfangstbewiis net âlder is as in pear oeren). Wy wurdearje it echt dat jo ree binne om mei sokke dingen om te gean om ús te stypjen! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Dizze donaasje is ferrûn. Annulearje en meitsje in nije."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Crypto-ynstruksjes"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Oermeitsje nei ien fan ús crypto-akkounts"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Donearje it totale bedrach fan %(total)s oan ien fan dizze adressen:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Keapje Bitcoin op Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Fyn de “Crypto”-side yn jo PayPal-app of webside. Dit is typysk ûnder “Finânsjes”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Folchje de ynstruksjes om Bitcoin (BTC) te keapjen. Jo moatte allinich it bedrach keapje dat jo donearje wolle, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Transfer de Bitcoin nei ús adres"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gean nei de “Bitcoin” side yn jo PayPal app of webside. Druk op de “Oermeitsje” knop %(transfer_icon)s, en dan “Stjoere”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Fier ús Bitcoin (BTC) adres yn as de ûntfanger, en folgje de ynstruksjes om jo donaasje fan %(total)s te stjoeren:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Ynliedings foar kredyt- / pinpas"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Donearje fia ús kredyt- / pinpas-side"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Donearje %(amount)s op <a %(a_page)s>dizze side</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Sjoch de stap-foar-stap gids hjirûnder."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Wachtsje op befêstiging (fernij de side om te kontrolearjen)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Wachtsje op oerdracht (ferfarsje de side om te kontrolearjen)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Oerbleaune tiid:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(jo wolle miskien annulearje en in nije donaasje meitsje)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Om de timer te resetten, meitsje gewoan in nije donaasje."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Status bywurkje"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "As jo problemen tsjinkomme, nim dan kontakt mei ús op fia %(email)s en jou safolle mooglik ynformaasje (lykas skermôfbyldings)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "As jo al betelle hawwe:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Soms kin it oant 24 oeren duorje foar't de befêstiging komt, soargje derfoar dat jo dizze side ferfarskje (sels as it ferrûn is)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Keapje PYUSD-munt op PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Folgjende de ynstruksjes om PYUSD-munt (PayPal USD) te keapjen."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Keapje in bytsje mear (wy riede oan %(more)s mear) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Gean nei de \"PYUSD\" side yn jo PayPal app of webside. Druk op de \"Oermeitsje\" knop %(icon)s, en dan \"Stjoere\"."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Ferfier %(amount)s nei %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Keap Bitcoin (BTC) op Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gean nei de “Bitcoin” (BTC) side yn Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Keap in bytsje mear (wy riede %(more)s mear oan) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Ferfier de Bitcoin nei ús adres"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik op de “Stjoer bitcoin” knop om in “útbetelling” te meitsjen. Skeakelje fan dollars nei BTC troch op it %(icon)s ikoan te drukken. Fier it BTC-bedrach hjirûnder yn en klik op “Stjoer”. Sjoch <a %(help_video)s>dit fideo</a> as jo fêst sitte."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Foar lytse donaasjes (ûnder $25), kinne jo Rush of Priority brûke moatte."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Keap Bitcoin (BTC) op Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Gean nei de “Crypto” side yn Revolut om Bitcoin (BTC) te keapjen."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Keap in bytsje mear (wy riede %(more)s mear oan) dan it bedrach dat jo donearje (%(amount)s), om transaksjekosten te dekken. Jo hâlde alles oer wat oerbliuwt."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Ferfier de Bitcoin nei ús adres"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klik op de “Stjoer bitcoin” knop om in “útbetelling” te meitsjen. Skeakelje fan euro's nei BTC troch op it %(icon)s ikoan te drukken. Fier it BTC-bedrach hjirûnder yn en klik op “Stjoer”. Sjoch <a %(help_video)s>dit fideo</a> as jo fêst sitte."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Soargje derfoar dat jo it BTC-bedrach hjirûnder brûke, <em>NET</em> euro's of dollars, oars ûntfange wy it korrekte bedrach net en kinne wy jo lidmaatskip net automatysk befestigje."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Foar lytse donaasjes (ûnder $25) kinne jo Rush of Priority brûke moatte."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Brûk ien fan de folgjende “kredytkaart nei Bitcoin” ekspresstsjinsten, dy't mar in pear minuten duorje:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Folje de folgjende details yn it formulier yn:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin bedrach:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Brûk dit <span %(underline)s>krekte bedrach</span>. Jo totale kosten kinne heger wêze fanwegen kredytkaartkosten. By lytse bedragen kin dit spitigernôch mear wêze as ús koarting."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adres (eksterne portemonnee):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s ynstruksjes"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Wy stypje allinich de standertferzje fan kryptomunten, gjin eksoatyske netwurken of ferzjes fan munten. It kin oant in oere duorje om de transaksje te befestigjen, ôfhinklik fan de munt."

msgid "page.donation.crypto_qr_code_title"
msgstr "Scan QR-koade om te beteljen"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Scan dizze QR-koade mei jo Crypto Wallet-app om de betellingsdetails fluch te foljen"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon kadokaart"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Brûk asjebleaft it <a %(a_form)s>offisjele Amazon.com-formulier</a> om ús in kadokaart fan %(amount)s te stjoeren nei it e-mailadres hjirûnder."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Wy kinne gjin oare metoaden fan kadokaarten akseptearje, <strong>allinne direkt stjoerd fan it offisjele formulier op Amazon.com</strong>. Wy kinne jo kadokaart net werombringe as jo dit formulier net brûke."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Fier it krekte bedrach yn: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Skriuw asjebleaft NET jo eigen berjocht."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "\"To\" ûntfanger e-post yn it formulier:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Unyk foar jo akkount, net diele."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Mar ien kear brûke."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Wachtsje op kadokaart… (ferfarsje de side om te kontrolearjen)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Nei it ferstjoeren fan jo kadokaart sil ús automatisearre systeem it binnen in pear minuten befestigje. As dit net wurket, besykje jo kadokaart opnij te ferstjoeren (<a %(a_instr)s>ynstruksjes</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "As dat noch net wurket, stjoer ús dan in e-mail en Anna sil it hânmjittich besjen (dit kin in pear dagen duorje), en soargje derfoar dat jo neame as jo al besocht hawwe it opnij te stjoeren."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Foarbyld:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Merk op dat de akkountnamme of ôfbylding frjemd útsjen kin. Gjin soargen! Dizze akkounts wurde beheard troch ús donearpartners. Us akkounts binne net hackt."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay ynstruksjes"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Donearje op Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit Alipay-akkount</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "As de donearside blokkearre wurdt, besykje in oare ynternetferbining (bygelyks VPN of telefoanynternet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Spitigernôch is de Alipay-side faak allinnich tagonklik fanôf <strong>fêstelân Sina</strong>. Jo moatte miskien jo VPN tydlik útskeakelje, of in VPN brûke nei fêstelân Sina (of Hong Kong wurket soms ek)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Doch in donaasje (scan de QR-koade of druk op de knop)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Iepenje de <a %(a_href)s>QR-koade donaasje side</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Scan de QR-koade mei de Alipay-app, of druk op de knop om de Alipay-app te iepenjen."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Haw geduld; de side kin in skoftke duorje om te laden om't it yn Sina is."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat-ynstruksjes"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Donearje op WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit WeChat-akkount</a>"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix ynstruksjes"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Donearje op Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Donearje it totale bedrach fan %(total)s mei <a %(a_account)s>dit Pix-akkount"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Stjoer ús de kwitânsje per e-mail"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Stjoer in kwitânsje of skermôfbylding nei jo persoanlike ferifikaasjeadres. Brûk dit e-mailadres NET foar jo PayPal-donaasje."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Stjoer in ûntfangst of skermôfbylding nei jo persoanlike ferifikaasje-adres:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "As de crypto-wisselkoers skommele tidens de transaksje, soargje der dan foar dat jo it ûntfangstbewiis opnimme dat de oarspronklike wisselkoers toant. Wy wurdearje it echt dat jo de muoite nimme om crypto te brûken, it helpt ús in protte!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "As jo jo kwitânsje e-maild hawwe, klik dan op dizze knop, sadat Anna it manuell besjen kin (dit kin in pear dagen duorje):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Ja, ik haw myn kwitânsje e-maild"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Tank foar jo donaasje! Anna sil jo lidmaatskip binnen in pear dagen manuell aktivearje."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Der gie wat mis. Ferfarsje de side en besykje it opnij."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Stap-foar-stap gids"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Guon fan 'e stappen neame krypto-wallets, mar meitsje jo gjin soargen, jo hoege hjirfoar neat oer krypto te learen."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Fier jo e-mailadres yn."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Selektearje jo betelmetoade."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Selektearje jo betelmetoade opnij."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Selektearje “Self-hosted” wallet."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Klik op “Ik befêstigje eigendom”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Jo moatte in e-mail kwitânsje ûntfange. Stjoer dat nei ús, en wy befestigje jo donaasje sa gau mooglik."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Wachtsje op syn minst <span %(span_hours)s>24 oeren</span> (en ferfarskje dizze side) foardat jo kontakt mei ús opnimme."

#, fuzzy
msgid "page.donate.mistake"
msgstr "As jo in flater makke hawwe by de betelling, kinne wy gjin refunds dwaan, mar wy sille besykje it goed te meitsjen."

#, fuzzy
msgid "page.my_donations.title"
msgstr "Myn donaasjes"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donaasjedetails wurde net iepenbier toand."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Noch gjin donaasje. <a %(a_donate)s>Doch myn earste donaasje.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Doch in oare donaasje."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Downloadde bestannen"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Downloads fan Fast Partner Servers binne markearre mei %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "As jo in bestân downloade mei sawol snelle as stadige downloads, sil it twa kear ferskine."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Flugge downloads yn de lêste 24 oeren telle mei foar it deistige limyt."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Alle tiden binne yn UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Downloaded bestannen wurde net iepenbier toand."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Noch gjin bestannen downloade."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Lêste 18 oeren"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Earder"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Akkount"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Ynlogge / Registrearje"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Akkount ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Iepenbier profyl: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Geheime kaai (net diele!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "toane"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Lidmaatskip: <strong>%(tier_name)s</strong> oant %(until_date)s <a %(a_extend)s>(ferlingje)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Lidmaatskip: <strong>Gjin</strong> <a %(a_become)s>(wur lid)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Flugge downloads brûkt (lêste 24 oeren): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "Hokker downloads?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Eksklusyf Telegram-groep: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Doch mei ús hjir!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Upgrade nei in <a %(a_tier)s>heger nivo</a> om by ús groep te kommen."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontakt opnimme mei Anna op %(email)s as jo ynteressearre binne om jo lidmaatskip te ferheegjen nei in hegere tier."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontakt email"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Jo kinne meardere lidmaatskippen kombinearje (fluch downloads per 24 oeren wurde byinoar opteld)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Iepenbier profyl"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Downloadde bestannen"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Myn donaasje"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Útlogge"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Jo binne no útlogd. Fernij de side om wer yn te loggen."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Der is wat misgien. Fernij de side en besykje it opnij."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registraasje suksesfol! Jo geheime kaai is: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Bewarje dizze kaai foarsichtich. As jo it ferlieze, ferlieze jo tagong ta jo akkount."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Blêdwizer.</strong> Jo kinne dizze side blêdwize om jo kaai op te heljen.</li><li %(li_item)s><strong>Download.</strong> Klik <a %(a_download)s>op dizze link</a> om jo kaai te downloaden.</li><li %(li_item)s><strong>Wachtwurdbehearder.</strong> Brûk in wachtwurdbehearder om de kaai op te slaan as jo it hjirûnder ynfiere.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Fier jo geheime kaai yn om yn te loggen:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Geheime kaai"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Oanmelde"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ungeldige geheime kaai. Ferifiearje jo kaai en besykje it opnij, of registrearje in nij akkount hjirûnder."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Ferlieze jo kaai net!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Noch gjin akkount?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Nij akkount registrearje"

#, fuzzy
msgid "page.login.lost_key"
msgstr "As jo jo kaai kwyt binne, nim dan asjebleaft <a %(a_contact)s>kontakt mei ús op</a> en jou safolle mooglik ynformaasje."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Jo moatte miskien tydlik in nij akkount oanmeitsje om kontakt mei ús op te nimmen."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Ald e-postbasearre akkount? Fier jo <a %(a_open)s>e-post hjir yn</a>."

#, fuzzy
msgid "page.list.title"
msgstr "List"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "bewurkje"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Bewarje"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Bewarre. Fernij de side."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Der is wat misgien. Besykje it nochris."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "List troch %(by)s, makke <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "List is leech."

#, fuzzy
msgid "page.list.new_item"
msgstr "Foegje ta of ferwiderje fan dizze list troch in bestân te finen en it ljepblêd \"Listen\" te iepenjen."

#, fuzzy
msgid "page.profile.title"
msgstr "Profyl"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profyl net fûn."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "bewurkje"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Feroarje jo werjefte namme. Jo identifikaasje (it diel nei “#”) kin net feroare wurde."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Bewarje"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Bewarre. Fernij de side."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Der is wat misgien. Besyk it noch ris."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profyl oanmakke <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Listen"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Noch gjin listen"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Meitsje in nije list troch in bestân te finen en it ljepblêd “Listen” te iepenjen."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Copyrightreformaasje is nedich foar nasjonale feiligens."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Sineeske LLM's (ynklusyf DeepSeek) wurde traind op myn yllegale argyf fan boeken en papieren — it grutste yn 'e wrâld. It Westen moat it auteursrjocht wetjouwing opnij besjen as in kwestje fan nasjonale feiligens."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "begeliedende artikels troch TorrentFreak: <a %(torrentfreak)s>earste</a>, <a %(torrentfreak_2)s>twadde</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Net sa lang lyn wiene \"skaad-biblioteken\" oan it ferdwinen. Sci-Hub, it grutte yllegale argyf fan akademyske papieren, hie ophâlden mei it opnimmen fan nije wurken fanwegen rjochtsaken. \"Z-Library\", de grutste yllegale biblioteek fan boeken, seach har beskuldige makkers arrestearre op kriminele auteursrjocht oanklachten. Se slaggen der op in ûnfoarstelbere wize yn om har arrestaasje te ûntkommen, mar har biblioteek is net minder yn gefaar."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Doe't Z-Library foar sluting stie, hie ik al har hiele biblioteek bewarre en wie ik op syk nei in platfoarm om it te hûzjen. Dat wie myn motivaasje foar it begjinnen fan Anna's Argyf: in fuortsetting fan de misje efter dy eardere inisjativen. Wy binne sûnt útgroeid ta de grutste skaad-biblioteek yn 'e wrâld, mei mear as 140 miljoen auteursrjochtlik beskerme teksten yn ferskate formaten — boeken, akademyske papieren, tydskriften, kranten, en mear."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Myn team en ik binne ideologen. Wy leauwe dat it bewarjen en hûzjen fan dizze bestannen moreel rjocht is. Biblioteken oer de hiele wrâld sjogge finansieringsbesunigings, en wy kinne it erfgoed fan 'e minskheid ek net fertrouwe oan bedriuwen."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Doe kaam AI. Hast alle grutte bedriuwen dy't LLM's bouwe, hawwe kontakt mei ús opnommen om te trainen op ús gegevens. De measten (mar net allegear!) Amerikaanske bedriuwen hawwe harren beslút heroverwage doe't se de yllegale aard fan ús wurk beseften. Yn tsjinstelling, Sineeske bedriuwen hawwe ús kolleksje entûsjast omearme, blykber net benaud foar de legaliteit. Dit is opmerklik jûn de rol fan Sina as ûndertekenaar fan hast alle grutte ynternasjonale auteursrjochtferdraggen."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Wy hawwe hege-snelheid tagong jûn oan sawat 30 bedriuwen. De measten fan harren binne LLM-bedriuwen, en guon binne gegevenshannelers, dy't ús kolleksje trochferkeapje sille. De measten binne Sineesk, hoewol't wy ek wurke hawwe mei bedriuwen út de FS, Jeropa, Ruslân, Súd-Koreä, en Japan. DeepSeek <a %(arxiv)s>joeche ta</a> dat in eardere ferzje traind waard op in diel fan ús kolleksje, hoewol't se tige geheimhâldend binne oer har lêste model (wêrschynlik ek traind op ús gegevens)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "As it Westen foarop bliuwe wol yn de race fan LLM's, en úteinlik, AGI, moat it syn posysje oer auteursrjocht op 'e nij besjen, en gau. Oft jo it no mei ús iens binne of net oer ús morele saak, dit wurdt no in kwestje fan ekonomy, en sels fan nasjonale feiligens. Alle machtsblokken bouwe keunstmjittige super-wittenskippers, super-hackers, en super-legers. Frijheid fan ynformaasje wurdt in kwestje fan oerlibjen foar dizze lannen — sels in kwestje fan nasjonale feiligens."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Us team komt fan oer de hiele wrâld, en wy hawwe gjin spesifike ôfstimming. Mar wy soene lannen mei sterke auteursrjochtwetten oanmoedigje om dizze besteansbedriging te brûken om se te reformearjen. Wat te dwaan?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Us earste oanbefelling is ienfâldich: ferkoart de auteursrjochttermyn. Yn 'e FS wurdt auteursrjocht ferliend foar 70 jier nei de dea fan 'e auteur. Dit is absurd. Wy kinne dit yn oerienstimming bringe mei patinten, dy't ferliend wurde foar 20 jier nei it yntsjinjen. Dit moat mear as genôch tiid wêze foar auteurs fan boeken, papieren, muzyk, keunst, en oare kreative wurken, om folslein kompensearre te wurden foar harren ynspanningen (ynklusyf langere-termyn projekten lykas filmadaptaasjes)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Dan, op syn minst, moatte beliedsmakkers útsûnderings opnimme foar de massa-preservaasje en fersprieding fan teksten. As ferlern ynkommen fan yndividuele klanten de wichtichste soarch is, kin persoanlik-nivo distribúsje ferbean bliuwe. Op syn beurt soene dyjingen dy't by steat binne om grutte repositories te behearjen — bedriuwen dy't LLM's traine, tegearre mei biblioteken en oare argiven — ûnder dizze útsûnderings falle."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Guon lannen dogge al in ferzje fan dit. TorrentFreak <a %(torrentfreak)s>rapporteare</a> dat Sina en Japan AI-útsûnderings ynfierd hawwe yn harren auteursrjochtwetten. It is ús net dúdlik hoe't dit ynteraksje hat mei ynternasjonale ferdraggen, mar it jout wis dekking oan harren ynlânske bedriuwen, wat ferklearret wat wy sjoen hawwe."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Wat Anna's Argyf oanbelanget — wy sille ús ûndergrûnske wurk trochsette, woartele yn morele oertsjûging. Mar ús grutste winsk is om yn it ljocht te kommen, en ús ynfloed legaal te fergrutsjen. Asjebleaft, reformearje it auteursrjocht."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lês de begeliedende artikels troch TorrentFreak: <a %(torrentfreak)s>earste</a>, <a %(torrentfreak_2)s>twadde</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Winners fan de $10,000 ISBN visualisaasje bounty"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Wy hawwe wat ûnfoarstelbere ynstjoeringen krigen foar de $10,000 ISBN visualisaasje bounty."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "In pear moannen lyn kundigen wy in <a %(all_isbns)s>$10,000 bounty</a> oan om de bêst mooglike visualisaasje fan ús gegevens te meitsjen dy't de ISBN-romte sjen lit. Wy leinen klam op it sjen litten hokker bestannen wy al/nog net argivearre hawwe, en wy levere letter in dataset dy't beskriuwt hoefolle biblioteken ISBN's hawwe (in mjitte fan seldsumheid)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Wy binne oerweldige troch de reaksje. Der is sa folle kreativiteit west. In grutte tank oan elkenien dy't meidien hat: jo enerzjy en entûsjasme binne oansteeklik!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Uteinlik woenen wy de folgjende fragen beantwurdzje: <strong>hokker boeken besteane der yn 'e wrâld, hoefolle hawwe wy al argivearre, en op hokker boeken moatte wy ús folgjende rjochtsje?</strong> It is prachtich om te sjen dat safolle minsken har ynteressearje foar dizze fragen."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Wy begûnen sels mei in basis fisualisaasje. Yn minder as 300kb fertsjintwurdiget dizze ôfbylding koart it grutste folslein iepen \"list fan boeken\" ea gearstald yn 'e skiednis fan 'e minskheid:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Bestannen yn Anna’s Argyf"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC gegevenslek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost’s eBook Yndeks"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Global Register fan Utjouwers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russyske Steatsbibleteek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Keizerlike Bibleteek fan Trantor"

#, fuzzy
msgid "common.back"
msgstr "Werom"

#, fuzzy
msgid "common.forward"
msgstr "Foarút"

#, fuzzy
msgid "common.last"
msgstr "Lêst"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Sjoch asjebleaft de <a %(all_isbns)s>oarspronklike blogpost</a> foar mear ynformaasje."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Wy hawwe in útdaging útjûn om dit te ferbetterjen. Wy soene in earste priis fan $6,000 útrikke, in twadde priis fan $3,000, en in tredde priis fan $1,000. Fanwegen de oerweldigjende reaksje en ûnbidige ynstjoeringen, hawwe wy besletten om de priispot in bytsje te ferheegjen, en in fjouwer-wei tredde priis fan $500 elk út te rikken. De winners steane hjirûnder, mar sjoch wis nei alle ynstjoeringen <a %(annas_archive)s>hjir</a>, of download ús <a %(a_2025_01_isbn_visualization_files)s>kombinearre torrent</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Earste priis $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Dizze <a %(phiresky_github)s>ynstjoering</a> (<a %(annas_archive_note_2951)s>Gitlab-kommentaar</a>) is gewoan alles wat wy woenen, en mear! Wy fûnen benammen de ûnbidich fleksibele fisualisaasje-opsjes geweldich (sels mei stipe foar oanpaste shaders), mar mei in wiidweidige list fan presets. Wy fûnen ek hoe fluch en soepel alles is, de ienfâldige ymplemintaasje (dy’t net iens in backend hat), de tûke minimap, en de wiidweidige útlis yn harren <a %(phiresky_github)s>blogpost</a>. Ûnbidich wurk, en de terjochte winner!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Twadde priis $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "In oare ûnbidige <a %(annas_archive_note_2913)s>ynstjoering</a>. Net sa fleksibel as de earste priis, mar wy fûnen de makro-nivo fisualisaasje eins better as de earste priis (romte-follende kromme, grinzen, labeling, markearring, panning, en zooming). In <a %(annas_archive_note_2971)s>kommentaar</a> fan Joe Davis resonearre mei ús:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Wylst perfekte fjouwerkanten en rjochthoeken wiskundich noflik binne, biede se gjin superieure lokaliteit yn in kaartkontekst. Ik leau dat de asymmetry ynherint yn dizze Hilbert of klassike Morton gjin flater is, mar in funksje. Krekt as de ferneamde laarsfoarmige omtrek fan Itaalje it daliks werkenber makket op in kaart, kinne de unike \"kuriositeiten\" fan dizze krommen tsjinje as kognitive landmarks. Dizze ûnderskiedendheid kin de romtlike ûnthâld ferbetterje en brûkers helpe harsels te oriïntearjen, wat it mooglik makket om spesifike regio's te finen of patroanen op te merken.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "En noch altyd in protte opsjes foar fisualisearjen en renderjen, lykas in ûnbidich soepele en yntuïtive UI. In solide twadde priis!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tredde priis $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Yn dizze <a %(annas_archive_note_2940)s>ynstjoering</a> fûnen wy de ferskillende soarten werjeften echt moai, benammen de fergeliking en útjouwer werjeften."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tredde priis $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Hoewol net de meast poetse UI, kontrolearret dizze <a %(annas_archive_note_2917)s>ynstjoering</a> in protte fan de fakjes. Wy fûnen benammen de fergeliking funksje moai."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tredde priis $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Lykas de earste priis, hat dizze <a %(annas_archive_note_2975)s>ynstjoering</a> ús yndruk makke mei syn fleksibiliteit. Uteinlik is dit wat in geweldige fisualisaasje-ark makket: maksimale fleksibiliteit foar krêftbrûkers, wylst it ienfâldich hâldt foar gemiddelde brûkers."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tredde priis $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "De lêste <a %(annas_archive_note_2947)s>ynstjoering</a> dy't in beleanning krijt is frij basis, mar hat wat unike funksjes dy't wy echt moai fûnen. Wy fûnen it moai hoe't se sjen litte hoefolle datasets in bepaald ISBN dekke as in mjitte fan populariteit/betrouberens. Wy fûnen ek de ienfâld mar effektyf fan it brûken fan in opasiteit-skuif foar fergelikingen echt moai."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Opfallende ideeën"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Guon mear ideeën en ymplemintaasjes dy't wy benammen moai fûnen:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Wolkenkrabbers foar seldsumens"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Live statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Annotaasjes, en ek live statistiken"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unike kaartbesjoch en filters"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Kâlde standert kleurenschema en hjittekaart."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Maklik wikseljen fan datasets foar flugge fergelikingen."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Moaie labels."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skaalbalke mei oantal boeken."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "In protte sliders om datasets te fergelykjen, as wiene jo in DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Wy koene noch in skoft trochgean, mar litte wy hjir stopje. Sjoch wis nei alle ynstjoeringen <a %(annas_archive)s>hjir</a>, of download ús <a %(a_2025_01_isbn_visualization_files)s>kombinearre torrent</a>. Sa'n soad ynstjoeringen, en elk bringt in unyk perspektyf, oft yn UI of ymplemintaasje."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Wy sille op syn minst de earste plak ynstjoering yn ús haadwebside opnimme, en miskien guon oaren. Wy binne ek begûn te tinken oer hoe't wy it proses fan it identifisearjen, befestigjen, en dan argivearjen fan de seldsumste boeken organisearje kinne. Mear hjir oer komt noch."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Tank oan elkenien dy't meidien hat. It is geweldich dat sa'n soad minsken derom jouwe."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Us herten binne fol mei tankberens."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisearjen fan Alle ISBNs — $10,000 beleanning foar 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Dizze ôfbylding fertsjintwurdiget de grutste folslein iepen \"list fan boeken\" ea gearstald yn de skiednis fan 'e minskheid."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Dizze ôfbylding is 1000×800 piksels. Elk piksel fertsjintwurdiget 2,500 ISBNs. As wy in bestân hawwe foar in ISBN, meitsje wy dat piksel mear grien. As wy witte dat in ISBN útjûn is, mar wy hawwe gjin oerienkommend bestân, meitsje wy it mear read."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Yn minder as 300kb, fertsjintwurdiget dizze ôfbylding koart de grutste folslein iepen \"list fan boeken\" ea gearstald yn de skiednis fan 'e minskheid (in pear hûndert GB komprimearre yn folslein)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "It toant ek: der is noch in protte wurk te dwaan yn it bewarjen fan boeken (wy hawwe allinnich 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Eftergrûn"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hoe kin Anna's Argiven syn missy fan it bewarjen fan alle kennis fan 'e minskheid berikke, sûnder te witten hokker boeken der noch binne? Wy hawwe in TODO-list nedich. Ien manier om dit yn kaart te bringen is troch ISBN-nûmers, dy't sûnt de jierren '70 oan elk publisearre boek tawiisd binne (yn de measte lannen)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Der is gjin sintrale autoriteit dy't alle ISBN-tawiisings wit. Ynstee dêrfan is it in ferspraat systeem, wêrby't lannen nûmerrigen krije, dy't dan lytsere rigen oan grutte útjouwers tawize, dy't op har beurt de rigen fierder ferdiele kinne oan lytse útjouwers. Uteinlik wurde yndividuele nûmers oan boeken tawiisd."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Wy binne twa jier lyn begûn mei it yn kaart bringen fan ISBNs <a %(blog)s>mei ús scrape fan ISBNdb</a>. Sûnt dy tiid hawwe wy in protte mear metadata-boarnen skraapt, lykas <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, en mear. In folsleine list is te finen op de siden “Datasets” en “Torrents” op Anna's Argiven. Wy hawwe no fierwei de grutste folslein iepen, maklik te downloaden kolleksje fan boekmetadata (en dus ISBNs) yn 'e wrâld."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Wy hawwe <a %(blog)s>wiidweidich skreaun</a> oer wêrom't wy soarchje oer bewarjen, en wêrom't wy op it stuit yn in kritysk tiidrek sitte. Wy moatte no seldsume, ûnderfokusearre en unyk bedrige boeken identifisearje en bewarje. It hawwen fan goede metadata oer alle boeken yn 'e wrâld helpt dêrby."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisearje"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Njonken it oersjochôfbylding kinne wy ek nei yndividuele datasets sjen dy't wy sammele hawwe. Brûk it útklapmenu en de knoppen om tusken harren te wikseljen."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Der binne in protte ynteressante patroanen te sjen yn dizze ôfbyldings. Wêrom is der wat regelmjittigens fan rigels en blokken, dy't op ferskate skalen liket te barren? Wat binne de lege gebieten? Wêrom binne bepaalde datasets sa klustere? Wy litte dizze fragen oer as in oefening foar de lêzer."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10.000 beleanning"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Der is hjir in protte te ûntdekken, dus wy kundigje in beleanning oan foar it ferbetterjen fan de visualisaasje hjirboppe. Oars as de measte fan ús beleannings, is dizze tiidbûn. Jo moatte jo iepen boarne koade yntsjinje foar 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "De bêste ynstjoering sil $6.000 krije, de twadde plak is $3.000, en it tredde plak is $1.000. Alle beleannings wurde útrikt mei Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Hjirûnder binne de minimale kritearia. As gjin ynstjoering oan de kritearia foldocht, kinne wy noch wat beleannings útrikke, mar dat sil nei ús ynsjoch wêze."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork dizze repo, en bewurkje dizze blogpost HTML (gjin oare backends as ús Flask-backend binne tastien)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Meitsje de ôfbylding hjirboppe soepel ynzoomber, sadat jo hielendal nei yndividuele ISBNs kinne ynzoome. It klikken op ISBNs moat jo nei in metadata-side of sykaksje op Anna's Argiven bringe."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Jo moatte noch altyd kinne wikselje tusken alle ferskillende datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Lânrigen en útjouwer-rigen moatte markearre wurde by hover. Jo kinne bygelyks <a %(github_xlcnd_isbnlib)s>data4info.py yn isbnlib</a> brûke foar lânynformaasje, en ús “isbngrp” scrape foar útjouwers (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "It moat goed wurkje op desktop en mobile."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Foar bonuspunten (dit binne allinnich ideeën — lit jo kreativiteit frij rinne):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sterke oerwagings sille jûn wurde oan brûkberens en hoe goed it derút sjocht."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Toan eigentlike metadata foar yndividuele ISBNs by it ynzoomen, lykas titel en auteur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Bêtere romte-follende kromme. Bygelyks in zigzag, dy't fan 0 nei 4 giet op de earste rigel en dan werom (yn omkearde rjochting) fan 5 nei 9 op de twadde rigel — rekursyf tapast."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Ferskillende of oanpasbere kleurtsjema's."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Spesjale werjeften foar it fergelykjen fan datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Manieren om problemen te debuggen, lykas oare metadata dy't net goed oerienkomme (bygelyks hiel ferskillende titels)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Ofbyldings annotearje mei opmerkingen oer ISBN's of berikken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Elke heuristyk foar it identifisearjen fan seldsume of bedrige boeken."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Hokker kreative ideeën jo ek betinke kinne!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Jo MEI folslein ôfwike fan de minimale kritearia, en in folslein oare fisualisaasje meitsje. As it echt spektakulêr is, dan kwalifisearret dat foar de bounty, mar nei ús goedtinken."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Meitsje ynstjoeringen troch in opmerking te pleatsen by <a %(annas_archive)s>dit probleem</a> mei in keppeling nei jo forked repo, merge request, of diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Koade"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "De koade om dizze ôfbyldings te generearjen, lykas oare foarbylden, is te finen yn <a %(annas_archive)s>dizze map</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Wy hawwe in kompakt dataformaat betocht, wêrmei alle nedige ISBN-ynformaasje sawat 75MB (komprimearre) is. De beskriuwing fan it dataformaat en de koade om it te generearjen kinne jo <a %(annas_archive_l1244_1319)s>hjir</a> fine. Foar de bounty binne jo net ferplichte dit te brûken, mar it is wierskynlik it meast handige formaat om mei te begjinnen. Jo kinne ús metadata transformearje hoe't jo wolle (hoewol al jo koade iepen boarne wêze moat)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Wy kinne net wachtsje om te sjen wat jo betinke. In protte súkses!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna's Argiven Containers (AAC): it standerdisearjen fan releases fan 'e grutste skaadbibleteek fan 'e wrâld"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna's Argiven is de grutste skaadbibleteek yn 'e wrâld wurden, wat ús ferplichtet ús releases te standerdisearjen."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna's Argiven</a> is fierwei de grutste skaadbibleteek yn 'e wrâld wurden, en de iennige skaadbibleteek fan dizze skaal dy't folslein iepen-boarne en iepen-data is. Hjirûnder is in tabel fan ús Datasets-side (in bytsje oanpast):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Wy hawwe dit op trije manieren berikt:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "It spegeljen fan besteande iepen-data skaadbibleteken (lykas Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "It helpen fan skaadbibleteken dy't mear iepen wolle wêze, mar net de tiid of middels hiene om dat te dwaan (lykas de Libgen stripferzamelingen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "It skrabjen fan bibleteken dy't net yn bulk diele wolle (lykas Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Foar (2) en (3) beheare wy no sels in grutte kolleksje fan torrents (hûnderten TB's). Oant no ta hawwe wy dizze kolleksjes as ienmalige projekten benadere, wat betsjut dat der foar elke kolleksje maatwurk ynfrastruktuer en gegevensorganisaasje nedich is. Dit foeget in signifikante overhead ta oan elke release, en makket it benammen dreech om mear ynkrementele releases te dwaan."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Dêrom hawwe wy besletten om ús releases te standerdisearjen. Dit is in technyske blogpost wêryn wy ús standert yntrodusearje: <strong>Anna’s Argiven Konteners</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Untwerpdoelen"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Us primêre gebrûksgefal is de distribúsje fan bestannen en assosjearre metadata út ferskate besteande kolleksjes. Us wichtichste oerwagings binne:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene bestannen en metadata, sa ticht mooglik by it orizjinele formaat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene identifiers yn de boarne-biblioteken, of sels it ûntbrekken fan identifiers."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Separate releases fan metadata tsjin bestânsgegevens, of allinnich metadata-releases (bygelyks ús ISBNdb release)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Distribúsje fia torrents, mar mei de mooglikheid fan oare distribúsjemetoaden (bygelyks IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Unferoarlike records, om't wy oannimme moatte dat ús torrents foar ivich bestean bliuwe."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Ynkrementele releases / oanfolbere releases."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Masine-lêsber en skriuwber, handich en fluch, benammen foar ús stack (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "In bytsje maklik foar minsklike ynspeksje, hoewol dit sekundêr is oan masine-lêsberens."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Maklik om ús kolleksjes te seedjen mei in standert hierde seedbox."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binaire gegevens kinne direkt tsjinne wurde troch webservers lykas Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Guon net-doelen:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Wy jouwe net om dat bestannen maklik manuell op skiif te navigearjen binne, of sykber sûnder foarferwurking."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Wy jouwe net om direkt kompatibel te wêzen mei besteande biblioteeksoftware."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Hoewol it maklik wêze moat foar elkenien om ús kolleksje te seedjen mei torrents, ferwachtsje wy net dat de bestannen brûkber binne sûnder signifikante technyske kennis en ynset."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Om't Anna’s Argiven iepen boarne is, wolle wy ús formaat direkt brûke. As wy ús sykindeks fernije, tagongje wy allinnich iepenbier beskikbere paden, sadat elkenien dy't ús biblioteek fork kin fluch oan de slach gean."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "De standert"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Uteinlik hawwe wy ús fêstlein op in relatyf ienfâldige standert. It is frij los, net-normatyf, en in wurk yn ûntwikkeling."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Argiven Kontener) is in ienich item besteande út <strong>metadata</strong>, en opsjoneel <strong>binaire gegevens</strong>, dy't beide net feroare wurde kinne. It hat in wrâldwiid unyk identifikaasje, neamd <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Kolleksje.</strong> Elke AAC heart by in kolleksje, dy't definiearre wurdt as in list fan AACs dy't semantysk konsistint binne. Dat betsjut dat as jo in wichtige feroaring meitsje oan it formaat fan de metadata, jo in nije kolleksje meitsje moatte."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“records” en “files” kolleksjes.</strong> Neffens konvinsje is it faak handich om “records” en “files” as ferskillende kolleksjes út te jaan, sadat se op ferskillende skema's útjûn wurde kinne, bygelyks basearre op scraping-tariven. In “record” is in allinne-metadata kolleksje, mei ynformaasje lykas boek titels, auteurs, ISBNs, ensfh., wylst “files” de kolleksjes binne dy't de eigentlike bestannen sels befetsje (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> It formaat fan AACID is dit: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Bygelyks, in eigentlike AACID dy't wy útjûn hawwe is <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: de namme fan de kolleksje, dy't ASCII letters, sifers, en underscores befetsje kin (mar gjin dûbele underscores)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: in koarte ferzje fan de ISO 8601, altyd yn UTC, bygelyks <code>20220723T194746Z</code>. Dit nûmer moat monotonyk tanimme foar elke útjefte, hoewol't de krekte semantyk ferskille kin per kolleksje. Wy suggerearje it brûken fan de tiid fan scraping of fan it generearjen fan de ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: in kolleksje-spesifike identifikaasje, as fan tapassing, bygelyks de Z-Library ID. Mei weglitten of ynkoarte wurde. Moat weglitten of ynkoarte wurde as de AACID oars mear as 150 tekens soe wêze."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: in UUID mar komprimearre nei ASCII, bygelyks mei base57. Wy brûke op it stuit de <a %(github_skorokithakis_shortuuid)s>shortuuid</a> Python-biblioteek."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID berik.</strong> Om't AACIDs monotonyk tanimmende timestamps befetsje, kinne wy dat brûke om berikken oan te jaan binnen in bepaalde kolleksje. Wy brûke dit formaat: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, wêrby't de timestamps ynklusyf binne. Dit is konsistint mei ISO 8601 notaasjes. Berikken binne trochgeand, en kinne oerlappe, mar yn gefal fan oerlap moatte se identike records befetsje as de earder útjûne yn dy kolleksje (om't AACs net feroare wurde kinne). Mankearjende records binne net tastien."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata-bestân.</strong> In metadata-bestân befettet de metadata fan in berik fan AACs, foar ien bepaalde kolleksje. Dizze hawwe de folgjende eigenskippen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Bestânsnamme moat in AACID berik wêze, foarôfgien troch <code style=\"color: red\">annas_archive_meta__</code> en folge troch <code>.jsonl.zstd</code>. Bygelyks, ien fan ús útjeften hjit<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Sa't oanjûn troch de bestânsútwreiding, is it bestânstype <a %(jsonlines)s>JSON Lines</a> komprimearre mei <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Elk JSON-objekt moat de folgjende fjilden op it boppeste nivo befetsje: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsjoneel). Gjin oare fjilden binne tastien."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> is arbitraire metadata, neffens de semantyk fan de kolleksje. It moat semantysk konsistint wêze binnen de kolleksje."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> is opsjoneel, en is de namme fan de binaire gegevensmap dy't de oerienkommende binaire gegevens befettet. De bestânsnamme fan de oerienkommende binaire gegevens binnen dy map is de AACID fan it record."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "De <code style=\"color: red\">annas_archive_meta__</code> foarheaksel kin oanpast wurde oan de namme fan jo ynstelling, bygelyks <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binaire gegevensmap.</strong> In map mei de binaire gegevens fan in berik fan AACs, foar ien bepaalde kolleksje. Dizze hawwe de folgjende eigenskippen:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Mapnamme moat in AACID berik wêze, foarôfgien troch <code style=\"color: green\">annas_archive_data__</code>, en gjin suffix. Bygelyks, ien fan ús eigentlike útjeften hat in map neamd<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "De map moat gegevensbestannen befetsje foar alle AACs binnen it spesifisearre berik. Elk gegevensbestân moat syn AACID as de bestânsnamme hawwe (gjin útwreidingen)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "It wurdt oanrikkemandearre om dizze mappen wat behearskber yn grutte te hâlden, bygelyks net grutter as 100GB-1TB elk, hoewol dizze oanrikkemandearring oer tiid feroarje kin."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> De metadata-bestannen en binêre datamappen kinne yn torrents bûn wurde, mei ien torrent per metadata-bestân, of ien torrent per binêre datamap. De torrents moatte de orizjinele bestâns-/mapnamme plus in <code>.torrent</code> suffix as har bestânsnamme hawwe."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Foarbyld"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Litte wy ús resinte Z-Library release as in foarbyld besjen. It bestiet út twa kolleksjes: “<span style=\"background: #fffaa3\">zlib3_records</span>” en “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dit lit ús metadata-records apart skraapje en frijlitte fan de eigentlike boekbestannen. Sa hawwe wy twa torrents mei metadata-bestannen frijlitten:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Wy hawwe ek in rige torrents mei binêre datamappen frijlitten, mar allinnich foar de “<span style=\"background: #ffd6fe\">zlib3_files</span>” kolleksje, yn totaal 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Troch <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> út te fieren kinne wy sjen wat der yn sit:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Yn dit gefal is it metadata fan in boek sa't rapporteare troch Z-Library. Op it topnivo hawwe wy allinnich “aacid” en “metadata”, mar gjin “data_folder”, om't der gjin oerienkommende binêre data is. De AACID befettet “22430000” as de primêre ID, dy't wy sjogge is nommen fan “zlibrary_id”. Wy kinne ferwachtsje dat oare AAC's yn dizze kolleksje deselde struktuer hawwe."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "No litte wy <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> útfiere:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dit is in folle lytsere AAC metadata, hoewol it grutste part fan dizze AAC earne oars yn in binêr bestân leit! Uteinlik hawwe wy dizze kear in “data_folder”, dus wy kinne ferwachtsje dat de oerienkommende binêre data leit by <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. De “metadata” befettet de “zlibrary_id”, sadat wy it maklik kinne assosjearje mei de oerienkommende AAC yn de “zlib_records” kolleksje. Wy koene it op ferskate manieren assosjearje, bygelyks fia AACID — de standert skriuwt dat net foar."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Merk op dat it ek net nedich is dat it “metadata” fjild sels JSON is. It kin in string wêze dy't XML of in oar dataformaat befettet. Jo kinne sels metadata-ynformaasje opslaan yn de assosjearre binêre blob, bygelyks as it in soad gegevens is."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Konklúzje"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Mei dizze standert kinne wy releases mear ynkrementeel meitsje, en makliker nije boarnen fan gegevens tafoegje. Wy hawwe al in pear spannende releases yn de pipeline!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Wy hoopje ek dat it makliker wurdt foar oare skaadbiblioteken om ús kolleksjes te spegeljen. Uteinlik is ús doel om minsklike kennis en kultuer foar ivich te bewarjen, dus hoe mear redundânsje, hoe better."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna’s Update: folslein iepen boarne argyf, ElasticSearch, 300GB+ oan boekomslagen"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Wy hawwe rûn de klok wurke om in goed alternatyf te bieden mei Anna’s Argyf. Hjir binne guon fan de dingen dy't wy koartlyn berikt hawwe."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Mei Z-Library dy't delgiet en har (alleged) oprjochters dy't arrestearre wurde, hawwe wy rûn de klok wurke om in goed alternatyf te bieden mei Anna’s Argyf (wy sille it hjir net keppelje, mar jo kinne it op Google fine). Hjir binne guon fan de dingen dy't wy koartlyn berikt hawwe."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna’s Argyf is folslein iepen boarne"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Wy leauwe dat ynformaasje frij wêze moat, en ús eigen koade is gjin útsûndering. Wy hawwe al ús koade frijlitten op ús privee hoste Gitlab-ynstânsje: <a %(annas_archive)s>Anna’s Software</a>. Wy brûke ek de issue tracker om ús wurk te organisearjen. As jo mei ús ûntwikkeling dwaande wolle, is dit in geweldige plak om te begjinnen."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Om jo in idee te jaan fan de dingen dêr't wy oan wurkje, nim ús resinte wurk oan kliïnt-side prestaasjeferbetteringen. Om't wy noch gjin paginaasje ymplementearre hawwe, soene wy faak heul lange sykpagina's weromjaan, mei 100-200 resultaten. Wy woene de sykresultaten net te gau ôfbrekke, mar dit betsjutte wol dat it guon apparaten fertrage soe. Hjirfoar hawwe wy in lyts trúk útfierd: wy hawwe de measte sykresultaten yn HTML-kommentaren (<code><!-- --></code>) ynpakt, en doe in lyts Javascript skreaun dat soe detektearje wannear't in resultaat sichtber wurde moat, op dat momint soene wy it kommentaar ûntpakke:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisaasje\" ymplementearre yn 23 rigels, gjin needsaak foar fancy biblioteken! Dit is it soarte fan rap pragmatyske koade dat jo einigje mei as jo beheinde tiid hawwe, en echte problemen dy't oplost wurde moatte. It is rapporteare dat ús sykfunksje no goed wurket op stadige apparaten!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "In oare grutte ynspanning wie it automatisearjen fan it bouwen fan de databank. Doe't wy lansearren, hawwe wy gewoan ferskate boarnen gearfoege. No wolle wy se bywurke hâlde, dus hawwe wy in rige skripts skreaun om nije metadata fan de twa Library Genesis forks te downloaden, en se te yntegrearjen. It doel is net allinich om dit nuttich te meitsjen foar ús argyf, mar ek om it maklik te meitsjen foar elkenien dy't mei skadubiblioteekmetadata boartsje wol. It doel soe in Jupyter-notebook wêze dy't alle soarten ynteressante metadata beskikber hat, sadat wy mear ûndersyk dwaan kinne lykas útfine hokker <a %(blog)s>persintaazje fan ISBNs foar ivich bewarre wurde</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Uteinlik hawwe wy ús donaasjesysteem opnij ûntwikkele. Jo kinne no in kredytkaart brûke om direkt jild yn ús crypto-wallets te deponearjen, sûnder echt wat oer cryptocurrencies te witten. Wy sille yn de gaten hâlde hoe goed dit yn de praktyk wurket, mar dit is in grutte stap."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Oerskeakelje nei ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Ien fan ús <a %(annas_archive)s>kaarten</a> wie in samling fan problemen mei ús syksysteem. Wy brûkten MySQL full-text search, om't wy al ús gegevens yn MySQL hiene. Mar it hie syn grinzen:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Guon queries duorren super lang, oant it punt wêr't se alle iepen ferbinings yn beslach namen."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Standert hat MySQL in minimale wurdlengte, of jo yndeks kin echt grut wurde. Minsken rapportearren dat se net koene sykje nei \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Sykjen wie allinich wat rap as it folslein yn it ûnthâld laden wie, wat ús twong om in djoerder masine te krijen om dit op út te fieren, plus wat kommando's om de yndeks by opstarten foar te laden."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Wy soene it net maklik útwreidzje kinne om nije funksjes te bouwen, lykas bettere <a %(wikipedia_cjk_characters)s>tokenisaasje foar net-wytromte-talen</a>, filterjen/facettering, sortearjen, \"bedoelde jo\" suggestjes, autokomplete, ensafuorthinne."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Nei petearen mei in rige saakkundigen hawwe wy keazen foar ElasticSearch. It hat net perfekt west (harren standert \"bedoelde jo\" suggestjes en autokomplete funksjes binne min), mar oer it algemien is it in stik better as MySQL foar sykjen. Wy binne noch net <a %(youtube)s>te entûsjast</a> oer it brûken foar misjekrityske gegevens (hoewol se in soad <a %(elastic_co)s>foarútgong</a> makke hawwe), mar oer it algemien binne wy tige tefreden mei de oerskeakeling."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Foar no hawwe wy in folle rapper sykjen ymplemintearre, bettere taalstipe, bettere relevânsje-sortearring, ferskate sortearopsjes, en filterjen op taal/boektype/bestândype. As jo benijd binne hoe't it wurket, <a %(annas_archive_l140)s>sjoch</a> <a %(annas_archive_l1115)s>efkes</a> <a %(annas_archive_l1635)s>hjir</a>. It is frij tagonklik, hoewol it wat mear kommentaar brûke kin…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ oan boekomslagen frijlitten"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Uteinlik binne wy bliid om in lytse frijlitting oan te kundigjen. Yn gearwurking mei de minsken dy't de Libgen.rs fork beheare, diele wy al har boekomslagen fia torrents en IPFS. Dit sil de lading fan it besjen fan de omslaggen ferdiele oer mear masines, en se better bewarje. Yn in protte (mar net alle) gefallen binne de boekomslagen yn de bestannen sels opnommen, dus dit is in soarte fan \"ôflaat gegevens\". Mar it hawwen yn IPFS is noch hieltyd tige nuttich foar deistige operaasje fan sawol Anna’s Argief as de ferskate Library Genesis forks."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Lykas gewoanlik kinne jo dizze frijlitting fine by de Pirate Library Mirror (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argief</a>). Wy sille hjir net nei ferwize, mar jo kinne it maklik fine."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hopelik kinne wy ús tempo wat ferleegje, no't wy in fatsoenlik alternatyf foar Z-Library hawwe. Dizze wurkdruk is net bysûnder duorsum. As jo ynteressearre binne om te helpen mei programmeren, serveroperaasjes, of bewaringswurk, nim dan wis kontakt mei ús op. Der is noch in protte <a %(annas_archive)s>wurk te dwaan</a>. Tank foar jo ynteresse en stipe."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna’s Argief hat de grutste skadubiblioteek foar strips yn 'e wrâld bewarre (95TB) — jo kinne helpe om it te seedjen"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "De grutste skadubiblioteek foar stripboeken yn 'e wrâld hie in ienige mislearringspunt.. oant hjoed."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Diskusjearje op Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "De grutste skadubiblioteek foar stripboeken is wierskynlik dy fan in spesifike Library Genesis fork: Libgen.li. De ienige behearder dy't dy side rint, slagge deryn om in ûnfoarstelbere kolleksje fan strips te sammeljen fan mear as 2 miljoen bestannen, mei in totaal fan mear as 95TB. Mar, oars as oare Library Genesis-kolleksjes, wie dizze net yn bulk beskikber fia torrents. Jo koene dizze strips allinich yndividueel tagong krije fia syn stadige persoanlike server — in ienige mislearringspunt. Oant hjoed!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Yn dizze post sille wy jo mear fertelle oer dizze kolleksje, en oer ús fundraiser om mear fan dit wurk te stypjen."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon besiket harsels te ferliezen yn 'e gewoane wrâld fan 'e bibleteek…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen forks"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Earst, wat eftergrûn. Jo kinne Library Genesis kenne foar harren epyske boekekolleksje. Minder minsken witte dat Library Genesis-frijwilligers oare projekten makke hawwe, lykas in grutte kolleksje fan tydskriften en standertdokuminten, in folsleine backup fan Sci-Hub (yn gearwurking mei de oprjochter fan Sci-Hub, Alexandra Elbakyan), en yndie, in grutte kolleksje fan strips."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Op in stuit giene ferskate operators fan Library Genesis-spegeltsjes harren eigen wei, wat late ta de hjoeddeistige situaasje fan it hawwen fan in oantal ferskillende \"forks\", dy't allegear noch de namme Library Genesis drage. De Libgen.li fork hat unyk dizze stripskolleksje, lykas in grutte tydskriftenkolleksje (wêr't wy ek oan wurkje)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Gearwurking"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Jûn syn grutte, hat dizze kolleksje al lang op ús winskelist stien, dus nei ús súkses mei it bewarjen fan Z-Library, rjochten wy ús op dizze kolleksje. Earst skraapten wy it direkt, wat in aardige útdaging wie, om't harren server net yn 'e bêste kondysje wie. Wy krigen sa'n 15TB op dizze manier, mar it gie stadich."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Gelokkich slagge it ús om yn kontakt te kommen mei de operator fan 'e bibleteek, dy't ynstimde om ús alle gegevens direkt te stjoeren, wat in stik flugger wie. It duorre noch mear as in heal jier om alle gegevens oer te bringen en te ferwurkjen, en wy hawwe hast alles ferlern troch skiifkorruptie, wat betsjutte soe dat wy opnij begjinne moasten."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Dizze ûnderfining hat ús leauwe litten dat it wichtich is om dizze gegevens sa gau mooglik út te bringen, sadat it breed spegele wurde kin. Wy binne mar ien of twa ûngelokkich timed ynsidinten fuort fan it foar altyd ferliezen fan dizze kolleksje!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "De kolleksje"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Fluch bewege betsjut wol dat de kolleksje in bytsje ûnorganisearre is… Litte wy efkes sjen. Stel jo foar dat wy in bestânsysteem hawwe (dat wy yn werklikheid oer torrents ferdiele):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "De earste map, <code>/repository</code>, is it mear strukturearre diel hjirfan. Dizze map befettet saneamde \"tûzen dirs\": mappen elk mei tûzenen bestannen, dy't ynkrementaal nûmere binne yn 'e database. Map <code>0</code> befettet bestannen mei comic_id 0–999, en sa fierder."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dit is itselde skema dat Library Genesis brûkt hat foar syn fiksje- en non-fiksjekolleksjes. It idee is dat elke \"tûzen dir\" automatysk omset wurdt yn in torrent sa gau't it fol is."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Lykwols, de Libgen.li operator hat nea torrents makke foar dizze kolleksje, en dus waarden de tûzen dirs wierskynlik ûngemaklik, en makken plak foar \"ûnsoartearre dirs\". Dizze binne <code>/comics0</code> troch <code>/comics4</code>. Se befetsje allegear unike mapstrukturen, dy't wierskynlik sin makken foar it sammeljen fan 'e bestannen, mar meitsje no net folle sin foar ús. Gelokkich ferwiist de metadata noch direkt nei al dizze bestannen, dus harren opslachorganisaasje op skiif makket eins net út!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "De metadata is beskikber yn 'e foarm fan in MySQL-database. Dit kin direkt fan 'e Libgen.li-webside downloade wurde, mar wy sille it ek beskikber meitsje yn in torrent, neist ús eigen tabel mei alle MD5-hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analyse"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "As jo 95TB yn jo opslachkluster dumpe, besykje jo te begripen wat der sels yn sit… Wy diene wat analyse om te sjen oft wy de grutte wat ferminderje koene, lykas troch duplikaten te ferwiderjen. Hjir binne guon fan ús befiningen:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantyske duplikaten (ferskillende scans fan itselde boek) kinne teoretysk útfiltere wurde, mar it is lestich. By it manuell trochsykjen fan 'e strips fûnen wy te folle falske posityven."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Der binne guon duplikaten allinnich troch MD5, wat relatyf fergriemerich is, mar it útfiterjen fan dy soe ús mar sa'n 1% in besparring jaan. Op dizze skaal is dat noch altyd sa'n 1TB, mar ek, op dizze skaal makket 1TB net echt út. Wy wolle leaver net it risiko rinne om per ongelok gegevens te ferneatigjen yn dit proses."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Wy fûnen in soad net-boekgegevens, lykas films basearre op stripboeken. Dat liket ek fergriemerich, om't dizze al breed beskikber binne fia oare middels. Lykwols, wy realisearren ús dat wy net gewoan filmtriemen útfiterje koene, om't der ek <em>ynteraktive stripboeken</em> binne dy't op 'e kompjûter útbrocht waarden, dy't immen opnaam en opslein hat as films."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Uteinlik soe de kolleksje wiskje soe mar in pear prosint besparje. Doe betochten wy ús dat wy datahoarders binne, en de minsken dy't dit spegelje sille ek datahoarders binne, en dus, \"WAT BEDOELSTO, WISKE?!\""

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Wy presintearje jo dêrom de folsleine, net oanpaste kolleksje. It is in soad data, mar wy hoopje dat genôch minsken it hoe dan ek seedje sille."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Jildynsammeling"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Wy bringe dizze data út yn grutte brokken. De earste torrent is fan <code>/comics0</code>, dy't wy yn ien grutte 12TB .tar-bestân dien hawwe. Dat is better foar jo hurde skiif en torrentsoftware as in gazillion lytsere bestannen."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "As diel fan dizze release dogge wy in jildynsammeling. Wy besykje $20,000 op te heljen om de operasjonele en kontraktkosten foar dizze kolleksje te dekken, en ek om trochgeande en takomstige projekten mooglik te meitsjen. Wy hawwe wat <em>massive</em> yn 'e wurken."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Wa stypje ik mei myn donaasje?</em> Koartsein: wy bewarje alle kennis en kultuer fan 'e minskheid, en meitsje it maklik tagonklik. Al ús koade en data binne iepen boarne, wy binne in folslein frijwilligersprojekt, en wy hawwe oant no ta 125TB oan boeken bewarre (neist de besteande torrents fan Libgen en Scihub). Uteinlik bouwe wy in flywheel dat minsken ynskeakelje en stimulearje om alle boeken yn 'e wrâld te finen, te scannen en te bewarjen. Wy sille yn in takomstige post oer ús masterplan skriuwe. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "As jo donearje foar in 12-moanne \"Amazing Archivist\" lidmaatskip ($780), kinne jo <strong>“in torrent adoptearje”</strong>, wat betsjut dat wy jo brûkersnamme of berjocht yn de bestânsnamme fan ien fan 'e torrents sette!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Jo kinne donearje troch nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a> te gean en op de knop \"Donearje\" te klikken. Wy sykje ek mear frijwilligers: software-yngenieurs, befeiligingsûndersikers, anonyme hannelseksperts, en oersetters. Jo kinne ús ek stypje troch hostingtsjinsten te leverjen. En fansels, seed ús torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Tank oan elkenien dy't ús al sa genereus stipe hat! Jo meitsje echt in ferskil."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hjir binne de torrents dy't oant no ta útbrocht binne (wy binne noch dwaande mei de rest):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alle torrents kinne fûn wurde op <a %(wikipedia_annas_archive)s>Anna’s Argyf</a> ûnder \"Datasets\" (wy keppelje dêr net direkt, sadat keppelings nei dizze blog net fan Reddit, Twitter, ensfh. fuorthelle wurde). Fan dêrút, folgje de keppeling nei de Tor-webside."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wat komt dernei?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "In protte torrents binne geweldich foar langduorjende behâld, mar net sa folle foar deistige tagong. Wy sille wurkje mei hostingpartners om al dizze data op it web te krijen (om't Anna’s Argyf neat direkt hostet). Fansels kinne jo dizze downloadlinks fine op Anna’s Argyf."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Wy noegje elkenien ek út om dingen mei dizze data te dwaan! Help ús it better te analysearjen, te deduplikearjen, op IPFS te setten, it te remixen, jo AI-modellen dermei te trainen, en sa fierder. It is allegear fan jo, en wy kinne net wachtsje om te sjen wat jo dermei dogge."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Uteinlik, lykas earder sein, hawwe wy noch wat massive releases yn 'e pipeline (as <em>immen</em> ús per <em>ûngelok</em> in dump fan in <em>bepaalde</em> ACS4-database stjoere koe, jo witte wêr't jo ús fine kinne...), en ek it bouwen fan it flywheel foar it bewarjen fan alle boeken yn 'e wrâld."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Dus bliuw op 'e hichte, wy binne krekt begûn."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nije boeken tafoege oan de Pirate Library Mirror (+24TB, 3,8 miljoen boeken)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Yn de oarspronklike release fan de Pirate Library Mirror (EDIT: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>), makken wy in spegel fan Z-Library, in grutte yllegale boekekolleksje. As herinnering, dit is wat wy yn dy oarspronklike blogpost skreaunen:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library is in populêre (en yllegale) bibleteek. Se hawwe de Library Genesis-kolleksje nommen en maklik trochsykber makke. Dêrneist binne se tige effektyf wurden yn it oanlûken fan nije boekbydragen, troch bydrage brûkers te stimulearjen mei ferskate foardielen. Se drage op it stuit dizze nije boeken net werom oan Library Genesis. En oars as Library Genesis, meitsje se har kolleksje net maklik spegelber, wat breed behâld foarkomt. Dit is wichtich foar har bedriuwsmodel, om't se jild freegje foar tagong ta har kolleksje yn bulk (mear as 10 boeken per dei)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Wy meitsje gjin morele oardielen oer it yn rekken bringen fan jild foar bulk tagong ta in yllegale boekekolleksje. It is bûten mis dat de Z-Bibleteek suksesfol west hat yn it útwreidzjen fan tagong ta kennis, en mear boeken te finen. Wy binne hjir gewoan om ús diel te dwaan: it soargjen foar de lange termyn behâld fan dizze priveekolleksje."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Dy kolleksje datearre út mids-2021. Yntusken is de Z-Bibleteek yn in ferbjusterjend tempo groeid: se hawwe sawat 3,8 miljoen nije boeken tafoege. Der binne wat duplikaten yn, wis, mar it meastepart liket legitime nij boeken te wêzen, of hegere kwaliteit scans fan earder ynstjoerde boeken. Dit is foar in grut part troch it tanommen oantal frijwillige moderators by de Z-Bibleteek, en harren bulk-upload systeem mei deduplikearring. Wy wolle harren lokwinskje mei dizze prestaasjes."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Wy binne bliid om oan te kundigjen dat wy alle boeken krigen hawwe dy't tafoege waarden oan de Z-Bibleteek tusken ús lêste spegel en augustus 2022. Wy hawwe ek weromgien en guon boeken skraapt dy't wy de earste kear mist hawwe. Alles byinoar is dizze nije kolleksje sawat 24TB, wat folle grutter is as de lêste (7TB). Us spegel is no yn totaal 31TB. Op 'e nij hawwe wy deduplikearre tsjin Library Genesis, om't der al torrents beskikber binne foar dy kolleksje."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Gean asjebleaft nei de Pirate Library Mirror om de nije kolleksje te besjen (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>). Dêr is mear ynformaasje oer hoe't de triemmen strukturearre binne, en wat feroare is sûnt de lêste kear. Wy sille der hjir net nei ferwize, om't dit gewoan in blogwebside is dy't gjin yllegale materialen hostet."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Fansels is it seedjen ek in geweldige manier om ús te helpen. Tank oan elkenien dy't ús foarige set torrents seedet. Wy binne tankber foar de positive reaksje, en bliid dat der safolle minsken binne dy't soarchje oer it behâld fan kennis en kultuer op dizze ûngewoane manier."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Hoe in pirate-argivaris te wurden"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "De earste útdaging kin in ferrassende wêze. It is gjin technysk probleem, of in juridysk probleem. It is in psychologysk probleem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Foardat wy begjinne, twa updates oer de Pirate Library Mirror (BEWURKJE: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Wy hawwe wat tige royale donaasjes krigen. De earste wie $10k fan de anonyme persoan dy't ek \"bookwarrior\" stipe hat, de oarspronklike oprjochter fan Library Genesis. Spesjale tank oan bookwarrior foar it fasilitearjen fan dizze donaasje. De twadde wie nochris $10k fan in anonyme donor, dy't nei ús lêste release kontakt opnaam en ynspirearre waard om te helpen. Wy hiene ek in oantal lytsere donaasjes. Tige tank foar al jimme royale stipe. Wy hawwe wat spannende nije projekten yn de pipeline dy't dit stypje sil, dus bliuw op 'e hichte."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Wy hiene wat technyske swierrichheden mei de grutte fan ús twadde release, mar ús torrents binne no online en seedje. Wy krigen ek in royale oanbieding fan in anonyme persoan om ús kolleksje te seedjen op harren heul-hege-snelheid servers, dus wy dogge in spesjale upload nei harren masines, wêrnei't elkenien oars dy't de kolleksje downloadt in grutte ferbettering yn snelheid sjen moat."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Hiele boeken kinne skreaun wurde oer de <em>wêrom</em> fan digitale behâld yn it algemien, en pirate-argivisme yn it bysûnder, mar lit ús in koarte yntroduksje jaan foar dyjingen dy't net te bekend binne. De wrâld produsearret mear kennis en kultuer as ea earder, mar ek mear dêrfan wurdt ferlern as ea earder. De minskheid fertrout foar in grut part op bedriuwen lykas akademyske útjouwers, streamingtsjinsten, en sosjale mediabedriuwen mei dit erfgoed, en se hawwe faak net bewiisd geweldige behearders te wêzen. Besjoch de dokumintêre Digital Amnesia, of eins elke lêzing fan Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Der binne guon ynstellingen dy't in goede baan dogge mei it argivearjen fan safolle as se kinne, mar se binne bûn troch de wet. As piraten binne wy yn in unike posysje om kolleksjes te argivearjen dy't se net oanreitsje kinne, fanwegen auteursrjochtshandhaving of oare beheiningen. Wy kinne ek kolleksjes ferskate kearen spegelje, oer de hiele wrâld, en dêrmei de kânsen op goed behâld ferheegje."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Foar no sille wy net yngean op diskusjes oer de foar- en neidielen fan yntellektueel eigendom, de moraliteit fan it brekken fan de wet, betinkingen oer sensuer, of de kwestje fan tagong ta kennis en kultuer. Mei dat alles út 'e wei, lit ús dûke yn de <em>hoe</em>. Wy sille diele hoe't ús team pirate-argivarissen waard, en de lessen dy't wy ûnderweis leard hawwe. Der binne in protte útdagings as jo oan dizze reis begjinne, en hooplik kinne wy jo troch guon fan harren helpe."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Mienskip"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "De earste útdaging kin in ferrassende wêze. It is gjin technysk probleem, of in juridysk probleem. It is in psychologysk probleem: dit wurk yn 'e skaad dwaan kin ongelooflijk iensum wêze. Ofhinklik fan wat jo fan plan binne te dwaan, en jo bedrigingsmodel, moatte jo miskien tige foarsichtich wêze. Oan 'e iene kant fan it spektrum hawwe wy minsken lykas Alexandra Elbakyan*, de oprjochter fan Sci-Hub, dy't tige iepen is oer har aktiviteiten. Mar se is op dit stuit yn heech risiko om arrestearre te wurden as se in westers lân soe besykje, en kin tsientallen jierren finzenisstraf krije. Is dat in risiko dat jo ree binne te nimmen? Wy binne oan 'e oare kant fan it spektrum; tige foarsichtich om gjin spoar efter te litten, en sterke operasjonele feiligens te hawwen."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Sa't neamd op HN troch \"ynno\", woe Alexandra yn it earstoan net bekend wêze: \"Har servers wiene ynsteld om detaillearre flaterberjochten fan PHP út te stjoeren, ynklusyf folsleine paad fan de foutmakende boarnebestân, dat ûnder map /home/<USER>'t se online hie op in net-relatearre side, keppele oan har echte namme. Foardat dizze iepenbiering wie se anonym.\" Dus, brûk tafallige brûkersnammen op de kompjûters dy't jo foar dit spul brûke, foar it gefal dat jo wat ferkeard konfigurearje."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Dy geheimhâlding, lykwols, komt mei in psychologyske kosten. De measte minsken hâlde fan erkend wurde foar it wurk dat se dogge, en dochs kinne jo gjin kredyt nimme foar dit yn it echte libben. Sels ienfâldige dingen kinne útdaagjend wêze, lykas freonen dy't freegje wat jo dien hawwe (op in stuit wurdt \"omprutsen mei myn NAS / homelab\" âld)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dêrom is it sa wichtich om wat mienskip te finen. Jo kinne wat operasjonele feiligens opjaan troch te fertrouwen yn guon tige tichte freonen, dy't jo witte dat jo djip fertrouwe kinne. Sels dan, wês foarsichtich om neat op skrift te setten, foar it gefal dat se harren e-mails oan de autoriteiten moatte oerdraachje, of as harren apparaten op in oare manier kompromittearre binne."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Noch better is om wat oare piraten te finen. As jo tichte freonen ynteressearre binne om mei te dwaan, geweldich! Oars kinne jo miskien oaren online fine. Spitigernôch is dit noch in niche mienskip. Oant no ta hawwe wy mar in hantsjefol oaren fûn dy't aktyf binne yn dizze romte. Goede startplakken lykje de Library Genesis forums te wêzen, en r/DataHoarder. It Archive Team hat ek lykas-minded yndividuen, hoewol se binnen de wet operearje (sels as yn guon griis gebieten fan de wet). De tradisjonele \"warez\" en piraterij sênes hawwe ek minsken dy't op ferlykbere manieren tinke."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Wy steane iepen foar ideeën oer hoe't wy mienskip kinne stimulearje en ideeën ferkenne. Fiel jo frij om ús in berjocht te stjoeren op Twitter of Reddit. Miskien koene wy in soarte fan forum of peteargroep hostje. Ien útdaging is dat dit maklik sensurearre wurde kin by it brûken fan mienskiplike platfoarms, dus wy soene it sels moatte hostje. Der is ek in ôfwaging tusken it folslein iepenbier meitsjen fan dizze diskusjes (mear potensjele belutsenens) tsjin it privee meitsjen (net litte potensjele \"doelen\" witte dat wy op it punt steane om se te skraapjen). Wy sille dêr oer neitinke moatte. Lit ús witte as jo hjir ynteressearre yn binne!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekten"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "As wy in projekt dogge, hat it in pear fazen:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domein seleksje / filosofy: Wêr wolle jo rûchwei op rjochtsje, en wêrom? Wat binne jo unike passys, feardichheden, en omstannichheden dy't jo yn jo foardiel brûke kinne?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Doelseleksje: Hokker spesifike kolleksje sille jo spegelje?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata skraapjen: Ynformaasje katalogisearje oer de triemmen, sûnder de (faak folle gruttere) triemmen sels te downloaden."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Gegevensseleksje: Op basis fan de metadata, bepale hokker gegevens it meast relevant binne om no te argivearjen. It kin alles wêze, mar faak is der in ridlike manier om romte en bandbreedte te besparjen."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Gegevens skraapjen: De gegevens echt krije."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Distribúsje: It ynpakken yn torrents, it oankundigje earne, minsken krije om it te fersprieden."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Dizze binne net folslein ûnôfhinklike fazen, en faak stjoere ynsjoggen út in lettere faze jo werom nei in eardere faze. Bygelyks, by it skraapjen fan metadata kinne jo realisearje dat it doel dat jo selektearre hawwe ferdigeningsmeganismen hat bûten jo feardigensnivo (lykas IP-blokken), dus geane jo werom en fine in oar doel."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domein seleksje / filosofy"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Der is gjin tekoart oan kennis en kultureel erfgoed om te rêden, wat oerweldigjend kin wêze. Dêrom is it faak nuttich om efkes stil te stean en te tinken oer wat jo bydrage kin wêze."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Elkenien hat in oare manier fan tinken oer dit, mar hjir binne wat fragen dy't jo josels stelle kinne:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Wêrom binne jo hjir ynteressearre yn? Wêr binne jo passy foar? As wy in groep minsken kinne krije dy't allegear de soarten dingen argivearje dy't se spesifyk soarch foar hawwe, soe dat in soad dekke! Jo sille folle mear witte as de gemiddelde persoan oer jo passy, lykas wat wichtige gegevens binne om te bewarjen, wat de bêste kolleksjes en online mienskippen binne, ensafuorthinne."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Hokker feardichheden hawwe jo dy't jo yn jo foardiel brûke kinne? Bygelyks, as jo in online befeiligingsekspert binne, kinne jo manieren fine om IP-blokken foar feilige doelen te ferslaan. As jo goed binne yn it organisearjen fan mienskippen, dan kinne jo miskien wat minsken byinoar bringe om in doel hinne. It is lykwols nuttich om wat programmeren te kennen, al is it mar foar it hâlden fan goede operasjonele feiligens troch dit proses."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hoefolle tiid hawwe jo hjirfoar? Us advys soe wêze om lyts te begjinnen en gruttere projekten te dwaan as jo der mear ûnderfining mei krije, mar it kin alles-omfiemjend wurde."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wat soe in hege-ynfloed gebiet wêze om op te fokusjen? As jo X oeren sille besteegje oan piratenargivearjen, hoe kinne jo dan de grutste \"bang for your buck\" krije?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wat binne unike manieren wêrop jo hjir oer tinke? Jo kinne wat ynteressante ideeën of oanpakken hawwe dy't oaren miskien mist hawwe."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Yn ús gefal soargen wy benammen oer de lange termyn bewarjen fan wittenskip. Wy wisten oer Library Genesis, en hoe't it folslein spegele waard mei torrents. Wy hâlden fan dat idee. Doe op in dei besocht ien fan ús wat wittenskiplike learboeken te finen op Library Genesis, mar koe se net fine, wat twifel brocht oer hoe folslein it echt wie. Wy sochten dy learboeken doe online, en fûnen se op oare plakken, wat it sied foar ús projekt plante. Sels foardat wy oer de Z-Library wisten, hiene wy it idee om net te besykjen al dy boeken manuell te sammeljen, mar te fokusjen op it spegeljen fan besteande kolleksjes, en se werom te dragen oan Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Doelseleksje"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Dus, wy hawwe ús gebiet dat wy besjogge, no hokker spesifike kolleksje sille wy spegelje? Der binne in pear dingen dy't in goed doel meitsje:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Grut"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unyk: net al goed dekt troch oare projekten."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Tagonklik: brûkt net in protte lagen fan beskerming om te foarkommen dat jo har metadata en gegevens skraapje."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Spesjale ynsjoch: jo hawwe wat spesjale ynformaasje oer dit doel, lykas dat jo op ien of oare manier spesjale tagong hawwe ta dizze kolleksje, of dat jo útfûn hawwe hoe't jo har ferdigening kinne oerwinne. Dit is net ferplichte (ús kommende projekt docht neat spesjaals), mar it helpt wis!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Doe't wy ús wittenskipsteksboeken op websiden oars as Library Genesis fûnen, besochten wy út te finen hoe't se op it ynternet kamen. Wy fûnen doe de Z-Library, en realisearren dat wylst de measte boeken net earst dêr ferskine, se úteinlik dêr einigje. Wy learden oer syn relaasje mei Library Genesis, en de (finansjele) stimulearstruktuer en superieure brûkersynterface, dy't beide it in folle folsleinere kolleksje makken. Wy diene doe wat foarriedige metadata en gegevensskraapjen, en realisearren dat wy om har IP-downloadlimiten hinne koene komme, troch ien fan ús leden syn spesjale tagong ta in protte proxyservers te brûken."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "As jo ferskate doelen ferkenne, is it al wichtich om jo spoaren te ferbergjen troch VPN's en wegwerp-e-postadressen te brûken, dêr't wy letter mear oer prate sille."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata skraapjen"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Litte wy hjir wat technysker wurde. Foar it feitlik skraapjen fan de metadata fan websiden, hawwe wy dingen frij ienfâldich hâlden. Wy brûke Python-skripts, soms curl, en in MySQL-database om de resultaten yn op te slaan. Wy hawwe gjin fancy skraapsoftware brûkt dy't komplekse websiden kin mappe, om't wy oant no ta allinich ien of twa soarten siden hoege te skraapjen troch gewoan troch id's te enumeratearjen en de HTML te parsjen. As der gjin maklik te enumeratearjen siden binne, dan kinne jo in goede crawler nedich hawwe dy't besiket alle siden te finen."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Foardat jo in hiele webside begjinne te skraapjen, besykje it earst in bytsje manuell te dwaan. Gean sels troch in pear tsientallen siden, om in gefoel te krijen foar hoe't dat wurket. Soms sille jo op dizze manier al tsjin IP-blokkearrings of oare ynteressante gedrach oanrinne. Itselde jildt foar gegevensskraapjen: foardat jo te djip yn dit doel geane, soargje derfoar dat jo syn gegevens effektyf kinne downloade."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Om beheiningen te omgean, binne der in pear dingen dy't jo kinne besykje. Binne der oare IP-adressen of servers dy't deselde gegevens hoste mar net deselde beheiningen hawwe? Binne der API-einpunten dy't gjin beheiningen hawwe, wylst oaren dat wol hawwe? By hokker downloadrate wurdt jo IP blokkearre, en foar hoe lang? Of wurde jo net blokkearre mar fertrage? Wat as jo in brûkersakkount oanmeitsje, hoe feroarje dingen dan? Kinne jo HTTP/2 brûke om ferbiningen iepen te hâlden, en fergruttet dat de snelheid wêrmei jo siden kinne oanfreegje? Binne der siden dy't meardere bestannen tagelyk listje, en is de ynformaasje dy't dêr listet genôch?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dingen dy't jo wierskynlik wolle bewarje omfetsje:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Bestânsnamme / lokaasje"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kin in ynterne ID wêze, mar ID's lykas ISBN of DOI binne ek nuttich."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Grutte: om te berekkenjen hoefolle skiifromte jo nedich hawwe."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): om te befestigjen dat jo it bestân goed downloade hawwe."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum tafoege/feroare: sadat jo letter weromkomme kinne en bestannen downloade dy't jo earder net downloade hawwe (hoewol jo faak ek de ID of hash hjirfoar brûke kinne)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beskriuwing, kategory, tags, auteurs, taal, ensfh."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Wy dogge dit typysk yn twa stadia. Earst downloade wy de rauwe HTML-bestannen, meast direkt yn MySQL (om in protte lytse bestannen te foarkommen, dêr't wy hjirûnder mear oer prate). Dan, yn in aparte stap, geane wy troch dy HTML-bestannen en parsearje se yn eigentlike MySQL-tabellen. Op dizze manier hoege jo net alles fanôf it begjin opnij te downloaden as jo in flater yn jo parsearkode ûntdekke, om't jo gewoan de HTML-bestannen mei de nije kode opnij kinne ferwurkje. It is ek faak makliker om de ferwurkingsstap te parallelisearjen, dus besparret wat tiid (en jo kinne de ferwurkingskode skriuwe wylst de skraapjen rint, ynstee fan beide stappen tagelyk te skriuwen)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Uteinlik, merk op dat foar guon doelen metadata-skrappen alles is wat der is. Der binne guon grutte metadata-kolleksjes dy't net goed bewarre wurde."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Data seleksje"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Faak kinne jo de metadata brûke om in ridlik subset fan gegevens te bepalen om te downloaden. Sels as jo úteinlik alle gegevens downloade wolle, kin it nuttich wêze om de wichtichste items earst te prioritearjen, foar it gefal dat jo ûntdutsen wurde en ferdigeningswurken ferbettere wurde, of om't jo mear skiifromte keapje moatte, of gewoan om't der wat oars yn jo libben opkomt foardat jo alles downloade kinne."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Bygelyks, in kolleksje kin meardere edysjes hawwe fan deselde ûnderlizzende boarne (lykas in boek of in film), wêrby't ien markearre is as de bêste kwaliteit. It soe in soad sin meitsje om dy edysjes earst te bewarjen. Jo wolle úteinlik miskien alle edysjes bewarje, om't yn guon gefallen de metadata ferkeard tagge kin wêze, of der kinne ûnbekende kompromissen wêze tusken edysjes (bygelyks, de \"bêste edysje\" kin yn de measte opsichten it bêste wêze, mar minder yn oare opsichten, lykas in film mei hegere resolúsje mar sûnder ûndertitels)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Jo kinne ek yn jo metadata-database sykje om ynteressante dingen te finen. Wat is it grutste bestân dat host wurdt, en wêrom is it sa grut? Wat is it lytste bestân? Binne der ynteressante of ûnferwachte patroanen as it giet om bepaalde kategoryen, talen, ensafuorthinne? Binne der dûbele of hiel ferlykbere titels? Binne der patroanen yn wannear't gegevens tafoege binne, lykas ien dei wêrop in protte bestannen tagelyk tafoege binne? Jo kinne faak in soad leare troch op ferskate manieren nei de dataset te sjen."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Yn ús gefal hawwe wy Z-Library boeken deduplisearre tsjin de md5-hashes yn Library Genesis, en dêrmei in soad downloadtiid en skiifromte besparre. Dit is lykwols in frij unike situaasje. Yn de measte gefallen binne der gjin wiidweidige databases fan hokker bestannen al goed bewarre wurde troch oare piraten. Dit is op himsels in grutte kâns foar immen dêr bûten. It soe geweldich wêze om in regelmjittich bywurke oersjoch te hawwen fan dingen lykas muzyk en films dy't al breed ferspraat binne op torrent-websides, en dêrom fan legere prioriteit binne om op te nimmen yn piratemirrens."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Data skrappen"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "No binne jo klear om de gegevens yn bulk te downloaden. Sa't earder neamd, moatte jo op dit punt al in hantsjefol bestannen manuell downloade hawwe, om it gedrach en de beheiningen fan it doel better te begripen. Mar der sille noch ferrassingen foar jo wêze as jo ienris in protte bestannen tagelyk downloade."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Us advys hjir is foaral om it simpel te hâlden. Begjin gewoan mei it downloaden fan in hantsjefol bestannen. Jo kinne Python brûke, en dan útwreidzje nei meardere threads. Mar soms is it sels ienfâldiger om Bash-bestannen direkt út de database te generearjen, en dan meardere fan harren yn meardere terminalfinsters útfiere om op te skalen. In rappe technyske trúk dy't hjir it neamen wurdich is, is it brûken fan OUTFILE yn MySQL, dy't jo oeral skriuwe kinne as jo \"secure_file_priv\" útskeakelje yn mysqld.cnf (en soargje derfoar dat jo ek AppArmor útskeakelje/overriden as jo op Linux binne)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Wy bewarje de gegevens op ienfâldige hurde skiiven. Begjin mei wat jo hawwe, en breid stadichoan út. It kin oerweldigjend wêze om te tinken oer it bewarjen fan hûnderten TB's oan gegevens. As dat de situaasje is dêr't jo foar steane, set dan earst in goed subset út, en yn jo oankundiging freegje om help by it bewarjen fan de rest. As jo sels mear hurde skiiven wolle krije, dan hat r/DataHoarder guon goede boarnen foar it krijen fan goede deals."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Besykje net te folle soargen te meitsjen oer fancy bestânsystemen. It is maklik om yn it kninegat te fallen fan it opsetten fan dingen lykas ZFS. Ien technysk detail om bewust fan te wêzen is lykwols dat in protte bestânsystemen net goed omgean mei in protte bestannen. Wy hawwe fûn dat in ienfâldige oplossing is om meardere mappen te meitsjen, bygelyks foar ferskillende ID-berik of hash-prefixen."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Nei it downloaden fan de gegevens, soargje derfoar dat jo de yntegriteit fan de bestannen kontrolearje mei hashes yn de metadata, as dy beskikber binne."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Distribúsje"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Jo hawwe de gegevens, en dêrmei hawwe jo (meast wierskynlik) it earste piratemirror fan jo doel yn 'e wrâld. Op in protte manieren is it dreechste diel foarby, mar it riskantste diel leit noch foar jo. Nei alle gedachten hawwe jo oant no ta stealth west; ûnder de radar fleane. Alles wat jo dwaan moasten wie in goede VPN brûke, gjin persoanlike details ynfolje yn formulieren (duh), en miskien in spesjale browsersesje brûke (of sels in oar kompjûter)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "No moatte jo de gegevens distribuearje. Yn ús gefal woene wy earst de boeken werom bydrage oan Library Genesis, mar ûntdutsen doe gau de swierrichheden dêryn (fiksje tsjin non-fiksje sortearring). Dus besleaten wy om distribúsje te brûken mei Library Genesis-styl torrents. As jo de kâns hawwe om by te dragen oan in besteand projekt, dan kin dat jo in soad tiid besparje. Mar der binne net in protte goed organisearre piratemirrens op it stuit."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Lit ús sizze dat jo beslute om sels torrents te distribuearjen. Besykje dy bestannen lyts te hâlden, sadat se maklik te spegeljen binne op oare websiden. Jo sille dan de torrents sels seedje moatte, wylst jo anonym bliuwe. Jo kinne in VPN brûke (mei of sûnder poart foarútstjoering), of betelje mei tumbled Bitcoins foar in Seedbox. As jo net witte wat guon fan dy termen betsjutte, sille jo in protte lêze moatte, om't it wichtich is dat jo de risiko-kompromissen hjir begripe."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Jo kinne de torrentbestannen sels hoste op besteande torrentwebsiden. Yn ús gefal keazen wy om eins in webside te hosten, om't wy ek ús filosofy op in dúdlike manier ferspriede woene. Jo kinne dit sels op in ferlykbere manier dwaan (wy brûke Njalla foar ús domeinen en hosting, betelle mei tumbled Bitcoins), mar fiel jo frij om kontakt mei ús op te nimmen om ús jo torrents te hosten. Wy sjogge dernei út om oer tiid in wiidweidich yndeks fan piratemirrens te bouwen, as dit idee oanslacht."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Wat VPN-seleksje oanbelanget, is hjir al in protte oer skreaun, dus wy sille gewoan it algemiene advys werhelje om te kiezen op reputaasje. Wierlike rjochtbank-testte gjin-log-belied mei lange track records fan privacybeskerming is de opsje mei it leechste risiko, yn ús miening. Merk op dat sels as jo alles goed dogge, kinne jo nea nei nul risiko komme. Bygelyks, by it seedjen fan jo torrents, kin in heech motivearre steatsakteur wierskynlik nei ynkommende en útgeande gegevensstreamen foar VPN-tsjinners sjen, en dedusearje wa't jo binne. Of jo kinne gewoan op ien of oare manier in flater meitsje. Wy hawwe dat wierskynlik al dien, en sille it wer dwaan. Gelokkich, steaten skele <em>dat</em> net sa folle oer piraterij."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Ien beslút om te meitsjen foar elk projekt, is oft it te publisearjen mei deselde identiteit as earder, of net. As jo deselde namme bliuwe brûken, dan kinne flaters yn operasjonele feiligens fan eardere projekten weromkomme om jo te biten. Mar publisearje ûnder ferskillende nammen betsjut dat jo gjin langere reputaasje opbouwe. Wy keazen om sterke operasjonele feiligens fan it begjin ôf te hawwen, sadat wy deselde identiteit bliuwe kinne brûken, mar wy sille net twifelje om ûnder in oare namme te publisearjen as wy in flater meitsje of as de omstannichheden dêr om freegje."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "It wurd derút krije kin lestich wêze. Sa't wy seine, is dit noch in niche-mienskip. Wy hawwe oarspronklik op Reddit pleatst, mar krigen echt traksje op Hacker News. Foar no is ús oanbefelling om it op in pear plakken te pleatsen en te sjen wat der bart. En nochris, nim kontakt mei ús op. Wy soene graach it wurd ferspriede wolle fan mear piraten-archivisme-ynspanningen."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Konklúzje"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hjir hoopje wy dat dit nuttich is foar nij begjinnende piraten-argivisten. Wy binne entûsjast om jo yn dizze wrâld te ferwolkomjen, dus aarzel net om kontakt op te nimmen. Litte wy safolle mooglik fan 'e kennis en kultuer fan' e wrâld bewarje en it oeral ferspriede."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Yntroduksje fan de Pirate Library Mirror: Bewarjen fan 7TB oan boeken (dy't net yn Libgen steane)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dit projekt (EDIT: ferhuze nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>) hat as doel by te dragen oan it bewarjen en befrijen fan minsklike kennis. Wy meitsje ús lytse en beskieden bydrage, yn de fuotleasten fan de grutten foar ús."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "De fokus fan dit projekt wurdt yllustrearre troch syn namme:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piraat</strong> - Wy skeine bewust de auteurswet yn de measte lannen. Dit stelt ús yn steat om wat te dwaan dat legale entiteiten net kinne: derfoar soargje dat boeken oeral ferspraat wurde."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Bibleteek</strong> - Lykas de measte bibleteken rjochtsje wy ús benammen op skreaune materialen lykas boeken. Wy kinne yn 'e takomst útwreidzje nei oare soarten media."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spegel</strong> - Wy binne strikt in spegel fan besteande bibleteken. Wy rjochtsje ús op bewarjen, net op it maklik trochsykber en downloadber meitsjen fan boeken (tagong) of it stimulearjen fan in grutte mienskip fan minsken dy't nije boeken bydrage (boarnen)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "De earste bibleteek dy't wy spegele hawwe is Z-Library. Dit is in populêre (en yllegale) bibleteek. Se hawwe de Library Genesis-kolleksje nommen en dy maklik trochsykber makke. Dêrneist binne se tige effektyf wurden yn it oanlûken fan nije boekbydragen, troch bydrage brûkers te stimulearjen mei ferskate foardielen. Se drage op it stuit dizze nije boeken net werom oan Library Genesis. En oars as Library Genesis, meitsje se har kolleksje net maklik spegelber, wat brede bewarjen foarkomt. Dit is wichtich foar har bedriuwsmodel, om't se jild freegje foar tagong ta har kolleksje yn bulk (mear as 10 boeken per dei)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Wy meitsje gjin morele oardielen oer it yn rekken bringen fan jild foar bulk tagong ta in yllegale boekekolleksje. It is bûten mis dat de Z-Bibleteek suksesfol west hat yn it útwreidzjen fan tagong ta kennis, en mear boeken te finen. Wy binne hjir gewoan om ús diel te dwaan: it soargjen foar de lange termyn behâld fan dizze priveekolleksje."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Wy wolle jo útnûgje om te helpen minsklike kennis te bewarjen en te befrijen troch ús torrents te downloaden en te seedjen. Sjoch de projektpagina foar mear ynformaasje oer hoe't de gegevens organisearre binne."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Wy wolle jo ek graach útnûgje om jo ideeën by te dragen oer hokker kolleksjes as folgjende spegele wurde moatte, en hoe't wy dat oanpakke kinne. Tegearre kinne wy in soad berikke. Dit is mar in lytse bydrage ûnder tal fan oaren. Tankewol, foar alles wat jo dogge."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Wy keppelje net nei de bestannen fan dizze blog. Fyn it sels.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb dump, of Hoefolle Boeken Bliuwe Foar Ivich Bewarre?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "As wy de bestannen fan skaadbibleteken goed deduplikearje soene, hokker persintaazje fan alle boeken yn 'e wrâld hawwe wy bewarre?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Mei de Pirate Library Mirror (EDIT: ferhuze nei <a %(wikipedia_annas_archive)s>Anna’s Argyf</a>), is ús doel om alle boeken yn 'e wrâld te nimmen en se foar ivich te bewarjen.<sup>1</sup> Tusken ús Z-Library torrents en de orizjinele Library Genesis torrents hawwe wy 11.783.153 bestannen. Mar hoefolle is dat eins? As wy dy bestannen goed deduplikearje soene, hokker persintaazje fan alle boeken yn 'e wrâld hawwe wy bewarre? Wy soene echt graach sa'n ding hawwe:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of it skreaune erfgoed fan 'e minskheid foar ivich bewarre"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Foar in persintaazje hawwe wy in noemer nedich: it totale oantal boeken dat ea publisearre is.<sup>2</sup> Foar de ûndergong fan Google Books, besocht in yngenieur op it projekt, Leonid Taycher, <a %(booksearch_blogspot)s>dit oantal te skatten</a>. Hy kaam - mei in knypeach - út op 129.864.880 (\"teminsten oant snein\"). Hy skatte dit oantal troch in unifoarme databank fan alle boeken yn 'e wrâld te bouwen. Hjirfoar luts hy ferskate datasets gear en fusearre se op ferskate manieren."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "As in koarte sydnoat, is der in oar persoan dy't besocht hat om alle boeken yn 'e wrâld te katalogisearjen: Aaron Swartz, de ferstoarne digitale aktivist en Reddit mei-oprjochter.<sup>3</sup> Hy <a %(youtube)s>begûn Open Library</a> mei it doel fan “ien webside foar elk boek dat ea publisearre is”, troch gegevens fan ferskate boarnen te kombinearjen. Hy betelle úteinlik de ultime priis foar syn wurk oan digitale bewarjen doe't hy ferfolge waard foar it massaal downloaden fan akademyske papieren, wat late ta syn selsmoard. It is oerstallich te sizzen dat dit ien fan 'e redenen is wêrom't ús groep pseudonym is, en wêrom't wy tige foarsichtich binne. Open Library wurdt noch altyd heroïsk rûn troch minsken by it Internet Archive, dy't Aaron syn erfskip fuortsette. Wy komme hjir letter yn dizze post op werom."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Yn de Google blogpost beskriuwt Taycher guon fan 'e útdagings by it skatten fan dit oantal. Earst, wat foarmet in boek? Der binne in pear mooglike definysjes:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fysike eksimplaren.</strong> Fansels is dit net hiel nuttich, om't se gewoan duplikaat binne fan itselde materiaal. It soe moai wêze as wy alle annotaasjes dy't minsken yn boeken meitsje, lykas Fermat syn ferneamde “krabbels yn 'e marzjes”, koene bewarje. Mar spitigernôch sil dat in dream fan in argivaris bliuwe."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Wurken”.</strong> Bygelyks “Harry Potter en de Geheime Keamer” as in logysk konsept, dat alle ferzjes dêrfan omfettet, lykas ferskate oersettingen en opnijprintsjes. Dit is in soarte fan nuttige definysje, mar it kin dreech wêze om de line te lûken fan wat telt. Bygelyks, wy wolle wierskynlik ferskate oersettingen bewarje, hoewol opnijprintsjes mei allinich lytse ferskillen miskien net sa wichtich binne."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edysjes”.</strong> Hjir telle jo elke unike ferzje fan in boek. As der wat oars oan is, lykas in oar omslach of in oare foarwurd, telt it as in oare edysje."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Bestannen.</strong> By it wurkjen mei skaadbiblioteken lykas Library Genesis, Sci-Hub, of Z-Library, is der in ekstra oerweging. Der kinne meardere scans fan deselde edysje wêze. En minsken kinne bettere ferzjes meitsje fan besteande bestannen, troch de tekst te scannen mei OCR, of siden te korrizjearjen dy't ûnder in hoeke skand binne. Wy wolle dizze bestannen allinich telle as ien edysje, wat goede metadata fereasket, of deduplikearring mei help fan dokumintgelikensensmjittingen."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edysjes” lykje de meast praktyske definysje fan wat “boeken” binne. Gelokkich wurdt dizze definysje ek brûkt foar it tawizen fan unike ISBN-nûmers. In ISBN, of International Standard Book Number, wurdt faak brûkt foar ynternasjonale hannel, om't it yntegrearre is mei it ynternasjonale barkoade-systeem (”International Article Number”). As jo in boek yn winkels ferkeapje wolle, hat it in barkoade nedich, dus krije jo in ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher syn blogpost neamt dat wylst ISBNs nuttich binne, se net universeel binne, om't se pas echt yn 'e midden fan 'e santiger jierren oannommen waarden, en net oeral yn 'e wrâld. Dochs is ISBN wierskynlik de meast brûkte identifier fan boekedysjes, dus it is ús bêste startpunt. As wy alle ISBNs yn 'e wrâld fine kinne, krije wy in nuttige list fan hokker boeken noch bewarre wurde moatte."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Dus, wêr krije wy de gegevens? Der binne in oantal besteande ynspanningen dy't besykje in list fan alle boeken yn 'e wrâld gear te stallen:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Uteinlik hawwe se dit ûndersyk dien foar Google Books. Mar har metadata is net tagonklik yn bulk en frij dreech te skraapjen."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Sa't earder neamd, is dit harren hiele missy. Se hawwe grutte hoemannichten biblioteekgegevens fan meiwurkende biblioteken en nasjonale argiven boarne, en dogge dat noch altyd. Se hawwe ek frijwillige bibliotekaressen en in technysk team dat besiket records te deduplikearjen, en se te labeljen mei allerhanne metadata. It bêste fan alles is dat harren dataset folslein iepen is. Jo kinne it gewoan <a %(openlibrary)s>downloade</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dit is in webside rûn troch de non-profit OCLC, dy't biblioteekbehearsystemen ferkeapet. Se aggregearje boekmetadata fan in protte biblioteken, en meitsje it beskikber fia de WorldCat-webside. Mar se fertsjinje ek jild troch dizze gegevens te ferkeapjen, dus it is net beskikber foar bulk download. Se hawwe wol guon mear beheinde bulk datasets beskikber foar download, yn gearwurking mei spesifike biblioteken."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dit is it ûnderwerp fan dizze blogpost. ISBNdb skraapt ferskate websiden foar boekmetadata, benammen priisgegevens, dy't se dan ferkeapje oan boekferkeapers, sadat se harren boeken kinne priisje yn oerienstimming mei de rest fan 'e merk. Om't ISBNs tsjintwurdich frij universeel binne, hawwe se effektyf in “webside foar elk boek” boud."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Ferskate yndividuele biblioteeksystemen en argiven.</strong> Der binne biblioteken en argiven dy't net yndeksearre en aggregearre binne troch ien fan 'e hjirboppe neamde, faak om't se ûnderfinansearre binne, of om oare redenen net harren gegevens diele wolle mei Open Library, OCLC, Google, ensafuorthinne. In protte fan dizze hawwe wol digitale records tagonklik fia it ynternet, en se binne faak net hiel goed beskerme, dus as jo wolle helpe en wat wille hawwe mei it learen oer frjemde biblioteeksystemen, binne dizze geweldige startpunten."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Yn dizze post binne wy bliid om in lytse release oan te kundigjen (yn ferliking mei ús foarige Z-Library releases). Wy hawwe it measte fan ISBNdb skraapt, en de gegevens beskikber makke foar torrenting op 'e webside fan de Pirate Library Mirror (EDIT: ferpleatst nei <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>; wy sille it hjir net direkt keppelje, sykje der gewoan nei). Dit binne sawat 30,9 miljoen records (20GB as <a %(jsonlines)s>JSON Lines</a>; 4,4GB gzipped). Op harren webside beweare se dat se eins 32,6 miljoen records hawwe, dus wy kinne miskien wat mist hawwe, of <em>sy</em> kinne wat ferkeard dwaan. Hoe dan ek, foar no sille wy net krekt diele hoe't wy it dien hawwe — wy litte dat as in oefening foar de lêzer. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wat wy wol diele sille is wat foarriedige analyze, om tichterby te kommen by it skatten fan it oantal boeken yn 'e wrâld. Wy hawwe nei trije datasets sjoen: dizze nije ISBNdb dataset, ús orizjinele release fan metadata dy't wy skraapt hawwe fan 'e Z-Library skaadbiblioteek (dy't Library Genesis omfettet), en de Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Litte wy begjinne mei wat rûge sifers:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Yn sawol Z-Library/Libgen as Open Library binne der folle mear boeken as unike ISBNs. Betsjut dit dat in protte fan dy boeken gjin ISBNs hawwe, of is de ISBN metadata gewoan ûntbrekkend? Wy kinne dizze fraach wierskynlik beantwurdzje mei in kombinaasje fan automatyske oerienkomst basearre op oare attributen (titel, auteur, útjouwer, ensfh.), mear gegevensboarnen ynlûke, en ISBNs út 'e eigentlike boekscans sels helje (yn it gefal fan Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Hoefolle fan dy ISBNs binne unyk? Dit wurdt it bêste yllustrearre mei in Venn-diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Om mear presys te wêzen:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Wy wiene ferrast troch hoefolle oerlaap der eins is! ISBNdb hat in enoarme hoemannichte ISBN's dy't net ferskine yn Z-Library of Open Library, en itselde jildt (yn in lytsere mar noch substansjele mjitte) foar de oare twa. Dit ropt in protte nije fragen op. Hoefolle soe automatyske oerienkomst helpe by it taggen fan de boeken dy't net mei ISBN's tagge wiene? Soe der in protte oerienkomsten wêze en dêrtroch mear oerlaap? Ek, wat soe der barre as wy in 4e of 5e dataset ynbringe? Hoefolle oerlaap soene wy dan sjen?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dit jout ús in startpunt. Wy kinne no sjen nei alle ISBN's dy't net yn de Z-Library dataset wiene, en dy't ek net oerienkomme mei titel/skriuwer fjilden. Dat kin ús in hânfet jaan op it bewarjen fan alle boeken yn 'e wrâld: earst troch it ynternet te skrabjen foar scans, dan troch yn it echte libben boeken te scannen. It lêste kin sels crowd-funded wurde, of oandreaun troch \"bounties\" fan minsken dy't graach spesifike boeken digitalisearre sjogge. Al dat is in ferhaal foar in oare kear."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "As jo mei ien fan dizze dingen helpe wolle — fierdere analyze; mear metadata skrabje; mear boeken fine; boeken OCR'e; dit dwaan foar oare domeinen (bygelyks papers, audioboeken, films, tv-shows, tydskriften) of sels guon fan dizze gegevens beskikber meitsje foar dingen lykas ML / grutte taalmodel training — nim dan kontakt mei my op (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "As jo spesifyk ynteressearre binne yn de data-analyze, wurkje wy oan it beskikber meitsjen fan ús datasets en skripts yn in makliker te brûken formaat. It soe geweldich wêze as jo gewoan in notysjeboek kinne forkje en hjirmei begjinne te boartsjen."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Uteinlik, as jo dit wurk stypje wolle, beskôgje dan in donaasje te meitsjen. Dit is in folslein frijwilligersrûne operaasje, en jo bydrage makket in grut ferskil. Elke bytsje helpt. Foar no nimme wy donaasje yn crypto; sjoch de Donear-side op Anna’s Argiven."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Foar in ridlike definysje fan \"foar ivich\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Fansels is it skreaune erfgoed fan 'e minskheid folle mear as boeken, benammen tsjintwurdich. Foar it doel fan dizze post en ús resinte releases rjochtsje wy ús op boeken, mar ús ynteresses geane fierder."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Der is folle mear te sizzen oer Aaron Swartz, mar wy woene him gewoan koart neame, om't hy in krúsjale rol spilet yn dit ferhaal. As de tiid ferrint, kinne mear minsken syn namme foar it earst tsjinkomme, en kinne se dêrnei sels yn it konijnenhol dûke."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "It krityske finster fan skaadbiblioteken"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Hoe kinne wy bewearje ús kolleksjes foar ivich te bewarjen, wylst se al tichtby 1 PB komme?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Sineeske ferzje 中文版</a>, diskusjearje op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "By Anna’s Argiven wurde wy faak frege hoe't wy kinne beweare ús kolleksjes foar ivich te bewarjen, wylst de totale grutte al tichtby 1 Petabyte (1000 TB) is, en noch groeit. Yn dit artikel sille wy nei ús filosofy sjen, en sjen wêrom't it folgjende desennium kritysk is foar ús missy om de kennis en kultuer fan 'e minskheid te bewarjen."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "De <a %(annas_archive_stats)s>totale grutte</a> fan ús kolleksjes, oer de lêste pear moannen, ferdield troch it oantal torrent seeders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioriteiten"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Wêrom soargje wy sa folle oer papers en boeken? Litte wy ús fûnemintele leauwen yn bewarjen yn it algemien oan 'e kant sette — wy kinne dêr in oare post oer skriuwe. Dus wêrom spesifyk papers en boeken? It antwurd is simpel: <strong>ynformaasjetichtheid</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte opslach, bewarret skreaune tekst de measte ynformaasje fan alle media. Wylst wy soargje foar sawol kennis as kultuer, soargje wy mear foar it earste. Yn it algemien fine wy in hiërargy fan ynformaasjetichtheid en belang fan bewarjen dy't der sa útsjocht:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademyske papers, tydskriften, rapporten"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organyske gegevens lykas DNA-sekwinsjes, plantesied, of mikrobiale samples"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Non-fiksje boeken"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Wittenskip & yngenieurswittenskip softwarekoade"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Mjitgegevens lykas wittenskiplike mjittingen, ekonomyske gegevens, bedriuwsrapporten"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Wittenskip & yngenieurswittenskip websiden, online diskusjes"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Non-fiksje tydskriften, kranten, hantliedingen"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Non-fiksje transkripsjes fan lêzingen, dokumintêres, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Ynterne gegevens fan bedriuwen of oerheden (lekkens)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata records yn it algemien (fan non-fiksje en fiksje; fan oare media, keunst, minsken, ensfh.; ynklusyf resinsjes)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografyske gegevens (bgl. kaarten, geologyske ûndersiken)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkripsjes fan juridyske of rjochtbankprosedueres"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiksjonele of ferdivedaasjeferzjes fan al it boppesteande"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "De ranglist yn dizze list is wat arbitêr - ferskate items binne lyk of hawwe ûnienigens binnen ús team - en wy ferjitte wierskynlik guon wichtige kategoryen. Mar dit is rûchwei hoe't wy prioritearje."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Guon fan dizze items binne te oars foar ús om ús soargen oer te meitsjen (of wurde al fersoarge troch oare ynstellingen), lykas organyske gegevens of geografyske gegevens. Mar de measte items yn dizze list binne eins wichtich foar ús."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "In oare grutte faktor yn ús priorisearring is hoefolle risiko in bepaald wurk rint. Wy leaver fokusje op wurken dy't:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Seldsum"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unyk ûnderfokusearre"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unyk yn gefaar fan ferneatiging (bgl. troch oarloch, besunigings, rjochtsaken, of politike ferfolging)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Uteinlik, wy soargje oer skaal. Wy hawwe beheinde tiid en jild, dus wy besteegje leaver in moanne oan it rêden fan 10.000 boeken dan 1.000 boeken - as se sawat like weardefol en yn gefaar binne."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Skaadbiblioteken"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Der binne in protte organisaasjes dy't ferlykbere missys hawwe, en ferlykbere prioriteiten. Yndie, der binne biblioteken, argiven, laboratoaria, musea, en oare ynstellingen dy't belêstge binne mei it behâld fan dit soarte. In protte fan dy binne goed finansierd, troch oerheden, yndividuen, of bedriuwen. Mar se hawwe ien grutte bline flek: it juridyske systeem."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hjiryn leit de unike rol fan skaadbiblioteken, en de reden wêrom't Anna's Argive bestiet. Wy kinne dingen dwaan dy't oare ynstellingen net meie. No, it is net (faak) dat wy materialen argivearje kinne dy't yllegaal binne om oars te behâlden. Nee, it is yn in protte plakken legaal om in argyf te bouwen mei alle boeken, papieren, tydskriften, ensfh."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Mar wat juridyske argiven faak misse is <strong>redundânsje en duorsumens</strong>. Der binne boeken wêrfan't mar ien eksimplaar bestiet yn in fysike bibleteek earne. Der binne metadata-records dy't troch ien inkele korporaasje bewarre wurde. Der binne kranten dy't allinnich op mikrofilm yn in inkele argyf bewarre wurde. Bibleteken kinne finansiering ferlieze, korporaasjes kinne fallyt gean, argiven kinne bombardearre en ferbaarnd wurde. Dit is net hypotetysk — dit bart hieltyd."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "It ding dat wy unyk dwaan kinne by Anna’s Argiven is in protte kopyen fan wurken opslaan, op grutte skaal. Wy kinne papieren, boeken, tydskriften en mear sammelje en se yn bulk ferspriede. Wy dogge dit op it stuit fia torrents, mar de krekte technologyen meitsje net út en sille oer tiid feroarje. It wichtichste diel is it fersprieden fan in protte kopyen oer de hiele wrâld. Dit sitaat fan mear as 200 jier lyn is noch altyd wier:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>It ferlerne kin net weromfûn wurde; mar lit ús bewarje wat oerbliuwt: net troch kluzen en slotten dy't se fan it publyk each en gebrûk ôfskermje, yn it oerjaan oan de slop fan 'e tiid, mar troch sa'n fermannichfâldiging fan kopyen, dat se bûten it berik fan tafal pleatst wurde.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "In koarte opmerking oer it iepenbier domein. Om't Anna’s Argiven unyk rjochte is op aktiviteiten dy't yllegaal binne yn in protte plakken oer de hiele wrâld, meitsje wy ús gjin soargen oer breed beskikbere kolleksjes, lykas boeken yn it iepenbier domein. Juridyske entiteiten soargje dêr faak al goed foar. Lykwols, der binne oerwagings dy't ús soms meitsje wurkje oan iepenbier beskikbere kolleksjes:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata-records kinne frij besjoen wurde op de Worldcat-webside, mar net yn bulk downloade wurde (oant wy se <a %(worldcat_scrape)s>skraapten</a>)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Koade kin iepen boarne wêze op Github, mar Github as gehiel kin net maklik spegele wurde en dus bewarre wurde (hoewol yn dit spesifike gefal der genôch ferspraat kopyen binne fan de measte koade repositories)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit is fergees te brûken, mar hat koartlyn strange anty-skraapmaatregels ynfierd, yn it ljocht fan data-hongerige LLM-training (mear dêroer letter)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "In fermannichfâldiging fan kopyen"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Werom nei ús oarspronklike fraach: hoe kinne wy oanspraak meitsje op it bewarjen fan ús kolleksjes foar ivich? It haadprobleem hjir is dat ús kolleksje <a %(torrents_stats)s>rap groeit</a>, troch it skraapjen en iepenboarnen fan guon massale kolleksjes (boppe op it geweldige wurk dat al dien is troch oare iepen-data skaadbibleteken lykas Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Dizze groei yn gegevens makket it dreger foar de kolleksjes om oer de hiele wrâld spegele te wurden. Gegevensopslach is djoer! Mar wy binne optimistysk, foaral as wy de folgjende trije trends observearje."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Wy hawwe de leechhingjende fruchten plukt"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Dizze folget direkt út ús prioriteiten dy't hjirboppe besprutsen binne. Wy leaver wurkje oan it befrijen fan grutte kolleksjes earst. No't wy guon fan de grutste kolleksjes yn 'e wrâld feilich steld hawwe, ferwachtsje wy dat ús groei folle stadiger sil wêze."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Der is noch in lange sturt fan lytsere kolleksjes, en nije boeken wurde elke dei skand of publisearre, mar it tempo sil wierskynlik folle stadiger wêze. Wy kinne noch dûbel of sels trijefâldich yn grutte wurde, mar oer in langere perioade."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Opslachkosten bliuwe eksponentieel sakje"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Op it momint fan skriuwen binne <a %(diskprices)s>skiifprizen</a> per TB sawat $12 foar nije skiiven, $8 foar brûkte skiiven, en $4 foar tape. As wy konservatyf binne en allinnich nei nije skiiven sjogge, betsjut dat dat it opslaan fan in petabyte sawat $12,000 kostet. As wy oannimme dat ús bibleteek trijefâldich sil groeie fan 900TB nei 2.7PB, soe dat betsjutte $32,400 om ús hiele bibleteek te spegeljen. Mei elektrisiteit, kosten fan oare hardware, en sa fierder, litte wy it rûn meitsje op $40,000. Of mei tape mear as $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Oan de iene kant is <strong>$15,000–$40,000 foar de som fan alle minsklike kennis in koopje</strong>. Oan de oare kant is it in bytsje steil om tonnen folsleine kopyen te ferwachtsjen, foaral as wy ek wolle dat dy minsken har torrents trochgean te seedjen foar it foardiel fan oaren."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dat is hjoed. Mar foarútgong giet troch:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Hurde skiifkosten per TB binne rûchwei yn trijeën dield oer de lêste 10 jier, en sille wierskynlik op in ferlykber tempo trochgean te sakjen. Tape liket op in ferlykbere trajekt te wêzen. SSD-prizen sakje noch flugger, en kinne oan 'e ein fan it desennium de HDD-prizen oernimme."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-prizen trends fan ferskate boarnen (klik om de stúdzje te besjen)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "As dit hâldt, dan kinne wy oer 10 jier mar $5,000–$13,000 nedich hawwe om ús hiele kolleksje te spegeljen (1/3e), of sels minder as wy minder yn grutte groeie. Wylst noch in protte jild, sil dit berikber wêze foar in protte minsken. En it kin sels better wêze fanwegen it folgjende punt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Ferbetteringen yn ynformaasjetichtens"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Wy bewarje op it stuit boeken yn de rauwe formaten dy't se oan ús jûn wurde. Wis, se binne komprimearre, mar faak binne it noch grutte scans of foto's fan siden."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Oant no ta wiene de iennige opsjes om de totale grutte fan ús kolleksje te ferminderjen mear agressive kompresje of deduplikearring. Mar om genôch besparring te krijen, binne beide te ferliesryk foar ús smaak. Swiere kompresje fan foto's kin tekst amper lêsber meitsje. En deduplikearring fereasket hege fertrouwen dat boeken presys itselde binne, wat faak te ûnkrekt is, benammen as de ynhâld itselde is, mar de scans op ferskillende mominten makke binne."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Der hat altyd in tredde opsje west, mar de kwaliteit wie sa min dat wy it nea beskôge hawwe: <strong>OCR, of Optyske Karaktererkenning</strong>. Dit is it proses fan it konvertearjen fan foto's nei gewoane tekst, troch AI te brûken om de karakters yn de foto's te detektearjen. Ark foar dit besteane al lang, en binne aardich goed, mar \"aardich goed\" is net genôch foar bewaringsdoelen."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Lykwols hawwe resinte multi-modale djip-learmodellen ekstreem rappe foarútgong makke, hoewol noch tsjin hege kosten. Wy ferwachtsje dat sawol de krektens as de kosten dramatysk sille ferbetterje yn de kommende jierren, oant it punt dat it realistysk wurdt om it op ús hiele bibleteek ta te passen."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Ferbetteringen yn OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "As dat bart, sille wy wierskynlik noch de orizjinele bestannen bewarje, mar derneist kinne wy in folle lytsere ferzje fan ús bibleteek hawwe dy't de measte minsken spegelje wolle. It moaie is dat rauwe tekst sels noch better komprimearret, en folle makliker te deduplikearjen is, wat ús noch mear besparring jout."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Yn it algemien is it net ûnrealistysk om teminsten in 5-10x reduksje yn totale bestânsgrutte te ferwachtsjen, miskien sels mear. Sels mei in konservative 5x reduksje, soene wy sjogge nei <strong>$1,000–$3,000 yn 10 jier sels as ús bibleteek trijefâldich groeit</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritike finster"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "As dizze prognosen krekt binne, moatte wy <strong>gewoan in pear jier wachtsje</strong> foardat ús hiele kolleksje breed spegele wurdt. Sa, yn de wurden fan Thomas Jefferson, \"pleatst bûten it berik fan tafal.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Spitigernôch hat de komst fan LLM's, en harren data-hongerige training, in protte auteursrjochtshâlders op de ferdigening set. Noch mear as se al wiene. In protte websiden meitsje it dreger om te skraapjen en te argivearjen, rjochtsaken fleane om, en ûnderwilens wurde fysike bibleteken en argiven trochgean negearre."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Wy kinne allinnich ferwachtsje dat dizze trends trochgean te fersmoarjen, en in protte wurken ferlern gean lang foardat se it publike domein yngeane."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Wy steane op de foarjûn fan in revolúsje yn bewarjen, mar <q>it ferlerne kin net weromfûn wurde.</q></strong> Wy hawwe in krityk finster fan sawat 5-10 jier wêryn it noch frij djoer is om in skaadbibleteek te operearjen en in protte spegels oer de wrâld te meitsjen, en wêryn tagong noch net folslein ôfsletten is."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "As wy dit finster kinne oerbrêgje, dan hawwe wy yndie de kennis en kultuer fan 'e minskheid foar ivich bewarre. Wy moatte dizze tiid net fergrieme. Wy moatte net tastean dat dit krityk finster foar ús slút."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Litte wy gean."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Eksklusyf tagong foar LLM-bedriuwen ta de grutste Sineeske non-fiksje boekekolleksje yn 'e wrâld"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Sineeske ferzje 中文版</a>, <a %(news_ycombinator)s>Diskusjearje op Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna's Argive hat in unike kolleksje fan 7,5 miljoen / 350TB Sineeske non-fiksje boeken oernommen — grutter as Library Genesis. Wy binne ree om in LLM-bedriuw eksklusive tagong te jaan, yn ruil foar heechweardige OCR en tekstekstraksje.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dit is in koart blogberjocht. Wy sykje in bedriuw of ynstelling om ús te helpen mei OCR en tekstekstraksje foar in massale kolleksje dy't wy oernommen hawwe, yn ruil foar eksklusive iere tagong. Nei de embargo-perioade sille wy fansels de hiele kolleksje frijjaan."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Heechweardich akademyske teksten binne tige nuttich foar de training fan LLMs. Hoewol ús kolleksje Sineesk is, kin dit sels nuttich wêze foar de training fan Ingelske LLMs: modellen lykje konsepten en kennis te enkodearjen ûnôfhinklik fan de boarnetaal."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Hjirfoar moat tekst út de scans helle wurde. Wat krijt Anna's Argiven hjirút? Folsleine tekstsykjen fan de boeken foar har brûkers."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Om't ús doelen oerienkomme mei dy fan LLM-ûntwikkelders, sykje wy in gearwurker. Wy binne ree om jo <strong>eksklusive betide tagong ta dizze kolleksje yn bulk foar 1 jier</strong> te jaan, as jo goede OCR en tekstekstraksje dwaan kinne. As jo ree binne om de hiele koade fan jo pipeline mei ús te dielen, soene wy ree wêze om de kolleksje langer te embargoearjen."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Foarbyldsiden"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Om ús te bewizen dat jo in goede pipeline hawwe, binne hjir wat foarbyldsiden om mei te begjinnen, út in boek oer supergeleiers. Jo pipeline moat goed omgean mei wiskunde, tabellen, grafiken, fuotnoaten, ensafuorthinne."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Stjoer jo ferwurke siden nei ús e-mailadres. As se der goed útsjogge, sille wy jo mear yn privee stjoere, en wy ferwachtsje dat jo jo pipeline dêr ek fluch op rinne kinne. As wy tefreden binne, kinne wy in oerienkomst meitsje."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Kolleksje"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Wat mear ynformaasje oer de kolleksje. <a %(duxiu)s>Duxiu</a> is in grutte databank fan skande boeken, makke troch de <a %(chaoxing)s>SuperStar Digital Library Group</a>. De measten binne akademyske boeken, skand om se digitaal beskikber te meitsjen foar universiteiten en biblioteken. Foar ús Ingelskpratende publyk hawwe <a %(library_princeton)s>Princeton</a> en de <a %(guides_lib_uw)s>Universiteit fan Washington</a> goede oersichten. Der is ek in treflik artikel dat mear eftergrûn jout: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (sykje it op yn Anna's Argiven)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "De boeken fan Duxiu binne al lang piratere op it Sineeske ynternet. Meastentiids wurde se ferkocht foar minder as in dollar troch opkeapers. Se wurde typysk ferspraat mei help fan it Sineeske ekwivalint fan Google Drive, dat faak hackt is om mear opslachromte mooglik te meitsjen. Guon technyske details kinne fûn wurde <a %(github_duty_machine)s>hjir</a> en <a %(github_821_github_io)s>hjir</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Hoewol de boeken semi-iepenbier ferspraat binne, is it frij dreech om se yn bulk te krijen. Wy hiene dit heech op ús TODO-list, en hawwe meardere moannen foltiids wurk derfoar ynpland. Lykwols, koartlyn hat in ynkringende, geweldige en talintearre frijwilliger ús berikt, dy't ús fertelde dat se al dit wurk al dien hiene — tsjin grutte kosten. Se dielden de folsleine kolleksje mei ús, sûnder wat werom te ferwachtsjen, útsein de garânsje fan langduorjende behâld. Wier bûtengewoan. Se stimden yn om op dizze manier om help te freegjen om de kolleksje OCR'e te krijen."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "De kolleksje bestiet út 7.543.702 bestannen. Dit is mear as Library Genesis non-fiksje (sawat 5,3 miljoen). De totale bestânsgrutte is sawat 359TB (326TiB) yn syn hjoeddeistige foarm."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Wy steane iepen foar oare foarstellen en ideeën. Nim gewoan kontakt mei ús op. Besjoch Anna's Argiven foar mear ynformaasje oer ús kolleksjes, behâldsinspanningen, en hoe't jo helpe kinne. Tankewol!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Warskôging: dizze blogpost is ferâldere. Wy hawwe besletten dat IPFS noch net klear is foar de haadtiid. Wy sille noch altyd keppelings nei bestannen op IPFS fan Anna's Argiven jaan as mooglik, mar wy sille it net mear sels hostje, noch riede wy oaren oan om te spegeljen mei help fan IPFS. Sjoch asjebleaft ús Torrents-side as jo ús kolleksje helpe wolle behâlde."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Help Z-Library op IPFS te seedjen"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Hoe in skaadbiblioteek te rinnen: operaasjes by Anna's Argiven"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Der is gjin <q>AWS foar skaadgoede doelen,</q> dus hoe rinne wy Anna's Argiven?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ik rin <a %(wikipedia_annas_archive)s>Anna's Argiven</a>, de grutste iepen boarne non-profit sykmasine foar <a %(wikipedia_shadow_library)s>skaadbiblioteken</a>, lykas Sci-Hub, Library Genesis, en Z-Library. Us doel is om kennis en kultuer maklik tagonklik te meitsjen, en úteinlik in mienskip te bouwen fan minsken dy't tegearre <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>alle boeken yn 'e wrâld</a> argivearje en behâlde."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Yn dit artikel sil ik sjen litte hoe't wy dizze webside rinne, en de unike útdagings dy't komme mei it operearjen fan in webside mei twifelachtige juridyske status, om't der gjin \"AWS foar skaadgoede doelen\" is."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Besjoch ek it susterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Hoe in piraat-argivaris te wurden</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Ynnovaasjetokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Litte wy begjinne mei ús techstack. It is mei opsetsin saai. Wy brûke Flask, MariaDB, en ElasticSearch. Dat is it letterlik. Sykjen is foar it grutste part in oplost probleem, en wy binne net fan doel it op 'e nij út te finen. Boppedat moatte wy ús <a %(mcfunley)s>ynnovaasjetokens</a> oan wat oars besteegje: net út 'e loft helle wurde troch de autoriteiten."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Hoe legaal of yllegaal is Anna’s Argiven krekt? Dit hinget foaral ôf fan de juridyske jurisdiksje. De measte lannen leauwe yn in foarm fan auteursrjocht, wat betsjut dat minsken of bedriuwen in eksklusyf monopolie krije op bepaalde soarten wurken foar in bepaalde perioade. As in sydnoat, by Anna’s Argiven leauwe wy dat, hoewol't der wat foardielen binne, auteursrjocht oer it algemien in netto-negatyf is foar de maatskippij — mar dat is in ferhaal foar in oare kear."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Dit eksklusive monopolie op bepaalde wurken betsjut dat it yllegaal is foar elkenien bûten dit monopolie om dy wurken direkt te fersprieden — ynklusyf ús. Mar Anna’s Argiven is in sykmasine dy't dy wurken net direkt ferspriedt (teminsten net op ús clearnet-webside), dus wy soene goed wêze, toch? Net krekt. Yn in protte jurisdiksjes is it net allinnich yllegaal om auteursrjochtlik beskerme wurken te fersprieden, mar ek om te keppeljen nei plakken dy't dat dogge. In klassyk foarbyld hjirfan is de Amerikaanske DMCA-wet."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dat is it strangste ein fan it spektrum. Oan de oare kant fan it spektrum kinne der teoretysk lannen wêze sûnder auteursrjochtwetten, mar dy besteane eins net. Hast elk lân hat in foarm fan auteursrjochtwet op 'e boeken. Hanthavening is in oar ferhaal. Der binne genôch lannen mei oerheden dy't net ynteressearre binne yn it hanthavenjen fan auteursrjochtwetten. Der binne ek lannen tusken de twa ekstreemen, dy't it fersprieden fan auteursrjochtlik beskerme wurken ferbiede, mar net it keppeljen nei sokke wurken."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "In oare oerweging is op it bedriuwsnivo. As in bedriuw operearret yn in jurisdiksje dy't net om auteursrjocht jout, mar it bedriuw sels is net ree om risiko te nimmen, dan kinne se jo webside slute sa gau't immen deroer kleit."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Uteinlik is in grutte oerweging betellingen. Om't wy anonym bliuwe moatte, kinne wy gjin tradisjonele betelmethoden brûke. Dit lit ús oer mei kryptomunten, en mar in lyts diel fan 'e bedriuwen stipet dy (der binne firtuele debitkaarten betelle mei krypto, mar dy wurde faak net akseptearre)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Systeemarsjitektuer"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Lit ús sizze dat jo wat bedriuwen fûn hawwe dy't ree binne om jo webside te hosten sûnder jo del te heljen — lit ús dizze \"frijheidsleafhawwende oanbieders\" neame 😄. Jo sille gau fernimme dat alles by harren hosten frij djoer is, dus jo wolle miskien wat \"goedkeape oanbieders\" fine en dêr it eigentlike hosten dwaan, troch de frijheidsleafhawwende oanbieders te proxyen. As jo it goed dogge, sille de goedkeape oanbieders nea witte wat jo hoste, en nea klachten ûntfange."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Mei al dizze oanbieders is der in risiko dat se jo dochs delhelje, dus jo hawwe ek redundânsje nedich. Wy hawwe dit op alle nivo's fan ús stack nedich."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Ien wat frijheidsleafhawwend bedriuw dat himsels yn in ynteressante posysje set hat, is Cloudflare. Se hawwe <a %(blog_cloudflare)s>argumintearre</a> dat se gjin hostingprovider binne, mar in nutsbedriuw, lykas in ISP. Se binne dêrom net ûnderwerp fan DMCA- of oare takedown-oanfragen, en stjoere alle oanfragen troch nei jo eigentlike hostingprovider. Se binne sa fier gien as nei de rjochtbank te gean om dizze struktuer te beskermjen. Wy kinne se dêrom brûke as in oare laach fan caching en beskerming."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare akseptearret gjin anonyme betellingen, dus wy kinne allinnich har fergese plan brûke. Dit betsjut dat wy har load balancing of failover-funksjes net kinne brûke. Wy hawwe dêrom <a %(annas_archive_l255)s>dit sels ymplemintearre</a> op domeinnivo. By it laden fan de side sil de browser kontrolearje oft it hjoeddeistige domein noch beskikber is, en as net, herskriuwt it alle URL's nei in oar domein. Om't Cloudflare in protte siden cachet, betsjut dit dat in brûker op ús haaddomein kin lânje, sels as de proxyserver del is, en dan by de folgjende klik nei in oar domein ferpleatst wurde kin."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Wy hawwe ek noch normale operasjonele soargen om mei te dwaan, lykas it kontrolearjen fan de sûnens fan de server, it loggen fan backend- en frontend-flaters, ensafuorthinne. Us failover-arsjitektuer makket mear robuustens op dit front mooglik, bygelyks troch in folslein oare set servers op ien fan 'e domeinen te draaien. Wy kinne sels âldere ferzjes fan 'e koade en datasets op dit aparte domein draaie, foar it gefal dat in krityske bug yn 'e haadferzje ûngemurken bliuwt."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Wy kinne ek in hedge meitsje tsjin Cloudflare dy't tsjin ús keart, troch it fan ien fan 'e domeinen te ferwiderjen, lykas dit aparte domein. Ferskillende permutaasjes fan dizze ideeën binne mooglik."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Ark"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Litte wy sjen nei hokker ark wy brûke om dit alles te berikken. Dit is hieltyd yn ûntwikkeling as wy nije problemen tsjinkomme en nije oplossingen fine."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Applikaasjeserver: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxyserver: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Serverbehear: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Untwikkeling: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Onion statyske hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Der binne guon besluten dêr't wy hinne en wer oer gien binne. Ien dêrfan is de kommunikaasje tusken servers: wy brûkten eartiids Wireguard hjirfoar, mar fûnen dat it soms ophâldt mei it ferstjoeren fan gegevens, of allinnich gegevens yn ien rjochting ferstjoert. Dit barde mei ferskate ferskillende Wireguard-ynstellingen dy't wy besocht hawwe, lykas <a %(github_costela_wesher)s>wesher</a> en <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Wy hawwe ek besocht om poarten oer SSH te tunneljen, mei autossh en sshuttle, mar kamen dêr <a %(github_sshuttle)s>problemen tsjin</a> (hoewol it my noch net dúdlik is oft autossh lêst hat fan TCP-oer-TCP problemen of net — it fielt gewoan as in janky oplossing foar my, mar miskien is it eins prima?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Ynstee dêrfan binne wy weromgien nei direkte ferbiningen tusken servers, en ferbergje wy dat in server draait op de goedkeape oanbieders mei IP-filtering mei UFW. Dit hat it neidiel dat Docker net goed wurket mei UFW, útsein as jo <code>network_mode: \"host\"</code> brûke. Dit alles is wat mear foutgefoelich, om't jo jo server bleatstelle oan it ynternet mei mar in lytse misconfiguraasje. Miskien moatte wy weromgean nei autossh — feedback hjir is tige wolkom."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Wy hawwe ek hinne en wer gien oer Varnish vs. Nginx. Wy hâlde op it stuit fan Varnish, mar it hat syn eigenaardichheden en rûge rânen. Itselde jildt foar Checkmk: wy hâlde der net fan, mar it wurket foar no. Weblate hat goed west, mar net ynkringend — ik bin soms bang dat it myn gegevens ferliest as ik besykje it te syngronisearjen mei ús git repo. Flask hat oer it algemien goed west, mar it hat wat frjemde eigenaardichheden dy't in soad tiid koste hawwe om te debuggen, lykas it konfigurearjen fan oanpaste domeinen, of problemen mei syn SqlAlchemy-yntegraasje."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Oant no ta binne de oare ark geweldich west: wy hawwe gjin serieuze klachten oer MariaDB, ElasticSearch, Gitlab, Zulip, Docker, en Tor. Al dizze hawwe wat problemen hân, mar neat te serieus of tiid-yntinsyf."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Konklúzje"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "It hat in ynteressante ûnderfining west om te learen hoe't jo in robúste en elastyske skaadbibleteek-sykmasine opsette. Der binne noch folle mear details te dielen yn lettere berjochten, lit my witte wêr't jo mear oer leare wolle!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Lykas altyd sykje wy nei donaasjes om dit wurk te stypjen, dus soargje derfoar dat jo de Donear-side op Anna’s Argiven besjogge. Wy sykje ek nei oare soarten stipe, lykas subsydzjes, lange-termyn sponsors, heech-risiko betellingsproviders, miskien sels (smakfolle!) advertinsjes. En as jo jo tiid en feardichheden bydrage wolle, sykje wy altyd nei ûntwikkelders, oersetters, ensafuorthinne. Tank foar jo ynteresse en stipe."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna en it team (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hoi, ik bin Anna. Ik haw <a %(wikipedia_annas_archive)s>Anna’s Argiven</a> makke, de grutste skaadbibleteek fan 'e wrâld. Dit is myn persoanlike blog, wêryn ik en myn teamgenoaten skriuwe oer piraterij, digitale behâld, en mear."

#, fuzzy
msgid "blog.index.text2"
msgstr "Ferbine mei my op <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Merk op dat dizze webside allinnich in blog is. Wy hostje hjir allinnich ús eigen wurden. Gjin torrents of oare auteursrjochtlik beskerme bestannen wurde hjir host of keppele."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogberjochten"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "5.998.794 boeken op IPFS sette"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Warskôging: dizze blogpost is ferâldere. Wy hawwe besletten dat IPFS noch net klear is foar de haadtiid. Wy sille noch altyd keppelings nei bestannen op IPFS fan Anna's Argiven jaan as mooglik, mar wy sille it net mear sels hostje, noch riede wy oaren oan om te spegeljen mei help fan IPFS. Sjoch asjebleaft ús Torrents-side as jo ús kolleksje helpe wolle behâlde."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna’s Argiven hat alle WorldCat (de grutste bibleteekmetadata-kolleksje fan 'e wrâld) skrabbe om in TODO-list fan boeken te meitsjen dy't bewarre wurde moatte.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "In jier lyn hawwe wy <a %(blog)s>ús ynset</a> om dizze fraach te beantwurdzjen: <strong>Hokker persintaazje boeken binne permanint bewarre troch skaadbibleteken?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Sadree't in boek yn in iepen-data skaadbibleteek lykas <a %(wikipedia_library_genesis)s>Library Genesis</a> komt, en no <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>, wurdt it oer de hiele wrâld spegele (troch torrents), en wurdt it dêrmei praktysk foar altyd bewarre."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Om de fraach te beantwurdzjen hokker persintaazje boeken bewarre is, moatte wy de noemer witte: hoefolle boeken besteane der yn totaal? En ideaal hawwe wy net allinnich in getal, mar ek eigentlike metadata. Dan kinne wy se net allinnich fergelykje mei skaadbibleteken, mar ek <strong>in TODO-list fan oerbleaune boeken meitsje om te bewarjen!</strong> Wy koene sels begjinne te dreamen fan in mienskiplike ynspanning om dizze TODO-list del te gean."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Wy hawwe <a %(wikipedia_isbndb_com)s>ISBNdb</a> skrast, en de <a %(openlibrary)s>Open Library dataset</a> downloade, mar de resultaten wiene net tefredenstellend. It haadprobleem wie dat der net in soad oerlap fan ISBNs wie. Sjoch dizze Venn-diagram út <a %(blog)s>ús blogpost</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Wy wiene tige ferrast troch hoe lyts de oerlap wie tusken ISBNdb en Open Library, dy't beide frijmoedich gegevens opnimme út ferskate boarnen, lykas webskrast en bibleteekrecords. As se beide in goede baan dogge by it finen fan de measte ISBNs dy't der binne, soene har sirkels grif substansjele oerlap hawwe, of soe ien in ûnderstel fan de oare wêze. It makke ús benijd, hoefolle boeken falle <em>kompleet bûten dizze sirkels</em>? Wy hawwe in gruttere databank nedich."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Dat is wannear't wy ús each op de grutste boekdatabank yn 'e wrâld setten: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is in proprietêre databank fan de non-profit <a %(wikipedia_oclc)s>OCLC</a>, dy't metadata records fan bibleteken oer de hiele wrâld aggregearret, yn ruil foar it jaan fan dy bibleteken tagong ta de folsleine dataset, en se yn 'e sykresultaten fan ein-brûkers te sjen."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Hoewol't OCLC in non-profit is, fereasket harren bedriuwsmodel it beskermjen fan harren databank. No, wy binne sorry te sizzen, freonen by OCLC, wy jouwe it allegear fuort. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Yn it ôfrûne jier hawwe wy alle WorldCat-records sekuer skrast. Yn it earstoan hiene wy in gelokkige brek. WorldCat wie krekt har folsleine webside-ûntwerp oan it útrôljen (yn augustus 2022). Dit omfette in substansjele oersjoch fan harren backend-systemen, dy't in protte befeiligingsflaters yntrodusearren. Wy grepen fuortendaliks de kâns, en koene hûnderten miljoenen (!) records yn in pear dagen skrasse."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat-ûntwerp</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Dêrnei waarden befeiligingsflaters stadichoan ien foar ien reparearre, oant de lêste dy't wy fûnen sawat in moanne lyn patcht waard. By dy tiid hiene wy frijwol alle records, en wiene wy allinnich op syk nei wat hegere kwaliteit records. Dus wy fiele dat it tiid is om frij te jaan!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Litte wy nei wat basisynformaasje oer de gegevens sjen:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formaat?</strong> <a %(blog)s>Anna’s Argiven Konteners (AAC)</a>, dat is yn wêzen <a %(jsonlines)s>JSON Lines</a> komprimearre mei <a %(zstd)s>Zstandard</a>, plus wat standerdisearre semantyk. Dizze konteners omfiemje ferskate soarten records, basearre op de ferskillende skrast dy't wy ynsetten."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Gegevens"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "In ûnbekende flater barde. Nim kontakt mei ús op fia %(email)s mei in skermôfbylding."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Dizze munt hat in hegere minimum dan gewoanlik. Selektearje in oare doer of in oare munt."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Oanfraach koe net foltôge wurde. Besykje it oer in pear minuten noch ris, en as it trochgiet, nim dan kontakt mei ús op by %(email)s mei in skermôfbylding."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Flater yn betellingsferwurking. Wachtsje in momint en besykje it opnij. As it probleem langer as 24 oeren duorret, nim dan kontakt mei ús op by %(email)s mei in skermôfbylding."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "ferburgen opmerking"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Bestânsprobleem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Better ferzje"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Wolle jo dizze brûker melde foar misledigjend of ûnfatsoenlik gedrach?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Meld misbrûk"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Misbrûk meld:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Jo hawwe dizze brûker meld foar misbrûk."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Antwurdzje"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Asjebleaft <a %(a_login)s>meld jo oan</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Jo hawwe in reaksje efterlitten. It kin in minút duorje foardat it ferskynt."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Der is wat misgien. Laad de side opnij en besykje it nochris."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s troffen siden"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Net sichtber yn Libgen.rs Non-Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Net sichtber yn Libgen.rs Fiction"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Net sichtber yn Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Marked brutsen yn Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Net fan Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Markearre as \"spam\" yn Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Markearre as \"min bestân\" yn Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Net alle siden koene omset wurde nei PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Running exiftool mislearre op dit bestân"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Boek (ûnbekend)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Boek (non-fiksje)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Boek (fiksje)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Tydskriftartikel"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standertdokumint"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Tydskrift"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Stripboek"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Muzykskoare"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Audioboek"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Oar"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Partner Server download"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Eksterne download"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Ekstern lien"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Eksterne liening (print útskeakele)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Ferken metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Befette yn torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Sineesk"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Uploads nei AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBook Yndeks"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tsjechyske metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russyske Steatsbibleteek"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titel"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Auteur"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Utjouwer"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edysje"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Jier publisearre"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Oarspronklike bestânsnamme"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Beskriuwing en metadata opmerkingen"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Partner Server downloads tydlik net beskikber foar dit bestân."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Fluch Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(oanrikkemand)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(geen browserferifikaasje of wachtlisten)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Trage Partner Server #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(in bytsje flugger, mar mei wachtrige)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(gjin wachtlist, mar kin heul traach wêze)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Non-Fiction"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiksje"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klik ek op “GET” oan de boppekant)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klik op “GET” oan de boppekant)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "harren advertinsjes binne bekend om kweade software te befetsjen, dus brûk in advertinsjeblokker of klik net op advertinsjes"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC-bestannen kinne ûnbetrouber wêze om te downloaden)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Bibleteek op Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(fereasket de Tor Browser)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Liene fan it Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(allinnich foar print-beheinde patrons)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(assosjearre DOI kin net beskikber wêze yn Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "samling"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Bulk torrent downloads"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(allinnich foar saakkundigen)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Sykje yn Anna’s Archive op ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Sykje yn ferskate oare databases nei ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Fyn orizjineel rekord yn ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Sykje Anna’s Argyf foar Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Fyn orizjineel rekord yn Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Sykje yn Anna’s Argyf nei OCLC (WorldCat) nûmer"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Fyn orizjineel rekord yn WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Sykje Anna’s Argyf foar DuXiu SSID nûmer"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Manueel sykje op DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Sykje Anna’s Argyf foar CADAL SSNO nûmer"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Fyn orizjineel rekord yn CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Sykje Anna’s Archive foar DuXiu DXID-nûmer"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBook Yndeks"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(gjin browserferifikaasje nedich)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tsjechyske metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "beskriuwing"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternatyf bestânsnamme"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternatyf titel"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternative auteur"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternative útjouwer"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternative edysje"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternative útwreiding"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata opmerkingen"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternative beskriuwing"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "datum iepen boarne"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub-bestân “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Controlled Digital Lending-bestân “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dit is in rekord fan in bestân fan de Internet Archive, net in direkt te downloaden bestân. Jo kinne besykje it boek te lienen (keppeling hjirûnder), of brûk dizze URL by it <a %(a_request)s>oanfreegjen fan in bestân</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "As jo dit bestân hawwe en it is noch net beskikber yn Anna’s Archive, beskôgje dan <a %(a_request)s>it te uploaden</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata-record"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nûmer %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata record"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dit is in metadata-record, gjin te downloaden bestân. Jo kinne dizze URL brûke by it <a %(a_request)s>oanfreegjen fan in bestân</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata fan keppele rekord"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Ferbetterje metadata op Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Warskôging: meardere keppele records:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Ferbetterje metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Meld bestânkwaliteit"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Downloadtiid"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Webside:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Sykje yn Anna’s Archive nei “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Koade-ûndersiker:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Besjoch yn Codes Explorer \"%(name)s\""

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lês mear…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Downloads (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Liene (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Ferken metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Kommentaren (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Listen (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistiken (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Technyske details"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Dit bestân kin problemen hawwe, en is ferburgen fan in boarnebiblioteek.</span> Soms is dit op fersyk fan in auteursrjochteigner, soms is it om't in better alternatyf beskikber is, mar soms is it om't der in probleem is mei it bestân sels. It kin noch hieltyd goed wêze om te downloaden, mar wy riede oan earst te sykjen nei in alternatyf bestân. Mear details:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "In bettere ferzje fan dit bestân kin beskikber wêze by %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "As jo dit bestân noch downloade wolle, soargje derfoar dat jo allinich fertroude, bywurke software brûke om it te iepenjen."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Fluch downloads"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Fluch downloads</strong> Wur in <a %(a_membership)s>lid</a> om de langduorjende bewarjen fan boeken, papieren en mear te stypjen. Om ús tankberens foar jo stipe te toanen, krije jo flugge downloads. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "As jo dizze moanne donearje, krije jo <strong>dûbel</strong> it oantal snelle downloads."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Fluch downloads</strong> Jo hawwe %(remaining)s oer hjoed. Tank foar it lid wêzen! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Snelle downloads</strong> Jo hawwe hjoed gjin snelle downloads mear."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Fluch downloads</strong> Jo hawwe dit bestân koartlyn downloade. Keppelings bliuwe in skoftke jildich."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opsje #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(gjin trochferwizing)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(iepenje yn besjogger)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Ferwize in freon, en sawol jo as jo freon krije %(percentage)s%% bonus rappe downloads!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Lear mear…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Stadige downloads"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Fan fertroude partners."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Mear ynformaasje yn de <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kin <a %(a_browser)s>browserferifikaasje</a> fereaskje — ûnbeheinde downloads!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Nei it downloaden:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Iepenje yn ús besjogger"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "toan eksterne downloads"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Eksterne downloads"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Gjin downloads fûn."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Alle downloadopsjes hawwe itselde bestân, en moatte feilich wêze om te brûken. Dat sein hawwende, wês altyd foarsichtich by it downloaden fan bestannen fan it ynternet, benammen fan siden bûten Anna’s Archive. Hâld bygelyks jo apparaten bywurke."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Foar grutte triemmen riede wy oan in downloadmanager te brûken om ûnderbrekkingen foar te kommen."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Oanbefellende downloadmanagers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Jo hawwe in e-boek of PDF-lêzer nedich om it triem te iepenjen, ôfhinklik fan it triemformaat."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Oanbefellende e-boeklêzers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Anna’s Argiven online besjogger"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Brûk online ark om tusken formaten te konvertearjen."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Oanbefellende konverzje-ark: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Jo kinne sawol PDF- as EPUB-bestannen nei jo Kindle of Kobo eReader stjoere."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Oanbefellende ark: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon's \"Send to Kindle\""

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz's \"Send to Kobo/Kindle\""

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Stypje auteurs en biblioteken"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "As jo dit leuk fine en it kinne betelje, beskôgje dan it orizjineel te keapjen, of de auteurs direkt te stypjen."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "As dit beskikber is by jo lokale biblioteek, beskôgje it dan dêr fergees te lienjen."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Bestânkwaliteit"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Help de mienskip troch de kwaliteit fan dit bestân te melden! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Meld bestânsprobleem (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Prachtige bestânkwaliteit (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Foegje reaksje ta (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Wat is der mis mei dit bestân?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Brûk asjebleaft it <a %(a_copyright)s>DMCA / Copyright claim formulier</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Beskriuw it probleem (ferplicht)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Probleembeskriuwing"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 fan in bettere ferzje fan dit bestân (as fan tapassing)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Folje dit yn as der in oar bestân is dat tichtby dit bestân komt (deselde edysje, deselde bestânsútwreiding as jo ien fine kinne), dat minsken ynstee fan dit bestân brûke moatte. As jo in bettere ferzje fan dit bestân bûten Anna’s Argyf kenne, upload it dan asjebleaft <a %(a_upload)s>hjir</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Jo kinne de md5 fan de URL krije, bygelyks"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Stjoer rapport yn"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Learje hoe't jo sels de <a %(a_metadata)s>metadata foar dit bestân ferbetterje</a> kinne."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Tige tank foar it yntsjinjen fan jo rapport. It sil op dizze side toand wurde, en ek hânmjittich besjoen wurde troch Anna (oant wy in goed moderaasjesysteem hawwe)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Der is wat misgien. Laad de side opnij en besykje it nochris."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "As dit bestân fan hege kwaliteit is, kinne jo hjir alles deroer beprate! As net, brûk dan de knop \"Meld bestânsprobleem\"."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Ik fûn dit boek geweldich!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Lit in reaksje efter"

#, fuzzy
msgid "common.english_only"
msgstr "De tekst hjirûnder giet fierder yn it Ingelsk."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totaal oantal downloads: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "In \"bestân MD5\" is in hash dy't berekkene wurdt út de ynhâld fan it bestân, en is ridlik unyk basearre op dy ynhâld. Alle skaadbiblioteken dy't wy hjir yndeksearre hawwe brûke foaral MD5's om bestannen te identifisearjen."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "In bestân kin ferskine yn meardere skaadbiblioteken. Foar ynformaasje oer de ferskate datasets dy't wy gearstald hawwe, sjoch de <a %(a_datasets)s>Datasets side</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dit is in bestân beheard troch de <a %(a_ia)s>IA's Controlled Digital Lending</a> bibleteek, en yndeksearre troch Anna’s Archive foar sykjen. Foar ynformaasje oer de ferskate datasets dy't wy gearstald hawwe, sjoch de <a %(a_datasets)s>Datasets side</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Foar ynformaasje oer dit spesifike bestân, sjoch syn <a %(a_href)s>JSON-bestân</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Probleem by it laden fan dizze side"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Fernij asjebleaft om it nochris te besykjen. <a %(a_contact)s>Kontakt mei ús opnimme</a> as it probleem foar meardere oeren oanhâldt."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Net fûn"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” waard net fûn yn ús databank."

#, fuzzy
msgid "page.login.title"
msgstr "Ynlogge / Registrearje"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Blêderferifikaasje"

#, fuzzy
msgid "page.login.text1"
msgstr "Om te foarkommen dat spam-bots in protte akkounts oanmeitsje, moatte wy earst jo browser ferifiearje."

#, fuzzy
msgid "page.login.text2"
msgstr "As jo yn in einleaze loop fêst komme, riede wy oan om <a %(a_privacypass)s>Privacy Pass</a> te ynstallearjen."

#, fuzzy
msgid "page.login.text3"
msgstr "It kin ek helpe om advertinsjeblokkers en oare browser-útwreidings út te setten."

#, fuzzy
msgid "page.codes.title"
msgstr "Koades"

#, fuzzy
msgid "page.codes.heading"
msgstr "Koadesferkenner"

#, fuzzy
msgid "page.codes.intro"
msgstr "Ferken de koades wêrmei records tagge binne, per prefix. De kolom \"records\" lit it oantal records sjen dat tagge is mei koades mei de jûn prefix, sa't sjoen yn de sykmasine (ynklusyf allinich metadata-records). De kolom \"koades\" lit sjen hoefolle eigentlike koades in jûn prefix hawwe."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Dizze side kin in skoft duorje om te generearjen, dêrom is in Cloudflare captcha nedich. <a %(a_donate)s>Leden</a> kinne de captcha oerslaan."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Asjebleaft, skraap dizze siden net. Ynstee riede wy oan <a %(a_import)s>te generearjen</a> of <a %(a_download)s>te downloaden</a> ús ElasticSearch en MariaDB databases, en ús <a %(a_software)s>iepen boarne koade</a> út te fieren. De rauwe gegevens kinne manuell ferkend wurde fia JSON-bestannen lykas <a %(a_json_file)s>dizze</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Prefix"

#, fuzzy
msgid "common.form.go"
msgstr "Gean"

#, fuzzy
msgid "common.form.reset"
msgstr "Reset"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Sykje Anna’s Argyf"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Warskôging: koade hat ferkearde Unicode-tekens en kin yn ferskate situaasjes ferkeard funksjonearje. De rauwe binêre kin dekodearre wurde fan 'e base64-fertsjintwurdiging yn de URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bekende koadefoarheaksel “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Foarheaksel"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Beskriuwing"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL foar in spesifike koade"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sil ferfongen wurde troch de wearde fan de koade"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Algemiene URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Webside"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s rekord komt oerien mei “%(prefix_label)s”"
msgstr[1] "%(count)s records komme oerien mei “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL foar spesifike koade: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Mear…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Koades dy't begjinne mei “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Yndeks fan"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "records"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "koades"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Minder as %(count)s records"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Foar DMCA / copyright-oanspraken, brûk <a %(a_copyright)s>dit formulier</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Alle oare manieren om kontakt mei ús op te nimmen oer auteursrjochtkearnen wurde automatysk wiske."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Wy stelle jo feedback en fragen tige op priis!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Mar, fan it grutte tal spam en ûnsin-e-mails dy't wy krije, kontrolearje asjebleaft de fakjes om te befestigjen dat jo dizze betingsten foar kontakt mei ús begripe."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Copyright-oanspraken op dit e-mailadres wurde negearre; brûk it formulier ynstee."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Partner servers binne net beskikber fanwegen hosting-slutingen. Se moatte gau wer online wêze."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Lidmaatskippen wurde dêrnei ferlinge."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Stjoer ús gjin e-mail om <a %(a_request)s>boeken oan te freegjen</a><br>of lytse (<10k) <a %(a_upload)s>uploads</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "By it stellen fan fragen oer akkount of donaasje, foegje jo akkount-ID, skermôfbyldings, kwitânsjes, en safolle mooglik ynformaasje ta. Wy kontrolearje ús e-post mar elke 1-2 wiken, dus it net opnimmen fan dizze ynformaasje sil elke oplossing fertrage."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Toan e-mail"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Copyright claim formulier"

#, fuzzy
msgid "page.copyright.intro"
msgstr "As jo in DCMA of oare copyright claim hawwe, folje dan dit formulier sa krekt mooglik yn. As jo problemen tsjinkomme, nim dan kontakt mei ús op fia ús spesjale DMCA-adres: %(email)s. Tink derom dat claims dy't nei dit adres maild wurde net ferwurke wurde, it is allinnich foar fragen. Brûk asjebleaft it formulier hjirûnder om jo claims yn te tsjinjen."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URLs op Anna’s Argiven (ferplicht). Ien per rigel. Nim allinnich URLs op dy't deselde edysje fan in boek beskriuwe. As jo in claim wolle yntsjinje foar meardere boeken of meardere edysjes, folje dit formulier dan meardere kearen yn."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Claims dy't meardere boeken of edysjes bondelje sille ôfwiisd wurde."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Jo namme (ferplicht)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adres (ferplicht)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefoannûmer (ferplicht)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mail (ferplicht)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Dúdlike beskriuwing fan it boarnemateriaal (ferplicht)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN's fan it boarnemateriaal (as fan tapassing). Ien per rigel. Nim allinnich dy op dy't krekt oerienkomme mei de edysje dêr't jo in copyright claim foar rapportearje."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URLs fan it boarnemateriaal, ien per rigel. Nim efkes de tiid om yn Open Library te sykjen nei jo boarnemateriaal. Dit sil ús helpe om jo claim te ferifiearjen."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URLs nei it boarnemateriaal, ien per rigel (ferplicht). Nim safolle mooglik op, om ús te helpen jo claim te ferifiearjen (bgl. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Ferklearring en hantekening (ferplicht)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Claim yntsjinje"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Tige tank foar it yntsjinjen fan jo copyright claim. Wy sille it sa gau mooglik besjen. Fernij de side om in nije claim yn te tsjinjen."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Der is wat misgien. Fernij de side en besykje it opnij."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "As jo ynteressearre binne yn it spegeljen fan dizze dataset foar <a %(a_archival)s>argyfwurk</a> of <a %(a_llm)s>LLM-training</a> doelen, nim dan kontakt mei ús op."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Us missy is om alle boeken yn 'e wrâld (lykas papieren, tydskriften, ensfh.) te argivearjen en breed tagonklik te meitsjen. Wy leauwe dat alle boeken breed spegele wurde moatte, om redundânsje en wjerstân te garandearjen. Dêrom sammelje wy bestannen fan ferskate boarnen. Guon boarnen binne folslein iepen en kinne yn bulk spegele wurde (lykas Sci-Hub). Oaren binne sletten en beskermjend, dus besykje wy se te skrassen om har boeken te \"befrijen\". Oaren falle earne tuskenyn."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Al ús gegevens kinne <a %(a_torrents)s>torrente</a> wurde, en al ús metadata kinne <a %(a_anna_software)s>generearre</a> of <a %(a_elasticsearch)s>downloade</a> wurde as ElasticSearch en MariaDB databases. De rauwe gegevens kinne hânmjittich ferkend wurde troch JSON-bestannen lykas <a %(a_dbrecord)s>dit</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Oersjoch"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Hjirûnder is in fluch oersjoch fan de boarnen fan de bestannen op Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Boarne"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Grutte"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% spegele troch AA / torrents beskikber"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Persintaazjes fan it oantal bestannen"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Lêst bywurke"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Non-fiksje en fiksje"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s bestân"
msgstr[1] "%(count)s bestannen"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: beferzen sûnt 2021; measte beskikber fia torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: lytse tafoegings sûnt doe</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "“Scimag” útsletten"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fiksje-torrents binne efter (hoewol't IDs ~4-6M net torrente binne om't se oerlappe mei ús Zlib-torrents)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "De “Sineeske” kolleksje yn Z-Library liket deselde te wêzen as ús DuXiu-kolleksje, mar mei ferskillende MD5s. Wy slute dizze bestannen út fan torrents om dûbeling te foarkommen, mar litte se noch wol sjen yn ús sykindeks."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Kontroleare Digitale Liening"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ fan de bestannen binne trochsykber."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totaal"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Útsûnderje dûbelingen"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Om't de skaadbiblioteken faak gegevens fan inoar syngronisearje, is der in soad oerlap tusken de biblioteken. Dêrom komme de sifers net oerien mei it totaal."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "It persintaazje \"spegele en siedde troch Anna’s Archive\" lit sjen hoefolle bestannen wy sels spegelje. Wy siedde dy bestannen yn bulk fia torrents, en meitsje se beskikber foar direkte download fia partnerwebsiden."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Boarnebiblioteken"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Guon boarnbibleteken befoarderje it massaal dielen fan harren gegevens fia torrents, wylst oaren harren kolleksje net maklik diele. Yn it lêste gefal besiket Anna’s Archive harren kolleksjes te skraapjen en beskikber te meitsjen (sjoch ús <a %(a_torrents)s>Torrents</a> side). Der binne ek tuskenyn situaasjes, bygelyks wêr't boarnbibleteken wol diele wolle, mar net de middels hawwe om dat te dwaan. Yn dy gefallen besykje wy ek te helpen."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Hjirûnder is in oersjoch fan hoe't wy ynterface mei de ferskate boarnbibleteken."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Boarne"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Bestannen"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Deistige <a %(dbdumps)s>HTTP-database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Automatyske torrents foar <a %(nonfiction)s>Non-Fiksje</a> en <a %(fiction)s>Fiksje</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna's Argiven behearret in kolleksje fan <a %(covers)s>boekomslach torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub hat sûnt 2021 gjin nije bestannen mear tafoege."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata-dumps beskikber <a %(scihub1)s>hjir</a> en <a %(scihub2)s>hjir</a>, en ek as diel fan de <a %(libgenli)s>Libgen.li-database</a> (dy't wy brûke)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Gegevens-torrents beskikber <a %(scihub1)s>hjir</a>, <a %(scihub2)s>hjir</a>, en <a %(libgenli)s>hjir</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Guon nije bestannen wurde <a %(libgenrs)s>tafoege</a> oan Libgen’s “scimag”, mar net genôch om nije torrents te rjochtfeardigjen"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kwartaal <a %(dbdumps)s>HTTP-database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Non-Fiksje torrents wurde dield mei Libgen.rs (en spegele <a %(libgenli)s>hjir</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna’s Argyf en Libgen.li beheare tegearre kolleksjes fan <a %(comics)s>stripboeken</a>, <a %(magazines)s>tydskriften</a>, <a %(standarts)s>standert dokuminten</a>, en <a %(fiction)s>fiksje (ôfwykt fan Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Harren “fiction_rus” kolleksje (Russyske fiksje) hat gjin spesifike torrents, mar wurdt dekt troch torrents fan oaren, en wy hâlde in <a %(fiction_rus)s>spegel</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna’s Argyf en Z-Library beheare tegearre in kolleksje fan <a %(metadata)s>Z-Library metadata</a> en <a %(files)s>Z-Library bestannen</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Guon metadata beskikber fia <a %(openlib)s>Open Library database dumps</a>, mar dy dekke net de hiele IA-kolleksje"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Gjin maklik tagonklike metadata dumps beskikber foar harren hiele kolleksje"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna's Argiven behearret in kolleksje fan <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Bestannen allinnich beskikber foar liening op in beheinde basis, mei ferskate tagongsbeheiningen"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna’s Argyf behearet in kolleksje fan <a %(ia)s>IA-bestannen</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Ferskate metadata-databases ferspraat oer it Sineeske ynternet; hoewol faak betelle databases"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Gjin maklik tagonklike metadata-dumps beskikber foar harren hiele kolleksje."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna's Argiven behearret in kolleksje fan <a %(duxiu)s>DuXiu metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Ferskate bestânsdatabases ferspraat oer it Sineeske ynternet; hoewol faak betelle databases"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s De measte bestannen binne allinnich tagonklik mei premium BaiduYun-akkounts; stadige downloadsnelheden."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Argiven behearet in kolleksje fan <a %(duxiu)s>DuXiu-bestannen</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Ferskate lytsere of ienmalige boarnen. Wy moedigje minsken oan om earst nei oare skaadbiblioteken te uploaden, mar soms hawwe minsken kolleksjes dy't te grut binne foar oaren om troch te sortearjen, hoewol net grut genôch om in eigen kategory te rjochtfeardigjen."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Allinnich-metadata boarnen"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Wy ferheegje ús kolleksje ek mei allinnich-metadata boarnen, dy't wy kinne keppelje oan bestannen, bygelyks mei ISBN-nûmers of oare fjilden. Hjirûnder is in oersjoch fan dy boarnen. Op 'e nij, guon fan dizze boarnen binne folslein iepen, wylst wy oaren skraapje moatte."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Us ynspiraasje foar it sammeljen fan metadata is Aaron Swartz’ doel fan “ien webside foar elk boek dat ea publisearre is”, dêr't hy <a %(a_openlib)s>Open Library</a> foar makke. Dat projekt hat it goed dien, mar ús unike posysje makket it mooglik om metadata te krijen dy't sy net kinne. In oare ynspiraasje wie ús winsk om te witten <a %(a_blog)s>hoefolle boeken der yn 'e wrâld binne</a>, sadat wy kinne berekkenje hoefolle boeken wy noch moatte rêde."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Merk op dat wy yn metadata-sykjen de orizjinele records sjen litte. Wy dogge gjin gearfoeging fan records."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Lêst bywurke"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Moanlikse <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Net direkt yn bulk beskikber, beskerme tsjin scraping"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna's Argiven behearret in kolleksje fan <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Unified database"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Wy kombinearje alle boppesteande boarnen yn ien unified database dy't wy brûke om dizze webside te betsjinjen. Dizze unified database is net direkt beskikber, mar om't Anna’s Archive folslein iepen boarne is, kin it frij maklik <a %(a_generated)s>generearre</a> of <a %(a_downloaded)s>downloade</a> wurde as ElasticSearch en MariaDB databases. De skripts op dy side sille automatysk alle nedige metadata downloade fan de hjirboppe neamde boarnen."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "As jo ús gegevens wolle ferkenne foardat jo dy skripts lokaal útfiere, kinne jo ús JSON-bestannen besjen, dy't fierder keppelje nei oare JSON-bestannen. <a %(a_json)s>Dit bestân</a> is in goed startpunt."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Oanpast fan ús <a %(a_href)s>blogpost</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> is in enoarme databank fan skande boeken, makke troch de <a %(superstar_link)s>SuperStar Digital Library Group</a>. De measten binne akademyske boeken, skand om se digitaal beskikber te meitsjen foar universiteiten en biblioteken. Foar ús Ingelsktalige publyk hawwe <a %(princeton_link)s>Princeton</a> en de <a %(uw_link)s>Universiteit fan Washington</a> goede oersichten. Der is ek in treflik artikel dat mear eftergrûn jout: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "De boeken fan Duxiu binne al lang piraat op it Sineeske ynternet. Meastentiids wurde se ferkocht foar minder as in dollar troch ferkeapers. Se wurde typysk ferspraat mei help fan it Sineeske ekwivalint fan Google Drive, dat faak hackt is om mear opslachromte mooglik te meitsjen. Guon technyske details kinne fûn wurde <a %(link1)s>hjir</a> en <a %(link2)s>hjir</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Hoewol't de boeken semi-iepenbier ferspraat binne, is it frij dreech om se yn bulk te krijen. Wy hiene dit heech op ús TODO-list, en hawwe meardere moannen fan fulltime wurk derfoar tawiisd. Mar, yn let 2023 hat in yncredible, geweldige, en talintearre frijwilliger ús berikt, en fertelde ús dat se al dit wurk al dien hiene — tsjin grutte kosten. Se dielden de folsleine kolleksje mei ús, sûnder wat yn ruil te ferwachtsjen, útsein de garânsje fan lange-termyn bewarjen. Wier bûtengewoan."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Boarnen"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totale bestannen: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totale bestânsgrutte: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Bestannen spegele troch Anna’s Archive: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Lêst bywurke: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents troch Anna’s Archive"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Foarbyldrekord op Anna’s Archive"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Us blogpost oer dizze gegevens"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripts foar it ymportearjen fan metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna’s Archive Containers formaat"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Mear ynformaasje fan ús frijwilligers (raw notysjes):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Dizze dataset is nau besibbe oan de <a %(a_datasets_openlib)s>Open Library dataset</a>. It befettet in scrape fan alle metadata en in grut diel fan de triemmen fan de IA’s Controlled Digital Lending Library. Updates wurde útbrocht yn it <a %(a_aac)s>Anna’s Argiven Containers formaat</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Dizze records wurde direkt ferwiisd fan de Open Library dataset, mar befetsje ek records dy't net yn Open Library steane. Wy hawwe ek in oantal datafiles dy't troch mienskipsleden oer de jierren hinne skrapt binne."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "De kolleksje bestiet út twa dielen. Jo hawwe beide dielen nedich om alle gegevens te krijen (útsein ferâldere torrents, dy't trochkrúst binne op de torrentside)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "ús earste release, foardat wy standerdisearren op it <a %(a_aac)s>Anna’s Archive Containers (AAC) formaat</a>. Bevat metadata (as json en xml), pdfs (fan acsm en lcpdf digitale útlieningssystemen), en omslach miniatueren."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "ynkrementele nije releases, mei help fan AAC. Bevat allinnich metadata mei tiidsstimpels nei 2023-01-01, om't de rest al dekt is troch “ia”. Ek alle pdf-bestannen, dizze kear fan de acsm en “bookreader” (IA’s web reader) útlieningssystemen. Hoewol't de namme net krekt goed is, folje wy noch altyd bookreader-bestannen yn de ia2_acsmpdf_files kolleksje, om't se ûnderling útslutend binne."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Haad %(source)s webside"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitale Útlieningsbibleteek"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata-dokumintaasje (de measte fjilden)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN lânynformaasje"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "De Ynternasjonale ISBN Agintskip publisearret regelmjittich de berikken dy't it tawiisd hat oan nasjonale ISBN-agintskippen. Hjirút kinne wy ôfmeitsje ta hokker lân, regio, of taalgroep dit ISBN heart. Wy brûke dizze gegevens op it stuit yndirekt, fia de <a %(a_isbnlib)s>isbnlib</a> Python-biblioteek."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Boarnen"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Lêst bywurke: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN webside"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Foar de eftergrûn fan de ferskate Library Genesis forks, sjoch de side foar de <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "De Libgen.li befettet it measte fan deselde ynhâld en metadata as de Libgen.rs, mar hat wat kolleksjes boppe-op dit, nammentlik strips, tydskriften, en standertdokuminten. It hat ek <a %(a_scihub)s>Sci-Hub</a> yntegrearre yn syn metadata en sykmasine, wat wy brûke foar ús databank."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "De metadata foar dizze bibleteek is fergees beskikber <a %(a_libgen_li)s>op libgen.li</a>. Lykwols, dizze server is traach en stipet gjin it werheljen fan brutsen ferbiningen. Deselde bestannen binne ek beskikber op <a %(a_ftp)s>in FTP-server</a>, dy't better wurket."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents binne beskikber foar de measte fan de ekstra ynhâld, benammen torrents foar stripboeken, tydskriften, en standert dokuminten binne útbrocht yn gearwurking mei Anna’s Argyf."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "De fiksjekolleksje hat syn eigen torrents (ôfwykt fan <a %(a_href)s>Libgen.rs</a>) begjinnend by %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Neffens de Libgen.li-behearder moat de “fiction_rus” (Russyske fiksje) kolleksje dekt wurde troch regelmjittich útbrochte torrents fan <a %(a_booktracker)s>booktracker.org</a>, benammen de <a %(a_flibusta)s>flibusta</a> en <a %(a_librusec)s>lib.rus.ec</a> torrents (dy't wy hjir <a %(a_torrents)s>spegelje</a>, hoewol't wy noch net fêststeld hawwe hokker torrents oerienkomme mei hokker bestannen)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistiken foar alle kolleksjes kinne fûn wurde <a %(a_href)s>op de webside fan libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Non-fiksje liket ek ôfwykt te wêzen, mar sûnder nije torrents. It liket derop dat dit sûnt begjin 2022 bard is, hoewol't wy dit net ferifiearre hawwe."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Bepaalde reeksen sûnder torrents (lykas fiksje reeksen f_3463000 oant f_4260000) binne wierskynlik Z-Library (of oare dûbele) bestannen, hoewol't wy miskien wat deduplikearje wolle en torrents meitsje foar lgli-unike bestannen yn dizze reeksen."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Merk op dat de torrentbestannen dy't ferwize nei “libgen.is” eksplisyt spegels binne fan <a %(a_libgen)s>Libgen.rs</a> (“.is” is in oar domein brûkt troch Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "In nuttige boarne foar it brûken fan de metadata is <a %(a_href)s>dizze side</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiksje-torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Strips-torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Tydskriften-torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standert dokumint torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russyske fiksje torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata fia FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata fjildynformaasje"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Spegel fan oare torrents (en unike fiksje- en strips-torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Diskusjefoarum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Us blogpost oer de stripboekenútjefte"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "It koarte ferhaal fan de ferskillende Library Genesis (of “Libgen”) forks, is dat oer tiid, de ferskillende minsken belutsen by Library Genesis in skeel hiene, en harren eigen wei giene."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "De “.fun” ferzje waard makke troch de orizjinele oprjochter. It wurdt opnij ûntwikkele yn it foardiel fan in nije, mear fersprate ferzje."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "De “.rs” ferzje hat heul ferlykbere gegevens, en publisearret meast konsekwint harren kolleksje yn bulk torrents. It is rûchwei ferdield yn in “fiksje” en in “non-fiksje” seksje."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oarspronklik op “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "De <a %(a_li)s>“.li” ferzje</a> hat in enoarme kolleksje fan strips, lykas oare ynhâld, dy't (noch) net beskikber is foar bulk download fia torrents. It hat wol in aparte torrentkolleksje fan fiksjeboeken, en it befettet de metadata fan <a %(a_scihub)s>Sci-Hub</a> yn syn databank."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Neffens dizze <a %(a_mhut)s>forumpost</a> waard Libgen.li oarspronklik host op “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> is yn in bepaalde sin ek in fork fan Library Genesis, hoewol't se in oare namme brûkten foar harren projekt."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Dizze side giet oer de “.rs” ferzje. It is bekend om konsekwint sawol syn metadata as de folsleine ynhâld fan syn boekekatalogus te publisearjen. Syn boekekolleksje is ferdield tusken in fiksje- en non-fiksje-diel."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "In nuttige boarne foar it brûken fan de metadata is <a %(a_metadata)s>dizze side</a> (blokkeart IP-berik, VPN kin nedich wêze)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Fan 2024-03 ôf, wurde nije torrents pleatst yn <a %(a_href)s>dizze forumdraad</a> (blokkeart IP-berik, VPN kin nedich wêze)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Non-fiksje torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiksje torrents op Anna’s Argyf"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata fjildynformaasje"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Non-fiksje torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiksje torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Diskusje forum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents troch Anna’s Argyf (boekomslagen)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Us blog oer de boekomslach release"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis is bekend om't se har gegevens al genereus yn bulk beskikber stelle fia torrents. Us Libgen-kolleksje bestiet út helpgegevens dy't se net direkt frijlitte, yn gearwurking mei harren. In protte tank oan elkenien belutsen by Library Genesis foar it wurkjen mei ús!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Dizze <a %(blog_post)s>earste release</a> is frij lyts: sawat 300GB oan boekomslagen fan de Libgen.rs fork, sawol fiksje as non-fiksje. Se binne organisearre op deselde wize as hoe't se ferskine op libgen.rs, bygelyks:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s foar in non-fiksje boek."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s foar in fiksje boek."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Krekt as mei de Z-Library kolleksje, hawwe wy se allegear yn in grutte .tar-bestân set, dat montearre wurde kin mei <a %(a_ratarmount)s>ratarmount</a> as jo de bestannen direkt tsjinje wolle."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> is in proprietêre databank fan de non-profit <a %(a_oclc)s>OCLC</a>, dy't metadata records fan biblioteken oer de hiele wrâld aggregearret. It is wierskynlik de grutste biblioteekmetadata kolleksje yn 'e wrâld."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, earste útjefte:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Yn oktober 2023 hawwe wy in wiidweidige scrape fan de OCLC (WorldCat) databank <a %(a_scrape)s>frijlitten</a>, yn it <a %(a_aac)s>Anna’s Archive Containers formaat</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents troch Anna’s Archive"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Us blogpost oer dizze gegevens"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library is in iepen boarne projekt fan de Internet Archive om elk boek yn de wrâld te katalogisearjen. It hat ien fan de grutste boekskanoperaasjes yn de wrâld, en hat in protte boeken beskikber foar digitale liening. Har boekmetadata katalogus is fergees beskikber foar download, en is opnaam yn Anna’s Argyf (hoewol net op it stuit yn sykjen, útsein as jo eksplisyt sykje nei in Open Library ID)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Release 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dit is in dump fan in protte oproppen nei isbndb.com yn septimber 2022. Wy besochten alle ISBN-berikken te dekken. Dit binne sawat 30,9 miljoen records. Op har webside beweare se dat se eins 32,6 miljoen records hawwe, dus wy hawwe miskien op ien of oare manier wat mist, of <em>se</em> kinne wat ferkeard dwaan."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "De JSON-antwurden binne frijwat rau fan har server. Ien gegevenskwaliteitsprobleem dat wy opmurken, is dat foar ISBN-13-nûmers dy't begjinne mei in oar foarheaksel dan “978-”, se noch altyd in “isbn”-fjild opnimme dat gewoan it ISBN-13-nûmer is mei de earste 3 nûmers ôfsnien (en it kontrôlesifer opnij berekkene). Dit is fansels ferkeard, mar sa dogge se it, dus wy hawwe it net feroare."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "In oar potinsjeel probleem dat jo tsjinkomme kinne, is it feit dat it “isbn13”-fjild duplikaten hat, dus jo kinne it net brûke as primêre kaai yn in databank. “isbn13”+“isbn”-fjilden kombinearre lykje wol unyk te wêzen."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Foar in eftergrûn oer Sci-Hub, ferwize wy jo nei syn <a %(a_scihub)s>offisjele webside</a>, <a %(a_wikipedia)s>Wikipedia-side</a>, en dizze <a %(a_radiolab)s>podcast-ynterview</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Merk op dat Sci-Hub sûnt 2021 <a %(a_reddit)s>beferzen is</a>. It wie earder beferzen, mar yn 2021 waarden in pear miljoen papieren tafoege. Noch altyd wurde in beheind oantal papieren tafoege oan de Libgen “scimag” kolleksjes, hoewol net genôch om nije bulk torrents te rjochtfeardigjen."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Wy brûke de Sci-Hub metadata sa't levere troch <a %(a_libgen_li)s>Libgen.li</a> yn syn “scimag” kolleksje. Wy brûke ek de <a %(a_dois)s>dois-2022-02-12.7z</a> dataset."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Merk op dat de “smarch” torrents <a %(a_smarch)s>ferâldere</a> binne en dêrom net opnommen binne yn ús torrentslist."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents op Anna’s Archive"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata en torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents op Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents op Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Updates op Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-side"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podcast-ynterview"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Uploads nei Anna's Argiven"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Oersjoch fan <a %(a1)s>datasets side</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Ferskate lytsere of ienmalige boarnen. Wy moedigje minsken oan om earst nei oare skaadbiblioteken te uploaden, mar soms hawwe minsken kolleksjes dy't te grut binne foar oaren om troch te sortearjen, mar net grut genôch om in eigen kategory te fertsjinjen."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "De “upload” kolleksje is opdield yn lytsere subkolleksjes, dy't oanjûn wurde yn de AACIDs en torrent-nammen. Alle subkolleksjes waarden earst deduplikearre tsjin de haadkolleksje, hoewol de metadata “upload_records” JSON-bestannen noch in protte ferwizings nei de orizjinele bestannen befetsje. Net-boekbestannen waarden ek fuorthelle út de measte subkolleksjes, en wurde typysk <em>net</em> neamd yn de “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "In protte subkolleksjes besteane sels út sub-sub-kolleksjes (bygelyks fan ferskate orizjinele boarnen), dy't fertsjintwurdige wurde as mappen yn de \"filepath\" fjilden."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "De subkolleksjes binne:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkolleksje"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notysjes"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "blêdzje"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "sykje"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Fan <a %(a_href)s>aaaaarg.fail</a>. Liket frij folslein te wêzen. Fan ús frijwilliger “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Fan in <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Hat in frij hege oerlap mei besteande papieren kolleksjes, mar hiel pear MD5-oerienkomsten, dus wy besletten it folslein te hâlden."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Scrape fan <q>iRead eBooks</q> (= fonetysk <q>ai rit i-books</q>; airitibooks.com), troch frijwilliger <q>j</q>. Komt oerien mei <q>airitibooks</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Fan in kolleksje <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Diels fan de orizjinele boarne, diels fan the-eye.eu, diels fan oare spegels."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Fan in privee boeken torrent webside, <a %(a_href)s>Bibliotik</a> (faak oantsjut as “Bib”), wêrfan boeken yn torrents bûn waarden troch namme (A.torrent, B.torrent) en ferspraat waarden fia the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Fan ús frijwilliger “bpb9v”. Foar mear ynformaasje oer <a %(a_href)s>CADAL</a>, sjoch de notysjes op ús <a %(a_duxiu)s>DuXiu dataset side</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Mear fan ús frijwilliger “bpb9v”, meast DuXiu-bestannen, lykas in map “WenQu” en “SuperStar_Journals” (SuperStar is it bedriuw efter DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Fan ús frijwilliger “cgiym”, Sineeske teksten fan ferskate boarnen (fertsjintwurdige as submappen), ynklusyf fan <a %(a_href)s>China Machine Press</a> (in grutte Sineeske útjouwer)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Net-Sineeske kolleksjes (fertsjintwurdige as submappen) fan ús frijwilliger “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Scrape fan boeken oer Sineeske arsjitektuer, troch frijwilliger <q>cm</q>: <q>Ik krige it troch in netwurk kwetsberens by de útjouwerij te eksploitearjen, mar dat gat is sûnt sluten</q>. Komt oerien mei <q>chinese_architecture</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Boeken fan akademyske útjouwerij <a %(a_href)s>De Gruyter</a>, sammele út in pear grutte torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Scrape fan <a %(a_href)s>docer.pl</a>, in Poalske bestânsdielingswebside rjochte op boeken en oare skreaune wurken. Skraapt yn let 2023 troch frijwilliger “p”. Wy hawwe gjin goede metadata fan de orizjinele webside (net iens bestânsútwreidings), mar wy filteren foar boekachtige bestannen en koene faak metadata út de bestannen sels helje."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direkt fan DuXiu, sammele troch frijwilliger “w”. Allinnich resinte DuXiu boeken binne direkt beskikber fia ebooks, dus de measten fan dizze moatte resint wêze."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Oerbleaune DuXiu-bestannen fan frijwilliger “m”, dy't net yn it DuXiu-eigendomlike PDG-formaat wiene (it haad <a %(a_href)s>DuXiu-dataset</a>). Sammele út in protte orizjinele boarnen, spitigernôch sûnder dy boarnen yn it bestânpaad te bewarjen."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Scrape fan eroatyske boeken, troch frijwilliger <q>do no harm</q>. Komt oerien mei <q>hentai</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Kolleksje skraapt fan in Japanske Manga útjouwer troch frijwilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Selektearre rjochterlike argiven fan Longquan</a>, oanbean troch frijwilliger “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Scrape fan <a %(a_href)s>magzdb.org</a>, in bûnsgenoat fan Library Genesis (it is keppele op de libgen.rs thússide) mar dy't net har bestannen direkt jaan woene. Krigen troch frijwilliger “p” yn let 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Ferskate lytse uploads, te lyts as eigen subkolleksje, mar fertsjintwurdige as mappen."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks fan AvaxHome, in Russyske bestânsdielingswebside."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Argyf fan kranten en tydskriften. Komt oerien mei <q>newsarch_magz</q> metadata yn <a %(a1)s><q>Oare metadata scrapes</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Scrape fan de <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Kolleksje fan frijwilliger “o” dy't Poalske boeken direkt fan orizjinele release (“scene”) websiden sammele hat."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Kombinearre kolleksjes fan <a %(a_href)s>shuge.org</a> troch frijwilligers “cgiym” en “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperiale Bibleteek fan Trantor”</a> (neamd nei de fiktive bibleteek), skraapt yn 2022 troch frijwilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-kolleksjes (fertsjintwurdige as mappen) fan frijwilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (troch <a %(a_sikuquanshu)s>Dizhi(迪志)</a> yn Taiwan), mebook (mebook.cc, 我的小书屋, myn lytse boekekamer — woz9ts: “Dizze side rjochtet him benammen op it dielen fan hege kwaliteit e-boekbestannen, guon dêrfan binne sels set troch de eigener. De eigener waard <a %(a_arrested)s>arrestearre</a> yn 2019 en immen makke in kolleksje fan bestannen dy't hy dielde.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Oerbleaune DuXiu-bestannen fan frijwilliger “woz9ts”, dy't net yn it DuXiu proprietêre PDG-formaat wiene (moatte noch omset wurde nei PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents troch Anna’s Argiven"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library skraap"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library hat syn woartels yn de <a %(a_href)s>Library Genesis</a> mienskip, en waard oarspronklik opstart mei harren gegevens. Sûnt dy tiid is it flink profesjonalisearre, en hat it in folle modernere ynterface. Se binne dêrom by steat om folle mear donaasjes te krijen, sawol finansjeel om harren webside te ferbetterjen, as donaasjes fan nije boeken. Se hawwe in grutte kolleksje opboud neist Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Bywurking fan febrewaris 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Ein 2022 waarden de bewearde oprjochters fan Z-Library arrestearre, en domeinen waarden yn beslach nommen troch de Amerikaanske autoriteiten. Sûnt dy tiid is de webside stadichoan wer online kommen. It is ûnbekend wa't it op it stuit bestjoert."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "De kolleksje bestiet út trije dielen. De oarspronklike beskriuwingspagina's foar de earste twa dielen binne hjirûnder bewarre bleaun. Jo hawwe alle trije dielen nedich om alle gegevens te krijen (útsein ferfongen torrents, dy't trochkrúst binne op de torrentside)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: ús earste release. Dit wie de aller earste release fan wat doe de “Pirate Library Mirror” (“pilimi”) neamd waard."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: twadde release, dizze kear mei alle bestannen yn .tar-bestannen ynpakt."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: ynkrementele nije releases, mei help fan it <a %(a_href)s>Anna’s Archive Containers (AAC) formaat</a>, no útbrocht yn gearwurking mei it Z-Library team."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents troch Anna’s Archive (metadata + ynhâld)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Foarbyldrekord op Anna’s Archive (oarspronklike kolleksje)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Foarbyldrekord op Anna's Argiven (\"zlib3\" kolleksje)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Haadwebside"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor domein"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blogpost oer Release 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blogpost oer Release 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib releases (oarspronklike beskriuwingspagina's)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Release 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "De earste spegel waard mei soarch krigen yn de rin fan 2021 en 2022. Op dit stuit is it wat ferâldere: it reflektearret de steat fan de kolleksje yn juny 2021. Wy sille dit yn de takomst bywurkje. Op it stuit rjochtsje wy ús op it útbringen fan dizze earste release."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Om't Library Genesis al bewarre is mei iepenbiere torrents, en opnaam is yn de Z-Library, hawwe wy in basis deduplikearring dien tsjin Library Genesis yn juny 2022. Hjirfoar hawwe wy MD5-hashes brûkt. Der is wierskynlik folle mear duplikaat ynhâld yn de bibleteek, lykas meardere bestânsformaten mei itselde boek. Dit is dreech om krekt te detektearjen, dus dogge wy dat net. Nei de deduplikearring hawwe wy mear as 2 miljoen bestannen oer, mei in totaal fan krekt ûnder 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "De kolleksje bestiet út twa dielen: in MySQL \".sql.gz\" dump fan de metadata, en de 72 torrentbestannen fan sawat 50-100GB elk. De metadata befettet de gegevens sa't se rapporteare binne troch de Z-Library webside (titel, auteur, beskriuwing, bestânstype), lykas de eigentlike bestânsgrutte en md5sum dy't wy observearre hawwe, om't dizze soms net oerienkomme. Der liket in oanbod fan bestannen te wêzen dêr't de Z-Library sels ferkearde metadata foar hat. Wy kinne ek yn guon isolearre gefallen ferkeard downloade bestannen hawwe, dy't wy yn de takomst besykje te detektearjen en te reparearjen."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "De grutte torrentbestannen befetsje de eigentlike boekgegevens, mei de Z-Library ID as de bestânsnamme. De bestânsútwreidings kinne rekonstruearre wurde mei de metadata dump."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "De kolleksje is in miks fan non-fiksje en fiksje ynhâld (net skieden lykas yn Library Genesis). De kwaliteit is ek tige fariabel."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Dizze earste release is no folslein beskikber. Merk op dat de torrentbestannen allinnich beskikber binne fia ús Tor spegel."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Release 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Wy hawwe alle boeken krigen dy't tafoege waarden oan de Z-Library tusken ús lêste spegel en augustus 2022. Wy hawwe ek weromgien en guon boeken skraapt dy't wy de earste kear mist hawwe. Alles byinoar is dizze nije kolleksje sawat 24TB. Op 'e nij is dizze kolleksje deduplikearre tsjin Library Genesis, om't der al torrents beskikber binne foar dy kolleksje."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "De gegevens binne ferlykber organisearre as de earste release. Der is in MySQL “.sql.gz” dump fan de metadata, dy't ek alle metadata fan de earste release omfettet, en dêrmei ferfangt. Wy hawwe ek wat nije kolommen tafoege:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: oft dit bestân al yn Library Genesis is, yn of de non-fiksje of fiksjekolleksje (matched by md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: yn hokker torrent dit bestân sit."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: ynsteld doe't wy it boek net downloade koene."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Wy hawwe dit de lêste kear neamd, mar om it te ferduidelikjen: \"bestânsnamme\" en \"md5\" binne de eigentlike eigenskippen fan it bestân, wylst \"bestânsnamme_rapporteare\" en \"md5_rapporteare\" binne wat wy fan Z-Library skraapt hawwe. Soms komme dizze twa net oerien mei elkoar, dus hawwe wy beide opnaam."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Foar dizze release hawwe wy de kollaasjewize feroare nei \"utf8mb4_unicode_ci\", dy't kompatibel wêze moat mei âldere ferzjes fan MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "De gegevensbestannen binne ferlykber mei de lêste kear, hoewol se folle grutter binne. Wy koenen ús gewoan net dwaande hâlde mei it meitsjen fan in protte lytsere torrentbestannen. “pilimi-zlib2-0-14679999-extra.torrent” befettet alle bestannen dy't wy yn de lêste release mist hawwe, wylst de oare torrents allegear nije ID-berik binne. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Update %(date)s:</strong> Wy hawwe de measte fan ús torrents te grut makke, wêrtroch torrentkliïnten muoite hawwe. Wy hawwe se fuorthelle en nije torrents útbrocht."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Update %(date)s:</strong> Der wiene noch te folle bestannen, dus hawwe wy se yn tar-bestannen ynpakt en nije torrents op 'e nij útbrocht."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Release 2 tafoeging (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dit is in ienkele ekstra torrent-bestân. It befettet gjin nije ynformaasje, mar it hat wat gegevens dy't in skoft duorje kinne om te berekkenjen. Dat makket it handich om te hawwen, om't it downloaden fan dizze torrent faak flugger is as it fanôf it begjin te berekkenjen. Benammen befettet it SQLite-yndeksen foar de tar-bestannen, foar gebrûk mei <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Faak Stelde Fragen (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Wat is Anna’s Argyf?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna’s Argyf</span> is in non-profit projekt mei twa doelen:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Preservaasje:</strong> It bewarjen fan alle kennis en kultuer fan 'e minskheid.</li><li><strong>Tagong:</strong> It beskikber meitsjen fan dizze kennis en kultuer foar elkenien yn 'e wrâld.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Al ús <a %(a_code)s>koade</a> en <a %(a_datasets)s>gegevens</a> binne folslein iepen boarne."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Behâld"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Wy bewarje boeken, papieren, strips, tydskriften en mear troch dizze materialen fan ferskate <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">skadelibraries</a>, offisjele biblioteken en oare kolleksjes byinoar te bringen op ien plak. Al dizze gegevens wurde foar altyd bewarre troch it maklik te meitsjen om it yn bulk te duplisearjen - mei torrents - wat resultearret yn in protte kopyen oer de hiele wrâld. Guon skadelibraries dogge dit sels al (bgl. Sci-Hub, Library Genesis), wylst Anna’s Archive oare biblioteken \"bevrijdt\" dy't gjin bulkdistribúsje oanbiede (bgl. Z-Library) of hielendal gjin skadelibraries binne (bgl. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Dizze brede distribúsje, kombinearre mei iepen-boarne koade, makket ús webside resistint tsjin sluting, en soarget foar de lange-termyn behâld fan 'e kennis en kultuer fan 'e minskheid. Lear mear oer <a href=\"/datasets\">ús datasets</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Wy skatte dat wy sawat <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% fan 'e wrâld syn boeken bewarre hawwe</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Tagong"

#, fuzzy
msgid "page.home.access.text"
msgstr "Wy wurkje mei partners om ús samlingen maklik en fergees tagonklik te meitsjen foar elkenien. Wy leauwe dat elkenien rjocht hat op de kollektive wiisheid fan 'e minskheid. En <a %(a_search)s>net ten koste fan auteurs</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Oere downloads yn de lêste 30 dagen. Oere trochsneed: %(hourly)s. Deistige trochsneed: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Wy leauwe sterk yn de frije stream fan ynformaasje, en it behâld fan kennis en kultuer. Mei dizze sykmasine bouwe wy op de skouders fan reuzen. Wy hawwe djip respekt foar it hurde wurk fan de minsken dy't de ferskate skaadbiblioteken makke hawwe, en wy hoopje dat dizze sykmasine harren berik ferbreedzje sil."

#, fuzzy
msgid "page.about.text3"
msgstr "Om op 'e hichte te bliuwen fan ús foarútgong, folgje Anna op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Foar fragen en feedback nim dan kontakt op mei Anna by %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Hoe kin ik helpe?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Folgje ús op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Fertel oaren oer Anna’s Archive op Twitter, Reddit, Tiktok, Instagram, yn jo lokale kafee of biblioteek, of wêr't jo ek geane! Wy leauwe net yn poartebewaking — as wy offline helle wurde, komme wy gewoan werom, om't al ús koade en gegevens folslein iepen boarne binne.</li><li>3. As jo kinne, beskôgje dan <a href=\"/donate\">te donearjen</a>.</li><li>4. Help ús webside <a href=\"https://translate.annas-software.org/\">te oersetten</a> yn ferskate talen.</li><li>5. As jo in software-ûntwikkelder binne, beskôgje dan by te dragen oan ús <a href=\"https://annas-software.org/\">iepen boarne</a>, of ús <a href=\"/datasets\">torrents</a> te seedjen.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Wy hawwe no ek in syngronisearre Matrix-kanaal by %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. As jo in befeiligingsûndersiker binne, kinne wy jo feardichheden brûke foar sawol oanfallende as ferdigenjende taken. Sjoch ús <a %(a_security)s>Befeiligings</a> side."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Wy sykje nei saakkundigen op it mêd fan betellingen foar anonyme keaplju. Kinne jo ús helpe om mear handige manieren ta te foegjen om te donearjen? PayPal, WeChat, kadokaarten. As jo immen kenne, nim dan kontakt mei ús op."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Wy sykje altyd nei mear serverkapasiteit."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Jo kinne helpe troch bestânsproblemen te melden, opmerkingen te litten, en listen te meitsjen op dizze webside. Jo kinne ek helpe troch <a %(a_upload)s>mear boeken te uploaden</a>, of bestânsproblemen of opmaak fan besteande boeken te ferbetterjen."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Meitsje of help de Wikipedia-side foar Anna’s Archive yn jo taal te ûnderhâlden."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Wy sykje nei lytse, smaakfolle advertinsjes. As jo advertearje wolle op Anna’s Argyf, lit it ús dan witte."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Wy soene it geweldich fine as minsken <a %(a_mirrors)s>spegeltsjes</a> opsette, en wy sille dit finansjeel stypje."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Foar mear wiidweidige ynformaasje oer hoe't jo frijwilliger wurde kinne, sjoch ús <a %(a_volunteering)s>Frijwilligers & Bounties</a> side."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Wêrom binne de stadige downloads sa stadich?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Wy hawwe letterlik net genôch middels om elkenien yn 'e wrâld hege-snelheid downloads te jaan, hoe graach wy dat ek wolle. As in rike beskermhear ús dit foar ús leverje wol, soe dat ynkringend wêze, mar oant dan besykje wy ús bêst. Wy binne in non-profit projekt dat amper troch donaasjes ûnderhâlden wurde kin."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dêrom hawwe wy twa systemen foar fergese downloads ymplemintearre, mei ús partners: dielde servers mei stadige downloads, en wat fluggere servers mei in wachtlist (om it tal minsken dat tagelyk downloadt te ferminderjen)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Wy hawwe ek <a %(a_verification)s>browserferifikaasje</a> foar ús stadige downloads, om't oars bots en scrapers se misbrûke, wat dingen noch stadiger makket foar legitime brûkers."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Merk op dat, by it brûken fan de Tor Browser, jo miskien jo feiligensynstellingen oanpasse moatte. Op de leechste fan de opsjes, neamd “Standert”, slagget de Cloudflare turnstile útdaging. Op de hegere opsjes, neamd “Feiliger” en “Feilichst”, mislearret de útdaging."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Foar grutte bestannen kinne stadige downloads soms yn 'e midden brekke. Wy riede oan om in downloadmanager (lykas JDownloader) te brûken om automatysk grutte downloads te hervatten."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donaasje FAQ"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Fernije lidmaatskippen automatysk?</div> Lidmaatskippen <strong>dogge dat net</strong> automatysk. Jo kinne lid wurde foar sa lang of koart as jo wolle."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kin ik myn lidmaatskip opwurdearje of meardere lidmaatskippen krije?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Hawwe jo oare betelmethoden?</div> Op it stuit net. In protte minsken wolle net dat argiven lykas dit bestean, dus wy moatte foarsichtich wêze. As jo ús kinne helpe om oare (mear handige) betelmethoden feilich yn te stellen, nim dan kontakt op mei %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Wat betsjutte de berik per moanne?</div> Jo kinne oan de legere kant fan in berik komme troch alle koartingen ta te passen, lykas it kiezen fan in perioade langer as in moanne."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Wêr besteegje jo donaasjes oan?</div> 100%% giet nei it bewarjen en tagonklik meitsjen fan 'e kennis en kultuer fan 'e wrâld. Op it stuit besteegje wy it meast oan servers, opslach en bandbreedte. Gjin jild giet nei teamleden persoanlik."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kin ik in grutte donaasje dwaan?</div> Dat soe geweldich wêze! Foar donaasjes fan mear as in pear tûzen dollar, nim dan direkt kontakt mei ús op by %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kin ik in donaasje dwaan sûnder lid te wurden?</div> Wis. Wy akseptearje donaasjes fan elk bedrach op dit Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Hoe kin ik nije boeken uploade?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "As alternatyf kinne jo se uploade nei Z-Library <a %(a_upload)s>hjir</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Foar lytse uploads (oant 10.000 bestannen) upload se asjebleaft nei sawol %(first)s as %(second)s."

#, fuzzy
msgid "page.upload.text1"
msgstr "Foar no riede wy oan om nije boeken te uploaden nei de Library Genesis forks. Hjir is in <a %(a_guide)s>hânige gids</a>. Merk op dat beide forks dy't wy op dizze webside yndeksearje, út ditselde uploadsysteem lûke."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Foar Libgen.li, soargje derfoar dat jo earst ynlogge op <a %(a_forum)s >harren forum</a> mei brûkersnamme %(username)s en wachtwurd %(password)s, en gean dan werom nei harren <a %(a_upload_page)s >uploadside</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "As jo e-mailadres net wurket op de Libgen-forums, riede wy oan om <a %(a_mail)s>Proton Mail</a> (fergees) te brûken. Jo kinne ek <a %(a_manual)s>manuell oanfreegje</a> om jo akkount te aktivearjen."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Merk op dat mhut.org bepaalde IP-berik blokkearret, dus in VPN kin nedich wêze."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Foar grutte uploads (mear as 10.000 bestannen) dy't net akseptearre wurde troch Libgen of Z-Library, nim dan kontakt mei ús op fia %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Om akademyske papieren te uploaden, upload se ek (njonken Library Genesis) nei <a %(a_stc_nexus)s>STC Nexus</a>. Sy binne de bêste skaadbiblioteek foar nije papieren. Wy hawwe se noch net yntegrearre, mar dat sille wy op in stuit dwaan. Jo kinne har <a %(a_telegram)s>upload bot op Telegram</a> brûke, of kontakt opnimme mei it adres dat yn harren fêstpinde berjocht stiet as jo te folle bestannen hawwe om op dizze manier te uploaden."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Hoe freegje ik boeken oan?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Op dit stuit kinne wy gjin boekoanfragen ferwurkje."

#, fuzzy
msgid "page.request.forums"
msgstr "Meitsje jo oanfragen asjebleaft op Z-Library of Libgen forums."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Stjoer ús gjin boekoanfragen fia e-mail."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Sammelje jo metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Dat dogge wy yndie."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Ik haw 1984 fan George Orwell downloade, sil de plysje by my oan de doar komme?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Meitsje jo gjin soargen, der binne in protte minsken dy't downloade fan websiden dy't troch ús keppele binne, en it is tige seldsum om yn problemen te kommen. Mar om feilich te bliuwen, riede wy oan om in VPN (betelle) te brûken, of <a %(a_tor)s>Tor</a> (fergees)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Hoe bewarje ik myn sykynstellingen?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Selektearje de ynstellings dy't jo leuk fine, hâld it sykfak leech, klik op “Sykje”, en markearje dan de side mei de blêdwizerfunksje fan jo browser."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Hawwe jo in mobile app?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Wy hawwe gjin offisjele mobile app, mar jo kinne dizze webside as in app ynstallearje."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik op it trijepuntsmenu yn de boppeste rjochterhoeke, en selektearje “Tafoegje oan startskerm”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik op de knop “Share” oan de ûnderkant, en selektearje “Add to Home Screen”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Hawwe jo in API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Wy hawwe ien stabile JSON API foar leden, foar it krijen fan in snelle download-URL: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentaasje binnen de JSON sels)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Foar oare gebrûksgefallen, lykas it iterearjen troch al ús bestannen, it bouwen fan oanpaste sykfunksjes, ensafuorthinne, riede wy oan <a %(a_generate)s>it generearjen</a> of <a %(a_download)s>it downloaden</a> fan ús ElasticSearch en MariaDB databases. De rauwe gegevens kinne manuell ferkend wurde <a %(a_explore)s>troch JSON-bestannen</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Us list mei rauwe torrents kin ek downloade wurde as <a %(a_torrents)s>JSON</a>."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Ik wol graach helpe mei seedjen, mar ik haw net folle skiifromte."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Brûk de <a %(a_list)s>torrentlistgenerator</a> om in list fan torrents te generearjen dy't it meast nedich binne om te torrenten, binnen jo opslachromte limiten."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "De torrents binne te stadich; kin ik de gegevens direkt fan jo downloade?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ja, sjoch de <a %(a_llm)s>LLM data</a> side."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Kin ik allinich in subset fan 'e bestannen downloade, lykas allinich in bepaalde taal of ûnderwerp?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Koart antwurd: net maklik."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Lang antwurd:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "De measte torrents befetsje de bestannen direkt, wat betsjut dat jo torrentkliïnten kinne ynstruearje om allinich de nedige bestannen te downloaden. Om te bepalen hokker bestannen te downloaden, kinne jo ús metadata <a %(a_generate)s>generearje</a>, of ús ElasticSearch- en MariaDB-databases <a %(a_download)s>downloaden</a>. Spitigernôch befetsje in oantal torrentkolleksjes .zip- of .tar-bestannen oan de woartel, yn dat gefal moatte jo de hiele torrent downloade foardat jo yndividuele bestannen selektearje kinne."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Wy hawwe lykwols <a %(a_ideas)s>wat ideeën</a> foar it lêste gefal.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Gjin maklike ark om torrents te filterjen binne noch beskikber, mar wy ferwolkomje bydragen."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Hoe geane jo om mei duplikaten yn de torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Wy besykje minimale duplikaasje of oerlap tusken de torrents yn dizze list te hâlden, mar dit kin net altyd berikt wurde, en hinget sterk ôf fan it belied fan de boarnebiblioteken. Foar biblioteken dy't harren eigen torrents útbringe, is it bûten ús hannen. Foar torrents útbrocht troch Anna’s Argyf, deduplikearje wy allinnich basearre op MD5 hash, wat betsjut dat ferskillende ferzjes fan itselde boek net deduplikearre wurde."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Kin ik de torrentlist as JSON krije?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ja."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Ik sjoch gjin PDFs of EPUBs yn de torrents, allinnich binêre bestannen? Wat moat ik dwaan?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Dit binne eins PDFs en EPUBs, se hawwe gewoan gjin útwreiding yn in soad fan ús torrents. Der binne twa plakken wêr't jo de metadata foar torrentbestannen fine kinne, ynklusyf de bestânstypen/útwreidingen:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Elke kolleksje of release hat syn eigen metadata. Bygelyks, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> hawwe in oerienkommende metadata-database host op de Libgen.rs-webside. Wy keppelje typysk nei relevante metadata-boarnen fan elke kolleksje’s <a %(a_datasets)s>dataset-side</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Wy riede oan om ús ElasticSearch- en MariaDB-databases te <a %(a_generate)s>generearjen</a> of te <a %(a_download)s>downloaden</a>. Dizze befetsje in mapping foar elk rekord yn Anna’s Archive nei de oerienkommende torrentbestannen (as beskikber), ûnder \"torrent_paths\" yn de ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Wêrom kin myn torrent-kliïnt guon fan jo torrent-bestannen / magneetkeppelings net iepenje?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Guon torrent-kliïnten stypje gjin grutte stikgrutte, dy't in protte fan ús torrents hawwe (foar nijere dogge wy dit net mear - sels al is it neffens de spesifikaasjes!). Besykje in oare kliïnt as jo hjir tsjinoan rinne, of klagje by de makkers fan jo torrent-kliïnt."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Hawwe jo in ferantwurdlike iepenbieringprogramma?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Wy ferwolkomje befeiligingsûndersikers om nei kwetsberens yn ús systemen te sykjen. Wy binne grutte foarstanners fan ferantwurde iepenbiering. Nim kontakt mei ús op <a %(a_contact)s>hjir</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Wy kinne op it stuit gjin bug bounties útrikke, útsein foar kwetsberens dy't de <a %(a_link)s >potinsje hawwe om ús anonimiteit te kompromittearjen</a>, dêrfoar biede wy bounties yn it berik fan $10k-50k. Wy wolle yn 'e takomst in bredere omfang foar bug bounties oanbiede! Tink derom dat sosjale engineering-oanfallen bûten de omfang falle."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "As jo ynteressearre binne yn offensive security, en wolle helpe om de kennis en kultuer fan 'e wrâld te argivearjen, nim dan kontakt mei ús op. D'r binne in protte manieren wêrop jo helpe kinne."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Binne der mear boarnen oer Anna’s Argyf?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna’s Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — reguliere updates"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna’s Software</a> — ús iepen boarne koade"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Oersette op Anna’s Software</a> — ús oersettingssysteem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — oer de gegevens"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternative domeinen"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — mear oer ús (help asjebleaft dizze side bywurkje, of meitsje ien foar jo eigen taal!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Hoe meld ik auteursrjochtferbrek?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Wy hostje hjir gjin auteursrjochtlik beskerme materialen. Wy binne in sykmasine, en sa indexearje wy allinnich metadata dy't al iepenbier beskikber is. By it downloaden fan dizze eksterne boarnen, advisearje wy om de wetten yn jo jurisdiksje te kontrolearjen mei respekt foar wat tastien is. Wy binne net ferantwurdlik foar ynhâld host troch oaren."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "As jo klachten hawwe oer wat jo hjir sjogge, is it bêste om kontakt op te nimmen mei de orizjinele webside. Wy helje har wizigingen regelmjittich yn ús databank. As jo echt tinke dat jo in jildige DMCA-klacht hawwe dêr't wy op reagearje moatte, folje dan it <a %(a_copyright)s>DMCA / Copyright claim form</a> yn. Wy nimme jo klachten serieus en sille sa gau mooglik op jo weromkomme."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Ik haatsje hoe't jo dit projekt rinne!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Wy wolle elkenien ek graach derop wize dat al ús koade en gegevens folslein iepen boarne binne. Dit is unyk foar projekten lykas ús — wy binne net bewust fan in oar projekt mei in fergelykber massyf katalogus dat ek folslein iepen boarne is. Wy ferwolkomje elkenien dy't tinkt dat wy ús projekt min beheare om ús koade en gegevens te nimmen en harren eigen skaadbiblioteek op te setten! Wy sizze dit net út wrok of sa — wy tinke echt dat dit geweldich wêze soe, om't it de standert foar elkenien ferheegje soe, en de erfenis fan 'e minskheid better bewarje soe."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Hawwe jo in uptime-monitor?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Sjoch asjebleaft <a %(a_href)s>dit treflike projekt</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Hoe kin ik boeken of oare fysike materialen donearje?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Stjoer se asjebleaft nei de <a %(a_archive)s>Internet Archive</a>. Sy sille se goed bewarje."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Wa is Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Jo binne Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Wat binne jo favorite boeken?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Hjir binne wat boeken dy't spesjale betsjutting hawwe foar de wrâld fan skaadbiblioteken en digitale preservaasje:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Jo hawwe hjoed gjin rappe downloads mear oer."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Wurdt lid om snelle downloads te brûken."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Wy stypje no Amazon-cadeaukaarten, kredyt- en debitkaarten, crypto, Alipay, en WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Folsleine databank"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Boeken, papieren, tydskriften, strips, biblioteekrecords, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Sykje"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub hat <a %(a_paused)s>it uploaden</a> fan nije papieren pauzeare."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB is in fuortsetting fan Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direkte tagong ta %(count)s akademyske papieren"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Iepenje"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "As jo in <a %(a_member)s>lid</a> binne, is browserferifikaasje net nedich."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Langduorjend argyf"

#, fuzzy
msgid "page.home.archive.body"
msgstr "De datasets dy't brûkt wurde yn Anna’s Archive binne folslein iepen, en kinne yn bulk spegele wurde mei torrents. <a %(a_datasets)s>Lear mear…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Jo kinne enoarm helpe troch torrents te seedjen. <a %(a_torrents)s>Lear mear…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Wy hawwe de grutste kolleksje fan heechweardige tekstgegevens yn 'e wrâld. <a %(a_llm)s>Lear mear…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Spegels: oprop foar frijwilligers"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Op syk nei frijwilligers"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "As in non-profit, iepen-boarne projekt, sykje wy altyd nei minsken om te helpen."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "As jo in heech-risiko anonyme betellingsferwurker rinne, nim dan kontakt mei ús op. Wy sykje ek minsken dy't lytse smaakfolle advertinsjes wolle pleatse. Alle opbringsten geane nei ús preservaasje-ynspanningen."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Anna’s Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS downloads"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Alle downloadlinks foar dit bestân: <a %(a_main)s>Haadside fan it bestân</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(jo moatte miskien meardere kearen besykje mei IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Om flugger te downloaden en de browserkontrôles oer te slaan, <a %(a_membership)s>wur in lid</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Foar bulk spegeljen fan ús kolleksje, sjoch de <a %(a_datasets)s>Datasets</a> en <a %(a_torrents)s>Torrents</a> siden."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-data"

#, fuzzy
msgid "page.llm.intro"
msgstr "It is goed begrepen dat LLM's bloeie op heechweardige data. Wy hawwe de grutste kolleksje fan boeken, papieren, tydskriften, ensfh. yn 'e wrâld, dy't guon fan 'e heechste kwaliteit tekstboarnen binne."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unike skaal en berik"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Us kolleksje befettet mear as hûndert miljoen bestannen, ynklusyf akademyske tydskriften, learboeken, en tydskriften. Wy berikke dizze skaal troch grutte besteande repositories te kombinearjen."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Guon fan ús boarnekolleksjes binne al yn bulk beskikber (Sci-Hub, en dielen fan Libgen). Oare boarnen hawwe wy sels befrijd. <a %(a_datasets)s>Datasets</a> toant in folslein oersjoch."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Us kolleksje omfettet miljoenen boeken, papieren, en tydskriften fan foar it e-boek tiidrek. Grutte dielen fan dizze kolleksje binne al OCR'd, en hawwe al in bytsje ynterne oerlap."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Hoe't wy helpe kinne"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Wy kinne hege-snelheid tagong biede ta ús folsleine kolleksjes, lykas ta net-frijlitten kolleksjes."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Dit is tagong op bedriuwsnivo dy't wy kinne leverje foar donaasjes yn it berik fan tsientûzenen USD. Wy binne ek ree om dit te ruiljen foar heechweardige kolleksjes dy't wy noch net hawwe."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Wy kinne jo werombetelje as jo ús kinne foarsjen mei ferbettering fan ús data, lykas:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Oerlap ferwiderje (deduplikearje)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Tekst- en metadata-ekstraksje"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Stypje langduorjende argyfering fan minsklike kennis, wylst jo bettere data krije foar jo model!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontakt mei ús opnimme</a> om te besprekken hoe't wy gearwurkje kinne."

#, fuzzy
msgid "page.login.continue"
msgstr "Trochgean"

#, fuzzy
msgid "page.login.please"
msgstr "Asjebleaft <a %(a_account)s>ynlogge</a> om dizze side te besjen.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna’s Argyf is tydlik offline foar ûnderhâld. Kom oer in oere werom."

#, fuzzy
msgid "page.metadata.header"
msgstr "Ferbetterje metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Jo kinne helpe by it behâld fan boeken troch metadata te ferbetterjen! Lês earst de eftergrûn oer metadata op Anna’s Argyf, en lear dan hoe't jo metadata kinne ferbetterje troch te keppeljen mei Open Library, en fertsjinje in fergees lidmaatskip op Anna’s Argyf."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Eftergrûn"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "As jo in boek besjogge op Anna’s Argyf, kinne jo ferskate fjilden sjen: titel, auteur, útjouwer, edysje, jier, beskriuwing, bestânsnamme, en mear. Al dy stikjes ynformaasje wurde <em>metadata</em> neamd."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Om't wy boeken kombinearje út ferskate <em>boarnenbiblioteken</em>, toane wy hokker metadata beskikber is yn dy boarnebiblioteek. Bygelyks, foar in boek dat wy krigen hawwe fan Library Genesis, sille wy de titel út de database fan Library Genesis sjen litte."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Soms is in boek oanwêzich yn <em>meardere</em> boarnebiblioteken, dy't ferskillende metadatafjilden hawwe kinne. Yn dat gefal toane wy gewoan de langste ferzje fan elk fjild, om't dy hopelik de meast nuttige ynformaasje befettet! Wy sille de oare fjilden noch ûnder de beskriuwing sjen litte, bygelyks as \"alternative titel\" (mar allinich as se oars binne)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Wy helje ek <em>koades</em> lykas identifiers en classifiers út de boarnebiblioteek. <em>Identifiers</em> fertsjintwurdigje unyk in bepaalde edysje fan in boek; foarbylden binne ISBN, DOI, Open Library ID, Google Books ID, of Amazon ID. <em>Classifiers</em> groepearje meardere ferlykbere boeken; foarbylden binne Dewey Decimal (DCC), UDC, LCC, RVK, of GOST. Soms binne dizze koades eksplisyt keppele yn boarnebiblioteken, en soms kinne wy se helje út de bestânsnamme of beskriuwing (foarnaamste ISBN en DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Wy kinne identifiers brûke om records te finen yn <em>metadata-allinnich kolleksjes</em>, lykas OpenLibrary, ISBNdb, of WorldCat/OCLC. Der is in spesifike <em>metadata ljepper</em> yn ús sykmasine as jo dy kolleksjes blêdzje wolle. Wy brûke oerienkommende records om ûntbrekkende metadatafjilden yn te foljen (bygelyks as in titel ûntbrekt), of bygelyks as \"alternative titel\" (as der in besteande titel is)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Om krekt te sjen wêr't metadata fan in boek wei kaam, sjoch de <em>“Technyske details” ljepper</em> op in boekside. It hat in keppeling nei de rauwe JSON foar dat boek, mei oanwizings nei de rauwe JSON fan de orizjinele records."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Foar mear ynformaasje, sjoch de folgjende siden: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Sykje (metadata ljepper)</a>, <a %(a_codes)s>Koades Explorer</a>, en <a %(a_example)s>Foarbyld metadata JSON</a>. Uteinlik kin al ús metadata <a %(a_generated)s>generearre</a> of <a %(a_downloaded)s>downloade</a> wurde as ElasticSearch en MariaDB databases."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library keppeling"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Dus as jo in bestân tsjinkomme mei minne metadata, hoe moatte jo it reparearje? Jo kinne nei de boarnebiblioteek gean en har prosedueres folgje foar it reparearjen fan metadata, mar wat te dwaan as in bestân oanwêzich is yn meardere boarnebiblioteken?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Der is ien identifier dy't spesjaal behannele wurdt op Anna’s Argyf. <strong>It annas_archive md5 fjild op Open Library oerskriuwt altyd alle oare metadata!</strong> Litte wy earst in bytsje weromgean en leare oer Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library waard oprjochte yn 2006 troch Aaron Swartz mei it doel fan \"ien webside foar elk boek dat ea publisearre is\". It is in soarte fan Wikipedia foar boekmetadata: elkenien kin it bewurkje, it is frij lisinsjeare, en kin yn bulk downloade wurde. It is in boekdatabase dy't it meast oerienkomt mei ús missy - yn feite is Anna’s Argyf ynspirearre troch de fisy en it libben fan Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Ynstee fan it tsjil op 'e nij út te finen, hawwe wy besletten ús frijwilligers troch te stjoeren nei Open Library. As jo in boek sjogge dat ferkearde metadata hat, kinne jo op de folgjende manier helpe:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Gean nei de <a %(a_openlib)s>Open Library webside</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Fyn it korrekte boekrecord. <strong>WARSKÔGING:</strong> wês wis dat jo de korrekte <strong>edysje</strong> selektearje. Yn Open Library hawwe jo \"wurken\" en \"edysjes\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "In \"wurk\" kin wêze \"Harry Potter en de Stien fan de Wizen\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "In \"edysje\" kin wêze:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "De earste edysje fan 1997 útjûn troch Bloomsbery mei 256 siden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "De paperback edysje fan 2003 útjûn troch Raincoast Books mei 223 siden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "De Poalske oersetting fan 2000 “Harry Potter I Kamie Filozoficzn” troch Media Rodzina mei 328 siden."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Al dy edysjes hawwe ferskillende ISBN's en ferskillende ynhâld, soargje derfoar dat jo de juste kieze!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Bewurkje it rekord (of meitsje it as it net bestiet), en foegje safolle nuttige ynformaasje ta as jo kinne! Jo binne hjir no dochs, meitsje it rekord mar echt geweldich."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Under “ID-nûmers” selektearje “Anna’s Archive” en foegje de MD5 fan it boek ta fan Anna’s Archive. Dit is de lange string fan letters en sifers nei “/md5/” yn de URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Besykje oare triemen te finen yn Anna’s Archive dy't ek oerienkomme mei dit rekord, en foegje dy ek ta. Yn de takomst kinne wy dy groepearje as duplikaten op de sykpagina fan Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "As jo klear binne, skriuw dan de URL op dy't jo krekt bywurke hawwe. As jo teminsten 30 records hawwe bywurke mei Anna’s Archive MD5's, stjoer ús dan in <a %(a_contact)s>e-mail</a> en stjoer ús de list. Wy jouwe jo in fergees lidmaatskip foar Anna’s Archive, sadat jo dit wurk makliker dwaan kinne (en as tank foar jo help). Dizze moatte heechweardige bewurkingen wêze dy't substansjele hoemannichten ynformaasje tafoegje, oars wurdt jo fersyk ôfwiisd. Jo fersyk wurdt ek ôfwiisd as ien fan de bewurkingen weromset of korrizjearre wurdt troch Open Library moderators."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Tink derom dat dit allinich wurket foar boeken, net foar akademyske papieren of oare soarten triemen. Foar oare soarten triemen riede wy noch altyd oan om de boarnebiblioteek te finen. It kin in pear wiken duorje foardat feroarings opnommen wurde yn Anna’s Archive, om't wy de lêste Open Library data dump moatte downloade en ús sykindex opnij generearje moatte."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spegels: oprop foar frijwilligers"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Om de wjerstân fan Anna’s Argyf te ferheegjen, sykje wy frijwilligers om spegels te draaien."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Wy sykje dit:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Jo draaie de iepen boarne koadebasis fan Anna’s Archive, en jo bywurkje sawol de koade as de gegevens regelmjittich."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Jo ferzje is dúdlik ûnderskieden as in spegel, bygelyks “Bob’s Archive, in Anna’s Archive spegel”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Jo binne ree om de risiko's te nimmen dy't ferbûn binne mei dit wurk, dy't signifikant binne. Jo hawwe in djip begryp fan de operasjonele feiligens dy't nedich is. De ynhâld fan <a %(a_shadow)s>dizze</a> <a %(a_pirate)s>berjochten</a> is foar jo fanselssprekkend."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Jo binne ree om by te dragen oan ús <a %(a_codebase)s>koadebasis</a> — yn gearwurking mei ús team — om dit te realisearjen."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Yn it begjin sille wy jo gjin tagong jaan ta ús partner server downloads, mar as dingen goed geane, kinne wy dat mei jo diele."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Hostingkosten"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Wy binne ree om hosting- en VPN-útjeften te dekken, yn earste ynstânsje oant $200 per moanne. Dit is genôch foar in basis sykserver en in DMCA-beskerme proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Wy sille allinnich betelje foar hosting as jo alles opset hawwe, en hawwe oantoand dat jo it argyf bywurkje kinne mei updates. Dit betsjut dat jo de earste 1-2 moannen út eigen bûse betelje moatte."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Jo tiid sil net fergoede wurde (en ús ek net), om't dit suver frijwilligerswurk is."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "As jo signifikant belutsen reitsje by de ûntwikkeling en operaasjes fan ús wurk, kinne wy besprekke om mear fan 'e donaasje-ynkomsten mei jo te dielen, sadat jo se nedichs brûke kinne."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Begjinne"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Nim asjebleaft <strong>gjin kontakt mei ús op</strong> om tastimming te freegjen, of foar basisfragen. Daden sprekke lûder as wurden! Alle ynformaasje is der, dus gean gewoan troch mei it opsetten fan jo spegel."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Fiel jo frij om tickets of merge requests te pleatsen op ús Gitlab as jo problemen tsjinkomme. Wy moatte miskien wat spegel-spesifike funksjes mei jo bouwe, lykas it rebranden fan “Anna’s Archive” nei de namme fan jo webside, (yn earste ynstânsje) it útskeakeljen fan brûkersakkounts, of it keppeljen werom nei ús haadside fan boekesiden."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "As jo jo spegel rinne hawwe, nim dan asjebleaft kontakt mei ús op. Wy soene graach jo OpSec besjen, en as dat solid is, sille wy nei jo spegel keppelje, en tichter mei jo gearwurkje."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Tank yn it foar oan elkenien dy't op dizze manier bydrage wol! It is net foar de swakke fan hert, mar it soe de duorsumens fan de grutste echt iepen bibleteek yn de minsklike skiednis fersterkje."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Download fan partnerwebside"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Trage downloads binne allinnich beskikber fia de offisjele webside. Besykje %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Stadige downloads binne net beskikber fia Cloudflare VPN's of oars fan Cloudflare IP-adressen."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Wachtsje asjebleaft <span %(span_countdown)s>%(wait_seconds)s</span> sekonden om dit bestân te downloaden."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Brûk de folgjende URL om te downloaden: <a %(a_download)s>Download no</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Tank foar it wachtsjen, dit hâldt de webside fergees tagonklik foar elkenien! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Warskôging: der binne in protte downloads fan jo IP-adres yn de lêste 24 oeren. Downloads kinne stadiger wêze as gewoanlik."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Downloads fan jo IP-adres yn de lêste 24 oeren: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "As jo in VPN brûke, in dield ynternetferbining, of jo ISP dielt IP's, kin dizze warskôging dêrtroch komme."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Om elkenien de kâns te jaan om bestannen fergees te downloaden, moatte jo wachtsje foardat jo dit bestân kinne downloade."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Fiel jo frij om fierder te blêdzjen yn Anna’s Argyf yn in oar ljepblêd wylst jo wachtsje (as jo browser it ferfarskjen fan eftergrûnljeppen stipet)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Fiel jo frij om te wachtsjen oant meardere download siden tagelyk lade (mar download asjebleaft mar ien bestân tagelyk per server)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Sadree't jo in downloadlink krije, is it jildich foar ferskate oeren."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Anna’s Argyf"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Opnimme yn Anna’s Argyf"

#, fuzzy
msgid "page.scidb.download"
msgstr "Download"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Om de tagonklikens en langduorjende behâld fan minsklike kennis te stypjen, wurde in <a %(a_donate)s>lid</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "As in bonus, 🧬&nbsp;SciDB ladet rapper foar leden, sûnder limiten."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Net wurkje? Besykje <a %(a_refresh)s>te ferfarskjen</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Gjin foarbyld beskikber. Download it bestân fan <a %(a_path)s>Anna’s Argyf</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB is in fuortsetting fan Sci-Hub, mei syn bekende interface en direkte besjen fan PDFs. Fier jo DOI yn om te besjen."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Wy hawwe de folsleine Sci-Hub kolleksje, en ek nije papieren. De measten kinne direkt besjoen wurde mei in fertroude ynterface, fergelykber mei Sci-Hub. Guon kinne downloade wurde fia eksterne boarnen, yn dat gefal toane wy keppelings nei dy."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Sykje"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nije syktocht"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Allinnich opnimme"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Útslute"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Net kontrolearre"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Download"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Tydskriftartikels"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digitale Liening"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titel, auteur, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Sykje"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Sykynstellingen"

#, fuzzy
msgid "page.search.submit"
msgstr "Sykje"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "De syktocht duorre te lang, wat gewoan is foar brede fragen. De filtertellingen kinne net akkuraat wêze."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "De syktocht duorre te lang, wat betsjut dat jo miskien ûnkrekte resultaten sjogge. Soms helpt it <a %(a_reload)s>opnij laden</a> fan de side."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Display"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "List"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Avansearre"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Sykje beskriuwings en metadata opmerkingen"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Foegje spesifyk sykfjild ta"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(sykje spesifyk fjild)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Jier publisearre"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Ynhâld"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Bestânstype"

#, fuzzy
msgid "page.search.more"
msgstr "mear…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Tagong"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Boarne"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "skraapt en iepen boarne makke troch AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Taal"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Sortearje op"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Meast relevant"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Nijste"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publikaasjejier)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Aldste"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Grutste"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(bestânsgrutte)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Lytste"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(iepen boarne)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Willekeurich"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "De sykyndeks wurdt moannebliks bywurke. It omfiemet op it stuit ynfieringen oant %(last_data_refresh_date)s. Foar mear technyske ynformaasje, sjoch de %(link_open_tag)sdatasets side</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Om de sykindex te ferkennen troch koades, brûk de <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Typ yn it fakje om ús katalogus fan %(count)s direkt te downloaden bestannen te sykjen, dy't wy <a %(a_preserve)s>foar ivich bewarje</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Yn feite kin elkenien helpe dizze triemmen te bewarjen troch ús <a %(a_torrents)s>ienfâldige list fan torrents</a> te seedjen."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Wy hawwe op it stuit de meast wiidweidige iepen katalogus fan boeken, papieren en oare skreaune wurken yn 'e wrâld. Wy spegelje Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>en mear</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "As jo oare “skaadbiblioteken” fine dy't wy spegelje moatte, of as jo fragen hawwe, nim dan kontakt mei ús op by %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Foar DMCA / copyright claims <a %(a_copyright)s>klik hjir</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: brûk toetseboerdfluchtoetsen “/” (syk fokus), “enter” (sykje), “j” (omheech), “k” (omleech), “<” (foarige side), “>” (folgjende side) foar fluggere navigaasje."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Op syk nei papieren?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Typ yn it fak om ús katalogus fan %(count)s akademyske papieren en tydskriftartikels te sykjen, dy't wy <a %(a_preserve)s>foar ivich bewarje</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Typ yn it fakje om te sykjen nei bestannen yn digitale lienbiblioteken."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Dizze sykindeks omfiemet op it stuit metadata fan de Controlled Digital Lending-biblioteek fan de Internet Archive. <a %(a_datasets)s>Mear oer ús datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Foar mear digitale lienbiblioteken, sjoch <a %(a_wikipedia)s>Wikipedia</a> en de <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Typ yn it fak om te sykjen nei metadata út bibleteken. Dit kin nuttich wêze by <a %(a_request)s>it oanfreegjen fan in bestân</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Dizze sykindex omfiemet op it stuit metadata fan ferskate metadata-boarnen. <a %(a_datasets)s>Mear oer ús datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Foar metadata litte wy de orizjinele records sjen. Wy dogge gjin gearfoeging fan records."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Der binne in protte, protte boarnen fan metadata foar skreaune wurken oer de hiele wrâld. <a %(a_wikipedia)s>Dizze Wikipediaside</a> is in goed begjin, mar as jo oare goede listen witte, lit it ús dan witte."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Typ yn it fakje om te sykjen."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Dit binne metadata-records, <span %(classname)s>net</span> downloadbere triemmen."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Flater by sykjen."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Besykje <a %(a_reload)s>de side opnij te laden</a>. As it probleem oanhâldt, stjoer ús dan in e-mail op %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Gjin bestannen fûn.</span> Besykje minder of oare sykterms en filters."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Soms bart dit ferkeard as de sykserver traach is. Yn sokke gefallen kin <a %(a_attrs)s>opnij lade</a> helpe."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Wy hawwe oerienkomsten fûn yn: %(in)s. Jo kinne ferwize nei de URL dy't dêr fûn is as jo in bestân <a %(a_request)s>oanfreegje</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Tydskriftartikels (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digitale Liening (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultaten %(from)s-%(to)s (%(total)s totaal)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ dielde treffers"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d dielde oerienkomsten"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Frijwilligerswurk & Bounties"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive is ôfhinklik fan frijwilligers lykas jo. Wy ferwolkomje alle ynsetnivo's, en hawwe twa haadkategoryen fan help dêr't wy nei sykje:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Ljocht frijwilligerswurk:</span> as jo mar in pear oeren hjir en dêr frijmeitsje kinne, binne der noch in protte manieren wêrop jo helpe kinne. Wy beleanje konsistente frijwilligers mei <span %(bold)s>🤝 lidmaatskippen fan Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Swierich frijwilligerswurk (USD$50-USD$5,000 beleannings):</span> as jo in soad tiid en/of middels oan ús misje wije kinne, wurkje wy graach tichter by jo. Uteinlik kinne jo diel útmeitsje fan it ynterne team. Hoewol't wy in strak budzjet hawwe, kinne wy <span %(bold)s>💰 monetêre beleannings</span> jaan foar it yntinsyfste wurk."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "As jo net yn steat binne om jo tiid frijwillich te jaan, kinne jo ús noch hieltyd in soad helpe troch <a %(a_donate)s>jild te donearjen</a>, <a %(a_torrents)s>ús torrents te seedjen</a>, <a %(a_uploading)s>boeken te uploaden</a>, of <a %(a_help)s>jo freonen te fertellen oer Anna’s Archive</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Bedriuwen:</span> wy biede hege-snelheid direkte tagong ta ús kolleksjes yn ruil foar in donaasje op bedriuwsnivo of yn ruil foar nije kolleksjes (bgl. nije scans, OCR’de datasets, it ferbetterjen fan ús gegevens). <a %(a_contact)s>Kontakt mei ús opnimme</a> as dit jo is. Sjoch ek ús <a %(a_llm)s>LLM-side</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Licht frijwilligerswurk"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "As jo in pear oeren oer hawwe, kinne jo op ferskate manieren helpe. Soargje derfoar dat jo meidwaan oan de <a %(a_telegram)s>frijwilligerschat op Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "As teken fan wurdearring jouwe wy typysk 6 moannen fan “Lokkige Bibliotekaris” foar basis mylpeallen, en mear foar trochgeand frijwilligerswurk. Alle mylpeallen fereaskje wurk fan hege kwaliteit — slordich wurk docht ús mear kwea as goed en wy sille it ôfwiize. Stjoer ús asjebleaft in <a %(a_contact)s>e-mail</a> as jo in mylpeal berikke."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Taak"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Mylpeal"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "It wurd fan Anna’s Argiven ferspriede. Bygelyks troch boeken oan te rieden op AA, te keppeljen nei ús blogberjochten, of algemien minsken nei ús webside te lieden."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s keppelings of skermôfbyldings."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Dizze moatte sjen litte dat jo immen ynformearje oer Anna’s Argiven, en dat se jo tankje."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Ferbetterje metadata troch te <a %(a_metadata)s>linken</a> mei Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Jo kinne de <a %(a_list)s >list fan willekeurige metadata-problemen</a> brûke as in startpunt."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Soargje derfoar dat jo in reaksje efterlitte op problemen dy't jo oplosse, sadat oaren jo wurk net dûbelje."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s keppelings fan records dy't jo ferbettere hawwe."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Oersette</a> de webside."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Folje in taal folslein oer (as it net al hast klear wie)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Ferbetterje de Wikipediaside foar Anna’s Archive yn jo taal. Omfetsje ynformaasje fan AA’s Wikipediaside yn oare talen, en fan ús webside en blog. Foegje ferwizings ta nei AA op oare relevante siden."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Keppeling nei bewurkingsskiednis dy't sjen lit dat jo wichtige bydragen levere hawwe."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Boek (of papier, ensfh.) oanfragen op de Z-Library of de Library Genesis forums ferfolje. Wy hawwe ús eigen boekoanfragesysteem net, mar wy spegelje dy biblioteken, dus it better meitsjen fan dy makket Anna’s Archive ek better."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s keppelings of skermôfbyldings fan fersiken dy't jo folbrocht hawwe."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Lytse taken pleatst yn ús <a %(a_telegram)s>frijwilligerschat op Telegram</a>. Meastentiids foar lidmaatskip, soms foar lytse beleannings."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Lytse taken pleatst yn ús frijwilligerspeteargroep."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Ofhinklik fan de taak."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Belonings"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Wy sykje altyd nei minsken mei solide programmeer- of offinsive feiligensfeardigens om belutsen te reitsjen. Jo kinne in serieuze bydrage leverje oan it bewarjen fan it erfskip fan de minskheid."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "As tankje jouwe wy lidmaatskip foar solide bydragen. As in grutte tankje jouwe wy monetêre belonings foar benammen wichtige en drege taken. Dit moat net sjoen wurde as in ferfanging foar in baan, mar it is in ekstra stimulâns en kin helpe mei makke kosten."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Measte fan ús koade is iepen boarne, en wy freegje dat ek fan jo koade as wy de beloning útrikke. Der binne wat útsûnderingen dy't wy op yndividuele basis beprate kinne."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Belonings wurde útrikt oan de earste persoan dy't in taak foltôget. Fiel jo frij om in reaksje te jaan op in beloningsticket om oaren te litten witte dat jo oan wat wurkje, sadat oaren wachtsje kinne of kontakt mei jo opnimme kinne om gear te wurkjen. Mar wês bewust dat oaren ek frij binne om der oan te wurkjen en besykje jo foar te wêzen. Wy rikke lykwols gjin belonings út foar slordich wurk. As twa heechweardige ynstjoeringen tichtby elkoar makke wurde (binnen in dei of twa), kinne wy kieze om belonings oan beide te jaan, nei ús ynsjoch, bygelyks 100%% foar de earste ynstjoering en 50%% foar de twadde ynstjoering (dus 150%% yn totaal)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Foar de gruttere belonings (benammen scraping belonings), nim asjebleaft kontakt mei ús op as jo ~5%% derfan foltôge hawwe, en jo der wis fan binne dat jo metoade skale sil nei de folsleine mylpeal. Jo sille jo metoade mei ús diele moatte sadat wy feedback jaan kinne. Ek kinne wy op dizze manier beslute wat te dwaan as der meardere minsken tichtby in beloning komme, lykas it mooglik útrikken oan meardere minsken, minsken oanmoedigje om gear te wurkjen, ensfh."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "WARSKÔGING: de hege-beloningstaken binne <span %(bold)s>dreech</span> — it kin wiis wêze om mei maklikere te begjinnen."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Gean nei ús <a %(a_gitlab)s>Gitlab-issueslist</a> en sortearje op “Label priority”. Dit toant rûchwei de folchoarder fan taken dy't wy wichtich fine. Taken sûnder eksplisite belonings binne noch altyd yn oanmerking foar lidmaatskip, benammen dy markearre as “Accepted” en “Anna’s favorite”. Jo kinne begjinne mei in “Starter project”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Updates oer <a %(wikipedia_annas_archive)s>Anna’s Argiven</a>, de grutste echt iepen bibleteek yn 'e minsklike skiednis."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna’s Argyf"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "De grutste iepen-boarne iepen-data bibleteek fan de wrâld. Spegelt Sci-Hub, Library Genesis, Z-Library, en mear."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Sykje yn Anna’s Archive"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna’s Argyf hat jo help nedich!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "In protte besykje ús del te heljen, mar wy fjochtsje werom."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "As jo no donearje, krije jo <strong>dûbel</strong> it oantal snelle downloads."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Jildich oant de ein fan dizze moanne."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Donearje"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "It bewarjen fan minsklike kennis: in prachtich fakânsjekado!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Ferrass in leave, jou se in akkount mei lidmaatskip."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Om de wjerstân fan Anna’s Argyf te ferheegjen, sykje wy frijwilligers om spegels te draaien."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "It perfekte Falentynskado!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Wy hawwe in nije donearmetoade beskikber: %(method_name)s. Oerwaach asjebleaft om %(donate_link_open_tag)ste donearjen</a> — it is net goedkeap om dizze webside te betsjinjen, en jo donaasje makket echt in ferskil. Tige tank."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Wy hâlde in fundraiser foar <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">it reservearjen</a> fan de grutste skadenbiblioteek foar strips yn 'e wrâld. Tank foar jo stipe! <a href=\"/donate\">Donearje.</a> As jo net kinne donearje, beskôgje ús te stypjen troch jo freonen te fertellen, en ús te folgjen op <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Resinte downloads:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Sykje"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "FAQ"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Ferbetterje metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Frijwilligerswurk & Bounties"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrents"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktiviteit"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Koadesferkenner"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM-data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Thús"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Anna’s Software ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Oersette ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Ynlogge / Registrearje"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Akkount"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Anna’s Argyf"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Bliuw yn kontakt"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / auteurskipsaken"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Avansearre"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Feiligens"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternativen"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "net oansletten"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Dit bestân kin problemen hawwe."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Flugge download"

#, fuzzy
msgid "page.donate.copy"
msgstr "kopiearje"

#, fuzzy
msgid "page.donate.copied"
msgstr "kopiearre!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Foarige"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Folgjende"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "allinne dizze moanne!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub hat <a %(a_closed)s>pauzeare</a> it uploaden fan nije papieren."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Selektearje in betelopsje. Wy jouwe koartingen foar betellingen mei crypto %(bitcoin_icon)s, om't wy (folle) minder fergoedingen hawwe."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Kies in betellingsopsje. Wy hawwe op it stuit allinnich krypto-betellingen %(bitcoin_icon)s, om't tradisjonele betellingsferwurkers wegerje mei ús te wurkjen."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Wy kinne kredyt-/debitkaarten net direkt stypje, om't banken net mei ús wurkje wolle. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Der binne lykwols ferskate manieren om kredyt-/debitkaarten te brûken, mei ús oare betelmethoden:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Stadige & eksterne downloads"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Downloads"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "As jo foar it earst crypto brûke, riede wy oan om %(option1)s, %(option2)s, of %(option3)s te brûken om Bitcoin te keapjen en te donearjen (de oarspronklike en meast brûkte cryptocurrency)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 keppelings fan records dy't jo ferbettere hawwe."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 keppelings of skermôfbyldings."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 keppelings of skermôfbyldings fan oanfragen dy't jo ferfolle hawwe."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "As jo ynteressearre binne yn it spegeljen fan dizze datasets foar <a %(a_faq)s>argyfwurk</a> of <a %(a_llm)s>LLM-training</a> doelen, nim dan kontakt mei ús op."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "As jo ynteressearre binne yn it spegeljen fan dizze dataset foar <a %(a_archival)s>argyfwurk</a> of <a %(a_llm)s>LLM-training</a> doelen, nim dan kontakt mei ús op."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Haadwebside"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN lânynformaasje"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "As jo ynteressearre binne yn it spegeljen fan dizze dataset foar <a %(a_archival)s>argyfwurk</a> of <a %(a_llm)s>LLM training</a> doelen, nim dan kontakt mei ús op."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "De International ISBN Agency bringt regelmjittich de reeksen út dy't it tawiisd hat oan nasjonale ISBN-agintskippen. Hjirút kinne wy ôfmeitsje ta hokker lân, regio, of taalgroep dit ISBN heart. Wy brûke dizze gegevens op it stuit yndirekt, fia de <a %(a_isbnlib)s>isbnlib</a> Python-biblioteek."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Boarnen"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Lêst bywurke: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN webside"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Útsûnderje “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Us ynspiraasje foar it sammeljen fan metadata is it doel fan Aaron Swartz fan “ien webside foar elk boek dat ea publisearre is”, wêrfoar hy <a %(a_openlib)s>Open Library</a> makke."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dat projekt hat it goed dien, mar ús unike posysje stelt ús yn steat om metadata te krijen dy't sy net kinne."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "In oare ynspiraasje wie ús winsk om te witten <a %(a_blog)s>hoefolle boeken der yn 'e wrâld binne</a>, sadat wy berekkenje kinne hoefolle boeken wy noch moatte rêde."

#~ msgid "page.partner_download.text1"
#~ msgstr "Om elkenien de kâns te jaan om bestannen fergees te downloaden, moatte jo <strong>%(wait_seconds)s sekonden</strong> wachtsje foardat jo dit bestân downloade kinne."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Automatysk side ferfarskje. As jo it downloadfinster misse, sil de timer opnij starte, dus automatysk ferfarskjen wurdt oanrikkemandearre."

#~ msgid "page.partner_download.download_now"
#~ msgstr "No downloaden"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Konvertearje: brûk online ark om te konvertearjen tusken formaten. Bygelyks, om te konvertearjen tusken epub en pdf, brûk <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: download it bestân (pdf of epub wurde stipe), stjoer it dan <a %(a_kindle)s>nei Kindle</a> mei web, app, of e-mail. Nuttige ark: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Stypje auteurs: As jo dit leuk fine en it jo kinne betelje, beskôgje dan it orizjineel te keapjen, of de auteurs direkt te stypjen."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Stypje bibleteken: As dit beskikber is by jo lokale bibleteek, beskôgje it dan fergees te lienjen."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Net direkt yn bulk beskikber, allinnich yn semi-bulk efter in betellingsmuorre"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Argyf behearet in kolleksje fan <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb is in bedriuw dat ferskate online boekwinkels skraapt om ISBN-metadata te finen. Anna’s Argyf hat backups makke fan de ISBNdb-boekmetadata. Dizze metadata is beskikber fia Anna’s Argyf (hoewol net op it stuit yn sykjen, útsein as jo eksplisyt sykje nei in ISBN-nûmer)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Foar technyske details, sjoch hjirûnder. Op in bepaald stuit kinne wy it brûke om te bepalen hokker boeken noch ûntbrekke yn skaadbiblioteken, om te prioritearjen hokker boeken te finen en/of te skennen."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Us blogpost oer dizze gegevens"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb skraap"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Op it stuit hawwe wy in inkele torrent, dy't in 4,4GB gzipped <a %(a_jsonl)s>JSON Lines</a>-bestân befettet (20GB útpakt): “isbndb_2022_09.jsonl.gz”. Om in “.jsonl”-bestân yn PostgreSQL te ymportearjen, kinne jo wat brûke lykas <a %(a_script)s>dit skript</a>. Jo kinne it sels direkt pipen mei wat lykas %(example_code)s sadat it op 'e flecht dekomprimearret."

#~ msgid "page.donate.wait"
#~ msgstr "Wachtsje asjebleaft op syn minst <span %(span_hours)s>twa oeren</span> (en ferfarskje dizze side) foardat jo kontakt mei ús opnimme."

#~ msgid "page.codes.search_archive"
#~ msgstr "Sykje Anna’s Argyf foar “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Donearje mei Alipay of WeChat. Jo kinne tusken dizze kieze op de folgjende side."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "It wurd fan Anna’s Archive ferspriede op sosjale media en online forums, troch boeken of listen op AA oan te rieden, of fragen te beantwurdzjen."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiksjekolleksje is útinoar gien, mar hat noch altyd <a %(libgenli)s>torrents</a>, hoewol net bywurke sûnt 2022 (wy hawwe wol direkte downloads)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna's Argiven en Libgen.li behearre tegearre kolleksjes fan <a %(comics)s>stripboeken</a> en <a %(magazines)s>tydskriften</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Gjin torrents foar Russyske fiksje en standert dokumintenkolleksjes."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Der binne gjin torrents beskikber foar de ekstra ynhâld. De torrents dy't op de Libgen.li-webside steane, binne spegels fan oare torrents dy't hjir list binne. De ienige útsûndering binne fiksje-torrents dy't begjinne by %(fiction_starting_point)s. De strips en tydskriften torrents wurde útbrocht as in gearwurking tusken Anna’s Argyf en Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Fan in kolleksje <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> krekte oarsprong ûndúdlik. Diels fan the-eye.eu, diels fan oare boarnen."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

