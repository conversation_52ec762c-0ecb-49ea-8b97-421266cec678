msgid "layout.index.invalid_request"
msgstr "<PERSON><PERSON><PERSON> c<PERSON>u không hợp lệ. <PERSON><PERSON><PERSON> cập %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hu<PERSON>"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "zlib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Lending Library"

msgid "layout.index.header.tagline_duxiu"
msgstr "Du<PERSON>iu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

msgid "layout.index.header.tagline_and"
msgstr " và "

msgid "layout.index.header.tagline_and_more"
msgstr "và còn nữa"

msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;<PERSON><PERSON><PERSON> tôi c<PERSON> những bản sao của %(libraries)s."

msgid "layout.index.header.tagline_newnew2b"
msgstr "Chúng tôi đào và làm mở mã nguồn %(scraped)s."

msgid "layout.index.header.tagline_open_source"
msgstr "Toàn bộ câu lệnh và dữ liệu của chúng tôi đều là nguồn mở."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Thư viện hoàn toàn miễn phí lớn nhất trong lịch sử loài người."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp; cuốn sách, %(paper_count)s&nbsp;bài viết — được lưu giữ vĩnh viễn."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Thư viện mã nguồn mở lớn nhất thế giới. ⭐️&nbsp;Bao gồm nội dung từ Sci-Hub, Library Genesis, Z-Library, và hơn thế nữa. 📈&nbsp;%(book_any)s quyển sách, %(journal_article)s bài viết, %(book_comic)s bộ truyện tranh, %(magazine)s tạp chí— được lưu trữ mãi mãi."

msgid "layout.index.header.tagline_short"
msgstr "📚 Thưu viện mã nguồn mở lớn nhất thế giới.<br>⭐️ Bao gồm nội dung từ Scihub, Libgen, Zlib, và hơn nữa."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Dữ liệu số không chính xác (ví dụ: tiêu đề, mô tả, ảnh bìa)"

msgid "common.md5_report_type_mapping.download"
msgstr "Sự cố tải xuống (ví dụ: không thể kết nối, thông báo lỗi, rất chậm)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Không thể mở được tệp (ví dụ: tệp bị hỏng, lỗi bản quyền)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Chất lượng kém (ví dụ: vấn đề về định dạng, chất lượng quét kém, thiếu trang)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Cần loại bỏ thư rác/tập tin (ví dụ: quảng cáo, nội dung lạm dụng)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Khiếu nại về bản quyền"

msgid "common.md5_report_type_mapping.other"
msgstr "Những thứ khác"

msgid "common.membership.tier_name.bonus"
msgstr "Thêm lượt tải về"

msgid "common.membership.tier_name.2"
msgstr "Mọt sách siêu đẳng"

msgid "common.membership.tier_name.3"
msgstr "Thủ thư số đỏ"

msgid "common.membership.tier_name.4"
msgstr "Ông Kẹ Dữ Liệu"

msgid "common.membership.tier_name.5"
msgstr "Lưu trữ viên tuyệt vời"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s tổng"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) tổng (theo đô la Mỹ)"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

msgid "common.donation.membership_bonus_parens"
msgstr " (thêm +%(num)s )"

msgid "common.donation.order_processing_status_labels.0"
msgstr "chưa trả tiền"

msgid "common.donation.order_processing_status_labels.1"
msgstr "đã trả tiền"

msgid "common.donation.order_processing_status_labels.2"
msgstr "đã bị huỷ"

msgid "common.donation.order_processing_status_labels.3"
msgstr "đã hết hạn"

msgid "common.donation.order_processing_status_labels.4"
msgstr "đang đợi Anna xác nhận"

msgid "common.donation.order_processing_status_labels.5"
msgstr "không hợp lệ"

msgid "page.donate.title"
msgstr "Quyên góp"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Bạn vẫn còn một đóng góp chưa được hoàn tất <a %(a_donation)s>donation</a>. Xin vui lòng hoàn tất hoặc huỷ quyên góp kia trước khi tiếp tục với một quyên góp khác."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Hiển thị tất cả các quyên góp của tôi</a>"

msgid "page.donate.header.text1"
msgstr "Kho Tàng Của Anna (Anna’s Archive) là một dự án phi lợi nhuận, mã nguồn mở và dữ liệu mở. Bằng việc quyên góp và trở thành thành viên, bạn hỗ trợ chúng mình hoạt động và phát triển. Gửi mọi thành viên thân mến: cảm ơn vì giúp chúng mình bước tiếp trên con đường này! ❤️"

msgid "page.donate.header.text2"
msgstr "Để biết thêm thông tin, xem <a %(a_donate)s>Những câu hỏi thường gặp về quyên góp</a>."

msgid "page.donate.refer.text1"
msgstr "Để nhận được nhiều lượt tải xuống hơn nữa, hãy <a %(a_refer)s>giới thiệu bạn bè của bạn</a>!"

msgid "page.donate.bonus_downloads.main"
msgstr "Bạn được thêm %(percentage)s%% lượt tải về nhanh, bởi vì bạn được mời bởi %(profile_link)s."

msgid "page.donate.bonus_downloads.period"
msgstr "Việc này được áp dụng cho toàn bộ thời hạn thành viên của bạn."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s lượt download nhanh mỗi ngày"

msgid "page.donate.perks.if_you_donate_this_month"
msgstr "nếu bạn quyên góp vào tháng này!"

msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / hàng tháng"

msgid "page.donate.buttons.join"
msgstr "Tham gia"

msgid "page.donate.buttons.selected"
msgstr "Được chọn"

msgid "page.donate.buttons.up_to_discounts"
msgstr "Giảm giá tới tận %(percentage)s%%"

msgid "page.donate.perks.scidb"
msgstr "Tài liệu SciDB <strong>không giới hạn</strong> không cần xác minh"

msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>Truy cập JSON API</a>"

msgid "page.donate.perks.refer"
msgstr "Kiếm <strong>%(percentage)s%% thêm lượt tải xuống</strong> bằng cách <a %(a_refer)s>giới thiệu bạn bè</a>."

msgid "page.donate.perks.credits"
msgstr "Tên người dùng hoặc đề cập ẩn danh của bạn trong phần ghi công"

msgid "page.donate.perks.previous_plus"
msgstr "Những lợi thế nêu trên, cùng với:"

msgid "page.donate.perks.early_access"
msgstr "Được trải nghiệm sớm những tính năng mới"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Truy cập độc quyền Telegram với những cập nhật sau nền"

msgid "page.donate.perks.adopt"
msgstr "\"Sử dụng torrent”: tên người dùng hoặc lời nhắn của bạn trong tên tệp torrent <div %(div_months)s> mỗi 12 tháng thành viên một lần</div>"

msgid "page.donate.perks.legendary"
msgstr "Một chỗ đứng trên tượng đài của những người bảo tồn tri thức và văn hóa"

msgid "page.donate.expert.title"
msgstr "Quyền truy cập của chuyên gia"

msgid "page.donate.expert.contact_us"
msgstr "liên hệ chúng tôi"

msgid "page.donate.small_team"
msgstr "Chúng tôi là một nhóm tình nguyện viên nhỏ. Chúng tôi có thể mất 1-2 tuần để phản hồi."

msgid "page.donate.expert.unlimited_access"
msgstr "<strong>không giới hạn</strong> lượt truy cập tốc độ cao"

msgid "page.donate.expert.direct_sftp"
msgstr "Máy chủ <strong>SFTP</strong> trực tiếp"

msgid "page.donate.expert.enterprise_donation"
msgstr "Đóng góp hoặc trao đổi ở cấp doanh nghiệp để lấy các bộ sưu tập mới (ví dụ: bản quét mới, bộ dữ liệu OCR)."

msgid "page.donate.header.large_donations_wealthy"
msgstr "Chúng tôi hoan nghênh những đóng góp lớn từ các cá nhân hoặc tổ chức giàu có. "

msgid "page.donate.header.large_donations"
msgstr "Cho các quyên góp cao hơn $5000, vui lòng liên hệ trực tiếp với chúng tôi tại %(email)s."

msgid "page.donate.header.recurring"
msgstr "Xin lưu ý rằng mặc dù các gói thành viên trên trang này là “theo hàng tháng”, chúng là các khoản quyên góp một lần (không định kỳ). Xem <a %(faq)s>Nhữung câu hỏi thường gặp về quyên góp</a>."

msgid "page.donate.without_membership"
msgstr "Nếu bạn muốn quyên góp (bất kỳ lượng tiền nào) mà không cần tư cách thành viên, vui lòng sử dụng địa chỉ Monero (XMR) này: %(address)s."

msgid "page.donate.payment.select_method"
msgstr "Vui lòng chọn một phương thức thanh toán."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(tạm thời không có)"

msgid "page.donate.payment.buttons.amazon_cc"
msgstr "Thẻ quà tặng %(amazon)s"

msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Thẻ ngân hàng (sử dụng ứng dụng)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Tiền crypto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Thẻ tín dụng/thẻ ghi nợ"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (loại bình thường)"

msgid "page.donate.payment.buttons.givebutter"
msgstr "Thẻ / PayPal / Venmo"

msgid "page.donate.payment.buttons.bmc"
msgstr "Thẻ tín dụng/thẻ ghi nợ/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "Paypal"

msgid "page.donate.payment.buttons.bank_card"
msgstr "Thẻ ngân hàng"

msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Thẻ tín dụng/thẻ ghi nợ (dự phòng)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Thẻ tín dụng/thẻ ghi nợ 2"

msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

msgid "page.donate.payment.buttons.wechat"
msgstr "Wechat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay / WeChat"

msgid "page.donate.payment.desc.crypto"
msgstr "Với tiền điện tử, bạn có thể quyên góp thông qua BTC, ETH, XMR và SOL. Sử dụng tùy chọn này nếu bạn đã quen với tiền điện tử."

msgid "page.donate.payment.desc.crypto2"
msgstr "Với tiền điện tử, bạn có thể quyên góp bằng BTC, ETH, XMR, v.v."

msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Nếu bạn sử dụng tiền điện tử lần đầu tiên, chúng tôi gợi ý sử dụng %(options)s để mua và quyên góp Bitcoin (loại tiền điện tử đầu tiên và được sử dụng nhiều nhất)."

msgid "page.donate.payment.processor.binance"
msgstr "Binance"

msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Để quyên góp bằng PayPal US, chúng tôi phải sử dụng PayPal Crypto, vì lựa chọn này cho phép chúng tôi bảo đảm sự ẩn danh. Chúng tôi đề cao việc bạn dành thời gian tìm hiểu phương thức quyên góp này, vì nó vô cùng hữu ích với chúng tôi."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Đóng góp bằng PayPal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Đóng góp bằng Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Nếu bạn có Cash App, đây là cách dễ nhất để đóng góp cho chúng tôi!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Lưu ý rằng đối với các giao dịch dưới %(amount)s, Cash App có thể tính phí %(fee)s. Đối với %(amount)s trở lên, nó là miễn phí!"

msgid "page.donate.payment.desc.revolut"
msgstr "Quyên góp bằng Revolut."

msgid "page.donate.payment.desc.revolut_easy"
msgstr "Nếu bạn có Revolut, đây là cách dễ nhất để quyên góp!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Đóng góp thông qua thẻ tín dụng hoặc thẻ ghi nợ."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay và Apple Pay cũng có thể dùng được."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Lưu ý rằng đối với những khoản đóng góp nhỏ, phí thẻ tín dụng có thể loại bỏ khoản chiết khấu %(discount)s%% của chúng tôi, vì vậy chúng tôi khuyên bạn nên đăng ký dài hạn hơn."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Lưu ý rằng đối với những khoản quyên góp nhỏ thì phí sẽ cao, vì vậy chúng tôi khuyên bạn nên đăng ký lâu hơn."

msgid "page.donate.payment.desc.binance_p1"
msgstr "Với Binance, bạn mua Bitcoin bằng thẻ tín dụng/thẻ ghi nợ hoặc tài khoản ngân hàng, sau đó quyên góp Bitcoin đó cho chúng tôi. Bằng cách này, chúng tôi có thể duy trì bảo mật và ẩn danh khi nhận quyên góp của bạn."

msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance có mặt ở hầu hết các quốc gia, và hỗ trợ hầu hết các ngân hàng và thẻ tín dụng/thẻ ghi nợ. Đây hiện là khuyến nghị chính của chúng tôi. Chúng tôi cảm kích bạn đã dành thời gian tìm hiểu cách quyên góp bằng phương pháp này, vì nó giúp chúng tôi rất nhiều."

msgid "page.donate.payment.desc.paypalreg"
msgstr "Quyên góp bằng tài khoản PayPal thường của bạn."

msgid "page.donate.payment.desc.givebutter"
msgstr "Quyên góp bằng thẻ tín dụng/thẻ ghi nợ, PayPal, hoặc Venmo. Bạn có thể chọn giữa các phương thức này trên trang kế tiếp."

msgid "page.donate.payment.desc.amazon"
msgstr "Đóng góp bằng thẻ quà tặng Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Lưu ý rằng chúng tôi cần làm tròn số tiền lên một khoản được người bán lại chấp nhận (tối thiểu %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>QUAN TRỌNG:</strong> Chúng tôi chỉ hỗ trợ Amazon.com, không hỗ trợ các trang web Amazon khác. Ví dụ: .de, .co.uk, .ca KHÔNG được hỗ trợ."

msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>QUAN TRỌNG:</strong> Tùy chọn này dành cho %(amazon)s. Nếu bạn muốn sử dụng một trang web Amazon khác, hãy chọn nó ở trên."

msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Phương pháp này sử dụng nhà cung cấp tiền điện tử làm trung gian chuyển đổi. Điều này có thể hơi khó hiểu, vì vậy vui lòng chỉ sử dụng phương pháp này nếu các phương thức thanh toán khác không sử dụng được. Nó cũng không hoạt động ở một số quốc gia."

msgid "page.donate.payment.desc.bank_card_app"
msgstr "Quyên góp bằng thẻ tín dụng/thẻ ghi nợ, thông qua ứng dụng Alipay (rất dễ để cài đặt)."

msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Cài đặt ứng dụng Alipay"

msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Cài đặt ứng dụng Alipay từ <a %(a_app_store)s>Cửa Hàng Ứng dụng Apple</a> hoặc <a %(a_play_store)s>Cửa Hàng Google Play</a>."

msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Đăng ký bằng số điện thoại của bạn."

msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Không cần thêm thông tin cá nhân nào khác."

msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Thêm thẻ ngân hàng"

msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Được hỗ trợ: Visa, MasterCard, JCB, Diners Club và Discover."

msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Xem <a %(a_alipay)s>hướng dẫn này</a> để biết thêm thông tin."

msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Chúng tôi không thể hỗ trợ thẻ tín dụng/ghi nợ trực tiếp, vì các ngân hàng không muốn làm việc với chúng tôi. ☹ Tuy nhiên, có một số cách để sử dụng thẻ tín dụng/ghi nợ, thông qua các phương thức thanh toán khác:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Thẻ quà tặng Amazon"

msgid "page.donate.ccexp.amazon_com"
msgstr "Gửi cho chúng tôi thẻ quà tặng Amazon.com bằng thẻ tín dụng/thẻ ghi nợ của bạn."

msgid "page.donate.ccexp.alipay"
msgstr "Alipay hỗ trợ thẻ tín dụng/ghi nợ quốc tế. Xem <a %(a_alipay)s>hướng dẫn này</a> để biết thêm thông tin."

msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) hỗ trợ thẻ tín dụng/thẻ ghi nợ quốc tế. Trong ứng dụng WeChat, hãy truy cập “Tôi => Dịch vụ => Ví => Thêm thẻ”. Nếu bạn không thấy nó, hãy kích hoạt nó bằng cách sử dụng “Tôi => Cài đặt => Chung => Công cụ => Weixin Pay => Bật”."

msgid "page.donate.ccexp.crypto"
msgstr "Bạn có thể mua tiền điện tử bằng thẻ tín dụng/thẻ ghi nợ."

msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Dịch vụ tiền điện tử nhanh"

msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Dịch vụ nhanh tiện lợi, nhưng tính phí cao hơn."

msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Bạn có thể sử dụng dịch vụ này thay vì sàn giao dịch tiền điện tử nếu bạn muốn nhanh chóng thực hiện một khoản quyên góp lớn hơn và không ngại phí $5-10."

msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Hãy chắc chắn bạn gửi đúng số tiền tiền điện tử được hiển thị trên trang quyên góp, không phải số tiền bằng $USD."

msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Nếu không, phí sẽ bị trừ và chúng tôi không có thể tự động xử lý gói thành viên của bạn."

msgid "page.donation.payment2cc.method.paybis"
msgstr "(tối thiểu: %(minimum)s)"

msgid "page.donation.payment2cc.method.switchere"
msgstr "(tối thiểu: %(minimum)s tùy thuộc vào quốc gia, không cần xác minh cho giao dịch đầu tiên)"

msgid "page.donation.payment2cc.method.munzen"
msgstr "(tối thiểu: %(minimum)s, không cần xác minh cho giao dịch đầu tiên)"

msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(tối thiểu: %(minimum)s)"

msgid "page.donation.payment2cc.method.moonpay"
msgstr "(tối thiểu: %(minimum)s)"

msgid "page.donation.payment2cc.method.coingate"
msgstr "(tối thiểu: %(minimum)s, không cần xác minh cho giao dịch đầu tiên)"

msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Nếu bất kỳ thông tin nào trong số này đã lỗi thời, xin vui lòng email cho chúng tôi để chúng tôi có thể biết."

msgid "page.donate.payment.desc.bmc"
msgstr "Đối với thẻ tín dụng, thẻ ghi nợ, Apple Pay và Google Pay, chúng tôi sử dụng “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Trong hệ thống của họ, một “cà phê” là tương đương với $5, vì vậy khoản quyên góp của bạn sẽ được làm tròn đến bội số gần nhất của 5."

msgid "page.donate.duration.intro"
msgstr "Xác định khoảng thời gian bạn muốn đăng ký."

msgid "page.donate.duration.1_mo"
msgstr "1 tháng"

msgid "page.donate.duration.3_mo"
msgstr "3 tháng"

msgid "page.donate.duration.6_mo"
msgstr "6 tháng"

msgid "page.donate.duration.12_mo"
msgstr "12 tháng"

msgid "page.donate.duration.24_mo"
msgstr "24 tháng"

msgid "page.donate.duration.48_mo"
msgstr "48 tháng"

msgid "page.donate.duration.96_mo"
msgstr "96 tháng"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>sau <span %(span_discount)s></span> chiết khấu</div><div %(div_total)s></div> <div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Phương thức thanh toán này yêu cầu tối thiểu %(amount)s. Vui lòng chọn thời hạn hoặc phương thức thanh toán khác."

msgid "page.donate.buttons.donate"
msgstr "Quyên góp"

msgid "page.donate.payment.maximum_method"
msgstr "Cách chuyển khoản này chỉ cho phép tối đa %(amount)s. Hãy chọn một phương thức thanh toán khác hay chọn một thời lương đóng phí khác."

msgid "page.donate.login2"
msgstr "Để làm một thành viên, xin hãy <a%(a_login)s> Đăng nhập hoặc Đăng kí</a>. Cảm ơn vì sự đóng góp của bạn."

msgid "page.donate.payment.crypto_select"
msgstr "Chọn loại tiền điện tử bạn mong muốn:"

msgid "page.donate.currency_lowest_minimum"
msgstr "(khoản tiền thấp nhất có thể)"

msgid "page.donate.coinbase_eth"
msgstr "(sử dụng khi gửi Ethereum từ Coinbase)"

msgid "page.donate.currency_warning_high_minimum"
msgstr "(cảnh báo: yêu cầu lượng tiền tối thiểu cao)"

msgid "page.donate.submit.confirm"
msgstr "Bấm nút quyên góp để xác nhận khoản tiền sẽ cho."

msgid "page.donate.submit.button"
msgstr "Quyên góp <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Bạn vẫn có thể hủy quá trình quyên góp trong thủ tục thanh toán."

msgid "page.donate.submit.success"
msgstr "✅ Đang chuyển hướng tới trang quyên góp…"

msgid "page.donate.submit.failure"
msgstr "❌ Đã xảy ra sự cố. Vui lòng tải lại trang và thử lại."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / tháng"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "trong 1 tháng"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "trong 3 tháng"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "trong 6 tháng"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "trong 12 tháng"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "trong 24 tháng"

msgid "page.donate.duration.summary.duration.48_mo"
msgstr "cho 48 tháng"

msgid "page.donate.duration.summary.duration.96_mo"
msgstr "cho 96 tháng"

msgid "page.donate.submit.button.label.1_mo"
msgstr "trong 1 tháng \"%(tier_name)s”"

msgid "page.donate.submit.button.label.3_mo"
msgstr "trong 3 tháng \"%(tier_name)s”"

msgid "page.donate.submit.button.label.6_mo"
msgstr "trong 6 tháng \"%(tier_name)s”"

msgid "page.donate.submit.button.label.12_mo"
msgstr "trong 12 tháng \"%(tier_name)s”"

msgid "page.donate.submit.button.label.24_mo"
msgstr "trong 24 tháng \"%(tier_name)s”"

msgid "page.donate.submit.button.label.48_mo"
msgstr "cho 48 tháng “%(tier_name)s”"

msgid "page.donate.submit.button.label.96_mo"
msgstr "cho 96 tháng “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Quyên góp"

msgid "page.donation.header.date"
msgstr "Ngày tháng: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Tổng cộng: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / tháng trong %(duration)s tháng, bao gồm %(discounts)s%% chiết khấu)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Tổng cộng: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / tháng cho %(duration)s tháng)</span>"

msgid "page.donation.header.status"
msgstr "Trạng thái: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Số định danh: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Hủy"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Bạn có muốn hủy quyên góp? Đừng hủy nếu bạn đã trả trước."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Có, hủy quyên góp"

msgid "page.donation.header.cancel.success"
msgstr "Quyên góp của bạn đã được hủy bỏ."

msgid "page.donation.header.cancel.new_donation"
msgstr "Thêm khoản quyên góp mới"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Có lỗi rồi. Xin hãy tải lại trang rồi thử lại nhé."

msgid "page.donation.header.reorder"
msgstr "Đặt lại"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Bạn đã thanh toán rồi. Nếu bạn vẫn muốn xem lại hướng dẫn thanh toán, hãy nhấp vào đây:"

msgid "page.donation.old_instructions.show_button"
msgstr "Hiển thị hướng dẫn thanh toán cũ"

msgid "page.donation.thank_you_donation"
msgstr "Cảm ơn vì đóng góp của bạn!"

msgid "page.donation.thank_you.secret_key"
msgstr "Hãy viết lại mã mật để đăng nhập nếu bạn chưa làm vậy:"

msgid "page.donation.thank_you.locked_out"
msgstr "Nếu không thì bạn có thể bị thoát khỏi tài khoản mà không phục hồi được!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Các hướng dẫn thanh toán hiện đã lỗi thời. Nếu bạn muốn thực hiện quyên góp khác, hãy sử dụng nút “Sắp xếp lại” ở trên."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Lưu ý quan trọng:</strong> Giá tiền điện tử có thể biến động mạnh, đôi khi thậm chí lên tới 20%% trong vài phút. Con số này vẫn thấp hơn mức phí mà chúng tôi phải gánh chịu với nhiều nhà cung cấp dịch vụ thanh toán, những người thường tính phí 50-60%% khi làm việc với một “tổ chức từ thiện trong bóng tối” như chúng tôi. <u>Nếu bạn gửi cho chúng tôi biên lai với giá ban đầu bạn đã thanh toán, chúng tôi vẫn sẽ ghi vào tài khoản của bạn cho tư cách thành viên đã chọn</u> (miễn là biên lai không quá vài giờ sau giao dịch). Chúng tôi thực sự đánh giá cao việc bạn sẵn sàng chịu đựng những thứ như thế này để hỗ trợ chúng tôi! ❤️"

msgid "page.donation.expired"
msgstr "Khoản quyên góp này đã hết hạn. Vui lòng hủy và tạo một cái mới."

msgid "page.donation.payment.crypto.top_header"
msgstr "Hướng dẫn về tiền điện tử"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Chuyển sang một trong các tài khoản tiền điện tử của chúng tôi"

msgid "page.donation.payment.crypto.text1"
msgstr "Đóng góp tổng số lượng %(total)s đến một trong những địa chỉ sau:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Mua Bitcoin trên Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Tìm trang “Tiền điện tử” trong ứng dụng hoặc trang web PayPal của bạn. Điều này thường nằm trong mục “Tài chính”."

msgid "page.donation.payment.paypal.text3"
msgstr "Làm theo hướng dẫn để mua Bitcoin (BTC). Bạn chỉ cần mua số tiền muốn quyên góp, %(total)s."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Chuyển Bitcoin đến địa chỉ của chúng tôi"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Đi tới trang “Bitcoin” trong ứng dụng hoặc trang web PayPal của bạn. Nhấn nút “Chuyển” %(transfer_icon)s, sau đó “Gửi”."

msgid "page.donation.payment.paypal.text5"
msgstr "Nhập địa chỉ Bitcoin (BTC) của chúng tôi làm người nhận và làm theo hướng dẫn để gửi khoản quyên góp %(total)s của bạn:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Hướng dẫn sử dụng thẻ tín dụng/thẻ ghi nợ"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Đóng góp thông qua trang thẻ tín dụng / thẻ ghi nợ của chúng tôi"

msgid "page.donation.donate_on_this_page"
msgstr "Quyên góp %(amount)s trên <a %(a_page)s>trang này</a>."

msgid "page.donation.stepbystep_below"
msgstr "Xem hướng dẫn từng bước dưới đây."

msgid "page.donation.status_header"
msgstr "Trạng thái:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Đang chờ xác nhận (làm mới trang để kiểm tra)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Đang chờ chuyển (làm mới trang để kiểm tra)…"

msgid "page.donation.time_left_header"
msgstr "Thời gian còn lại:"

msgid "page.donation.might_want_to_cancel"
msgstr "(bạn có thể muốn hủy và tạo một khoản quyên góp mới)"

msgid "page.donation.reset_timer"
msgstr "Để đặt lại bộ đếm thời gian, chỉ cần đơn giản tạo một khoản quyên góp mới."

msgid "page.donation.refresh_status"
msgstr "Cập nhật trạng thái"

msgid "page.donation.footer.issues_contact"
msgstr "Nếu bạn gặp bất kỳ vấn đề nào, vui lòng liên hệ với chúng tôi tại %(email)s và cung cấp càng nhiều thông tin càng tốt (chẳng hạn như ảnh chụp màn hình)."

msgid "page.donation.expired_already_paid"
msgstr "Nếu bạn đã thanh toán:"

msgid "page.donation.confirmation_can_take_a_while"
msgstr "Đôi khi việc xác nhận có thể mất đến 24 tiếng, vì vậy hãy chắc chắn làm mới trang này (ngay cả khi nó đã hết hạn)."

msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Mua đồng PYUSD trên PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Làm theo hướng dẫn để mua đồng PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Mua thêm một chút (chúng tôi khuyên bạn nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại."

msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Đi đến trang “PYUSD” trong ứng dụng hoặc trang web PayPal của bạn. Nhấn nút “Chuyển” %(icon)s, sau đó “Gửi”."

msgid "page.donation.transfer_amount_to"
msgstr "Chuyển %(amount)s đến %(account)s"

msgid "page.donation.cash_app_btc.step1"
msgstr "Mua Bitcoin (BTC) trên Cash App"

msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Đi tới trang “Bitcoin” (BTC) trong Cash App."

msgid "page.donation.cash_app_btc.step1.more"
msgstr "Mua thêm một chút (chúng tôi khuyên bạn nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại."

msgid "page.donation.cash_app_btc.step2"
msgstr "Chuyển cái Bitcoin đến địa chỉ của chúng tôi"

msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Nhấp vào nút “Gửi bitcoin” để thực hiện “rút tiền”. Chuyển từ đô la sang BTC bằng cách nhấn vào biểu tượng %(icon)s. Nhập số lượng BTC bên dưới và nhấp vào “Gửi”. Xem <a %(help_video)s>video này</a> nếu bạn gặp khó khăn."

msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Đối với các khoản quyên góp nhỏ (dưới $25), bạn có thể cần sử dụng Rush hoặc Priority."

msgid "page.donation.revolut.step1"
msgstr "Mua Bitcoin (BTC) trên Revolut"

msgid "page.donation.revolut.step1.text1"
msgstr "Đi tới trang “Crypto” trong Revolut để mua Bitcoin (BTC)."

msgid "page.donation.revolut.step1.more"
msgstr "Mua thêm một chút (chúng tôi khuyên nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại."

msgid "page.donation.revolut.step2"
msgstr "Chuyển cái Bitcoin đến địa chỉ của chúng tôi"

msgid "page.donation.revolut.step2.transfer"
msgstr "Nhấp vào nút “Gửi bitcoin” để thực hiện “rút tiền”. Chuyển từ euro sang BTC bằng cách nhấn vào biểu tượng %(icon)s. Nhập số lượng BTC bên dưới và nhấp vào “Gửi”. Xem <a %(help_video)s>video này</a> nếu bạn gặp khó khăn."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Hãy chắc chắn sử dụng số lượng BTC dưới đây, <em>KHÔNG PHẢI</em> euro hoặc đô la, nếu không chúng tôi sẽ không nhận được số tiền chính xác và không thể tự động xác nhận tư cách thành viên của bạn."

msgid "page.donation.revolut.step2.rush_priority"
msgstr "Đối với các khoản quyên góp nhỏ (dưới $25), bạn có thể cần sử dụng Rush hoặc Priority."

msgid "page.donation.payment2cc.cc2btc"
msgstr "Sử dụng bất kỳ dịch vụ “thẻ tín dụng sang Bitcoin” nhanh nào sau đây, chỉ cần vài phút:"

msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Điền các chi tiết sau vào biểu mẫu:"

msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Số lượng BTC / Bitcoin:"

msgid "page.donation.payment2cc.exact_amount"
msgstr "Vui lòng sử dụng <span %(underline)s>số lượng chính xác</span> này. Tổng chi phí của bạn có thể cao hơn do phí thẻ tín dụng. Đối với các khoản nhỏ, điều này có thể nhiều hơn mức giảm giá của chúng tôi, rất tiếc."

msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Địa chỉ BTC / Bitcoin (ví bên ngoài):"

msgid "page.donation.crypto_instructions"
msgstr "Hướng dẫn %(coin_name)s"

msgid "page.donation.crypto_standard"
msgstr "Chúng tôi chỉ hỗ trợ phiên bản tiêu chuẩn của tiền điện tử, không hỗ trợ các mạng hoặc phiên bản tiền điện tử lạ. Có thể cần đến một giờ để xác nhận giao dịch, tùy thuộc vào loại tiền."

msgid "page.donation.crypto_qr_code_title"
msgstr "Quét mã QR để thanh toán"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Quét mã QR này bằng ứng dụng Wallet Crypto của bạn để nhanh chóng điền vào chi tiết thanh toán"

msgid "page.donation.amazon.header"
msgstr "Thẻ quà tặng Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Vui lòng sử dụng <a %(a_form)s>biểu mẫu chính thức của Amazon.com</a> để gửi cho chúng tôi một thẻ quà tặng trị giá %(amount)s đến địa chỉ email bên dưới."

msgid "page.donation.amazon.only_official"
msgstr "Chúng tôi không thể chấp nhận các phương thức thẻ quà tặng khác, <strong>chỉ được gửi trực tiếp từ biểu mẫu chính thức trên Amazon.com</strong>. Chúng tôi không thể hoàn trả thẻ quà tặng của bạn nếu bạn không sử dụng biểu mẫu này."

msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Nhập số tiền chính xác: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Vui lòng KHÔNG viết tin nhắn của riêng bạn."

msgid "page.donation.amazon.form_to"
msgstr "Email người nhận “Đến” trong biểu mẫu:"

msgid "page.donation.amazon.unique"
msgstr "Duy nhất cho tài khoản của bạn, không chia sẻ."

msgid "page.donation.amazon.only_use_once"
msgstr "Chỉ sử dụng một lần."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Đang chờ thẻ quà tặng… (làm mới trang để kiểm tra)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Sau khi gửi thẻ quà tặng của bạn, hệ thống tự động của chúng tôi sẽ xác nhận trong vài phút. Nếu điều này không hoạt động, hãy thử gửi lại thẻ quà tặng của bạn (<a %(a_instr)s>hướng dẫn</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Nếu điều đó vẫn không hoạt động, vui lòng email cho chúng tôi và Anna sẽ xem xét thủ công (việc này có thể mất vài ngày), và hãy chắc chắn đề cập nếu bạn đã thử gửi lại."

msgid "page.donation.amazon.example"
msgstr "Ví dụ:"

msgid "page.donate.strange_account"
msgstr "Lưu ý rằng tên hoặc hình ảnh tài khoản có thể trông lạ. Không cần phải lo lắng! Những tài khoản này được quản lý bởi các đối tác quyên góp của chúng tôi. Tài khoản của chúng tôi chưa bị hack."

msgid "page.donation.payment.alipay.top_header"
msgstr "Hướng dẫn sử dụng Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Quyên góp trên Alipay"

msgid "page.donation.payment.alipay.text1_new"
msgstr "Quyên góp tổng số tiền %(total)s bằng cách sử dụng <a %(a_account)s>tài khoản Alipay này</a>"

msgid "page.donation.page_blocked"
msgstr "Nếu trang quyên góp bị chặn, hãy thử một kết nối internet khác (ví dụ: VPN hoặc internet điện thoại)."

msgid "page.donation.payment.alipay.error"
msgstr "Thật không may, trang Alipay thường chỉ truy cập được từ <strong>Trung Quốc đại lục</strong>. Bạn có thể cần tạm thời tắt VPN của mình, hoặc sử dụng VPN đến Trung Quốc đại lục (hoặc đôi khi Hồng Kông cũng hoạt động)."

msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Thực hiện quyên góp (quét mã QR hoặc nhấn nút)"

msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Mở <a %(a_href)s>trang quyên góp mã QR</a>."

msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Quét mã QR bằng ứng dụng Alipay, hoặc nhấn nút để mở ứng dụng Alipay."

msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Xin vui lòng kiên nhẫn; trang có thể mất một lúc để tải bởi vì nó ở Trung Quốc."

msgid "page.donation.payment.wechat.top_header"
msgstr "Hướng dẫn WeChat"

msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Quyên góp trên WeChat"

msgid "page.donation.payment.wechat.text1"
msgstr "Quyên góp tổng số tiền %(total)s bằng cách sử dụng <a %(a_account)s>tài khoản WeChat này</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "hướng dẫn pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Quyên góp trên Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Quyên góp tổng số tiền %(total)s bằng <a %(a_account)s>tài khoản Pix này"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Gửi biên lai cho chúng tôi qua email"

msgid "page.donation.footer.verification"
msgstr "Gửi biên lai hoặc ảnh chụp màn hình đến địa chỉ xác minh cá nhân của bạn. KHÔNG nên sử dụng địa chỉ email này cho khoản quyên góp PayPal của bạn."

msgid "page.donation.footer.text1"
msgstr "Gửi biên lai hoặc ảnh chụp màn hình đến địa chỉ xác minh cá nhân của bạn:"

msgid "page.donation.footer.crypto_note"
msgstr "Nếu tỷ giá hối đoái tiền điện tử biến động trong quá trình giao dịch, hãy đảm bảo bao gồm biên lai hiển thị tỷ giá hối đoái ban đầu. Chúng tôi thực sự đánh giá cao việc bạn đã chịu khó sử dụng tiền điện tử, nó giúp ích cho chúng tôi rất nhiều!"

msgid "page.donation.footer.text2"
msgstr "Khi bạn đã gửi biên lai qua email, hãy nhấp vào nút này để Anna có thể xem lại theo cách thủ công (việc này có thể mất vài ngày):"

msgid "page.donation.footer.button"
msgstr "Có, tôi đã gửi biên lai của mình qua email"

msgid "page.donation.footer.success"
msgstr "✅ Cảm ơn sự đóng góp của bạn! Anna sẽ tự kích hoạt tư cách thành viên của bạn trong vòng vài ngày."

msgid "page.donation.footer.failure"
msgstr "Có sự cố xảy ra.Xin tải lại trang và thử lại."

msgid "page.donation.stepbystep"
msgstr "Hướng dẫn từng bước"

msgid "page.donation.crypto_dont_worry"
msgstr "Một số bước đề cập đến ví tiền điện tử, nhưng đừng lo lắng, bạn không cần phải học bất cứ điều gì về tiền điện tử cho việc này."

msgid "page.donation.hoodpay.step1"
msgstr "1. Nhập email của bạn."

msgid "page.donation.hoodpay.step2"
msgstr "2. Chọn phương thức thanh toán của bạn."

msgid "page.donation.hoodpay.step3"
msgstr "3. Chọn lại phương thức thanh toán của bạn."

msgid "page.donation.hoodpay.step4"
msgstr "4. Chọn ví “Tự lưu trữ”."

msgid "page.donation.hoodpay.step5"
msgstr "5. Nhấp vào “Tôi xác nhận quyền sở hữu”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Bạn sẽ nhận được biên lai qua email. Vui lòng gửi biên lai đó cho chúng tôi, và chúng tôi sẽ xác nhận quyên góp của bạn sớm nhất có thể."

msgid "page.donate.wait_new"
msgstr "Vui lòng chờ ít nhất <span %(span_hours)s>24 giờ</span> (và làm mới trang này) trước khi liên hệ với chúng tôi."

msgid "page.donate.mistake"
msgstr "Nếu bạn đã mắc lỗi trong quá trình thanh toán, chúng tôi không thể hoàn tiền, nhưng chúng tôi sẽ cố gắng khắc phục."

msgid "page.my_donations.title"
msgstr "Những đóng góp của tôi"

msgid "page.my_donations.not_shown"
msgstr "Những đóng góp sẽ được giữ riêng tư."

msgid "page.my_donations.no_donations"
msgstr "Chưa có khoản quyên góp nào. <a %(a_donate)s>Thực hiện khoản quyên góp đầu tiên của tôi.</a>"

msgid "page.my_donations.make_another"
msgstr "Quyên góp lần nữa."

msgid "page.downloaded.title"
msgstr "Tài liệu đã tải về"

msgid "page.downloaded.fast_partner_star"
msgstr "Các tải xuống từ Máy chủ Đối tác Nhanh được đánh dấu bởi %(icon)s."

msgid "page.downloaded.twice"
msgstr "Nếu bạn đã tải xuống một tệp với cả hai tải xuống nhanh và chậm, nó sẽ xuất hiện hai lần."

msgid "page.downloaded.fast_download_time"
msgstr "Các lượt tải xuống nhanh trong 24 giờ qua được tính vào giới hạn hàng ngày."

msgid "page.downloaded.times_utc"
msgstr "Tất cả thời gian đều theo giờ UTC."

msgid "page.downloaded.not_public"
msgstr "Các tệp đã tải xuống không được hiển thị công khai."

msgid "page.downloaded.no_files"
msgstr "Chưa có tệp nào được tải về."

msgid "page.downloaded.last_18_hours"
msgstr "18 giờ trước"

msgid "page.downloaded.earlier"
msgstr "Trước đó"

msgid "page.account.logged_in.title"
msgstr "Tài khoản"

msgid "page.account.logged_out.title"
msgstr "Đăng nhập / Đăng ký"

msgid "page.account.logged_in.account_id"
msgstr "ID tài khoản: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Hồ sơ công khai: %(profile_link)s"

msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Khóa bí mật (đừng chia sẻ nó!): %(secret_key)s"

msgid "page.account.logged_in.secret_key_show"
msgstr "hiển thị"

msgid "page.account.logged_in.membership_has_some"
msgstr "Thành viên: <strong>%(tier_name)s</strong> đến %(until_date)s <a %(a_extend)s>(gia hạn)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Thành viên: <strong>Không có</strong> <a %(a_become)s>(trở thành thành viên)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Các lượt tải nhanh đã sử dụng (trong 24 giờ qua): <strong>%(used)s / %(total)s</strong>"

msgid "page.account.logged_in.which_downloads"
msgstr "tải xuống nào?"

msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Nhóm Telegram độc quyền: %(link)s"

msgid "page.account.logged_in.telegram_group_join"
msgstr "Tham gia với chúng tôi tại đây!"

msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Nâng cấp lên <a %(a_tier)s>hạng cao hơn</a> để tham gia nhóm của chúng tôi."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Liên hệ với Anna tại %(email)s nếu bạn quan tâm đến việc nâng cấp thành viên của mình lên một cấp cao hơn."

msgid "page.contact.title"
msgstr "Địa chỉ email liên lạc"

msgid "page.account.logged_in.membership_multiple"
msgstr "Bạn có thể kết hợp nhiều gói thành viên (lượt tải nhanh mỗi 24 giờ sẽ được cộng dồn)."

msgid "layout.index.header.nav.public_profile"
msgstr "Hồ sơ công khai"

msgid "layout.index.header.nav.downloaded_files"
msgstr "Tệp đã tải"

msgid "layout.index.header.nav.my_donations"
msgstr "Các lần quyên góp của tôi"

msgid "page.account.logged_in.logout.button"
msgstr "Thoát tài khoản"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Bạn đã đăng xuất. Tải lại trang để đăng nhập lại."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Đã xảy ra lỗi. Hãy tải lại trang và thử lại."

msgid "page.account.logged_out.registered.text1"
msgstr "Đăng ký thành công! Khóa bí mật của bạn là: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Hãy lưu mã này cẩn thận. Nếu bạn làm mất, bạn sẽ mất quyền truy cập vào tài khoản của mình."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Đánh dấu trang.</strong> Bạn có thể đánh dấu trang này để lấy lại mã của mình.</li><li %(li_item)s><strong>Tải xuống.</strong> Nhấp vào <a %(a_download)s>liên kết này</a> để tải xuống mã của bạn.</li><li %(li_item)s><strong>Trình quản lý mật khẩu.</strong> Sử dụng trình quản lý mật khẩu để lưu mã khi bạn nhập nó bên dưới.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Nhập khóa bí mật của bạn để đăng nhập:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Mật khẩu bí mật"

msgid "page.account.logged_out.key_form.button"
msgstr "Đăng nhập"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Khóa bí mật không hợp lệ. Hãy xác minh khóa của bạn và thử lại hoặc đăng ký một tài khoản mới bên dưới."

msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Đừng làm mất mã của bạn!"

msgid "page.account.logged_out.register.header"
msgstr "Chưa có tài khoản?"

msgid "page.account.logged_out.register.button"
msgstr "Tạo tài khoản mới"

msgid "page.login.lost_key"
msgstr "Nếu bạn mất mã, vui lòng <a %(a_contact)s>liên hệ với chúng tôi</a> và cung cấp càng nhiều thông tin càng tốt."

msgid "page.login.lost_key_contact"
msgstr "Bạn có thể phải tạm thời tạo một tài khoản mới để liên hệ với chúng tôi."

msgid "page.account.logged_out.old_email.button"
msgstr "Tài khoản cũ dùng email? Điền <a %(a_open)s>email vào đây</a>."

msgid "page.list.title"
msgstr "Danh sách"

msgid "page.list.header.edit.link"
msgstr "Thay đổi"

msgid "page.list.edit.button"
msgstr "Làm"

msgid "page.list.edit.success"
msgstr "Đã lưu. Hãy tải lại trang."

msgid "page.list.edit.failure"
msgstr "Có một sự cố đã xảy ra. Xin hãy thử lại."

msgid "page.list.by_and_date"
msgstr "Danh sách bởi %(by)s, được tạo <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Danh sách trống."

msgid "page.list.new_item"
msgstr "Thêm hoặc xóa khỏi danh sách này bằng cách tìm tệp và mở tab “Danh sách”."

msgid "page.profile.title"
msgstr "Hồ sơ"

msgid "page.profile.not_found"
msgstr "Không tìm thấy hồ sơ."

msgid "page.profile.header.edit"
msgstr "Thay đổi"

msgid "page.profile.change_display_name.text"
msgstr "Thay đổi tên hiển thị của bạn. Mã định danh của bạn (phần sau “#”) không thể thay đổi."

msgid "page.profile.change_display_name.button"
msgstr "Lưu"

msgid "page.profile.change_display_name.success"
msgstr "✅ Đã lưu. Vui lòng tải lại trang."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Đã xảy ra lỗi. Vui lòng thử lại."

msgid "page.profile.created_time"
msgstr "Đã tạo hồ sơ <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Danh sách"

msgid "page.profile.lists.no_lists"
msgstr "Chưa có danh sách nào"

msgid "page.profile.lists.new_list"
msgstr "Tạo danh sách mới bằng cách tìm tệp và mở tab “Danh sách”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Cải cách bản quyền là cần thiết cho an ninh quốc gia"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "Tóm tắt: Các LLM của Trung Quốc (bao gồm DeepSeek) được đào tạo trên kho lưu trữ sách và tài liệu bất hợp pháp của tôi — lớn nhất thế giới. Phương Tây cần cải tổ luật bản quyền như một vấn đề an ninh quốc gia."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "các bài viết đồng hành của TorrentFreak: <a %(torrentfreak)s>thứ nhất</a>, <a %(torrentfreak_2)s>thứ hai</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Không lâu trước đây, các “thư viện bóng tối” đang dần biến mất. Sci-Hub, kho lưu trữ bất hợp pháp khổng lồ của các bài báo học thuật, đã ngừng tiếp nhận các tác phẩm mới do các vụ kiện tụng. “Thư viện Z”, thư viện sách bất hợp pháp lớn nhất, đã chứng kiến những người sáng lập bị cáo buộc bị bắt vì tội vi phạm bản quyền. Họ đã thoát khỏi sự bắt giữ một cách đáng kinh ngạc, nhưng thư viện của họ không kém phần bị đe dọa."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Khi Thư viện Z đối mặt với việc đóng cửa, tôi đã sao lưu toàn bộ thư viện của nó và đang tìm kiếm một nền tảng để lưu trữ. Đó là động lực của tôi khi bắt đầu Kho Lưu Trữ của Anna: tiếp tục sứ mệnh đằng sau những sáng kiến trước đó. Chúng tôi đã phát triển thành thư viện bóng tối lớn nhất thế giới, lưu trữ hơn 140 triệu văn bản có bản quyền trên nhiều định dạng — sách, bài báo học thuật, tạp chí, báo chí và hơn thế nữa."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Đội ngũ của tôi và tôi là những người theo chủ nghĩa lý tưởng. Chúng tôi tin rằng việc bảo tồn và lưu trữ những tệp này là đúng đắn về mặt đạo đức. Các thư viện trên khắp thế giới đang chứng kiến sự cắt giảm tài trợ, và chúng tôi cũng không thể tin tưởng vào di sản của nhân loại cho các tập đoàn."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Rồi AI xuất hiện. Hầu hết các công ty lớn xây dựng LLM đều liên hệ với chúng tôi để đào tạo trên dữ liệu của chúng tôi. Hầu hết (nhưng không phải tất cả!) các công ty có trụ sở tại Mỹ đã xem xét lại khi họ nhận ra tính bất hợp pháp của công việc của chúng tôi. Ngược lại, các công ty Trung Quốc đã nhiệt tình đón nhận bộ sưu tập của chúng tôi, dường như không bận tâm đến tính hợp pháp của nó. Điều này đáng chú ý vì vai trò của Trung Quốc là một bên ký kết hầu hết các hiệp ước bản quyền quốc tế lớn."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Chúng tôi đã cung cấp quyền truy cập tốc độ cao cho khoảng 30 công ty. Hầu hết trong số họ là các công ty LLM, và một số là các nhà môi giới dữ liệu, những người sẽ bán lại bộ sưu tập của chúng tôi. Hầu hết là người Trung Quốc, mặc dù chúng tôi cũng đã làm việc với các công ty từ Mỹ, Châu Âu, Nga, Hàn Quốc và Nhật Bản. DeepSeek <a %(arxiv)s>thừa nhận</a> rằng một phiên bản trước đó đã được đào tạo trên một phần bộ sưu tập của chúng tôi, mặc dù họ rất kín tiếng về mô hình mới nhất của họ (có lẽ cũng được đào tạo trên dữ liệu của chúng tôi)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Nếu phương Tây muốn dẫn đầu trong cuộc đua LLM, và cuối cùng là AGI, họ cần xem xét lại lập trường của mình về bản quyền, và sớm thôi. Cho dù bạn đồng ý hay không với chúng tôi về trường hợp đạo đức của chúng tôi, điều này hiện đang trở thành một vấn đề kinh tế, và thậm chí là an ninh quốc gia. Tất cả các khối quyền lực đang xây dựng các siêu nhà khoa học nhân tạo, siêu hacker và siêu quân đội. Tự do thông tin đang trở thành vấn đề sống còn cho các quốc gia này — thậm chí là vấn đề an ninh quốc gia."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Đội ngũ của chúng tôi đến từ khắp nơi trên thế giới, và chúng tôi không có sự liên kết cụ thể nào. Nhưng chúng tôi khuyến khích các quốc gia có luật bản quyền mạnh mẽ sử dụng mối đe dọa hiện hữu này để cải cách chúng. Vậy phải làm gì?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Khuyến nghị đầu tiên của chúng tôi rất đơn giản: rút ngắn thời hạn bản quyền. Ở Mỹ, bản quyền được cấp trong 70 năm sau khi tác giả qua đời. Điều này thật vô lý. Chúng ta có thể đưa điều này phù hợp với bằng sáng chế, được cấp trong 20 năm sau khi nộp đơn. Điều này nên đủ thời gian cho các tác giả của sách, bài báo, âm nhạc, nghệ thuật và các tác phẩm sáng tạo khác, để được bồi thường đầy đủ cho những nỗ lực của họ (bao gồm cả các dự án dài hạn như chuyển thể phim)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Sau đó, ít nhất, các nhà hoạch định chính sách nên bao gồm các ngoại lệ cho việc bảo tồn và phổ biến văn bản hàng loạt. Nếu mất doanh thu từ khách hàng cá nhân là mối lo ngại chính, việc phân phối ở cấp độ cá nhân có thể vẫn bị cấm. Đổi lại, những người có khả năng quản lý các kho lưu trữ lớn — các công ty đào tạo LLM, cùng với các thư viện và các kho lưu trữ khác — sẽ được bảo vệ bởi những ngoại lệ này."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Một số quốc gia đã thực hiện một phiên bản của điều này. TorrentFreak <a %(torrentfreak)s>báo cáo</a> rằng Trung Quốc và Nhật Bản đã giới thiệu các ngoại lệ AI vào luật bản quyền của họ. Chúng tôi không rõ điều này tương tác như thế nào với các hiệp ước quốc tế, nhưng chắc chắn nó mang lại sự bảo vệ cho các công ty trong nước của họ, điều này giải thích những gì chúng tôi đã thấy."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Đối với Kho Lưu Trữ của Anna — chúng tôi sẽ tiếp tục công việc ngầm của mình dựa trên niềm tin đạo đức. Tuy nhiên, mong muốn lớn nhất của chúng tôi là bước ra ánh sáng, và khuếch đại tác động của chúng tôi một cách hợp pháp. Xin hãy cải cách bản quyền."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Đọc các bài viết đồng hành của TorrentFreak: <a %(torrentfreak)s>thứ nhất</a>, <a %(torrentfreak_2)s>thứ hai</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Những người chiến thắng giải thưởng trực quan hóa ISBN trị giá 10.000 đô la"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "Tóm tắt: Chúng tôi đã nhận được một số bài dự thi đáng kinh ngạc cho giải thưởng trực quan hóa ISBN trị giá 10.000 đô la."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Vài tháng trước, chúng tôi đã công bố một <a %(all_isbns)s>giải thưởng 10.000 đô la</a> để tạo ra hình ảnh trực quan tốt nhất có thể về dữ liệu của chúng tôi hiển thị không gian ISBN. Chúng tôi nhấn mạnh việc hiển thị những tệp nào chúng tôi đã/chưa lưu trữ, và sau đó là một tập dữ liệu mô tả có bao nhiêu thư viện nắm giữ ISBN (một thước đo độ hiếm)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Chúng tôi đã bị choáng ngợp bởi phản hồi. Có rất nhiều sự sáng tạo. Một lời cảm ơn lớn đến tất cả những ai đã tham gia: năng lượng và sự nhiệt tình của bạn thật lây lan!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Cuối cùng, chúng tôi muốn trả lời các câu hỏi sau: <strong>những cuốn sách nào tồn tại trên thế giới, chúng tôi đã lưu trữ được bao nhiêu, và chúng tôi nên tập trung vào những cuốn sách nào tiếp theo?</strong> Thật tuyệt vời khi thấy nhiều người quan tâm đến những câu hỏi này."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Chúng tôi đã bắt đầu với một hình ảnh trực quan cơ bản. Trong chưa đầy 300kb, bức tranh này tóm tắt một cách ngắn gọn danh sách sách mở lớn nhất từng được tập hợp trong lịch sử nhân loại:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Tất cả ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Các tệp trong Lưu trữ của Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNO của CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Rò rỉ dữ liệu CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSID của DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Chỉ mục eBook của EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Sổ Đăng ký Nhà xuất bản Toàn cầu ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Thư viện Quốc gia Nga"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Thư viện Hoàng gia Trantor"

#, fuzzy
msgid "common.back"
msgstr "Quay lại"

#, fuzzy
msgid "common.forward"
msgstr "Tiếp"

#, fuzzy
msgid "common.last"
msgstr "Cuối"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Vui lòng xem <a %(all_isbns)s>bài viết blog gốc</a> để biết thêm thông tin."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Chúng tôi đã đưa ra một thử thách để cải thiện điều này. Chúng tôi sẽ trao giải nhất trị giá 6.000 đô la, giải nhì 3.000 đô la và giải ba 1.000 đô la. Do phản hồi áp đảo và các bài dự thi đáng kinh ngạc, chúng tôi đã quyết định tăng nhẹ quỹ giải thưởng và trao giải ba cho bốn người, mỗi người 500 đô la. Dưới đây là danh sách người chiến thắng, nhưng hãy chắc chắn xem tất cả các bài dự thi <a %(annas_archive)s>tại đây</a>, hoặc tải xuống <a %(a_2025_01_isbn_visualization_files)s>torrent kết hợp</a> của chúng tôi."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Giải nhất 6.000 đô la: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Bài <a %(phiresky_github)s>dự thi</a> này (<a %(annas_archive_note_2951)s>bình luận trên Gitlab</a>) đơn giản là mọi thứ chúng tôi mong muốn, và hơn thế nữa! Chúng tôi đặc biệt thích các tùy chọn hình ảnh hóa cực kỳ linh hoạt (thậm chí hỗ trợ các shader tùy chỉnh), nhưng với danh sách cài đặt sẵn toàn diện. Chúng tôi cũng thích cách mọi thứ hoạt động nhanh và mượt mà, cách triển khai đơn giản (thậm chí không có backend), bản đồ nhỏ thông minh và giải thích chi tiết trong <a %(phiresky_github)s>bài viết blog</a> của họ. Công việc tuyệt vời và xứng đáng giành chiến thắng!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Giải nhì 3.000 đô la: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Một <a %(annas_archive_note_2913)s>bài dự thi</a> đáng kinh ngạc khác. Không linh hoạt như giải nhất, nhưng chúng tôi thực sự thích hình ảnh hóa ở cấp độ vĩ mô của nó hơn giải nhất (đường cong lấp đầy không gian, biên giới, nhãn, làm nổi bật, di chuyển và phóng to). Một <a %(annas_archive_note_2971)s>bình luận</a> của Joe Davis đã gây ấn tượng với chúng tôi:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Mặc dù các hình vuông và hình chữ nhật hoàn hảo có tính thẩm mỹ toán học, nhưng chúng không cung cấp tính cục bộ vượt trội trong bối cảnh bản đồ. Tôi tin rằng sự bất đối xứng vốn có trong các đường cong Hilbert hoặc Morton cổ điển này không phải là một khuyết điểm mà là một đặc điểm. Giống như đường viền hình chiếc ủng nổi tiếng của Ý làm cho nó dễ nhận biết ngay lập tức trên bản đồ, những \"đặc điểm\" độc đáo của các đường cong này có thể đóng vai trò là điểm mốc nhận thức. Sự khác biệt này có thể nâng cao trí nhớ không gian và giúp người dùng định hướng, có thể giúp dễ dàng xác định các khu vực cụ thể hoặc nhận thấy các mẫu.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Và vẫn còn rất nhiều tùy chọn để hình ảnh hóa và kết xuất, cũng như giao diện người dùng cực kỳ mượt mà và trực quan. Một vị trí thứ hai vững chắc!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Giải ba 500 đô la #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Trong <a %(annas_archive_note_2940)s>bài dự thi</a> này, chúng tôi thực sự thích các loại chế độ xem khác nhau, đặc biệt là chế độ xem so sánh và nhà xuất bản."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Giải ba 500 đô la #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Mặc dù giao diện người dùng không phải là hoàn thiện nhất, nhưng <a %(annas_archive_note_2917)s>bài dự thi</a> này đáp ứng nhiều tiêu chí. Chúng tôi đặc biệt thích tính năng so sánh của nó."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Giải ba 500 đô la #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Giống như giải nhất, <a %(annas_archive_note_2975)s>bài dự thi</a> này đã gây ấn tượng với chúng tôi bởi tính linh hoạt của nó. Cuối cùng, đây là điều tạo nên một công cụ hình ảnh hóa tuyệt vời: tính linh hoạt tối đa cho người dùng chuyên nghiệp, trong khi vẫn giữ mọi thứ đơn giản cho người dùng trung bình."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Giải ba 500 đô la #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Bài <a %(annas_archive_note_2947)s>dự thi</a> cuối cùng nhận được giải thưởng khá cơ bản, nhưng có một số tính năng độc đáo mà chúng tôi thực sự thích. Chúng tôi thích cách họ hiển thị có bao nhiêu datasets bao phủ một ISBN cụ thể như một thước đo độ phổ biến/độ tin cậy. Chúng tôi cũng thực sự thích sự đơn giản nhưng hiệu quả của việc sử dụng thanh trượt độ mờ để so sánh."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Ý tưởng đáng chú ý"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Một số ý tưởng và triển khai khác mà chúng tôi đặc biệt thích:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Những tòa nhà chọc trời cho sự hiếm có"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Thống kê trực tiếp"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Chú thích, và cũng có thống kê trực tiếp"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Chế độ xem bản đồ độc đáo và bộ lọc"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Bảng màu mặc định mát mẻ và bản đồ nhiệt."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Dễ dàng chuyển đổi các Datasets để so sánh nhanh chóng."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Nhãn đẹp."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Thanh tỷ lệ với số lượng sách."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Nhiều thanh trượt để so sánh Datasets, như thể bạn là một DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Chúng tôi có thể tiếp tục một lúc nữa, nhưng hãy dừng lại ở đây. Hãy chắc chắn xem tất cả các bài nộp <a %(annas_archive)s>tại đây</a>, hoặc tải xuống <a %(a_2025_01_isbn_visualization_files)s>torrent kết hợp</a> của chúng tôi. Rất nhiều bài nộp, và mỗi bài mang đến một góc nhìn độc đáo, dù là trong giao diện người dùng hay triển khai."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Chúng tôi sẽ ít nhất tích hợp bài nộp đạt giải nhất vào trang web chính của chúng tôi, và có thể một số bài khác. Chúng tôi cũng đã bắt đầu suy nghĩ về cách tổ chức quá trình xác định, xác nhận, và sau đó lưu trữ những cuốn sách hiếm nhất. Sẽ có thêm thông tin về vấn đề này."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Cảm ơn tất cả mọi người đã tham gia. Thật tuyệt vời khi có rất nhiều người quan tâm."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Trái tim chúng tôi tràn đầy lòng biết ơn."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Hình dung Tất cả ISBN — Phần thưởng $10,000 trước ngày 31-01-2025"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Bức tranh này đại diện cho “danh sách sách” mở hoàn toàn lớn nhất từng được tập hợp trong lịch sử nhân loại."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Bức tranh này có kích thước 1000×800 pixel. Mỗi pixel đại diện cho 2.500 ISBN. Nếu chúng tôi có một tệp cho một ISBN, chúng tôi làm cho pixel đó xanh hơn. Nếu chúng tôi biết một ISBN đã được phát hành, nhưng chúng tôi không có tệp tương ứng, chúng tôi làm cho nó đỏ hơn."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Trong chưa đầy 300kb, bức tranh này tóm tắt đại diện cho “danh sách sách” mở hoàn toàn lớn nhất từng được tập hợp trong lịch sử nhân loại (vài trăm GB nén đầy đủ)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Nó cũng cho thấy: còn rất nhiều việc phải làm trong việc sao lưu sách (chúng tôi chỉ có 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Bối cảnh"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Làm thế nào để Lưu trữ của Anna đạt được sứ mệnh sao lưu tất cả kiến thức của nhân loại, mà không biết những cuốn sách nào vẫn còn ngoài kia? Chúng tôi cần một danh sách CẦN LÀM. Một cách để lập bản đồ này là thông qua số ISBN, mà từ những năm 1970 đã được gán cho mỗi cuốn sách xuất bản (ở hầu hết các quốc gia)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Không có cơ quan trung ương nào biết tất cả các gán ISBN. Thay vào đó, đó là một hệ thống phân tán, nơi các quốc gia nhận được dải số, sau đó gán các dải nhỏ hơn cho các nhà xuất bản lớn, những người có thể tiếp tục chia nhỏ dải cho các nhà xuất bản nhỏ hơn. Cuối cùng, các số cá nhân được gán cho các cuốn sách."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Chúng tôi đã bắt đầu lập bản đồ ISBN <a %(blog)s>hai năm trước</a> với việc thu thập dữ liệu từ ISBNdb. Kể từ đó, chúng tôi đã thu thập dữ liệu từ nhiều nguồn metadata khác, như <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, và nhiều hơn nữa. Danh sách đầy đủ có thể được tìm thấy trên các trang “Datasets” và “Torrents” trên Lưu trữ của Anna. Hiện tại, chúng tôi có bộ sưu tập metadata sách mở hoàn toàn, dễ dàng tải xuống lớn nhất thế giới (và do đó là ISBNs)."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Chúng tôi đã <a %(blog)s>viết rất nhiều</a> về lý do tại sao chúng tôi quan tâm đến việc bảo tồn, và tại sao chúng tôi hiện đang trong một cửa sổ quan trọng. Chúng tôi phải xác định các cuốn sách hiếm, ít được chú ý và có nguy cơ đặc biệt và bảo tồn chúng. Có metadata tốt về tất cả các cuốn sách trên thế giới giúp ích cho điều đó."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Hình dung"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Ngoài hình ảnh tổng quan, chúng tôi cũng có thể xem xét các datasets riêng lẻ mà chúng tôi đã thu thập. Sử dụng menu thả xuống và các nút để chuyển đổi giữa chúng."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Có rất nhiều mẫu thú vị để thấy trong những bức tranh này. Tại sao có sự đều đặn của các dòng và khối, dường như xảy ra ở các quy mô khác nhau? Những khu vực trống là gì? Tại sao một số datasets lại tập trung như vậy? Chúng tôi sẽ để những câu hỏi này như một bài tập cho người đọc."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Giải thưởng $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Có rất nhiều điều để khám phá ở đây, vì vậy chúng tôi đang công bố một giải thưởng cho việc cải thiện hình ảnh hóa ở trên. Không giống như hầu hết các giải thưởng của chúng tôi, giải thưởng này có thời hạn. Bạn phải <a %(annas_archive)s>nộp</a> mã nguồn mở của mình trước ngày 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Bài nộp tốt nhất sẽ nhận được $6,000, vị trí thứ hai là $3,000, và vị trí thứ ba là $1,000. Tất cả các giải thưởng sẽ được trao bằng Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Dưới đây là các tiêu chí tối thiểu. Nếu không có bài nộp nào đáp ứng các tiêu chí, chúng tôi có thể vẫn trao một số giải thưởng, nhưng điều đó sẽ do chúng tôi quyết định."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork repo này, và chỉnh sửa HTML của bài viết blog này (không cho phép các backend khác ngoài backend Flask của chúng tôi)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Làm cho hình ảnh trên có thể phóng to mượt mà, để bạn có thể phóng to đến từng ISBN. Nhấp vào ISBNs nên dẫn bạn đến trang metadata hoặc tìm kiếm trên Lưu trữ của Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Bạn vẫn phải có thể chuyển đổi giữa tất cả các datasets khác nhau."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Dải số quốc gia và dải số nhà xuất bản nên được làm nổi bật khi di chuột. Bạn có thể sử dụng ví dụ như <a %(github_xlcnd_isbnlib)s>data4info.py trong isbnlib</a> cho thông tin quốc gia, và thu thập dữ liệu “isbngrp” của chúng tôi cho các nhà xuất bản (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Nó phải hoạt động tốt trên cả máy tính để bàn và di động."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Để có điểm thưởng (đây chỉ là ý tưởng — hãy để sự sáng tạo của bạn bay xa):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sự cân nhắc mạnh mẽ sẽ được dành cho tính khả dụng và vẻ đẹp của nó."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Hiển thị metadata thực tế cho từng ISBN khi phóng to, chẳng hạn như tiêu đề và tác giả."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Đường cong lấp đầy không gian tốt hơn. Ví dụ: một đường zig-zag, đi từ 0 đến 4 trên hàng đầu tiên và sau đó quay lại (ngược lại) từ 5 đến 9 trên hàng thứ hai — áp dụng đệ quy."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Các bảng màu khác nhau hoặc có thể tùy chỉnh."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Các chế độ xem đặc biệt để so sánh datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Các cách để gỡ lỗi các vấn đề, chẳng hạn như các metadata khác không đồng ý tốt (ví dụ: tiêu đề khác biệt lớn)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Chú thích hình ảnh với nhận xét về ISBN hoặc phạm vi."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Bất kỳ phương pháp nào để xác định sách hiếm hoặc có nguy cơ."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Bất kỳ ý tưởng sáng tạo nào bạn có thể nghĩ ra!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Bạn CÓ THỂ hoàn toàn đi chệch khỏi các tiêu chí tối thiểu và thực hiện một hình ảnh hóa hoàn toàn khác. Nếu nó thực sự ngoạn mục, thì điều đó đủ điều kiện cho phần thưởng, nhưng theo quyết định của chúng tôi."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Gửi bài bằng cách đăng bình luận vào <a %(annas_archive)s>vấn đề này</a> với liên kết đến kho lưu trữ đã fork của bạn, yêu cầu hợp nhất hoặc diff."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Mã"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Mã để tạo ra những hình ảnh này, cũng như các ví dụ khác, có thể được tìm thấy trong <a %(annas_archive)s>thư mục này</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Chúng tôi đã nghĩ ra một định dạng dữ liệu gọn nhẹ, với tất cả thông tin ISBN cần thiết khoảng 75MB (đã nén). Mô tả định dạng dữ liệu và mã để tạo ra nó có thể được tìm thấy <a %(annas_archive_l1244_1319)s>tại đây</a>. Để nhận phần thưởng, bạn không bắt buộc phải sử dụng cái này, nhưng có lẽ đây là định dạng tiện lợi nhất để bắt đầu. Bạn có thể chuyển đổi metadata của chúng tôi theo bất kỳ cách nào bạn muốn (mặc dù tất cả mã của bạn phải là mã nguồn mở)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Chúng tôi rất mong chờ những gì bạn sẽ tạo ra. Chúc may mắn!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Các Container của Lưu trữ Anna (AAC): tiêu chuẩn hóa các phát hành từ thư viện bóng tối lớn nhất thế giới"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Lưu trữ Anna đã trở thành thư viện bóng tối lớn nhất thế giới, yêu cầu chúng tôi phải tiêu chuẩn hóa các phát hành của mình."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Lưu trữ Anna</a> đã trở thành thư viện bóng tối lớn nhất thế giới, và là thư viện bóng tối duy nhất ở quy mô này hoàn toàn mã nguồn mở và dữ liệu mở. Dưới đây là một bảng từ trang Datasets của chúng tôi (đã được chỉnh sửa nhẹ):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Chúng tôi đã đạt được điều này theo ba cách:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Phản chiếu các thư viện bóng tối dữ liệu mở hiện có (như Sci-Hub và Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Giúp đỡ các thư viện bóng tối muốn mở hơn, nhưng không có thời gian hoặc nguồn lực để làm điều đó (như bộ sưu tập truyện tranh Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Thu thập dữ liệu từ các thư viện không muốn chia sẻ số lượng lớn (như Thư viện Z)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Đối với (2) và (3), hiện tại chúng tôi quản lý một bộ sưu tập torrent đáng kể (hàng trăm TB). Cho đến nay, chúng tôi đã tiếp cận các bộ sưu tập này như những trường hợp riêng lẻ, nghĩa là cơ sở hạ tầng và tổ chức dữ liệu được thiết kế riêng cho từng bộ sưu tập. Điều này làm tăng đáng kể chi phí cho mỗi lần phát hành và đặc biệt khó khăn khi thực hiện các lần phát hành gia tăng."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Đó là lý do tại sao chúng tôi quyết định tiêu chuẩn hóa các lần phát hành của mình. Đây là một bài viết kỹ thuật trong đó chúng tôi giới thiệu tiêu chuẩn của mình: <strong>Các Container của Lưu Trữ Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Mục tiêu thiết kế"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Trường hợp sử dụng chính của chúng tôi là phân phối các tệp và metadata liên quan từ các bộ sưu tập hiện có khác nhau. Những cân nhắc quan trọng nhất của chúng tôi là:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Các tệp và metadata không đồng nhất, ở định dạng gần nhất có thể với bản gốc."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Các định danh không đồng nhất trong các thư viện nguồn, hoặc thậm chí không có định danh."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Phát hành riêng biệt metadata so với dữ liệu tệp, hoặc phát hành chỉ metadata (ví dụ: phát hành ISBNdb của chúng tôi)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Phân phối qua torrent, mặc dù có khả năng sử dụng các phương pháp phân phối khác (ví dụ: IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Các bản ghi không thể thay đổi, vì chúng tôi nên giả định rằng các torrent của chúng tôi sẽ tồn tại mãi mãi."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Phát hành gia tăng / phát hành có thể bổ sung."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Có thể đọc và ghi bằng máy, một cách thuận tiện và nhanh chóng, đặc biệt là cho ngăn xếp của chúng tôi (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Dễ dàng kiểm tra bằng mắt thường, mặc dù điều này là thứ yếu so với khả năng đọc của máy."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Dễ dàng gieo mầm bộ sưu tập của chúng tôi với một seedbox thuê tiêu chuẩn."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Dữ liệu nhị phân có thể được phục vụ trực tiếp bởi các máy chủ web như Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Một số mục tiêu không phải là:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Chúng tôi không quan tâm đến việc các tệp dễ dàng điều hướng thủ công trên đĩa, hoặc có thể tìm kiếm mà không cần xử lý trước."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Chúng tôi không quan tâm đến việc tương thích trực tiếp với phần mềm thư viện hiện có."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Mặc dù bất kỳ ai cũng có thể dễ dàng gieo mầm bộ sưu tập của chúng tôi bằng cách sử dụng torrent, chúng tôi không mong đợi các tệp có thể sử dụng được mà không có kiến thức kỹ thuật đáng kể và cam kết."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Vì Lưu Trữ Anna là mã nguồn mở, chúng tôi muốn sử dụng định dạng của mình trực tiếp. Khi chúng tôi làm mới chỉ mục tìm kiếm của mình, chúng tôi chỉ truy cập các đường dẫn có sẵn công khai, để bất kỳ ai sao chép thư viện của chúng tôi có thể bắt đầu nhanh chóng."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Tiêu chuẩn"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Cuối cùng, chúng tôi đã chọn một tiêu chuẩn tương đối đơn giản. Nó khá lỏng lẻo, không mang tính quy chuẩn, và đang trong quá trình hoàn thiện."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna’s Archive Container) là một mục duy nhất bao gồm <strong>metadata</strong>, và tùy chọn <strong>dữ liệu nhị phân</strong>, cả hai đều không thể thay đổi. Nó có một định danh duy nhất toàn cầu, được gọi là <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Bộ sưu tập.</strong> Mỗi AAC thuộc về một bộ sưu tập, theo định nghĩa là một danh sách các AAC có tính nhất quán về ngữ nghĩa. Điều đó có nghĩa là nếu bạn thực hiện một thay đổi đáng kể đối với định dạng của metadata, thì bạn phải tạo một bộ sưu tập mới."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Bộ sưu tập “bản ghi” và “tệp”.</strong> Theo thông lệ, thường tiện lợi khi phát hành “bản ghi” và “tệp” dưới dạng các bộ sưu tập khác nhau, để chúng có thể được phát hành theo các lịch trình khác nhau, ví dụ như dựa trên tốc độ thu thập dữ liệu. Một “bản ghi” là một bộ sưu tập chỉ chứa metadata, bao gồm thông tin như tiêu đề sách, tác giả, ISBN, v.v., trong khi “tệp” là các bộ sưu tập chứa các tệp thực tế (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Định dạng của AACID là: <code style=\"color: #0093ff\">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Ví dụ, một AACID thực tế mà chúng tôi đã phát hành là <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{collection}</code>: tên bộ sưu tập, có thể chứa các chữ cái ASCII, số và dấu gạch dưới (nhưng không có dấu gạch dưới kép)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: một phiên bản ngắn của ISO 8601, luôn ở UTC, ví dụ: <code>20220723T194746Z</code>. Số này phải tăng đơn điệu cho mỗi lần phát hành, mặc dù ý nghĩa chính xác của nó có thể khác nhau tùy theo bộ sưu tập. Chúng tôi đề xuất sử dụng thời gian thu thập dữ liệu hoặc tạo ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{collection-specific ID}</code>: một định danh cụ thể của bộ sưu tập, nếu có, ví dụ: ID của Thư viện Z. Có thể bỏ qua hoặc cắt ngắn. Phải bỏ qua hoặc cắt ngắn nếu AACID vượt quá 150 ký tự."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: một UUID nhưng được nén thành ASCII, ví dụ: sử dụng base57. Hiện tại chúng tôi sử dụng thư viện Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Phạm vi AACID.</strong> Vì AACID chứa các dấu thời gian tăng đơn điệu, chúng tôi có thể sử dụng điều đó để biểu thị phạm vi trong một bộ sưu tập cụ thể. Chúng tôi sử dụng định dạng này: <code style=\"color: blue\">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, trong đó các dấu thời gian là bao gồm. Điều này nhất quán với ký hiệu ISO 8601. Các phạm vi là liên tục và có thể chồng chéo, nhưng trong trường hợp chồng chéo phải chứa các bản ghi giống hệt như bản đã phát hành trước đó trong bộ sưu tập đó (vì AAC là không thể thay đổi). Không được phép thiếu bản ghi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Tệp metadata.</strong> Một tệp metadata chứa metadata của một phạm vi AAC, cho một bộ sưu tập cụ thể. Chúng có các thuộc tính sau:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Tên tệp phải là một phạm vi AACID, được tiền tố bằng <code style=\"color: red\">annas_archive_meta__</code> và theo sau là <code>.jsonl.zstd</code>. Ví dụ, một trong những phát hành của chúng tôi được gọi là<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Như được chỉ định bởi phần mở rộng tệp, loại tệp là <a %(jsonlines)s>JSON Lines</a> được nén bằng <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Mỗi đối tượng JSON phải chứa các trường sau ở cấp cao nhất: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (tùy chọn). Không được phép có các trường khác."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> là metadata tùy ý, theo ngữ nghĩa của bộ sưu tập. Nó phải nhất quán về ngữ nghĩa trong bộ sưu tập."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> là tùy chọn, và là tên của thư mục dữ liệu nhị phân chứa dữ liệu nhị phân tương ứng. Tên tệp của dữ liệu nhị phân tương ứng trong thư mục đó là AACID của bản ghi."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Tiền tố <code style=\"color: red\">annas_archive_meta__</code> có thể được điều chỉnh theo tên của tổ chức của bạn, ví dụ: <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Thư mục dữ liệu nhị phân.</strong> Một thư mục chứa dữ liệu nhị phân của một phạm vi AAC, cho một bộ sưu tập cụ thể. Chúng có các thuộc tính sau:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Tên thư mục phải là một phạm vi AACID, được tiền tố bằng <code style=\"color: green\">annas_archive_data__</code>, và không có hậu tố. Ví dụ, một trong những phát hành thực tế của chúng tôi có một thư mục được gọi là<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Thư mục phải chứa các tệp dữ liệu cho tất cả các AAC trong phạm vi được chỉ định. Mỗi tệp dữ liệu phải có AACID của nó làm tên tệp (không có phần mở rộng)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Nên tạo các thư mục này có kích thước dễ quản lý, ví dụ không lớn hơn 100GB-1TB mỗi thư mục, mặc dù khuyến nghị này có thể thay đổi theo thời gian."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Các tệp metadata và thư mục dữ liệu nhị phân có thể được gói trong các torrent, với một torrent cho mỗi tệp metadata, hoặc một torrent cho mỗi thư mục dữ liệu nhị phân. Các torrent phải có tên tệp/thư mục gốc cộng với hậu tố <code>.torrent</code> làm tên tệp của chúng."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Ví dụ"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Hãy xem xét bản phát hành Thư viện Z gần đây của chúng tôi như một ví dụ. Nó bao gồm hai bộ sưu tập: “<span style=\"background: #fffaa3\">zlib3_records</span>” và “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Điều này cho phép chúng tôi tách riêng và phát hành các bản ghi metadata từ các tệp sách thực tế. Do đó, chúng tôi đã phát hành hai torrent với các tệp metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Chúng tôi cũng đã phát hành một loạt torrent với các thư mục dữ liệu nhị phân, nhưng chỉ cho bộ sưu tập “<span style=\"background: #ffd6fe\">zlib3_files</span>”, tổng cộng 62:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Bằng cách chạy <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> chúng ta có thể thấy những gì bên trong:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Trong trường hợp này, đó là metadata của một cuốn sách như được báo cáo bởi Thư viện Z. Ở cấp cao nhất, chúng ta chỉ có “aacid” và “metadata”, nhưng không có “data_folder”, vì không có dữ liệu nhị phân tương ứng. AACID chứa “22430000” làm ID chính, mà chúng ta có thể thấy được lấy từ “zlibrary_id”. Chúng ta có thể mong đợi các AAC khác trong bộ sưu tập này có cấu trúc tương tự."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Bây giờ hãy chạy <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Đây là metadata AAC nhỏ hơn nhiều, mặc dù phần lớn của AAC này nằm ở nơi khác trong một tệp nhị phân! Sau cùng, lần này chúng ta có “data_folder”, vì vậy chúng ta có thể mong đợi dữ liệu nhị phân tương ứng được đặt tại <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” chứa “zlibrary_id”, vì vậy chúng ta có thể dễ dàng liên kết nó với AAC tương ứng trong bộ sưu tập “zlib_records”. Chúng ta có thể đã liên kết theo nhiều cách khác nhau, ví dụ thông qua AACID — tiêu chuẩn không quy định điều đó."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Lưu ý rằng cũng không cần thiết trường “metadata” phải là JSON. Nó có thể là một chuỗi chứa XML hoặc bất kỳ định dạng dữ liệu nào khác. Bạn thậm chí có thể lưu trữ thông tin metadata trong blob nhị phân liên kết, ví dụ nếu đó là rất nhiều dữ liệu."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Kết luận"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Với tiêu chuẩn này, chúng ta có thể phát hành dữ liệu một cách gia tăng hơn, và dễ dàng thêm các nguồn dữ liệu mới. Chúng tôi đã có một vài bản phát hành thú vị trong kế hoạch!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Chúng tôi cũng hy vọng rằng các thư viện bóng tối khác có thể dễ dàng sao chép các bộ sưu tập của chúng tôi. Sau cùng, mục tiêu của chúng tôi là bảo tồn kiến thức và văn hóa của con người mãi mãi, vì vậy càng nhiều sự dư thừa càng tốt."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Cập nhật của Anna: kho lưu trữ mã nguồn mở hoàn toàn, ElasticSearch, hơn 300GB bìa sách"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Chúng tôi đã làm việc không ngừng nghỉ để cung cấp một giải pháp thay thế tốt với Kho lưu trữ của Anna. Dưới đây là một số điều chúng tôi đã đạt được gần đây."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Với Thư viện Z bị sập và những người sáng lập (bị cáo buộc) của nó bị bắt, chúng tôi đã làm việc không ngừng nghỉ để cung cấp một giải pháp thay thế tốt với Kho lưu trữ của Anna (chúng tôi sẽ không liên kết nó ở đây, nhưng bạn có thể tìm kiếm trên Google). Dưới đây là một số điều chúng tôi đã đạt được gần đây."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Kho lưu trữ của Anna là mã nguồn mở hoàn toàn"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Chúng tôi tin rằng thông tin nên được tự do, và mã nguồn của chúng tôi cũng không ngoại lệ. Chúng tôi đã phát hành tất cả mã nguồn của mình trên Gitlab riêng của chúng tôi: <a %(annas_archive)s>Phần mềm của Anna</a>. Chúng tôi cũng sử dụng trình theo dõi vấn đề để tổ chức công việc của mình. Nếu bạn muốn tham gia vào phát triển của chúng tôi, đây là một nơi tuyệt vời để bắt đầu."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Để cho bạn thấy một chút về những gì chúng tôi đang làm, hãy xem công việc gần đây của chúng tôi về cải thiện hiệu suất phía khách hàng. Vì chúng tôi chưa thực hiện phân trang, chúng tôi thường trả về các trang tìm kiếm rất dài, với 100-200 kết quả. Chúng tôi không muốn cắt ngắn kết quả tìm kiếm quá sớm, nhưng điều này có nghĩa là nó sẽ làm chậm một số thiết bị. Để giải quyết vấn đề này, chúng tôi đã thực hiện một mẹo nhỏ: chúng tôi bọc hầu hết các kết quả tìm kiếm trong các bình luận HTML (<code><!-- --></code>), và sau đó viết một chút Javascript để phát hiện khi nào một kết quả nên trở nên hiển thị, vào lúc đó chúng tôi sẽ mở bình luận:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"ảo hóa\" được triển khai trong 23 dòng, không cần thư viện cầu kỳ! Đây là loại mã thực dụng nhanh chóng mà bạn có khi có thời gian hạn chế và các vấn đề thực tế cần được giải quyết. Đã có báo cáo rằng tìm kiếm của chúng tôi hiện hoạt động tốt trên các thiết bị chậm!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Một nỗ lực lớn khác là tự động hóa việc xây dựng cơ sở dữ liệu. Khi chúng tôi ra mắt, chúng tôi chỉ kéo các nguồn khác nhau một cách ngẫu nhiên. Bây giờ chúng tôi muốn giữ chúng được cập nhật, vì vậy chúng tôi đã viết một loạt các kịch bản để tải xuống metadata mới từ hai nhánh của Library Genesis và tích hợp chúng. Mục tiêu không chỉ là làm cho điều này hữu ích cho kho lưu trữ của chúng tôi, mà còn làm cho mọi thứ dễ dàng cho bất kỳ ai muốn thử nghiệm với metadata của thư viện bóng tối. Mục tiêu sẽ là một sổ tay Jupyter có tất cả các loại metadata thú vị có sẵn, để chúng tôi có thể thực hiện nhiều nghiên cứu hơn như tìm hiểu <a %(blog)s>phần trăm ISBN được bảo tồn mãi mãi</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Cuối cùng, chúng tôi đã cải tiến hệ thống quyên góp của mình. Bây giờ bạn có thể sử dụng thẻ tín dụng để gửi tiền trực tiếp vào ví tiền điện tử của chúng tôi, mà không cần thực sự biết gì về tiền điện tử. Chúng tôi sẽ tiếp tục theo dõi xem điều này hoạt động tốt như thế nào trong thực tế, nhưng đây là một bước tiến lớn."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Chuyển sang ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Một trong những <a %(annas_archive)s>vé</a> của chúng tôi là một túi vấn đề với hệ thống tìm kiếm của chúng tôi. Chúng tôi đã sử dụng tìm kiếm toàn văn MySQL, vì chúng tôi đã có tất cả dữ liệu của mình trong MySQL. Nhưng nó có những giới hạn:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Một số truy vấn mất rất nhiều thời gian, đến mức chúng chiếm hết tất cả các kết nối mở."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Theo mặc định, MySQL có độ dài từ tối thiểu, hoặc chỉ mục của bạn có thể trở nên rất lớn. Mọi người báo cáo không thể tìm kiếm “Ben Hur”."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Tìm kiếm chỉ nhanh khi được tải đầy đủ vào bộ nhớ, điều này yêu cầu chúng tôi phải có một máy đắt tiền hơn để chạy điều này, cộng với một số lệnh để tải trước chỉ mục khi khởi động."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Chúng tôi sẽ không thể mở rộng nó dễ dàng để xây dựng các tính năng mới, như <a %(wikipedia_cjk_characters)s>tách từ tốt hơn cho các ngôn ngữ không có khoảng trắng</a>, lọc/phân loại, sắp xếp, gợi ý \"bạn có ý định\", tự động hoàn thành, v.v."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Sau khi nói chuyện với một loạt các chuyên gia, chúng tôi đã chọn ElasticSearch. Nó không hoàn hảo (các gợi ý \"bạn có ý định\" và tính năng tự động hoàn thành mặc định của họ không tốt), nhưng nhìn chung nó tốt hơn nhiều so với MySQL cho tìm kiếm. Chúng tôi vẫn chưa <a %(youtube)s>quá hài lòng</a> khi sử dụng nó cho bất kỳ dữ liệu quan trọng nào (mặc dù họ đã có nhiều <a %(elastic_co)s>tiến bộ</a>), nhưng nhìn chung chúng tôi khá hài lòng với sự chuyển đổi này."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Hiện tại, chúng tôi đã triển khai tìm kiếm nhanh hơn nhiều, hỗ trợ ngôn ngữ tốt hơn, sắp xếp độ liên quan tốt hơn, các tùy chọn sắp xếp khác nhau và lọc theo ngôn ngữ/loại sách/loại tệp. Nếu bạn tò mò về cách nó hoạt động, <a %(annas_archive_l140)s>hãy</a> <a %(annas_archive_l1115)s>xem</a> <a %(annas_archive_l1635)s>qua</a>. Nó khá dễ tiếp cận, mặc dù có thể cần thêm một số chú thích…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "Hơn 300GB bìa sách được phát hành"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Cuối cùng, chúng tôi vui mừng thông báo một bản phát hành nhỏ. Trong sự hợp tác với những người điều hành nhánh Libgen.rs, chúng tôi đang chia sẻ tất cả bìa sách của họ thông qua torrents và IPFS. Điều này sẽ phân phối tải trọng của việc xem bìa sách giữa nhiều máy hơn và sẽ bảo quản chúng tốt hơn. Trong nhiều trường hợp (nhưng không phải tất cả), bìa sách được bao gồm trong các tệp tin, vì vậy đây là loại \"dữ liệu dẫn xuất\". Nhưng có nó trong IPFS vẫn rất hữu ích cho hoạt động hàng ngày của cả Anna’s Archive và các nhánh khác nhau của Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Như thường lệ, bạn có thể tìm thấy bản phát hành này tại Pirate Library Mirror (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). Chúng tôi sẽ không liên kết đến nó ở đây, nhưng bạn có thể dễ dàng tìm thấy nó."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hy vọng chúng tôi có thể giảm tốc độ một chút, bây giờ chúng tôi có một lựa chọn thay thế khá tốt cho Thư viện Z. Khối lượng công việc này không thực sự bền vững. Nếu bạn quan tâm đến việc giúp đỡ với lập trình, vận hành máy chủ, hoặc công việc bảo tồn, hãy liên hệ với chúng tôi. Vẫn còn rất nhiều <a %(annas_archive)s>công việc cần làm</a>. Cảm ơn sự quan tâm và hỗ trợ của bạn."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna’s Archive đã sao lưu thư viện bóng tối truyện tranh lớn nhất thế giới (95TB) — bạn có thể giúp gieo hạt"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Thư viện bóng tối truyện tranh lớn nhất thế giới đã có một điểm thất bại duy nhất... cho đến hôm nay."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Thảo luận trên Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Thư viện bóng tối truyện tranh lớn nhất có thể là của một nhánh Library Genesis cụ thể: Libgen.li. Người quản trị duy nhất điều hành trang web đó đã thu thập được một bộ sưu tập truyện tranh khổng lồ với hơn 2 triệu tệp, tổng cộng hơn 95TB. Tuy nhiên, không giống như các bộ sưu tập Library Genesis khác, bộ sưu tập này không có sẵn số lượng lớn thông qua torrents. Bạn chỉ có thể truy cập các truyện tranh này riêng lẻ thông qua máy chủ cá nhân chậm của anh ấy — một điểm thất bại duy nhất. Cho đến hôm nay!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Trong bài viết này, chúng tôi sẽ cho bạn biết thêm về bộ sưu tập này và về chiến dịch gây quỹ của chúng tôi để hỗ trợ thêm cho công việc này."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Tiến sĩ Barbara Gordon cố gắng đánh mất mình trong thế giới tầm thường của thư viện…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Các nhánh của Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Trước tiên, một số thông tin cơ bản. Bạn có thể biết đến Library Genesis với bộ sưu tập sách đồ sộ của họ. Ít người biết rằng các tình nguyện viên của Library Genesis đã tạo ra các dự án khác, chẳng hạn như một bộ sưu tập tạp chí và tài liệu tiêu chuẩn lớn, một bản sao lưu đầy đủ của Sci-Hub (hợp tác với người sáng lập Sci-Hub, Alexandra Elbakyan), và thực sự là một bộ sưu tập truyện tranh khổng lồ."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Tại một thời điểm nào đó, các nhà điều hành khác nhau của các bản sao Library Genesis đã đi theo con đường riêng của họ, điều này đã dẫn đến tình trạng hiện tại có một số \"nhánh\" khác nhau, tất cả vẫn mang tên Library Genesis. Nhánh Libgen.li đặc biệt có bộ sưu tập truyện tranh này, cũng như một bộ sưu tập tạp chí lớn (mà chúng tôi cũng đang làm việc)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Hợp tác"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Với kích thước của nó, bộ sưu tập này đã nằm trong danh sách mong muốn của chúng tôi từ lâu, vì vậy sau khi thành công trong việc sao lưu Thư viện Z, chúng tôi đã đặt mục tiêu vào bộ sưu tập này. Ban đầu, chúng tôi đã trích xuất trực tiếp, điều này khá thách thức, vì máy chủ của họ không ở trong tình trạng tốt nhất. Chúng tôi đã thu được khoảng 15TB theo cách này, nhưng tiến độ rất chậm."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "May mắn thay, chúng tôi đã liên lạc được với người điều hành thư viện, người đã đồng ý gửi tất cả dữ liệu cho chúng tôi trực tiếp, điều này nhanh hơn rất nhiều. Tuy nhiên, vẫn mất hơn nửa năm để chuyển và xử lý tất cả dữ liệu, và chúng tôi suýt mất tất cả do hỏng đĩa, điều này có nghĩa là phải bắt đầu lại từ đầu."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Trải nghiệm này khiến chúng tôi tin rằng điều quan trọng là phải đưa dữ liệu này ra ngoài càng nhanh càng tốt, để nó có thể được sao lưu rộng rãi. Chúng tôi chỉ còn một hoặc hai sự cố không may xảy ra là có thể mất bộ sưu tập này mãi mãi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Bộ sưu tập"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Di chuyển nhanh có nghĩa là bộ sưu tập có phần hơi lộn xộn… Hãy cùng xem qua. Hãy tưởng tượng chúng ta có một hệ thống tệp (thực tế chúng tôi đang chia nhỏ thành các torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Thư mục đầu tiên, <code>/repository</code>, là phần có cấu trúc hơn của điều này. Thư mục này chứa các “thousand dirs”: các thư mục mỗi thư mục có hàng nghìn tệp, được đánh số tăng dần trong cơ sở dữ liệu. Thư mục <code>0</code> chứa các tệp có comic_id từ 0–999, và cứ thế tiếp tục."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Đây là cùng một sơ đồ mà Library Genesis đã sử dụng cho các bộ sưu tập tiểu thuyết và phi tiểu thuyết của mình. Ý tưởng là mỗi “thousand dir” sẽ tự động được chuyển thành một torrent ngay khi nó được lấp đầy."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Tuy nhiên, người điều hành Libgen.li chưa bao giờ tạo torrent cho bộ sưu tập này, vì vậy các thousand dirs có thể trở nên bất tiện và nhường chỗ cho các “unsorted dirs”. Đây là <code>/comics0</code> đến <code>/comics4</code>. Tất cả đều chứa các cấu trúc thư mục độc đáo, có lẽ có ý nghĩa khi thu thập các tệp, nhưng bây giờ không còn ý nghĩa nhiều đối với chúng tôi. May mắn thay, metadata vẫn tham chiếu trực tiếp đến tất cả các tệp này, vì vậy tổ chức lưu trữ của chúng trên đĩa thực sự không quan trọng!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata có sẵn dưới dạng cơ sở dữ liệu MySQL. Điều này có thể được tải xuống trực tiếp từ trang web Libgen.li, nhưng chúng tôi cũng sẽ cung cấp nó trong một torrent, cùng với bảng của chúng tôi với tất cả các mã băm MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Phân tích"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Khi bạn nhận được 95TB đổ vào cụm lưu trữ của mình, bạn cố gắng hiểu xem có gì trong đó… Chúng tôi đã thực hiện một số phân tích để xem liệu chúng tôi có thể giảm kích thước một chút hay không, chẳng hạn như bằng cách loại bỏ các bản sao. Dưới đây là một số phát hiện của chúng tôi:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Các bản sao ngữ nghĩa (các bản quét khác nhau của cùng một cuốn sách) về mặt lý thuyết có thể được lọc ra, nhưng điều này rất khó khăn. Khi xem xét thủ công qua các truyện tranh, chúng tôi đã tìm thấy quá nhiều kết quả dương tính giả."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Có một số bản sao chỉ theo MD5, điều này tương đối lãng phí, nhưng việc lọc chúng ra chỉ giúp tiết kiệm khoảng 1% in. Ở quy mô này, đó vẫn là khoảng 1TB, nhưng cũng ở quy mô này, 1TB thực sự không quan trọng. Chúng tôi thà không mạo hiểm vô tình phá hủy dữ liệu trong quá trình này."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Chúng tôi đã tìm thấy một loạt dữ liệu không phải sách, chẳng hạn như phim dựa trên truyện tranh. Điều đó cũng có vẻ lãng phí, vì những thứ này đã có sẵn rộng rãi thông qua các phương tiện khác. Tuy nhiên, chúng tôi nhận ra rằng chúng tôi không thể chỉ lọc ra các tệp phim, vì cũng có <em>truyện tranh tương tác</em> được phát hành trên máy tính, mà ai đó đã ghi lại và lưu dưới dạng phim."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Cuối cùng, bất cứ thứ gì chúng tôi có thể xóa khỏi bộ sưu tập cũng chỉ tiết kiệm được vài phần trăm. Sau đó, chúng tôi nhớ rằng chúng tôi là những người tích trữ dữ liệu, và những người sẽ sao chép điều này cũng là những người tích trữ dữ liệu, vì vậy, “BẠN NÓI GÌ, XÓA?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Do đó, chúng tôi xin giới thiệu với bạn, bộ sưu tập đầy đủ, không chỉnh sửa. Đó là rất nhiều dữ liệu, nhưng chúng tôi hy vọng đủ người sẽ quan tâm để chia sẻ nó."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Gây quỹ"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Chúng tôi đang phát hành dữ liệu này trong một số phần lớn. Torrent đầu tiên là của <code>/comics0</code>, mà chúng tôi đã đặt vào một tệp .tar khổng lồ 12TB. Điều đó tốt hơn cho ổ cứng và phần mềm torrent của bạn hơn là hàng triệu tệp nhỏ hơn."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Là một phần của đợt phát hành này, chúng tôi đang thực hiện một cuộc gây quỹ. Chúng tôi đang tìm cách huy động 20.000 đô la để trang trải chi phí hoạt động và hợp đồng cho bộ sưu tập này, cũng như cho phép các dự án đang diễn ra và trong tương lai. Chúng tôi có một số <em>dự án lớn</em> đang được thực hiện."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Tôi đang hỗ trợ ai với khoản quyên góp của mình?</em> Tóm lại: chúng tôi đang sao lưu tất cả kiến thức và văn hóa của nhân loại, và làm cho nó dễ dàng truy cập. Tất cả mã và dữ liệu của chúng tôi đều là mã nguồn mở, chúng tôi là một dự án hoàn toàn do tình nguyện viên điều hành, và chúng tôi đã lưu trữ được 125TB sách cho đến nay (ngoài các torrent hiện có của Libgen và Scihub). Cuối cùng, chúng tôi đang xây dựng một bánh đà cho phép và khuyến khích mọi người tìm kiếm, quét và sao lưu tất cả các cuốn sách trên thế giới. Chúng tôi sẽ viết về kế hoạch tổng thể của mình trong một bài viết tương lai. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Nếu bạn quyên góp cho một thành viên “Amazing Archivist” 12 tháng (780 đô la), bạn sẽ được <strong>“nhận nuôi một torrent”</strong>, nghĩa là chúng tôi sẽ đặt tên người dùng hoặc thông điệp của bạn vào tên tệp của một trong các torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Bạn có thể quyên góp bằng cách truy cập <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a> và nhấp vào nút “Quyên góp”. Chúng tôi cũng đang tìm kiếm thêm tình nguyện viên: kỹ sư phần mềm, nhà nghiên cứu bảo mật, chuyên gia thương mại ẩn danh và dịch giả. Bạn cũng có thể hỗ trợ chúng tôi bằng cách cung cấp dịch vụ lưu trữ. Và tất nhiên, hãy chia sẻ các torrent của chúng tôi!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Cảm ơn tất cả những người đã hào phóng hỗ trợ chúng tôi! Bạn thực sự đang tạo ra sự khác biệt."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Dưới đây là các torrent đã phát hành cho đến nay (chúng tôi vẫn đang xử lý phần còn lại):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Tất cả các torrent có thể được tìm thấy trên <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a> dưới mục “Datasets” (chúng tôi không liên kết trực tiếp ở đó, để các liên kết đến blog này không bị xóa khỏi Reddit, Twitter, v.v.). Từ đó, theo liên kết đến trang web Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Tiếp theo là gì?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Một loạt các torrent rất tốt cho việc bảo quản lâu dài, nhưng không nhiều cho việc truy cập hàng ngày. Chúng tôi sẽ làm việc với các đối tác lưu trữ để đưa tất cả dữ liệu này lên web (vì Lưu trữ của Anna không lưu trữ bất cứ thứ gì trực tiếp). Tất nhiên bạn sẽ có thể tìm thấy các liên kết tải xuống này trên Lưu trữ của Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Chúng tôi cũng mời mọi người làm gì đó với dữ liệu này! Giúp chúng tôi phân tích tốt hơn, loại bỏ trùng lặp, đưa nó lên IPFS, remix nó, huấn luyện các mô hình AI của bạn với nó, v.v. Tất cả đều là của bạn, và chúng tôi không thể chờ đợi để xem bạn làm gì với nó."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Cuối cùng, như đã nói trước đó, chúng tôi vẫn có một số phát hành lớn sắp tới (nếu <em>ai đó</em> có thể <em>vô tình</em> gửi cho chúng tôi một bản dump của một cơ sở dữ liệu <em>ACS4 nhất định</em>, bạn biết nơi để tìm chúng tôi…), cũng như xây dựng bánh đà để sao lưu tất cả các cuốn sách trên thế giới."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Vì vậy, hãy theo dõi, chúng tôi chỉ mới bắt đầu."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x sách mới được thêm vào Bản sao Thư viện Cướp biển (+24TB, 3,8 triệu cuốn sách)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Trong lần phát hành ban đầu của Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>), chúng tôi đã tạo một bản sao của Thư viện Z, một bộ sưu tập sách bất hợp pháp lớn. Như một lời nhắc nhở, đây là những gì chúng tôi đã viết trong bài đăng blog ban đầu đó:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Thư viện Z là một thư viện phổ biến (và bất hợp pháp). Họ đã lấy bộ sưu tập Library Genesis và làm cho nó dễ dàng tìm kiếm. Ngoài ra, họ đã trở nên rất hiệu quả trong việc kêu gọi đóng góp sách mới, bằng cách khuyến khích người dùng đóng góp với nhiều đặc quyền khác nhau. Hiện tại họ không đóng góp những cuốn sách mới này trở lại Library Genesis. Và không giống như Library Genesis, họ không làm cho bộ sưu tập của mình dễ dàng sao chép, điều này ngăn cản việc bảo quản rộng rãi. Điều này quan trọng đối với mô hình kinh doanh của họ, vì họ tính phí để truy cập bộ sưu tập của mình với số lượng lớn (hơn 10 cuốn sách mỗi ngày)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Chúng tôi không đưa ra phán xét đạo đức về việc thu phí truy cập số lượng lớn vào một bộ sưu tập sách bất hợp pháp. Không còn nghi ngờ gì nữa, Thư viện Z đã thành công trong việc mở rộng quyền truy cập vào tri thức và tìm nguồn sách nhiều hơn. Chúng tôi chỉ đơn giản ở đây để làm phần việc của mình: đảm bảo việc bảo tồn lâu dài bộ sưu tập cá nhân này."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Bộ sưu tập đó có từ giữa năm 2021. Trong thời gian đó, Thư viện Z đã phát triển với tốc độ đáng kinh ngạc: họ đã thêm khoảng 3,8 triệu cuốn sách mới. Chắc chắn có một số bản sao, nhưng phần lớn dường như là sách mới hợp pháp hoặc các bản quét chất lượng cao hơn của các cuốn sách đã được gửi trước đó. Điều này phần lớn là do số lượng người điều hành tình nguyện tại Thư viện Z tăng lên và hệ thống tải lên số lượng lớn của họ với tính năng loại bỏ trùng lặp. Chúng tôi muốn chúc mừng họ về những thành tựu này."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Chúng tôi vui mừng thông báo rằng chúng tôi đã có được tất cả các cuốn sách được thêm vào Thư viện Z giữa bản sao cuối cùng của chúng tôi và tháng 8 năm 2022. Chúng tôi cũng đã quay lại và thu thập một số cuốn sách mà chúng tôi đã bỏ lỡ lần đầu tiên. Tất cả, bộ sưu tập mới này khoảng 24TB, lớn hơn nhiều so với lần trước (7TB). Bản sao của chúng tôi hiện tổng cộng là 31TB. Một lần nữa, chúng tôi đã loại bỏ trùng lặp với Library Genesis, vì đã có các torrent có sẵn cho bộ sưu tập đó."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Vui lòng truy cập vào Pirate Library Mirror để xem bộ sưu tập mới (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>). Có thêm thông tin ở đó về cách các tệp được cấu trúc và những gì đã thay đổi kể từ lần trước. Chúng tôi sẽ không liên kết đến nó từ đây, vì đây chỉ là một trang blog không lưu trữ bất kỳ tài liệu bất hợp pháp nào."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Tất nhiên, việc seeding cũng là một cách tuyệt vời để giúp đỡ chúng tôi. Cảm ơn tất cả mọi người đã seeding bộ torrent trước của chúng tôi. Chúng tôi rất biết ơn phản hồi tích cực và vui mừng vì có rất nhiều người quan tâm đến việc bảo tồn tri thức và văn hóa theo cách đặc biệt này."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Cách trở thành một nhà lưu trữ cướp biển"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Thách thức đầu tiên có thể là một điều bất ngờ. Nó không phải là một vấn đề kỹ thuật, hay một vấn đề pháp lý. Đó là một vấn đề tâm lý."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Trước khi chúng tôi đi sâu vào, hai cập nhật về Pirate Library Mirror (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Chúng tôi đã nhận được một số khoản quyên góp vô cùng hào phóng. Đầu tiên là 10.000 đô la từ một cá nhân ẩn danh, người cũng đã hỗ trợ \"bookwarrior\", người sáng lập ban đầu của Library Genesis. Đặc biệt cảm ơn bookwarrior đã tạo điều kiện cho khoản quyên góp này. Khoản thứ hai là 10.000 đô la khác từ một nhà tài trợ ẩn danh, người đã liên lạc sau khi chúng tôi phát hành lần cuối và được truyền cảm hứng để giúp đỡ. Chúng tôi cũng đã nhận được một số khoản quyên góp nhỏ hơn. Cảm ơn rất nhiều vì tất cả sự hỗ trợ hào phóng của các bạn. Chúng tôi có một số dự án mới thú vị đang trong quá trình thực hiện mà điều này sẽ hỗ trợ, vì vậy hãy theo dõi."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Chúng tôi đã gặp một số khó khăn kỹ thuật với kích thước của lần phát hành thứ hai, nhưng các torrent của chúng tôi đã hoạt động và đang seeding. Chúng tôi cũng nhận được một đề nghị hào phóng từ một cá nhân ẩn danh để seeding bộ sưu tập của chúng tôi trên các máy chủ tốc độ rất cao của họ, vì vậy chúng tôi đang thực hiện một tải lên đặc biệt cho máy của họ, sau đó mọi người khác đang tải xuống bộ sưu tập sẽ thấy sự cải thiện lớn về tốc độ."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Toàn bộ sách có thể được viết về lý do tại sao của việc bảo tồn kỹ thuật số nói chung, và lưu trữ cướp biển nói riêng, nhưng hãy để chúng tôi cung cấp một hướng dẫn nhanh cho những người không quá quen thuộc. Thế giới đang sản xuất nhiều tri thức và văn hóa hơn bao giờ hết, nhưng cũng có nhiều thứ bị mất hơn bao giờ hết. Nhân loại phần lớn giao phó di sản này cho các tập đoàn như nhà xuất bản học thuật, dịch vụ phát trực tuyến và công ty truyền thông xã hội, và họ thường không chứng tỏ là những người quản lý tốt. Hãy xem bộ phim tài liệu Digital Amnesia, hoặc thực sự bất kỳ bài nói nào của Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Có một số tổ chức làm tốt việc lưu trữ càng nhiều càng tốt, nhưng họ bị ràng buộc bởi luật pháp. Là những cướp biển, chúng tôi ở vị trí độc nhất để lưu trữ các bộ sưu tập mà họ không thể chạm tới, vì thực thi bản quyền hoặc các hạn chế khác. Chúng tôi cũng có thể sao chép các bộ sưu tập nhiều lần, trên khắp thế giới, do đó tăng cơ hội bảo tồn đúng cách."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Hiện tại, chúng tôi sẽ không tham gia vào các cuộc thảo luận về ưu và nhược điểm của sở hữu trí tuệ, đạo đức của việc vi phạm pháp luật, suy nghĩ về kiểm duyệt, hoặc vấn đề truy cập tri thức và văn hóa. Với tất cả những điều đó đã được giải quyết, hãy đi sâu vào cách thức. Chúng tôi sẽ chia sẻ cách nhóm của chúng tôi trở thành những nhà lưu trữ cướp biển, và những bài học mà chúng tôi đã học được trên đường đi. Có nhiều thách thức khi bạn bắt đầu hành trình này, và hy vọng chúng tôi có thể giúp bạn vượt qua một số trong số đó."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Cộng đồng"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Thách thức đầu tiên có thể là một điều bất ngờ. Nó không phải là một vấn đề kỹ thuật, hay một vấn đề pháp lý. Đó là một vấn đề tâm lý: làm công việc này trong bóng tối có thể vô cùng cô đơn. Tùy thuộc vào những gì bạn dự định làm, và mô hình mối đe dọa của bạn, bạn có thể phải rất cẩn thận. Ở một đầu của phổ, chúng ta có những người như Alexandra Elbakyan*, người sáng lập Sci-Hub, người rất cởi mở về các hoạt động của mình. Nhưng cô ấy có nguy cơ cao bị bắt nếu cô ấy đến thăm một quốc gia phương Tây vào thời điểm này, và có thể đối mặt với hàng thập kỷ tù giam. Đó có phải là rủi ro bạn sẵn sàng chấp nhận không? Chúng tôi ở đầu kia của phổ; rất cẩn thận để không để lại bất kỳ dấu vết nào, và có an ninh hoạt động mạnh mẽ."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Như đã đề cập trên HN bởi \"ynno\", ban đầu Alexandra không muốn được biết đến: \"Máy chủ của cô ấy được thiết lập để phát ra các thông báo lỗi chi tiết từ PHP, bao gồm đường dẫn đầy đủ của tệp nguồn bị lỗi, nằm dưới thư mục /home/<USER>" Vì vậy, hãy sử dụng tên người dùng ngẫu nhiên trên các máy tính bạn sử dụng cho công việc này, trong trường hợp bạn cấu hình sai điều gì đó."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Tuy nhiên, sự bí mật đó đi kèm với một cái giá tâm lý. Hầu hết mọi người đều thích được công nhận cho công việc mà họ làm, nhưng bạn không thể nhận bất kỳ công lao nào cho điều này trong đời thực. Ngay cả những điều đơn giản cũng có thể trở nên thách thức, như bạn bè hỏi bạn đã làm gì (đến một lúc nào đó \"nghịch với NAS / homelab của tôi\" trở nên cũ kỹ)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Đây là lý do tại sao việc tìm kiếm một cộng đồng là rất quan trọng. Bạn có thể từ bỏ một số an ninh hoạt động bằng cách tâm sự với một số người bạn rất thân, những người mà bạn biết bạn có thể tin tưởng sâu sắc. Ngay cả khi đó, hãy cẩn thận không đặt bất cứ điều gì vào văn bản, trong trường hợp họ phải giao nộp email của họ cho các cơ quan chức năng, hoặc nếu thiết bị của họ bị xâm phạm theo cách nào đó."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Tốt hơn nữa là tìm một số đồng nghiệp cướp biển. Nếu bạn bè thân của bạn quan tâm đến việc tham gia cùng bạn, tuyệt vời! Nếu không, bạn có thể tìm thấy những người khác trực tuyến. Đáng buồn thay, đây vẫn là một cộng đồng ngách. Cho đến nay, chúng tôi chỉ tìm thấy một số ít người khác đang hoạt động trong lĩnh vực này. Những nơi bắt đầu tốt dường như là các diễn đàn Library Genesis và r/DataHoarder. Đội Lưu trữ cũng có những cá nhân cùng chí hướng, mặc dù họ hoạt động trong khuôn khổ pháp luật (ngay cả khi trong một số khu vực xám của pháp luật). Các cảnh \"warez\" và cướp biển truyền thống cũng có những người suy nghĩ theo cách tương tự."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Chúng tôi luôn sẵn sàng đón nhận ý tưởng về cách thúc đẩy cộng đồng và khám phá ý tưởng. Hãy thoải mái nhắn tin cho chúng tôi trên Twitter hoặc Reddit. Có lẽ chúng tôi có thể tổ chức một diễn đàn hoặc nhóm trò chuyện nào đó. Một thách thức là điều này có thể dễ dàng bị kiểm duyệt khi sử dụng các nền tảng phổ biến, vì vậy chúng tôi sẽ phải tự mình tổ chức. Cũng có sự đánh đổi giữa việc có những cuộc thảo luận này hoàn toàn công khai (có nhiều khả năng tham gia hơn) so với việc làm cho nó riêng tư (không để các \"mục tiêu\" tiềm năng biết rằng chúng tôi sắp thu thập dữ liệu của họ). Chúng tôi sẽ phải suy nghĩ về điều đó. Hãy cho chúng tôi biết nếu bạn quan tâm đến điều này!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Dự án"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Khi chúng tôi thực hiện một dự án, nó có một vài giai đoạn:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Lựa chọn miền / triết lý: Bạn muốn tập trung vào đâu và tại sao? Đam mê, kỹ năng và hoàn cảnh độc đáo của bạn là gì mà bạn có thể sử dụng để mang lại lợi ích cho mình?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Lựa chọn mục tiêu: Bạn sẽ sao chép bộ sưu tập cụ thể nào?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Thu thập metadata: Lập danh mục thông tin về các tệp, mà không thực sự tải xuống các tệp (thường lớn hơn nhiều) đó."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Lựa chọn dữ liệu: Dựa trên metadata, thu hẹp dữ liệu nào là quan trọng nhất để lưu trữ ngay bây giờ. Có thể là tất cả, nhưng thường có một cách hợp lý để tiết kiệm không gian và băng thông."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Thu thập dữ liệu: Thực sự lấy dữ liệu."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Phân phối: Đóng gói nó trong các torrent, thông báo ở đâu đó, khiến mọi người lan truyền nó."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Đây không phải là các giai đoạn hoàn toàn độc lập, và thường những hiểu biết từ một giai đoạn sau sẽ đưa bạn quay lại một giai đoạn trước đó. Ví dụ, trong quá trình thu thập metadata, bạn có thể nhận ra rằng mục tiêu mà bạn đã chọn có các cơ chế phòng thủ vượt quá trình độ kỹ năng của bạn (như chặn IP), vì vậy bạn quay lại và tìm một mục tiêu khác."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Lựa chọn miền / triết lý"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Không thiếu kiến thức và di sản văn hóa cần được lưu giữ, điều này có thể gây choáng ngợp. Đó là lý do tại sao thường hữu ích khi dành một chút thời gian để suy nghĩ về những gì bạn có thể đóng góp."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Mỗi người có một cách suy nghĩ khác nhau về điều này, nhưng đây là một số câu hỏi mà bạn có thể tự hỏi mình:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Tại sao bạn quan tâm đến điều này? Bạn đam mê điều gì? Nếu chúng ta có thể tập hợp một nhóm người mà tất cả đều lưu trữ những thứ mà họ đặc biệt quan tâm, điều đó sẽ bao phủ rất nhiều! Bạn sẽ biết nhiều hơn người bình thường về niềm đam mê của mình, như dữ liệu quan trọng nào cần lưu, bộ sưu tập và cộng đồng trực tuyến nào là tốt nhất, v.v."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Bạn có những kỹ năng nào mà bạn có thể sử dụng để mang lại lợi ích cho mình? Ví dụ, nếu bạn là chuyên gia bảo mật trực tuyến, bạn có thể tìm cách vượt qua các chặn IP cho các mục tiêu an toàn. Nếu bạn giỏi tổ chức cộng đồng, thì có lẽ bạn có thể tập hợp một số người xung quanh một mục tiêu. Tuy nhiên, biết một chút lập trình cũng hữu ích, ít nhất là để duy trì bảo mật hoạt động tốt trong suốt quá trình này."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Bạn có bao nhiêu thời gian cho việc này? Lời khuyên của chúng tôi là bắt đầu từ những dự án nhỏ và thực hiện các dự án lớn hơn khi bạn đã quen với nó, nhưng nó có thể chiếm hết thời gian của bạn."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Khu vực nào có đòn bẩy cao để tập trung vào? Nếu bạn sẽ dành X giờ cho việc lưu trữ lậu, thì làm thế nào bạn có thể đạt được \"hiệu quả cao nhất\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Bạn có những cách suy nghĩ độc đáo nào về điều này? Bạn có thể có một số ý tưởng hoặc cách tiếp cận thú vị mà người khác có thể đã bỏ lỡ."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Trong trường hợp của chúng tôi, chúng tôi đặc biệt quan tâm đến việc bảo tồn lâu dài khoa học. Chúng tôi biết về Library Genesis, và cách nó được sao chép hoàn toàn nhiều lần bằng cách sử dụng torrent. Chúng tôi yêu thích ý tưởng đó. Rồi một ngày, một trong số chúng tôi cố gắng tìm một số sách giáo khoa khoa học trên Library Genesis, nhưng không thể tìm thấy chúng, khiến chúng tôi nghi ngờ về mức độ hoàn chỉnh của nó. Sau đó, chúng tôi tìm kiếm những sách giáo khoa đó trực tuyến và tìm thấy chúng ở những nơi khác, điều này đã gieo mầm cho dự án của chúng tôi. Ngay cả trước khi chúng tôi biết về Thư viện Z, chúng tôi đã có ý tưởng không cố gắng thu thập tất cả những cuốn sách đó một cách thủ công, mà tập trung vào việc sao chép các bộ sưu tập hiện có và đóng góp chúng trở lại Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Lựa chọn mục tiêu"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Vậy, chúng ta đã có khu vực mà chúng ta đang xem xét, bây giờ chúng ta sẽ sao chép bộ sưu tập cụ thể nào? Có một vài yếu tố làm cho một mục tiêu trở nên tốt:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Lớn"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Độc đáo: không đã được bao phủ tốt bởi các dự án khác."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Dễ tiếp cận: không sử dụng nhiều lớp bảo vệ để ngăn bạn trích xuất metadata và dữ liệu của họ."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Thông tin đặc biệt: bạn có một số thông tin đặc biệt về mục tiêu này, như bạn có quyền truy cập đặc biệt vào bộ sưu tập này, hoặc bạn đã tìm ra cách vượt qua các biện pháp bảo vệ của họ. Điều này không bắt buộc (dự án sắp tới của chúng tôi không làm gì đặc biệt), nhưng chắc chắn sẽ hữu ích!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Khi chúng tôi tìm thấy sách giáo khoa khoa học của mình trên các trang web khác ngoài Library Genesis, chúng tôi đã cố gắng tìm hiểu cách chúng xuất hiện trên internet. Sau đó, chúng tôi tìm thấy Thư viện Z, và nhận ra rằng mặc dù hầu hết các cuốn sách không xuất hiện đầu tiên ở đó, nhưng cuối cùng chúng cũng có mặt ở đó. Chúng tôi đã tìm hiểu về mối quan hệ của nó với Library Genesis, và cấu trúc khuyến khích (tài chính) và giao diện người dùng vượt trội, cả hai đều làm cho nó trở thành một bộ sưu tập hoàn chỉnh hơn nhiều. Sau đó, chúng tôi đã thực hiện một số trích xuất metadata và dữ liệu sơ bộ, và nhận ra rằng chúng tôi có thể vượt qua giới hạn tải xuống IP của họ, tận dụng quyền truy cập đặc biệt của một trong các thành viên của chúng tôi vào nhiều máy chủ proxy."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Khi bạn đang khám phá các mục tiêu khác nhau, điều quan trọng là phải che giấu dấu vết của bạn bằng cách sử dụng VPN và địa chỉ email tạm thời, điều mà chúng tôi sẽ nói thêm sau."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Trích xuất metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Hãy đi sâu hơn một chút về mặt kỹ thuật ở đây. Để thực sự trích xuất metadata từ các trang web, chúng tôi đã giữ mọi thứ khá đơn giản. Chúng tôi sử dụng các script Python, đôi khi là curl, và một cơ sở dữ liệu MySQL để lưu trữ kết quả. Chúng tôi không sử dụng bất kỳ phần mềm trích xuất phức tạp nào có thể lập bản đồ các trang web phức tạp, vì cho đến nay chúng tôi chỉ cần trích xuất một hoặc hai loại trang bằng cách chỉ định qua các id và phân tích cú pháp HTML. Nếu không có các trang dễ dàng chỉ định, thì bạn có thể cần một trình thu thập thông tin thực sự để tìm tất cả các trang."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Trước khi bạn bắt đầu trích xuất toàn bộ một trang web, hãy thử làm điều đó thủ công một chút. Tự mình đi qua vài chục trang, để có cảm giác về cách hoạt động của nó. Đôi khi bạn sẽ gặp phải các chặn IP hoặc hành vi thú vị khác theo cách này. Điều tương tự cũng áp dụng cho việc trích xuất dữ liệu: trước khi đi quá sâu vào mục tiêu này, hãy chắc chắn rằng bạn có thể thực sự tải xuống dữ liệu của nó một cách hiệu quả."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Để vượt qua các hạn chế, có một vài điều bạn có thể thử. Có địa chỉ IP hoặc máy chủ nào khác lưu trữ cùng dữ liệu nhưng không có cùng hạn chế không? Có điểm cuối API nào không có hạn chế, trong khi những điểm khác có không? Tốc độ tải xuống nào khiến IP của bạn bị chặn, và trong bao lâu? Hoặc bạn không bị chặn mà bị giảm tốc độ? Nếu bạn tạo một tài khoản người dùng, mọi thứ thay đổi như thế nào? Bạn có thể sử dụng HTTP/2 để giữ kết nối mở, và điều đó có tăng tốc độ yêu cầu trang không? Có trang nào liệt kê nhiều tệp cùng lúc, và thông tin được liệt kê ở đó có đủ không?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Những thứ bạn có thể muốn lưu bao gồm:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Tiêu đề"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Tên tệp / vị trí"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: có thể là một ID nội bộ, nhưng các ID như ISBN hoặc DOI cũng hữu ích."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Kích thước: để tính toán dung lượng đĩa bạn cần."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): để xác nhận rằng bạn đã tải xuống tệp đúng cách."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Ngày thêm/sửa đổi: để bạn có thể quay lại sau và tải xuống các tệp mà bạn chưa tải xuống trước đó (mặc dù bạn cũng có thể sử dụng ID hoặc hash cho việc này)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Mô tả, danh mục, thẻ, tác giả, ngôn ngữ, v.v."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Chúng tôi thường thực hiện điều này trong hai giai đoạn. Đầu tiên, chúng tôi tải xuống các tệp HTML thô, thường là trực tiếp vào MySQL (để tránh nhiều tệp nhỏ, mà chúng tôi sẽ nói thêm bên dưới). Sau đó, trong một bước riêng biệt, chúng tôi đi qua các tệp HTML đó và phân tích chúng thành các bảng MySQL thực tế. Bằng cách này, bạn không phải tải xuống lại mọi thứ từ đầu nếu bạn phát hiện ra lỗi trong mã phân tích của mình, vì bạn chỉ cần xử lý lại các tệp HTML với mã mới. Nó cũng thường dễ dàng hơn để song song hóa bước xử lý, do đó tiết kiệm một số thời gian (và bạn có thể viết mã xử lý trong khi trích xuất đang chạy, thay vì phải viết cả hai bước cùng một lúc)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Cuối cùng, lưu ý rằng đối với một số mục tiêu, trích xuất metadata là tất cả những gì có. Có một số bộ sưu tập metadata khổng lồ ngoài kia không được bảo tồn đúng cách."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Lựa chọn dữ liệu"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Thường thì bạn có thể sử dụng metadata để tìm ra một tập hợp con hợp lý của dữ liệu để tải xuống. Ngay cả khi bạn cuối cùng muốn tải xuống tất cả dữ liệu, việc ưu tiên các mục quan trọng nhất trước có thể hữu ích, trong trường hợp bạn bị phát hiện và các biện pháp phòng thủ được cải thiện, hoặc vì bạn cần mua thêm đĩa, hoặc đơn giản là vì có điều gì khác xảy ra trong cuộc sống của bạn trước khi bạn có thể tải xuống mọi thứ."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Ví dụ, một bộ sưu tập có thể có nhiều phiên bản của cùng một tài nguyên cơ bản (như một cuốn sách hoặc một bộ phim), trong đó một phiên bản được đánh dấu là chất lượng tốt nhất. Lưu các phiên bản đó trước sẽ rất hợp lý. Bạn có thể cuối cùng muốn lưu tất cả các phiên bản, vì trong một số trường hợp metadata có thể được gắn thẻ không chính xác, hoặc có thể có những sự đánh đổi không rõ giữa các phiên bản (ví dụ, \"phiên bản tốt nhất\" có thể tốt nhất theo hầu hết các cách nhưng tệ hơn theo các cách khác, như một bộ phim có độ phân giải cao hơn nhưng thiếu phụ đề)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Bạn cũng có thể tìm kiếm cơ sở dữ liệu metadata của mình để tìm những điều thú vị. Tệp lớn nhất được lưu trữ là gì, và tại sao nó lại lớn như vậy? Tệp nhỏ nhất là gì? Có những mẫu thú vị hoặc bất ngờ nào khi nói đến các danh mục, ngôn ngữ nhất định, v.v.? Có tiêu đề trùng lặp hoặc rất giống nhau không? Có mẫu nào về thời điểm dữ liệu được thêm vào, như một ngày mà nhiều tệp được thêm vào cùng lúc không? Bạn thường có thể học được nhiều điều bằng cách nhìn vào tập dữ liệu theo những cách khác nhau."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Trong trường hợp của chúng tôi, chúng tôi đã loại bỏ các cuốn sách của Thư viện Z dựa trên các hash md5 trong Library Genesis, do đó tiết kiệm được rất nhiều thời gian tải xuống và dung lượng đĩa. Đây là một tình huống khá độc đáo. Trong hầu hết các trường hợp, không có cơ sở dữ liệu toàn diện nào về các tệp đã được bảo tồn đúng cách bởi các đồng nghiệp. Điều này tự nó là một cơ hội lớn cho ai đó ngoài kia. Sẽ rất tuyệt vời nếu có một cái nhìn tổng quan được cập nhật thường xuyên về những thứ như âm nhạc và phim đã được gieo rộng rãi trên các trang web torrent, và do đó có mức độ ưu tiên thấp hơn để bao gồm trong các bản sao của hải tặc."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Trích xuất dữ liệu"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Bây giờ bạn đã sẵn sàng để thực sự tải xuống dữ liệu hàng loạt. Như đã đề cập trước đó, tại thời điểm này bạn nên đã tải xuống thủ công một loạt tệp, để hiểu rõ hơn về hành vi và hạn chế của mục tiêu. Tuy nhiên, vẫn sẽ có những bất ngờ đang chờ đợi bạn khi bạn thực sự bắt đầu tải xuống nhiều tệp cùng một lúc."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Lời khuyên của chúng tôi ở đây chủ yếu là giữ cho nó đơn giản. Bắt đầu bằng cách chỉ tải xuống một loạt tệp. Bạn có thể sử dụng Python, và sau đó mở rộng ra nhiều luồng. Nhưng đôi khi thậm chí đơn giản hơn là tạo các tệp Bash trực tiếp từ cơ sở dữ liệu, và sau đó chạy nhiều tệp trong nhiều cửa sổ terminal để mở rộng quy mô. Một mẹo kỹ thuật nhanh đáng nhắc đến ở đây là sử dụng OUTFILE trong MySQL, mà bạn có thể viết ở bất kỳ đâu nếu bạn vô hiệu hóa \"secure_file_priv\" trong mysqld.cnf (và hãy chắc chắn cũng vô hiệu hóa/ghi đè AppArmor nếu bạn đang sử dụng Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Chúng tôi lưu trữ dữ liệu trên các ổ cứng đơn giản. Bắt đầu với bất kỳ thứ gì bạn có, và mở rộng dần dần. Có thể cảm thấy choáng ngợp khi nghĩ về việc lưu trữ hàng trăm TB dữ liệu. Nếu đó là tình huống bạn đang đối mặt, chỉ cần đưa ra một tập hợp con tốt trước, và trong thông báo của bạn yêu cầu sự giúp đỡ trong việc lưu trữ phần còn lại. Nếu bạn muốn tự mình có thêm ổ cứng, thì r/DataHoarder có một số tài nguyên tốt để có được các giao dịch tốt."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Cố gắng không lo lắng quá nhiều về các hệ thống tệp phức tạp. Rất dễ rơi vào hố thỏ của việc thiết lập những thứ như ZFS. Một chi tiết kỹ thuật cần lưu ý là nhiều hệ thống tệp không xử lý tốt với nhiều tệp. Chúng tôi đã tìm thấy một giải pháp đơn giản là tạo nhiều thư mục, ví dụ cho các dải ID khác nhau hoặc tiền tố hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Sau khi tải dữ liệu, hãy chắc chắn kiểm tra tính toàn vẹn của các tệp bằng cách sử dụng các hàm băm trong metadata, nếu có sẵn."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Phân phối"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Bạn đã có dữ liệu, do đó bạn đang sở hữu bản sao cướp biển đầu tiên trên thế giới của mục tiêu của mình (có khả năng nhất). Theo nhiều cách, phần khó nhất đã qua, nhưng phần rủi ro nhất vẫn còn ở phía trước. Dù sao, cho đến nay bạn đã hoạt động bí mật; bay dưới radar. Tất cả những gì bạn cần làm là sử dụng một VPN tốt suốt quá trình, không điền thông tin cá nhân của bạn vào bất kỳ biểu mẫu nào (dĩ nhiên), và có thể sử dụng một phiên trình duyệt đặc biệt (hoặc thậm chí một máy tính khác)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Bây giờ bạn phải phân phối dữ liệu. Trong trường hợp của chúng tôi, chúng tôi đầu tiên muốn đóng góp sách trở lại Library Genesis, nhưng sau đó nhanh chóng phát hiện ra những khó khăn trong việc đó (phân loại tiểu thuyết so với phi tiểu thuyết). Vì vậy, chúng tôi quyết định phân phối bằng cách sử dụng các torrent theo kiểu Library Genesis. Nếu bạn có cơ hội đóng góp cho một dự án hiện có, thì điều đó có thể tiết kiệm cho bạn rất nhiều thời gian. Tuy nhiên, hiện tại không có nhiều bản sao cướp biển được tổ chức tốt."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Vì vậy, giả sử bạn quyết định tự phân phối các torrent. Hãy cố gắng giữ cho các tệp đó nhỏ, để chúng dễ dàng được sao chép trên các trang web khác. Sau đó, bạn sẽ phải seed các torrent đó, trong khi vẫn giữ ẩn danh. Bạn có thể sử dụng VPN (có hoặc không có chuyển tiếp cổng), hoặc thanh toán bằng Bitcoins đã được trộn lẫn cho một Seedbox. Nếu bạn không biết một số thuật ngữ đó có nghĩa là gì, bạn sẽ có một loạt việc đọc cần làm, vì điều quan trọng là bạn phải hiểu các đánh đổi rủi ro ở đây."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Bạn có thể lưu trữ các tệp torrent trên các trang web torrent hiện có. Trong trường hợp của chúng tôi, chúng tôi đã chọn thực sự lưu trữ một trang web, vì chúng tôi cũng muốn truyền bá triết lý của mình một cách rõ ràng. Bạn có thể tự làm điều này theo cách tương tự (chúng tôi sử dụng Njalla cho các tên miền và lưu trữ của mình, được thanh toán bằng Bitcoins đã được trộn lẫn), nhưng cũng đừng ngần ngại liên hệ với chúng tôi để chúng tôi lưu trữ các torrent của bạn. Chúng tôi đang tìm cách xây dựng một chỉ mục toàn diện về các bản sao cướp biển theo thời gian, nếu ý tưởng này được đón nhận."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Về việc chọn VPN, đã có rất nhiều bài viết về vấn đề này, vì vậy chúng tôi chỉ nhắc lại lời khuyên chung là chọn theo uy tín. Chính sách không lưu nhật ký đã được kiểm tra tại tòa án với lịch sử lâu dài bảo vệ quyền riêng tư là lựa chọn ít rủi ro nhất, theo ý kiến của chúng tôi. Lưu ý rằng ngay cả khi bạn làm mọi thứ đúng, bạn không bao giờ có thể đạt đến mức rủi ro bằng không. Ví dụ, khi bạn đang seeding torrent của mình, một tác nhân quốc gia có động cơ cao có thể nhìn vào luồng dữ liệu đến và đi cho các máy chủ VPN, và suy ra bạn là ai. Hoặc bạn có thể chỉ đơn giản là mắc lỗi nào đó. Chúng tôi có lẽ đã mắc lỗi, và sẽ lại mắc lỗi. May mắn thay, các quốc gia không quan tâm <em>nhiều</em> đến vi phạm bản quyền."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Một quyết định cần đưa ra cho mỗi dự án là liệu có nên xuất bản nó dưới cùng một danh tính như trước hay không. Nếu bạn tiếp tục sử dụng cùng một tên, thì những sai lầm trong bảo mật hoạt động từ các dự án trước có thể quay lại gây rắc rối cho bạn. Nhưng xuất bản dưới các tên khác nhau có nghĩa là bạn không xây dựng được danh tiếng lâu dài. Chúng tôi đã chọn có bảo mật hoạt động mạnh mẽ từ đầu để có thể tiếp tục sử dụng cùng một danh tính, nhưng chúng tôi sẽ không ngần ngại xuất bản dưới một tên khác nếu chúng tôi mắc lỗi hoặc nếu hoàn cảnh yêu cầu."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Truyền tải thông tin có thể khó khăn. Như chúng tôi đã nói, đây vẫn là một cộng đồng ngách. Ban đầu chúng tôi đã đăng trên Reddit, nhưng thực sự đã thu hút sự chú ý trên Hacker News. Hiện tại, khuyến nghị của chúng tôi là đăng nó ở một vài nơi và xem điều gì xảy ra. Và một lần nữa, hãy liên hệ với chúng tôi. Chúng tôi rất muốn lan truyền thông điệp về những nỗ lực lưu trữ cướp biển."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Kết luận"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hy vọng điều này hữu ích cho những nhà lưu trữ cướp biển mới bắt đầu. Chúng tôi rất vui mừng chào đón bạn đến với thế giới này, vì vậy đừng ngần ngại liên hệ. Hãy cùng nhau bảo tồn càng nhiều kiến thức và văn hóa của thế giới càng tốt, và sao chép nó rộng rãi."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Giới thiệu Bản sao Thư viện Cướp biển: Bảo tồn 7TB sách (không có trong Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Dự án này (CHỈNH SỬA: chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>) nhằm đóng góp vào việc bảo tồn và giải phóng kiến thức của con người. Chúng tôi thực hiện đóng góp nhỏ bé và khiêm tốn của mình, theo bước chân của những người vĩ đại trước chúng tôi."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Trọng tâm của dự án này được minh họa qua tên gọi của nó:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Cướp biển</strong> - Chúng tôi cố ý vi phạm luật bản quyền ở hầu hết các quốc gia. Điều này cho phép chúng tôi làm điều mà các thực thể hợp pháp không thể làm: đảm bảo sách được sao chép rộng rãi."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Thư viện</strong> - Giống như hầu hết các thư viện, chúng tôi tập trung chủ yếu vào tài liệu viết như sách. Chúng tôi có thể mở rộng sang các loại phương tiện khác trong tương lai."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Bản sao</strong> - Chúng tôi chỉ là một bản sao của các thư viện hiện có. Chúng tôi tập trung vào việc bảo tồn, không phải làm cho sách dễ dàng tìm kiếm và tải xuống (truy cập) hoặc phát triển một cộng đồng lớn những người đóng góp sách mới (nguồn cung cấp)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Thư viện đầu tiên mà chúng tôi đã sao chép là Thư viện Z. Đây là một thư viện phổ biến (và bất hợp pháp). Họ đã lấy bộ sưu tập Library Genesis và làm cho nó dễ dàng tìm kiếm. Ngoài ra, họ đã trở nên rất hiệu quả trong việc kêu gọi đóng góp sách mới, bằng cách khuyến khích người dùng đóng góp với nhiều đặc quyền khác nhau. Hiện tại, họ không đóng góp những cuốn sách mới này trở lại Library Genesis. Và không giống như Library Genesis, họ không làm cho bộ sưu tập của mình dễ dàng sao chép, điều này ngăn cản việc bảo tồn rộng rãi. Điều này quan trọng đối với mô hình kinh doanh của họ, vì họ tính phí để truy cập bộ sưu tập của mình hàng loạt (hơn 10 cuốn sách mỗi ngày)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Chúng tôi không đưa ra phán xét đạo đức về việc thu phí truy cập số lượng lớn vào một bộ sưu tập sách bất hợp pháp. Không còn nghi ngờ gì nữa, Thư viện Z đã thành công trong việc mở rộng quyền truy cập vào tri thức và tìm nguồn sách nhiều hơn. Chúng tôi chỉ đơn giản ở đây để làm phần việc của mình: đảm bảo việc bảo tồn lâu dài bộ sưu tập cá nhân này."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Chúng tôi muốn mời bạn giúp bảo tồn và giải phóng kiến thức của con người bằng cách tải xuống và chia sẻ các torrent của chúng tôi. Xem trang dự án để biết thêm thông tin về cách dữ liệu được tổ chức."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Chúng tôi cũng rất muốn mời bạn đóng góp ý tưởng của mình về những bộ sưu tập nào nên sao chép tiếp theo và cách thực hiện điều đó. Cùng nhau, chúng ta có thể đạt được nhiều điều. Đây chỉ là một đóng góp nhỏ trong số vô số đóng góp khác. Cảm ơn bạn, vì tất cả những gì bạn làm."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Chúng tôi không liên kết đến các tệp từ blog này. Vui lòng tự tìm kiếm.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Dữ liệu ISBNdb, hoặc Có Bao Nhiêu Cuốn Sách Được Bảo Tồn Mãi Mãi?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Nếu chúng ta thực hiện việc loại bỏ trùng lặp các tệp từ các thư viện bóng tối một cách đúng đắn, chúng ta đã bảo tồn được bao nhiêu phần trăm tổng số sách trên thế giới?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Với Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>), mục tiêu của chúng tôi là lấy tất cả các cuốn sách trên thế giới và bảo tồn chúng mãi mãi.<sup>1</sup> Giữa các torrent của Thư viện Z và các torrent gốc của Library Genesis, chúng tôi có 11.783.153 tệp. Nhưng thực sự thì con số đó là bao nhiêu? Nếu chúng ta loại bỏ trùng lặp các tệp đó một cách đúng đắn, chúng ta đã bảo tồn được bao nhiêu phần trăm tổng số sách trên thế giới? Chúng tôi thực sự muốn có một cái gì đó như thế này:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of di sản viết của nhân loại được bảo tồn mãi mãi"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Để có một tỷ lệ phần trăm, chúng ta cần một mẫu số: tổng số sách từng được xuất bản.<sup>2</sup> Trước khi Google Books ngừng hoạt động, một kỹ sư trong dự án, Leonid Taycher, <a %(booksearch_blogspot)s>đã cố gắng ước tính</a> con số này. Ông đã đưa ra — một cách hài hước — con số 129.864.880 (“ít nhất cho đến Chủ nhật”). Ông ước tính con số này bằng cách xây dựng một cơ sở dữ liệu thống nhất của tất cả các cuốn sách trên thế giới. Để làm điều này, ông đã thu thập các Datasets khác nhau và sau đó hợp nhất chúng theo nhiều cách khác nhau."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Như một lưu ý nhanh, có một người khác đã cố gắng lập danh mục tất cả các cuốn sách trên thế giới: Aaron Swartz, nhà hoạt động kỹ thuật số quá cố và đồng sáng lập Reddit.<sup>3</sup> Ông đã <a %(youtube)s>bắt đầu Open Library</a> với mục tiêu “một trang web cho mỗi cuốn sách từng được xuất bản”, kết hợp dữ liệu từ nhiều nguồn khác nhau. Ông đã phải trả giá đắt nhất cho công việc bảo tồn kỹ thuật số của mình khi bị truy tố vì tải xuống hàng loạt các bài báo học thuật, dẫn đến việc tự tử của ông. Không cần phải nói, đây là một trong những lý do nhóm của chúng tôi sử dụng bút danh, và tại sao chúng tôi rất cẩn thận. Open Library vẫn đang được điều hành một cách anh hùng bởi những người tại Internet Archive, tiếp tục di sản của Aaron. Chúng tôi sẽ quay lại vấn đề này sau trong bài viết này."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Trong bài viết trên blog của Google, Taycher mô tả một số thách thức với việc ước tính con số này. Trước tiên, một cuốn sách được định nghĩa như thế nào? Có một vài định nghĩa có thể có:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Bản sao vật lý.</strong> Rõ ràng điều này không hữu ích lắm, vì chúng chỉ là bản sao của cùng một tài liệu. Sẽ thật tuyệt nếu chúng ta có thể bảo tồn tất cả các chú thích mà mọi người thực hiện trong sách, như những \"chữ viết tay bên lề\" nổi tiếng của Fermat. Nhưng tiếc thay, đó sẽ vẫn là giấc mơ của một nhà lưu trữ."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Tác phẩm”.</strong> Ví dụ, “Harry Potter và Phòng chứa Bí mật” như một khái niệm logic, bao gồm tất cả các phiên bản của nó, như các bản dịch và tái bản khác nhau. Đây là một định nghĩa khá hữu ích, nhưng có thể khó để vạch ra ranh giới của những gì được tính. Ví dụ, chúng ta có thể muốn bảo tồn các bản dịch khác nhau, mặc dù các bản tái bản chỉ có những khác biệt nhỏ có thể không quan trọng lắm."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Ấn bản”.</strong> Ở đây bạn đếm mỗi phiên bản độc đáo của một cuốn sách. Nếu có bất kỳ điều gì khác biệt, như bìa khác hoặc lời nói đầu khác, nó được tính là một ấn bản khác."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Tệp.</strong> Khi làm việc với các thư viện bóng tối như Library Genesis, Sci-Hub, hoặc Thư viện Z, có một cân nhắc bổ sung. Có thể có nhiều bản quét của cùng một ấn bản. Và mọi người có thể tạo ra các phiên bản tốt hơn của các tệp hiện có, bằng cách quét văn bản bằng OCR, hoặc chỉnh sửa các trang đã được quét ở một góc. Chúng tôi chỉ muốn đếm các tệp này là một ấn bản, điều này sẽ yêu cầu metadata tốt, hoặc loại bỏ trùng lặp bằng cách đo lường sự tương đồng của tài liệu."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Ấn bản” dường như là định nghĩa thực tế nhất về “sách” là gì. Thuận tiện, định nghĩa này cũng được sử dụng để gán số ISBN duy nhất. ISBN, hay Số Sách Tiêu Chuẩn Quốc Tế, thường được sử dụng cho thương mại quốc tế, vì nó được tích hợp với hệ thống mã vạch quốc tế (”Số Bài Viết Quốc Tế”). Nếu bạn muốn bán một cuốn sách trong các cửa hàng, nó cần có mã vạch, vì vậy bạn nhận được một ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Bài viết trên blog của Taycher đề cập rằng mặc dù ISBN hữu ích, chúng không phải là phổ quát, vì chúng chỉ thực sự được chấp nhận vào giữa những năm bảy mươi, và không phải ở khắp nơi trên thế giới. Tuy nhiên, ISBN có lẽ là định danh được sử dụng rộng rãi nhất cho các ấn bản sách, vì vậy đó là điểm khởi đầu tốt nhất của chúng tôi. Nếu chúng tôi có thể tìm thấy tất cả các ISBN trên thế giới, chúng tôi sẽ có một danh sách hữu ích về những cuốn sách nào vẫn cần được bảo tồn."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Vậy, chúng ta lấy dữ liệu từ đâu? Có một số nỗ lực hiện có đang cố gắng biên soạn danh sách tất cả các cuốn sách trên thế giới:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Sau tất cả, họ đã thực hiện nghiên cứu này cho Google Books. Tuy nhiên, metadata của họ không thể truy cập được hàng loạt và khá khó để trích xuất."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Như đã đề cập trước đó, đây là toàn bộ sứ mệnh của họ. Họ đã thu thập lượng lớn dữ liệu thư viện từ các thư viện hợp tác và các kho lưu trữ quốc gia, và tiếp tục làm như vậy. Họ cũng có các thủ thư tình nguyện và một đội ngũ kỹ thuật đang cố gắng loại bỏ trùng lặp các bản ghi và gắn thẻ chúng với tất cả các loại metadata. Tốt nhất là, dataset của họ hoàn toàn mở. Bạn có thể đơn giản <a %(openlibrary)s>tải xuống</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Đây là một trang web do tổ chức phi lợi nhuận OCLC điều hành, bán các hệ thống quản lý thư viện. Họ tổng hợp metadata sách từ nhiều thư viện và cung cấp nó thông qua trang web WorldCat. Tuy nhiên, họ cũng kiếm tiền bằng cách bán dữ liệu này, vì vậy nó không có sẵn để tải xuống hàng loạt. Họ có một số Datasets hàng loạt hạn chế hơn có sẵn để tải xuống, hợp tác với các thư viện cụ thể."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Đây là chủ đề của bài viết blog này. ISBNdb trích xuất dữ liệu từ các trang web khác nhau để lấy metadata sách, đặc biệt là dữ liệu giá cả, mà họ sau đó bán cho các nhà bán sách, để họ có thể định giá sách của mình phù hợp với phần còn lại của thị trường. Vì ISBN hiện nay khá phổ biến, họ đã xây dựng hiệu quả một “trang web cho mỗi cuốn sách”."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Các hệ thống thư viện và kho lưu trữ cá nhân khác nhau.</strong> Có những thư viện và kho lưu trữ chưa được lập chỉ mục và tổng hợp bởi bất kỳ ai trong số những người trên, thường là vì họ thiếu kinh phí, hoặc vì lý do khác không muốn chia sẻ dữ liệu của họ với Open Library, OCLC, Google, v.v. Nhiều trong số này có hồ sơ kỹ thuật số có thể truy cập qua internet, và chúng thường không được bảo vệ tốt, vì vậy nếu bạn muốn giúp đỡ và có một chút vui vẻ khi tìm hiểu về các hệ thống thư viện kỳ lạ, đây là những điểm khởi đầu tuyệt vời."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Trong bài viết này, chúng tôi vui mừng thông báo một bản phát hành nhỏ (so với các bản phát hành Thư viện Z trước đây của chúng tôi). Chúng tôi đã trích xuất hầu hết ISBNdb và làm cho dữ liệu có sẵn để tải torrent trên trang web của Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>; chúng tôi sẽ không liên kết trực tiếp ở đây, chỉ cần tìm kiếm nó). Đây là khoảng 30,9 triệu bản ghi (20GB dưới dạng <a %(jsonlines)s>JSON Lines</a>; 4,4GB nén gzip). Trên trang web của họ, họ tuyên bố rằng họ thực sự có 32,6 triệu bản ghi, vì vậy chúng tôi có thể đã bỏ lỡ một số, hoặc <em>họ</em> có thể đã làm sai điều gì đó. Dù sao đi nữa, hiện tại chúng tôi sẽ không chia sẻ chính xác cách chúng tôi đã làm điều đó — chúng tôi sẽ để lại điều đó như một bài tập cho người đọc. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Những gì chúng tôi sẽ chia sẻ là một số phân tích sơ bộ, để cố gắng tiến gần hơn đến việc ước tính số lượng sách trên thế giới. Chúng tôi đã xem xét ba datasets: dataset ISBNdb mới này, bản phát hành metadata gốc của chúng tôi mà chúng tôi đã thu thập từ thư viện bóng tối Thư viện Z (bao gồm cả Library Genesis), và dữ liệu dump của Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Hãy bắt đầu với một số con số sơ bộ:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Trong cả Thư viện Z/Libgen và Open Library, có nhiều sách hơn số ISBN duy nhất. Điều đó có nghĩa là nhiều cuốn sách không có ISBN, hay chỉ đơn giản là thiếu metadata ISBN? Chúng tôi có thể trả lời câu hỏi này bằng cách kết hợp việc khớp tự động dựa trên các thuộc tính khác (tiêu đề, tác giả, nhà xuất bản, v.v.), thu thập thêm các nguồn dữ liệu và trích xuất ISBN từ chính các bản quét sách (trong trường hợp của Thư viện Z/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Có bao nhiêu trong số những ISBN đó là duy nhất? Điều này được minh họa tốt nhất bằng một biểu đồ Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Để chính xác hơn:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Chúng tôi đã ngạc nhiên bởi sự trùng lặp ít ỏi! ISBNdb có một lượng lớn ISBN không xuất hiện trong cả Thư viện Z hay Open Library, và điều tương tự cũng xảy ra (ở mức độ nhỏ hơn nhưng vẫn đáng kể) với hai cái còn lại. Điều này đặt ra nhiều câu hỏi mới. Việc ghép tự động sẽ giúp ích bao nhiêu trong việc gắn thẻ các cuốn sách chưa được gắn thẻ ISBN? Liệu có nhiều sự trùng khớp và do đó tăng sự trùng lặp? Ngoài ra, điều gì sẽ xảy ra nếu chúng ta đưa vào datasets thứ 4 hoặc thứ 5? Chúng ta sẽ thấy bao nhiêu sự trùng lặp khi đó?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Điều này cho chúng ta một điểm khởi đầu. Chúng ta có thể xem tất cả các ISBN không có trong dataset Thư viện Z, và không khớp với các trường tiêu đề/tác giả. Điều đó có thể giúp chúng ta bảo tồn tất cả các cuốn sách trên thế giới: đầu tiên bằng cách thu thập các bản quét từ internet, sau đó là ra ngoài thực tế để quét sách. Việc sau thậm chí có thể được tài trợ cộng đồng, hoặc được thúc đẩy bởi “tiền thưởng” từ những người muốn thấy các cuốn sách cụ thể được số hóa. Tất cả đó là một câu chuyện cho một thời điểm khác."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Nếu bạn muốn giúp đỡ với bất kỳ điều gì trong số này — phân tích thêm; thu thập thêm metadata; tìm thêm sách; OCR sách; thực hiện điều này cho các lĩnh vực khác (ví dụ như bài báo, sách nói, phim, chương trình truyền hình, tạp chí) hoặc thậm chí làm cho một số dữ liệu này có sẵn cho các mục đích như đào tạo ML / mô hình ngôn ngữ lớn — vui lòng liên hệ với tôi (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Nếu bạn đặc biệt quan tâm đến phân tích dữ liệu, chúng tôi đang làm việc để làm cho datasets và script của chúng tôi có sẵn ở định dạng dễ sử dụng hơn. Sẽ rất tuyệt nếu bạn có thể chỉ cần fork một notebook và bắt đầu khám phá điều này."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Cuối cùng, nếu bạn muốn hỗ trợ công việc này, xin hãy cân nhắc việc quyên góp. Đây là một hoạt động hoàn toàn do tình nguyện viên điều hành, và sự đóng góp của bạn tạo ra sự khác biệt lớn. Mỗi chút đều có ích. Hiện tại chúng tôi nhận quyên góp bằng tiền điện tử; xem trang Quyên góp trên Lưu trữ của Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Với một định nghĩa hợp lý nào đó về \"mãi mãi\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Tất nhiên, di sản viết của nhân loại còn nhiều hơn sách, đặc biệt là ngày nay. Vì lợi ích của bài viết này và các bản phát hành gần đây của chúng tôi, chúng tôi tập trung vào sách, nhưng sự quan tâm của chúng tôi còn mở rộng hơn."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Có rất nhiều điều có thể nói về Aaron Swartz, nhưng chúng tôi chỉ muốn đề cập đến anh ấy một cách ngắn gọn, vì anh ấy đóng một vai trò then chốt trong câu chuyện này. Khi thời gian trôi qua, nhiều người có thể lần đầu tiên biết đến tên anh ấy, và sau đó có thể tự mình khám phá sâu hơn."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Cửa sổ quan trọng của các thư viện bóng tối"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Làm thế nào chúng ta có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi, khi chúng đã gần đạt 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Phiên bản tiếng Trung 中文版</a>, thảo luận trên <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Tại Lưu trữ của Anna, chúng tôi thường được hỏi làm thế nào chúng tôi có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi, khi tổng kích thước đã gần đạt 1 Petabyte (1000 TB) và vẫn đang tăng. Trong bài viết này, chúng tôi sẽ xem xét triết lý của mình và xem tại sao thập kỷ tới là quan trọng đối với sứ mệnh bảo tồn kiến thức và văn hóa của nhân loại."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Tổng kích thước</a> của các bộ sưu tập của chúng tôi, trong vài tháng qua, được phân chia theo số lượng người chia sẻ torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Ưu tiên"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Tại sao chúng tôi lại quan tâm nhiều đến các bài báo và sách? Hãy gác lại niềm tin cơ bản của chúng tôi về việc bảo tồn nói chung — chúng tôi có thể viết một bài viết khác về điều đó. Vậy tại sao lại là các bài báo và sách cụ thể? Câu trả lời rất đơn giản: <strong>mật độ thông tin</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Với mỗi megabyte lưu trữ, văn bản viết lưu trữ nhiều thông tin nhất trong tất cả các phương tiện. Mặc dù chúng tôi quan tâm đến cả kiến thức và văn hóa, nhưng chúng tôi quan tâm nhiều hơn đến cái trước. Nhìn chung, chúng tôi tìm thấy một hệ thống phân cấp về mật độ thông tin và tầm quan trọng của việc bảo tồn trông đại khái như sau:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Các bài báo học thuật, tạp chí, báo cáo"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Dữ liệu hữu cơ như chuỗi DNA, hạt giống thực vật, hoặc mẫu vi sinh vật"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Sách phi hư cấu"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Mã phần mềm khoa học & kỹ thuật"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Dữ liệu đo lường như đo lường khoa học, dữ liệu kinh tế, báo cáo doanh nghiệp"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Các trang web khoa học & kỹ thuật, thảo luận trực tuyến"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Tạp chí phi hư cấu, báo chí, sách hướng dẫn"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Bản ghi phi hư cấu của các buổi nói chuyện, phim tài liệu, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Dữ liệu nội bộ từ các tập đoàn hoặc chính phủ (rò rỉ)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Hồ sơ metadata nói chung (của phi hư cấu và hư cấu; của các phương tiện khác, nghệ thuật, con người, v.v.; bao gồm đánh giá)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Dữ liệu địa lý (ví dụ: bản đồ, khảo sát địa chất)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Bản ghi chép của các thủ tục pháp lý hoặc tòa án"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Phiên bản hư cấu hoặc giải trí của tất cả các mục trên"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Xếp hạng trong danh sách này có phần tùy ý — một số mục có sự đồng hạng hoặc có sự bất đồng trong nhóm của chúng tôi — và có lẽ chúng tôi đã quên một số danh mục quan trọng. Nhưng đây là cách chúng tôi ưu tiên một cách tổng quát."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Một số mục trong danh sách này quá khác biệt so với các mục khác để chúng tôi lo lắng (hoặc đã được các tổ chức khác xử lý), chẳng hạn như dữ liệu hữu cơ hoặc dữ liệu địa lý. Nhưng hầu hết các mục trong danh sách này thực sự quan trọng đối với chúng tôi."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Một yếu tố lớn khác trong việc ưu tiên của chúng tôi là mức độ rủi ro của một tác phẩm nhất định. Chúng tôi thích tập trung vào các tác phẩm mà:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Hiếm"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Được chú ý đặc biệt"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Có nguy cơ bị phá hủy đặc biệt (ví dụ: do chiến tranh, cắt giảm tài trợ, kiện tụng, hoặc đàn áp chính trị)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Cuối cùng, chúng tôi quan tâm đến quy mô. Chúng tôi có thời gian và tiền bạc hạn chế, vì vậy chúng tôi thà dành một tháng để cứu 10.000 cuốn sách hơn là 1.000 cuốn sách — nếu chúng có giá trị và rủi ro tương đương."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Thư viện bóng tối"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Có nhiều tổ chức có sứ mệnh tương tự và ưu tiên tương tự. Thực tế, có các thư viện, kho lưu trữ, phòng thí nghiệm, bảo tàng và các tổ chức khác được giao nhiệm vụ bảo tồn loại này. Nhiều trong số đó được tài trợ tốt, bởi chính phủ, cá nhân, hoặc tập đoàn. Nhưng họ có một điểm mù lớn: hệ thống pháp lý."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Đây là vai trò độc đáo của các thư viện bóng tối, và lý do Anna’s Archive tồn tại. Chúng tôi có thể làm những điều mà các tổ chức khác không được phép làm. Bây giờ, không phải (thường) là chúng tôi có thể lưu trữ các tài liệu mà không được phép bảo tồn ở nơi khác. Không, ở nhiều nơi, việc xây dựng một kho lưu trữ với bất kỳ sách, bài báo, tạp chí nào là hợp pháp."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Nhưng điều mà các kho lưu trữ hợp pháp thường thiếu là <strong>tính dư thừa và độ bền lâu dài</strong>. Có những cuốn sách mà chỉ có một bản sao tồn tại trong một thư viện vật lý nào đó. Có những hồ sơ metadata được bảo vệ bởi một công ty duy nhất. Có những tờ báo chỉ được bảo quản trên vi phim trong một kho lưu trữ duy nhất. Thư viện có thể bị cắt giảm tài trợ, công ty có thể phá sản, kho lưu trữ có thể bị đánh bom và thiêu rụi. Đây không phải là giả thuyết — điều này xảy ra mọi lúc."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Điều mà chúng tôi có thể làm một cách độc đáo tại Lưu trữ của Anna là lưu trữ nhiều bản sao của các tác phẩm, ở quy mô lớn. Chúng tôi có thể thu thập các bài báo, sách, tạp chí và nhiều hơn nữa, và phân phối chúng hàng loạt. Hiện tại, chúng tôi thực hiện điều này thông qua torrents, nhưng công nghệ chính xác không quan trọng và sẽ thay đổi theo thời gian. Phần quan trọng là phân phối nhiều bản sao trên khắp thế giới. Câu nói này từ hơn 200 năm trước vẫn còn đúng:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Những gì đã mất không thể phục hồi; nhưng hãy cứu những gì còn lại: không phải bằng cách khóa và khóa chúng khỏi tầm mắt và sử dụng của công chúng, trong việc giao chúng cho sự lãng phí của thời gian, mà bằng cách nhân bản nhiều bản sao, để đặt chúng ngoài tầm với của tai nạn.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Một ghi chú nhanh về phạm vi công cộng. Vì Lưu trữ của Anna tập trung độc đáo vào các hoạt động bất hợp pháp ở nhiều nơi trên thế giới, chúng tôi không bận tâm đến các bộ sưu tập có sẵn rộng rãi, chẳng hạn như sách thuộc phạm vi công cộng. Các tổ chức pháp lý thường đã chăm sóc tốt cho điều đó. Tuy nhiên, có những cân nhắc khiến chúng tôi đôi khi làm việc trên các bộ sưu tập có sẵn công khai:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Các bản ghi metadata có thể được xem tự do trên trang web Worldcat, nhưng không thể tải xuống hàng loạt (cho đến khi chúng tôi <a %(worldcat_scrape)s>thu thập</a> chúng)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Mã có thể là mã nguồn mở trên Github, nhưng Github như một tổng thể không thể dễ dàng sao chép và do đó không thể bảo tồn (mặc dù trong trường hợp cụ thể này có đủ bản sao phân phối của hầu hết các kho mã)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit miễn phí sử dụng, nhưng gần đây đã đưa ra các biện pháp chống thu thập dữ liệu nghiêm ngặt, sau khi LLM đào tạo dữ liệu khát khao (sẽ nói thêm về điều đó sau)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Nhân bản nhiều bản sao"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Quay lại câu hỏi ban đầu của chúng tôi: làm thế nào chúng tôi có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi? Vấn đề chính ở đây là bộ sưu tập của chúng tôi đã <a %(torrents_stats)s>tăng trưởng</a> nhanh chóng, bằng cách thu thập và mở mã nguồn một số bộ sưu tập lớn (trên nền tảng công việc tuyệt vời đã được thực hiện bởi các thư viện bóng dữ liệu mở khác như Sci-Hub và Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Sự gia tăng dữ liệu này làm cho việc sao chép các bộ sưu tập trên toàn thế giới trở nên khó khăn hơn. Lưu trữ dữ liệu rất tốn kém! Nhưng chúng tôi lạc quan, đặc biệt khi quan sát ba xu hướng sau đây."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Chúng tôi đã hái những quả thấp"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Điều này theo trực tiếp từ các ưu tiên của chúng tôi đã thảo luận ở trên. Chúng tôi thích làm việc để giải phóng các bộ sưu tập lớn trước. Bây giờ chúng tôi đã bảo đảm một số bộ sưu tập lớn nhất trên thế giới, chúng tôi mong đợi sự tăng trưởng của mình sẽ chậm hơn nhiều."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Vẫn còn một đuôi dài của các bộ sưu tập nhỏ hơn, và sách mới được quét hoặc xuất bản mỗi ngày, nhưng tốc độ có thể sẽ chậm hơn nhiều. Chúng tôi có thể vẫn tăng gấp đôi hoặc thậm chí gấp ba kích thước, nhưng trong một khoảng thời gian dài hơn."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Chi phí lưu trữ tiếp tục giảm theo cấp số nhân"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Tại thời điểm viết bài, <a %(diskprices)s>giá đĩa</a> mỗi TB khoảng $12 cho đĩa mới, $8 cho đĩa đã qua sử dụng, và $4 cho băng. Nếu chúng tôi bảo thủ và chỉ nhìn vào đĩa mới, điều đó có nghĩa là lưu trữ một petabyte tốn khoảng $12,000. Nếu chúng tôi giả định thư viện của mình sẽ tăng gấp ba từ 900TB lên 2.7PB, điều đó có nghĩa là $32,400 để sao chép toàn bộ thư viện của chúng tôi. Thêm điện, chi phí phần cứng khác, và vân vân, hãy làm tròn lên $40,000. Hoặc với băng nhiều hơn như $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Một mặt <strong>$15,000–$40,000 cho tổng số kiến thức của nhân loại là một món hời</strong>. Mặt khác, hơi cao để mong đợi hàng tấn bản sao đầy đủ, đặc biệt nếu chúng tôi cũng muốn những người đó tiếp tục gieo hạt torrents của họ vì lợi ích của người khác."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Đó là hôm nay. Nhưng tiến bộ vẫn tiếp tục:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Chi phí ổ cứng mỗi TB đã giảm khoảng một phần ba trong 10 năm qua, và có khả năng sẽ tiếp tục giảm với tốc độ tương tự. Băng dường như đang trên một quỹ đạo tương tự. Giá SSD đang giảm thậm chí nhanh hơn, và có thể sẽ vượt qua giá HDD vào cuối thập kỷ này."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Xu hướng giá HDD từ các nguồn khác nhau (nhấp để xem nghiên cứu)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Nếu điều này đúng, thì trong 10 năm nữa chúng ta có thể chỉ cần từ 5.000–13.000 đô la để sao chép toàn bộ bộ sưu tập của mình (1/3), hoặc thậm chí ít hơn nếu chúng ta phát triển ít hơn về kích thước. Mặc dù vẫn là một số tiền lớn, nhưng điều này sẽ có thể đạt được đối với nhiều người. Và nó có thể còn tốt hơn vì điểm tiếp theo…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Cải tiến về mật độ thông tin"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Hiện tại, chúng tôi lưu trữ sách ở định dạng thô mà chúng được cung cấp cho chúng tôi. Chắc chắn, chúng đã được nén, nhưng thường thì chúng vẫn là các bản quét lớn hoặc ảnh chụp của các trang."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Cho đến nay, các lựa chọn duy nhất để thu nhỏ tổng kích thước bộ sưu tập của chúng tôi là thông qua nén mạnh hơn hoặc loại bỏ trùng lặp. Tuy nhiên, để tiết kiệm đáng kể, cả hai đều quá mất mát đối với chúng tôi. Nén mạnh ảnh có thể làm cho văn bản khó đọc. Và loại bỏ trùng lặp đòi hỏi sự tự tin cao rằng các cuốn sách hoàn toàn giống nhau, điều này thường không chính xác, đặc biệt nếu nội dung giống nhau nhưng các bản quét được thực hiện vào các dịp khác nhau."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Luôn có một lựa chọn thứ ba, nhưng chất lượng của nó đã quá tệ đến mức chúng tôi chưa bao giờ xem xét: <strong>OCR, hay Nhận dạng Ký tự Quang học</strong>. Đây là quá trình chuyển đổi ảnh thành văn bản thuần túy, bằng cách sử dụng AI để phát hiện các ký tự trong ảnh. Các công cụ cho việc này đã tồn tại từ lâu và khá tốt, nhưng “khá tốt” là không đủ cho mục đích bảo tồn."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Tuy nhiên, các mô hình học sâu đa phương thức gần đây đã tiến bộ cực kỳ nhanh chóng, mặc dù vẫn có chi phí cao. Chúng tôi kỳ vọng cả độ chính xác và chi phí sẽ được cải thiện đáng kể trong những năm tới, đến mức nó sẽ trở nên thực tế để áp dụng cho toàn bộ thư viện của chúng tôi."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Cải tiến OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Khi điều đó xảy ra, chúng tôi có thể vẫn sẽ bảo tồn các tệp gốc, nhưng ngoài ra chúng tôi có thể có một phiên bản nhỏ hơn nhiều của thư viện mà hầu hết mọi người sẽ muốn sao chép. Điều thú vị là văn bản thô tự nó nén tốt hơn nhiều và dễ dàng loại bỏ trùng lặp hơn, mang lại cho chúng tôi nhiều tiết kiệm hơn."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Nhìn chung, không phải là không thực tế khi mong đợi ít nhất giảm 5-10 lần tổng kích thước tệp, có thể thậm chí nhiều hơn. Ngay cả với mức giảm bảo thủ 5 lần, chúng tôi sẽ xem xét <strong>1.000–3.000 đô la trong 10 năm ngay cả khi thư viện của chúng tôi tăng gấp ba lần kích thước</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Cửa sổ quan trọng"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Nếu những dự báo này chính xác, chúng tôi <strong>chỉ cần đợi vài năm</strong> trước khi toàn bộ bộ sưu tập của chúng tôi sẽ được sao chép rộng rãi. Do đó, theo lời của Thomas Jefferson, “được đặt ngoài tầm với của tai nạn.”"

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Thật không may, sự xuất hiện của LLMs, và việc đào tạo dữ liệu khát khao của chúng, đã đặt nhiều chủ sở hữu bản quyền vào thế phòng thủ. Thậm chí còn hơn cả trước đây. Nhiều trang web đang làm cho việc thu thập và lưu trữ trở nên khó khăn hơn, các vụ kiện tụng đang diễn ra, và trong khi đó các thư viện và lưu trữ vật lý tiếp tục bị bỏ quên."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Chúng tôi chỉ có thể mong đợi những xu hướng này tiếp tục xấu đi, và nhiều tác phẩm sẽ bị mất trước khi chúng bước vào phạm vi công cộng."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Chúng ta đang ở ngưỡng cửa của một cuộc cách mạng trong bảo tồn, nhưng <q>những gì đã mất không thể phục hồi.</q></strong> Chúng ta có một cửa sổ quan trọng khoảng 5-10 năm trong đó vẫn còn khá đắt đỏ để vận hành một thư viện bóng tối và tạo ra nhiều bản sao trên khắp thế giới, và trong đó quyền truy cập chưa bị đóng hoàn toàn."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Nếu chúng ta có thể vượt qua cửa sổ này, thì chúng ta sẽ thực sự bảo tồn kiến thức và văn hóa của nhân loại mãi mãi. Chúng ta không nên để thời gian này trôi qua vô ích. Chúng ta không nên để cửa sổ quan trọng này đóng lại với chúng ta."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Hãy tiến lên."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Quyền truy cập độc quyền cho các công ty LLM vào bộ sưu tập sách phi hư cấu Trung Quốc lớn nhất thế giới"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Phiên bản tiếng Trung 中文版</a>, <a %(news_ycombinator)s>Thảo luận trên Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Lưu trữ của Anna đã thu thập được một bộ sưu tập độc đáo gồm 7,5 triệu / 350TB sách phi hư cấu Trung Quốc — lớn hơn Thư viện Genesis. Chúng tôi sẵn sàng cung cấp quyền truy cập độc quyền cho một công ty LLM, đổi lại là OCR chất lượng cao và trích xuất văn bản.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Đây là một bài viết blog ngắn. Chúng tôi đang tìm kiếm một công ty hoặc tổ chức nào đó để giúp chúng tôi với OCR và trích xuất văn bản cho một bộ sưu tập khổng lồ mà chúng tôi đã thu thập, đổi lại là quyền truy cập sớm độc quyền. Sau thời gian cấm vận, chúng tôi tất nhiên sẽ phát hành toàn bộ bộ sưu tập."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Văn bản học thuật chất lượng cao cực kỳ hữu ích cho việc đào tạo các LLM. Mặc dù bộ sưu tập của chúng tôi là tiếng Trung, điều này vẫn có thể hữu ích cho việc đào tạo các LLM tiếng Anh: các mô hình dường như mã hóa các khái niệm và kiến thức bất kể ngôn ngữ nguồn."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Để làm điều này, văn bản cần được trích xuất từ các bản quét. Lưu trữ của Anna được gì từ việc này? Tìm kiếm toàn văn của các cuốn sách cho người dùng của mình."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Vì mục tiêu của chúng tôi phù hợp với các nhà phát triển LLM, chúng tôi đang tìm kiếm một cộng tác viên. Chúng tôi sẵn sàng cung cấp cho bạn <strong>quyền truy cập sớm độc quyền vào bộ sưu tập này với số lượng lớn trong 1 năm</strong>, nếu bạn có thể thực hiện OCR và trích xuất văn bản đúng cách. Nếu bạn sẵn lòng chia sẻ toàn bộ mã của quy trình của bạn với chúng tôi, chúng tôi sẽ sẵn sàng kéo dài thời gian cấm vận bộ sưu tập."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Trang ví dụ"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Để chứng minh cho chúng tôi thấy rằng bạn có một quy trình tốt, đây là một số trang ví dụ để bắt đầu, từ một cuốn sách về siêu dẫn. Quy trình của bạn nên xử lý đúng cách các công thức toán học, bảng biểu, biểu đồ, chú thích cuối trang, v.v."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Gửi các trang đã xử lý của bạn đến email của chúng tôi. Nếu chúng trông ổn, chúng tôi sẽ gửi cho bạn nhiều hơn trong riêng tư, và chúng tôi mong bạn có thể nhanh chóng chạy quy trình của mình trên những trang đó. Khi chúng tôi hài lòng, chúng ta có thể thỏa thuận."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Bộ sưu tập"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Một số thông tin thêm về bộ sưu tập. <a %(duxiu)s>Duxiu</a> là một cơ sở dữ liệu khổng lồ về sách đã quét, được tạo ra bởi <a %(chaoxing)s>Nhóm Thư viện Kỹ thuật số SuperStar</a>. Hầu hết là sách học thuật, được quét để làm cho chúng có sẵn kỹ thuật số cho các trường đại học và thư viện. Đối với khán giả nói tiếng Anh của chúng tôi, <a %(library_princeton)s>Princeton</a> và <a %(guides_lib_uw)s>Đại học Washington</a> có cái nhìn tổng quan tốt. Cũng có một bài viết xuất sắc cung cấp thêm bối cảnh: <a %(doi)s>“Số hóa Sách Trung Quốc: Nghiên cứu Trường hợp của Công cụ Tìm kiếm Học giả SuperStar DuXiu”</a> (tìm kiếm trong Lưu trữ của Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Những cuốn sách từ Duxiu đã lâu bị vi phạm bản quyền trên internet Trung Quốc. Thông thường chúng được bán với giá chưa đến một đô la bởi các nhà bán lẻ. Chúng thường được phân phối bằng cách sử dụng phiên bản tương đương của Google Drive tại Trung Quốc, thường bị hack để cho phép có thêm không gian lưu trữ. Một số chi tiết kỹ thuật có thể được tìm thấy <a %(github_duty_machine)s>tại đây</a> và <a %(github_821_github_io)s>tại đây</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Mặc dù các cuốn sách đã được phân phối bán công khai, nhưng khá khó để có được chúng với số lượng lớn. Chúng tôi đã đặt điều này cao trong danh sách công việc của mình và đã dành nhiều tháng làm việc toàn thời gian cho nó. Tuy nhiên, gần đây một tình nguyện viên tuyệt vời, đáng kinh ngạc và tài năng đã liên hệ với chúng tôi, nói rằng họ đã làm tất cả công việc này rồi — với chi phí lớn. Họ đã chia sẻ toàn bộ bộ sưu tập với chúng tôi, mà không mong đợi bất cứ điều gì đáp lại, ngoại trừ sự đảm bảo bảo tồn lâu dài. Thật đáng kinh ngạc. Họ đã đồng ý yêu cầu sự giúp đỡ theo cách này để bộ sưu tập được OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Bộ sưu tập gồm 7.543.702 tệp. Đây là nhiều hơn Thư viện Genesis phi hư cấu (khoảng 5,3 triệu). Tổng kích thước tệp là khoảng 359TB (326TiB) ở dạng hiện tại."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Chúng tôi mở cửa cho các đề xuất và ý tưởng khác. Chỉ cần liên hệ với chúng tôi. Hãy xem Lưu trữ của Anna để biết thêm thông tin về các bộ sưu tập của chúng tôi, nỗ lực bảo tồn và cách bạn có thể giúp đỡ. Cảm ơn!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Cảnh báo: bài viết blog này đã bị ngừng sử dụng. Chúng tôi đã quyết định rằng IPFS chưa sẵn sàng cho thời điểm hiện tại. Chúng tôi vẫn sẽ liên kết đến các tệp trên IPFS từ Lưu trữ của Anna khi có thể, nhưng chúng tôi sẽ không tự lưu trữ nữa, cũng không khuyến khích người khác sao chép bằng IPFS. Vui lòng xem trang Torrents của chúng tôi nếu bạn muốn giúp bảo tồn bộ sưu tập của chúng tôi."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Giúp gieo hạt Thư viện Z trên IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cách vận hành một thư viện bóng tối: hoạt động tại Lưu trữ của Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Không có <q>AWS cho các tổ chức từ thiện bóng tối,</q> vậy làm thế nào chúng tôi vận hành Lưu trữ của Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Tôi điều hành <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, công cụ tìm kiếm mã nguồn mở phi lợi nhuận lớn nhất thế giới cho <a %(wikipedia_shadow_library)s>thư viện bóng tối</a>, như Sci-Hub, Library Genesis và Thư viện Z. Mục tiêu của chúng tôi là làm cho kiến thức và văn hóa dễ dàng tiếp cận, và cuối cùng là xây dựng một cộng đồng những người cùng nhau lưu trữ và bảo tồn <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tất cả các cuốn sách trên thế giới</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Trong bài viết này, tôi sẽ chỉ ra cách chúng tôi vận hành trang web này, và những thách thức độc đáo đi kèm với việc điều hành một trang web có tình trạng pháp lý đáng ngờ, vì không có “AWS cho các tổ chức từ thiện bóng tối”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Cũng hãy xem bài viết chị em <a %(blog_how_to_become_a_pirate_archivist)s>Cách trở thành một nhà lưu trữ hải tặc</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Thẻ đổi mới"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Hãy bắt đầu với công nghệ của chúng tôi. Nó cố ý nhàm chán. Chúng tôi sử dụng Flask, MariaDB và ElasticSearch. Đó là tất cả. Tìm kiếm phần lớn là một vấn đề đã được giải quyết, và chúng tôi không có ý định phát minh lại nó. Ngoài ra, chúng tôi phải dành <a %(mcfunley)s>thẻ đổi mới</a> của mình cho một thứ khác: không bị chính quyền gỡ bỏ."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Vậy Anna’s Archive hợp pháp hay bất hợp pháp đến mức nào? Điều này chủ yếu phụ thuộc vào khu vực pháp lý. Hầu hết các quốc gia tin vào một hình thức bản quyền nào đó, có nghĩa là người hoặc công ty được chỉ định một độc quyền nhất định đối với một số loại tác phẩm trong một khoảng thời gian nhất định. Ngoài ra, tại Anna’s Archive, chúng tôi tin rằng mặc dù có một số lợi ích, nhưng nhìn chung bản quyền là một điều tiêu cực cho xã hội — nhưng đó là một câu chuyện khác."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Độc quyền này đối với một số tác phẩm có nghĩa là bất kỳ ai ngoài độc quyền này đều không được phép phân phối trực tiếp các tác phẩm đó — bao gồm cả chúng tôi. Nhưng Anna’s Archive là một công cụ tìm kiếm không phân phối trực tiếp các tác phẩm đó (ít nhất là không trên trang web clearnet của chúng tôi), vì vậy chúng tôi nên ổn, phải không? Không hẳn. Ở nhiều khu vực pháp lý, không chỉ việc phân phối các tác phẩm có bản quyền là bất hợp pháp, mà còn việc liên kết đến các nơi làm điều đó cũng vậy. Một ví dụ kinh điển về điều này là luật DMCA của Hoa Kỳ."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Đó là đầu cực kỳ nghiêm ngặt của phổ. Ở đầu kia của phổ, về lý thuyết có thể có các quốc gia không có luật bản quyền nào cả, nhưng thực tế không tồn tại. Hầu như mọi quốc gia đều có một số hình thức luật bản quyền. Việc thực thi là một câu chuyện khác. Có rất nhiều quốc gia có chính phủ không quan tâm đến việc thực thi luật bản quyền. Cũng có những quốc gia nằm giữa hai cực đoan, cấm phân phối các tác phẩm có bản quyền, nhưng không cấm liên kết đến các tác phẩm đó."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Một cân nhắc khác là ở cấp độ công ty. Nếu một công ty hoạt động trong một khu vực pháp lý không quan tâm đến bản quyền, nhưng bản thân công ty không sẵn sàng chấp nhận bất kỳ rủi ro nào, thì họ có thể đóng cửa trang web của bạn ngay khi có ai đó phàn nàn về nó."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Cuối cùng, một cân nhắc lớn là thanh toán. Vì chúng tôi cần giữ ẩn danh, chúng tôi không thể sử dụng các phương thức thanh toán truyền thống. Điều này khiến chúng tôi chỉ còn lại tiền điện tử, và chỉ có một số ít công ty hỗ trợ điều đó (có các thẻ ghi nợ ảo được thanh toán bằng tiền điện tử, nhưng chúng thường không được chấp nhận)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Kiến trúc hệ thống"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Vậy hãy giả sử rằng bạn đã tìm thấy một số công ty sẵn sàng lưu trữ trang web của bạn mà không đóng cửa bạn — hãy gọi họ là “nhà cung cấp yêu tự do” 😄. Bạn sẽ nhanh chóng nhận ra rằng lưu trữ mọi thứ với họ khá đắt đỏ, vì vậy bạn có thể muốn tìm một số “nhà cung cấp giá rẻ” và thực hiện việc lưu trữ thực tế ở đó, thông qua các nhà cung cấp yêu tự do. Nếu bạn làm đúng, các nhà cung cấp giá rẻ sẽ không bao giờ biết bạn đang lưu trữ gì, và không bao giờ nhận được bất kỳ khiếu nại nào."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Với tất cả các nhà cung cấp này, có nguy cơ họ vẫn đóng cửa bạn, vì vậy bạn cũng cần có sự dự phòng. Chúng tôi cần điều này ở tất cả các cấp độ của công nghệ của chúng tôi."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Một công ty có phần yêu tự do đã đặt mình vào một vị trí thú vị là Cloudflare. Họ đã <a %(blog_cloudflare)s>lập luận</a> rằng họ không phải là nhà cung cấp lưu trữ, mà là một tiện ích, giống như một ISP. Do đó, họ không phải tuân theo DMCA hoặc các yêu cầu gỡ bỏ khác, và chuyển tiếp bất kỳ yêu cầu nào đến nhà cung cấp lưu trữ thực tế của bạn. Họ đã đi xa đến mức ra tòa để bảo vệ cấu trúc này. Do đó, chúng tôi có thể sử dụng họ như một lớp bộ nhớ đệm và bảo vệ khác."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare không chấp nhận thanh toán ẩn danh, vì vậy chúng tôi chỉ có thể sử dụng gói miễn phí của họ. Điều này có nghĩa là chúng tôi không thể sử dụng các tính năng cân bằng tải hoặc chuyển đổi dự phòng của họ. Do đó, chúng tôi <a %(annas_archive_l255)s>tự triển khai điều này</a> ở cấp độ tên miền. Khi tải trang, trình duyệt sẽ kiểm tra xem tên miền hiện tại có còn khả dụng không, và nếu không, nó sẽ viết lại tất cả các URL sang một tên miền khác. Vì Cloudflare lưu trữ nhiều trang, điều này có nghĩa là người dùng có thể truy cập vào tên miền chính của chúng tôi, ngay cả khi máy chủ proxy bị sập, và sau đó trong lần nhấp tiếp theo sẽ được chuyển sang một tên miền khác."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Chúng tôi vẫn còn phải đối mặt với các mối quan tâm hoạt động bình thường, chẳng hạn như giám sát sức khỏe máy chủ, ghi lại lỗi backend và frontend, v.v. Kiến trúc chuyển đổi dự phòng của chúng tôi cho phép có thêm độ bền vững trên mặt trận này, ví dụ bằng cách chạy một bộ máy chủ hoàn toàn khác trên một trong các tên miền. Chúng tôi thậm chí có thể chạy các phiên bản cũ hơn của mã và Datasets trên tên miền riêng biệt này, trong trường hợp một lỗi nghiêm trọng trong phiên bản chính không được phát hiện."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Chúng tôi cũng có thể phòng ngừa việc Cloudflare quay lưng lại với chúng tôi, bằng cách loại bỏ nó khỏi một trong các tên miền, chẳng hạn như tên miền riêng biệt này. Các hoán vị khác nhau của những ý tưởng này là có thể."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Công cụ"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Hãy xem các công cụ chúng tôi sử dụng để thực hiện tất cả những điều này. Điều này đang phát triển rất nhiều khi chúng tôi gặp phải các vấn đề mới và tìm ra các giải pháp mới."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Máy chủ ứng dụng: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Máy chủ proxy: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Quản lý máy chủ: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Phát triển: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Lưu trữ tĩnh Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Có một số quyết định mà chúng tôi đã thay đổi qua lại. Một trong số đó là giao tiếp giữa các máy chủ: chúng tôi từng sử dụng Wireguard cho việc này, nhưng nhận thấy rằng đôi khi nó ngừng truyền dữ liệu hoặc chỉ truyền dữ liệu theo một hướng. Điều này đã xảy ra với một số thiết lập Wireguard khác nhau mà chúng tôi đã thử, chẳng hạn như <a %(github_costela_wesher)s>wesher</a> và <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Chúng tôi cũng đã thử đường hầm cổng qua SSH, sử dụng autossh và sshuttle, nhưng gặp <a %(github_sshuttle)s>vấn đề ở đó</a> (mặc dù tôi vẫn chưa rõ liệu autossh có gặp vấn đề TCP-over-TCP hay không — nó chỉ cảm thấy như một giải pháp không ổn định đối với tôi nhưng có thể nó thực sự ổn?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Thay vào đó, chúng tôi đã quay lại kết nối trực tiếp giữa các máy chủ, ẩn rằng một máy chủ đang chạy trên các nhà cung cấp giá rẻ bằng cách sử dụng lọc IP với UFW. Điều này có nhược điểm là Docker không hoạt động tốt với UFW, trừ khi bạn sử dụng <code>network_mode: \"host\"</code>. Tất cả điều này có phần dễ mắc lỗi hơn, vì bạn sẽ phơi bày máy chủ của mình ra internet chỉ với một cấu hình sai nhỏ. Có lẽ chúng tôi nên quay lại sử dụng autossh — phản hồi sẽ rất được hoan nghênh ở đây."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Chúng tôi cũng đã thay đổi qua lại giữa Varnish và Nginx. Hiện tại chúng tôi thích Varnish, nhưng nó có những điểm kỳ quặc và góc cạnh. Điều tương tự cũng áp dụng cho Checkmk: chúng tôi không yêu thích nó, nhưng nó hoạt động tạm thời. Weblate đã ổn nhưng không tuyệt vời — đôi khi tôi lo sợ nó sẽ mất dữ liệu của tôi mỗi khi tôi cố gắng đồng bộ hóa nó với kho git của chúng tôi. Flask nhìn chung đã tốt, nhưng nó có một số điểm kỳ quặc kỳ lạ đã tốn rất nhiều thời gian để gỡ lỗi, chẳng hạn như cấu hình tên miền tùy chỉnh hoặc các vấn đề với tích hợp SqlAlchemy của nó."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Cho đến nay, các công cụ khác đã rất tuyệt vời: chúng tôi không có khiếu nại nghiêm trọng nào về MariaDB, ElasticSearch, Gitlab, Zulip, Docker và Tor. Tất cả những điều này đã gặp một số vấn đề, nhưng không có gì quá nghiêm trọng hoặc tốn thời gian."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Kết luận"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Đó là một trải nghiệm thú vị khi học cách thiết lập một công cụ tìm kiếm thư viện bóng tối mạnh mẽ và bền bỉ. Có rất nhiều chi tiết khác để chia sẻ trong các bài viết sau, vì vậy hãy cho tôi biết bạn muốn tìm hiểu thêm về điều gì!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Như mọi khi, chúng tôi đang tìm kiếm các khoản quyên góp để hỗ trợ công việc này, vì vậy hãy chắc chắn kiểm tra trang Quyên góp trên Lưu trữ của Anna. Chúng tôi cũng đang tìm kiếm các loại hỗ trợ khác, chẳng hạn như tài trợ, nhà tài trợ dài hạn, nhà cung cấp thanh toán rủi ro cao, có thể thậm chí là quảng cáo (có gu thẩm mỹ!). Và nếu bạn muốn đóng góp thời gian và kỹ năng của mình, chúng tôi luôn tìm kiếm các nhà phát triển, dịch giả, v.v. Cảm ơn sự quan tâm và hỗ trợ của bạn."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Xin chào, tôi là Anna. Tôi đã tạo ra <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, thư viện bóng tối lớn nhất thế giới. Đây là blog cá nhân của tôi, nơi tôi và các đồng đội viết về vi phạm bản quyền, bảo tồn kỹ thuật số và nhiều hơn nữa."

#, fuzzy
msgid "blog.index.text2"
msgstr "Kết nối với tôi trên <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Lưu ý rằng trang web này chỉ là một blog. Chúng tôi chỉ lưu trữ lời nói của chính mình ở đây. Không có torrent hoặc các tệp có bản quyền nào được lưu trữ hoặc liên kết ở đây."

#, fuzzy
msgid "blog.index.heading"
msgstr "Bài viết blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1,3 tỷ WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Đưa 5.998.794 cuốn sách lên IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Cảnh báo: bài viết blog này đã bị ngừng sử dụng. Chúng tôi đã quyết định rằng IPFS chưa sẵn sàng cho thời điểm hiện tại. Chúng tôi vẫn sẽ liên kết đến các tệp trên IPFS từ Lưu trữ của Anna khi có thể, nhưng chúng tôi sẽ không tự lưu trữ nữa, cũng không khuyến khích người khác sao chép bằng IPFS. Vui lòng xem trang Torrents của chúng tôi nếu bạn muốn giúp bảo tồn bộ sưu tập của chúng tôi."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>Tóm tắt:</strong> Lưu trữ của Anna đã quét toàn bộ WorldCat (bộ sưu tập metadata thư viện lớn nhất thế giới) để tạo danh sách TODO các cuốn sách cần được bảo tồn.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Một năm trước, chúng tôi <a %(blog)s>bắt đầu</a> trả lời câu hỏi này: <strong>Phần trăm sách nào đã được bảo tồn vĩnh viễn bởi các thư viện bóng tối?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Khi một cuốn sách được đưa vào một thư viện bóng tối dữ liệu mở như <a %(wikipedia_library_genesis)s>Library Genesis</a>, và bây giờ là <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, nó sẽ được sao chép khắp thế giới (thông qua torrents), do đó thực tế bảo tồn nó mãi mãi."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Để trả lời câu hỏi về phần trăm sách đã được bảo tồn, chúng ta cần biết mẫu số: có bao nhiêu cuốn sách tồn tại tổng cộng? Và lý tưởng nhất là chúng ta không chỉ có một con số, mà còn có metadata thực tế. Sau đó, chúng ta không chỉ có thể so khớp chúng với các thư viện bóng tối, mà còn <strong>tạo một danh sách TODO của các cuốn sách còn lại cần bảo tồn!</strong> Chúng ta thậm chí có thể bắt đầu mơ về một nỗ lực cộng đồng để đi xuống danh sách TODO này."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Chúng tôi đã thu thập dữ liệu từ <a %(wikipedia_isbndb_com)s>ISBNdb</a>, và tải xuống <a %(openlibrary)s>dữ liệu Open Library</a>, nhưng kết quả không đạt yêu cầu. Vấn đề chính là không có nhiều sự trùng lặp của ISBNs. Xem biểu đồ Venn này từ <a %(blog)s>bài viết blog của chúng tôi</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Chúng tôi rất ngạc nhiên về việc có rất ít sự trùng lặp giữa ISBNdb và Open Library, cả hai đều bao gồm dữ liệu từ nhiều nguồn khác nhau, chẳng hạn như thu thập dữ liệu web và hồ sơ thư viện. Nếu cả hai đều làm tốt việc tìm kiếm hầu hết các ISBNs ngoài kia, các vòng tròn của chúng chắc chắn sẽ có sự trùng lặp đáng kể, hoặc một trong hai sẽ là tập con của cái kia. Điều này khiến chúng tôi tự hỏi, có bao nhiêu cuốn sách nằm <em>hoàn toàn ngoài các vòng tròn này</em>? Chúng tôi cần một cơ sở dữ liệu lớn hơn."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Đó là khi chúng tôi đặt mục tiêu vào cơ sở dữ liệu sách lớn nhất thế giới: <a %(wikipedia_worldcat)s>WorldCat</a>. Đây là một cơ sở dữ liệu độc quyền của tổ chức phi lợi nhuận <a %(wikipedia_oclc)s>OCLC</a>, nơi tổng hợp các bản ghi metadata từ các thư viện trên khắp thế giới, để đổi lấy việc cung cấp cho các thư viện đó quyền truy cập vào toàn bộ tập dữ liệu, và cho phép chúng xuất hiện trong kết quả tìm kiếm của người dùng cuối."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Mặc dù OCLC là một tổ chức phi lợi nhuận, mô hình kinh doanh của họ yêu cầu bảo vệ cơ sở dữ liệu của mình. Chà, chúng tôi rất tiếc phải nói, các bạn tại OCLC, chúng tôi sẽ chia sẻ tất cả. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Trong năm qua, chúng tôi đã cẩn thận thu thập tất cả các bản ghi của WorldCat. Ban đầu, chúng tôi gặp may mắn. WorldCat vừa triển khai thiết kế lại hoàn toàn trang web của họ (vào tháng 8 năm 2022). Điều này bao gồm một sự cải tổ đáng kể của các hệ thống backend của họ, giới thiệu nhiều lỗ hổng bảo mật. Chúng tôi ngay lập tức nắm bắt cơ hội, và có thể thu thập hàng trăm triệu (!) bản ghi chỉ trong vài ngày."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Thiết kế lại WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Sau đó, các lỗ hổng bảo mật dần dần được sửa chữa từng cái một, cho đến khi cái cuối cùng mà chúng tôi tìm thấy được vá khoảng một tháng trước. Đến lúc đó, chúng tôi đã có hầu hết các bản ghi, và chỉ đang tìm kiếm các bản ghi chất lượng cao hơn một chút. Vì vậy, chúng tôi cảm thấy đã đến lúc phát hành!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Hãy xem một số thông tin cơ bản về dữ liệu:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Định dạng?</strong> <a %(blog)s>Các Container Lưu trữ của Anna (AAC)</a>, về cơ bản là <a %(jsonlines)s>JSON Lines</a> nén với <a %(zstd)s>Zstandard</a>, cùng với một số ngữ nghĩa tiêu chuẩn hóa. Các container này bao bọc các loại bản ghi khác nhau, dựa trên các lần thu thập dữ liệu khác nhau mà chúng tôi đã triển khai."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Dữ liệu"

msgid "dyn.buy_membership.error.unknown"
msgstr "Đã xảy ra lỗi không xác định. Vui lòng liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình."

msgid "dyn.buy_membership.error.minimum"
msgstr "Đồng tiền này có mức tối thiểu cao hơn bình thường. Vui lòng chọn thời gian khác hoặc đồng tiền khác."

msgid "dyn.buy_membership.error.try_again"
msgstr "Yêu cầu không thể hoàn thành. Vui lòng thử lại sau vài phút, và nếu tình trạng này tiếp tục xảy ra, hãy liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình."

msgid "dyn.buy_membership.error.wait"
msgstr "Lỗi trong quá trình xử lý thanh toán. Vui lòng chờ một chút và thử lại. Nếu vấn đề vẫn tiếp diễn trong hơn 24 giờ, vui lòng liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình."

msgid "page.comments.hidden_comment"
msgstr "bình luận ẩn"

msgid "page.comments.file_issue"
msgstr "Vấn đề tệp: %(file_issue)s"

msgid "page.comments.better_version"
msgstr "Phiên bản tốt hơn"

msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Bạn có muốn báo cáo người dùng này vì hành vi lạm dụng hoặc không phù hợp không?"

msgid "page.comments.report_abuse"
msgstr "Báo cáo lạm dụng"

msgid "page.comments.abuse_reported"
msgstr "Lạm dụng đã được báo cáo:"

msgid "page.comments.reported_abuse_this_user"
msgstr "Bạn đã báo cáo người dùng này vì lạm dụng."

msgid "page.comments.reply_button"
msgstr "Trả lời"

msgid "page.md5.quality.logged_out_login"
msgstr "Xin vui lòng <a %(a_login)s>đăng nhập</a>."

msgid "page.md5.quality.comment_thanks"
msgstr "Bạn đã để lại bình luận. Có thể mất một phút để cho nó hiển thị."

msgid "page.md5.quality.comment_error"
msgstr "Đã xảy ra lỗi. Xin vui lòng tải lại trang và thử lại."

msgid "page.md5.box.download.affected_files"
msgstr "%(count)s trang bị ảnh hưởng"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Không hiển thị trong Libgen.rs Phi hư cấu"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Không hiển thị trong Libgen.rs Hư cấu"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Không thể thấy được ở Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Được đánh dấu là bị lỗi ở libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Thiếu từ Z-Library"

msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Được đánh dấu là “spam” trong Z-Library"

msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Được đánh dấu là “tệp xấu” trong Z-Library"

msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Không thể chuyển đổi tất cả các trang sang PDF"

msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Chạy exiftool thất bại trên tệp này"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Sách (Chưa biết)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Sách (Phi hư cấu)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Sách (Viễn tưởng)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Bài báo"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Tài liệu tiêu chuẩn"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Tạp chí"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Truyện tranh"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Bản nhạc"

msgid "common.md5_content_type_mapping.audiobook"
msgstr "Sách nói"

msgid "common.md5_content_type_mapping.other"
msgstr "Khác"

msgid "common.access_types_mapping.aa_download"
msgstr "Tải xuống từ Máy chủ Đối tác"

msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "Tải xuống bên ngoài"

msgid "common.access_types_mapping.external_borrow"
msgstr "Mượn bên ngoài"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Mượn bên ngoài (không in được)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Khám phá dữ liệu số"

msgid "common.access_types_mapping.torrents_available"
msgstr "Chứa trong torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Tiếng Trung"

msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

msgid "common.record_sources_mapping.uploads"
msgstr "Tải lên AA"

msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

msgid "common.record_sources_mapping.edsebk"
msgstr "Chỉ mục Sách điện tử EBSCOhost"

msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Dữ liệu số Séc"

msgid "common.record_sources_mapping.gbooks"
msgstr "Google Sách"

msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

msgid "common.record_sources_mapping.libby"
msgstr "Libby"

msgid "common.record_sources_mapping.rgb"
msgstr "Thư viện Quốc gia Nga"

msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

msgid "common.specific_search_fields.title"
msgstr "Tiêu đề"

msgid "common.specific_search_fields.author"
msgstr "Tác giả"

msgid "common.specific_search_fields.publisher"
msgstr "Nhà xuất bản"

msgid "common.specific_search_fields.edition_varia"
msgstr "Phiên bản"

msgid "common.specific_search_fields.year"
msgstr "Năm xuất bản"

msgid "common.specific_search_fields.original_filename"
msgstr "Tên tệp gốc"

msgid "common.specific_search_fields.description_comments"
msgstr "Mô tả và nhận xét dữ liệu số"

msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Tải xuống từ Máy chủ Đối tác tạm thời không khả dụng cho tệp này."

msgid "common.md5.servers.fast_partner"
msgstr "Máy chủ Đối tác Nhanh #%(number)s"

msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(được đề xuất)"

msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(không cần xác minh trình duyệt hoặc danh sách chờ)"

msgid "common.md5.servers.slow_partner"
msgstr "Máy chủ đối tác chậm #%(number)s"

msgid "common.md5.servers.faster_with_waitlist"
msgstr "(nhanh hơn một chút nhưng có danh sách chờ)"

msgid "common.md5.servers.slow_no_waitlist"
msgstr "(không có danh sách chờ, nhưng có thể rất chậm)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Phi hư cấu"

msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Viễn tưởng"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(cũng nhấp vào “NHẬN” ở trên cùng)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(nhấp vào “NHẬN” ở trên cùng)"

msgid "page.md5.box.download.libgen_ads"
msgstr "quảng cáo của họ được biết là chứa phần mềm độc hại, vì vậy nên sử dụng trình chặn quảng cáo hoặc không nhấp vào những quảng cáo"

msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Các tệp Nexus/STC có thể không đáng tin cậy để tải xuống)"

msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library trên Tor"

msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(cần trình duyệt Tor)"

msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Mượn từ Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(chỉ dành cho người khuyết tật không thể in)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI được liên kết với có thể không có sẵn trong Sci-Hub)"

msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

msgid "page.md5.box.download.collection"
msgstr "bộ sưu tập"

msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Tải xuống torrent hàng loạt"

msgid "page.md5.box.download.experts_only"
msgstr "(chỉ dành cho chuyên gia)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Tìm kiếm Anna’s Archive cho ISBN"

msgid "page.md5.box.download.other_isbn"
msgstr "Tìm kiếm các cơ sở dữ liệu khác cho ISBN"

msgid "page.md5.box.download.original_isbndb"
msgstr "Tìm bản ghi gốc trong ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Tìm kiếm Anna’s Archive cho ID Open Library"

msgid "page.md5.box.download.original_openlib"
msgstr "Tìm bản ghi gốc trong Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Tìm kiếm Anna’s Archive cho số OCLC (WorldCat)"

msgid "page.md5.box.download.original_oclc"
msgstr "Tìm ghi chép gốc trong WorldCat"

msgid "page.md5.box.download.aa_duxiu"
msgstr "Tìm kiếm Lưu Trữ của Anna cho số SSID DuXiu"

msgid "page.md5.box.download.original_duxiu"
msgstr "Tìm kiếm thủ công trên DuXiu"

msgid "page.md5.box.download.aa_cadal"
msgstr "Tìm kiếm Lưu Trữ của Anna cho số SSNO CADAL"

msgid "page.md5.box.download.original_cadal"
msgstr "Tìm bản ghi gốc trong CADAL"

msgid "page.md5.box.download.aa_dxid"
msgstr "Tìm kiếm Lưu Trữ của Anna cho số DXID DuXiu"

msgid "page.md5.box.download.edsebk"
msgstr "Chỉ mục Sách điện tử EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(không cần xác minh trình duyệt)"

msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Dữ liệu số Czech %(id)s}"

msgid "page.md5.top_row.gbooks"
msgstr "Google Sách %(id)s}"

msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

msgid "page.datasets.sources.metadata.header"
msgstr "Dữ liệu số"

msgid "page.md5.box.descr_title"
msgstr "mô tả"

msgid "page.md5.box.alternative_filename"
msgstr "Tên tệp thay thế"

msgid "page.md5.box.alternative_title"
msgstr "Tiêu đề thay thế"

msgid "page.md5.box.alternative_author"
msgstr "Tác giả thay thế"

msgid "page.md5.box.alternative_publisher"
msgstr "Nhà xuất bản thay thế"

msgid "page.md5.box.alternative_edition"
msgstr "Phiên bản thay thế"

msgid "page.md5.box.alternative_extension"
msgstr "Phần mở rộng thay thế"

msgid "page.md5.box.metadata_comments_title"
msgstr "bình luận của dữ liệu số"

msgid "page.md5.box.alternative_description"
msgstr "Mô tả thay thế"

msgid "page.md5.box.date_open_sourced_title"
msgstr "ngày mở mã nguồn"

msgid "page.md5.header.scihub"
msgstr "Tệp Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Internet Archive Mượn dạng số có kiểm soát (CDL) tệp “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Đây là bản ghi của một tệp từ Internet Archive, không phải là tệp có thể tải xuống trực tiếp. Bạn có thể thử mượn sách (liên kết bên dưới), hoặc sử dụng URL này khi <a %(a_request)s>đang yêu cầu tệp</a>."

msgid "page.md5.header.consider_upload"
msgstr "Nếu bạn có tệp này và nó chưa có trong Lưu trữ của Anna, hãy <a %(a_request)s>tải lên nó</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Bản ghi dữ liệu số ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Mở bản ghi dữ liệu số của Thư viện %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Số OCLC (WorldCat) bản ghi dữ liệu số %(id)s"

msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID bản ghi dữ liệu số %(id)s"

msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO bản ghi dữ liệu số %(id)s"

msgid "page.md5.header.meta_magzdb_id"
msgstr "Bản ghi dữ liệu số MagzDB ID %(id)s"

msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Bản ghi dữ liệu số Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Đây là một bản ghi dữ liệu số, không phải là tệp tải xuống được. Bạn có thể sử dụng URL này khi <a %(a_request)s>đang yêu cầu tệp</a>."

msgid "page.md5.text.linked_metadata"
msgstr "Dữ liệu số từ bản ghi liên kết"

msgid "page.md5.text.linked_metadata_openlib"
msgstr "Cải thiện dữ liệu số trên Open Library"

msgid "page.md5.warning.multiple_links"
msgstr "Cảnh báo: nhiều bản ghi liên kết:"

msgid "page.md5.header.improve_metadata"
msgstr "Cải thiện dữ liệu số"

msgid "page.md5.text.report_quality"
msgstr "Báo cáo chất lượng tệp"

msgid "page.search.results.download_time"
msgstr "Thời gian tải về"

msgid "page.md5.codes.url"
msgstr "Liên kết URL:"

msgid "page.md5.codes.website"
msgstr "Trang web:"

msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

msgid "page.md5.codes.aa_search"
msgstr "Tìm kiếm Anna’s Archive cho “%(name)s”"

msgid "page.md5.codes.code_explorer"
msgstr "Khám phá Mã:"

msgid "page.md5.codes.code_search"
msgstr "Xem trong Khám phá Mã “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Đọc thêm…"

msgid "page.md5.tabs.downloads"
msgstr "Tải xuống (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Mượn (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Khám phá dữ liệu số (%(count)s)"

msgid "page.md5.tabs.comments"
msgstr "Các bình luận (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Danh sách (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Thống kê (%(count)s)"

msgid "common.tech_details"
msgstr "Chi tiết kỹ thuật"

msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Tệp này có thể có vấn đề và đã bị ẩn khỏi thư viện nguồn.</span> Đôi khi điều này là do yêu cầu của chủ bản quyền, đôi khi là do có sẵn giải pháp thay thế tốt hơn, nhưng đôi khi đó là do vấn đề với chính tập tin đó. Tải xuống vẫn có thể ổn nhưng chúng tôi khuyên bạn trước tiên nên tìm kiếm tệp thay thế. Thêm chi tiết:"

msgid "page.md5.box.download.better_file"
msgstr "Phiên bản tốt hơn của tệp này có thể tìm thấy tại %(link)s"

msgid "page.md5.box.issues.text2"
msgstr "Nếu bạn vẫn muốn tải xuống tệp này, hãy nhớ chỉ sử dụng phần mềm đã cập nhật, đáng tin cậy để mở tệp."

msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Các bản tải xuống nhanh"

msgid "page.md5.box.download.header_fast_no_member"
msgstr "Trở thành <a %(a_membership)s>thành viên</a> để hỗ trợ việc bảo quản lâu dài những sách, bài nghiên cứu, v.v. Để thể hiện lòng biết ơn của chúng tôi đối với sự hỗ trợ của bạn, bạn sẽ có được bản tải xuống nhanh. ❤️"

msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Nếu bạn quyên góp vào tháng này, bạn sẽ nhận được <strong>gấp đôi</strong> số lượt tải xuống nhanh."

msgid "page.md5.box.download.header_fast_member"
msgstr "Bạn còn %(remaining)s lượt hôm nay. Cảm ơn vì đã là thành viên! ❤️"

msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "Bạn đã hết lượt tải xuống nhanh cho hôm nay."

msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "Bạn đã tải xuống tệp này gần đây. Liên kết vẫn còn hiệu lực trong một khoảng thời gian."

msgid "page.md5.box.download.option"
msgstr "Tùy chọn #%(num)d: %(link)s %(extra)s"

msgid "page.md5.box.download.no_redirect"
msgstr "(không chuyển hướng)"

msgid "page.md5.box.download.open_in_viewer"
msgstr "(mở trong trình xem)"

msgid "layout.index.header.banner.refer"
msgstr "Giới thiệu bạn bè, và cả bạn và bạn bè của bạn sẽ nhận được thêm %(percentage)s%% lượt tải nhanh!"

msgid "layout.index.header.learn_more"
msgstr "Tìm hiểu thêm…"

msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Các bản tải xuống chậm"

msgid "page.md5.box.download.trusted_partners"
msgstr "Từ các đối tác đáng tin cậy."

msgid "page.md5.box.download.slow_faq"
msgstr "Xem thêm thông tin trong <a %(a_slow)s>Câu hỏi thường gặp (FAQ)</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(có thể yêu cầu <a %(a_browser)s>xác minh trình duyệt</a> — lượt tải xuống không giới hạn!)"

msgid "page.md5.box.download.after_downloading"
msgstr "Sau khi tải xuống:"

msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Mở trong trình xem của chúng tôi"

msgid "page.md5.box.external_downloads"
msgstr "hiển thị tải xuống bên ngoài"

msgid "page.md5.box.download.header_external"
msgstr "Tải xuống bên ngoài"

msgid "page.md5.box.download.no_found"
msgstr "Không tìm thấy nội dung tải xuống nào."

msgid "page.md5.box.download.no_issues_notice"
msgstr "Tất cả các tùy chọn tải xuống đều có cùng một tệp và thường sẽ an toàn khi sử dụng. Tuy vật, hãy luôn thận trọng khi tải xuống tệp từ Internet, đặc biệt là từ các trang bên ngoài Anna's Archive. Ví dụ: hãy đảm bảo cập nhật thiết bị của bạn."

msgid "page.md5.box.download.dl_managers"
msgstr "Đối với các tệp lớn, chúng tôi khuyên bạn nên sử dụng trình quản lý tải xuống để tránh gián đoạn."

msgid "page.md5.box.download.dl_managers.links"
msgstr "Trình quản lý tải xuống được khuyến nghị: %(links)s"

msgid "page.md5.box.download.readers"
msgstr "Bạn sẽ cần một trình đọc ebook hoặc PDF để mở tệp, tùy thuộc vào định dạng tệp."

msgid "page.md5.box.download.readers.links"
msgstr "Trình đọc ebook được khuyến nghị: %(links)s"

msgid "page.md5.box.download.aa_viewer"
msgstr "Trình xem trực tuyến của Lưu Trữ của Anna"

msgid "page.md5.box.download.conversion"
msgstr "Sử dụng công cụ trực tuyến để chuyển đổi giữa các định dạng."

msgid "page.md5.box.download.conversion.links"
msgstr "Công cụ chuyển đổi được khuyến nghị: %(links)s"

msgid "page.md5.box.download.sendtokindle"
msgstr "Bạn có thể gửi cả tệp PDF và EPUB đến Kindle hoặc Kobo eReader của mình."

msgid "page.md5.box.download.sendtokindle.links"
msgstr "Công cụ được khuyến nghị: %(links)s"

msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon’s “Send to Kindle”"

msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz’s “Send to Kobo/Kindle”"

msgid "page.md5.box.download.support"
msgstr "Hỗ trợ tác giả và thư viện"

msgid "page.md5.box.download.support.authors"
msgstr "Nếu bạn thích sách này và có khả năng để mua nó, hãy cân nhắc mua bản gốc hoặc hỗ trợ trực tiếp cho các tác giả."

msgid "page.md5.box.download.support.libraries"
msgstr "Nếu sách này có sẵn tại thư viện địa phương của bạn, hãy cân nhắc mượn miễn phí ở đó."

msgid "page.md5.quality.header"
msgstr "Chất lượng tệp"

msgid "page.md5.quality.report"
msgstr "Giúp đỡ cộng đồng bằng cách báo cáo chất lượng của tệp này! 🙌"

msgid "page.md5.quality.report_issue"
msgstr "Báo cáo vấn đề tệp (%(count)s)"

msgid "page.md5.quality.great_quality"
msgstr "Chất lượng tệp tốt (%(count)s)"

msgid "page.md5.quality.add_comment"
msgstr "Thêm bình luận (%(count)s)"

msgid "page.md5.quality.what_is_wrong"
msgstr "Cái gì sai với tệp này?"

msgid "page.md5.quality.copyright"
msgstr "Xin vui lòng sử dụng <a %(a_copyright)s>biểu mẫu khiếu nại DMCA / Bản quyền</a>."

msgid "page.md5.quality.describe_the_issue"
msgstr "Mô tả vấn đề (bắt buộc)"

msgid "page.md5.quality.issue_description"
msgstr "Mô tả vấn đề"

msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 của phiên bản tốt hơn của tệp này (nếu có)."

msgid "page.md5.quality.better_md5.text2"
msgstr "Điền vào đây nếu có tệp khác tương tự với tệp này (cùng phiên bản, cùng định dạng tệp nếu bạn có thể tìm thấy), mà mọi người nên sử dụng thay vì tệp này. Nếu bạn biết phiên bản tốt hơn của tệp này ngoài Anna’s Archive, thì xin vui lòng <a %(a_upload)s>tải lên</a>."

msgid "page.md5.quality.better_md5.line1"
msgstr "Bạn có thể tìm được md5 từ URL, vân vân"

msgid "page.md5.quality.submit_report"
msgstr "Gửi báo cáo"

msgid "page.md5.quality.improve_the_metadata"
msgstr "Tìm hiểu cách <a %(a_metadata)s>cải thiện dữ liệu số</a> cho tệp này."

msgid "page.md5.quality.report_thanks"
msgstr "Cảm ơn bạn đã gửi báo cáo. Báo cáo sẽ được hiển thị trên trang này và sẽ cũng được xem xét thủ công bởi Anna (cho đến khi chúng tôi có hệ thống kiểm duyệt phù hợp)."

msgid "page.md5.quality.report_error"
msgstr "Đã xảy ra lỗi. Vui lòng tải lại trang và thử lại."

msgid "page.md5.quality.great.summary"
msgstr "Nếu tệp này có chất lượng tốt, bạn có thể thảo luận bất cứ điều gì về nó tại đây! Nếu không, vui lòng sử dụng nút “Báo cáo vấn đề tệp”."

msgid "page.md5.quality.loved_the_book"
msgstr "Tôi rất thích cuốn sách này!"

msgid "page.md5.quality.submit_comment"
msgstr "Để lại bình luận"

msgid "common.english_only"
msgstr "Văn bản bên dưới tiếp tục bằng tiếng Anh."

msgid "page.md5.text.stats.total_downloads"
msgstr "Tổng số lượt tải xuống: %(total)s"

msgid "page.md5.text.md5_info.text1"
msgstr "“file MD5” là một hàm băm được tính toán từ nội dung tệp, và khá độc đáo dựa trên nội dung đó. Tất cả các thư viện bóng mà chúng tôi đã lập chỉ mục ở đây chủ yếu sử dụng MD5 để xác định tệp."

msgid "page.md5.text.md5_info.text2"
msgstr "Một tệp có thể xuất hiện trong nhiều thư viện bón—g. Để biết thông tin về các datasets khác nhau mà chúng tôi đã biên soạn, hãy xem <a %(a_datasets)s>trang Datasets</a>."

msgid "page.md5.text.ia_info.text1"
msgstr "Đây là một tệp được quản lý bởi thư viện <a %(a_ia)s>Mượn Kỹ Thuật Số Có Kiểm Soát (CDL) của IA</a> và được Anna’s Archive lập chỉ mục để tìm kiếm. Để biết thông tin về các datasets khác nhau mà chúng tôi đã biên soạn, hãy xem <a %(a_datasets)s>trang Datasets</a>."

msgid "page.md5.text.file_info.text1"
msgstr "Để biết thông tin về tệp cụ thể này, hãy xem <a %(a_href)s>tệp JSON</a> của nó."

msgid "page.aarecord_issue.title"
msgstr "🔥 Sự cố khi tải trang này"

msgid "page.aarecord_issue.text"
msgstr "Xin vui lòng làm mới để thử lại. <a %(a_contact)s>Liên hệ với chúng tôi</a> nếu sự cố vẫn tiếp diễn trong nhiều giờ."

msgid "page.md5.invalid.header"
msgstr "Không tìm thấy"

msgid "page.md5.invalid.text"
msgstr "\"%(md5_input)s\" không thể được tìm thấy trong kho dữ liệu của chúng tôi l."

msgid "page.login.title"
msgstr "Đăng nhập / Đăng ký"

msgid "page.browserverification.header"
msgstr "Xác minh trình duyệt"

msgid "page.login.text1"
msgstr "Để ngăn chặn các chương trình spam tạo nhiều tài khoản, trước tiên chúng tôi cần xác minh trình duyệt của bạn."

msgid "page.login.text2"
msgstr "Nếu bạn bị mắc kẹt trong vòng lặp vô hạn, chúng tôi khuyên bạn nên tải <a %(a_privacypass)s>Privacy Pass</a>."

msgid "page.login.text3"
msgstr "Nó cũng có thể giúp tắt các trình chặn quảng cáo và các tiện ích mở rộng trình duyệt khác."

msgid "page.codes.title"
msgstr "Mã"

msgid "page.codes.heading"
msgstr "Khám phá mã"

#, fuzzy
msgid "page.codes.intro"
msgstr "Khám phá các mã mà các bản ghi được gắn thẻ với, theo tiền tố. Cột “bản ghi” hiển thị số lượng bản ghi được gắn thẻ với mã có tiền tố đã cho, như được thấy trong công cụ tìm kiếm (bao gồm cả các bản ghi chỉ có dữ liệu số). Cột “mã” hiển thị có bao nhiêu mã thực sự có tiền tố đã cho."

msgid "page.codes.why_cloudflare"
msgstr "Trang này có thể mất một lúc để tạo, đó là lý do tại sao nó yêu cầu captcha của Cloudflare. <a %(a_donate)s>Thành viên</a> có thể bỏ qua captcha."

msgid "page.codes.dont_scrape"
msgstr "Xin vui lòng đừng quét các trang này. Thay vào đó, chúng tôi khuyến nghị <a %(a_import)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi, và chạy <a %(a_software)s>mã nguồn mở</a> của chúng tôi. Dữ liệu thô có thể được khám phá thủ công thông qua các tệp JSON như <a %(a_json_file)s>tệp này</a>."

msgid "page.codes.prefix"
msgstr "Tiền tố"

msgid "common.form.go"
msgstr "Đi"

msgid "common.form.reset"
msgstr "Đặt lại"

msgid "page.codes.search_archive_start"
msgstr "Tìm kiếm Lưu trữ của Anna"

msgid "page.codes.bad_unicode"
msgstr "Cảnh báo: mã có các ký tự Unicode không chính xác và có thể hoạt động không đúng trong nhiều tình huống khác nhau. Dữ liệu nhị phân thô có thể được giải mã từ biểu diễn base64 trong URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Tiền tố mã đã biết “%(key)s”"

msgid "page.codes.code_prefix"
msgstr "Tiền tố"

msgid "page.codes.code_label"
msgstr "Nhãn"

msgid "page.codes.code_description"
msgstr "Mô tả"

msgid "page.codes.code_url"
msgstr "Liên kết URL cho một mã cụ thể"

msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sẽ được thay thế bằng giá trị của mã"

msgid "page.codes.generic_url"
msgstr "Liên kết URL chung"

msgid "page.codes.code_website"
msgstr "Trang web"

msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s bản ghi khớp với “%(prefix_label)s”"

msgid "page.codes.url_link"
msgstr "Liên kết URL cho mã cụ thể: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Thêm nữa…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Mã bắt đầu với “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Mục lục của"

msgid "page.codes.records_prefix"
msgstr "bản ghi"

msgid "page.codes.records_codes"
msgstr "mã"

msgid "page.codes.fewer_than"
msgstr "Ít hơn %(count)s bản ghi"

msgid "page.contact.dmca.form"
msgstr "Đối với các khiếu nại DMCA / bản quyền, sử dụng <a %(a_copyright)s>biểu mẫu này</a>."

msgid "page.contact.dmca.delete"
msgstr "Bất kỳ cách liên hệ nào khác về khiếu nại bản quyền sẽ tự động bị xóa."

msgid "page.contact.checkboxes.text1"
msgstr "Chúng tôi rất hoan nghênh các phản hồi và câu hỏi của bạn!"

msgid "page.contact.checkboxes.text2"
msgstr "Tuy nhiên, do lượng thư rác và email vô nghĩa mà chúng tôi nhận được, vui lòng đánh dấu vào các ô để xác nhận bạn hiểu các điều kiện này khi liên hệ với chúng tôi."

msgid "page.contact.checkboxes.copyright"
msgstr "Các khiếu nại bản quyền gửi đến email này sẽ bị bỏ qua; thay vào đó xin hãy sử dụng biểu mẫu."

msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Các máy chủ đối tác không khả dụng do đóng cửa dịch vụ lưu trữ. Chúng sẽ sớm hoạt động trở lại."

msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Các gói thành viên sẽ được kéo dài thêm tương ứng."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Đừng gửi email cho chúng tôi để <a %(a_request)s>yêu cầu sách</a><br>hoặc tải lên nhỏ (<10k) <a %(a_upload)s>tệp tin</a>."

msgid "page.donate.please_include"
msgstr "Khi hỏi về tài khoản hoặc quyên góp, hãy thêm ID tài khoản của bạn, ảnh chụp màn hình, biên lai, càng nhiều thông tin càng tốt. Chúng tôi chỉ kiểm tra email mỗi 1-2 tuần, vì vậy không bao gồm thông tin này sẽ làm chậm bất kỳ giải pháp nào."

msgid "page.contact.checkboxes.show_email_button"
msgstr "Hiển thị email"

msgid "page.copyright.title"
msgstr "Biểu mẫu khiếu nại DMCA / Bản quyền"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Nếu bạn có khiếu nại về DMCA hoặc bản quyền khác, vui lòng điền vào biểu mẫu này càng chính xác càng tốt. Nếu bạn gặp bất kỳ vấn đề nào, vui lòng liên hệ với chúng tôi tại địa chỉ DMCA chuyên dụng: %(email)s. Lưu ý rằng các khiếu nại gửi qua email đến địa chỉ này sẽ không được xử lý, nó chỉ dành cho các câu hỏi. Vui lòng sử dụng biểu mẫu dưới đây để gửi khiếu nại của bạn."

msgid "page.copyright.form.aa_urls"
msgstr "Các liên kết URL trên Anna’s Archive (bắt buộc). Một URL mỗi dòng. Vui lòng chỉ bao gồm các URL mô tả chính xác cùng một phiên bản của một cuốn sách. Nếu bạn muốn khiếu nại cho nhiều cuốn sách hoặc nhiều phiên bản, vui lòng gửi biểu mẫu này nhiều lần."

msgid "page.copyright.form.aa_urls.note"
msgstr "Các khiếu nại gộp nhiều cuốn sách hoặc phiên bản sẽ bị từ chối."

msgid "page.copyright.form.name"
msgstr "Tên của bạn (bắt buộc)"

msgid "page.copyright.form.address"
msgstr "Địa chỉ (bắt buộc)"

msgid "page.copyright.form.phone"
msgstr "Số điện thoại (bắt buộc)"

msgid "page.copyright.form.email"
msgstr "E-mail (bắt buộc)"

msgid "page.copyright.form.description"
msgstr "Mô tả rõ ràng về tài liệu nguồn (bắt buộc)"

msgid "page.copyright.form.isbns"
msgstr "ISBN của tài liệu nguồn (nếu có). Một ISBN mỗi dòng. Vui lòng chỉ bao gồm những ISBN khớp chính xác với phiên bản mà bạn đang báo cáo khiếu nại bản quyền."

msgid "page.copyright.form.openlib_urls"
msgstr "Các liên kết URL <a %(a_openlib)s>Open Library</a> của tài liệu nguồn, một liên kết URL mỗi dòng. Vui lòng dành chút thời gian để tìm kiếm tài liệu nguồn của bạn trên Open Library. Điều này sẽ giúp chúng tôi xác minh khiếu nại của bạn."

msgid "page.copyright.form.external_urls"
msgstr "URL đến tài liệu nguồn, một URL mỗi dòng (bắt buộc). Vui lòng bao gồm càng nhiều càng tốt, để giúp chúng tôi xác minh khiếu nại của bạn (ví dụ: Amazon, WorldCat, Google Books, DOI)."

msgid "page.copyright.form.statement"
msgstr "Tuyên bố và chữ ký (bắt buộc)"

msgid "page.copyright.form.submit_claim"
msgstr "Gửi khiếu nại"

msgid "page.copyright.form.on_success"
msgstr "✅ Cảm ơn bạn đã gửi khiếu nại bản quyền. Chúng tôi sẽ xem xét nó sớm nhất có thể. Vui lòng tải lại trang để gửi một khiếu nại khác."

msgid "page.copyright.form.on_failure"
msgstr "❌ Đã xảy ra lỗi. Vui lòng tải lại trang và thử lại."

msgid "page.datasets.title"
msgstr "Bộ dữ liệu"

msgid "page.datasets.common.intro"
msgstr "Nếu bạn quan tâm đến việc sao lưu bộ dữ liệu này cho mục đích <a %(a_archival)s>lưu trữ</a> hoặc <a %(a_llm)s>đào tạo LLM</a>, xin vui lòng liên hệ với chúng tôi."

msgid "page.datasets.intro.text2"
msgstr "Sứ mệnh của chúng tôi là lưu trữ tất cả các cuốn sách trên thế giới (cũng như các bài báo, tạp chí, v.v.) và làm cho chúng dễ dàng tiếp cận. Chúng tôi tin rằng tất cả các cuốn sách nên được phản chiếu rộng rãi để đảm bảo tính dự phòng và khả năng phục hồi. Đây là lý do tại sao chúng tôi đang tập hợp các tệp từ nhiều nguồn khác nhau. Một số nguồn hoàn toàn mở và có thể được phản chiếu hàng loạt (chẳng hạn như Sci-Hub). Những nguồn khác thì đóng và bảo vệ, vì vậy chúng tôi cố gắng thu thập chúng để “giải phóng” sách của họ. Những nguồn khác thì nằm ở giữa."

msgid "page.datasets.intro.text3"
msgstr "Tất cả dữ liệu của chúng tôi có thể được <a %(a_torrents)s>tải qua torrent</a> và tất cả dữ liệu số của chúng tôi có thể được <a %(a_anna_software)s>tạo</a> hoặc <a %(a_elasticsearch)s>tải xuống</a> dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB. Dữ liệu thô có thể được khám phá thủ công thông qua các tệp JSON như <a %(a_dbrecord)s>này</a>."

msgid "page.datasets.overview.title"
msgstr "Tổng quan"

msgid "page.datasets.overview.text1"
msgstr "Dưới đây là tổng quan nhanh về các nguồn của các tệp trên Anna’s Archive."

msgid "page.datasets.overview.source.header"
msgstr "Nguồn"

msgid "page.datasets.overview.size.header"
msgstr "Kích thước"

msgid "page.datasets.overview.mirrored.header"
msgstr "%% được phản chiếu bởi AA / có sẵn qua các torrent"

msgid "page.datasets.overview.mirrored.clarification"
msgstr "Tỷ lệ phần trăm số lượng tệp"

msgid "page.datasets.overview.last_updated.header"
msgstr "Cập nhật lần cuối"

msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Phi hư cấu và Hư cấu"

msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s tệp"

msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Qua Libgen.li “scimag”"

msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: đã bị đóng băng từ năm 2021; hầu hết có sẵn qua torrents"

msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: bổ sung nhỏ kể từ đó</div>"

msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Loại trừ “scimag”"

msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrents hư cấu đang bị chậm (mặc dù ID ~4-6M chưa được tải qua torrents vì chúng trùng lặp với torrents Zlib của chúng tôi)."

msgid "page.datasets.zlibzh.searchable"
msgstr "Bộ sưu tập “Tiếng Trung” trong Z-Library dường như giống với bộ sưu tập DuXiu của chúng tôi, nhưng có MD5 khác nhau. Chúng tôi loại trừ các tệp này khỏi torrents để tránh trùng lặp, nhưng vẫn hiển thị chúng trong chỉ mục tìm kiếm của chúng tôi."

msgid "common.record_sources_mapping.iacdl"
msgstr "IA Cho mượn kỹ thuật số có kiểm soát"

msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ tệp có thể tìm kiếm được."

msgid "page.datasets.overview.total"
msgstr "Tổng cộng"

msgid "page.datasets.overview.excluding_duplicates"
msgstr "Loại trừ các bản sao"

msgid "page.datasets.overview.text4"
msgstr "Vì các thư viện bóng thường đồng bộ dữ liệu từ nhau, nên có sự trùng lặp đáng kể giữa các thư viện. Đó là lý do tại sao các con số không cộng lại thành tổng."

msgid "page.datasets.overview.text5"
msgstr "Tỷ lệ phần trăm “được sao lưu và chia sẻ bởi Anna’s Archive” cho thấy có bao nhiêu tệp chúng tôi tự bản sao. Chúng tôi gieo những tệp này hàng loạt qua các torrent và làm cho chúng có sẵn để tải trực tiếp qua các trang web đối tác."

msgid "page.datasets.source_libraries.title"
msgstr "Thư viện nguồn"

msgid "page.datasets.source_libraries.text1"
msgstr "Một số thư viện nguồn khuyến khích chia sẻ hàng loạt dữ liệu của họ qua các torrent, trong khi những thư viện khác không sẵn sàng chia sẻ bộ sưu tập của họ. Trong trường hợp sau, Anna’s Archive cố gắng thu thập bộ sưu tập của họ và làm cho chúng có sẵn (xem trang <a %(a_torrents)s>Torrents</a> của chúng tôi). Cũng có những tình huống trung gian, ví dụ, nơi các thư viện nguồn sẵn sàng chia sẻ, nhưng không có đủ nguồn lực để làm điều đó. Trong những trường hợp đó, chúng tôi cũng cố gắng giúp đỡ."

msgid "page.datasets.source_libraries.text2"
msgstr "Dưới đây là tổng quan về cách chúng tôi giao tiếp với các thư viện nguồn khác nhau."

msgid "page.datasets.sources.source.header"
msgstr "Nguồn"

msgid "page.datasets.sources.files.header"
msgstr "Tệp tin"

msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Bản sao cơ sở dữ liệu HTTP</a> hàng ngày"

msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrent tự động cho <a %(nonfiction)s>Phi hư cấu</a> và <a %(fiction)s>Hư cấu</a>"

msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(covers)s>torrent bìa sách</a>"

msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub đã ngừng cập nhật tệp mới từ năm 2021."

msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Các bản sao dữ liệu số có sẵn <a %(scihub1)s>tại đây</a> và <a %(scihub2)s>tại đây</a>, cũng như là một phần của <a %(libgenli)s>cơ sở dữ liệu Libgen.li</a> (mà chúng tôi sử dụng)"

msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrent dữ liệu có sẵn <a %(scihub1)s>tại đây</a>, <a %(scihub2)s>tại đây</a>, và <a %(libgenli)s>tại đây</a>"

msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Một số tệp mới đang <a %(libgenrs)s>được</a> <a %(libgenli)s>thêm</a> vào “scimag” của Libgen, nhưng không đủ để tạo torrent mới"

msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Bản sao cơ sở dữ liệu HTTP</a> hàng quý"

msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrent Phi hư cấu được chia sẻ với Libgen.rs (và được bản sao <a %(libgenli)s>tại đây</a>)."

msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Lưu trữ của Anna và Libgen.li cùng quản lý các bộ sưu tập <a %(comics)s>truyện tranh</a>, <a %(magazines)s>tạp chí</a>, <a %(standarts)s>tài liệu tiêu chuẩn</a>, và <a %(fiction)s>tiểu thuyết (tách ra từ Libgen.rs)</a>."

msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Bộ sưu tập “fiction_rus” (tiểu thuyết Nga) của họ không có torrent riêng, nhưng được bao phủ bởi các torrent từ những người khác, và chúng tôi giữ một <a %(fiction_rus)s>bản sao</a>."

msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Lưu trữ của Anna và Z-Library cùng quản lý một bộ sưu tập <a %(metadata)s>dữ liệu số Z-Library</a> và <a %(files)s>tệp Z-Library</a>"

msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Một số metadata có thể được truy cập từ <a %(openlib)s>bản sao cơ sở dữ liệu Open Library</a>, nhưng chúng không phủ hoàn toàn bộ sưu tập IA"

msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Không có bản sao dữ liệu số dễ dàng truy cập cho toàn bộ bộ sưu tập"

msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(ia)s>dữ liệu số của IA</a>"

msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Tệp chỉ có sẵn để mượn trong một phạm vi hạn chế, với các hạn chế truy cập khác nhau"

msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(ia)s>tệp IA</a>"

msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Các cơ sở dữ liệu số khác nhau rải rác trên internet Trung Quốc; mặc dù thường là các cơ sở dữ liệu trả phí"

msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Không có các bản dữ liệu số dễ dàng truy cập cho toàn bộ bộ sưu tập."

msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna’s Archive quản lý một bộ sưu tập <a %(duxiu)s>dữ liệu số DuXiu</a>"

msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Các cơ sở dữ liệu tệp khác nhau được rải rác trên internet Trung Quốc; mặc dù thường là các cơ sở dữ liệu trả phí"

msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Hầu hết các tệp chỉ có thể truy cập bằng tài khoản BaiduYun cao cấp; tốc độ tải xuống chậm."

msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna’s Archive có quản lý một bộ sưu tập <a %(duxiu)s>tệp DuXiu</a>"

msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Các nguồn nhỏ hơn hoặc một lần. Chúng tôi khuyến khích mọi người tải lên các thư viện bóng khác trước, nhưng đôi khi mọi người có các bộ sưu tập quá lớn để người khác sắp xếp, mặc dù không đủ lớn để xứng đáng có danh mục riêng."

msgid "page.datasets.metadata_only_sources.title"
msgstr "Nguồn chỉ có dữ liệu số"

msgid "page.datasets.metadata_only_sources.text1"
msgstr "Chúng tôi cũng làm phong phú thêm bộ sưu tập của mình với các nguồn chỉ có dữ liệu số, mà chúng tôi có thể khớp với các tệp tin, ví dụ như sử dụng số ISBN hoặc các trường khác. Dưới đây là tổng quan về những nguồn đó. Một lần nữa, một số nguồn này hoàn toàn mở, trong khi với những nguồn khác chúng tôi phải thu thập dữ liệu."

msgid "page.faq.metadata.inspiration"
msgstr "Nguồn cảm hứng của chúng tôi cho việc thu thập dữ liệu số là mục tiêu của Aaron Swartz về “một trang web cho mỗi cuốn sách từng được xuất bản”, mà anh ấy đã tạo ra <a %(a_openlib)s>Open Library</a>. Dự án đó đã làm rất tốt, nhưng vị trí độc đáo của chúng tôi cho phép chúng tôi có được dữ liệu số mà họ không thể có. Một nguồn cảm hứng khác là mong muốn của chúng tôi biết <a %(a_blog)s>có bao nhiêu cuốn sách trên thế giới</a>, để chúng tôi có thể tính toán có bao nhiêu cuốn sách chúng tôi còn phải cứu."

msgid "page.datasets.metadata_only_sources.text2"
msgstr "Lưu ý rằng trong tìm kiếm dữ liệu số, chúng tôi hiển thị các bản ghi gốc. Chúng tôi không thực hiện bất kỳ việc hợp nhất bản ghi nào."

msgid "page.datasets.sources.last_updated.header"
msgstr "Cập nhật lần cuối"

msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Các bản dữ liệu <a %(dbdumps)s>hàng tháng</a>"

msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Không có sẵn trực tiếp với số lượng lớn, được bảo vệ chống lại việc quét dữ liệu"

msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna’s Archive quản lý một bộ sưu tập <a %(worldcat)s>dữ liệu số OCLC (WorldCat)</a>"

msgid "page.datasets.unified_database.title"
msgstr "Cơ sở dữ liệu hợp nhất"

msgid "page.datasets.unified_database.text1"
msgstr "Chúng tôi kết hợp tất cả các nguồn trên vào một cơ sở dữ liệu hợp nhất mà chúng tôi sử dụng để phục vụ trang web này. Cơ sở dữ liệu hợp nhất này không có sẵn trực tiếp, nhưng vì Anna’s Archive hoàn toàn mã nguồn mở, nó có thể được <a %(a_generated)s>tạo ra</a> hoặc <a %(a_downloaded)s>tải xuống</a> khá dễ dàng dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB. Các script trên trang đó sẽ tự động tải xuống tất cả dữ liệu số cần thiết từ các nguồn đã đề cập ở trên."

msgid "page.datasets.unified_database.text2"
msgstr "Nếu bạn muốn khám phá dữ liệu của chúng tôi trước khi chạy các script đó trên máy bạn, bạn có thể xem các tệp JSON của chúng tôi, liên kết thêm đến các tệp JSON khác. <a %(a_json)s>Tệp này</a> là một điểm khởi đầu tốt."

msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

msgid "page.datasets.duxiu.see_blog_post"
msgstr "Được điều chỉnh từ <a %(a_href)s>bài viết blog</a> của chúng tôi."

msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> là một cơ sở dữ liệu khổng lồ về sách đã được quét, được tạo ra bởi <a %(superstar_link)s>Nhóm Thư Viện Kỹ Thuật Số SuperStar</a>. Phần lớn là sách học thuật, được quét để cung cấp kỹ thuật số cho các trường đại học và thư viện. Đối với khán giả nói tiếng Anh, <a %(princeton_link)s>Princeton</a> và <a %(uw_link)s>Đại học Washington</a> có những tổng quan tốt. Cũng có một bài viết xuất sắc cung cấp thêm thông tin: <a %(article_link)s>“Số hóa sách tiếng Trung: Nghiên Cứu Trường Hợp về Công Cụ Tìm Kiếm Duxiu Scholar”</a>."

msgid "page.datasets.duxiu.description2"
msgstr "Những cuốn sách từ DuXiu đã bị sao chép lậu trên internet Trung Quốc từ lâu. Thông thường, chúng được bán với giá chưa đến một đô la bởi các nhà bán lẻ. Chúng thường được phân phối bằng cách sử dụng dịch vụ tương đương với Google Drive của Trung Quốc, dịch vụ này thường bị hack để có thêm dung lượng lưu trữ. Một số chi tiết kỹ thuật có thể được tìm thấy <a %(link1)s>tại đây</a> và <a %(link2)s>tại đây</a>."

msgid "page.datasets.duxiu.description3"
msgstr "Mặc dù những cuốn sách này đã được xuất bản công khai một phần, việc thu gom chúng trên quy mô lớn là một trở ngại lớn. Chúng tôi đặt ưu tiên rất cao cho việc này trong danh sách việc cần làm, và đã bỏ công sức toàn thời gian cho nó trong vòng nhiều tháng. Thế nhưng, vào cuối năm 2023, một tình nguyện viên tuyệt vời và đặc biệt xuất sắc đã liên lạc với chúng tôi rằng họ đã hoàn thành công việc này - một phí tổn cực kì tốn kém. Người này đã chia sẻ với chúng tôi toàn bộ bộ sưu tập của họ và không đòi hỏi lại bất cứ gì ngoài sự đảm bảo rằng chúng sẽ được bảo tồn lâu dài. Thật sự vô cùng kinh ngạc."

msgid "page.datasets.common.resources"
msgstr "Tài nguyên"

msgid "page.datasets.common.total_files"
msgstr "Tổng số tệp: %(count)s"

msgid "page.datasets.common.total_filesize"
msgstr "Tổng kích thước tệp: %(size)s"

msgid "page.datasets.common.mirrored_file_count"
msgstr "Các tệp được bản sao bởi Anna’s Archive: %(count)s (%(percent)s%%)"

msgid "page.datasets.common.last_updated"
msgstr "Cập nhật lần cuối: %(date)s"

msgid "page.datasets.common.aa_torrents"
msgstr "Những torrent bởi Lưu Trữ của Anna"

msgid "page.datasets.common.aa_example_record"
msgstr "Bản ghi ví dụ trên Lưu Trữ của Anna"

msgid "page.datasets.duxiu.blog_post"
msgstr "Bài viết trên blog của chúng tôi về dữ liệu này"

msgid "page.datasets.common.import_scripts"
msgstr "Tập lệnh để nhập dữ liệu số"

msgid "page.datasets.common.aac"
msgstr "Định dạng Container Lưu Trư của Anna"

msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Thông tin thêm từ các tình nguyện viên của chúng tôi (ghi chú thô):"

msgid "page.datasets.ia.title"
msgstr "IA Mượn Kỹ Thuật Số Có Kiểm Soát (CDL)"

msgid "page.datasets.ia.description"
msgstr "Bộ dữ liệu này liên quan chặt chẽ đến <a %(a_datasets_openlib)s>bộ dữ liệu Open Library</a>. Nó chứa một bản quét tất cả dữ liệu số và một phần lớn các tệp từ Thư viện Cho Mượn Kỹ Thuật Số Có Kiểm Soát (CDL) của IA. Các bản cập nhật được phát hành theo <a %(a_aac)s>định dạng Container Lưu Trư của Anna</a>."

msgid "page.datasets.ia.description2"
msgstr "Các bản ghi này được tham chiếu trực tiếp từ bộ dữ liệu Open Library, nhưng cũng chứa các bản ghi không có trong Open Library. Chúng tôi cũng có một số tệp dữ liệu được cộng đồng thu thập trong nhiều năm qua."

msgid "page.datasets.ia.description3"
msgstr "Bộ sưu tập bao gồm hai phần. Bạn cần cả hai phần để có được tất cả dữ liệu (trừ các torrent đã bị thay thế, được gạch bỏ trên trang torrent)."

msgid "page.datasets.ia.part1"
msgstr "bản phát hành đầu tiên của chúng tôi, trước khi chúng tôi chuẩn hóa theo định dạng <a %(a_aac)s>Container Lưu Trư của Anna (AAC)</a>. Chứa dữ liệu số (dưới dạng json và xml), các tệp pdf (từ các hệ thống cho mượn sách kỹ thuật số acsm và lcpdf), và hình thu nhỏ của bìa sách."

msgid "page.datasets.ia.part2"
msgstr "các bản phát hành mới gia tăng, sử dụng AAC. Chỉ chứa dữ liệu số với dấu thời gian sau ngày 2023-01-01, vì phần còn lại đã được bao phủ bởi “ia”. Cũng bao gồm tất cả các tệp pdf, lần này từ các hệ thống cho mượn sách acsm và “bookreader” (trình đọc web của IA). Mặc dù tên không hoàn toàn chính xác, chúng tôi vẫn đưa các tệp bookreader vào bộ sưu tập ia2_acsmpdf_files, vì chúng không trùng lặp."

msgid "page.datasets.common.main_website"
msgstr "Trang web chính %(source)s"

msgid "page.datasets.ia.ia_lending"
msgstr "Thư viện Cho mượn Sách Kỹ thuật số"

msgid "page.datasets.common.metadata_docs"
msgstr "Tài liệu dữ liệu số (hầu hết các trường)"

msgid "page.datasets.isbn_ranges.title"
msgstr "Thông tin quốc gia ISBN"

msgid "page.datasets.isbn_ranges.text1"
msgstr "Cơ quan ISBN Quốc tế thường xuyên phát hành các phạm vi mà họ đã phân bổ cho các cơ quan ISBN quốc gia. Từ đó, chúng tôi có thể xác định quốc gia, khu vực hoặc nhóm ngôn ngữ mà ISBN này thuộc về. Hiện tại, chúng tôi sử dụng dữ liệu này gián tiếp, thông qua thư viện Python <a %(a_isbnlib)s>isbnlib</a>."

msgid "page.datasets.isbn_ranges.resources"
msgstr "Tài nguyên"

msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Cập nhật lần cuối: %(isbn_country_date)s (%(link)s)"

msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Trang web ISBN"

msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Dữ liệu số"

msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

msgid "page.datasets.libgen_li.description1"
msgstr "Để biết câu chuyện nền của các nhánh Library Genesis khác nhau, xem trang cho <a %(a_libgen_rs)s>Libgen.rs</a>."

msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li chứa hầu hết nội dung và dữ liệu số giống như Libgen.rs, nhưng có một số bộ sưu tập bổ sung, cụ thể là truyện tranh, tạp chí và tài liệu tiêu chuẩn. Nó cũng đã tích hợp <a %(a_scihub)s>Sci-Hub</a> vào dữ liệu số và công cụ tìm kiếm của mình, đó là những gì chúng tôi sử dụng cho cơ sở dữ liệu của chúng tôi."

msgid "page.datasets.libgen_li.description3"
msgstr "Dữ liệu số cho thư viện này có sẵn miễn phí <a %(a_libgen_li)s>tại libgen.li</a>. Tuy nhiên, máy chủ này chậm và không hỗ trợ tiếp tục kết nối bị gián đoạn. Các tệp tương tự cũng có sẵn trên <a %(a_ftp)s>máy chủ FTP</a>, hoạt động tốt hơn."

msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Những torrent được có sẵn cho hầu hết các nội dung bổ sung, đặc biệt là torrents cho truyện tranh, tạp chí và tài liệu tiêu chuẩn đã được phát hành hợp tác với Lưu trữ của Anna."

msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Bộ sưu tập tiểu thuyết có các torrent riêng của nó (được tách ra từ <a %(a_href)s>Libgen.rs</a>) bắt đầu từ %(start)s."

msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Theo quản trị viên của Libgen.li, bộ sưu tập “fiction_rus” (tiểu thuyết Nga) nên được bao phủ bởi các torrent phát hành thường xuyên từ <a %(a_booktracker)s>booktracker.org</a>, đặc biệt là các torrent <a %(a_flibusta)s>flibusta</a> và <a %(a_librusec)s>lib.rus.ec</a> (mà chúng tôi sao lưu <a %(a_torrents)s>tại đây</a>, mặc dù chúng tôi chưa xác định được torrent nào tương ứng với tệp nào)."

msgid "page.datasets.libgen_li.description4.stats"
msgstr "Thống kê cho tất cả các bộ sưu tập có thể được tìm thấy <a %(a_href)s>trên trang web của libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Sách phi hư cấu cũng dường như đã có sự khác biệt, nhưng không có các torrent mới. Có vẻ như điều này đã xảy ra từ đầu năm 2022, mặc dù chúng tôi chưa xác minh điều này."

msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Một số phạm vi không có torrent (chẳng hạn như phạm vi tiểu thuyết f_3463000 đến f_4260000) có khả năng là các tệp của Z-Library (hoặc các tệp trùng lặp khác), mặc dù chúng tôi có thể muốn thực hiện một số loại bỏ trùng lặp và tạo torrent cho các tệp duy nhất của lgli trong các phạm vi này."

msgid "page.datasets.libgen_li.description5"
msgstr "Lưu ý rằng các tệp torrent đề cập đến “libgen.is” là bản sao rõ ràng của <a %(a_libgen)s>Libgen.rs</a> (“.is” là một miền khác được sử dụng bởi Libgen.rs)."

msgid "page.datasets.libgen_li.description6"
msgstr "Một tài nguyên hữu ích khi sử dụng dữ liệu số là <a %(a_href)s>trang này</a>."

msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrent tiểu thuyết trên Anna’s Archive"

msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrent truyện tranh trên Anna’s Archive"

msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrent tạp chí trên Anna’s Archive"

msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Các torrent tài liệu tiêu chuẩn trên Lưu trữ của Anna"

msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Các torrent tiểu thuyết Nga trên Lưu trữ của Anna"

msgid "page.datasets.libgen_li.link_metadata"
msgstr "Dữ liệu số"

msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Dữ liệu số qua FTP"

msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Thông tin trường dữ liệu số"

msgid "page.datasets.libgen_li.mirrors"
msgstr "Bản sao của các torrent khác (và các torrent tiểu thuyết và truyện tranh độc đáo)"

msgid "page.datasets.libgen_li.forum"
msgstr "Diễn đàn thảo luận"

msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Bài viết blog của chúng tôi về việc phát hành truyện tranh"

msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

msgid "page.datasets.libgen_rs.story"
msgstr "Câu chuyện ngắn gọn về các nhánh khác nhau của Library Genesis (hoặc “Libgen”) là theo thời gian, những người khác nhau tham gia vào Library Genesis đã có mâu thuẫn, và đi theo con đường riêng của họ."

msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Phiên bản “.fun” được tạo ra bởi người sáng lập ban đầu. Nó đang được cải tiến để ủng hộ một phiên bản mới, phân phối nhiều hơn."

msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Phiên bản “.rs” có dữ liệu rất giống nhau và thường xuyên phát hành bộ sưu tập của họ dưới dạng torrent số lượng lớn. Nó được chia thành hai phần “tiểu thuyết” và “phi tiểu thuyết”."

msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Ban đầu tại “http://gen.lib.rus.ec”."

msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Phiên bản <a %(a_li)s>“.li”</a> có một bộ sưu tập truyện tranh khổng lồ, cũng như các nội dung khác, chưa (chưa) có sẵn để tải xuống hàng loạt qua torrent. Nó có một bộ sưu tập torrent riêng của sách tiểu thuyết và chứa dữ liệu số của <a %(a_scihub)s>Sci-Hub</a> trong cơ sở dữ liệu của nó."

msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Theo <a %(a_mhut)s>bài đăng trên diễn đàn</a> này, Libgen.li ban đầu được lưu trữ tại “http://free-books.dontexist.com”."

msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> theo một số cách cũng là một nhánh của Library Genesis, mặc dù họ đã sử dụng một tên khác cho dự án của mình."

msgid "page.datasets.libgen_rs.description.about"
msgstr "Trang này là về phiên bản “.rs”. Nó nổi tiếng vì thường xuyên xuất bản cả dữ liệu số và toàn bộ nội dung của danh mục sách của nó. Bộ sưu tập sách của nó được chia thành phần tiểu thuyết và phi tiểu thuyết."

msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Một nguồn tài nguyên hữu ích để sử dụng dữ liệu số là <a %(a_metadata)s>trang này</a> (chặn dải IP, có thể phải cần VPN)."

msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Tính đến tháng 03-2024, các torrent mới đang được đăng trong <a %(a_href)s>chủ đề diễn đàn này</a> (chặn dải IP, có thể cần VPN)."

msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrent Phi Hư Cấu trên Lưu Trữ của Anna"

msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrent Hư Cấu trên Lưu Trữ của Anna"

msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Dữ liệu số Libgen.rs"

msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Thông tin trường dữ liệu số Libgen.rs"

msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrent Phi Hư Cấu Libgen.rs"

msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrent Hư Cấu Libgen.rs"

msgid "page.datasets.libgen_rs.link_forum"
msgstr "Diễn đàn thảo luận Libgen.rs"

msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrent bởi Lưu Trữ của Anna (bìa sách)"

msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Blog của chúng tôi về việc phát hành bìa sách"

msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis được biết đến với việc hào phóng cung cấp dữ liệu của họ dưới dạng torrent. Bộ sưu tập Libgen của chúng tôi bao gồm dữ liệu phụ trợ mà họ không phát hành trực tiếp, hợp tác với họ. Cảm ơn rất nhiều đến tất cả những người tham gia Library Genesis đã làm việc với chúng tôi!"

msgid "page.datasets.libgen_rs.release1.title"
msgstr "Phát hành 1 (%(date)s)"

msgid "page.datasets.libgen_rs.release1.intro"
msgstr "<a %(blog_post)s>Phát hành đầu tiên</a> này khá nhỏ: khoảng 300GB bìa sách từ nhánh Libgen.rs, cả hư cấu và phi hư cấu. Chúng được tổ chức theo cách chúng xuất hiện trên libgen.rs, ví dụ:"

msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s cho một cuốn sách phi hư cấu."

msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s cho một cuốn sách hư cấu."

msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Giống như với bộ sưu tập Z-Library, chúng tôi đặt tất cả vào một tệp .tar lớn, có thể được gắn kết bằng <a %(a_ratarmount)s>ratarmount</a> nếu bạn muốn phục vụ các tệp trực tiếp."

msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> là một cơ sở dữ liệu độc quyền của tổ chức phi lợi nhuận <a %(a_oclc)s>OCLC</a>, tập hợp các bản ghi dữ liệu số từ các thư viện trên khắp thế giới. Đây có thể là bộ sưu tập dữ liệu số thư viện lớn nhất trên thế giới."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Tháng 10 năm 2023, phát hành ban đầu:"

msgid "page.datasets.worldcat.description2"
msgstr "Vào tháng 10 năm 2023, chúng tôi đã <a %(a_scrape)s>phát hành</a> một bản scrape toàn diện của cơ sở dữ liệu OCLC (WorldCat), theo <a %(a_aac)s>định dạng Container Lưu Trữ của Anna</a>."

msgid "page.datasets.worldcat.torrents"
msgstr "Torrent bởi Anna’s Archive"

msgid "page.datasets.worldcat.blog_announcement"
msgstr "Bài viết blog của chúng tôi về dữ liệu này"

msgid "page.datasets.openlib.title"
msgstr "Open Library"

msgid "page.datasets.openlib.description"
msgstr "Open Library là một dự án mã nguồn mở của Internet Archive để lập danh mục mọi cuốn sách trên thế giới. Nó có một trong những hoạt động quét sách lớn nhất thế giới và có nhiều sách có sẵn để cho mượn kỹ thuật số. Danh mục dữ liệu số sách của nó có sẵn miễn phí để tải xuống và được bao gồm trên Lưu Trữ của Anna (mặc dù hiện tại không có trong tìm kiếm, trừ khi bạn tìm kiếm rõ ràng một ID Open Library)."

msgid "page.datesets.openlib.link_metadata"
msgstr "Dữ liệu số"

msgid "page.datasets.isbndb.release1.title"
msgstr "Phát hành 1 (2022-10-31)"

msgid "page.datasets.isbndb.release1.text1"
msgstr "Đây là một bản dump của rất nhiều cuộc gọi đến isbndb.com trong tháng 9 năm 2022. Chúng tôi đã cố gắng bao phủ tất cả các phạm vi ISBN. Đây là khoảng 30,9 triệu bản ghi. Trên trang web của họ, họ tuyên bố rằng họ thực sự có 32,6 triệu bản ghi, vì vậy chúng tôi có thể đã bỏ lỡ một số, hoặc <em>họ</em> có thể đã làm sai điều gì đó."

msgid "page.datasets.isbndb.release1.text2"
msgstr "Các phản hồi JSON hầu như là nguyên bản từ máy chủ của họ. Một vấn đề chất lượng dữ liệu mà chúng tôi nhận thấy là đối với các số ISBN-13 bắt đầu bằng một tiền tố khác ngoài “978-”, họ vẫn bao gồm một trường “isbn” chỉ đơn giản là số ISBN-13 với ba số đầu tiên bị cắt bỏ (và chữ số kiểm tra được tính lại). Điều này rõ ràng là sai, nhưng đây là cách họ làm, vì vậy chúng tôi không thay đổi nó."

msgid "page.datasets.isbndb.release1.text3"
msgstr "Một vấn đề tiềm ẩn khác mà bạn có thể gặp phải là thực tế rằng trường “isbn13” có các bản sao, vì vậy bạn không thể sử dụng nó làm khóa chính trong cơ sở dữ liệu. Các trường “isbn13”+“isbn” kết hợp dường như là duy nhất."

msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

msgid "page.datasets.scihub.description1"
msgstr "Để biết thêm thông tin về Sci-Hub, vui lòng tham khảo <a %(a_scihub)s>trang web chính thức</a>, <a %(a_wikipedia)s>trang Wikipedia</a>, và <a %(a_radiolab)s>cuộc phỏng vấn podcast</a> này."

msgid "page.datasets.scihub.description2"
msgstr "Lưu ý rằng Sci-Hub đã bị <a %(a_reddit)s>đóng băng từ năm 2021</a>. Trước đó cũng đã bị đóng băng, nhưng vào năm 2021, một vài triệu bài báo đã được thêm vào. Tuy nhiên, một số lượng hạn chế các bài báo vẫn được thêm vào các bộ sưu tập “scimag” của Libgen, mặc dù không đủ để tạo ra các torrent mới."

msgid "page.datasets.scihub.description3"
msgstr "Chúng tôi sử dụng dữ liệu số của Sci-Hub do <a %(a_libgen_li)s>Libgen.li</a> cung cấp trong bộ sưu tập “scimag”. Chúng tôi cũng sử dụng tập dữ liệu <a %(a_dois)s>dois-2022-02-12.7z</a>."

msgid "page.datasets.scihub.description4"
msgstr "Lưu ý rằng các torrent “smarch” đã <a %(a_smarch)s>bị ngừng sử dụng</a> và do đó không được bao gồm trong danh sách torrent của chúng tôi."

msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent trên Lưu Trữ của Anna"

msgid "page.datasets.scihub.link_metadata"
msgstr "Dữ liệu số và torrent"

msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent trên Libgen.rs"

msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent trên Libgen.li"

msgid "page.datasets.scihub.link_paused"
msgstr "Cập nhật trên Reddit"

msgid "page.datasets.scihub.link_wikipedia"
msgstr "Trang Wikipedia"

msgid "page.datasets.scihub.link_podcast"
msgstr "Cuộc phỏng vấn podcast"

msgid "page.datasets.upload.title"
msgstr "Các tải lên đến Lưu Trữ của Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Tổng quan từ <a %(a1)s>trang datasets</a>."

msgid "page.datasets.upload.description"
msgstr "Các nguồn nhỏ hơn hoặc một lần. Chúng tôi khuyến khích mọi người tải lên đến các thư viện bóng khác trước, nhưng đôi khi mọi người có các bộ sưu tập quá lớn để người khác sắp xếp, mặc dù không đủ lớn để xứng đáng có danh mục riêng."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Bộ sưu tập “tải lên” được chia thành các bộ sưu tập con nhỏ hơn, được chỉ định trong các AACID và tên torrent. Tất cả các bộ sưu tập con đầu tiên được loại bỏ trùng lặp so với bộ sưu tập chính, mặc dù các tệp JSON “upload_records” dữ liệu số vẫn chứa nhiều tham chiếu đến các tệp gốc. Các tệp không phải sách cũng đã được loại bỏ khỏi hầu hết các bộ sưu tập con, và thường <em>không</em> được ghi chú trong “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Nhiều bộ sưu tập con tự chúng cũng  bao gồm nhiều bộ sưu tập con  (chẳng hạn như từ các nguồn khác nhau), được biểu diễn dưới dạng thư mục trong các trường “filepath”."

msgid "page.datasets.upload.subs.heading"
msgstr "Các bộ sưu tập con là:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Tập hợp con"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Ghi chú"

msgid "page.datasets.upload.action.browse"
msgstr "duyệt"

msgid "page.datasets.upload.action.search"
msgstr "tìm kiếm"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Từ <a %(a_href)s>aaaaarg.fail</a>. Có vẻ khá đầy đủ. Từ tình nguyện viên của chúng tôi “cgiym”."

msgid "page.datasets.upload.source.acm"
msgstr "Từ một <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Có sự trùng lặp khá cao với các bộ sưu tập bài báo hiện có, nhưng rất ít trùng khớp MD5, vì vậy chúng tôi quyết định giữ nguyên hoàn toàn."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Thu thập từ <q>iRead eBooks</q> (= phát âm <q>ai rit i-books</q>; airitibooks.com), bởi tình nguyện viên <q>j</q>. Tương ứng với metadata <q>airitibooks</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Từ một bộ sưu tập <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Một phần từ nguồn gốc, một phần từ the-eye.eu, một phần từ các bản sao khác."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Từ một trang web torrent sách riêng tư, <a %(a_href)s>Bibliotik</a> (thường được gọi là “Bib”), trong đó các sách được gộp thành các torrent theo tên (A.torrent, B.torrent) và phân phối qua the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Từ tình nguyện viên của chúng tôi “bpb9v”. Để biết thêm thông tin về <a %(a_href)s>CADAL</a>, xem ghi chú trong <a %(a_duxiu)s>trang dữ liệu DuXiu của chúng tôi</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Thêm từ tình nguyện viên của chúng tôi “bpb9v”, chủ yếu là các tệp DuXiu, cũng như một thư mục “WenQu” và “SuperStar_Journals” (SuperStar là công ty đứng sau DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Từ tình nguyện viên của chúng tôi “cgiym”, các văn bản tiếng Trung từ nhiều nguồn khác nhau (được đại diện dưới dạng thư mục con), bao gồm từ <a %(a_href)s>China Machine Press</a> (một nhà xuất bản lớn của Trung Quốc)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Các bộ sưu tập không phải tiếng Trung (được đại diện dưới dạng thư mục con) từ tình nguyện viên của chúng tôi “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Thu thập sách về kiến trúc Trung Quốc, bởi tình nguyện viên <q>cm</q>: <q>Tôi đã lấy được bằng cách khai thác lỗ hổng mạng tại nhà xuất bản, nhưng lỗ hổng đó đã được đóng lại</q>. Tương ứng với metadata <q>chinese_architecture</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>."

msgid "page.datasets.upload.source.degruyter"
msgstr "Sách từ nhà xuất bản học thuật <a %(a_href)s>De Gruyter</a>, được thu thập từ một vài torrent lớn."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Bản scrape của <a %(a_href)s>docer.pl</a>, một trang web chia sẻ tệp của Ba Lan tập trung vào sách và các tác phẩm viết khác. Được scrape vào cuối năm 2023 bởi tình nguyện viên “p”. Chúng tôi không có dữ liệu số tốt từ trang web gốc (thậm chí không có phần mở rộng tệp), nhưng chúng tôi đã lọc các tệp giống sách và thường có thể trích xuất dữ liệu số từ chính các tệp."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, trực tiếp từ DuXiu, được thu thập bởi tình nguyện viên “w”. Chỉ có các sách DuXiu gần đây có sẵn trực tiếp qua ebooks, vì vậy hầu hết trong số này phải là gần đây."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Các tệp DuXiu còn lại từ tình nguyện viên “m”, không nằm trong định dạng PDG độc quyền của DuXiu (bộ <a %(a_href)s>dữ liệu DuXiu chính</a>). Được thu thập từ nhiều nguồn gốc ban đầu, tiếc là không bảo quản được những nguồn gốc đó trong đường dẫn tệp."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Thu thập sách khiêu dâm, bởi tình nguyện viên <q>do no harm</q>. Tương ứng với metadata <q>hentai</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Bộ sưu tập được scrape từ một nhà xuất bản Manga Nhật bởi tình nguyện viên “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Các tài liệu lưu trữ tư pháp được chọn của Longquan</a>, được cung cấp bởi tình nguyện viên “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Bản scrape của <a %(a_href)s>magzdb.org</a>, một đồng minh của Library Genesis (nó được liên kết trên trang chủ của libgen.rs) nhưng không muốn cung cấp tệp của họ trực tiếp. Được thu thập bởi tình nguyện viên “p” vào cuối năm 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Nhiều tải lên nhỏ, quá nhỏ để làm bộ sưu tập con riêng, nhưng được đại diện dưới dạng thư mục."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebooks từ AvaxHome, một trang web chia sẻ tệp của Nga."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Lưu trữ báo và tạp chí. Tương ứng với metadata <q>newsarch_magz</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Thu thập từ <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Bộ sưu tập của tình nguyện viên “o” người đã thu thập sách Ba Lan trực tiếp từ các trang web phát hành gốc (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Các bộ sưu tập kết hợp của <a %(a_href)s>shuge.org</a> bởi các tình nguyện viên “cgiym” và “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Thư viện Hoàng gia của Trantor”</a> (được đặt tên theo thư viện hư cấu), được scrape vào năm 2022 bởi tình nguyện viên “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Các bộ sưu tập con (được đại diện dưới dạng thư mục) từ tình nguyện viên “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (bởi <a %(a_sikuquanshu)s>Dizhi(迪志)</a> ở Đài Loan), mebook (mebook.cc, 我的小书屋, phòng sách nhỏ của tôi — woz9ts: “Trang web này chủ yếu tập trung vào việc chia sẻ các tệp ebook chất lượng cao, một số trong đó được dàn trang bởi chính chủ sở hữu. Chủ sở hữu đã <a %(a_arrested)s>bị bắt</a> vào năm 2019 và ai đó đã tạo ra một bộ sưu tập các tệp mà anh ta đã chia sẻ.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Các tệp DuXiu còn lại từ tình nguyện viên “woz9ts”, không ở định dạng PDG độc quyền của DuXiu (vẫn cần chuyển đổi sang PDF)."

msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents bởi Lưu Trữ của Anna"

msgid "page.datasets.zlib.title"
msgstr "Dữ liệu từ Z-Library"

msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library có nguồn gốc từ cộng đồng <a %(a_href)s>Library Genesis</a>, và ban đầu được khởi động với dữ liệu của họ. Kể từ đó, nó đã chuyên nghiệp hóa đáng kể và có giao diện hiện đại hơn nhiều. Do đó, họ có thể nhận được nhiều khoản quyên góp hơn, cả về mặt tài chính để tiếp tục cải thiện trang web của họ, cũng như quyên góp sách mới. Họ đã tích lũy được một bộ sưu tập lớn ngoài Library Genesis."

msgid "page.datasets.zlib.description.allegations.title"
msgstr "Cập nhật vào tháng 2 năm 2023."

msgid "page.datasets.zlib.description.allegations"
msgstr "Cuối năm 2022, những người bị cho là người sáng lập Z-Library đã bị bắt, và tên miền này đã bị chính quyền Hoa Kỳ tịch thu. Kể từ đó, trang web này đang dần nhen nhóm tìm cách hoạt động trở lại. Hiện tại vẫn chưa rõ ai là người điều hành."

msgid "page.datasets.zlib.description.three_parts"
msgstr "Bộ sưu tập bao gồm ba phần. Các trang mô tả ban đầu cho hai phần đầu tiên được giữ lại bên dưới. Bạn cần cả ba phần để có được tất cả dữ liệu (trừ các torrents đã bị thay thế, được gạch bỏ trên trang torrents)."

msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: phát hành đầu tiên của chúng tôi. Đây là lần phát hành đầu tiên của cái gọi là “Bản sao Thư viện Cướp biển” (“pilimi”)."

msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: phát hành thứ hai, lần này với tất cả các tệp được gói trong các tệp .tar."

msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: các phát hành mới gia tăng, sử dụng <a %(a_href)s>định dạng Container Lưu Trữ của Anna (AAC)</a>, hiện được phát hành hợp tác với đội ngũ Z-Library."

msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents bởi Lưu Trữ của Anna (dữ liệu số + nội dung)"

msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Bản ghi ví dụ trên Lưu Trữ của Anna (bộ sưu tập gốc)"

msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Bản ghi ví dụ trên Lưu Trữ của Anna (bộ sưu tập “zlib3”)"

msgid "page.datasets.zlib.link.zlib"
msgstr "Trang web chính"

msgid "page.datasets.zlib.link.onion"
msgstr "Tên miền Tor"

msgid "page.datasets.zlib.blog.release1"
msgstr "Bài viết blog về Phát hành 1"

msgid "page.datasets.zlib.blog.release2"
msgstr "Bài viết blog về Phát hành 2"

msgid "page.datasets.zlib.historical.title"
msgstr "Các phát hành Zlib (trang mô tả gốc)"

msgid "page.datasets.zlib.historical.release1.title"
msgstr "Phát hành 1 (%(date)s)"

msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Bản sao ban đầu đã được thu thập một cách tỉ mỉ trong suốt năm 2021 và 2022. Tại thời điểm này, nó hơi lỗi thời: nó phản ánh trạng thái của bộ sưu tập vào tháng 6 năm 2021. Chúng tôi sẽ cập nhật điều này trong tương lai. Hiện tại chúng tôi đang tập trung vào việc phát hành đầu tiên này."

msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Vì Library Genesis đã được bảo tồn với các torrent công khai và được bao gồm trong Z-Library, chúng tôi đã thực hiện việc loại bỏ trùng lặp cơ bản với Library Genesis vào tháng 6 năm 2022. Để làm điều này, chúng tôi đã sử dụng các hàm băm MD5. Có khả năng còn rất nhiều nội dung trùng lặp trong thư viện, chẳng hạn như nhiều định dạng tệp khác nhau của cùng một cuốn sách. Điều này rất khó phát hiện chính xác, vì vậy chúng tôi không làm. Sau khi loại bỏ trùng lặp, chúng tôi còn lại hơn 2 triệu tệp, tổng cộng chỉ dưới 7TB."

msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Bộ sưu tập bao gồm hai phần: một tệp dump MySQL “.sql.gz” của dữ liệu số, và 72 tệp torrent khoảng 50-100GB mỗi tệp. Dữ liệu số chứa các dữ liệu được báo cáo bởi trang web Z-Library (tiêu đề, tác giả, mô tả, loại tệp), cũng như kích thước tệp thực tế và md5sum mà chúng tôi quan sát được, vì đôi khi những thông tin này không khớp nhau. Có vẻ như có các phạm vi tệp mà chính Z-Library có dữ liệu số không chính xác. Chúng tôi cũng có thể đã tải xuống các tệp không chính xác trong một số trường hợp riêng lẻ, điều này chúng tôi sẽ cố gắng phát hiện và sửa chữa trong tương lai."

msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Các tệp torrent lớn chứa dữ liệu sách thực tế, với ID Z-Library làm tên tệp. Các phần mở rộng tệp có thể được tái tạo lại bằng cách sử dụng tệp dump dữ liệu số."

msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Bộ sưu tập là sự kết hợp của nội dung phi hư cấu và hư cấu (không được tách riêng như trong Library Genesis). Chất lượng cũng rất khác nhau."

msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Bản phát hành đầu tiên này hiện đã hoàn toàn có sẵn. Lưu ý rằng các tệp torrent chỉ có sẵn thông qua bản sao Tor của chúng tôi."

msgid "page.datasets.zlib.historical.release2.title"
msgstr "Phát hành 2 (%(date)s)"

msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Chúng tôi đã lấy tất cả các sách được thêm vào Z-Library giữa bản sao cuối của chúng tôi và tháng 8 năm 2022. Chúng tôi cũng đã quay lại và thu thập một số sách mà chúng tôi đã bỏ lỡ lần đầu tiên. Tất cả, bộ sưu tập mới này khoảng 24TB. Một lần nữa, bộ sưu tập này đã được loại bỏ trùng lặp với Library Genesis, vì đã có các torrent có sẵn cho bộ sưu tập đó."

msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Dữ liệu được tổ chức tương tự như bản phát hành đầu tiên. Có một tệp dump MySQL “.sql.gz” của dữ liệu số, cũng bao gồm tất cả dữ liệu số từ bản phát hành đầu tiên, do đó thay thế nó. Chúng tôi cũng đã thêm một số cột mới:"

msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: liệu tệp này đã có trong Library Genesis, trong bộ sưu tập phi hư cấu hoặc hư cấu (được khớp bằng md5)."

msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: tệp này nằm trong torrent nào."

msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: được đặt khi chúng tôi không thể tải xuống cuốn sách."

msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Chúng tôi đã đề cập điều này lần trước, nhưng chỉ để làm rõ: “filename” và “md5” là các thuộc tính thực tế của tệp, trong khi “filename_reported” và “md5_reported” là những gì chúng tôi thu thập từ Z-Library. Đôi khi hai thông tin này không khớp nhau, vì vậy chúng tôi đã bao gồm cả hai."

msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Đối với bản phát hành này, chúng tôi đã thay đổi collation thành “utf8mb4_unicode_ci”, điều này sẽ tương thích với các phiên bản cũ hơn của MySQL."

msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Các tệp dữ liệu tương tự như lần trước, mặc dù chúng lớn hơn nhiều. Chúng tôi đơn giản không thể tạo ra hàng tấn tệp torrent nhỏ hơn. “pilimi-zlib2-0-14679999-extra.torrent” chứa tất cả các tệp mà chúng tôi đã bỏ lỡ trong bản phát hành trước, trong khi các torrent khác đều là các phạm vi ID mới. "

msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Cập nhật %(date)s:</strong> Chúng tôi đã làm hầu hết các torrent của mình quá lớn, khiến các ứng dụng torrent gặp khó khăn. Chúng tôi đã gỡ bỏ chúng và phát hành các torrent mới."

msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Cập nhật %(date)s:</strong> Vẫn còn quá nhiều tệp, vì vậy chúng tôi đã gói chúng trong các tệp tar và phát hành các torrent mới một lần nữa."

msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Phát hành 2 phụ lục (%(date)s)"

msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Đây là một tệp torrent bổ sung duy nhất. Nó không chứa bất kỳ thông tin mới nào, nhưng nó có một số dữ liệu có thể mất một thời gian để tính toán. Điều này làm cho nó tiện lợi để có, vì tải xuống torrent này thường nhanh hơn so với tính toán từ đầu. Đặc biệt, nó chứa các chỉ mục SQLite cho các tệp tar, để sử dụng với <a %(a_href)s>ratarmount</a>."

msgid "page.faq.title"
msgstr "Câu hỏi thường gặp (FAQ)"

msgid "page.faq.what_is.title"
msgstr "Lưu trữ của Anna là gì?"

msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Lưu trữ của Anna</span> là một dự án phi lợi nhuận với hai mục tiêu:"

msgid "page.home.intro.text2"
msgstr "<li><strong>Bảo tồn:</strong> Sao lưu tất cả kiến thức và văn hóa của nhân loại.</li><li><strong>Truy cập:</strong> Làm cho kiến thức và văn hóa này có sẵn cho bất kỳ ai trên thế giới.</li>"

msgid "page.home.intro.open_source"
msgstr "Tất cả <a %(a_code)s>mã</a> và <a %(a_datasets)s>dữ liệu</a> của chúng tôi đều hoàn toàn mã nguồn mở."

msgid "page.home.preservation.header"
msgstr "Sự bảo tồn"

msgid "page.home.preservation.text1"
msgstr "Chúng tôi bảo tồn sách, báo, truyện tranh, tạp chí, v.v. bằng cách đưa những tài liệu này từ nhiều <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">thư viện chìm</a>, thư viện chính thức và các bộ sưu tập khác cùng nhau ở một nơi. Tất cả dữ liệu này được lưu giữ vĩnh viễn bằng cách giúp dễ dàng sao chép hàng loạt — sử dụng torrent — tạo ra nhiều bản sao trên khắp thế giới. Một số thư viện chìm đã tự thực hiện việc này (ví dụ: Sci-Hub, Library Genesis), trong khi Anna's Archive “giải phóng” các thư viện khác không cung cấp phân phối hàng loạt như là Z-Library hoặc không phải là thư viện chìm (ví dụ: Internet Archive, DuXiu)."

msgid "page.home.preservation.text2"
msgstr "Sự phân phối rộng rãi này, kết hợp với mã nguồn mở, giúp trang web của chúng tôi có khả năng chống lại việc gỡ bỏ và đảm bảo việc bảo tồn lâu dài kiến thức và văn hóa của nhân loại. Tìm hiểu thêm về <a href=\"/datasets\">bộ dữ liệu của chúng tôi</a>."

msgid "page.home.preservation.label"
msgstr "Chúng tôi ước tính rằng chúng tôi đã bảo tồn khoảng <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% sách trên thế giới</a>."

msgid "page.home.access.header"
msgstr "Truy cập"

msgid "page.home.access.text"
msgstr "Chúng tôi làm việc với các đối tác để giúp mọi người có thể truy cập các bộ sưu tập của mình một cách dễ dàng và miễn phí. Chúng tôi tin rằng mọi người đều có quyền sở hữu được trí tuệ tập thể của nhân loại. Và <a %(a_search)s>không gây thiệt hại cho tác giả</a>."

msgid "page.home.access.label"
msgstr "Số lượt tải xuống hàng giờ trong 30 ngày qua. Trung bình hàng giờ: %(hourly)s. Trung bình hàng ngày: %(daily)s."

msgid "page.about.text2"
msgstr "Chúng tôi tin tưởng mạnh mẽ vào sự lưu thông tự do của thông tin, và việc bảo tồn kiến thức và văn hóa. Với công cụ tìm kiếm này, chúng tôi xây dựng trên vai những người khổng lồ. Chúng tôi rất tôn trọng công việc vất vả của những người đã tạo ra các thư viện bóng tối khác nhau, và hy vọng rằng công cụ tìm kiếm này sẽ mở rộng tầm với của họ."

msgid "page.about.text3"
msgstr "Để cập nhật tiến trình của chúng tôi, theo dõi Anna trên <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> hoặc <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Để có câu hỏi và phản hồi, vui lòng liên hệ Anna tại %(email)s."

msgid "page.faq.help.title"
msgstr "Bằng cách nào để tôi có thể giúp?"

msgid "page.about.help.text"
msgstr "<li>1. Theo dõi chúng tôi trên <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, hoặc <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Lan truyền về Anna’s Archive trên Twitter, Reddit, Tiktok, Instagram, tại quán cà phê hoặc thư viện địa phương của bạn, hoặc bất cứ nơi nào bạn đến! Chúng tôi không tin vào việc giữ kín — nếu chúng tôi bị gỡ xuống, chúng tôi sẽ xuất hiện lại ở nơi khác, vì tất cả mã và dữ liệu của chúng tôi đều hoàn toàn mã nguồn mở.</li><li>3. Nếu bạn có thể, hãy xem xét <a href=\"/donate\">quyên góp</a>.</li><li>4. Giúp <a href=\"https://translate.annas-software.org/\">dịch</a> trang web của chúng tôi sang các ngôn ngữ khác nhau.</li><li>5. Nếu bạn là kỹ sư phần mềm, hãy xem xét đóng góp vào <a href=\"https://annas-software.org/\">mã nguồn mở</a> của chúng tôi, hoặc chia sẻ <a href=\"/datasets\">các torrent</a> của chúng tôi.</li>"

msgid "page.volunteering.section.light.matrix"
msgstr "Chúng tôi hiện cũng có một kênh Matrix đồng bộ tại %(matrix)s."

msgid "page.about.help.text6"
msgstr "6. Nếu bạn là một nhà nghiên cứu bảo mật, chúng tôi có thể sử dụng kỹ năng của bạn cho cả tấn công và phòng thủ. Hãy xem trang <a %(a_security)s>Bảo mật</a> của chúng tôi."

msgid "page.about.help.text7"
msgstr "7. Chúng tôi đang tìm kiếm các chuyên gia về thanh toán cho các thương gia ẩn danh. Bạn có thể giúp chúng tôi thêm các cách quyên góp tiện lợi hơn không? PayPal, WeChat, thẻ quà tặng. Nếu bạn biết ai đó, vui lòng liên hệ với chúng tôi."

msgid "page.about.help.text8"
msgstr "8. Chúng tôi luôn tìm kiếm thêm dung lượng máy chủ."

msgid "page.about.help.text9"
msgstr "9. Bạn có thể giúp bằng cách báo cáo sự cố tệp, để lại bình luận và tạo danh sách ngay trên trang web này. Bạn cũng có thể giúp bằng cách <a %(a_upload)s>tải lên thêm sách</a>, hoặc sửa chữa sự cố tệp hoặc định dạng của các sách hiện có."

msgid "page.about.help.text10"
msgstr "10. Tạo hoặc giúp duy trì trang Wikipedia cho Lưu Trữ của Anna bằng ngôn ngữ của bạn."

msgid "page.about.help.text11"
msgstr "11. Chúng tôi đang tìm cách đặt các quảng cáo nhỏ, tinh tế. Nếu bạn muốn quảng cáo trên Lưu Trữ của Anna, vui lòng cho chúng tôi biết."

msgid "page.faq.help.mirrors"
msgstr "Chúng tôi rất mong mọi người thiết lập <a %(a_mirrors)s>các bản sao</a>, và chúng tôi sẽ hỗ trợ tài chính cho việc này."

msgid "page.about.help.volunteer"
msgstr "Để biết thêm thông tin chi tiết về cách tình nguyện, xem trang <a %(a_volunteering)s>Tình nguyện & Tiền thưởng</a> của chúng tôi."

msgid "page.faq.slow.title"
msgstr "Tại sao tải xuống chậm lại chậm như vậy?"

msgid "page.faq.slow.text1"
msgstr "Chúng tôi thực sự không có đủ nguồn lực để cung cấp cho mọi người trên thế giới tải xuống tốc độ cao, dù chúng tôi rất muốn. Nếu có một nhà hảo tâm giàu có muốn hỗ trợ chúng tôi điều này, thì thật tuyệt vời, nhưng cho đến lúc đó, chúng tôi đang cố gắng hết sức. Chúng tôi là một dự án phi lợi nhuận mà chỉ có thể tự duy trì thông qua các khoản quyên góp."

msgid "page.faq.slow.text2"
msgstr "Đây là lý do tại sao chúng tôi triển khai hai hệ thống tải xuống miễn phí, với các đối tác của chúng tôi: các máy chủ chia sẻ với tốc độ tải xuống chậm, và các máy chủ nhanh hơn một chút với danh sách chờ (để giảm số lượng người tải xuống cùng một lúc)."

msgid "page.faq.slow.text3"
msgstr "Chúng tôi cũng có <a %(a_verification)s>xác minh trình duyệt</a> cho các lượt tải xuống chậm, vì nếu không, các bot và trình thu thập dữ liệu sẽ lạm dụng chúng, làm cho mọi thứ chậm hơn đối với người dùng hợp pháp."

msgid "page.faq.slow.text4"
msgstr "Lưu ý rằng, khi sử dụng Trình duyệt Tor, bạn có thể cần điều chỉnh cài đặt bảo mật của mình. Ở mức thấp nhất của các tùy chọn, gọi là “Tiêu chuẩn”, thử thách Cloudflare turnstile thành công. Ở các tùy chọn cao hơn, gọi là “An toàn hơn” và “An toàn nhất”, thử thách thất bại."

msgid "page.faq.slow.text5"
msgstr "Đối với các tệp lớn, đôi khi tải xuống chậm có thể bị gián đoạn giữa chừng. Chúng tôi khuyên bạn nên sử dụng trình quản lý tải xuống (chẳng hạn như JDownloader) để tự động tiếp tục các lượt tải xuống lớn."

msgid "page.donate.faq.title"
msgstr "Câu hỏi thường gặp về đóng góp"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Các gói thành viên có tự động gia hạn không?</div> Các gói thành viên <strong>không</strong> tự động gia hạn. Bạn có thể tham gia trong thời gian dài hoặc ngắn tùy ý."

msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Tôi có thể nâng cấp tư cách thành viên hoặc có nhiều tư cách thành viên không?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Bạn có phương thức thanh toán khác không?</div> Hiện tại thì chúng tôi không có. Rất nhiều người không muốn những kho lưu trữ như thế này tồn tại nên chúng tôi phải cẩn thận. Nếu bạn có thể giúp chúng tôi thiết lập các phương thức thanh toán khác (thuận tiện hơn) một cách an toàn, vui lòng liên hệ theo số %(email)s."

msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Các khoảng giá mỗi tháng có ý nghĩa gì?</div> Bạn có thể đạt được mức thấp hơn của một khoảng giá bằng cách áp dụng tất cả các giảm giá, chẳng hạn như chọn một thời gian dài hơn một tháng."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Bạn chi tiêu các khoản quyên góp vào việc gì?</div> 100%% được sử dụng để bảo tồn và làm cho kiến thức và văn hóa của thế giới trở nên dễ tiếp cận. Hiện tại, chúng tôi chủ yếu chi tiêu vào máy chủ, lưu trữ và băng thông. Không có khoản tiền nào được chuyển đến bất kỳ thành viên nào trong nhóm."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Tôi có thể quyên góp một số tiền lớn không?</div> Điều đó thật tuyệt vời! Để quyên góp trên vài nghìn đô la, vui lòng liên hệ trực tiếp với chúng tôi tại %(email)s."

msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Tôi có thể quyên góp mà không trở thành thành viên không?</div> Chắc chắn rồi. Chúng tôi chấp nhận mọi khoản quyên góp bất kể số tiền tại địa chỉ Monero (XMR) này: %(address)s."

msgid "page.faq.upload.title"
msgstr "Làm thế nào để tôi tải lên sách mới?"

msgid "page.upload.zlib.text1"
msgstr "Ngoài ra, bạn có thể tải chúng lên Z-Library <a %(a_upload)s>tại đây</a>."

msgid "page.upload.upload_to_both"
msgstr "Đối với các tải lên nhỏ (tối đa 10.000 tệp) vui lòng tải chúng lên cả %(first)s và %(second)s."

msgid "page.upload.text1"
msgstr "Hiện tại, chúng tôi đề xuất tải lên sách mới vào các nhánh của Library Genesis. Đây là <a %(a_guide)s>hướng dẫn tiện lợi</a>. Lưu ý rằng cả hai nhánh mà chúng tôi lập chỉ mục trên trang web này đều lấy từ hệ thống tải lên này."

msgid "page.upload.libgenli_login_instructions"
msgstr "Đối với Libgen.li, hãy chắc chắn đăng nhập trước vào <a %(a_forum)s>diễn đàn của họ</a> với tên người dùng %(username)s và mật khẩu %(password)s, sau đó quay lại <a %(a_upload_page)s>trang tải lên</a> của họ."

msgid "common.libgen.email"
msgstr "Nếu địa chỉ email của bạn không hoạt động trên diễn đàn Libgen, chúng tôi khuyên bạn nên sử dụng <a %(a_mail)s>Proton Mail</a> (miễn phí). Bạn cũng có thể <a %(a_manual)s>yêu cầu kích hoạt</a> tài khoản của mình thủ công."

msgid "page.faq.mhut_upload"
msgstr "Lưu ý rằng mhut.org chặn một số dải IP nhất định, vì vậy có thể cần sử dụng VPN."

msgid "page.upload.large.text"
msgstr "Đối với các tải lên lớn (hơn 10.000 tệp) không được chấp nhận bởi Libgen hoặc Z-Library, vui lòng liên hệ với chúng tôi tại %(a_email)s."

msgid "page.upload.zlib.text2"
msgstr "Để tải lên các bài giấy học thuật, vui lòng cũng (ngoài Library Genesis) tải lên <a %(a_stc_nexus)s>STC Nexus</a>. Họ là thư viện bóng tốt nhất cho các bài giấy mới. Chúng tôi chưa tích hợp họ, nhưng sẽ làm điều đó vào một thời điểm nào đó. Bạn có thể sử dụng <a %(a_telegram)s>bot tải lên trên Telegram</a> của họ, hoặc liên hệ với địa chỉ được liệt kê trong tin nhắn ghim của họ nếu bạn có quá nhiều tệp để tải lên theo cách này."

msgid "page.faq.request.title"
msgstr "Làm thế nào để yêu cầu sách?"

msgid "page.request.cannot_accomodate"
msgstr "Hiện tại, chúng tôi không thể đáp ứng yêu cầu sách."

msgid "page.request.forums"
msgstr "Xin vui lòng gửi yêu cầu của bạn trên các diễn đàn Z-Library hoặc Libgen."

msgid "page.request.dont_email"
msgstr "Đừng gửi email các yêu cầu sách của bạn cho chúng tôi."

msgid "page.faq.metadata.title"
msgstr "Bạn có thu thập dữ liệu số không?"

msgid "page.faq.metadata.indeed"
msgstr "Chúng tôi thực sự có."

msgid "page.faq.1984.title"
msgstr "Tôi đã tải xuống 1984 của George Orwell, liệu cảnh sát có đến gõ cửa nhà tôi không?"

msgid "page.faq.1984.text"
msgstr "Đừng lo lắng quá, có rất nhiều người tải xuống từ các trang web mà chúng tôi liên kết, và rất hiếm khi gặp rắc rối. Tuy nhiên, để an toàn, chúng tôi khuyên bạn nên sử dụng VPN (trả phí), hoặc <a %(a_tor)s>Tor</a> (miễn phí)."

msgid "page.faq.save_search.title"
msgstr "Làm thế nào để lưu cài đặt tìm kiếm của tôi?"

msgid "page.faq.save_search.text1"
msgstr "Chọn các cài đặt bạn thích, để trống hộp tìm kiếm, nhấp vào “Tìm kiếm”, và sau đó đánh dấu trang bằng tính năng đánh dấu trang của trình duyệt của bạn."

msgid "page.faq.mobile.title"
msgstr "Bạn có ứng dụng di động không?"

msgid "page.faq.mobile.text1"
msgstr "Chúng tôi không có ứng dụng di động chính thức, nhưng bạn có thể cài đặt trang web này như một ứng dụng."

msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Nhấp vào menu ba chấm ở góc trên bên phải, và chọn “Thêm vào Màn hình chính”."

msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Nhấn nút “Chia sẻ” ở dưới cùng, và chọn “Thêm vào Màn hình chính”."

msgid "page.faq.api.title"
msgstr "Bạn có API không?"

msgid "page.faq.api.text1"
msgstr "Chúng tôi có một API JSON ổn định cho thành viên, để lấy URL tải xuống nhanh: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (tài liệu nằm trong JSON)."

msgid "page.faq.api.text2"
msgstr "Đối với các trường hợp sử dụng khác, chẳng hạn như lặp qua tất cả các tệp của chúng tôi, xây dựng tìm kiếm tùy chỉnh, v.v., chúng tôi khuyến nghị <a %(a_generate)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Dữ liệu thô có thể được khám phá thủ công <a %(a_explore)s>thông qua các tệp JSON</a>."

msgid "page.faq.api.text3"
msgstr "Danh sách torrent thô của chúng tôi cũng có thể được tải xuống dưới dạng <a %(a_torrents)s>JSON</a>."

msgid "page.faq.torrents.title"
msgstr "Câu hỏi thường gặp về Torrents"

msgid "page.faq.torrents.q1"
msgstr "Tôi muốn giúp chia sẻ, nhưng tôi không có nhiều dung lượng đĩa."

msgid "page.faq.torrents.a1"
msgstr "Sử dụng <a %(a_list)s>trình tạo danh sách torrent</a> để tạo danh sách các torrent cần seed nhất, trong giới hạn dung lượng lưu trữ của bạn."

msgid "page.faq.torrents.q2"
msgstr "Các torrent quá chậm; tôi có thể tải dữ liệu trực tiếp từ bạn không?"

msgid "page.faq.torrents.a2"
msgstr "Có, xem trang <a %(a_llm)s>dữ liệu LLM</a>."

msgid "page.faq.torrents.q3"
msgstr "Tôi có thể chỉ tải xuống một phần của các tệp, như chỉ một ngôn ngữ hoặc chủ đề cụ thể không?"

msgid "page.faq.torrents.a3_short_answer"
msgstr "Câu trả lời ngắn: không dễ dàng."

msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Câu trả lời dài:"

msgid "page.faq.torrents.a3"
msgstr "Hầu hết các torrent chứa các tệp trực tiếp, có nghĩa là bạn có thể hướng dẫn các ứng dụng torrent chỉ tải xuống các tệp cần thiết. Để xác định tệp nào cần tải xuống, bạn có thể <a %(a_generate)s>tạo</a> dữ liệu số của chúng tôi, hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Thật không may, một số bộ sưu tập torrent chứa các tệp .zip hoặc .tar ở gốc, trong trường hợp đó bạn cần tải xuống toàn bộ torrent trước khi có thể chọn các tệp riêng lẻ."

msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Tuy nhiên, chúng tôi có <a %(a_ideas)s>một số ý tưởng</a> cho trường hợp sau.)"

msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Chưa có công cụ dễ sử dụng nào để lọc torrent, nhưng chúng tôi hoan nghênh sự đóng góp."

msgid "page.faq.torrents.q4"
msgstr "Bạn xử lý các tệp trùng lặp trong các torrent như thế nào?"

msgid "page.faq.torrents.a4"
msgstr "Chúng tôi cố gắng giữ sự trùng lặp hoặc chồng chéo tối thiểu giữa các torrent trong danh sách này, nhưng điều này không phải lúc nào cũng đạt được, và phụ thuộc nhiều vào chính sách của các thư viện nguồn. Đối với các thư viện phát hành torrent của riêng họ, điều này nằm ngoài tầm kiểm soát của chúng tôi. Đối với các torrent do Anna’s Archive phát hành, chúng tôi chỉ loại bỏ trùng lặp dựa trên hash MD5, có nghĩa là các phiên bản khác nhau của cùng một cuốn sách sẽ không bị loại bỏ trùng lặp."

msgid "page.faq.torrents.q5"
msgstr "Tôi có thể lấy danh sách torrent dưới dạng JSON không?"

msgid "page.faq.torrents.a5"
msgstr "Có."

msgid "page.faq.torrents.q6"
msgstr "Tôi không thấy các tệp PDF hoặc EPUB trong torrents, chỉ có các tệp nhị phân? Tôi phải làm gì?"

msgid "page.faq.torrents.a6"
msgstr "Thực ra đây là các tệp PDF và EPUB, chúng chỉ không có phần mở rộng trong nhiều torrents của chúng tôi. Có hai nơi bạn có thể tìm thấy dữ liệu số cho các tệp torrent, bao gồm các loại tệp/phần mở rộng:"

msgid "page.faq.torrents.a6.li1"
msgstr "1. Mỗi bộ sưu tập hoặc phát hành có dữ liệu số riêng. Ví dụ, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> có cơ sở dữ liệu dữ liệu số tương ứng được lưu trữ trên trang web Libgen.rs. Chúng tôi thường liên kết đến các tài nguyên dữ liệu số liên quan từ <a %(a_datasets)s>trang tập dữ liệu</a> của mỗi bộ sưu tập."

msgid "page.faq.torrents.a6.li2"
msgstr "2. Chúng tôi khuyến nghị <a %(a_generate)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Chúng chứa một ánh xạ cho mỗi bản ghi trong Anna’s Archive đến các tệp torrent tương ứng của nó (nếu có), dưới “torrent_paths” trong ElasticSearch JSON."

msgid "page.faq.torrents.q7"
msgstr "Tại sao ứng dụng torrent của tôi không thể mở một số tệp torrent / liên kết magnet của bạn?"

msgid "page.faq.torrents.a7"
msgstr "Một số ứng dụng torrent không hỗ trợ kích thước mảnh lớn, điều mà nhiều torrent của chúng tôi có (đối với các torrent mới hơn, chúng tôi không làm điều này nữa — mặc dù nó hợp lệ theo thông số kỹ thuật!). Vì vậy, hãy thử một ứng dụng khác nếu bạn gặp phải vấn đề này, hoặc phàn nàn với các nhà phát hành ứng dụng torrent của bạn."

msgid "page.faq.security.title"
msgstr "Bạn có chương trình tiết lộ có trách nhiệm không?"

msgid "page.faq.security.text1"
msgstr "Chúng tôi hoan nghênh các nhà nghiên cứu bảo mật tìm kiếm các lỗ hổng trong hệ thống của chúng tôi. Chúng tôi là những người ủng hộ lớn của việc tiết lộ có trách nhiệm. Liên hệ với chúng tôi <a %(a_contact)s>tại đây</a>."

msgid "page.faq.security.text2"
msgstr "Hiện tại chúng tôi không thể trao thưởng cho các lỗi bảo mật, ngoại trừ các lỗ hổng có <a %(a_link)s>tiềm năng làm tổn hại đến tính ẩn danh của chúng tôi</a>, với mức thưởng từ $10k-50k. Chúng tôi mong muốn mở rộng phạm vi trao thưởng cho các lỗi bảo mật trong tương lai! Xin lưu ý rằng các cuộc tấn công kỹ thuật xã hội không nằm trong phạm vi này."

msgid "page.faq.security.text3"
msgstr "Nếu bạn quan tâm đến bảo mật tấn công và muốn giúp lưu trữ kiến thức và văn hóa của thế giới, hãy liên hệ với chúng tôi. Có nhiều cách bạn có thể giúp đỡ."

msgid "page.faq.resources.title"
msgstr "Có thêm tài nguyên nào về Anna’s Archive không?"

msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog của Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — cập nhật thường xuyên"

msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Phần mềm của Anna</a> — mã nguồn mở của chúng tôi"

msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Dịch trên Phần mềm của Anna</a> — hệ thống dịch thuật của chúng tôi"

msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — về dữ liệu"

msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — các tên miền thay thế"

msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — thêm về chúng tôi (vui lòng hãy giúp cập nhật trang này, hoặc tạo một trang cho ngôn ngữ của bạn!)"

msgid "page.faq.copyright.title"
msgstr "Làm thế nào để báo cáo vi phạm bản quyền?"

msgid "page.faq.copyright.text1"
msgstr "Chúng tôi không lưu trữ bất kỳ tài liệu có bản quyền nào ở đây. Chúng tôi là một công cụ tìm kiếm, và do đó chỉ lập chỉ mục dữ liệu số đã có sẵn công khai. Khi tải xuống từ các nguồn bên ngoài này, chúng tôi khuyên bạn nên kiểm tra luật pháp tại khu vực của bạn về những gì được phép. Chúng tôi không chịu trách nhiệm về nội dung được lưu trữ bởi các trang web khác."

msgid "page.faq.copyright.text2"
msgstr "Nếu bạn có khiếu nại về những gì bạn thấy ở đây, cách tốt nhất là liên hệ với trang web gốc. Chúng tôi thường xuyên cập nhật thay đổi của họ vào cơ sở dữ liệu của chúng tôi. Nếu bạn thực sự nghĩ rằng bạn có một khiếu nại DMCA hợp lệ mà chúng tôi nên phản hồi, vui lòng điền vào <a %(a_copyright)s>mẫu khiếu nại DMCA / Bản quyền</a>. Chúng tôi coi trọng khiếu nại của bạn và sẽ phản hồi sớm nhất có thể."

msgid "page.faq.hate.title"
msgstr "Tôi ghét cách bạn điều hành dự án này!"

msgid "page.faq.hate.text1"
msgstr "Chúng tôi cũng muốn nhắc nhở mọi người rằng tất cả mã và dữ liệu của chúng tôi hoàn toàn là mã nguồn mở. Điều này là duy nhất đối với các dự án như của chúng tôi — chúng tôi không biết bất kỳ dự án nào khác có danh mục khổng lồ tương tự mà cũng hoàn toàn là mã nguồn mở. Chúng tôi rất hoan nghênh bất kỳ ai nghĩ rằng chúng tôi điều hành dự án kém để lấy mã và dữ liệu của chúng tôi và thiết lập thư viện bóng của riêng họ! Chúng tôi không nói điều này vì ác ý hay gì đó — chúng tôi thực sự nghĩ rằng điều này sẽ tuyệt vời vì nó sẽ nâng cao tiêu chuẩn cho mọi người và bảo tồn tốt hơn di sản của nhân loại."

msgid "page.faq.uptime.title"
msgstr "Bạn có sử dụng công cụ giám sát thời gian hoạt động không?"

msgid "page.faq.uptime.text1"
msgstr "Vui lòng xem <a %(a_href)s>dự án tuyệt vời này</a>."

msgid "page.faq.physical.title"
msgstr "Làm thế nào để tôi quyên góp sách hoặc các tài liệu vật lý khác?"

msgid "page.faq.physical.text1"
msgstr "Xin vui lòng gửi chúng đến <a %(a_archive)s>Internet Archive</a>. Họ sẽ bảo quản chúng một cách đúng đắn."

msgid "page.faq.anna.title"
msgstr "Anna là ai?"

msgid "page.faq.anna.text1"
msgstr "Bạn là Anna!"

msgid "page.faq.favorite.title"
msgstr "Những cuốn sách yêu thích của bạn là gì?"

msgid "page.faq.favorite.text1"
msgstr "Dưới đây là một số cuốn sách có ý nghĩa đặc biệt đối với thế giới thư viện bóng và bảo tồn kỹ thuật số:"

msgid "page.fast_downloads.no_more_new"
msgstr "Bạn đã hết lượt tải xuống nhanh hôm nay."

msgid "page.fast_downloads.no_member"
msgstr "Trở thành một thành viên để có thể tải xuống nhanh hơn."

msgid "page.fast_downloads.no_member_2"
msgstr "Chúng tôi hiện hỗ trợ thẻ quà tặng Amazon, thẻ tín dụng và thẻ ghi nợ, tiền điện tử, Alipay và WeChat."

msgid "page.home.full_database.header"
msgstr "Cơ sở dữ liệu đầy đủ"

msgid "page.home.full_database.subtitle"
msgstr "Sách, bài báo, tạp chí, truyện tranh, hồ sơ thư viện, dữ liệu số, …"

msgid "page.home.full_database.search"
msgstr "Tìm kiếm"

msgid "page.home.scidb.header"
msgstr "SciDB"

msgid "layout.index.header.nav.beta"
msgstr "bản beta"

msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub đã <a %(a_paused)s>tạm dừng</a> việc tải lên các bài báo mới."

msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB là sự tiếp nối của Sci-Hub."

msgid "page.home.scidb.subtitle"
msgstr "Truy cập trực tiếp vào %(count)s các bài báo học thuật"

msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

msgid "page.home.scidb.open"
msgstr "Mở"

msgid "page.home.scidb.browser_verification"
msgstr "Nếu bạn là <a %(a_member)s>thành viên</a>, không cần xác minh trình duyệt."

msgid "page.home.archive.header"
msgstr "Lưu trữ dài hạn"

msgid "page.home.archive.body"
msgstr "Các datasets được sử dụng trong Anna’s Archive hoàn toàn mở, và có thể được sao chép hàng loạt bằng cách sử dụng torrents. <a %(a_datasets)s>Tìm hiểu thêm…</a>"

msgid "page.home.torrents.body"
msgstr "Bạn có thể giúp đỡ rất nhiều bằng cách seed torrents. <a %(a_torrents)s>Tìm hiểu thêm…</a>"

msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s seeders"

msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s seeders"

msgid "page.home.llm.header"
msgstr "Dữ liệu đào tạo LLM"

msgid "page.home.llm.body"
msgstr "Chúng tôi có bộ sưu tập dữ liệu văn bản chất lượng cao lớn nhất thế giới. <a %(a_llm)s>Tìm hiểu thêm…</a>"

msgid "page.home.mirrors.header"
msgstr "🪩 Gương: kêu gọi tình nguyện viên"

msgid "page.home.volunteering.header"
msgstr "🤝 Tìm kiếm tình nguyện viên"

msgid "page.home.volunteering.help_out"
msgstr "Là một dự án phi lợi nhuận, mã nguồn mở, chúng tôi luôn tìm kiếm những người có thể giúp đỡ."

msgid "page.home.payment_processor.body"
msgstr "Nếu bạn điều hành một bộ xử lý thanh toán ẩn danh có rủi ro cao, vui lòng liên hệ với chúng tôi. Chúng tôi cũng đang tìm kiếm những người muốn đặt quảng cáo nhỏ tinh tế. Tất cả số tiền thu được sẽ được sử dụng cho các nỗ lực bảo tồn của chúng tôi."

msgid "layout.index.header.nav.annasblog"
msgstr "Blog của Anna ↗"

msgid "page.ipfs_downloads.title"
msgstr "Tải xuống IPFS"

msgid "page.partner_download.main_page"
msgstr "<a %(a_main)s>&lt; Tất cả các đường link tải xuống cho tệp này</a>"

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Cổng IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(bạn có thể cần phải thử nhiều lần với IPFS)"

msgid "page.partner_download.faster_downloads"
msgstr "🚀 Để tải xuống nhanh hơn và bỏ qua các kiểm tra trình duyệt, <a %(a_membership)s>hãy trở thành thành viên</a>."

msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Để sao chép hàng loạt bộ sưu tập của chúng tôi, hãy xem các trang <a %(a_datasets)s>Datasets</a> và <a %(a_torrents)s>Torrents</a>."

msgid "page.llm.title"
msgstr "Dữ liệu LLM"

msgid "page.llm.intro"
msgstr "Người ta hiểu rõ rằng các LLM phát triển mạnh nhờ dữ liệu chất lượng cao. Chúng tôi có bộ sưu tập sách, bài báo, tạp chí, v.v. lớn nhất thế giới, đây là một số nguồn văn bản chất lượng cao nhất."

msgid "page.llm.unique_scale"
msgstr "Quy mô và phạm vi độc đáo"

msgid "page.llm.unique_scale.text1"
msgstr "Bộ sưu tập của chúng tôi chứa hơn một trăm triệu tệp, bao gồm các tạp chí học thuật, sách giáo khoa và tạp chí. Chúng tôi đạt được quy mô này bằng cách kết hợp các kho lưu trữ lớn hiện có."

msgid "page.llm.unique_scale.text2"
msgstr "Một số bộ sưu tập nguồn của chúng tôi đã có sẵn với số lượng lớn (Sci-Hub và các phần của Libgen). Các nguồn khác chúng tôi tự giải phóng. <a %(a_datasets)s>Datasets</a> hiển thị tổng quan đầy đủ."

msgid "page.llm.unique_scale.text3"
msgstr "Bộ sưu tập của chúng tôi bao gồm hàng triệu sách, bài báo và tạp chí từ trước thời đại sách điện tử. Phần lớn bộ sưu tập này đã được OCR và đã có ít sự trùng lặp nội bộ."

msgid "page.llm.how_we_can_help"
msgstr "Chúng tôi có thể giúp như thế nào"

msgid "page.llm.how_we_can_help.text1"
msgstr "Chúng tôi có thể cung cấp quyền truy cập tốc độ cao vào toàn bộ bộ sưu tập hiện tại, cũng như các bộ sưu tập chưa được công bố."

msgid "page.llm.how_we_can_help.text2"
msgstr "Đây là quyền truy cập cấp doanh nghiệp mà chúng tôi có thể cung cấp cho các khoản quyên góp trong khoảng hàng chục nghìn USD. Chúng tôi cũng sẵn sàng trao đổi điều này để lấy các bộ sưu tập chất lượng cao mà chúng tôi chưa có."

msgid "page.llm.how_we_can_help.text3"
msgstr "Chúng tôi có thể hoàn tiền cho bạn nếu bạn có thể cung cấp cho chúng tôi sự phong phú của dữ liệu của chúng tôi, chẳng hạn như:"

msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

msgid "page.llm.how_we_can_help.deduplication"
msgstr "Loại bỏ trùng lặp"

msgid "page.llm.how_we_can_help.extraction"
msgstr "Trích xuất văn bản và dữ liệu số"

msgid "page.llm.how_we_can_help.text4"
msgstr "Hỗ trợ lưu trữ lâu dài kiến thức của con người, đồng thời nhận được dữ liệu tốt hơn cho mô hình của bạn!"

msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Liên hệ với chúng tôi</a> để thảo luận về cách chúng ta có thể hợp tác với nhau."

msgid "page.login.continue"
msgstr "Tiếp tục"

msgid "page.login.please"
msgstr "Vui lòng <a %(a_account)s>đăng nhập</a> để xem trang này.</a>"

msgid "page.maintenance.header"
msgstr "Anna’s Archive đang tạm thời ngừng hoạt động để bảo trì. Vui lòng quay lại sau một giờ."

msgid "page.metadata.header"
msgstr "Cải thiện dữ liệu số"

msgid "page.metadata.body1"
msgstr "Bạn có thể giúp bảo tồn sách bằng cách cải thiện dữ liệu số! Trước tiên, hãy đọc phần giới thiệu về dữ liệu số trên Anna’s Archive, sau đó học cách cải thiện dữ liệu số thông qua việc liên kết với Open Library, và nhận được tư cách thành viên miễn phí trên Anna’s Archive."

msgid "page.metadata.background.title"
msgstr "Giới thiệu"

msgid "page.metadata.background.body1"
msgstr "Khi bạn xem một cuốn sách trên Anna’s Archive, bạn có thể thấy các trường khác nhau: tiêu đề, tác giả, nhà xuất bản, ấn bản, năm, mô tả, tên tệp, và nhiều hơn nữa. Tất cả những thông tin này được gọi là <em>dữ liệu số</em>."

msgid "page.metadata.background.body2"
msgstr "Vì chúng tôi kết hợp sách từ các <em>thư viện nguồn</em> khác nhau, chúng tôi hiển thị bất kỳ dữ liệu số nào có sẵn trong thư viện nguồn đó. Ví dụ, đối với một cuốn sách mà chúng tôi lấy từ Library Genesis, chúng tôi sẽ hiển thị tiêu đề từ cơ sở dữ liệu của Library Genesis."

msgid "page.metadata.background.body3"
msgstr "Đôi khi một cuốn sách có mặt trong <em>nhiều</em> thư viện nguồn, có thể có các trường dữ liệu số khác nhau. Trong trường hợp đó, chúng tôi đơn giản hiển thị phiên bản dài nhất của mỗi trường, vì phiên bản đó hy vọng chứa thông tin hữu ích nhất! Chúng tôi vẫn sẽ hiển thị các trường khác dưới phần mô tả, ví dụ như “tiêu đề thay thế” (nhưng chỉ khi chúng khác nhau)."

msgid "page.metadata.background.body4"
msgstr "Chúng tôi cũng trích xuất <em>mã</em> như các định danh và phân loại từ thư viện nguồn. <em>Định danh</em> đại diện duy nhất cho một ấn bản cụ thể của một cuốn sách; ví dụ là ISBN, DOI, mã ID Open Library, mã ID Google Sách, hoặc mã ID Amazon. <em>Phân loại</em> nhóm các cuốn sách tương tự lại với nhau; ví dụ là Dewey Decimal (DCC), UDC, LCC, RVK, hoặc GOST. Đôi khi các mã này được liên kết rõ ràng trong các thư viện nguồn, và đôi khi chúng tôi có thể trích xuất chúng từ tên tệp hoặc mô tả (chủ yếu là ISBN và DOI)."

msgid "page.metadata.background.body5"
msgstr "Chúng tôi có thể sử dụng các định danh để tìm các bản ghi trong <em>các bộ sưu tập chỉ có dữ liệu số</em>, như OpenLibrary, ISBNdb, hoặc WorldCat/OCLC. Có một <em>tab dữ liệu số</em> cụ thể trong công cụ tìm kiếm của chúng tôi nếu bạn muốn duyệt qua các bộ sưu tập đó. Chúng tôi sử dụng các bản ghi khớp để điền vào các trường dữ liệu số còn thiếu (ví dụ nếu thiếu tiêu đề), hoặc ví dụ như “tiêu đề thay thế” (nếu có tiêu đề hiện có)."

msgid "page.metadata.background.body6"
msgstr "Để xem chính xác dữ liệu số của một cuốn sách đến từ đâu, hãy xem tab <em>“Chi tiết kỹ thuật”</em> trên trang sách. Nó có một liên kết đến JSON thô cho cuốn sách đó, với các chỉ dẫn đến JSON thô của các bản ghi gốc."

msgid "page.metadata.background.body7"
msgstr "Để biết thêm thông tin, hãy xem các trang sau: <a %(a_datasets)s>Các bộ dữ liệu</a>, <a %(a_search_metadata)s>Tìm kiếm (tab dữ liệu số)</a>, <a %(a_codes)s>Khám phá mã</a>, và <a %(a_example)s>Ví dụ JSON dữ liệu số</a>. Cuối cùng, tất cả dữ liệu số của chúng tôi có thể được <a %(a_generated)s>tạo ra</a> hoặc <a %(a_downloaded)s>tải xuống</a> dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB."

msgid "page.metadata.openlib.title"
msgstr "Liên kết Open Library"

msgid "page.metadata.openlib.body1"
msgstr "Vậy nếu bạn gặp một tệp có dữ liệu số xấu, bạn nên sửa nó như thế nào? Bạn có thể đi đến thư viện nguồn và làm theo các quy trình của nó để sửa dữ liệu số, nhưng phải làm gì nếu một tệp có mặt trong nhiều thư viện nguồn?"

msgid "page.metadata.openlib.body2"
msgstr "Có một định danh được coi là đặc biệt trên Lưu Trữ của Anna. <strong>Trường annas_archive md5 trên Open Library luôn ghi đè tất cả các dữ liệu số khác!</strong> Hãy lùi lại một chút và tìm hiểu về Open Library."

msgid "page.metadata.openlib.body3"
msgstr "Open Library được thành lập vào năm 2006 bởi Aaron Swartz với mục tiêu “một trang web cho mỗi cuốn sách từng được xuất bản”. Nó giống như một Wikipedia cho dữ liệu số sách: mọi người đều có thể chỉnh sửa, nó được cấp phép tự do, và có thể tải xuống hàng loạt. Đây là cơ sở dữ liệu sách phù hợp nhất với sứ mệnh của chúng tôi — thực tế, Lưu Trữ của Anna đã được truyền cảm hứng từ tầm nhìn và cuộc đời của Aaron Swartz."

msgid "page.metadata.openlib.body4"
msgstr "Thay vì phát minh lại điều đó, chúng tôi quyết định hướng các tình nguyện viên của mình về phía Open Library. Nếu bạn thấy một cuốn sách có dữ liệu số không chính xác, bạn có thể giúp đỡ theo cách sau:"

msgid "page.metadata.openlib.howto.item.1"
msgstr " Đi đến <a %(a_openlib)s>trang web Open Library</a>."

msgid "page.metadata.openlib.howto.item.2"
msgstr "Tìm bản ghi sách chính xác. <strong>CẢNH BÁO:</strong> hãy chắc chắn chọn đúng <strong>ấn bản</strong>. Trong Open Library, bạn có “tác phẩm” và “ấn bản”."

msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Một “tác phẩm” có thể là “Harry Potter và Hòn đá Phù thủy”."

msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Một “ấn bản” có thể là:"

msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Ấn bản đầu tiên năm 1997 được xuất bản bởi Bloomsbery với 256 trang."

msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Ấn bản bìa mềm năm 2003 được xuất bản bởi Raincoast Books với 223 trang."

msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Bản dịch tiếng Ba Lan năm 2000 “Harry Potter I Kamie Filozoficzn” bởi Media Rodzina với 328 trang."

msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Tất cả các ấn bản này đều có ISBN và nội dung khác nhau, vì vậy hãy chắc chắn chọn đúng ấn bản!"

msgid "page.metadata.openlib.howto.item.3"
msgstr "Chỉnh sửa bản ghi (hoặc tạo mới nếu chưa có), và thêm càng nhiều thông tin hữu ích càng tốt! Bạn đã ở đây rồi, hãy làm cho bản ghi thật tuyệt vời."

msgid "page.metadata.openlib.howto.item.4"
msgstr "Dưới mục “ID Numbers” chọn “Anna’s Archive” và thêm MD5 của cuốn sách từ Anna’s Archive. Đây là chuỗi dài các chữ cái và số sau “/md5/” trong URL."

msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Cố gắng tìm các tệp khác trong Anna’s Archive cũng khớp với bản ghi này, và thêm chúng vào. Trong tương lai, chúng ta có thể nhóm chúng lại như các bản sao trên trang tìm kiếm của Anna’s Archive."

msgid "page.metadata.openlib.howto.item.5"
msgstr "Khi bạn hoàn thành, hãy ghi lại URL mà bạn vừa cập nhật. Khi bạn đã cập nhật ít nhất 30 bản ghi với MD5 của Anna’s Archive, hãy gửi cho chúng tôi một <a %(a_contact)s>email</a> và gửi danh sách đó. Chúng tôi sẽ tặng bạn một thành viên miễn phí cho Anna’s Archive, để bạn có thể dễ dàng thực hiện công việc này (và như một lời cảm ơn vì sự giúp đỡ của bạn). Những chỉnh sửa này phải có chất lượng cao và thêm một lượng thông tin đáng kể, nếu không yêu cầu của bạn sẽ bị từ chối. Yêu cầu của bạn cũng sẽ bị từ chối nếu bất kỳ chỉnh sửa nào bị hoàn tác hoặc sửa đổi bởi các quản trị viên của Open Library."

msgid "page.metadata.openlib.body5"
msgstr "Lưu ý rằng điều này chỉ áp dụng cho sách, không phải các bài viết học thuật hoặc các loại tệp khác. Đối với các loại tệp khác, chúng tôi vẫn khuyến nghị tìm thư viện nguồn. Có thể mất vài tuần để các thay đổi được bao gồm trong Lưu Trữ của Anna, vì chúng tôi cần tải xuống bản dữ liệu mới nhất của Open Library và tái tạo chỉ mục tìm kiếm của chúng tôi."

msgid "page.mirrors.title"
msgstr "Các bản sao: kêu gọi tình nguyện viên"

msgid "page.mirrors.intro"
msgstr "Để tăng cường khả năng phục hồi của Lưu trữ của Anna, chúng tôi đang tìm kiếm các tình nguyện viên để chạy các bản sao."

msgid "page.mirrors.text1"
msgstr "Chúng tôi đang tìm kiếm điều này:"

msgid "page.mirrors.list.run_anna"
msgstr "Bạn chạy mã nguồn mở của Anna’s Archive, và bạn thường xuyên cập nhật cả mã và dữ liệu."

msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Phiên bản của bạn được phân biệt rõ ràng là một bản sao, ví dụ: “Bob’s Archive, một bản sao của Anna’s Archive”."

msgid "page.mirrors.list.know_the_risks"
msgstr "Bạn sẵn sàng chấp nhận những rủi ro liên quan đến công việc này, những rủi ro này là đáng kể. Bạn có hiểu biết sâu sắc về bảo mật hoạt động cần thiết. Nội dung của <a %(a_shadow)s>những</a> <a %(a_pirate)s>bài viết</a> này là hiển nhiên đối với bạn."

msgid "page.mirrors.list.willing_to_contribute"
msgstr "Bạn sẵn sàng đóng góp vào <a %(a_codebase)s>mã nguồn</a> của chúng tôi — hợp tác với đội ngũ của chúng tôi — để thực hiện điều này."

msgid "page.mirrors.list.maybe_partner"
msgstr "Ban đầu, chúng tôi sẽ không cung cấp cho bạn quyền truy cập vào các lượt tải xuống từ máy chủ đối tác của chúng tôi, nhưng nếu mọi việc diễn ra tốt đẹp, chúng tôi có thể chia sẻ điều đó với bạn."

msgid "page.mirrors.expenses.title"
msgstr "Chi phí lưu trữ"

msgid "page.mirrors.expenses.text1"
msgstr "Chúng tôi sẵn sàng chi trả chi phí lưu trữ và VPN, ban đầu lên đến $200 mỗi tháng. Điều này đủ cho một máy chủ tìm kiếm cơ bản và một proxy được bảo vệ bởi DMCA."

msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Chúng tôi sẽ chỉ trả tiền lưu trữ khi bạn đã thiết lập mọi thứ và chứng minh rằng bạn có thể giữ cho kho lưu trữ được cập nhật với các bản cập nhật. Điều này có nghĩa là bạn sẽ phải tự trả tiền cho 1-2 tháng đầu tiên."

msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Thời gian của bạn sẽ không được bồi thường (và của chúng tôi cũng vậy), vì đây là công việc tình nguyện hoàn toàn."

msgid "page.mirrors.expenses.maybe_donation"
msgstr "Nếu bạn tham gia đáng kể vào việc phát triển và vận hành công việc của chúng tôi, chúng tôi có thể thảo luận về việc chia sẻ thêm doanh thu từ quyên góp với bạn, để bạn triển khai khi cần thiết."

msgid "page.mirrors.getting_started.title"
msgstr "Bắt đầu"

msgid "page.mirrors.getting_started.text1"
msgstr "Vui lòng <strong>không liên hệ với chúng tôi</strong> để xin phép, hoặc hỏi các câu hỏi cơ bản. Hành động nói lên nhiều hơn lời nói! Tất cả thông tin đều có sẵn, vì vậy hãy tiến hành thiết lập bản sao của bạn."

msgid "page.mirrors.getting_started.text2"
msgstr "Hãy thoải mái đăng vé hoặc yêu cầu hợp nhất lên Gitlab của chúng tôi khi bạn gặp sự cố. Chúng tôi có thể cần xây dựng một số tính năng cụ thể cho bản sao với bạn, chẳng hạn như đổi thương hiệu từ “Anna’s Archive” sang tên trang web của bạn, (ban đầu) vô hiệu hóa tài khoản người dùng, hoặc liên kết trở lại trang chính của chúng tôi từ các trang sách."

msgid "page.mirrors.getting_started.text3"
msgstr "Khi bạn đã có bản sao của mình hoạt động, vui lòng liên hệ với chúng tôi. Chúng tôi rất muốn xem xét bảo mật của bạn, và khi điều đó ổn định, chúng tôi sẽ liên kết đến bản sao của bạn và bắt đầu làm việc chặt chẽ hơn với bạn."

msgid "page.mirrors.getting_started.text4"
msgstr "Cảm ơn trước bất kỳ ai sẵn lòng đóng góp theo cách này! Đây không phải là việc dành cho những người yếu tim, nhưng nó sẽ củng cố sự tồn tại lâu dài của thư viện mở lớn nhất trong lịch sử loài người."

msgid "page.partner_download.header"
msgstr "Tải xuống từ trang web đối tác"

msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Tải xuống chậm chỉ có sẵn thông qua trang web chính thức. Truy cập %(websites)s."

msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Tải xuống chậm không có sẵn thông qua VPN của Cloudflare hoặc từ các địa chỉ IP của Cloudflare."

msgid "page.partner_download.wait_banner"
msgstr "Vui lòng chờ <span %(span_countdown)s>%(wait_seconds)s</span> giây để tải xuống tệp này."

msgid "page.partner_download.url"
msgstr "<a %(a_download)s>📚 Tải xuống ngay bây giờ</a>"

msgid "page.partner_download.li4"
msgstr "Cảm ơn bạn đã chờ đợi, điều này giúp trang web có thể truy cập miễn phí cho mọi người! 😊"

msgid "page.partner_download.warning_many_downloads"
msgstr "Cảnh báo: đã có nhiều lượt tải xuống từ địa chỉ IP của bạn trong 24 giờ qua. Tốc độ tải xuống có thể chậm hơn bình thường."

msgid "page.partner_download.downloads_last_24_hours"
msgstr "Lượt tải xuống từ địa chỉ IP của bạn trong 24 giờ qua: %(count)s."

msgid "page.partner_download.warning_many_downloads2"
msgstr "Nếu bạn đang sử dụng VPN, kết nối internet chia sẻ hoặc ISP của bạn chia sẻ IP, cảnh báo này có thể do điều đó."

msgid "page.partner_download.wait"
msgstr "Để mọi người có cơ hội tải xuống tệp miễn phí, bạn cần chờ trước khi có thể tải xuống tệp này."

msgid "page.partner_download.li1"
msgstr "Hãy tiếp tục duyệt Anna’s Archive trong một tab khác trong khi chờ đợi (nếu trình duyệt của bạn hỗ trợ làm mới các tab nền)."

msgid "page.partner_download.li2"
msgstr "Hãy chờ đợi để nhiều trang tải xuống cùng lúc (nhưng vui lòng chỉ tải xuống một tệp cùng lúc trên mỗi máy chủ)."

msgid "page.partner_download.li3"
msgstr "Khi bạn nhận được liên kết tải xuống, nó sẽ có hiệu lực trong vài giờ."

msgid "layout.index.header.title"
msgstr "Anna's Archive"

msgid "page.scidb.header"
msgstr "SciDB"

msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

msgid "page.scidb.aa_record"
msgstr "Ghi chép trong Lưu trữ của Anna"

msgid "page.scidb.download"
msgstr "Tải xuống"

msgid "page.scidb.scihub"
msgstr "Sci-Hub"

msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

msgid "page.scidb.please_donate"
msgstr "Để hỗ trợ khả năng truy cập và bảo tồn lâu dài kiến thức của con người, hãy trở thành <a %(a_donate)s>thành viên</a>."

msgid "page.scidb.please_donate_bonus"
msgstr "Như một phần thưởng, 🧬&nbsp;SciDB tải nhanh hơn cho các thành viên, không có bất kỳ giới hạn nào."

msgid "page.scidb.refresh"
msgstr "Không hoạt động? Hãy thử <a %(a_refresh)s>làm mới</a>."

msgid "page.scidb.no_preview_new"
msgstr "Chưa có bản xem trước. Tải tệp từ <a %(a_path)s>Lưu Trữ của Anna</a>."

msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB là sự tiếp nối của Sci-Hub, với giao diện quen thuộc và xem trực tiếp các tệp PDF. Nhập DOI của bạn để xem."

msgid "page.home.scidb.text3"
msgstr "Chúng tôi có toàn bộ sưu tập của Sci-Hub, cũng như các tài liệu mới. Hầu hết có thể được xem trực tiếp với giao diện quen thuộc, tương tự như Sci-Hub. Một số có thể được tải xuống thông qua các nguồn bên ngoài, trong trường hợp đó chúng tôi sẽ hiển thị liên kết đến những nguồn đó."

msgid "page.search.title.results"
msgstr "%(search_input)s - Tìm kiếm"

msgid "page.search.title.new"
msgstr "Tìm kiếm mới"

msgid "page.search.icon.include_only"
msgstr "Chỉ bao gồm"

msgid "page.search.icon.exclude"
msgstr "Loại trừ"

msgid "page.search.icon.unchecked"
msgstr "Chưa kiểm tra"

msgid "page.search.tabs.download"
msgstr "Tải xuống"

msgid "page.search.tabs.journals"
msgstr "Bài báo tạp chí"

msgid "page.search.tabs.digital_lending"
msgstr "Cho mượn kỹ thuật số"

msgid "page.search.tabs.metadata"
msgstr "Dữ liệu số"

msgid "common.search.placeholder"
msgstr "Tên, tác giả, DOI, ISBN, MD5,.…"

msgid "common.search.submit"
msgstr "Tìm kiếm"

msgid "page.search.search_settings"
msgstr "Thiết lập tìm kiếm"

msgid "page.search.submit"
msgstr "Tìm kiếm"

msgid "page.search.too_long_broad_query"
msgstr "Quá trình tìm kiếm mất quá nhiều thời gian, điều này thường xảy ra với các truy vấn rộng. Số lượng bộ lọc có thể không chính xác."

msgid "page.search.too_inaccurate"
msgstr "Quá trình tìm kiếm mất quá nhiều thời gian, và bạn có thể thấy kết quả không chính xác. Đôi khi việc <a %(a_reload)s>tải lại</a> trang sẽ hữu ích."

msgid "page.search.filters.display.header"
msgstr "Hiển thị"

msgid "page.search.filters.display.list"
msgstr "Danh sách"

msgid "page.search.filters.display.table"
msgstr "Bảng"

msgid "page.search.advanced.header"
msgstr "Tuỳ chọn nâng cao"

msgid "page.search.advanced.description_comments"
msgstr "Tìm kiếm mô tả và các nhận xét dữ liệu"

msgid "page.search.advanced.add_specific"
msgstr "Thêm trường tìm kiếm cụ thể"

msgid "common.specific_search_fields.select"
msgstr "(tìm kiếm mục cụ thể)"

msgid "page.search.advanced.field.year_published"
msgstr "Năm xuất bản"

msgid "page.search.filters.content.header"
msgstr "Nội dung"

msgid "page.search.filters.filetype.header"
msgstr "Loại tệp"

msgid "page.search.more"
msgstr "thêm nữa.…"

msgid "page.search.filters.access.header"
msgstr "Truy cập"

msgid "page.search.filters.source.header"
msgstr "Nguồn"

msgid "page.search.filters.source.scraped"
msgstr "dược thu thập dữ liệu và tạo thành nguồn mở bởi Anna's Archive"

msgid "page.search.filters.language.header"
msgstr "Ngôn ngữ"

msgid "page.search.filters.order_by.header"
msgstr "Sắp xếp bằng"

msgid "page.search.filters.sorting.most_relevant"
msgstr "Liên quan nhất"

msgid "page.search.filters.sorting.newest"
msgstr "Mới nhất"

msgid "page.search.filters.sorting.note_publication_year"
msgstr "(năm phát hành)"

msgid "page.search.filters.sorting.oldest"
msgstr "Già nhất"

msgid "page.search.filters.sorting.largest"
msgstr "Lớn nhất"

msgid "page.search.filters.sorting.note_filesize"
msgstr "(Kích thước tập tin)"

msgid "page.search.filters.sorting.smallest"
msgstr "Nhỏ nhất"

msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(mã nguồn mở)"

msgid "page.search.filters.sorting.random"
msgstr "Ngẫu nhiên"

msgid "page.search.header.update_info"
msgstr "Chỉ mục tìm kiếm được cập nhật hàng tháng. Nó hiện bao gồm các mục nhập tối đa %(last_data_refresh_date)s. Để biết thêm thông tin kỹ thuật, hãy xem trang %(link_open_tag)sdatasets</a>."

msgid "page.search.header.codes_explorer"
msgstr "Để khám phá chỉ mục tìm kiếm bằng mã, hãy sử dụng <a %(a_href)s>Khám phá Mã (Codes Explorer)</a>."

msgid "page.search.results.search_downloads"
msgstr "Nhập vào hộp để tìm kiếm danh mục các tệp có thể tải xuống trực tiếp %(count)s của chúng tôi mà chúng tôi <a %(a_preserve)s>lưu giữ mãi mãi</a>."

msgid "page.search.results.help_preserve"
msgstr "Trên thực tế, bất kỳ ai cũng có thể giúp bảo tồn các tệp này bằng cách tạo <a %(a_torrents)s>danh sách torrent thống nhất</a> của chúng tôi."

msgid "page.search.results.most_comprehensive"
msgstr "Chúng tôi hiện có danh mục sách, bài báo và các tác phẩm viết hoàn toàn mở toàn diện nhất thế giới. Chúng tôi lấy dữ liệu từ Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>và hơn thế nữa</a>."

msgid "page.search.results.other_shadow_libs"
msgstr "Nếu bạn tìm thấy thư viện chìm khác mà chúng tôi nên thu thập thêm vào hoặc nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi theo địa chỉ %(email)s."

msgid "page.search.results.dmca"
msgstr "Đối với DMCA / khiếu nại về bản quyền <a %(a_copyright)s>nhấp vào đây</a>."

msgid "page.search.results.shortcuts"
msgstr "Mẹo hay: hãy sử dụng phím tắt “/” (tiêu điểm tìm kiếm), “enter” (tìm kiếm), “j” (lên), “k” (xuống), “<” (trang trước), “>” (trang tiếp theo) để điều hướng nhanh hơn."

msgid "page.search.results.looking_for_papers"
msgstr "Đang tìm giấy tờ?"

msgid "page.search.results.search_journals"
msgstr "Nhập vào hộp để tìm kiếm danh mục các bài báo học thuật và bài báo tạp chí %(count)s mà chúng tôi <a %(a_preserve)s>lưu giữ mãi mãi</a>."

msgid "page.search.results.search_digital_lending"
msgstr "Nhập vào hộp để tìm kiếm tệp trong thư viện cho mượn trên Internet."

msgid "page.search.results.digital_lending_info"
msgstr "Chỉ mục tìm kiếm này hiện bao gồm siêu dữ liệu từ thư viện cho vay kỹ thuật số có kiểm soát của Internet Archive. <a %(a_datasets)s>Tìm hiểu thêm về tập dữ liệu của chúng tôi</a>."

msgid "page.search.results.digital_lending_info_more"
msgstr "Để biết thêm các thư viện cho mượn sách trên Internet, hãy xem <a %(a_wikipedia)s>Wikipedia</a> và <a %(a_mobileread)s>MobileRead Wiki</a>."

msgid "page.search.results.search_metadata"
msgstr "Nhập vào hộp để tìm kiếm dữ liệu từ thư viện. Điều này có thể hữu ích khi <a %(a_request)s>yêu cầu một tệp</a>."

msgid "page.search.results.metadata_info"
msgstr "Chỉ mục tìm kiếm này hiện bao gồmdữ liệu từ nhiều nguồn dữ liệu số khác nhau. <a %(a_datasets)s>Tìm hiểu thêm về tập dữ liệu của chúng tôi</a>."

msgid "page.search.results.metadata_no_merging"
msgstr "Đối với dữ liệu số, chúng tôi hiển thị các bản ghi gốc. Chúng tôi không thực hiện bất kỳ việc hợp nhất hồ sơ nào."

msgid "page.search.results.metadata_info_more"
msgstr "Có rất nhiều nguồn dữ liệu cho các tác phẩm viết trên khắp thế giới. <a %(a_wikipedia)s>Trang Wikipedia này</a> là một khởi đầu tốt, nhưng nếu bạn biết những danh sách hay khác, vui lòng cho chúng tôi biết."

msgid "page.search.results.search_generic"
msgstr "Gõ vào ô để tìm kiếm."

msgid "page.search.results.these_are_records"
msgstr "Đây là các bản ghi dữ liệu số, <span %(classname)s>không phải</span> các tệp có thể tải xuống."

msgid "page.search.results.error.header"
msgstr "Lỗi trong quá trình tìm kiếm."

msgid "page.search.results.error.unknown"
msgstr "Hãy thử <a %(a_reload)s>tải lại trang</a>. Nếu sự cố vẫn tiếp diễn, vui lòng gửi email cho chúng tôi theo địa chỉ %(email)s."

msgid "page.search.results.none"
msgstr "<span %(classname)s>Không có tệp nào được tìm thấy.</span> Hãy thử các cựm từ và bộ lộc tìm kiếm khác nhau hoặc nhỏ hơn."

msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Đôi khi điều này xảy ra không chính xác khi máy chủ tìm kiếm chậm. Trong những trường hợp như vậy, <a %(a_attrs)s>tải lại</a> có thể giúp."

msgid "page.search.found_matches.main"
msgstr "Chúng tôi đã tìm thấy kết quả phù hợp trong: %(in)s. Bạn có thể tham khảo URL được tìm thấy ở đó khi <a %(a_request)s>yêu cầu tệp</a>."

msgid "page.search.found_matches.journals"
msgstr "Bài báo (%(count)s)"

msgid "page.search.found_matches.digital_lending"
msgstr "Cho vay kỹ thuật số (%(count)s)"

msgid "page.search.found_matches.metadata"
msgstr "Dữ liệu số (%(count)s)"

msgid "page.search.results.numbers_pages"
msgstr "Kết quả %(from)s-%(to)s (tổng cộng %(total)s)"

msgid "page.search.results.partial_more"
msgstr "%(num)d+ nhứng kết quả liên quan một phần"

msgid "page.search.results.partial"
msgstr "%(num)d những kết quả liên quan một phần"

msgid "page.volunteering.title"
msgstr "Tình nguyện & Tiền thưởng"

msgid "page.volunteering.intro.text1"
msgstr "Lưu Trữ của Anna dựa vào các tình nguyện viên như bạn. Chúng tôi hoan nghênh mọi mức độ cam kết và có hai loại trợ giúp chính mà chúng tôi đang tìm kiếm:"

msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Công việc tình nguyện nhẹ:</span> nếu bạn chỉ có thể dành vài giờ ở đây và đó, vẫn có rất nhiều cách bạn có thể giúp đỡ. Chúng tôi thưởng cho các tình nguyện viên nhất quán bằng <span %(bold)s>🤝 tư cách thành viên của Thư Viện của Anna</span>."

msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Công việc tình nguyện nặng (tiền thưởng từ 50 USD đến 5,000 USD):</span> nếu bạn có thể dành nhiều thời gian và/hoặc tài nguyên cho sứ mệnh của chúng tôi, chúng tôi rất mong được làm việc chặt chẽ hơn với bạn. Cuối cùng, bạn có thể tham gia vào đội ngũ nội bộ. Mặc dù ngân sách của chúng tôi hạn chế, chúng tôi có thể trao <span %(bold)s>💰 tiền thưởng</span> cho những công việc căng thẳng nhất."

msgid "page.volunteering.intro.text2"
msgstr "Nếu bạn không thể tình nguyện thời gian của mình, bạn vẫn có thể giúp chúng tôi rất nhiều bằng cách <a %(a_donate)s>quyên góp tiền</a>, <a %(a_torrents)s>seeding các torrents của chúng tôi</a>, <a %(a_uploading)s>tải lên sách</a>, hoặc <a %(a_help)s>kể cho bạn bè về Lưu Trữ của Anna</a>."

msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Các công ty:</span> chúng tôi cung cấp quyền truy cập trực tiếp tốc độ cao vào các bộ sưu tập của chúng tôi để đổi lấy quyên góp cấp doanh nghiệp hoặc trao đổi cho các bộ sưu tập mới (ví dụ: các bản quét mới, bộ dữ liệu OCR, làm giàu dữ liệu của chúng tôi). <a %(a_contact)s>Liên hệ với chúng tôi</a> nếu bạn là đối tượng này. Xem thêm trang <a %(a_llm)s>LLM của chúng tôi</a>."

msgid "page.volunteering.section.light.heading"
msgstr "Tình nguyện nhẹ"

msgid "page.volunteering.section.light.text1"
msgstr "Nếu bạn có vài giờ rảnh rỗi, bạn có thể giúp đỡ theo nhiều cách. Hãy chắc chắn tham gia <a %(a_telegram)s>phòng chat tình nguyện trên Telegram</a>."

msgid "page.volunteering.section.light.text2"
msgstr "Như một biểu hiện của sự cảm kích, chúng tôi thường tặng 6 tháng “Thủ Thư May Mắn” cho các cột mốc cơ bản, và nhiều hơn cho công việc tình nguyện liên tục. Tất cả các cột mốc đều yêu cầu công việc chất lượng cao — công việc cẩu thả gây hại cho chúng tôi nhiều hơn là giúp đỡ và chúng tôi sẽ từ chối nó. Vui lòng <a %(a_contact)s>email cho chúng tôi</a> khi bạn đạt được một cột mốc."

msgid "page.volunteering.table.header.task"
msgstr "Nhiệm vụ"

msgid "page.volunteering.table.header.milestone"
msgstr "Cột mốc"

msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Lan truyền thông tin về Lưu trữ của Anna. Ví dụ, bằng cách giới thiệu sách trên Lưu trữ của Anna, liên kết đến các bài viết trên blog của chúng tôi, hoặc chỉ đơn giản là hướng dẫn mọi người đến trang web của chúng tôi."

msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s liên kết hoặc ảnh chụp màn hình."

msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Những điều này nên cho thấy bạn thông báo cho ai đó về Lưu trữ của Anna, và họ cảm ơn bạn."

msgid "page.volunteering.table.open_library.task"
msgstr "Cải thiện dữ liệu số bằng cách <a %(a_metadata)s>liên kết</a> với Open Library."

msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Bạn có thể sử dụng <a %(a_list)s>danh sách các vấn đề metadata ngẫu nhiên</a> làm điểm khởi đầu."

msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Hãy chắc chắn để lại bình luận về các vấn đề bạn đã sửa, để người khác không lặp lại công việc của bạn."

msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s liên kết của các bản ghi bạn đã cải thiện."

msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Dịch</a> trang web."

msgid "page.volunteering.table.translate.milestone"
msgstr "Dịch hoàn toàn một ngôn ngữ (nếu nó chưa gần hoàn thành.)"

msgid "page.volunteering.table.wikipedia.task"
msgstr "Cải thiện trang Wikipedia cho Anna’s Archive bằng ngôn ngữ của bạn. Bao gồm thông tin từ trang Wikipedia của AA bằng các ngôn ngữ khác, và từ trang web và blog của chúng tôi. Thêm các tham chiếu đến AA trên các trang liên quan khác."

msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Liên kết đến lịch sử chỉnh sửa cho thấy bạn đã đóng góp đáng kể."

msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Đáp ứng yêu cầu sách (hoặc tài liệu, v.v.) trên các diễn đàn Z-Library hoặc Library Genesis. Chúng tôi không có hệ thống yêu cầu sách riêng, nhưng chúng tôi bản sao các thư viện đó, vì vậy làm cho chúng tốt hơn cũng làm cho Lưu Trữ của Anna tốt hơn."

msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s liên kết hoặc ảnh chụp màn hình của các yêu cầu bạn đã hoàn thành."

msgid "page.volunteering.table.misc.task"
msgstr "Các nhiệm vụ nhỏ được đăng trên <a %(a_telegram)s>phòng chat tình nguyện trên Telegram</a> của chúng tôi. Thường xuyên là cho thành viên, đôi khi là cho các khoản tiền thưởng nhỏ."

msgid "page.volunteering.table.misc.task2"
msgstr "Các nhiệm vụ nhỏ được đăng trong nhóm trò chuyện tình nguyện của chúng tôi."

msgid "page.volunteering.table.misc.milestone"
msgstr "Tùy thuộc vào nhiệm vụ."

msgid "page.volunteering.section.bounties.heading"
msgstr "Tiền thưởng"

msgid "page.volunteering.section.bounties.text1"
msgstr "Chúng tôi luôn tìm kiếm những người có kỹ năng lập trình vững chắc hoặc an ninh mạng tấn công để tham gia. Bạn có thể đóng góp quan trọng trong việc bảo tồn di sản của nhân loại."

msgid "page.volunteering.section.bounties.text2"
msgstr "Như một lời cảm ơn, chúng tôi tặng thành viên cho những đóng góp vững chắc. Như một lời cảm ơn lớn, chúng tôi tặng tiền thưởng cho những nhiệm vụ đặc biệt quan trọng và khó khăn. Điều này không nên được xem như là một công việc thay thế, nhưng nó là một động lực thêm và có thể giúp bù đắp chi phí phát sinh."

msgid "page.volunteering.section.bounties.text3"
msgstr "Hầu hết mã của chúng tôi là mã nguồn mở, và chúng tôi cũng sẽ yêu cầu mã của bạn là mã nguồn mở khi trao thưởng. Có một số ngoại lệ mà chúng tôi có thể thảo luận trên cơ sở từng cá nhân."

msgid "page.volunteering.section.bounties.text4"
msgstr "Tiền thưởng được trao cho người đầu tiên hoàn thành nhiệm vụ. Hãy thoải mái bình luận trên vé tiền thưởng để cho người khác biết bạn đang làm gì, để người khác có thể tạm dừng hoặc liên hệ với bạn để hợp tác. Nhưng hãy lưu ý rằng người khác vẫn có thể làm việc trên đó và cố gắng vượt qua bạn. Tuy nhiên, chúng tôi không trao thưởng cho công việc cẩu thả. Nếu có hai bài nộp chất lượng cao được thực hiện gần nhau (trong vòng một hoặc hai ngày), chúng tôi có thể chọn trao thưởng cho cả hai, theo quyết định của chúng tôi, ví dụ 100%% cho bài nộp đầu tiên và 50%% cho bài nộp thứ hai (tổng cộng là 150%%)."

msgid "page.volunteering.section.bounties.text5"
msgstr "Đối với các tiền thưởng lớn hơn (đặc biệt là tiền thưởng scraping), vui lòng liên hệ với chúng tôi khi bạn đã hoàn thành khoảng 5%% của nó, và bạn tự tin rằng phương pháp của bạn sẽ mở rộng đến cột mốc đầy đủ. Bạn sẽ phải chia sẻ phương pháp của mình với chúng tôi để chúng tôi có thể đưa ra phản hồi. Ngoài ra, bằng cách này chúng tôi có thể quyết định phải làm gì nếu có nhiều người đang tiến gần đến một tiền thưởng, chẳng hạn như có thể trao thưởng cho nhiều người, khuyến khích mọi người hợp tác, v.v."

msgid "page.volunteering.section.bounties.text6"
msgstr "CẢNH BÁO: các nhiệm vụ có tiền thưởng cao <span %(bold)s>khó khăn</span> — có thể bạn nên bắt đầu với những nhiệm vụ dễ hơn."

msgid "page.volunteering.section.bounties.text7"
msgstr "Đi đến <a %(a_gitlab)s>danh sách vấn đề trên Gitlab</a> của chúng tôi và sắp xếp theo “Ưu tiên nhãn”. Điều này cho thấy thứ tự các nhiệm vụ mà chúng tôi quan tâm. Các nhiệm vụ không có tiền thưởng rõ ràng vẫn đủ điều kiện để nhận thành viên, đặc biệt là những nhiệm vụ được đánh dấu “Đã chấp nhận” và “Yêu thích của Anna”. Bạn có thể muốn bắt đầu với một “Dự án khởi đầu”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Cập nhật về <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, thư viện mở thực sự lớn nhất trong lịch sử loài người."

msgid "layout.index.title"
msgstr "Anna’s Archive"

msgid "layout.index.meta.description"
msgstr "Thư viện dữ liệu mở nguồn mở lớn nhất thế giới. Mirrors Sci-Hub, Library Genesis, Z-Library, và hơn nữa."

msgid "layout.index.meta.opensearch"
msgstr "Tìm kiếm thư viện của Anna"

msgid "layout.index.header.banner.fundraiser.help"
msgstr "Lưu Trữ của Anna cần sự giúp đỡ của bạn!"

msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Nhiều người cố gắng hạ gục chúng tôi, nhưng chúng tôi chống trả."

msgid "layout.index.header.banner.fundraiser.now"
msgstr "Nếu bạn quyên góp ngay bây giờ, bạn sẽ nhận được <strong>gấp đôi</strong> số lượt tải nhanh."

msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Có hiệu lực đến hết tháng này."

msgid "layout.index.header.nav.donate"
msgstr "Quyên góp"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Tiết kiệm kiến thức nhân loại: Món quà tuyệt vời dịp nghỉ lễ!"

msgid "layout.index.header.banner.surprise"
msgstr "Gây ngạc nhiên cho người thân yêu, cung cấp cho họ một tài khoản có tư cách thành viên."

msgid "layout.index.header.banner.mirrors"
msgstr "Để tăng cường khả năng phục hồi của Lưu Trữ Anna, chúng tôi đang tìm kiếm các tình nguyện viên để chạy các bản sao."

msgid "layout.index.header.banner.valentine_gift"
msgstr "Quà tặng này Valentine hoàn hảo!"

msgid "layout.index.header.banner.new_donation_method"
msgstr "Chúng tôi có một phương thức quyên góp mới: %(method_name)s. Vui lòng cân nhắc %(donate_link_open_tag)sviệc quyên góp</a> — việc điều hành trang web này không hề rẻ và khoản đóng góp của bạn thực sự tạo nên sự khác biệt. Cảm ơn bạn rất nhiều."

msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Chúng tôi đang tổ chức một chiến dịch gây quỹ để <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">hỗ trợ</a> lưu giữ bộ truyện tranh lớn nhất tren một thư viện chìm trên thế giới. Cảm ơn vì sự hỗ trợ của bạn! <a href=\"/donate\">Quyên góp.</a> Nếu bạn không thể quyên góp, hãy cân nhắc ủng hộ chúng tôi bằng cách giới thiệu với bạn bè của bạn và theo dõi chúng tôi trên <a href=\"https://www.reddit.com/r /Annas_Archive\">Reddit</a> hoặc <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

msgid "layout.index.header.recent_downloads"
msgstr "Tải xuống gần đây:"

msgid "layout.index.header.nav.search"
msgstr "Tìm kiếm"

msgid "layout.index.header.nav.faq"
msgstr "Những câu hỏi thường gặp"

msgid "layout.index.header.nav.improve_metadata"
msgstr "Cải thiện dữ liệu số"

msgid "layout.index.header.nav.volunteering"
msgstr "Tình nguyện & Tiền thưởng"

msgid "layout.index.header.nav.datasets"
msgstr "Bộ dữ liệu"

msgid "layout.index.header.nav.torrents"
msgstr "Tải về qua Torrent"

msgid "layout.index.header.nav.activity"
msgstr "Hoạt động"

msgid "layout.index.header.nav.codes"
msgstr "Khám phá Mã"

msgid "layout.index.header.nav.llm_data"
msgstr "Dữ liệu cho mô hình ngôn ngữ lớn"

msgid "layout.index.header.nav.home"
msgstr "Trang Chủ"

msgid "layout.index.header.nav.annassoftware"
msgstr "Phần mềm của Anna ↗"

msgid "layout.index.header.nav.translate"
msgstr "Phiên dịch ↗"

msgid "layout.index.header.nav.login_register"
msgstr "Đăng nhập / Đăng ký"

msgid "layout.index.header.nav.account"
msgstr "Tài khoản"

msgid "layout.index.footer.list1.header"
msgstr "Kho lưu trữ của Anna"

msgid "layout.index.footer.list2.header"
msgstr "Liên hệ"

msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / khiếu nại bản quyền"

msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

msgid "layout.index.header.nav.advanced"
msgstr "Nâng cao"

msgid "layout.index.header.nav.security"
msgstr "Bảo mật"

msgid "layout.index.footer.list3.header"
msgstr "Dự phòng"

msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "không liên kết"

msgid "page.search.results.issues"
msgstr "❌ Tệp này có thể có vấn đề."

msgid "page.search.results.fast_download"
msgstr "Tải về nhanh"

msgid "page.donate.copy"
msgstr "chép"

msgid "page.donate.copied"
msgstr "đã chép!"

msgid "page.search.pagination.prev"
msgstr "Trước"

msgid "page.search.pagination.numbers_spacing"
msgstr "…"

msgid "page.search.pagination.next"
msgstr "Tiếp theo"

#~ msgid "page.donate.pix.header"
#~ msgstr "Pix"

#~ msgid "page.donate.alipay.url"
#~ msgstr "URL"

#~ msgid "page.donate.alipay.header"
#~ msgstr "Alipay 支付宝"

#~ msgid "page.donate.duration.into"
#~ msgstr ""

#~ msgid "page.home.intro"
#~ msgstr ""

#~ msgid "page.account.logged_in.membership_some"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_out"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_logged_in"
#~ msgstr ""

#~ msgid "common.md5_content_type_mapping.book_any"
#~ msgstr "Sách (Bất kì)"

#~ msgid "layout.index.header.nav.home"
#~ msgstr ""

#~ msgid "page.md5.box.download.zlib_tor_extra"
#~ msgstr ""

#~ msgid "page.isbn.title"
#~ msgstr "ISBN %(isbn_input)s"

#~ msgid "page.isbn.breadcrumbs"
#~ msgstr ""

#~ msgid "page.isbn.invalid.header"
#~ msgstr "Không tìm thấy"

#~ msgid "page.isbn.invalid.text"
#~ msgstr ""

#~ msgid "page.isbn.results.text"
#~ msgstr ""

#~ msgid "page.isbn.results.none"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.results_more"
#~ msgstr "Tìm kiếm ▶ %(num)d+ kết quả cho <span class=\"italic\">%(search_input)s</span> (trong siêu dữ liệu thư viện bóng tối)"

#~ msgid "page.search.breadcrumbs.results"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.error"
#~ msgstr ""

#~ msgid "page.search.breadcrumbs.new"
#~ msgstr "Tìm kiếm ▶ Tìm kiếm mới"

#~ msgid "page.donate.header.text3"
#~ msgstr "Bạn cũng có thể quyên góp mà không cần tạo tài khoản:"

#~ msgid "page.donate.buttons.one_time"
#~ msgstr "Quyên góp một lần duy nhất (không lợi thế)"

#~ msgid "page.donate.one_time_payment.intro"
#~ msgstr "Chọn một phương thức thanh toán. Hãy cân nhắc sử dụng phương thức thanh toán dựa trên tiền điện tử %(bitcoin_icon)s vì chúng tôi phải chịu ít phí hơn."

#~ msgid "page.donate.crypto.intro"
#~ msgstr "Nếu bạn đã có tiền điện tử thì đây là địa chỉ của chúng tôi."

#~ msgid "page.donate.text_thank_you"
#~ msgstr "Cảm ơn bạn rất nhiều vì đã giúp đỡ! Dự án này sẽ không thể thực hiện được nếu không có bạn."

#~ msgid "page.donate.one_time_payment.paypal.text1"
#~ msgstr "Để quyên góp bằng PayPal US, chúng tôi sẽ sử dụng PayPal Crypto để cho phép chúng tôi ẩn danh. Chúng tôi đánh giá cao việc bạn dành thời gian tìm hiểu cách quyên góp bằng phương pháp này vì nó giúp ích cho chúng tôi rất nhiều."

#~ msgid "page.donate.one_time_payment.paypal.text3"
#~ msgstr "Làm theo hướng dẫn để mua Bitcoin (BTC). Bạn chỉ cần mua số tiền mà bạn muốn quyên góp."

#~ msgid "page.donate.one_time_payment.paypal.text3b"
#~ msgstr "Nếu bạn mất một số Bitcoin do biến động hoặc phí, <em>xin đừng lo lắng</em>. Điều đó là bình thường với tiền điện tử nhưng nó cho phép chúng tôi hoạt động ẩn danh."

#~ msgid "page.donate.one_time_payment.paypal.text5"
#~ msgstr "Nhập địa chỉ Bitcoin (BTC) của chúng tôi làm người nhận và làm theo hướng dẫn để gửi khoản quyên góp của bạn:"

#~ msgid "page.donate.one_time_payment.alipay.text"
#~ msgstr "Vui lòng sử dụng <a %(a_account)s>tài khoản Alipay này</a> để gửi khoản đóng góp của bạn."

#~ msgid "page.donate.one_time_payment.pix.text"
#~ msgstr "Vui lòng sử dụng <a %(a_account)s>tài khoản Pix này</a> để gửi khoản quyên góp của bạn."

#~ msgid "page.donate.faq.text_other_payment2"
#~ msgstr ""

#~ msgid "page.search.results.error.text"
#~ msgstr ""

#~ msgid "page.donate.login"
#~ msgstr "Để trở thành một thành viên, xin vui lòng <a href=\"/login\">Đăng nhập hoặc đăng ký</a>. Nếu bạn không muốn tạo tài khoản, hãy chọn “Quyên góp một lần duy nhất” ở phía trên. Cảm ơn vì sự giúp đỡ!"

#~ msgid "layout.index.footer.list1.home"
#~ msgstr "Trang chủ"

#~ msgid "layout.index.footer.list1.about"
#~ msgstr "Thông tin"

#~ msgid "layout.index.footer.list1.donate"
#~ msgstr "Quyên góp"

#~ msgid "layout.index.footer.list1.datasets"
#~ msgstr "Cơ sở dữ liệu"

#~ msgid "layout.index.footer.list1.mobile"
#~ msgstr "Ứng dụng di động"

#~ msgid "layout.index.footer.list2.blog"
#~ msgstr "Blog của Anna"

#~ msgid "layout.index.footer.list2.software"
#~ msgstr "Phần mềm của Anna"

#~ msgid "layout.index.footer.list2.translate"
#~ msgstr "Phiên dịch"

#~ msgid "layout.index.footer.list2.twitter"
#~ msgstr "Twitter"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr ""

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr "DOI %(doi_input)s"

#~ msgid "page.doi.breadcrumbs"
#~ msgstr ""

#~ msgid "page.doi.invalid.header"
#~ msgstr "Không tìm thấy"

#~ msgid "page.doi.invalid.text"
#~ msgstr ""

#~ msgid "page.doi.box.header"
#~ msgstr "doi:%(doi_input)s"

#~ msgid "page.doi.box.canonical_url"
#~ msgstr "URL chuẩn: %(link)s"

#~ msgid "page.doi.box.scihub"
#~ msgstr ""

#~ msgid "page.doi.results.text"
#~ msgstr "Các tệp phù hợp trong cơ sở dữ liệu của chúng tôi:"

#~ msgid "page.doi.results.none"
#~ msgstr "Không có tài liệu nào được tìm thấy giống với những gì đã nhập."

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr "Bận đã hết số lượt tải về nhanh. Hãy nâng cấp mức thành viên của bạn qua %(email)s để có thể nhận được nhiều lượt hơn."

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Tôi có thể đóng góp theo những cách khác không?</div> Có thể chứ! Xem <a href=\"/about\">trang giới thiệu</a> trong phần “Các cách trợ giúp”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Tôi không thích việc bạn đang \"kiếm tiền\" từ Anna's Archive!</div> Nếu bạn không thích cách chúng tôi vận hành dự án của mình, hãy chạy thư viện của riêng bạn! Tất cả mã và dữ liệu của chúng tôi đều là nguồn mở nên không có gì ngăn cản bạn. ;)"

#~ msgid "page.request.title"
#~ msgstr "Yêu cầu sách"

#~ msgid "page.request.text1"
#~ msgstr ""

#~ msgid "page.request.text2"
#~ msgstr ""

#~ msgid "page.request.text3"
#~ msgstr ""

#~ msgid "page.upload.title"
#~ msgstr "Tải lên"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr "Về chúng tôi"

#~ msgid "page.about.header"
#~ msgstr "Về chúng tôi"

#~ msgid "page.home.search.header"
#~ msgstr "Tìm kiếm"

#~ msgid "page.home.search.intro"
#~ msgstr "Tìm kiếm danh mục của chúng tôi."

#~ msgid "page.home.random_book.header"
#~ msgstr "Sách ngẫu nhiên"

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr "Sách ngẫu nhiên"

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr ""

#~ msgid "page.home.explore.header"
#~ msgstr "Tìm kiếm sách"

#~ msgid "page.home.explore.intro"
#~ msgstr ""

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr "Về chúng tôi"

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr "Ứng dụng di động"

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr "Yêu cầu sách"

#~ msgid "layout.index.header.nav.upload"
#~ msgstr "Tải lên"

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr "Làm thế nào để giúp đỡ"

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Quyên góp tổng số tiền %(total)s bằng <a %(a_account)s>tài khoản Alipay này"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr "\"Gương\": kêu gọi tình nguyện viên"

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "chỉ trong tháng này!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub đã <a %(a_closed)s>tạm dừng</a> việc tải lên các bài báo mới."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Hãy lựa chọn phương pháp thanh toán. Chúng mình có ưu đãi khi bạn sử dụng những thanh toán bằng crypto %(bitcoin_icon)s, vì chúng mình bị mắc phí ít hơn (rất nhiều)."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Chọn một phương thức thanh toán. Chúng tôi hiện chỉ có các phương thức thanh toán dựa trên tiền điện tử %(bitcoin_icon)s vì các nhà xử lý thanh toán truyền thống từ chối làm việc với chúng tôi."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Chúng tôi không thể hỗ trợ thẻ tín dụng/thẻ ghi nợ trực tiếp, vì các ngân hàng không muốn làm việc với chúng tôi. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Tuy nhiên, có một số cách để sử dụng thẻ tín dụng/thẻ ghi nợ, thông qua các phương thức thanh toán khác của chúng tôi:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Tải xuống chậm từ bên ngoài"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Lượt tải xuống"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Nếu bạn đang sử dụng tiền điện tử lần đầu tiên, chúng tôi khuyên bạn nên sử dụng %(option1)s, %(option2)s hoặc %(option3)s để mua và ủng hộ Bitcoin (loại tiền điện tử đầu tiên và được sử dụng nhiều nhất)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 liên kết của các bản ghi bạn đã cải thiện."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 liên kết hoặc ảnh chụp màn hình."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 liên kết hoặc ảnh chụp màn hình của các yêu cầu bạn đã đáp ứng."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Nếu bạn quan tâm đến việc phản chiếu các datasets này cho mục đích <a %(a_faq)s>lưu trữ</a> hoặc <a %(a_llm)s>đào tạo LLM</a>, vui lòng liên hệ với chúng tôi."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Nếu bạn quan tâm đến việc sao chép bộ dữ liệu này cho mục đích <a %(a_archival)s>lưu trữ</a> hoặc <a %(a_llm)s>đào tạo LLM</a>, vui lòng liên hệ với chúng tôi."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Trang web chính"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Thông tin quốc gia ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Nếu bạn quan tâm đến việc sao chép bộ dữ liệu này cho mục đích <a %(a_archival)s>lưu trữ</a> hoặc <a %(a_llm)s>đào tạo LLM</a>, vui lòng liên hệ với chúng tôi."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Cơ quan ISBN Quốc tế thường xuyên phát hành các phạm vi mà nó đã phân bổ cho các cơ quan ISBN quốc gia. Từ đó chúng tôi có thể suy ra quốc gia, khu vực hoặc nhóm ngôn ngữ mà ISBN này thuộc về. Hiện tại chúng tôi sử dụng dữ liệu này gián tiếp, thông qua thư viện Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Tài nguyên"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Cập nhật lần cuối: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Trang web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Dữ liệu số"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Loại trừ “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Nguồn cảm hứng của chúng tôi cho việc thu thập dữ liệu số là mục tiêu của Aaron Swartz về “một trang web cho mỗi cuốn sách từng được xuất bản”, mà ông đã tạo ra <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Dự án đó đã làm tốt, nhưng vị trí độc đáo của chúng tôi cho phép chúng tôi có được dữ liệu số mà họ không thể."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Một nguồn cảm hứng khác là mong muốn của chúng tôi để biết <a %(a_blog)s>có bao nhiêu cuốn sách trên thế giới</a>, để chúng tôi có thể tính toán số lượng sách còn lại cần lưu trữ."

#~ msgid "page.partner_download.text1"
#~ msgstr "Để mọi người có cơ hội tải xuống tệp miễn phí, bạn cần đợi <strong>%(wait_seconds)s giây</strong> trước khi có thể tải xuống tệp này."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Tự động làm mới trang. Nếu bạn bỏ lỡ cửa sổ tải xuống, bộ đếm thời gian sẽ khởi động lại, vì vậy nên làm mới tự động."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Tải xuống ngay"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Chuyển đổi: sử dụng các công cụ trực tuyến để chuyển đổi giữa các định dạng. Ví dụ, để chuyển đổi giữa epub và pdf, sử dụng <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: tải xuống tệp (hỗ trợ pdf hoặc epub), sau đó <a %(a_kindle)s>gửi nó đến Kindle</a> bằng web, ứng dụng hoặc email. Công cụ hữu ích: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Hỗ trợ tác giả: Nếu bạn thích điều này và có khả năng, hãy cân nhắc mua bản gốc hoặc hỗ trợ trực tiếp cho các tác giả."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Hỗ trợ thư viện: Nếu sách này có sẵn tại thư viện địa phương của bạn, hãy cân nhắc mượn miễn phí ở đó."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Không có sẵn trực tiếp với số lượng lớn, chỉ có sẵn một phần sau tường phí"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna’s Archive quản lý một bộ sưu tập <a %(isbndb)s>dữ liệu số ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb là một công ty thu thập dữ liệu từ các cửa hàng sách trực tuyến khác nhau để tìm dữ liệu số ISBN. Anna’s Archive đã sao lưu dữ liệu số sách của ISBNdb. Dữ liệu số này có sẵn thông qua Anna’s Archive (mặc dù hiện tại không có trong tìm kiếm, trừ khi bạn tìm kiếm cụ thể số ISBN)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Để biết chi tiết kỹ thuật, xem bên dưới. Tại một thời điểm nào đó, chúng tôi có thể sử dụng nó để xác định những cuốn sách nào còn thiếu trong các thư viện bóng tối, để ưu tiên tìm và/hoặc quét những cuốn sách đó."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Bài viết blog của chúng tôi về dữ liệu này"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Thu thập dữ liệu ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Hiện tại chúng tôi có một torrent duy nhất, chứa một tệp <a %(a_jsonl)s>JSON Lines</a> 4,4GB nén gzip (20GB sau khi giải nén): “isbndb_2022_09.jsonl.gz”. Để nhập tệp “.jsonl” vào PostgreSQL, bạn có thể sử dụng một cái gì đó như <a %(a_script)s>script này</a>. Bạn thậm chí có thể truyền trực tiếp nó bằng cách sử dụng một cái gì đó như %(example_code)s để nó giải nén ngay lập tức."

#~ msgid "page.donate.wait"
#~ msgstr "Vui lòng đợi ít nhất <span %(span_hours)s>hai giờ</span> (và làm mới trang này) trước khi liên hệ với chúng tôi."

#~ msgid "page.codes.search_archive"
#~ msgstr "Tìm kiếm Anna’s Archive cho “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Đóng góp bằng Alipay hoặc WeChat. Bạn có thể chọn giữa những cách chuyển tiền này trên trang tiếp theo."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Lan truyền thông tin về Anna’s Archive trên mạng xã hội và các diễn đàn trực tuyến, bằng cách giới thiệu sách hoặc danh sách trên AA, hoặc trả lời câu hỏi."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Bộ sưu tập Hư cấu đã tách ra nhưng vẫn có các <a %(libgenli)s>torrent</a>, mặc dù không được cập nhật từ năm 2022 (chúng tôi có tải xuống trực tiếp)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Lưu trữ của Anna và Libgen.li cùng quản lý các bộ sưu tập <a %(comics)s>truyện tranh</a> và <a %(magazines)s>tạp chí</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Không có torrent nào cho các bộ sưu tập tiểu thuyết Nga và tài liệu tiêu chuẩn."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Không có torrent nào có sẵn cho nội dung bổ sung. Các torrent trên trang web Libgen.li là bản sao của các torrent khác được liệt kê ở đây. Ngoại lệ duy nhất là các torrent tiểu thuyết bắt đầu từ %(fiction_starting_point)s. Các torrent truyện tranh và tạp chí được phát hành như một sự hợp tác giữa Anna’s Archive và Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Từ một bộ sưu tập <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> nguồn gốc chính xác không rõ ràng. Một phần từ the-eye.eu, một phần từ các nguồn khác."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

