��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b :  ld �  �g 1   �i �  �i �  Fk    n �  �p y  Ur i   �s �   9t K   �t |   	u B  �u �  �w P  �y >  { �  S} �  �~ �  �� �  g� =  � ~  @� &  �� B   �   )� X   A� �  �� %   .� �  T� K   #� 4   o�    �� 3   �� N   � *   B� 
   m� <   {� 7   �� 4   �    %� S   ?� �  �� n  =� q   �� E   �    d�    t�    ��    �� !   ��    ՚ 	   �    �    �� 2   �    7� &   =� 	   d� 
   n�    |�    ��    ��      `  � �  D� �   � 1   ϡ �  � �   ӣ e  �� �   !� !   � �   >� !   � �   &� &   � �  8� !   ê �  � a   Ԭ �   6�    խ �  � �  �� _  (� �  ��    <� b   U� o   �� �   (� :   � ;   P� �   �� E   %� S   k� A   ��    � �   � o  �� N   � �   `� �   � X   ʽ �  #� P   �� �  �� �   �� �   C� m   )�   �� �   �� K   p� R  �� �   � t   �� Q   _� �   �� 
   E� �   P� y  "� K   ��    �� �   �� 4  ��    � �  � �   ��   �� �  �� �   �� �  �� I  e� k   �� j   � s   �� �   �� x   �� �   � 8   �� �   �� |   �� `   
�    n� �   �� r   3�   �� �   �� )   �� [  �� 8  � b  N� �  �� �   �� �   =� y  9� �   �� �   �� �   O� �  #� �   �� `  �� 
   �� �   � �   �� �   �� �  �� �   &� �   �� �   Y� >  %� �   d� r  � P  �� <   �� k   � �   �� n   =� �  �� 
  � �   � �   < *   � �   +  � �      �	 z   
 �   �
   T 5  Y 8  �
 K  � �   :   � �  8 T  ' l  | �  � �  m �  ) j   �  �   >!    " 0  &" i  W# �  �$ w  �& ~  .( �   �) 
   �* �  �* �  �, :  F.    �/ �   �/ q  s0   �1 �  �2   �5 w   �6     7   97 �  N9 
   ; =  %; �  c<   �= /  
A �  =B �   D t   �D T  E ;   kF    �F �  �F ^  oH �  �I @   dK K   �K �  �K �   �N �   ?O �   �O �  SP &   OR    vR 
  �R   �T �   �U S   aV �   �V -   TW s  �W G  �X    >Z 	   PZ *   ZZ K   �Z I   �Z L   [ f   h[ �   �[    m\ 9   �\ R   �\ ^   ] 3   m] F   �] @   �] �   )^    �^    �^ �  �^ �  `` 1  b U  Hc �   �d   _e �  vf    h   )h �   9i �   j �  �j �  yl �  `n ]  �p 
  Is K   Tu @   �u +   �u    
v i  w �  xy {  h{ �  �| q  �~   � U  u� 9   ˃ V  � �  \� �  �    �� (  � �  7� �   �� �  �� 4  r�    �� :   �� �   �    � 5  �� 3  œ 8  �� �   2�    ��    � A  +� N  m� K   �� �   � �  �� a  $� �   �� 2  b� �   �� u  �    �� �  �� �   M� v  <� l  �� �   �   ��    ճ 6  � p  �    �� �  �� w  Z� �  һ x  �� X  2� �  �� K   M� Q  �� %  �� �   � [   �� �   � 
   �� ?   �� &   �� -   �� #   +�    O� �   j� �  X� �  �� �  s� =  d�    �� �  ��   �� �  �� +  ~� Q  �� �  ��    �� `  ��    ��    �� I  � �  _� �  ^� ]  �    j� �  z� �  � �  ��   K� �  X� �   �   $    	  ( �   2	   �	 &  �
 �   � �   �   � �  �    b �   | W   R \   �     :    B   Y   � 4  �   � 4   � r    �   �"    v# �   �# O   f$ �   �$ �   M% 6   I& v   �&    �& �  ' �  �( h  �+ �  ). �   �/    �0 �   �0 �  &1    �2 �   �2 �  �3 �   w7 M   a8 N   �8 �  �8 1   �: !  ; �  (> �  @ 7   �B �   C �   �C b  �D ]  �G    MJ ;  ^J 6   �K �   �K �   �L X  �M 
   O e   P 1   tP _  �P N   R p  UR �  �U 
  �W y  �X d   $Z H   �Z �   �Z �  �[ �  y^ V  a`   �b F   �d ,  e {  Cf �  �g   mi �  oj   �l 1   o �  Bo 5   �q )  5r �   _t    �t �  u �  �w �  �z =  u| �  �} �  H   � �   *� #  !� �  E� �   ɋ )  x� �  �� V   `� �   �� �   �� $   7�    \� �  h� �   � :   �� W  �� ]  T� a  �� �  � 4  �� �   �   � �  ��    �� �   ��    �� �  �� )   �� )   ��    �    � (   �    ,�    F�    b� 
   x�    ��    ��    ��    ��    ˩    ܩ    �� 8   �    E�    I�    V�    ]�   d� s   z� (   �    � 4   /� '   d� ?   �� <   ̬ )   	� 
   3�    >�    M�    e�    {� 	   �� 
   ��    ��    ��    �� ;   ҭ 2   � *   A� -   l� 1   �� -   ̮ ;   ��    6� 4   M� S   ��     ֯ ]   �� Q   U�    �� k   �� W   '�    �    �� 6   ��    �    ��    �    *�    I�    b�    j�    �� (   ��    �� 	   ò 
   Ͳ    ز 1   ۲    
�    � 	   �    '� 	   A�    K�    g�    m� 	   t�    ~�    ��    ��    ��    ��    س    �� 
   �� 	   �    � %   .�    T� 
   `� )   k�    ��    ��    ��    Ŵ    �    �    � �   � �   �� �   u� �   �� q  � �   Z�    � "   � 
   (�    3�    :�    F�    ^� 9   q� z   �� V   &� �   }� *   � T   ,� C   �� �   Ž �   K� 4  � �   � n   �� '   
�    5�    J�    W� 	   f�    p�    �� 
   ��    ��    ��    ��    �� "   ��    �    � +   6�    b� !   }�    ��    ��    ��    ��    �� !   ��    � G  1�    y�    �    ��     ��    �� \   �� s   � B   �� 8   �� Q   	�    [�    c�    k� �   n�    ��    �� 6   
� �   A� "   ��    �� �   �� #   ��   �� i   �� �   0� �   �� #  �� @   �� 5  � �   S� �  ��   ��    ��    �� @   � J   E� e   �� b   �� v   Y� Y   �� �   *� %   �� 8   ��    � '    � Z   H� '   ��    ��    �� 
   �� #   �� �   �    �� 7   �� I   ��    D�    ]� [   u� {   �� �  M�     �    � �   4�    %�    .�    4� (   @� 	   i� -   s� �  ��    D�    a�    w�    �� �  �� )   %�    O�    ^� 7   j�    �� 	   �� >   �� !   ��    � 1   � �   G� %   ��     � i   8� !   ��    ��    ��    �� :   �� ~   4�    �� L   �� �   � d   �� k   N�    �� E  �� X   �    x� :   ��    �� �   �� �   ��    �� E   �� �   �� %  �� #   �� (   ��    � �  1� /   -� .   ]� +   �� -   �� *   �� �   � #   ��    �� 9   � O   U�    ��    �� %   �� >   �� �  4�   �� #  � R   0� S   ��    ��    �� �  ��   �� �   �� /   P� [  �� �  �  3   � +    �  ; �  ,    �    � ;   �    8 {  E    �	 �  �	   � 2  �
    � �   � I   � -   � ~    �  � 3  - �   a �  ? �    s  � ~    �   � �   � `   P �   � <   � .   �        '    8 )   Q %   { e   � B    	   J 0   T ~  � 9      > �   U  �   �  -   �!    �! $   �!    " /    "    P" 1   p" -   �" �   �" 1   �# �   �#    �$ �   �$ �   �% �   y& r  0' �   �( -   �) �   �) 	   w* �  �* �   3, "   �,   -    !/    ./    J/ *   d/ @   �/    �/    �/ ^   �/ �   F0 )  1    D2 
   P2 #   ^2 �   �2 �  V3 �   �4 �   �5    n6    �6    �6    �6    �6    �6    7 R   
7 /   ]7 ~  �7 k   :    x: �   �: �   .; [   �; �   < e   �< b   
= 	   p= |   z= V   �= �   N> c   ? g   l?    �?   �? {   �@ J   rA f   �A d   $B K   �B    �B ?   �B �   &C �   �C F   \D �   �D    �E ?  �E }   �F o   RG �   �G    jH i  {H   �J !   L $   %L    JL    RL =  ^L .   �M y   �M �   EN �   @O �   *P �   �P �   �Q �   �R   bS �   zT b  U {   ~V �  �V �   �X M  �Y 
   [ 
   [ 
   ([ �   6[ 
   �[ 
   �[ m   \ �   r\ $  ] 
   '^ �   5^ @   �^ �   �^ >   �_ �   �_ �   c` 
   �` �   �` 
   �a 
   �a 
   �a �  �a �   }d @  "e    cg    �g    �g �   �g ,   yh 8   �h G  �h �   'j ,   k    1k    Ak F   `k L   �k D   �k #   9l #   ]l ;  �l %   �m   �m   �o �   q m   �q �   6r w  �r �  st �  w �   z �   �z �   �{    <| �  U| $   ~ �  A~ 1  � t  O� �   Ă ~  �� �   �� �   � �   �� -   /� Q   ]�    �� ,   ȇ    ��    �    � 9  1�    k� v   |� U   �    I�    U�    ^� (   n� �   �� g   � P   �� ?  Ջ .   �    D�    P� (   V� ;   �    �� 	   ͍    ׍ 	   ��    � 	   �    �� 	   � :   � �   K�    �    ��    �    �    -� 
   <�    J� 
   Y�    g�    �� .   �� �   Ǐ $   [� G   ��    Ȑ �   H�   ?� �   S� �  7� �   ԕ �  �� +   l� �   �� N   f� j   �� e    � �   �� d  x� k   ݜ �   I�    ѝ �   � �   �� �   �    ��    ��    ʟ    �     � ,   �    ?� 3   G�    {�     �� "   �� .   Ƞ    ��    �    -�    J�    Q�    q�    ~�    ��    �� 4   �� ,   ܡ �   	� �   �� 6   U� *   �� �   �� s   J� �   �� 3   Q� 3   �� 7   �� E   � J   7� /   ��   �� W  �� �  �    �� X   �� �   � ?   �� L  �   ?� �   U� Q   �� %   N� �   t� �   V� �   @� 8   ܱ �   � -  ��    Գ =   �    -� G   I� �   �� �   :�    ȵ    е    ٵ 3   � �   � S   �� 8   � G   K� -   �� )   �� $   � d   � ,   u� ~   �� M   !� 0  o� }   �� r   � �   �� D   �� !   Լ     �� !   �     9� !   Z�     |� !   �� R   �� A   � >  T� I   �� 6   �� T   � 6   i�    �� �   �� �   3� �   �� �   �� 	   �� �   �� 6   ��    �� /  ��    � :   3� D   n� 0   �� [   �� h   @� S   ��    ��     � �   =� 3   &� ;   Z� r   �� #  	� ;   -� �   i� �   5� x   �� �   J� ;   �� Q   � �   q�    +� v   H�    ��   �� :   �� V   �    r� 4   �� I  �� =   � W   M� �   �� �   m� c   �� �   T� �   ��    ��    �� R   �� G   )�    q� 1   ��    ��    ��    �� 4   � �   6� p   ��    D� 2   a� 8   �� $   �� 9   �� �   ,� H   �� �   )� t   �� +   E� �   q�   �� 6   � o   G� !   �� o   �� W   I� '   �� r   �� �   <� 3   �� V   
�    d� 6   w� o   ��    � u   4� -   ��    �� .   �� �   $�   �� Q   ��    �    2� Q   O�    �� n   �� >   -� �   l� �   U�    �� k    �   l�    s� �   �� C   v� ;   �� r   ��    i�    ��    ��    ��    �� 0   �� c   �� M   P� &   ��    ��    �� &   �� >   � ;   O� 
   �� a   �� Z   ��    V� )   h� F   �� -   ��    � {   #�   �� k   ��    )�    9� �   F� �   � {   ��    4� �  J�   2� 8   N� �   �� 5   � /  R� 3   �� �   �� -   L     z  �  �  -   ) m   W    � s   E �   � )   B �   l U   � '   D �   l T   � 3   D _   x 9   � m    �   � �   "	 >   �	   /
 �  M �   
 @   �
 �  $ S      T p  e   � 7   �     ' �   H 0   � �  ' q   � `       | +   � �  �    C   H �  L e  � �  A  P   �! [   M" �   �" J   .# C   y# u   �# v   3$ (   �$ 2   �$ <   % N   C%    �% I   �% �   �% 6   �& 
   �& �   �& a  G' �   �(    q) !   �)    �) Y   �) }   * l   �* �   �* �   �+    p, -   �, O  �,    
. �   .   �. I  �1 X   H3 4   �3    �3    �3    �3 U   �3 D   ;4 �   �4 v  5 p   �6    7    7 %   *7 %   P7 }   v7    �7 K   8    S8 8   Y8 "   �8    �8    �8 _   �8 
   59 +   @9    l9 -   �9    �9 �   �9 "  d: �   �; �   .< {   �<   ==    B> "   R> �   u> �   p? �   o@    \A �   iA f   �A F   XB �   �B �   /C v   �C    ,D r   GD    �D    �D    �D    �D    E    0E    ME    dE    {E    �E 5   �E 5   �E 5    F %   6F 5   \F 0   �F 5   �F #   �F    G M   4G "   �G    �G W   �G >   
H �   LH C   �H (   I    ?I 0   ]I *   �I    �I V   �I :   *J v   eJ "  �J #   �K $   #L    HL    fL =   {L 	   �L    �L    �L �   �L    �M #   �M    �M 
   �M 	   �M L   N 1   UN @  �N    �O '   �O    P (   #P     LP #   mP !   �P #   �P '   �P 4   �P 9   4Q    nQ o   uQ 7   �Q    R    9R I   JR \   �R .   �R N    S %   oS �   �S x   ,T Z   �T     U '   U 	   0U    :U    NU &   iU �  �U �   XW #   �W    X /   X    CX '   SX    {X 
   �X s   �X N   Y   ZY    cZ ,   �Z �   �Z ,   a[ (   �[ +   �[ .   �[ 6   \ 8   I\    �\    �\ \   �\    
] 5   ,] ?   b] [  �] E   �^ \   D_ a   �_ #   ` �   '` '   �`    a V   (a    a /   �a $   �a X   �a A   Bb '   �b �   �b    �c    �c    �c    �c    �c    d '   d    Ed    \d d   td P  �d +   *f 1   Vf 
  �f �   �g    ch ,   �h    �h    �h    �h    �h    i    i    3i    Ei    Xi    gi 
   xi    �i    �i    �i    �i    �i +   �i '  �i i  $k �  �l �  un C   q   Ds �  dt    Av i  Pv    �w 0  �w   
y 6  z   J| �  Q} 8   + �   d S   (� %   |� Y   �� \   �� q   Y� �   ˁ �   M� �   � �   Ѓ   ��    � �   �� *  �� �   $� �   ��    ��   �� �  �� 4  ��   ��    ː �   ؐ �   x� F  �   V� w   ]� �   Ք +   �� /   ��    � r   �� 3   m�    �� �   �� U   \� �   �� (   ;� �   d� �   � `   �� y   �� R   u� |   Ț e   E� 8   �� �   � f   k� �   Ҝ �   |�    � /   � +   D� v   p� 7   �    � 
   +� M   6�    ��    ��    �� D   ğ F   	� [   P� %   ��    Ҡ 
   ߠ    � 	   � W   �� �   U� }   � B   k�    �� &   �� 5   ݢ    �    %� 
   <� 
   G�    R� 
   _�    j�    q�    ~�    ��    ��    ��    ��    ɣ    �    ��    
� 
   �    $�    1� Q   9� %   ��    �� �   Ǥ    s� x   �� �   	�    �    ��    	�    �    *�    6�    :� �   C� �   )� ^   ƨ    %� $   <� �   a�    �� �   � �   �� )   t�    �� �   ��   s� �   u� �   � �   � 5   �� �   ٯ 3   �� 4   ߰ W   � �   l�    $� �   C� �   � �   �� w   g�    ߴ    ��    � 
   #�    1�    F�    W�    i� �   �� �   C� �   �   �� %  �� v   ƹ W   =� ,  ��   ¼ M  Ҿ �    � [  �� �  A�    4� �   E� �  /� �   ��   �� �  �� �   ��   4�    9� N   N� �   �� �  S� c   O� 8  ��    ��    ��    � �   %� c   � �   h� D   �� |   C� a   �� x   "� 5   ��   �� J   �� (   ;� [   d�   �� !   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: vi
Language-Team: vi <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Thư viện Z là một thư viện phổ biến (và bất hợp pháp). Họ đã lấy bộ sưu tập Library Genesis và làm cho nó dễ dàng tìm kiếm. Ngoài ra, họ đã trở nên rất hiệu quả trong việc kêu gọi đóng góp sách mới, bằng cách khuyến khích người dùng đóng góp với nhiều đặc quyền khác nhau. Hiện tại họ không đóng góp những cuốn sách mới này trở lại Library Genesis. Và không giống như Library Genesis, họ không làm cho bộ sưu tập của mình dễ dàng sao chép, điều này ngăn cản việc bảo quản rộng rãi. Điều này quan trọng đối với mô hình kinh doanh của họ, vì họ tính phí để truy cập bộ sưu tập của mình với số lượng lớn (hơn 10 cuốn sách mỗi ngày). Chúng tôi không đưa ra phán xét đạo đức về việc thu phí truy cập số lượng lớn vào một bộ sưu tập sách bất hợp pháp. Không còn nghi ngờ gì nữa, Thư viện Z đã thành công trong việc mở rộng quyền truy cập vào tri thức và tìm nguồn sách nhiều hơn. Chúng tôi chỉ đơn giản ở đây để làm phần việc của mình: đảm bảo việc bảo tồn lâu dài bộ sưu tập cá nhân này. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>) Trong lần phát hành ban đầu của Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>), chúng tôi đã tạo một bản sao của Thư viện Z, một bộ sưu tập sách bất hợp pháp lớn. Như một lời nhắc nhở, đây là những gì chúng tôi đã viết trong bài đăng blog ban đầu đó: Bộ sưu tập đó có từ giữa năm 2021. Trong thời gian đó, Thư viện Z đã phát triển với tốc độ đáng kinh ngạc: họ đã thêm khoảng 3,8 triệu cuốn sách mới. Chắc chắn có một số bản sao, nhưng phần lớn dường như là sách mới hợp pháp hoặc các bản quét chất lượng cao hơn của các cuốn sách đã được gửi trước đó. Điều này phần lớn là do số lượng người điều hành tình nguyện tại Thư viện Z tăng lên và hệ thống tải lên số lượng lớn của họ với tính năng loại bỏ trùng lặp. Chúng tôi muốn chúc mừng họ về những thành tựu này. Chúng tôi vui mừng thông báo rằng chúng tôi đã có được tất cả các cuốn sách được thêm vào Thư viện Z giữa bản sao cuối cùng của chúng tôi và tháng 8 năm 2022. Chúng tôi cũng đã quay lại và thu thập một số cuốn sách mà chúng tôi đã bỏ lỡ lần đầu tiên. Tất cả, bộ sưu tập mới này khoảng 24TB, lớn hơn nhiều so với lần trước (7TB). Bản sao của chúng tôi hiện tổng cộng là 31TB. Một lần nữa, chúng tôi đã loại bỏ trùng lặp với Library Genesis, vì đã có các torrent có sẵn cho bộ sưu tập đó. Vui lòng truy cập vào Pirate Library Mirror để xem bộ sưu tập mới (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>). Có thêm thông tin ở đó về cách các tệp được cấu trúc và những gì đã thay đổi kể từ lần trước. Chúng tôi sẽ không liên kết đến nó từ đây, vì đây chỉ là một trang blog không lưu trữ bất kỳ tài liệu bất hợp pháp nào. Tất nhiên, việc seeding cũng là một cách tuyệt vời để giúp đỡ chúng tôi. Cảm ơn tất cả mọi người đã seeding bộ torrent trước của chúng tôi. Chúng tôi rất biết ơn phản hồi tích cực và vui mừng vì có rất nhiều người quan tâm đến việc bảo tồn tri thức và văn hóa theo cách đặc biệt này. 3x sách mới được thêm vào Bản sao Thư viện Cướp biển (+24TB, 3,8 triệu cuốn sách) Đọc các bài viết đồng hành của TorrentFreak: <a %(torrentfreak)s>thứ nhất</a>, <a %(torrentfreak_2)s>thứ hai</a> - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) các bài viết đồng hành của TorrentFreak: <a %(torrentfreak)s>thứ nhất</a>, <a %(torrentfreak_2)s>thứ hai</a> Không lâu trước đây, các “thư viện bóng tối” đang dần biến mất. Sci-Hub, kho lưu trữ bất hợp pháp khổng lồ của các bài báo học thuật, đã ngừng tiếp nhận các tác phẩm mới do các vụ kiện tụng. “Thư viện Z”, thư viện sách bất hợp pháp lớn nhất, đã chứng kiến những người sáng lập bị cáo buộc bị bắt vì tội vi phạm bản quyền. Họ đã thoát khỏi sự bắt giữ một cách đáng kinh ngạc, nhưng thư viện của họ không kém phần bị đe dọa. Một số quốc gia đã thực hiện một phiên bản của điều này. TorrentFreak <a %(torrentfreak)s>báo cáo</a> rằng Trung Quốc và Nhật Bản đã giới thiệu các ngoại lệ AI vào luật bản quyền của họ. Chúng tôi không rõ điều này tương tác như thế nào với các hiệp ước quốc tế, nhưng chắc chắn nó mang lại sự bảo vệ cho các công ty trong nước của họ, điều này giải thích những gì chúng tôi đã thấy. Đối với Kho Lưu Trữ của Anna — chúng tôi sẽ tiếp tục công việc ngầm của mình dựa trên niềm tin đạo đức. Tuy nhiên, mong muốn lớn nhất của chúng tôi là bước ra ánh sáng, và khuếch đại tác động của chúng tôi một cách hợp pháp. Xin hãy cải cách bản quyền. Khi Thư viện Z đối mặt với việc đóng cửa, tôi đã sao lưu toàn bộ thư viện của nó và đang tìm kiếm một nền tảng để lưu trữ. Đó là động lực của tôi khi bắt đầu Kho Lưu Trữ của Anna: tiếp tục sứ mệnh đằng sau những sáng kiến trước đó. Chúng tôi đã phát triển thành thư viện bóng tối lớn nhất thế giới, lưu trữ hơn 140 triệu văn bản có bản quyền trên nhiều định dạng — sách, bài báo học thuật, tạp chí, báo chí và hơn thế nữa. Đội ngũ của tôi và tôi là những người theo chủ nghĩa lý tưởng. Chúng tôi tin rằng việc bảo tồn và lưu trữ những tệp này là đúng đắn về mặt đạo đức. Các thư viện trên khắp thế giới đang chứng kiến sự cắt giảm tài trợ, và chúng tôi cũng không thể tin tưởng vào di sản của nhân loại cho các tập đoàn. Rồi AI xuất hiện. Hầu hết các công ty lớn xây dựng LLM đều liên hệ với chúng tôi để đào tạo trên dữ liệu của chúng tôi. Hầu hết (nhưng không phải tất cả!) các công ty có trụ sở tại Mỹ đã xem xét lại khi họ nhận ra tính bất hợp pháp của công việc của chúng tôi. Ngược lại, các công ty Trung Quốc đã nhiệt tình đón nhận bộ sưu tập của chúng tôi, dường như không bận tâm đến tính hợp pháp của nó. Điều này đáng chú ý vì vai trò của Trung Quốc là một bên ký kết hầu hết các hiệp ước bản quyền quốc tế lớn. Chúng tôi đã cung cấp quyền truy cập tốc độ cao cho khoảng 30 công ty. Hầu hết trong số họ là các công ty LLM, và một số là các nhà môi giới dữ liệu, những người sẽ bán lại bộ sưu tập của chúng tôi. Hầu hết là người Trung Quốc, mặc dù chúng tôi cũng đã làm việc với các công ty từ Mỹ, Châu Âu, Nga, Hàn Quốc và Nhật Bản. DeepSeek <a %(arxiv)s>thừa nhận</a> rằng một phiên bản trước đó đã được đào tạo trên một phần bộ sưu tập của chúng tôi, mặc dù họ rất kín tiếng về mô hình mới nhất của họ (có lẽ cũng được đào tạo trên dữ liệu của chúng tôi). Nếu phương Tây muốn dẫn đầu trong cuộc đua LLM, và cuối cùng là AGI, họ cần xem xét lại lập trường của mình về bản quyền, và sớm thôi. Cho dù bạn đồng ý hay không với chúng tôi về trường hợp đạo đức của chúng tôi, điều này hiện đang trở thành một vấn đề kinh tế, và thậm chí là an ninh quốc gia. Tất cả các khối quyền lực đang xây dựng các siêu nhà khoa học nhân tạo, siêu hacker và siêu quân đội. Tự do thông tin đang trở thành vấn đề sống còn cho các quốc gia này — thậm chí là vấn đề an ninh quốc gia. Đội ngũ của chúng tôi đến từ khắp nơi trên thế giới, và chúng tôi không có sự liên kết cụ thể nào. Nhưng chúng tôi khuyến khích các quốc gia có luật bản quyền mạnh mẽ sử dụng mối đe dọa hiện hữu này để cải cách chúng. Vậy phải làm gì? Khuyến nghị đầu tiên của chúng tôi rất đơn giản: rút ngắn thời hạn bản quyền. Ở Mỹ, bản quyền được cấp trong 70 năm sau khi tác giả qua đời. Điều này thật vô lý. Chúng ta có thể đưa điều này phù hợp với bằng sáng chế, được cấp trong 20 năm sau khi nộp đơn. Điều này nên đủ thời gian cho các tác giả của sách, bài báo, âm nhạc, nghệ thuật và các tác phẩm sáng tạo khác, để được bồi thường đầy đủ cho những nỗ lực của họ (bao gồm cả các dự án dài hạn như chuyển thể phim). Sau đó, ít nhất, các nhà hoạch định chính sách nên bao gồm các ngoại lệ cho việc bảo tồn và phổ biến văn bản hàng loạt. Nếu mất doanh thu từ khách hàng cá nhân là mối lo ngại chính, việc phân phối ở cấp độ cá nhân có thể vẫn bị cấm. Đổi lại, những người có khả năng quản lý các kho lưu trữ lớn — các công ty đào tạo LLM, cùng với các thư viện và các kho lưu trữ khác — sẽ được bảo vệ bởi những ngoại lệ này. Cải cách bản quyền là cần thiết cho an ninh quốc gia Tóm tắt: Các LLM của Trung Quốc (bao gồm DeepSeek) được đào tạo trên kho lưu trữ sách và tài liệu bất hợp pháp của tôi — lớn nhất thế giới. Phương Tây cần cải tổ luật bản quyền như một vấn đề an ninh quốc gia. Vui lòng xem <a %(all_isbns)s>bài viết blog gốc</a> để biết thêm thông tin. Chúng tôi đã đưa ra một thử thách để cải thiện điều này. Chúng tôi sẽ trao giải nhất trị giá 6.000 đô la, giải nhì 3.000 đô la và giải ba 1.000 đô la. Do phản hồi áp đảo và các bài dự thi đáng kinh ngạc, chúng tôi đã quyết định tăng nhẹ quỹ giải thưởng và trao giải ba cho bốn người, mỗi người 500 đô la. Dưới đây là danh sách người chiến thắng, nhưng hãy chắc chắn xem tất cả các bài dự thi <a %(annas_archive)s>tại đây</a>, hoặc tải xuống <a %(a_2025_01_isbn_visualization_files)s>torrent kết hợp</a> của chúng tôi. Giải nhất 6.000 đô la: phiresky Bài <a %(phiresky_github)s>dự thi</a> này (<a %(annas_archive_note_2951)s>bình luận trên Gitlab</a>) đơn giản là mọi thứ chúng tôi mong muốn, và hơn thế nữa! Chúng tôi đặc biệt thích các tùy chọn hình ảnh hóa cực kỳ linh hoạt (thậm chí hỗ trợ các shader tùy chỉnh), nhưng với danh sách cài đặt sẵn toàn diện. Chúng tôi cũng thích cách mọi thứ hoạt động nhanh và mượt mà, cách triển khai đơn giản (thậm chí không có backend), bản đồ nhỏ thông minh và giải thích chi tiết trong <a %(phiresky_github)s>bài viết blog</a> của họ. Công việc tuyệt vời và xứng đáng giành chiến thắng! - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Trái tim chúng tôi tràn đầy lòng biết ơn. Ý tưởng đáng chú ý Những tòa nhà chọc trời cho sự hiếm có Nhiều thanh trượt để so sánh Datasets, như thể bạn là một DJ. Thanh tỷ lệ với số lượng sách. Nhãn đẹp. Bảng màu mặc định mát mẻ và bản đồ nhiệt. Chế độ xem bản đồ độc đáo và bộ lọc Chú thích, và cũng có thống kê trực tiếp Thống kê trực tiếp Một số ý tưởng và triển khai khác mà chúng tôi đặc biệt thích: Chúng tôi có thể tiếp tục một lúc nữa, nhưng hãy dừng lại ở đây. Hãy chắc chắn xem tất cả các bài nộp <a %(annas_archive)s>tại đây</a>, hoặc tải xuống <a %(a_2025_01_isbn_visualization_files)s>torrent kết hợp</a> của chúng tôi. Rất nhiều bài nộp, và mỗi bài mang đến một góc nhìn độc đáo, dù là trong giao diện người dùng hay triển khai. Chúng tôi sẽ ít nhất tích hợp bài nộp đạt giải nhất vào trang web chính của chúng tôi, và có thể một số bài khác. Chúng tôi cũng đã bắt đầu suy nghĩ về cách tổ chức quá trình xác định, xác nhận, và sau đó lưu trữ những cuốn sách hiếm nhất. Sẽ có thêm thông tin về vấn đề này. Cảm ơn tất cả mọi người đã tham gia. Thật tuyệt vời khi có rất nhiều người quan tâm. Dễ dàng chuyển đổi các Datasets để so sánh nhanh chóng. Tất cả ISBN SSNO của CADAL Rò rỉ dữ liệu CERLALC SSID của DuXiu Chỉ mục eBook của EBSCOhost Google Books Goodreads Internet Archive ISBNdb Sổ Đăng ký Nhà xuất bản Toàn cầu ISBN Libby Các tệp trong Lưu trữ của Anna Nexus/STC OCLC/Worldcat OpenLibrary Thư viện Quốc gia Nga Thư viện Hoàng gia Trantor Giải nhì 3.000 đô la: hypha “Mặc dù các hình vuông và hình chữ nhật hoàn hảo có tính thẩm mỹ toán học, nhưng chúng không cung cấp tính cục bộ vượt trội trong bối cảnh bản đồ. Tôi tin rằng sự bất đối xứng vốn có trong các đường cong Hilbert hoặc Morton cổ điển này không phải là một khuyết điểm mà là một đặc điểm. Giống như đường viền hình chiếc ủng nổi tiếng của Ý làm cho nó dễ nhận biết ngay lập tức trên bản đồ, những "đặc điểm" độc đáo của các đường cong này có thể đóng vai trò là điểm mốc nhận thức. Sự khác biệt này có thể nâng cao trí nhớ không gian và giúp người dùng định hướng, có thể giúp dễ dàng xác định các khu vực cụ thể hoặc nhận thấy các mẫu.” Một <a %(annas_archive_note_2913)s>bài dự thi</a> đáng kinh ngạc khác. Không linh hoạt như giải nhất, nhưng chúng tôi thực sự thích hình ảnh hóa ở cấp độ vĩ mô của nó hơn giải nhất (đường cong lấp đầy không gian, biên giới, nhãn, làm nổi bật, di chuyển và phóng to). Một <a %(annas_archive_note_2971)s>bình luận</a> của Joe Davis đã gây ấn tượng với chúng tôi: Và vẫn còn rất nhiều tùy chọn để hình ảnh hóa và kết xuất, cũng như giao diện người dùng cực kỳ mượt mà và trực quan. Một vị trí thứ hai vững chắc! - Anna và đội ngũ (<a %(reddit)s>Reddit</a>) Vài tháng trước, chúng tôi đã công bố một <a %(all_isbns)s>giải thưởng 10.000 đô la</a> để tạo ra hình ảnh trực quan tốt nhất có thể về dữ liệu của chúng tôi hiển thị không gian ISBN. Chúng tôi nhấn mạnh việc hiển thị những tệp nào chúng tôi đã/chưa lưu trữ, và sau đó là một tập dữ liệu mô tả có bao nhiêu thư viện nắm giữ ISBN (một thước đo độ hiếm). Chúng tôi đã bị choáng ngợp bởi phản hồi. Có rất nhiều sự sáng tạo. Một lời cảm ơn lớn đến tất cả những ai đã tham gia: năng lượng và sự nhiệt tình của bạn thật lây lan! Cuối cùng, chúng tôi muốn trả lời các câu hỏi sau: <strong>những cuốn sách nào tồn tại trên thế giới, chúng tôi đã lưu trữ được bao nhiêu, và chúng tôi nên tập trung vào những cuốn sách nào tiếp theo?</strong> Thật tuyệt vời khi thấy nhiều người quan tâm đến những câu hỏi này. Chúng tôi đã bắt đầu với một hình ảnh trực quan cơ bản. Trong chưa đầy 300kb, bức tranh này tóm tắt một cách ngắn gọn danh sách sách mở lớn nhất từng được tập hợp trong lịch sử nhân loại: Giải ba 500 đô la #1: maxlion Trong <a %(annas_archive_note_2940)s>bài dự thi</a> này, chúng tôi thực sự thích các loại chế độ xem khác nhau, đặc biệt là chế độ xem so sánh và nhà xuất bản. Giải ba 500 đô la #2: abetusk Mặc dù giao diện người dùng không phải là hoàn thiện nhất, nhưng <a %(annas_archive_note_2917)s>bài dự thi</a> này đáp ứng nhiều tiêu chí. Chúng tôi đặc biệt thích tính năng so sánh của nó. Giải ba 500 đô la #3: conundrumer0 Giống như giải nhất, <a %(annas_archive_note_2975)s>bài dự thi</a> này đã gây ấn tượng với chúng tôi bởi tính linh hoạt của nó. Cuối cùng, đây là điều tạo nên một công cụ hình ảnh hóa tuyệt vời: tính linh hoạt tối đa cho người dùng chuyên nghiệp, trong khi vẫn giữ mọi thứ đơn giản cho người dùng trung bình. Giải ba 500 đô la #4: charelf Bài <a %(annas_archive_note_2947)s>dự thi</a> cuối cùng nhận được giải thưởng khá cơ bản, nhưng có một số tính năng độc đáo mà chúng tôi thực sự thích. Chúng tôi thích cách họ hiển thị có bao nhiêu datasets bao phủ một ISBN cụ thể như một thước đo độ phổ biến/độ tin cậy. Chúng tôi cũng thực sự thích sự đơn giản nhưng hiệu quả của việc sử dụng thanh trượt độ mờ để so sánh. Những người chiến thắng giải thưởng trực quan hóa ISBN trị giá 10.000 đô la Tóm tắt: Chúng tôi đã nhận được một số bài dự thi đáng kinh ngạc cho giải thưởng trực quan hóa ISBN trị giá 10.000 đô la. Bối cảnh Làm thế nào để Lưu trữ của Anna đạt được sứ mệnh sao lưu tất cả kiến thức của nhân loại, mà không biết những cuốn sách nào vẫn còn ngoài kia? Chúng tôi cần một danh sách CẦN LÀM. Một cách để lập bản đồ này là thông qua số ISBN, mà từ những năm 1970 đã được gán cho mỗi cuốn sách xuất bản (ở hầu hết các quốc gia). Không có cơ quan trung ương nào biết tất cả các gán ISBN. Thay vào đó, đó là một hệ thống phân tán, nơi các quốc gia nhận được dải số, sau đó gán các dải nhỏ hơn cho các nhà xuất bản lớn, những người có thể tiếp tục chia nhỏ dải cho các nhà xuất bản nhỏ hơn. Cuối cùng, các số cá nhân được gán cho các cuốn sách. Chúng tôi đã bắt đầu lập bản đồ ISBN <a %(blog)s>hai năm trước</a> với việc thu thập dữ liệu từ ISBNdb. Kể từ đó, chúng tôi đã thu thập dữ liệu từ nhiều nguồn metadata khác, như <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, và nhiều hơn nữa. Danh sách đầy đủ có thể được tìm thấy trên các trang “Datasets” và “Torrents” trên Lưu trữ của Anna. Hiện tại, chúng tôi có bộ sưu tập metadata sách mở hoàn toàn, dễ dàng tải xuống lớn nhất thế giới (và do đó là ISBNs). Chúng tôi đã <a %(blog)s>viết rất nhiều</a> về lý do tại sao chúng tôi quan tâm đến việc bảo tồn, và tại sao chúng tôi hiện đang trong một cửa sổ quan trọng. Chúng tôi phải xác định các cuốn sách hiếm, ít được chú ý và có nguy cơ đặc biệt và bảo tồn chúng. Có metadata tốt về tất cả các cuốn sách trên thế giới giúp ích cho điều đó. Giải thưởng $10,000 Sự cân nhắc mạnh mẽ sẽ được dành cho tính khả dụng và vẻ đẹp của nó. Hiển thị metadata thực tế cho từng ISBN khi phóng to, chẳng hạn như tiêu đề và tác giả. Đường cong lấp đầy không gian tốt hơn. Ví dụ: một đường zig-zag, đi từ 0 đến 4 trên hàng đầu tiên và sau đó quay lại (ngược lại) từ 5 đến 9 trên hàng thứ hai — áp dụng đệ quy. Các bảng màu khác nhau hoặc có thể tùy chỉnh. Các chế độ xem đặc biệt để so sánh datasets. Các cách để gỡ lỗi các vấn đề, chẳng hạn như các metadata khác không đồng ý tốt (ví dụ: tiêu đề khác biệt lớn). Chú thích hình ảnh với nhận xét về ISBN hoặc phạm vi. Bất kỳ phương pháp nào để xác định sách hiếm hoặc có nguy cơ. Bất kỳ ý tưởng sáng tạo nào bạn có thể nghĩ ra! Mã Mã để tạo ra những hình ảnh này, cũng như các ví dụ khác, có thể được tìm thấy trong <a %(annas_archive)s>thư mục này</a>. Chúng tôi đã nghĩ ra một định dạng dữ liệu gọn nhẹ, với tất cả thông tin ISBN cần thiết khoảng 75MB (đã nén). Mô tả định dạng dữ liệu và mã để tạo ra nó có thể được tìm thấy <a %(annas_archive_l1244_1319)s>tại đây</a>. Để nhận phần thưởng, bạn không bắt buộc phải sử dụng cái này, nhưng có lẽ đây là định dạng tiện lợi nhất để bắt đầu. Bạn có thể chuyển đổi metadata của chúng tôi theo bất kỳ cách nào bạn muốn (mặc dù tất cả mã của bạn phải là mã nguồn mở). Chúng tôi rất mong chờ những gì bạn sẽ tạo ra. Chúc may mắn! Fork repo này, và chỉnh sửa HTML của bài viết blog này (không cho phép các backend khác ngoài backend Flask của chúng tôi). Làm cho hình ảnh trên có thể phóng to mượt mà, để bạn có thể phóng to đến từng ISBN. Nhấp vào ISBNs nên dẫn bạn đến trang metadata hoặc tìm kiếm trên Lưu trữ của Anna. Bạn vẫn phải có thể chuyển đổi giữa tất cả các datasets khác nhau. Dải số quốc gia và dải số nhà xuất bản nên được làm nổi bật khi di chuột. Bạn có thể sử dụng ví dụ như <a %(github_xlcnd_isbnlib)s>data4info.py trong isbnlib</a> cho thông tin quốc gia, và thu thập dữ liệu “isbngrp” của chúng tôi cho các nhà xuất bản (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Nó phải hoạt động tốt trên cả máy tính để bàn và di động. Có rất nhiều điều để khám phá ở đây, vì vậy chúng tôi đang công bố một giải thưởng cho việc cải thiện hình ảnh hóa ở trên. Không giống như hầu hết các giải thưởng của chúng tôi, giải thưởng này có thời hạn. Bạn phải <a %(annas_archive)s>nộp</a> mã nguồn mở của mình trước ngày 2025-01-31 (23:59 UTC). Bài nộp tốt nhất sẽ nhận được $6,000, vị trí thứ hai là $3,000, và vị trí thứ ba là $1,000. Tất cả các giải thưởng sẽ được trao bằng Monero (XMR). Dưới đây là các tiêu chí tối thiểu. Nếu không có bài nộp nào đáp ứng các tiêu chí, chúng tôi có thể vẫn trao một số giải thưởng, nhưng điều đó sẽ do chúng tôi quyết định. Để có điểm thưởng (đây chỉ là ý tưởng — hãy để sự sáng tạo của bạn bay xa): Bạn CÓ THỂ hoàn toàn đi chệch khỏi các tiêu chí tối thiểu và thực hiện một hình ảnh hóa hoàn toàn khác. Nếu nó thực sự ngoạn mục, thì điều đó đủ điều kiện cho phần thưởng, nhưng theo quyết định của chúng tôi. Gửi bài bằng cách đăng bình luận vào <a %(annas_archive)s>vấn đề này</a> với liên kết đến kho lưu trữ đã fork của bạn, yêu cầu hợp nhất hoặc diff. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Bức tranh này có kích thước 1000×800 pixel. Mỗi pixel đại diện cho 2.500 ISBN. Nếu chúng tôi có một tệp cho một ISBN, chúng tôi làm cho pixel đó xanh hơn. Nếu chúng tôi biết một ISBN đã được phát hành, nhưng chúng tôi không có tệp tương ứng, chúng tôi làm cho nó đỏ hơn. Trong chưa đầy 300kb, bức tranh này tóm tắt đại diện cho “danh sách sách” mở hoàn toàn lớn nhất từng được tập hợp trong lịch sử nhân loại (vài trăm GB nén đầy đủ). Nó cũng cho thấy: còn rất nhiều việc phải làm trong việc sao lưu sách (chúng tôi chỉ có 16%). Hình dung Tất cả ISBN — Phần thưởng $10,000 trước ngày 31-01-2025 Bức tranh này đại diện cho “danh sách sách” mở hoàn toàn lớn nhất từng được tập hợp trong lịch sử nhân loại. Hình dung Ngoài hình ảnh tổng quan, chúng tôi cũng có thể xem xét các datasets riêng lẻ mà chúng tôi đã thu thập. Sử dụng menu thả xuống và các nút để chuyển đổi giữa chúng. Có rất nhiều mẫu thú vị để thấy trong những bức tranh này. Tại sao có sự đều đặn của các dòng và khối, dường như xảy ra ở các quy mô khác nhau? Những khu vực trống là gì? Tại sao một số datasets lại tập trung như vậy? Chúng tôi sẽ để những câu hỏi này như một bài tập cho người đọc. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Kết luận Với tiêu chuẩn này, chúng ta có thể phát hành dữ liệu một cách gia tăng hơn, và dễ dàng thêm các nguồn dữ liệu mới. Chúng tôi đã có một vài bản phát hành thú vị trong kế hoạch! Chúng tôi cũng hy vọng rằng các thư viện bóng tối khác có thể dễ dàng sao chép các bộ sưu tập của chúng tôi. Sau cùng, mục tiêu của chúng tôi là bảo tồn kiến thức và văn hóa của con người mãi mãi, vì vậy càng nhiều sự dư thừa càng tốt. Ví dụ Hãy xem xét bản phát hành Thư viện Z gần đây của chúng tôi như một ví dụ. Nó bao gồm hai bộ sưu tập: “<span style="background: #fffaa3">zlib3_records</span>” và “<span style="background: #ffd6fe">zlib3_files</span>”. Điều này cho phép chúng tôi tách riêng và phát hành các bản ghi metadata từ các tệp sách thực tế. Do đó, chúng tôi đã phát hành hai torrent với các tệp metadata: Chúng tôi cũng đã phát hành một loạt torrent với các thư mục dữ liệu nhị phân, nhưng chỉ cho bộ sưu tập “<span style="background: #ffd6fe">zlib3_files</span>”, tổng cộng 62: Bằng cách chạy <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> chúng ta có thể thấy những gì bên trong: Trong trường hợp này, đó là metadata của một cuốn sách như được báo cáo bởi Thư viện Z. Ở cấp cao nhất, chúng ta chỉ có “aacid” và “metadata”, nhưng không có “data_folder”, vì không có dữ liệu nhị phân tương ứng. AACID chứa “22430000” làm ID chính, mà chúng ta có thể thấy được lấy từ “zlibrary_id”. Chúng ta có thể mong đợi các AAC khác trong bộ sưu tập này có cấu trúc tương tự. Bây giờ hãy chạy <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Đây là metadata AAC nhỏ hơn nhiều, mặc dù phần lớn của AAC này nằm ở nơi khác trong một tệp nhị phân! Sau cùng, lần này chúng ta có “data_folder”, vì vậy chúng ta có thể mong đợi dữ liệu nhị phân tương ứng được đặt tại <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” chứa “zlibrary_id”, vì vậy chúng ta có thể dễ dàng liên kết nó với AAC tương ứng trong bộ sưu tập “zlib_records”. Chúng ta có thể đã liên kết theo nhiều cách khác nhau, ví dụ thông qua AACID — tiêu chuẩn không quy định điều đó. Lưu ý rằng cũng không cần thiết trường “metadata” phải là JSON. Nó có thể là một chuỗi chứa XML hoặc bất kỳ định dạng dữ liệu nào khác. Bạn thậm chí có thể lưu trữ thông tin metadata trong blob nhị phân liên kết, ví dụ nếu đó là rất nhiều dữ liệu. Các tệp và metadata không đồng nhất, ở định dạng gần nhất có thể với bản gốc. Dữ liệu nhị phân có thể được phục vụ trực tiếp bởi các máy chủ web như Nginx. Các định danh không đồng nhất trong các thư viện nguồn, hoặc thậm chí không có định danh. Phát hành riêng biệt metadata so với dữ liệu tệp, hoặc phát hành chỉ metadata (ví dụ: phát hành ISBNdb của chúng tôi). Phân phối qua torrent, mặc dù có khả năng sử dụng các phương pháp phân phối khác (ví dụ: IPFS). Các bản ghi không thể thay đổi, vì chúng tôi nên giả định rằng các torrent của chúng tôi sẽ tồn tại mãi mãi. Phát hành gia tăng / phát hành có thể bổ sung. Có thể đọc và ghi bằng máy, một cách thuận tiện và nhanh chóng, đặc biệt là cho ngăn xếp của chúng tôi (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Dễ dàng kiểm tra bằng mắt thường, mặc dù điều này là thứ yếu so với khả năng đọc của máy. Dễ dàng gieo mầm bộ sưu tập của chúng tôi với một seedbox thuê tiêu chuẩn. Mục tiêu thiết kế Chúng tôi không quan tâm đến việc các tệp dễ dàng điều hướng thủ công trên đĩa, hoặc có thể tìm kiếm mà không cần xử lý trước. Chúng tôi không quan tâm đến việc tương thích trực tiếp với phần mềm thư viện hiện có. Mặc dù bất kỳ ai cũng có thể dễ dàng gieo mầm bộ sưu tập của chúng tôi bằng cách sử dụng torrent, chúng tôi không mong đợi các tệp có thể sử dụng được mà không có kiến thức kỹ thuật đáng kể và cam kết. Trường hợp sử dụng chính của chúng tôi là phân phối các tệp và metadata liên quan từ các bộ sưu tập hiện có khác nhau. Những cân nhắc quan trọng nhất của chúng tôi là: Một số mục tiêu không phải là: Vì Lưu Trữ Anna là mã nguồn mở, chúng tôi muốn sử dụng định dạng của mình trực tiếp. Khi chúng tôi làm mới chỉ mục tìm kiếm của mình, chúng tôi chỉ truy cập các đường dẫn có sẵn công khai, để bất kỳ ai sao chép thư viện của chúng tôi có thể bắt đầu nhanh chóng. <strong>AAC.</strong> AAC (Anna’s Archive Container) là một mục duy nhất bao gồm <strong>metadata</strong>, và tùy chọn <strong>dữ liệu nhị phân</strong>, cả hai đều không thể thay đổi. Nó có một định danh duy nhất toàn cầu, được gọi là <strong>AACID</strong>. <strong>AACID.</strong> Định dạng của AACID là: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. Ví dụ, một AACID thực tế mà chúng tôi đã phát hành là <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Phạm vi AACID.</strong> Vì AACID chứa các dấu thời gian tăng đơn điệu, chúng tôi có thể sử dụng điều đó để biểu thị phạm vi trong một bộ sưu tập cụ thể. Chúng tôi sử dụng định dạng này: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>, trong đó các dấu thời gian là bao gồm. Điều này nhất quán với ký hiệu ISO 8601. Các phạm vi là liên tục và có thể chồng chéo, nhưng trong trường hợp chồng chéo phải chứa các bản ghi giống hệt như bản đã phát hành trước đó trong bộ sưu tập đó (vì AAC là không thể thay đổi). Không được phép thiếu bản ghi. <code>{collection}</code>: tên bộ sưu tập, có thể chứa các chữ cái ASCII, số và dấu gạch dưới (nhưng không có dấu gạch dưới kép). <code>{collection-specific ID}</code>: một định danh cụ thể của bộ sưu tập, nếu có, ví dụ: ID của Thư viện Z. Có thể bỏ qua hoặc cắt ngắn. Phải bỏ qua hoặc cắt ngắn nếu AACID vượt quá 150 ký tự. <code>{ISO 8601 timestamp}</code>: một phiên bản ngắn của ISO 8601, luôn ở UTC, ví dụ: <code>20220723T194746Z</code>. Số này phải tăng đơn điệu cho mỗi lần phát hành, mặc dù ý nghĩa chính xác của nó có thể khác nhau tùy theo bộ sưu tập. Chúng tôi đề xuất sử dụng thời gian thu thập dữ liệu hoặc tạo ID. <code>{shortuuid}</code>: một UUID nhưng được nén thành ASCII, ví dụ: sử dụng base57. Hiện tại chúng tôi sử dụng thư viện Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Thư mục dữ liệu nhị phân.</strong> Một thư mục chứa dữ liệu nhị phân của một phạm vi AAC, cho một bộ sưu tập cụ thể. Chúng có các thuộc tính sau: Thư mục phải chứa các tệp dữ liệu cho tất cả các AAC trong phạm vi được chỉ định. Mỗi tệp dữ liệu phải có AACID của nó làm tên tệp (không có phần mở rộng). Tên thư mục phải là một phạm vi AACID, được tiền tố bằng <code style="color: green">annas_archive_data__</code>, và không có hậu tố. Ví dụ, một trong những phát hành thực tế của chúng tôi có một thư mục được gọi là<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Nên tạo các thư mục này có kích thước dễ quản lý, ví dụ không lớn hơn 100GB-1TB mỗi thư mục, mặc dù khuyến nghị này có thể thay đổi theo thời gian. <strong>Bộ sưu tập.</strong> Mỗi AAC thuộc về một bộ sưu tập, theo định nghĩa là một danh sách các AAC có tính nhất quán về ngữ nghĩa. Điều đó có nghĩa là nếu bạn thực hiện một thay đổi đáng kể đối với định dạng của metadata, thì bạn phải tạo một bộ sưu tập mới. Tiêu chuẩn <strong>Tệp metadata.</strong> Một tệp metadata chứa metadata của một phạm vi AAC, cho một bộ sưu tập cụ thể. Chúng có các thuộc tính sau: <code>data_folder</code> là tùy chọn, và là tên của thư mục dữ liệu nhị phân chứa dữ liệu nhị phân tương ứng. Tên tệp của dữ liệu nhị phân tương ứng trong thư mục đó là AACID của bản ghi. Mỗi đối tượng JSON phải chứa các trường sau ở cấp cao nhất: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (tùy chọn). Không được phép có các trường khác. Tên tệp phải là một phạm vi AACID, được tiền tố bằng <code style="color: red">annas_archive_meta__</code> và theo sau là <code>.jsonl.zstd</code>. Ví dụ, một trong những phát hành của chúng tôi được gọi là<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Như được chỉ định bởi phần mở rộng tệp, loại tệp là <a %(jsonlines)s>JSON Lines</a> được nén bằng <a %(zstd)s>Zstandard</a>. <code>metadata</code> là metadata tùy ý, theo ngữ nghĩa của bộ sưu tập. Nó phải nhất quán về ngữ nghĩa trong bộ sưu tập. Tiền tố <code style="color: red">annas_archive_meta__</code> có thể được điều chỉnh theo tên của tổ chức của bạn, ví dụ: <code style="color: red">my_institute_meta__</code>. <strong>Bộ sưu tập “bản ghi” và “tệp”.</strong> Theo thông lệ, thường tiện lợi khi phát hành “bản ghi” và “tệp” dưới dạng các bộ sưu tập khác nhau, để chúng có thể được phát hành theo các lịch trình khác nhau, ví dụ như dựa trên tốc độ thu thập dữ liệu. Một “bản ghi” là một bộ sưu tập chỉ chứa metadata, bao gồm thông tin như tiêu đề sách, tác giả, ISBN, v.v., trong khi “tệp” là các bộ sưu tập chứa các tệp thực tế (pdf, epub). Cuối cùng, chúng tôi đã chọn một tiêu chuẩn tương đối đơn giản. Nó khá lỏng lẻo, không mang tính quy chuẩn, và đang trong quá trình hoàn thiện. <strong>Torrents.</strong> Các tệp metadata và thư mục dữ liệu nhị phân có thể được gói trong các torrent, với một torrent cho mỗi tệp metadata, hoặc một torrent cho mỗi thư mục dữ liệu nhị phân. Các torrent phải có tên tệp/thư mục gốc cộng với hậu tố <code>.torrent</code> làm tên tệp của chúng. <a %(wikipedia_annas_archive)s>Lưu trữ Anna</a> đã trở thành thư viện bóng tối lớn nhất thế giới, và là thư viện bóng tối duy nhất ở quy mô này hoàn toàn mã nguồn mở và dữ liệu mở. Dưới đây là một bảng từ trang Datasets của chúng tôi (đã được chỉnh sửa nhẹ): Chúng tôi đã đạt được điều này theo ba cách: Phản chiếu các thư viện bóng tối dữ liệu mở hiện có (như Sci-Hub và Library Genesis). Giúp đỡ các thư viện bóng tối muốn mở hơn, nhưng không có thời gian hoặc nguồn lực để làm điều đó (như bộ sưu tập truyện tranh Libgen). Thu thập dữ liệu từ các thư viện không muốn chia sẻ số lượng lớn (như Thư viện Z). Đối với (2) và (3), hiện tại chúng tôi quản lý một bộ sưu tập torrent đáng kể (hàng trăm TB). Cho đến nay, chúng tôi đã tiếp cận các bộ sưu tập này như những trường hợp riêng lẻ, nghĩa là cơ sở hạ tầng và tổ chức dữ liệu được thiết kế riêng cho từng bộ sưu tập. Điều này làm tăng đáng kể chi phí cho mỗi lần phát hành và đặc biệt khó khăn khi thực hiện các lần phát hành gia tăng. Đó là lý do tại sao chúng tôi quyết định tiêu chuẩn hóa các lần phát hành của mình. Đây là một bài viết kỹ thuật trong đó chúng tôi giới thiệu tiêu chuẩn của mình: <strong>Các Container của Lưu Trữ Anna</strong>. Các Container của Lưu trữ Anna (AAC): tiêu chuẩn hóa các phát hành từ thư viện bóng tối lớn nhất thế giới Lưu trữ Anna đã trở thành thư viện bóng tối lớn nhất thế giới, yêu cầu chúng tôi phải tiêu chuẩn hóa các phát hành của mình. Hơn 300GB bìa sách được phát hành Cuối cùng, chúng tôi vui mừng thông báo một bản phát hành nhỏ. Trong sự hợp tác với những người điều hành nhánh Libgen.rs, chúng tôi đang chia sẻ tất cả bìa sách của họ thông qua torrents và IPFS. Điều này sẽ phân phối tải trọng của việc xem bìa sách giữa nhiều máy hơn và sẽ bảo quản chúng tốt hơn. Trong nhiều trường hợp (nhưng không phải tất cả), bìa sách được bao gồm trong các tệp tin, vì vậy đây là loại "dữ liệu dẫn xuất". Nhưng có nó trong IPFS vẫn rất hữu ích cho hoạt động hàng ngày của cả Anna’s Archive và các nhánh khác nhau của Library Genesis. Như thường lệ, bạn có thể tìm thấy bản phát hành này tại Pirate Library Mirror (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Anna’s Archive</a>). Chúng tôi sẽ không liên kết đến nó ở đây, nhưng bạn có thể dễ dàng tìm thấy nó. Hy vọng chúng tôi có thể giảm tốc độ một chút, bây giờ chúng tôi có một lựa chọn thay thế khá tốt cho Thư viện Z. Khối lượng công việc này không thực sự bền vững. Nếu bạn quan tâm đến việc giúp đỡ với lập trình, vận hành máy chủ, hoặc công việc bảo tồn, hãy liên hệ với chúng tôi. Vẫn còn rất nhiều <a %(annas_archive)s>công việc cần làm</a>. Cảm ơn sự quan tâm và hỗ trợ của bạn. Chuyển sang ElasticSearch Một số truy vấn mất rất nhiều thời gian, đến mức chúng chiếm hết tất cả các kết nối mở. Theo mặc định, MySQL có độ dài từ tối thiểu, hoặc chỉ mục của bạn có thể trở nên rất lớn. Mọi người báo cáo không thể tìm kiếm “Ben Hur”. Tìm kiếm chỉ nhanh khi được tải đầy đủ vào bộ nhớ, điều này yêu cầu chúng tôi phải có một máy đắt tiền hơn để chạy điều này, cộng với một số lệnh để tải trước chỉ mục khi khởi động. Chúng tôi sẽ không thể mở rộng nó dễ dàng để xây dựng các tính năng mới, như <a %(wikipedia_cjk_characters)s>tách từ tốt hơn cho các ngôn ngữ không có khoảng trắng</a>, lọc/phân loại, sắp xếp, gợi ý "bạn có ý định", tự động hoàn thành, v.v. Một trong những <a %(annas_archive)s>vé</a> của chúng tôi là một túi vấn đề với hệ thống tìm kiếm của chúng tôi. Chúng tôi đã sử dụng tìm kiếm toàn văn MySQL, vì chúng tôi đã có tất cả dữ liệu của mình trong MySQL. Nhưng nó có những giới hạn: Sau khi nói chuyện với một loạt các chuyên gia, chúng tôi đã chọn ElasticSearch. Nó không hoàn hảo (các gợi ý "bạn có ý định" và tính năng tự động hoàn thành mặc định của họ không tốt), nhưng nhìn chung nó tốt hơn nhiều so với MySQL cho tìm kiếm. Chúng tôi vẫn chưa <a %(youtube)s>quá hài lòng</a> khi sử dụng nó cho bất kỳ dữ liệu quan trọng nào (mặc dù họ đã có nhiều <a %(elastic_co)s>tiến bộ</a>), nhưng nhìn chung chúng tôi khá hài lòng với sự chuyển đổi này. Hiện tại, chúng tôi đã triển khai tìm kiếm nhanh hơn nhiều, hỗ trợ ngôn ngữ tốt hơn, sắp xếp độ liên quan tốt hơn, các tùy chọn sắp xếp khác nhau và lọc theo ngôn ngữ/loại sách/loại tệp. Nếu bạn tò mò về cách nó hoạt động, <a %(annas_archive_l140)s>hãy</a> <a %(annas_archive_l1115)s>xem</a> <a %(annas_archive_l1635)s>qua</a>. Nó khá dễ tiếp cận, mặc dù có thể cần thêm một số chú thích… Kho lưu trữ của Anna là mã nguồn mở hoàn toàn Chúng tôi tin rằng thông tin nên được tự do, và mã nguồn của chúng tôi cũng không ngoại lệ. Chúng tôi đã phát hành tất cả mã nguồn của mình trên Gitlab riêng của chúng tôi: <a %(annas_archive)s>Phần mềm của Anna</a>. Chúng tôi cũng sử dụng trình theo dõi vấn đề để tổ chức công việc của mình. Nếu bạn muốn tham gia vào phát triển của chúng tôi, đây là một nơi tuyệt vời để bắt đầu. Để cho bạn thấy một chút về những gì chúng tôi đang làm, hãy xem công việc gần đây của chúng tôi về cải thiện hiệu suất phía khách hàng. Vì chúng tôi chưa thực hiện phân trang, chúng tôi thường trả về các trang tìm kiếm rất dài, với 100-200 kết quả. Chúng tôi không muốn cắt ngắn kết quả tìm kiếm quá sớm, nhưng điều này có nghĩa là nó sẽ làm chậm một số thiết bị. Để giải quyết vấn đề này, chúng tôi đã thực hiện một mẹo nhỏ: chúng tôi bọc hầu hết các kết quả tìm kiếm trong các bình luận HTML (<code><!-- --></code>), và sau đó viết một chút Javascript để phát hiện khi nào một kết quả nên trở nên hiển thị, vào lúc đó chúng tôi sẽ mở bình luận: DOM "ảo hóa" được triển khai trong 23 dòng, không cần thư viện cầu kỳ! Đây là loại mã thực dụng nhanh chóng mà bạn có khi có thời gian hạn chế và các vấn đề thực tế cần được giải quyết. Đã có báo cáo rằng tìm kiếm của chúng tôi hiện hoạt động tốt trên các thiết bị chậm! Một nỗ lực lớn khác là tự động hóa việc xây dựng cơ sở dữ liệu. Khi chúng tôi ra mắt, chúng tôi chỉ kéo các nguồn khác nhau một cách ngẫu nhiên. Bây giờ chúng tôi muốn giữ chúng được cập nhật, vì vậy chúng tôi đã viết một loạt các kịch bản để tải xuống metadata mới từ hai nhánh của Library Genesis và tích hợp chúng. Mục tiêu không chỉ là làm cho điều này hữu ích cho kho lưu trữ của chúng tôi, mà còn làm cho mọi thứ dễ dàng cho bất kỳ ai muốn thử nghiệm với metadata của thư viện bóng tối. Mục tiêu sẽ là một sổ tay Jupyter có tất cả các loại metadata thú vị có sẵn, để chúng tôi có thể thực hiện nhiều nghiên cứu hơn như tìm hiểu <a %(blog)s>phần trăm ISBN được bảo tồn mãi mãi</a>. Cuối cùng, chúng tôi đã cải tiến hệ thống quyên góp của mình. Bây giờ bạn có thể sử dụng thẻ tín dụng để gửi tiền trực tiếp vào ví tiền điện tử của chúng tôi, mà không cần thực sự biết gì về tiền điện tử. Chúng tôi sẽ tiếp tục theo dõi xem điều này hoạt động tốt như thế nào trong thực tế, nhưng đây là một bước tiến lớn. Với Thư viện Z bị sập và những người sáng lập (bị cáo buộc) của nó bị bắt, chúng tôi đã làm việc không ngừng nghỉ để cung cấp một giải pháp thay thế tốt với Kho lưu trữ của Anna (chúng tôi sẽ không liên kết nó ở đây, nhưng bạn có thể tìm kiếm trên Google). Dưới đây là một số điều chúng tôi đã đạt được gần đây. Cập nhật của Anna: kho lưu trữ mã nguồn mở hoàn toàn, ElasticSearch, hơn 300GB bìa sách Chúng tôi đã làm việc không ngừng nghỉ để cung cấp một giải pháp thay thế tốt với Kho lưu trữ của Anna. Dưới đây là một số điều chúng tôi đã đạt được gần đây. Phân tích Các bản sao ngữ nghĩa (các bản quét khác nhau của cùng một cuốn sách) về mặt lý thuyết có thể được lọc ra, nhưng điều này rất khó khăn. Khi xem xét thủ công qua các truyện tranh, chúng tôi đã tìm thấy quá nhiều kết quả dương tính giả. Có một số bản sao chỉ theo MD5, điều này tương đối lãng phí, nhưng việc lọc chúng ra chỉ giúp tiết kiệm khoảng 1% in. Ở quy mô này, đó vẫn là khoảng 1TB, nhưng cũng ở quy mô này, 1TB thực sự không quan trọng. Chúng tôi thà không mạo hiểm vô tình phá hủy dữ liệu trong quá trình này. Chúng tôi đã tìm thấy một loạt dữ liệu không phải sách, chẳng hạn như phim dựa trên truyện tranh. Điều đó cũng có vẻ lãng phí, vì những thứ này đã có sẵn rộng rãi thông qua các phương tiện khác. Tuy nhiên, chúng tôi nhận ra rằng chúng tôi không thể chỉ lọc ra các tệp phim, vì cũng có <em>truyện tranh tương tác</em> được phát hành trên máy tính, mà ai đó đã ghi lại và lưu dưới dạng phim. Cuối cùng, bất cứ thứ gì chúng tôi có thể xóa khỏi bộ sưu tập cũng chỉ tiết kiệm được vài phần trăm. Sau đó, chúng tôi nhớ rằng chúng tôi là những người tích trữ dữ liệu, và những người sẽ sao chép điều này cũng là những người tích trữ dữ liệu, vì vậy, “BẠN NÓI GÌ, XÓA?!” :) Khi bạn nhận được 95TB đổ vào cụm lưu trữ của mình, bạn cố gắng hiểu xem có gì trong đó… Chúng tôi đã thực hiện một số phân tích để xem liệu chúng tôi có thể giảm kích thước một chút hay không, chẳng hạn như bằng cách loại bỏ các bản sao. Dưới đây là một số phát hiện của chúng tôi: Do đó, chúng tôi xin giới thiệu với bạn, bộ sưu tập đầy đủ, không chỉnh sửa. Đó là rất nhiều dữ liệu, nhưng chúng tôi hy vọng đủ người sẽ quan tâm để chia sẻ nó. Hợp tác Với kích thước của nó, bộ sưu tập này đã nằm trong danh sách mong muốn của chúng tôi từ lâu, vì vậy sau khi thành công trong việc sao lưu Thư viện Z, chúng tôi đã đặt mục tiêu vào bộ sưu tập này. Ban đầu, chúng tôi đã trích xuất trực tiếp, điều này khá thách thức, vì máy chủ của họ không ở trong tình trạng tốt nhất. Chúng tôi đã thu được khoảng 15TB theo cách này, nhưng tiến độ rất chậm. May mắn thay, chúng tôi đã liên lạc được với người điều hành thư viện, người đã đồng ý gửi tất cả dữ liệu cho chúng tôi trực tiếp, điều này nhanh hơn rất nhiều. Tuy nhiên, vẫn mất hơn nửa năm để chuyển và xử lý tất cả dữ liệu, và chúng tôi suýt mất tất cả do hỏng đĩa, điều này có nghĩa là phải bắt đầu lại từ đầu. Trải nghiệm này khiến chúng tôi tin rằng điều quan trọng là phải đưa dữ liệu này ra ngoài càng nhanh càng tốt, để nó có thể được sao lưu rộng rãi. Chúng tôi chỉ còn một hoặc hai sự cố không may xảy ra là có thể mất bộ sưu tập này mãi mãi! Bộ sưu tập Di chuyển nhanh có nghĩa là bộ sưu tập có phần hơi lộn xộn… Hãy cùng xem qua. Hãy tưởng tượng chúng ta có một hệ thống tệp (thực tế chúng tôi đang chia nhỏ thành các torrent): Thư mục đầu tiên, <code>/repository</code>, là phần có cấu trúc hơn của điều này. Thư mục này chứa các “thousand dirs”: các thư mục mỗi thư mục có hàng nghìn tệp, được đánh số tăng dần trong cơ sở dữ liệu. Thư mục <code>0</code> chứa các tệp có comic_id từ 0–999, và cứ thế tiếp tục. Đây là cùng một sơ đồ mà Library Genesis đã sử dụng cho các bộ sưu tập tiểu thuyết và phi tiểu thuyết của mình. Ý tưởng là mỗi “thousand dir” sẽ tự động được chuyển thành một torrent ngay khi nó được lấp đầy. Tuy nhiên, người điều hành Libgen.li chưa bao giờ tạo torrent cho bộ sưu tập này, vì vậy các thousand dirs có thể trở nên bất tiện và nhường chỗ cho các “unsorted dirs”. Đây là <code>/comics0</code> đến <code>/comics4</code>. Tất cả đều chứa các cấu trúc thư mục độc đáo, có lẽ có ý nghĩa khi thu thập các tệp, nhưng bây giờ không còn ý nghĩa nhiều đối với chúng tôi. May mắn thay, metadata vẫn tham chiếu trực tiếp đến tất cả các tệp này, vì vậy tổ chức lưu trữ của chúng trên đĩa thực sự không quan trọng! Metadata có sẵn dưới dạng cơ sở dữ liệu MySQL. Điều này có thể được tải xuống trực tiếp từ trang web Libgen.li, nhưng chúng tôi cũng sẽ cung cấp nó trong một torrent, cùng với bảng của chúng tôi với tất cả các mã băm MD5. <q>Tiến sĩ Barbara Gordon cố gắng đánh mất mình trong thế giới tầm thường của thư viện…</q> Các nhánh của Libgen Trước tiên, một số thông tin cơ bản. Bạn có thể biết đến Library Genesis với bộ sưu tập sách đồ sộ của họ. Ít người biết rằng các tình nguyện viên của Library Genesis đã tạo ra các dự án khác, chẳng hạn như một bộ sưu tập tạp chí và tài liệu tiêu chuẩn lớn, một bản sao lưu đầy đủ của Sci-Hub (hợp tác với người sáng lập Sci-Hub, Alexandra Elbakyan), và thực sự là một bộ sưu tập truyện tranh khổng lồ. Tại một thời điểm nào đó, các nhà điều hành khác nhau của các bản sao Library Genesis đã đi theo con đường riêng của họ, điều này đã dẫn đến tình trạng hiện tại có một số "nhánh" khác nhau, tất cả vẫn mang tên Library Genesis. Nhánh Libgen.li đặc biệt có bộ sưu tập truyện tranh này, cũng như một bộ sưu tập tạp chí lớn (mà chúng tôi cũng đang làm việc). Gây quỹ Chúng tôi đang phát hành dữ liệu này trong một số phần lớn. Torrent đầu tiên là của <code>/comics0</code>, mà chúng tôi đã đặt vào một tệp .tar khổng lồ 12TB. Điều đó tốt hơn cho ổ cứng và phần mềm torrent của bạn hơn là hàng triệu tệp nhỏ hơn. Là một phần của đợt phát hành này, chúng tôi đang thực hiện một cuộc gây quỹ. Chúng tôi đang tìm cách huy động 20.000 đô la để trang trải chi phí hoạt động và hợp đồng cho bộ sưu tập này, cũng như cho phép các dự án đang diễn ra và trong tương lai. Chúng tôi có một số <em>dự án lớn</em> đang được thực hiện. <em>Tôi đang hỗ trợ ai với khoản quyên góp của mình?</em> Tóm lại: chúng tôi đang sao lưu tất cả kiến thức và văn hóa của nhân loại, và làm cho nó dễ dàng truy cập. Tất cả mã và dữ liệu của chúng tôi đều là mã nguồn mở, chúng tôi là một dự án hoàn toàn do tình nguyện viên điều hành, và chúng tôi đã lưu trữ được 125TB sách cho đến nay (ngoài các torrent hiện có của Libgen và Scihub). Cuối cùng, chúng tôi đang xây dựng một bánh đà cho phép và khuyến khích mọi người tìm kiếm, quét và sao lưu tất cả các cuốn sách trên thế giới. Chúng tôi sẽ viết về kế hoạch tổng thể của mình trong một bài viết tương lai. :) Nếu bạn quyên góp cho một thành viên “Amazing Archivist” 12 tháng (780 đô la), bạn sẽ được <strong>“nhận nuôi một torrent”</strong>, nghĩa là chúng tôi sẽ đặt tên người dùng hoặc thông điệp của bạn vào tên tệp của một trong các torrent! Bạn có thể quyên góp bằng cách truy cập <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a> và nhấp vào nút “Quyên góp”. Chúng tôi cũng đang tìm kiếm thêm tình nguyện viên: kỹ sư phần mềm, nhà nghiên cứu bảo mật, chuyên gia thương mại ẩn danh và dịch giả. Bạn cũng có thể hỗ trợ chúng tôi bằng cách cung cấp dịch vụ lưu trữ. Và tất nhiên, hãy chia sẻ các torrent của chúng tôi! Cảm ơn tất cả những người đã hào phóng hỗ trợ chúng tôi! Bạn thực sự đang tạo ra sự khác biệt. Dưới đây là các torrent đã phát hành cho đến nay (chúng tôi vẫn đang xử lý phần còn lại): Tất cả các torrent có thể được tìm thấy trên <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a> dưới mục “Datasets” (chúng tôi không liên kết trực tiếp ở đó, để các liên kết đến blog này không bị xóa khỏi Reddit, Twitter, v.v.). Từ đó, theo liên kết đến trang web Tor. <a %(news_ycombinator)s>Thảo luận trên Hacker News</a> Tiếp theo là gì? Một loạt các torrent rất tốt cho việc bảo quản lâu dài, nhưng không nhiều cho việc truy cập hàng ngày. Chúng tôi sẽ làm việc với các đối tác lưu trữ để đưa tất cả dữ liệu này lên web (vì Lưu trữ của Anna không lưu trữ bất cứ thứ gì trực tiếp). Tất nhiên bạn sẽ có thể tìm thấy các liên kết tải xuống này trên Lưu trữ của Anna. Chúng tôi cũng mời mọi người làm gì đó với dữ liệu này! Giúp chúng tôi phân tích tốt hơn, loại bỏ trùng lặp, đưa nó lên IPFS, remix nó, huấn luyện các mô hình AI của bạn với nó, v.v. Tất cả đều là của bạn, và chúng tôi không thể chờ đợi để xem bạn làm gì với nó. Cuối cùng, như đã nói trước đó, chúng tôi vẫn có một số phát hành lớn sắp tới (nếu <em>ai đó</em> có thể <em>vô tình</em> gửi cho chúng tôi một bản dump của một cơ sở dữ liệu <em>ACS4 nhất định</em>, bạn biết nơi để tìm chúng tôi…), cũng như xây dựng bánh đà để sao lưu tất cả các cuốn sách trên thế giới. Vì vậy, hãy theo dõi, chúng tôi chỉ mới bắt đầu. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Thư viện bóng tối truyện tranh lớn nhất có thể là của một nhánh Library Genesis cụ thể: Libgen.li. Người quản trị duy nhất điều hành trang web đó đã thu thập được một bộ sưu tập truyện tranh khổng lồ với hơn 2 triệu tệp, tổng cộng hơn 95TB. Tuy nhiên, không giống như các bộ sưu tập Library Genesis khác, bộ sưu tập này không có sẵn số lượng lớn thông qua torrents. Bạn chỉ có thể truy cập các truyện tranh này riêng lẻ thông qua máy chủ cá nhân chậm của anh ấy — một điểm thất bại duy nhất. Cho đến hôm nay! Trong bài viết này, chúng tôi sẽ cho bạn biết thêm về bộ sưu tập này và về chiến dịch gây quỹ của chúng tôi để hỗ trợ thêm cho công việc này. Anna’s Archive đã sao lưu thư viện bóng tối truyện tranh lớn nhất thế giới (95TB) — bạn có thể giúp gieo hạt Thư viện bóng tối truyện tranh lớn nhất thế giới đã có một điểm thất bại duy nhất... cho đến hôm nay. Cảnh báo: bài viết blog này đã bị ngừng sử dụng. Chúng tôi đã quyết định rằng IPFS chưa sẵn sàng cho thời điểm hiện tại. Chúng tôi vẫn sẽ liên kết đến các tệp trên IPFS từ Lưu trữ của Anna khi có thể, nhưng chúng tôi sẽ không tự lưu trữ nữa, cũng không khuyến khích người khác sao chép bằng IPFS. Vui lòng xem trang Torrents của chúng tôi nếu bạn muốn giúp bảo tồn bộ sưu tập của chúng tôi. Đưa 5.998.794 cuốn sách lên IPFS Nhân bản nhiều bản sao Quay lại câu hỏi ban đầu của chúng tôi: làm thế nào chúng tôi có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi? Vấn đề chính ở đây là bộ sưu tập của chúng tôi đã <a %(torrents_stats)s>tăng trưởng</a> nhanh chóng, bằng cách thu thập và mở mã nguồn một số bộ sưu tập lớn (trên nền tảng công việc tuyệt vời đã được thực hiện bởi các thư viện bóng dữ liệu mở khác như Sci-Hub và Library Genesis). Sự gia tăng dữ liệu này làm cho việc sao chép các bộ sưu tập trên toàn thế giới trở nên khó khăn hơn. Lưu trữ dữ liệu rất tốn kém! Nhưng chúng tôi lạc quan, đặc biệt khi quan sát ba xu hướng sau đây. <a %(annas_archive_stats)s>Tổng kích thước</a> của các bộ sưu tập của chúng tôi, trong vài tháng qua, được phân chia theo số lượng người chia sẻ torrent. Xu hướng giá HDD từ các nguồn khác nhau (nhấp để xem nghiên cứu). <a %(critical_window_chinese)s>Phiên bản tiếng Trung 中文版</a>, thảo luận trên <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Chúng tôi đã hái những quả thấp Điều này theo trực tiếp từ các ưu tiên của chúng tôi đã thảo luận ở trên. Chúng tôi thích làm việc để giải phóng các bộ sưu tập lớn trước. Bây giờ chúng tôi đã bảo đảm một số bộ sưu tập lớn nhất trên thế giới, chúng tôi mong đợi sự tăng trưởng của mình sẽ chậm hơn nhiều. Vẫn còn một đuôi dài của các bộ sưu tập nhỏ hơn, và sách mới được quét hoặc xuất bản mỗi ngày, nhưng tốc độ có thể sẽ chậm hơn nhiều. Chúng tôi có thể vẫn tăng gấp đôi hoặc thậm chí gấp ba kích thước, nhưng trong một khoảng thời gian dài hơn. Cải tiến OCR. Ưu tiên Mã phần mềm khoa học & kỹ thuật Phiên bản hư cấu hoặc giải trí của tất cả các mục trên Dữ liệu địa lý (ví dụ: bản đồ, khảo sát địa chất) Dữ liệu nội bộ từ các tập đoàn hoặc chính phủ (rò rỉ) Dữ liệu đo lường như đo lường khoa học, dữ liệu kinh tế, báo cáo doanh nghiệp Hồ sơ metadata nói chung (của phi hư cấu và hư cấu; của các phương tiện khác, nghệ thuật, con người, v.v.; bao gồm đánh giá) Sách phi hư cấu Tạp chí phi hư cấu, báo chí, sách hướng dẫn Bản ghi phi hư cấu của các buổi nói chuyện, phim tài liệu, podcast Dữ liệu hữu cơ như chuỗi DNA, hạt giống thực vật, hoặc mẫu vi sinh vật Các bài báo học thuật, tạp chí, báo cáo Các trang web khoa học & kỹ thuật, thảo luận trực tuyến Bản ghi chép của các thủ tục pháp lý hoặc tòa án Có nguy cơ bị phá hủy đặc biệt (ví dụ: do chiến tranh, cắt giảm tài trợ, kiện tụng, hoặc đàn áp chính trị) Hiếm Được chú ý đặc biệt Tại sao chúng tôi lại quan tâm nhiều đến các bài báo và sách? Hãy gác lại niềm tin cơ bản của chúng tôi về việc bảo tồn nói chung — chúng tôi có thể viết một bài viết khác về điều đó. Vậy tại sao lại là các bài báo và sách cụ thể? Câu trả lời rất đơn giản: <strong>mật độ thông tin</strong>. Với mỗi megabyte lưu trữ, văn bản viết lưu trữ nhiều thông tin nhất trong tất cả các phương tiện. Mặc dù chúng tôi quan tâm đến cả kiến thức và văn hóa, nhưng chúng tôi quan tâm nhiều hơn đến cái trước. Nhìn chung, chúng tôi tìm thấy một hệ thống phân cấp về mật độ thông tin và tầm quan trọng của việc bảo tồn trông đại khái như sau: Xếp hạng trong danh sách này có phần tùy ý — một số mục có sự đồng hạng hoặc có sự bất đồng trong nhóm của chúng tôi — và có lẽ chúng tôi đã quên một số danh mục quan trọng. Nhưng đây là cách chúng tôi ưu tiên một cách tổng quát. Một số mục trong danh sách này quá khác biệt so với các mục khác để chúng tôi lo lắng (hoặc đã được các tổ chức khác xử lý), chẳng hạn như dữ liệu hữu cơ hoặc dữ liệu địa lý. Nhưng hầu hết các mục trong danh sách này thực sự quan trọng đối với chúng tôi. Một yếu tố lớn khác trong việc ưu tiên của chúng tôi là mức độ rủi ro của một tác phẩm nhất định. Chúng tôi thích tập trung vào các tác phẩm mà: Cuối cùng, chúng tôi quan tâm đến quy mô. Chúng tôi có thời gian và tiền bạc hạn chế, vì vậy chúng tôi thà dành một tháng để cứu 10.000 cuốn sách hơn là 1.000 cuốn sách — nếu chúng có giá trị và rủi ro tương đương. <em><q>Những gì đã mất không thể phục hồi; nhưng hãy cứu những gì còn lại: không phải bằng cách khóa và khóa chúng khỏi tầm mắt và sử dụng của công chúng, trong việc giao chúng cho sự lãng phí của thời gian, mà bằng cách nhân bản nhiều bản sao, để đặt chúng ngoài tầm với của tai nạn.</q></em><br>— Thomas Jefferson, 1791 Thư viện bóng tối Mã có thể là mã nguồn mở trên Github, nhưng Github như một tổng thể không thể dễ dàng sao chép và do đó không thể bảo tồn (mặc dù trong trường hợp cụ thể này có đủ bản sao phân phối của hầu hết các kho mã) Các bản ghi metadata có thể được xem tự do trên trang web Worldcat, nhưng không thể tải xuống hàng loạt (cho đến khi chúng tôi <a %(worldcat_scrape)s>thu thập</a> chúng) Reddit miễn phí sử dụng, nhưng gần đây đã đưa ra các biện pháp chống thu thập dữ liệu nghiêm ngặt, sau khi LLM đào tạo dữ liệu khát khao (sẽ nói thêm về điều đó sau) Có nhiều tổ chức có sứ mệnh tương tự và ưu tiên tương tự. Thực tế, có các thư viện, kho lưu trữ, phòng thí nghiệm, bảo tàng và các tổ chức khác được giao nhiệm vụ bảo tồn loại này. Nhiều trong số đó được tài trợ tốt, bởi chính phủ, cá nhân, hoặc tập đoàn. Nhưng họ có một điểm mù lớn: hệ thống pháp lý. Đây là vai trò độc đáo của các thư viện bóng tối, và lý do Anna’s Archive tồn tại. Chúng tôi có thể làm những điều mà các tổ chức khác không được phép làm. Bây giờ, không phải (thường) là chúng tôi có thể lưu trữ các tài liệu mà không được phép bảo tồn ở nơi khác. Không, ở nhiều nơi, việc xây dựng một kho lưu trữ với bất kỳ sách, bài báo, tạp chí nào là hợp pháp. Nhưng điều mà các kho lưu trữ hợp pháp thường thiếu là <strong>tính dư thừa và độ bền lâu dài</strong>. Có những cuốn sách mà chỉ có một bản sao tồn tại trong một thư viện vật lý nào đó. Có những hồ sơ metadata được bảo vệ bởi một công ty duy nhất. Có những tờ báo chỉ được bảo quản trên vi phim trong một kho lưu trữ duy nhất. Thư viện có thể bị cắt giảm tài trợ, công ty có thể phá sản, kho lưu trữ có thể bị đánh bom và thiêu rụi. Đây không phải là giả thuyết — điều này xảy ra mọi lúc. Điều mà chúng tôi có thể làm một cách độc đáo tại Lưu trữ của Anna là lưu trữ nhiều bản sao của các tác phẩm, ở quy mô lớn. Chúng tôi có thể thu thập các bài báo, sách, tạp chí và nhiều hơn nữa, và phân phối chúng hàng loạt. Hiện tại, chúng tôi thực hiện điều này thông qua torrents, nhưng công nghệ chính xác không quan trọng và sẽ thay đổi theo thời gian. Phần quan trọng là phân phối nhiều bản sao trên khắp thế giới. Câu nói này từ hơn 200 năm trước vẫn còn đúng: Một ghi chú nhanh về phạm vi công cộng. Vì Lưu trữ của Anna tập trung độc đáo vào các hoạt động bất hợp pháp ở nhiều nơi trên thế giới, chúng tôi không bận tâm đến các bộ sưu tập có sẵn rộng rãi, chẳng hạn như sách thuộc phạm vi công cộng. Các tổ chức pháp lý thường đã chăm sóc tốt cho điều đó. Tuy nhiên, có những cân nhắc khiến chúng tôi đôi khi làm việc trên các bộ sưu tập có sẵn công khai: - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Chi phí lưu trữ tiếp tục giảm theo cấp số nhân 3. Cải tiến về mật độ thông tin Hiện tại, chúng tôi lưu trữ sách ở định dạng thô mà chúng được cung cấp cho chúng tôi. Chắc chắn, chúng đã được nén, nhưng thường thì chúng vẫn là các bản quét lớn hoặc ảnh chụp của các trang. Cho đến nay, các lựa chọn duy nhất để thu nhỏ tổng kích thước bộ sưu tập của chúng tôi là thông qua nén mạnh hơn hoặc loại bỏ trùng lặp. Tuy nhiên, để tiết kiệm đáng kể, cả hai đều quá mất mát đối với chúng tôi. Nén mạnh ảnh có thể làm cho văn bản khó đọc. Và loại bỏ trùng lặp đòi hỏi sự tự tin cao rằng các cuốn sách hoàn toàn giống nhau, điều này thường không chính xác, đặc biệt nếu nội dung giống nhau nhưng các bản quét được thực hiện vào các dịp khác nhau. Luôn có một lựa chọn thứ ba, nhưng chất lượng của nó đã quá tệ đến mức chúng tôi chưa bao giờ xem xét: <strong>OCR, hay Nhận dạng Ký tự Quang học</strong>. Đây là quá trình chuyển đổi ảnh thành văn bản thuần túy, bằng cách sử dụng AI để phát hiện các ký tự trong ảnh. Các công cụ cho việc này đã tồn tại từ lâu và khá tốt, nhưng “khá tốt” là không đủ cho mục đích bảo tồn. Tuy nhiên, các mô hình học sâu đa phương thức gần đây đã tiến bộ cực kỳ nhanh chóng, mặc dù vẫn có chi phí cao. Chúng tôi kỳ vọng cả độ chính xác và chi phí sẽ được cải thiện đáng kể trong những năm tới, đến mức nó sẽ trở nên thực tế để áp dụng cho toàn bộ thư viện của chúng tôi. Khi điều đó xảy ra, chúng tôi có thể vẫn sẽ bảo tồn các tệp gốc, nhưng ngoài ra chúng tôi có thể có một phiên bản nhỏ hơn nhiều của thư viện mà hầu hết mọi người sẽ muốn sao chép. Điều thú vị là văn bản thô tự nó nén tốt hơn nhiều và dễ dàng loại bỏ trùng lặp hơn, mang lại cho chúng tôi nhiều tiết kiệm hơn. Nhìn chung, không phải là không thực tế khi mong đợi ít nhất giảm 5-10 lần tổng kích thước tệp, có thể thậm chí nhiều hơn. Ngay cả với mức giảm bảo thủ 5 lần, chúng tôi sẽ xem xét <strong>1.000–3.000 đô la trong 10 năm ngay cả khi thư viện của chúng tôi tăng gấp ba lần kích thước</strong>. Tại thời điểm viết bài, <a %(diskprices)s>giá đĩa</a> mỗi TB khoảng $12 cho đĩa mới, $8 cho đĩa đã qua sử dụng, và $4 cho băng. Nếu chúng tôi bảo thủ và chỉ nhìn vào đĩa mới, điều đó có nghĩa là lưu trữ một petabyte tốn khoảng $12,000. Nếu chúng tôi giả định thư viện của mình sẽ tăng gấp ba từ 900TB lên 2.7PB, điều đó có nghĩa là $32,400 để sao chép toàn bộ thư viện của chúng tôi. Thêm điện, chi phí phần cứng khác, và vân vân, hãy làm tròn lên $40,000. Hoặc với băng nhiều hơn như $15,000–$20,000. Một mặt <strong>$15,000–$40,000 cho tổng số kiến thức của nhân loại là một món hời</strong>. Mặt khác, hơi cao để mong đợi hàng tấn bản sao đầy đủ, đặc biệt nếu chúng tôi cũng muốn những người đó tiếp tục gieo hạt torrents của họ vì lợi ích của người khác. Đó là hôm nay. Nhưng tiến bộ vẫn tiếp tục: Chi phí ổ cứng mỗi TB đã giảm khoảng một phần ba trong 10 năm qua, và có khả năng sẽ tiếp tục giảm với tốc độ tương tự. Băng dường như đang trên một quỹ đạo tương tự. Giá SSD đang giảm thậm chí nhanh hơn, và có thể sẽ vượt qua giá HDD vào cuối thập kỷ này. Nếu điều này đúng, thì trong 10 năm nữa chúng ta có thể chỉ cần từ 5.000–13.000 đô la để sao chép toàn bộ bộ sưu tập của mình (1/3), hoặc thậm chí ít hơn nếu chúng ta phát triển ít hơn về kích thước. Mặc dù vẫn là một số tiền lớn, nhưng điều này sẽ có thể đạt được đối với nhiều người. Và nó có thể còn tốt hơn vì điểm tiếp theo… Tại Lưu trữ của Anna, chúng tôi thường được hỏi làm thế nào chúng tôi có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi, khi tổng kích thước đã gần đạt 1 Petabyte (1000 TB) và vẫn đang tăng. Trong bài viết này, chúng tôi sẽ xem xét triết lý của mình và xem tại sao thập kỷ tới là quan trọng đối với sứ mệnh bảo tồn kiến thức và văn hóa của nhân loại. Cửa sổ quan trọng Nếu những dự báo này chính xác, chúng tôi <strong>chỉ cần đợi vài năm</strong> trước khi toàn bộ bộ sưu tập của chúng tôi sẽ được sao chép rộng rãi. Do đó, theo lời của Thomas Jefferson, “được đặt ngoài tầm với của tai nạn.” Thật không may, sự xuất hiện của LLMs, và việc đào tạo dữ liệu khát khao của chúng, đã đặt nhiều chủ sở hữu bản quyền vào thế phòng thủ. Thậm chí còn hơn cả trước đây. Nhiều trang web đang làm cho việc thu thập và lưu trữ trở nên khó khăn hơn, các vụ kiện tụng đang diễn ra, và trong khi đó các thư viện và lưu trữ vật lý tiếp tục bị bỏ quên. Chúng tôi chỉ có thể mong đợi những xu hướng này tiếp tục xấu đi, và nhiều tác phẩm sẽ bị mất trước khi chúng bước vào phạm vi công cộng. <strong>Chúng ta đang ở ngưỡng cửa của một cuộc cách mạng trong bảo tồn, nhưng <q>những gì đã mất không thể phục hồi.</q></strong> Chúng ta có một cửa sổ quan trọng khoảng 5-10 năm trong đó vẫn còn khá đắt đỏ để vận hành một thư viện bóng tối và tạo ra nhiều bản sao trên khắp thế giới, và trong đó quyền truy cập chưa bị đóng hoàn toàn. Nếu chúng ta có thể vượt qua cửa sổ này, thì chúng ta sẽ thực sự bảo tồn kiến thức và văn hóa của nhân loại mãi mãi. Chúng ta không nên để thời gian này trôi qua vô ích. Chúng ta không nên để cửa sổ quan trọng này đóng lại với chúng ta. Hãy tiến lên. Cửa sổ quan trọng của các thư viện bóng tối Làm thế nào chúng ta có thể tuyên bố bảo tồn các bộ sưu tập của mình mãi mãi, khi chúng đã gần đạt 1 PB? Bộ sưu tập Một số thông tin thêm về bộ sưu tập. <a %(duxiu)s>Duxiu</a> là một cơ sở dữ liệu khổng lồ về sách đã quét, được tạo ra bởi <a %(chaoxing)s>Nhóm Thư viện Kỹ thuật số SuperStar</a>. Hầu hết là sách học thuật, được quét để làm cho chúng có sẵn kỹ thuật số cho các trường đại học và thư viện. Đối với khán giả nói tiếng Anh của chúng tôi, <a %(library_princeton)s>Princeton</a> và <a %(guides_lib_uw)s>Đại học Washington</a> có cái nhìn tổng quan tốt. Cũng có một bài viết xuất sắc cung cấp thêm bối cảnh: <a %(doi)s>“Số hóa Sách Trung Quốc: Nghiên cứu Trường hợp của Công cụ Tìm kiếm Học giả SuperStar DuXiu”</a> (tìm kiếm trong Lưu trữ của Anna). Những cuốn sách từ Duxiu đã lâu bị vi phạm bản quyền trên internet Trung Quốc. Thông thường chúng được bán với giá chưa đến một đô la bởi các nhà bán lẻ. Chúng thường được phân phối bằng cách sử dụng phiên bản tương đương của Google Drive tại Trung Quốc, thường bị hack để cho phép có thêm không gian lưu trữ. Một số chi tiết kỹ thuật có thể được tìm thấy <a %(github_duty_machine)s>tại đây</a> và <a %(github_821_github_io)s>tại đây</a>. Mặc dù các cuốn sách đã được phân phối bán công khai, nhưng khá khó để có được chúng với số lượng lớn. Chúng tôi đã đặt điều này cao trong danh sách công việc của mình và đã dành nhiều tháng làm việc toàn thời gian cho nó. Tuy nhiên, gần đây một tình nguyện viên tuyệt vời, đáng kinh ngạc và tài năng đã liên hệ với chúng tôi, nói rằng họ đã làm tất cả công việc này rồi — với chi phí lớn. Họ đã chia sẻ toàn bộ bộ sưu tập với chúng tôi, mà không mong đợi bất cứ điều gì đáp lại, ngoại trừ sự đảm bảo bảo tồn lâu dài. Thật đáng kinh ngạc. Họ đã đồng ý yêu cầu sự giúp đỡ theo cách này để bộ sưu tập được OCR. Bộ sưu tập gồm 7.543.702 tệp. Đây là nhiều hơn Thư viện Genesis phi hư cấu (khoảng 5,3 triệu). Tổng kích thước tệp là khoảng 359TB (326TiB) ở dạng hiện tại. Chúng tôi mở cửa cho các đề xuất và ý tưởng khác. Chỉ cần liên hệ với chúng tôi. Hãy xem Lưu trữ của Anna để biết thêm thông tin về các bộ sưu tập của chúng tôi, nỗ lực bảo tồn và cách bạn có thể giúp đỡ. Cảm ơn! Trang ví dụ Để chứng minh cho chúng tôi thấy rằng bạn có một quy trình tốt, đây là một số trang ví dụ để bắt đầu, từ một cuốn sách về siêu dẫn. Quy trình của bạn nên xử lý đúng cách các công thức toán học, bảng biểu, biểu đồ, chú thích cuối trang, v.v. Gửi các trang đã xử lý của bạn đến email của chúng tôi. Nếu chúng trông ổn, chúng tôi sẽ gửi cho bạn nhiều hơn trong riêng tư, và chúng tôi mong bạn có thể nhanh chóng chạy quy trình của mình trên những trang đó. Khi chúng tôi hài lòng, chúng ta có thể thỏa thuận. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Phiên bản tiếng Trung 中文版</a>, <a %(news_ycombinator)s>Thảo luận trên Hacker News</a> Đây là một bài viết blog ngắn. Chúng tôi đang tìm kiếm một công ty hoặc tổ chức nào đó để giúp chúng tôi với OCR và trích xuất văn bản cho một bộ sưu tập khổng lồ mà chúng tôi đã thu thập, đổi lại là quyền truy cập sớm độc quyền. Sau thời gian cấm vận, chúng tôi tất nhiên sẽ phát hành toàn bộ bộ sưu tập. Văn bản học thuật chất lượng cao cực kỳ hữu ích cho việc đào tạo các LLM. Mặc dù bộ sưu tập của chúng tôi là tiếng Trung, điều này vẫn có thể hữu ích cho việc đào tạo các LLM tiếng Anh: các mô hình dường như mã hóa các khái niệm và kiến thức bất kể ngôn ngữ nguồn. Để làm điều này, văn bản cần được trích xuất từ các bản quét. Lưu trữ của Anna được gì từ việc này? Tìm kiếm toàn văn của các cuốn sách cho người dùng của mình. Vì mục tiêu của chúng tôi phù hợp với các nhà phát triển LLM, chúng tôi đang tìm kiếm một cộng tác viên. Chúng tôi sẵn sàng cung cấp cho bạn <strong>quyền truy cập sớm độc quyền vào bộ sưu tập này với số lượng lớn trong 1 năm</strong>, nếu bạn có thể thực hiện OCR và trích xuất văn bản đúng cách. Nếu bạn sẵn lòng chia sẻ toàn bộ mã của quy trình của bạn với chúng tôi, chúng tôi sẽ sẵn sàng kéo dài thời gian cấm vận bộ sưu tập. Quyền truy cập độc quyền cho các công ty LLM vào bộ sưu tập sách phi hư cấu Trung Quốc lớn nhất thế giới <em><strong>TL;DR:</strong> Lưu trữ của Anna đã thu thập được một bộ sưu tập độc đáo gồm 7,5 triệu / 350TB sách phi hư cấu Trung Quốc — lớn hơn Thư viện Genesis. Chúng tôi sẵn sàng cung cấp quyền truy cập độc quyền cho một công ty LLM, đổi lại là OCR chất lượng cao và trích xuất văn bản.</em> Kiến trúc hệ thống Vậy hãy giả sử rằng bạn đã tìm thấy một số công ty sẵn sàng lưu trữ trang web của bạn mà không đóng cửa bạn — hãy gọi họ là “nhà cung cấp yêu tự do” 😄. Bạn sẽ nhanh chóng nhận ra rằng lưu trữ mọi thứ với họ khá đắt đỏ, vì vậy bạn có thể muốn tìm một số “nhà cung cấp giá rẻ” và thực hiện việc lưu trữ thực tế ở đó, thông qua các nhà cung cấp yêu tự do. Nếu bạn làm đúng, các nhà cung cấp giá rẻ sẽ không bao giờ biết bạn đang lưu trữ gì, và không bao giờ nhận được bất kỳ khiếu nại nào. Với tất cả các nhà cung cấp này, có nguy cơ họ vẫn đóng cửa bạn, vì vậy bạn cũng cần có sự dự phòng. Chúng tôi cần điều này ở tất cả các cấp độ của công nghệ của chúng tôi. Một công ty có phần yêu tự do đã đặt mình vào một vị trí thú vị là Cloudflare. Họ đã <a %(blog_cloudflare)s>lập luận</a> rằng họ không phải là nhà cung cấp lưu trữ, mà là một tiện ích, giống như một ISP. Do đó, họ không phải tuân theo DMCA hoặc các yêu cầu gỡ bỏ khác, và chuyển tiếp bất kỳ yêu cầu nào đến nhà cung cấp lưu trữ thực tế của bạn. Họ đã đi xa đến mức ra tòa để bảo vệ cấu trúc này. Do đó, chúng tôi có thể sử dụng họ như một lớp bộ nhớ đệm và bảo vệ khác. Cloudflare không chấp nhận thanh toán ẩn danh, vì vậy chúng tôi chỉ có thể sử dụng gói miễn phí của họ. Điều này có nghĩa là chúng tôi không thể sử dụng các tính năng cân bằng tải hoặc chuyển đổi dự phòng của họ. Do đó, chúng tôi <a %(annas_archive_l255)s>tự triển khai điều này</a> ở cấp độ tên miền. Khi tải trang, trình duyệt sẽ kiểm tra xem tên miền hiện tại có còn khả dụng không, và nếu không, nó sẽ viết lại tất cả các URL sang một tên miền khác. Vì Cloudflare lưu trữ nhiều trang, điều này có nghĩa là người dùng có thể truy cập vào tên miền chính của chúng tôi, ngay cả khi máy chủ proxy bị sập, và sau đó trong lần nhấp tiếp theo sẽ được chuyển sang một tên miền khác. Chúng tôi vẫn còn phải đối mặt với các mối quan tâm hoạt động bình thường, chẳng hạn như giám sát sức khỏe máy chủ, ghi lại lỗi backend và frontend, v.v. Kiến trúc chuyển đổi dự phòng của chúng tôi cho phép có thêm độ bền vững trên mặt trận này, ví dụ bằng cách chạy một bộ máy chủ hoàn toàn khác trên một trong các tên miền. Chúng tôi thậm chí có thể chạy các phiên bản cũ hơn của mã và Datasets trên tên miền riêng biệt này, trong trường hợp một lỗi nghiêm trọng trong phiên bản chính không được phát hiện. Chúng tôi cũng có thể phòng ngừa việc Cloudflare quay lưng lại với chúng tôi, bằng cách loại bỏ nó khỏi một trong các tên miền, chẳng hạn như tên miền riêng biệt này. Các hoán vị khác nhau của những ý tưởng này là có thể. Kết luận Đó là một trải nghiệm thú vị khi học cách thiết lập một công cụ tìm kiếm thư viện bóng tối mạnh mẽ và bền bỉ. Có rất nhiều chi tiết khác để chia sẻ trong các bài viết sau, vì vậy hãy cho tôi biết bạn muốn tìm hiểu thêm về điều gì! Như mọi khi, chúng tôi đang tìm kiếm các khoản quyên góp để hỗ trợ công việc này, vì vậy hãy chắc chắn kiểm tra trang Quyên góp trên Lưu trữ của Anna. Chúng tôi cũng đang tìm kiếm các loại hỗ trợ khác, chẳng hạn như tài trợ, nhà tài trợ dài hạn, nhà cung cấp thanh toán rủi ro cao, có thể thậm chí là quảng cáo (có gu thẩm mỹ!). Và nếu bạn muốn đóng góp thời gian và kỹ năng của mình, chúng tôi luôn tìm kiếm các nhà phát triển, dịch giả, v.v. Cảm ơn sự quan tâm và hỗ trợ của bạn. Thẻ đổi mới Hãy bắt đầu với công nghệ của chúng tôi. Nó cố ý nhàm chán. Chúng tôi sử dụng Flask, MariaDB và ElasticSearch. Đó là tất cả. Tìm kiếm phần lớn là một vấn đề đã được giải quyết, và chúng tôi không có ý định phát minh lại nó. Ngoài ra, chúng tôi phải dành <a %(mcfunley)s>thẻ đổi mới</a> của mình cho một thứ khác: không bị chính quyền gỡ bỏ. Vậy Anna’s Archive hợp pháp hay bất hợp pháp đến mức nào? Điều này chủ yếu phụ thuộc vào khu vực pháp lý. Hầu hết các quốc gia tin vào một hình thức bản quyền nào đó, có nghĩa là người hoặc công ty được chỉ định một độc quyền nhất định đối với một số loại tác phẩm trong một khoảng thời gian nhất định. Ngoài ra, tại Anna’s Archive, chúng tôi tin rằng mặc dù có một số lợi ích, nhưng nhìn chung bản quyền là một điều tiêu cực cho xã hội — nhưng đó là một câu chuyện khác. Độc quyền này đối với một số tác phẩm có nghĩa là bất kỳ ai ngoài độc quyền này đều không được phép phân phối trực tiếp các tác phẩm đó — bao gồm cả chúng tôi. Nhưng Anna’s Archive là một công cụ tìm kiếm không phân phối trực tiếp các tác phẩm đó (ít nhất là không trên trang web clearnet của chúng tôi), vì vậy chúng tôi nên ổn, phải không? Không hẳn. Ở nhiều khu vực pháp lý, không chỉ việc phân phối các tác phẩm có bản quyền là bất hợp pháp, mà còn việc liên kết đến các nơi làm điều đó cũng vậy. Một ví dụ kinh điển về điều này là luật DMCA của Hoa Kỳ. Đó là đầu cực kỳ nghiêm ngặt của phổ. Ở đầu kia của phổ, về lý thuyết có thể có các quốc gia không có luật bản quyền nào cả, nhưng thực tế không tồn tại. Hầu như mọi quốc gia đều có một số hình thức luật bản quyền. Việc thực thi là một câu chuyện khác. Có rất nhiều quốc gia có chính phủ không quan tâm đến việc thực thi luật bản quyền. Cũng có những quốc gia nằm giữa hai cực đoan, cấm phân phối các tác phẩm có bản quyền, nhưng không cấm liên kết đến các tác phẩm đó. Một cân nhắc khác là ở cấp độ công ty. Nếu một công ty hoạt động trong một khu vực pháp lý không quan tâm đến bản quyền, nhưng bản thân công ty không sẵn sàng chấp nhận bất kỳ rủi ro nào, thì họ có thể đóng cửa trang web của bạn ngay khi có ai đó phàn nàn về nó. Cuối cùng, một cân nhắc lớn là thanh toán. Vì chúng tôi cần giữ ẩn danh, chúng tôi không thể sử dụng các phương thức thanh toán truyền thống. Điều này khiến chúng tôi chỉ còn lại tiền điện tử, và chỉ có một số ít công ty hỗ trợ điều đó (có các thẻ ghi nợ ảo được thanh toán bằng tiền điện tử, nhưng chúng thường không được chấp nhận). - Anna và đội ngũ (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Tôi điều hành <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, công cụ tìm kiếm mã nguồn mở phi lợi nhuận lớn nhất thế giới cho <a %(wikipedia_shadow_library)s>thư viện bóng tối</a>, như Sci-Hub, Library Genesis và Thư viện Z. Mục tiêu của chúng tôi là làm cho kiến thức và văn hóa dễ dàng tiếp cận, và cuối cùng là xây dựng một cộng đồng những người cùng nhau lưu trữ và bảo tồn <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>tất cả các cuốn sách trên thế giới</a>. Trong bài viết này, tôi sẽ chỉ ra cách chúng tôi vận hành trang web này, và những thách thức độc đáo đi kèm với việc điều hành một trang web có tình trạng pháp lý đáng ngờ, vì không có “AWS cho các tổ chức từ thiện bóng tối”. <em>Cũng hãy xem bài viết chị em <a %(blog_how_to_become_a_pirate_archivist)s>Cách trở thành một nhà lưu trữ hải tặc</a>.</em> Cách vận hành một thư viện bóng tối: hoạt động tại Lưu trữ của Anna Không có <q>AWS cho các tổ chức từ thiện bóng tối,</q> vậy làm thế nào chúng tôi vận hành Lưu trữ của Anna? Công cụ Máy chủ ứng dụng: Flask, MariaDB, ElasticSearch, Docker. Phát triển: Gitlab, Weblate, Zulip. Quản lý máy chủ: Ansible, Checkmk, UFW. Lưu trữ tĩnh Onion: Tor, Nginx. Máy chủ proxy: Varnish. Hãy xem các công cụ chúng tôi sử dụng để thực hiện tất cả những điều này. Điều này đang phát triển rất nhiều khi chúng tôi gặp phải các vấn đề mới và tìm ra các giải pháp mới. Có một số quyết định mà chúng tôi đã thay đổi qua lại. Một trong số đó là giao tiếp giữa các máy chủ: chúng tôi từng sử dụng Wireguard cho việc này, nhưng nhận thấy rằng đôi khi nó ngừng truyền dữ liệu hoặc chỉ truyền dữ liệu theo một hướng. Điều này đã xảy ra với một số thiết lập Wireguard khác nhau mà chúng tôi đã thử, chẳng hạn như <a %(github_costela_wesher)s>wesher</a> và <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Chúng tôi cũng đã thử đường hầm cổng qua SSH, sử dụng autossh và sshuttle, nhưng gặp <a %(github_sshuttle)s>vấn đề ở đó</a> (mặc dù tôi vẫn chưa rõ liệu autossh có gặp vấn đề TCP-over-TCP hay không — nó chỉ cảm thấy như một giải pháp không ổn định đối với tôi nhưng có thể nó thực sự ổn?). Thay vào đó, chúng tôi đã quay lại kết nối trực tiếp giữa các máy chủ, ẩn rằng một máy chủ đang chạy trên các nhà cung cấp giá rẻ bằng cách sử dụng lọc IP với UFW. Điều này có nhược điểm là Docker không hoạt động tốt với UFW, trừ khi bạn sử dụng <code>network_mode: "host"</code>. Tất cả điều này có phần dễ mắc lỗi hơn, vì bạn sẽ phơi bày máy chủ của mình ra internet chỉ với một cấu hình sai nhỏ. Có lẽ chúng tôi nên quay lại sử dụng autossh — phản hồi sẽ rất được hoan nghênh ở đây. Chúng tôi cũng đã thay đổi qua lại giữa Varnish và Nginx. Hiện tại chúng tôi thích Varnish, nhưng nó có những điểm kỳ quặc và góc cạnh. Điều tương tự cũng áp dụng cho Checkmk: chúng tôi không yêu thích nó, nhưng nó hoạt động tạm thời. Weblate đã ổn nhưng không tuyệt vời — đôi khi tôi lo sợ nó sẽ mất dữ liệu của tôi mỗi khi tôi cố gắng đồng bộ hóa nó với kho git của chúng tôi. Flask nhìn chung đã tốt, nhưng nó có một số điểm kỳ quặc kỳ lạ đã tốn rất nhiều thời gian để gỡ lỗi, chẳng hạn như cấu hình tên miền tùy chỉnh hoặc các vấn đề với tích hợp SqlAlchemy của nó. Cho đến nay, các công cụ khác đã rất tuyệt vời: chúng tôi không có khiếu nại nghiêm trọng nào về MariaDB, ElasticSearch, Gitlab, Zulip, Docker và Tor. Tất cả những điều này đã gặp một số vấn đề, nhưng không có gì quá nghiêm trọng hoặc tốn thời gian. Cộng đồng Thách thức đầu tiên có thể là một điều bất ngờ. Nó không phải là một vấn đề kỹ thuật, hay một vấn đề pháp lý. Đó là một vấn đề tâm lý: làm công việc này trong bóng tối có thể vô cùng cô đơn. Tùy thuộc vào những gì bạn dự định làm, và mô hình mối đe dọa của bạn, bạn có thể phải rất cẩn thận. Ở một đầu của phổ, chúng ta có những người như Alexandra Elbakyan*, người sáng lập Sci-Hub, người rất cởi mở về các hoạt động của mình. Nhưng cô ấy có nguy cơ cao bị bắt nếu cô ấy đến thăm một quốc gia phương Tây vào thời điểm này, và có thể đối mặt với hàng thập kỷ tù giam. Đó có phải là rủi ro bạn sẵn sàng chấp nhận không? Chúng tôi ở đầu kia của phổ; rất cẩn thận để không để lại bất kỳ dấu vết nào, và có an ninh hoạt động mạnh mẽ. * Như đã đề cập trên HN bởi "ynno", ban đầu Alexandra không muốn được biết đến: "Máy chủ của cô ấy được thiết lập để phát ra các thông báo lỗi chi tiết từ PHP, bao gồm đường dẫn đầy đủ của tệp nguồn bị lỗi, nằm dưới thư mục /home/<USER>" Vì vậy, hãy sử dụng tên người dùng ngẫu nhiên trên các máy tính bạn sử dụng cho công việc này, trong trường hợp bạn cấu hình sai điều gì đó. Tuy nhiên, sự bí mật đó đi kèm với một cái giá tâm lý. Hầu hết mọi người đều thích được công nhận cho công việc mà họ làm, nhưng bạn không thể nhận bất kỳ công lao nào cho điều này trong đời thực. Ngay cả những điều đơn giản cũng có thể trở nên thách thức, như bạn bè hỏi bạn đã làm gì (đến một lúc nào đó "nghịch với NAS / homelab của tôi" trở nên cũ kỹ). Đây là lý do tại sao việc tìm kiếm một cộng đồng là rất quan trọng. Bạn có thể từ bỏ một số an ninh hoạt động bằng cách tâm sự với một số người bạn rất thân, những người mà bạn biết bạn có thể tin tưởng sâu sắc. Ngay cả khi đó, hãy cẩn thận không đặt bất cứ điều gì vào văn bản, trong trường hợp họ phải giao nộp email của họ cho các cơ quan chức năng, hoặc nếu thiết bị của họ bị xâm phạm theo cách nào đó. Tốt hơn nữa là tìm một số đồng nghiệp cướp biển. Nếu bạn bè thân của bạn quan tâm đến việc tham gia cùng bạn, tuyệt vời! Nếu không, bạn có thể tìm thấy những người khác trực tuyến. Đáng buồn thay, đây vẫn là một cộng đồng ngách. Cho đến nay, chúng tôi chỉ tìm thấy một số ít người khác đang hoạt động trong lĩnh vực này. Những nơi bắt đầu tốt dường như là các diễn đàn Library Genesis và r/DataHoarder. Đội Lưu trữ cũng có những cá nhân cùng chí hướng, mặc dù họ hoạt động trong khuôn khổ pháp luật (ngay cả khi trong một số khu vực xám của pháp luật). Các cảnh "warez" và cướp biển truyền thống cũng có những người suy nghĩ theo cách tương tự. Chúng tôi luôn sẵn sàng đón nhận ý tưởng về cách thúc đẩy cộng đồng và khám phá ý tưởng. Hãy thoải mái nhắn tin cho chúng tôi trên Twitter hoặc Reddit. Có lẽ chúng tôi có thể tổ chức một diễn đàn hoặc nhóm trò chuyện nào đó. Một thách thức là điều này có thể dễ dàng bị kiểm duyệt khi sử dụng các nền tảng phổ biến, vì vậy chúng tôi sẽ phải tự mình tổ chức. Cũng có sự đánh đổi giữa việc có những cuộc thảo luận này hoàn toàn công khai (có nhiều khả năng tham gia hơn) so với việc làm cho nó riêng tư (không để các "mục tiêu" tiềm năng biết rằng chúng tôi sắp thu thập dữ liệu của họ). Chúng tôi sẽ phải suy nghĩ về điều đó. Hãy cho chúng tôi biết nếu bạn quan tâm đến điều này! Kết luận Hy vọng điều này hữu ích cho những nhà lưu trữ cướp biển mới bắt đầu. Chúng tôi rất vui mừng chào đón bạn đến với thế giới này, vì vậy đừng ngần ngại liên hệ. Hãy cùng nhau bảo tồn càng nhiều kiến thức và văn hóa của thế giới càng tốt, và sao chép nó rộng rãi. Dự án 4. Lựa chọn dữ liệu Thường thì bạn có thể sử dụng metadata để tìm ra một tập hợp con hợp lý của dữ liệu để tải xuống. Ngay cả khi bạn cuối cùng muốn tải xuống tất cả dữ liệu, việc ưu tiên các mục quan trọng nhất trước có thể hữu ích, trong trường hợp bạn bị phát hiện và các biện pháp phòng thủ được cải thiện, hoặc vì bạn cần mua thêm đĩa, hoặc đơn giản là vì có điều gì khác xảy ra trong cuộc sống của bạn trước khi bạn có thể tải xuống mọi thứ. Ví dụ, một bộ sưu tập có thể có nhiều phiên bản của cùng một tài nguyên cơ bản (như một cuốn sách hoặc một bộ phim), trong đó một phiên bản được đánh dấu là chất lượng tốt nhất. Lưu các phiên bản đó trước sẽ rất hợp lý. Bạn có thể cuối cùng muốn lưu tất cả các phiên bản, vì trong một số trường hợp metadata có thể được gắn thẻ không chính xác, hoặc có thể có những sự đánh đổi không rõ giữa các phiên bản (ví dụ, "phiên bản tốt nhất" có thể tốt nhất theo hầu hết các cách nhưng tệ hơn theo các cách khác, như một bộ phim có độ phân giải cao hơn nhưng thiếu phụ đề). Bạn cũng có thể tìm kiếm cơ sở dữ liệu metadata của mình để tìm những điều thú vị. Tệp lớn nhất được lưu trữ là gì, và tại sao nó lại lớn như vậy? Tệp nhỏ nhất là gì? Có những mẫu thú vị hoặc bất ngờ nào khi nói đến các danh mục, ngôn ngữ nhất định, v.v.? Có tiêu đề trùng lặp hoặc rất giống nhau không? Có mẫu nào về thời điểm dữ liệu được thêm vào, như một ngày mà nhiều tệp được thêm vào cùng lúc không? Bạn thường có thể học được nhiều điều bằng cách nhìn vào tập dữ liệu theo những cách khác nhau. Trong trường hợp của chúng tôi, chúng tôi đã loại bỏ các cuốn sách của Thư viện Z dựa trên các hash md5 trong Library Genesis, do đó tiết kiệm được rất nhiều thời gian tải xuống và dung lượng đĩa. Đây là một tình huống khá độc đáo. Trong hầu hết các trường hợp, không có cơ sở dữ liệu toàn diện nào về các tệp đã được bảo tồn đúng cách bởi các đồng nghiệp. Điều này tự nó là một cơ hội lớn cho ai đó ngoài kia. Sẽ rất tuyệt vời nếu có một cái nhìn tổng quan được cập nhật thường xuyên về những thứ như âm nhạc và phim đã được gieo rộng rãi trên các trang web torrent, và do đó có mức độ ưu tiên thấp hơn để bao gồm trong các bản sao của hải tặc. 6. Phân phối Bạn đã có dữ liệu, do đó bạn đang sở hữu bản sao cướp biển đầu tiên trên thế giới của mục tiêu của mình (có khả năng nhất). Theo nhiều cách, phần khó nhất đã qua, nhưng phần rủi ro nhất vẫn còn ở phía trước. Dù sao, cho đến nay bạn đã hoạt động bí mật; bay dưới radar. Tất cả những gì bạn cần làm là sử dụng một VPN tốt suốt quá trình, không điền thông tin cá nhân của bạn vào bất kỳ biểu mẫu nào (dĩ nhiên), và có thể sử dụng một phiên trình duyệt đặc biệt (hoặc thậm chí một máy tính khác). Bây giờ bạn phải phân phối dữ liệu. Trong trường hợp của chúng tôi, chúng tôi đầu tiên muốn đóng góp sách trở lại Library Genesis, nhưng sau đó nhanh chóng phát hiện ra những khó khăn trong việc đó (phân loại tiểu thuyết so với phi tiểu thuyết). Vì vậy, chúng tôi quyết định phân phối bằng cách sử dụng các torrent theo kiểu Library Genesis. Nếu bạn có cơ hội đóng góp cho một dự án hiện có, thì điều đó có thể tiết kiệm cho bạn rất nhiều thời gian. Tuy nhiên, hiện tại không có nhiều bản sao cướp biển được tổ chức tốt. Vì vậy, giả sử bạn quyết định tự phân phối các torrent. Hãy cố gắng giữ cho các tệp đó nhỏ, để chúng dễ dàng được sao chép trên các trang web khác. Sau đó, bạn sẽ phải seed các torrent đó, trong khi vẫn giữ ẩn danh. Bạn có thể sử dụng VPN (có hoặc không có chuyển tiếp cổng), hoặc thanh toán bằng Bitcoins đã được trộn lẫn cho một Seedbox. Nếu bạn không biết một số thuật ngữ đó có nghĩa là gì, bạn sẽ có một loạt việc đọc cần làm, vì điều quan trọng là bạn phải hiểu các đánh đổi rủi ro ở đây. Bạn có thể lưu trữ các tệp torrent trên các trang web torrent hiện có. Trong trường hợp của chúng tôi, chúng tôi đã chọn thực sự lưu trữ một trang web, vì chúng tôi cũng muốn truyền bá triết lý của mình một cách rõ ràng. Bạn có thể tự làm điều này theo cách tương tự (chúng tôi sử dụng Njalla cho các tên miền và lưu trữ của mình, được thanh toán bằng Bitcoins đã được trộn lẫn), nhưng cũng đừng ngần ngại liên hệ với chúng tôi để chúng tôi lưu trữ các torrent của bạn. Chúng tôi đang tìm cách xây dựng một chỉ mục toàn diện về các bản sao cướp biển theo thời gian, nếu ý tưởng này được đón nhận. Về việc chọn VPN, đã có rất nhiều bài viết về vấn đề này, vì vậy chúng tôi chỉ nhắc lại lời khuyên chung là chọn theo uy tín. Chính sách không lưu nhật ký đã được kiểm tra tại tòa án với lịch sử lâu dài bảo vệ quyền riêng tư là lựa chọn ít rủi ro nhất, theo ý kiến của chúng tôi. Lưu ý rằng ngay cả khi bạn làm mọi thứ đúng, bạn không bao giờ có thể đạt đến mức rủi ro bằng không. Ví dụ, khi bạn đang seeding torrent của mình, một tác nhân quốc gia có động cơ cao có thể nhìn vào luồng dữ liệu đến và đi cho các máy chủ VPN, và suy ra bạn là ai. Hoặc bạn có thể chỉ đơn giản là mắc lỗi nào đó. Chúng tôi có lẽ đã mắc lỗi, và sẽ lại mắc lỗi. May mắn thay, các quốc gia không quan tâm <em>nhiều</em> đến vi phạm bản quyền. Một quyết định cần đưa ra cho mỗi dự án là liệu có nên xuất bản nó dưới cùng một danh tính như trước hay không. Nếu bạn tiếp tục sử dụng cùng một tên, thì những sai lầm trong bảo mật hoạt động từ các dự án trước có thể quay lại gây rắc rối cho bạn. Nhưng xuất bản dưới các tên khác nhau có nghĩa là bạn không xây dựng được danh tiếng lâu dài. Chúng tôi đã chọn có bảo mật hoạt động mạnh mẽ từ đầu để có thể tiếp tục sử dụng cùng một danh tính, nhưng chúng tôi sẽ không ngần ngại xuất bản dưới một tên khác nếu chúng tôi mắc lỗi hoặc nếu hoàn cảnh yêu cầu. Truyền tải thông tin có thể khó khăn. Như chúng tôi đã nói, đây vẫn là một cộng đồng ngách. Ban đầu chúng tôi đã đăng trên Reddit, nhưng thực sự đã thu hút sự chú ý trên Hacker News. Hiện tại, khuyến nghị của chúng tôi là đăng nó ở một vài nơi và xem điều gì xảy ra. Và một lần nữa, hãy liên hệ với chúng tôi. Chúng tôi rất muốn lan truyền thông điệp về những nỗ lực lưu trữ cướp biển. 1. Lựa chọn miền / triết lý Không thiếu kiến thức và di sản văn hóa cần được lưu giữ, điều này có thể gây choáng ngợp. Đó là lý do tại sao thường hữu ích khi dành một chút thời gian để suy nghĩ về những gì bạn có thể đóng góp. Mỗi người có một cách suy nghĩ khác nhau về điều này, nhưng đây là một số câu hỏi mà bạn có thể tự hỏi mình: Trong trường hợp của chúng tôi, chúng tôi đặc biệt quan tâm đến việc bảo tồn lâu dài khoa học. Chúng tôi biết về Library Genesis, và cách nó được sao chép hoàn toàn nhiều lần bằng cách sử dụng torrent. Chúng tôi yêu thích ý tưởng đó. Rồi một ngày, một trong số chúng tôi cố gắng tìm một số sách giáo khoa khoa học trên Library Genesis, nhưng không thể tìm thấy chúng, khiến chúng tôi nghi ngờ về mức độ hoàn chỉnh của nó. Sau đó, chúng tôi tìm kiếm những sách giáo khoa đó trực tuyến và tìm thấy chúng ở những nơi khác, điều này đã gieo mầm cho dự án của chúng tôi. Ngay cả trước khi chúng tôi biết về Thư viện Z, chúng tôi đã có ý tưởng không cố gắng thu thập tất cả những cuốn sách đó một cách thủ công, mà tập trung vào việc sao chép các bộ sưu tập hiện có và đóng góp chúng trở lại Library Genesis. Bạn có những kỹ năng nào mà bạn có thể sử dụng để mang lại lợi ích cho mình? Ví dụ, nếu bạn là chuyên gia bảo mật trực tuyến, bạn có thể tìm cách vượt qua các chặn IP cho các mục tiêu an toàn. Nếu bạn giỏi tổ chức cộng đồng, thì có lẽ bạn có thể tập hợp một số người xung quanh một mục tiêu. Tuy nhiên, biết một chút lập trình cũng hữu ích, ít nhất là để duy trì bảo mật hoạt động tốt trong suốt quá trình này. Khu vực nào có đòn bẩy cao để tập trung vào? Nếu bạn sẽ dành X giờ cho việc lưu trữ lậu, thì làm thế nào bạn có thể đạt được "hiệu quả cao nhất"? Bạn có những cách suy nghĩ độc đáo nào về điều này? Bạn có thể có một số ý tưởng hoặc cách tiếp cận thú vị mà người khác có thể đã bỏ lỡ. Bạn có bao nhiêu thời gian cho việc này? Lời khuyên của chúng tôi là bắt đầu từ những dự án nhỏ và thực hiện các dự án lớn hơn khi bạn đã quen với nó, nhưng nó có thể chiếm hết thời gian của bạn. Tại sao bạn quan tâm đến điều này? Bạn đam mê điều gì? Nếu chúng ta có thể tập hợp một nhóm người mà tất cả đều lưu trữ những thứ mà họ đặc biệt quan tâm, điều đó sẽ bao phủ rất nhiều! Bạn sẽ biết nhiều hơn người bình thường về niềm đam mê của mình, như dữ liệu quan trọng nào cần lưu, bộ sưu tập và cộng đồng trực tuyến nào là tốt nhất, v.v. 3. Trích xuất metadata Ngày thêm/sửa đổi: để bạn có thể quay lại sau và tải xuống các tệp mà bạn chưa tải xuống trước đó (mặc dù bạn cũng có thể sử dụng ID hoặc hash cho việc này). Hash (md5, sha1): để xác nhận rằng bạn đã tải xuống tệp đúng cách. ID: có thể là một ID nội bộ, nhưng các ID như ISBN hoặc DOI cũng hữu ích. Tên tệp / vị trí Mô tả, danh mục, thẻ, tác giả, ngôn ngữ, v.v. Kích thước: để tính toán dung lượng đĩa bạn cần. Hãy đi sâu hơn một chút về mặt kỹ thuật ở đây. Để thực sự trích xuất metadata từ các trang web, chúng tôi đã giữ mọi thứ khá đơn giản. Chúng tôi sử dụng các script Python, đôi khi là curl, và một cơ sở dữ liệu MySQL để lưu trữ kết quả. Chúng tôi không sử dụng bất kỳ phần mềm trích xuất phức tạp nào có thể lập bản đồ các trang web phức tạp, vì cho đến nay chúng tôi chỉ cần trích xuất một hoặc hai loại trang bằng cách chỉ định qua các id và phân tích cú pháp HTML. Nếu không có các trang dễ dàng chỉ định, thì bạn có thể cần một trình thu thập thông tin thực sự để tìm tất cả các trang. Trước khi bạn bắt đầu trích xuất toàn bộ một trang web, hãy thử làm điều đó thủ công một chút. Tự mình đi qua vài chục trang, để có cảm giác về cách hoạt động của nó. Đôi khi bạn sẽ gặp phải các chặn IP hoặc hành vi thú vị khác theo cách này. Điều tương tự cũng áp dụng cho việc trích xuất dữ liệu: trước khi đi quá sâu vào mục tiêu này, hãy chắc chắn rằng bạn có thể thực sự tải xuống dữ liệu của nó một cách hiệu quả. Để vượt qua các hạn chế, có một vài điều bạn có thể thử. Có địa chỉ IP hoặc máy chủ nào khác lưu trữ cùng dữ liệu nhưng không có cùng hạn chế không? Có điểm cuối API nào không có hạn chế, trong khi những điểm khác có không? Tốc độ tải xuống nào khiến IP của bạn bị chặn, và trong bao lâu? Hoặc bạn không bị chặn mà bị giảm tốc độ? Nếu bạn tạo một tài khoản người dùng, mọi thứ thay đổi như thế nào? Bạn có thể sử dụng HTTP/2 để giữ kết nối mở, và điều đó có tăng tốc độ yêu cầu trang không? Có trang nào liệt kê nhiều tệp cùng lúc, và thông tin được liệt kê ở đó có đủ không? Những thứ bạn có thể muốn lưu bao gồm: Chúng tôi thường thực hiện điều này trong hai giai đoạn. Đầu tiên, chúng tôi tải xuống các tệp HTML thô, thường là trực tiếp vào MySQL (để tránh nhiều tệp nhỏ, mà chúng tôi sẽ nói thêm bên dưới). Sau đó, trong một bước riêng biệt, chúng tôi đi qua các tệp HTML đó và phân tích chúng thành các bảng MySQL thực tế. Bằng cách này, bạn không phải tải xuống lại mọi thứ từ đầu nếu bạn phát hiện ra lỗi trong mã phân tích của mình, vì bạn chỉ cần xử lý lại các tệp HTML với mã mới. Nó cũng thường dễ dàng hơn để song song hóa bước xử lý, do đó tiết kiệm một số thời gian (và bạn có thể viết mã xử lý trong khi trích xuất đang chạy, thay vì phải viết cả hai bước cùng một lúc). Cuối cùng, lưu ý rằng đối với một số mục tiêu, trích xuất metadata là tất cả những gì có. Có một số bộ sưu tập metadata khổng lồ ngoài kia không được bảo tồn đúng cách. Tiêu đề Lựa chọn miền / triết lý: Bạn muốn tập trung vào đâu và tại sao? Đam mê, kỹ năng và hoàn cảnh độc đáo của bạn là gì mà bạn có thể sử dụng để mang lại lợi ích cho mình? Lựa chọn mục tiêu: Bạn sẽ sao chép bộ sưu tập cụ thể nào? Thu thập metadata: Lập danh mục thông tin về các tệp, mà không thực sự tải xuống các tệp (thường lớn hơn nhiều) đó. Lựa chọn dữ liệu: Dựa trên metadata, thu hẹp dữ liệu nào là quan trọng nhất để lưu trữ ngay bây giờ. Có thể là tất cả, nhưng thường có một cách hợp lý để tiết kiệm không gian và băng thông. Thu thập dữ liệu: Thực sự lấy dữ liệu. Phân phối: Đóng gói nó trong các torrent, thông báo ở đâu đó, khiến mọi người lan truyền nó. 5. Trích xuất dữ liệu Bây giờ bạn đã sẵn sàng để thực sự tải xuống dữ liệu hàng loạt. Như đã đề cập trước đó, tại thời điểm này bạn nên đã tải xuống thủ công một loạt tệp, để hiểu rõ hơn về hành vi và hạn chế của mục tiêu. Tuy nhiên, vẫn sẽ có những bất ngờ đang chờ đợi bạn khi bạn thực sự bắt đầu tải xuống nhiều tệp cùng một lúc. Lời khuyên của chúng tôi ở đây chủ yếu là giữ cho nó đơn giản. Bắt đầu bằng cách chỉ tải xuống một loạt tệp. Bạn có thể sử dụng Python, và sau đó mở rộng ra nhiều luồng. Nhưng đôi khi thậm chí đơn giản hơn là tạo các tệp Bash trực tiếp từ cơ sở dữ liệu, và sau đó chạy nhiều tệp trong nhiều cửa sổ terminal để mở rộng quy mô. Một mẹo kỹ thuật nhanh đáng nhắc đến ở đây là sử dụng OUTFILE trong MySQL, mà bạn có thể viết ở bất kỳ đâu nếu bạn vô hiệu hóa "secure_file_priv" trong mysqld.cnf (và hãy chắc chắn cũng vô hiệu hóa/ghi đè AppArmor nếu bạn đang sử dụng Linux). Chúng tôi lưu trữ dữ liệu trên các ổ cứng đơn giản. Bắt đầu với bất kỳ thứ gì bạn có, và mở rộng dần dần. Có thể cảm thấy choáng ngợp khi nghĩ về việc lưu trữ hàng trăm TB dữ liệu. Nếu đó là tình huống bạn đang đối mặt, chỉ cần đưa ra một tập hợp con tốt trước, và trong thông báo của bạn yêu cầu sự giúp đỡ trong việc lưu trữ phần còn lại. Nếu bạn muốn tự mình có thêm ổ cứng, thì r/DataHoarder có một số tài nguyên tốt để có được các giao dịch tốt. Cố gắng không lo lắng quá nhiều về các hệ thống tệp phức tạp. Rất dễ rơi vào hố thỏ của việc thiết lập những thứ như ZFS. Một chi tiết kỹ thuật cần lưu ý là nhiều hệ thống tệp không xử lý tốt với nhiều tệp. Chúng tôi đã tìm thấy một giải pháp đơn giản là tạo nhiều thư mục, ví dụ cho các dải ID khác nhau hoặc tiền tố hash. Sau khi tải dữ liệu, hãy chắc chắn kiểm tra tính toàn vẹn của các tệp bằng cách sử dụng các hàm băm trong metadata, nếu có sẵn. 2. Lựa chọn mục tiêu Dễ tiếp cận: không sử dụng nhiều lớp bảo vệ để ngăn bạn trích xuất metadata và dữ liệu của họ. Thông tin đặc biệt: bạn có một số thông tin đặc biệt về mục tiêu này, như bạn có quyền truy cập đặc biệt vào bộ sưu tập này, hoặc bạn đã tìm ra cách vượt qua các biện pháp bảo vệ của họ. Điều này không bắt buộc (dự án sắp tới của chúng tôi không làm gì đặc biệt), nhưng chắc chắn sẽ hữu ích! Lớn Vậy, chúng ta đã có khu vực mà chúng ta đang xem xét, bây giờ chúng ta sẽ sao chép bộ sưu tập cụ thể nào? Có một vài yếu tố làm cho một mục tiêu trở nên tốt: Khi chúng tôi tìm thấy sách giáo khoa khoa học của mình trên các trang web khác ngoài Library Genesis, chúng tôi đã cố gắng tìm hiểu cách chúng xuất hiện trên internet. Sau đó, chúng tôi tìm thấy Thư viện Z, và nhận ra rằng mặc dù hầu hết các cuốn sách không xuất hiện đầu tiên ở đó, nhưng cuối cùng chúng cũng có mặt ở đó. Chúng tôi đã tìm hiểu về mối quan hệ của nó với Library Genesis, và cấu trúc khuyến khích (tài chính) và giao diện người dùng vượt trội, cả hai đều làm cho nó trở thành một bộ sưu tập hoàn chỉnh hơn nhiều. Sau đó, chúng tôi đã thực hiện một số trích xuất metadata và dữ liệu sơ bộ, và nhận ra rằng chúng tôi có thể vượt qua giới hạn tải xuống IP của họ, tận dụng quyền truy cập đặc biệt của một trong các thành viên của chúng tôi vào nhiều máy chủ proxy. Khi bạn đang khám phá các mục tiêu khác nhau, điều quan trọng là phải che giấu dấu vết của bạn bằng cách sử dụng VPN và địa chỉ email tạm thời, điều mà chúng tôi sẽ nói thêm sau. Độc đáo: không đã được bao phủ tốt bởi các dự án khác. Khi chúng tôi thực hiện một dự án, nó có một vài giai đoạn: Đây không phải là các giai đoạn hoàn toàn độc lập, và thường những hiểu biết từ một giai đoạn sau sẽ đưa bạn quay lại một giai đoạn trước đó. Ví dụ, trong quá trình thu thập metadata, bạn có thể nhận ra rằng mục tiêu mà bạn đã chọn có các cơ chế phòng thủ vượt quá trình độ kỹ năng của bạn (như chặn IP), vì vậy bạn quay lại và tìm một mục tiêu khác. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>) Toàn bộ sách có thể được viết về lý do tại sao của việc bảo tồn kỹ thuật số nói chung, và lưu trữ cướp biển nói riêng, nhưng hãy để chúng tôi cung cấp một hướng dẫn nhanh cho những người không quá quen thuộc. Thế giới đang sản xuất nhiều tri thức và văn hóa hơn bao giờ hết, nhưng cũng có nhiều thứ bị mất hơn bao giờ hết. Nhân loại phần lớn giao phó di sản này cho các tập đoàn như nhà xuất bản học thuật, dịch vụ phát trực tuyến và công ty truyền thông xã hội, và họ thường không chứng tỏ là những người quản lý tốt. Hãy xem bộ phim tài liệu Digital Amnesia, hoặc thực sự bất kỳ bài nói nào của Jason Scott. Có một số tổ chức làm tốt việc lưu trữ càng nhiều càng tốt, nhưng họ bị ràng buộc bởi luật pháp. Là những cướp biển, chúng tôi ở vị trí độc nhất để lưu trữ các bộ sưu tập mà họ không thể chạm tới, vì thực thi bản quyền hoặc các hạn chế khác. Chúng tôi cũng có thể sao chép các bộ sưu tập nhiều lần, trên khắp thế giới, do đó tăng cơ hội bảo tồn đúng cách. Hiện tại, chúng tôi sẽ không tham gia vào các cuộc thảo luận về ưu và nhược điểm của sở hữu trí tuệ, đạo đức của việc vi phạm pháp luật, suy nghĩ về kiểm duyệt, hoặc vấn đề truy cập tri thức và văn hóa. Với tất cả những điều đó đã được giải quyết, hãy đi sâu vào cách thức. Chúng tôi sẽ chia sẻ cách nhóm của chúng tôi trở thành những nhà lưu trữ cướp biển, và những bài học mà chúng tôi đã học được trên đường đi. Có nhiều thách thức khi bạn bắt đầu hành trình này, và hy vọng chúng tôi có thể giúp bạn vượt qua một số trong số đó. Cách trở thành một nhà lưu trữ cướp biển Thách thức đầu tiên có thể là một điều bất ngờ. Nó không phải là một vấn đề kỹ thuật, hay một vấn đề pháp lý. Đó là một vấn đề tâm lý. Trước khi chúng tôi đi sâu vào, hai cập nhật về Pirate Library Mirror (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>): Chúng tôi đã nhận được một số khoản quyên góp vô cùng hào phóng. Đầu tiên là 10.000 đô la từ một cá nhân ẩn danh, người cũng đã hỗ trợ "bookwarrior", người sáng lập ban đầu của Library Genesis. Đặc biệt cảm ơn bookwarrior đã tạo điều kiện cho khoản quyên góp này. Khoản thứ hai là 10.000 đô la khác từ một nhà tài trợ ẩn danh, người đã liên lạc sau khi chúng tôi phát hành lần cuối và được truyền cảm hứng để giúp đỡ. Chúng tôi cũng đã nhận được một số khoản quyên góp nhỏ hơn. Cảm ơn rất nhiều vì tất cả sự hỗ trợ hào phóng của các bạn. Chúng tôi có một số dự án mới thú vị đang trong quá trình thực hiện mà điều này sẽ hỗ trợ, vì vậy hãy theo dõi. Chúng tôi đã gặp một số khó khăn kỹ thuật với kích thước của lần phát hành thứ hai, nhưng các torrent của chúng tôi đã hoạt động và đang seeding. Chúng tôi cũng nhận được một đề nghị hào phóng từ một cá nhân ẩn danh để seeding bộ sưu tập của chúng tôi trên các máy chủ tốc độ rất cao của họ, vì vậy chúng tôi đang thực hiện một tải lên đặc biệt cho máy của họ, sau đó mọi người khác đang tải xuống bộ sưu tập sẽ thấy sự cải thiện lớn về tốc độ. Bài viết blog Xin chào, tôi là Anna. Tôi đã tạo ra <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, thư viện bóng tối lớn nhất thế giới. Đây là blog cá nhân của tôi, nơi tôi và các đồng đội viết về vi phạm bản quyền, bảo tồn kỹ thuật số và nhiều hơn nữa. Kết nối với tôi trên <a %(reddit)s>Reddit</a>. Lưu ý rằng trang web này chỉ là một blog. Chúng tôi chỉ lưu trữ lời nói của chính mình ở đây. Không có torrent hoặc các tệp có bản quyền nào được lưu trữ hoặc liên kết ở đây. <strong>Thư viện</strong> - Giống như hầu hết các thư viện, chúng tôi tập trung chủ yếu vào tài liệu viết như sách. Chúng tôi có thể mở rộng sang các loại phương tiện khác trong tương lai. <strong>Bản sao</strong> - Chúng tôi chỉ là một bản sao của các thư viện hiện có. Chúng tôi tập trung vào việc bảo tồn, không phải làm cho sách dễ dàng tìm kiếm và tải xuống (truy cập) hoặc phát triển một cộng đồng lớn những người đóng góp sách mới (nguồn cung cấp). <strong>Cướp biển</strong> - Chúng tôi cố ý vi phạm luật bản quyền ở hầu hết các quốc gia. Điều này cho phép chúng tôi làm điều mà các thực thể hợp pháp không thể làm: đảm bảo sách được sao chép rộng rãi. <em>Chúng tôi không liên kết đến các tệp từ blog này. Vui lòng tự tìm kiếm.</em> - Anna và đội ngũ (<a %(reddit)s>Reddit</a>) Dự án này (CHỈNH SỬA: chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>) nhằm đóng góp vào việc bảo tồn và giải phóng kiến thức của con người. Chúng tôi thực hiện đóng góp nhỏ bé và khiêm tốn của mình, theo bước chân của những người vĩ đại trước chúng tôi. Trọng tâm của dự án này được minh họa qua tên gọi của nó: Thư viện đầu tiên mà chúng tôi đã sao chép là Thư viện Z. Đây là một thư viện phổ biến (và bất hợp pháp). Họ đã lấy bộ sưu tập Library Genesis và làm cho nó dễ dàng tìm kiếm. Ngoài ra, họ đã trở nên rất hiệu quả trong việc kêu gọi đóng góp sách mới, bằng cách khuyến khích người dùng đóng góp với nhiều đặc quyền khác nhau. Hiện tại, họ không đóng góp những cuốn sách mới này trở lại Library Genesis. Và không giống như Library Genesis, họ không làm cho bộ sưu tập của mình dễ dàng sao chép, điều này ngăn cản việc bảo tồn rộng rãi. Điều này quan trọng đối với mô hình kinh doanh của họ, vì họ tính phí để truy cập bộ sưu tập của mình hàng loạt (hơn 10 cuốn sách mỗi ngày). Chúng tôi không đưa ra phán xét đạo đức về việc thu phí truy cập số lượng lớn vào một bộ sưu tập sách bất hợp pháp. Không còn nghi ngờ gì nữa, Thư viện Z đã thành công trong việc mở rộng quyền truy cập vào tri thức và tìm nguồn sách nhiều hơn. Chúng tôi chỉ đơn giản ở đây để làm phần việc của mình: đảm bảo việc bảo tồn lâu dài bộ sưu tập cá nhân này. Chúng tôi muốn mời bạn giúp bảo tồn và giải phóng kiến thức của con người bằng cách tải xuống và chia sẻ các torrent của chúng tôi. Xem trang dự án để biết thêm thông tin về cách dữ liệu được tổ chức. Chúng tôi cũng rất muốn mời bạn đóng góp ý tưởng của mình về những bộ sưu tập nào nên sao chép tiếp theo và cách thực hiện điều đó. Cùng nhau, chúng ta có thể đạt được nhiều điều. Đây chỉ là một đóng góp nhỏ trong số vô số đóng góp khác. Cảm ơn bạn, vì tất cả những gì bạn làm. Giới thiệu Bản sao Thư viện Cướp biển: Bảo tồn 7TB sách (không có trong Libgen) 10% of di sản viết của nhân loại được bảo tồn mãi mãi <strong>Google.</strong> Sau tất cả, họ đã thực hiện nghiên cứu này cho Google Books. Tuy nhiên, metadata của họ không thể truy cập được hàng loạt và khá khó để trích xuất. <strong>Các hệ thống thư viện và kho lưu trữ cá nhân khác nhau.</strong> Có những thư viện và kho lưu trữ chưa được lập chỉ mục và tổng hợp bởi bất kỳ ai trong số những người trên, thường là vì họ thiếu kinh phí, hoặc vì lý do khác không muốn chia sẻ dữ liệu của họ với Open Library, OCLC, Google, v.v. Nhiều trong số này có hồ sơ kỹ thuật số có thể truy cập qua internet, và chúng thường không được bảo vệ tốt, vì vậy nếu bạn muốn giúp đỡ và có một chút vui vẻ khi tìm hiểu về các hệ thống thư viện kỳ lạ, đây là những điểm khởi đầu tuyệt vời. <strong>ISBNdb.</strong> Đây là chủ đề của bài viết blog này. ISBNdb trích xuất dữ liệu từ các trang web khác nhau để lấy metadata sách, đặc biệt là dữ liệu giá cả, mà họ sau đó bán cho các nhà bán sách, để họ có thể định giá sách của mình phù hợp với phần còn lại của thị trường. Vì ISBN hiện nay khá phổ biến, họ đã xây dựng hiệu quả một “trang web cho mỗi cuốn sách”. <strong>Open Library.</strong> Như đã đề cập trước đó, đây là toàn bộ sứ mệnh của họ. Họ đã thu thập lượng lớn dữ liệu thư viện từ các thư viện hợp tác và các kho lưu trữ quốc gia, và tiếp tục làm như vậy. Họ cũng có các thủ thư tình nguyện và một đội ngũ kỹ thuật đang cố gắng loại bỏ trùng lặp các bản ghi và gắn thẻ chúng với tất cả các loại metadata. Tốt nhất là, dataset của họ hoàn toàn mở. Bạn có thể đơn giản <a %(openlibrary)s>tải xuống</a>. <strong>WorldCat.</strong> Đây là một trang web do tổ chức phi lợi nhuận OCLC điều hành, bán các hệ thống quản lý thư viện. Họ tổng hợp metadata sách từ nhiều thư viện và cung cấp nó thông qua trang web WorldCat. Tuy nhiên, họ cũng kiếm tiền bằng cách bán dữ liệu này, vì vậy nó không có sẵn để tải xuống hàng loạt. Họ có một số Datasets hàng loạt hạn chế hơn có sẵn để tải xuống, hợp tác với các thư viện cụ thể. 1. Với một định nghĩa hợp lý nào đó về "mãi mãi". ;) 2. Tất nhiên, di sản viết của nhân loại còn nhiều hơn sách, đặc biệt là ngày nay. Vì lợi ích của bài viết này và các bản phát hành gần đây của chúng tôi, chúng tôi tập trung vào sách, nhưng sự quan tâm của chúng tôi còn mở rộng hơn. 3. Có rất nhiều điều có thể nói về Aaron Swartz, nhưng chúng tôi chỉ muốn đề cập đến anh ấy một cách ngắn gọn, vì anh ấy đóng một vai trò then chốt trong câu chuyện này. Khi thời gian trôi qua, nhiều người có thể lần đầu tiên biết đến tên anh ấy, và sau đó có thể tự mình khám phá sâu hơn. <strong>Bản sao vật lý.</strong> Rõ ràng điều này không hữu ích lắm, vì chúng chỉ là bản sao của cùng một tài liệu. Sẽ thật tuyệt nếu chúng ta có thể bảo tồn tất cả các chú thích mà mọi người thực hiện trong sách, như những "chữ viết tay bên lề" nổi tiếng của Fermat. Nhưng tiếc thay, đó sẽ vẫn là giấc mơ của một nhà lưu trữ. <strong>“Ấn bản”.</strong> Ở đây bạn đếm mỗi phiên bản độc đáo của một cuốn sách. Nếu có bất kỳ điều gì khác biệt, như bìa khác hoặc lời nói đầu khác, nó được tính là một ấn bản khác. <strong>Tệp.</strong> Khi làm việc với các thư viện bóng tối như Library Genesis, Sci-Hub, hoặc Thư viện Z, có một cân nhắc bổ sung. Có thể có nhiều bản quét của cùng một ấn bản. Và mọi người có thể tạo ra các phiên bản tốt hơn của các tệp hiện có, bằng cách quét văn bản bằng OCR, hoặc chỉnh sửa các trang đã được quét ở một góc. Chúng tôi chỉ muốn đếm các tệp này là một ấn bản, điều này sẽ yêu cầu metadata tốt, hoặc loại bỏ trùng lặp bằng cách đo lường sự tương đồng của tài liệu. <strong>“Tác phẩm”.</strong> Ví dụ, “Harry Potter và Phòng chứa Bí mật” như một khái niệm logic, bao gồm tất cả các phiên bản của nó, như các bản dịch và tái bản khác nhau. Đây là một định nghĩa khá hữu ích, nhưng có thể khó để vạch ra ranh giới của những gì được tính. Ví dụ, chúng ta có thể muốn bảo tồn các bản dịch khác nhau, mặc dù các bản tái bản chỉ có những khác biệt nhỏ có thể không quan trọng lắm. - Anna và đội ngũ (<a %(reddit)s>Reddit</a>) Với Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>), mục tiêu của chúng tôi là lấy tất cả các cuốn sách trên thế giới và bảo tồn chúng mãi mãi.<sup>1</sup> Giữa các torrent của Thư viện Z và các torrent gốc của Library Genesis, chúng tôi có 11.783.153 tệp. Nhưng thực sự thì con số đó là bao nhiêu? Nếu chúng ta loại bỏ trùng lặp các tệp đó một cách đúng đắn, chúng ta đã bảo tồn được bao nhiêu phần trăm tổng số sách trên thế giới? Chúng tôi thực sự muốn có một cái gì đó như thế này: Hãy bắt đầu với một số con số sơ bộ: Trong cả Thư viện Z/Libgen và Open Library, có nhiều sách hơn số ISBN duy nhất. Điều đó có nghĩa là nhiều cuốn sách không có ISBN, hay chỉ đơn giản là thiếu metadata ISBN? Chúng tôi có thể trả lời câu hỏi này bằng cách kết hợp việc khớp tự động dựa trên các thuộc tính khác (tiêu đề, tác giả, nhà xuất bản, v.v.), thu thập thêm các nguồn dữ liệu và trích xuất ISBN từ chính các bản quét sách (trong trường hợp của Thư viện Z/Libgen). Có bao nhiêu trong số những ISBN đó là duy nhất? Điều này được minh họa tốt nhất bằng một biểu đồ Venn: Để chính xác hơn: Chúng tôi đã ngạc nhiên bởi sự trùng lặp ít ỏi! ISBNdb có một lượng lớn ISBN không xuất hiện trong cả Thư viện Z hay Open Library, và điều tương tự cũng xảy ra (ở mức độ nhỏ hơn nhưng vẫn đáng kể) với hai cái còn lại. Điều này đặt ra nhiều câu hỏi mới. Việc ghép tự động sẽ giúp ích bao nhiêu trong việc gắn thẻ các cuốn sách chưa được gắn thẻ ISBN? Liệu có nhiều sự trùng khớp và do đó tăng sự trùng lặp? Ngoài ra, điều gì sẽ xảy ra nếu chúng ta đưa vào datasets thứ 4 hoặc thứ 5? Chúng ta sẽ thấy bao nhiêu sự trùng lặp khi đó? Điều này cho chúng ta một điểm khởi đầu. Chúng ta có thể xem tất cả các ISBN không có trong dataset Thư viện Z, và không khớp với các trường tiêu đề/tác giả. Điều đó có thể giúp chúng ta bảo tồn tất cả các cuốn sách trên thế giới: đầu tiên bằng cách thu thập các bản quét từ internet, sau đó là ra ngoài thực tế để quét sách. Việc sau thậm chí có thể được tài trợ cộng đồng, hoặc được thúc đẩy bởi “tiền thưởng” từ những người muốn thấy các cuốn sách cụ thể được số hóa. Tất cả đó là một câu chuyện cho một thời điểm khác. Nếu bạn muốn giúp đỡ với bất kỳ điều gì trong số này — phân tích thêm; thu thập thêm metadata; tìm thêm sách; OCR sách; thực hiện điều này cho các lĩnh vực khác (ví dụ như bài báo, sách nói, phim, chương trình truyền hình, tạp chí) hoặc thậm chí làm cho một số dữ liệu này có sẵn cho các mục đích như đào tạo ML / mô hình ngôn ngữ lớn — vui lòng liên hệ với tôi (<a %(reddit)s>Reddit</a>). Nếu bạn đặc biệt quan tâm đến phân tích dữ liệu, chúng tôi đang làm việc để làm cho datasets và script của chúng tôi có sẵn ở định dạng dễ sử dụng hơn. Sẽ rất tuyệt nếu bạn có thể chỉ cần fork một notebook và bắt đầu khám phá điều này. Cuối cùng, nếu bạn muốn hỗ trợ công việc này, xin hãy cân nhắc việc quyên góp. Đây là một hoạt động hoàn toàn do tình nguyện viên điều hành, và sự đóng góp của bạn tạo ra sự khác biệt lớn. Mỗi chút đều có ích. Hiện tại chúng tôi nhận quyên góp bằng tiền điện tử; xem trang Quyên góp trên Lưu trữ của Anna. Để có một tỷ lệ phần trăm, chúng ta cần một mẫu số: tổng số sách từng được xuất bản.<sup>2</sup> Trước khi Google Books ngừng hoạt động, một kỹ sư trong dự án, Leonid Taycher, <a %(booksearch_blogspot)s>đã cố gắng ước tính</a> con số này. Ông đã đưa ra — một cách hài hước — con số 129.864.880 (“ít nhất cho đến Chủ nhật”). Ông ước tính con số này bằng cách xây dựng một cơ sở dữ liệu thống nhất của tất cả các cuốn sách trên thế giới. Để làm điều này, ông đã thu thập các Datasets khác nhau và sau đó hợp nhất chúng theo nhiều cách khác nhau. Như một lưu ý nhanh, có một người khác đã cố gắng lập danh mục tất cả các cuốn sách trên thế giới: Aaron Swartz, nhà hoạt động kỹ thuật số quá cố và đồng sáng lập Reddit.<sup>3</sup> Ông đã <a %(youtube)s>bắt đầu Open Library</a> với mục tiêu “một trang web cho mỗi cuốn sách từng được xuất bản”, kết hợp dữ liệu từ nhiều nguồn khác nhau. Ông đã phải trả giá đắt nhất cho công việc bảo tồn kỹ thuật số của mình khi bị truy tố vì tải xuống hàng loạt các bài báo học thuật, dẫn đến việc tự tử của ông. Không cần phải nói, đây là một trong những lý do nhóm của chúng tôi sử dụng bút danh, và tại sao chúng tôi rất cẩn thận. Open Library vẫn đang được điều hành một cách anh hùng bởi những người tại Internet Archive, tiếp tục di sản của Aaron. Chúng tôi sẽ quay lại vấn đề này sau trong bài viết này. Trong bài viết trên blog của Google, Taycher mô tả một số thách thức với việc ước tính con số này. Trước tiên, một cuốn sách được định nghĩa như thế nào? Có một vài định nghĩa có thể có: “Ấn bản” dường như là định nghĩa thực tế nhất về “sách” là gì. Thuận tiện, định nghĩa này cũng được sử dụng để gán số ISBN duy nhất. ISBN, hay Số Sách Tiêu Chuẩn Quốc Tế, thường được sử dụng cho thương mại quốc tế, vì nó được tích hợp với hệ thống mã vạch quốc tế (”Số Bài Viết Quốc Tế”). Nếu bạn muốn bán một cuốn sách trong các cửa hàng, nó cần có mã vạch, vì vậy bạn nhận được một ISBN. Bài viết trên blog của Taycher đề cập rằng mặc dù ISBN hữu ích, chúng không phải là phổ quát, vì chúng chỉ thực sự được chấp nhận vào giữa những năm bảy mươi, và không phải ở khắp nơi trên thế giới. Tuy nhiên, ISBN có lẽ là định danh được sử dụng rộng rãi nhất cho các ấn bản sách, vì vậy đó là điểm khởi đầu tốt nhất của chúng tôi. Nếu chúng tôi có thể tìm thấy tất cả các ISBN trên thế giới, chúng tôi sẽ có một danh sách hữu ích về những cuốn sách nào vẫn cần được bảo tồn. Vậy, chúng ta lấy dữ liệu từ đâu? Có một số nỗ lực hiện có đang cố gắng biên soạn danh sách tất cả các cuốn sách trên thế giới: Trong bài viết này, chúng tôi vui mừng thông báo một bản phát hành nhỏ (so với các bản phát hành Thư viện Z trước đây của chúng tôi). Chúng tôi đã trích xuất hầu hết ISBNdb và làm cho dữ liệu có sẵn để tải torrent trên trang web của Bản sao Thư viện Cướp biển (CHỈNH SỬA: đã chuyển đến <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>; chúng tôi sẽ không liên kết trực tiếp ở đây, chỉ cần tìm kiếm nó). Đây là khoảng 30,9 triệu bản ghi (20GB dưới dạng <a %(jsonlines)s>JSON Lines</a>; 4,4GB nén gzip). Trên trang web của họ, họ tuyên bố rằng họ thực sự có 32,6 triệu bản ghi, vì vậy chúng tôi có thể đã bỏ lỡ một số, hoặc <em>họ</em> có thể đã làm sai điều gì đó. Dù sao đi nữa, hiện tại chúng tôi sẽ không chia sẻ chính xác cách chúng tôi đã làm điều đó — chúng tôi sẽ để lại điều đó như một bài tập cho người đọc. ;-) Những gì chúng tôi sẽ chia sẻ là một số phân tích sơ bộ, để cố gắng tiến gần hơn đến việc ước tính số lượng sách trên thế giới. Chúng tôi đã xem xét ba datasets: dataset ISBNdb mới này, bản phát hành metadata gốc của chúng tôi mà chúng tôi đã thu thập từ thư viện bóng tối Thư viện Z (bao gồm cả Library Genesis), và dữ liệu dump của Open Library. Dữ liệu ISBNdb, hoặc Có Bao Nhiêu Cuốn Sách Được Bảo Tồn Mãi Mãi? Nếu chúng ta thực hiện việc loại bỏ trùng lặp các tệp từ các thư viện bóng tối một cách đúng đắn, chúng ta đã bảo tồn được bao nhiêu phần trăm tổng số sách trên thế giới? Cập nhật về <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, thư viện mở thực sự lớn nhất trong lịch sử loài người. <em>Thiết kế lại WorldCat</em> Dữ liệu <strong>Định dạng?</strong> <a %(blog)s>Các Container Lưu trữ của Anna (AAC)</a>, về cơ bản là <a %(jsonlines)s>JSON Lines</a> nén với <a %(zstd)s>Zstandard</a>, cùng với một số ngữ nghĩa tiêu chuẩn hóa. Các container này bao bọc các loại bản ghi khác nhau, dựa trên các lần thu thập dữ liệu khác nhau mà chúng tôi đã triển khai. Một năm trước, chúng tôi <a %(blog)s>bắt đầu</a> trả lời câu hỏi này: <strong>Phần trăm sách nào đã được bảo tồn vĩnh viễn bởi các thư viện bóng tối?</strong> Hãy xem một số thông tin cơ bản về dữ liệu: Khi một cuốn sách được đưa vào một thư viện bóng tối dữ liệu mở như <a %(wikipedia_library_genesis)s>Library Genesis</a>, và bây giờ là <a %(wikipedia_annas_archive)s>Lưu trữ của Anna</a>, nó sẽ được sao chép khắp thế giới (thông qua torrents), do đó thực tế bảo tồn nó mãi mãi. Để trả lời câu hỏi về phần trăm sách đã được bảo tồn, chúng ta cần biết mẫu số: có bao nhiêu cuốn sách tồn tại tổng cộng? Và lý tưởng nhất là chúng ta không chỉ có một con số, mà còn có metadata thực tế. Sau đó, chúng ta không chỉ có thể so khớp chúng với các thư viện bóng tối, mà còn <strong>tạo một danh sách TODO của các cuốn sách còn lại cần bảo tồn!</strong> Chúng ta thậm chí có thể bắt đầu mơ về một nỗ lực cộng đồng để đi xuống danh sách TODO này. Chúng tôi đã thu thập dữ liệu từ <a %(wikipedia_isbndb_com)s>ISBNdb</a>, và tải xuống <a %(openlibrary)s>dữ liệu Open Library</a>, nhưng kết quả không đạt yêu cầu. Vấn đề chính là không có nhiều sự trùng lặp của ISBNs. Xem biểu đồ Venn này từ <a %(blog)s>bài viết blog của chúng tôi</a>: Chúng tôi rất ngạc nhiên về việc có rất ít sự trùng lặp giữa ISBNdb và Open Library, cả hai đều bao gồm dữ liệu từ nhiều nguồn khác nhau, chẳng hạn như thu thập dữ liệu web và hồ sơ thư viện. Nếu cả hai đều làm tốt việc tìm kiếm hầu hết các ISBNs ngoài kia, các vòng tròn của chúng chắc chắn sẽ có sự trùng lặp đáng kể, hoặc một trong hai sẽ là tập con của cái kia. Điều này khiến chúng tôi tự hỏi, có bao nhiêu cuốn sách nằm <em>hoàn toàn ngoài các vòng tròn này</em>? Chúng tôi cần một cơ sở dữ liệu lớn hơn. Đó là khi chúng tôi đặt mục tiêu vào cơ sở dữ liệu sách lớn nhất thế giới: <a %(wikipedia_worldcat)s>WorldCat</a>. Đây là một cơ sở dữ liệu độc quyền của tổ chức phi lợi nhuận <a %(wikipedia_oclc)s>OCLC</a>, nơi tổng hợp các bản ghi metadata từ các thư viện trên khắp thế giới, để đổi lấy việc cung cấp cho các thư viện đó quyền truy cập vào toàn bộ tập dữ liệu, và cho phép chúng xuất hiện trong kết quả tìm kiếm của người dùng cuối. Mặc dù OCLC là một tổ chức phi lợi nhuận, mô hình kinh doanh của họ yêu cầu bảo vệ cơ sở dữ liệu của mình. Chà, chúng tôi rất tiếc phải nói, các bạn tại OCLC, chúng tôi sẽ chia sẻ tất cả. :-) Trong năm qua, chúng tôi đã cẩn thận thu thập tất cả các bản ghi của WorldCat. Ban đầu, chúng tôi gặp may mắn. WorldCat vừa triển khai thiết kế lại hoàn toàn trang web của họ (vào tháng 8 năm 2022). Điều này bao gồm một sự cải tổ đáng kể của các hệ thống backend của họ, giới thiệu nhiều lỗ hổng bảo mật. Chúng tôi ngay lập tức nắm bắt cơ hội, và có thể thu thập hàng trăm triệu (!) bản ghi chỉ trong vài ngày. Sau đó, các lỗ hổng bảo mật dần dần được sửa chữa từng cái một, cho đến khi cái cuối cùng mà chúng tôi tìm thấy được vá khoảng một tháng trước. Đến lúc đó, chúng tôi đã có hầu hết các bản ghi, và chỉ đang tìm kiếm các bản ghi chất lượng cao hơn một chút. Vì vậy, chúng tôi cảm thấy đã đến lúc phát hành! 1,3 tỷ WorldCat scrape <em><strong>Tóm tắt:</strong> Lưu trữ của Anna đã quét toàn bộ WorldCat (bộ sưu tập metadata thư viện lớn nhất thế giới) để tạo danh sách TODO các cuốn sách cần được bảo tồn.</em> WorldCat Cảnh báo: bài viết blog này đã bị ngừng sử dụng. Chúng tôi đã quyết định rằng IPFS chưa sẵn sàng cho thời điểm hiện tại. Chúng tôi vẫn sẽ liên kết đến các tệp trên IPFS từ Lưu trữ của Anna khi có thể, nhưng chúng tôi sẽ không tự lưu trữ nữa, cũng không khuyến khích người khác sao chép bằng IPFS. Vui lòng xem trang Torrents của chúng tôi nếu bạn muốn giúp bảo tồn bộ sưu tập của chúng tôi. Giúp gieo hạt Thư viện Z trên IPFS Tải xuống từ Máy chủ Đối tác SciDB Mượn bên ngoài Mượn bên ngoài (không in được) Tải xuống bên ngoài Khám phá dữ liệu số Chứa trong torrents Quay lại  (thêm +%(num)s ) chưa trả tiền đã trả tiền đã bị huỷ đã hết hạn đang đợi Anna xác nhận không hợp lệ Văn bản bên dưới tiếp tục bằng tiếng Anh. Đi Đặt lại Tiếp Cuối Nếu địa chỉ email của bạn không hoạt động trên diễn đàn Libgen, chúng tôi khuyên bạn nên sử dụng <a %(a_mail)s>Proton Mail</a> (miễn phí). Bạn cũng có thể <a %(a_manual)s>yêu cầu kích hoạt</a> tài khoản của mình thủ công. (có thể yêu cầu <a %(a_browser)s>xác minh trình duyệt</a> — lượt tải xuống không giới hạn!) Máy chủ Đối tác Nhanh #%(number)s (được đề xuất) (nhanh hơn một chút nhưng có danh sách chờ) (không cần xác minh trình duyệt) (không cần xác minh trình duyệt hoặc danh sách chờ) (không có danh sách chờ, nhưng có thể rất chậm) Máy chủ đối tác chậm #%(number)s Sách nói Truyện tranh Sách (Viễn tưởng) Sách (Phi hư cấu) Sách (Chưa biết) Bài báo Tạp chí Bản nhạc Khác Tài liệu tiêu chuẩn Không thể chuyển đổi tất cả các trang sang PDF Được đánh dấu là bị lỗi ở libgen.li Không thể thấy được ở Libgen.li Không hiển thị trong Libgen.rs Hư cấu Không hiển thị trong Libgen.rs Phi hư cấu Chạy exiftool thất bại trên tệp này Được đánh dấu là “tệp xấu” trong Z-Library Thiếu từ Z-Library Được đánh dấu là “spam” trong Z-Library Không thể mở được tệp (ví dụ: tệp bị hỏng, lỗi bản quyền) Khiếu nại về bản quyền Sự cố tải xuống (ví dụ: không thể kết nối, thông báo lỗi, rất chậm) Dữ liệu số không chính xác (ví dụ: tiêu đề, mô tả, ảnh bìa) Những thứ khác Chất lượng kém (ví dụ: vấn đề về định dạng, chất lượng quét kém, thiếu trang) Cần loại bỏ thư rác/tập tin (ví dụ: quảng cáo, nội dung lạm dụng) %(amount)s (%(amount_usd)s) %(amount)s tổng %(amount)s (%(amount_usd)s) tổng (theo đô la Mỹ) Mọt sách siêu đẳng Thủ thư số đỏ Ông Kẹ Dữ Liệu Lưu trữ viên tuyệt vời Thêm lượt tải về Cerlalc Dữ liệu số Séc DuXiu 读秀 Chỉ mục Sách điện tử EBSCOhost Google Sách Goodreads HathiTrust IA IA Cho mượn kỹ thuật số có kiểm soát ISBNdb ISBN GRP Libgen.li Loại trừ “scimag” Libgen.rs Phi hư cấu và Hư cấu Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Thư viện Quốc gia Nga Sci-Hub Qua Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Tải lên AA Z-Library Z-Library Tiếng Trung Tên, tác giả, DOI, ISBN, MD5,.… Tìm kiếm Tác giả Mô tả và nhận xét dữ liệu số Phiên bản Tên tệp gốc Nhà xuất bản (tìm kiếm mục cụ thể) Tiêu đề Năm xuất bản Chi tiết kỹ thuật Đồng tiền này có mức tối thiểu cao hơn bình thường. Vui lòng chọn thời gian khác hoặc đồng tiền khác. Yêu cầu không thể hoàn thành. Vui lòng thử lại sau vài phút, và nếu tình trạng này tiếp tục xảy ra, hãy liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình. Đã xảy ra lỗi không xác định. Vui lòng liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình. Lỗi trong quá trình xử lý thanh toán. Vui lòng chờ một chút và thử lại. Nếu vấn đề vẫn tiếp diễn trong hơn 24 giờ, vui lòng liên hệ với chúng tôi tại %(email)s kèm theo ảnh chụp màn hình. Chúng tôi đang tổ chức một chiến dịch gây quỹ để <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">hỗ trợ</a> lưu giữ bộ truyện tranh lớn nhất tren một thư viện chìm trên thế giới. Cảm ơn vì sự hỗ trợ của bạn! <a href="/donate">Quyên góp.</a> Nếu bạn không thể quyên góp, hãy cân nhắc ủng hộ chúng tôi bằng cách giới thiệu với bạn bè của bạn và theo dõi chúng tôi trên <a href="https://www.reddit.com/r /Annas_Archive">Reddit</a> hoặc <a href="https://t.me/annasarchiveorg">Telegram</a>. Đừng gửi email cho chúng tôi để <a %(a_request)s>yêu cầu sách</a><br>hoặc tải lên nhỏ (<10k) <a %(a_upload)s>tệp tin</a>. Kho lưu trữ của Anna DMCA / khiếu nại bản quyền Liên hệ Reddit Dự phòng SLUM (%(unaffiliated)s) không liên kết Lưu Trữ của Anna cần sự giúp đỡ của bạn! Nếu bạn quyên góp ngay bây giờ, bạn sẽ nhận được <strong>gấp đôi</strong> số lượt tải nhanh. Nhiều người cố gắng hạ gục chúng tôi, nhưng chúng tôi chống trả. Nếu bạn quyên góp vào tháng này, bạn sẽ nhận được <strong>gấp đôi</strong> số lượt tải xuống nhanh. Có hiệu lực đến hết tháng này. Tiết kiệm kiến thức nhân loại: Món quà tuyệt vời dịp nghỉ lễ! Các gói thành viên sẽ được kéo dài thêm tương ứng. Các máy chủ đối tác không khả dụng do đóng cửa dịch vụ lưu trữ. Chúng sẽ sớm hoạt động trở lại. Để tăng cường khả năng phục hồi của Lưu Trữ Anna, chúng tôi đang tìm kiếm các tình nguyện viên để chạy các bản sao. Chúng tôi có một phương thức quyên góp mới: %(method_name)s. Vui lòng cân nhắc %(donate_link_open_tag)sviệc quyên góp</a> — việc điều hành trang web này không hề rẻ và khoản đóng góp của bạn thực sự tạo nên sự khác biệt. Cảm ơn bạn rất nhiều. Giới thiệu bạn bè, và cả bạn và bạn bè của bạn sẽ nhận được thêm %(percentage)s%% lượt tải nhanh! Gây ngạc nhiên cho người thân yêu, cung cấp cho họ một tài khoản có tư cách thành viên. Quà tặng này Valentine hoàn hảo! Tìm hiểu thêm… Tài khoản Hoạt động Nâng cao Blog của Anna ↗ Phần mềm của Anna ↗ bản beta Khám phá Mã Bộ dữ liệu Quyên góp Tệp đã tải Những câu hỏi thường gặp Trang Chủ Cải thiện dữ liệu số Dữ liệu cho mô hình ngôn ngữ lớn Đăng nhập / Đăng ký Các lần quyên góp của tôi Hồ sơ công khai Tìm kiếm Bảo mật Tải về qua Torrent Phiên dịch ↗ Tình nguyện & Tiền thưởng Tải xuống gần đây: 📚&nbsp;Thư viện mã nguồn mở lớn nhất thế giới. ⭐️&nbsp;Bao gồm nội dung từ Sci-Hub, Library Genesis, Z-Library, và hơn thế nữa. 📈&nbsp;%(book_any)s quyển sách, %(journal_article)s bài viết, %(book_comic)s bộ truyện tranh, %(magazine)s tạp chí— được lưu trữ mãi mãi.  và  và còn nữa DuXiu Internet Archive Lending Library LibGen 📚&nbsp;Thư viện hoàn toàn miễn phí lớn nhất trong lịch sử loài người. 📈&nbsp;%(book_count)s&nbsp; cuốn sách, %(paper_count)s&nbsp;bài viết — được lưu giữ vĩnh viễn. ⭐️&nbsp;Chúng tôi có những bản sao của %(libraries)s. Chúng tôi đào và làm mở mã nguồn %(scraped)s. Toàn bộ câu lệnh và dữ liệu của chúng tôi đều là nguồn mở. OpenLib Sci-Hub ,  📚 Thưu viện mã nguồn mở lớn nhất thế giới.<br>⭐️ Bao gồm nội dung từ Scihub, Libgen, Zlib, và hơn nữa. zlib Anna's Archive Yêu cầu không hợp lệ. Truy cập %(websites)s. Thư viện dữ liệu mở nguồn mở lớn nhất thế giới. Mirrors Sci-Hub, Library Genesis, Z-Library, và hơn nữa. Tìm kiếm thư viện của Anna Anna’s Archive Xin vui lòng làm mới để thử lại. <a %(a_contact)s>Liên hệ với chúng tôi</a> nếu sự cố vẫn tiếp diễn trong nhiều giờ. 🔥 Sự cố khi tải trang này <li>1. Theo dõi chúng tôi trên <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, hoặc <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Lan truyền về Anna’s Archive trên Twitter, Reddit, Tiktok, Instagram, tại quán cà phê hoặc thư viện địa phương của bạn, hoặc bất cứ nơi nào bạn đến! Chúng tôi không tin vào việc giữ kín — nếu chúng tôi bị gỡ xuống, chúng tôi sẽ xuất hiện lại ở nơi khác, vì tất cả mã và dữ liệu của chúng tôi đều hoàn toàn mã nguồn mở.</li><li>3. Nếu bạn có thể, hãy xem xét <a href="/donate">quyên góp</a>.</li><li>4. Giúp <a href="https://translate.annas-software.org/">dịch</a> trang web của chúng tôi sang các ngôn ngữ khác nhau.</li><li>5. Nếu bạn là kỹ sư phần mềm, hãy xem xét đóng góp vào <a href="https://annas-software.org/">mã nguồn mở</a> của chúng tôi, hoặc chia sẻ <a href="/datasets">các torrent</a> của chúng tôi.</li> 10. Tạo hoặc giúp duy trì trang Wikipedia cho Lưu Trữ của Anna bằng ngôn ngữ của bạn. 11. Chúng tôi đang tìm cách đặt các quảng cáo nhỏ, tinh tế. Nếu bạn muốn quảng cáo trên Lưu Trữ của Anna, vui lòng cho chúng tôi biết. 6. Nếu bạn là một nhà nghiên cứu bảo mật, chúng tôi có thể sử dụng kỹ năng của bạn cho cả tấn công và phòng thủ. Hãy xem trang <a %(a_security)s>Bảo mật</a> của chúng tôi. 7. Chúng tôi đang tìm kiếm các chuyên gia về thanh toán cho các thương gia ẩn danh. Bạn có thể giúp chúng tôi thêm các cách quyên góp tiện lợi hơn không? PayPal, WeChat, thẻ quà tặng. Nếu bạn biết ai đó, vui lòng liên hệ với chúng tôi. 8. Chúng tôi luôn tìm kiếm thêm dung lượng máy chủ. 9. Bạn có thể giúp bằng cách báo cáo sự cố tệp, để lại bình luận và tạo danh sách ngay trên trang web này. Bạn cũng có thể giúp bằng cách <a %(a_upload)s>tải lên thêm sách</a>, hoặc sửa chữa sự cố tệp hoặc định dạng của các sách hiện có. Để biết thêm thông tin chi tiết về cách tình nguyện, xem trang <a %(a_volunteering)s>Tình nguyện & Tiền thưởng</a> của chúng tôi. Chúng tôi tin tưởng mạnh mẽ vào sự lưu thông tự do của thông tin, và việc bảo tồn kiến thức và văn hóa. Với công cụ tìm kiếm này, chúng tôi xây dựng trên vai những người khổng lồ. Chúng tôi rất tôn trọng công việc vất vả của những người đã tạo ra các thư viện bóng tối khác nhau, và hy vọng rằng công cụ tìm kiếm này sẽ mở rộng tầm với của họ. Để cập nhật tiến trình của chúng tôi, theo dõi Anna trên <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> hoặc <a href="https://t.me/annasarchiveorg">Telegram</a>. Để có câu hỏi và phản hồi, vui lòng liên hệ Anna tại %(email)s. ID tài khoản: %(account_id)s Thoát tài khoản ❌ Đã xảy ra lỗi. Hãy tải lại trang và thử lại. ✅ Bạn đã đăng xuất. Tải lại trang để đăng nhập lại. Các lượt tải nhanh đã sử dụng (trong 24 giờ qua): <strong>%(used)s / %(total)s</strong> Thành viên: <strong>%(tier_name)s</strong> đến %(until_date)s <a %(a_extend)s>(gia hạn)</a> Bạn có thể kết hợp nhiều gói thành viên (lượt tải nhanh mỗi 24 giờ sẽ được cộng dồn). Thành viên: <strong>Không có</strong> <a %(a_become)s>(trở thành thành viên)</a> Liên hệ với Anna tại %(email)s nếu bạn quan tâm đến việc nâng cấp thành viên của mình lên một cấp cao hơn. Hồ sơ công khai: %(profile_link)s Khóa bí mật (đừng chia sẻ nó!): %(secret_key)s hiển thị Tham gia với chúng tôi tại đây! Nâng cấp lên <a %(a_tier)s>hạng cao hơn</a> để tham gia nhóm của chúng tôi. Nhóm Telegram độc quyền: %(link)s Tài khoản tải xuống nào? Đăng nhập Đừng làm mất mã của bạn! Khóa bí mật không hợp lệ. Hãy xác minh khóa của bạn và thử lại hoặc đăng ký một tài khoản mới bên dưới. Mật khẩu bí mật Nhập khóa bí mật của bạn để đăng nhập: Tài khoản cũ dùng email? Điền <a %(a_open)s>email vào đây</a>. Tạo tài khoản mới Chưa có tài khoản? Đăng ký thành công! Khóa bí mật của bạn là: <span %(span_key)s>%(key)s</span> Hãy lưu mã này cẩn thận. Nếu bạn làm mất, bạn sẽ mất quyền truy cập vào tài khoản của mình. <li %(li_item)s><strong>Đánh dấu trang.</strong> Bạn có thể đánh dấu trang này để lấy lại mã của mình.</li><li %(li_item)s><strong>Tải xuống.</strong> Nhấp vào <a %(a_download)s>liên kết này</a> để tải xuống mã của bạn.</li><li %(li_item)s><strong>Trình quản lý mật khẩu.</strong> Sử dụng trình quản lý mật khẩu để lưu mã khi bạn nhập nó bên dưới.</li> Đăng nhập / Đăng ký Xác minh trình duyệt Cảnh báo: mã có các ký tự Unicode không chính xác và có thể hoạt động không đúng trong nhiều tình huống khác nhau. Dữ liệu nhị phân thô có thể được giải mã từ biểu diễn base64 trong URL. Mô tả Nhãn Tiền tố Liên kết URL cho một mã cụ thể Trang web Mã bắt đầu với “%(prefix_label)s” Xin vui lòng đừng quét các trang này. Thay vào đó, chúng tôi khuyến nghị <a %(a_import)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi, và chạy <a %(a_software)s>mã nguồn mở</a> của chúng tôi. Dữ liệu thô có thể được khám phá thủ công thông qua các tệp JSON như <a %(a_json_file)s>tệp này</a>. Ít hơn %(count)s bản ghi Liên kết URL chung Khám phá mã Mục lục của Khám phá các mã mà các bản ghi được gắn thẻ với, theo tiền tố. Cột “bản ghi” hiển thị số lượng bản ghi được gắn thẻ với mã có tiền tố đã cho, như được thấy trong công cụ tìm kiếm (bao gồm cả các bản ghi chỉ có dữ liệu số). Cột “mã” hiển thị có bao nhiêu mã thực sự có tiền tố đã cho. Tiền tố mã đã biết “%(key)s” Thêm nữa… Tiền tố %(count)s bản ghi khớp với “%(prefix_label)s” mã bản ghi “%%s” sẽ được thay thế bằng giá trị của mã Tìm kiếm Lưu trữ của Anna Mã Liên kết URL cho mã cụ thể: “%(url)s” Trang này có thể mất một lúc để tạo, đó là lý do tại sao nó yêu cầu captcha của Cloudflare. <a %(a_donate)s>Thành viên</a> có thể bỏ qua captcha. Lạm dụng đã được báo cáo: Phiên bản tốt hơn Bạn có muốn báo cáo người dùng này vì hành vi lạm dụng hoặc không phù hợp không? Vấn đề tệp: %(file_issue)s bình luận ẩn Trả lời Báo cáo lạm dụng Bạn đã báo cáo người dùng này vì lạm dụng. Các khiếu nại bản quyền gửi đến email này sẽ bị bỏ qua; thay vào đó xin hãy sử dụng biểu mẫu. Hiển thị email Chúng tôi rất hoan nghênh các phản hồi và câu hỏi của bạn! Tuy nhiên, do lượng thư rác và email vô nghĩa mà chúng tôi nhận được, vui lòng đánh dấu vào các ô để xác nhận bạn hiểu các điều kiện này khi liên hệ với chúng tôi. Bất kỳ cách liên hệ nào khác về khiếu nại bản quyền sẽ tự động bị xóa. Đối với các khiếu nại DMCA / bản quyền, sử dụng <a %(a_copyright)s>biểu mẫu này</a>. Địa chỉ email liên lạc Các liên kết URL trên Anna’s Archive (bắt buộc). Một URL mỗi dòng. Vui lòng chỉ bao gồm các URL mô tả chính xác cùng một phiên bản của một cuốn sách. Nếu bạn muốn khiếu nại cho nhiều cuốn sách hoặc nhiều phiên bản, vui lòng gửi biểu mẫu này nhiều lần. Các khiếu nại gộp nhiều cuốn sách hoặc phiên bản sẽ bị từ chối. Địa chỉ (bắt buộc) Mô tả rõ ràng về tài liệu nguồn (bắt buộc) E-mail (bắt buộc) URL đến tài liệu nguồn, một URL mỗi dòng (bắt buộc). Vui lòng bao gồm càng nhiều càng tốt, để giúp chúng tôi xác minh khiếu nại của bạn (ví dụ: Amazon, WorldCat, Google Books, DOI). ISBN của tài liệu nguồn (nếu có). Một ISBN mỗi dòng. Vui lòng chỉ bao gồm những ISBN khớp chính xác với phiên bản mà bạn đang báo cáo khiếu nại bản quyền. Tên của bạn (bắt buộc) ❌ Đã xảy ra lỗi. Vui lòng tải lại trang và thử lại. ✅ Cảm ơn bạn đã gửi khiếu nại bản quyền. Chúng tôi sẽ xem xét nó sớm nhất có thể. Vui lòng tải lại trang để gửi một khiếu nại khác. Các liên kết URL <a %(a_openlib)s>Open Library</a> của tài liệu nguồn, một liên kết URL mỗi dòng. Vui lòng dành chút thời gian để tìm kiếm tài liệu nguồn của bạn trên Open Library. Điều này sẽ giúp chúng tôi xác minh khiếu nại của bạn. Số điện thoại (bắt buộc) Tuyên bố và chữ ký (bắt buộc) Gửi khiếu nại Nếu bạn có khiếu nại về DMCA hoặc bản quyền khác, vui lòng điền vào biểu mẫu này càng chính xác càng tốt. Nếu bạn gặp bất kỳ vấn đề nào, vui lòng liên hệ với chúng tôi tại địa chỉ DMCA chuyên dụng: %(email)s. Lưu ý rằng các khiếu nại gửi qua email đến địa chỉ này sẽ không được xử lý, nó chỉ dành cho các câu hỏi. Vui lòng sử dụng biểu mẫu dưới đây để gửi khiếu nại của bạn. Biểu mẫu khiếu nại DMCA / Bản quyền Bản ghi ví dụ trên Lưu Trữ của Anna Những torrent bởi Lưu Trữ của Anna Định dạng Container Lưu Trư của Anna Tập lệnh để nhập dữ liệu số Nếu bạn quan tâm đến việc sao lưu bộ dữ liệu này cho mục đích <a %(a_archival)s>lưu trữ</a> hoặc <a %(a_llm)s>đào tạo LLM</a>, xin vui lòng liên hệ với chúng tôi. Cập nhật lần cuối: %(date)s Trang web chính %(source)s Tài liệu dữ liệu số (hầu hết các trường) Các tệp được bản sao bởi Anna’s Archive: %(count)s (%(percent)s%%) Tài nguyên Tổng số tệp: %(count)s Tổng kích thước tệp: %(size)s Bài viết trên blog của chúng tôi về dữ liệu này <a %(duxiu_link)s>Duxiu</a> là một cơ sở dữ liệu khổng lồ về sách đã được quét, được tạo ra bởi <a %(superstar_link)s>Nhóm Thư Viện Kỹ Thuật Số SuperStar</a>. Phần lớn là sách học thuật, được quét để cung cấp kỹ thuật số cho các trường đại học và thư viện. Đối với khán giả nói tiếng Anh, <a %(princeton_link)s>Princeton</a> và <a %(uw_link)s>Đại học Washington</a> có những tổng quan tốt. Cũng có một bài viết xuất sắc cung cấp thêm thông tin: <a %(article_link)s>“Số hóa sách tiếng Trung: Nghiên Cứu Trường Hợp về Công Cụ Tìm Kiếm Duxiu Scholar”</a>. Những cuốn sách từ DuXiu đã bị sao chép lậu trên internet Trung Quốc từ lâu. Thông thường, chúng được bán với giá chưa đến một đô la bởi các nhà bán lẻ. Chúng thường được phân phối bằng cách sử dụng dịch vụ tương đương với Google Drive của Trung Quốc, dịch vụ này thường bị hack để có thêm dung lượng lưu trữ. Một số chi tiết kỹ thuật có thể được tìm thấy <a %(link1)s>tại đây</a> và <a %(link2)s>tại đây</a>. Mặc dù những cuốn sách này đã được xuất bản công khai một phần, việc thu gom chúng trên quy mô lớn là một trở ngại lớn. Chúng tôi đặt ưu tiên rất cao cho việc này trong danh sách việc cần làm, và đã bỏ công sức toàn thời gian cho nó trong vòng nhiều tháng. Thế nhưng, vào cuối năm 2023, một tình nguyện viên tuyệt vời và đặc biệt xuất sắc đã liên lạc với chúng tôi rằng họ đã hoàn thành công việc này - một phí tổn cực kì tốn kém. Người này đã chia sẻ với chúng tôi toàn bộ bộ sưu tập của họ và không đòi hỏi lại bất cứ gì ngoài sự đảm bảo rằng chúng sẽ được bảo tồn lâu dài. Thật sự vô cùng kinh ngạc. Thông tin thêm từ các tình nguyện viên của chúng tôi (ghi chú thô): Được điều chỉnh từ <a %(a_href)s>bài viết blog</a> của chúng tôi. DuXiu 读秀 %(count)s tệp Bộ dữ liệu này liên quan chặt chẽ đến <a %(a_datasets_openlib)s>bộ dữ liệu Open Library</a>. Nó chứa một bản quét tất cả dữ liệu số và một phần lớn các tệp từ Thư viện Cho Mượn Kỹ Thuật Số Có Kiểm Soát (CDL) của IA. Các bản cập nhật được phát hành theo <a %(a_aac)s>định dạng Container Lưu Trư của Anna</a>. Các bản ghi này được tham chiếu trực tiếp từ bộ dữ liệu Open Library, nhưng cũng chứa các bản ghi không có trong Open Library. Chúng tôi cũng có một số tệp dữ liệu được cộng đồng thu thập trong nhiều năm qua. Bộ sưu tập bao gồm hai phần. Bạn cần cả hai phần để có được tất cả dữ liệu (trừ các torrent đã bị thay thế, được gạch bỏ trên trang torrent). Thư viện Cho mượn Sách Kỹ thuật số bản phát hành đầu tiên của chúng tôi, trước khi chúng tôi chuẩn hóa theo định dạng <a %(a_aac)s>Container Lưu Trư của Anna (AAC)</a>. Chứa dữ liệu số (dưới dạng json và xml), các tệp pdf (từ các hệ thống cho mượn sách kỹ thuật số acsm và lcpdf), và hình thu nhỏ của bìa sách. các bản phát hành mới gia tăng, sử dụng AAC. Chỉ chứa dữ liệu số với dấu thời gian sau ngày 2023-01-01, vì phần còn lại đã được bao phủ bởi “ia”. Cũng bao gồm tất cả các tệp pdf, lần này từ các hệ thống cho mượn sách acsm và “bookreader” (trình đọc web của IA). Mặc dù tên không hoàn toàn chính xác, chúng tôi vẫn đưa các tệp bookreader vào bộ sưu tập ia2_acsmpdf_files, vì chúng không trùng lặp. IA Mượn Kỹ Thuật Số Có Kiểm Soát (CDL) 98%%+ tệp có thể tìm kiếm được. Sứ mệnh của chúng tôi là lưu trữ tất cả các cuốn sách trên thế giới (cũng như các bài báo, tạp chí, v.v.) và làm cho chúng dễ dàng tiếp cận. Chúng tôi tin rằng tất cả các cuốn sách nên được phản chiếu rộng rãi để đảm bảo tính dự phòng và khả năng phục hồi. Đây là lý do tại sao chúng tôi đang tập hợp các tệp từ nhiều nguồn khác nhau. Một số nguồn hoàn toàn mở và có thể được phản chiếu hàng loạt (chẳng hạn như Sci-Hub). Những nguồn khác thì đóng và bảo vệ, vì vậy chúng tôi cố gắng thu thập chúng để “giải phóng” sách của họ. Những nguồn khác thì nằm ở giữa. Tất cả dữ liệu của chúng tôi có thể được <a %(a_torrents)s>tải qua torrent</a> và tất cả dữ liệu số của chúng tôi có thể được <a %(a_anna_software)s>tạo</a> hoặc <a %(a_elasticsearch)s>tải xuống</a> dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB. Dữ liệu thô có thể được khám phá thủ công thông qua các tệp JSON như <a %(a_dbrecord)s>này</a>. Dữ liệu số Trang web ISBN Cập nhật lần cuối: %(isbn_country_date)s (%(link)s) Tài nguyên Cơ quan ISBN Quốc tế thường xuyên phát hành các phạm vi mà họ đã phân bổ cho các cơ quan ISBN quốc gia. Từ đó, chúng tôi có thể xác định quốc gia, khu vực hoặc nhóm ngôn ngữ mà ISBN này thuộc về. Hiện tại, chúng tôi sử dụng dữ liệu này gián tiếp, thông qua thư viện Python <a %(a_isbnlib)s>isbnlib</a>. Thông tin quốc gia ISBN Đây là một bản dump của rất nhiều cuộc gọi đến isbndb.com trong tháng 9 năm 2022. Chúng tôi đã cố gắng bao phủ tất cả các phạm vi ISBN. Đây là khoảng 30,9 triệu bản ghi. Trên trang web của họ, họ tuyên bố rằng họ thực sự có 32,6 triệu bản ghi, vì vậy chúng tôi có thể đã bỏ lỡ một số, hoặc <em>họ</em> có thể đã làm sai điều gì đó. Các phản hồi JSON hầu như là nguyên bản từ máy chủ của họ. Một vấn đề chất lượng dữ liệu mà chúng tôi nhận thấy là đối với các số ISBN-13 bắt đầu bằng một tiền tố khác ngoài “978-”, họ vẫn bao gồm một trường “isbn” chỉ đơn giản là số ISBN-13 với ba số đầu tiên bị cắt bỏ (và chữ số kiểm tra được tính lại). Điều này rõ ràng là sai, nhưng đây là cách họ làm, vì vậy chúng tôi không thay đổi nó. Một vấn đề tiềm ẩn khác mà bạn có thể gặp phải là thực tế rằng trường “isbn13” có các bản sao, vì vậy bạn không thể sử dụng nó làm khóa chính trong cơ sở dữ liệu. Các trường “isbn13”+“isbn” kết hợp dường như là duy nhất. Phát hành 1 (2022-10-31) Torrents hư cấu đang bị chậm (mặc dù ID ~4-6M chưa được tải qua torrents vì chúng trùng lặp với torrents Zlib của chúng tôi). Bài viết blog của chúng tôi về việc phát hành truyện tranh Torrent truyện tranh trên Anna’s Archive Để biết câu chuyện nền của các nhánh Library Genesis khác nhau, xem trang cho <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li chứa hầu hết nội dung và dữ liệu số giống như Libgen.rs, nhưng có một số bộ sưu tập bổ sung, cụ thể là truyện tranh, tạp chí và tài liệu tiêu chuẩn. Nó cũng đã tích hợp <a %(a_scihub)s>Sci-Hub</a> vào dữ liệu số và công cụ tìm kiếm của mình, đó là những gì chúng tôi sử dụng cho cơ sở dữ liệu của chúng tôi. Dữ liệu số cho thư viện này có sẵn miễn phí <a %(a_libgen_li)s>tại libgen.li</a>. Tuy nhiên, máy chủ này chậm và không hỗ trợ tiếp tục kết nối bị gián đoạn. Các tệp tương tự cũng có sẵn trên <a %(a_ftp)s>máy chủ FTP</a>, hoạt động tốt hơn. Sách phi hư cấu cũng dường như đã có sự khác biệt, nhưng không có các torrent mới. Có vẻ như điều này đã xảy ra từ đầu năm 2022, mặc dù chúng tôi chưa xác minh điều này. Theo quản trị viên của Libgen.li, bộ sưu tập “fiction_rus” (tiểu thuyết Nga) nên được bao phủ bởi các torrent phát hành thường xuyên từ <a %(a_booktracker)s>booktracker.org</a>, đặc biệt là các torrent <a %(a_flibusta)s>flibusta</a> và <a %(a_librusec)s>lib.rus.ec</a> (mà chúng tôi sao lưu <a %(a_torrents)s>tại đây</a>, mặc dù chúng tôi chưa xác định được torrent nào tương ứng với tệp nào). Bộ sưu tập tiểu thuyết có các torrent riêng của nó (được tách ra từ <a %(a_href)s>Libgen.rs</a>) bắt đầu từ %(start)s. Một số phạm vi không có torrent (chẳng hạn như phạm vi tiểu thuyết f_3463000 đến f_4260000) có khả năng là các tệp của Z-Library (hoặc các tệp trùng lặp khác), mặc dù chúng tôi có thể muốn thực hiện một số loại bỏ trùng lặp và tạo torrent cho các tệp duy nhất của lgli trong các phạm vi này. Thống kê cho tất cả các bộ sưu tập có thể được tìm thấy <a %(a_href)s>trên trang web của libgen</a>. Những torrent được có sẵn cho hầu hết các nội dung bổ sung, đặc biệt là torrents cho truyện tranh, tạp chí và tài liệu tiêu chuẩn đã được phát hành hợp tác với Lưu trữ của Anna. Lưu ý rằng các tệp torrent đề cập đến “libgen.is” là bản sao rõ ràng của <a %(a_libgen)s>Libgen.rs</a> (“.is” là một miền khác được sử dụng bởi Libgen.rs). Một tài nguyên hữu ích khi sử dụng dữ liệu số là <a %(a_href)s>trang này</a>. %(icon)s Bộ sưu tập “fiction_rus” (tiểu thuyết Nga) của họ không có torrent riêng, nhưng được bao phủ bởi các torrent từ những người khác, và chúng tôi giữ một <a %(fiction_rus)s>bản sao</a>. Các torrent tiểu thuyết Nga trên Lưu trữ của Anna Torrent tiểu thuyết trên Anna’s Archive Diễn đàn thảo luận Dữ liệu số Dữ liệu số qua FTP Torrent tạp chí trên Anna’s Archive Thông tin trường dữ liệu số Bản sao của các torrent khác (và các torrent tiểu thuyết và truyện tranh độc đáo) Các torrent tài liệu tiêu chuẩn trên Lưu trữ của Anna Libgen.li Torrent bởi Lưu Trữ của Anna (bìa sách) Library Genesis được biết đến với việc hào phóng cung cấp dữ liệu của họ dưới dạng torrent. Bộ sưu tập Libgen của chúng tôi bao gồm dữ liệu phụ trợ mà họ không phát hành trực tiếp, hợp tác với họ. Cảm ơn rất nhiều đến tất cả những người tham gia Library Genesis đã làm việc với chúng tôi! Blog của chúng tôi về việc phát hành bìa sách Trang này là về phiên bản “.rs”. Nó nổi tiếng vì thường xuyên xuất bản cả dữ liệu số và toàn bộ nội dung của danh mục sách của nó. Bộ sưu tập sách của nó được chia thành phần tiểu thuyết và phi tiểu thuyết. Một nguồn tài nguyên hữu ích để sử dụng dữ liệu số là <a %(a_metadata)s>trang này</a> (chặn dải IP, có thể phải cần VPN). Tính đến tháng 03-2024, các torrent mới đang được đăng trong <a %(a_href)s>chủ đề diễn đàn này</a> (chặn dải IP, có thể cần VPN). Torrent Hư Cấu trên Lưu Trữ của Anna Torrent Hư Cấu Libgen.rs Diễn đàn thảo luận Libgen.rs Dữ liệu số Libgen.rs Thông tin trường dữ liệu số Libgen.rs Torrent Phi Hư Cấu Libgen.rs Torrent Phi Hư Cấu trên Lưu Trữ của Anna %(example)s cho một cuốn sách hư cấu. <a %(blog_post)s>Phát hành đầu tiên</a> này khá nhỏ: khoảng 300GB bìa sách từ nhánh Libgen.rs, cả hư cấu và phi hư cấu. Chúng được tổ chức theo cách chúng xuất hiện trên libgen.rs, ví dụ: %(example)s cho một cuốn sách phi hư cấu. Giống như với bộ sưu tập Z-Library, chúng tôi đặt tất cả vào một tệp .tar lớn, có thể được gắn kết bằng <a %(a_ratarmount)s>ratarmount</a> nếu bạn muốn phục vụ các tệp trực tiếp. Phát hành 1 (%(date)s) Câu chuyện ngắn gọn về các nhánh khác nhau của Library Genesis (hoặc “Libgen”) là theo thời gian, những người khác nhau tham gia vào Library Genesis đã có mâu thuẫn, và đi theo con đường riêng của họ. Theo <a %(a_mhut)s>bài đăng trên diễn đàn</a> này, Libgen.li ban đầu được lưu trữ tại “http://free-books.dontexist.com”. Phiên bản “.fun” được tạo ra bởi người sáng lập ban đầu. Nó đang được cải tiến để ủng hộ một phiên bản mới, phân phối nhiều hơn. Phiên bản <a %(a_li)s>“.li”</a> có một bộ sưu tập truyện tranh khổng lồ, cũng như các nội dung khác, chưa (chưa) có sẵn để tải xuống hàng loạt qua torrent. Nó có một bộ sưu tập torrent riêng của sách tiểu thuyết và chứa dữ liệu số của <a %(a_scihub)s>Sci-Hub</a> trong cơ sở dữ liệu của nó. Phiên bản “.rs” có dữ liệu rất giống nhau và thường xuyên phát hành bộ sưu tập của họ dưới dạng torrent số lượng lớn. Nó được chia thành hai phần “tiểu thuyết” và “phi tiểu thuyết”. Ban đầu tại “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> theo một số cách cũng là một nhánh của Library Genesis, mặc dù họ đã sử dụng một tên khác cho dự án của mình. Libgen.rs Chúng tôi cũng làm phong phú thêm bộ sưu tập của mình với các nguồn chỉ có dữ liệu số, mà chúng tôi có thể khớp với các tệp tin, ví dụ như sử dụng số ISBN hoặc các trường khác. Dưới đây là tổng quan về những nguồn đó. Một lần nữa, một số nguồn này hoàn toàn mở, trong khi với những nguồn khác chúng tôi phải thu thập dữ liệu. Lưu ý rằng trong tìm kiếm dữ liệu số, chúng tôi hiển thị các bản ghi gốc. Chúng tôi không thực hiện bất kỳ việc hợp nhất bản ghi nào. Nguồn chỉ có dữ liệu số Open Library là một dự án mã nguồn mở của Internet Archive để lập danh mục mọi cuốn sách trên thế giới. Nó có một trong những hoạt động quét sách lớn nhất thế giới và có nhiều sách có sẵn để cho mượn kỹ thuật số. Danh mục dữ liệu số sách của nó có sẵn miễn phí để tải xuống và được bao gồm trên Lưu Trữ của Anna (mặc dù hiện tại không có trong tìm kiếm, trừ khi bạn tìm kiếm rõ ràng một ID Open Library). Open Library Loại trừ các bản sao Cập nhật lần cuối Tỷ lệ phần trăm số lượng tệp %% được phản chiếu bởi AA / có sẵn qua các torrent Kích thước Nguồn Dưới đây là tổng quan nhanh về các nguồn của các tệp trên Anna’s Archive. Vì các thư viện bóng thường đồng bộ dữ liệu từ nhau, nên có sự trùng lặp đáng kể giữa các thư viện. Đó là lý do tại sao các con số không cộng lại thành tổng. Tỷ lệ phần trăm “được sao lưu và chia sẻ bởi Anna’s Archive” cho thấy có bao nhiêu tệp chúng tôi tự bản sao. Chúng tôi gieo những tệp này hàng loạt qua các torrent và làm cho chúng có sẵn để tải trực tiếp qua các trang web đối tác. Tổng quan Tổng cộng Torrent trên Lưu Trữ của Anna Để biết thêm thông tin về Sci-Hub, vui lòng tham khảo <a %(a_scihub)s>trang web chính thức</a>, <a %(a_wikipedia)s>trang Wikipedia</a>, và <a %(a_radiolab)s>cuộc phỏng vấn podcast</a> này. Lưu ý rằng Sci-Hub đã bị <a %(a_reddit)s>đóng băng từ năm 2021</a>. Trước đó cũng đã bị đóng băng, nhưng vào năm 2021, một vài triệu bài báo đã được thêm vào. Tuy nhiên, một số lượng hạn chế các bài báo vẫn được thêm vào các bộ sưu tập “scimag” của Libgen, mặc dù không đủ để tạo ra các torrent mới. Chúng tôi sử dụng dữ liệu số của Sci-Hub do <a %(a_libgen_li)s>Libgen.li</a> cung cấp trong bộ sưu tập “scimag”. Chúng tôi cũng sử dụng tập dữ liệu <a %(a_dois)s>dois-2022-02-12.7z</a>. Lưu ý rằng các torrent “smarch” đã <a %(a_smarch)s>bị ngừng sử dụng</a> và do đó không được bao gồm trong danh sách torrent của chúng tôi. Torrent trên Libgen.li Torrent trên Libgen.rs Dữ liệu số và torrent Cập nhật trên Reddit Cuộc phỏng vấn podcast Trang Wikipedia Sci-Hub Sci-Hub: đã bị đóng băng từ năm 2021; hầu hết có sẵn qua torrents Libgen.li: bổ sung nhỏ kể từ đó</div> Một số thư viện nguồn khuyến khích chia sẻ hàng loạt dữ liệu của họ qua các torrent, trong khi những thư viện khác không sẵn sàng chia sẻ bộ sưu tập của họ. Trong trường hợp sau, Anna’s Archive cố gắng thu thập bộ sưu tập của họ và làm cho chúng có sẵn (xem trang <a %(a_torrents)s>Torrents</a> của chúng tôi). Cũng có những tình huống trung gian, ví dụ, nơi các thư viện nguồn sẵn sàng chia sẻ, nhưng không có đủ nguồn lực để làm điều đó. Trong những trường hợp đó, chúng tôi cũng cố gắng giúp đỡ. Dưới đây là tổng quan về cách chúng tôi giao tiếp với các thư viện nguồn khác nhau. Thư viện nguồn %(icon)s Các cơ sở dữ liệu tệp khác nhau được rải rác trên internet Trung Quốc; mặc dù thường là các cơ sở dữ liệu trả phí %(icon)s Hầu hết các tệp chỉ có thể truy cập bằng tài khoản BaiduYun cao cấp; tốc độ tải xuống chậm. %(icon)s Anna’s Archive có quản lý một bộ sưu tập <a %(duxiu)s>tệp DuXiu</a> %(icon)s Các cơ sở dữ liệu số khác nhau rải rác trên internet Trung Quốc; mặc dù thường là các cơ sở dữ liệu trả phí %(icon)s Không có các bản dữ liệu số dễ dàng truy cập cho toàn bộ bộ sưu tập. %(icon)s Anna’s Archive quản lý một bộ sưu tập <a %(duxiu)s>dữ liệu số DuXiu</a> Tệp tin %(icon)s Tệp chỉ có sẵn để mượn trong một phạm vi hạn chế, với các hạn chế truy cập khác nhau %(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(ia)s>tệp IA</a> %(icon)s Một số metadata có thể được truy cập từ <a %(openlib)s>bản sao cơ sở dữ liệu Open Library</a>, nhưng chúng không phủ hoàn toàn bộ sưu tập IA %(icon)s Không có bản sao dữ liệu số dễ dàng truy cập cho toàn bộ bộ sưu tập %(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(ia)s>dữ liệu số của IA</a> Cập nhật lần cuối %(icon)s Lưu trữ của Anna và Libgen.li cùng quản lý các bộ sưu tập <a %(comics)s>truyện tranh</a>, <a %(magazines)s>tạp chí</a>, <a %(standarts)s>tài liệu tiêu chuẩn</a>, và <a %(fiction)s>tiểu thuyết (tách ra từ Libgen.rs)</a>. %(icon)s Torrent Phi hư cấu được chia sẻ với Libgen.rs (và được bản sao <a %(libgenli)s>tại đây</a>). %(icon)s <a %(dbdumps)s>Bản sao cơ sở dữ liệu HTTP</a> hàng quý %(icon)s Torrent tự động cho <a %(nonfiction)s>Phi hư cấu</a> và <a %(fiction)s>Hư cấu</a> %(icon)s Lưu trữ của Anna quản lý một bộ sưu tập <a %(covers)s>torrent bìa sách</a> %(icon)s <a %(dbdumps)s>Bản sao cơ sở dữ liệu HTTP</a> hàng ngày Dữ liệu số %(icon)s Các bản dữ liệu <a %(dbdumps)s>hàng tháng</a> %(icon)s Torrent dữ liệu có sẵn <a %(scihub1)s>tại đây</a>, <a %(scihub2)s>tại đây</a>, và <a %(libgenli)s>tại đây</a> %(icon)s Một số tệp mới đang <a %(libgenrs)s>được</a> <a %(libgenli)s>thêm</a> vào “scimag” của Libgen, nhưng không đủ để tạo torrent mới %(icon)s Sci-Hub đã ngừng cập nhật tệp mới từ năm 2021. %(icon)s Các bản sao dữ liệu số có sẵn <a %(scihub1)s>tại đây</a> và <a %(scihub2)s>tại đây</a>, cũng như là một phần của <a %(libgenli)s>cơ sở dữ liệu Libgen.li</a> (mà chúng tôi sử dụng) Nguồn %(icon)s Các nguồn nhỏ hơn hoặc một lần. Chúng tôi khuyến khích mọi người tải lên các thư viện bóng khác trước, nhưng đôi khi mọi người có các bộ sưu tập quá lớn để người khác sắp xếp, mặc dù không đủ lớn để xứng đáng có danh mục riêng. %(icon)s Không có sẵn trực tiếp với số lượng lớn, được bảo vệ chống lại việc quét dữ liệu %(icon)s Anna’s Archive quản lý một bộ sưu tập <a %(worldcat)s>dữ liệu số OCLC (WorldCat)</a> %(icon)s Lưu trữ của Anna và Z-Library cùng quản lý một bộ sưu tập <a %(metadata)s>dữ liệu số Z-Library</a> và <a %(files)s>tệp Z-Library</a> Bộ dữ liệu Chúng tôi kết hợp tất cả các nguồn trên vào một cơ sở dữ liệu hợp nhất mà chúng tôi sử dụng để phục vụ trang web này. Cơ sở dữ liệu hợp nhất này không có sẵn trực tiếp, nhưng vì Anna’s Archive hoàn toàn mã nguồn mở, nó có thể được <a %(a_generated)s>tạo ra</a> hoặc <a %(a_downloaded)s>tải xuống</a> khá dễ dàng dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB. Các script trên trang đó sẽ tự động tải xuống tất cả dữ liệu số cần thiết từ các nguồn đã đề cập ở trên. Nếu bạn muốn khám phá dữ liệu của chúng tôi trước khi chạy các script đó trên máy bạn, bạn có thể xem các tệp JSON của chúng tôi, liên kết thêm đến các tệp JSON khác. <a %(a_json)s>Tệp này</a> là một điểm khởi đầu tốt. Cơ sở dữ liệu hợp nhất Torrents bởi Lưu Trữ của Anna duyệt tìm kiếm Các nguồn nhỏ hơn hoặc một lần. Chúng tôi khuyến khích mọi người tải lên đến các thư viện bóng khác trước, nhưng đôi khi mọi người có các bộ sưu tập quá lớn để người khác sắp xếp, mặc dù không đủ lớn để xứng đáng có danh mục riêng. Tổng quan từ <a %(a1)s>trang datasets</a>. Từ <a %(a_href)s>aaaaarg.fail</a>. Có vẻ khá đầy đủ. Từ tình nguyện viên của chúng tôi “cgiym”. Từ một <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Có sự trùng lặp khá cao với các bộ sưu tập bài báo hiện có, nhưng rất ít trùng khớp MD5, vì vậy chúng tôi quyết định giữ nguyên hoàn toàn. Thu thập từ <q>iRead eBooks</q> (= phát âm <q>ai rit i-books</q>; airitibooks.com), bởi tình nguyện viên <q>j</q>. Tương ứng với metadata <q>airitibooks</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>. Từ một bộ sưu tập <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Một phần từ nguồn gốc, một phần từ the-eye.eu, một phần từ các bản sao khác. Từ một trang web torrent sách riêng tư, <a %(a_href)s>Bibliotik</a> (thường được gọi là “Bib”), trong đó các sách được gộp thành các torrent theo tên (A.torrent, B.torrent) và phân phối qua the-eye.eu. Từ tình nguyện viên của chúng tôi “bpb9v”. Để biết thêm thông tin về <a %(a_href)s>CADAL</a>, xem ghi chú trong <a %(a_duxiu)s>trang dữ liệu DuXiu của chúng tôi</a>. Thêm từ tình nguyện viên của chúng tôi “bpb9v”, chủ yếu là các tệp DuXiu, cũng như một thư mục “WenQu” và “SuperStar_Journals” (SuperStar là công ty đứng sau DuXiu). Từ tình nguyện viên của chúng tôi “cgiym”, các văn bản tiếng Trung từ nhiều nguồn khác nhau (được đại diện dưới dạng thư mục con), bao gồm từ <a %(a_href)s>China Machine Press</a> (một nhà xuất bản lớn của Trung Quốc). Các bộ sưu tập không phải tiếng Trung (được đại diện dưới dạng thư mục con) từ tình nguyện viên của chúng tôi “cgiym”. Thu thập sách về kiến trúc Trung Quốc, bởi tình nguyện viên <q>cm</q>: <q>Tôi đã lấy được bằng cách khai thác lỗ hổng mạng tại nhà xuất bản, nhưng lỗ hổng đó đã được đóng lại</q>. Tương ứng với metadata <q>chinese_architecture</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>. Sách từ nhà xuất bản học thuật <a %(a_href)s>De Gruyter</a>, được thu thập từ một vài torrent lớn. Bản scrape của <a %(a_href)s>docer.pl</a>, một trang web chia sẻ tệp của Ba Lan tập trung vào sách và các tác phẩm viết khác. Được scrape vào cuối năm 2023 bởi tình nguyện viên “p”. Chúng tôi không có dữ liệu số tốt từ trang web gốc (thậm chí không có phần mở rộng tệp), nhưng chúng tôi đã lọc các tệp giống sách và thường có thể trích xuất dữ liệu số từ chính các tệp. DuXiu epubs, trực tiếp từ DuXiu, được thu thập bởi tình nguyện viên “w”. Chỉ có các sách DuXiu gần đây có sẵn trực tiếp qua ebooks, vì vậy hầu hết trong số này phải là gần đây. Các tệp DuXiu còn lại từ tình nguyện viên “m”, không nằm trong định dạng PDG độc quyền của DuXiu (bộ <a %(a_href)s>dữ liệu DuXiu chính</a>). Được thu thập từ nhiều nguồn gốc ban đầu, tiếc là không bảo quản được những nguồn gốc đó trong đường dẫn tệp. <span></span> <span></span> <span></span> Thu thập sách khiêu dâm, bởi tình nguyện viên <q>do no harm</q>. Tương ứng với metadata <q>hentai</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>. <span></span> <span></span> Bộ sưu tập được scrape từ một nhà xuất bản Manga Nhật bởi tình nguyện viên “t”. <a %(a_href)s>Các tài liệu lưu trữ tư pháp được chọn của Longquan</a>, được cung cấp bởi tình nguyện viên “c”. Bản scrape của <a %(a_href)s>magzdb.org</a>, một đồng minh của Library Genesis (nó được liên kết trên trang chủ của libgen.rs) nhưng không muốn cung cấp tệp của họ trực tiếp. Được thu thập bởi tình nguyện viên “p” vào cuối năm 2023. <span></span> Nhiều tải lên nhỏ, quá nhỏ để làm bộ sưu tập con riêng, nhưng được đại diện dưới dạng thư mục. Ebooks từ AvaxHome, một trang web chia sẻ tệp của Nga. Lưu trữ báo và tạp chí. Tương ứng với metadata <q>newsarch_magz</q> trong <a %(a1)s><q>Các thu thập metadata khác</q></a>. Thu thập từ <a %(a1)s>Philosophy Documentation Center</a>. Bộ sưu tập của tình nguyện viên “o” người đã thu thập sách Ba Lan trực tiếp từ các trang web phát hành gốc (“scene”). Các bộ sưu tập kết hợp của <a %(a_href)s>shuge.org</a> bởi các tình nguyện viên “cgiym” và “woz9ts”. <span></span> <a %(a_href)s>“Thư viện Hoàng gia của Trantor”</a> (được đặt tên theo thư viện hư cấu), được scrape vào năm 2022 bởi tình nguyện viên “t”. <span></span> <span></span> <span></span> Các bộ sưu tập con (được đại diện dưới dạng thư mục) từ tình nguyện viên “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (bởi <a %(a_sikuquanshu)s>Dizhi(迪志)</a> ở Đài Loan), mebook (mebook.cc, 我的小书屋, phòng sách nhỏ của tôi — woz9ts: “Trang web này chủ yếu tập trung vào việc chia sẻ các tệp ebook chất lượng cao, một số trong đó được dàn trang bởi chính chủ sở hữu. Chủ sở hữu đã <a %(a_arrested)s>bị bắt</a> vào năm 2019 và ai đó đã tạo ra một bộ sưu tập các tệp mà anh ta đã chia sẻ.”). Các tệp DuXiu còn lại từ tình nguyện viên “woz9ts”, không ở định dạng PDG độc quyền của DuXiu (vẫn cần chuyển đổi sang PDF). Bộ sưu tập “tải lên” được chia thành các bộ sưu tập con nhỏ hơn, được chỉ định trong các AACID và tên torrent. Tất cả các bộ sưu tập con đầu tiên được loại bỏ trùng lặp so với bộ sưu tập chính, mặc dù các tệp JSON “upload_records” dữ liệu số vẫn chứa nhiều tham chiếu đến các tệp gốc. Các tệp không phải sách cũng đã được loại bỏ khỏi hầu hết các bộ sưu tập con, và thường <em>không</em> được ghi chú trong “upload_records” JSON. Các bộ sưu tập con là: Ghi chú Tập hợp con Nhiều bộ sưu tập con tự chúng cũng  bao gồm nhiều bộ sưu tập con  (chẳng hạn như từ các nguồn khác nhau), được biểu diễn dưới dạng thư mục trong các trường “filepath”. Các tải lên đến Lưu Trữ của Anna Bài viết blog của chúng tôi về dữ liệu này <a %(a_worldcat)s>WorldCat</a> là một cơ sở dữ liệu độc quyền của tổ chức phi lợi nhuận <a %(a_oclc)s>OCLC</a>, tập hợp các bản ghi dữ liệu số từ các thư viện trên khắp thế giới. Đây có thể là bộ sưu tập dữ liệu số thư viện lớn nhất trên thế giới. Vào tháng 10 năm 2023, chúng tôi đã <a %(a_scrape)s>phát hành</a> một bản scrape toàn diện của cơ sở dữ liệu OCLC (WorldCat), theo <a %(a_aac)s>định dạng Container Lưu Trữ của Anna</a>. Tháng 10 năm 2023, phát hành ban đầu: OCLC (WorldCat) Torrent bởi Anna’s Archive Bản ghi ví dụ trên Lưu Trữ của Anna (bộ sưu tập gốc) Bản ghi ví dụ trên Lưu Trữ của Anna (bộ sưu tập “zlib3”) Torrents bởi Lưu Trữ của Anna (dữ liệu số + nội dung) Bài viết blog về Phát hành 1 Bài viết blog về Phát hành 2 Cuối năm 2022, những người bị cho là người sáng lập Z-Library đã bị bắt, và tên miền này đã bị chính quyền Hoa Kỳ tịch thu. Kể từ đó, trang web này đang dần nhen nhóm tìm cách hoạt động trở lại. Hiện tại vẫn chưa rõ ai là người điều hành. Cập nhật vào tháng 2 năm 2023. Z-Library có nguồn gốc từ cộng đồng <a %(a_href)s>Library Genesis</a>, và ban đầu được khởi động với dữ liệu của họ. Kể từ đó, nó đã chuyên nghiệp hóa đáng kể và có giao diện hiện đại hơn nhiều. Do đó, họ có thể nhận được nhiều khoản quyên góp hơn, cả về mặt tài chính để tiếp tục cải thiện trang web của họ, cũng như quyên góp sách mới. Họ đã tích lũy được một bộ sưu tập lớn ngoài Library Genesis. Bộ sưu tập bao gồm ba phần. Các trang mô tả ban đầu cho hai phần đầu tiên được giữ lại bên dưới. Bạn cần cả ba phần để có được tất cả dữ liệu (trừ các torrents đã bị thay thế, được gạch bỏ trên trang torrents). %(title)s: phát hành đầu tiên của chúng tôi. Đây là lần phát hành đầu tiên của cái gọi là “Bản sao Thư viện Cướp biển” (“pilimi”). %(title)s: phát hành thứ hai, lần này với tất cả các tệp được gói trong các tệp .tar. %(title)s: các phát hành mới gia tăng, sử dụng <a %(a_href)s>định dạng Container Lưu Trữ của Anna (AAC)</a>, hiện được phát hành hợp tác với đội ngũ Z-Library. Bản sao ban đầu đã được thu thập một cách tỉ mỉ trong suốt năm 2021 và 2022. Tại thời điểm này, nó hơi lỗi thời: nó phản ánh trạng thái của bộ sưu tập vào tháng 6 năm 2021. Chúng tôi sẽ cập nhật điều này trong tương lai. Hiện tại chúng tôi đang tập trung vào việc phát hành đầu tiên này. Vì Library Genesis đã được bảo tồn với các torrent công khai và được bao gồm trong Z-Library, chúng tôi đã thực hiện việc loại bỏ trùng lặp cơ bản với Library Genesis vào tháng 6 năm 2022. Để làm điều này, chúng tôi đã sử dụng các hàm băm MD5. Có khả năng còn rất nhiều nội dung trùng lặp trong thư viện, chẳng hạn như nhiều định dạng tệp khác nhau của cùng một cuốn sách. Điều này rất khó phát hiện chính xác, vì vậy chúng tôi không làm. Sau khi loại bỏ trùng lặp, chúng tôi còn lại hơn 2 triệu tệp, tổng cộng chỉ dưới 7TB. Bộ sưu tập bao gồm hai phần: một tệp dump MySQL “.sql.gz” của dữ liệu số, và 72 tệp torrent khoảng 50-100GB mỗi tệp. Dữ liệu số chứa các dữ liệu được báo cáo bởi trang web Z-Library (tiêu đề, tác giả, mô tả, loại tệp), cũng như kích thước tệp thực tế và md5sum mà chúng tôi quan sát được, vì đôi khi những thông tin này không khớp nhau. Có vẻ như có các phạm vi tệp mà chính Z-Library có dữ liệu số không chính xác. Chúng tôi cũng có thể đã tải xuống các tệp không chính xác trong một số trường hợp riêng lẻ, điều này chúng tôi sẽ cố gắng phát hiện và sửa chữa trong tương lai. Các tệp torrent lớn chứa dữ liệu sách thực tế, với ID Z-Library làm tên tệp. Các phần mở rộng tệp có thể được tái tạo lại bằng cách sử dụng tệp dump dữ liệu số. Bộ sưu tập là sự kết hợp của nội dung phi hư cấu và hư cấu (không được tách riêng như trong Library Genesis). Chất lượng cũng rất khác nhau. Bản phát hành đầu tiên này hiện đã hoàn toàn có sẵn. Lưu ý rằng các tệp torrent chỉ có sẵn thông qua bản sao Tor của chúng tôi. Phát hành 1 (%(date)s) Đây là một tệp torrent bổ sung duy nhất. Nó không chứa bất kỳ thông tin mới nào, nhưng nó có một số dữ liệu có thể mất một thời gian để tính toán. Điều này làm cho nó tiện lợi để có, vì tải xuống torrent này thường nhanh hơn so với tính toán từ đầu. Đặc biệt, nó chứa các chỉ mục SQLite cho các tệp tar, để sử dụng với <a %(a_href)s>ratarmount</a>. Phát hành 2 phụ lục (%(date)s) Chúng tôi đã lấy tất cả các sách được thêm vào Z-Library giữa bản sao cuối của chúng tôi và tháng 8 năm 2022. Chúng tôi cũng đã quay lại và thu thập một số sách mà chúng tôi đã bỏ lỡ lần đầu tiên. Tất cả, bộ sưu tập mới này khoảng 24TB. Một lần nữa, bộ sưu tập này đã được loại bỏ trùng lặp với Library Genesis, vì đã có các torrent có sẵn cho bộ sưu tập đó. Dữ liệu được tổ chức tương tự như bản phát hành đầu tiên. Có một tệp dump MySQL “.sql.gz” của dữ liệu số, cũng bao gồm tất cả dữ liệu số từ bản phát hành đầu tiên, do đó thay thế nó. Chúng tôi cũng đã thêm một số cột mới: Chúng tôi đã đề cập điều này lần trước, nhưng chỉ để làm rõ: “filename” và “md5” là các thuộc tính thực tế của tệp, trong khi “filename_reported” và “md5_reported” là những gì chúng tôi thu thập từ Z-Library. Đôi khi hai thông tin này không khớp nhau, vì vậy chúng tôi đã bao gồm cả hai. Đối với bản phát hành này, chúng tôi đã thay đổi collation thành “utf8mb4_unicode_ci”, điều này sẽ tương thích với các phiên bản cũ hơn của MySQL. Các tệp dữ liệu tương tự như lần trước, mặc dù chúng lớn hơn nhiều. Chúng tôi đơn giản không thể tạo ra hàng tấn tệp torrent nhỏ hơn. “pilimi-zlib2-0-14679999-extra.torrent” chứa tất cả các tệp mà chúng tôi đã bỏ lỡ trong bản phát hành trước, trong khi các torrent khác đều là các phạm vi ID mới.  <strong>Cập nhật %(date)s:</strong> Chúng tôi đã làm hầu hết các torrent của mình quá lớn, khiến các ứng dụng torrent gặp khó khăn. Chúng tôi đã gỡ bỏ chúng và phát hành các torrent mới. <strong>Cập nhật %(date)s:</strong> Vẫn còn quá nhiều tệp, vì vậy chúng tôi đã gói chúng trong các tệp tar và phát hành các torrent mới một lần nữa. %(key)s: liệu tệp này đã có trong Library Genesis, trong bộ sưu tập phi hư cấu hoặc hư cấu (được khớp bằng md5). %(key)s: tệp này nằm trong torrent nào. %(key)s: được đặt khi chúng tôi không thể tải xuống cuốn sách. Phát hành 2 (%(date)s) Các phát hành Zlib (trang mô tả gốc) Tên miền Tor Trang web chính Dữ liệu từ Z-Library Bộ sưu tập “Tiếng Trung” trong Z-Library dường như giống với bộ sưu tập DuXiu của chúng tôi, nhưng có MD5 khác nhau. Chúng tôi loại trừ các tệp này khỏi torrents để tránh trùng lặp, nhưng vẫn hiển thị chúng trong chỉ mục tìm kiếm của chúng tôi. Dữ liệu số Bạn được thêm %(percentage)s%% lượt tải về nhanh, bởi vì bạn được mời bởi %(profile_link)s. Việc này được áp dụng cho toàn bộ thời hạn thành viên của bạn. Quyên góp Tham gia Được chọn Giảm giá tới tận %(percentage)s%% Alipay hỗ trợ thẻ tín dụng/ghi nợ quốc tế. Xem <a %(a_alipay)s>hướng dẫn này</a> để biết thêm thông tin. Gửi cho chúng tôi thẻ quà tặng Amazon.com bằng thẻ tín dụng/thẻ ghi nợ của bạn. Bạn có thể mua tiền điện tử bằng thẻ tín dụng/thẻ ghi nợ. WeChat (Weixin Pay) hỗ trợ thẻ tín dụng/thẻ ghi nợ quốc tế. Trong ứng dụng WeChat, hãy truy cập “Tôi => Dịch vụ => Ví => Thêm thẻ”. Nếu bạn không thấy nó, hãy kích hoạt nó bằng cách sử dụng “Tôi => Cài đặt => Chung => Công cụ => Weixin Pay => Bật”. (sử dụng khi gửi Ethereum từ Coinbase) đã chép! chép (khoản tiền thấp nhất có thể) (cảnh báo: yêu cầu lượng tiền tối thiểu cao) -%(percentage)s%% 12 tháng 1 tháng 24 tháng 3 tháng 48 tháng 6 tháng 96 tháng Xác định khoảng thời gian bạn muốn đăng ký. <div %(div_monthly_cost)s></div><div %(div_after)s>sau <span %(span_discount)s></span> chiết khấu</div><div %(div_total)s></div> <div %(div_duration)s></div> %(percentage)s%% trong 12 tháng trong 1 tháng trong 24 tháng trong 3 tháng cho 48 tháng trong 6 tháng cho 96 tháng %(monthly_cost)s / tháng liên hệ chúng tôi Máy chủ <strong>SFTP</strong> trực tiếp Đóng góp hoặc trao đổi ở cấp doanh nghiệp để lấy các bộ sưu tập mới (ví dụ: bản quét mới, bộ dữ liệu OCR). Quyền truy cập của chuyên gia <strong>không giới hạn</strong> lượt truy cập tốc độ cao <div %(div_question)s>Tôi có thể nâng cấp tư cách thành viên hoặc có nhiều tư cách thành viên không?</div> <div %(div_question)s>Tôi có thể quyên góp mà không trở thành thành viên không?</div> Chắc chắn rồi. Chúng tôi chấp nhận mọi khoản quyên góp bất kể số tiền tại địa chỉ Monero (XMR) này: %(address)s. <div %(div_question)s>Các khoảng giá mỗi tháng có ý nghĩa gì?</div> Bạn có thể đạt được mức thấp hơn của một khoảng giá bằng cách áp dụng tất cả các giảm giá, chẳng hạn như chọn một thời gian dài hơn một tháng. <div %(div_question)s>Các gói thành viên có tự động gia hạn không?</div> Các gói thành viên <strong>không</strong> tự động gia hạn. Bạn có thể tham gia trong thời gian dài hoặc ngắn tùy ý. <div %(div_question)s>Bạn chi tiêu các khoản quyên góp vào việc gì?</div> 100%% được sử dụng để bảo tồn và làm cho kiến thức và văn hóa của thế giới trở nên dễ tiếp cận. Hiện tại, chúng tôi chủ yếu chi tiêu vào máy chủ, lưu trữ và băng thông. Không có khoản tiền nào được chuyển đến bất kỳ thành viên nào trong nhóm. <div %(div_question)s>Tôi có thể quyên góp một số tiền lớn không?</div> Điều đó thật tuyệt vời! Để quyên góp trên vài nghìn đô la, vui lòng liên hệ trực tiếp với chúng tôi tại %(email)s. <div %(div_question)s>Bạn có phương thức thanh toán khác không?</div> Hiện tại thì chúng tôi không có. Rất nhiều người không muốn những kho lưu trữ như thế này tồn tại nên chúng tôi phải cẩn thận. Nếu bạn có thể giúp chúng tôi thiết lập các phương thức thanh toán khác (thuận tiện hơn) một cách an toàn, vui lòng liên hệ theo số %(email)s. Câu hỏi thường gặp về đóng góp Bạn vẫn còn một đóng góp chưa được hoàn tất <a %(a_donation)s>donation</a>. Xin vui lòng hoàn tất hoặc huỷ quyên góp kia trước khi tiếp tục với một quyên góp khác. <a %(a_all_donations)s>Hiển thị tất cả các quyên góp của tôi</a> Cho các quyên góp cao hơn $5000, vui lòng liên hệ trực tiếp với chúng tôi tại %(email)s. Chúng tôi hoan nghênh những đóng góp lớn từ các cá nhân hoặc tổ chức giàu có.  Xin lưu ý rằng mặc dù các gói thành viên trên trang này là “theo hàng tháng”, chúng là các khoản quyên góp một lần (không định kỳ). Xem <a %(faq)s>Nhữung câu hỏi thường gặp về quyên góp</a>. Kho Tàng Của Anna (Anna’s Archive) là một dự án phi lợi nhuận, mã nguồn mở và dữ liệu mở. Bằng việc quyên góp và trở thành thành viên, bạn hỗ trợ chúng mình hoạt động và phát triển. Gửi mọi thành viên thân mến: cảm ơn vì giúp chúng mình bước tiếp trên con đường này! ❤️ Để biết thêm thông tin, xem <a %(a_donate)s>Những câu hỏi thường gặp về quyên góp</a>. Để làm một thành viên, xin hãy <a%(a_login)s> Đăng nhập hoặc Đăng kí</a>. Cảm ơn vì sự đóng góp của bạn. $%(cost)s / hàng tháng Nếu bạn đã mắc lỗi trong quá trình thanh toán, chúng tôi không thể hoàn tiền, nhưng chúng tôi sẽ cố gắng khắc phục. Tìm trang “Tiền điện tử” trong ứng dụng hoặc trang web PayPal của bạn. Điều này thường nằm trong mục “Tài chính”. Đi tới trang “Bitcoin” trong ứng dụng hoặc trang web PayPal của bạn. Nhấn nút “Chuyển” %(transfer_icon)s, sau đó “Gửi”. Alipay Alipay / WeChat Thẻ quà tặng Amazon Thẻ quà tặng %(amazon)s Thẻ ngân hàng Thẻ ngân hàng (sử dụng ứng dụng) Binance Thẻ tín dụng/thẻ ghi nợ/Apple/Google (BMC) Cash App Thẻ tín dụng/thẻ ghi nợ Thẻ tín dụng/thẻ ghi nợ 2 Thẻ tín dụng/thẻ ghi nợ (dự phòng) Tiền crypto %(bitcoin_icon)s Thẻ / PayPal / Venmo PayPal (US) %(bitcoin_icon)s Paypal PayPal (loại bình thường) Pix (Brazil) Revolut (tạm thời không có) Wechat Chọn loại tiền điện tử bạn mong muốn: Đóng góp bằng thẻ quà tặng Amazon. <strong>QUAN TRỌNG:</strong> Tùy chọn này dành cho %(amazon)s. Nếu bạn muốn sử dụng một trang web Amazon khác, hãy chọn nó ở trên. <strong>QUAN TRỌNG:</strong> Chúng tôi chỉ hỗ trợ Amazon.com, không hỗ trợ các trang web Amazon khác. Ví dụ: .de, .co.uk, .ca KHÔNG được hỗ trợ. Vui lòng KHÔNG viết tin nhắn của riêng bạn. Nhập số tiền chính xác: %(amount)s Lưu ý rằng chúng tôi cần làm tròn số tiền lên một khoản được người bán lại chấp nhận (tối thiểu %(minimum)s). Quyên góp bằng thẻ tín dụng/thẻ ghi nợ, thông qua ứng dụng Alipay (rất dễ để cài đặt). Cài đặt ứng dụng Alipay từ <a %(a_app_store)s>Cửa Hàng Ứng dụng Apple</a> hoặc <a %(a_play_store)s>Cửa Hàng Google Play</a>. Đăng ký bằng số điện thoại của bạn. Không cần thêm thông tin cá nhân nào khác. <span %(style)s>1</span>Cài đặt ứng dụng Alipay Được hỗ trợ: Visa, MasterCard, JCB, Diners Club và Discover. Xem <a %(a_alipay)s>hướng dẫn này</a> để biết thêm thông tin. <span %(style)s>2</span>Thêm thẻ ngân hàng Với Binance, bạn mua Bitcoin bằng thẻ tín dụng/thẻ ghi nợ hoặc tài khoản ngân hàng, sau đó quyên góp Bitcoin đó cho chúng tôi. Bằng cách này, chúng tôi có thể duy trì bảo mật và ẩn danh khi nhận quyên góp của bạn. Binance có mặt ở hầu hết các quốc gia, và hỗ trợ hầu hết các ngân hàng và thẻ tín dụng/thẻ ghi nợ. Đây hiện là khuyến nghị chính của chúng tôi. Chúng tôi cảm kích bạn đã dành thời gian tìm hiểu cách quyên góp bằng phương pháp này, vì nó giúp chúng tôi rất nhiều. Đối với thẻ tín dụng, thẻ ghi nợ, Apple Pay và Google Pay, chúng tôi sử dụng “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Trong hệ thống của họ, một “cà phê” là tương đương với $5, vì vậy khoản quyên góp của bạn sẽ được làm tròn đến bội số gần nhất của 5. Đóng góp bằng Cash App. Nếu bạn có Cash App, đây là cách dễ nhất để đóng góp cho chúng tôi! Lưu ý rằng đối với các giao dịch dưới %(amount)s, Cash App có thể tính phí %(fee)s. Đối với %(amount)s trở lên, nó là miễn phí! Đóng góp thông qua thẻ tín dụng hoặc thẻ ghi nợ. Phương pháp này sử dụng nhà cung cấp tiền điện tử làm trung gian chuyển đổi. Điều này có thể hơi khó hiểu, vì vậy vui lòng chỉ sử dụng phương pháp này nếu các phương thức thanh toán khác không sử dụng được. Nó cũng không hoạt động ở một số quốc gia. Chúng tôi không thể hỗ trợ thẻ tín dụng/ghi nợ trực tiếp, vì các ngân hàng không muốn làm việc với chúng tôi. ☹ Tuy nhiên, có một số cách để sử dụng thẻ tín dụng/ghi nợ, thông qua các phương thức thanh toán khác: Với tiền điện tử, bạn có thể quyên góp thông qua BTC, ETH, XMR và SOL. Sử dụng tùy chọn này nếu bạn đã quen với tiền điện tử. Với tiền điện tử, bạn có thể quyên góp bằng BTC, ETH, XMR, v.v. Dịch vụ tiền điện tử nhanh Nếu bạn sử dụng tiền điện tử lần đầu tiên, chúng tôi gợi ý sử dụng %(options)s để mua và quyên góp Bitcoin (loại tiền điện tử đầu tiên và được sử dụng nhiều nhất). Lưu ý rằng đối với những khoản đóng góp nhỏ, phí thẻ tín dụng có thể loại bỏ khoản chiết khấu %(discount)s%% của chúng tôi, vì vậy chúng tôi khuyên bạn nên đăng ký dài hạn hơn. Quyên góp bằng thẻ tín dụng/thẻ ghi nợ, PayPal, hoặc Venmo. Bạn có thể chọn giữa các phương thức này trên trang kế tiếp. Google Pay và Apple Pay cũng có thể dùng được. Lưu ý rằng đối với những khoản quyên góp nhỏ thì phí sẽ cao, vì vậy chúng tôi khuyên bạn nên đăng ký lâu hơn. Để quyên góp bằng PayPal US, chúng tôi phải sử dụng PayPal Crypto, vì lựa chọn này cho phép chúng tôi bảo đảm sự ẩn danh. Chúng tôi đề cao việc bạn dành thời gian tìm hiểu phương thức quyên góp này, vì nó vô cùng hữu ích với chúng tôi. Đóng góp bằng PayPal. Quyên góp bằng tài khoản PayPal thường của bạn. Quyên góp bằng Revolut. Nếu bạn có Revolut, đây là cách dễ nhất để quyên góp! Cách chuyển khoản này chỉ cho phép tối đa %(amount)s. Hãy chọn một phương thức thanh toán khác hay chọn một thời lương đóng phí khác. Phương thức thanh toán này yêu cầu tối thiểu %(amount)s. Vui lòng chọn thời hạn hoặc phương thức thanh toán khác. Binance Coinbase Kraken Vui lòng chọn một phương thức thanh toán. "Sử dụng torrent”: tên người dùng hoặc lời nhắn của bạn trong tên tệp torrent <div %(div_months)s> mỗi 12 tháng thành viên một lần</div> Tên người dùng hoặc đề cập ẩn danh của bạn trong phần ghi công Được trải nghiệm sớm những tính năng mới Truy cập độc quyền Telegram với những cập nhật sau nền %(number)s lượt download nhanh mỗi ngày nếu bạn quyên góp vào tháng này! <a %(a_api)s>Truy cập JSON API</a> Một chỗ đứng trên tượng đài của những người bảo tồn tri thức và văn hóa Những lợi thế nêu trên, cùng với: Kiếm <strong>%(percentage)s%% thêm lượt tải xuống</strong> bằng cách <a %(a_refer)s>giới thiệu bạn bè</a>. Tài liệu SciDB <strong>không giới hạn</strong> không cần xác minh Khi hỏi về tài khoản hoặc quyên góp, hãy thêm ID tài khoản của bạn, ảnh chụp màn hình, biên lai, càng nhiều thông tin càng tốt. Chúng tôi chỉ kiểm tra email mỗi 1-2 tuần, vì vậy không bao gồm thông tin này sẽ làm chậm bất kỳ giải pháp nào. Để nhận được nhiều lượt tải xuống hơn nữa, hãy <a %(a_refer)s>giới thiệu bạn bè của bạn</a>! Chúng tôi là một nhóm tình nguyện viên nhỏ. Chúng tôi có thể mất 1-2 tuần để phản hồi. Lưu ý rằng tên hoặc hình ảnh tài khoản có thể trông lạ. Không cần phải lo lắng! Những tài khoản này được quản lý bởi các đối tác quyên góp của chúng tôi. Tài khoản của chúng tôi chưa bị hack. Quyên góp <span %(span_cost)s></span> <span %(span_label)s></span> trong 12 tháng "%(tier_name)s” trong 1 tháng "%(tier_name)s” trong 24 tháng "%(tier_name)s” trong 3 tháng "%(tier_name)s” cho 48 tháng “%(tier_name)s” trong 6 tháng "%(tier_name)s” cho 96 tháng “%(tier_name)s” Bạn vẫn có thể hủy quá trình quyên góp trong thủ tục thanh toán. Bấm nút quyên góp để xác nhận khoản tiền sẽ cho. <strong>Lưu ý quan trọng:</strong> Giá tiền điện tử có thể biến động mạnh, đôi khi thậm chí lên tới 20%% trong vài phút. Con số này vẫn thấp hơn mức phí mà chúng tôi phải gánh chịu với nhiều nhà cung cấp dịch vụ thanh toán, những người thường tính phí 50-60%% khi làm việc với một “tổ chức từ thiện trong bóng tối” như chúng tôi. <u>Nếu bạn gửi cho chúng tôi biên lai với giá ban đầu bạn đã thanh toán, chúng tôi vẫn sẽ ghi vào tài khoản của bạn cho tư cách thành viên đã chọn</u> (miễn là biên lai không quá vài giờ sau giao dịch). Chúng tôi thực sự đánh giá cao việc bạn sẵn sàng chịu đựng những thứ như thế này để hỗ trợ chúng tôi! ❤️ ❌ Đã xảy ra sự cố. Vui lòng tải lại trang và thử lại. <span %(span_circle)s>1</span>Mua Bitcoin trên Paypal <span %(span_circle)s>2</span>Chuyển Bitcoin đến địa chỉ của chúng tôi ✅ Đang chuyển hướng tới trang quyên góp… Quyên góp Vui lòng chờ ít nhất <span %(span_hours)s>24 giờ</span> (và làm mới trang này) trước khi liên hệ với chúng tôi. Nếu bạn muốn quyên góp (bất kỳ lượng tiền nào) mà không cần tư cách thành viên, vui lòng sử dụng địa chỉ Monero (XMR) này: %(address)s. Sau khi gửi thẻ quà tặng của bạn, hệ thống tự động của chúng tôi sẽ xác nhận trong vài phút. Nếu điều này không hoạt động, hãy thử gửi lại thẻ quà tặng của bạn (<a %(a_instr)s>hướng dẫn</a>). Nếu điều đó vẫn không hoạt động, vui lòng email cho chúng tôi và Anna sẽ xem xét thủ công (việc này có thể mất vài ngày), và hãy chắc chắn đề cập nếu bạn đã thử gửi lại. Ví dụ: Vui lòng sử dụng <a %(a_form)s>biểu mẫu chính thức của Amazon.com</a> để gửi cho chúng tôi một thẻ quà tặng trị giá %(amount)s đến địa chỉ email bên dưới. Email người nhận “Đến” trong biểu mẫu: Thẻ quà tặng Amazon Chúng tôi không thể chấp nhận các phương thức thẻ quà tặng khác, <strong>chỉ được gửi trực tiếp từ biểu mẫu chính thức trên Amazon.com</strong>. Chúng tôi không thể hoàn trả thẻ quà tặng của bạn nếu bạn không sử dụng biểu mẫu này. Chỉ sử dụng một lần. Duy nhất cho tài khoản của bạn, không chia sẻ. Đang chờ thẻ quà tặng… (làm mới trang để kiểm tra) Mở <a %(a_href)s>trang quyên góp mã QR</a>. Quét mã QR bằng ứng dụng Alipay, hoặc nhấn nút để mở ứng dụng Alipay. Xin vui lòng kiên nhẫn; trang có thể mất một lúc để tải bởi vì nó ở Trung Quốc. <span %(style)s>3</span>Thực hiện quyên góp (quét mã QR hoặc nhấn nút) Mua đồng PYUSD trên PayPal Mua Bitcoin (BTC) trên Cash App Mua thêm một chút (chúng tôi khuyên bạn nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại. Đi tới trang “Bitcoin” (BTC) trong Cash App. Chuyển cái Bitcoin đến địa chỉ của chúng tôi Đối với các khoản quyên góp nhỏ (dưới $25), bạn có thể cần sử dụng Rush hoặc Priority. Nhấp vào nút “Gửi bitcoin” để thực hiện “rút tiền”. Chuyển từ đô la sang BTC bằng cách nhấn vào biểu tượng %(icon)s. Nhập số lượng BTC bên dưới và nhấp vào “Gửi”. Xem <a %(help_video)s>video này</a> nếu bạn gặp khó khăn. Dịch vụ nhanh tiện lợi, nhưng tính phí cao hơn. Bạn có thể sử dụng dịch vụ này thay vì sàn giao dịch tiền điện tử nếu bạn muốn nhanh chóng thực hiện một khoản quyên góp lớn hơn và không ngại phí $5-10. Hãy chắc chắn bạn gửi đúng số tiền tiền điện tử được hiển thị trên trang quyên góp, không phải số tiền bằng $USD. Nếu không, phí sẽ bị trừ và chúng tôi không có thể tự động xử lý gói thành viên của bạn. Đôi khi việc xác nhận có thể mất đến 24 tiếng, vì vậy hãy chắc chắn làm mới trang này (ngay cả khi nó đã hết hạn). Hướng dẫn sử dụng thẻ tín dụng/thẻ ghi nợ Đóng góp thông qua trang thẻ tín dụng / thẻ ghi nợ của chúng tôi Một số bước đề cập đến ví tiền điện tử, nhưng đừng lo lắng, bạn không cần phải học bất cứ điều gì về tiền điện tử cho việc này. Hướng dẫn %(coin_name)s Quét mã QR này bằng ứng dụng Wallet Crypto của bạn để nhanh chóng điền vào chi tiết thanh toán Quét mã QR để thanh toán Chúng tôi chỉ hỗ trợ phiên bản tiêu chuẩn của tiền điện tử, không hỗ trợ các mạng hoặc phiên bản tiền điện tử lạ. Có thể cần đến một giờ để xác nhận giao dịch, tùy thuộc vào loại tiền. Quyên góp %(amount)s trên <a %(a_page)s>trang này</a>. Khoản quyên góp này đã hết hạn. Vui lòng hủy và tạo một cái mới. Nếu bạn đã thanh toán: Có, tôi đã gửi biên lai của mình qua email Nếu tỷ giá hối đoái tiền điện tử biến động trong quá trình giao dịch, hãy đảm bảo bao gồm biên lai hiển thị tỷ giá hối đoái ban đầu. Chúng tôi thực sự đánh giá cao việc bạn đã chịu khó sử dụng tiền điện tử, nó giúp ích cho chúng tôi rất nhiều! Có sự cố xảy ra.Xin tải lại trang và thử lại. <span %(span_circle)s>%(circle_number)s</span>Gửi biên lai cho chúng tôi qua email Nếu bạn gặp bất kỳ vấn đề nào, vui lòng liên hệ với chúng tôi tại %(email)s và cung cấp càng nhiều thông tin càng tốt (chẳng hạn như ảnh chụp màn hình). ✅ Cảm ơn sự đóng góp của bạn! Anna sẽ tự kích hoạt tư cách thành viên của bạn trong vòng vài ngày. Gửi biên lai hoặc ảnh chụp màn hình đến địa chỉ xác minh cá nhân của bạn: Khi bạn đã gửi biên lai qua email, hãy nhấp vào nút này để Anna có thể xem lại theo cách thủ công (việc này có thể mất vài ngày): Gửi biên lai hoặc ảnh chụp màn hình đến địa chỉ xác minh cá nhân của bạn. KHÔNG nên sử dụng địa chỉ email này cho khoản quyên góp PayPal của bạn. Hủy Có, hủy quyên góp Bạn có muốn hủy quyên góp? Đừng hủy nếu bạn đã trả trước. ❌ Có lỗi rồi. Xin hãy tải lại trang rồi thử lại nhé. Thêm khoản quyên góp mới Quyên góp của bạn đã được hủy bỏ. Ngày tháng: %(date)s Số định danh: %(id)s Đặt lại Trạng thái: <span %(span_label)s>%(label)s</span> Tổng cộng: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / tháng trong %(duration)s tháng, bao gồm %(discounts)s%% chiết khấu)</span> Tổng cộng: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / tháng cho %(duration)s tháng)</span> 1. Nhập email của bạn. 2. Chọn phương thức thanh toán của bạn. 3. Chọn lại phương thức thanh toán của bạn. 4. Chọn ví “Tự lưu trữ”. 5. Nhấp vào “Tôi xác nhận quyền sở hữu”. 6. Bạn sẽ nhận được biên lai qua email. Vui lòng gửi biên lai đó cho chúng tôi, và chúng tôi sẽ xác nhận quyên góp của bạn sớm nhất có thể. (bạn có thể muốn hủy và tạo một khoản quyên góp mới) Các hướng dẫn thanh toán hiện đã lỗi thời. Nếu bạn muốn thực hiện quyên góp khác, hãy sử dụng nút “Sắp xếp lại” ở trên. Bạn đã thanh toán rồi. Nếu bạn vẫn muốn xem lại hướng dẫn thanh toán, hãy nhấp vào đây: Hiển thị hướng dẫn thanh toán cũ Nếu trang quyên góp bị chặn, hãy thử một kết nối internet khác (ví dụ: VPN hoặc internet điện thoại). Thật không may, trang Alipay thường chỉ truy cập được từ <strong>Trung Quốc đại lục</strong>. Bạn có thể cần tạm thời tắt VPN của mình, hoặc sử dụng VPN đến Trung Quốc đại lục (hoặc đôi khi Hồng Kông cũng hoạt động). <span %(span_circle)s>1</span>Quyên góp trên Alipay Quyên góp tổng số tiền %(total)s bằng cách sử dụng <a %(a_account)s>tài khoản Alipay này</a> Hướng dẫn sử dụng Alipay <span %(span_circle)s>1</span>Chuyển sang một trong các tài khoản tiền điện tử của chúng tôi Đóng góp tổng số lượng %(total)s đến một trong những địa chỉ sau: Hướng dẫn về tiền điện tử Làm theo hướng dẫn để mua Bitcoin (BTC). Bạn chỉ cần mua số tiền muốn quyên góp, %(total)s. Nhập địa chỉ Bitcoin (BTC) của chúng tôi làm người nhận và làm theo hướng dẫn để gửi khoản quyên góp %(total)s của bạn: <span %(span_circle)s>1</span>Quyên góp trên Pix Quyên góp tổng số tiền %(total)s bằng <a %(a_account)s>tài khoản Pix này hướng dẫn pix <span %(span_circle)s>1</span>Quyên góp trên WeChat Quyên góp tổng số tiền %(total)s bằng cách sử dụng <a %(a_account)s>tài khoản WeChat này</a> Hướng dẫn WeChat Sử dụng bất kỳ dịch vụ “thẻ tín dụng sang Bitcoin” nhanh nào sau đây, chỉ cần vài phút: Địa chỉ BTC / Bitcoin (ví bên ngoài): Số lượng BTC / Bitcoin: Điền các chi tiết sau vào biểu mẫu: Nếu bất kỳ thông tin nào trong số này đã lỗi thời, xin vui lòng email cho chúng tôi để chúng tôi có thể biết. Vui lòng sử dụng <span %(underline)s>số lượng chính xác</span> này. Tổng chi phí của bạn có thể cao hơn do phí thẻ tín dụng. Đối với các khoản nhỏ, điều này có thể nhiều hơn mức giảm giá của chúng tôi, rất tiếc. (tối thiểu: %(minimum)s, không cần xác minh cho giao dịch đầu tiên) (tối thiểu: %(minimum)s) (tối thiểu: %(minimum)s) (tối thiểu: %(minimum)s, không cần xác minh cho giao dịch đầu tiên) (tối thiểu: %(minimum)s) (tối thiểu: %(minimum)s tùy thuộc vào quốc gia, không cần xác minh cho giao dịch đầu tiên) Làm theo hướng dẫn để mua đồng PYUSD (PayPal USD). Mua thêm một chút (chúng tôi khuyên bạn nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại. Đi đến trang “PYUSD” trong ứng dụng hoặc trang web PayPal của bạn. Nhấn nút “Chuyển” %(icon)s, sau đó “Gửi”. Cập nhật trạng thái Để đặt lại bộ đếm thời gian, chỉ cần đơn giản tạo một khoản quyên góp mới. Hãy chắc chắn sử dụng số lượng BTC dưới đây, <em>KHÔNG PHẢI</em> euro hoặc đô la, nếu không chúng tôi sẽ không nhận được số tiền chính xác và không thể tự động xác nhận tư cách thành viên của bạn. Mua Bitcoin (BTC) trên Revolut Mua thêm một chút (chúng tôi khuyên nên mua thêm %(more)s) so với số tiền bạn đang quyên góp (%(amount)s), để trang trải phí giao dịch. Bạn sẽ giữ lại bất kỳ số tiền nào còn lại. Đi tới trang “Crypto” trong Revolut để mua Bitcoin (BTC). Chuyển cái Bitcoin đến địa chỉ của chúng tôi Đối với các khoản quyên góp nhỏ (dưới $25), bạn có thể cần sử dụng Rush hoặc Priority. Nhấp vào nút “Gửi bitcoin” để thực hiện “rút tiền”. Chuyển từ euro sang BTC bằng cách nhấn vào biểu tượng %(icon)s. Nhập số lượng BTC bên dưới và nhấp vào “Gửi”. Xem <a %(help_video)s>video này</a> nếu bạn gặp khó khăn. Trạng thái: 1 2 Hướng dẫn từng bước Xem hướng dẫn từng bước dưới đây. Nếu không thì bạn có thể bị thoát khỏi tài khoản mà không phục hồi được! Hãy viết lại mã mật để đăng nhập nếu bạn chưa làm vậy: Cảm ơn vì đóng góp của bạn! Thời gian còn lại: Quyên góp Chuyển %(amount)s đến %(account)s Đang chờ xác nhận (làm mới trang để kiểm tra)… Đang chờ chuyển (làm mới trang để kiểm tra)… Trước đó Các lượt tải xuống nhanh trong 24 giờ qua được tính vào giới hạn hàng ngày. Các tải xuống từ Máy chủ Đối tác Nhanh được đánh dấu bởi %(icon)s. 18 giờ trước Chưa có tệp nào được tải về. Các tệp đã tải xuống không được hiển thị công khai. Tất cả thời gian đều theo giờ UTC. Tài liệu đã tải về Nếu bạn đã tải xuống một tệp với cả hai tải xuống nhanh và chậm, nó sẽ xuất hiện hai lần. Đừng lo lắng quá, có rất nhiều người tải xuống từ các trang web mà chúng tôi liên kết, và rất hiếm khi gặp rắc rối. Tuy nhiên, để an toàn, chúng tôi khuyên bạn nên sử dụng VPN (trả phí), hoặc <a %(a_tor)s>Tor</a> (miễn phí). Tôi đã tải xuống 1984 của George Orwell, liệu cảnh sát có đến gõ cửa nhà tôi không? Bạn là Anna! Anna là ai? Chúng tôi có một API JSON ổn định cho thành viên, để lấy URL tải xuống nhanh: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (tài liệu nằm trong JSON). Đối với các trường hợp sử dụng khác, chẳng hạn như lặp qua tất cả các tệp của chúng tôi, xây dựng tìm kiếm tùy chỉnh, v.v., chúng tôi khuyến nghị <a %(a_generate)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Dữ liệu thô có thể được khám phá thủ công <a %(a_explore)s>thông qua các tệp JSON</a>. Danh sách torrent thô của chúng tôi cũng có thể được tải xuống dưới dạng <a %(a_torrents)s>JSON</a>. Bạn có API không? Chúng tôi không lưu trữ bất kỳ tài liệu có bản quyền nào ở đây. Chúng tôi là một công cụ tìm kiếm, và do đó chỉ lập chỉ mục dữ liệu số đã có sẵn công khai. Khi tải xuống từ các nguồn bên ngoài này, chúng tôi khuyên bạn nên kiểm tra luật pháp tại khu vực của bạn về những gì được phép. Chúng tôi không chịu trách nhiệm về nội dung được lưu trữ bởi các trang web khác. Nếu bạn có khiếu nại về những gì bạn thấy ở đây, cách tốt nhất là liên hệ với trang web gốc. Chúng tôi thường xuyên cập nhật thay đổi của họ vào cơ sở dữ liệu của chúng tôi. Nếu bạn thực sự nghĩ rằng bạn có một khiếu nại DMCA hợp lệ mà chúng tôi nên phản hồi, vui lòng điền vào <a %(a_copyright)s>mẫu khiếu nại DMCA / Bản quyền</a>. Chúng tôi coi trọng khiếu nại của bạn và sẽ phản hồi sớm nhất có thể. Làm thế nào để báo cáo vi phạm bản quyền? Dưới đây là một số cuốn sách có ý nghĩa đặc biệt đối với thế giới thư viện bóng và bảo tồn kỹ thuật số: Những cuốn sách yêu thích của bạn là gì? Chúng tôi cũng muốn nhắc nhở mọi người rằng tất cả mã và dữ liệu của chúng tôi hoàn toàn là mã nguồn mở. Điều này là duy nhất đối với các dự án như của chúng tôi — chúng tôi không biết bất kỳ dự án nào khác có danh mục khổng lồ tương tự mà cũng hoàn toàn là mã nguồn mở. Chúng tôi rất hoan nghênh bất kỳ ai nghĩ rằng chúng tôi điều hành dự án kém để lấy mã và dữ liệu của chúng tôi và thiết lập thư viện bóng của riêng họ! Chúng tôi không nói điều này vì ác ý hay gì đó — chúng tôi thực sự nghĩ rằng điều này sẽ tuyệt vời vì nó sẽ nâng cao tiêu chuẩn cho mọi người và bảo tồn tốt hơn di sản của nhân loại. Tôi ghét cách bạn điều hành dự án này! Chúng tôi rất mong mọi người thiết lập <a %(a_mirrors)s>các bản sao</a>, và chúng tôi sẽ hỗ trợ tài chính cho việc này. Bằng cách nào để tôi có thể giúp? Chúng tôi thực sự có. Nguồn cảm hứng của chúng tôi cho việc thu thập dữ liệu số là mục tiêu của Aaron Swartz về “một trang web cho mỗi cuốn sách từng được xuất bản”, mà anh ấy đã tạo ra <a %(a_openlib)s>Open Library</a>. Dự án đó đã làm rất tốt, nhưng vị trí độc đáo của chúng tôi cho phép chúng tôi có được dữ liệu số mà họ không thể có. Một nguồn cảm hứng khác là mong muốn của chúng tôi biết <a %(a_blog)s>có bao nhiêu cuốn sách trên thế giới</a>, để chúng tôi có thể tính toán có bao nhiêu cuốn sách chúng tôi còn phải cứu. Bạn có thu thập dữ liệu số không? Lưu ý rằng mhut.org chặn một số dải IP nhất định, vì vậy có thể cần sử dụng VPN. <strong>Android:</strong> Nhấp vào menu ba chấm ở góc trên bên phải, và chọn “Thêm vào Màn hình chính”. <strong>iOS:</strong> Nhấn nút “Chia sẻ” ở dưới cùng, và chọn “Thêm vào Màn hình chính”. Chúng tôi không có ứng dụng di động chính thức, nhưng bạn có thể cài đặt trang web này như một ứng dụng. Bạn có ứng dụng di động không? Xin vui lòng gửi chúng đến <a %(a_archive)s>Internet Archive</a>. Họ sẽ bảo quản chúng một cách đúng đắn. Làm thế nào để tôi quyên góp sách hoặc các tài liệu vật lý khác? Làm thế nào để yêu cầu sách? <a %(a_blog)s>Blog của Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — cập nhật thường xuyên <a %(a_software)s>Phần mềm của Anna</a> — mã nguồn mở của chúng tôi <a %(a_datasets)s>Datasets</a> — về dữ liệu <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — các tên miền thay thế Có thêm tài nguyên nào về Anna’s Archive không? <a %(a_translate)s>Dịch trên Phần mềm của Anna</a> — hệ thống dịch thuật của chúng tôi <a %(a_wikipedia)s>Wikipedia</a> — thêm về chúng tôi (vui lòng hãy giúp cập nhật trang này, hoặc tạo một trang cho ngôn ngữ của bạn!) Chọn các cài đặt bạn thích, để trống hộp tìm kiếm, nhấp vào “Tìm kiếm”, và sau đó đánh dấu trang bằng tính năng đánh dấu trang của trình duyệt của bạn. Làm thế nào để lưu cài đặt tìm kiếm của tôi? Chúng tôi hoan nghênh các nhà nghiên cứu bảo mật tìm kiếm các lỗ hổng trong hệ thống của chúng tôi. Chúng tôi là những người ủng hộ lớn của việc tiết lộ có trách nhiệm. Liên hệ với chúng tôi <a %(a_contact)s>tại đây</a>. Hiện tại chúng tôi không thể trao thưởng cho các lỗi bảo mật, ngoại trừ các lỗ hổng có <a %(a_link)s>tiềm năng làm tổn hại đến tính ẩn danh của chúng tôi</a>, với mức thưởng từ $10k-50k. Chúng tôi mong muốn mở rộng phạm vi trao thưởng cho các lỗi bảo mật trong tương lai! Xin lưu ý rằng các cuộc tấn công kỹ thuật xã hội không nằm trong phạm vi này. Nếu bạn quan tâm đến bảo mật tấn công và muốn giúp lưu trữ kiến thức và văn hóa của thế giới, hãy liên hệ với chúng tôi. Có nhiều cách bạn có thể giúp đỡ. Bạn có chương trình tiết lộ có trách nhiệm không? Chúng tôi thực sự không có đủ nguồn lực để cung cấp cho mọi người trên thế giới tải xuống tốc độ cao, dù chúng tôi rất muốn. Nếu có một nhà hảo tâm giàu có muốn hỗ trợ chúng tôi điều này, thì thật tuyệt vời, nhưng cho đến lúc đó, chúng tôi đang cố gắng hết sức. Chúng tôi là một dự án phi lợi nhuận mà chỉ có thể tự duy trì thông qua các khoản quyên góp. Đây là lý do tại sao chúng tôi triển khai hai hệ thống tải xuống miễn phí, với các đối tác của chúng tôi: các máy chủ chia sẻ với tốc độ tải xuống chậm, và các máy chủ nhanh hơn một chút với danh sách chờ (để giảm số lượng người tải xuống cùng một lúc). Chúng tôi cũng có <a %(a_verification)s>xác minh trình duyệt</a> cho các lượt tải xuống chậm, vì nếu không, các bot và trình thu thập dữ liệu sẽ lạm dụng chúng, làm cho mọi thứ chậm hơn đối với người dùng hợp pháp. Lưu ý rằng, khi sử dụng Trình duyệt Tor, bạn có thể cần điều chỉnh cài đặt bảo mật của mình. Ở mức thấp nhất của các tùy chọn, gọi là “Tiêu chuẩn”, thử thách Cloudflare turnstile thành công. Ở các tùy chọn cao hơn, gọi là “An toàn hơn” và “An toàn nhất”, thử thách thất bại. Đối với các tệp lớn, đôi khi tải xuống chậm có thể bị gián đoạn giữa chừng. Chúng tôi khuyên bạn nên sử dụng trình quản lý tải xuống (chẳng hạn như JDownloader) để tự động tiếp tục các lượt tải xuống lớn. Tại sao tải xuống chậm lại chậm như vậy? Câu hỏi thường gặp (FAQ) Sử dụng <a %(a_list)s>trình tạo danh sách torrent</a> để tạo danh sách các torrent cần seed nhất, trong giới hạn dung lượng lưu trữ của bạn. Có, xem trang <a %(a_llm)s>dữ liệu LLM</a>. Hầu hết các torrent chứa các tệp trực tiếp, có nghĩa là bạn có thể hướng dẫn các ứng dụng torrent chỉ tải xuống các tệp cần thiết. Để xác định tệp nào cần tải xuống, bạn có thể <a %(a_generate)s>tạo</a> dữ liệu số của chúng tôi, hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Thật không may, một số bộ sưu tập torrent chứa các tệp .zip hoặc .tar ở gốc, trong trường hợp đó bạn cần tải xuống toàn bộ torrent trước khi có thể chọn các tệp riêng lẻ. Chưa có công cụ dễ sử dụng nào để lọc torrent, nhưng chúng tôi hoan nghênh sự đóng góp. (Tuy nhiên, chúng tôi có <a %(a_ideas)s>một số ý tưởng</a> cho trường hợp sau.) Câu trả lời dài: Câu trả lời ngắn: không dễ dàng. Chúng tôi cố gắng giữ sự trùng lặp hoặc chồng chéo tối thiểu giữa các torrent trong danh sách này, nhưng điều này không phải lúc nào cũng đạt được, và phụ thuộc nhiều vào chính sách của các thư viện nguồn. Đối với các thư viện phát hành torrent của riêng họ, điều này nằm ngoài tầm kiểm soát của chúng tôi. Đối với các torrent do Anna’s Archive phát hành, chúng tôi chỉ loại bỏ trùng lặp dựa trên hash MD5, có nghĩa là các phiên bản khác nhau của cùng một cuốn sách sẽ không bị loại bỏ trùng lặp. Có. Thực ra đây là các tệp PDF và EPUB, chúng chỉ không có phần mở rộng trong nhiều torrents của chúng tôi. Có hai nơi bạn có thể tìm thấy dữ liệu số cho các tệp torrent, bao gồm các loại tệp/phần mở rộng: 1. Mỗi bộ sưu tập hoặc phát hành có dữ liệu số riêng. Ví dụ, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> có cơ sở dữ liệu dữ liệu số tương ứng được lưu trữ trên trang web Libgen.rs. Chúng tôi thường liên kết đến các tài nguyên dữ liệu số liên quan từ <a %(a_datasets)s>trang tập dữ liệu</a> của mỗi bộ sưu tập. 2. Chúng tôi khuyến nghị <a %(a_generate)s>tạo</a> hoặc <a %(a_download)s>tải xuống</a> cơ sở dữ liệu ElasticSearch và MariaDB của chúng tôi. Chúng chứa một ánh xạ cho mỗi bản ghi trong Anna’s Archive đến các tệp torrent tương ứng của nó (nếu có), dưới “torrent_paths” trong ElasticSearch JSON. Một số ứng dụng torrent không hỗ trợ kích thước mảnh lớn, điều mà nhiều torrent của chúng tôi có (đối với các torrent mới hơn, chúng tôi không làm điều này nữa — mặc dù nó hợp lệ theo thông số kỹ thuật!). Vì vậy, hãy thử một ứng dụng khác nếu bạn gặp phải vấn đề này, hoặc phàn nàn với các nhà phát hành ứng dụng torrent của bạn. Tôi muốn giúp chia sẻ, nhưng tôi không có nhiều dung lượng đĩa. Các torrent quá chậm; tôi có thể tải dữ liệu trực tiếp từ bạn không? Tôi có thể chỉ tải xuống một phần của các tệp, như chỉ một ngôn ngữ hoặc chủ đề cụ thể không? Bạn xử lý các tệp trùng lặp trong các torrent như thế nào? Tôi có thể lấy danh sách torrent dưới dạng JSON không? Tôi không thấy các tệp PDF hoặc EPUB trong torrents, chỉ có các tệp nhị phân? Tôi phải làm gì? Tại sao ứng dụng torrent của tôi không thể mở một số tệp torrent / liên kết magnet của bạn? Câu hỏi thường gặp về Torrents Làm thế nào để tôi tải lên sách mới? Vui lòng xem <a %(a_href)s>dự án tuyệt vời này</a>. Bạn có sử dụng công cụ giám sát thời gian hoạt động không? Lưu trữ của Anna là gì? Trở thành một thành viên để có thể tải xuống nhanh hơn. Chúng tôi hiện hỗ trợ thẻ quà tặng Amazon, thẻ tín dụng và thẻ ghi nợ, tiền điện tử, Alipay và WeChat. Bạn đã hết lượt tải xuống nhanh hôm nay. Truy cập Số lượt tải xuống hàng giờ trong 30 ngày qua. Trung bình hàng giờ: %(hourly)s. Trung bình hàng ngày: %(daily)s. Chúng tôi làm việc với các đối tác để giúp mọi người có thể truy cập các bộ sưu tập của mình một cách dễ dàng và miễn phí. Chúng tôi tin rằng mọi người đều có quyền sở hữu được trí tuệ tập thể của nhân loại. Và <a %(a_search)s>không gây thiệt hại cho tác giả</a>. Các datasets được sử dụng trong Anna’s Archive hoàn toàn mở, và có thể được sao chép hàng loạt bằng cách sử dụng torrents. <a %(a_datasets)s>Tìm hiểu thêm…</a> Lưu trữ dài hạn Cơ sở dữ liệu đầy đủ Tìm kiếm Sách, bài báo, tạp chí, truyện tranh, hồ sơ thư viện, dữ liệu số, … Tất cả <a %(a_code)s>mã</a> và <a %(a_datasets)s>dữ liệu</a> của chúng tôi đều hoàn toàn mã nguồn mở. <span %(span_anna)s>Lưu trữ của Anna</span> là một dự án phi lợi nhuận với hai mục tiêu: <li><strong>Bảo tồn:</strong> Sao lưu tất cả kiến thức và văn hóa của nhân loại.</li><li><strong>Truy cập:</strong> Làm cho kiến thức và văn hóa này có sẵn cho bất kỳ ai trên thế giới.</li> Chúng tôi có bộ sưu tập dữ liệu văn bản chất lượng cao lớn nhất thế giới. <a %(a_llm)s>Tìm hiểu thêm…</a> Dữ liệu đào tạo LLM 🪩 Gương: kêu gọi tình nguyện viên Nếu bạn điều hành một bộ xử lý thanh toán ẩn danh có rủi ro cao, vui lòng liên hệ với chúng tôi. Chúng tôi cũng đang tìm kiếm những người muốn đặt quảng cáo nhỏ tinh tế. Tất cả số tiền thu được sẽ được sử dụng cho các nỗ lực bảo tồn của chúng tôi. Sự bảo tồn Chúng tôi ước tính rằng chúng tôi đã bảo tồn khoảng <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% sách trên thế giới</a>. Chúng tôi bảo tồn sách, báo, truyện tranh, tạp chí, v.v. bằng cách đưa những tài liệu này từ nhiều <a href="https://en.wikipedia.org/wiki/Shadow_library">thư viện chìm</a>, thư viện chính thức và các bộ sưu tập khác cùng nhau ở một nơi. Tất cả dữ liệu này được lưu giữ vĩnh viễn bằng cách giúp dễ dàng sao chép hàng loạt — sử dụng torrent — tạo ra nhiều bản sao trên khắp thế giới. Một số thư viện chìm đã tự thực hiện việc này (ví dụ: Sci-Hub, Library Genesis), trong khi Anna's Archive “giải phóng” các thư viện khác không cung cấp phân phối hàng loạt như là Z-Library hoặc không phải là thư viện chìm (ví dụ: Internet Archive, DuXiu). Sự phân phối rộng rãi này, kết hợp với mã nguồn mở, giúp trang web của chúng tôi có khả năng chống lại việc gỡ bỏ và đảm bảo việc bảo tồn lâu dài kiến thức và văn hóa của nhân loại. Tìm hiểu thêm về <a href="/datasets">bộ dữ liệu của chúng tôi</a>. Nếu bạn là <a %(a_member)s>thành viên</a>, không cần xác minh trình duyệt. 🧬&nbsp;SciDB là sự tiếp nối của Sci-Hub. SciDB Mở DOI Sci-Hub đã <a %(a_paused)s>tạm dừng</a> việc tải lên các bài báo mới. Truy cập trực tiếp vào %(count)s các bài báo học thuật 🧬&nbsp;SciDB là sự tiếp nối của Sci-Hub, với giao diện quen thuộc và xem trực tiếp các tệp PDF. Nhập DOI của bạn để xem. Chúng tôi có toàn bộ sưu tập của Sci-Hub, cũng như các tài liệu mới. Hầu hết có thể được xem trực tiếp với giao diện quen thuộc, tương tự như Sci-Hub. Một số có thể được tải xuống thông qua các nguồn bên ngoài, trong trường hợp đó chúng tôi sẽ hiển thị liên kết đến những nguồn đó. Bạn có thể giúp đỡ rất nhiều bằng cách seed torrents. <a %(a_torrents)s>Tìm hiểu thêm…</a> >%(count)s seeders <%(count)s seeders %(count_min)s–%(count_max)s seeders 🤝 Tìm kiếm tình nguyện viên Là một dự án phi lợi nhuận, mã nguồn mở, chúng tôi luôn tìm kiếm những người có thể giúp đỡ. Tải xuống IPFS Danh sách bởi %(by)s, được tạo <span %(span_time)s>%(time)s</span> Làm Có một sự cố đã xảy ra. Xin hãy thử lại. Đã lưu. Hãy tải lại trang. Danh sách trống. Thay đổi Thêm hoặc xóa khỏi danh sách này bằng cách tìm tệp và mở tab “Danh sách”. Danh sách Chúng tôi có thể giúp như thế nào Loại bỏ trùng lặp Trích xuất văn bản và dữ liệu số OCR Chúng tôi có thể cung cấp quyền truy cập tốc độ cao vào toàn bộ bộ sưu tập hiện tại, cũng như các bộ sưu tập chưa được công bố. Đây là quyền truy cập cấp doanh nghiệp mà chúng tôi có thể cung cấp cho các khoản quyên góp trong khoảng hàng chục nghìn USD. Chúng tôi cũng sẵn sàng trao đổi điều này để lấy các bộ sưu tập chất lượng cao mà chúng tôi chưa có. Chúng tôi có thể hoàn tiền cho bạn nếu bạn có thể cung cấp cho chúng tôi sự phong phú của dữ liệu của chúng tôi, chẳng hạn như: Hỗ trợ lưu trữ lâu dài kiến thức của con người, đồng thời nhận được dữ liệu tốt hơn cho mô hình của bạn! <a %(a_contact)s>Liên hệ với chúng tôi</a> để thảo luận về cách chúng ta có thể hợp tác với nhau. Người ta hiểu rõ rằng các LLM phát triển mạnh nhờ dữ liệu chất lượng cao. Chúng tôi có bộ sưu tập sách, bài báo, tạp chí, v.v. lớn nhất thế giới, đây là một số nguồn văn bản chất lượng cao nhất. Dữ liệu LLM Quy mô và phạm vi độc đáo Bộ sưu tập của chúng tôi chứa hơn một trăm triệu tệp, bao gồm các tạp chí học thuật, sách giáo khoa và tạp chí. Chúng tôi đạt được quy mô này bằng cách kết hợp các kho lưu trữ lớn hiện có. Một số bộ sưu tập nguồn của chúng tôi đã có sẵn với số lượng lớn (Sci-Hub và các phần của Libgen). Các nguồn khác chúng tôi tự giải phóng. <a %(a_datasets)s>Datasets</a> hiển thị tổng quan đầy đủ. Bộ sưu tập của chúng tôi bao gồm hàng triệu sách, bài báo và tạp chí từ trước thời đại sách điện tử. Phần lớn bộ sưu tập này đã được OCR và đã có ít sự trùng lặp nội bộ. Tiếp tục Nếu bạn mất mã, vui lòng <a %(a_contact)s>liên hệ với chúng tôi</a> và cung cấp càng nhiều thông tin càng tốt. Bạn có thể phải tạm thời tạo một tài khoản mới để liên hệ với chúng tôi. Vui lòng <a %(a_account)s>đăng nhập</a> để xem trang này.</a> Để ngăn chặn các chương trình spam tạo nhiều tài khoản, trước tiên chúng tôi cần xác minh trình duyệt của bạn. Nếu bạn bị mắc kẹt trong vòng lặp vô hạn, chúng tôi khuyên bạn nên tải <a %(a_privacypass)s>Privacy Pass</a>. Nó cũng có thể giúp tắt các trình chặn quảng cáo và các tiện ích mở rộng trình duyệt khác. Đăng nhập / Đăng ký Anna’s Archive đang tạm thời ngừng hoạt động để bảo trì. Vui lòng quay lại sau một giờ. Tác giả thay thế Mô tả thay thế Phiên bản thay thế Phần mở rộng thay thế Tên tệp thay thế Nhà xuất bản thay thế Tiêu đề thay thế ngày mở mã nguồn Đọc thêm… mô tả Tìm kiếm Lưu Trữ của Anna cho số SSNO CADAL Tìm kiếm Lưu Trữ của Anna cho số SSID DuXiu Tìm kiếm Lưu Trữ của Anna cho số DXID DuXiu Tìm kiếm Anna’s Archive cho ISBN Tìm kiếm Anna’s Archive cho số OCLC (WorldCat) Tìm kiếm Anna’s Archive cho ID Open Library Trình xem trực tuyến của Lưu Trữ của Anna %(count)s trang bị ảnh hưởng Sau khi tải xuống: Phiên bản tốt hơn của tệp này có thể tìm thấy tại %(link)s Tải xuống torrent hàng loạt bộ sưu tập Sử dụng công cụ trực tuyến để chuyển đổi giữa các định dạng. Công cụ chuyển đổi được khuyến nghị: %(links)s Đối với các tệp lớn, chúng tôi khuyên bạn nên sử dụng trình quản lý tải xuống để tránh gián đoạn. Trình quản lý tải xuống được khuyến nghị: %(links)s Chỉ mục Sách điện tử EBSCOhost (chỉ dành cho chuyên gia) (cũng nhấp vào “NHẬN” ở trên cùng) (nhấp vào “NHẬN” ở trên cùng) Tải xuống bên ngoài Bạn còn %(remaining)s lượt hôm nay. Cảm ơn vì đã là thành viên! ❤️ Bạn đã hết lượt tải xuống nhanh cho hôm nay. Bạn đã tải xuống tệp này gần đây. Liên kết vẫn còn hiệu lực trong một khoảng thời gian. Trở thành <a %(a_membership)s>thành viên</a> để hỗ trợ việc bảo quản lâu dài những sách, bài nghiên cứu, v.v. Để thể hiện lòng biết ơn của chúng tôi đối với sự hỗ trợ của bạn, bạn sẽ có được bản tải xuống nhanh. ❤️ 🚀 Các bản tải xuống nhanh 🐢 Các bản tải xuống chậm Mượn từ Internet Archive Cổng IPFS #%(num)d (bạn có thể cần phải thử nhiều lần với IPFS) Libgen.li Libgen.rs Viễn tưởng Libgen.rs Phi hư cấu quảng cáo của họ được biết là chứa phần mềm độc hại, vì vậy nên sử dụng trình chặn quảng cáo hoặc không nhấp vào những quảng cáo Amazon’s “Send to Kindle” djazz’s “Send to Kobo/Kindle” MagzDB ManualsLib Nexus/STC (Các tệp Nexus/STC có thể không đáng tin cậy để tải xuống) Không tìm thấy nội dung tải xuống nào. Tất cả các tùy chọn tải xuống đều có cùng một tệp và thường sẽ an toàn khi sử dụng. Tuy vật, hãy luôn thận trọng khi tải xuống tệp từ Internet, đặc biệt là từ các trang bên ngoài Anna's Archive. Ví dụ: hãy đảm bảo cập nhật thiết bị của bạn. (không chuyển hướng) Mở trong trình xem của chúng tôi (mở trong trình xem) Tùy chọn #%(num)d: %(link)s %(extra)s Tìm bản ghi gốc trong CADAL Tìm kiếm thủ công trên DuXiu Tìm bản ghi gốc trong ISBNdb Tìm ghi chép gốc trong WorldCat Tìm bản ghi gốc trong Open Library Tìm kiếm các cơ sở dữ liệu khác cho ISBN (chỉ dành cho người khuyết tật không thể in) PubMed Bạn sẽ cần một trình đọc ebook hoặc PDF để mở tệp, tùy thuộc vào định dạng tệp. Trình đọc ebook được khuyến nghị: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (DOI được liên kết với có thể không có sẵn trong Sci-Hub) Bạn có thể gửi cả tệp PDF và EPUB đến Kindle hoặc Kobo eReader của mình. Công cụ được khuyến nghị: %(links)s Xem thêm thông tin trong <a %(a_slow)s>Câu hỏi thường gặp (FAQ)</a>. Hỗ trợ tác giả và thư viện Nếu bạn thích sách này và có khả năng để mua nó, hãy cân nhắc mua bản gốc hoặc hỗ trợ trực tiếp cho các tác giả. Nếu sách này có sẵn tại thư viện địa phương của bạn, hãy cân nhắc mượn miễn phí ở đó. Tải xuống từ Máy chủ Đối tác tạm thời không khả dụng cho tệp này. torrent Từ các đối tác đáng tin cậy. Z-Library Z-Library trên Tor (cần trình duyệt Tor) hiển thị tải xuống bên ngoài <span class="font-bold">❌ Tệp này có thể có vấn đề và đã bị ẩn khỏi thư viện nguồn.</span> Đôi khi điều này là do yêu cầu của chủ bản quyền, đôi khi là do có sẵn giải pháp thay thế tốt hơn, nhưng đôi khi đó là do vấn đề với chính tập tin đó. Tải xuống vẫn có thể ổn nhưng chúng tôi khuyên bạn trước tiên nên tìm kiếm tệp thay thế. Thêm chi tiết: Nếu bạn vẫn muốn tải xuống tệp này, hãy nhớ chỉ sử dụng phần mềm đã cập nhật, đáng tin cậy để mở tệp. bình luận của dữ liệu số AA: Tìm kiếm Anna’s Archive cho “%(name)s” Khám phá Mã: Xem trong Khám phá Mã “%(name)s” Liên kết URL: Trang web: Nếu bạn có tệp này và nó chưa có trong Lưu trữ của Anna, hãy <a %(a_request)s>tải lên nó</a>. Internet Archive Mượn dạng số có kiểm soát (CDL) tệp “%(id)s” Đây là bản ghi của một tệp từ Internet Archive, không phải là tệp có thể tải xuống trực tiếp. Bạn có thể thử mượn sách (liên kết bên dưới), hoặc sử dụng URL này khi <a %(a_request)s>đang yêu cầu tệp</a>. Cải thiện dữ liệu số CADAL SSNO bản ghi dữ liệu số %(id)s Đây là một bản ghi dữ liệu số, không phải là tệp tải xuống được. Bạn có thể sử dụng URL này khi <a %(a_request)s>đang yêu cầu tệp</a>. DuXiu SSID bản ghi dữ liệu số %(id)s Bản ghi dữ liệu số ISBNdb %(id)s Bản ghi dữ liệu số MagzDB ID %(id)s Bản ghi dữ liệu số Nexus/STC ID %(id)s Số OCLC (WorldCat) bản ghi dữ liệu số %(id)s Mở bản ghi dữ liệu số của Thư viện %(id)s Tệp Sci-Hub “%(id)s” Không tìm thấy "%(md5_input)s" không thể được tìm thấy trong kho dữ liệu của chúng tôi l. Thêm bình luận (%(count)s) Bạn có thể tìm được md5 từ URL, vân vân MD5 của phiên bản tốt hơn của tệp này (nếu có). Điền vào đây nếu có tệp khác tương tự với tệp này (cùng phiên bản, cùng định dạng tệp nếu bạn có thể tìm thấy), mà mọi người nên sử dụng thay vì tệp này. Nếu bạn biết phiên bản tốt hơn của tệp này ngoài Anna’s Archive, thì xin vui lòng <a %(a_upload)s>tải lên</a>. Đã xảy ra lỗi. Xin vui lòng tải lại trang và thử lại. Bạn đã để lại bình luận. Có thể mất một phút để cho nó hiển thị. Xin vui lòng sử dụng <a %(a_copyright)s>biểu mẫu khiếu nại DMCA / Bản quyền</a>. Mô tả vấn đề (bắt buộc) Nếu tệp này có chất lượng tốt, bạn có thể thảo luận bất cứ điều gì về nó tại đây! Nếu không, vui lòng sử dụng nút “Báo cáo vấn đề tệp”. Chất lượng tệp tốt (%(count)s) Chất lượng tệp Tìm hiểu cách <a %(a_metadata)s>cải thiện dữ liệu số</a> cho tệp này. Mô tả vấn đề Xin vui lòng <a %(a_login)s>đăng nhập</a>. Tôi rất thích cuốn sách này! Giúp đỡ cộng đồng bằng cách báo cáo chất lượng của tệp này! 🙌 Đã xảy ra lỗi. Vui lòng tải lại trang và thử lại. Báo cáo vấn đề tệp (%(count)s) Cảm ơn bạn đã gửi báo cáo. Báo cáo sẽ được hiển thị trên trang này và sẽ cũng được xem xét thủ công bởi Anna (cho đến khi chúng tôi có hệ thống kiểm duyệt phù hợp). Để lại bình luận Gửi báo cáo Cái gì sai với tệp này? Mượn (%(count)s) Các bình luận (%(count)s) Tải xuống (%(count)s) Khám phá dữ liệu số (%(count)s) Danh sách (%(count)s) Thống kê (%(count)s) Để biết thông tin về tệp cụ thể này, hãy xem <a %(a_href)s>tệp JSON</a> của nó. Đây là một tệp được quản lý bởi thư viện <a %(a_ia)s>Mượn Kỹ Thuật Số Có Kiểm Soát (CDL) của IA</a> và được Anna’s Archive lập chỉ mục để tìm kiếm. Để biết thông tin về các datasets khác nhau mà chúng tôi đã biên soạn, hãy xem <a %(a_datasets)s>trang Datasets</a>. Dữ liệu số từ bản ghi liên kết Cải thiện dữ liệu số trên Open Library “file MD5” là một hàm băm được tính toán từ nội dung tệp, và khá độc đáo dựa trên nội dung đó. Tất cả các thư viện bóng mà chúng tôi đã lập chỉ mục ở đây chủ yếu sử dụng MD5 để xác định tệp. Một tệp có thể xuất hiện trong nhiều thư viện bón—g. Để biết thông tin về các datasets khác nhau mà chúng tôi đã biên soạn, hãy xem <a %(a_datasets)s>trang Datasets</a>. Báo cáo chất lượng tệp Tổng số lượt tải xuống: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Dữ liệu số Czech %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Sách %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Cảnh báo: nhiều bản ghi liên kết: Khi bạn xem một cuốn sách trên Anna’s Archive, bạn có thể thấy các trường khác nhau: tiêu đề, tác giả, nhà xuất bản, ấn bản, năm, mô tả, tên tệp, và nhiều hơn nữa. Tất cả những thông tin này được gọi là <em>dữ liệu số</em>. Vì chúng tôi kết hợp sách từ các <em>thư viện nguồn</em> khác nhau, chúng tôi hiển thị bất kỳ dữ liệu số nào có sẵn trong thư viện nguồn đó. Ví dụ, đối với một cuốn sách mà chúng tôi lấy từ Library Genesis, chúng tôi sẽ hiển thị tiêu đề từ cơ sở dữ liệu của Library Genesis. Đôi khi một cuốn sách có mặt trong <em>nhiều</em> thư viện nguồn, có thể có các trường dữ liệu số khác nhau. Trong trường hợp đó, chúng tôi đơn giản hiển thị phiên bản dài nhất của mỗi trường, vì phiên bản đó hy vọng chứa thông tin hữu ích nhất! Chúng tôi vẫn sẽ hiển thị các trường khác dưới phần mô tả, ví dụ như “tiêu đề thay thế” (nhưng chỉ khi chúng khác nhau). Chúng tôi cũng trích xuất <em>mã</em> như các định danh và phân loại từ thư viện nguồn. <em>Định danh</em> đại diện duy nhất cho một ấn bản cụ thể của một cuốn sách; ví dụ là ISBN, DOI, mã ID Open Library, mã ID Google Sách, hoặc mã ID Amazon. <em>Phân loại</em> nhóm các cuốn sách tương tự lại với nhau; ví dụ là Dewey Decimal (DCC), UDC, LCC, RVK, hoặc GOST. Đôi khi các mã này được liên kết rõ ràng trong các thư viện nguồn, và đôi khi chúng tôi có thể trích xuất chúng từ tên tệp hoặc mô tả (chủ yếu là ISBN và DOI). Chúng tôi có thể sử dụng các định danh để tìm các bản ghi trong <em>các bộ sưu tập chỉ có dữ liệu số</em>, như OpenLibrary, ISBNdb, hoặc WorldCat/OCLC. Có một <em>tab dữ liệu số</em> cụ thể trong công cụ tìm kiếm của chúng tôi nếu bạn muốn duyệt qua các bộ sưu tập đó. Chúng tôi sử dụng các bản ghi khớp để điền vào các trường dữ liệu số còn thiếu (ví dụ nếu thiếu tiêu đề), hoặc ví dụ như “tiêu đề thay thế” (nếu có tiêu đề hiện có). Để xem chính xác dữ liệu số của một cuốn sách đến từ đâu, hãy xem tab <em>“Chi tiết kỹ thuật”</em> trên trang sách. Nó có một liên kết đến JSON thô cho cuốn sách đó, với các chỉ dẫn đến JSON thô của các bản ghi gốc. Để biết thêm thông tin, hãy xem các trang sau: <a %(a_datasets)s>Các bộ dữ liệu</a>, <a %(a_search_metadata)s>Tìm kiếm (tab dữ liệu số)</a>, <a %(a_codes)s>Khám phá mã</a>, và <a %(a_example)s>Ví dụ JSON dữ liệu số</a>. Cuối cùng, tất cả dữ liệu số của chúng tôi có thể được <a %(a_generated)s>tạo ra</a> hoặc <a %(a_downloaded)s>tải xuống</a> dưới dạng cơ sở dữ liệu ElasticSearch và MariaDB. Giới thiệu Bạn có thể giúp bảo tồn sách bằng cách cải thiện dữ liệu số! Trước tiên, hãy đọc phần giới thiệu về dữ liệu số trên Anna’s Archive, sau đó học cách cải thiện dữ liệu số thông qua việc liên kết với Open Library, và nhận được tư cách thành viên miễn phí trên Anna’s Archive. Cải thiện dữ liệu số Vậy nếu bạn gặp một tệp có dữ liệu số xấu, bạn nên sửa nó như thế nào? Bạn có thể đi đến thư viện nguồn và làm theo các quy trình của nó để sửa dữ liệu số, nhưng phải làm gì nếu một tệp có mặt trong nhiều thư viện nguồn? Có một định danh được coi là đặc biệt trên Lưu Trữ của Anna. <strong>Trường annas_archive md5 trên Open Library luôn ghi đè tất cả các dữ liệu số khác!</strong> Hãy lùi lại một chút và tìm hiểu về Open Library. Open Library được thành lập vào năm 2006 bởi Aaron Swartz với mục tiêu “một trang web cho mỗi cuốn sách từng được xuất bản”. Nó giống như một Wikipedia cho dữ liệu số sách: mọi người đều có thể chỉnh sửa, nó được cấp phép tự do, và có thể tải xuống hàng loạt. Đây là cơ sở dữ liệu sách phù hợp nhất với sứ mệnh của chúng tôi — thực tế, Lưu Trữ của Anna đã được truyền cảm hứng từ tầm nhìn và cuộc đời của Aaron Swartz. Thay vì phát minh lại điều đó, chúng tôi quyết định hướng các tình nguyện viên của mình về phía Open Library. Nếu bạn thấy một cuốn sách có dữ liệu số không chính xác, bạn có thể giúp đỡ theo cách sau: Lưu ý rằng điều này chỉ áp dụng cho sách, không phải các bài viết học thuật hoặc các loại tệp khác. Đối với các loại tệp khác, chúng tôi vẫn khuyến nghị tìm thư viện nguồn. Có thể mất vài tuần để các thay đổi được bao gồm trong Lưu Trữ của Anna, vì chúng tôi cần tải xuống bản dữ liệu mới nhất của Open Library và tái tạo chỉ mục tìm kiếm của chúng tôi.  Đi đến <a %(a_openlib)s>trang web Open Library</a>. Tìm bản ghi sách chính xác. <strong>CẢNH BÁO:</strong> hãy chắc chắn chọn đúng <strong>ấn bản</strong>. Trong Open Library, bạn có “tác phẩm” và “ấn bản”. Một “tác phẩm” có thể là “Harry Potter và Hòn đá Phù thủy”. Một “ấn bản” có thể là: Ấn bản đầu tiên năm 1997 được xuất bản bởi Bloomsbery với 256 trang. Ấn bản bìa mềm năm 2003 được xuất bản bởi Raincoast Books với 223 trang. Bản dịch tiếng Ba Lan năm 2000 “Harry Potter I Kamie Filozoficzn” bởi Media Rodzina với 328 trang. Tất cả các ấn bản này đều có ISBN và nội dung khác nhau, vì vậy hãy chắc chắn chọn đúng ấn bản! Chỉnh sửa bản ghi (hoặc tạo mới nếu chưa có), và thêm càng nhiều thông tin hữu ích càng tốt! Bạn đã ở đây rồi, hãy làm cho bản ghi thật tuyệt vời. Dưới mục “ID Numbers” chọn “Anna’s Archive” và thêm MD5 của cuốn sách từ Anna’s Archive. Đây là chuỗi dài các chữ cái và số sau “/md5/” trong URL. Cố gắng tìm các tệp khác trong Anna’s Archive cũng khớp với bản ghi này, và thêm chúng vào. Trong tương lai, chúng ta có thể nhóm chúng lại như các bản sao trên trang tìm kiếm của Anna’s Archive. Khi bạn hoàn thành, hãy ghi lại URL mà bạn vừa cập nhật. Khi bạn đã cập nhật ít nhất 30 bản ghi với MD5 của Anna’s Archive, hãy gửi cho chúng tôi một <a %(a_contact)s>email</a> và gửi danh sách đó. Chúng tôi sẽ tặng bạn một thành viên miễn phí cho Anna’s Archive, để bạn có thể dễ dàng thực hiện công việc này (và như một lời cảm ơn vì sự giúp đỡ của bạn). Những chỉnh sửa này phải có chất lượng cao và thêm một lượng thông tin đáng kể, nếu không yêu cầu của bạn sẽ bị từ chối. Yêu cầu của bạn cũng sẽ bị từ chối nếu bất kỳ chỉnh sửa nào bị hoàn tác hoặc sửa đổi bởi các quản trị viên của Open Library. Liên kết Open Library Nếu bạn tham gia đáng kể vào việc phát triển và vận hành công việc của chúng tôi, chúng tôi có thể thảo luận về việc chia sẻ thêm doanh thu từ quyên góp với bạn, để bạn triển khai khi cần thiết. Chúng tôi sẽ chỉ trả tiền lưu trữ khi bạn đã thiết lập mọi thứ và chứng minh rằng bạn có thể giữ cho kho lưu trữ được cập nhật với các bản cập nhật. Điều này có nghĩa là bạn sẽ phải tự trả tiền cho 1-2 tháng đầu tiên. Thời gian của bạn sẽ không được bồi thường (và của chúng tôi cũng vậy), vì đây là công việc tình nguyện hoàn toàn. Chúng tôi sẵn sàng chi trả chi phí lưu trữ và VPN, ban đầu lên đến $200 mỗi tháng. Điều này đủ cho một máy chủ tìm kiếm cơ bản và một proxy được bảo vệ bởi DMCA. Chi phí lưu trữ Vui lòng <strong>không liên hệ với chúng tôi</strong> để xin phép, hoặc hỏi các câu hỏi cơ bản. Hành động nói lên nhiều hơn lời nói! Tất cả thông tin đều có sẵn, vì vậy hãy tiến hành thiết lập bản sao của bạn. Hãy thoải mái đăng vé hoặc yêu cầu hợp nhất lên Gitlab của chúng tôi khi bạn gặp sự cố. Chúng tôi có thể cần xây dựng một số tính năng cụ thể cho bản sao với bạn, chẳng hạn như đổi thương hiệu từ “Anna’s Archive” sang tên trang web của bạn, (ban đầu) vô hiệu hóa tài khoản người dùng, hoặc liên kết trở lại trang chính của chúng tôi từ các trang sách. Khi bạn đã có bản sao của mình hoạt động, vui lòng liên hệ với chúng tôi. Chúng tôi rất muốn xem xét bảo mật của bạn, và khi điều đó ổn định, chúng tôi sẽ liên kết đến bản sao của bạn và bắt đầu làm việc chặt chẽ hơn với bạn. Cảm ơn trước bất kỳ ai sẵn lòng đóng góp theo cách này! Đây không phải là việc dành cho những người yếu tim, nhưng nó sẽ củng cố sự tồn tại lâu dài của thư viện mở lớn nhất trong lịch sử loài người. Bắt đầu Để tăng cường khả năng phục hồi của Lưu trữ của Anna, chúng tôi đang tìm kiếm các tình nguyện viên để chạy các bản sao. Phiên bản của bạn được phân biệt rõ ràng là một bản sao, ví dụ: “Bob’s Archive, một bản sao của Anna’s Archive”. Bạn sẵn sàng chấp nhận những rủi ro liên quan đến công việc này, những rủi ro này là đáng kể. Bạn có hiểu biết sâu sắc về bảo mật hoạt động cần thiết. Nội dung của <a %(a_shadow)s>những</a> <a %(a_pirate)s>bài viết</a> này là hiển nhiên đối với bạn. Ban đầu, chúng tôi sẽ không cung cấp cho bạn quyền truy cập vào các lượt tải xuống từ máy chủ đối tác của chúng tôi, nhưng nếu mọi việc diễn ra tốt đẹp, chúng tôi có thể chia sẻ điều đó với bạn. Bạn chạy mã nguồn mở của Anna’s Archive, và bạn thường xuyên cập nhật cả mã và dữ liệu. Bạn sẵn sàng đóng góp vào <a %(a_codebase)s>mã nguồn</a> của chúng tôi — hợp tác với đội ngũ của chúng tôi — để thực hiện điều này. Chúng tôi đang tìm kiếm điều này: Các bản sao: kêu gọi tình nguyện viên Quyên góp lần nữa. Chưa có khoản quyên góp nào. <a %(a_donate)s>Thực hiện khoản quyên góp đầu tiên của tôi.</a> Những đóng góp sẽ được giữ riêng tư. Những đóng góp của tôi 📡 Để sao chép hàng loạt bộ sưu tập của chúng tôi, hãy xem các trang <a %(a_datasets)s>Datasets</a> và <a %(a_torrents)s>Torrents</a>. Lượt tải xuống từ địa chỉ IP của bạn trong 24 giờ qua: %(count)s. 🚀 Để tải xuống nhanh hơn và bỏ qua các kiểm tra trình duyệt, <a %(a_membership)s>hãy trở thành thành viên</a>. Tải xuống từ trang web đối tác Hãy tiếp tục duyệt Anna’s Archive trong một tab khác trong khi chờ đợi (nếu trình duyệt của bạn hỗ trợ làm mới các tab nền). Hãy chờ đợi để nhiều trang tải xuống cùng lúc (nhưng vui lòng chỉ tải xuống một tệp cùng lúc trên mỗi máy chủ). Khi bạn nhận được liên kết tải xuống, nó sẽ có hiệu lực trong vài giờ. Cảm ơn bạn đã chờ đợi, điều này giúp trang web có thể truy cập miễn phí cho mọi người! 😊 <a %(a_main)s>&lt; Tất cả các đường link tải xuống cho tệp này</a> ❌ Tải xuống chậm không có sẵn thông qua VPN của Cloudflare hoặc từ các địa chỉ IP của Cloudflare. ❌ Tải xuống chậm chỉ có sẵn thông qua trang web chính thức. Truy cập %(websites)s. <a %(a_download)s>📚 Tải xuống ngay bây giờ</a> Để mọi người có cơ hội tải xuống tệp miễn phí, bạn cần chờ trước khi có thể tải xuống tệp này. Vui lòng chờ <span %(span_countdown)s>%(wait_seconds)s</span> giây để tải xuống tệp này. Cảnh báo: đã có nhiều lượt tải xuống từ địa chỉ IP của bạn trong 24 giờ qua. Tốc độ tải xuống có thể chậm hơn bình thường. Nếu bạn đang sử dụng VPN, kết nối internet chia sẻ hoặc ISP của bạn chia sẻ IP, cảnh báo này có thể do điều đó. Lưu ❌ Đã xảy ra lỗi. Vui lòng thử lại. ✅ Đã lưu. Vui lòng tải lại trang. Thay đổi tên hiển thị của bạn. Mã định danh của bạn (phần sau “#”) không thể thay đổi. Đã tạo hồ sơ <span %(span_time)s>%(time)s</span> Thay đổi Danh sách Tạo danh sách mới bằng cách tìm tệp và mở tab “Danh sách”. Chưa có danh sách nào Không tìm thấy hồ sơ. Hồ sơ Hiện tại, chúng tôi không thể đáp ứng yêu cầu sách. Đừng gửi email các yêu cầu sách của bạn cho chúng tôi. Xin vui lòng gửi yêu cầu của bạn trên các diễn đàn Z-Library hoặc Libgen. Ghi chép trong Lưu trữ của Anna DOI: %(doi)s Tải xuống SciDB Nexus/STC Chưa có bản xem trước. Tải tệp từ <a %(a_path)s>Lưu Trữ của Anna</a>. Để hỗ trợ khả năng truy cập và bảo tồn lâu dài kiến thức của con người, hãy trở thành <a %(a_donate)s>thành viên</a>. Như một phần thưởng, 🧬&nbsp;SciDB tải nhanh hơn cho các thành viên, không có bất kỳ giới hạn nào. Không hoạt động? Hãy thử <a %(a_refresh)s>làm mới</a>. Sci-Hub Thêm trường tìm kiếm cụ thể Tìm kiếm mô tả và các nhận xét dữ liệu Năm xuất bản Tuỳ chọn nâng cao Truy cập Nội dung Hiển thị Danh sách Bảng Loại tệp Ngôn ngữ Sắp xếp bằng Lớn nhất Liên quan nhất Mới nhất (Kích thước tập tin) (mã nguồn mở) (năm phát hành) Già nhất Ngẫu nhiên Nhỏ nhất Nguồn dược thu thập dữ liệu và tạo thành nguồn mở bởi Anna's Archive Cho vay kỹ thuật số (%(count)s) Bài báo (%(count)s) Chúng tôi đã tìm thấy kết quả phù hợp trong: %(in)s. Bạn có thể tham khảo URL được tìm thấy ở đó khi <a %(a_request)s>yêu cầu tệp</a>. Dữ liệu số (%(count)s) Để khám phá chỉ mục tìm kiếm bằng mã, hãy sử dụng <a %(a_href)s>Khám phá Mã (Codes Explorer)</a>. Chỉ mục tìm kiếm được cập nhật hàng tháng. Nó hiện bao gồm các mục nhập tối đa %(last_data_refresh_date)s. Để biết thêm thông tin kỹ thuật, hãy xem trang %(link_open_tag)sdatasets</a>. Loại trừ Chỉ bao gồm Chưa kiểm tra thêm nữa.… Tiếp theo … Trước Chỉ mục tìm kiếm này hiện bao gồm siêu dữ liệu từ thư viện cho vay kỹ thuật số có kiểm soát của Internet Archive. <a %(a_datasets)s>Tìm hiểu thêm về tập dữ liệu của chúng tôi</a>. Để biết thêm các thư viện cho mượn sách trên Internet, hãy xem <a %(a_wikipedia)s>Wikipedia</a> và <a %(a_mobileread)s>MobileRead Wiki</a>. Đối với DMCA / khiếu nại về bản quyền <a %(a_copyright)s>nhấp vào đây</a>. Thời gian tải về Lỗi trong quá trình tìm kiếm. Hãy thử <a %(a_reload)s>tải lại trang</a>. Nếu sự cố vẫn tiếp diễn, vui lòng gửi email cho chúng tôi theo địa chỉ %(email)s. Tải về nhanh Trên thực tế, bất kỳ ai cũng có thể giúp bảo tồn các tệp này bằng cách tạo <a %(a_torrents)s>danh sách torrent thống nhất</a> của chúng tôi. ➡️ Đôi khi điều này xảy ra không chính xác khi máy chủ tìm kiếm chậm. Trong những trường hợp như vậy, <a %(a_attrs)s>tải lại</a> có thể giúp. ❌ Tệp này có thể có vấn đề. Đang tìm giấy tờ? Chỉ mục tìm kiếm này hiện bao gồmdữ liệu từ nhiều nguồn dữ liệu số khác nhau. <a %(a_datasets)s>Tìm hiểu thêm về tập dữ liệu của chúng tôi</a>. Có rất nhiều nguồn dữ liệu cho các tác phẩm viết trên khắp thế giới. <a %(a_wikipedia)s>Trang Wikipedia này</a> là một khởi đầu tốt, nhưng nếu bạn biết những danh sách hay khác, vui lòng cho chúng tôi biết. Đối với dữ liệu số, chúng tôi hiển thị các bản ghi gốc. Chúng tôi không thực hiện bất kỳ việc hợp nhất hồ sơ nào. Chúng tôi hiện có danh mục sách, bài báo và các tác phẩm viết hoàn toàn mở toàn diện nhất thế giới. Chúng tôi lấy dữ liệu từ Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>và hơn thế nữa</a>. <span %(classname)s>Không có tệp nào được tìm thấy.</span> Hãy thử các cựm từ và bộ lộc tìm kiếm khác nhau hoặc nhỏ hơn. Kết quả %(from)s-%(to)s (tổng cộng %(total)s) Nếu bạn tìm thấy thư viện chìm khác mà chúng tôi nên thu thập thêm vào hoặc nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi theo địa chỉ %(email)s. %(num)d những kết quả liên quan một phần %(num)d+ nhứng kết quả liên quan một phần Nhập vào hộp để tìm kiếm tệp trong thư viện cho mượn trên Internet. Nhập vào hộp để tìm kiếm danh mục các tệp có thể tải xuống trực tiếp %(count)s của chúng tôi mà chúng tôi <a %(a_preserve)s>lưu giữ mãi mãi</a>. Gõ vào ô để tìm kiếm. Nhập vào hộp để tìm kiếm danh mục các bài báo học thuật và bài báo tạp chí %(count)s mà chúng tôi <a %(a_preserve)s>lưu giữ mãi mãi</a>. Nhập vào hộp để tìm kiếm dữ liệu từ thư viện. Điều này có thể hữu ích khi <a %(a_request)s>yêu cầu một tệp</a>. Mẹo hay: hãy sử dụng phím tắt “/” (tiêu điểm tìm kiếm), “enter” (tìm kiếm), “j” (lên), “k” (xuống), “<” (trang trước), “>” (trang tiếp theo) để điều hướng nhanh hơn. Đây là các bản ghi dữ liệu số, <span %(classname)s>không phải</span> các tệp có thể tải xuống. Thiết lập tìm kiếm Tìm kiếm Cho mượn kỹ thuật số Tải xuống Bài báo tạp chí Dữ liệu số Tìm kiếm mới %(search_input)s - Tìm kiếm Quá trình tìm kiếm mất quá nhiều thời gian, và bạn có thể thấy kết quả không chính xác. Đôi khi việc <a %(a_reload)s>tải lại</a> trang sẽ hữu ích. Quá trình tìm kiếm mất quá nhiều thời gian, điều này thường xảy ra với các truy vấn rộng. Số lượng bộ lọc có thể không chính xác. Đối với các tải lên lớn (hơn 10.000 tệp) không được chấp nhận bởi Libgen hoặc Z-Library, vui lòng liên hệ với chúng tôi tại %(a_email)s. Đối với Libgen.li, hãy chắc chắn đăng nhập trước vào <a %(a_forum)s>diễn đàn của họ</a> với tên người dùng %(username)s và mật khẩu %(password)s, sau đó quay lại <a %(a_upload_page)s>trang tải lên</a> của họ. Hiện tại, chúng tôi đề xuất tải lên sách mới vào các nhánh của Library Genesis. Đây là <a %(a_guide)s>hướng dẫn tiện lợi</a>. Lưu ý rằng cả hai nhánh mà chúng tôi lập chỉ mục trên trang web này đều lấy từ hệ thống tải lên này. Đối với các tải lên nhỏ (tối đa 10.000 tệp) vui lòng tải chúng lên cả %(first)s và %(second)s. Ngoài ra, bạn có thể tải chúng lên Z-Library <a %(a_upload)s>tại đây</a>. Để tải lên các bài giấy học thuật, vui lòng cũng (ngoài Library Genesis) tải lên <a %(a_stc_nexus)s>STC Nexus</a>. Họ là thư viện bóng tốt nhất cho các bài giấy mới. Chúng tôi chưa tích hợp họ, nhưng sẽ làm điều đó vào một thời điểm nào đó. Bạn có thể sử dụng <a %(a_telegram)s>bot tải lên trên Telegram</a> của họ, hoặc liên hệ với địa chỉ được liệt kê trong tin nhắn ghim của họ nếu bạn có quá nhiều tệp để tải lên theo cách này. <span %(label)s>Công việc tình nguyện nặng (tiền thưởng từ 50 USD đến 5,000 USD):</span> nếu bạn có thể dành nhiều thời gian và/hoặc tài nguyên cho sứ mệnh của chúng tôi, chúng tôi rất mong được làm việc chặt chẽ hơn với bạn. Cuối cùng, bạn có thể tham gia vào đội ngũ nội bộ. Mặc dù ngân sách của chúng tôi hạn chế, chúng tôi có thể trao <span %(bold)s>💰 tiền thưởng</span> cho những công việc căng thẳng nhất. <span %(label)s>Công việc tình nguyện nhẹ:</span> nếu bạn chỉ có thể dành vài giờ ở đây và đó, vẫn có rất nhiều cách bạn có thể giúp đỡ. Chúng tôi thưởng cho các tình nguyện viên nhất quán bằng <span %(bold)s>🤝 tư cách thành viên của Thư Viện của Anna</span>. Lưu Trữ của Anna dựa vào các tình nguyện viên như bạn. Chúng tôi hoan nghênh mọi mức độ cam kết và có hai loại trợ giúp chính mà chúng tôi đang tìm kiếm: Nếu bạn không thể tình nguyện thời gian của mình, bạn vẫn có thể giúp chúng tôi rất nhiều bằng cách <a %(a_donate)s>quyên góp tiền</a>, <a %(a_torrents)s>seeding các torrents của chúng tôi</a>, <a %(a_uploading)s>tải lên sách</a>, hoặc <a %(a_help)s>kể cho bạn bè về Lưu Trữ của Anna</a>. <span %(bold)s>Các công ty:</span> chúng tôi cung cấp quyền truy cập trực tiếp tốc độ cao vào các bộ sưu tập của chúng tôi để đổi lấy quyên góp cấp doanh nghiệp hoặc trao đổi cho các bộ sưu tập mới (ví dụ: các bản quét mới, bộ dữ liệu OCR, làm giàu dữ liệu của chúng tôi). <a %(a_contact)s>Liên hệ với chúng tôi</a> nếu bạn là đối tượng này. Xem thêm trang <a %(a_llm)s>LLM của chúng tôi</a>. Tiền thưởng Chúng tôi luôn tìm kiếm những người có kỹ năng lập trình vững chắc hoặc an ninh mạng tấn công để tham gia. Bạn có thể đóng góp quan trọng trong việc bảo tồn di sản của nhân loại. Như một lời cảm ơn, chúng tôi tặng thành viên cho những đóng góp vững chắc. Như một lời cảm ơn lớn, chúng tôi tặng tiền thưởng cho những nhiệm vụ đặc biệt quan trọng và khó khăn. Điều này không nên được xem như là một công việc thay thế, nhưng nó là một động lực thêm và có thể giúp bù đắp chi phí phát sinh. Hầu hết mã của chúng tôi là mã nguồn mở, và chúng tôi cũng sẽ yêu cầu mã của bạn là mã nguồn mở khi trao thưởng. Có một số ngoại lệ mà chúng tôi có thể thảo luận trên cơ sở từng cá nhân. Tiền thưởng được trao cho người đầu tiên hoàn thành nhiệm vụ. Hãy thoải mái bình luận trên vé tiền thưởng để cho người khác biết bạn đang làm gì, để người khác có thể tạm dừng hoặc liên hệ với bạn để hợp tác. Nhưng hãy lưu ý rằng người khác vẫn có thể làm việc trên đó và cố gắng vượt qua bạn. Tuy nhiên, chúng tôi không trao thưởng cho công việc cẩu thả. Nếu có hai bài nộp chất lượng cao được thực hiện gần nhau (trong vòng một hoặc hai ngày), chúng tôi có thể chọn trao thưởng cho cả hai, theo quyết định của chúng tôi, ví dụ 100%% cho bài nộp đầu tiên và 50%% cho bài nộp thứ hai (tổng cộng là 150%%). Đối với các tiền thưởng lớn hơn (đặc biệt là tiền thưởng scraping), vui lòng liên hệ với chúng tôi khi bạn đã hoàn thành khoảng 5%% của nó, và bạn tự tin rằng phương pháp của bạn sẽ mở rộng đến cột mốc đầy đủ. Bạn sẽ phải chia sẻ phương pháp của mình với chúng tôi để chúng tôi có thể đưa ra phản hồi. Ngoài ra, bằng cách này chúng tôi có thể quyết định phải làm gì nếu có nhiều người đang tiến gần đến một tiền thưởng, chẳng hạn như có thể trao thưởng cho nhiều người, khuyến khích mọi người hợp tác, v.v. CẢNH BÁO: các nhiệm vụ có tiền thưởng cao <span %(bold)s>khó khăn</span> — có thể bạn nên bắt đầu với những nhiệm vụ dễ hơn. Đi đến <a %(a_gitlab)s>danh sách vấn đề trên Gitlab</a> của chúng tôi và sắp xếp theo “Ưu tiên nhãn”. Điều này cho thấy thứ tự các nhiệm vụ mà chúng tôi quan tâm. Các nhiệm vụ không có tiền thưởng rõ ràng vẫn đủ điều kiện để nhận thành viên, đặc biệt là những nhiệm vụ được đánh dấu “Đã chấp nhận” và “Yêu thích của Anna”. Bạn có thể muốn bắt đầu với một “Dự án khởi đầu”. Tình nguyện nhẹ Chúng tôi hiện cũng có một kênh Matrix đồng bộ tại %(matrix)s. Nếu bạn có vài giờ rảnh rỗi, bạn có thể giúp đỡ theo nhiều cách. Hãy chắc chắn tham gia <a %(a_telegram)s>phòng chat tình nguyện trên Telegram</a>. Như một biểu hiện của sự cảm kích, chúng tôi thường tặng 6 tháng “Thủ Thư May Mắn” cho các cột mốc cơ bản, và nhiều hơn cho công việc tình nguyện liên tục. Tất cả các cột mốc đều yêu cầu công việc chất lượng cao — công việc cẩu thả gây hại cho chúng tôi nhiều hơn là giúp đỡ và chúng tôi sẽ từ chối nó. Vui lòng <a %(a_contact)s>email cho chúng tôi</a> khi bạn đạt được một cột mốc. %(links)s liên kết hoặc ảnh chụp màn hình của các yêu cầu bạn đã hoàn thành. Đáp ứng yêu cầu sách (hoặc tài liệu, v.v.) trên các diễn đàn Z-Library hoặc Library Genesis. Chúng tôi không có hệ thống yêu cầu sách riêng, nhưng chúng tôi bản sao các thư viện đó, vì vậy làm cho chúng tốt hơn cũng làm cho Lưu Trữ của Anna tốt hơn. Cột mốc Nhiệm vụ Tùy thuộc vào nhiệm vụ. Các nhiệm vụ nhỏ được đăng trên <a %(a_telegram)s>phòng chat tình nguyện trên Telegram</a> của chúng tôi. Thường xuyên là cho thành viên, đôi khi là cho các khoản tiền thưởng nhỏ. Các nhiệm vụ nhỏ được đăng trong nhóm trò chuyện tình nguyện của chúng tôi. Hãy chắc chắn để lại bình luận về các vấn đề bạn đã sửa, để người khác không lặp lại công việc của bạn. %(links)s liên kết của các bản ghi bạn đã cải thiện. Bạn có thể sử dụng <a %(a_list)s>danh sách các vấn đề metadata ngẫu nhiên</a> làm điểm khởi đầu. Cải thiện dữ liệu số bằng cách <a %(a_metadata)s>liên kết</a> với Open Library. Những điều này nên cho thấy bạn thông báo cho ai đó về Lưu trữ của Anna, và họ cảm ơn bạn. %(links)s liên kết hoặc ảnh chụp màn hình. Lan truyền thông tin về Lưu trữ của Anna. Ví dụ, bằng cách giới thiệu sách trên Lưu trữ của Anna, liên kết đến các bài viết trên blog của chúng tôi, hoặc chỉ đơn giản là hướng dẫn mọi người đến trang web của chúng tôi. Dịch hoàn toàn một ngôn ngữ (nếu nó chưa gần hoàn thành.) <a %(a_translate)s>Dịch</a> trang web. Liên kết đến lịch sử chỉnh sửa cho thấy bạn đã đóng góp đáng kể. Cải thiện trang Wikipedia cho Anna’s Archive bằng ngôn ngữ của bạn. Bao gồm thông tin từ trang Wikipedia của AA bằng các ngôn ngữ khác, và từ trang web và blog của chúng tôi. Thêm các tham chiếu đến AA trên các trang liên quan khác. Tình nguyện & Tiền thưởng 