#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Ongeldige versoek. Besoek %(websites)s."

#, fuzzy
msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

#, fuzzy
msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

#, fuzzy
msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Internet Archive Uitleenbiblioteek"

#, fuzzy
msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

#, fuzzy
msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " en "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "en meer"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;Ons spieël %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Ons skraap en maak %(scraped)s oopbron."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Al ons kode en data is heeltemal oopbron."

#, fuzzy
msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Die grootste werklik oop biblioteek in die geskiedenis van die mensdom."

#, fuzzy
msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;boeke, %(paper_count)s&nbsp;artikels — vir ewig bewaar."

#, fuzzy
msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Die wêreld se grootste oopbron oopdata-biblioteek. ⭐️&nbsp;Spieëls Sci-Hub, Library Genesis, Z-Library, en meer. 📈&nbsp;%(book_any)s boeke, %(journal_article)s artikels, %(book_comic)s strokiesprente, %(magazine)s tydskrifte — vir altyd bewaar."

#, fuzzy
msgid "layout.index.header.tagline_short"
msgstr "📚 Die wêreld se grootste oopbron oopdata biblioteek.<br>⭐️ Spieël Scihub, Libgen, Zlib, en meer."

#, fuzzy
msgid "common.md5_report_type_mapping.metadata"
msgstr "Verkeerde metadata (bv. titel, beskrywing, voorbladbeeld)"

#, fuzzy
msgid "common.md5_report_type_mapping.download"
msgstr "Aflaaiprobleme (bv. kan nie koppel nie, foutboodskap, baie stadig)"

#, fuzzy
msgid "common.md5_report_type_mapping.broken"
msgstr "Lêer kan nie oopgemaak word nie (bv. beskadigde lêer, DRM)"

#, fuzzy
msgid "common.md5_report_type_mapping.pages"
msgstr "Swak kwaliteit (bv. formateringskwessies, swak skankwaliteit, ontbrekende bladsye)"

#, fuzzy
msgid "common.md5_report_type_mapping.spam"
msgstr "Strooipos / lêer moet verwyder word (bv. advertensies, beledigende inhoud)"

#, fuzzy
msgid "common.md5_report_type_mapping.copyright"
msgstr "Kopiereg eis"

#, fuzzy
msgid "common.md5_report_type_mapping.other"
msgstr "Ander"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Bonus aflaaie"

#, fuzzy
msgid "common.membership.tier_name.2"
msgstr "Briljante Boekwurm"

#, fuzzy
msgid "common.membership.tier_name.3"
msgstr "Gelukkige Bibliotekaris"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Skitterende Datahamster"

#, fuzzy
msgid "common.membership.tier_name.5"
msgstr "Ongelooflike Argivaris"

#, fuzzy
msgid "common.membership.format_currency.total"
msgstr "%(amount)s totaal"

#, fuzzy
msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s (%(amount_usd)s) totaal"

#, fuzzy
msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s (%(amount_usd)s)"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

#, fuzzy
msgid "common.donation.order_processing_status_labels.0"
msgstr "onbetaal"

#, fuzzy
msgid "common.donation.order_processing_status_labels.1"
msgstr "betaal"

#, fuzzy
msgid "common.donation.order_processing_status_labels.2"
msgstr "gekanseleer"

#, fuzzy
msgid "common.donation.order_processing_status_labels.3"
msgstr "verval"

#, fuzzy
msgid "common.donation.order_processing_status_labels.4"
msgstr "wag vir Anna om te bevestig"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "ongeldig"

#, fuzzy
msgid "page.donate.title"
msgstr "Skenk"

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation"
msgstr "Jy het 'n <a %(a_donation)s>bestaande donasie</a> in proses. Voltooi of kanselleer asseblief daardie donasie voordat jy 'n nuwe donasie maak."

#, fuzzy
msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s>Sien al my donasies</a>"

#, fuzzy
msgid "page.donate.header.text1"
msgstr "Anna se Argief is 'n nie-winsgewende, oopbron, oopdata-projek. Deur te skenk en 'n lid te word, ondersteun jy ons bedrywighede en ontwikkeling. Aan al ons lede: dankie dat julle ons aan die gang hou! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Vir meer inligting, kyk na die <a %(a_donate)s>Donasie FAQ</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Om nog meer aflaaie te kry, <a %(a_refer)s>verwys jou vriende</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Jy kry %(percentage)s%% bonus vinnige aflaaie, omdat jy deur gebruiker %(profile_link)s verwys is."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Dit geld vir die hele lidmaatskapstydperk."

#, fuzzy
msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s vinnige aflaaie per dag"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "as u hierdie maand skenk!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / maand"

#, fuzzy
msgid "page.donate.buttons.join"
msgstr "Sluit aan"

#, fuzzy
msgid "page.donate.buttons.selected"
msgstr "Geselekteer"

#, fuzzy
msgid "page.donate.buttons.up_to_discounts"
msgstr "tot %(percentage)s%% afslag"

#, fuzzy
msgid "page.donate.perks.scidb"
msgstr "SciDB artikels <strong>onbeperk</strong> sonder verifikasie"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "<a %(a_api)s>JSON API</a> toegang"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Verdien <strong>%(percentage)s%% bonus aflaaie</strong> deur <a %(a_refer)s>vriende te verwys</a>."

#, fuzzy
msgid "page.donate.perks.credits"
msgstr "Jou gebruikersnaam of anonieme vermelding in die krediete"

#, fuzzy
msgid "page.donate.perks.previous_plus"
msgstr "Vorige voordele, plus:"

#, fuzzy
msgid "page.donate.perks.early_access"
msgstr "Vroeë toegang tot nuwe kenmerke"

#, fuzzy
msgid "page.donate.perks.exclusive_telegram"
msgstr "Eksklusiewe Telegram met agter-die-skerms opdaterings"

#, fuzzy
msgid "page.donate.perks.adopt"
msgstr "“Neem 'n torrent aan”: jou gebruikersnaam of boodskap in 'n torrent-lêernaam <div %(div_months)s>een keer elke 12 maande van lidmaatskap</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Legendariese status in die bewaring van die mensdom se kennis en kultuur"

#, fuzzy
msgid "page.donate.expert.title"
msgstr "Kundige Toegang"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "kontak ons"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Ons is 'n klein span vrywilligers. Dit mag ons 1-2 weke neem om te antwoord."

#, fuzzy
msgid "page.donate.expert.unlimited_access"
msgstr "<strong>Onbeperkte</strong> hoëspoed toegang"

#, fuzzy
msgid "page.donate.expert.direct_sftp"
msgstr "Direkte <strong>SFTP</strong> bedieners"

#, fuzzy
msgid "page.donate.expert.enterprise_donation"
msgstr "Skenkings of uitruil op ondernemingsvlak vir nuwe versamelings (bv. nuwe skanderings, OCR-datasets)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Ons verwelkom groot skenkings van welgestelde individue of instellings. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Vir skenkings oor $5000, kontak ons asseblief direk by %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Wees bewus daarvan dat hoewel die lidmaatskappe op hierdie bladsy “per maand” is, dit eenmalige skenkings is (nie-herhalend). Sien die <a %(faq)s>Skenking Vrae</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "As jy 'n skenking (enige bedrag) sonder lidmaatskap wil maak, gebruik gerus hierdie Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Kies asseblief 'n betaalmetode."

#, fuzzy
msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

#, fuzzy
msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(tydelik onbeskikbaar)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s geskenkkaart"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Bankkaart (met app)"

#, fuzzy
msgid "page.donate.payment.buttons.crypto"
msgstr "Crypto %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit"
msgstr "Krediet-/debietkaart"

#, fuzzy
msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (VSA) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (gereeld)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kaart / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Krediet-/debiet-/Apple-/Google-kaart (BMC)"

#, fuzzy
msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

#, fuzzy
msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brasilië)"

#, fuzzy
msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Bankkaart"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Krediet-/debietkaart (rugsteun)"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Krediet-/debietkaart 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

#, fuzzy
msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

#, fuzzy
msgid "page.donate.payment.desc.crypto"
msgstr "Met kripto kan u skenk met BTC, ETH, XMR, en SOL. Gebruik hierdie opsie as u reeds vertroud is met kripto-geldeenhede."

#, fuzzy
msgid "page.donate.payment.desc.crypto2"
msgstr "Met kripto kan jy skenk met BTC, ETH, XMR, en meer."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "As u kripto vir die eerste keer gebruik, stel ons voor om %(options)s te gebruik om Bitcoin (die oorspronklike en mees gebruikte kriptogeldeenheid) te koop en te skenk."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

#, fuzzy
msgid "page.donate.payment.desc.paypal"
msgstr "Om met PayPal US te skenk, gaan ons PayPal Crypto gebruik, wat ons toelaat om anoniem te bly. Ons waardeer dat jy die tyd neem om te leer hoe om met hierdie metode te skenk, aangesien dit ons baie help."

#, fuzzy
msgid "page.donate.payment.desc.paypal_short"
msgstr "Skenk met behulp van PayPal."

#, fuzzy
msgid "page.donate.payment.desc.cashapp"
msgstr "Skenk met Cash App."

#, fuzzy
msgid "page.donate.payment.desc.cashapp_easy"
msgstr "As jy Cash App het, is dit die maklikste manier om te skenk!"

#, fuzzy
msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Let daarop dat vir transaksies onder %(amount)s, Cash App 'n %(fee)s fooi kan hef. Vir %(amount)s of meer, is dit gratis!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Skenk met Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "As jy Revolut het, is dit die maklikste manier om te skenk!"

#, fuzzy
msgid "page.donate.payment.desc.credit_debit"
msgstr "Skenk met 'n krediet- of debietkaart."

#, fuzzy
msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay en Apple Pay mag ook werk."

#, fuzzy
msgid "page.donate.payment.desc.elimate_discount"
msgstr "Let daarop dat vir klein donasies die kredietkaartfooie ons %(discount)s%% afslag kan elimineer, so ons beveel langer intekeninge aan."

#, fuzzy
msgid "page.donate.payment.desc.longer_subs"
msgstr "Let daarop dat vir klein donasies die fooie hoog is, so ons beveel langer intekeninge aan."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Met Binance koop jy Bitcoin met 'n krediet-/debietkaart of bankrekening, en skenk dan daardie Bitcoin aan ons. Op hierdie manier kan ons veilig en anoniem bly wanneer ons jou skenking aanvaar."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance is beskikbaar in byna elke land, en ondersteun die meeste banke en krediet-/debietkaarte. Dit is tans ons hoofaanbeveling. Ons waardeer dit dat u die tyd neem om te leer hoe om met hierdie metode te skenk, aangesien dit ons baie help."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Skenk met jou gereelde PayPal-rekening."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Skenk met krediet-/debietkaart, PayPal, of Venmo. Jy kan tussen hierdie kies op die volgende bladsy."

#, fuzzy
msgid "page.donate.payment.desc.amazon"
msgstr "Skenk met 'n Amazon geskenkkaart."

#, fuzzy
msgid "page.donate.payment.desc.amazon_round"
msgstr "Let daarop dat ons moet afrond na bedrae wat deur ons herverkopers aanvaar word (minimum %(minimum)s)."

#, fuzzy
msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>BELANGRIK:</strong> Ons ondersteun slegs Amazon.com, nie ander Amazon-webwerwe nie. Byvoorbeeld, .de, .co.uk, .ca, word NIE ondersteun nie."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>BELANGRIK:</strong> Hierdie opsie is vir %(amazon)s. As u 'n ander Amazon-webwerf wil gebruik, kies dit hierbo."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Hierdie metode gebruik 'n kriptogeldverskaffer as 'n intermediêre omskakeling. Dit kan 'n bietjie verwarrend wees, so gebruik asseblief slegs hierdie metode as ander betaalmetodes nie werk nie. Dit werk ook nie in alle lande nie."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Skenk met 'n krediet-/debietkaart, deur die Alipay-app (super maklik om op te stel)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Installeer Alipay-app"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Installeer die Alipay-app vanaf die <a %(a_app_store)s>Apple App Store</a> of <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Registreer met jou telefoonnommer."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Geen verdere persoonlike besonderhede is nodig nie."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Voeg bankkaart by"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Ondersteun: Visa, MasterCard, JCB, Diners Club en Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Sien <a %(a_alipay)s>hierdie gids</a> vir meer inligting."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Ons kan nie krediet-/debietkaarte direk ondersteun nie, omdat banke nie met ons wil werk nie. ☹ Daar is egter verskeie maniere om krediet-/debietkaarte te gebruik deur ander betaalmetodes:"

#, fuzzy
msgid "page.donate.payment.buttons.amazon"
msgstr "Amazon Geskenkkaart"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Stuur vir ons Amazon.com geskenkkaarte met jou krediet-/debietkaart."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay ondersteun internasionale krediet-/debietkaarte. Sien <a %(a_alipay)s>hierdie gids</a> vir meer inligting."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) ondersteun internasionale krediet-/debietkaarte. In die WeChat-app, gaan na “Me => Services => Wallet => Add a Card”. As jy dit nie sien nie, aktiveer dit deur “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Jy kan kripto koop met krediet-/debietkaarte."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Kripto vinnige dienste"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Vinnige dienste is gerieflik, maar hef hoër fooie."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Jy kan dit gebruik in plaas van 'n kripto-uitruil as jy vinnig 'n groter skenking wil maak en nie omgee vir 'n fooi van $5-10 nie."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Maak seker om die presiese kripto-bedrag wat op die skenkingsbladsy getoon word, te stuur, nie die bedrag in $USD nie."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Andersins sal die fooi afgetrek word en ons kan nie jou lidmaatskap outomaties verwerk nie."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s afhangende van land, geen verifikasie vir eerste transaksie)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, geen verifikasie vir eerste transaksie)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, geen verifikasie vir eerste transaksie)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "As enige van hierdie inligting verouderd is, stuur asseblief 'n e-pos om ons in kennis te stel."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Vir kredietkaarte, debietkaarte, Apple Pay en Google Pay gebruik ons “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). In hul stelsel is een “koffie” gelyk aan $5, so u donasie sal afgerond word tot die naaste veelvoud van 5."

#, fuzzy
msgid "page.donate.duration.intro"
msgstr "Kies hoe lank jy wil inteken."

#, fuzzy
msgid "page.donate.duration.1_mo"
msgstr "1 maand"

#, fuzzy
msgid "page.donate.duration.3_mo"
msgstr "3 maande"

#, fuzzy
msgid "page.donate.duration.6_mo"
msgstr "6 maande"

#, fuzzy
msgid "page.donate.duration.12_mo"
msgstr "12 maande"

#, fuzzy
msgid "page.donate.duration.24_mo"
msgstr "24 maande"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 maande"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 maande"

#, fuzzy
msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>na <span %(span_discount)s></span> afslag</div><div %(div_total)s></div><div %(div_duration)s></div>"

#, fuzzy
msgid "page.donate.payment.minimum_method"
msgstr "Hierdie betaalmetode vereis 'n minimum van %(amount)s. Kies asseblief 'n ander duur of betaalmetode."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Skenk"

#, fuzzy
msgid "page.donate.payment.maximum_method"
msgstr "Hierdie betaalmetode laat slegs 'n maksimum van %(amount)s toe. Kies asseblief 'n ander duur of betaalmetode."

#, fuzzy
msgid "page.donate.login2"
msgstr "Om 'n lid te word, <a %(a_login)s>Meld aan of Registreer</a> asseblief. Dankie vir u ondersteuning!"

#, fuzzy
msgid "page.donate.payment.crypto_select"
msgstr "Kies jou voorkeur kripto-munt:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(laagste minimum bedrag)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(gebruik wanneer Ethereum vanaf Coinbase gestuur word)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(waarskuwing: hoë minimum bedrag)"

#, fuzzy
msgid "page.donate.submit.confirm"
msgstr "Klik die skenk-knoppie om hierdie skenking te bevestig."

#, fuzzy
msgid "page.donate.submit.button"
msgstr "Skenk <span %(span_cost)s></span> <span %(span_label)s></span>"

#, fuzzy
msgid "page.donate.submit.cancel_note"
msgstr "Jy kan steeds die donasie kanselleer tydens die betaalproses."

#, fuzzy
msgid "page.donate.submit.success"
msgstr "✅ Herlei na die skenkingsbladsy…"

#, fuzzy
msgid "page.donate.submit.failure"
msgstr "❌ Iets het verkeerd gegaan. Laai asseblief die bladsy weer en probeer weer."

#, fuzzy
msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

#, fuzzy
msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / maand"

#, fuzzy
msgid "page.donate.duration.summary.duration.1_mo"
msgstr "vir 1 maand"

#, fuzzy
msgid "page.donate.duration.summary.duration.3_mo"
msgstr "vir 3 maande"

#, fuzzy
msgid "page.donate.duration.summary.duration.6_mo"
msgstr "vir 6 maande"

#, fuzzy
msgid "page.donate.duration.summary.duration.12_mo"
msgstr "vir 12 maande"

#, fuzzy
msgid "page.donate.duration.summary.duration.24_mo"
msgstr "vir 24 maande"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "vir 48 maande"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "vir 96 maande"

#, fuzzy
msgid "page.donate.submit.button.label.1_mo"
msgstr "vir 1 maand “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.3_mo"
msgstr "vir 3 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.6_mo"
msgstr "vir 6 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.12_mo"
msgstr "vir 12 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.24_mo"
msgstr "vir 24 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "vir 48 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "vir 96 maande “%(tier_name)s”"

#, fuzzy
msgid "page.donation.title"
msgstr "Donasie"

#, fuzzy
msgid "page.donation.header.date"
msgstr "Datum: %(date)s"

#, fuzzy
msgid "page.donation.header.total_including_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand vir %(duration)s maande, insluitend %(discounts)s%% afslag)</span>"

#, fuzzy
msgid "page.donation.header.total_without_discount"
msgstr "Totaal: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / maand vir %(duration)s maande)</span>"

#, fuzzy
msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

#, fuzzy
msgid "page.donation.header.id"
msgstr "Identifiseerder: %(id)s"

#, fuzzy
msgid "page.donation.header.cancel.button"
msgstr "Kanselleer"

#, fuzzy
msgid "page.donation.header.cancel.confirm.msg"
msgstr "Is u seker dat u wil kanselleer? Moet nie kanselleer as u reeds betaal het nie."

#, fuzzy
msgid "page.donation.header.cancel.confirm.button"
msgstr "Ja, kanselleer asseblief"

#, fuzzy
msgid "page.donation.header.cancel.success"
msgstr "✅ Jou skenking is gekanselleer."

#, fuzzy
msgid "page.donation.header.cancel.new_donation"
msgstr "Maak 'n nuwe donasie"

#, fuzzy
msgid "page.donation.header.cancel.failure"
msgstr "❌ Iets het verkeerd geloop. Herlaai asseblief die bladsy en probeer weer."

#, fuzzy
msgid "page.donation.header.reorder"
msgstr "Hersorteer"

#, fuzzy
msgid "page.donation.old_instructions.intro_paid"
msgstr "Jy het reeds betaal. As jy steeds die betalingsinstruksies wil hersien, klik hier:"

#, fuzzy
msgid "page.donation.old_instructions.show_button"
msgstr "Wys ou betaalinstruksies"

#, fuzzy
msgid "page.donation.thank_you_donation"
msgstr "Dankie vir jou donasie!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "As u dit nog nie gedoen het nie, skryf u geheime sleutel neer vir aanmelding:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Anders kan jy dalk uit hierdie rekening gesluit word!"

#, fuzzy
msgid "page.donation.old_instructions.intro_outdated"
msgstr "Die betalingsinstruksies is nou verouderd. As jy nog 'n skenking wil maak, gebruik die “Herbestel” knoppie hierbo."

#, fuzzy
msgid "page.donate.submit.crypto_note"
msgstr "<strong>Belangrike nota:</strong> Kripto-pryse kan wild wissel, soms selfs soveel as 20%% binne 'n paar minute. Dit is steeds minder as die fooie wat ons met baie betalingsverskaffers aangaan, wat dikwels 50-60%% vra om met 'n “skaduliefdadigheid” soos ons te werk. <u>As jy vir ons die kwitansie met die oorspronklike prys wat jy betaal het stuur, sal ons steeds jou rekening krediteer vir die gekose lidmaatskap</u> (solank die kwitansie nie ouer as 'n paar uur is nie). Ons waardeer dit regtig dat jy bereid is om met sulke dinge te werk om ons te ondersteun! ❤️"

#, fuzzy
msgid "page.donation.expired"
msgstr "Hierdie donasie het verval. Kanselleer asseblief en skep 'n nuwe een."

#, fuzzy
msgid "page.donation.payment.crypto.top_header"
msgstr "Kripto-instruksies"

#, fuzzy
msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Oordra na een van ons kripto-rekeninge"

#, fuzzy
msgid "page.donation.payment.crypto.text1"
msgstr "Skenk die totale bedrag van %(total)s aan een van hierdie adresse:"

#, fuzzy
msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Koop Bitcoin op Paypal"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Vind die “Crypto” bladsy in jou PayPal app of webwerf. Dit is gewoonlik onder “Finansies”."

#, fuzzy
msgid "page.donation.payment.paypal.text3"
msgstr "Volg die instruksies om Bitcoin (BTC) te koop. Jy hoef net die bedrag te koop wat jy wil skenk, %(total)s."

#, fuzzy
msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Stuur die Bitcoin na ons adres"

#, fuzzy
msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Gaan na die “Bitcoin” bladsy in jou PayPal-app of -webwerf. Druk die “Oordra” knoppie %(transfer_icon)s, en dan “Stuur”."

#, fuzzy
msgid "page.donation.payment.paypal.text5"
msgstr "Voer ons Bitcoin (BTC) adres in as die ontvanger, en volg die instruksies om jou donasie van %(total)s te stuur:"

#, fuzzy
msgid "page.donation.credit_debit_card_instructions"
msgstr "Krediet- / debietkaartinstruksies"

#, fuzzy
msgid "page.donation.credit_debit_card_our_page"
msgstr "Skenk deur ons krediet- / debietkaartbladsy"

#, fuzzy
msgid "page.donation.donate_on_this_page"
msgstr "Skenk %(amount)s op <a %(a_page)s>hierdie bladsy</a>."

#, fuzzy
msgid "page.donation.stepbystep_below"
msgstr "Sien die stap-vir-stap gids hieronder."

#, fuzzy
msgid "page.donation.status_header"
msgstr "Status:"

#, fuzzy
msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Wag vir bevestiging (verfris die bladsy om te kyk)…"

#, fuzzy
msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Wag vir oordrag (verfris die bladsy om te kyk)…"

#, fuzzy
msgid "page.donation.time_left_header"
msgstr "Tyd oor:"

#, fuzzy
msgid "page.donation.might_want_to_cancel"
msgstr "(jy mag dalk wil kanselleer en 'n nuwe donasie maak)"

#, fuzzy
msgid "page.donation.reset_timer"
msgstr "Om die timer te herbegin, skep eenvoudig 'n nuwe skenking."

#, fuzzy
msgid "page.donation.refresh_status"
msgstr "Werk status by"

#, fuzzy
msgid "page.donation.footer.issues_contact"
msgstr "As jy enige probleme ondervind, kontak ons asseblief by %(email)s en sluit soveel inligting as moontlik in (soos skermkiekies)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "As u reeds betaal het:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Soms kan bevestiging tot 24 uur neem, so maak seker om hierdie bladsy te verfris (selfs al het dit verval)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

#, fuzzy
msgid "page.donation.buy_pyusd"
msgstr "Koop PYUSD-munt op PayPal"

#, fuzzy
msgid "page.donation.pyusd.instructions"
msgstr "Volg die instruksies om PYUSD-munt (PayPal USD) te koop."

#, fuzzy
msgid "page.donation.pyusd.more"
msgstr "Koop 'n bietjie meer (ons beveel %(more)s meer aan) as die bedrag wat jy skenk (%(amount)s), om transaksiefooie te dek. Jy sal enigiets wat oorbly, hou."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

#, fuzzy
msgid "page.donation.pyusd.transfer"
msgstr "Gaan na die “PYUSD” bladsy in jou PayPal-app of webwerf. Druk die “Oordra” knoppie %(icon)s, en dan “Stuur”."

#, fuzzy
msgid "page.donation.transfer_amount_to"
msgstr "Oordra %(amount)s na %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Koop Bitcoin (BTC) op Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Gaan na die “Bitcoin” (BTC) bladsy in Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Koop 'n bietjie meer (ons beveel %(more)s meer aan) as die bedrag wat jy skenk (%(amount)s), om transaksiefooie te dek. Jy sal enigiets wat oorbly, hou."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Dra die Bitcoin oor na ons adres"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik op die “Stuur bitcoin” knoppie om 'n “onttrekking” te maak. Skakel van dollars na BTC deur die %(icon)s ikoon te druk. Voer die BTC bedrag hieronder in en klik “Stuur”. Kyk na <a %(help_video)s>hierdie video</a> as jy vasval."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Vir klein skenkings (onder $25), mag jy dalk Rush of Priority moet gebruik."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Koop Bitcoin (BTC) op Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Gaan na die “Crypto” bladsy in Revolut om Bitcoin (BTC) te koop."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Koop 'n bietjie meer (ons beveel %(more)s meer aan) as die bedrag wat jy skenk (%(amount)s), om transaksiefooie te dek. Jy sal enigiets wat oorbly, hou."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Dra die Bitcoin oor na ons adres"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klik op die “Stuur bitcoin” knoppie om 'n “onttrekking” te maak. Skakel van euros na BTC deur die %(icon)s ikoon te druk. Voer die BTC bedrag hieronder in en klik “Stuur”. Kyk na <a %(help_video)s>hierdie video</a> as jy vasval."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Maak seker dat u die BTC-bedrag hieronder gebruik, <em>NIE</em> euro's of dollars nie, anders ontvang ons nie die korrekte bedrag nie en kan ons nie u lidmaatskap outomaties bevestig nie."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Vir klein skenkings (onder $25) mag jy dalk Rush of Priority moet gebruik."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Gebruik enige van die volgende “kredietkaart na Bitcoin” vinnige dienste, wat net 'n paar minute neem:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Vul die volgende besonderhede in die vorm in:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "BTC / Bitcoin bedrag:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Gebruik asseblief hierdie <span %(underline)s>presiese bedrag</span>. U totale koste mag hoër wees as gevolg van kredietkaartfooie. Vir klein bedrae mag dit ongelukkig meer wees as ons afslag."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "BTC / Bitcoin adres (eksterne beursie):"

#, fuzzy
msgid "page.donation.crypto_instructions"
msgstr "%(coin_name)s instruksies"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Ons ondersteun slegs die standaard weergawe van kripto-munte, geen eksotiese netwerke of weergawes van munte nie. Dit kan tot 'n uur neem om die transaksie te bevestig, afhangende van die munt."

msgid "page.donation.crypto_qr_code_title"
msgstr "Skandeer QR -kode om te betaal"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Skandeer hierdie QR -kode met u crypto -beursie -app om vinnig die betalingsbesonderhede in te vul"

#, fuzzy
msgid "page.donation.amazon.header"
msgstr "Amazon-geskenkkaart"

#, fuzzy
msgid "page.donation.amazon.form_instructions"
msgstr "Gebruik asseblief die <a %(a_form)s>amptelike Amazon.com-vorm</a> om vir ons 'n geskenkkaart van %(amount)s na die e-posadres hieronder te stuur."

#, fuzzy
msgid "page.donation.amazon.only_official"
msgstr "Ons kan nie ander metodes van geskenkbewyse aanvaar nie, <strong>slegs direk gestuur vanaf die amptelike vorm op Amazon.com</strong>. Ons kan nie jou geskenkbewys teruggee as jy nie hierdie vorm gebruik nie."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Voer die presiese bedrag in: %(amount)s"

#, fuzzy
msgid "page.donate.payment.desc.amazon_message"
msgstr "Moet asseblief NIE jou eie boodskap skryf nie."

#, fuzzy
msgid "page.donation.amazon.form_to"
msgstr "“To” ontvanger e-pos in die vorm:"

#, fuzzy
msgid "page.donation.amazon.unique"
msgstr "Uniek aan jou rekening, moenie deel nie."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Slegs een keer gebruik."

#, fuzzy
msgid "page.donation.amazon.waiting_gift_card"
msgstr "Wag vir geskenkkaart… (verfris die bladsy om te kyk)"

#, fuzzy
msgid "page.donation.amazon.confirm_automated"
msgstr "Na die stuur van jou geskenkkaart, sal ons outomatiese stelsel dit binne 'n paar minute bevestig. As dit nie werk nie, probeer om jou geskenkkaart weer te stuur (<a %(a_instr)s>instruksies</a>)."

#, fuzzy
msgid "page.donation.amazon.doesnt_work"
msgstr "As dit steeds nie werk nie, stuur asseblief 'n e-pos aan ons en Anna sal dit handmatig hersien (dit kan 'n paar dae neem), en maak seker dat jy noem as jy reeds probeer het om weer te stuur."

#, fuzzy
msgid "page.donation.amazon.example"
msgstr "Voorbeeld:"

#, fuzzy
msgid "page.donate.strange_account"
msgstr "Let daarop dat die rekeningnaam of prentjie vreemd mag lyk. Geen rede tot kommer nie! Hierdie rekeninge word bestuur deur ons skenkingsvennote. Ons rekeninge is nie gekap nie."

#, fuzzy
msgid "page.donation.payment.alipay.top_header"
msgstr "Alipay instruksies"

#, fuzzy
msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Skink op Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Skenk die totale bedrag van %(total)s deur <a %(a_account)s>hierdie Alipay-rekening</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "As die donasieblad geblokkeer word, probeer 'n ander internetverbinding (bv. VPN of fooninternet)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Ongelukkig is die Alipay-bladsy dikwels slegs toeganklik vanaf <strong>vasteland China</strong>. Jy mag dalk jou VPN tydelik moet deaktiveer, of 'n VPN na die vasteland van China gebruik (of Hong Kong werk soms ook)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Maak skenking (skandeer QR-kode of druk knoppie)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Maak die <a %(a_href)s>QR-kode skenkingsbladsy</a> oop."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Skandeer die QR-kode met die Alipay-app, of druk die knoppie om die Alipay-app oop te maak."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Wees asseblief geduldig; die bladsy kan 'n rukkie neem om te laai aangesien dit in China is."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "WeChat-instruksies"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Skenk op WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Skenk die totale bedrag van %(total)s deur <a %(a_account)s>hierdie WeChat-rekening</a> te gebruik"

#, fuzzy
msgid "page.donation.payment.pix.top_header"
msgstr "Pix instruksies"

#, fuzzy
msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Skenk op Pix"

#, fuzzy
msgid "page.donation.payment.pix.text1"
msgstr "Skenk die totale bedrag van %(total)s deur <a %(a_account)s>hierdie Pix-rekening</a> te gebruik"

#, fuzzy
msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Stuur vir ons die kwitansie per e-pos"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Stuur 'n kwitansie of skermskoot na u persoonlike verifikasie-adres. Moet NIE hierdie e-posadres gebruik vir u PayPal-skenking nie."

#, fuzzy
msgid "page.donation.footer.text1"
msgstr "Stuur 'n kwitansie of skermskoot na jou persoonlike verifikasie-adres:"

#, fuzzy
msgid "page.donation.footer.crypto_note"
msgstr "As die kripto-wisselkoers tydens die transaksie gewissel het, maak seker dat jy die kwitansie insluit wat die oorspronklike wisselkoers toon. Ons waardeer dit regtig dat jy die moeite doen om kripto te gebruik, dit help ons baie!"

#, fuzzy
msgid "page.donation.footer.text2"
msgstr "Wanneer u u kwitansie per e-pos gestuur het, klik hierdie knoppie, sodat Anna dit handmatig kan hersien (dit kan 'n paar dae neem):"

#, fuzzy
msgid "page.donation.footer.button"
msgstr "Ja, ek het my kwitansie ge-epos"

#, fuzzy
msgid "page.donation.footer.success"
msgstr "✅ Dankie vir jou skenking! Anna sal jou lidmaatskap binne 'n paar dae handmatig aktiveer."

#, fuzzy
msgid "page.donation.footer.failure"
msgstr "❌ Iets het verkeerd gegaan. Laai asseblief die bladsy weer en probeer weer."

#, fuzzy
msgid "page.donation.stepbystep"
msgstr "Stap-vir-stap gids"

#, fuzzy
msgid "page.donation.crypto_dont_worry"
msgstr "Sommige van die stappe noem kripto-beursies, maar moenie bekommerd wees nie, jy hoef niks oor kripto te leer nie."

#, fuzzy
msgid "page.donation.hoodpay.step1"
msgstr "1. Voer jou e-pos in."

#, fuzzy
msgid "page.donation.hoodpay.step2"
msgstr "2. Kies jou betaalmetode."

#, fuzzy
msgid "page.donation.hoodpay.step3"
msgstr "3. Kies jou betaalmetode weer."

#, fuzzy
msgid "page.donation.hoodpay.step4"
msgstr "4. Kies “Self-gehoste” beursie."

#, fuzzy
msgid "page.donation.hoodpay.step5"
msgstr "5. Klik “Ek bevestig eienaarskap”."

#, fuzzy
msgid "page.donation.hoodpay.step6"
msgstr "6. Jy behoort 'n e-pos kwitansie te ontvang. Stuur dit asseblief aan ons, en ons sal jou donasie so gou as moontlik bevestig."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Wag asseblief ten minste <span %(span_hours)s>24 uur</span> (en verfris hierdie bladsy) voordat u ons kontak."

#, fuzzy
msgid "page.donate.mistake"
msgstr "As jy 'n fout gemaak het tydens betaling, kan ons nie terugbetalings doen nie, maar ons sal probeer om dit reg te stel."

#, fuzzy
msgid "page.my_donations.title"
msgstr "My donasies"

#, fuzzy
msgid "page.my_donations.not_shown"
msgstr "Donasiebesonderhede word nie openbaar gewys nie."

#, fuzzy
msgid "page.my_donations.no_donations"
msgstr "Nog geen skenkings nie. <a %(a_donate)s>Maak my eerste skenking.</a>"

#, fuzzy
msgid "page.my_donations.make_another"
msgstr "Maak nog 'n donasie."

#, fuzzy
msgid "page.downloaded.title"
msgstr "Afgelaaide lêers"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Aflaaie van Vinnige Vennootbedieners word gemerk deur %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "As jy 'n lêer afgelaai het met beide vinnige en stadige aflaaie, sal dit twee keer verskyn."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Vinnige aflaaie in die laaste 24 uur tel teen die daaglikse limiet."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Alle tye is in UTC."

#, fuzzy
msgid "page.downloaded.not_public"
msgstr "Afgelaaide lêers word nie publiek gewys nie."

#, fuzzy
msgid "page.downloaded.no_files"
msgstr "Geen lêers afgelaai nie."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "Laaste 18 uur"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Vroeër"

#, fuzzy
msgid "page.account.logged_in.title"
msgstr "Rekening"

#, fuzzy
msgid "page.account.logged_out.title"
msgstr "Meld aan / Registreer"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "Rekening ID: %(account_id)s"

#, fuzzy
msgid "page.account.logged_in.public_profile"
msgstr "Publieke profiel: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Geheime sleutel (moet nie deel nie!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "wys"

#, fuzzy
msgid "page.account.logged_in.membership_has_some"
msgstr "Lidmaatskap: <strong>%(tier_name)s</strong> tot %(until_date)s <a %(a_extend)s>(verleng)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_none"
msgstr "Lidmaatskap: <strong>Geen</strong> <a %(a_become)s>(word 'n lid)</a>"

#, fuzzy
msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Vinnige aflaaie gebruik (laaste 24 uur): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "watter aflaaie?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Eksklusiewe Telegram-groep: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Sluit hier by ons aan!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Gradeer op na 'n <a %(a_tier)s>hoër vlak</a> om by ons groep aan te sluit."

#, fuzzy
msgid "page.account.logged_in.membership_upgrade"
msgstr "Kontak Anna by %(email)s as u belangstel om u lidmaatskap na 'n hoër vlak op te gradeer."

#, fuzzy
msgid "page.contact.title"
msgstr "Kontak e-pos"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Jy kan verskeie lidmaatskappe kombineer (vinnige aflaaie per 24 uur sal saamgevoeg word)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Openbare profiel"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Afgelaaide lêers"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "My skenkings"

#, fuzzy
msgid "page.account.logged_in.logout.button"
msgstr "Teken uit"

#, fuzzy
msgid "page.account.logged_in.logout.success"
msgstr "✅ Jy is nou uitgeteken. Herlaai die bladsy om weer in te teken."

#, fuzzy
msgid "page.account.logged_in.logout.failure"
msgstr "❌ Iets het verkeerd gegaan. Herlaai asseblief die bladsy en probeer weer."

#, fuzzy
msgid "page.account.logged_out.registered.text1"
msgstr "Registrasie suksesvol! Jou geheime sleutel is: <span %(span_key)s>%(key)s</span>"

#, fuzzy
msgid "page.account.logged_out.registered.text2"
msgstr "Bewaar hierdie sleutel sorgvuldig. As jy dit verloor, sal jy toegang tot jou rekening verloor."

#, fuzzy
msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Boekmerk.</strong> U kan hierdie bladsy boekmerk om u sleutel te herwin.</li><li %(li_item)s><strong>Aflaai.</strong> Klik <a %(a_download)s>hierdie skakel</a> om u sleutel af te laai.</li><li %(li_item)s><strong>Wagwoordbestuurder.</strong> Gebruik 'n wagwoordbestuurder om die sleutel te stoor wanneer u dit hieronder invoer.</li>"

#, fuzzy
msgid "page.account.logged_out.key_form.text"
msgstr "Voer jou geheime sleutel in om aan te meld:"

#, fuzzy
msgid "page.account.logged_out.key_form.placeholder"
msgstr "Geheime sleutel"

#, fuzzy
msgid "page.account.logged_out.key_form.button"
msgstr "Meld aan"

#, fuzzy
msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Ongeldige geheime sleutel. Verifieer jou sleutel en probeer weer, of registreer 'n nuwe rekening hieronder."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Moet nie jou sleutel verloor nie!"

#, fuzzy
msgid "page.account.logged_out.register.header"
msgstr "Het jy nog nie 'n rekening nie?"

#, fuzzy
msgid "page.account.logged_out.register.button"
msgstr "Registreer nuwe rekening"

#, fuzzy
msgid "page.login.lost_key"
msgstr "As u u sleutel verloor het, kontak ons asseblief <a %(a_contact)s>hier</a> en verskaf soveel inligting as moontlik."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "U mag dalk tydelik 'n nuwe rekening moet skep om ons te kontak."

#, fuzzy
msgid "page.account.logged_out.old_email.button"
msgstr "Ou e-posgebaseerde rekening? Voer jou <a %(a_open)s>e-pos hier in</a>."

#, fuzzy
msgid "page.list.title"
msgstr "Lys"

#, fuzzy
msgid "page.list.header.edit.link"
msgstr "wysig"

#, fuzzy
msgid "page.list.edit.button"
msgstr "Stoor"

#, fuzzy
msgid "page.list.edit.success"
msgstr "✅ Gestoor. Laai asseblief die bladsy weer."

#, fuzzy
msgid "page.list.edit.failure"
msgstr "❌ Iets het verkeerd gegaan. Probeer asseblief weer."

#, fuzzy
msgid "page.list.by_and_date"
msgstr "Lys volgens %(by)s, geskep <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.list.empty"
msgstr "Lys is leeg."

#, fuzzy
msgid "page.list.new_item"
msgstr "Voeg by of verwyder van hierdie lys deur 'n lêer te vind en die “Lyste” oortjie oop te maak."

#, fuzzy
msgid "page.profile.title"
msgstr "Profiel"

#, fuzzy
msgid "page.profile.not_found"
msgstr "Profiel nie gevind nie."

#, fuzzy
msgid "page.profile.header.edit"
msgstr "wysig"

#, fuzzy
msgid "page.profile.change_display_name.text"
msgstr "Verander jou vertoonnaam. Jou identifiseerder (die deel na “#”) kan nie verander word nie."

#, fuzzy
msgid "page.profile.change_display_name.button"
msgstr "Stoor"

#, fuzzy
msgid "page.profile.change_display_name.success"
msgstr "✅ Gestoor. Herlaai asseblief die bladsy."

#, fuzzy
msgid "page.profile.change_display_name.failure"
msgstr "❌ Iets het verkeerd geloop. Probeer asseblief weer."

#, fuzzy
msgid "page.profile.created_time"
msgstr "Profiel geskep <span %(span_time)s>%(time)s</span>"

#, fuzzy
msgid "page.profile.lists.header"
msgstr "Lyste"

#, fuzzy
msgid "page.profile.lists.no_lists"
msgstr "Nog geen lyste nie"

#, fuzzy
msgid "page.profile.lists.new_list"
msgstr "Skep 'n nuwe lys deur 'n lêer te vind en die “Lyste” oortjie oop te maak."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Kopiereg hervorming is nodig vir nasionale sekuriteit."

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: Chinese LLM's (insluitend DeepSeek) is opgelei op my onwettige argief van boeke en artikels — die grootste in die wêreld. Die Weste moet kopieregwetgewing heroorweeg as 'n kwessie van nasionale sekuriteit."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "metgesel artikels deur TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Nie te lank gelede nie, was “skadu-biblioteke” besig om uit te sterf. Sci-Hub, die massiewe onwettige argief van akademiese artikels, het opgehou om nuwe werke in te neem, weens regsgedinge. “Z-Library”, die grootste onwettige biblioteek van boeke, het gesien hoe sy beweerde skeppers in hegtenis geneem is op kriminele kopieregklagte. Hulle het ongelooflik daarin geslaag om hul arrestasie te ontduik, maar hul biblioteek is steeds onder bedreiging."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Toe Z-Library gesluit is, het ek reeds sy hele biblioteek gerugsteun en was ek op soek na 'n platform om dit te huisves. Dit was my motivering om Anna se Argief te begin: 'n voortsetting van die missie agter daardie vroeëre inisiatiewe. Ons het sedertdien gegroei tot die grootste skadu-biblioteek in die wêreld, wat meer as 140 miljoen gekopieregde tekste in verskeie formate huisves — boeke, akademiese artikels, tydskrifte, koerante, en meer."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "My span en ek is idealiste. Ons glo dat die bewaring en huisvesting van hierdie lêers moreel reg is. Biblioteke regoor die wêreld sien befondsingsbesnoeiings, en ons kan ook nie die mensdom se erfenis aan korporasies toevertrou nie."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Toe het KI gekom. Feitlik al die groot maatskappye wat LLM's bou, het ons gekontak om op ons data op te lei. Die meeste (maar nie almal nie!) Amerikaanse maatskappye het heroorweeg sodra hulle die onwettige aard van ons werk besef het. Daarenteen het Chinese firmas ons versameling entoesiasties omhels, klaarblyklik ongestoord deur die wettigheid daarvan. Dit is noemenswaardig gegewe China se rol as 'n ondertekenaar van byna al die groot internasionale kopieregverdragte."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Ons het hoëspoed toegang aan ongeveer 30 maatskappye gegee. Die meeste van hulle is LLM-maatskappye, en sommige is databrokers, wat ons versameling sal herverkoop. Die meeste is Chinees, hoewel ons ook met maatskappye van die VSA, Europa, Rusland, Suid-Korea, en Japan gewerk het. DeepSeek <a %(arxiv)s>het erken</a> dat 'n vroeëre weergawe op 'n deel van ons versameling opgelei is, hoewel hulle stil is oor hul nuutste model (waarskynlik ook op ons data opgelei)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "As die Weste voor wil bly in die wedloop van LLM's, en uiteindelik, AGI, moet dit sy posisie oor kopiereg heroorweeg, en gou. Of jy nou met ons saamstem of nie oor ons morele saak, dit word nou 'n saak van ekonomie, en selfs van nasionale sekuriteit. Alle magblokke bou kunsmatige super-wetenskaplikes, super-hackers, en super-militêre. Vryheid van inligting word 'n kwessie van oorlewing vir hierdie lande — selfs 'n kwessie van nasionale sekuriteit."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Ons span is van regoor die wêreld, en ons het nie 'n spesifieke belyning nie. Maar ons sal lande met sterk kopieregwette aanmoedig om hierdie eksistensiële bedreiging te gebruik om dit te hervorm. Wat om te doen?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Ons eerste aanbeveling is eenvoudig: verkort die kopieregtermyn. In die VSA word kopiereg vir 70 jaar na die dood van die outeur toegestaan. Dit is absurd. Ons kan dit in lyn bring met patente, wat vir 20 jaar na indiening toegestaan word. Dit behoort meer as genoeg tyd te wees vir outeurs van boeke, artikels, musiek, kuns, en ander kreatiewe werke, om ten volle vergoed te word vir hul pogings (insluitend langtermynprojekte soos rolprentaanpassings)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Dan, ten minste, moet beleidmakers uitsonderings insluit vir die massabewaring en verspreiding van tekste. As verlore inkomste van individuele kliënte die hoofbesorgdheid is, kan persoonlike verspreiding verbied bly. Op sy beurt, diegene wat in staat is om groot bewaarplekke te bestuur — maatskappye wat LLM's oplei, saam met biblioteke en ander argiewe — sal deur hierdie uitsonderings gedek word."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Sommige lande doen reeds 'n weergawe hiervan. TorrentFreak <a %(torrentfreak)s>het berig</a> dat China en Japan KI-uitsonderings in hul kopieregwette ingestel het. Dit is vir ons onduidelik hoe dit met internasionale verdragte saamwerk, maar dit gee beslis dekking aan hul binnelandse maatskappye, wat verduidelik wat ons gesien het."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Wat Anna se Argief betref — ons sal voortgaan met ons ondergrondse werk gewortel in morele oortuiging. Tog is ons grootste wens om in die lig te tree, en ons impak wettig te versterk. Hervorm asseblief kopiereg."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Lees die metgesel artikels deur TorrentFreak: <a %(torrentfreak)s>eerste</a>, <a %(torrentfreak_2)s>tweede</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Wenners van die $10,000 ISBN visualisering beloning"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Ons het 'n paar ongelooflike inskrywings ontvang vir die $10,000 ISBN visualisering beloning."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "'n Paar maande gelede het ons 'n <a %(all_isbns)s>$10,000 beloning</a> aangekondig om die beste moontlike visualisering van ons data te maak wat die ISBN-ruimte wys. Ons het beklemtoon om te wys watter lêers ons reeds geberg het en watter nie, en ons het later 'n datastel bygevoeg wat beskryf hoeveel biblioteke ISBN's hou (’n maatstaf van skaarsheid)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Ons is oorweldig deur die reaksie. Daar was soveel kreatiwiteit. 'n Groot dankie aan almal wat deelgeneem het: julle energie en entoesiasme is aansteeklik!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Uiteindelik wou ons die volgende vrae beantwoord: <strong>watter boeke bestaan in die wêreld, hoeveel het ons al geargiveer, en op watter boeke moet ons volgende fokus?</strong> Dit is wonderlik om te sien dat soveel mense omgee oor hierdie vrae."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Ons het self met 'n basiese visualisering begin. In minder as 300kb verteenwoordig hierdie prentjie bondig die grootste volledig oop \"lys van boeke\" wat ooit in die geskiedenis van die mensdom saamgestel is:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Alle ISBNs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Lêers in Anna se Argief"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "CADAL SSNOs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "CERLALC data-lek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "DuXiu SSIDs"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "EBSCOhost se eBoek Indeks"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Argief"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "ISBN Globale Register van Uitgewers"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Russiese Staatsbiblioteek"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Imperiale Biblioteek van Trantor"

#, fuzzy
msgid "common.back"
msgstr "Terug"

#, fuzzy
msgid "common.forward"
msgstr "Vorentoe"

#, fuzzy
msgid "common.last"
msgstr "Laaste"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Sien asseblief die <a %(all_isbns)s>oorspronklike blogpos</a> vir meer inligting."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Ons het 'n uitdaging uitgereik om hierop te verbeter. Ons sou 'n eerste plek beloning van $6,000, tweede plek van $3,000, en derde plek van $1,000 toeken. As gevolg van die oorweldigende reaksie en ongelooflike inskrywings, het ons besluit om die prysgeld effens te verhoog, en 'n vierdelige derde plek van $500 elk toe te ken. Die wenners is hieronder, maar maak seker om na al die inskrywings <a %(annas_archive)s>hier</a> te kyk, of laai ons <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a> af."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Eerste plek $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "Hierdie <a %(phiresky_github)s>inskrywing</a> (<a %(annas_archive_note_2951)s>Gitlab kommentaar</a>) is eenvoudig alles wat ons wou hê, en meer! Ons het veral van die ongelooflik buigsame visualiseringsopsies gehou (selfs met ondersteuning vir pasgemaakte shaders), maar met 'n omvattende lys van voorafinstellings. Ons het ook gehou van hoe vinnig en glad alles is, die eenvoudige implementering (wat nie eens 'n agterkant het nie), die slim minimap, en uitgebreide verduideliking in hul <a %(phiresky_github)s>blogpos</a>. Ongelooflike werk, en die welverdiende wenner!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Tweede plek $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Nog 'n ongelooflike <a %(annas_archive_note_2913)s>inskrywing</a>. Nie so buigsaam soos die eerste plek nie, maar ons het eintlik sy makro-vlak visualisering bo die eerste plek verkies (ruimte-vul kurwe, grense, etikettering, beklemtoning, panning, en inzoomen). 'n <a %(annas_archive_note_2971)s>kommentaar</a> deur Joe Davis het by ons aangesluit:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Terwyl perfekte vierkante en reghoeke wiskundig aangenaam is, bied hulle nie beter lokaliteit in 'n kartering konteks nie. Ek glo die asimmetrie inherent in hierdie Hilbert of klassieke Morton is nie 'n fout nie, maar 'n kenmerk. Net soos Italië se beroemde stewelsilhoeët dit onmiddellik herkenbaar op 'n kaart maak, kan die unieke \"quirks\" van hierdie kurwes as kognitiewe bakens dien. Hierdie kenmerkendheid kan ruimtelike geheue verbeter en gebruikers help om hulself te oriënteer, wat moontlik die vind van spesifieke streke of die opmerk van patrone makliker maak.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "En steeds baie opsies vir visualisering en weergawe, sowel as 'n ongelooflik gladde en intuïtiewe UI. 'n Soliede tweede plek!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Derde plek $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "In hierdie <a %(annas_archive_note_2940)s>inskrywing</a> het ons regtig van die verskillende soorte aansigte gehou, veral die vergelyking en uitgewer aansigte."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Derde plek $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Alhoewel nie die mees gepoleerde UI nie, merk hierdie <a %(annas_archive_note_2917)s>inskrywing</a> baie van die bokse af. Ons het veral van sy vergelykingsfunksie gehou."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Derde plek $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Soos die eerste plek, het hierdie <a %(annas_archive_note_2975)s>inskrywing</a> ons beïndruk met sy buigsaamheid. Uiteindelik is dit wat 'n uitstekende visualisering hulpmiddel maak: maksimale buigsaamheid vir kraggebruikers, terwyl dinge eenvoudig gehou word vir gemiddelde gebruikers."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Derde plek $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Die finale <a %(annas_archive_note_2947)s>inskrywing</a> om 'n beloning te kry is redelik basies, maar het 'n paar unieke kenmerke waarvan ons regtig gehou het. Ons het gehou van hoe hulle wys hoeveel datasets 'n spesifieke ISBN dek as 'n maatstaf van gewildheid/betroubaarheid. Ons het ook regtig gehou van die eenvoud maar doeltreffendheid van die gebruik van 'n deursigtigheid-skuifbalk vir vergelykings."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Noemenswaardige idees"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Nog 'n paar idees en implementasies waarvan ons veral gehou het:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Wolkenkrabbers vir skaarsheid"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Lewendige statistieke"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Aantekeninge, en ook lewendige statistieke"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Unieke kaartbeskouing en filters"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Koel verstek kleur skema en hittekaart."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Maklike skakeling van datasets vir vinnige vergelykings."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Pragtige etikette."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Skaalstaaf met aantal boeke."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Baie skuifknoppies om datasets te vergelyk, asof jy 'n DJ is."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Ons kan nog 'n rukkie aangaan, maar kom ons stop hier. Maak seker om na al die inskrywings <a %(annas_archive)s>hier</a> te kyk, of laai ons <a %(a_2025_01_isbn_visualization_files)s>gecombineerde torrent</a> af. Soveel inskrywings, en elkeen bring 'n unieke perspektief, hetsy in UI of implementering."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Ons sal ten minste die eerste plek inskrywing in ons hoofwebwerf inkorporeer, en dalk 'n paar ander. Ons het ook begin dink oor hoe om die proses van identifisering, bevestiging, en dan argivering van die skaarsste boeke te organiseer. Meer hieroor sal volg."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Dankie aan almal wat deelgeneem het. Dit is wonderlik dat soveel mense omgee."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Ons harte is vol dankbaarheid."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Visualisering van Alle ISBNs — $10,000 beloning teen 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Hierdie prentjie verteenwoordig die grootste volledig oop \"lys van boeke\" wat ooit in die geskiedenis van die mensdom saamgestel is."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Hierdie prentjie is 1000×800 pixels. Elke pixel verteenwoordig 2,500 ISBNs. As ons 'n lêer vir 'n ISBN het, maak ons daardie pixel groener. As ons weet 'n ISBN is uitgereik, maar ons het nie 'n ooreenstemmende lêer nie, maak ons dit rooi."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "In minder as 300kb, verteenwoordig hierdie prentjie bondig die grootste volledig oop \"lys van boeke\" wat ooit in die geskiedenis van die mensdom saamgestel is (enkele honderde GB saamgepers in geheel)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Dit wys ook: daar is nog baie werk oor om boeke te rugsteun (ons het net 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Agtergrond"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Hoe kan Anna se Argief sy missie bereik om al die kennis van die mensdom te rugsteun, sonder om te weet watter boeke nog daar buite is? Ons het 'n TODO-lys nodig. Een manier om dit in kaart te bring, is deur ISBN-nommers, wat sedert die 1970's aan elke gepubliseerde boek toegeken is (in die meeste lande)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Daar is geen sentrale owerheid wat al die ISBN-toekennings ken nie. In plaas daarvan is dit 'n verspreide stelsel, waar lande reekse van nommers kry, wat dan kleiner reekse aan groot uitgewers toeken, wat verder reekse aan kleiner uitgewers kan onderverdeel. Uiteindelik word individuele nommers aan boeke toegeken."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Ons het twee jaar gelede begin om ISBN's in kaart te bring met ons skraap van ISBNdb. Sedertdien het ons baie meer metadata-bronne geskraap, soos Worldcat, Google Books, Goodreads, Libby, en meer. 'n Volledige lys kan gevind word op die “Datasets” en “Torrents” bladsye op Anna se Argief. Ons het nou verreweg die grootste volledig oop, maklik aflaaibare versameling van boekmetadata (en dus ISBN's) in die wêreld."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Ons het uitgebreid geskryf oor waarom ons omgee vir bewaring, en waarom ons tans in 'n kritieke venster is. Ons moet nou skaars, ondergefocusde en uniek bedreigde boeke identifiseer en bewaar. Om goeie metadata oor al die boeke in die wêreld te hê, help daarmee."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Visualisering"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Benewens die oorsigbeeld, kan ons ook kyk na individuele datasets wat ons verkry het. Gebruik die aftreklys en knoppies om tussen hulle te skakel."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Daar is baie interessante patrone om in hierdie prente te sien. Hoekom is daar 'n sekere gereeldheid van lyne en blokke, wat op verskillende skale blyk te gebeur? Wat is die leë areas? Hoekom is sekere datasets so saamgegroepeer? Ons sal hierdie vrae as 'n oefening vir die leser laat."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "$10,000 beloning"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Daar is baie om hier te verken, so ons kondig 'n beloning aan vir die verbetering van die visualisering hierbo. Anders as die meeste van ons belonings, is hierdie een tydsbeperk. Jy moet jou oopbron-kode indien teen 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Die beste indiening sal $6,000 kry, tweede plek is $3,000, en derde plek is $1,000. Alle belonings sal met Monero (XMR) toegeken word."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Hieronder is die minimum kriteria. As geen indiening aan die kriteria voldoen nie, mag ons steeds sommige belonings toeken, maar dit sal na ons diskresie wees."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork hierdie repo, en redigeer hierdie blogpos HTML (geen ander agtergronde behalwe ons Flask-agtergrond word toegelaat nie)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Maak die prent hierbo gladde inzoombaar, sodat jy heeltemal na individuele ISBN's kan inzoem. Deur op ISBN's te klik, moet jy na 'n metadata-bladsy of soektog op Anna se Argief geneem word."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Jy moet steeds tussen al die verskillende datasets kan skakel."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Landreekse en uitgewerreekse moet uitgelig word wanneer daar oor gehouer word. Jy kan byvoorbeeld data4info.py in isbnlib gebruik vir landinligting, en ons “isbngrp” skraap vir uitgewers (dataset, torrent)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Dit moet goed werk op beide rekenaar en selfoon."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Vir bonuspunte (hierdie is net idees — laat jou kreatiwiteit vry loop):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Sterk oorweging sal gegee word aan bruikbaarheid en hoe goed dit lyk."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Wys werklike metadata vir individuele ISBN's wanneer jy inzoom, soos titel en outeur."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Beter ruimte-vulkurwe. Byvoorbeeld 'n zig-zag, wat van 0 tot 4 op die eerste ry gaan en dan terug (in omgekeerde) van 5 tot 9 op die tweede ry — herhalend toegepas."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Verskillende of aanpasbare kleurtemas."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Spesiale aansigte vir die vergelyking van datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Maniere om probleme op te los, soos ander metadata wat nie goed ooreenstem nie (bv. baie verskillende titels)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Annotering van beelde met kommentaar oor ISBN's of reekse."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Enige heuristieke vir die identifisering van skaars of bedreigde boeke."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Watter kreatiewe idees jy ook al kan uitdink!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Jy MAG heeltemal afwyk van die minimale kriteria, en 'n heeltemal ander visualisering doen. As dit regtig skouspelagtig is, kwalifiseer dit vir die beloning, maar na ons diskresie."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Maak inskrywings deur 'n kommentaar te plaas op <a %(annas_archive)s>hierdie kwessie</a> met 'n skakel na jou geforkte repo, saamvoegingsversoek, of verskil."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kode"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Die kode om hierdie beelde te genereer, sowel as ander voorbeelde, kan gevind word in <a %(annas_archive)s>hierdie gids</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Ons het 'n kompakte dataformaat ontwikkel, waarmee al die vereiste ISBN-inligting ongeveer 75MB (gekomprimeer) is. Die beskrywing van die dataformaat en kode om dit te genereer kan <a %(annas_archive_l1244_1319)s>hier</a> gevind word. Vir die beloning is jy nie verplig om dit te gebruik nie, maar dit is waarskynlik die gerieflikste formaat om mee te begin. Jy kan ons metadata transformeer soos jy wil (al moet al jou kode oopbron wees)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Ons kan nie wag om te sien wat jy uitdink nie. Sterkte!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Anna se Argiefhouers (AAH): standaardisering van vrystellings van die wêreld se grootste skadubiblioteek"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Anna se Argief het die grootste skadubiblioteek in die wêreld geword, wat vereis dat ons ons vrystellings moet standaardiseer."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Anna se Argief</a> het verreweg die grootste skadubiblioteek in die wêreld geword, en die enigste skadubiblioteek van sy skaal wat volledig oopbron en oopdata is. Hieronder is 'n tabel van ons Datasets-bladsy (effens gewysig):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Ons het dit op drie maniere bereik:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Spieëling van bestaande oopdata skadubiblioteke (soos Sci-Hub en Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Hulp aan skadubiblioteke wat meer oop wil wees, maar nie die tyd of hulpbronne gehad het om dit te doen nie (soos die Libgen strokiesprentversameling)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Afskraap van biblioteke wat nie in grootmaat wil deel nie (soos Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Vir (2) en (3) bestuur ons nou 'n aansienlike versameling torrents self (100s van TBs). Tot dusver het ons hierdie versamelings as eenmalige projekte benader, wat beteken dat daar vir elke versameling 'n spesifieke infrastruktuur en data-organisasie is. Dit voeg beduidende oorhoofse koste by elke vrystelling, en maak dit veral moeilik om meer inkrementele vrystellings te doen."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Daarom het ons besluit om ons vrystellings te standaardiseer. Dit is 'n tegniese blogpos waarin ons ons standaard bekendstel: <strong>Anna se Argiefhouers</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Ontwerpdoelwitte"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Ons primêre gebruiksgeval is die verspreiding van lêers en geassosieerde metadata van verskillende bestaande versamelings. Ons belangrikste oorwegings is:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Heterogene lêers en metadata, so na as moontlik aan die oorspronklike formaat."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Heterogene identifiseerders in die bronbiblioteke, of selfs 'n gebrek aan identifiseerders."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Afsonderlike vrystellings van metadata teenoor lêerdata, of slegs metadata-vrystellings (bv. ons ISBNdb-vrystelling)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Verspreiding deur torrents, alhoewel met die moontlikheid van ander verspreidingsmetodes (bv. IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Onveranderlike rekords, aangesien ons moet aanvaar dat ons torrents vir ewig sal bestaan."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Inkrementele vrystellings / byvoegbare vrystellings."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Masjienleesbaar en skryfbaar, gerieflik en vinnig, veral vir ons stapel (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Redelik maklik vir menslike inspeksie, alhoewel dit sekondêr is tot masjienleesbaarheid."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Maklik om ons versamelings te saai met 'n standaard gehuurde saadkas."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Binêre data kan direk deur webbedieners soos Nginx bedien word."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Sommige nie-doelwitte:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Ons gee nie om dat lêers maklik is om handmatig op skyf te navigeer, of deursoekbaar is sonder voorafverwerking nie."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Ons gee nie om om direk versoenbaar te wees met bestaande biblioteek sagteware nie."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Alhoewel dit maklik moet wees vir enigiemand om ons versameling met torrents te saai, verwag ons nie dat die lêers bruikbaar sal wees sonder beduidende tegniese kennis en toewyding nie."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Aangesien Anna se Argief oopbron is, wil ons ons formaat direk gebruik. Wanneer ons ons soekindeks verfris, het ons slegs toegang tot publiek beskikbare paaie, sodat enigiemand wat ons biblioteek vurk, vinnig kan begin."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Die standaard"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Uiteindelik het ons op 'n relatief eenvoudige standaard besluit. Dit is redelik los, nie-normatief nie, en 'n werk in vordering."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Anna se Argief Houer) is 'n enkele item wat bestaan uit <strong>metadata</strong>, en opsioneel <strong>binêre data</strong>, albei is onveranderlik. Dit het 'n wêreldwyd unieke identifiseerder, genaamd <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Versameling.</strong> Elke AAC behoort aan 'n versameling, wat per definisie 'n lys van AAC's is wat semanties konsekwent is. Dit beteken dat as jy 'n beduidende verandering aan die formaat van die metadata maak, jy 'n nuwe versameling moet skep."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>“rekords” en “lêers” versamelings.</strong> Volgens konvensie is dit dikwels gerieflik om “rekords” en “lêers” as verskillende versamelings vry te stel, sodat hulle op verskillende skedules vrygestel kan word, bv. gebaseer op skraaptempo's. 'n “Rekord” is 'n metadata-slegs versameling, wat inligting soos boek titels, outeurs, ISBN's, ens. bevat, terwyl “lêers” die versamelings is wat die werklike lêers self bevat (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Die formaat van AACID is soos volg: <code style=\"color: #0093ff\">aacid__{versameling}__{ISO 8601 tydstempel}__{versameling-spesifieke ID}__{kortuuid}</code>. Byvoorbeeld, 'n werklike AACID wat ons vrygestel het, is <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{versameling}</code>: die versameling naam, wat ASCII letters, syfers, en onderstrepings kan bevat (maar geen dubbele onderstrepings nie)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 tydstempel}</code>: 'n kort weergawe van die ISO 8601, altyd in UTC, bv. <code>20220723T194746Z</code>. Hierdie nommer moet monotoon toeneem vir elke vrystelling, alhoewel die presiese semantiek per versameling kan verskil. Ons stel voor om die tyd van skraap of van die generering van die ID te gebruik."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{versameling-spesifieke ID}</code>: 'n versameling-spesifieke identifiseerder, indien van toepassing, bv. die Z-Library ID. Mag weggelaat of verkort word. Moet weggelaat of verkort word as die AACID andersins meer as 150 karakters sou wees."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{kortuuid}</code>: 'n UUID maar saamgepers na ASCII, bv. deur base57 te gebruik. Ons gebruik tans die <a %(github_skorokithakis_shortuuid)s>kortuuid</a> Python-biblioteek."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>AACID reeks.</strong> Aangesien AACID's monotoon toenemende tydstempels bevat, kan ons dit gebruik om reekse binne 'n spesifieke versameling aan te dui. Ons gebruik hierdie formaat: <code style=\"color: blue\">aacid__{versameling}__{van_tydstempel}--{tot_tydstempel}</code>, waar die tydstempels inklusief is. Dit is konsekwent met ISO 8601-notasie. Reekse is aaneenlopend, en mag oorvleuel, maar in geval van oorvleueling moet identiese rekords bevat as die een wat voorheen in daardie versameling vrygestel is (aangesien AAC's onveranderlik is). Ontbrekende rekords word nie toegelaat nie."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Metadata lêer.</strong> 'n Metadata lêer bevat die metadata van 'n reeks AAC's, vir een spesifieke versameling. Hierdie het die volgende eienskappe:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Lêernaam moet 'n AACID reeks wees, voorafgegaan deur <code style=\"color: red\">anna_se_argief_meta__</code> en gevolg deur <code>.jsonl.zstd</code>. Byvoorbeeld, een van ons vrystellings word genoem<br><code><span style=\"color: red\">anna_se_argief_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Soos aangedui deur die lêeruitbreiding, is die lêertipe <a %(jsonlines)s>JSON Lines</a> saamgepers met <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Elke JSON-objek moet die volgende velde op die boonste vlak bevat: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (opsioneel). Geen ander velde word toegelaat nie."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> is arbitrêre metadata, volgens die semantiek van die versameling. Dit moet semanties konsekwent binne die versameling wees."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> is opsioneel, en is die naam van die binêre data-lêergids wat die ooreenstemmende binêre data bevat. Die lêernaam van die ooreenstemmende binêre data binne daardie gids is die rekord se AACID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Die <code style=\"color: red\">anna_se_argief_meta__</code> voorvoegsel kan aangepas word na die naam van jou instelling, bv. <code style=\"color: red\">my_instituut_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Binêre data-lêergids.</strong> 'n Lêergids met die binêre data van 'n reeks AAC's, vir een spesifieke versameling. Hierdie het die volgende eienskappe:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Lêergidsnaam moet 'n AACID reeks wees, voorafgegaan deur <code style=\"color: green\">anna_se_argief_data__</code>, en geen agtervoegsel. Byvoorbeeld, een van ons werklike vrystellings het 'n lêergids genaamd<br><code><span style=\"color: green\">anna_se_argief_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Die lêergids moet datalêers vir alle AAC's binne die gespesifiseerde reeks bevat. Elke datalêer moet sy AACID as die lêernaam hê (geen uitbreidings nie)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Dit word aanbeveel om hierdie vouers ietwat hanteerbaar in grootte te maak, bv. nie groter as 100GB-1TB elk nie, alhoewel hierdie aanbeveling oor tyd kan verander."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrents.</strong> Die metadata-lêers en binêre data-vouers kan in torrents gebundel word, met een torrent per metadata-lêer, of een torrent per binêre data-vouer. Die torrents moet die oorspronklike lêer/gidsnaam plus 'n <code>.torrent</code> agtervoegsel as hul lêernaam hê."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Voorbeeld"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Kom ons kyk na ons onlangse Z-Library vrystelling as 'n voorbeeld. Dit bestaan uit twee versamelings: “<span style=\"background: #fffaa3\">zlib3_records</span>” en “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Dit stel ons in staat om metadata-rekords apart van die werklike boeklêers te skraap en vry te stel. As sodanig het ons twee torrents met metadata-lêers vrygestel:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Ons het ook 'n klomp torrents met binêre data-vouers vrygestel, maar slegs vir die “<span style=\"background: #ffd6fe\">zlib3_files</span>” versameling, 62 in totaal:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Deur <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> te laat loop, kan ons sien wat binne is:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "In hierdie geval is dit metadata van 'n boek soos gerapporteer deur Z-Library. Op die boonste vlak het ons slegs “aacid” en “metadata”, maar geen “data_folder” nie, aangesien daar geen ooreenstemmende binêre data is nie. Die AACID bevat “22430000” as die primêre ID, wat ons kan sien is geneem van “zlibrary_id”. Ons kan verwag dat ander AAC's in hierdie versameling dieselfde struktuur sal hê."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Nou laat ons <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> laat loop:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Dit is 'n baie kleiner AAC metadata, alhoewel die grootste deel van hierdie AAC elders in 'n binêre lêer geleë is! Uiteindelik het ons hierdie keer 'n “data_folder”, so ons kan verwag dat die ooreenstemmende binêre data geleë is by <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. Die “metadata” bevat die “zlibrary_id”, so ons kan dit maklik assosieer met die ooreenstemmende AAC in die “zlib_records” versameling. Ons kon op 'n aantal verskillende maniere geassosieer het, bv. deur AACID — die standaard skryf dit nie voor nie."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Let daarop dat dit ook nie nodig is vir die “metadata” veld om self JSON te wees nie. Dit kan 'n string wees wat XML of enige ander dataformaat bevat. U kan selfs metadata-inligting in die geassosieerde binêre blob stoor, bv. as dit baie data is."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Gevolgtrekking"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Met hierdie standaard kan ons vrystellings meer inkrementeel maak, en makliker nuwe databronne byvoeg. Ons het reeds 'n paar opwindende vrystellings in die pyplyn!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Ons hoop ook dat dit makliker word vir ander skadubiblioteke om ons versamelings te spieël. Uiteindelik is ons doel om menslike kennis en kultuur vir ewig te bewaar, so hoe meer redundansie, hoe beter."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Anna se Opdatering: volledig oopbron-argief, ElasticSearch, 300GB+ van boekomslage"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Ons werk dag en nag om 'n goeie alternatief met Anna se Argief te bied. Hier is 'n paar van die dinge wat ons onlangs bereik het."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Met Z-Library wat gesluit is en sy (vermeende) stigters wat gearresteer is, werk ons dag en nag om 'n goeie alternatief met Anna se Argief te bied (ons sal dit nie hier skakel nie, maar jy kan dit Google). Hier is 'n paar van die dinge wat ons onlangs bereik het."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Anna se Argief is volledig oopbron"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Ons glo dat inligting vry moet wees, en ons eie kode is geen uitsondering nie. Ons het al ons kode op ons privaat gehoste Gitlab-instansie vrygestel: <a %(annas_archive)s>Anna se Sagteware</a>. Ons gebruik ook die probleemopsporing om ons werk te organiseer. As jy met ons ontwikkeling wil betrokke raak, is dit 'n goeie plek om te begin."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Om jou 'n voorsmakie te gee van die dinge waaraan ons werk, neem ons onlangse werk aan kliëntkant-prestasieverbeterings. Aangesien ons nog nie paginering geïmplementeer het nie, sou ons dikwels baie lang soekbladsye teruggee, met 100-200 resultate. Ons wou nie die soekresultate te gou afsny nie, maar dit het beteken dat dit sommige toestelle sou vertraag. Hiervoor het ons 'n klein truuk geïmplementeer: ons het die meeste soekresultate in HTML-kommentaar (<code><!-- --></code>) toegedraai, en toe 'n bietjie Javascript geskryf wat sou opspoor wanneer 'n resultaat sigbaar moet word, op watter oomblik ons die kommentaar sou afwikkel:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "DOM \"virtualisering\" geïmplementeer in 23 reëls, geen behoefte aan luukse biblioteke nie! Dit is die soort vinnige pragmatiese kode wat jy eindig met wanneer jy beperkte tyd het, en werklike probleme wat opgelos moet word. Dit is gerapporteer dat ons soektog nou goed werk op stadige toestelle!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Nog 'n groot poging was om die databasis outomaties te bou. Toe ons begin het, het ons net lukraak verskillende bronne saamgevoeg. Nou wil ons hulle op datum hou, so ons het 'n klomp skripte geskryf om nuwe metadata van die twee Library Genesis-vurke af te laai en te integreer. Die doel is om dit nie net nuttig vir ons argief te maak nie, maar om dinge maklik te maak vir enigiemand wat met skadubiblioteek-metadata wil speel. Die doel sou 'n Jupyter-notaboek wees wat allerhande interessante metadata beskikbaar het, sodat ons meer navorsing kan doen soos om uit te vind watter <a %(blog)s>persentasie van ISBNs vir ewig bewaar word</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Uiteindelik het ons ons donasiestelsel herontwerp. Jy kan nou 'n kredietkaart gebruik om direk geld in ons kripto-beursies te deponeer, sonder om regtig iets van kriptogeldeenhede te weet. Ons sal aanhou monitor hoe goed dit in die praktyk werk, maar dit is 'n groot saak."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Skakel oor na ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Een van ons <a %(annas_archive)s>kaartjies</a> was 'n versameling van kwessies met ons soekstelsel. Ons het MySQL vol-tekst soektog gebruik, aangesien ons al ons data in MySQL gehad het. Maar dit het sy beperkings gehad:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Sommige navrae het super lank geneem, tot op die punt waar hulle al die oop verbindings sou beset."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "MySQL het standaard 'n minimum woordlengte, of jou indeks kan regtig groot word. Mense het gerapporteer dat hulle nie kon soek vir “Ben Hur” nie."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Soektog was net ietwat vinnig wanneer dit volledig in geheue gelaai is, wat vereis het dat ons 'n duurder masjien moes kry om dit op te laat loop, plus 'n paar opdragte om die indeks by opstart vooraf te laai."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Ons sou dit nie maklik kon uitbrei om nuwe funksies te bou nie, soos beter <a %(wikipedia_cjk_characters)s>tokenisering vir nie-witspasie tale</a>, filter/fasettering, sortering, \"het jy bedoel\" voorstelle, outovoltooiing, ensovoorts."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Na gesprekke met 'n klomp kundiges, het ons op ElasticSearch besluit. Dit was nie perfek nie (hulle standaard \"het jy bedoel\" voorstelle en outovoltooiing funksies is swak), maar oor die algemeen was dit baie beter as MySQL vir soektog. Ons is steeds nie <a %(youtube)s>te gretig</a> om dit vir enige missie-kritieke data te gebruik nie (alhoewel hulle baie <a %(elastic_co)s>vordering</a> gemaak het), maar oor die algemeen is ons redelik tevrede met die oorskakeling."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Vir nou het ons 'n baie vinniger soektog geïmplementeer, beter taalondersteuning, beter relevansie sortering, verskillende sorteeropsies, en filter op taal/boektipe/lêertipe. As jy nuuskierig is hoe dit werk, <a %(annas_archive_l140)s>kyk</a> <a %(annas_archive_l1115)s>gerus</a> <a %(annas_archive_l1635)s>daarna</a>. Dit is redelik toeganklik, alhoewel dit 'n paar meer kommentare kan gebruik…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ van boekomslags vrygestel"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Uiteindelik is ons bly om 'n klein vrystelling aan te kondig. In samewerking met die mense wat die Libgen.rs-vurk bedryf, deel ons al hul boekomslags deur torrents en IPFS. Dit sal die las van die besigtiging van die omslags oor meer masjiene versprei, en hulle beter bewaar. In baie (maar nie alle) gevalle is die boekomslags in die lêers self ingesluit, so dit is 'n soort van \"afgeleide data\". Maar om dit in IPFS te hê, is steeds baie nuttig vir die daaglikse werking van beide Anna se Argief en die verskillende Library Genesis-vurke."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Soos gewoonlik kan jy hierdie vrystelling by die Pirate Library Mirror vind (WYSIG: geskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>). Ons sal nie hierna skakel nie, maar jy kan dit maklik vind."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Hopelik kan ons ons pas 'n bietjie verslap, nou dat ons 'n ordentlike alternatief vir Z-Library het. Hierdie werklading is nie besonder volhoubaar nie. As jy belangstel om te help met programmering, bedienerbedrywighede, of bewaringswerk, kontak ons gerus. Daar is steeds baie <a %(annas_archive)s>werk om te doen</a>. Dankie vir jou belangstelling en ondersteuning."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Anna se Argief het die wêreld se grootste strokiesprente skadubiblioteek gerugsteun (95TB) — jy kan help om dit te saai"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Die grootste strokiesprente skadubiblioteek in die wêreld het 'n enkele punt van mislukking gehad.. tot vandag."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Bespreek op Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Die grootste skadubiblioteek van strokiesprente is waarskynlik dié van 'n spesifieke Library Genesis-vurk: Libgen.li. Die een administrateur wat daardie webwerf bestuur het, het daarin geslaag om 'n ongelooflike strokiesprenteversameling van meer as 2 miljoen lêers te versamel, wat meer as 95TB beloop. Anders as ander Library Genesis-versamelings, was hierdie een egter nie in grootmaat beskikbaar deur torrents nie. Jy kon hierdie strokiesprente slegs individueel deur sy stadige persoonlike bediener verkry — 'n enkele punt van mislukking. Tot vandag!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "In hierdie pos gaan ons jou meer vertel oor hierdie versameling, en oor ons fondsinsameling om meer van hierdie werk te ondersteun."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon probeer haarself in die alledaagse wêreld van die biblioteek te verloor…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Libgen-vurke"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Eers, 'n bietjie agtergrond. Jy mag Library Genesis ken vir hul epiese boekversameling. Minder mense weet dat Library Genesis-vrywilligers ander projekte geskep het, soos 'n aansienlike versameling van tydskrifte en standaarddokumente, 'n volledige rugsteun van Sci-Hub (in samewerking met die stigter van Sci-Hub, Alexandra Elbakyan), en inderdaad, 'n massiewe versameling strokiesprente."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Op 'n stadium het verskillende operateurs van Library Genesis-spieëls hul eie paaie gegaan, wat aanleiding gegee het tot die huidige situasie van 'n aantal verskillende \"vurke\", wat almal steeds die naam Library Genesis dra. Die Libgen.li-vurk het uniek hierdie strokiesprentversameling, sowel as 'n aansienlike tydskrifversameling (waaraan ons ook werk)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Samewerking"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Gegewe die grootte daarvan, was hierdie versameling lank op ons wenslys, so na ons sukses met die rugsteun van Z-Library, het ons ons visier op hierdie versameling gestel. Aanvanklik het ons dit direk geskraap, wat nogal 'n uitdaging was, aangesien hul bediener nie in die beste toestand was nie. Ons het op hierdie manier ongeveer 15TB gekry, maar dit was stadig."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Gelukkig het ons daarin geslaag om in aanraking te kom met die operateur van die biblioteek, wat ingestem het om al die data direk aan ons te stuur, wat baie vinniger was. Dit het steeds meer as 'n halfjaar geneem om al die data oor te dra en te verwerk, en ons het amper alles verloor weens skyfkorruptie, wat sou beteken het dat ons van voor af moes begin."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Hierdie ervaring het ons laat glo dat dit belangrik is om hierdie data so vinnig as moontlik daar buite te kry, sodat dit wyd en syd gespieël kan word. Ons is net een of twee ongelukkige tydsgebeurtenisse weg van die verlies van hierdie versameling vir altyd!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Die versameling"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Om vinnig te beweeg beteken wel dat die versameling 'n bietjie ongeorganiseerd is… Kom ons kyk. Stel jou voor ons het 'n lêerstelsel (wat ons in werklikheid oor torrents verdeel):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Die eerste gids, <code>/repository</code>, is die meer gestruktureerde deel hiervan. Hierdie gids bevat sogenaamde “duisend gids”: gidse elk met 'n duisend lêers, wat inkrementeel in die databasis genommer is. Gids <code>0</code> bevat lêers met comic_id 0–999, en so aan."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Dit is dieselfde skema wat Library Genesis gebruik het vir sy fiksie- en nie-fiksieversamelings. Die idee is dat elke “duisend gids” outomaties in 'n torrent verander word sodra dit vol is."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Die Libgen.li-operateur het egter nooit torrents vir hierdie versameling gemaak nie, en dus het die duisend gidse waarskynlik ongerieflik geword, en plek gemaak vir “ongesorteerde gidse”. Dit is <code>/comics0</code> tot <code>/comics4</code>. Hulle bevat almal unieke gidsstrukture, wat waarskynlik sin gemaak het vir die versameling van die lêers, maar maak nou nie veel sin vir ons nie. Gelukkig verwys die metadata steeds direk na al hierdie lêers, so hul bergingsorganisasie op skyf maak eintlik nie saak nie!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Die metadata is beskikbaar in die vorm van 'n MySQL-databasis. Dit kan direk van die Libgen.li-webwerf afgelaai word, maar ons sal dit ook in 'n torrent beskikbaar stel, saam met ons eie tabel met al die MD5-hashes."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analise"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Wanneer jy 95TB in jou bergingskluster gestort kry, probeer jy sin maak van wat selfs daarin is… Ons het 'n bietjie analise gedoen om te sien of ons die grootte 'n bietjie kon verminder, soos deur duplikate te verwyder. Hier is 'n paar van ons bevindings:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Semantiese duplikate (verskillende skanderings van dieselfde boek) kan teoreties uitgefilter word, maar dit is lastig. Wanneer ons handmatig deur die strokiesprente gekyk het, het ons te veel vals positiewe gevind."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Daar is 'n paar duplikate suiwer deur MD5, wat relatief vermorsend is, maar om dit uit te filter sou ons net ongeveer 1% in besparing gee. Op hierdie skaal is dit steeds ongeveer 1TB, maar ook, op hierdie skaal maak 1TB nie regtig saak nie. Ons wil eerder nie die risiko loop om per ongeluk data in hierdie proses te vernietig nie."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Ons het 'n klomp nie-boekdata gevind, soos flieks gebaseer op strokiesprente. Dit lyk ook vermorsend, aangesien hierdie reeds wyd beskikbaar is deur ander middele. Ons het egter besef dat ons nie net flieklêers kon uitfilter nie, aangesien daar ook <em>interaktiewe strokiesprente</em> is wat op die rekenaar vrygestel is, wat iemand opgeneem en as flieks gestoor het."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Uiteindelik sou enigiets wat ons uit die versameling kon verwyder, slegs 'n paar persent bespaar. Toe onthou ons dat ons data-hamsteraars is, en die mense wat dit gaan spieël, is ook data-hamsteraars, en so, “WAT BEDOEL JY, VERWYDER?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Ons bied dus aan u die volledige, ongewysigde versameling. Dit is baie data, maar ons hoop genoeg mense sal omgee om dit tog te saai."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Insamelingsveldtog"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Ons stel hierdie data in groot stukke vry. Die eerste torrent is van <code>/comics0</code>, wat ons in een groot 12TB .tar-lêer geplaas het. Dit is beter vir jou hardeskyf en torrent-sagteware as 'n menigte kleiner lêers."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "As deel van hierdie vrystelling hou ons 'n insamelingsveldtog. Ons poog om $20,000 in te samel om operasionele en kontrakteringskoste vir hierdie versameling te dek, sowel as om deurlopende en toekomstige projekte moontlik te maak. Ons het 'n paar <em>massiewe</em> projekte in die vooruitsig."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Wie ondersteun ek met my donasie?</em> In kort: ons rugsteun alle kennis en kultuur van die mensdom en maak dit maklik toeganklik. Al ons kode en data is oopbron, ons is 'n heeltemal vrywilliger-gedrewe projek, en ons het reeds 125TB se boeke gered (benewens Libgen en Scihub se bestaande torrents). Uiteindelik bou ons 'n vliegwiel wat mense aanmoedig en motiveer om al die boeke in die wêreld te vind, te skandeer en te rugsteun. Ons sal in 'n toekomstige pos oor ons meesterplan skryf. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "As jy 'n 12-maande “Amazing Archivist” lidmaatskap ($780) skenk, kry jy die kans om <strong>“'n torrent aan te neem”</strong>, wat beteken dat ons jou gebruikersnaam of boodskap in die lêernaam van een van die torrents sal plaas!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Jy kan skenk deur na <a %(wikipedia_annas_archive)s>Anna se Argief</a> te gaan en op die “Skenk” knoppie te klik. Ons soek ook meer vrywilligers: sagteware-ingenieurs, sekuriteitsnavorsers, anonieme handelkenners, en vertalers. Jy kan ons ook ondersteun deur gasheerdienste te verskaf. En natuurlik, saai asseblief ons torrents!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Dankie aan almal wat ons al so ruimhartig ondersteun het! Julle maak werklik 'n verskil."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Hier is die torrents wat tot dusver vrygestel is (ons verwerk nog die res):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Alle torrents kan gevind word op <a %(wikipedia_annas_archive)s>Anna se Argief</a> onder “Datasets” (ons skakel nie direk daarheen nie, sodat skakels na hierdie blog nie van Reddit, Twitter, ens. verwyder word nie). Van daar af, volg die skakel na die Tor-webwerf."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Wat is volgende?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "'n Klomp torrents is wonderlik vir langtermynbewaring, maar nie soseer vir alledaagse toegang nie. Ons sal saam met gasheermaatskappye werk om al hierdie data op die web te kry (aangesien Anna se Argief niks direk huisves nie). Natuurlik sal jy hierdie aflaaiskakels op Anna se Argief kan vind."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Ons nooi ook almal uit om iets met hierdie data te doen! Help ons om dit beter te analiseer, te dedupliseer, op IPFS te plaas, dit te remiks, jou KI-modelle daarmee op te lei, ensovoorts. Dit is alles joune, en ons kan nie wag om te sien wat jy daarmee doen nie."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Laastens, soos voorheen gesê, het ons nog 'n paar massiewe vrystellings wat op pad is (as <em>iemand</em> per ongeluk vir ons 'n dump van 'n <em>sekere</em> ACS4-databasis kan stuur, weet jy waar om ons te vind...), sowel as om die vliegwiel te bou vir die rugsteun van al die boeke in die wêreld."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Bly dus ingeskakel, ons het net begin."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x nuwe boeke bygevoeg tot die Pirate Library Mirror (+24TB, 3.8 miljoen boeke)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "In die oorspronklike vrystelling van die Pirate Library Mirror (EDIT: geskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>), het ons 'n spieël van Z-Library gemaak, 'n groot onwettige boekversameling. As 'n herinnering, dit is wat ons in daardie oorspronklike blogpos geskryf het:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library is 'n gewilde (en onwettige) biblioteek. Hulle het die Library Genesis-versameling geneem en dit maklik deursoekbaar gemaak. Boonop het hulle baie effektief geword in die verkryging van nuwe boekbydraes deur bydraende gebruikers met verskeie voordele te beloon. Hulle dra tans nie hierdie nuwe boeke terug na Library Genesis nie. En anders as Library Genesis, maak hulle nie hul versameling maklik spieëlbaar nie, wat wye bewaring voorkom. Dit is belangrik vir hul sakemodel, aangesien hulle geld vra vir toegang tot hul versameling in grootmaat (meer as 10 boeke per dag)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Ons maak nie morele oordele oor die hef van geld vir grootmaat toegang tot 'n onwettige boekversameling nie. Dit is ongetwyfeld dat die Z-Biblioteek suksesvol was in die uitbreiding van toegang tot kennis en die verkryging van meer boeke. Ons is eenvoudig hier om ons deel te doen: om die langtermynbewaring van hierdie private versameling te verseker."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Daardie versameling dateer terug na middel-2021. Intussen het die Z-Biblioteek teen 'n verstommende tempo gegroei: hulle het ongeveer 3.8 miljoen nuwe boeke bygevoeg. Daar is sekerlik 'n paar duplikate daarin, maar die meerderheid daarvan blyk wettiglik nuwe boeke te wees, of hoër kwaliteit skanderings van voorheen ingediende boeke. Dit is grootliks te danke aan die verhoogde aantal vrywillige moderators by die Z-Biblioteek, en hul grootmaat-oplaaistelsel met deduplikasie. Ons wil hulle graag gelukwens met hierdie prestasies."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Ons is bly om aan te kondig dat ons al die boeke gekry het wat by die Z-Biblioteek gevoeg is tussen ons laaste spieël en Augustus 2022. Ons het ook teruggegaan en 'n paar boeke geskrap wat ons die eerste keer gemis het. Altesaam is hierdie nuwe versameling ongeveer 24TB, wat baie groter is as die vorige een (7TB). Ons spieël is nou altesaam 31TB. Weereens het ons teen Library Genesis gededupliseer, aangesien daar reeds torrents beskikbaar is vir daardie versameling."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Gaan asseblief na die Pirate Library Mirror om die nuwe versameling te bekyk (WYSIG: verskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>). Daar is meer inligting daar oor hoe die lêers gestruktureer is, en wat sedert die laaste keer verander het. Ons sal nie van hier af daarna skakel nie, aangesien dit net 'n blogwebwerf is wat nie enige onwettige materiaal huisves nie."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Natuurlik is saai ook 'n wonderlike manier om ons te help. Dankie aan almal wat ons vorige stel torrents saai. Ons is dankbaar vir die positiewe reaksie, en bly dat daar soveel mense is wat omgee vir die bewaring van kennis en kultuur op hierdie ongewone manier."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Hoe om 'n seerower-argivaris te word"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Die eerste uitdaging mag 'n verrassende een wees. Dit is nie 'n tegniese probleem, of 'n wettige probleem nie. Dit is 'n sielkundige probleem."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Voordat ons begin, twee opdaterings oor die Pirate Library Mirror (WYSIG: verskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Ons het 'n paar uiters ruim skenkings ontvang. Die eerste was $10k van die anonieme individu wat ook \"bookwarrior\", die oorspronklike stigter van Library Genesis, ondersteun het. Spesiale dank aan bookwarrior vir die fasilitering van hierdie skenking. Die tweede was nog $10k van 'n anonieme skenker, wat na ons laaste vrystelling in aanraking gekom het en geïnspireer was om te help. Ons het ook 'n aantal kleiner skenkings gehad. Baie dankie vir al julle ruim ondersteuning. Ons het 'n paar opwindende nuwe projekte in die pyplyn wat dit sal ondersteun, so bly ingeskakel."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Ons het 'n paar tegniese probleme gehad met die grootte van ons tweede vrystelling, maar ons torrents is nou op en saai. Ons het ook 'n ruim aanbod van 'n anonieme individu gekry om ons versameling op hul baie hoëspoed-bedieners te saai, so ons doen 'n spesiale oplaai na hul masjiene, waarna almal anders wat die versameling aflaai 'n groot verbetering in spoed behoort te sien."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Hele boeke kan geskryf word oor die <em>waarom</em> van digitale bewaring in die algemeen, en seerower-argivisme in die besonder, maar laat ons 'n vinnige inleiding gee vir diegene wat nie te vertroud is nie. Die wêreld produseer meer kennis en kultuur as ooit tevore, maar ook meer daarvan gaan verlore as ooit tevore. Die mensdom vertrou grootliks op korporasies soos akademiese uitgewers, stroomdienste, en sosiale media maatskappye met hierdie erfenis, en hulle het dikwels nie bewys dat hulle goeie bewaarders is nie. Kyk na die dokumentêr Digital Amnesia, of regtig enige praatjie deur Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Daar is 'n paar instellings wat 'n goeie werk doen om soveel as moontlik te argiveer, maar hulle is gebonde aan die wet. As seerowers is ons in 'n unieke posisie om versamelings te argiveer wat hulle nie kan aanraak nie, weens kopieregafdwinging of ander beperkings. Ons kan ook versamelings baie keer oor die wêreld spieël, wat die kanse op behoorlike bewaring verhoog."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Vir nou gaan ons nie in gesprekke oor die voor- en nadele van intellektuele eiendom, die moraliteit van die oortreding van die wet, bespiegelinge oor sensuur, of die kwessie van toegang tot kennis en kultuur nie. Met dit alles uit die weg, laat ons in die <em>hoe</em> duik. Ons sal deel hoe ons span seerower-argivarisse geword het, en die lesse wat ons langs die pad geleer het. Daar is baie uitdagings wanneer jy hierdie reis aanpak, en hopelik kan ons jou deur sommige van hulle help."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Gemeenskap"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Die eerste uitdaging mag 'n verrassende een wees. Dit is nie 'n tegniese probleem, of 'n wettige probleem nie. Dit is 'n sielkundige probleem: om hierdie werk in die skaduwees te doen, kan ongelooflik eensaam wees. Afhangende van wat jy beplan om te doen, en jou bedreigingsmodel, mag jy baie versigtig moet wees. Aan die een kant van die spektrum het ons mense soos Alexandra Elbakyan*, die stigter van Sci-Hub, wat baie openlik is oor haar aktiwiteite. Maar sy is in groot gevaar om gearresteer te word as sy op hierdie stadium 'n Westerse land sou besoek, en kan dekades se tronkstraf in die gesig staar. Is dit 'n risiko wat jy bereid sou wees om te neem? Ons is aan die ander kant van die spektrum; baie versigtig om geen spoor agter te laat nie, en sterk operasionele sekuriteit te hê."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Soos genoem op HN deur \"ynno\", wou Alexandra aanvanklik nie bekend wees nie: \"Haar bedieners was opgestel om gedetailleerde foutboodskappe van PHP uit te stuur, insluitend die volle pad van die foutiewe bronlêer, wat onder die gids /home/<USER>'n gebruikersnaam opgespoor kon word wat sy aanlyn op 'n onverwante webwerf gehad het, gekoppel aan haar regte naam. Voor hierdie onthulling was sy anoniem.\" Gebruik dus ewekansige gebruikersname op die rekenaars wat jy vir hierdie dinge gebruik, ingeval jy iets verkeerd konfigureer."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Daardie geheimhouding kom egter met 'n sielkundige koste. Die meeste mense hou daarvan om erkenning te kry vir die werk wat hulle doen, en tog kan jy geen krediet daarvoor in die regte lewe neem nie. Selfs eenvoudige dinge kan uitdagend wees, soos vriende wat jou vra waarmee jy besig was (op 'n stadium raak \"karring met my NAS / homelab\" oud)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Dit is hoekom dit so belangrik is om 'n gemeenskap te vind. Jy kan 'n bietjie operasionele sekuriteit prysgee deur in 'n paar baie naby vriende te vertrou, wat jy weet jy kan diep vertrou. Selfs dan moet jy versigtig wees om niks op skrif te stel nie, ingeval hulle hul e-posse aan die owerhede moet oorhandig, of as hul toestelle op 'n ander manier gekompromitteer is."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Nog beter is om 'n paar mede-seerowers te vind. As jou naby vriende belangstel om by jou aan te sluit, wonderlik! Andersins, mag jy ander aanlyn kan vind. Ongelukkig is dit steeds 'n nisgemeenskap. Tot dusver het ons net 'n handjievol ander gevind wat aktief in hierdie ruimte is. Goeie beginplekke blyk die Library Genesis-forums en r/DataHoarder te wees. Die Archive Team het ook eendersdenkende individue, alhoewel hulle binne die wet opereer (selfs al is dit in sommige grys areas van die wet). Die tradisionele \"warez\" en seerower-tonele het ook mense wat op soortgelyke maniere dink."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Ons is oop vir idees oor hoe om gemeenskap te bevorder en idees te verken. Voel vry om ons op Twitter of Reddit te kontak. Miskien kan ons 'n soort forum of geselsgroep aanbied. Een uitdaging is dat dit maklik gesensor kan word wanneer algemene platforms gebruik word, so ons sal dit self moet aanbied. Daar is ook 'n afweging tussen om hierdie besprekings heeltemal openbaar te maak (meer potensiële betrokkenheid) teenoor om dit privaat te maak (om nie potensiële \"teikens\" te laat weet dat ons op die punt staan om hulle te skraap nie). Ons sal daaroor moet nadink. Laat weet ons as u hierin belangstel!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projekte"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Wanneer ons 'n projek doen, het dit 'n paar fases:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Domein seleksie / filosofie: Waar wil jy ongeveer fokus, en hoekom? Wat is jou unieke passies, vaardighede en omstandighede wat jy tot jou voordeel kan gebruik?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Teiken seleksie: Watter spesifieke versameling gaan jy spieël?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Metadata skraap: Katalogisering van inligting oor die lêers, sonder om die (dikwels baie groter) lêers self af te laai."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Data seleksie: Gebaseer op die metadata, om te bepaal watter data tans die mees relevant is om te argiveer. Dit kan alles wees, maar daar is dikwels 'n redelike manier om ruimte en bandwydte te bespaar."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Data skraap: Om die data werklik te kry."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Verspreiding: Om dit in torrents te verpak, dit iewers aan te kondig, mense te kry om dit te versprei."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Hierdie is nie heeltemal onafhanklike fases nie, en dikwels stuur insigte van 'n latere fase jou terug na 'n vroeëre fase. Byvoorbeeld, tydens metadata skraap kan jy besef dat die teiken wat jy gekies het, verdedigingsmeganismes het wat jou vaardigheidsvlak oorskry (soos IP-blokke), so jy gaan terug en vind 'n ander teiken."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Domein seleksie / filosofie"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Daar is geen tekort aan kennis en kulturele erfenis om te red nie, wat oorweldigend kan wees. Daarom is dit dikwels nuttig om 'n oomblik te neem en te dink oor wat jou bydrae kan wees."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Almal het 'n ander manier van dink oor hierdie, maar hier is 'n paar vrae wat jy jouself kan vra:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Hoekom stel jy hierin belang? Waaroor is jy passievol? As ons 'n klomp mense kan kry wat almal die soort dinge argiveer wat hulle spesifiek omgee, sal dit baie dek! Jy sal baie meer weet as die gemiddelde persoon oor jou passie, soos wat belangrike data is om te red, wat die beste versamelings en aanlyn gemeenskappe is, ensovoorts."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Watter vaardighede het jy wat jy tot jou voordeel kan gebruik? Byvoorbeeld, as jy 'n aanlyn sekuriteit kenner is, kan jy maniere vind om IP-blokke vir veilige teikens te oorwin. As jy goed is met die organiseer van gemeenskappe, kan jy dalk 'n paar mense rondom 'n doel bymekaar bring. Dit is nuttig om 'n bietjie programmering te ken, al is dit net om goeie operasionele sekuriteit deur hierdie proses te handhaaf."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Hoeveel tyd het jy hiervoor? Ons advies sou wees om klein te begin en groter projekte te doen soos jy die slag daarvan kry, maar dit kan alles-verterend raak."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Wat sou 'n hoë-hefboom area wees om op te fokus? As jy X ure gaan spandeer aan seerower argivering, hoe kan jy die grootste \"waarde vir jou geld\" kry?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Wat is unieke maniere waarop jy hieroor dink? Jy mag dalk interessante idees of benaderings hê wat ander dalk gemis het."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "In ons geval het ons veral omgegee oor die langtermyn bewaring van wetenskap. Ons het geweet van Library Genesis, en hoe dit heeltemal gespiegeld is baie keer oor deur torrents. Ons het daardie idee liefgehad. Toe een dag, het een van ons probeer om 'n paar wetenskaplike handboeke op Library Genesis te vind, maar kon hulle nie vind nie, wat twyfel laat ontstaan het oor hoe volledig dit regtig was. Ons het toe daardie handboeke aanlyn gesoek, en hulle op ander plekke gevind, wat die saad vir ons projek geplant het. Selfs voordat ons van die Z-Library geweet het, het ons die idee gehad om nie al daardie boeke handmatig te probeer versamel nie, maar om te fokus op die spieëling van bestaande versamelings, en om hulle terug te dra by Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Teiken seleksie"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "So, ons het ons area waarna ons kyk, nou watter spesifieke versameling moet ons spieël? Daar is 'n paar dinge wat 'n goeie teiken maak:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Groot"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Uniek: nie reeds goed gedek deur ander projekte nie."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Toeganklik: gebruik nie baie lae van beskerming om te verhoed dat jy hul metadata en data afskraap nie."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Spesiale insig: jy het spesiale inligting oor hierdie teiken, soos dat jy op een of ander manier spesiale toegang tot hierdie versameling het, of jy het uitgevind hoe om hul verdediging te oorkom. Dit is nie nodig nie (ons komende projek doen niks spesiaals nie), maar dit help beslis!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Toe ons ons wetenskaplike handboeke op webwerwe anders as Library Genesis gevind het, het ons probeer uitvind hoe hulle hul pad na die internet gemaak het. Ons het toe die Z-Library gevind, en besef dat hoewel die meeste boeke nie daar hul eerste verskyning maak nie, hulle uiteindelik daar beland. Ons het geleer oor sy verhouding met Library Genesis, en die (finansiële) aansporingstruktuur en superieure gebruikerskoppelvlak, wat albei dit 'n baie meer volledige versameling gemaak het. Ons het toe 'n paar voorlopige metadata en data afskraap gedoen, en besef dat ons hul IP aflaai perke kon omseil, deur een van ons lede se spesiale toegang tot baie proxy-bedieners te benut."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Terwyl jy verskillende teikens verken, is dit reeds belangrik om jou spore te verberg deur VPN's en weggooibare e-posadresse te gebruik, waaroor ons later meer sal praat."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Metadata afskraap"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Kom ons raak 'n bietjie meer tegnies hier. Vir die werklike afskraap van die metadata van webwerwe, het ons dinge redelik eenvoudig gehou. Ons gebruik Python-skripte, soms curl, en 'n MySQL-databasis om die resultate in te stoor. Ons het nie enige fancy afskraap sagteware gebruik wat komplekse webwerwe kan karteer nie, aangesien ons tot dusver net een of twee soorte bladsye moes afskraap deur net deur id's te lys en die HTML te ontleed. As daar nie maklik gelysde bladsye is nie, dan mag jy 'n behoorlike crawler nodig hê wat probeer om alle bladsye te vind."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Voordat jy 'n hele webwerf begin afskraap, probeer dit eers 'n bietjie handmatig. Gaan self deur 'n paar dosyn bladsye, om 'n gevoel te kry vir hoe dit werk. Soms sal jy reeds op IP-blokke of ander interessante gedrag afkom op hierdie manier. Dieselfde geld vir data afskraap: voordat jy te diep in hierdie teiken ingaan, maak seker dat jy sy data effektief kan aflaai."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Om beperkings te omseil, is daar 'n paar dinge wat jy kan probeer. Is daar enige ander IP-adresse of bedieners wat dieselfde data huisves maar nie dieselfde beperkings het nie? Is daar enige API-eindpunte wat nie beperkings het nie, terwyl ander wel het? Teen watter aflaai tempo word jou IP geblokkeer, en vir hoe lank? Of word jy nie geblokkeer nie, maar vertraag? Wat as jy 'n gebruikersrekening skep, hoe verander dinge dan? Kan jy HTTP/2 gebruik om verbindings oop te hou, en verhoog dit die tempo waarteen jy bladsye kan versoek? Is daar bladsye wat verskeie lêers gelyk lys, en is die inligting wat daar gelys word voldoende?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Dinge wat jy waarskynlik wil stoor, sluit in:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Titel"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Lêernaam / ligging"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: kan 'n interne ID wees, maar ID's soos ISBN of DOI is ook nuttig."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Grootte: om te bereken hoeveel skyfspasie jy nodig het."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): om te bevestig dat jy die lêer behoorlik afgelaai het."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Datum bygevoeg/verander: sodat jy later kan terugkom en lêers aflaai wat jy nie voorheen afgelaai het nie (alhoewel jy dikwels ook die ID of hash hiervoor kan gebruik)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Beskrywing, kategorie, etikette, outeurs, taal, ens."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Ons doen dit gewoonlik in twee fases. Eerstens laai ons die rou HTML-lêers af, gewoonlik direk in MySQL (om baie klein lêers te vermy, waaroor ons hieronder meer praat). Dan, in 'n aparte stap, gaan ons deur daardie HTML-lêers en ontleed hulle in werklike MySQL-tabelle. Op hierdie manier hoef jy nie alles van nuuts af weer af te laai as jy 'n fout in jou ontledingskode ontdek nie, aangesien jy net die HTML-lêers met die nuwe kode kan herverwerk. Dit is ook dikwels makliker om die verwerkingsstap te paralleliseer, wat dus tyd bespaar (en jy kan die verwerkingskode skryf terwyl die afskraap aan die gang is, in plaas daarvan om albei stappe gelyktydig te moet skryf)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Uiteindelik, let daarop dat vir sommige teikens metadata-skraap al is wat daar is. Daar is 'n paar groot metadata-versamelings daar buite wat nie behoorlik bewaar word nie."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Dataseleksie"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Dikwels kan jy die metadata gebruik om 'n redelike substel van data te bepaal om af te laai. Selfs as jy uiteindelik al die data wil aflaai, kan dit nuttig wees om die belangrikste items eerste te prioritiseer, ingeval jy opgespoor word en verdediging verbeter word, of omdat jy meer skywe moet koop, of bloot omdat iets anders in jou lewe opduik voordat jy alles kan aflaai."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Byvoorbeeld, 'n versameling mag verskeie uitgawes van dieselfde onderliggende bron hê (soos 'n boek of 'n film), waar een gemerk is as die beste kwaliteit. Om daardie uitgawes eerste te stoor, sou baie sin maak. Jy mag uiteindelik al die uitgawes wil stoor, aangesien in sommige gevalle die metadata verkeerd gemerk kan wees, of daar onbekende kompromieë tussen uitgawes kan wees (byvoorbeeld, die \"beste uitgawe\" mag in meeste opsigte die beste wees, maar slegter in ander opsigte, soos 'n film wat 'n hoër resolusie het maar ondertitels ontbreek)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Jy kan ook jou metadata-databasis deursoek om interessante dinge te vind. Wat is die grootste lêer wat gehost word, en hoekom is dit so groot? Wat is die kleinste lêer? Is daar interessante of onverwagte patrone wanneer dit kom by sekere kategorieë, tale, ensovoorts? Is daar duplikaat of baie soortgelyke titels? Is daar patrone oor wanneer data bygevoeg is, soos een dag waarop baie lêers gelyktydig bygevoeg is? Jy kan dikwels baie leer deur op verskillende maniere na die datastel te kyk."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "In ons geval het ons Z-Library-boeke gededupliseer teen die md5-hashes in Library Genesis, en sodoende baie aflaaityd en skyfspasie bespaar. Dit is egter 'n redelik unieke situasie. In meeste gevalle is daar geen omvattende databasisse van watter lêers reeds behoorlik bewaar word deur mede-pirate nie. Dit is op sigself 'n groot geleentheid vir iemand daar buite. Dit sou wonderlik wees om 'n gereeld bygewerkte oorsig te hê van dinge soos musiek en films wat reeds wyd gesaai word op torrent-webwerwe, en is dus laer prioriteit om in piratiespiegels in te sluit."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Data-skraap"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Nou is jy gereed om die data in grootmaat af te laai. Soos voorheen genoem, op hierdie punt behoort jy reeds 'n klomp lêers handmatig afgelaai te hê, om die gedrag en beperkings van die teiken beter te verstaan. Daar sal egter steeds verrassings wees wanneer jy eintlik baie lêers gelyktydig begin aflaai."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Ons raad hier is hoofsaaklik om dit eenvoudig te hou. Begin deur net 'n klomp lêers af te laai. Jy kan Python gebruik, en dan uitbrei na veelvuldige drade. Maar soms is dit selfs eenvoudiger om Bash-lêers direk vanaf die databasis te genereer, en dan veelvuldige daarvan in veelvuldige terminale vensters te laat loop om op te skaal. 'n Vinnige tegniese truuk wat hier die moeite werd is om te noem, is om OUTFILE in MySQL te gebruik, wat jy enige plek kan skryf as jy \"secure_file_priv\" in mysqld.cnf deaktiveer (en wees seker om ook AppArmor te deaktiveer/oorheers as jy op Linux is)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Ons stoor die data op eenvoudige hardeskywe. Begin met wat jy ook al het, en brei stadig uit. Dit kan oorweldigend wees om te dink oor die stoor van honderde TBs data. As dit die situasie is wat jy in die gesig staar, plaas net eers 'n goeie substel uit, en vra in jou aankondiging vir hulp om die res te stoor. As jy wel meer hardeskywe self wil kry, dan het r/DataHoarder 'n paar goeie hulpbronne oor hoe om goeie aanbiedinge te kry."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Probeer om nie te veel te bekommer oor fancy lêerstelsels nie. Dit is maklik om in die konynhol te val van die opstel van dinge soos ZFS. Een tegniese detail om van bewus te wees, is dat baie lêerstelsels nie goed hanteer met baie lêers nie. Ons het gevind dat 'n eenvoudige oplossing is om veelvuldige gidse te skep, bv. vir verskillende ID-reekse of hash-voorvoegsels."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Na die aflaai van die data, maak seker om die integriteit van die lêers te kontroleer deur hashes in die metadata, indien beskikbaar."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Verspreiding"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Jy het die data, en gee jou daarmee die besit van die wêreld se eerste piratiespieël van jou teiken (waarskynlik). In baie opsigte is die moeilikste deel verby, maar die riskantste deel lê nog voor. Tot dusver was jy immers stil; onder die radar gevlieg. Al wat jy moes doen was om 'n goeie VPN deurgaans te gebruik, nie jou persoonlike besonderhede in enige vorms in te vul nie (duh), en miskien 'n spesiale blaaier sessie te gebruik (of selfs 'n ander rekenaar)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Nou moet jy die data versprei. In ons geval wou ons eers die boeke terug by Library Genesis bydra, maar het toe vinnig die moeilikhede daarin ontdek (fiksie vs nie-fiksie sortering). So ons het besluit op verspreiding deur Library Genesis-styl torrents. As jy die geleentheid het om by 'n bestaande projek by te dra, kan dit jou baie tyd bespaar. Daar is egter tans nie baie goed georganiseerde piratiespiegels daar buite nie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "So kom ons sê jy besluit om torrents self te versprei. Probeer om daardie lêers klein te hou, sodat hulle maklik op ander webwerwe gespieël kan word. Jy sal dan self die torrents moet saai, terwyl jy steeds anoniem bly. Jy kan 'n VPN gebruik (met of sonder poort vorentoe), of met gewasde Bitcoins vir 'n Seedbox betaal. As jy nie weet wat sommige van daardie terme beteken nie, sal jy 'n klomp leeswerk moet doen, aangesien dit belangrik is dat jy die risiko-afwegings hier verstaan."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Jy kan die torrent-lêers self op bestaande torrent-webwerwe host. In ons geval het ons gekies om eintlik 'n webwerf te host, aangesien ons ook ons filosofie op 'n duidelike manier wou versprei. Jy kan dit self op 'n soortgelyke manier doen (ons gebruik Njalla vir ons domeine en hosting, betaal met gewasde Bitcoins), maar voel ook vry om ons te kontak om jou torrents te host. Ons is van plan om oor tyd 'n omvattende indeks van piratiespiegels te bou, as hierdie idee posvat."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Wat VPN-keuse betref, is daar al baie oor geskryf, so ons herhaal net die algemene raad om volgens reputasie te kies. Werklike hof-getoetste geen-log-beleide met lang rekord van die beskerming van privaatheid is die laagste risiko-opsie, na ons mening. Let daarop dat selfs wanneer jy alles reg doen, jy nooit tot nul risiko kan kom nie. Byvoorbeeld, wanneer jy jou torrents saai, kan 'n hoogs gemotiveerde nasionale akteur waarskynlik kyk na inkomende en uitgaande data vloei vir VPN-bedieners, en aflei wie jy is. Of jy kan eenvoudig op een of ander manier 'n fout maak. Ons het waarskynlik al, en sal weer. Gelukkig gee nasionale state nie <em>soveel</em> om oor piraterij nie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Een besluit om vir elke projek te maak, is of om dit onder dieselfde identiteit as voorheen te publiseer, of nie. As jy dieselfde naam bly gebruik, kan foute in operasionele sekuriteit van vroeëre projekte jou terugkom en byt. Maar publiseer onder verskillende name beteken dat jy nie 'n langer blywende reputasie bou nie. Ons het gekies om sterk operasionele sekuriteit van die begin af te hê sodat ons dieselfde identiteit kan bly gebruik, maar ons sal nie huiwer om onder 'n ander naam te publiseer as ons 'n fout maak of as die omstandighede dit vereis nie."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Om die woord uit te kry kan lastig wees. Soos ons gesê het, is dit steeds 'n nisgemeenskap. Ons het oorspronklik op Reddit gepos, maar het regtig op Hacker News traksie gekry. Vir nou is ons aanbeveling om dit op 'n paar plekke te plaas en te sien wat gebeur. En weer, kontak ons. Ons sal graag die woord van meer piratiese argivisme pogings wil versprei."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Gevolgtrekking"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Hopelik is dit nuttig vir nuut-begin piratiese argivarisse. Ons is opgewonde om jou in hierdie wêreld te verwelkom, so moenie huiwer om uit te reik nie. Kom ons bewaar soveel as moontlik van die wêreld se kennis en kultuur, en weerspieël dit wyd en syd."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Ons stel die Piratiese Biblioteekspieël voor: Bewaring van 7TB boeke (wat nie in Libgen is nie)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Hierdie projek (WYSIG: verskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>) poog om by te dra tot die bewaring en bevryding van menslike kennis. Ons maak ons klein en nederige bydrae, in die voetspore van die grootes voor ons."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Die fokus van hierdie projek word geïllustreer deur sy naam:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Piraties</strong> - Ons oortree doelbewus die kopieregwet in die meeste lande. Dit stel ons in staat om iets te doen wat wettige entiteite nie kan doen nie: om seker te maak boeke word wyd en syd weerspieël."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Biblioteek</strong> - Soos die meeste biblioteke, fokus ons hoofsaaklik op geskrewe materiaal soos boeke. Ons mag in die toekoms uitbrei na ander tipes media."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Spieël</strong> - Ons is streng 'n spieël van bestaande biblioteke. Ons fokus op bewaring, nie op die maak van boeke maklik deursoekbaar en aflaaibaar (toegang) of die bevordering van 'n groot gemeenskap van mense wat nuwe boeke bydra (bron) nie."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Die eerste biblioteek wat ons gespieël het, is Z-Biblioteek. Dit is 'n gewilde (en onwettige) biblioteek. Hulle het die Library Genesis-versameling geneem en dit maklik deursoekbaar gemaak. Boonop het hulle baie effektief geword in die werwing van nuwe boekbydraes, deur bydraende gebruikers met verskeie voordele te beloon. Hulle dra tans nie hierdie nuwe boeke terug na Library Genesis nie. En anders as Library Genesis, maak hulle nie hul versameling maklik spieëlbaar nie, wat wye bewaring voorkom. Dit is belangrik vir hul besigheidsmodel, aangesien hulle geld vra vir toegang tot hul versameling in grootmaat (meer as 10 boeke per dag)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Ons maak nie morele oordele oor die hef van geld vir grootmaat toegang tot 'n onwettige boekversameling nie. Dit is ongetwyfeld dat die Z-Biblioteek suksesvol was in die uitbreiding van toegang tot kennis en die verkryging van meer boeke. Ons is eenvoudig hier om ons deel te doen: om die langtermynbewaring van hierdie private versameling te verseker."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Ons wil jou graag uitnooi om te help om menslike kennis te bewaar en te bevry deur ons torrents af te laai en te saai. Sien die projekbladsy vir meer inligting oor hoe die data georganiseer is."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Ons wil jou ook graag uitnooi om jou idees by te dra oor watter versamelings volgende gespieël moet word, en hoe om dit aan te pak. Saam kan ons baie bereik. Dit is maar 'n klein bydrae onder ontelbare ander. Dankie, vir alles wat jy doen."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Ons skakel nie na die lêers vanaf hierdie blog nie. Vind dit asseblief self.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "ISBNdb-dump, of Hoeveel Boeke Word Vir Ewig Bewaar?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "As ons die lêers van skadubiblioteke behoorlik sou dedupliseer, watter persentasie van al die boeke in die wêreld het ons bewaar?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Met die Piratiese Biblioteekspieël (WYSIG: verskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>), is ons doel om al die boeke in die wêreld te neem, en dit vir ewig te bewaar.<sup>1</sup> Tussen ons Z-Biblioteek-torrents en die oorspronklike Library Genesis-torrents, het ons 11,783,153 lêers. Maar hoeveel is dit regtig? As ons daardie lêers behoorlik dedupliseer, watter persentasie van al die boeke in die wêreld het ons bewaar? Ons wil regtig iets soos hierdie hê:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of die mensdom se geskrewe erfenis vir ewig bewaar"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Vir 'n persentasie, het ons 'n noemer nodig: die totale aantal boeke wat ooit gepubliseer is.<sup>2</sup> Voor die ondergang van Google Books, het 'n ingenieur op die projek, Leonid Taycher, <a %(booksearch_blogspot)s>geprobeer om hierdie getal te skat</a>. Hy het — tong in die kies — met 129,864,880 vorendag gekom (“ten minste tot Sondag”). Hy het hierdie getal geskat deur 'n verenigde databasis van al die boeke in die wêreld te bou. Hiervoor het hy verskillende datasets saamgevoeg en dit op verskeie maniere saamgevoeg."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "As 'n vinnige ter syde, is daar nog 'n persoon wat probeer het om al die boeke in die wêreld te katalogiseer: Aaron Swartz, die oorlede digitale aktivis en mede-stigter van Reddit.<sup>3</sup> Hy het <a %(youtube)s>Open Library begin</a> met die doel van “een webbladsy vir elke boek wat ooit gepubliseer is”, deur data van baie verskillende bronne te kombineer. Hy het die uiteindelike prys betaal vir sy digitale bewaring werk toe hy vervolg is vir die massiewe aflaai van akademiese artikels, wat tot sy selfmoord gelei het. Dit is vanselfsprekend een van die redes waarom ons groep pseudoniem is, en waarom ons baie versigtig is. Open Library word steeds heldhaftig bestuur deur mense by die Internet Archive, wat Aaron se nalatenskap voortsit. Ons sal later in hierdie pos hierop terugkom."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "In die Google-blogpos beskryf Taycher sommige van die uitdagings met die skatting van hierdie getal. Eerstens, wat beskou ons as 'n boek? Daar is 'n paar moontlike definisies:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Fisiese kopieë.</strong> Dit is natuurlik nie baie nuttig nie, aangesien hulle net duplikate van dieselfde materiaal is. Dit sou wonderlik wees as ons al die aantekeninge wat mense in boeke maak, soos Fermat se beroemde “krabbels in die kantlyne”, kon bewaar. Maar helaas, dit sal 'n argivaris se droom bly."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Werke”.</strong> Byvoorbeeld “Harry Potter and the Chamber of Secrets” as 'n logiese konsep, wat alle weergawes daarvan insluit, soos verskillende vertalings en herdrukke. Dit is 'n soort nuttige definisie, maar dit kan moeilik wees om die lyn te trek van wat tel. Byvoorbeeld, ons wil waarskynlik verskillende vertalings bewaar, alhoewel herdrukke met slegs geringe verskille dalk nie so belangrik is nie."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Uitgawes”.</strong> Hier tel jy elke unieke weergawe van 'n boek. As enigiets daaraan anders is, soos 'n ander omslag of 'n ander voorwoord, tel dit as 'n ander uitgawe."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Lêers.</strong> Wanneer jy met skadubiblioteke soos Library Genesis, Sci-Hub, of Z-Library werk, is daar 'n bykomende oorweging. Daar kan verskeie skanderings van dieselfde uitgawe wees. En mense kan beter weergawes van bestaande lêers maak, deur die teks met OCR te skandeer, of bladsye reg te stel wat teen 'n hoek geskanseer is. Ons wil hierdie lêers net as een uitgawe tel, wat goeie metadata of deduplikasie met behulp van dokumentgelykheidsmaatreëls sou vereis."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Uitgawes” lyk die mees praktiese definisie van wat “boeke” is. Gerieflik, hierdie definisie word ook gebruik vir die toekenning van unieke ISBN-nommers. 'n ISBN, of Internasionale Standaard Boeknommer, word algemeen gebruik vir internasionale handel, aangesien dit geïntegreer is met die internasionale strepieskode-stelsel (“Internasionale Artikelnommer”). As jy 'n boek in winkels wil verkoop, het dit 'n strepieskode nodig, so jy kry 'n ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Taycher se blogpos noem dat hoewel ISBN's nuttig is, hulle nie universeel is nie, aangesien hulle eers regtig in die middel-sewentigs aangeneem is, en nie oral in die wêreld nie. Tog is ISBN waarskynlik die mees wydverspreide identifiseerder van boekuitgawes, so dit is ons beste beginpunt. As ons al die ISBN's in die wêreld kan vind, kry ons 'n nuttige lys van watter boeke nog bewaar moet word."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "So, waar kry ons die data? Daar is 'n aantal bestaande pogings wat probeer om 'n lys van al die boeke in die wêreld saam te stel:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Uiteindelik het hulle hierdie navorsing vir Google Books gedoen. Hulle metadata is egter nie in grootmaat toeganklik nie en redelik moeilik om te skraap."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Soos voorheen genoem, is dit hul hele missie. Hulle het groot hoeveelhede biblioteekdata van samewerkende biblioteke en nasionale argiewe verkry, en gaan voort om dit te doen. Hulle het ook vrywillige bibliotekarisse en 'n tegniese span wat probeer om rekords te dedupliseer, en hulle met allerhande metadata te merk. Die beste van alles, hul dataset is heeltemal oop. Jy kan dit eenvoudig <a %(openlibrary)s>aflaai</a>."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Dit is 'n webwerf wat deur die nie-winsgewende OCLC bestuur word, wat biblioteekbestuurstelsels verkoop. Hulle versamel boekmetadata van baie biblioteke, en maak dit beskikbaar deur die WorldCat-webwerf. Hulle maak egter ook geld deur hierdie data te verkoop, so dit is nie beskikbaar vir grootmaat aflaai nie. Hulle het wel 'n paar meer beperkte grootmaat datastelle beskikbaar vir aflaai, in samewerking met spesifieke biblioteke."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Dit is die onderwerp van hierdie blogpos. ISBNdb skraap verskeie webwerwe vir boekmetadata, veral prysdata, wat hulle dan aan boekverkopers verkoop, sodat hulle hul boeke volgens die res van die mark kan prys. Aangesien ISBN's deesdae redelik universeel is, het hulle effektief 'n “webbladsy vir elke boek” gebou."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Verskeie individuele biblioteekstelsels en argiewe.</strong> Daar is biblioteke en argiewe wat nie deur enige van die bogenoemde geïndekseer en saamgevoeg is nie, dikwels omdat hulle onderbefonds is, of om ander redes nie hul data met Open Library, OCLC, Google, ensovoorts wil deel nie. Baie van hierdie het wel digitale rekords wat deur die internet toeganklik is, en hulle is dikwels nie baie goed beskerm nie, so as jy wil help en 'n bietjie pret wil hê om oor vreemde biblioteekstelsels te leer, is hierdie goeie beginpunte."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "In hierdie pos is ons bly om 'n klein vrystelling aan te kondig (in vergelyking met ons vorige Z-Library vrystellings). Ons het die meeste van ISBNdb geskraap, en die data beskikbaar gemaak vir torrenting op die webwerf van die Pirate Library Mirror (EDIT: verskuif na <a %(wikipedia_annas_archive)s>Anna se Argief</a>; ons sal dit nie hier direk skakel nie, soek dit net). Dit is ongeveer 30.9 miljoen rekords (20GB as <a %(jsonlines)s>JSON Lines</a>; 4.4GB gegzip). Op hul webwerf beweer hulle dat hulle eintlik 32.6 miljoen rekords het, so ons het dalk op een of ander manier sommige gemis, of <em>hulle</em> kan iets verkeerd doen. In elk geval, vir nou sal ons nie presies deel hoe ons dit gedoen het nie — ons sal dit as 'n oefening vir die leser laat. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Wat ons wel sal deel, is 'n paar voorlopige analises, om nader te kom aan die skatting van die aantal boeke in die wêreld. Ons het na drie datastelle gekyk: hierdie nuwe ISBNdb-dataset, ons oorspronklike vrystelling van metadata wat ons van die Z-Library skadubiblioteek geskraap het (wat Library Genesis insluit), en die Open Library data dump."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Kom ons begin met 'n paar rowwe syfers:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "In beide Z-Library/Libgen en Open Library is daar baie meer boeke as unieke ISBN's. Beteken dit dat baie van daardie boeke nie ISBN's het nie, of is die ISBN-metadata eenvoudig ontbreek? Ons kan waarskynlik hierdie vraag beantwoord met 'n kombinasie van outomatiese passing gebaseer op ander eienskappe (titel, outeur, uitgewer, ens.), meer databronne inbring, en ISBN's uit die werklike boekskanderings self onttrek (in die geval van Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Hoeveel van daardie ISBN's is uniek? Dit word die beste geïllustreer met 'n Venn-diagram:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Om meer presies te wees:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Ons was verbaas oor hoe min oorvleueling daar is! ISBNdb het 'n groot hoeveelheid ISBNs wat nie in Z-Library of Open Library verskyn nie, en dieselfde geld (in 'n kleiner maar steeds beduidende mate) vir die ander twee. Dit laat baie nuwe vrae ontstaan. Hoeveel sou outomatiese ooreenstemming help om die boeke wat nie met ISBNs gemerk is nie, te merk? Sou daar baie ooreenkomste wees en dus verhoogde oorvleueling? Ook, wat sou gebeur as ons 'n 4de of 5de dataset inbring? Hoeveel oorvleueling sou ons dan sien?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Dit gee ons wel 'n beginpunt. Ons kan nou kyk na al die ISBNs wat nie in die Z-Library dataset was nie, en wat ook nie titel-/outeurvelde ooreenstem nie. Dit kan ons 'n handvatsel gee om al die boeke in die wêreld te bewaar: eers deur die internet te skraap vir skanderings, dan deur in die werklike lewe uit te gaan om boeke te skandeer. Laasgenoemde kan selfs deur skare-befondsing gedoen word, of gedryf word deur \"belonings\" van mense wat graag spesifieke boeke gedigitaliseer wil sien. Dit alles is 'n storie vir 'n ander tyd."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "As u wil help met enige van hierdie dinge — verdere analise; meer metadata skraap; meer boeke vind; OCR van boeke; dit doen vir ander domeine (bv. artikels, oudioboeke, flieks, TV-programme, tydskrifte) of selfs sommige van hierdie data beskikbaar maak vir dinge soos ML / groot taalmodel opleiding — kontak my asseblief (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "As u spesifiek in die data-analise belangstel, werk ons daaraan om ons datasets en skripte in 'n meer gebruikersvriendelike formaat beskikbaar te stel. Dit sal wonderlik wees as u net 'n notaboek kan fork en daarmee kan begin speel."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Laastens, as u hierdie werk wil ondersteun, oorweeg asseblief om 'n donasie te maak. Dit is 'n heeltemal vrywilliger-gedrewe operasie, en u bydrae maak 'n groot verskil. Elke bietjie help. Vir nou neem ons donasies in kripto; sien die Skenk-bladsy op Anna se Argief."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Vir 'n redelike definisie van \"vir ewig\". ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Natuurlik is die mensdom se geskrewe erfenis baie meer as boeke, veral deesdae. Ter wille van hierdie pos en ons onlangse vrystellings fokus ons op boeke, maar ons belangstellings strek verder."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Daar is baie meer wat oor Aaron Swartz gesê kan word, maar ons wou hom net kortliks noem, aangesien hy 'n deurslaggewende rol in hierdie storie speel. Soos tyd verbygaan, mag meer mense sy naam vir die eerste keer teëkom, en kan hulle self in die konynhol duik."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Die kritieke venster van skadu-biblioteke"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Hoe kan ons beweer dat ons ons versamelings vir ewig kan bewaar, wanneer hulle reeds 1 PB nader?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Chinese weergawe 中文版</a>, bespreek op <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "By Anna se Argief word ons dikwels gevra hoe ons kan beweer dat ons ons versamelings vir ewig kan bewaar, wanneer die totale grootte reeds 1 Petabyte (1000 TB) nader, en steeds groei. In hierdie artikel sal ons na ons filosofie kyk, en sien waarom die volgende dekade krities is vir ons missie om die mensdom se kennis en kultuur te bewaar."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "Die <a %(annas_archive_stats)s>totale grootte</a> van ons versamelings, oor die afgelope paar maande, verdeel volgens die aantal torrent-saadverspreiders."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Prioriteite"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Waarom gee ons soveel om oor artikels en boeke? Kom ons sit ons fundamentele geloof in bewaring in die algemeen opsy — ons mag dalk 'n ander pos daaroor skryf. So waarom spesifiek artikels en boeke? Die antwoord is eenvoudig: <strong>inligtingsdigtheid</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Per megabyte van berging, stoor geskrewe teks die meeste inligting van alle media. Terwyl ons omgee vir beide kennis en kultuur, gee ons meer om oor eersgenoemde. Oor die algemeen vind ons 'n hiërargie van inligtingsdigtheid en belangrikheid van bewaring wat ongeveer so lyk:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Akademiese artikels, joernale, verslae"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Organiese data soos DNS-volgordes, plant sade, of mikrobiese monsters"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Nie-fiksie boeke"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Wetenskap- en ingenieurswese-sagtewarekode"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Meetdata soos wetenskaplike metings, ekonomiese data, korporatiewe verslae"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Wetenskap- en ingenieurswese-webwerwe, aanlyn besprekings"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Nie-fiksie tydskrifte, koerante, handleidings"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Nie-fiksie transkripsies van praatjies, dokumentêre, podcasts"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Interne data van korporasies of regerings (lekkasies)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Metadata-rekords oor die algemeen (van nie-fiksie en fiksie; van ander media, kuns, mense, ens.; insluitend resensies)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Geografiese data (bv. kaarte, geologiese opnames)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkripsies van regsgedinge of hofverrigtinge"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Fiktiewe of vermaaklikheidsweergawes van al die bogenoemde"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Die rangorde in hierdie lys is ietwat arbitrêr — verskeie items is gelykop of daar is meningsverskille binne ons span — en ons vergeet waarskynlik van sommige belangrike kategorieë. Maar dit is ongeveer hoe ons prioritiseer."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Sommige van hierdie items is te verskillend van die ander vir ons om oor te bekommer (of word reeds deur ander instellings hanteer), soos organiese data of geografiese data. Maar die meeste van die items in hierdie lys is eintlik vir ons belangrik."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Nog 'n groot faktor in ons prioritisering is hoe groot die risiko is dat 'n sekere werk verlore gaan. Ons verkies om te fokus op werke wat:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Skaars"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Uniek onderbeklemtoon"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Uniek in gevaar van vernietiging (bv. deur oorlog, befondsingsbesnoeiings, regsgedinge, of politieke vervolging)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Laastens gee ons om oor skaal. Ons het beperkte tyd en geld, so ons verkies om 'n maand te spandeer om 10,000 boeke te red eerder as 1,000 boeke — as hulle ongeveer ewe waardevol en in gevaar is."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Skadubiblioteke"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Daar is baie organisasies wat soortgelyke missies en soortgelyke prioriteite het. Inderdaad, daar is biblioteke, argiewe, laboratoriums, museums, en ander instellings wat met die bewaring van hierdie aard belas is. Baie van hulle is goed befonds, deur regerings, individue, of korporasies. Maar hulle het een massiewe blinde kol: die regstelsel."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Hier lê die unieke rol van skadubiblioteke, en die rede waarom Anna se Argief bestaan. Ons kan dinge doen wat ander instellings nie toegelaat word om te doen nie. Nou, dit is nie (dikwels) dat ons materiaal kan argiveer wat elders onwettig is om te bewaar nie. Nee, dit is wettig in baie plekke om 'n argief te bou met enige boeke, artikels, tydskrifte, ensovoorts."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Maar wat wettige argiewe dikwels kort, is <strong>redundansie en langlewendheid</strong>. Daar bestaan boeke waarvan slegs een kopie in 'n fisiese biblioteek iewers bestaan. Daar is metadata-rekords wat deur 'n enkele korporasie bewaak word. Daar is koerante wat slegs op mikrofilm in 'n enkele argief bewaar word. Biblioteke kan befondsingsbesnoeiings kry, korporasies kan bankrot gaan, argiewe kan gebombardeer en afgebrand word. Dit is nie hipoteties nie — dit gebeur heeltyd."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Die ding wat ons uniek kan doen by Anna se Argief is om baie kopieë van werke op groot skaal te stoor. Ons kan artikels, boeke, tydskrifte en meer versamel en dit in grootmaat versprei. Ons doen dit tans deur torrents, maar die presiese tegnologieë maak nie saak nie en sal oor tyd verander. Die belangrike deel is om baie kopieë oor die wêreld te versprei. Hierdie aanhaling van meer as 200 jaar gelede is steeds waar:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Die verlore kan nie herwin word nie; maar laat ons red wat oorbly: nie deur kluise en slotte wat hulle van die openbare oog en gebruik afsper, deur hulle aan die vergetelheid van tyd oor te laat nie, maar deur so 'n vermenigvuldiging van kopieë, dat hulle buite die bereik van ongeluk geplaas word.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "'n Vinnige nota oor publieke domein. Aangesien Anna se Argief uniek fokus op aktiwiteite wat in baie plekke regoor die wêreld onwettig is, steur ons ons nie aan wyd beskikbare versamelings, soos boeke in die publieke domein nie. Wettige entiteite sorg dikwels reeds goed daarvoor. Daar is egter oorwegings wat ons soms laat werk aan publiek beskikbare versamelings:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Metadata-rekords kan vrylik op die Worldcat-webwerf besigtig word, maar nie in grootmaat afgelaai word nie (totdat ons hulle <a %(worldcat_scrape)s>gekrap</a> het)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kode kan oopbron op Github wees, maar Github as geheel kan nie maklik weerspieël en dus bewaar word nie (alhoewel daar in hierdie spesifieke geval voldoende verspreide kopieë van die meeste kode-bewaarplekke is)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit is gratis om te gebruik, maar het onlangs streng anti-krapmaatreëls ingestel, in die nasleep van data-honger LLM-opleiding (meer daaroor later)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "'n Vermenigvuldiging van kopieë"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Terug na ons oorspronklike vraag: hoe kan ons beweer dat ons ons versamelings vir ewig bewaar? Die hoofprobleem hier is dat ons versameling teen 'n vinnige tempo <a %(torrents_stats)s>groei</a>, deur groot versamelings te krap en oopbron te maak (bo en behalwe die wonderlike werk wat reeds deur ander oop-data-skadu-biblioteke soos Sci-Hub en Library Genesis gedoen is)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Hierdie groei in data maak dit moeiliker vir die versamelings om regoor die wêreld weerspieël te word. Databerging is duur! Maar ons is optimisties, veral wanneer ons die volgende drie tendense waarneem."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Ons het die lae hangende vrugte gepluk"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Hierdie een volg direk uit ons prioriteite hierbo bespreek. Ons verkies om eers aan die bevryding van groot versamelings te werk. Nou dat ons van die grootste versamelings in die wêreld verseker het, verwag ons dat ons groei baie stadiger sal wees."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Daar is steeds 'n lang stert van kleiner versamelings, en nuwe boeke word elke dag geskandeer of gepubliseer, maar die tempo sal waarskynlik baie stadiger wees. Ons mag steeds in grootte verdubbel of selfs verdriedubbel, maar oor 'n langer tydperk."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Bergingskoste bly eksponensieel daal"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Ten tyde van skryf is <a %(diskprices)s>skyfpryse</a> per TB ongeveer $12 vir nuwe skywe, $8 vir gebruikte skywe, en $4 vir band. As ons konserwatief is en slegs na nuwe skywe kyk, beteken dit dat die berging van 'n petabyte ongeveer $12,000 kos. As ons aanneem dat ons biblioteek van 900TB na 2.7PB sal verdriedubbel, sou dit beteken $32,400 om ons hele biblioteek te weerspieël. Voeg elektrisiteit, koste van ander hardeware, ensovoorts by, laat ons dit afrond na $40,000. Of met band meer soos $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Aan die een kant is <strong>$15,000–$40,000 vir die som van alle menslike kennis 'n winskoop</strong>. Aan die ander kant is dit 'n bietjie steil om tonne volle kopieë te verwag, veral as ons ook wil hê dat daardie mense hul torrents moet aanhou saai tot voordeel van ander."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Dit is vandag. Maar vooruitgang marsjeer vorentoe:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Hardeskyf koste per TB is ongeveer in derdes gesny oor die laaste 10 jaar, en sal waarskynlik aanhou daal teen 'n soortgelyke tempo. Band blyk op 'n soortgelyke trajek te wees. SSD-pryse daal selfs vinniger, en mag teen die einde van die dekade HDD-pryse oorneem."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "HDD-prysneigings van verskillende bronne (klik om studie te sien)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "As dit hou, dan mag ons oor 10 jaar slegs $5,000–$13,000 kyk om ons hele versameling te weerspieël (1/3de), of selfs minder as ons minder in grootte groei. Alhoewel dit steeds baie geld is, sal dit vir baie mense haalbaar wees. En dit mag selfs beter wees as gevolg van die volgende punt…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Verbeterings in inligtingsdigtheid"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Ons stoor tans boeke in die rou formate waarin hulle aan ons gegee word. Ja, hulle is saamgepers, maar dikwels is dit steeds groot skanderings of foto's van bladsye."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Tot nou toe was die enigste opsies om die totale grootte van ons versameling te verminder deur meer aggressiewe kompressie, of deduplikasie. Maar om beduidende besparings te kry, is albei te verliesryk vir ons smaak. Swaar kompressie van foto's kan teks skaars leesbaar maak. En deduplikasie vereis 'n hoë mate van vertroue dat boeke presies dieselfde is, wat dikwels te onakkuraat is, veral as die inhoud dieselfde is, maar die skanderings op verskillende geleenthede gemaak is."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Daar was nog altyd 'n derde opsie, maar die kwaliteit daarvan was so afgryslik dat ons dit nooit oorweeg het nie: <strong>OCR, of Optiese Karakterherkenning</strong>. Dit is die proses om foto's in gewone teks om te skakel deur AI te gebruik om die karakters in die foto's te herken. Gereedskap hiervoor het lank bestaan en was redelik ordentlik, maar \"redelik ordentlik\" is nie genoeg vir bewaring nie."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Egter, onlangse multi-modale diep-leer modelle het uiters vinnige vordering gemaak, alhoewel steeds teen hoë koste. Ons verwag dat beide akkuraatheid en koste dramaties sal verbeter in die komende jare, tot die punt waar dit realisties sal word om op ons hele biblioteek toe te pas."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "OCR verbeterings."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Wanneer dit gebeur, sal ons waarskynlik steeds die oorspronklike lêers bewaar, maar daarby kan ons 'n baie kleiner weergawe van ons biblioteek hê wat die meeste mense sal wil spieël. Die kinkel is dat rou teks self nog beter saamgepers word, en baie makliker is om te dedupliseer, wat ons nog meer besparings gee."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Oor die algemeen is dit nie onrealisties om ten minste 'n 5-10x vermindering in totale lêergrootte te verwag nie, dalk selfs meer. Selfs met 'n konserwatiewe 5x vermindering, sal ons kyk na <strong>$1,000–$3,000 in 10 jaar selfs al verdriedubbel ons biblioteek in grootte</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Kritieke venster"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "As hierdie voorspellings akkuraat is, moet ons <strong>net 'n paar jaar wag</strong> voordat ons hele versameling wyd gespiegeld sal word. Dus, in die woorde van Thomas Jefferson, \"geplaas buite die bereik van ongeluk.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Ongelukkig het die opkoms van LLM's, en hul data-honger opleiding, baie kopiereghouers op die verdediging geplaas. Selfs meer as wat hulle reeds was. Baie webwerwe maak dit moeiliker om te krap en te argiveer, regsgedinge vlieg rond, en intussen word fisiese biblioteke en argiewe steeds verwaarloos."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Ons kan net verwag dat hierdie tendense sal voortgaan om te versleg, en baie werke sal verlore gaan lank voordat hulle die publieke domein betree."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Ons is op die vooraand van 'n rewolusie in bewaring, maar <q>die verlore kan nie herwin word nie.</q></strong> Ons het 'n kritieke venster van ongeveer 5-10 jaar waarin dit steeds redelik duur is om 'n skadubiblioteek te bedryf en baie spieëls regoor die wêreld te skep, en waarin toegang nog nie heeltemal afgesluit is nie."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "As ons hierdie venster kan oorbrug, sal ons inderdaad die kennis en kultuur van die mensdom vir ewig bewaar het. Ons moet nie hierdie tyd mors nie. Ons moet nie toelaat dat hierdie kritieke venster op ons sluit nie."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Kom ons gaan."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Eksklusiewe toegang vir LLM-maatskappye tot die grootste Chinese nie-fiksie boekversameling in die wêreld"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Chinese weergawe 中文版</a>, <a %(news_ycombinator)s>Bespreek op Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna se Argief het 'n unieke versameling van 7.5 miljoen / 350TB Chinese nie-fiksie boeke verkry — groter as Library Genesis. Ons is bereid om 'n LLM-maatskappy eksklusiewe toegang te gee, in ruil vir hoë kwaliteit OCR en teksonttrekking.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Dit is 'n kort blogpos. Ons soek 'n maatskappy of instelling om ons te help met OCR en teksonttrekking vir 'n massiewe versameling wat ons verkry het, in ruil vir eksklusiewe vroeë toegang. Na die embargo-tydperk sal ons natuurlik die hele versameling vrylaat."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Hoë kwaliteit akademiese teks is uiters nuttig vir die opleiding van LLMs. Alhoewel ons versameling Chinees is, behoort dit selfs nuttig te wees vir die opleiding van Engelse LLMs: modelle blyk konsepte en kennis te enkodeer ongeag die brontaal."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Hiervoor moet teks uit die skanderings onttrek word. Wat kry Anna se Argief daaruit? Volledige teks soektog van die boeke vir sy gebruikers."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Omdat ons doelwitte ooreenstem met dié van LLM-ontwikkelaars, soek ons 'n medewerker. Ons is bereid om jou <strong>eksklusiewe vroeë toegang tot hierdie versameling in grootmaat vir 1 jaar</strong> te gee, as jy behoorlike OCR en teksonttrekking kan doen. As jy bereid is om die hele kode van jou pyplyn met ons te deel, sal ons bereid wees om die versameling langer te embargo."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Voorbeeldbladsye"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Om aan ons te bewys dat jy 'n goeie pyplyn het, is hier 'n paar voorbeeldbladsye om mee te begin, uit 'n boek oor supergeleiers. Jou pyplyn moet wiskunde, tabelle, grafieke, voetnote, ensovoorts behoorlik hanteer."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Stuur jou verwerkte bladsye na ons e-pos. As hulle goed lyk, sal ons jou meer privaat stuur, en ons verwag dat jy jou pyplyn vinnig daarop kan laat loop. Sodra ons tevrede is, kan ons 'n ooreenkoms maak."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Versameling"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Nog 'n bietjie inligting oor die versameling. <a %(duxiu)s>Duxiu</a> is 'n massiewe databasis van geskanne boeke, geskep deur die <a %(chaoxing)s>SuperStar Digital Library Group</a>. Meeste is akademiese boeke, geskan om dit digitaal beskikbaar te maak vir universiteite en biblioteke. Vir ons Engelssprekende gehoor het <a %(library_princeton)s>Princeton</a> en die <a %(guides_lib_uw)s>Universiteit van Washington</a> goeie oorsigte. Daar is ook 'n uitstekende artikel wat meer agtergrond gee: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (soek dit op in Anna se Argief)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Die boeke van Duxiu is lank reeds op die Chinese internet gepirateer. Gewoonlik word hulle vir minder as 'n dollar deur herverkopers verkoop. Hulle word tipies versprei deur die Chinese ekwivalent van Google Drive, wat dikwels gekap is om meer stoorplek toe te laat. Sommige tegniese besonderhede kan <a %(github_duty_machine)s>hier</a> en <a %(github_821_github_io)s>hier</a> gevind word."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Alhoewel die boeke semi-openbaar versprei is, is dit nogal moeilik om hulle in grootmaat te verkry. Ons het dit hoog op ons TODO-lys gehad, en het verskeie maande van voltydse werk daarvoor toegeken. Maar onlangs het 'n ongelooflike, wonderlike en talentvolle vrywilliger ons genader en gesê dat hulle al hierdie werk reeds gedoen het — teen groot koste. Hulle het die volledige versameling met ons gedeel, sonder om enigiets in ruil te verwag, behalwe die waarborg van langtermynbewaring. Werklik merkwaardig. Hulle het ingestem om op hierdie manier hulp te vra om die versameling OCR te kry."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Die versameling is 7,543,702 lêers. Dit is meer as Library Genesis nie-fiksie (ongeveer 5.3 miljoen). Totale lêergrootte is ongeveer 359TB (326TiB) in sy huidige vorm."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Ons is oop vir ander voorstelle en idees. Kontak ons net. Kyk na Anna se Argief vir meer inligting oor ons versamelings, bewaringspogings, en hoe jy kan help. Dankie!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Waarskuwing: hierdie blogpos is verouderd. Ons het besluit dat IPFS nog nie gereed is vir hoofstroomgebruik nie. Ons sal steeds na lêers op IPFS van Anna se Argief skakel waar moontlik, maar ons sal dit nie meer self huisves nie, en ons beveel ook nie aan dat ander dit met IPFS spieël nie. Sien asseblief ons Torrents-bladsy as jy ons versameling wil help bewaar."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Help om Z-Library op IPFS te saai"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Hoe om 'n skadubiblioteek te bestuur: bedrywighede by Anna se Argief"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Daar is geen <q>AWS vir skaduliefdadighede nie,</q> so hoe bestuur ons Anna se Argief?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Ek bestuur <a %(wikipedia_annas_archive)s>Anna se Argief</a>, die wêreld se grootste oopbron nie-winsgewende soekenjin vir <a %(wikipedia_shadow_library)s>skadubiblioteke</a>, soos Sci-Hub, Library Genesis, en Z-Library. Ons doel is om kennis en kultuur maklik toeganklik te maak, en uiteindelik 'n gemeenskap van mense te bou wat saam <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>al die boeke in die wêreld</a> argiveer en bewaar."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "In hierdie artikel sal ek wys hoe ons hierdie webwerf bestuur, en die unieke uitdagings wat gepaard gaan met die bedryf van 'n webwerf met twyfelagtige wettige status, aangesien daar geen “AWS vir skaduliefdadighede” is nie."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Kyk ook na die susterartikel <a %(blog_how_to_become_a_pirate_archivist)s>Hoe om 'n seerower-argivaris te word</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Innovasie-tokens"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Kom ons begin met ons tegnologie-stapel. Dit is doelbewus vervelig. Ons gebruik Flask, MariaDB, en ElasticSearch. Dit is letterlik al. Soektog is grootliks 'n opgeloste probleem, en ons beoog nie om dit te herontdek nie. Boonop moet ons ons <a %(mcfunley)s>innovasie-tokens</a> op iets anders spandeer: om nie deur die owerhede gesluit te word nie."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Hoe wettig of onwettig is Anna se Argief presies? Dit hang meestal af van die regsjurisdiksie. Die meeste lande glo in een of ander vorm van kopiereg, wat beteken dat mense of maatskappye 'n eksklusiewe monopolie op sekere soorte werke vir 'n sekere tydperk toegeken word. Terloops, by Anna se Argief glo ons dat hoewel daar sommige voordele is, kopiereg oor die algemeen 'n netto-negatief vir die samelewing is — maar dit is 'n storie vir 'n ander keer."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Hierdie eksklusiewe monopolie op sekere werke beteken dat dit onwettig is vir enigiemand buite hierdie monopolie om daardie werke direk te versprei — insluitend ons. Maar Anna se Argief is 'n soekenjin wat nie daardie werke direk versprei nie (ten minste nie op ons clearnet-webwerf nie), so ons behoort reg te wees, reg? Nie heeltemal nie. In baie jurisdiksies is dit nie net onwettig om kopieregwerke te versprei nie, maar ook om na plekke te skakel wat dit doen. 'n Klassieke voorbeeld hiervan is die Verenigde State se DMCA-wet."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Dit is die strengste kant van die spektrum. Aan die ander kant van die spektrum kan daar teoreties lande wees met geen kopieregwette hoegenaamd nie, maar hierdie bestaan nie regtig nie. Byna elke land het een of ander vorm van kopieregwet op die boeke. Handhawing is 'n ander storie. Daar is baie lande met regerings wat nie omgee om kopieregwet te handhaaf nie. Daar is ook lande tussen die twee uiterstes, wat die verspreiding van kopieregwerke verbied, maar nie verbied om na sulke werke te skakel nie."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Nog 'n oorweging is op die maatskappyvlak. As 'n maatskappy in 'n jurisdiksie werk wat nie omgee oor kopiereg nie, maar die maatskappy self is nie bereid om enige risiko te neem nie, dan kan hulle jou webwerf sluit sodra iemand daaroor kla."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Laastens, 'n groot oorweging is betalings. Aangesien ons anoniem moet bly, kan ons nie tradisionele betaalmetodes gebruik nie. Dit laat ons met kriptogeldeenhede, en slegs 'n klein subset van maatskappye ondersteun dit (daar is virtuele debietkaarte betaal deur kripto, maar hulle word dikwels nie aanvaar nie)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Stelselargitektuur"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Kom ons sê jy het 'n paar maatskappye gevind wat bereid is om jou webwerf te huisves sonder om jou af te sluit — kom ons noem hulle “vryheidsliefde verskaffers” 😄. Jy sal vinnig vind dat dit redelik duur is om alles by hulle te huisves, so jy mag dalk wil 'n paar “goedkoop verskaffers” vind en die werklike hosting daar doen, deur die vryheidsliefde verskaffers te proxy. As jy dit reg doen, sal die goedkoop verskaffers nooit weet wat jy huisves nie, en nooit enige klagtes ontvang nie."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Met al hierdie verskaffers is daar 'n risiko dat hulle jou tog afskakel, so jy het ook redundansie nodig. Ons benodig dit op alle vlakke van ons stapel."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Een ietwat vryheidsliefde maatskappy wat homself in 'n interessante posisie geplaas het, is Cloudflare. Hulle het <a %(blog_cloudflare)s>aangevoer</a> dat hulle nie 'n hostingverskaffer is nie, maar 'n nutsdiens, soos 'n ISP. Hulle is dus nie onderhewig aan DMCA of ander afskakelversoeke nie, en stuur enige versoeke na jou werklike hostingverskaffer. Hulle het so ver gegaan as om hof toe te gaan om hierdie struktuur te beskerm. Ons kan hulle dus gebruik as nog 'n laag van kas en beskerming."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare aanvaar nie anonieme betalings nie, so ons kan slegs hul gratis plan gebruik. Dit beteken dat ons nie hul lasbalansering of failover-funksies kan gebruik nie. Ons het dit dus <a %(annas_archive_l255)s>self geïmplementeer</a> op die domeinvlak. By bladsylaai sal die blaaier kyk of die huidige domein steeds beskikbaar is, en indien nie, herskryf dit alle URL's na 'n ander domein. Aangesien Cloudflare baie bladsye kas, beteken dit dat 'n gebruiker op ons hoofdomein kan land, selfs al is die proxy-bediener af, en dan by die volgende klik na 'n ander domein oorgedra word."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Ons het steeds ook normale operasionele bekommernisse om mee te doen, soos om die gesondheid van die bediener te monitor, agterkant- en voorkantfoute aan te teken, en so aan. Ons failover-argitektuur laat ook meer robuustheid op hierdie front toe, byvoorbeeld deur 'n heeltemal ander stel bedieners op een van die domeine te laat loop. Ons kan selfs ouer weergawes van die kode en datasets op hierdie aparte domein laat loop, ingeval 'n kritieke fout in die hoofweergawe ongesiens bly."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Ons kan ook teen Cloudflare se draai teen ons beskerm, deur dit van een van die domeine te verwyder, soos hierdie aparte domein. Verskillende permutasies van hierdie idees is moontlik."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Gereedskap"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Kom ons kyk na watter gereedskap ons gebruik om al hierdie dinge te bereik. Dit ontwikkel baie soos ons nuwe probleme teëkom en nuwe oplossings vind."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Aansoekbediener: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Proxy-bediener: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Bedienerbestuur: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Ontwikkeling: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Ui-statiese hosting: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Daar is 'n paar besluite waaroor ons heen en weer gegaan het. Een daarvan is die kommunikasie tussen bedieners: ons het vroeër Wireguard hiervoor gebruik, maar gevind dat dit soms ophou om enige data oor te dra, of slegs data in een rigting oordra. Dit het gebeur met verskeie verskillende Wireguard-opstellings wat ons probeer het, soos <a %(github_costela_wesher)s>wesher</a> en <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Ons het ook probeer om poorte oor SSH te tonnel, met behulp van autossh en sshuttle, maar het <a %(github_sshuttle)s>probleme daar</a> ondervind (alhoewel dit steeds nie vir my duidelik is of autossh aan TCP-oor-TCP probleme ly of nie — dit voel net vir my soos 'n slordige oplossing, maar miskien is dit eintlik reg?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "In plaas daarvan het ons teruggekeer na direkte verbindings tussen bedieners, wat verberg dat 'n bediener op die goedkoop verskaffers loop deur IP-filtrering met UFW te gebruik. Dit het die nadeel dat Docker nie goed werk met UFW nie, tensy jy <code>network_mode: \"host\"</code> gebruik. Al hierdie is 'n bietjie meer foutgevoelig, want jy sal jou bediener aan die internet blootstel met net 'n klein verkeerde konfigurasie. Miskien moet ons terugbeweeg na autossh — terugvoer sal hier baie welkom wees."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Ons het ook heen en weer gegaan oor Varnish vs. Nginx. Ons hou tans van Varnish, maar dit het sy eienaardighede en rowwe kante. Dieselfde geld vir Checkmk: ons is nie mal daaroor nie, maar dit werk vir nou. Weblate was okay, maar nie ongelooflik nie — ek vrees soms dat dit my data sal verloor wanneer ek probeer om dit met ons git repo te sinkroniseer. Flask was oor die algemeen goed, maar dit het 'n paar vreemde eienaardighede wat baie tyd gekos het om te ontfout, soos die konfigurasie van pasgemaakte domeine, of probleme met sy SqlAlchemy-integrasie."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Tot dusver was die ander gereedskap wonderlik: ons het geen ernstige klagtes oor MariaDB, ElasticSearch, Gitlab, Zulip, Docker, en Tor nie. Al hierdie het 'n paar probleme gehad, maar niks oormatig ernstig of tydrowend nie."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Gevolgtrekking"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Dit was 'n interessante ervaring om te leer hoe om 'n robuuste en veerkragtige skadubiblioteek-soekenjin op te stel. Daar is baie meer besonderhede om in latere plasings te deel, so laat weet my wat jy meer oor wil leer!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Soos altyd, soek ons na donasies om hierdie werk te ondersteun, so maak seker om die Skenk-bladsy op Anna se Argief te besoek. Ons soek ook ander tipes ondersteuning, soos toelaes, langtermyn-borge, hoërisiko-betalingverskaffers, miskien selfs (stylvolle!) advertensies. En as jy jou tyd en vaardighede wil bydra, soek ons altyd na ontwikkelaars, vertalers, ensovoorts. Dankie vir jou belangstelling en ondersteuning."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna en die span (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hallo, ek is Anna. Ek het <a %(wikipedia_annas_archive)s>Anna se Argief</a> geskep, die wêreld se grootste skadubiblioteek. Dit is my persoonlike blog, waarin ek en my spanmaats skryf oor piraterij, digitale bewaring, en meer."

#, fuzzy
msgid "blog.index.text2"
msgstr "Koppel met my op <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Let daarop dat hierdie webwerf net 'n blog is. Ons huisves slegs ons eie woorde hier. Geen torrents of ander kopiereg-beskermde lêers word hier gehuisves of gekoppel nie."

#, fuzzy
msgid "blog.index.heading"
msgstr "Blogplasings"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat-skraap"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Plaas 5,998,794 boeke op IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Waarskuwing: hierdie blogpos is verouderd. Ons het besluit dat IPFS nog nie gereed is vir hoofstroomgebruik nie. Ons sal steeds na lêers op IPFS van Anna se Argief skakel waar moontlik, maar ons sal dit nie meer self huisves nie, en ons beveel ook nie aan dat ander dit met IPFS spieël nie. Sien asseblief ons Torrents-bladsy as jy ons versameling wil help bewaar."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Anna se Argief het die hele WorldCat (die wêreld se grootste biblioteek-metadata-versameling) geskraap om 'n TODO-lys van boeke te maak wat bewaar moet word.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "'n Jaar gelede het ons <a %(blog)s>begin</a> om hierdie vraag te beantwoord: <strong>Watter persentasie van boeke is permanent bewaar deur skadubiblioteke?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Sodra 'n boek in 'n oop-data skadubiblioteek soos <a %(wikipedia_library_genesis)s>Library Genesis</a>, en nou <a %(wikipedia_annas_archive)s>Anna se Argief</a>, beland, word dit regoor die wêreld gespieël (deur torrents), en sodoende word dit prakties vir ewig bewaar."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Om die vraag te beantwoord oor watter persentasie van boeke bewaar is, moet ons die noemer ken: hoeveel boeke bestaan daar in totaal? En ideaal gesproke het ons nie net 'n getal nie, maar werklike metadata. Dan kan ons hulle nie net teen skadubiblioteke pas nie, maar ook <strong>'n TODO-lys van oorblywende boeke skep om te bewaar!</strong> Ons kan selfs begin droom van 'n skare-gedrewe poging om hierdie TODO-lys af te werk."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Ons het <a %(wikipedia_isbndb_com)s>ISBNdb</a> geskrap, en die <a %(openlibrary)s>Open Library dataset</a> afgelaai, maar die resultate was onbevredigend. Die hoofprobleem was dat daar nie 'n groot oorvleueling van ISBNs was nie. Kyk na hierdie Venn-diagram van <a %(blog)s>ons blogpos</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Ons was baie verbaas oor hoe min oorvleueling daar tussen ISBNdb en Open Library was, albei van hulle sluit vrylik data van verskeie bronne in, soos webskrapings en biblioteekrekords. As hulle albei 'n goeie werk doen om die meeste ISBNs daar buite te vind, sou hul sirkels sekerlik aansienlike oorvleueling hê, of een sou 'n subset van die ander wees. Dit het ons laat wonder, hoeveel boeke val <em>heeltemal buite hierdie sirkels</em>? Ons benodig 'n groter databasis."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Dit is toe dat ons ons visier op die grootste boekdatabasis in die wêreld gerig het: <a %(wikipedia_worldcat)s>WorldCat</a>. Dit is 'n eie databasis deur die nie-winsgewende <a %(wikipedia_oclc)s>OCLC</a>, wat metadata-rekords van biblioteke regoor die wêreld versamel, in ruil daarvoor dat hulle daardie biblioteke toegang gee tot die volledige dataset, en dat hulle in eindgebruikers se soekresultate verskyn."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Alhoewel OCLC 'n nie-winsgewende organisasie is, vereis hul besigheidsmodel die beskerming van hul databasis. Wel, ons is jammer om te sê, vriende by OCLC, ons gee dit alles weg. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Oor die afgelope jaar het ons noukeurig al die WorldCat-rekords geskrap. Aanvanklik het ons 'n gelukkige deurbraak gehad. WorldCat was net besig om hul volledige webwerf-herontwerp uit te rol (in Aug 2022). Dit het 'n aansienlike herstrukturering van hul agterstelsels ingesluit, wat baie sekuriteitsfoute geïntroduceer het. Ons het onmiddellik die geleentheid aangegryp, en kon honderde miljoene (!) rekords in slegs 'n paar dae skrap."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>WorldCat-herontwerp</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Daarna is sekuriteitsfoute stadig een vir een reggestel, totdat die laaste een wat ons gevind het, omtrent 'n maand gelede reggemaak is. Teen daardie tyd het ons feitlik al die rekords gehad, en was ons net op soek na effens hoër kwaliteit rekords. Dus het ons gevoel dit is tyd om vry te stel!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Kom ons kyk na 'n paar basiese inligting oor die data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Formaat?</strong> <a %(blog)s>Anna se Argiefhouers (AAC)</a>, wat in wese <a %(jsonlines)s>JSON Lines</a> saamgepers met <a %(zstd)s>Zstandard</a> is, plus 'n paar gestandaardiseerde semantiek. Hierdie houers omhul verskillende tipes rekords, gebaseer op die verskillende skrapings wat ons ontplooi het."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

#, fuzzy
msgid "dyn.buy_membership.error.unknown"
msgstr "'n Onbekende fout het voorgekom. Kontak ons asseblief by %(email)s met 'n skermskoot."

#, fuzzy
msgid "dyn.buy_membership.error.minimum"
msgstr "Hierdie munt het 'n hoër as gewone minimum. Kies asseblief 'n ander duur of 'n ander munt."

#, fuzzy
msgid "dyn.buy_membership.error.try_again"
msgstr "Versoek kon nie voltooi word nie. Probeer asseblief weer oor 'n paar minute, en as dit aanhou gebeur, kontak ons by %(email)s met 'n skermskoot."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Fout in betalingsverwerking. Wag 'n oomblik en probeer weer. As die probleem langer as 24 uur voortduur, kontak ons asseblief by %(email)s met 'n skermskoot."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "versteekte kommentaar"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Lêer probleem: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Beter weergawe"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Wil u hierdie gebruiker rapporteer vir beledigende of onvanpaste gedrag?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Rapporteer misbruik"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Misbruik gerapporteer:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "U het hierdie gebruiker vir misbruik gerapporteer."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Antwoord"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Asseblief <a %(a_login)s>meld aan</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "U het 'n kommentaar gelaat. Dit mag 'n minuut neem om te verskyn."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Iets het verkeerd gegaan. Laai asseblief die bladsy weer en probeer weer."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s geaffekteerde bladsye"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Nie sigbaar in Libgen.rs Nie-Fiksie nie"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Nie sigbaar in Libgen.rs Fiksie"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Nie sigbaar in Libgen.li nie"

#, fuzzy
msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Gemerk as gebreek in Libgen.li"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Ontbreek van Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Gemerk as “spam” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Gemerk as “slegte lêer” in Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Nie alle bladsye kon na PDF omgeskakel word nie"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Exiftool het misluk op hierdie lêer"

#, fuzzy
msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Boek (onbekend)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Boek (nie-fiksie)"

#, fuzzy
msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Boek (fiksie)"

#, fuzzy
msgid "common.md5_content_type_mapping.journal_article"
msgstr "Joernaalartikel"

#, fuzzy
msgid "common.md5_content_type_mapping.standards_document"
msgstr "Standaarddokument"

#, fuzzy
msgid "common.md5_content_type_mapping.magazine"
msgstr "Tydskrif"

#, fuzzy
msgid "common.md5_content_type_mapping.book_comic"
msgstr "Strokiesprentboek"

#, fuzzy
msgid "common.md5_content_type_mapping.musical_score"
msgstr "Musiekpartituur"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Oudioboek"

#, fuzzy
msgid "common.md5_content_type_mapping.other"
msgstr "Ander"

#, fuzzy
msgid "common.access_types_mapping.aa_download"
msgstr "Vennootbediener aflaai"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

#, fuzzy
msgid "common.access_types_mapping.external_download"
msgstr "Eksterne aflaai"

#, fuzzy
msgid "common.access_types_mapping.external_borrow"
msgstr "Eksterne uitleen"

#, fuzzy
msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Eksterne uitleen (druk gestrem)"

#, fuzzy
msgid "common.access_types_mapping.meta_explore"
msgstr "Verken metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Bevat in torrents"

#, fuzzy
msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

#, fuzzy
msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "common.record_sources_mapping.zlib"
msgstr "Z-Biblioteek"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Chinese"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

#, fuzzy
msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Oplaai na AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "EBSCOhost eBoek Indeks"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Tsjeggiese metadata"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Boeke"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Russiese Staatsbiblioteek"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Titel"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Skrywer"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Uitgewer"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Uitgawe"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Jaar gepubliseer"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Oorspronklike lêernaam"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Beskrywing en metadata kommentaar"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Vennootbediener-aflaaie tydelik nie beskikbaar vir hierdie lêer nie."

#, fuzzy
msgid "common.md5.servers.fast_partner"
msgstr "Vinnige Vennootbediener #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(aanbeveel)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(geen blaaierverifikasie of waglyste nie)"

#, fuzzy
msgid "common.md5.servers.slow_partner"
msgstr "Stadige Vennootbediener #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(iets vinniger maar met waglys)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(geen waglys nie, maar kan baie stadig wees)"

#, fuzzy
msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

#, fuzzy
msgid "page.md5.box.download.lgrsnf"
msgstr "Libgen.rs Nie-Fiksie"

#, fuzzy
msgid "page.md5.box.download.lgrsfic"
msgstr "Libgen.rs Fiksie"

#, fuzzy
msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

#, fuzzy
msgid "page.md5.box.download.extra_also_click_get"
msgstr "(klik ook “KRY” bo-aan)"

#, fuzzy
msgid "page.md5.box.download.extra_click_get"
msgstr "(klik “KRY” bo-aan)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "hulle advertensies is bekend daarvoor dat dit kwaadwillige sagteware bevat, gebruik dus 'n advertensieblokker of klik nie op advertensies nie"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Nexus/STC lêers kan onbetroubaar wees om af te laai)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Biblioteek"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Biblioteek op Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(vereis die Tor-blaaier)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "page.md5.box.download.ia_borrow"
msgstr "Leen van die Internet Archive"

#, fuzzy
msgid "page.md5.box.download.print_disabled_only"
msgstr "(slegs vir drukgestremde beskermhere)"

#, fuzzy
msgid "page.md5.box.download.scihub_maybe"
msgstr "(geassosieerde DOI mag dalk nie in Sci-Hub beskikbaar wees nie)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "versameling"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

#, fuzzy
msgid "page.md5.box.download.bulk_torrents"
msgstr "Grootmaat torrent aflaaie"

#, fuzzy
msgid "page.md5.box.download.experts_only"
msgstr "(slegs vir kenners)"

#, fuzzy
msgid "page.md5.box.download.aa_isbn"
msgstr "Soek Anna se Argief vir ISBN"

#, fuzzy
msgid "page.md5.box.download.other_isbn"
msgstr "Soek verskeie ander databasisse vir ISBN"

#, fuzzy
msgid "page.md5.box.download.original_isbndb"
msgstr "Vind oorspronklike rekord in ISBNdb"

#, fuzzy
msgid "page.md5.box.download.aa_openlib"
msgstr "Soek Anna se Argief vir Open Library ID"

#, fuzzy
msgid "page.md5.box.download.original_openlib"
msgstr "Vind oorspronklike rekord in Open Library"

#, fuzzy
msgid "page.md5.box.download.aa_oclc"
msgstr "Soek Anna se Argief vir OCLC (WorldCat) nommer"

#, fuzzy
msgid "page.md5.box.download.original_oclc"
msgstr "Vind oorspronklike rekord in WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Soek Anna se Argief vir DuXiu SSID nommer"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Soek handmatig op DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Soek Anna se Argief vir CADAL SSNO nommer"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Vind oorspronklike rekord in CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Soek Anna se Argief vir DuXiu DXID nommer"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "EBSCOhost eBoek Indeks"

#, fuzzy
msgid "page.md5.box.download.scidb"
msgstr "Anna se Argief 🧬 SciDB"

#, fuzzy
msgid "common.md5.servers.no_browser_verification"
msgstr "(geen blaaierverifikasie benodig nie)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Tsjeggiese metadata %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Boeke %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "beskrywing"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Alternatiewe lêernaam"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Alternatiewe titel"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Alternatiewe skrywer"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Alternatiewe uitgewer"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Alternatiewe uitgawe"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Alternatiewe uitbreiding"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "metadata kommentare"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Alternatiewe beskrywing"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "datum oopbron gemaak"

#, fuzzy
msgid "page.md5.header.scihub"
msgstr "Sci-Hub lêer “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia"
msgstr "Internet Archive Beheerde Digitale Uitleen lêer “%(id)s”"

#, fuzzy
msgid "page.md5.header.ia_desc"
msgstr "Dit is 'n rekord van 'n lêer van die Internet Archive, nie 'n direk aflaaibare lêer nie. U kan probeer om die boek te leen (skakel hieronder), of gebruik hierdie URL wanneer u <a %(a_request)s>'n lêer versoek</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "As jy hierdie lêer het en dit is nog nie beskikbaar in Anna se Argief nie, oorweeg om dit <a %(a_request)s>op te laai</a>."

#, fuzzy
msgid "page.md5.header.meta_isbn"
msgstr "ISBNdb %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_openlib"
msgstr "Open Library %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_oclc"
msgstr "OCLC (WorldCat) nommer %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "DuXiu SSID %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "CADAL SSNO %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "MagzDB ID %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Nexus/STC ID %(id)s metadata rekord"

#, fuzzy
msgid "page.md5.header.meta_desc"
msgstr "Dit is 'n metadata rekord, nie 'n aflaaibare lêer nie. Jy kan hierdie URL gebruik wanneer jy <a %(a_request)s>'n lêer versoek</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata van gekoppelde rekord"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Verbeter metadata op Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Waarskuwing: veelvuldige gekoppelde rekords:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Verbeter metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Rapporteer lêerkwaliteit"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Aflaaityd"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Webwerf:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Soek Anna se Argief vir “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Kodes Verkenner:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Bekyk in Kodes Verkenner “%(name)s”"

#, fuzzy
msgid "page.md5.box.descr_read_more"
msgstr "Lees meer…"

#, fuzzy
msgid "page.md5.tabs.downloads"
msgstr "Aflaaie (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.borrow"
msgstr "Leen (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.explore_metadata"
msgstr "Verken metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Kommentaar (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.lists"
msgstr "Lyste (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.stats"
msgstr "Statistieke (%(count)s)"

#, fuzzy
msgid "common.tech_details"
msgstr "Tegniese besonderhede"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Hierdie lêer mag probleme hê, en is van 'n bronbiblioteek versteek.</span> Soms is dit op versoek van 'n kopiereghouer, soms is dit omdat 'n beter alternatief beskikbaar is, maar soms is dit as gevolg van 'n probleem met die lêer self. Dit mag dalk steeds reg wees om af te laai, maar ons beveel aan om eers na 'n alternatiewe lêer te soek. Meer besonderhede:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "'n Beter weergawe van hierdie lêer mag beskikbaar wees by %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "As jy steeds hierdie lêer wil aflaai, maak seker om slegs betroubare, opgedateerde sagteware te gebruik om dit oop te maak."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Vinnige aflaaie"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Vinnige aflaaie</strong> Word 'n <a %(a_membership)s>lid</a> om die langtermynbewaring van boeke, artikels en meer te ondersteun. Om ons dankbaarheid vir u ondersteuning te toon, kry u vinnige aflaaie. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "As jy hierdie maand skenk, kry jy <strong>dubbel</strong> die aantal vinnige aflaaie."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Vinnige aflaaie</strong> Jy het %(remaining)s oor vandag. Dankie dat jy 'n lid is! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Vinnige aflaaie</strong> U het vandag geen vinnige aflaaie meer oor nie."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Vinnige aflaaie</strong> Jy het hierdie lêer onlangs afgelaai. Skakels bly vir 'n rukkie geldig."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Opsie #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(geen herleiding)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(maak oop in kyker)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Verwys 'n vriend, en beide jy en jou vriend kry %(percentage)s%% bonus vinnige aflaaie!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Leer meer…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Stadige aflaaie"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Van betroubare vennote."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Meer inligting in die <a %(a_slow)s>FAQ</a>."

#, fuzzy
msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(kan vereis <a %(a_browser)s>blaaier verifikasie</a> — onbeperkte aflaaie!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Na aflaai:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Maak oop in ons kyker"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "wys eksterne aflaaie"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Eksterne aflaaie"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Geen aflaaie gevind nie."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Al die aflaai-opsies het dieselfde lêer, en behoort veilig te wees om te gebruik. Dit gesê, wees altyd versigtig wanneer jy lêers van die internet aflaai, veral van webwerwe buite Anna se Argief. Maak byvoorbeeld seker dat jou toestelle op datum is."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Vir groot lêers, beveel ons aan om 'n aflaaibestuurder te gebruik om onderbrekings te voorkom."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Aanbevole aflaaibestuurders: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "U sal 'n e-boek of PDF-leser nodig hê om die lêer oop te maak, afhangende van die lêerformaat."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Aanbevole e-boeklesers: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Anna se Argief aanlyn kyker"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Gebruik aanlyn gereedskap om tussen formate om te skakel."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Aanbevole omskakelingsgereedskap: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "U kan beide PDF- en EPUB-lêers na u Kindle of Kobo e-leser stuur."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Aanbevole gereedskap: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon se “Stuur na Kindle”"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz se “Stuur na Kobo/Kindle”"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Ondersteun skrywers en biblioteke"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "As u hiervan hou en dit kan bekostig, oorweeg dit om die oorspronklike te koop, of om die skrywers direk te ondersteun."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "As dit by u plaaslike biblioteek beskikbaar is, oorweeg om dit daar gratis te leen."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Lêerkwaliteit"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Help die gemeenskap deur die kwaliteit van hierdie lêer te rapporteer! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Rapporteer lêerprobleem (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Goeie lêerkwaliteit (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Voeg kommentaar by (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Wat is verkeerd met hierdie lêer?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Gebruik asseblief die <a %(a_copyright)s>DMCA / Kopiereg eisvorm</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Beskryf die probleem (vereis)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Probleembeskrywing"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 van 'n beter weergawe van hierdie lêer (indien van toepassing)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Vul dit in as daar 'n ander lêer is wat noukeurig ooreenstem met hierdie lêer (dieselfde uitgawe, dieselfde lêeruitbreiding as jy een kan vind), wat mense eerder moet gebruik as hierdie lêer. As jy van 'n beter weergawe van hierdie lêer buite Anna se Argief weet, laai dit asseblief <a %(a_upload)s>op</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Jy kan die md5 van die URL kry, bv."

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Dien verslag in"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Leer hoe om <a %(a_metadata)s>die metadata vir hierdie lêer self te verbeter</a>."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Dankie dat u u verslag ingedien het. Dit sal op hierdie bladsy gewys word, sowel as handmatig deur Anna hersien word (totdat ons 'n behoorlike moderasiestelsel het)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Iets het verkeerd gegaan. Laai asseblief die bladsy weer en probeer weer."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "As hierdie lêer van goeie gehalte is, kan u hieroor enige iets bespreek! Indien nie, gebruik asseblief die “Rapporteer lêerprobleem” knoppie."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Ek het hierdie boek baie geniet!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Laat kommentaar"

#, fuzzy
msgid "common.english_only"
msgstr "Die teks hieronder gaan voort in Engels."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Totale aflaaie: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "'n “Lêer MD5” is 'n hash wat bereken word vanaf die lêerinhoud, en is redelik uniek gebaseer op daardie inhoud. Alle skadubiblioteke wat ons hier geïndekseer het, gebruik hoofsaaklik MD5's om lêers te identifiseer."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "'n Lêer mag in verskeie skadubiblioteke verskyn. Vir inligting oor die verskeie datasets wat ons saamgestel het, sien die <a %(a_datasets)s>Datasets bladsy</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Dit is 'n lêer wat bestuur word deur die <a %(a_ia)s>IA se Beheerde Digitale Uitleen</a> biblioteek, en geïndekseer deur Anna se Argief vir soektog. Vir inligting oor die verskeie datasets wat ons saamgestel het, sien die <a %(a_datasets)s>Datasets bladsy</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Vir inligting oor hierdie spesifieke lêer, kyk na sy <a %(a_href)s>JSON lêer</a>."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Probleem om hierdie bladsy te laai"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Verfris asseblief om weer te probeer. <a %(a_contact)s>Kontak ons</a> as die probleem vir verskeie ure voortduur."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Nie gevind nie"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” is nie in ons databasis gevind nie."

#, fuzzy
msgid "page.login.title"
msgstr "Meld aan / Registreer"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Blaaierverifikasie"

#, fuzzy
msgid "page.login.text1"
msgstr "Om te voorkom dat spam-bots baie rekeninge skep, moet ons eers u blaaier verifieer."

#, fuzzy
msgid "page.login.text2"
msgstr "As jy in 'n oneindige lus vasgevang raak, beveel ons aan om <a %(a_privacypass)s>Privacy Pass</a> te installeer."

#, fuzzy
msgid "page.login.text3"
msgstr "Dit mag ook help om advertensieblokkeerders en ander blaaieruitbreidings af te skakel."

#, fuzzy
msgid "page.codes.title"
msgstr "Kodes"

#, fuzzy
msgid "page.codes.heading"
msgstr "Kodes Verkenner"

#, fuzzy
msgid "page.codes.intro"
msgstr "Verken die kodes waarmee rekords gemerk is, volgens voorvoegsel. Die “rekords” kolom wys die aantal rekords wat gemerk is met kodes met die gegewe voorvoegsel, soos gesien in die soekenjin (insluitend slegs metadata-rekords). Die “kodes” kolom wys hoeveel werklike kodes 'n gegewe voorvoegsel het."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Hierdie bladsy kan 'n rukkie neem om te genereer, daarom vereis dit 'n Cloudflare captcha. <a %(a_donate)s>Lede</a> kan die captcha oorslaan."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Moet asseblief nie hierdie bladsye krap nie. Ons beveel eerder aan om ons ElasticSearch en MariaDB databasisse <a %(a_import)s>te genereer</a> of <a %(a_download)s>af te laai</a>, en ons <a %(a_software)s>oopbron kode</a> te gebruik. Die rou data kan handmatig deur JSON-lêers soos <a %(a_json_file)s>hierdie een</a> verken word."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Voorvoegsel"

#, fuzzy
msgid "common.form.go"
msgstr "Gaan"

#, fuzzy
msgid "common.form.reset"
msgstr "Herstel"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Soek Anna se Argief"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Waarskuwing: kode het verkeerde Unicode karakters daarin, en mag verkeerd optree in verskeie situasies. Die rou binêre kan vanaf die base64 voorstelling in die URL gedekodeer word."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Bekende kode voorvoegsel “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Voorvoegsel"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Etiket"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Beskrywing"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL vir 'n spesifieke kode"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” sal vervang word met die kode se waarde"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "Generiese URL"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Webwerf"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s rekord wat ooreenstem met “%(prefix_label)s”"
msgstr[1] "%(count)s rekords wat ooreenstem met “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL vir spesifieke kode: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Meer…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kodes wat begin met “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks van"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "rekords"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kodes"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Minder as %(count)s rekords"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Vir DMCA / kopiereg-eise, gebruik <a %(a_copyright)s>hierdie vorm</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Enige ander maniere om ons te kontak oor kopiereg-eise sal outomaties uitgevee word."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Ons verwelkom u terugvoer en vrae baie graag!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "As gevolg van die hoeveelheid strooipos en onsin-e-posse wat ons ontvang, merk asseblief die blokkies om te bevestig dat u hierdie voorwaardes vir kontak verstaan."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Kopiereg-eise na hierdie e-pos sal geïgnoreer word; gebruik eerder die vorm."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Vennootbedieners is nie beskikbaar nie as gevolg van gasheer-sluitings. Hulle behoort binnekort weer beskikbaar te wees."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Lidmaatskappe sal dienooreenkomstig verleng word."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Moet ons nie e-pos om <a %(a_request)s>boeke te versoek</a><br>of klein (<10k) <a %(a_upload)s>oplaaie</a> nie."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Wanneer jy rekening- of skenkingsvrae vra, voeg jou rekening-ID, skermkiekies, kwitansies, soveel inligting as moontlik by. Ons kyk slegs elke 1-2 weke na ons e-pos, so om nie hierdie inligting in te sluit nie, sal enige oplossing vertraag."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Wys e-pos"

#, fuzzy
msgid "page.copyright.title"
msgstr "DMCA / Kopiereg-eisvorm"

#, fuzzy
msgid "page.copyright.intro"
msgstr "As u 'n DCMA of ander kopiereg-eis het, vul asseblief hierdie vorm so presies as moontlik in. As u enige probleme ondervind, kontak ons asseblief by ons toegewyde DMCA-adres: %(email)s. Let daarop dat eise wat na hierdie adres ge-e-pos word, nie verwerk sal word nie, dit is slegs vir vrae. Gebruik asseblief die vorm hieronder om u eise in te dien."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL's op Anna se Argief (vereis). Een per reël. Sluit asseblief slegs URL's in wat dieselfde uitgawe van 'n boek beskryf. As u 'n eis vir verskeie boeke of verskeie uitgawes wil indien, vul asseblief hierdie vorm verskeie kere in."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Eise wat verskeie boeke of uitgawes saamvoeg, sal verwerp word."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "U naam (vereis)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Adres (vereis)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Telefoonnommer (vereis)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-pos (vereis)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Duidelike beskrywing van die bronmateriaal (vereis)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN's van bronmateriaal (indien van toepassing). Een per reël. Sluit asseblief slegs dié in wat presies ooreenstem met die uitgawe waarvoor u 'n kopiereg-eis rapporteer."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL's van bronmateriaal, een per reël. Neem asseblief 'n oomblik om Open Library te soek vir u bronmateriaal. Dit sal ons help om u eis te verifieer."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL's na bronmateriaal, een per reël (vereis). Sluit asseblief soveel as moontlik in om ons te help om u eis te verifieer (bv. Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Verklaring en handtekening (vereis)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Dien eis in"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Dankie dat u u kopiereg-eis ingedien het. Ons sal dit so gou as moontlik hersien. Herlaai asseblief die bladsy om nog een in te dien."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Iets het verkeerd gegaan. Herlaai asseblief die bladsy en probeer weer."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "As u belangstel om hierdie datastel te spieël vir <a %(a_archival)s>argivering</a> of <a %(a_llm)s>LLM opleiding</a> doeleindes, kontak ons asseblief."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Ons missie is om al die boeke in die wêreld (sowel as artikels, tydskrifte, ens.) te argiveer en wyd toeganklik te maak. Ons glo dat alle boeke wyd en syd gespieël moet word om sekerheid en veerkragtigheid te verseker. Dit is hoekom ons lêers van 'n verskeidenheid bronne saamvoeg. Sommige bronne is heeltemal oop en kan in grootmaat gespieël word (soos Sci-Hub). Ander is gesluit en beskermend, so ons probeer om hulle te krap om hul boeke te “bevry”. Ander val êrens tussenin."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Al ons data kan <a %(a_torrents)s>getorrent</a> word, en al ons metadata kan <a %(a_anna_software)s>gegenereer</a> of <a %(a_elasticsearch)s>afgelaai</a> word as ElasticSearch en MariaDB databasisse. Die rou data kan handmatig deur JSON-lêers soos <a %(a_dbrecord)s>hierdie</a> verken word."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Oorsig"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Hieronder is 'n vinnige oorsig van die bronne van die lêers op Anna se Argief."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Bron"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Grootte"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% gespieël deur AA / torrents beskikbaar"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Persentasies van aantal lêers"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Laas opgedateer"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Nie-fiksie en Fiksie"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s lêer"
msgstr[1] "%(count)s lêers"

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Via Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: gevries sedert 2021; meeste beskikbaar deur torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: klein toevoegings sedertdien</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Uitsluitend “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Fiksie-torrents is agter (alhoewel ID's ~4-6M nie getorrent is nie aangesien hulle oorvleuel met ons Zlib-torrents)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Die “Chinese” versameling in Z-Library blyk dieselfde te wees as ons DuXiu-versameling, maar met verskillende MD5s. Ons sluit hierdie lêers uit van torrents om duplisering te vermy, maar wys hulle steeds in ons soekindeks."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "IA Beheerde Digitale Uitleen"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ van lêers is deursoekbaar."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Totaal"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Duplikate uitgesluit"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Aangesien die skadubiblioteke dikwels data van mekaar sinkroniseer, is daar aansienlike oorvleueling tussen die biblioteke. Daarom stem die getalle nie ooreen met die totaal nie."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Die “gespieël en gesaai deur Anna se Argief” persentasie wys hoeveel lêers ons self spieël. Ons saai daardie lêers in grootmaat deur torrents, en maak hulle beskikbaar vir direkte aflaai deur vennootwebwerwe."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Bronbiblioteke"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Sommige bronbiblioteke bevorder die massadeel van hul data deur torrents, terwyl ander nie hul versameling maklik deel nie. In laasgenoemde geval probeer Anna se Argief om hul versamelings te skraap en beskikbaar te maak (sien ons <a %(a_torrents)s>Torrents</a> bladsy). Daar is ook tussenin situasies, byvoorbeeld, waar bronbiblioteke bereid is om te deel, maar nie die hulpbronne het om dit te doen nie. In daardie gevalle probeer ons ook help."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Hieronder is 'n oorsig van hoe ons met die verskillende bronbiblioteke skakel."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Bron"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Lêers"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s Daaglikse <a %(dbdumps)s>HTTP databasis stortings</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Outomatiese torrents vir <a %(nonfiction)s>Nie-Fiksie</a> en <a %(fiction)s>Fiksie</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(covers)s>boekomslag torrents</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub het nuwe lêers sedert 2021 gevries."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Metadata stortings beskikbaar <a %(scihub1)s>hier</a> en <a %(scihub2)s>hier</a>, sowel as deel van die <a %(libgenli)s>Libgen.li databasis</a> (wat ons gebruik)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Data torrents beskikbaar <a %(scihub1)s>hier</a>, <a %(scihub2)s>hier</a>, en <a %(libgenli)s>hier</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Sommige nuwe lêers word <a %(libgenrs)s>bygevoeg</a> tot Libgen se “scimag”, maar nie genoeg om nuwe torrents te regverdig nie"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Kwartaalikse <a %(dbdumps)s>HTTP databasis stortings</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Nie-Fiksie torrents word gedeel met Libgen.rs (en weerspieël <a %(libgenli)s>hier</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Anna se Argief en Libgen.li bestuur gesamentlik versamelings van <a %(comics)s>strokiesprente</a>, <a %(magazines)s>tydskrifte</a>, <a %(standarts)s>standaarddokumente</a>, en <a %(fiction)s>fiksie (afgeskei van Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Hul “fiction_rus” versameling (Russiese fiksie) het geen toegewyde torrents nie, maar word gedek deur torrents van ander, en ons hou 'n <a %(fiction_rus)s>spieël</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Anna se Argief en Z-Biblioteek bestuur gesamentlik 'n versameling van <a %(metadata)s>Z-Biblioteek metadata</a> en <a %(files)s>Z-Biblioteek lêers</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Sommige metadata beskikbaar deur <a %(openlib)s>Open Library databasis stortings</a>, maar dit dek nie die hele IA-versameling nie"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Geen maklik toeganklike metadata stortings beskikbaar vir hul hele versameling nie"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(ia)s>IA metadata</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Lêers slegs beskikbaar vir uitleen op 'n beperkte basis, met verskeie toegangsbeperkings"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(ia)s>IA-lêers</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Verskeie metadata databasisse versprei oor die Chinese internet; alhoewel dikwels betaalde databasisse"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Geen maklik toeganklike metadata-dumps beskikbaar vir hul hele versameling nie."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(duxiu)s>DuXiu metadata</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Verskeie lêerdatabasisse versprei oor die Chinese internet; hoewel dikwels betaalde databasisse"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Meeste lêers slegs toeganklik met premium BaiduYun-rekeninge; stadige aflaaispoed."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(duxiu)s>DuXiu lêers</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Verskeie kleiner of eenmalige bronne. Ons moedig mense aan om eers na ander skadubiblioteke op te laai, maar soms het mense versamelings wat te groot is vir ander om deur te sorteer, hoewel nie groot genoeg om hul eie kategorie te regverdig nie."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Slegs-metadata bronne"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Ons verryk ook ons versameling met slegs-metadata bronne, wat ons kan koppel aan lêers, bv. deur ISBN-nommers of ander velde te gebruik. Hieronder is 'n oorsig van daardie bronne. Weereens, sommige van hierdie bronne is heeltemal oop, terwyl ons ander moet skraap."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Ons inspirasie vir die versameling van metadata is Aaron Swartz se doel van “een webbladsy vir elke boek wat ooit gepubliseer is”, waarvoor hy <a %(a_openlib)s>Open Library</a> geskep het. Daardie projek het goed gevaar, maar ons unieke posisie stel ons in staat om metadata te kry wat hulle nie kan nie. Nog 'n inspirasie was ons begeerte om te weet <a %(a_blog)s>hoeveel boeke daar in die wêreld is</a>, sodat ons kan bereken hoeveel boeke ons nog moet red."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Let daarop dat ons in metadata-soektogte die oorspronklike rekords wys. Ons doen geen samesmelting van rekords nie."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Laas opgedateer"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Maandelikse <a %(dbdumps)s>databasis-dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Nie direk in grootmaat beskikbaar nie, beskerm teen skraap"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(worldcat)s>OCLC (WorldCat) metadata</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Gevorderde databasis"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Ons kombineer al die bogenoemde bronne in een gevorderde databasis wat ons gebruik om hierdie webwerf te bedien. Hierdie gevorderde databasis is nie direk beskikbaar nie, maar aangesien Anna se Argief volledig oopbron is, kan dit redelik maklik <a %(a_generated)s>gegenereer</a> of <a %(a_downloaded)s>afgelaai</a> word as ElasticSearch en MariaDB databasisse. Die skripte op daardie bladsy sal outomaties al die nodige metadata van die bogenoemde bronne aflaai."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "As u ons data wil verken voordat u daardie skripte plaaslik uitvoer, kan u na ons JSON-lêers kyk, wat verder skakel na ander JSON-lêers. <a %(a_json)s>Hierdie lêer</a> is 'n goeie beginpunt."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Aangepas van ons <a %(a_href)s>blogpos</a>."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> is 'n massiewe databasis van gescande boeke, geskep deur die <a %(superstar_link)s>SuperStar Digital Library Group</a>. Die meeste is akademiese boeke, gescandeer om dit digitaal beskikbaar te maak vir universiteite en biblioteke. Vir ons Engelssprekende gehoor het <a %(princeton_link)s>Princeton</a> en die <a %(uw_link)s>Universiteit van Washington</a> goeie oorsigte. Daar is ook 'n uitstekende artikel wat meer agtergrond gee: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Die boeke van Duxiu is lankal op die Chinese internet gepirateer. Gewoonlik word hulle vir minder as 'n dollar deur herverkopers verkoop. Hulle word tipies versprei deur die Chinese ekwivalent van Google Drive, wat dikwels gekap is om meer stoorplek toe te laat. Sommige tegniese besonderhede kan <a %(link1)s>hier</a> en <a %(link2)s>hier</a> gevind word."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Alhoewel die boeke semi-openbaar versprei is, is dit redelik moeilik om hulle in grootmaat te verkry. Ons het dit hoog op ons TODO-lys gehad en het verskeie maande van voltydse werk daarvoor toegeken. In laat 2023 het 'n ongelooflike, wonderlike en talentvolle vrywilliger egter na ons uitgereik en ons vertel dat hulle al hierdie werk reeds gedoen het — teen groot koste. Hulle het die volledige versameling met ons gedeel, sonder om enigiets in ruil te verwag, behalwe die waarborg van langtermynbewaring. Werklik merkwaardig."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Hulpbronne"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Totale lêers: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Totale lêergrootte: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Lêers gespieël deur Anna se Argief: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Laas opgedateer: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrents deur Anna se Argief"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Voorbeeldrekord op Anna se Argief"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Ons blogpos oor hierdie data"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skripte vir die invoer van metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Anna se Argiefhouers formaat"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Meer inligting van ons vrywilligers (rou notas):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Beheerde Digitale Uitleen"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Hierdie datastel is nou verwant aan die <a %(a_datasets_openlib)s>Open Library-datastel</a>. Dit bevat 'n skraap van alle metadata en 'n groot gedeelte van lêers van die IA se Beheerde Digitale Uitleenbiblioteek. Opdaterings word vrygestel in die <a %(a_aac)s>Anna se Argiefhouers-formaat</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Hierdie rekords word direk verwys vanaf die Open Library-datastel, maar bevat ook rekords wat nie in Open Library is nie. Ons het ook 'n aantal datalêers wat oor die jare deur gemeenskapslede geskraap is."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Die versameling bestaan uit twee dele. U benodig albei dele om alle data te kry (behalwe vervangde torrents, wat op die torrents-bladsy deurgehaal is)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "ons eerste vrystelling, voordat ons gestandaardiseer het op die <a %(a_aac)s>Anna se Argiefhouers (AAC) formaat</a>. Bevat metadata (as json en xml), pdfs (van acsm en lcpdf digitale uitleenstelsels), en voorbladduimnaels."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "inkrementele nuwe vrystellings, met behulp van AAC. Bevat slegs metadata met tydstempels na 2023-01-01, aangesien die res reeds deur “ia” gedek is. Ook alle pdf-lêers, hierdie keer van die acsm en “bookreader” (IA se webleser) uitleenstelsels. Ten spyte van die naam wat nie heeltemal reg is nie, plaas ons steeds bookreader-lêers in die ia2_acsmpdf_files-versameling, aangesien hulle mekaar uitsluit."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Hoof %(source)s webwerf"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Digitale Uitleenbiblioteek"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Metadata dokumentasie (meeste velde)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "ISBN landinligting"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Die Internasionale ISBN-agentskap stel gereeld die reekse vry wat dit aan nasionale ISBN-agentskappe toegewys het. Hieruit kan ons aflei aan watter land, streek of taalgroep hierdie ISBN behoort. Ons gebruik tans hierdie data indirek, deur die <a %(a_isbnlib)s>isbnlib</a> Python-biblioteek."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Hulpbronne"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Laas opgedateer: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "ISBN webwerf"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Vir die agtergrond van die verskillende Library Genesis forks, sien die bladsy vir die <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Die Libgen.li bevat die meeste van dieselfde inhoud en metadata as die Libgen.rs, maar het 'n paar versamelings bo-op dit, naamlik strokiesprente, tydskrifte en standaarddokumente. Dit het ook <a %(a_scihub)s>Sci-Hub</a> in sy metadata en soekenjin geïntegreer, wat ons vir ons databasis gebruik."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Die metadata vir hierdie biblioteek is gratis beskikbaar <a %(a_libgen_li)s>by libgen.li</a>. Hierdie bediener is egter stadig en ondersteun nie die hervat van gebroke verbindings nie. Dieselfde lêers is ook beskikbaar op <a %(a_ftp)s>'n FTP-bediener</a>, wat beter werk."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrents is beskikbaar vir die meeste van die bykomende inhoud, veral torrents vir strokiesprente, tydskrifte, en standaarddokumente is vrygestel in samewerking met Anna se Argief."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Die fiksieversameling het sy eie torrents (afgeskei van <a %(a_href)s>Libgen.rs</a>) wat begin by %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Volgens die Libgen.li administrateur, behoort die “fiction_rus” (Russiese fiksie) versameling gedek te word deur gereeld vrygestelde torrents van <a %(a_booktracker)s>booktracker.org</a>, veral die <a %(a_flibusta)s>flibusta</a> en <a %(a_librusec)s>lib.rus.ec</a> torrents (wat ons <a %(a_torrents)s>hier</a> spieël, alhoewel ons nog nie vasgestel het watter torrents ooreenstem met watter lêers nie)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistieke vir alle versamelings kan gevind word <a %(a_href)s>op libgen se webwerf</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Niefiksie blyk ook afgewyk te het, maar sonder nuwe torrents. Dit lyk of dit sedert vroeg in 2022 gebeur het, hoewel ons dit nie geverifieer het nie."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Sekere reekse sonder torrents (soos fiksie reekse f_3463000 tot f_4260000) is waarskynlik Z-Biblioteek (of ander duplikaat) lêers, alhoewel ons dalk 'n bietjie deduplikasie wil doen en torrents maak vir lgli-unieke lêers in hierdie reekse."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Let daarop dat die torrent-lêers wat na “libgen.is” verwys, uitdruklik spieëls is van <a %(a_libgen)s>Libgen.rs</a> (“.is” is 'n ander domein wat deur Libgen.rs gebruik word)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "'n Hulpbron om die metadata te gebruik is <a %(a_href)s>hierdie bladsy</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Fiksie torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Strokiesprente torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Tydskrifte torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Standaarddokument torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Russiese fiksie torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata via FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Metadata veldinligting"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Spieël van ander torrents (en unieke fiksie en strokiesprente torrents)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Besprekingsforum"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Ons blogpos oor die strokiesprente vrystelling"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Die kort verhaal van die verskillende Library Genesis (of “Libgen”) vertakkings, is dat oor tyd, die verskillende mense betrokke by Library Genesis 'n uitval gehad het, en hul eie paaie gegaan het."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Die “.fun” weergawe is deur die oorspronklike stigter geskep. Dit word herontwerp ten gunste van 'n nuwe, meer verspreide weergawe."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Die “.rs” weergawe het baie soortgelyke data, en stel die meeste konsekwent hul versameling in grootmaat torrents vry. Dit is ongeveer verdeel in 'n “fiksie” en 'n “nie-fiksie” afdeling."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Oorspronklik by “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Die <a %(a_li)s>“.li” weergawe</a> het 'n massiewe versameling strokiesprente, sowel as ander inhoud, wat nie (nog nie) beskikbaar is vir grootmaat aflaai deur torrents nie. Dit het wel 'n aparte torrent versameling van fiksieboeke, en dit bevat die metadata van <a %(a_scihub)s>Sci-Hub</a> in sy databasis."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Volgens hierdie <a %(a_mhut)s>forumpos</a> was Libgen.li oorspronklik gehuisves by “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> is in 'n sekere sin ook 'n vertakking van Library Genesis, alhoewel hulle 'n ander naam vir hul projek gebruik het."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Hierdie bladsy gaan oor die “.rs” weergawe. Dit is bekend daarvoor dat dit konsekwent beide sy metadata en die volledige inhoud van sy boekkatalogus publiseer. Sy boekversameling is verdeel tussen 'n fiksie en nie-fiksie gedeelte."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "'n Hulpbron om die metadata te gebruik is <a %(a_metadata)s>hierdie bladsy</a> (blokkeer IP-reekse, VPN mag nodig wees)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Vanaf 2024-03 word nuwe torrents in <a %(a_href)s>hierdie forumdraad</a> geplaas (blokkeer IP-reekse, VPN mag nodig wees)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Nie-fiksie torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Fiksie torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Libgen.rs Metadata"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Libgen.rs metadata veldinligting"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Libgen.rs Nie-fiksie torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Libgen.rs Fiksie torrents"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Libgen.rs Besprekingsforum"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrents deur Anna se Argief (boekomslae)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Ons blog oor die boekomslagvrystelling"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis is bekend daarvoor dat hulle reeds hul data ruimskoots in grootmaat deur torrents beskikbaar stel. Ons Libgen-versameling bestaan uit bykomende data wat hulle nie direk vrystel nie, in vennootskap met hulle. Baie dankie aan almal betrokke by Library Genesis vir die samewerking met ons!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Vrystelling 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "Hierdie <a %(blog_post)s>eerste vrystelling</a> is redelik klein: ongeveer 300GB van boekomslae van die Libgen.rs-vurk, beide fiksie en nie-fiksie. Hulle is georganiseer op dieselfde manier as hoe hulle op libgen.rs verskyn, bv.:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s vir 'n nie-fiksie boek."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s vir 'n fiksie boek."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Net soos met die Z-Library-versameling, het ons hulle almal in 'n groot .tar-lêer gesit, wat gemonteer kan word met <a %(a_ratarmount)s>ratarmount</a> as jy die lêers direk wil bedien."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> is 'n eie databasis deur die nie-winsgewende <a %(a_oclc)s>OCLC</a>, wat metadata-rekords van biblioteke regoor die wêreld versamel. Dit is waarskynlik die grootste biblioteek-metadata-versameling in die wêreld."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, aanvanklike vrystelling:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "In Oktober 2023 het ons 'n omvattende skraap van die OCLC (WorldCat) databasis <a %(a_scrape)s>vrygestel</a>, in die <a %(a_aac)s>Anna se Argief Houers formaat</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrents deur Anna se Argief"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Ons blogpos oor hierdie data"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library is 'n oopbronprojek deur die Internet Archive om elke boek in die wêreld te katalogiseer. Dit het een van die wêreld se grootste boekskandeerbedrywighede, en het baie boeke beskikbaar vir digitale uitleen. Sy boekmetadata-katalogus is gratis beskikbaar vir aflaai, en is ingesluit op Anna se Argief (alhoewel tans nie in soektog nie, behalwe as jy spesifiek vir 'n Open Library ID soek)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Vrystelling 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Dit is 'n dump van baie oproepe na isbndb.com gedurende September 2022. Ons het probeer om alle ISBN-reekse te dek. Dit is ongeveer 30.9 miljoen rekords. Op hul webwerf beweer hulle dat hulle eintlik 32.6 miljoen rekords het, so ons het dalk op een of ander manier 'n paar gemis, of <em>hulle</em> kan iets verkeerd doen."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Die JSON-reaksies is redelik rou van hul bediener. Een data kwaliteit probleem wat ons opgemerk het, is dat vir ISBN-13 nommers wat met 'n ander voorvoegsel as “978-” begin, hulle steeds 'n “isbn” veld insluit wat eenvoudig die ISBN-13 nommer is met die eerste 3 nommers afgekap (en die kontrolesyfer herbereken). Dit is duidelik verkeerd, maar dit is hoe hulle dit blykbaar doen, so ons het dit nie verander nie."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Nog 'n potensiële probleem wat jy mag teëkom, is die feit dat die “isbn13” veld duplikate het, so jy kan dit nie as 'n primêre sleutel in 'n databasis gebruik nie. “isbn13”+“isbn” velde gekombineer blyk uniek te wees."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Vir 'n agtergrond oor Sci-Hub, verwys asseblief na sy <a %(a_scihub)s>amptelike webwerf</a>, <a %(a_wikipedia)s>Wikipedia-bladsy</a>, en hierdie <a %(a_radiolab)s>podsendingsonderhoud</a>."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Let daarop dat Sci-Hub sedert <a %(a_reddit)s>2021 gevries is</a>. Dit was voorheen gevries, maar in 2021 is 'n paar miljoen artikels bygevoeg. Nog steeds word 'n beperkte aantal artikels by die Libgen “scimag” versamelings gevoeg, hoewel nie genoeg om nuwe grootmaat-torrents te regverdig nie."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Ons gebruik die Sci-Hub metadata soos verskaf deur <a %(a_libgen_li)s>Libgen.li</a> in sy “scimag” versameling. Ons gebruik ook die <a %(a_dois)s>dois-2022-02-12.7z</a> datastel."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Let daarop dat die “smarch” torrents <a %(a_smarch)s>verouderd</a> is en daarom nie in ons torrents lys ingesluit is nie."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrents op Anna se Argief"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata en torrents"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrents op Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrents op Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Opdaterings op Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Wikipedia-bladsy"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Podsendingsonderhoud"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Oplaaisels na Anna se Argief"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Oorsig vanaf <a %(a1)s>datasets-bladsy</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Verskeie kleiner of eenmalige bronne. Ons moedig mense aan om eers na ander skadubiblioteke op te laai, maar soms het mense versamelings wat te groot is vir ander om deur te sorteer, hoewel nie groot genoeg om hul eie kategorie te regverdig nie."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Die “oplaai” versameling is opgedeel in kleiner subversamelings, wat aangedui word in die AACIDs en torrentname. Alle subversamelings is eers gededupliseer teen die hoofversameling, hoewel die metadata “upload_records” JSON-lêers steeds baie verwysings na die oorspronklike lêers bevat. Nie-boeklêers is ook uit die meeste subversamelings verwyder, en word tipies <em>nie</em> in die “upload_records” JSON aangedui nie."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Baie subversamelings bestaan self uit sub-sub-versamelings (bv. van verskillende oorspronklike bronne), wat as gidse in die “filepath” velde voorgestel word."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Die subversamelings is:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subversameling"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Notas"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "blaai"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "soek"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Van <a %(a_href)s>aaaaarg.fail</a>. Lyk redelik volledig te wees. Van ons vrywilliger “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Van 'n <a %(a_href)s><q>ACM Digital Library 2020</q></a> torrent. Het 'n redelike hoë oorvleueling met bestaande versamelings van artikels, maar baie min MD5-ooreenkomste, so ons het besluit om dit heeltemal te hou."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Afskraap van <q>iRead eBooks</q> (= foneties <q>ai rit i-books</q>; airitibooks.com), deur vrywilliger <q>j</q>. Kom ooreen met <q>airitibooks</q> metadata in <a %(a1)s><q>Ander metadata-afskrapings</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Van 'n versameling <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Gedeeltelik van die oorspronklike bron, gedeeltelik van the-eye.eu, gedeeltelik van ander spieëls."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Van 'n private boeke-torrent-webwerf, <a %(a_href)s>Bibliotik</a> (dikwels na verwys as “Bib”), waarvan boeke in torrents gebundel is volgens naam (A.torrent, B.torrent) en versprei is deur the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Van ons vrywilliger “bpb9v”. Vir meer inligting oor <a %(a_href)s>CADAL</a>, sien die notas op ons <a %(a_duxiu)s>DuXiu datastelbladsy</a>."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Meer van ons vrywilliger “bpb9v”, meestal DuXiu-lêers, sowel as 'n gids “WenQu” en “SuperStar_Journals” (SuperStar is die maatskappy agter DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Van ons vrywilliger “cgiym”, Chinese tekste van verskeie bronne (verteenwoordig as subgidse), insluitend van <a %(a_href)s>China Machine Press</a> (’n groot Chinese uitgewer)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Nie-Chinese versamelings (verteenwoordig as subgidse) van ons vrywilliger “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Afskraap van boeke oor Chinese argitektuur, deur vrywilliger <q>cm</q>: <q>Ek het dit verkry deur 'n netwerk kwesbaarheid by die uitgewershuis te benut, maar daardie gaping is sedertdien gesluit</q>. Kom ooreen met <q>chinese_architecture</q> metadata in <a %(a1)s><q>Ander metadata-afskrapings</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Boeke van akademiese uitgewer <a %(a_href)s>De Gruyter</a>, versamel van 'n paar groot torrents."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Afskraapsel van <a %(a_href)s>docer.pl</a>, 'n Poolse lêerdelingswebwerf wat fokus op boeke en ander geskrewe werke. Afgeskraap in laat 2023 deur vrywilliger “p”. Ons het nie goeie metadata van die oorspronklike webwerf nie (nie eens lêeruitbreidings nie), maar ons het gefiltreer vir boekagtige lêers en was dikwels in staat om metadata uit die lêers self te onttrek."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, direk van DuXiu, versamel deur vrywilliger “w”. Slegs onlangse DuXiu-boeke is direk beskikbaar deur e-boeke, so die meeste van hierdie moet onlangse wees."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Oorblywende DuXiu-lêers van vrywilliger “m”, wat nie in die DuXiu-eie PDG-formaat was nie (die hoof <a %(a_href)s>DuXiu datastel</a>). Versamel van baie oorspronklike bronne, ongelukkig sonder om daardie bronne in die lêerpad te bewaar."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Afskraap van erotiese boeke, deur vrywilliger <q>do no harm</q>. Kom ooreen met <q>hentai</q> metadata in <a %(a1)s><q>Ander metadata-afskrapings</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Versameling afgeskraap van 'n Japannese Manga-uitgewer deur vrywilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Geselekteerde geregtelike argiewe van Longquan</a>, verskaf deur vrywilliger “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Afskraapsel van <a %(a_href)s>magzdb.org</a>, 'n bondgenoot van Library Genesis (dit is gekoppel op die libgen.rs tuisblad) maar wat nie hul lêers direk wou verskaf nie. Verkry deur vrywilliger “p” in laat 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Verskeie klein oplaaisels, te klein as hul eie subversameling, maar verteenwoordig as gidse."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "E-boeke van AvaxHome, 'n Russiese lêerdelingswebwerf."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Argief van koerante en tydskrifte. Kom ooreen met <q>newsarch_magz</q> metadata in <a %(a1)s><q>Ander metadata-afskrapings</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Afskraap van die <a %(a1)s>Philosophy Documentation Center</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Versameling van vrywilliger “o” wat Poolse boeke direk van oorspronklike vrystellings (“scene”) webwerwe versamel het."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Gekombineerde versamelings van <a %(a_href)s>shuge.org</a> deur vrywilligers “cgiym” en “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Imperial Library of Trantor”</a> (genoem na die fiktiewe biblioteek), afgeskraap in 2022 deur vrywilliger “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-versamelings (verteenwoordig as gidse) van vrywilliger “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (deur <a %(a_sikuquanshu)s>Dizhi(迪志)</a> in Taiwan), mebook (mebook.cc, 我的小书屋, my little bookroom — woz9ts: “Hierdie webwerf fokus hoofsaaklik op die deel van hoë kwaliteit e-boeklêers, waarvan sommige deur die eienaar self geset is. Die eienaar is <a %(a_arrested)s>in hegtenis geneem</a> in 2019 en iemand het 'n versameling van lêers wat hy gedeel het, gemaak.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Oorblywende DuXiu-lêers van vrywilliger “woz9ts”, wat nie in die DuXiu-eiendomlike PDG-formaat was nie (nog om na PDF omgeskakel te word)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrents deur Anna se Argief"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Z-Library skraap"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library het sy wortels in die <a %(a_href)s>Library Genesis</a>-gemeenskap, en het oorspronklik met hul data begin. Sedertdien het dit aansienlik geprofessionaliseer en het 'n baie meer moderne koppelvlak. Hulle is dus in staat om baie meer donasies te kry, beide geldelik om hul webwerf te verbeter, sowel as donasies van nuwe boeke. Hulle het 'n groot versameling bykomend tot Library Genesis opgebou."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Opdatering vanaf Februarie 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "In laat 2022 is die beweerde stigters van Z-Library gearresteer, en domeine is deur die Verenigde State se owerhede gekonfiskeer. Sedertdien het die webwerf stadigaan weer aanlyn gekom. Dit is onbekend wie dit tans bestuur."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Die versameling bestaan uit drie dele. Die oorspronklike beskrywingsbladsye vir die eerste twee dele word hieronder bewaar. Jy benodig al drie dele om al die data te kry (behalwe vervangde torrents, wat op die torrents-bladsy deurgehaal is)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: ons eerste vrystelling. Dit was die heel eerste vrystelling van wat toe die “Pirate Library Mirror” (“pilimi”) genoem is."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: tweede vrystelling, hierdie keer met alle lêers toegedraai in .tar-lêers."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: inkrementele nuwe vrystellings, met behulp van die <a %(a_href)s>Anna se Argiefhouers (AAC) formaat</a>, nou vrygestel in samewerking met die Z-Library-span."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrents deur Anna se Argief (metadata + inhoud)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Voorbeeldrekord op Anna se Argief (oorspronklike versameling)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Voorbeeldrekord op Anna se Argief (“zlib3” versameling)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Hoofwebwerf"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Tor-domein"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Blogpos oor Vrystelling 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Blogpos oor Vrystelling 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Zlib-vrystellings (oorspronklike beskrywingsbladsye)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Vrystelling 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Die aanvanklike spieël is moeisaam verkry oor die loop van 2021 en 2022. Op hierdie stadium is dit effens verouderd: dit weerspieël die toestand van die versameling in Junie 2021. Ons sal dit in die toekoms opdateer. Op die oomblik fokus ons daarop om hierdie eerste vrystelling uit te kry."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Aangesien Library Genesis reeds met openbare torrents bewaar word, en ingesluit is in die Z-Library, het ons 'n basiese deduplikasie teen Library Genesis in Junie 2022 gedoen. Hiervoor het ons MD5-hashes gebruik. Daar is waarskynlik baie meer duplikaatinhoud in die biblioteek, soos verskeie lêerformate met dieselfde boek. Dit is moeilik om akkuraat op te spoor, so ons doen dit nie. Na die deduplikasie het ons meer as 2 miljoen lêers oor, wat net onder 7TB beloop."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Die versameling bestaan uit twee dele: 'n MySQL “.sql.gz” dump van die metadata, en die 72 torrentlêers van ongeveer 50-100GB elk. Die metadata bevat die data soos gerapporteer deur die Z-Library-webwerf (titel, outeur, beskrywing, lêertipe), sowel as die werklike lêergrootte en md5sum wat ons waargeneem het, aangesien hierdie soms nie ooreenstem nie. Dit lyk of daar reekse lêers is waarvoor die Z-Library self verkeerde metadata het. Ons mag ook in sommige geïsoleerde gevalle verkeerd afgelaaide lêers hê, wat ons in die toekoms sal probeer opspoor en regmaak."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Die groot torrentlêers bevat die werklike boekdata, met die Z-Library ID as die lêernaam. Die lêeruitbreidings kan met behulp van die metadata-dump heropgebou word."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Die versameling is 'n mengsel van nie-fiksie en fiksie inhoud (nie geskei soos in Library Genesis nie). Die kwaliteit wissel ook wyd."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Hierdie eerste vrystelling is nou volledig beskikbaar. Let daarop dat die torrentlêers slegs deur ons Tor-spieël beskikbaar is."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Vrystelling 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Ons het al die boeke gekry wat tussen ons laaste spieël en Augustus 2022 by die Z-Library gevoeg is. Ons het ook teruggegaan en 'n paar boeke afgelaai wat ons die eerste keer gemis het. Altesaam is hierdie nuwe versameling ongeveer 24TB. Weereens, hierdie versameling is gededupliceer teen Library Genesis, aangesien daar reeds torrents beskikbaar is vir daardie versameling."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Die data is soortgelyk georganiseer as die eerste vrystelling. Daar is 'n MySQL “.sql.gz” dump van die metadata, wat ook al die metadata van die eerste vrystelling insluit, en dit dus vervang. Ons het ook 'n paar nuwe kolomme bygevoeg:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: of hierdie lêer reeds in Library Genesis is, in óf die nie-fiksie óf fiksie versameling (ooreenstem met md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: watter torrent hierdie lêer in is."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: gestel wanneer ons nie die boek kon aflaai nie."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Ons het dit laas keer genoem, maar net om te verduidelik: “filename” en “md5” is die werklike eienskappe van die lêer, terwyl “filename_reported” en “md5_reported” is wat ons van Z-Library afgelaai het. Soms stem hierdie twee nie met mekaar ooreen nie, so ons het albei ingesluit."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Vir hierdie vrystelling het ons die sortering verander na “utf8mb4_unicode_ci”, wat versoenbaar moet wees met ouer weergawes van MySQL."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Die datalêers is soortgelyk aan laas keer, alhoewel hulle baie groter is. Ons kon eenvoudig nie die moeite doen om tonne kleiner torrentlêers te skep nie. “pilimi-zlib2-0-14679999-extra.torrent” bevat al die lêers wat ons in die laaste vrystelling gemis het, terwyl die ander torrents almal nuwe ID-reekse is. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Opdatering %(date)s:</strong> Ons het die meeste van ons torrents te groot gemaak, wat veroorsaak het dat torrentkliënte gesukkel het. Ons het hulle verwyder en nuwe torrents vrygestel."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Opdatering %(date)s:</strong> Daar was steeds te veel lêers, so ons het hulle in tar-lêers toegedraai en weer nuwe torrents vrygestel."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Vrystelling 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Dit is 'n enkele ekstra torrentlêer. Dit bevat geen nuwe inligting nie, maar dit het 'n paar data daarin wat 'n rukkie kan neem om te bereken. Dit maak dit gerieflik om te hê, aangesien die aflaai van hierdie torrent dikwels vinniger is as om dit van nuuts af te bereken. Dit bevat veral SQLite-indekse vir die tar-lêers, vir gebruik met <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Gereelde Vrae (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Wat is Anna se Argief?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Anna se Argief</span> is 'n nie-winsgewende projek met twee doelwitte:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Bewaring:</strong> Rugsteun van alle kennis en kultuur van die mensdom.</li><li><strong>Toegang:</strong> Maak hierdie kennis en kultuur beskikbaar vir enige iemand in die wêreld.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Al ons <a %(a_code)s>kode</a> en <a %(a_datasets)s>data</a> is heeltemal oopbron."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Bewaring"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Ons bewaar boeke, artikels, strokiesprente, tydskrifte, en meer, deur hierdie materiaal van verskeie <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">skadubiblioteke</a>, amptelike biblioteke, en ander versamelings bymekaar te bring op een plek. Al hierdie data word vir ewig bewaar deur dit maklik te maak om dit in grootmaat te dupliseer — met behulp van torrents — wat lei tot baie kopieë regoor die wêreld. Sommige skadubiblioteke doen dit reeds self (bv. Sci-Hub, Library Genesis), terwyl Anna se Argief ander biblioteke “bevry” wat nie grootmaatverspreiding aanbied nie (bv. Z-Library) of glad nie skadubiblioteke is nie (bv. Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Hierdie wye verspreiding, gekombineer met oopbron-kode, maak ons webwerf bestand teen verwyderings en verseker die langtermynbewaring van die mensdom se kennis en kultuur. Leer meer oor <a href=\"/datasets\">ons datastelle</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Ons skat dat ons ongeveer <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% van die wêreld se boeke bewaar het</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Toegang"

#, fuzzy
msgid "page.home.access.text"
msgstr "Ons werk saam met vennote om ons versamelings maklik en gratis toeganklik te maak vir enigiemand. Ons glo dat almal die reg het tot die kollektiewe wysheid van die mensdom. En <a %(a_search)s>nie ten koste van skrywers nie</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Uurlikse aflaaie in die laaste 30 dae. Uurlikse gemiddelde: %(hourly)s. Daaglikse gemiddelde: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Ons glo sterk in die vrye vloei van inligting, en die bewaring van kennis en kultuur. Met hierdie soekenjin bou ons op die skouers van reuse. Ons respekteer die harde werk van die mense wat die verskeie skadubiblioteke geskep het, en ons hoop dat hierdie soekenjin hul bereik sal verbreed."

#, fuzzy
msgid "page.about.text3"
msgstr "Om op hoogte te bly van ons vordering, volg Anna op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Vir vrae en terugvoer, kontak asseblief vir Anna by %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Hoe kan ek help?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Volg ons op <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Versprei die woord oor Anna se Argief op Twitter, Reddit, Tiktok, Instagram, by jou plaaslike kafee of biblioteek, of waar jy ook al gaan! Ons glo nie in hekbewaking nie — as ons afgehaal word, sal ons net elders opduik, aangesien al ons kode en data volledig oopbron is.</li><li>3. Indien jy kan, oorweeg om <a href=\"/donate\">te skenk</a>.</li><li>4. Help om ons webwerf in verskillende tale te <a href=\"https://translate.annas-software.org/\">vertaal</a>.</li><li>5. As jy 'n sagteware-ingenieur is, oorweeg om by te dra tot ons <a href=\"https://annas-software.org/\">oopbron</a>, of om ons <a href=\"/datasets\">torrents</a> te saai.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Ons het nou ook 'n gesinkroniseerde Matrix-kanaal by %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. As jy 'n sekuriteitsnavorser is, kan ons jou vaardighede gebruik vir beide aanval en verdediging. Kyk na ons <a %(a_security)s>Sekuriteit</a> bladsy."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Ons is op soek na kundiges in betalings vir anonieme handelaars. Kan jy ons help om meer gerieflike maniere by te voeg om te skenk? PayPal, WeChat, geskenkkaarte. As jy iemand ken, kontak ons asseblief."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Ons is altyd op soek na meer bedienerkapasiteit."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Jy kan help deur lêerkwessies aan te meld, kommentaar te lewer, en lyste reg op hierdie webwerf te skep. Jy kan ook help deur <a %(a_upload)s>meer boeke op te laai</a>, of lêerkwessies of formatering van bestaande boeke reg te maak."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Skep of help om die Wikipedia-bladsy vir Anna se Argief in jou taal te onderhou."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Ons is op soek na klein, smaakvolle advertensies. As jy op Anna se Argief wil adverteer, laat weet ons asseblief."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Ons sal graag wil hê dat mense <a %(a_mirrors)s>spieëls</a> opstel, en ons sal dit finansieel ondersteun."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Vir meer uitgebreide inligting oor hoe om vrywillig te werk, sien ons <a %(a_volunteering)s>Vrywilligers & Belonings</a> bladsy."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Waarom is die stadige aflaaie so stadig?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Ons het letterlik nie genoeg hulpbronne om almal in die wêreld hoëspoed aflaaie te gee nie, hoe graag ons ook al wil. As 'n ryk weldoener sou opstaan en dit vir ons voorsien, sou dit ongelooflik wees, maar tot dan probeer ons ons bes. Ons is 'n nie-winsgewende projek wat skaars deur donasies kan oorleef."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Dit is hoekom ons twee stelsels vir gratis aflaaie geïmplementeer het, saam met ons vennote: gedeelde bedieners met stadige aflaaie, en effens vinniger bedieners met 'n waglys (om die aantal mense wat terselfdertyd aflaai te verminder)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Ons het ook <a %(a_verification)s>blaaierverifikasie</a> vir ons stadige aflaaie, want anders sal bots en skrapers dit misbruik, wat dinge selfs stadiger maak vir wettige gebruikers."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Let daarop dat, wanneer u die Tor-blaaier gebruik, u dalk u sekuriteitsinstellings moet aanpas. Op die laagste van die opsies, genaamd “Standaard”, slaag die Cloudflare turnstile uitdaging. Op die hoër opsies, genaamd “Veiliger” en “Veiligste”, misluk die uitdaging."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Vir groot lêers kan stadige aflaaie soms in die middel breek. Ons beveel aan om 'n aflaaibestuurder (soos JDownloader) te gebruik om groot aflaaie outomaties te hervat."

#, fuzzy
msgid "page.donate.faq.title"
msgstr "Donasie V&A"

#, fuzzy
msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Hernu lidmaatskappe outomaties?</div> Lidmaatskappe <strong>hernuw nie</strong> outomaties nie. Jy kan aansluit vir so lank of kort as wat jy wil."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Kan ek my lidmaatskap opgradeer of verskeie lidmaatskappe kry?</div>"

#, fuzzy
msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Het jy ander betaalmetodes?</div> Tans nie. Baie mense wil nie hê dat argiewe soos hierdie moet bestaan nie, so ons moet versigtig wees. As jy ons kan help om ander (meer gerieflike) betaalmetodes veilig op te stel, kontak ons asseblief by %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Wat beteken die reekse per maand?</div> U kan aan die laer kant van 'n reeks kom deur al die afslag toe te pas, soos om 'n tydperk langer as 'n maand te kies."

#, fuzzy
msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Waarvoor gebruik julle skenkings?</div> 100%% gaan na die bewaring en toeganklikmaking van die wêreld se kennis en kultuur. Tans bestee ons dit meestal aan bedieners, berging en bandwydte. Geen geld gaan persoonlik na enige spanlede nie."

#, fuzzy
msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Kan ek 'n groot skenking maak?</div> Dit sal wonderlik wees! Vir skenkings van meer as 'n paar duisend dollar, kontak ons asseblief direk by %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Kan ek 'n skenking maak sonder om 'n lid te word?</div> Natuurlik. Ons aanvaar skenkings van enige bedrag op hierdie Monero (XMR) adres: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Hoe laai ek nuwe boeke op?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Alternatiewelik, kan jy dit na Z-Library <a %(a_upload)s>hier</a> oplaai."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Vir klein oplaaisels (tot 10,000 lêers) laai asseblief na beide %(first)s en %(second)s op."

#, fuzzy
msgid "page.upload.text1"
msgstr "Vir nou stel ons voor dat nuwe boeke na die Library Genesis-vurke opgelaai word. Hier is 'n <a %(a_guide)s>handige gids</a>. Let daarop dat beide vurke wat ons op hierdie webwerf indekseer, van dieselfde oplaai-stelsel gebruik maak."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Vir Libgen.li, maak seker om eers aan te meld op <a %(a_forum)s >hulle forum</a> met gebruikersnaam %(username)s en wagwoord %(password)s, en keer dan terug na hulle <a %(a_upload_page)s >oplaaibladsy</a>."

#, fuzzy
msgid "common.libgen.email"
msgstr "As u e-posadres nie op die Libgen-forums werk nie, beveel ons aan om <a %(a_mail)s>Proton Mail</a> (gratis) te gebruik. U kan ook <a %(a_manual)s>manueel versoek</a> dat u rekening geaktiveer word."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Let daarop dat mhut.org sekere IP-reekse blokkeer, so 'n VPN mag nodig wees."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Vir groot oplaaisels (meer as 10,000 lêers) wat nie deur Libgen of Z-Library aanvaar word nie, kontak ons asseblief by %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Om akademiese artikels op te laai, laai asseblief ook (benewens Library Genesis) op na <a %(a_stc_nexus)s>STC Nexus</a>. Hulle is die beste skadu-biblioteek vir nuwe artikels. Ons het hulle nog nie geïntegreer nie, maar ons sal op 'n stadium. Jy kan hul <a %(a_telegram)s>oplaaibot op Telegram</a> gebruik, of kontak die adres wat in hul vasgesteekte boodskap gelys is as jy te veel lêers het om op hierdie manier op te laai."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Hoe versoek ek boeke?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Op hierdie stadium kan ons nie boekversoeke akkommodeer nie."

#, fuzzy
msgid "page.request.forums"
msgstr "Maak asseblief jou versoeke op Z-Library of Libgen forums."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Moet asseblief nie jou boekversoeke aan ons e-pos nie."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Versamel julle metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Ons doen inderdaad."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Ek het 1984 deur George Orwell afgelaai, sal die polisie by my deur kom?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Moet nie te veel bekommer nie, daar is baie mense wat van webwerwe wat deur ons gekoppel is aflaai, en dit is uiters skaars om in die moeilikheid te kom. Om veilig te bly, beveel ons egter aan om 'n VPN (betaal) te gebruik, of <a %(a_tor)s>Tor</a> (gratis)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Hoe stoor ek my soekinstellings?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Kies die instellings wat jy verkies, hou die soekkassie leeg, klik “Soek”, en boekmerk dan die bladsy met jou blaaier se boekmerkfunksie."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Het julle 'n mobiele toepassing?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Ons het nie 'n amptelike mobiele toepassing nie, maar jy kan hierdie webwerf as 'n toepassing installeer."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik op die drie-kolletjie menu in die regter boonste hoek, en kies “Voeg by Tuisblad”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik op die “Share” knoppie onderaan, en kies “Add to Home Screen”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Het u 'n API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Ons het een stabiele JSON API vir lede, om 'n vinnige aflaai-URL te kry: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasie binne JSON self)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Vir ander gebruiksgevalle, soos om deur al ons lêers te iterer, pasgemaakte soektogte te bou, ensovoorts, beveel ons aan om ons ElasticSearch en MariaDB databasisse <a %(a_generate)s>te genereer</a> of <a %(a_download)s>af te laai</a>. Die rou data kan handmatig verken word <a %(a_explore)s>deur JSON-lêers</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Ons rou torrents lys kan ook as <a %(a_torrents)s>JSON</a> afgelaai word."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Torrents FAQ"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Ek wil graag saai, maar ek het nie veel skyfspasie nie."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Gebruik die <a %(a_list)s>torrentlys generator</a> om 'n lys van torrente te genereer wat die meeste getorrent moet word, binne jou bergingsruimte perke."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Die torrents is te stadig; kan ek die data direk van julle aflaai?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ja, sien die <a %(a_llm)s>LLM data</a> bladsy."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Kan ek slegs 'n subset van die lêers aflaai, soos slegs 'n spesifieke taal of onderwerp?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Kort antwoord: nie maklik nie."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Lang antwoord:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Meeste torrents bevat die lêers direk, wat beteken dat jy torrent-kliënte kan opdrag gee om slegs die vereiste lêers af te laai. Om te bepaal watter lêers om af te laai, kan jy ons metadata <a %(a_generate)s>genereer</a>, of ons ElasticSearch en MariaDB databasisse <a %(a_download)s>aflaai</a>. Ongelukkig bevat 'n aantal torrent-versamelings .zip of .tar lêers by die wortel, in welke geval jy die hele torrent moet aflaai voordat jy individuele lêers kan kies."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Ons het wel <a %(a_ideas)s>'n paar idees</a> vir laasgenoemde geval.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Geen maklike gereedskap om torrents te filter is nog beskikbaar nie, maar ons verwelkom bydraes."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Hoe hanteer julle duplikate in die torrents?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Ons probeer om minimale duplisering of oorvleueling tussen die torrents in hierdie lys te hou, maar dit kan nie altyd bereik word nie, en hang swaar af van die beleide van die bronbiblioteke. Vir biblioteke wat hul eie torrents uitreik, is dit buite ons beheer. Vir torrents wat deur Anna se Argief vrygestel word, dedupliseer ons slegs gebaseer op MD5-hash, wat beteken dat verskillende weergawes van dieselfde boek nie gededupliseer word nie."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Kan ek die torrentlys as JSON kry?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ja."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Ek sien nie PDFs of EPUBs in die torrents nie, net binêre lêers? Wat moet ek doen?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Hierdie is eintlik PDF's en EPUB's, hulle het net nie 'n uitbreiding in baie van ons torrente nie. Daar is twee plekke waar jy die metadata vir torrentlêers kan vind, insluitend die lêertipes/uitbreidings:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Elke versameling of vrystelling het sy eie metadata. Byvoorbeeld, <a %(a_libgen_nonfic)s>Libgen.rs torrents</a> het 'n ooreenstemmende metadata-databasis wat op die Libgen.rs-webwerf gehuisves word. Ons skakel tipies na relevante metadata-hulpbronne vanaf elke versameling se <a %(a_datasets)s>datastelbladsy</a>."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Ons beveel aan om ons ElasticSearch en MariaDB databasisse <a %(a_generate)s>te genereer</a> of <a %(a_download)s>af te laai</a>. Dit bevat 'n kartering vir elke rekord in Anna se Argief na die ooreenstemmende torrent-lêers (indien beskikbaar), onder “torrent_paths” in die ElasticSearch JSON."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Hoekom kan my torrent-kliënt nie sommige van julle torrent-lêers / magneetskakels oopmaak nie?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Sommige torrent-kliënte ondersteun nie groot stukgroottes nie, wat baie van ons torrents het (vir nuweres doen ons dit nie meer nie — al is dit geldig volgens spesifikasies!). Probeer dus 'n ander kliënt as jy hierop afkom, of kla by die vervaardigers van jou torrent-kliënt."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Het u 'n verantwoordelike openbaarmakingsprogram?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Ons verwelkom sekuriteitsnavorsers om kwesbaarhede in ons stelsels te soek. Ons is groot voorstanders van verantwoordelike openbaarmaking. Kontak ons <a %(a_contact)s>hier</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Ons is tans nie in staat om foutbelonings toe te ken nie, behalwe vir kwesbaarhede wat die <a %(a_link)s >potensiaal het om ons anonimiteit in gevaar te stel</a>, waarvoor ons belonings in die $10k-50k reeks aanbied. Ons wil graag in die toekoms 'n wyer omvang vir foutbelonings aanbied! Let asseblief daarop dat sosiale ingenieursaanvalle buite die omvang is."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "As jy belangstel in offensiewe sekuriteit, en wil help om die wêreld se kennis en kultuur te argiveer, kontak ons gerus. Daar is baie maniere waarop jy kan help."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Is daar meer hulpbronne oor Anna se Argief?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Anna se Blog</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — gereelde opdaterings"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Anna se Sagteware</a> — ons oopbron kode"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Vertaal op Anna se Argief</a> — ons vertaalsisteem"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — oor die data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — alternatiewe domeine"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — meer oor ons (help asseblief om hierdie bladsy op datum te hou, of skep een vir jou eie taal!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Hoe rapporteer ek kopieregskending?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Ons huisves geen kopiereg-beskermde materiaal hier nie. Ons is 'n soekenjin, en indekseer slegs metadata wat reeds publiek beskikbaar is. Wanneer jy van hierdie eksterne bronne aflaai, stel ons voor dat jy die wette in jou jurisdiksie nagaan met betrekking tot wat toegelaat word. Ons is nie verantwoordelik vir inhoud wat deur ander gehuisves word nie."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "As jy klagtes het oor wat jy hier sien, is jou beste opsie om die oorspronklike webwerf te kontak. Ons trek gereeld hul veranderinge in ons databasis in. As jy regtig dink jy het 'n geldige DMCA-klag wat ons moet aanspreek, vul asseblief die <a %(a_copyright)s>DMCA / Kopiereg klagvorm</a> in. Ons neem jou klagtes ernstig op en sal so gou as moontlik terugkom na jou."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Ek haat hoe julle hierdie projek bestuur!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Ons wil ook almal herinner dat al ons kode en data heeltemal oopbron is. Dit is uniek vir projekte soos ons s'n — ons is nie bewus van enige ander projek met 'n soortgelyke massiewe katalogus wat ook volledig oopbron is nie. Ons verwelkom enigeen wat dink ons bestuur ons projek swak om ons kode en data te neem en hul eie skadu-biblioteek op te stel! Ons sê dit nie uit wrok of iets nie — ons dink regtig dit sal wonderlik wees aangesien dit die standaard vir almal sal verhoog, en beter die nalatenskap van die mensdom sal bewaar."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Het u 'n uptime-monitor?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Sien asseblief <a %(a_href)s>hierdie uitstekende projek</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Hoe skenk ek boeke of ander fisiese materiaal?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Stuur dit asseblief na die <a %(a_archive)s>Internet Archive</a>. Hulle sal dit behoorlik bewaar."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Wie is Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Jy is Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Wat is jou gunsteling boeke?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Hier is 'n paar boeke wat spesiale betekenis dra vir die wêreld van skadubiblioteke en digitale bewaring:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Jy het vandag uit vinnige aflaaie gehardloop."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Word 'n lid om vinnige aflaaie te gebruik."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Ons ondersteun nou Amazon-geskenkkaarte, krediet- en debietkaarte, kripto, Alipay, en WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Volledige databasis"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Boeke, artikels, tydskrifte, strokiesprente, biblioteekrekords, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Soek"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub het <a %(a_paused)s>opgehou</a> om nuwe artikels op te laai."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB is 'n voortsetting van Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Direkte toegang tot %(count)s akademiese artikels"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Oop"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "As u 'n <a %(a_member)s>lid</a> is, is blaaierverifikasie nie nodig nie."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Langtermyn-argief"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Die datasets wat in Anna se Argief gebruik word, is heeltemal oop, en kan in grootmaat gespieël word deur torrents te gebruik. <a %(a_datasets)s>Leer meer…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Jy kan geweldig help deur torrents te saai. <a %(a_torrents)s>Leer meer…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s saaiers"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s saaiers"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM opleidingsdata"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Ons het die wêreld se grootste versameling van hoë kwaliteit teksdata. <a %(a_llm)s>Leer meer…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Spieëls: oproep vir vrywilligers"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Op soek na vrywilligers"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "As 'n nie-winsgewende, oopbronprojek, is ons altyd op soek na mense om te help."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "As jy 'n hoërisiko anonieme betalingsverwerker bestuur, kontak ons asseblief. Ons soek ook mense wat smaakvolle klein advertensies wil plaas. Alle opbrengste gaan na ons bewaringspogings."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Anna se Blog ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "IPFS aflaaie"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Alle aflaaiskakels vir hierdie lêer: <a %(a_main)s>Lêer hoofblad</a>."

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway"
msgstr "IPFS Gateway #%(num)d"

#, fuzzy
msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(jy mag dalk verskeie kere met IPFS moet probeer)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Om vinniger aflaaie te kry en die blaaierkontroles oor te slaan, <a %(a_membership)s>word 'n lid</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Vir grootmaatspieëling van ons versameling, kyk na die <a %(a_datasets)s>Datasets</a> en <a %(a_torrents)s>Torrents</a> bladsye."

#, fuzzy
msgid "page.llm.title"
msgstr "LLM-data"

#, fuzzy
msgid "page.llm.intro"
msgstr "Dit is goed verstaan dat LLM's floreer op hoë-gehalte data. Ons het die grootste versameling boeke, artikels, tydskrifte, ens. in die wêreld, wat van die hoogste gehalte teksbronne is."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Unieke skaal en reeks"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Ons versameling bevat meer as 'n honderd miljoen lêers, insluitend akademiese joernale, handboeke, en tydskrifte. Ons bereik hierdie skaal deur groot bestaande bewaarplekke te kombineer."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Sommige van ons bronversamelings is reeds in grootmaat beskikbaar (Sci-Hub, en dele van Libgen). Ander bronne het ons self bevry. <a %(a_datasets)s>Datasets</a> toon 'n volledige oorsig."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Ons versameling sluit miljoene boeke, artikels, en tydskrifte van voor die e-boek era in. Groot dele van hierdie versameling is reeds OCR’ed, en het reeds min interne oorvleueling."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Hoe ons kan help"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Ons kan hoëspoed toegang tot ons volledige versamelings verskaf, sowel as tot onuitgereikte versamelings."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Dit is ondernemingsvlak toegang wat ons kan verskaf vir donasies in die reeks van tienduisende USD. Ons is ook bereid om dit te ruil vir hoë-gehalte versamelings wat ons nog nie het nie."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Ons kan jou terugbetaal as jy ons kan voorsien van verryking van ons data, soos:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Verwydering van oorvleueling (deduplikasie)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Teks- en metadata-ekstraksie"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Ondersteun langtermyn-argivering van menslike kennis, terwyl jy beter data vir jou model kry!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Kontak ons</a> om te bespreek hoe ons kan saamwerk."

#, fuzzy
msgid "page.login.continue"
msgstr "Gaan voort"

#, fuzzy
msgid "page.login.please"
msgstr "Asseblief <a %(a_account)s>meld aan</a> om hierdie bladsy te sien.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Anna se Argief is tydelik af vir instandhouding. Kom asseblief oor 'n uur terug."

#, fuzzy
msgid "page.metadata.header"
msgstr "Verbeter metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "U kan help met die bewaring van boeke deur metadata te verbeter! Lees eers die agtergrond oor metadata op Anna se Argief, en leer dan hoe om metadata te verbeter deur skakeling met Open Library, en verdien gratis lidmaatskap op Anna se Argief."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Agtergrond"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Wanneer u na 'n boek op Anna se Argief kyk, kan u verskeie velde sien: titel, outeur, uitgewer, uitgawe, jaar, beskrywing, lêernaam, en meer. Al daardie inligting word <em>metadata</em> genoem."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Aangesien ons boeke van verskeie <em>bronbiblioteke</em> kombineer, wys ons watter metadata ook al beskikbaar is in daardie bronbiblioteek. Byvoorbeeld, vir 'n boek wat ons van Library Genesis gekry het, sal ons die titel van Library Genesis se databasis wys."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Soms is 'n boek teenwoordig in <em>meervoudige</em> bronbiblioteke, wat dalk verskillende metadata-velde het. In daardie geval wys ons eenvoudig die langste weergawe van elke veld, aangesien daardie een hopelik die nuttigste inligting bevat! Ons sal steeds die ander velde onder die beskrywing wys, bv. as \"alternatiewe titel\" (maar slegs as hulle verskillend is)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Ons onttrek ook <em>kodes</em> soos identifiseerders en klassifiseerders van die bronbiblioteek. <em>Identifiseerders</em> verteenwoordig uniek 'n spesifieke uitgawe van 'n boek; voorbeelde is ISBN, DOI, Open Library ID, Google Books ID, of Amazon ID. <em>Klassifiseerders</em> groepeer verskeie soortgelyke boeke; voorbeelde is Dewey Desimaal (DCC), UDC, LCC, RVK, of GOST. Soms is hierdie kodes eksplisiet gekoppel in bronbiblioteke, en soms kan ons hulle onttrek van die lêernaam of beskrywing (hoofsaaklik ISBN en DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Ons kan identifiseerders gebruik om rekords in <em>metadata-slegs versamelings</em> te vind, soos OpenLibrary, ISBNdb, of WorldCat/OCLC. Daar is 'n spesifieke <em>metadata-oortjie</em> in ons soekenjin as u daardie versamelings wil deursoek. Ons gebruik ooreenstemmende rekords om ontbrekende metadata-velde in te vul (bv. as 'n titel ontbreek), of bv. as \"alternatiewe titel\" (as daar 'n bestaande titel is)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Om presies te sien waar die metadata van 'n boek vandaan kom, sien die <em>“Tegniese besonderhede” oortjie</em> op 'n boekbladsy. Dit het 'n skakel na die rou JSON vir daardie boek, met verwysings na die rou JSON van die oorspronklike rekords."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Vir meer inligting, sien die volgende bladsye: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Soek (metadata-oortjie)</a>, <a %(a_codes)s>Kode Verkenner</a>, en <a %(a_example)s>Voorbeeld metadata JSON</a>. Laastens, al ons metadata kan as <a %(a_generated)s>ElasticSearch</a> en <a %(a_downloaded)s>MariaDB</a> databasisse gegenereer of afgelaai word."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Open Library skakeling"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "So as u 'n lêer met slegte metadata teëkom, hoe moet u dit regmaak? U kan na die bronbiblioteek gaan en sy prosedures volg om metadata reg te maak, maar wat om te doen as 'n lêer in meervoudige bronbiblioteke teenwoordig is?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Daar is een identifiseerder wat spesiaal behandel word op Anna se Argief. <strong>Die annas_archive md5 veld op Open Library oorskry altyd alle ander metadata!</strong> Kom ons gaan eers 'n bietjie terug en leer oor Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library is in 2006 gestig deur Aaron Swartz met die doel van “een webbladsy vir elke boek wat ooit gepubliseer is”. Dit is soortgelyk aan 'n Wikipedia vir boekmetadata: almal kan dit redigeer, dit is vrylik gelisensieer, en kan in grootmaat afgelaai word. Dit is 'n boekdatabasis wat die meeste in lyn is met ons missie — in werklikheid is Anna se Argief geïnspireer deur Aaron Swartz se visie en lewe."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "In plaas daarvan om die wiel te herontdek, het ons besluit om ons vrywilligers na Open Library te herlei. As u 'n boek sien wat verkeerde metadata het, kan u op die volgende manier help:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Gaan na die <a %(a_openlib)s>Open Library webwerf</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Vind die korrekte boekrekord. <strong>WAARSKUWING:</strong> wees seker om die korrekte <strong>uitgawe</strong> te kies. In Open Library het u “werke” en “uitgawes”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "'n “Werk” kan wees “Harry Potter en die Steen der Wyse”."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "'n “Uitgawe” kan wees:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Die 1997 eerste uitgawe gepubliseer deur Bloomsbery met 256 bladsye."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Die 2003 sagteband uitgawe gepubliseer deur Raincoast Books met 223 bladsye."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Die 2000 Poolse vertaling “Harry Potter I Kamie Filozoficzn” deur Media Rodzina met 328 bladsye."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Al hierdie uitgawes het verskillende ISBN's en verskillende inhoud, so maak seker jy kies die regte een!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Wysig die rekord (of skep dit as daar nie een bestaan nie), en voeg soveel nuttige inligting as moontlik by! Jy is nou hier, maak die rekord regtig wonderlik."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Onder “ID-nommers” kies “Anna se Argief” en voeg die MD5 van die boek van Anna se Argief by. Dit is die lang string letters en syfers na “/md5/” in die URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Probeer om ander lêers in Anna se Argief te vind wat ook by hierdie rekord pas, en voeg dit ook by. In die toekoms kan ons dit as duplikate groepeer op Anna se Argief se soekbladsy."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Wanneer jy klaar is, skryf die URL neer wat jy pas opgedateer het. Sodra jy ten minste 30 rekords met Anna se Argief MD5's opgedateer het, stuur vir ons 'n <a %(a_contact)s>e-pos</a> en stuur vir ons die lys. Ons sal jou 'n gratis lidmaatskap vir Anna se Argief gee, sodat jy hierdie werk makliker kan doen (en as 'n dankie vir jou hulp). Dit moet hoë kwaliteit wysigings wees wat aansienlike hoeveelhede inligting byvoeg, anders sal jou versoek afgekeur word. Jou versoek sal ook afgekeur word as enige van die wysigings deur Open Library-moderators teruggerol of reggestel word."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Let daarop dat dit net vir boeke werk, nie akademiese artikels of ander soorte lêers nie. Vir ander soorte lêers beveel ons steeds aan om die bronbiblioteek te vind. Dit kan 'n paar weke neem vir veranderinge om in Anna se Argief ingesluit te word, aangesien ons die nuutste Open Library data dump moet aflaai en ons soekindeks moet hergenereer."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Spieëls: oproep vir vrywilligers"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Om die veerkragtigheid van Anna se Argief te verhoog, soek ons vrywilligers om spieëls te bestuur."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Ons soek na hierdie:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Jy bestuur die Anna se Argief oopbron-kodebasis, en jy werk gereeld beide die kode en die data op."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Jou weergawe is duidelik onderskei as 'n spieël, bv. “Bob se Argief, 'n Anna se Argief spieël”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Jy is bereid om die risiko's wat met hierdie werk geassosieer word, wat beduidend is, te neem. Jy het 'n diep begrip van die operasionele sekuriteit wat vereis word. Die inhoud van <a %(a_shadow)s>hierdie</a> <a %(a_pirate)s>plasings</a> is vir jou vanselfsprekend."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Jy is bereid om by te dra tot ons <a %(a_codebase)s>kodebasis</a> — in samewerking met ons span — om dit te laat gebeur."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Aanvanklik sal ons jou nie toegang gee tot ons vennootbediener-aflaaie nie, maar as dinge goed gaan, kan ons dit met jou deel."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Gasheeruitgawes"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Ons is bereid om gasheer- en VPN-uitgawes te dek, aanvanklik tot $200 per maand. Dit is voldoende vir 'n basiese soekbediener en 'n DMCA-beskermde proxy."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Ons sal slegs betaal vir gasheer sodra jy alles opgestel het, en bewys het dat jy in staat is om die argief op datum te hou met opdaterings. Dit beteken jy sal vir die eerste 1-2 maande uit jou eie sak moet betaal."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Jou tyd sal nie vergoed word nie (en ook nie ons s'n nie), aangesien dit suiwer vrywillige werk is."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "As jy beduidend betrokke raak by die ontwikkeling en bedrywighede van ons werk, kan ons bespreek om meer van die donasie-inkomste met jou te deel, sodat jy dit kan aanwend soos nodig."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Begin"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Moet asseblief <strong>nie ons kontak</strong> om toestemming te vra, of vir basiese vrae nie. Dade spreek harder as woorde! Al die inligting is daar buite, so gaan net voort met die opstel van jou spieël."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Voel vry om kaartjies of samevoegingsversoeke op ons Gitlab te plaas wanneer jy probleme ondervind. Ons mag dalk spieël-spesifieke kenmerke saam met jou moet bou, soos hermerk van “Anna se Argief” na jou webwerf se naam, (aanvanklik) deaktiveer van gebruikersrekeninge, of skakeling terug na ons hoofwebwerf vanaf boekbladsye."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Sodra jy jou spieël aan die gang het, kontak ons asseblief. Ons sal graag jou OpSec wil hersien, en sodra dit solied is, sal ons na jou spieël skakel, en nader saam met jou begin werk."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Dankie by voorbaat aan enigeen wat bereid is om op hierdie manier by te dra! Dit is nie vir die flou van hart nie, maar dit sal die langlewendheid van die grootste werklik oop biblioteek in die menslike geskiedenis versterk."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Laai af vanaf vennootwebwerf"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Stadige aflaaie is slegs beskikbaar deur die amptelike webwerf. Besoek %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Stadige aflaaie is nie beskikbaar deur Cloudflare VPN's of andersins vanaf Cloudflare IP-adresse nie."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Wag asseblief <span %(span_countdown)s>%(wait_seconds)s</span> sekondes om hierdie lêer af te laai."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Gebruik die volgende URL om af te laai: <a %(a_download)s>Laai nou af</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Dankie dat jy gewag het, dit hou die webwerf gratis toeganklik vir almal! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Waarskuwing: daar was baie aflaaie vanaf jou IP-adres in die laaste 24 uur. Aflaaie mag stadiger wees as gewoonlik."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Aflae vanaf jou IP-adres in die laaste 24 uur: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "As jy 'n VPN, gedeelde internetverbinding, of jou ISP deel IP's gebruik, kan hierdie waarskuwing daaraan te wyte wees."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Om vir almal 'n geleentheid te gee om lêers gratis af te laai, moet jy wag voordat jy hierdie lêer kan aflaai."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Voel vry om voort te gaan met blaai in Anna se Argief in 'n ander oortjie terwyl jy wag (as jou blaaier agtergrond-oortjies kan verfris)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Voel vry om te wag vir verskeie aflaaibladsye om gelyktydig te laai (maar laai asseblief slegs een lêer op 'n slag per bediener af)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Sodra u 'n aflaaiskakel kry, is dit vir 'n paar uur geldig."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Anna se Argief"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rekord in Anna se Argief"

#, fuzzy
msgid "page.scidb.download"
msgstr "Aflaai"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Om die toeganklikheid en langtermynbewaring van menslike kennis te ondersteun, word 'n <a %(a_donate)s>lid</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "As 'n bonus, 🧬&nbsp;SciDB laai vinniger vir lede, sonder enige beperkings."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Werk nie? Probeer <a %(a_refresh)s>verfris</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Geen voorskou beskikbaar nie. Laai lêer af van <a %(a_path)s>Anna se Argief</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB is 'n voortsetting van Sci-Hub, met sy bekende koppelvlak en direkte besigtiging van PDF's. Voer u DOI in om te besigtig."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Ons het die volledige Sci-Hub-versameling, sowel as nuwe artikels. Die meeste kan direk besigtig word met 'n bekende koppelvlak, soortgelyk aan Sci-Hub. Sommige kan afgelaai word deur eksterne bronne, in welke geval ons skakels na daardie bronne wys."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Soek"

#, fuzzy
msgid "page.search.title.new"
msgstr "Nuwe soektog"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Sluit slegs in"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Sluit uit"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Ongekontroleer"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Aflaai"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Joernaalartikels"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Digitale Uitleen"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Titel, outeur, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Soek"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Soekinstellings"

#, fuzzy
msgid "page.search.submit"
msgstr "Soek"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Die soektog het te lank geneem, wat algemeen is vir breë navrae. Die filtertellings mag dalk nie akkuraat wees nie."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Die soektog het te lank geneem, wat beteken dat jy moontlik onakkurate resultate sal sien. Soms help dit om die bladsy <a %(a_reload)s>weer te laai</a>."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Vertoon"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Lys"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Tabel"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Gevorderd"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Soek beskrywings en metadata kommentare"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Voeg spesifieke soekveld by"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(soek spesifieke veld)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Jaar gepubliseer"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Inhoud"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Lêertipe"

#, fuzzy
msgid "page.search.more"
msgstr "meer…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Toegang"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Bron"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "gekrap en oopbron gemaak deur AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Taal"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Rangskik volgens"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Mees relevant"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Nuutste"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(publikasiejaar)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Oudste"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Grootste"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(lêergrootte)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Kleinste"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(oopbron)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Lukraak"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Die soekindeks word maandeliks opgedateer. Dit sluit tans inskrywings in tot %(last_data_refresh_date)s. Vir meer tegniese inligting, sien die %(link_open_tag)sdatastelle-bladsy</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Om die soekindeks volgens kodes te verken, gebruik die <a %(a_href)s>Kode Verkenner</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Tik in die blokkie om ons katalogus van %(count)s direk aflaaibare lêers te soek, wat ons <a %(a_preserve)s>vir ewig bewaar</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "In werklikheid kan enigiemand help om hierdie lêers te bewaar deur ons <a %(a_torrents)s>verenigde lys van torrents</a> te saai."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Ons het tans die wêreld se mees omvattende oop katalogus van boeke, artikels en ander geskrewe werke. Ons spieël Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>en meer</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "As jy ander “skadu-biblioteke” vind wat ons moet spieël, of as jy enige vrae het, kontak ons asseblief by %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Vir DMCA / kopiereg eise <a %(a_copyright)s>klik hier</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Wenk: gebruik sleutelbordkortpaaie “/” (soekfokus), “enter” (soek), “j” (op), “k” (af), “<” (vorige bladsy), “>” (volgende bladsy) vir vinniger navigasie."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Op soek na artikels?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Tik in die boks om ons katalogus van %(count)s akademiese referate en joernaalartikels te soek, wat ons <a %(a_preserve)s>vir altyd bewaar</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Tik in die boks om na lêers in digitale uitleenbiblioteke te soek."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Hierdie soekindeks sluit tans metadata in van die Internet Archive se Beheerde Digitale Uitleenbiblioteek. <a %(a_datasets)s>Meer oor ons datasets</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Vir meer digitale uitleenbiblioteke, sien <a %(a_wikipedia)s>Wikipedia</a> en die <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Tik in die boks om na metadata van biblioteke te soek. Dit kan nuttig wees wanneer jy <a %(a_request)s>'n lêer versoek</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Hierdie soekindeks sluit tans metadata van verskeie metadata bronne in. <a %(a_datasets)s>Meer oor ons datasets</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Vir metadata wys ons die oorspronklike rekords. Ons doen geen samevoeging van rekords nie."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Daar is baie, baie bronne van metadata vir geskrewe werke regoor die wêreld. <a %(a_wikipedia)s>Hierdie Wikipedia-bladsy</a> is 'n goeie begin, maar as jy van ander goeie lyste weet, laat weet ons asseblief."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Tik in die blokkie om te soek."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Hierdie is metadata-rekords, <span %(classname)s>nie</span> aflaaibare lêers nie."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Fout tydens soektog."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Probeer <a %(a_reload)s>herlaai die bladsy</a>. As die probleem voortduur, stuur asseblief 'n e-pos aan ons by %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Geen lêers gevind nie.</span> Probeer minder of verskillende soekterme en filters."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Soms gebeur dit verkeerd wanneer die soekbediener stadig is. In sulke gevalle kan <a %(a_attrs)s>herlaai</a> help."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Ons het ooreenkomste gevind in: %(in)s. Jy kan verwys na die URL wat daar gevind is wanneer jy <a %(a_request)s>'n lêer versoek</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Joernaalartikels (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Digitale Uitleen (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Resultate %(from)s-%(to)s (%(total)s totaal)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ gedeeltelike ooreenkomste"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d gedeeltelike ooreenkomste"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Vrywilligerswerk & Belonings"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna se Argief maak staat op vrywilligers soos jy. Ons verwelkom alle vlakke van toewyding, en het twee hoofkategorieë van hulp wat ons soek:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Ligte vrywilligerswerk:</span> as jy net 'n paar uur hier en daar kan spaar, is daar steeds baie maniere waarop jy kan help. Ons beloon konsekwente vrywilligers met <span %(bold)s>🤝 lidmaatskappe by Anna se Argief</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Swaar vrywilligerswerk (USD$50-USD$5,000 belonings):</span> as u baie tyd en/of hulpbronne aan ons missie kan wy, sal ons graag nouer met u wil saamwerk. Uiteindelik kan u by die binneste span aansluit. Alhoewel ons 'n beperkte begroting het, kan ons <span %(bold)s>💰 geldelike belonings</span> toeken vir die mees intense werk."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "As u nie u tyd kan vrywillig nie, kan u ons steeds baie help deur <a %(a_donate)s>geld te skenk</a>, <a %(a_torrents)s>ons torrents te saai</a>, <a %(a_uploading)s>boeke op te laai</a>, of <a %(a_help)s>u vriende van Anna se Argief te vertel</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Maatskappye:</span> ons bied hoëspoed direkte toegang tot ons versamelings in ruil vir ondernemingsvlak skenkings of uitruil vir nuwe versamelings (bv. nuwe skanderings, OCR’ed datasets, verryking van ons data). <a %(a_contact)s>Kontak ons</a> as dit u is. Sien ook ons <a %(a_llm)s>LLM bladsy</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Ligte vrywilligerswerk"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "As u 'n paar uur oor het, kan u op verskeie maniere help. Sluit gerus aan by die <a %(a_telegram)s>vrywilligersgeselskap op Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "As 'n teken van waardering gee ons gewoonlik 6 maande van “Gelukkige Bibliotekaris” vir basiese mylpale, en meer vir voortgesette vrywilligerswerk. Alle mylpale vereis hoë kwaliteit werk — slordige werk benadeel ons meer as wat dit help en ons sal dit verwerp. Stuur asseblief 'n <a %(a_contact)s>e-pos aan ons</a> wanneer u 'n mylpaal bereik."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Taak"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Mylpaal"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Verspreiding van die woord oor Anna se Argief. Byvoorbeeld, deur boeke op AA aan te beveel, na ons blogplasings te skakel, of mense oor die algemeen na ons webwerf te verwys."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s skakels of skermkiekies."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Hierdie moet wys dat jy iemand van Anna se Argief vertel, en hulle jou bedank."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Verbeter metadata deur <a %(a_metadata)s>te koppel</a> met Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Jy kan die <a %(a_list)s >lys van lukrake metadata-kwessies</a> as 'n beginpunt gebruik."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Maak seker dat jy 'n opmerking los oor kwessies wat jy oplos, sodat ander nie jou werk dupliseer nie."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s skakels van rekords wat u verbeter het."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Vertaal</a> die webwerf."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Vertaal 'n taal volledig (as dit nie reeds amper voltooi was nie)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Verbeter die Wikipedia-bladsy vir Anna se Argief in u taal. Sluit inligting in van AA se Wikipedia-bladsy in ander tale, en van ons webwerf en blog. Voeg verwysings na AA by op ander relevante bladsye."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Skakel na wysigingsgeskiedenis wat wys dat u beduidende bydraes gemaak het."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Vervul boek (of artikel, ens.) versoeke op die Z-Library of die Library Genesis forums. Ons het nie ons eie boekversoekstelsel nie, maar ons spieël daardie biblioteke, so om hulle beter te maak maak Anna se Argief ook beter."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s skakels of skermkiekies van versoeke wat u vervul het."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Klein take gepos op ons <a %(a_telegram)s>vrywilligersgeselskap op Telegram</a>. Gewoonlik vir lidmaatskap, soms vir klein belonings."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Klein take gepos in ons vrywilliger-klets groep."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Hang af van die taak."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Beloonings"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Ons is altyd op soek na mense met soliede programmerings- of offensiewe sekuriteitsvaardighede om betrokke te raak. Jy kan 'n ernstige bydrae lewer tot die bewaring van die mensdom se nalatenskap."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "As 'n dankie, gee ons lidmaatskap weg vir soliede bydraes. As 'n groot dankie, gee ons geldelike belonings vir besonder belangrike en moeilike take. Dit moet nie as 'n plaasvervanger vir 'n werk gesien word nie, maar dit is 'n ekstra aansporing en kan help met aangegane koste."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Die meeste van ons kode is oopbron, en ons sal vra dat jou kode ook oopbron is wanneer ons die beloning toeken. Daar is 'n paar uitsonderings wat ons op 'n individuele basis kan bespreek."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Belonings word toegeken aan die eerste persoon wat 'n taak voltooi. Voel vry om kommentaar te lewer op 'n beloningstiket om ander te laat weet dat jy aan iets werk, sodat ander kan wag of jou kan kontak om saam te werk. Maar wees bewus daarvan dat ander steeds vry is om daaraan te werk en jou te probeer klop. Ons ken egter nie belonings toe vir slordige werk nie. As twee hoë kwaliteit inskrywings naby mekaar gemaak word (binne 'n dag of twee), kan ons kies om belonings aan beide toe te ken, na ons diskresie, byvoorbeeld 100%% vir die eerste inskrywing en 50%% vir die tweede inskrywing (dus 150%% totaal)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Vir die groter belonings (veral skraapbelonings), kontak ons asseblief wanneer jy ~5%% daarvan voltooi het, en jy is vol vertroue dat jou metode sal skaal tot die volle mylpaal. Jy sal jou metode met ons moet deel sodat ons terugvoer kan gee. Ook, op hierdie manier kan ons besluit wat om te doen as daar verskeie mense is wat naby 'n beloning kom, soos om dit moontlik aan verskeie mense toe te ken, mense aan te moedig om saam te werk, ens."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "WAARSKUWING: die hoë-beloning take is <span %(bold)s>moeilik</span> — dit mag wys wees om met makliker take te begin."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Gaan na ons <a %(a_gitlab)s>Gitlab kwessies lys</a> en sorteer volgens “Etiket prioriteit”. Dit wys ongeveer die volgorde van take wat vir ons belangrik is. Take sonder eksplisiete belonings is steeds in aanmerking vir lidmaatskap, veral dié gemerk “Aanvaar” en “Anna se gunsteling”. Jy mag dalk wil begin met 'n “Beginner projek”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Opdaterings oor <a %(wikipedia_annas_archive)s>Anna se Argief</a>, die grootste werklik oop biblioteek in die menslike geskiedenis."

#, fuzzy
msgid "layout.index.title"
msgstr "Anna se Argief"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Die wêreld se grootste oopbron oopdata-biblioteek. Spieëls Sci-Hub, Library Genesis, Z-Library, en meer."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Soek Anna se Argief"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Anna se Argief het jou hulp nodig!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Baie probeer ons onderkry, maar ons veg terug."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "As u nou skenk, kry u <strong>dubbel</strong> die aantal vinnige aflaaie."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Geldig tot die einde van hierdie maand."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Skenk"

#, fuzzy
msgid "layout.index.header.banner.holiday_gift"
msgstr "Om menslike kennis te bewaar: 'n wonderlike vakansiegeskenk!"

#, fuzzy
msgid "layout.index.header.banner.surprise"
msgstr "Verras 'n geliefde, gee hulle 'n rekening met lidmaatskap."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Om die veerkragtigheid van Anna se Argief te verhoog, soek ons vrywilligers om spieëls te bestuur."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Die perfekte Valentynsgeskenk!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Ons het 'n nuwe donasiemetode beskikbaar: %(method_name)s. Oorweeg asseblief om %(donate_link_open_tag)ste skenk</a> — dit is nie goedkoop om hierdie webwerf te bestuur nie, en jou donasie maak regtig 'n verskil. Baie dankie."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Ons hou 'n fondsinsameling vir <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">om die grootste strokiesprent-skaduweebiblioteek in die wêreld te rugsteun</a>. Dankie vir jou ondersteuning! <a href=\"/donate\">Skenk.</a> As jy nie kan skenk nie, oorweeg om ons te ondersteun deur jou vriende te vertel, en ons te volg op <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, of <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Onlangse aflaaie:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Soek"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Gereelde Vrae"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Verbeter metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Vrywilligerswerk & Belonings"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrente"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktiwiteit"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Kode Verkenner"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "LLM data"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Tuis"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Anna se Argief ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Vertaal ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Meld aan / Registreer"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Rekening"

#, fuzzy
msgid "layout.index.footer.list1.header"
msgstr "Anna se Argief"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Bly in kontak"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "DMCA / kopiereg-eise"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Gevorderd"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Sekuriteit"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatiewe"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "nie geaffilieer nie"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Hierdie lêer mag probleme hê."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Vinnige aflaai"

#, fuzzy
msgid "page.donate.copy"
msgstr "kopieer"

#, fuzzy
msgid "page.donate.copied"
msgstr "gekopieer!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Vorige"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Volgende"

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "slegs hierdie maand!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub het <a %(a_closed)s>opgehou</a> om nuwe artikels op te laai."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Kies 'n betaalopsie. Ons gee afslag vir kripto-gebaseerde betalings %(bitcoin_icon)s, omdat ons (baie) minder fooie het."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Kies 'n betaalopsie. Ons het tans slegs kripto-gebaseerde betalings %(bitcoin_icon)s, aangesien tradisionele betaalverwerkers weier om met ons saam te werk."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Ons kan nie krediet-/debietkaarte direk ondersteun nie, omdat banke nie met ons wil werk nie. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Daar is egter verskeie maniere om krediet-/debietkaarte te gebruik, deur ons ander betaalmetodes te gebruik:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Stadige & eksterne aflaaie"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Aflaaie"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "As jy vir die eerste keer kripto gebruik, stel ons voor dat jy %(option1)s, %(option2)s, of %(option3)s gebruik om Bitcoin (die oorspronklike en mees gebruikte kripto-geldeenheid) te koop en te skenk."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 skakels van rekords wat u verbeter het."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 skakels of skermskote."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 skakels of skermskote van versoeke wat u vervul het."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "As u belangstel om hierdie datasets te spieël vir <a %(a_faq)s>argivering</a> of <a %(a_llm)s>LLM opleiding</a> doeleindes, kontak ons asseblief."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "As u belangstel om hierdie datastel te spieël vir <a %(a_archival)s>argivering</a> of <a %(a_llm)s>LLM-opleiding</a> doeleindes, kontak ons asseblief."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Hoofwebwerf"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "ISBN landinligting"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "As u belangstel om hierdie datastel te spieël vir <a %(a_archival)s>argivering</a> of <a %(a_llm)s>LLM opleiding</a> doeleindes, kontak ons asseblief."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Die Internasionale ISBN Agentskap stel gereeld die reekse vry wat dit aan nasionale ISBN agentskappe toegeken het. Hieruit kan ons aflei aan watter land, streek, of taalgroep hierdie ISBN behoort. Ons gebruik tans hierdie data indirek, deur die <a %(a_isbnlib)s>isbnlib</a> Python-biblioteek."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Hulpbronne"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Laas opgedateer: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "ISBN webwerf"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Uitsluitende “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Ons inspirasie vir die versameling van metadata is Aaron Swartz se doel van “een webbladsy vir elke boek wat ooit gepubliseer is”, waarvoor hy <a %(a_openlib)s>Open Library</a> geskep het."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Daardie projek het goed gevaar, maar ons unieke posisie stel ons in staat om metadata te kry wat hulle nie kan nie."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Nog 'n inspirasie was ons begeerte om te weet <a %(a_blog)s>hoeveel boeke daar in die wêreld is</a>, sodat ons kan bereken hoeveel boeke ons nog moet red."

#~ msgid "page.partner_download.text1"
#~ msgstr "Om vir almal 'n geleentheid te gee om lêers gratis af te laai, moet jy <strong>%(wait_seconds)s sekondes</strong> wag voordat jy hierdie lêer kan aflaai."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Ververs bladsy outomaties. As jy die aflaaivenster mis, sal die timer herbegin, so outomatiese verversing word aanbeveel."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Laai nou af"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Omskakel: gebruik aanlyn gereedskap om tussen formate om te skakel. Byvoorbeeld, om tussen epub en pdf om te skakel, gebruik <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: laai die lêer af (pdf of epub word ondersteun), dan <a %(a_kindle)s>stuur dit na Kindle</a> deur web, app, of e-pos. Nuttige gereedskap: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Ondersteun outeurs: As u hiervan hou en dit kan bekostig, oorweeg dit om die oorspronklike te koop, of die outeurs direk te ondersteun."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Ondersteun biblioteke: As dit by jou plaaslike biblioteek beskikbaar is, oorweeg om dit gratis daar te leen."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Nie direk in grootmaat beskikbaar nie, slegs in semi-grootmaat agter 'n betaalmuur"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Anna se Argief bestuur 'n versameling van <a %(isbndb)s>ISBNdb metadata</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb is 'n maatskappy wat verskeie aanlyn boekwinkels skraap om ISBN-metadata te vind. Anna se Argief maak rugsteunkopieë van die ISBNdb-boekmetadata. Hierdie metadata is beskikbaar deur Anna se Argief (alhoewel tans nie in soektogte nie, behalwe as jy spesifiek na 'n ISBN-nommer soek)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Vir tegniese besonderhede, sien hieronder. Op 'n stadium kan ons dit gebruik om te bepaal watter boeke nog ontbreek in skadubiblioteke, om te prioritiseer watter boeke gevind en/of gescan moet word."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Ons blogpos oor hierdie data"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "ISBNdb skraap"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Tans het ons 'n enkele torrent, wat 'n 4.4GB gegzipte <a %(a_jsonl)s>JSON Lines</a> lêer (20GB uitgepak) bevat: “isbndb_2022_09.jsonl.gz”. Om 'n “.jsonl” lêer in PostgreSQL in te voer, kan jy iets soos <a %(a_script)s>hierdie skrip</a> gebruik. Jy kan dit selfs direk pyp met iets soos %(example_code)s sodat dit op die vlug gedecomprimeer word."

#~ msgid "page.donate.wait"
#~ msgstr "Wag asseblief ten minste <span %(span_hours)s>twee uur</span> (en verfris hierdie bladsy) voordat u ons kontak."

#~ msgid "page.codes.search_archive"
#~ msgstr "Soek Anna se Argief vir “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Skenk met behulp van Alipay of WeChat. Jy kan tussen hierdie twee kies op die volgende bladsy."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Versprei die woord van Anna se Argief op sosiale media en aanlyn forums, deur boeke of lyste op AA aan te beveel, of vrae te beantwoord."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Fiksieversameling het afgewyk maar het steeds <a %(libgenli)s>torrents</a>, alhoewel nie sedert 2022 opgedateer nie (ons het wel direkte aflaaie)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Anna se Argief en Libgen.li bestuur gesamentlik versamelings van <a %(comics)s>strokiesprente</a> en <a %(magazines)s>tydskrifte</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Geen torrents vir Russiese fiksie en standaard dokumente versamelings nie."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Daar is geen torrents beskikbaar vir die bykomende inhoud nie. Die torrents wat op die Libgen.li webwerf is, is spieëls van ander torrents wat hier gelys is. Die een uitsondering is fiksie torrents wat begin by %(fiction_starting_point)s. Die strokiesprente en tydskrifte torrents word vrygestel as 'n samewerking tussen Anna se Argief en Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Van 'n versameling <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> presiese oorsprong onduidelik. Gedeeltelik van the-eye.eu, gedeeltelik van ander bronne."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

