��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  ld �  +h -   �i �  )j 1  �k 5  o +  9r �  et �   
v �   �v H   w p   `w q  �w 6  Cz �  z| �  �} a   �  $�   �� �  � ;  Ɋ �  � x  � O   [� L  �� y   ��   r� ,   {�   �� H   �� 5   � #   >� 2   b� �   �� 4   �    Q� B   o� ;   �� @   �    /� h   A� 
  �� �  �� �   �� P   *�    {�    ��     ��    �� 0   С    �    �    +�    E� #   L�    p� &   y� 	   �� 
   ��    �� (   Ң .   �� )   *� �  T� �  � �   � -   � V  � �   p� �  m� G  � ,   b� �   �� ,   N�   {� 1   �� �  �� ,   �� �  �� >   8� �   w�    � !  &� K  H� �  �� �  � !   � f   0� �   �� )  $� ?   N� P   �� �   �� e   �� p   �� Q   n�    �� �   �� �  �� {   3� �   ��   j� z   �� �  � I   �� �  �� �   r� �   f� �   c�   �� �   � H   �� h  &� A  �� �   �� _   {� �   ��    �   �� �  �� H   ��    �� )  �� h  �    t�   }� �   ��   {� T  ��   �� N  �� f  @� b   �� �   
� y   �� �   � ~   �� �   5� M   �� �   � �   �� t   w�    �� �   � x   �� (  "� �   K�    � �  3� �  �� �  �� �  
� �   �� )  W� �  ��   P  �   W   P �  V   ' o  E    � �   �   � �   �	 �  �
 �   j �   &
 �   �
 �  � �   y �  ; �    ?   � �   � �   q �   ` i  �   c �   } �    L   � Z  / }  �  C  "    L$ �   j$ �   % J  �% �  C' ;  �( j  * f  {, 5   �. '  / �  @1 �  5 "  �6 1  ; �  F= �   ? �   �? 
   �@ E  �@ �  �A �  �C �  �F �  nH "  :J    ]K a  jK   �M |  �O    cQ   pQ   �R :  �T _  �U X  9Y �   �Z    [ b  1[   �] !   �_ n  �_ �  ;a �  c ^  �f /  �g �   j �   �j �  Sk <   �l "   m %  6m   \o   _q P   as H   �s S  �s �   Ow �   x     y f  �y 5   �{    | \  8| I  �~ �   � q   �� �   � A   �� �  � �  ��    �    5� 7   I� P   �� m   ҅ V   @� �   �� �   :� '   � K   � m   b� �   Ј 6   R� P   �� L   ډ �   '�    �� 6   � �   �    �� h  ӎ �  <� �   ϑ k  ��   #� "   7� j  Z� 1  ŗ   �� F  � [  V� a  �� 	  � �  � H   ȧ ]   � -   o� ,  �� �  ʩ }  �� �  8� *  � �  1� �  ƴ �  �� \   M� �  ��   �� W  ��    � T  '� B  |� �   �� *  �� 3  ��    � :   � �   N�    5� �  B� \  '� �  �� &  m� 9  ��    �� x  �� �  ]� H   *� t   s� �  �� �  �� �   � X  �� �   V� �  ��    �� �  �� �   S� ?  M�   �� �  �� %  s�    �� �  �� �  U�    8�   W� �  o� �  T� I  �  �  1 @  � H   � ~  < m  �
 �   ) n   � �   M
    �
 @   �
 %   4 /   Z 1   �    � 8  � �   �  � �  � K  f 
   � l  � �  *! |  �$ �  H'   �) �  �-    �1 �  �1    �3     �3 �  �3 �  �6 w  �: 	  �=    B 4  B 2  HE �  {H �  L �  �O   �T H  �W (   Z R  4Z �   �[ ^  F\ �  �` +  Vc   �d #  �e x  �f !   'i 3  Ii q   }j �   �j    �k e   �k \   l �  cl �  p �  �r T   xw �  �w   �| 
   �} +  �} Z   �~ �   B _  � D   n� �   �� '   @� ?  h� �  �� �  �� U  5� �   ��    V� �   n� �  /�    ِ �   � O  ܑ E  ,� {   r� X   � 3  G� -   {� �  �� �  Z� �  T� =   S� �   �� �   ;� �  3� �  ��    �� j  �� K   � ,  k�    �� �  �� w  X� �   г -   [� A  �� N   ˵ &  � �  A� X  � U  j� �   �� S   H�   �� �  �� f  V� %  �� ;  �� R   � B  r� �  �� #  j� K  �� �  �� �  �� -   T� �  �� ?   n� �  �� �   q� "    � �  C� B  �� �  ;� �  �� �  x�   `� �  d� �   d�   \� ~  v� �   �� �  ��   � U   � �    �   � )   �    � �  �   � s   �	 �  
 �  � �  � J  $ ?  o .  � �  � �  z (   ?   h    w f  � *   � #       :    @ )   V    �    � !   �    �    �    �         %     1  #   C     g  6   v     �     �     �  
   �  0  �  �   
" &   �"    �" :   �" 2   
# =   =# T   {# .   �#    �#    $    #$     =$    ^$ 
   z$    �$    �$    �$    �$ D   �$ T   % H   i% H   �% O   �% A   K& V   �& "   �& Q   ' O   Y' '   �' m   �' X   ?(    �( x   �( k   )    �)    �) &   �)    �)     �)      *    A*    [*    y*    �*    �* 2   �*    �*    �*    �*    + 5   +    P+    W+ 	   `+    j+ 	   �+ &   �+    �+    �+ 	   �+    �+    �+ &   �+    ,     ,    7,    U,    ], 	   },    �, C   �, 
   �,    �, +   �,    $-    --    F-    O- 
   n-    y-    �- �   �-   6. �   P/ !  �/ �  1 �   �3    64    L4    j4    4    �4    �4    �4 7   �4 �   5 p   �5 �   6 )   �6 W   �6 H   7 �   Y7 �   �7 6  �8 �   �9 ~   U: !   �:    �:    ;    ;    ';    6;    L;    i;    p;    �;    �; '   �;    �;    �;    <    !<    7<    R<    k< 
   �< 
   �<    �<    �< #   �<    �< P  �<    O> 
   T>    b> 5   h>    �> Y   �> q   �> Q   q? H   �? T   @    a@    i@    q@ �   u@    A    A G   2A �   zA    B    1B �   GB 3   C x  9C �   �G �   3H    I �  J P   �K ~  �K �   YM f  N i  xP #   �Q    R �   R s   �R s   S _   xS �   �S T   uT �   �T +   TU E   �U 
   �U &   �U h   �U *   aV    �V    �V    �V %   �V �   �V    �W B   �W w   �W %   sX -   �X d   �X �   ,Y 
  �Y    �[    �[ 
  
\    ] 
   ']    2]    ?]    ]] F   m] �  �]    �_    �_    �_ 
   �_ -  �_ 1   b 
   =b    Kb    Xb    xb 
   �b @   �b !   �b    �b ,   �b �   (c '   d    )d �   ;d !   �d    �d    �d    e W   !e �   ye    f Z   !f   |f �   �g u   &h    �h �  �h y   Rj    �j 0   �j    k    /k   0l    =m �   Zm �   �m '  �n "   �o '   p    >p N  Rp $   �r ,   �r ,   �r #    s ?   Ds �   �s ,   ht #   �t 7   �t W   �t 
   Iu *   Tu %   u :   �u y  �u ]  Zy |  �{ V   5 B   �    �    � �  � z  ��   � *   ,� `  W� �  �� 0   A� <   r� �  �� �  >�    7�    F� D   [� 
   ��   ��    �� ,  ڐ �  � �  �� $   )� �   N� K   � 3   \� �   �� �  "� �  � (  � c  �� �   � �  �� �   s� 
  �� +  � h   1� (  �� T   æ 9   �    R�    r�     �� 5   �� &   ا w   �� H   w� 	   �� E   ʨ �  � @   Ԫ N  � �   d� �   ;� 9   '� ,   a� )   ��    �� 2   Ѯ 3   � @   8� 2   y� ]  �� 9   
� 6  D�    {� ;  �� �   ҳ �   e� �  "� ;  ж -   � �   :� 	   ��   � �   �     ջ �  ��    ��    �� "   Ӿ #   �� M   �    h�    u� �   ~� <  � �  B�    �� 
   � (   � �   :� �  8�   � �   �     ��     � #   )� *   M�    x�    ��    �� n   �� @   &� s  g� �   �� "   ^� �   �� �   I� |   �� �   u� �   C� y   ��    ^� �   n� v   � �   �� �   h� s   �� "   j� Z  �� �   �� S   �� �   �� �   r� W   ��    T� _   c� �   �� �   i� a   ]�   ��    �� �  �� �   �� �   g� �   ��    �� �  �� `  �� $   +� ,   P�    }� 
   �� �  �� @   �� �   �� &  J� �   q� �   j� F  � �   `� �   >� �   �� k   �� �  4� �   �� 3  k� 0  �� K  �� 
   � 
   *� 
   8� �   F� 
   � 
   � }   (� p   �� A  � 
   Y� �   g� x   8� �   �� ?   V� �   �� r   T� 
   �� �   �� 
   �� 
   �� 
   �� Q  �� �   )  u  �  .   W    �    �   � %   � :   � h   �   � .   ^    � ,   � @   � A    J   M .   � .   � v  � 1   m
 s  �
 t  
 �   � �   P �   � �  �   \ �  c    �     �   �    � 
  � %   � a   �  m  �  �! �   �# �  n$ +  ]&   �' �   �( D   I) P   �)    �) <   �)    7*    F*    _* o  x*    �+ �   �+ K   �,    �,    �,    -     - �   7- �   . �   �. o  / A   �0    �0    �0 $   �0 ,   1    01    B1 	   N1    X1    d1    m1 	   y1    �1 ?   �1 �   �1    {2    �2    �2    �2    �2    �2    �2    3    3    33 1   S3 �   �3    44 C   L4 �   �4 �   5 4  �5 !  07 p  R8 (  �9   �: /   �< �   (= N    > �   O> o   �> #  B? =  f@ �   �A �   )B    �B �   �B �   �C �   %D    �D     E    #E    BE    _E ?   sE    �E 3   �E    �E     �E #   F 1   BF &   tF    �F    �F    �F    �F    �F    �F %    G    &G H   2G Q   {G �   �G �   �H /   �I 6   �I �   �I �   �J �   BK M   �K 5   ,L 6   bL K   �L ]   �L 8   CM P  |M �  �N �  �P ;   [R _   �R �   �R ;   �S �  T �  �U �   QW    )X     �X   �X   �Y �    [ ?   �[ �   \ �  �\ 0   d^ K   �^ 3   �^ W   _ �   m_ �   !`    �`    �`    �` ?   �` �   a T   �a ?    b Q   `b ,   �b %   �b +   c L   1c (   ~c q   �c F   d �  `d v   �e �   of   �f H   h "   Mh &   ph (   �h &   �h (   �h &   i (   7i f   `i Q   �i �  j �   �m M   =n \   �n 4   �n    o �   &o �   �o .  �p C  �q 	   s �   s +   �s    �s N  
t +   Yu C   �u \   �u F   &v �   mv ~   �v ^   {w !   �w .   �w   +x C   Jy =   �y �   �y s  iz ]   �{ �   ;| �   -} �   �} �   n~ @   1 P   r �   � +   �� �   ǀ J   �� (  ݁ ;   � }   B� 1   �� 6   � �  )� �   �� Y   .� �   �� �   g� f   �� �   ]� �   >�    � #   	� �   -� �   �� '   <� )   d�    ��    ��     �� 1   ׋ �   	� r   �� -   � :   C� G   ~� C   ƍ J   
� �   U� l   "� �   �� �   x� @   4� �   u� j  (� D   �� s   ؓ $   L� n   q� W   �� .   8� �   g� �   A� 8   � v   P� !   Ǘ D   � s   .� $   �� �   ǘ <   l� #   �� 3   ͙ �   � <  �� P   ˛    �    6� P   P�    �� g   �� a   #�   �� �   �� "   e� b   ��   � -   	�   7� _   V� =   �� �   �� s  ��    �    �    � !   � @   5� Q   v� �   ȥ ,   J�    w�    �� (   �� `   Ц W   1� 
   �� t   �� m   	�    w� 8   �� e   ɨ N   /� %   ~� �   ��   0� �   ��    =�    W� �   i� �  7� x   �    {� M  ��   � <   a� �   �� 7   L� �  �� =   I� �   �� *   I�    t�   �� @   �� �   ҽ �   �� �   M� �   �� -   �� �   �� g   r� 7   �� �   � N   �� ?   �� h   >� Q   �� `   �� �   Z�   :� F   U� $  �� {  �� '  =� <   e� G  �� g  �� O  R� �  �� F  o� A   ��    ��   � K   1� a  }� �   �� W   ��    �� *   � �  /�    � 3  � �  D� �  �� ;  �� p   �� �   [� �   �� Y   �� Z   �� �   :� �   �� +   s� ?   �� J   �� 8   *�    c� K   �� �   �� [   ��    �� �   �� U  |�   ��    ��    �� 
   � r   '� o   �� j   
� �   u� �   o� "   � I   4� �  ~�    � �   *� �   � Y  �� Y   +� 6   ��    ��    ��    �� c   �� >   1� �   p� �  0� �   ��    K�    c� *   � %   �� �   ��    c� R   {� 
   �� H   �� U   "�    x�    �� �   �� 
   J� .   U� '   �� '   ��    �� �   �� V  �  �   � �   � t   F E  �     /      G 4  J )   
   � �   � �   r	 f   �	 �   c
 �    �   �    O �   h    �    
    *
    B
    \
    {
    �
 !   �
    �
    �
 <   �
 <   8 D   u /   � A   � >   , -   k &   �    � Z   � &   3    Z f   g 7   � �    5   � 2   �     D   / D   t    � �   � �   � �   
 A  �         4 '   S "   { c   � 	           % �   E %   , #   R    v 
   } 	   � j   � &   � �  $    � (   � %    '   > %   f     � &   � (   � ,   � G   * 9   r    � �   � J   R    �    � C   � p    ,   � P   � =   � �   < �   � k   �  
   �  &   �  	   ! 
   )! !   7! (   Y! J  �! �   �#    �$    �$ B   �$    �$ 2   %    F%    a% �   r% X   & D  e&    �' '   �' �   �' '   �( #   �( &   ) )   5) 7   _) )   �)    �)    �) @   �)    0* H   P* ?   �* �  �* |   �, s   +- u   �- "   . �   8. (   &/    O/    c/    �/ 0   �/ .   ,0 P   [0 |   �0 (   )1   R1    r2    �2 8   �2 !   �2    �2    3 #   )3    M3    k3 �   �3 �  
4 %   �5 +   �5 z  �5   ^7    p8 &   �8    �8    �8    �8    �8    9 "   !9    D9    [9    {9    �9    �9    �9    �9    �9    �9    �9 )   : f  +: �  �; g  R= �  �? �  WC >  �E   8G    <I �  PI    �J �  K �  �L �  
N   �P E  �Q C   %T �   iT N   HU "   �U a   �U l   V �   �V �   W   �W 	  �X k  �Y �  5[    �^ 1  _ �  8` �   �a   pb "   |c \  �c ?  �d f  <g 1  �h    �i �   �i �   �j @  /k �   pl �   im �   %n '   �n ;   �n 2   5o ^   ho e   �o    -p �   Kp R   �p �   Cq 1   �q �   �q   �r c   �s �   Xt r   �t �   `u �   �u u   �v �   �v �   �w �   'x �   �x 
   �y H   �y U   z �   oz D   �z    8{    E{ �   W{ 0   �{    	|    )| b   8| X   �| p   �|    e}    �}    �}    �} 	   �} �   �} ~   /~ �   �~ R   H    � )   � D   �    �    &�    5�    B� 
   O� 
   Z�    e�    n�    ~� !   ��    ��    ��    ΀    ߀    �    �    �    -�    :�    K� @   T� %   �� %   �� �   �    �� �   ͂   N�    a�    q�    �� 
   ��    ��    ��    �� �    �   �� e   d�    ʆ    �� �   ��    �� �   ˇ �   �� G   ~� +   Ɖ �   � >  �� �   �� )  �� �   ȍ 1   o� �   ��    v�     ��    �� �   7� 9   � �   U� �   9�   � �   )�    �� 
   ǔ    Ҕ    �    ��    �    "�    6� �   T� �   A� �   �   �� �  ܘ �   t� �   � �  �� D  &� �  k� �   � �  Ϣ (  u� 
   ��   �� �  �� C  �� �  ƪ �  �� �   d� B  $�     g� u   �� �   �� A  ֵ z   � �  ��    � 
   -�    8� �   R� V   � �   p� Q   !� �   s� [   � �   k� <   � �   O� e   ;� 2   �� �   Կ 2  `� #   ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: fa
Language-Team: fa <<EMAIL>>
Plural-Forms: nplurals=1; plural=0;
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library یک کتابخانه محبوب (و غیرقانونی) است. آن‌ها مجموعه Library Genesis را گرفته و به راحتی قابل جستجو کرده‌اند. علاوه بر این، آن‌ها در جذب مشارکت‌های جدید کتاب بسیار مؤثر شده‌اند، با تشویق کاربران مشارکت‌کننده با امتیازات مختلف. آن‌ها در حال حاضر این کتاب‌های جدید را به Library Genesis برنمی‌گردانند. و برخلاف Library Genesis، آن‌ها مجموعه خود را به راحتی قابل لینک کمکی نمی‌کنند، که مانع از حفظ گسترده می‌شود. این برای مدل کسب‌وکار آن‌ها مهم است، زیرا آن‌ها برای دسترسی به مجموعه خود به صورت عمده (بیش از ۱۰ کتاب در روز) هزینه دریافت می‌کنند. ما در مورد دریافت هزینه برای دسترسی عمده به یک مجموعه کتاب غیرقانونی قضاوت اخلاقی نمی‌کنیم. بدون شک، Z-Library در گسترش دسترسی به دانش و تأمین کتاب‌های بیشتر موفق بوده است. ما فقط اینجا هستیم تا نقش خود را ایفا کنیم: اطمینان از حفظ طولانی‌مدت این مجموعه خصوصی. - آنا و تیم (<a %(reddit)s>Reddit</a>) در نسخه اصلی انتشار لینک کمکی کتابخانه دزدان دریایی (ویرایش: به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> منتقل شد)، ما یک لینک کمکی از Z-Library، یک مجموعه بزرگ کتاب غیرقانونی، ایجاد کردیم. به عنوان یادآوری، این چیزی است که در آن پست وبلاگ اصلی نوشتیم: آن مجموعه به اواسط سال ۲۰۲۱ برمی‌گردد. در این مدت، Z-Library با سرعتی شگفت‌انگیز رشد کرده است: آن‌ها حدود ۳.۸ میلیون کتاب جدید اضافه کرده‌اند. البته برخی از آن‌ها تکراری هستند، اما بیشتر آن‌ها به نظر می‌رسد کتاب‌های جدید واقعی یا اسکن‌های با کیفیت بالاتر از کتاب‌های قبلاً ارسال شده باشند. این عمدتاً به دلیل افزایش تعداد مدیران داوطلب در Z-Library و سیستم بارگذاری عمده آن‌ها با حذف تکراری‌ها است. ما می‌خواهیم به آن‌ها برای این دستاوردها تبریک بگوییم. ما خوشحالیم که اعلام کنیم تمام کتاب‌هایی که بین آخرین لینک کمکی ما و آگوست ۲۰۲۲ به Z-Library اضافه شده بودند را دریافت کرده‌ایم. همچنین به عقب برگشته و برخی کتاب‌هایی که در اولین بار از دست داده بودیم را جمع‌آوری کرده‌ایم. در مجموع، این مجموعه جدید حدود ۲۴ ترابایت است که بسیار بزرگتر از مجموعه قبلی (۷ ترابایت) است. لینک کمکی ما اکنون در مجموع ۳۱ ترابایت است. دوباره، ما در برابر Library Genesis تکراری‌ها را حذف کردیم، زیرا برای آن مجموعه تورنت‌هایی در دسترس است. لطفاً به لینک کمکی کتابخانه دزدان دریایی بروید تا مجموعه جدید را بررسی کنید (ویرایش: به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> منتقل شد). اطلاعات بیشتری در آنجا درباره ساختار فایل‌ها و تغییرات از آخرین بار وجود دارد. ما از اینجا به آن لینک نمی‌دهیم، زیرا این فقط یک وبلاگ است که هیچ مواد غیرقانونی را میزبانی نمی‌کند. البته، بذرپاشی نیز یک راه عالی برای کمک به ما است. از همه کسانی که مجموعه تورنت‌های قبلی ما را بذرپاشی می‌کنند، متشکریم. ما از پاسخ مثبت شما سپاسگزاریم و خوشحالیم که افراد زیادی به حفظ دانش و فرهنگ به این روش غیرمعمول اهمیت می‌دهند. 3 کتاب جدید به لینک کمکی کتابخانه دزدان دریایی اضافه شد (+24TB، 3.8 میلیون کتاب) مقالات همراه را توسط TorrentFreak بخوانید: <a %(torrentfreak)s>اول</a>، <a %(torrentfreak_2)s>دوم</a> - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) مقالات همراه توسط TorrentFreak: <a %(torrentfreak)s>اول</a>، <a %(torrentfreak_2)s>دوم</a> نه چندان دور، "کتابخانه‌های سایه" در حال نابودی بودند. Sci-Hub، آرشیو غیرقانونی بزرگ مقالات علمی، به دلیل دعاوی حقوقی، از پذیرش آثار جدید متوقف شده بود. "Z-Library"، بزرگترین کتابخانه غیرقانونی کتاب‌ها، با دستگیری خالقان ادعایی‌اش به اتهام نقض حق نشر مواجه شد. آنها به طرز شگفت‌انگیزی از دستگیری فرار کردند، اما کتابخانه‌شان همچنان در معرض تهدید است. برخی کشورها در حال حاضر نسخه‌ای از این را انجام می‌دهند. TorrentFreak <a %(torrentfreak)s>گزارش داد</a> که چین و ژاپن استثناهای هوش مصنوعی را به قوانین حق نشر خود معرفی کرده‌اند. برای ما مشخص نیست که این چگونه با معاهدات بین‌المللی تعامل دارد، اما قطعاً به شرکت‌های داخلی آنها پوشش می‌دهد، که توضیح می‌دهد آنچه را که دیده‌ایم. در مورد آرشیو آنا - ما به کار زیرزمینی خود که بر اساس اعتقاد اخلاقی است، ادامه خواهیم داد. با این حال، بزرگترین آرزوی ما این است که به نور وارد شویم و تأثیر خود را به صورت قانونی تقویت کنیم. لطفاً حق نشر را اصلاح کنید. وقتی Z-Library با تعطیلی مواجه شد، من قبلاً کل کتابخانه آن را پشتیبان‌گیری کرده بودم و به دنبال یک پلتفرم برای میزبانی آن بودم. این انگیزه من برای شروع آرشیو آنا بود: ادامه مأموریت پشت آن ابتکارات قبلی. از آن زمان تاکنون به بزرگترین کتابخانه سایه در جهان تبدیل شده‌ایم و بیش از ۱۴۰ میلیون متن دارای حق نشر را در قالب‌های مختلف - کتاب‌ها، مقالات علمی، مجلات، روزنامه‌ها و بیشتر - میزبانی می‌کنیم. تیم من و من ایدئولوگ هستیم. ما معتقدیم که حفظ و میزبانی این فایل‌ها از نظر اخلاقی درست است. کتابخانه‌ها در سراسر جهان با کاهش بودجه مواجه هستند و نمی‌توانیم به میراث بشری به شرکت‌ها اعتماد کنیم. سپس هوش مصنوعی آمد. تقریباً همه شرکت‌های بزرگ سازنده LLMها با ما تماس گرفتند تا بر روی داده‌های ما آموزش ببینند. بیشتر (اما نه همه!) شرکت‌های مستقر در ایالات متحده پس از درک ماهیت غیرقانونی کار ما، تجدید نظر کردند. در مقابل، شرکت‌های چینی با اشتیاق مجموعه ما را پذیرفتند، ظاهراً بدون نگرانی از قانونی بودن آن. این قابل توجه است با توجه به نقش چین به عنوان امضاکننده تقریباً همه معاهدات بین‌المللی حق نشر. ما دسترسی با سرعت بالا به حدود ۳۰ شرکت داده‌ایم. بیشتر آنها شرکت‌های LLM هستند و برخی دلالان داده هستند که مجموعه ما را مجدداً به فروش خواهند رساند. بیشتر آنها چینی هستند، اگرچه ما با شرکت‌هایی از ایالات متحده، اروپا، روسیه، کره جنوبی و ژاپن نیز کار کرده‌ایم. DeepSeek <a %(arxiv)s>اعتراف کرد</a> که نسخه قبلی بر روی بخشی از مجموعه ما آموزش دیده است، اگرچه آنها درباره مدل جدید خود سکوت کرده‌اند (احتمالاً آن هم بر روی داده‌های ما آموزش دیده است). اگر غرب می‌خواهد در رقابت LLMها و در نهایت AGI پیشرو باشد، باید موضع خود را در مورد حق نشر بازنگری کند و به زودی. چه با ما در مورد مورد اخلاقی ما موافق باشید یا نه، این اکنون به یک مسئله اقتصادی و حتی امنیت ملی تبدیل شده است. همه بلوک‌های قدرت در حال ساختن ابر-دانشمندان مصنوعی، ابر-هکرها و ابر-نظامی‌ها هستند. آزادی اطلاعات به مسئله بقا برای این کشورها تبدیل شده است - حتی به مسئله امنیت ملی. تیم ما از سراسر جهان است و ما هم‌راستایی خاصی نداریم. اما ما کشورهایی با قوانین حق نشر قوی را تشویق می‌کنیم که از این تهدید وجودی برای اصلاح آنها استفاده کنند. پس چه باید کرد؟ اولین توصیه ما ساده است: مدت زمان حق نشر را کوتاه کنید. در ایالات متحده، حق نشر برای ۷۰ سال پس از مرگ نویسنده اعطا می‌شود. این مضحک است. ما می‌توانیم این را با حق اختراع هماهنگ کنیم، که برای ۲۰ سال پس از ثبت اعطا می‌شود. این باید بیش از حد کافی باشد تا نویسندگان کتاب‌ها، مقالات، موسیقی، هنر و دیگر آثار خلاقانه به طور کامل برای تلاش‌هایشان جبران شوند (از جمله پروژه‌های بلندمدت مانند اقتباس‌های سینمایی). سپس، حداقل، سیاست‌گذاران باید استثناهایی برای حفظ و انتشار گسترده متون در نظر بگیرند. اگر از دست دادن درآمد از مشتریان فردی نگرانی اصلی است، توزیع در سطح شخصی می‌تواند ممنوع باقی بماند. در عوض، کسانی که قادر به مدیریت مخازن وسیع هستند - شرکت‌هایی که LLMها را آموزش می‌دهند، همراه با کتابخانه‌ها و دیگر آرشیوها - تحت پوشش این استثناها قرار می‌گیرند. اصلاح قانون حق نشر برای امنیت ملی ضروری است. خلاصه: LLMهای چینی (از جمله DeepSeek) بر اساس آرشیو غیرقانونی کتاب‌ها و مقالات من - که بزرگترین در جهان است - آموزش دیده‌اند. غرب باید به عنوان یک مسئله امنیت ملی، قانون حق نشر را بازنگری کند. لطفاً برای اطلاعات بیشتر به <a %(all_isbns)s>پست وبلاگ اصلی</a> مراجعه کنید. ما چالشی برای بهبود این موضوع صادر کردیم. ما جایزه‌ای به مبلغ ۶,۰۰۰ دلار برای مقام اول، ۳,۰۰۰ دلار برای مقام دوم و ۱,۰۰۰ دلار برای مقام سوم تعیین کردیم. به دلیل پاسخ‌های فراوان و ارسال‌های فوق‌العاده، تصمیم گرفتیم کمی جایزه را افزایش دهیم و چهار جایزه ۵۰۰ دلاری برای مقام سوم اهدا کنیم. برندگان در زیر آمده‌اند، اما حتماً به همه ارسال‌ها <a %(annas_archive)s>اینجا</a> نگاه کنید، یا <a %(a_2025_01_isbn_visualization_files)s>تورنت ترکیبی ما</a> را دانلود کنید. مقام اول ۶,۰۰۰ دلار: phiresky این <a %(phiresky_github)s>ارسال</a> (<a %(annas_archive_note_2951)s>نظر در Gitlab</a>) دقیقاً همان چیزی است که ما می‌خواستیم و حتی بیشتر! ما به ویژه گزینه‌های تجسم بسیار انعطاف‌پذیر (حتی پشتیبانی از شیدرهای سفارشی) را دوست داشتیم، اما با یک لیست جامع از پیش‌تنظیمات. همچنین سرعت و روانی همه چیز، پیاده‌سازی ساده (که حتی بک‌اند ندارد)، نقشه کوچک هوشمند و توضیحات گسترده در <a %(phiresky_github)s>پست وبلاگ</a> آن‌ها را دوست داشتیم. کار فوق‌العاده و برنده‌ای که به حق سزاوار است! - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) قلب‌های ما پر از قدردانی است. ایده‌های قابل توجه آسمان‌خراش‌ها برای نایابی تعداد زیادی اسلایدر برای مقایسه مجموعه داده‌ها، انگار که یک دی‌جی هستید. نوار مقیاس با تعداد کتاب‌ها. برچسب‌های زیبا. طرح رنگی پیش‌فرض جذاب و نقشه حرارتی. نمای نقشه منحصربه‌فرد و فیلترها حاشیه‌نویسی‌ها و همچنین آمار زنده آمار زنده برخی ایده‌ها و پیاده‌سازی‌هایی که به‌ویژه دوست داشتیم: می‌توانیم برای مدتی ادامه دهیم، اما بیایید اینجا متوقف شویم. حتماً به همه ارسال‌ها <a %(annas_archive)s>اینجا</a> نگاه کنید، یا تورنت <a %(a_2025_01_isbn_visualization_files)s>ترکیبی ما</a> را دانلود کنید. تعداد زیادی ارسال وجود دارد و هر کدام دیدگاه منحصربه‌فردی را ارائه می‌دهند، چه در رابط کاربری و چه در پیاده‌سازی. ما حداقل ارسال مقام اول را در وب‌سایت اصلی خود ادغام خواهیم کرد و شاید برخی دیگر را نیز. همچنین شروع به فکر کردن درباره چگونگی سازماندهی فرآیند شناسایی، تأیید و سپس بایگانی نایاب‌ترین کتاب‌ها کرده‌ایم. اطلاعات بیشتری در این زمینه به زودی ارائه خواهد شد. از همه کسانی که شرکت کردند تشکر می‌کنیم. شگفت‌انگیز است که این‌قدر افراد اهمیت می‌دهند. تغییر آسان مجموعه داده‌ها برای مقایسه سریع. همهٔ ISBNها SSNOهای CADAL نشت داده‌های CERLALC SSIDهای DuXiu شاخص کتاب الکترونیکی EBSCOhost کتاب‌های گوگل گودریدز آرشیو اینترنت ISBNdb ثبت جهانی ناشران ISBN لیبی فایل‌ها در آرشیو آنا Nexus/STC OCLC/Worldcat کتابخانهٔ باز کتابخانهٔ دولتی روسیه کتابخانه امپریال ترانتور مقام دوم ۳,۰۰۰ دلار: hypha «در حالی که مربع‌ها و مستطیل‌های کامل از نظر ریاضی دلپذیر هستند، در زمینه نقشه‌برداری برتری محلی ارائه نمی‌دهند. من معتقدم عدم تقارن ذاتی در این منحنی‌های هیلبرت یا مورتون کلاسیک نقص نیست بلکه ویژگی است. درست مانند طرح معروف چکمه‌ای شکل ایتالیا که آن را به سرعت در نقشه قابل تشخیص می‌کند، "ویژگی‌های" منحصر به فرد این منحنی‌ها ممکن است به عنوان نشانه‌های شناختی عمل کنند. این تمایز می‌تواند حافظه فضایی را تقویت کند و به کاربران کمک کند تا خود را جهت‌دهی کنند، و احتمالاً یافتن مناطق خاص یا مشاهده الگوها را آسان‌تر کند.» یکی دیگر از <a %(annas_archive_note_2913)s>ارسال‌های</a> فوق‌العاده. به اندازه مقام اول انعطاف‌پذیر نیست، اما در واقع تجسم در سطح کلان آن را بر مقام اول ترجیح دادیم (منحنی پرکننده فضا، مرزها، برچسب‌گذاری، برجسته‌سازی، پیمایش و بزرگنمایی). یک <a %(annas_archive_note_2971)s>نظر</a> از جو دیویس با ما هم‌صدا شد: و هنوز هم گزینه‌های زیادی برای تجسم و رندرینگ، و همچنین یک رابط کاربری فوق‌العاده روان و شهودی وجود دارد. یک مقام دوم محکم! - آنا و تیم (<a %(reddit)s>Reddit</a>) چند ماه پیش، ما یک <a %(all_isbns)s>جایزهٔ ۱۰٬۰۰۰ دلاری</a> برای بهترین تجسم ممکن از داده‌های خود که فضای ISBN را نشان می‌دهد، اعلام کردیم. ما بر نشان دادن اینکه کدام فایل‌ها را قبلاً بایگانی کرده‌ایم/نکرده‌ایم تأکید کردیم و بعداً یک مجموعه داده توصیف‌کنندهٔ تعداد کتابخانه‌هایی که ISBNها را نگه می‌دارند (معیاری از نادر بودن) اضافه کردیم. ما از پاسخ‌ها شگفت‌زده شده‌ایم. خلاقیت زیادی وجود داشته است. از همه کسانی که شرکت کرده‌اند، تشکر بزرگی داریم: انرژی و اشتیاق شما مسری است! در نهایت، ما می‌خواستیم به سوالات زیر پاسخ دهیم: <strong>کدام کتاب‌ها در جهان وجود دارند، چند تا از آن‌ها را قبلاً بایگانی کرده‌ایم، و بر روی کدام کتاب‌ها باید تمرکز کنیم؟</strong> دیدن اینکه این سوالات برای بسیاری از افراد مهم است، عالی است. ما خودمان با یک تجسم پایه شروع کردیم. در کمتر از ۳۰۰ کیلوبایت، این تصویر به‌طور مختصر بزرگترین "فهرست کتاب‌ها"ی کاملاً باز را که تاکنون در تاریخ بشریت گردآوری شده است، نشان می‌دهد: مقام سوم ۵۰۰ دلار #۱: maxlion در این <a %(annas_archive_note_2940)s>ارسال</a> ما واقعاً انواع مختلف نماها را دوست داشتیم، به ویژه نماهای مقایسه و ناشر. مقام سوم ۵۰۰ دلار #۲: abetusk در حالی که رابط کاربری آن چندان صیقلی نیست، این <a %(annas_archive_note_2917)s>ارسال</a> بسیاری از معیارها را برآورده می‌کند. ما به ویژه ویژگی مقایسه آن را دوست داشتیم. مقام سوم ۵۰۰ دلار #۳: conundrumer0 مانند مقام اول، این <a %(annas_archive_note_2975)s>ارسال</a> ما را با انعطاف‌پذیری خود تحت تأثیر قرار داد. در نهایت این همان چیزی است که یک ابزار تجسم عالی را می‌سازد: حداکثر انعطاف‌پذیری برای کاربران حرفه‌ای، در حالی که چیزها را برای کاربران عادی ساده نگه می‌دارد. مقام سوم ۵۰۰ دلار #۴: charelf آخرین <a %(annas_archive_note_2947)s>ارسال</a> که جایزه‌ای دریافت می‌کند، نسبتاً ساده است، اما دارای ویژگی‌های منحصر به فردی است که واقعاً دوست داشتیم. ما دوست داشتیم که چگونه نشان می‌دهند چند مجموعه داده یک ISBN خاص را به عنوان معیاری از محبوبیت/قابلیت اطمینان پوشش می‌دهند. همچنین سادگی اما اثربخشی استفاده از یک اسلایدر شفافیت برای مقایسه‌ها را واقعاً دوست داشتیم. برندگان جایزه ۱۰,۰۰۰ دلاری تجسم ISBN خلاصه: ما برخی از ارسال‌های شگفت‌انگیز برای جایزه ۱۰,۰۰۰ دلاری تجسم ISBN دریافت کردیم. پیش‌زمینه چگونه می‌تواند آرشیو آنا مأموریت خود را برای پشتیبان‌گیری از تمام دانش بشری انجام دهد، بدون اینکه بداند کدام کتاب‌ها هنوز وجود دارند؟ ما به یک فهرست کارها نیاز داریم. یکی از راه‌های ترسیم این نقشه، استفاده از شماره‌های ISBN است که از دهه ۱۹۷۰ به هر کتاب منتشر شده (در بیشتر کشورها) اختصاص داده شده است. هیچ مرجع مرکزی وجود ندارد که از تمام تخصیص‌های ISBN آگاه باشد. در عوض، این یک سیستم توزیع شده است که در آن کشورها محدوده‌هایی از اعداد را دریافت می‌کنند، سپس این محدوده‌ها را به ناشران بزرگ اختصاص می‌دهند، که ممکن است این محدوده‌ها را به ناشران کوچک‌تر تقسیم کنند. در نهایت، شماره‌های فردی به کتاب‌ها اختصاص داده می‌شوند. ما دو سال پیش با خراشیدن ISBNdb شروع به نقشه‌برداری از ISBNها کردیم. از آن زمان، منابع متادیتای بسیاری بیشتری را خراشیده‌ایم، مانند <a %(blog_2)s>Worldcat</a>، Google Books، Goodreads، Libby و بیشتر. فهرست کامل را می‌توان در صفحات "Datasets" و "Torrents" در آرشیو آنا یافت. اکنون ما به مراتب بزرگ‌ترین مجموعه کاملاً باز و به راحتی قابل دانلود از متادیتای کتاب (و بنابراین ISBNها) در جهان را داریم. ما <a %(blog)s>به طور گسترده‌ای نوشته‌ایم</a> که چرا به حفظ اهمیت می‌دهیم و چرا در حال حاضر در یک پنجره بحرانی قرار داریم. اکنون باید کتاب‌های نادر، کم‌توجه و به‌طور منحصربه‌فرد در معرض خطر را شناسایی و حفظ کنیم. داشتن متادیتای خوب در مورد تمام کتاب‌های جهان به این امر کمک می‌کند. جایزه ۱۰,۰۰۰ دلاری توجه ویژه‌ای به قابلیت استفاده و ظاهر خوب داده خواهد شد. نمایش متادیتای واقعی برای ISBNهای فردی هنگام بزرگنمایی، مانند عنوان و نویسنده. منحنی پرکننده فضای بهتر. به عنوان مثال، یک زیگ‌زاگ که در ردیف اول از ۰ به ۴ می‌رود و سپس در ردیف دوم به صورت معکوس از ۵ به ۹ برمی‌گردد - به صورت بازگشتی اعمال می‌شود. طرح‌های رنگی متفاوت یا قابل تنظیم. نمایش‌های ویژه برای مقایسه مجموعه داده‌ها. راه‌هایی برای رفع مشکلات، مانند متادیتای دیگر که به خوبی همخوانی ندارند (مثلاً عناوین بسیار متفاوت). حاشیه‌نویسی تصاویر با نظرات در مورد ISBNها یا محدوده‌ها. هرگونه روش اکتشافی برای شناسایی کتاب‌های نادر یا در معرض خطر. هر ایده خلاقانه‌ای که می‌توانید ارائه دهید! کد کدی که برای تولید این تصاویر استفاده می‌شود، به همراه مثال‌های دیگر، در <a %(annas_archive)s>این دایرکتوری</a> یافت می‌شود. ما یک فرمت داده فشرده ایجاد کردیم که با آن تمام اطلاعات مورد نیاز ISBN حدود ۷۵ مگابایت (فشرده) است. توضیحات فرمت داده و کد برای تولید آن را می‌توانید <a %(annas_archive_l1244_1319)s>اینجا</a> پیدا کنید. برای جایزه نیازی به استفاده از این نیست، اما احتمالاً راحت‌ترین فرمت برای شروع است. شما می‌توانید متادیتای ما را به هر شکلی که می‌خواهید تغییر دهید (اگرچه تمام کد شما باید منبع باز باشد). ما نمی‌توانیم صبر کنیم تا ببینیم چه چیزی ارائه می‌دهید. موفق باشید! این مخزن را فورک کنید و HTML این پست وبلاگ را ویرایش کنید (هیچ بک‌اند دیگری به جز بک‌اند Flask ما مجاز نیست). تصویر بالا باید به‌طور روان قابل زوم باشد، به‌طوری که بتوانید تا ISBNهای فردی زوم کنید. کلیک بر روی ISBNها باید شما را به صفحه متادیتا یا جستجو در آرشیو آنا ببرد. شما باید همچنان بتوانید بین تمام مجموعه داده‌های مختلف جابجا شوید. محدوده‌های کشور و ناشر باید هنگام قرار گرفتن نشانگر برجسته شوند. می‌توانید از <a %(github_xlcnd_isbnlib)s>data4info.py در isbnlib</a> برای اطلاعات کشور و از خراش "isbngrp" ما برای ناشران استفاده کنید (<a %(annas_archive)s>dataset</a>، <a %(annas_archive_2)s>torrent</a>). باید به خوبی روی دسکتاپ و موبایل کار کند. چیزهای زیادی برای کاوش در اینجا وجود دارد، بنابراین ما جایزه‌ای برای بهبود بصری‌سازی بالا اعلام می‌کنیم. برخلاف بیشتر جوایز ما، این یکی محدود به زمان است. شما باید کد منبع باز خود را تا ۲۰۲۵-۰۱-۳۱ (۲۳:۵۹ UTC) <a %(annas_archive)s>ارسال کنید</a>. بهترین ارسال ۶,۰۰۰ دلار دریافت خواهد کرد، جایگاه دوم ۳,۰۰۰ دلار و جایگاه سوم ۱,۰۰۰ دلار. تمام جوایز با استفاده از Monero (XMR) اهدا خواهند شد. در زیر حداقل معیارها آمده است. اگر هیچ ارسالی معیارها را برآورده نکند، ممکن است همچنان برخی جوایز اهدا شود، اما این به صلاحدید ما خواهد بود. برای امتیازهای اضافی (این‌ها فقط ایده هستند — بگذارید خلاقیت شما به اوج برسد): شما می‌توانید کاملاً از معیارهای حداقلی دور شوید و یک تجسم کاملاً متفاوت انجام دهید. اگر واقعاً فوق‌العاده باشد، واجد شرایط جایزه است، اما به صلاحدید ما. با ارسال یک نظر به <a %(annas_archive)s>این مسئله</a> با لینک به مخزن فورک شده، درخواست ادغام یا تفاوت، ارسال‌ها را انجام دهید. - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) این تصویر ۱۰۰۰×۸۰۰ پیکسل است. هر پیکسل نمایانگر ۲,۵۰۰ ISBN است. اگر فایلی برای یک ISBN داشته باشیم، آن پیکسل را سبزتر می‌کنیم. اگر بدانیم یک ISBN صادر شده است، اما فایل مطابقتی نداریم، آن را قرمزتر می‌کنیم. در کمتر از ۳۰۰ کیلوبایت، این تصویر به‌طور مختصر بزرگ‌ترین «فهرست کتاب» کاملاً باز را که تاکنون در تاریخ بشریت گردآوری شده است، نشان می‌دهد (چند صد گیگابایت فشرده به‌طور کامل). همچنین نشان می‌دهد: کار زیادی برای پشتیبان‌گیری از کتاب‌ها باقی مانده است (ما فقط ۱۶% داریم). تصویری از همه ISBNها — جایزه ۱۰,۰۰۰ دلاری تا ۲۰۲۵-۰۱-۳۱ این تصویر بزرگ‌ترین «فهرست کتاب» کاملاً باز است که تاکنون در تاریخ بشریت گردآوری شده است. بصری‌سازی علاوه بر تصویر کلی، می‌توانیم به مجموعه داده‌های فردی که به دست آورده‌ایم نیز نگاه کنیم. از منوی کشویی و دکمه‌ها برای جابجایی بین آن‌ها استفاده کنید. در این تصاویر الگوهای جالب زیادی برای دیدن وجود دارد. چرا برخی از خطوط و بلوک‌ها به نظر می‌رسد که در مقیاس‌های مختلف اتفاق می‌افتند؟ مناطق خالی چیست؟ چرا برخی از مجموعه داده‌ها این‌قدر متراکم هستند؟ این سوالات را به عنوان تمرینی برای خواننده باقی می‌گذاریم. - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) نتیجه‌گیری با این استاندارد، می‌توانیم نسخه‌ها را به صورت تدریجی‌تر منتشر کنیم و به راحتی منابع داده جدید اضافه کنیم. ما در حال حاضر چند نسخه هیجان‌انگیز در دست اقدام داریم! ما همچنین امیدواریم که برای سایر کتابخانه‌های سایه آسان‌تر شود تا مجموعه‌های ما را لینک کمکی کنند. به هر حال، هدف ما حفظ دانش و فرهنگ انسانی برای همیشه است، بنابراین هر چه تکرار بیشتر باشد بهتر است. مثال بیایید به نسخه اخیر Z-Library به عنوان مثال نگاه کنیم. این شامل دو مجموعه است: “<span style="background: #fffaa3">zlib3_records</span>” و “<span style="background: #ffd6fe">zlib3_files</span>”. این به ما اجازه می‌دهد تا رکوردهای metadata را جداگانه از فایل‌های واقعی کتاب استخراج و منتشر کنیم. به این ترتیب، ما دو تورنت با فایل‌های metadata منتشر کردیم: ما همچنین یک سری تورنت با پوشه‌های داده باینری منتشر کردیم، اما فقط برای مجموعه “<span style="background: #ffd6fe">zlib3_files</span>”، در مجموع 62 عدد: با اجرای <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> می‌توانیم ببینیم داخل آن چیست: در این مورد، این metadata یک کتاب است که توسط Z-Library گزارش شده است. در سطح بالا فقط “aacid” و “metadata” داریم، اما “data_folder” نداریم، زیرا داده باینری مربوطه وجود ندارد. AACID شامل “22430000” به عنوان شناسه اصلی است که می‌توانیم ببینیم از “zlibrary_id” گرفته شده است. می‌توانیم انتظار داشته باشیم که سایر AACها در این مجموعه ساختار مشابهی داشته باشند. حالا بیایید <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code> را اجرا کنیم: این یک metadata AAC بسیار کوچکتر است، اگرچه بخش عمده این AAC در جای دیگری در یک فایل باینری قرار دارد! به هر حال، این بار یک “data_folder” داریم، بنابراین می‌توانیم انتظار داشته باشیم که داده باینری مربوطه در <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code> قرار داشته باشد. “metadata” شامل “zlibrary_id” است، بنابراین می‌توانیم به راحتی آن را با AAC مربوطه در مجموعه “zlib_records” مرتبط کنیم. ما می‌توانستیم به روش‌های مختلفی مرتبط کنیم، مثلاً از طریق AACID — استاندارد این را تجویز نمی‌کند. توجه داشته باشید که لازم نیست که فیلد “metadata” خود JSON باشد. می‌تواند یک رشته حاوی XML یا هر فرمت داده دیگری باشد. حتی می‌توانید اطلاعات metadata را در blob باینری مرتبط ذخیره کنید، مثلاً اگر داده زیادی باشد. فایل‌ها و metadata ناهمگن، تا حد امکان نزدیک به فرمت اصلی. داده‌های باینری می‌توانند مستقیماً توسط وب‌سرورهایی مانند Nginx ارائه شوند. شناسه‌های ناهمگن در کتابخانه‌های منبع، یا حتی عدم وجود شناسه‌ها. انتشارهای جداگانه metadata در مقابل داده‌های فایل، یا انتشارهای فقط metadata (مثلاً انتشار ISBNdb ما). توزیع از طریق تورنت‌ها، اگرچه با امکان روش‌های توزیع دیگر (مثلاً IPFS). رکوردهای غیرقابل تغییر، زیرا باید فرض کنیم تورنت‌های ما برای همیشه زنده خواهند ماند. انتشارهای افزایشی / انتشارهای قابل افزودن. قابل خواندن و نوشتن توسط ماشین، به‌طور راحت و سریع، به‌ویژه برای پشته ما (Python، MySQL، ElasticSearch، Transmission، Debian، ext4). بازرسی انسانی نسبتاً آسان، اگرچه این امر در مقایسه با خوانایی ماشین ثانویه است. آسان برای بذر دادن مجموعه‌های ما با یک seedbox اجاره‌ای استاندارد. اهداف طراحی ما به آسانی پیمایش دستی فایل‌ها بر روی دیسک یا جستجوپذیری بدون پیش‌پردازش اهمیت نمی‌دهیم. ما به سازگاری مستقیم با نرم‌افزار کتابخانه موجود اهمیت نمی‌دهیم. در حالی که باید برای هر کسی آسان باشد که مجموعه ما را با استفاده از تورنت‌ها بذر دهد، ما انتظار نداریم که فایل‌ها بدون دانش فنی قابل توجه و تعهد قابل استفاده باشند. مورد استفاده اصلی ما توزیع فایل‌ها و metadata مرتبط از مجموعه‌های موجود مختلف است. مهم‌ترین ملاحظات ما عبارتند از: برخی اهداف غیر: از آنجا که آرشیو آنا منبع باز است، ما می‌خواهیم فرمت خود را مستقیماً استفاده کنیم. وقتی شاخص جستجوی خود را تازه‌سازی می‌کنیم، فقط به مسیرهای عمومی دسترسی داریم، به طوری که هر کسی که کتابخانه ما را فورک کند، می‌تواند به سرعت شروع به کار کند. <strong>AAC.</strong> AAC (ظرف آرشیو آنا) یک آیتم واحد است که شامل <strong>metadata</strong> و به صورت اختیاری <strong>داده‌های باینری</strong> است، که هر دو غیرقابل تغییر هستند. این آیتم دارای یک شناسه منحصر به فرد جهانی است که به آن <strong>AACID</strong> گفته می‌شود. <strong>AACID.</strong> فرمت AACID به این صورت است: <code style="color: #0093ff">aacid__{collection}__{ISO 8601 timestamp}__{collection-specific ID}__{shortuuid}</code>. به عنوان مثال، یک AACID واقعی که ما منتشر کرده‌ایم <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code> است. <strong>محدوده AACID.</strong> از آنجا که AACIDها شامل زمان‌بندی‌های یکنواخت افزایشی هستند، می‌توانیم از آن برای نشان دادن محدوده‌ها در یک مجموعه خاص استفاده کنیم. ما از این فرمت استفاده می‌کنیم: <code style="color: blue">aacid__{collection}__{from_timestamp}--{to_timestamp}</code>، که در آن زمان‌بندی‌ها شامل می‌شوند. این با نشانه‌گذاری ISO 8601 سازگار است. محدوده‌ها پیوسته هستند و ممکن است همپوشانی داشته باشند، اما در صورت همپوشانی باید شامل ضبط‌های یکسانی با آنچه قبلاً در آن مجموعه منتشر شده است باشند (زیرا AACها غیرقابل تغییر هستند). ضبط‌های مفقود مجاز نیستند. <code>{collection}</code>: نام مجموعه، که ممکن است شامل حروف ASCII، اعداد و زیرخط باشد (اما نه دو زیرخط). <code>{collection-specific ID}</code>: شناسه خاص مجموعه، در صورت لزوم، مثلاً شناسه Z-Library. ممکن است حذف یا کوتاه شود. باید حذف یا کوتاه شود اگر AACID در غیر این صورت بیش از 150 کاراکتر باشد. <code>{ISO 8601 timestamp}</code>: نسخه کوتاه شده ISO 8601، همیشه در UTC، مثلاً <code>20220723T194746Z</code>. این عدد باید برای هر انتشار به صورت یکنواخت افزایش یابد، اگرچه معنای دقیق آن می‌تواند برای هر مجموعه متفاوت باشد. ما پیشنهاد می‌کنیم از زمان اسکرپینگ یا تولید شناسه استفاده کنید. <code>{shortuuid}</code>: یک UUID اما فشرده شده به ASCII، مثلاً با استفاده از base57. ما در حال حاضر از کتابخانه <a %(github_skorokithakis_shortuuid)s>shortuuid</a> پایتون استفاده می‌کنیم. <strong>پوشه داده‌های باینری.</strong> یک پوشه با داده‌های باینری یک محدوده از AACها، برای یک مجموعه خاص. این پوشه‌ها دارای ویژگی‌های زیر هستند: دایرکتوری باید فایل‌های داده برای تمام AACها در محدوده مشخص شده را شامل شود. هر فایل داده باید AACID خود را به عنوان نام فایل داشته باشد (بدون پسوند). نام دایرکتوری باید یک محدوده AACID باشد که با <code style="color: green">annas_archive_data__</code> پیشوند شده و بدون پسوند باشد. به عنوان مثال، یکی از نسخه‌های واقعی ما دایرکتوری‌ای به نام<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code> دارد. توصیه می‌شود این پوشه‌ها را به گونه‌ای مدیریت‌پذیر در اندازه بسازید، مثلاً هر کدام بزرگتر از 100GB-1TB نباشند، اگرچه این توصیه ممکن است با گذشت زمان تغییر کند. <strong>مجموعه.</strong> هر AAC به یک مجموعه تعلق دارد که به تعریف، لیستی از AACهایی است که از نظر معنایی سازگار هستند. این بدان معناست که اگر تغییر قابل توجهی در فرمت metadata ایجاد کنید، باید یک مجموعه جدید ایجاد کنید. استاندارد <strong>فایل metadata.</strong> یک فایل metadata شامل metadata یک محدوده از AACها برای یک مجموعه خاص است. این فایل‌ها دارای ویژگی‌های زیر هستند: <code>data_folder</code> اختیاری است و نام پوشه داده‌های باینری است که شامل داده‌های باینری مربوطه است. نام فایل داده‌های باینری مربوطه درون آن پوشه، AACID ضبط است. هر شیء JSON باید شامل فیلدهای زیر در سطح بالا باشد: <strong>aacid</strong>، <strong>metadata</strong>، <strong>data_folder</strong> (اختیاری). هیچ فیلد دیگری مجاز نیست. نام فایل باید یک محدوده AACID باشد، که با <code style="color: red">annas_archive_meta__</code> پیشوند شده و با <code>.jsonl.zstd</code> دنبال می‌شود. به عنوان مثال، یکی از انتشارهای ما به نام<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code> است. همانطور که توسط پسوند فایل نشان داده شده است، نوع فایل <a %(jsonlines)s>JSON Lines</a> فشرده شده با <a %(zstd)s>Zstandard</a> است. <code>metadata</code> metadata دلخواه است، بر اساس معنای مجموعه. باید درون مجموعه به صورت معنایی سازگار باشد. پیشوند <code style="color: red">annas_archive_meta__</code> ممکن است به نام مؤسسه شما تطبیق داده شود، مثلاً <code style="color: red">my_institute_meta__</code>. <strong>مجموعه‌های "ضبط‌ها" و "فایل‌ها".</strong> به طور معمول، انتشار "ضبط‌ها" و "فایل‌ها" به عنوان مجموعه‌های مختلف راحت است، به طوری که می‌توانند در زمان‌بندی‌های مختلف منتشر شوند، مثلاً بر اساس نرخ‌های اسکرپینگ. یک "ضبط" مجموعه‌ای است که فقط شامل metadata است و اطلاعاتی مانند عناوین کتاب، نویسندگان، ISBNها و غیره را شامل می‌شود، در حالی که "فایل‌ها" مجموعه‌هایی هستند که شامل فایل‌های واقعی (pdf، epub) هستند. در نهایت، ما به یک استاندارد نسبتاً ساده رسیدیم. این استاندارد نسبتاً آزاد، غیر هنجاری و در حال پیشرفت است. <strong>تورنت‌ها.</strong> فایل‌های metadata و پوشه‌های داده باینری ممکن است در تورنت‌ها بسته‌بندی شوند، با یک تورنت برای هر فایل metadata، یا یک تورنت برای هر پوشه داده باینری. تورنت‌ها باید نام فایل/دایرکتوری اصلی به علاوه یک پسوند <code>.torrent</code> به عنوان نام فایل خود داشته باشند. <a %(wikipedia_annas_archive)s>آرشیو آنا</a> به مراتب بزرگترین کتابخانه سایه‌ای جهان شده است و تنها کتابخانه سایه‌ای در مقیاس خود است که کاملاً منبع باز و داده باز است. در زیر جدولی از صفحه مجموعه داده‌های ما (کمی تغییر یافته) آمده است: ما این کار را به سه روش انجام دادیم: آینه‌سازی کتابخانه‌های سایه‌ای داده باز موجود (مانند Sci-Hub و Library Genesis). کمک به کتابخانه‌های سایه‌ای که می‌خواهند بازتر باشند، اما زمان یا منابع لازم برای این کار را نداشتند (مانند مجموعه کمیک‌های Libgen). خراش دادن کتابخانه‌هایی که نمی‌خواهند به صورت عمده به اشتراک بگذارند (مانند Z-Library). برای (۲) و (۳) ما اکنون مجموعه قابل توجهی از تورنت‌ها را خودمان مدیریت می‌کنیم (صدها ترابایت). تا کنون ما به این مجموعه‌ها به عنوان یک‌بار مصرف‌ها نزدیک شده‌ایم، به این معنی که زیرساخت و سازماندهی داده‌های خاص برای هر مجموعه. این امر به هر انتشار بار اضافی قابل توجهی اضافه می‌کند و انجام انتشارهای افزایشی بیشتر را به‌ویژه دشوار می‌سازد. به همین دلیل تصمیم گرفتیم انتشارهای خود را استاندارد کنیم. این یک پست وبلاگ فنی است که در آن استاندارد خود را معرفی می‌کنیم: <strong>کانتینرهای آرشیو آنا</strong>. کانتینرهای آرشیو آنا (AAC): استانداردسازی انتشارها از بزرگترین کتابخانه سایه‌ای جهان آرشیو آنا به بزرگترین کتابخانه سایه‌ای جهان تبدیل شده است و ما را ملزم به استانداردسازی انتشارهایمان کرده است. بیش از 300 گیگابایت از جلد کتاب‌ها منتشر شد در نهایت، خوشحالیم که یک انتشار کوچک را اعلام کنیم. در همکاری با افرادی که شاخه Libgen.rs را اداره می‌کنند، تمام جلدهای کتاب آن‌ها را از طریق تورنت و IPFS به اشتراک می‌گذاریم. این بار مشاهده جلدها را بین ماشین‌های بیشتری توزیع می‌کند و آن‌ها را بهتر حفظ می‌کند. در بسیاری از موارد (اما نه همه)، جلدهای کتاب در خود فایل‌ها گنجانده شده‌اند، بنابراین این نوعی "داده مشتق شده" است. اما داشتن آن در IPFS همچنان برای عملیات روزانه هم آرشیو آنا و هم شاخه‌های مختلف Library Genesis بسیار مفید است. همانطور که معمولاً، می‌توانید این انتشار را در لینک کمکی کتابخانه دزدان دریایی پیدا کنید (ویرایش: به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> منتقل شد). ما اینجا به آن لینک نمی‌دهیم، اما می‌توانید به راحتی آن را پیدا کنید. امیدواریم اکنون که جایگزین مناسبی برای Z-Library داریم، بتوانیم کمی از سرعت خود بکاهیم. این حجم کار به‌ویژه پایدار نیست. اگر علاقه‌مند به کمک در برنامه‌نویسی، عملیات سرور یا کارهای حفظ و نگهداری هستید، حتماً با ما تماس بگیرید. هنوز کارهای زیادی برای <a %(annas_archive)s>انجام دادن</a> باقی مانده است. از علاقه و حمایت شما سپاسگزاریم. تغییر به ElasticSearch برخی از جستجوها بسیار طولانی می‌شدند، تا جایی که تمام اتصالات باز را اشغال می‌کردند. به طور پیش‌فرض MySQL حداقل طول کلمه دارد، یا شاخص شما می‌تواند واقعاً بزرگ شود. گزارش شده بود که مردم نمی‌توانند برای "Ben Hur" جستجو کنند. جستجو فقط زمانی نسبتاً سریع بود که به طور کامل در حافظه بارگذاری شده بود، که نیاز داشتیم یک ماشین گران‌تر برای اجرای آن بگیریم، به علاوه برخی دستورات برای پیش‌بارگذاری شاخص در شروع. ما نمی‌توانستیم به راحتی آن را برای ساخت ویژگی‌های جدید گسترش دهیم، مانند <a %(wikipedia_cjk_characters)s>توکن‌سازی بهتر برای زبان‌های بدون فاصله</a>، فیلتر/فیسیتینگ، مرتب‌سازی، پیشنهادات "آیا منظورتان این بود"، تکمیل خودکار و غیره. یکی از <a %(annas_archive)s>تیکت‌های</a> ما مجموعه‌ای از مشکلات با سیستم جستجوی ما بود. ما از جستجوی متن کامل MySQL استفاده کردیم، زیرا تمام داده‌های ما در MySQL بود. اما محدودیت‌هایی داشت: پس از صحبت با تعدادی از کارشناسان، به ElasticSearch رسیدیم. این کامل نبوده است (پیشنهادات "آیا منظورتان این بود" و ویژگی‌های تکمیل خودکار آن‌ها ضعیف است)، اما به طور کلی برای جستجو بهتر از MySQL بوده است. ما هنوز <a %(youtube)s>خیلی مشتاق</a> استفاده از آن برای داده‌های حیاتی نیستیم (اگرچه آن‌ها پیشرفت زیادی کرده‌اند)، اما به طور کلی از این تغییر راضی هستیم. فعلاً، جستجوی بسیار سریع‌تر، پشتیبانی بهتر از زبان، مرتب‌سازی بهتر بر اساس مرتبط بودن، گزینه‌های مرتب‌سازی مختلف و فیلتر بر اساس زبان/نوع کتاب/نوع فایل را پیاده‌سازی کرده‌ایم. اگر کنجکاوید که چگونه کار می‌کند، <a %(annas_archive_l140)s>نگاهی</a> <a %(annas_archive_l1115)s>بیندازید</a>. این نسبتاً قابل دسترسی است، اگرچه می‌تواند از نظرات بیشتری استفاده کند… آرشیو آنا کاملاً متن‌باز است ما معتقدیم که اطلاعات باید رایگان باشد و کدهای ما نیز از این قاعده مستثنی نیستند. ما تمام کدهای خود را در نسخه خصوصی Gitlab خود منتشر کرده‌ایم: <a %(annas_archive)s>نرم‌افزار آنا</a>. ما همچنین از ردیاب مشکلات برای سازماندهی کار خود استفاده می‌کنیم. اگر می‌خواهید با توسعه ما درگیر شوید، اینجا مکان خوبی برای شروع است. برای اینکه شما را با چیزهایی که روی آن کار می‌کنیم آشنا کنیم، به کار اخیر ما در بهبود عملکرد سمت کاربر نگاهی بیندازید. از آنجا که هنوز صفحه‌بندی را پیاده‌سازی نکرده‌ایم، اغلب صفحات جستجوی بسیار طولانی با 100-200 نتیجه برمی‌گرداندیم. نمی‌خواستیم نتایج جستجو را زودتر قطع کنیم، اما این به معنای کند شدن برخی دستگاه‌ها بود. برای این کار، یک ترفند کوچک پیاده‌سازی کردیم: بیشتر نتایج جستجو را در نظرات HTML قرار دادیم (<code><!-- --></code>) و سپس یک جاوااسکریپت کوچک نوشتیم که تشخیص می‌دهد چه زمانی یک نتیجه باید قابل مشاهده شود، در آن لحظه نظر را باز می‌کنیم: "مجازی‌سازی" DOM در 23 خط پیاده‌سازی شد، نیازی به کتابخانه‌های پیچیده نیست! این نوع کد سریع و عمل‌گرایانه‌ای است که وقتی زمان محدودی دارید و مشکلات واقعی که باید حل شوند، به آن می‌رسید. گزارش شده است که جستجوی ما اکنون بر روی دستگاه‌های کند به خوبی کار می‌کند! تلاش بزرگ دیگر ما خودکارسازی ساخت پایگاه داده بود. وقتی شروع کردیم، به طور تصادفی منابع مختلف را با هم ترکیب کردیم. اکنون می‌خواهیم آن‌ها را به‌روز نگه داریم، بنابراین مجموعه‌ای از اسکریپت‌ها نوشتیم تا metadata جدید را از دو شاخه Library Genesis دانلود کرده و آن‌ها را یکپارچه کنیم. هدف این است که نه تنها این برای آرشیو ما مفید باشد، بلکه کار را برای هر کسی که می‌خواهد با metadata کتابخانه سایه بازی کند، آسان کند. هدف این است که یک دفترچه Jupyter داشته باشیم که انواع metadata جالب را در اختیار داشته باشد، تا بتوانیم تحقیقات بیشتری مانند تعیین <a %(blog)s>درصد ISBNهایی که برای همیشه حفظ می‌شوند</a> انجام دهیم. در نهایت، سیستم اهدای خود را بازسازی کردیم. اکنون می‌توانید با استفاده از کارت اعتباری به طور مستقیم پول را به کیف پول‌های رمزنگاری ما واریز کنید، بدون اینکه واقعاً نیازی به دانستن چیزی درباره ارزهای دیجیتال داشته باشید. ما همچنان نظارت خواهیم کرد که این در عمل چقدر خوب کار می‌کند، اما این یک تغییر بزرگ است. با پایین آمدن Z-Library و دستگیری (ادعایی) بنیان‌گذاران آن، ما شبانه‌روز کار کرده‌ایم تا جایگزین خوبی با آرشیو آنا ارائه دهیم (ما اینجا لینک نمی‌دهیم، اما می‌توانید آن را گوگل کنید). در اینجا برخی از چیزهایی که اخیراً به دست آورده‌ایم آورده شده است. به‌روزرسانی آنا: آرشیو کاملاً متن‌باز، ElasticSearch، بیش از 300 گیگابایت از جلد کتاب‌ها ما شبانه‌روز کار کرده‌ایم تا جایگزین خوبی با آرشیو آنا ارائه دهیم. در اینجا برخی از چیزهایی که اخیراً به دست آورده‌ایم آورده شده است. تحلیل تکراری‌های معنایی (اسکن‌های مختلف از یک کتاب) به طور نظری می‌توانند فیلتر شوند، اما این کار دشوار است. وقتی به صورت دستی کمیک‌ها را بررسی کردیم، تعداد زیادی مثبت کاذب پیدا کردیم. برخی تکراری‌ها صرفاً بر اساس MD5 هستند که نسبتاً هدررفت است، اما حذف آن‌ها تنها حدود 1% in صرفه‌جویی به ما می‌دهد. در این مقیاس، این هنوز حدود 1 ترابایت است، اما همچنین، در این مقیاس 1 ترابایت واقعاً مهم نیست. ما ترجیح می‌دهیم در این فرآیند به طور تصادفی داده‌ها را از بین نبریم. ما مجموعه‌ای از داده‌های غیرکتابی پیدا کردیم، مانند فیلم‌هایی که بر اساس کتاب‌های کمیک ساخته شده‌اند. این نیز به نظر هدررفت می‌آید، زیرا این‌ها از طریق روش‌های دیگر به طور گسترده در دسترس هستند. با این حال، متوجه شدیم که نمی‌توانیم به سادگی فایل‌های فیلم را فیلتر کنیم، زیرا همچنین <em>کتاب‌های کمیک تعاملی</em> وجود دارند که بر روی کامپیوتر منتشر شده‌اند و کسی آن‌ها را ضبط کرده و به صورت فیلم ذخیره کرده است. در نهایت، هر چیزی که می‌توانستیم از مجموعه حذف کنیم تنها چند درصد صرفه‌جویی می‌کرد. سپس به یاد آوردیم که ما جمع‌کنندگان داده هستیم و افرادی که این را لینک کمکی می‌کنند نیز جمع‌کنندگان داده هستند، و بنابراین، "منظورتان از حذف چیست؟!" :) وقتی ۹۵ ترابایت به فضای ذخیره‌سازی شما ریخته می‌شود، سعی می‌کنید بفهمید که اصلاً چه چیزی در آنجا وجود دارد… ما برخی تحلیل‌ها انجام دادیم تا ببینیم آیا می‌توانیم اندازه را کمی کاهش دهیم، مثلاً با حذف تکراری‌ها. در اینجا برخی از یافته‌های ما آمده است: بنابراین، ما مجموعه کامل و بدون تغییر را به شما ارائه می‌دهیم. این مقدار زیادی داده است، اما امیدواریم که افراد کافی به آن اهمیت دهند تا آن را به اشتراک بگذارند. همکاری با توجه به اندازه‌اش، این مجموعه مدت‌ها در لیست آرزوهای ما بوده است، بنابراین پس از موفقیت ما در پشتیبان‌گیری از Z-Library، تمرکز خود را بر روی این مجموعه قرار دادیم. در ابتدا آن را به طور مستقیم استخراج کردیم، که چالشی بزرگ بود، زیرا سرور آن‌ها در بهترین وضعیت نبود. به این روش حدود ۱۵ ترابایت به دست آوردیم، اما این کار به کندی پیش می‌رفت. خوشبختانه، ما توانستیم با اپراتور کتابخانه تماس بگیریم، که موافقت کرد تمام داده‌ها را مستقیماً برای ما ارسال کند، که بسیار سریع‌تر بود. با این حال، بیش از نیم سال طول کشید تا تمام داده‌ها منتقل و پردازش شوند، و ما تقریباً همه آن‌ها را به دلیل خرابی دیسک از دست دادیم، که به معنای شروع دوباره بود. این تجربه ما را به این باور رسانده است که مهم است این داده‌ها را هر چه سریع‌تر منتشر کنیم، تا بتوان آن‌ها را به طور گسترده‌ای آینه کرد. ما فقط یک یا دو حادثه بدشانس از دست دادن این مجموعه برای همیشه فاصله داریم! مجموعه حرکت سریع به این معناست که مجموعه کمی نامنظم است… بیایید نگاهی بیندازیم. تصور کنید که ما یک سیستم فایل داریم (که در واقع آن را در تورنت‌ها تقسیم می‌کنیم): اولین دایرکتوری، <code>/repository</code>، بخش ساختارمندتر این مجموعه است. این دایرکتوری شامل دایرکتوری‌های به اصطلاح "هزار دایرکتوری" است: دایرکتوری‌هایی که هر کدام هزار فایل دارند و به صورت افزایشی در پایگاه داده شماره‌گذاری شده‌اند. دایرکتوری <code>0</code> شامل فایل‌هایی با comic_id 0–999 است و به همین ترتیب. این همان طرحی است که Library Genesis برای مجموعه‌های داستانی و غیر داستانی خود استفاده کرده است. ایده این است که هر "هزار دایرکتوری" به محض پر شدن به طور خودکار به یک تورنت تبدیل شود. با این حال، اپراتور Libgen.li هرگز برای این مجموعه تورنتی ایجاد نکرد، و بنابراین هزار دایرکتوری‌ها احتمالاً نامناسب شدند و جای خود را به "دایرکتوری‌های نامرتب" دادند. این‌ها <code>/comics0</code> تا <code>/comics4</code> هستند. همه آن‌ها ساختارهای دایرکتوری منحصر به فردی دارند که احتمالاً برای جمع‌آوری فایل‌ها منطقی بودند، اما اکنون برای ما چندان منطقی نیستند. خوشبختانه، metadata هنوز به طور مستقیم به همه این فایل‌ها اشاره می‌کند، بنابراین سازماندهی ذخیره‌سازی آن‌ها روی دیسک واقعاً مهم نیست! metadata به صورت یک پایگاه داده MySQL در دسترس است. این می‌تواند مستقیماً از وب‌سایت Libgen.li دانلود شود، اما ما آن را نیز در یک تورنت، همراه با جدول خودمان با تمام هش‌های MD5، در دسترس قرار خواهیم داد. <q>دکتر باربارا گوردون سعی می‌کند خود را در دنیای عادی کتابخانه گم کند…</q> شاخه‌های Libgen ابتدا، کمی پیش‌زمینه. ممکن است Library Genesis را به خاطر مجموعه کتاب‌های حماسی‌اش بشناسید. افراد کمتری می‌دانند که داوطلبان Library Genesis پروژه‌های دیگری نیز ایجاد کرده‌اند، مانند مجموعه‌ای بزرگ از مجلات و اسناد استاندارد، یک نسخه پشتیبان کامل از Sci-Hub (در همکاری با بنیان‌گذار Sci-Hub، الکساندرا الباکیان)، و در واقع، مجموعه‌ای عظیم از کمیک‌ها. در مقطعی، اپراتورهای مختلف آینه‌های Library Genesis راه‌های جداگانه‌ای را در پیش گرفتند که منجر به وضعیت فعلی شد که تعدادی شاخه مختلف وجود دارد که همگی هنوز نام Library Genesis را دارند. شاخه Libgen.li به طور منحصر به فرد این مجموعه کمیک‌ها را دارد، همچنین مجموعه‌ای بزرگ از مجلات (که ما نیز روی آن کار می‌کنیم). جمع‌آوری کمک مالی ما این داده‌ها را در چند بخش بزرگ منتشر می‌کنیم. اولین تورنت از <code>/comics0</code> است که ما آن را در یک فایل بزرگ 12 ترابایتی .tar قرار دادیم. این برای هارد دیسک و نرم‌افزار تورنت شما بهتر از هزاران فایل کوچک است. به عنوان بخشی از این انتشار، ما یک جمع‌آوری کمک مالی انجام می‌دهیم. ما به دنبال جمع‌آوری ۲۰,۰۰۰ دلار برای پوشش هزینه‌های عملیاتی و قراردادی این مجموعه هستیم، و همچنین برای امکان‌پذیر کردن پروژه‌های جاری و آینده. ما برخی <em>بزرگ</em> را در دست کار داریم. <em>من با کمک مالی خود از چه کسی حمایت می‌کنم؟</em> به طور خلاصه: ما در حال پشتیبان‌گیری از تمام دانش و فرهنگ بشری هستیم و آن را به راحتی در دسترس قرار می‌دهیم. تمام کد و داده‌های ما منبع باز هستند، ما یک پروژه کاملاً داوطلبانه هستیم و تاکنون ۱۲۵ ترابایت کتاب را ذخیره کرده‌ایم (علاوه بر تورنت‌های موجود Libgen و Scihub). در نهایت، ما در حال ساخت یک چرخه هستیم که افراد را تشویق و ترغیب می‌کند تا تمام کتاب‌های جهان را پیدا، اسکن و پشتیبان‌گیری کنند. ما در یک پست آینده درباره برنامه اصلی خود خواهیم نوشت. :) اگر برای عضویت ۱۲ ماهه "Amazing Archivist" ($780) کمک مالی کنید، می‌توانید <strong>“یک تورنت را به نام خود بگیرید”</strong>، به این معنی که ما نام کاربری یا پیام شما را در نام فایل یکی از تورنت‌ها قرار خواهیم داد! می‌توانید با رفتن به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> و کلیک بر روی دکمه "کمک مالی" کمک کنید. ما همچنین به دنبال داوطلبان بیشتری هستیم: مهندسان نرم‌افزار، محققان امنیتی، کارشناسان تجارت ناشناس و مترجمان. همچنین می‌توانید با ارائه خدمات میزبانی از ما حمایت کنید. و البته، لطفاً تورنت‌های ما را به اشتراک بگذارید! از همه کسانی که تاکنون به سخاوت از ما حمایت کرده‌اند، متشکریم! شما واقعاً تفاوت ایجاد می‌کنید. در اینجا تورنت‌هایی که تاکنون منتشر شده‌اند (ما هنوز در حال پردازش بقیه هستیم): تمام تورنت‌ها را می‌توانید در <a %(wikipedia_annas_archive)s>آرشیو آنا</a> تحت "Datasets" پیدا کنید (ما به طور مستقیم به آنجا لینک نمی‌دهیم، بنابراین لینک‌های این وبلاگ از Reddit، Twitter و غیره حذف نمی‌شوند). از آنجا، به وب‌سایت Tor بروید. <a %(news_ycombinator)s>در Hacker News بحث کنید</a> چه چیزی در پیش است؟ مجموعه‌ای از تورنت‌ها برای حفظ طولانی‌مدت عالی هستند، اما برای دسترسی روزمره چندان مناسب نیستند. ما با شرکای میزبانی برای قرار دادن تمام این داده‌ها بر روی وب کار خواهیم کرد (زیرا آرشیو آنا هیچ چیزی را به طور مستقیم میزبانی نمی‌کند). البته شما می‌توانید این لینک‌های دانلود را در آرشیو آنا پیدا کنید. ما همچنین از همه دعوت می‌کنیم تا با این داده‌ها کاری انجام دهند! به ما کمک کنید تا آن را بهتر تحلیل کنیم، تکراری‌ها را حذف کنیم، آن را بر روی IPFS قرار دهیم، مدل‌های هوش مصنوعی خود را با آن آموزش دهید و غیره. همه این‌ها متعلق به شماست و ما نمی‌توانیم صبر کنیم تا ببینیم با آن چه می‌کنید. در نهایت، همانطور که قبلاً گفته شد، ما هنوز برخی از انتشارهای بزرگ را در پیش داریم (اگر <em>کسی</em> بتواند <em>به طور تصادفی</em> یک دامپ از یک پایگاه داده <em>خاص</em> ACS4 برای ما ارسال کند، می‌دانید کجا ما را پیدا کنید...)، و همچنین در حال ساخت چرخه برای پشتیبان‌گیری از تمام کتاب‌های جهان هستیم. پس با ما همراه باشید، ما تازه شروع کرده‌ایم. - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) بزرگ‌ترین کتابخانه سایه‌ای کتاب‌های کمیک احتمالاً متعلق به یکی از شاخه‌های Library Genesis به نام Libgen.li است. مدیر این سایت موفق به جمع‌آوری مجموعه‌ای شگفت‌انگیز از کمیک‌ها با بیش از ۲ میلیون فایل شده است که مجموعاً بیش از ۹۵ ترابایت را شامل می‌شود. با این حال، برخلاف دیگر مجموعه‌های Library Genesis، این مجموعه به صورت عمده از طریق تورنت‌ها در دسترس نبود. شما فقط می‌توانستید این کمیک‌ها را به صورت فردی از طریق سرور شخصی کند او دسترسی داشته باشید — یک نقطه شکست واحد. تا امروز! در این پست، ما بیشتر درباره این مجموعه و جمع‌آوری کمک‌های مالی برای حمایت از این کار به شما خواهیم گفت. آرشیو آنا بزرگترین کتابخانه سایه‌ای کمیک‌های جهان (95 ترابایت) را پشتیبان‌گیری کرده است — شما می‌توانید به اشتراک‌گذاری آن کمک کنید بزرگترین کتابخانه سایه‌ای کمیک‌های جهان یک نقطه شکست داشت.. تا امروز. هشدار: این پست وبلاگ منسوخ شده است. ما تصمیم گرفته‌ایم که IPFS هنوز برای استفاده عمومی آماده نیست. ما همچنان به فایل‌ها در IPFS از آرشیو آنا لینک خواهیم داد، اما دیگر خودمان آن را میزبانی نمی‌کنیم و به دیگران نیز توصیه نمی‌کنیم که با استفاده از IPFS لینک کمکی کنند. لطفاً به صفحه تورنت‌های ما مراجعه کنید اگر می‌خواهید به حفظ مجموعه ما کمک کنید. قرار دادن ۵,۹۹۸,۷۹۴ کتاب در IPFS تکثیر نسخه‌ها برگردیم به سوال اصلی ما: چگونه می‌توانیم ادعا کنیم که مجموعه‌های خود را برای همیشه حفظ می‌کنیم؟ مشکل اصلی اینجاست که مجموعه ما با سرعت زیادی <a %(torrents_stats)s>در حال رشد</a> است، با استخراج و منبع باز کردن برخی مجموعه‌های عظیم (علاوه بر کار شگفت‌انگیزی که قبلاً توسط سایر کتابخانه‌های سایه‌ای داده باز مانند Sci-Hub و Library Genesis انجام شده است). این رشد در داده‌ها باعث می‌شود که مجموعه‌ها در سراسر جهان به سختی لینک کمکی شوند. ذخیره‌سازی داده‌ها گران است! اما ما خوش‌بین هستیم، به ویژه هنگامی که به سه روند زیر نگاه می‌کنیم. <a %(annas_archive_stats)s>اندازه کل</a> مجموعه‌های ما، در چند ماه گذشته، بر اساس تعداد بذرهای تورنت تقسیم‌بندی شده است. روند قیمت‌های HDD از منابع مختلف (برای مشاهده مطالعه کلیک کنید). <a %(critical_window_chinese)s>نسخه چینی 中文版</a>، بحث در <a %(reddit)s>Reddit</a>، <a %(news_ycombinator)s>Hacker News</a> ۱. ما میوه‌های در دسترس را چیده‌ایم این مورد مستقیماً از اولویت‌های ما که در بالا بحث شد، پیروی می‌کند. ما ترجیح می‌دهیم ابتدا بر روی آزادسازی مجموعه‌های بزرگ کار کنیم. اکنون که برخی از بزرگترین مجموعه‌های جهان را تأمین کرده‌ایم، انتظار داریم رشد ما بسیار کندتر باشد. هنوز یک دنباله طولانی از مجموعه‌های کوچکتر وجود دارد و کتاب‌های جدید هر روز اسکن یا منتشر می‌شوند، اما نرخ آن احتمالاً بسیار کندتر خواهد بود. ممکن است هنوز دو برابر یا حتی سه برابر شویم، اما در یک دوره زمانی طولانی‌تر. بهبودهای OCR. اولویت‌ها کد نرم‌افزارهای علمی و مهندسی نسخه‌های داستانی یا سرگرمی از همه موارد فوق داده‌های جغرافیایی (مثلاً نقشه‌ها، بررسی‌های زمین‌شناسی) داده‌های داخلی از شرکت‌ها یا دولت‌ها (نشت‌ها) داده‌های اندازه‌گیری مانند اندازه‌گیری‌های علمی، داده‌های اقتصادی، گزارش‌های شرکتی سوابق metadata به‌طور کلی (از غیر داستانی و داستانی؛ از رسانه‌های دیگر، هنر، افراد و غیره؛ شامل نقدها) کتاب‌های غیر داستانی مجلات غیر داستانی، روزنامه‌ها، راهنماها رونوشت‌های غیر داستانی از سخنرانی‌ها، مستندها، پادکست‌ها داده‌های ارگانیک مانند توالی‌های DNA، بذر گیاهان یا نمونه‌های میکروبی مقالات علمی، مجلات، گزارش‌ها وب‌سایت‌های علمی و مهندسی، بحث‌های آنلاین رونوشت‌های دادرسی‌های قانونی یا دادگاهی به‌طور منحصر به فردی در معرض خطر نابودی (مثلاً توسط جنگ، کاهش بودجه، دعاوی حقوقی یا آزار و اذیت سیاسی) نادر به‌طور منحصر به فردی کم‌توجه چرا ما این‌قدر به مقالات و کتاب‌ها اهمیت می‌دهیم؟ بیایید باور اساسی‌مان به حفظ و نگهداری را کنار بگذاریم — شاید در پست دیگری درباره آن بنویسیم. پس چرا به‌طور خاص مقالات و کتاب‌ها؟ پاسخ ساده است: <strong>تراکم اطلاعات</strong>. به ازای هر مگابایت ذخیره‌سازی، متن نوشته شده بیشترین اطلاعات را در میان همه رسانه‌ها ذخیره می‌کند. در حالی که ما به هر دو دانش و فرهنگ اهمیت می‌دهیم، اما به اولی بیشتر اهمیت می‌دهیم. به‌طور کلی، ما یک سلسله‌مراتب از تراکم اطلاعات و اهمیت حفظ و نگهداری پیدا می‌کنیم که به‌طور تقریبی به این شکل است: رتبه‌بندی در این فهرست تا حدی دلخواه است — چندین مورد با هم برابر هستند یا در تیم ما اختلاف نظر وجود دارد — و احتمالاً برخی دسته‌های مهم را فراموش کرده‌ایم. اما این تقریباً نحوه اولویت‌بندی ما است. برخی از این موارد برای ما بسیار متفاوت از دیگران هستند که نگران آن‌ها باشیم (یا قبلاً توسط مؤسسات دیگر مراقبت شده‌اند)، مانند داده‌های ارگانیک یا داده‌های جغرافیایی. اما بیشتر موارد در این فهرست واقعاً برای ما مهم هستند. یکی دیگر از عوامل بزرگ در اولویت‌بندی ما این است که چقدر یک اثر خاص در معرض خطر است. ما ترجیح می‌دهیم بر روی آثاری تمرکز کنیم که: در نهایت، ما به مقیاس اهمیت می‌دهیم. ما زمان و پول محدودی داریم، بنابراین ترجیح می‌دهیم یک ماه را صرف نجات ۱۰,۰۰۰ کتاب کنیم تا ۱,۰۰۰ کتاب — اگر به‌طور تقریبی به همان اندازه ارزشمند و در معرض خطر باشند. <em><q>آنچه از دست رفته نمی‌تواند بازیابی شود؛ اما بیایید آنچه باقی مانده را نجات دهیم: نه با گاوصندوق‌ها و قفل‌هایی که آن‌ها را از دید و استفاده عمومی دور می‌کنند و به زباله‌های زمان می‌سپارند، بلکه با چنین تکثیر نسخه‌هایی که آن‌ها را از دسترس حوادث دور نگه دارد.</q></em><br>— توماس جفرسون، ۱۷۹۱ کتابخانه‌های سایه کد می‌تواند در Github به صورت منبع باز باشد، اما Github به عنوان یک کل نمی‌تواند به راحتی لینک کمکی شود و بنابراین حفظ شود (اگرچه در این مورد خاص نسخه‌های به اندازه کافی توزیع شده از اکثر مخازن کد وجود دارد) رکوردهای metadata می‌توانند به صورت آزاد در وب‌سایت Worldcat مشاهده شوند، اما نمی‌توان آن‌ها را به صورت عمده دانلود کرد (تا زمانی که ما <a %(worldcat_scrape)s>آن‌ها را استخراج</a> کردیم) استفاده از Reddit رایگان است، اما اخیراً اقدامات سختگیرانه‌ای علیه استخراج داده‌ها، در پی آموزش LLM‌های داده‌محور، اعمال کرده است (بیشتر در این باره بعداً) سازمان‌های زیادی وجود دارند که مأموریت‌ها و اولویت‌های مشابهی دارند. در واقع، کتابخانه‌ها، آرشیوها، آزمایشگاه‌ها، موزه‌ها و مؤسسات دیگری وجود دارند که وظیفه حفظ و نگهداری از این نوع را دارند. بسیاری از آن‌ها به خوبی تأمین مالی می‌شوند، توسط دولت‌ها، افراد یا شرکت‌ها. اما آن‌ها یک نقطه کور بزرگ دارند: سیستم حقوقی. در اینجا نقش منحصر به فرد کتابخانه‌های سایه‌ای و دلیل وجود آرشیو آنا نهفته است. ما می‌توانیم کارهایی انجام دهیم که سایر مؤسسات اجازه انجام آن را ندارند. اکنون، این‌طور نیست (اغلب) که ما می‌توانیم موادی را که در جای دیگری غیرقانونی است، آرشیو کنیم. نه، در بسیاری از مکان‌ها قانونی است که آرشیوی با هر کتاب، مقاله، مجله و غیره بسازیم. اما آنچه که آرشیوهای قانونی اغلب فاقد آن هستند، <strong>پایداری و طول عمر</strong> است. کتاب‌هایی وجود دارند که تنها یک نسخه از آن‌ها در یک کتابخانه فیزیکی در جایی وجود دارد. رکوردهای metadata وجود دارند که توسط یک شرکت واحد محافظت می‌شوند. روزنامه‌هایی وجود دارند که تنها بر روی میکروفیلم در یک آرشیو حفظ شده‌اند. کتابخانه‌ها ممکن است با کاهش بودجه مواجه شوند، شرکت‌ها ممکن است ورشکسته شوند، آرشیوها ممکن است بمباران و به خاکستر تبدیل شوند. این یک فرضیه نیست — این اتفاق همیشه می‌افتد. کاری که ما می‌توانیم به طور منحصر به فرد در آرشیو آنا انجام دهیم، ذخیره بسیاری از نسخه‌های آثار در مقیاس بزرگ است. ما می‌توانیم مقالات، کتاب‌ها، مجلات و بیشتر را جمع‌آوری کرده و به صورت عمده توزیع کنیم. در حال حاضر این کار را از طریق تورنت انجام می‌دهیم، اما فناوری‌های دقیق مهم نیستند و با گذشت زمان تغییر خواهند کرد. بخش مهم این است که نسخه‌های زیادی در سراسر جهان توزیع شوند. این نقل قول از بیش از ۲۰۰ سال پیش هنوز هم درست است: یادداشتی سریع درباره دامنه عمومی. از آنجا که آرشیو آنا به طور منحصر به فرد بر فعالیت‌هایی تمرکز دارد که در بسیاری از نقاط جهان غیرقانونی است، ما به مجموعه‌های به طور گسترده در دسترس، مانند کتاب‌های دامنه عمومی، توجه نمی‌کنیم. نهادهای قانونی اغلب به خوبی از آن مراقبت می‌کنند. با این حال، ملاحظاتی وجود دارد که گاهی اوقات ما را وادار می‌کند بر روی مجموعه‌های عمومی کار کنیم: - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) ۲. هزینه‌های ذخیره‌سازی به طور نمایی کاهش می‌یابد ۳. بهبود در تراکم اطلاعات ما در حال حاضر کتاب‌ها را در قالب‌های خامی که به ما داده می‌شوند ذخیره می‌کنیم. البته، آنها فشرده شده‌اند، اما اغلب هنوز اسکن‌های بزرگ یا عکس‌هایی از صفحات هستند. تا کنون، تنها گزینه‌های کاهش اندازه کل مجموعه‌مان از طریق فشرده‌سازی بیشتر یا حذف تکرار بوده است. با این حال، برای صرفه‌جویی‌های قابل توجه، هر دو برای سلیقه ما بیش از حد از دست می‌دهند. فشرده‌سازی سنگین عکس‌ها می‌تواند متن را به سختی قابل خواندن کند. و حذف تکرار نیاز به اطمینان بالا از یکسان بودن کتاب‌ها دارد، که اغلب بسیار نادرست است، به خصوص اگر محتوا یکسان باشد اما اسکن‌ها در مواقع مختلف انجام شده باشند. همیشه یک گزینه سوم وجود داشته است، اما کیفیت آن به قدری ضعیف بوده که هرگز به آن فکر نکرده‌ایم: <strong>OCR، یا تشخیص نوری کاراکترها</strong>. این فرآیند تبدیل عکس‌ها به متن ساده است، با استفاده از هوش مصنوعی برای تشخیص کاراکترها در عکس‌ها. ابزارهایی برای این کار مدت‌هاست که وجود دارند و نسبتاً خوب بوده‌اند، اما "نسبتاً خوب" برای اهداف حفظ و نگهداری کافی نیست. با این حال، مدل‌های یادگیری عمیق چندوجهی اخیر پیشرفت‌های بسیار سریعی داشته‌اند، هرچند هنوز با هزینه‌های بالا. ما انتظار داریم که هم دقت و هم هزینه‌ها به طور چشمگیری در سال‌های آینده بهبود یابد، تا جایی که اعمال آن بر کل کتابخانه‌مان واقع‌بینانه شود. وقتی این اتفاق بیفتد، احتمالاً همچنان فایل‌های اصلی را حفظ خواهیم کرد، اما علاوه بر آن می‌توانیم نسخه‌ای بسیار کوچکتر از کتابخانه خود داشته باشیم که اکثر مردم بخواهند لینک کمکی کنند. نکته این است که متن خام خود حتی بهتر فشرده می‌شود و بسیار آسان‌تر برای حذف تکرار است، که به ما صرفه‌جویی بیشتری می‌دهد. به طور کلی، انتظار کاهش حداقل ۵-۱۰ برابری در اندازه کل فایل‌ها غیرواقعی نیست، شاید حتی بیشتر. حتی با یک کاهش محافظه‌کارانه ۵ برابری، ما به <strong>۱,۰۰۰ تا ۳,۰۰۰ دلار در ۱۰ سال نگاه می‌کنیم حتی اگر کتابخانه ما سه برابر شود</strong>. در زمان نوشتن، <a %(diskprices)s>قیمت دیسک‌ها</a> به ازای هر ترابایت حدود ۱۲ دلار برای دیسک‌های جدید، ۸ دلار برای دیسک‌های استفاده شده و ۴ دلار برای نوار است. اگر محافظه‌کارانه نگاه کنیم و فقط به دیسک‌های جدید توجه کنیم، این بدان معناست که ذخیره یک پتابایت حدود ۱۲,۰۰۰ دلار هزینه دارد. اگر فرض کنیم کتابخانه ما از ۹۰۰ ترابایت به ۲.۷ پتابایت سه برابر شود، این به معنای ۳۲,۴۰۰ دلار برای لینک کمکی کل کتابخانه ما خواهد بود. با افزودن هزینه برق، هزینه سایر سخت‌افزارها و غیره، بیایید آن را به ۴۰,۰۰۰ دلار گرد کنیم. یا با نوار بیشتر شبیه ۱۵,۰۰۰ تا ۲۰,۰۰۰ دلار. از یک طرف <strong> ۱۵,۰۰۰ تا ۴۰,۰۰۰ دلار برای مجموع تمام دانش بشری یک معامله است</strong>. از طرف دیگر، انتظار داشتن تعداد زیادی نسخه کامل کمی زیاد است، به خصوص اگر بخواهیم این افراد به اشتراک‌گذاری تورنت‌های خود برای بهره‌مندی دیگران ادامه دهند. این وضعیت امروز است. اما پیشرفت به جلو حرکت می‌کند: هزینه‌های هارد دیسک به ازای هر ترابایت در حدود یک سوم در ده سال گذشته کاهش یافته است و احتمالاً با سرعت مشابهی به کاهش ادامه خواهد داد. نوار نیز به نظر می‌رسد در مسیر مشابهی قرار دارد. قیمت‌های SSD حتی سریع‌تر در حال کاهش است و ممکن است تا پایان دهه از قیمت‌های HDD پیشی بگیرد. اگر این روند ادامه یابد، در ۱۰ سال آینده ممکن است تنها ۵,۰۰۰ تا ۱۳,۰۰۰ دلار برای لینک کمکی کل مجموعه‌مان (یک سوم) نیاز داشته باشیم، یا حتی کمتر اگر اندازه‌مان کمتر رشد کند. در حالی که هنوز مقدار زیادی پول است، این برای بسیاری از افراد قابل دستیابی خواهد بود. و ممکن است حتی بهتر باشد به دلیل نکته بعدی… در آرشیو آنا، اغلب از ما پرسیده می‌شود که چگونه می‌توانیم ادعا کنیم که مجموعه‌های خود را برای همیشه حفظ می‌کنیم، وقتی که اندازه کل آن‌ها در حال حاضر به 1 پتابایت (1000 ترابایت) نزدیک می‌شود و همچنان در حال رشد است. در این مقاله به فلسفه ما خواهیم پرداخت و خواهیم دید چرا دهه آینده برای مأموریت ما در حفظ دانش و فرهنگ بشریت بحرانی است. پنجره بحرانی اگر این پیش‌بینی‌ها دقیق باشند، ما <strong>فقط نیاز داریم چند سال صبر کنیم</strong> تا کل مجموعه‌مان به طور گسترده لینک کمکی شود. بنابراین، به گفته توماس جفرسون، "فراتر از دسترس حادثه قرار گیرد." متأسفانه، ظهور LLMها و آموزش‌های داده‌محور آنها، بسیاری از دارندگان حق‌تألیف را به حالت دفاعی برده است. حتی بیشتر از آنچه که قبلاً بودند. بسیاری از وب‌سایت‌ها در حال سخت‌تر کردن فرآیند جمع‌آوری و آرشیو هستند، دادخواست‌ها در حال پرواز هستند، و در همین حال کتابخانه‌ها و آرشیوهای فیزیکی همچنان نادیده گرفته می‌شوند. ما فقط می‌توانیم انتظار داشته باشیم که این روندها به بدتر شدن ادامه دهند و بسیاری از آثار قبل از ورود به حوزه عمومی از دست بروند. <strong>ما در آستانه یک انقلاب در حفظ و نگهداری هستیم، اما <q>از دست رفته‌ها قابل بازیابی نیستند.</q></strong> ما یک پنجره بحرانی حدود ۵ تا ۱۰ سال داریم که در آن هنوز نسبتاً گران است که یک کتابخانه سایه‌ای را اداره کنیم و لینک‌های کمکی زیادی در سراسر جهان ایجاد کنیم، و در این مدت دسترسی هنوز به طور کامل بسته نشده است. اگر بتوانیم این پنجره را پل بزنیم، در واقع دانش و فرهنگ بشری را برای همیشه حفظ کرده‌ایم. نباید بگذاریم این زمان به هدر برود. نباید بگذاریم این پنجره بحرانی بر ما بسته شود. برویم. پنجره بحرانی کتابخانه‌های سایه چگونه می‌توانیم ادعا کنیم که مجموعه‌های خود را برای همیشه حفظ می‌کنیم، وقتی که آن‌ها در حال حاضر به 1 پتابایت نزدیک می‌شوند؟ مجموعه اطلاعات بیشتری درباره مجموعه. <a %(duxiu)s>Duxiu</a> یک پایگاه داده عظیم از کتاب‌های اسکن شده است که توسط <a %(chaoxing)s>گروه کتابخانه دیجیتال سوپراستار</a> ایجاد شده است. بیشتر آنها کتاب‌های آکادمیک هستند که برای دسترسی دیجیتالی به دانشگاه‌ها و کتابخانه‌ها اسکن شده‌اند. برای مخاطبان انگلیسی‌زبان ما، <a %(library_princeton)s>پرینستون</a> و <a %(guides_lib_uw)s>دانشگاه واشنگتن</a> نمای کلی خوبی دارند. همچنین مقاله‌ای عالی وجود دارد که پس‌زمینه بیشتری ارائه می‌دهد: <a %(doi)s>“دیجیتالی کردن کتاب‌های چینی: مطالعه موردی موتور جستجوی سوپراستار دوکسیو”</a> (آن را در آرشیو آنا جستجو کنید). کتاب‌های Duxiu مدت‌هاست که در اینترنت چینی به صورت غیرقانونی توزیع شده‌اند. معمولاً آنها توسط فروشندگان با قیمتی کمتر از یک دلار فروخته می‌شوند. آنها معمولاً با استفاده از معادل چینی Google Drive توزیع می‌شوند که اغلب برای فضای ذخیره‌سازی بیشتر هک شده است. برخی جزئیات فنی را می‌توان <a %(github_duty_machine)s>اینجا</a> و <a %(github_821_github_io)s>اینجا</a> یافت. اگرچه کتاب‌ها به صورت نیمه‌عمومی توزیع شده‌اند، اما به دست آوردن آنها به صورت عمده بسیار دشوار است. ما این را در لیست کارهای خود قرار داده بودیم و چندین ماه کار تمام‌وقت برای آن اختصاص دادیم. با این حال، اخیراً یک داوطلب فوق‌العاده، شگفت‌انگیز و با استعداد با ما تماس گرفت و به ما گفت که همه این کارها را قبلاً انجام داده‌اند — با هزینه زیاد. آنها کل مجموعه را با ما به اشتراک گذاشتند، بدون اینکه چیزی در ازای آن انتظار داشته باشند، به جز تضمین حفظ طولانی‌مدت. واقعاً قابل توجه. آنها موافقت کردند که به این روش برای دریافت کمک در OCR مجموعه درخواست کنند. مجموعه شامل ۷,۵۴۳,۷۰۲ فایل است. این بیشتر از کتاب‌های غیرداستانی Library Genesis (حدود ۵.۳ میلیون) است. اندازه کل فایل‌ها در فرم فعلی حدود ۳۵۹ ترابایت (۳۲۶ تیبی‌بایت) است. ما به پیشنهادات و ایده‌های دیگر باز هستیم. فقط با ما تماس بگیرید. برای اطلاعات بیشتر درباره مجموعه‌های ما، تلاش‌های حفظ و چگونگی کمک شما، به آرشیو آنا مراجعه کنید. متشکریم! صفحات نمونه برای اثبات به ما که خط لوله خوبی دارید، در اینجا برخی صفحات نمونه برای شروع کار، از یک کتاب درباره ابررساناها آورده شده است. خط لوله شما باید به درستی با ریاضیات، جداول، نمودارها، پاورقی‌ها و غیره برخورد کند. صفحات پردازش شده خود را به ایمیل ما ارسال کنید. اگر خوب به نظر برسند، ما صفحات بیشتری را به صورت خصوصی برای شما ارسال خواهیم کرد و انتظار داریم که بتوانید به سرعت خط لوله خود را بر روی آنها اجرا کنید. هنگامی که ما راضی شدیم، می‌توانیم معامله‌ای انجام دهیم. - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>نسخه چینی 中文版</a>، <a %(news_ycombinator)s>بحث در Hacker News</a> این یک پست وبلاگ کوتاه است. ما به دنبال یک شرکت یا مؤسسه‌ای هستیم که به ما در OCR و استخراج متن برای مجموعه عظیمی که به دست آورده‌ایم کمک کند، در ازای دسترسی انحصاری اولیه. پس از دوره تحریم، ما البته کل مجموعه را منتشر خواهیم کرد. متن آکادمیک با کیفیت بالا برای آموزش LLMها بسیار مفید است. در حالی که مجموعه ما چینی است، این باید حتی برای آموزش LLMهای انگلیسی نیز مفید باشد: مدل‌ها به نظر می‌رسد مفاهیم و دانش را بدون توجه به زبان منبع رمزگذاری می‌کنند. برای این کار، متن باید از اسکن‌ها استخراج شود. آرشیو آنا چه چیزی از این کار به دست می‌آورد؟ جستجوی کامل متن کتاب‌ها برای کاربرانش. زیرا اهداف ما با اهداف توسعه‌دهندگان LLM همسو است، ما به دنبال یک همکار هستیم. ما مایل به ارائه <strong>دسترسی انحصاری اولیه به این مجموعه به صورت عمده برای ۱ سال</strong> هستیم، اگر بتوانید OCR و استخراج متن مناسب انجام دهید. اگر مایل به اشتراک‌گذاری کل کد خط لوله خود با ما باشید، ما مایل به تمدید دوره تحریم مجموعه برای مدت طولانی‌تر هستیم. دسترسی انحصاری برای شرکت‌های LLM به بزرگترین مجموعه کتاب‌های غیر داستانی چینی در جهان <em><strong>خلاصه:</strong> آرشیو آنا مجموعه‌ای منحصر به فرد از ۷.۵ میلیون / ۳۵۰ ترابایت کتاب‌های غیرداستانی چینی را به دست آورده است — بزرگتر از Library Genesis. ما مایل به ارائه دسترسی انحصاری به یک شرکت LLM هستیم، در ازای OCR با کیفیت بالا و استخراج متن.</em> معماری سیستم فرض کنید که شرکت‌هایی پیدا کرده‌اید که مایل به میزبانی وب‌سایت شما بدون تعطیلی آن هستند - بیایید آن‌ها را "ارائه‌دهندگان دوستدار آزادی" بنامیم 😄. به زودی متوجه خواهید شد که میزبانی همه چیز با آن‌ها بسیار گران است، بنابراین ممکن است بخواهید برخی "ارائه‌دهندگان ارزان" پیدا کنید و میزبانی واقعی را در آنجا انجام دهید، و از طریق ارائه‌دهندگان دوستدار آزادی پروکسی کنید. اگر این کار را درست انجام دهید، ارائه‌دهندگان ارزان هرگز نخواهند دانست که چه چیزی را میزبانی می‌کنید و هرگز شکایتی دریافت نخواهند کرد. با همه این ارائه‌دهندگان، خطر تعطیلی شما همچنان وجود دارد، بنابراین به تکرارپذیری نیاز دارید. ما به این نیاز در تمام سطوح پشته خود داریم. یک شرکت نسبتاً دوستدار آزادی که خود را در موقعیت جالبی قرار داده است، Cloudflare است. آن‌ها <a %(blog_cloudflare)s>ادعا کرده‌اند</a> که یک ارائه‌دهنده میزبانی نیستند، بلکه یک خدمات عمومی مانند یک ISP هستند. بنابراین آن‌ها مشمول DMCA یا درخواست‌های تعطیلی دیگر نیستند و هر درخواست را به ارائه‌دهنده میزبانی واقعی شما ارسال می‌کنند. آن‌ها حتی تا حدی به دادگاه رفته‌اند تا از این ساختار محافظت کنند. بنابراین می‌توانیم از آن‌ها به عنوان یک لایه دیگر از کش و حفاظت استفاده کنیم. Cloudflare پرداخت‌های ناشناس را قبول نمی‌کند، بنابراین ما فقط می‌توانیم از طرح رایگان آن‌ها استفاده کنیم. این به این معنی است که نمی‌توانیم از ویژگی‌های تعادل بار یا انتقال آن‌ها استفاده کنیم. بنابراین ما <a %(annas_archive_l255)s>این را خودمان در سطح دامنه پیاده‌سازی کردیم</a>. در بارگذاری صفحه، مرورگر بررسی می‌کند که آیا دامنه فعلی هنوز در دسترس است و اگر نه، همه URLها را به یک دامنه دیگر بازنویسی می‌کند. از آنجا که Cloudflare بسیاری از صفحات را کش می‌کند، این به این معنی است که کاربر می‌تواند به دامنه اصلی ما برسد، حتی اگر سرور پروکسی پایین باشد، و سپس در کلیک بعدی به یک دامنه دیگر منتقل شود. ما همچنان نگرانی‌های عملیاتی عادی مانند نظارت بر سلامت سرور، ثبت خطاهای بک‌اند و فرانت‌اند و غیره را داریم. معماری انتقال ما امکان پایداری بیشتری در این زمینه فراهم می‌کند، به عنوان مثال با اجرای یک مجموعه کاملاً متفاوت از سرورها در یکی از دامنه‌ها. ما حتی می‌توانیم نسخه‌های قدیمی‌تر کد و داده‌ها را در این دامنه جداگانه اجرا کنیم، در صورتی که یک باگ بحرانی در نسخه اصلی نادیده گرفته شود. ما همچنین می‌توانیم در برابر تغییر نظر Cloudflare از ما محافظت کنیم، با حذف آن از یکی از دامنه‌ها، مانند این دامنه جداگانه. ترکیب‌های مختلفی از این ایده‌ها ممکن است. نتیجه‌گیری تجربه جالبی بوده است که یاد بگیریم چگونه یک موتور جستجوی کتابخانه سایه‌ای قوی و مقاوم راه‌اندازی کنیم. جزئیات بیشتری برای به اشتراک گذاشتن در پست‌های بعدی وجود دارد، بنابراین به من بگویید که دوست دارید درباره چه چیزی بیشتر بدانید! همانطور که همیشه، ما به دنبال کمک‌های مالی برای حمایت از این کار هستیم، بنابراین حتماً صفحه کمک مالی در آرشیو آنا را بررسی کنید. ما همچنین به دنبال انواع دیگر حمایت‌ها هستیم، مانند کمک‌های مالی، حامیان بلندمدت، ارائه‌دهندگان پرداخت پرخطر، شاید حتی تبلیغات (با سلیقه!). و اگر می‌خواهید زمان و مهارت‌های خود را اهدا کنید، ما همیشه به دنبال توسعه‌دهندگان، مترجمان و غیره هستیم. از علاقه و حمایت شما متشکریم. توکن‌های نوآوری بیایید با تکنولوژی خود شروع کنیم. این تکنولوژی عمداً کسل‌کننده است. ما از Flask، MariaDB و ElasticSearch استفاده می‌کنیم. همین. جستجو عمدتاً یک مسئله حل‌شده است و قصد نداریم آن را دوباره اختراع کنیم. علاوه بر این، باید توکن‌های <a %(mcfunley)s>نوآوری</a> خود را برای چیز دیگری خرج کنیم: جلوگیری از تعطیلی توسط مقامات. پس دقیقاً چقدر آرشیو آنا قانونی یا غیرقانونی است؟ این بیشتر به حوزه قضایی قانونی بستگی دارد. بیشتر کشورها به نوعی از حق تکثیر اعتقاد دارند، به این معنی که به افراد یا شرکت‌ها انحصار خاصی بر روی انواع خاصی از آثار برای مدت زمان معینی اختصاص داده می‌شود. به عنوان یک نکته جانبی، در آرشیو آنا معتقدیم که در حالی که برخی مزایا وجود دارد، به طور کلی حق تکثیر برای جامعه منفی است - اما این داستانی برای زمان دیگری است. این انحصار خاص بر روی آثار خاص به این معنی است که برای هر کسی خارج از این انحصار غیرقانونی است که آن آثار را به طور مستقیم توزیع کند - از جمله ما. اما آرشیو آنا یک موتور جستجو است که آن آثار را به طور مستقیم توزیع نمی‌کند (حداقل نه در وب‌سایت clearnet ما)، بنابراین باید مشکلی نداشته باشیم، درست است؟ نه دقیقاً. در بسیاری از حوزه‌های قضایی، نه تنها توزیع آثار دارای حق تکثیر غیرقانونی است، بلکه لینک دادن به مکان‌هایی که این کار را انجام می‌دهند نیز غیرقانونی است. یک مثال کلاسیک از این قانون DMCA ایالات متحده است. این سخت‌ترین انتهای طیف است. در انتهای دیگر طیف، به طور نظری می‌توان کشورهایی را داشت که هیچ قانون حق تکثیری ندارند، اما این‌ها واقعاً وجود ندارند. تقریباً هر کشوری نوعی از قانون حق تکثیر را در کتاب‌های خود دارد. اجرای آن داستان دیگری است. بسیاری از کشورها با دولت‌هایی وجود دارند که اهمیتی به اجرای قانون حق تکثیر نمی‌دهند. همچنین کشورهایی بین این دو انتها وجود دارند که توزیع آثار دارای حق تکثیر را ممنوع می‌کنند، اما لینک دادن به چنین آثاری را ممنوع نمی‌کنند. یکی دیگر از ملاحظات در سطح شرکت است. اگر شرکتی در حوزه قضایی فعالیت کند که به حق تکثیر اهمیتی نمی‌دهد، اما خود شرکت مایل به پذیرش هیچ ریسکی نیست، ممکن است به محض اینکه کسی از آن شکایت کند، وب‌سایت شما را تعطیل کند. در نهایت، یک ملاحظه بزرگ پرداخت‌ها است. از آنجا که ما باید ناشناس بمانیم، نمی‌توانیم از روش‌های پرداخت سنتی استفاده کنیم. این ما را با ارزهای دیجیتال تنها می‌گذارد، و تنها تعداد کمی از شرکت‌ها از آن‌ها پشتیبانی می‌کنند (کارت‌های بدهی مجازی که با ارز دیجیتال پرداخت می‌شوند وجود دارند، اما اغلب پذیرفته نمی‌شوند). - آنا و تیم (<a %(reddit)s>Reddit</a>، <a %(t_me)s>Telegram</a>) من <a %(wikipedia_annas_archive)s>آرشیو آنا</a> را اداره می‌کنم، بزرگترین موتور جستجوی غیرانتفاعی متن‌باز جهان برای <a %(wikipedia_shadow_library)s>کتابخانه‌های سایه</a>، مانند Sci-Hub، Library Genesis و Z-Library. هدف ما این است که دانش و فرهنگ را به راحتی در دسترس قرار دهیم و در نهایت جامعه‌ای از افرادی بسازیم که با هم <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>تمام کتاب‌های جهان</a> را آرشیو و حفظ کنند. در این مقاله نشان خواهم داد که چگونه این وب‌سایت را اجرا می‌کنیم و چالش‌های منحصر به فردی که با راه‌اندازی یک وب‌سایت با وضعیت قانونی مشکوک همراه است، زیرا هیچ "AWS برای خیریه‌های سایه‌ای" وجود ندارد. <em>همچنین مقاله خواهر <a %(blog_how_to_become_a_pirate_archivist)s>چگونه یک آرشیویست دزد دریایی شویم</a> را بررسی کنید.</em> چگونه یک کتابخانه سایه‌ای را اداره کنیم: عملیات در آرشیو آنا هیچ <q>AWS برای خیریه‌های سایه‌ای وجود ندارد،</q> پس چگونه آرشیو آنا را اجرا می‌کنیم؟ ابزارها سرور برنامه: Flask، MariaDB، ElasticSearch، Docker. توسعه: Gitlab، Weblate، Zulip. مدیریت سرور: Ansible، Checkmk، UFW. میزبانی استاتیک Onion: Tor، Nginx. سرور پروکسی: Varnish. بیایید نگاهی بیندازیم به ابزارهایی که برای انجام همه این کارها استفاده می‌کنیم. این ابزارها به مرور زمان با مواجهه با مشکلات جدید و یافتن راه‌حل‌های جدید تکامل می‌یابند. برخی تصمیماتی وجود دارد که ما بارها و بارها در مورد آن‌ها تجدید نظر کرده‌ایم. یکی از آن‌ها ارتباط بین سرورها است: ما قبلاً از Wireguard برای این کار استفاده می‌کردیم، اما متوجه شدیم که گاهی اوقات هیچ داده‌ای را منتقل نمی‌کند یا فقط داده‌ها را در یک جهت منتقل می‌کند. این اتفاق با چندین تنظیمات مختلف Wireguard که امتحان کردیم، مانند <a %(github_costela_wesher)s>wesher</a> و <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a> رخ داد. همچنین تلاش کردیم پورت‌ها را از طریق SSH تونل کنیم، با استفاده از autossh و sshuttle، اما با <a %(github_sshuttle)s>مشکلاتی مواجه شدیم</a> (اگرچه هنوز برای من روشن نیست که آیا autossh از مشکلات TCP-over-TCP رنج می‌برد یا نه — فقط به نظر می‌رسد که یک راه‌حل ناکارآمد است اما شاید در واقع خوب باشد؟). در عوض، ما به اتصالات مستقیم بین سرورها بازگشتیم و با استفاده از فیلتر IP با UFW پنهان کردیم که یک سرور در ارائه‌دهندگان ارزان قیمت در حال اجرا است. این کار معایبی دارد که Docker با UFW خوب کار نمی‌کند، مگر اینکه از <code>network_mode: "host"</code> استفاده کنید. همه این‌ها کمی بیشتر مستعد خطا است، زیرا با یک پیکربندی نادرست کوچک، سرور خود را در معرض اینترنت قرار می‌دهید. شاید باید به autossh برگردیم — بازخورد در اینجا بسیار خوشایند خواهد بود. ما همچنین در مورد Varnish در مقابل Nginx تجدید نظر کرده‌ایم. در حال حاضر Varnish را دوست داریم، اما دارای ویژگی‌ها و لبه‌های خشن خود است. همین امر در مورد Checkmk نیز صدق می‌کند: ما آن را دوست نداریم، اما فعلاً کار می‌کند. Weblate خوب بوده اما فوق‌العاده نیست — گاهی اوقات می‌ترسم که داده‌هایم را از دست بدهد هر زمان که سعی می‌کنم آن را با مخزن git خود همگام‌سازی کنم. Flask به طور کلی خوب بوده، اما دارای برخی ویژگی‌های عجیب است که زمان زیادی برای رفع اشکال آن‌ها صرف شده است، مانند پیکربندی دامنه‌های سفارشی یا مشکلات با یکپارچه‌سازی SqlAlchemy آن. تا کنون سایر ابزارها عالی بوده‌اند: ما هیچ شکایت جدی در مورد MariaDB، ElasticSearch، Gitlab، Zulip، Docker و Tor نداریم. همه این‌ها برخی مشکلات داشته‌اند، اما هیچ‌کدام به طور جدی یا وقت‌گیر نبوده‌اند. جامعه اولین چالش ممکن است شما را شگفت‌زده کند. این یک مشکل فنی یا قانونی نیست. این یک مشکل روانی است: انجام این کار در سایه‌ها می‌تواند به شدت تنهایی باشد. بسته به آنچه که قصد دارید انجام دهید و مدل تهدید شما، ممکن است مجبور باشید بسیار محتاط باشید. در یک سوی طیف، افرادی مانند الکساندرا الباکیان، بنیان‌گذار Sci-Hub، داریم که بسیار باز درباره فعالیت‌های خود صحبت می‌کند. اما او در معرض خطر بالای دستگیری است اگر در این نقطه به یک کشور غربی سفر کند و ممکن است با دهه‌ها زندان مواجه شود. آیا این خطری است که شما مایل به پذیرش آن هستید؟ ما در سوی دیگر طیف هستیم؛ بسیار محتاطیم که هیچ ردی از خود به جا نگذاریم و امنیت عملیاتی قوی داشته باشیم. * همان‌طور که در HN توسط "ynno" ذکر شده است، الکساندرا در ابتدا نمی‌خواست شناخته شود: "سرورهای او به گونه‌ای تنظیم شده بودند که پیام‌های خطای دقیق از PHP ارسال کنند، از جمله مسیر کامل فایل منبع خطا، که تحت دایرکتوری /home/<USER>" بنابراین، از نام‌های کاربری تصادفی در کامپیوترهایی که برای این کار استفاده می‌کنید، استفاده کنید، در صورتی که چیزی را اشتباه پیکربندی کنید. با این حال، این پنهان‌کاری با هزینه روانی همراه است. بیشتر مردم دوست دارند برای کاری که انجام می‌دهند شناخته شوند، و با این حال شما نمی‌توانید در زندگی واقعی هیچ اعتباری برای این کار بگیرید. حتی چیزهای ساده می‌توانند چالش‌برانگیز باشند، مانند دوستانی که از شما می‌پرسند که به چه کاری مشغول بوده‌اید (در یک نقطه "دستکاری با NAS / homelab من" قدیمی می‌شود). به همین دلیل است که پیدا کردن یک جامعه بسیار مهم است. می‌توانید با اعتماد به دوستان بسیار نزدیک خود، که می‌دانید می‌توانید به آن‌ها عمیقاً اعتماد کنید، از برخی امنیت‌های عملیاتی صرف‌نظر کنید. حتی در این صورت، مراقب باشید که چیزی را به صورت مکتوب قرار ندهید، در صورتی که مجبور شوند ایمیل‌های خود را به مقامات تحویل دهند یا اگر دستگاه‌های آن‌ها به روش دیگری به خطر بیفتد. بهتر از آن، پیدا کردن برخی دزدان دریایی هم‌فکر است. اگر دوستان نزدیک شما علاقه‌مند به پیوستن به شما هستند، عالی است! در غیر این صورت، ممکن است بتوانید دیگران را به صورت آنلاین پیدا کنید. متأسفانه این هنوز یک جامعه خاص است. تا کنون ما فقط تعداد کمی از دیگران را پیدا کرده‌ایم که در این فضا فعال هستند. مکان‌های شروع خوب به نظر می‌رسد انجمن‌های Library Genesis و r/DataHoarder باشند. تیم Archive نیز افرادی هم‌فکر دارد، اگرچه آن‌ها در چارچوب قانون عمل می‌کنند (حتی اگر در برخی مناطق خاکستری قانون باشند). صحنه‌های سنتی "warez" و دزدی دریایی نیز افرادی دارند که به روش‌های مشابه فکر می‌کنند. ما به ایده‌ها برای تقویت جامعه و کشف ایده‌ها باز هستیم. احساس راحتی کنید و به ما در توییتر یا ردیت پیام دهید. شاید بتوانیم نوعی انجمن یا گروه چت میزبانی کنیم. یکی از چالش‌ها این است که این می‌تواند به راحتی در پلتفرم‌های رایج سانسور شود، بنابراین باید خودمان آن را میزبانی کنیم. همچنین یک تعادل بین داشتن این بحث‌ها به صورت کاملاً عمومی (بیشتر تعامل بالقوه) در مقابل خصوصی کردن آن (اجازه ندادن به "اهداف" بالقوه که بدانند ما در حال جمع‌آوری اطلاعات از آن‌ها هستیم) وجود دارد. باید در مورد آن فکر کنیم. اگر به این موضوع علاقه‌مند هستید، به ما اطلاع دهید! نتیجه‌گیری امیدواریم این برای آرشیویست‌های دزدان دریایی تازه‌کار مفید باشد. ما از ورود شما به این دنیا هیجان‌زده‌ایم، بنابراین از تماس با ما دریغ نکنید. بیایید تا جایی که می‌توانیم دانش و فرهنگ جهان را حفظ کنیم و آن را به طور گسترده لینک کمکی کنیم. پروژه‌ها ۴. انتخاب داده‌ها اغلب می‌توانید از metadata برای تعیین یک زیرمجموعه منطقی از داده‌ها برای دانلود استفاده کنید. حتی اگر در نهایت بخواهید همه داده‌ها را دانلود کنید، می‌تواند مفید باشد که ابتدا مهم‌ترین آیتم‌ها را اولویت‌بندی کنید، در صورتی که شناسایی شوید و دفاع‌ها بهبود یابند، یا به دلیل اینکه نیاز به خرید دیسک‌های بیشتری دارید، یا به سادگی به دلیل اینکه قبل از اینکه بتوانید همه چیز را دانلود کنید، چیز دیگری در زندگی‌تان پیش می‌آید. به عنوان مثال، یک مجموعه ممکن است چندین نسخه از یک منبع اصلی (مانند یک کتاب یا فیلم) داشته باشد، که یکی به عنوان بهترین کیفیت علامت‌گذاری شده است. ذخیره این نسخه‌ها در ابتدا منطقی خواهد بود. ممکن است در نهایت بخواهید همه نسخه‌ها را ذخیره کنید، زیرا در برخی موارد ممکن است metadata به اشتباه برچسب‌گذاری شده باشد، یا ممکن است بین نسخه‌ها تعادل‌های ناشناخته‌ای وجود داشته باشد (برای مثال، "بهترین نسخه" ممکن است در بیشتر جنبه‌ها بهترین باشد اما در جنبه‌های دیگر بدتر باشد، مانند فیلمی که وضوح بالاتری دارد اما زیرنویس ندارد). همچنین می‌توانید در پایگاه داده metadata خود به دنبال چیزهای جالب بگردید. بزرگترین فایلی که میزبانی می‌شود چیست و چرا اینقدر بزرگ است؟ کوچکترین فایل چیست؟ آیا الگوهای جالب یا غیرمنتظره‌ای در مورد دسته‌بندی‌های خاص، زبان‌ها و غیره وجود دارد؟ آیا عناوین تکراری یا بسیار مشابه وجود دارد؟ آیا الگوهایی در زمان اضافه شدن داده‌ها وجود دارد، مانند یک روز که در آن بسیاری از فایل‌ها به یکباره اضافه شده‌اند؟ اغلب می‌توانید با نگاه کردن به مجموعه داده به روش‌های مختلف، چیزهای زیادی یاد بگیرید. در مورد ما، کتاب‌های Z-Library را با هش‌های md5 در Library Genesis تکراری‌زدایی کردیم و به این ترتیب زمان دانلود و فضای دیسک زیادی را صرفه‌جویی کردیم. این یک وضعیت کاملاً منحصر به فرد است. در بیشتر موارد، پایگاه‌های داده جامعی وجود ندارد که نشان دهد کدام فایل‌ها قبلاً به‌درستی توسط دزدان دریایی همکار حفظ شده‌اند. این خود یک فرصت بزرگ برای کسی است که در آنجا وجود دارد. داشتن یک نمای کلی به‌روز شده از چیزهایی مانند موسیقی و فیلم‌هایی که قبلاً به‌طور گسترده در وب‌سایت‌های تورنت بذر شده‌اند و بنابراین اولویت کمتری برای گنجاندن در آینه‌های دزدان دریایی دارند، عالی خواهد بود. ۶. توزیع شما داده‌ها را دارید، بنابراین احتمالاً اولین لینک کمکی دزدان دریایی جهان از هدف خود را در اختیار دارید. به بسیاری از جهات، سخت‌ترین بخش کار تمام شده است، اما پرخطرترین بخش هنوز پیش روی شماست. به هر حال، تا کنون شما به صورت مخفیانه عمل کرده‌اید؛ زیر رادار پرواز کرده‌اید. تنها کاری که باید انجام می‌دادید استفاده از یک VPN خوب در طول کار بود، پر نکردن جزئیات شخصی خود در هیچ فرمی (واضح است)، و شاید استفاده از یک جلسه مرورگر خاص (یا حتی یک کامپیوتر متفاوت). اکنون باید داده‌ها را توزیع کنید. در مورد ما، ابتدا می‌خواستیم کتاب‌ها را به Library Genesis برگردانیم، اما به سرعت با مشکلاتی در آن مواجه شدیم (دسته‌بندی داستانی در مقابل غیر داستانی). بنابراین تصمیم گرفتیم از توزیع با استفاده از تورنت‌های به سبک Library Genesis استفاده کنیم. اگر فرصت مشارکت در یک پروژه موجود را دارید، این می‌تواند زمان زیادی را برای شما صرفه‌جویی کند. با این حال، در حال حاضر لینک‌های کمکی دزدان دریایی به خوبی سازماندهی شده زیادی وجود ندارد. بنابراین فرض کنید تصمیم می‌گیرید خودتان تورنت‌ها را توزیع کنید. سعی کنید این فایل‌ها را کوچک نگه دارید، تا به راحتی بتوان آن‌ها را در وب‌سایت‌های دیگر لینک کمکی کرد. سپس باید خودتان تورنت‌ها را بذر کنید، در حالی که همچنان ناشناس باقی می‌مانید. می‌توانید از یک VPN (با یا بدون پورت فورواردینگ) استفاده کنید، یا با بیت‌کوین‌های تامبل شده برای یک Seedbox پرداخت کنید. اگر نمی‌دانید برخی از این اصطلاحات به چه معناست، باید مقداری مطالعه کنید، زیرا مهم است که درک کنید که در اینجا چه ریسک‌هایی وجود دارد. می‌توانید فایل‌های تورنت را خودتان در وب‌سایت‌های تورنت موجود میزبانی کنید. در مورد ما، تصمیم گرفتیم که واقعاً یک وب‌سایت میزبانی کنیم، زیرا می‌خواستیم فلسفه خود را به وضوح گسترش دهیم. می‌توانید این کار را به روش مشابه خودتان انجام دهید (ما از Njalla برای دامنه‌ها و میزبانی خود استفاده می‌کنیم، که با بیت‌کوین‌های تامبل شده پرداخت می‌شود)، اما همچنین می‌توانید با ما تماس بگیرید تا ما تورنت‌های شما را میزبانی کنیم. ما به دنبال ساخت یک فهرست جامع از لینک‌های کمکی دزدان دریایی در طول زمان هستیم، اگر این ایده مورد توجه قرار گیرد. در مورد انتخاب VPN، در این مورد قبلاً زیاد نوشته شده است، بنابراین ما فقط توصیه عمومی انتخاب بر اساس شهرت را تکرار می‌کنیم. سیاست‌های بدون لاگ واقعی که در دادگاه آزمایش شده‌اند و سابقه طولانی در حفاظت از حریم خصوصی دارند، به نظر ما کم‌ریسک‌ترین گزینه است. توجه داشته باشید که حتی وقتی همه چیز را درست انجام می‌دهید، هرگز نمی‌توانید به ریسک صفر برسید. برای مثال، هنگام بذر کردن تورنت‌های خود، یک بازیگر دولتی با انگیزه بالا احتمالاً می‌تواند به جریان‌های داده ورودی و خروجی برای سرورهای VPN نگاه کند و بفهمد که شما کی هستید. یا ممکن است به سادگی به نوعی اشتباه کنید. ما احتمالاً قبلاً اشتباه کرده‌ایم و دوباره خواهیم کرد. خوشبختانه، دولت‌ها آن‌قدرها به دزدی دریایی اهمیت نمی‌دهند. برای هر پروژه، یک تصمیم مهم این است که آیا آن را با همان هویت قبلی منتشر کنیم یا نه. اگر از همان نام استفاده کنید، اشتباهات امنیتی عملیاتی از پروژه‌های قبلی ممکن است به شما آسیب برساند. اما انتشار با نام‌های مختلف به این معناست که شما شهرتی پایدار ایجاد نمی‌کنید. ما تصمیم گرفتیم از ابتدا امنیت عملیاتی قوی داشته باشیم تا بتوانیم از همان هویت استفاده کنیم، اما اگر اشتباهی رخ دهد یا شرایط ایجاب کند، از انتشار با نامی متفاوت دریغ نخواهیم کرد. انتشار خبر می‌تواند دشوار باشد. همان‌طور که گفتیم، این هنوز یک جامعه خاص است. ما ابتدا در Reddit پست کردیم، اما واقعاً در Hacker News توجه جلب کردیم. فعلاً توصیه ما این است که آن را در چند مکان منتشر کنید و ببینید چه اتفاقی می‌افتد. و دوباره، با ما تماس بگیرید. ما دوست داریم خبر تلاش‌های بیشتر برای آرشیو دزدان دریایی را گسترش دهیم. ۱. انتخاب دامنه / فلسفه هیچ کمبودی از دانش و میراث فرهنگی برای نجات وجود ندارد، که می‌تواند طاقت‌فرسا باشد. به همین دلیل است که اغلب مفید است که لحظه‌ای وقت بگذارید و به این فکر کنید که چه کمکی می‌توانید بکنید. هر کس به روش متفاوتی به این موضوع فکر می‌کند، اما در اینجا چند سوال وجود دارد که می‌توانید از خود بپرسید: در مورد ما، ما به ویژه به حفظ بلندمدت علم اهمیت می‌دادیم. ما درباره Library Genesis می‌دانستیم و اینکه چگونه بارها و بارها با استفاده از تورنت‌ها به طور کامل لینک کمکی شده بود. ما آن ایده را دوست داشتیم. سپس یک روز، یکی از ما سعی کرد برخی کتاب‌های درسی علمی را در Library Genesis پیدا کند، اما نتوانست آن‌ها را پیدا کند، که باعث شد به کامل بودن آن شک کنیم. سپس آن کتاب‌های درسی را به صورت آنلاین جستجو کردیم و آن‌ها را در مکان‌های دیگر پیدا کردیم، که بذر پروژه ما را کاشت. حتی قبل از اینکه درباره Z-Library بدانیم، ایده جمع‌آوری دستی همه آن کتاب‌ها را نداشتیم، بلکه تمرکز بر لینک کمکی مجموعه‌های موجود و بازگرداندن آن‌ها به Library Genesis بود. چه مهارت‌هایی دارید که می‌توانید به نفع خود استفاده کنید؟ به عنوان مثال، اگر شما یک کارشناس امنیت آنلاین هستید، می‌توانید راه‌هایی برای شکست دادن بلوک‌های IP برای اهداف امن پیدا کنید. اگر در سازماندهی جوامع عالی هستید، شاید بتوانید برخی افراد را در اطراف یک هدف جمع کنید. اگرچه دانستن مقداری برنامه‌نویسی مفید است، حتی اگر فقط برای حفظ امنیت عملیاتی خوب در طول این فرآیند باشد. چه منطقه‌ای با اهرم بالا برای تمرکز وجود دارد؟ اگر قرار است X ساعت را صرف آرشیو دزدی دریایی کنید، چگونه می‌توانید بیشترین "بازده برای سرمایه‌گذاری" را به دست آورید؟ چه روش‌های منحصربه‌فردی برای فکر کردن به این موضوع دارید؟ ممکن است ایده‌ها یا رویکردهای جالبی داشته باشید که دیگران ممکن است از دست داده باشند. چقدر زمان برای این کار دارید؟ توصیه ما این است که با پروژه‌های کوچک شروع کنید و با آشنایی بیشتر با آن، پروژه‌های بزرگ‌تر انجام دهید، اما می‌تواند همه‌گیر شود. چرا به این موضوع علاقه‌مند هستید؟ به چه چیزی علاقه دارید؟ اگر بتوانیم گروهی از افراد را که همه نوع چیزهایی را که به طور خاص به آن‌ها اهمیت می‌دهند آرشیو کنند، جمع کنیم، این پوشش زیادی خواهد داشت! شما بیشتر از فرد متوسط درباره علاقه‌تان می‌دانید، مانند اینکه چه داده‌هایی مهم برای ذخیره‌سازی هستند، بهترین مجموعه‌ها و جوامع آنلاین کدامند و غیره. ۳. استخراج متادیتا تاریخ اضافه/تغییر یافته: تا بتوانید بعداً برگردید و فایل‌هایی را که قبلاً دانلود نکرده‌اید، دانلود کنید (اگرچه اغلب می‌توانید از شناسه یا هش برای این کار استفاده کنید). هش (md5، sha1): برای تأیید اینکه فایل را به‌درستی دانلود کرده‌اید. شناسه: می‌تواند یک شناسه داخلی باشد، اما شناسه‌هایی مانند ISBN یا DOI نیز مفید هستند. نام فایل / مکان توضیحات، دسته‌بندی، برچسب‌ها، نویسندگان، زبان و غیره. اندازه: برای محاسبه میزان فضای دیسکی که نیاز دارید. بیایید کمی فنی‌تر شویم. برای استخراج متادیتا از وب‌سایت‌ها، ما کارها را بسیار ساده نگه داشته‌ایم. از اسکریپت‌های پایتون، گاهی اوقات curl، و یک پایگاه داده MySQL برای ذخیره نتایج استفاده می‌کنیم. ما از هیچ نرم‌افزار استخراج پیشرفته‌ای که بتواند وب‌سایت‌های پیچیده را نقشه‌برداری کند استفاده نکرده‌ایم، زیرا تا کنون فقط نیاز داشتیم یک یا دو نوع صفحه را با شمارش از طریق شناسه‌ها و تجزیه HTML استخراج کنیم. اگر صفحات به راحتی قابل شمارش نباشند، ممکن است به یک خزنده مناسب نیاز داشته باشید که سعی کند همه صفحات را پیدا کند. قبل از اینکه شروع به اسکرپ کردن یک وب‌سایت کامل کنید، سعی کنید به صورت دستی برای مدتی این کار را انجام دهید. خودتان چندین صفحه را مرور کنید تا حس کنید که این کار چگونه انجام می‌شود. گاهی اوقات به این روش با بلاک‌های IP یا رفتارهای جالب دیگر مواجه خواهید شد. همین امر برای اسکرپ کردن داده‌ها نیز صدق می‌کند: قبل از اینکه به عمق این هدف بروید، مطمئن شوید که می‌توانید داده‌های آن را به طور مؤثر دانلود کنید. برای دور زدن محدودیت‌ها، چند راهکار وجود دارد که می‌توانید امتحان کنید. آیا آدرس‌های IP یا سرورهای دیگری وجود دارند که همان داده‌ها را میزبانی می‌کنند اما محدودیت‌های مشابهی ندارند؟ آیا نقاط پایانی API وجود دارند که محدودیت ندارند، در حالی که دیگران دارند؟ در چه نرخی از دانلود، IP شما مسدود می‌شود و برای چه مدت؟ یا آیا مسدود نمی‌شوید اما سرعت شما کاهش می‌یابد؟ اگر یک حساب کاربری ایجاد کنید، اوضاع چگونه تغییر می‌کند؟ آیا می‌توانید از HTTP/2 برای باز نگه داشتن اتصالات استفاده کنید و آیا این باعث افزایش نرخ درخواست صفحات می‌شود؟ آیا صفحاتی وجود دارند که چندین فایل را به‌طور همزمان فهرست می‌کنند و آیا اطلاعات فهرست شده در آنجا کافی است؟ مواردی که احتمالاً می‌خواهید ذخیره کنید شامل: ما معمولاً این کار را در دو مرحله انجام می‌دهیم. ابتدا فایل‌های خام HTML را دانلود می‌کنیم، معمولاً مستقیماً به MySQL (برای جلوگیری از تعداد زیادی فایل کوچک، که در زیر بیشتر در مورد آن صحبت می‌کنیم). سپس، در یک مرحله جداگانه، از طریق آن فایل‌های HTML عبور کرده و آن‌ها را به جداول واقعی MySQL تجزیه می‌کنیم. به این ترتیب نیازی نیست همه چیز را از ابتدا دوباره دانلود کنید اگر در کد تجزیه خود اشتباهی پیدا کردید، زیرا می‌توانید فایل‌های HTML را با کد جدید دوباره پردازش کنید. همچنین اغلب پردازش موازی این مرحله آسان‌تر است، بنابراین مقداری زمان صرفه‌جویی می‌شود (و می‌توانید کد پردازش را در حالی که اسکرپینگ در حال اجرا است بنویسید، به جای اینکه مجبور باشید هر دو مرحله را همزمان بنویسید). در نهایت، توجه داشته باشید که برای برخی اهداف، اسکرپ کردن metadata تمام چیزی است که وجود دارد. مجموعه‌های عظیمی از metadata وجود دارند که به درستی حفظ نشده‌اند. عنوان انتخاب دامنه / فلسفه: به طور تقریبی می‌خواهید روی چه چیزی تمرکز کنید و چرا؟ چه علاقه‌ها، مهارت‌ها و شرایط منحصربه‌فردی دارید که می‌توانید به نفع خود استفاده کنید؟ انتخاب هدف: کدام مجموعه خاص را لینک کمکی می‌کنید؟ استخراج metadata: فهرست‌بندی اطلاعات درباره فایل‌ها، بدون دانلود واقعی خود فایل‌ها (که اغلب بسیار بزرگ‌تر هستند). انتخاب داده: بر اساس متادیتا، محدود کردن اینکه کدام داده‌ها در حال حاضر برای آرشیو کردن مرتبط‌تر هستند. می‌تواند همه چیز باشد، اما اغلب راهی منطقی برای صرفه‌جویی در فضا و پهنای باند وجود دارد. استخراج داده: در واقع دریافت داده‌ها. توزیع: بسته‌بندی آن در تورنت‌ها، اعلام آن در جایی، و جلب مردم برای انتشار آن. ۵. اسکرپ کردن داده‌ها اکنون آماده‌اید که واقعاً داده‌ها را به صورت عمده دانلود کنید. همان‌طور که قبلاً ذکر شد، در این مرحله باید به صورت دستی تعدادی فایل دانلود کرده باشید تا رفتار و محدودیت‌های هدف را بهتر درک کنید. با این حال، هنوز هم شگفتی‌هایی در انتظار شما خواهد بود وقتی که واقعاً به دانلود تعداد زیادی فایل به صورت همزمان بپردازید. توصیه ما در اینجا عمدتاً این است که آن را ساده نگه دارید. با دانلود تعدادی فایل شروع کنید. می‌توانید از Python استفاده کنید و سپس به چندین رشته گسترش دهید. اما گاهی اوقات حتی ساده‌تر این است که فایل‌های Bash را مستقیماً از پایگاه داده تولید کنید و سپس چندین مورد از آن‌ها را در چندین پنجره ترمینال اجرا کنید تا مقیاس را افزایش دهید. یک ترفند فنی سریع که در اینجا ارزش ذکر دارد استفاده از OUTFILE در MySQL است، که می‌توانید آن را در هر جایی بنویسید اگر "secure_file_priv" را در mysqld.cnf غیرفعال کنید (و مطمئن شوید که AppArmor را نیز غیرفعال/بازنویسی کنید اگر در لینوکس هستید). ما داده‌ها را بر روی دیسک‌های سخت ساده ذخیره می‌کنیم. با هر چیزی که دارید شروع کنید و به آرامی گسترش دهید. ممکن است فکر کردن به ذخیره صدها ترابایت داده طاقت‌فرسا باشد. اگر این وضعیت شماست، ابتدا یک زیرمجموعه خوب را بیرون بگذارید و در اعلامیه خود درخواست کمک برای ذخیره بقیه کنید. اگر می‌خواهید خودتان دیسک‌های سخت بیشتری تهیه کنید، r/DataHoarder منابع خوبی برای دریافت معاملات خوب دارد. سعی کنید زیاد نگران فایل‌سیستم‌های پیچیده نباشید. به راحتی می‌توان به دام تنظیم چیزهایی مانند ZFS افتاد. یک جزئیات فنی که باید از آن آگاه باشید این است که بسیاری از فایل‌سیستم‌ها با تعداد زیادی فایل به خوبی کنار نمی‌آیند. ما متوجه شدیم که یک راه‌حل ساده ایجاد چندین دایرکتوری است، مثلاً برای محدوده‌های مختلف ID یا پیشوندهای هش. پس از دانلود داده‌ها، حتماً با استفاده از هش‌های موجود در metadata، در صورت موجود بودن، صحت فایل‌ها را بررسی کنید. ۲. انتخاب هدف قابل دسترسی: از لایه‌های محافظتی زیادی استفاده نمی‌کند تا از استخراج متادیتا و داده‌هایشان جلوگیری کند. بینش ویژه: شما اطلاعات خاصی درباره این هدف دارید، مانند اینکه به نوعی به این مجموعه دسترسی ویژه دارید یا متوجه شده‌اید که چگونه دفاع‌های آنها را شکست دهید. این الزامی نیست (پروژه آینده ما کار خاصی انجام نمی‌دهد)، اما قطعاً کمک می‌کند! بزرگ بنابراین، ما منطقه‌ای را که به آن نگاه می‌کنیم داریم، حالا کدام مجموعه خاص را لینک کمکی کنیم؟ چند چیز وجود دارد که یک هدف خوب را می‌سازد: وقتی کتاب‌های درسی علمی خود را در وب‌سایت‌هایی غیر از Library Genesis پیدا کردیم، سعی کردیم بفهمیم چگونه راه خود را به اینترنت پیدا کرده‌اند. سپس Z-Library را پیدا کردیم و متوجه شدیم که در حالی که اکثر کتاب‌ها ابتدا در آنجا ظاهر نمی‌شوند، اما در نهایت به آنجا می‌رسند. ما درباره رابطه آن با Library Genesis و ساختار انگیزشی (مالی) و رابط کاربری برتر آن که هر دو آن را به مجموعه‌ای بسیار کامل‌تر تبدیل کرده‌اند، یاد گرفتیم. سپس برخی از متادیتا و داده‌ها را به‌طور مقدماتی استخراج کردیم و متوجه شدیم که می‌توانیم محدودیت‌های دانلود IP آنها را دور بزنیم و از دسترسی ویژه یکی از اعضای خود به تعداد زیادی سرور پروکسی استفاده کنیم. در حالی که اهداف مختلف را بررسی می‌کنید، از همین حالا مهم است که ردپای خود را با استفاده از VPN‌ها و آدرس‌های ایمیل یکبار مصرف پنهان کنید، که بعداً بیشتر در مورد آن صحبت خواهیم کرد. منحصر به فرد: قبلاً به خوبی توسط پروژه‌های دیگر پوشش داده نشده باشد. وقتی پروژه‌ای را انجام می‌دهیم، چند مرحله دارد: این مراحل کاملاً مستقل نیستند و اغلب بینش‌هایی از یک مرحله بعدی شما را به مرحله قبلی بازمی‌گرداند. به عنوان مثال، در طول استخراج متادیتا ممکن است متوجه شوید که هدفی که انتخاب کرده‌اید دارای مکانیزم‌های دفاعی فراتر از سطح مهارت شما است (مانند بلوک‌های IP)، بنابراین به عقب برمی‌گردید و هدف دیگری پیدا می‌کنید. - آنا و تیم (<a %(reddit)s>Reddit</a>) کتاب‌های کاملی می‌توانند درباره <em>چرا</em>ی حفظ دیجیتال به طور کلی و آرشیویسم دزدان دریایی به طور خاص نوشته شوند، اما اجازه دهید یک مقدمه سریع برای کسانی که با آن آشنا نیستند ارائه دهیم. جهان بیش از هر زمان دیگری دانش و فرهنگ تولید می‌کند، اما همچنین بیش از هر زمان دیگری از آن از دست می‌رود. بشریت عمدتاً به شرکت‌هایی مانند ناشران دانشگاهی، خدمات پخش و شرکت‌های رسانه‌های اجتماعی این میراث را می‌سپارد و آن‌ها اغلب به عنوان نگهبانان خوبی ثابت نشده‌اند. مستند Digital Amnesia یا واقعاً هر سخنرانی از جیسون اسکات را بررسی کنید. برخی از مؤسسات وجود دارند که در آرشیو کردن هر چه می‌توانند کار خوبی انجام می‌دهند، اما آن‌ها به قانون محدود هستند. به عنوان دزدان دریایی، ما در موقعیت منحصر به فردی هستیم که می‌توانیم مجموعه‌هایی را آرشیو کنیم که آن‌ها نمی‌توانند به آن‌ها دست بزنند، به دلیل اجرای حق تکثیر یا محدودیت‌های دیگر. ما همچنین می‌توانیم مجموعه‌ها را بارها و بارها در سراسر جهان لینک کمکی کنیم، و به این ترتیب شانس حفظ صحیح را افزایش دهیم. فعلاً وارد بحث درباره مزایا و معایب مالکیت فکری، اخلاق شکستن قانون، تأملات در مورد سانسور، یا مسئله دسترسی به دانش و فرهنگ نمی‌شویم. با کنار گذاشتن همه این‌ها، بیایید به <em>چگونه</em> بپردازیم. ما به اشتراک می‌گذاریم که چگونه تیم ما به آرشیویست‌های دزدان دریایی تبدیل شد و درس‌هایی که در طول راه آموختیم. چالش‌های زیادی وجود دارد وقتی که شما این سفر را آغاز می‌کنید و امیدواریم که بتوانیم شما را از برخی از آن‌ها عبور دهیم. چگونه یک آرشیویست دزد دریایی شویم اولین چالش ممکن است شما را شگفت‌زده کند. این یک مشکل فنی یا قانونی نیست. این یک مشکل روانی است. قبل از اینکه وارد جزئیات شویم، دو به‌روزرسانی درباره لینک کمکی کتابخانه دزدان دریایی (ویرایش: به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> منتقل شد): ما برخی کمک‌های مالی بسیار سخاوتمندانه دریافت کردیم. اولین کمک ۱۰ هزار دلار از یک فرد ناشناس بود که همچنین از "bookwarrior"، بنیان‌گذار اصلی Library Genesis حمایت کرده است. تشکر ویژه از bookwarrior برای تسهیل این کمک. دومین کمک ۱۰ هزار دلار دیگر از یک اهداکننده ناشناس بود که پس از انتشار آخرین نسخه ما با ما تماس گرفت و الهام گرفت تا کمک کند. ما همچنین تعدادی کمک‌های مالی کوچکتر داشتیم. از همه شما برای حمایت سخاوتمندانه‌تان بسیار متشکریم. ما پروژه‌های جدید هیجان‌انگیزی در دست داریم که این کمک‌ها از آن‌ها پشتیبانی خواهد کرد، پس با ما همراه باشید. ما با اندازه انتشار دوم خود مشکلات فنی داشتیم، اما تورنت‌های ما اکنون فعال و در حال بذرپاشی هستند. ما همچنین یک پیشنهاد سخاوتمندانه از یک فرد ناشناس برای بذرپاشی مجموعه ما بر روی سرورهای بسیار پرسرعت آن‌ها دریافت کردیم، بنابراین ما یک بارگذاری ویژه به ماشین‌های آن‌ها انجام می‌دهیم، پس از آن همه کسانی که مجموعه را دانلود می‌کنند باید بهبود بزرگی در سرعت مشاهده کنند. پست‌های وبلاگ سلام، من آنا هستم. من <a %(wikipedia_annas_archive)s>آرشیو آنا</a>، بزرگترین کتابخانه سایه جهان را ایجاد کردم. این وبلاگ شخصی من است که در آن من و هم‌تیمی‌هایم درباره دزدی دریایی، حفظ دیجیتال و موارد دیگر می‌نویسیم. با من در <a %(reddit)s>Reddit</a> ارتباط برقرار کنید. توجه داشته باشید که این وب‌سایت فقط یک وبلاگ است. ما فقط کلمات خودمان را اینجا میزبانی می‌کنیم. هیچ تورنت یا فایل‌های دارای حق تکثیر اینجا میزبانی یا لینک نمی‌شوند. <strong>کتابخانه</strong> - مانند اکثر کتابخانه‌ها، ما عمدتاً بر روی مواد نوشتاری مانند کتاب‌ها تمرکز می‌کنیم. ممکن است در آینده به انواع دیگر رسانه‌ها گسترش یابیم. <strong>لینک کمکی</strong> - ما به طور دقیق یک لینک کمکی از کتابخانه‌های موجود هستیم. ما بر حفظ تمرکز داریم، نه بر آسان‌سازی جستجو و دانلود کتاب‌ها (دسترسی) یا پرورش یک جامعه بزرگ از افرادی که کتاب‌های جدید را ارائه می‌دهند (منبع‌یابی). <strong>دزد دریایی</strong> - ما عمداً قوانین کپی‌رایت را در اکثر کشورها نقض می‌کنیم. این به ما اجازه می‌دهد کاری انجام دهیم که نهادهای قانونی نمی‌توانند: اطمینان از اینکه کتاب‌ها به طور گسترده لینک کمکی می‌شوند. <em>ما به فایل‌ها از این وبلاگ لینک نمی‌دهیم. لطفاً خودتان آن را پیدا کنید.</em> - آنا و تیم (<a %(reddit)s>Reddit</a>) این پروژه (ویرایش: منتقل شده به <a %(wikipedia_annas_archive)s>آرشیو آنا</a>) هدفش کمک به حفظ و آزادسازی دانش انسانی است. ما سهم کوچک و فروتنانه خود را در پیروی از بزرگان قبل از خود انجام می‌دهیم. تمرکز این پروژه با نام آن نشان داده شده است: اولین کتابخانه‌ای که ما لینک کمکی کرده‌ایم Z-Library است. این یک کتابخانه محبوب (و غیرقانونی) است. آن‌ها مجموعه Library Genesis را گرفته و آن را به راحتی قابل جستجو کرده‌اند. علاوه بر این، آن‌ها در جلب مشارکت‌های جدید کتاب بسیار مؤثر شده‌اند، با تشویق کاربران مشارکت‌کننده با امتیازات مختلف. آن‌ها در حال حاضر این کتاب‌های جدید را به Library Genesis برنمی‌گردانند. و برخلاف Library Genesis، آن‌ها مجموعه خود را به راحتی قابل لینک کمکی نمی‌کنند، که مانع از حفظ گسترده می‌شود. این برای مدل کسب‌وکار آن‌ها مهم است، زیرا آن‌ها برای دسترسی به مجموعه خود به صورت عمده (بیش از 10 کتاب در روز) هزینه دریافت می‌کنند. ما در مورد دریافت هزینه برای دسترسی عمده به یک مجموعه کتاب غیرقانونی قضاوت اخلاقی نمی‌کنیم. بدون شک، Z-Library در گسترش دسترسی به دانش و تأمین کتاب‌های بیشتر موفق بوده است. ما فقط اینجا هستیم تا نقش خود را ایفا کنیم: اطمینان از حفظ طولانی‌مدت این مجموعه خصوصی. ما دوست داریم شما را دعوت کنیم تا با دانلود و اشتراک‌گذاری تورنت‌های ما به حفظ و آزادسازی دانش انسانی کمک کنید. برای اطلاعات بیشتر درباره نحوه سازماندهی داده‌ها، به صفحه پروژه مراجعه کنید. ما همچنین شما را به شدت دعوت می‌کنیم که ایده‌های خود را برای اینکه کدام مجموعه‌ها را به عنوان لینک کمکی بعدی انتخاب کنیم و چگونه این کار را انجام دهیم، با ما به اشتراک بگذارید. با هم می‌توانیم به دستاوردهای بزرگی برسیم. این تنها یک کمک کوچک در میان بی‌شمار کمک‌های دیگر است. از شما برای همه کارهایی که انجام می‌دهید، سپاسگزاریم. معرفی لینک کمکی کتابخانه دزدان دریایی: حفظ 7 ترابایت کتاب (که در Libgen نیستند) 10% oاز میراث مکتوب بشریت برای همیشه حفظ شده است <strong>گوگل.</strong> به هر حال، آن‌ها این تحقیق را برای گوگل بوکس انجام دادند. با این حال، متادیتای آن‌ها به صورت عمده قابل دسترسی نیست و به سختی قابل استخراج است. <strong>سیستم‌های کتابخانه‌ای و آرشیوهای مختلف.</strong> کتابخانه‌ها و آرشیوهایی وجود دارند که توسط هیچ‌یک از موارد فوق فهرست و تجمیع نشده‌اند، اغلب به این دلیل که بودجه کافی ندارند یا به دلایل دیگر نمی‌خواهند داده‌های خود را با کتابخانهٔ باز، OCLC، گوگل و غیره به اشتراک بگذارند. بسیاری از این‌ها دارای سوابق دیجیتالی هستند که از طریق اینترنت قابل دسترسی است و اغلب به خوبی محافظت نمی‌شوند، بنابراین اگر می‌خواهید کمک کنید و از یادگیری درباره سیستم‌های کتابخانه‌ای عجیب لذت ببرید، این‌ها نقاط شروع خوبی هستند. <strong>ISBNdb.</strong> این موضوع این پست وبلاگ است. ISBNdb از وب‌سایت‌های مختلف برای متادیتای کتاب، به ویژه داده‌های قیمت‌گذاری، استخراج می‌کند که سپس آن‌ها را به کتابفروشان می‌فروشد تا بتوانند کتاب‌های خود را مطابق با بقیه بازار قیمت‌گذاری کنند. از آنجا که ISBNها امروزه نسبتاً جهانی هستند، آن‌ها به طور مؤثر یک «صفحه وب برای هر کتاب» ساخته‌اند. <strong>کتابخانهٔ باز.</strong> همان‌طور که قبلاً ذکر شد، این تمام مأموریت آن‌هاست. آن‌ها مقادیر زیادی از داده‌های کتابخانه‌ای را از کتابخانه‌های همکار و آرشیوهای ملی جمع‌آوری کرده‌اند و همچنان به این کار ادامه می‌دهند. آن‌ها همچنین کتابداران داوطلب و یک تیم فنی دارند که سعی در حذف تکرار رکوردها و برچسب‌گذاری آن‌ها با انواع متادیتا دارند. بهترین از همه، مجموعه داده آن‌ها کاملاً باز است. شما می‌توانید به سادگی <a %(openlibrary)s>آن را دانلود کنید</a>. <strong>WorldCat.</strong> این یک وب‌سایت است که توسط سازمان غیرانتفاعی OCLC اداره می‌شود، که سیستم‌های مدیریت کتابخانه را می‌فروشد. آن‌ها متادیتای کتاب را از بسیاری از کتابخانه‌ها جمع‌آوری می‌کنند و از طریق وب‌سایت WorldCat در دسترس قرار می‌دهند. با این حال، آن‌ها همچنین با فروش این داده‌ها درآمد کسب می‌کنند، بنابراین برای دانلود عمده در دسترس نیست. آن‌ها برخی از مجموعه داده‌های عمده محدودتر را برای دانلود در همکاری با کتابخانه‌های خاص در دسترس قرار می‌دهند. 1. برای برخی تعریف‌های معقول از "برای همیشه". ;) 2. البته، میراث نوشتاری بشریت بسیار بیشتر از کتاب‌ها است، به ویژه در دنیای امروز. برای این پست و انتشارهای اخیر ما بر روی کتاب‌ها تمرکز کرده‌ایم، اما علاقه‌های ما فراتر می‌رود. 3. چیزهای زیادی درباره آرون سوارتز می‌توان گفت، اما ما فقط می‌خواستیم به طور مختصر به او اشاره کنیم، زیرا او نقش محوری در این داستان دارد. با گذشت زمان، ممکن است افراد بیشتری برای اولین بار با نام او مواجه شوند و سپس خودشان به عمق ماجرا بروند. <strong>نسخه‌های فیزیکی.</strong> بدیهی است که این خیلی مفید نیست، زیرا آن‌ها فقط نسخه‌های تکراری از همان مطالب هستند. جالب خواهد بود اگر بتوانیم تمام حاشیه‌نویسی‌هایی که مردم در کتاب‌ها انجام می‌دهند، مانند «یادداشت‌های حاشیه‌ای» معروف فرما، را حفظ کنیم. اما افسوس، این یک آرزوی آرشیوی باقی خواهد ماند. <strong>«چاپ‌ها».</strong> در اینجا شما هر نسخه منحصر به فرد از یک کتاب را می‌شمارید. اگر هر چیزی در مورد آن متفاوت باشد، مانند جلد متفاوت یا مقدمه متفاوت، به عنوان یک چاپ متفاوت حساب می‌شود. <strong>فایل‌ها.</strong> هنگام کار با کتابخانه‌های سایه مانند Library Genesis، Sci-Hub یا Z-Library، یک ملاحظه اضافی وجود دارد. ممکن است چندین اسکن از همان چاپ وجود داشته باشد. و مردم می‌توانند نسخه‌های بهتری از فایل‌های موجود ایجاد کنند، با اسکن متن با استفاده از OCR، یا اصلاح صفحات که با زاویه اسکن شده‌اند. ما می‌خواهیم این فایل‌ها را به عنوان یک چاپ بشماریم، که نیاز به متادیتای خوب یا حذف تکرار با استفاده از معیارهای شباهت سند دارد. <strong>«آثار».</strong> به عنوان مثال «هری پاتر و تالار اسرار» به عنوان یک مفهوم منطقی، شامل تمام نسخه‌های آن، مانند ترجمه‌ها و چاپ‌های مختلف. این نوعی تعریف مفید است، اما ممکن است سخت باشد که خطی بکشیم که چه چیزی حساب می‌شود. به عنوان مثال، احتمالاً می‌خواهیم ترجمه‌های مختلف را حفظ کنیم، اگرچه چاپ‌هایی با تفاوت‌های جزئی ممکن است به اندازه کافی مهم نباشند. - آنا و تیم (<a %(reddit)s>Reddit</a>) با لینک کمکی کتابخانه دزدان دریایی (ویرایش: منتقل شده به <a %(wikipedia_annas_archive)s>آرشیو آنا</a>)، هدف ما این است که تمام کتاب‌های جهان را بگیریم و برای همیشه حفظ کنیم.<sup>1</sup> بین تورنت‌های Z-Library ما و تورنت‌های اصلی Library Genesis، ما 11,783,153 فایل داریم. اما واقعاً این تعداد چقدر است؟ اگر این فایل‌ها را به درستی تکراری‌زدایی کنیم، چه درصدی از تمام کتاب‌های جهان را حفظ کرده‌ایم؟ ما واقعاً دوست داریم چیزی شبیه به این داشته باشیم: بیایید با چند عدد تقریبی شروع کنیم: در هر دو Z-Library/Libgen و کتابخانهٔ باز، کتاب‌های بیشتری نسبت به ISBNهای منحصر به فرد وجود دارد. آیا این بدان معناست که بسیاری از آن کتاب‌ها ISBN ندارند، یا اینکه متادیتای ISBN به سادگی وجود ندارد؟ احتمالاً می‌توانیم به این سوال با ترکیبی از تطبیق خودکار بر اساس ویژگی‌های دیگر (عنوان، نویسنده، ناشر و غیره)، جمع‌آوری منابع داده بیشتر و استخراج ISBNها از خود اسکن‌های کتاب (در مورد Z-Library/Libgen) پاسخ دهیم. چند تا از این ISBNها منحصر به فرد هستند؟ این موضوع به بهترین شکل با یک نمودار وِن نشان داده می‌شود: برای دقیق‌تر بودن: ما از اینکه چقدر همپوشانی کمی وجود دارد شگفت‌زده شدیم! ISBNdb تعداد زیادی ISBN دارد که در هیچ‌کدام از Z-Library یا Open Library ظاهر نمی‌شوند، و همین موضوع (به میزان کمتر اما همچنان قابل توجه) برای دو مورد دیگر نیز صادق است. این موضوع سوالات جدید زیادی را مطرح می‌کند. چقدر تطبیق خودکار می‌تواند در برچسب‌گذاری کتاب‌هایی که با ISBN برچسب‌گذاری نشده‌اند کمک کند؟ آیا تطبیق‌های زیادی وجود خواهد داشت و در نتیجه همپوشانی افزایش خواهد یافت؟ همچنین، اگر یک مجموعه داده چهارم یا پنجم را وارد کنیم چه اتفاقی می‌افتد؟ چقدر همپوشانی خواهیم دید؟ این به ما نقطه شروعی می‌دهد. اکنون می‌توانیم به تمام ISBNهایی که در مجموعه داده Z-Library نبودند و با فیلدهای عنوان/نویسنده نیز مطابقت ندارند نگاه کنیم. این می‌تواند به ما در حفظ تمام کتاب‌های جهان کمک کند: ابتدا با جستجو در اینترنت برای اسکن‌ها، سپس با رفتن به دنیای واقعی برای اسکن کتاب‌ها. دومی حتی می‌تواند از طریق جمع‌آوری سرمایه عمومی یا توسط "پاداش‌ها" از افرادی که دوست دارند کتاب‌های خاصی دیجیتالی شوند، انجام شود. همه این‌ها داستانی برای زمان دیگری است. اگر می‌خواهید در هر یک از این موارد کمک کنید — تحلیل بیشتر؛ جمع‌آوری metadata بیشتر؛ یافتن کتاب‌های بیشتر؛ OCR کردن کتاب‌ها؛ انجام این کار برای حوزه‌های دیگر (مثلاً مقالات، کتاب‌های صوتی، فیلم‌ها، برنامه‌های تلویزیونی، مجلات) یا حتی در دسترس قرار دادن برخی از این داده‌ها برای مواردی مانند آموزش مدل‌های زبانی بزرگ / ML — لطفاً با من تماس بگیرید (<a %(reddit)s>Reddit</a>). اگر به طور خاص به تحلیل داده‌ها علاقه‌مند هستید، ما در حال کار بر روی در دسترس قرار دادن مجموعه داده‌ها و اسکریپت‌های خود در قالبی آسان‌تر برای استفاده هستیم. عالی خواهد بود اگر بتوانید به سادگی یک دفترچه را فورک کنید و با آن بازی کنید. در نهایت، اگر می‌خواهید از این کار حمایت کنید، لطفاً به فکر اهدای کمک مالی باشید. این یک عملیات کاملاً داوطلبانه است و کمک شما تفاوت بزرگی ایجاد می‌کند. هر مقدار کمک می‌کند. فعلاً ما کمک‌های مالی را به صورت ارز دیجیتال می‌پذیریم؛ صفحه اهدای کمک در آرشیو آنا را ببینید. برای محاسبه درصد، به یک مخرج نیاز داریم: تعداد کل کتاب‌هایی که تاکنون منتشر شده‌اند.<sup>2</sup> قبل از پایان پروژه گوگل بوکس، مهندسی به نام لئونید تایچر <a %(booksearch_blogspot)s>سعی کرد این عدد را تخمین بزند</a>. او به شوخی به عدد ۱۲۹,۸۶۴,۸۸۰ رسید («حداقل تا یکشنبه»). او این عدد را با ساخت یک پایگاه داده یکپارچه از تمام کتاب‌های جهان تخمین زد. برای این کار، او مجموعه داده‌های مختلفی را جمع‌آوری کرد و سپس آن‌ها را به روش‌های مختلف ادغام کرد. به عنوان یک نکته جانبی سریع، شخص دیگری نیز تلاش کرد تا تمام کتاب‌های جهان را فهرست کند: آرون سوارتز، فعال دیجیتال فقید و هم‌بنیان‌گذار Reddit.<sup>3</sup> او <a %(youtube)s>کتابخانهٔ باز</a> را با هدف «یک صفحه وب برای هر کتابی که تاکنون منتشر شده» راه‌اندازی کرد و داده‌ها را از منابع مختلف ترکیب کرد. او در نهایت به دلیل کارهایش در حفظ دیجیتال، زمانی که به دلیل دانلود انبوه مقالات علمی تحت پیگرد قانونی قرار گرفت و منجر به خودکشی‌اش شد، بهای نهایی را پرداخت. نیازی به گفتن نیست که این یکی از دلایلی است که گروه ما به صورت مستعار فعالیت می‌کند و بسیار محتاط هستیم. کتابخانهٔ باز همچنان به طور قهرمانانه توسط افراد در آرشیو اینترنتی اداره می‌شود و میراث آرون را ادامه می‌دهد. ما در ادامه این پست به این موضوع باز خواهیم گشت. در پست وبلاگ گوگل، تایچر برخی از چالش‌های تخمین این عدد را توصیف می‌کند. اول، چه چیزی یک کتاب را تشکیل می‌دهد؟ چند تعریف ممکن وجود دارد: «چاپ‌ها» به نظر می‌رسد که عملی‌ترین تعریف از آنچه «کتاب‌ها» هستند باشد. به طور مناسب، این تعریف همچنین برای اختصاص شماره‌های ISBN منحصر به فرد استفاده می‌شود. یک ISBN، یا شماره استاندارد بین‌المللی کتاب، به طور معمول برای تجارت بین‌المللی استفاده می‌شود، زیرا با سیستم بارکد بین‌المللی (”شماره مقاله بین‌المللی”) یکپارچه شده است. اگر می‌خواهید کتابی را در فروشگاه‌ها بفروشید، به یک بارکد نیاز دارید، بنابراین یک ISBN دریافت می‌کنید. پست وبلاگ تایچر اشاره می‌کند که در حالی که ISBNها مفید هستند، اما جهانی نیستند، زیرا واقعاً در اواسط دهه هفتاد پذیرفته شدند و نه در همه جای جهان. با این حال، ISBN احتمالاً پرکاربردترین شناسه چاپ‌های کتاب است، بنابراین این بهترین نقطه شروع ماست. اگر بتوانیم تمام ISBNهای جهان را پیدا کنیم، فهرست مفیدی از کتاب‌هایی که هنوز نیاز به حفظ دارند، به دست می‌آوریم. پس، از کجا داده‌ها را به دست می‌آوریم؟ تلاش‌های موجودی وجود دارد که سعی در جمع‌آوری فهرستی از تمام کتاب‌های جهان دارند: در این پست، خوشحالیم که یک انتشار کوچک (در مقایسه با انتشارهای قبلی Z-Library ما) را اعلام کنیم. ما بیشتر ISBNdb را استخراج کردیم و داده‌ها را برای تورنت در وب‌سایت آینه کتابخانه دزدان دریایی در دسترس قرار دادیم (ویرایش: به <a %(wikipedia_annas_archive)s>آرشیو آنا</a> منتقل شد؛ ما اینجا به طور مستقیم لینک نمی‌دهیم، فقط جستجو کنید). این‌ها حدود ۳۰.۹ میلیون رکورد هستند (۲۰ گیگابایت به صورت <a %(jsonlines)s>JSON Lines</a>; ۴.۴ گیگابایت فشرده شده). در وب‌سایت آن‌ها ادعا می‌کنند که در واقع ۳۲.۶ میلیون رکورد دارند، بنابراین ممکن است به نوعی برخی را از دست داده باشیم، یا <em>آن‌ها</em> ممکن است اشتباهی کرده باشند. در هر صورت، فعلاً دقیقاً نحوه انجام آن را به اشتراک نمی‌گذاریم — ما آن را به عنوان یک تمرین برای خواننده باقی می‌گذاریم. ;-) آنچه ما به اشتراک خواهیم گذاشت، برخی تحلیل‌های اولیه است تا سعی کنیم به تخمین تعداد کتاب‌های جهان نزدیک‌تر شویم. ما به سه مجموعه داده نگاه کردیم: این مجموعه داده جدید ISBNdb، انتشار اولیه متادیتای ما که از کتابخانه سایه Z-Library استخراج کردیم (که شامل Library Genesis است) و داده‌های کتابخانهٔ باز. خروجی ISBNdb، یا چند کتاب برای همیشه حفظ می‌شوند؟ اگر ما فایل‌ها را از کتابخانه‌های سایه به درستی تکراری‌زدایی کنیم، چه درصدی از تمام کتاب‌های جهان را حفظ کرده‌ایم؟ به‌روزرسانی‌ها درباره <a %(wikipedia_annas_archive)s>آرشیو آنا</a>، بزرگ‌ترین کتابخانه واقعاً باز در تاریخ بشر. <em>طراحی مجدد ورلدکت</em> داده‌ها <strong>فرمت؟</strong> <a %(blog)s>کانتینرهای آرشیو آنا (AAC)</a>، که اساساً <a %(jsonlines)s>JSON Lines</a> فشرده شده با <a %(zstd)s>Zstandard</a> است، به علاوه برخی از معانی استاندارد شده. این کانتینرها انواع مختلفی از سوابق را بر اساس خراش‌های مختلفی که انجام دادیم، در بر می‌گیرند. یک سال پیش، ما <a %(blog)s>شروع به کار</a> کردیم تا به این سوال پاسخ دهیم: <strong>چه درصدی از کتاب‌ها توسط کتابخانه‌های سایه‌ای به طور دائمی حفظ شده‌اند؟</strong> بیایید به برخی اطلاعات پایه‌ای درباره داده‌ها نگاهی بیندازیم: وقتی کتابی وارد یک کتابخانه سایه‌ای با داده‌های باز مانند <a %(wikipedia_library_genesis)s>Library Genesis</a> و اکنون <a %(wikipedia_annas_archive)s>آرشیو آنا</a> می‌شود، در سراسر جهان (از طریق تورنت‌ها) منعکس می‌شود و به این ترتیب عملاً برای همیشه حفظ می‌شود. برای پاسخ به سوال اینکه چه درصدی از کتاب‌ها حفظ شده‌اند، باید مخرج را بدانیم: چند کتاب در کل وجود دارد؟ و ایده‌آل این است که نه تنها یک عدد، بلکه metadata واقعی داشته باشیم. سپس می‌توانیم نه تنها آن‌ها را با کتابخانه‌های سایه‌ای مقایسه کنیم، بلکه <strong>یک لیست کارهای باقی‌مانده از کتاب‌هایی که باید حفظ شوند ایجاد کنیم!</strong> حتی می‌توانیم رویای یک تلاش جمعی برای پایین رفتن از این لیست کارها را ببینیم. ما <a %(wikipedia_isbndb_com)s>ISBNdb</a> را خراش دادیم و <a %(openlibrary)s>مجموعه داده کتابخانهٔ باز</a> را دانلود کردیم، اما نتایج رضایت‌بخش نبود. مشکل اصلی این بود که همپوشانی زیادی از ISBNها وجود نداشت. این نمودار ونی از <a %(blog)s>پست وبلاگ ما</a> را ببینید: ما از اینکه چقدر همپوشانی کمی بین ISBNdb و کتابخانهٔ باز وجود داشت، بسیار شگفت‌زده شدیم، هر دو که به طور آزادانه داده‌ها را از منابع مختلف، مانند خراش‌های وب و سوابق کتابخانه‌ای، شامل می‌شوند. اگر هر دو در یافتن بیشتر ISBNها در آنجا کار خوبی انجام دهند، دایره‌های آن‌ها قطعاً همپوشانی قابل توجهی خواهند داشت، یا یکی زیرمجموعه دیگری خواهد بود. این ما را به فکر واداشت که چند کتاب <em>کاملاً خارج از این دایره‌ها قرار می‌گیرند</em>؟ ما به یک پایگاه داده بزرگتر نیاز داریم. در آن زمان ما به بزرگترین پایگاه داده کتاب در جهان نگاه کردیم: <a %(wikipedia_worldcat)s>ورلدکت</a>. این یک پایگاه داده اختصاصی توسط غیرانتفاعی <a %(wikipedia_oclc)s>OCLC</a> است که سوابق metadata را از کتابخانه‌های سراسر جهان جمع‌آوری می‌کند، در ازای دادن دسترسی به مجموعه داده کامل به آن کتابخانه‌ها و نمایش آن‌ها در نتایج جستجوی کاربران نهایی. حتی اگر OCLC یک غیرانتفاعی است، مدل کسب‌وکار آن‌ها نیاز به حفاظت از پایگاه داده‌شان دارد. خوب، ما متاسفیم که بگوییم، دوستان در OCLC، ما همه چیز را به اشتراک می‌گذاریم. :-) در طول سال گذشته، ما به دقت تمام سوابق ورلدکت را خراش دادیم. در ابتدا، یک شانس خوب به ما رسید. ورلدکت به تازگی طراحی کامل وب‌سایت خود را (در آگوست ۲۰۲۲) راه‌اندازی کرده بود. این شامل یک بازسازی اساسی از سیستم‌های پشتیبان آن‌ها بود که بسیاری از نقص‌های امنیتی را معرفی کرد. ما بلافاصله از این فرصت استفاده کردیم و توانستیم صدها میلیون (!) رکورد را در عرض چند روز خراش دهیم. پس از آن، نقص‌های امنیتی به تدریج یکی یکی برطرف شدند، تا اینکه آخرین نقصی که پیدا کردیم حدود یک ماه پیش برطرف شد. تا آن زمان ما تقریباً تمام سوابق را داشتیم و فقط به دنبال سوابق با کیفیت کمی بالاتر بودیم. بنابراین احساس کردیم زمان انتشار فرا رسیده است! خراش ۱.۳ میلیاردی WorldCat <em><strong>خلاصه:</strong> آرشیو آنا تمام WorldCat (بزرگ‌ترین مجموعه metadata کتابخانه‌ای جهان) را خراش داده تا فهرستی از کتاب‌هایی که نیاز به حفظ دارند تهیه کند.</em> ورلدکت هشدار: این پست وبلاگ منسوخ شده است. ما تصمیم گرفته‌ایم که IPFS هنوز برای استفاده عمومی آماده نیست. ما همچنان به فایل‌ها در IPFS از آرشیو آنا لینک خواهیم داد، اما دیگر خودمان آن را میزبانی نمی‌کنیم و به دیگران نیز توصیه نمی‌کنیم که با استفاده از IPFS لینک کمکی کنند. لطفاً به صفحه تورنت‌های ما مراجعه کنید اگر می‌خواهید به حفظ مجموعه ما کمک کنید. کمک به توزیع Z-Library در IPFS دانلود از سرور شریک SciDB امانت خارجی قرض خارجی (چاپ غیرفعال) دانلود خارجی کاوش در فراداده موجود در تورنت‌ها بازگشت  (+%(num)s پاداش) پرداخت نشده پرداخت شده لغو شد منقضی شده در انتظار تأیید آنا نامعتبر متن زیر به انگلیسی ادامه دارد. برو بازنشانی به جلو آخرین اگر ایمیل‌تون در انجمن Libgen کار نکرد، ما پیشنهاد می‌کنیم که از <a %(a_mail)s>Proton Mail</a> استفاده کنید. شما همچنین میتونید <a %(a_manual)s>بصورت دستی درخواست بدید</a> تا حسابتون فعال بشه. (ممکن است نیاز به <a %(a_browser)s>تأیید مرورگر</a> داشته باشد — دانلودهای نامحدود!) سرور شریک سریع #%(number)s (پیشنهادی) (کمی سریع‌تر اما با لیست انتظار) (نیازی به تأیید مرورگر نیست) (بدون تأیید مرورگر یا لیست انتظار) (بدون لیست انتظار، اما ممکن است بسیار کند باشد) سرور شریک کند شماره %(number)s کتاب صوتی کتاب کمیک کتاب (داستانی) کتاب (غیر داستانی) کتاب (ناشناخته) مقاله مجله پارتیتور موسیقی دیگر سند استاندارد همه صفحات نمی‌توانند به PDF تبدیل شوند در Libgen.li به عنوان فایل خراب نشانه گذاری شده است در Libgen.rs در بخش داستانی قابل مشاهده نیست در Libgen.rs در بخش داستانی قابل مشاهده نیست در libgen.rs در بخش غیر داستانی قابل مشاهده نیست اجرای exiftool بر روی این فایل شکست خورد به عنوان «فایل بد» در Z-Library علامت‌گذاری شده است در Z-Library موجود نیست به عنوان «اسپم» در Z-Library علامت‌گذاری شده است فایل نمی‌تواند باز شود (مثلاً فایل خراب، DRM) ادعای حق نسخه‌برداری مشکلات دانلود (مثلاً عدم اتصال، پیام خطا، سرعت دانلود پایین) اطلاعات نادرست (مانند عنوان، توضیحات، تصویر جلد) دیگر کیفیت ضعیف (مثلاً مشکلات قالب‌بندی، کیفیت اسکن ضعیف، صفحات گمشده) اسپم / فایل باید حذف شود (مثلاً تبلیغات، محتوای توهین‌آمیز) %(amount)s (%(amount_usd)s) %(amount)s مجموع %(amount)s (%(amount_usd)s) مجموع کرم کتاب کم‌نظیر کتابدار خوش‌شانس داده‌دوست درخشان آرشیودار عالی دانلودهای اضافی Cerlalc فراداده چک DuXiu 读秀 نمایه کتاب الکترونیکی EBSCOhost کتاب‌های گوگل گودریدز هایتی‌تراست IA قرض‌دهی دیجیتال کنترل‌شده IA ISBNdb ISBN GRP Libgen.li به‌جز “scimag” Libgen.rs غیرداستانی و داستانی Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary کتابخانه دولتی روسیه Sci-Hub از طریق Libgen.li "scimag" Sci-Hub / Libgen “scimag” Trantor بارگذاری‌ها به AA Z-Library Z-Library چینی جستجو عنوان، نویسنده، DOI، شابک، MD5، … جستجو نویسنده توضیحات و نظرات فراداده نسخه نام فایل اصلی ناشر (جستجوی فیلد خاص) عنوان سال انتشار جزییات فنی این سکه حداقل بالاتری از حد معمول دارد. لطفاً مدت زمان یا سکه دیگری را انتخاب کنید. درخواست نمی‌تواند تکمیل شود. لطفاً چند دقیقه دیگر دوباره تلاش کنید و اگر این مشکل همچنان ادامه داشت، با ما در %(email)s تماس بگیرید و یک اسکرین‌شات ارسال کنید. یک خطای ناشناخته رخ داد. لطفاً با %(email)s تماس بگیرید و یک اسکرین‌شات ارسال کنید. خطا در پردازش پرداخت. لطفاً لحظه‌ای صبر کرده و دوباره تلاش کنید. اگر مشکل بیش از ۲۴ ساعت ادامه داشت، لطفاً با ما در %(email)s تماس بگیرید و یک اسکرین‌شات ارسال کنید. ما در حال برگزاری یک جمع‌آوری کمک مالی برای <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">پشتیبان‌گیری</a> از بزرگترین کتابخانه سایه‌ای کمیک در جهان هستیم. از حمایت شما متشکریم! <a href="/donate">اهدا کنید.</a> اگر نمی‌توانید اهدا کنید، لطفاً با گفتن به دوستانتان و دنبال کردن ما در <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a> یا <a href="https://t.me/annasarchiveorg">Telegram</a> از ما حمایت کنید. برای <a %(a_request)s>درخواست کتاب</a><br>یا<a %(a_upload)s>بارگذاری‌های</a> کوچک (کمتر از ۱۰ هزار) به ما ایمیل ندید. بایگانی آنا DMCA / ادعای حق نشر در تماس باش ردیت جایگزین‌ها اسلوم (%(unaffiliated)s) غیر وابسته آرشیو آنا به کمک شما نیاز دارد! اگر اکنون اهدا کنید، <strong>دو برابر</strong> تعداد دانلودهای سریع دریافت می‌کنید. بسیاری سعی می‌کنند ما را از بین ببرند، اما ما مقاومت می‌کنیم. اگر این ماه اهدا کنید، تعداد دانلودهای سریع شما <strong>دو برابر</strong> خواهد شد. معتبر تا پایان این ماه. صرفه جویی در دانش بشر: یک هدیه عالی برای تعطیلات! عضویت‌ها به طور متناسب تمدید خواهند شد. سرورهای شریک به دلیل تعطیلی میزبانی در دسترس نیستند. آن‌ها به زودی دوباره فعال خواهند شد. برای افزایش مقاومت آرشیو آنا، ما به دنبال داوطلبانی برای اجرای آینه‌ها هستیم. ما یک روش جدید برای اهدا داریم: %(method_name)s. لطفاً به %(donate_link_open_tag)sاهدا کردن</a> فکر کنید — اداره این وب‌سایت ارزان نیست و اهداهای شما واقعاً تفاوت ایجاد می‌کند. بسیار متشکریم. یک دوست معرفی کنید، و هم شما و هم دوستتان %(percentage)s%% دانلود سریع اضافی دریافت کنید! یکی از عزیزان را سورپرایز کنید، به آنها یک حساب کاربری با عضویت بدهید. هدیه ولنتاین عالی! بیشتر بدانید… حساب فعالیت پیشرفته وبلاگ آنا ↗ نرم افزار آنا ↗ بتا کاوشگر کدها مجموعه داده ها اهدا کنید فایل‌های بارگیری شده سؤالات متداول صفحه اصلی بهبود فراداده داده‌های LLM ورود / ثبت‌نام حمایت‌های من نمایه‌ی عمومی جستجو امنیت تورنت‌ها ترجمه ↗ داوطلبی و پاداش‌ها دانلودهای اخیر: 📚&nbsp;بزرگترین کتابخانه متن‌باز و داده‌باز جهان. ⭐️&nbsp;آینه‌های Sci-Hub، Library Genesis، Z-Library و بیشتر. 📈&nbsp;%(book_any)s کتاب، %(journal_article)s مقاله، %(book_comic)s کمیک، %(magazine)s مجله — برای همیشه حفظ شده‌اند.  و  و بیشتر DuXiu کتابخانه امانت اینترنت آرشیو LibGen 📚&nbsp;بزرگترین کتابخانهٔ واقعاً باز در تاریخ بشر. 📈 %(book_count)s کتاب ها، %(paper_count)s مقالات — برای همیشه حفظ می‌شود. ⭐️&nbsp;ما لینک کمکی %(libraries)s را ارائه می‌دهیم. ما %(scraped)s را استخراج و متن‌باز می‌کنیم. تمام کدها و داده‌های ما کاملاً متن‌باز هستند. OpenLib Sci-Hub ،  📚 بزرگترین کتابخانه منبع باز منبع باز جهان.<br>⭐️ لینک هایی از Scihub، Libgen، Zlib، و موارد دیگر. Z-Lib بایگانی Anna درخواست نامعتبر. به %(websites)s مراجعه کنید. بزرگترین کتابخانه متن‌باز و داده‌باز جهان. آینه‌های Sci-Hub، Library Genesis، Z-Library و بیشتر. جستجوی آرشیو آنا بایگانی آنا لطفاً برای تلاش مجدد صفحه را تازه کنید. <a %(a_contact)s>با ما تماس بگیرید</a> اگر مشکل برای چندین ساعت ادامه داشت. 🔥 مشکل در بارگذاری این صفحه <li>1. ما را در <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> یا <a href="https://t.me/annasarchiveorg">Telegram</a> دنبال کنید.</li><li>2. درباره آرشیو آنا در توییتر، Reddit، Tiktok، اینستاگرام، در کافه یا کتابخانه محلی خود یا هر جایی که می‌روید، اطلاع‌رسانی کنید! ما به دروازه‌بانی اعتقاد نداریم — اگر ما را پایین بیاورند، دوباره در جای دیگری ظاهر خواهیم شد، زیرا تمام کد و داده‌های ما کاملاً منبع باز هستند.</li><li>3. اگر توانایی دارید، به <a href="/donate">اهدا</a> فکر کنید.</li><li>4. به <a href="https://translate.annas-software.org/">ترجمه</a> وب‌سایت ما به زبان‌های مختلف کمک کنید.</li><li>5. اگر مهندس نرم‌افزار هستید، به پروژه <a href="https://annas-software.org/">منبع باز</a> ما کمک کنید یا تورنت‌های ما را بذر کنید.<a href="/datasets"></a>.</li> ۱۰. صفحه ویکی‌پدیا برای آرشیو آنا را به زبان خود ایجاد یا نگهداری کنید. ۱۱. ما به دنبال قرار دادن تبلیغات کوچک و زیبا هستیم. اگر می‌خواهید در آرشیو آنا تبلیغ کنید، لطفاً به ما اطلاع دهید. ۶. اگر شما یک محقق امنیتی هستید، می‌توانیم از مهارت‌های شما هم برای حمله و هم برای دفاع استفاده کنیم. صفحه <a %(a_security)s>امنیت</a> ما را بررسی کنید. 7. ما به دنبال کارشناسانی در زمینه پرداخت برای بازرگانان ناشناس هستیم. آیا می‌توانید به ما کمک کنید تا روش‌های راحت‌تری برای اهدا اضافه کنیم؟ PayPal، WeChat، کارت‌های هدیه. اگر کسی را می‌شناسید، لطفاً با ما تماس بگیرید. ۸. ما همیشه به دنبال ظرفیت سرور بیشتری هستیم. ۹. شما می‌توانید با گزارش مشکلات فایل، گذاشتن نظرات و ایجاد لیست‌ها در همین وب‌سایت کمک کنید. همچنین می‌توانید با <a %(a_upload)s>بارگذاری کتاب‌های بیشتر</a>، یا رفع مشکلات فایل یا قالب‌بندی کتاب‌های موجود کمک کنید. برای اطلاعات بیشتر در مورد نحوه داوطلب شدن، به صفحه <a %(a_volunteering)s>داوطلبی و پاداش‌ها</a> ما مراجعه کنید. ما به شدت به جریان آزاد اطلاعات و حفظ دانش و فرهنگ اعتقاد داریم. با این موتور جستجو، ما با استفاده از درک به دست آمده توسط متفکران اصلی که قبلا برای پیشرفت فکری پیش رفته اند، پیش می رویم. ما عمیقاً به کار سخت افرادی که کتابخانه‌های سایه مختلف را ایجاد کرده‌اند احترام می‌گذاریم و امیدواریم که این موتور جستجو دامنه دسترسی آنها را گسترش دهد. برای اطلاع از پیشرفت، Anna را در شبکه های اجتماعی دنبال کنید <a href="https://www.reddit.com/r/Annas_Archive/">ردیت</a> یا <a href="https://t.me/annasarchiveorg">تلگرام</a>. برای هرگونه سوال یا بازخورد لطفا به آدرس رو به رو به ما ایمیل بزنید %(email)s شناسه حساب: %(account_id)s خروج ❌ مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. ✅ شما اکنون خارج شده‌اید. برای ورود مجدد صفحه را بارگذاری کنید. دانلودهای سریع استفاده شده (۲۴ ساعت گذشته): <strong>%(used)s / %(total)s</strong> عضویت: <strong>%(tier_name)s</strong> تا %(until_date)s <a %(a_extend)s>(تمدید)</a> شما می‌توانید چندین عضویت را ترکیب کنید (دانلودهای سریع در ۲۴ ساعت با هم جمع می‌شوند). عضویت: <strong>هیچ‌کدام</strong> <a %(a_become)s>(عضو شوید)</a> در صورت تمایل به ارتقاء عضویت خود به سطح بالاتر، با آنا در %(email)s تماس بگیرید. پروفایل عمومی: %(profile_link)s کلید مخفی (به اشتراک نگذارید!): %(secret_key)s نمایش اینجا به ما بپیوندید! برای پیوستن به گروه ما به <a %(a_tier)s>سطح بالاتر</a> ارتقا دهید. گروه اختصاصی Telegram: %(link)s حساب کاربری کدام دانلودها؟ ورود کلید خود را گم نکنید! کلید مخفی نامعتبر. کلید خود را تأیید کرده و دوباره تلاش کنید، یا به صورت جایگزین یک حساب جدید ثبت کنید. کلید مخفی کلید مخفی خود را برای ورود وارد کنید: حساب قدیمی مبتنی بر ایمیل؟ <a %(a_open)s>ایمیل خود را اینجا وارد کنید</a>. ثبت حساب کاربری جدید هنوز حساب کاربری ندارید؟ ثبت‌نام موفقیت‌آمیز! کلید مخفی شما: <span %(span_key)s>%(key)s</span> این کلید را با دقت نگه دارید. اگر آن را گم کنید، دسترسی به حساب خود را از دست خواهید داد. <li %(li_item)s><strong>نشانک.</strong> می‌توانید این صفحه را نشانک‌گذاری کنید تا کلید خود را بازیابی کنید.</li><li %(li_item)s><strong>دانلود.</strong> برای دانلود کلید خود، <a %(a_download)s>این لینک</a> را کلیک کنید.</li><li %(li_item)s><strong>مدیر رمز عبور.</strong> از یک مدیر رمز عبور برای ذخیره کلید هنگام وارد کردن آن در زیر استفاده کنید.</li> ورود / ثبت‌نام تأیید مرورگر هشدار: کد دارای کاراکترهای نادرست یونیکد است و ممکن است در موقعیت‌های مختلف به درستی عمل نکند. باینری خام را می‌توان از نمایه base64 در URL رمزگشایی کرد. توضیحات برچسب پیشوند URL برای یک کد خاص وب‌سایت کدهایی که با “%(prefix_label)s” شروع می‌شوند لطفاً این صفحات را اسکرپ نکنید. در عوض، ما توصیه می‌کنیم <a %(a_import)s>تولید</a> یا <a %(a_download)s>دانلود</a> پایگاه‌های داده ElasticSearch و MariaDB ما و اجرای <a %(a_software)s>کد منبع باز</a> ما. داده‌های خام را می‌توان به صورت دستی از طریق فایل‌های JSON مانند <a %(a_json_file)s>این یکی</a> کاوش کرد. کمتر از %(count)s ضبط آدرس عمومی کاوشگر کدها فهرست کدهایی را که رکوردها با آن‌ها برچسب‌گذاری شده‌اند، با پیشوند کاوش کنید. ستون «رکوردها» تعداد رکوردهایی را نشان می‌دهد که با کدهای دارای پیشوند مشخص برچسب‌گذاری شده‌اند، همان‌طور که در موتور جستجو دیده می‌شود (شامل رکوردهای فقط فراداده). ستون «کدها» نشان می‌دهد که چند کد واقعی دارای پیشوند مشخص هستند. پیشوند کد شناخته شده «%(key)s» بیشتر… پیشوند page.codes.record_starting_with کدها ضبط‌ها «%%ها» با مقدار کد جایگزین خواهند شد جستجو در آرشیو آنا کدها آدرس برای کد خاص: “%(url)s” تولید این صفحه ممکن است مدتی طول بکشد، به همین دلیل نیاز به کپچای Cloudflare دارد. <a %(a_donate)s>اعضا</a> می‌توانند کپچا را رد کنند. سوءاستفاده گزارش شده: نسخه بهتر آیا می‌خواهید این کاربر را به دلیل رفتار توهین‌آمیز یا نامناسب گزارش دهید؟ مشکل فایل: %(file_issue)s نظر مخفی پاسخ گزارش سوءاستفاده شما این کاربر را به دلیل سوءاستفاده گزارش دادید. ادعاهای حق‌تألیف به این ایمیل نادیده گرفته خواهد شد؛ لطفاً از فرم استفاده کنید. نمایش ایمیل ما بسیار از بازخورد و سوالات شما استقبال می‌کنیم! با این حال، به دلیل حجم زیاد اسپم و ایمیل‌های بی‌معنی که دریافت می‌کنیم، لطفاً برای تأیید اینکه شرایط تماس با ما را درک کرده‌اید، جعبه‌ها را علامت بزنید. هر روش دیگری برای تماس با ما در مورد ادعاهای حق چاپ به‌طور خودکار حذف خواهد شد. برای ادعاهای DMCA / حق‌تکثیر، از <a %(a_copyright)s>این فرم</a> استفاده کنید. ایمیل تماس آدرس‌های اینترنتی در آرشیو آنا (الزامی). هر کدام در یک خط. لطفاً فقط آدرس‌هایی را شامل کنید که دقیقاً همان نسخه از کتاب را توصیف می‌کنند. اگر می‌خواهید برای چندین کتاب یا چندین نسخه ادعا کنید، لطفاً این فرم را چندین بار ارسال کنید. ادعاهایی که چندین کتاب یا نسخه را با هم ترکیب می‌کنند رد خواهند شد. آدرس (الزامی) توضیح واضح از منبع (الزامی) ایمیل (الزامی) آدرس‌های اینترنتی منبع، هر کدام در یک خط (الزامی). لطفاً تا حد امکان شامل کنید تا به ما در تأیید ادعای شما کمک کند (مثلاً Amazon، WorldCat، Google Books، DOI). شماره‌های ISBN منبع (در صورت وجود). هر کدام در یک خط. لطفاً فقط آن‌هایی را شامل کنید که دقیقاً با نسخه‌ای که برای آن ادعای حق نشر می‌کنید مطابقت دارند. نام شما (الزامی) ❌ مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. ✅ از ارسال ادعای حق نشر خود متشکریم. ما آن را در اسرع وقت بررسی خواهیم کرد. لطفاً صفحه را مجدداً بارگذاری کنید تا یکی دیگر ارسال کنید. <a %(a_openlib)s>آدرس‌های Open Library</a> منبع، هر کدام در یک خط. لطفاً لحظه‌ای وقت بگذارید و در Open Library به دنبال منبع خود بگردید. این به ما کمک می‌کند تا ادعای شما را تأیید کنیم. شماره تلفن (الزامی) بیانیه و امضا (الزامی) ارسال ادعا اگر شما یک ادعای DMCA یا دیگر ادعاهای حق نشر دارید، لطفاً این فرم را با دقت هرچه تمام‌تر پر کنید. اگر با مشکلی مواجه شدید، لطفاً با آدرس اختصاصی DMCA ما تماس بگیرید: %(email)s. توجه داشته باشید که ادعاهایی که به این آدرس ایمیل می‌شوند پردازش نخواهند شد، این آدرس فقط برای سوالات است. لطفاً برای ارسال ادعاهای خود از فرم زیر استفاده کنید. فرم ادعای DMCA / حق نشر نمونه رکورد در آرشیو آنا تورنت‌ها توسط آرشیو آنا فرمت ظروف آرشیو آنا اسکریپت‌ها برای وارد کردن فراداده اگر علاقه‌مند به آینه‌سازی این مجموعه داده برای <a %(a_archival)s>آرشیو</a> یا اهداف <a %(a_llm)s>آموزش LLM</a> هستید، لطفاً با ما تماس بگیرید. آخرین به‌روزرسانی: %(date)s وب‌سایت اصلی %(source)s مستندات فراداده (بیشتر فیلدها) فایل‌های آینه شده توسط آرشیو آنا: %(count)s (%(percent)s%%) منابع تعداد کل فایل‌ها: %(count)s حجم کل فایل‌ها: %(size)s پست وبلاگ ما درباره این داده‌ها <a %(duxiu_link)s>Duxiu</a> یک پایگاه داده عظیم از کتاب‌های اسکن شده است که توسط <a %(superstar_link)s>گروه کتابخانه دیجیتال SuperStar</a> ایجاد شده است. بیشتر این کتاب‌ها، کتاب‌های دانشگاهی هستند که به منظور دسترسی دیجیتالی به دانشگاه‌ها و کتابخانه‌ها اسکن شده‌اند. برای مخاطبان انگلیسی‌زبان ما، <a %(princeton_link)s>پرینستون</a> و <a %(uw_link)s>دانشگاه واشنگتن</a> مرورهای خوبی دارند. همچنین یک مقاله عالی وجود دارد که اطلاعات بیشتری ارائه می‌دهد: <a %(article_link)s>“دیجیتالی کردن کتاب‌های چینی: مطالعه موردی موتور جستجوی DuXiu Scholar”</a>. کتاب‌های Duxiu مدت‌هاست که در اینترنت چینی به صورت غیرقانونی توزیع می‌شوند. معمولاً این کتاب‌ها توسط فروشندگان با قیمتی کمتر از یک دلار فروخته می‌شوند. آن‌ها معمولاً با استفاده از معادل چینی Google Drive توزیع می‌شوند که اغلب برای فضای ذخیره‌سازی بیشتر هک شده است. برخی جزئیات فنی را می‌توانید <a %(link1)s>اینجا</a> و <a %(link2)s>اینجا</a> پیدا کنید. اگرچه کتاب‌ها به صورت نیمه‌عمومی توزیع شده‌اند، اما به دست آوردن آن‌ها به صورت عمده بسیار دشوار است. ما این موضوع را در لیست کارهای خود قرار داده بودیم و چندین ماه کار تمام وقت برای آن اختصاص دادیم. با این حال، در اواخر سال ۲۰۲۳ یک داوطلب فوق‌العاده، شگفت‌انگیز و با استعداد با ما تماس گرفت و به ما گفت که تمام این کارها را با هزینه زیاد انجام داده است. آن‌ها مجموعه کامل را با ما به اشتراک گذاشتند، بدون اینکه چیزی در ازای آن انتظار داشته باشند، جز تضمین حفظ طولانی‌مدت. واقعاً قابل توجه است. اطلاعات بیشتر از داوطلبان ما (یادداشت‌های خام): اقتباس شده از <a %(a_href)s>پست وبلاگ</a> ما. DuXiu 读秀 page.datasets.file این مجموعه داده به مجموعه داده <a %(a_datasets_openlib)s>Open Library</a> نزدیک است. شامل یک خراش از تمام فراداده‌ها و بخش بزرگی از فایل‌های کتابخانه دیجیتال کنترل‌شده IA است. به‌روزرسانی‌ها در قالب <a %(a_aac)s>فرمت کانتینرهای آرشیو آنا</a> منتشر می‌شوند. این رکوردها مستقیماً از مجموعه داده‌های Open Library ارجاع داده می‌شوند، اما همچنین شامل رکوردهایی هستند که در Open Library نیستند. ما همچنین تعدادی فایل داده داریم که توسط اعضای جامعه در طول سال‌ها جمع‌آوری شده‌اند. این مجموعه از دو بخش تشکیل شده است. شما به هر دو بخش نیاز دارید تا تمام داده‌ها را دریافت کنید (به جز تورنت‌های منسوخ شده، که در صفحه تورنت‌ها خط خورده‌اند). کتابخانه امانت دیجیتال اولین انتشار ما، قبل از اینکه به فرمت <a %(a_aac)s>ظروف آرشیو آنا (AAC)</a> استانداردسازی کنیم. شامل فراداده (به صورت json و xml)، پی‌دی‌اف‌ها (از سیستم‌های امانت دیجیتال acsm و lcpdf)، و تصاویر کوچک جلد کتاب‌ها. انتشارهای جدید تدریجی، با استفاده از AAC. فقط شامل فراداده با برچسب زمانی بعد از 2023-01-01 است، زیرا بقیه قبلاً توسط "ia" پوشش داده شده‌اند. همچنین تمام فایل‌های پی‌دی‌اف، این بار از سیستم‌های امانت acsm و "bookreader" (خواننده وب IA). با وجود اینکه نام دقیقاً درست نیست، ما همچنان فایل‌های bookreader را در مجموعه ia2_acsmpdf_files قرار می‌دهیم، زیرا آنها به طور متقابل انحصاری هستند. امانت دیجیتال کنترل‌شده IA ۹۸%%+ از فایل‌ها قابل جستجو هستند. ماموریت ما آرشیو کردن تمام کتاب‌های جهان (همچنین مقالات، مجلات و غیره) و دسترسی گسترده به آن‌ها است. ما معتقدیم که تمام کتاب‌ها باید به طور گسترده آینه‌سازی شوند تا اطمینان از افزونگی و مقاومت حاصل شود. به همین دلیل است که ما فایل‌ها را از منابع مختلف جمع‌آوری می‌کنیم. برخی منابع کاملاً باز هستند و می‌توانند به صورت عمده آینه‌سازی شوند (مانند Sci-Hub). دیگران بسته و محافظت شده هستند، بنابراین ما سعی می‌کنیم آن‌ها را خراشیده کنیم تا کتاب‌هایشان را "آزاد" کنیم. برخی دیگر در جایی بین این دو قرار دارند. تمام داده‌های ما می‌توانند <a %(a_torrents)s>تورنت</a> شوند و تمام فراداده‌های ما می‌توانند به صورت <a %(a_anna_software)s>تولید</a> شده یا به عنوان پایگاه‌های داده ElasticSearch و MariaDB <a %(a_elasticsearch)s>دانلود</a> شوند. داده‌های خام می‌توانند به صورت دستی از طریق فایل‌های JSON مانند <a %(a_dbrecord)s>این</a> کاوش شوند. فراداده وب‌سایت ISBN آخرین به‌روزرسانی: %(isbn_country_date)s (%(link)s) منابع آژانس بین‌المللی ISBN به طور منظم محدوده‌هایی را که به آژانس‌های ملی ISBN اختصاص داده است، منتشر می‌کند. از این طریق می‌توانیم بفهمیم که این ISBN به کدام کشور، منطقه یا گروه زبانی تعلق دارد. ما در حال حاضر از این داده‌ها به طور غیرمستقیم، از طریق کتابخانه پایتون <a %(a_isbnlib)s>isbnlib</a> استفاده می‌کنیم. اطلاعات کشور ISBN این یک دامپ از تعداد زیادی تماس به isbndb.com در طول سپتامبر ۲۰۲۲ است. ما سعی کردیم تمام محدوده‌های ISBN را پوشش دهیم. این‌ها حدود ۳۰.۹ میلیون رکورد هستند. در وب‌سایت خود ادعا می‌کنند که در واقع ۳۲.۶ میلیون رکورد دارند، بنابراین ممکن است به نوعی برخی را از دست داده باشیم، یا <em>آن‌ها</em> ممکن است اشتباهی کرده باشند. پاسخ‌های JSON تقریباً خام از سرور آن‌ها هستند. یکی از مشکلات کیفیت داده که متوجه شدیم این است که برای شماره‌های ISBN-13 که با پیشوندی غیر از "978-" شروع می‌شوند، همچنان یک فیلد "isbn" شامل می‌شود که به سادگی شماره ISBN-13 با حذف سه رقم اول (و محاسبه مجدد رقم چک) است. این به وضوح اشتباه است، اما این روشی است که آن‌ها به نظر می‌رسد انجام می‌دهند، بنابراین ما آن را تغییر ندادیم. یکی دیگر از مشکلات احتمالی که ممکن است با آن مواجه شوید، این است که فیلد "isbn13" تکراری دارد، بنابراین نمی‌توانید از آن به عنوان کلید اصلی در یک پایگاه داده استفاده کنید. فیلدهای "isbn13"+"isbn" به نظر می‌رسد که منحصربه‌فرد هستند. انتشار ۱ (۲۰۲۲-۱۰-۳۱) تورنت‌های داستانی عقب هستند (اگرچه شناسه‌های ~۴-۶M تورنت نشده‌اند چون با تورنت‌های Zlib ما همپوشانی دارند). پست وبلاگ ما درباره انتشار کتاب‌های کمیک تورنت‌های کمیک در آرشیو آنا برای داستان پشت شاخه‌های مختلف Library Genesis، به صفحه <a %(a_libgen_rs)s>Libgen.rs</a> مراجعه کنید. محتوای Libgen.li شامل بیشتر همان محتوا و فراداده‌های Libgen.rs است، اما برخی مجموعه‌های اضافی مانند کمیک‌ها، مجلات و اسناد استاندارد نیز دارد. همچنین <a %(a_scihub)s>Sci-Hub</a> را در فراداده و موتور جستجوی خود ادغام کرده است که ما از آن برای پایگاه داده خود استفاده می‌کنیم. فراداده این کتابخانه به صورت رایگان <a %(a_libgen_li)s>در libgen.li</a> در دسترس است. با این حال، این سرور کند است و از ادامه اتصال‌های قطع شده پشتیبانی نمی‌کند. همان فایل‌ها نیز در <a %(a_ftp)s>یک سرور FTP</a> موجود هستند که بهتر عمل می‌کند. غیرداستانی نیز به نظر می‌رسد که از هم جدا شده است، اما بدون تورنت‌های جدید. به نظر می‌رسد این اتفاق از اوایل سال ۲۰۲۲ رخ داده است، اگرچه ما این را تأیید نکرده‌ایم. به گفته مدیر Libgen.li، مجموعه "fiction_rus" (داستان‌های تخیلی روسی) باید توسط تورنت‌هایی که به طور منظم از <a %(a_booktracker)s>booktracker.org</a> منتشر می‌شوند، پوشش داده شود، به ویژه تورنت‌های <a %(a_flibusta)s>flibusta</a> و <a %(a_librusec)s>lib.rus.ec</a> (که ما آن‌ها را <a %(a_torrents)s>اینجا</a> لینک کمکی می‌کنیم، اگرچه هنوز مشخص نکرده‌ایم که کدام تورنت‌ها به کدام فایل‌ها مربوط می‌شوند). مجموعه داستان‌های تخیلی تورنت‌های خود را دارد (جدا شده از <a %(a_href)s>Libgen.rs</a>) که از %(start)s شروع می‌شود. برخی از محدوده‌ها بدون تورنت (مانند محدوده‌های داستانی f_3463000 تا f_4260000) احتمالاً فایل‌های Z-Library (یا دیگر تکراری‌ها) هستند، اگرچه ممکن است بخواهیم برخی از تکراری‌ها را حذف کنیم و برای فایل‌های منحصر به فرد lgli در این محدوده‌ها تورنت بسازیم. آمار برای همه مجموعه‌ها را می‌توانید <a %(a_href)s>در وب‌سایت libgen</a> پیدا کنید. تورنت‌ها برای بیشتر محتوای اضافی در دسترس هستند، به ویژه تورنت‌های کتاب‌های کمیک، مجلات و اسناد استاندارد که با همکاری آرشیو آنا منتشر شده‌اند. توجه داشته باشید که فایل‌های تورنتی که به "libgen.is" اشاره دارند، به طور صریح لینک‌های کمکی <a %(a_libgen)s>Libgen.rs</a> هستند ("is." یک دامنه متفاوت است که توسط Libgen.rs استفاده می‌شود). یک منبع مفید در استفاده از فراداده <a %(a_href)s>این صفحه</a> است. %(icon)s مجموعه "fiction_rus" (داستان‌های تخیلی روسی) آن‌ها تورنت‌های اختصاصی ندارد، اما توسط تورنت‌های دیگر پوشش داده می‌شود و ما یک <a %(fiction_rus)s>لینک کمکی</a> نگه می‌داریم. تورنت‌های داستان‌های تخیلی روسی در آرشیو آنا تورنت‌های داستانی در آرشیو آنا انجمن بحث و گفتگو فراداده فراداده از طریق FTP تورنت‌های مجلات در آرشیو آنا اطلاعات فیلد فراداده لینک کمکی سایر تورنت‌ها (و تورنت‌های داستانی و کمیک منحصر به فرد) تورنت‌های اسناد استاندارد در آرشیو آنا Libgen.li تورنت‌ها توسط آرشیو آنا (جلد کتاب‌ها) Library Genesis به خاطر ارائه سخاوتمندانه داده‌های خود به صورت عمده از طریق تورنت‌ها شناخته شده است. مجموعه Libgen ما شامل داده‌های کمکی است که آنها به طور مستقیم منتشر نمی‌کنند، در همکاری با آنها. از همه کسانی که با Library Genesis همکاری کرده‌اند، بسیار متشکریم! وبلاگ ما درباره انتشار جلد کتاب‌ها این صفحه درباره نسخه “.rs” است. این نسخه به خاطر انتشار مداوم فراداده‌ها و محتوای کامل فهرست کتاب‌هایش شناخته شده است. مجموعه کتاب‌های آن به دو بخش داستانی و غیر داستانی تقسیم می‌شود. یک منبع مفید برای استفاده از فراداده <a %(a_metadata)s>این صفحه</a> است (محدوده‌های IP را مسدود می‌کند، ممکن است نیاز به VPN باشد). از مارس ۲۰۲۴، تورنت‌های جدید در <a %(a_href)s>این موضوع انجمن</a> منتشر می‌شوند (محدوده‌های IP را مسدود می‌کند، ممکن است نیاز به VPN باشد). تورنت‌های داستانی در آرشیو آنا تورنت‌های داستانی Libgen.rs انجمن بحث و گفتگو Libgen.rs فراداده Libgen.rs اطلاعات میدان فراداده Libgen.rs تورنت‌های غیر داستانی Libgen.rs تورنت‌های غیر داستانی در آرشیو آنا %(example)s برای یک کتاب داستانی. این <a %(blog_post)s>اولین انتشار</a> نسبتاً کوچک است: حدود ۳۰۰ گیگابایت جلد کتاب از شاخه Libgen.rs، هم داستانی و هم غیر داستانی. آنها به همان روشی که در libgen.rs ظاهر می‌شوند، سازماندهی شده‌اند، به عنوان مثال: %(example)s برای یک کتاب غیر داستانی. همانند مجموعه Z-Library، ما همه آنها را در یک فایل بزرگ .tar قرار داده‌ایم که می‌توان با استفاده از <a %(a_ratarmount)s>ratarmount</a> آن را نصب کرد اگر می‌خواهید فایل‌ها را مستقیماً سرو کنید. انتشار ۱ (%(date)s) داستان کوتاه درباره شاخه‌های مختلف Library Genesis (یا "Libgen") این است که با گذشت زمان، افراد مختلفی که با Library Genesis درگیر بودند، اختلاف پیدا کردند و راه‌های جداگانه‌ای را پیش گرفتند. طبق این <a %(a_mhut)s>پست انجمن</a>، Libgen.li در اصل در “http://free-books.dontexist.com” میزبانی شده بود. نسخه ".fun" توسط بنیان‌گذار اصلی ایجاد شد. این نسخه در حال بازسازی به نفع یک نسخه جدید و توزیع‌شده‌تر است. نسخه <a %(a_li)s>".li"</a> مجموعه عظیمی از کمیک‌ها و همچنین محتوای دیگر دارد که هنوز برای دانلود حجیم از طریق تورنت‌ها در دسترس نیست. این نسخه یک مجموعه تورنت جداگانه از کتاب‌های داستانی دارد و شامل فراداده <a %(a_scihub)s>Sci-Hub</a> در پایگاه داده خود است. نسخه ".rs" داده‌های بسیار مشابهی دارد و بیشتر به طور مداوم مجموعه خود را در تورنت‌های حجیم منتشر می‌کند. این نسخه به طور تقریبی به دو بخش "داستانی" و "غیر داستانی" تقسیم شده است. در اصل در “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> به نوعی نیز یک شاخه از Library Genesis است، اگرچه آنها نام متفاوتی برای پروژه خود استفاده کردند. Libgen.rs ما همچنین مجموعه خود را با منابع فقط فراداده غنی می‌کنیم، که می‌توانیم آن‌ها را با فایل‌ها مطابقت دهیم، مثلاً با استفاده از شماره‌های ISBN یا فیلدهای دیگر. در زیر یک نمای کلی از آن‌ها آمده است. باز هم، برخی از این منابع کاملاً باز هستند، در حالی که برای دیگران باید آن‌ها را خراش دهیم. توجه داشته باشید که در جستجوی فراداده، ما رکوردهای اصلی را نشان می‌دهیم. ما هیچ ادغامی از رکوردها انجام نمی‌دهیم. منابع فقط فراداده Open Library یک پروژه متن باز توسط Internet Archive است که هدف آن فهرست کردن هر کتاب در جهان است. این پروژه یکی از بزرگترین عملیات‌های اسکن کتاب در جهان را دارد و بسیاری از کتاب‌ها را برای امانت دیجیتال در دسترس دارد. فهرست فراداده کتاب‌های آن برای دانلود رایگان در دسترس است و در آرشیو آنا گنجانده شده است (اگرچه در حال حاضر در جستجو نیست، مگر اینکه به طور صریح برای یک شناسه Open Library جستجو کنید). Open Library به‌جز تکراری‌ها آخرین به‌روزرسانی درصد تعداد فایل‌ها %% توسط AA لینک کمکی شده / تورنت‌ها موجود است اندازه منبع در زیر یک بررسی اجمالی سریع از منابع فایل‌های موجود در آرشیو آنا آمده است. از آنجا که کتابخانه‌های سایه‌ای اغلب داده‌ها را از یکدیگر همگام‌سازی می‌کنند، همپوشانی قابل توجهی بین کتابخانه‌ها وجود دارد. به همین دلیل است که اعداد به مجموع نمی‌رسند. درصد "لینک کمکی و بذر شده توسط آرشیو آنا" نشان می‌دهد که چند فایل را خودمان لینک کمکی کرده‌ایم. ما این فایل‌ها را به صورت عمده از طریق تورنت‌ها بذر می‌کنیم و آن‌ها را برای دانلود مستقیم از طریق وب‌سایت‌های شریک در دسترس قرار می‌دهیم. بررسی اجمالی مجموع تورنت‌ها در آرشیو آنا برای اطلاعات بیشتر درباره Sci-Hub، لطفاً به <a %(a_scihub)s>وب‌سایت رسمی</a>، <a %(a_wikipedia)s>صفحه ویکی‌پدیا</a> و این <a %(a_radiolab)s>مصاحبه پادکست</a> مراجعه کنید. توجه داشته باشید که Sci-Hub از <a %(a_reddit)s>سال ۲۰۲۱ مسدود شده است</a>. قبلاً نیز مسدود شده بود، اما در سال ۲۰۲۱ چند میلیون مقاله اضافه شد. با این حال، تعداد محدودی مقاله به مجموعه‌های "scimag" Libgen اضافه می‌شود، هرچند که به اندازه‌ای نیست که تورنت‌های جدیدی ایجاد شود. ما از فراداده Sci-Hub که توسط <a %(a_libgen_li)s>Libgen.li</a> در مجموعه "scimag" ارائه شده است، استفاده می‌کنیم. همچنین از مجموعه داده <a %(a_dois)s>dois-2022-02-12.7z</a> استفاده می‌کنیم. توجه داشته باشید که تورنت‌های "smarch" <a %(a_smarch)s>منسوخ شده‌اند</a> و بنابراین در لیست تورنت‌های ما گنجانده نشده‌اند. تورنت‌ها در Libgen.li تورنت‌ها در Libgen.rs فراداده و تورنت‌ها به‌روزرسانی‌ها در Reddit مصاحبه پادکست صفحه ویکی‌پدیا Sci-Hub Sci-Hub: از سال ۲۰۲۱ متوقف شده؛ بیشتر از طریق تورنت‌ها موجود است Libgen.li: از آن زمان اضافه‌های جزئی</div> برخی از کتابخانه‌های منبع، به اشتراک‌گذاری انبوه داده‌های خود از طریق تورنت‌ها را ترویج می‌کنند، در حالی که دیگران به راحتی مجموعه خود را به اشتراک نمی‌گذارند. در مورد دوم، آرشیو آنا سعی می‌کند مجموعه‌های آن‌ها را خراش دهد و در دسترس قرار دهد (به صفحه <a %(a_torrents)s>تورنت‌ها</a> ما مراجعه کنید). همچنین وضعیت‌های میانی وجود دارد، به عنوان مثال، جایی که کتابخانه‌های منبع مایل به اشتراک‌گذاری هستند، اما منابع لازم برای انجام این کار را ندارند. در این موارد، ما نیز سعی می‌کنیم کمک کنیم. در زیر یک نمای کلی از نحوه تعامل ما با کتابخانه‌های منبع مختلف آمده است. کتابخانه‌های منبع %(icon)s پایگاه‌های داده فایل مختلفی که در اینترنت چینی پراکنده شده‌اند؛ هرچند اغلب پایگاه‌های داده پولی هستند %(icon)s بیشتر فایل‌ها فقط با استفاده از حساب‌های پریمیوم BaiduYun قابل دسترسی هستند؛ سرعت دانلود کند. %(icon)s آرشیو آنا مجموعه‌ای از <a %(duxiu)s>فایل‌های DuXiu</a> را مدیریت می‌کند %(icon)s پایگاه‌های داده فراداده مختلفی که در اینترنت چینی پراکنده شده‌اند؛ هرچند اغلب پایگاه‌های داده پولی هستند %(icon)s هیچ تخلیه فراداده‌ای که به راحتی قابل دسترسی باشد برای کل مجموعه آن‌ها موجود نیست. %(icon)s آرشیو آنا مجموعه‌ای از <a %(duxiu)s>فراداده DuXiu</a> را مدیریت می‌کند فایل‌ها %(icon)s فایل‌ها فقط به صورت محدود برای امانت‌گیری در دسترس هستند، با محدودیت‌های مختلف دسترسی %(icon)s آرشیو آنا مجموعه‌ای از <a %(ia)s>فایل‌های IA</a> را مدیریت می‌کند %(icon)s برخی از فراداده‌ها از طریق <a %(openlib)s>پایگاه داده Open Library</a> در دسترس هستند، اما این‌ها کل مجموعه IA را پوشش نمی‌دهند. %(icon)s هیچ تخلیه فراداده‌ای به‌راحتی در دسترس برای کل مجموعه آن‌ها وجود ندارد %(icon)s آرشیو آنا مجموعه‌ای از <a %(ia)s>فراداده IA</a> را مدیریت می‌کند آخرین به‌روزرسانی %(icon)s آرشیو آنا و Libgen.li به صورت مشترک مجموعه‌هایی از <a %(comics)s>کتاب‌های کمیک</a>، <a %(magazines)s>مجلات</a>، <a %(standarts)s>اسناد استاندارد</a> و <a %(fiction)s>داستان‌های تخیلی (جدا شده از Libgen.rs)</a> را مدیریت می‌کنند. %(icon)s تورنت‌های غیرداستانی با Libgen.rs به اشتراک گذاشته می‌شوند (و در <a %(libgenli)s>اینجا</a> آینه شده‌اند). %(icon)s تخلیه‌های فصلی <a %(dbdumps)s>پایگاه داده HTTP</a> %(icon)s تورنت‌های خودکار برای <a %(nonfiction)s>غیرداستانی</a> و <a %(fiction)s>داستانی</a> %(icon)s آرشیو آنا مجموعه‌ای از <a %(covers)s>تورنت‌های جلد کتاب</a> را مدیریت می‌کند %(icon)s تخلیه‌های روزانه <a %(dbdumps)s>پایگاه داده HTTP</a> فراداده %(icon)s تخلیه‌های پایگاه داده ماهانه <a %(dbdumps)s>ماهانه</a> %(icon)s تورنت‌های داده در <a %(scihub1)s>اینجا</a>، <a %(scihub2)s>اینجا</a> و <a %(libgenli)s>اینجا</a> در دسترس هستند %(icon)s برخی فایل‌های جدید به <a %(libgenrs)s>“scimag”</a> لیبجن <a %(libgenli)s>اضافه می‌شوند</a>، اما به اندازه‌ای نیستند که تورنت‌های جدیدی ایجاد کنند %(icon)s Sci-Hub از سال ۲۰۲۱ فایل‌های جدید را متوقف کرده است. %(icon)s تخلیه‌های فراداده در <a %(scihub1)s>اینجا</a> و <a %(scihub2)s>اینجا</a> در دسترس هستند، و همچنین به عنوان بخشی از <a %(libgenli)s>پایگاه داده Libgen.li</a> (که ما استفاده می‌کنیم) منبع %(icon)s منابع کوچکتر یا یکباره مختلف. ما مردم را تشویق می‌کنیم که ابتدا به کتابخانه‌های سایه دیگر آپلود کنند، اما گاهی اوقات مردم مجموعه‌هایی دارند که برای دیگران بیش از حد بزرگ است که بتوانند آن‌ها را مرتب کنند، هرچند به اندازه کافی بزرگ نیستند که دسته‌بندی خاص خود را داشته باشند. %(icon)s به صورت عمده مستقیماً در دسترس نیست، در برابر خراشیدن محافظت شده است %(icon)s آرشیو آنا مجموعه‌ای از <a %(worldcat)s>فراداده OCLC (WorldCat)</a> را مدیریت می‌کند %(icon)s آرشیو آنا و Z-Library به صورت مشترک مجموعه‌ای از <a %(metadata)s>فراداده Z-Library</a> و <a %(files)s>فایل‌های Z-Library</a> را مدیریت می‌کنند. Datasets ما همه منابع فوق را در یک پایگاه داده یکپارچه ترکیب می‌کنیم که از آن برای ارائه این وب‌سایت استفاده می‌کنیم. این پایگاه داده یکپارچه به طور مستقیم در دسترس نیست، اما از آنجا که آرشیو آنا کاملاً متن‌باز است، می‌توان آن را به راحتی به عنوان پایگاه‌های داده ElasticSearch و MariaDB <a %(a_generated)s>تولید</a> یا <a %(a_downloaded)s>دانلود</a> کرد. اسکریپت‌های موجود در آن صفحه به طور خودکار تمام فراداده‌های لازم را از منابع ذکر شده دانلود می‌کنند. اگر مایلید داده‌های ما را قبل از اجرای آن اسکریپت‌ها به صورت محلی بررسی کنید، می‌توانید به فایل‌های JSON ما نگاه کنید، که به فایل‌های JSON دیگر لینک می‌دهند. <a %(a_json)s>این فایل</a> نقطه شروع خوبی است. پایگاه داده یکپارچه تورنت‌ها توسط آرشیو آنا مرور جستجو منابع کوچکتر یا یکباره مختلف. ما مردم را تشویق می‌کنیم که ابتدا به کتابخانه‌های سایه دیگر آپلود کنند، اما گاهی اوقات مردم مجموعه‌هایی دارند که برای دیگران بیش از حد بزرگ است که بتوانند آن‌ها را مرتب کنند، هرچند به اندازه کافی بزرگ نیستند که دسته‌بندی خاص خود را داشته باشند. نمای کلی از <a %(a1)s>صفحه دیتاست‌ها</a>. از <a %(a_href)s>aaaaarg.fail</a>. به نظر می‌رسد که نسبتاً کامل است. از داوطلب ما "cgiym". از یک تورنت <a %(a_href)s><q>ACM Digital Library 2020</q></a>. همپوشانی زیادی با مجموعه مقالات موجود دارد، اما تطابق MD5 بسیار کمی دارد، بنابراین تصمیم گرفتیم آن را به طور کامل نگه داریم. خراش از <q>iRead eBooks</q> (به صورت آوایی <q>ai rit i-books</q>; airitibooks.com)، توسط داوطلب <q>j</q>. متناظر با metadata <q>airitibooks</q> در <a %(a1)s><q>خراش‌های دیگر متادیتا</q></a>. از مجموعه <a %(a1)s><q>کتابخانه اسکندریه</q></a>. بخشی از منبع اصلی، بخشی از the-eye.eu، بخشی از دیگر آینه‌ها. از یک وب‌سایت تورنت کتاب‌های خصوصی، <a %(a_href)s>Bibliotik</a> (که اغلب به عنوان "Bib" شناخته می‌شود)، که کتاب‌ها به صورت تورنت با نام (A.torrent, B.torrent) بسته‌بندی شده و از طریق the-eye.eu توزیع شده‌اند. از داوطلب ما "bpb9v". برای اطلاعات بیشتر درباره <a %(a_href)s>CADAL</a>، به یادداشت‌ها در <a %(a_duxiu)s>صفحه مجموعه داده DuXiu</a> ما مراجعه کنید. بیشتر از داوطلب ما "bpb9v"، عمدتاً فایل‌های DuXiu، همچنین یک پوشه "WenQu" و "SuperStar_Journals" (SuperStar شرکت پشت DuXiu است). از داوطلب ما "cgiym"، متون چینی از منابع مختلف (به صورت زیرشاخه‌ها)، از جمله از <a %(a_href)s>China Machine Press</a> (یک ناشر بزرگ چینی). مجموعه‌های غیر چینی (به صورت زیرشاخه‌ها) از داوطلب ما "cgiym". خراش از کتاب‌هایی درباره معماری چینی، توسط داوطلب <q>cm</q>: <q>من آن را با بهره‌برداری از یک آسیب‌پذیری شبکه در انتشارات به دست آوردم، اما آن حفره اکنون بسته شده است</q>. متناظر با metadata <q>chinese_architecture</q> در <a %(a1)s><q>خراش‌های دیگر متادیتا</q></a>. کتاب‌ها از انتشارات دانشگاهی <a %(a_href)s>De Gruyter</a>، جمع‌آوری شده از چند تورنت بزرگ. خراش از <a %(a_href)s>docer.pl</a>، یک وب‌سایت اشتراک‌گذاری فایل لهستانی که بر روی کتاب‌ها و آثار نوشتاری دیگر متمرکز است. در اواخر 2023 توسط داوطلب "p" خراشیده شد. ما فراداده خوبی از وب‌سایت اصلی نداریم (حتی پسوند فایل‌ها)، اما ما فایل‌های شبیه به کتاب را فیلتر کردیم و اغلب توانستیم فراداده را از خود فایل‌ها استخراج کنیم. کتاب‌های الکترونیکی DuXiu، مستقیماً از DuXiu، جمع‌آوری شده توسط داوطلب "w". فقط کتاب‌های اخیر DuXiu به صورت الکترونیکی در دسترس هستند، بنابراین بیشتر این‌ها باید جدید باشند. فایل‌های باقی‌مانده DuXiu از داوطلب "m"، که در قالب اختصاصی PDG DuXiu نبودند (مجموعه داده اصلی <a %(a_href)s>DuXiu</a>). از منابع اصلی بسیاری جمع‌آوری شده‌اند، متأسفانه بدون حفظ آن منابع در مسیر فایل. <span></span> <span></span> <span></span> خراش از کتاب‌های اروتیک، توسط داوطلب <q>do no harm</q>. متناظر با metadata <q>hentai</q> در <a %(a1)s><q>خراش‌های دیگر متادیتا</q></a>. <span></span> <span></span> مجموعه‌ای که توسط یک ناشر مانگا ژاپنی توسط داوطلب "t" خراشیده شده است. <a %(a_href)s>آرشیوهای قضایی منتخب Longquan</a>، ارائه شده توسط داوطلب "c". خراش از <a %(a_href)s>magzdb.org</a>، یک هم‌پیمان Library Genesis (که در صفحه اصلی libgen.rs لینک شده است) اما نمی‌خواستند فایل‌های خود را مستقیماً ارائه دهند. توسط داوطلب "p" در اواخر 2023 به دست آمده است. <span></span> آپلودهای کوچک مختلف، که به عنوان زیرمجموعه‌های خودشان خیلی کوچک هستند، اما به صورت دایرکتوری‌ها نمایان شده‌اند. کتاب‌های الکترونیکی از AvaxHome، یک وب‌سایت اشتراک‌گذاری فایل روسی. آرشیو روزنامه‌ها و مجلات. متناظر با metadata <q>newsarch_magz</q> در <a %(a1)s><q>خراش‌های دیگر متادیتا</q></a>. خراش از <a %(a1)s>مرکز مستندات فلسفه</a>. مجموعه داوطلب "o" که کتاب‌های لهستانی را مستقیماً از وب‌سایت‌های انتشار اصلی ("صحنه") جمع‌آوری کرده است. مجموعه‌های ترکیبی از <a %(a_href)s>shuge.org</a> توسط داوطلبان "cgiym" و "woz9ts". <span></span> <a %(a_href)s>«کتابخانه امپراتوری ترانتور»</a> (نام‌گذاری شده به نام کتابخانه خیالی)، استخراج شده در سال ۲۰۲۲ توسط داوطلب «t». <span></span> <span></span> <span></span> زیر-زیر-مجموعه‌ها (نمایش داده شده به عنوان دایرکتوری‌ها) از داوطلب «woz9ts»: <a %(a_program_think)s>program-think</a>، <a %(a_haodoo)s>haodoo</a>، <a %(a_skqs)s>skqs</a> (توسط <a %(a_sikuquanshu)s>Dizhi(迪志)</a> در تایوان)، mebook (mebook.cc، کتابخانه کوچک من، my little bookroom — woz9ts: «این سایت عمدتاً بر اشتراک‌گذاری فایل‌های کتاب الکترونیکی با کیفیت بالا تمرکز دارد، برخی از آن‌ها توسط صاحب سایت صفحه‌آرایی شده‌اند. صاحب سایت در سال ۲۰۱۹ <a %(a_arrested)s>دستگیر</a> شد و کسی مجموعه‌ای از فایل‌هایی که او به اشتراک گذاشته بود را جمع‌آوری کرد.»). فایل‌های باقی‌مانده DuXiu از داوطلب «woz9ts»، که در فرمت اختصاصی PDG DuXiu نبودند (هنوز باید به PDF تبدیل شوند). مجموعه "آپلود" به زیرمجموعه‌های کوچکتر تقسیم شده است، که در AACIDs و نام‌های تورنت مشخص شده‌اند. همه زیرمجموعه‌ها ابتدا در برابر مجموعه اصلی تکرارزدایی شده‌اند، هرچند فایل‌های JSON "upload_records" فراداده هنوز حاوی بسیاری از ارجاعات به فایل‌های اصلی هستند. فایل‌های غیرکتاب نیز از بیشتر زیرمجموعه‌ها حذف شده‌اند و معمولاً در "upload_records" JSON ذکر نشده‌اند. زیرمجموعه‌ها عبارتند از: یادداشت‌ها زیرمجموعه بسیاری از زیرمجموعه‌ها خود شامل زیرزیرمجموعه‌ها هستند (مثلاً از منابع اصلی مختلف)، که به عنوان دایرکتوری‌ها در فیلدهای "filepath" نمایش داده می‌شوند. آپلودها به آرشیو آنا پست وبلاگ ما درباره این داده‌ها <a %(a_worldcat)s>WorldCat</a> یک پایگاه داده اختصاصی توسط سازمان غیرانتفاعی <a %(a_oclc)s>OCLC</a> است که فراداده کتابخانه‌ها از سراسر جهان را جمع‌آوری می‌کند. احتمالاً بزرگترین مجموعه فراداده کتابخانه‌ای در جهان است. در اکتبر ۲۰۲۳ ما یک <a %(a_scrape)s>اسکرپ جامع</a> از پایگاه داده OCLC (WorldCat) را در <a %(a_aac)s>فرمت کانتینرهای آرشیو آنا</a> منتشر کردیم. اکتبر ۲۰۲۳، انتشار اولیه: OCLC (WorldCat) تورنت‌ها توسط آرشیو آنا نمونه ضبط در آرشیو آنا (مجموعه اصلی) نمونه ضبط در آرشیو آنا (مجموعه «zlib3») تورنت‌ها توسط آرشیو آنا (فراداده + محتوا) پست وبلاگ درباره انتشار ۱ پست وبلاگ درباره انتشار ۲ در اواخر سال ۲۰۲۲، بنیان‌گذاران ادعایی Z-Library دستگیر شدند و دامنه‌ها توسط مقامات ایالات متحده توقیف شد. از آن زمان، وب‌سایت به آرامی دوباره آنلاین شده است. مشخص نیست که در حال حاضر چه کسی آن را اداره می‌کند. به‌روزرسانی تا فوریه ۲۰۲۳. Z-Library ریشه در جامعه <a %(a_href)s>Library Genesis</a> دارد و در ابتدا با داده‌های آن‌ها شروع به کار کرد. از آن زمان، به طور قابل توجهی حرفه‌ای‌تر شده و رابط کاربری بسیار مدرن‌تری دارد. بنابراین، آن‌ها قادر به دریافت کمک‌های مالی بیشتری هستند، هم برای بهبود وب‌سایت خود و هم برای دریافت کتاب‌های جدید. آن‌ها مجموعه بزرگی علاوه بر Library Genesis جمع‌آوری کرده‌اند. این مجموعه شامل سه بخش است. صفحات توضیحات اصلی برای دو بخش اول در زیر حفظ شده‌اند. شما به هر سه بخش نیاز دارید تا تمام داده‌ها را دریافت کنید (به جز تورنت‌های جایگزین شده، که در صفحه تورنت‌ها خط خورده‌اند). %(title)s: اولین انتشار ما. این اولین انتشار چیزی بود که آن زمان «آینه کتابخانه دزدان دریایی» («pilimi») نامیده می‌شد. %(title)s: انتشار دوم، این بار با تمام فایل‌ها در فایل‌های .tar بسته‌بندی شده. %(title)s: انتشارهای جدید افزایشی، با استفاده از <a %(a_href)s>فرمت کانتینرهای آرشیو آنا (AAC)</a>، اکنون با همکاری تیم Z-Library منتشر شده است. لینک کمکی اولیه با زحمت فراوان در طول سال‌های ۲۰۲۱ و ۲۰۲۲ به دست آمد. در این مرحله کمی قدیمی شده است: وضعیت مجموعه در ژوئن ۲۰۲۱ را منعکس می‌کند. ما در آینده این را به‌روزرسانی خواهیم کرد. در حال حاضر تمرکز ما بر انتشار اولین نسخه است. از آنجا که Library Genesis قبلاً با تورنت‌های عمومی حفظ شده و در Z-Library گنجانده شده است، ما در ژوئن ۲۰۲۲ یک حذف تکراری اولیه در برابر Library Genesis انجام دادیم. برای این کار از هش‌های MD5 استفاده کردیم. احتمالاً محتوای تکراری زیادی در کتابخانه وجود دارد، مانند فرمت‌های مختلف فایل با همان کتاب. این کار به‌طور دقیق سخت است، بنابراین ما آن را انجام نمی‌دهیم. پس از حذف تکراری، بیش از ۲ میلیون فایل باقی ماند که مجموعاً کمی کمتر از ۷ ترابایت است. مجموعه شامل دو بخش است: یک فایل MySQL “.sql.gz” از فراداده‌ها و ۷۲ فایل تورنت هر کدام حدود ۵۰-۱۰۰ گیگابایت. فراداده‌ها شامل داده‌هایی است که توسط وب‌سایت Z-Library گزارش شده است (عنوان، نویسنده، توضیحات، نوع فایل)، همچنین اندازه واقعی فایل و md5sum که ما مشاهده کردیم، زیرا گاهی اوقات این‌ها با هم مطابقت ندارند. به نظر می‌رسد که برای برخی از فایل‌ها، خود Z-Library فراداده‌های نادرستی دارد. ممکن است ما نیز در برخی موارد خاص فایل‌ها را به‌طور نادرست دانلود کرده باشیم که در آینده سعی خواهیم کرد آن‌ها را شناسایی و اصلاح کنیم. فایل‌های تورنت بزرگ شامل داده‌های واقعی کتاب‌ها هستند، با شناسه Z-Library به عنوان نام فایل. پسوند فایل‌ها را می‌توان با استفاده از فایل فراداده بازسازی کرد. مجموعه‌ای از محتوای غیر داستانی و داستانی است (مانند Library Genesis جدا نشده است). کیفیت نیز بسیار متغیر است. این اولین نسخه اکنون به‌طور کامل در دسترس است. توجه داشته باشید که فایل‌های تورنت فقط از طریق لینک کمکی Tor ما در دسترس هستند. انتشار ۱ (%(date)s) این یک فایل تورنت اضافی است. حاوی اطلاعات جدیدی نیست، اما دارای داده‌هایی است که محاسبه آن‌ها ممکن است مدتی طول بکشد. این امر آن را راحت می‌کند، زیرا دانلود این تورنت اغلب سریع‌تر از محاسبه آن از ابتدا است. به‌ویژه، شامل شاخص‌های SQLite برای فایل‌های tar است، برای استفاده با <a %(a_href)s>ratarmount</a>. ضمیمه انتشار ۲ (%(date)s) ما تمام کتاب‌هایی را که بین آخرین لینک کمکی ما و آگوست ۲۰۲۲ به Z-Library اضافه شده بودند، به دست آورده‌ایم. همچنین برخی از کتاب‌هایی را که در اولین بار از دست داده بودیم، جمع‌آوری کرده‌ایم. در مجموع، این مجموعه جدید حدود ۲۴ ترابایت است. باز هم، این مجموعه در برابر Library Genesis حذف تکراری شده است، زیرا تورنت‌هایی برای آن مجموعه در دسترس هستند. داده‌ها به‌طور مشابه به اولین نسخه سازماندهی شده‌اند. یک فایل MySQL “.sql.gz” از فراداده‌ها وجود دارد که شامل تمام فراداده‌های اولین نسخه نیز می‌شود و بنابراین آن را جایگزین می‌کند. ما همچنین چند ستون جدید اضافه کرده‌ایم: ما این را دفعه قبل ذکر کردیم، اما برای روشن شدن: “filename” و “md5” ویژگی‌های واقعی فایل هستند، در حالی که “filename_reported” و “md5_reported” آن چیزی است که از Z-Library جمع‌آوری کرده‌ایم. گاهی اوقات این دو با هم مطابقت ندارند، بنابراین هر دو را شامل کرده‌ایم. برای این نسخه، ما collation را به “utf8mb4_unicode_ci” تغییر دادیم که باید با نسخه‌های قدیمی‌تر MySQL سازگار باشد. فایل‌های داده مشابه دفعه قبل هستند، اگرچه بسیار بزرگتر هستند. ما واقعاً نمی‌توانستیم تعداد زیادی فایل تورنت کوچکتر ایجاد کنیم. “pilimi-zlib2-0-14679999-extra.torrent” شامل تمام فایل‌هایی است که در نسخه قبلی از دست داده بودیم، در حالی که تورنت‌های دیگر همه محدوده‌های جدید شناسه هستند.  <strong>به‌روزرسانی %(date)s:</strong> ما بیشتر تورنت‌های خود را بیش از حد بزرگ کردیم، که باعث مشکل در کلاینت‌های تورنت شد. ما آن‌ها را حذف کرده و تورنت‌های جدیدی منتشر کردیم. <strong>به‌روزرسانی %(date)s:</strong> هنوز هم فایل‌های زیادی وجود داشت، بنابراین آن‌ها را در فایل‌های tar بسته‌بندی کرده و دوباره تورنت‌های جدیدی منتشر کردیم. %(key)s: آیا این فایل در Library Genesis وجود دارد، در مجموعه غیر داستانی یا داستانی (با md5 مطابقت داده شده). %(key)s: این فایل در کدام تورنت قرار دارد. %(key)s: زمانی که نتوانستیم کتاب را دانلود کنیم. انتشار ۲ (%(date)s) انتشارهای Zlib (صفحات توضیحات اصلی) دامنه Tor وب‌سایت اصلی استخراج Z-Library مجموعه "چینی" در Z-Library به نظر می‌رسد همان مجموعه DuXiu ما باشد، اما با MD5های متفاوت. ما این فایل‌ها را از تورنت‌ها حذف می‌کنیم تا از تکرار جلوگیری کنیم، اما همچنان آن‌ها را در شاخص جستجوی خود نشان می‌دهیم. فراداده شما %(percentage)s%% دانلود سریع اضافی دریافت می‌کنید، زیرا توسط کاربر %(profile_link)s معرفی شده‌اید. این مورد برای کل دوره عضویت اعمال می‌شود. کمک مالی پیوستن انتخاب شده تا %(percentage)s%% تخفیف Alipay از کارت‌های اعتباری/دبیت بین‌المللی پشتیبانی می‌کند. برای اطلاعات بیشتر <a %(a_alipay)s>این راهنما</a> را ببینید. برای ما کارت‌های هدیه Amazon.com را با استفاده از کارت اعتباری/دبیت خود ارسال کنید. می‌توانید با استفاده از کارت‌های اعتباری/دبیت، ارز دیجیتال خریداری کنید. WeChat (Weixin Pay) از کارت‌های اعتباری/دبیت بین‌المللی پشتیبانی می‌کند. در اپلیکیشن WeChat، به «Me => Services => Wallet => Add a Card» بروید. اگر آن را نمی‌بینید، با استفاده از «Me => Settings => General => Tools => Weixin Pay => Enable» آن را فعال کنید. (استفاده هنگام ارسال اتریوم از Coinbase) کپی شد! کپی (کمترین مقدار حداقل) (هشدار: مقدار حداقل بالا) -%(percentage)s%% ۱۲ ماه ۱ ماه ۲۴ ماه 3 ماه ۴۸ ماه ۶ ماه ۹۶ ماه مدت زمان اشتراک خود را انتخاب کنید. <div %(div_monthly_cost)s></div><div %(div_after)s>بعد از <span %(span_discount)s></span> تخفیف‌ها</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% برای ۱۲ ماه برای ۱ ماه برای ۲۴ ماه برای ۳ ماه برای ۴۸ ماه برای ۶ ماه برای ۹۶ ماه %(monthly_cost)s / ماه با ما تماس بگیرید سرورهای <strong>SFTP</strong> مستقیم کمک مالی در سطح سازمانی یا تبادل برای مجموعه‌های جدید (مثلاً اسکن‌های جدید، مجموعه‌های OCR شده). دسترسی تخصصی <strong>دسترسی نامحدود</strong> با سرعت بالا <div %(div_question)s>آیا می‌توانم عضویتم را ارتقا دهم یا چندین عضویت بگیرم؟</div> <div %(div_question)s>آیا می‌توانم بدون عضویت کمک مالی کنم؟</div> البته. ما کمک‌های مالی به هر مقدار را در این آدرس Monero (XMR) می‌پذیریم: %(address)s. <div %(div_question)s>محدوده‌ها در هر ماه چه معنایی دارند؟</div> شما می‌توانید با اعمال تمام تخفیف‌ها، مانند انتخاب دوره‌ای طولانی‌تر از یک ماه، به پایین‌ترین حد یک محدوده برسید. <div %(div_question)s>آیا عضویت‌ها به‌طور خودکار تمدید می‌شوند؟</div> عضویت‌ها <strong>به‌طور خودکار</strong> تمدید نمی‌شوند. شما می‌توانید برای مدت زمان دلخواه خود عضو شوید. <div %(div_question)s>کمک‌های مالی شما صرف چه می‌شود؟</div> 100%% صرف حفظ و دسترسی به دانش و فرهنگ جهان می‌شود. در حال حاضر بیشتر آن را صرف سرورها، ذخیره‌سازی و پهنای باند می‌کنیم. هیچ پولی به اعضای تیم شخصاً نمی‌رسد. <div %(div_question)s>آیا می‌توانم کمک مالی بزرگی انجام دهم؟</div> این فوق‌العاده خواهد بود! برای کمک‌های مالی بیش از چند هزار دلار، لطفاً مستقیماً با ما تماس بگیرید در %(email)s. <div %(div_question)s>آیا روش‌های پرداخت دیگری دارید؟</div> در حال حاضر خیر. بسیاری از افراد نمی‌خواهند آرشیوهایی مانند این وجود داشته باشند، بنابراین باید محتاط باشیم. اگر می‌توانید به ما کمک کنید تا روش‌های پرداخت دیگر (و راحت‌تر) را به‌طور ایمن راه‌اندازی کنیم، لطفاً با ما در %(email)s تماس بگیرید. سوالات متداول حمایت (Donation) شما یک <a %(a_donation)s>کمک مالی موجود</a> در حال انجام دارید. لطفاً قبل از انجام کمک مالی جدید، آن کمک مالی را تکمیل یا لغو کنید. <a %(a_all_donations)s>مشاهده تمام کمک‌های مالی من</a> برای کمک‌های مالی بیش از 5000 دلار لطفاً مستقیماً با ما تماس بگیرید %(email)s. ما از کمک‌های بزرگ افراد ثروتمند یا مؤسسات استقبال می‌کنیم.  توجه داشته باشید که در حالی که عضویت‌ها در این صفحه "ماهانه" هستند، آنها حمایت‌های یک‌باره (غیر تکراری) هستند. به <a %(faq)s>سوالات متداول حمایت (Donation)</a> مراجعه کنید. آرشیو آنا یک پروژه غیرانتفاعی، متن‌باز و داده‌باز است. با کمک مالی و عضویت، شما از عملیات و توسعه ما حمایت می‌کنید. به همه اعضای ما: از اینکه ما را حمایت می‌کنید، متشکریم! ❤️ برای اطلاعات بیشتر، به <a %(a_donate)s>سوالات متداول حمایت (Donation)</a> مراجعه کنید. برای عضویت، لطفاً <a %(a_login)s>وارد شوید یا ثبت‌نام کنید</a>. از حمایت شما متشکریم! %(cost)s دلار / ماه اگر در حین پرداخت اشتباهی کردید، ما نمی‌توانیم بازپرداخت انجام دهیم، اما سعی می‌کنیم آن را درست کنیم. صفحه "Crypto" را در برنامه یا وب‌سایت PayPal خود پیدا کنید. این معمولاً زیر "Finances" قرار دارد. به صفحه «بیت‌کوین» در برنامه یا وب‌سایت PayPal بروید. دکمه «انتقال» %(transfer_icon)s را فشار دهید و سپس «ارسال» را انتخاب کنید. Alipay Alipay 支付宝 / WeChat 微信 کارت هدیه آمازون کارت هدیه %(amazon)s کارت بانکی کارت بانکی (با استفاده از اپلیکیشن) بایننس کارت اعتباری/دبیت/Apple/Google (BMC) Cash App کارت اعتباری/دبیت کارت اعتباری/دبیت ۲ کارت اعتباری/دبیت (پشتیبان) ارز دیجیتال %(bitcoin_icon)s کارت / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (عادی) Pix (Brazil) Revolut (موقتا در دسترس نیست) وی‌چت ارز دیجیتال مورد نظر خود را انتخاب کنید: با استفاده از کارت هدیه آمازون کمک مالی کنید. <strong>مهم:</strong> این گزینه برای %(amazon)s است. اگر می‌خواهید از وب‌سایت دیگری از آمازون استفاده کنید، آن را در بالا انتخاب کنید. <strong>مهم:</strong> ما فقط از Amazon.com پشتیبانی می‌کنیم، نه سایر وب‌سایت‌های آمازون. به عنوان مثال، .de، .co.uk، .ca پشتیبانی نمی‌شوند. لطفاً پیام خود را ننویسید. مقدار دقیق را وارد کنید: %(amount)s توجه داشته باشید که ما باید به مبالغی که توسط فروشندگان ما پذیرفته می‌شود (حداقل %(minimum)s) گرد کنیم. اهدا با استفاده از کارت اعتباری/دبیت، از طریق اپلیکیشن Alipay (بسیار آسان برای راه‌اندازی). اپلیکیشن Alipay را از <a %(a_app_store)s>فروشگاه اپل</a> یا <a %(a_play_store)s>فروشگاه گوگل پلی</a> نصب کنید. با استفاده از شماره تلفن خود ثبت‌نام کنید. جزئیات شخصی بیشتری لازم نیست. <span %(style)s>1</span>نصب اپلیکیشن Alipay پشتیبانی شده: Visa، MasterCard، JCB، Diners Club و Discover. برای اطلاعات بیشتر <a %(a_alipay)s>این راهنما</a> را ببینید. <span %(style)s>2</span>افزودن کارت بانکی با Binance، می‌توانید بیت‌کوین را با کارت اعتباری/دبیت یا حساب بانکی خریداری کرده و سپس آن بیت‌کوین را به ما اهدا کنید. به این ترتیب می‌توانیم هنگام پذیرش کمک مالی شما امن و ناشناس بمانیم. Binance در تقریباً هر کشوری در دسترس است و از اکثر بانک‌ها و کارت‌های اعتباری/دبیت پشتیبانی می‌کند. این در حال حاضر توصیه اصلی ما است. از اینکه وقت می‌گذارید تا یاد بگیرید چگونه با استفاده از این روش کمک مالی کنید، قدردانی می‌کنیم، زیرا این کار به ما کمک زیادی می‌کند. برای کارت‌های اعتباری، کارت‌های دبیت، Apple Pay و Google Pay، ما از “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>) استفاده می‌کنیم. در سیستم آن‌ها، یک “قهوه” برابر با ۵ دلار است، بنابراین کمک مالی شما به نزدیک‌ترین مضرب ۵ گرد می‌شود. با استفاده از Cash App کمک مالی کنید. اگر Cash App دارید، این ساده‌ترین راه برای کمک مالی است! توجه داشته باشید که برای تراکنش‌های زیر %(amount)s، Cash App ممکن است هزینه‌ای به مبلغ %(fee)s دریافت کند. برای %(amount)s یا بیشتر، رایگان است! کمک مالی با کارت اعتباری یا بدهی. این روش از یک ارائه‌دهنده ارز دیجیتال به عنوان واسطه تبدیل استفاده می‌کند. این ممکن است کمی گیج‌کننده باشد، بنابراین لطفاً فقط در صورتی از این روش استفاده کنید که روش‌های پرداخت دیگر کار نکنند. همچنین در همه کشورها کار نمی‌کند. ما نمی‌توانیم به طور مستقیم از کارت‌های اعتباری/دبیت پشتیبانی کنیم، زیرا بانک‌ها نمی‌خواهند با ما همکاری کنند. ☹ با این حال، چندین راه برای استفاده از کارت‌های اعتباری/دبیت وجود دارد، با استفاده از روش‌های پرداخت دیگر: با ارز دیجیتال می‌توانید با استفاده از BTC، ETH، XMR و SOL اهدا کنید. از این گزینه استفاده کنید اگر با ارز دیجیتال آشنا هستید. با ارز دیجیتال می‌توانید با استفاده از BTC، ETH، XMR و بیشتر کمک مالی کنید. خدمات سریع کریپتو اگر برای اولین بار از ارز دیجیتال استفاده می‌کنید، پیشنهاد می‌کنیم از %(options)s برای خرید و اهدا بیت‌کوین (اولین و پرکاربردترین ارز دیجیتال) استفاده کنید. توجه داشته باشید که برای کمک‌های کوچک، هزینه‌های کارت اعتباری ممکن است تخفیف %(discount)s%% ما را از بین ببرد، بنابراین ما اشتراک‌های طولانی‌تر را توصیه می‌کنیم. با استفاده از کارت اعتباری/دبیت، PayPal، یا Venmo اهدا کنید. می‌توانید در صفحه بعدی بین این گزینه‌ها انتخاب کنید. Google Pay و Apple Pay نیز ممکن است کار کنند. توجه داشته باشید که برای کمک‌های کوچک، هزینه‌ها بالا است، بنابراین ما اشتراک‌های طولانی‌تر را توصیه می‌کنیم. برای کمک مالی با استفاده از PayPal US، ما از PayPal Crypto استفاده می‌کنیم که به ما امکان می‌دهد ناشناس بمانیم. از شما قدردانی می‌کنیم که وقت گذاشتید تا یاد بگیرید چگونه با این روش کمک مالی کنید، زیرا این کار به ما کمک زیادی می‌کند. کمک مالی با استفاده از PayPal. با استفاده از حساب عادی PayPal خود اهدا کنید. با استفاده از Revolut اهدا کنید. اگر Revolut دارید، این آسان‌ترین راه برای اهدا است! این روش پرداخت فقط حداکثر %(amount)s را مجاز می‌داند. لطفاً مدت زمان یا روش پرداخت دیگری را انتخاب کنید. این روش پرداخت حداقل %(amount)s نیاز دارد. لطفاً مدت زمان یا روش پرداخت دیگری را انتخاب کنید. بایننس Coinbase Kraken لطفاً یک روش پرداخت را انتخاب کنید. "پذیرش یک تورنت": نام کاربری یا پیام شما در نام فایل تورنت <div %(div_months)s>یک بار در هر ۱۲ ماه عضویت</div> نام کاربری شما یا ذکر ناشناس در اعتبارنامه‌ها دسترسی زودهنگام به ویژگی‌های جدید تلگرام اختصاصی با به‌روزرسانی‌های پشت صحنه %(number)s دانلود سریع در روز اگر این ماه کمک کنید! <a %(a_api)s>دسترسی به JSON API</a> وضعیت افسانه‌ای در حفظ دانش و فرهنگ بشریت مزایای قبلی، به علاوه: کسب <strong>%(percentage)s%% دانلود اضافی</strong> با <a %(a_refer)s>معرفی دوستان</a>. مقالات SciDB <strong>نامحدود</strong> بدون تأیید هنگام پرسیدن سوالات مربوط به حساب یا حمایت، شناسه حساب، اسکرین‌شات‌ها، رسیدها و هر اطلاعات ممکن را اضافه کنید. ما ایمیل خود را هر ۱-۲ هفته یک‌بار بررسی می‌کنیم، بنابراین عدم ارائه این اطلاعات باعث تأخیر در حل مشکل خواهد شد. برای دریافت دانلودهای بیشتر، <a %(a_refer)s>دوستان خود را معرفی کنید</a>! ما یک تیم کوچک از داوطلبان هستیم. ممکن است ۱-۲ هفته طول بکشد تا پاسخ دهیم. توجه داشته باشید که نام یا تصویر حساب ممکن است عجیب به نظر برسد. نگران نباشید! این حساب‌ها توسط شرکای اهدا ما مدیریت می‌شوند. حساب‌های ما هک نشده‌اند. کمک مالی <span %(span_cost)s></span> <span %(span_label)s></span> برای 12 ماه "%(tier_name)s" برای ۱ ماه “%(tier_name)s” برای ۲۴ ماه “%(tier_name)s” برای ۳ ماه “%(tier_name)s” برای ۴۸ ماه “%(tier_name)s” برای ۶ ماه “%(tier_name)s” برای ۹۶ ماه “%(tier_name)s” شما هنوز می‌توانید کمک مالی را در هنگام پرداخت لغو کنید. برای تأیید این اهدا، روی دکمه اهدا کلیک کنید. <strong>نکته مهم:</strong> قیمت‌های ارز دیجیتال می‌توانند به شدت نوسان کنند، گاهی حتی تا 20%% در چند دقیقه. این هنوز کمتر از هزینه‌هایی است که با بسیاری از ارائه‌دهندگان پرداخت متحمل می‌شویم، که اغلب 50-60%% برای همکاری با یک "خیریه سایه" مانند ما دریافت می‌کنند. <u>اگر رسید با قیمت اصلی که پرداخت کرده‌اید را برای ما ارسال کنید، همچنان حساب شما را برای عضویت انتخابی اعتبار خواهیم داد</u> (تا زمانی که رسید بیش از چند ساعت قدیمی نباشد). ما واقعاً قدردان این هستیم که شما مایلید با چنین مسائلی کنار بیایید تا از ما حمایت کنید! ❤️ ❌ مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. <span %(span_circle)s>1</span>خرید بیت‌کوین از طریق PayPal <span %(span_circle)s>2</span> بیت‌کوین را به آدرس ما منتقل کنید ✅ در حال هدایت به صفحه اهدا… اهدا لطفاً حداقل <span %(span_hours)s>۲۴ ساعت</span> صبر کنید (و این صفحه را تازه‌سازی کنید) قبل از اینکه با ما تماس بگیرید. اگر مایل به اهدای کمک مالی (هر مبلغی) بدون عضویت هستید، لطفاً از این آدرس Monero (XMR) استفاده کنید: %(address)s. پس از ارسال کارت هدیه خود، سیستم خودکار ما آن را ظرف چند دقیقه تأیید خواهد کرد. اگر این کار نکرد، سعی کنید کارت هدیه خود را دوباره ارسال کنید (<a %(a_instr)s>دستورالعمل‌ها</a>). اگر هنوز هم کار نمی‌کند، لطفاً به ما ایمیل بزنید و آنا به صورت دستی آن را بررسی خواهد کرد (این ممکن است چند روز طول بکشد)، و حتماً ذکر کنید که آیا قبلاً تلاش به ارسال مجدد کرده‌اید. مثال: لطفاً از <a %(a_form)s>فرم رسمی Amazon.com</a> برای ارسال کارت هدیه %(amount)s به آدرس ایمیل زیر استفاده کنید. ایمیل گیرنده "به" به شکل: کارت هدیه آمازون ما نمی‌توانیم روش‌های دیگر کارت‌های هدیه را بپذیریم، <strong>فقط مستقیماً از فرم رسمی در Amazon.com ارسال شده باشد</strong>. اگر از این فرم استفاده نکنید، نمی‌توانیم کارت هدیه شما را بازگردانیم. فقط یک بار استفاده کنید. منحصر به حساب شما، به اشتراک نگذارید. در انتظار کارت هدیه... (برای بررسی صفحه را تازه کنید) <a %(a_href)s>صفحه اهدا با کد QR</a> را باز کنید. کد QR را با اپلیکیشن Alipay اسکن کنید، یا دکمه را فشار دهید تا اپلیکیشن Alipay باز شود. لطفاً صبور باشید؛ ممکن است بارگذاری صفحه کمی طول بکشد زیرا در چین است. <span %(style)s>3</span>انجام اهدا (اسکن کد QR یا فشار دادن دکمه) خرید سکه PYUSD در PayPal خرید بیت‌کوین (BTC) در Cash App کمی بیشتر بخرید (ما %(more)s بیشتر را توصیه می‌کنیم) از مقداری که اهدا می‌کنید (%(amount)s)، تا هزینه‌های تراکنش را پوشش دهید. هر چیزی که باقی بماند برای شما خواهد بود. به صفحه «بیت‌کوین» (BTC) در Cash App بروید. بیت‌کوین را به آدرس ما منتقل کنید برای اهداهای کوچک (کمتر از ۲۵ دلار)، ممکن است نیاز به استفاده از Rush یا Priority داشته باشید. برای انجام «برداشت» روی دکمه «ارسال بیت‌کوین» کلیک کنید. با فشار دادن آیکون %(icon)s از دلار به BTC تغییر دهید. مقدار BTC را در زیر وارد کرده و روی «ارسال» کلیک کنید. اگر گیر کردید، <a %(help_video)s>این ویدیو</a> را ببینید. خدمات سریع راحت هستند، اما هزینه‌های بیشتری دارند. می‌توانید از این به جای یک صرافی کریپتو استفاده کنید اگر به دنبال انجام سریع یک اهدا بزرگ هستید و مشکلی با هزینه ۵ تا ۱۰ دلاری ندارید. حتماً مقدار دقیق کریپتو نشان داده شده در صفحه اهدا را ارسال کنید، نه مقدار به دلار آمریکا. در غیر این صورت هزینه کسر می‌شود و نمی‌توانیم عضویت شما را به‌طور خودکار پردازش کنیم. گاهی تأیید ممکن است تا ۲۴ ساعت طول بکشد، بنابراین حتماً این صفحه را تازه‌سازی کنید (حتی اگر منقضی شده باشد). دستورالعمل‌های کارت اعتباری / دبیت از طریق صفحه کارت اعتباری / دبیت ما اهدا کنید برخی از مراحل به کیف پول‌های رمزنگاری اشاره می‌کنند، اما نگران نباشید، نیازی به یادگیری چیزی درباره رمزنگاری ندارید. دستورالعمل‌های %(coin_name)s ﺪﯿﻨﮐ ﺮﭘ ﺍﺭ ﺖﺧﺍﺩﺮﭘ ﺕﺎﯿﺋﺰﺟ ﺖﻋﺮﺳ ﻪﺑ ﺎﺗ ﺪﯿﻨﮐ ﻦﮑﺳﺍ ﺩﻮﺧ ﯼﺭﺎﮕﻧﺰﻣﺭ ﻝﻮﭘ ﻒﯿﮐ ﻪﻣﺎﻧﺮﺑ ﺎﺑ ﺍ ﺪﯿﻨﮐ ﻦﮑﺳﺍ ﺖﺧﺍﺩﺮﭘ ﯼﺍﺮﺑ ﺍﺭ QR ﺪﮐ ما فقط از نسخه استاندارد سکه‌های رمزنگاری پشتیبانی می‌کنیم، نه شبکه‌ها یا نسخه‌های عجیب و غریب سکه‌ها. تأیید تراکنش بسته به نوع سکه ممکن است تا یک ساعت طول بکشد. اهدا %(amount)s در <a %(a_page)s>این صفحه</a>. این اهدا منقضی شده است. لطفاً آن را لغو کرده و یک اهدا جدید ایجاد کنید. اگر قبلاً پرداخت کرده‌اید: بله، من رسید خود را ایمیل کردم اگر نرخ تبادل ارز دیجیتال در طول تراکنش نوسان داشت، حتماً رسیدی که نرخ تبادل اصلی را نشان می‌دهد، ضمیمه کنید. ما واقعاً از زحمتی که برای استفاده از ارز دیجیتال می‌کشید، قدردانی می‌کنیم، این به ما خیلی کمک می‌کند! ❌ مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. <span %(span_circle)s>%(circle_number)s</span>رسید را به ما ایمیل کنید اگر با هر مشکلی مواجه شدید، لطفاً با ما در %(email)s تماس بگیرید و تا حد امکان اطلاعات بیشتری (مانند اسکرین‌شات‌ها) ارائه دهید. ✅ از اهدا شما متشکریم! آنا عضویت شما را ظرف چند روز به صورت دستی فعال خواهد کرد. یک رسید یا اسکرین‌شات به آدرس تأیید شخصی خود ارسال کنید: وقتی رسید خود را ایمیل کردید، این دکمه را کلیک کنید تا آنا بتواند به صورت دستی آن را بررسی کند (این ممکن است چند روز طول بکشد): رسید یا اسکرین‌شات را به آدرس تأیید شخصی خود ارسال کنید. از این آدرس ایمیل برای اهدای PayPal خود استفاده نکنید. لغو بله، لطفاً لغو کنید آیا مطمئن هستید که می‌خواهید لغو کنید؟ اگر قبلاً پرداخت کرده‌اید، لغو نکنید. ❌ مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. اهدا جدیدی انجام دهید ✅ اهدا شما لغو شده است. تاریخ: %(date)s شناسه: %(id)s دوباره سفارش دهید وضعیت: <span %(span_label)s>%(label)s</span> مجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ماه برای %(duration)s ماه، شامل %(discounts)s%% تخفیف)</span> مجموع: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / ماه برای %(duration)s ماه)</span> ۱. ایمیل خود را وارد کنید. ۲. روش پرداخت خود را انتخاب کنید. ۳. روش پرداخت خود را دوباره انتخاب کنید. ۴. کیف پول "خود میزبان" را انتخاب کنید. ۵. روی "مالکیت را تأیید می‌کنم" کلیک کنید. ۶. شما باید یک رسید ایمیل دریافت کنید. لطفاً آن را برای ما ارسال کنید و ما در اسرع وقت اهدا شما را تأیید خواهیم کرد. (ممکن است بخواهید اهدا را لغو کرده و یک اهدا جدید ایجاد کنید) دستورالعمل‌های پرداخت اکنون قدیمی شده‌اند. اگر مایل به اهدای کمک مالی دیگری هستید، از دکمه “سفارش مجدد” در بالا استفاده کنید. شما قبلاً پرداخت کرده‌اید. اگر همچنان می‌خواهید دستورالعمل‌های پرداخت را مرور کنید، اینجا کلیک کنید: نمایش دستورالعمل‌های پرداخت قدیمی اگر صفحه اهدای کمک مسدود شد، یک اتصال اینترنتی دیگر را امتحان کنید (مثلاً VPN یا اینترنت تلفن همراه). متأسفانه، صفحه Alipay اغلب فقط از <strong>سرزمین اصلی چین</strong> قابل دسترسی است. ممکن است نیاز باشد موقتاً VPN خود را غیرفعال کنید، یا از VPN به سرزمین اصلی چین استفاده کنید (یا گاهی اوقات هنگ کنگ نیز کار می‌کند). <span %(span_circle)s>۱</span> در Alipay کمک مالی کنید مبلغ کل %(total)s را با استفاده از <a %(a_account)s>این حساب Alipay</a> اهدا کنید دستورالعمل‌های Alipay <span %(span_circle)s>۱</span> به یکی از حساب‌های رمزنگاری ما انتقال دهید مبلغ کل %(total)s را به یکی از این آدرس‌ها اهدا کنید: دستورالعمل‌های رمزنگاری دستورالعمل‌ها را برای خرید بیت‌کوین (BTC) دنبال کنید. شما فقط نیاز دارید به مقدار مورد نظر خود برای اهدا، %(total)s خرید کنید. آدرس بیت‌کوین (BTC) ما را به عنوان گیرنده وارد کنید و دستورالعمل‌ها را برای ارسال کمک مالی خود به مبلغ %(total)s دنبال کنید: <span %(span_circle)s>1</span>در Pix اهدا کنید مجموع مبلغ %(total)s را با استفاده از <a %(a_account)s>این حساب Pix</a> اهدا کنید دستورالعمل‌های Pix <span %(span_circle)s>۱</span> در WeChat کمک مالی کنید مبلغ کل %(total)s را با استفاده از <a %(a_account)s>این حساب WeChat</a> اهدا کنید دستورالعمل‌های WeChat از هر یک از خدمات «کارت اعتباری به بیت‌کوین» زیر استفاده کنید که فقط چند دقیقه طول می‌کشد: آدرس BTC / بیت‌کوین (کیف پول خارجی): مقدار BTC / بیت‌کوین: جزئیات زیر را در فرم پر کنید: اگر هر یک از این اطلاعات قدیمی است، لطفاً به ما ایمیل بزنید تا ما را مطلع کنید. لطفاً از این <span %(underline)s>مقدار دقیق</span> استفاده کنید. هزینه کل شما ممکن است به دلیل کارمزد کارت اعتباری بیشتر باشد. متأسفانه، برای مقادیر کم این ممکن است بیشتر از تخفیف ما باشد. (حداقل: %(minimum)s، بدون تأیید برای اولین تراکنش) (حداقل: %(minimum)s) (حداقل: %(minimum)s) (حداقل: %(minimum)s، بدون تأیید برای اولین تراکنش) (حداقل: %(minimum)s) (حداقل: %(minimum)s بسته به کشور، بدون تأیید برای اولین تراکنش) دستورالعمل‌ها را برای خرید سکه PYUSD (PayPal USD) دنبال کنید. کمی بیشتر بخرید (ما توصیه می‌کنیم %(more)s بیشتر) از مقداری که اهدا می‌کنید (%(amount)s)، تا هزینه‌های تراکنش را پوشش دهید. هر چیزی که باقی بماند برای شما خواهد بود. به صفحه "PYUSD" در اپلیکیشن یا وب‌سایت PayPal خود بروید. دکمه "انتقال" %(icon)s را فشار دهید و سپس "ارسال" را انتخاب کنید. به‌روزرسانی وضعیت برای بازنشانی تایمر، به سادگی یک اهدا جدید ایجاد کنید. حتماً از مقدار BTC زیر استفاده کنید، <em>نه</em> یورو یا دلار، در غیر این صورت ما مبلغ صحیح را دریافت نمی‌کنیم و نمی‌توانیم عضویت شما را به‌طور خودکار تأیید کنیم. خرید بیت‌کوین (BTC) در Revolut کمی بیشتر بخرید (ما %(more)s بیشتر را توصیه می‌کنیم) از مقداری که اهدا می‌کنید (%(amount)s)، تا هزینه‌های تراکنش را پوشش دهید. هر چیزی که باقی بماند برای شما خواهد بود. به صفحه «کریپتو» در Revolut بروید تا بیت‌کوین (BTC) بخرید. بیت‌کوین را به آدرس ما منتقل کنید برای اهداهای کوچک (کمتر از ۲۵ دلار) ممکن است نیاز به استفاده از Rush یا Priority داشته باشید. برای انجام «برداشت» روی دکمه «ارسال بیت‌کوین» کلیک کنید. با فشار دادن آیکون %(icon)s از یورو به BTC تغییر دهید. مقدار BTC را در زیر وارد کرده و روی «ارسال» کلیک کنید. اگر گیر کردید، <a %(help_video)s>این ویدیو</a> را ببینید. وضعیت: 1 2 راهنمای گام به گام راهنمای گام به گام را در زیر ببینید. در غیر این صورت ممکن است از این حساب قفل شوید! اگر هنوز این کار را نکرده‌اید، کلید مخفی خود را برای ورود یادداشت کنید: از حمایت شما سپاسگزاریم! زمان باقی‌مانده: کمک مالی انتقال %(amount)s به %(account)s در انتظار تأیید (برای بررسی صفحه را تازه‌سازی کنید)… در انتظار انتقال (برای بررسی صفحه را تازه کنید)… قبلاً دانلودهای سریع در ۲۴ ساعت گذشته به محدودیت روزانه حساب می‌شوند. دانلودها از سرورهای شریک سریع با %(icon)s علامت‌گذاری شده‌اند. ۱۸ ساعت گذشته هنوز هیچ فایلی دانلود نشده است. فایل‌های دانلود شده به صورت عمومی نمایش داده نمی‌شوند. تمام زمان‌ها به وقت هماهنگ جهانی (UTC) هستند. فایل‌های دانلود شده اگر فایلی را با دانلود سریع و کند دانلود کرده‌اید، دو بار نمایش داده می‌شود. نگران نباشید، افراد زیادی از وب‌سایت‌هایی که ما لینک داده‌ایم دانلود می‌کنند و بسیار نادر است که به مشکل بربخورید. با این حال، برای ایمنی بیشتر توصیه می‌کنیم از VPN (پرداختی) یا <a %(a_tor)s>Tor</a> (رایگان) استفاده کنید. من کتاب 1984 نوشته جورج اورول را دانلود کردم، آیا پلیس به درب خانه من خواهد آمد؟ شما آنا هستید! آنا کیست؟ ما یک API JSON پایدار برای اعضا داریم، برای دریافت URL دانلود سریع: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (مستندات درون JSON خود). برای موارد استفاده دیگر، مانند مرور تمام فایل‌های ما، ساخت جستجوی سفارشی و غیره، توصیه می‌کنیم <a %(a_generate)s>تولید</a> یا <a %(a_download)s>دانلود</a> پایگاه‌های داده ElasticSearch و MariaDB ما. داده‌های خام را می‌توان به‌صورت دستی <a %(a_explore)s>از طریق فایل‌های JSON</a> بررسی کرد. لیست تورنت‌های خام ما نیز به‌صورت <a %(a_torrents)s>JSON</a> قابل دانلود است. آیا API دارید؟ ما هیچ‌گونه مواد دارای حق نشر را در اینجا میزبانی نمی‌کنیم. ما یک موتور جستجو هستیم و به این ترتیب فقط فراداده‌ای را که قبلاً به‌طور عمومی در دسترس است، فهرست می‌کنیم. هنگام دانلود از این منابع خارجی، پیشنهاد می‌کنیم قوانین حوزه قضایی خود را در مورد آنچه مجاز است بررسی کنید. ما مسئول محتوای میزبانی شده توسط دیگران نیستیم. اگر شکایتی درباره آنچه در اینجا می‌بینید دارید، بهترین راه این است که با وب‌سایت اصلی تماس بگیرید. ما به طور منظم تغییرات آن‌ها را به پایگاه داده خود می‌کشیم. اگر واقعاً فکر می‌کنید که شکایت DMCA معتبری دارید که باید به آن پاسخ دهیم، لطفاً <a %(a_copyright)s>فرم شکایت DMCA / حق‌تألیف</a> را پر کنید. ما شکایات شما را جدی می‌گیریم و در اسرع وقت به شما پاسخ خواهیم داد. چگونه نقض حق‌تألیف را گزارش کنم؟ در اینجا چند کتاب وجود دارد که برای دنیای کتابخانه‌های سایه و حفظ دیجیتال اهمیت ویژه‌ای دارند: کتاب‌های مورد علاقه شما چیست؟ ما همچنین می‌خواهیم به همه یادآوری کنیم که تمام کد و داده‌های ما کاملاً منبع باز هستند. این برای پروژه‌هایی مانند ما منحصر به فرد است — ما از هیچ پروژه دیگری با کاتالوگی به این وسعت که کاملاً منبع باز باشد، آگاه نیستیم. ما بسیار خوشحال می‌شویم اگر کسی فکر می‌کند که ما پروژه خود را به‌خوبی اجرا نمی‌کنیم، کد و داده‌های ما را بگیرد و کتابخانه سایه خود را راه‌اندازی کند! ما این را از روی کینه یا چیزی نمی‌گوییم — ما واقعاً فکر می‌کنیم این عالی خواهد بود زیرا سطح استاندارد را برای همه بالا می‌برد و میراث بشریت را بهتر حفظ می‌کند. من از نحوه اجرای این پروژه متنفرم! ما دوست داریم که افراد <a %(a_mirrors)s>آینه‌ها</a> را راه‌اندازی کنند و ما از نظر مالی از این کار حمایت خواهیم کرد. چطور می‌توانم کمک کنم؟ بله، ما داریم. الهام‌بخش ما برای جمع‌آوری فراداده، هدف آرون سوارتز برای "یک صفحه وب برای هر کتابی که تا به حال منتشر شده" بود، که برای آن <a %(a_openlib)s>Open Library</a> را ایجاد کرد. آن پروژه موفق بوده است، اما موقعیت منحصر به فرد ما به ما اجازه می‌دهد فراداده‌ای را به دست آوریم که آن‌ها نمی‌توانند. الهام‌بخش دیگر ما تمایل به دانستن <a %(a_blog)s>تعداد کتاب‌های موجود در جهان</a> بود، تا بتوانیم محاسبه کنیم که چند کتاب دیگر باقی مانده است که باید نجات دهیم. آیا شما فراداده جمع‌آوری می‌کنید؟ توجه داشته باشید که mhut.org برخی از محدوده‌های IP را مسدود می‌کند، بنابراین ممکن است نیاز به استفاده از VPN باشد. <strong>اندروید:</strong> روی منوی سه‌نقطه در بالا سمت راست کلیک کنید و "افزودن به صفحه اصلی" را انتخاب کنید. <strong>iOS:</strong> روی دکمه "اشتراک‌گذاری" در پایین کلیک کنید و "افزودن به صفحه اصلی" را انتخاب کنید. ما اپلیکیشن موبایل رسمی نداریم، اما می‌توانید این وب‌سایت را به عنوان یک اپلیکیشن نصب کنید. آیا برنامه موبایل دارید؟ لطفاً آن‌ها را به <a %(a_archive)s>Internet Archive</a> ارسال کنید. آن‌ها به درستی آن‌ها را حفظ خواهند کرد. چگونه می‌توانم کتاب‌ها یا مواد فیزیکی دیگر را اهدا کنم؟ چگونه کتاب‌ها را درخواست کنم؟ <a %(a_blog)s>وبلاگ آنا</a>، <a %(a_reddit_u)s>ردیت</a>، <a %(a_reddit_r)s>ساب‌ردیت</a> — به‌روزرسانی‌های منظم <a %(a_software)s>نرم‌افزار آنا</a> — کد منبع باز ما <a %(a_datasets)s>Datasets</a> — درباره داده‌ها <a %(a_li)s>.li</a>، <a %(a_se)s>.se</a>، <a %(a_org)s>.org</a> — دامنه‌های جایگزین آیا منابع بیشتری درباره آرشیو آنا وجود دارد؟ <a %(a_translate)s>ترجمه در نرم‌افزار آنا</a> — سیستم ترجمه ما <a %(a_wikipedia)s>ویکی‌پدیا</a> — بیشتر درباره ما (لطفاً کمک کنید این صفحه را به‌روز نگه دارید، یا یکی برای زبان خودتان ایجاد کنید!) تنظیمات مورد نظر خود را انتخاب کنید، جعبه جستجو را خالی بگذارید، روی "جستجو" کلیک کنید، و سپس صفحه را با استفاده از ویژگی نشانک مرورگر خود نشانک‌گذاری کنید. چگونه تنظیمات جستجوی خود را ذخیره کنم؟ ما از پژوهشگران امنیتی دعوت می‌کنیم تا به دنبال آسیب‌پذیری‌ها در سیستم‌های ما بگردند. ما حامیان بزرگ افشای مسئولانه هستیم. با ما <a %(a_contact)s>اینجا</a> تماس بگیرید. در حال حاضر قادر به اعطای پاداش‌های باگ نیستیم، به جز برای آسیب‌پذیری‌هایی که <a %(a_link)s>پتانسیل به خطر انداختن ناشناس بودن ما</a> را دارند، که برای آن‌ها پاداش‌هایی در محدوده ۱۰ هزار تا ۵۰ هزار دلار ارائه می‌دهیم. ما دوست داریم در آینده دامنه وسیع‌تری برای پاداش‌های باگ ارائه دهیم! لطفاً توجه داشته باشید که حملات مهندسی اجتماعی خارج از دامنه هستند. اگر به امنیت تهاجمی علاقه‌مند هستید و می‌خواهید به آرشیو کردن دانش و فرهنگ جهان کمک کنید، حتماً با ما تماس بگیرید. راه‌های زیادی وجود دارد که می‌توانید کمک کنید. آیا برنامه افشای مسئولانه دارید؟ ما واقعاً منابع کافی برای ارائه دانلودهای پرسرعت به همه افراد جهان نداریم، هرچند که دوست داریم این کار را انجام دهیم. اگر یک خیر ثروتمند بخواهد این کار را برای ما فراهم کند، فوق‌العاده خواهد بود، اما تا آن زمان، ما تمام تلاش خود را می‌کنیم. ما یک پروژه غیرانتفاعی هستیم که به سختی از طریق کمک‌های مالی خود را تأمین می‌کند. به همین دلیل ما دو سیستم برای دانلود رایگان با شرکای خود پیاده‌سازی کرده‌ایم: سرورهای مشترک با دانلودهای کند و سرورهای کمی سریع‌تر با لیست انتظار (برای کاهش تعداد افرادی که همزمان دانلود می‌کنند). ما همچنین برای دانلودهای کند خود <a %(a_verification)s>تأیید مرورگر</a> داریم، زیرا در غیر این صورت ربات‌ها و اسکرپرها از آنها سوءاستفاده می‌کنند و اوضاع را برای کاربران واقعی حتی کندتر می‌کنند. توجه داشته باشید که هنگام استفاده از مرورگر Tor، ممکن است نیاز به تنظیم تنظیمات امنیتی خود داشته باشید. در پایین‌ترین گزینه‌ها، به نام "استاندارد"، چالش Cloudflare turnstile موفقیت‌آمیز است. در گزینه‌های بالاتر، به نام "ایمن‌تر" و "ایمن‌ترین"، چالش شکست می‌خورد. برای فایل‌های بزرگ، گاهی اوقات دانلودهای کند ممکن است در وسط قطع شوند. ما توصیه می‌کنیم از یک مدیر دانلود (مانند JDownloader) استفاده کنید تا به طور خودکار دانلودهای بزرگ را از سر بگیرد. چرا دانلودهای کند اینقدر کند هستند؟ سوالات متداول (FAQ) از <a %(a_list)s>تولیدکننده لیست تورنت</a> استفاده کنید تا لیستی از تورنت‌هایی که بیشترین نیاز به اشتراک‌گذاری دارند، در محدوده فضای ذخیره‌سازی خود ایجاد کنید. بله، صفحه <a %(a_llm)s>داده‌های LLM</a> را ببینید. بیشتر تورنت‌ها فایل‌ها را به‌طور مستقیم شامل می‌شوند، به این معنی که می‌توانید به کلاینت‌های تورنت دستور دهید که فقط فایل‌های مورد نیاز را دانلود کنند. برای تعیین اینکه کدام فایل‌ها را دانلود کنید، می‌توانید <a %(a_generate)s>فراداده</a> ما را تولید کنید، یا <a %(a_download)s>پایگاه‌های داده ElasticSearch و MariaDB</a> ما را دانلود کنید. متأسفانه، تعدادی از مجموعه‌های تورنت شامل فایل‌های .zip یا .tar در ریشه هستند، که در این صورت باید کل تورنت را دانلود کنید تا بتوانید فایل‌های فردی را انتخاب کنید. ابزارهای آسان برای فیلتر کردن تورنت‌ها هنوز در دسترس نیستند، اما ما از مشارکت‌ها استقبال می‌کنیم. (ما <a %(a_ideas)s>برخی ایده‌ها</a> برای حالت دوم داریم.) پاسخ طولانی: پاسخ کوتاه: به راحتی نه. ما سعی می‌کنیم تکرار یا همپوشانی حداقلی بین تورنت‌های این لیست داشته باشیم، اما این همیشه قابل دستیابی نیست و به شدت به سیاست‌های کتابخانه‌های منبع بستگی دارد. برای کتابخانه‌هایی که تورنت‌های خود را منتشر می‌کنند، این از دست ما خارج است. برای تورنت‌هایی که توسط آرشیو آنا منتشر می‌شوند، ما فقط بر اساس هش MD5 تکراری‌ها را حذف می‌کنیم، که به این معنی است که نسخه‌های مختلف یک کتاب تکراری نمی‌شوند. بله. این‌ها در واقع PDF و EPUB هستند، فقط در بسیاری از تورنت‌های ما پسوند ندارند. دو مکان وجود دارد که می‌توانید فراداده فایل‌های تورنت، از جمله نوع/پسوند فایل‌ها را پیدا کنید: ۱. هر مجموعه یا انتشار دارای فراداده خود است. به عنوان مثال، <a %(a_libgen_nonfic)s>تورنت‌های Libgen.rs</a> دارای یک پایگاه داده فراداده مربوطه در وب‌سایت Libgen.rs هستند. ما معمولاً به منابع فراداده مرتبط از صفحه <a %(a_datasets)s>مجموعه داده</a> هر مجموعه لینک می‌دهیم. ۲. ما توصیه می‌کنیم <a %(a_generate)s>تولید</a> یا <a %(a_download)s>دانلود</a> پایگاه‌های داده ElasticSearch و MariaDB ما را انجام دهید. این‌ها شامل یک نقشه‌برداری برای هر ضبط در آرشیو آنا به فایل‌های تورنت مربوطه (در صورت موجود بودن) هستند، تحت "torrent_paths" در JSON ElasticSearch. برخی از کلاینت‌های تورنت از اندازه‌های بزرگ قطعه پشتیبانی نمی‌کنند، که بسیاری از تورنت‌های ما این ویژگی را دارند (برای تورنت‌های جدیدتر این کار را دیگر انجام نمی‌دهیم — حتی اگر طبق مشخصات معتبر باشد!). بنابراین اگر با این مشکل مواجه شدید، یک کلاینت دیگر را امتحان کنید یا به سازندگان کلاینت تورنت خود شکایت کنید. می‌خواهم به اشتراک‌گذاری کمک کنم، اما فضای دیسک زیادی ندارم. تورنت‌ها خیلی کند هستند؛ آیا می‌توانم داده‌ها را مستقیماً از شما دانلود کنم؟ آیا می‌توانم فقط یک زیرمجموعه از فایل‌ها، مانند یک زبان یا موضوع خاص را دانلود کنم؟ چگونه با تکراری‌ها در تورنت‌ها برخورد می‌کنید؟ آیا می‌توانم لیست تورنت را به صورت JSON دریافت کنم؟ من فایل‌های PDF یا EPUB را در تورنت‌ها نمی‌بینم، فقط فایل‌های باینری هستند؟ چه کار کنم؟ چرا کلاینت تورنت من نمی‌تواند برخی از فایل‌های تورنت / لینک‌های مگنت شما را باز کند؟ سؤالات متداول تورنت‌ها چگونه کتاب‌های جدید بارگذاری کنم؟ لطفاً <a %(a_href)s>این پروژه عالی</a> را ببینید. آیا مانیتور زمان کارکرد دارید؟ آرشیو آنا چیست؟ برای استفاده از دانلودهای سریع، عضو شوید. ما اکنون از کارت‌های هدیه آمازون، کارت‌های اعتباری و نقدی، ارزهای دیجیتال، Alipay و WeChat پشتیبانی می‌کنیم. شما امروز از دانلودهای سریع خود استفاده کرده‌اید. دسترسی دانلودهای ساعتی در 30 روز گذشته. میانگین ساعتی: %(hourly)s. میانگین روزانه: %(daily)s. ما با شرکای خود کار می کنیم تا مجموعه های خود را به راحتی و رایگان در دسترس همه قرار دهیم. ما معتقدیم که هرکسی حق دارد از خرد جمعی بشریت برخوردار باشد. و <a %(a_search)s>نه به هزینه برای نویسندگان</a>. مجموعه داده‌های استفاده شده در آرشیو آنا کاملاً باز هستند و می‌توانند به صورت عمده با استفاده از تورنت‌ها لینک کمکی شوند. <a %(a_datasets)s>بیشتر بدانید…</a> آرشیو بلندمدت پایگاه داده کامل جستجو کتاب‌ها، مقالات، مجلات، کمیک‌ها، سوابق کتابخانه، فراداده، … تمام <a %(a_code)s>کد</a> و <a %(a_datasets)s>داده</a> ما کاملاً متن‌باز هستند. <span %(span_anna)s>آرشیو آنا</span> یک پروژه غیرانتفاعی با دو هدف است: <li><strong>حفاظت:</strong> پشتیبان‌گیری از تمام دانش و فرهنگ بشری.</li><li><strong>دسترسی:</strong> در دسترس قرار دادن این دانش و فرهنگ برای هر کسی در جهان.</li> ما بزرگترین مجموعه داده‌های متنی با کیفیت بالا در جهان را داریم. <a %(a_llm)s>بیشتر بدانید…</a> داده‌های آموزشی LLM 🪩 لینک‌های کمکی: فراخوان برای داوطلبان اگر شما یک پردازشگر پرداخت ناشناس با ریسک بالا را اداره می‌کنید، لطفاً با ما تماس بگیرید. ما همچنین به دنبال افرادی هستیم که مایل به قرار دادن تبلیغات کوچک و زیبا هستند. تمام درآمدها به تلاش‌های حفظ و نگهداری ما اختصاص می‌یابد. نگهداشت ما تخمین می زنیم که حدود <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% درصد از کتاب های جهان را حفظ کرده ایم. </a>. ما کتاب‌ها، مقالات، کمیک‌ها، مجلات و موارد دیگر را با جمع‌آوری این مواد از کتابخانه‌های سایه‌ای مختلف، کتابخانه‌های رسمی و مجموعه‌های دیگر در یک مکان حفظ می‌کنیم. تمام این داده‌ها با استفاده از تورنت‌ها به‌صورت عمده تکثیر می‌شوند و در نتیجه نسخه‌های زیادی در سراسر جهان ایجاد می‌شود. برخی از کتابخانه‌های سایه‌ای خودشان این کار را انجام می‌دهند (مثلاً Sci-Hub، Library Genesis)، در حالی که آرشیو آنا کتابخانه‌های دیگر را که توزیع عمده ارائه نمی‌دهند (مثلاً Z-Library) یا اصلاً کتابخانه‌های سایه‌ای نیستند (مثلاً Internet Archive، DuXiu) "آزاد" می‌کند. این توزیع گسترده، همراه با کد متن‌باز، وب‌سایت ما را در برابر حذف‌ها مقاوم می‌کند و حفظ طولانی‌مدت دانش و فرهنگ بشری را تضمین می‌کند. بیشتر درباره <a href="/datasets">مجموعه داده‌های ما</a> بدانید. اگر <a %(a_member)s>عضو</a> هستید، تأیید مرورگر لازم نیست. 🧬&nbsp;SciDB ادامه‌ای از Sci-Hub است. SciDB باز DOI Sci-Hub بارگذاری مقالات جدید را <a %(a_paused)s>متوقف کرده است</a>. دسترسی مستقیم به %(count)s مقالات علمی 🧬&nbsp;SciDB ادامه‌ای از Sci-Hub است، با رابط کاربری آشنا و مشاهده مستقیم PDFها. DOI خود را وارد کنید تا مشاهده کنید. ما مجموعه کامل Sci-Hub را داریم، همچنین مقالات جدید. بیشتر آن‌ها را می‌توان مستقیماً با یک رابط آشنا، مشابه Sci-Hub مشاهده کرد. برخی را می‌توان از منابع خارجی دانلود کرد، که در این صورت لینک‌های آن‌ها را نشان می‌دهیم. شما می‌توانید با اشتراک‌گذاری تورنت‌ها کمک بزرگی کنید. <a %(a_torrents)s>بیشتر بدانید…</a> >%(count)s بذرپاش <%(count)s بذرکاران %(count_min)s–%(count_max)s بذرکار 🤝 به دنبال داوطلبان به عنوان یک پروژه غیرانتفاعی و منبع باز، همیشه به دنبال افرادی هستیم که کمک کنند. دانلودهای IPFS فهرست توسط %(by)s، ایجاد شده <span %(span_time)s>%(time)s</span> ذخیره ❌ مشکلی پیش آمد. لطفاً دوباره تلاش کنید. ✅ ذخیره شد. لطفاً صفحه را مجدداً بارگذاری کنید. لیست خالی است. ویرایش با پیدا کردن یک فایل و باز کردن تب «فهرست‌ها»، می‌توانید به این فهرست اضافه یا از آن حذف کنید. فهرست چگونه می‌توانیم کمک کنیم حذف همپوشانی (deduplication) استخراج متن و فراداده OCR ما قادر به ارائه دسترسی با سرعت بالا به مجموعه‌های کامل خود، و همچنین به مجموعه‌های منتشر نشده هستیم. این دسترسی در سطح سازمانی است که می‌توانیم در ازای کمک‌های مالی در محدوده ده‌ها هزار دلار آمریکا ارائه دهیم. ما همچنین مایل به مبادله این با مجموعه‌های با کیفیت بالا که هنوز نداریم هستیم. ما می‌توانیم به شما بازپرداخت کنیم اگر بتوانید به ما با غنی‌سازی داده‌های ما کمک کنید، مانند: از بایگانی بلندمدت دانش بشری حمایت کنید، در حالی که داده‌های بهتری برای مدل خود دریافت می‌کنید! <a %(a_contact)s>با ما تماس بگیرید</a> تا در مورد چگونگی همکاری صحبت کنیم. به خوبی درک شده است که LLMها بر روی داده‌های با کیفیت بالا رشد می‌کنند. ما بزرگترین مجموعه کتاب‌ها، مقالات، مجلات و غیره در جهان را داریم که برخی از با کیفیت‌ترین منابع متنی هستند. داده‌های LLM مقیاس و دامنه منحصر به فرد مجموعه ما شامل بیش از صد میلیون فایل است، از جمله مجلات علمی، کتاب‌های درسی و مجلات. ما این مقیاس را با ترکیب مخازن بزرگ موجود به دست می‌آوریم. برخی از مجموعه‌های منبع ما در حال حاضر به صورت عمده در دسترس هستند (Sci-Hub و بخش‌هایی از Libgen). منابع دیگر را خودمان آزاد کردیم. <a %(a_datasets)s>Datasets</a> نمای کلی کامل را نشان می‌دهد. مجموعه ما شامل میلیون‌ها کتاب، مقاله و مجله از قبل از دوران کتاب الکترونیکی است. بخش‌های بزرگی از این مجموعه قبلاً OCR شده‌اند و هم‌اکنون همپوشانی داخلی کمی دارند. ادامه اگر کلید خود را گم کرده‌اید، لطفاً <a %(a_contact)s>با ما تماس بگیرید</a> و تا حد امکان اطلاعات بیشتری ارائه دهید. ممکن است مجبور شوید به طور موقت یک حساب جدید ایجاد کنید تا با ما تماس بگیرید. لطفاً <a %(a_account)s>وارد شوید</a> تا این صفحه را مشاهده کنید.</a> برای جلوگیری از ایجاد تعداد زیادی حساب توسط ربات‌های هرزنامه، ابتدا باید مرورگر شما را تأیید کنیم. اگر در یک حلقه بی‌نهایت گرفتار شدید، توصیه می‌کنیم <a %(a_privacypass)s>Privacy Pass</a> را نصب کنید. همچنین ممکن است خاموش کردن مسدودکننده‌های تبلیغات و سایر افزونه‌های مرورگر کمک کند. ورود / ثبت نام آرشیو آنا به طور موقت برای نگهداری پایین است. لطفاً یک ساعت دیگر بازگردید. نویسنده جایگزین توضیحات جایگزین نسخه جایگزین پسوند جایگزین نام فایل جایگزین ناشر جایگزین عنوان جایگزین تاریخ منبع باز شدن بیشتر بخوانید… توضیحات جستجوی Anna’s Archive برای شماره SSNO CADAL جستجوی Anna’s Archive برای شماره SSID DuXiu جستجو در بایگانی آنا برای شماره DXID DuXiu جستجو در آرشیو آنا برای ISBN جستجوی Anna’s Archive برای شماره OCLC (WorldCat) جستجوی Anna’s Archive برای شناسه Open Library نمایشگر آنلاین آرشیو آنا %(count)s صفحات تحت تأثیر پس از دانلود: نسخه بهتری از این فایل ممکن است در %(link)s موجود باشد دانلودهای تورنت عمده مجموعه از ابزارهای آنلاین برای تبدیل بین فرمت‌ها استفاده کنید. ابزارهای تبدیل پیشنهادی: %(links)s برای فایل‌های بزرگ، توصیه می‌کنیم از یک مدیر دانلود استفاده کنید تا از قطع شدن جلوگیری شود. مدیران دانلود پیشنهادی: %(links)s نمایه کتاب الکترونیکی EBSCOhost (فقط متخصصان) (روی عبارت "GET" در بالای صفحه کلیک کنید) (روی عبارت "GET" در بالای صفحه کلیک کنید) دانلودهای خارجی <strong>🚀 دانلودهای سریع</strong> شما %(remaining)s امروز باقی مانده دارید. با تشکر از اینکه عضو هستید! ❤️ <strong>🚀 دانلودهای سریع</strong> دانلودهای سریع شما برای امروز به پایان رسیده است. <strong>🚀 دانلودهای سریع</strong> شما این فایل را اخیراً دانلود کرده‌اید. لینک‌ها برای مدتی معتبر باقی می‌مانند. <strong>🚀 دانلودهای سریع</strong> با <a %(a_membership)s>عضویت</a> از حفظ طولانی‌مدت کتاب‌ها، مقالات و بیشتر حمایت کنید. برای نشان دادن قدردانی از حمایت شما، دانلودهای سریع دریافت می‌کنید. ❤️ 🚀 دانلودهای سریع 🐢 دانلودهای کند قرض گرفتن از Internet Archive درگاه IPFS شماره %(num)d (برای دانلود با IPFS ممکن است نیاز باشد چند بار تلاش کنید) Libgen.li Libgen.rs داستانی Libgen.rs غیر داستانی تبلیغات آن‌ها به داشتن نرم‌افزارهای مخرب معروف هستند، بنابراین از مسدودکننده تبلیغات استفاده کنید یا روی تبلیغات کلیک نکنید "ارسال به Kindle" آمازون "ارسال به Kobo/Kindle" djazz MagzDB ManualsLib Nexus/STC (فایل‌های Nexus/STC ممکن است برای دانلود غیرقابل اعتماد باشند) هیچ دانلودی یافت نشد. تمام گزینه‌های دانلود دارای فایل یکسانی هستند و باید ایمن باشند. با این حال، همیشه هنگام دانلود فایل‌ها از اینترنت، به ویژه از سایت‌های خارجی به آرشیو آنا، احتیاط کنید. به عنوان مثال، مطمئن شوید که دستگاه‌های خود را به‌روز نگه دارید. (بدون تغییر مسیر) در نمایشگر ما باز کنید (در نمایشگر باز کنید) گزینه #%(num)d: %(link)s %(extra)s یافتن ضبط اصلی در CADAL جستجوی دستی در DuXiu یافتن ضبط اصلی در ISBNdb یافتن ضبط اصلی در WorldCat یافتن ضبط اصلی در Open Library جستجو در پایگاه‌های داده مختلف برای ISBN (فقط برای کاربران ناتوان در چاپ) PubMed برای باز کردن فایل، بسته به فرمت فایل، به یک کتابخوان الکترونیکی یا PDF نیاز خواهید داشت. کتابخوان‌های الکترونیکی پیشنهادی: %(links)s آرشیو آنا 🧬 SciDB Sci-Hub: %(doi)s (DOI مرتبط ممکن است در Sci-Hub موجود نباشد) می‌توانید فایل‌های PDF و EPUB را به Kindle یا Kobo eReader خود ارسال کنید. ابزارهای پیشنهادی: %(links)s اطلاعات بیشتر در <a %(a_slow)s>پرسش‌های متداول</a>. حمایت از نویسندگان و کتابخانه‌ها اگر این را دوست دارید و توانایی مالی دارید، به خرید نسخه اصلی یا حمایت مستقیم از نویسندگان فکر کنید. اگر این در کتابخانه محلی شما موجود است، به قرض گرفتن رایگان آن از آنجا فکر کنید. دانلود از سرور شریک به طور موقت برای این فایل در دسترس نیست. تورنت از شرکای مورد اعتماد. Z-Library Z-Library TOR (نیازمند مرورگر TOR) نمایش دانلودهای خارجی <span class="font-bold">❌ این فایل ممکن است مشکلاتی داشته باشد و از یک کتابخانه منبع مخفی شده است.</span> گاهی اوقات این به درخواست صاحب حق نسخه‌برداری است، گاهی به دلیل وجود جایگزین بهتر، اما گاهی به دلیل مشکل خود فایل است. ممکن است هنوز هم دانلود آن مشکلی نداشته باشد، اما توصیه می‌کنیم ابتدا به دنبال فایل جایگزین بگردید. جزئیات بیشتر: اگر هنوز می‌خواهید این فایل را دانلود کنید، حتماً از نرم‌افزارهای معتبر و به‌روز برای باز کردن آن استفاده کنید. نظرات فراداده AA: در آرشیو آنا جستجو کنید برای “%(name)s” کاوشگر کدها: مشاهده در کاوشگر کد “%(name)s” آدرس اینترنتی: وب‌سایت: اگر این فایل را دارید و هنوز در آرشیو آنا موجود نیست، <a %(a_request)s>آن را بارگذاری کنید</a>. پرونده امانت دیجیتال کنترل‌شده Internet Archive “%(id)s” این یک ضبط از یک پرونده از Internet Archive است، نه یک فایل قابل دانلود مستقیم. می‌توانید سعی کنید کتاب را قرض بگیرید (لینک زیر)، یا از این URL هنگام <a %(a_request)s>درخواست یک فایل</a> استفاده کنید. بهبود فراداده ضبط فراداده CADAL SSNO %(id)s این یک ضبط فراداده است، نه یک فایل قابل دانلود. شما می‌توانید از این URL هنگام <a %(a_request)s>درخواست یک فایل</a> استفاده کنید. ضبط فراداده DuXiu SSID %(id)s ضبط فراداده ISBNdb %(id)s فراداده ضبط MagzDB ID %(id)s فراداده ضبط Nexus/STC ID %(id)s فراداده ضبط شماره OCLC (WorldCat) %(id)s فراداده ضبط Open Library %(id)s فایل Sci-Hub “%(id)s” پیدا نشد "%(md5_input)s" در پایگاه داده ما یافت نشد. افزودن نظر (%(count)s) می‌توانید md5 را از URL دریافت کنید، مثلاً MD5 نسخه بهتر این فایل (در صورت وجود). این را پر کنید اگر فایل دیگری وجود دارد که به این فایل نزدیک است (همان نسخه، همان پسوند فایل اگر می‌توانید پیدا کنید)، که مردم باید به جای این فایل از آن استفاده کنند. اگر نسخه بهتری از این فایل خارج از آرشیو آنا می‌شناسید، لطفاً <a %(a_upload)s>آن را بارگذاری کنید</a>. مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. شما یک نظر گذاشتید. ممکن است یک دقیقه طول بکشد تا نمایش داده شود. لطفاً از <a %(a_copyright)s>فرم ادعای DMCA / حق نسخه‌برداری</a> استفاده کنید. توضیح مشکل (الزامی) اگر این فایل کیفیت بالایی دارد، می‌توانید هر چیزی درباره آن را اینجا بحث کنید! اگر نه، لطفاً از دکمه "گزارش مشکل فایل" استفاده کنید. کیفیت عالی فایل (%(count)s) کیفیت فایل یاد بگیرید که چگونه <a %(a_metadata)s>فراداده این فایل را خودتان بهبود دهید</a>. توضیحات مشکل لطفاً <a %(a_login)s>وارد شوید</a>. من این کتاب را دوست داشتم! با گزارش کیفیت این فایل به جامعه کمک کنید! 🙌 مشکلی پیش آمد. لطفاً صفحه را مجدداً بارگذاری کرده و دوباره تلاش کنید. گزارش مشکل فایل (%(count)s) از ارسال گزارش شما متشکریم. این گزارش در این صفحه نمایش داده خواهد شد و همچنین به صورت دستی توسط آنا بررسی خواهد شد (تا زمانی که سیستم نظارت مناسبی داشته باشیم). نظر بدهید ارسال گزارش چه مشکلی با این فایل وجود دارد؟ امانت گرفتن (%(count)s) نظرات (%(count)s) دانلودها (%(count)s) کاوش فراداده (%(count)s) فهرست‌ها (%(count)s) آمار (%(count)s) برای اطلاعات بیشتر درباره این فایل خاص، به <a %(a_href)s>فایل JSON</a> آن مراجعه کنید. این یک فایل مدیریت شده توسط <a %(a_ia)s>کتابخانه امانت دیجیتال کنترل شده IA</a> است و توسط آرشیو آنا برای جستجو فهرست شده است. برای اطلاعات بیشتر درباره Datasets مختلفی که گردآوری کرده‌ایم، به <a %(a_datasets)s>صفحه Datasets</a> مراجعه کنید. فراداده از ضبط مرتبط بهبود فراداده در Open Library "فایل MD5" یک هش است که از محتوای فایل محاسبه می‌شود و بر اساس آن محتوا به طور معقولی منحصر به فرد است. تمام کتابخانه‌های سایه‌ای که در اینجا فهرست کرده‌ایم عمدتاً از MD5ها برای شناسایی فایل‌ها استفاده می‌کنند. یک فایل ممکن است در چندین کتابخانه سایه‌ای ظاهر شود. برای اطلاعات بیشتر درباره Datasets مختلفی که گردآوری کرده‌ایم، به <a %(a_datasets)s>صفحه Datasets</a> مراجعه کنید. گزارش کیفیت فایل تعداد دانلودها: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} فراداده چک %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} کتاب‌های گوگل %(id)s} گودریدز %(id)s} هایتی‌تراست %(id)s} ISBNdb %(id)s} شابک GRP %(id)s} لیبی %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} ترانتور %(id)s} هشدار: چندین ضبط مرتبط: وقتی به یک کتاب در آرشیو آنا نگاه می‌کنید، می‌توانید فیلدهای مختلفی را ببینید: عنوان، نویسنده، ناشر، ویرایش، سال، توضیحات، نام فایل و بیشتر. تمام این اطلاعات به عنوان <em>فراداده</em> شناخته می‌شوند. از آنجا که ما کتاب‌ها را از <em>کتابخانه‌های منبع</em> مختلف ترکیب می‌کنیم، هر فراداده‌ای که در آن کتابخانه منبع موجود باشد را نمایش می‌دهیم. به عنوان مثال، برای کتابی که از Library Genesis دریافت کرده‌ایم، عنوان را از پایگاه داده Library Genesis نمایش می‌دهیم. گاهی اوقات یک کتاب در <em>چندین</em> کتابخانه منبع موجود است که ممکن است فیلدهای فراداده متفاوتی داشته باشند. در این صورت، ما به سادگی طولانی‌ترین نسخه هر فیلد را نمایش می‌دهیم، زیرا امیدواریم که آن نسخه حاوی مفیدترین اطلاعات باشد! ما همچنان فیلدهای دیگر را زیر توضیحات نمایش می‌دهیم، مثلاً به عنوان "عنوان جایگزین" (اما فقط اگر متفاوت باشند). ما همچنین <em>کدها</em>یی مانند شناسه‌ها و طبقه‌بندی‌کننده‌ها را از کتابخانه منبع استخراج می‌کنیم. <em>شناسه‌ها</em> یک نسخه خاص از یک کتاب را به طور منحصر به فرد نمایندگی می‌کنند؛ مثال‌ها شامل ISBN، DOI، شناسه Open Library، شناسه Google Books یا شناسه Amazon هستند. <em>طبقه‌بندی‌کننده‌ها</em> چندین کتاب مشابه را گروه‌بندی می‌کنند؛ مثال‌ها شامل Dewey Decimal (DCC)، UDC، LCC، RVK یا GOST هستند. گاهی اوقات این کدها به طور صریح در کتابخانه‌های منبع لینک شده‌اند و گاهی اوقات می‌توانیم آنها را از نام فایل یا توضیحات استخراج کنیم (عمدتاً ISBN و DOI). ما می‌توانیم از شناسه‌ها برای یافتن ضبط‌ها در <em>مجموعه‌های فقط فراداده</em>، مانند OpenLibrary، ISBNdb یا WorldCat/OCLC استفاده کنیم. یک <em>تب فراداده</em> خاص در موتور جستجوی ما وجود دارد اگر بخواهید آن مجموعه‌ها را مرور کنید. ما از ضبط‌های مطابق برای پر کردن فیلدهای فراداده گم‌شده استفاده می‌کنیم (مثلاً اگر عنوانی گم شده باشد)، یا مثلاً به عنوان "عنوان جایگزین" (اگر عنوان موجود باشد). برای دیدن دقیقاً از کجا فراداده یک کتاب آمده است، به <em>تب "جزئیات فنی"</em> در صفحه کتاب مراجعه کنید. این تب دارای لینکی به JSON خام برای آن کتاب است، با اشاره به JSON خام ضبط‌های اصلی. برای اطلاعات بیشتر، به صفحات زیر مراجعه کنید: <a %(a_datasets)s>Datasets</a>، <a %(a_search_metadata)s>Search (metadata tab)</a>، <a %(a_codes)s>Codes Explorer</a>، و <a %(a_example)s>Example metadata JSON</a>. در نهایت، تمام فراداده‌های ما می‌توانند به صورت <a %(a_generated)s>تولید شده</a> یا <a %(a_downloaded)s>دانلود شده</a> به عنوان پایگاه‌های داده ElasticSearch و MariaDB باشند. پیش‌زمینه شما می‌توانید با بهبود فراداده به حفظ کتاب‌ها کمک کنید! ابتدا، پیش‌زمینه‌ای درباره فراداده در آرشیو آنا بخوانید، و سپس یاد بگیرید چگونه از طریق لینک‌دهی با Open Library فراداده را بهبود بخشید و عضویت رایگان در آرشیو آنا کسب کنید. بهبود فراداده پس اگر با فایلی با فراداده بد مواجه شدید، چگونه باید آن را اصلاح کنید؟ می‌توانید به کتابخانه منبع بروید و رویه‌های آن را برای اصلاح فراداده دنبال کنید، اما اگر یک فایل در چندین کتابخانه منبع موجود باشد چه باید کرد؟ یک شناسه وجود دارد که در آرشیو آنا به طور ویژه‌ای مورد توجه قرار می‌گیرد. <strong>فیلد md5 آرشیو آنا در Open Library همیشه بر تمام فراداده‌های دیگر اولویت دارد!</strong> بیایید کمی به عقب برگردیم و درباره Open Library بیشتر بیاموزیم. Open Library در سال ۲۰۰۶ توسط آرون سوارتز با هدف "یک صفحه وب برای هر کتابی که تا به حال منتشر شده" تأسیس شد. این یک نوع ویکی‌پدیا برای فراداده کتاب است: همه می‌توانند آن را ویرایش کنند، به صورت آزادانه مجوز دارد و می‌توان آن را به صورت عمده دانلود کرد. این یک پایگاه داده کتاب است که بیشترین هم‌راستایی را با مأموریت ما دارد — در واقع، آرشیو آنا از دیدگاه و زندگی آرون سوارتز الهام گرفته است. به جای اختراع دوباره چرخ، تصمیم گرفتیم داوطلبان خود را به سمت Open Library هدایت کنیم. اگر کتابی را با فراداده نادرست مشاهده کردید، می‌توانید به روش زیر کمک کنید: توجه داشته باشید که این فقط برای کتاب‌ها کار می‌کند، نه مقالات علمی یا انواع دیگر فایل‌ها. برای انواع دیگر فایل‌ها همچنان توصیه می‌کنیم منبع کتابخانه را پیدا کنید. ممکن است چند هفته طول بکشد تا تغییرات در آرشیو آنا گنجانده شود، زیرا ما نیاز داریم آخرین داده‌های Open Library را دانلود کنیم و شاخص جستجوی خود را بازسازی کنیم.  به <a %(a_openlib)s>وب‌سایت Open Library</a> بروید. ضبط کتاب صحیح را پیدا کنید. <strong>هشدار:</strong> حتماً نسخه <strong>درست</strong> را انتخاب کنید. در Open Library، شما "آثار" و "نسخه‌ها" دارید. یک "اثر" می‌تواند "هری پاتر و سنگ جادو" باشد. یک "نسخه" می‌تواند: نسخه اول سال ۱۹۹۷ منتشر شده توسط بلومزبری با ۲۵۶ صفحه. نسخه شومیز سال ۲۰۰۳ منتشر شده توسط رینکوست بوکس با ۲۲۳ صفحه. ترجمه لهستانی سال ۲۰۰۰ "هری پاتر و سنگ جادو" توسط مدیا رودزینا با ۳۲۸ صفحه. همه این نسخه‌ها دارای ISBN و محتوای متفاوتی هستند، بنابراین حتماً نسخه صحیح را انتخاب کنید! ضبط را ویرایش کنید (یا اگر وجود ندارد، ایجاد کنید) و تا حد ممکن اطلاعات مفید اضافه کنید! حالا که اینجا هستید، بهتر است ضبط را واقعاً شگفت‌انگیز کنید. در بخش "شماره‌های شناسایی" گزینه "آرشیو آنا" را انتخاب کنید و MD5 کتاب را از آرشیو آنا اضافه کنید. این رشته طولانی از حروف و اعداد بعد از "/md5/" در URL است. سعی کنید فایل‌های دیگری را در آرشیو آنا پیدا کنید که با این ضبط همخوانی دارند و آن‌ها را نیز اضافه کنید. در آینده می‌توانیم آن‌ها را به عنوان نسخه‌های تکراری در صفحه جستجوی آرشیو آنا گروه‌بندی کنیم. وقتی کارتان تمام شد، URL که به‌روزرسانی کرده‌اید را یادداشت کنید. وقتی حداقل ۳۰ ضبط با MD5های آرشیو آنا به‌روزرسانی کردید، یک <a %(a_contact)s>ایمیل</a> برای ما ارسال کنید و لیست را بفرستید. ما به شما یک عضویت رایگان برای آرشیو آنا می‌دهیم تا بتوانید این کار را راحت‌تر انجام دهید (و به عنوان تشکر از کمک شما). این باید ویرایش‌های با کیفیت بالا باشد که مقدار قابل توجهی اطلاعات اضافه کند، در غیر این صورت درخواست شما رد خواهد شد. درخواست شما همچنین در صورتی رد خواهد شد که هر یک از ویرایش‌ها توسط مدیران Open Library بازگردانده یا اصلاح شود. لینک‌دهی Open Library اگر به‌طور قابل توجهی در توسعه و عملیات کار ما درگیر شوید، می‌توانیم در مورد به اشتراک‌گذاری بیشتر درآمدهای اهدایی با شما صحبت کنیم تا به‌طور لازم از آن استفاده کنید. ما فقط زمانی هزینه میزبانی را پرداخت می‌کنیم که همه چیز را راه‌اندازی کرده باشید و نشان داده باشید که می‌توانید آرشیو را با به‌روزرسانی‌ها به‌روز نگه دارید. این بدان معناست که شما باید هزینه ۱-۲ ماه اول را از جیب خود بپردازید. زمان شما جبران نخواهد شد (و زمان ما نیز همین‌طور)، زیرا این کار کاملاً داوطلبانه است. ما مایل به پوشش هزینه‌های میزبانی و VPN هستیم، در ابتدا تا ۲۰۰ دلار در ماه. این مبلغ برای یک سرور جستجوی پایه و یک پروکسی محافظت‌شده توسط DMCA کافی است. هزینه‌های میزبانی لطفاً <strong>با ما تماس نگیرید</strong> تا اجازه بگیرید یا سوالات ابتدایی بپرسید. عمل‌ها بلندتر از کلمات صحبت می‌کنند! تمام اطلاعات در دسترس است، بنابراین فقط با راه‌اندازی لینک کمکی خود پیش بروید. احساس راحتی کنید که بلیط‌ها یا درخواست‌های ادغام را به گیت‌لب ما ارسال کنید وقتی با مشکلاتی مواجه می‌شوید. ممکن است نیاز داشته باشیم برخی ویژگی‌های خاص لینک کمکی را با شما بسازیم، مانند تغییر برند از "آرشیو آنا" به نام وب‌سایت شما، (در ابتدا) غیرفعال کردن حساب‌های کاربری، یا لینک دادن به سایت اصلی ما از صفحات کتاب. وقتی لینک کمکی خود را راه‌اندازی کردید، لطفاً با ما تماس بگیرید. ما دوست داریم امنیت عملیاتی شما را بررسی کنیم و وقتی که آن محکم بود، به لینک کمکی شما لینک دهیم و شروع به همکاری نزدیک‌تر با شما کنیم. پیشاپیش از هر کسی که مایل به مشارکت به این روش است تشکر می‌کنیم! این کار برای افراد ضعیف‌دل نیست، اما طول عمر بزرگترین کتابخانه واقعاً باز در تاریخ بشر را تثبیت می‌کند. شروع به کار برای افزایش مقاومت بایگانی آنا، ما به دنبال داوطلبانی هستیم که آینه‌ها را اجرا کنند. نسخه شما به‌وضوح به‌عنوان یک لینک کمکی متمایز است، مثلاً "آرشیو باب، یک لینک کمکی آرشیو آنا". شما مایل به پذیرش ریسک‌های مرتبط با این کار هستید که قابل توجه هستند. شما درک عمیقی از امنیت عملیاتی مورد نیاز دارید. محتوای <a %(a_shadow)s>این</a> <a %(a_pirate)s>پست‌ها</a> برای شما بدیهی است. در ابتدا ما به شما دسترسی به دانلودهای سرور شریک خود را نمی‌دهیم، اما اگر همه چیز خوب پیش برود، می‌توانیم آن را با شما به اشتراک بگذاریم. شما کدبیس متن‌باز آرشیو آنا را اجرا می‌کنید و به‌طور منظم هم کد و هم داده‌ها را به‌روزرسانی می‌کنید. شما مایل به همکاری با تیم ما برای مشارکت در <a %(a_codebase)s>کدبیس</a> ما هستید تا این کار را انجام دهید. ما به دنبال این هستیم: آینه‌ها: فراخوانی برای داوطلبان یک کمک مالی دیگر انجام دهید. هنوز هیچ کمکی نشده است. <a %(a_donate)s>اولین کمک مالی من.</a> جزئیات کمک‌های مالی به صورت عمومی نمایش داده نمی‌شوند. کمک‌های مالی من 📡 برای آینه‌سازی عمده مجموعه ما، صفحات <a %(a_datasets)s>Datasets</a> و <a %(a_torrents)s>Torrents</a> را بررسی کنید. دانلودها از آدرس IP شما در ۲۴ ساعت گذشته: %(count)s. 🚀 برای دریافت سریعتر دانلودها و رد شدن از چک مرورگر، <a %(a_membership)s>عضو شوید</a>. از وب سایت شریک دانلود کنید در حین انتظار، می‌توانید به مرور آرشیو آنا در یک تب دیگر ادامه دهید (اگر مرورگر شما از تازه‌سازی تب‌های پس‌زمینه پشتیبانی می‌کند). احساس راحتی کنید و منتظر بمانید تا چندین صفحه دانلود به طور همزمان بارگذاری شوند (اما لطفاً فقط یک فایل را به طور همزمان از هر سرور دانلود کنید). پس از دریافت لینک دانلود، آن برای چندین ساعت معتبر است. از صبر شما متشکریم، این کار دسترسی رایگان به وب‌سایت را برای همه فراهم می‌کند! 😊 🔗 همه لینک‌های دانلود برای این فایل: <a %(a_main)s>صفحه اصلی فایل</a>. ❌ دانلودهای کند از طریق VPNهای Cloudflare یا به هر صورت از آدرس‌های IP Cloudflare در دسترس نیستند. ❌ دانلودهای کند فقط از طریق وب‌سایت رسمی در دسترس هستند. بازدید کنید %(websites)s. 📚 از آدرس زیر برای دانلود استفاده کنید: <a %(a_download)s>لینک دانلود</a>. برای اینکه به همه فرصت دانلود رایگان فایل‌ها داده شود، باید قبل از دانلود این فایل صبر کنید. لطفاً <span %(span_countdown)s>%(wait_seconds)s</span> ثانیه صبر کنید تا این فایل دانلود شود. هشدار: در ۲۴ ساعت گذشته دانلودهای زیادی از آدرس IP شما انجام شده است. دانلودها ممکن است کندتر از حد معمول باشند. اگر از VPN، اتصال اینترنت مشترک، یا ISP که IPها را به اشتراک می‌گذارد استفاده می‌کنید، این هشدار ممکن است به دلیل آن باشد. ذخیره ❌ مشکلی پیش آمد. لطفاً دوباره تلاش کنید. ✅ ذخیره شد. لطفاً صفحه را مجدداً بارگذاری کنید. نام نمایشی خود را تغییر دهید. شناسه شما (قسمت بعد از “#”) قابل تغییر نیست. پروفایل ایجاد شده <span %(span_time)s>%(time)s</span> ویرایش فهرست‌ها با پیدا کردن یک فایل و باز کردن تب "فهرست‌ها"، یک فهرست جدید ایجاد کنید. هنوز هیچ فهرستی وجود ندارد پروفایل یافت نشد. پروفایل در حال حاضر، نمی‌توانیم درخواست‌های کتاب را بپذیریم. لطفاً درخواست‌های کتاب خود را به ما ایمیل نکنید. لطفاً درخواست‌های خود را در انجمن‌های Z-Library یا Libgen مطرح کنید. ضبط در آرشیو آنا DOI: %(doi)s دانلود SciDB Nexus/STC هنوز پیش‌نمایشی موجود نیست. فایل را از <a %(a_path)s>آرشیو آنا</a> دانلود کنید. برای حمایت از دسترسی و حفظ طولانی‌مدت دانش بشری، <a %(a_donate)s>عضو</a> شوید. به عنوان یک امتیاز، 🧬&nbsp;SciDB برای اعضا سریع‌تر بارگذاری می‌شود و بدون هیچ محدودیتی. کار نمی‌کند؟ سعی کنید <a %(a_refresh)s>تازه‌سازی</a>. Sci-Hub افزودن فیلد جستجوی خاص توضیحات و نظرات فراداده را جستجو کنید سال انتشار پیشرفته دسترسی محتوای نمایش فهرست جدول نوع فایل زبان مرتب‌سازی بر اساس بزرگترین مرتبط ترین جدیدترین (حجم فایل) (منبع باز) (سال انتشار) قدیمی ترین تصادفی کوچکترین منبع توسط AA جمع‌آوری و منبع‌باز شده است امانت دیجیتال (%(count)s) مقالات ژورنال (%(count)s) ما تطابق‌هایی در: %(in)s پیدا کرده‌ایم. می‌توانید به URL یافت شده در آنجا هنگام <a %(a_request)s>درخواست یک فایل</a> اشاره کنید. فراداده (%(count)s) برای کاوش در شاخص جستجو با کدها، از <a %(a_href)s>کاوشگر کدها</a> استفاده کنید. شاخص جستجو ماهانه به‌روزرسانی می‌شود. در حال حاضر شامل ورودی‌ها تا %(last_data_refresh_date)s است. برای اطلاعات فنی بیشتر، به <a %(link_open_tag)s>صفحه Datasets</a> مراجعه کنید. حذف کنید فقط شامل کنید بررسی نشده بیشتر… بعدی … قبلی این شاخص جستجو در حال حاضر شامل فراداده از کتابخانه امانت دیجیتال کنترل‌شده Internet Archive است. <a %(a_datasets)s>بیشتر درباره Datasets ما</a>. برای کتابخانه‌های امانت دیجیتال بیشتر، به <a %(a_wikipedia)s>ویکی‌پدیا</a> و <a %(a_mobileread)s>ویکی MobileRead</a> مراجعه کنید. برای ادعاهای DMCA / حق‌تکثیر <a %(a_copyright)s>اینجا کلیک کنید</a>. زمان دانلود خطا در حین جستجو. <a %(a_reload)s>بارگذاری مجدد صفحه</a> را امتحان کنید. اگر مشکل ادامه داشت، لطفاً به ما در %(email)s ایمیل بزنید. بارگیری سریع در واقع، هر کسی می‌تواند با اشتراک‌گذاری <a %(a_torrents)s>فهرست یکپارچه تورنت‌های ما</a> به حفظ این فایل‌ها کمک کند. ➡️ گاهی اوقات این اتفاق به اشتباه می‌افتد وقتی که سرور جستجو کند است. در چنین مواردی، <a %(a_attrs)s>بارگذاری مجدد</a> می‌تواند کمک کند. ❌ این فایل ممکن است مشکلاتی داشته باشد. دنبال مقالات می‌گردید؟ این شاخص جستجو در حال حاضر شامل فراداده از منابع مختلف فراداده است. <a %(a_datasets)s>بیشتر درباره مجموعه داده‌های ما</a>. منابع بسیاری برای فراداده آثار نوشتاری در سراسر جهان وجود دارد. <a %(a_wikipedia)s>این صفحه ویکی‌پدیا</a> یک شروع خوب است، اما اگر لیست‌های خوب دیگری می‌شناسید، لطفاً به ما اطلاع دهید. برای فراداده، ما رکوردهای اصلی را نمایش می‌دهیم. هیچ ادغامی از رکوردها انجام نمی‌دهیم. ما در حال حاضر جامع‌ترین فهرست باز کتاب‌ها، مقالات و دیگر آثار نوشتاری جهان را داریم. ما لینک‌های کمکی Sci-Hub، Library Genesis، Z-Library، <a %(a_datasets)s>و بیشتر</a> را ارائه می‌دهیم. <span class="font-bold">هیچ فایلی یافت نشد.</span> از عبارات جستجو و فیلترهای کمتر یا متفاوت استفاده کنید. نتایج %(from)s-%(to)s (%(total)s مجموع) اگر "کتابخانه‌های سایه" دیگری پیدا کردید که باید لینک کمکی کنیم، یا اگر سوالی دارید، لطفاً با ما در %(email)s تماس بگیرید. %(num)d مسابقات جزئی %(num)d+ مسابقات جزئی برای جستجوی فایل‌ها در کتابخانه‌های امانت دیجیتال در کادر تایپ کنید. در جعبه تایپ کنید تا در کاتالوگ ما از %(count)s فایل‌های قابل دانلود مستقیم جستجو کنید، که ما <a %(a_preserve)s>برای همیشه حفظ می‌کنیم</a>. در کادر تایپ کنید تا جستجو کنید. در جعبه تایپ کنید تا در کاتالوگ ما از %(count)s مقالات علمی و مقالات ژورنالی جستجو کنید، که ما <a %(a_preserve)s>برای همیشه حفظ می‌کنیم</a>. در جعبه تایپ کنید تا برای فراداده از کتابخانه‌ها جستجو کنید. این می‌تواند هنگام <a %(a_request)s>درخواست یک فایل</a> مفید باشد. نکته: از میانبرهای صفحه‌کلید “/” (تمرکز جستجو)، “enter” (جستجو)، “j” (بالا)، “k” (پایین)، “<” (صفحه قبلی)، “>” (صفحه بعدی) برای ناوبری سریع‌تر استفاده کنید. این‌ها رکوردهای فراداده هستند، <span %(classname)s>نه</span> فایل‌های قابل دانلود. تنظیمات جستجو جستجو امانت دیجیتال دانلود مقالات ژورنال فراداده جستجو جدید %(search_input)s - جستجو جستجو بیش از حد طول کشید، که به این معنی است که ممکن است نتایج نادرستی ببینید. گاهی اوقات <a %(a_reload)s>بارگذاری مجدد</a> صفحه کمک می‌کند. جستجو بیش از حد طول کشید، که برای جستجوهای گسترده معمول است. شمارش فیلترها ممکن است دقیق نباشد. برای بارگذاری‌های بزرگ (بیش از ۱۰,۰۰۰ فایل) که توسط Libgen یا Z-Library پذیرفته نمی‌شوند، لطفاً با ما در %(a_email)s تماس بگیرید. برای Libgen.li، مطمئن شوید که ابتدا با نام کاربری %(username)s و رمز عبور %(password)s در <a %(a_forum)s >انجمن آن‌ها</a> وارد شوید و سپس به <a %(a_upload_page)s >صفحه آپلود</a> آن‌ها بازگردید. فعلاً پیشنهاد می‌کنیم کتاب‌های جدید را به شاخه‌های Library Genesis بارگذاری کنید. اینجا یک <a %(a_guide)s>راهنمای مفید</a> است. توجه داشته باشید که هر دو شاخه‌ای که ما در این وب‌سایت فهرست می‌کنیم از همین سیستم بارگذاری استفاده می‌کنند. برای آپلودهای کوچک (تا ۱۰,۰۰۰ فایل) لطفاً آن‌ها را به هر دو %(first)s و %(second)s آپلود کنید. به‌طور جایگزین، می‌توانید آنها را در Z-Library <a %(a_upload)s>اینجا</a> آپلود کنید. برای آپلود مقالات علمی، لطفاً علاوه بر Library Genesis، به <a %(a_stc_nexus)s>STC Nexus</a> نیز آپلود کنید. آنها بهترین کتابخانه سایه‌ای برای مقالات جدید هستند. ما هنوز آنها را یکپارچه نکرده‌ایم، اما در آینده این کار را خواهیم کرد. می‌توانید از <a %(a_telegram)s>ربات آپلود آنها در تلگرام</a> استفاده کنید، یا اگر فایل‌های زیادی برای آپلود دارید، با آدرس ذکر شده در پیام پین شده آنها تماس بگیرید. <span %(label)s>کار داوطلبانه سنگین (پاداش‌های ۵۰ تا ۵۰۰۰ دلار آمریکا):</span> اگر می‌توانید زمان و/یا منابع زیادی را به مأموریت ما اختصاص دهید، دوست داریم با شما نزدیک‌تر کار کنیم. در نهایت می‌توانید به تیم داخلی بپیوندید. اگرچه بودجه ما محدود است، اما می‌توانیم برای کارهای بسیار فشرده <span %(bold)s>💰 پاداش‌های مالی</span> اعطا کنیم. <span %(label)s>کار داوطلبانه سبک:</span> اگر فقط می‌توانید چند ساعت اینجا و آنجا وقت بگذارید، هنوز هم راه‌های زیادی وجود دارد که می‌توانید کمک کنید. ما داوطلبان مستمر را با <span %(bold)s>🤝 عضویت در آرشیو آنا</span> پاداش می‌دهیم. آرشیو آنا به داوطلبانی مانند شما متکی است. ما از همه سطوح تعهد استقبال می‌کنیم و دو دسته اصلی کمک داریم که به دنبال آن هستیم: اگر نمی‌توانید زمان خود را داوطلبانه اختصاص دهید، هنوز هم می‌توانید با <a %(a_donate)s>اهدا پول</a>، <a %(a_torrents)s>اشتراک‌گذاری تورنت‌های ما</a>، <a %(a_uploading)s>آپلود کتاب‌ها</a>، یا <a %(a_help)s>گفتن به دوستانتان درباره آرشیو آنا</a> به ما کمک زیادی کنید. <span %(bold)s>شرکت‌ها:</span> ما دسترسی مستقیم با سرعت بالا به مجموعه‌های خود را در ازای اهدا در سطح سازمانی یا تبادل برای مجموعه‌های جدید (مثلاً اسکن‌های جدید، دیتاست‌های OCR شده، غنی‌سازی داده‌های ما) ارائه می‌دهیم. <a %(a_contact)s>با ما تماس بگیرید</a> اگر شما این شرایط را دارید. همچنین صفحه <a %(a_llm)s>LLM</a> ما را ببینید. جوایز ما همیشه به دنبال افرادی با مهارت‌های برنامه‌نویسی قوی یا امنیت تهاجمی هستیم تا درگیر شوند. شما می‌توانید تأثیر جدی در حفظ میراث بشریت داشته باشید. به عنوان تشکر، ما برای مشارکت‌های قوی عضویت رایگان می‌دهیم. به عنوان یک تشکر بزرگ، ما برای وظایف بسیار مهم و دشوار جوایز نقدی می‌دهیم. این نباید به عنوان جایگزینی برای یک شغل دیده شود، اما یک انگیزه اضافی است و می‌تواند به هزینه‌های متحمل شده کمک کند. بیشتر کدهای ما منبع باز هستند و ما از شما نیز خواهیم خواست که کدهای خود را هنگام اعطای جایزه منبع باز کنید. برخی استثناها وجود دارد که می‌توانیم به صورت فردی در مورد آنها بحث کنیم. جوایز به اولین فردی که یک وظیفه را تکمیل کند اعطا می‌شود. احساس راحتی کنید که در یک بلیط جایزه نظر بدهید تا دیگران بدانند که شما روی چیزی کار می‌کنید، بنابراین دیگران می‌توانند صبر کنند یا با شما تماس بگیرند تا تیم تشکیل دهند. اما آگاه باشید که دیگران همچنان آزاد هستند که روی آن کار کنند و سعی کنند شما را شکست دهند. با این حال، ما برای کارهای ضعیف جوایز نمی‌دهیم. اگر دو ارسال با کیفیت بالا نزدیک به هم انجام شود (در عرض یک یا دو روز)، ممکن است تصمیم بگیریم که به هر دو جایزه بدهیم، به عنوان مثال 100%% برای اولین ارسال و 50%% برای دومین ارسال (بنابراین مجموعاً 150%%). برای جوایز بزرگتر (به ویژه جوایز خراشیدن)، لطفاً وقتی حدود ~5%% از آن را تکمیل کردید و مطمئن هستید که روش شما به مقیاس کامل می‌رسد، با ما تماس بگیرید. شما باید روش خود را با ما به اشتراک بگذارید تا بتوانیم بازخورد بدهیم. همچنین، به این ترتیب می‌توانیم تصمیم بگیریم که چه کاری انجام دهیم اگر چندین نفر به جایزه نزدیک شوند، مانند اعطای آن به چندین نفر، تشویق افراد به تشکیل تیم و غیره. هشدار: وظایف با جایزه بالا <span %(bold)s>دشوار</span> هستند — ممکن است عاقلانه باشد که با وظایف آسان‌تر شروع کنید. به <a %(a_gitlab)s>لیست مشکلات Gitlab ما</a> بروید و بر اساس "اولویت برچسب" مرتب کنید. این تقریباً ترتیب وظایفی را که برای ما مهم هستند نشان می‌دهد. وظایفی که جایزه صریح ندارند همچنان واجد شرایط عضویت هستند، به ویژه آنهایی که با "پذیرفته شده" و "مورد علاقه آنا" علامت‌گذاری شده‌اند. ممکن است بخواهید با یک "پروژه شروع کننده" شروع کنید. کار داوطلبانه سبک ما اکنون یک کانال همگام‌سازی شده در ماتریکس به آدرس %(matrix)s داریم. اگر چند ساعت وقت آزاد دارید، می‌توانید به چندین روش کمک کنید. حتماً به <a %(a_telegram)s>گفتگوی داوطلبان در تلگرام</a> بپیوندید. به عنوان نشانه‌ای از قدردانی، معمولاً ۶ ماه عضویت «کتابدار خوش‌شانس» را برای دستاوردهای پایه و بیشتر برای کار داوطلبانه مستمر اعطا می‌کنیم. همه دستاوردها نیاز به کار با کیفیت بالا دارند — کار بی‌کیفیت به ما بیشتر آسیب می‌زند تا کمک و آن را رد خواهیم کرد. لطفاً <a %(a_contact)s>به ما ایمیل بزنید</a> وقتی به یک دستاورد رسیدید. %(links)s لینک‌ها یا اسکرین‌شات‌های درخواست‌هایی که انجام داده‌اید. برآورده کردن درخواست‌های کتاب (یا مقاله و غیره) در انجمن‌های Z-Library یا Library Genesis. ما سیستم درخواست کتاب خودمان را نداریم، اما آن کتابخانه‌ها را لینک کمکی می‌کنیم، بنابراین بهتر کردن آن‌ها آرشیو آنا را نیز بهتر می‌کند. دستاورد وظیفه بسته به وظیفه. وظایف کوچک ارسال شده در <a %(a_telegram)s>گفتگوی داوطلبان در تلگرام</a>. معمولاً برای عضویت، گاهی برای پاداش‌های کوچک. وظایف کوچک در گروه چت داوطلبان ما ارسال می‌شود. اطمینان حاصل کنید که بر روی مشکلاتی که حل می‌کنید، نظری بگذارید تا دیگران کار شما را تکرار نکنند. %(links)s لینک‌های رکوردهایی که بهبود داده‌اید. می‌توانید از <a %(a_list)s>فهرست مشکلات متادیتای تصادفی</a> به عنوان نقطه شروع استفاده کنید. بهبود فراداده با <a %(a_metadata)s>پیوند دادن</a> به Open Library. این‌ها باید نشان دهند که شما به کسی درباره آرشیو آنا اطلاع می‌دهید و او از شما تشکر می‌کند. %(links)s لینک‌ها یا اسکرین‌شات‌ها. گسترش خبر آرشیو آنا. به عنوان مثال، با توصیه کتاب‌ها در AA، لینک دادن به پست‌های وبلاگ ما، یا به طور کلی هدایت افراد به وب‌سایت ما. ترجمه کامل یک زبان (اگر قبلاً نزدیک به تکمیل نبوده باشد.) <a %(a_translate)s>ترجمه</a> وب‌سایت. لینک به تاریخچه ویرایش که نشان می‌دهد شما مشارکت‌های قابل توجهی داشته‌اید. بهبود صفحه ویکی‌پدیا برای آرشیو آنا به زبان خودتان. شامل اطلاعات از صفحه ویکی‌پدیای AA به زبان‌های دیگر و از وب‌سایت و وبلاگ ما. افزودن ارجاعات به AA در صفحات مرتبط دیگر. داوطلبی و پاداش‌ها 