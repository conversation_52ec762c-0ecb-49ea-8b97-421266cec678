#, fuzzy
msgid "layout.index.invalid_request"
msgstr "Permintaan tidak sah. Lawati %(websites)s."

msgid "layout.index.header.tagline_scihub"
msgstr "Sci-Hub"

msgid "layout.index.header.tagline_libgen"
msgstr "LibGen"

msgid "layout.index.header.tagline_zlib"
msgstr "Z-Lib"

msgid "layout.index.header.tagline_openlib"
msgstr "OpenLib"

#, fuzzy
msgid "layout.index.header.tagline_ia"
msgstr "Perpustakaan <PERSON>man Internet Archive"

msgid "layout.index.header.tagline_duxiu"
msgstr "DuXiu"

msgid "layout.index.header.tagline_separator"
msgstr ", "

#, fuzzy
msgid "layout.index.header.tagline_and"
msgstr " dan "

#, fuzzy
msgid "layout.index.header.tagline_and_more"
msgstr "dan banyak lagi"

#, fuzzy
msgid "layout.index.header.tagline_newnew2a"
msgstr "⭐️&nbsp;<PERSON><PERSON> %(libraries)s."

#, fuzzy
msgid "layout.index.header.tagline_newnew2b"
msgstr "Ka<PERSON> mengikis dan sumber terbuka %(scraped)s."

#, fuzzy
msgid "layout.index.header.tagline_open_source"
msgstr "Semua kod dan data kami adalah sumber terbuka sepenuhnya."

msgid "layout.index.header.tagline_new1"
msgstr "📚&nbsp;Perpustakaan terbuka terbesar dalam sejarah manusia."

msgid "layout.index.header.tagline_new3"
msgstr "📈&nbsp;%(book_count)s&nbsp;buku-buku, %(paper_count)s&nbsp;kertas penyelidikan — terpelihara selamanya."

msgid "layout.index.header.tagline"
msgstr "📚&nbsp;Perpustakaan sumber terbuka data terbuka terbesar di dunia. ⭐️&nbsp;Cermin Sci-Hub, Library Genesis, Z-Library, dan banyak lagi. 📈&nbsp;%(book_any)s buku-buku, %(journal_article)s kertas-kertas penyelidikan, %(book_comic)s komik-komik, %(magazine)s majalah-majalah — terpelihara selamanya."

msgid "layout.index.header.tagline_short"
msgstr "📚 Perpustakaan sumber terbuka data terbuka terbesar di dunia.<br>⭐️ Cermin Scihub, Libgen, Zlib, dan banyak lagi."

msgid "common.md5_report_type_mapping.metadata"
msgstr "Metadata salah (cth. judul, huraian, gambar muka depan)"

msgid "common.md5_report_type_mapping.download"
msgstr "Masalah memuat turn (cth. tidak boleh menyambung, mesej ralat, sambungan sangat lambat)"

msgid "common.md5_report_type_mapping.broken"
msgstr "Fail tidak boleh dibuka (cth. fail rosak, DRM)"

msgid "common.md5_report_type_mapping.pages"
msgstr "Kualiti rendah (cth. masalah format, kualiti imbas rendah, halaman hilang)"

msgid "common.md5_report_type_mapping.spam"
msgstr "Spam / fail perlu dikeluarkan (cth. iklan, kandungan kesat)"

msgid "common.md5_report_type_mapping.copyright"
msgstr "Tuntutan hak cipta"

msgid "common.md5_report_type_mapping.other"
msgstr "Lain-lain"

#, fuzzy
msgid "common.membership.tier_name.bonus"
msgstr "Muat turun bonus"

msgid "common.membership.tier_name.2"
msgstr "Pembaca Pintar"

msgid "common.membership.tier_name.3"
msgstr "Cendekiawan Celik"

#, fuzzy
msgid "common.membership.tier_name.4"
msgstr "Pengumpul Data Hebat"

msgid "common.membership.tier_name.5"
msgstr "Arkib Anggun"

msgid "common.membership.format_currency.total"
msgstr "%(amount)s jumlah"

msgid "common.membership.format_currency.total_with_usd"
msgstr "%(amount)s %(amount_usd)s jumlah"

msgid "common.membership.format_currency.amount_with_usd"
msgstr "%(amount)s %(amount_usd)s"

#, fuzzy
msgid "common.donation.membership_bonus_parens"
msgstr " (+%(num)s bonus)"

msgid "common.donation.order_processing_status_labels.0"
msgstr "belum dibayar"

msgid "common.donation.order_processing_status_labels.1"
msgstr "telah dibayar"

msgid "common.donation.order_processing_status_labels.2"
msgstr "dibatalkan"

msgid "common.donation.order_processing_status_labels.3"
msgstr "tamat tempoh"

msgid "common.donation.order_processing_status_labels.4"
msgstr "menunggu pengesahan daripada Anna"

#, fuzzy
msgid "common.donation.order_processing_status_labels.5"
msgstr "tidak sah"

msgid "page.donate.title"
msgstr "Derma"

msgid "page.donate.header.existing_unpaid_donation"
msgstr "Anda ada dermaan sebanyak %(a_donation)s yang belum selesai. Sila selesaikan atau batalkan dermaan tersebut sebelum membuat dermaan yang baharu."

msgid "page.donate.header.existing_unpaid_donation_view_all"
msgstr "<a %(a_all_donations)s> Lihat semua sumbangan saya</a>"

msgid "page.donate.header.text1"
msgstr "Anna's Archive ialah projek tanpa untung, sumber terbuka dan data terbuka. Dengan menderma dan daftar menjadi ahli, anda boleh menyokong operasi dan pembangunan kami. Kepada semua ahli-ahli kami: Terima kasih kerana menyokong kami! ❤️"

#, fuzzy
msgid "page.donate.header.text2"
msgstr "Untuk maklumat lanjut, lihat <a %(a_donate)s>FAQ Derma</a>."

#, fuzzy
msgid "page.donate.refer.text1"
msgstr "Untuk mendapatkan lebih banyak muat turun, <a %(a_refer)s>rujuk rakan anda</a>!"

#, fuzzy
msgid "page.donate.bonus_downloads.main"
msgstr "Anda mendapat %(percentage)s%% muat turun bonus pantas, kerana anda dirujuk oleh pengguna %(profile_link)s."

#, fuzzy
msgid "page.donate.bonus_downloads.period"
msgstr "Ini terpakai untuk keseluruhan tempoh keahlian."

msgid "page.donate.perks.fast_downloads"
msgstr "%(number)s muat turun dalam sehari"

#, fuzzy
msgid "page.donate.perks.if_you_donate_this_month"
msgstr "jika anda menderma bulan ini!"

#, fuzzy
msgid "page.donate.membership_per_month"
msgstr "$%(cost)s / bulan"

msgid "page.donate.buttons.join"
msgstr "Sertai"

msgid "page.donate.buttons.selected"
msgstr "Dipilih"

msgid "page.donate.buttons.up_to_discounts"
msgstr "Diskaun sehingga %(percentage)s"

msgid "page.donate.perks.scidb"
msgstr "kertas SciDB <strong>tanpa had</strong> tanpa verifikasi"

#, fuzzy
msgid "page.donate.perks.jsonapi"
msgstr "Akses <a %(a_api)s>JSON API</a>"

#, fuzzy
msgid "page.donate.perks.refer"
msgstr "Dapatkan <strong>%(percentage)s%% muat turun bonus</strong> dengan <a %(a_refer)s>merujuk rakan</a>."

msgid "page.donate.perks.credits"
msgstr "Nama pengguna anda atau sebutan tanpa nama dalam kredit"

msgid "page.donate.perks.previous_plus"
msgstr "Faedah sebulumnya, termasuk:"

msgid "page.donate.perks.early_access"
msgstr "Akses awal kepada ciri - ciri baharu"

msgid "page.donate.perks.exclusive_telegram"
msgstr "Telegram eksklusif untuk kemas kini di belakang tabir"

msgid "page.donate.perks.adopt"
msgstr "“Adopt a torrent”: nama pengguna atau mesej di dalam nama fail torrent <div %(div_months)s>sekali setiap 12 bulan keahlian</div>"

#, fuzzy
msgid "page.donate.perks.legendary"
msgstr "Status legenda dalam pemeliharaan pengetahuan dan budaya manusia"

msgid "page.donate.expert.title"
msgstr "Akses Pakar"

#, fuzzy
msgid "page.donate.expert.contact_us"
msgstr "hubungi kami"

#, fuzzy
msgid "page.donate.small_team"
msgstr "Kami adalah pasukan kecil sukarelawan. Ia mungkin mengambil masa 1-2 minggu untuk kami memberi maklum balas."

msgid "page.donate.expert.unlimited_access"
msgstr "Akses berkelajuan tinggi <strong>tanpa had</strong>"

msgid "page.donate.expert.direct_sftp"
msgstr "Pelayan <strong>SFTP</strong> langsung"

msgid "page.donate.expert.enterprise_donation"
msgstr "Derma peringkat perusahaan atau pertukaran koleksi baharu (cth. imbasan-imbasan baharu, dataset OCR)."

#, fuzzy
msgid "page.donate.header.large_donations_wealthy"
msgstr "Kami mengalu-alukan sumbangan besar daripada individu atau institusi yang kaya. "

#, fuzzy
msgid "page.donate.header.large_donations"
msgstr "Untuk dermaan melebihi $5000, sila hubungi kami terus di %(email)s."

#, fuzzy
msgid "page.donate.header.recurring"
msgstr "Sila ambil perhatian bahawa walaupun keahlian di halaman ini adalah \"sebulan\", ia adalah derma sekali sahaja (tidak berulang). Lihat <a %(faq)s>FAQ Derma</a>."

#, fuzzy
msgid "page.donate.without_membership"
msgstr "Jika anda ingin membuat sumbangan (sebarang jumlah) tanpa keahlian, sila gunakan alamat Monero (XMR) ini: %(address)s."

#, fuzzy
msgid "page.donate.payment.select_method"
msgstr "Sila pilih kaedah pembayaran."

msgid "page.donate.discount"
msgstr "-%(percentage)s%%"

msgid "page.donate.payment.buttons.temporarily_unavailable"
msgstr "(tidak boleh diakses buat sementara waktu)"

#, fuzzy
msgid "page.donate.payment.buttons.amazon_cc"
msgstr "%(amazon)s kad hadiah"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card_app"
msgstr "Kad bank (menggunakan aplikasi)"

msgid "page.donate.payment.buttons.crypto"
msgstr "Kripto %(bitcoin_icon)s"

msgid "page.donate.payment.buttons.cashapp"
msgstr "Cash App"

#, fuzzy
msgid "page.donate.payment.buttons.revolut"
msgstr "Revolut"

msgid "page.donate.payment.buttons.credit_debit"
msgstr "Kad kredit/debit"

msgid "page.donate.payment.buttons.paypal"
msgstr "PayPal (US) %(bitcoin_icon)s"

#, fuzzy
msgid "page.donate.payment.buttons.paypalreg"
msgstr "PayPal (biasa)"

#, fuzzy
msgid "page.donate.payment.buttons.givebutter"
msgstr "Kad / PayPal / Venmo"

#, fuzzy
msgid "page.donate.payment.buttons.bmc"
msgstr "Kredit/debit/Apple/Google (BMC)"

msgid "page.donate.payment.buttons.alipay"
msgstr "Alipay"

msgid "page.donate.payment.buttons.pix"
msgstr "Pix (Brazil)"

msgid "page.donate.payment.buttons.paypal_plain"
msgstr "PayPal"

#, fuzzy
msgid "page.donate.payment.buttons.bank_card"
msgstr "Kad bank"

#, fuzzy
msgid "page.donate.payment.buttons.credit_debit_backup"
msgstr "Kad kredit/debit (sandaran)"

msgid "page.donate.payment.buttons.credit_debit2"
msgstr "Kad kredit/debit 2"

#, fuzzy
msgid "page.donate.payment.buttons.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.buttons.wechat"
msgstr "WeChat"

msgid "page.donate.payment.buttons.alipay_wechat"
msgstr "Alipay 支付宝 / WeChat 微信"

msgid "page.donate.payment.desc.crypto"
msgstr "Dengan menggunakan kripto, and boleh menderma menggunakan BTC, ETH, XMR, dan SOL. Gunakan kaedah ini jika and sudah biasa dengan mata wang kripto."

msgid "page.donate.payment.desc.crypto2"
msgstr "Dengan menggunakan kripto, anda boleh menderma menggunakan BTC, ETH, XMR dan lain-lain."

#, fuzzy
msgid "page.donate.payment.desc.crypto_suggestion_dynamic"
msgstr "Jika anda menggunakan kripto untuk kali pertama, kami mencadangkan menggunakan %(options)s untuk membeli dan menderma Bitcoin (mata wang kripto asal dan paling banyak digunakan)."

#, fuzzy
msgid "page.donate.payment.processor.binance"
msgstr "Binance"

#, fuzzy
msgid "page.donate.payment.processor.coinbase"
msgstr "Coinbase"

#, fuzzy
msgid "page.donate.payment.processor.kraken"
msgstr "Kraken"

msgid "page.donate.payment.desc.paypal"
msgstr "Untuk menderma menerusi PayPal US, kami akan menggunakan PayPal Kripto yang membolehkan kami untuk kekal tanpa nama. Kami menghargai usaha anda untuk belajar menggunakan kaedah ini kerana ia banyak membantu kami."

msgid "page.donate.payment.desc.paypal_short"
msgstr "Derma melalui Paypal."

msgid "page.donate.payment.desc.cashapp"
msgstr "Derma melalui Cash App."

msgid "page.donate.payment.desc.cashapp_easy"
msgstr "Jika anda mempunyai Cash App, ini adalah cara termudah untuk menderma!"

msgid "page.donate.payment.desc.cashapp_fee"
msgstr "Ambil maklum bahawa untuk transaksi kurang daripada %(amount)s, Cash App akan mengenakan caj %(fee)s. Untuk jumlah transaksi lebih daripada %(amount)s, transaksi adalah percuma!"

#, fuzzy
msgid "page.donate.payment.desc.revolut"
msgstr "Derma menggunakan Revolut."

#, fuzzy
msgid "page.donate.payment.desc.revolut_easy"
msgstr "Jika anda mempunyai Revolut, ini adalah cara paling mudah untuk menderma!"

msgid "page.donate.payment.desc.credit_debit"
msgstr "Derma melalui kad kredit atau kad debit."

msgid "page.donate.payment.desc.google_apple"
msgstr "Google Pay dan Apple Pay juga mugkin boleh digunakan."

msgid "page.donate.payment.desc.elimate_discount"
msgstr "Sila ambil maklum untuk jumlah sumbangan yang kecil, caj kad kredit boleh menghapuskan diskaun %(discount)s%% kami. Jadi kami mencadangkan langganan yang lebih lama."

msgid "page.donate.payment.desc.longer_subs"
msgstr "Sila ambil maklum untuk sumbangan yang kecil, caj yang dikenakan akan menjadi lebih tinggi. Jadi kami mencadangkan langganan yang lebih lama."

#, fuzzy
msgid "page.donate.payment.desc.binance_p1"
msgstr "Dengan Binance, anda membeli Bitcoin dengan kad kredit/debit atau akaun bank, dan kemudian menderma Bitcoin itu kepada kami. Dengan cara itu kami boleh kekal selamat dan tanpa nama semasa menerima sumbangan anda."

#, fuzzy
msgid "page.donate.payment.desc.binance_p2"
msgstr "Binance tersedia di hampir setiap negara, dan menyokong kebanyakan bank dan kad kredit/debit. Ini adalah cadangan utama kami buat masa ini. Kami menghargai anda meluangkan masa untuk belajar cara menderma menggunakan kaedah ini, kerana ia sangat membantu kami."

#, fuzzy
msgid "page.donate.payment.desc.paypalreg"
msgstr "Derma menggunakan akaun PayPal biasa anda."

#, fuzzy
msgid "page.donate.payment.desc.givebutter"
msgstr "Derma menggunakan kad kredit/debit, PayPal, atau Venmo. Anda boleh memilih antara ini di halaman seterusnya."

msgid "page.donate.payment.desc.amazon"
msgstr "Derma melalui kad hadiah Amazon."

msgid "page.donate.payment.desc.amazon_round"
msgstr "Sila ambil maklum bahawa kami perlu bulatkan jumlah kepada jumlah yang boleh diterima oleh penjual semula kami (minima %(minimum)s)."

msgid "page.donate.payment.desc.amazon_com"
msgstr "<strong>PENTING:</strong> Kami hanya menyokong Amazon.com dan bukan laman-laman Amazon lain. Contoh, de, .co.uk, .ca, tidak disokong oleh laman kami."

#, fuzzy
msgid "page.donate.payment.desc.amazon_cc"
msgstr "<strong>PENTING:</strong> Pilihan ini adalah untuk %(amazon)s. Jika anda ingin menggunakan laman web Amazon yang lain, pilih di atas."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_backup"
msgstr "Kaedah ini menggunakan penyedia mata wang kripto sebagai penukaran perantaraan. Ini mungkin agak mengelirukan, jadi sila gunakan kaedah ini hanya jika kaedah pembayaran lain tidak berfungsi. Ia juga tidak berfungsi di semua negara."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app"
msgstr "Derma menggunakan kad kredit/debit, melalui aplikasi Alipay (sangat mudah untuk disediakan)."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.header"
msgstr "<span %(style)s>1</span>Pasang aplikasi Alipay"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc1"
msgstr "Pasang aplikasi Alipay dari <a %(a_app_store)s>Apple App Store</a> atau <a %(a_play_store)s>Google Play Store</a>."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc2"
msgstr "Daftar menggunakan nombor telefon anda."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step1.desc3"
msgstr "Tiada butiran peribadi lanjut diperlukan."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.header"
msgstr "<span %(style)s>2</span>Tambah kad bank"

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc1"
msgstr "Disokong: Visa, MasterCard, JCB, Diners Club dan Discover."

#, fuzzy
msgid "page.donate.payment.desc.bank_card_app.step2.desc2"
msgstr "Lihat <a %(a_alipay)s>panduan ini</a> untuk maklumat lanjut."

#, fuzzy
msgid "page.donate.payment.desc.credit_debit_explained"
msgstr "Kami tidak dapat menyokong kad kredit/debit secara langsung, kerana bank tidak mahu bekerjasama dengan kami. ☹ Walau bagaimanapun, terdapat beberapa cara untuk menggunakan kad kredit/debit, menggunakan kaedah pembayaran lain:"

msgid "page.donate.payment.buttons.amazon"
msgstr "Kad Hadiah Amazon"

#, fuzzy
msgid "page.donate.ccexp.amazon_com"
msgstr "Hantar kad hadiah Amazon.com kepada kami menggunakan kad kredit/debit anda."

#, fuzzy
msgid "page.donate.ccexp.alipay"
msgstr "Alipay menyokong kad kredit/debit antarabangsa. Lihat <a %(a_alipay)s>panduan ini</a> untuk maklumat lanjut."

#, fuzzy
msgid "page.donate.ccexp.wechat"
msgstr "WeChat (Weixin Pay) menyokong kad kredit/debit antarabangsa. Dalam aplikasi WeChat, pergi ke “Me => Services => Wallet => Add a Card”. Jika anda tidak melihatnya, aktifkan menggunakan “Me => Settings => General => Tools => Weixin Pay => Enable”."

#, fuzzy
msgid "page.donate.ccexp.crypto"
msgstr "Anda boleh membeli kripto menggunakan kad kredit/debit."

#, fuzzy
msgid "page.donate.payment.desc.crypto_express_services"
msgstr "Perkhidmatan ekspres kripto"

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.1"
msgstr "Perkhidmatan ekspres adalah mudah, tetapi mengenakan yuran yang lebih tinggi."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.2"
msgstr "Anda boleh menggunakan ini sebagai ganti pertukaran kripto jika anda ingin membuat sumbangan yang lebih besar dengan cepat dan tidak kisah dengan yuran $5-10."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.3"
msgstr "Pastikan untuk menghantar jumlah kripto yang tepat seperti yang ditunjukkan pada halaman sumbangan, bukan jumlah dalam $USD."

#, fuzzy
msgid "page.donation.ccexp.crypto_express_services.4"
msgstr "Jika tidak, yuran akan ditolak dan kami tidak dapat memproses keahlian anda secara automatik."

#, fuzzy
msgid "page.donation.payment2cc.method.paybis"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.switchere"
msgstr "(minimum: %(minimum)s bergantung pada negara, tiada pengesahan untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.method.munzen"
msgstr "(minimum: %(minimum)s, tiada pengesahan untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.method.mercuryo"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.moonpay"
msgstr "(minimum: %(minimum)s)"

#, fuzzy
msgid "page.donation.payment2cc.method.coingate"
msgstr "(minimum: %(minimum)s, tiada pengesahan untuk transaksi pertama)"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.outdated"
msgstr "Jika ada maklumat ini yang tidak terkini, sila emel kami untuk memberitahu kami."

#, fuzzy
msgid "page.donate.payment.desc.bmc"
msgstr "Untuk kad kredit, kad debit, Apple Pay, dan Google Pay, kami menggunakan “Buy Me a Coffee” (BMC <span class=\"icon-[ph--coffee-fill] text-lg align-text-bottom\"></span>). Dalam sistem mereka, satu “kopi” adalah bersamaan dengan $5, jadi derma anda akan dibundarkan kepada gandaan terdekat 5."

msgid "page.donate.duration.intro"
msgstr "Pilih tempoh langganan anda."

msgid "page.donate.duration.1_mo"
msgstr "1 bulan"

msgid "page.donate.duration.3_mo"
msgstr "3 bulan"

msgid "page.donate.duration.6_mo"
msgstr "6 bulan"

msgid "page.donate.duration.12_mo"
msgstr "12 bulan"

msgid "page.donate.duration.24_mo"
msgstr "24 bulan"

#, fuzzy
msgid "page.donate.duration.48_mo"
msgstr "48 bulan"

#, fuzzy
msgid "page.donate.duration.96_mo"
msgstr "96 bulan"

msgid "page.donate.duration.summary"
msgstr "<div %(div_monthly_cost)s></div><div %(div_after)s>selepas <span %(span_discount)s></span> diskaun</div><div %(div_total)s></div><div %(div_duration)s></div>"

msgid "page.donate.payment.minimum_method"
msgstr "Kaedah pembayaran ini memerlukan jumlah minima %(amount)s. Silih pilih tempoh langganan yang berbeza atau kaedah pembayaran yang lain."

#, fuzzy
msgid "page.donate.buttons.donate"
msgstr "Derma"

msgid "page.donate.payment.maximum_method"
msgstr "Kaedah pembayaran ini hanya membenarkan jumlah maksimum sebanyak %(amount)s. Sila pilih tempoh langganan yang berbeza atau kaedah pembayaran yang lain."

msgid "page.donate.login2"
msgstr "Untuk menjadi ahli, sila <a %(a_login)s> Log masuk atau Daftar</a>. Terima kasih atas sokongan anda!"

msgid "page.donate.payment.crypto_select"
msgstr "Sila pilih mata wang kripto pilihan anda:"

#, fuzzy
msgid "page.donate.currency_lowest_minimum"
msgstr "(jumlah minimum terendah)"

#, fuzzy
msgid "page.donate.coinbase_eth"
msgstr "(gunakan apabila menghantar Ethereum dari Coinbase)"

#, fuzzy
msgid "page.donate.currency_warning_high_minimum"
msgstr "(amaran: jumlah minimum yang tinggi)"

msgid "page.donate.submit.confirm"
msgstr "Tekan butang derma untuk mengesahkan dermaan ini."

msgid "page.donate.submit.button"
msgstr "Derma <span %(span_cost)s></span> <span %(span_label)s></span>"

msgid "page.donate.submit.cancel_note"
msgstr "Anda masih boleh membatalkan dermaan ini semasa pembayaran."

msgid "page.donate.submit.success"
msgstr "✅ Mengubah hala ke halaman derma…"

msgid "page.donate.submit.failure"
msgstr "❌ Ralat. Sila muat semula halaman dan cuba sekali lagi."

msgid "page.donate.duration.summary.discount"
msgstr "%(percentage)s%%"

msgid "page.donate.duration.summary.monthly_cost"
msgstr "%(monthly_cost)s / sebulan"

msgid "page.donate.duration.summary.duration.1_mo"
msgstr "untuk 1 bulan"

msgid "page.donate.duration.summary.duration.3_mo"
msgstr "untuk 3 bulan"

msgid "page.donate.duration.summary.duration.6_mo"
msgstr "untuk 6 bulan"

msgid "page.donate.duration.summary.duration.12_mo"
msgstr "untuk 12 bulan"

msgid "page.donate.duration.summary.duration.24_mo"
msgstr "untuk 24 bulan"

#, fuzzy
msgid "page.donate.duration.summary.duration.48_mo"
msgstr "untuk 48 bulan"

#, fuzzy
msgid "page.donate.duration.summary.duration.96_mo"
msgstr "untuk 96 bulan"

msgid "page.donate.submit.button.label.1_mo"
msgstr "“%(tier_name)s” untuk 1 bulan"

msgid "page.donate.submit.button.label.3_mo"
msgstr "“%(tier_name)s” untuk 3 bulan"

msgid "page.donate.submit.button.label.6_mo"
msgstr "“%(tier_name)s” untuk 6 bulan"

msgid "page.donate.submit.button.label.12_mo"
msgstr "“%(tier_name)s” untuk 12 bulan"

msgid "page.donate.submit.button.label.24_mo"
msgstr "“%(tier_name)s” untuk 24 bulan"

#, fuzzy
msgid "page.donate.submit.button.label.48_mo"
msgstr "untuk 48 bulan “%(tier_name)s”"

#, fuzzy
msgid "page.donate.submit.button.label.96_mo"
msgstr "untuk 96 bulan “%(tier_name)s”"

msgid "page.donation.title"
msgstr "Sumbangan"

msgid "page.donation.header.date"
msgstr "Tarikh: %(date)s"

msgid "page.donation.header.total_including_discount"
msgstr "Jumlah: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / sebulan untuk %(duration)s bulan, termasuk %(discounts)s%% diskaun)</span>"

msgid "page.donation.header.total_without_discount"
msgstr "Jumlah: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / sebulan untuk %(duration)s bulan)</span>"

msgid "page.donation.header.status"
msgstr "Status: <span %(span_label)s>%(label)s</span>"

msgid "page.donation.header.id"
msgstr "Pengecam: %(id)s"

msgid "page.donation.header.cancel.button"
msgstr "Batal"

msgid "page.donation.header.cancel.confirm.msg"
msgstr "Adakah anda pasti untuk membatalkan transaksi? Jangan batalkan jika pembayaran telah dibuat."

msgid "page.donation.header.cancel.confirm.button"
msgstr "Betul, tolong batalkan"

msgid "page.donation.header.cancel.success"
msgstr "✅ Dermaan anda telah dibatalkan."

msgid "page.donation.header.cancel.new_donation"
msgstr "Buat dermaan baharu"

msgid "page.donation.header.cancel.failure"
msgstr "❌ Ralat. Sila muat semula halaman dan cuba semula."

msgid "page.donation.header.reorder"
msgstr "Pesan semula"

msgid "page.donation.old_instructions.intro_paid"
msgstr "Anda telah membuat pembayaran. Jika anda ingin semak arahan pembayaran, tekan sini:"

msgid "page.donation.old_instructions.show_button"
msgstr "Tunjuk arahan pembayaran lama"

msgid "page.donation.thank_you_donation"
msgstr "Terima kasih atas sumbangan anda!"

#, fuzzy
msgid "page.donation.thank_you.secret_key"
msgstr "Jika anda belum melakukannya, tuliskan kunci rahsia anda untuk log masuk:"

#, fuzzy
msgid "page.donation.thank_you.locked_out"
msgstr "Jika tidak, anda mungkin akan terkunci daripada akaun ini!"

msgid "page.donation.old_instructions.intro_outdated"
msgstr "Arahan pembayaran telah tamat tempoh. Jika anda ingin membuat derma baharu, sila tekan butang 'Pesan semula' di atas."

msgid "page.donate.submit.crypto_note"
msgstr "<strong>Maklumat penting:</strong> Harga kripto boleh berubah secara mendadak, kadang-kadang sehingga 20%% dalam masa beberapa minit. Ini masih kurang daripada caj yang kami tanggung dengan kaedah pembayaran yang lain yang sering mengecaj sehingga 50-60%% untuk bekerja dengan \"badan amal bayangan\" seperti kami. <u>Jika anda menghantar kepada kami resit dengan harga asal yang anda bayar, kami masih akan mengkreditkan akaun anda berdasarkan keahlian yang telah dipilih</u> (selagi resit tidak melebihi beberapa jam). Kami amat menghargai kesanggupan anda untuk bersabar dengan perkara seperti ini untuk menyokong kami! ❤️"

msgid "page.donation.expired"
msgstr "Dermaan ini telah tamat tempoh. Sila batalkan transaksi ini dan cipta yang baharu."

msgid "page.donation.payment.crypto.top_header"
msgstr "Arahan Kripto"

msgid "page.donation.payment.crypto.header1"
msgstr "<span %(span_circle)s>1</span>Buat pindahan ke salah satu akaun kripto kami"

msgid "page.donation.payment.crypto.text1"
msgstr "Derma jumlah sebanyak %(total)s ke salah satu alamat tersebut:"

msgid "page.donate.submit.header1"
msgstr "<span %(span_circle)s>1</span>Beli Bitcoin melalui Paypal"

msgid "page.donate.one_time_payment.paypal.text2"
msgstr "Cari halaman “Crypto” di aplikasi atau laman web PayPal anda. Halaman boleh dijumpai di bawah “Finances”."

msgid "page.donation.payment.paypal.text3"
msgstr "Ikut arahan yang diberi untuk membeli Bitcoin (BTC). Anda hanya perlu membeli jumlah sebanyak %(total)s berdasarkan jumlah yang anda mahu menderma."

msgid "page.donate.submit.header2"
msgstr "<span %(span_circle)s>2</span>Pindah Bitcoin ke alamat kami"

msgid "page.donate.one_time_payment.paypal.text4"
msgstr "Pergi ke halaman “Bitcoin” di aplikasi atau laman web PayPal anda. Tekan butang “Transfer” %(transfer_icon)s dan seterusnya tekan butang “Send”."

msgid "page.donation.payment.paypal.text5"
msgstr "Masukkan alamat Bitcoin (BTC) kami sebagai penerima dan ikut arahan yang diberi untuk hantar derma berjumlah %(total)s:"

msgid "page.donation.credit_debit_card_instructions"
msgstr "Arahan kad debit/kredit"

msgid "page.donation.credit_debit_card_our_page"
msgstr "Derma melalui halaman kad kredit/debit kami"

msgid "page.donation.donate_on_this_page"
msgstr "Derma sebanyak %(amount)s di <a %(a_page)s>halaman ini</a>."

msgid "page.donation.stepbystep_below"
msgstr "Lihat panduan di bawah."

msgid "page.donation.status_header"
msgstr "Status:"

msgid "page.donation.waiting_for_confirmation_refresh"
msgstr "Menunggu pengesahan (muat semula halaman untuk semak)…"

msgid "page.donation.waiting_for_transfer_refresh"
msgstr "Menunggu pindahan (muat semula halaman untuk semak) …"

msgid "page.donation.time_left_header"
msgstr "Masa yang tinggal:"

msgid "page.donation.might_want_to_cancel"
msgstr "(anda mungkin perlu membatalkan dan membuat derma baharu)"

msgid "page.donation.reset_timer"
msgstr "Untuk menetapkan semula masa, buat derma yang baharu."

msgid "page.donation.refresh_status"
msgstr "Kemas kini status"

msgid "page.donation.footer.issues_contact"
msgstr "Jika anda menghadapi apa-apa masalah, sila hubungi kami di %(email)s dan sertakan informasi sebanyak yang boleh (seperti tangkapan skrin)."

#, fuzzy
msgid "page.donation.expired_already_paid"
msgstr "Jika anda telah membayar:"

#, fuzzy
msgid "page.donation.confirmation_can_take_a_while"
msgstr "Kadangkala pengesahan boleh mengambil masa sehingga 24 jam, jadi pastikan untuk menyegarkan halaman ini (walaupun ia telah tamat tempoh)."

#, fuzzy
msgid "page.donation.step1"
msgstr "1"

msgid "page.donation.buy_pyusd"
msgstr "Beli syiling PYUSD melalui PayPal"

msgid "page.donation.pyusd.instructions"
msgstr "Ikut arahan yang diberi untuk membeli syiling PYUSD (PayPal USD)."

msgid "page.donation.pyusd.more"
msgstr "Beli sedikit lagi (kami mencadang lebih %(more)s) berbanding jumlah yang akan diderma (%(amount)s) untuk menampung yuran transaksi. Anda akan simpan lebihan daripada transaksi tersebut."

#, fuzzy
msgid "page.donation.step2"
msgstr "2"

msgid "page.donation.pyusd.transfer"
msgstr "Pergi ke halaman “PYUSD” di aplikasi atau laman web PayPal anda. Tekan butang “Transfer” %(icon)s dan seterusnya “Send”."

msgid "page.donation.transfer_amount_to"
msgstr "Pindah %(amount)s ke %(account)s"

#, fuzzy
msgid "page.donation.cash_app_btc.step1"
msgstr "Beli Bitcoin (BTC) di Cash App"

#, fuzzy
msgid "page.donation.cash_app_btc.step1.text1"
msgstr "Pergi ke halaman “Bitcoin” (BTC) dalam Cash App."

#, fuzzy
msgid "page.donation.cash_app_btc.step1.more"
msgstr "Beli sedikit lebih (kami mengesyorkan %(more)s lebih) daripada jumlah yang anda derma (%(amount)s), untuk menampung yuran transaksi. Anda akan menyimpan baki yang tinggal."

#, fuzzy
msgid "page.donation.cash_app_btc.step2"
msgstr "Pindahkan Bitcoin ke alamat kami"

#, fuzzy
msgid "page.donation.cash_app_btc.step2.transfer"
msgstr "Klik butang “Hantar bitcoin” untuk membuat “pengeluaran”. Tukar dari dolar ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah dan klik “Hantar”. Lihat <a %(help_video)s>video ini</a> jika anda tersekat."

#, fuzzy
msgid "page.donation.cash_app_btc.step2.rush_priority"
msgstr "Untuk derma kecil (di bawah $25), anda mungkin perlu menggunakan Rush atau Priority."

#, fuzzy
msgid "page.donation.revolut.step1"
msgstr "Beli Bitcoin (BTC) di Revolut"

#, fuzzy
msgid "page.donation.revolut.step1.text1"
msgstr "Pergi ke halaman “Crypto” dalam Revolut untuk membeli Bitcoin (BTC)."

#, fuzzy
msgid "page.donation.revolut.step1.more"
msgstr "Beli sedikit lebih (kami mengesyorkan %(more)s lebih) daripada jumlah yang anda derma (%(amount)s), untuk menampung yuran transaksi. Anda akan menyimpan baki yang tinggal."

#, fuzzy
msgid "page.donation.revolut.step2"
msgstr "Pindahkan Bitcoin ke alamat kami"

#, fuzzy
msgid "page.donation.revolut.step2.transfer"
msgstr "Klik butang “Hantar bitcoin” untuk membuat “pengeluaran”. Tukar dari euro ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah dan klik “Hantar”. Lihat <a %(help_video)s>video ini</a> jika anda tersekat."

#, fuzzy
msgid "page.donation.revolut.btc_amount_below"
msgstr "Pastikan untuk menggunakan jumlah BTC di bawah, <em>BUKAN</em> euro atau dolar, jika tidak, kami tidak akan menerima jumlah yang betul dan tidak dapat mengesahkan keahlian anda secara automatik."

#, fuzzy
msgid "page.donation.revolut.step2.rush_priority"
msgstr "Untuk derma kecil (di bawah $25) anda mungkin perlu menggunakan Rush atau Priority."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc"
msgstr "Gunakan mana-mana perkhidmatan “kad kredit ke Bitcoin” ekspres berikut, yang hanya mengambil masa beberapa minit:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.form"
msgstr "Isi butiran berikut dalam borang:"

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_amount"
msgstr "Jumlah BTC / Bitcoin:"

#, fuzzy
msgid "page.donation.payment2cc.exact_amount"
msgstr "Sila gunakan <span %(underline)s>jumlah tepat</span> ini. Kos keseluruhan anda mungkin lebih tinggi kerana yuran kad kredit. Untuk jumlah kecil, ini mungkin lebih daripada diskaun kami, malangnya."

#, fuzzy
msgid "page.donation.payment2cc.cc2btc.btc_address"
msgstr "Alamat BTC / Bitcoin (dompet luaran):"

msgid "page.donation.crypto_instructions"
msgstr "Arahan %(coin_name)s"

#, fuzzy
msgid "page.donation.crypto_standard"
msgstr "Kami hanya menyokong versi standard syiling kripto, tiada rangkaian atau versi syiling eksotik. Ia boleh mengambil masa sehingga satu jam untuk mengesahkan transaksi, bergantung pada syiling."

msgid "page.donation.crypto_qr_code_title"
msgstr "Imbas kod QR untuk membayar"

msgid "page.donation.crypto_qr_code_instructions"
msgstr "Imbas kod QR ini dengan aplikasi dompet crypto anda dengan cepat mengisi butiran pembayaran"

msgid "page.donation.amazon.header"
msgstr "Kad hadiah Amazon"

msgid "page.donation.amazon.form_instructions"
msgstr "Sila gunakan <a %(a_form)s> borang Amazon.com rasmi</a> untuk menghantar kad hadiah berjumlah %(amount)s kepada alamat e-mel kami di bawah."

msgid "page.donation.amazon.only_official"
msgstr "Kami tidak boleh menerima kaedah kad hadiah yang lain, <strong>hanya hantar terus kad hadiah dari borang rasmi Amazon.com</strong>. Kami tidak boleh memulangkan semula kad hadiah anda jika anda tidak menggunakan borang rasmi."

#, fuzzy
msgid "page.donate.payment.desc.amazon_message_1"
msgstr "Masukkan jumlah yang tepat: %(amount)s"

msgid "page.donate.payment.desc.amazon_message"
msgstr "Tolong JANGAN tulis mesej anda sendiri."

msgid "page.donation.amazon.form_to"
msgstr "“Kepada” emel penerima di dalam borang:"

msgid "page.donation.amazon.unique"
msgstr "Unik kepada akaun anda, jangan kongsi."

#, fuzzy
msgid "page.donation.amazon.only_use_once"
msgstr "Hanya guna sekali."

msgid "page.donation.amazon.waiting_gift_card"
msgstr "Menunggu kad hadiah... (muat semula halaman untuk semak)"

msgid "page.donation.amazon.confirm_automated"
msgstr "Selepas menghantar kad hadiah anda, sistem automatik kami akan mengesahkan dalam beberapa minit. Jika tidak berjaya, cuba menghantar kad hadiah anda semula (<a %(a_instr)s>arahan</a>)."

msgid "page.donation.amazon.doesnt_work"
msgstr "Jika masih tidak berjaya, sila emel kami dan Anna akan menyemaknya secara manual (ini akan mengambil masa beberapa hari) dan pastikan anda maklumkan jika anda telah cuba menghantar semula."

msgid "page.donation.amazon.example"
msgstr "Contoh:"

msgid "page.donate.strange_account"
msgstr "Ambil maklum bahawa nama akaun atau gambar mungkin nampak pelik. Jangan risau! Akaun-akaun ini diuruskan oleh rakan derma kami. Akaun kami tidak digodam."

msgid "page.donation.payment.alipay.top_header"
msgstr "Arahan Alipay"

msgid "page.donation.payment.alipay.header1"
msgstr "<span %(span_circle)s>1</span>Derma melalui Alipay"

#, fuzzy
msgid "page.donation.payment.alipay.text1_new"
msgstr "Derma jumlah keseluruhan %(total)s menggunakan <a %(a_account)s>akaun Alipay ini</a>"

#, fuzzy
msgid "page.donation.page_blocked"
msgstr "Jika halaman derma disekat, cuba sambungan internet yang berbeza (contohnya VPN atau internet telefon)."

#, fuzzy
msgid "page.donation.payment.alipay.error"
msgstr "Malangnya, halaman Alipay sering hanya boleh diakses dari <strong>tanah besar China</strong>. Anda mungkin perlu mematikan VPN anda buat sementara waktu, atau menggunakan VPN ke tanah besar China (atau Hong Kong juga kadang-kadang berfungsi)."

#, fuzzy
msgid "page.donation.bank_card_app.step3.header"
msgstr "<span %(style)s>3</span>Buat sumbangan (imbas kod QR atau tekan butang)"

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.1"
msgstr "Buka <a %(a_href)s>halaman sumbangan kod QR</a>."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.2"
msgstr "Imbas kod QR dengan aplikasi Alipay, atau tekan butang untuk membuka aplikasi Alipay."

#, fuzzy
msgid "page.donation.bank_card_app.step3.desc.3"
msgstr "Sila bersabar; halaman mungkin mengambil masa untuk dimuatkan kerana ia berada di China."

#, fuzzy
msgid "page.donation.payment.wechat.top_header"
msgstr "Arahan WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.header1"
msgstr "<span %(span_circle)s>1</span>Derma di WeChat"

#, fuzzy
msgid "page.donation.payment.wechat.text1"
msgstr "Derma jumlah keseluruhan %(total)s menggunakan <a %(a_account)s>akaun WeChat ini</a>"

msgid "page.donation.payment.pix.top_header"
msgstr "Arahan Pix"

msgid "page.donation.payment.pix.header1"
msgstr "<span %(span_circle)s>1</span>Derma melalui Pix"

msgid "page.donation.payment.pix.text1"
msgstr "Derma %(total)s menggunakan %(a_account)s akaun Pix ini"

msgid "page.donation.footer.header"
msgstr "<span %(span_circle)s>%(circle_number)s</span>Emelkan resit kepada kami"

#, fuzzy
msgid "page.donation.footer.verification"
msgstr "Hantar resit atau tangkapan skrin ke alamat pengesahan peribadi anda. Jangan gunakan alamat emel ini untuk sumbangan PayPal anda."

msgid "page.donation.footer.text1"
msgstr "Hantar resit atau tangkapan skrin ke alamat peribadi verifikasi anda:"

msgid "page.donation.footer.crypto_note"
msgstr "Jika kadar pertukaran kripto berubah-ubah semasa transaksi, pastikan anda menyertakan sekali resit yang menunjukkan kadar pertukaran asal. Kami sangat menghargai anda bersusah payah menggunakan crypto, ia banyak membantu kami!"

msgid "page.donation.footer.text2"
msgstr "Apabila resit anda sudah diemel, tekan butang ini supaya Anna boleh menyemaknya secara manual (ini mungkin akan mengambil masa beberapa hari):"

msgid "page.donation.footer.button"
msgstr "Ya, resit saya sudah diemel"

msgid "page.donation.footer.success"
msgstr "✅ Terima kasih atas dermaan anda! Anna akan mengaktifkan keahlian anda secara manual dalam masa beberapa hari."

msgid "page.donation.footer.failure"
msgstr "❌ Ada sesuatu yang tidak kena. Sila muat semula halaman dan cuba sekali lagi."

msgid "page.donation.stepbystep"
msgstr "Panduan langkah demi langkah"

msgid "page.donation.crypto_dont_worry"
msgstr "Beberapa langkah ada menyebut dompet kripto, tetapi jangan risau, anda tidak perlu belajar apa-apa tentang kripto untuk ini."

msgid "page.donation.hoodpay.step1"
msgstr "1. Masukkan emel anda."

msgid "page.donation.hoodpay.step2"
msgstr "2. Pilih kaedah pembayaran anda."

msgid "page.donation.hoodpay.step3"
msgstr "3. Pilih kaedah pembayaran anda sekali lagi."

msgid "page.donation.hoodpay.step4"
msgstr "4. Pilih dompet \"Self-hosted\"."

msgid "page.donation.hoodpay.step5"
msgstr "5. Tekan “I confirm ownership”."

msgid "page.donation.hoodpay.step6"
msgstr "6. Anda akan menerima resit emel. Sila hantar resit tersebut kepada kami dan kami akan mengesahkan dermaan anda secepat mungkin."

#, fuzzy
msgid "page.donate.wait_new"
msgstr "Sila tunggu sekurang-kurangnya <span %(span_hours)s>24 jam</span> (dan muat semula halaman ini) sebelum menghubungi kami."

#, fuzzy
msgid "page.donate.mistake"
msgstr "Jika anda membuat kesilapan semasa pembayaran, kami tidak boleh membuat bayaran balik, tetapi kami akan cuba menyelesaikannya."

msgid "page.my_donations.title"
msgstr "Dermaan saya"

msgid "page.my_donations.not_shown"
msgstr "Butiran derma tidak akan ditunjuk secara terbuka."

msgid "page.my_donations.no_donations"
msgstr "Tiada dermaan lagi. <a %(a_donate)s>Buat derma pertama saya.</a>"

msgid "page.my_donations.make_another"
msgstr "Buat dermaan lagi."

msgid "page.downloaded.title"
msgstr "Fail-fail yang dimuat turun"

#, fuzzy
msgid "page.downloaded.fast_partner_star"
msgstr "Muat turun dari Pelayan Rakan Kongsi Pantas ditandakan oleh %(icon)s."

#, fuzzy
msgid "page.downloaded.twice"
msgstr "Jika anda memuat turun fail dengan muat turun pantas dan perlahan, ia akan muncul dua kali."

#, fuzzy
msgid "page.downloaded.fast_download_time"
msgstr "Muat turun pantas dalam 24 jam terakhir dikira dalam had harian."

#, fuzzy
msgid "page.downloaded.times_utc"
msgstr "Semua masa adalah dalam UTC."

msgid "page.downloaded.not_public"
msgstr "Fail-fail yang telah dimuat turun tidak akan ditunjuk secara terbuka."

msgid "page.downloaded.no_files"
msgstr "Belum ada fail yang telah dimuat turun."

#, fuzzy
msgid "page.downloaded.last_18_hours"
msgstr "18 jam terakhir"

#, fuzzy
msgid "page.downloaded.earlier"
msgstr "Terdahulu"

msgid "page.account.logged_in.title"
msgstr "Akaun"

msgid "page.account.logged_out.title"
msgstr "Log masuk / Daftar"

#, fuzzy
msgid "page.account.logged_in.account_id"
msgstr "ID Akaun: %(account_id)s"

msgid "page.account.logged_in.public_profile"
msgstr "Profil awam: %(profile_link)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_dont_share"
msgstr "Kunci rahsia (jangan kongsi!): %(secret_key)s"

#, fuzzy
msgid "page.account.logged_in.secret_key_show"
msgstr "tunjuk"

msgid "page.account.logged_in.membership_has_some"
msgstr "Keahlian: <strong>%(tier_name)s</strong> sehingga %(until_date)s <a %(a_extend)s>(Panjangkan)</a>"

msgid "page.account.logged_in.membership_none"
msgstr "Keahlian: <strong>Tiada</strong> <a %(a_become)s>(jadi ahli)</a>"

msgid "page.account.logged_in.membership_fast_downloads_used"
msgstr "Muat turun pantas yang telah diggunakan (24 jam terakhir): <strong>%(used)s / %(total)s</strong>"

#, fuzzy
msgid "page.account.logged_in.which_downloads"
msgstr "muat turun yang mana?"

#, fuzzy
msgid "page.account.logged_in.telegram_group_wrapper"
msgstr "Kumpulan Telegram eksklusif: %(link)s"

#, fuzzy
msgid "page.account.logged_in.telegram_group_join"
msgstr "Sertai kami di sini!"

#, fuzzy
msgid "page.account.logged_in.telegram_group_upgrade"
msgstr "Naik taraf ke <a %(a_tier)s>tahap lebih tinggi</a> untuk sertai kumpulan kami."

msgid "page.account.logged_in.membership_upgrade"
msgstr "Hubungi Anna di %(email)s jika anda berminat untuk menaik taraf keahlian anda ke peringkat yang lebih tinggi."

#, fuzzy
msgid "page.contact.title"
msgstr "E-mel untuk dihubungi"

#, fuzzy
msgid "page.account.logged_in.membership_multiple"
msgstr "Anda boleh menggabungkan pelbagai keahlian (muat turun pantas setiap 24 jam akan ditambah bersama)."

#, fuzzy
msgid "layout.index.header.nav.public_profile"
msgstr "Profil awam"

#, fuzzy
msgid "layout.index.header.nav.downloaded_files"
msgstr "Fail yang dimuat turun"

#, fuzzy
msgid "layout.index.header.nav.my_donations"
msgstr "Sumbangan saya"

msgid "page.account.logged_in.logout.button"
msgstr "Log keluar"

msgid "page.account.logged_in.logout.success"
msgstr "✅ Anda telah dilog keluar. Muat semula halaman untuk log masuk semula."

msgid "page.account.logged_in.logout.failure"
msgstr "❌ Ada sesuatu yang tidak kena. Sila muat semula halaman dan cuba semula."

msgid "page.account.logged_out.registered.text1"
msgstr "Pendaftaran berjaya! Kunci rahsia anda ialah: <span %(span_key)s>%(key)s</span>"

msgid "page.account.logged_out.registered.text2"
msgstr "Simpan kunci ini dengan berhati-hati. Jika kunci anda hilang, anda akan kehilangan akses kepada akaun anda."

msgid "page.account.logged_out.registered.text3"
msgstr "<li %(li_item)s><strong>Penanda.</strong> Anda boleh menanda halaman ini untuk dapatkan semula kunci anda.</li><li %(li_item)s><strong>Muat turun.</strong> Tekan<a %(a_download)s>pautan ini</a> untuk memuat turun kunci anda.</li><li %(li_item)s><strong>Pengurus kata laluan.</strong> Gunakan pengurus kata laluan untuk menyimpan kunci apabila anda memasukkanya di bawah.</li>"

msgid "page.account.logged_out.key_form.text"
msgstr "Masukkan kunci rahsia anda untuk log masuk:"

msgid "page.account.logged_out.key_form.placeholder"
msgstr "Kunci rahsia"

msgid "page.account.logged_out.key_form.button"
msgstr "Log masuk"

msgid "page.account.logged_out.key_form.invalid_key"
msgstr "Kunci rahsia tidak sah. Sahkan kunci anda dan cuba semula atau daftar akaun baru di bawah."

#, fuzzy
msgid "page.account.logged_out.key_form.dont_lose_key"
msgstr "Jangan hilangkan kunci anda!"

msgid "page.account.logged_out.register.header"
msgstr "Belum mempunyai akaun lagi?"

msgid "page.account.logged_out.register.button"
msgstr "Daftar akaun baharu"

#, fuzzy
msgid "page.login.lost_key"
msgstr "Jika anda kehilangan kunci anda, sila <a %(a_contact)s>hubungi kami</a> dan berikan sebanyak mungkin maklumat."

#, fuzzy
msgid "page.login.lost_key_contact"
msgstr "Anda mungkin perlu membuat akaun baru buat sementara waktu untuk menghubungi kami."

msgid "page.account.logged_out.old_email.button"
msgstr "Akaun berasakan emel lama? Masukkan <a %(a_open)s>emel anda disini</a>."

msgid "page.list.title"
msgstr "Senarai"

msgid "page.list.header.edit.link"
msgstr "sunting"

msgid "page.list.edit.button"
msgstr "Simpan"

msgid "page.list.edit.success"
msgstr "✅ Disimpan. Sila muat semula halaman."

msgid "page.list.edit.failure"
msgstr "❌ Ada sesuatu yang tidak kena. Sila cuba semula."

msgid "page.list.by_and_date"
msgstr "Senarai mengikut %(by)s, dicipta <span %(span_time)s>%(time)s</span>"

msgid "page.list.empty"
msgstr "Senarai kosong."

msgid "page.list.new_item"
msgstr "Tambah atau alih keluar daripada senarai ini dengan mencari fail dan membuka tab \"Senarai\"."

msgid "page.profile.title"
msgstr "Profil"

msgid "page.profile.not_found"
msgstr "Profil tidak ditemui."

msgid "page.profile.header.edit"
msgstr "sunting"

msgid "page.profile.change_display_name.text"
msgstr "Tukar nama paparan anda. Pengecam anda (bahagian selepas “#”) tidak boleh ditukar."

msgid "page.profile.change_display_name.button"
msgstr "Simpan"

msgid "page.profile.change_display_name.success"
msgstr "✅ Disimpan. Sila muat semula halaman."

msgid "page.profile.change_display_name.failure"
msgstr "❌ Ada sesuatu yang tidak kena. Sila cuba semula."

msgid "page.profile.created_time"
msgstr "Profil telah dicipta <span %(span_time)s>%(time)s</span>"

msgid "page.profile.lists.header"
msgstr "Senarai-senarai"

msgid "page.profile.lists.no_lists"
msgstr "Tiada senarai lagi"

msgid "page.profile.lists.new_list"
msgstr "Cipta senarai baharu dengan mencaru fail dan membuka tab “Senarai”."

#, fuzzy
msgid "blog.ai-copyright.title"
msgstr "Pembaharuan hak cipta diperlukan untuk keselamatan negara"

#, fuzzy
msgid "blog.ai-copyright.tldr"
msgstr "TL;DR: LLM Cina (termasuk DeepSeek) dilatih pada arkib buku dan kertas haram saya — yang terbesar di dunia. Barat perlu merombak undang-undang hak cipta sebagai perkara keselamatan negara."

#, fuzzy
msgid "blog.ai-copyright.subtitle"
msgstr "artikel pengiring oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a>"

#, fuzzy
msgid "blog.ai-copyright.text1"
msgstr "Tidak lama dahulu, “perpustakaan bayangan” hampir pupus. Sci-Hub, arkib haram besar kertas akademik, telah berhenti mengambil karya baru, disebabkan oleh tuntutan mahkamah. “Z-Library”, perpustakaan haram terbesar buku, melihat penciptanya yang didakwa ditangkap atas tuduhan hak cipta jenayah. Mereka berjaya melarikan diri dari penangkapan mereka, tetapi perpustakaan mereka tidak kurang terancam."

#, fuzzy
msgid "blog.ai-copyright.text2"
msgstr "Apabila Z-Library menghadapi penutupan, saya sudah menyandarkan keseluruhan perpustakaannya dan sedang mencari platform untuk menempatkannya. Itulah motivasi saya untuk memulakan Arkib Anna: satu kesinambungan misi di sebalik inisiatif terdahulu. Sejak itu, kami telah berkembang menjadi perpustakaan bayangan terbesar di dunia, menempatkan lebih daripada 140 juta teks berhak cipta dalam pelbagai format — buku, kertas akademik, majalah, surat khabar, dan banyak lagi."

#, fuzzy
msgid "blog.ai-copyright.text3"
msgstr "Pasukan saya dan saya adalah ideolog. Kami percaya bahawa memelihara dan menghoskan fail-fail ini adalah betul dari segi moral. Perpustakaan di seluruh dunia mengalami pemotongan dana, dan kami juga tidak boleh mempercayakan warisan manusia kepada korporat."

#, fuzzy
msgid "blog.ai-copyright.text4"
msgstr "Kemudian datanglah AI. Hampir semua syarikat besar yang membina LLM menghubungi kami untuk melatih data kami. Kebanyakan (tetapi tidak semua!) syarikat yang berpangkalan di AS mempertimbangkan semula apabila mereka menyedari sifat haram kerja kami. Sebaliknya, firma China dengan bersemangat menerima koleksi kami, nampaknya tidak terganggu oleh kesahihannya. Ini adalah penting memandangkan peranan China sebagai penandatangan kepada hampir semua perjanjian hak cipta antarabangsa utama."

#, fuzzy
msgid "blog.ai-copyright.text5"
msgstr "Kami telah memberikan akses berkelajuan tinggi kepada kira-kira 30 syarikat. Kebanyakan mereka adalah syarikat LLM, dan beberapa adalah broker data, yang akan menjual semula koleksi kami. Kebanyakan adalah dari China, walaupun kami juga telah bekerjasama dengan syarikat dari AS, Eropah, Rusia, Korea Selatan, dan Jepun. DeepSeek <a %(arxiv)s>mengakui</a> bahawa versi terdahulu dilatih pada sebahagian daripada koleksi kami, walaupun mereka berahsia tentang model terbaru mereka (mungkin juga dilatih pada data kami)."

#, fuzzy
msgid "blog.ai-copyright.text6"
msgstr "Jika Barat ingin kekal di hadapan dalam perlumbaan LLM, dan akhirnya, AGI, ia perlu mempertimbangkan semula kedudukannya mengenai hak cipta, dan segera. Sama ada anda bersetuju dengan kami atau tidak mengenai kes moral kami, ini kini menjadi kes ekonomi, dan juga keselamatan negara. Semua blok kuasa sedang membina super-saintis buatan, super-penggodam, dan super-tentera. Kebebasan maklumat menjadi perkara survival bagi negara-negara ini — malah menjadi perkara keselamatan negara."

#, fuzzy
msgid "blog.ai-copyright.text7"
msgstr "Pasukan kami berasal dari seluruh dunia, dan kami tidak mempunyai penjajaran tertentu. Tetapi kami menggalakkan negara-negara dengan undang-undang hak cipta yang kuat untuk menggunakan ancaman eksistensial ini untuk mereformasinya. Jadi apa yang perlu dilakukan?"

#, fuzzy
msgid "blog.ai-copyright.text8"
msgstr "Cadangan pertama kami adalah mudah: memendekkan tempoh hak cipta. Di AS, hak cipta diberikan selama 70 tahun selepas kematian pengarang. Ini tidak masuk akal. Kita boleh menyelaraskannya dengan paten, yang diberikan selama 20 tahun selepas pemfailan. Ini sepatutnya lebih daripada cukup masa untuk pengarang buku, kertas, muzik, seni, dan karya kreatif lain, untuk mendapat pampasan sepenuhnya untuk usaha mereka (termasuk projek jangka panjang seperti adaptasi filem)."

#, fuzzy
msgid "blog.ai-copyright.text9"
msgstr "Kemudian, sekurang-kurangnya, pembuat dasar harus memasukkan pengecualian untuk pemeliharaan dan penyebaran teks secara besar-besaran. Jika kehilangan pendapatan daripada pelanggan individu adalah kebimbangan utama, pengedaran pada tahap peribadi boleh kekal dilarang. Sebaliknya, mereka yang mampu mengurus repositori besar — syarikat yang melatih LLM, bersama dengan perpustakaan dan arkib lain — akan dilindungi oleh pengecualian ini."

#, fuzzy
msgid "blog.ai-copyright.text10"
msgstr "Beberapa negara sudah melakukan versi ini. TorrentFreak <a %(torrentfreak)s>melaporkan</a> bahawa China dan Jepun telah memperkenalkan pengecualian AI kepada undang-undang hak cipta mereka. Tidak jelas kepada kami bagaimana ini berinteraksi dengan perjanjian antarabangsa, tetapi ia pasti memberikan perlindungan kepada syarikat domestik mereka, yang menjelaskan apa yang telah kami lihat."

#, fuzzy
msgid "blog.ai-copyright.text11"
msgstr "Bagi Arkib Anna — kami akan meneruskan kerja bawah tanah kami yang berakar pada keyakinan moral. Namun harapan terbesar kami adalah untuk memasuki cahaya, dan memperkuat impak kami secara sah. Sila reformasi hak cipta."

#, fuzzy
msgid "blog.ai-copyright.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.ai-copyright.postscript"
msgstr "Baca artikel pengiring oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a>"

#, fuzzy
msgid "blog.all-isbns-winners.title"
msgstr "Pemenang hadiah visualisasi ISBN $10,000"

#, fuzzy
msgid "blog.all-isbns-winners.tldr"
msgstr "TL;DR: Kami menerima beberapa penyertaan yang luar biasa untuk hadiah visualisasi ISBN $10,000."

#, fuzzy
msgid "blog.all-isbns-winners.text1"
msgstr "Beberapa bulan yang lalu kami mengumumkan <a %(all_isbns)s>ganjaran $10,000</a> untuk membuat visualisasi terbaik data kami yang menunjukkan ruang ISBN. Kami menekankan untuk menunjukkan fail mana yang telah/ belum kami arkibkan, dan kemudian kami menambah set data yang menerangkan berapa banyak perpustakaan yang menyimpan ISBN (ukuran kelangkaan)."

#, fuzzy
msgid "blog.all-isbns-winners.text2"
msgstr "Kami sangat terharu dengan sambutan yang diterima. Terdapat begitu banyak kreativiti. Terima kasih yang besar kepada semua yang telah mengambil bahagian: tenaga dan semangat anda sangat menular!"

#, fuzzy
msgid "blog.all-isbns-winners.text3"
msgstr "Akhirnya, kami ingin menjawab soalan-soalan berikut: <strong>buku mana yang wujud di dunia, berapa banyak yang telah kami arkibkan, dan buku mana yang harus kami fokuskan seterusnya?</strong> Sangat bagus melihat begitu ramai orang mengambil berat tentang soalan-soalan ini."

#, fuzzy
msgid "blog.all-isbns-winners.text4"
msgstr "Kami memulakan dengan visualisasi asas kami sendiri. Dalam kurang daripada 300kb, gambar ini secara ringkas mewakili \"senarai buku\" terbesar yang sepenuhnya terbuka yang pernah dikumpulkan dalam sejarah manusia:"

#, fuzzy
msgid "blog.all-isbns-winners.opt.all"
msgstr "Semua ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.md5"
msgstr "Fail dalam Arkib Anna"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cadal_ssno"
msgstr "SSNO CADAL"

#, fuzzy
msgid "blog.all-isbns-winners.opt.cerlalc"
msgstr "Kebocoran data CERLALC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.duxiu_ssid"
msgstr "SSID DuXiu"

#, fuzzy
msgid "blog.all-isbns-winners.opt.edsebk"
msgstr "Indeks eBook EBSCOhost"

#, fuzzy
msgid "blog.all-isbns-winners.opt.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "blog.all-isbns-winners.opt.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ia"
msgstr "Internet Archive"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbndb"
msgstr "ISBNdb"

#, fuzzy
msgid "blog.all-isbns-winners.opt.isbngrp"
msgstr "Daftar Penerbit Global ISBN"

#, fuzzy
msgid "blog.all-isbns-winners.opt.libby"
msgstr "Libby"

#, fuzzy
msgid "blog.all-isbns-winners.opt.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "blog.all-isbns-winners.opt.oclc"
msgstr "OCLC/Worldcat"

#, fuzzy
msgid "blog.all-isbns-winners.opt.ol"
msgstr "OpenLibrary"

#, fuzzy
msgid "blog.all-isbns-winners.opt.rgb"
msgstr "Perpustakaan Negara Rusia"

#, fuzzy
msgid "blog.all-isbns-winners.opt.trantor"
msgstr "Perpustakaan Imperial Trantor"

#, fuzzy
msgid "common.back"
msgstr "Kembali"

#, fuzzy
msgid "common.forward"
msgstr "Ke Hadapan"

#, fuzzy
msgid "common.last"
msgstr "Terakhir"

#, fuzzy
msgid "blog.all-isbns-winner.text5"
msgstr "Sila lihat <a %(all_isbns)s>catatan blog asal</a> untuk maklumat lanjut."

#, fuzzy
msgid "blog.all-isbns-winner.text6"
msgstr "Kami mengeluarkan cabaran untuk memperbaiki ini. Kami akan memberikan ganjaran tempat pertama sebanyak $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Disebabkan sambutan yang luar biasa dan penyertaan yang hebat, kami telah memutuskan untuk meningkatkan sedikit jumlah hadiah, dan memberikan empat ganjaran tempat ketiga sebanyak $500 setiap satu. Pemenangnya adalah di bawah, tetapi pastikan untuk melihat semua penyertaan <a %(annas_archive)s>di sini</a>, atau muat turun <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>."

#, fuzzy
msgid "blog.all-isbns-winners.first"
msgstr "Tempat pertama $6,000: phiresky"

#, fuzzy
msgid "blog.all-isbns-winners.first.text1"
msgstr "<a %(phiresky_github)s>Penyertaan</a> ini (<a %(annas_archive_note_2951)s>komen Gitlab</a>) adalah segala yang kami inginkan, dan lebih lagi! Kami terutamanya menyukai pilihan visualisasi yang sangat fleksibel (malah menyokong shader tersuai), tetapi dengan senarai pratetap yang komprehensif. Kami juga menyukai betapa pantas dan lancarnya semuanya, pelaksanaan yang mudah (yang bahkan tidak mempunyai backend), minimap yang bijak, dan penjelasan yang luas dalam <a %(phiresky_github)s>catatan blog</a> mereka. Kerja yang luar biasa, dan pemenang yang sangat layak!"

#, fuzzy
msgid "blog.all-isbns-winners.second"
msgstr "Tempat kedua $3,000: hypha"

#, fuzzy
msgid "blog.all-isbns-winners.second.text1"
msgstr "Satu lagi <a %(annas_archive_note_2913)s>penyertaan</a> yang luar biasa. Tidak sefleksibel tempat pertama, tetapi kami sebenarnya lebih menyukai visualisasi peringkat makro berbanding tempat pertama (lengkung pengisian ruang, sempadan, pelabelan, penyorotan, panning, dan zooming). Satu <a %(annas_archive_note_2971)s>komen</a> oleh Joe Davis yang menarik perhatian kami:"

#, fuzzy
msgid "blog.all-isbns-winners.second.quote"
msgstr "“Walaupun segi empat sama dan segi empat tepat adalah menyenangkan secara matematik, mereka tidak memberikan keunggulan dalam konteks pemetaan. Saya percaya asimetri yang wujud dalam Hilbert atau Morton klasik ini bukanlah kelemahan tetapi satu ciri. Seperti garis besar berbentuk but yang terkenal di Itali menjadikannya mudah dikenali pada peta, \"keanehan\" unik lengkung ini mungkin berfungsi sebagai penanda kognitif. Keunikan ini dapat meningkatkan ingatan spatial dan membantu pengguna mengorientasikan diri mereka, berpotensi memudahkan pencarian kawasan tertentu atau melihat corak.”"

#, fuzzy
msgid "blog.all-isbns-winners.second.text2"
msgstr "Dan masih banyak pilihan untuk visualisasi dan rendering, serta UI yang sangat lancar dan intuitif. Tempat kedua yang kukuh!"

#, fuzzy
msgid "blog.all-isbns-winners.third1"
msgstr "Tempat ketiga $500 #1: maxlion"

#, fuzzy
msgid "blog.all-isbns-winners.third1.text1"
msgstr "Dalam <a %(annas_archive_note_2940)s>penyertaan</a> ini, kami sangat menyukai pelbagai jenis pandangan, terutamanya pandangan perbandingan dan penerbit."

#, fuzzy
msgid "blog.all-isbns-winners.third2"
msgstr "Tempat ketiga $500 #2: abetusk"

#, fuzzy
msgid "blog.all-isbns-winners.third2.text1"
msgstr "Walaupun bukan UI yang paling kemas, <a %(annas_archive_note_2917)s>penyertaan</a> ini memenuhi banyak kriteria. Kami terutamanya menyukai ciri perbandingannya."

#, fuzzy
msgid "blog.all-isbns-winners.third3"
msgstr "Tempat ketiga $500 #3: conundrumer0"

#, fuzzy
msgid "blog.all-isbns-winners.third3.text1"
msgstr "Seperti tempat pertama, <a %(annas_archive_note_2975)s>penyertaan</a> ini memukau kami dengan fleksibilitinya. Akhirnya, inilah yang menjadikan alat visualisasi hebat: fleksibiliti maksimum untuk pengguna berkuasa, sambil mengekalkan kesederhanaan untuk pengguna biasa."

#, fuzzy
msgid "blog.all-isbns-winners.third4"
msgstr "Tempat ketiga $500 #4: charelf"

#, fuzzy
msgid "blog.all-isbns-winners.third4.text1"
msgstr "Penyertaan <a %(annas_archive_note_2947)s>akhir</a> yang mendapat ganjaran adalah agak asas, tetapi mempunyai beberapa ciri unik yang kami sangat suka. Kami suka bagaimana mereka menunjukkan berapa banyak datasets yang merangkumi ISBN tertentu sebagai ukuran populariti/kebolehpercayaan. Kami juga sangat menyukai kesederhanaan tetapi keberkesanan menggunakan peluncur kelegapan untuk perbandingan."

#, fuzzy
msgid "blog.all-isbns-winners.notable"
msgstr "Idea yang menonjol"

#, fuzzy
msgid "blog.all-isbns-winners.notable.text1"
msgstr "Beberapa idea dan pelaksanaan lain yang kami sangat suka:"

#, fuzzy
msgid "blog.all-isbns-winners.notable.BWV_1011"
msgstr "Bangunan pencakar langit untuk kelangkaan"

#, fuzzy
msgid "blog.all-isbns-winners.notable.robingchan"
msgstr "Statistik langsung"

#, fuzzy
msgid "blog.all-isbns-winners.notable.reguster"
msgstr "Anotasi, dan juga statistik langsung"

#, fuzzy
msgid "blog.all-isbns-winners.notable.orangereporter"
msgstr "Pandangan peta unik dan penapis"

#, fuzzy
msgid "blog.all-isbns-winners.notable.joe.davis"
msgstr "Skema warna lalai yang menarik dan peta haba."

#, fuzzy
msgid "blog.all-isbns-winners.notable.timharding"
msgstr "Penukaran datasets yang mudah untuk perbandingan cepat."

#, fuzzy
msgid "blog.all-isbns-winners.notable.j1618"
msgstr "Label yang cantik."

#, fuzzy
msgid "blog.all-isbns-winners.notable.immartian"
msgstr "Bar skala dengan bilangan buku."

#, fuzzy
msgid "blog.all-isbns-winners.notable.backrndsource"
msgstr "Banyak peluncur untuk membandingkan datasets, seolah-olah anda seorang DJ."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text2"
msgstr "Kami boleh teruskan untuk seketika, tetapi mari berhenti di sini. Pastikan untuk melihat semua penyerahan <a %(annas_archive)s>di sini</a>, atau muat turun <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Begitu banyak penyerahan, dan setiap satu membawa perspektif unik, sama ada dalam UI atau pelaksanaan."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text3"
msgstr "Kami sekurang-kurangnya akan menggabungkan penyerahan tempat pertama ke dalam laman web utama kami, dan mungkin beberapa yang lain. Kami juga telah mula memikirkan cara untuk mengatur proses mengenal pasti, mengesahkan, dan kemudian mengarkibkan buku-buku yang paling jarang. Lebih banyak akan datang dalam hal ini."

#, fuzzy
msgid "blog.all-isbns-winners.notable.text4"
msgstr "Terima kasih kepada semua yang telah mengambil bahagian. Sangat menakjubkan bahawa begitu ramai orang peduli."

#, fuzzy
msgid "blog.all-isbns-winners.gratitude"
msgstr "Hati kami penuh dengan rasa syukur."

#, fuzzy
msgid "blog.all-isbns-winners.footer"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.all-isbns.title"
msgstr "Memvisualisasikan Semua ISBN — ganjaran $10,000 menjelang 2025-01-31"

#, fuzzy
msgid "blog.all-isbns.tldr"
msgstr "Gambar ini mewakili \"senarai buku\" terbuka sepenuhnya terbesar yang pernah dikumpulkan dalam sejarah manusia."

#, fuzzy
msgid "blog.all-isbns.text1"
msgstr "Gambar ini berukuran 1000×800 piksel. Setiap piksel mewakili 2,500 ISBN. Jika kami mempunyai fail untuk ISBN, kami menjadikan piksel itu lebih hijau. Jika kami tahu ISBN telah dikeluarkan, tetapi kami tidak mempunyai fail yang sepadan, kami menjadikannya lebih merah."

#, fuzzy
msgid "blog.all-isbns.text2"
msgstr "Dalam kurang daripada 300kb, gambar ini secara ringkas mewakili \"senarai buku\" terbuka sepenuhnya terbesar yang pernah dikumpulkan dalam sejarah manusia (beberapa ratus GB dimampatkan sepenuhnya)."

#, fuzzy
msgid "blog.all-isbns.text3"
msgstr "Ia juga menunjukkan: masih banyak kerja yang perlu dilakukan dalam menyandarkan buku (kami hanya mempunyai 16%)."

#, fuzzy
msgid "blog.all-isbns.background"
msgstr "Latar Belakang"

#, fuzzy
msgid "blog.all-isbns.background.text1"
msgstr "Bagaimana Arkib Anna dapat mencapai misinya untuk menyandarkan semua pengetahuan manusia, tanpa mengetahui buku mana yang masih ada di luar sana? Kami memerlukan senarai TODO. Salah satu cara untuk memetakan ini adalah melalui nombor ISBN, yang sejak tahun 1970-an telah diberikan kepada setiap buku yang diterbitkan (di kebanyakan negara)."

#, fuzzy
msgid "blog.all-isbns.background.text2"
msgstr "Tiada pihak berkuasa pusat yang mengetahui semua penugasan ISBN. Sebaliknya, ia adalah sistem teragih, di mana negara-negara mendapat julat nombor, yang kemudian memberikan julat yang lebih kecil kepada penerbit utama, yang mungkin membahagikan lagi julat kepada penerbit kecil. Akhirnya nombor individu diberikan kepada buku."

#, fuzzy
msgid "blog.all-isbns.background.text3"
msgstr "Kami mula memetakan ISBN <a %(blog)s>dua tahun lalu</a> dengan pengikisan kami dari ISBNdb. Sejak itu, kami telah mengikis banyak lagi sumber metadata, seperti <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, dan banyak lagi. Senarai penuh boleh didapati di halaman \"Datasets\" dan \"Torrents\" di Arkib Anna. Kami kini mempunyai koleksi metadata buku yang terbuka sepenuhnya dan mudah dimuat turun terbesar di dunia (dan dengan itu ISBN)."

#, fuzzy
msgid "blog.all-isbns.background.text4"
msgstr "Kami telah <a %(blog)s>menulis secara meluas</a> tentang mengapa kami peduli tentang pemeliharaan, dan mengapa kami kini berada dalam jendela kritikal. Kami mesti sekarang mengenal pasti buku-buku yang jarang, kurang diberi tumpuan, dan unik berisiko dan memeliharanya. Mempunyai metadata yang baik pada semua buku di dunia membantu dengan itu."

#, fuzzy
msgid "blog.all-isbns.visualizing"
msgstr "Memvisualisasikan"

#, fuzzy
msgid "blog.all-isbns.visualizing.text1"
msgstr "Selain daripada imej gambaran keseluruhan, kami juga boleh melihat datasets individu yang telah kami perolehi. Gunakan dropdown dan butang untuk beralih antara mereka."

#, fuzzy
msgid "blog.all-isbns.visualizing.text2"
msgstr "Terdapat banyak corak menarik untuk dilihat dalam gambar-gambar ini. Mengapa terdapat beberapa keteraturan garis dan blok, yang nampaknya berlaku pada skala yang berbeza? Apakah kawasan kosong itu? Mengapa sesetengah datasets begitu berkelompok? Kami akan meninggalkan soalan-soalan ini sebagai latihan untuk pembaca."

#, fuzzy
msgid "blog.all-isbns.bounty"
msgstr "Ganjaran $10,000"

#, fuzzy
msgid "blog.all-isbns.bounty.text1"
msgstr "Terdapat banyak yang boleh diterokai di sini, jadi kami mengumumkan ganjaran untuk memperbaiki visualisasi di atas. Tidak seperti kebanyakan ganjaran kami, yang ini terikat masa. Anda perlu <a %(annas_archive)s>menghantar</a> kod sumber terbuka anda sebelum 2025-01-31 (23:59 UTC)."

#, fuzzy
msgid "blog.all-isbns.bounty.text2"
msgstr "Penyerahan terbaik akan mendapat $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Semua ganjaran akan diberikan menggunakan Monero (XMR)."

#, fuzzy
msgid "blog.all-isbns.bounty.text3"
msgstr "Di bawah adalah kriteria minimum. Jika tiada penyerahan memenuhi kriteria, kami mungkin masih memberikan beberapa ganjaran, tetapi itu akan mengikut budi bicara kami."

#, fuzzy
msgid "blog.all-isbns.bounty.req1"
msgstr "Fork repo ini, dan edit HTML pos blog ini (tiada backend lain selain backend Flask kami dibenarkan)."

#, fuzzy
msgid "blog.all-isbns.bounty.req2"
msgstr "Jadikan gambar di atas boleh dizum dengan lancar, supaya anda boleh zum sehingga ke ISBN individu. Mengklik ISBN sepatutnya membawa anda ke halaman metadata atau carian di Arkib Anna."

#, fuzzy
msgid "blog.all-isbns.bounty.req3"
msgstr "Anda mesti masih boleh bertukar antara semua Datasets yang berbeza."

#, fuzzy
msgid "blog.all-isbns.bounty.req4"
msgstr "Julat negara dan julat penerbit harus diserlahkan apabila dihover. Anda boleh menggunakan contohnya <a %(github_xlcnd_isbnlib)s>data4info.py dalam isbnlib</a> untuk maklumat negara, dan \"isbngrp\" scrape kami untuk penerbit (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>)."

#, fuzzy
msgid "blog.all-isbns.bounty.req5"
msgstr "Ia mesti berfungsi dengan baik di desktop dan mudah alih."

#, fuzzy
msgid "blog.all-isbns.bounty.text4"
msgstr "Untuk mata bonus (ini hanyalah idea — biarkan kreativiti anda berkembang):"

#, fuzzy
msgid "blog.all-isbns.bounty.bonus1"
msgstr "Pertimbangan kuat akan diberikan kepada kebolehgunaan dan bagaimana ia kelihatan."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus2"
msgstr "Tunjukkan metadata sebenar untuk ISBN individu apabila diperbesar, seperti tajuk dan pengarang."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus3"
msgstr "Lengkung pengisian ruang yang lebih baik. Contohnya, zig-zag, dari 0 ke 4 pada baris pertama dan kemudian kembali (secara terbalik) dari 5 ke 9 pada baris kedua — diterapkan secara rekursif."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus4"
msgstr "Skema warna yang berbeza atau boleh disesuaikan."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus5"
msgstr "Pandangan khas untuk membandingkan Datasets."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus6"
msgstr "Cara untuk menyelesaikan masalah, seperti metadata lain yang tidak bersetuju dengan baik (contohnya tajuk yang sangat berbeza)."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus7"
msgstr "Menandakan imej dengan komen pada ISBN atau julat."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus8"
msgstr "Sebarang heuristik untuk mengenal pasti buku yang jarang atau berisiko."

#, fuzzy
msgid "blog.all-isbns.bounty.bonus9"
msgstr "Apa sahaja idea kreatif yang anda boleh fikirkan!"

#, fuzzy
msgid "blog.all-isbns.bounty.text5"
msgstr "Anda BOLEH sepenuhnya menyimpang dari kriteria minimum, dan melakukan visualisasi yang sama sekali berbeza. Jika ia benar-benar spektakuler, maka itu layak untuk ganjaran, tetapi atas budi bicara kami."

#, fuzzy
msgid "blog.all-isbns.bounty.text6"
msgstr "Buat penyerahan dengan menyiarkan komen kepada <a %(annas_archive)s>isu ini</a> dengan pautan ke repo bercabang anda, permintaan gabungan, atau perbezaan."

#, fuzzy
msgid "blog.all-isbns.bounty.code"
msgstr "Kod"

#, fuzzy
msgid "blog.all-isbns.bounty.code.text1"
msgstr "Kod untuk menjana imej-imej ini, serta contoh-contoh lain, boleh didapati dalam <a %(annas_archive)s>direktori ini</a>."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text2"
msgstr "Kami telah mencipta format data yang padat, di mana semua maklumat ISBN yang diperlukan adalah sekitar 75MB (dimampatkan). Penerangan format data dan kod untuk menjana ia boleh didapati <a %(annas_archive_l1244_1319)s>di sini</a>. Untuk ganjaran ini, anda tidak diwajibkan menggunakan ini, tetapi ia mungkin format yang paling mudah untuk bermula. Anda boleh mengubah metadata kami mengikut kehendak anda (walaupun semua kod anda perlu sumber terbuka)."

#, fuzzy
msgid "blog.all-isbns.bounty.code.text3"
msgstr "Kami tidak sabar untuk melihat apa yang anda hasilkan. Semoga berjaya!"

#, fuzzy
msgid "blog.all-isbns.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-archive-containers.title"
msgstr "Kontena Arkib Anna (AAC): menyeragamkan keluaran dari perpustakaan bayangan terbesar di dunia"

#, fuzzy
msgid "blog.annas-archive-containers.tldr"
msgstr "Arkib Anna telah menjadi perpustakaan bayangan terbesar di dunia, memerlukan kami untuk menyeragamkan keluaran kami."

#, fuzzy
msgid "blog.annas-archive-containers.text1"
msgstr "<a %(wikipedia_annas_archive)s>Arkib Anna</a> telah menjadi perpustakaan bayangan terbesar di dunia, dan satu-satunya perpustakaan bayangan pada skala ini yang sepenuhnya sumber terbuka dan data terbuka. Di bawah adalah jadual dari halaman Datasets kami (sedikit diubah suai):"

#, fuzzy
msgid "blog.annas-archive-containers.text2"
msgstr "Kami mencapai ini dengan tiga cara:"

#, fuzzy
msgid "blog.annas-archive-containers.text2.li1"
msgstr "Mencerminkan perpustakaan bayangan data terbuka yang sedia ada (seperti Sci-Hub dan Library Genesis)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li2"
msgstr "Membantu perpustakaan bayangan yang ingin lebih terbuka, tetapi tidak mempunyai masa atau sumber untuk melakukannya (seperti koleksi komik Libgen)."

#, fuzzy
msgid "blog.annas-archive-containers.text2.li3"
msgstr "Mengikis perpustakaan yang tidak ingin berkongsi secara besar-besaran (seperti Z-Library)."

#, fuzzy
msgid "blog.annas-archive-containers.text3"
msgstr "Untuk (2) dan (3) kami kini menguruskan koleksi torrent yang besar sendiri (ratusan TB). Setakat ini kami mendekati koleksi ini sebagai satu-satu, bermakna infrastruktur dan organisasi data yang khusus untuk setiap koleksi. Ini menambah beban yang ketara kepada setiap pelepasan, dan menjadikannya sangat sukar untuk melakukan pelepasan yang lebih beransur-ansur."

#, fuzzy
msgid "blog.annas-archive-containers.text4"
msgstr "Itulah sebabnya kami memutuskan untuk menyeragamkan pelepasan kami. Ini adalah catatan blog teknikal di mana kami memperkenalkan piawaian kami: <strong>Kontena Arkib Anna</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.goals.heading"
msgstr "Matlamat reka bentuk"

#, fuzzy
msgid "blog.annas-archive-containers.goals.text1"
msgstr "Kes penggunaan utama kami adalah pengedaran fail dan metadata yang berkaitan dari pelbagai koleksi sedia ada. Pertimbangan terpenting kami adalah:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal1"
msgstr "Fail dan metadata yang heterogen, sedekat mungkin dengan format asal."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal2"
msgstr "Pengenal pasti yang heterogen dalam perpustakaan sumber, atau bahkan kekurangan pengenal pasti."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal3"
msgstr "Pelepasan berasingan metadata berbanding data fail, atau pelepasan metadata sahaja (contohnya pelepasan ISBNdb kami)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal4"
msgstr "Pengedaran melalui torrent, walaupun dengan kemungkinan kaedah pengedaran lain (contohnya IPFS)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal5"
msgstr "Rekod yang tidak boleh diubah, kerana kami harus menganggap torrent kami akan hidup selama-lamanya."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal6"
msgstr "Pelepasan beransur-ansur / pelepasan yang boleh ditambah."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal7"
msgstr "Boleh dibaca dan ditulis oleh mesin, dengan mudah dan cepat, terutamanya untuk tumpukan kami (Python, MySQL, ElasticSearch, Transmission, Debian, ext4)."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal8"
msgstr "Pemeriksaan manusia yang agak mudah, walaupun ini adalah sekunder kepada kebolehbacaan mesin."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal9"
msgstr "Mudah untuk menyemai koleksi kami dengan seedbox sewaan standard."

#, fuzzy
msgid "blog.annas-archive-containers.goals.goal10"
msgstr "Data binari boleh disajikan secara langsung oleh pelayan web seperti Nginx."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text2"
msgstr "Beberapa matlamat bukan:"

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal1"
msgstr "Kami tidak peduli tentang fail yang mudah dinavigasi secara manual pada cakera, atau boleh dicari tanpa pra-pemprosesan."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal2"
msgstr "Kami tidak peduli tentang keserasian langsung dengan perisian perpustakaan yang sedia ada."

#, fuzzy
msgid "blog.annas-archive-containers.goals.nongoal3"
msgstr "Walaupun sepatutnya mudah bagi sesiapa sahaja untuk menyemai koleksi kami menggunakan torrent, kami tidak menjangkakan fail-fail tersebut boleh digunakan tanpa pengetahuan teknikal yang signifikan dan komitmen."

#, fuzzy
msgid "blog.annas-archive-containers.goals.text3"
msgstr "Oleh kerana Arkib Anna adalah sumber terbuka, kami ingin menggunakan format kami secara langsung. Apabila kami menyegarkan indeks carian kami, kami hanya mengakses laluan yang tersedia secara umum, supaya sesiapa yang menyalin perpustakaan kami boleh memulakannya dengan cepat."

#, fuzzy
msgid "blog.annas-archive-containers.standard.heading"
msgstr "Piawaian"

#, fuzzy
msgid "blog.annas-archive-containers.standard.text1"
msgstr "Akhirnya, kami memutuskan untuk menggunakan piawaian yang agak mudah. Ia agak longgar, tidak normatif, dan sedang dalam proses."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aac"
msgstr "<strong>AAC.</strong> AAC (Bekas Arkib Anna) adalah satu item yang terdiri daripada <strong>metadata</strong>, dan secara opsional <strong>data binari</strong>, kedua-duanya tidak boleh diubah. Ia mempunyai pengecam unik global, yang dipanggil <strong>AACID</strong>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.collection"
msgstr "<strong>Koleksi.</strong> Setiap AAC tergolong dalam satu koleksi, yang mengikut definisi adalah senarai AAC yang konsisten secara semantik. Ini bermakna jika anda membuat perubahan yang signifikan pada format metadata, maka anda perlu mencipta koleksi baru."

#, fuzzy
msgid "blog.annas-archive-containers.standard.records-files"
msgstr "<strong>Koleksi “rekod” dan “fail”.</strong> Secara konvensyen, sering kali lebih mudah untuk melepaskan “rekod” dan “fail” sebagai koleksi yang berbeza, supaya ia boleh dilepaskan pada jadual yang berbeza, contohnya berdasarkan kadar pengikisan. “Rekod” adalah koleksi metadata sahaja, yang mengandungi maklumat seperti tajuk buku, pengarang, ISBN, dan sebagainya, manakala “fail” adalah koleksi yang mengandungi fail sebenar itu sendiri (pdf, epub)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid"
msgstr "<strong>AACID.</strong> Format AACID adalah seperti ini: <code style=\"color: #0093ff\">aacid__{koleksi}__{cap masa ISO 8601}__{ID khusus koleksi}__{shortuuid}</code>. Sebagai contoh, AACID sebenar yang kami lepaskan adalah <code style=\"color: #0093ff\">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection"
msgstr "<code>{koleksi}</code>: nama koleksi, yang mungkin mengandungi huruf ASCII, nombor, dan garis bawah (tetapi tiada garis bawah berganda)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.iso8601"
msgstr "<code>{ISO 8601 timestamp}</code>: versi pendek ISO 8601, sentiasa dalam UTC, contohnya <code>20220723T194746Z</code>. Nombor ini perlu meningkat secara monoton untuk setiap keluaran, walaupun semantik tepatnya boleh berbeza bagi setiap koleksi. Kami mencadangkan menggunakan masa pengikisan atau penjanaan ID."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.collection-id"
msgstr "<code>{ID khusus koleksi}</code>: pengenal pasti khusus koleksi, jika berkenaan, contohnya ID Z-Library. Mungkin diabaikan atau dipendekkan. Mesti diabaikan atau dipendekkan jika AACID sebaliknya melebihi 150 aksara."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid.shortuuid"
msgstr "<code>{shortuuid}</code>: UUID tetapi dimampatkan kepada ASCII, contohnya menggunakan base57. Kami kini menggunakan perpustakaan Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.aacid-range"
msgstr "<strong>Julat AACID.</strong> Oleh kerana AACID mengandungi cap masa yang meningkat secara monoton, kita boleh menggunakannya untuk menandakan julat dalam koleksi tertentu. Kami menggunakan format ini: <code style=\"color: blue\">aacid__{koleksi}__{dari_cap_masa}--{ke_cap_masa}</code>, di mana cap masa adalah inklusif. Julat adalah berterusan, dan mungkin bertindih, tetapi dalam kes pertindihan mesti mengandungi rekod yang sama seperti yang dikeluarkan sebelumnya dalam koleksi itu (kerana AAC adalah tidak boleh ubah). Rekod yang hilang tidak dibenarkan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file"
msgstr "<strong>Fail metadata.</strong> Fail metadata mengandungi metadata julat AAC, untuk satu koleksi tertentu. Ini mempunyai sifat berikut:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.filename"
msgstr "Nama fail mesti menjadi julat AACID, didahului dengan <code style=\"color: red\">annas_archive_meta__</code> dan diikuti oleh <code>.jsonl.zstd</code>. Sebagai contoh, salah satu keluaran kami dipanggil<br><code><span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.jsonlines"
msgstr "Seperti yang ditunjukkan oleh sambungan fail, jenis fail adalah <a %(jsonlines)s>JSON Lines</a> yang dimampatkan dengan <a %(zstd)s>Zstandard</a>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.fields"
msgstr "Setiap objek JSON mesti mengandungi medan berikut di peringkat atas: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (pilihan). Tiada medan lain dibenarkan."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.metadata"
msgstr "<code>metadata</code> adalah metadata sewenang-wenangnya, mengikut semantik koleksi. Ia mesti konsisten secara semantik dalam koleksi tersebut."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.data_folder"
msgstr "<code>data_folder</code> adalah pilihan, dan merupakan nama folder data binari yang mengandungi data binari yang sepadan. Nama fail data binari yang sepadan dalam folder itu adalah AACID rekod tersebut."

#, fuzzy
msgid "blog.annas-archive-containers.standard.metadata-file.prefix"
msgstr "Awalan <code style=\"color: red\">annas_archive_meta__</code> boleh disesuaikan dengan nama institusi anda, contohnya <code style=\"color: red\">my_institute_meta__</code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary"
msgstr "<strong>Folder data binari.</strong> Sebuah folder dengan data binari bagi satu julat AAC, untuk satu koleksi tertentu. Ini mempunyai sifat-sifat berikut:"

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.name"
msgstr "Nama direktori mesti merupakan julat AACID, diawali dengan <code style=\"color: green\">annas_archive_data__</code>, dan tiada akhiran. Sebagai contoh, salah satu keluaran sebenar kami mempunyai direktori yang dipanggil<br><code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.contents"
msgstr "Direktori mesti mengandungi fail data untuk semua AAC dalam julat yang ditentukan. Setiap fail data mesti mempunyai AACID sebagai nama failnya (tanpa sambungan)."

#, fuzzy
msgid "blog.annas-archive-containers.standard.binary.tip"
msgstr "Adalah disyorkan untuk membuat folder ini agak mudah diurus dari segi saiz, contohnya tidak lebih besar daripada 100GB-1TB setiap satu, walaupun cadangan ini mungkin berubah dari masa ke masa."

#, fuzzy
msgid "blog.annas-archive-containers.standard.torrents"
msgstr "<strong>Torrent.</strong> Fail metadata dan folder data binari boleh dibundel dalam torrent, dengan satu torrent bagi setiap fail metadata, atau satu torrent bagi setiap folder data binari. Torrent mesti mempunyai nama fail/direktori asal ditambah dengan akhiran <code>.torrent</code> sebagai nama failnya."

#, fuzzy
msgid "blog.annas-archive-containers.example.heading"
msgstr "Contoh"

#, fuzzy
msgid "blog.annas-archive-containers.example.text1"
msgstr "Mari kita lihat keluaran Z-Library terbaru kami sebagai contoh. Ia terdiri daripada dua koleksi: “<span style=\"background: #fffaa3\">zlib3_records</span>” dan “<span style=\"background: #ffd6fe\">zlib3_files</span>”. Ini membolehkan kami mengikis dan mengeluarkan rekod metadata secara berasingan daripada fail buku sebenar. Oleh itu, kami mengeluarkan dua torrent dengan fail metadata:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text2"
msgstr "Kami juga mengeluarkan beberapa torrent dengan folder data binari, tetapi hanya untuk koleksi “<span style=\"background: #ffd6fe\">zlib3_files</span>”, sebanyak 62 kesemuanya:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text3"
msgstr "Dengan menjalankan <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #fffaa3\">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kita dapat melihat apa yang ada di dalamnya:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text4"
msgstr "Dalam kes ini, ia adalah metadata sebuah buku seperti yang dilaporkan oleh Z-Library. Pada peringkat atas, kami hanya mempunyai “aacid” dan “metadata”, tetapi tiada “data_folder”, kerana tiada data binari yang sepadan. AACID mengandungi “22430000” sebagai ID utama, yang kita dapat lihat diambil daripada “zlibrary_id”. Kita boleh menjangkakan AAC lain dalam koleksi ini mempunyai struktur yang sama."

#, fuzzy
msgid "blog.annas-archive-containers.example.text5"
msgstr "Sekarang mari kita jalankan <code>zstdcat <span style=\"color: red\">annas_archive_meta__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>:"

#, fuzzy
msgid "blog.annas-archive-containers.example.text6"
msgstr "Ini adalah metadata AAC yang jauh lebih kecil, walaupun sebahagian besar AAC ini terletak di tempat lain dalam fail binari! Lagipun, kami mempunyai “data_folder” kali ini, jadi kami boleh menjangkakan data binari yang sepadan terletak di <code><span style=\"color: green\">annas_archive_data__</span><span style=\"color: blue\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style=\"color: #0093ff\">aacid__<span style=\"background: #ffd6fe\">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” mengandungi “zlibrary_id”, jadi kita boleh dengan mudah mengaitkannya dengan AAC yang sepadan dalam koleksi “zlib_records”. Kami boleh mengaitkan dengan beberapa cara yang berbeza, contohnya melalui AACID — standard tidak menetapkan itu."

#, fuzzy
msgid "blog.annas-archive-containers.example.text7"
msgstr "Perhatikan bahawa ia juga tidak perlu untuk medan “metadata” itu sendiri menjadi JSON. Ia boleh menjadi rentetan yang mengandungi XML atau mana-mana format data lain. Anda juga boleh menyimpan maklumat metadata dalam blob binari yang berkaitan, contohnya jika ia adalah banyak data."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.heading"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text1"
msgstr "Dengan standard ini, kita boleh membuat pelepasan lebih secara beransur-ansur, dan lebih mudah menambah sumber data baru. Kami sudah mempunyai beberapa pelepasan menarik dalam perancangan!"

#, fuzzy
msgid "blog.annas-archive-containers.conclusion.text2"
msgstr "Kami juga berharap ia menjadi lebih mudah untuk perpustakaan bayangan lain untuk mencerminkan koleksi kami. Lagipun, matlamat kami adalah untuk memelihara pengetahuan dan budaya manusia selama-lamanya, jadi lebih banyak redundansi lebih baik."

#, fuzzy
msgid "blog.annas-archive-containers.conclusion"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.annas-update-2022.title"
msgstr "Kemaskini Anna: arkib sumber terbuka sepenuhnya, ElasticSearch, 300GB+ kulit buku"

#, fuzzy
msgid "blog.annas-update-2022.tldr"
msgstr "Kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arkib Anna. Berikut adalah beberapa perkara yang telah kami capai baru-baru ini."

#, fuzzy
msgid "blog.annas-update-2022.text1"
msgstr "Dengan Z-Library ditutup dan pengasasnya (yang didakwa) ditangkap, kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arkib Anna (kami tidak akan pautkan di sini, tetapi anda boleh Google). Berikut adalah beberapa perkara yang telah kami capai baru-baru ini."

#, fuzzy
msgid "blog.annas-update-2022.open-source"
msgstr "Arkib Anna adalah sepenuhnya sumber terbuka"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text1"
msgstr "Kami percaya bahawa maklumat harus bebas, dan kod kami sendiri tidak terkecuali. Kami telah melepaskan semua kod kami di Gitlab yang dihoskan secara peribadi: <a %(annas_archive)s>Perisian Anna</a>. Kami juga menggunakan penjejak isu untuk mengatur kerja kami. Jika anda ingin terlibat dengan pembangunan kami, ini adalah tempat yang baik untuk bermula."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text2"
msgstr "Untuk memberi anda gambaran tentang perkara yang kami sedang usahakan, lihat kerja terbaru kami mengenai penambahbaikan prestasi sisi klien. Oleh kerana kami belum melaksanakan pemisahan halaman, kami sering mengembalikan halaman carian yang sangat panjang, dengan 100-200 hasil. Kami tidak mahu memotong hasil carian terlalu awal, tetapi ini bermakna ia akan memperlahankan beberapa peranti. Untuk ini, kami melaksanakan sedikit helah: kami membungkus kebanyakan hasil carian dalam komen HTML (<code><!-- --></code>), dan kemudian menulis sedikit Javascript yang akan mengesan bila hasil harus menjadi kelihatan, pada saat itu kami akan membuka komen tersebut:"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text3"
msgstr "\"Virtualisasi\" DOM dilaksanakan dalam 23 baris, tidak perlu perpustakaan mewah! Ini adalah jenis kod pragmatik cepat yang anda dapatkan apabila anda mempunyai masa yang terhad, dan masalah sebenar yang perlu diselesaikan. Telah dilaporkan bahawa carian kami kini berfungsi dengan baik pada peranti yang perlahan!"

#, fuzzy
msgid "blog.annas-update-2022.open-source.text4"
msgstr "Satu lagi usaha besar adalah untuk mengautomatikkan pembinaan pangkalan data. Apabila kami dilancarkan, kami hanya mengumpulkan pelbagai sumber secara sembarangan. Sekarang kami ingin memastikan mereka dikemas kini, jadi kami menulis beberapa skrip untuk memuat turun metadata baru dari dua cabang Library Genesis, dan mengintegrasikannya. Matlamatnya adalah bukan sahaja menjadikan ini berguna untuk arkib kami, tetapi untuk memudahkan sesiapa yang ingin bermain-main dengan metadata perpustakaan bayangan. Matlamatnya adalah sebuah buku nota Jupyter yang mempunyai pelbagai metadata menarik yang tersedia, supaya kami dapat melakukan lebih banyak penyelidikan seperti mencari tahu <a %(blog)s>peratusan ISBN yang dipelihara selama-lamanya</a>."

#, fuzzy
msgid "blog.annas-update-2022.open-source.text5"
msgstr "Akhirnya, kami memperbaharui sistem derma kami. Anda kini boleh menggunakan kad kredit untuk terus mendepositkan wang ke dalam dompet kripto kami, tanpa benar-benar perlu mengetahui apa-apa tentang mata wang kripto. Kami akan terus memantau sejauh mana ini berfungsi dalam praktik, tetapi ini adalah perkara besar."

#, fuzzy
msgid "blog.annas-update-2022.es"
msgstr "Beralih ke ElasticSearch"

#, fuzzy
msgid "blog.annas-update-2022.es.text1"
msgstr "Salah satu <a %(annas_archive)s>tiket</a> kami adalah sekumpulan isu dengan sistem carian kami. Kami menggunakan carian teks penuh MySQL, kerana kami mempunyai semua data kami dalam MySQL. Tetapi ia mempunyai hadnya:"

#, fuzzy
msgid "blog.annas-update-2022.es.problem1"
msgstr "Beberapa pertanyaan mengambil masa yang sangat lama, sehingga mereka akan menguasai semua sambungan terbuka."

#, fuzzy
msgid "blog.annas-update-2022.es.problem2"
msgstr "Secara lalai MySQL mempunyai panjang perkataan minimum, atau indeks anda boleh menjadi sangat besar. Orang melaporkan tidak dapat mencari \"Ben Hur\"."

#, fuzzy
msgid "blog.annas-update-2022.es.problem3"
msgstr "Carian hanya agak pantas apabila dimuat sepenuhnya dalam memori, yang memerlukan kami mendapatkan mesin yang lebih mahal untuk menjalankannya, ditambah beberapa arahan untuk memuatkan indeks pada permulaan."

#, fuzzy
msgid "blog.annas-update-2022.es.problem4"
msgstr "Kami tidak akan dapat memperluaskannya dengan mudah untuk membina ciri baru, seperti <a %(wikipedia_cjk_characters)s>tokenisasi yang lebih baik untuk bahasa tanpa ruang putih</a>, penapisan/pemfokusan, penyusunan, cadangan \"adakah anda maksudkan\", pelengkapan automatik, dan sebagainya."

#, fuzzy
msgid "blog.annas-update-2022.es.text2"
msgstr "Selepas berbincang dengan beberapa pakar, kami memilih ElasticSearch. Ia tidak sempurna (cadangan \"adakah anda maksudkan\" dan ciri autolengkap mereka tidak bagus), tetapi secara keseluruhan ia jauh lebih baik daripada MySQL untuk carian. Kami masih tidak <a %(youtube)s>terlalu berminat</a> menggunakannya untuk sebarang data kritikal misi (walaupun mereka telah membuat banyak <a %(elastic_co)s>kemajuan</a>), tetapi secara keseluruhan kami agak gembira dengan pertukaran ini."

#, fuzzy
msgid "blog.annas-update-2022.es.text3"
msgstr "Buat masa ini, kami telah melaksanakan carian yang lebih pantas, sokongan bahasa yang lebih baik, penyusunan relevansi yang lebih baik, pilihan penyusunan yang berbeza, dan penapisan pada jenis bahasa/buku/jenis fail. Jika anda ingin tahu bagaimana ia berfungsi, <a %(annas_archive_l140)s>lihatlah</a> <a %(annas_archive_l1115)s>di</a> <a %(annas_archive_l1635)s>sini</a>. Ia agak mudah diakses, walaupun ia boleh menggunakan lebih banyak komen…"

#, fuzzy
msgid "blog.annas-update-2022.covers"
msgstr "300GB+ kulit buku dikeluarkan"

#, fuzzy
msgid "blog.annas-update-2022.covers.text1"
msgstr "Akhirnya, kami gembira untuk mengumumkan pelepasan kecil. Dalam kerjasama dengan rakan-rakan yang mengendalikan cabang Libgen.rs, kami berkongsi semua kulit buku mereka melalui torrents dan IPFS. Ini akan mengagihkan beban melihat kulit buku di antara lebih banyak mesin, dan akan memelihara mereka dengan lebih baik. Dalam banyak (tetapi tidak semua) kes, kulit buku disertakan dalam fail itu sendiri, jadi ini adalah sejenis \"data terbitan\". Tetapi memilikinya dalam IPFS masih sangat berguna untuk operasi harian kedua-dua Arkib Anna dan pelbagai cabang Library Genesis."

#, fuzzy
msgid "blog.annas-update-2022.covers.text2"
msgstr "Seperti biasa, anda boleh menemui pelepasan ini di Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>). Kami tidak akan pautkan di sini, tetapi anda boleh menemuinya dengan mudah."

#, fuzzy
msgid "blog.annas-update-2022.covers.text3"
msgstr "Mudah-mudahan kita dapat melonggarkan sedikit rentak kita, sekarang kita mempunyai alternatif yang baik kepada Z-Library. Beban kerja ini tidak begitu mampan. Jika anda berminat untuk membantu dalam pengaturcaraan, operasi pelayan, atau kerja pemeliharaan, sila hubungi kami. Masih banyak <a %(annas_archive)s>kerja yang perlu dilakukan</a>. Terima kasih atas minat dan sokongan anda."

#, fuzzy
msgid "blog.all-isbns-winners.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.backed-up-libgen-li.title"
msgstr "Arkib Anna telah menyandarkan perpustakaan bayangan komik terbesar di dunia (95TB) — anda boleh membantu menyemai"

#, fuzzy
msgid "blog.backed-up-libgen-li.tldr"
msgstr "Perpustakaan bayangan buku komik terbesar di dunia mempunyai satu titik kegagalan.. sehingga hari ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.links"
msgstr "<a %(news_ycombinator)s>Bincangkan di Hacker News</a>"

#, fuzzy
msgid "blog.backed-up-libgen-li.text1"
msgstr "Perpustakaan bayangan terbesar bagi buku komik mungkin adalah dari satu cabang Library Genesis: Libgen.li. Satu pentadbir yang menjalankan laman itu berjaya mengumpulkan koleksi komik yang luar biasa lebih daripada 2 juta fail, berjumlah lebih 95TB. Walau bagaimanapun, tidak seperti koleksi Library Genesis yang lain, yang ini tidak tersedia secara pukal melalui torrent. Anda hanya boleh mengakses komik ini secara individu melalui pelayan peribadinya yang perlahan — satu titik kegagalan. Sehingga hari ini!"

#, fuzzy
msgid "blog.backed-up-libgen-li.text2"
msgstr "Dalam kiriman ini, kami akan memberitahu anda lebih lanjut tentang koleksi ini, dan tentang pengumpulan dana kami untuk menyokong lebih banyak kerja ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.fig1"
msgstr "<q>Dr. Barbara Gordon cuba menghilangkan dirinya dalam dunia perpustakaan yang biasa…</q>"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks"
msgstr "Cabang Libgen"

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text1"
msgstr "Pertama, sedikit latar belakang. Anda mungkin mengenali Library Genesis untuk koleksi buku epik mereka. Lebih sedikit orang tahu bahawa sukarelawan Library Genesis telah mencipta projek lain, seperti koleksi majalah dan dokumen standard yang besar, sandaran penuh Sci-Hub (dalam kerjasama dengan pengasas Sci-Hub, Alexandra Elbakyan), dan sememangnya, koleksi komik yang besar."

#, fuzzy
msgid "blog.backed-up-libgen-li.forks.text2"
msgstr "Pada satu ketika, pengendali cermin Library Genesis yang berbeza pergi ke arah masing-masing, yang menyebabkan situasi semasa mempunyai beberapa “fork” yang berbeza, semuanya masih membawa nama Library Genesis. Fork Libgen.li secara unik mempunyai koleksi komik ini, serta koleksi majalah yang besar (yang juga sedang kami usahakan)."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration"
msgstr "Kerjasama"

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text1"
msgstr "Memandangkan saiznya, koleksi ini telah lama berada dalam senarai keinginan kami, jadi selepas kejayaan kami dengan membuat sandaran Z-Library, kami menetapkan pandangan kami pada koleksi ini. Pada mulanya kami mengikisnya secara langsung, yang merupakan cabaran besar, kerana pelayan mereka tidak dalam keadaan terbaik. Kami mendapat kira-kira 15TB dengan cara ini, tetapi ia berjalan dengan perlahan."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text2"
msgstr "Nasib baik, kami berjaya berhubung dengan pengendali perpustakaan, yang bersetuju untuk menghantar semua data kepada kami secara langsung, yang jauh lebih pantas. Ia masih mengambil masa lebih setengah tahun untuk memindahkan dan memproses semua data, dan kami hampir kehilangan semuanya akibat kerosakan cakera, yang akan bermakna memulakan semula."

#, fuzzy
msgid "blog.backed-up-libgen-li.collaboration.text3"
msgstr "Pengalaman ini telah membuat kami percaya bahawa adalah penting untuk menyebarkan data ini secepat mungkin, supaya ia dapat dicerminkan secara meluas. Kami hanya satu atau dua insiden yang tidak bernasib baik daripada kehilangan koleksi ini selama-lamanya!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection"
msgstr "Koleksi"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text1"
msgstr "Bergerak pantas memang bermakna koleksi ini agak tidak teratur… Mari kita lihat. Bayangkan kita mempunyai sistem fail (yang sebenarnya kita pecahkan kepada torrent):"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text2"
msgstr "Direktori pertama, <code>/repository</code>, adalah bahagian yang lebih teratur. Direktori ini mengandungi apa yang dipanggil “seribu dir”: direktori masing-masing dengan seribu fail, yang dinomborkan secara berturutan dalam pangkalan data. Direktori <code>0</code> mengandungi fail dengan comic_id 0–999, dan seterusnya."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text3"
msgstr "Ini adalah skema yang sama seperti yang digunakan oleh Library Genesis untuk koleksi fiksyen dan bukan fiksyennya. Idea ini adalah bahawa setiap “seribu dir” secara automatik akan ditukar menjadi torrent sebaik sahaja ia penuh."

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text4"
msgstr "Walau bagaimanapun, pengendali Libgen.li tidak pernah membuat torrent untuk koleksi ini, dan oleh itu seribu dir mungkin menjadi tidak praktikal, dan memberi laluan kepada “dir tidak tersusun”. Ini adalah <code>/comics0</code> hingga <code>/comics4</code>. Kesemuanya mengandungi struktur direktori unik, yang mungkin masuk akal untuk mengumpul fail, tetapi tidak begitu masuk akal bagi kami sekarang. Nasib baik, metadata masih merujuk terus kepada semua fail ini, jadi organisasi penyimpanan mereka pada cakera sebenarnya tidak penting!"

#, fuzzy
msgid "blog.backed-up-libgen-li.collection.text5"
msgstr "Metadata tersedia dalam bentuk pangkalan data MySQL. Ini boleh dimuat turun terus dari laman web Libgen.li, tetapi kami juga akan menyediakannya dalam torrent, bersama dengan jadual kami sendiri dengan semua hash MD5."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis"
msgstr "Analisis"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text1"
msgstr "Apabila anda mendapat 95TB dimasukkan ke dalam kluster storan anda, anda cuba memahami apa yang ada di dalamnya… Kami melakukan beberapa analisis untuk melihat sama ada kami boleh mengurangkan saiznya sedikit, seperti dengan membuang pendua. Berikut adalah beberapa penemuan kami:"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item1"
msgstr "Pendua semantik (imbasan berbeza dari buku yang sama) secara teori boleh ditapis, tetapi ia rumit. Apabila melihat secara manual melalui komik, kami mendapati terlalu banyak positif palsu."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item2"
msgstr "Terdapat beberapa pendua semata-mata oleh MD5, yang agak membazir, tetapi menapisnya hanya akan memberikan kami kira-kira 1% in penjimatan. Pada skala ini, itu masih kira-kira 1TB, tetapi juga, pada skala ini 1TB tidak begitu penting. Kami lebih suka tidak mengambil risiko memusnahkan data secara tidak sengaja dalam proses ini."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item3"
msgstr "Kami menemui banyak data bukan buku, seperti filem berdasarkan buku komik. Itu juga nampaknya membazir, kerana ini sudah tersedia secara meluas melalui cara lain. Walau bagaimanapun, kami menyedari bahawa kami tidak boleh hanya menapis fail filem, kerana terdapat juga <em>buku komik interaktif</em> yang dikeluarkan di komputer, yang seseorang rakam dan simpan sebagai filem."

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.item4"
msgstr "Akhirnya, apa sahaja yang kami boleh padamkan dari koleksi hanya akan menjimatkan beberapa peratus sahaja. Kemudian kami teringat bahawa kami adalah pengumpul data, dan orang yang akan mencerminkan ini juga adalah pengumpul data, jadi, “APA MAKSUD ANDA, PADAM?!” :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.analysis.text2"
msgstr "Oleh itu, kami mempersembahkan kepada anda, koleksi penuh yang tidak diubah suai. Ia adalah banyak data, tetapi kami berharap cukup ramai orang akan peduli untuk menyemainya."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser"
msgstr "Pengumpulan Dana"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text1"
msgstr "Kami melepaskan data ini dalam beberapa bahagian besar. Torrent pertama adalah <code>/comics0</code>, yang kami masukkan ke dalam satu fail .tar besar 12TB. Itu lebih baik untuk cakera keras dan perisian torrent anda daripada beribu-ribu fail yang lebih kecil."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text2"
msgstr "Sebagai sebahagian daripada pelepasan ini, kami mengadakan pengumpulan dana. Kami berusaha untuk mengumpul $20,000 untuk menampung kos operasi dan kontrak untuk koleksi ini, serta membolehkan projek berterusan dan masa depan. Kami mempunyai beberapa <em>besar</em> yang sedang dalam perancangan."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text3"
msgstr "<em>Siapa yang saya sokong dengan sumbangan saya?</em> Ringkasnya: kami menyokong semua pengetahuan dan budaya manusia, dan menjadikannya mudah diakses. Semua kod dan data kami adalah sumber terbuka, kami adalah projek yang dijalankan sepenuhnya oleh sukarelawan, dan kami telah menyelamatkan 125TB buku setakat ini (selain daripada torrent sedia ada Libgen dan Scihub). Akhirnya kami membina roda terbang yang membolehkan dan mendorong orang untuk mencari, mengimbas, dan menyandarkan semua buku di dunia. Kami akan menulis tentang rancangan induk kami dalam catatan masa depan. :)"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text4"
msgstr "Jika anda menderma untuk keahlian “Amazing Archivist” selama 12 bulan ($780), anda boleh <strong>“mengadopsi torrent”</strong>, yang bermaksud kami akan meletakkan nama pengguna atau mesej anda dalam nama fail salah satu torrent!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text5"
msgstr "Anda boleh menderma dengan pergi ke <a %(wikipedia_annas_archive)s>Arkib Anna</a> dan mengklik butang “Derma”. Kami juga mencari lebih ramai sukarelawan: jurutera perisian, penyelidik keselamatan, pakar pedagang tanpa nama, dan penterjemah. Anda juga boleh menyokong kami dengan menyediakan perkhidmatan hosting. Dan sudah tentu, sila benihkan torrent kami!"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text6"
msgstr "Terima kasih kepada semua yang telah menyokong kami dengan begitu murah hati! Anda benar-benar membuat perbezaan."

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text7"
msgstr "Berikut adalah torrent yang telah dikeluarkan setakat ini (kami masih memproses yang lain):"

#, fuzzy
msgid "blog.backed-up-libgen-li.fundraiser.text8"
msgstr "Semua torrent boleh didapati di <a %(wikipedia_annas_archive)s>Arkib Anna</a> di bawah “Datasets” (kami tidak memautkan terus ke sana, jadi pautan ke blog ini tidak dikeluarkan dari Reddit, Twitter, dll). Dari situ, ikuti pautan ke laman web Tor."

#, fuzzy
msgid "blog.backed-up-libgen-li.next"
msgstr "Apa yang seterusnya?"

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text1"
msgstr "Sekumpulan torrent adalah hebat untuk pemeliharaan jangka panjang, tetapi tidak begitu untuk akses harian. Kami akan bekerjasama dengan rakan hosting untuk mendapatkan semua data ini di web (kerana Arkib Anna tidak menjadi hos apa-apa secara langsung). Sudah tentu anda akan dapat mencari pautan muat turun ini di Arkib Anna."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text2"
msgstr "Kami juga menjemput semua orang untuk melakukan sesuatu dengan data ini! Bantu kami menganalisisnya dengan lebih baik, mendeduplikasikannya, meletakkannya di IPFS, mengadunkannya, melatih model AI anda dengannya, dan sebagainya. Ia semua milik anda, dan kami tidak sabar untuk melihat apa yang anda lakukan dengannya."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text3"
msgstr "Akhirnya, seperti yang dikatakan sebelum ini, kami masih mempunyai beberapa pelepasan besar yang akan datang (jika <em>seseorang</em> boleh <em>secara tidak sengaja</em> menghantar kami satu longgokan pangkalan data <em>ACS4 tertentu</em>, anda tahu di mana untuk mencari kami…), serta membina roda tenaga untuk menyandarkan semua buku di dunia."

#, fuzzy
msgid "blog.backed-up-libgen-li.next.text4"
msgstr "Jadi nantikan, kami baru sahaja bermula."

#, fuzzy
msgid "blog.backed-up-libgen-li.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.3x-new-books.title"
msgstr "3x buku baru ditambah ke Cermin Perpustakaan Lanun (+24TB, 3.8 juta buku)"

#, fuzzy
msgid "blog.3x-new-books.text1"
msgstr "Dalam keluaran asal Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>), kami membuat cermin Z-Library, satu koleksi buku haram yang besar. Sebagai peringatan, ini adalah apa yang kami tulis dalam catatan blog asal itu:"

#, fuzzy
msgid "blog.3x-new-books.q1.text1"
msgstr "Z-Library adalah perpustakaan yang popular (dan haram). Mereka telah mengambil koleksi Library Genesis dan menjadikannya mudah dicari. Selain itu, mereka menjadi sangat berkesan dalam meminta sumbangan buku baru, dengan memberi insentif kepada pengguna yang menyumbang dengan pelbagai kelebihan. Mereka kini tidak menyumbangkan buku-buku baru ini kembali kepada Library Genesis. Dan tidak seperti Library Genesis, mereka tidak menjadikan koleksi mereka mudah dicerminkan, yang menghalang pemeliharaan yang meluas. Ini penting untuk model perniagaan mereka, kerana mereka mengenakan bayaran untuk mengakses koleksi mereka secara pukal (lebih daripada 10 buku sehari)."

#, fuzzy
msgid "blog.3x-new-books.q1.text2"
msgstr "Kami tidak membuat penilaian moral tentang mengenakan bayaran untuk akses pukal kepada koleksi buku haram. Tidak dapat dinafikan bahawa Z-Library telah berjaya dalam memperluas akses kepada pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bahagian kami: memastikan pemeliharaan jangka panjang koleksi peribadi ini."

#, fuzzy
msgid "blog.3x-new-books.text2"
msgstr "Koleksi itu bertarikh kembali ke pertengahan 2021. Sementara itu, Z-Library telah berkembang pada kadar yang mengejutkan: mereka telah menambah kira-kira 3.8 juta buku baru. Terdapat beberapa pendua di dalamnya, tetapi kebanyakannya nampaknya adalah buku baru yang sah, atau imbasan berkualiti lebih tinggi daripada buku yang telah diserahkan sebelum ini. Ini sebahagian besarnya disebabkan oleh peningkatan bilangan moderator sukarelawan di Z-Library, dan sistem muat naik pukal mereka dengan deduplikasi. Kami ingin mengucapkan tahniah kepada mereka atas pencapaian ini."

#, fuzzy
msgid "blog.3x-new-books.text3"
msgstr "Kami gembira untuk mengumumkan bahawa kami telah mendapatkan semua buku yang ditambah ke Z-Library antara cermin terakhir kami dan Ogos 2022. Kami juga telah kembali dan mengikis beberapa buku yang kami terlepas kali pertama. Secara keseluruhannya, koleksi baru ini adalah kira-kira 24TB, yang jauh lebih besar daripada yang terakhir (7TB). Cermin kami kini berjumlah 31TB. Sekali lagi, kami telah melakukan deduplikasi terhadap Library Genesis, kerana sudah ada torrents yang tersedia untuk koleksi itu."

#, fuzzy
msgid "blog.3x-new-books.text4"
msgstr "Sila pergi ke Cermin Perpustakaan Lanun untuk melihat koleksi baharu (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>). Terdapat lebih banyak maklumat di sana tentang bagaimana fail disusun, dan apa yang telah berubah sejak kali terakhir. Kami tidak akan memautkannya dari sini, kerana ini hanyalah laman web blog yang tidak menjadi hos bahan haram."

#, fuzzy
msgid "blog.3x-new-books.text5"
msgstr "Sudah tentu, membenihkan juga merupakan cara yang baik untuk membantu kami. Terima kasih kepada semua yang membenihkan set torrent kami yang terdahulu. Kami berterima kasih atas sambutan positif, dan gembira bahawa terdapat begitu ramai orang yang mengambil berat tentang pemeliharaan pengetahuan dan budaya dengan cara yang luar biasa ini."

#, fuzzy
msgid "blog.3x-new-books.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.how-to.title"
msgstr "Bagaimana untuk menjadi arkivis lanun"

#, fuzzy
msgid "blog.how-to.tldr"
msgstr "Cabaran pertama mungkin mengejutkan. Ia bukan masalah teknikal, atau masalah undang-undang. Ia adalah masalah psikologi."

#, fuzzy
msgid "blog.how-to.updates"
msgstr "Sebelum kita menyelam masuk, dua kemas kini mengenai Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>):"

#, fuzzy
msgid "blog.how-to.updates.item1"
msgstr "Kami menerima beberapa sumbangan yang sangat murah hati. Yang pertama adalah $10k daripada individu tanpa nama yang juga telah menyokong \"bookwarrior\", pengasas asal Library Genesis. Terima kasih khas kepada bookwarrior kerana memudahkan sumbangan ini. Yang kedua adalah $10k lagi daripada penderma tanpa nama, yang menghubungi kami selepas keluaran terakhir kami, dan terinspirasi untuk membantu. Kami juga menerima beberapa sumbangan yang lebih kecil. Terima kasih banyak atas sokongan anda yang murah hati. Kami mempunyai beberapa projek baru yang menarik dalam perancangan yang akan disokong oleh ini, jadi nantikan."

#, fuzzy
msgid "blog.how-to.updates.item2"
msgstr "Kami mengalami beberapa kesulitan teknikal dengan saiz keluaran kedua kami, tetapi torrent kami kini sudah tersedia dan sedang disemai. Kami juga menerima tawaran murah hati daripada individu tanpa nama untuk menyemai koleksi kami pada pelayan berkelajuan sangat tinggi mereka, jadi kami sedang melakukan muat naik khas ke mesin mereka, selepas itu semua orang lain yang memuat turun koleksi tersebut sepatutnya melihat peningkatan besar dalam kelajuan."

#, fuzzy
msgid "blog.how-to.text1"
msgstr "Buku penuh boleh ditulis tentang <em>mengapa</em> pemeliharaan digital secara umum, dan arkivisme lanun secara khusus, tetapi mari kita berikan pengenalan ringkas untuk mereka yang tidak terlalu biasa. Dunia sedang menghasilkan lebih banyak pengetahuan dan budaya berbanding sebelum ini, tetapi juga lebih banyak daripadanya hilang berbanding sebelum ini. Manusia sebahagian besarnya mempercayakan syarikat seperti penerbit akademik, perkhidmatan penstriman, dan syarikat media sosial dengan warisan ini, dan mereka sering kali tidak terbukti menjadi penjaga yang baik. Lihatlah dokumentari Digital Amnesia, atau mana-mana ceramah oleh Jason Scott."

#, fuzzy
msgid "blog.how-to.text2"
msgstr "Terdapat beberapa institusi yang melakukan kerja yang baik dalam mengarkibkan sebanyak mungkin, tetapi mereka terikat oleh undang-undang. Sebagai lanun, kami berada dalam kedudukan unik untuk mengarkibkan koleksi yang mereka tidak dapat sentuh, kerana penguatkuasaan hak cipta atau sekatan lain. Kami juga boleh mencermin koleksi berkali-kali, di seluruh dunia, dengan itu meningkatkan peluang pemeliharaan yang betul."

#, fuzzy
msgid "blog.how-to.text3"
msgstr "Buat masa ini, kami tidak akan membincangkan tentang kebaikan dan keburukan harta intelek, moral melanggar undang-undang, renungan tentang penapisan, atau isu akses kepada pengetahuan dan budaya. Dengan semua itu diketepikan, mari kita terjun ke dalam <em>bagaimana</em>. Kami akan berkongsi bagaimana pasukan kami menjadi arkivis lanun, dan pelajaran yang kami pelajari sepanjang perjalanan. Terdapat banyak cabaran apabila anda memulakan perjalanan ini, dan semoga kami dapat membantu anda melalui beberapa daripadanya."

#, fuzzy
msgid "blog.how-to.community"
msgstr "Komuniti"

#, fuzzy
msgid "blog.how-to.community.text1"
msgstr "Cabaran pertama mungkin mengejutkan. Ia bukan masalah teknikal, atau masalah undang-undang. Ia adalah masalah psikologi: melakukan kerja ini dalam bayangan boleh menjadi sangat sunyi. Bergantung pada apa yang anda rancang untuk lakukan, dan model ancaman anda, anda mungkin perlu sangat berhati-hati. Di satu hujung spektrum, kami mempunyai orang seperti Alexandra Elbakyan*, pengasas Sci-Hub, yang sangat terbuka tentang aktivitinya. Tetapi dia berisiko tinggi ditangkap jika dia melawat negara barat pada masa ini, dan boleh menghadapi puluhan tahun penjara. Adakah itu risiko yang anda sanggup ambil? Kami berada di hujung spektrum yang lain; sangat berhati-hati untuk tidak meninggalkan sebarang jejak, dan mempunyai keselamatan operasi yang kuat."

#, fuzzy
msgid "blog.how-to.community.text2"
msgstr "* Seperti yang disebutkan di HN oleh \"ynno\", Alexandra pada mulanya tidak mahu dikenali: \"Pelayan-pelayan beliau disediakan untuk mengeluarkan mesej ralat terperinci dari PHP, termasuk laluan penuh fail sumber yang bermasalah, yang berada di bawah direktori /home/<USER>" Jadi, gunakan nama pengguna rawak pada komputer yang anda gunakan untuk perkara ini, sekiranya anda salah konfigurasi sesuatu."

#, fuzzy
msgid "blog.how-to.community.text3"
msgstr "Namun, kerahsiaan itu datang dengan kos psikologi. Kebanyakan orang suka diiktiraf untuk kerja yang mereka lakukan, dan namun anda tidak boleh mengambil sebarang kredit untuk ini dalam kehidupan sebenar. Malah perkara-perkara mudah boleh menjadi mencabar, seperti rakan-rakan bertanya apa yang anda telah lakukan (pada satu ketika \"bermain dengan NAS / homelab saya\" menjadi membosankan)."

#, fuzzy
msgid "blog.how-to.community.text4"
msgstr "Inilah sebabnya mengapa sangat penting untuk mencari beberapa komuniti. Anda boleh melepaskan sedikit keselamatan operasi dengan mempercayai beberapa rakan yang sangat rapat, yang anda tahu anda boleh percayai sepenuhnya. Walaupun begitu, berhati-hati untuk tidak meletakkan apa-apa dalam tulisan, sekiranya mereka perlu menyerahkan e-mel mereka kepada pihak berkuasa, atau jika peranti mereka dikompromi dengan cara lain."

#, fuzzy
msgid "blog.how-to.community.text5"
msgstr "Lebih baik lagi adalah mencari beberapa rakan lanun. Jika rakan rapat anda berminat untuk menyertai anda, hebat! Jika tidak, anda mungkin dapat mencari orang lain dalam talian. Malangnya ini masih merupakan komuniti niche. Setakat ini kami hanya menemui segelintir orang lain yang aktif dalam ruang ini. Tempat permulaan yang baik nampaknya adalah forum Library Genesis, dan r/DataHoarder. Pasukan Arkib juga mempunyai individu yang sependapat, walaupun mereka beroperasi dalam undang-undang (walaupun dalam beberapa kawasan kelabu undang-undang). Adegan \"warez\" dan cetak rompak tradisional juga mempunyai orang yang berfikir dengan cara yang serupa."

#, fuzzy
msgid "blog.how-to.community.text6"
msgstr "Kami terbuka kepada idea tentang cara untuk memupuk komuniti dan meneroka idea. Jangan ragu untuk menghantar mesej kepada kami di Twitter atau Reddit. Mungkin kami boleh menganjurkan sejenis forum atau kumpulan sembang. Satu cabaran adalah bahawa ini boleh dengan mudah disekat apabila menggunakan platform biasa, jadi kami perlu menganjurkannya sendiri. Terdapat juga pertukaran antara mengadakan perbincangan ini secara terbuka sepenuhnya (lebih banyak potensi penglibatan) berbanding menjadikannya peribadi (tidak membiarkan \"sasaran\" berpotensi tahu bahawa kami akan mengikis mereka). Kami perlu memikirkan tentang itu. Beritahu kami jika anda berminat dengan ini!"

#, fuzzy
msgid "blog.how-to.projects"
msgstr "Projek"

#, fuzzy
msgid "blog.how-to.projects.text1"
msgstr "Apabila kami menjalankan projek, ia mempunyai beberapa fasa:"

#, fuzzy
msgid "blog.how-to.projects.phase1"
msgstr "Pemilihan domain / falsafah: Di mana anda ingin memberi tumpuan secara kasar, dan mengapa? Apakah minat, kemahiran, dan keadaan unik anda yang boleh anda gunakan untuk manfaat anda?"

#, fuzzy
msgid "blog.how-to.projects.phase2"
msgstr "Pemilihan sasaran: Koleksi khusus mana yang akan anda cermin?"

#, fuzzy
msgid "blog.how-to.projects.phase3"
msgstr "Pengikisan metadata: Mengkatalogkan maklumat tentang fail, tanpa benar-benar memuat turun fail (yang selalunya lebih besar) itu sendiri."

#, fuzzy
msgid "blog.how-to.projects.phase4"
msgstr "Pemilihan data: Berdasarkan metadata, mengecilkan data mana yang paling relevan untuk diarkibkan sekarang. Mungkin semuanya, tetapi selalunya terdapat cara yang munasabah untuk menjimatkan ruang dan lebar jalur."

#, fuzzy
msgid "blog.how-to.projects.phase5"
msgstr "Pengikisan data: Sebenarnya mendapatkan data."

#, fuzzy
msgid "blog.how-to.projects.phase6"
msgstr "Pengedaran: Membungkusnya dalam torrent, mengumumkannya di suatu tempat, mendapatkan orang untuk menyebarkannya."

#, fuzzy
msgid "blog.how-to.projects.text2"
msgstr "Ini bukan fasa yang sepenuhnya bebas, dan selalunya pandangan dari fasa kemudian menghantar anda kembali ke fasa sebelumnya. Sebagai contoh, semasa pengikisan metadata anda mungkin menyedari bahawa sasaran yang anda pilih mempunyai mekanisme pertahanan di luar tahap kemahiran anda (seperti sekatan IP), jadi anda kembali dan mencari sasaran lain."

#, fuzzy
msgid "blog.how-to.projects.domain"
msgstr "1. Pemilihan domain / falsafah"

#, fuzzy
msgid "blog.how-to.projects.domain.text1"
msgstr "Tidak ada kekurangan pengetahuan dan warisan budaya untuk diselamatkan, yang boleh menjadi luar biasa. Itulah sebabnya selalunya berguna untuk meluangkan masa dan memikirkan tentang apa yang boleh anda sumbangkan."

#, fuzzy
msgid "blog.how-to.projects.domain.text2"
msgstr "Setiap orang mempunyai cara berfikir yang berbeza tentang ini, tetapi berikut adalah beberapa soalan yang boleh anda tanyakan kepada diri sendiri:"

#, fuzzy
msgid "blog.how-to.projects.domain.why.why"
msgstr "Mengapa anda berminat dengan ini? Apa yang anda minati? Jika kita boleh mendapatkan sekumpulan orang yang semua mengarkibkan jenis perkara yang mereka pedulikan secara khusus, itu akan meliputi banyak! Anda akan tahu lebih banyak daripada orang biasa tentang minat anda, seperti data penting untuk disimpan, apakah koleksi dan komuniti dalam talian terbaik, dan sebagainya."

#, fuzzy
msgid "blog.how-to.projects.domain.why.skills"
msgstr "Apakah kemahiran yang anda miliki yang boleh anda gunakan untuk manfaat anda? Sebagai contoh, jika anda seorang pakar keselamatan dalam talian, anda boleh mencari cara untuk mengalahkan sekatan IP untuk sasaran yang selamat. Jika anda hebat dalam mengatur komuniti, maka mungkin anda boleh mengumpulkan beberapa orang di sekitar satu matlamat. Adalah berguna untuk mengetahui sedikit pengaturcaraan, walaupun hanya untuk menjaga keselamatan operasi yang baik sepanjang proses ini."

#, fuzzy
msgid "blog.how-to.projects.domain.why.time"
msgstr "Berapa banyak masa yang anda ada untuk ini? Nasihat kami adalah untuk memulakan dengan kecil dan melakukan projek yang lebih besar apabila anda sudah biasa, tetapi ia boleh menjadi sangat menyeluruh."

#, fuzzy
msgid "blog.how-to.projects.domain.why.target"
msgstr "Apakah kawasan leverage tinggi yang perlu difokuskan? Jika anda akan menghabiskan X jam untuk pengarkiban lanun, maka bagaimana anda boleh mendapatkan \"hasil yang paling berbaloi\"?"

#, fuzzy
msgid "blog.how-to.projects.domain.why.thinking"
msgstr "Apakah cara unik yang anda fikirkan tentang ini? Anda mungkin mempunyai beberapa idea atau pendekatan menarik yang mungkin terlepas oleh orang lain."

#, fuzzy
msgid "blog.how-to.projects.domain.text3"
msgstr "Dalam kes kami, kami sangat mengambil berat tentang pemeliharaan jangka panjang sains. Kami tahu tentang Library Genesis, dan bagaimana ia dicerminkan sepenuhnya berkali-kali menggunakan torrents. Kami menyukai idea itu. Kemudian suatu hari, salah seorang daripada kami cuba mencari beberapa buku teks saintifik di Library Genesis, tetapi tidak dapat menemuinya, menimbulkan keraguan tentang betapa lengkapnya ia sebenarnya. Kami kemudian mencari buku teks tersebut dalam talian, dan menemuinya di tempat lain, yang menanam benih untuk projek kami. Bahkan sebelum kami tahu tentang Z-Library, kami mempunyai idea untuk tidak cuba mengumpulkan semua buku tersebut secara manual, tetapi untuk fokus pada mencerminkan koleksi sedia ada, dan menyumbangkannya kembali kepada Library Genesis."

#, fuzzy
msgid "blog.how-to.projects.target"
msgstr "2. Pemilihan sasaran"

#, fuzzy
msgid "blog.how-to.projects.target.text1"
msgstr "Jadi, kami mempunyai kawasan yang kami lihat, sekarang koleksi khusus mana yang kami cermin? Terdapat beberapa perkara yang menjadikan sasaran yang baik:"

#, fuzzy
msgid "blog.how-to.projects.target.large"
msgstr "Besar"

#, fuzzy
msgid "blog.how-to.projects.target.unique"
msgstr "Unik: tidak sudah diliputi dengan baik oleh projek lain."

#, fuzzy
msgid "blog.how-to.projects.target.accessible"
msgstr "Mudah diakses: tidak menggunakan banyak lapisan perlindungan untuk menghalang anda daripada mengikis metadata dan data mereka."

#, fuzzy
msgid "blog.how-to.projects.target.insight"
msgstr "Wawasan istimewa: anda mempunyai maklumat istimewa tentang sasaran ini, seperti anda mempunyai akses khas kepada koleksi ini, atau anda berjaya mengatasi pertahanan mereka. Ini tidak diperlukan (projek kami yang akan datang tidak melakukan apa-apa yang istimewa), tetapi ia pasti membantu!"

#, fuzzy
msgid "blog.how-to.projects.target.text2"
msgstr "Apabila kami menemui buku teks sains kami di laman web selain daripada Library Genesis, kami cuba mencari tahu bagaimana mereka sampai ke internet. Kami kemudian menemui Z-Library, dan menyedari bahawa walaupun kebanyakan buku tidak pertama kali muncul di sana, mereka akhirnya berakhir di sana. Kami belajar tentang hubungannya dengan Library Genesis, dan struktur insentif (kewangan) dan antara muka pengguna yang unggul, kedua-duanya menjadikannya koleksi yang lebih lengkap. Kami kemudian melakukan beberapa pengikisan metadata dan data awal, dan menyedari bahawa kami dapat mengatasi had muat turun IP mereka, memanfaatkan akses khas salah seorang ahli kami ke banyak pelayan proksi."

#, fuzzy
msgid "blog.how-to.projects.target.text3"
msgstr "Semasa anda meneroka sasaran yang berbeza, adalah penting untuk menyembunyikan jejak anda dengan menggunakan VPN dan alamat e-mel sekali pakai, yang akan kami bincangkan lebih lanjut kemudian."

#, fuzzy
msgid "blog.how-to.projects.metadata"
msgstr "3. Pengikisan metadata"

#, fuzzy
msgid "blog.how-to.projects.metadata.text1"
msgstr "Mari kita menjadi sedikit lebih teknikal di sini. Untuk benar-benar mengikis metadata dari laman web, kami telah menjaga perkara-perkara dengan cukup mudah. Kami menggunakan skrip Python, kadang-kadang curl, dan pangkalan data MySQL untuk menyimpan hasilnya. Kami belum menggunakan sebarang perisian pengikisan mewah yang dapat memetakan laman web yang kompleks, kerana setakat ini kami hanya perlu mengikis satu atau dua jenis halaman dengan hanya menyenaraikan melalui id dan mengurai HTML. Jika tidak ada halaman yang mudah disenaraikan, maka anda mungkin memerlukan perayap yang betul yang cuba mencari semua halaman."

#, fuzzy
msgid "blog.how-to.projects.metadata.text2"
msgstr "Sebelum anda mula mengikis seluruh laman web, cuba lakukannya secara manual untuk seketika. Pergi melalui beberapa dozen halaman sendiri, untuk mendapatkan rasa bagaimana itu berfungsi. Kadang-kadang anda akan sudah menghadapi blok IP atau tingkah laku menarik lain dengan cara ini. Begitu juga untuk pengikisan data: sebelum terlalu mendalam ke dalam sasaran ini, pastikan anda benar-benar dapat memuat turun datanya dengan berkesan."

#, fuzzy
msgid "blog.how-to.projects.metadata.text3"
msgstr "Untuk mengatasi sekatan, ada beberapa perkara yang boleh anda cuba. Adakah terdapat alamat IP atau pelayan lain yang menjadi tuan rumah data yang sama tetapi tidak mempunyai sekatan yang sama? Adakah terdapat titik akhir API yang tidak mempunyai sekatan, sementara yang lain ada? Pada kadar muat turun berapa IP anda disekat, dan untuk berapa lama? Atau adakah anda tidak disekat tetapi diperlambat? Bagaimana jika anda membuat akaun pengguna, bagaimana perkara berubah kemudian? Bolehkah anda menggunakan HTTP/2 untuk menjaga sambungan terbuka, dan adakah itu meningkatkan kadar di mana anda boleh meminta halaman? Adakah terdapat halaman yang menyenaraikan beberapa fail sekaligus, dan adakah maklumat yang disenaraikan di sana mencukupi?"

#, fuzzy
msgid "blog.how-to.projects.metadata.text4"
msgstr "Perkara yang mungkin anda ingin simpan termasuk:"

#, fuzzy
msgid "blog.how-to.projects.metadata.title"
msgstr "Tajuk"

#, fuzzy
msgid "blog.how-to.projects.metadata.location"
msgstr "Nama fail / lokasi"

#, fuzzy
msgid "blog.how-to.projects.metadata.id"
msgstr "ID: boleh jadi beberapa ID dalaman, tetapi ID seperti ISBN atau DOI juga berguna."

#, fuzzy
msgid "blog.how-to.projects.metadata.size"
msgstr "Saiz: untuk mengira berapa banyak ruang cakera yang anda perlukan."

#, fuzzy
msgid "blog.how-to.projects.metadata.hash"
msgstr "Hash (md5, sha1): untuk mengesahkan bahawa anda memuat turun fail dengan betul."

#, fuzzy
msgid "blog.how-to.projects.metadata.dates"
msgstr "Tarikh ditambah/diubah: supaya anda boleh kembali kemudian dan memuat turun fail yang anda belum muat turun sebelum ini (walaupun anda juga boleh menggunakan ID atau hash untuk ini)."

#, fuzzy
msgid "blog.how-to.projects.metadata.notes"
msgstr "Penerangan, kategori, tag, pengarang, bahasa, dan lain-lain."

#, fuzzy
msgid "blog.how-to.projects.metadata.text5"
msgstr "Kami biasanya melakukan ini dalam dua peringkat. Pertama, kami memuat turun fail HTML mentah, biasanya terus ke dalam MySQL (untuk mengelakkan banyak fail kecil, yang kami bincangkan lebih lanjut di bawah). Kemudian, dalam langkah berasingan, kami melalui fail HTML tersebut dan memprosesnya ke dalam jadual MySQL sebenar. Dengan cara ini, anda tidak perlu memuat turun semula semuanya dari awal jika anda menemui kesilapan dalam kod pemprosesan anda, kerana anda boleh memproses semula fail HTML dengan kod baru. Ia juga sering lebih mudah untuk memparallelkan langkah pemprosesan, dengan itu menjimatkan masa (dan anda boleh menulis kod pemprosesan semasa pengikisan sedang berjalan, daripada perlu menulis kedua-dua langkah sekaligus)."

#, fuzzy
msgid "blog.how-to.projects.metadata.text6"
msgstr "Akhirnya, perhatikan bahawa untuk beberapa sasaran, pengikisan metadata adalah semua yang ada. Terdapat beberapa koleksi metadata besar di luar sana yang tidak dipelihara dengan betul."

#, fuzzy
msgid "blog.how-to.projects.data"
msgstr "4. Pemilihan data"

#, fuzzy
msgid "blog.how-to.projects.data.text1"
msgstr "Selalunya anda boleh menggunakan metadata untuk menentukan subset data yang munasabah untuk dimuat turun. Walaupun anda akhirnya ingin memuat turun semua data, ia boleh berguna untuk memprioritaskan item yang paling penting terlebih dahulu, sekiranya anda dikesan dan pertahanan diperbaiki, atau kerana anda perlu membeli lebih banyak cakera, atau semata-mata kerana sesuatu yang lain muncul dalam hidup anda sebelum anda dapat memuat turun semuanya."

#, fuzzy
msgid "blog.how-to.projects.data.text2"
msgstr "Sebagai contoh, satu koleksi mungkin mempunyai pelbagai edisi sumber asas yang sama (seperti buku atau filem), di mana satu ditandakan sebagai kualiti terbaik. Menyimpan edisi tersebut terlebih dahulu adalah masuk akal. Anda mungkin akhirnya ingin menyimpan semua edisi, kerana dalam beberapa kes metadata mungkin ditandakan dengan salah, atau mungkin terdapat kompromi yang tidak diketahui antara edisi (contohnya, \"edisi terbaik\" mungkin terbaik dalam kebanyakan cara tetapi lebih buruk dalam cara lain, seperti filem yang mempunyai resolusi lebih tinggi tetapi tiada sari kata)."

#, fuzzy
msgid "blog.how-to.projects.data.text3"
msgstr "Anda juga boleh mencari pangkalan data metadata anda untuk mencari perkara menarik. Apakah fail terbesar yang dihoskan, dan mengapa ia begitu besar? Apakah fail terkecil? Adakah terdapat corak menarik atau tidak dijangka apabila berkaitan dengan kategori tertentu, bahasa, dan sebagainya? Adakah terdapat tajuk yang berulang atau sangat serupa? Adakah terdapat corak apabila data ditambah, seperti satu hari di mana banyak fail ditambah sekaligus? Anda sering dapat belajar banyak dengan melihat set data dalam pelbagai cara."

#, fuzzy
msgid "blog.how-to.projects.data.text4"
msgstr "Dalam kes kami, kami menghapuskan pendua buku Z-Library terhadap hash md5 dalam Library Genesis, dengan itu menjimatkan banyak masa muat turun dan ruang cakera. Ini adalah situasi yang agak unik. Dalam kebanyakan kes, tiada pangkalan data komprehensif tentang fail mana yang sudah dipelihara dengan baik oleh rakan-rakan lanun. Ini sendiri adalah peluang besar untuk seseorang di luar sana. Ia akan menjadi hebat untuk mempunyai gambaran keseluruhan yang dikemas kini secara berkala tentang perkara seperti muzik dan filem yang sudah banyak disemai di laman web torrent, dan oleh itu keutamaan yang lebih rendah untuk dimasukkan dalam cermin lanun."

#, fuzzy
msgid "blog.how-to.projects.scraping"
msgstr "5. Pengikisan data"

#, fuzzy
msgid "blog.how-to.projects.scraping.text1"
msgstr "Sekarang anda sudah bersedia untuk memuat turun data secara pukal. Seperti yang disebutkan sebelum ini, pada ketika ini anda sepatutnya sudah memuat turun secara manual beberapa fail, untuk lebih memahami tingkah laku dan sekatan sasaran. Walau bagaimanapun, masih akan ada kejutan yang menanti anda sebaik sahaja anda benar-benar mula memuat turun banyak fail sekaligus."

#, fuzzy
msgid "blog.how-to.projects.scraping.text2"
msgstr "Nasihat kami di sini adalah terutamanya untuk menjadikannya mudah. Mulakan dengan hanya memuat turun beberapa fail. Anda boleh menggunakan Python, dan kemudian berkembang kepada berbilang benang. Tetapi kadang-kadang lebih mudah adalah untuk menjana fail Bash secara langsung dari pangkalan data, dan kemudian menjalankan beberapa daripadanya dalam beberapa tetingkap terminal untuk meningkatkan skala. Satu helah teknikal cepat yang patut disebut di sini ialah menggunakan OUTFILE dalam MySQL, yang boleh anda tulis di mana-mana jika anda melumpuhkan \"secure_file_priv\" dalam mysqld.cnf (dan pastikan juga untuk melumpuhkan/menggantikan AppArmor jika anda menggunakan Linux)."

#, fuzzy
msgid "blog.how-to.projects.scraping.text3"
msgstr "Kami menyimpan data pada cakera keras yang mudah. Mulakan dengan apa sahaja yang anda ada, dan berkembang perlahan-lahan. Ia boleh menjadi menakutkan untuk memikirkan tentang menyimpan ratusan TB data. Jika itu adalah situasi yang anda hadapi, hanya keluarkan subset yang baik terlebih dahulu, dan dalam pengumuman anda minta bantuan dalam menyimpan selebihnya. Jika anda ingin mendapatkan lebih banyak cakera keras sendiri, maka r/DataHoarder mempunyai beberapa sumber yang baik untuk mendapatkan tawaran yang baik."

#, fuzzy
msgid "blog.how-to.projects.scraping.text4"
msgstr "Cuba risau tentang sistem fail yang rumit. Mudah untuk terjebak dalam memasang perkara seperti ZFS. Satu butiran teknikal yang perlu disedari ialah banyak sistem fail tidak berfungsi dengan baik dengan banyak fail. Kami mendapati bahawa penyelesaian mudah adalah dengan membuat beberapa direktori, contohnya untuk julat ID yang berbeza atau awalan hash."

#, fuzzy
msgid "blog.how-to.projects.scraping.text5"
msgstr "Selepas memuat turun data, pastikan untuk memeriksa integriti fail menggunakan hash dalam metadata, jika ada."

#, fuzzy
msgid "blog.how-to.projects.distribution"
msgstr "6. Pengedaran"

#, fuzzy
msgid "blog.how-to.projects.distribution.text1"
msgstr "Anda mempunyai data, dengan itu memberikan anda pemilikan cermin lanun pertama di dunia bagi sasaran anda (kemungkinan besar). Dalam banyak cara, bahagian yang paling sukar telah berakhir, tetapi bahagian yang paling berisiko masih di hadapan anda. Lagipun, setakat ini anda telah bertindak secara senyap; terbang di bawah radar. Apa yang perlu anda lakukan ialah menggunakan VPN yang baik sepanjang masa, tidak mengisi butiran peribadi anda dalam sebarang borang (duh), dan mungkin menggunakan sesi pelayar khas (atau bahkan komputer yang berbeza)."

#, fuzzy
msgid "blog.how-to.projects.distribution.text2"
msgstr "Sekarang anda perlu mengedarkan data. Dalam kes kami, kami mula-mula ingin menyumbangkan buku-buku kembali kepada Library Genesis, tetapi kemudian dengan cepat menemui kesukaran dalam hal itu (penyusunan fiksyen vs bukan fiksyen). Jadi kami memutuskan untuk pengedaran menggunakan torrent gaya Library Genesis. Jika anda mempunyai peluang untuk menyumbang kepada projek sedia ada, maka itu boleh menjimatkan banyak masa anda. Walau bagaimanapun, tidak banyak cermin lanun yang teratur dengan baik di luar sana pada masa ini."

#, fuzzy
msgid "blog.how-to.projects.distribution.text3"
msgstr "Jadi katakan anda memutuskan untuk mengedarkan torrent sendiri. Cuba pastikan fail-fail tersebut kecil, supaya mudah dicerminkan di laman web lain. Anda kemudian perlu menyemai torrent tersebut sendiri, sambil tetap kekal tanpa nama. Anda boleh menggunakan VPN (dengan atau tanpa pemajuan port), atau membayar dengan Bitcoin yang telah dicampur untuk Seedbox. Jika anda tidak tahu apa maksud beberapa istilah tersebut, anda perlu membaca banyak, kerana penting untuk anda memahami pertukaran risiko di sini."

#, fuzzy
msgid "blog.how-to.projects.distribution.text4"
msgstr "Anda boleh menjadi hos fail torrent itu sendiri di laman web torrent sedia ada. Dalam kes kami, kami memilih untuk benar-benar menjadi hos laman web, kerana kami juga ingin menyebarkan falsafah kami dengan cara yang jelas. Anda boleh melakukannya sendiri dengan cara yang serupa (kami menggunakan Njalla untuk domain dan hosting kami, dibayar dengan Bitcoin yang telah dicampur), tetapi juga jangan ragu untuk menghubungi kami untuk membolehkan kami menjadi hos torrent anda. Kami sedang mencari untuk membina indeks komprehensif cermin lanun dari masa ke masa, jika idea ini mendapat sambutan."

#, fuzzy
msgid "blog.how-to.projects.distribution.text5"
msgstr "Mengenai pemilihan VPN, banyak telah ditulis tentang ini, jadi kami hanya akan mengulangi nasihat umum untuk memilih berdasarkan reputasi. Polisi tanpa log yang telah diuji di mahkamah dengan rekod panjang melindungi privasi adalah pilihan risiko terendah, pada pendapat kami. Perhatikan bahawa walaupun anda melakukan semuanya dengan betul, anda tidak boleh mencapai risiko sifar. Sebagai contoh, semasa menyemai torrents anda, pelaku negara yang sangat bermotivasi mungkin dapat melihat aliran data masuk dan keluar untuk pelayan VPN, dan menyimpulkan siapa anda. Atau anda boleh sahaja membuat kesilapan. Kami mungkin sudah melakukannya, dan akan melakukannya lagi. Nasib baik, negara-negara tidak begitu peduli <em>tentang</em> cetak rompak."

#, fuzzy
msgid "blog.how-to.projects.distribution.text6"
msgstr "Satu keputusan yang perlu dibuat untuk setiap projek adalah sama ada untuk menerbitkannya menggunakan identiti yang sama seperti sebelumnya, atau tidak. Jika anda terus menggunakan nama yang sama, maka kesilapan dalam keselamatan operasi dari projek sebelumnya boleh kembali menghantui anda. Tetapi menerbitkan di bawah nama yang berbeza bermakna anda tidak membina reputasi yang lebih lama. Kami memilih untuk mempunyai keselamatan operasi yang kuat dari awal supaya kami boleh terus menggunakan identiti yang sama, tetapi kami tidak akan teragak-agak untuk menerbitkan di bawah nama yang berbeza jika kami membuat kesilapan atau jika keadaan memerlukannya."

#, fuzzy
msgid "blog.how-to.projects.distribution.text7"
msgstr "Menyebarkan berita boleh menjadi rumit. Seperti yang kami katakan, ini masih merupakan komuniti niche. Kami pada asalnya menyiarkan di Reddit, tetapi benar-benar mendapat perhatian di Hacker News. Buat masa ini, cadangan kami adalah untuk menyiarkannya di beberapa tempat dan lihat apa yang berlaku. Dan sekali lagi, hubungi kami. Kami ingin menyebarkan usaha pengarkiban lanun yang lebih banyak."

#, fuzzy
msgid "blog.how-to.conclusion"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.how-to.conclusion.text1"
msgstr "Semoga ini berguna untuk pengarkib lanun yang baru bermula. Kami teruja untuk mengalu-alukan anda ke dunia ini, jadi jangan teragak-agak untuk menghubungi. Mari kita memelihara sebanyak mungkin pengetahuan dan budaya dunia, dan mencerminkannya jauh dan luas."

#, fuzzy
msgid "blog.how-to.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.title"
msgstr "Memperkenalkan Cermin Perpustakaan Lanun: Memelihara 7TB buku (yang tidak ada dalam Libgen)"

#, fuzzy
msgid "blog.introducing.text1"
msgstr "Projek ini (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>) bertujuan untuk menyumbang kepada pemeliharaan dan pembebasan pengetahuan manusia. Kami membuat sumbangan kecil dan rendah hati kami, mengikuti jejak langkah orang-orang hebat sebelum kami."

#, fuzzy
msgid "blog.introducing.text2"
msgstr "Fokus projek ini digambarkan oleh namanya:"

#, fuzzy
msgid "blog.introducing.focus.pirate"
msgstr "<strong>Lanun</strong> - Kami sengaja melanggar undang-undang hak cipta di kebanyakan negara. Ini membolehkan kami melakukan sesuatu yang entiti sah tidak dapat lakukan: memastikan buku-buku dicerminkan secara meluas."

#, fuzzy
msgid "blog.introducing.focus.library"
msgstr "<strong>Perpustakaan</strong> - Seperti kebanyakan perpustakaan, kami memberi tumpuan terutamanya kepada bahan bertulis seperti buku. Kami mungkin akan berkembang ke jenis media lain pada masa hadapan."

#, fuzzy
msgid "blog.introducing.focus.mirror"
msgstr "<strong>Cermin</strong> - Kami adalah cermin semata-mata bagi perpustakaan yang sedia ada. Kami memberi tumpuan kepada pemeliharaan, bukan untuk menjadikan buku mudah dicari dan dimuat turun (akses) atau memupuk komuniti besar orang yang menyumbang buku baru (sumber)."

#, fuzzy
msgid "blog.introducing.text3"
msgstr "Perpustakaan pertama yang kami cerminkan adalah Z-Library. Ini adalah perpustakaan yang popular (dan haram). Mereka telah mengambil koleksi Library Genesis dan menjadikannya mudah dicari. Selain itu, mereka telah menjadi sangat berkesan dalam meminta sumbangan buku baru, dengan memberi insentif kepada pengguna yang menyumbang dengan pelbagai kelebihan. Mereka pada masa ini tidak menyumbangkan buku baru ini kembali kepada Library Genesis. Dan tidak seperti Library Genesis, mereka tidak menjadikan koleksi mereka mudah dicerminkan, yang menghalang pemeliharaan yang meluas. Ini penting untuk model perniagaan mereka, kerana mereka mengenakan bayaran untuk mengakses koleksi mereka secara pukal (lebih daripada 10 buku sehari)."

#, fuzzy
msgid "blog.introducing.text4"
msgstr "Kami tidak membuat penilaian moral tentang mengenakan bayaran untuk akses pukal kepada koleksi buku haram. Tidak dapat dinafikan bahawa Z-Library telah berjaya dalam memperluas akses kepada pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bahagian kami: memastikan pemeliharaan jangka panjang koleksi peribadi ini."

#, fuzzy
msgid "blog.introducing.text5"
msgstr "Kami ingin menjemput anda untuk membantu memelihara dan membebaskan pengetahuan manusia dengan memuat turun dan menyemai torrents kami. Lihat halaman projek untuk maklumat lanjut tentang bagaimana data diatur."

#, fuzzy
msgid "blog.introducing.text6"
msgstr "Kami juga sangat mengalu-alukan anda untuk menyumbangkan idea anda tentang koleksi mana yang perlu dicerminkan seterusnya, dan bagaimana untuk melakukannya. Bersama-sama kita boleh mencapai banyak. Ini hanyalah sumbangan kecil di antara banyak yang lain. Terima kasih, untuk semua yang anda lakukan."

#, fuzzy
msgid "blog.introducing.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.introducing.footnote"
msgstr "<em>Kami tidak memautkan fail dari blog ini. Sila cari sendiri.</em>"

#, fuzzy
msgid "blog.isbndb-dump.title"
msgstr "Pembuangan ISBNdb, atau Berapa Banyak Buku Yang Dipelihara Selamanya?"

#, fuzzy
msgid "blog.isbndb-dump.tldr"
msgstr "Jika kita menghapuskan fail dari perpustakaan bayangan dengan betul, berapa peratus daripada semua buku di dunia yang telah kita pelihara?"

#, fuzzy
msgid "blog.isbndb-dump.text1"
msgstr "Dengan Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>), matlamat kami adalah untuk mengambil semua buku di dunia, dan memeliharanya selama-lamanya.<sup>1</sup> Antara torrents Z-Library kami, dan torrents asal Library Genesis, kami mempunyai 11,783,153 fail. Tetapi berapa banyak sebenarnya? Jika kita menghapuskan fail-fail tersebut dengan betul, berapa peratus daripada semua buku di dunia yang telah kita pelihara? Kami benar-benar ingin mempunyai sesuatu seperti ini:"

#, fuzzy
msgid "blog.isbndb-dump.10%"
msgstr "10% of warisan tulisan manusia dipelihara selama-lamanya"

#, fuzzy
msgid "blog.isbndb-dump.text2"
msgstr "Untuk peratusan, kita memerlukan penyebut: jumlah keseluruhan buku yang pernah diterbitkan.<sup>2</sup> Sebelum kemusnahan Google Books, seorang jurutera dalam projek itu, Leonid Taycher, <a %(booksearch_blogspot)s>mencuba untuk menganggarkan</a> nombor ini. Dia datang — secara berseloroh — dengan 129,864,880 (“sekurang-kurangnya sehingga Ahad”). Dia menganggarkan nombor ini dengan membina pangkalan data bersatu bagi semua buku di dunia. Untuk ini, dia mengumpulkan pelbagai set data dan kemudian menggabungkannya dengan pelbagai cara."

#, fuzzy
msgid "blog.isbndb-dump.text3"
msgstr "Sebagai selingan ringkas, terdapat seorang lagi yang cuba untuk mengkatalogkan semua buku di dunia: Aaron Swartz, aktivis digital yang telah meninggal dunia dan pengasas bersama Reddit.<sup>3</sup> Dia <a %(youtube)s>memulakan Open Library</a> dengan matlamat “satu halaman web untuk setiap buku yang pernah diterbitkan”, menggabungkan data dari banyak sumber yang berbeza. Dia akhirnya membayar harga tertinggi untuk kerja pemeliharaan digitalnya apabila dia didakwa kerana memuat turun kertas akademik secara besar-besaran, yang membawa kepada bunuh dirinya. Tidak perlu dikatakan, ini adalah salah satu sebab kumpulan kami menggunakan nama samaran, dan mengapa kami sangat berhati-hati. Open Library masih dijalankan secara heroik oleh orang-orang di Internet Archive, meneruskan legasi Aaron. Kami akan kembali kepada ini kemudian dalam pos ini."

#, fuzzy
msgid "blog.isbndb-dump.text4"
msgstr "Dalam catatan blog Google, Taycher menerangkan beberapa cabaran dengan menganggarkan nombor ini. Pertama, apa yang membentuk sebuah buku? Terdapat beberapa definisi yang mungkin:"

#, fuzzy
msgid "blog.isbndb-dump.maybe.copies"
msgstr "<strong>Salinan fizikal.</strong> Jelas sekali ini tidak begitu membantu, kerana mereka hanya salinan bahan yang sama. Ia akan menarik jika kita dapat memelihara semua anotasi yang dibuat orang dalam buku, seperti “coretan di tepi” Fermat yang terkenal. Tetapi malangnya, itu akan kekal sebagai impian seorang arkivis."

#, fuzzy
msgid "blog.isbndb-dump.maybe.works"
msgstr "<strong>“Karya”.</strong> Sebagai contoh “Harry Potter and the Chamber of Secrets” sebagai konsep logik, merangkumi semua versinya, seperti terjemahan dan cetakan semula yang berbeza. Ini adalah definisi yang agak berguna, tetapi boleh menjadi sukar untuk menarik garis apa yang dikira. Sebagai contoh, kita mungkin mahu memelihara terjemahan yang berbeza, walaupun cetakan semula dengan hanya perbezaan kecil mungkin tidak begitu penting."

#, fuzzy
msgid "blog.isbndb-dump.maybe.editions"
msgstr "<strong>“Edisi”.</strong> Di sini anda mengira setiap versi unik buku. Jika ada apa-apa yang berbeza mengenainya, seperti kulit yang berbeza atau kata pengantar yang berbeza, ia dikira sebagai edisi yang berbeza."

#, fuzzy
msgid "blog.isbndb-dump.maybe.files"
msgstr "<strong>Fail.</strong> Apabila bekerja dengan perpustakaan bayangan seperti Library Genesis, Sci-Hub, atau Z-Library, terdapat pertimbangan tambahan. Terdapat beberapa imbasan edisi yang sama. Dan orang boleh membuat versi yang lebih baik dari fail sedia ada, dengan mengimbas teks menggunakan OCR, atau membetulkan halaman yang diimbas pada sudut. Kami mahu hanya mengira fail-fail ini sebagai satu edisi, yang memerlukan metadata yang baik, atau deduplikasi menggunakan ukuran kesamaan dokumen."

#, fuzzy
msgid "blog.isbndb-dump.text5"
msgstr "“Edisi” nampaknya adalah definisi yang paling praktikal untuk apa yang dimaksudkan dengan “buku”. Secara kebetulan, definisi ini juga digunakan untuk memberikan nombor ISBN yang unik. ISBN, atau Nombor Buku Standard Antarabangsa, biasanya digunakan untuk perdagangan antarabangsa, kerana ia diintegrasikan dengan sistem kod bar antarabangsa (”Nombor Artikel Antarabangsa”). Jika anda ingin menjual buku di kedai, ia memerlukan kod bar, jadi anda perlu mendapatkan ISBN."

#, fuzzy
msgid "blog.isbndb-dump.text6"
msgstr "Catatan blog Taycher menyebut bahawa walaupun ISBN berguna, ia tidak bersifat universal, kerana ia hanya benar-benar diterima pakai pada pertengahan tahun tujuh puluhan, dan tidak di seluruh dunia. Namun, ISBN mungkin adalah pengenal pasti edisi buku yang paling banyak digunakan, jadi ia adalah titik permulaan terbaik kita. Jika kita dapat mencari semua ISBN di dunia, kita akan mendapat senarai berguna tentang buku mana yang masih perlu dipelihara."

#, fuzzy
msgid "blog.isbndb-dump.text7"
msgstr "Jadi, di mana kita mendapatkan data? Terdapat beberapa usaha sedia ada yang cuba menyusun senarai semua buku di dunia:"

#, fuzzy
msgid "blog.isbndb-dump.effort.google"
msgstr "<strong>Google.</strong> Lagipun, mereka telah melakukan penyelidikan ini untuk Google Books. Walau bagaimanapun, metadata mereka tidak boleh diakses secara pukal dan agak sukar untuk diambil."

#, fuzzy
msgid "blog.isbndb-dump.effort.openlib"
msgstr "<strong>Open Library.</strong> Seperti yang disebutkan sebelum ini, ini adalah misi utama mereka. Mereka telah mendapatkan sejumlah besar data perpustakaan dari perpustakaan yang bekerjasama dan arkib nasional, dan terus melakukannya. Mereka juga mempunyai pustakawan sukarela dan pasukan teknikal yang cuba menghapuskan rekod yang berulang, dan menandainya dengan pelbagai jenis metadata. Yang terbaik, set data mereka adalah sepenuhnya terbuka. Anda boleh <a %(openlibrary)s>muat turunnya</a> dengan mudah."

#, fuzzy
msgid "blog.isbndb-dump.effort.worldcat"
msgstr "<strong>WorldCat.</strong> Ini adalah laman web yang dikendalikan oleh OCLC bukan untung, yang menjual sistem pengurusan perpustakaan. Mereka mengumpulkan metadata buku dari banyak perpustakaan, dan menjadikannya tersedia melalui laman web WorldCat. Walau bagaimanapun, mereka juga menjana wang dengan menjual data ini, jadi ia tidak tersedia untuk muat turun pukal. Mereka mempunyai beberapa set data pukal yang lebih terhad yang tersedia untuk muat turun, dengan kerjasama perpustakaan tertentu."

#, fuzzy
msgid "blog.isbndb-dump.effort.isbndb"
msgstr "<strong>ISBNdb.</strong> Ini adalah topik catatan blog ini. ISBNdb mengikis pelbagai laman web untuk metadata buku, khususnya data harga, yang kemudian mereka jual kepada penjual buku, supaya mereka dapat menetapkan harga buku mereka selaras dengan pasaran. Memandangkan ISBN agak universal pada masa kini, mereka secara efektif membina \"halaman web untuk setiap buku\"."

#, fuzzy
msgid "blog.isbndb-dump.effort.ils"
msgstr "<strong>Pelbagai sistem perpustakaan individu dan arkib.</strong> Terdapat perpustakaan dan arkib yang belum diindeks dan digabungkan oleh mana-mana yang di atas, selalunya kerana mereka kekurangan dana, atau atas sebab lain tidak mahu berkongsi data mereka dengan Open Library, OCLC, Google, dan sebagainya. Banyak daripada ini mempunyai rekod digital yang boleh diakses melalui internet, dan mereka sering kali tidak dilindungi dengan baik, jadi jika anda ingin membantu dan berseronok belajar tentang sistem perpustakaan yang pelik, ini adalah titik permulaan yang hebat."

#, fuzzy
msgid "blog.isbndb-dump.text8"
msgstr "Dalam pos ini, kami gembira untuk mengumumkan pelepasan kecil (berbanding dengan pelepasan Z-Library kami sebelum ini). Kami telah mengikis kebanyakan ISBNdb, dan menjadikan data tersebut tersedia untuk torrenting di laman web Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>; kami tidak akan memautkannya di sini secara langsung, hanya cari sahaja). Ini adalah kira-kira 30.9 juta rekod (20GB sebagai <a %(jsonlines)s>JSON Lines</a>; 4.4GB dimampatkan). Di laman web mereka, mereka mendakwa bahawa mereka sebenarnya mempunyai 32.6 juta rekod, jadi kami mungkin terlepas beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Walau bagaimanapun, buat masa ini kami tidak akan berkongsi dengan tepat bagaimana kami melakukannya — kami akan meninggalkannya sebagai latihan untuk pembaca. ;-)"

#, fuzzy
msgid "blog.isbndb-dump.text9"
msgstr "Apa yang akan kami kongsikan adalah beberapa analisis awal, untuk cuba mendekati anggaran jumlah buku di dunia. Kami melihat tiga set data: set data ISBNdb baru ini, pelepasan metadata asal kami yang kami kikis dari perpustakaan bayangan Z-Library (yang termasuk Library Genesis), dan pembuangan data Open Library."

#, fuzzy
msgid "blog.isbndb-dump.text10"
msgstr "Mari kita mulakan dengan beberapa angka kasar:"

#, fuzzy
msgid "blog.isbndb-dump.text11"
msgstr "Dalam kedua-dua Z-Library/Libgen dan Open Library terdapat lebih banyak buku daripada ISBN unik. Adakah itu bermakna banyak buku tersebut tidak mempunyai ISBN, atau adakah metadata ISBN hanya hilang? Kami mungkin boleh menjawab soalan ini dengan gabungan pemadanan automatik berdasarkan atribut lain (tajuk, pengarang, penerbit, dll), menarik lebih banyak sumber data, dan mengekstrak ISBN dari imbasan buku sebenar (dalam kes Z-Library/Libgen)."

#, fuzzy
msgid "blog.isbndb-dump.text12"
msgstr "Berapa banyak daripada ISBN tersebut yang unik? Ini paling baik digambarkan dengan diagram Venn:"

#, fuzzy
msgid "blog.isbndb-dump.text13"
msgstr "Untuk lebih tepat:"

#, fuzzy
msgid "blog.isbndb-dump.text14"
msgstr "Kami terkejut dengan betapa sedikitnya pertindihan yang ada! ISBNdb mempunyai sejumlah besar ISBN yang tidak muncul sama ada dalam Z-Library atau Open Library, dan perkara yang sama berlaku (walaupun pada tahap yang lebih kecil tetapi masih ketara) untuk kedua-dua yang lain. Ini menimbulkan banyak persoalan baru. Sejauh mana padanan automatik akan membantu dalam menandakan buku yang tidak ditandakan dengan ISBN? Adakah akan terdapat banyak padanan dan oleh itu peningkatan pertindihan? Juga, apa yang akan berlaku jika kita membawa masuk dataset ke-4 atau ke-5? Berapa banyak pertindihan yang akan kita lihat kemudian?"

#, fuzzy
msgid "blog.isbndb-dump.text15"
msgstr "Ini memberi kita titik permulaan. Kita kini boleh melihat semua ISBN yang tidak terdapat dalam dataset Z-Library, dan yang tidak sepadan dengan medan tajuk/pengarang juga. Itu boleh memberi kita pegangan untuk memelihara semua buku di dunia: pertama dengan mengikis internet untuk imbasan, kemudian dengan keluar dalam kehidupan sebenar untuk mengimbas buku. Yang terakhir ini boleh dibiayai oleh orang ramai, atau didorong oleh \"ganjaran\" daripada orang yang ingin melihat buku tertentu didigitalkan. Semua itu adalah cerita untuk masa yang lain."

#, fuzzy
msgid "blog.isbndb-dump.text16"
msgstr "Jika anda ingin membantu dengan mana-mana perkara ini — analisis lanjut; mengikis lebih banyak metadata; mencari lebih banyak buku; OCR buku; melakukan ini untuk domain lain (contohnya kertas, buku audio, filem, rancangan TV, majalah) atau bahkan menjadikan sebahagian daripada data ini tersedia untuk perkara seperti latihan model bahasa besar / ML — sila hubungi saya (<a %(reddit)s>Reddit</a>)."

#, fuzzy
msgid "blog.isbndb-dump.text17"
msgstr "Jika anda khususnya berminat dalam analisis data, kami sedang berusaha untuk menjadikan dataset dan skrip kami tersedia dalam format yang lebih mudah digunakan. Ia akan menjadi hebat jika anda boleh hanya fork sebuah notebook dan mula bermain dengan ini."

#, fuzzy
msgid "blog.isbndb-dump.text18"
msgstr "Akhirnya, jika anda ingin menyokong kerja ini, sila pertimbangkan untuk membuat sumbangan. Ini adalah operasi yang dijalankan sepenuhnya oleh sukarelawan, dan sumbangan anda membuat perbezaan yang besar. Setiap sedikit membantu. Buat masa ini kami menerima sumbangan dalam bentuk kripto; lihat halaman Derma di Arkib Anna."

#, fuzzy
msgid "blog.isbndb-dump.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>)"

#, fuzzy
msgid "blog.isbndb-dump.fn1"
msgstr "1. Untuk beberapa definisi \"selama-lamanya\" yang munasabah. ;)"

#, fuzzy
msgid "blog.isbndb-dump.fn2"
msgstr "2. Sudah tentu, warisan tulisan manusia adalah lebih daripada sekadar buku, terutamanya pada masa kini. Demi pos ini dan keluaran terbaru kami, kami memberi tumpuan kepada buku, tetapi minat kami melangkaui itu."

#, fuzzy
msgid "blog.isbndb-dump.fn3"
msgstr "3. Terdapat banyak lagi yang boleh dikatakan tentang Aaron Swartz, tetapi kami hanya ingin menyebutnya secara ringkas, kerana dia memainkan peranan penting dalam cerita ini. Apabila masa berlalu, lebih ramai orang mungkin menemui namanya buat kali pertama, dan seterusnya boleh menyelami lubang arnab itu sendiri."

#, fuzzy
msgid "blog.critical-window.title"
msgstr "Tingkap kritikal perpustakaan bayangan"

#, fuzzy
msgid "blog.critical-window.tldr"
msgstr "Bagaimana kita boleh mendakwa untuk memelihara koleksi kita selama-lamanya, apabila ia sudah menghampiri 1 PB?"

#, fuzzy
msgid "blog.critical-window.links"
msgstr "<a %(critical_window_chinese)s>Versi Cina 中文版</a>, bincangkan di <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a>"

#, fuzzy
msgid "blog.critical-window.text1"
msgstr "Di Arkib Anna, kami sering ditanya bagaimana kami boleh mendakwa untuk memelihara koleksi kami selama-lamanya, apabila saiz keseluruhan sudah menghampiri 1 Petabyte (1000 TB), dan masih berkembang. Dalam artikel ini, kami akan melihat falsafah kami, dan melihat mengapa dekad seterusnya adalah kritikal untuk misi kami memelihara pengetahuan dan budaya manusia."

#, fuzzy
msgid "blog.critical-window.fig1"
msgstr "<a %(annas_archive_stats)s>Saiz keseluruhan</a> koleksi kami, sepanjang beberapa bulan lepas, dipecahkan mengikut bilangan penanam torrent."

#, fuzzy
msgid "blog.critical-window.priorities"
msgstr "Keutamaan"

#, fuzzy
msgid "blog.critical-window.priorities.text1"
msgstr "Mengapa kita begitu peduli tentang kertas dan buku? Mari kita ketepikan kepercayaan asas kita dalam pemeliharaan secara umum — kita mungkin menulis pos lain tentang itu. Jadi mengapa kertas dan buku secara khusus? Jawapannya mudah: <strong>ketumpatan maklumat</strong>."

#, fuzzy
msgid "blog.critical-window.priorities.text2"
msgstr "Setiap megabait storan, teks bertulis menyimpan maklumat paling banyak daripada semua media. Walaupun kita peduli tentang kedua-dua pengetahuan dan budaya, kita lebih peduli tentang yang pertama. Secara keseluruhan, kita mendapati hierarki ketumpatan maklumat dan kepentingan pemeliharaan yang kelihatan kira-kira seperti ini:"

#, fuzzy
msgid "blog.critical-window.priorities.order.papers"
msgstr "Kertas akademik, jurnal, laporan"

#, fuzzy
msgid "blog.critical-window.priorities.order.organic"
msgstr "Data organik seperti urutan DNA, biji tumbuhan, atau sampel mikrob"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-books"
msgstr "Buku bukan fiksyen"

#, fuzzy
msgid "blog.critical-window.priorities.order.code"
msgstr "Kod perisian sains & kejuruteraan"

#, fuzzy
msgid "blog.critical-window.priorities.order.measurements"
msgstr "Data pengukuran seperti pengukuran saintifik, data ekonomi, laporan korporat"

#, fuzzy
msgid "blog.critical-window.priorities.order.science-websites"
msgstr "Laman web sains & kejuruteraan, perbincangan dalam talian"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-other"
msgstr "Majalah bukan fiksyen, surat khabar, manual"

#, fuzzy
msgid "blog.critical-window.priorities.order.nonfiction-transcripts"
msgstr "Transkrip bukan fiksyen daripada ceramah, dokumentari, podcast"

#, fuzzy
msgid "blog.critical-window.priorities.order.leaks"
msgstr "Data dalaman daripada korporat atau kerajaan (bocor)"

#, fuzzy
msgid "blog.critical-window.priorities.order.metadata"
msgstr "Rekod metadata secara umum (bukan fiksyen dan fiksyen; media lain, seni, orang, dll; termasuk ulasan)"

#, fuzzy
msgid "blog.critical-window.priorities.order.geographic"
msgstr "Data geografi (contohnya peta, kajian geologi)"

#, fuzzy
msgid "blog.critical-window.priorities.order.transcripts"
msgstr "Transkrip prosiding undang-undang atau mahkamah"

#, fuzzy
msgid "blog.critical-window.priorities.order.fiction"
msgstr "Versi fiksyen atau hiburan bagi semua perkara di atas"

#, fuzzy
msgid "blog.critical-window.priorities.text3"
msgstr "Kedudukan dalam senarai ini agak sewenang-wenangnya — beberapa item adalah seri atau terdapat ketidaksetujuan dalam pasukan kami — dan kami mungkin terlupa beberapa kategori penting. Tetapi ini adalah secara kasar bagaimana kami memprioritaskan."

#, fuzzy
msgid "blog.critical-window.priorities.text4"
msgstr "Beberapa item ini terlalu berbeza daripada yang lain untuk kami bimbangkan (atau sudah diuruskan oleh institusi lain), seperti data organik atau data geografi. Tetapi kebanyakan item dalam senarai ini sebenarnya penting bagi kami."

#, fuzzy
msgid "blog.critical-window.priorities.text5"
msgstr "Faktor besar lain dalam pemprioritasan kami adalah sejauh mana risiko sesuatu karya. Kami lebih suka memberi tumpuan kepada karya yang:"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.rare"
msgstr "Jarang"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.underfocused"
msgstr "Unik tidak diberi tumpuan"

#, fuzzy
msgid "blog.critical-window.priorities.rarity.at-risk"
msgstr "Unik berisiko dimusnahkan (contohnya oleh perang, pemotongan dana, tuntutan mahkamah, atau penganiayaan politik)"

#, fuzzy
msgid "blog.critical-window.priorities.text6"
msgstr "Akhirnya, kami mengambil berat tentang skala. Kami mempunyai masa dan wang yang terhad, jadi kami lebih suka menghabiskan sebulan menyelamatkan 10,000 buku daripada 1,000 buku — jika mereka sama-sama berharga dan berisiko."

#, fuzzy
msgid "blog.critical-window.shadowlib"
msgstr "Perpustakaan bayangan"

#, fuzzy
msgid "blog.critical-window.shadowlib.text1"
msgstr "Terdapat banyak organisasi yang mempunyai misi dan keutamaan yang serupa. Memang, terdapat perpustakaan, arkib, makmal, muzium, dan institusi lain yang ditugaskan untuk pemeliharaan jenis ini. Banyak daripada mereka dibiayai dengan baik, oleh kerajaan, individu, atau syarikat. Tetapi mereka mempunyai satu titik buta besar: sistem undang-undang."

#, fuzzy
msgid "blog.critical-window.shadowlib.text2"
msgstr "Di sinilah terletaknya peranan unik perpustakaan bayangan, dan sebab Arkib Anna wujud. Kami boleh melakukan perkara yang tidak dibenarkan oleh institusi lain. Sekarang, bukan (selalunya) bahawa kami boleh mengarkibkan bahan yang haram untuk dipelihara di tempat lain. Tidak, adalah sah di banyak tempat untuk membina arkib dengan sebarang buku, kertas, majalah, dan sebagainya."

#, fuzzy
msgid "blog.critical-window.shadowlib.text3"
msgstr "Tetapi apa yang sering kurang dalam arkib sah adalah <strong>redundansi dan jangka hayat</strong>. Terdapat buku yang hanya satu salinan wujud di beberapa perpustakaan fizikal di suatu tempat. Terdapat rekod metadata yang dijaga oleh satu syarikat sahaja. Terdapat surat khabar yang hanya dipelihara pada mikrofilem dalam satu arkib. Perpustakaan boleh mengalami pemotongan dana, syarikat boleh muflis, arkib boleh dibom dan dibakar hingga musnah. Ini bukan hipotesis — ini berlaku sepanjang masa."

#, fuzzy
msgid "blog.critical-window.shadowlib.text4"
msgstr "Perkara yang kami boleh lakukan secara unik di Arkib Anna adalah menyimpan banyak salinan karya, pada skala besar. Kami boleh mengumpul kertas, buku, majalah, dan banyak lagi, dan mengedarkannya secara pukal. Kami kini melakukan ini melalui torrent, tetapi teknologi yang tepat tidak penting dan akan berubah dari masa ke masa. Bahagian penting adalah mendapatkan banyak salinan diedarkan di seluruh dunia. Petikan ini dari lebih 200 tahun yang lalu masih relevan:"

#, fuzzy
msgid "blog.critical-window.quote.the-lost"
msgstr "<em><q>Yang hilang tidak dapat dipulihkan; tetapi mari kita selamatkan apa yang tinggal: bukan dengan peti besi dan kunci yang menghalang mereka dari pandangan dan penggunaan awam, dengan menyerahkan mereka kepada pembaziran masa, tetapi dengan penggandaan salinan, yang akan meletakkan mereka di luar jangkauan kemalangan.</q></em><br>— Thomas Jefferson, 1791"

#, fuzzy
msgid "blog.critical-window.shadowlib.text5"
msgstr "Nota ringkas tentang domain awam. Oleh kerana Arkib Anna secara unik menumpukan pada aktiviti yang menyalahi undang-undang di banyak tempat di seluruh dunia, kami tidak peduli dengan koleksi yang tersedia secara meluas, seperti buku domain awam. Entiti sah sering kali sudah menjaga hal itu dengan baik. Walau bagaimanapun, terdapat pertimbangan yang membuatkan kami kadang-kadang bekerja pada koleksi yang tersedia secara umum:"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.metadata"
msgstr "Rekod metadata boleh dilihat secara bebas di laman web Worldcat, tetapi tidak boleh dimuat turun secara pukal (sehingga kami <a %(worldcat_scrape)s>mengikis</a> mereka)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.github"
msgstr "Kod boleh menjadi sumber terbuka di Github, tetapi Github secara keseluruhan tidak boleh dicermin dengan mudah dan dengan itu dipelihara (walaupun dalam kes ini terdapat salinan yang cukup diedarkan bagi kebanyakan repositori kod)"

#, fuzzy
msgid "blog.critical-window.shadowlib.example.reddit"
msgstr "Reddit boleh digunakan secara percuma, tetapi baru-baru ini telah meletakkan langkah anti-pengikisan yang ketat, selepas kelaparan data untuk latihan LLM (lebih lanjut tentang itu kemudian)"

#, fuzzy
msgid "blog.critical-window.copies"
msgstr "Penggandaan salinan"

#, fuzzy
msgid "blog.critical-window.copies.text1"
msgstr "Kembali kepada soalan asal kami: bagaimana kami boleh mendakwa untuk memelihara koleksi kami selama-lamanya? Masalah utama di sini adalah bahawa koleksi kami telah <a %(torrents_stats)s>berkembang</a> dengan pesat, dengan mengikis dan membuka sumber beberapa koleksi besar (di atas kerja menakjubkan yang sudah dilakukan oleh perpustakaan bayangan data terbuka lain seperti Sci-Hub dan Library Genesis)."

#, fuzzy
msgid "blog.critical-window.copies.text2"
msgstr "Pertumbuhan data ini menjadikannya lebih sukar untuk koleksi dicermin di seluruh dunia. Penyimpanan data mahal! Tetapi kami optimis, terutamanya apabila memerhatikan tiga trend berikut."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit"
msgstr "1. Kami telah memetik buah yang rendah"

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text1"
msgstr "Ini mengikuti secara langsung dari keutamaan kami yang dibincangkan di atas. Kami lebih suka bekerja untuk membebaskan koleksi besar terlebih dahulu. Sekarang bahawa kami telah mengamankan beberapa koleksi terbesar di dunia, kami menjangkakan pertumbuhan kami akan jauh lebih perlahan."

#, fuzzy
msgid "blog.critical-window.low-hanging-fruit.text2"
msgstr "Masih terdapat ekor panjang koleksi yang lebih kecil, dan buku baru diimbas atau diterbitkan setiap hari, tetapi kadar itu mungkin akan jauh lebih perlahan. Kami mungkin masih berganda atau bahkan tiga kali ganda dalam saiz, tetapi dalam tempoh masa yang lebih lama."

#, fuzzy
msgid "blog.critical-window.storage"
msgstr "2. Kos penyimpanan terus menurun secara eksponen"

#, fuzzy
msgid "blog.critical-window.storage.text1"
msgstr "Pada masa penulisan ini, <a %(diskprices)s>harga cakera</a> per TB adalah sekitar $12 untuk cakera baru, $8 untuk cakera terpakai, dan $4 untuk pita. Jika kita bersikap konservatif dan hanya melihat cakera baru, ini bermakna menyimpan satu petabait berharga sekitar $12,000. Jika kita anggarkan perpustakaan kita akan meningkat tiga kali ganda dari 900TB ke 2.7PB, ini bermakna $32,400 untuk mencermin seluruh perpustakaan kita. Menambah kos elektrik, kos perkakasan lain, dan sebagainya, mari kita bulatkan kepada $40,000. Atau dengan pita lebih kurang $15,000–$20,000."

#, fuzzy
msgid "blog.critical-window.storage.text2"
msgstr "Di satu pihak <strong>$15,000–$40,000 untuk jumlah semua pengetahuan manusia adalah sangat murah</strong>. Di pihak lain, agak mahal untuk mengharapkan banyak salinan penuh, terutamanya jika kita juga ingin orang-orang tersebut terus menyemai torrent mereka untuk manfaat orang lain."

#, fuzzy
msgid "blog.critical-window.storage.text3"
msgstr "Itu adalah hari ini. Tetapi kemajuan terus bergerak ke hadapan:"

#, fuzzy
msgid "blog.critical-window.storage.text4"
msgstr "Kos cakera keras per TB telah dikurangkan kira-kira satu pertiga dalam 10 tahun terakhir, dan mungkin akan terus menurun pada kadar yang sama. Pita nampaknya berada pada trajektori yang sama. Harga SSD menurun lebih cepat, dan mungkin akan mengatasi harga HDD menjelang akhir dekad ini."

#, fuzzy
msgid "blog.critical-window.hdd-prices"
msgstr "Trend harga HDD dari pelbagai sumber (klik untuk melihat kajian)."

#, fuzzy
msgid "blog.critical-window.storage.text5"
msgstr "Jika ini berterusan, maka dalam 10 tahun kita mungkin hanya melihat $5,000–$13,000 untuk mencermin seluruh koleksi kita (1/3), atau lebih kurang jika kita berkembang kurang dalam saiz. Walaupun masih banyak wang, ini akan dapat dicapai oleh ramai orang. Dan mungkin lebih baik lagi kerana perkara seterusnya…"

#, fuzzy
msgid "blog.critical-window.storage.density"
msgstr "3. Peningkatan dalam ketumpatan maklumat"

#, fuzzy
msgid "blog.critical-window.storage.density.text1"
msgstr "Kami kini menyimpan buku dalam format mentah yang diberikan kepada kami. Memang, ia dimampatkan, tetapi selalunya ia masih imbasan besar atau gambar halaman."

#, fuzzy
msgid "blog.critical-window.storage.density.text2"
msgstr "Sehingga kini, satu-satunya pilihan untuk mengecilkan saiz keseluruhan koleksi kami adalah melalui pemampatan yang lebih agresif, atau deduplikasi. Walau bagaimanapun, untuk mendapatkan penjimatan yang cukup besar, kedua-duanya terlalu banyak kehilangan untuk citarasa kami. Pemampatan berat foto boleh menjadikan teks hampir tidak boleh dibaca. Dan deduplikasi memerlukan keyakinan tinggi bahawa buku-buku adalah sama, yang selalunya terlalu tidak tepat, terutamanya jika kandungannya sama tetapi imbasan dibuat pada masa yang berbeza."

#, fuzzy
msgid "blog.critical-window.storage.density.text3"
msgstr "Sentiasa ada pilihan ketiga, tetapi kualitinya sangat buruk sehingga kami tidak pernah mempertimbangkannya: <strong>OCR, atau Pengecaman Aksara Optik</strong>. Ini adalah proses menukar foto menjadi teks biasa, dengan menggunakan AI untuk mengesan aksara dalam foto. Alat untuk ini telah lama wujud, dan agak baik, tetapi \"agak baik\" tidak mencukupi untuk tujuan pemeliharaan."

#, fuzzy
msgid "blog.critical-window.storage.density.text4"
msgstr "Walau bagaimanapun, model pembelajaran mendalam multi-modal baru-baru ini telah membuat kemajuan yang sangat pesat, walaupun masih pada kos yang tinggi. Kami menjangkakan ketepatan dan kos akan meningkat dengan ketara dalam tahun-tahun akan datang, sehingga menjadi realistik untuk diterapkan pada seluruh perpustakaan kami."

#, fuzzy
msgid "blog.critical-window.ocr"
msgstr "Peningkatan OCR."

#, fuzzy
msgid "blog.critical-window.storage.density.text5"
msgstr "Apabila itu berlaku, kami mungkin masih akan memelihara fail asal, tetapi sebagai tambahan kami boleh mempunyai versi perpustakaan yang lebih kecil yang kebanyakan orang akan mahu cermin. Yang menarik adalah bahawa teks mentah itu sendiri dimampatkan dengan lebih baik, dan lebih mudah untuk dideduplikasi, memberikan kami lebih banyak penjimatan."

#, fuzzy
msgid "blog.critical-window.storage.density.text6"
msgstr "Secara keseluruhan, tidak mustahil untuk mengharapkan sekurang-kurangnya pengurangan 5-10x dalam saiz fail keseluruhan, mungkin lebih. Walaupun dengan pengurangan konservatif 5x, kita akan melihat <strong>$1,000–$3,000 dalam 10 tahun walaupun perpustakaan kita meningkat tiga kali ganda dalam saiz</strong>."

#, fuzzy
msgid "blog.critical-window.the-window"
msgstr "Tingkap kritikal"

#, fuzzy
msgid "blog.critical-window.the-window.text1"
msgstr "Jika ramalan ini tepat, kita <strong>hanya perlu menunggu beberapa tahun</strong> sebelum seluruh koleksi kita akan dicermin secara meluas. Oleh itu, dalam kata-kata Thomas Jefferson, \"diletakkan di luar jangkauan kemalangan.\""

#, fuzzy
msgid "blog.critical-window.the-window.text2"
msgstr "Malangnya, kemunculan LLM, dan latihan data yang memerlukan banyak data, telah membuat banyak pemegang hak cipta bersikap defensif. Lebih daripada yang mereka sudah lakukan. Banyak laman web membuatnya lebih sukar untuk mengikis dan mengarkib, tuntutan mahkamah berterbangan, dan sementara itu perpustakaan fizikal dan arkib terus diabaikan."

#, fuzzy
msgid "blog.critical-window.the-window.text3"
msgstr "Kita hanya boleh mengharapkan trend ini terus memburuk, dan banyak karya hilang sebelum mereka memasuki domain awam."

#, fuzzy
msgid "blog.critical-window.the-window.text4"
msgstr "<strong>Kita berada di ambang revolusi dalam pemeliharaan, tetapi <q>yang hilang tidak dapat dipulihkan.</q></strong> Kita mempunyai tingkap kritikal sekitar 5-10 tahun di mana ia masih agak mahal untuk mengendalikan perpustakaan bayangan dan mencipta banyak cermin di seluruh dunia, dan di mana akses belum sepenuhnya ditutup."

#, fuzzy
msgid "blog.critical-window.the-window.text5"
msgstr "Jika kita dapat merapatkan jurang ini, maka kita benar-benar telah memelihara pengetahuan dan budaya manusia untuk selama-lamanya. Kita tidak seharusnya membiarkan masa ini terbuang sia-sia. Kita tidak seharusnya membiarkan peluang kritikal ini tertutup kepada kita."

#, fuzzy
msgid "blog.critical-window.the-window.text6"
msgstr "Mari kita pergi."

#, fuzzy
msgid "blog.critical-window.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.duxiu-exclusive.title"
msgstr "Akses eksklusif untuk syarikat LLM kepada koleksi buku bukan fiksyen Cina terbesar di dunia"

#, fuzzy
msgid "blog.duxiu-exclusive.subtitle"
msgstr "<a %(duxiu_exclusive_chinese)s>Versi Cina 中文版</a>, <a %(news_ycombinator)s>Bincangkan di Hacker News</a>"

#, fuzzy
msgid "blog.duxiu-exclusive.tldr"
msgstr "<em><strong>TL;DR:</strong> Arkib Anna memperoleh koleksi unik 7.5 juta / 350TB buku bukan fiksyen Cina — lebih besar daripada Library Genesis. Kami bersedia memberikan akses eksklusif kepada syarikat LLM, sebagai pertukaran untuk OCR berkualiti tinggi dan pengekstrakan teks.</em>"

#, fuzzy
msgid "blog.duxiu-exclusive.text1"
msgstr "Ini adalah catatan blog ringkas. Kami sedang mencari syarikat atau institusi untuk membantu kami dengan OCR dan pengekstrakan teks untuk koleksi besar yang kami peroleh, sebagai pertukaran untuk akses awal eksklusif. Selepas tempoh embargo, kami tentunya akan melepaskan keseluruhan koleksi."

#, fuzzy
msgid "blog.duxiu-exclusive.text2"
msgstr "Teks akademik berkualiti tinggi sangat berguna untuk latihan LLM. Walaupun koleksi kami adalah dalam bahasa Cina, ini seharusnya berguna juga untuk latihan LLM bahasa Inggeris: model nampaknya menyandikan konsep dan pengetahuan tanpa mengira bahasa sumber."

#, fuzzy
msgid "blog.duxiu-exclusive.text3"
msgstr "Untuk ini, teks perlu diekstrak dari imbasan. Apa yang Arkib Anna dapat daripadanya? Carian teks penuh buku untuk penggunanya."

#, fuzzy
msgid "blog.duxiu-exclusive.text4"
msgstr "Kerana matlamat kami selari dengan pembangun LLM, kami sedang mencari rakan kerjasama. Kami bersedia memberikan anda <strong>akses awal eksklusif kepada koleksi ini secara pukal selama 1 tahun</strong>, jika anda dapat melakukan OCR dan pengekstrakan teks yang betul. Jika anda bersedia berkongsi keseluruhan kod saluran anda dengan kami, kami bersedia untuk melanjutkan tempoh embargo koleksi."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages"
msgstr "Halaman contoh"

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text1"
msgstr "Untuk membuktikan kepada kami bahawa anda mempunyai saluran yang baik, berikut adalah beberapa halaman contoh untuk memulakan, dari sebuah buku mengenai superkonduktor. Saluran anda seharusnya dapat mengendalikan matematik, jadual, carta, nota kaki, dan sebagainya dengan betul."

#, fuzzy
msgid "blog.duxiu-exclusive.example_pages.text2"
msgstr "Hantar halaman yang telah diproses kepada e-mel kami. Jika ia kelihatan baik, kami akan menghantar lebih banyak kepada anda secara peribadi, dan kami mengharapkan anda dapat menjalankan saluran anda dengan cepat pada halaman tersebut juga. Setelah kami berpuas hati, kita boleh membuat perjanjian."

#, fuzzy
msgid "blog.duxiu-exclusive.collection"
msgstr "Koleksi"

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text1"
msgstr "Beberapa maklumat lanjut mengenai koleksi. <a %(duxiu)s>Duxiu</a> adalah pangkalan data besar buku yang diimbas, dicipta oleh <a %(chaoxing)s>SuperStar Digital Library Group</a>. Kebanyakan adalah buku akademik, diimbas untuk menjadikannya tersedia secara digital kepada universiti dan perpustakaan. Untuk penonton berbahasa Inggeris kami, <a %(library_princeton)s>Princeton</a> dan <a %(guides_lib_uw)s>University of Washington</a> mempunyai gambaran keseluruhan yang baik. Terdapat juga artikel yang sangat baik memberikan lebih banyak latar belakang: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cari di Arkib Anna)."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text2"
msgstr "Buku-buku dari Duxiu telah lama dipirate di internet Cina. Biasanya ia dijual dengan harga kurang dari satu dolar oleh penjual semula. Ia biasanya diedarkan menggunakan setara Cina Google Drive, yang sering digodam untuk membolehkan lebih banyak ruang simpanan. Beberapa butiran teknikal boleh didapati <a %(github_duty_machine)s>di sini</a> dan <a %(github_821_github_io)s>di sini</a>."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text3"
msgstr "Walaupun buku-buku telah diedarkan secara separa awam, agak sukar untuk mendapatkannya secara pukal. Kami meletakkan ini tinggi dalam senarai TODO kami, dan memperuntukkan beberapa bulan kerja sepenuh masa untuknya. Walau bagaimanapun, baru-baru ini seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberitahu kami bahawa mereka telah melakukan semua kerja ini — dengan kos yang besar. Mereka berkongsi keseluruhan koleksi dengan kami, tanpa mengharapkan apa-apa balasan, kecuali jaminan pemeliharaan jangka panjang. Benar-benar luar biasa. Mereka bersetuju untuk meminta bantuan dengan cara ini untuk mendapatkan koleksi di-OCR."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text4"
msgstr "Koleksi ini terdiri daripada 7,543,702 fail. Ini lebih banyak daripada Library Genesis bukan fiksyen (sekitar 5.3 juta). Jumlah saiz fail adalah sekitar 359TB (326TiB) dalam bentuk semasa."

#, fuzzy
msgid "blog.duxiu-exclusive.collection.text5"
msgstr "Kami terbuka kepada cadangan dan idea lain. Hubungi kami sahaja. Lihat Arkib Anna untuk maklumat lanjut mengenai koleksi kami, usaha pemeliharaan, dan bagaimana anda boleh membantu. Terima kasih!"

#, fuzzy
msgid "blog.duxiu-exclusive.signoff"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.zlib-on-ipfs.deprecated"
msgstr "Amaran: catatan blog ini telah dihentikan. Kami telah memutuskan bahawa IPFS belum bersedia untuk digunakan secara meluas. Kami masih akan memautkan fail pada IPFS dari Arkib Anna apabila mungkin, tetapi kami tidak akan menjadi hosnya sendiri lagi, dan kami juga tidak mengesyorkan orang lain untuk mencerminkannya menggunakan IPFS. Sila lihat halaman Torrents kami jika anda ingin membantu memelihara koleksi kami."

#, fuzzy
msgid "blog.zlib-on-ipfs.title"
msgstr "Bantu sebar Z-Library di IPFS"

#, fuzzy
msgid "blog.how-to-run.title"
msgstr "Cara menjalankan perpustakaan bayangan: operasi di Arkib Anna"

#, fuzzy
msgid "blog.how-to-run.tldr"
msgstr "Tiada <q>AWS untuk badan amal bayangan,</q> jadi bagaimana kami menjalankan Arkib Anna?"

#, fuzzy
msgid "blog.how-to-run.text1"
msgstr "Saya menjalankan <a %(wikipedia_annas_archive)s>Arkib Anna</a>, enjin carian sumber terbuka bukan untung terbesar di dunia untuk <a %(wikipedia_shadow_library)s>perpustakaan bayangan</a>, seperti Sci-Hub, Library Genesis, dan Z-Library. Matlamat kami adalah untuk menjadikan pengetahuan dan budaya mudah diakses, dan akhirnya membina komuniti orang yang bersama-sama mengarkib dan memelihara <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>semua buku di dunia</a>."

#, fuzzy
msgid "blog.how-to-run.text2"
msgstr "Dalam artikel ini saya akan menunjukkan bagaimana kami menjalankan laman web ini, dan cabaran unik yang datang dengan mengendalikan laman web dengan status undang-undang yang meragukan, kerana tiada “AWS untuk badan amal bayangan”."

#, fuzzy
msgid "blog.how-to-run.text3"
msgstr "<em>Juga lihat artikel saudara <a %(blog_how_to_become_a_pirate_archivist)s>Bagaimana menjadi pengarkib lanun</a>.</em>"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens"
msgstr "Token inovasi"

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text1"
msgstr "Mari kita mulakan dengan susunan teknologi kami. Ia sengaja membosankan. Kami menggunakan Flask, MariaDB, dan ElasticSearch. Itu sahaja. Carian sebahagian besarnya adalah masalah yang telah diselesaikan, dan kami tidak berniat untuk menciptanya semula. Selain itu, kami perlu membelanjakan <a %(mcfunley)s>token inovasi</a> kami pada perkara lain: tidak diturunkan oleh pihak berkuasa."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text2"
msgstr "Jadi, seberapa sah atau tidak sah sebenarnya Arkib Anna? Ini kebanyakannya bergantung pada bidang kuasa undang-undang. Kebanyakan negara mempercayai beberapa bentuk hak cipta, yang bermaksud bahawa orang atau syarikat diberikan monopoli eksklusif pada jenis karya tertentu untuk tempoh masa tertentu. Sebagai tambahan, di Arkib Anna kami percaya walaupun terdapat beberapa manfaat, secara keseluruhan hak cipta adalah negatif bersih untuk masyarakat — tetapi itu adalah cerita untuk masa lain."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text3"
msgstr "Monopoli eksklusif pada karya tertentu ini bermaksud bahawa adalah haram bagi sesiapa di luar monopoli ini untuk mengedarkan karya tersebut secara langsung — termasuk kami. Tetapi Arkib Anna adalah enjin carian yang tidak mengedarkan karya tersebut secara langsung (sekurang-kurangnya tidak di laman web clearnet kami), jadi kami sepatutnya baik-baik saja, bukan? Tidak semestinya. Di banyak bidang kuasa, bukan sahaja haram untuk mengedarkan karya berhak cipta, tetapi juga untuk memautkan ke tempat yang melakukannya. Contoh klasik ini adalah undang-undang DMCA di Amerika Syarikat."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text4"
msgstr "Itulah hujung spektrum yang paling ketat. Di hujung spektrum yang lain, secara teorinya mungkin ada negara tanpa undang-undang hak cipta sama sekali, tetapi ini sebenarnya tidak wujud. Hampir setiap negara mempunyai beberapa bentuk undang-undang hak cipta dalam buku. Penguatkuasaan adalah cerita yang berbeza. Terdapat banyak negara dengan kerajaan yang tidak peduli untuk menguatkuasakan undang-undang hak cipta. Terdapat juga negara di antara dua ekstrem, yang melarang pengedaran karya berhak cipta, tetapi tidak melarang memautkan kepada karya tersebut."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text5"
msgstr "Pertimbangan lain adalah di peringkat syarikat. Jika sebuah syarikat beroperasi dalam bidang kuasa yang tidak peduli tentang hak cipta, tetapi syarikat itu sendiri tidak bersedia mengambil sebarang risiko, maka mereka mungkin menutup laman web anda sebaik sahaja ada yang mengadu mengenainya."

#, fuzzy
msgid "blog.how-to-run.innovation-tokens.text6"
msgstr "Akhirnya, pertimbangan besar adalah pembayaran. Oleh kerana kami perlu kekal tanpa nama, kami tidak boleh menggunakan kaedah pembayaran tradisional. Ini meninggalkan kami dengan mata wang kripto, dan hanya sebilangan kecil syarikat yang menyokongnya (terdapat kad debit maya yang dibayar dengan kripto, tetapi ia sering tidak diterima)."

#, fuzzy
msgid "blog.how-to-run.architecture"
msgstr "Seni bina sistem"

#, fuzzy
msgid "blog.how-to-run.architecture.text1"
msgstr "Jadi katakan anda menemui beberapa syarikat yang bersedia untuk menjadi hos laman web anda tanpa menutupnya — mari kita panggil mereka “penyedia yang mencintai kebebasan” 😄. Anda akan cepat mendapati bahawa menjadi hos segala-galanya dengan mereka agak mahal, jadi anda mungkin ingin mencari beberapa “penyedia murah” dan melakukan hos sebenar di sana, dengan proksi melalui penyedia yang mencintai kebebasan. Jika anda melakukannya dengan betul, penyedia murah tidak akan tahu apa yang anda hoskan, dan tidak akan menerima sebarang aduan."

#, fuzzy
msgid "blog.how-to-run.architecture.text2"
msgstr "Dengan semua penyedia ini terdapat risiko mereka menutup anda juga, jadi anda juga memerlukan redundansi. Kami memerlukan ini pada semua peringkat tumpukan kami."

#, fuzzy
msgid "blog.how-to-run.architecture.text3"
msgstr "Satu syarikat yang agak mencintai kebebasan yang telah meletakkan dirinya dalam kedudukan yang menarik ialah Cloudflare. Mereka telah <a %(blog_cloudflare)s>berhujah</a> bahawa mereka bukan penyedia hos, tetapi utiliti, seperti ISP. Oleh itu, mereka tidak tertakluk kepada DMCA atau permintaan penarikan balik lain, dan meneruskan sebarang permintaan kepada penyedia hos sebenar anda. Mereka telah pergi sejauh pergi ke mahkamah untuk melindungi struktur ini. Oleh itu, kita boleh menggunakannya sebagai satu lagi lapisan caching dan perlindungan."

#, fuzzy
msgid "blog.how-to-run.architecture.text4"
msgstr "Cloudflare tidak menerima pembayaran tanpa nama, jadi kami hanya boleh menggunakan pelan percuma mereka. Ini bermakna kami tidak boleh menggunakan ciri pengimbangan beban atau failover mereka. Oleh itu, kami <a %(annas_archive_l255)s>melaksanakannya sendiri</a> di peringkat domain. Apabila halaman dimuatkan, pelayar akan memeriksa sama ada domain semasa masih tersedia, dan jika tidak, ia menulis semula semua URL ke domain yang berbeza. Oleh kerana Cloudflare menyimpan banyak halaman, ini bermakna pengguna boleh mendarat di domain utama kami, walaupun pelayan proksi tidak berfungsi, dan kemudian pada klik seterusnya dipindahkan ke domain lain."

#, fuzzy
msgid "blog.how-to-run.architecture.text5"
msgstr "Kami masih juga mempunyai kebimbangan operasi biasa untuk ditangani, seperti memantau kesihatan pelayan, mencatatkan ralat backend dan frontend, dan sebagainya. Seni bina failover kami membolehkan lebih ketahanan di bahagian ini juga, contohnya dengan menjalankan satu set pelayan yang sama sekali berbeza pada salah satu domain. Kami juga boleh menjalankan versi kod dan Datasets yang lebih lama pada domain berasingan ini, sekiranya pepijat kritikal dalam versi utama tidak disedari."

#, fuzzy
msgid "blog.how-to-run.architecture.text6"
msgstr "Kami juga boleh melindungi diri daripada Cloudflare berpaling tadah terhadap kami, dengan mengeluarkannya dari salah satu domain, seperti domain berasingan ini. Pelbagai permutasi idea-idea ini adalah mungkin."

#, fuzzy
msgid "blog.how-to-run.tools"
msgstr "Alat"

#, fuzzy
msgid "blog.how-to-run.tools.text1"
msgstr "Mari kita lihat alat apa yang kita gunakan untuk mencapai semua ini. Ini sangat berkembang apabila kita menghadapi masalah baru dan mencari penyelesaian baru."

#, fuzzy
msgid "blog.how-to-run.tools.app"
msgstr "Pelayan aplikasi: Flask, MariaDB, ElasticSearch, Docker."

#, fuzzy
msgid "blog.how-to-run.tools.proxy"
msgstr "Pelayan proksi: Varnish."

#, fuzzy
msgid "blog.how-to-run.tools.management"
msgstr "Pengurusan pelayan: Ansible, Checkmk, UFW."

#, fuzzy
msgid "blog.how-to-run.tools.dev"
msgstr "Pembangunan: Gitlab, Weblate, Zulip."

#, fuzzy
msgid "blog.how-to-run.tools.onion"
msgstr "Hos statik Onion: Tor, Nginx."

#, fuzzy
msgid "blog.how-to-run.tools.text2"
msgstr "Terdapat beberapa keputusan yang kami telah berulang-alik. Salah satunya ialah komunikasi antara pelayan: kami pernah menggunakan Wireguard untuk ini, tetapi mendapati bahawa ia kadang-kadang berhenti menghantar sebarang data, atau hanya menghantar data dalam satu arah. Ini berlaku dengan beberapa tetapan Wireguard yang berbeza yang kami cuba, seperti <a %(github_costela_wesher)s>wesher</a> dan <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Kami juga mencuba terowong port melalui SSH, menggunakan autossh dan sshuttle, tetapi menghadapi <a %(github_sshuttle)s>masalah di sana</a> (walaupun masih tidak jelas kepada saya jika autossh mengalami masalah TCP-over-TCP atau tidak — ia hanya terasa seperti penyelesaian yang janggal kepada saya tetapi mungkin ia sebenarnya baik?)."

#, fuzzy
msgid "blog.how-to-run.tools.text3"
msgstr "Sebaliknya, kami kembali kepada sambungan langsung antara pelayan, menyembunyikan bahawa pelayan sedang berjalan pada penyedia murah menggunakan penapisan IP dengan UFW. Ini mempunyai kelemahan bahawa Docker tidak berfungsi dengan baik dengan UFW, melainkan anda menggunakan <code>network_mode: \"host\"</code>. Semua ini adalah sedikit lebih terdedah kepada kesilapan, kerana anda akan mendedahkan pelayan anda kepada internet dengan hanya sedikit salah konfigurasi. Mungkin kita patut kembali kepada autossh — maklum balas akan sangat dialu-alukan di sini."

#, fuzzy
msgid "blog.how-to-run.tools.text4"
msgstr "Kami juga telah berulang-alik pada Varnish vs. Nginx. Kami kini menyukai Varnish, tetapi ia mempunyai keanehan dan tepi kasar. Perkara yang sama berlaku untuk Checkmk: kami tidak menyukainya, tetapi ia berfungsi buat masa ini. Weblate telah okay tetapi tidak luar biasa — saya kadang-kadang bimbang ia akan kehilangan data saya setiap kali saya cuba menyegerakkannya dengan repo git kami. Flask telah baik secara keseluruhan, tetapi ia mempunyai beberapa keanehan pelik yang telah memakan banyak masa untuk menyahpepijat, seperti mengkonfigurasi domain tersuai, atau isu dengan integrasi SqlAlchemy."

#, fuzzy
msgid "blog.how-to-run.tools.text5"
msgstr "Setakat ini alat lain telah hebat: kami tidak mempunyai aduan serius tentang MariaDB, ElasticSearch, Gitlab, Zulip, Docker, dan Tor. Semua ini telah mempunyai beberapa isu, tetapi tiada yang terlalu serius atau memakan masa."

#, fuzzy
msgid "blog.how-to-run.conclusions"
msgstr "Kesimpulan"

#, fuzzy
msgid "blog.how-to-run.conclusions.text1"
msgstr "Ia adalah satu pengalaman yang menarik untuk belajar bagaimana untuk menubuhkan enjin carian perpustakaan bayangan yang kukuh dan tahan lasak. Terdapat banyak lagi butiran untuk dikongsi dalam kiriman akan datang, jadi beritahu saya apa yang anda ingin pelajari lebih lanjut!"

#, fuzzy
msgid "blog.how-to-run.conclusions.text2"
msgstr "Seperti biasa, kami mencari sumbangan untuk menyokong kerja ini, jadi pastikan untuk melihat halaman Derma di Arkib Anna. Kami juga mencari jenis sokongan lain, seperti geran, penaja jangka panjang, penyedia pembayaran berisiko tinggi, mungkin juga iklan (yang berselera!). Dan jika anda ingin menyumbangkan masa dan kemahiran anda, kami sentiasa mencari pembangun, penterjemah, dan sebagainya. Terima kasih atas minat dan sokongan anda."

#, fuzzy
msgid "blog.how-to-run.signature"
msgstr "- Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>)"

#, fuzzy
msgid "blog.index.text1"
msgstr "Hai, saya Anna. Saya mencipta <a %(wikipedia_annas_archive)s>Arkib Anna</a>, perpustakaan bayangan terbesar di dunia. Ini adalah blog peribadi saya, di mana saya dan rakan sepasukan saya menulis tentang cetak rompak, pemeliharaan digital, dan banyak lagi."

#, fuzzy
msgid "blog.index.text2"
msgstr "Hubungi saya di <a %(reddit)s>Reddit</a>."

#, fuzzy
msgid "blog.index.text3"
msgstr "Perhatikan bahawa laman web ini hanyalah sebuah blog. Kami hanya menempatkan kata-kata kami sendiri di sini. Tiada torrent atau fail berhak cipta lain yang dihoskan atau dipautkan di sini."

#, fuzzy
msgid "blog.index.heading"
msgstr "Kiriman blog"

#, fuzzy
msgid "blog.worldcat-scrape.title"
msgstr "1.3B WorldCat scrape"

#, fuzzy
msgid "blog.books-on-ipfs.title"
msgstr "Meletakkan 5,998,794 buku di IPFS"

#, fuzzy
msgid "blog.books-on-ipfs.deprecated"
msgstr "Amaran: catatan blog ini telah dihentikan. Kami telah memutuskan bahawa IPFS belum bersedia untuk digunakan secara meluas. Kami masih akan memautkan fail pada IPFS dari Arkib Anna apabila mungkin, tetapi kami tidak akan menjadi hosnya sendiri lagi, dan kami juga tidak mengesyorkan orang lain untuk mencerminkannya menggunakan IPFS. Sila lihat halaman Torrents kami jika anda ingin membantu memelihara koleksi kami."

#, fuzzy
msgid "blog.worldcat-scrape.tldr"
msgstr "<em><strong>TL;DR:</strong> Arkib Anna mengikis semua WorldCat (koleksi metadata perpustakaan terbesar di dunia) untuk membuat senarai TODO buku yang perlu dipelihara.</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text1"
msgstr "Setahun yang lalu, kami <a %(blog)s>memulakan</a> untuk menjawab soalan ini: <strong>Apakah peratusan buku yang telah dipelihara secara kekal oleh perpustakaan bayangan?</strong>"

#, fuzzy
msgid "blog.worldcat-scrape.text2"
msgstr "Sebaik sahaja sebuah buku masuk ke dalam perpustakaan bayangan data terbuka seperti <a %(wikipedia_library_genesis)s>Library Genesis</a>, dan kini <a %(wikipedia_annas_archive)s>Arkib Anna</a>, ia akan dicerminkan di seluruh dunia (melalui torrent), dengan itu secara praktikal memeliharanya selama-lamanya."

#, fuzzy
msgid "blog.worldcat-scrape.text3"
msgstr "Untuk menjawab soalan tentang peratusan buku yang telah dipelihara, kita perlu mengetahui penyebutnya: berapa banyak buku yang wujud secara keseluruhan? Dan idealnya kita bukan sahaja mempunyai nombor, tetapi metadata sebenar. Kemudian kita bukan sahaja boleh memadankan mereka dengan perpustakaan bayangan, tetapi juga <strong>mencipta senarai TODO buku yang tinggal untuk dipelihara!</strong> Kita bahkan boleh mula bermimpi tentang usaha crowdsourced untuk menuruni senarai TODO ini."

#, fuzzy
msgid "blog.worldcat-scrape.text4"
msgstr "Kami mengikis <a %(wikipedia_isbndb_com)s>ISBNdb</a>, dan memuat turun <a %(openlibrary)s>set data Open Library</a>, tetapi hasilnya tidak memuaskan. Masalah utama adalah bahawa tidak banyak pertindihan ISBN. Lihatlah diagram Venn ini dari <a %(blog)s>kiriman blog kami</a>:"

#, fuzzy
msgid "blog.worldcat-scrape.text5"
msgstr "Kami sangat terkejut dengan betapa sedikitnya pertindihan antara ISBNdb dan Open Library, kedua-duanya dengan bebasnya memasukkan data dari pelbagai sumber, seperti pengikisan web dan rekod perpustakaan. Jika kedua-duanya melakukan kerja yang baik dalam mencari kebanyakan ISBN di luar sana, bulatan mereka pasti akan mempunyai pertindihan yang ketara, atau satu akan menjadi subset kepada yang lain. Ini membuatkan kami tertanya-tanya, berapa banyak buku yang jatuh <em>sepenuhnya di luar bulatan ini</em>? Kami memerlukan pangkalan data yang lebih besar."

#, fuzzy
msgid "blog.worldcat-scrape.worldcat"
msgstr "WorldCat"

#, fuzzy
msgid "blog.worldcat-scrape.text6"
msgstr "Itulah ketika kami menetapkan pandangan kami pada pangkalan data buku terbesar di dunia: <a %(wikipedia_worldcat)s>WorldCat</a>. Ini adalah pangkalan data proprietari oleh organisasi bukan untung <a %(wikipedia_oclc)s>OCLC</a>, yang mengumpulkan rekod metadata dari perpustakaan di seluruh dunia, sebagai pertukaran untuk memberikan perpustakaan tersebut akses kepada set data penuh, dan memaparkan mereka dalam hasil carian pengguna akhir."

#, fuzzy
msgid "blog.worldcat-scrape.text7"
msgstr "Walaupun OCLC adalah organisasi bukan untung, model perniagaan mereka memerlukan perlindungan pangkalan data mereka. Baiklah, kami minta maaf untuk mengatakan, rakan-rakan di OCLC, kami akan memberikannya semua. :-)"

#, fuzzy
msgid "blog.worldcat-scrape.text8"
msgstr "Sepanjang tahun lalu, kami telah mengikis semua rekod WorldCat dengan teliti. Pada mulanya, kami mendapat peluang yang baik. WorldCat baru sahaja melancarkan reka bentuk semula laman web mereka sepenuhnya (pada Ogos 2022). Ini termasuk pembaharuan besar-besaran sistem backend mereka, memperkenalkan banyak kelemahan keselamatan. Kami segera mengambil peluang itu, dan berjaya mengikis ratusan juta (!) rekod dalam beberapa hari sahaja."

#, fuzzy
msgid "blog.worldcat-scrape.alt.redesign"
msgstr "<em>Reka bentuk semula WorldCat</em>"

#, fuzzy
msgid "blog.worldcat-scrape.text9"
msgstr "Selepas itu, kelemahan keselamatan perlahan-lahan diperbaiki satu demi satu, sehingga yang terakhir yang kami temui telah ditampal kira-kira sebulan yang lalu. Pada masa itu kami sudah mempunyai hampir semua rekod, dan hanya mencari rekod yang sedikit lebih berkualiti. Jadi kami merasakan sudah tiba masanya untuk melepaskannya!"

#, fuzzy
msgid "blog.worldcat-scrape.text10"
msgstr "Mari kita lihat beberapa maklumat asas mengenai data:"

#, fuzzy
msgid "blog.worldcat-scrape.data.format"
msgstr "<strong>Format?</strong> <a %(blog)s>Kontena Arkib Anna (AAC)</a>, yang pada dasarnya adalah <a %(jsonlines)s>JSON Lines</a> yang dimampatkan dengan <a %(zstd)s>Zstandard</a>, ditambah beberapa semantik yang distandardkan. Kontena ini membungkus pelbagai jenis rekod, berdasarkan pelbagai pengikisan yang kami laksanakan."

#, fuzzy
msgid "blog.worldcat-scrape.data"
msgstr "Data"

msgid "dyn.buy_membership.error.unknown"
msgstr "Ralat yang tidak diketahui berlaku. Sila hubungi kami di %(email)s berserta tangkapan skrin."

msgid "dyn.buy_membership.error.minimum"
msgstr "Syiling ini mempunyai minimum yang lebih tinggi daripada biasa. Sila pilih tempoh masa atau syiling yang lain."

msgid "dyn.buy_membership.error.try_again"
msgstr "Permintaan tidak dapat diselesaikan. Sila cuba semula dalam beberapa minit. Jika isu ini masih berlaku, sila hubungi kami di %(email)s berserta tangkapan skrin."

#, fuzzy
msgid "dyn.buy_membership.error.wait"
msgstr "Ralat dalam pemprosesan pembayaran. Sila tunggu sebentar dan cuba lagi. Jika masalah berterusan lebih daripada 24 jam, sila hubungi kami di %(email)s dengan tangkapan skrin."

#, fuzzy
msgid "page.comments.hidden_comment"
msgstr "komen tersembunyi"

#, fuzzy
msgid "page.comments.file_issue"
msgstr "Isu fail: %(file_issue)s"

#, fuzzy
msgid "page.comments.better_version"
msgstr "Versi yang lebih baik"

#, fuzzy
msgid "page.comments.do_you_want_to_report_abuse"
msgstr "Adakah anda ingin melaporkan pengguna ini untuk tingkah laku yang kasar atau tidak sesuai?"

#, fuzzy
msgid "page.comments.report_abuse"
msgstr "Laporkan penyalahgunaan"

#, fuzzy
msgid "page.comments.abuse_reported"
msgstr "Penyalahgunaan dilaporkan:"

#, fuzzy
msgid "page.comments.reported_abuse_this_user"
msgstr "Anda telah melaporkan pengguna ini untuk penyalahgunaan."

#, fuzzy
msgid "page.comments.reply_button"
msgstr "Balas"

#, fuzzy
msgid "page.md5.quality.logged_out_login"
msgstr "Sila <a %(a_login)s>log masuk</a>."

#, fuzzy
msgid "page.md5.quality.comment_thanks"
msgstr "Anda telah meninggalkan komen. Ia mungkin mengambil masa sebentar untuk dipaparkan."

#, fuzzy
msgid "page.md5.quality.comment_error"
msgstr "Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi."

#, fuzzy
msgid "page.md5.box.download.affected_files"
msgstr "%(count)s halaman terjejas"

msgid "common.md5_problem_type_mapping.lgrsnf_visible"
msgstr "Tidak dapat dilihat di Bukan Fiksyen Libgen.rs"

msgid "common.md5_problem_type_mapping.lgrsfic_visible"
msgstr "Tidak dapat dilihat di Fiksyen Libgen.rs"

msgid "common.md5_problem_type_mapping.lgli_visible"
msgstr "Tidak dapat dilihat di Libgen.li"

msgid "common.md5_problem_type_mapping.lgli_broken"
msgstr "Ditanda rosak di Libgen.li"

msgid "common.md5_problem_type_mapping.zlib_missing"
msgstr "Hilang dari Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_spam"
msgstr "Ditandakan sebagai “spam” dalam Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.zlib_bad_file"
msgstr "Ditandakan sebagai “fail buruk” dalam Z-Library"

#, fuzzy
msgid "common.md5_problem_type_mapping.duxiu_pdg_broken_files"
msgstr "Tidak semua halaman dapat ditukar kepada PDF"

#, fuzzy
msgid "common.md5_problem_type_mapping.upload_exiftool_failed"
msgstr "Menjalankan exiftool gagal pada fail ini"

msgid "common.md5_content_type_mapping.book_unknown"
msgstr "Buku (tidak diketahui)"

msgid "common.md5_content_type_mapping.book_nonfiction"
msgstr "Buku (bukan fiksyen)"

msgid "common.md5_content_type_mapping.book_fiction"
msgstr "Buku (fiksyen)"

msgid "common.md5_content_type_mapping.journal_article"
msgstr "Artikel jurnal"

msgid "common.md5_content_type_mapping.standards_document"
msgstr "Dokumen piawaian"

msgid "common.md5_content_type_mapping.magazine"
msgstr "Majalah"

msgid "common.md5_content_type_mapping.book_comic"
msgstr "Buku komik"

msgid "common.md5_content_type_mapping.musical_score"
msgstr "Skor muzik"

#, fuzzy
msgid "common.md5_content_type_mapping.audiobook"
msgstr "Buku Audio"

msgid "common.md5_content_type_mapping.other"
msgstr "Lain-lain"

msgid "common.access_types_mapping.aa_download"
msgstr "muat turun server rakan kongsi"

#, fuzzy
msgid "common.access_types_mapping.aa_scidb"
msgstr "SciDB"

msgid "common.access_types_mapping.external_download"
msgstr "muat turun luaran"

msgid "common.access_types_mapping.external_borrow"
msgstr "Pinjaman luar"

msgid "common.access_types_mapping.external_borrow_printdisabled"
msgstr "Pinjaman luar (cetakan dilumpuhkan)"

msgid "common.access_types_mapping.meta_explore"
msgstr "Teroka metadata"

#, fuzzy
msgid "common.access_types_mapping.torrents_available"
msgstr "Terkandung dalam torrents"

msgid "common.record_sources_mapping.lgrs"
msgstr "Libgen.rs"

msgid "common.record_sources_mapping.lgli"
msgstr "Libgen.li"

msgid "common.record_sources_mapping.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "common.record_sources_mapping.zlibzh"
msgstr "Z-Library Cina"

#, fuzzy
msgid "common.record_sources_mapping.ia"
msgstr "IA"

msgid "common.record_sources_mapping.isbndb"
msgstr "ISBNdb"

msgid "common.record_sources_mapping.ol"
msgstr "OpenLibrary"

msgid "common.record_sources_mapping.scihub"
msgstr "Sci-Hub"

msgid "common.record_sources_mapping.oclc"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "common.record_sources_mapping.duxiu"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "common.record_sources_mapping.uploads"
msgstr "Muat naik ke AA"

#, fuzzy
msgid "common.record_sources_mapping.magzdb"
msgstr "MagzDB"

#, fuzzy
msgid "common.record_sources_mapping.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "common.record_sources_mapping.edsebk"
msgstr "Indeks eBook EBSCOhost"

#, fuzzy
msgid "common.record_sources_mapping.cerlalc"
msgstr "Cerlalc"

#, fuzzy
msgid "common.record_sources_mapping.czech_oo42hcks"
msgstr "Metadata Czech"

#, fuzzy
msgid "common.record_sources_mapping.gbooks"
msgstr "Google Books"

#, fuzzy
msgid "common.record_sources_mapping.goodreads"
msgstr "Goodreads"

#, fuzzy
msgid "common.record_sources_mapping.isbngrp"
msgstr "ISBN GRP"

#, fuzzy
msgid "common.record_sources_mapping.libby"
msgstr "Libby"

#, fuzzy
msgid "common.record_sources_mapping.rgb"
msgstr "Perpustakaan Negara Rusia"

#, fuzzy
msgid "common.record_sources_mapping.trantor"
msgstr "Trantor"

#, fuzzy
msgid "common.record_sources_mapping.hathi"
msgstr "HathiTrust"

#, fuzzy
msgid "common.specific_search_fields.title"
msgstr "Tajuk"

#, fuzzy
msgid "common.specific_search_fields.author"
msgstr "Pengarang"

#, fuzzy
msgid "common.specific_search_fields.publisher"
msgstr "Penerbit"

#, fuzzy
msgid "common.specific_search_fields.edition_varia"
msgstr "Edisi"

#, fuzzy
msgid "common.specific_search_fields.year"
msgstr "Tahun diterbitkan"

#, fuzzy
msgid "common.specific_search_fields.original_filename"
msgstr "Nama fail asal"

#, fuzzy
msgid "common.specific_search_fields.description_comments"
msgstr "Penerangan dan komen metadata"

#, fuzzy
msgid "page.md5.box.download.temporarily_unavailable"
msgstr "Muat turun dari Pelayan Rakan tidak tersedia buat sementara waktu untuk fail ini."

msgid "common.md5.servers.fast_partner"
msgstr "Server Rakan Kongsi Pantas #%(number)s"

#, fuzzy
msgid "common.md5.servers.fast_partner.recommended2"
msgstr "(disarankan)"

#, fuzzy
msgid "common.md5.servers.no_browser_verification_or_waitlists"
msgstr "(tiada pengesahan pelayar atau senarai menunggu)"

msgid "common.md5.servers.slow_partner"
msgstr "Server Rakan Kongsi Lambat #%(number)s"

#, fuzzy
msgid "common.md5.servers.faster_with_waitlist"
msgstr "(sedikit lebih cepat tetapi dengan senarai menunggu)"

#, fuzzy
msgid "common.md5.servers.slow_no_waitlist"
msgstr "(tiada senarai menunggu, tetapi boleh menjadi sangat perlahan)"

msgid "page.md5.box.download.scihub"
msgstr "Sci-Hub: %(doi)s"

msgid "page.md5.box.download.lgrsnf"
msgstr "Bukan Fiksyen Libgen.rs"

msgid "page.md5.box.download.lgrsfic"
msgstr "Fiksyen Libgen.rs"

msgid "page.md5.box.download.lgli"
msgstr "Libgen.li"

msgid "page.md5.box.download.extra_also_click_get"
msgstr "(tekan “GET” di atas)"

msgid "page.md5.box.download.extra_click_get"
msgstr "(tekan “GET” di atas)"

#, fuzzy
msgid "page.md5.box.download.libgen_ads"
msgstr "iklan mereka diketahui mengandungi perisian hasad, jadi gunakan penyekat iklan atau jangan klik iklan"

#, fuzzy
msgid "page.md5.box.download.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.md5.box.download.nexusstc_unreliable"
msgstr "(Fail Nexus/STC boleh menjadi tidak boleh dipercayai untuk dimuat turun)"

#, fuzzy
msgid "page.md5.box.download.zlib"
msgstr "Z-Library"

#, fuzzy
msgid "page.md5.box.download.zlib_tor"
msgstr "Z-Library di Tor"

#, fuzzy
msgid "page.md5.box.download.zlib_tor_extra"
msgstr "(memerlukan Pelayar Tor)"

#, fuzzy
msgid "page.md5.box.download.magzdb"
msgstr "MagzDB"

msgid "page.md5.box.download.ia_borrow"
msgstr "Pinjam dari Internet Archive"

msgid "page.md5.box.download.print_disabled_only"
msgstr "(cetakan dilumpuhkan untuk penaung sahaja)"

msgid "page.md5.box.download.scihub_maybe"
msgstr "(DOI berkaitan mungkin tidak tersedia di Sci-Hub)"

#, fuzzy
msgid "page.md5.box.download.manualslib"
msgstr "ManualsLib"

#, fuzzy
msgid "page.md5.box.download.pubmed"
msgstr "PubMed"

#, fuzzy
msgid "page.md5.box.download.collection"
msgstr "koleksi"

#, fuzzy
msgid "page.md5.box.download.torrent"
msgstr "torrent"

msgid "page.md5.box.download.bulk_torrents"
msgstr "Muat turun torrent secara pukal"

msgid "page.md5.box.download.experts_only"
msgstr "(pakar sahaja)"

msgid "page.md5.box.download.aa_isbn"
msgstr "Cari ISBN di Anna's Archive"

msgid "page.md5.box.download.other_isbn"
msgstr "Cari ISBN di pengkalan-pengkalan data lain"

msgid "page.md5.box.download.original_isbndb"
msgstr "Cari rekod asal di ISBNdb"

msgid "page.md5.box.download.aa_openlib"
msgstr "Cari ID Open Library di Anna's Archive"

msgid "page.md5.box.download.original_openlib"
msgstr "Cari rekod asal di Open Library"

msgid "page.md5.box.download.aa_oclc"
msgstr "Cari nombor OCLC (WorldCat) di Anna's Archive"

msgid "page.md5.box.download.original_oclc"
msgstr "Cari rekod asal di WorldCat"

#, fuzzy
msgid "page.md5.box.download.aa_duxiu"
msgstr "Cari dalam Arkib Anna untuk nombor SSID DuXiu"

#, fuzzy
msgid "page.md5.box.download.original_duxiu"
msgstr "Cari secara manual di DuXiu"

#, fuzzy
msgid "page.md5.box.download.aa_cadal"
msgstr "Cari Arkib Anna untuk nombor CADAL SSNO"

#, fuzzy
msgid "page.md5.box.download.original_cadal"
msgstr "Cari rekod asal dalam CADAL"

#, fuzzy
msgid "page.md5.box.download.aa_dxid"
msgstr "Cari Arkib Anna untuk nombor DXID DuXiu"

#, fuzzy
msgid "page.md5.box.download.edsebk"
msgstr "Indeks eBook EBSCOhost"

msgid "page.md5.box.download.scidb"
msgstr "Anna’s Archive 🧬 SciDB"

msgid "common.md5.servers.no_browser_verification"
msgstr "(pengesahan pelayar tidak diperlukan)"

#, fuzzy
msgid "page.md5.top_row.isbndb"
msgstr "ISBNdb %(id)s}"

#, fuzzy
msgid "page.md5.top_row.oclc"
msgstr "OCLC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.duxiu_ssid"
msgstr "DuXiu SSID %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cadal_ssno"
msgstr "CADAL SSNO %(id)s}"

#, fuzzy
msgid "page.md5.top_row.magzdb"
msgstr "MagzDB %(id)s}"

#, fuzzy
msgid "page.md5.top_row.nexusstc"
msgstr "Nexus/STC %(id)s}"

#, fuzzy
msgid "page.md5.top_row.edsebk"
msgstr "EBSCOhost edsebk %(id)s}"

#, fuzzy
msgid "page.md5.top_row.cerlalc"
msgstr "Cerlalc %(id)s}"

#, fuzzy
msgid "page.md5.top_row.czech_oo42hcks"
msgstr "Metadata Czech %(id)s}"

#, fuzzy
msgid "page.md5.top_row.gbooks"
msgstr "Google Books %(id)s}"

#, fuzzy
msgid "page.md5.top_row.goodreads"
msgstr "Goodreads %(id)s}"

#, fuzzy
msgid "page.md5.top_row.isbngrp"
msgstr "ISBN GRP %(id)s}"

#, fuzzy
msgid "page.md5.top_row.libby"
msgstr "Libby %(id)s}"

#, fuzzy
msgid "page.md5.top_row.rgb"
msgstr "RSL %(id)s}"

#, fuzzy
msgid "page.md5.top_row.trantor"
msgstr "Trantor %(id)s}"

#, fuzzy
msgid "page.md5.top_row.hathi"
msgstr "HathiTrust %(id)s}"

#, fuzzy
msgid "page.datasets.sources.metadata.header"
msgstr "Metadata"

#, fuzzy
msgid "page.md5.box.descr_title"
msgstr "penerangan"

#, fuzzy
msgid "page.md5.box.alternative_filename"
msgstr "Nama fail alternatif"

#, fuzzy
msgid "page.md5.box.alternative_title"
msgstr "Tajuk alternatif"

#, fuzzy
msgid "page.md5.box.alternative_author"
msgstr "Pengarang alternatif"

#, fuzzy
msgid "page.md5.box.alternative_publisher"
msgstr "Penerbit alternatif"

#, fuzzy
msgid "page.md5.box.alternative_edition"
msgstr "Edisi alternatif"

#, fuzzy
msgid "page.md5.box.alternative_extension"
msgstr "Sambungan alternatif"

#, fuzzy
msgid "page.md5.box.metadata_comments_title"
msgstr "komen metadata"

#, fuzzy
msgid "page.md5.box.alternative_description"
msgstr "Penerangan alternatif"

#, fuzzy
msgid "page.md5.box.date_open_sourced_title"
msgstr "tarikh sumber terbuka"

msgid "page.md5.header.scihub"
msgstr "Fail Sci-Hub “%(id)s”"

msgid "page.md5.header.ia"
msgstr "Fail Controlled Digital Lending Internet Archive “%(id)s”"

msgid "page.md5.header.ia_desc"
msgstr "Ini adalah rekod dari Internet Archive dan bukan fail yang boleh dimuat turun terus. Anda boleh cuba meminjam buku (pautan di bawah) atau gunakan URL ini apabila a %(a_request)s>meminta file</a>."

#, fuzzy
msgid "page.md5.header.consider_upload"
msgstr "Jika anda mempunyai fail ini dan ia belum tersedia di Arkib Anna, pertimbangkan untuk <a %(a_request)s>memuat naiknya</a>."

msgid "page.md5.header.meta_isbn"
msgstr "Rekod metadata ISBNdb %(id)s"

msgid "page.md5.header.meta_openlib"
msgstr "Rekod metadata Open Library %(id)s"

msgid "page.md5.header.meta_oclc"
msgstr "Rekod metadata nombor OCLC (WorldCat) %(id)s"

#, fuzzy
msgid "page.md5.header.meta_duxiu_ssid"
msgstr "Rekod metadata DuXiu SSID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_cadal_ssno"
msgstr "Rekod metadata CADAL SSNO %(id)s"

#, fuzzy
msgid "page.md5.header.meta_magzdb_id"
msgstr "Rekod metadata MagzDB ID %(id)s"

#, fuzzy
msgid "page.md5.header.meta_nexus_stc_id"
msgstr "Rekod metadata Nexus/STC ID %(id)s"

msgid "page.md5.header.meta_desc"
msgstr "Ini adalah rekod metadata dan bukan fail yang boleh dimuat turun terus. Anda boleh menggunakan URL ini apabila <a %(a_request)s>meminta fail</a>."

#, fuzzy
msgid "page.md5.text.linked_metadata"
msgstr "Metadata daripada rekod yang dipautkan"

#, fuzzy
msgid "page.md5.text.linked_metadata_openlib"
msgstr "Tingkatkan metadata di Open Library"

#, fuzzy
msgid "page.md5.warning.multiple_links"
msgstr "Amaran: pelbagai rekod yang dipautkan:"

#, fuzzy
msgid "page.md5.header.improve_metadata"
msgstr "Perbaiki metadata"

#, fuzzy
msgid "page.md5.text.report_quality"
msgstr "Laporkan kualiti fail"

#, fuzzy
msgid "page.search.results.download_time"
msgstr "Masa muat turun"

#, fuzzy
msgid "page.md5.codes.url"
msgstr "URL:"

#, fuzzy
msgid "page.md5.codes.website"
msgstr "Laman web:"

#, fuzzy
msgid "page.md5.codes.aa_abbr"
msgstr "AA:"

#, fuzzy
msgid "page.md5.codes.aa_search"
msgstr "Cari Arkib Anna untuk “%(name)s”"

#, fuzzy
msgid "page.md5.codes.code_explorer"
msgstr "Peneroka Kod:"

#, fuzzy
msgid "page.md5.codes.code_search"
msgstr "Lihat dalam Peneroka Kod “%(name)s”"

msgid "page.md5.box.descr_read_more"
msgstr "Baca lagi…"

msgid "page.md5.tabs.downloads"
msgstr "Muat turun (%(count)s)"

msgid "page.md5.tabs.borrow"
msgstr "Pinjam (%(count)s)"

msgid "page.md5.tabs.explore_metadata"
msgstr "Teroka metadata (%(count)s)"

#, fuzzy
msgid "page.md5.tabs.comments"
msgstr "Komen (%(count)s)"

msgid "page.md5.tabs.lists"
msgstr "Senarai (%(count)s)"

msgid "page.md5.tabs.stats"
msgstr "Statistik (%(count)s)"

msgid "common.tech_details"
msgstr "Perincian teknikal"

#, fuzzy
msgid "page.md5.box.issues.text1"
msgstr "<span class=\"font-bold\">❌ Fail ini mungkin mempunyai isu, dan telah disembunyikan dari perpustakaan sumber.</span> Kadang-kadang ini atas permintaan pemegang hak cipta, kadang-kadang kerana alternatif yang lebih baik tersedia, tetapi kadang-kadang kerana isu dengan fail itu sendiri. Ia mungkin masih boleh dimuat turun, tetapi kami mengesyorkan mencari fail alternatif terlebih dahulu. Maklumat lanjut:"

#, fuzzy
msgid "page.md5.box.download.better_file"
msgstr "Versi yang lebih baik bagi fail ini mungkin tersedia di %(link)s"

#, fuzzy
msgid "page.md5.box.issues.text2"
msgstr "Jika anda masih mahu memuat turun fail ini, pastikan hanya menggunakan perisian yang dipercayai dan dikemas kini untuk membukanya."

#, fuzzy
msgid "page.md5.box.download.header_fast_only"
msgstr "🚀 Muat turun pantas"

#, fuzzy
msgid "page.md5.box.download.header_fast_no_member"
msgstr "<strong>🚀 Muat turun pantas</strong> Sertai sebagai <a %(a_membership)s>ahli</a> untuk menyokong pemeliharaan jangka panjang buku, kertas, dan banyak lagi. Sebagai tanda terima kasih atas sokongan anda, anda mendapat muat turun pantas. ❤️"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.this_month"
msgstr "Jika anda menderma bulan ini, anda mendapat <strong>dua kali ganda</strong> jumlah muat turun pantas."

#, fuzzy
msgid "page.md5.box.download.header_fast_member"
msgstr "<strong>🚀 Muat turun pantas</strong> Anda mempunyai %(remaining)s baki hari ini. Terima kasih kerana menjadi ahli! ❤️"

#, fuzzy
msgid "page.md5.box.download.header_fast_member_no_remaining_new"
msgstr "<strong>🚀 Muat turun pantas</strong> Anda telah kehabisan muat turun pantas untuk hari ini."

#, fuzzy
msgid "page.md5.box.download.header_fast_member_valid_for"
msgstr "<strong>🚀 Muat turun pantas</strong> Anda telah memuat turun fail ini baru-baru ini. Pautan kekal sah untuk seketika."

#, fuzzy
msgid "page.md5.box.download.option"
msgstr "Pilihan #%(num)d: %(link)s %(extra)s"

#, fuzzy
msgid "page.md5.box.download.no_redirect"
msgstr "(tiada pengalihan)"

#, fuzzy
msgid "page.md5.box.download.open_in_viewer"
msgstr "(buka dalam penonton)"

#, fuzzy
msgid "layout.index.header.banner.refer"
msgstr "Rujuk rakan, dan anda serta rakan anda akan mendapat %(percentage)s%% muat turun pantas bonus!"

#, fuzzy
msgid "layout.index.header.learn_more"
msgstr "Ketahui lebih lanjut…"

#, fuzzy
msgid "page.md5.box.download.header_slow_only"
msgstr "🐢 Muat turun perlahan"

#, fuzzy
msgid "page.md5.box.download.trusted_partners"
msgstr "Daripada rakan kongsi yang dipercayai."

#, fuzzy
msgid "page.md5.box.download.slow_faq"
msgstr "Maklumat lanjut dalam <a %(a_slow)s>Soalan Lazim</a>."

msgid "common.md5.servers.browser_verification_unlimited"
msgstr "(mungkin memerlukan <a %(a_browser)s>pengesahan pelayar</a> — muat turun tanpa had!)"

#, fuzzy
msgid "page.md5.box.download.after_downloading"
msgstr "Selepas memuat turun:"

#, fuzzy
msgid "page.md5.box.download.open_in_our_viewer"
msgstr "Buka dalam penonton kami"

#, fuzzy
msgid "page.md5.box.external_downloads"
msgstr "tunjukkan muat turun luaran"

#, fuzzy
msgid "page.md5.box.download.header_external"
msgstr "Muat turun luaran"

#, fuzzy
msgid "page.md5.box.download.no_found"
msgstr "Tiada muat turun ditemui."

#, fuzzy
msgid "page.md5.box.download.no_issues_notice"
msgstr "Semua pilihan muat turun mempunyai fail yang sama, dan sepatutnya selamat untuk digunakan. Walau bagaimanapun, sentiasa berhati-hati apabila memuat turun fail dari internet, terutamanya dari laman web luar Anna’s Archive. Sebagai contoh, pastikan peranti anda sentiasa dikemas kini."

#, fuzzy
msgid "page.md5.box.download.dl_managers"
msgstr "Untuk fail besar, kami mengesyorkan menggunakan pengurus muat turun untuk mengelakkan gangguan."

#, fuzzy
msgid "page.md5.box.download.dl_managers.links"
msgstr "Pengurus muat turun yang disyorkan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.readers"
msgstr "Anda memerlukan pembaca ebook atau PDF untuk membuka fail, bergantung pada format fail."

#, fuzzy
msgid "page.md5.box.download.readers.links"
msgstr "Pembaca ebook yang disyorkan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.aa_viewer"
msgstr "Penonton dalam talian Arkib Anna"

#, fuzzy
msgid "page.md5.box.download.conversion"
msgstr "Gunakan alat dalam talian untuk menukar antara format."

#, fuzzy
msgid "page.md5.box.download.conversion.links"
msgstr "Alat penukaran yang disyorkan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.sendtokindle"
msgstr "Anda boleh menghantar kedua-dua fail PDF dan EPUB ke Kindle atau eReader Kobo anda."

#, fuzzy
msgid "page.md5.box.download.sendtokindle.links"
msgstr "Alat yang disyorkan: %(links)s"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kindle"
msgstr "Amazon ‘Send to Kindle’"

#, fuzzy
msgid "page.md5.box.download.link.send_to_kobokindle"
msgstr "djazz ‘Send to Kobo/Kindle’"

#, fuzzy
msgid "page.md5.box.download.support"
msgstr "Sokong penulis dan perpustakaan"

#, fuzzy
msgid "page.md5.box.download.support.authors"
msgstr "Jika anda suka ini dan mampu, pertimbangkan untuk membeli yang asal, atau menyokong penulis secara langsung."

#, fuzzy
msgid "page.md5.box.download.support.libraries"
msgstr "Jika ini tersedia di perpustakaan tempatan anda, pertimbangkan untuk meminjamnya secara percuma di sana."

#, fuzzy
msgid "page.md5.quality.header"
msgstr "Kualiti fail"

#, fuzzy
msgid "page.md5.quality.report"
msgstr "Bantu komuniti dengan melaporkan kualiti fail ini! 🙌"

#, fuzzy
msgid "page.md5.quality.report_issue"
msgstr "Laporkan isu fail (%(count)s)"

#, fuzzy
msgid "page.md5.quality.great_quality"
msgstr "Kualiti fail yang hebat (%(count)s)"

#, fuzzy
msgid "page.md5.quality.add_comment"
msgstr "Tambah komen (%(count)s)"

#, fuzzy
msgid "page.md5.quality.what_is_wrong"
msgstr "Apa yang salah dengan fail ini?"

#, fuzzy
msgid "page.md5.quality.copyright"
msgstr "Sila gunakan <a %(a_copyright)s>borang tuntutan DMCA / Hak Cipta</a>."

#, fuzzy
msgid "page.md5.quality.describe_the_issue"
msgstr "Huraikan isu (diperlukan)"

#, fuzzy
msgid "page.md5.quality.issue_description"
msgstr "Huraian isu"

#, fuzzy
msgid "page.md5.quality.better_md5.text1"
msgstr "MD5 versi yang lebih baik bagi fail ini (jika berkenaan)."

#, fuzzy
msgid "page.md5.quality.better_md5.text2"
msgstr "Isi ini jika terdapat fail lain yang hampir sama dengan fail ini (edisi yang sama, sambungan fail yang sama jika anda boleh menemukannya), yang sepatutnya digunakan oleh orang ramai sebagai ganti fail ini. Jika anda tahu versi yang lebih baik bagi fail ini di luar Arkib Anna, sila <a %(a_upload)s>muat naik</a>."

#, fuzzy
msgid "page.md5.quality.better_md5.line1"
msgstr "Anda boleh mendapatkan md5 dari URL, contohnya"

#, fuzzy
msgid "page.md5.quality.submit_report"
msgstr "Hantar laporan"

#, fuzzy
msgid "page.md5.quality.improve_the_metadata"
msgstr "Belajar bagaimana untuk <a %(a_metadata)s>menambah baik metadata</a> untuk fail ini sendiri."

#, fuzzy
msgid "page.md5.quality.report_thanks"
msgstr "Terima kasih kerana menghantar laporan anda. Ia akan dipaparkan di halaman ini, serta disemak secara manual oleh Anna (sehingga kami mempunyai sistem moderasi yang betul)."

#, fuzzy
msgid "page.md5.quality.report_error"
msgstr "Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi."

#, fuzzy
msgid "page.md5.quality.great.summary"
msgstr "Jika fail ini mempunyai kualiti yang baik, anda boleh membincangkan apa sahaja mengenainya di sini! Jika tidak, sila gunakan butang “Laporkan isu fail”."

#, fuzzy
msgid "page.md5.quality.loved_the_book"
msgstr "Saya suka buku ini!"

#, fuzzy
msgid "page.md5.quality.submit_comment"
msgstr "Tinggalkan komen"

#, fuzzy
msgid "common.english_only"
msgstr "Teks di bawah diteruskan dalam Bahasa Inggeris."

#, fuzzy
msgid "page.md5.text.stats.total_downloads"
msgstr "Jumlah muat turun: %(total)s"

#, fuzzy
msgid "page.md5.text.md5_info.text1"
msgstr "“File MD5” adalah hash yang dikira daripada kandungan fail, dan adalah unik berdasarkan kandungan tersebut. Semua perpustakaan bayangan yang kami indeks di sini menggunakan MD5 untuk mengenal pasti fail."

#, fuzzy
msgid "page.md5.text.md5_info.text2"
msgstr "Satu fail mungkin muncul dalam pelbagai perpustakaan bayangan. Untuk maklumat tentang pelbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>."

#, fuzzy
msgid "page.md5.text.ia_info.text1"
msgstr "Ini adalah fail yang diuruskan oleh perpustakaan <a %(a_ia)s>IA’s Controlled Digital Lending</a>, dan diindeks oleh Anna’s Archive untuk carian. Untuk maklumat tentang pelbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>."

#, fuzzy
msgid "page.md5.text.file_info.text1"
msgstr "Untuk maklumat tentang fail ini, lihat <a %(a_href)s>fail JSON</a>nya."

#, fuzzy
msgid "page.aarecord_issue.title"
msgstr "🔥 Masalah memuatkan halaman ini"

#, fuzzy
msgid "page.aarecord_issue.text"
msgstr "Sila segarkan untuk mencuba lagi. <a %(a_contact)s>Hubungi kami</a> jika masalah berterusan selama beberapa jam."

#, fuzzy
msgid "page.md5.invalid.header"
msgstr "Tidak ditemui"

#, fuzzy
msgid "page.md5.invalid.text"
msgstr "“%(md5_input)s” tidak ditemui dalam pangkalan data kami."

#, fuzzy
msgid "page.login.title"
msgstr "Log masuk / Daftar"

#, fuzzy
msgid "page.browserverification.header"
msgstr "Pengesahan pelayar"

#, fuzzy
msgid "page.login.text1"
msgstr "Untuk mengelakkan spam-bot daripada mencipta banyak akaun, kami perlu mengesahkan pelayar anda terlebih dahulu."

#, fuzzy
msgid "page.login.text2"
msgstr "Jika anda terperangkap dalam gelung tak terhingga, kami mengesyorkan memasang <a %(a_privacypass)s>Privacy Pass</a>."

#, fuzzy
msgid "page.login.text3"
msgstr "Ia juga mungkin membantu untuk mematikan penyekat iklan dan sambungan pelayar lain."

#, fuzzy
msgid "page.codes.title"
msgstr "Kod"

#, fuzzy
msgid "page.codes.heading"
msgstr "Peneroka Kod"

#, fuzzy
msgid "page.codes.intro"
msgstr "Terokai kod yang ditandakan pada rekod, mengikut awalan. Lajur “rekod” menunjukkan bilangan rekod yang ditandakan dengan kod dengan awalan yang diberikan, seperti yang dilihat dalam enjin carian (termasuk rekod metadata sahaja). Lajur “kod” menunjukkan berapa banyak kod sebenar mempunyai awalan yang diberikan."

#, fuzzy
msgid "page.codes.why_cloudflare"
msgstr "Halaman ini mungkin mengambil masa untuk dijana, sebab itu ia memerlukan captcha Cloudflare. <a %(a_donate)s>Ahli</a> boleh melangkau captcha."

#, fuzzy
msgid "page.codes.dont_scrape"
msgstr "Sila jangan mengikis halaman ini. Sebaliknya kami mengesyorkan <a %(a_import)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami, dan menjalankan <a %(a_software)s>kod sumber terbuka</a> kami. Data mentah boleh diterokai secara manual melalui fail JSON seperti <a %(a_json_file)s>yang ini</a>."

#, fuzzy
msgid "page.codes.prefix"
msgstr "Awalan"

#, fuzzy
msgid "common.form.go"
msgstr "Pergi"

#, fuzzy
msgid "common.form.reset"
msgstr "Tetapkan semula"

#, fuzzy
msgid "page.codes.search_archive_start"
msgstr "Cari Arkib Anna"

#, fuzzy
msgid "page.codes.bad_unicode"
msgstr "Amaran: kod mempunyai aksara Unicode yang salah, dan mungkin berkelakuan tidak betul dalam pelbagai situasi. Biner mentah boleh dinyahkod daripada perwakilan base64 dalam URL."

#, fuzzy
msgid "page.codes.known_code_prefix"
msgstr "Awalan kod yang diketahui “%(key)s”"

#, fuzzy
msgid "page.codes.code_prefix"
msgstr "Awalan"

#, fuzzy
msgid "page.codes.code_label"
msgstr "Label"

#, fuzzy
msgid "page.codes.code_description"
msgstr "Penerangan"

#, fuzzy
msgid "page.codes.code_url"
msgstr "URL untuk kod tertentu"

#, fuzzy
msgctxt "the %s should not be changed"
msgid "page.codes.s_substitution"
msgstr "“%%s” akan digantikan dengan nilai kod"

#, fuzzy
msgid "page.codes.generic_url"
msgstr "URL Am"

#, fuzzy
msgid "page.codes.code_website"
msgstr "Laman Web"

#, fuzzy
msgid "page.codes.record_starting_with"
msgid_plural "page.codes.records_starting_with"
msgstr[0] "%(count)s rekod sepadan dengan “%(prefix_label)s”"
msgstr[1] ""

#, fuzzy
msgid "page.codes.url_link"
msgstr "URL untuk kod tertentu: “%(url)s”"

#, fuzzy
msgid "page.codes.more"
msgstr "Lebih lanjut…"

#, fuzzy
msgid "page.codes.codes_starting_with"
msgstr "Kod bermula dengan “%(prefix_label)s”"

#, fuzzy
msgid "page.codes.index_of_dir_path"
msgstr "Indeks"

#, fuzzy
msgid "page.codes.records_prefix"
msgstr "rekod"

#, fuzzy
msgid "page.codes.records_codes"
msgstr "kod"

#, fuzzy
msgid "page.codes.fewer_than"
msgstr "Kurang daripada %(count)s rekod"

#, fuzzy
msgid "page.contact.dmca.form"
msgstr "Untuk tuntutan DMCA / hak cipta, gunakan <a %(a_copyright)s>borang ini</a>."

#, fuzzy
msgid "page.contact.dmca.delete"
msgstr "Sebarang cara lain untuk menghubungi kami mengenai tuntutan hak cipta akan dipadamkan secara automatik."

#, fuzzy
msgid "page.contact.checkboxes.text1"
msgstr "Kami sangat mengalu-alukan maklum balas dan soalan anda!"

#, fuzzy
msgid "page.contact.checkboxes.text2"
msgstr "Walau bagaimanapun, disebabkan jumlah spam dan emel tidak masuk akal yang kami terima, sila tandakan kotak untuk mengesahkan anda memahami syarat-syarat ini untuk menghubungi kami."

#, fuzzy
msgid "page.contact.checkboxes.copyright"
msgstr "Tuntutan hak cipta ke e-mel ini akan diabaikan; gunakan borang sebagai gantinya."

#, fuzzy
msgid "layout.index.header.banner.issues.partners_closed"
msgstr "Pelayan rakan kongsi tidak tersedia kerana penutupan hosting. Mereka sepatutnya beroperasi semula tidak lama lagi."

#, fuzzy
msgid "layout.index.header.banner.issues.memberships_extended"
msgstr "Keahlian akan dilanjutkan sewajarnya."

#, fuzzy
msgid "layout.index.footer.dont_email"
msgstr "Jangan emel kami untuk <a %(a_request)s>meminta buku</a><br>atau muat naik kecil (<10k) <a %(a_upload)s>muat naik</a>."

#, fuzzy
msgid "page.donate.please_include"
msgstr "Apabila bertanya soalan akaun atau derma, sertakan ID akaun anda, tangkapan skrin, resit, sebanyak mungkin maklumat. Kami hanya memeriksa emel kami setiap 1-2 minggu, jadi tidak menyertakan maklumat ini akan melambatkan sebarang penyelesaian."

#, fuzzy
msgid "page.contact.checkboxes.show_email_button"
msgstr "Tunjukkan e-mel"

#, fuzzy
msgid "page.copyright.title"
msgstr "Borang tuntutan DMCA / Hak Cipta"

#, fuzzy
msgid "page.copyright.intro"
msgstr "Jika anda mempunyai tuntutan DMCA atau hak cipta lain, sila isi borang ini dengan seberapa tepat yang mungkin. Jika anda menghadapi sebarang masalah, sila hubungi kami di alamat DMCA khusus kami: %(email)s. Sila ambil perhatian bahawa tuntutan yang dihantar melalui e-mel ke alamat ini tidak akan diproses, ia hanya untuk pertanyaan. Sila gunakan borang di bawah untuk menghantar tuntutan anda."

#, fuzzy
msgid "page.copyright.form.aa_urls"
msgstr "URL di Arkib Anna (diperlukan). Satu per baris. Sila hanya masukkan URL yang menerangkan edisi buku yang sama. Jika anda ingin membuat tuntutan untuk pelbagai buku atau pelbagai edisi, sila hantar borang ini beberapa kali."

#, fuzzy
msgid "page.copyright.form.aa_urls.note"
msgstr "Tuntutan yang menggabungkan pelbagai buku atau edisi akan ditolak."

#, fuzzy
msgid "page.copyright.form.name"
msgstr "Nama anda (diperlukan)"

#, fuzzy
msgid "page.copyright.form.address"
msgstr "Alamat (diperlukan)"

#, fuzzy
msgid "page.copyright.form.phone"
msgstr "Nombor telefon (diperlukan)"

#, fuzzy
msgid "page.copyright.form.email"
msgstr "E-mel (diperlukan)"

#, fuzzy
msgid "page.copyright.form.description"
msgstr "Penerangan jelas mengenai bahan sumber (diperlukan)"

#, fuzzy
msgid "page.copyright.form.isbns"
msgstr "ISBN bahan sumber (jika berkenaan). Satu per baris. Sila hanya masukkan yang sepadan dengan edisi yang anda laporkan tuntutan hak cipta."

#, fuzzy
msgid "page.copyright.form.openlib_urls"
msgstr "<a %(a_openlib)s>Open Library</a> URL bahan sumber, satu per baris. Sila luangkan masa untuk mencari bahan sumber anda di Open Library. Ini akan membantu kami mengesahkan tuntutan anda."

#, fuzzy
msgid "page.copyright.form.external_urls"
msgstr "URL kepada bahan sumber, satu per baris (diperlukan). Sila masukkan sebanyak mungkin, untuk membantu kami mengesahkan tuntutan anda (contohnya Amazon, WorldCat, Google Books, DOI)."

#, fuzzy
msgid "page.copyright.form.statement"
msgstr "Pernyataan dan tandatangan (diperlukan)"

#, fuzzy
msgid "page.copyright.form.submit_claim"
msgstr "Hantar tuntutan"

#, fuzzy
msgid "page.copyright.form.on_success"
msgstr "✅ Terima kasih kerana menghantar tuntutan hak cipta anda. Kami akan menyemaknya secepat mungkin. Sila muat semula halaman untuk menghantar yang lain."

#, fuzzy
msgid "page.copyright.form.on_failure"
msgstr "❌ Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi."

#, fuzzy
msgid "page.datasets.title"
msgstr "Datasets"

#, fuzzy
msgid "page.datasets.common.intro"
msgstr "Jika anda berminat untuk mencerminkan set data ini untuk tujuan <a %(a_archival)s>arkib</a> atau <a %(a_llm)s>latihan LLM</a>, sila hubungi kami."

#, fuzzy
msgid "page.datasets.intro.text2"
msgstr "Misi kami adalah untuk mengarkibkan semua buku di dunia (serta kertas kerja, majalah, dll), dan menjadikannya boleh diakses secara meluas. Kami percaya bahawa semua buku harus dicerminkan secara meluas, untuk memastikan redundansi dan ketahanan. Inilah sebabnya kami mengumpulkan fail dari pelbagai sumber. Sesetengah sumber adalah terbuka sepenuhnya dan boleh dicerminkan secara pukal (seperti Sci-Hub). Yang lain adalah tertutup dan melindungi, jadi kami cuba mengikis mereka untuk “membebaskan” buku mereka. Yang lain berada di antara."

#, fuzzy
msgid "page.datasets.intro.text3"
msgstr "Semua data kami boleh <a %(a_torrents)s>ditorrent</a>, dan semua metadata kami boleh <a %(a_anna_software)s>dihasilkan</a> atau <a %(a_elasticsearch)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB. Data mentah boleh diterokai secara manual melalui fail JSON seperti <a %(a_dbrecord)s>ini</a>."

#, fuzzy
msgid "page.datasets.overview.title"
msgstr "Gambaran Keseluruhan"

#, fuzzy
msgid "page.datasets.overview.text1"
msgstr "Di bawah adalah gambaran keseluruhan ringkas tentang sumber fail di Anna’s Archive."

#, fuzzy
msgid "page.datasets.overview.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.overview.size.header"
msgstr "Saiz"

#, fuzzy
msgid "page.datasets.overview.mirrored.header"
msgstr "%% dicerminkan oleh AA / torrents tersedia"

#, fuzzy
msgid "page.datasets.overview.mirrored.clarification"
msgstr "Peratusan bilangan fail"

#, fuzzy
msgid "page.datasets.overview.last_updated.header"
msgstr "Kali terakhir dikemas kini"

#, fuzzy
msgid "common.record_sources_mapping.lgrs.nonfiction_and_fiction"
msgstr "Bukan Fiksyen dan Fiksyen"

#, fuzzy
msgid "page.datasets.file"
msgid_plural "page.datasets.files"
msgstr[0] "%(count)s fail"
msgstr[1] ""

#, fuzzy
msgid "common.record_sources_mapping.scihub.via_lgli_scimag"
msgstr "Melalui Libgen.li “scimag”"

#, fuzzy
msgid "page.datasets.scihub_frozen_1"
msgstr "Sci-Hub: dibekukan sejak 2021; kebanyakannya tersedia melalui torrents"

#, fuzzy
msgid "page.datasets.scihub_frozen_2"
msgstr "Libgen.li: penambahan kecil sejak itu</div>"

#, fuzzy
msgid "common.record_sources_mapping.lgli.excluding_scimag"
msgstr "Tidak termasuk “scimag”"

#, fuzzy
msgid "page.datasets.lgli_fiction_is_behind"
msgstr "Torrents fiksyen ketinggalan (walaupun ID ~4-6M tidak ditorrent kerana bertindih dengan torrents Zlib kami)."

#, fuzzy
msgid "page.datasets.zlibzh.searchable"
msgstr "Koleksi “Cina” dalam Z-Library nampaknya sama dengan koleksi DuXiu kami, tetapi dengan MD5 yang berbeza. Kami mengecualikan fail-fail ini daripada torrents untuk mengelakkan penduaan, tetapi masih menunjukkannya dalam indeks carian kami."

#, fuzzy
msgid "common.record_sources_mapping.iacdl"
msgstr "Peminjaman Digital Terkawal IA"

#, fuzzy
msgid "page.datasets.iacdl.searchable"
msgstr "98%%+ fail boleh dicari."

#, fuzzy
msgid "page.datasets.overview.total"
msgstr "Jumlah"

#, fuzzy
msgid "page.datasets.overview.excluding_duplicates"
msgstr "Tidak termasuk pendua"

#, fuzzy
msgid "page.datasets.overview.text4"
msgstr "Oleh kerana perpustakaan bayangan sering menyelaraskan data antara satu sama lain, terdapat pertindihan yang ketara antara perpustakaan. Itulah sebabnya nombor tidak menjumlahkan kepada jumlah keseluruhan."

#, fuzzy
msgid "page.datasets.overview.text5"
msgstr "Peratusan “dicerminkan dan disemai oleh Arkib Anna” menunjukkan berapa banyak fail yang kami cerminkan sendiri. Kami menyemai fail-fail tersebut secara pukal melalui torrent, dan menjadikannya tersedia untuk muat turun terus melalui laman web rakan kongsi."

#, fuzzy
msgid "page.datasets.source_libraries.title"
msgstr "Perpustakaan sumber"

#, fuzzy
msgid "page.datasets.source_libraries.text1"
msgstr "Sesetengah perpustakaan sumber mempromosikan perkongsian data mereka secara pukal melalui torrents, manakala yang lain tidak berkongsi koleksi mereka dengan mudah. Dalam kes yang terakhir, Arkib Anna cuba untuk mengikis koleksi mereka, dan membuatnya tersedia (lihat halaman <a %(a_torrents)s>Torrents</a> kami). Terdapat juga situasi di antara, contohnya, di mana perpustakaan sumber bersedia untuk berkongsi, tetapi tidak mempunyai sumber untuk berbuat demikian. Dalam kes tersebut, kami juga cuba untuk membantu."

#, fuzzy
msgid "page.datasets.source_libraries.text2"
msgstr "Di bawah adalah gambaran keseluruhan bagaimana kami berinteraksi dengan pelbagai perpustakaan sumber."

#, fuzzy
msgid "page.datasets.sources.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.sources.files.header"
msgstr "Fail"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.metadata1"
msgstr "%(icon)s <a %(dbdumps)s>Pembuangan pangkalan data HTTP</a> harian"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files1"
msgstr "%(icon)s Torrent automatik untuk <a %(nonfiction)s>Bukan Fiksyen</a> dan <a %(fiction)s>Fiksyen</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_rs.files2"
msgstr "%(icon)s Arkib Anna menguruskan koleksi <a %(covers)s>torrents kulit buku</a>"

#, fuzzy
msgid "common.record_sources_mapping.scihub_scimag"
msgstr "Sci-Hub / Libgen “scimag”"

#, fuzzy
msgid "page.datasets.sources.scihub.metadata1"
msgstr "%(icon)s Sci-Hub telah membekukan fail baru sejak 2021."

#, fuzzy
msgid "page.datasets.sources.scihub.metadata2"
msgstr "%(icon)s Dump metadata tersedia <a %(scihub1)s>di sini</a> dan <a %(scihub2)s>di sini</a>, serta sebagai sebahagian daripada <a %(libgenli)s>pangkalan data Libgen.li</a> (yang kami gunakan)"

#, fuzzy
msgid "page.datasets.sources.scihub.files1"
msgstr "%(icon)s Torrent data tersedia <a %(scihub1)s>di sini</a>, <a %(scihub2)s>di sini</a>, dan <a %(libgenli)s>di sini</a>"

#, fuzzy
msgid "page.datasets.sources.scihub.files2"
msgstr "%(icon)s Beberapa fail baru <a %(libgenrs)s>sedang</a> <a %(libgenli)s>ditambah</a> ke “scimag” Libgen, tetapi tidak cukup untuk menjamin torrents baru"

#, fuzzy
msgid "page.datasets.sources.libgen_li.metadata1"
msgstr "%(icon)s Dump pangkalan data HTTP suku tahunan <a %(dbdumps)s>di sini</a>"

#, fuzzy
msgid "page.datasets.sources.libgen_li.files1"
msgstr "%(icon)s Torrent Bukan Fiksyen dikongsi dengan Libgen.rs (dan dicerminkan <a %(libgenli)s>di sini</a>)."

#, fuzzy
msgid "page.datasets.sources.libgen_li.collab"
msgstr "%(icon)s Arkib Anna dan Libgen.li secara kolaboratif mengurus koleksi <a %(comics)s>buku komik</a>, <a %(magazines)s>majalah</a>, <a %(standarts)s>dokumen standard</a>, dan <a %(fiction)s>fiksyen (berbeza dari Libgen.rs)</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus"
msgstr "%(icon)s Koleksi “fiction_rus” mereka (fiksyen Rusia) tidak mempunyai torrent khusus, tetapi dilindungi oleh torrent dari pihak lain, dan kami menyimpan <a %(fiction_rus)s>cermin</a>."

#, fuzzy
msgid "page.datasets.sources.zlib.metadata_and_files"
msgstr "%(icon)s Arkib Anna dan Z-Library secara bersama-sama mengurus koleksi <a %(metadata)s>metadata Z-Library</a> dan <a %(files)s>fail Z-Library</a>"

#, fuzzy
msgid "page.datasets.sources.ia.metadata1"
msgstr "%(icon)s Beberapa metadata tersedia melalui <a %(openlib)s>Open Library database dumps</a>, tetapi ia tidak merangkumi keseluruhan koleksi IA"

#, fuzzy
msgid "page.datasets.sources.ia.metadata2"
msgstr "%(icon)s Tiada metadata dumps yang mudah diakses untuk keseluruhan koleksi mereka"

#, fuzzy
msgid "page.datasets.sources.ia.metadata3"
msgstr "%(icon)s Arkib Anna mengurus koleksi <a %(ia)s>metadata IA</a>"

#, fuzzy
msgid "page.datasets.sources.ia.files1"
msgstr "%(icon)s Fail hanya tersedia untuk dipinjam secara terhad, dengan pelbagai sekatan akses"

#, fuzzy
msgid "page.datasets.sources.ia.files2"
msgstr "%(icon)s Arkib Anna mengurus koleksi <a %(ia)s>fail IA</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata1"
msgstr "%(icon)s Pelbagai pangkalan data metadata berselerak di internet China; walaupun sering kali pangkalan data berbayar"

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata2"
msgstr "%(icon)s Tiada metadata dumps yang mudah diakses untuk keseluruhan koleksi mereka."

#, fuzzy
msgid "page.datasets.sources.duxiu.metadata3"
msgstr "%(icon)s Arkib Anna mengurus koleksi <a %(duxiu)s>metadata DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.duxiu.files1"
msgstr "%(icon)s Pelbagai pangkalan data fail berselerak di internet China; walaupun sering kali pangkalan data berbayar"

#, fuzzy
msgid "page.datasets.sources.duxiu.files2"
msgstr "%(icon)s Kebanyakan fail hanya boleh diakses menggunakan akaun premium BaiduYun; kelajuan muat turun yang perlahan."

#, fuzzy
msgid "page.datasets.sources.duxiu.files3"
msgstr "%(icon)s Arkib Anna mengurus koleksi <a %(duxiu)s>fail DuXiu</a>"

#, fuzzy
msgid "page.datasets.sources.uploads.metadata_and_files"
msgstr "%(icon)s Pelbagai sumber kecil atau satu kali. Kami menggalakkan orang ramai untuk memuat naik ke perpustakaan bayangan lain terlebih dahulu, tetapi kadang-kadang orang mempunyai koleksi yang terlalu besar untuk orang lain menyusun, walaupun tidak cukup besar untuk memerlukan kategori mereka sendiri."

#, fuzzy
msgid "page.datasets.metadata_only_sources.title"
msgstr "Sumber metadata sahaja"

#, fuzzy
msgid "page.datasets.metadata_only_sources.text1"
msgstr "Kami juga memperkayakan koleksi kami dengan sumber metadata sahaja, yang boleh kami padankan dengan fail, contohnya menggunakan nombor ISBN atau medan lain. Di bawah adalah gambaran keseluruhan sumber tersebut. Sekali lagi, sesetengah sumber ini adalah terbuka sepenuhnya, manakala untuk yang lain kami perlu mengikisnya."

#, fuzzy
msgid "page.faq.metadata.inspiration"
msgstr "Inspirasi kami untuk mengumpul metadata adalah matlamat Aaron Swartz untuk \"satu halaman web untuk setiap buku yang pernah diterbitkan\", yang mana dia mencipta <a %(a_openlib)s>Open Library</a>. Projek itu telah berjaya, tetapi kedudukan unik kami membolehkan kami mendapatkan metadata yang mereka tidak boleh. Satu lagi inspirasi adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku yang ada di dunia</a>, supaya kami boleh mengira berapa banyak buku yang masih perlu kami selamatkan."

#, fuzzy
msgid "page.datasets.metadata_only_sources.text2"
msgstr "Perhatikan bahawa dalam carian metadata, kami menunjukkan rekod asal. Kami tidak melakukan sebarang penggabungan rekod."

#, fuzzy
msgid "page.datasets.sources.last_updated.header"
msgstr "Kali terakhir dikemas kini"

#, fuzzy
msgid "page.datasets.sources.openlib.metadata1"
msgstr "%(icon)s Bulanan <a %(dbdumps)s>database dumps</a>"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata1"
msgstr "%(icon)s Tidak tersedia secara langsung dalam jumlah besar, dilindungi daripada pengikisan"

#, fuzzy
msgid "page.datasets.sources.worldcat.metadata2"
msgstr "%(icon)s Arkib Anna menguruskan koleksi <a %(worldcat)s>metadata OCLC (WorldCat)</a>"

#, fuzzy
msgid "page.datasets.unified_database.title"
msgstr "Pangkalan data bersatu"

#, fuzzy
msgid "page.datasets.unified_database.text1"
msgstr "Kami menggabungkan semua sumber di atas ke dalam satu pangkalan data bersatu yang kami gunakan untuk menyajikan laman web ini. Pangkalan data bersatu ini tidak tersedia secara langsung, tetapi kerana Arkib Anna adalah sumber terbuka sepenuhnya, ia boleh dengan mudah <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB. Skrip di halaman tersebut akan secara automatik memuat turun semua metadata yang diperlukan dari sumber yang disebutkan di atas."

#, fuzzy
msgid "page.datasets.unified_database.text2"
msgstr "Jika anda ingin meneroka data kami sebelum menjalankan skrip tersebut secara tempatan, anda boleh melihat fail JSON kami, yang menghubungkan lebih lanjut kepada fail JSON lain. <a %(a_json)s>Fail ini</a> adalah titik permulaan yang baik."

#, fuzzy
msgid "page.datasets.duxiu.title"
msgstr "DuXiu 读秀"

#, fuzzy
msgid "page.datasets.duxiu.see_blog_post"
msgstr "Diadaptasi daripada <a %(a_href)s>catatan blog</a> kami."

#, fuzzy
msgid "page.datasets.duxiu.description"
msgstr "<a %(duxiu_link)s>Duxiu</a> adalah pangkalan data besar buku yang diimbas, dicipta oleh <a %(superstar_link)s>SuperStar Digital Library Group</a>. Kebanyakannya adalah buku akademik, diimbas untuk menjadikannya tersedia secara digital kepada universiti dan perpustakaan. Untuk penonton berbahasa Inggeris kami, <a %(princeton_link)s>Princeton</a> dan <a %(uw_link)s>University of Washington</a> mempunyai gambaran keseluruhan yang baik. Terdapat juga artikel yang sangat baik memberikan lebih latar belakang: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>."

#, fuzzy
msgid "page.datasets.duxiu.description2"
msgstr "Buku-buku dari Duxiu telah lama dipirate di internet China. Biasanya ia dijual kurang dari satu dolar oleh penjual semula. Ia biasanya diedarkan menggunakan setara Google Drive di China, yang sering digodam untuk membolehkan lebih banyak ruang simpanan. Beberapa butiran teknikal boleh didapati <a %(link1)s>di sini</a> dan <a %(link2)s>di sini</a>."

#, fuzzy
msgid "page.datasets.duxiu.description3"
msgstr "Walaupun buku-buku telah diedarkan secara separa awam, agak sukar untuk memperolehnya dalam jumlah besar. Kami meletakkan ini tinggi dalam senarai TODO kami, dan memperuntukkan beberapa bulan kerja sepenuh masa untuknya. Walau bagaimanapun, pada akhir 2023 seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberitahu kami bahawa mereka telah melakukan semua kerja ini — dengan kos yang besar. Mereka berkongsi koleksi penuh dengan kami, tanpa mengharapkan apa-apa balasan, kecuali jaminan pemeliharaan jangka panjang. Benar-benar luar biasa."

#, fuzzy
msgid "page.datasets.common.resources"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.common.total_files"
msgstr "Jumlah fail: %(count)s"

#, fuzzy
msgid "page.datasets.common.total_filesize"
msgstr "Jumlah saiz fail: %(size)s"

#, fuzzy
msgid "page.datasets.common.mirrored_file_count"
msgstr "Fail yang dicerminkan oleh Arkib Anna: %(count)s (%(percent)s%%)"

#, fuzzy
msgid "page.datasets.common.last_updated"
msgstr "Kemas kini terakhir: %(date)s"

#, fuzzy
msgid "page.datasets.common.aa_torrents"
msgstr "Torrent oleh Arkib Anna"

#, fuzzy
msgid "page.datasets.common.aa_example_record"
msgstr "Contoh rekod di Arkib Anna"

#, fuzzy
msgid "page.datasets.duxiu.blog_post"
msgstr "Catatan blog kami tentang data ini"

#, fuzzy
msgid "page.datasets.common.import_scripts"
msgstr "Skrip untuk mengimport metadata"

#, fuzzy
msgid "page.datasets.common.aac"
msgstr "Format Kontena Arkib Anna"

#, fuzzy
msgid "page.datasets.duxiu.raw_notes.title"
msgstr "Maklumat lanjut daripada sukarelawan kami (nota mentah):"

#, fuzzy
msgid "page.datasets.ia.title"
msgstr "IA Controlled Digital Lending"

#, fuzzy
msgid "page.datasets.ia.description"
msgstr "Set data ini berkait rapat dengan <a %(a_datasets_openlib)s>set data Open Library</a>. Ia mengandungi pengikisan semua metadata dan sebahagian besar fail dari Perpustakaan Peminjaman Digital Terkawal IA. Kemas kini dikeluarkan dalam <a %(a_aac)s>format Kontena Arkib Anna</a>."

#, fuzzy
msgid "page.datasets.ia.description2"
msgstr "Rekod-rekod ini dirujuk secara langsung dari set data Open Library, tetapi juga mengandungi rekod yang tidak terdapat dalam Open Library. Kami juga mempunyai beberapa fail data yang dikikis oleh ahli komuniti selama bertahun-tahun."

#, fuzzy
msgid "page.datasets.ia.description3"
msgstr "Koleksi ini terdiri daripada dua bahagian. Anda memerlukan kedua-dua bahagian untuk mendapatkan semua data (kecuali torrent yang telah digantikan, yang dicoretkan pada halaman torrent)."

#, fuzzy
msgid "page.datasets.ia.part1"
msgstr "keluaran pertama kami, sebelum kami menstandardkan pada format <a %(a_aac)s>Kontena Arkib Anna (AAC)</a>. Mengandungi metadata (sebagai json dan xml), pdf (daripada sistem pinjaman digital acsm dan lcpdf), dan lakaran muka depan."

#, fuzzy
msgid "page.datasets.ia.part2"
msgstr "keluaran baru secara beransur-ansur, menggunakan AAC. Hanya mengandungi metadata dengan cap masa selepas 2023-01-01, kerana selebihnya sudah diliputi oleh \"ia\". Juga semua fail pdf, kali ini daripada sistem pinjaman acsm dan \"bookreader\" (pembaca web IA). Walaupun nama tidak tepat, kami masih mengisi fail bookreader ke dalam koleksi ia2_acsmpdf_files, kerana mereka saling eksklusif."

#, fuzzy
msgid "page.datasets.common.main_website"
msgstr "Laman web utama %(source)s"

#, fuzzy
msgid "page.datasets.ia.ia_lending"
msgstr "Perpustakaan Pinjaman Digital"

#, fuzzy
msgid "page.datasets.common.metadata_docs"
msgstr "Dokumentasi metadata (kebanyakan medan)"

#, fuzzy
msgid "page.datasets.isbn_ranges.title"
msgstr "Maklumat negara ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.text1"
msgstr "Agensi ISBN Antarabangsa secara berkala mengeluarkan julat yang telah diperuntukkan kepada agensi ISBN kebangsaan. Daripada ini, kita boleh menentukan negara, wilayah, atau kumpulan bahasa yang dimiliki ISBN ini. Kami kini menggunakan data ini secara tidak langsung, melalui perpustakaan Python <a %(a_isbnlib)s>isbnlib</a>."

#, fuzzy
msgid "page.datasets.isbn_ranges.resources"
msgstr "Sumber"

#, fuzzy
msgid "page.datasets.isbn_ranges.last_updated"
msgstr "Kali terakhir dikemas kini: %(isbn_country_date)s (%(link)s)"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_website"
msgstr "Laman web ISBN"

#, fuzzy
msgid "page.datasets.isbn_ranges.isbn_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.title"
msgstr "Libgen.li"

#, fuzzy
msgid "page.datasets.libgen_li.description1"
msgstr "Untuk latar belakang tentang pelbagai cabang Library Genesis, lihat halaman untuk <a %(a_libgen_rs)s>Libgen.rs</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description2"
msgstr "Libgen.li mengandungi kebanyakan kandungan dan metadata yang sama seperti Libgen.rs, tetapi mempunyai beberapa koleksi tambahan, iaitu komik, majalah, dan dokumen standard. Ia juga telah mengintegrasikan <a %(a_scihub)s>Sci-Hub</a> ke dalam metadata dan enjin carian, yang kami gunakan untuk pangkalan data kami."

#, fuzzy
msgid "page.datasets.libgen_li.description3"
msgstr "Metadata untuk perpustakaan ini boleh didapati secara percuma <a %(a_libgen_li)s>di libgen.li</a>. Walau bagaimanapun, pelayan ini lambat dan tidak menyokong penyambungan semula sambungan yang terputus. Fail yang sama juga boleh didapati di <a %(a_ftp)s>pelayan FTP</a>, yang berfungsi dengan lebih baik."

#, fuzzy
msgid "page.datasets.libgen_li.description4.torrents"
msgstr "Torrent tersedia untuk kebanyakan kandungan tambahan, terutamanya torrent untuk komik, majalah, dan dokumen standard telah dikeluarkan dengan kerjasama Arkib Anna."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_torrents"
msgstr "Koleksi fiksyen mempunyai torrent sendiri (berbeza dari <a %(a_href)s>Libgen.rs</a>) bermula pada %(start)s."

#, fuzzy
msgid "page.datasets.libgen_li.description4.fiction_rus"
msgstr "Menurut pentadbir Libgen.li, koleksi “fiction_rus” (fiksyen Rusia) sepatutnya dilindungi oleh torrent yang dikeluarkan secara berkala dari <a %(a_booktracker)s>booktracker.org</a>, terutamanya torrent <a %(a_flibusta)s>flibusta</a> dan <a %(a_librusec)s>lib.rus.ec</a> (yang kami cermin <a %(a_torrents)s>di sini</a>, walaupun kami belum menetapkan torrent mana yang sepadan dengan fail mana)."

#, fuzzy
msgid "page.datasets.libgen_li.description4.stats"
msgstr "Statistik untuk semua koleksi boleh didapati <a %(a_href)s>di laman web libgen</a>."

#, fuzzy
msgid "page.datasets.libgen_li.description4.1"
msgstr "Buku bukan fiksyen juga kelihatan telah menyimpang, tetapi tanpa aliran baru. Nampaknya ini telah berlaku sejak awal 2022, walaupun kami belum mengesahkannya."

#, fuzzy
msgid "page.datasets.libgen_li.description4.omissions"
msgstr "Julat tertentu tanpa torrent (seperti julat fiksyen f_3463000 hingga f_4260000) mungkin fail Z-Library (atau pendua lain), walaupun kami mungkin ingin melakukan deduplikasi dan membuat torrent untuk fail unik lgli dalam julat ini."

#, fuzzy
msgid "page.datasets.libgen_li.description5"
msgstr "Perhatikan bahawa fail torrent yang merujuk kepada “libgen.is” adalah cermin secara eksplisit dari <a %(a_libgen)s>Libgen.rs</a> (“.is” adalah domain berbeza yang digunakan oleh Libgen.rs)."

#, fuzzy
msgid "page.datasets.libgen_li.description6"
msgstr "Sumber yang berguna dalam menggunakan metadata adalah <a %(a_href)s>halaman ini</a>."

#, fuzzy
msgid "page.datasets.libgen_li.fiction_torrents"
msgstr "Torrent fiksyen di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_li.comics_torrents"
msgstr "Torrent komik di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_li.magazines_torrents"
msgstr "Torrent majalah di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_li.standarts_torrents"
msgstr "Torrent dokumen standard di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_li.fiction_rus_torrents"
msgstr "Torrent fiksyen Rusia di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.libgen_li.link_metadata_ftp"
msgstr "Metadata melalui FTP"

#, fuzzy
msgid "page.datasets.libgen_li.metadata_structure"
msgstr "Maklumat medan metadata"

#, fuzzy
msgid "page.datasets.libgen_li.mirrors"
msgstr "Cermin dari torrent lain (dan torrent fiksyen dan komik unik)"

#, fuzzy
msgid "page.datasets.libgen_li.forum"
msgstr "Forum perbincangan"

#, fuzzy
msgid "page.datasets.libgen_li.comics_announcement"
msgstr "Catatan blog kami tentang pelepasan buku komik"

#, fuzzy
msgid "page.datasets.libgen_rs.title"
msgstr "Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.story"
msgstr "Kisah ringkas tentang pelbagai cabang Library Genesis (atau “Libgen”) adalah bahawa dari masa ke masa, orang yang terlibat dengan Library Genesis mengalami perpecahan, dan pergi ke arah masing-masing."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_fun"
msgstr "Versi “.fun” dicipta oleh pengasas asal. Ia sedang diperbaharui untuk versi baru yang lebih teragih."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_rs"
msgstr "Versi “.rs” mempunyai data yang sangat serupa, dan paling konsisten mengeluarkan koleksi mereka dalam torrent pukal. Ia dibahagikan secara kasar kepada bahagian “fiksyen” dan “bukan fiksyen”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.rus_dot_ec"
msgstr "Asalnya di “http://gen.lib.rus.ec”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dot_li"
msgstr "Versi <a %(a_li)s>“.li”</a> mempunyai koleksi komik yang besar, serta kandungan lain, yang belum (lagi) tersedia untuk muat turun pukal melalui torrent. Ia mempunyai koleksi torrent berasingan untuk buku fiksyen, dan mengandungi metadata <a %(a_scihub)s>Sci-Hub</a> dalam pangkalan datanya."

#, fuzzy
msgid "page.datasets.libgen_rs.story.dontexist"
msgstr "Menurut <a %(a_mhut)s>kiriman forum</a> ini, Libgen.li pada asalnya dihoskan di “http://free-books.dontexist.com”."

#, fuzzy
msgid "page.datasets.libgen_rs.story.zlib"
msgstr "<a %(a_zlib)s>Z-Library</a> dalam beberapa pengertian juga merupakan cabang dari Library Genesis, walaupun mereka menggunakan nama yang berbeza untuk projek mereka."

#, fuzzy
msgid "page.datasets.libgen_rs.description.about"
msgstr "Halaman ini adalah tentang versi “.rs”. Ia terkenal kerana secara konsisten menerbitkan metadata dan kandungan penuh katalog bukunya. Koleksi bukunya dibahagikan antara bahagian fiksyen dan bukan fiksyen."

#, fuzzy
msgid "page.datasets.libgen_rs.description.metadata"
msgstr "Sumber yang berguna dalam menggunakan metadata adalah <a %(a_metadata)s>halaman ini</a> (menyekat julat IP, VPN mungkin diperlukan)."

#, fuzzy
msgid "page.datasets.libgen_rs.description.new_torrents"
msgstr "Sehingga 2024-03, torrent baru diposkan dalam <a %(a_href)s>benang forum ini</a> (menyekat julat IP, VPN mungkin diperlukan)."

#, fuzzy
msgid "page.datasets.libgen_rs.nonfiction_torrents"
msgstr "Torrent Bukan Fiksyen di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.fiction_torrents"
msgstr "Torrent Fiksyen di Arkib Anna"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata"
msgstr "Metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_metadata_fields"
msgstr "Maklumat medan metadata Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_nonfiction"
msgstr "Torrent Bukan Fiksyen Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_fiction"
msgstr "Torrent Fiksyen Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.link_forum"
msgstr "Forum Perbincangan Libgen.rs"

#, fuzzy
msgid "page.datasets.libgen_rs.aa_covers"
msgstr "Torrent oleh Arkib Anna (kulit buku)"

#, fuzzy
msgid "page.datasets.libgen_rs.covers_announcement"
msgstr "Blog kami tentang pelepasan kulit buku"

#, fuzzy
msgid "page.datasets.libgen_rs.about"
msgstr "Library Genesis terkenal kerana sudah bermurah hati membuat data mereka tersedia secara pukal melalui torrent. Koleksi Libgen kami terdiri daripada data tambahan yang mereka tidak lepaskan secara langsung, dengan kerjasama mereka. Terima kasih banyak kepada semua yang terlibat dengan Library Genesis kerana bekerjasama dengan kami!"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.title"
msgstr "Pelepasan 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.intro"
msgstr "<a %(blog_post)s>Pelepasan pertama</a> ini agak kecil: kira-kira 300GB kulit buku dari cabang Libgen.rs, kedua-dua fiksyen dan bukan fiksyen. Mereka disusun dengan cara yang sama seperti bagaimana mereka muncul di libgen.rs, contohnya:"

#, fuzzy
msgid "page.datasets.libgen_rs.release1.nonfiction"
msgstr "%(example)s untuk buku bukan fiksyen."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.fiction"
msgstr "%(example)s untuk buku fiksyen."

#, fuzzy
msgid "page.datasets.libgen_rs.release1.outro"
msgstr "Sama seperti dengan koleksi Z-Library, kami meletakkan semuanya dalam fail .tar besar, yang boleh dipasang menggunakan <a %(a_ratarmount)s>ratarmount</a> jika anda ingin menyajikan fail secara langsung."

#, fuzzy
msgid "page.datasets.worldcat.title"
msgstr "OCLC (WorldCat)"

#, fuzzy
msgid "page.datasets.worldcat.description"
msgstr "<a %(a_worldcat)s>WorldCat</a> adalah pangkalan data proprietari oleh organisasi bukan untung <a %(a_oclc)s>OCLC</a>, yang mengumpulkan rekod metadata dari perpustakaan di seluruh dunia. Ia mungkin koleksi metadata perpustakaan terbesar di dunia."

#, fuzzy
msgid "page.datasets.worldcat.description2.label"
msgstr "Oktober 2023, keluaran awal:"

#, fuzzy
msgid "page.datasets.worldcat.description2"
msgstr "Pada Oktober 2023 kami <a %(a_scrape)s>melepaskan</a> pengikisan menyeluruh pangkalan data OCLC (WorldCat), dalam <a %(a_aac)s>format Kontena Arkib Anna</a>."

#, fuzzy
msgid "page.datasets.worldcat.torrents"
msgstr "Torrent oleh Arkib Anna"

#, fuzzy
msgid "page.datasets.worldcat.blog_announcement"
msgstr "Catatan blog kami mengenai data ini"

#, fuzzy
msgid "page.datasets.openlib.title"
msgstr "Open Library"

#, fuzzy
msgid "page.datasets.openlib.description"
msgstr "Open Library adalah projek sumber terbuka oleh Internet Archive untuk mengkatalogkan setiap buku di dunia. Ia mempunyai salah satu operasi pengimbasan buku terbesar di dunia, dan mempunyai banyak buku yang tersedia untuk pinjaman digital. Katalog metadata bukunya tersedia secara percuma untuk dimuat turun, dan disertakan di Arkib Anna (walaupun tidak dalam carian buat masa ini, kecuali jika anda secara eksplisit mencari ID Open Library)."

#, fuzzy
msgid "page.datesets.openlib.link_metadata"
msgstr "Metadata"

#, fuzzy
msgid "page.datasets.isbndb.release1.title"
msgstr "Keluaran 1 (2022-10-31)"

#, fuzzy
msgid "page.datasets.isbndb.release1.text1"
msgstr "Ini adalah dump dari banyak panggilan ke isbndb.com semasa September 2022. Kami cuba meliputi semua julat ISBN. Ini adalah kira-kira 30.9 juta rekod. Di laman web mereka, mereka mendakwa bahawa mereka sebenarnya mempunyai 32.6 juta rekod, jadi kami mungkin terlepas beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah."

#, fuzzy
msgid "page.datasets.isbndb.release1.text2"
msgstr "Respons JSON hampir mentah dari pelayan mereka. Satu isu kualiti data yang kami perhatikan adalah bahawa untuk nombor ISBN-13 yang bermula dengan awalan yang berbeza daripada \"978-\", mereka masih termasuk medan \"isbn\" yang hanya nombor ISBN-13 dengan tiga nombor pertama dipotong (dan digit semak dikira semula). Ini jelas salah, tetapi begitulah cara mereka melakukannya, jadi kami tidak mengubahnya."

#, fuzzy
msgid "page.datasets.isbndb.release1.text3"
msgstr "Satu lagi isu yang mungkin anda hadapi adalah fakta bahawa medan \"isbn13\" mempunyai pendua, jadi anda tidak boleh menggunakannya sebagai kunci utama dalam pangkalan data. Gabungan medan \"isbn13\" + \"isbn\" nampaknya unik."

#, fuzzy
msgid "page.datasets.scihub.title"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.datasets.scihub.description1"
msgstr "Untuk latar belakang mengenai Sci-Hub, sila rujuk kepada <a %(a_scihub)s>laman web rasmi</a>, <a %(a_wikipedia)s>halaman Wikipedia</a>, dan <a %(a_radiolab)s>temu bual podcast</a> ini."

#, fuzzy
msgid "page.datasets.scihub.description2"
msgstr "Perhatikan bahawa Sci-Hub telah <a %(a_reddit)s>dibekukan sejak 2021</a>. Ia telah dibekukan sebelum ini, tetapi pada tahun 2021 beberapa juta kertas kerja telah ditambah. Namun, beberapa kertas kerja terhad masih ditambah ke dalam koleksi “scimag” Libgen, walaupun tidak mencukupi untuk menjamin torrent pukal baru."

#, fuzzy
msgid "page.datasets.scihub.description3"
msgstr "Kami menggunakan metadata Sci-Hub seperti yang disediakan oleh <a %(a_libgen_li)s>Libgen.li</a> dalam koleksi “scimag”nya. Kami juga menggunakan dataset <a %(a_dois)s>dois-2022-02-12.7z</a>."

#, fuzzy
msgid "page.datasets.scihub.description4"
msgstr "Perhatikan bahawa torrent “smarch” adalah <a %(a_smarch)s>usang</a> dan oleh itu tidak termasuk dalam senarai torrent kami."

#, fuzzy
msgid "page.datasets.scihub.aa_torrents"
msgstr "Torrent di Arkib Anna"

#, fuzzy
msgid "page.datasets.scihub.link_metadata"
msgstr "Metadata dan torrent"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_rs_torrents"
msgstr "Torrent di Libgen.rs"

#, fuzzy
msgid "page.datasets.scihub.link_libgen_li_torrents"
msgstr "Torrent di Libgen.li"

#, fuzzy
msgid "page.datasets.scihub.link_paused"
msgstr "Kemas kini di Reddit"

#, fuzzy
msgid "page.datasets.scihub.link_wikipedia"
msgstr "Halaman Wikipedia"

#, fuzzy
msgid "page.datasets.scihub.link_podcast"
msgstr "Temu bual podcast"

#, fuzzy
msgid "page.datasets.upload.title"
msgstr "Muat naik ke Arkib Anna"

#, fuzzy
msgid "page.datasets.upload.overview"
msgstr "Gambaran dari <a %(a1)s>halaman datasets</a>."

#, fuzzy
msgid "page.datasets.upload.description"
msgstr "Pelbagai sumber kecil atau sekali sahaja. Kami menggalakkan orang ramai untuk memuat naik ke perpustakaan bayangan lain terlebih dahulu, tetapi kadang-kadang orang mempunyai koleksi yang terlalu besar untuk orang lain menyusun, walaupun tidak cukup besar untuk menjamin kategori mereka sendiri."

#, fuzzy
msgid "page.datasets.upload.subcollections"
msgstr "Koleksi “muat naik” dibahagikan kepada subkoleksi yang lebih kecil, yang ditunjukkan dalam AACID dan nama torrent. Semua subkoleksi pertama kali diduplikasi terhadap koleksi utama, walaupun fail JSON “upload_records” metadata masih mengandungi banyak rujukan kepada fail asal. Fail bukan buku juga telah dikeluarkan daripada kebanyakan subkoleksi, dan biasanya <em>tidak</em> dinyatakan dalam “upload_records” JSON."

#, fuzzy
msgid "page.datasets.upload.subsubcollections"
msgstr "Banyak subkoleksi itu sendiri terdiri daripada sub-sub-koleksi (contohnya daripada sumber asal yang berbeza), yang diwakili sebagai direktori dalam medan “filepath”."

#, fuzzy
msgid "page.datasets.upload.subs.heading"
msgstr "Subkoleksi adalah:"

#, fuzzy
msgid "page.datasets.upload.subs.subcollection"
msgstr "Subkoleksi"

#, fuzzy
msgid "page.datasets.upload.subs.notes"
msgstr "Nota"

#, fuzzy
msgid "page.datasets.upload.action.browse"
msgstr "semak imbas"

#, fuzzy
msgid "page.datasets.upload.action.search"
msgstr "cari"

#, fuzzy
msgid "page.datasets.upload.source.aaaaarg"
msgstr "Daripada <a %(a_href)s>aaaaarg.fail</a>. Nampaknya agak lengkap. Daripada sukarelawan kami “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.acm"
msgstr "Daripada torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Mempunyai pertindihan yang agak tinggi dengan koleksi kertas sedia ada, tetapi sangat sedikit padanan MD5, jadi kami memutuskan untuk menyimpannya sepenuhnya."

#, fuzzy
msgid "page.datasets.upload.source.airitibooks"
msgstr "Pengikisan <q>iRead eBooks</q> (= secara fonetik <q>ai rit i-books</q>; airitibooks.com), oleh sukarelawan <q>j</q>. Berkaitan dengan metadata <q>airitibooks</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.alexandrina"
msgstr "Dari koleksi <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sebahagiannya dari sumber asal, sebahagiannya dari the-eye.eu, sebahagiannya dari cermin lain."

#, fuzzy
msgid "page.datasets.upload.source.bibliotik"
msgstr "Daripada laman web torrent buku persendirian, <a %(a_href)s>Bibliotik</a> (sering dirujuk sebagai “Bib”), di mana buku-buku dibundel ke dalam torrent mengikut nama (A.torrent, B.torrent) dan diedarkan melalui the-eye.eu."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_cadal"
msgstr "Daripada sukarelawan kami “bpb9v”. Untuk maklumat lanjut mengenai <a %(a_href)s>CADAL</a>, lihat nota dalam <a %(a_duxiu)s>halaman set data DuXiu</a> kami."

#, fuzzy
msgid "page.datasets.upload.source.bpb9v_direct"
msgstr "Lebih banyak daripada sukarelawan kami “bpb9v”, kebanyakannya fail DuXiu, serta folder “WenQu” dan “SuperStar_Journals” (SuperStar adalah syarikat di belakang DuXiu)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_chinese"
msgstr "Daripada sukarelawan kami “cgiym”, teks Cina daripada pelbagai sumber (diwakili sebagai subdirektori), termasuk daripada <a %(a_href)s>China Machine Press</a> (penerbit utama Cina)."

#, fuzzy
msgid "page.datasets.upload.source.cgiym_more"
msgstr "Koleksi bukan Cina (diwakili sebagai subdirektori) daripada sukarelawan kami “cgiym”."

#, fuzzy
msgid "page.datasets.upload.source.chinese_architecture"
msgstr "Pengikisan buku tentang seni bina Cina, oleh sukarelawan <q>cm</q>: <q>Saya mendapatkannya dengan mengeksploitasi kelemahan rangkaian di rumah penerbitan, tetapi kelemahan itu telah ditutup</q>. Berkaitan dengan metadata <q>chinese_architecture</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.degruyter"
msgstr "Buku daripada penerbit akademik <a %(a_href)s>De Gruyter</a>, dikumpulkan daripada beberapa torrent besar."

#, fuzzy
msgid "page.datasets.upload.source.docer"
msgstr "Pengikisan <a %(a_href)s>docer.pl</a>, laman web perkongsian fail Poland yang memfokuskan pada buku dan karya bertulis lain. Dikikis pada akhir 2023 oleh sukarelawan “p”. Kami tidak mempunyai metadata yang baik daripada laman web asal (malah tidak ada sambungan fail), tetapi kami menapis fail yang kelihatan seperti buku dan sering kali dapat mengekstrak metadata daripada fail itu sendiri."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_epub"
msgstr "DuXiu epubs, terus dari DuXiu, dikumpulkan oleh sukarelawan “w”. Hanya buku DuXiu terkini yang tersedia secara langsung melalui ebook, jadi kebanyakan ini mesti terkini."

#, fuzzy
msgid "page.datasets.upload.source.duxiu_main"
msgstr "Fail DuXiu yang tinggal daripada sukarelawan “m”, yang tidak berada dalam format proprietari PDG DuXiu (set data utama <a %(a_href)s>DuXiu</a>). Dikumpulkan daripada banyak sumber asal, malangnya tanpa mengekalkan sumber tersebut dalam laluan fail."

#, fuzzy
msgid "page.datasets.upload.source.elsevier"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.emo37c"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.french"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.hentai"
msgstr "Pengikisan buku erotik, oleh sukarelawan <q>do no harm</q>. Berkaitan dengan metadata <q>hentai</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.ia_multipart"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.imslp"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.japanese_manga"
msgstr "Koleksi yang dikikis daripada penerbit Manga Jepun oleh sukarelawan “t”."

#, fuzzy
msgid "page.datasets.upload.source.longquan_archives"
msgstr "<a %(a_href)s>Arkib kehakiman terpilih Longquan</a>, disediakan oleh sukarelawan “c”."

#, fuzzy
msgid "page.datasets.upload.source.magzdb"
msgstr "Pengikisan <a %(a_href)s>magzdb.org</a>, sekutu Library Genesis (ia dipautkan pada halaman utama libgen.rs) tetapi yang tidak mahu menyediakan fail mereka secara langsung. Diperoleh oleh sukarelawan “p” pada akhir 2023."

#, fuzzy
msgid "page.datasets.upload.source.mangaz_com"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.misc"
msgstr "Pelbagai muat naik kecil, terlalu kecil sebagai subkoleksi mereka sendiri, tetapi diwakili sebagai direktori."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_ebooks"
msgstr "Ebook dari AvaxHome, sebuah laman web perkongsian fail Rusia."

#, fuzzy
msgid "page.datasets.upload.source.newsarch_magz"
msgstr "Arkib surat khabar dan majalah. Berkaitan dengan metadata <q>newsarch_magz</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>."

#, fuzzy
msgid "page.datasets.upload.source.pdcnet_org"
msgstr "Pengikisan <a %(a1)s>Pusat Dokumentasi Falsafah</a>."

#, fuzzy
msgid "page.datasets.upload.source.polish"
msgstr "Koleksi sukarelawan “o” yang mengumpulkan buku Poland secara langsung daripada laman web pelepasan asal (“scene”)."

#, fuzzy
msgid "page.datasets.upload.source.shuge"
msgstr "Koleksi gabungan <a %(a_href)s>shuge.org</a> oleh sukarelawan “cgiym” dan “woz9ts”."

#, fuzzy
msgid "page.datasets.upload.source.shukui_net_cdl"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.trantor"
msgstr "<a %(a_href)s>“Perpustakaan Imperial Trantor”</a> (dinamakan sempena perpustakaan fiksyen), dikikis pada 2022 oleh sukarelawan “t”."

#, fuzzy
msgid "page.datasets.upload.source.turkish_pdfs"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.twlibrary"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.wll"
msgstr "<span></span>"

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_direct"
msgstr "Sub-sub-koleksi (diwakili sebagai direktori) daripada sukarelawan “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (oleh <a %(a_sikuquanshu)s>Dizhi(迪志)</a> di Taiwan), mebook (mebook.cc, 我的小书屋, bilik buku kecil saya — woz9ts: “Laman ini terutamanya memfokuskan pada perkongsian fail ebook berkualiti tinggi, beberapa daripadanya disusun oleh pemiliknya sendiri. Pemiliknya <a %(a_arrested)s>ditangkap</a> pada 2019 dan seseorang membuat koleksi fail yang dikongsinya.”)."

#, fuzzy
msgid "page.datasets.upload.source.woz9ts_duxiu"
msgstr "Fail DuXiu yang tinggal daripada sukarelawan “woz9ts”, yang tidak dalam format PDG proprietari DuXiu (masih perlu ditukar kepada PDF)."

#, fuzzy
msgid "page.datasets.upload.aa_torrents"
msgstr "Torrent oleh Arkib Anna"

#, fuzzy
msgid "page.datasets.zlib.title"
msgstr "Scrape Z-Library"

#, fuzzy
msgid "page.datasets.zlib.description.intro"
msgstr "Z-Library berakar umbi dalam komuniti <a %(a_href)s>Library Genesis</a>, dan pada asalnya dimulakan dengan data mereka. Sejak itu, ia telah menjadi lebih profesional, dan mempunyai antara muka yang lebih moden. Oleh itu, mereka mampu mendapatkan lebih banyak sumbangan, baik dari segi kewangan untuk terus memperbaiki laman web mereka, serta sumbangan buku baru. Mereka telah mengumpulkan koleksi besar selain daripada Library Genesis."

#, fuzzy
msgid "page.datasets.zlib.description.allegations.title"
msgstr "Kemas kini setakat Februari 2023."

#, fuzzy
msgid "page.datasets.zlib.description.allegations"
msgstr "Pada akhir 2022, pengasas Z-Library yang didakwa telah ditangkap, dan domain telah dirampas oleh pihak berkuasa Amerika Syarikat. Sejak itu laman web tersebut perlahan-lahan kembali dalam talian. Tidak diketahui siapa yang mengendalikannya sekarang."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts"
msgstr "Koleksi ini terdiri daripada tiga bahagian. Halaman penerangan asal untuk dua bahagian pertama dipelihara di bawah. Anda memerlukan ketiga-tiga bahagian untuk mendapatkan semua data (kecuali torrent yang telah digantikan, yang dicoretkan pada halaman torrent)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.first"
msgstr "%(title)s: keluaran pertama kami. Ini adalah keluaran pertama yang pada masa itu dipanggil “Pirate Library Mirror” (“pilimi”)."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.second"
msgstr "%(title)s: keluaran kedua, kali ini dengan semua fail dibungkus dalam fail .tar."

#, fuzzy
msgid "page.datasets.zlib.description.three_parts.third_and_incremental"
msgstr "%(title)s: keluaran baru tambahan, menggunakan format <a %(a_href)s>Kontena Arkib Anna (AAC)</a>, kini dikeluarkan dengan kerjasama pasukan Z-Library."

#, fuzzy
msgid "page.datasets.zlib.aa_torrents"
msgstr "Torrent oleh Arkib Anna (metadata + kandungan)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.original"
msgstr "Contoh rekod di Arkib Anna (koleksi asal)"

#, fuzzy
msgid "page.datasets.zlib.aa_example_record.zlib3"
msgstr "Contoh rekod di Arkib Anna (koleksi “zlib3”)"

#, fuzzy
msgid "page.datasets.zlib.link.zlib"
msgstr "Laman web utama"

#, fuzzy
msgid "page.datasets.zlib.link.onion"
msgstr "Domain Tor"

#, fuzzy
msgid "page.datasets.zlib.blog.release1"
msgstr "Catatan blog tentang Keluaran 1"

#, fuzzy
msgid "page.datasets.zlib.blog.release2"
msgstr "Catatan blog tentang Keluaran 2"

#, fuzzy
msgid "page.datasets.zlib.historical.title"
msgstr "Keluaran Zlib (halaman penerangan asal)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.title"
msgstr "Keluaran 1 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description1"
msgstr "Cermin awal diperoleh dengan susah payah sepanjang tahun 2021 dan 2022. Pada ketika ini ia sedikit ketinggalan zaman: ia mencerminkan keadaan koleksi pada Jun 2021. Kami akan mengemas kini ini pada masa hadapan. Buat masa ini kami fokus untuk mengeluarkan keluaran pertama ini."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description2"
msgstr "Memandangkan Library Genesis sudah dipelihara dengan torrents awam, dan termasuk dalam Z-Library, kami melakukan deduplikasi asas terhadap Library Genesis pada Jun 2022. Untuk ini, kami menggunakan hash MD5. Kemungkinan terdapat banyak kandungan duplikat dalam perpustakaan, seperti pelbagai format fail dengan buku yang sama. Ini sukar untuk dikesan dengan tepat, jadi kami tidak melakukannya. Selepas deduplikasi, kami mempunyai lebih daripada 2 juta fail, dengan jumlah keseluruhan hampir 7TB."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description3"
msgstr "Koleksi ini terdiri daripada dua bahagian: dump MySQL “.sql.gz” metadata, dan 72 fail torrent sekitar 50-100GB setiap satu. Metadata mengandungi data seperti yang dilaporkan oleh laman web Z-Library (tajuk, pengarang, deskripsi, jenis fail), serta saiz fail sebenar dan md5sum yang kami perhatikan, kerana kadang-kadang ini tidak sepadan. Terdapat julat fail yang metadata Z-Library sendiri tidak betul. Kami mungkin juga telah memuat turun fail yang salah dalam beberapa kes terpencil, yang akan kami cuba kesan dan betulkan pada masa hadapan."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description4"
msgstr "Fail torrent besar mengandungi data buku sebenar, dengan ID Z-Library sebagai nama fail. Sambungan fail boleh dibina semula menggunakan dump metadata."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description5"
msgstr "Koleksi ini adalah campuran kandungan bukan fiksyen dan fiksyen (tidak dipisahkan seperti dalam Library Genesis). Kualitinya juga sangat berbeza."

#, fuzzy
msgid "page.datasets.zlib.historical.release1.description6"
msgstr "Keluaran pertama ini kini tersedia sepenuhnya. Perhatikan bahawa fail torrent hanya tersedia melalui cermin Tor kami."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.title"
msgstr "Keluaran 2 (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description1"
msgstr "Kami telah mendapatkan semua buku yang ditambah ke Z-Library antara cermin terakhir kami dan Ogos 2022. Kami juga telah kembali dan mengikis beberapa buku yang kami terlepas kali pertama. Secara keseluruhan, koleksi baru ini adalah sekitar 24TB. Sekali lagi, koleksi ini dideduplikasi terhadap Library Genesis, kerana sudah ada torrents tersedia untuk koleksi tersebut."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description2"
msgstr "Data diatur serupa dengan pelepasan pertama. Terdapat dump MySQL “.sql.gz” metadata, yang juga termasuk semua metadata dari pelepasan pertama, dengan itu menggantikannya. Kami juga menambah beberapa lajur baru:"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.in_libgen"
msgstr "%(key)s: sama ada fail ini sudah ada di Library Genesis, dalam koleksi bukan fiksyen atau fiksyen (dipadankan oleh md5)."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.pilimi_torrent"
msgstr "%(key)s: torrent mana fail ini berada."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.field.unavailable"
msgstr "%(key)s: ditetapkan apabila kami tidak dapat memuat turun buku tersebut."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description3"
msgstr "Kami menyebutkan ini kali terakhir, tetapi hanya untuk menjelaskan: “filename” dan “md5” adalah sifat sebenar fail, manakala “filename_reported” dan “md5_reported” adalah apa yang kami kikis dari Z-Library. Kadang-kadang kedua-duanya tidak sepadan, jadi kami menyertakan kedua-duanya."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description4"
msgstr "Untuk pelepasan ini, kami menukar collation kepada “utf8mb4_unicode_ci”, yang sepatutnya serasi dengan versi MySQL yang lebih lama."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5"
msgstr "Fail data adalah serupa dengan kali terakhir, walaupun mereka jauh lebih besar. Kami tidak dapat diganggu untuk mencipta banyak fail torrent yang lebih kecil. “pilimi-zlib2-0-14679999-extra.torrent” mengandungi semua fail yang kami terlepas dalam pelepasan terakhir, manakala torrents lain adalah semua julat ID baru. "

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update1"
msgstr "<strong>Kemas kini %(date)s:</strong> Kami membuat kebanyakan torrents kami terlalu besar, menyebabkan klien torrent menghadapi masalah. Kami telah mengeluarkannya dan melepaskan torrents baru."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.description5.update2"
msgstr "<strong>Kemas kini %(date)s:</strong> Masih terdapat terlalu banyak fail, jadi kami membungkusnya dalam fail tar dan melepaskan torrents baru sekali lagi."

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.title"
msgstr "Pelepasan 2 addendum (%(date)s)"

#, fuzzy
msgid "page.datasets.zlib.historical.release2.addendum.description1"
msgstr "Ini adalah satu fail torrent tambahan. Ia tidak mengandungi sebarang maklumat baru, tetapi ia mempunyai beberapa data di dalamnya yang boleh mengambil masa untuk dikira. Ini menjadikannya mudah untuk dimiliki, kerana memuat turun torrent ini sering lebih cepat daripada mengiranya dari awal. Khususnya, ia mengandungi indeks SQLite untuk fail tar, untuk digunakan dengan <a %(a_href)s>ratarmount</a>."

#, fuzzy
msgid "page.faq.title"
msgstr "Soalan Lazim (FAQ)"

#, fuzzy
msgid "page.faq.what_is.title"
msgstr "Apakah Arkib Anna?"

#, fuzzy
msgid "page.home.intro.text1"
msgstr "<span %(span_anna)s>Arkib Anna</span> adalah projek bukan untung dengan dua matlamat:"

#, fuzzy
msgid "page.home.intro.text2"
msgstr "<li><strong>Pemeliharaan:</strong> Menyandarkan semua pengetahuan dan budaya manusia.</li><li><strong>Akses:</strong> Menjadikan pengetahuan dan budaya ini tersedia kepada sesiapa sahaja di dunia.</li>"

#, fuzzy
msgid "page.home.intro.open_source"
msgstr "Semua <a %(a_code)s>kod</a> dan <a %(a_datasets)s>data</a> kami adalah sumber terbuka sepenuhnya."

#, fuzzy
msgid "page.home.preservation.header"
msgstr "Pemeliharaan"

#, fuzzy
msgid "page.home.preservation.text1"
msgstr "Kami memelihara buku, kertas, komik, majalah, dan banyak lagi, dengan membawa bahan-bahan ini dari pelbagai <a href=\"https://en.wikipedia.org/wiki/Shadow_library\">perpustakaan bayangan</a>, perpustakaan rasmi, dan koleksi lain ke satu tempat. Semua data ini dipelihara selama-lamanya dengan memudahkan penggandaan secara besar-besaran — menggunakan torrent — yang menghasilkan banyak salinan di seluruh dunia. Beberapa perpustakaan bayangan sudah melakukan ini sendiri (contohnya Sci-Hub, Library Genesis), manakala Arkib Anna “membebaskan” perpustakaan lain yang tidak menawarkan pengedaran besar-besaran (contohnya Z-Library) atau bukan perpustakaan bayangan sama sekali (contohnya Internet Archive, DuXiu)."

#, fuzzy
msgid "page.home.preservation.text2"
msgstr "Pengedaran yang meluas ini, digabungkan dengan kod sumber terbuka, menjadikan laman web kami tahan terhadap penutupan, dan memastikan pemeliharaan jangka panjang pengetahuan dan budaya manusia. Ketahui lebih lanjut tentang <a href=\"/datasets\">datasets kami</a>."

#, fuzzy
msgid "page.home.preservation.label"
msgstr "Kami menganggarkan bahawa kami telah memelihara kira-kira <a href=\"https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html\">5%% daripada buku-buku di dunia</a>."

#, fuzzy
msgid "page.home.access.header"
msgstr "Akses"

#, fuzzy
msgid "page.home.access.text"
msgstr "Kami bekerjasama dengan rakan kongsi untuk menjadikan koleksi kami mudah dan bebas diakses oleh sesiapa sahaja. Kami percaya bahawa setiap orang mempunyai hak kepada kebijaksanaan kolektif manusia. Dan <a %(a_search)s>tidak dengan mengorbankan penulis</a>."

#, fuzzy
msgid "page.home.access.label"
msgstr "Muat turun setiap jam dalam 30 hari terakhir. Purata setiap jam: %(hourly)s. Purata setiap hari: %(daily)s."

#, fuzzy
msgid "page.about.text2"
msgstr "Kami sangat percaya pada aliran bebas maklumat, dan pemeliharaan pengetahuan dan budaya. Dengan enjin carian ini, kami membina di atas bahu gergasi. Kami sangat menghormati kerja keras orang-orang yang telah mencipta pelbagai perpustakaan bayangan, dan kami berharap enjin carian ini akan meluaskan jangkauan mereka."

#, fuzzy
msgid "page.about.text3"
msgstr "Untuk kekal dikemas kini mengenai kemajuan kami, ikuti Anna di <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a> atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>. Untuk soalan dan maklum balas sila hubungi Anna di %(email)s."

#, fuzzy
msgid "page.faq.help.title"
msgstr "Bagaimana saya boleh membantu?"

#, fuzzy
msgid "page.about.help.text"
msgstr "<li>1. Ikuti kami di <a href=\"https://www.reddit.com/r/Annas_Archive/\">Reddit</a>, atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>.</li><li>2. Sebarkan tentang Arkib Anna di Twitter, Reddit, Tiktok, Instagram, di kafe atau perpustakaan tempatan anda, atau di mana sahaja anda pergi! Kami tidak percaya dalam menyekat akses — jika kami diturunkan, kami akan muncul semula di tempat lain, kerana semua kod dan data kami sepenuhnya sumber terbuka.</li><li>3. Jika anda mampu, pertimbangkan untuk <a href=\"/donate\">menderma</a>.</li><li>4. Bantu <a href=\"https://translate.annas-software.org/\">menerjemah</a> laman web kami ke dalam pelbagai bahasa.</li><li>5. Jika anda seorang jurutera perisian, pertimbangkan untuk menyumbang kepada <a href=\"https://annas-software.org/\">sumber terbuka</a> kami, atau menyemai <a href=\"/datasets\">torrents</a> kami.</li>"

#, fuzzy
msgid "page.volunteering.section.light.matrix"
msgstr "Kami kini juga mempunyai saluran Matrix yang diselaraskan di %(matrix)s."

#, fuzzy
msgid "page.about.help.text6"
msgstr "6. Jika anda seorang penyelidik keselamatan, kami boleh menggunakan kemahiran anda untuk serangan dan pertahanan. Lihat halaman <a %(a_security)s>Keselamatan</a> kami."

#, fuzzy
msgid "page.about.help.text7"
msgstr "7. Kami sedang mencari pakar dalam pembayaran untuk pedagang tanpa nama. Bolehkah anda membantu kami menambah cara yang lebih mudah untuk menderma? PayPal, WeChat, kad hadiah. Jika anda mengenali sesiapa, sila hubungi kami."

#, fuzzy
msgid "page.about.help.text8"
msgstr "8. Kami sentiasa mencari lebih banyak kapasiti pelayan."

#, fuzzy
msgid "page.about.help.text9"
msgstr "9. Anda boleh membantu dengan melaporkan isu fail, meninggalkan komen, dan membuat senarai terus di laman web ini. Anda juga boleh membantu dengan <a %(a_upload)s>memuat naik lebih banyak buku</a>, atau membaiki isu fail atau format buku sedia ada."

#, fuzzy
msgid "page.about.help.text10"
msgstr "10. Cipta atau bantu mengekalkan halaman Wikipedia untuk Arkib Anna dalam bahasa anda."

#, fuzzy
msgid "page.about.help.text11"
msgstr "11. Kami sedang mencari untuk meletakkan iklan kecil yang berselera. Jika anda ingin mengiklankan di Arkib Anna, sila maklumkan kepada kami."

#, fuzzy
msgid "page.faq.help.mirrors"
msgstr "Kami ingin orang ramai menubuhkan <a %(a_mirrors)s>cermin</a>, dan kami akan menyokong kewangan ini."

#, fuzzy
msgid "page.about.help.volunteer"
msgstr "Untuk maklumat lebih lanjut tentang cara menjadi sukarelawan, lihat halaman <a %(a_volunteering)s>Sukarelawan & Ganjaran</a> kami."

#, fuzzy
msgid "page.faq.slow.title"
msgstr "Mengapa muat turun yang perlahan begitu lambat?"

#, fuzzy
msgid "page.faq.slow.text1"
msgstr "Kami benar-benar tidak mempunyai sumber yang mencukupi untuk memberikan semua orang di dunia muat turun berkelajuan tinggi, walaupun kami ingin. Jika seorang dermawan kaya ingin tampil dan menyediakan ini untuk kami, itu akan menjadi luar biasa, tetapi sehingga itu, kami mencuba yang terbaik. Kami adalah projek bukan untung yang hampir tidak dapat bertahan melalui derma."

#, fuzzy
msgid "page.faq.slow.text2"
msgstr "Inilah sebabnya kami melaksanakan dua sistem untuk muat turun percuma, dengan rakan kongsi kami: pelayan kongsi dengan muat turun perlahan, dan pelayan yang sedikit lebih pantas dengan senarai menunggu (untuk mengurangkan bilangan orang yang memuat turun pada masa yang sama)."

#, fuzzy
msgid "page.faq.slow.text3"
msgstr "Kami juga mempunyai <a %(a_verification)s>pengesahan pelayar</a> untuk muat turun perlahan kami, kerana jika tidak, bot dan pengikis akan menyalahgunakannya, menjadikan keadaan lebih perlahan untuk pengguna yang sah."

#, fuzzy
msgid "page.faq.slow.text4"
msgstr "Perhatikan bahawa, apabila menggunakan Pelayar Tor, anda mungkin perlu menyesuaikan tetapan keselamatan anda. Pada pilihan terendah, yang dipanggil “Standard”, cabaran turnstile Cloudflare berjaya. Pada pilihan yang lebih tinggi, yang dipanggil “Safer” dan “Safest”, cabaran tersebut gagal."

#, fuzzy
msgid "page.faq.slow.text5"
msgstr "Untuk fail besar, kadangkala muat turun yang perlahan boleh terputus di tengah jalan. Kami mengesyorkan menggunakan pengurus muat turun (seperti JDownloader) untuk menyambung semula muat turun besar secara automatik."

msgid "page.donate.faq.title"
msgstr "FAQ Derma"

msgid "page.donate.faq.renew"
msgstr "<div %(div_question)s>Adakah keahlian akan diperbaharui secara automatik?</div> Keahlian <strong> tidak akan</strong> diperbaharui secara automatik. Anda boleh menyertai seberapa lama atau pendek yang anda mahu."

#, fuzzy
msgid "page.donate.faq.membership"
msgstr "<div %(div_question)s>Bolehkah saya menaik taraf keahlian saya atau mendapatkan pelbagai keahlian?</div>"

msgid "page.donate.faq.text_other_payment1"
msgstr "<div %(div_question)s>Adakah anda ada kaedah pembayaran lain?</div> Setakat ini tiada. Ramai pihak tidak mahu arkib seperti ini wujud, jadi kami terpaksa berhati-hati. Jika anda boleh membantu kami untuk menyediakan kaedah pembayaran (yang lebih mudah) dengan selamat, sila hubungi kami di %(email)s."

#, fuzzy
msgid "page.donate.faq.ranges"
msgstr "<div %(div_question)s>Apakah maksud julat per bulan?</div> Anda boleh mencapai bahagian bawah julat dengan menggunakan semua diskaun, seperti memilih tempoh lebih lama daripada sebulan."

msgid "page.donate.faq.spend"
msgstr "<div %(div_question)s>Bagaimana anda membelanjakan derma yang diberi?</div> 100%% derma akan digunakan untuk memelihara dan menjadikan pengetahuan dan budaya dunia lebih mudah diakses. Pada masa ini kebanyakkan derma dibelanjakan untuk pelayan, storan dan jalur lebar. Tiada derma yang akan diberikan kepada mana-mana ahli pasukan secara peribadi."

msgid "page.donate.faq.text_large_donation"
msgstr "<div %(div_question)s>Bolehkah saya membuat derma yang besar?</div> Itu pasti menakjubkan! Untuk menderma lebih daripada beberapa ribu ringgit, sila hubungi kami terus di %(email)s."

#, fuzzy
msgid "page.donate.faq.non_member_donation"
msgstr "<div %(div_question)s>Bolehkah saya membuat derma tanpa menjadi ahli?</div> Sudah tentu. Kami menerima derma dalam apa jua jumlah di alamat Monero (XMR) ini: %(address)s."

#, fuzzy
msgid "page.faq.upload.title"
msgstr "Bagaimana saya memuat naik buku baharu?"

#, fuzzy
msgid "page.upload.zlib.text1"
msgstr "Sebagai alternatif, anda boleh memuat naiknya ke Z-Library <a %(a_upload)s>di sini</a>."

#, fuzzy
msgid "page.upload.upload_to_both"
msgstr "Untuk muat naik kecil (sehingga 10,000 fail) sila muat naik ke kedua-dua %(first)s dan %(second)s."

msgid "page.upload.text1"
msgstr "Buat masa ini, kami cadangkan memuat naik buku baharu ke garpu Library Genesis. Gunakan <a %(a_guide)s>panduan berguna</a> ini. Ambil maklum bahawa kedua-dua garpu yang kami indeks di laman web ini ditarik daripada sistem muat naik yang sama."

#, fuzzy
msgid "page.upload.libgenli_login_instructions"
msgstr "Untuk Libgen.li, pastikan untuk log masuk terlebih dahulu di <a %(a_forum)s>forum mereka</a> dengan nama pengguna %(username)s dan kata laluan %(password)s, dan kemudian kembali ke <a %(a_upload_page)s>halaman muat naik</a> mereka."

msgid "common.libgen.email"
msgstr "Jika alamat emel anda tidak boleh digunakan di forum Libgen, kami mencadangkan anda untuk menggunakan <a %(a_mail)s>Proton Mail</a> (percuma). Anda juga boleh <a %(a_manual)s>meminta secara manual</a> agar akaun anda dapat diaktifkan."

#, fuzzy
msgid "page.faq.mhut_upload"
msgstr "Perhatikan bahawa mhut.org menyekat julat IP tertentu, jadi VPN mungkin diperlukan."

#, fuzzy
msgid "page.upload.large.text"
msgstr "Untuk muat naik besar (lebih 10,000 fail) yang tidak diterima oleh Libgen atau Z-Library, sila hubungi kami di %(a_email)s."

#, fuzzy
msgid "page.upload.zlib.text2"
msgstr "Untuk memuat naik kertas akademik, sila juga (sebagai tambahan kepada Library Genesis) muat naik ke <a %(a_stc_nexus)s>STC Nexus</a>. Mereka adalah perpustakaan bayangan terbaik untuk kertas baru. Kami belum mengintegrasikan mereka lagi, tetapi kami akan pada suatu masa nanti. Anda boleh menggunakan <a %(a_telegram)s>bot muat naik mereka di Telegram</a>, atau hubungi alamat yang disenaraikan dalam mesej yang disematkan mereka jika anda mempunyai terlalu banyak fail untuk dimuat naik dengan cara ini."

#, fuzzy
msgid "page.faq.request.title"
msgstr "Bagaimana saya meminta buku?"

#, fuzzy
msgid "page.request.cannot_accomodate"
msgstr "Pada masa ini, kami tidak dapat memenuhi permintaan buku."

#, fuzzy
msgid "page.request.forums"
msgstr "Sila buat permintaan anda di forum Z-Library atau Libgen."

#, fuzzy
msgid "page.request.dont_email"
msgstr "Jangan email kami permintaan buku anda."

#, fuzzy
msgid "page.faq.metadata.title"
msgstr "Adakah anda mengumpul metadata?"

#, fuzzy
msgid "page.faq.metadata.indeed"
msgstr "Ya, kami memang mengumpulnya."

#, fuzzy
msgid "page.faq.1984.title"
msgstr "Saya memuat turun 1984 oleh George Orwell, adakah polis akan datang ke pintu saya?"

#, fuzzy
msgid "page.faq.1984.text"
msgstr "Jangan risau terlalu banyak, terdapat ramai orang yang memuat turun dari laman web yang dihubungkan oleh kami, dan sangat jarang untuk menghadapi masalah. Walau bagaimanapun, untuk kekal selamat kami mengesyorkan menggunakan VPN (berbayar), atau <a %(a_tor)s>Tor</a> (percuma)."

#, fuzzy
msgid "page.faq.save_search.title"
msgstr "Bagaimana saya menyimpan tetapan carian saya?"

#, fuzzy
msgid "page.faq.save_search.text1"
msgstr "Pilih tetapan yang anda suka, biarkan kotak carian kosong, klik “Cari”, dan kemudian tandakan halaman menggunakan ciri penanda halaman pelayar anda."

#, fuzzy
msgid "page.faq.mobile.title"
msgstr "Adakah anda mempunyai aplikasi mudah alih?"

#, fuzzy
msgid "page.faq.mobile.text1"
msgstr "Kami tidak mempunyai aplikasi mudah alih rasmi, tetapi anda boleh memasang laman web ini sebagai aplikasi."

#, fuzzy
msgid "page.faq.mobile.android"
msgstr "<strong>Android:</strong> Klik menu tiga titik di bahagian atas kanan, dan pilih “Tambah ke Skrin Utama”."

#, fuzzy
msgid "page.faq.mobile.ios"
msgstr "<strong>iOS:</strong> Klik butang “Kongsi” di bahagian bawah, dan pilih “Tambah ke Skrin Utama”."

#, fuzzy
msgid "page.faq.api.title"
msgstr "Adakah anda mempunyai API?"

#, fuzzy
msgid "page.faq.api.text1"
msgstr "Kami mempunyai satu API JSON stabil untuk ahli, untuk mendapatkan URL muat turun pantas: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasi dalam JSON itu sendiri)."

#, fuzzy
msgid "page.faq.api.text2"
msgstr "Untuk kegunaan lain, seperti mengulangi semua fail kami, membina carian tersuai, dan sebagainya, kami mengesyorkan <a %(a_generate)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Data mentah boleh diterokai secara manual <a %(a_explore)s>melalui fail JSON</a>."

#, fuzzy
msgid "page.faq.api.text3"
msgstr "Senarai torrent mentah kami boleh dimuat turun sebagai <a %(a_torrents)s>JSON</a> juga."

#, fuzzy
msgid "page.faq.torrents.title"
msgstr "Soalan Lazim Torrent"

#, fuzzy
msgid "page.faq.torrents.q1"
msgstr "Saya ingin membantu seed, tetapi saya tidak mempunyai banyak ruang cakera."

#, fuzzy
msgid "page.faq.torrents.a1"
msgstr "Gunakan <a %(a_list)s>penjana senarai torrent</a> untuk menjana senarai torrent yang paling memerlukan torrenting, dalam had ruang storan anda."

#, fuzzy
msgid "page.faq.torrents.q2"
msgstr "Torrent terlalu perlahan; bolehkah saya memuat turun data terus dari anda?"

#, fuzzy
msgid "page.faq.torrents.a2"
msgstr "Ya, lihat halaman <a %(a_llm)s>data LLM</a>."

#, fuzzy
msgid "page.faq.torrents.q3"
msgstr "Bolehkah saya memuat turun hanya sebahagian daripada fail, seperti hanya bahasa atau topik tertentu?"

#, fuzzy
msgid "page.faq.torrents.a3_short_answer"
msgstr "Jawapan ringkas: tidak mudah."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_start"
msgstr "Jawapan panjang:"

#, fuzzy
msgid "page.faq.torrents.a3"
msgstr "Kebanyakan torrent mengandungi fail secara langsung, yang bermaksud anda boleh mengarahkan klien torrent untuk hanya memuat turun fail yang diperlukan. Untuk menentukan fail mana yang hendak dimuat turun, anda boleh <a %(a_generate)s>menjana</a> metadata kami, atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Malangnya, beberapa koleksi torrent mengandungi fail .zip atau .tar di akar, dalam kes ini anda perlu memuat turun keseluruhan torrent sebelum dapat memilih fail individu."

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_ideas"
msgstr "(Kami mempunyai <a %(a_ideas)s>beberapa idea</a> untuk kes yang terakhir ini.)"

#, fuzzy
msgid "page.faq.torrents.a3_long_answer_contributions"
msgstr "Tiada alat yang mudah digunakan untuk menapis torrent tersedia lagi, tetapi kami mengalu-alukan sumbangan."

#, fuzzy
msgid "page.faq.torrents.q4"
msgstr "Bagaimana anda mengendalikan pendua dalam torrent?"

#, fuzzy
msgid "page.faq.torrents.a4"
msgstr "Kami cuba untuk meminimumkan pendua atau pertindihan antara torrent dalam senarai ini, tetapi ini tidak selalu dapat dicapai, dan bergantung banyak pada dasar perpustakaan sumber. Untuk perpustakaan yang mengeluarkan torrent mereka sendiri, ia di luar kawalan kami. Untuk torrent yang dikeluarkan oleh Arkib Anna, kami menduplikasi hanya berdasarkan hash MD5, yang bermaksud bahawa versi berbeza dari buku yang sama tidak akan diduplikasi."

#, fuzzy
msgid "page.faq.torrents.q5"
msgstr "Bolehkah saya mendapatkan senarai torrent sebagai JSON?"

#, fuzzy
msgid "page.faq.torrents.a5"
msgstr "Ya."

#, fuzzy
msgid "page.faq.torrents.q6"
msgstr "Saya tidak nampak PDF atau EPUB dalam torrent, hanya fail binari? Apa yang perlu saya lakukan?"

#, fuzzy
msgid "page.faq.torrents.a6"
msgstr "Ini sebenarnya adalah PDF dan EPUB, cuma tidak mempunyai sambungan dalam banyak torrent kami. Terdapat dua tempat di mana anda boleh mencari metadata untuk fail torrent, termasuk jenis/sambungan fail:"

#, fuzzy
msgid "page.faq.torrents.a6.li1"
msgstr "1. Setiap koleksi atau keluaran mempunyai metadata tersendiri. Sebagai contoh, <a %(a_libgen_nonfic)s>torrent Libgen.rs</a> mempunyai pangkalan data metadata yang dihoskan di laman web Libgen.rs. Kami biasanya memautkan kepada sumber metadata yang berkaitan dari <a %(a_datasets)s>halaman dataset</a> setiap koleksi."

#, fuzzy
msgid "page.faq.torrents.a6.li2"
msgstr "2. Kami mengesyorkan <a %(a_generate)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Ini mengandungi pemetaan untuk setiap rekod dalam Arkib Anna kepada fail torrent yang sepadan (jika ada), di bawah \"torrent_paths\" dalam JSON ElasticSearch."

#, fuzzy
msgid "page.faq.torrents.q7"
msgstr "Mengapa klien torrent saya tidak dapat membuka beberapa fail torrent / pautan magnet anda?"

#, fuzzy
msgid "page.faq.torrents.a7"
msgstr "Sesetengah klien torrent tidak menyokong saiz kepingan yang besar, yang mana banyak torrent kami mempunyai (untuk yang lebih baru kami tidak melakukannya lagi — walaupun ia sah mengikut spesifikasi!). Jadi cuba klien lain jika anda menghadapi masalah ini, atau adukan kepada pembuat klien torrent anda."

#, fuzzy
msgid "page.faq.security.title"
msgstr "Adakah anda mempunyai program pendedahan yang bertanggungjawab?"

#, fuzzy
msgid "page.faq.security.text1"
msgstr "Kami mengalu-alukan penyelidik keselamatan untuk mencari kerentanan dalam sistem kami. Kami adalah penyokong besar pendedahan yang bertanggungjawab. Hubungi kami <a %(a_contact)s>di sini</a>."

#, fuzzy
msgid "page.faq.security.text2"
msgstr "Kami kini tidak dapat memberikan ganjaran bug, kecuali untuk kerentanan yang mempunyai <a %(a_link)s>potensi untuk menjejaskan anonimiti kami</a>, yang mana kami menawarkan ganjaran dalam lingkungan $10k-50k. Kami ingin menawarkan skop yang lebih luas untuk ganjaran bug pada masa hadapan! Sila ambil perhatian bahawa serangan kejuruteraan sosial adalah di luar skop."

#, fuzzy
msgid "page.faq.security.text3"
msgstr "Jika anda berminat dalam keselamatan ofensif, dan ingin membantu mengarkibkan pengetahuan dan budaya dunia, pastikan untuk menghubungi kami. Terdapat banyak cara di mana anda boleh membantu."

#, fuzzy
msgid "page.faq.resources.title"
msgstr "Adakah terdapat lebih banyak sumber mengenai Arkib Anna?"

#, fuzzy
msgid "page.faq.resources.annas_blog"
msgstr "<a %(a_blog)s>Blog Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — kemas kini berkala"

#, fuzzy
msgid "page.faq.resources.annas_software"
msgstr "<a %(a_software)s>Perisian Anna</a> — kod sumber terbuka kami"

#, fuzzy
msgid "page.faq.resources.translate"
msgstr "<a %(a_translate)s>Terjemah di Perisian Anna</a> — sistem terjemahan kami"

#, fuzzy
msgid "page.faq.resources.datasets"
msgstr "<a %(a_datasets)s>Datasets</a> — tentang data"

#, fuzzy
msgid "page.faq.resources.domains"
msgstr "<a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domain alternatif"

#, fuzzy
msgid "page.faq.resources.wikipedia"
msgstr "<a %(a_wikipedia)s>Wikipedia</a> — lebih lanjut tentang kami (sila bantu kemas kini halaman ini, atau cipta satu untuk bahasa anda sendiri!)"

#, fuzzy
msgid "page.faq.copyright.title"
msgstr "Bagaimana saya melaporkan pelanggaran hak cipta?"

#, fuzzy
msgid "page.faq.copyright.text1"
msgstr "Kami tidak menjadi tuan rumah sebarang bahan berhak cipta di sini. Kami adalah enjin carian, dan oleh itu hanya mengindeks metadata yang sudah tersedia secara umum. Apabila memuat turun dari sumber luaran ini, kami mencadangkan untuk memeriksa undang-undang di bidang kuasa anda berkenaan dengan apa yang dibenarkan. Kami tidak bertanggungjawab terhadap kandungan yang dihoskan oleh pihak lain."

#, fuzzy
msgid "page.faq.copyright.text2"
msgstr "Jika anda mempunyai aduan tentang apa yang anda lihat di sini, pilihan terbaik anda adalah menghubungi laman web asal. Kami kerap menarik perubahan mereka ke dalam pangkalan data kami. Jika anda benar-benar berfikir anda mempunyai aduan DMCA yang sah yang perlu kami tangani, sila isi <a %(a_copyright)s>borang aduan DMCA / Hak Cipta</a>. Kami mengambil aduan anda dengan serius, dan akan menghubungi anda secepat mungkin."

#, fuzzy
msgid "page.faq.hate.title"
msgstr "Saya benci cara anda menjalankan projek ini!"

#, fuzzy
msgid "page.faq.hate.text1"
msgstr "Kami juga ingin mengingatkan semua orang bahawa semua kod dan data kami adalah sumber terbuka sepenuhnya. Ini adalah unik untuk projek seperti kami — kami tidak mengetahui sebarang projek lain dengan katalog yang begitu besar yang juga sepenuhnya sumber terbuka. Kami sangat mengalu-alukan sesiapa yang berfikir kami menjalankan projek kami dengan buruk untuk mengambil kod dan data kami dan menubuhkan perpustakaan bayangan mereka sendiri! Kami tidak mengatakan ini dengan niat jahat atau apa-apa — kami benar-benar berfikir ini akan menjadi hebat kerana ia akan meningkatkan standard untuk semua orang, dan lebih baik memelihara warisan manusia."

#, fuzzy
msgid "page.faq.uptime.title"
msgstr "Adakah anda mempunyai pemantau masa operasi?"

#, fuzzy
msgid "page.faq.uptime.text1"
msgstr "Sila lihat <a %(a_href)s>projek yang cemerlang ini</a>."

#, fuzzy
msgid "page.faq.physical.title"
msgstr "Bagaimana saya menderma buku atau bahan fizikal lain?"

#, fuzzy
msgid "page.faq.physical.text1"
msgstr "Sila hantar mereka ke <a %(a_archive)s>Internet Archive</a>. Mereka akan memeliharanya dengan betul."

#, fuzzy
msgid "page.faq.anna.title"
msgstr "Siapa Anna?"

#, fuzzy
msgid "page.faq.anna.text1"
msgstr "Anda adalah Anna!"

#, fuzzy
msgid "page.faq.favorite.title"
msgstr "Apakah buku kegemaran anda?"

#, fuzzy
msgid "page.faq.favorite.text1"
msgstr "Berikut adalah beberapa buku yang membawa makna istimewa kepada dunia perpustakaan bayangan dan pemeliharaan digital:"

#, fuzzy
msgid "page.fast_downloads.no_more_new"
msgstr "Anda telah kehabisan muat turun pantas hari ini."

#, fuzzy
msgid "page.fast_downloads.no_member"
msgstr "Jadi ahli untuk menggunakan muat turun pantas."

#, fuzzy
msgid "page.fast_downloads.no_member_2"
msgstr "Kami kini menyokong kad hadiah Amazon, kad kredit dan debit, kripto, Alipay, dan WeChat."

#, fuzzy
msgid "page.home.full_database.header"
msgstr "Pangkalan data penuh"

#, fuzzy
msgid "page.home.full_database.subtitle"
msgstr "Buku, kertas kerja, majalah, komik, rekod perpustakaan, metadata, …"

#, fuzzy
msgid "page.home.full_database.search"
msgstr "Cari"

#, fuzzy
msgid "page.home.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "layout.index.header.nav.beta"
msgstr "beta"

#, fuzzy
msgid "page.home.scidb.scihub_paused"
msgstr "Sci-Hub telah <a %(a_paused)s>menghentikan</a> muat naik kertas baru."

#, fuzzy
msgid "page.home.scidb.continuation"
msgstr "🧬&nbsp;SciDB adalah kesinambungan Sci-Hub."

#, fuzzy
msgid "page.home.scidb.subtitle"
msgstr "Akses langsung kepada %(count)s kertas akademik"

#, fuzzy
msgid "page.home.scidb.placeholder_doi"
msgstr "DOI"

#, fuzzy
msgid "page.home.scidb.open"
msgstr "Buka"

#, fuzzy
msgid "page.home.scidb.browser_verification"
msgstr "Jika anda seorang <a %(a_member)s>ahli</a>, pengesahan pelayar tidak diperlukan."

#, fuzzy
msgid "page.home.archive.header"
msgstr "Arkib jangka panjang"

#, fuzzy
msgid "page.home.archive.body"
msgstr "Set data yang digunakan dalam Arkib Anna adalah sepenuhnya terbuka, dan boleh dicerminkan secara pukal menggunakan torrent. <a %(a_datasets)s>Ketahui lebih lanjut…</a>"

#, fuzzy
msgid "page.home.torrents.body"
msgstr "Anda boleh membantu dengan banyak dengan menyemai torrent. <a %(a_torrents)s>Ketahui lebih lanjut…</a>"

#, fuzzy
msgid "page.home.torrents.legend_less"
msgstr "<%(count)s seeders"

#, fuzzy
msgid "page.home.torrents.legend_range"
msgstr "%(count_min)s–%(count_max)s penyebar"

#, fuzzy
msgid "page.home.torrents.legend_greater"
msgstr ">%(count)s penyemai"

#, fuzzy
msgid "page.home.llm.header"
msgstr "LLM training data"

#, fuzzy
msgid "page.home.llm.body"
msgstr "Kami mempunyai koleksi data teks berkualiti tinggi terbesar di dunia. <a %(a_llm)s>Ketahui lebih lanjut…</a>"

#, fuzzy
msgid "page.home.mirrors.header"
msgstr "🪩 Cermin: panggilan untuk sukarelawan"

#, fuzzy
msgid "page.home.volunteering.header"
msgstr "🤝 Mencari sukarelawan"

#, fuzzy
msgid "page.home.volunteering.help_out"
msgstr "Sebagai projek sumber terbuka tanpa untung, kami sentiasa mencari orang untuk membantu."

#, fuzzy
msgid "page.home.payment_processor.body"
msgstr "Jika anda menjalankan pemproses pembayaran tanpa nama berisiko tinggi, sila hubungi kami. Kami juga mencari orang yang ingin meletakkan iklan kecil yang berselera. Semua hasil akan digunakan untuk usaha pemeliharaan kami."

#, fuzzy
msgid "layout.index.header.nav.annasblog"
msgstr "Blog Anna ↗"

#, fuzzy
msgid "page.ipfs_downloads.title"
msgstr "Muat turun IPFS"

#, fuzzy
msgid "page.partner_download.main_page"
msgstr "🔗 Semua pautan muat turun untuk fail ini: <a %(a_main)s>Halaman utama fail</a>."

msgid "page.md5.box.download.ipfs_gateway"
msgstr "Pintu masuk IPFS #%(num)d"

msgid "page.md5.box.download.ipfs_gateway_extra"
msgstr "(anda mungkin perlu mencuba beberapa kali apabila menggunakan IPFS)"

#, fuzzy
msgid "page.partner_download.faster_downloads"
msgstr "🚀 Untuk mendapatkan muat turun yang lebih pantas dan melangkau pemeriksaan pelayar, <a %(a_membership)s>jadi ahli</a>."

#, fuzzy
msgid "page.partner_download.bulk_mirroring"
msgstr "📡 Untuk pencerminan pukal koleksi kami, lihat halaman <a %(a_datasets)s>Datasets</a> dan <a %(a_torrents)s>Torrents</a>."

#, fuzzy
msgid "page.llm.title"
msgstr "Data LLM"

#, fuzzy
msgid "page.llm.intro"
msgstr "Difahami bahawa LLM berkembang dengan data berkualiti tinggi. Kami mempunyai koleksi buku, kertas kerja, majalah, dan lain-lain yang terbesar di dunia, yang merupakan beberapa sumber teks berkualiti tinggi."

#, fuzzy
msgid "page.llm.unique_scale"
msgstr "Skala dan julat unik"

#, fuzzy
msgid "page.llm.unique_scale.text1"
msgstr "Koleksi kami mengandungi lebih daripada seratus juta fail, termasuk jurnal akademik, buku teks, dan majalah. Kami mencapai skala ini dengan menggabungkan repositori besar yang sedia ada."

#, fuzzy
msgid "page.llm.unique_scale.text2"
msgstr "Beberapa koleksi sumber kami sudah tersedia dalam jumlah besar (Sci-Hub, dan sebahagian daripada Libgen). Sumber lain kami bebaskan sendiri. <a %(a_datasets)s>Datasets</a> menunjukkan gambaran penuh."

#, fuzzy
msgid "page.llm.unique_scale.text3"
msgstr "Koleksi kami termasuk berjuta-juta buku, kertas kerja, dan majalah dari sebelum era e-buku. Sebahagian besar koleksi ini telah di-OCR, dan sudah mempunyai sedikit pertindihan dalaman."

#, fuzzy
msgid "page.llm.how_we_can_help"
msgstr "Bagaimana kami boleh membantu"

#, fuzzy
msgid "page.llm.how_we_can_help.text1"
msgstr "Kami mampu menyediakan akses berkelajuan tinggi kepada koleksi penuh kami, serta kepada koleksi yang belum dikeluarkan."

#, fuzzy
msgid "page.llm.how_we_can_help.text2"
msgstr "Ini adalah akses peringkat perusahaan yang kami boleh sediakan untuk sumbangan dalam lingkungan puluhan ribu USD. Kami juga bersedia menukar ini dengan koleksi berkualiti tinggi yang kami belum ada."

#, fuzzy
msgid "page.llm.how_we_can_help.text3"
msgstr "Kami boleh memulangkan wang anda jika anda dapat menyediakan kami dengan pengayaan data kami, seperti:"

#, fuzzy
msgid "page.llm.how_we_can_help.ocr"
msgstr "OCR"

#, fuzzy
msgid "page.llm.how_we_can_help.deduplication"
msgstr "Mengeluarkan pertindihan (deduplikasi)"

#, fuzzy
msgid "page.llm.how_we_can_help.extraction"
msgstr "Pengekstrakan teks dan metadata"

#, fuzzy
msgid "page.llm.how_we_can_help.text4"
msgstr "Sokong arkib jangka panjang pengetahuan manusia, sambil mendapatkan data yang lebih baik untuk model anda!"

#, fuzzy
msgid "page.llm.how_we_can_help.text5"
msgstr "<a %(a_contact)s>Hubungi kami</a> untuk membincangkan bagaimana kita boleh bekerjasama."

#, fuzzy
msgid "page.login.continue"
msgstr "Teruskan"

#, fuzzy
msgid "page.login.please"
msgstr "Sila <a %(a_account)s>log masuk</a> untuk melihat halaman ini.</a>"

#, fuzzy
msgid "page.maintenance.header"
msgstr "Arkib Anna sedang dalam penyelenggaraan sementara. Sila kembali dalam satu jam."

#, fuzzy
msgid "page.metadata.header"
msgstr "Tingkatkan metadata"

#, fuzzy
msgid "page.metadata.body1"
msgstr "Anda boleh membantu pemeliharaan buku dengan meningkatkan metadata! Pertama, baca latar belakang tentang metadata di Arkib Anna, dan kemudian pelajari cara meningkatkan metadata melalui pautan dengan Open Library, dan dapatkan keahlian percuma di Arkib Anna."

#, fuzzy
msgid "page.metadata.background.title"
msgstr "Latar Belakang"

#, fuzzy
msgid "page.metadata.background.body1"
msgstr "Apabila anda melihat sebuah buku di Arkib Anna, anda boleh melihat pelbagai medan: tajuk, pengarang, penerbit, edisi, tahun, deskripsi, nama fail, dan banyak lagi. Semua maklumat tersebut dipanggil <em>metadata</em>."

#, fuzzy
msgid "page.metadata.background.body2"
msgstr "Oleh kerana kami menggabungkan buku dari pelbagai <em>perpustakaan sumber</em>, kami menunjukkan apa sahaja metadata yang tersedia dalam perpustakaan sumber tersebut. Sebagai contoh, untuk sebuah buku yang kami peroleh dari Library Genesis, kami akan menunjukkan tajuk dari pangkalan data Library Genesis."

#, fuzzy
msgid "page.metadata.background.body3"
msgstr "Kadang-kadang sebuah buku terdapat dalam <em>beberapa</em> perpustakaan sumber, yang mungkin mempunyai medan metadata yang berbeza. Dalam kes itu, kami hanya menunjukkan versi terpanjang bagi setiap medan, kerana versi tersebut diharapkan mengandungi maklumat yang paling berguna! Kami masih akan menunjukkan medan lain di bawah deskripsi, contohnya sebagai \"tajuk alternatif\" (tetapi hanya jika ia berbeza)."

#, fuzzy
msgid "page.metadata.background.body4"
msgstr "Kami juga mengekstrak <em>kod</em> seperti pengenal dan pengelas dari perpustakaan sumber. <em>Pengenal</em> mewakili edisi tertentu bagi sebuah buku secara unik; contohnya adalah ISBN, DOI, Open Library ID, Google Books ID, atau Amazon ID. <em>Pengelas</em> mengelompokkan beberapa buku yang serupa; contohnya adalah Dewey Decimal (DCC), UDC, LCC, RVK, atau GOST. Kadang-kadang kod ini secara eksplisit dihubungkan dalam perpustakaan sumber, dan kadang-kadang kami boleh mengekstraknya dari nama fail atau deskripsi (terutamanya ISBN dan DOI)."

#, fuzzy
msgid "page.metadata.background.body5"
msgstr "Kami boleh menggunakan pengenal untuk mencari rekod dalam <em>koleksi metadata sahaja</em>, seperti OpenLibrary, ISBNdb, atau WorldCat/OCLC. Terdapat tab <em>metadata</em> khusus dalam enjin carian kami jika anda ingin melayari koleksi tersebut. Kami menggunakan rekod yang sepadan untuk mengisi medan metadata yang hilang (contohnya jika tajuk hilang), atau contohnya sebagai \"tajuk alternatif\" (jika terdapat tajuk yang sedia ada)."

#, fuzzy
msgid "page.metadata.background.body6"
msgstr "Untuk melihat dengan tepat dari mana metadata sebuah buku berasal, lihat tab <em>“Butiran teknikal”</em> pada halaman buku. Ia mempunyai pautan ke JSON mentah untuk buku tersebut, dengan petunjuk ke JSON mentah rekod asal."

#, fuzzy
msgid "page.metadata.background.body7"
msgstr "Untuk maklumat lanjut, lihat halaman berikut: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Carian (tab metadata)</a>, <a %(a_codes)s>Penjelajah Kod</a>, dan <a %(a_example)s>Contoh metadata JSON</a>. Akhir sekali, semua metadata kami boleh <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB."

#, fuzzy
msgid "page.metadata.openlib.title"
msgstr "Pautan Open Library"

#, fuzzy
msgid "page.metadata.openlib.body1"
msgstr "Jadi jika anda menemui fail dengan metadata yang buruk, bagaimana anda harus memperbaikinya? Anda boleh pergi ke perpustakaan sumber dan mengikuti prosedurnya untuk memperbaiki metadata, tetapi apa yang perlu dilakukan jika fail tersebut terdapat dalam beberapa perpustakaan sumber?"

#, fuzzy
msgid "page.metadata.openlib.body2"
msgstr "Terdapat satu pengenal yang dianggap istimewa di Arkib Anna. <strong>Medan annas_archive md5 di Open Library sentiasa mengatasi semua metadata lain!</strong> Mari kita mundur sedikit dan belajar tentang Open Library."

#, fuzzy
msgid "page.metadata.openlib.body3"
msgstr "Open Library diasaskan pada tahun 2006 oleh Aaron Swartz dengan matlamat \"satu halaman web untuk setiap buku yang pernah diterbitkan\". Ia sejenis Wikipedia untuk metadata buku: semua orang boleh menyuntingnya, ia berlesen bebas, dan boleh dimuat turun secara pukal. Ia adalah pangkalan data buku yang paling selaras dengan misi kami — sebenarnya, Arkib Anna telah diilhamkan oleh visi dan kehidupan Aaron Swartz."

#, fuzzy
msgid "page.metadata.openlib.body4"
msgstr "Daripada mencipta semula roda, kami memutuskan untuk mengarahkan sukarelawan kami ke Open Library. Jika anda melihat sebuah buku yang mempunyai metadata yang salah, anda boleh membantu dengan cara berikut:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.1"
msgstr " Pergi ke <a %(a_openlib)s>laman web Open Library</a>."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2"
msgstr "Cari rekod buku yang betul. <strong>AMARAN:</strong> pastikan anda memilih <strong>edisi</strong> yang betul. Di Open Library, anda mempunyai \"karya\" dan \"edisi\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.1"
msgstr "Sebuah \"karya\" boleh jadi \"Harry Potter and the Philosopher's Stone\"."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2"
msgstr "Sebuah \"edisi\" boleh jadi:"

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.1"
msgstr "Edisi pertama tahun 1997 diterbitkan oleh Bloomsbery dengan 256 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.2"
msgstr "Edisi paperback tahun 2003 diterbitkan oleh Raincoast Books dengan 223 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.2.3"
msgstr "Terjemahan Poland tahun 2000 “Harry Potter I Kamie Filozoficzn” oleh Media Rodzina dengan 328 halaman."

#, fuzzy
msgid "page.metadata.openlib.howto.item.2.3"
msgstr "Semua edisi tersebut mempunyai ISBN dan kandungan yang berbeza, jadi pastikan anda memilih yang betul!"

#, fuzzy
msgid "page.metadata.openlib.howto.item.3"
msgstr "Edit rekod (atau cipta jika tiada), dan tambah sebanyak mungkin maklumat berguna yang anda boleh! Anda sudah berada di sini, jadi buatlah rekod itu benar-benar hebat."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4"
msgstr "Di bawah “ID Numbers” pilih “Anna’s Archive” dan tambah MD5 buku dari Anna’s Archive. Ini adalah rentetan panjang huruf dan nombor selepas “/md5/” dalam URL."

#, fuzzy
msgid "page.metadata.openlib.howto.item.4.1"
msgstr "Cuba cari fail lain dalam Anna’s Archive yang juga sepadan dengan rekod ini, dan tambah juga fail-fail tersebut. Pada masa hadapan, kita boleh kumpulkan fail-fail tersebut sebagai pendua di halaman carian Anna’s Archive."

#, fuzzy
msgid "page.metadata.openlib.howto.item.5"
msgstr "Apabila anda selesai, tuliskan URL yang baru anda kemas kini. Setelah anda mengemas kini sekurang-kurangnya 30 rekod dengan MD5 dari Anna’s Archive, hantarkan kami <a %(a_contact)s>email</a> dan hantarkan senarai tersebut. Kami akan memberikan anda keahlian percuma untuk Anna’s Archive, supaya anda boleh melakukan kerja ini dengan lebih mudah (dan sebagai tanda terima kasih atas bantuan anda). Ini mesti merupakan suntingan berkualiti tinggi yang menambah sejumlah besar maklumat, jika tidak permintaan anda akan ditolak. Permintaan anda juga akan ditolak jika mana-mana suntingan dibatalkan atau diperbetulkan oleh moderator Open Library."

#, fuzzy
msgid "page.metadata.openlib.body5"
msgstr "Perhatikan bahawa ini hanya berfungsi untuk buku, bukan kertas akademik atau jenis fail lain. Untuk jenis fail lain, kami masih mengesyorkan mencari perpustakaan sumber. Mungkin mengambil masa beberapa minggu untuk perubahan dimasukkan dalam Anna’s Archive, kerana kami perlu memuat turun data dump Open Library terkini, dan menjana semula indeks carian kami."

#, fuzzy
msgid "page.mirrors.title"
msgstr "Cermin: panggilan untuk sukarelawan"

#, fuzzy
msgid "page.mirrors.intro"
msgstr "Untuk meningkatkan ketahanan Arkib Anna, kami mencari sukarelawan untuk menjalankan cermin."

#, fuzzy
msgid "page.mirrors.text1"
msgstr "Kami mencari ini:"

#, fuzzy
msgid "page.mirrors.list.run_anna"
msgstr "Anda menjalankan kod sumber terbuka Anna’s Archive, dan anda kerap mengemas kini kedua-dua kod dan data."

#, fuzzy
msgid "page.mirrors.list.clearly_a_mirror"
msgstr "Versi anda jelas dibezakan sebagai cermin, contohnya “Arkib Bob, cermin Anna’s Archive”."

#, fuzzy
msgid "page.mirrors.list.know_the_risks"
msgstr "Anda bersedia mengambil risiko yang berkaitan dengan kerja ini, yang adalah signifikan. Anda mempunyai pemahaman mendalam tentang keselamatan operasi yang diperlukan. Kandungan <a %(a_shadow)s>ini</a> <a %(a_pirate)s>pos</a> adalah jelas kepada anda."

#, fuzzy
msgid "page.mirrors.list.willing_to_contribute"
msgstr "Anda bersedia menyumbang kepada <a %(a_codebase)s>kod sumber</a> kami — dalam kerjasama dengan pasukan kami — untuk merealisasikan ini."

#, fuzzy
msgid "page.mirrors.list.maybe_partner"
msgstr "Pada mulanya kami tidak akan memberi anda akses kepada muat turun pelayan rakan kongsi kami, tetapi jika semuanya berjalan lancar, kami boleh berkongsi dengan anda."

#, fuzzy
msgid "page.mirrors.expenses.title"
msgstr "Perbelanjaan hosting"

#, fuzzy
msgid "page.mirrors.expenses.text1"
msgstr "Kami bersedia menanggung perbelanjaan hosting dan VPN, pada mulanya sehingga $200 sebulan. Ini mencukupi untuk pelayan carian asas dan proksi yang dilindungi DMCA."

#, fuzzy
msgid "page.mirrors.expenses.must_demonstrate_ability"
msgstr "Kami hanya akan membayar untuk hosting setelah anda mempunyai semuanya disediakan, dan telah menunjukkan bahawa anda mampu mengemas kini arkib dengan kemas kini. Ini bermakna anda perlu membayar untuk 1-2 bulan pertama dari poket anda sendiri."

#, fuzzy
msgid "page.mirrors.expenses.no_compensation_for_time"
msgstr "Masa anda tidak akan diberi pampasan (dan begitu juga kami), kerana ini adalah kerja sukarela sepenuhnya."

#, fuzzy
msgid "page.mirrors.expenses.maybe_donation"
msgstr "Jika anda terlibat secara signifikan dalam pembangunan dan operasi kerja kami, kami boleh berbincang untuk berkongsi lebih banyak hasil sumbangan dengan anda, untuk anda gunakan mengikut keperluan."

#, fuzzy
msgid "page.mirrors.getting_started.title"
msgstr "Memulakan"

#, fuzzy
msgid "page.mirrors.getting_started.text1"
msgstr "Sila <strong>jangan hubungi kami</strong> untuk meminta kebenaran, atau untuk soalan asas. Tindakan lebih bermakna daripada kata-kata! Semua maklumat ada di luar sana, jadi teruskan dengan menyediakan cermin anda."

#, fuzzy
msgid "page.mirrors.getting_started.text2"
msgstr "Jangan ragu untuk menghantar tiket atau permintaan gabungan ke Gitlab kami apabila anda menghadapi masalah. Kami mungkin perlu membina beberapa ciri khusus cermin dengan anda, seperti penjenamaan semula dari “Anna’s Archive” kepada nama laman web anda, (pada mulanya) melumpuhkan akaun pengguna, atau memaut kembali ke laman utama kami dari halaman buku."

#, fuzzy
msgid "page.mirrors.getting_started.text3"
msgstr "Setelah anda mempunyai cermin anda berjalan, sila hubungi kami. Kami ingin mengkaji OpSec anda, dan setelah itu kukuh, kami akan memaut ke cermin anda, dan mula bekerjasama lebih rapat dengan anda."

#, fuzzy
msgid "page.mirrors.getting_started.text4"
msgstr "Terima kasih terlebih dahulu kepada sesiapa yang bersedia menyumbang dengan cara ini! Ia bukan untuk yang lemah semangat, tetapi ia akan mengukuhkan jangka hayat perpustakaan terbuka terbesar dalam sejarah manusia."

#, fuzzy
msgid "page.partner_download.header"
msgstr "Muat turun dari laman web rakan kongsi"

#, fuzzy
msgid "page.partner_download.slow_downloads_official"
msgstr "❌ Muat turun perlahan hanya tersedia melalui laman web rasmi. Lawati %(websites)s."

#, fuzzy
msgid "page.partner_download.slow_downloads_cloudflare"
msgstr "❌ Muat turun yang perlahan tidak tersedia melalui VPN Cloudflare atau dari alamat IP Cloudflare."

#, fuzzy
msgid "page.partner_download.wait_banner"
msgstr "Sila tunggu <span %(span_countdown)s>%(wait_seconds)s</span> saat untuk memuat turun fail ini."

#, fuzzy
msgid "page.partner_download.url"
msgstr "📚 Gunakan URL berikut untuk memuat turun: <a %(a_download)s>Muat turun sekarang</a>."

#, fuzzy
msgid "page.partner_download.li4"
msgstr "Terima kasih kerana menunggu, ini memastikan laman web ini boleh diakses secara percuma untuk semua orang! 😊"

#, fuzzy
msgid "page.partner_download.warning_many_downloads"
msgstr "Amaran: terdapat banyak muat turun dari alamat IP anda dalam 24 jam terakhir. Muat turun mungkin lebih perlahan daripada biasa."

#, fuzzy
msgid "page.partner_download.downloads_last_24_hours"
msgstr "Muat turun dari alamat IP anda dalam 24 jam yang lalu: %(count)s."

#, fuzzy
msgid "page.partner_download.warning_many_downloads2"
msgstr "Jika anda menggunakan VPN, sambungan internet berkongsi, atau ISP anda berkongsi IP, amaran ini mungkin disebabkan oleh itu."

#, fuzzy
msgid "page.partner_download.wait"
msgstr "Untuk memberi peluang kepada semua orang memuat turun fail secara percuma, anda perlu menunggu sebelum anda boleh memuat turun fail ini."

#, fuzzy
msgid "page.partner_download.li1"
msgstr "Sila teruskan melayari Arkib Anna di tab yang berbeza semasa menunggu (jika pelayar anda menyokong penyegaran tab latar belakang)."

#, fuzzy
msgid "page.partner_download.li2"
msgstr "Sila tunggu beberapa halaman muat turun dimuatkan pada masa yang sama (tetapi sila hanya muat turun satu fail pada satu masa bagi setiap pelayan)."

#, fuzzy
msgid "page.partner_download.li3"
msgstr "Sebaik sahaja anda mendapat pautan muat turun, ia sah untuk beberapa jam."

#, fuzzy
msgid "layout.index.header.title"
msgstr "Arkib Anna"

#, fuzzy
msgid "page.scidb.header"
msgstr "SciDB"

#, fuzzy
msgid "page.scidb.doi"
msgstr "DOI: %(doi)s"

#, fuzzy
msgid "page.scidb.aa_record"
msgstr "Rekod dalam Arkib Anna"

#, fuzzy
msgid "page.scidb.download"
msgstr "Muat turun"

#, fuzzy
msgid "page.scidb.scihub"
msgstr "Sci-Hub"

#, fuzzy
msgid "page.scidb.nexusstc"
msgstr "Nexus/STC"

#, fuzzy
msgid "page.scidb.please_donate"
msgstr "Untuk menyokong kebolehcapaian dan pemeliharaan jangka panjang pengetahuan manusia, jadi <a %(a_donate)s>ahli</a>."

#, fuzzy
msgid "page.scidb.please_donate_bonus"
msgstr "Sebagai bonus, 🧬&nbsp;SciDB memuat lebih pantas untuk ahli, tanpa sebarang had."

#, fuzzy
msgid "page.scidb.refresh"
msgstr "Tidak berfungsi? Cuba <a %(a_refresh)s>menyegarkan</a>."

#, fuzzy
msgid "page.scidb.no_preview_new"
msgstr "Tiada pratonton tersedia lagi. Muat turun fail dari <a %(a_path)s>Arkib Anna</a>."

#, fuzzy
msgid "page.home.scidb.text2"
msgstr "🧬&nbsp;SciDB adalah kesinambungan Sci-Hub, dengan antara muka yang biasa dan paparan langsung PDF. Masukkan DOI anda untuk melihat."

#, fuzzy
msgid "page.home.scidb.text3"
msgstr "Kami mempunyai koleksi penuh Sci-Hub, serta kertas baru. Kebanyakan boleh dilihat secara langsung dengan antara muka yang biasa, serupa dengan Sci-Hub. Sesetengah boleh dimuat turun melalui sumber luaran, dalam kes ini kami menunjukkan pautan kepada mereka."

#, fuzzy
msgid "page.search.title.results"
msgstr "%(search_input)s - Cari"

#, fuzzy
msgid "page.search.title.new"
msgstr "Carian baru"

#, fuzzy
msgid "page.search.icon.include_only"
msgstr "Hanya sertakan"

#, fuzzy
msgid "page.search.icon.exclude"
msgstr "Kecualikan"

#, fuzzy
msgid "page.search.icon.unchecked"
msgstr "Tidak ditandakan"

#, fuzzy
msgid "page.search.tabs.download"
msgstr "Muat turun"

#, fuzzy
msgid "page.search.tabs.journals"
msgstr "Artikel jurnal"

#, fuzzy
msgid "page.search.tabs.digital_lending"
msgstr "Peminjaman Digital"

#, fuzzy
msgid "page.search.tabs.metadata"
msgstr "Metadata"

#, fuzzy
msgid "common.search.placeholder"
msgstr "Tajuk, pengarang, DOI, ISBN, MD5, …"

#, fuzzy
msgid "common.search.submit"
msgstr "Cari"

#, fuzzy
msgid "page.search.search_settings"
msgstr "Tetapan carian"

#, fuzzy
msgid "page.search.submit"
msgstr "Cari"

#, fuzzy
msgid "page.search.too_long_broad_query"
msgstr "Carian mengambil masa terlalu lama, yang biasa untuk pertanyaan yang luas. Kiraan penapis mungkin tidak tepat."

#, fuzzy
msgid "page.search.too_inaccurate"
msgstr "Carian mengambil masa terlalu lama, yang bermaksud anda mungkin melihat hasil yang tidak tepat. Kadang-kadang <a %(a_reload)s>memuat semula</a> halaman membantu."

#, fuzzy
msgid "page.search.filters.display.header"
msgstr "Papar"

#, fuzzy
msgid "page.search.filters.display.list"
msgstr "Senarai"

#, fuzzy
msgid "page.search.filters.display.table"
msgstr "Jadual"

#, fuzzy
msgid "page.search.advanced.header"
msgstr "Lanjutan"

#, fuzzy
msgid "page.search.advanced.description_comments"
msgstr "Cari penerangan dan komen metadata"

#, fuzzy
msgid "page.search.advanced.add_specific"
msgstr "Tambah medan carian khusus"

#, fuzzy
msgid "common.specific_search_fields.select"
msgstr "(cari medan khusus)"

#, fuzzy
msgid "page.search.advanced.field.year_published"
msgstr "Tahun diterbitkan"

#, fuzzy
msgid "page.search.filters.content.header"
msgstr "Kandungan"

#, fuzzy
msgid "page.search.filters.filetype.header"
msgstr "Jenis fail"

#, fuzzy
msgid "page.search.more"
msgstr "lagi…"

#, fuzzy
msgid "page.search.filters.access.header"
msgstr "Akses"

#, fuzzy
msgid "page.search.filters.source.header"
msgstr "Sumber"

#, fuzzy
msgid "page.search.filters.source.scraped"
msgstr "dikikis dan sumber terbuka oleh AA"

#, fuzzy
msgid "page.search.filters.language.header"
msgstr "Bahasa"

#, fuzzy
msgid "page.search.filters.order_by.header"
msgstr "Susun mengikut"

#, fuzzy
msgid "page.search.filters.sorting.most_relevant"
msgstr "Paling relevan"

#, fuzzy
msgid "page.search.filters.sorting.newest"
msgstr "Terbaru"

#, fuzzy
msgid "page.search.filters.sorting.note_publication_year"
msgstr "(tahun penerbitan)"

#, fuzzy
msgid "page.search.filters.sorting.oldest"
msgstr "Tertua"

#, fuzzy
msgid "page.search.filters.sorting.largest"
msgstr "Terbesar"

#, fuzzy
msgid "page.search.filters.sorting.note_filesize"
msgstr "(saiz fail)"

#, fuzzy
msgid "page.search.filters.sorting.smallest"
msgstr "Paling kecil"

#, fuzzy
msgid "page.search.filters.sorting.note_open_sourced"
msgstr "(sumber terbuka)"

#, fuzzy
msgid "page.search.filters.sorting.random"
msgstr "Rawak"

#, fuzzy
msgid "page.search.header.update_info"
msgstr "Indeks carian dikemas kini setiap bulan. Ia kini termasuk entri sehingga %(last_data_refresh_date)s. Untuk maklumat teknikal lanjut, lihat halaman %(link_open_tag)sdatasets</a>."

#, fuzzy
msgid "page.search.header.codes_explorer"
msgstr "Untuk meneroka indeks carian mengikut kod, gunakan <a %(a_href)s>Codes Explorer</a>."

#, fuzzy
msgid "page.search.results.search_downloads"
msgstr "Taip dalam kotak untuk mencari katalog kami yang mengandungi %(count)s fail yang boleh dimuat turun secara langsung, yang kami <a %(a_preserve)s>kekalkan selama-lamanya</a>."

#, fuzzy
msgid "page.search.results.help_preserve"
msgstr "Malah, sesiapa sahaja boleh membantu memelihara fail-fail ini dengan menyemai <a %(a_torrents)s>senarai torrent bersatu kami</a>."

#, fuzzy
msgid "page.search.results.most_comprehensive"
msgstr "Kami kini mempunyai katalog terbuka buku, kertas kerja, dan karya bertulis lain yang paling komprehensif di dunia. Kami mencermin Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>dan banyak lagi</a>."

#, fuzzy
msgid "page.search.results.other_shadow_libs"
msgstr "Jika anda menemui “perpustakaan bayangan” lain yang patut kami cerminkan, atau jika anda mempunyai sebarang soalan, sila hubungi kami di %(email)s."

#, fuzzy
msgid "page.search.results.dmca"
msgstr "Untuk tuntutan DMCA / hak cipta <a %(a_copyright)s>klik di sini</a>."

#, fuzzy
msgid "page.search.results.shortcuts"
msgstr "Tip: gunakan pintasan papan kekunci “/” (fokus carian), “enter” (cari), “j” (atas), “k” (bawah), “<” (halaman sebelumnya), “>” (halaman seterusnya) untuk navigasi yang lebih cepat."

#, fuzzy
msgid "page.search.results.looking_for_papers"
msgstr "Mencari kertas kerja?"

#, fuzzy
msgid "page.search.results.search_journals"
msgstr "Taip dalam kotak untuk mencari katalog kami yang mengandungi %(count)s kertas kerja akademik dan artikel jurnal, yang kami <a %(a_preserve)s>kekalkan selama-lamanya</a>."

#, fuzzy
msgid "page.search.results.search_digital_lending"
msgstr "Taip dalam kotak untuk mencari fail dalam perpustakaan pinjaman digital."

#, fuzzy
msgid "page.search.results.digital_lending_info"
msgstr "Indeks carian ini kini termasuk metadata dari perpustakaan Peminjaman Digital Terkawal Internet Archive. <a %(a_datasets)s>Maklumat lanjut tentang datasets kami</a>."

#, fuzzy
msgid "page.search.results.digital_lending_info_more"
msgstr "Untuk lebih banyak perpustakaan pinjaman digital, lihat <a %(a_wikipedia)s>Wikipedia</a> dan <a %(a_mobileread)s>MobileRead Wiki</a>."

#, fuzzy
msgid "page.search.results.search_metadata"
msgstr "Taip dalam kotak untuk mencari metadata dari perpustakaan. Ini boleh berguna apabila <a %(a_request)s>meminta fail</a>."

#, fuzzy
msgid "page.search.results.metadata_info"
msgstr "Indeks carian ini kini termasuk metadata dari pelbagai sumber metadata. <a %(a_datasets)s>Lebih lanjut mengenai dataset kami</a>."

#, fuzzy
msgid "page.search.results.metadata_no_merging"
msgstr "Untuk metadata, kami menunjukkan rekod asal. Kami tidak menggabungkan rekod."

#, fuzzy
msgid "page.search.results.metadata_info_more"
msgstr "Terdapat banyak, banyak sumber metadata untuk karya bertulis di seluruh dunia. <a %(a_wikipedia)s>Laman Wikipedia ini</a> adalah permulaan yang baik, tetapi jika anda tahu senarai baik yang lain, sila maklumkan kepada kami."

#, fuzzy
msgid "page.search.results.search_generic"
msgstr "Taip dalam kotak untuk mencari."

#, fuzzy
msgid "page.search.results.these_are_records"
msgstr "Ini adalah rekod metadata, <span %(classname)s>bukan</span> fail yang boleh dimuat turun."

#, fuzzy
msgid "page.search.results.error.header"
msgstr "Ralat semasa carian."

#, fuzzy
msgid "page.search.results.error.unknown"
msgstr "Cuba <a %(a_reload)s>muat semula halaman</a>. Jika masalah berterusan, sila emel kami di %(email)s."

#, fuzzy
msgid "page.search.results.none"
msgstr "<span class=\"font-bold\">Tiada fail dijumpai.</span> Cuba gunakan istilah carian dan penapis yang kurang atau berbeza."

#, fuzzy
msgid "page.search.results.incorrectly_slow"
msgstr "➡️ Kadang-kadang ini berlaku secara tidak betul apabila pelayan carian lambat. Dalam kes sedemikian, <a %(a_attrs)s>memuat semula</a> boleh membantu."

#, fuzzy
msgid "page.search.found_matches.main"
msgstr "Kami telah menemui padanan dalam: %(in)s. Anda boleh merujuk kepada URL yang terdapat di sana apabila <a %(a_request)s>meminta fail</a>."

#, fuzzy
msgid "page.search.found_matches.journals"
msgstr "Artikel Jurnal (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.digital_lending"
msgstr "Peminjaman Digital (%(count)s)"

#, fuzzy
msgid "page.search.found_matches.metadata"
msgstr "Metadata (%(count)s)"

#, fuzzy
msgid "page.search.results.numbers_pages"
msgstr "Keputusan %(from)s-%(to)s (%(total)s jumlah keseluruhan)"

#, fuzzy
msgid "page.search.results.partial_more"
msgstr "%(num)d+ padanan separa"

#, fuzzy
msgid "page.search.results.partial"
msgstr "%(num)d padanan separa"

#, fuzzy
msgid "page.volunteering.title"
msgstr "Sukarelawan & Ganjaran"

#, fuzzy
msgid "page.volunteering.intro.text1"
msgstr "Anna’s Archive bergantung kepada sukarelawan seperti anda. Kami mengalu-alukan semua tahap komitmen, dan mempunyai dua kategori utama bantuan yang kami cari:"

#, fuzzy
msgid "page.volunteering.intro.light"
msgstr "<span %(label)s>Kerja sukarela ringan:</span> jika anda hanya boleh meluangkan beberapa jam di sana sini, masih terdapat banyak cara anda boleh membantu. Kami memberi ganjaran kepada sukarelawan yang konsisten dengan <span %(bold)s>🤝 keahlian Anna’s Archive</span>."

#, fuzzy
msgid "page.volunteering.intro.heavy"
msgstr "<span %(label)s>Kerja sukarela berat (USD$50-USD$5,000 ganjaran):</span> jika anda mampu mendedikasikan banyak masa dan/atau sumber kepada misi kami, kami ingin bekerjasama dengan anda dengan lebih rapat. Akhirnya anda boleh sertai pasukan dalaman. Walaupun bajet kami ketat, kami mampu memberikan <span %(bold)s>💰 ganjaran wang</span> untuk kerja yang paling intensif."

#, fuzzy
msgid "page.volunteering.intro.text2"
msgstr "Jika anda tidak dapat menyumbangkan masa, anda masih boleh membantu kami dengan banyak cara seperti <a %(a_donate)s>menderma wang</a>, <a %(a_torrents)s>menyemai torrents kami</a>, <a %(a_uploading)s>memuat naik buku</a>, atau <a %(a_help)s>memberitahu rakan-rakan anda tentang Arkib Anna</a>."

#, fuzzy
msgid "page.volunteering.intro.text3"
msgstr "<span %(bold)s>Syarikat:</span> kami menawarkan akses langsung berkelajuan tinggi kepada koleksi kami sebagai pertukaran untuk derma peringkat perusahaan atau pertukaran untuk koleksi baru (contohnya imbasan baru, datasets OCR, memperkayakan data kami). <a %(a_contact)s>Hubungi kami</a> jika ini anda. Lihat juga <a %(a_llm)s>halaman LLM kami</a>."

#, fuzzy
msgid "page.volunteering.section.light.heading"
msgstr "Kerja sukarela ringan"

#, fuzzy
msgid "page.volunteering.section.light.text1"
msgstr "Jika anda mempunyai beberapa jam untuk diluangkan, anda boleh membantu dalam pelbagai cara. Pastikan anda sertai <a %(a_telegram)s>perbualan sukarelawan di Telegram</a>."

#, fuzzy
msgid "page.volunteering.section.light.text2"
msgstr "Sebagai tanda penghargaan, kami biasanya memberikan 6 bulan “Pustakawan Bertuah” untuk pencapaian asas, dan lebih lagi untuk kerja sukarela yang berterusan. Semua pencapaian memerlukan kerja berkualiti tinggi — kerja yang tidak kemas lebih menyusahkan kami dan kami akan menolaknya. Sila <a %(a_contact)s>emel kami</a> apabila anda mencapai pencapaian."

#, fuzzy
msgid "page.volunteering.table.header.task"
msgstr "Tugas"

#, fuzzy
msgid "page.volunteering.table.header.milestone"
msgstr "Pencapaian"

#, fuzzy
msgid "page.volunteering.table.spread_the_word.task.alt1"
msgstr "Menyebarkan berita tentang Arkib Anna. Sebagai contoh, dengan mengesyorkan buku di AA, memautkan ke catatan blog kami, atau secara amnya mengarahkan orang ke laman web kami."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone_count"
msgstr "%(links)s pautan atau tangkapan skrin."

#, fuzzy
msgid "page.volunteering.table.spread_the_word.milestone.let_them_know"
msgstr "Ini sepatutnya menunjukkan anda memberitahu seseorang tentang Arkib Anna, dan mereka mengucapkan terima kasih kepada anda."

#, fuzzy
msgid "page.volunteering.table.open_library.task"
msgstr "Perbaiki metadata dengan <a %(a_metadata)s>menghubungkan</a> dengan Open Library."

#, fuzzy
msgid "page.volunteering.table.open_library.random_metadata"
msgstr "Anda boleh menggunakan <a %(a_list)s>senarai isu metadata rawak</a> sebagai titik permulaan."

#, fuzzy
msgid "page.volunteering.table.open_library.leave_comment"
msgstr "Pastikan untuk meninggalkan komen pada isu yang anda selesaikan, supaya orang lain tidak menggandakan kerja anda."

#, fuzzy
msgid "page.volunteering.table.open_library.milestone_count"
msgstr "%(links)s pautan rekod yang anda perbaiki."

#, fuzzy
msgid "page.volunteering.table.translate.task"
msgstr "<a %(a_translate)s>Menterjemah</a> laman web."

#, fuzzy
msgid "page.volunteering.table.translate.milestone"
msgstr "Terjemah sepenuhnya satu bahasa (jika ia belum hampir siap)."

#, fuzzy
msgid "page.volunteering.table.wikipedia.task"
msgstr "Perbaiki halaman Wikipedia untuk Arkib Anna dalam bahasa anda. Sertakan maklumat dari halaman Wikipedia AA dalam bahasa lain, dan dari laman web dan blog kami. Tambah rujukan kepada AA di halaman berkaitan lain."

#, fuzzy
msgid "page.volunteering.table.wikipedia.milestone"
msgstr "Pautan ke sejarah suntingan yang menunjukkan anda membuat sumbangan yang signifikan."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.task"
msgstr "Memenuhi permintaan buku (atau kertas, dll) di forum Z-Library atau Library Genesis. Kami tidak mempunyai sistem permintaan buku sendiri, tetapi kami mencerminkan perpustakaan tersebut, jadi menjadikannya lebih baik juga menjadikan Arkib Anna lebih baik."

#, fuzzy
msgid "page.volunteering.table.fulfill_requests.milestone_count"
msgstr "%(links)s pautan atau tangkapan skrin permintaan yang anda penuhi."

#, fuzzy
msgid "page.volunteering.table.misc.task"
msgstr "Tugas kecil yang diposkan di <a %(a_telegram)s>perbualan sukarelawan di Telegram</a>. Biasanya untuk keahlian, kadang-kadang untuk ganjaran kecil."

#, fuzzy
msgid "page.volunteering.table.misc.task2"
msgstr "Tugas kecil diposkan dalam kumpulan sembang sukarelawan kami."

#, fuzzy
msgid "page.volunteering.table.misc.milestone"
msgstr "Bergantung pada tugas."

#, fuzzy
msgid "page.volunteering.section.bounties.heading"
msgstr "Ganjaran"

#, fuzzy
msgid "page.volunteering.section.bounties.text1"
msgstr "Kami sentiasa mencari individu yang mempunyai kemahiran pengaturcaraan atau keselamatan ofensif yang kukuh untuk terlibat. Anda boleh membuat sumbangan besar dalam memelihara warisan manusia."

#, fuzzy
msgid "page.volunteering.section.bounties.text2"
msgstr "Sebagai tanda terima kasih, kami memberikan keahlian untuk sumbangan yang kukuh. Sebagai tanda terima kasih yang besar, kami memberikan ganjaran wang untuk tugas-tugas yang sangat penting dan sukar. Ini tidak sepatutnya dilihat sebagai pengganti pekerjaan, tetapi ia adalah insentif tambahan dan boleh membantu dengan kos yang ditanggung."

#, fuzzy
msgid "page.volunteering.section.bounties.text3"
msgstr "Kebanyakan kod kami adalah sumber terbuka, dan kami akan meminta kod anda juga bersifat sumber terbuka apabila memberikan ganjaran. Terdapat beberapa pengecualian yang boleh kita bincangkan secara individu."

#, fuzzy
msgid "page.volunteering.section.bounties.text4"
msgstr "Ganjaran diberikan kepada orang pertama yang menyelesaikan tugas. Sila komen pada tiket ganjaran untuk memberitahu orang lain bahawa anda sedang mengusahakan sesuatu, supaya orang lain boleh menahan diri atau menghubungi anda untuk bekerjasama. Tetapi sedar bahawa orang lain masih bebas untuk mengusahakannya juga dan cuba mengalahkan anda. Walau bagaimanapun, kami tidak memberikan ganjaran untuk kerja yang tidak kemas. Jika dua penyerahan berkualiti tinggi dibuat berdekatan antara satu sama lain (dalam masa sehari atau dua), kami mungkin memilih untuk memberikan ganjaran kepada kedua-duanya, mengikut budi bicara kami, contohnya 100%% untuk penyerahan pertama dan 50%% untuk penyerahan kedua (jadi 150%% keseluruhan)."

#, fuzzy
msgid "page.volunteering.section.bounties.text5"
msgstr "Untuk ganjaran yang lebih besar (terutamanya ganjaran pengikisan), sila hubungi kami apabila anda telah menyelesaikan ~5%% daripadanya, dan anda yakin bahawa kaedah anda akan berkembang ke tahap penuh. Anda perlu berkongsi kaedah anda dengan kami supaya kami boleh memberikan maklum balas. Juga, dengan cara ini kami boleh memutuskan apa yang perlu dilakukan jika terdapat beberapa orang yang hampir mencapai ganjaran, seperti berpotensi memberikan ganjaran kepada beberapa orang, menggalakkan orang untuk bekerjasama, dan sebagainya."

#, fuzzy
msgid "page.volunteering.section.bounties.text6"
msgstr "AMARAN: tugas ganjaran tinggi adalah <span %(bold)s>sukar</span> — mungkin bijak untuk memulakan dengan yang lebih mudah."

#, fuzzy
msgid "page.volunteering.section.bounties.text7"
msgstr "Pergi ke <a %(a_gitlab)s>senarai isu Gitlab kami</a> dan susun mengikut “Keutamaan Label”. Ini menunjukkan secara kasar susunan tugas yang kami utamakan. Tugas tanpa ganjaran eksplisit masih layak untuk keahlian, terutamanya yang ditandakan “Diterima” dan “Kegemaran Anna”. Anda mungkin mahu memulakan dengan “Projek Permulaan”."

#, fuzzy
msgid "blog.template.subheading"
msgstr "Kemaskini tentang <a %(wikipedia_annas_archive)s>Arkib Anna</a>, perpustakaan terbuka terbesar dalam sejarah manusia."

#, fuzzy
msgid "layout.index.title"
msgstr "Arkib Anna"

#, fuzzy
msgid "layout.index.meta.description"
msgstr "Perpustakaan sumber terbuka dan data terbuka terbesar di dunia. Mencermin Sci-Hub, Library Genesis, Z-Library, dan banyak lagi."

#, fuzzy
msgid "layout.index.meta.opensearch"
msgstr "Cari Arkib Anna"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.help"
msgstr "Arkib Anna memerlukan bantuan anda!"

#, fuzzy
msgid "layout.index.header.banner.fundraiser.takedown"
msgstr "Ramai yang cuba menjatuhkan kami, tetapi kami melawan kembali."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.now"
msgstr "Jika anda menderma sekarang, anda mendapat <strong>dua kali ganda</strong> bilangan muat turun pantas."

#, fuzzy
msgid "layout.index.header.banner.fundraiser.valid_end_of_month"
msgstr "Sah sehingga akhir bulan ini."

#, fuzzy
msgid "layout.index.header.nav.donate"
msgstr "Derma"

msgid "layout.index.header.banner.holiday_gift"
msgstr "Menyelamatkan pengetahuan manusia: hadiah percutian yang hebat!"

msgid "layout.index.header.banner.surprise"
msgstr "Kejutkan orang yang disayangi, berikan mereka akaun dengan keahlian."

#, fuzzy
msgid "layout.index.header.banner.mirrors"
msgstr "Untuk meningkatkan ketahanan Arkib Anna, kami mencari sukarelawan untuk menjalankan cermin."

#, fuzzy
msgid "layout.index.header.banner.valentine_gift"
msgstr "Hadiah Valentine yang sempurna!"

#, fuzzy
msgid "layout.index.header.banner.new_donation_method"
msgstr "Kami mempunyai kaedah derma baru yang tersedia: %(method_name)s. Sila pertimbangkan untuk %(donate_link_open_tag)smenderma</a> — menjalankan laman web ini tidak murah, dan derma anda benar-benar membuat perbezaan. Terima kasih banyak."

#, fuzzy
msgid "layout.index.banners.comics_fundraiser.text"
msgstr "Kami sedang menjalankan pengumpulan dana untuk <a href=\"https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html\">menyandarkan</a> perpustakaan bayangan komik terbesar di dunia. Terima kasih atas sokongan anda! <a href=\"/donate\">Derma.</a> Jika anda tidak boleh menderma, pertimbangkan untuk menyokong kami dengan memberitahu rakan-rakan anda, dan mengikuti kami di <a href=\"https://www.reddit.com/r/Annas_Archive\">Reddit</a>, atau <a href=\"https://t.me/annasarchiveorg\">Telegram</a>."

#, fuzzy
msgid "layout.index.header.recent_downloads"
msgstr "Muat turun terkini:"

#, fuzzy
msgid "layout.index.header.nav.search"
msgstr "Cari"

#, fuzzy
msgid "layout.index.header.nav.faq"
msgstr "Soalan Lazim"

#, fuzzy
msgid "layout.index.header.nav.improve_metadata"
msgstr "Tingkatkan metadata"

#, fuzzy
msgid "layout.index.header.nav.volunteering"
msgstr "Sukarelawan & Ganjaran"

#, fuzzy
msgid "layout.index.header.nav.datasets"
msgstr "Datasets"

#, fuzzy
msgid "layout.index.header.nav.torrents"
msgstr "Torrent"

#, fuzzy
msgid "layout.index.header.nav.activity"
msgstr "Aktiviti"

#, fuzzy
msgid "layout.index.header.nav.codes"
msgstr "Peneroka Kod"

#, fuzzy
msgid "layout.index.header.nav.llm_data"
msgstr "Data LLM"

#, fuzzy
msgid "layout.index.header.nav.home"
msgstr "Laman Utama"

#, fuzzy
msgid "layout.index.header.nav.annassoftware"
msgstr "Perisian Anna ↗"

#, fuzzy
msgid "layout.index.header.nav.translate"
msgstr "Terjemah ↗"

#, fuzzy
msgid "layout.index.header.nav.login_register"
msgstr "Log masuk / Daftar"

#, fuzzy
msgid "layout.index.header.nav.account"
msgstr "Akaun"

msgid "layout.index.footer.list1.header"
msgstr "Arkib Anna"

#, fuzzy
msgid "layout.index.footer.list2.header"
msgstr "Kekal berhubung"

#, fuzzy
msgid "layout.index.footer.list2.dmca_copyright"
msgstr "Tuntutan DMCA / hak cipta"

#, fuzzy
msgid "layout.index.footer.list2.reddit"
msgstr "Reddit"

#, fuzzy
msgid "layout.index.header.nav.advanced"
msgstr "Lanjutan"

#, fuzzy
msgid "layout.index.header.nav.security"
msgstr "Keselamatan"

#, fuzzy
msgid "layout.index.footer.list3.header"
msgstr "Alternatif"

#, fuzzy
msgid "layout.index.footer.list3.link.slum"
msgstr "SLUM (%(unaffiliated)s)"

#, fuzzy
msgid "layout.index.footer.list3.link.unaffiliated"
msgstr "tidak berafiliasi"

#, fuzzy
msgid "page.search.results.issues"
msgstr "❌ Fail ini mungkin mempunyai masalah."

#, fuzzy
msgid "page.search.results.fast_download"
msgstr "Muat turun pantas"

#, fuzzy
msgid "page.donate.copy"
msgstr "salin"

#, fuzzy
msgid "page.donate.copied"
msgstr "disalin!"

#, fuzzy
msgid "page.search.pagination.prev"
msgstr "Sebelumnya"

#, fuzzy
msgid "page.search.pagination.numbers_spacing"
msgstr "…"

#, fuzzy
msgid "page.search.pagination.next"
msgstr "Seterusnya"

#~ msgid "page.home.torrents.number"
#~ msgstr ""

#~ msgid "layout.index.header.tagline_new2"
#~ msgstr "⭐️&nbsp;Cermin%(libraries)s, dan banyak lagi."

#~ msgid "page.home.preservation.text"
#~ msgstr ""

#~ msgid "page.doi.title"
#~ msgstr ""

#~ msgid "page.doi.breadcrumbs"
#~ msgstr ""

#~ msgid "page.doi.invalid.header"
#~ msgstr ""

#~ msgid "page.doi.invalid.text"
#~ msgstr ""

#~ msgid "page.doi.box.header"
#~ msgstr ""

#~ msgid "page.doi.box.canonical_url"
#~ msgstr ""

#~ msgid "page.doi.box.scihub"
#~ msgstr ""

#~ msgid "page.doi.results.text"
#~ msgstr ""

#~ msgid "page.doi.results.none"
#~ msgstr ""

#~ msgid "page.md5.box.download.header_fast_member_no_remaining"
#~ msgstr ""

#~ msgid "page.fast_downloads.no_more"
#~ msgstr ""

#~ msgid "page.donate.faq.text_other_contribs"
#~ msgstr "<div %(div_question)s>Bolehkah saya menyumbang dengan cara lain?</div> Boleh! Sila lihat <a href=\"/about\">halaman about</a> di bawah “Bagaimana untuk membantu”."

#~ msgid "page.donate.faq.monetizing"
#~ msgstr "<div %(div_question)s>Saya tidak suka anda \"mengewangkan\" Anna's Archive!</div> Jika anda tidak suka cara kami mengendalikan projek kami, sila cipta perpustakaan bayangan anda sendiri! Semua kod dan data kami adalah sumber terbuka jadi tiada apa yang menghalang anda. ;)"

#~ msgid "page.request.title"
#~ msgstr "Permintaan buku"

#~ msgid "page.request.text1"
#~ msgstr "Buat masa ini, sila minta eBook di <a %(a_forum)s>forum Libgen.rs</a>. Anda boleh mencipta akaun di sana dan siarkan di salah satu bebenang ini:"

#~ msgid "page.request.text2"
#~ msgstr "<li %(li_item)s>Gunakan <a %(a_ebook)s>bebenang ini</a> untuk eBook.</li><li %(li_item)s>Gunakan <a %(a_regular)s>bebenang ini</a> untuk buku-buku yang tidak dapat diperolehi dalam bentuk eBook.</li>"

#~ msgid "page.request.text3"
#~ msgstr "Dalam kedua-dua kes, pastikan anda mengikuti peraturan yang dinyatakan di dalam bebenang tersebut."

#~ msgid "page.upload.title"
#~ msgstr "Muat naik"

#~ msgid "page.upload.libgen.header"
#~ msgstr ""

#~ msgid "page.upload.zlib.header"
#~ msgstr ""

#~ msgid "page.upload.large.header"
#~ msgstr ""

#~ msgid "page.about.title"
#~ msgstr ""

#~ msgid "page.about.header"
#~ msgstr ""

#~ msgid "page.home.search.header"
#~ msgstr ""

#~ msgid "page.home.search.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.header"
#~ msgstr ""

#~ msgid "page.home.random_book.intro"
#~ msgstr ""

#~ msgid "page.home.random_book.submit"
#~ msgstr ""

#~ msgid "page.about.text1"
#~ msgstr ""

#~ msgid "page.about.text4"
#~ msgstr ""

#~ msgid "page.home.explore.header"
#~ msgstr ""

#~ msgid "page.home.explore.intro"
#~ msgstr ""

#~ msgid "page.wechat.header"
#~ msgstr ""

#~ msgid "page.wechat.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.about"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mobile"
#~ msgstr ""

#~ msgid "layout.index.header.nav.wechat"
#~ msgstr ""

#~ msgid "layout.index.header.nav.request"
#~ msgstr ""

#~ msgid "layout.index.header.nav.upload"
#~ msgstr ""

#~ msgid "layout.index.header.nav.refer"
#~ msgstr ""

#~ msgid "page.about.help.header"
#~ msgstr ""

#~ msgid "page.refer.title"
#~ msgstr ""

#~ msgid "page.refer.section1.intro"
#~ msgstr ""

#~ msgid "page.refer.section1.list_start"
#~ msgstr ""

#~ msgid "page.refer.section1.list_1"
#~ msgstr ""

#~ msgid "page.refer.section1.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_start"
#~ msgstr ""

#~ msgid "page.refer.section2.list_1"
#~ msgstr ""

#~ msgid "page.refer.section2.list_2"
#~ msgstr ""

#~ msgid "page.refer.section2.list_3"
#~ msgstr ""

#~ msgid "page.refer.linkbox.header"
#~ msgstr ""

#~ msgid "page.refer.linkbox.login"
#~ msgstr ""

#~ msgid "page.refer.linkbox.donate"
#~ msgstr ""

#~ msgid "page.refer.linkbox.remember"
#~ msgstr ""

#~ msgid "common.record_sources_mapping.ia"
#~ msgstr "Internet Archive"

#~ msgid "page.donation.payment.alipay.text1"
#~ msgstr "Derma %(total)s menggunakan %(a_account)s akaun Alipay ini"

#~ msgid "page.upload.zlib.text"
#~ msgstr ""

#~ msgid "page.home.mirrors.body"
#~ msgstr ""

#~ msgid "layout.index.header.nav.mirrors"
#~ msgstr ""

#~ msgid "page.scidb.no_preview"
#~ msgstr ""

#~ msgid "page.donate.perks.only_this_month"
#~ msgstr "hanya bulan ini!"

#~ msgid "page.home.scidb.text1"
#~ msgstr "Sci-Hub telah <a %(a_closed)s>menghentikan</a> pemuatan kertas baru."

#~ msgid "page.donate.payment.intro"
#~ msgstr "Sila pilih kaedah pembayaran. Kami memberi diskaun untuk pembayaran berasaskan kripto %(bitcoin_icon)s, kerana kami menanggung yuran yang lebih sedikit."

#~ msgid "page.donate.payment.intro2"
#~ msgstr "Sila pilih kaedah pembayaran. Setakat ini, kami hanya menerima pembayaran berasaskan kripto %(bitcoin_icon)s sahaja kerana pemproses pembayaran tradisional lain enggan bekerjasama dengan kami."

#~ msgid "page.donate.payment.desc.credit_debit_p1"
#~ msgstr "Kami tidak dapat menyokong kad kredit/debit secara langsung, kerana bank tidak mahu bekerjasama dengan kami. :("

#~ msgid "page.donate.payment.desc.credit_debit_p2"
#~ msgstr "Walau bagaimanapun, terdapat beberapa cara untuk menggunakan kad kredit/debit, menggunakan kaedah pembayaran kami yang lain:"

#~ msgid "page.md5.box.download.header_slow"
#~ msgstr "🐢 Muat turun perlahan & luaran"

#~ msgid "page.md5.box.download.header_generic"
#~ msgstr "Muat turun"

#~ msgid "page.donate.payment.desc.crypto_suggestion"
#~ msgstr "Jika anda menggunakan kripto buat kali pertama, kami mencadangkan anda untuk mengguna %(option1)s, %(option2)s atau %(option3)s untuk membeli dan menderma Bitcoin ( mata wang kripto asal dan paling banyak digunakan)."

#~ msgid "page.volunteering.table.open_library.milestone"
#~ msgstr "30 pautan rekod yang anda perbaiki."

#~ msgid "page.volunteering.table.spread_the_word.milestone"
#~ msgstr "100 pautan atau tangkapan skrin."

#~ msgid "page.volunteering.table.fulfill_requests.milestone"
#~ msgstr "30 pautan atau tangkapan skrin permintaan yang anda penuhi."

#~ msgid "page.datasets.intro.text1"
#~ msgstr "Jika anda berminat untuk mencerminkan datasets ini untuk tujuan <a %(a_faq)s>arkib</a> atau <a %(a_llm)s>latihan LLM</a>, sila hubungi kami."

#~ msgid "page.datasets.ia.intro"
#~ msgstr "Jika anda berminat untuk mencerminkan set data ini untuk tujuan <a %(a_archival)s>arkib</a> atau <a %(a_llm)s>latihan LLM</a>, sila hubungi kami."

#~ msgid "page.datasets.ia.ia_main_website"
#~ msgstr "Laman web utama"

#~ msgid "page.datasets/isbn_ranges.title"
#~ msgstr "Maklumat negara ISBN"

#~ msgid "page.datasets/isbn_ranges.intro"
#~ msgstr "Jika anda berminat untuk mencerminkan set data ini untuk tujuan <a %(a_archival)s>arkib</a> atau <a %(a_llm)s>latihan LLM</a>, sila hubungi kami."

#~ msgid "page.datasets/isbn_ranges.text1"
#~ msgstr "Agensi ISBN Antarabangsa secara berkala mengeluarkan julat yang telah diperuntukkan kepada agensi ISBN kebangsaan. Daripada ini, kami boleh menentukan negara, wilayah, atau kumpulan bahasa yang dimiliki ISBN ini. Kami kini menggunakan data ini secara tidak langsung, melalui perpustakaan Python <a %(a_isbnlib)s>isbnlib</a>."

#~ msgid "page.datasets/isbn_ranges.resources"
#~ msgstr "Sumber"

#~ msgid "page.datasets/isbn_ranges.last_updated"
#~ msgstr "Kali terakhir dikemas kini: %(isbn_country_date)s (%(link)s)"

#~ msgid "page.datasets/isbn_ranges.isbn_website"
#~ msgstr "Laman web ISBN"

#~ msgid "page.datasets/isbn_ranges.isbn_metadata"
#~ msgstr "Metadata"

#~ msgid "common.record_sources.mapping.lgli.excluding_scimag"
#~ msgstr "Tidak termasuk “scimag”"

#~ msgid "page.faq.metadata.inspiration1"
#~ msgstr "Inspirasi kami untuk mengumpul metadata adalah matlamat Aaron Swartz untuk “satu halaman web untuk setiap buku yang pernah diterbitkan”, yang mana beliau mencipta <a %(a_openlib)s>Open Library</a>."

#~ msgid "page.faq.metadata.inspiration2"
#~ msgstr "Projek itu telah berjaya, tetapi kedudukan unik kami membolehkan kami mendapatkan metadata yang mereka tidak boleh."

#~ msgid "page.faq.metadata.inspiration3"
#~ msgstr "Inspirasi lain adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku yang ada di dunia</a>, supaya kami boleh mengira berapa banyak buku yang masih perlu diselamatkan."

#~ msgid "page.partner_download.text1"
#~ msgstr "Untuk memberi peluang kepada semua orang untuk memuat turun fail secara percuma, anda perlu menunggu <strong>%(wait_seconds)s saat</strong> sebelum anda boleh memuat turun fail ini."

#~ msgid "page.partner_download.automatic_refreshing"
#~ msgstr "Segarkan halaman secara automatik. Jika anda terlepas tetingkap muat turun, pemasa akan dimulakan semula, jadi penyegaran automatik adalah disyorkan."

#~ msgid "page.partner_download.download_now"
#~ msgstr "Muat turun sekarang"

#~ msgid "common.record_soruces_mapping.nexusstc"
#~ msgstr "Nexus/STC"

#~ msgid "page.md5.box.download.convert"
#~ msgstr "Tukar: gunakan alat dalam talian untuk menukar antara format. Sebagai contoh, untuk menukar antara epub dan pdf, gunakan <a %(a_cloudconvert)s>CloudConvert</a>."

#~ msgid "page.md5.box.download.kindle"
#~ msgstr "Kindle: muat turun fail (pdf atau epub disokong), kemudian <a %(a_kindle)s>hantarkannya ke Kindle</a> menggunakan web, aplikasi, atau e-mel. Alat yang berguna: <a %(a_kobosend)s rel=\"noopener noreferrer nofollow\">1</a>."

#~ msgid "page.md5.box.download.support_authors"
#~ msgstr "Sokong pengarang: Jika anda suka ini dan mampu, pertimbangkan untuk membeli yang asal, atau menyokong pengarang secara langsung."

#~ msgid "page.md5.box.download.support_libraries"
#~ msgstr "Sokong perpustakaan: Jika ini tersedia di perpustakaan tempatan anda, pertimbangkan untuk meminjamnya secara percuma di sana."

#~ msgid "page.datasets.sources.isbndb.metadata1"
#~ msgstr "%(icon)s Tidak tersedia secara langsung dalam jumlah besar, hanya dalam separa besar di belakang paywall"

#~ msgid "page.datasets.sources.isbndb.metadata2"
#~ msgstr "%(icon)s Arkib Anna mengurus koleksi <a %(isbndb)s>metadata ISBNdb</a>"

#~ msgid "page.datasets.isbndb.title"
#~ msgstr "ISBNdb"

#~ msgid "page.datasets.isbndb.description"
#~ msgstr "ISBNdb adalah sebuah syarikat yang mengumpul metadata ISBN dari pelbagai kedai buku dalam talian. Arkib Anna telah membuat sandaran metadata buku ISBNdb. Metadata ini boleh didapati melalui Arkib Anna (walaupun tidak dalam carian buat masa ini, kecuali jika anda mencari nombor ISBN secara eksplisit)."

#~ msgid "page.datasets.isbndb.technical"
#~ msgstr "Untuk butiran teknikal, lihat di bawah. Pada satu ketika, kita boleh menggunakannya untuk menentukan buku mana yang masih hilang dari perpustakaan bayangan, untuk memprioritikan buku mana yang perlu dicari dan/atau diimbas."

#~ msgid "page.datasets.isbndb.blog_post"
#~ msgstr "Catatan blog kami tentang data ini"

#~ msgid "page.datasets.isbndb.scrape.title"
#~ msgstr "Pengumpulan ISBNdb"

#~ msgid "page.datasets.isbndb.release1.text4"
#~ msgstr "Pada masa ini, kami mempunyai satu torrent, yang mengandungi fail 4.4GB gzipped <a %(a_jsonl)s>JSON Lines</a> (20GB tidak dimampatkan): \"isbndb_2022_09.jsonl.gz\". Untuk mengimport fail \".jsonl\" ke dalam PostgreSQL, anda boleh menggunakan sesuatu seperti <a %(a_script)s>skrip ini</a>. Anda juga boleh mengalirkannya secara langsung menggunakan sesuatu seperti %(example_code)s supaya ia dinyahmampatkan secara langsung."

#~ msgid "page.donate.wait"
#~ msgstr "Sila tunggu sekurang-kurangnya <span %(span_hours)s>dua jam</span> (dan muat semula halaman ini) sebelum menghubungi kami."

#~ msgid "page.codes.search_archive"
#~ msgstr "Cari Arkib Anna untuk “%(term)s”"

#~ msgid "page.donate.payment.desc.alipay_wechat"
#~ msgstr "Derma melalui Alipay atau WeChat. Anda boleh memilih kaedah-kaedah pembayaran ini di halaman seterusnya."

#~ msgid "page.volunteering.table.spread_the_word.task"
#~ msgstr "Sebarkan berita tentang Arkib Anna di media sosial dan forum dalam talian, dengan mengesyorkan buku atau senarai di AA, atau menjawab soalan."

#~ msgid "page.datasets.sources.libgen_li.files2"
#~ msgstr "%(icon)s Koleksi Fiksyen telah berbeza tetapi masih mempunyai <a %(libgenli)s>torrents</a>, walaupun tidak dikemas kini sejak 2022 (kami mempunyai muat turun langsung)."

#~ msgid "page.datasets.sources.libgen_li.files3"
#~ msgstr "%(icon)s Arkib Anna dan Libgen.li secara bersama-sama mengurus koleksi <a %(comics)s>buku komik</a> dan <a %(magazines)s>majalah</a>."

#~ msgid "page.datasets.sources.libgen_li.files4"
#~ msgstr "%(icon)s Tiada torrent untuk koleksi fiksyen Rusia dan dokumen standard."

#~ msgid "page.datasets.libgen_li.description4"
#~ msgstr "Tiada torrent tersedia untuk kandungan tambahan. Torrent yang terdapat di laman web Libgen.li adalah cermin dari torrent lain yang disenaraikan di sini. Satu pengecualian adalah torrent fiksyen yang bermula pada %(fiction_starting_point)s. Torrent komik dan majalah dikeluarkan sebagai kerjasama antara Arkib Anna dan Libgen.li."

#~ msgid "common.md5.servers.fast_partner.recommended"
#~ msgstr "(%(recommended)s)"

#~ msgid "page.datasets.upload.source.alexandrina"
#~ msgstr "Daripada koleksi <a %(a_href)s><q>Bibliotheca Alexandrina,</q></a> asal usul tepat tidak jelas. Sebahagiannya daripada the-eye.eu, sebahagiannya daripada sumber lain."

#~ msgid "layout.index.footer.list2.telegram"
#~ msgstr "Telegram"

