��         9              �q     �q     r     3r     Or     gr     r     �r     �r     �r     �r     �r     s     3s     Ks     ds     }s     �s     �s     �s     �s     �s     
t     %t     =t     Ut     lt     �t     �t  "   �t     �t      u     #u  '   Bu  ,   ju  (   �u  $   �u  (   �u  -   v  '   <v  )   dv  $   �v  $   �v  $   �v  $   �v  )   "w     Lw  %   kw  "   �w  %   �w  !   �w  !   �w  $   x     Cx  !   ax  "   �x      �x     �x  #   �x     
y     *y     Hy  "   gy     �y  #   �y  #   �y  #   �y      z     5z     Rz     oz     �z     �z  #   �z     �z  #   	{     -{  #   K{     o{  #   �{     �{     �{     �{     |     $|     D|     d|     �|     �|     �|     �|     �|     }     +}     H}     e}     �}     �}      �}      �}      �}     ~     8~     S~     n~     �~     �~     �~     �~     �~          0     L     e     z     �     �     �     �      �      	�  (   *�  0   S�  .   ��  .   ��  -   �  +   �  +   <�  +   h�  +   ��  +   ��  +   �  +   �  )   D�  *   n�  )   ��  )   Â  )   �  )   �  )   A�  )   k�  )   ��  )   ��  +   �  ,   �  ,   B�  ,   o�  )   ��  )   Ƅ  )   ��  *   �  ,   E�  2   r�  7   ��  :   ݅  4   �  6   M�  -   ��  6   ��  2   �  1   �  1   N�  .   ��  4   ��  @   �  ;   %�  =   a�  >   ��  =   ވ  ;   �  4   X�  ,   ��  /   ��  #   �  #   �  '   2�  '   Z�  '   ��  #   ��  #   Ί  #   �  "   �     9�  #   W�  #   {�  #   ��     Ë  "   ݋  "    �  "   #�  "   F�     i�     ��     ��  "   Ɍ  (   �  (   �  (   >�  (   g�  (   ��     ��     ֍     �  !   �  '   1�  '   Y�  '   ��  '   ��  '   ю  '   ��  &   !�  ,   H�  ,   u�  ,   ��  #   Ϗ  )   �  )   �  )   G�  )   q�  )   ��     Ő     �  $   �  $   '�  #   L�  )   p�  )   ��  )   đ  )   �  )   �  )   B�  )   l�  )   ��     ��     ߒ  #   ��  #   !�  #   E�  #   i�  "   ��     ��     ϓ     �     
�     +�     I�     b�  !   ~�  !   ��          ܔ     ��  &   �  ,   >�  ,   k�     ��     ��  *   ѕ  -   ��  0   *�  +   [�  2   ��  .   ��  6   �  6    �  <   W�  -   ��  ,     6   �  1   &�  .   X�  +   ��  3   ��  %   �  %   
�  %   3�  %   Y�  %   �  %   ��  #   ˙     �  -   �  /   <�  -   l�  $   ��  $   ��  $   �  $   	�  $   .�     S�     r�  $   ��  *   ��  *   ߛ  *   
�  *   5�  *   `�  *   ��  "   ��  "   ٜ  "   ��  "   �  "   B�     e�     ��  %   ��  %   Ɲ  %   �  %   �  %   8�  %   ^�     ��     ��     ��  %   ٞ  %   ��  %   %�  %   K�  %   q�  "   ��  (   ��  (   �     �     )�     G�     b�     }�     ��     ��     Π     �  "   �  "   (�  "   K�  "   n�  "   ��  "   ��     ס  !   �  !   �  !   7�  '   Y�  '   ��  '   ��  '   Ѣ  '   ��  '   !�     I�     c�     y�     ��     ��     ��     У     �      �      �     ;�     W�     s�     ��     ��     Ǥ     �     ��     �     1�     M�     i�     ��     ��     ��     ԥ     �     �      �     @�     `�     ��  !   ��  '   ¦  '   �  '   �  '   :�  '   b�  '   ��  '   ��     ڧ  !   ��  !   �  !   :�  &   \�  &   ��  (   ��  $   Ө  #   ��     �  #   :�  "   ^�      ��  &   ��  #   ɩ  "   �  #   �  #   4�  #   X�  #   |�  #   ��  #   Ī  #   �     �     (�     D�     `�     |�     ��     ��  #   ҫ  #   ��  #   �  #   >�  #   b�     ��  &   ��  #   ɬ  !   �  !   �  !   1�  !   S�  "   u�     ��     ��     έ     �     ��     �     �     ,�     =�     Q�     k�     ��     ��     ��     ��     ˮ     �     �     &�     @�     [�     r�     ��     ��     ��     ί     �     ��     �     0�     L�     k�      ��     ��     ��     ְ     �     	�     )�     F�     c�     ~�     ��     ��     ű     ݱ     ��     
�     %�     =�     U�     m�     ��     ��     ��     ɲ     �     ��     �     %�     <�     R�  !   k�     ��      ��     ȳ     �     ��     �     5�     P�     k�     ��     ��     ��     ״     �     �     *�     G�  '   _�  $   ��  +   ��  9   ص  -   �  (   @�  .   i�     ��  '   ��  0   ̶  0   ��  0   .�  0   _�  0   ��  0   ��     �     �     �     '�     6�     B�  1   V�     ��  ,   ��  '   ո  *   ��  7   (�  #   `�     ��  )   ��  *   ι  ,   ��  /   &�  ,   V�  /   ��  (   ��  -   ܺ  %   
�  2   0�  6   c�  +   ��  ,   ƻ  /   �  .   #�  6   R�  -   ��  ,   ��  )   �  %   �  (   4�  '   ]�  '   ��  $   ��  $   ҽ  #   ��  1   �  '   M�  0   u�     ��     ľ     �      �  !   �  %   @�  ,   f�  #   ��  $   ��  $   ܿ  '   �  #   )�      M�  #   n�  $   ��  %   ��  "   ��  3    �  "   4�  9   W�  #   ��  $   ��  &   ��  "   �      $�  !   E�  $   g�  4   ��  +   ��  %   ��  %   �  "   9�  $   \�     ��     ��  $   ��  2   ��  +   �  /   4�  '   d�  $   ��  #   ��  "   ��     ��      �  "   -�      P�     q�  +   ��     ��      ��  (   ��      $�      E�      f�  #   ��  +   ��  *   ��  )   �  .   ,�  0   [�  8   ��  '   ��  6   ��  1   $�  "   V�  .   y�      ��  #   ��  )   ��     �     6�      V�      w�  !   ��  %   ��     ��     ��      �     <�  (   [�     ��     ��  (   ��      ��  &   �  $   .�  &   S�     z�      ��      ��  !   ��  $   ��  $   "�     G�     c�  $   ��  !   ��     ��  "   ��      �      -�  $   N�  $   s�  '   ��  #   ��  "   ��  %   �  !   -�      O�     p�     ��     ��     ��     ��     ��     �     (�     =�     T�     k�     ��     ��     ��     ��     ��     ��  !   ��  $   !�  %   F�  %   l�  5   ��  *   ��  *   ��  &   �  )   E�  %   o�  ,   ��  &   ��  *   ��  -   �  -   B�     p�  &   ��  '   ��  .   ��  ,   �  ,   8�  %   e�  (   ��  '   ��  '   ��  (   �  (   -�  (   V�     �     ��     ��     ��     ��     �     �     1�     I�     h�     �     ��     ��     ��     ��     ��     
�     �  @   ,�     m�     ��  6   ��     ��     ��     �     �     6�     S�  )   p�     ��     ��     ��     ��  &   �  !   -�  )   O�     y�     ��     ��     ��     ��     ��      �     5�     Q�     q�  !   ��     ��     ��     ��     ��      �     ?�     Y�      w�     ��     ��  &   ��      ��     
�  #   #�     G�  !   b�  !   ��  "   ��  (   ��     ��      �  #   2�     V�     t�      ��      ��  #   ��  !   ��     �  &   6�     ]�     z�     ��     ��     ��     ��      �     �     6�     P�  '   j�  &   ��  &   ��  #   ��     �     $�  #   D�  #   h�  #   ��  #   ��  $   ��  +   ��  '   %�  $   M�  $   r�  $   ��  &   ��  0   ��  5   �  .   J�  *   y�  -   ��  $   ��  $   ��  #   �  ,   @�  (   m�     ��  %   ��  )   ��  *   �  *   /�     Z�  *   z�     ��  !   ��     ��  +   �  )   /�  ,   Y�  0   ��  (   ��  $   ��  "   �  %   (�  ,   N�  '   {�  +   ��  (   ��  &   ��  +   �  &   K�  &   r�     ��  '   ��  %   ��  $   �  $   *�  (   O�  "   x�     ��  )   ��  )   ��  )   
�  !   7�     Y�  +   u�  *   ��  -   ��  &   ��  "   !�  $   D�     i�     ��     ��     ��     ��      ��  !   �  !   =�  !   _�  !   ��  ,   ��  ,   ��  "   ��       �  !   A�  #   c�     ��     ��     ��  $   ��  $   �  $   (�  "   M�  "   p�  "   ��  %   ��  %   ��  %   �  "   (�     K�     k�  "   ��  "   ��  "   ��  )   ��  &   �  &   E�  )   l�  &   ��  &   ��  )   ��  %   �  '   4�  #   \�  #   ��  &   ��  &   ��  #   ��  0   �  (   G�  (   p�  -   ��     ��  $   ��  $    �  $   %�      J�  "   k�  "   ��      ��     ��  #   ��     �  '   4�  '   \�  %   ��  '   ��  (   ��  )   ��  &   %�  0   L�  %   }�  !   ��  &   ��  &   ��  $   �  "   8�  "   [�  "   ~�  (   ��  !   ��  *   ��  -   �  "   E�  &   h�      ��  +   ��  )   ��  &   �  "   -�  !   P�  *   r�  #   ��  (   ��  %   ��     �  )   0�  (   Z�  #   ��  !   ��     ��  '   ��  &   �     8�  (   S�  "   |�  #   ��  )   ��     ��     
�  -   *�  *   X�     ��      ��      ��  *   ��  0   �  $   @�  *   e�  0   ��  1   ��  @   ��  3   4�  3   h�  3   ��  3   ��  3   �  3   8�  ,   l�  <   ��  5   ��  3   �  3   @�  3   t�  3   ��  3   ��  ;   �  ;   L�  6   ��  ;   ��  8   ��  ,   4�  #   a�     ��     ��     ��     ��  #   ��        "   >     a     |     �  #   �     �     �         %    >    W    j #   { )   �    �    �    �        .    H    c    }    �    � %   � +   � *   " +   M *   y +   � *   � +   � )   '    Q    o &   �    � #   �    � #   
    1    H    ^ #   t #   �    � +   � 4   � "   3 *   V    �    �    �    �     �     )    )   B "   l )   � "   � %   � %    )   ( #   R    v #   � (   � )   � /   
	 "   =	 &   `	 "   �	 (   �	 %   �	    �	 #   
 3   =
 "   q
 !   �
    �
 "   �
 #   �
 '    )   E %   o &   � 2   � 2   � 2   " 3   U 2   � 2   � 3   � #   #
 #   G
    k
     �
 %   �
 $   �
 %   �
 ,    /   G    w     � 0   � 2   � )    #   F %   j $   �    � %   � "   �      %   ? "   e "   � %   � &   � $   � !       ?    W    q $   �     � *   �            7    W    o    �    �    �    �    � %    $   - %   R $   x %   � $   � %   �        -    H    g    �    �    �    �    �    � &        @    a &   ~    �    � "   � "       $ &   @ (   g (   � (   � (   �         # %   D &   j     � .   � )   � -    -   9 -   g -   � +   � ,   � (       E !   e )   � "   �    � !   �     "   *    M     i    �    � #   �    �         !   : "   \ *    '   � #   � (   � #       C    ]    u    � -   � +   �        $    @    \    x    � "   � -   � )    *   +    V "   q $   � &   � '   � $    "   - '   P "   x "   � !   �    � $      $   %  "   J  '   m     �  +   �  *   �  $   ! (   1! %   Z! (   �! (   �! '   �! &   �! &   !" )   H"     r"    �"    �"    �"    �" &    #    '#     C# !   d#    �# )   �# $   �#    �#    
$    !$    5$    N$ "   m$ "   �$     �$    �$    �$     % .   (% *   W%    �% "   �% !   �%    �%    �%    &    1&    K&    a&    w&    �&    �&    �&    �&    �&    �&    �&    '    +'    D'    ]'    u'    �'    �'    �'    �'    �'    �'    (    .(    C(    [(    o(    �(    �(    �(    �(    �( !    )    ")    >)    Y)    r)    �)    �)    �)    �)    �)    *    **    B*    V*    j*    ~*    �*    �*    �*    �*    �*    �* .   + &   7+ &   ^+ !   �+    �+    �+    �+    �+    �+    ,    -,    B,    W,    l,    �,    �,    �,    �,    �,    �,    -    -    1-    O-    o-    �-    �-    �-    �-    �-    .    #.     B.    c.    .    �.    �.    �.    �.     �.    
/    +/    H/    e/ $   �/    �/    �/    �/    �/    0    .0    G0    ]0    s0 !   �0    �0    �0    �0    
1    *1    D1    Z1    p1    �1    �1    �1    �1    �1    �1 &   2 #   ,2    P2    m2    �2    �2    �2    �2    3    3    &3    <3    X3    t3    �3    �3    �3    �3    �3    �3    4    4    *4    B4 $   b4     �4 "   �4 !   �4 "   �4    5 $   /5    T5    q5    �5    �5    �5    �5    6     "6    C6 $   c6 '   �6 !   �6 #   �6     �6     7 &   87 !   _7 '   �7    �7 "   �7 *   �7 %   8 %   :8 (   `8 9   �8 2   �8 +   �8 &   "9 &   I9    p9 "   �9 (   �9    �9    �9    :     2: )   S: -   }:    �:     �:    �: )   ;    2; &   Q; !   x; (   �; $   �;    �; $   < $   *< %   O< #   u< &   �<     �< )   �<    =    (= #   F=    j=    �= "   �= "   �= (   �=    >    1> %   O> '   u> -   �>    �> &   �>    ?    +? $   J?    o?    �?    �? $   �?    �?    �?    @    5@    P@    c@    z@    �@    �@     �@    �@    A     A    @A    ZA !   yA    �A    �A    �A    �A    B    B !   4B !   VB !   xB    �B    �B    �B #   �B    C    7C    VC %   nC "   �C !   �C    �C    �C    D    /D    MD    lD    �D    �D    �D    �D    �D    E    -E    AE    UE    sE    �E %   �E    �E    �E    
F #   *F    NF    jF    �F    �F    �F    �F    �F    
G    !G    9G    RG    iG    �G    �G    �G    �G    �G    �G    H    =H    \H    {H    �H    �H    �H    �H    I     I    <I    XI    tI    �I "   �I "   �I $   �I $   J &   <J &   cJ &   �J $   �J "   �J "   �J $   K "   AK    dK $   �K .   �K .   �K    L    L "   ;L "   ^L "   �L "   �L "   �L    �L "   �L      M    AM    aM '   |M    �M    �M    �M    �M    N    $N $   <N -   aN &   �N    �N    �N    �N    O    !O    ;O /   [O -   �O    �O    �O !   �O ,   P -   =P '   kP (   �P (   �P %   �P    Q    %Q    >Q    XQ    tQ    �Q    �Q    �Q    �Q    �Q    R    R    )R    =R    OR    cR    }R    �R    �R    �R !   �R )   �R )   &S    PS !   lS "   �S "   �S     �S !   �S #   T #   ;T #   _T #   �T )   �T "   �T )   �T -   U 1   LU "   ~U "   �U $   �U !   �U "   V )   .V "   XV    {V "   �V !   �V    �V    �V    W    5W    PW    aW &   }W    �W (   �W -   �W    X !   0X     RX !   sX !   �X !   �X $   �X    �X &   Y !   @Y &   bY '   �Y &   �Y    �Y !   �Y %   Z    9Z     UZ *   vZ $   �Z "   �Z #   �Z #   
[    1[ %   O[    u[    �[     �[    �[    �[    �[    \    )\    C\     ^\    \ '   �\    �\    �\    �\    ]    ]    7]    U]    s]    �] *   �] (   �] (   ^ (   ,^ (   U^ (   ~^ (   �^ (   �^ '   �^ &   !_ %   H_ %   n_ 8   �_ -   �_ (   �_ #   $` &   H` !   o` "   �` 2   �` 4   �` 4   a )   Qa ?   {a 7   �a 1   �a +   %b &   Qb +   xb &   �b    �b �  �b �  sd \  g -   kh   �h <  �i �  �k p  �m T  Go I   �p j   �p G   Qq e   �q �  �q �  �s �   u �  �u   �w �  �x   �z �  �|   �~ �  � �  �� 9   B� �   |� H   ;� &  ��    �� 6  ˆ G   � #   J�    n� )   �� J   ��    ��    � -   )�    W� $   w�    �� 9   �� G  � ;  1� m   m� 7   ۍ 
   � 
   �    )� 
   @�    K�    b� 	   o�    y�    ��    ��    ��    �� 	   Ɏ 
   ӎ    �    �    �    %� R  @� s  �� |   � -   �� ^  �� �   �   ԕ �   �    �� �   ڗ    s� �   �� #   3� 
  W�    e� �  �� (   � _   <�    �� T  �� F   � �  G� X  �    `� Q   q� _   â �   #� 0   � ,   �    B� 2   ¤ G   �� 1   =�    o� w   s� �  � F   �� d   �� �   \� C   � &  X� 9   �   �� �   ӫ �   c� L   
� �   W� �   !� G   ��   � �   � p   ְ F   G� m   ��    �� �   � =  �� G   �� 
   <� �   G� �   �    �� �  �� �   ��   8� �  N� �   � Q  �   6� E   U� K   �� _   �� u   G� `   �� c   � 9   �� �   �� ]   U� A   ��    �� x   
� Z   �� �   �� �   ��    D�   ]�   s� K  � -  �� �   �� �   �� 6  [� �   �� �   O� �   �� x  �� �   �   ��    �� �   �� �   Z� �   %� q  �� �   T� �   �� �   w� �   �    �� 2  |�   �� #   �� e   �� �   N� Z   �� k  =� �   �� ]   ^� t   ��    1� =  O� �   �� �  l�    �� l   � �   s� �   �   �� �   �� �  �� �  �� +   m� a  �� �  �� 8  �� �  �� :  ��   �� Q   � �   ^�    �� �   � I  �� x  � 
  ��   �� �   �� 	   ]  �  g  ]  �    X    Y �   a G  	 �   Q   9 �   X	 [   2
 
   �
 y  �
 Q      h
   y
 '  ~ F  � �   � i  � q   E [   � �    5       D E  Y =  � [  � (   9 G   b    � �   � s   E f   � �    !   �    � �  � �   �! �   D" A   �" �   # &   �#   �# 
  �$    �% 	   �% !   & 5   (& .   ^& 4   �& L   �& e   '    u' +   �' >   �' B   �'     6( 9   W( /   �( p   �(    2)    9)   S) F  c* �   �+ �   �, �   �- �   . j  �.    _0 �   u0 �   \1 �   2 Z  �2 y  4 �  �5 �  �7 �  ]9 G   
; 0   R; (   �; �   �;   J< x  c> D  �? [  !A 5  }B <  �C   �E ?   G   NG 8  mH i  �I    K �   !K U  L t   ZM G  �M 
  O    "P &   3P n   ZP    �P �  �P �  zS �  �T �   �W �   SX    Y   &Y )  =Z G   g[ n   �[ #  \    B] ~   C^ �  �^ [   M`   �`    �a (  �a �   �c #  �d �  �f �  Pi �   6k 
   l   l �  'm 
   �n �  �n �  mp J  ]r .  �t $  �v P  �w G   My �  �y �   s{ w   _| =   �| W   }    m} 8   r} $   �} *   �}    �}    ~ �   2~   �~ .  � Y  � �   l�    M� �  V� V  F� �  �� �  "� �  ɏ �  U� 
   �   ��     �    � �  � E  ܗ 
  "� �  0� 
   �� %  Ǟ   � �  �� R  �� �  I� �  3� �  Ƭ    S� �   r� �   H�   ۯ �  � �   ϴ �   �� �   � u  �    W� �   n� O   %� Q   u�    ǹ <   ڹ B   � m  Z� �  ȼ �  {� 0   `� �  �� �   t�    -� �   3� =   �� �   '� �   �� -   �� p   ��    #� s  6� �  ��   O� a  T� m   ��    $� ~   9� !  ��    �� �   �� �  z� �   +� 8   �� <   %� [  b� -   �� �  �� �  u� 	  � %   "� x   H� �   �� l  V� �  ��    �� �   �� )   �� �   �� �   }�   G� �   T� D   .� -   s� 
  �� *   �� �  �� \  �� �   � +  �� [   � 8   k� �   �� >  e� q  �� �  � �  � >   � �   D� 9  � B  R� �   �� �  n� �  _� -      M .   T �  � `   A    � n  � #  $ �  H
 �   � B  � #   U  @ �   � �  I �  + v   � R  g :  � E   � �   ; u   � $   <    a A  f �   � 5   [  3  �  �  �!   �# ,  �$ �  �& �   �( �  }) I  2+    |, �   �,    >- �  G-    �.    /    $/ 
   */ #   8/    \/    n/    ~/    �/    �/ 
   �/ 
   �/ 
   �/    �/ !   �/ 	   0 /   0    B0    H0 
   X0    c0 �   l0 V   W1 &   �1    �1 4   �1 %   2 0   =2 >   n2 &   �2 
   �2 
   �2    �2    �2    3    %3    43 
   <3 	   G3    Q3 ,   b3    �3     �3 (   �3 .   �3 (   #4 3   L4    �4 -   �4 .   �4    �4 W   5 7   ^5 	   �5 J   �5 ;   �5    '6    A6     S6    t6    �6    �6    �6    �6    �6    �6    �6    �6    7 	   7 
   7    %7    (7    G7    N7 	   W7    a7 	   }7    �7    �7    �7 	   �7    �7    �7    �7    �7    �7    8    38    ;8 	   K8    U8 %   d8    �8 	   �8    �8    �8    �8    �8    �8    �8    �8    9 n   9 �   �9 \   $: �   �: �  /; v   &= 
   �=    �=    �=    �= 
   �=    �=    �= #   > f   2> >   �> e   �>    >? ?   \? %   �? r   �? [   5@ �   �@ ^   ~A D   �A    "B    BB    ZB    `B    iB 
   rB    �B    �B    �B    �B    �B    �B    �B    �B    �B    �B     C    C    "C    .C    3C    ?C    GC    TC    kC 4  C    �D    �D    �D &   �D    �D >   �D l   =E )   �E -   �E 9   F    <F    DF    LF x   OF    �F 
   �F *   �F    G    �G 
   �G p   �G "   H _  3H V   �K �   �K �   wL �   M 7   �M �   7N �   0O <  �O �   �P    �Q 
   �Q J   R H   SR `   �R a   �R c   _S @   �S m   T    rT -   �T    �T    �T N   �T %   )U    OU    UU 	   kU    uU Z   �U    �U +   �U G   &V    nV    �V O   �V k   �V w  ZW    �X    �X �   �X 
   �Y    �Y    �Y    �Y 	   �Y )   �Y U  Z    a[    �[    �[    �[ ?  �[ '   �\    ]    ] V   ]    r]    v] *   |]    �]    �] %   �] �   �]    p^    �^ Z   �^    �^    _    '_    -_ 8   E_ P   ~_    �_ 8   �_ �   ` g   �` K   5a    �a �   �a B   vb    �b 3   �b    c �   c �   �c    Rd L   id �   �d �   Ne    f '   $f    Lf �  \f     �g    h    #h    ;h    Uh �   uh    i    %i '   @i @   hi    �i    �i    �i "   �i o  j ]  ul @  �m 8   p 8   Mp    �p "   �p   �p �   �q �   �r    ms �   �s �  qt    �u    v   *v 8  Ix    �y    �y <   �y    �y D  �y    #{ N  8{ �  �| �   ~    �~ l   
 .   z    � s   � 8  9� 0  r� �   �� �  B� l   Є �   =� S   $� �   x� �   � T   � �   7� #   �    �    5�    H�    Q�    f�    �� =   �� &   ډ 	   � $   � L  0� &   }� �   �� �   u� }   ��    x�    ��    ��    ͍ !   ��    � #   "�    F� �   f� %   R� �   x�    C� �   Z� v   '� h   �� &  � �   .� '   �� �   "� 	   ǔ A  є w   �    �� �  ��    \�    i�    �    �� *   ��    ݘ    � U   � �   ?�   
�    �    '�    .� �   D� @  �� �   >�    �    ��    ��    ��    ��    ՞    �    �� F   � +   H�   t� e   x�    ޡ p   � s   c� @   ע t   � R   �� D   �    %� X   *� :   �� �   �� Q   L� >   ��    ݥ �   �� g   ڦ I   B� c   �� M   � A   >�    �� 2   �� v   �� �   3� 7   ϩ �   �    Ū -  ̪ Z   �� T   U� �   ��    <� 	  E� �   O�    =�    T�    l�    x� &  }� -   �� g   ұ �   :� �   � �   �� �   �� �   o� �   � �   ¶ Y   |� -  ַ j   � �  o� �   �� �   �� 
   �� 
   �� 
   ¼ �   м 
   i� 
   w� L   �� Y   ҽ �   ,� 
   � m   � =   �� �   ƿ 4   J� z   � [   �� 
   V� �   d� 
   �� 
   �� 
   � $  � �   ?� �  ��    v�    �� 
   �� �   ��    C� #   [� �   � �   v�    �    1�    A� )   Y� 0   �� .   ��    ��    � �   #� !   � �  ?�   �� �   �� P   � �   ��   g� �  }� $  n� �   �� �   *� u   ��    2� �  H�    �� q  �� �   k� ,  B� �   o� B  �� �   :� �   �� x   �� &   � H   7�    �� '   �� 
   ��    ��    �� �   ��    �� k   �� /   Q�    ��    ��    ��    �� l   �� K   #� 7   o� �   �� 3   ��    ��    ��    �� $   �    '�    9�    B�    J�    S�    [�    d�    l�    u� �   ��    0�    A� 
   P�    ^� 
   m�    {� 
   ��    ��    ��    �� &   �� e   ��    \� 3   h� h   �� �   � �   �� �   j� [  >� �   �� ,  P� 	   }� �   �� 6   � C   O� P   �� �   �� �   �� ;   r� d   ��    � ~   %� q   �� �   �    ��     ��    ��    ��    �    �    ,�    4�    T�    ]�    n�    ��    ��    ��    ��    ��    ��    ��    
� *   �    =� )   D�     n� �   �� �   � '   �� &   �� �   �� \   � r   �� '   O� )   w� .   �� :   �� <   � '   H� �   p�   E� )  J�    t� F   �� �   �� (   �� �   �� �   �� �   z� W   
�    e� �   �� �   4� l   �� 5   G  �   }  �       � *   �    ! I   < �   � �       �    �    �    � �   � 7   ` $   � 5   � "   �        4 @   T    � d   � 8    �   P O   C l   � �     >   � "   � !   � "   	 !   A	 "   c	 !   �	 "   �	 ;   �	 1   
 s  9
 9   � 9   � ;   !
 %   ]
    �
 y   �
 v    �   z �   3    � �   � +   �    � �   �    � &   � 8   � 0    U   H X   � G   � !   ?    a �   � 4   ,     a T   � �   � M   � �    |   � ]   ' �   �     +   ' |   S    � [   �    A �   ] ;    R   Y    �    � �   � O   � G    �   ] p   � E   Y �   � �   .    �    � \   � 4   *    _ "   s    �    �    � -   � �   � k   �     �      ! ,   %!    R! #   q! �   �! 9   " u   P" S   �"    # g   8# �   �# 2   �$ T   �$ 
   % K   )% >   u% 
   �% �   �% w   V& /   �& 7   �& 
   6' -   A' T   o' 
   �' u   �' %   H(    n( !   �( P   �( �   �( @   �)    �)    * @   +*    l* W   �* A   �* �   + �   �+    \, 5   n, �   �,    g- �   �- H   1.     z. S   �. �   �.    �/    �/    �/    �/    �/ :   0 I   P0 !   �0    �0 	   �0     �0 8   �0 7   31 	   k1 @   u1 E   �1    �1 '   2 E   42    z2    �2 [   �2   3 R   %4    x4    �4 �   �4 9  L5 W   �6    �6 �  �6 �  �8 0   +: u   \:    �: �  �: ,   z= d   �=    >    +> �  I>    B@ S   b@ m   �@ h   $A j   �A *   �A d   #B 5   �B    �B q   �B ?   MC /   �C U   �C 8   D K   LD �   �D �   'E -   �E �   �E o  �F �   H ?   �H u  I   �J �   �K .  �L �   �M /   �N    �N �   �N ,   \O   �O j   �Q N   �Q    KR    \R �  zR    2T �   6T <  �T &  <V 0  cW J   �X J   �X d   *Y 2   �Y 7   �Y ^   �Y Z   YZ    �Z '   �Z 7   �Z ,   )[    V[ .   i[ X   �[ 0   �[    "\ k   (\    �\ �   �]    ?^    T^    i^ E   n^ a   �^ U   _ �   l_ n   6`    �` (   �` �   �`    �a �   �a �  �b   Te P   Zf -   �f    �f    �f    �f E   �f /   .g �   ^g   �g h   �h    Pi    di &   wi    �i W   �i    j D   j    dj 2   kj '   �j    �j    �j [   �j    :k    Bk &   `k    �k    �k w   �k �   #l f   �l j   Qm W   �m �   n    �n    �n �   o �   �o �   �p    <q n   Eq R   �q B   r o   Jr t   �r S   /s    �s O   �s    �s    �s    t    "t    7t    Lt    `t    qt    �t 
   �t '   �t -   �t '   �t    u -   9u &   gu     �u    �u    �u @   �u    !v    Av 6   Iv (   �v _   �v -   	w    7w    Nw    ]w    ww    �w |   �w ^    x x   x �   �x    �y    z    z    ;z C   Uz 	   �z    �z    �z e   �z    3{    O{    o{ 
   v{ 	   �{ H   �{    �{   �{    }    }    7} $   M}    r}    �}    �}    �}    �} *    ~ *   +~    V~ W   ]~ '   �~    �~    �~ 1   
 S   <    � 5   �    � l   � h   r� Q   ۀ    -� &   5� 	   \�    f�    w�    �� �  �� �   B�    Ń    ԃ $   ؃ 
   �� '   �    3� 
   8� z   C� =   �� �   ��    ��     ҅ �   �     ��    ��    Æ "   � ,   � "   3�    V� 
   p� <   ~�    �� .   ԇ 9   � 8  =� H   v� S   �� E   �    Y� �   s� #   �    4� \   A�    �� "   ��    ͋ 7   � H   �    b� �   ��    ,�    =�    L�    l�    �    ��    ��    č    ؍ F   �   5� &   <� #   c� �   �� �   W�    �    �    ;�    N�    ^�    u�    ��    ��    ��    ȑ    ۑ    � 
   ��    	�    �    *�    7�    C� &   S� �   z� 1  S� �  ��    � �  ?� �   � y  Ԛ    N�   ]�    `�   t� �   �� �  h� �   � i  ա 6   ?� �   v� E   �    _� H   z� O   ä j   � f   ~� �   � �   �� �   :� �  �    �� �   �� �   |� i   p� �   ڬ    ~� �   �� h  i� �   ү �   �� 	   o� [   y� ^   ձ �   4� �   /� j   Գ �   ?�    ˴ #   ݴ    � @   � 1   U�    �� {   �� A   � y   R� &   ̶ �   � �   v� I   	� o   S� R   ø b   � T   y� W   ι �   &� ^   ��    � |   ��    � 2   � '   E� V   m� 8   ļ    ��    � G   �    ]�    p�    �� 9   �� '   ǽ 9   �    )�    @� 
   M�    X� 	   ^� Q   h� r   �� R   -� 7   ��    ��    �� "   ۿ    ��    �    � 	   �    )�    /�    7� 
   >�    I�    P�    _�    h�    w�    �    ��    ��    ��    ��    ��    �� "   ��    ��    � �   -�    �� T   �� �    � 
   ��    ��    ��    �� 
   �    � 
   � �   � �   �� D   K�    ��    �� c   ��    � �   +� �   �� '   G�    o� �   �� �   � L   �� �   4� u   � 8   x� �   ��    I�    `� H   x� �   ��    o� �   �� w   9� �   �� Y   ~�    ��    ��    �� 
   ��    
�    �    "�    .� �   F� n   �� {   W� �   �� �   �� b   �� W   � �  i� t  b�   �� �   �� %  �� \  ��    	� �   � R  �� �   %� �  ��   �� {   �� X  \�    �� H   �� �   � f  �� B   %� �   h� 
   g�    r�    x� �   �� =   "� q   `� *   �� \   �� Q   Z� z   �� &   '� �   N� <   �� -   9� T   g� �   ��    ��  blog.3x-new-books.q1.text1 blog.3x-new-books.q1.text2 blog.3x-new-books.signature blog.3x-new-books.text1 blog.3x-new-books.text2 blog.3x-new-books.text3 blog.3x-new-books.text4 blog.3x-new-books.text5 blog.3x-new-books.title blog.ai-copyright.postscript blog.ai-copyright.signature blog.ai-copyright.subtitle blog.ai-copyright.text1 blog.ai-copyright.text10 blog.ai-copyright.text11 blog.ai-copyright.text2 blog.ai-copyright.text3 blog.ai-copyright.text4 blog.ai-copyright.text5 blog.ai-copyright.text6 blog.ai-copyright.text7 blog.ai-copyright.text8 blog.ai-copyright.text9 blog.ai-copyright.title blog.ai-copyright.tldr blog.all-isbns-winner.text5 blog.all-isbns-winner.text6 blog.all-isbns-winners.first blog.all-isbns-winners.first.text1 blog.all-isbns-winners.footer blog.all-isbns-winners.gratitude blog.all-isbns-winners.notable blog.all-isbns-winners.notable.BWV_1011 blog.all-isbns-winners.notable.backrndsource blog.all-isbns-winners.notable.immartian blog.all-isbns-winners.notable.j1618 blog.all-isbns-winners.notable.joe.davis blog.all-isbns-winners.notable.orangereporter blog.all-isbns-winners.notable.reguster blog.all-isbns-winners.notable.robingchan blog.all-isbns-winners.notable.text1 blog.all-isbns-winners.notable.text2 blog.all-isbns-winners.notable.text3 blog.all-isbns-winners.notable.text4 blog.all-isbns-winners.notable.timharding blog.all-isbns-winners.opt.all blog.all-isbns-winners.opt.cadal_ssno blog.all-isbns-winners.opt.cerlalc blog.all-isbns-winners.opt.duxiu_ssid blog.all-isbns-winners.opt.edsebk blog.all-isbns-winners.opt.gbooks blog.all-isbns-winners.opt.goodreads blog.all-isbns-winners.opt.ia blog.all-isbns-winners.opt.isbndb blog.all-isbns-winners.opt.isbngrp blog.all-isbns-winners.opt.libby blog.all-isbns-winners.opt.md5 blog.all-isbns-winners.opt.nexusstc blog.all-isbns-winners.opt.oclc blog.all-isbns-winners.opt.ol blog.all-isbns-winners.opt.rgb blog.all-isbns-winners.opt.trantor blog.all-isbns-winners.second blog.all-isbns-winners.second.quote blog.all-isbns-winners.second.text1 blog.all-isbns-winners.second.text2 blog.all-isbns-winners.signature blog.all-isbns-winners.text1 blog.all-isbns-winners.text2 blog.all-isbns-winners.text3 blog.all-isbns-winners.text4 blog.all-isbns-winners.third1 blog.all-isbns-winners.third1.text1 blog.all-isbns-winners.third2 blog.all-isbns-winners.third2.text1 blog.all-isbns-winners.third3 blog.all-isbns-winners.third3.text1 blog.all-isbns-winners.third4 blog.all-isbns-winners.third4.text1 blog.all-isbns-winners.title blog.all-isbns-winners.tldr blog.all-isbns.background blog.all-isbns.background.text1 blog.all-isbns.background.text2 blog.all-isbns.background.text3 blog.all-isbns.background.text4 blog.all-isbns.bounty blog.all-isbns.bounty.bonus1 blog.all-isbns.bounty.bonus2 blog.all-isbns.bounty.bonus3 blog.all-isbns.bounty.bonus4 blog.all-isbns.bounty.bonus5 blog.all-isbns.bounty.bonus6 blog.all-isbns.bounty.bonus7 blog.all-isbns.bounty.bonus8 blog.all-isbns.bounty.bonus9 blog.all-isbns.bounty.code blog.all-isbns.bounty.code.text1 blog.all-isbns.bounty.code.text2 blog.all-isbns.bounty.code.text3 blog.all-isbns.bounty.req1 blog.all-isbns.bounty.req2 blog.all-isbns.bounty.req3 blog.all-isbns.bounty.req4 blog.all-isbns.bounty.req5 blog.all-isbns.bounty.text1 blog.all-isbns.bounty.text2 blog.all-isbns.bounty.text3 blog.all-isbns.bounty.text4 blog.all-isbns.bounty.text5 blog.all-isbns.bounty.text6 blog.all-isbns.signature blog.all-isbns.text1 blog.all-isbns.text2 blog.all-isbns.text3 blog.all-isbns.title blog.all-isbns.tldr blog.all-isbns.visualizing blog.all-isbns.visualizing.text1 blog.all-isbns.visualizing.text2 blog.annas-archive-containers.conclusion blog.annas-archive-containers.conclusion.heading blog.annas-archive-containers.conclusion.text1 blog.annas-archive-containers.conclusion.text2 blog.annas-archive-containers.example.heading blog.annas-archive-containers.example.text1 blog.annas-archive-containers.example.text2 blog.annas-archive-containers.example.text3 blog.annas-archive-containers.example.text4 blog.annas-archive-containers.example.text5 blog.annas-archive-containers.example.text6 blog.annas-archive-containers.example.text7 blog.annas-archive-containers.goals.goal1 blog.annas-archive-containers.goals.goal10 blog.annas-archive-containers.goals.goal2 blog.annas-archive-containers.goals.goal3 blog.annas-archive-containers.goals.goal4 blog.annas-archive-containers.goals.goal5 blog.annas-archive-containers.goals.goal6 blog.annas-archive-containers.goals.goal7 blog.annas-archive-containers.goals.goal8 blog.annas-archive-containers.goals.goal9 blog.annas-archive-containers.goals.heading blog.annas-archive-containers.goals.nongoal1 blog.annas-archive-containers.goals.nongoal2 blog.annas-archive-containers.goals.nongoal3 blog.annas-archive-containers.goals.text1 blog.annas-archive-containers.goals.text2 blog.annas-archive-containers.goals.text3 blog.annas-archive-containers.standard.aac blog.annas-archive-containers.standard.aacid blog.annas-archive-containers.standard.aacid-range blog.annas-archive-containers.standard.aacid.collection blog.annas-archive-containers.standard.aacid.collection-id blog.annas-archive-containers.standard.aacid.iso8601 blog.annas-archive-containers.standard.aacid.shortuuid blog.annas-archive-containers.standard.binary blog.annas-archive-containers.standard.binary.contents blog.annas-archive-containers.standard.binary.name blog.annas-archive-containers.standard.binary.tip blog.annas-archive-containers.standard.collection blog.annas-archive-containers.standard.heading blog.annas-archive-containers.standard.metadata-file blog.annas-archive-containers.standard.metadata-file.data_folder blog.annas-archive-containers.standard.metadata-file.fields blog.annas-archive-containers.standard.metadata-file.filename blog.annas-archive-containers.standard.metadata-file.jsonlines blog.annas-archive-containers.standard.metadata-file.metadata blog.annas-archive-containers.standard.metadata-file.prefix blog.annas-archive-containers.standard.records-files blog.annas-archive-containers.standard.text1 blog.annas-archive-containers.standard.torrents blog.annas-archive-containers.text1 blog.annas-archive-containers.text2 blog.annas-archive-containers.text2.li1 blog.annas-archive-containers.text2.li2 blog.annas-archive-containers.text2.li3 blog.annas-archive-containers.text3 blog.annas-archive-containers.text4 blog.annas-archive-containers.title blog.annas-archive-containers.tldr blog.annas-update-2022.covers blog.annas-update-2022.covers.text1 blog.annas-update-2022.covers.text2 blog.annas-update-2022.covers.text3 blog.annas-update-2022.es blog.annas-update-2022.es.problem1 blog.annas-update-2022.es.problem2 blog.annas-update-2022.es.problem3 blog.annas-update-2022.es.problem4 blog.annas-update-2022.es.text1 blog.annas-update-2022.es.text2 blog.annas-update-2022.es.text3 blog.annas-update-2022.open-source blog.annas-update-2022.open-source.text1 blog.annas-update-2022.open-source.text2 blog.annas-update-2022.open-source.text3 blog.annas-update-2022.open-source.text4 blog.annas-update-2022.open-source.text5 blog.annas-update-2022.text1 blog.annas-update-2022.title blog.annas-update-2022.tldr blog.backed-up-libgen-li.analysis blog.backed-up-libgen-li.analysis.item1 blog.backed-up-libgen-li.analysis.item2 blog.backed-up-libgen-li.analysis.item3 blog.backed-up-libgen-li.analysis.item4 blog.backed-up-libgen-li.analysis.text1 blog.backed-up-libgen-li.analysis.text2 blog.backed-up-libgen-li.collaboration blog.backed-up-libgen-li.collaboration.text1 blog.backed-up-libgen-li.collaboration.text2 blog.backed-up-libgen-li.collaboration.text3 blog.backed-up-libgen-li.collection blog.backed-up-libgen-li.collection.text1 blog.backed-up-libgen-li.collection.text2 blog.backed-up-libgen-li.collection.text3 blog.backed-up-libgen-li.collection.text4 blog.backed-up-libgen-li.collection.text5 blog.backed-up-libgen-li.fig1 blog.backed-up-libgen-li.forks blog.backed-up-libgen-li.forks.text1 blog.backed-up-libgen-li.forks.text2 blog.backed-up-libgen-li.fundraiser blog.backed-up-libgen-li.fundraiser.text1 blog.backed-up-libgen-li.fundraiser.text2 blog.backed-up-libgen-li.fundraiser.text3 blog.backed-up-libgen-li.fundraiser.text4 blog.backed-up-libgen-li.fundraiser.text5 blog.backed-up-libgen-li.fundraiser.text6 blog.backed-up-libgen-li.fundraiser.text7 blog.backed-up-libgen-li.fundraiser.text8 blog.backed-up-libgen-li.links blog.backed-up-libgen-li.next blog.backed-up-libgen-li.next.text1 blog.backed-up-libgen-li.next.text2 blog.backed-up-libgen-li.next.text3 blog.backed-up-libgen-li.next.text4 blog.backed-up-libgen-li.signature blog.backed-up-libgen-li.text1 blog.backed-up-libgen-li.text2 blog.backed-up-libgen-li.title blog.backed-up-libgen-li.tldr blog.books-on-ipfs.deprecated blog.books-on-ipfs.title blog.critical-window.copies blog.critical-window.copies.text1 blog.critical-window.copies.text2 blog.critical-window.fig1 blog.critical-window.hdd-prices blog.critical-window.links blog.critical-window.low-hanging-fruit blog.critical-window.low-hanging-fruit.text1 blog.critical-window.low-hanging-fruit.text2 blog.critical-window.ocr blog.critical-window.priorities blog.critical-window.priorities.order.code blog.critical-window.priorities.order.fiction blog.critical-window.priorities.order.geographic blog.critical-window.priorities.order.leaks blog.critical-window.priorities.order.measurements blog.critical-window.priorities.order.metadata blog.critical-window.priorities.order.nonfiction-books blog.critical-window.priorities.order.nonfiction-other blog.critical-window.priorities.order.nonfiction-transcripts blog.critical-window.priorities.order.organic blog.critical-window.priorities.order.papers blog.critical-window.priorities.order.science-websites blog.critical-window.priorities.order.transcripts blog.critical-window.priorities.rarity.at-risk blog.critical-window.priorities.rarity.rare blog.critical-window.priorities.rarity.underfocused blog.critical-window.priorities.text1 blog.critical-window.priorities.text2 blog.critical-window.priorities.text3 blog.critical-window.priorities.text4 blog.critical-window.priorities.text5 blog.critical-window.priorities.text6 blog.critical-window.quote.the-lost blog.critical-window.shadowlib blog.critical-window.shadowlib.example.github blog.critical-window.shadowlib.example.metadata blog.critical-window.shadowlib.example.reddit blog.critical-window.shadowlib.text1 blog.critical-window.shadowlib.text2 blog.critical-window.shadowlib.text3 blog.critical-window.shadowlib.text4 blog.critical-window.shadowlib.text5 blog.critical-window.signature blog.critical-window.storage blog.critical-window.storage.density blog.critical-window.storage.density.text1 blog.critical-window.storage.density.text2 blog.critical-window.storage.density.text3 blog.critical-window.storage.density.text4 blog.critical-window.storage.density.text5 blog.critical-window.storage.density.text6 blog.critical-window.storage.text1 blog.critical-window.storage.text2 blog.critical-window.storage.text3 blog.critical-window.storage.text4 blog.critical-window.storage.text5 blog.critical-window.text1 blog.critical-window.the-window blog.critical-window.the-window.text1 blog.critical-window.the-window.text2 blog.critical-window.the-window.text3 blog.critical-window.the-window.text4 blog.critical-window.the-window.text5 blog.critical-window.the-window.text6 blog.critical-window.title blog.critical-window.tldr blog.duxiu-exclusive.collection blog.duxiu-exclusive.collection.text1 blog.duxiu-exclusive.collection.text2 blog.duxiu-exclusive.collection.text3 blog.duxiu-exclusive.collection.text4 blog.duxiu-exclusive.collection.text5 blog.duxiu-exclusive.example_pages blog.duxiu-exclusive.example_pages.text1 blog.duxiu-exclusive.example_pages.text2 blog.duxiu-exclusive.signoff blog.duxiu-exclusive.subtitle blog.duxiu-exclusive.text1 blog.duxiu-exclusive.text2 blog.duxiu-exclusive.text3 blog.duxiu-exclusive.text4 blog.duxiu-exclusive.title blog.duxiu-exclusive.tldr blog.how-to-run.architecture blog.how-to-run.architecture.text1 blog.how-to-run.architecture.text2 blog.how-to-run.architecture.text3 blog.how-to-run.architecture.text4 blog.how-to-run.architecture.text5 blog.how-to-run.architecture.text6 blog.how-to-run.conclusions blog.how-to-run.conclusions.text1 blog.how-to-run.conclusions.text2 blog.how-to-run.innovation-tokens blog.how-to-run.innovation-tokens.text1 blog.how-to-run.innovation-tokens.text2 blog.how-to-run.innovation-tokens.text3 blog.how-to-run.innovation-tokens.text4 blog.how-to-run.innovation-tokens.text5 blog.how-to-run.innovation-tokens.text6 blog.how-to-run.signature blog.how-to-run.text1 blog.how-to-run.text2 blog.how-to-run.text3 blog.how-to-run.title blog.how-to-run.tldr blog.how-to-run.tools blog.how-to-run.tools.app blog.how-to-run.tools.dev blog.how-to-run.tools.management blog.how-to-run.tools.onion blog.how-to-run.tools.proxy blog.how-to-run.tools.text1 blog.how-to-run.tools.text2 blog.how-to-run.tools.text3 blog.how-to-run.tools.text4 blog.how-to-run.tools.text5 blog.how-to.community blog.how-to.community.text1 blog.how-to.community.text2 blog.how-to.community.text3 blog.how-to.community.text4 blog.how-to.community.text5 blog.how-to.community.text6 blog.how-to.conclusion blog.how-to.conclusion.text1 blog.how-to.projects blog.how-to.projects.data blog.how-to.projects.data.text1 blog.how-to.projects.data.text2 blog.how-to.projects.data.text3 blog.how-to.projects.data.text4 blog.how-to.projects.distribution blog.how-to.projects.distribution.text1 blog.how-to.projects.distribution.text2 blog.how-to.projects.distribution.text3 blog.how-to.projects.distribution.text4 blog.how-to.projects.distribution.text5 blog.how-to.projects.distribution.text6 blog.how-to.projects.distribution.text7 blog.how-to.projects.domain blog.how-to.projects.domain.text1 blog.how-to.projects.domain.text2 blog.how-to.projects.domain.text3 blog.how-to.projects.domain.why.skills blog.how-to.projects.domain.why.target blog.how-to.projects.domain.why.thinking blog.how-to.projects.domain.why.time blog.how-to.projects.domain.why.why blog.how-to.projects.metadata blog.how-to.projects.metadata.dates blog.how-to.projects.metadata.hash blog.how-to.projects.metadata.id blog.how-to.projects.metadata.location blog.how-to.projects.metadata.notes blog.how-to.projects.metadata.size blog.how-to.projects.metadata.text1 blog.how-to.projects.metadata.text2 blog.how-to.projects.metadata.text3 blog.how-to.projects.metadata.text4 blog.how-to.projects.metadata.text5 blog.how-to.projects.metadata.text6 blog.how-to.projects.metadata.title blog.how-to.projects.phase1 blog.how-to.projects.phase2 blog.how-to.projects.phase3 blog.how-to.projects.phase4 blog.how-to.projects.phase5 blog.how-to.projects.phase6 blog.how-to.projects.scraping blog.how-to.projects.scraping.text1 blog.how-to.projects.scraping.text2 blog.how-to.projects.scraping.text3 blog.how-to.projects.scraping.text4 blog.how-to.projects.scraping.text5 blog.how-to.projects.target blog.how-to.projects.target.accessible blog.how-to.projects.target.insight blog.how-to.projects.target.large blog.how-to.projects.target.text1 blog.how-to.projects.target.text2 blog.how-to.projects.target.text3 blog.how-to.projects.target.unique blog.how-to.projects.text1 blog.how-to.projects.text2 blog.how-to.signature blog.how-to.text1 blog.how-to.text2 blog.how-to.text3 blog.how-to.title blog.how-to.tldr blog.how-to.updates blog.how-to.updates.item1 blog.how-to.updates.item2 blog.index.heading blog.index.text1 blog.index.text2 blog.index.text3 blog.introducing.focus.library blog.introducing.focus.mirror blog.introducing.focus.pirate blog.introducing.footnote blog.introducing.signature blog.introducing.text1 blog.introducing.text2 blog.introducing.text3 blog.introducing.text4 blog.introducing.text5 blog.introducing.text6 blog.introducing.title blog.isbndb-dump.10% blog.isbndb-dump.effort.google blog.isbndb-dump.effort.ils blog.isbndb-dump.effort.isbndb blog.isbndb-dump.effort.openlib blog.isbndb-dump.effort.worldcat blog.isbndb-dump.fn1 blog.isbndb-dump.fn2 blog.isbndb-dump.fn3 blog.isbndb-dump.maybe.copies blog.isbndb-dump.maybe.editions blog.isbndb-dump.maybe.files blog.isbndb-dump.maybe.works blog.isbndb-dump.signature blog.isbndb-dump.text1 blog.isbndb-dump.text10 blog.isbndb-dump.text11 blog.isbndb-dump.text12 blog.isbndb-dump.text13 blog.isbndb-dump.text14 blog.isbndb-dump.text15 blog.isbndb-dump.text16 blog.isbndb-dump.text17 blog.isbndb-dump.text18 blog.isbndb-dump.text2 blog.isbndb-dump.text3 blog.isbndb-dump.text4 blog.isbndb-dump.text5 blog.isbndb-dump.text6 blog.isbndb-dump.text7 blog.isbndb-dump.text8 blog.isbndb-dump.text9 blog.isbndb-dump.title blog.isbndb-dump.tldr blog.template.subheading blog.worldcat-scrape.alt.redesign blog.worldcat-scrape.data blog.worldcat-scrape.data.format blog.worldcat-scrape.text1 blog.worldcat-scrape.text10 blog.worldcat-scrape.text2 blog.worldcat-scrape.text3 blog.worldcat-scrape.text4 blog.worldcat-scrape.text5 blog.worldcat-scrape.text6 blog.worldcat-scrape.text7 blog.worldcat-scrape.text8 blog.worldcat-scrape.text9 blog.worldcat-scrape.title blog.worldcat-scrape.tldr blog.worldcat-scrape.worldcat blog.zlib-on-ipfs.deprecated blog.zlib-on-ipfs.title common.access_types_mapping.aa_download common.access_types_mapping.aa_scidb common.access_types_mapping.external_borrow common.access_types_mapping.external_borrow_printdisabled common.access_types_mapping.external_download common.access_types_mapping.meta_explore common.access_types_mapping.torrents_available common.back common.donation.membership_bonus_parens common.donation.order_processing_status_labels.0 common.donation.order_processing_status_labels.1 common.donation.order_processing_status_labels.2 common.donation.order_processing_status_labels.3 common.donation.order_processing_status_labels.4 common.donation.order_processing_status_labels.5 common.english_only common.form.go common.form.reset common.forward common.last common.libgen.email common.md5.servers.browser_verification_unlimited common.md5.servers.fast_partner common.md5.servers.fast_partner.recommended2 common.md5.servers.faster_with_waitlist common.md5.servers.no_browser_verification common.md5.servers.no_browser_verification_or_waitlists common.md5.servers.slow_no_waitlist common.md5.servers.slow_partner common.md5_content_type_mapping.audiobook common.md5_content_type_mapping.book_comic common.md5_content_type_mapping.book_fiction common.md5_content_type_mapping.book_nonfiction common.md5_content_type_mapping.book_unknown common.md5_content_type_mapping.journal_article common.md5_content_type_mapping.magazine common.md5_content_type_mapping.musical_score common.md5_content_type_mapping.other common.md5_content_type_mapping.standards_document common.md5_problem_type_mapping.duxiu_pdg_broken_files common.md5_problem_type_mapping.lgli_broken common.md5_problem_type_mapping.lgli_visible common.md5_problem_type_mapping.lgrsfic_visible common.md5_problem_type_mapping.lgrsnf_visible common.md5_problem_type_mapping.upload_exiftool_failed common.md5_problem_type_mapping.zlib_bad_file common.md5_problem_type_mapping.zlib_missing common.md5_problem_type_mapping.zlib_spam common.md5_report_type_mapping.broken common.md5_report_type_mapping.copyright common.md5_report_type_mapping.download common.md5_report_type_mapping.metadata common.md5_report_type_mapping.other common.md5_report_type_mapping.pages common.md5_report_type_mapping.spam common.membership.format_currency.amount_with_usd common.membership.format_currency.total common.membership.format_currency.total_with_usd common.membership.tier_name.2 common.membership.tier_name.3 common.membership.tier_name.4 common.membership.tier_name.5 common.membership.tier_name.bonus common.record_sources_mapping.cerlalc common.record_sources_mapping.czech_oo42hcks common.record_sources_mapping.duxiu common.record_sources_mapping.edsebk common.record_sources_mapping.gbooks common.record_sources_mapping.goodreads common.record_sources_mapping.hathi common.record_sources_mapping.ia common.record_sources_mapping.iacdl common.record_sources_mapping.isbndb common.record_sources_mapping.isbngrp common.record_sources_mapping.lgli common.record_sources_mapping.lgli.excluding_scimag common.record_sources_mapping.lgrs common.record_sources_mapping.lgrs.nonfiction_and_fiction common.record_sources_mapping.libby common.record_sources_mapping.magzdb common.record_sources_mapping.nexusstc common.record_sources_mapping.oclc common.record_sources_mapping.ol common.record_sources_mapping.rgb common.record_sources_mapping.scihub common.record_sources_mapping.scihub.via_lgli_scimag common.record_sources_mapping.scihub_scimag common.record_sources_mapping.trantor common.record_sources_mapping.uploads common.record_sources_mapping.zlib common.record_sources_mapping.zlibzh common.search.placeholder common.search.submit common.specific_search_fields.author common.specific_search_fields.description_comments common.specific_search_fields.edition_varia common.specific_search_fields.original_filename common.specific_search_fields.publisher common.specific_search_fields.select common.specific_search_fields.title common.specific_search_fields.year common.tech_details dyn.buy_membership.error.minimum dyn.buy_membership.error.try_again dyn.buy_membership.error.unknown dyn.buy_membership.error.wait layout.index.banners.comics_fundraiser.text layout.index.footer.dont_email layout.index.footer.list1.header layout.index.footer.list2.dmca_copyright layout.index.footer.list2.header layout.index.footer.list2.reddit layout.index.footer.list3.header layout.index.footer.list3.link.slum layout.index.footer.list3.link.unaffiliated layout.index.header.banner.fundraiser.help layout.index.header.banner.fundraiser.now layout.index.header.banner.fundraiser.takedown layout.index.header.banner.fundraiser.this_month layout.index.header.banner.fundraiser.valid_end_of_month layout.index.header.banner.holiday_gift layout.index.header.banner.issues.memberships_extended layout.index.header.banner.issues.partners_closed layout.index.header.banner.mirrors layout.index.header.banner.new_donation_method layout.index.header.banner.refer layout.index.header.banner.surprise layout.index.header.banner.valentine_gift layout.index.header.learn_more layout.index.header.nav.account layout.index.header.nav.activity layout.index.header.nav.advanced layout.index.header.nav.annasblog layout.index.header.nav.annassoftware layout.index.header.nav.beta layout.index.header.nav.codes layout.index.header.nav.datasets layout.index.header.nav.donate layout.index.header.nav.downloaded_files layout.index.header.nav.faq layout.index.header.nav.home layout.index.header.nav.improve_metadata layout.index.header.nav.llm_data layout.index.header.nav.login_register layout.index.header.nav.my_donations layout.index.header.nav.public_profile layout.index.header.nav.search layout.index.header.nav.security layout.index.header.nav.torrents layout.index.header.nav.translate layout.index.header.nav.volunteering layout.index.header.recent_downloads layout.index.header.tagline layout.index.header.tagline_and layout.index.header.tagline_and_more layout.index.header.tagline_duxiu layout.index.header.tagline_ia layout.index.header.tagline_libgen layout.index.header.tagline_new1 layout.index.header.tagline_new3 layout.index.header.tagline_newnew2a layout.index.header.tagline_newnew2b layout.index.header.tagline_open_source layout.index.header.tagline_openlib layout.index.header.tagline_scihub layout.index.header.tagline_separator layout.index.header.tagline_short layout.index.header.tagline_zlib layout.index.header.title layout.index.invalid_request layout.index.meta.description layout.index.meta.opensearch layout.index.title page.aarecord_issue.text page.aarecord_issue.title page.about.help.text page.about.help.text10 page.about.help.text11 page.about.help.text6 page.about.help.text7 page.about.help.text8 page.about.help.text9 page.about.help.volunteer page.about.text2 page.about.text3 page.account.logged_in.account_id page.account.logged_in.logout.button page.account.logged_in.logout.failure page.account.logged_in.logout.success page.account.logged_in.membership_fast_downloads_used page.account.logged_in.membership_has_some page.account.logged_in.membership_multiple page.account.logged_in.membership_none page.account.logged_in.membership_upgrade page.account.logged_in.public_profile page.account.logged_in.secret_key_dont_share page.account.logged_in.secret_key_show page.account.logged_in.telegram_group_join page.account.logged_in.telegram_group_upgrade page.account.logged_in.telegram_group_wrapper page.account.logged_in.title page.account.logged_in.which_downloads page.account.logged_out.key_form.button page.account.logged_out.key_form.dont_lose_key page.account.logged_out.key_form.invalid_key page.account.logged_out.key_form.placeholder page.account.logged_out.key_form.text page.account.logged_out.old_email.button page.account.logged_out.register.button page.account.logged_out.register.header page.account.logged_out.registered.text1 page.account.logged_out.registered.text2 page.account.logged_out.registered.text3 page.account.logged_out.title page.browserverification.header page.codes.bad_unicode page.codes.code_description page.codes.code_label page.codes.code_prefix page.codes.code_url page.codes.code_website page.codes.codes_starting_with page.codes.dont_scrape page.codes.fewer_than page.codes.generic_url page.codes.heading page.codes.index_of_dir_path page.codes.intro page.codes.known_code_prefix page.codes.more page.codes.prefix page.codes.record_starting_with page.codes.records_starting_with page.codes.records_codes page.codes.records_prefix the %s should not be changedpage.codes.s_substitution page.codes.search_archive_start page.codes.title page.codes.url_link page.codes.why_cloudflare page.comments.abuse_reported page.comments.better_version page.comments.do_you_want_to_report_abuse page.comments.file_issue page.comments.hidden_comment page.comments.reply_button page.comments.report_abuse page.comments.reported_abuse_this_user page.contact.checkboxes.copyright page.contact.checkboxes.show_email_button page.contact.checkboxes.text1 page.contact.checkboxes.text2 page.contact.dmca.delete page.contact.dmca.form page.contact.title page.copyright.form.aa_urls page.copyright.form.aa_urls.note page.copyright.form.address page.copyright.form.description page.copyright.form.email page.copyright.form.external_urls page.copyright.form.isbns page.copyright.form.name page.copyright.form.on_failure page.copyright.form.on_success page.copyright.form.openlib_urls page.copyright.form.phone page.copyright.form.statement page.copyright.form.submit_claim page.copyright.intro page.copyright.title page.datasets.common.aa_example_record page.datasets.common.aa_torrents page.datasets.common.aac page.datasets.common.import_scripts page.datasets.common.intro page.datasets.common.last_updated page.datasets.common.main_website page.datasets.common.metadata_docs page.datasets.common.mirrored_file_count page.datasets.common.resources page.datasets.common.total_files page.datasets.common.total_filesize page.datasets.duxiu.blog_post page.datasets.duxiu.description page.datasets.duxiu.description2 page.datasets.duxiu.description3 page.datasets.duxiu.raw_notes.title page.datasets.duxiu.see_blog_post page.datasets.duxiu.title page.datasets.file page.datasets.files page.datasets.ia.description page.datasets.ia.description2 page.datasets.ia.description3 page.datasets.ia.ia_lending page.datasets.ia.part1 page.datasets.ia.part2 page.datasets.ia.title page.datasets.iacdl.searchable page.datasets.intro.text2 page.datasets.intro.text3 page.datasets.isbn_ranges.isbn_metadata page.datasets.isbn_ranges.isbn_website page.datasets.isbn_ranges.last_updated page.datasets.isbn_ranges.resources page.datasets.isbn_ranges.text1 page.datasets.isbn_ranges.title page.datasets.isbndb.release1.text1 page.datasets.isbndb.release1.text2 page.datasets.isbndb.release1.text3 page.datasets.isbndb.release1.title page.datasets.lgli_fiction_is_behind page.datasets.libgen_li.comics_announcement page.datasets.libgen_li.comics_torrents page.datasets.libgen_li.description1 page.datasets.libgen_li.description2 page.datasets.libgen_li.description3 page.datasets.libgen_li.description4.1 page.datasets.libgen_li.description4.fiction_rus page.datasets.libgen_li.description4.fiction_torrents page.datasets.libgen_li.description4.omissions page.datasets.libgen_li.description4.stats page.datasets.libgen_li.description4.torrents page.datasets.libgen_li.description5 page.datasets.libgen_li.description6 page.datasets.libgen_li.fiction_rus page.datasets.libgen_li.fiction_rus_torrents page.datasets.libgen_li.fiction_torrents page.datasets.libgen_li.forum page.datasets.libgen_li.link_metadata page.datasets.libgen_li.link_metadata_ftp page.datasets.libgen_li.magazines_torrents page.datasets.libgen_li.metadata_structure page.datasets.libgen_li.mirrors page.datasets.libgen_li.standarts_torrents page.datasets.libgen_li.title page.datasets.libgen_rs.aa_covers page.datasets.libgen_rs.about page.datasets.libgen_rs.covers_announcement page.datasets.libgen_rs.description.about page.datasets.libgen_rs.description.metadata page.datasets.libgen_rs.description.new_torrents page.datasets.libgen_rs.fiction_torrents page.datasets.libgen_rs.link_fiction page.datasets.libgen_rs.link_forum page.datasets.libgen_rs.link_metadata page.datasets.libgen_rs.link_metadata_fields page.datasets.libgen_rs.link_nonfiction page.datasets.libgen_rs.nonfiction_torrents page.datasets.libgen_rs.release1.fiction page.datasets.libgen_rs.release1.intro page.datasets.libgen_rs.release1.nonfiction page.datasets.libgen_rs.release1.outro page.datasets.libgen_rs.release1.title page.datasets.libgen_rs.story page.datasets.libgen_rs.story.dontexist page.datasets.libgen_rs.story.dot_fun page.datasets.libgen_rs.story.dot_li page.datasets.libgen_rs.story.dot_rs page.datasets.libgen_rs.story.rus_dot_ec page.datasets.libgen_rs.story.zlib page.datasets.libgen_rs.title page.datasets.metadata_only_sources.text1 page.datasets.metadata_only_sources.text2 page.datasets.metadata_only_sources.title page.datasets.openlib.description page.datasets.openlib.title page.datasets.overview.excluding_duplicates page.datasets.overview.last_updated.header page.datasets.overview.mirrored.clarification page.datasets.overview.mirrored.header page.datasets.overview.size.header page.datasets.overview.source.header page.datasets.overview.text1 page.datasets.overview.text4 page.datasets.overview.text5 page.datasets.overview.title page.datasets.overview.total page.datasets.scihub.aa_torrents page.datasets.scihub.description1 page.datasets.scihub.description2 page.datasets.scihub.description3 page.datasets.scihub.description4 page.datasets.scihub.link_libgen_li_torrents page.datasets.scihub.link_libgen_rs_torrents page.datasets.scihub.link_metadata page.datasets.scihub.link_paused page.datasets.scihub.link_podcast page.datasets.scihub.link_wikipedia page.datasets.scihub.title page.datasets.scihub_frozen_1 page.datasets.scihub_frozen_2 page.datasets.source_libraries.text1 page.datasets.source_libraries.text2 page.datasets.source_libraries.title page.datasets.sources.duxiu.files1 page.datasets.sources.duxiu.files2 page.datasets.sources.duxiu.files3 page.datasets.sources.duxiu.metadata1 page.datasets.sources.duxiu.metadata2 page.datasets.sources.duxiu.metadata3 page.datasets.sources.files.header page.datasets.sources.ia.files1 page.datasets.sources.ia.files2 page.datasets.sources.ia.metadata1 page.datasets.sources.ia.metadata2 page.datasets.sources.ia.metadata3 page.datasets.sources.last_updated.header page.datasets.sources.libgen_li.collab page.datasets.sources.libgen_li.files1 page.datasets.sources.libgen_li.metadata1 page.datasets.sources.libgen_rs.files1 page.datasets.sources.libgen_rs.files2 page.datasets.sources.libgen_rs.metadata1 page.datasets.sources.metadata.header page.datasets.sources.openlib.metadata1 page.datasets.sources.scihub.files1 page.datasets.sources.scihub.files2 page.datasets.sources.scihub.metadata1 page.datasets.sources.scihub.metadata2 page.datasets.sources.source.header page.datasets.sources.uploads.metadata_and_files page.datasets.sources.worldcat.metadata1 page.datasets.sources.worldcat.metadata2 page.datasets.sources.zlib.metadata_and_files page.datasets.title page.datasets.unified_database.text1 page.datasets.unified_database.text2 page.datasets.unified_database.title page.datasets.upload.aa_torrents page.datasets.upload.action.browse page.datasets.upload.action.search page.datasets.upload.description page.datasets.upload.overview page.datasets.upload.source.aaaaarg page.datasets.upload.source.acm page.datasets.upload.source.airitibooks page.datasets.upload.source.alexandrina page.datasets.upload.source.bibliotik page.datasets.upload.source.bpb9v_cadal page.datasets.upload.source.bpb9v_direct page.datasets.upload.source.cgiym_chinese page.datasets.upload.source.cgiym_more page.datasets.upload.source.chinese_architecture page.datasets.upload.source.degruyter page.datasets.upload.source.docer page.datasets.upload.source.duxiu_epub page.datasets.upload.source.duxiu_main page.datasets.upload.source.elsevier page.datasets.upload.source.emo37c page.datasets.upload.source.french page.datasets.upload.source.hentai page.datasets.upload.source.ia_multipart page.datasets.upload.source.imslp page.datasets.upload.source.japanese_manga page.datasets.upload.source.longquan_archives page.datasets.upload.source.magzdb page.datasets.upload.source.mangaz_com page.datasets.upload.source.misc page.datasets.upload.source.newsarch_ebooks page.datasets.upload.source.newsarch_magz page.datasets.upload.source.pdcnet_org page.datasets.upload.source.polish page.datasets.upload.source.shuge page.datasets.upload.source.shukui_net_cdl page.datasets.upload.source.trantor page.datasets.upload.source.turkish_pdfs page.datasets.upload.source.twlibrary page.datasets.upload.source.wll page.datasets.upload.source.woz9ts_direct page.datasets.upload.source.woz9ts_duxiu page.datasets.upload.subcollections page.datasets.upload.subs.heading page.datasets.upload.subs.notes page.datasets.upload.subs.subcollection page.datasets.upload.subsubcollections page.datasets.upload.title page.datasets.worldcat.blog_announcement page.datasets.worldcat.description page.datasets.worldcat.description2 page.datasets.worldcat.description2.label page.datasets.worldcat.title page.datasets.worldcat.torrents page.datasets.zlib.aa_example_record.original page.datasets.zlib.aa_example_record.zlib3 page.datasets.zlib.aa_torrents page.datasets.zlib.blog.release1 page.datasets.zlib.blog.release2 page.datasets.zlib.description.allegations page.datasets.zlib.description.allegations.title page.datasets.zlib.description.intro page.datasets.zlib.description.three_parts page.datasets.zlib.description.three_parts.first page.datasets.zlib.description.three_parts.second page.datasets.zlib.description.three_parts.third_and_incremental page.datasets.zlib.historical.release1.description1 page.datasets.zlib.historical.release1.description2 page.datasets.zlib.historical.release1.description3 page.datasets.zlib.historical.release1.description4 page.datasets.zlib.historical.release1.description5 page.datasets.zlib.historical.release1.description6 page.datasets.zlib.historical.release1.title page.datasets.zlib.historical.release2.addendum.description1 page.datasets.zlib.historical.release2.addendum.title page.datasets.zlib.historical.release2.description1 page.datasets.zlib.historical.release2.description2 page.datasets.zlib.historical.release2.description3 page.datasets.zlib.historical.release2.description4 page.datasets.zlib.historical.release2.description5 page.datasets.zlib.historical.release2.description5.update1 page.datasets.zlib.historical.release2.description5.update2 page.datasets.zlib.historical.release2.field.in_libgen page.datasets.zlib.historical.release2.field.pilimi_torrent page.datasets.zlib.historical.release2.field.unavailable page.datasets.zlib.historical.release2.title page.datasets.zlib.historical.title page.datasets.zlib.link.onion page.datasets.zlib.link.zlib page.datasets.zlib.title page.datasets.zlibzh.searchable page.datesets.openlib.link_metadata page.donate.bonus_downloads.main page.donate.bonus_downloads.period page.donate.buttons.donate page.donate.buttons.join page.donate.buttons.selected page.donate.buttons.up_to_discounts page.donate.ccexp.alipay page.donate.ccexp.amazon_com page.donate.ccexp.crypto page.donate.ccexp.wechat page.donate.coinbase_eth page.donate.copied page.donate.copy page.donate.currency_lowest_minimum page.donate.currency_warning_high_minimum page.donate.discount page.donate.duration.12_mo page.donate.duration.1_mo page.donate.duration.24_mo page.donate.duration.3_mo page.donate.duration.48_mo page.donate.duration.6_mo page.donate.duration.96_mo page.donate.duration.intro page.donate.duration.summary page.donate.duration.summary.discount page.donate.duration.summary.duration.12_mo page.donate.duration.summary.duration.1_mo page.donate.duration.summary.duration.24_mo page.donate.duration.summary.duration.3_mo page.donate.duration.summary.duration.48_mo page.donate.duration.summary.duration.6_mo page.donate.duration.summary.duration.96_mo page.donate.duration.summary.monthly_cost page.donate.expert.contact_us page.donate.expert.direct_sftp page.donate.expert.enterprise_donation page.donate.expert.title page.donate.expert.unlimited_access page.donate.faq.membership page.donate.faq.non_member_donation page.donate.faq.ranges page.donate.faq.renew page.donate.faq.spend page.donate.faq.text_large_donation page.donate.faq.text_other_payment1 page.donate.faq.title page.donate.header.existing_unpaid_donation page.donate.header.existing_unpaid_donation_view_all page.donate.header.large_donations page.donate.header.large_donations_wealthy page.donate.header.recurring page.donate.header.text1 page.donate.header.text2 page.donate.login2 page.donate.membership_per_month page.donate.mistake page.donate.one_time_payment.paypal.text2 page.donate.one_time_payment.paypal.text4 page.donate.payment.buttons.alipay page.donate.payment.buttons.alipay_wechat page.donate.payment.buttons.amazon page.donate.payment.buttons.amazon_cc page.donate.payment.buttons.bank_card page.donate.payment.buttons.bank_card_app page.donate.payment.buttons.binance page.donate.payment.buttons.bmc page.donate.payment.buttons.cashapp page.donate.payment.buttons.credit_debit page.donate.payment.buttons.credit_debit2 page.donate.payment.buttons.credit_debit_backup page.donate.payment.buttons.crypto page.donate.payment.buttons.givebutter page.donate.payment.buttons.paypal page.donate.payment.buttons.paypal_plain page.donate.payment.buttons.paypalreg page.donate.payment.buttons.pix page.donate.payment.buttons.revolut page.donate.payment.buttons.temporarily_unavailable page.donate.payment.buttons.wechat page.donate.payment.crypto_select page.donate.payment.desc.amazon page.donate.payment.desc.amazon_cc page.donate.payment.desc.amazon_com page.donate.payment.desc.amazon_message page.donate.payment.desc.amazon_message_1 page.donate.payment.desc.amazon_round page.donate.payment.desc.bank_card_app page.donate.payment.desc.bank_card_app.step1.desc1 page.donate.payment.desc.bank_card_app.step1.desc2 page.donate.payment.desc.bank_card_app.step1.desc3 page.donate.payment.desc.bank_card_app.step1.header page.donate.payment.desc.bank_card_app.step2.desc1 page.donate.payment.desc.bank_card_app.step2.desc2 page.donate.payment.desc.bank_card_app.step2.header page.donate.payment.desc.binance_p1 page.donate.payment.desc.binance_p2 page.donate.payment.desc.bmc page.donate.payment.desc.cashapp page.donate.payment.desc.cashapp_easy page.donate.payment.desc.cashapp_fee page.donate.payment.desc.credit_debit page.donate.payment.desc.credit_debit_backup page.donate.payment.desc.credit_debit_explained page.donate.payment.desc.crypto page.donate.payment.desc.crypto2 page.donate.payment.desc.crypto_express_services page.donate.payment.desc.crypto_suggestion_dynamic page.donate.payment.desc.elimate_discount page.donate.payment.desc.givebutter page.donate.payment.desc.google_apple page.donate.payment.desc.longer_subs page.donate.payment.desc.paypal page.donate.payment.desc.paypal_short page.donate.payment.desc.paypalreg page.donate.payment.desc.revolut page.donate.payment.desc.revolut_easy page.donate.payment.maximum_method page.donate.payment.minimum_method page.donate.payment.processor.binance page.donate.payment.processor.coinbase page.donate.payment.processor.kraken page.donate.payment.select_method page.donate.perks.adopt page.donate.perks.credits page.donate.perks.early_access page.donate.perks.exclusive_telegram page.donate.perks.fast_downloads page.donate.perks.if_you_donate_this_month page.donate.perks.jsonapi page.donate.perks.legendary page.donate.perks.previous_plus page.donate.perks.refer page.donate.perks.scidb page.donate.please_include page.donate.refer.text1 page.donate.small_team page.donate.strange_account page.donate.submit.button page.donate.submit.button.label.12_mo page.donate.submit.button.label.1_mo page.donate.submit.button.label.24_mo page.donate.submit.button.label.3_mo page.donate.submit.button.label.48_mo page.donate.submit.button.label.6_mo page.donate.submit.button.label.96_mo page.donate.submit.cancel_note page.donate.submit.confirm page.donate.submit.crypto_note page.donate.submit.failure page.donate.submit.header1 page.donate.submit.header2 page.donate.submit.success page.donate.title page.donate.wait_new page.donate.without_membership page.donation.amazon.confirm_automated page.donation.amazon.doesnt_work page.donation.amazon.example page.donation.amazon.form_instructions page.donation.amazon.form_to page.donation.amazon.header page.donation.amazon.only_official page.donation.amazon.only_use_once page.donation.amazon.unique page.donation.amazon.waiting_gift_card page.donation.bank_card_app.step3.desc.1 page.donation.bank_card_app.step3.desc.2 page.donation.bank_card_app.step3.desc.3 page.donation.bank_card_app.step3.header page.donation.buy_pyusd page.donation.cash_app_btc.step1 page.donation.cash_app_btc.step1.more page.donation.cash_app_btc.step1.text1 page.donation.cash_app_btc.step2 page.donation.cash_app_btc.step2.rush_priority page.donation.cash_app_btc.step2.transfer page.donation.ccexp.crypto_express_services.1 page.donation.ccexp.crypto_express_services.2 page.donation.ccexp.crypto_express_services.3 page.donation.ccexp.crypto_express_services.4 page.donation.confirmation_can_take_a_while page.donation.credit_debit_card_instructions page.donation.credit_debit_card_our_page page.donation.crypto_dont_worry page.donation.crypto_instructions page.donation.crypto_qr_code_instructions page.donation.crypto_qr_code_title page.donation.crypto_standard page.donation.donate_on_this_page page.donation.expired page.donation.expired_already_paid page.donation.footer.button page.donation.footer.crypto_note page.donation.footer.failure page.donation.footer.header page.donation.footer.issues_contact page.donation.footer.success page.donation.footer.text1 page.donation.footer.text2 page.donation.footer.verification page.donation.header.cancel.button page.donation.header.cancel.confirm.button page.donation.header.cancel.confirm.msg page.donation.header.cancel.failure page.donation.header.cancel.new_donation page.donation.header.cancel.success page.donation.header.date page.donation.header.id page.donation.header.reorder page.donation.header.status page.donation.header.total_including_discount page.donation.header.total_without_discount page.donation.hoodpay.step1 page.donation.hoodpay.step2 page.donation.hoodpay.step3 page.donation.hoodpay.step4 page.donation.hoodpay.step5 page.donation.hoodpay.step6 page.donation.might_want_to_cancel page.donation.old_instructions.intro_outdated page.donation.old_instructions.intro_paid page.donation.old_instructions.show_button page.donation.page_blocked page.donation.payment.alipay.error page.donation.payment.alipay.header1 page.donation.payment.alipay.text1_new page.donation.payment.alipay.top_header page.donation.payment.crypto.header1 page.donation.payment.crypto.text1 page.donation.payment.crypto.top_header page.donation.payment.paypal.text3 page.donation.payment.paypal.text5 page.donation.payment.pix.header1 page.donation.payment.pix.text1 page.donation.payment.pix.top_header page.donation.payment.wechat.header1 page.donation.payment.wechat.text1 page.donation.payment.wechat.top_header page.donation.payment2cc.cc2btc page.donation.payment2cc.cc2btc.btc_address page.donation.payment2cc.cc2btc.btc_amount page.donation.payment2cc.cc2btc.form page.donation.payment2cc.cc2btc.outdated page.donation.payment2cc.exact_amount page.donation.payment2cc.method.coingate page.donation.payment2cc.method.mercuryo page.donation.payment2cc.method.moonpay page.donation.payment2cc.method.munzen page.donation.payment2cc.method.paybis page.donation.payment2cc.method.switchere page.donation.pyusd.instructions page.donation.pyusd.more page.donation.pyusd.transfer page.donation.refresh_status page.donation.reset_timer page.donation.revolut.btc_amount_below page.donation.revolut.step1 page.donation.revolut.step1.more page.donation.revolut.step1.text1 page.donation.revolut.step2 page.donation.revolut.step2.rush_priority page.donation.revolut.step2.transfer page.donation.status_header page.donation.step1 page.donation.step2 page.donation.stepbystep page.donation.stepbystep_below page.donation.thank_you.locked_out page.donation.thank_you.secret_key page.donation.thank_you_donation page.donation.time_left_header page.donation.title page.donation.transfer_amount_to page.donation.waiting_for_confirmation_refresh page.donation.waiting_for_transfer_refresh page.downloaded.earlier page.downloaded.fast_download_time page.downloaded.fast_partner_star page.downloaded.last_18_hours page.downloaded.no_files page.downloaded.not_public page.downloaded.times_utc page.downloaded.title page.downloaded.twice page.faq.1984.text page.faq.1984.title page.faq.anna.text1 page.faq.anna.title page.faq.api.text1 page.faq.api.text2 page.faq.api.text3 page.faq.api.title page.faq.copyright.text1 page.faq.copyright.text2 page.faq.copyright.title page.faq.favorite.text1 page.faq.favorite.title page.faq.hate.text1 page.faq.hate.title page.faq.help.mirrors page.faq.help.title page.faq.metadata.indeed page.faq.metadata.inspiration page.faq.metadata.title page.faq.mhut_upload page.faq.mobile.android page.faq.mobile.ios page.faq.mobile.text1 page.faq.mobile.title page.faq.physical.text1 page.faq.physical.title page.faq.request.title page.faq.resources.annas_blog page.faq.resources.annas_software page.faq.resources.datasets page.faq.resources.domains page.faq.resources.title page.faq.resources.translate page.faq.resources.wikipedia page.faq.save_search.text1 page.faq.save_search.title page.faq.security.text1 page.faq.security.text2 page.faq.security.text3 page.faq.security.title page.faq.slow.text1 page.faq.slow.text2 page.faq.slow.text3 page.faq.slow.text4 page.faq.slow.text5 page.faq.slow.title page.faq.title page.faq.torrents.a1 page.faq.torrents.a2 page.faq.torrents.a3 page.faq.torrents.a3_long_answer_contributions page.faq.torrents.a3_long_answer_ideas page.faq.torrents.a3_long_answer_start page.faq.torrents.a3_short_answer page.faq.torrents.a4 page.faq.torrents.a5 page.faq.torrents.a6 page.faq.torrents.a6.li1 page.faq.torrents.a6.li2 page.faq.torrents.a7 page.faq.torrents.q1 page.faq.torrents.q2 page.faq.torrents.q3 page.faq.torrents.q4 page.faq.torrents.q5 page.faq.torrents.q6 page.faq.torrents.q7 page.faq.torrents.title page.faq.upload.title page.faq.uptime.text1 page.faq.uptime.title page.faq.what_is.title page.fast_downloads.no_member page.fast_downloads.no_member_2 page.fast_downloads.no_more_new page.home.access.header page.home.access.label page.home.access.text page.home.archive.body page.home.archive.header page.home.full_database.header page.home.full_database.search page.home.full_database.subtitle page.home.intro.open_source page.home.intro.text1 page.home.intro.text2 page.home.llm.body page.home.llm.header page.home.mirrors.header page.home.payment_processor.body page.home.preservation.header page.home.preservation.label page.home.preservation.text1 page.home.preservation.text2 page.home.scidb.browser_verification page.home.scidb.continuation page.home.scidb.header page.home.scidb.open page.home.scidb.placeholder_doi page.home.scidb.scihub_paused page.home.scidb.subtitle page.home.scidb.text2 page.home.scidb.text3 page.home.torrents.body page.home.torrents.legend_greater page.home.torrents.legend_less page.home.torrents.legend_range page.home.volunteering.header page.home.volunteering.help_out page.ipfs_downloads.title page.list.by_and_date page.list.edit.button page.list.edit.failure page.list.edit.success page.list.empty page.list.header.edit.link page.list.new_item page.list.title page.llm.how_we_can_help page.llm.how_we_can_help.deduplication page.llm.how_we_can_help.extraction page.llm.how_we_can_help.ocr page.llm.how_we_can_help.text1 page.llm.how_we_can_help.text2 page.llm.how_we_can_help.text3 page.llm.how_we_can_help.text4 page.llm.how_we_can_help.text5 page.llm.intro page.llm.title page.llm.unique_scale page.llm.unique_scale.text1 page.llm.unique_scale.text2 page.llm.unique_scale.text3 page.login.continue page.login.lost_key page.login.lost_key_contact page.login.please page.login.text1 page.login.text2 page.login.text3 page.login.title page.maintenance.header page.md5.box.alternative_author page.md5.box.alternative_description page.md5.box.alternative_edition page.md5.box.alternative_extension page.md5.box.alternative_filename page.md5.box.alternative_publisher page.md5.box.alternative_title page.md5.box.date_open_sourced_title page.md5.box.descr_read_more page.md5.box.descr_title page.md5.box.download.aa_cadal page.md5.box.download.aa_duxiu page.md5.box.download.aa_dxid page.md5.box.download.aa_isbn page.md5.box.download.aa_oclc page.md5.box.download.aa_openlib page.md5.box.download.aa_viewer page.md5.box.download.affected_files page.md5.box.download.after_downloading page.md5.box.download.better_file page.md5.box.download.bulk_torrents page.md5.box.download.collection page.md5.box.download.conversion page.md5.box.download.conversion.links page.md5.box.download.dl_managers page.md5.box.download.dl_managers.links page.md5.box.download.edsebk page.md5.box.download.experts_only page.md5.box.download.extra_also_click_get page.md5.box.download.extra_click_get page.md5.box.download.header_external page.md5.box.download.header_fast_member page.md5.box.download.header_fast_member_no_remaining_new page.md5.box.download.header_fast_member_valid_for page.md5.box.download.header_fast_no_member page.md5.box.download.header_fast_only page.md5.box.download.header_slow_only page.md5.box.download.ia_borrow page.md5.box.download.ipfs_gateway page.md5.box.download.ipfs_gateway_extra page.md5.box.download.lgli page.md5.box.download.lgrsfic page.md5.box.download.lgrsnf page.md5.box.download.libgen_ads page.md5.box.download.link.send_to_kindle page.md5.box.download.link.send_to_kobokindle page.md5.box.download.magzdb page.md5.box.download.manualslib page.md5.box.download.nexusstc page.md5.box.download.nexusstc_unreliable page.md5.box.download.no_found page.md5.box.download.no_issues_notice page.md5.box.download.no_redirect page.md5.box.download.open_in_our_viewer page.md5.box.download.open_in_viewer page.md5.box.download.option page.md5.box.download.original_cadal page.md5.box.download.original_duxiu page.md5.box.download.original_isbndb page.md5.box.download.original_oclc page.md5.box.download.original_openlib page.md5.box.download.other_isbn page.md5.box.download.print_disabled_only page.md5.box.download.pubmed page.md5.box.download.readers page.md5.box.download.readers.links page.md5.box.download.scidb page.md5.box.download.scihub page.md5.box.download.scihub_maybe page.md5.box.download.sendtokindle page.md5.box.download.sendtokindle.links page.md5.box.download.slow_faq page.md5.box.download.support page.md5.box.download.support.authors page.md5.box.download.support.libraries page.md5.box.download.temporarily_unavailable page.md5.box.download.torrent page.md5.box.download.trusted_partners page.md5.box.download.zlib page.md5.box.download.zlib_tor page.md5.box.download.zlib_tor_extra page.md5.box.external_downloads page.md5.box.issues.text1 page.md5.box.issues.text2 page.md5.box.metadata_comments_title page.md5.codes.aa_abbr page.md5.codes.aa_search page.md5.codes.code_explorer page.md5.codes.code_search page.md5.codes.url page.md5.codes.website page.md5.header.consider_upload page.md5.header.ia page.md5.header.ia_desc page.md5.header.improve_metadata page.md5.header.meta_cadal_ssno page.md5.header.meta_desc page.md5.header.meta_duxiu_ssid page.md5.header.meta_isbn page.md5.header.meta_magzdb_id page.md5.header.meta_nexus_stc_id page.md5.header.meta_oclc page.md5.header.meta_openlib page.md5.header.scihub page.md5.invalid.header page.md5.invalid.text page.md5.quality.add_comment page.md5.quality.better_md5.line1 page.md5.quality.better_md5.text1 page.md5.quality.better_md5.text2 page.md5.quality.comment_error page.md5.quality.comment_thanks page.md5.quality.copyright page.md5.quality.describe_the_issue page.md5.quality.great.summary page.md5.quality.great_quality page.md5.quality.header page.md5.quality.improve_the_metadata page.md5.quality.issue_description page.md5.quality.logged_out_login page.md5.quality.loved_the_book page.md5.quality.report page.md5.quality.report_error page.md5.quality.report_issue page.md5.quality.report_thanks page.md5.quality.submit_comment page.md5.quality.submit_report page.md5.quality.what_is_wrong page.md5.tabs.borrow page.md5.tabs.comments page.md5.tabs.downloads page.md5.tabs.explore_metadata page.md5.tabs.lists page.md5.tabs.stats page.md5.text.file_info.text1 page.md5.text.ia_info.text1 page.md5.text.linked_metadata page.md5.text.linked_metadata_openlib page.md5.text.md5_info.text1 page.md5.text.md5_info.text2 page.md5.text.report_quality page.md5.text.stats.total_downloads page.md5.top_row.cadal_ssno page.md5.top_row.cerlalc page.md5.top_row.czech_oo42hcks page.md5.top_row.duxiu_ssid page.md5.top_row.edsebk page.md5.top_row.gbooks page.md5.top_row.goodreads page.md5.top_row.hathi page.md5.top_row.isbndb page.md5.top_row.isbngrp page.md5.top_row.libby page.md5.top_row.magzdb page.md5.top_row.nexusstc page.md5.top_row.oclc page.md5.top_row.rgb page.md5.top_row.trantor page.md5.warning.multiple_links page.metadata.background.body1 page.metadata.background.body2 page.metadata.background.body3 page.metadata.background.body4 page.metadata.background.body5 page.metadata.background.body6 page.metadata.background.body7 page.metadata.background.title page.metadata.body1 page.metadata.header page.metadata.openlib.body1 page.metadata.openlib.body2 page.metadata.openlib.body3 page.metadata.openlib.body4 page.metadata.openlib.body5 page.metadata.openlib.howto.item.1 page.metadata.openlib.howto.item.2 page.metadata.openlib.howto.item.2.1 page.metadata.openlib.howto.item.2.2 page.metadata.openlib.howto.item.2.2.1 page.metadata.openlib.howto.item.2.2.2 page.metadata.openlib.howto.item.2.2.3 page.metadata.openlib.howto.item.2.3 page.metadata.openlib.howto.item.3 page.metadata.openlib.howto.item.4 page.metadata.openlib.howto.item.4.1 page.metadata.openlib.howto.item.5 page.metadata.openlib.title page.mirrors.expenses.maybe_donation page.mirrors.expenses.must_demonstrate_ability page.mirrors.expenses.no_compensation_for_time page.mirrors.expenses.text1 page.mirrors.expenses.title page.mirrors.getting_started.text1 page.mirrors.getting_started.text2 page.mirrors.getting_started.text3 page.mirrors.getting_started.text4 page.mirrors.getting_started.title page.mirrors.intro page.mirrors.list.clearly_a_mirror page.mirrors.list.know_the_risks page.mirrors.list.maybe_partner page.mirrors.list.run_anna page.mirrors.list.willing_to_contribute page.mirrors.text1 page.mirrors.title page.my_donations.make_another page.my_donations.no_donations page.my_donations.not_shown page.my_donations.title page.partner_download.bulk_mirroring page.partner_download.downloads_last_24_hours page.partner_download.faster_downloads page.partner_download.header page.partner_download.li1 page.partner_download.li2 page.partner_download.li3 page.partner_download.li4 page.partner_download.main_page page.partner_download.slow_downloads_cloudflare page.partner_download.slow_downloads_official page.partner_download.url page.partner_download.wait page.partner_download.wait_banner page.partner_download.warning_many_downloads page.partner_download.warning_many_downloads2 page.profile.change_display_name.button page.profile.change_display_name.failure page.profile.change_display_name.success page.profile.change_display_name.text page.profile.created_time page.profile.header.edit page.profile.lists.header page.profile.lists.new_list page.profile.lists.no_lists page.profile.not_found page.profile.title page.request.cannot_accomodate page.request.dont_email page.request.forums page.scidb.aa_record page.scidb.doi page.scidb.download page.scidb.header page.scidb.nexusstc page.scidb.no_preview_new page.scidb.please_donate page.scidb.please_donate_bonus page.scidb.refresh page.scidb.scihub page.search.advanced.add_specific page.search.advanced.description_comments page.search.advanced.field.year_published page.search.advanced.header page.search.filters.access.header page.search.filters.content.header page.search.filters.display.header page.search.filters.display.list page.search.filters.display.table page.search.filters.filetype.header page.search.filters.language.header page.search.filters.order_by.header page.search.filters.sorting.largest page.search.filters.sorting.most_relevant page.search.filters.sorting.newest page.search.filters.sorting.note_filesize page.search.filters.sorting.note_open_sourced page.search.filters.sorting.note_publication_year page.search.filters.sorting.oldest page.search.filters.sorting.random page.search.filters.sorting.smallest page.search.filters.source.header page.search.filters.source.scraped page.search.found_matches.digital_lending page.search.found_matches.journals page.search.found_matches.main page.search.found_matches.metadata page.search.header.codes_explorer page.search.header.update_info page.search.icon.exclude page.search.icon.include_only page.search.icon.unchecked page.search.more page.search.pagination.next page.search.pagination.numbers_spacing page.search.pagination.prev page.search.results.digital_lending_info page.search.results.digital_lending_info_more page.search.results.dmca page.search.results.download_time page.search.results.error.header page.search.results.error.unknown page.search.results.fast_download page.search.results.help_preserve page.search.results.incorrectly_slow page.search.results.issues page.search.results.looking_for_papers page.search.results.metadata_info page.search.results.metadata_info_more page.search.results.metadata_no_merging page.search.results.most_comprehensive page.search.results.none page.search.results.numbers_pages page.search.results.other_shadow_libs page.search.results.partial page.search.results.partial_more page.search.results.search_digital_lending page.search.results.search_downloads page.search.results.search_generic page.search.results.search_journals page.search.results.search_metadata page.search.results.shortcuts page.search.results.these_are_records page.search.search_settings page.search.submit page.search.tabs.digital_lending page.search.tabs.download page.search.tabs.journals page.search.tabs.metadata page.search.title.new page.search.title.results page.search.too_inaccurate page.search.too_long_broad_query page.upload.large.text page.upload.libgenli_login_instructions page.upload.text1 page.upload.upload_to_both page.upload.zlib.text1 page.upload.zlib.text2 page.volunteering.intro.heavy page.volunteering.intro.light page.volunteering.intro.text1 page.volunteering.intro.text2 page.volunteering.intro.text3 page.volunteering.section.bounties.heading page.volunteering.section.bounties.text1 page.volunteering.section.bounties.text2 page.volunteering.section.bounties.text3 page.volunteering.section.bounties.text4 page.volunteering.section.bounties.text5 page.volunteering.section.bounties.text6 page.volunteering.section.bounties.text7 page.volunteering.section.light.heading page.volunteering.section.light.matrix page.volunteering.section.light.text1 page.volunteering.section.light.text2 page.volunteering.table.fulfill_requests.milestone_count page.volunteering.table.fulfill_requests.task page.volunteering.table.header.milestone page.volunteering.table.header.task page.volunteering.table.misc.milestone page.volunteering.table.misc.task page.volunteering.table.misc.task2 page.volunteering.table.open_library.leave_comment page.volunteering.table.open_library.milestone_count page.volunteering.table.open_library.random_metadata page.volunteering.table.open_library.task page.volunteering.table.spread_the_word.milestone.let_them_know page.volunteering.table.spread_the_word.milestone_count page.volunteering.table.spread_the_word.task.alt1 page.volunteering.table.translate.milestone page.volunteering.table.translate.task page.volunteering.table.wikipedia.milestone page.volunteering.table.wikipedia.task page.volunteering.title Project-Id-Version: PROJECT VERSION
Report-Msgid-Bugs-To: EMAIL@ADDRESS
POT-Creation-Date: 2025-08-03 20:50+0000
PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE
Last-Translator: FULL NAME <EMAIL@ADDRESS>
Language: ms
Language-Team: ms <<EMAIL>>
Plural-Forms: nplurals=2; plural=(n != 1);
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: 8bit
Generated-By: Babel 2.17.0
 Z-Library adalah perpustakaan yang popular (dan haram). Mereka telah mengambil koleksi Library Genesis dan menjadikannya mudah dicari. Selain itu, mereka menjadi sangat berkesan dalam meminta sumbangan buku baru, dengan memberi insentif kepada pengguna yang menyumbang dengan pelbagai kelebihan. Mereka kini tidak menyumbangkan buku-buku baru ini kembali kepada Library Genesis. Dan tidak seperti Library Genesis, mereka tidak menjadikan koleksi mereka mudah dicerminkan, yang menghalang pemeliharaan yang meluas. Ini penting untuk model perniagaan mereka, kerana mereka mengenakan bayaran untuk mengakses koleksi mereka secara pukal (lebih daripada 10 buku sehari). Kami tidak membuat penilaian moral tentang mengenakan bayaran untuk akses pukal kepada koleksi buku haram. Tidak dapat dinafikan bahawa Z-Library telah berjaya dalam memperluas akses kepada pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bahagian kami: memastikan pemeliharaan jangka panjang koleksi peribadi ini. - Anna dan pasukan (<a %(reddit)s>Reddit</a>) Dalam keluaran asal Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>), kami membuat cermin Z-Library, satu koleksi buku haram yang besar. Sebagai peringatan, ini adalah apa yang kami tulis dalam catatan blog asal itu: Koleksi itu bertarikh kembali ke pertengahan 2021. Sementara itu, Z-Library telah berkembang pada kadar yang mengejutkan: mereka telah menambah kira-kira 3.8 juta buku baru. Terdapat beberapa pendua di dalamnya, tetapi kebanyakannya nampaknya adalah buku baru yang sah, atau imbasan berkualiti lebih tinggi daripada buku yang telah diserahkan sebelum ini. Ini sebahagian besarnya disebabkan oleh peningkatan bilangan moderator sukarelawan di Z-Library, dan sistem muat naik pukal mereka dengan deduplikasi. Kami ingin mengucapkan tahniah kepada mereka atas pencapaian ini. Kami gembira untuk mengumumkan bahawa kami telah mendapatkan semua buku yang ditambah ke Z-Library antara cermin terakhir kami dan Ogos 2022. Kami juga telah kembali dan mengikis beberapa buku yang kami terlepas kali pertama. Secara keseluruhannya, koleksi baru ini adalah kira-kira 24TB, yang jauh lebih besar daripada yang terakhir (7TB). Cermin kami kini berjumlah 31TB. Sekali lagi, kami telah melakukan deduplikasi terhadap Library Genesis, kerana sudah ada torrents yang tersedia untuk koleksi itu. Sila pergi ke Cermin Perpustakaan Lanun untuk melihat koleksi baharu (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>). Terdapat lebih banyak maklumat di sana tentang bagaimana fail disusun, dan apa yang telah berubah sejak kali terakhir. Kami tidak akan memautkannya dari sini, kerana ini hanyalah laman web blog yang tidak menjadi hos bahan haram. Sudah tentu, membenihkan juga merupakan cara yang baik untuk membantu kami. Terima kasih kepada semua yang membenihkan set torrent kami yang terdahulu. Kami berterima kasih atas sambutan positif, dan gembira bahawa terdapat begitu ramai orang yang mengambil berat tentang pemeliharaan pengetahuan dan budaya dengan cara yang luar biasa ini. 3x buku baru ditambah ke Cermin Perpustakaan Lanun (+24TB, 3.8 juta buku) Baca artikel pengiring oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a> - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) artikel pengiring oleh TorrentFreak: <a %(torrentfreak)s>pertama</a>, <a %(torrentfreak_2)s>kedua</a> Tidak lama dahulu, “perpustakaan bayangan” hampir pupus. Sci-Hub, arkib haram besar kertas akademik, telah berhenti mengambil karya baru, disebabkan oleh tuntutan mahkamah. “Z-Library”, perpustakaan haram terbesar buku, melihat penciptanya yang didakwa ditangkap atas tuduhan hak cipta jenayah. Mereka berjaya melarikan diri dari penangkapan mereka, tetapi perpustakaan mereka tidak kurang terancam. Beberapa negara sudah melakukan versi ini. TorrentFreak <a %(torrentfreak)s>melaporkan</a> bahawa China dan Jepun telah memperkenalkan pengecualian AI kepada undang-undang hak cipta mereka. Tidak jelas kepada kami bagaimana ini berinteraksi dengan perjanjian antarabangsa, tetapi ia pasti memberikan perlindungan kepada syarikat domestik mereka, yang menjelaskan apa yang telah kami lihat. Bagi Arkib Anna — kami akan meneruskan kerja bawah tanah kami yang berakar pada keyakinan moral. Namun harapan terbesar kami adalah untuk memasuki cahaya, dan memperkuat impak kami secara sah. Sila reformasi hak cipta. Apabila Z-Library menghadapi penutupan, saya sudah menyandarkan keseluruhan perpustakaannya dan sedang mencari platform untuk menempatkannya. Itulah motivasi saya untuk memulakan Arkib Anna: satu kesinambungan misi di sebalik inisiatif terdahulu. Sejak itu, kami telah berkembang menjadi perpustakaan bayangan terbesar di dunia, menempatkan lebih daripada 140 juta teks berhak cipta dalam pelbagai format — buku, kertas akademik, majalah, surat khabar, dan banyak lagi. Pasukan saya dan saya adalah ideolog. Kami percaya bahawa memelihara dan menghoskan fail-fail ini adalah betul dari segi moral. Perpustakaan di seluruh dunia mengalami pemotongan dana, dan kami juga tidak boleh mempercayakan warisan manusia kepada korporat. Kemudian datanglah AI. Hampir semua syarikat besar yang membina LLM menghubungi kami untuk melatih data kami. Kebanyakan (tetapi tidak semua!) syarikat yang berpangkalan di AS mempertimbangkan semula apabila mereka menyedari sifat haram kerja kami. Sebaliknya, firma China dengan bersemangat menerima koleksi kami, nampaknya tidak terganggu oleh kesahihannya. Ini adalah penting memandangkan peranan China sebagai penandatangan kepada hampir semua perjanjian hak cipta antarabangsa utama. Kami telah memberikan akses berkelajuan tinggi kepada kira-kira 30 syarikat. Kebanyakan mereka adalah syarikat LLM, dan beberapa adalah broker data, yang akan menjual semula koleksi kami. Kebanyakan adalah dari China, walaupun kami juga telah bekerjasama dengan syarikat dari AS, Eropah, Rusia, Korea Selatan, dan Jepun. DeepSeek <a %(arxiv)s>mengakui</a> bahawa versi terdahulu dilatih pada sebahagian daripada koleksi kami, walaupun mereka berahsia tentang model terbaru mereka (mungkin juga dilatih pada data kami). Jika Barat ingin kekal di hadapan dalam perlumbaan LLM, dan akhirnya, AGI, ia perlu mempertimbangkan semula kedudukannya mengenai hak cipta, dan segera. Sama ada anda bersetuju dengan kami atau tidak mengenai kes moral kami, ini kini menjadi kes ekonomi, dan juga keselamatan negara. Semua blok kuasa sedang membina super-saintis buatan, super-penggodam, dan super-tentera. Kebebasan maklumat menjadi perkara survival bagi negara-negara ini — malah menjadi perkara keselamatan negara. Pasukan kami berasal dari seluruh dunia, dan kami tidak mempunyai penjajaran tertentu. Tetapi kami menggalakkan negara-negara dengan undang-undang hak cipta yang kuat untuk menggunakan ancaman eksistensial ini untuk mereformasinya. Jadi apa yang perlu dilakukan? Cadangan pertama kami adalah mudah: memendekkan tempoh hak cipta. Di AS, hak cipta diberikan selama 70 tahun selepas kematian pengarang. Ini tidak masuk akal. Kita boleh menyelaraskannya dengan paten, yang diberikan selama 20 tahun selepas pemfailan. Ini sepatutnya lebih daripada cukup masa untuk pengarang buku, kertas, muzik, seni, dan karya kreatif lain, untuk mendapat pampasan sepenuhnya untuk usaha mereka (termasuk projek jangka panjang seperti adaptasi filem). Kemudian, sekurang-kurangnya, pembuat dasar harus memasukkan pengecualian untuk pemeliharaan dan penyebaran teks secara besar-besaran. Jika kehilangan pendapatan daripada pelanggan individu adalah kebimbangan utama, pengedaran pada tahap peribadi boleh kekal dilarang. Sebaliknya, mereka yang mampu mengurus repositori besar — syarikat yang melatih LLM, bersama dengan perpustakaan dan arkib lain — akan dilindungi oleh pengecualian ini. Pembaharuan hak cipta diperlukan untuk keselamatan negara TL;DR: LLM Cina (termasuk DeepSeek) dilatih pada arkib buku dan kertas haram saya — yang terbesar di dunia. Barat perlu merombak undang-undang hak cipta sebagai perkara keselamatan negara. Sila lihat <a %(all_isbns)s>catatan blog asal</a> untuk maklumat lanjut. Kami mengeluarkan cabaran untuk memperbaiki ini. Kami akan memberikan ganjaran tempat pertama sebanyak $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Disebabkan sambutan yang luar biasa dan penyertaan yang hebat, kami telah memutuskan untuk meningkatkan sedikit jumlah hadiah, dan memberikan empat ganjaran tempat ketiga sebanyak $500 setiap satu. Pemenangnya adalah di bawah, tetapi pastikan untuk melihat semua penyertaan <a %(annas_archive)s>di sini</a>, atau muat turun <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Tempat pertama $6,000: phiresky <a %(phiresky_github)s>Penyertaan</a> ini (<a %(annas_archive_note_2951)s>komen Gitlab</a>) adalah segala yang kami inginkan, dan lebih lagi! Kami terutamanya menyukai pilihan visualisasi yang sangat fleksibel (malah menyokong shader tersuai), tetapi dengan senarai pratetap yang komprehensif. Kami juga menyukai betapa pantas dan lancarnya semuanya, pelaksanaan yang mudah (yang bahkan tidak mempunyai backend), minimap yang bijak, dan penjelasan yang luas dalam <a %(phiresky_github)s>catatan blog</a> mereka. Kerja yang luar biasa, dan pemenang yang sangat layak! - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Hati kami penuh dengan rasa syukur. Idea yang menonjol Bangunan pencakar langit untuk kelangkaan Banyak peluncur untuk membandingkan datasets, seolah-olah anda seorang DJ. Bar skala dengan bilangan buku. Label yang cantik. Skema warna lalai yang menarik dan peta haba. Pandangan peta unik dan penapis Anotasi, dan juga statistik langsung Statistik langsung Beberapa idea dan pelaksanaan lain yang kami sangat suka: Kami boleh teruskan untuk seketika, tetapi mari berhenti di sini. Pastikan untuk melihat semua penyerahan <a %(annas_archive)s>di sini</a>, atau muat turun <a %(a_2025_01_isbn_visualization_files)s>torrent gabungan kami</a>. Begitu banyak penyerahan, dan setiap satu membawa perspektif unik, sama ada dalam UI atau pelaksanaan. Kami sekurang-kurangnya akan menggabungkan penyerahan tempat pertama ke dalam laman web utama kami, dan mungkin beberapa yang lain. Kami juga telah mula memikirkan cara untuk mengatur proses mengenal pasti, mengesahkan, dan kemudian mengarkibkan buku-buku yang paling jarang. Lebih banyak akan datang dalam hal ini. Terima kasih kepada semua yang telah mengambil bahagian. Sangat menakjubkan bahawa begitu ramai orang peduli. Penukaran datasets yang mudah untuk perbandingan cepat. Semua ISBN SSNO CADAL Kebocoran data CERLALC SSID DuXiu Indeks eBook EBSCOhost Google Books Goodreads Internet Archive ISBNdb Daftar Penerbit Global ISBN Libby Fail dalam Arkib Anna Nexus/STC OCLC/Worldcat OpenLibrary Perpustakaan Negara Rusia Perpustakaan Imperial Trantor Tempat kedua $3,000: hypha “Walaupun segi empat sama dan segi empat tepat adalah menyenangkan secara matematik, mereka tidak memberikan keunggulan dalam konteks pemetaan. Saya percaya asimetri yang wujud dalam Hilbert atau Morton klasik ini bukanlah kelemahan tetapi satu ciri. Seperti garis besar berbentuk but yang terkenal di Itali menjadikannya mudah dikenali pada peta, "keanehan" unik lengkung ini mungkin berfungsi sebagai penanda kognitif. Keunikan ini dapat meningkatkan ingatan spatial dan membantu pengguna mengorientasikan diri mereka, berpotensi memudahkan pencarian kawasan tertentu atau melihat corak.” Satu lagi <a %(annas_archive_note_2913)s>penyertaan</a> yang luar biasa. Tidak sefleksibel tempat pertama, tetapi kami sebenarnya lebih menyukai visualisasi peringkat makro berbanding tempat pertama (lengkung pengisian ruang, sempadan, pelabelan, penyorotan, panning, dan zooming). Satu <a %(annas_archive_note_2971)s>komen</a> oleh Joe Davis yang menarik perhatian kami: Dan masih banyak pilihan untuk visualisasi dan rendering, serta UI yang sangat lancar dan intuitif. Tempat kedua yang kukuh! - Anna dan pasukan (<a %(reddit)s>Reddit</a>) Beberapa bulan yang lalu kami mengumumkan <a %(all_isbns)s>ganjaran $10,000</a> untuk membuat visualisasi terbaik data kami yang menunjukkan ruang ISBN. Kami menekankan untuk menunjukkan fail mana yang telah/ belum kami arkibkan, dan kemudian kami menambah set data yang menerangkan berapa banyak perpustakaan yang menyimpan ISBN (ukuran kelangkaan). Kami sangat terharu dengan sambutan yang diterima. Terdapat begitu banyak kreativiti. Terima kasih yang besar kepada semua yang telah mengambil bahagian: tenaga dan semangat anda sangat menular! Akhirnya, kami ingin menjawab soalan-soalan berikut: <strong>buku mana yang wujud di dunia, berapa banyak yang telah kami arkibkan, dan buku mana yang harus kami fokuskan seterusnya?</strong> Sangat bagus melihat begitu ramai orang mengambil berat tentang soalan-soalan ini. Kami memulakan dengan visualisasi asas kami sendiri. Dalam kurang daripada 300kb, gambar ini secara ringkas mewakili "senarai buku" terbesar yang sepenuhnya terbuka yang pernah dikumpulkan dalam sejarah manusia: Tempat ketiga $500 #1: maxlion Dalam <a %(annas_archive_note_2940)s>penyertaan</a> ini, kami sangat menyukai pelbagai jenis pandangan, terutamanya pandangan perbandingan dan penerbit. Tempat ketiga $500 #2: abetusk Walaupun bukan UI yang paling kemas, <a %(annas_archive_note_2917)s>penyertaan</a> ini memenuhi banyak kriteria. Kami terutamanya menyukai ciri perbandingannya. Tempat ketiga $500 #3: conundrumer0 Seperti tempat pertama, <a %(annas_archive_note_2975)s>penyertaan</a> ini memukau kami dengan fleksibilitinya. Akhirnya, inilah yang menjadikan alat visualisasi hebat: fleksibiliti maksimum untuk pengguna berkuasa, sambil mengekalkan kesederhanaan untuk pengguna biasa. Tempat ketiga $500 #4: charelf Penyertaan <a %(annas_archive_note_2947)s>akhir</a> yang mendapat ganjaran adalah agak asas, tetapi mempunyai beberapa ciri unik yang kami sangat suka. Kami suka bagaimana mereka menunjukkan berapa banyak datasets yang merangkumi ISBN tertentu sebagai ukuran populariti/kebolehpercayaan. Kami juga sangat menyukai kesederhanaan tetapi keberkesanan menggunakan peluncur kelegapan untuk perbandingan. Pemenang hadiah visualisasi ISBN $10,000 TL;DR: Kami menerima beberapa penyertaan yang luar biasa untuk hadiah visualisasi ISBN $10,000. Latar Belakang Bagaimana Arkib Anna dapat mencapai misinya untuk menyandarkan semua pengetahuan manusia, tanpa mengetahui buku mana yang masih ada di luar sana? Kami memerlukan senarai TODO. Salah satu cara untuk memetakan ini adalah melalui nombor ISBN, yang sejak tahun 1970-an telah diberikan kepada setiap buku yang diterbitkan (di kebanyakan negara). Tiada pihak berkuasa pusat yang mengetahui semua penugasan ISBN. Sebaliknya, ia adalah sistem teragih, di mana negara-negara mendapat julat nombor, yang kemudian memberikan julat yang lebih kecil kepada penerbit utama, yang mungkin membahagikan lagi julat kepada penerbit kecil. Akhirnya nombor individu diberikan kepada buku. Kami mula memetakan ISBN <a %(blog)s>dua tahun lalu</a> dengan pengikisan kami dari ISBNdb. Sejak itu, kami telah mengikis banyak lagi sumber metadata, seperti <a %(blog_2)s>Worldcat</a>, Google Books, Goodreads, Libby, dan banyak lagi. Senarai penuh boleh didapati di halaman "Datasets" dan "Torrents" di Arkib Anna. Kami kini mempunyai koleksi metadata buku yang terbuka sepenuhnya dan mudah dimuat turun terbesar di dunia (dan dengan itu ISBN). Kami telah <a %(blog)s>menulis secara meluas</a> tentang mengapa kami peduli tentang pemeliharaan, dan mengapa kami kini berada dalam jendela kritikal. Kami mesti sekarang mengenal pasti buku-buku yang jarang, kurang diberi tumpuan, dan unik berisiko dan memeliharanya. Mempunyai metadata yang baik pada semua buku di dunia membantu dengan itu. Ganjaran $10,000 Pertimbangan kuat akan diberikan kepada kebolehgunaan dan bagaimana ia kelihatan. Tunjukkan metadata sebenar untuk ISBN individu apabila diperbesar, seperti tajuk dan pengarang. Lengkung pengisian ruang yang lebih baik. Contohnya, zig-zag, dari 0 ke 4 pada baris pertama dan kemudian kembali (secara terbalik) dari 5 ke 9 pada baris kedua — diterapkan secara rekursif. Skema warna yang berbeza atau boleh disesuaikan. Pandangan khas untuk membandingkan Datasets. Cara untuk menyelesaikan masalah, seperti metadata lain yang tidak bersetuju dengan baik (contohnya tajuk yang sangat berbeza). Menandakan imej dengan komen pada ISBN atau julat. Sebarang heuristik untuk mengenal pasti buku yang jarang atau berisiko. Apa sahaja idea kreatif yang anda boleh fikirkan! Kod Kod untuk menjana imej-imej ini, serta contoh-contoh lain, boleh didapati dalam <a %(annas_archive)s>direktori ini</a>. Kami telah mencipta format data yang padat, di mana semua maklumat ISBN yang diperlukan adalah sekitar 75MB (dimampatkan). Penerangan format data dan kod untuk menjana ia boleh didapati <a %(annas_archive_l1244_1319)s>di sini</a>. Untuk ganjaran ini, anda tidak diwajibkan menggunakan ini, tetapi ia mungkin format yang paling mudah untuk bermula. Anda boleh mengubah metadata kami mengikut kehendak anda (walaupun semua kod anda perlu sumber terbuka). Kami tidak sabar untuk melihat apa yang anda hasilkan. Semoga berjaya! Fork repo ini, dan edit HTML pos blog ini (tiada backend lain selain backend Flask kami dibenarkan). Jadikan gambar di atas boleh dizum dengan lancar, supaya anda boleh zum sehingga ke ISBN individu. Mengklik ISBN sepatutnya membawa anda ke halaman metadata atau carian di Arkib Anna. Anda mesti masih boleh bertukar antara semua Datasets yang berbeza. Julat negara dan julat penerbit harus diserlahkan apabila dihover. Anda boleh menggunakan contohnya <a %(github_xlcnd_isbnlib)s>data4info.py dalam isbnlib</a> untuk maklumat negara, dan "isbngrp" scrape kami untuk penerbit (<a %(annas_archive)s>dataset</a>, <a %(annas_archive_2)s>torrent</a>). Ia mesti berfungsi dengan baik di desktop dan mudah alih. Terdapat banyak yang boleh diterokai di sini, jadi kami mengumumkan ganjaran untuk memperbaiki visualisasi di atas. Tidak seperti kebanyakan ganjaran kami, yang ini terikat masa. Anda perlu <a %(annas_archive)s>menghantar</a> kod sumber terbuka anda sebelum 2025-01-31 (23:59 UTC). Penyerahan terbaik akan mendapat $6,000, tempat kedua $3,000, dan tempat ketiga $1,000. Semua ganjaran akan diberikan menggunakan Monero (XMR). Di bawah adalah kriteria minimum. Jika tiada penyerahan memenuhi kriteria, kami mungkin masih memberikan beberapa ganjaran, tetapi itu akan mengikut budi bicara kami. Untuk mata bonus (ini hanyalah idea — biarkan kreativiti anda berkembang): Anda BOLEH sepenuhnya menyimpang dari kriteria minimum, dan melakukan visualisasi yang sama sekali berbeza. Jika ia benar-benar spektakuler, maka itu layak untuk ganjaran, tetapi atas budi bicara kami. Buat penyerahan dengan menyiarkan komen kepada <a %(annas_archive)s>isu ini</a> dengan pautan ke repo bercabang anda, permintaan gabungan, atau perbezaan. - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Gambar ini berukuran 1000×800 piksel. Setiap piksel mewakili 2,500 ISBN. Jika kami mempunyai fail untuk ISBN, kami menjadikan piksel itu lebih hijau. Jika kami tahu ISBN telah dikeluarkan, tetapi kami tidak mempunyai fail yang sepadan, kami menjadikannya lebih merah. Dalam kurang daripada 300kb, gambar ini secara ringkas mewakili "senarai buku" terbuka sepenuhnya terbesar yang pernah dikumpulkan dalam sejarah manusia (beberapa ratus GB dimampatkan sepenuhnya). Ia juga menunjukkan: masih banyak kerja yang perlu dilakukan dalam menyandarkan buku (kami hanya mempunyai 16%). Memvisualisasikan Semua ISBN — ganjaran $10,000 menjelang 2025-01-31 Gambar ini mewakili "senarai buku" terbuka sepenuhnya terbesar yang pernah dikumpulkan dalam sejarah manusia. Memvisualisasikan Selain daripada imej gambaran keseluruhan, kami juga boleh melihat datasets individu yang telah kami perolehi. Gunakan dropdown dan butang untuk beralih antara mereka. Terdapat banyak corak menarik untuk dilihat dalam gambar-gambar ini. Mengapa terdapat beberapa keteraturan garis dan blok, yang nampaknya berlaku pada skala yang berbeza? Apakah kawasan kosong itu? Mengapa sesetengah datasets begitu berkelompok? Kami akan meninggalkan soalan-soalan ini sebagai latihan untuk pembaca. - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Kesimpulan Dengan standard ini, kita boleh membuat pelepasan lebih secara beransur-ansur, dan lebih mudah menambah sumber data baru. Kami sudah mempunyai beberapa pelepasan menarik dalam perancangan! Kami juga berharap ia menjadi lebih mudah untuk perpustakaan bayangan lain untuk mencerminkan koleksi kami. Lagipun, matlamat kami adalah untuk memelihara pengetahuan dan budaya manusia selama-lamanya, jadi lebih banyak redundansi lebih baik. Contoh Mari kita lihat keluaran Z-Library terbaru kami sebagai contoh. Ia terdiri daripada dua koleksi: “<span style="background: #fffaa3">zlib3_records</span>” dan “<span style="background: #ffd6fe">zlib3_files</span>”. Ini membolehkan kami mengikis dan mengeluarkan rekod metadata secara berasingan daripada fail buku sebenar. Oleh itu, kami mengeluarkan dua torrent dengan fail metadata: Kami juga mengeluarkan beberapa torrent dengan folder data binari, tetapi hanya untuk koleksi “<span style="background: #ffd6fe">zlib3_files</span>”, sebanyak 62 kesemuanya: Dengan menjalankan <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #fffaa3">zlib3_records</span>__20230808T014342Z--20230808T023702Z.jsonl.zst</span></code> kita dapat melihat apa yang ada di dalamnya: Dalam kes ini, ia adalah metadata sebuah buku seperti yang dilaporkan oleh Z-Library. Pada peringkat atas, kami hanya mempunyai “aacid” dan “metadata”, tetapi tiada “data_folder”, kerana tiada data binari yang sepadan. AACID mengandungi “22430000” sebagai ID utama, yang kita dapat lihat diambil daripada “zlibrary_id”. Kita boleh menjangkakan AAC lain dalam koleksi ini mempunyai struktur yang sama. Sekarang mari kita jalankan <code>zstdcat <span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230809T223215Z.jsonl.zst</span></code>: Ini adalah metadata AAC yang jauh lebih kecil, walaupun sebahagian besar AAC ini terletak di tempat lain dalam fail binari! Lagipun, kami mempunyai “data_folder” kali ini, jadi kami boleh menjangkakan data binari yang sepadan terletak di <code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z--20230808T051504Z</span>/<span style="color: #0093ff">aacid__<span style="background: #ffd6fe">zlib3_files</span>__20230808T051503Z__22433983__NRgUGwTJYJpkQjTbz2jA3M</span></code>. “Metadata” mengandungi “zlibrary_id”, jadi kita boleh dengan mudah mengaitkannya dengan AAC yang sepadan dalam koleksi “zlib_records”. Kami boleh mengaitkan dengan beberapa cara yang berbeza, contohnya melalui AACID — standard tidak menetapkan itu. Perhatikan bahawa ia juga tidak perlu untuk medan “metadata” itu sendiri menjadi JSON. Ia boleh menjadi rentetan yang mengandungi XML atau mana-mana format data lain. Anda juga boleh menyimpan maklumat metadata dalam blob binari yang berkaitan, contohnya jika ia adalah banyak data. Fail dan metadata yang heterogen, sedekat mungkin dengan format asal. Data binari boleh disajikan secara langsung oleh pelayan web seperti Nginx. Pengenal pasti yang heterogen dalam perpustakaan sumber, atau bahkan kekurangan pengenal pasti. Pelepasan berasingan metadata berbanding data fail, atau pelepasan metadata sahaja (contohnya pelepasan ISBNdb kami). Pengedaran melalui torrent, walaupun dengan kemungkinan kaedah pengedaran lain (contohnya IPFS). Rekod yang tidak boleh diubah, kerana kami harus menganggap torrent kami akan hidup selama-lamanya. Pelepasan beransur-ansur / pelepasan yang boleh ditambah. Boleh dibaca dan ditulis oleh mesin, dengan mudah dan cepat, terutamanya untuk tumpukan kami (Python, MySQL, ElasticSearch, Transmission, Debian, ext4). Pemeriksaan manusia yang agak mudah, walaupun ini adalah sekunder kepada kebolehbacaan mesin. Mudah untuk menyemai koleksi kami dengan seedbox sewaan standard. Matlamat reka bentuk Kami tidak peduli tentang fail yang mudah dinavigasi secara manual pada cakera, atau boleh dicari tanpa pra-pemprosesan. Kami tidak peduli tentang keserasian langsung dengan perisian perpustakaan yang sedia ada. Walaupun sepatutnya mudah bagi sesiapa sahaja untuk menyemai koleksi kami menggunakan torrent, kami tidak menjangkakan fail-fail tersebut boleh digunakan tanpa pengetahuan teknikal yang signifikan dan komitmen. Kes penggunaan utama kami adalah pengedaran fail dan metadata yang berkaitan dari pelbagai koleksi sedia ada. Pertimbangan terpenting kami adalah: Beberapa matlamat bukan: Oleh kerana Arkib Anna adalah sumber terbuka, kami ingin menggunakan format kami secara langsung. Apabila kami menyegarkan indeks carian kami, kami hanya mengakses laluan yang tersedia secara umum, supaya sesiapa yang menyalin perpustakaan kami boleh memulakannya dengan cepat. <strong>AAC.</strong> AAC (Bekas Arkib Anna) adalah satu item yang terdiri daripada <strong>metadata</strong>, dan secara opsional <strong>data binari</strong>, kedua-duanya tidak boleh diubah. Ia mempunyai pengecam unik global, yang dipanggil <strong>AACID</strong>. <strong>AACID.</strong> Format AACID adalah seperti ini: <code style="color: #0093ff">aacid__{koleksi}__{cap masa ISO 8601}__{ID khusus koleksi}__{shortuuid}</code>. Sebagai contoh, AACID sebenar yang kami lepaskan adalah <code style="color: #0093ff">aacid__zlib3_records__20230808T014342Z__22433983__URsJNGy5CjokTsNT6hUmmj</code>. <strong>Julat AACID.</strong> Oleh kerana AACID mengandungi cap masa yang meningkat secara monoton, kita boleh menggunakannya untuk menandakan julat dalam koleksi tertentu. Kami menggunakan format ini: <code style="color: blue">aacid__{koleksi}__{dari_cap_masa}--{ke_cap_masa}</code>, di mana cap masa adalah inklusif. Julat adalah berterusan, dan mungkin bertindih, tetapi dalam kes pertindihan mesti mengandungi rekod yang sama seperti yang dikeluarkan sebelumnya dalam koleksi itu (kerana AAC adalah tidak boleh ubah). Rekod yang hilang tidak dibenarkan. <code>{koleksi}</code>: nama koleksi, yang mungkin mengandungi huruf ASCII, nombor, dan garis bawah (tetapi tiada garis bawah berganda). <code>{ID khusus koleksi}</code>: pengenal pasti khusus koleksi, jika berkenaan, contohnya ID Z-Library. Mungkin diabaikan atau dipendekkan. Mesti diabaikan atau dipendekkan jika AACID sebaliknya melebihi 150 aksara. <code>{ISO 8601 timestamp}</code>: versi pendek ISO 8601, sentiasa dalam UTC, contohnya <code>20220723T194746Z</code>. Nombor ini perlu meningkat secara monoton untuk setiap keluaran, walaupun semantik tepatnya boleh berbeza bagi setiap koleksi. Kami mencadangkan menggunakan masa pengikisan atau penjanaan ID. <code>{shortuuid}</code>: UUID tetapi dimampatkan kepada ASCII, contohnya menggunakan base57. Kami kini menggunakan perpustakaan Python <a %(github_skorokithakis_shortuuid)s>shortuuid</a>. <strong>Folder data binari.</strong> Sebuah folder dengan data binari bagi satu julat AAC, untuk satu koleksi tertentu. Ini mempunyai sifat-sifat berikut: Direktori mesti mengandungi fail data untuk semua AAC dalam julat yang ditentukan. Setiap fail data mesti mempunyai AACID sebagai nama failnya (tanpa sambungan). Nama direktori mesti merupakan julat AACID, diawali dengan <code style="color: green">annas_archive_data__</code>, dan tiada akhiran. Sebagai contoh, salah satu keluaran sebenar kami mempunyai direktori yang dipanggil<br><code><span style="color: green">annas_archive_data__</span><span style="color: blue">aacid__zlib3_files__20230808T055130Z--20230808T055131Z</span></code>. Adalah disyorkan untuk membuat folder ini agak mudah diurus dari segi saiz, contohnya tidak lebih besar daripada 100GB-1TB setiap satu, walaupun cadangan ini mungkin berubah dari masa ke masa. <strong>Koleksi.</strong> Setiap AAC tergolong dalam satu koleksi, yang mengikut definisi adalah senarai AAC yang konsisten secara semantik. Ini bermakna jika anda membuat perubahan yang signifikan pada format metadata, maka anda perlu mencipta koleksi baru. Piawaian <strong>Fail metadata.</strong> Fail metadata mengandungi metadata julat AAC, untuk satu koleksi tertentu. Ini mempunyai sifat berikut: <code>data_folder</code> adalah pilihan, dan merupakan nama folder data binari yang mengandungi data binari yang sepadan. Nama fail data binari yang sepadan dalam folder itu adalah AACID rekod tersebut. Setiap objek JSON mesti mengandungi medan berikut di peringkat atas: <strong>aacid</strong>, <strong>metadata</strong>, <strong>data_folder</strong> (pilihan). Tiada medan lain dibenarkan. Nama fail mesti menjadi julat AACID, didahului dengan <code style="color: red">annas_archive_meta__</code> dan diikuti oleh <code>.jsonl.zstd</code>. Sebagai contoh, salah satu keluaran kami dipanggil<br><code><span style="color: red">annas_archive_meta__</span><span style="color: blue">aacid__zlib3_records__20230808T014342Z--20230808T023702Z</span>.jsonl.zst</code>. Seperti yang ditunjukkan oleh sambungan fail, jenis fail adalah <a %(jsonlines)s>JSON Lines</a> yang dimampatkan dengan <a %(zstd)s>Zstandard</a>. <code>metadata</code> adalah metadata sewenang-wenangnya, mengikut semantik koleksi. Ia mesti konsisten secara semantik dalam koleksi tersebut. Awalan <code style="color: red">annas_archive_meta__</code> boleh disesuaikan dengan nama institusi anda, contohnya <code style="color: red">my_institute_meta__</code>. <strong>Koleksi “rekod” dan “fail”.</strong> Secara konvensyen, sering kali lebih mudah untuk melepaskan “rekod” dan “fail” sebagai koleksi yang berbeza, supaya ia boleh dilepaskan pada jadual yang berbeza, contohnya berdasarkan kadar pengikisan. “Rekod” adalah koleksi metadata sahaja, yang mengandungi maklumat seperti tajuk buku, pengarang, ISBN, dan sebagainya, manakala “fail” adalah koleksi yang mengandungi fail sebenar itu sendiri (pdf, epub). Akhirnya, kami memutuskan untuk menggunakan piawaian yang agak mudah. Ia agak longgar, tidak normatif, dan sedang dalam proses. <strong>Torrent.</strong> Fail metadata dan folder data binari boleh dibundel dalam torrent, dengan satu torrent bagi setiap fail metadata, atau satu torrent bagi setiap folder data binari. Torrent mesti mempunyai nama fail/direktori asal ditambah dengan akhiran <code>.torrent</code> sebagai nama failnya. <a %(wikipedia_annas_archive)s>Arkib Anna</a> telah menjadi perpustakaan bayangan terbesar di dunia, dan satu-satunya perpustakaan bayangan pada skala ini yang sepenuhnya sumber terbuka dan data terbuka. Di bawah adalah jadual dari halaman Datasets kami (sedikit diubah suai): Kami mencapai ini dengan tiga cara: Mencerminkan perpustakaan bayangan data terbuka yang sedia ada (seperti Sci-Hub dan Library Genesis). Membantu perpustakaan bayangan yang ingin lebih terbuka, tetapi tidak mempunyai masa atau sumber untuk melakukannya (seperti koleksi komik Libgen). Mengikis perpustakaan yang tidak ingin berkongsi secara besar-besaran (seperti Z-Library). Untuk (2) dan (3) kami kini menguruskan koleksi torrent yang besar sendiri (ratusan TB). Setakat ini kami mendekati koleksi ini sebagai satu-satu, bermakna infrastruktur dan organisasi data yang khusus untuk setiap koleksi. Ini menambah beban yang ketara kepada setiap pelepasan, dan menjadikannya sangat sukar untuk melakukan pelepasan yang lebih beransur-ansur. Itulah sebabnya kami memutuskan untuk menyeragamkan pelepasan kami. Ini adalah catatan blog teknikal di mana kami memperkenalkan piawaian kami: <strong>Kontena Arkib Anna</strong>. Kontena Arkib Anna (AAC): menyeragamkan keluaran dari perpustakaan bayangan terbesar di dunia Arkib Anna telah menjadi perpustakaan bayangan terbesar di dunia, memerlukan kami untuk menyeragamkan keluaran kami. 300GB+ kulit buku dikeluarkan Akhirnya, kami gembira untuk mengumumkan pelepasan kecil. Dalam kerjasama dengan rakan-rakan yang mengendalikan cabang Libgen.rs, kami berkongsi semua kulit buku mereka melalui torrents dan IPFS. Ini akan mengagihkan beban melihat kulit buku di antara lebih banyak mesin, dan akan memelihara mereka dengan lebih baik. Dalam banyak (tetapi tidak semua) kes, kulit buku disertakan dalam fail itu sendiri, jadi ini adalah sejenis "data terbitan". Tetapi memilikinya dalam IPFS masih sangat berguna untuk operasi harian kedua-dua Arkib Anna dan pelbagai cabang Library Genesis. Seperti biasa, anda boleh menemui pelepasan ini di Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>). Kami tidak akan pautkan di sini, tetapi anda boleh menemuinya dengan mudah. Mudah-mudahan kita dapat melonggarkan sedikit rentak kita, sekarang kita mempunyai alternatif yang baik kepada Z-Library. Beban kerja ini tidak begitu mampan. Jika anda berminat untuk membantu dalam pengaturcaraan, operasi pelayan, atau kerja pemeliharaan, sila hubungi kami. Masih banyak <a %(annas_archive)s>kerja yang perlu dilakukan</a>. Terima kasih atas minat dan sokongan anda. Beralih ke ElasticSearch Beberapa pertanyaan mengambil masa yang sangat lama, sehingga mereka akan menguasai semua sambungan terbuka. Secara lalai MySQL mempunyai panjang perkataan minimum, atau indeks anda boleh menjadi sangat besar. Orang melaporkan tidak dapat mencari "Ben Hur". Carian hanya agak pantas apabila dimuat sepenuhnya dalam memori, yang memerlukan kami mendapatkan mesin yang lebih mahal untuk menjalankannya, ditambah beberapa arahan untuk memuatkan indeks pada permulaan. Kami tidak akan dapat memperluaskannya dengan mudah untuk membina ciri baru, seperti <a %(wikipedia_cjk_characters)s>tokenisasi yang lebih baik untuk bahasa tanpa ruang putih</a>, penapisan/pemfokusan, penyusunan, cadangan "adakah anda maksudkan", pelengkapan automatik, dan sebagainya. Salah satu <a %(annas_archive)s>tiket</a> kami adalah sekumpulan isu dengan sistem carian kami. Kami menggunakan carian teks penuh MySQL, kerana kami mempunyai semua data kami dalam MySQL. Tetapi ia mempunyai hadnya: Selepas berbincang dengan beberapa pakar, kami memilih ElasticSearch. Ia tidak sempurna (cadangan "adakah anda maksudkan" dan ciri autolengkap mereka tidak bagus), tetapi secara keseluruhan ia jauh lebih baik daripada MySQL untuk carian. Kami masih tidak <a %(youtube)s>terlalu berminat</a> menggunakannya untuk sebarang data kritikal misi (walaupun mereka telah membuat banyak <a %(elastic_co)s>kemajuan</a>), tetapi secara keseluruhan kami agak gembira dengan pertukaran ini. Buat masa ini, kami telah melaksanakan carian yang lebih pantas, sokongan bahasa yang lebih baik, penyusunan relevansi yang lebih baik, pilihan penyusunan yang berbeza, dan penapisan pada jenis bahasa/buku/jenis fail. Jika anda ingin tahu bagaimana ia berfungsi, <a %(annas_archive_l140)s>lihatlah</a> <a %(annas_archive_l1115)s>di</a> <a %(annas_archive_l1635)s>sini</a>. Ia agak mudah diakses, walaupun ia boleh menggunakan lebih banyak komen… Arkib Anna adalah sepenuhnya sumber terbuka Kami percaya bahawa maklumat harus bebas, dan kod kami sendiri tidak terkecuali. Kami telah melepaskan semua kod kami di Gitlab yang dihoskan secara peribadi: <a %(annas_archive)s>Perisian Anna</a>. Kami juga menggunakan penjejak isu untuk mengatur kerja kami. Jika anda ingin terlibat dengan pembangunan kami, ini adalah tempat yang baik untuk bermula. Untuk memberi anda gambaran tentang perkara yang kami sedang usahakan, lihat kerja terbaru kami mengenai penambahbaikan prestasi sisi klien. Oleh kerana kami belum melaksanakan pemisahan halaman, kami sering mengembalikan halaman carian yang sangat panjang, dengan 100-200 hasil. Kami tidak mahu memotong hasil carian terlalu awal, tetapi ini bermakna ia akan memperlahankan beberapa peranti. Untuk ini, kami melaksanakan sedikit helah: kami membungkus kebanyakan hasil carian dalam komen HTML (<code><!-- --></code>), dan kemudian menulis sedikit Javascript yang akan mengesan bila hasil harus menjadi kelihatan, pada saat itu kami akan membuka komen tersebut: "Virtualisasi" DOM dilaksanakan dalam 23 baris, tidak perlu perpustakaan mewah! Ini adalah jenis kod pragmatik cepat yang anda dapatkan apabila anda mempunyai masa yang terhad, dan masalah sebenar yang perlu diselesaikan. Telah dilaporkan bahawa carian kami kini berfungsi dengan baik pada peranti yang perlahan! Satu lagi usaha besar adalah untuk mengautomatikkan pembinaan pangkalan data. Apabila kami dilancarkan, kami hanya mengumpulkan pelbagai sumber secara sembarangan. Sekarang kami ingin memastikan mereka dikemas kini, jadi kami menulis beberapa skrip untuk memuat turun metadata baru dari dua cabang Library Genesis, dan mengintegrasikannya. Matlamatnya adalah bukan sahaja menjadikan ini berguna untuk arkib kami, tetapi untuk memudahkan sesiapa yang ingin bermain-main dengan metadata perpustakaan bayangan. Matlamatnya adalah sebuah buku nota Jupyter yang mempunyai pelbagai metadata menarik yang tersedia, supaya kami dapat melakukan lebih banyak penyelidikan seperti mencari tahu <a %(blog)s>peratusan ISBN yang dipelihara selama-lamanya</a>. Akhirnya, kami memperbaharui sistem derma kami. Anda kini boleh menggunakan kad kredit untuk terus mendepositkan wang ke dalam dompet kripto kami, tanpa benar-benar perlu mengetahui apa-apa tentang mata wang kripto. Kami akan terus memantau sejauh mana ini berfungsi dalam praktik, tetapi ini adalah perkara besar. Dengan Z-Library ditutup dan pengasasnya (yang didakwa) ditangkap, kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arkib Anna (kami tidak akan pautkan di sini, tetapi anda boleh Google). Berikut adalah beberapa perkara yang telah kami capai baru-baru ini. Kemaskini Anna: arkib sumber terbuka sepenuhnya, ElasticSearch, 300GB+ kulit buku Kami telah bekerja tanpa henti untuk menyediakan alternatif yang baik dengan Arkib Anna. Berikut adalah beberapa perkara yang telah kami capai baru-baru ini. Analisis Pendua semantik (imbasan berbeza dari buku yang sama) secara teori boleh ditapis, tetapi ia rumit. Apabila melihat secara manual melalui komik, kami mendapati terlalu banyak positif palsu. Terdapat beberapa pendua semata-mata oleh MD5, yang agak membazir, tetapi menapisnya hanya akan memberikan kami kira-kira 1% in penjimatan. Pada skala ini, itu masih kira-kira 1TB, tetapi juga, pada skala ini 1TB tidak begitu penting. Kami lebih suka tidak mengambil risiko memusnahkan data secara tidak sengaja dalam proses ini. Kami menemui banyak data bukan buku, seperti filem berdasarkan buku komik. Itu juga nampaknya membazir, kerana ini sudah tersedia secara meluas melalui cara lain. Walau bagaimanapun, kami menyedari bahawa kami tidak boleh hanya menapis fail filem, kerana terdapat juga <em>buku komik interaktif</em> yang dikeluarkan di komputer, yang seseorang rakam dan simpan sebagai filem. Akhirnya, apa sahaja yang kami boleh padamkan dari koleksi hanya akan menjimatkan beberapa peratus sahaja. Kemudian kami teringat bahawa kami adalah pengumpul data, dan orang yang akan mencerminkan ini juga adalah pengumpul data, jadi, “APA MAKSUD ANDA, PADAM?!” :) Apabila anda mendapat 95TB dimasukkan ke dalam kluster storan anda, anda cuba memahami apa yang ada di dalamnya… Kami melakukan beberapa analisis untuk melihat sama ada kami boleh mengurangkan saiznya sedikit, seperti dengan membuang pendua. Berikut adalah beberapa penemuan kami: Oleh itu, kami mempersembahkan kepada anda, koleksi penuh yang tidak diubah suai. Ia adalah banyak data, tetapi kami berharap cukup ramai orang akan peduli untuk menyemainya. Kerjasama Memandangkan saiznya, koleksi ini telah lama berada dalam senarai keinginan kami, jadi selepas kejayaan kami dengan membuat sandaran Z-Library, kami menetapkan pandangan kami pada koleksi ini. Pada mulanya kami mengikisnya secara langsung, yang merupakan cabaran besar, kerana pelayan mereka tidak dalam keadaan terbaik. Kami mendapat kira-kira 15TB dengan cara ini, tetapi ia berjalan dengan perlahan. Nasib baik, kami berjaya berhubung dengan pengendali perpustakaan, yang bersetuju untuk menghantar semua data kepada kami secara langsung, yang jauh lebih pantas. Ia masih mengambil masa lebih setengah tahun untuk memindahkan dan memproses semua data, dan kami hampir kehilangan semuanya akibat kerosakan cakera, yang akan bermakna memulakan semula. Pengalaman ini telah membuat kami percaya bahawa adalah penting untuk menyebarkan data ini secepat mungkin, supaya ia dapat dicerminkan secara meluas. Kami hanya satu atau dua insiden yang tidak bernasib baik daripada kehilangan koleksi ini selama-lamanya! Koleksi Bergerak pantas memang bermakna koleksi ini agak tidak teratur… Mari kita lihat. Bayangkan kita mempunyai sistem fail (yang sebenarnya kita pecahkan kepada torrent): Direktori pertama, <code>/repository</code>, adalah bahagian yang lebih teratur. Direktori ini mengandungi apa yang dipanggil “seribu dir”: direktori masing-masing dengan seribu fail, yang dinomborkan secara berturutan dalam pangkalan data. Direktori <code>0</code> mengandungi fail dengan comic_id 0–999, dan seterusnya. Ini adalah skema yang sama seperti yang digunakan oleh Library Genesis untuk koleksi fiksyen dan bukan fiksyennya. Idea ini adalah bahawa setiap “seribu dir” secara automatik akan ditukar menjadi torrent sebaik sahaja ia penuh. Walau bagaimanapun, pengendali Libgen.li tidak pernah membuat torrent untuk koleksi ini, dan oleh itu seribu dir mungkin menjadi tidak praktikal, dan memberi laluan kepada “dir tidak tersusun”. Ini adalah <code>/comics0</code> hingga <code>/comics4</code>. Kesemuanya mengandungi struktur direktori unik, yang mungkin masuk akal untuk mengumpul fail, tetapi tidak begitu masuk akal bagi kami sekarang. Nasib baik, metadata masih merujuk terus kepada semua fail ini, jadi organisasi penyimpanan mereka pada cakera sebenarnya tidak penting! Metadata tersedia dalam bentuk pangkalan data MySQL. Ini boleh dimuat turun terus dari laman web Libgen.li, tetapi kami juga akan menyediakannya dalam torrent, bersama dengan jadual kami sendiri dengan semua hash MD5. <q>Dr. Barbara Gordon cuba menghilangkan dirinya dalam dunia perpustakaan yang biasa…</q> Cabang Libgen Pertama, sedikit latar belakang. Anda mungkin mengenali Library Genesis untuk koleksi buku epik mereka. Lebih sedikit orang tahu bahawa sukarelawan Library Genesis telah mencipta projek lain, seperti koleksi majalah dan dokumen standard yang besar, sandaran penuh Sci-Hub (dalam kerjasama dengan pengasas Sci-Hub, Alexandra Elbakyan), dan sememangnya, koleksi komik yang besar. Pada satu ketika, pengendali cermin Library Genesis yang berbeza pergi ke arah masing-masing, yang menyebabkan situasi semasa mempunyai beberapa “fork” yang berbeza, semuanya masih membawa nama Library Genesis. Fork Libgen.li secara unik mempunyai koleksi komik ini, serta koleksi majalah yang besar (yang juga sedang kami usahakan). Pengumpulan Dana Kami melepaskan data ini dalam beberapa bahagian besar. Torrent pertama adalah <code>/comics0</code>, yang kami masukkan ke dalam satu fail .tar besar 12TB. Itu lebih baik untuk cakera keras dan perisian torrent anda daripada beribu-ribu fail yang lebih kecil. Sebagai sebahagian daripada pelepasan ini, kami mengadakan pengumpulan dana. Kami berusaha untuk mengumpul $20,000 untuk menampung kos operasi dan kontrak untuk koleksi ini, serta membolehkan projek berterusan dan masa depan. Kami mempunyai beberapa <em>besar</em> yang sedang dalam perancangan. <em>Siapa yang saya sokong dengan sumbangan saya?</em> Ringkasnya: kami menyokong semua pengetahuan dan budaya manusia, dan menjadikannya mudah diakses. Semua kod dan data kami adalah sumber terbuka, kami adalah projek yang dijalankan sepenuhnya oleh sukarelawan, dan kami telah menyelamatkan 125TB buku setakat ini (selain daripada torrent sedia ada Libgen dan Scihub). Akhirnya kami membina roda terbang yang membolehkan dan mendorong orang untuk mencari, mengimbas, dan menyandarkan semua buku di dunia. Kami akan menulis tentang rancangan induk kami dalam catatan masa depan. :) Jika anda menderma untuk keahlian “Amazing Archivist” selama 12 bulan ($780), anda boleh <strong>“mengadopsi torrent”</strong>, yang bermaksud kami akan meletakkan nama pengguna atau mesej anda dalam nama fail salah satu torrent! Anda boleh menderma dengan pergi ke <a %(wikipedia_annas_archive)s>Arkib Anna</a> dan mengklik butang “Derma”. Kami juga mencari lebih ramai sukarelawan: jurutera perisian, penyelidik keselamatan, pakar pedagang tanpa nama, dan penterjemah. Anda juga boleh menyokong kami dengan menyediakan perkhidmatan hosting. Dan sudah tentu, sila benihkan torrent kami! Terima kasih kepada semua yang telah menyokong kami dengan begitu murah hati! Anda benar-benar membuat perbezaan. Berikut adalah torrent yang telah dikeluarkan setakat ini (kami masih memproses yang lain): Semua torrent boleh didapati di <a %(wikipedia_annas_archive)s>Arkib Anna</a> di bawah “Datasets” (kami tidak memautkan terus ke sana, jadi pautan ke blog ini tidak dikeluarkan dari Reddit, Twitter, dll). Dari situ, ikuti pautan ke laman web Tor. <a %(news_ycombinator)s>Bincangkan di Hacker News</a> Apa yang seterusnya? Sekumpulan torrent adalah hebat untuk pemeliharaan jangka panjang, tetapi tidak begitu untuk akses harian. Kami akan bekerjasama dengan rakan hosting untuk mendapatkan semua data ini di web (kerana Arkib Anna tidak menjadi hos apa-apa secara langsung). Sudah tentu anda akan dapat mencari pautan muat turun ini di Arkib Anna. Kami juga menjemput semua orang untuk melakukan sesuatu dengan data ini! Bantu kami menganalisisnya dengan lebih baik, mendeduplikasikannya, meletakkannya di IPFS, mengadunkannya, melatih model AI anda dengannya, dan sebagainya. Ia semua milik anda, dan kami tidak sabar untuk melihat apa yang anda lakukan dengannya. Akhirnya, seperti yang dikatakan sebelum ini, kami masih mempunyai beberapa pelepasan besar yang akan datang (jika <em>seseorang</em> boleh <em>secara tidak sengaja</em> menghantar kami satu longgokan pangkalan data <em>ACS4 tertentu</em>, anda tahu di mana untuk mencari kami…), serta membina roda tenaga untuk menyandarkan semua buku di dunia. Jadi nantikan, kami baru sahaja bermula. - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Perpustakaan bayangan terbesar bagi buku komik mungkin adalah dari satu cabang Library Genesis: Libgen.li. Satu pentadbir yang menjalankan laman itu berjaya mengumpulkan koleksi komik yang luar biasa lebih daripada 2 juta fail, berjumlah lebih 95TB. Walau bagaimanapun, tidak seperti koleksi Library Genesis yang lain, yang ini tidak tersedia secara pukal melalui torrent. Anda hanya boleh mengakses komik ini secara individu melalui pelayan peribadinya yang perlahan — satu titik kegagalan. Sehingga hari ini! Dalam kiriman ini, kami akan memberitahu anda lebih lanjut tentang koleksi ini, dan tentang pengumpulan dana kami untuk menyokong lebih banyak kerja ini. Arkib Anna telah menyandarkan perpustakaan bayangan komik terbesar di dunia (95TB) — anda boleh membantu menyemai Perpustakaan bayangan buku komik terbesar di dunia mempunyai satu titik kegagalan.. sehingga hari ini. Amaran: catatan blog ini telah dihentikan. Kami telah memutuskan bahawa IPFS belum bersedia untuk digunakan secara meluas. Kami masih akan memautkan fail pada IPFS dari Arkib Anna apabila mungkin, tetapi kami tidak akan menjadi hosnya sendiri lagi, dan kami juga tidak mengesyorkan orang lain untuk mencerminkannya menggunakan IPFS. Sila lihat halaman Torrents kami jika anda ingin membantu memelihara koleksi kami. Meletakkan 5,998,794 buku di IPFS Penggandaan salinan Kembali kepada soalan asal kami: bagaimana kami boleh mendakwa untuk memelihara koleksi kami selama-lamanya? Masalah utama di sini adalah bahawa koleksi kami telah <a %(torrents_stats)s>berkembang</a> dengan pesat, dengan mengikis dan membuka sumber beberapa koleksi besar (di atas kerja menakjubkan yang sudah dilakukan oleh perpustakaan bayangan data terbuka lain seperti Sci-Hub dan Library Genesis). Pertumbuhan data ini menjadikannya lebih sukar untuk koleksi dicermin di seluruh dunia. Penyimpanan data mahal! Tetapi kami optimis, terutamanya apabila memerhatikan tiga trend berikut. <a %(annas_archive_stats)s>Saiz keseluruhan</a> koleksi kami, sepanjang beberapa bulan lepas, dipecahkan mengikut bilangan penanam torrent. Trend harga HDD dari pelbagai sumber (klik untuk melihat kajian). <a %(critical_window_chinese)s>Versi Cina 中文版</a>, bincangkan di <a %(reddit)s>Reddit</a>, <a %(news_ycombinator)s>Hacker News</a> 1. Kami telah memetik buah yang rendah Ini mengikuti secara langsung dari keutamaan kami yang dibincangkan di atas. Kami lebih suka bekerja untuk membebaskan koleksi besar terlebih dahulu. Sekarang bahawa kami telah mengamankan beberapa koleksi terbesar di dunia, kami menjangkakan pertumbuhan kami akan jauh lebih perlahan. Masih terdapat ekor panjang koleksi yang lebih kecil, dan buku baru diimbas atau diterbitkan setiap hari, tetapi kadar itu mungkin akan jauh lebih perlahan. Kami mungkin masih berganda atau bahkan tiga kali ganda dalam saiz, tetapi dalam tempoh masa yang lebih lama. Peningkatan OCR. Keutamaan Kod perisian sains & kejuruteraan Versi fiksyen atau hiburan bagi semua perkara di atas Data geografi (contohnya peta, kajian geologi) Data dalaman daripada korporat atau kerajaan (bocor) Data pengukuran seperti pengukuran saintifik, data ekonomi, laporan korporat Rekod metadata secara umum (bukan fiksyen dan fiksyen; media lain, seni, orang, dll; termasuk ulasan) Buku bukan fiksyen Majalah bukan fiksyen, surat khabar, manual Transkrip bukan fiksyen daripada ceramah, dokumentari, podcast Data organik seperti urutan DNA, biji tumbuhan, atau sampel mikrob Kertas akademik, jurnal, laporan Laman web sains & kejuruteraan, perbincangan dalam talian Transkrip prosiding undang-undang atau mahkamah Unik berisiko dimusnahkan (contohnya oleh perang, pemotongan dana, tuntutan mahkamah, atau penganiayaan politik) Jarang Unik tidak diberi tumpuan Mengapa kita begitu peduli tentang kertas dan buku? Mari kita ketepikan kepercayaan asas kita dalam pemeliharaan secara umum — kita mungkin menulis pos lain tentang itu. Jadi mengapa kertas dan buku secara khusus? Jawapannya mudah: <strong>ketumpatan maklumat</strong>. Setiap megabait storan, teks bertulis menyimpan maklumat paling banyak daripada semua media. Walaupun kita peduli tentang kedua-dua pengetahuan dan budaya, kita lebih peduli tentang yang pertama. Secara keseluruhan, kita mendapati hierarki ketumpatan maklumat dan kepentingan pemeliharaan yang kelihatan kira-kira seperti ini: Kedudukan dalam senarai ini agak sewenang-wenangnya — beberapa item adalah seri atau terdapat ketidaksetujuan dalam pasukan kami — dan kami mungkin terlupa beberapa kategori penting. Tetapi ini adalah secara kasar bagaimana kami memprioritaskan. Beberapa item ini terlalu berbeza daripada yang lain untuk kami bimbangkan (atau sudah diuruskan oleh institusi lain), seperti data organik atau data geografi. Tetapi kebanyakan item dalam senarai ini sebenarnya penting bagi kami. Faktor besar lain dalam pemprioritasan kami adalah sejauh mana risiko sesuatu karya. Kami lebih suka memberi tumpuan kepada karya yang: Akhirnya, kami mengambil berat tentang skala. Kami mempunyai masa dan wang yang terhad, jadi kami lebih suka menghabiskan sebulan menyelamatkan 10,000 buku daripada 1,000 buku — jika mereka sama-sama berharga dan berisiko. <em><q>Yang hilang tidak dapat dipulihkan; tetapi mari kita selamatkan apa yang tinggal: bukan dengan peti besi dan kunci yang menghalang mereka dari pandangan dan penggunaan awam, dengan menyerahkan mereka kepada pembaziran masa, tetapi dengan penggandaan salinan, yang akan meletakkan mereka di luar jangkauan kemalangan.</q></em><br>— Thomas Jefferson, 1791 Perpustakaan bayangan Kod boleh menjadi sumber terbuka di Github, tetapi Github secara keseluruhan tidak boleh dicermin dengan mudah dan dengan itu dipelihara (walaupun dalam kes ini terdapat salinan yang cukup diedarkan bagi kebanyakan repositori kod) Rekod metadata boleh dilihat secara bebas di laman web Worldcat, tetapi tidak boleh dimuat turun secara pukal (sehingga kami <a %(worldcat_scrape)s>mengikis</a> mereka) Reddit boleh digunakan secara percuma, tetapi baru-baru ini telah meletakkan langkah anti-pengikisan yang ketat, selepas kelaparan data untuk latihan LLM (lebih lanjut tentang itu kemudian) Terdapat banyak organisasi yang mempunyai misi dan keutamaan yang serupa. Memang, terdapat perpustakaan, arkib, makmal, muzium, dan institusi lain yang ditugaskan untuk pemeliharaan jenis ini. Banyak daripada mereka dibiayai dengan baik, oleh kerajaan, individu, atau syarikat. Tetapi mereka mempunyai satu titik buta besar: sistem undang-undang. Di sinilah terletaknya peranan unik perpustakaan bayangan, dan sebab Arkib Anna wujud. Kami boleh melakukan perkara yang tidak dibenarkan oleh institusi lain. Sekarang, bukan (selalunya) bahawa kami boleh mengarkibkan bahan yang haram untuk dipelihara di tempat lain. Tidak, adalah sah di banyak tempat untuk membina arkib dengan sebarang buku, kertas, majalah, dan sebagainya. Tetapi apa yang sering kurang dalam arkib sah adalah <strong>redundansi dan jangka hayat</strong>. Terdapat buku yang hanya satu salinan wujud di beberapa perpustakaan fizikal di suatu tempat. Terdapat rekod metadata yang dijaga oleh satu syarikat sahaja. Terdapat surat khabar yang hanya dipelihara pada mikrofilem dalam satu arkib. Perpustakaan boleh mengalami pemotongan dana, syarikat boleh muflis, arkib boleh dibom dan dibakar hingga musnah. Ini bukan hipotesis — ini berlaku sepanjang masa. Perkara yang kami boleh lakukan secara unik di Arkib Anna adalah menyimpan banyak salinan karya, pada skala besar. Kami boleh mengumpul kertas, buku, majalah, dan banyak lagi, dan mengedarkannya secara pukal. Kami kini melakukan ini melalui torrent, tetapi teknologi yang tepat tidak penting dan akan berubah dari masa ke masa. Bahagian penting adalah mendapatkan banyak salinan diedarkan di seluruh dunia. Petikan ini dari lebih 200 tahun yang lalu masih relevan: Nota ringkas tentang domain awam. Oleh kerana Arkib Anna secara unik menumpukan pada aktiviti yang menyalahi undang-undang di banyak tempat di seluruh dunia, kami tidak peduli dengan koleksi yang tersedia secara meluas, seperti buku domain awam. Entiti sah sering kali sudah menjaga hal itu dengan baik. Walau bagaimanapun, terdapat pertimbangan yang membuatkan kami kadang-kadang bekerja pada koleksi yang tersedia secara umum: - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) 2. Kos penyimpanan terus menurun secara eksponen 3. Peningkatan dalam ketumpatan maklumat Kami kini menyimpan buku dalam format mentah yang diberikan kepada kami. Memang, ia dimampatkan, tetapi selalunya ia masih imbasan besar atau gambar halaman. Sehingga kini, satu-satunya pilihan untuk mengecilkan saiz keseluruhan koleksi kami adalah melalui pemampatan yang lebih agresif, atau deduplikasi. Walau bagaimanapun, untuk mendapatkan penjimatan yang cukup besar, kedua-duanya terlalu banyak kehilangan untuk citarasa kami. Pemampatan berat foto boleh menjadikan teks hampir tidak boleh dibaca. Dan deduplikasi memerlukan keyakinan tinggi bahawa buku-buku adalah sama, yang selalunya terlalu tidak tepat, terutamanya jika kandungannya sama tetapi imbasan dibuat pada masa yang berbeza. Sentiasa ada pilihan ketiga, tetapi kualitinya sangat buruk sehingga kami tidak pernah mempertimbangkannya: <strong>OCR, atau Pengecaman Aksara Optik</strong>. Ini adalah proses menukar foto menjadi teks biasa, dengan menggunakan AI untuk mengesan aksara dalam foto. Alat untuk ini telah lama wujud, dan agak baik, tetapi "agak baik" tidak mencukupi untuk tujuan pemeliharaan. Walau bagaimanapun, model pembelajaran mendalam multi-modal baru-baru ini telah membuat kemajuan yang sangat pesat, walaupun masih pada kos yang tinggi. Kami menjangkakan ketepatan dan kos akan meningkat dengan ketara dalam tahun-tahun akan datang, sehingga menjadi realistik untuk diterapkan pada seluruh perpustakaan kami. Apabila itu berlaku, kami mungkin masih akan memelihara fail asal, tetapi sebagai tambahan kami boleh mempunyai versi perpustakaan yang lebih kecil yang kebanyakan orang akan mahu cermin. Yang menarik adalah bahawa teks mentah itu sendiri dimampatkan dengan lebih baik, dan lebih mudah untuk dideduplikasi, memberikan kami lebih banyak penjimatan. Secara keseluruhan, tidak mustahil untuk mengharapkan sekurang-kurangnya pengurangan 5-10x dalam saiz fail keseluruhan, mungkin lebih. Walaupun dengan pengurangan konservatif 5x, kita akan melihat <strong>$1,000–$3,000 dalam 10 tahun walaupun perpustakaan kita meningkat tiga kali ganda dalam saiz</strong>. Pada masa penulisan ini, <a %(diskprices)s>harga cakera</a> per TB adalah sekitar $12 untuk cakera baru, $8 untuk cakera terpakai, dan $4 untuk pita. Jika kita bersikap konservatif dan hanya melihat cakera baru, ini bermakna menyimpan satu petabait berharga sekitar $12,000. Jika kita anggarkan perpustakaan kita akan meningkat tiga kali ganda dari 900TB ke 2.7PB, ini bermakna $32,400 untuk mencermin seluruh perpustakaan kita. Menambah kos elektrik, kos perkakasan lain, dan sebagainya, mari kita bulatkan kepada $40,000. Atau dengan pita lebih kurang $15,000–$20,000. Di satu pihak <strong>$15,000–$40,000 untuk jumlah semua pengetahuan manusia adalah sangat murah</strong>. Di pihak lain, agak mahal untuk mengharapkan banyak salinan penuh, terutamanya jika kita juga ingin orang-orang tersebut terus menyemai torrent mereka untuk manfaat orang lain. Itu adalah hari ini. Tetapi kemajuan terus bergerak ke hadapan: Kos cakera keras per TB telah dikurangkan kira-kira satu pertiga dalam 10 tahun terakhir, dan mungkin akan terus menurun pada kadar yang sama. Pita nampaknya berada pada trajektori yang sama. Harga SSD menurun lebih cepat, dan mungkin akan mengatasi harga HDD menjelang akhir dekad ini. Jika ini berterusan, maka dalam 10 tahun kita mungkin hanya melihat $5,000–$13,000 untuk mencermin seluruh koleksi kita (1/3), atau lebih kurang jika kita berkembang kurang dalam saiz. Walaupun masih banyak wang, ini akan dapat dicapai oleh ramai orang. Dan mungkin lebih baik lagi kerana perkara seterusnya… Di Arkib Anna, kami sering ditanya bagaimana kami boleh mendakwa untuk memelihara koleksi kami selama-lamanya, apabila saiz keseluruhan sudah menghampiri 1 Petabyte (1000 TB), dan masih berkembang. Dalam artikel ini, kami akan melihat falsafah kami, dan melihat mengapa dekad seterusnya adalah kritikal untuk misi kami memelihara pengetahuan dan budaya manusia. Tingkap kritikal Jika ramalan ini tepat, kita <strong>hanya perlu menunggu beberapa tahun</strong> sebelum seluruh koleksi kita akan dicermin secara meluas. Oleh itu, dalam kata-kata Thomas Jefferson, "diletakkan di luar jangkauan kemalangan." Malangnya, kemunculan LLM, dan latihan data yang memerlukan banyak data, telah membuat banyak pemegang hak cipta bersikap defensif. Lebih daripada yang mereka sudah lakukan. Banyak laman web membuatnya lebih sukar untuk mengikis dan mengarkib, tuntutan mahkamah berterbangan, dan sementara itu perpustakaan fizikal dan arkib terus diabaikan. Kita hanya boleh mengharapkan trend ini terus memburuk, dan banyak karya hilang sebelum mereka memasuki domain awam. <strong>Kita berada di ambang revolusi dalam pemeliharaan, tetapi <q>yang hilang tidak dapat dipulihkan.</q></strong> Kita mempunyai tingkap kritikal sekitar 5-10 tahun di mana ia masih agak mahal untuk mengendalikan perpustakaan bayangan dan mencipta banyak cermin di seluruh dunia, dan di mana akses belum sepenuhnya ditutup. Jika kita dapat merapatkan jurang ini, maka kita benar-benar telah memelihara pengetahuan dan budaya manusia untuk selama-lamanya. Kita tidak seharusnya membiarkan masa ini terbuang sia-sia. Kita tidak seharusnya membiarkan peluang kritikal ini tertutup kepada kita. Mari kita pergi. Tingkap kritikal perpustakaan bayangan Bagaimana kita boleh mendakwa untuk memelihara koleksi kita selama-lamanya, apabila ia sudah menghampiri 1 PB? Koleksi Beberapa maklumat lanjut mengenai koleksi. <a %(duxiu)s>Duxiu</a> adalah pangkalan data besar buku yang diimbas, dicipta oleh <a %(chaoxing)s>SuperStar Digital Library Group</a>. Kebanyakan adalah buku akademik, diimbas untuk menjadikannya tersedia secara digital kepada universiti dan perpustakaan. Untuk penonton berbahasa Inggeris kami, <a %(library_princeton)s>Princeton</a> dan <a %(guides_lib_uw)s>University of Washington</a> mempunyai gambaran keseluruhan yang baik. Terdapat juga artikel yang sangat baik memberikan lebih banyak latar belakang: <a %(doi)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a> (cari di Arkib Anna). Buku-buku dari Duxiu telah lama dipirate di internet Cina. Biasanya ia dijual dengan harga kurang dari satu dolar oleh penjual semula. Ia biasanya diedarkan menggunakan setara Cina Google Drive, yang sering digodam untuk membolehkan lebih banyak ruang simpanan. Beberapa butiran teknikal boleh didapati <a %(github_duty_machine)s>di sini</a> dan <a %(github_821_github_io)s>di sini</a>. Walaupun buku-buku telah diedarkan secara separa awam, agak sukar untuk mendapatkannya secara pukal. Kami meletakkan ini tinggi dalam senarai TODO kami, dan memperuntukkan beberapa bulan kerja sepenuh masa untuknya. Walau bagaimanapun, baru-baru ini seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberitahu kami bahawa mereka telah melakukan semua kerja ini — dengan kos yang besar. Mereka berkongsi keseluruhan koleksi dengan kami, tanpa mengharapkan apa-apa balasan, kecuali jaminan pemeliharaan jangka panjang. Benar-benar luar biasa. Mereka bersetuju untuk meminta bantuan dengan cara ini untuk mendapatkan koleksi di-OCR. Koleksi ini terdiri daripada 7,543,702 fail. Ini lebih banyak daripada Library Genesis bukan fiksyen (sekitar 5.3 juta). Jumlah saiz fail adalah sekitar 359TB (326TiB) dalam bentuk semasa. Kami terbuka kepada cadangan dan idea lain. Hubungi kami sahaja. Lihat Arkib Anna untuk maklumat lanjut mengenai koleksi kami, usaha pemeliharaan, dan bagaimana anda boleh membantu. Terima kasih! Halaman contoh Untuk membuktikan kepada kami bahawa anda mempunyai saluran yang baik, berikut adalah beberapa halaman contoh untuk memulakan, dari sebuah buku mengenai superkonduktor. Saluran anda seharusnya dapat mengendalikan matematik, jadual, carta, nota kaki, dan sebagainya dengan betul. Hantar halaman yang telah diproses kepada e-mel kami. Jika ia kelihatan baik, kami akan menghantar lebih banyak kepada anda secara peribadi, dan kami mengharapkan anda dapat menjalankan saluran anda dengan cepat pada halaman tersebut juga. Setelah kami berpuas hati, kita boleh membuat perjanjian. - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) <a %(duxiu_exclusive_chinese)s>Versi Cina 中文版</a>, <a %(news_ycombinator)s>Bincangkan di Hacker News</a> Ini adalah catatan blog ringkas. Kami sedang mencari syarikat atau institusi untuk membantu kami dengan OCR dan pengekstrakan teks untuk koleksi besar yang kami peroleh, sebagai pertukaran untuk akses awal eksklusif. Selepas tempoh embargo, kami tentunya akan melepaskan keseluruhan koleksi. Teks akademik berkualiti tinggi sangat berguna untuk latihan LLM. Walaupun koleksi kami adalah dalam bahasa Cina, ini seharusnya berguna juga untuk latihan LLM bahasa Inggeris: model nampaknya menyandikan konsep dan pengetahuan tanpa mengira bahasa sumber. Untuk ini, teks perlu diekstrak dari imbasan. Apa yang Arkib Anna dapat daripadanya? Carian teks penuh buku untuk penggunanya. Kerana matlamat kami selari dengan pembangun LLM, kami sedang mencari rakan kerjasama. Kami bersedia memberikan anda <strong>akses awal eksklusif kepada koleksi ini secara pukal selama 1 tahun</strong>, jika anda dapat melakukan OCR dan pengekstrakan teks yang betul. Jika anda bersedia berkongsi keseluruhan kod saluran anda dengan kami, kami bersedia untuk melanjutkan tempoh embargo koleksi. Akses eksklusif untuk syarikat LLM kepada koleksi buku bukan fiksyen Cina terbesar di dunia <em><strong>TL;DR:</strong> Arkib Anna memperoleh koleksi unik 7.5 juta / 350TB buku bukan fiksyen Cina — lebih besar daripada Library Genesis. Kami bersedia memberikan akses eksklusif kepada syarikat LLM, sebagai pertukaran untuk OCR berkualiti tinggi dan pengekstrakan teks.</em> Seni bina sistem Jadi katakan anda menemui beberapa syarikat yang bersedia untuk menjadi hos laman web anda tanpa menutupnya — mari kita panggil mereka “penyedia yang mencintai kebebasan” 😄. Anda akan cepat mendapati bahawa menjadi hos segala-galanya dengan mereka agak mahal, jadi anda mungkin ingin mencari beberapa “penyedia murah” dan melakukan hos sebenar di sana, dengan proksi melalui penyedia yang mencintai kebebasan. Jika anda melakukannya dengan betul, penyedia murah tidak akan tahu apa yang anda hoskan, dan tidak akan menerima sebarang aduan. Dengan semua penyedia ini terdapat risiko mereka menutup anda juga, jadi anda juga memerlukan redundansi. Kami memerlukan ini pada semua peringkat tumpukan kami. Satu syarikat yang agak mencintai kebebasan yang telah meletakkan dirinya dalam kedudukan yang menarik ialah Cloudflare. Mereka telah <a %(blog_cloudflare)s>berhujah</a> bahawa mereka bukan penyedia hos, tetapi utiliti, seperti ISP. Oleh itu, mereka tidak tertakluk kepada DMCA atau permintaan penarikan balik lain, dan meneruskan sebarang permintaan kepada penyedia hos sebenar anda. Mereka telah pergi sejauh pergi ke mahkamah untuk melindungi struktur ini. Oleh itu, kita boleh menggunakannya sebagai satu lagi lapisan caching dan perlindungan. Cloudflare tidak menerima pembayaran tanpa nama, jadi kami hanya boleh menggunakan pelan percuma mereka. Ini bermakna kami tidak boleh menggunakan ciri pengimbangan beban atau failover mereka. Oleh itu, kami <a %(annas_archive_l255)s>melaksanakannya sendiri</a> di peringkat domain. Apabila halaman dimuatkan, pelayar akan memeriksa sama ada domain semasa masih tersedia, dan jika tidak, ia menulis semula semua URL ke domain yang berbeza. Oleh kerana Cloudflare menyimpan banyak halaman, ini bermakna pengguna boleh mendarat di domain utama kami, walaupun pelayan proksi tidak berfungsi, dan kemudian pada klik seterusnya dipindahkan ke domain lain. Kami masih juga mempunyai kebimbangan operasi biasa untuk ditangani, seperti memantau kesihatan pelayan, mencatatkan ralat backend dan frontend, dan sebagainya. Seni bina failover kami membolehkan lebih ketahanan di bahagian ini juga, contohnya dengan menjalankan satu set pelayan yang sama sekali berbeza pada salah satu domain. Kami juga boleh menjalankan versi kod dan Datasets yang lebih lama pada domain berasingan ini, sekiranya pepijat kritikal dalam versi utama tidak disedari. Kami juga boleh melindungi diri daripada Cloudflare berpaling tadah terhadap kami, dengan mengeluarkannya dari salah satu domain, seperti domain berasingan ini. Pelbagai permutasi idea-idea ini adalah mungkin. Kesimpulan Ia adalah satu pengalaman yang menarik untuk belajar bagaimana untuk menubuhkan enjin carian perpustakaan bayangan yang kukuh dan tahan lasak. Terdapat banyak lagi butiran untuk dikongsi dalam kiriman akan datang, jadi beritahu saya apa yang anda ingin pelajari lebih lanjut! Seperti biasa, kami mencari sumbangan untuk menyokong kerja ini, jadi pastikan untuk melihat halaman Derma di Arkib Anna. Kami juga mencari jenis sokongan lain, seperti geran, penaja jangka panjang, penyedia pembayaran berisiko tinggi, mungkin juga iklan (yang berselera!). Dan jika anda ingin menyumbangkan masa dan kemahiran anda, kami sentiasa mencari pembangun, penterjemah, dan sebagainya. Terima kasih atas minat dan sokongan anda. Token inovasi Mari kita mulakan dengan susunan teknologi kami. Ia sengaja membosankan. Kami menggunakan Flask, MariaDB, dan ElasticSearch. Itu sahaja. Carian sebahagian besarnya adalah masalah yang telah diselesaikan, dan kami tidak berniat untuk menciptanya semula. Selain itu, kami perlu membelanjakan <a %(mcfunley)s>token inovasi</a> kami pada perkara lain: tidak diturunkan oleh pihak berkuasa. Jadi, seberapa sah atau tidak sah sebenarnya Arkib Anna? Ini kebanyakannya bergantung pada bidang kuasa undang-undang. Kebanyakan negara mempercayai beberapa bentuk hak cipta, yang bermaksud bahawa orang atau syarikat diberikan monopoli eksklusif pada jenis karya tertentu untuk tempoh masa tertentu. Sebagai tambahan, di Arkib Anna kami percaya walaupun terdapat beberapa manfaat, secara keseluruhan hak cipta adalah negatif bersih untuk masyarakat — tetapi itu adalah cerita untuk masa lain. Monopoli eksklusif pada karya tertentu ini bermaksud bahawa adalah haram bagi sesiapa di luar monopoli ini untuk mengedarkan karya tersebut secara langsung — termasuk kami. Tetapi Arkib Anna adalah enjin carian yang tidak mengedarkan karya tersebut secara langsung (sekurang-kurangnya tidak di laman web clearnet kami), jadi kami sepatutnya baik-baik saja, bukan? Tidak semestinya. Di banyak bidang kuasa, bukan sahaja haram untuk mengedarkan karya berhak cipta, tetapi juga untuk memautkan ke tempat yang melakukannya. Contoh klasik ini adalah undang-undang DMCA di Amerika Syarikat. Itulah hujung spektrum yang paling ketat. Di hujung spektrum yang lain, secara teorinya mungkin ada negara tanpa undang-undang hak cipta sama sekali, tetapi ini sebenarnya tidak wujud. Hampir setiap negara mempunyai beberapa bentuk undang-undang hak cipta dalam buku. Penguatkuasaan adalah cerita yang berbeza. Terdapat banyak negara dengan kerajaan yang tidak peduli untuk menguatkuasakan undang-undang hak cipta. Terdapat juga negara di antara dua ekstrem, yang melarang pengedaran karya berhak cipta, tetapi tidak melarang memautkan kepada karya tersebut. Pertimbangan lain adalah di peringkat syarikat. Jika sebuah syarikat beroperasi dalam bidang kuasa yang tidak peduli tentang hak cipta, tetapi syarikat itu sendiri tidak bersedia mengambil sebarang risiko, maka mereka mungkin menutup laman web anda sebaik sahaja ada yang mengadu mengenainya. Akhirnya, pertimbangan besar adalah pembayaran. Oleh kerana kami perlu kekal tanpa nama, kami tidak boleh menggunakan kaedah pembayaran tradisional. Ini meninggalkan kami dengan mata wang kripto, dan hanya sebilangan kecil syarikat yang menyokongnya (terdapat kad debit maya yang dibayar dengan kripto, tetapi ia sering tidak diterima). - Anna dan pasukan (<a %(reddit)s>Reddit</a>, <a %(t_me)s>Telegram</a>) Saya menjalankan <a %(wikipedia_annas_archive)s>Arkib Anna</a>, enjin carian sumber terbuka bukan untung terbesar di dunia untuk <a %(wikipedia_shadow_library)s>perpustakaan bayangan</a>, seperti Sci-Hub, Library Genesis, dan Z-Library. Matlamat kami adalah untuk menjadikan pengetahuan dan budaya mudah diakses, dan akhirnya membina komuniti orang yang bersama-sama mengarkib dan memelihara <a %(blog_isbndb_dump_how_many_books_are_preserved_forever)s>semua buku di dunia</a>. Dalam artikel ini saya akan menunjukkan bagaimana kami menjalankan laman web ini, dan cabaran unik yang datang dengan mengendalikan laman web dengan status undang-undang yang meragukan, kerana tiada “AWS untuk badan amal bayangan”. <em>Juga lihat artikel saudara <a %(blog_how_to_become_a_pirate_archivist)s>Bagaimana menjadi pengarkib lanun</a>.</em> Cara menjalankan perpustakaan bayangan: operasi di Arkib Anna Tiada <q>AWS untuk badan amal bayangan,</q> jadi bagaimana kami menjalankan Arkib Anna? Alat Pelayan aplikasi: Flask, MariaDB, ElasticSearch, Docker. Pembangunan: Gitlab, Weblate, Zulip. Pengurusan pelayan: Ansible, Checkmk, UFW. Hos statik Onion: Tor, Nginx. Pelayan proksi: Varnish. Mari kita lihat alat apa yang kita gunakan untuk mencapai semua ini. Ini sangat berkembang apabila kita menghadapi masalah baru dan mencari penyelesaian baru. Terdapat beberapa keputusan yang kami telah berulang-alik. Salah satunya ialah komunikasi antara pelayan: kami pernah menggunakan Wireguard untuk ini, tetapi mendapati bahawa ia kadang-kadang berhenti menghantar sebarang data, atau hanya menghantar data dalam satu arah. Ini berlaku dengan beberapa tetapan Wireguard yang berbeza yang kami cuba, seperti <a %(github_costela_wesher)s>wesher</a> dan <a %(github_k4yt3x_wg_meshconf)s>wg-meshconf</a>. Kami juga mencuba terowong port melalui SSH, menggunakan autossh dan sshuttle, tetapi menghadapi <a %(github_sshuttle)s>masalah di sana</a> (walaupun masih tidak jelas kepada saya jika autossh mengalami masalah TCP-over-TCP atau tidak — ia hanya terasa seperti penyelesaian yang janggal kepada saya tetapi mungkin ia sebenarnya baik?). Sebaliknya, kami kembali kepada sambungan langsung antara pelayan, menyembunyikan bahawa pelayan sedang berjalan pada penyedia murah menggunakan penapisan IP dengan UFW. Ini mempunyai kelemahan bahawa Docker tidak berfungsi dengan baik dengan UFW, melainkan anda menggunakan <code>network_mode: "host"</code>. Semua ini adalah sedikit lebih terdedah kepada kesilapan, kerana anda akan mendedahkan pelayan anda kepada internet dengan hanya sedikit salah konfigurasi. Mungkin kita patut kembali kepada autossh — maklum balas akan sangat dialu-alukan di sini. Kami juga telah berulang-alik pada Varnish vs. Nginx. Kami kini menyukai Varnish, tetapi ia mempunyai keanehan dan tepi kasar. Perkara yang sama berlaku untuk Checkmk: kami tidak menyukainya, tetapi ia berfungsi buat masa ini. Weblate telah okay tetapi tidak luar biasa — saya kadang-kadang bimbang ia akan kehilangan data saya setiap kali saya cuba menyegerakkannya dengan repo git kami. Flask telah baik secara keseluruhan, tetapi ia mempunyai beberapa keanehan pelik yang telah memakan banyak masa untuk menyahpepijat, seperti mengkonfigurasi domain tersuai, atau isu dengan integrasi SqlAlchemy. Setakat ini alat lain telah hebat: kami tidak mempunyai aduan serius tentang MariaDB, ElasticSearch, Gitlab, Zulip, Docker, dan Tor. Semua ini telah mempunyai beberapa isu, tetapi tiada yang terlalu serius atau memakan masa. Komuniti Cabaran pertama mungkin mengejutkan. Ia bukan masalah teknikal, atau masalah undang-undang. Ia adalah masalah psikologi: melakukan kerja ini dalam bayangan boleh menjadi sangat sunyi. Bergantung pada apa yang anda rancang untuk lakukan, dan model ancaman anda, anda mungkin perlu sangat berhati-hati. Di satu hujung spektrum, kami mempunyai orang seperti Alexandra Elbakyan*, pengasas Sci-Hub, yang sangat terbuka tentang aktivitinya. Tetapi dia berisiko tinggi ditangkap jika dia melawat negara barat pada masa ini, dan boleh menghadapi puluhan tahun penjara. Adakah itu risiko yang anda sanggup ambil? Kami berada di hujung spektrum yang lain; sangat berhati-hati untuk tidak meninggalkan sebarang jejak, dan mempunyai keselamatan operasi yang kuat. * Seperti yang disebutkan di HN oleh "ynno", Alexandra pada mulanya tidak mahu dikenali: "Pelayan-pelayan beliau disediakan untuk mengeluarkan mesej ralat terperinci dari PHP, termasuk laluan penuh fail sumber yang bermasalah, yang berada di bawah direktori /home/<USER>" Jadi, gunakan nama pengguna rawak pada komputer yang anda gunakan untuk perkara ini, sekiranya anda salah konfigurasi sesuatu. Namun, kerahsiaan itu datang dengan kos psikologi. Kebanyakan orang suka diiktiraf untuk kerja yang mereka lakukan, dan namun anda tidak boleh mengambil sebarang kredit untuk ini dalam kehidupan sebenar. Malah perkara-perkara mudah boleh menjadi mencabar, seperti rakan-rakan bertanya apa yang anda telah lakukan (pada satu ketika "bermain dengan NAS / homelab saya" menjadi membosankan). Inilah sebabnya mengapa sangat penting untuk mencari beberapa komuniti. Anda boleh melepaskan sedikit keselamatan operasi dengan mempercayai beberapa rakan yang sangat rapat, yang anda tahu anda boleh percayai sepenuhnya. Walaupun begitu, berhati-hati untuk tidak meletakkan apa-apa dalam tulisan, sekiranya mereka perlu menyerahkan e-mel mereka kepada pihak berkuasa, atau jika peranti mereka dikompromi dengan cara lain. Lebih baik lagi adalah mencari beberapa rakan lanun. Jika rakan rapat anda berminat untuk menyertai anda, hebat! Jika tidak, anda mungkin dapat mencari orang lain dalam talian. Malangnya ini masih merupakan komuniti niche. Setakat ini kami hanya menemui segelintir orang lain yang aktif dalam ruang ini. Tempat permulaan yang baik nampaknya adalah forum Library Genesis, dan r/DataHoarder. Pasukan Arkib juga mempunyai individu yang sependapat, walaupun mereka beroperasi dalam undang-undang (walaupun dalam beberapa kawasan kelabu undang-undang). Adegan "warez" dan cetak rompak tradisional juga mempunyai orang yang berfikir dengan cara yang serupa. Kami terbuka kepada idea tentang cara untuk memupuk komuniti dan meneroka idea. Jangan ragu untuk menghantar mesej kepada kami di Twitter atau Reddit. Mungkin kami boleh menganjurkan sejenis forum atau kumpulan sembang. Satu cabaran adalah bahawa ini boleh dengan mudah disekat apabila menggunakan platform biasa, jadi kami perlu menganjurkannya sendiri. Terdapat juga pertukaran antara mengadakan perbincangan ini secara terbuka sepenuhnya (lebih banyak potensi penglibatan) berbanding menjadikannya peribadi (tidak membiarkan "sasaran" berpotensi tahu bahawa kami akan mengikis mereka). Kami perlu memikirkan tentang itu. Beritahu kami jika anda berminat dengan ini! Kesimpulan Semoga ini berguna untuk pengarkib lanun yang baru bermula. Kami teruja untuk mengalu-alukan anda ke dunia ini, jadi jangan teragak-agak untuk menghubungi. Mari kita memelihara sebanyak mungkin pengetahuan dan budaya dunia, dan mencerminkannya jauh dan luas. Projek 4. Pemilihan data Selalunya anda boleh menggunakan metadata untuk menentukan subset data yang munasabah untuk dimuat turun. Walaupun anda akhirnya ingin memuat turun semua data, ia boleh berguna untuk memprioritaskan item yang paling penting terlebih dahulu, sekiranya anda dikesan dan pertahanan diperbaiki, atau kerana anda perlu membeli lebih banyak cakera, atau semata-mata kerana sesuatu yang lain muncul dalam hidup anda sebelum anda dapat memuat turun semuanya. Sebagai contoh, satu koleksi mungkin mempunyai pelbagai edisi sumber asas yang sama (seperti buku atau filem), di mana satu ditandakan sebagai kualiti terbaik. Menyimpan edisi tersebut terlebih dahulu adalah masuk akal. Anda mungkin akhirnya ingin menyimpan semua edisi, kerana dalam beberapa kes metadata mungkin ditandakan dengan salah, atau mungkin terdapat kompromi yang tidak diketahui antara edisi (contohnya, "edisi terbaik" mungkin terbaik dalam kebanyakan cara tetapi lebih buruk dalam cara lain, seperti filem yang mempunyai resolusi lebih tinggi tetapi tiada sari kata). Anda juga boleh mencari pangkalan data metadata anda untuk mencari perkara menarik. Apakah fail terbesar yang dihoskan, dan mengapa ia begitu besar? Apakah fail terkecil? Adakah terdapat corak menarik atau tidak dijangka apabila berkaitan dengan kategori tertentu, bahasa, dan sebagainya? Adakah terdapat tajuk yang berulang atau sangat serupa? Adakah terdapat corak apabila data ditambah, seperti satu hari di mana banyak fail ditambah sekaligus? Anda sering dapat belajar banyak dengan melihat set data dalam pelbagai cara. Dalam kes kami, kami menghapuskan pendua buku Z-Library terhadap hash md5 dalam Library Genesis, dengan itu menjimatkan banyak masa muat turun dan ruang cakera. Ini adalah situasi yang agak unik. Dalam kebanyakan kes, tiada pangkalan data komprehensif tentang fail mana yang sudah dipelihara dengan baik oleh rakan-rakan lanun. Ini sendiri adalah peluang besar untuk seseorang di luar sana. Ia akan menjadi hebat untuk mempunyai gambaran keseluruhan yang dikemas kini secara berkala tentang perkara seperti muzik dan filem yang sudah banyak disemai di laman web torrent, dan oleh itu keutamaan yang lebih rendah untuk dimasukkan dalam cermin lanun. 6. Pengedaran Anda mempunyai data, dengan itu memberikan anda pemilikan cermin lanun pertama di dunia bagi sasaran anda (kemungkinan besar). Dalam banyak cara, bahagian yang paling sukar telah berakhir, tetapi bahagian yang paling berisiko masih di hadapan anda. Lagipun, setakat ini anda telah bertindak secara senyap; terbang di bawah radar. Apa yang perlu anda lakukan ialah menggunakan VPN yang baik sepanjang masa, tidak mengisi butiran peribadi anda dalam sebarang borang (duh), dan mungkin menggunakan sesi pelayar khas (atau bahkan komputer yang berbeza). Sekarang anda perlu mengedarkan data. Dalam kes kami, kami mula-mula ingin menyumbangkan buku-buku kembali kepada Library Genesis, tetapi kemudian dengan cepat menemui kesukaran dalam hal itu (penyusunan fiksyen vs bukan fiksyen). Jadi kami memutuskan untuk pengedaran menggunakan torrent gaya Library Genesis. Jika anda mempunyai peluang untuk menyumbang kepada projek sedia ada, maka itu boleh menjimatkan banyak masa anda. Walau bagaimanapun, tidak banyak cermin lanun yang teratur dengan baik di luar sana pada masa ini. Jadi katakan anda memutuskan untuk mengedarkan torrent sendiri. Cuba pastikan fail-fail tersebut kecil, supaya mudah dicerminkan di laman web lain. Anda kemudian perlu menyemai torrent tersebut sendiri, sambil tetap kekal tanpa nama. Anda boleh menggunakan VPN (dengan atau tanpa pemajuan port), atau membayar dengan Bitcoin yang telah dicampur untuk Seedbox. Jika anda tidak tahu apa maksud beberapa istilah tersebut, anda perlu membaca banyak, kerana penting untuk anda memahami pertukaran risiko di sini. Anda boleh menjadi hos fail torrent itu sendiri di laman web torrent sedia ada. Dalam kes kami, kami memilih untuk benar-benar menjadi hos laman web, kerana kami juga ingin menyebarkan falsafah kami dengan cara yang jelas. Anda boleh melakukannya sendiri dengan cara yang serupa (kami menggunakan Njalla untuk domain dan hosting kami, dibayar dengan Bitcoin yang telah dicampur), tetapi juga jangan ragu untuk menghubungi kami untuk membolehkan kami menjadi hos torrent anda. Kami sedang mencari untuk membina indeks komprehensif cermin lanun dari masa ke masa, jika idea ini mendapat sambutan. Mengenai pemilihan VPN, banyak telah ditulis tentang ini, jadi kami hanya akan mengulangi nasihat umum untuk memilih berdasarkan reputasi. Polisi tanpa log yang telah diuji di mahkamah dengan rekod panjang melindungi privasi adalah pilihan risiko terendah, pada pendapat kami. Perhatikan bahawa walaupun anda melakukan semuanya dengan betul, anda tidak boleh mencapai risiko sifar. Sebagai contoh, semasa menyemai torrents anda, pelaku negara yang sangat bermotivasi mungkin dapat melihat aliran data masuk dan keluar untuk pelayan VPN, dan menyimpulkan siapa anda. Atau anda boleh sahaja membuat kesilapan. Kami mungkin sudah melakukannya, dan akan melakukannya lagi. Nasib baik, negara-negara tidak begitu peduli <em>tentang</em> cetak rompak. Satu keputusan yang perlu dibuat untuk setiap projek adalah sama ada untuk menerbitkannya menggunakan identiti yang sama seperti sebelumnya, atau tidak. Jika anda terus menggunakan nama yang sama, maka kesilapan dalam keselamatan operasi dari projek sebelumnya boleh kembali menghantui anda. Tetapi menerbitkan di bawah nama yang berbeza bermakna anda tidak membina reputasi yang lebih lama. Kami memilih untuk mempunyai keselamatan operasi yang kuat dari awal supaya kami boleh terus menggunakan identiti yang sama, tetapi kami tidak akan teragak-agak untuk menerbitkan di bawah nama yang berbeza jika kami membuat kesilapan atau jika keadaan memerlukannya. Menyebarkan berita boleh menjadi rumit. Seperti yang kami katakan, ini masih merupakan komuniti niche. Kami pada asalnya menyiarkan di Reddit, tetapi benar-benar mendapat perhatian di Hacker News. Buat masa ini, cadangan kami adalah untuk menyiarkannya di beberapa tempat dan lihat apa yang berlaku. Dan sekali lagi, hubungi kami. Kami ingin menyebarkan usaha pengarkiban lanun yang lebih banyak. 1. Pemilihan domain / falsafah Tidak ada kekurangan pengetahuan dan warisan budaya untuk diselamatkan, yang boleh menjadi luar biasa. Itulah sebabnya selalunya berguna untuk meluangkan masa dan memikirkan tentang apa yang boleh anda sumbangkan. Setiap orang mempunyai cara berfikir yang berbeza tentang ini, tetapi berikut adalah beberapa soalan yang boleh anda tanyakan kepada diri sendiri: Dalam kes kami, kami sangat mengambil berat tentang pemeliharaan jangka panjang sains. Kami tahu tentang Library Genesis, dan bagaimana ia dicerminkan sepenuhnya berkali-kali menggunakan torrents. Kami menyukai idea itu. Kemudian suatu hari, salah seorang daripada kami cuba mencari beberapa buku teks saintifik di Library Genesis, tetapi tidak dapat menemuinya, menimbulkan keraguan tentang betapa lengkapnya ia sebenarnya. Kami kemudian mencari buku teks tersebut dalam talian, dan menemuinya di tempat lain, yang menanam benih untuk projek kami. Bahkan sebelum kami tahu tentang Z-Library, kami mempunyai idea untuk tidak cuba mengumpulkan semua buku tersebut secara manual, tetapi untuk fokus pada mencerminkan koleksi sedia ada, dan menyumbangkannya kembali kepada Library Genesis. Apakah kemahiran yang anda miliki yang boleh anda gunakan untuk manfaat anda? Sebagai contoh, jika anda seorang pakar keselamatan dalam talian, anda boleh mencari cara untuk mengalahkan sekatan IP untuk sasaran yang selamat. Jika anda hebat dalam mengatur komuniti, maka mungkin anda boleh mengumpulkan beberapa orang di sekitar satu matlamat. Adalah berguna untuk mengetahui sedikit pengaturcaraan, walaupun hanya untuk menjaga keselamatan operasi yang baik sepanjang proses ini. Apakah kawasan leverage tinggi yang perlu difokuskan? Jika anda akan menghabiskan X jam untuk pengarkiban lanun, maka bagaimana anda boleh mendapatkan "hasil yang paling berbaloi"? Apakah cara unik yang anda fikirkan tentang ini? Anda mungkin mempunyai beberapa idea atau pendekatan menarik yang mungkin terlepas oleh orang lain. Berapa banyak masa yang anda ada untuk ini? Nasihat kami adalah untuk memulakan dengan kecil dan melakukan projek yang lebih besar apabila anda sudah biasa, tetapi ia boleh menjadi sangat menyeluruh. Mengapa anda berminat dengan ini? Apa yang anda minati? Jika kita boleh mendapatkan sekumpulan orang yang semua mengarkibkan jenis perkara yang mereka pedulikan secara khusus, itu akan meliputi banyak! Anda akan tahu lebih banyak daripada orang biasa tentang minat anda, seperti data penting untuk disimpan, apakah koleksi dan komuniti dalam talian terbaik, dan sebagainya. 3. Pengikisan metadata Tarikh ditambah/diubah: supaya anda boleh kembali kemudian dan memuat turun fail yang anda belum muat turun sebelum ini (walaupun anda juga boleh menggunakan ID atau hash untuk ini). Hash (md5, sha1): untuk mengesahkan bahawa anda memuat turun fail dengan betul. ID: boleh jadi beberapa ID dalaman, tetapi ID seperti ISBN atau DOI juga berguna. Nama fail / lokasi Penerangan, kategori, tag, pengarang, bahasa, dan lain-lain. Saiz: untuk mengira berapa banyak ruang cakera yang anda perlukan. Mari kita menjadi sedikit lebih teknikal di sini. Untuk benar-benar mengikis metadata dari laman web, kami telah menjaga perkara-perkara dengan cukup mudah. Kami menggunakan skrip Python, kadang-kadang curl, dan pangkalan data MySQL untuk menyimpan hasilnya. Kami belum menggunakan sebarang perisian pengikisan mewah yang dapat memetakan laman web yang kompleks, kerana setakat ini kami hanya perlu mengikis satu atau dua jenis halaman dengan hanya menyenaraikan melalui id dan mengurai HTML. Jika tidak ada halaman yang mudah disenaraikan, maka anda mungkin memerlukan perayap yang betul yang cuba mencari semua halaman. Sebelum anda mula mengikis seluruh laman web, cuba lakukannya secara manual untuk seketika. Pergi melalui beberapa dozen halaman sendiri, untuk mendapatkan rasa bagaimana itu berfungsi. Kadang-kadang anda akan sudah menghadapi blok IP atau tingkah laku menarik lain dengan cara ini. Begitu juga untuk pengikisan data: sebelum terlalu mendalam ke dalam sasaran ini, pastikan anda benar-benar dapat memuat turun datanya dengan berkesan. Untuk mengatasi sekatan, ada beberapa perkara yang boleh anda cuba. Adakah terdapat alamat IP atau pelayan lain yang menjadi tuan rumah data yang sama tetapi tidak mempunyai sekatan yang sama? Adakah terdapat titik akhir API yang tidak mempunyai sekatan, sementara yang lain ada? Pada kadar muat turun berapa IP anda disekat, dan untuk berapa lama? Atau adakah anda tidak disekat tetapi diperlambat? Bagaimana jika anda membuat akaun pengguna, bagaimana perkara berubah kemudian? Bolehkah anda menggunakan HTTP/2 untuk menjaga sambungan terbuka, dan adakah itu meningkatkan kadar di mana anda boleh meminta halaman? Adakah terdapat halaman yang menyenaraikan beberapa fail sekaligus, dan adakah maklumat yang disenaraikan di sana mencukupi? Perkara yang mungkin anda ingin simpan termasuk: Kami biasanya melakukan ini dalam dua peringkat. Pertama, kami memuat turun fail HTML mentah, biasanya terus ke dalam MySQL (untuk mengelakkan banyak fail kecil, yang kami bincangkan lebih lanjut di bawah). Kemudian, dalam langkah berasingan, kami melalui fail HTML tersebut dan memprosesnya ke dalam jadual MySQL sebenar. Dengan cara ini, anda tidak perlu memuat turun semula semuanya dari awal jika anda menemui kesilapan dalam kod pemprosesan anda, kerana anda boleh memproses semula fail HTML dengan kod baru. Ia juga sering lebih mudah untuk memparallelkan langkah pemprosesan, dengan itu menjimatkan masa (dan anda boleh menulis kod pemprosesan semasa pengikisan sedang berjalan, daripada perlu menulis kedua-dua langkah sekaligus). Akhirnya, perhatikan bahawa untuk beberapa sasaran, pengikisan metadata adalah semua yang ada. Terdapat beberapa koleksi metadata besar di luar sana yang tidak dipelihara dengan betul. Tajuk Pemilihan domain / falsafah: Di mana anda ingin memberi tumpuan secara kasar, dan mengapa? Apakah minat, kemahiran, dan keadaan unik anda yang boleh anda gunakan untuk manfaat anda? Pemilihan sasaran: Koleksi khusus mana yang akan anda cermin? Pengikisan metadata: Mengkatalogkan maklumat tentang fail, tanpa benar-benar memuat turun fail (yang selalunya lebih besar) itu sendiri. Pemilihan data: Berdasarkan metadata, mengecilkan data mana yang paling relevan untuk diarkibkan sekarang. Mungkin semuanya, tetapi selalunya terdapat cara yang munasabah untuk menjimatkan ruang dan lebar jalur. Pengikisan data: Sebenarnya mendapatkan data. Pengedaran: Membungkusnya dalam torrent, mengumumkannya di suatu tempat, mendapatkan orang untuk menyebarkannya. 5. Pengikisan data Sekarang anda sudah bersedia untuk memuat turun data secara pukal. Seperti yang disebutkan sebelum ini, pada ketika ini anda sepatutnya sudah memuat turun secara manual beberapa fail, untuk lebih memahami tingkah laku dan sekatan sasaran. Walau bagaimanapun, masih akan ada kejutan yang menanti anda sebaik sahaja anda benar-benar mula memuat turun banyak fail sekaligus. Nasihat kami di sini adalah terutamanya untuk menjadikannya mudah. Mulakan dengan hanya memuat turun beberapa fail. Anda boleh menggunakan Python, dan kemudian berkembang kepada berbilang benang. Tetapi kadang-kadang lebih mudah adalah untuk menjana fail Bash secara langsung dari pangkalan data, dan kemudian menjalankan beberapa daripadanya dalam beberapa tetingkap terminal untuk meningkatkan skala. Satu helah teknikal cepat yang patut disebut di sini ialah menggunakan OUTFILE dalam MySQL, yang boleh anda tulis di mana-mana jika anda melumpuhkan "secure_file_priv" dalam mysqld.cnf (dan pastikan juga untuk melumpuhkan/menggantikan AppArmor jika anda menggunakan Linux). Kami menyimpan data pada cakera keras yang mudah. Mulakan dengan apa sahaja yang anda ada, dan berkembang perlahan-lahan. Ia boleh menjadi menakutkan untuk memikirkan tentang menyimpan ratusan TB data. Jika itu adalah situasi yang anda hadapi, hanya keluarkan subset yang baik terlebih dahulu, dan dalam pengumuman anda minta bantuan dalam menyimpan selebihnya. Jika anda ingin mendapatkan lebih banyak cakera keras sendiri, maka r/DataHoarder mempunyai beberapa sumber yang baik untuk mendapatkan tawaran yang baik. Cuba risau tentang sistem fail yang rumit. Mudah untuk terjebak dalam memasang perkara seperti ZFS. Satu butiran teknikal yang perlu disedari ialah banyak sistem fail tidak berfungsi dengan baik dengan banyak fail. Kami mendapati bahawa penyelesaian mudah adalah dengan membuat beberapa direktori, contohnya untuk julat ID yang berbeza atau awalan hash. Selepas memuat turun data, pastikan untuk memeriksa integriti fail menggunakan hash dalam metadata, jika ada. 2. Pemilihan sasaran Mudah diakses: tidak menggunakan banyak lapisan perlindungan untuk menghalang anda daripada mengikis metadata dan data mereka. Wawasan istimewa: anda mempunyai maklumat istimewa tentang sasaran ini, seperti anda mempunyai akses khas kepada koleksi ini, atau anda berjaya mengatasi pertahanan mereka. Ini tidak diperlukan (projek kami yang akan datang tidak melakukan apa-apa yang istimewa), tetapi ia pasti membantu! Besar Jadi, kami mempunyai kawasan yang kami lihat, sekarang koleksi khusus mana yang kami cermin? Terdapat beberapa perkara yang menjadikan sasaran yang baik: Apabila kami menemui buku teks sains kami di laman web selain daripada Library Genesis, kami cuba mencari tahu bagaimana mereka sampai ke internet. Kami kemudian menemui Z-Library, dan menyedari bahawa walaupun kebanyakan buku tidak pertama kali muncul di sana, mereka akhirnya berakhir di sana. Kami belajar tentang hubungannya dengan Library Genesis, dan struktur insentif (kewangan) dan antara muka pengguna yang unggul, kedua-duanya menjadikannya koleksi yang lebih lengkap. Kami kemudian melakukan beberapa pengikisan metadata dan data awal, dan menyedari bahawa kami dapat mengatasi had muat turun IP mereka, memanfaatkan akses khas salah seorang ahli kami ke banyak pelayan proksi. Semasa anda meneroka sasaran yang berbeza, adalah penting untuk menyembunyikan jejak anda dengan menggunakan VPN dan alamat e-mel sekali pakai, yang akan kami bincangkan lebih lanjut kemudian. Unik: tidak sudah diliputi dengan baik oleh projek lain. Apabila kami menjalankan projek, ia mempunyai beberapa fasa: Ini bukan fasa yang sepenuhnya bebas, dan selalunya pandangan dari fasa kemudian menghantar anda kembali ke fasa sebelumnya. Sebagai contoh, semasa pengikisan metadata anda mungkin menyedari bahawa sasaran yang anda pilih mempunyai mekanisme pertahanan di luar tahap kemahiran anda (seperti sekatan IP), jadi anda kembali dan mencari sasaran lain. - Anna dan pasukan (<a %(reddit)s>Reddit</a>) Buku penuh boleh ditulis tentang <em>mengapa</em> pemeliharaan digital secara umum, dan arkivisme lanun secara khusus, tetapi mari kita berikan pengenalan ringkas untuk mereka yang tidak terlalu biasa. Dunia sedang menghasilkan lebih banyak pengetahuan dan budaya berbanding sebelum ini, tetapi juga lebih banyak daripadanya hilang berbanding sebelum ini. Manusia sebahagian besarnya mempercayakan syarikat seperti penerbit akademik, perkhidmatan penstriman, dan syarikat media sosial dengan warisan ini, dan mereka sering kali tidak terbukti menjadi penjaga yang baik. Lihatlah dokumentari Digital Amnesia, atau mana-mana ceramah oleh Jason Scott. Terdapat beberapa institusi yang melakukan kerja yang baik dalam mengarkibkan sebanyak mungkin, tetapi mereka terikat oleh undang-undang. Sebagai lanun, kami berada dalam kedudukan unik untuk mengarkibkan koleksi yang mereka tidak dapat sentuh, kerana penguatkuasaan hak cipta atau sekatan lain. Kami juga boleh mencermin koleksi berkali-kali, di seluruh dunia, dengan itu meningkatkan peluang pemeliharaan yang betul. Buat masa ini, kami tidak akan membincangkan tentang kebaikan dan keburukan harta intelek, moral melanggar undang-undang, renungan tentang penapisan, atau isu akses kepada pengetahuan dan budaya. Dengan semua itu diketepikan, mari kita terjun ke dalam <em>bagaimana</em>. Kami akan berkongsi bagaimana pasukan kami menjadi arkivis lanun, dan pelajaran yang kami pelajari sepanjang perjalanan. Terdapat banyak cabaran apabila anda memulakan perjalanan ini, dan semoga kami dapat membantu anda melalui beberapa daripadanya. Bagaimana untuk menjadi arkivis lanun Cabaran pertama mungkin mengejutkan. Ia bukan masalah teknikal, atau masalah undang-undang. Ia adalah masalah psikologi. Sebelum kita menyelam masuk, dua kemas kini mengenai Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>): Kami menerima beberapa sumbangan yang sangat murah hati. Yang pertama adalah $10k daripada individu tanpa nama yang juga telah menyokong "bookwarrior", pengasas asal Library Genesis. Terima kasih khas kepada bookwarrior kerana memudahkan sumbangan ini. Yang kedua adalah $10k lagi daripada penderma tanpa nama, yang menghubungi kami selepas keluaran terakhir kami, dan terinspirasi untuk membantu. Kami juga menerima beberapa sumbangan yang lebih kecil. Terima kasih banyak atas sokongan anda yang murah hati. Kami mempunyai beberapa projek baru yang menarik dalam perancangan yang akan disokong oleh ini, jadi nantikan. Kami mengalami beberapa kesulitan teknikal dengan saiz keluaran kedua kami, tetapi torrent kami kini sudah tersedia dan sedang disemai. Kami juga menerima tawaran murah hati daripada individu tanpa nama untuk menyemai koleksi kami pada pelayan berkelajuan sangat tinggi mereka, jadi kami sedang melakukan muat naik khas ke mesin mereka, selepas itu semua orang lain yang memuat turun koleksi tersebut sepatutnya melihat peningkatan besar dalam kelajuan. Kiriman blog Hai, saya Anna. Saya mencipta <a %(wikipedia_annas_archive)s>Arkib Anna</a>, perpustakaan bayangan terbesar di dunia. Ini adalah blog peribadi saya, di mana saya dan rakan sepasukan saya menulis tentang cetak rompak, pemeliharaan digital, dan banyak lagi. Hubungi saya di <a %(reddit)s>Reddit</a>. Perhatikan bahawa laman web ini hanyalah sebuah blog. Kami hanya menempatkan kata-kata kami sendiri di sini. Tiada torrent atau fail berhak cipta lain yang dihoskan atau dipautkan di sini. <strong>Perpustakaan</strong> - Seperti kebanyakan perpustakaan, kami memberi tumpuan terutamanya kepada bahan bertulis seperti buku. Kami mungkin akan berkembang ke jenis media lain pada masa hadapan. <strong>Cermin</strong> - Kami adalah cermin semata-mata bagi perpustakaan yang sedia ada. Kami memberi tumpuan kepada pemeliharaan, bukan untuk menjadikan buku mudah dicari dan dimuat turun (akses) atau memupuk komuniti besar orang yang menyumbang buku baru (sumber). <strong>Lanun</strong> - Kami sengaja melanggar undang-undang hak cipta di kebanyakan negara. Ini membolehkan kami melakukan sesuatu yang entiti sah tidak dapat lakukan: memastikan buku-buku dicerminkan secara meluas. <em>Kami tidak memautkan fail dari blog ini. Sila cari sendiri.</em> - Anna dan pasukan (<a %(reddit)s>Reddit</a>) Projek ini (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>) bertujuan untuk menyumbang kepada pemeliharaan dan pembebasan pengetahuan manusia. Kami membuat sumbangan kecil dan rendah hati kami, mengikuti jejak langkah orang-orang hebat sebelum kami. Fokus projek ini digambarkan oleh namanya: Perpustakaan pertama yang kami cerminkan adalah Z-Library. Ini adalah perpustakaan yang popular (dan haram). Mereka telah mengambil koleksi Library Genesis dan menjadikannya mudah dicari. Selain itu, mereka telah menjadi sangat berkesan dalam meminta sumbangan buku baru, dengan memberi insentif kepada pengguna yang menyumbang dengan pelbagai kelebihan. Mereka pada masa ini tidak menyumbangkan buku baru ini kembali kepada Library Genesis. Dan tidak seperti Library Genesis, mereka tidak menjadikan koleksi mereka mudah dicerminkan, yang menghalang pemeliharaan yang meluas. Ini penting untuk model perniagaan mereka, kerana mereka mengenakan bayaran untuk mengakses koleksi mereka secara pukal (lebih daripada 10 buku sehari). Kami tidak membuat penilaian moral tentang mengenakan bayaran untuk akses pukal kepada koleksi buku haram. Tidak dapat dinafikan bahawa Z-Library telah berjaya dalam memperluas akses kepada pengetahuan, dan mendapatkan lebih banyak buku. Kami hanya di sini untuk melakukan bahagian kami: memastikan pemeliharaan jangka panjang koleksi peribadi ini. Kami ingin menjemput anda untuk membantu memelihara dan membebaskan pengetahuan manusia dengan memuat turun dan menyemai torrents kami. Lihat halaman projek untuk maklumat lanjut tentang bagaimana data diatur. Kami juga sangat mengalu-alukan anda untuk menyumbangkan idea anda tentang koleksi mana yang perlu dicerminkan seterusnya, dan bagaimana untuk melakukannya. Bersama-sama kita boleh mencapai banyak. Ini hanyalah sumbangan kecil di antara banyak yang lain. Terima kasih, untuk semua yang anda lakukan. Memperkenalkan Cermin Perpustakaan Lanun: Memelihara 7TB buku (yang tidak ada dalam Libgen) 10% of warisan tulisan manusia dipelihara selama-lamanya <strong>Google.</strong> Lagipun, mereka telah melakukan penyelidikan ini untuk Google Books. Walau bagaimanapun, metadata mereka tidak boleh diakses secara pukal dan agak sukar untuk diambil. <strong>Pelbagai sistem perpustakaan individu dan arkib.</strong> Terdapat perpustakaan dan arkib yang belum diindeks dan digabungkan oleh mana-mana yang di atas, selalunya kerana mereka kekurangan dana, atau atas sebab lain tidak mahu berkongsi data mereka dengan Open Library, OCLC, Google, dan sebagainya. Banyak daripada ini mempunyai rekod digital yang boleh diakses melalui internet, dan mereka sering kali tidak dilindungi dengan baik, jadi jika anda ingin membantu dan berseronok belajar tentang sistem perpustakaan yang pelik, ini adalah titik permulaan yang hebat. <strong>ISBNdb.</strong> Ini adalah topik catatan blog ini. ISBNdb mengikis pelbagai laman web untuk metadata buku, khususnya data harga, yang kemudian mereka jual kepada penjual buku, supaya mereka dapat menetapkan harga buku mereka selaras dengan pasaran. Memandangkan ISBN agak universal pada masa kini, mereka secara efektif membina "halaman web untuk setiap buku". <strong>Open Library.</strong> Seperti yang disebutkan sebelum ini, ini adalah misi utama mereka. Mereka telah mendapatkan sejumlah besar data perpustakaan dari perpustakaan yang bekerjasama dan arkib nasional, dan terus melakukannya. Mereka juga mempunyai pustakawan sukarela dan pasukan teknikal yang cuba menghapuskan rekod yang berulang, dan menandainya dengan pelbagai jenis metadata. Yang terbaik, set data mereka adalah sepenuhnya terbuka. Anda boleh <a %(openlibrary)s>muat turunnya</a> dengan mudah. <strong>WorldCat.</strong> Ini adalah laman web yang dikendalikan oleh OCLC bukan untung, yang menjual sistem pengurusan perpustakaan. Mereka mengumpulkan metadata buku dari banyak perpustakaan, dan menjadikannya tersedia melalui laman web WorldCat. Walau bagaimanapun, mereka juga menjana wang dengan menjual data ini, jadi ia tidak tersedia untuk muat turun pukal. Mereka mempunyai beberapa set data pukal yang lebih terhad yang tersedia untuk muat turun, dengan kerjasama perpustakaan tertentu. 1. Untuk beberapa definisi "selama-lamanya" yang munasabah. ;) 2. Sudah tentu, warisan tulisan manusia adalah lebih daripada sekadar buku, terutamanya pada masa kini. Demi pos ini dan keluaran terbaru kami, kami memberi tumpuan kepada buku, tetapi minat kami melangkaui itu. 3. Terdapat banyak lagi yang boleh dikatakan tentang Aaron Swartz, tetapi kami hanya ingin menyebutnya secara ringkas, kerana dia memainkan peranan penting dalam cerita ini. Apabila masa berlalu, lebih ramai orang mungkin menemui namanya buat kali pertama, dan seterusnya boleh menyelami lubang arnab itu sendiri. <strong>Salinan fizikal.</strong> Jelas sekali ini tidak begitu membantu, kerana mereka hanya salinan bahan yang sama. Ia akan menarik jika kita dapat memelihara semua anotasi yang dibuat orang dalam buku, seperti “coretan di tepi” Fermat yang terkenal. Tetapi malangnya, itu akan kekal sebagai impian seorang arkivis. <strong>“Edisi”.</strong> Di sini anda mengira setiap versi unik buku. Jika ada apa-apa yang berbeza mengenainya, seperti kulit yang berbeza atau kata pengantar yang berbeza, ia dikira sebagai edisi yang berbeza. <strong>Fail.</strong> Apabila bekerja dengan perpustakaan bayangan seperti Library Genesis, Sci-Hub, atau Z-Library, terdapat pertimbangan tambahan. Terdapat beberapa imbasan edisi yang sama. Dan orang boleh membuat versi yang lebih baik dari fail sedia ada, dengan mengimbas teks menggunakan OCR, atau membetulkan halaman yang diimbas pada sudut. Kami mahu hanya mengira fail-fail ini sebagai satu edisi, yang memerlukan metadata yang baik, atau deduplikasi menggunakan ukuran kesamaan dokumen. <strong>“Karya”.</strong> Sebagai contoh “Harry Potter and the Chamber of Secrets” sebagai konsep logik, merangkumi semua versinya, seperti terjemahan dan cetakan semula yang berbeza. Ini adalah definisi yang agak berguna, tetapi boleh menjadi sukar untuk menarik garis apa yang dikira. Sebagai contoh, kita mungkin mahu memelihara terjemahan yang berbeza, walaupun cetakan semula dengan hanya perbezaan kecil mungkin tidak begitu penting. - Anna dan pasukan (<a %(reddit)s>Reddit</a>) Dengan Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>), matlamat kami adalah untuk mengambil semua buku di dunia, dan memeliharanya selama-lamanya.<sup>1</sup> Antara torrents Z-Library kami, dan torrents asal Library Genesis, kami mempunyai 11,783,153 fail. Tetapi berapa banyak sebenarnya? Jika kita menghapuskan fail-fail tersebut dengan betul, berapa peratus daripada semua buku di dunia yang telah kita pelihara? Kami benar-benar ingin mempunyai sesuatu seperti ini: Mari kita mulakan dengan beberapa angka kasar: Dalam kedua-dua Z-Library/Libgen dan Open Library terdapat lebih banyak buku daripada ISBN unik. Adakah itu bermakna banyak buku tersebut tidak mempunyai ISBN, atau adakah metadata ISBN hanya hilang? Kami mungkin boleh menjawab soalan ini dengan gabungan pemadanan automatik berdasarkan atribut lain (tajuk, pengarang, penerbit, dll), menarik lebih banyak sumber data, dan mengekstrak ISBN dari imbasan buku sebenar (dalam kes Z-Library/Libgen). Berapa banyak daripada ISBN tersebut yang unik? Ini paling baik digambarkan dengan diagram Venn: Untuk lebih tepat: Kami terkejut dengan betapa sedikitnya pertindihan yang ada! ISBNdb mempunyai sejumlah besar ISBN yang tidak muncul sama ada dalam Z-Library atau Open Library, dan perkara yang sama berlaku (walaupun pada tahap yang lebih kecil tetapi masih ketara) untuk kedua-dua yang lain. Ini menimbulkan banyak persoalan baru. Sejauh mana padanan automatik akan membantu dalam menandakan buku yang tidak ditandakan dengan ISBN? Adakah akan terdapat banyak padanan dan oleh itu peningkatan pertindihan? Juga, apa yang akan berlaku jika kita membawa masuk dataset ke-4 atau ke-5? Berapa banyak pertindihan yang akan kita lihat kemudian? Ini memberi kita titik permulaan. Kita kini boleh melihat semua ISBN yang tidak terdapat dalam dataset Z-Library, dan yang tidak sepadan dengan medan tajuk/pengarang juga. Itu boleh memberi kita pegangan untuk memelihara semua buku di dunia: pertama dengan mengikis internet untuk imbasan, kemudian dengan keluar dalam kehidupan sebenar untuk mengimbas buku. Yang terakhir ini boleh dibiayai oleh orang ramai, atau didorong oleh "ganjaran" daripada orang yang ingin melihat buku tertentu didigitalkan. Semua itu adalah cerita untuk masa yang lain. Jika anda ingin membantu dengan mana-mana perkara ini — analisis lanjut; mengikis lebih banyak metadata; mencari lebih banyak buku; OCR buku; melakukan ini untuk domain lain (contohnya kertas, buku audio, filem, rancangan TV, majalah) atau bahkan menjadikan sebahagian daripada data ini tersedia untuk perkara seperti latihan model bahasa besar / ML — sila hubungi saya (<a %(reddit)s>Reddit</a>). Jika anda khususnya berminat dalam analisis data, kami sedang berusaha untuk menjadikan dataset dan skrip kami tersedia dalam format yang lebih mudah digunakan. Ia akan menjadi hebat jika anda boleh hanya fork sebuah notebook dan mula bermain dengan ini. Akhirnya, jika anda ingin menyokong kerja ini, sila pertimbangkan untuk membuat sumbangan. Ini adalah operasi yang dijalankan sepenuhnya oleh sukarelawan, dan sumbangan anda membuat perbezaan yang besar. Setiap sedikit membantu. Buat masa ini kami menerima sumbangan dalam bentuk kripto; lihat halaman Derma di Arkib Anna. Untuk peratusan, kita memerlukan penyebut: jumlah keseluruhan buku yang pernah diterbitkan.<sup>2</sup> Sebelum kemusnahan Google Books, seorang jurutera dalam projek itu, Leonid Taycher, <a %(booksearch_blogspot)s>mencuba untuk menganggarkan</a> nombor ini. Dia datang — secara berseloroh — dengan 129,864,880 (“sekurang-kurangnya sehingga Ahad”). Dia menganggarkan nombor ini dengan membina pangkalan data bersatu bagi semua buku di dunia. Untuk ini, dia mengumpulkan pelbagai set data dan kemudian menggabungkannya dengan pelbagai cara. Sebagai selingan ringkas, terdapat seorang lagi yang cuba untuk mengkatalogkan semua buku di dunia: Aaron Swartz, aktivis digital yang telah meninggal dunia dan pengasas bersama Reddit.<sup>3</sup> Dia <a %(youtube)s>memulakan Open Library</a> dengan matlamat “satu halaman web untuk setiap buku yang pernah diterbitkan”, menggabungkan data dari banyak sumber yang berbeza. Dia akhirnya membayar harga tertinggi untuk kerja pemeliharaan digitalnya apabila dia didakwa kerana memuat turun kertas akademik secara besar-besaran, yang membawa kepada bunuh dirinya. Tidak perlu dikatakan, ini adalah salah satu sebab kumpulan kami menggunakan nama samaran, dan mengapa kami sangat berhati-hati. Open Library masih dijalankan secara heroik oleh orang-orang di Internet Archive, meneruskan legasi Aaron. Kami akan kembali kepada ini kemudian dalam pos ini. Dalam catatan blog Google, Taycher menerangkan beberapa cabaran dengan menganggarkan nombor ini. Pertama, apa yang membentuk sebuah buku? Terdapat beberapa definisi yang mungkin: “Edisi” nampaknya adalah definisi yang paling praktikal untuk apa yang dimaksudkan dengan “buku”. Secara kebetulan, definisi ini juga digunakan untuk memberikan nombor ISBN yang unik. ISBN, atau Nombor Buku Standard Antarabangsa, biasanya digunakan untuk perdagangan antarabangsa, kerana ia diintegrasikan dengan sistem kod bar antarabangsa (”Nombor Artikel Antarabangsa”). Jika anda ingin menjual buku di kedai, ia memerlukan kod bar, jadi anda perlu mendapatkan ISBN. Catatan blog Taycher menyebut bahawa walaupun ISBN berguna, ia tidak bersifat universal, kerana ia hanya benar-benar diterima pakai pada pertengahan tahun tujuh puluhan, dan tidak di seluruh dunia. Namun, ISBN mungkin adalah pengenal pasti edisi buku yang paling banyak digunakan, jadi ia adalah titik permulaan terbaik kita. Jika kita dapat mencari semua ISBN di dunia, kita akan mendapat senarai berguna tentang buku mana yang masih perlu dipelihara. Jadi, di mana kita mendapatkan data? Terdapat beberapa usaha sedia ada yang cuba menyusun senarai semua buku di dunia: Dalam pos ini, kami gembira untuk mengumumkan pelepasan kecil (berbanding dengan pelepasan Z-Library kami sebelum ini). Kami telah mengikis kebanyakan ISBNdb, dan menjadikan data tersebut tersedia untuk torrenting di laman web Cermin Perpustakaan Lanun (EDIT: dipindahkan ke <a %(wikipedia_annas_archive)s>Arkib Anna</a>; kami tidak akan memautkannya di sini secara langsung, hanya cari sahaja). Ini adalah kira-kira 30.9 juta rekod (20GB sebagai <a %(jsonlines)s>JSON Lines</a>; 4.4GB dimampatkan). Di laman web mereka, mereka mendakwa bahawa mereka sebenarnya mempunyai 32.6 juta rekod, jadi kami mungkin terlepas beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Walau bagaimanapun, buat masa ini kami tidak akan berkongsi dengan tepat bagaimana kami melakukannya — kami akan meninggalkannya sebagai latihan untuk pembaca. ;-) Apa yang akan kami kongsikan adalah beberapa analisis awal, untuk cuba mendekati anggaran jumlah buku di dunia. Kami melihat tiga set data: set data ISBNdb baru ini, pelepasan metadata asal kami yang kami kikis dari perpustakaan bayangan Z-Library (yang termasuk Library Genesis), dan pembuangan data Open Library. Pembuangan ISBNdb, atau Berapa Banyak Buku Yang Dipelihara Selamanya? Jika kita menghapuskan fail dari perpustakaan bayangan dengan betul, berapa peratus daripada semua buku di dunia yang telah kita pelihara? Kemaskini tentang <a %(wikipedia_annas_archive)s>Arkib Anna</a>, perpustakaan terbuka terbesar dalam sejarah manusia. <em>Reka bentuk semula WorldCat</em> Data <strong>Format?</strong> <a %(blog)s>Kontena Arkib Anna (AAC)</a>, yang pada dasarnya adalah <a %(jsonlines)s>JSON Lines</a> yang dimampatkan dengan <a %(zstd)s>Zstandard</a>, ditambah beberapa semantik yang distandardkan. Kontena ini membungkus pelbagai jenis rekod, berdasarkan pelbagai pengikisan yang kami laksanakan. Setahun yang lalu, kami <a %(blog)s>memulakan</a> untuk menjawab soalan ini: <strong>Apakah peratusan buku yang telah dipelihara secara kekal oleh perpustakaan bayangan?</strong> Mari kita lihat beberapa maklumat asas mengenai data: Sebaik sahaja sebuah buku masuk ke dalam perpustakaan bayangan data terbuka seperti <a %(wikipedia_library_genesis)s>Library Genesis</a>, dan kini <a %(wikipedia_annas_archive)s>Arkib Anna</a>, ia akan dicerminkan di seluruh dunia (melalui torrent), dengan itu secara praktikal memeliharanya selama-lamanya. Untuk menjawab soalan tentang peratusan buku yang telah dipelihara, kita perlu mengetahui penyebutnya: berapa banyak buku yang wujud secara keseluruhan? Dan idealnya kita bukan sahaja mempunyai nombor, tetapi metadata sebenar. Kemudian kita bukan sahaja boleh memadankan mereka dengan perpustakaan bayangan, tetapi juga <strong>mencipta senarai TODO buku yang tinggal untuk dipelihara!</strong> Kita bahkan boleh mula bermimpi tentang usaha crowdsourced untuk menuruni senarai TODO ini. Kami mengikis <a %(wikipedia_isbndb_com)s>ISBNdb</a>, dan memuat turun <a %(openlibrary)s>set data Open Library</a>, tetapi hasilnya tidak memuaskan. Masalah utama adalah bahawa tidak banyak pertindihan ISBN. Lihatlah diagram Venn ini dari <a %(blog)s>kiriman blog kami</a>: Kami sangat terkejut dengan betapa sedikitnya pertindihan antara ISBNdb dan Open Library, kedua-duanya dengan bebasnya memasukkan data dari pelbagai sumber, seperti pengikisan web dan rekod perpustakaan. Jika kedua-duanya melakukan kerja yang baik dalam mencari kebanyakan ISBN di luar sana, bulatan mereka pasti akan mempunyai pertindihan yang ketara, atau satu akan menjadi subset kepada yang lain. Ini membuatkan kami tertanya-tanya, berapa banyak buku yang jatuh <em>sepenuhnya di luar bulatan ini</em>? Kami memerlukan pangkalan data yang lebih besar. Itulah ketika kami menetapkan pandangan kami pada pangkalan data buku terbesar di dunia: <a %(wikipedia_worldcat)s>WorldCat</a>. Ini adalah pangkalan data proprietari oleh organisasi bukan untung <a %(wikipedia_oclc)s>OCLC</a>, yang mengumpulkan rekod metadata dari perpustakaan di seluruh dunia, sebagai pertukaran untuk memberikan perpustakaan tersebut akses kepada set data penuh, dan memaparkan mereka dalam hasil carian pengguna akhir. Walaupun OCLC adalah organisasi bukan untung, model perniagaan mereka memerlukan perlindungan pangkalan data mereka. Baiklah, kami minta maaf untuk mengatakan, rakan-rakan di OCLC, kami akan memberikannya semua. :-) Sepanjang tahun lalu, kami telah mengikis semua rekod WorldCat dengan teliti. Pada mulanya, kami mendapat peluang yang baik. WorldCat baru sahaja melancarkan reka bentuk semula laman web mereka sepenuhnya (pada Ogos 2022). Ini termasuk pembaharuan besar-besaran sistem backend mereka, memperkenalkan banyak kelemahan keselamatan. Kami segera mengambil peluang itu, dan berjaya mengikis ratusan juta (!) rekod dalam beberapa hari sahaja. Selepas itu, kelemahan keselamatan perlahan-lahan diperbaiki satu demi satu, sehingga yang terakhir yang kami temui telah ditampal kira-kira sebulan yang lalu. Pada masa itu kami sudah mempunyai hampir semua rekod, dan hanya mencari rekod yang sedikit lebih berkualiti. Jadi kami merasakan sudah tiba masanya untuk melepaskannya! 1.3B WorldCat scrape <em><strong>TL;DR:</strong> Arkib Anna mengikis semua WorldCat (koleksi metadata perpustakaan terbesar di dunia) untuk membuat senarai TODO buku yang perlu dipelihara.</em> WorldCat Amaran: catatan blog ini telah dihentikan. Kami telah memutuskan bahawa IPFS belum bersedia untuk digunakan secara meluas. Kami masih akan memautkan fail pada IPFS dari Arkib Anna apabila mungkin, tetapi kami tidak akan menjadi hosnya sendiri lagi, dan kami juga tidak mengesyorkan orang lain untuk mencerminkannya menggunakan IPFS. Sila lihat halaman Torrents kami jika anda ingin membantu memelihara koleksi kami. Bantu sebar Z-Library di IPFS muat turun server rakan kongsi SciDB Pinjaman luar Pinjaman luar (cetakan dilumpuhkan) muat turun luaran Teroka metadata Terkandung dalam torrents Kembali  (+%(num)s bonus) belum dibayar telah dibayar dibatalkan tamat tempoh menunggu pengesahan daripada Anna tidak sah Teks di bawah diteruskan dalam Bahasa Inggeris. Pergi Tetapkan semula Ke Hadapan Terakhir Jika alamat emel anda tidak boleh digunakan di forum Libgen, kami mencadangkan anda untuk menggunakan <a %(a_mail)s>Proton Mail</a> (percuma). Anda juga boleh <a %(a_manual)s>meminta secara manual</a> agar akaun anda dapat diaktifkan. (mungkin memerlukan <a %(a_browser)s>pengesahan pelayar</a> — muat turun tanpa had!) Server Rakan Kongsi Pantas #%(number)s (disarankan) (sedikit lebih cepat tetapi dengan senarai menunggu) (pengesahan pelayar tidak diperlukan) (tiada pengesahan pelayar atau senarai menunggu) (tiada senarai menunggu, tetapi boleh menjadi sangat perlahan) Server Rakan Kongsi Lambat #%(number)s Buku Audio Buku komik Buku (fiksyen) Buku (bukan fiksyen) Buku (tidak diketahui) Artikel jurnal Majalah Skor muzik Lain-lain Dokumen piawaian Tidak semua halaman dapat ditukar kepada PDF Ditanda rosak di Libgen.li Tidak dapat dilihat di Libgen.li Tidak dapat dilihat di Fiksyen Libgen.rs Tidak dapat dilihat di Bukan Fiksyen Libgen.rs Menjalankan exiftool gagal pada fail ini Ditandakan sebagai “fail buruk” dalam Z-Library Hilang dari Z-Library Ditandakan sebagai “spam” dalam Z-Library Fail tidak boleh dibuka (cth. fail rosak, DRM) Tuntutan hak cipta Masalah memuat turn (cth. tidak boleh menyambung, mesej ralat, sambungan sangat lambat) Metadata salah (cth. judul, huraian, gambar muka depan) Lain-lain Kualiti rendah (cth. masalah format, kualiti imbas rendah, halaman hilang) Spam / fail perlu dikeluarkan (cth. iklan, kandungan kesat) %(amount)s %(amount_usd)s %(amount)s jumlah %(amount)s %(amount_usd)s jumlah Pembaca Pintar Cendekiawan Celik Pengumpul Data Hebat Arkib Anggun Muat turun bonus Cerlalc Metadata Czech DuXiu 读秀 Indeks eBook EBSCOhost Google Books Goodreads HathiTrust IA Peminjaman Digital Terkawal IA ISBNdb ISBN GRP Libgen.li Tidak termasuk “scimag” Libgen.rs Bukan Fiksyen dan Fiksyen Libby MagzDB Nexus/STC OCLC (WorldCat) OpenLibrary Perpustakaan Negara Rusia Sci-Hub Melalui Libgen.li “scimag” Sci-Hub / Libgen “scimag” Trantor Muat naik ke AA Z-Library Z-Library Cina Tajuk, pengarang, DOI, ISBN, MD5, … Cari Pengarang Penerangan dan komen metadata Edisi Nama fail asal Penerbit (cari medan khusus) Tajuk Tahun diterbitkan Perincian teknikal Syiling ini mempunyai minimum yang lebih tinggi daripada biasa. Sila pilih tempoh masa atau syiling yang lain. Permintaan tidak dapat diselesaikan. Sila cuba semula dalam beberapa minit. Jika isu ini masih berlaku, sila hubungi kami di %(email)s berserta tangkapan skrin. Ralat yang tidak diketahui berlaku. Sila hubungi kami di %(email)s berserta tangkapan skrin. Ralat dalam pemprosesan pembayaran. Sila tunggu sebentar dan cuba lagi. Jika masalah berterusan lebih daripada 24 jam, sila hubungi kami di %(email)s dengan tangkapan skrin. Kami sedang menjalankan pengumpulan dana untuk <a href="https://annas-blog.org/backed-up-the-worlds-largest-comics-shadow-lib.html">menyandarkan</a> perpustakaan bayangan komik terbesar di dunia. Terima kasih atas sokongan anda! <a href="/donate">Derma.</a> Jika anda tidak boleh menderma, pertimbangkan untuk menyokong kami dengan memberitahu rakan-rakan anda, dan mengikuti kami di <a href="https://www.reddit.com/r/Annas_Archive">Reddit</a>, atau <a href="https://t.me/annasarchiveorg">Telegram</a>. Jangan emel kami untuk <a %(a_request)s>meminta buku</a><br>atau muat naik kecil (<10k) <a %(a_upload)s>muat naik</a>. Arkib Anna Tuntutan DMCA / hak cipta Kekal berhubung Reddit Alternatif SLUM (%(unaffiliated)s) tidak berafiliasi Arkib Anna memerlukan bantuan anda! Jika anda menderma sekarang, anda mendapat <strong>dua kali ganda</strong> bilangan muat turun pantas. Ramai yang cuba menjatuhkan kami, tetapi kami melawan kembali. Jika anda menderma bulan ini, anda mendapat <strong>dua kali ganda</strong> jumlah muat turun pantas. Sah sehingga akhir bulan ini. Menyelamatkan pengetahuan manusia: hadiah percutian yang hebat! Keahlian akan dilanjutkan sewajarnya. Pelayan rakan kongsi tidak tersedia kerana penutupan hosting. Mereka sepatutnya beroperasi semula tidak lama lagi. Untuk meningkatkan ketahanan Arkib Anna, kami mencari sukarelawan untuk menjalankan cermin. Kami mempunyai kaedah derma baru yang tersedia: %(method_name)s. Sila pertimbangkan untuk %(donate_link_open_tag)smenderma</a> — menjalankan laman web ini tidak murah, dan derma anda benar-benar membuat perbezaan. Terima kasih banyak. Rujuk rakan, dan anda serta rakan anda akan mendapat %(percentage)s%% muat turun pantas bonus! Kejutkan orang yang disayangi, berikan mereka akaun dengan keahlian. Hadiah Valentine yang sempurna! Ketahui lebih lanjut… Akaun Aktiviti Lanjutan Blog Anna ↗ Perisian Anna ↗ beta Peneroka Kod Datasets Derma Fail yang dimuat turun Soalan Lazim Laman Utama Tingkatkan metadata Data LLM Log masuk / Daftar Sumbangan saya Profil awam Cari Keselamatan Torrent Terjemah ↗ Sukarelawan & Ganjaran Muat turun terkini: 📚&nbsp;Perpustakaan sumber terbuka data terbuka terbesar di dunia. ⭐️&nbsp;Cermin Sci-Hub, Library Genesis, Z-Library, dan banyak lagi. 📈&nbsp;%(book_any)s buku-buku, %(journal_article)s kertas-kertas penyelidikan, %(book_comic)s komik-komik, %(magazine)s majalah-majalah — terpelihara selamanya.  dan  dan banyak lagi DuXiu Perpustakaan Pinjaman Internet Archive LibGen 📚&nbsp;Perpustakaan terbuka terbesar dalam sejarah manusia. 📈&nbsp;%(book_count)s&nbsp;buku-buku, %(paper_count)s&nbsp;kertas penyelidikan — terpelihara selamanya. ⭐️&nbsp;Kami mencermin %(libraries)s. Kami mengikis dan sumber terbuka %(scraped)s. Semua kod dan data kami adalah sumber terbuka sepenuhnya. OpenLib Sci-Hub ,  📚 Perpustakaan sumber terbuka data terbuka terbesar di dunia.<br>⭐️ Cermin Scihub, Libgen, Zlib, dan banyak lagi. Z-Lib Arkib Anna Permintaan tidak sah. Lawati %(websites)s. Perpustakaan sumber terbuka dan data terbuka terbesar di dunia. Mencermin Sci-Hub, Library Genesis, Z-Library, dan banyak lagi. Cari Arkib Anna Arkib Anna Sila segarkan untuk mencuba lagi. <a %(a_contact)s>Hubungi kami</a> jika masalah berterusan selama beberapa jam. 🔥 Masalah memuatkan halaman ini <li>1. Ikuti kami di <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a>, atau <a href="https://t.me/annasarchiveorg">Telegram</a>.</li><li>2. Sebarkan tentang Arkib Anna di Twitter, Reddit, Tiktok, Instagram, di kafe atau perpustakaan tempatan anda, atau di mana sahaja anda pergi! Kami tidak percaya dalam menyekat akses — jika kami diturunkan, kami akan muncul semula di tempat lain, kerana semua kod dan data kami sepenuhnya sumber terbuka.</li><li>3. Jika anda mampu, pertimbangkan untuk <a href="/donate">menderma</a>.</li><li>4. Bantu <a href="https://translate.annas-software.org/">menerjemah</a> laman web kami ke dalam pelbagai bahasa.</li><li>5. Jika anda seorang jurutera perisian, pertimbangkan untuk menyumbang kepada <a href="https://annas-software.org/">sumber terbuka</a> kami, atau menyemai <a href="/datasets">torrents</a> kami.</li> 10. Cipta atau bantu mengekalkan halaman Wikipedia untuk Arkib Anna dalam bahasa anda. 11. Kami sedang mencari untuk meletakkan iklan kecil yang berselera. Jika anda ingin mengiklankan di Arkib Anna, sila maklumkan kepada kami. 6. Jika anda seorang penyelidik keselamatan, kami boleh menggunakan kemahiran anda untuk serangan dan pertahanan. Lihat halaman <a %(a_security)s>Keselamatan</a> kami. 7. Kami sedang mencari pakar dalam pembayaran untuk pedagang tanpa nama. Bolehkah anda membantu kami menambah cara yang lebih mudah untuk menderma? PayPal, WeChat, kad hadiah. Jika anda mengenali sesiapa, sila hubungi kami. 8. Kami sentiasa mencari lebih banyak kapasiti pelayan. 9. Anda boleh membantu dengan melaporkan isu fail, meninggalkan komen, dan membuat senarai terus di laman web ini. Anda juga boleh membantu dengan <a %(a_upload)s>memuat naik lebih banyak buku</a>, atau membaiki isu fail atau format buku sedia ada. Untuk maklumat lebih lanjut tentang cara menjadi sukarelawan, lihat halaman <a %(a_volunteering)s>Sukarelawan & Ganjaran</a> kami. Kami sangat percaya pada aliran bebas maklumat, dan pemeliharaan pengetahuan dan budaya. Dengan enjin carian ini, kami membina di atas bahu gergasi. Kami sangat menghormati kerja keras orang-orang yang telah mencipta pelbagai perpustakaan bayangan, dan kami berharap enjin carian ini akan meluaskan jangkauan mereka. Untuk kekal dikemas kini mengenai kemajuan kami, ikuti Anna di <a href="https://www.reddit.com/r/Annas_Archive/">Reddit</a> atau <a href="https://t.me/annasarchiveorg">Telegram</a>. Untuk soalan dan maklum balas sila hubungi Anna di %(email)s. ID Akaun: %(account_id)s Log keluar ❌ Ada sesuatu yang tidak kena. Sila muat semula halaman dan cuba semula. ✅ Anda telah dilog keluar. Muat semula halaman untuk log masuk semula. Muat turun pantas yang telah diggunakan (24 jam terakhir): <strong>%(used)s / %(total)s</strong> Keahlian: <strong>%(tier_name)s</strong> sehingga %(until_date)s <a %(a_extend)s>(Panjangkan)</a> Anda boleh menggabungkan pelbagai keahlian (muat turun pantas setiap 24 jam akan ditambah bersama). Keahlian: <strong>Tiada</strong> <a %(a_become)s>(jadi ahli)</a> Hubungi Anna di %(email)s jika anda berminat untuk menaik taraf keahlian anda ke peringkat yang lebih tinggi. Profil awam: %(profile_link)s Kunci rahsia (jangan kongsi!): %(secret_key)s tunjuk Sertai kami di sini! Naik taraf ke <a %(a_tier)s>tahap lebih tinggi</a> untuk sertai kumpulan kami. Kumpulan Telegram eksklusif: %(link)s Akaun muat turun yang mana? Log masuk Jangan hilangkan kunci anda! Kunci rahsia tidak sah. Sahkan kunci anda dan cuba semula atau daftar akaun baru di bawah. Kunci rahsia Masukkan kunci rahsia anda untuk log masuk: Akaun berasakan emel lama? Masukkan <a %(a_open)s>emel anda disini</a>. Daftar akaun baharu Belum mempunyai akaun lagi? Pendaftaran berjaya! Kunci rahsia anda ialah: <span %(span_key)s>%(key)s</span> Simpan kunci ini dengan berhati-hati. Jika kunci anda hilang, anda akan kehilangan akses kepada akaun anda. <li %(li_item)s><strong>Penanda.</strong> Anda boleh menanda halaman ini untuk dapatkan semula kunci anda.</li><li %(li_item)s><strong>Muat turun.</strong> Tekan<a %(a_download)s>pautan ini</a> untuk memuat turun kunci anda.</li><li %(li_item)s><strong>Pengurus kata laluan.</strong> Gunakan pengurus kata laluan untuk menyimpan kunci apabila anda memasukkanya di bawah.</li> Log masuk / Daftar Pengesahan pelayar Amaran: kod mempunyai aksara Unicode yang salah, dan mungkin berkelakuan tidak betul dalam pelbagai situasi. Biner mentah boleh dinyahkod daripada perwakilan base64 dalam URL. Penerangan Label Awalan URL untuk kod tertentu Laman Web Kod bermula dengan “%(prefix_label)s” Sila jangan mengikis halaman ini. Sebaliknya kami mengesyorkan <a %(a_import)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami, dan menjalankan <a %(a_software)s>kod sumber terbuka</a> kami. Data mentah boleh diterokai secara manual melalui fail JSON seperti <a %(a_json_file)s>yang ini</a>. Kurang daripada %(count)s rekod URL Am Peneroka Kod Indeks Terokai kod yang ditandakan pada rekod, mengikut awalan. Lajur “rekod” menunjukkan bilangan rekod yang ditandakan dengan kod dengan awalan yang diberikan, seperti yang dilihat dalam enjin carian (termasuk rekod metadata sahaja). Lajur “kod” menunjukkan berapa banyak kod sebenar mempunyai awalan yang diberikan. Awalan kod yang diketahui “%(key)s” Lebih lanjut… Awalan %(count)s rekod sepadan dengan “%(prefix_label)s” page.codes.records_starting_with kod rekod “%%s” akan digantikan dengan nilai kod Cari Arkib Anna Kod URL untuk kod tertentu: “%(url)s” Halaman ini mungkin mengambil masa untuk dijana, sebab itu ia memerlukan captcha Cloudflare. <a %(a_donate)s>Ahli</a> boleh melangkau captcha. Penyalahgunaan dilaporkan: Versi yang lebih baik Adakah anda ingin melaporkan pengguna ini untuk tingkah laku yang kasar atau tidak sesuai? Isu fail: %(file_issue)s komen tersembunyi Balas Laporkan penyalahgunaan Anda telah melaporkan pengguna ini untuk penyalahgunaan. Tuntutan hak cipta ke e-mel ini akan diabaikan; gunakan borang sebagai gantinya. Tunjukkan e-mel Kami sangat mengalu-alukan maklum balas dan soalan anda! Walau bagaimanapun, disebabkan jumlah spam dan emel tidak masuk akal yang kami terima, sila tandakan kotak untuk mengesahkan anda memahami syarat-syarat ini untuk menghubungi kami. Sebarang cara lain untuk menghubungi kami mengenai tuntutan hak cipta akan dipadamkan secara automatik. Untuk tuntutan DMCA / hak cipta, gunakan <a %(a_copyright)s>borang ini</a>. E-mel untuk dihubungi URL di Arkib Anna (diperlukan). Satu per baris. Sila hanya masukkan URL yang menerangkan edisi buku yang sama. Jika anda ingin membuat tuntutan untuk pelbagai buku atau pelbagai edisi, sila hantar borang ini beberapa kali. Tuntutan yang menggabungkan pelbagai buku atau edisi akan ditolak. Alamat (diperlukan) Penerangan jelas mengenai bahan sumber (diperlukan) E-mel (diperlukan) URL kepada bahan sumber, satu per baris (diperlukan). Sila masukkan sebanyak mungkin, untuk membantu kami mengesahkan tuntutan anda (contohnya Amazon, WorldCat, Google Books, DOI). ISBN bahan sumber (jika berkenaan). Satu per baris. Sila hanya masukkan yang sepadan dengan edisi yang anda laporkan tuntutan hak cipta. Nama anda (diperlukan) ❌ Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi. ✅ Terima kasih kerana menghantar tuntutan hak cipta anda. Kami akan menyemaknya secepat mungkin. Sila muat semula halaman untuk menghantar yang lain. <a %(a_openlib)s>Open Library</a> URL bahan sumber, satu per baris. Sila luangkan masa untuk mencari bahan sumber anda di Open Library. Ini akan membantu kami mengesahkan tuntutan anda. Nombor telefon (diperlukan) Pernyataan dan tandatangan (diperlukan) Hantar tuntutan Jika anda mempunyai tuntutan DMCA atau hak cipta lain, sila isi borang ini dengan seberapa tepat yang mungkin. Jika anda menghadapi sebarang masalah, sila hubungi kami di alamat DMCA khusus kami: %(email)s. Sila ambil perhatian bahawa tuntutan yang dihantar melalui e-mel ke alamat ini tidak akan diproses, ia hanya untuk pertanyaan. Sila gunakan borang di bawah untuk menghantar tuntutan anda. Borang tuntutan DMCA / Hak Cipta Contoh rekod di Arkib Anna Torrent oleh Arkib Anna Format Kontena Arkib Anna Skrip untuk mengimport metadata Jika anda berminat untuk mencerminkan set data ini untuk tujuan <a %(a_archival)s>arkib</a> atau <a %(a_llm)s>latihan LLM</a>, sila hubungi kami. Kemas kini terakhir: %(date)s Laman web utama %(source)s Dokumentasi metadata (kebanyakan medan) Fail yang dicerminkan oleh Arkib Anna: %(count)s (%(percent)s%%) Sumber Jumlah fail: %(count)s Jumlah saiz fail: %(size)s Catatan blog kami tentang data ini <a %(duxiu_link)s>Duxiu</a> adalah pangkalan data besar buku yang diimbas, dicipta oleh <a %(superstar_link)s>SuperStar Digital Library Group</a>. Kebanyakannya adalah buku akademik, diimbas untuk menjadikannya tersedia secara digital kepada universiti dan perpustakaan. Untuk penonton berbahasa Inggeris kami, <a %(princeton_link)s>Princeton</a> dan <a %(uw_link)s>University of Washington</a> mempunyai gambaran keseluruhan yang baik. Terdapat juga artikel yang sangat baik memberikan lebih latar belakang: <a %(article_link)s>“Digitizing Chinese Books: A Case Study of the SuperStar DuXiu Scholar Search Engine”</a>. Buku-buku dari Duxiu telah lama dipirate di internet China. Biasanya ia dijual kurang dari satu dolar oleh penjual semula. Ia biasanya diedarkan menggunakan setara Google Drive di China, yang sering digodam untuk membolehkan lebih banyak ruang simpanan. Beberapa butiran teknikal boleh didapati <a %(link1)s>di sini</a> dan <a %(link2)s>di sini</a>. Walaupun buku-buku telah diedarkan secara separa awam, agak sukar untuk memperolehnya dalam jumlah besar. Kami meletakkan ini tinggi dalam senarai TODO kami, dan memperuntukkan beberapa bulan kerja sepenuh masa untuknya. Walau bagaimanapun, pada akhir 2023 seorang sukarelawan yang luar biasa, menakjubkan, dan berbakat menghubungi kami, memberitahu kami bahawa mereka telah melakukan semua kerja ini — dengan kos yang besar. Mereka berkongsi koleksi penuh dengan kami, tanpa mengharapkan apa-apa balasan, kecuali jaminan pemeliharaan jangka panjang. Benar-benar luar biasa. Maklumat lanjut daripada sukarelawan kami (nota mentah): Diadaptasi daripada <a %(a_href)s>catatan blog</a> kami. DuXiu 读秀 %(count)s fail page.datasets.files Set data ini berkait rapat dengan <a %(a_datasets_openlib)s>set data Open Library</a>. Ia mengandungi pengikisan semua metadata dan sebahagian besar fail dari Perpustakaan Peminjaman Digital Terkawal IA. Kemas kini dikeluarkan dalam <a %(a_aac)s>format Kontena Arkib Anna</a>. Rekod-rekod ini dirujuk secara langsung dari set data Open Library, tetapi juga mengandungi rekod yang tidak terdapat dalam Open Library. Kami juga mempunyai beberapa fail data yang dikikis oleh ahli komuniti selama bertahun-tahun. Koleksi ini terdiri daripada dua bahagian. Anda memerlukan kedua-dua bahagian untuk mendapatkan semua data (kecuali torrent yang telah digantikan, yang dicoretkan pada halaman torrent). Perpustakaan Pinjaman Digital keluaran pertama kami, sebelum kami menstandardkan pada format <a %(a_aac)s>Kontena Arkib Anna (AAC)</a>. Mengandungi metadata (sebagai json dan xml), pdf (daripada sistem pinjaman digital acsm dan lcpdf), dan lakaran muka depan. keluaran baru secara beransur-ansur, menggunakan AAC. Hanya mengandungi metadata dengan cap masa selepas 2023-01-01, kerana selebihnya sudah diliputi oleh "ia". Juga semua fail pdf, kali ini daripada sistem pinjaman acsm dan "bookreader" (pembaca web IA). Walaupun nama tidak tepat, kami masih mengisi fail bookreader ke dalam koleksi ia2_acsmpdf_files, kerana mereka saling eksklusif. IA Controlled Digital Lending 98%%+ fail boleh dicari. Misi kami adalah untuk mengarkibkan semua buku di dunia (serta kertas kerja, majalah, dll), dan menjadikannya boleh diakses secara meluas. Kami percaya bahawa semua buku harus dicerminkan secara meluas, untuk memastikan redundansi dan ketahanan. Inilah sebabnya kami mengumpulkan fail dari pelbagai sumber. Sesetengah sumber adalah terbuka sepenuhnya dan boleh dicerminkan secara pukal (seperti Sci-Hub). Yang lain adalah tertutup dan melindungi, jadi kami cuba mengikis mereka untuk “membebaskan” buku mereka. Yang lain berada di antara. Semua data kami boleh <a %(a_torrents)s>ditorrent</a>, dan semua metadata kami boleh <a %(a_anna_software)s>dihasilkan</a> atau <a %(a_elasticsearch)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB. Data mentah boleh diterokai secara manual melalui fail JSON seperti <a %(a_dbrecord)s>ini</a>. Metadata Laman web ISBN Kali terakhir dikemas kini: %(isbn_country_date)s (%(link)s) Sumber Agensi ISBN Antarabangsa secara berkala mengeluarkan julat yang telah diperuntukkan kepada agensi ISBN kebangsaan. Daripada ini, kita boleh menentukan negara, wilayah, atau kumpulan bahasa yang dimiliki ISBN ini. Kami kini menggunakan data ini secara tidak langsung, melalui perpustakaan Python <a %(a_isbnlib)s>isbnlib</a>. Maklumat negara ISBN Ini adalah dump dari banyak panggilan ke isbndb.com semasa September 2022. Kami cuba meliputi semua julat ISBN. Ini adalah kira-kira 30.9 juta rekod. Di laman web mereka, mereka mendakwa bahawa mereka sebenarnya mempunyai 32.6 juta rekod, jadi kami mungkin terlepas beberapa, atau <em>mereka</em> mungkin melakukan sesuatu yang salah. Respons JSON hampir mentah dari pelayan mereka. Satu isu kualiti data yang kami perhatikan adalah bahawa untuk nombor ISBN-13 yang bermula dengan awalan yang berbeza daripada "978-", mereka masih termasuk medan "isbn" yang hanya nombor ISBN-13 dengan tiga nombor pertama dipotong (dan digit semak dikira semula). Ini jelas salah, tetapi begitulah cara mereka melakukannya, jadi kami tidak mengubahnya. Satu lagi isu yang mungkin anda hadapi adalah fakta bahawa medan "isbn13" mempunyai pendua, jadi anda tidak boleh menggunakannya sebagai kunci utama dalam pangkalan data. Gabungan medan "isbn13" + "isbn" nampaknya unik. Keluaran 1 (2022-10-31) Torrents fiksyen ketinggalan (walaupun ID ~4-6M tidak ditorrent kerana bertindih dengan torrents Zlib kami). Catatan blog kami tentang pelepasan buku komik Torrent komik di Arkib Anna Untuk latar belakang tentang pelbagai cabang Library Genesis, lihat halaman untuk <a %(a_libgen_rs)s>Libgen.rs</a>. Libgen.li mengandungi kebanyakan kandungan dan metadata yang sama seperti Libgen.rs, tetapi mempunyai beberapa koleksi tambahan, iaitu komik, majalah, dan dokumen standard. Ia juga telah mengintegrasikan <a %(a_scihub)s>Sci-Hub</a> ke dalam metadata dan enjin carian, yang kami gunakan untuk pangkalan data kami. Metadata untuk perpustakaan ini boleh didapati secara percuma <a %(a_libgen_li)s>di libgen.li</a>. Walau bagaimanapun, pelayan ini lambat dan tidak menyokong penyambungan semula sambungan yang terputus. Fail yang sama juga boleh didapati di <a %(a_ftp)s>pelayan FTP</a>, yang berfungsi dengan lebih baik. Buku bukan fiksyen juga kelihatan telah menyimpang, tetapi tanpa aliran baru. Nampaknya ini telah berlaku sejak awal 2022, walaupun kami belum mengesahkannya. Menurut pentadbir Libgen.li, koleksi “fiction_rus” (fiksyen Rusia) sepatutnya dilindungi oleh torrent yang dikeluarkan secara berkala dari <a %(a_booktracker)s>booktracker.org</a>, terutamanya torrent <a %(a_flibusta)s>flibusta</a> dan <a %(a_librusec)s>lib.rus.ec</a> (yang kami cermin <a %(a_torrents)s>di sini</a>, walaupun kami belum menetapkan torrent mana yang sepadan dengan fail mana). Koleksi fiksyen mempunyai torrent sendiri (berbeza dari <a %(a_href)s>Libgen.rs</a>) bermula pada %(start)s. Julat tertentu tanpa torrent (seperti julat fiksyen f_3463000 hingga f_4260000) mungkin fail Z-Library (atau pendua lain), walaupun kami mungkin ingin melakukan deduplikasi dan membuat torrent untuk fail unik lgli dalam julat ini. Statistik untuk semua koleksi boleh didapati <a %(a_href)s>di laman web libgen</a>. Torrent tersedia untuk kebanyakan kandungan tambahan, terutamanya torrent untuk komik, majalah, dan dokumen standard telah dikeluarkan dengan kerjasama Arkib Anna. Perhatikan bahawa fail torrent yang merujuk kepada “libgen.is” adalah cermin secara eksplisit dari <a %(a_libgen)s>Libgen.rs</a> (“.is” adalah domain berbeza yang digunakan oleh Libgen.rs). Sumber yang berguna dalam menggunakan metadata adalah <a %(a_href)s>halaman ini</a>. %(icon)s Koleksi “fiction_rus” mereka (fiksyen Rusia) tidak mempunyai torrent khusus, tetapi dilindungi oleh torrent dari pihak lain, dan kami menyimpan <a %(fiction_rus)s>cermin</a>. Torrent fiksyen Rusia di Arkib Anna Torrent fiksyen di Arkib Anna Forum perbincangan Metadata Metadata melalui FTP Torrent majalah di Arkib Anna Maklumat medan metadata Cermin dari torrent lain (dan torrent fiksyen dan komik unik) Torrent dokumen standard di Arkib Anna Libgen.li Torrent oleh Arkib Anna (kulit buku) Library Genesis terkenal kerana sudah bermurah hati membuat data mereka tersedia secara pukal melalui torrent. Koleksi Libgen kami terdiri daripada data tambahan yang mereka tidak lepaskan secara langsung, dengan kerjasama mereka. Terima kasih banyak kepada semua yang terlibat dengan Library Genesis kerana bekerjasama dengan kami! Blog kami tentang pelepasan kulit buku Halaman ini adalah tentang versi “.rs”. Ia terkenal kerana secara konsisten menerbitkan metadata dan kandungan penuh katalog bukunya. Koleksi bukunya dibahagikan antara bahagian fiksyen dan bukan fiksyen. Sumber yang berguna dalam menggunakan metadata adalah <a %(a_metadata)s>halaman ini</a> (menyekat julat IP, VPN mungkin diperlukan). Sehingga 2024-03, torrent baru diposkan dalam <a %(a_href)s>benang forum ini</a> (menyekat julat IP, VPN mungkin diperlukan). Torrent Fiksyen di Arkib Anna Torrent Fiksyen Libgen.rs Forum Perbincangan Libgen.rs Metadata Libgen.rs Maklumat medan metadata Libgen.rs Torrent Bukan Fiksyen Libgen.rs Torrent Bukan Fiksyen di Arkib Anna %(example)s untuk buku fiksyen. <a %(blog_post)s>Pelepasan pertama</a> ini agak kecil: kira-kira 300GB kulit buku dari cabang Libgen.rs, kedua-dua fiksyen dan bukan fiksyen. Mereka disusun dengan cara yang sama seperti bagaimana mereka muncul di libgen.rs, contohnya: %(example)s untuk buku bukan fiksyen. Sama seperti dengan koleksi Z-Library, kami meletakkan semuanya dalam fail .tar besar, yang boleh dipasang menggunakan <a %(a_ratarmount)s>ratarmount</a> jika anda ingin menyajikan fail secara langsung. Pelepasan 1 (%(date)s) Kisah ringkas tentang pelbagai cabang Library Genesis (atau “Libgen”) adalah bahawa dari masa ke masa, orang yang terlibat dengan Library Genesis mengalami perpecahan, dan pergi ke arah masing-masing. Menurut <a %(a_mhut)s>kiriman forum</a> ini, Libgen.li pada asalnya dihoskan di “http://free-books.dontexist.com”. Versi “.fun” dicipta oleh pengasas asal. Ia sedang diperbaharui untuk versi baru yang lebih teragih. Versi <a %(a_li)s>“.li”</a> mempunyai koleksi komik yang besar, serta kandungan lain, yang belum (lagi) tersedia untuk muat turun pukal melalui torrent. Ia mempunyai koleksi torrent berasingan untuk buku fiksyen, dan mengandungi metadata <a %(a_scihub)s>Sci-Hub</a> dalam pangkalan datanya. Versi “.rs” mempunyai data yang sangat serupa, dan paling konsisten mengeluarkan koleksi mereka dalam torrent pukal. Ia dibahagikan secara kasar kepada bahagian “fiksyen” dan “bukan fiksyen”. Asalnya di “http://gen.lib.rus.ec”. <a %(a_zlib)s>Z-Library</a> dalam beberapa pengertian juga merupakan cabang dari Library Genesis, walaupun mereka menggunakan nama yang berbeza untuk projek mereka. Libgen.rs Kami juga memperkayakan koleksi kami dengan sumber metadata sahaja, yang boleh kami padankan dengan fail, contohnya menggunakan nombor ISBN atau medan lain. Di bawah adalah gambaran keseluruhan sumber tersebut. Sekali lagi, sesetengah sumber ini adalah terbuka sepenuhnya, manakala untuk yang lain kami perlu mengikisnya. Perhatikan bahawa dalam carian metadata, kami menunjukkan rekod asal. Kami tidak melakukan sebarang penggabungan rekod. Sumber metadata sahaja Open Library adalah projek sumber terbuka oleh Internet Archive untuk mengkatalogkan setiap buku di dunia. Ia mempunyai salah satu operasi pengimbasan buku terbesar di dunia, dan mempunyai banyak buku yang tersedia untuk pinjaman digital. Katalog metadata bukunya tersedia secara percuma untuk dimuat turun, dan disertakan di Arkib Anna (walaupun tidak dalam carian buat masa ini, kecuali jika anda secara eksplisit mencari ID Open Library). Open Library Tidak termasuk pendua Kali terakhir dikemas kini Peratusan bilangan fail %% dicerminkan oleh AA / torrents tersedia Saiz Sumber Di bawah adalah gambaran keseluruhan ringkas tentang sumber fail di Anna’s Archive. Oleh kerana perpustakaan bayangan sering menyelaraskan data antara satu sama lain, terdapat pertindihan yang ketara antara perpustakaan. Itulah sebabnya nombor tidak menjumlahkan kepada jumlah keseluruhan. Peratusan “dicerminkan dan disemai oleh Arkib Anna” menunjukkan berapa banyak fail yang kami cerminkan sendiri. Kami menyemai fail-fail tersebut secara pukal melalui torrent, dan menjadikannya tersedia untuk muat turun terus melalui laman web rakan kongsi. Gambaran Keseluruhan Jumlah Torrent di Arkib Anna Untuk latar belakang mengenai Sci-Hub, sila rujuk kepada <a %(a_scihub)s>laman web rasmi</a>, <a %(a_wikipedia)s>halaman Wikipedia</a>, dan <a %(a_radiolab)s>temu bual podcast</a> ini. Perhatikan bahawa Sci-Hub telah <a %(a_reddit)s>dibekukan sejak 2021</a>. Ia telah dibekukan sebelum ini, tetapi pada tahun 2021 beberapa juta kertas kerja telah ditambah. Namun, beberapa kertas kerja terhad masih ditambah ke dalam koleksi “scimag” Libgen, walaupun tidak mencukupi untuk menjamin torrent pukal baru. Kami menggunakan metadata Sci-Hub seperti yang disediakan oleh <a %(a_libgen_li)s>Libgen.li</a> dalam koleksi “scimag”nya. Kami juga menggunakan dataset <a %(a_dois)s>dois-2022-02-12.7z</a>. Perhatikan bahawa torrent “smarch” adalah <a %(a_smarch)s>usang</a> dan oleh itu tidak termasuk dalam senarai torrent kami. Torrent di Libgen.li Torrent di Libgen.rs Metadata dan torrent Kemas kini di Reddit Temu bual podcast Halaman Wikipedia Sci-Hub Sci-Hub: dibekukan sejak 2021; kebanyakannya tersedia melalui torrents Libgen.li: penambahan kecil sejak itu</div> Sesetengah perpustakaan sumber mempromosikan perkongsian data mereka secara pukal melalui torrents, manakala yang lain tidak berkongsi koleksi mereka dengan mudah. Dalam kes yang terakhir, Arkib Anna cuba untuk mengikis koleksi mereka, dan membuatnya tersedia (lihat halaman <a %(a_torrents)s>Torrents</a> kami). Terdapat juga situasi di antara, contohnya, di mana perpustakaan sumber bersedia untuk berkongsi, tetapi tidak mempunyai sumber untuk berbuat demikian. Dalam kes tersebut, kami juga cuba untuk membantu. Di bawah adalah gambaran keseluruhan bagaimana kami berinteraksi dengan pelbagai perpustakaan sumber. Perpustakaan sumber %(icon)s Pelbagai pangkalan data fail berselerak di internet China; walaupun sering kali pangkalan data berbayar %(icon)s Kebanyakan fail hanya boleh diakses menggunakan akaun premium BaiduYun; kelajuan muat turun yang perlahan. %(icon)s Arkib Anna mengurus koleksi <a %(duxiu)s>fail DuXiu</a> %(icon)s Pelbagai pangkalan data metadata berselerak di internet China; walaupun sering kali pangkalan data berbayar %(icon)s Tiada metadata dumps yang mudah diakses untuk keseluruhan koleksi mereka. %(icon)s Arkib Anna mengurus koleksi <a %(duxiu)s>metadata DuXiu</a> Fail %(icon)s Fail hanya tersedia untuk dipinjam secara terhad, dengan pelbagai sekatan akses %(icon)s Arkib Anna mengurus koleksi <a %(ia)s>fail IA</a> %(icon)s Beberapa metadata tersedia melalui <a %(openlib)s>Open Library database dumps</a>, tetapi ia tidak merangkumi keseluruhan koleksi IA %(icon)s Tiada metadata dumps yang mudah diakses untuk keseluruhan koleksi mereka %(icon)s Arkib Anna mengurus koleksi <a %(ia)s>metadata IA</a> Kali terakhir dikemas kini %(icon)s Arkib Anna dan Libgen.li secara kolaboratif mengurus koleksi <a %(comics)s>buku komik</a>, <a %(magazines)s>majalah</a>, <a %(standarts)s>dokumen standard</a>, dan <a %(fiction)s>fiksyen (berbeza dari Libgen.rs)</a>. %(icon)s Torrent Bukan Fiksyen dikongsi dengan Libgen.rs (dan dicerminkan <a %(libgenli)s>di sini</a>). %(icon)s Dump pangkalan data HTTP suku tahunan <a %(dbdumps)s>di sini</a> %(icon)s Torrent automatik untuk <a %(nonfiction)s>Bukan Fiksyen</a> dan <a %(fiction)s>Fiksyen</a> %(icon)s Arkib Anna menguruskan koleksi <a %(covers)s>torrents kulit buku</a> %(icon)s <a %(dbdumps)s>Pembuangan pangkalan data HTTP</a> harian Metadata %(icon)s Bulanan <a %(dbdumps)s>database dumps</a> %(icon)s Torrent data tersedia <a %(scihub1)s>di sini</a>, <a %(scihub2)s>di sini</a>, dan <a %(libgenli)s>di sini</a> %(icon)s Beberapa fail baru <a %(libgenrs)s>sedang</a> <a %(libgenli)s>ditambah</a> ke “scimag” Libgen, tetapi tidak cukup untuk menjamin torrents baru %(icon)s Sci-Hub telah membekukan fail baru sejak 2021. %(icon)s Dump metadata tersedia <a %(scihub1)s>di sini</a> dan <a %(scihub2)s>di sini</a>, serta sebagai sebahagian daripada <a %(libgenli)s>pangkalan data Libgen.li</a> (yang kami gunakan) Sumber %(icon)s Pelbagai sumber kecil atau satu kali. Kami menggalakkan orang ramai untuk memuat naik ke perpustakaan bayangan lain terlebih dahulu, tetapi kadang-kadang orang mempunyai koleksi yang terlalu besar untuk orang lain menyusun, walaupun tidak cukup besar untuk memerlukan kategori mereka sendiri. %(icon)s Tidak tersedia secara langsung dalam jumlah besar, dilindungi daripada pengikisan %(icon)s Arkib Anna menguruskan koleksi <a %(worldcat)s>metadata OCLC (WorldCat)</a> %(icon)s Arkib Anna dan Z-Library secara bersama-sama mengurus koleksi <a %(metadata)s>metadata Z-Library</a> dan <a %(files)s>fail Z-Library</a> Datasets Kami menggabungkan semua sumber di atas ke dalam satu pangkalan data bersatu yang kami gunakan untuk menyajikan laman web ini. Pangkalan data bersatu ini tidak tersedia secara langsung, tetapi kerana Arkib Anna adalah sumber terbuka sepenuhnya, ia boleh dengan mudah <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB. Skrip di halaman tersebut akan secara automatik memuat turun semua metadata yang diperlukan dari sumber yang disebutkan di atas. Jika anda ingin meneroka data kami sebelum menjalankan skrip tersebut secara tempatan, anda boleh melihat fail JSON kami, yang menghubungkan lebih lanjut kepada fail JSON lain. <a %(a_json)s>Fail ini</a> adalah titik permulaan yang baik. Pangkalan data bersatu Torrent oleh Arkib Anna semak imbas cari Pelbagai sumber kecil atau sekali sahaja. Kami menggalakkan orang ramai untuk memuat naik ke perpustakaan bayangan lain terlebih dahulu, tetapi kadang-kadang orang mempunyai koleksi yang terlalu besar untuk orang lain menyusun, walaupun tidak cukup besar untuk menjamin kategori mereka sendiri. Gambaran dari <a %(a1)s>halaman datasets</a>. Daripada <a %(a_href)s>aaaaarg.fail</a>. Nampaknya agak lengkap. Daripada sukarelawan kami “cgiym”. Daripada torrent <a %(a_href)s><q>ACM Digital Library 2020</q></a>. Mempunyai pertindihan yang agak tinggi dengan koleksi kertas sedia ada, tetapi sangat sedikit padanan MD5, jadi kami memutuskan untuk menyimpannya sepenuhnya. Pengikisan <q>iRead eBooks</q> (= secara fonetik <q>ai rit i-books</q>; airitibooks.com), oleh sukarelawan <q>j</q>. Berkaitan dengan metadata <q>airitibooks</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>. Dari koleksi <a %(a1)s><q>Bibliotheca Alexandrina</q></a>. Sebahagiannya dari sumber asal, sebahagiannya dari the-eye.eu, sebahagiannya dari cermin lain. Daripada laman web torrent buku persendirian, <a %(a_href)s>Bibliotik</a> (sering dirujuk sebagai “Bib”), di mana buku-buku dibundel ke dalam torrent mengikut nama (A.torrent, B.torrent) dan diedarkan melalui the-eye.eu. Daripada sukarelawan kami “bpb9v”. Untuk maklumat lanjut mengenai <a %(a_href)s>CADAL</a>, lihat nota dalam <a %(a_duxiu)s>halaman set data DuXiu</a> kami. Lebih banyak daripada sukarelawan kami “bpb9v”, kebanyakannya fail DuXiu, serta folder “WenQu” dan “SuperStar_Journals” (SuperStar adalah syarikat di belakang DuXiu). Daripada sukarelawan kami “cgiym”, teks Cina daripada pelbagai sumber (diwakili sebagai subdirektori), termasuk daripada <a %(a_href)s>China Machine Press</a> (penerbit utama Cina). Koleksi bukan Cina (diwakili sebagai subdirektori) daripada sukarelawan kami “cgiym”. Pengikisan buku tentang seni bina Cina, oleh sukarelawan <q>cm</q>: <q>Saya mendapatkannya dengan mengeksploitasi kelemahan rangkaian di rumah penerbitan, tetapi kelemahan itu telah ditutup</q>. Berkaitan dengan metadata <q>chinese_architecture</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>. Buku daripada penerbit akademik <a %(a_href)s>De Gruyter</a>, dikumpulkan daripada beberapa torrent besar. Pengikisan <a %(a_href)s>docer.pl</a>, laman web perkongsian fail Poland yang memfokuskan pada buku dan karya bertulis lain. Dikikis pada akhir 2023 oleh sukarelawan “p”. Kami tidak mempunyai metadata yang baik daripada laman web asal (malah tidak ada sambungan fail), tetapi kami menapis fail yang kelihatan seperti buku dan sering kali dapat mengekstrak metadata daripada fail itu sendiri. DuXiu epubs, terus dari DuXiu, dikumpulkan oleh sukarelawan “w”. Hanya buku DuXiu terkini yang tersedia secara langsung melalui ebook, jadi kebanyakan ini mesti terkini. Fail DuXiu yang tinggal daripada sukarelawan “m”, yang tidak berada dalam format proprietari PDG DuXiu (set data utama <a %(a_href)s>DuXiu</a>). Dikumpulkan daripada banyak sumber asal, malangnya tanpa mengekalkan sumber tersebut dalam laluan fail. <span></span> <span></span> <span></span> Pengikisan buku erotik, oleh sukarelawan <q>do no harm</q>. Berkaitan dengan metadata <q>hentai</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>. <span></span> <span></span> Koleksi yang dikikis daripada penerbit Manga Jepun oleh sukarelawan “t”. <a %(a_href)s>Arkib kehakiman terpilih Longquan</a>, disediakan oleh sukarelawan “c”. Pengikisan <a %(a_href)s>magzdb.org</a>, sekutu Library Genesis (ia dipautkan pada halaman utama libgen.rs) tetapi yang tidak mahu menyediakan fail mereka secara langsung. Diperoleh oleh sukarelawan “p” pada akhir 2023. <span></span> Pelbagai muat naik kecil, terlalu kecil sebagai subkoleksi mereka sendiri, tetapi diwakili sebagai direktori. Ebook dari AvaxHome, sebuah laman web perkongsian fail Rusia. Arkib surat khabar dan majalah. Berkaitan dengan metadata <q>newsarch_magz</q> dalam <a %(a1)s><q>Pengikisan metadata lain</q></a>. Pengikisan <a %(a1)s>Pusat Dokumentasi Falsafah</a>. Koleksi sukarelawan “o” yang mengumpulkan buku Poland secara langsung daripada laman web pelepasan asal (“scene”). Koleksi gabungan <a %(a_href)s>shuge.org</a> oleh sukarelawan “cgiym” dan “woz9ts”. <span></span> <a %(a_href)s>“Perpustakaan Imperial Trantor”</a> (dinamakan sempena perpustakaan fiksyen), dikikis pada 2022 oleh sukarelawan “t”. <span></span> <span></span> <span></span> Sub-sub-koleksi (diwakili sebagai direktori) daripada sukarelawan “woz9ts”: <a %(a_program_think)s>program-think</a>, <a %(a_haodoo)s>haodoo</a>, <a %(a_skqs)s>skqs</a> (oleh <a %(a_sikuquanshu)s>Dizhi(迪志)</a> di Taiwan), mebook (mebook.cc, 我的小书屋, bilik buku kecil saya — woz9ts: “Laman ini terutamanya memfokuskan pada perkongsian fail ebook berkualiti tinggi, beberapa daripadanya disusun oleh pemiliknya sendiri. Pemiliknya <a %(a_arrested)s>ditangkap</a> pada 2019 dan seseorang membuat koleksi fail yang dikongsinya.”). Fail DuXiu yang tinggal daripada sukarelawan “woz9ts”, yang tidak dalam format PDG proprietari DuXiu (masih perlu ditukar kepada PDF). Koleksi “muat naik” dibahagikan kepada subkoleksi yang lebih kecil, yang ditunjukkan dalam AACID dan nama torrent. Semua subkoleksi pertama kali diduplikasi terhadap koleksi utama, walaupun fail JSON “upload_records” metadata masih mengandungi banyak rujukan kepada fail asal. Fail bukan buku juga telah dikeluarkan daripada kebanyakan subkoleksi, dan biasanya <em>tidak</em> dinyatakan dalam “upload_records” JSON. Subkoleksi adalah: Nota Subkoleksi Banyak subkoleksi itu sendiri terdiri daripada sub-sub-koleksi (contohnya daripada sumber asal yang berbeza), yang diwakili sebagai direktori dalam medan “filepath”. Muat naik ke Arkib Anna Catatan blog kami mengenai data ini <a %(a_worldcat)s>WorldCat</a> adalah pangkalan data proprietari oleh organisasi bukan untung <a %(a_oclc)s>OCLC</a>, yang mengumpulkan rekod metadata dari perpustakaan di seluruh dunia. Ia mungkin koleksi metadata perpustakaan terbesar di dunia. Pada Oktober 2023 kami <a %(a_scrape)s>melepaskan</a> pengikisan menyeluruh pangkalan data OCLC (WorldCat), dalam <a %(a_aac)s>format Kontena Arkib Anna</a>. Oktober 2023, keluaran awal: OCLC (WorldCat) Torrent oleh Arkib Anna Contoh rekod di Arkib Anna (koleksi asal) Contoh rekod di Arkib Anna (koleksi “zlib3”) Torrent oleh Arkib Anna (metadata + kandungan) Catatan blog tentang Keluaran 1 Catatan blog tentang Keluaran 2 Pada akhir 2022, pengasas Z-Library yang didakwa telah ditangkap, dan domain telah dirampas oleh pihak berkuasa Amerika Syarikat. Sejak itu laman web tersebut perlahan-lahan kembali dalam talian. Tidak diketahui siapa yang mengendalikannya sekarang. Kemas kini setakat Februari 2023. Z-Library berakar umbi dalam komuniti <a %(a_href)s>Library Genesis</a>, dan pada asalnya dimulakan dengan data mereka. Sejak itu, ia telah menjadi lebih profesional, dan mempunyai antara muka yang lebih moden. Oleh itu, mereka mampu mendapatkan lebih banyak sumbangan, baik dari segi kewangan untuk terus memperbaiki laman web mereka, serta sumbangan buku baru. Mereka telah mengumpulkan koleksi besar selain daripada Library Genesis. Koleksi ini terdiri daripada tiga bahagian. Halaman penerangan asal untuk dua bahagian pertama dipelihara di bawah. Anda memerlukan ketiga-tiga bahagian untuk mendapatkan semua data (kecuali torrent yang telah digantikan, yang dicoretkan pada halaman torrent). %(title)s: keluaran pertama kami. Ini adalah keluaran pertama yang pada masa itu dipanggil “Pirate Library Mirror” (“pilimi”). %(title)s: keluaran kedua, kali ini dengan semua fail dibungkus dalam fail .tar. %(title)s: keluaran baru tambahan, menggunakan format <a %(a_href)s>Kontena Arkib Anna (AAC)</a>, kini dikeluarkan dengan kerjasama pasukan Z-Library. Cermin awal diperoleh dengan susah payah sepanjang tahun 2021 dan 2022. Pada ketika ini ia sedikit ketinggalan zaman: ia mencerminkan keadaan koleksi pada Jun 2021. Kami akan mengemas kini ini pada masa hadapan. Buat masa ini kami fokus untuk mengeluarkan keluaran pertama ini. Memandangkan Library Genesis sudah dipelihara dengan torrents awam, dan termasuk dalam Z-Library, kami melakukan deduplikasi asas terhadap Library Genesis pada Jun 2022. Untuk ini, kami menggunakan hash MD5. Kemungkinan terdapat banyak kandungan duplikat dalam perpustakaan, seperti pelbagai format fail dengan buku yang sama. Ini sukar untuk dikesan dengan tepat, jadi kami tidak melakukannya. Selepas deduplikasi, kami mempunyai lebih daripada 2 juta fail, dengan jumlah keseluruhan hampir 7TB. Koleksi ini terdiri daripada dua bahagian: dump MySQL “.sql.gz” metadata, dan 72 fail torrent sekitar 50-100GB setiap satu. Metadata mengandungi data seperti yang dilaporkan oleh laman web Z-Library (tajuk, pengarang, deskripsi, jenis fail), serta saiz fail sebenar dan md5sum yang kami perhatikan, kerana kadang-kadang ini tidak sepadan. Terdapat julat fail yang metadata Z-Library sendiri tidak betul. Kami mungkin juga telah memuat turun fail yang salah dalam beberapa kes terpencil, yang akan kami cuba kesan dan betulkan pada masa hadapan. Fail torrent besar mengandungi data buku sebenar, dengan ID Z-Library sebagai nama fail. Sambungan fail boleh dibina semula menggunakan dump metadata. Koleksi ini adalah campuran kandungan bukan fiksyen dan fiksyen (tidak dipisahkan seperti dalam Library Genesis). Kualitinya juga sangat berbeza. Keluaran pertama ini kini tersedia sepenuhnya. Perhatikan bahawa fail torrent hanya tersedia melalui cermin Tor kami. Keluaran 1 (%(date)s) Ini adalah satu fail torrent tambahan. Ia tidak mengandungi sebarang maklumat baru, tetapi ia mempunyai beberapa data di dalamnya yang boleh mengambil masa untuk dikira. Ini menjadikannya mudah untuk dimiliki, kerana memuat turun torrent ini sering lebih cepat daripada mengiranya dari awal. Khususnya, ia mengandungi indeks SQLite untuk fail tar, untuk digunakan dengan <a %(a_href)s>ratarmount</a>. Pelepasan 2 addendum (%(date)s) Kami telah mendapatkan semua buku yang ditambah ke Z-Library antara cermin terakhir kami dan Ogos 2022. Kami juga telah kembali dan mengikis beberapa buku yang kami terlepas kali pertama. Secara keseluruhan, koleksi baru ini adalah sekitar 24TB. Sekali lagi, koleksi ini dideduplikasi terhadap Library Genesis, kerana sudah ada torrents tersedia untuk koleksi tersebut. Data diatur serupa dengan pelepasan pertama. Terdapat dump MySQL “.sql.gz” metadata, yang juga termasuk semua metadata dari pelepasan pertama, dengan itu menggantikannya. Kami juga menambah beberapa lajur baru: Kami menyebutkan ini kali terakhir, tetapi hanya untuk menjelaskan: “filename” dan “md5” adalah sifat sebenar fail, manakala “filename_reported” dan “md5_reported” adalah apa yang kami kikis dari Z-Library. Kadang-kadang kedua-duanya tidak sepadan, jadi kami menyertakan kedua-duanya. Untuk pelepasan ini, kami menukar collation kepada “utf8mb4_unicode_ci”, yang sepatutnya serasi dengan versi MySQL yang lebih lama. Fail data adalah serupa dengan kali terakhir, walaupun mereka jauh lebih besar. Kami tidak dapat diganggu untuk mencipta banyak fail torrent yang lebih kecil. “pilimi-zlib2-0-14679999-extra.torrent” mengandungi semua fail yang kami terlepas dalam pelepasan terakhir, manakala torrents lain adalah semua julat ID baru.  <strong>Kemas kini %(date)s:</strong> Kami membuat kebanyakan torrents kami terlalu besar, menyebabkan klien torrent menghadapi masalah. Kami telah mengeluarkannya dan melepaskan torrents baru. <strong>Kemas kini %(date)s:</strong> Masih terdapat terlalu banyak fail, jadi kami membungkusnya dalam fail tar dan melepaskan torrents baru sekali lagi. %(key)s: sama ada fail ini sudah ada di Library Genesis, dalam koleksi bukan fiksyen atau fiksyen (dipadankan oleh md5). %(key)s: torrent mana fail ini berada. %(key)s: ditetapkan apabila kami tidak dapat memuat turun buku tersebut. Keluaran 2 (%(date)s) Keluaran Zlib (halaman penerangan asal) Domain Tor Laman web utama Scrape Z-Library Koleksi “Cina” dalam Z-Library nampaknya sama dengan koleksi DuXiu kami, tetapi dengan MD5 yang berbeza. Kami mengecualikan fail-fail ini daripada torrents untuk mengelakkan penduaan, tetapi masih menunjukkannya dalam indeks carian kami. Metadata Anda mendapat %(percentage)s%% muat turun bonus pantas, kerana anda dirujuk oleh pengguna %(profile_link)s. Ini terpakai untuk keseluruhan tempoh keahlian. Derma Sertai Dipilih Diskaun sehingga %(percentage)s Alipay menyokong kad kredit/debit antarabangsa. Lihat <a %(a_alipay)s>panduan ini</a> untuk maklumat lanjut. Hantar kad hadiah Amazon.com kepada kami menggunakan kad kredit/debit anda. Anda boleh membeli kripto menggunakan kad kredit/debit. WeChat (Weixin Pay) menyokong kad kredit/debit antarabangsa. Dalam aplikasi WeChat, pergi ke “Me => Services => Wallet => Add a Card”. Jika anda tidak melihatnya, aktifkan menggunakan “Me => Settings => General => Tools => Weixin Pay => Enable”. (gunakan apabila menghantar Ethereum dari Coinbase) disalin! salin (jumlah minimum terendah) (amaran: jumlah minimum yang tinggi) -%(percentage)s%% 12 bulan 1 bulan 24 bulan 3 bulan 48 bulan 6 bulan 96 bulan Pilih tempoh langganan anda. <div %(div_monthly_cost)s></div><div %(div_after)s>selepas <span %(span_discount)s></span> diskaun</div><div %(div_total)s></div><div %(div_duration)s></div> %(percentage)s%% untuk 12 bulan untuk 1 bulan untuk 24 bulan untuk 3 bulan untuk 48 bulan untuk 6 bulan untuk 96 bulan %(monthly_cost)s / sebulan hubungi kami Pelayan <strong>SFTP</strong> langsung Derma peringkat perusahaan atau pertukaran koleksi baharu (cth. imbasan-imbasan baharu, dataset OCR). Akses Pakar Akses berkelajuan tinggi <strong>tanpa had</strong> <div %(div_question)s>Bolehkah saya menaik taraf keahlian saya atau mendapatkan pelbagai keahlian?</div> <div %(div_question)s>Bolehkah saya membuat derma tanpa menjadi ahli?</div> Sudah tentu. Kami menerima derma dalam apa jua jumlah di alamat Monero (XMR) ini: %(address)s. <div %(div_question)s>Apakah maksud julat per bulan?</div> Anda boleh mencapai bahagian bawah julat dengan menggunakan semua diskaun, seperti memilih tempoh lebih lama daripada sebulan. <div %(div_question)s>Adakah keahlian akan diperbaharui secara automatik?</div> Keahlian <strong> tidak akan</strong> diperbaharui secara automatik. Anda boleh menyertai seberapa lama atau pendek yang anda mahu. <div %(div_question)s>Bagaimana anda membelanjakan derma yang diberi?</div> 100%% derma akan digunakan untuk memelihara dan menjadikan pengetahuan dan budaya dunia lebih mudah diakses. Pada masa ini kebanyakkan derma dibelanjakan untuk pelayan, storan dan jalur lebar. Tiada derma yang akan diberikan kepada mana-mana ahli pasukan secara peribadi. <div %(div_question)s>Bolehkah saya membuat derma yang besar?</div> Itu pasti menakjubkan! Untuk menderma lebih daripada beberapa ribu ringgit, sila hubungi kami terus di %(email)s. <div %(div_question)s>Adakah anda ada kaedah pembayaran lain?</div> Setakat ini tiada. Ramai pihak tidak mahu arkib seperti ini wujud, jadi kami terpaksa berhati-hati. Jika anda boleh membantu kami untuk menyediakan kaedah pembayaran (yang lebih mudah) dengan selamat, sila hubungi kami di %(email)s. FAQ Derma Anda ada dermaan sebanyak %(a_donation)s yang belum selesai. Sila selesaikan atau batalkan dermaan tersebut sebelum membuat dermaan yang baharu. <a %(a_all_donations)s> Lihat semua sumbangan saya</a> Untuk dermaan melebihi $5000, sila hubungi kami terus di %(email)s. Kami mengalu-alukan sumbangan besar daripada individu atau institusi yang kaya.  Sila ambil perhatian bahawa walaupun keahlian di halaman ini adalah "sebulan", ia adalah derma sekali sahaja (tidak berulang). Lihat <a %(faq)s>FAQ Derma</a>. Anna's Archive ialah projek tanpa untung, sumber terbuka dan data terbuka. Dengan menderma dan daftar menjadi ahli, anda boleh menyokong operasi dan pembangunan kami. Kepada semua ahli-ahli kami: Terima kasih kerana menyokong kami! ❤️ Untuk maklumat lanjut, lihat <a %(a_donate)s>FAQ Derma</a>. Untuk menjadi ahli, sila <a %(a_login)s> Log masuk atau Daftar</a>. Terima kasih atas sokongan anda! $%(cost)s / bulan Jika anda membuat kesilapan semasa pembayaran, kami tidak boleh membuat bayaran balik, tetapi kami akan cuba menyelesaikannya. Cari halaman “Crypto” di aplikasi atau laman web PayPal anda. Halaman boleh dijumpai di bawah “Finances”. Pergi ke halaman “Bitcoin” di aplikasi atau laman web PayPal anda. Tekan butang “Transfer” %(transfer_icon)s dan seterusnya tekan butang “Send”. Alipay Alipay 支付宝 / WeChat 微信 Kad Hadiah Amazon %(amazon)s kad hadiah Kad bank Kad bank (menggunakan aplikasi) Binance Kredit/debit/Apple/Google (BMC) Cash App Kad kredit/debit Kad kredit/debit 2 Kad kredit/debit (sandaran) Kripto %(bitcoin_icon)s Kad / PayPal / Venmo PayPal (US) %(bitcoin_icon)s PayPal PayPal (biasa) Pix (Brazil) Revolut (tidak boleh diakses buat sementara waktu) WeChat Sila pilih mata wang kripto pilihan anda: Derma melalui kad hadiah Amazon. <strong>PENTING:</strong> Pilihan ini adalah untuk %(amazon)s. Jika anda ingin menggunakan laman web Amazon yang lain, pilih di atas. <strong>PENTING:</strong> Kami hanya menyokong Amazon.com dan bukan laman-laman Amazon lain. Contoh, de, .co.uk, .ca, tidak disokong oleh laman kami. Tolong JANGAN tulis mesej anda sendiri. Masukkan jumlah yang tepat: %(amount)s Sila ambil maklum bahawa kami perlu bulatkan jumlah kepada jumlah yang boleh diterima oleh penjual semula kami (minima %(minimum)s). Derma menggunakan kad kredit/debit, melalui aplikasi Alipay (sangat mudah untuk disediakan). Pasang aplikasi Alipay dari <a %(a_app_store)s>Apple App Store</a> atau <a %(a_play_store)s>Google Play Store</a>. Daftar menggunakan nombor telefon anda. Tiada butiran peribadi lanjut diperlukan. <span %(style)s>1</span>Pasang aplikasi Alipay Disokong: Visa, MasterCard, JCB, Diners Club dan Discover. Lihat <a %(a_alipay)s>panduan ini</a> untuk maklumat lanjut. <span %(style)s>2</span>Tambah kad bank Dengan Binance, anda membeli Bitcoin dengan kad kredit/debit atau akaun bank, dan kemudian menderma Bitcoin itu kepada kami. Dengan cara itu kami boleh kekal selamat dan tanpa nama semasa menerima sumbangan anda. Binance tersedia di hampir setiap negara, dan menyokong kebanyakan bank dan kad kredit/debit. Ini adalah cadangan utama kami buat masa ini. Kami menghargai anda meluangkan masa untuk belajar cara menderma menggunakan kaedah ini, kerana ia sangat membantu kami. Untuk kad kredit, kad debit, Apple Pay, dan Google Pay, kami menggunakan “Buy Me a Coffee” (BMC <span class="icon-[ph--coffee-fill] text-lg align-text-bottom"></span>). Dalam sistem mereka, satu “kopi” adalah bersamaan dengan $5, jadi derma anda akan dibundarkan kepada gandaan terdekat 5. Derma melalui Cash App. Jika anda mempunyai Cash App, ini adalah cara termudah untuk menderma! Ambil maklum bahawa untuk transaksi kurang daripada %(amount)s, Cash App akan mengenakan caj %(fee)s. Untuk jumlah transaksi lebih daripada %(amount)s, transaksi adalah percuma! Derma melalui kad kredit atau kad debit. Kaedah ini menggunakan penyedia mata wang kripto sebagai penukaran perantaraan. Ini mungkin agak mengelirukan, jadi sila gunakan kaedah ini hanya jika kaedah pembayaran lain tidak berfungsi. Ia juga tidak berfungsi di semua negara. Kami tidak dapat menyokong kad kredit/debit secara langsung, kerana bank tidak mahu bekerjasama dengan kami. ☹ Walau bagaimanapun, terdapat beberapa cara untuk menggunakan kad kredit/debit, menggunakan kaedah pembayaran lain: Dengan menggunakan kripto, and boleh menderma menggunakan BTC, ETH, XMR, dan SOL. Gunakan kaedah ini jika and sudah biasa dengan mata wang kripto. Dengan menggunakan kripto, anda boleh menderma menggunakan BTC, ETH, XMR dan lain-lain. Perkhidmatan ekspres kripto Jika anda menggunakan kripto untuk kali pertama, kami mencadangkan menggunakan %(options)s untuk membeli dan menderma Bitcoin (mata wang kripto asal dan paling banyak digunakan). Sila ambil maklum untuk jumlah sumbangan yang kecil, caj kad kredit boleh menghapuskan diskaun %(discount)s%% kami. Jadi kami mencadangkan langganan yang lebih lama. Derma menggunakan kad kredit/debit, PayPal, atau Venmo. Anda boleh memilih antara ini di halaman seterusnya. Google Pay dan Apple Pay juga mugkin boleh digunakan. Sila ambil maklum untuk sumbangan yang kecil, caj yang dikenakan akan menjadi lebih tinggi. Jadi kami mencadangkan langganan yang lebih lama. Untuk menderma menerusi PayPal US, kami akan menggunakan PayPal Kripto yang membolehkan kami untuk kekal tanpa nama. Kami menghargai usaha anda untuk belajar menggunakan kaedah ini kerana ia banyak membantu kami. Derma melalui Paypal. Derma menggunakan akaun PayPal biasa anda. Derma menggunakan Revolut. Jika anda mempunyai Revolut, ini adalah cara paling mudah untuk menderma! Kaedah pembayaran ini hanya membenarkan jumlah maksimum sebanyak %(amount)s. Sila pilih tempoh langganan yang berbeza atau kaedah pembayaran yang lain. Kaedah pembayaran ini memerlukan jumlah minima %(amount)s. Silih pilih tempoh langganan yang berbeza atau kaedah pembayaran yang lain. Binance Coinbase Kraken Sila pilih kaedah pembayaran. “Adopt a torrent”: nama pengguna atau mesej di dalam nama fail torrent <div %(div_months)s>sekali setiap 12 bulan keahlian</div> Nama pengguna anda atau sebutan tanpa nama dalam kredit Akses awal kepada ciri - ciri baharu Telegram eksklusif untuk kemas kini di belakang tabir %(number)s muat turun dalam sehari jika anda menderma bulan ini! Akses <a %(a_api)s>JSON API</a> Status legenda dalam pemeliharaan pengetahuan dan budaya manusia Faedah sebulumnya, termasuk: Dapatkan <strong>%(percentage)s%% muat turun bonus</strong> dengan <a %(a_refer)s>merujuk rakan</a>. kertas SciDB <strong>tanpa had</strong> tanpa verifikasi Apabila bertanya soalan akaun atau derma, sertakan ID akaun anda, tangkapan skrin, resit, sebanyak mungkin maklumat. Kami hanya memeriksa emel kami setiap 1-2 minggu, jadi tidak menyertakan maklumat ini akan melambatkan sebarang penyelesaian. Untuk mendapatkan lebih banyak muat turun, <a %(a_refer)s>rujuk rakan anda</a>! Kami adalah pasukan kecil sukarelawan. Ia mungkin mengambil masa 1-2 minggu untuk kami memberi maklum balas. Ambil maklum bahawa nama akaun atau gambar mungkin nampak pelik. Jangan risau! Akaun-akaun ini diuruskan oleh rakan derma kami. Akaun kami tidak digodam. Derma <span %(span_cost)s></span> <span %(span_label)s></span> “%(tier_name)s” untuk 12 bulan “%(tier_name)s” untuk 1 bulan “%(tier_name)s” untuk 24 bulan “%(tier_name)s” untuk 3 bulan untuk 48 bulan “%(tier_name)s” “%(tier_name)s” untuk 6 bulan untuk 96 bulan “%(tier_name)s” Anda masih boleh membatalkan dermaan ini semasa pembayaran. Tekan butang derma untuk mengesahkan dermaan ini. <strong>Maklumat penting:</strong> Harga kripto boleh berubah secara mendadak, kadang-kadang sehingga 20%% dalam masa beberapa minit. Ini masih kurang daripada caj yang kami tanggung dengan kaedah pembayaran yang lain yang sering mengecaj sehingga 50-60%% untuk bekerja dengan "badan amal bayangan" seperti kami. <u>Jika anda menghantar kepada kami resit dengan harga asal yang anda bayar, kami masih akan mengkreditkan akaun anda berdasarkan keahlian yang telah dipilih</u> (selagi resit tidak melebihi beberapa jam). Kami amat menghargai kesanggupan anda untuk bersabar dengan perkara seperti ini untuk menyokong kami! ❤️ ❌ Ralat. Sila muat semula halaman dan cuba sekali lagi. <span %(span_circle)s>1</span>Beli Bitcoin melalui Paypal <span %(span_circle)s>2</span>Pindah Bitcoin ke alamat kami ✅ Mengubah hala ke halaman derma… Derma Sila tunggu sekurang-kurangnya <span %(span_hours)s>24 jam</span> (dan muat semula halaman ini) sebelum menghubungi kami. Jika anda ingin membuat sumbangan (sebarang jumlah) tanpa keahlian, sila gunakan alamat Monero (XMR) ini: %(address)s. Selepas menghantar kad hadiah anda, sistem automatik kami akan mengesahkan dalam beberapa minit. Jika tidak berjaya, cuba menghantar kad hadiah anda semula (<a %(a_instr)s>arahan</a>). Jika masih tidak berjaya, sila emel kami dan Anna akan menyemaknya secara manual (ini akan mengambil masa beberapa hari) dan pastikan anda maklumkan jika anda telah cuba menghantar semula. Contoh: Sila gunakan <a %(a_form)s> borang Amazon.com rasmi</a> untuk menghantar kad hadiah berjumlah %(amount)s kepada alamat e-mel kami di bawah. “Kepada” emel penerima di dalam borang: Kad hadiah Amazon Kami tidak boleh menerima kaedah kad hadiah yang lain, <strong>hanya hantar terus kad hadiah dari borang rasmi Amazon.com</strong>. Kami tidak boleh memulangkan semula kad hadiah anda jika anda tidak menggunakan borang rasmi. Hanya guna sekali. Unik kepada akaun anda, jangan kongsi. Menunggu kad hadiah... (muat semula halaman untuk semak) Buka <a %(a_href)s>halaman sumbangan kod QR</a>. Imbas kod QR dengan aplikasi Alipay, atau tekan butang untuk membuka aplikasi Alipay. Sila bersabar; halaman mungkin mengambil masa untuk dimuatkan kerana ia berada di China. <span %(style)s>3</span>Buat sumbangan (imbas kod QR atau tekan butang) Beli syiling PYUSD melalui PayPal Beli Bitcoin (BTC) di Cash App Beli sedikit lebih (kami mengesyorkan %(more)s lebih) daripada jumlah yang anda derma (%(amount)s), untuk menampung yuran transaksi. Anda akan menyimpan baki yang tinggal. Pergi ke halaman “Bitcoin” (BTC) dalam Cash App. Pindahkan Bitcoin ke alamat kami Untuk derma kecil (di bawah $25), anda mungkin perlu menggunakan Rush atau Priority. Klik butang “Hantar bitcoin” untuk membuat “pengeluaran”. Tukar dari dolar ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah dan klik “Hantar”. Lihat <a %(help_video)s>video ini</a> jika anda tersekat. Perkhidmatan ekspres adalah mudah, tetapi mengenakan yuran yang lebih tinggi. Anda boleh menggunakan ini sebagai ganti pertukaran kripto jika anda ingin membuat sumbangan yang lebih besar dengan cepat dan tidak kisah dengan yuran $5-10. Pastikan untuk menghantar jumlah kripto yang tepat seperti yang ditunjukkan pada halaman sumbangan, bukan jumlah dalam $USD. Jika tidak, yuran akan ditolak dan kami tidak dapat memproses keahlian anda secara automatik. Kadangkala pengesahan boleh mengambil masa sehingga 24 jam, jadi pastikan untuk menyegarkan halaman ini (walaupun ia telah tamat tempoh). Arahan kad debit/kredit Derma melalui halaman kad kredit/debit kami Beberapa langkah ada menyebut dompet kripto, tetapi jangan risau, anda tidak perlu belajar apa-apa tentang kripto untuk ini. Arahan %(coin_name)s Imbas kod QR ini dengan aplikasi dompet crypto anda dengan cepat mengisi butiran pembayaran Imbas kod QR untuk membayar Kami hanya menyokong versi standard syiling kripto, tiada rangkaian atau versi syiling eksotik. Ia boleh mengambil masa sehingga satu jam untuk mengesahkan transaksi, bergantung pada syiling. Derma sebanyak %(amount)s di <a %(a_page)s>halaman ini</a>. Dermaan ini telah tamat tempoh. Sila batalkan transaksi ini dan cipta yang baharu. Jika anda telah membayar: Ya, resit saya sudah diemel Jika kadar pertukaran kripto berubah-ubah semasa transaksi, pastikan anda menyertakan sekali resit yang menunjukkan kadar pertukaran asal. Kami sangat menghargai anda bersusah payah menggunakan crypto, ia banyak membantu kami! ❌ Ada sesuatu yang tidak kena. Sila muat semula halaman dan cuba sekali lagi. <span %(span_circle)s>%(circle_number)s</span>Emelkan resit kepada kami Jika anda menghadapi apa-apa masalah, sila hubungi kami di %(email)s dan sertakan informasi sebanyak yang boleh (seperti tangkapan skrin). ✅ Terima kasih atas dermaan anda! Anna akan mengaktifkan keahlian anda secara manual dalam masa beberapa hari. Hantar resit atau tangkapan skrin ke alamat peribadi verifikasi anda: Apabila resit anda sudah diemel, tekan butang ini supaya Anna boleh menyemaknya secara manual (ini mungkin akan mengambil masa beberapa hari): Hantar resit atau tangkapan skrin ke alamat pengesahan peribadi anda. Jangan gunakan alamat emel ini untuk sumbangan PayPal anda. Batal Betul, tolong batalkan Adakah anda pasti untuk membatalkan transaksi? Jangan batalkan jika pembayaran telah dibuat. ❌ Ralat. Sila muat semula halaman dan cuba semula. Buat dermaan baharu ✅ Dermaan anda telah dibatalkan. Tarikh: %(date)s Pengecam: %(id)s Pesan semula Status: <span %(span_label)s>%(label)s</span> Jumlah: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / sebulan untuk %(duration)s bulan, termasuk %(discounts)s%% diskaun)</span> Jumlah: %(total)s <span %(span_details)s>(%(monthly_amount_usd)s / sebulan untuk %(duration)s bulan)</span> 1. Masukkan emel anda. 2. Pilih kaedah pembayaran anda. 3. Pilih kaedah pembayaran anda sekali lagi. 4. Pilih dompet "Self-hosted". 5. Tekan “I confirm ownership”. 6. Anda akan menerima resit emel. Sila hantar resit tersebut kepada kami dan kami akan mengesahkan dermaan anda secepat mungkin. (anda mungkin perlu membatalkan dan membuat derma baharu) Arahan pembayaran telah tamat tempoh. Jika anda ingin membuat derma baharu, sila tekan butang 'Pesan semula' di atas. Anda telah membuat pembayaran. Jika anda ingin semak arahan pembayaran, tekan sini: Tunjuk arahan pembayaran lama Jika halaman derma disekat, cuba sambungan internet yang berbeza (contohnya VPN atau internet telefon). Malangnya, halaman Alipay sering hanya boleh diakses dari <strong>tanah besar China</strong>. Anda mungkin perlu mematikan VPN anda buat sementara waktu, atau menggunakan VPN ke tanah besar China (atau Hong Kong juga kadang-kadang berfungsi). <span %(span_circle)s>1</span>Derma melalui Alipay Derma jumlah keseluruhan %(total)s menggunakan <a %(a_account)s>akaun Alipay ini</a> Arahan Alipay <span %(span_circle)s>1</span>Buat pindahan ke salah satu akaun kripto kami Derma jumlah sebanyak %(total)s ke salah satu alamat tersebut: Arahan Kripto Ikut arahan yang diberi untuk membeli Bitcoin (BTC). Anda hanya perlu membeli jumlah sebanyak %(total)s berdasarkan jumlah yang anda mahu menderma. Masukkan alamat Bitcoin (BTC) kami sebagai penerima dan ikut arahan yang diberi untuk hantar derma berjumlah %(total)s: <span %(span_circle)s>1</span>Derma melalui Pix Derma %(total)s menggunakan %(a_account)s akaun Pix ini Arahan Pix <span %(span_circle)s>1</span>Derma di WeChat Derma jumlah keseluruhan %(total)s menggunakan <a %(a_account)s>akaun WeChat ini</a> Arahan WeChat Gunakan mana-mana perkhidmatan “kad kredit ke Bitcoin” ekspres berikut, yang hanya mengambil masa beberapa minit: Alamat BTC / Bitcoin (dompet luaran): Jumlah BTC / Bitcoin: Isi butiran berikut dalam borang: Jika ada maklumat ini yang tidak terkini, sila emel kami untuk memberitahu kami. Sila gunakan <span %(underline)s>jumlah tepat</span> ini. Kos keseluruhan anda mungkin lebih tinggi kerana yuran kad kredit. Untuk jumlah kecil, ini mungkin lebih daripada diskaun kami, malangnya. (minimum: %(minimum)s, tiada pengesahan untuk transaksi pertama) (minimum: %(minimum)s) (minimum: %(minimum)s) (minimum: %(minimum)s, tiada pengesahan untuk transaksi pertama) (minimum: %(minimum)s) (minimum: %(minimum)s bergantung pada negara, tiada pengesahan untuk transaksi pertama) Ikut arahan yang diberi untuk membeli syiling PYUSD (PayPal USD). Beli sedikit lagi (kami mencadang lebih %(more)s) berbanding jumlah yang akan diderma (%(amount)s) untuk menampung yuran transaksi. Anda akan simpan lebihan daripada transaksi tersebut. Pergi ke halaman “PYUSD” di aplikasi atau laman web PayPal anda. Tekan butang “Transfer” %(icon)s dan seterusnya “Send”. Kemas kini status Untuk menetapkan semula masa, buat derma yang baharu. Pastikan untuk menggunakan jumlah BTC di bawah, <em>BUKAN</em> euro atau dolar, jika tidak, kami tidak akan menerima jumlah yang betul dan tidak dapat mengesahkan keahlian anda secara automatik. Beli Bitcoin (BTC) di Revolut Beli sedikit lebih (kami mengesyorkan %(more)s lebih) daripada jumlah yang anda derma (%(amount)s), untuk menampung yuran transaksi. Anda akan menyimpan baki yang tinggal. Pergi ke halaman “Crypto” dalam Revolut untuk membeli Bitcoin (BTC). Pindahkan Bitcoin ke alamat kami Untuk derma kecil (di bawah $25) anda mungkin perlu menggunakan Rush atau Priority. Klik butang “Hantar bitcoin” untuk membuat “pengeluaran”. Tukar dari euro ke BTC dengan menekan ikon %(icon)s. Masukkan jumlah BTC di bawah dan klik “Hantar”. Lihat <a %(help_video)s>video ini</a> jika anda tersekat. Status: 1 2 Panduan langkah demi langkah Lihat panduan di bawah. Jika tidak, anda mungkin akan terkunci daripada akaun ini! Jika anda belum melakukannya, tuliskan kunci rahsia anda untuk log masuk: Terima kasih atas sumbangan anda! Masa yang tinggal: Sumbangan Pindah %(amount)s ke %(account)s Menunggu pengesahan (muat semula halaman untuk semak)… Menunggu pindahan (muat semula halaman untuk semak) … Terdahulu Muat turun pantas dalam 24 jam terakhir dikira dalam had harian. Muat turun dari Pelayan Rakan Kongsi Pantas ditandakan oleh %(icon)s. 18 jam terakhir Belum ada fail yang telah dimuat turun. Fail-fail yang telah dimuat turun tidak akan ditunjuk secara terbuka. Semua masa adalah dalam UTC. Fail-fail yang dimuat turun Jika anda memuat turun fail dengan muat turun pantas dan perlahan, ia akan muncul dua kali. Jangan risau terlalu banyak, terdapat ramai orang yang memuat turun dari laman web yang dihubungkan oleh kami, dan sangat jarang untuk menghadapi masalah. Walau bagaimanapun, untuk kekal selamat kami mengesyorkan menggunakan VPN (berbayar), atau <a %(a_tor)s>Tor</a> (percuma). Saya memuat turun 1984 oleh George Orwell, adakah polis akan datang ke pintu saya? Anda adalah Anna! Siapa Anna? Kami mempunyai satu API JSON stabil untuk ahli, untuk mendapatkan URL muat turun pantas: <a %(a_fast_download)s>/dyn/api/fast_download.json</a> (dokumentasi dalam JSON itu sendiri). Untuk kegunaan lain, seperti mengulangi semua fail kami, membina carian tersuai, dan sebagainya, kami mengesyorkan <a %(a_generate)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Data mentah boleh diterokai secara manual <a %(a_explore)s>melalui fail JSON</a>. Senarai torrent mentah kami boleh dimuat turun sebagai <a %(a_torrents)s>JSON</a> juga. Adakah anda mempunyai API? Kami tidak menjadi tuan rumah sebarang bahan berhak cipta di sini. Kami adalah enjin carian, dan oleh itu hanya mengindeks metadata yang sudah tersedia secara umum. Apabila memuat turun dari sumber luaran ini, kami mencadangkan untuk memeriksa undang-undang di bidang kuasa anda berkenaan dengan apa yang dibenarkan. Kami tidak bertanggungjawab terhadap kandungan yang dihoskan oleh pihak lain. Jika anda mempunyai aduan tentang apa yang anda lihat di sini, pilihan terbaik anda adalah menghubungi laman web asal. Kami kerap menarik perubahan mereka ke dalam pangkalan data kami. Jika anda benar-benar berfikir anda mempunyai aduan DMCA yang sah yang perlu kami tangani, sila isi <a %(a_copyright)s>borang aduan DMCA / Hak Cipta</a>. Kami mengambil aduan anda dengan serius, dan akan menghubungi anda secepat mungkin. Bagaimana saya melaporkan pelanggaran hak cipta? Berikut adalah beberapa buku yang membawa makna istimewa kepada dunia perpustakaan bayangan dan pemeliharaan digital: Apakah buku kegemaran anda? Kami juga ingin mengingatkan semua orang bahawa semua kod dan data kami adalah sumber terbuka sepenuhnya. Ini adalah unik untuk projek seperti kami — kami tidak mengetahui sebarang projek lain dengan katalog yang begitu besar yang juga sepenuhnya sumber terbuka. Kami sangat mengalu-alukan sesiapa yang berfikir kami menjalankan projek kami dengan buruk untuk mengambil kod dan data kami dan menubuhkan perpustakaan bayangan mereka sendiri! Kami tidak mengatakan ini dengan niat jahat atau apa-apa — kami benar-benar berfikir ini akan menjadi hebat kerana ia akan meningkatkan standard untuk semua orang, dan lebih baik memelihara warisan manusia. Saya benci cara anda menjalankan projek ini! Kami ingin orang ramai menubuhkan <a %(a_mirrors)s>cermin</a>, dan kami akan menyokong kewangan ini. Bagaimana saya boleh membantu? Ya, kami memang mengumpulnya. Inspirasi kami untuk mengumpul metadata adalah matlamat Aaron Swartz untuk "satu halaman web untuk setiap buku yang pernah diterbitkan", yang mana dia mencipta <a %(a_openlib)s>Open Library</a>. Projek itu telah berjaya, tetapi kedudukan unik kami membolehkan kami mendapatkan metadata yang mereka tidak boleh. Satu lagi inspirasi adalah keinginan kami untuk mengetahui <a %(a_blog)s>berapa banyak buku yang ada di dunia</a>, supaya kami boleh mengira berapa banyak buku yang masih perlu kami selamatkan. Adakah anda mengumpul metadata? Perhatikan bahawa mhut.org menyekat julat IP tertentu, jadi VPN mungkin diperlukan. <strong>Android:</strong> Klik menu tiga titik di bahagian atas kanan, dan pilih “Tambah ke Skrin Utama”. <strong>iOS:</strong> Klik butang “Kongsi” di bahagian bawah, dan pilih “Tambah ke Skrin Utama”. Kami tidak mempunyai aplikasi mudah alih rasmi, tetapi anda boleh memasang laman web ini sebagai aplikasi. Adakah anda mempunyai aplikasi mudah alih? Sila hantar mereka ke <a %(a_archive)s>Internet Archive</a>. Mereka akan memeliharanya dengan betul. Bagaimana saya menderma buku atau bahan fizikal lain? Bagaimana saya meminta buku? <a %(a_blog)s>Blog Anna</a>, <a %(a_reddit_u)s>Reddit</a>, <a %(a_reddit_r)s>Subreddit</a> — kemas kini berkala <a %(a_software)s>Perisian Anna</a> — kod sumber terbuka kami <a %(a_datasets)s>Datasets</a> — tentang data <a %(a_li)s>.li</a>, <a %(a_se)s>.se</a>, <a %(a_org)s>.org</a> — domain alternatif Adakah terdapat lebih banyak sumber mengenai Arkib Anna? <a %(a_translate)s>Terjemah di Perisian Anna</a> — sistem terjemahan kami <a %(a_wikipedia)s>Wikipedia</a> — lebih lanjut tentang kami (sila bantu kemas kini halaman ini, atau cipta satu untuk bahasa anda sendiri!) Pilih tetapan yang anda suka, biarkan kotak carian kosong, klik “Cari”, dan kemudian tandakan halaman menggunakan ciri penanda halaman pelayar anda. Bagaimana saya menyimpan tetapan carian saya? Kami mengalu-alukan penyelidik keselamatan untuk mencari kerentanan dalam sistem kami. Kami adalah penyokong besar pendedahan yang bertanggungjawab. Hubungi kami <a %(a_contact)s>di sini</a>. Kami kini tidak dapat memberikan ganjaran bug, kecuali untuk kerentanan yang mempunyai <a %(a_link)s>potensi untuk menjejaskan anonimiti kami</a>, yang mana kami menawarkan ganjaran dalam lingkungan $10k-50k. Kami ingin menawarkan skop yang lebih luas untuk ganjaran bug pada masa hadapan! Sila ambil perhatian bahawa serangan kejuruteraan sosial adalah di luar skop. Jika anda berminat dalam keselamatan ofensif, dan ingin membantu mengarkibkan pengetahuan dan budaya dunia, pastikan untuk menghubungi kami. Terdapat banyak cara di mana anda boleh membantu. Adakah anda mempunyai program pendedahan yang bertanggungjawab? Kami benar-benar tidak mempunyai sumber yang mencukupi untuk memberikan semua orang di dunia muat turun berkelajuan tinggi, walaupun kami ingin. Jika seorang dermawan kaya ingin tampil dan menyediakan ini untuk kami, itu akan menjadi luar biasa, tetapi sehingga itu, kami mencuba yang terbaik. Kami adalah projek bukan untung yang hampir tidak dapat bertahan melalui derma. Inilah sebabnya kami melaksanakan dua sistem untuk muat turun percuma, dengan rakan kongsi kami: pelayan kongsi dengan muat turun perlahan, dan pelayan yang sedikit lebih pantas dengan senarai menunggu (untuk mengurangkan bilangan orang yang memuat turun pada masa yang sama). Kami juga mempunyai <a %(a_verification)s>pengesahan pelayar</a> untuk muat turun perlahan kami, kerana jika tidak, bot dan pengikis akan menyalahgunakannya, menjadikan keadaan lebih perlahan untuk pengguna yang sah. Perhatikan bahawa, apabila menggunakan Pelayar Tor, anda mungkin perlu menyesuaikan tetapan keselamatan anda. Pada pilihan terendah, yang dipanggil “Standard”, cabaran turnstile Cloudflare berjaya. Pada pilihan yang lebih tinggi, yang dipanggil “Safer” dan “Safest”, cabaran tersebut gagal. Untuk fail besar, kadangkala muat turun yang perlahan boleh terputus di tengah jalan. Kami mengesyorkan menggunakan pengurus muat turun (seperti JDownloader) untuk menyambung semula muat turun besar secara automatik. Mengapa muat turun yang perlahan begitu lambat? Soalan Lazim (FAQ) Gunakan <a %(a_list)s>penjana senarai torrent</a> untuk menjana senarai torrent yang paling memerlukan torrenting, dalam had ruang storan anda. Ya, lihat halaman <a %(a_llm)s>data LLM</a>. Kebanyakan torrent mengandungi fail secara langsung, yang bermaksud anda boleh mengarahkan klien torrent untuk hanya memuat turun fail yang diperlukan. Untuk menentukan fail mana yang hendak dimuat turun, anda boleh <a %(a_generate)s>menjana</a> metadata kami, atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Malangnya, beberapa koleksi torrent mengandungi fail .zip atau .tar di akar, dalam kes ini anda perlu memuat turun keseluruhan torrent sebelum dapat memilih fail individu. Tiada alat yang mudah digunakan untuk menapis torrent tersedia lagi, tetapi kami mengalu-alukan sumbangan. (Kami mempunyai <a %(a_ideas)s>beberapa idea</a> untuk kes yang terakhir ini.) Jawapan panjang: Jawapan ringkas: tidak mudah. Kami cuba untuk meminimumkan pendua atau pertindihan antara torrent dalam senarai ini, tetapi ini tidak selalu dapat dicapai, dan bergantung banyak pada dasar perpustakaan sumber. Untuk perpustakaan yang mengeluarkan torrent mereka sendiri, ia di luar kawalan kami. Untuk torrent yang dikeluarkan oleh Arkib Anna, kami menduplikasi hanya berdasarkan hash MD5, yang bermaksud bahawa versi berbeza dari buku yang sama tidak akan diduplikasi. Ya. Ini sebenarnya adalah PDF dan EPUB, cuma tidak mempunyai sambungan dalam banyak torrent kami. Terdapat dua tempat di mana anda boleh mencari metadata untuk fail torrent, termasuk jenis/sambungan fail: 1. Setiap koleksi atau keluaran mempunyai metadata tersendiri. Sebagai contoh, <a %(a_libgen_nonfic)s>torrent Libgen.rs</a> mempunyai pangkalan data metadata yang dihoskan di laman web Libgen.rs. Kami biasanya memautkan kepada sumber metadata yang berkaitan dari <a %(a_datasets)s>halaman dataset</a> setiap koleksi. 2. Kami mengesyorkan <a %(a_generate)s>menjana</a> atau <a %(a_download)s>memuat turun</a> pangkalan data ElasticSearch dan MariaDB kami. Ini mengandungi pemetaan untuk setiap rekod dalam Arkib Anna kepada fail torrent yang sepadan (jika ada), di bawah "torrent_paths" dalam JSON ElasticSearch. Sesetengah klien torrent tidak menyokong saiz kepingan yang besar, yang mana banyak torrent kami mempunyai (untuk yang lebih baru kami tidak melakukannya lagi — walaupun ia sah mengikut spesifikasi!). Jadi cuba klien lain jika anda menghadapi masalah ini, atau adukan kepada pembuat klien torrent anda. Saya ingin membantu seed, tetapi saya tidak mempunyai banyak ruang cakera. Torrent terlalu perlahan; bolehkah saya memuat turun data terus dari anda? Bolehkah saya memuat turun hanya sebahagian daripada fail, seperti hanya bahasa atau topik tertentu? Bagaimana anda mengendalikan pendua dalam torrent? Bolehkah saya mendapatkan senarai torrent sebagai JSON? Saya tidak nampak PDF atau EPUB dalam torrent, hanya fail binari? Apa yang perlu saya lakukan? Mengapa klien torrent saya tidak dapat membuka beberapa fail torrent / pautan magnet anda? Soalan Lazim Torrent Bagaimana saya memuat naik buku baharu? Sila lihat <a %(a_href)s>projek yang cemerlang ini</a>. Adakah anda mempunyai pemantau masa operasi? Apakah Arkib Anna? Jadi ahli untuk menggunakan muat turun pantas. Kami kini menyokong kad hadiah Amazon, kad kredit dan debit, kripto, Alipay, dan WeChat. Anda telah kehabisan muat turun pantas hari ini. Akses Muat turun setiap jam dalam 30 hari terakhir. Purata setiap jam: %(hourly)s. Purata setiap hari: %(daily)s. Kami bekerjasama dengan rakan kongsi untuk menjadikan koleksi kami mudah dan bebas diakses oleh sesiapa sahaja. Kami percaya bahawa setiap orang mempunyai hak kepada kebijaksanaan kolektif manusia. Dan <a %(a_search)s>tidak dengan mengorbankan penulis</a>. Set data yang digunakan dalam Arkib Anna adalah sepenuhnya terbuka, dan boleh dicerminkan secara pukal menggunakan torrent. <a %(a_datasets)s>Ketahui lebih lanjut…</a> Arkib jangka panjang Pangkalan data penuh Cari Buku, kertas kerja, majalah, komik, rekod perpustakaan, metadata, … Semua <a %(a_code)s>kod</a> dan <a %(a_datasets)s>data</a> kami adalah sumber terbuka sepenuhnya. <span %(span_anna)s>Arkib Anna</span> adalah projek bukan untung dengan dua matlamat: <li><strong>Pemeliharaan:</strong> Menyandarkan semua pengetahuan dan budaya manusia.</li><li><strong>Akses:</strong> Menjadikan pengetahuan dan budaya ini tersedia kepada sesiapa sahaja di dunia.</li> Kami mempunyai koleksi data teks berkualiti tinggi terbesar di dunia. <a %(a_llm)s>Ketahui lebih lanjut…</a> LLM training data 🪩 Cermin: panggilan untuk sukarelawan Jika anda menjalankan pemproses pembayaran tanpa nama berisiko tinggi, sila hubungi kami. Kami juga mencari orang yang ingin meletakkan iklan kecil yang berselera. Semua hasil akan digunakan untuk usaha pemeliharaan kami. Pemeliharaan Kami menganggarkan bahawa kami telah memelihara kira-kira <a href="https://annas-blog.org/blog-isbndb-dump-how-many-books-are-preserved-forever.html">5%% daripada buku-buku di dunia</a>. Kami memelihara buku, kertas, komik, majalah, dan banyak lagi, dengan membawa bahan-bahan ini dari pelbagai <a href="https://en.wikipedia.org/wiki/Shadow_library">perpustakaan bayangan</a>, perpustakaan rasmi, dan koleksi lain ke satu tempat. Semua data ini dipelihara selama-lamanya dengan memudahkan penggandaan secara besar-besaran — menggunakan torrent — yang menghasilkan banyak salinan di seluruh dunia. Beberapa perpustakaan bayangan sudah melakukan ini sendiri (contohnya Sci-Hub, Library Genesis), manakala Arkib Anna “membebaskan” perpustakaan lain yang tidak menawarkan pengedaran besar-besaran (contohnya Z-Library) atau bukan perpustakaan bayangan sama sekali (contohnya Internet Archive, DuXiu). Pengedaran yang meluas ini, digabungkan dengan kod sumber terbuka, menjadikan laman web kami tahan terhadap penutupan, dan memastikan pemeliharaan jangka panjang pengetahuan dan budaya manusia. Ketahui lebih lanjut tentang <a href="/datasets">datasets kami</a>. Jika anda seorang <a %(a_member)s>ahli</a>, pengesahan pelayar tidak diperlukan. 🧬&nbsp;SciDB adalah kesinambungan Sci-Hub. SciDB Buka DOI Sci-Hub telah <a %(a_paused)s>menghentikan</a> muat naik kertas baru. Akses langsung kepada %(count)s kertas akademik 🧬&nbsp;SciDB adalah kesinambungan Sci-Hub, dengan antara muka yang biasa dan paparan langsung PDF. Masukkan DOI anda untuk melihat. Kami mempunyai koleksi penuh Sci-Hub, serta kertas baru. Kebanyakan boleh dilihat secara langsung dengan antara muka yang biasa, serupa dengan Sci-Hub. Sesetengah boleh dimuat turun melalui sumber luaran, dalam kes ini kami menunjukkan pautan kepada mereka. Anda boleh membantu dengan banyak dengan menyemai torrent. <a %(a_torrents)s>Ketahui lebih lanjut…</a> >%(count)s penyemai <%(count)s seeders %(count_min)s–%(count_max)s penyebar 🤝 Mencari sukarelawan Sebagai projek sumber terbuka tanpa untung, kami sentiasa mencari orang untuk membantu. Muat turun IPFS Senarai mengikut %(by)s, dicipta <span %(span_time)s>%(time)s</span> Simpan ❌ Ada sesuatu yang tidak kena. Sila cuba semula. ✅ Disimpan. Sila muat semula halaman. Senarai kosong. sunting Tambah atau alih keluar daripada senarai ini dengan mencari fail dan membuka tab "Senarai". Senarai Bagaimana kami boleh membantu Mengeluarkan pertindihan (deduplikasi) Pengekstrakan teks dan metadata OCR Kami mampu menyediakan akses berkelajuan tinggi kepada koleksi penuh kami, serta kepada koleksi yang belum dikeluarkan. Ini adalah akses peringkat perusahaan yang kami boleh sediakan untuk sumbangan dalam lingkungan puluhan ribu USD. Kami juga bersedia menukar ini dengan koleksi berkualiti tinggi yang kami belum ada. Kami boleh memulangkan wang anda jika anda dapat menyediakan kami dengan pengayaan data kami, seperti: Sokong arkib jangka panjang pengetahuan manusia, sambil mendapatkan data yang lebih baik untuk model anda! <a %(a_contact)s>Hubungi kami</a> untuk membincangkan bagaimana kita boleh bekerjasama. Difahami bahawa LLM berkembang dengan data berkualiti tinggi. Kami mempunyai koleksi buku, kertas kerja, majalah, dan lain-lain yang terbesar di dunia, yang merupakan beberapa sumber teks berkualiti tinggi. Data LLM Skala dan julat unik Koleksi kami mengandungi lebih daripada seratus juta fail, termasuk jurnal akademik, buku teks, dan majalah. Kami mencapai skala ini dengan menggabungkan repositori besar yang sedia ada. Beberapa koleksi sumber kami sudah tersedia dalam jumlah besar (Sci-Hub, dan sebahagian daripada Libgen). Sumber lain kami bebaskan sendiri. <a %(a_datasets)s>Datasets</a> menunjukkan gambaran penuh. Koleksi kami termasuk berjuta-juta buku, kertas kerja, dan majalah dari sebelum era e-buku. Sebahagian besar koleksi ini telah di-OCR, dan sudah mempunyai sedikit pertindihan dalaman. Teruskan Jika anda kehilangan kunci anda, sila <a %(a_contact)s>hubungi kami</a> dan berikan sebanyak mungkin maklumat. Anda mungkin perlu membuat akaun baru buat sementara waktu untuk menghubungi kami. Sila <a %(a_account)s>log masuk</a> untuk melihat halaman ini.</a> Untuk mengelakkan spam-bot daripada mencipta banyak akaun, kami perlu mengesahkan pelayar anda terlebih dahulu. Jika anda terperangkap dalam gelung tak terhingga, kami mengesyorkan memasang <a %(a_privacypass)s>Privacy Pass</a>. Ia juga mungkin membantu untuk mematikan penyekat iklan dan sambungan pelayar lain. Log masuk / Daftar Arkib Anna sedang dalam penyelenggaraan sementara. Sila kembali dalam satu jam. Pengarang alternatif Penerangan alternatif Edisi alternatif Sambungan alternatif Nama fail alternatif Penerbit alternatif Tajuk alternatif tarikh sumber terbuka Baca lagi… penerangan Cari Arkib Anna untuk nombor CADAL SSNO Cari dalam Arkib Anna untuk nombor SSID DuXiu Cari Arkib Anna untuk nombor DXID DuXiu Cari ISBN di Anna's Archive Cari nombor OCLC (WorldCat) di Anna's Archive Cari ID Open Library di Anna's Archive Penonton dalam talian Arkib Anna %(count)s halaman terjejas Selepas memuat turun: Versi yang lebih baik bagi fail ini mungkin tersedia di %(link)s Muat turun torrent secara pukal koleksi Gunakan alat dalam talian untuk menukar antara format. Alat penukaran yang disyorkan: %(links)s Untuk fail besar, kami mengesyorkan menggunakan pengurus muat turun untuk mengelakkan gangguan. Pengurus muat turun yang disyorkan: %(links)s Indeks eBook EBSCOhost (pakar sahaja) (tekan “GET” di atas) (tekan “GET” di atas) Muat turun luaran <strong>🚀 Muat turun pantas</strong> Anda mempunyai %(remaining)s baki hari ini. Terima kasih kerana menjadi ahli! ❤️ <strong>🚀 Muat turun pantas</strong> Anda telah kehabisan muat turun pantas untuk hari ini. <strong>🚀 Muat turun pantas</strong> Anda telah memuat turun fail ini baru-baru ini. Pautan kekal sah untuk seketika. <strong>🚀 Muat turun pantas</strong> Sertai sebagai <a %(a_membership)s>ahli</a> untuk menyokong pemeliharaan jangka panjang buku, kertas, dan banyak lagi. Sebagai tanda terima kasih atas sokongan anda, anda mendapat muat turun pantas. ❤️ 🚀 Muat turun pantas 🐢 Muat turun perlahan Pinjam dari Internet Archive Pintu masuk IPFS #%(num)d (anda mungkin perlu mencuba beberapa kali apabila menggunakan IPFS) Libgen.li Fiksyen Libgen.rs Bukan Fiksyen Libgen.rs iklan mereka diketahui mengandungi perisian hasad, jadi gunakan penyekat iklan atau jangan klik iklan Amazon ‘Send to Kindle’ djazz ‘Send to Kobo/Kindle’ MagzDB ManualsLib Nexus/STC (Fail Nexus/STC boleh menjadi tidak boleh dipercayai untuk dimuat turun) Tiada muat turun ditemui. Semua pilihan muat turun mempunyai fail yang sama, dan sepatutnya selamat untuk digunakan. Walau bagaimanapun, sentiasa berhati-hati apabila memuat turun fail dari internet, terutamanya dari laman web luar Anna’s Archive. Sebagai contoh, pastikan peranti anda sentiasa dikemas kini. (tiada pengalihan) Buka dalam penonton kami (buka dalam penonton) Pilihan #%(num)d: %(link)s %(extra)s Cari rekod asal dalam CADAL Cari secara manual di DuXiu Cari rekod asal di ISBNdb Cari rekod asal di WorldCat Cari rekod asal di Open Library Cari ISBN di pengkalan-pengkalan data lain (cetakan dilumpuhkan untuk penaung sahaja) PubMed Anda memerlukan pembaca ebook atau PDF untuk membuka fail, bergantung pada format fail. Pembaca ebook yang disyorkan: %(links)s Anna’s Archive 🧬 SciDB Sci-Hub: %(doi)s (DOI berkaitan mungkin tidak tersedia di Sci-Hub) Anda boleh menghantar kedua-dua fail PDF dan EPUB ke Kindle atau eReader Kobo anda. Alat yang disyorkan: %(links)s Maklumat lanjut dalam <a %(a_slow)s>Soalan Lazim</a>. Sokong penulis dan perpustakaan Jika anda suka ini dan mampu, pertimbangkan untuk membeli yang asal, atau menyokong penulis secara langsung. Jika ini tersedia di perpustakaan tempatan anda, pertimbangkan untuk meminjamnya secara percuma di sana. Muat turun dari Pelayan Rakan tidak tersedia buat sementara waktu untuk fail ini. torrent Daripada rakan kongsi yang dipercayai. Z-Library Z-Library di Tor (memerlukan Pelayar Tor) tunjukkan muat turun luaran <span class="font-bold">❌ Fail ini mungkin mempunyai isu, dan telah disembunyikan dari perpustakaan sumber.</span> Kadang-kadang ini atas permintaan pemegang hak cipta, kadang-kadang kerana alternatif yang lebih baik tersedia, tetapi kadang-kadang kerana isu dengan fail itu sendiri. Ia mungkin masih boleh dimuat turun, tetapi kami mengesyorkan mencari fail alternatif terlebih dahulu. Maklumat lanjut: Jika anda masih mahu memuat turun fail ini, pastikan hanya menggunakan perisian yang dipercayai dan dikemas kini untuk membukanya. komen metadata AA: Cari Arkib Anna untuk “%(name)s” Peneroka Kod: Lihat dalam Peneroka Kod “%(name)s” URL: Laman web: Jika anda mempunyai fail ini dan ia belum tersedia di Arkib Anna, pertimbangkan untuk <a %(a_request)s>memuat naiknya</a>. Fail Controlled Digital Lending Internet Archive “%(id)s” Ini adalah rekod dari Internet Archive dan bukan fail yang boleh dimuat turun terus. Anda boleh cuba meminjam buku (pautan di bawah) atau gunakan URL ini apabila a %(a_request)s>meminta file</a>. Perbaiki metadata Rekod metadata CADAL SSNO %(id)s Ini adalah rekod metadata dan bukan fail yang boleh dimuat turun terus. Anda boleh menggunakan URL ini apabila <a %(a_request)s>meminta fail</a>. Rekod metadata DuXiu SSID %(id)s Rekod metadata ISBNdb %(id)s Rekod metadata MagzDB ID %(id)s Rekod metadata Nexus/STC ID %(id)s Rekod metadata nombor OCLC (WorldCat) %(id)s Rekod metadata Open Library %(id)s Fail Sci-Hub “%(id)s” Tidak ditemui “%(md5_input)s” tidak ditemui dalam pangkalan data kami. Tambah komen (%(count)s) Anda boleh mendapatkan md5 dari URL, contohnya MD5 versi yang lebih baik bagi fail ini (jika berkenaan). Isi ini jika terdapat fail lain yang hampir sama dengan fail ini (edisi yang sama, sambungan fail yang sama jika anda boleh menemukannya), yang sepatutnya digunakan oleh orang ramai sebagai ganti fail ini. Jika anda tahu versi yang lebih baik bagi fail ini di luar Arkib Anna, sila <a %(a_upload)s>muat naik</a>. Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi. Anda telah meninggalkan komen. Ia mungkin mengambil masa sebentar untuk dipaparkan. Sila gunakan <a %(a_copyright)s>borang tuntutan DMCA / Hak Cipta</a>. Huraikan isu (diperlukan) Jika fail ini mempunyai kualiti yang baik, anda boleh membincangkan apa sahaja mengenainya di sini! Jika tidak, sila gunakan butang “Laporkan isu fail”. Kualiti fail yang hebat (%(count)s) Kualiti fail Belajar bagaimana untuk <a %(a_metadata)s>menambah baik metadata</a> untuk fail ini sendiri. Huraian isu Sila <a %(a_login)s>log masuk</a>. Saya suka buku ini! Bantu komuniti dengan melaporkan kualiti fail ini! 🙌 Sesuatu telah berlaku kesilapan. Sila muat semula halaman dan cuba lagi. Laporkan isu fail (%(count)s) Terima kasih kerana menghantar laporan anda. Ia akan dipaparkan di halaman ini, serta disemak secara manual oleh Anna (sehingga kami mempunyai sistem moderasi yang betul). Tinggalkan komen Hantar laporan Apa yang salah dengan fail ini? Pinjam (%(count)s) Komen (%(count)s) Muat turun (%(count)s) Teroka metadata (%(count)s) Senarai (%(count)s) Statistik (%(count)s) Untuk maklumat tentang fail ini, lihat <a %(a_href)s>fail JSON</a>nya. Ini adalah fail yang diuruskan oleh perpustakaan <a %(a_ia)s>IA’s Controlled Digital Lending</a>, dan diindeks oleh Anna’s Archive untuk carian. Untuk maklumat tentang pelbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>. Metadata daripada rekod yang dipautkan Tingkatkan metadata di Open Library “File MD5” adalah hash yang dikira daripada kandungan fail, dan adalah unik berdasarkan kandungan tersebut. Semua perpustakaan bayangan yang kami indeks di sini menggunakan MD5 untuk mengenal pasti fail. Satu fail mungkin muncul dalam pelbagai perpustakaan bayangan. Untuk maklumat tentang pelbagai datasets yang telah kami kumpulkan, lihat <a %(a_datasets)s>halaman Datasets</a>. Laporkan kualiti fail Jumlah muat turun: %(total)s CADAL SSNO %(id)s} Cerlalc %(id)s} Metadata Czech %(id)s} DuXiu SSID %(id)s} EBSCOhost edsebk %(id)s} Google Books %(id)s} Goodreads %(id)s} HathiTrust %(id)s} ISBNdb %(id)s} ISBN GRP %(id)s} Libby %(id)s} MagzDB %(id)s} Nexus/STC %(id)s} OCLC %(id)s} RSL %(id)s} Trantor %(id)s} Amaran: pelbagai rekod yang dipautkan: Apabila anda melihat sebuah buku di Arkib Anna, anda boleh melihat pelbagai medan: tajuk, pengarang, penerbit, edisi, tahun, deskripsi, nama fail, dan banyak lagi. Semua maklumat tersebut dipanggil <em>metadata</em>. Oleh kerana kami menggabungkan buku dari pelbagai <em>perpustakaan sumber</em>, kami menunjukkan apa sahaja metadata yang tersedia dalam perpustakaan sumber tersebut. Sebagai contoh, untuk sebuah buku yang kami peroleh dari Library Genesis, kami akan menunjukkan tajuk dari pangkalan data Library Genesis. Kadang-kadang sebuah buku terdapat dalam <em>beberapa</em> perpustakaan sumber, yang mungkin mempunyai medan metadata yang berbeza. Dalam kes itu, kami hanya menunjukkan versi terpanjang bagi setiap medan, kerana versi tersebut diharapkan mengandungi maklumat yang paling berguna! Kami masih akan menunjukkan medan lain di bawah deskripsi, contohnya sebagai "tajuk alternatif" (tetapi hanya jika ia berbeza). Kami juga mengekstrak <em>kod</em> seperti pengenal dan pengelas dari perpustakaan sumber. <em>Pengenal</em> mewakili edisi tertentu bagi sebuah buku secara unik; contohnya adalah ISBN, DOI, Open Library ID, Google Books ID, atau Amazon ID. <em>Pengelas</em> mengelompokkan beberapa buku yang serupa; contohnya adalah Dewey Decimal (DCC), UDC, LCC, RVK, atau GOST. Kadang-kadang kod ini secara eksplisit dihubungkan dalam perpustakaan sumber, dan kadang-kadang kami boleh mengekstraknya dari nama fail atau deskripsi (terutamanya ISBN dan DOI). Kami boleh menggunakan pengenal untuk mencari rekod dalam <em>koleksi metadata sahaja</em>, seperti OpenLibrary, ISBNdb, atau WorldCat/OCLC. Terdapat tab <em>metadata</em> khusus dalam enjin carian kami jika anda ingin melayari koleksi tersebut. Kami menggunakan rekod yang sepadan untuk mengisi medan metadata yang hilang (contohnya jika tajuk hilang), atau contohnya sebagai "tajuk alternatif" (jika terdapat tajuk yang sedia ada). Untuk melihat dengan tepat dari mana metadata sebuah buku berasal, lihat tab <em>“Butiran teknikal”</em> pada halaman buku. Ia mempunyai pautan ke JSON mentah untuk buku tersebut, dengan petunjuk ke JSON mentah rekod asal. Untuk maklumat lanjut, lihat halaman berikut: <a %(a_datasets)s>Datasets</a>, <a %(a_search_metadata)s>Carian (tab metadata)</a>, <a %(a_codes)s>Penjelajah Kod</a>, dan <a %(a_example)s>Contoh metadata JSON</a>. Akhir sekali, semua metadata kami boleh <a %(a_generated)s>dihasilkan</a> atau <a %(a_downloaded)s>dimuat turun</a> sebagai pangkalan data ElasticSearch dan MariaDB. Latar Belakang Anda boleh membantu pemeliharaan buku dengan meningkatkan metadata! Pertama, baca latar belakang tentang metadata di Arkib Anna, dan kemudian pelajari cara meningkatkan metadata melalui pautan dengan Open Library, dan dapatkan keahlian percuma di Arkib Anna. Tingkatkan metadata Jadi jika anda menemui fail dengan metadata yang buruk, bagaimana anda harus memperbaikinya? Anda boleh pergi ke perpustakaan sumber dan mengikuti prosedurnya untuk memperbaiki metadata, tetapi apa yang perlu dilakukan jika fail tersebut terdapat dalam beberapa perpustakaan sumber? Terdapat satu pengenal yang dianggap istimewa di Arkib Anna. <strong>Medan annas_archive md5 di Open Library sentiasa mengatasi semua metadata lain!</strong> Mari kita mundur sedikit dan belajar tentang Open Library. Open Library diasaskan pada tahun 2006 oleh Aaron Swartz dengan matlamat "satu halaman web untuk setiap buku yang pernah diterbitkan". Ia sejenis Wikipedia untuk metadata buku: semua orang boleh menyuntingnya, ia berlesen bebas, dan boleh dimuat turun secara pukal. Ia adalah pangkalan data buku yang paling selaras dengan misi kami — sebenarnya, Arkib Anna telah diilhamkan oleh visi dan kehidupan Aaron Swartz. Daripada mencipta semula roda, kami memutuskan untuk mengarahkan sukarelawan kami ke Open Library. Jika anda melihat sebuah buku yang mempunyai metadata yang salah, anda boleh membantu dengan cara berikut: Perhatikan bahawa ini hanya berfungsi untuk buku, bukan kertas akademik atau jenis fail lain. Untuk jenis fail lain, kami masih mengesyorkan mencari perpustakaan sumber. Mungkin mengambil masa beberapa minggu untuk perubahan dimasukkan dalam Anna’s Archive, kerana kami perlu memuat turun data dump Open Library terkini, dan menjana semula indeks carian kami.  Pergi ke <a %(a_openlib)s>laman web Open Library</a>. Cari rekod buku yang betul. <strong>AMARAN:</strong> pastikan anda memilih <strong>edisi</strong> yang betul. Di Open Library, anda mempunyai "karya" dan "edisi". Sebuah "karya" boleh jadi "Harry Potter and the Philosopher's Stone". Sebuah "edisi" boleh jadi: Edisi pertama tahun 1997 diterbitkan oleh Bloomsbery dengan 256 halaman. Edisi paperback tahun 2003 diterbitkan oleh Raincoast Books dengan 223 halaman. Terjemahan Poland tahun 2000 “Harry Potter I Kamie Filozoficzn” oleh Media Rodzina dengan 328 halaman. Semua edisi tersebut mempunyai ISBN dan kandungan yang berbeza, jadi pastikan anda memilih yang betul! Edit rekod (atau cipta jika tiada), dan tambah sebanyak mungkin maklumat berguna yang anda boleh! Anda sudah berada di sini, jadi buatlah rekod itu benar-benar hebat. Di bawah “ID Numbers” pilih “Anna’s Archive” dan tambah MD5 buku dari Anna’s Archive. Ini adalah rentetan panjang huruf dan nombor selepas “/md5/” dalam URL. Cuba cari fail lain dalam Anna’s Archive yang juga sepadan dengan rekod ini, dan tambah juga fail-fail tersebut. Pada masa hadapan, kita boleh kumpulkan fail-fail tersebut sebagai pendua di halaman carian Anna’s Archive. Apabila anda selesai, tuliskan URL yang baru anda kemas kini. Setelah anda mengemas kini sekurang-kurangnya 30 rekod dengan MD5 dari Anna’s Archive, hantarkan kami <a %(a_contact)s>email</a> dan hantarkan senarai tersebut. Kami akan memberikan anda keahlian percuma untuk Anna’s Archive, supaya anda boleh melakukan kerja ini dengan lebih mudah (dan sebagai tanda terima kasih atas bantuan anda). Ini mesti merupakan suntingan berkualiti tinggi yang menambah sejumlah besar maklumat, jika tidak permintaan anda akan ditolak. Permintaan anda juga akan ditolak jika mana-mana suntingan dibatalkan atau diperbetulkan oleh moderator Open Library. Pautan Open Library Jika anda terlibat secara signifikan dalam pembangunan dan operasi kerja kami, kami boleh berbincang untuk berkongsi lebih banyak hasil sumbangan dengan anda, untuk anda gunakan mengikut keperluan. Kami hanya akan membayar untuk hosting setelah anda mempunyai semuanya disediakan, dan telah menunjukkan bahawa anda mampu mengemas kini arkib dengan kemas kini. Ini bermakna anda perlu membayar untuk 1-2 bulan pertama dari poket anda sendiri. Masa anda tidak akan diberi pampasan (dan begitu juga kami), kerana ini adalah kerja sukarela sepenuhnya. Kami bersedia menanggung perbelanjaan hosting dan VPN, pada mulanya sehingga $200 sebulan. Ini mencukupi untuk pelayan carian asas dan proksi yang dilindungi DMCA. Perbelanjaan hosting Sila <strong>jangan hubungi kami</strong> untuk meminta kebenaran, atau untuk soalan asas. Tindakan lebih bermakna daripada kata-kata! Semua maklumat ada di luar sana, jadi teruskan dengan menyediakan cermin anda. Jangan ragu untuk menghantar tiket atau permintaan gabungan ke Gitlab kami apabila anda menghadapi masalah. Kami mungkin perlu membina beberapa ciri khusus cermin dengan anda, seperti penjenamaan semula dari “Anna’s Archive” kepada nama laman web anda, (pada mulanya) melumpuhkan akaun pengguna, atau memaut kembali ke laman utama kami dari halaman buku. Setelah anda mempunyai cermin anda berjalan, sila hubungi kami. Kami ingin mengkaji OpSec anda, dan setelah itu kukuh, kami akan memaut ke cermin anda, dan mula bekerjasama lebih rapat dengan anda. Terima kasih terlebih dahulu kepada sesiapa yang bersedia menyumbang dengan cara ini! Ia bukan untuk yang lemah semangat, tetapi ia akan mengukuhkan jangka hayat perpustakaan terbuka terbesar dalam sejarah manusia. Memulakan Untuk meningkatkan ketahanan Arkib Anna, kami mencari sukarelawan untuk menjalankan cermin. Versi anda jelas dibezakan sebagai cermin, contohnya “Arkib Bob, cermin Anna’s Archive”. Anda bersedia mengambil risiko yang berkaitan dengan kerja ini, yang adalah signifikan. Anda mempunyai pemahaman mendalam tentang keselamatan operasi yang diperlukan. Kandungan <a %(a_shadow)s>ini</a> <a %(a_pirate)s>pos</a> adalah jelas kepada anda. Pada mulanya kami tidak akan memberi anda akses kepada muat turun pelayan rakan kongsi kami, tetapi jika semuanya berjalan lancar, kami boleh berkongsi dengan anda. Anda menjalankan kod sumber terbuka Anna’s Archive, dan anda kerap mengemas kini kedua-dua kod dan data. Anda bersedia menyumbang kepada <a %(a_codebase)s>kod sumber</a> kami — dalam kerjasama dengan pasukan kami — untuk merealisasikan ini. Kami mencari ini: Cermin: panggilan untuk sukarelawan Buat dermaan lagi. Tiada dermaan lagi. <a %(a_donate)s>Buat derma pertama saya.</a> Butiran derma tidak akan ditunjuk secara terbuka. Dermaan saya 📡 Untuk pencerminan pukal koleksi kami, lihat halaman <a %(a_datasets)s>Datasets</a> dan <a %(a_torrents)s>Torrents</a>. Muat turun dari alamat IP anda dalam 24 jam yang lalu: %(count)s. 🚀 Untuk mendapatkan muat turun yang lebih pantas dan melangkau pemeriksaan pelayar, <a %(a_membership)s>jadi ahli</a>. Muat turun dari laman web rakan kongsi Sila teruskan melayari Arkib Anna di tab yang berbeza semasa menunggu (jika pelayar anda menyokong penyegaran tab latar belakang). Sila tunggu beberapa halaman muat turun dimuatkan pada masa yang sama (tetapi sila hanya muat turun satu fail pada satu masa bagi setiap pelayan). Sebaik sahaja anda mendapat pautan muat turun, ia sah untuk beberapa jam. Terima kasih kerana menunggu, ini memastikan laman web ini boleh diakses secara percuma untuk semua orang! 😊 🔗 Semua pautan muat turun untuk fail ini: <a %(a_main)s>Halaman utama fail</a>. ❌ Muat turun yang perlahan tidak tersedia melalui VPN Cloudflare atau dari alamat IP Cloudflare. ❌ Muat turun perlahan hanya tersedia melalui laman web rasmi. Lawati %(websites)s. 📚 Gunakan URL berikut untuk memuat turun: <a %(a_download)s>Muat turun sekarang</a>. Untuk memberi peluang kepada semua orang memuat turun fail secara percuma, anda perlu menunggu sebelum anda boleh memuat turun fail ini. Sila tunggu <span %(span_countdown)s>%(wait_seconds)s</span> saat untuk memuat turun fail ini. Amaran: terdapat banyak muat turun dari alamat IP anda dalam 24 jam terakhir. Muat turun mungkin lebih perlahan daripada biasa. Jika anda menggunakan VPN, sambungan internet berkongsi, atau ISP anda berkongsi IP, amaran ini mungkin disebabkan oleh itu. Simpan ❌ Ada sesuatu yang tidak kena. Sila cuba semula. ✅ Disimpan. Sila muat semula halaman. Tukar nama paparan anda. Pengecam anda (bahagian selepas “#”) tidak boleh ditukar. Profil telah dicipta <span %(span_time)s>%(time)s</span> sunting Senarai-senarai Cipta senarai baharu dengan mencaru fail dan membuka tab “Senarai”. Tiada senarai lagi Profil tidak ditemui. Profil Pada masa ini, kami tidak dapat memenuhi permintaan buku. Jangan email kami permintaan buku anda. Sila buat permintaan anda di forum Z-Library atau Libgen. Rekod dalam Arkib Anna DOI: %(doi)s Muat turun SciDB Nexus/STC Tiada pratonton tersedia lagi. Muat turun fail dari <a %(a_path)s>Arkib Anna</a>. Untuk menyokong kebolehcapaian dan pemeliharaan jangka panjang pengetahuan manusia, jadi <a %(a_donate)s>ahli</a>. Sebagai bonus, 🧬&nbsp;SciDB memuat lebih pantas untuk ahli, tanpa sebarang had. Tidak berfungsi? Cuba <a %(a_refresh)s>menyegarkan</a>. Sci-Hub Tambah medan carian khusus Cari penerangan dan komen metadata Tahun diterbitkan Lanjutan Akses Kandungan Papar Senarai Jadual Jenis fail Bahasa Susun mengikut Terbesar Paling relevan Terbaru (saiz fail) (sumber terbuka) (tahun penerbitan) Tertua Rawak Paling kecil Sumber dikikis dan sumber terbuka oleh AA Peminjaman Digital (%(count)s) Artikel Jurnal (%(count)s) Kami telah menemui padanan dalam: %(in)s. Anda boleh merujuk kepada URL yang terdapat di sana apabila <a %(a_request)s>meminta fail</a>. Metadata (%(count)s) Untuk meneroka indeks carian mengikut kod, gunakan <a %(a_href)s>Codes Explorer</a>. Indeks carian dikemas kini setiap bulan. Ia kini termasuk entri sehingga %(last_data_refresh_date)s. Untuk maklumat teknikal lanjut, lihat halaman %(link_open_tag)sdatasets</a>. Kecualikan Hanya sertakan Tidak ditandakan lagi… Seterusnya … Sebelumnya Indeks carian ini kini termasuk metadata dari perpustakaan Peminjaman Digital Terkawal Internet Archive. <a %(a_datasets)s>Maklumat lanjut tentang datasets kami</a>. Untuk lebih banyak perpustakaan pinjaman digital, lihat <a %(a_wikipedia)s>Wikipedia</a> dan <a %(a_mobileread)s>MobileRead Wiki</a>. Untuk tuntutan DMCA / hak cipta <a %(a_copyright)s>klik di sini</a>. Masa muat turun Ralat semasa carian. Cuba <a %(a_reload)s>muat semula halaman</a>. Jika masalah berterusan, sila emel kami di %(email)s. Muat turun pantas Malah, sesiapa sahaja boleh membantu memelihara fail-fail ini dengan menyemai <a %(a_torrents)s>senarai torrent bersatu kami</a>. ➡️ Kadang-kadang ini berlaku secara tidak betul apabila pelayan carian lambat. Dalam kes sedemikian, <a %(a_attrs)s>memuat semula</a> boleh membantu. ❌ Fail ini mungkin mempunyai masalah. Mencari kertas kerja? Indeks carian ini kini termasuk metadata dari pelbagai sumber metadata. <a %(a_datasets)s>Lebih lanjut mengenai dataset kami</a>. Terdapat banyak, banyak sumber metadata untuk karya bertulis di seluruh dunia. <a %(a_wikipedia)s>Laman Wikipedia ini</a> adalah permulaan yang baik, tetapi jika anda tahu senarai baik yang lain, sila maklumkan kepada kami. Untuk metadata, kami menunjukkan rekod asal. Kami tidak menggabungkan rekod. Kami kini mempunyai katalog terbuka buku, kertas kerja, dan karya bertulis lain yang paling komprehensif di dunia. Kami mencermin Sci-Hub, Library Genesis, Z-Library, <a %(a_datasets)s>dan banyak lagi</a>. <span class="font-bold">Tiada fail dijumpai.</span> Cuba gunakan istilah carian dan penapis yang kurang atau berbeza. Keputusan %(from)s-%(to)s (%(total)s jumlah keseluruhan) Jika anda menemui “perpustakaan bayangan” lain yang patut kami cerminkan, atau jika anda mempunyai sebarang soalan, sila hubungi kami di %(email)s. %(num)d padanan separa %(num)d+ padanan separa Taip dalam kotak untuk mencari fail dalam perpustakaan pinjaman digital. Taip dalam kotak untuk mencari katalog kami yang mengandungi %(count)s fail yang boleh dimuat turun secara langsung, yang kami <a %(a_preserve)s>kekalkan selama-lamanya</a>. Taip dalam kotak untuk mencari. Taip dalam kotak untuk mencari katalog kami yang mengandungi %(count)s kertas kerja akademik dan artikel jurnal, yang kami <a %(a_preserve)s>kekalkan selama-lamanya</a>. Taip dalam kotak untuk mencari metadata dari perpustakaan. Ini boleh berguna apabila <a %(a_request)s>meminta fail</a>. Tip: gunakan pintasan papan kekunci “/” (fokus carian), “enter” (cari), “j” (atas), “k” (bawah), “<” (halaman sebelumnya), “>” (halaman seterusnya) untuk navigasi yang lebih cepat. Ini adalah rekod metadata, <span %(classname)s>bukan</span> fail yang boleh dimuat turun. Tetapan carian Cari Peminjaman Digital Muat turun Artikel jurnal Metadata Carian baru %(search_input)s - Cari Carian mengambil masa terlalu lama, yang bermaksud anda mungkin melihat hasil yang tidak tepat. Kadang-kadang <a %(a_reload)s>memuat semula</a> halaman membantu. Carian mengambil masa terlalu lama, yang biasa untuk pertanyaan yang luas. Kiraan penapis mungkin tidak tepat. Untuk muat naik besar (lebih 10,000 fail) yang tidak diterima oleh Libgen atau Z-Library, sila hubungi kami di %(a_email)s. Untuk Libgen.li, pastikan untuk log masuk terlebih dahulu di <a %(a_forum)s>forum mereka</a> dengan nama pengguna %(username)s dan kata laluan %(password)s, dan kemudian kembali ke <a %(a_upload_page)s>halaman muat naik</a> mereka. Buat masa ini, kami cadangkan memuat naik buku baharu ke garpu Library Genesis. Gunakan <a %(a_guide)s>panduan berguna</a> ini. Ambil maklum bahawa kedua-dua garpu yang kami indeks di laman web ini ditarik daripada sistem muat naik yang sama. Untuk muat naik kecil (sehingga 10,000 fail) sila muat naik ke kedua-dua %(first)s dan %(second)s. Sebagai alternatif, anda boleh memuat naiknya ke Z-Library <a %(a_upload)s>di sini</a>. Untuk memuat naik kertas akademik, sila juga (sebagai tambahan kepada Library Genesis) muat naik ke <a %(a_stc_nexus)s>STC Nexus</a>. Mereka adalah perpustakaan bayangan terbaik untuk kertas baru. Kami belum mengintegrasikan mereka lagi, tetapi kami akan pada suatu masa nanti. Anda boleh menggunakan <a %(a_telegram)s>bot muat naik mereka di Telegram</a>, atau hubungi alamat yang disenaraikan dalam mesej yang disematkan mereka jika anda mempunyai terlalu banyak fail untuk dimuat naik dengan cara ini. <span %(label)s>Kerja sukarela berat (USD$50-USD$5,000 ganjaran):</span> jika anda mampu mendedikasikan banyak masa dan/atau sumber kepada misi kami, kami ingin bekerjasama dengan anda dengan lebih rapat. Akhirnya anda boleh sertai pasukan dalaman. Walaupun bajet kami ketat, kami mampu memberikan <span %(bold)s>💰 ganjaran wang</span> untuk kerja yang paling intensif. <span %(label)s>Kerja sukarela ringan:</span> jika anda hanya boleh meluangkan beberapa jam di sana sini, masih terdapat banyak cara anda boleh membantu. Kami memberi ganjaran kepada sukarelawan yang konsisten dengan <span %(bold)s>🤝 keahlian Anna’s Archive</span>. Anna’s Archive bergantung kepada sukarelawan seperti anda. Kami mengalu-alukan semua tahap komitmen, dan mempunyai dua kategori utama bantuan yang kami cari: Jika anda tidak dapat menyumbangkan masa, anda masih boleh membantu kami dengan banyak cara seperti <a %(a_donate)s>menderma wang</a>, <a %(a_torrents)s>menyemai torrents kami</a>, <a %(a_uploading)s>memuat naik buku</a>, atau <a %(a_help)s>memberitahu rakan-rakan anda tentang Arkib Anna</a>. <span %(bold)s>Syarikat:</span> kami menawarkan akses langsung berkelajuan tinggi kepada koleksi kami sebagai pertukaran untuk derma peringkat perusahaan atau pertukaran untuk koleksi baru (contohnya imbasan baru, datasets OCR, memperkayakan data kami). <a %(a_contact)s>Hubungi kami</a> jika ini anda. Lihat juga <a %(a_llm)s>halaman LLM kami</a>. Ganjaran Kami sentiasa mencari individu yang mempunyai kemahiran pengaturcaraan atau keselamatan ofensif yang kukuh untuk terlibat. Anda boleh membuat sumbangan besar dalam memelihara warisan manusia. Sebagai tanda terima kasih, kami memberikan keahlian untuk sumbangan yang kukuh. Sebagai tanda terima kasih yang besar, kami memberikan ganjaran wang untuk tugas-tugas yang sangat penting dan sukar. Ini tidak sepatutnya dilihat sebagai pengganti pekerjaan, tetapi ia adalah insentif tambahan dan boleh membantu dengan kos yang ditanggung. Kebanyakan kod kami adalah sumber terbuka, dan kami akan meminta kod anda juga bersifat sumber terbuka apabila memberikan ganjaran. Terdapat beberapa pengecualian yang boleh kita bincangkan secara individu. Ganjaran diberikan kepada orang pertama yang menyelesaikan tugas. Sila komen pada tiket ganjaran untuk memberitahu orang lain bahawa anda sedang mengusahakan sesuatu, supaya orang lain boleh menahan diri atau menghubungi anda untuk bekerjasama. Tetapi sedar bahawa orang lain masih bebas untuk mengusahakannya juga dan cuba mengalahkan anda. Walau bagaimanapun, kami tidak memberikan ganjaran untuk kerja yang tidak kemas. Jika dua penyerahan berkualiti tinggi dibuat berdekatan antara satu sama lain (dalam masa sehari atau dua), kami mungkin memilih untuk memberikan ganjaran kepada kedua-duanya, mengikut budi bicara kami, contohnya 100%% untuk penyerahan pertama dan 50%% untuk penyerahan kedua (jadi 150%% keseluruhan). Untuk ganjaran yang lebih besar (terutamanya ganjaran pengikisan), sila hubungi kami apabila anda telah menyelesaikan ~5%% daripadanya, dan anda yakin bahawa kaedah anda akan berkembang ke tahap penuh. Anda perlu berkongsi kaedah anda dengan kami supaya kami boleh memberikan maklum balas. Juga, dengan cara ini kami boleh memutuskan apa yang perlu dilakukan jika terdapat beberapa orang yang hampir mencapai ganjaran, seperti berpotensi memberikan ganjaran kepada beberapa orang, menggalakkan orang untuk bekerjasama, dan sebagainya. AMARAN: tugas ganjaran tinggi adalah <span %(bold)s>sukar</span> — mungkin bijak untuk memulakan dengan yang lebih mudah. Pergi ke <a %(a_gitlab)s>senarai isu Gitlab kami</a> dan susun mengikut “Keutamaan Label”. Ini menunjukkan secara kasar susunan tugas yang kami utamakan. Tugas tanpa ganjaran eksplisit masih layak untuk keahlian, terutamanya yang ditandakan “Diterima” dan “Kegemaran Anna”. Anda mungkin mahu memulakan dengan “Projek Permulaan”. Kerja sukarela ringan Kami kini juga mempunyai saluran Matrix yang diselaraskan di %(matrix)s. Jika anda mempunyai beberapa jam untuk diluangkan, anda boleh membantu dalam pelbagai cara. Pastikan anda sertai <a %(a_telegram)s>perbualan sukarelawan di Telegram</a>. Sebagai tanda penghargaan, kami biasanya memberikan 6 bulan “Pustakawan Bertuah” untuk pencapaian asas, dan lebih lagi untuk kerja sukarela yang berterusan. Semua pencapaian memerlukan kerja berkualiti tinggi — kerja yang tidak kemas lebih menyusahkan kami dan kami akan menolaknya. Sila <a %(a_contact)s>emel kami</a> apabila anda mencapai pencapaian. %(links)s pautan atau tangkapan skrin permintaan yang anda penuhi. Memenuhi permintaan buku (atau kertas, dll) di forum Z-Library atau Library Genesis. Kami tidak mempunyai sistem permintaan buku sendiri, tetapi kami mencerminkan perpustakaan tersebut, jadi menjadikannya lebih baik juga menjadikan Arkib Anna lebih baik. Pencapaian Tugas Bergantung pada tugas. Tugas kecil yang diposkan di <a %(a_telegram)s>perbualan sukarelawan di Telegram</a>. Biasanya untuk keahlian, kadang-kadang untuk ganjaran kecil. Tugas kecil diposkan dalam kumpulan sembang sukarelawan kami. Pastikan untuk meninggalkan komen pada isu yang anda selesaikan, supaya orang lain tidak menggandakan kerja anda. %(links)s pautan rekod yang anda perbaiki. Anda boleh menggunakan <a %(a_list)s>senarai isu metadata rawak</a> sebagai titik permulaan. Perbaiki metadata dengan <a %(a_metadata)s>menghubungkan</a> dengan Open Library. Ini sepatutnya menunjukkan anda memberitahu seseorang tentang Arkib Anna, dan mereka mengucapkan terima kasih kepada anda. %(links)s pautan atau tangkapan skrin. Menyebarkan berita tentang Arkib Anna. Sebagai contoh, dengan mengesyorkan buku di AA, memautkan ke catatan blog kami, atau secara amnya mengarahkan orang ke laman web kami. Terjemah sepenuhnya satu bahasa (jika ia belum hampir siap). <a %(a_translate)s>Menterjemah</a> laman web. Pautan ke sejarah suntingan yang menunjukkan anda membuat sumbangan yang signifikan. Perbaiki halaman Wikipedia untuk Arkib Anna dalam bahasa anda. Sertakan maklumat dari halaman Wikipedia AA dalam bahasa lain, dan dari laman web dan blog kami. Tambah rujukan kepada AA di halaman berkaitan lain. Sukarelawan & Ganjaran 